version: '3'

services:
  localstack:
    container_name: localstack
    image: localstack/localstack:3.2.0
    ports:
      - "4566:4566"
    environment:
      - DEBUG=${LOCALSTACK_DEBUG-}
      - DOCKER_HOST=unix:///var/run/docker.sock
      - EDGE_PORT=4566
      - SQS_ENDPOINT_STRATEGY=path
    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./.localstack_volume}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"

  postgres-db:
    container_name: postgres-db
    image: library/postgres:16.4
    restart: always
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: billy
      POSTGRES_PASSWORD: password
      POSTGRES_DB: billy-db
    command: postgres -c log_min_duration_statement=25
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: "512M"

  gotenberg:
    container_name: gotenberg
    image: gotenberg/gotenberg:8.9.0
    command:
      - "gotenberg"
      - "--log-format=json"
      - "--log-level=info"
      - "--chromium-restart-after=50"
    ports:
      - "3001:3000"
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: "512M"

  memcached:
    container_name: memcached
    image: memcached:1.6-alpine
    ports:
      - "11211:11211"

  elasticsearch:
    container_name: elasticsearch
    image: elasticsearch:7.11.2
    environment:
      - bootstrap.memory_lock=true
      - action.auto_create_index=true
      - "discovery.type=single-node"
      - "ES_JAVA_OPTS=-Xms512m -Xmx1024m"
      - "logger.level=WARN"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
