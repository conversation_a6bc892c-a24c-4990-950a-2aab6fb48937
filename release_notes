#!/usr/bin/env python3
import argparse
import subprocess
import json

envs = ["sandbox", "dev2", "prod", "qa"]


def get_env_url(env_name: str) -> str:
    if env_name == "sandbox":
        return "https://sandbox.subskribe.net/api/backend/version"
    elif env_name == "dev2":
        return "https://dev2.subskribe.net/api/backend/version"
    elif env_name == "prod":
        return "https://app.subskribe.com/api/backend/version"
    elif env_name == "qa":
        return "https://qa01.subskribe.net/api/backend/version"
    else:
        raise LookupError(f"Accepted environments are {envs}")


def get_commit_hash_from_env_url(env_url: str) -> str:
    curl_command = f"curl {env_url}"
    process = subprocess.run(curl_command, shell=True, check=True, capture_output=True)
    if process.returncode != 0:
        raise ConnectionError(f"error when running cURL: {process.stderr}")
    else:
        version_json = json.loads(process.stdout)
        return version_json["version"]


def print_release_notes(commit1: str, commit2: str) -> None:
    print_command = f"git fetch --all; git log --pretty=format:\"%h %<(20)%an %<(20)%ad %<(20)%s\" \"{first_commit}..{second_commit}\" | cat"
    process = subprocess.run(print_command, shell=True, check=True, capture_output=True)
    if process.returncode != 0:
        raise ChildProcessError(f"error when running git log: {process.stderr}")
    else:
        print(process.stdout.decode("utf-8"))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Print succinct release notes between two versions, powered by git log")
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--envs', nargs=2,
                       help=f"diff between two environments, options are {envs}. Syntax is --envs BEHIND_ENV AHEAD_ENV")
    group.add_argument('--commits', nargs=2, help=f"diff between two git commit hashes")
    args = parser.parse_args()

    if args.commits:
        first_commit = args.commits[0]
        second_commit = args.commits[1]
        print_release_notes(first_commit, second_commit)

    elif args.envs:
        first_env = args.envs[0]
        second_env = args.envs[1]
        first_commit = get_commit_hash_from_env_url(get_env_url(first_env))
        second_commit = get_commit_hash_from_env_url(get_env_url(second_env))
        print_release_notes(first_commit, second_commit)
