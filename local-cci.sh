#!/usr/bin/env bash

# This script is intended to be used when CircleCI is partly down, to run the
# deployment workflows. Must be run from root folder of git checkout.
#
# By default it runs the sequence of jobs defined in workflow-auto().
# Use "local-cci.sh help" for other options.

source ../billy-infra/lib-local-cci.sh

job-native-build() {
	_cci-local-exec native-build "Building jar natively"
}

job-native-test() {
	_cci-local-exec native-test "Testing native jar"
}

job-docker-build() {
  _setup-aws dev2
	_cci-local-exec docker-build "Building docker image"
}

job-security-scan-source-code() {
  _setup-aws dev2
	_cci-local-exec security-scan-source-code "Security scan of source code"
}

job-security-scan-docker-image-ecr() {
  _setup-aws dev2
	_cci-local-exec security-scan-docker-image-ecr "Security scan of billy docker image"
}

job-security-scan-gotenberg() {
  _setup-aws dev2
	_cci-local-exec security-scan-gotenberg "Security scan of gotenberg 7 docker image"
}

job-docker-test-e2e() {
  _setup-aws dev2
	_cci-local-exec docker-run-e2e-tests "Test e2e with docker image"
}

job-docker-push() {
  # check all pre-conditions upfront
  _check-aws-profiles-exist dev2 prod

  _setup-aws dev2
	_cci-local-exec push-image-to-non-prod-ecr "Pushing docker image to non-prod ECR"
  _setup-aws prod
	_cci-local-exec push-image-to-prod-ecr "Pushing docker image to prod ECR"
}

job-deploy-non-prod() {
  job-deploy-devops1
  job-deploy-dev2
  job-deploy-sandbox
}

job-deploy-devops1() {
  if _check-git-branch 'master' '.*/test-auto-deploy-devops1/.*'; then
    _setup-aws devops1
  	_cci-local-exec deploy-to-devops1 "Deploying billy in devops1"
  else
    echo 'No deployment of billy to devops1'
  fi
}

job-deploy-dev2() {
  if _check-git-branch 'master'; then
    _setup-aws dev2
  	_cci-local-exec deploy-to-dev2 "Deploying billy in dev2"
  else
    echo 'No deployment of billy to dev2'
  fi
}

job-deploy-sandbox() {
  if _check-git-branch 'master'; then
    _setup-aws sandbox
  	_cci-local-exec deploy-to-sandbox "Deploying billy in sandbox"
  else
    echo 'No deployment of billy to beta'
  fi
}

job-deploy-prod() {
  if _check-git-branch 'master'; then
    _setup-aws prod
  	_cci-local-exec deploy-to-prod "Deploying billy in prod"
  else
    echo 'No deployment of billy to prod'
  fi
}

workflow-auto() {
  # check all pre-conditions upfront
  _check-aws-profiles-exist devops1 dev2 sandbox prod

#  native-build
#  native-test
  job-docker-build
  job-docker-test-e2e
  job-docker-push
  job-deploy-non-prod
}

_main "$*"
