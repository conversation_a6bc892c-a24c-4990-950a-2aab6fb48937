# Whether to use 'space', 'tab' or 'mixed' (both) characters for indentation.
# The default value is 'tab'.
org.eclipse.jdt.core.formatter.tabulation.char=space

# Number of spaces used for indentation in case 'space' characters have been selected.
# The default value is 4.
org.eclipse.jdt.core.formatter.tabulation.size=4

# Number of spaces used for indentation in case 'mixed' characters have been selected.
# The default value is 4.
org.eclipse.jdt.core.formatter.indentation.size=4

# Whether or not indentation characters are inserted into empty lines.
# The default value is 'true'.
org.eclipse.jdt.core.formatter.indent_empty_lines=false

# Number of spaces used for multiline indentation.
# The default value is 2.
groovy.formatter.multiline.indentation=2

# Length after which list are considered too long. These will be wrapped.
# The default value is 30.
groovy.formatter.longListLength=30

# Remove unnecessary semicolons. The default value is 'false'.
groovy.formatter.remove.unnecessary.semicolons=true

#Whether opening braces position shall be the next line.
#The default value is 'same'.
groovy.formatter.braces.start=same

#Ignore if formatter cannot parse incompatible code (such as Java predicates)
ignoreFormatterProblems=true
