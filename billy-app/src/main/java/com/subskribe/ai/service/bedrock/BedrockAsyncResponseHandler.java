package com.subskribe.ai.service.bedrock;

import com.subskribe.ai.AIProductMetadata;
import com.subskribe.ai.service.EventOutputHelper;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import io.dropwizard.setup.Environment;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import javax.inject.Inject;
import org.glassfish.jersey.media.sse.EventOutput;
import org.json.JSONObject;
import org.json.JSONPointer;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelWithResponseStreamResponseHandler;

public class BedrockAsyncResponseHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(BedrockAsyncResponseHandler.class);

    private final ExecutorService streamingExecutor;

    private static final String BEDROCK_STREAMING_THREAD = "bedrock-streaming-thread";

    private static final int MAX_BEDROCK_STREAMING_THREADS = 5;

    @Inject
    public BedrockAsyncResponseHandler(Environment environment) {
        streamingExecutor = environment.lifecycle().executorService(BEDROCK_STREAMING_THREAD).maxThreads(MAX_BEDROCK_STREAMING_THREADS).build();
    }

    public static InvokeModelWithResponseStreamResponseHandler getHandler(EventOutput eventOutput) {
        return InvokeModelWithResponseStreamResponseHandler.builder()
            .subscriber(
                InvokeModelWithResponseStreamResponseHandler.Visitor.builder()
                    .onChunk(chunk -> {
                        var response = new JSONObject(chunk.bytes().asUtf8String());

                        // Extract and print the text from the content blocks.
                        if (Objects.equals(response.getString("type"), "content_block_delta")) {
                            var text = new JSONPointer("/delta/text").queryFrom(response).toString();
                            EventOutputHelper.writeText(eventOutput, text);
                        }
                    })
                    .build()
            )
            .build();
    }

    public void spawnAndWaitForFuture(CompletableFuture<Void> future, EventOutput eventOutput) {
        streamingExecutor.submit(() -> {
            try {
                future.get();
            } catch (Exception ex) {
                String message = String.format("**INTERNAL ERROR:** there was an error processing your request, reason: %s", ex.getMessage());
                EventOutputHelper.writeText(eventOutput, message);
                LOGGER.warn(AIProductMetadata.AI_SUMMARY, "error from Bedrock while generating summary", ex);
            } finally {
                // successfully write DONE after streaming is over
                EventOutputHelper.writeDone(eventOutput);
                EventOutputHelper.closeEventOutput(eventOutput);
            }
        });
    }
}
