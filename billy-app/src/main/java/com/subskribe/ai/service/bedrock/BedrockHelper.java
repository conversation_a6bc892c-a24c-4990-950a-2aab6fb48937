package com.subskribe.ai.service.bedrock;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.subskribe.ai.service.model.ImmutableMessage;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.core.document.Document;
import software.amazon.awssdk.protocols.json.internal.unmarshall.document.DocumentUnmarshaller;
import software.amazon.awssdk.protocols.jsoncore.JsonNodeParser;
import software.amazon.awssdk.services.bedrockruntime.model.ContentBlock;
import software.amazon.awssdk.services.bedrockruntime.model.ConversationRole;
import software.amazon.awssdk.services.bedrockruntime.model.Message;

public final class BedrockHelper {

    private static final JsonNodeParser SDK_NODE_PARSER = JsonNodeParser.create();

    private BedrockHelper() {}

    public static com.subskribe.ai.service.model.Message toModelMessage(Message message) {
        String textMessageContent = message
            .content()
            .stream()
            .map(ContentBlock::text)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.joining(","));
        return ImmutableMessage.builder().role(message.roleAsString()).message(textMessageContent).createdAt(0).build();
    }

    public static Message toBedrockMessage(com.subskribe.ai.service.model.Message message) {
        return Message.builder()
            .role(ConversationRole.fromValue(message.getRole()))
            .content(ContentBlock.builder().text(message.getMessage()).build())
            .build();
    }

    public static List<Message> toBedrockMessages(List<com.subskribe.ai.service.model.Message> messages) {
        if (CollectionUtils.isEmpty(messages)) {
            return List.of();
        }
        return messages.stream().map(BedrockHelper::toBedrockMessage).toList();
    }

    public static List<com.subskribe.ai.service.model.Message> toModelMessages(List<Message> messages) {
        if (CollectionUtils.isEmpty(messages)) {
            return List.of();
        }
        return messages.stream().map(BedrockHelper::toModelMessage).toList();
    }

    public static void createUserMessage(List<Message> context, String userTextMessage) {
        context.add(Message.builder().role(ConversationRole.USER).content(ContentBlock.builder().text(userTextMessage).build()).build());
    }

    public static void createSystemMessage(List<Message> context, String systemTextMessage) {
        context.add(Message.builder().role(ConversationRole.ASSISTANT).content(ContentBlock.builder().text(systemTextMessage).build()).build());
    }

    public static com.subskribe.ai.service.model.Message modelMessageWithRole(ConversationRole conversationRole, String message) {
        return ImmutableMessage.builder().role(conversationRole.toString()).message(message).createdAt(Instant.now().getEpochSecond()).build();
    }

    public static Document jsonStringToDocument(String jsonString) {
        return SDK_NODE_PARSER.parse(jsonString).visit(new DocumentUnmarshaller());
    }

    public static JsonNode jsonNodeFromString(String argsStr) {
        try {
            return JacksonProvider.defaultMapper().readTree(argsStr);
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException(e);
        }
    }
}
