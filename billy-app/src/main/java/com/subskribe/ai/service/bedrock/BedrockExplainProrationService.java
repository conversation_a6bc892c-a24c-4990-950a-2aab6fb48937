package com.subskribe.ai.service.bedrock;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.subskribe.ai.service.PromptService;
import com.subskribe.billy.exception.ExternalDependencyException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import javax.inject.Inject;
import org.glassfish.jersey.media.sse.EventOutput;
import org.json.JSONArray;
import org.json.JSONObject;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeAsyncClient;
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeClient;
import software.amazon.awssdk.services.bedrockruntime.model.BedrockRuntimeException;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelRequest;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelResponse;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelWithResponseStreamRequest;

public class BedrockExplainProrationService {

    private static final String ORDER_DATA_IDENTIFIER = "__ORDER_DATA__";

    private static final String ORDER_LINE_DATA_IDENTIFIER = "__ORDER_LINE_DATA__";

    private final TenantSettingService tenantSettingService;

    private final ProrationConfigurationGetService prorationConfigurationGetService;

    private final OrderGetService orderGetService;

    private final ProductCatalogGetService productCatalogGetService;

    private final PromptService promptService;

    private final BedrockRuntimeAsyncClient bedrockRuntimeAsyncClient;

    private final BedrockAsyncResponseHandler bedrockAsyncResponseHandler;

    private final BedrockRuntimeClient bedrockRuntimeClient;

    @Inject
    public BedrockExplainProrationService(
        TenantSettingService tenantSettingService,
        ProrationConfigurationGetService prorationConfigurationGetService,
        OrderGetService orderGetService,
        ProductCatalogGetService productCatalogGetService,
        PromptService promptService,
        BedrockClientProvider bedrockClientProvider,
        BedrockAsyncResponseHandler bedrockAsyncResponseHandler
    ) {
        this.tenantSettingService = tenantSettingService;
        this.prorationConfigurationGetService = prorationConfigurationGetService;
        this.orderGetService = orderGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.promptService = promptService;
        this.bedrockRuntimeAsyncClient = bedrockClientProvider.getBedrockRuntimeAsyncClient();
        this.bedrockRuntimeClient = bedrockClientProvider.getBedrockRuntimeClient();
        this.bedrockAsyncResponseHandler = bedrockAsyncResponseHandler;
    }

    public EventOutput explainProrationAsync(String orderId, String orderLineItemId) {
        Validator.validateStringNotBlank(orderId, "orderId cannot be blank");
        Validator.validateStringNotBlank(orderLineItemId, "orderLineItemId cannot be blank");
        String prompt = getPrompt(orderId, orderLineItemId);

        try {
            InvokeModelWithResponseStreamRequest invokeModelRequest = makeInvokeModelStreamRequest(prompt);
            EventOutput eventOutput = new EventOutput();
            CompletableFuture<Void> future = bedrockRuntimeAsyncClient.invokeModelWithResponseStream(
                invokeModelRequest,
                BedrockAsyncResponseHandler.getHandler(eventOutput)
            );
            bedrockAsyncResponseHandler.spawnAndWaitForFuture(future, eventOutput);
            return eventOutput;
        } catch (Exception ex) {
            throw new ServiceFailureException("Error processing bedrock request", ex);
        }
    }

    public String explainProration(String orderId, String orderLineItemId) {
        Validator.validateStringNotBlank(orderId, "orderId cannot be blank");
        Validator.validateStringNotBlank(orderLineItemId, "orderLineItemId cannot be blank");
        String prompt = getPrompt(orderId, orderLineItemId);

        try {
            InvokeModelRequest request = makeInvokeModelRequest(prompt);
            InvokeModelResponse response = bedrockRuntimeClient.invokeModel(request);
            JSONObject responseBodyJson = new JSONObject(response.body().asUtf8String());
            JSONArray content = responseBodyJson.getJSONArray("content");
            return content.getJSONObject(0).getString("text");
        } catch (BedrockRuntimeException ex) {
            throw new ExternalDependencyException("Bedrock request failed with: " + ex.awsErrorDetails().errorMessage(), ex);
        } catch (Exception ex) {
            throw new ServiceFailureException("Error processing explain proration.", ex);
        }
    }

    private String getPrompt(String orderId, String orderLineItemId) {
        Optional<Order> orderOptional = orderGetService.getOrderByOrderIdOptional(orderId);
        Order order = orderOptional.orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ORDER, orderId));

        Optional<OrderLineItem> orderLineItemOptional = order
            .getLineItems()
            .stream()
            .filter(lineItem -> lineItem.getOrderLineId().equals(orderLineItemId))
            .findFirst();
        OrderLineItem orderLineItem = orderLineItemOptional.orElseThrow(() ->
            new ObjectNotFoundException(BillyObjectType.ORDER_LINE_ITEM, orderLineItemId)
        );

        // if the order line item action is UPDATE, get the delta quantity
        if (orderLineItem.getAction() == ActionType.UPDATE) {
            List<OrderLineItem> netEffectLines = order
                .getLineItemsNetEffect()
                .stream()
                .filter(lineItem -> lineItem.getBaseExternalSubscriptionChargeId().equals(orderLineItem.getBaseExternalSubscriptionChargeId()))
                .toList();
            Long quantity = netEffectLines.stream().map(OrderLineItem::getQuantity).reduce(Long::sum).orElse(orderLineItem.getQuantity());
            orderLineItem.setQuantity(quantity);
        }

        Charge charge = productCatalogGetService.getChargeByChargeId(orderLineItem.getChargeId());
        return getExplainProrationPrompt(order, orderLineItem, charge);
    }

    public String getExplainProrationPrompt(Order order, OrderLineItem orderLineItem, Charge charge) {
        ProrationConfig prorationConfig = prorationConfigurationGetService.getEntityProrationConfiguration(order.getEntityId());
        Map<String, String> orderData = new HashMap<>();
        orderData.put("orderStartDate", formatDate(order.getStartDate()));
        orderData.put("orderEndDate", formatDate(order.getEndDate().minusSeconds(1)));
        orderData.put("billingAnchorDate", formatDate(order.getBillingAnchorDate()));
        orderData.put("billingCycle", order.getBillingCycle().getCycle().name());
        orderData.put("prorationMode", prorationConfig.getMode().name());

        Map<String, String> orderLineItemData = new HashMap<>();
        orderLineItemData.put("startDate", formatDate(orderLineItem.getEffectiveDate()));
        orderLineItemData.put("endDate", formatDate(orderLineItem.getEndDate().minusSeconds(1)));
        orderLineItemData.put("chargeType", charge.getType().name());
        orderLineItemData.put("chargeCycle", charge.getRecurrence().getCycle().name());
        // todo: the current prompt only considered per unit charges. Missing block and tiered where charge amount != unitPrice x quantity
        orderLineItemData.put("unitPrice", orderLineItem.getSellUnitPrice().toPlainString());
        orderLineItemData.put("quantity", String.valueOf(orderLineItem.getQuantity()));
        orderLineItemData.put("total", orderLineItem.getAmount().toPlainString());

        try {
            String orderDataString = JacksonProvider.emptyFieldExcludingMapper().writeValueAsString(orderData);
            String orderLineDataString = JacksonProvider.emptyFieldExcludingMapper().writeValueAsString(orderLineItemData);
            String prompt = promptService
                .getExplainProrationPrompt()
                .replace(ORDER_LINE_DATA_IDENTIFIER, orderLineDataString)
                .replace(ORDER_DATA_IDENTIFIER, orderDataString);
            return JacksonProvider.defaultMapper().writeValueAsString(prompt);
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Error serializing order detail", e);
        }
    }

    private static InvokeModelRequest makeInvokeModelRequest(String prompt) {
        return InvokeModelRequest.builder()
            .modelId(BedrockHelper.MODEL_ID)
            .body(SdkBytes.fromUtf8String(BedrockHelper.REQUEST_BLOCK.replace(BedrockHelper.PROMPT_DATA_IDENTIFIER, prompt)))
            .build();
    }

    private static InvokeModelWithResponseStreamRequest makeInvokeModelStreamRequest(String prompt) {
        return InvokeModelWithResponseStreamRequest.builder()
            .modelId(BedrockHelper.MODEL_ID)
            .body(SdkBytes.fromUtf8String(BedrockHelper.REQUEST_BLOCK.replace(BedrockHelper.PROMPT_DATA_IDENTIFIER, prompt)))
            .build();
    }

    private String formatDate(Instant instant) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM dd yyyy").withZone(timeZone.toZoneId());
        return formatter.format(instant);
    }
}
