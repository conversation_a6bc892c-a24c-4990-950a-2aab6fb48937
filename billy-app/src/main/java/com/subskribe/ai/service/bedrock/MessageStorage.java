package com.subskribe.ai.service.bedrock;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.node.TextNode;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import javax.inject.Inject;
import software.amazon.awssdk.core.document.Document;
import software.amazon.awssdk.protocols.json.internal.unmarshall.document.DocumentUnmarshaller;
import software.amazon.awssdk.protocols.jsoncore.JsonNodeParser;
import software.amazon.awssdk.services.bedrockruntime.model.Message;

public class MessageStorage {

    private static final JsonNodeParser SDK_NODE_PARSER = JsonNodeParser.create();
    private static final ObjectMapper CUSTOM_MAPPER = JacksonProvider.defaultMapper()
        .registerModule(new DocumentModule())
        .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    private final CacheService cacheService;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public MessageStorage(CacheService cacheService, TenantIdProvider tenantIdProvider) {
        this.cacheService = cacheService;
        this.tenantIdProvider = tenantIdProvider;
    }

    public void putMessages(String sessionId, List<Message> messages) {
        if (messages == null) {
            return;
        }
        try {
            String messagesJson = CUSTOM_MAPPER.writeValueAsString(messages.stream().map(Message::toBuilder).toList());
            cacheService.invalidateKey(tenantIdProvider.provideTenantIdString(), CacheType.BEDROCK_AGENT_MESSAGE, sessionId);
            cacheService.putBytes(
                tenantIdProvider.provideTenantIdString(),
                CacheType.BEDROCK_AGENT_MESSAGE,
                sessionId,
                messagesJson.getBytes(StandardCharsets.UTF_8)
            );
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("error serializing messages", e);
        }
    }

    public List<Message> getMessages(String sessionId) {
        try {
            byte[] messageBytes = cacheService.getBytes(tenantIdProvider.provideTenantIdString(), CacheType.BEDROCK_AGENT_MESSAGE, sessionId);
            if (messageBytes == null) {
                // we specifically return null because it is valid to store empty messages list
                return null;
            }
            List<Message.Builder> messagesRead = CUSTOM_MAPPER.readValue(
                messageBytes,
                CUSTOM_MAPPER.getTypeFactory().constructCollectionType(List.class, Message.serializableBuilderClass())
            );
            return messagesRead.stream().map(Message.Builder::build).toList();
        } catch (IOException e) {
            throw new ServiceFailureException("error serializing messages", e);
        }
    }

    // TODO: find a better way to do this all of these classes are require for AWS Document Serialization
    public static final class DocumentSerializer extends JsonSerializer<Document> {

        @Override
        public void serialize(Document value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeString(value.toString());
        }
    }

    public static final class DocumentDeserializer extends StdDeserializer<Document> {

        public DocumentDeserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public Document deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
            TextNode node = p.getCodec().readTree(p);
            return SDK_NODE_PARSER.parse(node.asText()).visit(new DocumentUnmarshaller());
        }
    }

    public static final class DocumentModule extends SimpleModule {

        public DocumentModule() {
            super("AwkSDkDocumentModule");
            addSerializer(Document.class, new DocumentSerializer());
            addDeserializer(Document.class, new DocumentDeserializer(Document.class));
        }
    }
}
