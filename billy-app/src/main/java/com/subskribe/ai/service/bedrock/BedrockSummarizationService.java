package com.subskribe.ai.service.bedrock;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.subskribe.ai.AIProductMetadata;
import com.subskribe.ai.service.EventOutputHelper;
import com.subskribe.ai.service.PromptService;
import com.subskribe.ai.service.SummarizationService;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.order.OrderDataAggregator;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.subscription.SubscriptionDataAggregator;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.validation.Validator;
import io.dropwizard.setup.Environment;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import javax.inject.Inject;
import org.glassfish.jersey.media.sse.EventOutput;
import org.json.JSONObject;
import org.json.JSONPointer;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeAsyncClient;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelRequest;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelWithResponseStreamRequest;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelWithResponseStreamResponseHandler;

public class BedrockSummarizationService implements SummarizationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BedrockSummarizationService.class);

    private static final String SUMMARY_TYPE_SHORT = "short";

    private static final Set<String> SUBSCRIPTION_TOP_LEVEL_FIELDS = Set.of(
        "entityId",
        "account",
        "state",
        "startDate",
        "endDate",
        "termLength",
        "billingCycle",
        "billingAnchorDate",
        "paymentTerm",
        "billingTerm",
        "charges",
        "orders",
        "autoRenew",
        "metrics",
        "creationTime",
        "currency"
    );

    private static final Set<String> ORDER_TOP_LEVEL_FIELDS = Set.of(
        "entityId",
        "name",
        "account",
        "createdBy",
        "owner",
        "orderType",
        "currency",
        "paymentTerm",
        "shippingContact",
        "billingContact",
        "lineItems",
        "startDate",
        "endDate",
        "termLength",
        "billingCycle",
        "billingTerm",
        "totalListAmount",
        "totalAmount",
        "totalDiscount",
        "totalDiscountPercent",
        "status",
        "metrics",
        "resoldBy"
    );

    private static final String SUMMARY_STREAMING_THREAD = "summary-streaming-thread";

    // TODO: TWEAK this later based on observation
    private static final int MAX_THREADS_FOR_SUMMARY_STREAMING = 5;

    private static final String SUBSCRIPTION_DATA_IDENTIFIER = "__SUBSCRIPTION_DATA__";

    private static final String ORDER_DATA_IDENTIFIER = "__ORDER_DATA__";

    private static final String MODEL_ID = "anthropic.claude-3-5-sonnet-********-v1:0";

    private static final String PROMPT_DATA_IDENTIFIER = "{{prompt}}";

    // tweak this for model output
    private static final String REQUEST_BLOCK =
        """
        {
            "anthropic_version": "bedrock-2023-05-31",
            "max_tokens": 1500,
            "temperature": 0.0,
            "messages": [{
                "role": "user",
                "content": {{prompt}}
            }]
        }""";

    private final BedrockRuntimeAsyncClient bedrockRuntimeAsyncClient;

    private final SubscriptionDataAggregator subscriptionDataAggregator;

    private final OrderDataAggregator orderDataAggregator;

    private final PromptService promptService;

    private final ExecutorService streamingExecutor;

    @Inject
    public BedrockSummarizationService(
        BedrockClientProvider bedrockClientProvider,
        SubscriptionDataAggregator subscriptionDataAggregator,
        OrderDataAggregator orderDataAggregator,
        PromptService promptService,
        Environment environment
    ) {
        bedrockRuntimeAsyncClient = bedrockClientProvider.getBedrockRuntimeAsyncClient();
        this.subscriptionDataAggregator = subscriptionDataAggregator;
        this.orderDataAggregator = orderDataAggregator;
        this.promptService = promptService;
        // this executor will start out with 0 threads
        // go to a maximum of MAX_THREADS_FOR_SUMMARY_STREAMING
        // threads will also be released after idle period
        streamingExecutor = environment.lifecycle().executorService(SUMMARY_STREAMING_THREAD).maxThreads(MAX_THREADS_FOR_SUMMARY_STREAMING).build();
    }

    @Override
    public EventOutput summarizeSubscriptionStream(String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "Subscription id cannot be blank");
        SubscriptionDetail subscriptionDetail = subscriptionDataAggregator.getSubscriptionDetail(subscriptionId);
        try {
            String subscriptionData = getSubscriptionSummaryJson(subscriptionDetail);
            String prompt = getSubscriptionSummaryPrompt(subscriptionData);
            String promptEncoded = JacksonProvider.defaultMapper().writeValueAsString(prompt);
            InvokeModelWithResponseStreamRequest invokeModelRequest = makeInvokeModelStreamRequest(promptEncoded);
            EventOutput eventOutput = new EventOutput();
            CompletableFuture<Void> future = bedrockRuntimeAsyncClient.invokeModelWithResponseStream(invokeModelRequest, getHandler(eventOutput));
            spawnAndWaitForFuture(future, eventOutput);
            return eventOutput;
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Error serializing Subscription detail", e);
        }
    }

    private static String getSubscriptionSummaryJson(SubscriptionDetail subscriptionDetail) throws JsonProcessingException {
        String subscriptionData = JacksonProvider.emptyFieldExcludingMapper().writeValueAsString(subscriptionDetail);
        JsonNode rootData = JacksonProvider.emptyFieldExcludingMapper().readTree(subscriptionData);
        ObjectNode newRootNode = JacksonProvider.emptyFieldExcludingMapper().createObjectNode();
        SUBSCRIPTION_TOP_LEVEL_FIELDS.forEach(fieldName -> {
            if (rootData.has(fieldName)) {
                newRootNode.set(fieldName, rootData.get(fieldName));
            }
        });

        removeUnwantedPlanChargeData(newRootNode, "charges");

        return JacksonProvider.emptyFieldExcludingMapper().writeValueAsString(newRootNode);
    }

    @Override
    public EventOutput summarizeOrderStream(String orderId, String type) {
        Validator.validateStringNotBlank(orderId, "Subscription id cannot be blank");
        OrderDetail orderDetail = orderDataAggregator.getOrderDetail(orderId, true, false);
        try {
            String orderData = getOrderSummaryJson(orderDetail);
            String prompt = getOrderSummaryPrompt(orderData, type);
            String promptEncoded = JacksonProvider.defaultMapper().writeValueAsString(prompt);
            InvokeModelWithResponseStreamRequest invokeModelRequest = makeInvokeModelStreamRequest(promptEncoded);
            EventOutput eventOutput = new EventOutput();
            CompletableFuture<Void> future = bedrockRuntimeAsyncClient.invokeModelWithResponseStream(invokeModelRequest, getHandler(eventOutput));
            spawnAndWaitForFuture(future, eventOutput);
            return eventOutput;
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Error serializing Subscription detail", e);
        }
    }

    private static String getOrderSummaryJson(OrderDetail orderDetail) throws JsonProcessingException {
        String allOrderData = JacksonProvider.emptyFieldExcludingMapper().writeValueAsString(orderDetail);
        JsonNode rootData = JacksonProvider.emptyFieldExcludingMapper().readTree(allOrderData);
        ObjectNode newRootNode = JacksonProvider.emptyFieldExcludingMapper().createObjectNode();
        ORDER_TOP_LEVEL_FIELDS.forEach(fieldName -> {
            if (rootData.has(fieldName)) {
                newRootNode.set(fieldName, rootData.get(fieldName));
            }
        });

        removeUnwantedPlanChargeData(newRootNode, "lineItems");

        return JacksonProvider.emptyFieldExcludingMapper().writeValueAsString(newRootNode);
    }

    private static void removeUnwantedPlanChargeData(ObjectNode newRootNode, String chargeNodeName) {
        // prune unwanted data
        if (newRootNode.has(chargeNodeName) && newRootNode.get(chargeNodeName) instanceof ArrayNode charges) {
            for (JsonNode charge : charges) {
                if (charge instanceof ObjectNode chargeObject) {
                    chargeObject.remove("chargeDetail");

                    if (chargeObject.has("plan") && chargeObject.get("plan") instanceof ObjectNode planObject) {
                        planObject.remove("charges");
                    }
                }
            }
        }
    }

    public String getSubscriptionSummaryPrompt(String subscriptionData) {
        return promptService.getSubscriptionSummaryPrompt().replace(SUBSCRIPTION_DATA_IDENTIFIER, subscriptionData);
    }

    public String getOrderSummaryPrompt(String orderData, String type) {
        if (SUMMARY_TYPE_SHORT.equals(type)) {
            return promptService.getOrderSummaryShortPrompt().replace(ORDER_DATA_IDENTIFIER, orderData);
        }
        return promptService.getOrderSummaryPrompt().replace(ORDER_DATA_IDENTIFIER, orderData);
    }

    public InvokeModelRequest makeInvokeModelRequest(String prompt) {
        return InvokeModelRequest.builder()
            .modelId(MODEL_ID)
            .body(SdkBytes.fromUtf8String(REQUEST_BLOCK.replace(PROMPT_DATA_IDENTIFIER, prompt)))
            .build();
    }

    public InvokeModelWithResponseStreamRequest makeInvokeModelStreamRequest(String prompt) {
        return InvokeModelWithResponseStreamRequest.builder()
            .modelId(MODEL_ID)
            .body(SdkBytes.fromUtf8String(REQUEST_BLOCK.replace(PROMPT_DATA_IDENTIFIER, prompt)))
            .build();
    }

    private static InvokeModelWithResponseStreamResponseHandler getHandler(EventOutput eventOutput) {
        return InvokeModelWithResponseStreamResponseHandler.builder()
            .subscriber(
                InvokeModelWithResponseStreamResponseHandler.Visitor.builder()
                    .onChunk(chunk -> {
                        var response = new JSONObject(chunk.bytes().asUtf8String());

                        // Extract and print the text from the content blocks.
                        if (Objects.equals(response.getString("type"), "content_block_delta")) {
                            var text = new JSONPointer("/delta/text").queryFrom(response).toString();
                            EventOutputHelper.writeText(eventOutput, text);
                        }
                    })
                    .build()
            )
            .build();
    }

    private void spawnAndWaitForFuture(CompletableFuture<Void> future, EventOutput eventOutput) {
        streamingExecutor.submit(() -> {
            try {
                future.get();
            } catch (Exception ex) {
                String message = String.format("**INTERNAL ERROR:** there was an error processing your request, reason: %s", ex.getMessage());
                EventOutputHelper.writeText(eventOutput, message);
                LOGGER.warn(AIProductMetadata.AI_SUMMARY, "error from Bedrock while generating summary", ex);
            } finally {
                // successfully write DONE after streaming is over
                EventOutputHelper.writeDone(eventOutput);
                EventOutputHelper.closeEventOutput(eventOutput);
            }
        });
    }
}
