package com.subskribe.ai.service;

import com.google.common.io.Resources;
import com.subskribe.ai.service.db.PromptDAO;
import com.subskribe.ai.service.model.PromptType;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.validation.Validator;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import javax.inject.Inject;

public class PromptService {

    private static final String CLAUDE_SONNET_PROMPT = "ai/claude_sonnet_prompt.txt";

    private static final String SUBSCRIPTION_SUMMARY_PROMPT = "ai/claude_subscription_summary_prompt.txt";

    private static final String ORDER_SUMMARY_PROMPT = "ai/claude_order_summary_prompt.txt";

    private static final String ORDER_SHORT_SUMMARY_PROMPT = "ai/claude_order_short_summary.txt";

    private static final int MIN_PROMPT_LENGTH_CHARS = 100;
    private static final int MAX_PROMPT_LENGTH_CHARS = 5000;

    private final PromptDAO promptDAO;

    @Inject
    public PromptService(PromptDAO promptDAO) {
        this.promptDAO = promptDAO;
    }

    public String getClaudeSonnetPrompt() {
        try {
            return Resources.toString(Resources.getResource(CLAUDE_SONNET_PROMPT), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new ServiceFailureException("error loading common prompt", e);
        }
    }

    public String getSubscriptionSummaryPrompt() {
        try {
            return Resources.toString(Resources.getResource(SUBSCRIPTION_SUMMARY_PROMPT), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new ServiceFailureException("error loading summary prompt", e);
        }
    }

    public String getOrderSummaryPrompt() {
        try {
            return Resources.toString(Resources.getResource(ORDER_SUMMARY_PROMPT), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new ServiceFailureException("error loading summary prompt", e);
        }
    }

    public String getOrderSummaryShortPrompt() {
        try {
            return Resources.toString(Resources.getResource(ORDER_SHORT_SUMMARY_PROMPT), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new ServiceFailureException("error loading summary prompt", e);
        }
    }

    public Optional<String> getPrompt(PromptType promptType) {
        Validator.validateNonNullArgument(promptType, "Prompt Type");
        return promptDAO.getStoredPrompt(promptType);
    }

    public void upsertPrompt(PromptType promptType, String promptText) {
        Validator.validateNonNullArgument(promptType, "Prompt Type");
        Validator.validateStringNotBlank(promptText, "Prompt text cannot be empty");
        Validator.validateStringLength(
            promptText,
            MIN_PROMPT_LENGTH_CHARS,
            MAX_PROMPT_LENGTH_CHARS,
            String.format("prompt text should be between %d tp %d chars", MIN_PROMPT_LENGTH_CHARS, MAX_PROMPT_LENGTH_CHARS)
        );
        if (getPrompt(promptType).isPresent()) {
            promptDAO.updatePrompt(promptType, promptText);
            return;
        }
        promptDAO.insertPrompt(promptType, promptText);
    }

    public void purgePrompt(PromptType promptType) {
        Validator.validateNonNullArgument(promptType, "Prompt Type");
        promptDAO.purgePrompt(promptType);
    }
}
