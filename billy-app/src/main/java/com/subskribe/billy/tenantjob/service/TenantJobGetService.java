package com.subskribe.billy.tenantjob.service;

import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.tenantjob.db.TenantJobQueueDAO;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobStatus;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;

public class TenantJobGetService {

    private static final int PROCESSING_WAIT_LOOP_COUNT = 6;
    private static final long WAIT_TIME_MS = 500;
    private static final int COMPLETION_WAIT_LOOP_COUNT = 60;
    private final TenantJobQueueDAO tenantJobQueueDAO;

    @Inject
    public TenantJobGetService(TenantJobQueueDAO tenantJobQueueDAO) {
        this.tenantJobQueueDAO = tenantJobQueueDAO;
    }

    public Optional<TenantJob> getActiveJobByTypeAndObjectId(TenantJobType jobType, String objectId) {
        return tenantJobQueueDAO.getActiveJobByTypeAndObjectId(jobType, objectId);
    }

    public Optional<TenantJob> getLatestJob(TenantJobType jobType, String objectId) {
        return tenantJobQueueDAO.getLatestJob(jobType, objectId);
    }

    public Optional<TenantJob> getActiveJobByType(TenantJobType jobType) {
        return tenantJobQueueDAO.getActiveJobByType(jobType);
    }

    public Optional<TenantJob> getActiveJobByType(List<TenantJobType> jobTypes) {
        return tenantJobQueueDAO.getActiveJobByTypes(jobTypes);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public TenantJob getTenantJobById(String jobId, Optional<String> optionalTenantId) {
        return tenantJobQueueDAO
            .getTenantJobById(jobId, optionalTenantId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.TENANT_JOB, jobId));
    }

    public void waitForProcessing(TenantJob tenantJob) throws InterruptedException {
        for (int i = 0; i < PROCESSING_WAIT_LOOP_COUNT; i++) {
            if (tenantJob.getStatus() != TenantJobStatus.QUEUED) {
                break;
            }
            Thread.sleep(WAIT_TIME_MS);
            tenantJob = getTenantJobById(tenantJob.getJobId(), Optional.empty());
        }
    }

    public void waitForCompletion(TenantJob tenantJob) throws InterruptedException {
        for (int i = 0; i < COMPLETION_WAIT_LOOP_COUNT; i++) {
            if (tenantJob.getStatus() == TenantJobStatus.FAILED || tenantJob.getStatus() == TenantJobStatus.SUCCESSFUL) {
                break;
            }
            Thread.sleep(WAIT_TIME_MS);
            tenantJob = getTenantJobById(tenantJob.getJobId(), Optional.empty());
        }
    }
}
