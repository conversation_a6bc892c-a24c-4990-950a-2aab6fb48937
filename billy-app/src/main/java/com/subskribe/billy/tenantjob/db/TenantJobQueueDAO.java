package com.subskribe.billy.tenantjob.db;

import static com.subskribe.billy.jooq.default_schema.tables.TenantJobQueue.TENANT_JOB_QUEUE;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.TenantJobQueueRecord;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenantjob.mapper.TenantJobMapper;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobStatus;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.UpdateSetMoreStep;
import org.mapstruct.factory.Mappers;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class TenantJobQueueDAO {

    private static final Set<TenantJobStatus> ACTIVE_JOB_STATUS_LIST = Set.of(TenantJobStatus.PROCESSING, TenantJobStatus.QUEUED);
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final TenantJobMapper recordMapper;

    @Inject
    public TenantJobQueueDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        recordMapper = Mappers.getMapper(TenantJobMapper.class);
    }

    public TenantJob createTenantJob(TenantJob tenantJob, Optional<String> optionalTenantId) {
        String tenantId = optionalTenantId.orElse(tenantIdProvider.provideTenantIdString());
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return saveTenantJob(tenantDSLContext, tenantJob, tenantId);
    }

    public TenantJob createTenantJobInTransaction(DSLContext txnContext, TenantJob tenantJob) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return saveTenantJob(txnContext, tenantJob, tenantId);
    }

    private TenantJob saveTenantJob(DSLContext tenantDSLContext, TenantJob tenantJob, String tenantId) {
        TenantJobQueueRecord record = recordMapper.toRecord(tenantJob);
        record.reset(TENANT_JOB_QUEUE.ID);
        record.setTenantId(tenantId);
        record.setJobId(AutoGenerate.getNewId());
        record.setIsDeleted(false);
        record.setAttempts(0);
        TenantJobQueueRecord insertedRecord = tenantDSLContext.insertInto(TENANT_JOB_QUEUE).set(record).returning().fetchOne();
        Optional.ofNullable(insertedRecord).orElseThrow(() -> new ServiceFailureException("Failed to save the tenant job."));
        return recordMapper.fromRecord(insertedRecord);
    }

    public Optional<TenantJob> getTenantJobById(String jobId, Optional<String> optionalTenantId) {
        String tenantId = optionalTenantId.orElse(tenantIdProvider.provideTenantIdString());
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        TenantJobQueueRecord record = tenantDslContext
            .select()
            .from(TENANT_JOB_QUEUE)
            .where(TENANT_JOB_QUEUE.TENANT_ID.eq(tenantId))
            .and(TENANT_JOB_QUEUE.JOB_ID.eq(jobId))
            .and(TENANT_JOB_QUEUE.IS_DELETED.isFalse())
            .fetchOneInto(TenantJobQueueRecord.class);
        return Optional.ofNullable(record).map(recordMapper::fromRecord);
    }

    public void updateStatus(String jobId, TenantJobStatus status) {
        updateStatus(jobId, status, StringUtils.EMPTY);
    }

    public void updateStatus(String jobId, TenantJobStatus status, String failureReason) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        TenantJob tenantJob = getTenantJobById(jobId, Optional.empty()).orElseThrow(() ->
            new ObjectNotFoundException(BillyObjectType.TENANT_JOB, jobId)
        );

        TenantJobStatus prevStatus = tenantJob.getStatus();

        // If the task is already in the desired status, return early
        if (prevStatus == status) {
            return;
        }

        validateStatusTransition(prevStatus, status);

        // increment attempts on processing
        int prevAttempts = Objects.requireNonNullElse(tenantJob.getAttempts(), 0);
        int newAttempts = (status == TenantJobStatus.PROCESSING) ? prevAttempts + 1 : prevAttempts;

        // update status and attempts
        UpdateSetMoreStep<TenantJobQueueRecord> updateStatement = dslContext
            .update(TENANT_JOB_QUEUE)
            .set(TENANT_JOB_QUEUE.STATUS, status.name())
            .set(TENANT_JOB_QUEUE.FAILURE_REASON, StringUtils.isBlank(failureReason) ? null : failureReason)
            .set(TENANT_JOB_QUEUE.ATTEMPTS, newAttempts);

        // update start time and end times
        LocalDateTime currentTimestamp = LocalDateTime.now(ZoneOffset.UTC);
        if (status == TenantJobStatus.PROCESSING) {
            updateStatement = updateStatement.set(TENANT_JOB_QUEUE.STARTED_ON, currentTimestamp).setNull(TENANT_JOB_QUEUE.ENDED_ON);
        } else if (prevStatus == TenantJobStatus.PROCESSING) {
            updateStatement = updateStatement.set(TENANT_JOB_QUEUE.ENDED_ON, currentTimestamp);
        }

        int count = updateStatement
            .where(TENANT_JOB_QUEUE.TENANT_ID.eq(tenantId))
            .and(TENANT_JOB_QUEUE.JOB_ID.eq(jobId))
            .and(TENANT_JOB_QUEUE.STATUS.eq(prevStatus.name()))
            .and(TENANT_JOB_QUEUE.ATTEMPTS.eq(prevAttempts))
            .and(TENANT_JOB_QUEUE.IS_DELETED.eq(false))
            .execute();
        if (count != 1) {
            throw new ServiceFailureException(String.format("could not update tenant job. id = %s", jobId));
        }
    }

    private void validateStatusTransition(TenantJobStatus prevStatus, TenantJobStatus newStatus) {
        switch (prevStatus) {
            case QUEUED, FAILED -> {
                if (newStatus == TenantJobStatus.PROCESSING) return;
            }
            case PROCESSING -> {
                if (newStatus == TenantJobStatus.SUCCESSFUL) return;
                if (newStatus == TenantJobStatus.FAILED) return;
            }
        }
        throw new IllegalStateException(String.format("invalid status transition. from: %s. to: %s", prevStatus, newStatus));
    }

    public Optional<TenantJob> getActiveJobByTypeAndObjectId(TenantJobType jobType, String objectId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        TenantJobQueueRecord record = tenantDslContext
            .select()
            .from(TENANT_JOB_QUEUE)
            .where(TENANT_JOB_QUEUE.TENANT_ID.eq(tenantId))
            .and(TENANT_JOB_QUEUE.JOB_TYPE.eq(jobType.name()))
            .and(TENANT_JOB_QUEUE.STATUS.in(ACTIVE_JOB_STATUS_LIST))
            .and(TENANT_JOB_QUEUE.OBJECT_ID.eq(objectId))
            .and(TENANT_JOB_QUEUE.IS_DELETED.isFalse())
            .orderBy(TENANT_JOB_QUEUE.CREATED_ON.desc())
            .limit(1)
            .fetchOneInto(TenantJobQueueRecord.class);
        return Optional.ofNullable(record).map(recordMapper::fromRecord);
    }

    public Optional<TenantJob> getLatestJob(TenantJobType jobType, String objectId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        TenantJobQueueRecord record = tenantDslContext
            .select()
            .from(TENANT_JOB_QUEUE)
            .where(TENANT_JOB_QUEUE.TENANT_ID.eq(tenantId))
            .and(TENANT_JOB_QUEUE.JOB_TYPE.eq(jobType.name()))
            .and(TENANT_JOB_QUEUE.OBJECT_ID.eq(objectId))
            .and(TENANT_JOB_QUEUE.IS_DELETED.isFalse())
            .orderBy(TENANT_JOB_QUEUE.CREATED_ON.desc())
            .limit(1)
            .fetchOneInto(TenantJobQueueRecord.class);
        return Optional.ofNullable(record).map(recordMapper::fromRecord);
    }

    public Optional<TenantJob> getLatestJobByTypeAndObjectId(TenantJobType jobType, String objectId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        TenantJobQueueRecord record = tenantDslContext
            .select()
            .from(TENANT_JOB_QUEUE)
            .where(TENANT_JOB_QUEUE.TENANT_ID.eq(tenantId))
            .and(TENANT_JOB_QUEUE.JOB_TYPE.eq(jobType.name()))
            // NOTE we check for all statuses
            .and(TENANT_JOB_QUEUE.STATUS.in(List.of(TenantJobStatus.values())))
            .and(TENANT_JOB_QUEUE.OBJECT_ID.eq(objectId))
            .and(TENANT_JOB_QUEUE.IS_DELETED.isFalse())
            .orderBy(TENANT_JOB_QUEUE.CREATED_ON.desc())
            .limit(1)
            .fetchOneInto(TenantJobQueueRecord.class);
        return Optional.ofNullable(record).map(recordMapper::fromRecord);
    }

    // todo: add index if needed
    public Optional<TenantJob> getActiveJobByType(TenantJobType jobType) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        TenantJobQueueRecord record = tenantDslContext
            .select()
            .from(TENANT_JOB_QUEUE)
            .where(TENANT_JOB_QUEUE.TENANT_ID.eq(tenantId))
            .and(TENANT_JOB_QUEUE.JOB_TYPE.eq(jobType.name()))
            .and(TENANT_JOB_QUEUE.STATUS.in(ACTIVE_JOB_STATUS_LIST))
            .and(TENANT_JOB_QUEUE.IS_DELETED.isFalse())
            .orderBy(TENANT_JOB_QUEUE.CREATED_ON.desc())
            .limit(1)
            .fetchOneInto(TenantJobQueueRecord.class);
        return Optional.ofNullable(record).map(recordMapper::fromRecord);
    }

    public Optional<TenantJob> getActiveJobByTypes(List<TenantJobType> jobTypes) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<String> jobTypeNames = jobTypes.stream().map(TenantJobType::name).toList();
        TenantJobQueueRecord record = tenantDslContext
            .select()
            .from(TENANT_JOB_QUEUE)
            .where(TENANT_JOB_QUEUE.TENANT_ID.eq(tenantId))
            .and(TENANT_JOB_QUEUE.JOB_TYPE.in(jobTypeNames))
            .and(TENANT_JOB_QUEUE.IS_DELETED.isFalse())
            .and(TENANT_JOB_QUEUE.STATUS.in(ACTIVE_JOB_STATUS_LIST))
            .orderBy(TENANT_JOB_QUEUE.CREATED_ON.desc())
            .limit(1)
            .fetchOneInto(TenantJobQueueRecord.class);
        return Optional.ofNullable(record).map(recordMapper::fromRecord);
    }
}
