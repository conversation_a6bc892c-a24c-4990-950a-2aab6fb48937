package com.subskribe.billy.anrok.service;

import static com.subskribe.billy.anrok.service.AnrokIntegrationGetService.makeAnrokClient;
import static com.subskribe.billy.anrok.service.AnrokIntegrationGetService.maskApiKey;
import static com.subskribe.billy.invoice.tax.TaxProductMetadata.ANROK_FEATURE;
import static javax.ws.rs.core.Response.Status.CONFLICT;

import com.subskribe.billy.anrok.client.AnrokClient;
import com.subskribe.billy.anrok.client.AnrokClientException;
import com.subskribe.billy.anrok.model.AnrokIntegrationInput;
import com.subskribe.billy.anrok.model.ImmutableAnrokIntegrationInput;
import com.subskribe.billy.avalara.service.AvalaraIntegrationGetService;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.integration.IntegrationTargetService;
import com.subskribe.billy.integration.model.Integration;
import com.subskribe.billy.integration.model.IntegrationRequest;
import com.subskribe.billy.integration.service.IntegrationPersistenceService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.taxjar.service.TaxJarIntegrationGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;

public class AnrokIntegrationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AnrokIntegrationService.class);

    private static final String FAKE_INTEGRATION_ID = "no_integration";

    private static final String INTEGRATION_ID_NOT_FOUND = "integrationIdNotFound";

    private final IntegrationPersistenceService integrationPersistenceService;
    private final SecretsService secretsService;
    private final TenantIdProvider tenantIdProvider;
    private final AvalaraIntegrationGetService avalaraIntegrationGetService;
    private final TaxJarIntegrationGetService taxJarIntegrationGetService;
    private final TaxRateGetService taxRateGetService;

    @Inject
    public AnrokIntegrationService(
        IntegrationPersistenceService integrationPersistenceService,
        SecretsService secretsService,
        TenantIdProvider tenantIdProvider,
        AvalaraIntegrationGetService avalaraIntegrationGetService,
        TaxJarIntegrationGetService taxJarIntegrationGetService,
        TaxRateGetService taxRateGetService
    ) {
        this.integrationPersistenceService = integrationPersistenceService;
        this.secretsService = secretsService;
        this.tenantIdProvider = tenantIdProvider;
        this.avalaraIntegrationGetService = avalaraIntegrationGetService;
        this.taxJarIntegrationGetService = taxJarIntegrationGetService;
        this.taxRateGetService = taxRateGetService;
    }

    public String addIntegration(AnrokIntegrationInput integrationInput) {
        // Block Anrok integration if Avalara integration exists
        if (avalaraIntegrationGetService.hasAvalaraIntegration()) {
            throw new IllegalStateException("An avalara integration exists. Only one tax service integration is allowed.");
        }

        // Block Anrok integration if TaxJar integration exists
        if (taxJarIntegrationGetService.getTaxJarIntegration().isPresent()) {
            throw new IllegalStateException("A TaxJar integration exists. Only one tax service integration is allowed.");
        }

        if (taxRateGetService.tenantHasPercentBasedTaxRates()) {
            throw new IllegalStateException("Cannot integrate Anrok because percent based tax rates exist");
        }

        if (StringUtils.isBlank(integrationInput.getApiKey())) {
            throw new IllegalArgumentException("Anrok api key is either null or empty.");
        }

        testIntegration(integrationInput);

        String tenantId = tenantIdProvider.provideTenantIdString();
        IntegrationRequest integrationRequest = new IntegrationRequest();
        String apiKey = integrationInput.getApiKey();
        integrationRequest.setTargetService(IntegrationTargetService.ANROK);
        integrationRequest.setClientId(tenantId);
        integrationRequest.setClientSecret(apiKey);
        Integration savedIntegration = integrationPersistenceService.addIntegration(integrationRequest);

        secretsService.storeOrUpdateAnrokApiKey(tenantIdProvider.provideTenantIdString(), integrationInput.getApiKey());
        LOGGER.info("Anrok integration created for tenant ID: {}", tenantIdProvider.provideTenantIdString());
        savedIntegration.setMaskedApiKey(maskApiKey(integrationInput.getApiKey()));
        return String.valueOf(savedIntegration.getIntegrationId());
    }

    public void testIntegration(AnrokIntegrationInput integrationInput) {
        AnrokIntegrationInput input = ImmutableAnrokIntegrationInput.builder().from(integrationInput).build();
        String tenantId = tenantIdProvider.provideTenantIdString();

        if (StringUtils.isBlank(input.getApiKey())) {
            String message = String.format("Anrok api key is either null or empty for tenant id: %s", tenantId);
            LOGGER.warn(ANROK_FEATURE, message);
            throw new IllegalArgumentException(message);
        }

        AnrokClient client = makeAnrokClient(integrationInput.getApiKey());
        try {
            // There is no ping method for the Anrok client. Fetching product mappings to test the connection instead.
            client.listProductIdMapping(FAKE_INTEGRATION_ID);
        } catch (AnrokClientException e) {
            if (e.getStatusCode() == CONFLICT.getStatusCode() && e.getType().equals(INTEGRATION_ID_NOT_FOUND)) {
                // API key is valid but the integration ID is not found. This is expected.
                return;
            }

            LOGGER.warn(ANROK_FEATURE, String.format("Anrok ping failed for tenant id: %s, reason: %s", tenantId, e.getMessage()));
            throw new InvalidInputException("Anrok integration test failed. Please enter a valid API key.");
        }
    }
}
