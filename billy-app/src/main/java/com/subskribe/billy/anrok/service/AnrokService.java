package com.subskribe.billy.anrok.service;

import static javax.ws.rs.core.Response.Status.CONFLICT;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.anrok.client.AnrokClient;
import com.subskribe.billy.anrok.client.AnrokClientException;
import com.subskribe.billy.anrok.client.TenancyAnrokClient;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.exception.AddressValidationException;
import com.subskribe.billy.exception.ExternalDependencyException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.tax.model.CommitTaxTransactionInput;
import com.subskribe.billy.invoice.tax.model.TaxCalculationInput;
import com.subskribe.billy.invoice.tax.model.TaxLineItem;
import com.subskribe.billy.invoice.tax.model.TaxTransaction;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.productcatalog.model.TaxRate;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class AnrokService {

    private static final String TAX_TRANSACTION_FORMAT = "TXN-%s-%s";
    private static final Logger LOGGER = LoggerFactory.getLogger(AnrokService.class);

    private static final String TRANSACTION_FROZEN_FOR_FILING = "transactionFrozenForFiling";
    private static final String TRANSACTION_ID_NOT_FOUND = "transactionIdNotFound";

    private static final Set<Integer> ANROK_USER_ERROR_CODES = Set.of(400, 401, 409);

    private static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd";

    private final AnrokIntegrationGetService anrokIntegrationGetService;
    private final TenantSettingService tenantSettingService;
    private final TaxRateGetService taxRateGetService;

    @Inject
    public AnrokService(
        AnrokIntegrationGetService anrokIntegrationGetService,
        TenantSettingService tenantSettingService,
        TaxRateGetService taxRateGetService
    ) {
        this.anrokIntegrationGetService = anrokIntegrationGetService;
        this.tenantSettingService = tenantSettingService;
        this.taxRateGetService = taxRateGetService;
    }

    public AccountAddress validateAddress(AccountAddress address) {
        Optional<TenancyAnrokClient> anrokClientOptional = anrokIntegrationGetService.getAnrokClientForTenant();
        if (anrokClientOptional.isEmpty()) {
            LOGGER.info("Anrok integration not found.");
            return address;
        }

        AnrokClient anrokClient = anrokClientOptional.get().getClient();
        return validateAddress(address, anrokClient);
    }

    public AccountAddress validateAddress(AccountAddress address, AnrokClient client) throws AddressValidationException {
        if (!TaxService.isAddressInUSA(address)) {
            LOGGER.info("Anrok only validates US addresses");
            return address;
        }

        List<TaxRate> taxRates = taxRateGetService.getTaxRates(PaginationQueryParams.DEFAULT_PAGINATION_QUERY_PARAMS);
        if (CollectionUtils.isEmpty(taxRates)) {
            String message =
                "Address could not be validated. Create a tax rate mapping from Settings > Tax Rate Mapping and re-save contact for address validation.";
            LOGGER.info(message);
            throw new AddressValidationException(message);
        }

        TaxLineItem taxLineItem = TaxLineItem.builder()
            .lineNo("item-1")
            .taxCode(taxRates.get(0).getTaxCode())
            .amount(BigDecimal.valueOf(100))
            .build();
        Map<String, Object> paramsMap = createClientParamsMap(
            createLineItemsParams(List.of(taxLineItem), "USD"),
            address,
            "USD",
            Optional.empty(),
            null,
            null,
            null
        );

        try {
            client.createEphemeralTransaction(paramsMap);
        } catch (AnrokClientException e) {
            String message = "Address could not be resolved. System will not be able to calculate taxes for invoices with this address.";
            if (StringUtils.isNotBlank(e.getType())) {
                message += " Error: " + e.getType();
            }
            LOGGER.info(message);
            throw new AddressValidationException(message);
        }
        return address;
    }

    public Optional<TaxTransaction> createTaxTransaction(TaxCalculationInput taxCalculationInput) {
        if (CollectionUtils.isEmpty(taxCalculationInput.getTaxLineItems())) {
            return Optional.empty();
        }

        return anrokIntegrationGetService.getAnrokClientForTenant().flatMap(anrokClient -> calculateTax(anrokClient, taxCalculationInput));
    }

    public void commitTaxTransaction(CommitTaxTransactionInput commitTransactionInput) {
        LOGGER.info("Committing tax transaction for code {}", commitTransactionInput.getTaxTransactionCode());
        Invoice invoice = commitTransactionInput.getInvoice();
        Validator.checkStateInternal(
            // we allow only posting when invoice is posted, converted, or paid
            invoice.getStatus() == InvoiceStatus.POSTED ||
            invoice.getStatus() == InvoiceStatus.CONVERTED ||
            invoice.getStatus() == InvoiceStatus.PAID,
            String.format("Invoice %s not in expected state %s", invoice.getInvoiceNumber().getNumber(), InvoiceStatus.POSTED)
        );
        String transactionId = getAnrokTransactionId(invoice);
        commitTransactionWithId(commitTransactionInput, transactionId);
    }

    public void voidTaxTransaction(Invoice voidedInvoice) {
        Validator.checkStateInternal(
            voidedInvoice.getStatus() == InvoiceStatus.VOIDED,
            String.format("Invoice %s not in expected state %s", voidedInvoice.getInvoiceNumber().getNumber(), InvoiceStatus.VOIDED)
        );
        String transactionId = getAnrokTransactionId(voidedInvoice);
        LOGGER.info("Voiding transaction id {}", transactionId);

        if (anrokIntegrationGetService.getAnrokIntegration().isEmpty()) {
            LOGGER.info("Anrok not enabled");
            return;
        }

        TenancyAnrokClient tenancyAnrokClient = anrokIntegrationGetService.getAnrokClientForTenant().orElseThrow();

        processTaxVoid(transactionId, tenancyAnrokClient);
    }

    private static void processTaxVoid(String transactionId, TenancyAnrokClient tenancyAnrokClient) {
        try {
            tenancyAnrokClient.getClient().voidTransaction(transactionId);
            LOGGER.info("Successfully deleted anrok transaction {}", transactionId);
        } catch (AnrokClientException e) {
            if (e.getStatusCode() == CONFLICT.getStatusCode() && e.getType().equals(TRANSACTION_FROZEN_FOR_FILING)) {
                processTaxCreateNegation(transactionId, tenancyAnrokClient);
                return;
            }

            if (e.getStatusCode() == CONFLICT.getStatusCode() && e.getType().equals(TRANSACTION_ID_NOT_FOUND)) {
                LOGGER.warn("Transaction id {} not found in Anrok, ignoring", transactionId);
                return;
            }

            throw new ServiceFailureException(String.format("Error deleting tax transaction id : %s", transactionId), e);
        }
    }

    private static void processTaxCreateNegation(String transactionId, TenancyAnrokClient tenancyAnrokClient) {
        Map<String, Object> createParams = createNegationTransactionParams(transactionId);
        try {
            tenancyAnrokClient.getClient().createNegationTransaction(createParams);
            LOGGER.info("Successfully deleted anrok transaction {}", transactionId);
        } catch (AnrokClientException e) {
            throw new ServiceFailureException(String.format("Error deleting tax transaction id : %s", transactionId), e);
        }
    }

    private static Map<String, Object> createNegationTransactionParams(String transactionId) {
        Map<String, Object> params = new HashMap<>();
        params.put("originalTransactionId", transactionId);
        params.put("newTransactionId", transactionId + "-Negative");
        return Collections.unmodifiableMap(params);
    }

    private void commitTransactionWithId(CommitTaxTransactionInput commitTransactionInput, String transactionId) {
        if (anrokIntegrationGetService.getAnrokIntegration().isEmpty()) {
            LOGGER.info("Anrok not enabled");
            return;
        }

        TenancyAnrokClient tenancyAnrokClient = anrokIntegrationGetService.getAnrokClientForTenant().orElseThrow();

        Map<String, Object> paramsMap = createClientParamsMap(commitTransactionInput, transactionId);
        try {
            tenancyAnrokClient.getClient().createOrUpdateTransaction(paramsMap);
            LOGGER.info("Successfully created transaction in Anrok with id {}", transactionId);
        } catch (AnrokClientException e) {
            checkIfAnrokClientExceptionIsUserError(e);
            throw new ServiceFailureException(String.format("Anrok create transaction API call failed for transaction id : %s", transactionId), e);
        }
    }

    private Optional<TaxTransaction> calculateTax(TenancyAnrokClient anrokClient, TaxCalculationInput taxCalculationInput) {
        Map<String, Object> paramsMap = createClientParamsMap(taxCalculationInput);
        try {
            Map<String, Object> taxResponseMap = anrokClient.getClient().createEphemeralTransaction(paramsMap);
            if (taxResponseMap == null) {
                return Optional.empty();
            }
            Integer amountToCollect = (Integer) taxResponseMap.get("taxAmountToCollect");
            BigDecimal totalTax = amountToCollect == null
                ? BigDecimal.ZERO
                : SupportedCurrency.convertFromSmallestUnitToAmount(amountToCollect, taxCalculationInput.getCurrency().getCurrencyCode());
            Map<String, BigDecimal> computedTaxByLineNo = getTaxByLineNumber(taxCalculationInput, taxResponseMap);
            return Optional.of(
                TaxTransaction.builder()
                    .totalTax(totalTax)
                    .transactionCode(taxCalculationInput.getTaxTransactionCode())
                    .computedTaxByLineNo(computedTaxByLineNo)
                    .build()
            );
        } catch (AnrokClientException e) {
            checkIfAnrokClientExceptionIsUserError(e);

            throw new ServiceFailureException(
                String.format(
                    "Anrok API call failure for tax transaction code: %s and account: %s",
                    taxCalculationInput.getTaxTransactionCode(),
                    taxCalculationInput.getAccount().getAccountId()
                ),
                e
            );
        }
    }

    private Map<String, BigDecimal> getTaxByLineNumber(TaxCalculationInput taxCalculationInput, Map<String, Object> taxResponseMap) {
        List<Map<String, Object>> lineItems = (List<Map<String, Object>>) taxResponseMap.get("lineItems");
        if (CollectionUtils.isEmpty(lineItems)) {
            throw new ExternalDependencyException("Anrok response does not contain line items");
        }

        Set<String> lineItemIds = taxCalculationInput.getTaxLineItems().stream().map(TaxLineItem::getLineNo).collect(Collectors.toSet());

        return lineItems
            .stream()
            .collect(
                Collectors.toMap(
                    lineItem -> {
                        String lineNo = (String) lineItem.get("id");
                        if (StringUtils.isBlank(lineNo)) {
                            throw new ExternalDependencyException("Anrok response does not contain line item id");
                        }

                        if (!lineItemIds.contains(lineNo)) {
                            throw new ExternalDependencyException(
                                String.format("Anrok response contains line item id %s that is not present in tax calculation input", lineNo)
                            );
                        }

                        return lineNo;
                    },
                    lineItem ->
                        SupportedCurrency.convertFromSmallestUnitToAmount(
                            (Integer) lineItem.get("taxAmountToCollect"),
                            taxCalculationInput.getCurrency().getCurrencyCode()
                        )
                )
            );
    }

    private static String getAnrokTransactionId(Invoice invoice) {
        String billingAccountId = invoice.getBillingContact().getAccountId();
        return String.format(TAX_TRANSACTION_FORMAT, billingAccountId, invoice.getInvoiceNumber().getNumber());
    }

    private Map<String, Object> createClientParamsMap(TaxCalculationInput taxCalculationInput) {
        String currencyCode = taxCalculationInput.getCurrency().getCurrencyCode();
        return createClientParamsMap(
            createLineItemsParams(taxCalculationInput.getTaxLineItems(), currencyCode),
            taxCalculationInput.getShippingAddress(),
            currencyCode,
            taxCalculationInput.getInvoiceDate(),
            StringUtils.isNotBlank(taxCalculationInput.getAccount().getExternalId())
                ? taxCalculationInput.getAccount().getExternalId()
                : taxCalculationInput.getAccount().getAccountId(),
            taxCalculationInput.getAccount().getName(),
            null
        );
    }

    private Map<String, Object> createClientParamsMap(CommitTaxTransactionInput commitTransactionInput, String transactionId) {
        String currencyCode = commitTransactionInput.getInvoice().getCurrencyCode();
        Account account = commitTransactionInput.getAccount();
        AccountAddress shippingAddress = commitTransactionInput.getShippingAddress();
        return createClientParamsMap(
            createLineItemsParams(commitTransactionInput.getInvoice(), currencyCode),
            shippingAddress,
            currencyCode,
            Optional.ofNullable(commitTransactionInput.getInvoice().getInvoiceDate()),
            StringUtils.isNotBlank(account.getExternalId()) ? account.getExternalId() : account.getAccountId(),
            account.getName(),
            transactionId
        );
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private Map<String, Object> createClientParamsMap(
        List<Map<String, Object>> lineItemsParams,
        AccountAddress accountAddress,
        String currencyCode,
        Optional<Instant> invoiceDate,
        String accountId,
        String accountName,
        String transactionId
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("currencyCode", currencyCode);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        String dateString = getDateString(invoiceDate.orElseGet(Instant::now), timeZone);
        params.put("accountingDate", dateString);
        params.put("taxDate", dateString);

        params.put("lineItems", lineItemsParams);

        String streetAddressLine = StringUtils.isNotBlank(accountAddress.getStreetAddressLine2())
            ? accountAddress.getStreetAddressLine1() + " " + accountAddress.getStreetAddressLine2()
            : accountAddress.getStreetAddressLine1();
        Map<String, Object> addressMap = new HashMap<>();
        addressMap.put("country", accountAddress.getCountry());
        addressMap.put("line1", streetAddressLine);
        addressMap.put("city", accountAddress.getCity());
        addressMap.put("region", accountAddress.getState());
        addressMap.put("postalCode", accountAddress.getZipcode());
        params.put("customerAddress", addressMap);

        if (StringUtils.isNotBlank(accountId)) {
            params.put("customerId", accountId);
        }
        if (StringUtils.isNotBlank(accountName)) {
            params.put("customerName", accountName);
        }

        if (StringUtils.isNotBlank(transactionId)) {
            params.put("id", transactionId);
        }

        return Collections.unmodifiableMap(params);
    }

    private String getDateString(Instant instant, TimeZone timeZone) {
        ZoneId zoneId = timeZone.toZoneId();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN).withZone(zoneId);
        return dateTimeFormatter.format(instant);
    }

    private static List<Map<String, Object>> createLineItemsParams(List<TaxLineItem> taxLineItems, String currencyCode) {
        return taxLineItems
            .stream()
            .sorted(Comparator.comparing(TaxLineItem::getLineNo))
            .map(lineItem -> {
                Map<String, Object> lineParams = new HashMap<>();
                lineParams.put("id", lineItem.getLineNo());
                if (StringUtils.isNotBlank(lineItem.getTaxCode())) {
                    lineParams.put("productExternalId", lineItem.getTaxCode());
                }
                long anrokAmount = SupportedCurrency.convertAmountToSmallestUnit(lineItem.getAmount(), currencyCode);
                lineParams.put("amount", anrokAmount);
                lineParams.put("isTaxIncludedInAmount", false);
                return lineParams;
            })
            .toList();
    }

    private static List<Map<String, Object>> createLineItemsParams(Invoice invoice, String currencyCode) {
        return invoice
            .getInvoiceItems()
            .stream()
            .sorted(Comparator.comparing(InvoiceItem::getInvoiceLineNumber))
            .map(lineItem -> {
                Map<String, Object> lineParams = new HashMap<>();
                lineParams.put("id", lineItem.getInvoiceLineNumber());
                if (StringUtils.isNotBlank(lineItem.getTaxRate().getTaxCode())) {
                    lineParams.put("productExternalId", lineItem.getTaxRate().getTaxCode());
                }
                long anrokAmount = SupportedCurrency.convertAmountToSmallestUnit(lineItem.getAmount(), currencyCode);
                lineParams.put("amount", anrokAmount);
                lineParams.put("isTaxIncludedInAmount", false);
                return lineParams;
            })
            .toList();
    }

    private static void checkIfAnrokClientExceptionIsUserError(AnrokClientException e) {
        if (e.getStatusCode() != null && ANROK_USER_ERROR_CODES.contains(e.getStatusCode())) {
            String errorMessage = String.format("Unable to create tax transaction: %s. Please contact your admin.", e.getType());
            throw new InvalidInputException(errorMessage);
        }
    }
}
