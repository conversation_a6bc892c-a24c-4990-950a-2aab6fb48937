package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.resources.shared.ResponseUtility;
import com.subskribe.billy.shared.batchupload.BatchUploadResult;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.util.List;
import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

@Path("/ratecards")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class RateCardResource {

    private final RateCardService rateCardService;

    @Inject
    public RateCardResource(RateCardService rateCardService) {
        this.rateCardService = rateCardService;
    }

    @GET
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(
        value = "Fetch the list of rate cards stored in the system",
        notes = "Fetch the list of rate cards stored in the system",
        tags = { "RateCard" }
    )
    @AllowAllRoles
    public Response getRateCards(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        List<RateCard> rateCards = rateCardService.getRateCards();
        return Response.ok(rateCards).build();
    }

    @POST
    @Path("/csv")
    @ApiOperation(
        value = "create a rate card using CSV price table multipart form data",
        notes = "create a rate card using the multipart form data, where a CSV can be passed in for the price table",
        tags = { "RateCard" }
    )
    @AllowRoles({ Role.ADMIN })
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces({ MediaType.APPLICATION_JSON })
    public Response addRateCardCsv(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @FormDataParam("name") String name,
        @FormDataParam("description") String description,
        @FormDataParam("currency") String currency,
        @FormDataParam("file") InputStream importStream,
        @FormDataParam("file") FormDataContentDisposition contentDisposition
    ) throws IOException {
        RateCard result = rateCardService.addRateCardCsv(name, description, currency, importStream);
        return Response.ok(result).build();
    }

    @PUT
    @Path("/{id}/csv")
    @ApiOperation(
        value = "create a rate card using CSV price table multipart form data",
        notes = "create a rate card using the multipart form data, where a CSV can be passed in for the price table",
        tags = { "RateCard" }
    )
    @AllowRoles({ Role.ADMIN })
    @Produces({ MediaType.APPLICATION_JSON })
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    public Response updateRateCardCsv(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(name = "id", value = "rate card id", required = true) String id,
        @FormDataParam("name") String name,
        @FormDataParam("description") String description,
        @FormDataParam("currency") String currency,
        @FormDataParam("file") InputStream importStream,
        @FormDataParam("file") FormDataContentDisposition contentDisposition
    ) throws IOException {
        RateCard result = rateCardService.updateRateCardCsv(id, name, description, currency, importStream);
        return Response.ok(result).build();
    }

    @GET
    @Path("/{id}/priceTable")
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @ApiOperation(
        value = "Fetch the price table for the rate card given the id",
        notes = "Get the price table for the rate card given the id",
        tags = { "RateCard" }
    )
    @AllowAllRoles
    public Response getRateCardPriceTable(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(name = "id", value = "rate card id", required = true) String id
    ) {
        return rateCardService
            .getRateCardPriceTableCsv(id)
            .map(output -> {
                String fileName = String.format("%s_price_table_%s.csv", id, Instant.now());
                return ResponseUtility.okResponseFromStreamingOutputAndFileName(output, fileName, "text/csv");
            })
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.RATE_CARD, id));
    }

    @GET
    @Path("/attributes/csv")
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @ApiOperation(
        value = "Fetch the price attributes defined in the system in CSV format",
        notes = "Get all the price attributes in the system in CSV format one per row",
        tags = { "RateCard" }
    )
    @AllowAllRoles
    public Response getPriceAttributesCsv(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        StreamingOutput streamingOutput = rateCardService.getPriceAttributesCsv();
        return ResponseUtility.okResponseFromStreamingOutputAndFileName(streamingOutput, "price_attributes.csv", "text/csv");
    }

    @POST
    @Path("/attributes/csv")
    @ApiOperation(
        value = "Import price attributes into the system",
        notes = "Import the price attributes from an input CSV file, the output provides details on each row of the input",
        tags = { "RateCard" }
    )
    @AllowRoles({ Role.ADMIN })
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    public Response importPriceAttributes(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @FormDataParam("file") InputStream importStream,
        @FormDataParam("file") FormDataContentDisposition contentDisposition
    ) throws IOException {
        BatchUploadResult result = rateCardService.importPriceAttributes(importStream);
        return Response.ok(result).build();
    }
}
