package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationTargetAndSubscriptions;
import com.subskribe.billy.notification.service.NotificationTargetService;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.validation.Validator;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import java.util.UUID;
import javax.inject.Inject;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.apache.commons.lang3.StringUtils;

@Path("/notifications")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class NotificationResource {

    private final NotificationTargetService notificationTargetService;

    @Inject
    public NotificationResource(NotificationTargetService notificationTargetService) {
        this.notificationTargetService = notificationTargetService;
    }

    @GET
    @ApiOperation(
        value = "Get all notifications",
        notes = "Returns all notification subscriptions for your tenant",
        response = NotificationTargetAndSubscriptions.class,
        responseContainer = "List",
        tags = { "Notifications" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getAllNotificationSubscriptionsForTenant(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        List<NotificationTargetAndSubscriptions> allSubscriptions = notificationTargetService.getAllNotificationSubscriptionsForTenant();
        return Response.ok(allSubscriptions).build();
    }

    @POST
    @Path("/{notificationId}")
    @ApiOperation(
        value = "Attach an event to a notification",
        notes = "Attaches a notification event to the target specified by the notification id",
        tags = { "Notifications" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response subscribeExistingNotificationTargetToEvent(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("notificationId") @ApiParam(value = "id of the notification target", required = true) String notificationIdString,
        @QueryParam("notificationEventType") @ApiParam(
            value = "type of event",
            required = true,
            allowableValues = "INVOICE_POSTED, SUBSCRIPTION_CREATED, ORDER_SUBMITTED, ORDER_EXECUTED"
        ) String notificationEventTypeString
    ) {
        UUID notificationId = UUID.fromString(notificationIdString);

        NotificationEventType notificationEventType = Validator.enumFromString(
            notificationEventTypeString,
            NotificationEventType.class,
            "notificationEventType"
        );
        notificationTargetService.subscribeExistingNotificationTargetToEvent(notificationId, notificationEventType);
        return Response.ok().build();
    }

    @POST
    @Path("/unsubscribe/{notificationId}")
    @ApiOperation(
        value = "Unsubscribe from an event",
        notes = "Unsubscribes the specified notification target from the specified event",
        tags = { "Notifications" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response unsubscribeTargetOrEvent(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("notificationId") @ApiParam(value = "id of the notification target", required = true) String notificationIdString,
        @QueryParam("notificationEventType") @ApiParam(
            value = "type of event",
            required = true,
            allowableValues = "INVOICE_POSTED, SUBSCRIPTION_CREATED, ORDER_SUBMITTED, ORDER_EXECUTED"
        ) String notificationEventTypeString
    ) {
        UUID notificationId = UUID.fromString(notificationIdString);
        if (StringUtils.isBlank(notificationEventTypeString)) {
            notificationTargetService.unsubscribeNotificationTarget(notificationId);
        } else {
            NotificationEventType notificationEventType = Validator.enumFromString(
                notificationEventTypeString,
                NotificationEventType.class,
                "notificationEventType"
            );
            notificationTargetService.unsubscribeNotificationTargetFromEvent(notificationId, notificationEventType);
        }
        return Response.ok().build();
    }

    @POST
    @ApiOperation(
        value = "Add a notification target",
        notes = "Adds a notification target and events according to the specified parameters",
        response = NotificationTargetAndSubscriptions.class,
        tags = { "Notifications" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response addTargetAndEventsSubscriptions(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @ApiParam(
            value = "json representing the notification details",
            required = true
        ) NotificationTargetAndSubscriptions notificationTargetAndSubscriptions
    ) {
        UUID notificationId = notificationTargetService.addNotificationTargetAndSubscriptions(notificationTargetAndSubscriptions);
        NotificationTargetAndSubscriptions notification = notificationTargetService.getNotificationTargetAndSubscriptionsById(notificationId, false);
        return Response.ok(notification).build();
    }
}
