package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.service.CompositeOrderDocumentService;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.compositeorder.service.CompositeOrderIdGenerator;
import com.subskribe.billy.compositeorder.service.CompositeOrderService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.resources.json.order.CompositeOrderJson;
import com.subskribe.billy.resources.json.order.CompositeOrderMapper;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.resources.shared.FileResponseBuilder;
import com.subskribe.billy.salesroom.intelligent.model.response.IntelligentSalesRoomOverviewResponse;
import com.subskribe.billy.salesroom.intelligent.service.IntelligentSalesRoomService;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.InputStream;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.lang3.BooleanUtils;
import org.mapstruct.factory.Mappers;

@Path("/compositeOrders")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class CompositeOrderResource {

    private final CompositeOrderGetService compositeOrderGetService;
    private final CompositeOrderService compositeOrderService;
    private final OrderGetService orderGetService;
    private final CompositeOrderDocumentService compositeOrderDocumentService;
    private final CompositeOrderMapper compositeOrderMapper;
    private final OrderMapper orderMapper;

    private final IntelligentSalesRoomService intelligentSalesRoomService;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public CompositeOrderResource(
        CompositeOrderGetService compositeOrderGetService,
        CompositeOrderService compositeOrderService,
        OrderGetService orderGetService,
        CompositeOrderDocumentService compositeOrderDocumentService,
        IntelligentSalesRoomService intelligentSalesRoomService,
        TenantIdProvider tenantIdProvider
    ) {
        this.compositeOrderGetService = compositeOrderGetService;
        this.compositeOrderService = compositeOrderService;
        this.orderGetService = orderGetService;
        this.compositeOrderDocumentService = compositeOrderDocumentService;
        this.intelligentSalesRoomService = intelligentSalesRoomService;
        this.tenantIdProvider = tenantIdProvider;
        compositeOrderMapper = Mappers.getMapper(CompositeOrderMapper.class);
        orderMapper = Mappers.getMapper(OrderMapper.class);
    }

    @GET
    @Path("/{id}")
    @ApiOperation(
        value = "Fetch composite order",
        notes = "Fetches the composite order with the specified id.",
        response = CompositeOrderJson.class,
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response getCompositeOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "id of the composite order", required = true) @NotBlank String compositeOrderId
    ) {
        CompositeOrderIdGenerator.verifyPrefixMatches(compositeOrderId);
        CompositeOrderJson compositeOrderJson = getCompositeOrderResponse(compositeOrderId);
        return Response.ok(compositeOrderJson).build();
    }

    @PUT
    @Path("/{id}/status/{status}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Update composite order status", notes = "Update the status of a specific composite order by its ID.", tags = { "Orders" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    public Response updateCompositeOrderStatus(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(name = "id", value = "Uniquely identifies the composite order.", required = true) String compositeOrderId,
        @PathParam("status") @ApiParam(
            name = "status",
            value = "New status to be set for the composite order: Draft, Submitted or Executed",
            required = true,
            allowableValues = "DRAFT, SUBMITTED, EXECUTED"
        ) String status,
        @QueryParam("statusUpdatedOn") @ApiParam(
            name = "statusUpdatedOn",
            value = "The timestamp when composite order status was updated."
        ) Long statusUpdatedOn,
        @QueryParam("adminApprovalFlowByPass") @ApiParam(
            name = "adminApprovalFlowByPass",
            value = "Admin approval to bypass the approval flow"
        ) Boolean adminApprovalByPass,
        @Context UriInfo uriInfo
    ) {
        boolean adminApprovalFlowByPass = BooleanUtils.isTrue(adminApprovalByPass);
        OrderStatus orderStatus = OrderStatus.valueOf(status.toUpperCase());
        Optional<Instant> statusUpdatedOnInstant = Optional.ofNullable(statusUpdatedOn).map(Instant::ofEpochSecond);
        compositeOrderService.updateCompositeOrderStatus(compositeOrderId, orderStatus, statusUpdatedOnInstant, adminApprovalFlowByPass);

        return Response.status(Response.Status.OK).build();
    }

    @PUT
    @Path("/{id}/execute")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Mark composite order as executed.",
        notes = "Marks the composite order as executed. This also executes the individual orders contained. Optionally, the execution time can be specified using the executedOn query parameter.",
        response = CompositeOrderJson.class,
        tags = { "Orders" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES_MANAGER })
    public Response executeOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the composite order.", required = true) String compositeOrderId,
        @QueryParam("executedOn") @ApiParam(name = "executedOn", value = "The date and time when the composite order was executed.") Long executedOn,
        @QueryParam("adminApprovalFlowBypass") @ApiParam(
            name = "adminApprovalFlowBypass",
            value = "Bypass approval checks by admin"
        ) Boolean adminApprovalFlowBypass
    ) {
        boolean adminApprovalByPass = BooleanUtils.isTrue(adminApprovalFlowBypass);
        boolean compositeOrderExists = compositeOrderGetService.compositeOrderExists(compositeOrderId);

        if (!compositeOrderExists) {
            throw new ObjectNotFoundException(BillyObjectType.COMPOSITE_ORDER, compositeOrderId);
        }
        compositeOrderService.executeCompositeOrder(compositeOrderId, executedOn, adminApprovalByPass);
        CompositeOrderJson compositeOrderJson = getCompositeOrderResponse(compositeOrderId);
        return Response.ok(compositeOrderJson).build();
    }

    @DELETE
    @Path("/{id}")
    @ApiOperation(value = "Delete composite order", notes = "Deletes the composite order with the specified id.", tags = { "Orders" })
    @AllowAllRoles
    public Response deleteCompositeOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "id of the composite order", required = true) @NotBlank String compositeOrderId
    ) {
        CompositeOrderIdGenerator.verifyPrefixMatches(compositeOrderId);
        compositeOrderService.deleteCompositeOrder(compositeOrderId);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/pdf")
    @ApiOperation(
        value = "Generate a composite order PDF",
        notes = "Generates a composite order form PDF. When completed this document can be downloaded via a get to /{id}/pdf.",
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response createCompositeOrderDocument(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "id of the composite order", required = true) String compositeOrderId
    ) {
        try {
            compositeOrderDocumentService.createCompositeOrderDocumentWithoutChecks(compositeOrderId);
            return Response.ok().build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
        }
    }

    @POST
    @Path("/salesRoom/{shareLink}/pdf")
    @ApiOperation(
        value = "Generate a composite order PDF via Sales Room Share Link",
        notes = "Generate and retrieve a PDF representation of the composite order details for a specific sales room using share link.",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response createCompositeOrderDocumentForSalesRoom(@NotEmpty @PathParam("shareLink") String shareLink) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);
        TenantContextInjector.runInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            compositeOrderDocumentService.createCompositeOrderDocumentWithoutChecks(salesRoom.getOrderId())
        );
        return Response.ok().build();
    }

    @GET
    @Path("/{id}/pdf")
    @ApiOperation(
        value = "Fetch composite order form PDF",
        notes = "Downloads the order form PDF for the specified order. This PDF must have been generated via a POST to " +
        "/{id}/pdf. The response is the PDF document.",
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response getCompositeOrderDocument(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "id of the order", required = true) String compositeOrderId
    ) {
        Optional<InputStream> inputStream = compositeOrderDocumentService.getCompositeOrderDocumentCreateIfNeeded(compositeOrderId);

        if (inputStream.isEmpty()) {
            return Response.status(Response.Status.ACCEPTED).build();
        }

        String fileName = compositeOrderDocumentService.getCompositeOrderDocumentFileName(compositeOrderId);
        return FileResponseBuilder.getFileResponse(inputStream.get(), fileName);
    }

    private CompositeOrderJson getCompositeOrderResponse(String compositeOrderId) {
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        List<Order> orders = orderGetService.getOrdersInCompositeOrder(compositeOrderId);
        List<OrderJson> orderJsons = orders.stream().map(orderMapper::orderToJson).toList();
        return compositeOrderMapper.compositeOrderToJson(compositeOrder, orderJsons, orderJsons.get(0).getAccountId());
    }
}
