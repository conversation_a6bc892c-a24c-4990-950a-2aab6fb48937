package com.subskribe.billy.resources.json.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.order.CreditableAmount;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrder;
import com.subskribe.billy.opportunity.model.OpportunityCrmType;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.resources.json.opportunity.OpportunityInput;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.shared.TenantDiscountJson;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.template.model.DocumentCustomContent;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.BooleanUtils;

@GraphQLName("OrderRequest")
public class OrderRequestJson {

    @JsonProperty
    @GraphQLField
    @GraphQLName("id")
    private String id;

    @JsonProperty
    @GraphQLField
    @GraphQLName("externalId")
    private String externalId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("name")
    private String name;

    @JsonProperty
    @GraphQLField
    @GraphQLName("accountId")
    String accountId;

    @NotNull
    @JsonProperty
    @GraphQLField
    @GraphQLName("orderType")
    @GraphQLNonNull
    private OrderType orderType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("paymentTerm")
    private String paymentTerm;

    @JsonProperty
    @GraphQLField
    @GraphQLName("subscriptionId")
    private String subscriptionId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("shippingContactId")
    private String shippingContactId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingContactId")
    private String billingContactId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("predefinedDiscounts")
    private List<TenantDiscountJson> predefinedDiscounts;

    @JsonProperty
    @GraphQLField
    @GraphQLName("creditableAmounts")
    private List<CreditableAmount> creditableAmounts;

    @JsonProperty
    @GraphQLField
    @GraphQLName("lineItems")
    private List<OrderLineItemRequestJson> lineItems;

    @NotNull
    @JsonProperty
    @GraphQLField
    @GraphQLName("startDate")
    @GraphQLNonNull
    private Long startDate;

    @JsonProperty
    @GraphQLField
    @GraphQLName("endDate")
    private Long endDate;

    @JsonProperty
    @GraphQLField
    @GraphQLName("executedOn")
    private Long executedOn;

    @JsonProperty
    @GraphQLField
    @GraphQLName("termLength")
    private RecurrenceJson termLength;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingCycle")
    private RecurrenceJson billingCycle;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingTerm")
    private BillingTerm billingTerm;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingAnchorDate")
    private Long billingAnchorDate;

    @GraphQLField
    @GraphQLName("rampInterval")
    private List<Long> rampInterval;

    @JsonProperty
    @GraphQLField
    @GraphQLName("orderFormTemplateIds")
    private List<String> orderFormTemplateIds;

    // TODO: Rename and remove _sfdc_ from all following fields.

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityId")
    private String sfdcOpportunityId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("isPrimaryOrderForSfdcOpportunity")
    private Boolean isPrimaryOrderForSfdcOpportunity;

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityName")
    private String sfdcOpportunityName;

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityType")
    private String sfdcOpportunityType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityStage")
    private String sfdcOpportunityStage;

    @JsonProperty
    @GraphQLField
    @GraphQLName("opportunityCrmType")
    private OpportunityCrmType opportunityCrmType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("ownerId")
    private String ownerId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("renewalForSubscriptionId")
    private String renewalForSubscriptionId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("documentMasterTemplateId")
    private String documentMasterTemplateId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("documentCustomContent")
    private DocumentCustomContent documentCustomContent;

    @JsonProperty
    @GraphQLField
    @GraphQLName("purchaseOrderNumber")
    private String purchaseOrderNumber;

    @JsonProperty
    @GraphQLField
    @GraphQLName("purchaseOrderRequiredForInvoicing")
    private Boolean purchaseOrderRequiredForInvoicing;

    @JsonProperty
    @GraphQLField
    @GraphQLName("autoRenew")
    private boolean autoRenew;

    @JsonProperty
    @GraphQLField
    @GraphQLName("approvalSegmentId")
    private String approvalSegmentId;

    @JsonProperty
    @GraphQLField
    private String attachmentId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("expiresOn")
    private Long expiresOn;

    @JsonProperty
    @GraphQLField
    private String entityId;

    @Valid
    @JsonProperty
    @GraphQLField
    @GraphQLName("customFields")
    private List<CustomFieldEntry> customFields;

    @JsonProperty
    @GraphQLField
    @GraphQLName("startDateType")
    private OrderStartDateType startDateType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("currency")
    private String currency;

    @JsonProperty
    @GraphQLField
    @GraphQLName("customBillingSchedule")
    private CustomBillingScheduleInput customBillingSchedule;

    @JsonProperty
    @GraphQLField
    @GraphQLName("customPredefinedTemplatesOnOrder")
    private List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder;

    @JsonProperty
    @GraphQLField
    @GraphQLName("subscriptionDurationModel")
    private SubscriptionDurationModel subscriptionDurationModel;

    @JsonProperty
    @GraphQLField
    @GraphQLName("opportunityInput")
    private OpportunityInput opportunityInput;

    public OrderRequestJson() {
        customFields = new ArrayList<>();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAccountId() {
        return accountId;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public String getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(String paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getShippingContactId() {
        return shippingContactId;
    }

    public void setShippingContactId(String shippingContactId) {
        this.shippingContactId = shippingContactId;
    }

    public String getBillingContactId() {
        return billingContactId;
    }

    public void setBillingContactId(String billingContactId) {
        this.billingContactId = billingContactId;
    }

    public List<TenantDiscountJson> getPredefinedDiscounts() {
        return predefinedDiscounts;
    }

    public void setPredefinedDiscounts(List<TenantDiscountJson> predefinedDiscounts) {
        this.predefinedDiscounts = predefinedDiscounts;
    }

    public List<CreditableAmount> getCreditableAmounts() {
        return creditableAmounts;
    }

    public void setCreditableAmounts(List<CreditableAmount> creditableAmounts) {
        this.creditableAmounts = creditableAmounts;
    }

    public List<OrderLineItemRequestJson> getLineItems() {
        return lineItems;
    }

    public void setLineItems(List<OrderLineItemRequestJson> lineItems) {
        this.lineItems = lineItems;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public Long getExecutedOn() {
        return executedOn;
    }

    public void setExecutedOn(Long executedOn) {
        this.executedOn = executedOn;
    }

    public RecurrenceJson getTermLength() {
        return termLength;
    }

    public void setTermLength(RecurrenceJson termLength) {
        this.termLength = termLength;
    }

    public RecurrenceJson getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(RecurrenceJson billingCycle) {
        this.billingCycle = billingCycle;
    }

    public BillingTerm getBillingTerm() {
        return billingTerm;
    }

    public void setBillingTerm(BillingTerm billingTerm) {
        this.billingTerm = billingTerm;
    }

    public Long getBillingAnchorDate() {
        return billingAnchorDate;
    }

    public void setBillingAnchorDate(Long billingAnchorDate) {
        this.billingAnchorDate = billingAnchorDate;
    }

    public List<Long> getRampInterval() {
        return rampInterval;
    }

    public void setRampInterval(List<Long> rampInterval) {
        this.rampInterval = rampInterval;
    }

    public List<String> getOrderFormTemplateIds() {
        return Objects.requireNonNullElseGet(orderFormTemplateIds, List::of);
    }

    public void setOrderFormTemplateIds(List<String> orderFormTemplateIds) {
        this.orderFormTemplateIds = orderFormTemplateIds;
    }

    public String getSfdcOpportunityId() {
        return sfdcOpportunityId;
    }

    public void setSfdcOpportunityId(String sfdcOpportunityId) {
        this.sfdcOpportunityId = sfdcOpportunityId;
    }

    public String getSfdcOpportunityName() {
        return sfdcOpportunityName;
    }

    public void setSfdcOpportunityName(String sfdcOpportunityName) {
        this.sfdcOpportunityName = sfdcOpportunityName;
    }

    public String getSfdcOpportunityType() {
        return sfdcOpportunityType;
    }

    public void setSfdcOpportunityType(String sfdcOpportunityType) {
        this.sfdcOpportunityType = sfdcOpportunityType;
    }

    public String getSfdcOpportunityStage() {
        return sfdcOpportunityStage;
    }

    public void setSfdcOpportunityStage(String sfdcOpportunityStage) {
        this.sfdcOpportunityStage = sfdcOpportunityStage;
    }

    public boolean getIsPrimaryOrderForSfdcOpportunity() {
        return BooleanUtils.isTrue(isPrimaryOrderForSfdcOpportunity);
    }

    public void setIsPrimaryOrderForSfdcOpportunity(Boolean primaryOrderForSfdcOpportunity) {
        isPrimaryOrderForSfdcOpportunity = primaryOrderForSfdcOpportunity;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getRenewalForSubscriptionId() {
        return renewalForSubscriptionId;
    }

    public void setRenewalForSubscriptionId(String renewalForSubscriptionId) {
        this.renewalForSubscriptionId = renewalForSubscriptionId;
    }

    public String getDocumentMasterTemplateId() {
        return documentMasterTemplateId;
    }

    public void setDocumentMasterTemplateId(String documentMasterTemplateId) {
        this.documentMasterTemplateId = documentMasterTemplateId;
    }

    public DocumentCustomContent getDocumentCustomContent() {
        return documentCustomContent;
    }

    public void setDocumentCustomContent(DocumentCustomContent documentCustomContent) {
        this.documentCustomContent = documentCustomContent;
    }

    public String getPurchaseOrderNumber() {
        return purchaseOrderNumber;
    }

    public void setPurchaseOrderNumber(String purchaseOrderNumber) {
        this.purchaseOrderNumber = purchaseOrderNumber;
    }

    public Boolean getPurchaseOrderRequiredForInvoicing() {
        return purchaseOrderRequiredForInvoicing;
    }

    public void setPurchaseOrderRequiredForInvoicing(Boolean purchaseOrderRequiredForInvoicing) {
        this.purchaseOrderRequiredForInvoicing = purchaseOrderRequiredForInvoicing;
    }

    public boolean isAutoRenew() {
        return autoRenew;
    }

    public void setAutoRenew(boolean autoRenew) {
        this.autoRenew = autoRenew;
    }

    public OpportunityCrmType getOpportunityCrmType() {
        return opportunityCrmType;
    }

    public void setOpportunityCrmType(OpportunityCrmType opportunityCrmType) {
        this.opportunityCrmType = opportunityCrmType;
    }

    public String getApprovalSegmentId() {
        return approvalSegmentId;
    }

    public void setApprovalSegmentId(String approvalSegmentId) {
        this.approvalSegmentId = approvalSegmentId;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    public Long getExpiresOn() {
        return expiresOn;
    }

    public void setExpiresOn(Long expiresOn) {
        this.expiresOn = expiresOn;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public List<CustomFieldEntry> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<CustomFieldEntry> customFields) {
        this.customFields = customFields;
    }

    public OrderStartDateType getStartDateType() {
        return startDateType;
    }

    public void setStartDateType(OrderStartDateType startDateType) {
        this.startDateType = startDateType;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public CustomBillingScheduleInput getCustomBillingSchedule() {
        return customBillingSchedule;
    }

    public void setCustomBillingSchedule(CustomBillingScheduleInput customBillingSchedule) {
        this.customBillingSchedule = customBillingSchedule;
    }

    public List<CustomPredefinedTemplateOnOrder> getCustomPredefinedTemplatesOnOrder() {
        return customPredefinedTemplatesOnOrder;
    }

    public void setCustomPredefinedTemplatesOnOrder(List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder) {
        this.customPredefinedTemplatesOnOrder = customPredefinedTemplatesOnOrder;
    }

    public SubscriptionDurationModel getSubscriptionDurationModel() {
        return subscriptionDurationModel;
    }

    public void setSubscriptionDurationModel(SubscriptionDurationModel subscriptionDurationModel) {
        this.subscriptionDurationModel = subscriptionDurationModel;
    }

    public OpportunityInput getOpportunityInput() {
        return opportunityInput;
    }

    public void setOpportunityInput(OpportunityInput opportunityInput) {
        this.opportunityInput = opportunityInput;
    }
}
