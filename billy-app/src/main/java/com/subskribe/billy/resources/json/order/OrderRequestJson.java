package com.subskribe.billy.resources.json.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.order.CreditableAmount;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrder;
import com.subskribe.billy.opportunity.model.OpportunityCrmType;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.resources.json.opportunity.OpportunityInput;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.shared.TenantDiscountJson;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.template.model.DocumentCustomContent;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.BooleanUtils;

@GraphQLName("OrderRequest")
@ApiModel(description = "JSON object containing information required to create an order.")
public class OrderRequestJson {

    @JsonProperty
    @GraphQLField
    @GraphQLName("id")
    @ApiModelProperty(value = OrderRequestDocumentation.ID, example = OrderRequestDocumentation.ID_EXAMPLE)
    private String id;

    @JsonProperty
    @GraphQLField
    @GraphQLName("externalId")
    @ApiModelProperty(value = OrderRequestDocumentation.EXTERNAL_ID, example = OrderRequestDocumentation.EXTERNAL_ID_EXAMPLE)
    private String externalId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("name")
    @ApiModelProperty(value = OrderRequestDocumentation.NAME, example = OrderRequestDocumentation.NAME_EXAMPLE)
    private String name;

    @JsonProperty
    @GraphQLField
    @GraphQLName("accountId")
    @ApiModelProperty(value = OrderRequestDocumentation.ACCOUNT_ID, example = OrderRequestDocumentation.ACCOUNT_ID_EXAMPLE)
    String accountId;

    @NotNull
    @JsonProperty
    @GraphQLField
    @GraphQLName("orderType")
    @ApiModelProperty(
        value = OrderRequestDocumentation.ORDER_TYPE,
        example = OrderRequestDocumentation.ORDER_TYPE_EXAMPLE,
        allowableValues = "NEW, AMENDMENT, RENEWAL, RESTRUCTURE"
    )
    @GraphQLNonNull
    private OrderType orderType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("paymentTerm")
    @ApiModelProperty(value = OrderRequestDocumentation.PAYMENT_TERM, example = OrderRequestDocumentation.PAYMENT_TERM_EXAMPLE)
    private String paymentTerm;

    @JsonProperty
    @GraphQLField
    @GraphQLName("subscriptionId")
    @ApiModelProperty(value = OrderRequestDocumentation.SUBSCRIPTION_ID, example = OrderRequestDocumentation.SUBSCRIPTION_ID_EXAMPLE)
    private String subscriptionId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("shippingContactId")
    @ApiModelProperty(value = OrderRequestDocumentation.SHIPPING_CONTACT_ID, example = OrderRequestDocumentation.SHIPPING_CONTACT_ID_EXAMPLE)
    private String shippingContactId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingContactId")
    @ApiModelProperty(value = OrderRequestDocumentation.BILLING_CONTACT_ID, example = OrderRequestDocumentation.BILLING_CONTACT_ID_EXAMPLE)
    private String billingContactId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("predefinedDiscounts")
    @ApiModelProperty(value = OrderRequestDocumentation.PREDEFINED_DISCOUNTS)
    private List<TenantDiscountJson> predefinedDiscounts;

    @JsonProperty
    @GraphQLField
    @GraphQLName("creditableAmounts")
    @ApiModelProperty(value = OrderRequestDocumentation.CREDITABLE_AMOUNTS)
    private List<CreditableAmount> creditableAmounts;

    @JsonProperty
    @GraphQLField
    @GraphQLName("lineItems")
    @ApiModelProperty(value = OrderRequestDocumentation.LINE_TEMS)
    private List<OrderLineItemRequestJson> lineItems;

    @NotNull
    @JsonProperty
    @GraphQLField
    @GraphQLName("startDate")
    @GraphQLNonNull
    @ApiModelProperty(value = OrderRequestDocumentation.START_DATE, example = OrderRequestDocumentation.START_DATE_EXAMPLE)
    private Long startDate;

    @JsonProperty
    @GraphQLField
    @GraphQLName("endDate")
    @ApiModelProperty(value = OrderRequestDocumentation.END_DATE, example = OrderRequestDocumentation.END_DATE_EXAMPLE)
    private Long endDate;

    @JsonProperty
    @GraphQLField
    @GraphQLName("executedOn")
    @ApiModelProperty(value = OrderRequestDocumentation.EXECUTED_ON, example = OrderRequestDocumentation.EXECUTED_ON_EXAMPLE)
    private Long executedOn;

    @JsonProperty
    @GraphQLField
    @GraphQLName("termLength")
    @ApiModelProperty(value = OrderRequestDocumentation.TERM_LENGTH)
    private RecurrenceJson termLength;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingCycle")
    @ApiModelProperty(value = OrderRequestDocumentation.BILLING_CYCLE)
    private RecurrenceJson billingCycle;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingTerm")
    @ApiModelProperty(value = OrderRequestDocumentation.BILLING_TERM, example = OrderRequestDocumentation.BILLING_TERM_EXAMPLE)
    private BillingTerm billingTerm;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingAnchorDate")
    @ApiModelProperty(value = OrderRequestDocumentation.BILLING_ANCHOR_DATE, example = OrderRequestDocumentation.BILLING_ANCHOR_DATE_EXAMPLE)
    private Long billingAnchorDate;

    @GraphQLField
    @GraphQLName("rampInterval")
    @ApiModelProperty(value = OrderRequestDocumentation.RAMP_INTERVAL, example = OrderRequestDocumentation.RAMP_INTERVAL_EXAMPLE)
    private List<Long> rampInterval;

    @JsonProperty
    @GraphQLField
    @GraphQLName("orderFormTemplateIds")
    @ApiModelProperty(value = OrderRequestDocumentation.ORDER_FORM_TEMPLATE_IDS, example = OrderRequestDocumentation.ORDER_FORM_TEMPLATE_IDS_EXAMPLE)
    private List<String> orderFormTemplateIds;

    // TODO: Rename and remove _sfdc_ from all following fields.

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityId")
    // @ApiModelProperty(value = "**DEPRECATED** \n Use `opportunityId` instead.")
    private String sfdcOpportunityId;

    // Renamed sfdcOpportunityId to opportunityId
    @JsonIgnore
    @GraphQLField
    @GraphQLName("opportunityId")
    @ApiModelProperty(value = OrderRequestDocumentation.OPPORTUNITY_ID, example = OrderRequestDocumentation.OPPORTUNITY_ID_EXAMPLE)
    private String opportunityId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("isPrimaryOrderForSfdcOpportunity")
    // @ApiModelProperty(value = "**DEPRECATED** \n Use `isPrimaryOrderForOpportunity` instead.")
    private Boolean isPrimaryOrderForSfdcOpportunity;

    @JsonIgnore
    @GraphQLField
    @GraphQLName("isPrimaryOrderForOpportunity")
    @ApiModelProperty(
        value = OrderRequestDocumentation.IS_PRIMARY_ORDER_FOR_OPPORTUNITY,
        example = OrderRequestDocumentation.IS_PRIMARY_ORDER_FOR_OPPORTUNITY_EXAMPLE
    )
    private Boolean isPrimaryOrderForOpportunity;

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityName")
    // @ApiModelProperty(value = "**DEPRECATED** \n Use `opportunityName` instead.")
    private String sfdcOpportunityName;

    @JsonIgnore
    @GraphQLField
    @GraphQLName("opportunityName")
    @ApiModelProperty(value = OrderRequestDocumentation.OPPORTUNITY_NAME, example = OrderRequestDocumentation.OPPORTUNITY_NAME_EXAMPLE)
    private String opportunityName;

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityType")
    // @ApiModelProperty(value = "**DEPRECATED** \n Use `opportunityType` instead.")
    private String sfdcOpportunityType;

    @JsonIgnore
    @GraphQLField
    @GraphQLName("opportunityType")
    @ApiModelProperty(value = OrderRequestDocumentation.OPPORTUNITY_TYPE, example = OrderRequestDocumentation.OPPORTUNITY_TYPE_EXAMPLE)
    private String opportunityType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("sfdcOpportunityStage")
    // @ApiModelProperty(value = "**DEPRECATED** \n Use `opportunityStage` instead.")
    private String sfdcOpportunityStage;

    @JsonIgnore
    @GraphQLField
    @GraphQLName("opportunityStage")
    @ApiModelProperty(value = OrderRequestDocumentation.OPPORTUNITY_STAGE, example = OrderRequestDocumentation.OPPORTUNITY_STAGE_EXAMPLE)
    private String opportunityStage;

    @JsonProperty
    @GraphQLField
    @GraphQLName("opportunityCrmType")
    @ApiModelProperty(
        value = OrderRequestDocumentation.OPPORTUNITY_CRM_TYPE,
        example = OrderRequestDocumentation.OPPORTUNITY_CRM_TYPE_EXAMPLE,
        allowableValues = "SALESFORCE, HUBSPOT"
    )
    private OpportunityCrmType opportunityCrmType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("ownerId")
    @ApiModelProperty(value = OrderRequestDocumentation.OWNER_ID, example = OrderRequestDocumentation.OWNER_ID_EXAMPLE)
    private String ownerId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("renewalForSubscriptionId")
    @ApiModelProperty(
        value = OrderRequestDocumentation.RENEWAL_FOR_SUBSCRIPTION_ID,
        example = OrderRequestDocumentation.RENEWAL_FOR_SUBSCRIPTION_ID_EXAMPLE
    )
    private String renewalForSubscriptionId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("documentMasterTemplateId")
    @ApiModelProperty(
        value = OrderRequestDocumentation.DOCUMENT_MASTER_TEMPLATE_ID,
        example = OrderRequestDocumentation.DOCUMENT_MASTER_TEMPLATE_ID_EXAMPLE
    )
    private String documentMasterTemplateId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("documentCustomContent")
    @ApiModelProperty(value = OrderRequestDocumentation.DOCUMENT_CUSTOM_CONTENT)
    private DocumentCustomContent documentCustomContent;

    @JsonProperty
    @GraphQLField
    @GraphQLName("purchaseOrderNumber")
    @ApiModelProperty(value = OrderRequestDocumentation.PURCHASE_ORDER_NUMBER, example = OrderRequestDocumentation.PURCHASE_ORDER_NUMBER_EXAMPLE)
    private String purchaseOrderNumber;

    @JsonProperty
    @GraphQLField
    @GraphQLName("purchaseOrderRequiredForInvoicing")
    @ApiModelProperty(
        value = OrderRequestDocumentation.PURCHASE_ORDER_REQUIRED_FOR_INVOICING,
        example = OrderRequestDocumentation.PURCHASE_ORDER_REQUIRED_FOR_INVOICING_EXAMPLE
    )
    private Boolean purchaseOrderRequiredForInvoicing;

    @JsonProperty
    @GraphQLField
    @GraphQLName("autoRenew")
    @ApiModelProperty(value = OrderRequestDocumentation.AUTO_RENEW, example = OrderRequestDocumentation.AUTO_RENEW_EXAMPLE)
    private boolean autoRenew;

    @JsonProperty
    @GraphQLField
    @GraphQLName("approvalSegmentId")
    @ApiModelProperty(value = OrderRequestDocumentation.APPROVAL_SEGMENT_ID, example = OrderRequestDocumentation.APPROVAL_SEGMENT_ID_EXAMPLE)
    private String approvalSegmentId;

    @JsonProperty
    @GraphQLField
    @ApiModelProperty(value = OrderRequestDocumentation.ATTACHMENT_ID, example = OrderRequestDocumentation.ATTACHMENT_ID_EXAMPLE)
    private String attachmentId;

    @JsonProperty
    @GraphQLField
    private String compositeOrderId;

    @JsonProperty
    @GraphQLField
    private String restructureForSubscriptionId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("expiresOn")
    @ApiModelProperty(value = OrderRequestDocumentation.EXPIRES_ON, example = OrderRequestDocumentation.EXPIRES_ON_EXAMPLE)
    private Long expiresOn;

    @JsonProperty
    @GraphQLField
    @ApiModelProperty(value = OrderRequestDocumentation.ENTITY_ID, example = OrderRequestDocumentation.ENTITY_ID_EXAMPLE)
    private String entityId;

    @Valid
    @GraphQLField
    @GraphQLName("customFields")
    @ApiModelProperty(value = OrderRequestDocumentation.CUSTOM_FIELDS)
    private List<CustomFieldEntry> customFields;

    @JsonProperty
    @GraphQLField
    @GraphQLName("startDateType")
    @ApiModelProperty(
        value = OrderRequestDocumentation.START_DATE_TYPE,
        example = OrderRequestDocumentation.START_DATE_TYPE_EXAMPLE,
        allowableValues = "FIXED, EXECUTION_DATE"
    )
    private OrderStartDateType startDateType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("currency")
    @ApiModelProperty(value = OrderRequestDocumentation.CURRENCY, example = OrderRequestDocumentation.CURRENCY_EXAMPLE)
    private String currency;

    @JsonProperty
    @GraphQLField
    @GraphQLName("customBillingSchedule")
    @ApiModelProperty(value = OrderRequestDocumentation.CUSTOM_BILLING_SCHEDULE)
    private CustomBillingScheduleInput customBillingSchedule;

    @JsonProperty
    @GraphQLField
    @GraphQLName("customPredefinedTemplatesOnOrder")
    @ApiModelProperty(value = OrderRequestDocumentation.CUSTOM_PREDEFINED_TEMPLATES_ON_ORDER)
    private List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder;

    @JsonProperty
    @GraphQLField
    @GraphQLName("subscriptionDurationModel")
    @ApiModelProperty(
        value = OrderRequestDocumentation.SUBSCRIPTION_DURATION_MODEL,
        example = OrderRequestDocumentation.SUBSCRIPTION_DURATION_MODEL_EXAMPLE,
        allowableValues = "TERMED, EVERGREEN"
    )
    private SubscriptionDurationModel subscriptionDurationModel;

    @JsonProperty
    @GraphQLField
    @GraphQLName("opportunityInput")
    @ApiModelProperty(value = OrderRequestDocumentation.OPPORTUNITY_INPUT)
    private OpportunityInput opportunityInput;

    public OrderRequestJson() {
        customFields = new ArrayList<>();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAccountId() {
        return accountId;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public String getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(String paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getShippingContactId() {
        return shippingContactId;
    }

    public void setShippingContactId(String shippingContactId) {
        this.shippingContactId = shippingContactId;
    }

    public String getBillingContactId() {
        return billingContactId;
    }

    public void setBillingContactId(String billingContactId) {
        this.billingContactId = billingContactId;
    }

    public List<TenantDiscountJson> getPredefinedDiscounts() {
        return predefinedDiscounts;
    }

    public void setPredefinedDiscounts(List<TenantDiscountJson> predefinedDiscounts) {
        this.predefinedDiscounts = predefinedDiscounts;
    }

    public List<CreditableAmount> getCreditableAmounts() {
        return creditableAmounts;
    }

    public void setCreditableAmounts(List<CreditableAmount> creditableAmounts) {
        this.creditableAmounts = creditableAmounts;
    }

    public List<OrderLineItemRequestJson> getLineItems() {
        return lineItems;
    }

    public void setLineItems(List<OrderLineItemRequestJson> lineItems) {
        this.lineItems = lineItems;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public Long getExecutedOn() {
        return executedOn;
    }

    public void setExecutedOn(Long executedOn) {
        this.executedOn = executedOn;
    }

    public RecurrenceJson getTermLength() {
        return termLength;
    }

    public void setTermLength(RecurrenceJson termLength) {
        this.termLength = termLength;
    }

    public RecurrenceJson getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(RecurrenceJson billingCycle) {
        this.billingCycle = billingCycle;
    }

    public BillingTerm getBillingTerm() {
        return billingTerm;
    }

    public void setBillingTerm(BillingTerm billingTerm) {
        this.billingTerm = billingTerm;
    }

    public Long getBillingAnchorDate() {
        return billingAnchorDate;
    }

    public void setBillingAnchorDate(Long billingAnchorDate) {
        this.billingAnchorDate = billingAnchorDate;
    }

    public List<Long> getRampInterval() {
        return rampInterval;
    }

    public void setRampInterval(List<Long> rampInterval) {
        this.rampInterval = rampInterval;
    }

    public List<String> getOrderFormTemplateIds() {
        return Objects.requireNonNullElseGet(orderFormTemplateIds, List::of);
    }

    public void setOrderFormTemplateIds(List<String> orderFormTemplateIds) {
        this.orderFormTemplateIds = orderFormTemplateIds;
    }

    public String getSfdcOpportunityId() {
        return sfdcOpportunityId;
    }

    public void setSfdcOpportunityId(String sfdcOpportunityId) {
        this.sfdcOpportunityId = sfdcOpportunityId;
    }

    public String getSfdcOpportunityName() {
        return sfdcOpportunityName;
    }

    public void setSfdcOpportunityName(String sfdcOpportunityName) {
        this.sfdcOpportunityName = sfdcOpportunityName;
    }

    public String getSfdcOpportunityType() {
        return sfdcOpportunityType;
    }

    public void setSfdcOpportunityType(String sfdcOpportunityType) {
        this.sfdcOpportunityType = sfdcOpportunityType;
    }

    public String getSfdcOpportunityStage() {
        return sfdcOpportunityStage;
    }

    public void setSfdcOpportunityStage(String sfdcOpportunityStage) {
        this.sfdcOpportunityStage = sfdcOpportunityStage;
    }

    public boolean getIsPrimaryOrderForSfdcOpportunity() {
        return BooleanUtils.isTrue(isPrimaryOrderForSfdcOpportunity);
    }

    public void setIsPrimaryOrderForSfdcOpportunity(Boolean primaryOrderForSfdcOpportunity) {
        isPrimaryOrderForSfdcOpportunity = primaryOrderForSfdcOpportunity;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getRenewalForSubscriptionId() {
        return renewalForSubscriptionId;
    }

    public void setRenewalForSubscriptionId(String renewalForSubscriptionId) {
        this.renewalForSubscriptionId = renewalForSubscriptionId;
    }

    public String getDocumentMasterTemplateId() {
        return documentMasterTemplateId;
    }

    public void setDocumentMasterTemplateId(String documentMasterTemplateId) {
        this.documentMasterTemplateId = documentMasterTemplateId;
    }

    public DocumentCustomContent getDocumentCustomContent() {
        return documentCustomContent;
    }

    public void setDocumentCustomContent(DocumentCustomContent documentCustomContent) {
        this.documentCustomContent = documentCustomContent;
    }

    public String getPurchaseOrderNumber() {
        return purchaseOrderNumber;
    }

    public void setPurchaseOrderNumber(String purchaseOrderNumber) {
        this.purchaseOrderNumber = purchaseOrderNumber;
    }

    public Boolean getPurchaseOrderRequiredForInvoicing() {
        return purchaseOrderRequiredForInvoicing;
    }

    public void setPurchaseOrderRequiredForInvoicing(Boolean purchaseOrderRequiredForInvoicing) {
        this.purchaseOrderRequiredForInvoicing = purchaseOrderRequiredForInvoicing;
    }

    public boolean isAutoRenew() {
        return autoRenew;
    }

    public void setAutoRenew(boolean autoRenew) {
        this.autoRenew = autoRenew;
    }

    public OpportunityCrmType getOpportunityCrmType() {
        return opportunityCrmType;
    }

    public void setOpportunityCrmType(OpportunityCrmType opportunityCrmType) {
        this.opportunityCrmType = opportunityCrmType;
    }

    public String getApprovalSegmentId() {
        return approvalSegmentId;
    }

    public void setApprovalSegmentId(String approvalSegmentId) {
        this.approvalSegmentId = approvalSegmentId;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getCompositeOrderId() {
        return compositeOrderId;
    }

    public void setCompositeOrderId(String compositeOrderId) {
        this.compositeOrderId = compositeOrderId;
    }

    public String getRestructureForSubscriptionId() {
        return restructureForSubscriptionId;
    }

    public void setRestructureForSubscriptionId(String restructureForSubscriptionId) {
        this.restructureForSubscriptionId = restructureForSubscriptionId;
    }

    public Long getExpiresOn() {
        return expiresOn;
    }

    public void setExpiresOn(Long expiresOn) {
        this.expiresOn = expiresOn;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public List<CustomFieldEntry> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<CustomFieldEntry> customFields) {
        this.customFields = customFields;
    }

    public OrderStartDateType getStartDateType() {
        return startDateType;
    }

    public void setStartDateType(OrderStartDateType startDateType) {
        this.startDateType = startDateType;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public CustomBillingScheduleInput getCustomBillingSchedule() {
        return customBillingSchedule;
    }

    public void setCustomBillingSchedule(CustomBillingScheduleInput customBillingSchedule) {
        this.customBillingSchedule = customBillingSchedule;
    }

    public List<CustomPredefinedTemplateOnOrder> getCustomPredefinedTemplatesOnOrder() {
        return customPredefinedTemplatesOnOrder;
    }

    public void setCustomPredefinedTemplatesOnOrder(List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder) {
        this.customPredefinedTemplatesOnOrder = customPredefinedTemplatesOnOrder;
    }

    public SubscriptionDurationModel getSubscriptionDurationModel() {
        return subscriptionDurationModel;
    }

    public void setSubscriptionDurationModel(SubscriptionDurationModel subscriptionDurationModel) {
        this.subscriptionDurationModel = subscriptionDurationModel;
    }

    public OpportunityInput getOpportunityInput() {
        return opportunityInput;
    }

    public void setOpportunityInput(OpportunityInput opportunityInput) {
        this.opportunityInput = opportunityInput;
    }
}
