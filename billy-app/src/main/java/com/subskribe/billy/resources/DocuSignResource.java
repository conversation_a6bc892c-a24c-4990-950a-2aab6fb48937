package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.docusign.mapper.DocuSignMapper;
import com.subskribe.billy.docusign.model.DocuSignIntegration;
import com.subskribe.billy.docusign.model.DocuSignIntegrationRequestJson;
import com.subskribe.billy.docusign.model.DocuSignIntegrationResponseJson;
import com.subskribe.billy.docusign.service.DocuSignAuthService;
import com.subskribe.billy.docusign.service.DocusignWebhookProcessor;
import com.subskribe.billy.shared.webhook.ImmutableIncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhookTaskDispatcher;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.net.URI;
import java.time.Clock;
import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.mapstruct.factory.Mappers;

@Path("/docusign")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class DocuSignResource {

    private final DocuSignAuthService docuSignAuthenticationService;
    private final DocuSignMapper docuSignMapper;
    private final IncomingWebhookTaskDispatcher incomingWebhookTaskDispatcher;
    private final Clock clock;

    @Context
    private ContainerRequestContext requestContext;

    @Inject
    public DocuSignResource(
        DocuSignAuthService docuSignAuthenticationService,
        IncomingWebhookTaskDispatcher incomingWebhookTaskDispatcher,
        Clock clock
    ) {
        this.docuSignAuthenticationService = docuSignAuthenticationService;
        this.incomingWebhookTaskDispatcher = incomingWebhookTaskDispatcher;
        this.clock = clock;
        docuSignMapper = Mappers.getMapper(DocuSignMapper.class);
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Create Docusign integration",
        notes = "Creates an integration with Docusign. On success a redirect URL is returned.",
        tags = { "Integrations" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response initiateIntegration(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @ApiParam(value = "details of the integration") DocuSignIntegrationRequestJson clientInformationJson
    ) {
        URI redirectUri = docuSignAuthenticationService.initiateIntegration(clientInformationJson);
        return Response.seeOther(redirectUri).build();
    }

    @DELETE
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Delete Docusign integration",
        notes = "Removes integration with Docusign from your tenant",
        response = DocuSignIntegrationResponseJson.class,
        tags = { "Integrations" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response deleteIntegration(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        DocuSignIntegration docuSignIntegration = docuSignAuthenticationService.deleteDocuSignIntegration(
            billyAuthPrincipal.getTenantId().getRequiredId()
        );
        DocuSignIntegrationResponseJson integrationResponseJson = docuSignMapper.convertIntegrationToResponseJson(docuSignIntegration);
        return Response.ok(integrationResponseJson).build();
    }

    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowUnauthenticated(cascade = true)
    @ApiOperation(
        value = "Complete Docusign integration",
        notes = "Complete the docusign integration. This should be called after the integration has been " +
        "created and an authorization code has been received from Docusign.",
        response = DocuSignIntegrationResponseJson.class,
        tags = { "Integrations" }
    )
    public Response completeIntegration(
        @QueryParam("code") @ApiParam(name = "code", value = "authorization code received from Docusign") String authorizationCode,
        @QueryParam("state") @ApiParam(name = "state", value = "id of the integration") String integrationId
    ) {
        DocuSignIntegration docuSignIntegration = docuSignAuthenticationService.handleAuthorizationCodeCallback(authorizationCode, integrationId);
        DocuSignIntegrationResponseJson integrationResponseJson = docuSignMapper.convertIntegrationToResponseJson(docuSignIntegration);
        return Response.ok(integrationResponseJson).build();
    }

    @AllowUnauthenticated(cascade = true)
    @POST
    @Path("/webhook")
    @Consumes({ MediaType.APPLICATION_JSON, "application/yaml", MediaType.TEXT_XML, MediaType.APPLICATION_FORM_URLENCODED })
    @ApiOperation(
        value = "Webhook callback for Docusign",
        notes = "This is the webhook which Docusign calls back to",
        hidden = true,
        tags = { "Signature" }
    )
    public Response processWebhookEvent(String payloadJson) {
        IncomingWebhook incomingWebhook = ImmutableIncomingWebhook.builder()
            .webhookType(DocusignWebhookProcessor.WEBHOOK_TYPE)
            .payload(payloadJson)
            .receivedOn(clock.millis())
            .headers(requestContext.getHeaders())
            .build();
        incomingWebhookTaskDispatcher.dispatch(incomingWebhook);
        return Response.ok().build();
    }
}
