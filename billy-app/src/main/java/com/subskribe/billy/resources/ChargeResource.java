package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ChargeDataBackfillService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.resources.shared.ResponseUtility;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.InputStream;
import javax.inject.Inject;
import javax.validation.constraints.NotEmpty;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.lang3.tuple.Pair;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataParam;
import org.mapstruct.factory.Mappers;

@Path("/charges")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class ChargeResource {

    private final ProductCatalogGetService productCatalogGetService;
    private final ChargeDataBackfillService chargeDataBackfillService;
    private final PlanMapper planMapper;

    @Inject
    public ChargeResource(ProductCatalogGetService productCatalogGetService, ChargeDataBackfillService chargeDataBackfillService) {
        this.productCatalogGetService = productCatalogGetService;
        this.chargeDataBackfillService = chargeDataBackfillService;
        planMapper = Mappers.getMapper(PlanMapper.class);
    }

    @GET
    @Path("/{chargeId}")
    @ApiOperation(
        value = "Get charge details",
        notes = "Gets the details of the specified charge.",
        response = ChargeJson.class,
        tags = { "Product Catalog" }
    )
    @AllowAllRoles
    public Response getCharge(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("chargeId") @ApiParam(value = "id of the charge", required = true) @NotEmpty String chargeId
    ) {
        Charge charge = productCatalogGetService.getChargeByChargeId(chargeId);
        return Response.ok(planMapper.chargeToJson(charge)).build();
    }

    @POST
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @Path("/revrec/csv")
    @ApiOperation(
        value = "Backfill revenue recognition fields",
        notes = "Backfill API to update revenue recognition fields on charges",
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response backfillRevRecFields(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @FormDataParam("file") InputStream importStream,
        @FormDataParam("file") FormDataBodyPart formDataBodyPart,
        @QueryParam("isDryRun") String isDryRunString,
        @Context UriInfo uriInfo
    ) {
        boolean isDryRun = !Boolean.FALSE.toString().equalsIgnoreCase(isDryRunString);
        Pair<InputStream, String> importedStreamDetails = chargeDataBackfillService.updateChargeRevRecFields(
            formDataBodyPart,
            importStream,
            isDryRun
        );
        return ResponseUtility.okResponseFromInputStreamFileNamePair(importedStreamDetails, "text/csv");
    }

    @GET
    @Path("/revrec/csv/download")
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON }) //produces json if there is an error
    @ApiOperation(
        value = "Download revenue recognition fields",
        notes = "Downloads your charge revenue recognition fields as a csv",
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getRevRecFieldsAsCsv(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        Pair<InputStream, String> importedStreamDetails = chargeDataBackfillService.getChargeRevRecFieldsAsCsv();
        return ResponseUtility.okResponseFromInputStreamFileNamePair(importedStreamDetails, "text/csv");
    }
}
