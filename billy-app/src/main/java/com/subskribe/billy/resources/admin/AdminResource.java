package com.subskribe.billy.resources.admin;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.aws.secretsmanager.model.SecretType;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.model.CrmIntegrationConfiguration;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.customization.model.CustomizationDefinition;
import com.subskribe.billy.customization.service.CustomizationService;
import com.subskribe.billy.dlq.model.DLQEntry;
import com.subskribe.billy.dlq.service.DLQService;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.model.EntityJson;
import com.subskribe.billy.entity.model.ImmutableEntity;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.entity.service.EntityService;
import com.subskribe.billy.erp.quickbooks.service.QuickbooksDataService;
import com.subskribe.billy.esign.model.ElectronicSignature;
import com.subskribe.billy.esign.model.ElectronicSignatureProvider;
import com.subskribe.billy.esign.model.EsignTenantSignatory;
import com.subskribe.billy.esign.services.EsignService;
import com.subskribe.billy.event.model.TenantEntityPartitionKey;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateService;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.integration.IntegrationTargetService;
import com.subskribe.billy.invoice.automated.model.AutomatedInvoiceRule;
import com.subskribe.billy.invoice.automated.service.AutomatedInvoiceRuleGetService;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRun;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunItem;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.service.InvoiceServiceInternal;
import com.subskribe.billy.looker.LookerService;
import com.subskribe.billy.looker.client.LookerUser;
import com.subskribe.billy.looker.client.LookerUserCreateRequest;
import com.subskribe.billy.looker.client.LookerUserDeleteRequest;
import com.subskribe.billy.looker.job.LookerTenantBackfillJob;
import com.subskribe.billy.looker.job.LookerTenantConfigExportJob;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.metricsreporting.service.MetricsReportingService;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.quotebuilder.model.Answer;
import com.subskribe.billy.order.quotebuilder.service.QuestionAnswerService;
import com.subskribe.billy.order.quotebuilder.service.QuoteBuilderService;
import com.subskribe.billy.order.quotebuilder.service.SlackQuoteBuilderService;
import com.subskribe.billy.order.services.ChangeOrderService;
import com.subskribe.billy.order.services.MissingOrderChargesService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.order.services.OrderServiceHelper;
import com.subskribe.billy.payment.services.PaymentService;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.resources.json.customfield.CustomFieldDefinitionCreateInput;
import com.subskribe.billy.resources.json.customfield.CustomFieldDefinitionJsonMapper;
import com.subskribe.billy.resources.json.entity.EntityJsonMapper;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.salesforce.model.SalesforcePackage;
import com.subskribe.billy.salesforce.model.subscription.SubskribeSalesforceSubscription;
import com.subskribe.billy.salesforce.service.SalesforceGetService;
import com.subskribe.billy.salesforce.service.SalesforceService;
import com.subskribe.billy.scheduler.job.AccountCrmTypeBackfillJob;
import com.subskribe.billy.scheduler.job.AccountingInvariantsJob;
import com.subskribe.billy.scheduler.job.AddressCountryDataMigrationJob;
import com.subskribe.billy.scheduler.job.AutomatedInvoiceRuleIdBackfillJob;
import com.subskribe.billy.scheduler.job.BackfillArrCategoryForOrdersJob;
import com.subskribe.billy.scheduler.job.BackfillMemoizedInvoiceLinesJob;
import com.subskribe.billy.scheduler.job.BulkInvoiceRunIdBackfillJob;
import com.subskribe.billy.scheduler.job.CognitoEmailTemplateUpdaterJob;
import com.subskribe.billy.scheduler.job.CompareOrderLineToPreviewAmountsJob;
import com.subskribe.billy.scheduler.job.CrmResyncAfterSpecificDateJob;
import com.subskribe.billy.scheduler.job.EmailLastSentOnBackfillJob;
import com.subskribe.billy.scheduler.job.EntityInvariantsJob;
import com.subskribe.billy.scheduler.job.EntityTaggingBackfillJob;
import com.subskribe.billy.scheduler.job.HubSpotEsignatureMigrationJob;
import com.subskribe.billy.scheduler.job.InvoiceConfigIdBackfillJob;
import com.subskribe.billy.scheduler.job.InvoiceDocumentGeneratorJob;
import com.subskribe.billy.scheduler.job.MultiEntityApiKeyBackfillJob;
import com.subskribe.billy.scheduler.job.MultiEntityUserBackfillJob;
import com.subskribe.billy.scheduler.job.MultiEntityWireInstructionBackfillJob;
import com.subskribe.billy.scheduler.job.OpportunityDataMigrationJob;
import com.subskribe.billy.scheduler.job.OrderDocumentGeneratorJob;
import com.subskribe.billy.scheduler.job.PaymentProcessorJob;
import com.subskribe.billy.scheduler.job.PaymentReconciliationJob;
import com.subskribe.billy.scheduler.job.RampGroupIdBackfillJob;
import com.subskribe.billy.scheduler.job.RenewalOrderLinesSubscriptionChargeBackfillJob;
import com.subskribe.billy.scheduler.job.SubscriptionCustomFieldsBackfillJob;
import com.subskribe.billy.scheduler.job.TenantSettingBackfillJob;
import com.subskribe.billy.scheduler.job.TransactionalExchangeBackfillJob;
import com.subskribe.billy.scheduler.job.WebhookSigningKeyBackfillJob;
import com.subskribe.billy.scheduler.model.QuartzJobType;
import com.subskribe.billy.scheduler.service.QuartzSchedulerService;
import com.subskribe.billy.security.audit.Audited;
import com.subskribe.billy.shared.ResourceConstants;
import com.subskribe.billy.shared.document.TemplateType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.subscription.services.SubscriptionChargeChangeTaskBackfiller;
import com.subskribe.billy.subscription.services.SubscriptionStatusChangeTaskBackfiller;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.model.DocumentMasterTemplateStatus;
import com.subskribe.billy.template.model.TemplateScript;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.DocumentTemplateService;
import com.subskribe.billy.template.services.TemplateScriptService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import com.subskribe.billy.tenant.config.admin.BillyConfigurationMerger;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.model.ImmutableTenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobModule;
import com.subskribe.billy.tenantjob.model.TenantJobObjectModel;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.UserCreationStatus;
import com.subskribe.billy.user.service.BulkUserCreationService;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import com.subskribe.zeppa.customizations.core.RuleProcessed;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;
import org.mapstruct.factory.Mappers;
import org.quartz.SchedulerException;
import org.quartz.SimpleScheduleBuilder;

@Path("/admin")
@Produces(MediaType.APPLICATION_JSON)
public class AdminResource {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminResource.class);

    private static final String ORDER_ID_SEPARATOR = ",";

    private final SecretsService secretsService;
    private final FeatureService featureService;
    private final BillyConfiguration billyConfiguration;
    private final QuartzSchedulerService quartzSchedulerService;
    private final DLQService dlqService;
    private final SalesforceService salesforceService;
    private final AccountService accountService;
    private final SalesforceGetService salesforceGetService;
    private final HubSpotIntegrationService hubSpotIntegrationService;
    private final QuickbooksDataService quickbooksDataService;
    private final UserService userService;
    private final TenantService tenantService;
    private final LookerService lookerService;
    private final EsignService esignService;
    private final CustomFieldService customFieldService;
    private final ReportingJobQueueService reportingJobQueueService;
    private final CustomFieldDefinitionJsonMapper customFieldDefinitionJsonMapper;
    private final CustomFieldAPIMapper customFieldAPIMapper;
    private final PlatformFeatureService platformFeatureService;
    private final CustomizationService customizationService;
    private final TenantIdProvider tenantIdProvider;
    private final OrderService orderService;
    private final OrderGetService orderGetService;
    private final ChangeOrderService changeOrderService;
    private final MissingOrderChargesService missingOrderChargesService;
    private final MetricsService metricsService;
    private final EntityService entityService;
    private final EntityJsonMapper entityJsonMapper;
    private final BulkUserCreationService bulkUserCreationService;
    private final MetricsReportingService metricsReportingService;
    private final TenantSettingService tenantSettingService;
    private final DocumentTemplateService documentTemplateService;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final TransactionalExchangeRateService transactionalExchangeRateService;
    private final EntityGetService entityGetService;
    private final EntityContextProvider entityContextProvider;
    private final AutomatedInvoiceRuleGetService automatedInvoiceRuleGetService;
    private final TenantJobDispatcherService tenantJobDispatcherService;
    private final QuestionAnswerService questionAnswerService;
    private final QuoteBuilderService quoteBuilderService;
    private final OrderMapper orderMapper;

    private final BulkInvoiceService bulkInvoiceService;

    private final InvoiceService invoiceService;
    private final CrmService crmService;
    private final PaymentService paymentService;
    private final InvoiceServiceInternal invoiceServiceInternal;

    private final TemplateScriptService templateScriptService;
    private final SlackQuoteBuilderService slackQuoteBuilderService;
    private final SubscriptionStatusChangeTaskBackfiller subscriptionStatusChangeTaskBackfiller;
    private final SubscriptionChargeChangeTaskBackfiller subscriptionChargeChangeTaskBackfiller;

    @Inject
    public AdminResource(
        SecretsService secretsService,
        FeatureService featureService,
        BillyConfiguration billyConfiguration,
        QuartzSchedulerService quartzSchedulerService,
        DLQService dlqService,
        SalesforceService salesforceService,
        AccountService accountService,
        SalesforceGetService salesforceGetService,
        HubSpotIntegrationService hubSpotIntegrationService,
        QuickbooksDataService quickbooksDataService,
        UserService userService,
        TenantService tenantService,
        LookerService lookerService,
        EsignService esignService,
        CustomFieldService customFieldService,
        ReportingJobQueueService reportingJobQueueService,
        PlatformFeatureService platformFeatureService,
        CustomizationService customizationService,
        TenantIdProvider tenantIdProvider,
        OrderService orderService,
        OrderGetService orderGetService,
        ChangeOrderService changeOrderService,
        MissingOrderChargesService missingOrderChargesService,
        MetricsService metricsService,
        EntityService entityService,
        BulkUserCreationService bulkUserCreationService,
        MetricsReportingService metricsReportingService,
        TenantSettingService tenantSettingService,
        DocumentTemplateService documentTemplateService,
        DocumentTemplateGetService documentTemplateGetService,
        TransactionalExchangeRateService transactionalExchangeRateService,
        EntityGetService entityGetService,
        EntityContextProvider entityContextProvider,
        AutomatedInvoiceRuleGetService automatedInvoiceRuleGetService,
        TenantJobDispatcherService tenantJobDispatcherService,
        QuestionAnswerService questionAnswerService,
        QuoteBuilderService quoteBuilderService,
        BulkInvoiceService bulkInvoiceService,
        InvoiceService invoiceService,
        CrmService crmService,
        PaymentService paymentService,
        InvoiceServiceInternal invoiceServiceInternal,
        TemplateScriptService templateScriptService,
        SlackQuoteBuilderService slackQuoteBuilderService,
        SubscriptionStatusChangeTaskBackfiller subscriptionStatusChangeTaskBackfiller,
        SubscriptionChargeChangeTaskBackfiller subscriptionChargeChangeTaskBackfiller
    ) {
        this.featureService = featureService;
        this.secretsService = secretsService;
        this.billyConfiguration = billyConfiguration;
        this.quartzSchedulerService = quartzSchedulerService;
        this.dlqService = dlqService;
        this.salesforceService = salesforceService;
        this.accountService = accountService;
        this.salesforceGetService = salesforceGetService;
        this.hubSpotIntegrationService = hubSpotIntegrationService;
        this.quickbooksDataService = quickbooksDataService;
        this.userService = userService;
        this.tenantService = tenantService;
        this.lookerService = lookerService;
        this.esignService = esignService;
        this.customFieldService = customFieldService;
        this.reportingJobQueueService = reportingJobQueueService;
        this.platformFeatureService = platformFeatureService;
        this.customizationService = customizationService;
        this.tenantIdProvider = tenantIdProvider;
        this.orderService = orderService;
        this.orderGetService = orderGetService;
        this.changeOrderService = changeOrderService;
        this.missingOrderChargesService = missingOrderChargesService;
        this.metricsService = metricsService;
        this.entityService = entityService;
        this.bulkUserCreationService = bulkUserCreationService;
        this.metricsReportingService = metricsReportingService;
        this.tenantSettingService = tenantSettingService;
        this.documentTemplateService = documentTemplateService;
        this.documentTemplateGetService = documentTemplateGetService;
        this.transactionalExchangeRateService = transactionalExchangeRateService;
        this.entityGetService = entityGetService;
        this.entityContextProvider = entityContextProvider;
        this.automatedInvoiceRuleGetService = automatedInvoiceRuleGetService;
        this.tenantJobDispatcherService = tenantJobDispatcherService;
        this.questionAnswerService = questionAnswerService;
        this.quoteBuilderService = quoteBuilderService;
        this.bulkInvoiceService = bulkInvoiceService;
        this.invoiceService = invoiceService;
        this.crmService = crmService;
        this.paymentService = paymentService;
        this.invoiceServiceInternal = invoiceServiceInternal;
        this.templateScriptService = templateScriptService;
        this.slackQuoteBuilderService = slackQuoteBuilderService;
        this.subscriptionStatusChangeTaskBackfiller = subscriptionStatusChangeTaskBackfiller;
        this.subscriptionChargeChangeTaskBackfiller = subscriptionChargeChangeTaskBackfiller;
        customFieldDefinitionJsonMapper = Mappers.getMapper(CustomFieldDefinitionJsonMapper.class);
        customFieldAPIMapper = Mappers.getMapper(CustomFieldAPIMapper.class);
        entityJsonMapper = Mappers.getMapper(EntityJsonMapper.class);
        orderMapper = Mappers.getMapper(OrderMapper.class);
    }

    @POST
    @Path("/stripe/apikey")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeStripeApiKey(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("stripeApiKey") String stripeApiKey
    ) {
        secretsService.storeOrUpdateStripeApiKey(stripeApiKey);
        return Response.ok().build();
    }

    @POST
    @Path("/stripe/endpoint-secret")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeStripeEndpointSecret(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("stripeEndpointSecret") String stripeEndpointSecret
    ) {
        secretsService.storeOrUpdateStripeEndpointSecret(stripeEndpointSecret);
        return Response.ok().build();
    }

    @POST
    @Path("/stripe/client-id")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeStripeClientId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("stripeClientId") String stripeClientId
    ) {
        secretsService.storeOrUpdateStripeClientId(stripeClientId);
        return Response.ok().build();
    }

    @POST
    @Path("/secret")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeStaticSecret(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("type") String secretTypeString,
        @NotEmpty @QueryParam("value") String secretValue
    ) {
        Validator.validateNonNullArguments(secretTypeString, secretValue);
        SecretType secretType = Validator.enumFromString(secretTypeString, SecretType.class, "secret type");
        secretsService.storeOrUpdateStaticSecret(secretType, secretValue);
        return Response.ok().build();
    }

    @GET
    @Path("/config")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response getTenantConfig(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @QueryParam("tenantId") String tenantId) {
        if (StringUtils.isNotBlank(tenantId)) {
            tenantService.throwIfTenantIdIsNotValid(tenantId);
        }
        BillyConfiguration mergedConfiguration = StringUtils.isBlank(tenantId)
            ? BillyConfigurationMerger.merge(billyConfiguration, Optional.empty())
            : TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        String serializedMergedConfiguration = UncheckedObjectMapper.defaultMapper().writeValueAsString(mergedConfiguration);
        return Response.ok(serializedMergedConfiguration).build();
    }

    @GET
    @Path("/dynamicflags")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response getDynamicFeatureFlags(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) {
        Map<String, Boolean> featureFlags = Stream.of(Feature.values()).collect(Collectors.toMap(Feature::getKey, featureService::isEnabled));
        return Response.ok(featureFlags).build();
    }

    @POST
    @Path("/opportunity/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runOpportunityDataMigrationJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.OPPORTUNITY_DATA_MIGRATION,
            OpportunityDataMigrationJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/tenant/setting/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runTenantSettingBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @QueryParam("isDryRun") String isDryRun)
        throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.ofNullable(isDryRun),
            QuartzJobType.TENANT_SETTING_BACKFILL,
            TenantSettingBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/account/crmType/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runAccountCrmTypeBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.ACCOUNT_CRM_TYPE_BACKFILL_JOB,
            AccountCrmTypeBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/cognito/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runCognitoEmailTemplateUpdaterJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.COGNITO_EMAIL_TEMPLATE_UPDATER,
            CognitoEmailTemplateUpdaterJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/invoice/documentGeneration/job")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runInvoiceDocumentGenerationJob(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("invoiceNumber") String invoiceNumber
    ) throws Exception {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.of(tenantId),
            UUID.randomUUID().toString(),
            Optional.of(invoiceNumber),
            QuartzJobType.INVOICE_DOCUMENT_GENERATION,
            InvoiceDocumentGeneratorJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/order/documentGeneration/job")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runOrderDocumentGenerationJob(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("orderId") String orderId
    ) throws Exception {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.of(tenantId),
            UUID.randomUUID().toString(),
            Optional.of(orderId),
            QuartzJobType.ORDER_DOCUMENT_GENERATION,
            OrderDocumentGeneratorJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @GET
    @Path("/dlq")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ENGINEER, Role.BILLY_ADMIN })
    public Response getAllDlqEntries(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) {
        List<DLQEntry> dlqEntries = dlqService.getAllEntries();
        return Response.ok(dlqEntries).build();
    }

    @PUT
    @Path("/dlq/retry")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ENGINEER, Role.BILLY_ADMIN })
    public Response retryDlqEntries(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, List<UUID> entryIds) {
        if (CollectionUtils.isEmpty(entryIds)) {
            return Response.ok().build();
        }

        List<DLQEntry> dlqEntries = dlqService.retryEntries(entryIds);
        return Response.ok(dlqEntries).build();
    }

    @PUT
    @Path("/dlq/retryAll")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ENGINEER, Role.BILLY_ADMIN })
    public Response retryAllDlqEntries(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) {
        List<DLQEntry> dlqEntries = dlqService.retryAllEntries();
        return Response.ok(dlqEntries).build();
    }

    @DELETE
    @Path("/dlq/deleteAllFailed")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ENGINEER, Role.BILLY_ADMIN })
    public Response deleteAllFailedDlqEntries(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) {
        dlqService.deleteAllFailedEntries();
        return Response.ok().build();
    }

    @DELETE
    @Path("/dlq")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ENGINEER, Role.BILLY_ADMIN })
    public Response deleteFailedDlqEntries(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, List<UUID> entryIds) {
        if (CollectionUtils.isEmpty(entryIds)) {
            return Response.ok().build();
        }

        dlqService.deleteFailedEntries(entryIds);
        return Response.ok().build();
    }

    @PUT
    @Path("/accounts/updateCrmId")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response updateAccountCrmId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("tenantId") @NotEmpty String tenantId,
        @QueryParam("accountId") @NotEmpty String accountId,
        @QueryParam("crmId") String crmId
    ) {
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () -> accountService.updateAccountCrmId(accountId, crmId));
        return Response.ok().build();
    }

    @PUT
    @Path("/user/billy-engineer/{email}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response setUserRoleToBillyEngineer(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @PathParam("email") String email,
        @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        userService.setUserRoleToBillyEngineer(email, tenantId);
        return Response.ok().build();
    }

    @POST
    @Path("/address/country/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runAddressCountryDataMigrationJob(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("isDryRun") String isDryRun
    ) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.ofNullable(isDryRun),
            QuartzJobType.ADDRESS_COUNTRY_DATA_MIGRATION_JOB,
            AddressCountryDataMigrationJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @PUT
    @Path("/tenant/is-test")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response updateTenantTestFlag(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("tenantId") String tenantId,
        @QueryParam("isTest") boolean isTest
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        tenantService.updateIsTestTenant(tenantId, isTest);
        return Response.ok().build();
    }

    @POST
    @Path("/hubspot/app-id")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeHubSpotAppId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("hubSpotAppId") String hubSpotAppId
    ) {
        secretsService.storeOrUpdateHubSpotAppId(hubSpotAppId);
        return Response.ok().build();
    }

    @POST
    @Path("/hubspot/client-id")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeHubSpotClientId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("hubSpotClientId") String hubSpotClientId
    ) {
        secretsService.storeOrUpdateHubSpotClientId(hubSpotClientId);
        return Response.ok().build();
    }

    @POST
    @Path("/hubspot/client-secret")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeHubSpotClientSecret(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("hubSpotClientSecret") String hubSpotClientSecret
    ) {
        secretsService.storeOrUpdateHubSpotClientSecret(hubSpotClientSecret);
        return Response.ok().build();
    }

    @POST
    @Path("/hubspot/scopes")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeHubSpotAppScopes(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("scopes") String scopes
    ) {
        secretsService.storeOrUpdateHubSpotAppScopes(scopes);
        return Response.ok().build();
    }

    @POST
    @Path("/quodd/api-key")
    @Consumes(MediaType.TEXT_PLAIN)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeQuoddApiKey(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @NotEmpty String quoddApiKey) {
        secretsService.storeOrUpdateStaticSecret(SecretType.QUODD_API_KEY, quoddApiKey);
        return Response.ok().build();
    }

    @POST
    @Path("/looker/client-id")
    @Consumes(MediaType.TEXT_PLAIN)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeLookerClientId(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @NotEmpty String lookerClientId) {
        secretsService.storeOrUpdateStaticSecret(SecretType.LOOKER_CLIENT_ID, lookerClientId);
        return Response.ok().build();
    }

    @POST
    @Path("/looker/client-secret")
    @Consumes(MediaType.TEXT_PLAIN)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeLookerClientSecret(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @NotEmpty String lookerClientSecret) {
        secretsService.storeOrUpdateStaticSecret(SecretType.LOOKER_CLIENT_SECRET, lookerClientSecret);
        return Response.ok().build();
    }

    @POST
    @Path("/looker/tenant-backfill")
    @Consumes(MediaType.TEXT_PLAIN)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response backfillLookerTenants(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.LOOKER_TENANT_BACKFILL,
            LookerTenantBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/looker/export-config")
    @Consumes(MediaType.TEXT_PLAIN)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runLookerExport(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.LOOKER_TENANT_CONFIG_EXPORT,
            LookerTenantConfigExportJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Audited
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/looker/user")
    @AllowRoles({ Role.BILLY_ENGINEER, Role.BILLY_ADMIN })
    public Response addLookerUser(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotNull @Valid LookerUserCreateRequest userCreateRequest
    ) {
        LookerUser lookerUser = lookerService.createLookerUser(userCreateRequest);
        return Response.ok(lookerUser).build();
    }

    @DELETE
    @Audited
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/looker/user")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response deleteLookerUser(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotNull @Valid LookerUserDeleteRequest userDeleteRequest
    ) {
        lookerService.deleteLookerUser(userDeleteRequest.getEmail());
        return Response.ok().build();
    }

    @PUT
    @Audited
    @Path("looker/arrCategory/backfillSubscription")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response backfillArrCategory(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("subscriptionId") String subscriptionId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            reportingJobQueueService.addArrJobsForSubscription(subscriptionId)
        );
        return Response.ok().build();
    }

    @POST
    @Audited
    @Path("looker/arrCategory/validate")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response validateArrWaterfallData(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            reportingJobQueueService.addArrJobsToValidateAccountData(tenantId)
        );
        return Response.ok().build();
    }

    @PUT
    @Audited
    @Path("looker/arrCategory/backfillAccount")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response backfillArrCategoryForAccount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("accountId") String accountId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            reportingJobQueueService.addArrJobsForAccount(accountId)
        );
        return Response.ok().build();
    }

    @PUT
    @Audited
    @Path("looker/arrCategory/backfillJob")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response backfillArrCategoryJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @QueryParam("tenantId") String tenantId)
        throws SchedulerException {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.ofNullable(tenantId),
            QuartzJobType.BACKFILL_ARR_REPORTING_CATEGORY,
            BackfillArrCategoryForOrdersJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @DELETE
    @Path("/looker/arrCategory")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response deleteTenantArrData(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            metricsReportingService.deleteTenantLevelArrData(tenantId)
        );
        return Response.ok().build();
    }

    @DELETE
    @Path("/looker/arrCategory/subscription")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response deleteSubscriptionArrData(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("subscriptionId") String subscriptionId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            metricsReportingService.deleteSubscriptionArrData(tenantId, subscriptionId)
        );
        return Response.ok().build();
    }

    @DELETE
    @Path("/looker/arrCategory/account")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response deleteAccountArrData(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("accountId") String accountId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            metricsReportingService.deleteAccountArrData(tenantId, accountId)
        );
        return Response.ok().build();
    }

    @PUT
    @Path("/order/backfillRenewalOrderLines")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runRenewalOrderLinesSubscriptionChargeBackfillJob(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("isDryRun") String isDryRun
    ) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.ofNullable(isDryRun),
            QuartzJobType.BACKFILL_RENEWAL_ORDER_LINES_BASE_SUBSCRIPTION_CHARGE,
            RenewalOrderLinesSubscriptionChargeBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/payment/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runPaymentProcessingJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.PAYMENT_PROCESSOR,
            PaymentProcessorJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/payment/reconciler/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runPaymentReconciliationJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.PAYMENT_RECONCILER,
            PaymentReconciliationJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/accounting/invariants/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runAccountingInvariantsJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.ACCOUNTING_INVARIANTS_JOB,
            AccountingInvariantsJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @DELETE
    @Path("/esign/signatory")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response deleteTenantSignatory(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("userId") String userId
    ) {
        esignService.deleteTenantSignatory(userId, tenantId);
        return Response.ok().build();
    }

    @POST
    @Path("/esign/signatory")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response insertTenantSignatory(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("userId") String userId
    ) {
        EsignTenantSignatory signatory = new EsignTenantSignatory(userId);
        esignService.insertTenantSignatory(signatory, tenantId);
        return Response.ok().build();
    }

    @POST
    @Path("/esign/pandadoc/apikey")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storePandaDocApiKey(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("pandaDocApiKey") String pandaDocApiKey
    ) {
        secretsService.storeOrUpdatePandaDocApiKey(pandaDocApiKey);
        return Response.ok().build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/customFieldDefinition")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response createCustomFieldDefinition(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(value = "custom field definition values", required = true) CustomFieldDefinitionCreateInput json,
        @Context UriInfo uriInfo
    ) {
        CustomFieldDefinition customFieldDefinition = customFieldDefinitionJsonMapper.toCustomFieldDefinition(json);
        var savedCustomFieldDefinition = customFieldService.createCustomFieldDefinitionBySystem(customFieldDefinition);
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(savedCustomFieldDefinition.getCustomFieldId());
        return Response.created(builder.build()).build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/customField/{parentObjectType}/{parentObjectId}")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response setCustomFields(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("parentObjectType") @ApiParam(value = "object type custom fields are attached to", required = true) String parentObjectType,
        @PathParam("parentObjectId") @ApiParam(value = "Id of the parent object", required = true) String parentObjectId,
        @NotNull @ApiParam(value = "custom field values", required = true) Map<String, CustomFieldValue> customFields
    ) {
        var type = CustomFieldParentType.valueOf(parentObjectType);

        CustomField customField = customFieldAPIMapper.toCustomField(customFields);

        CustomField updatedCustomField = updateCustomField(type, parentObjectId, customField);

        return Response.ok(customFieldAPIMapper.toCustomFieldsMap(updatedCustomField)).build();
    }

    private CustomField updateCustomField(CustomFieldParentType type, String parentObjectId, CustomField customField) {
        return customFieldService.setCustomFieldsBySystem(type, parentObjectId, customField);
    }

    @GET
    @Path("/salesforce/package")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response getSalesforcePackage(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        SalesforcePackage salesforcePackage = salesforceService.getSalesforcePackageByTenantId(tenantId).orElse(null);
        return Response.ok(salesforcePackage).build();
    }

    @GET
    @Path("/salesforce/subscription")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response getSalesforceSubscriptionBySubskribeSubscriptionId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("subscriptionId") String subskribeSubscriptionId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        Optional<SubskribeSalesforceSubscription> optionalSubscription = salesforceGetService.getSubscriptionFromSalesforceForTenant(
            subskribeSubscriptionId,
            tenantId
        );
        SubskribeSalesforceSubscription subscription = optionalSubscription.orElseThrow(() ->
            new IllegalArgumentException("No Salesforce subscription associated with this subscription ID")
        );
        return Response.ok().entity(subscription).build();
    }

    @POST
    @Audited
    @Path("/salesforce/transactionArrMetrics/backfill")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response salesforceBackfillArrTransactionMetricsOnTenant(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            crmService.backfillTransactionalMetricsForTenant(tenantId)
        );
        return Response.ok().build();
    }

    @POST
    @Audited
    @Path("/crm/resyncOrders/{epochDate}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response resyncOrdersToCrmUpdatedFromDate(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @PathParam("epochDate") Long epochDate,
        @QueryParam("tenantId") String tenantId
    ) throws SchedulerException {
        if (StringUtils.isNotEmpty(tenantId)) {
            tenantService.throwIfTenantIdIsNotValid(tenantId);
        }

        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.ofNullable(tenantId),
            UUID.randomUUID().toString(),
            Optional.of(epochDate.toString()),
            QuartzJobType.CRM_ORDERS_RESYNC_FROM_SPECIFIC_TIME,
            CrmResyncAfterSpecificDateJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @PUT
    @Path("/quickbooks/deleteJournalEntries")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Delete journal entries from quickbooks", notes = "Delete journal entries from quickbooks.", tags = { "Integrations" })
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response deleteJournalEntriesFromQuickbooks(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @ApiParam(name = "ids", value = "quickbooks journal entry ids", required = true) List<String> externalJournalEntryIds
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        quickbooksDataService.deleteJournalEntriesByIds(tenantId, externalJournalEntryIds);
        return Response.ok().build();
    }

    @POST
    @Path("/quickbooks/cleanupJournalEntries")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Cleanup journal entries from quickbooks", notes = "Cleanup journal entries from quickbooks.", tags = { "Integrations" })
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response cleanupJournalEntriesFromQuickbooks(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("integrationId") String integrationId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        quickbooksDataService.cleanupJournalEntries(tenantId, integrationId);
        return Response.ok().build();
    }

    @POST
    @Path("/import/flatfile/apikey")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeFlatfileApiKey(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("flatfileApiKey") String flatfileApiKey
    ) {
        secretsService.storeOrUpdateFlatfileApiKey(flatfileApiKey);
        return Response.ok().build();
    }

    @POST
    @Path("/notification/signing/backfill")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runWebhookSigningKeyBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.WEBHOOK_SIGNING_KEY_BACKFILL,
            WebhookSigningKeyBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/entity/tagging/backfill")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runEntityTaggingBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.ENTITY_TAGGING_BACKFILL_JOB,
            EntityTaggingBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/entity/invariants/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runEntityInvariantsJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.ENTITY_INVARIANTS_JOB,
            EntityInvariantsJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/entities")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response addEntityForTenant(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @Valid EntityJson entityJson,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @QueryParam("isDefault") Boolean isDefault
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        Entity inputEntity = entityJsonMapper.fromJson(entityJson);
        Entity entity = BooleanUtils.isTrue(isDefault) ? ImmutableEntity.copyOf(inputEntity).withIsDefault(true) : inputEntity;
        Entity createdEntity = TenantContextInjector.spawnThreadAndCallInTenantAllEntityContext(
            tenantId,
            entityGetService,
            entityContextProvider,
            () -> entityService.addEntity(entity)
        );
        return Response.ok(createdEntity).build();
    }

    @POST
    @Path("/invoice/configid/backfill")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runInvoiceConfigIdBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.INVOICE_CONFIG_ID_BACKFILL_JOB,
            InvoiceConfigIdBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/slack/token")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeSlackToken(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("token") String token
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        String keyPrefix = billyConfiguration.getApprovalFlowConfiguration().getSlackTokenSecretPrefix();
        secretsService.storeOrUpdateSlackToken(tenantId, keyPrefix, token);
        slackQuoteBuilderService.addSlackWorkspaceInfo(tenantId, token);
        return Response.ok().build();
    }

    @POST
    @Path("/slack/signingSecret")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeSlackSigningSecret(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("signingSecret") String signingSecret
    ) {
        String envName = billyConfiguration.getEnvName();
        secretsService.storeOrUpdateSlackSigningSecret(signingSecret, envName);
        return Response.ok().build();
    }

    @PUT
    @Path("/hubspot/configuration")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response updateHubSpotConfiguration(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("tenantId") String tenantId,
        @NotNull @Valid @ApiParam(
            value = "hubspot integration configuration",
            required = true
        ) CrmIntegrationConfiguration crmIntegrationConfiguration
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        hubSpotIntegrationService.updateConfiguration(crmIntegrationConfiguration, Optional.of(tenantId));
        return Response.ok().build();
    }

    @POST
    @Path("/quickbooks/client-id")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeQuickbooksClientId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("quickbooksClientId") String quickbooksClientId
    ) {
        secretsService.storeOrUpdatePublicAppClientId(IntegrationTargetService.QUICKBOOKS, quickbooksClientId);
        return Response.ok().build();
    }

    @POST
    @Path("/quickbooks/client-secret")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeQuickbooksClientSecret(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("quickbooksClientSecret") String quickbooksClientSecret
    ) {
        secretsService.storeOrUpdatePublicAppClientSecret(IntegrationTargetService.QUICKBOOKS, quickbooksClientSecret);
        return Response.ok().build();
    }

    @POST
    @Path("/merge/apikey")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response storeMergeApiKey(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("mergeApiKey") String apiKey
    ) {
        if (!featureService.isEnabled(Feature.MERGE_POC)) {
            throw new UnsupportedOperationException("merge POC is not enabled");
        }
        secretsService.storeOrUpdateMergeApiKey(apiKey);
        return Response.ok().build();
    }

    @POST
    @Path("/platform-feature")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response enablePlatformFeature(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("feature") String feature,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        PlatformFeature platformFeature = Validator.enumFromString(feature, PlatformFeature.class, "feature");
        platformFeatureService.enablePlatformFeatureForTenant(platformFeature, tenantId);
        return Response.ok().build();
    }

    @PUT
    @Path("/platform-feature/ready")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response movePlatformFeatureToReady(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("feature") String feature,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("entityId") String entityId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        PlatformFeature platformFeature = Validator.enumFromString(feature, PlatformFeature.class, "feature");
        EntityContext entityContext = EntityContext.buildEntityContextByEntityIds(Set.of(entityId));
        TenantContextInjector.spawnThreadAndRunInEntityContext(tenantId, entityContext, entityContextProvider, () ->
            platformFeatureService.movePlatformFeatureToReady(platformFeature)
        );
        return Response.ok().build();
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Path("/customization/selectionCustomization/test")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response testSelectionCustomizationZeppaScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("accountId") String accountId,
        @QueryParam("crmOpportunityId") String crmOpportunityId,
        @QueryParam("userId") String userId,
        @NotNull @ApiParam String zeppaScript
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        List<RuleProcessed> rulesProcessed = TenantContextInjector.spawnThreadAndCallInTenantContext(tenantId, tenantIdProvider, () ->
            customizationService.fireSelectionCustomization(accountId, userId, crmOpportunityId, zeppaScript)
        );
        return Response.ok(rulesProcessed).build();
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Path("/customization/orderCreationCustomization/test")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response testOrderCreationCustomizationZeppaScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("orderId") String orderId,
        @QueryParam("userId") String userId,
        @NotNull @ApiParam String zeppaScript
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        List<RuleProcessed> rulesProcessed = TenantContextInjector.spawnThreadAndCallInTenantContext(tenantId, tenantIdProvider, () ->
            customizationService.fireOrderCreationCustomization(orderId, userId, zeppaScript)
        );
        return Response.ok(rulesProcessed).build();
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Path("/customization/selectionCustomization")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response addSelectionCustomization(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("accountId") String accountId,
        @QueryParam("userId") String userId,
        @QueryParam("crmOpportunityId") String crmOpportunityId,
        @NotNull @QueryParam("expectedVersion") Integer expectedVersion,
        @NotNull @ApiParam String zeppaScript
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        CustomizationDefinition customizationDefinition = TenantContextInjector.spawnThreadAndCallInTenantContext(tenantId, tenantIdProvider, () ->
            customizationService.upsertSelectionCustomization(accountId, userId, crmOpportunityId, zeppaScript, expectedVersion)
        );
        return Response.ok(customizationDefinition).build();
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Path("/customization/orderCreationCustomization")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response addOrderCreationCustomization(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("orderId") String orderId,
        @QueryParam("userId") String userId,
        @NotNull @QueryParam("expectedVersion") Integer expectedVersion,
        @NotNull @ApiParam String zeppaScript
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        CustomizationDefinition customizationDefinition = TenantContextInjector.spawnThreadAndCallInTenantContext(tenantId, tenantIdProvider, () ->
            customizationService.upsertOrderCreationCustomization(orderId, userId, zeppaScript, expectedVersion)
        );
        return Response.ok(customizationDefinition).build();
    }

    @GET
    @Path("/guidedSelling/usecase/{id}/qscript")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces({ MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN })
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response getUsecaseScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotNull @Valid @PathParam("id") String usecaseId,
        @Context UriInfo uriInfo
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        Validator.validateStringNotBlank(usecaseId, "usecaseId cannot be empty");
        String script = TenantContextInjector.spawnThreadAndCallInTenantContext(tenantId, tenantIdProvider, () ->
            questionAnswerService
                .getQScript(usecaseId)
                .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.GUIDED_SELLING_QSCRIPT, usecaseId))
        );
        return Response.ok(script).build();
    }

    @PUT
    @Path("/guidedSelling/usecase/{id}/qscript")
    @Consumes(MediaType.TEXT_PLAIN)
    @Produces({ MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN })
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response putQScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotNull @ApiParam String qscript,
        @NotNull @Valid @PathParam("id") String usecaseId,
        @Context UriInfo uriInfo
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        if (StringUtils.isBlank(usecaseId)) {
            throw new InvalidInputException("Usecase id cannot be blank");
        }

        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () -> {
            Optional<String> qscriptOptional = questionAnswerService.getQScript(usecaseId);

            if (qscriptOptional.isPresent()) {
                questionAnswerService.updateQScriptForGuidedSelling(usecaseId, qscript);
                return;
            }
            questionAnswerService.addQScriptToGuidedSelling(usecaseId, qscript);
        });
        return Response.ok(qscript).build();
    }

    @PUT
    @Path("/guidedSelling/usecase/{usecase}/testQscript")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiOperation(
        value = "Test quote building Zeppa Q script",
        notes = "Given the use case, list of answers and Zeppa Q script try creating the order in dryrun mode",
        response = OrderJson.class,
        tags = { ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response testQScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotNull @Valid @PathParam("usecase") String usecase,
        @Valid @NotNull @FormDataParam("answers") List<Answer> answers,
        @Valid @NotNull @FormDataParam("qscript") String zeppaQScript,
        @Valid @NotNull @QueryParam("accountId") String accountId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        Order order = TenantContextInjector.spawnThreadAndCallInTenantContext(tenantId, tenantIdProvider, () ->
            quoteBuilderService.testQScriptOnUsecase(accountId, usecase, answers, zeppaQScript)
        );
        return Response.ok(orderMapper.orderToJson(order)).build();
    }

    @GET
    @Path("/customization/selectionCustomization")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response getSelectionCustomization(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        Optional<CustomizationDefinition> definitionOptional = TenantContextInjector.spawnThreadAndCallInTenantContext(
            tenantId,
            tenantIdProvider,
            customizationService::getCurrentSelectionCustomization
        );
        return definitionOptional
            .map(def -> Response.ok(def).build())
            .orElseGet(() ->
                Response.status(HttpStatus.NOT_FOUND_404, String.format("Selection customization not defined for tenant %s", tenantId)).build()
            );
    }

    @GET
    @Path("/customization/orderCreationCustomization")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    @Produces({ MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN })
    public Response getOrderCreationCustomization(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @QueryParam("show") String show
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        Optional<CustomizationDefinition> definitionOptional = TenantContextInjector.spawnThreadAndCallInTenantContext(
            tenantId,
            tenantIdProvider,
            customizationService::getCurrentOrderCreationCustomization
        );
        return definitionOptional
            .map(def -> {
                // if we want to show only source then just project source with version
                if (StringUtils.isNotBlank(show) && "source".equalsIgnoreCase(show)) {
                    return Response.ok(def.getZeppaScript()).header("script-version", def.getVersion()).build();
                }
                return Response.ok(def).build();
            })
            .orElseGet(() ->
                Response.status(HttpStatus.NOT_FOUND_404, String.format("Order creation customization not defined for tenant %s", tenantId)).build()
            );
    }

    @POST
    @Path("/hubspot/esign/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runHubSpotEsignatureMigrationJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.HUBSPOT_ESIGNATURE_MIGRATION_JOB,
            HubSpotEsignatureMigrationJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/import/renewalOrderLineBackFill/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runImportRenewalOrderLineBackFillJob(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("orderIds") String rawOrderIds
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        List<String> orderIds = Stream.of(rawOrderIds.split(ORDER_ID_SEPARATOR)).map(String::strip).filter(StringUtils::isNotEmpty).toList();
        TenantContextInjector.spawnThreadAndCallInTenantAllEntityContext(tenantId, entityGetService, entityContextProvider, () -> {
            orderIds.forEach(orderId -> {
                Order order = orderGetService.getOrderByOrderId(orderId);
                if (order.getStatus() == OrderStatus.DRAFT) {
                    LOGGER.info("backfilling missing items for order: {}", order.getId());
                    BigDecimal totalAmountBefore = order
                        .getLineItems()
                        .stream()
                        .map(OrderLineItem::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    missingOrderChargesService.findAndAddMissingOrderCharges(order);
                    Map<String, Metrics> orderLineMetrics = metricsService.getMetricsForAllOrderLines(order);
                    List<OrderLineItem> consolidatedLineItems = OrderServiceHelper.consolidateOrderLineItemsByBaseExternalSubscriptionChargeId(
                        order.getLineItems(),
                        orderLineMetrics
                    );
                    order.setLineItems(consolidatedLineItems);
                    orderService.findAndAddMissingPlanTemplates(order);

                    BigDecimal totalAmountAfter = order
                        .getLineItems()
                        .stream()
                        .map(OrderLineItem::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                    if (totalAmountBefore.compareTo(totalAmountAfter) != 0) {
                        throw new InvariantCheckFailedException("order line items amount and after do not match");
                    }

                    if (order.getOrderType() == OrderType.RENEWAL) {
                        orderService.upsertOrder(order, false, false);
                    }

                    if (order.getOrderType() == OrderType.AMENDMENT) {
                        changeOrderService.upsertChangeOrder(order, false, false);
                    }
                }
            });
            return true;
        });
        return Response.ok().build();
    }

    @POST
    @Path("/entity/wire-instruction/backfill")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runMultiEntityWireInstructionBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.MULTI_ENTITY_WIRE_INSTRUCTION_BACKFILL_JOB,
            MultiEntityWireInstructionBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/entity/api-key/backfill")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runMultiEntityApiKeyBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.MULTI_ENTITY_API_KEY_BACKFILL_JOB,
            MultiEntityApiKeyBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/order/ramp-group-id/backfill")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runRampGroupIdBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.RAMP_GROUP_ID_BACKFILL_JOB,
            RampGroupIdBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule(),
            false
        );
        return Response.ok().build();
    }

    @POST
    @Path("/customFields/backfillSubscriptionItems/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response subscriptionItemCustomFieldsBackfill(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.SUBSCRIPTION_ITEM_CUSTOM_FIELDS_BACKFILL_JOB,
            SubscriptionCustomFieldsBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Consumes("text/csv")
    @Path("/users/upload")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response uploadCSV(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        InputStream csv,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        Map<String, UserCreationStatus> result = TenantContextInjector.spawnThreadAndCallInTenantContext(tenantId, tenantIdProvider, () ->
            bulkUserCreationService.fromCSV(csv, tenantId)
        );
        return Response.ok(result).build();
    }

    @POST
    @Path("/entity/user-tagging/backfill")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runEntityUserBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.MULTI_ENTITY_USER_BACKFILL_JOB,
            MultiEntityUserBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/tenant/setting/update-order-start-date")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response enableUpdateOrderStartDate(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("isEnabled") Boolean isEnabled,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        tenantSettingService.updateIsUpdateOrderStartDateEnabledForTenant(isEnabled, tenantId);
        return Response.ok().build();
    }

    @GET
    @Path("/document/master/templates")
    @ApiOperation(value = "Get document templates", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response getDocumentMasterTemplates(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @QueryParam("templateType") DocumentTemplateType templateType
    ) {
        Validator.validateUUID(tenantId, "tenantId");
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        var documentMasterTemplates = TenantContextInjector.spawnThreadAndCallInTenantAllEntityContext(
            tenantId,
            entityGetService,
            entityContextProvider,
            () -> documentTemplateGetService.getDocumentMasterTemplatesForTenant(Optional.of(tenantId), templateType)
        );
        return Response.ok(documentMasterTemplates).build();
    }

    @GET
    @Path("/document/master/templates/{id}")
    @ApiOperation(value = "Get document template", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response getDocumentMasterTemplate(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("id") String id,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        var documentMasterTemplate = TenantContextInjector.spawnThreadAndCallInTenantAllEntityContext(
            tenantId,
            entityGetService,
            entityContextProvider,
            () ->
                documentTemplateGetService
                    .getDocumentMasterTemplateById(id, Optional.of(tenantId))
                    .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_MASTER_TEMPLATE, id))
        );
        return Response.ok(documentMasterTemplate).build();
    }

    @POST
    @Path("/document/master/templates")
    @ApiOperation(value = "Add new document template", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response addDocumentMasterTemplate(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @Valid DocumentMasterTemplate documentMasterTemplate,
        @Context UriInfo uriInfo,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        DocumentMasterTemplate savedDocumentMasterTemplate = TenantContextInjector.spawnThreadAndCallInTenantAllEntityContext(
            tenantId,
            entityGetService,
            entityContextProvider,
            () -> documentTemplateService.upsertDocumentMasterTemplate(documentMasterTemplate, Optional.empty())
        );
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(savedDocumentMasterTemplate.getId().toString());
        return Response.created(builder.build()).build();
    }

    @POST
    @Path("/document/master/templates/file")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiOperation(value = "Add new document template", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response addDocumentMasterTemplateViaFileUpload(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @FormDataParam("file") InputStream fileStream,
        @FormDataParam("file") FormDataContentDisposition contentDisposition,
        @FormDataParam("data") DocumentMasterTemplate documentMasterTemplate,
        @Context UriInfo uriInfo,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        String content = convertFileStreamToString(fileStream);
        documentMasterTemplate.setContent(content);
        DocumentMasterTemplate savedDocumentMasterTemplate = TenantContextInjector.spawnThreadAndCallInTenantAllEntityContext(
            tenantId,
            entityGetService,
            entityContextProvider,
            () -> documentTemplateService.upsertDocumentMasterTemplate(documentMasterTemplate, Optional.empty())
        );
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(savedDocumentMasterTemplate.getId().toString());
        return Response.created(builder.build()).build();
    }

    @PUT
    @Path("/document/master/templates/{id}")
    @ApiOperation(value = "Update document template", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response updateDocumentMasterTemplate(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("id") String id,
        @Valid DocumentMasterTemplate documentMasterTemplate,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        documentMasterTemplate.setId(UUID.fromString(id));
        TenantContextInjector.spawnThreadAndCallInTenantAllEntityContext(tenantId, entityGetService, entityContextProvider, () ->
            documentTemplateService.upsertDocumentMasterTemplate(documentMasterTemplate, Optional.empty())
        );
        return Response.ok().build();
    }

    @PUT
    @Path("/document/master/templates/{id}/file")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiOperation(value = "Update document template", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response updateDocumentMasterTemplateViaFileUpload(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("id") String id,
        @FormDataParam("file") InputStream fileStream,
        @FormDataParam("file") FormDataContentDisposition contentDisposition,
        @FormDataParam("data") DocumentMasterTemplate documentMasterTemplate,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        String content = convertFileStreamToString(fileStream);
        documentMasterTemplate.setContent(content);
        documentMasterTemplate.setId(UUID.fromString(id));
        TenantContextInjector.spawnThreadAndCallInTenantAllEntityContext(tenantId, entityGetService, entityContextProvider, () ->
            documentTemplateService.upsertDocumentMasterTemplate(documentMasterTemplate, Optional.empty())
        );
        return Response.ok().build();
    }

    @PUT
    @Path("/document/master/templates/{id}/status/{status}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Update document template status", notes = "Updates the status of a document template", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response updateDocumentMasterTemplateStatus(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "id of the document template") String id,
        @PathParam("status") @ApiParam(value = "new status to set", allowableValues = "DRAFT, ACTIVE") String status,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @Context UriInfo uriInfo
    ) {
        TenantContextInjector.spawnThreadAndCallInTenantAllEntityContext(tenantId, entityGetService, entityContextProvider, () -> {
            DocumentMasterTemplate masterTemplate = documentTemplateGetService
                .getDocumentMasterTemplateById(id, Optional.empty())
                .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_MASTER_TEMPLATE, id));
            Optional<DocumentMasterTemplateStatus> masterTemplateStatus = DocumentMasterTemplateStatus.fromString(status);
            if (masterTemplateStatus.isEmpty()) {
                throw new IllegalArgumentException(
                    "Invalid document template status: " + status + ". DocumentMasterTemplate status should be either DRAFT or ACTIVE"
                );
            }

            documentTemplateService.updateDocumentMasterTemplateStatus(masterTemplate, masterTemplateStatus.get(), Optional.empty());
            return null;
        });

        return Response.status(Response.Status.OK).build();
    }

    @DELETE
    @Path("/document/master/templates/{id}")
    @ApiOperation(value = "Delete document template", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response deleteDocumentMasterTemplate(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @PathParam("id") String id,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);

        var deletedTemplate = TenantContextInjector.spawnThreadAndCallInTenantAllEntityContext(
            tenantId,
            entityGetService,
            entityContextProvider,
            () -> documentTemplateService.deleteDocumentMasterTemplate(id, Optional.of(tenantId))
        );
        return Response.ok().entity(deletedTemplate).build();
    }

    @POST
    @Path("/fx/transactional/refresh")
    @ApiOperation(value = "Refresh transactional exchange rates", hidden = true)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response refreshExchangeRates(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("tenantId") @NotEmpty String tenantId,
        @QueryParam("effectiveDate") @NotNull Long effectiveDate
    ) {
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            transactionalExchangeRateService.refreshSupportedExchangeRates(Instant.ofEpochSecond(effectiveDate))
        );
        return Response.ok().build();
    }

    @POST
    @Path("/fx/transactional/backfill")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runTransactionFXBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.TRANSACTIONAL_FX_BACKFILL_JOB,
            TransactionalExchangeBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/bulkInvoiceRun/backfillId")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    @SuppressWarnings({ "deprecation" })
    public Response runBulkInvoiceRunIdBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.BULK_INVOICE_RUN_ID_BACKFILL,
            BulkInvoiceRunIdBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/automatedInvoiceRule/backfillId")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    @SuppressWarnings({ "deprecation" })
    public Response runAutomatedInvoiceRuleIdBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.AUTOMATED_INVOICE_RULE_ID_BACKFILL,
            AutomatedInvoiceRuleIdBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @POST
    @Path("/automatedInvoiceRule/run")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runAutomatedInvoiceRuleForTenant(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("tenantId") String tenantId,
        @QueryParam("automatedInvoiceRuleId") String automatedInvoiceRuleId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);

        AutomatedInvoiceRule automatedInvoiceRule = automatedInvoiceRuleGetService.getAutomatedInvoiceRuleById(automatedInvoiceRuleId, tenantId);
        if (BooleanUtils.isNotTrue(automatedInvoiceRule.isEnabled())) {
            throw new InvalidInputException("Automated invoice rule is not enabled.");
        }

        List<Entity> entities = entityGetService.getEntitiesByTenantId(tenantId);
        entities
            .stream()
            .map(Entity::getEntityId)
            .forEach(entityId -> dispatchAutomatedInvoiceGenerationJob(tenantId, entityId, automatedInvoiceRule));

        return Response.ok().build();
    }

    @PUT
    @Path("/bulkInvoiceRunItem/addGeneratedByToInvoice")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response addGeneratedByToInvoiceForBulkInvoiceRunItem(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("tenantId") String tenantId,
        @QueryParam("bulkInvoiceRunItemId") String id
    ) {
        UUID bulkInvoiceRunItemId = Validator.validateUUID(id, "bulkInvoiceRunItemId");
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            addGeneratedByToInvoiceForBulkInvoiceRunItem(bulkInvoiceRunItemId)
        );

        return Response.ok().build();
    }

    private void addGeneratedByToInvoiceForBulkInvoiceRunItem(UUID bulkInvoiceRunItemId) {
        BulkInvoiceRunItem bulkInvoiceRunItem = bulkInvoiceService.getBulkInvoiceRunItemById(bulkInvoiceRunItemId);

        if (bulkInvoiceRunItem.isDraftInvoiceNotGenerated() && bulkInvoiceRunItem.isInvoiceNotPosted()) {
            throw new InvalidInputException("No invoice is posted for this bulk invoice run item: " + bulkInvoiceRunItemId);
        }

        String invoiceNumber = Optional.ofNullable(bulkInvoiceRunItem.getPostedInvoiceNumber()).orElse(bulkInvoiceRunItem.getDraftInvoiceNumber());
        UUID bulkInvoiceRunId = Validator.validateUUID(bulkInvoiceRunItem.getRunId(), "BulkInvoiceRunId");
        BulkInvoiceRun bulkInvoiceRun = bulkInvoiceService.getBulkInvoiceRun(bulkInvoiceRunId);
        invoiceService.updateInvoiceGeneratedBy(invoiceNumber, bulkInvoiceRun.getBulkInvoiceRunId());
    }

    @PUT
    @Path("/bulkInvoiceRun/addGeneratedByToInvoices")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response addGeneratedByToInvoicesForBulkInvoiceRun(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("tenantId") String tenantId,
        @QueryParam("bulkInvoiceRunId") String bulkInvoiceRunId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        Validator.validateNonNullArgument(bulkInvoiceRunId, "bulkInvoiceRunId");

        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            addGeneratedByToInvoicesForBulkInvoiceRun(bulkInvoiceRunId)
        );

        return Response.ok().build();
    }

    private void addGeneratedByToInvoicesForBulkInvoiceRun(String bulkInvoiceRunId) {
        BulkInvoiceRun bulkInvoiceRun = bulkInvoiceService.getBulkInvoiceRunByBulkInvoiceRunId(bulkInvoiceRunId);
        List<BulkInvoiceRunItem> bulkInvoiceRunItems = bulkInvoiceService.getBulkInvoiceRunItems(UUID.fromString(bulkInvoiceRun.getId()));

        List<String> invoiceNumbers = bulkInvoiceRunItems
            .stream()
            .filter(item -> item.isDraftInvoiceGenerated() || item.isInvoicePosted())
            .map(item -> Optional.ofNullable(item.getPostedInvoiceNumber()).orElse(item.getDraftInvoiceNumber()))
            .toList();

        invoiceNumbers.forEach(invoiceNumber -> invoiceService.updateInvoiceGeneratedBy(invoiceNumber, bulkInvoiceRun.getBulkInvoiceRunId()));
    }

    private void dispatchAutomatedInvoiceGenerationJob(String tenantId, String entityId, AutomatedInvoiceRule automatedInvoiceRule) {
        String partitionKey = TenantEntityPartitionKey.builder().withTenantId(tenantId).withEntityId(entityId).build().getKey();

        ImmutableTenantJob job = ImmutableTenantJob.builder()
            .module(TenantJobModule.AUTOMATIC_INVOICE_GENERATION)
            .jobType(TenantJobType.AUTOMATED_INVOICE_RULE_TRIGGER)
            .entityId(entityId)
            .objectModel(TenantJobObjectModel.AUTOMATED_INVOICE_RULE)
            .objectId(automatedInvoiceRule.getAutomatedInvoiceRuleId())
            .partitionKey(partitionKey)
            .build();

        LOGGER.info(
            "Dispatching tenant job (via Admin API) for automated invoice generation. Rule id: {}. Partition key: {}",
            automatedInvoiceRule.getAutomatedInvoiceRuleId(),
            partitionKey
        );

        tenantJobDispatcherService.dispatch(job, Optional.of(tenantId));
    }

    /**
     * Admin endpoint to migrate existing payments for a tenant with a default bank account and bankFee as 0
     */
    @POST
    @Path("/payment/bankfees/migrate")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response migratePayments(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @QueryParam("paymentBankAccountId") String paymentBankAccountId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        List<String> migratedPaymentIds = TenantContextInjector.spawnThreadAndCallInTenantContext(tenantId, tenantIdProvider, () ->
            paymentService.migrateExistingPaymentsForTenant(paymentBankAccountId)
        );
        return Response.ok(migratedPaymentIds).build();
    }

    @POST
    @Path("/invoice/memoize/backfill")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response memoizationBackfill(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("orderId") String orderId,
        @QueryParam("isDryRun") Boolean dryRunParam
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        boolean isDryRun = dryRunParam == null || dryRunParam;
        List<UUID> backfilledMemoizedLines = TenantContextInjector.spawnThreadAndCallInTenantContext(tenantId, tenantIdProvider, () ->
            invoiceServiceInternal.backfillMemoizedItems(tenantId, orderId, Optional.empty(), isDryRun)
        );
        return Response.ok(backfilledMemoizedLines).build();
    }

    @POST
    @Path("/invoice/memoize/backfill/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response memoizationBackfillJob(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantIds") String tenantIdsParam,
        @NotEmpty @QueryParam("batchSize") String batchSize,
        @QueryParam("isDryRun") Boolean dryRunParam
    ) {
        if (tenantIdsParam == null || tenantIdsParam.isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST).entity("tenantIds parameter is required").build();
        }
        List<String> tenantIds = Arrays.asList(tenantIdsParam.split(","));
        boolean isDryRun = dryRunParam == null || dryRunParam;
        tenantIds.forEach(tenantId -> {
            tenantService.throwIfTenantIdIsNotValid(tenantId);
            try {
                quartzSchedulerService.scheduleSimpleQuartzJob(
                    Optional.of(tenantId),
                    UUID.randomUUID().toString(),
                    Optional.of(batchSize + "__" + isDryRun),
                    QuartzJobType.BACKFILL_MEMOIZED_INVOICE_LINES,
                    BackfillMemoizedInvoiceLinesJob.class,
                    Date.from(Instant.now()),
                    SimpleScheduleBuilder.simpleSchedule()
                );
            } catch (SchedulerException e) {
                LOGGER.warn("Failed to schedule memoization backfill job for tenant {}", tenantId, e);
            }
        });
        return Response.ok().build();
    }

    @POST
    @Path("/invoice/amounts/comparison/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response compareOrderLineToPreviewAmountsJob(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotEmpty @QueryParam("tenantIds") String tenantIdsParam,
        @QueryParam("orderStatus") String orderStatus
    ) {
        if (tenantIdsParam == null || tenantIdsParam.isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST).entity("tenantIds parameter is required").build();
        }
        List<String> tenantIds = Arrays.asList(tenantIdsParam.split(","));
        tenantIds.forEach(tenantId -> {
            tenantService.throwIfTenantIdIsNotValid(tenantId);
            try {
                quartzSchedulerService.scheduleSimpleQuartzJob(
                    Optional.of(tenantId),
                    UUID.randomUUID().toString(),
                    orderStatus != null ? Optional.of(orderStatus) : Optional.empty(),
                    QuartzJobType.COMPARE_ORDER_LINE_TO_PREVIEW_AMOUNTS,
                    CompareOrderLineToPreviewAmountsJob.class,
                    Date.from(Instant.now()),
                    SimpleScheduleBuilder.simpleSchedule()
                );
            } catch (SchedulerException e) {
                LOGGER.warn("Failed to schedule job for tenant {}", tenantId, e);
            }
        });

        return Response.ok().build();
    }

    @POST
    @Path("/emailLastSentOn/job")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response runEmailLastSentOnBackfillJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) throws Exception {
        quartzSchedulerService.scheduleSimpleQuartzJob(
            Optional.empty(),
            UUID.randomUUID().toString(),
            Optional.empty(),
            QuartzJobType.EMAIL_LAST_SENT_ON_BACKFILL,
            EmailLastSentOnBackfillJob.class,
            Date.from(Instant.now()),
            SimpleScheduleBuilder.simpleSchedule()
        );
        return Response.ok().build();
    }

    @GET
    @Path("/templateScript")
    @AllowRoles({ Role.BILLY_ENGINEER })
    public Response getTenantTemplateScripts(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("templateType") TemplateType templateType,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        List<TemplateScript> templateScripts = templateScriptService.getTemplateScripts(templateType, Optional.of(tenantId), false);
        return Response.ok(templateScripts).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/templateScript")
    @AllowRoles({ Role.BILLY_ENGINEER })
    public Response addTenantTemplateScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotNull TemplateScript templateScript,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        TemplateScript result = templateScriptService.addTemplateScript(templateScript, Optional.of(tenantId));
        return Response.ok(result).build();
    }

    @DELETE
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/templateScript")
    @AllowRoles({ Role.BILLY_ENGINEER })
    public Response disableTenantTemplateScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotNull TemplateScript templateScript,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        templateScriptService.disableTemplateScript(templateScript.getId(), Optional.of(tenantId));
        return Response.ok().build();
    }

    @GET
    @Path("/templateScript/global")
    @AllowRoles({ Role.BILLY_ENGINEER })
    public Response getGlobalTemplateScripts(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("templateType") TemplateType templateType
    ) {
        List<TemplateScript> templateScripts = templateScriptService.getGlobalTemplateScripts(templateType);
        return Response.ok(templateScripts).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/templateScript/global")
    @AllowRoles({ Role.BILLY_ENGINEER })
    public Response addGlobalTemplateScript(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @NotNull TemplateScript templateScript) {
        TemplateScript result = templateScriptService.addGlobalTemplateScript(templateScript);
        return Response.ok(result).build();
    }

    @DELETE
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/templateScript/global")
    @AllowRoles({ Role.BILLY_ENGINEER })
    public Response disableGlobalTemplateScript(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @NotNull TemplateScript templateScript) {
        templateScriptService.disableGlobalTemplateScript(templateScript.getId());
        return Response.ok().build();
    }

    @POST
    @Path("/resend-email/{email}")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response resendEmailForExistingUser(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("email") String email,
        @NotEmpty @QueryParam("tenantId") String tenantId
    ) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        userService.resendCognitoInvitationEmailForExistingUserInGivenTenant(email, tenantId);
        return Response.ok().build();
    }

    @POST
    @Path("/esign/void")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response forceVoidEsignatureDocumentForOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("orderId") String orderId
    ) {
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () ->
            esignService.forceVoidEsignatureDocumentForOrder(orderId)
        );
        return Response.ok().build();
    }

    @POST
    @Path("/esign/upload-completed-esign")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response uploadCompletedEsignDocumentToS3(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("tenantId") String tenantId,
        @NotEmpty @QueryParam("orderId") String orderId
    ) {
        TenantContextInjector.spawnThreadAndRunInTenantContext(tenantId, tenantIdProvider, () -> {
            ElectronicSignature signature = esignService.getElectronicSignatureByOrderId(orderId);
            if (signature.getDocumentProvider() == ElectronicSignatureProvider.PANDADOC) {
                esignService.downloadFromPandaDocAndUploadToS3(signature, true);
            }
        });
        return Response.ok().build();
    }

    @POST
    @Path("/backfill-subscription-status-change-tasks")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response backfillSubscriptionStatusChangeTasks(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        tenantService
            .getTenants()
            .forEach(tenant ->
                TenantContextInjector.spawnThreadAndRunInTenantContext(tenant.getTenantId(), tenantIdProvider, () ->
                    subscriptionStatusChangeTaskBackfiller.dispatchNewTaskForTenant(tenant.getTenantId())
                )
            );
        return Response.ok().build();
    }

    @POST
    @Path("/backfill-subscription-charge-change-tasks")
    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    public Response backfillSubscriptionChargeChangeTasks(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        tenantService
            .getTenants()
            .forEach(tenant ->
                TenantContextInjector.spawnThreadAndRunInTenantContext(tenant.getTenantId(), tenantIdProvider, () ->
                    subscriptionChargeChangeTaskBackfiller.dispatchNewTaskForTenant(tenant.getTenantId())
                )
            );
        return Response.ok().build();
    }

    private String convertFileStreamToString(InputStream inputStream) {
        try {
            return new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new ServiceFailureException("Error while reading file stream", e);
        }
    }
}
