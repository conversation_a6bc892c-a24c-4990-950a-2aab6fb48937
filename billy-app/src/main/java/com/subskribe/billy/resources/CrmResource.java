package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.crm.model.CrmOpportunityNameChangeNotificationRequest;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.validation.Validator;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import software.amazon.kinesis.exceptions.InvalidStateException;

@Path("/crm")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class CrmResource {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrmResource.class);

    private final CrmService crmService;
    private final OpportunityService opportunityService;
    private final TenantIdProvider tenantIdProvider;

    @Inject
    public CrmResource(CrmService crmService, OpportunityService opportunityService, TenantIdProvider tenantIdProvider) {
        this.crmService = crmService;
        this.opportunityService = opportunityService;
        this.tenantIdProvider = tenantIdProvider;
    }

    @POST
    @Path("/account/{accountId}/sync")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Sync account to crm", hidden = true)
    @AllowAllRoles
    public Response syncAccountToCrm(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("accountId") @ApiParam(value = "id of the account to sync") String accountId
    ) {
        crmService.dispatchAccountSync(accountId);
        return Response.ok().build();
    }

    @POST
    @Path("/subscription/{subscriptionId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowAllRoles
    @ApiOperation(value = "Sync subscription to crm", hidden = true)
    public Response syncSubscriptionToCrm(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("subscriptionId") @ApiParam(value = "id of the subscription to sync") String subscriptionId
    ) {
        crmService.pushSubscriptionSync(subscriptionId);
        return Response.ok().build();
    }

    @POST
    @Path("/order/{orderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowAllRoles
    @ApiOperation(value = "Sync order to crm", hidden = true)
    public Response syncOrderToCrm(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("orderId") @ApiParam(value = "id of the order to sync") String orderId
    ) throws InvalidStateException {
        crmService.dispatchOrder(orderId);
        return Response.ok().build();
    }

    @POST
    @Path("/{opportunityCrmId}/notifyNameChange")
    @ApiOperation(
        value = "Opportunity name change notification",
        notes = "Subskribe is notified for the opportunity name change happening in Crm systems. " +
        "This is used to update the opportunity name in Subskribe.",
        tags = { "Integrations" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response opportunityNameChangeNotification(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("opportunityCrmId") @ApiParam(value = "crm id of the opportunity", required = true) String opportunityCrmId,
        @NotNull @Valid @ApiParam(
            value = "opportunity request",
            required = true
        ) CrmOpportunityNameChangeNotificationRequest crmOpportunityNameChangeNotificationRequest
    ) {
        Validator.validateNonNullArgument(opportunityCrmId);
        String tenantId = tenantIdProvider.provideTenantIdString();
        LOGGER.info("Opportunity name change notification for opportunity: {} in tenant: {}", opportunityCrmId, tenantId);
        opportunityService.processOpportunityNameChangeNotification(opportunityCrmId, crmOpportunityNameChangeNotificationRequest);
        return Response.ok().build();
    }
}
