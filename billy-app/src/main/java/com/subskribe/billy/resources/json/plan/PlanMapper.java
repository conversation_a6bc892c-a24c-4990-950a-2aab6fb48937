package com.subskribe.billy.resources.json.plan;

import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.productcatalog.model.AddPlanRequest;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.UpdatePlanRequest;
import com.subskribe.billy.shared.mapper.BaseMapper;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import java.util.Currency;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public abstract class PlanMapper implements BaseMapper, CustomFieldAPIMapper {

    public abstract List<PlanJson> plansToJson(List<Plan> plans);

    public abstract Plan addPlanRequestToPlan(AddPlanRequest addPlanRequest);

    @Mapping(source = "id", target = "planId")
    @Mapping(target = "id", ignore = true)
    public abstract Plan updatePlanRequestToPlan(UpdatePlanRequest updatePlanRequest);

    public Plan jsonToPlan(PlanJson json) {
        var plan = new Plan();
        plan.setId(null);
        plan.setEntityIds(json.getEntityIds());
        plan.setPlanId(json.getId());
        plan.setDisplayName(json.getDisplayName());
        plan.setName(json.getName());
        plan.setDescription(json.getDescription());
        plan.setStatus(json.getStatus());
        plan.setProductId(json.getProductId());
        plan.setCharges(jsonToCharges(json.getCharges()));
        Currency currency = StringUtils.isNotBlank(json.getCurrency())
            ? Currency.getInstance(json.getCurrency())
            : SupportedCurrency.getDefaultCurrency();
        plan.setCurrency(currency);
        plan.setExternalId(json.getExternalId());
        plan.setTemplateIds(json.getTemplateIds());
        plan.setReplacementPlanIds(json.getReplacementPlanIds());
        plan.setCustomFields(toCustomField(json.getCustomFields()));
        return plan;
    }

    public PlanJson planToJson(Plan plan) {
        var json = new PlanJson();
        json.setId(plan.getPlanId());
        json.setEntityIds(plan.getEntityIds());
        json.setName(plan.getName());
        json.setDisplayName(plan.getDisplayName());
        json.setDescription(plan.getDescription());
        json.setStatus(plan.getStatus());
        json.setProductId(plan.getProductId());
        json.setCharges(chargesToJson(plan.getCharges()));
        Currency currency = (plan.getCurrency() != null) ? plan.getCurrency() : SupportedCurrency.getDefaultCurrency();
        json.setCurrency(currency.getCurrencyCode());
        json.setExternalId(plan.getExternalId());
        json.setTemplateIds(plan.getTemplateIds());
        json.setReplacementPlanIds(plan.getReplacementPlanIds());
        json.setCustomFields(toCustomFieldsMap(plan.getCustomFields()));
        json.setUpdatedOn((plan.getUpdatedOn() != null) ? plan.getUpdatedOn().getEpochSecond() : null);
        return json;
    }

    public abstract List<Charge> jsonToCharges(List<ChargeJson> jsons);

    public abstract List<ChargeJson> chargesToJson(List<Charge> charges);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "chargeId", source = "id")
    public abstract Charge jsonToCharge(ChargeJson json);

    @Mapping(target = "id", source = "chargeId")
    public abstract ChargeJson chargeToJson(Charge charge);

    public abstract Plan clonePlan(Plan plan);
}
