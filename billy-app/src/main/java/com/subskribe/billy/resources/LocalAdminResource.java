package com.subskribe.billy.resources;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.apikey.ApiKeyService;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.model.ApiKeyJwtToken;
import com.subskribe.billy.auth.model.ApiKeyTokenAndSecret;
import com.subskribe.billy.auth.model.AuthTenantCognito;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.auth.model.UserPrincipal;
import com.subskribe.billy.auth.services.TenantCognitoService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.docusign.service.DocuSignAuthService;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.automated.model.AutomatedInvoiceRule;
import com.subskribe.billy.invoice.automated.service.AutomatedInvoiceRuleGetService;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRun;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunInput;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceService;
import com.subskribe.billy.invoice.document.InvoiceDocumentDataAggregator;
import com.subskribe.billy.invoice.document.InvoiceDocumentJson;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.metricsreporting.service.MetricsReportingService;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationService;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.postgres.PostgresAdvisoryLock;
import com.subskribe.billy.resources.json.customfield.CustomFieldDefinitionCreateInput;
import com.subskribe.billy.resources.json.customfield.CustomFieldDefinitionJsonMapper;
import com.subskribe.billy.resources.json.tenant.TenantCreationResponseJson;
import com.subskribe.billy.resources.shared.PaginatedResponse;
import com.subskribe.billy.resources.shared.PaginationValidator;
import com.subskribe.billy.salesroom.intelligent.service.IntelligentSalesRoomService;
import com.subskribe.billy.scheduler.job.JobJson;
import com.subskribe.billy.scheduler.service.QuartzManagerService;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.TaskExecution;
import com.subskribe.billy.shared.task.queue.scheduler.TaskDispatcher;
import com.subskribe.billy.shared.task.queue.scheduler.TaskSchedulerRunner;
import com.subskribe.billy.shared.task.queue.service.TenantQueuedTaskExecutionService;
import com.subskribe.billy.shared.task.queue.service.TenantQueuedTaskService;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.tenant.model.Tenant;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.model.TenantSettingSeal;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.time.TimeZoneService;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.ForbiddenException;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Field;
import org.jooq.Record;
import org.mapstruct.factory.Mappers;
import org.quartz.SchedulerException;

@Path("/admin/local")
@Produces(MediaType.APPLICATION_JSON)
@Api(hidden = true)
public class LocalAdminResource {

    private static final String TEST_STRING_FORMAT = "TEST-%s-%s";

    private static final String TEST_USER_ID_SALES = "USER-SALES";

    private static final String TEST_USER_ID_FINANCE = "USER-FINANCE";

    private static final String TEST_USER_ID_ADMIN = "USER-ADMIN";

    private static final String TEST_USER_ID_SALES_MANAGER = "USER-SALES-MANAGER";

    private static final String TEST_USER_ID_ACCOUNTANT = "USER-ACCOUNTANT";

    private static final String TEST_USER_ID_BILLING_CLERK = "USER-BILLING-CLERK";

    private static final String TEST_USER_ID_REVENUE_CLERK = "USER-REVENUE-CLERK";

    private static final String TEST_USER_ID_READ_ONLY = "USER-READ-ONLY";

    private static final String TEST_USER_ID_EXECUTIVE = "USER-EXECUTIVE";

    private static final String TEST_USER_ID_BILLY_ENGINEER = "USER-BILLY-ENGINEER";

    private static final String TEST_USER_ID_BILLY_ADMIN = "USER-BILLY-ADMIN";

    private static final String TEST_STRIPE_CONNECT_ACCOUNT_ID = "acct_1Kj6crR9UnQ2znX5";

    private static final String TENANT_CREATION_LOCK_KEY = "create-local-tenant";

    private final BillyConfiguration billyConfiguration;

    private final TenantService tenantService;

    private final TenantCognitoService tenantCognitoService;

    private final ApiKeyService apiKeyService;

    private final UserService userService;

    private final TenantSettingService tenantSettingService;

    private final PaymentIntegrationService paymentIntegrationService;

    private final QuartzManagerService quartzManagerService;

    private final DSLContextProvider dslContextProvider;

    private final CustomFieldService customFieldService;

    private final CustomFieldDefinitionJsonMapper customFieldDefinitionJsonMapper;

    private final CustomFieldAPIMapper customFieldAPIMapper;

    private final FeatureService featureService;

    private final InvoiceDocumentDataAggregator invoiceDocumentDataAggregator;

    private final DocuSignAuthService docuSignAuthService;

    private final MetricsReportingService metricsReportingService;

    private final PlatformFeatureService platformFeatureService;

    private final TaskDispatcher taskDispatcher;

    private final TenantQueuedTaskService tenantQueuedTaskService;

    private final TenantQueuedTaskExecutionService tenantQueuedTaskExecutionService;

    private final TaskSchedulerRunner taskSchedulerRunner;

    private final BulkInvoiceService bulkInvoiceService;

    private final AutomatedInvoiceRuleGetService automatedInvoiceRuleGetService;

    private final IntelligentSalesRoomService intelligentSalesRoomService;

    @Inject
    public LocalAdminResource(
        BillyConfiguration billyConfiguration,
        TenantService tenantService,
        TenantCognitoService tenantCognitoService,
        ApiKeyService apiKeyService,
        UserService userService,
        TenantSettingService tenantSettingService,
        PaymentIntegrationService paymentIntegrationService,
        QuartzManagerService quartzManagerService,
        DSLContextProvider dslContextProvider,
        CustomFieldService customFieldService,
        FeatureService featureService,
        InvoiceDocumentDataAggregator invoiceDocumentDataAggregator,
        DocuSignAuthService docuSignAuthService,
        MetricsReportingService metricsReportingService,
        PlatformFeatureService platformFeatureService,
        TaskDispatcher taskDispatcher,
        TenantQueuedTaskService tenantQueuedTaskService,
        TenantQueuedTaskExecutionService tenantQueuedTaskExecutionService,
        TaskSchedulerRunner taskSchedulerRunner,
        BulkInvoiceService bulkInvoiceService,
        AutomatedInvoiceRuleGetService automatedInvoiceRuleGetService,
        IntelligentSalesRoomService intelligentSalesRoomService
    ) {
        this.billyConfiguration = billyConfiguration;
        this.tenantService = tenantService;
        this.tenantCognitoService = tenantCognitoService;
        this.apiKeyService = apiKeyService;
        this.userService = userService;
        this.tenantSettingService = tenantSettingService;
        this.paymentIntegrationService = paymentIntegrationService;
        this.quartzManagerService = quartzManagerService;
        this.dslContextProvider = dslContextProvider;
        this.customFieldService = customFieldService;
        this.featureService = featureService;
        this.invoiceDocumentDataAggregator = invoiceDocumentDataAggregator;
        this.docuSignAuthService = docuSignAuthService;
        this.metricsReportingService = metricsReportingService;
        this.platformFeatureService = platformFeatureService;
        this.taskDispatcher = taskDispatcher;
        this.tenantQueuedTaskService = tenantQueuedTaskService;
        this.tenantQueuedTaskExecutionService = tenantQueuedTaskExecutionService;
        this.taskSchedulerRunner = taskSchedulerRunner;
        this.bulkInvoiceService = bulkInvoiceService;
        this.automatedInvoiceRuleGetService = automatedInvoiceRuleGetService;
        this.intelligentSalesRoomService = intelligentSalesRoomService;
        customFieldDefinitionJsonMapper = Mappers.getMapper(CustomFieldDefinitionJsonMapper.class);
        customFieldAPIMapper = Mappers.getMapper(CustomFieldAPIMapper.class);
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/tenant")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    @AllowNonRlsDataAccess
    public Response createLocalTenant(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) {
        try {
            PostgresAdvisoryLock.acquireLockBlocking(dslContextProvider.get(), TENANT_CREATION_LOCK_KEY);
            throwIfNotLocal();
            throwIfNotTestTenant();
            Tenant tenant = createTestTenantObject();
            String userId = adminUser.getSubskribeUserId().orElse(StringUtils.EMPTY);
            setUpBillyAdminPrincipal(tenant.getEmail(), adminUser.getTenantId().getRequiredId(), userId);
            TenantCreationResponseJson result = tenantService.createTenant(tenant);
            tenantSettingService.updateTenantSettingSealForTest(TenantSettingSeal.ON, result.getTenantId());
            createFakeAuthTenantCognitoForTenant(result.getTenantId());
            createTestUser(Role.ADMIN, result.getTenantId());
            String adminApiKey = generateApiKeyWithRole(Role.ADMIN, result.getTenantId(), EntityContext.ALL_ACCESS_ID).getSecretValue();
            result.setAdminApiKey(adminApiKey);
            return Response.ok().entity(result).build();
        } finally {
            CurrentUserProvider.resetAuth();
        }
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/tenant/user")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response createUserForLocalTenant(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotNull @QueryParam("role") String roleString
    ) {
        try {
            throwIfNotLocal();
            String tenantId = adminUser.getTenantId().getRequiredId();
            Role role = Role.fromString(roleString).orElseThrow();
            createTestUser(role, tenantId);
            ApiKeyJwtToken apiKey = generateApiKeyWithRole(role, tenantId, EntityContext.ALL_ACCESS_ID);
            return Response.ok().entity(apiKey).build();
        } finally {
            CurrentUserProvider.resetAuth();
        }
    }

    @DELETE
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/tenant")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response deleteLocalTenant(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) {
        try {
            throwIfNotLocal();
            String email = generateRandomString("email") + "@subskribe.com";
            String userId = adminUser.getSubskribeUserId().orElse(StringUtils.EMPTY);
            setUpBillyAdminPrincipal(email, adminUser.getTenantId().getRequiredId(), userId);
            tenantService.deactivateTenant(adminUser.getTenantId().getRequiredId());
            return Response.ok().build();
        } finally {
            CurrentUserProvider.resetAuth();
        }
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/stripe")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response createTestStripeIntegration(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) {
        throwIfNotLocal();
        paymentIntegrationService.createTestStripeIntegration(TEST_STRIPE_CONNECT_ACCOUNT_ID);
        return Response.ok().build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/setFeature/{featureName}")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response setLocalFeatureFlag(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("featureName") String featureName,
        @NotNull @QueryParam("value") String value
    ) {
        throwIfNotLocal();
        featureService.addLocalTenantFeatureFlagOverride(Feature.valueOf(featureName), BooleanUtils.toBoolean(value));
        return Response.ok().build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/customFieldDefinition")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response createCustomFieldDefinition(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(value = "custom field definition values", required = true) CustomFieldDefinitionCreateInput json,
        @Context UriInfo uriInfo
    ) {
        throwIfNotLocal();
        CustomFieldDefinition customFieldDefinition = customFieldDefinitionJsonMapper.toCustomFieldDefinition(json);
        var savedCustomFieldDefinition = customFieldService.createCustomFieldDefinitionBySystem(customFieldDefinition);
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(savedCustomFieldDefinition.getCustomFieldId());
        return Response.created(builder.build()).build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/customField/{parentObjectType}/{parentObjectId}")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response setCustomFields(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("parentObjectType") @ApiParam(value = "object type custom fields are attached to", required = true) String parentObjectType,
        @PathParam("parentObjectId") @ApiParam(value = "Id of the parent object", required = true) String parentObjectId,
        @NotNull @ApiParam(value = "custom field values", required = true) Map<String, CustomFieldValue> customFields
    ) {
        throwIfNotLocal();
        var type = CustomFieldParentType.valueOf(parentObjectType);
        CustomField customField = customFieldAPIMapper.toCustomField(customFields);
        CustomField updatedCustomField = updateCustomField(type, parentObjectId, customField);
        return Response.ok(customFieldAPIMapper.toCustomFieldsMap(updatedCustomField)).build();
    }

    private CustomField updateCustomField(CustomFieldParentType type, String parentObjectId, CustomField customField) {
        return customFieldService.setCustomFieldsBySystem(type, parentObjectId, customField);
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/triggerJob")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowUnauthenticated(cascade = true)
    public Response triggerJob(@NotNull JobJson job) {
        throwIfNotLocal();
        try {
            quartzManagerService.scheduleJob(job.getJobName(), job.getJobGroup());
        } catch (SchedulerException | InterruptedException e) {
            return Response.serverError().build();
        }
        return Response.ok().build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/runQuery")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles(Role.ADMIN)
    @AllowNonRlsDataAccess
    public Response runQuery(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @NotEmpty String sqlQuery) {
        throwIfNotLocal();
        Record[] records = dslContextProvider.get().resultQuery(sqlQuery).fetchArray();
        List<Map<String, Object>> resultsMap = new ArrayList<>();
        for (Record record : records) {
            Map<String, Object> recordMap = new HashMap<>();
            for (Field<?> field : record.fields()) {
                recordMap.put(field.getName(), record.getValue(field));
            }
            resultsMap.add(recordMap);
        }
        return Response.ok(resultsMap).build();
    }

    @GET
    @Path("/invoice-document-data/{invoiceNumber}")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response createInvoiceDocument(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @PathParam("invoiceNumber") String invoiceNumber
    ) {
        throwIfNotLocal();
        InvoiceDocumentJson invoiceDocumentJson = invoiceDocumentDataAggregator.generateDocumentDataForInvoice(new Invoice.Number(invoiceNumber));
        return Response.ok(invoiceDocumentJson).build();
    }

    private void createTestUser(Role role, String tenantId) {
        if (role.notUserAssignable() && !UserService.isBillyRole(role)) {
            return;
        }
        User user = createTestUserObjectForRole(role, tenantId);
        userService.createUserForTenant(user, tenantId);
        userService.acceptTermsForGivenTestUser(user, tenantId);
    }

    private User createTestUserObjectForRole(Role role, String tenantId) {
        User user = new User();
        user.setUserId(getUserIdForRole(role));
        user.setTenantId(tenantId);
        String email = generateRandomString("email") + "@subskribe.com";
        user.setEmail(email.toLowerCase());
        user.setRole(role);
        user.setNormalizedEmail(user.getEmail());
        user.setDisplayName(generateRandomString("displayName"));
        user.setEntityIds(Set.of(EntityContext.ALL_ACCESS_ID));
        return user;
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/key/{role}")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response createApiKey(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @PathParam("role") String roleString) {
        throwIfNotLocal();
        Optional<Role> role = Role.fromString(roleString);
        if (role.isEmpty()) {
            throw new IllegalArgumentException("Invalid role: " + roleString);
        }
        ApiKeyTokenAndSecret result = apiKeyService.generateNewApiKey(role, Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty());
        return Response.ok().entity(result.getJwtToken()).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/tenant/entityApiKey")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response createEntityApiKeyForLocalTenant(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotNull @QueryParam("role") String roleString,
        @NotNull @QueryParam("entityId") String entityId
    ) {
        try {
            throwIfNotLocal();
            String tenantId = adminUser.getTenantId().getRequiredId();
            Role role = Role.fromString(roleString).orElseThrow();
            ApiKeyJwtToken apiKey = generateApiKeyWithRole(role, tenantId, entityId);
            return Response.ok().entity(apiKey).build();
        } finally {
            CurrentUserProvider.resetAuth();
        }
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/docusign")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response createTestDocuSignIntegration(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) {
        throwIfNotLocal();
        docuSignAuthService.createTestDocuSignIntegration();
        return Response.ok().build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/generateArrReportingData")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response generateArrMetricsForOrder(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @NotNull List<String> orderIds) {
        throwIfNotLocal();
        orderIds.forEach(metricsReportingService::updateOrderLineArrCategoryForOrder);
        return Response.ok().build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/generateArrReportingData/externalSchedule")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response generateArrMetricsForExternalSchedule(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotNull List<String> scheduleIds
    ) {
        throwIfNotLocal();
        scheduleIds.forEach(metricsReportingService::updateArrMetricsForExternalScheduleId);
        return Response.ok().build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/generateArrReportingData/invoices")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response generateArrMetricsForUsageCharges(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotNull List<String> invoiceNumbers
    ) {
        throwIfNotLocal();
        invoiceNumbers.forEach(metricsReportingService::updateArrMetricsForInvoices);
        return Response.ok().build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/enablePlatformFeature")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response enablePlatformFeature(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("platformFeatureName") String platformFeatureName
    ) {
        throwIfNotLocal();
        PlatformFeature platformFeature = Validator.enumFromString(platformFeatureName, PlatformFeature.class, "platformFeatureName");
        var result = platformFeatureService.enablePlatformFeatureForTenant(platformFeature, adminUser.getTenantId().getRequiredId());
        return Response.ok(result).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/queued-task")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response addQueuedTask(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @NotNull List<QueuedTaskRequest> taskRequests) {
        throwIfNotLocal();
        List<QueuedTask> queuedTasks = taskDispatcher.scheduleTasks(taskRequests);
        return Response.ok(queuedTasks).build();
    }

    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/queued-task")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response getQueuedTasks(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @QueryParam("limit") @ApiParam(name = "limit", value = "number of items per page") Integer limitArg,
        @QueryParam("pageToken") @ApiParam(name = "pageToken", value = "pass this to subsequent calls") long pageToken
    ) {
        throwIfNotLocal();
        int limit = PaginationValidator.validateLimit(limitArg, 100, 500);
        PageResult<List<QueuedTask>, Instant> allTasks = tenantQueuedTaskService.findAllTasks(
            PageRequest.from(Instant.ofEpochSecond(pageToken), limit)
        );
        PaginatedResponse<QueuedTask> paginatedResponse = new PaginatedResponse<>(
            allTasks.getResult(),
            allTasks.getResult().size(),
            allTasks.getNextPageToken() != null ? String.valueOf(allTasks.getNextPageToken().getEpochSecond()) : null
        );
        return Response.ok(paginatedResponse).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/queued-task/run")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response runQueuedTasks(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser) {
        throwIfNotLocal();
        taskSchedulerRunner.run();
        return Response.ok().build();
    }

    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/queued-task/{taskId}/executions")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response getQueuedTasks(@Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser, @NotNull @PathParam("taskId") String taskId) {
        throwIfNotLocal();
        List<TaskExecution> allExecutionsForTaskId = tenantQueuedTaskExecutionService.findAllExecutionsForTaskId(taskId);
        return Response.ok(allExecutionsForTaskId).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/bulk-invoice-run/automated/{automatedInvoiceRuleId}")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response createBulkInvoiceRunForAutomatedInvoiceRule(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotNull BulkInvoiceRunInput bulkInvoiceRunInput,
        @NotNull @PathParam("automatedInvoiceRuleId") String automatedInvoiceRuleId
    ) {
        throwIfNotLocal();
        AutomatedInvoiceRule automatedInvoiceRule = automatedInvoiceRuleGetService.getAutomatedInvoiceRuleById(automatedInvoiceRuleId);
        BulkInvoiceRun bulkInvoiceRun = bulkInvoiceService.createBulkInvoiceRunForAutomatedInvoiceRule(bulkInvoiceRunInput, automatedInvoiceRule);
        return Response.ok(bulkInvoiceRun.getBulkInvoiceRunId()).build();
    }

    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/bulk-invoice-run/automated/{automatedInvoiceRuleId}")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response getBulkInvoiceRunByAutomatedInvoiceRuleId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @NotNull @PathParam("automatedInvoiceRuleId") String automatedInvoiceRuleId
    ) {
        throwIfNotLocal();
        BulkInvoiceRun bulkInvoiceRun = bulkInvoiceService.getLatestBulkInvoiceRunForAutomatedInvoiceRule(automatedInvoiceRuleId);
        return Response.ok(bulkInvoiceRun.getBulkInvoiceRunId()).build();
    }

    @PUT
    @Path("/{salesRoomId}/ready-to-share")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response transitionToReadyToShare(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId
    ) {
        intelligentSalesRoomService.transitionToReadyToShare(salesRoomId);
        return Response.ok().build();
    }

    @POST
    @Path("/setDocumentGenerationEnablement")
    @ApiOperation(value = "Testing API available only locally", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response setDocumentGenerationEnablement(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @QueryParam("enable") boolean enable
    ) {
        throwIfNotLocal();
        var existingValue = billyConfiguration.getDocumentConfiguration().isEnabled();
        billyConfiguration.getDocumentConfiguration().setEnabled(enable);
        return Response.ok(existingValue).build();
    }

    private void throwIfNotLocal() {
        if (billyConfiguration.isLocalOrCi()) {
            return;
        }

        throw new ForbiddenException("Not allowed.");
    }

    private void throwIfNotTestTenant() {
        if (tenantService.getCurrentTenant().isTest()) {
            return;
        }

        throw new ForbiddenException("Not allowed.");
    }

    private void setUpBillyAdminPrincipal(String email, String tenantId, String userId) {
        String userIdentifier = generateRandomString("localUser");
        String clientId = generateRandomString("clientId");
        UserPrincipal billyAdminPrincipal = new UserPrincipal(
            userIdentifier,
            clientId,
            Role.BILLY_ADMIN.name(),
            email,
            userId,
            tenantId,
            // TODO: multi-entity - do we need to set all access context here?
            EntityContext.builder().build(),
            false
        );
        CurrentUserProvider.setAuthPrincipal(new BillyAuthPrincipal(billyAdminPrincipal));
    }

    private void createFakeAuthTenantCognitoForTenant(String tenantId) {
        AuthTenantCognito authTenantCognito = new AuthTenantCognito();
        authTenantCognito.setTenantId(tenantId);
        authTenantCognito.setUserPoolId(generateRandomString("userPoolId"));
        authTenantCognito.setClientId(generateRandomString("clientId"));
        authTenantCognito.setDomainName(generateRandomString("domainName"));

        tenantCognitoService.addTenantAuthInfo(authTenantCognito);
    }

    private Tenant createTestTenantObject() {
        String id = AutoGenerate.getNewId();
        String tenantId = AutoGenerate.getNewId();
        String name = generateRandomString("name");
        String email = generateRandomString("email") + "@subskribe.com";
        String phoneNumber = generateRandomString("phoneNumber");
        Tenant tenant = new Tenant(id, tenantId, name, email, phoneNumber, null, false, false, Instant.now(), Instant.now());
        TenantSetting tenantSetting = new TenantSetting(
            null,
            TimeZoneService.getDefaultTimeZone(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            false,
            false,
            false,
            false,
            null
        );
        tenant.setTenantSetting(tenantSetting);
        return tenant;
    }

    private String generateRandomString(String identifier) {
        return String.format(TEST_STRING_FORMAT, identifier, AutoGenerate.getNewId());
    }

    private ApiKeyJwtToken generateApiKeyWithRole(Role role, String tenantIdString, String entityId) {
        String userId = getUserIdForRole(role);
        TenantId tenantId = new TenantId(tenantIdString);
        ApiKeyTokenAndSecret result = apiKeyService.generateNewApiKeyForGivenTenant(
            tenantId,
            Optional.of(role),
            Optional.empty(),
            Optional.empty(),
            Optional.of(userId),
            Optional.of(entityId)
        );
        return result.getJwtToken();
    }

    private String getUserIdForRole(Role role) {
        return switch (role) {
            case SALES -> TEST_USER_ID_SALES;
            case FINANCE -> TEST_USER_ID_FINANCE;
            case ADMIN -> TEST_USER_ID_ADMIN;
            case SALES_MANAGER -> TEST_USER_ID_SALES_MANAGER;
            case ACCOUNTANT -> TEST_USER_ID_ACCOUNTANT;
            case BILLING_CLERK -> TEST_USER_ID_BILLING_CLERK;
            case REVENUE_CLERK -> TEST_USER_ID_REVENUE_CLERK;
            case READ_ONLY -> TEST_USER_ID_READ_ONLY;
            case EXECUTIVE -> TEST_USER_ID_EXECUTIVE;
            case BILLY_ENGINEER -> TEST_USER_ID_BILLY_ENGINEER;
            case BILLY_ADMIN -> TEST_USER_ID_BILLY_ADMIN;
            default -> throw new ServiceFailureException("Role not supported by local admin resource: " + role);
        };
    }
}
