package com.subskribe.billy.resources.json.tenant;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.entity.model.EntityRef;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalSegmentJson;
import com.subskribe.billy.resources.json.usergroup.UserGroupJson;
import com.subskribe.billy.shared.enums.Status;
import com.subskribe.billy.user.model.Role;
import graphql.annotations.annotationTypes.GraphQLConstructor;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.BooleanUtils;
import software.amazon.awssdk.services.cognitoidentityprovider.model.UserStatusType;

// TODO: this class is overloaded - separate into two different classes for REST and GQL
@GraphQLName("User")
public class UserJson implements NotifyingEvent {

    @GraphQLField
    @GraphQLName("id")
    @GraphQLNonNull
    private String id;

    @GraphQLField
    @GraphQLName("displayName")
    @GraphQLNonNull
    private String displayName;

    @GraphQLField
    @GraphQLName("title")
    private String title;

    @GraphQLField
    @GraphQLName("email")
    @GraphQLNonNull
    private String email;

    @GraphQLField
    @GraphQLName("phoneNumber")
    private String phoneNumber;

    @GraphQLField
    @GraphQLName("state")
    @GraphQLNonNull
    private Status state;

    @GraphQLField
    @GraphQLName("role")
    @GraphQLNonNull
    private Role role;

    @GraphQLField
    @GraphQLName("ssoOnly")
    private Boolean ssoOnly;

    @GraphQLField
    @GraphQLName("tenantName")
    private String tenantName;

    @JsonProperty
    @GraphQLField
    @GraphQLName("cognitoUserStatus")
    private UserStatusType cognitoUserStatus;

    @GraphQLField
    @GraphQLName("userGroups")
    private List<UserGroupJson> userGroups = new ArrayList<>();

    @GraphQLField
    @GraphQLName("approvalSegments")
    private List<ApprovalSegmentJson> approvalSegments = new ArrayList<>();

    @GraphQLField
    @GraphQLName("hasAllEntitiesAccess")
    private Boolean hasAllEntitiesAccess;

    @GraphQLField
    @GraphQLName("availableEntities")
    private List<@GraphQLNonNull EntityRef> availableEntities;

    @JsonProperty
    @GraphQLField
    @GraphQLName("entityIds")
    private List<@GraphQLNonNull String> entityIds;

    @JsonProperty
    @GraphQLField
    @GraphQLName("externalId")
    private String externalId;

    public UserJson() {}

    @GraphQLConstructor
    public UserJson(
        @GraphQLName("id") String id,
        @GraphQLName("displayName") @GraphQLNonNull String displayName,
        @GraphQLName("title") String title,
        @GraphQLName("email") @GraphQLNonNull String email,
        @GraphQLName("phoneNumber") String phoneNumber,
        @GraphQLName("state") @GraphQLNonNull Status state,
        @GraphQLName("role") @GraphQLNonNull Role role,
        @GraphQLName("ssoOnly") Boolean ssoOnly,
        @GraphQLName("externalId") String externalId
    ) {
        this.id = id;
        this.displayName = displayName;
        this.title = title;
        this.email = email;
        this.phoneNumber = phoneNumber;
        this.state = state;
        this.role = role;
        this.ssoOnly = ssoOnly;
        this.externalId = externalId;
    }

    @JsonProperty
    public String getId() {
        return id;
    }

    @JsonProperty
    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty
    public String getDisplayName() {
        return displayName;
    }

    @JsonProperty
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    @JsonProperty
    public String getTitle() {
        return title;
    }

    @JsonProperty
    public void setTitle(String title) {
        this.title = title;
    }

    @JsonProperty
    public String getEmail() {
        return email;
    }

    @JsonProperty
    public void setEmail(String email) {
        this.email = email;
    }

    @JsonProperty
    public String getPhoneNumber() {
        return phoneNumber;
    }

    @JsonProperty
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @JsonProperty
    public String getTenantName() {
        return tenantName;
    }

    @JsonProperty
    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    @JsonProperty
    public Status getState() {
        return state;
    }

    @JsonProperty
    public void setState(Status state) {
        this.state = state;
    }

    @JsonProperty
    public Role getRole() {
        return role;
    }

    @JsonProperty
    public void setRole(Role role) {
        this.role = role;
    }

    @JsonProperty
    public boolean getSsoOnly() {
        if (ssoOnly == null && role != null && role.isSsoOnlyByDefault()) {
            return true;
        }
        return BooleanUtils.isTrue(ssoOnly);
    }

    @JsonProperty
    public void setSsoOnly(Boolean ssoOnly) {
        this.ssoOnly = ssoOnly;
    }

    public UserStatusType getCognitoUserStatus() {
        return cognitoUserStatus;
    }

    public void setCognitoUserStatus(UserStatusType cognitoUserStatus) {
        this.cognitoUserStatus = cognitoUserStatus;
    }

    @JsonProperty
    public List<ApprovalSegmentJson> getApprovalSegments() {
        return approvalSegments;
    }

    public void setApprovalSegments(List<ApprovalSegmentJson> approvalSegments) {
        this.approvalSegments = approvalSegments;
    }

    @JsonProperty
    public List<UserGroupJson> getUserGroups() {
        return userGroups;
    }

    public void setUserGroups(List<UserGroupJson> userGroups) {
        this.userGroups = userGroups;
    }

    public Boolean getHasAllEntitiesAccess() {
        return hasAllEntitiesAccess;
    }

    public void setHasAllEntitiesAccess(Boolean hasAllEntitiesAccess) {
        this.hasAllEntitiesAccess = hasAllEntitiesAccess;
    }

    public List<EntityRef> getAvailableEntities() {
        return availableEntities;
    }

    public void setAvailableEntities(List<EntityRef> availableEntities) {
        this.availableEntities = availableEntities;
    }

    public List<String> getEntityIds() {
        return entityIds;
    }

    public void setEntityIds(List<String> entityIds) {
        this.entityIds = entityIds;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    @Override
    public String getEventObjectId() {
        return getId();
    }
}
