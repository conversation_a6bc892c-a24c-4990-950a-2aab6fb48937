package com.subskribe.billy.resources;

import com.subskribe.billy.admin.AdminOperationsService;
import com.subskribe.billy.admin.model.SubscriptionModifiableResponse;
import com.subskribe.billy.admin.model.SubscriptionTypeUpdateInput;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.ChangeOrderService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.resources.json.PaginatedItemsResponse;
import com.subskribe.billy.resources.json.metrics.MetricsJson;
import com.subskribe.billy.resources.json.metrics.MetricsJsonMapper;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.resources.json.subscription.SubscriptionJson;
import com.subskribe.billy.resources.json.subscription.SubscriptionJsonMapper;
import com.subskribe.billy.resources.json.subscription.SubscriptionUpdateJson;
import com.subskribe.billy.resources.json.subscription.UpdateActivationDateRequest;
import com.subskribe.billy.resources.shared.PaginatedResponse;
import com.subskribe.billy.resources.shared.PaginationValidator;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.model.SubscriptionSchedules;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionLifecycleScheduleService;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.time.Instant;
import java.util.List;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.apache.commons.lang.BooleanUtils;
import org.mapstruct.factory.Mappers;

@Path("/subscriptions")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class SubscriptionResource {

    private final SubscriptionService subscriptionService;
    private final SubscriptionJsonMapper subscriptionJsonMapper;
    private final MetricsService metricsService;
    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;
    private final MetricsJsonMapper metricsJsonMapper;
    private final SubscriptionGetService subscriptionGetService;
    private final AdminOperationsService adminOperationsService;
    private final ChangeOrderService changeOrderService;
    private final OrderMapper orderMapper;
    private final SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService;
    private final OrderGetService orderGetService;
    private final OrderService orderService;

    @Inject
    public SubscriptionResource(
        SubscriptionService subscriptionService,
        MetricsService metricsService,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        SubscriptionGetService subscriptionGetService,
        AdminOperationsService adminOperationsService,
        ChangeOrderService changeOrderService,
        SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService,
        OrderGetService orderGetService,
        OrderService orderService
    ) {
        this.subscriptionService = subscriptionService;
        this.metricsService = metricsService;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.subscriptionGetService = subscriptionGetService;
        this.adminOperationsService = adminOperationsService;
        this.changeOrderService = changeOrderService;
        this.subscriptionLifecycleScheduleService = subscriptionLifecycleScheduleService;
        this.orderGetService = orderGetService;
        this.orderService = orderService;
        subscriptionJsonMapper = Mappers.getMapper(SubscriptionJsonMapper.class);
        metricsJsonMapper = Mappers.getMapper(MetricsJsonMapper.class);
        orderMapper = Mappers.getMapper(OrderMapper.class);
    }

    @GET
    @ApiOperation(
        value = "Get paginated subscriptions",
        notes = "returns all Subscriptions in the system in a paginated fashion",
        response = PaginatedSubscriptionsResponse.class,
        tags = { "Subscriptions" }
    )
    @AllowAllRoles
    public Response getSubscriptions(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("limit") @ApiParam(name = "limit", value = "number of items per page") Integer limit,
        @QueryParam("pageToken") @ApiParam(name = "pageToken", value = "pass this to subsequent calls") String pageTokenString,
        @QueryParam("accountId") @ApiParam(
            name = "accountId",
            value = "optionally pass in account Id, only subscriptions for this account will will returned"
        ) String accountId
    ) {
        int limitArg = PaginationValidator.validateLimit(limit);

        PaginatedResponse<Subscription> paginatedResponse = subscriptionGetService.getSubscriptions(accountId, pageTokenString, limitArg);
        PaginatedResponse<SubscriptionJson> subscriptionJsonPaginatedResponse = new PaginatedResponse<>(
            paginatedResponse.getData().stream().map(subscriptionJsonMapper::subscriptionToJson).toList(),
            paginatedResponse.getCount(),
            paginatedResponse.getPageToken()
        );
        return Response.ok(subscriptionJsonPaginatedResponse).build();
    }

    @GET
    @Path("/{id}")
    @ApiOperation(
        value = "Get subscription details",
        notes = "Gets the details of the specified subscription.",
        response = SubscriptionJson.class,
        tags = { "Subscriptions" }
    )
    @AllowAllRoles
    public Response getSubscriptionById(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(name = "id", value = "subscription id", required = true) String id
    ) {
        var subscription = subscriptionGetService.getSubscription(id);
        var subscriptionJson = subscriptionJsonMapper.subscriptionToJson(subscription);

        return Response.ok(subscriptionJson).build();
    }

    @GET
    @Path("/{id}/firstOrder")
    @ApiOperation(
        value = "Get the id of the first order for subscription",
        notes = "The first order for the subscription is the NEW, RENEWAL or RESTRUCTURE order that created the subscription.",
        hidden = true,
        response = OrderJson.class,
        tags = { "Subscriptions" }
    )
    @AllowAllRoles
    public Response getFirstOrderInSubscription(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(name = "id", value = "subscription id", required = true) String id
    ) {
        var order = orderGetService.getFirstOrderInSubscription(id);
        var orderJson = orderMapper.orderToJson(order);
        return Response.ok(orderJson).build();
    }

    @PUT
    @Path("/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Update subscription details", notes = "Updates the details of the specified subscription.", tags = { "Subscriptions" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.SALES_MANAGER, Role.SALES })
    public Response updateSubscription(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String id,
        @NotNull @Valid @ApiParam(value = "subscription details in json", required = true) SubscriptionUpdateJson subscriptionUpdateJson
    ) {
        subscriptionService.updateSubscriptionAttributes(id, subscriptionUpdateJson);
        return Response.ok().build();
    }

    @PUT
    @Path("/{id}/activationDate")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Update subscription activation date",
        notes = "Updates the subscription's activation date.",
        hidden = true,
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.SALES_MANAGER, Role.SALES })
    public Response updateSubscriptionActivationDate(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String id,
        @ApiParam(value = "subscription activation date as seconds since epoch", required = true) Long activationDate
    ) {
        subscriptionService.updateSubscriptionActivationDate(id, null, DateTimeConverter.epochSecondsToInstant(activationDate));
        return Response.ok().build();
    }

    // This endpoint is here as a convenience for some platform where flexibility in sending requests is limited.
    // For instance, Hubspot CRM does not allow sending PUT requests and is not able to use a dynamic URL so IDs cannot be in the path.
    // This endpoint intentionally 1) keeps the path static and 2) uses POST method with values in the request body to account for the most commonly supported HTTP operations.
    @POST
    @Path("/activationDate")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Update subscription activation date",
        notes = "Updates the subscription's activation date using either subscription id or order id. Using the order id will update the corresponding subscription's activation date.",
        hidden = true,
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.SALES_MANAGER, Role.SALES })
    public Response updateActivationDate(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(
            value = "Update subscription activation date by subscription id or order id",
            required = true
        ) UpdateActivationDateRequest request
    ) {
        subscriptionService.updateSubscriptionActivationDate(
            request.getSubscriptionId(),
            request.getOrderId(),
            DateTimeConverter.epochSecondsToInstant(request.getActivationDate())
        );
        return Response.ok().build();
    }

    @DELETE
    @Path("/{id}/{version}")
    @ApiOperation(
        value = "Delete subscription",
        notes = "Deletes the subscription for given subscription Id if invoices have not been generated and revenue has not been recognized",
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response deleteSubscription(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String id,
        @PathParam("version") @ApiParam(value = "subscription version", required = true) int version
    ) {
        adminOperationsService.deleteSubscription(id, version);
        return Response.ok().build();
    }

    @GET
    @Path("/{id}/draftAmendment")
    @ApiOperation(
        value = "Generate draft amendment order",
        notes = "Generate a draft amendment object for the given subscription. The draft amendment is composed of current subscription charges as the default line items.",
        response = OrderJson.class,
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getDraftAmendment(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String id
    ) {
        Order order = changeOrderService.generateDraftAmendment(id);
        return Response.ok(orderMapper.orderToJson(order)).build();
    }

    @GET
    @Path("/{id}/draftRenewal")
    @ApiOperation(
        value = "Generate draft renewal order",
        notes = "Generate a draft renewal order object for the given subscription. The draft renewal is composed of current subscription charges as the default line items.",
        response = OrderJson.class,
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getDraftRenewal(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String id
    ) {
        Order order = orderService.generateRenewalOrder(id);
        return Response.ok(orderMapper.orderToJson(order)).build();
    }

    @PUT
    @Path("/{id}/{version}/revert")
    @ApiOperation(
        value = "Revert subscription",
        notes = "Reverts the subscription for given subscription Id and version to it's previous version. Operation is allowed only if invoices have not been generated and revenue has not been recognized",
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response revertSubscription(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String id,
        @PathParam("version") @ApiParam(value = "subscription version", required = true) int version
    ) {
        adminOperationsService.revertSubscription(id, version);
        return Response.ok().build();
    }

    @GET
    @Path("/{id}/modifiable")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Subscription can be modified", notes = "Returns true if subscription can be deleted.", tags = { "Subscriptions" })
    @AllowRoles({ Role.ADMIN })
    public Response subscriptionModifiable(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String id
    ) {
        SubscriptionModifiableResponse subscriptionModifiableResponse = adminOperationsService.canDeleteSubscription(id);
        return Response.ok(subscriptionModifiableResponse).build();
    }

    @GET
    @Path("/{id}/reversible")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Subscription can be reverted",
        notes = "Returns true if subscription can be reverted to a prior version.",
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response subscriptionReversible(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String id
    ) {
        SubscriptionModifiableResponse subscriptionModifiableResponse = adminOperationsService.canRevertSubscription(id);
        return Response.ok(subscriptionModifiableResponse).build();
    }

    @POST
    @Path("/{id}/renewalOpportunity")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Link renewal opportunity to subscription",
        notes = "Update renewal opportunity CRM id on subscription",
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK, Role.REVENUE_CLERK })
    public Response updateRenewalOpportunity(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String subscriptionId,
        @QueryParam("renewalOpportunityCrmId") @ApiParam(value = "renewal opportunity CRM id", required = true) String renewalOpportunityCrmId
    ) {
        subscriptionService.updateRenewalOpportunity(subscriptionId, renewalOpportunityCrmId);
        return Response.ok().build();
    }

    @GET
    @Path("/{id}/metrics")
    @ApiOperation(
        value = "Get subscription metrics",
        notes = "Returns the metrics associated with the specified subscription. Metrics include ACV, ARR, etc.",
        response = MetricsJson.class,
        tags = { "Subscriptions" }
    )
    @AllowAllRoles
    public Response getSubscriptionMetrics(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "id of the subscription", required = true) String subscriptionId,
        @QueryParam("targetDate") @ApiParam(name = "targetDate", value = "As of date for the metrics. If omitted defaults to now.") Long targetDate,
        @QueryParam("forceRecalculate") @ApiParam(
            name = "forceRecalculate",
            value = "If true, forces recalculation of metrics instead of using cached values."
        ) Boolean forceRecalculate
    ) {
        Instant target = targetDate == null ? Instant.now() : Instant.ofEpochSecond(targetDate);
        Metrics subscriptionMetrics = metricsService.getSubscriptionMetrics(subscriptionId, target, BooleanUtils.isTrue(forceRecalculate));
        MetricsJson json = metricsJsonMapper.metricsToJson(subscriptionMetrics);
        return Response.ok(json).build();
    }

    @GET
    @Path("/{id}/billingPeriods")
    @ApiOperation(
        value = "Get billing periods",
        notes = "Gets the billing periods for the specified subscription.",
        response = Instant.class,
        responseContainer = "List",
        tags = { "Subscriptions" }
    )
    @AllowAllRoles
    public Response getSubscriptionBillingPeriods(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String subscriptionId
    ) {
        List<Instant> billingPeriodStartDates = subscriptionBillingPeriodService.getFutureBillingPeriodStartDates(subscriptionId);
        return Response.ok(billingPeriodStartDates).build();
    }

    @POST
    @Path("/{id}/test-notifications")
    @ApiOperation(
        value = "Send test notifications for subscription",
        notes = "Sends test notifications for the specified subscription to the specified notification target.",
        response = Instant.class,
        responseContainer = "List",
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response sendTestNotificationsForSubscription(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String subscriptionId,
        @QueryParam("notificationTargetId") @ApiParam(
            value = "ID of the notification target to send tests to",
            required = true
        ) String notificationTargetId
    ) {
        subscriptionLifecycleScheduleService.sendTestEventsForSubscription(subscriptionId, notificationTargetId);
        return Response.ok().build();
    }

    @GET
    @Path("/{id}/change-events")
    @ApiOperation(
        value = "Get change events for subscription",
        notes = "Returns the scheduled change events for the specified subscription.",
        response = SubscriptionSchedules.class,
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getAllChangeEventsForSubscription(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "subscription id", required = true) String subscriptionId
    ) {
        SubscriptionSchedules schedulesForSubscription = subscriptionLifecycleScheduleService.getSchedulesForSubscription(subscriptionId);
        return Response.ok(schedulesForSubscription).build();
    }

    @POST
    @Path("/custom-field/backfill-subscription/{subscriptionId}")
    @ApiOperation(value = "Temporary back-fill job", hidden = true)
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN })
    public Response backFillSubscriptionChargeCustomField(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal adminUser,
        @PathParam("subscriptionId") String subscriptionId
    ) {
        int subscriptionChargesUpdated = adminOperationsService.backFillSubscriptionItemCustomFields(subscriptionId);
        return Response.ok(String.format("%d charges updated", subscriptionChargesUpdated)).build();
    }

    @PUT
    @Path("/{subscriptionId}/type")
    @ApiOperation(
        value = "update subscription type",
        notes = "Updates a subscription's type. e.g. NEW -> RENEWAL. When updating to RENEWAL, renewedFromSubscriptionId is required. When updating to RESTRUCTURE, restructuredFromSubscriptionId is required.",
        hidden = true,
        tags = { "Subscriptions" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response updateSubscriptionType(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("subscriptionId") @ApiParam(value = "subscription id", required = true) String subscriptionId,
        @NotNull @Valid @ApiParam(
            value = "update subscription type input params",
            required = true
        ) SubscriptionTypeUpdateInput subscriptionTypeUpdateInput
    ) {
        adminOperationsService.updateSubscriptionType(subscriptionId, subscriptionTypeUpdateInput);
        return Response.ok().build();
    }

    private static class PaginatedSubscriptionsResponse extends PaginatedItemsResponse<SubscriptionJson> {}
}
