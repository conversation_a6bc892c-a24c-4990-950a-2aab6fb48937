package com.subskribe.billy.resources.json.invoicesettlement;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.event.model.NotifyingEvent;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;

@GraphQLName("Refund")
public class RefundDetail implements NotifyingEvent {

    @JsonProperty
    @NotNull
    @GraphQLField
    @GraphQLNonNull
    private String id;

    @JsonProperty
    @NotNull
    @GraphQLField
    @GraphQLNonNull
    private String refundId;

    @JsonProperty
    @GraphQLField
    private String referenceId;

    @JsonProperty
    @NotNull
    @GraphQLField
    @GraphQLNonNull
    private String creditMemoNumber;

    @JsonProperty
    @GraphQLField
    private BigDecimal amount;

    @JsonProperty
    @NotNull
    @GraphQLField
    @GraphQLNonNull
    private Long refundDate;

    @JsonProperty
    @NotNull
    @GraphQLField
    @GraphQLNonNull
    private String paymentId;

    @JsonProperty
    @GraphQLField
    private String paymentMethodType;

    @JsonProperty
    @GraphQLField
    private String createdBy;

    @JsonProperty
    @GraphQLField
    private String notes;

    @JsonProperty
    @NotNull
    @GraphQLField
    @GraphQLNonNull
    private String currency;

    @JsonProperty
    @GraphQLField
    private Long createdOn;

    @JsonProperty
    @GraphQLField
    private Long updatedOn;

    public RefundDetail() {}

    public RefundDetail(
        @GraphQLName("id") String id,
        @GraphQLName("refundId") String refundId,
        @GraphQLName("referenceId") String referenceId,
        @GraphQLName("creditMemoNumber") String creditMemoNumber,
        @GraphQLName("amount") BigDecimal amount,
        @GraphQLName("refundDate") Long refundDate,
        @GraphQLName("paymentId") String paymentId,
        @GraphQLName("paymentMethodType") String paymentMethodType,
        @GraphQLName("createdBy") String createdBy,
        @GraphQLName("notes") String notes,
        @GraphQLName("currency") String currency,
        @GraphQLName("createdOn") Long createdOn,
        @GraphQLName("updatedOn") Long updatedOn
    ) {
        this.id = id;
        this.refundId = refundId;
        this.referenceId = referenceId;
        this.creditMemoNumber = creditMemoNumber;
        this.amount = amount;
        this.paymentId = paymentId;
        this.paymentMethodType = paymentMethodType;
        this.createdBy = createdBy;
        this.notes = notes;
        this.currency = currency;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.refundDate = refundDate;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getCreditMemoNumber() {
        return creditMemoNumber;
    }

    public void setCreditMemoNumber(String creditMemoNumber) {
        this.creditMemoNumber = creditMemoNumber;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getRefundDate() {
        return refundDate;
    }

    public void setRefundDate(Long refundDate) {
        this.refundDate = refundDate;
    }

    public String getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }

    public String getPaymentMethodType() {
        return paymentMethodType;
    }

    public void setPaymentMethodType(String paymentMethodType) {
        this.paymentMethodType = paymentMethodType;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Long getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Long createdOn) {
        this.createdOn = createdOn;
    }

    public Long getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Long updatedOn) {
        this.updatedOn = updatedOn;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @Override
    public String getEventObjectId() {
        return getId();
    }
}
