package com.subskribe.billy.resources.json.metrics;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.resources.json.shared.TimeSeriesAmountJson;
import com.subskribe.billy.shared.pecuniary.Numbers;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import java.util.List;

@GraphQLName("AccountMetrics")
public class MetricsJson {

    @JsonProperty
    @GraphQLField
    @GraphQLName("tcv")
    @GraphQLNonNull
    private BigDecimal tcv;

    @JsonProperty
    @GraphQLField
    @GraphQLName("recurringTotal")
    private BigDecimal recurringTotal;

    @JsonProperty
    @GraphQLField
    @GraphQLName("nonRecurringTotal")
    private BigDecimal nonRecurringTotal;

    @JsonProperty
    @GraphQLField
    @GraphQLName("arr")
    @GraphQLNonNull
    private BigDecimal arr;

    @JsonProperty
    @GraphQLField
    @GraphQLName("entryArr")
    private BigDecimal entryArr;

    @JsonProperty
    @GraphQLField
    @GraphQLName("exitArr")
    private BigDecimal exitArr;

    @JsonProperty
    @GraphQLField
    @GraphQLName("averageArr")
    private BigDecimal averageArr;

    @JsonProperty
    @GraphQLField
    @GraphQLName("arrTrend")
    @GraphQLNonNull
    private List<TimeSeriesAmountJson> arrTrend;

    @JsonProperty
    @GraphQLField
    @GraphQLName("deltaTcv")
    @GraphQLNonNull
    private BigDecimal deltaTcv;

    @JsonProperty
    @GraphQLField
    @GraphQLName("deltaArr")
    @GraphQLNonNull
    private BigDecimal deltaArr;

    public MetricsJson() {}

    public BigDecimal getTcv() {
        return tcv;
    }

    public void setTcv(BigDecimal tcv) {
        this.tcv = Numbers.makeCurrencyScale(tcv);
    }

    public BigDecimal getRecurringTotal() {
        return recurringTotal;
    }

    public void setRecurringTotal(BigDecimal recurringTotal) {
        this.recurringTotal = recurringTotal;
    }

    public BigDecimal getNonRecurringTotal() {
        return nonRecurringTotal;
    }

    public void setNonRecurringTotal(BigDecimal nonRecurringTotal) {
        this.nonRecurringTotal = nonRecurringTotal;
    }

    public BigDecimal getArr() {
        return arr;
    }

    public void setArr(BigDecimal arr) {
        this.arr = Numbers.makeCurrencyScale(arr);
    }

    public BigDecimal getEntryArr() {
        return entryArr;
    }

    public void setEntryArr(BigDecimal entryArr) {
        this.entryArr = entryArr;
    }

    public BigDecimal getExitArr() {
        return exitArr;
    }

    public void setExitArr(BigDecimal exitArr) {
        this.exitArr = Numbers.makeCurrencyScale(exitArr);
    }

    public List<TimeSeriesAmountJson> getArrTrend() {
        return arrTrend;
    }

    public void setArrTrend(List<TimeSeriesAmountJson> arrTrend) {
        this.arrTrend = arrTrend;
    }

    public BigDecimal getAverageArr() {
        return averageArr;
    }

    public void setAverageArr(BigDecimal averageArr) {
        this.averageArr = Numbers.makeCurrencyScale(averageArr);
    }

    public BigDecimal getDeltaTcv() {
        return deltaTcv;
    }

    public void setDeltaTcv(BigDecimal deltaTcv) {
        this.deltaTcv = deltaTcv;
    }

    public BigDecimal getDeltaArr() {
        return deltaArr;
    }

    public void setDeltaArr(BigDecimal deltaArr) {
        this.deltaArr = deltaArr;
    }
}
