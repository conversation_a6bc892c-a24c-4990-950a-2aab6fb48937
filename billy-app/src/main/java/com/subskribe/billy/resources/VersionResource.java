package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.infra.SystemDataProvider;
import com.subskribe.billy.resources.json.version.VersionJson;
import io.swagger.annotations.Api;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import javax.inject.Inject;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

@Path("/version")
@Produces(MediaType.APPLICATION_JSON)
@Api(hidden = true)
public class VersionResource {

    private final SystemDataProvider systemDataProvider;
    private static final String VERSION_FILE = "version.txt";
    private static final String RELEASE_NAME_ENVIRONMENT_VARIABLE = "RELEASE_NAME";
    private static final String RELEASED_FROM_ENVIRONMENT_VARIABLE = "RELEASED_FROM";
    private static final String UNKNOWN = "undefined";

    @Inject
    public VersionResource(SystemDataProvider systemDataProvider) {
        this.systemDataProvider = systemDataProvider;
    }

    @AllowUnauthenticated(cascade = true)
    @GET
    public Response getVersion() {
        var versionJson = new VersionJson(
            getBuildSha(),
            systemDataProvider.getValueOfEnvironmentVariable(RELEASE_NAME_ENVIRONMENT_VARIABLE).orElse(UNKNOWN),
            systemDataProvider.getValueOfEnvironmentVariable(RELEASED_FROM_ENVIRONMENT_VARIABLE).orElse(UNKNOWN)
        );
        return Response.ok(versionJson).build();
    }

    private String getBuildSha() {
        try (var stream = getClass().getClassLoader().getResourceAsStream(VERSION_FILE)) {
            if (stream == null) {
                throw new ServiceFailureException(String.format("%s not found", VERSION_FILE));
            }
            String fileContent = IOUtils.toString(stream, StandardCharsets.UTF_8);
            return StringUtils.trim(fileContent);
        } catch (IOException e) {
            throw new ServiceFailureException(String.format("Could not read build information from %s", VERSION_FILE));
        }
    }
}
