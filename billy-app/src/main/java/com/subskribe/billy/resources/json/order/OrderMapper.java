package com.subskribe.billy.resources.json.order;

import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.resources.json.opportunity.OpportunityInput;
import com.subskribe.billy.resources.json.opportunity.OpportunityJson;
import com.subskribe.billy.resources.json.shared.TenantDiscountLineItemJson;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.shared.mapper.OrderObjectMapper;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Currency;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.control.DeepClone;

@Mapper(mappingControl = DeepClone.class)
public abstract class OrderMapper implements OrderObjectMapper, CustomFieldAPIMapper {

    @Mapping(target = "metrics", ignore = true)
    @Mapping(target = "lineItemsHavingBaseSubscriptionCharge", ignore = true)
    @Mapping(target = "lineItemsWithEmptyBaseSubscriptionCharge", ignore = true)
    @Mapping(target = "creditableAmounts", ignore = true)
    public abstract Order duplicateOrder(Order order);

    @Mapping(target = "metrics", ignore = true)
    public abstract OrderLineItem duplicateOrderLineItem(OrderLineItem orderLineItem);

    public List<OrderJson> ordersToJson(List<Order> orders) {
        if (orders == null) {
            return null;
        }

        List<OrderJson> ordersJson = new ArrayList<>();
        orders.forEach(o -> ordersJson.add(orderToJson(o)));
        return ordersJson;
    }

    public List<Order> getUnmodifiableList(List<Order> orders) {
        if (orders == null) {
            return null;
        }
        return Collections.unmodifiableList(orders);
    }

    @Mapping(source = "externalSubscriptionId", target = "subscriptionId")
    @Mapping(target = "lineItems", expression = "java(orderLineItemsToJson(order.getLineItems()))")
    @Mapping(target = "lineItemsNetEffect", expression = "java(orderLineItemListToRawJson(order.getLineItemsNetEffect()))")
    @Mapping(source = "orderId", target = "id")
    @Mapping(target = "opportunity", source = "opportunity")
    public abstract OrderJson orderToJson(Order order);

    @Mapping(source = "subscriptionId", target = "externalSubscriptionId")
    @Mapping(target = "subscriptionUuid", ignore = true)
    @Mapping(source = "id", target = "orderId")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "orderProcessingMetadata", ignore = true)
    @Mapping(source = "currency", target = "currency", qualifiedByName = "stringToCurrencyStrictConversion")
    @Mapping(
        source = "customBillingSchedule",
        target = "customBillingSchedule",
        qualifiedByName = "customBillingScheduleInputToCustomBillingSchedule"
    )
    @Mapping(target = "opportunity", source = "opportunityInput")
    public abstract Order jsonToOrder(OrderRequestJson orderRequestJson);

    public abstract Opportunity toOpportunity(OpportunityInput input);

    public abstract OpportunityJson toOpportunityJson(Opportunity opportunity);

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    // Concrete method to handle Optional<Opportunity>
    public OpportunityJson map(Optional<Opportunity> opportunity) {
        return opportunity.map(this::toOpportunityJson).orElse(null);
    }

    @Named("stringToCurrencyStrictConversion")
    public Currency stringToCurrencyStrictConversion(String currency) {
        return currency == null ? null : Currency.getInstance(currency);
    }

    public List<OrderLineItemJson> orderLineItemsToJson(List<OrderLineItem> orderLineItemList) {
        List<OrderLineItemJson> orderLineItemJsonList = new ArrayList<>();
        var existingChargeIdMap = orderLineItemList
            .stream()
            .filter(ol -> StringUtils.isNotBlank(ol.getBaseExternalSubscriptionChargeId()))
            .collect(Collectors.groupingBy(OrderLineItem::getBaseExternalSubscriptionChargeId));
        existingChargeIdMap.keySet().forEach(id -> mapSameChargeOrderLinesToJson(orderLineItemJsonList, existingChargeIdMap.get(id)));

        var sChargeIdMap = orderLineItemList
            .stream()
            .filter(ol -> StringUtils.isBlank(ol.getBaseExternalSubscriptionChargeId()))
            .collect(Collectors.groupingBy(OrderLineItem::getChargeId));
        sChargeIdMap.keySet().forEach(id -> mapSameChargeOrderLinesToJson(orderLineItemJsonList, sChargeIdMap.get(id)));

        orderLineItemJsonList.sort(Comparator.comparing(OrderLineItemJson::getRank));
        return orderLineItemJsonList;
    }

    private void mapSameChargeOrderLinesToJson(List<OrderLineItemJson> orderLinesJson, List<OrderLineItem> orderLineItems) {
        AtomicBoolean found = new AtomicBoolean(false);
        orderLineItems.forEach(li -> {
            if (li.getQuantity() > 0 || li.getAction() == ActionType.ADD) {
                found.set(true);
                var orderLineJson = createOrderLineItemJson(li, li.getQuantity());
                updateAmountsForLineItem(orderLineItems, orderLineJson);
                orderLinesJson.add(orderLineJson);
            }
        });

        if (!found.get()) {
            var firstOrder = orderLineItems.get(0);
            orderLinesJson.add(createOrderLineItemJson(firstOrder, 0));
        }
    }

    private void updateAmountsForLineItem(List<OrderLineItem> orderLineItems, OrderLineItemJson lineItemJson) {
        if (lineItemJson.getAction() != ActionType.UPDATE && lineItemJson.getAction() != ActionType.NONE) {
            return;
        }

        // Current restriction is for the updates, there will be only a single positive update line per charge
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal listAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal annualizedAmount = BigDecimal.ZERO;
        BigDecimal taxEstimate = null;
        for (OrderLineItem ol : orderLineItems) {
            amount = amount.add(ol.getAmount());
            listAmount = listAmount.add(ol.getListAmount());
            discountAmount = discountAmount.add(ol.getDiscountAmount());
            if (ol.getAnnualizedAmount() != null) {
                annualizedAmount = annualizedAmount.add(ol.getAnnualizedAmount());
            }
            taxEstimate = ol.getTaxEstimate() != null
                ? ol.getTaxEstimate().add(Optional.ofNullable(taxEstimate).orElse(BigDecimal.ZERO))
                : taxEstimate;
        }

        lineItemJson.setAmount(amount);
        lineItemJson.setListAmount(listAmount);
        lineItemJson.setDiscountAmount(discountAmount);
        if (orderLineItems.stream().anyMatch(ol -> ol.getAnnualizedAmount() != null)) {
            // set merged annualized amount only if any of the line items has annualized amount
            lineItemJson.setAnnualizedAmount(annualizedAmount);
        }
        lineItemJson.setTaxEstimate(taxEstimate);
    }

    private OrderLineItemJson createOrderLineItemJson(OrderLineItem orderLineItem, long quantity) {
        var orderLineItemJson = new OrderLineItemJson();
        var subscriptionChargeId = orderLineItem.getExternalSubscriptionChargeId() == null
            ? orderLineItem.getBaseExternalSubscriptionChargeId()
            : orderLineItem.getExternalSubscriptionChargeId();

        orderLineItemJson.setId(orderLineItem.getOrderLineId());
        orderLineItemJson.setDryRunItem(orderLineItem.isDryRunItem());
        orderLineItemJson.setPlanId(orderLineItem.getPlanId());
        orderLineItemJson.setChargeId(orderLineItem.getChargeId());
        orderLineItemJson.setAction(orderLineItem.getAction());
        orderLineItemJson.setSubscriptionChargeId(subscriptionChargeId);
        orderLineItemJson.setCurrencyConversionRateId(uuidToString(orderLineItem.getCurrencyConversionRateId()));
        orderLineItemJson.setSubscriptionChargeGroupId(orderLineItem.getSubscriptionChargeGroupId());
        orderLineItemJson.setQuantity(quantity);
        orderLineItemJson.setIsRamp(orderLineItem.getIsRamp());
        orderLineItemJson.setDiscounts(discountsToDiscountDetailsJson(orderLineItem.getDiscounts()));
        orderLineItemJson.setAmount(orderLineItem.getAmount());
        orderLineItemJson.setListAmount(orderLineItem.getListAmount());
        orderLineItemJson.setSellUnitPrice(orderLineItem.getSellUnitPrice());
        orderLineItemJson.setListUnitPrice(orderLineItem.getListUnitPrice());
        orderLineItemJson.setDiscountAmount(orderLineItem.getDiscountAmount());
        orderLineItemJson.setEffectiveDate(instantToLong(orderLineItem.getEffectiveDate()));
        orderLineItemJson.setEndDate(instantToLong(orderLineItem.getEndDate()));
        orderLineItemJson.setRank(orderLineItem.getRank());
        orderLineItemJson.setPredefinedDiscounts(orderDiscountLineItemsToJson(orderLineItem.getPredefinedDiscounts()));
        orderLineItemJson.setPricingOverride(pricingOverrideToJson(orderLineItem.getPricingOverride()));
        orderLineItemJson.setListPriceOverrideRatio(orderLineItem.getListPriceOverrideRatio());
        orderLineItemJson.setListUnitPriceBeforeOverride(orderLineItem.getListUnitPriceBeforeOverride());
        orderLineItemJson.setListAmountBeforeOverride(orderLineItem.getListAmountBeforeOverride());
        orderLineItemJson.setTaxEstimate(orderLineItem.getTaxEstimate());
        orderLineItemJson.setAttributeReferences(orderLineItem.getAttributeReferences());
        orderLineItemJson.setCustomFields(orderLineItem.getCustomFields());
        orderLineItemJson.setAnnualizedAmount(orderLineItem.getAnnualizedAmount());
        orderLineItemJson.setArrOverride(orderLineItem.getArrOverride());
        orderLineItemJson.setReplacedPlanId(orderLineItem.getReplacedPlanId());

        return orderLineItemJson;
    }

    @Mapping(source = "subscriptionChargeId", target = "baseExternalSubscriptionChargeId")
    @Mapping(target = "subscriptionChargeId", ignore = true)
    @Mapping(source = "id", target = "orderLineId")
    @Mapping(target = "id", ignore = true)
    public abstract OrderLineItem jsonToOrderLineItem(OrderLineItemRequestJson orderLineItemRequestJson);

    public List<OrderLineItemJson> orderLineItemListToRawJson(List<OrderLineItem> orderLineItemList) {
        if (orderLineItemList == null) {
            return null;
        }

        List<OrderLineItemJson> orderLinesJson = new ArrayList<>();
        orderLineItemList.forEach(ol -> orderLinesJson.add(orderLineItemToRawJson(ol)));
        return orderLinesJson;
    }

    @Mapping(source = "orderLineItem.orderLineId", target = "id")
    @Mapping(source = "orderLineItem.externalSubscriptionChargeId", target = "subscriptionChargeId")
    public abstract OrderLineItemJson orderLineItemToRawJson(OrderLineItem orderLineItem);

    public abstract List<TenantDiscountLineItemJson> orderDiscountLineItemsToJson(List<TenantDiscountLineItem> orderDiscountLineItem);
}
