package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.order.services.OrderDocumentService;
import com.subskribe.billy.resources.shared.FileResponseBuilder;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomAIGeneratedContent;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomActivityLog;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomEngagementSession;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomFile;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomFileCategory;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomShareLinkAccess;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomTheme;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWidget;
import com.subskribe.billy.salesroom.intelligent.model.request.IntelligentSalesRoomCreateWidgetRequest;
import com.subskribe.billy.salesroom.intelligent.model.request.IntelligentSalesRoomExtractThemeRequest;
import com.subskribe.billy.salesroom.intelligent.model.request.IntelligentSalesRoomReorderWidgetsRequest;
import com.subskribe.billy.salesroom.intelligent.model.request.IntelligentSalesRoomShareLinkAccessRequest;
import com.subskribe.billy.salesroom.intelligent.model.request.IntelligentSalesRoomTrackActivityRequest;
import com.subskribe.billy.salesroom.intelligent.model.request.IntelligentSalesRoomTrackTabVisitRequest;
import com.subskribe.billy.salesroom.intelligent.model.request.IntelligentSalesRoomUpdateAIContentRequest;
import com.subskribe.billy.salesroom.intelligent.model.request.IntelligentSalesRoomUpdateCustomerInfoRequest;
import com.subskribe.billy.salesroom.intelligent.model.request.IntelligentSalesRoomUpdateThemeRequest;
import com.subskribe.billy.salesroom.intelligent.model.request.IntelligentSalesRoomUpdateWidgetRequest;
import com.subskribe.billy.salesroom.intelligent.model.response.IntelligentSalesRoomEngagementSummaryResponse;
import com.subskribe.billy.salesroom.intelligent.model.response.IntelligentSalesRoomOrderMetadataResponse;
import com.subskribe.billy.salesroom.intelligent.model.response.IntelligentSalesRoomOverviewResponse;
import com.subskribe.billy.salesroom.intelligent.service.IntelligentSalesRoomS3Service;
import com.subskribe.billy.salesroom.intelligent.service.IntelligentSalesRoomService;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.Example;
import io.swagger.annotations.ExampleProperty;
import java.io.InputStream;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.glassfish.jersey.media.multipart.FormDataParam;

@Path("/intelligent-sales-rooms")
@Produces(MediaType.APPLICATION_JSON)
@Api(tags = { "Intelligent Sales Room" })
public class IntelligentSalesRoomResource {

    private final IntelligentSalesRoomService intelligentSalesRoomService;

    private final TenantIdProvider tenantIdProvider;

    private final IntelligentSalesRoomS3Service intelligentSalesRoomS3Service;

    private final OrderDocumentService orderDocumentService;

    @Inject
    public IntelligentSalesRoomResource(
        IntelligentSalesRoomService intelligentSalesRoomService,
        TenantIdProvider tenantIdProvider,
        IntelligentSalesRoomS3Service intelligentSalesRoomS3Service,
        OrderDocumentService orderDocumentService
    ) {
        this.intelligentSalesRoomService = intelligentSalesRoomService;
        this.tenantIdProvider = tenantIdProvider;
        this.intelligentSalesRoomS3Service = intelligentSalesRoomS3Service;
        this.orderDocumentService = orderDocumentService;
    }

    @POST
    @ApiOperation(
        value = "Create a new intelligent sales room",
        notes = "Creates a new sales room for the specified order",
        response = IntelligentSalesRoomOverviewResponse.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response createSalesRoom(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @QueryParam("orderId") @NotNull String orderId) {
        IntelligentSalesRoomOverviewResponse response = intelligentSalesRoomService.createSalesRoom(orderId);
        return Response.ok(response).build();
    }

    @GET
    @Path("/by-order/{orderId}")
    @ApiOperation(
        value = "Get sales room by order ID",
        notes = "Returns the sales room for the specified order, creating one if it doesn't exist",
        response = IntelligentSalesRoomOverviewResponse.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response getSalesRoomByOrderId(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @PathParam("orderId") String orderId) {
        IntelligentSalesRoomOverviewResponse response = intelligentSalesRoomService.getSalesRoomByOrderId(orderId);
        return Response.ok(response).build();
    }

    @GET
    @Path("/{salesRoomId}")
    @ApiOperation(
        value = "Get sales room by ID",
        notes = "Returns the sales room details",
        response = IntelligentSalesRoomOverviewResponse.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response getSalesRoomById(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @PathParam("salesRoomId") String salesRoomId) {
        IntelligentSalesRoomOverviewResponse response = intelligentSalesRoomService.getSalesRoomById(salesRoomId);
        return Response.ok(response).build();
    }

    @GET
    @Path("/{salesRoomId}/widgets")
    @ApiOperation(
        value = "Get all widgets for a sales room",
        notes = "Returns all widgets ordered by sort order",
        response = IntelligentSalesRoomWidget.class,
        responseContainer = "List",
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response getWidgets(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @PathParam("salesRoomId") String salesRoomId) {
        List<IntelligentSalesRoomWidget> widgets = intelligentSalesRoomService.getWidgetsBySalesRoomId(salesRoomId);
        return Response.ok(widgets).build();
    }

    @POST
    @Path("/{salesRoomId}/widgets")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Create a new widget",
        notes = """
        Creates a new user-generated widget with type-specific content structure.

        Content format by widget type:
        - RICH_TEXT: {"html": "HTML content"}
        - AI_GENERATED_TEXT: {} (empty object - content is generated by calling the AI generation endpoint after widget creation)
        - MEDIA_CONTENT: {"type": "YOUTUBE_VIDEO|LINK|FILE_UPLOAD|GOOGLE_WORKSPACE|PDF_UPLOAD", "url": "...", "fileId": "...", "title": "..."}
        - USER_PDF: {"fileId": "uploaded-file-uuid"}

        Media content types:
        - YOUTUBE_VIDEO: YouTube video URLs
        - LINK: Generic web links
        - FILE_UPLOAD: References uploaded files via fileId
        - GOOGLE_WORKSPACE: Google Docs/Sheets/Slides URLs
        - PDF_UPLOAD: References uploaded PDF files via fileId

        AI Generated content structure after generation:
        - type: "AI_GENERATED_TEXT"
        - aiContentId: UUID of the generated content record
        - displayContent: The actual generated text content
        """,
        response = IntelligentSalesRoomWidget.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response createWidget(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @NotNull @Valid @ApiParam(
            value = "Widget creation request with type-specific content format",
            required = true,
            examples = @Example(
                value = {
                    @ExampleProperty(
                        mediaType = "application/json",
                        value = """
                        {
                          "type": "RICH_TEXT",
                          "name": "Executive Summary",
                          "content": "{\\"html\\": \\"<h2>Executive Summary</h2><p>This proposal outlines our comprehensive solution.</p>\\"}"
                        }
                        """
                    ),
                    @ExampleProperty(
                        mediaType = "application/json",
                        value = """
                        {
                          "type": "MEDIA_CONTENT",
                          "name": "Product Demo",
                          "content": "{\\"type\\": \\"YOUTUBE_VIDEO\\", \\"url\\": \\"https://www.youtube.com/watch?v=example\\", \\"title\\": \\"Product Demo\\"}"
                        }
                        """
                    ),
                    @ExampleProperty(
                        mediaType = "application/json",
                        value = """
                        {
                          "type": "MEDIA_CONTENT",
                          "name": "Company Presentation",
                          "content": "{\\"type\\": \\"FILE_UPLOAD\\", \\"fileId\\": \\"file-uuid-123\\", \\"title\\": \\"Company Presentation\\"}"
                        }
                        """
                    ),
                    @ExampleProperty(
                        mediaType = "application/json",
                        value = """
                        {
                          "type": "MEDIA_CONTENT",
                          "name": "Google Doc",
                          "content": "{\\"type\\": \\"GOOGLE_WORKSPACE\\", \\"url\\": \\"https://docs.google.com/document/d/example\\", \\"title\\": \\"Requirements Doc\\"}"
                        }
                        """
                    ),
                    @ExampleProperty(
                        mediaType = "application/json",
                        value = """
                        {
                          "type": "AI_GENERATED_TEXT",
                          "name": "Smart Introduction",
                          "content": "{}"
                        }
                        """
                    ),
                }
            )
        ) IntelligentSalesRoomCreateWidgetRequest request
    ) {
        IntelligentSalesRoomWidget widget = intelligentSalesRoomService.createUserWidget(
            salesRoomId,
            request.getType(),
            request.getName(),
            request.getContent()
        );
        return Response.ok(widget).build();
    }

    @PUT
    @Path("/{salesRoomId}/widgets/{widgetId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Update a widget",
        notes = "Updates widget name and content",
        response = IntelligentSalesRoomWidget.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response updateWidget(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @PathParam("widgetId") String widgetId,
        @NotNull @Valid @ApiParam(
            value = "Widget update request with type-specific content format",
            required = true,
            examples = @Example(
                value = {
                    @ExampleProperty(
                        mediaType = "application/json",
                        value = """
                        {
                          "name": "Updated Executive Summary",
                          "content": "{\\"html\\": \\"<h2>Updated Summary</h2><p>This content has been revised.</p>\\"}"
                        }
                        """
                    ),
                    @ExampleProperty(
                        mediaType = "application/json",
                        value = """
                        {
                          "name": "Updated Product Demo",
                          "content": "{\\"type\\": \\"YOUTUBE_VIDEO\\", \\"url\\": \\"https://www.youtube.com/watch?v=updated\\", \\"title\\": \\"Updated Demo\\"}"
                        }
                        """
                    ),
                    @ExampleProperty(
                        mediaType = "application/json",
                        value = """
                        {
                          "name": "Updated AI Introduction",
                          "content": "{\\"type\\": \\"AI_GENERATED_TEXT\\", \\"aiContentId\\": \\"content-uuid-123\\", \\"displayContent\\": \\"This is the updated AI generated content...\\"}"
                        }
                        """
                    ),
                }
            )
        ) IntelligentSalesRoomUpdateWidgetRequest request
    ) {
        IntelligentSalesRoomWidget widget = intelligentSalesRoomService.updateWidget(widgetId, request.getName(), request.getContent());
        return Response.ok(widget).build();
    }

    @DELETE
    @Path("/{salesRoomId}/widgets/{widgetId}")
    @ApiOperation(value = "Delete a widget", notes = "Deletes a user-generated widget", tags = { "Intelligent Sales Room" })
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response deleteWidget(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @PathParam("widgetId") String widgetId
    ) {
        intelligentSalesRoomService.deleteWidget(widgetId);
        return Response.ok().build();
    }

    @PUT
    @Path("/{salesRoomId}/widgets/reorder")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Reorder widgets", notes = "Updates the sort order of all widgets", tags = { "Intelligent Sales Room" })
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response reorderWidgets(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @NotNull @Valid IntelligentSalesRoomReorderWidgetsRequest request
    ) {
        intelligentSalesRoomService.reorderWidgets(salesRoomId, request.getOrderedWidgetIds());
        return Response.ok().build();
    }

    @GET
    @Path("/{salesRoomId}/theme")
    @ApiOperation(
        value = "Get sales room theme",
        notes = "Returns the current theme settings",
        response = IntelligentSalesRoomTheme.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response getTheme(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @PathParam("salesRoomId") String salesRoomId) {
        Optional<IntelligentSalesRoomTheme> theme = intelligentSalesRoomService.getThemeBySalesRoomId(salesRoomId);
        if (theme.isPresent()) {
            return Response.ok(theme.get()).build();
        } else {
            return Response.ok().build();
        }
    }

    @PUT
    @Path("/{salesRoomId}/theme")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Update theme manually",
        notes = "Updates theme with user-provided values",
        response = IntelligentSalesRoomTheme.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response updateTheme(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @NotNull @Valid IntelligentSalesRoomUpdateThemeRequest request
    ) {
        IntelligentSalesRoomTheme theme = intelligentSalesRoomService.updateTheme(
            salesRoomId,
            request.getWebsiteUrl(),
            request.getLogoUrl(),
            request.getPrimaryColor(),
            request.getSecondaryColor(),
            request.getTemplate()
        );
        return Response.ok(theme).build();
    }

    @POST
    @Path("/{salesRoomId}/theme/extract")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Extract theme from website",
        notes = "Automatically extracts branding from website using AI",
        response = IntelligentSalesRoomTheme.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response extractTheme(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @NotNull @Valid IntelligentSalesRoomExtractThemeRequest request
    ) {
        IntelligentSalesRoomTheme theme = intelligentSalesRoomService.extractThemeFromWebsite(salesRoomId, request.getWebsiteUrl());
        return Response.ok(theme).build();
    }

    @GET
    @Path("/{salesRoomId}/files")
    @ApiOperation(
        value = "Get all files for a sales room",
        notes = "Returns all uploaded files",
        response = IntelligentSalesRoomFile.class,
        responseContainer = "List",
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response getFiles(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @QueryParam("category") IntelligentSalesRoomFileCategory category
    ) {
        List<IntelligentSalesRoomFile> files;
        if (category != null) {
            files = intelligentSalesRoomService.getFilesByCategory(salesRoomId, category);
        } else {
            files = intelligentSalesRoomService.getFilesBySalesRoomId(salesRoomId);
        }
        return Response.ok(files).build();
    }

    @POST
    @Path("/{salesRoomId}/files")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiOperation(
        value = "Upload a file",
        notes = "Uploads a file to the sales room",
        response = IntelligentSalesRoomFile.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response uploadFile(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @FormDataParam("file") InputStream fileStream,
        @FormDataParam("originalFileName") String originalFileName,
        @FormDataParam("fileSize") long fileSize,
        @FormDataParam("contentType") String contentType,
        @FormDataParam("category") IntelligentSalesRoomFileCategory category
    ) {
        IntelligentSalesRoomFile file = intelligentSalesRoomService.uploadFile(
            salesRoomId,
            originalFileName,
            fileStream,
            fileSize,
            contentType,
            category
        );
        return Response.ok(file).build();
    }

    @DELETE
    @Path("/{salesRoomId}/files/{fileId}")
    @ApiOperation(value = "Delete a file", notes = "Deletes an uploaded file", tags = { "Intelligent Sales Room" })
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response deleteFile(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @PathParam("fileId") String fileId
    ) {
        intelligentSalesRoomService.deleteFile(fileId);
        return Response.ok().build();
    }

    @GET
    @Path("/{salesRoomId}/files/{fileId}/download")
    @ApiOperation(value = "Get file download URL", notes = "Returns a presigned URL for file download", tags = { "Intelligent Sales Room" })
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response getFileDownloadUrl(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @PathParam("fileId") String fileId,
        @QueryParam("expirationMinutes") @ApiParam(value = "URL expiration in minutes", defaultValue = "60") Integer expirationMinutes
    ) {
        Duration expiration = Duration.ofMinutes(expirationMinutes != null ? expirationMinutes : 60);
        String downloadUrl = intelligentSalesRoomService.generateFileDownloadUrl(fileId, expiration);
        return Response.ok().entity("{\"downloadUrl\":\"" + downloadUrl + "\"}").build();
    }

    @POST
    @Path("/{salesRoomId}/widgets/{widgetId}/ai-content")
    @ApiOperation(
        value = "Generate AI content for widget",
        notes = "Generates AI content based on order context",
        response = IntelligentSalesRoomAIGeneratedContent.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response generateAIContent(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @PathParam("widgetId") String widgetId
    ) {
        IntelligentSalesRoomAIGeneratedContent content = intelligentSalesRoomService.generateAIContent(salesRoomId, widgetId);
        return Response.ok(content).build();
    }

    @PUT
    @Path("/{salesRoomId}/ai-content/{contentId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Update AI generated content",
        notes = "Updates AI content with user edits",
        response = IntelligentSalesRoomAIGeneratedContent.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response updateAIContent(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @PathParam("contentId") String contentId,
        @NotNull @Valid IntelligentSalesRoomUpdateAIContentRequest request
    ) {
        IntelligentSalesRoomAIGeneratedContent content = intelligentSalesRoomService.updateAIContent(contentId, request.getEditedContent());
        return Response.ok(content).build();
    }

    @POST
    @Path("/{salesRoomId}/activate")
    @ApiOperation(value = "Activate sales room", notes = "Moves sales room from READY_TO_SHARE to ACTIVE status", tags = { "Intelligent Sales Room" })
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response activateSalesRoom(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @PathParam("salesRoomId") String salesRoomId) {
        intelligentSalesRoomService.activateSalesRoom(salesRoomId);
        return Response.ok().build();
    }

    @POST
    @Path("/{salesRoomId}/retract")
    @ApiOperation(value = "Retract sales room", notes = "Moves sales room from ACTIVE to READY_TO_SHARE status", tags = { "Intelligent Sales Room" })
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response retractSalesRoom(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @PathParam("salesRoomId") String salesRoomId) {
        intelligentSalesRoomService.retractSalesRoom(salesRoomId);
        return Response.ok().build();
    }

    @POST
    @Path("/{salesRoomId}/accept")
    @ApiOperation(value = "Accept sales room proposal", notes = "Moves sales room to ACCEPTED status", tags = { "Intelligent Sales Room" })
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response acceptSalesRoom(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @PathParam("salesRoomId") String salesRoomId) {
        intelligentSalesRoomService.acceptSalesRoom(salesRoomId);
        return Response.ok().build();
    }

    @GET
    @Path("/{salesRoomId}/engagement/summary")
    @ApiOperation(
        value = "Get engagement summary",
        notes = "Returns engagement metrics and summary",
        response = IntelligentSalesRoomEngagementSummaryResponse.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowAllRoles
    public Response getEngagementSummary(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @QueryParam("fromDate") Long fromDateEpoch,
        @QueryParam("toDate") Long toDateEpoch
    ) {
        Optional<Instant> fromDate = fromDateEpoch != null ? Optional.of(Instant.ofEpochSecond(fromDateEpoch)) : Optional.empty();
        Optional<Instant> toDate = toDateEpoch != null ? Optional.of(Instant.ofEpochSecond(toDateEpoch)) : Optional.empty();

        IntelligentSalesRoomEngagementSummaryResponse summary = intelligentSalesRoomService.getEngagementSummary(salesRoomId, fromDate, toDate);
        return Response.ok(summary).build();
    }

    @GET
    @Path("/{salesRoomId}/engagement/sessions")
    @ApiOperation(
        value = "Get engagement sessions",
        notes = "Returns all user sessions",
        response = IntelligentSalesRoomEngagementSession.class,
        responseContainer = "List",
        tags = { "Intelligent Sales Room" }
    )
    @AllowAllRoles
    public Response getEngagementSessions(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @PathParam("salesRoomId") String salesRoomId) {
        List<IntelligentSalesRoomEngagementSession> sessions = intelligentSalesRoomService.getEngagementSessions(salesRoomId);
        return Response.ok(sessions).build();
    }

    @GET
    @Path("/{salesRoomId}/engagement/activity")
    @ApiOperation(
        value = "Get activity logs",
        notes = "Returns activity logs with optional date filtering",
        response = IntelligentSalesRoomActivityLog.class,
        responseContainer = "List",
        tags = { "Intelligent Sales Room" }
    )
    @AllowAllRoles
    public Response getActivityLogs(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @QueryParam("fromDate") Long fromDateEpoch,
        @QueryParam("toDate") Long toDateEpoch
    ) {
        Optional<Instant> fromDate = fromDateEpoch != null ? Optional.of(Instant.ofEpochSecond(fromDateEpoch)) : Optional.empty();
        Optional<Instant> toDate = toDateEpoch != null ? Optional.of(Instant.ofEpochSecond(toDateEpoch)) : Optional.empty();

        List<IntelligentSalesRoomActivityLog> logs = intelligentSalesRoomService.getActivityLogs(salesRoomId, fromDate, toDate);
        return Response.ok(logs).build();
    }

    @POST
    @Path("/share/{shareLink}/access")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Access sales room via share link",
        notes = "End-user access to sales room using share link",
        response = IntelligentSalesRoomOverviewResponse.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response accessViaShareLink(
        @PathParam("shareLink") String shareLink,
        @NotNull @Valid IntelligentSalesRoomShareLinkAccessRequest request,
        @Context HttpServletRequest httpRequest
    ) {
        Optional<String> ipAddress = Optional.ofNullable(httpRequest.getRemoteAddr());
        Optional<String> userAgent = Optional.ofNullable(httpRequest.getHeader("User-Agent"));

        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        IntelligentSalesRoomOverviewResponse response = TenantContextInjector.callInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            intelligentSalesRoomService.accessSalesRoomViaShareLink(shareLink, request.getUserName(), request.getUserEmail(), ipAddress, userAgent)
        );

        return Response.ok(response).build();
    }

    @POST
    @Path("/share/{shareLink}/sessions")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Start engagement session via share link",
        notes = "Starts tracking session for end-user",
        response = IntelligentSalesRoomEngagementSession.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response startSessionViaShareLink(
        @PathParam("shareLink") String shareLink,
        @NotNull @Valid IntelligentSalesRoomShareLinkAccessRequest request,
        @Context HttpServletRequest httpRequest
    ) {
        Optional<String> ipAddress = Optional.ofNullable(httpRequest.getRemoteAddr());
        Optional<String> userAgent = Optional.ofNullable(httpRequest.getHeader("User-Agent"));

        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        IntelligentSalesRoomEngagementSession session = TenantContextInjector.callInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            intelligentSalesRoomService.startUserSession(
                salesRoom.getSalesRoomId().toString(),
                request.getUserName(),
                request.getUserEmail(),
                ipAddress,
                userAgent
            )
        );

        return Response.ok(session).build();
    }

    @PUT
    @Path("/{shareLink}/sessions/{sessionId}/track-tab")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Track tab visit", notes = "Records which tab the user visited", tags = { "Intelligent Sales Room" })
    @AllowUnauthenticated(cascade = true)
    public Response trackTabVisit(
        @PathParam("shareLink") String shareLink,
        @PathParam("sessionId") String sessionId,
        @NotNull @Valid IntelligentSalesRoomTrackTabVisitRequest request
    ) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        TenantContextInjector.runInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            intelligentSalesRoomService.trackTabVisit(sessionId, request.getTabName())
        );

        return Response.ok().build();
    }

    @PUT
    @Path("/{shareLink}/sessions/{sessionId}/track-interaction")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Track widget interaction", notes = "Records user interaction with a widget", tags = { "Intelligent Sales Room" })
    @AllowUnauthenticated(cascade = true)
    public Response trackActivity(
        @PathParam("shareLink") String shareLink,
        @PathParam("sessionId") String sessionId,
        @NotNull @Valid IntelligentSalesRoomTrackActivityRequest request
    ) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        TenantContextInjector.runInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            intelligentSalesRoomService.trackActivity(sessionId, request.getWidgetId(), request.getActivity(), request.getTimeSpentSeconds())
        );

        return Response.ok().build();
    }

    @PUT
    @Path("/{shareLink}/sessions/{sessionId}/end")
    @ApiOperation(value = "End engagement session", notes = "Ends the user's engagement session", tags = { "Intelligent Sales Room" })
    @AllowUnauthenticated(cascade = true)
    public Response endSession(@PathParam("shareLink") String shareLink, @PathParam("sessionId") String sessionId) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        TenantContextInjector.runInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            intelligentSalesRoomService.endUserSession(sessionId)
        );

        return Response.ok().build();
    }

    @GET
    @Path("/{salesRoomId}/share-access")
    @ApiOperation(
        value = "Get share link access records",
        notes = "Returns all users who have accessed via share link",
        response = IntelligentSalesRoomShareLinkAccess.class,
        responseContainer = "List",
        tags = { "Intelligent Sales Room" }
    )
    @AllowAllRoles
    public Response getShareLinkAccesses(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @PathParam("salesRoomId") String salesRoomId) {
        List<IntelligentSalesRoomShareLinkAccess> accesses = intelligentSalesRoomService.getShareLinkAccesses(salesRoomId);
        return Response.ok(accesses).build();
    }

    @GET
    @Path("/{salesRoomId}/visitors/count")
    @ApiOperation(value = "Get unique visitor count", notes = "Returns the number of unique visitors", tags = { "Intelligent Sales Room" })
    @AllowAllRoles
    public Response getUniqueVisitorCount(@Auth @ApiParam(hidden = true) BillyAuthPrincipal principal, @PathParam("salesRoomId") String salesRoomId) {
        int count = intelligentSalesRoomService.getUniqueVisitorCount(salesRoomId);
        return Response.ok().entity("{\"count\":" + count + "}").build();
    }

    @GET
    @Path("/share/{shareLink}/widgets")
    @ApiOperation(
        value = "Get widgets via share link",
        notes = "End-user access to sales room widgets",
        response = IntelligentSalesRoomWidget.class,
        responseContainer = "List",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response getWidgetsViaShareLink(@PathParam("shareLink") String shareLink) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        List<IntelligentSalesRoomWidget> widgets = TenantContextInjector.callInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            intelligentSalesRoomService.getWidgetsBySalesRoomId(salesRoom.getSalesRoomId().toString())
        );

        return Response.ok(widgets).build();
    }

    @GET
    @Path("/share/{shareLink}/theme")
    @ApiOperation(
        value = "Get theme via share link",
        notes = "End-user access to sales room theme",
        response = IntelligentSalesRoomTheme.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response getThemeViaShareLink(@PathParam("shareLink") String shareLink) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        Optional<IntelligentSalesRoomTheme> theme = TenantContextInjector.callInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            intelligentSalesRoomService.getThemeBySalesRoomId(salesRoom.getSalesRoomId().toString())
        );

        if (theme.isPresent()) {
            return Response.ok(theme.get()).build();
        } else {
            return Response.ok().build();
        }
    }

    @GET
    @Path("/share/{shareLink}/files")
    @ApiOperation(
        value = "Get files via share link",
        notes = "End-user access to sales room files",
        response = IntelligentSalesRoomFile.class,
        responseContainer = "List",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response getFilesViaShareLink(
        @PathParam("shareLink") String shareLink,
        @QueryParam("category") IntelligentSalesRoomFileCategory category
    ) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        List<IntelligentSalesRoomFile> files = TenantContextInjector.callInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () -> {
            if (category != null) {
                return intelligentSalesRoomService.getFilesByCategory(salesRoom.getSalesRoomId().toString(), category);
            } else {
                return intelligentSalesRoomService.getFilesBySalesRoomId(salesRoom.getSalesRoomId().toString());
            }
        });

        return Response.ok(files).build();
    }

    @GET
    @Path("/share/{shareLink}/files/{fileId}/download")
    @ApiOperation(value = "Get file download URL via share link", notes = "End-user access to file download", tags = { "Intelligent Sales Room" })
    @AllowUnauthenticated(cascade = true)
    public Response getFileDownloadUrlViaShareLink(
        @PathParam("shareLink") String shareLink,
        @PathParam("fileId") String fileId,
        @QueryParam("expirationMinutes") @ApiParam(value = "URL expiration in minutes", defaultValue = "60") Integer expirationMinutes
    ) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        String downloadUrl = TenantContextInjector.callInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () -> {
            IntelligentSalesRoomFile file = intelligentSalesRoomService.getFileById(fileId);
            if (!file.getSalesRoomId().equals(salesRoom.getSalesRoomId().toString())) {
                throw new InvalidInputException("File does not belong to this sales room");
            }

            Duration expiration = Duration.ofMinutes(expirationMinutes != null ? expirationMinutes : 60);
            return intelligentSalesRoomService.generateFileDownloadUrl(fileId, expiration);
        });

        return Response.ok().entity("{\"downloadUrl\":\"" + downloadUrl + "\"}").build();
    }

    @GET
    @Path("/share/{shareLink}/engagement/summary")
    @ApiOperation(
        value = "Get engagement summary via share link",
        notes = "Public engagement metrics for sales room",
        response = IntelligentSalesRoomEngagementSummaryResponse.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response getEngagementSummaryViaShareLink(
        @PathParam("shareLink") String shareLink,
        @QueryParam("fromDate") Long fromDateEpoch,
        @QueryParam("toDate") Long toDateEpoch
    ) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        IntelligentSalesRoomEngagementSummaryResponse summary = TenantContextInjector.callInTenantContext(
            salesRoom.getTenantId(),
            tenantIdProvider,
            () -> {
                Optional<Instant> fromDate = fromDateEpoch != null ? Optional.of(Instant.ofEpochSecond(fromDateEpoch)) : Optional.empty();
                Optional<Instant> toDate = toDateEpoch != null ? Optional.of(Instant.ofEpochSecond(toDateEpoch)) : Optional.empty();

                return intelligentSalesRoomService.getEngagementSummary(salesRoom.getSalesRoomId().toString(), fromDate, toDate);
            }
        );

        return Response.ok(summary).build();
    }

    @GET
    @Path("/share/{shareLink}")
    @ApiOperation(
        value = "Get sales room details via share link",
        notes = "End-user access to basic sales room information",
        response = IntelligentSalesRoomOverviewResponse.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response getSalesRoomViaShareLink(@PathParam("shareLink") String shareLink) {
        IntelligentSalesRoomOverviewResponse response = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);
        return Response.ok(response).build();
    }

    @GET
    @Path("/share/{shareLink}/ai-content")
    @ApiOperation(
        value = "Get AI content via share link",
        notes = "End-user access to AI generated content",
        response = IntelligentSalesRoomAIGeneratedContent.class,
        responseContainer = "List",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response getAIContentViaShareLink(@PathParam("shareLink") String shareLink) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        List<IntelligentSalesRoomAIGeneratedContent> aiContent = TenantContextInjector.callInTenantContext(
            salesRoom.getTenantId(),
            tenantIdProvider,
            () -> intelligentSalesRoomService.getAIContentBySalesRoomId(salesRoom.getSalesRoomId().toString())
        );

        return Response.ok(aiContent).build();
    }

    @PUT
    @Path("/{salesRoomId}/customer-information")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Update customer information",
        notes = "Updates billing and/or shipping contact information",
        tags = { "Intelligent Sales Room" }
    )
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response updateCustomerInformation(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal principal,
        @PathParam("salesRoomId") String salesRoomId,
        @NotNull @Valid IntelligentSalesRoomUpdateCustomerInfoRequest request
    ) {
        intelligentSalesRoomService.updateCustomerInformation(salesRoomId, request.billingContactId(), request.shippingContactId());
        return Response.ok().build();
    }

    @PUT
    @Path("/share/{shareLink}/customer-information")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Update customer information via share link",
        notes = "End-user update of customer information via share link",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response updateCustomerInformationViaShareLink(
        @PathParam("shareLink") String shareLink,
        @NotNull @Valid IntelligentSalesRoomUpdateCustomerInfoRequest request
    ) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        TenantContextInjector.runInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            intelligentSalesRoomService.updateCustomerInformation(
                salesRoom.getSalesRoomId().toString(),
                request.billingContactId(),
                request.shippingContactId()
            )
        );

        return Response.ok().build();
    }

    @GET
    @Path("/share/{shareLink}/image/{fileName}")
    @Produces({ "image/*", MediaType.APPLICATION_JSON })
    @ApiOperation(value = "Download image file via share link", notes = "Downloads image file for public access", tags = { "Intelligent Sales Room" })
    @AllowUnauthenticated(cascade = true)
    public Response downloadImageViaShareLink(@NotEmpty @PathParam("shareLink") String shareLink, @NotEmpty @PathParam("fileName") String fileName) {
        String tenantId = intelligentSalesRoomService.getTenantIdByShareLink(shareLink);

        InputStream stream = TenantContextInjector.callInTenantContext(tenantId, tenantIdProvider, () ->
            intelligentSalesRoomService.downloadFileViaShareLink(shareLink, fileName)
        );

        return buildFileDownloadResponse(stream, fileName);
    }

    private Response buildFileDownloadResponse(InputStream stream, String fileName) {
        return Response.ok(stream)
            .header("Cache-Control", "public, max-age=86400")
            .header("Content-Type", intelligentSalesRoomS3Service.detectContentType(fileName))
            .build();
    }

    @GET
    @Path("/share/{shareLink}/order/pdf")
    @ApiOperation(
        value = "Fetch order form PDF for an Intelligent Sales Room",
        notes = "Downloads the order form PDF for the specified Intelligent Sales Room.",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response getOrderDocument(@NotEmpty @PathParam("shareLink") String shareLink) {
        String tenantId = intelligentSalesRoomService.getTenantIdByShareLink(shareLink);
        String orderId = intelligentSalesRoomService.getOrderIdByShareLink(shareLink);
        return TenantContextInjector.callInTenantContext(tenantId, tenantIdProvider, () ->
            getResponseForOrderDocumentDownload(orderDocumentService, orderId)
        );
    }

    static Response getResponseForOrderDocumentDownload(OrderDocumentService orderDocumentService, String orderId) {
        Optional<InputStream> inputStream = orderDocumentService.getOrderDocumentCreateIfNeeded(orderId);
        if (inputStream.isEmpty()) {
            return Response.status(Response.Status.ACCEPTED).build();
        }
        String fileName = orderDocumentService.getOrderDocumentFileName(orderId);
        return FileResponseBuilder.getFileResponse(inputStream.get(), fileName);
    }

    @POST
    @Path("/share/{shareLink}/esign")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Send email for e-signature via share link",
        notes = "Sends email for eSign for Sales Room via share link",
        response = IntelligentSalesRoomEngagementSession.class,
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response sendEmailForEsignViaShareLink(@PathParam("shareLink") String shareLink, EmailContact accountSignatory) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);
        TenantContextInjector.runInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            intelligentSalesRoomService.sendEmailForEsign(salesRoom.getSalesRoomId().toString(), accountSignatory)
        );
        return Response.ok().build();
    }

    @GET
    @Path("/share/{shareLink}/custom-fields")
    @ApiOperation(
        value = "Fetch custom fields for an Intelligent Sales Room",
        notes = "Retrieves the custom fields for the specified Intelligent Sales Room.",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response getCustomFields(@NotEmpty @PathParam("shareLink") String shareLink) {
        String tenantId = intelligentSalesRoomService.getTenantIdByShareLink(shareLink);
        Map<String, CustomFieldValue> response = TenantContextInjector.callInTenantContext(tenantId, tenantIdProvider, () ->
            intelligentSalesRoomService.getCustomFields(shareLink)
        );
        return Response.ok(response).build();
    }

    @PUT
    @Path("/share/{shareLink}/custom-fields")
    @ApiOperation(
        value = "Update custom fields for an Intelligent Sales Room",
        notes = "Updates the custom fields for the specified Intelligent Sales Room.",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response updateCustomFields(
        @NotEmpty @PathParam("shareLink") String shareLink,
        @NotNull @ApiParam(value = "Custom field values", required = true) Map<String, CustomFieldValue> customFields
    ) {
        String tenantId = intelligentSalesRoomService.getTenantIdByShareLink(shareLink);
        Map<String, CustomFieldValue> response = TenantContextInjector.callInTenantContext(tenantId, tenantIdProvider, () ->
            intelligentSalesRoomService.updateCustomFields(shareLink, customFields)
        );
        return Response.ok(response).build();
    }

    @GET
    @Path("/share/{shareLink}/order-metadata")
    @ApiOperation(
        value = "Fetch order metadata for an Intelligent Sales Room",
        notes = "Retrieves the corder metadata for the specified Intelligent Sales Room.",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response getOrderMetadata(@NotEmpty @PathParam("shareLink") String shareLink) {
        String tenantId = intelligentSalesRoomService.getTenantIdByShareLink(shareLink);
        IntelligentSalesRoomOrderMetadataResponse response = TenantContextInjector.callInTenantContext(tenantId, tenantIdProvider, () ->
            intelligentSalesRoomService.getSalesRoomOrderMetadata(shareLink)
        );
        return Response.ok(response).build();
    }
}
