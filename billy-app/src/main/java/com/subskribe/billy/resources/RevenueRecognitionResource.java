package com.subskribe.billy.resources;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.resources.shared.ResponseUtility;
import com.subskribe.billy.revrec.bulk.model.BulkRevenueRecognition;
import com.subskribe.billy.revrec.bulk.model.BulkRevenueRecognitionInput;
import com.subskribe.billy.revrec.bulk.model.BulkRevenueRecognitionItem;
import com.subskribe.billy.revrec.bulk.service.BulkRevenueService;
import com.subskribe.billy.revrec.model.RecognitionEventCompletion;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.revrec.model.RecognitionSchedule;
import com.subskribe.billy.revrec.model.RecognitionTransaction;
import com.subskribe.billy.revrec.model.TransactionType;
import com.subskribe.billy.revrec.services.RevenueRecognitionEventService;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.revrec.services.RevenueRecognitionService;
import com.subskribe.billy.revrec.services.RevenueWaterfallService;
import com.subskribe.billy.shared.batchupload.BatchUploadResult;
import com.subskribe.billy.shared.utility.UUIDConverter;
import com.subskribe.billy.user.model.Role;
import edu.umd.cs.findbugs.annotations.NonNull;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.ForbiddenException;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

@Path("/revrec")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class RevenueRecognitionResource {

    private final BillyConfiguration billyConfiguration;
    private final RevenueWaterfallService revenueWaterfallService;
    private final RevenueRecognitionService revenueRecognitionService;
    private final RevenueRecognitionGetService revenueRecognitionGetService;
    private final RevenueRecognitionEventService revenueRecognitionEventService;
    private final BulkRevenueService bulkRevenueService;

    @Inject
    public RevenueRecognitionResource(
        BillyConfiguration billyConfiguration,
        RevenueRecognitionService revenueRecognitionService,
        RevenueWaterfallService revenueWaterfallService,
        RevenueRecognitionGetService revenueRecognitionGetService,
        RevenueRecognitionEventService revenueRecognitionEventService,
        BulkRevenueService bulkRevenueService
    ) {
        this.billyConfiguration = billyConfiguration;
        this.revenueRecognitionService = revenueRecognitionService;
        this.revenueWaterfallService = revenueWaterfallService;
        this.revenueRecognitionGetService = revenueRecognitionGetService;
        this.revenueRecognitionEventService = revenueRecognitionEventService;
        this.bulkRevenueService = bulkRevenueService;
    }

    @GET
    @Path("/waterfall")
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @ApiOperation(
        value = "Download waterfall report",
        notes = "Downloads a revenue waterfall report in csv format.",
        tags = { "Revenue Recognition" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.REVENUE_CLERK, Role.READ_ONLY, Role.EXECUTIVE })
    public Response getRevenueWaterfall(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("startDate") @ApiParam(name = "startDate", value = "report start date as unix timestamp") Long startDate,
        @QueryParam("endDate") @ApiParam(name = "endDate", value = "report end date as unix timestamp") Long endDate
    ) {
        StreamingOutput streamingOutput = Objects.nonNull(startDate) && Objects.nonNull(endDate)
            ? revenueWaterfallService.getWaterfallByDateRange(Instant.ofEpochSecond(startDate), Instant.ofEpochSecond(endDate))
            : revenueWaterfallService.getWaterfall();
        String fileName = revenueWaterfallService.getReportFileName();
        return ResponseUtility.okResponseFromStreamingOutputAndFileName(streamingOutput, fileName, "text/csv");
    }

    @GET
    @Path("/test/transactions/{invoiceItemId}")
    @ApiOperation(value = "Test API for E2E testing", hidden = true, tags = { "Revenue Recognition" })
    @AllowAllRoles
    public Response getRevenueTransactionsByInvoiceItem(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("invoiceItemId") String invoiceItemId
    ) {
        throwIfNotLocal();
        List<RecognitionTransaction> recognitionTransactions = revenueRecognitionGetService.getRecognitionTransactionsByInvoiceItem(
            UUID.fromString(invoiceItemId)
        );
        return Response.ok(recognitionTransactions).build();
    }

    @GET
    @Path("/test/schedules/{orderId}")
    @ApiOperation(value = "Test API for manual testing", hidden = true, tags = { "Revenue Recognition" })
    @AllowRoles({ Role.ADMIN })
    public Response getRecognitionSchedulesByOrderId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("orderId") String orderId
    ) {
        throwIfNotLocal();
        List<RecognitionSchedule> recognitionSchedules = revenueRecognitionGetService.getRecognitionSchedulesByOrderId(orderId);
        return Response.ok(recognitionSchedules).build();
    }

    @GET
    @Path("/test/transactionsByOrder/{orderId}")
    @ApiOperation(value = "Test API for manual testing", hidden = true, tags = { "Revenue Recognition" })
    @AllowRoles({ Role.ADMIN })
    public Response getRecognitionTransactionsByOrderId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("orderId") String orderId
    ) {
        throwIfNotLocal();
        List<RecognitionTransaction> recognitionTransactions = revenueRecognitionGetService.getRecognitionTransactionsByOrderId(orderId);
        return Response.ok(recognitionTransactions).build();
    }

    @POST
    @Path("/test/schedule")
    @ApiOperation(value = "Test API for manual testing", hidden = true, tags = { "Revenue Recognition" })
    @AllowRoles({ Role.ADMIN })
    public Response generateRecognitionSchedule(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NonNull @QueryParam("sourceTransactionType") TransactionType sourceTransactionType,
        @NotEmpty @QueryParam("transactionId") String transactionId
    ) {
        throwIfNotLocal();
        revenueRecognitionService.generateRecognitionSchedulesForSourceTransaction(sourceTransactionType, transactionId);
        return Response.ok().build();
    }

    @POST
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Path("/events/upload")
    @ApiOperation(
        value = "Upload revenue events",
        notes = """
        Uploads revenue events from a csv file. The format of the file is
        Subscription.Id,Charge.Id,Alias.Id,RevrecEvent.PercentComplete
        Percent complete should be a decimal.""",
        tags = { "Revenue Recognition" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.REVENUE_CLERK })
    public Response uploadCompletionEvents(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @FormDataParam("file") InputStream importStream,
        @FormDataParam("file") FormDataContentDisposition contentDisposition
    ) throws IOException {
        // Returns a result per row number starting with row index = 1
        BatchUploadResult result = revenueRecognitionEventService.uploadEvents(importStream);
        return Response.ok(result).build();
    }

    @GET
    @Path("/events")
    @ApiOperation(
        value = "Get revenue recognition events",
        notes = "Gets revenue recognition events for the specified subscription and charge.",
        response = RecognitionEventCompletion.class,
        responseContainer = "List",
        tags = { "Revenue Recognition" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.REVENUE_CLERK })
    public Response getRecognitionEventsBySubscriptionIdChargeId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("accountingPeriodId") @ApiParam(
            name = "accountingPeriodId",
            value = "id of the accounting period to limit events to"
        ) String accountingPeriodId,
        @NotNull @QueryParam("subscriptionId") @ApiParam(
            name = "subscriptionId",
            value = "id of the subscription",
            required = true
        ) String subscriptionId,
        @NotNull @QueryParam("chargeId") @ApiParam(name = "chargeId", value = "id of the charge", required = true) String chargeId
    ) {
        List<RecognitionEventCompletion> events = StringUtils.isNotBlank(accountingPeriodId)
            ? revenueRecognitionEventService.getRecognitionEventsInAccountingPeriod(accountingPeriodId, subscriptionId, chargeId)
            : revenueRecognitionEventService.getRecognitionEventsBySubscriptionIdChargeId(subscriptionId, chargeId);
        return Response.ok(events).build();
    }

    @POST
    @Path("/events")
    @ApiOperation(
        value = "Create a revenue recognition event",
        notes = "Creates a revenue recognition event, only if it is different from the previous event for same subscription and charge.",
        tags = { "Revenue Recognition" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.REVENUE_CLERK })
    public Response createRecognitionEvent(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(value = "recognition event details", required = true) RecognitionEventCompletion recognitionEventCompletion
    ) {
        Optional<RecognitionEventCompletion> savedEvent = revenueRecognitionEventService.insertEventSkipDuplicate(recognitionEventCompletion);
        if (savedEvent.isEmpty()) {
            return Response.status(Response.Status.NO_CONTENT).build();
        }
        return Response.ok(savedEvent.get()).build();
    }

    @GET
    @Path("/rules")
    @ApiOperation(value = "Get revenue recognition rules", notes = "Get revenue recognition rules.", tags = { "Revenue Recognition" })
    @AllowAllRoles
    public Response getRecognitionRuleById(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        var recognitionRules = revenueRecognitionGetService.getRecognitionRules();
        return Response.ok(recognitionRules).build();
    }

    @GET
    @Path("/rules/{id}")
    @ApiOperation(value = "Get revenue recognition rule", notes = "Get a revenue recognition rule using id.", tags = { "Revenue Recognition" })
    @AllowAllRoles
    public Response getRecognitionRuleById(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "id of the recognition rule", required = true) String ruleId
    ) {
        var recognitionRule = revenueRecognitionGetService.getRecognitionRuleByRuleId(ruleId);
        return Response.ok(recognitionRule).build();
    }

    @POST
    @Path("/rules")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Create a revenue recognition rule", notes = "Creates a revenue recognition rule.", tags = { "Revenue Recognition" })
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response addRecognitionRule(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(value = "recognition rule details", required = true) RecognitionRule recognitionRule,
        @Context UriInfo uriInfo
    ) {
        var savedRule = revenueRecognitionService.upsertRecognitionRule(recognitionRule);

        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(savedRule.getRuleId());
        return Response.created(builder.build()).entity(savedRule).build();
    }

    @DELETE
    @Path("/rules/{ruleId}")
    @ApiOperation(
        value = "Delete a recognition rule",
        notes = "Deletes a recognition rule. Note you can't delete a recognition rule that's in use.",
        response = RecognitionRule.class,
        tags = { "Revenue Recognition" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response deleteRule(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("ruleId") @ApiParam(value = "id of the recognition rule", required = true) String ruleId
    ) {
        var recognitionRule = revenueRecognitionService.deleteRecognitionRule(ruleId);
        return Response.ok().entity(recognitionRule).build();
    }

    @POST
    @Path("/bulk")
    @ApiOperation(
        value = "Create a bulk revenue recognition",
        notes = "Creates a bulk revenue recognition as specified by the input parameters. On success the id of the bulk revenue recognition is returned.",
        response = String.class,
        tags = { "Revenue Recognition" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response createBulkRevenueRecognition(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @ApiParam(
            value = "json representing the bulk revenue recognition parameters",
            required = true
        ) BulkRevenueRecognitionInput bulkRevenueRecognitionInput,
        @Context UriInfo uriInfo
    ) {
        BulkRevenueRecognition bulkRevenueRecognition = bulkRevenueService.createBulkRevenueRecognition(bulkRevenueRecognitionInput);
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        var uri = builder.path(bulkRevenueRecognition.getBulkRevenueRecognitionId()).build();
        return Response.created(uri).build();
    }

    @GET
    @Path("bulk/{bulkRevenueRecognitionId}")
    @ApiOperation(
        value = "Get bulk revenue recognition details",
        notes = "Returns the details of the specified bulk revenue recognition",
        response = BulkRevenueRecognition.class,
        tags = { "Revenue Recognition" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getBulkRevenueRecognition(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("bulkRevenueRecognitionId") @ApiParam(
            value = "id of the bulk revenue recognition",
            required = true
        ) String bulkRevenueRecognitionId
    ) {
        BulkRevenueRecognition result;
        Optional<UUID> internalId = UUIDConverter.toUUID(bulkRevenueRecognitionId);
        if (internalId.isPresent()) {
            result = bulkRevenueService.getBulkRevenueRecognition(internalId.get());
        } else {
            result = bulkRevenueService.getBulkRevenueRecognitionByBulkRevenueRecognitionId(bulkRevenueRecognitionId);
        }
        return Response.ok(result).build();
    }

    @GET
    @Path("bulk/{bulkRevenueRecognitionId}/revrecItems")
    @ApiOperation(
        value = "Get items for bulk revenue recognition",
        notes = "Returns the items associated with the specified bulk revenue recognition",
        response = BulkRevenueRecognitionItem.class,
        responseContainer = "List",
        tags = { "Revenue Recognition" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getBulkRevenueRecognitionItems(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("bulkRevenueRecognitionId") @ApiParam(
            value = "id of the bulk revenue recognition",
            required = true
        ) String bulkRevenueRecognitionId
    ) {
        List<BulkRevenueRecognitionItem> bulkRevenueRecognitionItems;
        Optional<UUID> internalId = UUIDConverter.toUUID(bulkRevenueRecognitionId);
        if (internalId.isPresent()) {
            bulkRevenueRecognitionItems = bulkRevenueService.getBulkRevenueRecognitionItems(internalId.get());
        } else {
            bulkRevenueRecognitionItems = bulkRevenueService.getBulkRevenueRecognitionItemsByBulkRevenueRecognitionItemId(bulkRevenueRecognitionId);
        }
        return Response.ok(bulkRevenueRecognitionItems).build();
    }

    @PUT
    @Path("/test/process/events")
    @ApiOperation(value = "Test API for E2E testing", hidden = true, tags = { "Revenue Recognition" })
    @AllowAllRoles
    public Response processRevenueRecognitionEvents(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        throwIfNotLocal();
        revenueRecognitionService.processRevenueRecognitionEvents();
        return Response.ok().build();
    }

    private void throwIfNotLocal() {
        if (billyConfiguration.isLocalOrCi()) {
            return;
        }

        throw new ForbiddenException("Not allowed.");
    }
}
