package com.subskribe.billy.resources;

import com.subskribe.ai.service.AgentSession;
import com.subskribe.ai.service.AgentSessionFactory;
import com.subskribe.ai.service.SummarizationService;
import com.subskribe.ai.service.bedrock.BedrockExplainProrationService;
import com.subskribe.ai.service.bedrock.BedrockSummarizationService;
import com.subskribe.ai.service.model.Message;
import com.subskribe.ai.service.openai.OpenAIAgentSessionFactory;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.resources.shared.ResponseUtility;
import com.subskribe.billy.shared.ResourceConstants;
import com.subskribe.billy.shared.enums.BillyObjectType;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.glassfish.jersey.media.sse.EventOutput;
import org.glassfish.jersey.media.sse.OutboundEvent;

@Path("/ai")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class AiResource {

    private final AgentSessionFactory agentFactory;
    private final FeatureService featureService;

    private final SummarizationService summarizationService;
    private final BedrockExplainProrationService bedrockExplainProrationService;

    @Inject
    public AiResource(
        OpenAIAgentSessionFactory openAIAgentSessionFactory,
        FeatureService featureService,
        BedrockSummarizationService summarizationService,
        BedrockExplainProrationService bedrockExplainProrationService
    ) {
        // right now this is configured to be open AI session factory
        // after bedrock is fully tested we can move to bedrock session factory
        agentFactory = openAIAgentSessionFactory;
        this.featureService = featureService;
        this.summarizationService = summarizationService;
        this.bedrockExplainProrationService = bedrockExplainProrationService;
    }

    @GET
    @Path("/summary/subscription/{subscriptionId}")
    @ApiOperation(
        value = "Generate a summary of the subscription in plain english in markdown format",
        notes = "Generate a complete summary of the subscription with the given id, a full detailed summary will be generated including metrics",
        response = String.class,
        tags = { "AI Agent", ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    @Produces({ MediaType.SERVER_SENT_EVENTS, MediaType.APPLICATION_JSON })
    public Response generateSubscriptionSummary(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("subscriptionId") @ApiParam(value = "id of the subscription") String subscriptionId
    ) {
        checkFeatureEnablement();
        EventOutput eventOutput = summarizationService.summarizeSubscriptionStream(subscriptionId);
        return ResponseUtility.okResponseFromEventOutput(eventOutput);
    }

    @GET
    @Path("/summary/order/{orderId}")
    @ApiOperation(
        value = "Generate a summary of the order in plain english in markdown format",
        notes = "Generate a complete summary of the order with the given id, a full detailed summary will be generated including metrics",
        response = String.class,
        tags = { "AI Summary", ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    @Produces({ MediaType.SERVER_SENT_EVENTS, MediaType.APPLICATION_JSON })
    public Response generateOrderSummary(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("orderId") @ApiParam(value = "id of the order") String orderId,
        @QueryParam("type") @ApiParam(
            value = "Force regeneration of the PDF document even if there has been no changes. Defaults to false."
        ) String type
    ) {
        checkFeatureEnablement();
        EventOutput eventOutput = summarizationService.summarizeOrderStream(orderId, type);
        return ResponseUtility.okResponseFromEventOutput(eventOutput);
    }

    @GET
    @Path("/explain/proration/async/{orderId}/{orderLineItemId}")
    @ApiOperation(
        value = "Generate an explanation of the proration calculation",
        notes = "Generate an explanation of the proration calculation for the given order line item.",
        response = String.class,
        tags = { "AI Summary", ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    @Produces({ MediaType.SERVER_SENT_EVENTS, MediaType.APPLICATION_JSON })
    public Response explainProrationAsync(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("orderId") @ApiParam(value = "id of the order") String orderId,
        @NotNull @PathParam("orderLineItemId") @ApiParam(value = "id of the order line item") String orderLineItemId
    ) {
        checkFeatureEnablement();
        EventOutput eventOutput = bedrockExplainProrationService.explainProrationAsync(orderId, orderLineItemId);
        return ResponseUtility.okResponseFromEventOutput(eventOutput);
    }

    @GET
    @Path("/explain/proration/{orderId}/{orderLineItemId}")
    @ApiOperation(
        value = "Generate an explanation of the proration calculation",
        notes = "Generate an explanation of the proration calculation for the given order line item.",
        response = String.class,
        tags = { "AI Summary", ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    public Response explainProration(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("orderId") @ApiParam(value = "id of the order") String orderId,
        @NotNull @PathParam("orderLineItemId") @ApiParam(value = "id of the order line item") String orderLineItemId
    ) {
        checkFeatureEnablement();
        String response = bedrockExplainProrationService.explainProration(orderId, orderLineItemId);
        return Response.ok().entity(response).build();
    }

    @POST
    @ApiOperation(
        value = "Create new conversational AI agent session",
        notes = "Create a new conversation session with Subskribe AI agent, this resource will return a session id which will be used for future conversations",
        tags = { "AI Agent", ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    @Path("/agents/session")
    public Response createAgentSession(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @Context UriInfo uriInfo) {
        checkFeatureEnablement();
        AgentSession agentSession = agentFactory.creatAgentSession();
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(agentSession.getSessionId());
        return Response.created(builder.build()).build();
    }

    @GET
    @ApiOperation(
        value = "Get the agent session given the id, NOTE: at the moment there is no response body",
        notes = "Get the agent session given the id, NOTE: at the moment there is no response body, if 200 is returned then session exists",
        tags = { "AI Agent", ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    @Path("/agents/session/{sessionId}")
    public Response getAgentSession(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("sessionId") @ApiParam(value = "id of the session to be fetched") String sessionId
    ) {
        checkFeatureEnablement();
        agentFactory.getAgentSession(sessionId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.AI_AGENT_SESSION, sessionId));
        return Response.ok().build();
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Produces({ MediaType.TEXT_PLAIN, MediaType.APPLICATION_JSON })
    @Path("/agents/session/{sessionId}/chat")
    @ApiOperation(
        value = "Chat with a given session id and get back a response for a given message",
        notes = "The API responds user message, a session id is required to identify the session this message needs to be posted",
        response = String.class,
        tags = { "AI Agent", ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    public Response chatResponse(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("sessionId") @ApiParam(value = "id of the session with which the conversation needs to happen") String sessionId,
        @NotNull String userMessage
    ) {
        checkFeatureEnablement();
        AgentSession agentSession = agentFactory
            .getAgentSession(sessionId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.AI_AGENT_SESSION, sessionId));
        String agentResponse = agentSession.getAgentResponse(userMessage);
        return Response.ok(agentResponse).build();
    }

    @GET
    @Produces({ MediaType.SERVER_SENT_EVENTS, MediaType.APPLICATION_JSON })
    @Path("/agents/session/{sessionId}/chatAsync")
    @ApiOperation(
        value = "Chat with a given session id and get back a response for a given message in a async manner in the form of server side events",
        notes = "The API responds to user message, a session id is required to identify the session this message needs to be posted",
        response = OutboundEvent.class,
        tags = { "AI Agent", ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    public Response chatResponseAsync(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("sessionId") @ApiParam(value = "id of the session with which the conversation needs to happen") String sessionId,
        @QueryParam("userMessage") @ApiParam(value = "the user message to which the AI can respond") String userMessage
    ) {
        checkFeatureEnablement();
        AgentSession agentSession = agentFactory
            .getAgentSession(sessionId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.AI_AGENT_SESSION, sessionId));
        EventOutput eventOutput = agentSession.getAgentResponseAsync(userMessage);
        return ResponseUtility.okResponseFromEventOutput(eventOutput);
    }

    @GET
    @Path("/agents/session/{sessionId}/messages")
    @ApiOperation(
        value = "List the messages belong to this session",
        notes = "The message will be returned in the most recent order, with the latest being the first",
        response = Message.class,
        responseContainer = "List",
        tags = { "AI Agent", ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    public Response chatMessages(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("sessionId") @ApiParam(value = "id of the session with which the conversation needs to happen") String sessionId,
        @QueryParam("limit") @ApiParam(value = "the number of message to fetch should be a number between 1 to 100 if present") int limit
    ) {
        checkFeatureEnablement();
        if (limit == 0) {
            limit = 10;
        }
        AgentSession agentSession = agentFactory
            .getAgentSession(sessionId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.AI_AGENT_SESSION, sessionId));

        return Response.ok(agentSession.getRecentMessages(limit)).build();
    }

    private void checkFeatureEnablement() {
        if (!featureService.isEnabled(Feature.SUBSKRIBE_AI_AGENT)) {
            throw new InvalidInputException("AI agent feature is not enabled for consumption");
        }
    }
}
