package com.subskribe.billy.resources;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.esign.EsignProductMetadata;
import com.subskribe.billy.esign.model.EsignRequest;
import com.subskribe.billy.esign.model.pandadoc.PandaDocWebhookPayload;
import com.subskribe.billy.esign.services.EsignDocumentService;
import com.subskribe.billy.esign.services.EsignService;
import com.subskribe.billy.esign.services.EsignWebhookProcessor;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.resources.shared.FileResponseBuilder;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.ErrorContext;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.webhook.ImmutableIncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhookTaskDispatcher;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.time.Clock;
import java.util.List;
import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Path("/esignature")
public class EsignResource {

    private static final Logger LOGGER = LoggerFactory.getLogger(EsignResource.class);
    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    private final EsignService esignService;
    private final EsignDocumentService esignDocumentService;
    private final BillyConfiguration billyConfiguration;
    private final FeatureService featureService;
    private final Clock clock;
    private final IncomingWebhookTaskDispatcher incomingWebhookTaskDispatcher;

    @Context
    private ContainerRequestContext requestContext;

    @Inject
    public EsignResource(
        EsignService esignService,
        EsignDocumentService esignDocumentService,
        BillyConfiguration billyConfiguration,
        FeatureService featureService,
        Clock clock,
        IncomingWebhookTaskDispatcher incomingWebhookTaskDispatcher
    ) {
        this.esignService = esignService;
        this.esignDocumentService = esignDocumentService;
        this.billyConfiguration = billyConfiguration;
        this.featureService = featureService;
        this.clock = clock;
        this.incomingWebhookTaskDispatcher = incomingWebhookTaskDispatcher;
    }

    @AllowUnauthenticated(cascade = true)
    @POST
    @Path("/pandadoc/webhook")
    @Consumes({ MediaType.APPLICATION_JSON, "application/yaml", MediaType.TEXT_XML, MediaType.APPLICATION_FORM_URLENCODED })
    @ApiOperation(
        value = "Webhook callback for Pandadoc integration",
        notes = "This is the webhook which Pandadoc calls back to",
        hidden = true,
        tags = { "Signature" }
    )
    public Response pandaDocESignatureWebhook(@ApiParam List<PandaDocWebhookPayload> payloads) {
        for (var payload : payloads) {
            try {
                IncomingWebhook incomingWebhook = ImmutableIncomingWebhook.builder()
                    .webhookType(EsignWebhookProcessor.WEBHOOK_TYPE)
                    .payload(OBJECT_MAPPER.writeValueAsString(payload))
                    .receivedOn(clock.millis())
                    .headers(requestContext.getHeaders())
                    .build();
                incomingWebhookTaskDispatcher.dispatch(incomingWebhook);
            } catch (JsonProcessingException e) {
                LOGGER.error(
                    new ErrorContext(EsignProductMetadata.ESIGN_WEBHOOKS, EsignProductMetadata.CANT_SERIALIZE_WEBHOOK),
                    "Error serializing PandaDoc webhook payload",
                    e
                );
                throw new InvalidInputException("Error serializing PandaDoc webhook payload", e);
            }
        }
        return Response.ok().build();
    }

    @PUT
    @Path("/pandadoc/void/{documentId}")
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    public Response voidPandaDocDocument(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("documentId") String documentId
    ) {
        esignService.voidPandaDocDocument(documentId);
        return Response.ok().build();
    }

    @PUT
    @Path("/voidSignedDocumentForOrder/{orderId}")
    @AllowRoles({ Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    @ApiOperation(
        value = "Mark a fully e-signed document internally as voided",
        notes = "Mark a fully e-signed document internally as voided",
        tags = { "Signature" }
    )
    public Response voidCompletelySignedDocumentForOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("orderId") String orderId
    ) {
        esignService.voidSignedDocumentForOrderInternal(orderId);
        return Response.ok().build();
    }

    @GET
    @Path("/pandadoc/store/{orderId}")
    @AllowAllRoles
    public Response getPandaDocDocument(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @PathParam("orderId") String orderId) {
        var optional = esignDocumentService.getEsignatureDocument(orderId);
        if (optional.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.ESIGNATURE, orderId);
        } else {
            return FileResponseBuilder.getFileResponse(optional.get(), orderId);
        }
    }

    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Test API for E2E testing", hidden = true)
    @POST
    @Path("/test/pandadoc/send")
    public Response sendEmailForEsignForTest(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @ApiParam EsignRequest esignRequest
    ) {
        validateTestApiIsCalledFromLocalOrCiOnly();
        esignService.requestEsignatureViaPandaDoc(esignRequest, true);
        return Response.ok().build();
    }

    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Test API for E2E testing", hidden = true)
    @POST
    @Path("/test/pandadoc/webhook")
    public Response pandaDocESignatureWebhookForTest(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @ApiParam List<PandaDocWebhookPayload> payloads
    ) {
        validateTestApiIsCalledFromLocalOrCiOnly();
        try {
            for (var payload : payloads) {
                esignService.updateStatusFromWebhook(payload, true);
            }
        } catch (Exception e) {
            LOGGER.warn("Error processing PandaDoc webhook", e);
        }
        return Response.ok().build();
    }

    private void validateTestApiIsCalledFromLocalOrCiOnly() {
        if (billyConfiguration.isLocalOrCi()) {
            return;
        }
        throw new UnsupportedOperationException("Test API call is not allowed outside of local/ci.");
    }
}
