package com.subskribe.billy.resources.json.version;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Objects;

public class VersionJson {

    @JsonProperty
    private String version;

    @JsonProperty
    private String release;

    @JsonProperty
    private String deployedFrom;

    public VersionJson() {}

    public VersionJson(String version, String release, String deployedFrom) {
        this.version = version;
        this.release = release;
        this.deployedFrom = deployedFrom;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRelease() {
        return release;
    }

    public void setRelease(String release) {
        this.release = release;
    }

    public String getDeployedFrom() {
        return deployedFrom;
    }

    public void setDeployedFrom(String deployedFrom) {
        this.deployedFrom = deployedFrom;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        VersionJson that = (VersionJson) o;
        return Objects.equals(version, that.version) && Objects.equals(release, that.release) && Objects.equals(deployedFrom, that.deployedFrom);
    }

    @Override
    public int hashCode() {
        return Objects.hash(version, release, deployedFrom);
    }
}
