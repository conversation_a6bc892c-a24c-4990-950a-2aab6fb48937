package com.subskribe.billy.resources;

import com.subskribe.ai.service.bedrock.BedrockGuidedSelling;
import com.subskribe.ai.service.model.GuidedSellingInput;
import com.subskribe.ai.service.model.MessagesAndAnswer;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.quotebuilder.model.Answer;
import com.subskribe.billy.order.quotebuilder.model.BuildOrderFromAnswersRequest;
import com.subskribe.billy.order.quotebuilder.model.GuidedSellingUsecase;
import com.subskribe.billy.order.quotebuilder.model.ImmutableBuildOrderFromAnswersRequest;
import com.subskribe.billy.order.quotebuilder.model.NextQuestion;
import com.subskribe.billy.order.quotebuilder.model.NextQuestions;
import com.subskribe.billy.order.quotebuilder.service.QuestionAnswerService;
import com.subskribe.billy.order.quotebuilder.service.QuoteBuilderService;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.shared.ResourceConstants;
import com.subskribe.billy.shared.annotations.Beta;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.validation.Validator;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.media.multipart.FormDataParam;
import org.mapstruct.factory.Mappers;

@Path("/guidedSelling")
@Produces(MediaType.APPLICATION_JSON)
@Beta
@Api
public class GuidedSellingResource {

    private final FeatureService featureService;

    private final QuestionAnswerService questionAnswerService;

    private final QuoteBuilderService quoteBuilderService;

    private final BedrockGuidedSelling aiGuidedSelling;

    private final OrderMapper orderMapper;

    private final BillyConfiguration billyConfiguration;

    @Inject
    public GuidedSellingResource(
        FeatureService featureService,
        QuestionAnswerService questionAnswerService,
        QuoteBuilderService quoteBuilderService,
        BedrockGuidedSelling aiGuidedSelling,
        BillyConfiguration billyConfiguration
    ) {
        this.featureService = featureService;
        this.questionAnswerService = questionAnswerService;
        this.quoteBuilderService = quoteBuilderService;
        this.aiGuidedSelling = aiGuidedSelling;
        this.billyConfiguration = billyConfiguration;
        orderMapper = Mappers.getMapper(OrderMapper.class);
    }

    @PUT
    @Path("/ai/answers")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Use AI to get answers for guided selling questions",
        notes = "Get the answer to guided selling questions form the deal desk AI, when all questions are answered then return the Answers for the guided selling",
        response = MessagesAndAnswer.class,
        tags = { ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    @Beta
    public Response getAnswersFromAi(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @Valid GuidedSellingInput guidedSellingInput,
        @Context UriInfo uriInfo
    ) throws Exception {
        checkForFeatureEnablement();
        return Response.ok(aiGuidedSelling.extractAnswers(guidedSellingInput)).build();
    }

    @GET
    @Path("/usecase")
    @AllowAllRoles
    @Beta
    public Response listUsecases(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @Context UriInfo uriInfo) {
        checkForFeatureEnablement();
        return Response.ok(questionAnswerService.listUsecases()).build();
    }

    @POST
    @Path("/usecase")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles(Role.ADMIN)
    @Beta
    public Response addUsecase(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull GuidedSellingUsecase guidedSellingUsecase,
        @Context UriInfo uriInfo
    ) {
        checkForFeatureEnablement();
        if (StringUtils.isNotBlank(guidedSellingUsecase.getId())) {
            throw new InvalidInputException("Cannot have id set during use case addition");
        }
        GuidedSellingUsecase added = questionAnswerService.upsertGuidedSellingUsecase(guidedSellingUsecase);
        return Response.ok(added).build();
    }

    @PUT
    @Path("/usecase/{usecase}/nextQuestion")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Get Next Question for Guided Selling",
        notes = "Get the next question given a set of answers to questions so far",
        response = NextQuestion.class,
        tags = { ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    @Beta
    public Response fetchNextQuestion(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @PathParam("usecase") String usecase,
        @Valid List<Answer> answersSoFar,
        @Context UriInfo uriInfo
    ) {
        checkForFeatureEnablement();
        return Response.ok(questionAnswerService.fetchNextQuestion(answersSoFar, usecase)).build();
    }

    @PUT
    @Path("/usecase/{usecase}/nextQuestions")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Get The next set of questions based on the answers provided so far",
        notes = "Get the ordered list of all relevant questions to answer based on the answers provided so far",
        response = NextQuestions.class,
        tags = { ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    @Beta
    public Response fetchNextQuestions(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @PathParam("usecase") String usecase,
        @Valid List<Answer> answersSoFar,
        @Context UriInfo uriInfo
    ) {
        checkForFeatureEnablement();
        return Response.ok(questionAnswerService.fetchNextQuestions(answersSoFar, usecase)).build();
    }

    @PUT
    @Path("/usecase/{usecase}/testQscript")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiOperation(
        value = "Test quote building Zeppa Q script",
        notes = "Given the use case, list of answers and Zeppa Q script try creating the order in dryrun mode",
        response = OrderJson.class,
        tags = { ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    @Beta
    public Response testQScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @PathParam("usecase") String usecase,
        @Valid @NotNull @FormDataParam("answers") List<Answer> answers,
        @Valid @NotNull @FormDataParam("qscript") String zeppaQScript,
        @Valid @NotNull @QueryParam("accountId") String accountId
    ) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("API not supported");
        }
        Order order = quoteBuilderService.testQScriptOnUsecase(accountId, usecase, answers, zeppaQScript);
        return Response.ok(orderMapper.orderToJson(order)).build();
    }

    @PUT
    @Path("/usecase/{usecase}/buildOrder")
    @ApiOperation(
        value = "Build an order using the guided selling answers",
        notes = "For a given use case this API allows building the order, given account id. you can also save the order",
        response = OrderJson.class,
        tags = { ResourceConstants.EXPERIMENTAL_TAG }
    )
    @AllowAllRoles
    @Beta
    public Response buildOrderFromAnswers(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @PathParam("usecase") String usecase,
        @Valid @NotNull List<Answer> answers,
        @Valid @NotNull @QueryParam("accountId") String accountId,
        @Valid @QueryParam("saveOrder") String saveOrder
    ) {
        boolean shouldSaveOrder = StringUtils.isNotBlank(saveOrder) && "true".equalsIgnoreCase(saveOrder);
        BuildOrderFromAnswersRequest buildOrderRequest = ImmutableBuildOrderFromAnswersRequest.builder()
            .accountId(accountId)
            .answers(answers)
            .usecaseIdentifier(usecase)
            .shouldSaveOrder(shouldSaveOrder)
            .build();
        Order order = quoteBuilderService.buildOrderFromAnswers(buildOrderRequest);
        return Response.ok(orderMapper.orderToJson(order)).build();
    }

    @GET
    @Path("/usecase/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowAllRoles
    @Beta
    public Response getUsecase(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @PathParam("id") String usecaseId,
        @Context UriInfo uriInfo
    ) {
        checkForFeatureEnablement();
        GuidedSellingUsecase stored = questionAnswerService
            .getGuidedSellingUsecase(usecaseId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.GUIDED_SELLING_USECASE, usecaseId));
        return Response.ok(stored).build();
    }

    @GET
    @Path("/usecase/{id}/qscript")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces({ MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN })
    @AllowRoles(Role.ADMIN)
    @Beta
    public Response getUsecaseScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @PathParam("id") String usecaseId,
        @Context UriInfo uriInfo
    ) {
        checkForFeatureEnablement();
        String script = questionAnswerService
            .getQScript(usecaseId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.GUIDED_SELLING_QSCRIPT, usecaseId));
        return Response.ok(script).build();
    }

    @PUT
    @Path("/usecase/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles(Role.ADMIN)
    @Beta
    public Response updateUsecase(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid GuidedSellingUsecase guidedSellingUsecase,
        @NotNull @Valid @PathParam("id") String usecaseId,
        @Context UriInfo uriInfo
    ) {
        checkForFeatureEnablement();
        if (StringUtils.isBlank(usecaseId)) {
            throw new InvalidInputException("Usecase id cannot be blank");
        }
        if (!usecaseId.equals(guidedSellingUsecase.getId())) {
            String message = String.format("Path id: %s and payload id: %s do not match", usecaseId, guidedSellingUsecase.getId());
            throw new InvalidInputException(message);
        }
        GuidedSellingUsecase updated = questionAnswerService.upsertGuidedSellingUsecase(guidedSellingUsecase);
        return Response.ok(updated).build();
    }

    @PUT
    @Path("/usecase/{id}/qscript")
    @Consumes(MediaType.TEXT_PLAIN)
    @Produces({ MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN })
    @AllowRoles(Role.ADMIN)
    @Beta
    public Response putQScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @ApiParam String qscript,
        @NotNull @Valid @PathParam("id") String usecaseId,
        @Context UriInfo uriInfo
    ) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("API not supported");
        }
        checkForFeatureEnablement();
        if (StringUtils.isBlank(usecaseId)) {
            throw new InvalidInputException("Usecase id cannot be blank");
        }

        Optional<String> qscriptOptional = questionAnswerService.getQScript(usecaseId);

        if (qscriptOptional.isPresent()) {
            questionAnswerService.updateQScriptForGuidedSelling(usecaseId, qscript);
            return Response.ok(qscript).build();
        }

        questionAnswerService.addQScriptToGuidedSelling(usecaseId, qscript);
        return Response.ok(qscript).build();
    }

    @DELETE
    @Path("/usecase/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles(Role.ADMIN)
    @Beta
    public Response deleteUsecase(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @PathParam("id") String usecaseId,
        @Context UriInfo uriInfo
    ) {
        checkForFeatureEnablement();
        Validator.validateStringNotBlank(usecaseId, "usecase id cannot be blank");
        GuidedSellingUsecase deleted = questionAnswerService.deleteGuidedSellingUsecase(usecaseId);
        return Response.ok(deleted).build();
    }

    private void checkForFeatureEnablement() {
        if (!featureService.isEnabled(Feature.QUOTE_BUILDER)) {
            throw new InvalidInputException("Quote builder feature not enabled");
        }
    }
}
