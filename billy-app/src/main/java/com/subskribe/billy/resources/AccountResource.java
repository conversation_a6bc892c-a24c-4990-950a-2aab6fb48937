package com.subskribe.billy.resources;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.bulk.service.AccountBulkOperationService;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.model.AccountPaymentConfiguration;
import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.account.model.AccountPaymentMethodStatus;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodService;
import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.crm.CrmType;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.exception.AddressValidationException;
import com.subskribe.billy.exception.BillyErrorMessage;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.resources.json.PaginatedItemsResponse;
import com.subskribe.billy.resources.json.account.AccountAddressJson;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.account.AccountMapper;
import com.subskribe.billy.resources.json.account.AccountPaymentConfigurationJson;
import com.subskribe.billy.resources.json.account.AccountPaymentMethodJson;
import com.subskribe.billy.resources.json.account.CrmAccountImportResponse;
import com.subskribe.billy.resources.json.account.ErpInputJson;
import com.subskribe.billy.resources.json.metrics.MetricsJson;
import com.subskribe.billy.resources.json.metrics.MetricsJsonMapper;
import com.subskribe.billy.resources.json.shared.TimeSeriesAmountJson;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.resources.shared.PaginationResponseJson;
import com.subskribe.billy.resources.shared.ResponseUtility;
import com.subskribe.billy.salesroom.intelligent.model.response.IntelligentSalesRoomOverviewResponse;
import com.subskribe.billy.salesroom.intelligent.service.IntelligentSalesRoomService;
import com.subskribe.billy.shared.SubskribeUrlGenerator;
import com.subskribe.billy.shared.enums.AccountType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.time.TimeZoneData;
import com.subskribe.billy.time.TimeZoneService;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.validation.Validator;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.InputStream;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataParam;
import org.mapstruct.factory.Mappers;

@Path("/accounts")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class AccountResource {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountResource.class);

    private final AccountService accountService;
    private final AccountGetService accountGetService;
    private final AccountPaymentMethodGetService accountPaymentMethodGetService;
    private final AccountPaymentMethodService accountPaymentMethodService;
    private final AccountMapper accountMapper;
    private final MetricsService metricsService;
    private final MetricsJsonMapper metricsJsonMapper;
    private final BillyConfiguration billyConfiguration;
    private final AccountBulkOperationService accountBulkOperationService;

    private final IntelligentSalesRoomService intelligentSalesRoomService;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public AccountResource(
        AccountService accountService,
        AccountGetService accountGetService,
        AccountPaymentMethodGetService accountPaymentMethodGetService,
        AccountPaymentMethodService accountPaymentMethodService,
        MetricsService metricsService,
        BillyConfiguration billyConfiguration,
        AccountBulkOperationService accountBulkOperationService,
        IntelligentSalesRoomService intelligentSalesRoomService,
        TenantIdProvider tenantIdProvider
    ) {
        this.accountService = accountService;
        this.accountGetService = accountGetService;
        this.accountPaymentMethodGetService = accountPaymentMethodGetService;
        this.accountPaymentMethodService = accountPaymentMethodService;
        this.metricsService = metricsService;
        this.billyConfiguration = billyConfiguration;
        this.accountBulkOperationService = accountBulkOperationService;
        this.intelligentSalesRoomService = intelligentSalesRoomService;
        this.tenantIdProvider = tenantIdProvider;
        accountMapper = Mappers.getMapper(AccountMapper.class);
        metricsJsonMapper = Mappers.getMapper(MetricsJsonMapper.class);
    }

    @GET
    @Path("/timezones")
    @ApiOperation(
        value = "Fetch the available timezones",
        notes = "Returns a list of timezones available to be set for an account",
        response = TimeZoneData.class,
        responseContainer = "List",
        hidden = true
    )
    @AllowAllRoles
    public Response getTimeZones(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        var availableTimeZones = TimeZoneService.getAvailableTimeZones();
        return Response.ok(availableTimeZones).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Add a new account",
        notes = "Create an account with the specified parameters. On success, the id of the newly created account is returned",
        response = AccountJson.class,
        tags = { "Accounts" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER, Role.BILLING_CLERK })
    public Response addAccount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid AccountJson accountJson,
        @Context UriInfo uriInfo
    ) {
        var account = accountMapper.jsonToAccount(accountJson);
        var addedAccount = accountService.addAccount(account, accountMapper.jsonToAccountAddress(accountJson.getAddress()));
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(addedAccount.getAccountId());
        AccountJson addedAccountJson = accountMapper.accountToJson(addedAccount);
        return Response.created(builder.build()).entity(addedAccountJson).build();
    }

    @PUT
    @Path("/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Update an account", notes = "Updates an existing account with the specified parameters", tags = { "Accounts" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER, Role.ACCOUNTANT })
    public Response updateAccount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") String accountId,
        @NotNull @Valid AccountJson accountJson
    ) {
        var account = accountMapper.jsonToAccount(accountJson);
        account.setAccountId(accountId);
        accountService.updateAccount(account, accountMapper.jsonToAccountAddress(accountJson.getAddress()));
        return Response.ok().build();
    }

    @DELETE
    @Path("/{id}")
    @ApiOperation(value = "Delete an account", notes = "Deletes the account associated with the passed ID", tags = { "Accounts" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    public Response deleteAccount(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @PathParam("id") String accountId) {
        var account = accountService.deleteAccount(accountId);
        var accountJson = accountMapper.accountToJson(account);
        return Response.ok().entity(accountJson).build();
    }

    @GET
    @ApiOperation(
        value = "Get all accounts",
        notes = "Returns a paginated list of accounts",
        response = PaginatedAccountsResponse.class,
        tags = { "Accounts" }
    )
    @AllowAllRoles
    public Response getAccounts(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("cursor") @ApiParam(
            name = "cursor",
            value = "A string token is used to fetch next set of results. If not provided, the first page of results will be returned. Use the 'next_cursor' value from the previous response to fetch the next page."
        ) UUID cursor,
        @QueryParam("limit") @ApiParam(
            name = "limit",
            value = "An integer specifying the maximum number of results to return per page. Defaults to 10 if not provided."
        ) int limit,
        @QueryParam("type") @ApiParam(
            name = "type",
            value = "The type of accounts to retrieve. Allowed values are:  ALL: Includes all account types. RESELLER: Includes only reseller accounts. NON_RESELLER: Includes only non-reseller accounts."
        ) String type
    ) {
        var paginationQueryParams = new PaginationQueryParams(cursor, limit);
        AccountType accountType = StringUtils.isBlank(type)
            ? AccountType.ALL
            : Validator.enumFromString(type.toUpperCase(), AccountType.class, "type");
        var accounts = accountGetService.getAccounts(accountType, paginationQueryParams);
        var paginationJson = new PaginationResponseJson<>(accounts, paginationQueryParams.getLimit(), accountMapper::accountsToJson, Account::getId);
        return Response.ok(paginationJson).build();
    }

    @GET
    @Path("/{id}")
    @ApiOperation(
        value = "Get an account by id",
        notes = "Retrieves detailed information about a specific account using its unique identifier. It could be Account_ID, CRM_ID or External_ID. This endpoint provides comprehensive data for a particular account, enabling users to access full account details.",
        response = AccountJson.class,
        tags = { "Accounts" }
    )
    @AllowAllRoles
    public Response getAccount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(name = "id", value = "Uniquely identifies the Account") String id,
        @QueryParam("idType") @ApiParam(
            name = "idType",
            value = "Specifies the type of ID being used. Allowed values are account_id: The default account ID. crm_id: The CRM (Customer Relationship Management) ID external_id: An external system's ID for the account. Default is account_id"
        ) AccountIdType idType
    ) {
        if (idType == null) {
            idType = AccountIdType.ACCOUNT_ID;
        }

        Account account =
            switch (idType) {
                case CRM_ID -> {
                    var accountByCrmId = accountGetService.getAccountByCrmId(id);
                    if (accountByCrmId.isEmpty()) {
                        throw new ObjectNotFoundException(BillyObjectType.ACCOUNT, id);
                    }
                    yield accountByCrmId.get();
                }
                case EXTERNAL_ID -> {
                    var accountByExternalId = accountGetService.getAccountByExternalId(id);
                    if (accountByExternalId.isEmpty()) {
                        throw new ObjectNotFoundException(BillyObjectType.ACCOUNT, id);
                    }
                    yield accountByExternalId.get();
                }
                case ACCOUNT_ID -> accountGetService.getAccount(id);
            };

        AccountJson accountJson = accountMapper.accountToJson(account);
        if (account.getAddressId() != null) {
            Optional<AccountAddress> accountAddress = accountGetService.getAccountAddress(account.getAddressId());
            accountAddress.ifPresent(address -> accountJson.setAddress(accountMapper.accountAddressToJson(address)));
        }

        return Response.ok(accountJson).build();
    }

    @GET
    @Path("/{id}/metrics")
    @ApiOperation(
        value = "Returns metrics for the specified account",
        notes = "Fetches metrics such as ARR, TCV, etc for the specified account as of the specified target date",
        response = MetricsJson.class,
        tags = { "Accounts" }
    )
    @AllowAllRoles
    public Response getAccountMetrics(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") String accountId,
        @QueryParam("targetDate") Long targetDate,
        @QueryParam("forceRecalculate") Boolean forceRecalculate
    ) {
        Instant target = targetDate == null ? Instant.now() : Instant.ofEpochSecond(targetDate);
        Metrics accountMetrics = metricsService.getAccountMetrics(accountId, target, BooleanUtils.isTrue(forceRecalculate));
        MetricsJson json = metricsJsonMapper.metricsToJson(accountMetrics);
        return Response.ok(json).build();
    }

    @GET
    @Path("/{id}/arrTrend")
    @ApiOperation(value = "Test API for E2E testing", hidden = true, tags = { "Accounts" })
    @AllowAllRoles
    public Response getTestAccountArrTrend(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @PathParam("id") String accountId) {
        validateTestApiIsCalledFromLocalOrCiOnly();
        var arrTrend = metricsService.getAccountARRTrend(accountGetService.getAccount(accountId));
        var json = arrTrend.stream().map(metricsJsonMapper::timeSeriesElementToJson).collect(Collectors.toList());
        return Response.ok(json).build();
    }

    @GET
    @Path("/{id}/pendingArr")
    @ApiOperation(
        value = "Returns the pending ARR",
        notes = "Fetches the pending ARR for the specified account id as a time series",
        response = TimeSeriesAmountJson.class,
        responseContainer = "List",
        hidden = true,
        tags = { "Accounts" }
    )
    @AllowAllRoles
    public Response getAccountPendingArr(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @PathParam("id") String accountId) {
        var pendingArrTrend = metricsService.getPendingAccountARRTrend(accountGetService.getAccount(accountId));
        var json = pendingArrTrend.stream().map(metricsJsonMapper::timeSeriesElementToJson).collect(Collectors.toList());
        return Response.ok(json).build();
    }

    @POST
    @Path("/{accountId}/contacts")
    @ApiOperation(
        value = "Add a contact for an account",
        notes = "Creates and adds a new contact for the specified account and returns the new contact ID.",
        response = AccountContactJson.class,
        tags = { "Accounts" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER, Role.BILLING_CLERK })
    public Response addAccountContact(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") @ApiParam("value = Uniquely identifies the account") String accountId,
        @NotNull @Valid AccountContactJson accountContactJson,
        @QueryParam("skipAddressValidation") @ApiParam("value = perform basic address validation") boolean skipAddressValidation,
        @QueryParam("strictValidation") @ApiParam("value = require the address to match a canonical address, if it exists") boolean strictValidation,
        @Context UriInfo uriInfo
    ) {
        try {
            AccountContactJson contactJson = addContactToAccount(accountContactJson, accountId, skipAddressValidation, strictValidation);
            UriBuilder builder = uriInfo.getAbsolutePathBuilder();
            builder.path(contactJson.getId());
            return Response.created(builder.build()).entity(contactJson).build();
        } catch (AddressValidationException ex) {
            return getAddressValidationErrorResponse(ex);
        }
    }

    private AccountContact mapJsonToAccountContactForAccountCreation(AccountContactJson accountContactJson, String accountId) {
        AccountContact accountContact = accountMapper.jsonToAccountContact(accountContactJson);
        accountContact.setId(null);
        accountContact.setAccountId(accountId);
        return accountContact;
    }

    private AccountContactJson addContactToAccount(
        AccountContactJson newContact,
        String accountId,
        boolean skipAddressValidation,
        boolean strictValidation
    ) {
        AccountContact accountContact = mapJsonToAccountContactForAccountCreation(newContact, accountId);
        AccountContact contact = accountService.addContact(accountContact, skipAddressValidation, strictValidation);
        return accountMapper.accountContactToJson(contact);
    }

    @POST
    @Path("/salesRoom/{shareLink}/contacts")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Create account contact via share link",
        notes = "Creates account contact via share link",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response addAccountContactViaShareLink(
        @NotEmpty @PathParam("shareLink") String shareLink,
        @NotNull @Valid AccountContactJson accountContactJson,
        @QueryParam("skipAddressValidation") @ApiParam("value = perform basic address validation") boolean skipAddressValidation,
        @QueryParam("strictValidation") @ApiParam("value = require the address to match a canonical address, if it exists") boolean strictValidation,
        @QueryParam("strictValidation") @ApiParam("value = Whether or not to add the contact for the reseller") boolean addToReseller
    ) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);
        return TenantContextInjector.callInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            addAccountContactInSalesRoom(salesRoom, accountContactJson, skipAddressValidation, strictValidation, addToReseller)
        );
    }

    @GET
    @Path("/salesRoom/{shareLink}/contacts")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Get account contacts via sales room share link",
        notes = "Gets account contacts via sales room share link",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response getAccountContactViaShareLink(@NotEmpty @PathParam("shareLink") String shareLink) {
        String tenantId = intelligentSalesRoomService.getTenantIdByShareLink(shareLink);
        String accountId = intelligentSalesRoomService.getAccountIdByShareLink(shareLink);
        return TenantContextInjector.callInTenantContext(tenantId, tenantIdProvider, () -> {
            List<AccountContact> accountContacts = accountGetService.getAccountContacts(accountId, true);
            List<AccountContactJson> accountContactsJson = accountMapper.accountContactsToJson(accountContacts);
            return Response.ok(accountContactsJson).build();
        });
    }

    private Response addAccountContactInSalesRoom(
        IntelligentSalesRoomOverviewResponse salesRoom,
        AccountContactJson accountContactJson,
        boolean skipAddressValidation,
        boolean strictValidation,
        boolean addToReseller
    ) {
        try {
            String accountId = getAccountIdForSalesRoomContactUpsert(salesRoom, addToReseller);
            AccountContactJson response = addContactToAccount(accountContactJson, accountId, skipAddressValidation, strictValidation);
            return Response.ok(response).build();
        } catch (AddressValidationException ex) {
            return getAddressValidationErrorResponse(ex);
        }
    }

    private String getAccountIdForSalesRoomContactUpsert(IntelligentSalesRoomOverviewResponse salesRoom, boolean updateReseller) {
        if (!updateReseller) {
            return salesRoom.getAccountId();
        }

        return intelligentSalesRoomService
            .getResellerAccountId(salesRoom.getSalesRoomId().toString())
            .orElseThrow(() -> new InvalidInputException("No reseller account found for sales room."));
    }

    @PUT
    @Path("/{accountId}/erp")
    @ApiOperation(
        value = "Update account ERP details",
        notes = "Update ERP details for an account specified by the account id",
        tags = { "Accounts" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response addErpDetails(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") @ApiParam("value = Uniquely identifies the account") String accountId,
        @QueryParam("override") boolean override,
        @NotNull @Valid ErpInputJson erpInputJson
    ) {
        var erpId = erpInputJson.getErpId();
        accountService.updateAccountErpId(accountId, erpId, override);
        return Response.ok().build();
    }

    @PUT
    @Path("/{accountId}/contacts/{contactId}")
    @ApiOperation(
        value = "Update a contact",
        notes = "Updates the contact specified by the account id and contact id with the passed information",
        tags = { "Accounts" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    public Response updateAccountContact(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") String accountId,
        @PathParam("contactId") String contactId,
        @NotNull @Valid AccountContactJson accountContactJson,
        @QueryParam("skipAddressValidation") boolean skipAddressValidation,
        @QueryParam("strictValidation") boolean strictValidation
    ) {
        var accountContact = accountMapper.jsonToAccountContact(accountContactJson);
        accountContact.setContactId(contactId);
        accountContact.setAccountId(accountId);

        try {
            accountService.updateContact(accountContact, skipAddressValidation, strictValidation);
            return Response.ok().build();
        } catch (AddressValidationException ex) {
            return getAddressValidationErrorResponse(ex);
        }
    }

    private Response updateAccountContactInSalesRoom(
        IntelligentSalesRoomOverviewResponse salesRoom,
        AccountContactJson accountContactJson,
        String contactId,
        boolean skipAddressValidation,
        boolean strictValidation,
        boolean addToReseller
    ) {
        try {
            String accountId = getAccountIdForSalesRoomContactUpsert(salesRoom, addToReseller);
            AccountContact accountContact = accountMapper.jsonToAccountContact(accountContactJson);
            accountContact.setContactId(contactId);
            accountContact.setAccountId(accountId);
            accountService.updateContact(accountContact, skipAddressValidation, strictValidation);
            return Response.ok().build();
        } catch (AddressValidationException ex) {
            return getAddressValidationErrorResponse(ex);
        }
    }

    @PUT
    @Path("/salesRoom/{shareLink}/contacts/{contactId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Update account contact via share link",
        notes = "Updates account contact via share link",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response updateAccountContactViaShareLink(
        @NotEmpty @PathParam("shareLink") String shareLink,
        @NotEmpty @PathParam("contactId") String contactId,
        @NotNull @Valid AccountContactJson accountContactJson,
        @QueryParam("skipAddressValidation") @ApiParam("value = perform basic address validation") boolean skipAddressValidation,
        @QueryParam("strictValidation") @ApiParam("value = require the address to match a canonical address, if it exists") boolean strictValidation,
        @QueryParam("strictValidation") @ApiParam("value = Whether or not to update the contact for the reseller") boolean updateReseller
    ) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);
        return TenantContextInjector.callInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            updateAccountContactInSalesRoom(salesRoom, accountContactJson, contactId, skipAddressValidation, strictValidation, updateReseller)
        );
    }

    @DELETE
    @Path("/{accountId}/contacts/{contactId}")
    @ApiOperation(value = "Delete a contact", notes = "Deletes the contact specified by the account id and contact id", tags = { "Accounts" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    public Response deleteAccountContact(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("contactId") String contactId,
        @PathParam("accountId") String accountId
    ) {
        var deletedContact = accountService.deleteContact(contactId);
        return Response.ok().entity(accountMapper.accountContactToJson(deletedContact)).build();
    }

    @GET
    @Path("/{accountId}/contacts")
    @ApiOperation(
        value = "Get contacts for an account",
        notes = "Returns a list of contacts associated with the specified account id",
        response = AccountContactJson.class,
        responseContainer = "List",
        tags = { "Accounts" }
    )
    @AllowAllRoles
    public Response getAccountContacts(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") @ApiParam(
            name = "accountId",
            value = "Uniquely identifies the account for which contacts are being retrieved."
        ) String accountId,
        @QueryParam("expand") @ApiParam(
            name = "expand",
            value = "When set to true, expands the response to include additional details about each contact, such as address, external id, erp id, fullName. Default is false."
        ) boolean expand
    ) {
        var accountContacts = accountGetService.getAccountContacts(accountId, expand);
        var accountContactsJson = accountMapper.accountContactsToJson(accountContacts);
        return Response.ok(accountContactsJson).build();
    }

    @GET
    @Path("/{accountId}/contacts/{contactId}")
    @ApiOperation(
        value = "Gets contact details",
        notes = "Returns the details of the specified contact",
        response = AccountContactJson.class,
        tags = { "Accounts" }
    )
    @AllowAllRoles
    public Response getAccountContact(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") @ApiParam(
            name = "accountId",
            value = "Uniquely identifies the account for which contacts are being retrieved."
        ) String accountId,
        @PathParam("contactId") @ApiParam(name = "contactId", value = "Uniquely identifies the contact.") String contactId
    ) {
        var accountContact = accountGetService.getContact(contactId);
        var contactJson = accountMapper.accountContactToJson(accountContact);
        return Response.ok(contactJson).build();
    }

    @POST
    @Path("/{accountId}/paymentMethods")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Add a payment method to an account",
        notes = "Add a payment method to the specified account and return its ID",
        hidden = true,
        response = AccountPaymentMethodJson.class,
        tags = { "Accounts" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER, Role.BILLING_CLERK })
    public Response addAccountPaymentMethod(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid AccountPaymentMethodJson accountPaymentMethodJson,
        @PathParam("accountId") String accountId,
        @Context UriInfo uriInfo
    ) {
        var accountPaymentMethod = accountMapper.jsonToAccountPaymentMethod(accountPaymentMethodJson);
        accountPaymentMethod.setAccountId(accountId);
        AccountPaymentMethod savedPaymentMethod = accountPaymentMethodService.addAccountPaymentMethod(accountPaymentMethod);
        UUID paymentMethodId = savedPaymentMethod.getId();
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        AccountPaymentMethodJson savedPaymentMethodJson = accountMapper.accountPaymentMethodToJson(savedPaymentMethod);
        builder.path(paymentMethodId.toString());
        return Response.created(builder.build()).entity(savedPaymentMethodJson).build();
    }

    @GET
    @Path("/{accountId}/paymentMethods/{id}")
    @ApiOperation(
        value = "Get the details of a payment method",
        notes = "Returns the details of the payment method for the specified account id and payment method id",
        hidden = true,
        response = AccountPaymentMethodJson.class,
        tags = { "Accounts" }
    )
    @AllowAllRoles
    public Response getPaymentMethod(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") String accountId,
        @NotNull @PathParam("id") UUID paymentMethodId
    ) {
        var paymentMethod = accountPaymentMethodGetService.getPaymentMethod(accountId, paymentMethodId);
        if (paymentMethod.isEmpty() || paymentMethod.get().getStatus() != AccountPaymentMethodStatus.ACTIVE) {
            throw new ObjectNotFoundException(BillyObjectType.PAYMENT_METHOD, paymentMethodId.toString());
        }

        var paymentMethodJson = accountMapper.accountPaymentMethodToJson(paymentMethod.get());
        return Response.ok(paymentMethodJson).build();
    }

    @GET
    @Path("/{accountId}/paymentMethods/internal/{id}")
    @ApiOperation(
        value = "Get the details of a payment method",
        notes = "Returns the details of the payment method for the specified account id and payment method id",
        hidden = true,
        response = AccountPaymentMethodJson.class,
        tags = { "Accounts" }
    )
    @AllowAllRoles
    public Response getPaymentMethodInternal(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") String accountId,
        @NotNull @PathParam("id") UUID paymentMethodId
    ) {
        var paymentMethod = accountPaymentMethodGetService.getPaymentMethod(accountId, paymentMethodId);
        if (paymentMethod.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.PAYMENT_METHOD, paymentMethodId.toString());
        }
        return Response.ok(paymentMethod).build();
    }

    @GET
    @Path("/{accountId}/paymentMethods")
    @ApiOperation(
        value = "Get the payment methods for an account",
        notes = "Returns a list of payment methods for the specified account id",
        hidden = true,
        response = AccountPaymentMethodJson.class,
        responseContainer = "List",
        tags = { "Accounts" }
    )
    @AllowAllRoles
    public Response getAccountPaymentMethods(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") String accountId
    ) {
        var paymentMethods = accountPaymentMethodGetService.getPaymentMethodsInAccount(accountId);
        var accountPaymentMethodsJson = accountMapper.accountPaymentMethodsToJson(paymentMethods);
        return Response.ok(accountPaymentMethodsJson).build();
    }

    private Response getAddressValidationErrorResponse(AddressValidationException ex) {
        var suggestedAddress = ex.getSuggestedAddress();
        var errorMessage = getAddressValidationError(suggestedAddress);
        return Response.status(Response.Status.BAD_REQUEST).entity(errorMessage).build();
    }

    private BillyErrorMessage<AccountAddressJson> getAddressValidationError(AccountAddress suggestedAddress) {
        if (suggestedAddress != null) {
            return new BillyErrorMessage<>(
                Response.Status.BAD_REQUEST.getStatusCode(),
                "address_validation_warning",
                "Address validation produced suggested alternate address",
                accountMapper.accountAddressToJson(suggestedAddress)
            );
        } else {
            return new BillyErrorMessage<>(Response.Status.BAD_REQUEST.getStatusCode(), "invalid_address");
        }
    }

    @POST
    @Path("/crm/import")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles(value = { Role.CRM, Role.SALES, Role.SALES_MANAGER, Role.FINANCE, Role.ADMIN }, cascade = true)
    @ApiOperation(
        value = "Import an account from a CRM",
        notes = "Ensures an account exists which matches the passed details." +
        "If an account exists that has a matching CRM Id, it will be updated, if not, it will be created",
        response = CrmAccountImportResponse.class,
        tags = { "Accounts" }
    )
    public Response importCrmAccount(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @NotNull @Valid AccountJson accountJson) {
        LOGGER.info("CRM import account API called with input: {}", accountJson);
        String newCurrencyCode = accountJson.getCurrency();
        Account account = accountMapper.jsonToAccount(accountJson);
        Optional<Account> existingSubskribeAccount = accountGetService.getAccountByCrmId(accountJson.getCrmId());
        existingSubskribeAccount.ifPresent(existing -> {
            account.setAccountId(existing.getAccountId());
            // Overwrite currency only if supplied in payload; otherwise, inherit existing currency.
            if (StringUtils.isNotBlank(newCurrencyCode)) {
                account.setCurrency(SupportedCurrency.validateCurrency(newCurrencyCode));
            } else {
                account.setCurrency(existing.getCurrency());
            }
        });
        Account importedAccount = accountService.upsertAccount(account);
        String accountPageUrl = SubskribeUrlGenerator.getUiUrlForAccount(billyConfiguration.getSiteUrl(), importedAccount.getAccountId());
        CrmAccountImportResponse response = new CrmAccountImportResponse(accountPageUrl, importedAccount.getAccountId());
        LOGGER.info("CRM import account API response: {}", response);
        return Response.ok(response).build();
    }

    @POST
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @Path("/bulk/updateCrmId")
    @ApiOperation(value = "Bulk update CRM ID", notes = "API to bulk update CRM ID on accounts", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response bulkUpdateAccountCrmId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @FormDataParam("file") InputStream inputStream,
        @FormDataParam("file") FormDataBodyPart formDataBodyPart,
        @QueryParam("crmType") @NotNull CrmType crmType,
        @QueryParam("isDryRun") String isDryRunString,
        @Context UriInfo uriInfo
    ) {
        boolean isDryRun = !Boolean.FALSE.toString().equalsIgnoreCase(isDryRunString);
        Pair<InputStream, String> importedStreamDetails = accountBulkOperationService.updateAccountCrmIds(
            formDataBodyPart,
            inputStream,
            crmType,
            isDryRun
        );
        return ResponseUtility.okResponseFromInputStreamFileNamePair(importedStreamDetails, "text/csv");
    }

    @PUT
    @Path("/{accountId}/crmId")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Update CRM ID", notes = "API to update CRM ID for an account", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response updateAccountCrmId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") @NotEmpty String accountId,
        @QueryParam("crmId") @NotEmpty String crmId
    ) {
        accountService.updateAccountCrmId(accountId, crmId);
        return Response.ok().build();
    }

    private void validateTestApiIsCalledFromLocalOrCiOnly() {
        if (billyConfiguration.isLocalOrCi()) {
            return;
        }

        throw new UnsupportedOperationException("Test API call is not allowed outside of local/ci.");
    }

    @POST
    @Path("/{accountId}/paymentConfig")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Upsert account payment configuration",
        notes = "Creates or updates the payment configuration for the specified account",
        response = AccountPaymentConfigurationJson.class,
        tags = { "Accounts" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    public Response upsertAccountPaymentConfig(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") String accountId,
        @NotNull @Valid AccountPaymentConfigurationJson configJson
    ) {
        AccountPaymentConfiguration config = accountMapper.jsonToAccountPaymentConfiguration(configJson);
        var updatedConfig = accountService.upsertAccountPaymentConfiguration(accountId, config);
        var updatedConfigJson = accountMapper.accountPaymentConfigurationToJson(updatedConfig);
        return Response.ok(updatedConfigJson).build();
    }

    @GET
    @Path("/{accountId}/paymentConfig")
    @ApiOperation(
        value = "Get account payment configuration",
        notes = "Retrieves the payment configuration for the specified account",
        response = AccountPaymentConfigurationJson.class,
        tags = { "Accounts" }
    )
    @AllowAllRoles
    public Response getAccountPaymentConfig(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountId") String accountId
    ) {
        Optional<AccountPaymentConfiguration> config = accountGetService.getAccountPaymentConfiguration(accountId);
        if (config.isEmpty()) {
            return Response.status(Response.Status.NO_CONTENT).build();
        }

        var configJson = accountMapper.accountPaymentConfigurationToJson(config.get());
        return Response.ok(configJson).build();
    }

    public enum AccountIdType {
        ACCOUNT_ID,
        CRM_ID,
        EXTERNAL_ID,
    }

    private static class PaginatedAccountsResponse extends PaginatedItemsResponse<AccountJson> {}
}
