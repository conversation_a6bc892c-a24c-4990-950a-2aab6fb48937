package com.subskribe.billy.resources.json.order;

import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.model.CompositeOrderDetail;
import com.subskribe.billy.compositeorder.model.ImmutableCompositeOrderDetail;
import com.subskribe.billy.graphql.order.CancelAndRestructureRequest;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.mapper.BaseMapper;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public abstract class CompositeOrderMapper implements BaseMapper {

    @Mapping(source = "compositeOrderId", target = "id")
    protected abstract ImmutableCompositeOrderJson compositeOrderToJson(CompositeOrder compositeOrder);

    public CompositeOrderJson compositeOrderToJson(CompositeOrder compositeOrder, List<OrderJson> orders, String accountId) {
        ImmutableCompositeOrderJson builder = compositeOrderToJson(compositeOrder);
        return builder.withOrders(orders);
    }

    @Mapping(source = "compositeOrderId", target = "id")
    protected abstract CompositeOrderDetail compositeOrderToDetail(CompositeOrder compositeOrder);

    public CompositeOrderDetail compositeOrderToDetail(CompositeOrder compositeOrder, List<String> orderIds, List<OrderDetail> orderDetails) {
        CompositeOrderDetail compositeOrderDetail = compositeOrderToDetail(compositeOrder);
        OrderDetail orderDetail = orderDetails
            .stream()
            .filter(od -> (od.getOrderType() == OrderType.RESTRUCTURE || od.getOrderType() == OrderType.RENEWAL))
            .findFirst()
            .orElse(null);
        AccountJson account = orderDetail != null ? orderDetail.getAccount() : null;
        return ImmutableCompositeOrderDetail.builder().from(compositeOrderDetail).orderIds(orderIds).orders(orderDetails).account(account).build();
    }

    @Mapping(source = "restructureForSubscriptionId", target = "subscriptionId")
    public abstract CancelAndRestructureRequest orderRequestJsonToCancelAndRestructureRequest(OrderRequestJson orderRequestJson);
}
