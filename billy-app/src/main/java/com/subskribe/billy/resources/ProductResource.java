package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogService;
import com.subskribe.billy.report.service.ReportService;
import com.subskribe.billy.resources.json.product.ProductInputJson;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.resources.json.product.ProductMapper;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.resources.shared.PaginationResponseJson;
import com.subskribe.billy.resources.shared.ResponseUtility;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.lang3.tuple.Pair;
import org.mapstruct.factory.Mappers;

@Path("/products")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class ProductResource {

    private static final String PRODUCT_CATALOG_EXPORT_ID = "catalog_export";

    private final ProductCatalogService productCatalog;

    private final ProductCatalogGetService productCatalogGetService;

    private final ReportService reportService;

    private final ProductMapper productMapper;

    @Inject
    public ProductResource(ProductCatalogService productCatalog, ProductCatalogGetService productCatalogGetService, ReportService reportService) {
        this.productCatalog = productCatalog;
        this.productCatalogGetService = productCatalogGetService;
        this.reportService = reportService;
        productMapper = Mappers.getMapper(ProductMapper.class);
    }

    // hack to get the response documented
    private static class ProductJsonPaginationResponse extends PaginationResponseJson<Product, ProductJson> {

        private ProductJsonPaginationResponse(
            List<Product> resource,
            int limit,
            Function<List<Product>, List<ProductJson>> toJson,
            Function<Product, UUID> getId
        ) {
            super(resource, limit, toJson, getId);
        }
    }

    @GET
    @ApiOperation(
        value = "Get products",
        notes = "Returns all products for your tenant. The results on paginated. To fetch them all pass the cursor returned " +
        "from a call to the subsequent call until all results are fetched. Initially the cursor should not be specified.",
        response = ProductJsonPaginationResponse.class,
        tags = { "Product Catalog" }
    )
    @AllowAllRoles
    public Response getProducts(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("cursor") @ApiParam(name = "cursor", value = "cursor received from prior call") UUID cursor,
        @QueryParam("limit") @ApiParam(name = "limit", value = "number of results per page") int limit
    ) {
        var paginationQueryParams = new PaginationQueryParams(cursor, limit);
        var products = productCatalogGetService.getProducts(paginationQueryParams);
        var paginationJson = new PaginationResponseJson<>(products, paginationQueryParams.getLimit(), productMapper::productsToJson, Product::getId);
        return Response.ok(paginationJson).build();
    }

    @GET
    @Path("/{id}")
    @ApiOperation(
        value = "Get product details.",
        notes = "Gets the details of the specified product.",
        response = ProductJson.class,
        tags = { "Product Catalog" }
    )
    @AllowAllRoles
    public Response getProduct(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "id of the product", required = true) String productId
    ) {
        var product = productCatalogGetService.getProduct(productId);
        var productJson = productMapper.productToJson(product);
        return Response.ok(productJson).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Create a product",
        notes = "Creates a product for your tenant. On success the id of the product is returned.",
        response = ProductJson.class,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response addProduct(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(value = "product details json", required = true) ProductInputJson productJson,
        @Context UriInfo uriInfo
    ) {
        var product = productMapper.inputJsonToProduct(productJson);
        var savedProduct = productCatalog.addProduct(product);
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(savedProduct.getProductId());
        ProductJson savedProductJson = productMapper.productToJson(savedProduct);
        return Response.created(builder.build()).entity(savedProductJson).build();
    }

    @PUT
    @Path("/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Update product details", notes = "Updates the details of the specified product", tags = { "Product Catalog" })
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response updateProduct(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("id") @ApiParam(value = "id of the product", required = true) String productId,
        @NotNull @Valid @ApiParam(value = "product details json", required = true) ProductInputJson productJson
    ) {
        var product = productMapper.inputJsonToProduct(productJson);
        product.setProductId(productId);
        productCatalog.updateProduct(product);
        return Response.ok().build();
    }

    @DELETE
    @Path("/{id}")
    @ApiOperation(value = "Delete a product", notes = "Delete the specified product.", response = ProductJson.class, tags = { "Product Catalog" })
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response deleteProduct(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("id") @ApiParam(value = "id of the product", required = true) String productId
    ) {
        var deletedProduct = productCatalog.deleteProduct(productId);
        return Response.ok().entity(productMapper.productToJson(deletedProduct)).build();
    }

    @GET
    @Path("/export")
    @ApiOperation(value = "Export product catalog", notes = "Export the product catalog in CSV format.", tags = { "Product Catalog" })
    @AllowRoles(
        { Role.ADMIN, Role.FINANCE, Role.SALES_MANAGER, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.READ_ONLY, Role.EXECUTIVE }
    )
    public Response exportProductCatalog(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        Pair<InputStream, String> report = reportService.runPredefinedReport(PRODUCT_CATALOG_EXPORT_ID, Map.of());

        return ResponseUtility.okResponseFromInputStreamFileNamePair(report, "text/csv");
    }
}
