package com.subskribe.billy.resources.json.order;

public class OrderRequestDocumentation {

    static final String ID = "System-generated unique identifier for the order.";
    static final String ID_EXAMPLE = "ORD-AXBY123";

    static final String EXTERNAL_ID =
        "Unique external reference ID for the order that can be used for integration with other systems. This ID can't be reused on multiple orders.";
    static final String EXTERNAL_ID_EXAMPLE = "EXT-456789"; // TODO: Validate format

    static final String NAME = "Name or title of the order for easy identification.";
    static final String NAME_EXAMPLE = "CreativePro Monthly Subscription Order";

    static final String ACCOUNT_ID = "Unique identifier of the account associated with this order.";
    static final String ACCOUNT_ID_EXAMPLE = "ACCT-ADE4567";

    static final String ORDER_TYPE =
        """
        Type of order being placed. This value determines how the order will impact a subscription when it is executed.

        Supported values:
        * `NEW`: Create a new subscription.
        * `CANCEL`: Cancel an existing subscription.
        * `AMENDMENT`: Amend an existing subscription.
        * `RENEWAL`: Renew an existing subscription.
        * `RESTRUCTURE`: Restructure an existing subscription.""";
    static final String ORDER_TYPE_EXAMPLE = "NEW";

    static final String PAYMENT_TERM = "Payment terms for the order (e.g., Net 30, Net 60).";
    static final String PAYMENT_TERM_EXAMPLE = "Net 30";

    static final String SUBSCRIPTION_ID =
        """
        The ID of the subscription you want to amend, restructure, or cancel.
        To renew a subscription, use the `renewalForSubscriptionId` field to specify the ID of the subscription you want to renew.
        This field is not required while creating a new subscription.""";
    static final String SUBSCRIPTION_ID_EXAMPLE = "SUB-BCDE123";

    static final String SHIPPING_CONTACT_ID = "ID of the contact to use for shipping information.";
    static final String SHIPPING_CONTACT_ID_EXAMPLE = "CONT-XYZ7891"; // TODO: validate format

    static final String BILLING_CONTACT_ID = "ID of the contact to use for billing information.";
    static final String BILLING_CONTACT_ID_EXAMPLE = "CONT-ABC1234"; // TODO: validate format

    static final String PREDEFINED_DISCOUNTS = "Array of predefined discount objects to apply to the order.";

    static final String CREDITABLE_AMOUNTS =
        "Array of creditable amount which can be refunded when one time charges are cancelled/debooked during amendment or cancellation of a subscription";

    static final String LINE_TEMS = "Array of line item objects (i.e., charges) you want to add to this order.";

    static final String START_DATE = "Start date of the subscription in Unix timestamp format (seconds since epoch). This date is inclusive.";
    static final String START_DATE_EXAMPLE = "1672531200";

    static final String END_DATE =
        """
        End date of the subscription in Unix timestamp format (seconds since epoch). If not provided for `TERMED` subscriptions, it will be calculated based on `termLength`. \n
        **NOTE:** This date is exclusive. For example, if the subscription's start date is 1735689600 (January 1, 2025 00:00:00) and the term length is 1 year, specify the end date as 1767225600 (January 1, 2026 00:00:00).
        Since the date is exclusive, the subscription is still active at December 31, 2025 23:59:59 but will have ended at January 1, 2026 00:00:00.""";
    static final String END_DATE_EXAMPLE = "1704067200";

    static final String EXECUTED_ON =
        "Date when the order was executed in Unix timestamp format. Don't include this field if you're creating an order with `orderType` = `NEW`. This field is required only when you're creating an order to amend, renew, restructure, or cancel an existing subscription.";
    static final String EXECUTED_ON_EXAMPLE = "1672617600";

    static final String TERM_LENGTH = "Length of the subscription term (e.g., 12 months, 1 year) specified using the `recurrence` object.";

    static final String BILLING_CYCLE =
        "Frequency at which the customer will be billed (e.g., monthly, quarterly, annually). Specified using the `recurrence` object.";

    static final String BILLING_TERM =
        """
        Specifies when billing occurs relative to service delivery.

        Supported values:
        * `UP_FRONT`: Billing occurs before the product or service is delivered
        * `IN_ARREARS`: The customer is billed after receiving the product or service.""";
    static final String BILLING_TERM_EXAMPLE = "UP_FRONT";

    static final String BILLING_ANCHOR_DATE =
        "Specific date to anchor billing cycles to, in Unix timestamp format (seconds since epoch). Useful for aligning billing with specific dates (e.g., first of the month).";
    static final String BILLING_ANCHOR_DATE_EXAMPLE = "1672531200";

    static final String RAMP_INTERVAL =
        "Array of timestamps (in Unix timestamp format) defining intervals for ramped pricing schedules. Used when implementing gradual quantity or price changes over time.";
    static final String RAMP_INTERVAL_EXAMPLE = "[1672531200, 1680307200, 1688083200]";

    static final String ORDER_FORM_TEMPLATE_IDS = "Array of document template IDs to use for generating order forms. Specified as UUIDs.";
    static final String ORDER_FORM_TEMPLATE_IDS_EXAMPLE = "[a7b8c3d4-e5f6-4a1b-9c2d-3e4f5a6b7c8d5, f1e2d3c4-b5a6-4978-8364-1a2b3c4d5e6f]";

    static final String OPPORTUNITY_ID = "Opportunity ID associated with this order for CRM integration.";
    static final String OPPORTUNITY_ID_EXAMPLE = "OPTY-AXYB456";

    static final String IS_PRIMARY_ORDER_FOR_OPPORTUNITY = "Indicates if this is the primary order associated with the opportunity.";
    static final String IS_PRIMARY_ORDER_FOR_OPPORTUNITY_EXAMPLE = "true";

    static final String OPPORTUNITY_NAME = "Name of the opportunity associated with this order.";
    static final String OPPORTUNITY_NAME_EXAMPLE = "CreativePro Q1 Expansion Opportunity";

    static final String OPPORTUNITY_TYPE = "Type of the opportunity associated with this order. For example, 'New Business' or 'Expansion'.";
    static final String OPPORTUNITY_TYPE_EXAMPLE = "New Business";

    static final String OPPORTUNITY_STAGE = "Current stage of the opportunity in the sales pipeline.";
    static final String OPPORTUNITY_STAGE_EXAMPLE = "Closed Won";

    static final String OPPORTUNITY_CRM_TYPE = "Type of CRM where the opportunity is managed.";
    static final String OPPORTUNITY_CRM_TYPE_EXAMPLE = "SALESFORCE";

    static final String OWNER_ID = "ID of the user who owns this order.";
    static final String OWNER_ID_EXAMPLE = "USR-12345AB";

    static final String RENEWAL_FOR_SUBSCRIPTION_ID = "ID of the subscription being renewed. Required only when `orderType` is `RENEWAL`.";
    static final String RENEWAL_FOR_SUBSCRIPTION_ID_EXAMPLE = "SUB-A1B3C4D";

    static final String DOCUMENT_MASTER_TEMPLATE_ID = "UUID of the master document template to use for generating order documents.";
    static final String DOCUMENT_MASTER_TEMPLATE_ID_EXAMPLE = "f47ac10b-58cc-4372-a567-0e02b2c3d479";

    static final String DOCUMENT_CUSTOM_CONTENT = "Custom content to include in the generated order documents.";

    static final String PURCHASE_ORDER_NUMBER = "The purchase order number associated with this order.";
    static final String PURCHASE_ORDER_NUMBER_EXAMPLE = "********9"; // TODO: Add valid example

    static final String PURCHASE_ORDER_REQUIRED_FOR_INVOICING = "Indicates whether a purchase order number is required to generate an invoice.";
    static final String PURCHASE_ORDER_REQUIRED_FOR_INVOICING_EXAMPLE = "true";

    static final String AUTO_RENEW = "Indicates whether the subscription should automatically renew at the end of its term.";
    static final String AUTO_RENEW_EXAMPLE = "false";

    static final String APPROVAL_SEGMENT_ID = "ID of the approval segment to use for routing this order through approval workflows.";
    static final String APPROVAL_SEGMENT_ID_EXAMPLE = "APSG-ABC45";

    static final String ATTACHMENT_ID = "ID of an attachment associated with this order (e.g., signed contract).";
    static final String ATTACHMENT_ID_EXAMPLE = "********"; // TODO: Add valid example

    static final String EXPIRES_ON =
        "Date when the order expires if the `status` is not `EXECUTED`. Specified in Unix timestamp format (seconds since epoch).";
    static final String EXPIRES_ON_EXAMPLE = "**********";

    static final String ENTITY_ID = "ID of the entity (e.g., business or subsidiary) associated with this order.";
    static final String ENTITY_ID_EXAMPLE = "ENT-98765AB";

    static final String CUSTOM_FIELDS = "Array of custom fields to include additional metadata with the order.";

    static final String START_DATE_TYPE =
        """
        Determines how the start date is calculated.

        **NOTE:** If you include the `rampInterval` object dates for a ramped pricing schedule, `startDateType` must be set to `FIXED`.

        Supported values:
        * `FIXED`: Start date is the value of `startDate`, or the first `rampInterval` timestamp if specified.
        * `EXECUTION_DATE`: Start date is the date when the order status changes to `EXECUTED`.""";
    static final String START_DATE_TYPE_EXAMPLE = "EXECUTION_DATE";

    static final String CURRENCY = "ISO 4217 currency code for the order. If you don't specify a value, the account's default currency is used.";
    static final String CURRENCY_EXAMPLE = "USD";

    static final String CUSTOM_BILLING_SCHEDULE = "Custom billing schedule for the order, allowing for non-standard billing periods and amounts.";

    static final String CUSTOM_PREDEFINED_TEMPLATES_ON_ORDER = "List of custom predefined templates to include on the order.";

    static final String SUBSCRIPTION_DURATION_MODEL =
        """
        Determines the subscription term.

        Supported values:
        * `TERMED`: Subscription has a fixed term length.
        * `EVERGREEN`: Subscription continutes indefinitely until cancelled.""";
    static final String SUBSCRIPTION_DURATION_MODEL_EXAMPLE = "TERMED";

    static final String OPPORTUNITY_INPUT = "Details of the opportunity associated with this order.";
}
