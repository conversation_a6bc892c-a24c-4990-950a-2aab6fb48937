package com.subskribe.billy.resources.shared;

import java.io.InputStream;
import javax.ws.rs.core.CacheControl;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;
import org.apache.commons.lang3.tuple.Pair;
import org.glassfish.jersey.media.sse.EventOutput;

public class ResponseUtility {

    private static final String CONNECTION_HEADER = "Connection";
    private static final String KEEP_ALIVE = "keep-alive";
    private static final CacheControl NO_STORE = CacheControl.valueOf("no-store");

    private ResponseUtility() {}

    public static Response okResponseFromEventOutput(EventOutput eventOutput) {
        return Response.ok(eventOutput)
            // event output is always sse mime type
            .type(MediaType.SERVER_SENT_EVENTS_TYPE)
            .cacheControl(NO_STORE)
            .header(CONNECTION_HEADER, KEEP_ALIVE)
            .build();
    }

    public static Response okResponseFromInputStreamFileNamePair(Pair<InputStream, String> inputStreamFileNamePair, String contentType) {
        return okResponseFromInputStreamAndFileName(inputStreamFileNamePair.getLeft(), inputStreamFileNamePair.getRight(), contentType);
    }

    public static Response okResponseFromInputStreamAndFileName(InputStream inputStream, String fileName, String contentType) {
        return Response.ok(inputStream)
            .header(HttpHeaders.CONTENT_TYPE, contentType)
            .header(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=\"%s\"", fileName))
            .build();
    }

    public static Response okResponseFromStreamingOutputAndFileName(StreamingOutput streamingOutput, String fileName, String contentType) {
        return Response.ok(streamingOutput)
            .header(HttpHeaders.CONTENT_TYPE, contentType)
            .header(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=\"%s\"", fileName))
            .build();
    }
}
