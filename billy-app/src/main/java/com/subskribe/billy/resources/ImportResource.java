package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.dataimport.export.AccountDomainExporter;
import com.subskribe.billy.dataimport.export.CatalogDomainExporter;
import com.subskribe.billy.dataimport.export.OrderDomainExporter;
import com.subskribe.billy.dataimport.model.DataImport;
import com.subskribe.billy.dataimport.model.DataImportInput;
import com.subskribe.billy.dataimport.model.ImportDomain;
import com.subskribe.billy.dataimport.model.flatfile.FlatfileSpaceResponse;
import com.subskribe.billy.dataimport.model.flatfile.FlatfileWorkbookResponse;
import com.subskribe.billy.dataimport.service.DataImportService;
import com.subskribe.billy.dataimport.service.FlatfileService;
import com.subskribe.billy.resources.shared.ResponseUtility;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.service.UserService;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

@Path("/import")
@Produces(MediaType.APPLICATION_JSON)
@Api
@AllowRoles({ Role.ADMIN })
public class ImportResource {

    private final DataImportService dataImportService;

    private final FlatfileService flatfileService;

    private final OrderDomainExporter orderDomainExporter;

    private final AccountDomainExporter accountDomainExporter;

    private final CatalogDomainExporter catalogDomainExporter;

    private final UserService userService;

    @Inject
    public ImportResource(
        DataImportService dataImportService,
        FlatfileService flatfileService,
        OrderDomainExporter orderDomainExporter,
        AccountDomainExporter accountDomainExporter,
        CatalogDomainExporter catalogDomainExporter,
        UserService userService
    ) {
        this.dataImportService = dataImportService;
        this.flatfileService = flatfileService;
        this.orderDomainExporter = orderDomainExporter;
        this.accountDomainExporter = accountDomainExporter;
        this.catalogDomainExporter = catalogDomainExporter;
        this.userService = userService;
    }

    @GET
    @Path("/export/newOrders")
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @ApiOperation(
        value = "Get new orders in import CSV format",
        notes = "Gets new orders in a format appropriate for re-import. On success, the output is a csv containing the orders.",
        tags = { "Import" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getNewOrderExportInImportFormat(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("useRealIds") @ApiParam(value = "use the subskribe ID instead of external ID for exported objects") Boolean useRealIds
    ) {
        StreamingOutput streamingOutput = orderDomainExporter.getNewOrderExportForImport(BooleanUtils.isTrue(useRealIds));
        String fileName = orderDomainExporter.getNewOrderExportFileName();
        return ResponseUtility.okResponseFromStreamingOutputAndFileName(streamingOutput, fileName, "text/csv");
    }

    @GET
    @Path("/export/amendmentOrders")
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @ApiOperation(
        value = "Get amendment orders in import CSV format",
        notes = "Gets amendment orders in a format appropriate for re-import for the given generation. On success, the output is a csv containing the orders.",
        tags = { "Import" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getAmendmentOrderExportInImportFormat(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("generation") @ApiParam(
            value = "the generation of amendments to include in the export, generations are defined as 1 based index of number of amendments applied to subscription"
        ) Integer generationInput,
        @QueryParam("useRealIds") @ApiParam(value = "use the subskribe ID instead of external ID for exported objects") Boolean useRealIds
    ) {
        int generation = Optional.ofNullable(generationInput).orElse(1);
        StreamingOutput streamingOutput = orderDomainExporter.getAmendmentOrdersExportForImport(generation, BooleanUtils.isTrue(useRealIds));
        String fileName = orderDomainExporter.getAmendOrderExportFileName(generation);
        return ResponseUtility.okResponseFromStreamingOutputAndFileName(streamingOutput, fileName, "text/csv");
    }

    @GET
    @Path("/export/accountContact")
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @ApiOperation(
        value = "Get account contacts for export",
        notes = "Gets account contacts in a format appropriate for re-import. " +
        "On success, the output is a csv containing the account and contacts.",
        tags = { "Import" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getAccountContactsInImportFormat(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        StreamingOutput streamingOutput = accountDomainExporter.getAccountDomainCreateDataForImport();
        String fileName = accountDomainExporter.getAccountContactExportFileName();
        return ResponseUtility.okResponseFromStreamingOutputAndFileName(streamingOutput, fileName, "text/csv");
    }

    @GET
    @Path("/export/catalog")
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @ApiOperation(
        value = "Get catalog data for export",
        notes = "Gets catalog data in a format appropriate for re-import. " +
        "On success, the output is a csv containing the catalog data containing Product, Plan and Charge data.",
        tags = { "Import" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getCatalogDataInImportFormat(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        StreamingOutput streamingOutput = catalogDomainExporter.getCatalogDomainDataForImport();
        String fileName = catalogDomainExporter.getCatalogExportFileName();
        return ResponseUtility.okResponseFromStreamingOutputAndFileName(streamingOutput, fileName, "text/csv");
    }

    @GET
    @ApiOperation(value = "Gets all import items", notes = "Gets all items that was imported", response = DataImport.class, tags = { "Import" })
    @AllowRoles({ Role.ADMIN })
    public Response getDataImports(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        List<DataImport> dataImports = dataImportService.getDataImports();
        return Response.ok(dataImports).build();
    }

    @GET
    @Path("/{importId}")
    @ApiOperation(value = "Gets an import item", notes = "Gets an item that was imported by its ID", response = DataImport.class, tags = { "Import" })
    @AllowRoles({ Role.ADMIN })
    public Response getDataImportById(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("importId") @NotNull @ApiParam(name = "importId", value = "id of the item", required = true) String importId
    ) {
        DataImport dataImport = dataImportService.getDataImportById(importId);
        return Response.ok(dataImport).build();
    }

    @GET
    @Path("/{importId}/result")
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON }) //produces json if there is an error
    @ApiOperation(value = "Get import details", notes = "Returns the details of an import by its ID", tags = { "Import" })
    @AllowRoles({ Role.ADMIN })
    public Response getImportResult(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("importId") @NotNull String importId
    ) {
        Pair<InputStream, String> importResult = dataImportService.getDataImportResults(importId);
        // Do not use ResponseUtility.okResponseFromInputStreamFileNamePair as the content disposition is already fully qualified
        return Response.ok(importResult.getLeft()).header(HttpHeaders.CONTENT_DISPOSITION, importResult.getRight()).build();
    }

    @PUT
    @Path("/{importId}/process")
    @ApiOperation(value = "Process an import by ID", notes = "Processes the import specified.", response = DataImport.class, tags = { "Import" })
    @AllowRoles({ Role.ADMIN })
    public Response processImport(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("importId") @NotNull String importId
    ) {
        DataImport processing = dataImportService.startProcessing(importId);
        return Response.ok(processing).build();
    }

    @POST
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiOperation(
        value = "Validate import file",
        notes = "Validates a multi-part import file. Returns an import ID on success.",
        response = String.class,
        tags = { "Import" }
    )
    @AllowRoles({ Role.IMPORT, Role.ADMIN })
    public Response validateMultiPartFileImport(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @FormDataParam("file") InputStream importStream,
        @FormDataParam("file") FormDataContentDisposition contentDisposition,
        @Context UriInfo uriInfo
    ) {
        Optional<User> optionalUser = userService.getCurrentUser();

        String importId = dataImportService.validateFileImportJob(
            DataImportInput.builder()
                .fileName(contentDisposition.getFileName())
                .importStream(importStream)
                .importUser(billyAuthPrincipal.getName())
                .importedByUserId(optionalUser.map(User::getUserId).orElse(null))
                .build()
        );
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(importId);
        return Response.created(builder.build()).build();
    }

    @GET
    @Path("/schemas")
    @ApiOperation(
        value = "Return available schemas",
        notes = "Returns the set of schemas available for import",
        response = List.class,
        responseContainer = "Map",
        tags = { "Import" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response getAvailableSchemas(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        Map<String, List<String>> availableSchemas = dataImportService.getSupportedSchemas();
        return Response.ok(availableSchemas).build();
    }

    @POST
    @Path("/flatfile/{domain}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Create a Flatfile workbook",
        notes = "Creates a Flatfile workbook and adds it to a space",
        response = FlatfileWorkbookResponse.class,
        tags = { "Import" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response createFlatfileWorkbook(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("domain") @ApiParam(name = "domain", value = "the domain to import", required = true, allowableValues = "ORDER") String domain,
        @Context UriInfo uriInfo
    ) {
        ImportDomain importDomain = ImportDomain.valueOf(domain.toUpperCase());
        FlatfileSpaceResponse flatfileSpace = flatfileService.createWorkbook(importDomain);
        return Response.ok(flatfileSpace).build();
    }
}
