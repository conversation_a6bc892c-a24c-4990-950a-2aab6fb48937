package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.model.CreditMemoBalance;
import com.subskribe.billy.invoicesettlement.services.CreditMemoDocumentService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoRetrievalService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoService;
import com.subskribe.billy.invoicesettlement.services.TenantCreditMemoConfigurationService;
import com.subskribe.billy.resources.json.creditmemo.CreditMemoBalanceJson;
import com.subskribe.billy.resources.json.creditmemo.CreditMemoBalanceJsonMapper;
import com.subskribe.billy.resources.json.creditmemo.CreditMemoJson;
import com.subskribe.billy.resources.json.creditmemo.CreditMemoJsonMapper;
import com.subskribe.billy.resources.json.creditmemo.StandaloneCreditMemoRequest;
import com.subskribe.billy.resources.json.creditmemo.TenantCreditMemoConfigJsonMapper;
import com.subskribe.billy.resources.json.creditmemo.TenantCreditMemoConfigurationJson;
import com.subskribe.billy.resources.shared.FileResponseBuilder;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.resources.shared.PaginationResponseJson;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.InputStream;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import javax.inject.Inject;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.mapstruct.factory.Mappers;

@Path("/creditmemos")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class CreditMemoResource {

    private static final Logger LOGGER = LoggerFactory.getLogger(CreditMemoResource.class);

    private final CreditMemoService creditMemoService;
    private final CreditMemoJsonMapper creditMemoJsonMapper;
    private final CreditMemoRetrievalService creditMemoRetrievalService;
    private final CreditMemoBalanceJsonMapper creditMemoBalanceJsonMapper;

    private final InvoiceRetrievalService invoiceRetrievalService;
    private final TenantCreditMemoConfigurationService tenantCreditMemoConfigurationService;
    private final CreditMemoDocumentService creditMemoDocumentService;
    private final TenantCreditMemoConfigJsonMapper tenantCreditMemoConfigJsonMapper;

    @Inject
    public CreditMemoResource(
        CreditMemoService creditMemoService,
        CreditMemoRetrievalService creditMemoRetrievalService,
        InvoiceRetrievalService invoiceRetrievalService,
        TenantCreditMemoConfigurationService tenantCreditMemoConfigurationService,
        CreditMemoDocumentService creditMemoDocumentService
    ) {
        this.creditMemoService = creditMemoService;
        this.creditMemoRetrievalService = creditMemoRetrievalService;
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.tenantCreditMemoConfigurationService = tenantCreditMemoConfigurationService;
        this.creditMemoDocumentService = creditMemoDocumentService;
        creditMemoJsonMapper = Mappers.getMapper(CreditMemoJsonMapper.class);
        creditMemoBalanceJsonMapper = Mappers.getMapper(CreditMemoBalanceJsonMapper.class);
        tenantCreditMemoConfigJsonMapper = Mappers.getMapper(TenantCreditMemoConfigJsonMapper.class);
    }

    @POST
    @Path("/configuration")
    @ApiOperation(
        value = "Update credit memo configuration",
        notes = "Updates the credit memo configuration for you tenant.",
        response = TenantCreditMemoConfigurationJson.class,
        tags = { "Credit Memo" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response updateCreditMemoConfiguration(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @ApiParam(value = "json representing the configuration") TenantCreditMemoConfigurationJson tenantCreditMemoConfigurationJson
    ) {
        var config = tenantCreditMemoConfigJsonMapper.fromJson(tenantCreditMemoConfigurationJson);
        String tenantId = billyAuthPrincipal.getTenantId().getRequiredId();
        var savedConfig = tenantCreditMemoConfigurationService.updateCreditMemoConfig(config, tenantId);
        return Response.ok(tenantCreditMemoConfigJsonMapper.toJson(savedConfig)).build();
    }

    @GET
    @Path("/configuration")
    @ApiOperation(
        value = "Get credit memo configuration for the tenant",
        notes = "Returns the credit memo configuration for your tenant",
        response = TenantCreditMemoConfigurationJson.class,
        tags = { "Credit Memo" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.READ_ONLY })
    public Response getCreditMemoConfiguration(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        var config = tenantCreditMemoConfigurationService.getCreditMemoConfig();
        var configJson = tenantCreditMemoConfigJsonMapper.toJson(config);
        return Response.ok(configJson).build();
    }

    // hack to get the response correctly documented in swagger
    private static class CreditMemoPaginationResponseJson extends PaginationResponseJson<CreditMemo, CreditMemoJson> {

        private CreditMemoPaginationResponseJson(
            List<CreditMemo> resource,
            int limit,
            Function<List<CreditMemo>, List<CreditMemoJson>> toJson,
            Function<CreditMemo, UUID> getId
        ) {
            super(resource, limit, toJson, getId);
        }
    }

    @GET
    @ApiOperation(
        value = "Get credit memos for an account",
        notes = "Returns a paginated list of credit memos for the specified account. " +
        "Pass the cursor returned to subsequent calls to retrieve all data.",
        response = CreditMemoPaginationResponseJson.class,
        tags = { "Credit Memo" }
    )
    @AllowAllRoles
    public Response getCreditMemoForAccount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("accountId") @ApiParam(name = "accountId", value = "id of the account") String accountId,
        @QueryParam("cursor") @ApiParam(name = "cursor", value = "cursor used to move the pages") UUID cursor,
        @QueryParam("limit") @ApiParam(name = "limit", value = "number of results per page") int limit
    ) {
        var paginationQueryParams = new PaginationQueryParams(cursor, limit);
        List<CreditMemo> creditMemos = creditMemoService.getCreditMemoForAccount(accountId, paginationQueryParams);
        var paginationJson = new PaginationResponseJson<>(
            creditMemos,
            paginationQueryParams.getLimit(),
            creditMemoJsonMapper::creditMemosToJson,
            CreditMemo::getId
        );
        return Response.ok(paginationJson).build();
    }

    @GET
    @Path("/{creditMemoNumber}")
    @ApiOperation(
        value = "Get credit memo details",
        notes = "Returns the details of the specified credit memo",
        response = CreditMemoJson.class,
        tags = { "Credit Memo" }
    )
    @AllowAllRoles
    public Response getCreditMemo(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("creditMemoNumber") @ApiParam(value = "number of the credit memo") String creditMemoNumber
    ) {
        CreditMemo creditMemo = creditMemoRetrievalService.getCreditMemoByNumber(creditMemoNumber);
        CreditMemoJson json = creditMemoJsonMapper.creditMemoToJson(creditMemo);
        return Response.ok(json).build();
    }

    @PUT
    @Path("/convert/{invoiceNumber}")
    @ApiOperation(
        value = "Convert invoice to credit memo",
        notes = "Converts the specified invoice to a credit memo. Note the invoice must be " + "in DRAFT status and have a negative balance.",
        response = CreditMemoJson.class,
        tags = { "Credit Memo" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response convertNegativeDraftInvoice(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("invoiceNumber") @ApiParam(value = "number of the invoice") String invoiceNumber
    ) {
        Invoice invoice = invoiceRetrievalService.getInvoice(new Invoice.Number(invoiceNumber));
        CreditMemo creditMemo = creditMemoService.convertNegativeInvoiceToCreditMemo(invoice);
        CreditMemoJson json = creditMemoJsonMapper.creditMemoToJson(creditMemo);
        return Response.ok(json).build();
    }

    @POST
    @ApiOperation(
        value = "Create a standalone credit memo",
        notes = "Creates a standalone credit memo for a specified account. On success the number of the new memo is returned",
        response = String.class,
        tags = { "Credit Memo" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response createStandaloneCreditMemo(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @ApiParam(value = "json representing the credit memo details") StandaloneCreditMemoRequest request,
        @Context UriInfo uriInfo
    ) {
        CreditMemo creditMemo = creditMemoService.createStandaloneCreditMemo(request);
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(creditMemo.getCreditMemoNumber());
        return Response.created(builder.build()).build();
    }

    @PUT
    @Path("/{creditMemoNumber}")
    @ApiOperation(
        value = "Update a credit memo",
        notes = "Updates the details of a credit memo in DRAFT status",
        response = CreditMemoJson.class,
        tags = { "Credit Memo" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response updateDraftCreditMemo(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @ApiParam(value = "json representation of the details to be updated") StandaloneCreditMemoRequest request,
        @PathParam("creditMemoNumber") @ApiParam(value = "credit memo number") String creditMemoNumber,
        @Context UriInfo uriInfo
    ) {
        CreditMemo creditMemo = creditMemoService.updateDraftCreditMemo(request, creditMemoNumber);
        return Response.ok(creditMemoJsonMapper.creditMemoToJson(creditMemo)).build();
    }

    @POST
    @Path("/{creditMemoNumber}/post")
    @ApiOperation(
        value = "Post a credit memo",
        notes = "Sets the status of the specified credit memo to POSTED",
        response = CreditMemoJson.class,
        tags = { "Credit Memo" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response postCreditMemo(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("creditMemoNumber") @ApiParam(value = "number of the credit memo") String creditMemoNumber
    ) {
        CreditMemo creditMemo = creditMemoService.postCreditMemo(creditMemoNumber);
        CreditMemoJson json = creditMemoJsonMapper.creditMemoToJson(creditMemo);
        return Response.ok(json).build();
    }

    @DELETE
    @Path("/{creditMemoNumber}")
    @ApiOperation(
        value = "Delete a credit memo",
        notes = "Deletes the specified credit memo. The credit memo must be in DRAFT status",
        response = CreditMemoJson.class,
        tags = { "Credit Memo" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response deleteCreditMemo(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("creditMemoNumber") @ApiParam(value = "number of the credit memo") String creditMemoNumber
    ) {
        CreditMemo creditMemo = creditMemoService.deleteCreditMemo(creditMemoNumber);
        CreditMemoJson json = creditMemoJsonMapper.creditMemoToJson(creditMemo);
        return Response.ok(json).build();
    }

    @GET
    @Path("/{creditMemoNumber}/balance")
    @ApiOperation(
        value = "Get credit memo balance",
        notes = "Gets the balance of the specified credit memo",
        response = CreditMemoBalanceJson.class,
        tags = { "Credit Memo" }
    )
    @AllowAllRoles
    public Response getCreditBalance(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("creditMemoNumber") @NotNull @ApiParam(value = "number of the credit memo") String creditMemoNumber
    ) {
        CreditMemoBalance creditMemoBalance = creditMemoRetrievalService.getCreditMemoBalance(creditMemoNumber);
        var json = creditMemoBalanceJsonMapper.creditMemoBalanceToJson(creditMemoBalance);
        return Response.ok(json).build();
    }

    @POST
    @Path("/{creditMemoNumber}/pdf")
    @ApiOperation(value = "Create a credit memo document", notes = "Creates a pdf version of the credit memo", tags = { "Credit Memo" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response createCreditMemoDocument(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @PathParam("creditMemoNumber") @ApiParam(value = "number of the credit memo") String creditMemoNumber
    ) {
        LOGGER.info("Creating credit memo document for credit memo number: {}", creditMemoNumber);
        creditMemoDocumentService.createCreditMemoDocument(creditMemoNumber);
        return Response.ok().build();
    }

    @GET
    @Path("/{creditMemoNumber}/pdf")
    @ApiOperation(
        value = "Download credit memo pdf",
        notes = "Downloads the pdf version of the credit memo. Note the credit memo must already have been " +
        "created. If the credit memo is still in the process of being created, HTTP 202 is returned.",
        tags = { "Credit Memo" }
    )
    @AllowAllRoles
    public Response getCreditMemoDocumentPdf(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @PathParam("creditMemoNumber") @ApiParam(value = "number of the credit memo") String creditMemoNumber
    ) {
        Optional<InputStream> inputStream = creditMemoDocumentService.getCreditMemoDocumentByCreditMemoNumber(creditMemoNumber);
        if (inputStream.isEmpty()) {
            return Response.status(Response.Status.ACCEPTED).build();
        }

        return FileResponseBuilder.getFileResponse(inputStream.get(), creditMemoNumber);
    }
}
