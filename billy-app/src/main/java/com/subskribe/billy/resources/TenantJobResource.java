package com.subskribe.billy.resources;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.scheduler.model.QuartzJobType;
import com.subskribe.billy.scheduler.service.QuartzManagerService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenantjob.model.ImmutableTenantJob;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.tenantjob.service.TenantJobGetService;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.SchedulerException;

@Path("/tenantJobs")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class TenantJobResource {

    private final List<TenantJobType> ALLOWED_DISPATCH_JOBS = List.of(
        TenantJobType.REV_REGENERATE_SCHEDULES_FOR_TENANT,
        TenantJobType.REPROCESS_EVENTS_FOR_TENANT
    );

    private final TenantJobGetService tenantJobGetService;
    private final TenantJobDispatcherService tenantJobDispatcherService;
    private final QuartzManagerService quartzManagerService;
    private final EntityContextProvider entityContextProvider;
    private final EntityContextResolver entityContextResolver;
    private final BillyConfiguration billyConfiguration;
    private final TenantIdProvider tenantIdProvider;

    @Inject
    public TenantJobResource(
        TenantJobGetService tenantJobGetService,
        TenantJobDispatcherService tenantJobDispatcherService,
        QuartzManagerService quartzManagerService,
        EntityContextProvider entityContextProvider,
        EntityContextResolver entityContextResolver,
        BillyConfiguration billyConfiguration,
        TenantIdProvider tenantIdProvider
    ) {
        this.tenantJobGetService = tenantJobGetService;
        this.tenantJobDispatcherService = tenantJobDispatcherService;
        this.quartzManagerService = quartzManagerService;
        this.entityContextProvider = entityContextProvider;
        this.entityContextResolver = entityContextResolver;
        this.billyConfiguration = billyConfiguration;
        this.tenantIdProvider = tenantIdProvider;
    }

    @POST
    @Path("/dispatch")
    @ApiOperation(value = "Dispatch tenant job", notes = "Executes a tenant job", tags = { "Jobs" })
    @AllowRoles({ Role.ADMIN })
    public Response dispatch(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, TenantJob tenantJob) {
        if (tenantJob.getTenantId() != null && !tenantJob.getTenantId().equals(tenantIdProvider.provideTenantIdString())) {
            throw new InvalidInputException("tenant id for job does not match your tenant id");
        }

        if (!ALLOWED_DISPATCH_JOBS.contains(tenantJob.getJobType())) {
            throw new InvalidInputException("this job type is not supported for tenant jobs api: " + tenantJob.getJobType().name());
        }
        // if entity ids are not specified, run under current entity selection
        if (CollectionUtils.isEmpty(tenantJob.getEntityIds())) {
            tenantJob = ImmutableTenantJob.builder().from(tenantJob).entityIds(entityContextProvider.provideEntityContext().getSelectedIds()).build();
        }
        if (!entityContextResolver.hasFullAccessTo(tenantJob.getEntityIds())) {
            throw new InvalidInputException(EntityContext.USER_OR_API_LACKS_FULL_ENTITY_ACCESS);
        }
        TenantJob dispatchedJob = tenantJobDispatcherService.dispatch(tenantJob);
        return Response.ok(dispatchedJob).build();
    }

    @POST
    @Path("/retry")
    @ApiOperation(value = "Retry tenant job", notes = "Executes a tenant job", tags = { "Jobs" })
    @AllowRoles({ Role.ADMIN })
    public Response executeTenantJob(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @QueryParam("jobId") String jobId) {
        tenantJobDispatcherService.retry(jobId, Optional.empty());
        return Response.ok().build();
    }

    @GET
    @Path("/{jobId}")
    @ApiOperation(value = "Get tenant job", notes = "Get tenant job by id", tags = { "Jobs" })
    @AllowAllRoles
    public Response getTenantJob(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @ApiParam(value = "Job id") @PathParam("jobId") String jobId
    ) {
        var tenantJob = tenantJobGetService.getTenantJobById(jobId, Optional.empty());
        return Response.ok(tenantJob).build();
    }

    @GET
    @Path("/test/waitForJob")
    @ApiOperation(value = "Wait for job", hidden = true, notes = "Wait for tenant job", tags = { "Jobs" })
    @AllowRoles({ Role.ADMIN })
    public Response waitForJobType(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("jobType") TenantJobType jobType,
        @QueryParam("objectId") String objectId,
        @QueryParam("isQuartz") @DefaultValue("true") Boolean isQuartz
    ) throws SchedulerException, InterruptedException {
        validateTestApiIsCalledFromLocalOrCiOnly();
        var optionalTenantJob = tenantJobGetService.getActiveJobByTypeAndObjectId(jobType, objectId);
        if (optionalTenantJob.isEmpty()) {
            return Response.ok().build();
        }
        var tenantJob = optionalTenantJob.get();
        tenantJobGetService.waitForProcessing(tenantJob);
        if (isQuartz) {
            quartzManagerService.waitForJob(tenantJob.getJobId(), QuartzJobType.TENANT_JOB_DISPATCHER_JOB.name());
        }
        tenantJobGetService.waitForCompletion(tenantJob);
        return Response.ok().build();
    }

    @GET
    @Path("/test/findJob")
    @ApiOperation(value = "Find job", hidden = true, notes = "Find tenant job", tags = { "Jobs" })
    @AllowRoles({ Role.ADMIN })
    public Response findJobByTypeAndObject(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("jobType") TenantJobType jobType,
        @QueryParam("objectId") String objectId,
        @QueryParam("isQuartz") @DefaultValue("true") Boolean isQuartz
    ) {
        validateTestApiIsCalledFromLocalOrCiOnly();
        var optionalTenantJob = tenantJobGetService.getActiveJobByTypeAndObjectId(jobType, objectId);
        if (optionalTenantJob.isEmpty()) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }
        return Response.ok(optionalTenantJob.get()).build();
    }

    private void validateTestApiIsCalledFromLocalOrCiOnly() {
        if (billyConfiguration.isLocalOrCi()) {
            return;
        }

        throw new UnsupportedOperationException("Test API call is not allowed outside of local/ci.");
    }
}
