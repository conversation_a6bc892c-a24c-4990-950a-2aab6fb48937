package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.resources.json.usage.RawUsagesData;
import com.subskribe.billy.resources.shared.ResponseUtility;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.usage.model.PrepaidStats;
import com.subskribe.billy.usage.model.UsageAggregateOutput;
import com.subskribe.billy.usage.model.UsageBatchInsertResult;
import com.subskribe.billy.usage.service.UsageAggregationService;
import com.subskribe.billy.usage.service.UsageReportRenderingService;
import com.subskribe.billy.usage.service.UsageService;
import com.subskribe.billy.usage.service.UsageStatisticsService;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;
import org.apache.commons.lang3.tuple.Pair;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

@Path("/v2/usage")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class UsageResource {

    private static final String USAGE_ARRIVAL_TIME_CHECKPOINT_KEY = "usageArrivalTimeCheckpoint";

    private final UsageService usageService;
    private final UsageStatisticsService usageStatisticsService;
    private final UsageReportRenderingService reportRenderingService;
    private final UsageAggregationService usageAggregationService;

    @Inject
    public UsageResource(
        UsageService usageService,
        UsageStatisticsService usageStatisticsService,
        UsageReportRenderingService reportRenderingService,
        UsageAggregationService usageAggregationService
    ) {
        this.usageService = usageService;
        this.usageStatisticsService = usageStatisticsService;
        this.reportRenderingService = reportRenderingService;
        this.usageAggregationService = usageAggregationService;
    }

    @POST
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Path("/csv")
    @ApiOperation(
        value = "Upload usage record CSV",
        notes = "Upload usage records in CSV file. Each row of the file represents a single usage record",
        response = UsageBatchInsertResult.class,
        tags = { "Usage" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK })
    public Response uploadSubscriptionUsageCSV(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @FormDataParam("file") InputStream importStream,
        @FormDataParam("file") FormDataContentDisposition contentDisposition
    ) throws IOException {
        // Returns a result per row number starting with row index = 1
        UsageBatchInsertResult result = usageService.uploadUsageDataCSV(importStream);
        return Response.ok(result).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Add usage record",
        notes = "Load usage records into the system",
        response = UsageBatchInsertResult.class,
        tags = { "Usage" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK })
    public Response addUsage(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @NotNull @Valid RawUsagesData usagesJson) {
        UsageBatchInsertResult result = usageService.insertRawUsageBatch(usagesJson.getData());
        return Response.ok(result).build();
    }

    @GET
    @Path("/{subscriptionId}")
    @ApiOperation(
        value = "Get aggregated usage",
        notes = "Retrieve the current aggregated usage data for a subscription between 2 instants",
        response = UsageAggregateOutput.class,
        responseContainer = "List",
        tags = { "Usage" }
    )
    @AllowAllRoles
    public Response getUsageAggregatesForSubscription(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("subscriptionId") String subscriptionId,
        @NotNull @QueryParam("from") Long from,
        @NotNull @QueryParam("to") Long to
    ) {
        Period period = Period.between(from, to);
        if (!period.isValid()) {
            throw new IllegalArgumentException("From date cannot be before to date.");
        }
        List<UsageAggregateOutput> usageAggregates = usageService.getAggregatedUsageForSubscription(subscriptionId, period);
        return Response.ok(usageAggregates).build();
    }

    @GET
    @Path("/stats/{subscriptionId}")
    @ApiOperation(
        value = "Get prepaid drawdown usage stats for subscription",
        notes = "Retrieve the current prepaid drawdown statistics for a subscription",
        response = PrepaidStats.class,
        responseContainer = "List",
        tags = { "Usage" }
    )
    @AllowAllRoles
    public Response getUsageStatsForSubscription(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @ApiParam(value = "ID of subscription to retrieve usages stats for", required = true) @NotNull @PathParam(
            "subscriptionId"
        ) String subscriptionId
    ) {
        //TODO: make prepaid a query param so that we can support prepaid and usage both
        List<PrepaidStats> prepaidStats = usageStatisticsService.getPrepaidDrawStatsForSubscription(subscriptionId);
        return Response.ok(prepaidStats).build();
    }

    @GET
    @Path("/stats/{subscriptionId}/csv")
    @ApiOperation(
        value = "Get prepaid drawdown usage stats CSV",
        notes = "Retrieve the current prepaid drawdown statistics for a subscription in CSV format",
        tags = { "Usage" }
    )
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @AllowAllRoles
    public Response getUsageStatsForSubscriptionCsv(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("subscriptionId") String subscriptionId
    ) {
        //TODO: make prepaid a query param so that we can support prepaid and usage both
        Pair<StreamingOutput, String> outputPair = reportRenderingService.getPrepaidDrawStatsForSubscriptionCsv(subscriptionId);
        StreamingOutput csvOutStream = outputPair.getLeft();
        String fileName = outputPair.getRight();
        return ResponseUtility.okResponseFromStreamingOutputAndFileName(csvOutStream, fileName, "text/csv");
    }

    @GET
    @Path("/stats/{subscriptionId}/pdf")
    @ApiOperation(
        value = "Get prepaid drawdown usage stats PDF",
        notes = "Retrieve the current prepaid drawdown statistics for a subscription in PDF format",
        tags = { "Usage" }
    )
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @AllowAllRoles
    public Response getUsageStatsForSubscriptionPdf(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("subscriptionId") String subscriptionId
    ) {
        //TODO: make prepaid a query param so that we can support prepaid and usage both
        Pair<Optional<InputStream>, String> pdfReport = reportRenderingService.getPrepaidDrawStatsForSubscriptionPdf(subscriptionId);
        Optional<InputStream> pdfStreamOptional = pdfReport.getLeft();
        if (pdfStreamOptional.isEmpty()) {
            return Response.status(Response.Status.ACCEPTED).build();
        }
        String fileName = pdfReport.getRight();
        return ResponseUtility.okResponseFromInputStreamAndFileName(pdfStreamOptional.get(), fileName, "application/pdf");
    }

    @GET
    @Path("/stats")
    @ApiOperation(
        value = "Get all prepaid drawdown usage stats",
        notes = "Retrieve the current prepaid drawdown statistics for all subscriptions for a time range",
        response = PrepaidStats.class,
        responseContainer = "List",
        tags = { "Usage" }
    )
    @AllowAllRoles
    public Response getUsageStatsForAllSubscriptions(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @QueryParam("from") Long from,
        @NotNull @QueryParam("to") Long to
    ) {
        Period period = Period.between(from, to);
        if (!period.isValid()) {
            throw new IllegalArgumentException("From date cannot be before to date.");
        }
        //TODO: make prepaid a query param so that we can support prepaid and usage both
        List<PrepaidStats> prepaidStats = usageStatisticsService.getPrepaidDrawStatsForAllSubscriptions(period);
        return Response.ok(prepaidStats).build();
    }

    @GET
    @Path("/stats/csv")
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON })
    @ApiOperation(
        value = "Get all prepaid drawdown usage stats CSV",
        notes = "Retrieve the current prepaid drawdown statistics for all subscriptions for a time range in CSV format",
        tags = { "Usage" }
    )
    @AllowAllRoles
    public Response getUsageStatsForAllSubscriptionsCsv(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @QueryParam("from") Long from,
        @NotNull @QueryParam("to") Long to
    ) {
        Period period = Period.between(from, to);
        if (!period.isValid()) {
            throw new IllegalArgumentException("From date cannot be before to date.");
        }
        //TODO: make prepaid a query param so that we can support prepaid and usage both
        Pair<StreamingOutput, String> outputPair = reportRenderingService.getPrepaidDrawStatsForAllSubscriptionsCsv(period);
        StreamingOutput csvOutStream = outputPair.getLeft();
        String fileName = outputPair.getRight();
        return ResponseUtility.okResponseFromStreamingOutputAndFileName(csvOutStream, fileName, "text/csv");
    }

    @GET
    @Path("/aggregate/usageArrivalTimeCheckpoint")
    @ApiOperation(
        value = "Get usage arrival checkpoint",
        notes = "Retrieve the latest usage record upload to the system as epoch seconds",
        tags = { "Usage" }
    )
    @AllowAllRoles
    public Response getUsageArrivalTimeCheckpoint(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        Optional<Instant> usageArrivalTimeCheckpointOptional = usageService.getRawUsageArrivalTimeCheckpoint();
        Map<String, Long> checkpointMap = Map.of();
        if (usageArrivalTimeCheckpointOptional.isPresent()) {
            checkpointMap = Map.of(USAGE_ARRIVAL_TIME_CHECKPOINT_KEY, usageArrivalTimeCheckpointOptional.get().getEpochSecond());
        }
        return Response.ok(checkpointMap).build();
    }

    @PUT
    @Path("/aggregate")
    @ApiOperation(value = "Aggregate raw usage", notes = "Trigger the process to aggregate any remaining raw usage records", tags = { "Usage" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response performOnDemandUsageAggregation(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        usageAggregationService.aggregateRawUsageAndPersist();
        return Response.ok().build();
    }
}
