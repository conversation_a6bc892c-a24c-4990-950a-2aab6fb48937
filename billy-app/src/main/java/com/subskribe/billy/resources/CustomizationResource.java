package com.subskribe.billy.resources;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.customization.model.CustomizationDefinition;
import com.subskribe.billy.customization.service.CustomizationService;
import com.subskribe.billy.shared.io.ClosingStreamingOutput;
import com.subskribe.billy.user.model.Role;
import com.subskribe.zeppa.customizations.core.RuleProcessed;
import io.dropwizard.auth.Auth;
import io.dropwizard.jersey.errors.ErrorMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.apache.commons.text.CaseUtils;
import org.eclipse.jetty.http.HttpStatus;

// TODO: remove beta/ after everyone has migrated
@Path("/{a:beta/customization|customization}")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class CustomizationResource {

    private static final MediaType ZEPPA_SOURCE = new MediaType("text", "zeppa");
    private static final String ZEPPA_SOURCE_MIME = "text/zeppa";

    private static final String API_TAG = "Customization";

    private final CustomizationService customizationService;

    private final BillyConfiguration billyConfiguration;

    @Inject
    public CustomizationResource(CustomizationService customizationService, BillyConfiguration billyConfiguration) {
        this.customizationService = customizationService;
        this.billyConfiguration = billyConfiguration;
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Path("/selectionCustomization/compile")
    @ApiOperation(
        value = "Compile and Verify a  selection customization for correctness",
        notes = "if this endpoint returns OK it means the selection customization zeppa script has successfully compiled",
        tags = { API_TAG }
    )
    @AllowRoles({ Role.ADMIN })
    public Response compileSelectionCustomizationZeppaScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("accountId") @ApiParam(value = "account id against which the compilation should happen", required = true) String accountId,
        @QueryParam("userId") @ApiParam(value = "user id against which the compilation should happen") String userId,
        @QueryParam("crmOpportunityId") @ApiParam(
            value = "the CRM Opportunity Id against which the compilation should happen"
        ) String crmOpportunityId,
        @NotNull @ApiParam(value = "the zeppa script that needs to be compiled", required = true) String zeppaScript
    ) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("Not implemented yet");
        }
        customizationService.compileSelectionCustomization(accountId, userId, crmOpportunityId, zeppaScript);
        return Response.ok().build();
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Path("/orderCreationCustomization/compile")
    @ApiOperation(
        value = "Compile and Verify a order creation customization for correctness",
        notes = "if this endpoint returns OK it means the order creation customization zeppa script has successfully compiled",
        tags = { API_TAG }
    )
    @AllowRoles({ Role.ADMIN })
    public Response compileOrderCreationCustomizationZeppaScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("orderId") @ApiParam(value = "order id against which the compilation should happen", required = true) String orderId,
        @QueryParam("userId") @ApiParam(value = "order id against which the compilation should happen") String userId,
        @NotNull @ApiParam(value = "the zeppa script that needs to be compiled", required = true) String zeppaScript
    ) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("Not implemented yet");
        }
        customizationService.compileOrderCustomization(orderId, userId, zeppaScript);
        return Response.ok().build();
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Path("/selectionCustomization/test")
    @ApiOperation(
        value = "Test a selection customization zeppa script",
        notes = "runs the provided zeppa script and returns a list of Rule actions that were applicable after zeppa script ran",
        tags = { API_TAG }
    )
    @AllowRoles({ Role.ADMIN })
    public Response testSelectionCustomizationZeppaScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("accountId") @ApiParam(value = "account id against which the test needs to happen", required = true) String accountId,
        @QueryParam("userId") @ApiParam(value = "user id against which the test needs to happen") String userId,
        @QueryParam("crmOpportunityId") @ApiParam(
            value = "the CRM Opportunity id against which this customization needs to run"
        ) String crmOpportunityId,
        @NotNull @ApiParam(value = "the zeppa script that needs to be tested", required = true) String zeppaScript
    ) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("Not implemented yet");
        }
        List<RuleProcessed> rulesProcessed = customizationService.fireSelectionCustomization(accountId, userId, crmOpportunityId, zeppaScript);
        return Response.ok(rulesProcessed).build();
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Path("/orderCreationCustomization/test")
    @ApiOperation(
        value = "Test a order creation customization zeppa script",
        notes = "runs the provided zeppa script and returns a list of Rule actions that were applicable after zeppa script ran",
        tags = { API_TAG }
    )
    @AllowRoles({ Role.ADMIN })
    public Response testOrderCreationCustomizationZeppaScript(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("orderId") @ApiParam(value = "order id against which the test needs to happen", required = true) String orderId,
        @QueryParam("userId") @ApiParam(
            value = "the user id against which this test needs to happen, the user should be part of the tenancy"
        ) String userId,
        @NotNull @ApiParam(value = "the zeppa script that needs to be tested", required = true) String zeppaScript
    ) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("Not implemented yet");
        }
        List<RuleProcessed> rulesProcessed = customizationService.fireOrderCreationCustomization(orderId, userId, zeppaScript);
        return Response.ok(rulesProcessed).build();
    }

    @GET
    @Path("orderCreationCustomization")
    @AllowRoles({ Role.ADMIN })
    @Produces({ MediaType.APPLICATION_JSON, ZEPPA_SOURCE_MIME })
    @ApiOperation(
        value = "Get the current order creation customization defined",
        notes = "returns a .zeppa source file for the current order creation customization if present, 404 if customization is not defined",
        tags = { API_TAG }
    )
    public Response getOrderCreationCustomization(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        Optional<CustomizationDefinition> definitionOptional = customizationService.getCurrentOrderCreationCustomization();
        return definitionOptional
            .map(CustomizationResource::definitionToResponse)
            .orElseGet(() ->
                Response.status(HttpStatus.NOT_FOUND_404)
                    .entity(new ErrorMessage(HttpStatus.NOT_FOUND_404, "Order creation customization not defined"))
                    .build()
            );
    }

    @GET
    @Path("selectionCustomization")
    @AllowRoles({ Role.ADMIN })
    @Produces({ MediaType.APPLICATION_JSON, ZEPPA_SOURCE_MIME })
    @ApiOperation(
        value = "Get the current selection customization defined",
        notes = "returns a .zeppa source file for the current selection customization if present, 404 if customization is not defined",
        tags = { API_TAG }
    )
    public Response getSelectionCustomization(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        Optional<CustomizationDefinition> definitionOptional = customizationService.getCurrentSelectionCustomization();
        return definitionOptional
            .map(CustomizationResource::definitionToResponse)
            .orElseGet(() ->
                Response.status(HttpStatus.NOT_FOUND_404)
                    .entity(new ErrorMessage(HttpStatus.NOT_FOUND_404, "Selection customization not defined"))
                    .build()
            );
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Path("/selectionCustomization")
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(
        value = "Add or Update selection customization script",
        notes = """
        Add or Update the selection customization zeppa script in the platform.\s
         - once the update happens new selection customization script will take effect\s
         - the updated version of the script is returned with the API call\s
        NOTE: the account id required for this is only for test before submission\s
        NOTE: it *does not mean* that this customization is run only for this account.
        """,
        tags = { API_TAG }
    )
    public Response addSelectionCustomization(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("testAccountId") @ApiParam(
            value = "before the script is actually stored the script is run against this test account id, " +
            "it *DOES NOT MEAN* this customization is only for this account",
            required = true
        ) String testAccountId,
        @NotNull @QueryParam("expectedVersion") @ApiParam(
            value = """
            this is the expected version of the script that is currently stored\s
            - if the expected version does not match what is stored the call will fail\s
            - if the script is being added for the first time then the expected version is 0\s
            - look at the GET call for how the current version number is returned""",
            required = true
        ) Integer expectedVersion,
        @NotNull @ApiParam String zeppaScript
    ) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("Not implemented yet");
        }
        CustomizationDefinition definition = customizationService.upsertSelectionCustomization(
            testAccountId,
            null/* user id is null because it is optional, blank user binding will be passed for testing*/,
            null/* crm opportunity id is null because it is optional, blank opportunity binding will be passed for testing*/,
            zeppaScript,
            expectedVersion
        );
        return Response.ok(definition).build();
    }

    @PUT
    @Consumes(MediaType.TEXT_PLAIN)
    @Path("/orderCreationCustomization")
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(
        value = "Add or Update order creation customization script",
        notes = """
        Add or Update the order creation customization zeppa script in the platform.
         - once the update happens new this script will take effect for all order creations
         - the updated version of the script is returned with the API call
        NOTE: the order id required for this is only for test before submission
        NOTE: it *does not mean* that this customization is run only for this order.
        """,
        tags = { API_TAG }
    )
    public Response addOrderCreationCustomization(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("testOrderId") @ApiParam(
            value = "before the script is actually stored its run against this test order id. " +
            "it *DOES NOT MEAN* this customization is only for this order",
            required = true
        ) String testOrderId,
        @NotNull @QueryParam("expectedVersion") @ApiParam(
            value = """
            this is the expected version of the script that is currently stored
            - if the expected version does not match what is stored the call will fail
            - if the script is being added for the first time then the expected version is 0
            - look at the GET call for how the current version number is returned""",
            required = true
        ) Integer expectedVersion,
        @NotNull @ApiParam String zeppaScript
    ) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("Not implemented yet");
        }
        CustomizationDefinition definition = customizationService.upsertOrderCreationCustomization(
            testOrderId,
            null/* user id is null because we are only testing blank binding will be passed in */,
            zeppaScript,
            expectedVersion
        );
        return Response.ok(definition).build();
    }

    private static Response definitionToResponse(CustomizationDefinition def) {
        String customizationContext = CaseUtils.toCamelCase(def.getCustomizationContext().name(), true, '_');
        String fileName = String.format("%s_version_%d.zeppa", customizationContext, def.getVersion());
        return Response.ok(ClosingStreamingOutput.of(new ByteArrayInputStream(def.getZeppaScript().getBytes(StandardCharsets.UTF_8))), ZEPPA_SOURCE)
            .header("content-disposition", fileName)
            .header("version", def.getVersion())
            .header("createdOn", def.getCreatedOn())
            .build();
    }
}
