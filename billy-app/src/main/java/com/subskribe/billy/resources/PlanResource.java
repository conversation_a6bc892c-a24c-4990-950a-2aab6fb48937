package com.subskribe.billy.resources;

import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationship;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipGetService;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.services.ChargeService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogService;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.ChargePartialJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.resources.json.plan.PlanMetadataJson;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.resources.shared.PaginationResponseJson;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.PlanTermsService;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.mapstruct.factory.Mappers;

@Path("/plans")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class PlanResource {

    private final ProductCatalogService productCatalog;
    private final ProductCatalogGetService productCatalogGetService;
    private final PlanMapper planMapper;

    private final AccountingService accountingService;
    private final AccountingGetService accountingGetService;
    private final CatalogRelationshipService catalogRelationshipService;
    private final CatalogRelationshipGetService catalogRelationshipGetService;
    private final PlanTermsService planTermsService;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final ChargeService chargeService;

    @Inject
    public PlanResource(
        ProductCatalogService productCatalog,
        ProductCatalogGetService productCatalogGetService,
        AccountingService accountingService,
        AccountingGetService accountingGetService,
        CatalogRelationshipService catalogRelationshipService,
        CatalogRelationshipGetService catalogRelationshipGetService,
        PlanTermsService planTermsService,
        DocumentTemplateGetService documentTemplateGetService,
        ChargeService chargeService
    ) {
        this.productCatalog = productCatalog;
        this.productCatalogGetService = productCatalogGetService;
        this.accountingService = accountingService;
        this.accountingGetService = accountingGetService;
        this.catalogRelationshipService = catalogRelationshipService;
        this.catalogRelationshipGetService = catalogRelationshipGetService;
        this.planTermsService = planTermsService;
        this.documentTemplateGetService = documentTemplateGetService;
        this.chargeService = chargeService;
        planMapper = Mappers.getMapper(PlanMapper.class);
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Create a plan",
        notes = "Creates a plan. On success the id of the new plan is returned.",
        response = PlanJson.class,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response addPlan(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(value = "json of the plan details", required = true) PlanJson planJson,
        @Context UriInfo uriInfo
    ) {
        var plan = planMapper.jsonToPlan(planJson);
        var savedPlan = productCatalog.addPlanAndCharges(plan);

        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(savedPlan.getPlanId());
        PlanJson savedPlanJson = planMapper.planToJson(savedPlan);
        return Response.created(builder.build()).entity(savedPlanJson).build();
    }

    @DELETE
    @Path("/{planId}")
    @ApiOperation(
        value = "Delete a plan",
        notes = "Deletes a plan. Note you can't delete a plan that's in use.",
        response = PlanJson.class,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response deletePlan(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId
    ) {
        var plan = productCatalog.deletePlan(planId);
        var planJson = planMapper.planToJson(plan);
        return Response.ok().entity(planJson).build();
    }

    @PUT
    @Path("/{planId}")
    @ApiOperation(
        value = "Update plan",
        notes = "Updates the details of the specified plan. Note you can't update the details of a plan once it's in use.",
        response = PlanJson.class,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response updatePlan(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId,
        @Valid @NotNull @ApiParam(value = "json of the plan details", required = true) PlanJson planJson
    ) {
        var plan = planMapper.jsonToPlan(planJson);
        plan.setPlanId(planId);
        productCatalog.updatePlanAndCharges(plan);
        return Response.ok().build();
    }

    @PUT
    @Path("/{planId}/terms")
    @ApiOperation(value = "Update plan terms", notes = "Updates predefined terms associated with the specified plan.", tags = { "Product Catalog" })
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response updatePlanTerms(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId,
        @Valid @NotNull @ApiParam(value = "json of the plan details", required = true) List<String> predefinedTermIds
    ) {
        List<DocumentTemplate> predefinedTerms = documentTemplateGetService.getDocumentTemplatesByTemplateIds(predefinedTermIds);
        planTermsService.updatePlanDocumentTemplates(planId, predefinedTerms);
        return Response.ok().build();
    }

    @PUT
    @Path("/{planId}/activate")
    @ApiOperation(value = "Activate a plan", notes = "Marks a plan as active", response = PlanJson.class, tags = { "Product Catalog" })
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response activatePlan(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId
    ) {
        var plan = productCatalog.activatePlan(planId);
        var planJson = planMapper.planToJson(plan);
        return Response.ok(planJson).build();
    }

    @PUT
    @Path("/{planId}/metadata")
    @ApiOperation(
        value = "Update plan metadata",
        notes = "Update plan metadata. For now, this can be used only to change the entities assigned to a plan",
        response = PlanJson.class,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response updatePlanMetadata(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "plan id", required = true) String planId,
        @Valid @NotNull @ApiParam(value = "plan metadata to be updated", required = true) PlanMetadataJson planMetadataJson
    ) {
        var plan = productCatalog.patchPlanMetadata(planId, planMetadataJson);
        var planJson = planMapper.planToJson(plan);
        return Response.ok(planJson).build();
    }

    @PUT
    @Path("/{planId}/revertToDraft")
    @ApiOperation(value = "Revert a plan to draft", notes = "Marks a plan as draft", response = PlanJson.class, tags = { "Product Catalog" })
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response deactivatePlan(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId
    ) {
        var plan = productCatalog.revertToDraft(planId);
        var planJson = planMapper.planToJson(plan);
        return Response.ok(planJson).build();
    }

    @PUT
    @Path("/{planId}/deprecate")
    @ApiOperation(
        value = "Deprecate a plan",
        notes = "Marks a plan as deprecated. Once deprecated a plan may not be attached to new orders.",
        response = PlanJson.class,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response deprecatePlan(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId
    ) {
        var plan = productCatalog.deprecatePlan(planId);
        var planJson = planMapper.planToJson(plan);
        return Response.ok(planJson).build();
    }

    @PUT
    @Path("/{planId}/reactivate")
    @ApiOperation(value = "Reactivate a plan", notes = "Reactivates a deprecated plan.", response = PlanJson.class, tags = { "Product Catalog" })
    @AllowRoles({ Role.ADMIN, Role.BILLY_ENGINEER })
    public Response reactivatePlan(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId
    ) {
        var plan = productCatalog.reactivatePlan(planId);
        var planJson = planMapper.planToJson(plan);
        return Response.ok(planJson).build();
    }

    @POST
    @Path("/{planId}/duplicate")
    @ApiOperation(
        value = "Duplicate a plan",
        notes = "Duplicates the specified plan. On success the new plan id is returned.",
        response = PlanJson.class,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response duplicatePlan(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan to duplicate", required = true) String planId,
        @Context UriInfo uriInfo
    ) {
        Plan duplicatePlan = productCatalog.duplicatePlan(planId);

        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(duplicatePlan.getPlanId());
        PlanJson duplicatePlanJson = planMapper.planToJson(duplicatePlan);
        return Response.created(builder.build()).entity(duplicatePlanJson).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/{planId}/charges")
    @ApiOperation(
        value = "Add charge to plan",
        notes = "Adds a charge to the specified plan.  Success response contains ID of the new charge.",
        response = ChargeJson.class,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response addCharge(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(
            value = "ID of the plan to which you want to add the charge. To get a list of IDs, call the [Get plans](/reference/getplans) operation.",
            required = true
        ) String planId,
        @NotNull @Valid @ApiParam(value = "JSON representing the charge details", required = true) ChargeJson chargeJson,
        @Context UriInfo uriInfo
    ) {
        var charge = planMapper.jsonToCharge(chargeJson);
        var chargeId = chargeService.addChargeToExistingPlan(planId, charge);
        var savedCharge = productCatalogGetService.getChargeByChargeId(chargeId);

        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(chargeId);
        ChargeJson savedChargeJson = planMapper.chargeToJson(savedCharge);
        return Response.created(builder.build()).entity(savedChargeJson).build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/{planId}/charges/{chargeId}")
    @ApiOperation(
        value = "Update charge details",
        notes = "Updates the details of the specified charge on the specified plan.",
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response updateCharge(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan associated with the charge", required = true) String planId,
        @PathParam("chargeId") @ApiParam(value = "id of the charge", required = true) String chargeId,
        @NotNull @Valid ChargeJson chargeJson
    ) {
        var charge = planMapper.jsonToCharge(chargeJson);
        charge.setChargeId(chargeId);
        chargeService.updateCharge(planId, charge);
        return Response.ok().build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/{planId}/charges/{chargeId}/partial")
    @ApiOperation(
        value = "Update non-financial charge details",
        notes = "Updates certain details of the specified charge which won't impact its financial treatment",
        response = ChargeJson.class,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response patchCharge(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId,
        @PathParam("chargeId") @ApiParam(value = "id of the charge", required = true) String chargeId,
        @NotNull @Valid ChargePartialJson chargePartialJson
    ) {
        chargePartialJson.setId(chargeId);
        Charge charge = chargeService.updateChargePartial(planId, chargePartialJson);
        return Response.ok(planMapper.chargeToJson(charge)).build();
    }

    @DELETE
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/{planId}/charges/{chargeId}")
    @ApiOperation(value = "Delete a charge", notes = "Removes a charge from a plan.", response = ChargeJson.class, tags = { "Product Catalog" })
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response deleteCharge(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId,
        @PathParam("chargeId") @ApiParam(value = "id of the charge", required = true) String chargeId
    ) {
        Charge charge = chargeService.deleteCharge(planId, chargeId);
        return Response.ok(planMapper.chargeToJson(charge)).build();
    }

    // hack to get response documented
    private static class PlanJsonPaginationResponse extends PaginationResponseJson<Plan, PlanJson> {

        private PlanJsonPaginationResponse(List<Plan> resource, int limit, Function<List<Plan>, List<PlanJson>> toJson, Function<Plan, UUID> getId) {
            super(resource, limit, toJson, getId);
        }
    }

    @GET
    @ApiOperation(
        value = "Get plans",
        notes = "Returns all plans for a product. The result is paginated. To retrieve all results pass the cursor " +
        "returned from a call to the next call until all results are returned.",
        response = PlanJsonPaginationResponse.class,
        tags = { "Product Catalog" }
    )
    @AllowAllRoles
    public Response getPlans(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("productId") @ApiParam(value = "id of the product") String productId,
        @QueryParam("status") @ApiParam(value = "filter by plan status") PlanStatus planStatus,
        @QueryParam("cursor") @ApiParam(value = "cursor from the last call") UUID cursor,
        @QueryParam("limit") @ApiParam(value = "number of results per page") int limit
    ) {
        var paginationQueryParams = new PaginationQueryParams(cursor, limit);
        var plans = productCatalogGetService.getPlans(productId, Optional.ofNullable(planStatus), null, null, paginationQueryParams, true);
        var paginationJson = new PaginationResponseJson<>(plans, paginationQueryParams.getLimit(), planMapper::plansToJson, Plan::getId);
        return Response.ok(paginationJson).build();
    }

    @GET
    @Path("/{planId}")
    @ApiOperation(
        value = "Get plan details",
        notes = "Returns the details of the specified plan.",
        response = PlanJson.class,
        tags = { "Product Catalog" }
    )
    @AllowAllRoles
    public Response getPlan(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId
    ) {
        var plan = productCatalogGetService.getPlan(planId);
        return Response.ok(planMapper.planToJson(plan)).build();
    }

    @GET
    @Path("/{planId}/charges/{chargeId}")
    @ApiOperation(
        value = "Get charge details",
        notes = "Gets the details of the specified charge on the specified plan.",
        response = ChargeJson.class,
        tags = { "Product Catalog" },
        hidden = true
    )
    @AllowAllRoles
    public Response getCharge(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId,
        @PathParam("chargeId") @ApiParam(value = "id of the charge", required = true) @NotEmpty String chargeId
    ) {
        Charge charge = productCatalogGetService.getCharge(planId, chargeId);
        return Response.ok(planMapper.chargeToJson(charge)).build();
    }

    @GET
    @Path("/{planId}/charges/{chargeId}/ledgerAccounts")
    @ApiOperation(
        value = "Get ledger accounts",
        notes = "Gets the ledger accounts mapped to the specified charge",
        response = LedgerAccount.class,
        responseContainer = "List",
        tags = { "Product Catalog" }
    )
    @AllowAllRoles
    public Response getChargeLedgerAccounts(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId,
        @PathParam("chargeId") @ApiParam(value = "id of the charge", required = true) @NotEmpty String chargeId
    ) {
        // make sure charge exists
        productCatalogGetService.getCharge(planId, chargeId);
        return Response.ok(accountingGetService.getLedgerAccountsForCharge(chargeId)).build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/{planId}/charges/{chargeId}/ledgerAccounts")
    @ApiOperation(
        value = "Map ledger accounts",
        notes = "Map ledger accounts to the specified charge for the specified plan.",
        response = LedgerAccount.class,
        responseContainer = "List",
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response mapLedgerAccountsToCharge(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @NotEmpty @ApiParam(value = "id of the plan", required = true) String planId,
        @PathParam("chargeId") @NotEmpty @ApiParam(value = "id of the charge", required = true) String chargeId,
        @NotNull @ApiParam(value = "list of the ledger account ids", required = true) List<String> ledgerAccountIds
    ) {
        productCatalogGetService.getCharge(planId, chargeId);
        return Response.ok(accountingService.mapLedgerAccountsToCharge(chargeId, ledgerAccountIds)).build();
    }

    @POST
    @Path("relationships/percentOf")
    @Deprecated
    @ApiOperation(
        value = "Create percent of relationship",
        notes = "Creates a percent of relationship between a charge and plan. On success the id of the relationship is returned.",
        hidden = true,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response addPercentOfPlanRelationship(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @ApiParam(value = "json representing the relationship", required = true) CatalogRelationship catalogRelationship,
        @Context UriInfo uriInfo
    ) {
        CatalogRelationship stored = catalogRelationshipService.addCatalogRelationship(catalogRelationship);
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(stored.getRelationshipId());
        return Response.created(builder.build()).build();
    }

    @GET
    @Path("relationships/percentOf")
    @Deprecated
    @ApiOperation(
        value = "Get percent of relationships for a plan",
        notes = "Gets the percent of relationships for the specified plan.",
        response = CatalogRelationship.class,
        hidden = true,
        responseContainer = "List",
        tags = { "Product Catalog" }
    )
    @AllowAllRoles
    public Response getPercentOfRelationships(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("fromPlanId") @NotEmpty @ApiParam(name = "fromPlanId", value = "id of the plan", required = true) String fromPlanId
    ) {
        List<CatalogRelationship> relationships = catalogRelationshipGetService.getRelationshipsForPercentOfPlanId(fromPlanId);
        return Response.ok(relationships).build();
    }

    @Deprecated
    @GET
    @Path("relationships/percentOf/{planId}")
    @ApiOperation(
        value = "Get percent of target plans",
        notes = "Gets the list of percent of charge target plans.",
        hidden = true,
        response = CatalogRelationship.class,
        tags = { "Product Catalog" }
    )
    @AllowAllRoles
    public Response getTargetPlansForPercentOfPlanId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @NotEmpty String planId
    ) {
        List<Plan> toPlans = productCatalogGetService.getTargetPlansForPercentOfPlan(planId);
        return Response.ok(planMapper.plansToJson(toPlans)).build();
    }

    @GET
    @Deprecated
    @Path("relationships/{relationshipId}")
    @ApiOperation(
        value = "Get relationship details",
        notes = "Gets the details of the specified catalog relationship.",
        hidden = true,
        response = CatalogRelationship.class,
        tags = { "Product Catalog" }
    )
    @AllowAllRoles
    public Response getCatalogRelationshipById(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("relationshipId") @NotEmpty @ApiParam(value = "id of the relationship", required = true) String relationshipId
    ) {
        CatalogRelationship relationship = catalogRelationshipGetService.getCatalogRelationshipById(relationshipId);
        return Response.ok(relationship).build();
    }

    @DELETE
    @Deprecated
    @Path("relationships/{relationshipId}")
    @ApiOperation(
        value = "Delete a catalog relationship",
        notes = "Deletes the specified catalog relationship",
        hidden = true,
        response = CatalogRelationship.class,
        tags = { "Product Catalog" }
    )
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    public Response deleteCatalogRelationshipById(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("relationshipId") @NotEmpty @ApiParam(value = "id of the relationship", required = true) String relationshipId
    ) {
        CatalogRelationship relationship = catalogRelationshipService.deleteCatalogRelationship(relationshipId);
        return Response.ok(relationship).build();
    }

    @GET
    @Path("/{planId}/replacedPlans")
    @ApiOperation(value = "Get replaced plans", response = PlanJson.class, responseContainer = "List", tags = { "Product Catalog" })
    @AllowAllRoles
    public Response getReplacedPlans(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("planId") @ApiParam(value = "id of the plan", required = true) String planId
    ) {
        List<Plan> replacedPlans = productCatalogGetService.getReplacedPlans(planId);
        return Response.ok(planMapper.plansToJson(replacedPlans)).build();
    }

    @GET
    @Path("/filter/customField")
    @ApiOperation(
        value = "Get Plans that have a specific value for a custom field",
        notes = "Gets Plans that have a specific value for a custom field",
        response = PlanJson.class,
        responseContainer = "List",
        tags = { "Product Catalog" }
    )
    @AllowAllRoles
    public Response getPlansFilterByCustomFields(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotEmpty @QueryParam("customFieldName") @ApiParam(value = "Name of the custom field to filter by") String customFieldName,
        @NotEmpty @QueryParam("customFieldValue") @ApiParam(value = "Value of the custom field to filter by") String customFieldValue
    ) {
        List<Plan> plans = productCatalogGetService.getPlansByCustomField(customFieldName, customFieldValue);
        List<PlanJson> result = planMapper.plansToJson(plans);
        return Response.ok(result).build();
    }
}
