package com.subskribe.billy.resources.json.taxrate;

import com.subskribe.billy.productcatalog.model.TaxRateStrategy;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface TaxRateStrategyJsonMapper {
    @Mapping(source = "taxRateStrategyId", target = "id")
    List<TaxRateStrategyJson> toTaxRateStrategiesJson(List<TaxRateStrategy> taxRateStrategies);

    @Mapping(source = "taxRateStrategyId", target = "id")
    TaxRateStrategyJson toTaxRateStrategyJson(TaxRateStrategy taxRateStrategy);

    @Mapping(source = "id", target = "taxRateStrategyId")
    @Mapping(target = "id", ignore = true)
    TaxRateStrategy fromTaxRateStrategyJson(TaxRateStrategyJson taxRateStrategyJson);
}
