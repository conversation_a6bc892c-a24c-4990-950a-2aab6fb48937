package com.subskribe.billy.resources;

import static javax.ws.rs.core.Response.Status.NOT_FOUND;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.EvergreenUtils;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderStub;
import com.subskribe.billy.order.services.ChangeOrderService;
import com.subskribe.billy.order.services.OrderDataBackfillService;
import com.subskribe.billy.order.services.OrderDocumentService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderIdGenerator;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.resources.json.metrics.MetricsJson;
import com.subskribe.billy.resources.json.metrics.MetricsJsonMapper;
import com.subskribe.billy.resources.json.order.CustomBillingScheduleMapper;
import com.subskribe.billy.resources.json.order.CustomBillingScheduleOutput;
import com.subskribe.billy.resources.json.order.OrderAttributesUpdateRequest;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.resources.json.order.OrderRequestJson;
import com.subskribe.billy.resources.shared.FileResponseBuilder;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.resources.shared.PaginationResponseJson;
import com.subskribe.billy.resources.shared.ResponseUtility;
import com.subskribe.billy.salesroom.intelligent.model.response.IntelligentSalesRoomOverviewResponse;
import com.subskribe.billy.salesroom.intelligent.service.IntelligentSalesRoomService;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.dropwizard.jersey.errors.ErrorMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.ForbiddenException;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataParam;
import org.mapstruct.factory.Mappers;

@Path("/orders")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class OrderResource {

    private static final int MAX_PAGINATION_LIMIT_FOR_GET_ORDERS = 50;

    private final OrderService orderService;
    private final OrderGetService orderGetService;
    private final ChangeOrderService changeOrderService;
    private final OrderMapper orderMapper;
    private final OrderDocumentService orderDocumentService;
    private final MetricsService metricsService;
    private final MetricsJsonMapper metricsJsonMapper;
    private final CustomBillingScheduleMapper customBillingScheduleMapper;
    private final OrderDataBackfillService orderDataBackfillService;
    private final BillyConfiguration billyConfiguration;
    private final TenantSettingService tenantSettingService;

    private final IntelligentSalesRoomService intelligentSalesRoomService;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public OrderResource(
        OrderService orderService,
        OrderGetService orderGetService,
        ChangeOrderService changeOrderService,
        OrderDocumentService orderDocumentService,
        MetricsService metricsService,
        OrderDataBackfillService orderExecutionDateAndCrmOppUpdateService,
        BillyConfiguration billyConfiguration,
        TenantSettingService tenantSettingService,
        IntelligentSalesRoomService intelligentSalesRoomService,
        TenantIdProvider tenantIdProvider
    ) {
        this.orderService = orderService;
        this.orderGetService = orderGetService;
        this.changeOrderService = changeOrderService;
        this.orderDocumentService = orderDocumentService;
        this.metricsService = metricsService;
        orderDataBackfillService = orderExecutionDateAndCrmOppUpdateService;
        this.billyConfiguration = billyConfiguration;
        this.tenantSettingService = tenantSettingService;
        this.intelligentSalesRoomService = intelligentSalesRoomService;
        this.tenantIdProvider = tenantIdProvider;
        orderMapper = Mappers.getMapper(OrderMapper.class);
        metricsJsonMapper = Mappers.getMapper(MetricsJsonMapper.class);
        customBillingScheduleMapper = Mappers.getMapper(CustomBillingScheduleMapper.class);
    }

    @POST
    @Path("/{id}/pdf")
    @ApiOperation(
        value = "Generate an order PDF",
        notes = "Generate and retrieve a PDF representation of the order details for a specific order by its ID.",
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response createOrderDocument(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the Order.", required = true) String orderId,
        @QueryParam("force") @ApiParam(
            value = "Force regeneration of the PDF document even if there has been no changes. Defaults to false."
        ) boolean force
    ) {
        return createOrderDocumentWithoutChecks(orderId, force);
    }

    private Response createOrderDocumentWithoutChecks(String orderId, boolean force) {
        OrderStub orderStub = orderGetService.getOrderStubOrThrow(orderId);
        if (orderStub.getCompositeOrderId().isPresent()) {
            throw new IllegalArgumentException("Use composite order resource to generate pdf for composite orders.");
        }

        if (!force && BooleanUtils.isNotTrue(orderStub.getShouldRegeneratePdf())) {
            return Response.ok().build();
        }

        try {
            orderDocumentService.createOrderDocumentWithoutChecks(orderId);
            return Response.ok().build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
        }
    }

    @POST
    @Path("/salesRoom/{shareLink}/pdf")
    @ApiOperation(
        value = "Generate an order PDF via Sales Room Share Link",
        notes = "Generate and retrieve a PDF representation of the order details for a specific sales room using share link.",
        tags = { "Intelligent Sales Room" }
    )
    @AllowUnauthenticated(cascade = true)
    public Response createOrderDocumentForSalesRoom(
        @NotEmpty @PathParam("shareLink") String shareLink,
        @QueryParam("force") @ApiParam(
            value = "Force regeneration of the PDF document even if there has been no changes. Defaults to false."
        ) boolean force
    ) {
        IntelligentSalesRoomOverviewResponse salesRoom = intelligentSalesRoomService.getSalesRoomByShareLink(shareLink);

        return TenantContextInjector.callInTenantContext(salesRoom.getTenantId(), tenantIdProvider, () ->
            createOrderDocumentWithoutChecks(salesRoom.getOrderId(), force)
        );
    }

    @GET
    @Path("/{id}/pdf")
    @ApiOperation(
        value = "Fetch order form PDF",
        notes = "Retrieves the PDF version of the order form for a specific order identified by its ID.",
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response getOrderDocument(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the Order.", required = true) String orderId
    ) {
        Optional<InputStream> inputStream = orderDocumentService.getOrderDocumentCreateIfNeeded(orderId);

        if (inputStream.isEmpty()) {
            return Response.status(Response.Status.ACCEPTED).build();
        }

        String fileName = orderDocumentService.getOrderDocumentFileName(orderId);
        return FileResponseBuilder.getFileResponse(inputStream.get(), fileName);
    }

    @GET
    @Path("{orderId}/pdf/{id}")
    @ApiOperation(
        value = "Fetch specific order form PDF on order",
        notes = "Retrieves specific PDF of the order form for a specific order identified by its orderId.",
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response getSpecificOrderDocument(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("orderId") @ApiParam(value = "Uniquely identifies the orderId", required = true) String orderId,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the pdf id.", required = true) String id
    ) {
        Optional<InputStream> inputStream = orderDocumentService.getOrderDocumentByIdAndOrderId(id, orderId);

        if (inputStream.isEmpty()) {
            return Response.status(Response.Status.ACCEPTED).build();
        }

        String fileName = orderDocumentService.getOrderDocumentFileName(orderId);
        return FileResponseBuilder.getFileResponse(inputStream.get(), fileName);
    }

    @GET
    @Path("/{id}/doc")
    @ApiOperation(
        value = "Download word doc version of order form",
        notes = "Download a Microsoft Word document of the order form for a specific order by its ID.",
        tags = { "Orders" }
    )
    // fetches the stored version of word doc and sends the output
    @AllowAllRoles
    public Response getOrderDocumentDoc(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the Order.", required = true) String orderId
    ) {
        Optional<InputStream> inputStream = orderDocumentService.getOrCreateOrderDocumentDoc(orderId);
        if (inputStream.isEmpty()) {
            return Response.status(Response.Status.ACCEPTED).build();
        }
        String fileName = orderDocumentService.getOrderDocumentFileName(orderId);
        return FileResponseBuilder.getDocResponse(inputStream.get(), fileName);
    }

    @GET
    @Path("/{id}/docx")
    @ApiOperation(
        value = "Download word docx version of order form",
        notes = "Download a Microsoft Word document of the order form for a specific order by its ID.",
        tags = { "Orders" }
    )
    // fetches the stored version of word doc and sends the output
    @AllowAllRoles
    public Response getOrderDocumentDocx(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the Order.", required = true) String orderId
    ) {
        validateDocxPermissions(billyAuthPrincipal);
        Optional<InputStream> inputStream = orderDocumentService.getOrCreateOrderDocumentDocx(orderId);
        if (inputStream.isEmpty()) {
            return Response.status(Response.Status.ACCEPTED).build();
        }
        StreamingOutput output = orderDocumentService.setDocxPermissions(inputStream.get());

        String fileName = orderDocumentService.getOrderDocumentFileName(orderId);
        return FileResponseBuilder.getDocxResponse(output, fileName);
    }

    private void validateDocxPermissions(BillyAuthPrincipal billyAuthPrincipal) {
        TenantSetting tenantSetting = tenantSettingService.getTenantSettingInternal();
        if (BooleanUtils.isFalse(tenantSetting.getIsDocxAdminOnly())) {
            return;
        }
        boolean hasAdminPermissions = billyAuthPrincipal.getRole().map(role -> role == Role.ADMIN).orElse(false);
        if (!hasAdminPermissions) {
            throw new ForbiddenException("Only admin users can access DOCX files");
        }
    }

    @GET
    @Path("/{id}/preview/html")
    @Produces({ MediaType.TEXT_HTML, MediaType.APPLICATION_JSON })
    @ApiOperation(value = "Test API for E2E testing", hidden = true, tags = { "Orders" })
    @AllowAllRoles
    public Response getOrderDocumentPreview(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @PathParam("id") String orderId) {
        String html = orderDocumentService.renderOrderHTML(orderId);
        return Response.ok(html).build();
    }

    @GET
    @Path("/{id}/preview/{fileName}")
    @Produces({ MediaType.TEXT_PLAIN, MediaType.APPLICATION_JSON })
    @ApiOperation(value = "Test API for E2E testing", hidden = true, tags = { "Orders" })
    @AllowAllRoles
    public Response getOrderDocumentPreviewAsset(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") String orderId,
        @PathParam("fileName") String fileName
    ) {
        throwIfNotLocal();
        var optionalBytes = orderDocumentService.getAssetContents(fileName);
        if (optionalBytes.isEmpty()) {
            return Response.status(NOT_FOUND).build();
        }
        return Response.ok(optionalBytes.get()).build();
    }

    private static class OrderJsonPaginationResponse extends PaginationResponseJson<Order, OrderJson> {

        private OrderJsonPaginationResponse(
            List<Order> resource,
            int limit,
            Function<List<Order>, List<OrderJson>> toJson,
            Function<Order, UUID> getId
        ) {
            super(resource, limit, toJson, getId);
        }
    }

    @GET
    @ApiOperation(
        value = "Get all Orders",
        notes = "Gets all orders for your tenant. The results are paginated. To fetch all results, take the cursor " +
        "returned from a call and pass it to subsequent calls.",
        response = OrderJsonPaginationResponse.class,
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response getOrders(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("cursor") @ApiParam(
            name = "cursor",
            value = "A string token is used to fetch next set of results. If not provided, the first page of results will be returned. Use the 'next_cursor' value from the previous response to fetch the next page."
        ) UUID cursor,
        @QueryParam("limit") @ApiParam(
            name = "limit",
            value = "An integer specifying the maximum number of results to return per page. Defaults to 10 if not provided. Limit is capped to " +
            MAX_PAGINATION_LIMIT_FOR_GET_ORDERS +
            " orders"
        ) int limit
    ) {
        if (limit > MAX_PAGINATION_LIMIT_FOR_GET_ORDERS) {
            limit = MAX_PAGINATION_LIMIT_FOR_GET_ORDERS;
        }

        var paginationQueryParams = new PaginationQueryParams(cursor, limit);
        var orders = orderGetService.getOrders(paginationQueryParams, Optional.empty());
        orders.forEach(EvergreenUtils::sanitizeEvergreenOrder);
        Function<List<Order>, List<OrderJson>> ordersToJson = orderMapper::ordersToJson;
        var paginationJson = new PaginationResponseJson<>(orders, paginationQueryParams.getLimit(), ordersToJson, Order::getId);

        return Response.ok(paginationJson).build();
    }

    @GET
    @Path("/{id}")
    @ApiOperation(
        value = "Get order details",
        notes = "Retrieve details of a specific order by its ID.",
        response = OrderJson.class,
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response getOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(name = "id", value = "Uniquely identifies the Order.", required = true) String id
    ) {
        OrderIdGenerator.verifyPrefixMatches(id);
        var order = orderGetService.getOrderByOrderId(id);
        EvergreenUtils.sanitizeEvergreenOrder(order);
        return Response.ok(orderMapper.orderToJson(order)).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Create an order",
        notes = "creates an order with the specified parameters. On success the order id is returned.",
        response = OrderJson.class,
        tags = { "Orders" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    public Response addOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(value = "json representing the order details.", required = true) OrderRequestJson orderRequestJson,
        @QueryParam("isDryRun") @ApiParam(name = "isDryRun", value = "indicates whether this order should be persisted.") Boolean isDryRun,
        @QueryParam("populateMissingLines") @ApiParam(
            name = "populateMissingLines",
            value = "indicates whether an amendment should populate missing lines not provided here."
        ) Boolean populateMissingLines,
        @Context UriInfo uriInfo
    ) {
        var order = orderMapper.jsonToOrder(orderRequestJson);
        isDryRun = BooleanUtils.isTrue(isDryRun);
        populateMissingLines = BooleanUtils.isTrue(populateMissingLines);

        Order resultOrder;
        switch (order.getOrderType()) {
            case NEW -> resultOrder = orderService.addOrder(order, isDryRun);
            case AMENDMENT, CANCEL -> resultOrder = changeOrderService.createChangeOrder(order, isDryRun, populateMissingLines);
            case RENEWAL -> resultOrder = orderService.renewSubscription(order, isDryRun);
            default -> {
                return Response.status(Response.Status.BAD_REQUEST)
                    .entity(new ErrorMessage(Response.Status.BAD_REQUEST.getStatusCode(), "unsupported order_type"))
                    .build();
            }
        }
        EvergreenUtils.sanitizeEvergreenOrder(order);

        if (isDryRun) {
            return Response.noContent().build();
        }
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(resultOrder.getOrderId());
        OrderJson resultOrderJson = orderMapper.orderToJson(resultOrder);
        return Response.created(builder.build()).entity(resultOrderJson).build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Update order details", notes = "Updates the details of the specified order.", tags = { "Orders" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    public Response updateOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(value = "json representing the order details.", required = true) OrderRequestJson orderRequestJson,
        @QueryParam("isDryRun") @ApiParam(name = "isDryRun", value = "true if the order should not be persisted.") Boolean isDryRun,
        @Context UriInfo uriInfo
    ) {
        var order = orderMapper.jsonToOrder(orderRequestJson);
        isDryRun = BooleanUtils.isTrue(isDryRun);

        var tenantId = billyAuthPrincipal.getTenantId().getRequiredId();
        order.getLineItems().forEach(li -> li.setTenantId(tenantId));
        order.setTenantId(tenantId);

        switch (orderRequestJson.getOrderType()) {
            case NEW, RENEWAL -> orderService.updateOrder(order, isDryRun);
            case AMENDMENT, CANCEL -> changeOrderService.updateChangeOrder(order, isDryRun, false, false);
            default -> {
                return Response.status(400, "unsupported order_type").build();
            }
        }

        return Response.ok().build();
    }

    @PUT
    @Path("/{id}/execute")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Mark order as executed.",
        notes = "Marks the order as executed. Optionally, the execution time can be specified using the executedOn query parameter.",
        response = OrderJson.class,
        tags = { "Orders" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES_MANAGER })
    public Response executeOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the Order.", required = true) String id,
        @QueryParam("executedOn") @ApiParam(name = "executedOn", value = "The date and time when the order was executed.") Long executedOn,
        @QueryParam("adminApprovalFlowBypass") @ApiParam(
            name = "adminApprovalFlowBypass",
            value = "Bypass approval flows by admin"
        ) Boolean adminApprovalFlowBypass
    ) {
        boolean adminApprovalByPass = BooleanUtils.isTrue(adminApprovalFlowBypass);
        orderGetService.getOrderByOrderId(id);
        orderService.moveOrderToExecuted(id, executedOn, adminApprovalByPass);
        var order = orderGetService.getOrderByOrderId(id);
        EvergreenUtils.sanitizeEvergreenOrder(order);
        return Response.ok(orderMapper.orderToJson(order)).build();
    }

    @PUT
    @Path("/{id}/execute/force")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Mark order as executed",
        notes = "Forcefully executes an order. This endpoint bypasses approval checks and immediately executes the order.",
        response = OrderJson.class,
        tags = { "Orders" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response forceExecuteOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the Order.", required = true) String id,
        @QueryParam("executedOn") @ApiParam(name = "executedOn", value = "The date and time when the order was executed.") Long executedOn,
        @QueryParam("skipApprovalCheck") @ApiParam(
            name = "skipApprovalCheck",
            value = "Indicates whether to skip the approval check. Default is false."
        ) boolean skipApprovalCheck,
        @Context UriInfo uriInfo
    ) {
        orderGetService.getOrderByOrderId(id);
        orderService.moveOrderToExecuted(id, executedOn, skipApprovalCheck);
        var order = orderGetService.getOrderByOrderId(id);
        EvergreenUtils.sanitizeEvergreenOrder(order);
        return Response.ok(orderMapper.orderToJson(order)).build();
    }

    @PUT
    @Path("/{id}/status/{status}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Update order status", notes = "Update the status of a specific order by its ID.", tags = { "Orders" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    public Response updateOrderStatus(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(name = "id", value = "Uniquely identifies the Order.", required = true) String id,
        @PathParam("status") @ApiParam(
            name = "status",
            value = "New status to be set for the order (e.g: Draft, Submitted, Executed, Cancelled)",
            required = true,
            allowableValues = "DRAFT, SUBMITTED, EXECUTED, CANCELLED"
        ) String status,
        @QueryParam("statusUpdatedOn") @ApiParam(
            name = "statusUpdatedOn",
            value = "The timestamp when order status was updated."
        ) Long statusUpdatedOn,
        @QueryParam("adminApprovalFlowByPass") @ApiParam(
            name = "adminApprovalFlowByPass",
            value = "Admin approval to bypass the approval flow"
        ) Boolean adminApprovalByPass,
        @Context UriInfo uriInfo
    ) {
        boolean adminApprovalFlowByPass = BooleanUtils.isTrue(adminApprovalByPass);
        OrderStub orderStub = orderGetService.getOrderStubOrThrow(id);
        if (orderStub.getCompositeOrderId().isPresent()) {
            throw new IllegalArgumentException("Use composite orders rest api to change status of composite orders.");
        }

        OrderStatus orderStatus = OrderStatus.valueOf(status.toUpperCase());
        Optional<Instant> statusUpdatedOnInstant = Optional.ofNullable(statusUpdatedOn).map(Instant::ofEpochSecond);
        orderService.updateOrderStatus(id, orderStatus, statusUpdatedOnInstant, adminApprovalFlowByPass);

        return Response.status(Response.Status.OK).build();
    }

    @DELETE
    @Path("/{id}")
    @ApiOperation(value = "Delete an order", notes = "Delete a specific order by its ID.", response = OrderJson.class, tags = { "Orders" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    public Response deleteOrder(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Unique identifier of the order to be deleted.", required = true) String id
    ) {
        Order order = orderService.deleteOrder(id);
        EvergreenUtils.sanitizeEvergreenOrder(order);
        return Response.status(Response.Status.OK).entity(orderMapper.orderToJson(order)).build();
    }

    @GET
    @Path("/{id}/metrics")
    @ApiOperation(
        value = "Get order metrics",
        notes = "Retrieve the metrics for a specific order by its ID. Metrics can be filtered by a target date.",
        response = MetricsJson.class,
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response getOrderMetrics(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the Order.", required = true) String orderId,
        @QueryParam("targetDate") @ApiParam(name = "targetDate", value = "The target date for filtering metrics.") Long targetDate
    ) {
        Instant target = targetDate == null ? Instant.now() : Instant.ofEpochSecond(targetDate);
        Metrics orderMetrics = metricsService.getOrderMetrics(orderId, target);
        MetricsJson json = metricsJsonMapper.metricsToJson(orderMetrics);
        return Response.ok(json).build();
    }

    @GET
    @Path("/{id}/stored-metrics")
    @ApiOperation(
        value = "Get stored order metrics",
        notes = "Retrieve the stored metrics for a specific order by its ID.",
        response = MetricsJson.class,
        tags = { "Orders" },
        hidden = true
    )
    @AllowAllRoles
    public Response getStoredOrderMetrics(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the Order.", required = true) String orderId
    ) {
        Metrics orderMetrics = metricsService.getStoredMetrics(orderId);
        MetricsJson json = metricsJsonMapper.metricsToJson(orderMetrics);
        return Response.ok(json).build();
    }

    @GET
    @Path("/{id}/annualTotal")
    @ApiOperation(value = "Test API for E2E testing", hidden = true, tags = { "Orders" })
    @AllowAllRoles
    public Response getOrderYearlyAmount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the Order.", required = true) String orderId
    ) {
        Map<Period, BigDecimal> yearlyAmounts = metricsService.getOrderAnnualTotal(orderId);
        return Response.ok(yearlyAmounts).build();
    }

    @GET
    @Path("/{id}/lineItems/metrics")
    @ApiOperation(
        value = "Get order line metrics",
        notes = "Retrieves metrics for all line items associated with the specified order.",
        response = MetricsJson.class,
        responseContainer = "Map",
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response getOrderLineMetrics(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @ApiParam(value = "Uniquely identifies the Order.", required = true) String orderId
    ) {
        Map<String, Metrics> orderLineMetrics = metricsService.getMetricsForAllOrderLines(orderId);
        Map<String, MetricsJson> json = orderLineMetrics
            .entrySet()
            .stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> metricsJsonMapper.metricsToJson(entry.getValue())));
        return Response.ok(json).build();
    }

    @POST
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON }) //produces json if there is an error
    @Path("/backfillOrderExecutionDateAndCrmOpportunity/csv")
    @ApiOperation(
        value = "Backfill order execution date and crm opportunity",
        notes = "Backfill API to update order execution date and crm opportunity on orders",
        hidden = true
    )
    @AllowRoles({ Role.ADMIN })
    public Response backfillOrderExecutionDateAndCrmOpportunityId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @FormDataParam("file") InputStream importStream,
        @FormDataParam("file") FormDataBodyPart formDataBodyPart,
        @QueryParam("isDryRun") String isDryRunString,
        @Context UriInfo uriInfo
    ) {
        boolean isDryRun = !Boolean.FALSE.toString().equalsIgnoreCase(isDryRunString);
        Pair<InputStream, String> importedStreamDetails = orderDataBackfillService.updateOrderExecutionDateAndCrmOppId(
            formDataBodyPart,
            importStream,
            isDryRun
        );
        return ResponseUtility.okResponseFromInputStreamFileNamePair(importedStreamDetails, "text/csv");
    }

    @POST
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces({ MediaType.APPLICATION_OCTET_STREAM, MediaType.APPLICATION_JSON }) //produces json if there is an error
    @Path("/backfillCrmOpportunity/csv")
    @ApiOperation(value = "Backfill crm opportunity", notes = "Backfill API to update crm opportunity on orders", hidden = true)
    @AllowRoles({ Role.ADMIN })
    public Response backfillCrmOpportunityId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @FormDataParam("file") InputStream importStream,
        @FormDataParam("file") FormDataBodyPart formDataBodyPart,
        @QueryParam("isDryRun") String isDryRunString,
        @Context UriInfo uriInfo
    ) {
        boolean isDryRun = !Boolean.FALSE.toString().equalsIgnoreCase(isDryRunString);
        Pair<InputStream, String> importedStreamDetails = orderDataBackfillService.updateOrderCrmOppId(formDataBodyPart, importStream, isDryRun);
        return ResponseUtility.okResponseFromInputStreamFileNamePair(importedStreamDetails, "text/csv");
    }

    @POST
    @Path("/test/expiry/{orderId}")
    @ApiOperation(value = "Test API for E2E testing", hidden = true, tags = { "Orders" })
    @AllowAllRoles
    public Response expireOrder(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @PathParam("orderId") String orderId) {
        throwIfNotLocal();
        Order order = orderGetService.getOrderByOrderId(orderId);
        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        orderService.expireOrder(order, Instant.now(), tenantTimeZone);

        return Response.ok().build();
    }

    @PUT
    @Path("/{orderId}/rebase")
    @ApiOperation(
        value = "Rebase amendment against latest subscription version",
        notes = "Rebase a specific order by its ID. Rebasing an order involves recalculating its metrics or values based on updated data or criteria.",
        response = OrderJson.class,
        tags = { "Orders" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response rebaseAmendment(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("orderId") @ApiParam(value = "Uniquely identifies the Order.", required = true) String orderId
    ) {
        Order order = changeOrderService.rebaseAmendment(orderId);
        return Response.ok(orderMapper.orderToJson(order)).build();
    }

    @GET
    @Path("/{orderId}/billing/custom")
    @ApiOperation(
        value = "Get custom billing schedule for the order",
        notes = "Get the custom billing schedule for the order",
        response = CustomBillingScheduleOutput.class,
        tags = { "Orders" }
    )
    @AllowAllRoles
    public Response getCustomBillingSchedule(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("orderId") @ApiParam(value = "Uniquely identifies the Order.", required = true) @NotBlank String orderId
    ) {
        CustomBillingSchedule customBillingSchedule = orderGetService.getCustomBillingScheduleForOrder(orderId, false);
        return Response.ok(customBillingScheduleMapper.toOutput(customBillingSchedule)).build();
    }

    @PUT
    @Path("/{orderId}/attributes")
    @ApiOperation(value = "Update order attributes for non-draft orders", notes = "Update order attributes for non-draft orders", tags = { "Orders" })
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    public Response updateOrderAttributes(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("orderId") @ApiParam(value = "Uniquely identifies the Order.", required = true) @NotBlank String orderId,
        @ApiParam(value = "Order Attributes") OrderAttributesUpdateRequest orderAttributesUpdateRequest
    ) {
        orderService.updateOrderAttributes(orderId, orderAttributesUpdateRequest);
        return Response.ok().build();
    }

    private void throwIfNotLocal() {
        if (billyConfiguration.isLocalOrCi()) {
            return;
        }

        throw new ForbiddenException("Not allowed.");
    }
}
