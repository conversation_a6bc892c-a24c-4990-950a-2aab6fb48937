package com.subskribe.billy.resources;

import static javax.ws.rs.core.Response.Status.BAD_REQUEST;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.admin.AdminOperationsService;
import com.subskribe.billy.admin.model.PaymentDeletableResponse;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.graphql.payment.PaymentDetail;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoicesettlement.model.PaymentBalance;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.mapper.PaymentRetryConfigMapper;
import com.subskribe.billy.payment.model.AccountPaymentManagementLink;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.model.PaymentBankAccount;
import com.subskribe.billy.payment.model.PaymentConfiguration;
import com.subskribe.billy.payment.model.PaymentRetryConfig;
import com.subskribe.billy.payment.model.PaymentRetryConfigInput;
import com.subskribe.billy.payment.model.PaymentRetryConfigOutput;
import com.subskribe.billy.payment.services.AccountPaymentManagementLinkService;
import com.subskribe.billy.payment.services.PaymentBankAccountService;
import com.subskribe.billy.payment.services.PaymentConfigurationService;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.payment.services.PaymentOrchestrator;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.payment.stripe.service.StripeSelfServeService;
import com.subskribe.billy.payment.stripe.webhook.StripeWebhookEventProcessor;
import com.subskribe.billy.payment.task.PaymentRetryQueuedTaskProcessor.PaymentRetryResult;
import com.subskribe.billy.resources.json.payment.PaymentBalanceJson;
import com.subskribe.billy.resources.json.payment.PaymentBalanceJsonMapper;
import com.subskribe.billy.resources.json.payment.PaymentBankAccountJson;
import com.subskribe.billy.resources.json.payment.PaymentBankAccountJsonMapper;
import com.subskribe.billy.resources.json.payment.PaymentJson;
import com.subskribe.billy.resources.json.payment.PaymentJsonMapper;
import com.subskribe.billy.resources.json.payment.VoidPaymentJson;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.resources.shared.PaginationResponseJson;
import com.subskribe.billy.shared.SubskribeUrlGenerator;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.webhook.ImmutableIncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhookTaskDispatcher;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.validation.Validator;
import io.dropwizard.auth.Auth;
import io.dropwizard.jersey.errors.ErrorMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.time.Clock;
import java.time.Instant;
import java.util.ConcurrentModificationException;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;
import org.mapstruct.factory.Mappers;

@Path("/payments")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class PaymentResource {

    private static final String BANK_ACCOUNT_PATH = "/bank-account";

    @Context
    private ContainerRequestContext requestContext;

    private final PaymentGetService paymentGetService;
    private final InvoiceSettlementService invoiceSettlementService;
    private final PaymentJsonMapper paymentMapper;
    private final PaymentBalanceJsonMapper paymentBalanceJsonMapper;
    private final PaymentRetryConfigMapper paymentRetryConfigMapper;
    private final PaymentConfigurationService paymentConfigurationService;
    private final AccountPaymentManagementLinkService accountPaymentManagementLinkService;
    private final BillyConfiguration billyConfiguration;
    private final PaymentBankAccountService paymentBankAccountService;
    private final PaymentBankAccountJsonMapper paymentBankAccountJsonMapper;
    private final StripeSelfServeService stripeSelfServeService;
    private final IncomingWebhookTaskDispatcher incomingWebhookTaskDispatcher;
    private final AdminOperationsService adminOperationsService;
    private final PaymentIntegrationGetService paymentIntegrationGetService;
    private final PaymentOrchestrator paymentOrchestrator;
    private final Clock clock;

    @Inject
    public PaymentResource(
        PaymentGetService paymentGetService,
        InvoiceSettlementService invoiceSettlementService,
        PaymentConfigurationService paymentConfigurationService,
        AccountPaymentManagementLinkService accountPaymentManagementLinkService,
        BillyConfiguration billyConfiguration,
        PaymentBankAccountService paymentBankAccountService,
        StripeSelfServeService stripeSelfServeService,
        IncomingWebhookTaskDispatcher incomingWebhookTaskDispatcher,
        AdminOperationsService adminOperationsService,
        PaymentIntegrationGetService paymentIntegrationGetService,
        PaymentOrchestrator paymentOrchestrator,
        Clock clock
    ) {
        this.paymentGetService = paymentGetService;
        this.invoiceSettlementService = invoiceSettlementService;
        this.paymentConfigurationService = paymentConfigurationService;
        this.accountPaymentManagementLinkService = accountPaymentManagementLinkService;
        this.billyConfiguration = billyConfiguration;
        this.paymentBankAccountService = paymentBankAccountService;
        this.stripeSelfServeService = stripeSelfServeService;
        this.incomingWebhookTaskDispatcher = incomingWebhookTaskDispatcher;
        this.adminOperationsService = adminOperationsService;
        this.paymentIntegrationGetService = paymentIntegrationGetService;
        this.paymentOrchestrator = paymentOrchestrator;
        this.clock = clock;
        paymentMapper = Mappers.getMapper(PaymentJsonMapper.class);
        paymentBalanceJsonMapper = Mappers.getMapper(PaymentBalanceJsonMapper.class);
        paymentBankAccountJsonMapper = Mappers.getMapper(PaymentBankAccountJsonMapper.class);
        paymentRetryConfigMapper = Mappers.getMapper(PaymentRetryConfigMapper.class);
    }

    // hack to get response documented
    private static class PaymentJsonPaginationResponse extends PaginationResponseJson<Payment, PaymentJson> {

        public PaymentJsonPaginationResponse(
            List<Payment> resource,
            int limit,
            Function<List<Payment>, List<PaymentJson>> toJson,
            Function<Payment, UUID> getId
        ) {
            super(resource, limit, toJson, getId);
        }
    }

    @GET
    @ApiOperation(
        value = "Get all payments",
        notes = "Returns all payments for you tenant. The results are paginated. To fetch all take the cursor " +
        "returned from a call and pass it to a subsequent call.",
        response = PaymentJsonPaginationResponse.class,
        tags = { "Payments" }
    )
    @AllowAllRoles
    public Response getPayments(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("cursor") @ApiParam(name = "cursor", value = "cursor returned from previous call") UUID cursor,
        @QueryParam("limit") @ApiParam(name = "limit", value = "number of results per page") int limit
    ) {
        var paginationQueryParams = new PaginationQueryParams(cursor, limit);
        var payments = paymentGetService.getPayments(paginationQueryParams);
        var paginationJson = new PaginationResponseJson<>(payments, paginationQueryParams.getLimit(), paymentMapper::paymentsToJson, Payment::getId);

        return Response.ok(paginationJson).build();
    }

    @GET
    @Path("/{id}")
    @ApiOperation(
        value = "Get payment details",
        notes = "Gets the details of the specified payment",
        response = PaymentJson.class,
        tags = { "Payments" }
    )
    @AllowAllRoles
    public Response getPayment(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @NotNull @ApiParam(value = "id of the payment", required = true) String paymentId
    ) {
        var payment = paymentGetService.getPaymentByPaymentId(paymentId);

        var paymentDetailsJson = paymentMapper.paymentToJson(payment);
        return Response.ok(paymentDetailsJson).build();
    }

    @GET
    @Path("/{id}/canDelete")
    @ApiOperation(
        value = "Check if the payment object can be deleted",
        notes = "Payment can be deleted if it has been voided and does not have journal entries associated",
        response = PaymentDeletableResponse.class,
        tags = { "Payments" }
    )
    @AllowAllRoles
    public Response canDeletePayment(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @NotNull @ApiParam(value = "id of the payment", required = true) String paymentId
    ) {
        PaymentDeletableResponse canDeletePayment = adminOperationsService.canDeletePayment(paymentId);
        return Response.ok(canDeletePayment).build();
    }

    @DELETE
    @Path("/{id}")
    @ApiOperation(
        value = "Delete payment by payment id",
        notes = "Deletes the specified payment if the payment has been voided. This operation also deletes any applications of this payment",
        response = PaymentJson.class,
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response deletePayment(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @NotNull @ApiParam(value = "Payment bank account id") String paymentId
    ) {
        Payment payment = adminOperationsService.deleteVoidedPayment(paymentId);
        return Response.ok(paymentMapper.paymentToJson(payment)).build();
    }

    @GET
    @Path("/{id}/balance")
    @ApiOperation(value = "Get payment balance", notes = "Gets the balance of a payment", response = PaymentBalanceJson.class, tags = { "Payments" })
    @AllowAllRoles
    public Response getPaymentBalance(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @NotNull @ApiParam(value = "id of the payment", required = true) String paymentId
    ) {
        PaymentBalance paymentBalance = invoiceSettlementService.getPaymentBalance(paymentId);

        var json = paymentBalanceJsonMapper.paymentBalanceToJson(paymentBalance);
        return Response.ok(json).build();
    }

    @PUT
    @Path("/{id}/void")
    @ApiOperation(
        value = "Voids a payment",
        notes = "Voids the specified payment per the specified parameters",
        response = PaymentJson.class,
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response voidPayment(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @NotNull String paymentId,
        @NotNull VoidPaymentJson voidPaymentJson
    ) {
        try {
            Invoice.Number invoiceNumber = new Invoice.Number(voidPaymentJson.getInvoiceNumber());
            invoiceSettlementService.voidPayment(
                paymentId,
                invoiceNumber,
                Instant.ofEpochSecond(voidPaymentJson.getVoidDate()),
                voidPaymentJson.getInvoiceBalance(),
                voidPaymentJson.getNote()
            );
            return Response.ok().build();
        } catch (ConcurrentModificationException ex) {
            Response.Status status = Response.Status.CONFLICT;
            return Response.status(status).entity(new ErrorMessage(status.getStatusCode(), ex.getMessage())).build();
        }
    }

    @GET
    @Path("/account/{id}")
    @ApiOperation(
        value = "Get payments",
        notes = "Returns the payments for the specified account",
        response = PaymentJson.class,
        responseContainer = "List",
        tags = { "Payments" }
    )
    @AllowAllRoles
    public Response getAccountPayment(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @NotNull @ApiParam(value = "id of the account", required = true) String id
    ) {
        var payments = paymentGetService.getPaymentsByAccountId(id);
        var paymentDetailsJson = paymentMapper.paymentsToJson(payments);
        return Response.ok(paymentDetailsJson).build();
    }

    @AllowUnauthenticated(cascade = true)
    @POST
    @Path("/webhook")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Webhook callback for Stripe integration",
        notes = "This is the webhook which Stripe calls back to",
        hidden = true,
        tags = { "Payments" }
    )
    // Note: Make sure to always return a 200 in less than 5 seconds (otherwise Stripe may disable the configured webhook)
    public Response paymentWebhook(@ApiParam(value = "stripe event json", required = true) String stripeJsonEvent) {
        IncomingWebhook incomingWebhook = ImmutableIncomingWebhook.builder()
            .webhookType(StripeWebhookEventProcessor.WEBHOOK_TYPE)
            .payload(stripeJsonEvent)
            .receivedOn(clock.millis())
            .headers(requestContext.getHeaders())
            .build();
        incomingWebhookTaskDispatcher.dispatch(incomingWebhook);
        return Response.ok().build();
    }

    @GET
    @Path("/configuration")
    @ApiOperation(
        value = "Get payment configuration",
        notes = "Returns the payment configuration for your tenant",
        response = PaymentConfiguration.class,
        tags = { "Payments" }
    )
    @AllowAllRoles
    public Response getPaymentConfiguration(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        PaymentConfiguration paymentConfiguration = paymentConfigurationService.getTenantPaymentConfiguration();
        return Response.ok(paymentConfiguration).build();
    }

    // TODO: Accept PaymentConfiguration object as parameter instead?
    @POST
    @Path("/configuration")
    @ApiOperation(
        value = "Update payment configuration",
        notes = "Updates the payment configuration for your tenant.",
        response = PaymentConfiguration.class,
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN })
    public Response updatePaymentConfiguration(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @ApiParam(
            value = "Payment types to set. Options can be one or more of the allowable values",
            allowableValues = "ACH, CARD, CHECK, WIRE, EXTERNAL"
        ) Set<PaymentType> paymentTypes
    ) {
        PaymentConfiguration paymentConfiguration = new PaymentConfiguration(paymentTypes);
        PaymentConfiguration savedPaymentConfiguration = paymentConfigurationService.updatePaymentConfiguration(paymentConfiguration);
        return Response.ok(savedPaymentConfiguration).build();
    }

    @GET
    @Path("/account-payment/{id}")
    @ApiOperation(
        value = "Get account payment management link",
        notes = "Returns a payment management link for an account",
        response = String.class,
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response getAccountPaymentManagementLink(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @NotNull String accountId
    ) {
        Validator.validateStringNotBlank(accountId, "Account ID cannot be blank.");
        Optional<AccountPaymentManagementLink> paymentLink = accountPaymentManagementLinkService.getOrCreateAccountPaymentManagementLink(accountId);
        if (paymentLink.isEmpty()) {
            return Response.status(BAD_REQUEST).build();
        }
        String paymentLinkId = paymentLink.get().getLinkId();
        String accountPaymentManagementUrl = SubskribeUrlGenerator.getUiAccountPaymentManagementUrl(billyConfiguration.getSiteUrl(), paymentLinkId);
        return Response.ok(accountPaymentManagementUrl).build();
    }

    @POST
    @Path(BANK_ACCOUNT_PATH)
    @ApiOperation(
        value = "Adds a new payment bank account",
        notes = "The bank account would be mapped to a cash and an expense ledger account",
        response = PaymentBankAccountJson.class,
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    public Response upsertBankAccount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @Context UriInfo uriInfo,
        @NotNull @ApiParam(value = "Payment bank account to be added") @Valid PaymentBankAccountJson paymentBankAccountJson
    ) {
        PaymentBankAccount paymentBankAccountRequest = paymentBankAccountJsonMapper.fromJson(paymentBankAccountJson);
        PaymentBankAccount paymentBankAccountCreated = paymentBankAccountService.upsert(paymentBankAccountRequest);
        PaymentBankAccountJson paymentBankAccountCreatedResponse = paymentBankAccountJsonMapper.toJson(paymentBankAccountCreated);
        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        return Response.created(builder.path(paymentBankAccountCreatedResponse.paymentBankAccountId()).build())
            .entity(paymentBankAccountCreatedResponse)
            .build();
    }

    @GET
    @Path(BANK_ACCOUNT_PATH + "/{id}")
    @ApiOperation(value = "Get a payment bank account by id", response = PaymentBankAccountJson.class, tags = { "Payments" })
    @AllowAllRoles
    public Response getBankAccount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @NotNull @ApiParam(value = "Payment bank account id") String paymentBankAccountId
    ) {
        var paymentBankAccount = paymentBankAccountService.getByPaymentBankAccountId(paymentBankAccountId);
        return Response.ok(paymentBankAccountJsonMapper.toJson(paymentBankAccount)).build();
    }

    @DELETE
    @Path(BANK_ACCOUNT_PATH + "/{id}")
    @ApiOperation(value = "Get a payment bank account by id", response = PaymentBankAccountJson.class, tags = { "Payments" })
    @AllowAllRoles
    public Response deleteBankAccount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @NotNull @ApiParam(value = "Payment bank account id") String paymentBankAccountId
    ) {
        var paymentBankAccount = paymentBankAccountService.delete(paymentBankAccountId);
        return Response.ok(paymentBankAccountJsonMapper.toJson(paymentBankAccount)).build();
    }

    @GET
    @Path(BANK_ACCOUNT_PATH + "/accounts/ledger")
    @ApiOperation(
        value = "Gets cash and expense ledger accounts for bank account creation",
        notes = "The bank account would be mapped to a cash and an expense ledger account from this list",
        response = LedgerAccount[].class,
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    public Response getLedgerAccountsForPaymentBankAccount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("id") String paymentBankAccountId
    ) {
        var ledgerAccounts = paymentBankAccountService.getLedgerAccountsForPaymentBankAccount(paymentBankAccountId);
        return Response.ok(ledgerAccounts).build();
    }

    @POST
    @Path("/processPaymentForInvoice/{invoiceNumber}")
    @ApiOperation(
        value = "Processes one time payment for an invoice if there is an automatic payment set up for the account",
        notes = "The automatic payment is processed via Stripe for this invoice",
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    public Response processPaymentForInvoice(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("invoiceNumber") String invoiceNumber
    ) {
        Optional<PaymentDetail> paymentDetail = stripeSelfServeService.processPaymentForInvoice(invoiceNumber);
        return Response.ok(paymentDetail).build();
    }

    @POST
    @Path("/retry/config")
    @ApiOperation(
        value = "Creates or updates a payment retry configuration",
        notes = "Creates or updates a payment retry configuration for the tenant",
        response = PaymentRetryConfigOutput.class,
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    public Response upsertPaymentRetryConfig(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(value = "Payment retry configuration to be created or updated") PaymentRetryConfigInput input
    ) {
        var retryConfig = paymentRetryConfigMapper.inputToModel(input);
        PaymentRetryConfig upsertedPaymentRetryConfig = paymentConfigurationService.upsertPaymentRetryConfig(retryConfig);
        return Response.ok(paymentRetryConfigMapper.modelToOutput(upsertedPaymentRetryConfig)).build();
    }

    @GET
    @Path("/retry/config")
    @ApiOperation(
        value = "Gets a payment retry configuration",
        notes = "Gets the latest payment retry configuration for the tenant, or by the grouping id if specified",
        response = PaymentRetryConfigOutput.class,
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    public Response getPaymentRetryConfig(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("groupingId") @ApiParam(value = "groupingId of the payment retry configuration") String groupingId // TODO : this API contract is bound to change once we have ME PG as it will expect the mapping id, right now it is used to fetch the latest configuration at the tenant level
    ) {
        var paymentRetryConfig = paymentConfigurationService.getPaymentRetryConfig(groupingId);
        return Response.ok(paymentRetryConfigMapper.modelToOutput(paymentRetryConfig)).build();
    }

    @DELETE
    @Path("/retry/config/{id}")
    @ApiOperation(
        value = "Deletes a payment retry configuration",
        notes = "Deletes a payment retry configuration for the tenant",
        response = PaymentRetryConfigOutput.class,
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    public Response deletePaymentRetryConfig(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("id") @NotNull @ApiParam(value = "ID of the payment retry configuration to be deleted", required = true) String groupingId
    ) {
        var deletedConfig = paymentConfigurationService.deletePaymentRetryConfig(groupingId);
        return Response.ok(paymentRetryConfigMapper.modelToOutput(deletedConfig)).build();
    }

    @POST
    @Path("{paymentId}/retry/manual")
    @ApiOperation(
        value = "Manually retries a payment",
        notes = "Manually retries a payment based on the provided payment ID and last attempt ID",
        response = PaymentRetryResult.class,
        tags = { "Payments" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    public Response manualPaymentRetry(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("paymentId") @NotBlank @ApiParam(value = "Payment id") String paymentId,
        @QueryParam("lastPaymentAttemptId") @NotBlank @ApiParam(value = "Last payment attempt id") String lastPaymentAttemptId
    ) {
        PaymentRetryResult paymentRetryResult = paymentOrchestrator.attemptManualRetry(paymentId, lastPaymentAttemptId);
        return Response.ok(paymentRetryResult).build();
    }

    @GET
    @Path("/payment-gateways")
    @ApiOperation(
        value = "Gets the list of payment integrations",
        notes = "Gets the list of all active and completed payment integrations for the tenant",
        tags = { "Payments" }
    )
    @AllowAllRoles
    public Response getPaymentIntegrations(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        List<PaymentStripeConnectIntegration> paymentStripeConnectIntegrations = paymentIntegrationGetService.getCompletedStripeConnectIntegrations();
        return Response.ok(paymentStripeConnectIntegrations).build();
    }
}
