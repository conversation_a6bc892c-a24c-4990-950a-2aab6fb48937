package com.subskribe.billy.resources;

import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.invoice.automated.model.AutomatedInvoiceRule;
import com.subskribe.billy.invoice.automated.service.AutomatedInvoiceRuleGetService;
import com.subskribe.billy.invoice.automated.service.AutomatedInvoiceRuleService;
import com.subskribe.billy.resources.json.invoice.AutomatedInvoiceRuleRequestJson;
import com.subskribe.billy.user.model.Role;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import javax.ws.rs.core.UriInfo;

@Path("/automatedInvoiceRules")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class AutomatedInvoiceRuleResource {

    private final AutomatedInvoiceRuleService automatedInvoiceRuleService;

    private final AutomatedInvoiceRuleGetService automatedInvoiceRuleGetService;

    @Inject
    public AutomatedInvoiceRuleResource(
        AutomatedInvoiceRuleService automatedInvoiceRuleService,
        AutomatedInvoiceRuleGetService automatedInvoiceRuleGetService
    ) {
        this.automatedInvoiceRuleService = automatedInvoiceRuleService;
        this.automatedInvoiceRuleGetService = automatedInvoiceRuleGetService;
    }

    @GET
    @Path("/internal/{id}")
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    @Deprecated
    public Response getAutomatedInvoiceRuleByInternalId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("id") @ApiParam(
            value = "Internal id of the automated invoice rule",
            required = true
        ) String automatedInvoiceRuleInternalId
    ) {
        AutomatedInvoiceRule automatedInvoiceRule = automatedInvoiceRuleGetService.getAutomatedInvoiceRule(automatedInvoiceRuleInternalId);
        return Response.ok(automatedInvoiceRule).build();
    }

    @GET
    @Path("/{id}")
    @ApiOperation(
        value = "Get automated invoice rule details",
        notes = "Returns the details of the specified automated invoice rule",
        tags = { "Billing" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response getAutomatedInvoiceRule(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("id") @ApiParam(value = "Id of the automated invoice rule", required = true) String automatedInvoiceRuleId
    ) {
        AutomatedInvoiceRule automatedInvoiceRule = automatedInvoiceRuleGetService.getAutomatedInvoiceRuleById(automatedInvoiceRuleId);
        return Response.ok(automatedInvoiceRule).build();
    }

    @GET
    @ApiOperation(
        value = "Get automated invoice rules for a tenant",
        notes = "Returns all the configured automated invoice rules for the tenant",
        tags = { "Billing" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response getAutomatedInvoiceRules(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        List<AutomatedInvoiceRule> automatedInvoiceRules = automatedInvoiceRuleGetService.getAutomatedInvoiceRulesForTenant();
        return Response.ok(automatedInvoiceRules).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Create an automated invoice rule",
        notes = "Creates an automated invoice rule with the specified parameters",
        response = AutomatedInvoiceRule.class,
        tags = { "Billing" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response addAutomatedInvoiceRule(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @Valid @ApiParam(value = "automated invoice rule", required = true) AutomatedInvoiceRuleRequestJson automatedInvoiceRuleRequestJson,
        @Context UriInfo uriInfo
    ) {
        AutomatedInvoiceRule createdAutomatedInvoiceRule = automatedInvoiceRuleService.addAutomatedInvoiceRule(automatedInvoiceRuleRequestJson);

        UriBuilder builder = uriInfo.getAbsolutePathBuilder();
        builder.path(createdAutomatedInvoiceRule.getAutomatedInvoiceRuleId());
        return Response.created(builder.build()).build();
    }

    @PUT
    @Path("/internal/{id}")
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    @Deprecated
    public Response updateAutomatedInvoiceRuleUsingInternalId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("id") @ApiParam(value = "id of the automated invoice rule", required = true) String automatedInvoiceRuleInternalId,
        @NotNull @Valid @ApiParam(value = "automated invoice rule", required = true) AutomatedInvoiceRuleRequestJson automatedInvoiceRuleUpdateJson
    ) {
        automatedInvoiceRuleService.updateAutomatedInvoiceRule(automatedInvoiceRuleInternalId, automatedInvoiceRuleUpdateJson);
        return Response.ok().build();
    }

    @PUT
    @Path("/{id}")
    @ApiOperation(
        value = "Update automated invoice rule details",
        notes = "Updates the details of the specified automated invoice rule",
        tags = { "Billing" }
    )
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    public Response updateAutomatedInvoiceRule(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @NotNull @PathParam("id") @ApiParam(value = "id of the automated invoice rule", required = true) String automatedInvoiceRuleId,
        @NotNull @Valid @ApiParam(value = "automated invoice rule", required = true) AutomatedInvoiceRuleRequestJson automatedInvoiceRuleUpdateJson
    ) {
        automatedInvoiceRuleService.updateAutomatedInvoiceRuleUsingAutomatedInvoiceRuleId(automatedInvoiceRuleId, automatedInvoiceRuleUpdateJson);
        return Response.ok().build();
    }
}
