package com.subskribe.billy.resources;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.crm.CrmIntegrationService;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.esign.services.EsignService;
import com.subskribe.billy.hubspot.model.HubSpotCompanyCard;
import com.subskribe.billy.hubspot.model.HubSpotCustomObjectSchema;
import com.subskribe.billy.hubspot.model.HubSpotIntegration;
import com.subskribe.billy.hubspot.model.HubSpotSetupMessage;
import com.subskribe.billy.hubspot.service.HubSpotGetService;
import com.subskribe.billy.hubspot.service.HubSpotInstallService;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.validation.Validator;
import io.dropwizard.auth.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.IOException;
import java.util.List;
import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.ForbiddenException;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

@Path("/hubspot")
@Produces(MediaType.APPLICATION_JSON)
@Api
public class HubSpotResource {

    private static final Logger LOGGER = LoggerFactory.getLogger(HubSpotResource.class);
    private static final String GET_METHOD = "GET";

    private final HubSpotIntegrationService hubSpotIntegrationService;

    private final HubSpotService hubSpotService;

    private final HubSpotGetService hubSpotGetService;

    private final OrderGetService orderGetService;

    private final CrmIntegrationService crmIntegrationService;

    private final HubSpotInstallService hubSpotInstallService;

    private final CrmService crmService;

    private final BillyConfiguration billyConfiguration;

    private final SubscriptionGetService subscriptionGetService;

    private final EsignService esignService;

    @Inject
    public HubSpotResource(
        HubSpotIntegrationService hubSpotIntegrationService,
        HubSpotService hubSpotService,
        HubSpotGetService hubSpotGetService,
        OrderGetService orderGetService,
        CrmIntegrationService crmIntegrationService,
        HubSpotInstallService hubSpotInstallService,
        CrmService crmService,
        BillyConfiguration billyConfiguration,
        SubscriptionGetService subscriptionGetService,
        EsignService esignService
    ) {
        this.hubSpotIntegrationService = hubSpotIntegrationService;
        this.hubSpotService = hubSpotService;
        this.hubSpotGetService = hubSpotGetService;
        this.orderGetService = orderGetService;
        this.crmIntegrationService = crmIntegrationService;
        this.hubSpotInstallService = hubSpotInstallService;
        this.crmService = crmService;
        this.billyConfiguration = billyConfiguration;
        this.subscriptionGetService = subscriptionGetService;
        this.esignService = esignService;
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(
        value = "Initiate a HubSpot integration",
        notes = "Initiates an integration with HubSpot. On success redirect URL is returned.",
        response = String.class,
        tags = { "Integrations" }
    )
    public Response initiateIntegration(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @ApiParam(name = "email", value = "admin email") String email
    ) {
        String redirectUrl = hubSpotIntegrationService.initiateIntegration(email);
        crmIntegrationService.throwIfTenantAlreadyHasACrmIntegration();
        return Response.status(Response.Status.SEE_OTHER).entity(redirectUrl).build();
    }

    @DELETE
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Delete HubSpot Integration", notes = "Deletes your integration with HubSpot.", tags = { "Integrations" })
    public Response deleteIntegration(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        hubSpotIntegrationService.deleteIntegration();
        return Response.ok().build();
    }

    @GET
    @AllowUnauthenticated(cascade = true)
    @ApiOperation(
        value = "Handle HubSpot authorization code",
        notes = "Endpoint to handle and process a HubSpot authorization code",
        tags = { "Integrations" }
    )
    public Response authorizationCodeCallback(
        @NotNull @QueryParam("code") @ApiParam(name = "code", value = "authorization code", required = true) String authorizationCode,
        @NotNull @QueryParam("state") @ApiParam(name = "state", value = "HubSpot integration id", required = true) String integrationId,
        @NotNull @QueryParam("redirect_uri") @ApiParam(
            name = "redirect_uri",
            value = "uri to redirect to HubSpot",
            required = true
        ) String redirectUri
    ) throws IOException {
        hubSpotIntegrationService.handleAuthorizationCodeCallback(authorizationCode, integrationId, redirectUri);
        return Response.ok().build();
    }

    // todo - for development, manual testing of contact sync feature, delete after completion
    @GET
    @Path("/test/contactsByAccountCrmId/{accountCrmId}")
    @ApiOperation(value = "Pull HubSpot orders", hidden = true, notes = "Pulls and creates HubSpot orders", tags = { "Integrations" })
    @AllowAllRoles
    public Response getContactsByAccountCrmId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountCrmId") String accountCrmId
    ) throws Exception {
        var accountContacts = hubSpotGetService.getContactsByHubSpotCompanyId(accountCrmId);
        return Response.ok(accountContacts).build();
    }

    @GET
    @Path("/cards/company")
    @AllowUnauthenticated(cascade = true)
    @ApiOperation(value = "Get HubSpot card for a company", hidden = true, tags = { "Integrations" })
    public Response getCompanyCard(
        @Context UriInfo uriInfo,
        @HeaderParam("X-HubSpot-Signature") String hubspotSignature,
        @HeaderParam("X-HubSpot-Signature-Version") String hubspotSignatureVersion,
        @QueryParam("portalId") String portalId,
        @QueryParam("associatedObjectId") String objectId
    ) {
        HubSpotCompanyCard companyCard = hubSpotService.getCompanyCard(objectId, hubspotSignature, portalId, GET_METHOD, uriInfo);
        return Response.ok(companyCard).build();
    }

    @GET
    @Path("/cards/deal")
    @AllowUnauthenticated(cascade = true)
    @ApiOperation(value = "Get HubSpot card for a company", hidden = true, tags = { "Integrations" })
    public Response getDealCard(
        @Context UriInfo uriInfo,
        @HeaderParam("X-HubSpot-Signature") String hubspotSignature,
        @QueryParam("portalId") String portalId,
        @QueryParam("associatedObjectId") String objectId
    ) throws IOException {
        HubSpotCompanyCard dealCard = hubSpotService.getDealCard(objectId, hubspotSignature, portalId, GET_METHOD, uriInfo);
        return Response.ok(dealCard).build();
    }

    @POST
    @Path("/test/completed")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Test API for E2E testing", hidden = true, tags = { "Integrations" })
    @AllowAllRoles
    public Response markTestIntegrationAsCompleted(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @QueryParam("integrationId") String integrationId,
        @QueryParam("refreshToken") String refreshToken
    ) throws IOException {
        throwIfNotLocal();
        HubSpotIntegration integration = hubSpotIntegrationService.markIntegrationCompleted(integrationId, refreshToken);
        return Response.ok(integration).build();
    }

    @POST
    @Path("/test/updateHubSpotForOrder/{orderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Test API for E2E testing", hidden = true, tags = { "Integrations" })
    @AllowAllRoles
    public Response updateHubSpotForOrder(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @PathParam("orderId") String orderId)
        throws IOException {
        throwIfNotLocal();
        hubSpotService.syncOrderToHubSpot(orderId);
        return Response.ok().build();
    }

    @POST
    @Path("/setup")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN, Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    @ApiOperation(value = "Add custom properties to HubSpot", notes = "Add custom properties to HubSpot", tags = { "Integrations" })
    public Response setupHubSpot(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        var response = hubSpotInstallService.setupHubSpotCustomProperties();
        return Response.ok(response).build();
    }

    @POST
    @Path("/setup/verify")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN, Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    @ApiOperation(value = "Verify HubSpot setup", notes = "Verify custom objects and properties", tags = { "Integrations" })
    public Response verifySetup(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal) {
        List<HubSpotSetupMessage> response = hubSpotInstallService.verifyHubSpotSetup();
        return Response.ok(response).build();
    }

    private void throwIfNotLocal() {
        if (billyConfiguration.isLocalOrCi()) {
            return;
        }

        throw new ForbiddenException("Not allowed.");
    }

    @POST
    @Path("/esign/{orderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Sync esign details for order id", tags = { "Integrations" })
    public Response syncEsignDetailsForOrderToHubSpot(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("orderId") String orderId
    ) {
        Validator.validateStringNotBlank(orderId, "order id cannot be blank");
        esignService.syncEsignToHubSpot(orderId);
        return Response.ok().build();
    }

    @POST
    @Path("/sync/order/{orderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Sync order to Hubspot")
    public Response syncOrder(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @PathParam("orderId") String orderId) {
        Validator.validateStringNotBlank(orderId, "order id cannot be blank");
        Order order = orderGetService.getOrderByOrderId(orderId);
        crmService.pushOrderSync(order);
        return Response.ok().build();
    }

    @POST
    @Path("/sync/account/{accountCrmId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Sync account to Hubspot")
    public Response syncHubspotAccount(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("accountCrmId") String accountCrmId
    ) {
        Validator.validateStringNotBlank(accountCrmId, "account CRM id cannot be blank");
        crmService.importAccount(accountCrmId);
        return Response.ok().build();
    }

    @POST
    @Path("/sync/subscription/{subscriptionId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Sync subscription to Hubspot")
    public Response syncHubspotSubscription(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("subscriptionId") String subscriptionId
    ) {
        crmService.pushSubscriptionSync(subscriptionId);
        return Response.ok().build();
    }

    @POST
    @Path("/sync/subscription/{subscriptionId}/status")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Sync subscription status to Hubspot")
    public Response syncHubspotSubscriptionStatus(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("subscriptionId") String subscriptionId
    ) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId cannot be blank");
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        hubSpotService.syncHubSpotSubscriptionStatus(subscription.getSubscriptionId(), subscription.getState().name());
        return Response.ok().build();
    }

    @DELETE
    @Path("/sync/subscription/{subscriptionId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Delete subscription on Hubspot")
    public Response deleteHubSpotSubscription(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("subscriptionId") String subscriptionId
    ) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId cannot be blank");
        hubSpotService.deleteHubSpotSubscription(subscriptionId);
        return Response.ok().build();
    }

    @GET
    @Path("/schemas")
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Get HubSpot object schemas", hidden = true, tags = { "Integrations" })
    public Response getObjectSchemas(@Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal, @Context UriInfo uriInfo) {
        List<HubSpotCustomObjectSchema> schemas = hubSpotService.getCustomObjectSchemaList();
        return Response.ok(schemas).build();
    }

    @GET
    @Path("/{objectType}")
    @Consumes(MediaType.APPLICATION_JSON)
    @AllowRoles({ Role.ADMIN })
    @ApiOperation(value = "Get HubSpot object records", hidden = true, tags = { "Integrations" })
    public Response getObjectRecords(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("objectType") String objectType,
        @QueryParam("objectIds") String objectIdsParam,
        @Context UriInfo uriInfo
    ) {
        Validator.validateStringNotBlank(objectType, "objectType cannot be blank");
        List<String> objectIds = List.of(objectIdsParam.split(","));
        Validator.validateCollectionNotEmpty(objectIds, "objectIds cannot be empty");
        return Response.ok(hubSpotService.getObjectRecords(objectType, objectIds)).build();
    }

    @GET
    @Path("/test/hubSpotOrdersByDealId/{dealId}")
    @ApiOperation(value = "Test API for E2E testing", hidden = true, tags = { "Integrations" })
    @AllowAllRoles
    public Response getHubSpotOrdersByDealId(
        @Auth @ApiParam(hidden = true) BillyAuthPrincipal billyAuthPrincipal,
        @PathParam("dealId") String dealId
    ) {
        throwIfNotLocal();
        Validator.validateStringNotBlank(dealId, "dealId cannot be blank");
        var orders = hubSpotGetService.getHubSpotOrdersByDealId(dealId);
        return Response.ok(orders).build();
    }
}
