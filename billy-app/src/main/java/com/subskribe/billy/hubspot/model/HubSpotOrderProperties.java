package com.subskribe.billy.hubspot.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import java.util.List;

public class HubSpotOrderProperties {

    public static final String API_NAME = "subskribe_orders";
    public static final String NAME_API_NAME = "name";
    public static final String ORDER_STATUS_API_NAME = "order_status";
    public static final String ORDER_EXECUTION_DATE_API_NAME = "order_execution_date";
    public static final String ORDER_TOTAL_API_NAME = "order_total";
    public static final String PRIMARY_ORDER_ID_API_NAME = "primary_order_id";
    public static final String PRIMARY_ORDER_LINK_API_NAME = "primary_order_link";
    public static final String SUBSCRIPTION_ID_API_NAME = "subscription_id";
    public static final String SUBSCRIPTION_LINK_API_NAME = "subscription_link";
    public static final String OPPORTUNITY_LINK_API_NAME = "opportunity_link";
    public static final String ENTRY_ARR_API_NAME = "entry_arr";
    public static final String EXIT_ARR_API_NAME = "exit_arr";
    public static final String AVERAGE_ARR_API_NAME = "average_arr";
    public static final String RECURRING_TOTAL_API_NAME = "recurring_total";
    public static final String NON_RECURRING_TOTAL_API_NAME = "non_recurring_total";
    public static final String TCV_API_NAME = "tcv";
    public static final String AVERAGE_ACV_API_NAME = "average_acv";
    public static final String CURRENCY_API_NAME = "currency";
    public static final String SUBSKRIBE_ORDER_AUTO_RENEW_API_NAME = "subskribe_order_auto_renew";
    public static final String SUBSKRIBE_ORDER_DISCOUNT_TOTAL_API_NAME = "subskribe_order_discount_total";
    public static final String SUBSKRIBE_ORDER_DISCOUNT_PERCENT_API_NAME = "subskribe_order_discount_percent";
    public static final String SUBSKRIBE_PRIMARY_ORDER_API_NAME = "subskribe_primary_order";
    public static final List<String> PROPERTIES = List.of(
        NAME_API_NAME,
        ORDER_STATUS_API_NAME,
        ORDER_EXECUTION_DATE_API_NAME,
        ORDER_TOTAL_API_NAME,
        PRIMARY_ORDER_ID_API_NAME,
        PRIMARY_ORDER_LINK_API_NAME,
        SUBSCRIPTION_ID_API_NAME,
        SUBSCRIPTION_LINK_API_NAME,
        OPPORTUNITY_LINK_API_NAME,
        ENTRY_ARR_API_NAME,
        EXIT_ARR_API_NAME,
        AVERAGE_ARR_API_NAME,
        RECURRING_TOTAL_API_NAME,
        NON_RECURRING_TOTAL_API_NAME,
        TCV_API_NAME,
        AVERAGE_ACV_API_NAME,
        CURRENCY_API_NAME,
        SUBSKRIBE_ORDER_AUTO_RENEW_API_NAME,
        SUBSKRIBE_ORDER_DISCOUNT_TOTAL_API_NAME,
        SUBSKRIBE_ORDER_DISCOUNT_PERCENT_API_NAME,
        SUBSKRIBE_PRIMARY_ORDER_API_NAME
    );

    @JsonProperty(NAME_API_NAME)
    @SerializedName(NAME_API_NAME)
    private String name;

    @JsonProperty(ORDER_STATUS_API_NAME)
    @SerializedName(ORDER_STATUS_API_NAME)
    private String orderStatus;

    @JsonProperty(ORDER_EXECUTION_DATE_API_NAME)
    @SerializedName(ORDER_EXECUTION_DATE_API_NAME)
    private String orderExecutionDate;

    @JsonProperty(ORDER_TOTAL_API_NAME)
    @SerializedName(ORDER_TOTAL_API_NAME)
    private String orderTotal;

    @JsonProperty(PRIMARY_ORDER_ID_API_NAME)
    @SerializedName(PRIMARY_ORDER_ID_API_NAME)
    private String primaryOrderId;

    @JsonProperty(PRIMARY_ORDER_LINK_API_NAME)
    @SerializedName(PRIMARY_ORDER_LINK_API_NAME)
    private String primaryOrderLink;

    @JsonProperty(SUBSCRIPTION_ID_API_NAME)
    @SerializedName(SUBSCRIPTION_ID_API_NAME)
    private String subscriptionId;

    @JsonProperty(SUBSCRIPTION_LINK_API_NAME)
    @SerializedName(SUBSCRIPTION_LINK_API_NAME)
    private String subscriptionLink;

    @JsonProperty(OPPORTUNITY_LINK_API_NAME)
    @SerializedName(OPPORTUNITY_LINK_API_NAME)
    private String opportunityLink;

    @JsonProperty(ENTRY_ARR_API_NAME)
    @SerializedName(ENTRY_ARR_API_NAME)
    private String entryArr;

    @JsonProperty(EXIT_ARR_API_NAME)
    @SerializedName(EXIT_ARR_API_NAME)
    private String exitArr;

    @JsonProperty(AVERAGE_ARR_API_NAME)
    @SerializedName(AVERAGE_ARR_API_NAME)
    private String averageArr;

    @JsonProperty(RECURRING_TOTAL_API_NAME)
    @SerializedName(RECURRING_TOTAL_API_NAME)
    private String recurringTotal;

    @JsonProperty(NON_RECURRING_TOTAL_API_NAME)
    @SerializedName(NON_RECURRING_TOTAL_API_NAME)
    private String nonRecurringTotal;

    @JsonProperty(TCV_API_NAME)
    @SerializedName(TCV_API_NAME)
    private String tcv;

    @JsonProperty(AVERAGE_ACV_API_NAME)
    @SerializedName(AVERAGE_ACV_API_NAME)
    private String averageAcv;

    @JsonProperty(CURRENCY_API_NAME)
    @SerializedName(CURRENCY_API_NAME)
    private String currency;

    @JsonProperty(SUBSKRIBE_ORDER_AUTO_RENEW_API_NAME)
    @SerializedName(SUBSKRIBE_ORDER_AUTO_RENEW_API_NAME)
    private String subskribeOrderAutoRenew;

    @JsonProperty(SUBSKRIBE_ORDER_DISCOUNT_TOTAL_API_NAME)
    @SerializedName(SUBSKRIBE_ORDER_DISCOUNT_TOTAL_API_NAME)
    private String subskribeOrderDiscountTotal;

    @JsonProperty(SUBSKRIBE_ORDER_DISCOUNT_PERCENT_API_NAME)
    @SerializedName(SUBSKRIBE_ORDER_DISCOUNT_PERCENT_API_NAME)
    private String subskribeOrderDiscountPercent;

    @JsonProperty(SUBSKRIBE_PRIMARY_ORDER_API_NAME)
    @SerializedName(SUBSKRIBE_PRIMARY_ORDER_API_NAME)
    private String subskribePrimaryOrder;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderExecutionDate() {
        return orderExecutionDate;
    }

    public void setOrderExecutionDate(String orderExecutionDate) {
        this.orderExecutionDate = orderExecutionDate;
    }

    public String getOrderTotal() {
        return orderTotal;
    }

    public void setOrderTotal(String orderTotal) {
        this.orderTotal = orderTotal;
    }

    public String getPrimaryOrderId() {
        return primaryOrderId;
    }

    public void setPrimaryOrderId(String primaryOrderId) {
        this.primaryOrderId = primaryOrderId;
    }

    public String getPrimaryOrderLink() {
        return primaryOrderLink;
    }

    public void setPrimaryOrderLink(String primaryOrderLink) {
        this.primaryOrderLink = primaryOrderLink;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getSubscriptionLink() {
        return subscriptionLink;
    }

    public void setSubscriptionLink(String subscriptionLink) {
        this.subscriptionLink = subscriptionLink;
    }

    public String getOpportunityLink() {
        return opportunityLink;
    }

    public void setOpportunityLink(String opportunityLink) {
        this.opportunityLink = opportunityLink;
    }

    public String getEntryArr() {
        return entryArr;
    }

    public void setEntryArr(String entryArr) {
        this.entryArr = entryArr;
    }

    public String getExitArr() {
        return exitArr;
    }

    public void setExitArr(String exitArr) {
        this.exitArr = exitArr;
    }

    public String getAverageArr() {
        return averageArr;
    }

    public void setAverageArr(String averageArr) {
        this.averageArr = averageArr;
    }

    public String getRecurringTotal() {
        return recurringTotal;
    }

    public void setRecurringTotal(String recurringTotal) {
        this.recurringTotal = recurringTotal;
    }

    public String getNonRecurringTotal() {
        return nonRecurringTotal;
    }

    public void setNonRecurringTotal(String nonRecurringTotal) {
        this.nonRecurringTotal = nonRecurringTotal;
    }

    public String getTcv() {
        return tcv;
    }

    public void setTcv(String tcv) {
        this.tcv = tcv;
    }

    public String getAverageAcv() {
        return averageAcv;
    }

    public void setAverageAcv(String averageAcv) {
        this.averageAcv = averageAcv;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getSubskribeOrderAutoRenew() {
        return subskribeOrderAutoRenew;
    }

    public void setSubskribeOrderAutoRenew(String subskribeOrderAutoRenew) {
        this.subskribeOrderAutoRenew = subskribeOrderAutoRenew;
    }

    public String getSubskribeOrderDiscountTotal() {
        return subskribeOrderDiscountTotal;
    }

    public void setSubskribeOrderDiscountTotal(String subskribeOrderDiscountTotal) {
        this.subskribeOrderDiscountTotal = subskribeOrderDiscountTotal;
    }

    public String getSubskribeOrderDiscountPercent() {
        return subskribeOrderDiscountPercent;
    }

    public void setSubskribeOrderDiscountPercent(String subskribeOrderDiscountPercent) {
        this.subskribeOrderDiscountPercent = subskribeOrderDiscountPercent;
    }

    public String getSubskribePrimaryOrder() {
        return subskribePrimaryOrder;
    }

    public void setSubskribePrimaryOrder(String subskribePrimaryOrder) {
        this.subskribePrimaryOrder = subskribePrimaryOrder;
    }

    @Override
    public String toString() {
        return (
            "HubSpotOrderProperties{" +
            "name='" +
            name +
            '\'' +
            ", orderStatus='" +
            orderStatus +
            '\'' +
            ", orderExecutionDate='" +
            orderExecutionDate +
            '\'' +
            ", orderTotal='" +
            orderTotal +
            '\'' +
            ", primaryOrderId='" +
            primaryOrderId +
            '\'' +
            ", primaryOrderLink='" +
            primaryOrderLink +
            '\'' +
            ", subscriptionId='" +
            subscriptionId +
            '\'' +
            ", subscriptionLink='" +
            subscriptionLink +
            '\'' +
            ", opportunityLink='" +
            opportunityLink +
            '\'' +
            ", entryArr='" +
            entryArr +
            '\'' +
            ", exitArr='" +
            exitArr +
            '\'' +
            ", averageArr='" +
            averageArr +
            '\'' +
            ", recurringTotal='" +
            recurringTotal +
            '\'' +
            ", nonRecurringTotal='" +
            nonRecurringTotal +
            '\'' +
            ", tcv='" +
            tcv +
            '\'' +
            ", averageAcv='" +
            averageAcv +
            '\'' +
            ", currency='" +
            currency +
            '\'' +
            ", subskribeOrderAutoRenew='" +
            subskribeOrderAutoRenew +
            '\'' +
            ", subskribeOrderDiscountTotal='" +
            subskribeOrderDiscountTotal +
            '\'' +
            ", subskribeOrderDiscountPercent='" +
            subskribeOrderDiscountPercent +
            '\'' +
            ", subskribePrimaryOrder='" +
            subskribePrimaryOrder +
            '\'' +
            '}'
        );
    }
}
