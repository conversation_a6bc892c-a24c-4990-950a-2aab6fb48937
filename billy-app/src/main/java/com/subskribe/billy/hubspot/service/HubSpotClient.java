package com.subskribe.billy.hubspot.service;

import static com.subskribe.billy.hubspot.client.HubspotClientConstants.RETRY_POLICY;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.hubspot.client.HubspotApiClient;
import com.subskribe.billy.hubspot.client.HubspotClientConstants;
import com.subskribe.billy.hubspot.model.HubSpotAccountDetails;
import com.subskribe.billy.hubspot.model.HubSpotApiResponse;
import com.subskribe.billy.hubspot.model.HubSpotAssociatedIdsResponse;
import com.subskribe.billy.hubspot.model.HubSpotAssociation;
import com.subskribe.billy.hubspot.model.HubSpotBatchReadRequest;
import com.subskribe.billy.hubspot.model.HubSpotCompany;
import com.subskribe.billy.hubspot.model.HubSpotCompanyProperties;
import com.subskribe.billy.hubspot.model.HubSpotContact;
import com.subskribe.billy.hubspot.model.HubSpotContactProperties;
import com.subskribe.billy.hubspot.model.HubSpotCreateResponse;
import com.subskribe.billy.hubspot.model.HubSpotCustomObjectProperties;
import com.subskribe.billy.hubspot.model.HubSpotCustomObjectSchema;
import com.subskribe.billy.hubspot.model.HubSpotCustomProperties;
import com.subskribe.billy.hubspot.model.HubSpotCustomPropertyUpdate;
import com.subskribe.billy.hubspot.model.HubSpotDeal;
import com.subskribe.billy.hubspot.model.HubSpotDealProperties;
import com.subskribe.billy.hubspot.model.HubSpotIdWrapper;
import com.subskribe.billy.hubspot.model.HubSpotIntegration;
import com.subskribe.billy.hubspot.model.HubSpotNote;
import com.subskribe.billy.hubspot.model.HubSpotNoteProperties;
import com.subskribe.billy.hubspot.model.HubSpotObject;
import com.subskribe.billy.hubspot.model.HubSpotObjectAssociation;
import com.subskribe.billy.hubspot.model.HubSpotObjectAssociationResponse;
import com.subskribe.billy.hubspot.model.HubSpotOrderAnnualAmountProperties;
import com.subskribe.billy.hubspot.model.HubSpotOrderLine;
import com.subskribe.billy.hubspot.model.HubSpotOrderLineProperties;
import com.subskribe.billy.hubspot.model.HubSpotProperty;
import com.subskribe.billy.hubspot.model.HubSpotSearchResponse;
import com.subskribe.billy.hubspot.model.HubSpotSubscription;
import com.subskribe.billy.hubspot.model.HubSpotSubscriptionLineItem;
import com.subskribe.billy.hubspot.model.HubSpotSubscriptionLineItemProperties;
import com.subskribe.billy.hubspot.model.HubSpotSubscriptionProperties;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.ratelimit.RateLimitExceededException;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import dev.failsafe.Failsafe;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class HubSpotClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(HubSpotClient.class);

    private static final String PATH_SEPARATOR = "/";

    private static final String HUBSPOT_API_CRM_V3 = "/crm/v3/objects/";

    private static final String HUBSPOT_API_SCHEMAS_PATH = "/crm/v3/schemas/";

    private static final String HUBSPOT_API_NOTES_PATH = HUBSPOT_API_CRM_V3 + "notes";

    private static final String HUBSPOT_API_NOTES_ASSOCIATIONS_PATH = "/crm/v4/objects/notes/%s/associations/%s/%s";

    private static final String HUBSPOT_API_ASSOCIATIONS_PATH = "/crm/v4/associations/%s/%s/labels";

    private static final String HUBSPOT_API_OBJECT_ASSOCIATION_PATH = "/crm/v4/objects/%s/%s/associations/%s/%s";

    private static final String HUBSPOT_PROPERTIES_PATH_V3 = "/crm/v3/properties/%s";

    private static final String HUBSPOT_PROPERTIES_PATH_V2 = "/properties/v2/%s/properties";

    private static final String HUBSPOT_UPDATE_PROPERTY_PATH = "/properties/v2/%s/properties/named/%s";

    private static final String HUBSPOT_CREATE_PROPERTY_GROUP_PATH = "/crm/v3/properties/%s/groups";

    private static final String HUBSPOT_CREATE_PROPERTIES_PATH = "/crm/v3/properties/%s/batch/create";

    static final String HUBSPOT_DOWNLOAD_LINK_FORMAT = "https://%s/oauth/authorize?client_id=%s&redirect_uri=%s&scope=%s&state=%s";

    static final String HUBSPOT_BASE_URL = "app.hubspot.com";

    static final String HUBSPOT_REQUEST_PARAM_VALUE_AUTHORIZATION_CODE = "authorization_code";

    static final String HUBSPOT_REQUEST_PARAM_CODE = "code";

    static final String HUBSPOT_REQUEST_PARAM_REDIRECT_URI = "redirect_uri";

    static final String HUBSPOT_REQUEST_PARAM_APP_ID = "app_id";

    static final String HUBSPOT_APP_ID_SECRET_NAME = "HUBSPOT_PUBLIC_APP_ID";

    private static final String PROPERTIES_SEPARATOR = ",";

    private static final String HUBSPOT_REQUEST_PARAM_PROPERTIES = "properties";

    private static final String HUBSPOT_API_DEAL_PATH = HUBSPOT_API_CRM_V3 + HubSpotDealProperties.API_NAME;

    private static final String HUBSPOT_API_COMPANY_PATH = HUBSPOT_API_CRM_V3 + HubSpotCompanyProperties.API_NAME;

    private static final String HUBSPOT_API_ACCOUNT_DETAILS_PATH = "/account-info/v3/details";

    private static final String HUBSPOT_REQUEST_PROPERTIES_BODY = "{ \"properties\": %s }";

    private static final String HUBSPOT_BATCH_OBJECT_REQUEST_BODY = "{ \"inputs\" : [ %s ] }";

    private static final String HUBSPOT_INPUTS_WRAPPER = "{ \"inputs\" : %s }";

    private static final String HUBSPOT_ASSOCIATION_BODY = "[ %s ]";

    static final String HUBSPOT_PAGE_LIMIT = "100";

    private static final String HUBSPOT_API_BATCH_ARCHIVE_PATH_FORMAT = HUBSPOT_API_CRM_V3 + "%s/batch/archive";

    private static final String HUBSPOT_API_BATCH_OBJECT_CREATE_PATH = HUBSPOT_API_CRM_V3 + "%s/batch/create";

    private static final String HUBSPOT_API_BATCH_OBJECT_UPDATE_PATH = HUBSPOT_API_CRM_V3 + "%s/batch/update";

    private static final String HUBSPOT_API_ASSOCIATIONS_PATH_FORMAT = HUBSPOT_API_CRM_V3 + "%s/%s/associations/%s";

    private static final String HUBSPOT_API_OBJECT_PROPERTY_PATH = "/crm/v3/properties/%s";

    private static final String HUBSPOT_API_ASSOCIATED_OBJECTS_PATH = "/crm/v3/objects/%s/%s/associations/%s";

    private static final String HUBSPOT_API_ASSOCIATED_CONTACTS_PATH = "/crm/v4/objects/companies/%s/associations/contacts";

    private static final String HUBSPOT_ASSOCIATION_NAME_FORMAT = "%s to %s";

    private static final String HUBSPOT_BATCH_READ_PATH_FORMAT = HUBSPOT_API_CRM_V3 + "%s/batch/read";

    private static final String HUBSPOT_PAGINATED_RESPONSE_AFTER = "after";

    private static final int MAX_CONTACT_PAGES = 2;

    private static final Gson GSON = new Gson();

    private static final ObjectMapper DEFAULT_OBJECT_MAPPER = JacksonProvider.defaultMapper();

    private final HubspotApiClient hubspotApiClient;
    private final HubSpotIntegration hubSpotIntegration;

    public HubSpotClient(HubspotApiClient hubspotApiClient, HubSpotIntegration hubSpotIntegration) {
        this.hubspotApiClient = hubspotApiClient;
        this.hubSpotIntegration = hubSpotIntegration;
    }

    HubSpotCompany getHubSpotCompanyById(String companyId) {
        try {
            Map<String, String> parameters = Map.of(HUBSPOT_REQUEST_PARAM_PROPERTIES, StringUtils.join(HubSpotCompanyProperties.PROPERTIES, ","));
            String responseString = hubspotApiClient.hubspotApiGet(HUBSPOT_API_COMPANY_PATH + "/" + companyId, parameters);
            return HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotCompany.class);
        } catch (IllegalStateException | IOException e) {
            String message = String.format("Failed to find a HubSpot company with ID: %s", companyId);
            LOGGER.warn(message);
            throw new IllegalStateException(message);
        }
    }

    HubSpotCustomProperties getHubSpotObjectWithProperties(String objectQualifiedName, String objectId, List<String> properties) {
        String propertiesArg = String.join(PROPERTIES_SEPARATOR, properties);
        try {
            Map<String, String> parameters = Map.of(HUBSPOT_REQUEST_PARAM_PROPERTIES, propertiesArg);
            String responseString = hubspotApiClient.hubspotApiGet(HUBSPOT_API_CRM_V3 + objectQualifiedName + "/" + objectId, parameters);
            return DEFAULT_OBJECT_MAPPER.readValue(responseString, HubSpotCustomProperties.class);
        } catch (IllegalStateException | IOException e) {
            String message = String.format("Failed to find a HubSpot object with ID: %s", objectId);
            throw new ConflictingStateException(message);
        }
    }

    HubSpotAccountDetails getHubSpotAccountDetails() {
        try {
            String responseString = hubspotApiClient.hubspotApiGet(HUBSPOT_API_ACCOUNT_DETAILS_PATH, Map.of());
            return DEFAULT_OBJECT_MAPPER.readValue(responseString, HubSpotAccountDetails.class);
        } catch (IllegalStateException | IOException e) {
            String message = "Unable to fetch hubspot account details";
            LOGGER.warn(message);
            throw new ConflictingStateException(message);
        }
    }

    Optional<HubSpotDeal> getDealById(String dealId) {
        try {
            Map<String, String> parameters = Map.of(HUBSPOT_REQUEST_PARAM_PROPERTIES, StringUtils.join(HubSpotDealProperties.PROPERTIES, ","));
            String responseString = hubspotApiClient.hubspotApiGet(HUBSPOT_API_DEAL_PATH + "/" + dealId, parameters);
            return Optional.of(HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotDeal.class));
        } catch (IllegalStateException | IOException e) {
            LOGGER.warn(String.format("Failed to find a HubSpot deal with ID: %s", dealId));
        }
        return Optional.empty();
    }

    List<HubSpotCustomProperties> getObjectsWithProperties(List<String> hsObjectIds, String objectQualifiedName, List<String> properties) {
        List<List<String>> batchedIds = Lists.partition(hsObjectIds, 100);
        List<HubSpotCustomProperties> result = new ArrayList<>();

        for (var batch : batchedIds) {
            HubSpotSearchResponse<HubSpotCustomProperties> searchResponse = readMany(
                objectQualifiedName,
                batch,
                HubSpotCustomProperties.class,
                properties,
                Optional.empty()
            );

            result.addAll(searchResponse.getResults());
        }
        return result;
    }

    List<HubSpotContact> getContactsByIds(List<String> contactIds) {
        List<List<String>> batchedIds = Lists.partition(contactIds, 100);
        List<HubSpotContact> result = new ArrayList<>();

        for (var batch : batchedIds) {
            HubSpotSearchResponse<HubSpotContact> searchResponse = readMany(
                HubSpotContactProperties.API_NAME,
                batch,
                HubSpotContact.class,
                HubSpotContactProperties.PROPERTIES,
                Optional.empty()
            );

            result.addAll(searchResponse.getResults());
        }

        return result;
    }

    Optional<HubSpotAssociatedIdsResponse> getAssociatedIdsByHubSpotObjectId(String hubSpotObjectId, String hubSpotId, String targetObjectId) {
        try {
            String responseString = hubspotApiClient.hubspotApiGet(
                String.format(HUBSPOT_API_ASSOCIATIONS_PATH_FORMAT, hubSpotObjectId, hubSpotId, targetObjectId),
                Map.of()
            );
            return Optional.of(HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotAssociatedIdsResponse.class));
        } catch (Exception e) {
            LOGGER.warn("Failed to get associated IDs for object {} with ID {} to object {}", hubSpotObjectId, hubSpotId, targetObjectId, e);
            return Optional.empty();
        }
    }

    Optional<HubSpotAssociatedIdsResponse> getAssociatedCompanyByDealId(String dealId) {
        try {
            String responseString = hubspotApiClient.hubspotApiGet("/crm/v3/objects/deal/" + dealId + "/associations/companies", Map.of());
            return Optional.of(HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotAssociatedIdsResponse.class));
        } catch (Exception e) {
            LOGGER.warn("Failed to get HubSpot company for deal id: {}", dealId, e);
            return Optional.empty();
        }
    }

    void upsertAccount(String accountId, String accountCrmId, HubSpotCompanyProperties hubSpotCompanyProperties) {
        try {
            hubspotApiClient.hubspotApiPatch(
                String.format("%s/%s", HUBSPOT_API_COMPANY_PATH, accountCrmId),
                String.format(HUBSPOT_REQUEST_PROPERTIES_BODY, HubspotClientConstants.HUBSPOT_GSON.toJson(hubSpotCompanyProperties))
            );
        } catch (IOException e) {
            LOGGER.warn("Failed to sync account: {} with HubSpot ID: {}", accountId, accountCrmId, e);
            // todo: shouldn't we be throwing a ServiceFailureException here
        }
    }

    HubSpotDeal updateDeal(String dealId, HubSpotDealProperties hubSpotDealProperties) throws IOException {
        String responseString = hubspotApiClient.hubspotApiPatch(
            String.format("%s/%s", HUBSPOT_API_DEAL_PATH, dealId),
            String.format(HUBSPOT_REQUEST_PROPERTIES_BODY, HubspotClientConstants.HUBSPOT_GSON.toJson(hubSpotDealProperties))
        );
        return HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotDeal.class);
    }

    HubSpotSubscription postSubscription(String subscriptionObjectId, HubSpotSubscriptionProperties subscriptionProperties) {
        try {
            String responseString = hubspotApiClient.hubspotApiPost(
                HUBSPOT_API_CRM_V3 + subscriptionObjectId,
                String.format(HUBSPOT_REQUEST_PROPERTIES_BODY, HubspotClientConstants.HUBSPOT_GSON.toJson(subscriptionProperties))
            );
            return HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotSubscription.class);
        } catch (IOException e) {
            LOGGER.warn("Failed to write subscription to HubSpot", e);
            throw new ServiceFailureException("Failed to write subscription to HubSpot", e);
        }
    }

    HubSpotSubscription upsertSubscription(
        String subscriptionObjectId,
        HubSpotSubscription hubSpotSubscription,
        HubSpotSubscriptionProperties subscriptionProperties
    ) {
        try {
            String responseString = hubspotApiClient.hubspotApiPatch(
                HUBSPOT_API_CRM_V3 + subscriptionObjectId + "/" + hubSpotSubscription.getId(),
                String.format(HUBSPOT_REQUEST_PROPERTIES_BODY, HubspotClientConstants.HUBSPOT_GSON.toJson(subscriptionProperties))
            );
            return HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotSubscription.class);
        } catch (IOException e) {
            LOGGER.warn("Failed to write subscription to HubSpot", e);
            throw new ServiceFailureException("Failed to write subscription to HubSpot", e);
        }
    }

    void postAnnualAmounts(String annualAmountQualifiedName, List<HubSpotObject<HubSpotOrderAnnualAmountProperties>> orderAnnualAmountQualifiedName) {
        String requestBody = orderAnnualAmountQualifiedName
            .stream()
            .map(HubspotClientConstants.HUBSPOT_GSON::toJson)
            .collect(Collectors.joining(","));

        try {
            hubspotApiClient.hubspotApiPost(
                String.format(HUBSPOT_API_BATCH_OBJECT_CREATE_PATH, annualAmountQualifiedName),
                String.format(HUBSPOT_BATCH_OBJECT_REQUEST_BODY, requestBody)
            );
        } catch (IOException e) {
            throw new ServiceFailureException("Failed to annual amounts to HubSpot", e);
        }
    }

    List<HubSpotSubscriptionLineItem> postSubscriptionLineItems(
        String subscriptionLineItemObjectId,
        List<HubSpotObject<HubSpotSubscriptionLineItemProperties>> subscriptionLineItemRequests
    ) {
        String requestBody = subscriptionLineItemRequests.stream().map(HubspotClientConstants.HUBSPOT_GSON::toJson).collect(Collectors.joining(","));

        try {
            var responseString = hubspotApiClient.hubspotApiPost(
                String.format(HUBSPOT_API_BATCH_OBJECT_CREATE_PATH, subscriptionLineItemObjectId),
                String.format(HUBSPOT_BATCH_OBJECT_REQUEST_BODY, requestBody)
            );
            var typeReference = DEFAULT_OBJECT_MAPPER.getTypeFactory()
                .constructParametricType(HubSpotCreateResponse.class, HubSpotSubscriptionLineItem.class);
            HubSpotCreateResponse<HubSpotSubscriptionLineItem> response = DEFAULT_OBJECT_MAPPER.readValue(responseString, typeReference);
            return response.getResults();
        } catch (IOException e) {
            throw new ServiceFailureException("Failed to post subscription line items to HubSpot", e);
        }
    }

    List<HubSpotOrderLine> postOrderLines(String orderLineQualifiedName, List<HubSpotObject<HubSpotOrderLineProperties>> orderLineRequests) {
        String requestBody = orderLineRequests.stream().map(HubspotClientConstants.HUBSPOT_GSON::toJson).collect(Collectors.joining(","));

        try {
            var responseString = hubspotApiClient.hubspotApiPost(
                String.format(HUBSPOT_API_BATCH_OBJECT_CREATE_PATH, orderLineQualifiedName),
                String.format(HUBSPOT_BATCH_OBJECT_REQUEST_BODY, requestBody)
            );
            var typeReference = DEFAULT_OBJECT_MAPPER.getTypeFactory().constructParametricType(HubSpotCreateResponse.class, HubSpotOrderLine.class);
            HubSpotCreateResponse<HubSpotOrderLine> response = DEFAULT_OBJECT_MAPPER.readValue(responseString, typeReference);
            return response.getResults();
        } catch (IOException e) {
            throw new ServiceFailureException("Failed to post order line items to HubSpot", e);
        }
    }

    List<HubSpotObject<Map<String, String>>> postMany(String objectQualifiedName, List<HubSpotObject<Map<String, String>>> objects) {
        if (objects.isEmpty()) {
            return List.of();
        }

        String requestBody = objects.stream().map(HubspotClientConstants.HUBSPOT_GSON::toJson).collect(Collectors.joining(","));

        try {
            String responseString = hubspotApiClient.hubspotApiPost(
                String.format(HUBSPOT_API_BATCH_OBJECT_CREATE_PATH, objectQualifiedName),
                String.format(HUBSPOT_BATCH_OBJECT_REQUEST_BODY, requestBody)
            );
            Type responseType = new TypeToken<HubSpotApiResponse<HubSpotObject<Map<String, String>>>>() {}.getType();
            HubSpotApiResponse<HubSpotObject<Map<String, String>>> response = HubspotClientConstants.HUBSPOT_GSON.fromJson(
                responseString,
                responseType
            );
            return response.getResults();
        } catch (IOException e) {
            throw new ServiceFailureException("Failed to post objects to HubSpot", e);
        }
    }

    public void archiveMany(String objectType, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        try {
            String path = String.format(HUBSPOT_API_BATCH_ARCHIVE_PATH_FORMAT, objectType);
            List<HubSpotIdWrapper> idObjects = ids.stream().map(HubSpotIdWrapper::new).toList();
            String payload = String.format(HUBSPOT_INPUTS_WRAPPER, HubspotClientConstants.HUBSPOT_GSON.toJson(idObjects));
            LOGGER.info("Archiving HubSpot objects of type {}: {}", objectType, ids);
            var response = hubspotApiClient.hubspotApiPost(path, payload);
            LOGGER.info("archive response: {}", response);
        } catch (IOException e) {
            throw new ServiceFailureException("hubspot call failed", e);
        }
    }

    HubSpotSearchResponse<HubSpotCustomObjectSchema> getBulkCustomObjectSchema() {
        try {
            String responseString = hubspotApiClient.hubspotApiGet(HUBSPOT_API_SCHEMAS_PATH, Map.of());
            return HubspotClientConstants.HUBSPOT_GSON.fromJson(
                responseString,
                new TypeToken<HubSpotSearchResponse<HubSpotCustomObjectSchema>>() {}.getType()
            );
        } catch (IOException e) {
            throw new ServiceFailureException("Failed to get custom object schema from on HubSpot", e);
        }
    }

    HubSpotNote postNote(HubSpotNoteProperties noteRequest) {
        try {
            String responseString = hubspotApiClient.hubspotApiPost(
                HUBSPOT_API_NOTES_PATH,
                String.format(HUBSPOT_REQUEST_PROPERTIES_BODY, HubspotClientConstants.HUBSPOT_GSON.toJson(noteRequest))
            );
            return HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotNote.class);
        } catch (IOException e) {
            LOGGER.warn("Failed to create HubSpot note", e);
            throw new ServiceFailureException("Failed to create HubSpot note", e);
        }
    }

    void putNotesAssociation(String noteId, String objectType, String objectId, HubSpotObjectAssociation objectAssociation) {
        try {
            hubspotApiClient.hubspotApiPut(
                String.format(HUBSPOT_API_NOTES_ASSOCIATIONS_PATH, noteId, objectType, objectId),
                String.format(HUBSPOT_ASSOCIATION_BODY, HubspotClientConstants.HUBSPOT_GSON.toJson(objectAssociation))
            );
        } catch (IOException e) {
            LOGGER.warn("Failed to create association between note with ID: {} and {} with ID: {}", noteId, objectType, objectId, e);
            throw new ServiceFailureException("Failed to create HubSpot note association", e);
        }
    }

    HubSpotObjectAssociationResponse getObjectAssociation(String sourceType, String targetType) {
        try {
            String responseString = hubspotApiClient.hubspotApiGet(String.format(HUBSPOT_API_ASSOCIATIONS_PATH, sourceType, targetType), Map.of());
            return HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotObjectAssociationResponse.class);
        } catch (IOException e) {
            LOGGER.warn("Failed to get object association on HubSpot", e);
            throw new ServiceFailureException("Failed to get object association on HubSpot", e);
        }
    }

    void postObjectTypeAssociation(String sourceType, String targetType) {
        try {
            HubSpotObjectAssociation hubSpotObjectAssociation = new HubSpotObjectAssociation();
            hubSpotObjectAssociation.setName(String.format(HUBSPOT_ASSOCIATION_NAME_FORMAT, sourceType, targetType));
            hubSpotObjectAssociation.setLabel(String.format(HUBSPOT_ASSOCIATION_NAME_FORMAT, sourceType, targetType));

            String responseString = hubspotApiClient.hubspotApiPost(
                String.format(HUBSPOT_API_ASSOCIATIONS_PATH, sourceType, targetType),
                HubspotClientConstants.HUBSPOT_GSON.toJson(hubSpotObjectAssociation)
            );
            HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotObjectAssociationResponse.class);
        } catch (IOException e) {
            LOGGER.warn("Failed to post object association on HubSpot", e);
            throw new ConflictingStateException("Failed to post object association on HubSpot");
        }
    }

    HubSpotAssociatedIdsResponse getAssociatedDealsByCompany(String companyId) {
        try {
            String responseString = hubspotApiClient.hubspotApiGet(
                String.format("%s/%s/associations/deals", HUBSPOT_API_COMPANY_PATH, companyId),
                Map.of()
            );
            return HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotAssociatedIdsResponse.class);
        } catch (IOException e) {
            LOGGER.warn("Failed to get HubSpot deals by company id: {}", companyId, e);
            throw new ServiceFailureException("Failed to get HubSpot deals by company id.", e);
        }
    }

    List<String> getAssociatedContactIdsByCompany(String companyId) {
        List<HubSpotAssociation> associatedContacts = new ArrayList<>();
        String after = StringUtils.EMPTY;

        for (int i = 0; i < MAX_CONTACT_PAGES; i++) {
            HubSpotSearchResponse<HubSpotAssociation> response = getAssociatedContactIdsByCompany(companyId, after);
            associatedContacts.addAll(response.getResults());

            if (response.getPaging() == null) {
                break;
            }
            after = response.getPaging().getNext().getAfter();
        }

        return associatedContacts.stream().map(HubSpotAssociation::toObjectId).toList();
    }

    private HubSpotSearchResponse<HubSpotAssociation> getAssociatedContactIdsByCompany(String companyId, String after) {
        Map<String, String> parameters = new HashMap<>();
        if (StringUtils.isNotBlank(after)) {
            parameters.put(HUBSPOT_PAGINATED_RESPONSE_AFTER, after);
        }

        try {
            String responseString = hubspotApiClient.hubspotApiGet(String.format(HUBSPOT_API_ASSOCIATED_CONTACTS_PATH, companyId), parameters);
            TypeReference<HubSpotSearchResponse<HubSpotAssociation>> typeReference = new TypeReference<>() {};
            return DEFAULT_OBJECT_MAPPER.readValue(responseString, typeReference);
        } catch (IOException e) {
            throw new ServiceFailureException("Failed to get HubSpot contacts by company id.", e);
        }
    }

    HubSpotAssociatedIdsResponse getAssociatedObjects(String fromObjectType, String fromObjectId, String toObjectType) {
        try {
            String path = String.format(HUBSPOT_API_ASSOCIATED_OBJECTS_PATH, fromObjectType, fromObjectId, toObjectType);
            String responseString = hubspotApiClient.hubspotApiGet(path, Map.of());
            return HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, HubSpotAssociatedIdsResponse.class);
        } catch (IOException e) {
            LOGGER.warn("Failed to fetch associated objects. from: {} {}. to: {}", fromObjectType, fromObjectId, toObjectType, e);
            throw new ServiceFailureException("Failed to fetch associated objects", e);
        }
    }

    void createObjectSchema(HubSpotCustomObjectSchema customObjectSchema) {
        try {
            hubspotApiClient.hubspotApiPost(HUBSPOT_API_SCHEMAS_PATH, HubspotClientConstants.HUBSPOT_GSON.toJson(customObjectSchema));
        } catch (IOException e) {
            LOGGER.warn("Failed to create object on hubspot", e);
            throw new ServiceFailureException("Failed to create object on hubspot", e);
        }
    }

    void addPropertyGroup(String objectType, String groupName, String groupLabel) throws IOException {
        String requestBody = GSON.toJson(Map.of("name", groupName, "label", groupLabel));
        hubspotApiClient.hubspotApiPost(String.format(HUBSPOT_CREATE_PROPERTY_GROUP_PATH, objectType), requestBody);
    }

    HubSpotApiResponse<HubSpotCustomObjectProperties> addProperties(String objectType, List<HubSpotCustomObjectProperties> properties) {
        String requestBody = String.format(
            HUBSPOT_BATCH_OBJECT_REQUEST_BODY,
            properties.stream().map(HubspotClientConstants.HUBSPOT_GSON::toJson).collect(Collectors.joining(","))
        );

        try {
            String responseString = hubspotApiClient.hubspotApiPost(String.format(HUBSPOT_CREATE_PROPERTIES_PATH, objectType), requestBody);
            return GSON.fromJson(responseString, new TypeToken<HubSpotApiResponse<HubSpotCustomObjectProperties>>() {}.getType());
        } catch (RateLimitExceededException rle) {
            throw rle;
        } catch (Exception e) {
            throw new ServiceFailureException("Failed to create properties on HubSpot", e);
        }
    }

    void updateProperty(String objectType, String propertyName, HubSpotCustomPropertyUpdate propertyPayload) throws IOException {
        String requestBody = GSON.toJson(propertyPayload);
        hubspotApiClient.hubspotApiPatch(String.format(HUBSPOT_UPDATE_PROPERTY_PATH, objectType, propertyName), requestBody);
    }

    List<HubSpotProperty> getObjectProperties(String objectType) {
        try {
            String responseString = hubspotApiClient.hubspotApiGet(String.format(HUBSPOT_PROPERTIES_PATH_V3, objectType), Map.of());
            var typeReference = DEFAULT_OBJECT_MAPPER.getTypeFactory().constructParametricType(HubSpotSearchResponse.class, HubSpotProperty.class);
            HubSpotSearchResponse<HubSpotProperty> response = DEFAULT_OBJECT_MAPPER.readValue(responseString, typeReference);
            return response.getResults();
        } catch (IOException e) {
            LOGGER.warn("Failed to find {} properties on HubSpot", objectType, e);
            throw new ServiceFailureException("Failed to find object properties on HubSpot", e);
        }
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    <T> HubSpotSearchResponse<T> readMany(
        String objectType,
        List<String> objectIds,
        Class<T> clazz,
        List<String> properties,
        Optional<String> idProperty
    ) {
        try {
            HubSpotBatchReadRequest searchRequest = new HubSpotBatchReadRequest();
            searchRequest.setProperties(properties);

            // Set IDs to search
            List<Map<String, String>> inputs = new ArrayList<>();
            objectIds.forEach(id -> inputs.add(Map.of("id", id)));
            searchRequest.setInputs(inputs);

            // idProperty is the object property you want to search by. Default uses hs_object_id.
            idProperty.ifPresent(searchRequest::setIdProperty);

            String path = String.format(HUBSPOT_BATCH_READ_PATH_FORMAT, objectType);
            String responseString = hubspotApiClient.hubspotApiPost(path, HubspotClientConstants.HUBSPOT_GSON.toJson(searchRequest));
            var typeReference = DEFAULT_OBJECT_MAPPER.getTypeFactory().constructParametricType(HubSpotSearchResponse.class, clazz);
            return DEFAULT_OBJECT_MAPPER.readValue(responseString, typeReference);
        } catch (IOException e) {
            LOGGER.warn("Failed to search on HubSpot", e);
            throw new ServiceFailureException("Failed to search on HubSpot", e);
        }
    }

    <T> HubSpotObject<T> createObject(String objectTypeId, HubSpotObject<T> object) {
        try {
            String responseString = hubspotApiClient.hubspotApiPost(
                HUBSPOT_API_CRM_V3 + objectTypeId,
                String.format(HUBSPOT_REQUEST_PROPERTIES_BODY, HubspotClientConstants.HUBSPOT_GSON.toJson(object.getProperties()))
            );
            return HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, new TypeToken<HubSpotObject<T>>() {}.getType());
        } catch (IOException e) {
            LOGGER.warn("Failed to create object on hubspot", e);
            throw new ServiceFailureException("Failed to create object on hubspot", e);
        }
    }

    <T> HubSpotObject<T> updateObject(String objectType, String objectId, HubSpotObject<T> object) {
        try {
            String responseString = hubspotApiClient.hubspotApiPatch(
                HUBSPOT_API_CRM_V3 + objectType + PATH_SEPARATOR + objectId,
                String.format(HUBSPOT_REQUEST_PROPERTIES_BODY, HubspotClientConstants.HUBSPOT_GSON.toJson(object.getProperties()))
            );
            return HubspotClientConstants.HUBSPOT_GSON.fromJson(responseString, new TypeToken<HubSpotObject<T>>() {}.getType());
        } catch (IOException e) {
            LOGGER.warn("Failed to update object on hubspot", e);
            throw new ServiceFailureException("Failed to update object on hubspot", e);
        }
    }

    void putHubSpotObjectAssociation(
        String fromObjectTypeId,
        String fromObjectId,
        String toObjectTypeId,
        String toObjectId,
        HubSpotObjectAssociation hubSpotObjectAssociation
    ) {
        try {
            hubspotApiClient.hubspotApiPut(
                String.format(HUBSPOT_API_OBJECT_ASSOCIATION_PATH, fromObjectTypeId, fromObjectId, toObjectTypeId, toObjectId),
                String.format(HUBSPOT_ASSOCIATION_BODY, HubspotClientConstants.HUBSPOT_GSON.toJson(hubSpotObjectAssociation))
            );
        } catch (IOException e) {
            LOGGER.warn(
                "Failed to create association between {} with id: {} and {} with ID: {}",
                fromObjectTypeId,
                fromObjectId,
                toObjectTypeId,
                toObjectId,
                e
            );
            throw new ServiceFailureException("Failed to create an object association on HubSpot.", e);
        }
    }

    public List<HubSpotCustomObjectProperties> getProperties(String objectType) {
        try {
            String responseString = hubspotApiClient.hubspotApiGet(String.format(HUBSPOT_API_OBJECT_PROPERTY_PATH, objectType), Map.of());
            HubSpotSearchResponse<HubSpotCustomObjectProperties> customPropertiesResponse = GSON.fromJson(
                responseString,
                new TypeToken<HubSpotSearchResponse<HubSpotCustomObjectProperties>>() {}.getType()
            );
            return customPropertiesResponse.getResults();
        } catch (IOException e) {
            throw new ServiceFailureException("Failed to get properties from hubspot", e);
        }
    }

    List<HubSpotCustomObjectSchema> getCustomObjectSchemaList() {
        HubSpotSearchResponse<HubSpotCustomObjectSchema> hubSpotCustomObjectResponse = getBulkCustomObjectSchema();
        return hubSpotCustomObjectResponse.getResults();
    }

    <T> void updateObjects(String objectQualifiedName, List<T> objects) throws IOException {
        hubspotApiClient.hubspotApiPost(
            String.format(HUBSPOT_API_BATCH_OBJECT_UPDATE_PATH, objectQualifiedName),
            String.format(HUBSPOT_INPUTS_WRAPPER, HubspotClientConstants.HUBSPOT_GSON.toJson(objects))
        );
    }

    List<HubSpotCustomObjectProperties> getObjectPropertiesV2(String objectType) {
        try {
            String responseString = hubspotApiClient.hubspotApiGet(String.format(HUBSPOT_PROPERTIES_PATH_V2, objectType), Map.of());
            return DEFAULT_OBJECT_MAPPER.readValue(responseString, new TypeReference<>() {});
        } catch (IOException e) {
            throw new ConflictingStateException("Failed to find object properties on HubSpot");
        }
    }

    void verifyRecordsWithRetries(String objectQualifiedName, List<String> hubSpotObjectIds) {
        try {
            Failsafe.with(RETRY_POLICY).get(() -> verifyHubSpotRecords(objectQualifiedName, hubSpotObjectIds));
        } catch (Exception e) {
            LOGGER.info("Failed to verify HubSpot records.", e);
        }
    }

    private String verifyHubSpotRecords(String objectQualifiedName, List<String> hubSpotObjectIds) {
        HubSpotSearchResponse<HubSpotCustomProperties> searchResponse = readMany(
            objectQualifiedName,
            hubSpotObjectIds,
            HubSpotCustomProperties.class,
            List.of(),
            Optional.empty()
        );

        if (searchResponse == null) {
            throw new IllegalStateException("Failed to verify HubSpot records.");
        }

        int expectedSize = hubSpotObjectIds.size();
        int actualSize = searchResponse.getResults().size();
        if (expectedSize != actualSize) {
            LOGGER.warn(
                "HubSpot sync size mismatch. Object Type: {}. Expected size: {}, Actual size: {}",
                objectQualifiedName,
                hubSpotObjectIds.size(),
                actualSize
            );
        }

        return searchResponse.toString();
    }

    public HubSpotIntegration getHubSpotIntegration() {
        return hubSpotIntegration;
    }
}
