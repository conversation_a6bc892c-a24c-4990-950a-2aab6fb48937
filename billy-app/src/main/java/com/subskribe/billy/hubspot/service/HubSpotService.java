package com.subskribe.billy.hubspot.service;

import com.amazonaws.services.dynamodbv2.LockItem;
import com.google.common.hash.Hashing;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.compositeorder.model.CancelAndRestructureOrderDataAggregator;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.country.CountryCode;
import com.subskribe.billy.crm.CrmObjectType;
import com.subskribe.billy.crm.CrmSyncDirection;
import com.subskribe.billy.crm.CrmType;
import com.subskribe.billy.crm.model.CrmAccountCurrencySourceType;
import com.subskribe.billy.crm.model.CrmFieldMapping;
import com.subskribe.billy.crm.model.CrmIntegrationConfiguration;
import com.subskribe.billy.crm.service.CrmFieldMappingService;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldSource;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.DuplicateObjectException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.graphql.order.OrderDataAggregator;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.order.OrderDetailsMapper;
import com.subskribe.billy.hubspot.HubSpotFormatter;
import com.subskribe.billy.hubspot.client.HubspotClientConstants;
import com.subskribe.billy.hubspot.client.HubspotClientFactory;
import com.subskribe.billy.hubspot.db.HubSpotDAO;
import com.subskribe.billy.hubspot.mapper.HubSpotContactMapper;
import com.subskribe.billy.hubspot.mapper.HubSpotMapper;
import com.subskribe.billy.hubspot.model.AssociatedId;
import com.subskribe.billy.hubspot.model.HubSpotAccountDetails;
import com.subskribe.billy.hubspot.model.HubSpotBatchCustomObjectRequestAssociation;
import com.subskribe.billy.hubspot.model.HubSpotCompany;
import com.subskribe.billy.hubspot.model.HubSpotCompanyCard;
import com.subskribe.billy.hubspot.model.HubSpotCompanyCardResult;
import com.subskribe.billy.hubspot.model.HubSpotCompanyProperties;
import com.subskribe.billy.hubspot.model.HubSpotContactProperties;
import com.subskribe.billy.hubspot.model.HubSpotCustomObjectProperties;
import com.subskribe.billy.hubspot.model.HubSpotCustomObjectSchema;
import com.subskribe.billy.hubspot.model.HubSpotCustomProperties;
import com.subskribe.billy.hubspot.model.HubSpotCustomPropertyUpdate;
import com.subskribe.billy.hubspot.model.HubSpotDeal;
import com.subskribe.billy.hubspot.model.HubSpotDealProperties;
import com.subskribe.billy.hubspot.model.HubSpotDealType;
import com.subskribe.billy.hubspot.model.HubSpotIntegration;
import com.subskribe.billy.hubspot.model.HubSpotNote;
import com.subskribe.billy.hubspot.model.HubSpotNoteProperties;
import com.subskribe.billy.hubspot.model.HubSpotObject;
import com.subskribe.billy.hubspot.model.HubSpotObjectAssociation;
import com.subskribe.billy.hubspot.model.HubSpotOption;
import com.subskribe.billy.hubspot.model.HubSpotOrder;
import com.subskribe.billy.hubspot.model.HubSpotOrderAnnualAmountProperties;
import com.subskribe.billy.hubspot.model.HubSpotOrderLine;
import com.subskribe.billy.hubspot.model.HubSpotOrderLineProperties;
import com.subskribe.billy.hubspot.model.HubSpotOrderProperties;
import com.subskribe.billy.hubspot.model.HubSpotProperty;
import com.subskribe.billy.hubspot.model.HubSpotRateCardProperties;
import com.subskribe.billy.hubspot.model.HubSpotSubscription;
import com.subskribe.billy.hubspot.model.HubSpotSubscriptionLineItem;
import com.subskribe.billy.hubspot.model.HubSpotSubscriptionLineItemProperties;
import com.subskribe.billy.hubspot.model.HubSpotSubscriptionProperties;
import com.subskribe.billy.hubspot.model.ImmutableHubSpotCustomPropertyUpdate;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.LineItemMetrics;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderDiscountService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderServiceHelper;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.account.AccountAddressJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.shared.SubskribeUrlGenerator;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.CompositeOrderType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.lock.DynamoDBLockProvider;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.ratelimit.RateLimitExceededException;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import javax.ws.rs.ForbiddenException;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.WordUtils;
import org.mapstruct.factory.Mappers;

public class HubSpotService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HubSpotService.class);

    private static final String SUBSKRIBE_ACCOUNT_LINK_FORMAT = "%s/accounts/%s";

    private static final String SUBSKRIBE_ACCOUNT_SYNC_FORMAT = "%s/accounts/import?accountCrmId=%s";

    public static final String SUBSKRIBE_CREATE_ORDER_URL_FORMAT = "%s/orders/crm/change-order?accountCrmId=%s&opportunityCrmId=%s&type=%s";

    private static final String SUBSKRIBE_ORDER_LINK_FORMAT = "%s/orders/%s";

    private static final String SUBSKRIBE_OPPORTUNITY_LINK_FORMAT = "%s/opportunities/%s";

    private static final String HUBSPOT_DEFINED_ASSOCIATION = "HUBSPOT_DEFINED";

    private static final String USER_DEFINED_ASSOCIATION = "USER_DEFINED";

    private static final String HUBSPOT_SUBSCRIPTION_NAME_FORMAT = "%s (%s to %s)";

    private static final String HUBSPOT_SUBSCRIPTION_DATE_FORMAT = "%s/%s/%s";

    private static final String HUBSPOT_SUBSCRIPTION_SUCCESS_FORMAT = "Subscription created successfully on Subskribe, Subskribe Subscription ID %s.";

    private static final String SYNC_TO_SUBSKRIBE_ACTION = "Sync Company to Subskribe";

    private static final String VIEW_ON_SUBSKRIBE_ACTION = "View on Subskribe";

    private static final String CREATE_QUOTE_ON_SUBSKRIBE_ACTION = "Create Order";

    private static final String HUBSPOT_LINE_ITEM_NAME_FORMAT = "Item Name: %s: %s";

    private static final String HUBSPOT_SUBSCRIPTION_CHARGE_NAME_FORMAT = "%s : %s";

    private static final String MISSING_HUBSPOT_COMPANY_CARD_TITLE = "Unable to find an associated Company for this deal.";

    private static final String COMPOSITE_ORDER_TYPE_NOT_SUPPORTED_CARD_TITLE = "Composite order type not supported";

    private static final String HTTP_SCHEME_PREFIX = "http://";

    private static final String HTTPS_SCHEME_PREFIX = "https://";

    private static final String ORDER_NAME_FORMAT = "%s (%s - %s)";

    private static final String LINE_ITEM_TERM_FORMAT = "%s to %s";

    private static final String EXCLUDE_NON_NUMERIC_REGEX = "[^0-9]";

    private static final String INVOICE_NUMBER_FORMAT = "%s-%s";

    // TODO: Find a better way to handle invoice prefixes
    private static final String DEFAULT_INVOICE_PREFIX = "INV";

    // HubSpot property internal name -> Subskribe custom field name
    private static final Map<String, String> EDUCATION_PERFECT_COMPANY_FIELD_MAPPING = Map.of(
        "sector",
        "sector",
        "decile_icsea",
        "ICSEAScore",
        "ep_product",
        "EPProduct"
    );

    private static final String HUBSPOT_SYNC_LOCK_KEY_FORMAT = "hubSpotSync/%s/%s";

    private static final int LOCK_WAIT_SECONDS = 60;

    private static final int LOCK_RETRY_INTERVAL_SECONDS = 1;

    private static final String SUBSKRIBE_GROUP_NAME = "subskribe";

    private static final String HUBSPOT_PROPERTY_STRING_TYPE = "string";

    private static final String HUBSPOT_PROPERTY_ENUMERATION_TYPE = "enumeration";

    private static final String HUBSPOT_PROPERTY_TEXT_FIELD_TYPE = "text";

    private static final String HUBSPOT_PROPERTY_SELECT_FIELD_TYPE = "select";

    private static final String HUBSPOT_PROPERTY_CHECKBOX_FIELD_TYPE = "checkbox";

    private static final String DATE_TYPE = "date";

    private static final List<String> hubSpotNativeObjectNames = List.of(
        HubSpotDealProperties.API_NAME,
        HubSpotContactProperties.API_NAME,
        HubSpotCompanyProperties.API_NAME
    );

    private final OrderGetService orderGetService;

    private final AccountService accountService;

    private final AccountGetService accountGetService;

    private final TenantIdProvider tenantIdProvider;

    private final HubSpotDAO hubSpotDAO;

    private final HubSpotGetService hubSpotGetService;

    private final HubSpotIntegrationService hubSpotIntegrationService;

    private final FeatureService featureService;

    private final OrderDataAggregator orderDataAggregator;

    private final OpportunityGetService opportunityGetService;

    private final OpportunityService opportunityService;

    private final MetricsService metricsService;

    private final SubscriptionGetService subscriptionGetService;

    private final CustomFieldService customFieldService;

    private final HubSpotContactMapper hubSpotContactMapper;

    private final ProductCatalogGetService productCatalogGetService;

    private final BillyConfiguration billyConfiguration;

    private final TenantSettingService tenantSettingService;

    private final HubSpotMapper hubSpotMapper;

    private final OrderDetailsMapper orderDetailsMapper;

    private final CompositeOrderGetService compositeOrderGetService;

    private final CancelAndRestructureOrderDataAggregator cancelAndRestructureOrderDataAggregator;

    private final DynamoDBLockProvider dynamoDBLockProvider;

    private final HubspotClientFactory hubspotClientFactory;

    private final SecretsService secretsService;

    private final RateCardService rateCardService;

    private final CrmFieldMappingService crmFieldMappingService;

    private final HubSpotJobQueueService hubSpotJobQueueService;

    @Inject
    public HubSpotService(
        OrderGetService orderGetService,
        AccountService accountService,
        AccountGetService accountGetService,
        TenantIdProvider tenantIdProvider,
        HubSpotDAO hubSpotDAO,
        HubSpotGetService hubSpotGetService,
        HubSpotIntegrationService hubSpotIntegrationService,
        FeatureService featureService,
        OrderDataAggregator orderDataAggregator,
        OpportunityGetService opportunityGetService,
        OpportunityService opportunityService,
        MetricsService metricsService,
        SubscriptionGetService subscriptionGetService,
        CustomFieldService customFieldService,
        ProductCatalogGetService productCatalogGetService,
        BillyConfiguration billyConfiguration,
        TenantSettingService tenantSettingService,
        CompositeOrderGetService compositeOrderGetService,
        CancelAndRestructureOrderDataAggregator cancelAndRestructureOrderDataAggregator,
        DynamoDBLockProvider dynamoDBLockProvider,
        HubspotClientFactory hubspotClientFactory,
        SecretsService secretsService,
        RateCardService rateCardService,
        CrmFieldMappingService crmFieldMappingService,
        HubSpotJobQueueService hubSpotJobQueueService
    ) {
        this.orderGetService = orderGetService;
        this.accountService = accountService;
        this.accountGetService = accountGetService;
        this.tenantIdProvider = tenantIdProvider;
        this.hubSpotDAO = hubSpotDAO;
        this.hubSpotGetService = hubSpotGetService;
        this.hubSpotIntegrationService = hubSpotIntegrationService;
        this.featureService = featureService;
        this.orderDataAggregator = orderDataAggregator;
        this.opportunityGetService = opportunityGetService;
        this.opportunityService = opportunityService;
        this.subscriptionGetService = subscriptionGetService;
        this.metricsService = metricsService;
        this.customFieldService = customFieldService;
        this.productCatalogGetService = productCatalogGetService;
        this.billyConfiguration = billyConfiguration;
        this.tenantSettingService = tenantSettingService;
        this.compositeOrderGetService = compositeOrderGetService;
        this.cancelAndRestructureOrderDataAggregator = cancelAndRestructureOrderDataAggregator;
        this.dynamoDBLockProvider = dynamoDBLockProvider;
        this.hubspotClientFactory = hubspotClientFactory;
        this.secretsService = secretsService;
        this.rateCardService = rateCardService;
        this.crmFieldMappingService = crmFieldMappingService;
        this.hubSpotJobQueueService = hubSpotJobQueueService;
        hubSpotContactMapper = Mappers.getMapper(HubSpotContactMapper.class);
        hubSpotMapper = Mappers.getMapper(HubSpotMapper.class);
        orderDetailsMapper = Mappers.getMapper(OrderDetailsMapper.class);
    }

    private Boolean companyHasRequiredAddressFields(HubSpotCompany company) {
        return Stream.of(
            company.getProperties().getAddress(),
            company.getProperties().getCountry(),
            company.getProperties().getState(),
            company.getProperties().getZip(),
            company.getProperties().getCity()
        ).allMatch(StringUtils::isNotBlank);
    }

    public Account getCompanyFromHubSpotById(String companyId) throws RateLimitExceededException {
        String tenantId = tenantIdProvider.provideTenantIdString();
        if (!hubSpotIntegrationService.hasCompletedHubSpotIntegration(tenantId)) {
            throw new IllegalStateException("The tenant does not have a completed HubSpot integration");
        }
        Optional<Account> existingAccount = accountGetService.getAccountByCrmId(companyId);
        if (existingAccount.isPresent()) {
            throw new DuplicateObjectException("Account with this crm id already exists: " + companyId);
        }

        HubSpotClient hubSpotClient = hubspotClientFactory.forTenantIntegration();
        HubSpotCompany company = hubSpotClient.getHubSpotCompanyById(companyId);
        return convertAccountForHubSpotCompany(company);
    }

    public void syncAccountToHubSpot(String accountId) {
        LOGGER.info("Start of HubSpot account sync for {}", accountId);
        List<String> errors = new ArrayList<>();
        Account account = accountGetService.getAccount(accountId);
        if (StringUtils.isNotBlank(account.getCrmId())) {
            HubSpotCompanyProperties propertiesBody = new HubSpotCompanyProperties();
            propertiesBody.setSubskribeAccount(String.format(SUBSKRIBE_ACCOUNT_LINK_FORMAT, billyConfiguration.getSiteUrl(), account.getAccountId()));
            HubSpotClient hubSpotClient = hubspotClientFactory.forTenantIntegration();

            LOGGER.info("Syncing account to HubSpot for {}, CRM ID: {}. Request: {}", account.getAccountId(), account.getCrmId(), propertiesBody);
            hubSpotClient.upsertAccount(account.getAccountId(), account.getCrmId(), propertiesBody);
            syncCustomFieldsForAccountToHubSpot(hubSpotClient, account, errors);
        } else {
            errors.add(String.format("CRM ID missing for account %s", accountId));
        }
        LOGGER.info("End of HubSpot account sync for {}", accountId);
        throwIfSyncErrorsAreFound(errors);
    }

    private void throwIfSyncErrorsAreFound(List<String> errors) {
        if (!errors.isEmpty()) {
            throw new IllegalStateException("Following errors are found during the CRM sync: " + String.join("; ", errors));
        }
    }

    private void syncCustomFieldsForAccountToHubSpot(HubSpotClient hubSpotClient, Account account, List<String> errors) {
        if (!hasEnterpriseHubspotIntegration() || StringUtils.isBlank(account.getCrmId()) || !featureService.isEnabled(Feature.CRM_FIELD_MAPPING)) {
            return;
        }
        Map<String, String> propertiesMap = new HashMap<>();
        List<HubSpotProperty> hubSpotCompanyProperties = hubSpotClient.getObjectProperties(HubSpotCompanyProperties.API_NAME);
        CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ACCOUNT, account.getAccountId());
        Map<String, CustomFieldValue> entries = customField.getEntries();
        List<CustomFieldValue> customFieldsToSync = entries.values().stream().toList();

        List<CrmFieldMapping> outboundCrmFieldMappings = crmFieldMappingService
            .getCrmFieldMappings(CrmType.HUBSPOT, CrmObjectType.ACCOUNT)
            .stream()
            .filter(m -> CrmSyncDirection.OUTBOUND == m.getDirection())
            .toList();

        for (var crmFieldMapping : outboundCrmFieldMappings) {
            // Check that the Subskribe custom field exists
            Optional<CustomFieldValue> customFieldValue = customFieldsToSync
                .stream()
                .filter(c -> StringUtils.equals(crmFieldMapping.getSubskribeFieldName(), c.getName()))
                .findAny();
            if (customFieldValue.isEmpty()) {
                continue;
            }
            CustomFieldValue cfValue = customFieldValue.get();

            // Check that the HubSpot property field exists
            Optional<HubSpotProperty> property = hubSpotCompanyProperties
                .stream()
                .filter(p -> StringUtils.equalsIgnoreCase(crmFieldMapping.getCrmFieldName(), p.getName()))
                .findAny();
            if (property.isPresent()) {
                String fieldType = property.get().getType();
                List<String> propertyOptions = property.get().getOptions().stream().map(HubSpotOption::getValue).toList();
                if (
                    StringUtils.equals(fieldType, HUBSPOT_PROPERTY_STRING_TYPE) || new HashSet<>(propertyOptions).containsAll(cfValue.getSelections())
                ) {
                    propertiesMap.put(property.get().getName(), formatHubSpotCustomFieldDisplay(cfValue));
                }
            }
        }

        var updateRequests = new ArrayList<>();
        if (MapUtils.isNotEmpty(propertiesMap)) {
            HubSpotObject<Map<String, String>> customProperty = new HubSpotObject<>();
            customProperty.setId(account.getCrmId());
            customProperty.setProperties(propertiesMap);
            updateRequests.add(customProperty);
        }

        LOGGER.info("Updating account custom fields for {}: {}", account.getAccountId(), updateRequests);
        if (CollectionUtils.isNotEmpty(updateRequests)) {
            try {
                hubSpotClient.updateObjects(HubSpotCompanyProperties.API_NAME, updateRequests);
            } catch (IOException e) {
                String message = String.format(
                    "Failed to update HubSpot company custom fields for account %s CRM ID %s",
                    account.getAccountId(),
                    account.getCrmId()
                );
                LOGGER.info(message, e);
                errors.add(message);
            }
        }
    }

    public Account createAccountForHubSpotCompanyIfNeeded(HubSpotCompany company) {
        Optional<Account> existingAccount = accountGetService.getAccountByCrmId(company.getId());
        if (existingAccount.isPresent()) {
            return existingAccount.get();
        }

        Account account = convertAccountForHubSpotCompany(company);

        Account savedAccount;
        if (companyHasRequiredAddressFields(company)) {
            AccountAddress address = hubSpotContactMapper.accountAddressFromHubSpotCompanyDetails(company.getProperties());
            address.setCountry(CountryCode.getCountryCodeFromCountryName(company.getProperties().getCountry(), true));
            savedAccount = accountService.upsertAccount(account, address);
        } else {
            savedAccount = accountService.upsertAccount(account);
        }
        return savedAccount;
    }

    private void syncCustomFieldsForOrderToHubSpot(
        HubSpotClient hubSpotClient,
        String orderQualifiedName,
        String hubSpotOrderId,
        String orderId,
        List<String> errors
    ) throws IOException {
        if (!hasEnterpriseHubspotIntegration() || StringUtils.isBlank(hubSpotOrderId)) {
            return;
        }

        Map<String, String> propertiesMap = new HashMap<>();
        if (featureService.isEnabled(Feature.CRM_FIELD_MAPPING)) {
            List<HubSpotProperty> hubSpotOrderProperties = hubSpotClient.getObjectProperties(orderQualifiedName);
            CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ORDER, orderId);
            Map<String, CustomFieldValue> entries = customField.getEntries();
            List<CustomFieldValue> customFieldsToSync = entries.values().stream().toList();

            List<CrmFieldMapping> outboundCrmFieldMappings = crmFieldMappingService
                .getCrmFieldMappings(CrmType.HUBSPOT, CrmObjectType.ORDER)
                .stream()
                .filter(m -> CrmSyncDirection.OUTBOUND == m.getDirection())
                .toList();

            for (var crmFieldMapping : outboundCrmFieldMappings) {
                // Check that the Subskribe custom field exists
                Optional<CustomFieldValue> customFieldValue = customFieldsToSync
                    .stream()
                    .filter(c -> StringUtils.equals(crmFieldMapping.getSubskribeFieldName(), c.getName()))
                    .findAny();
                if (customFieldValue.isEmpty()) {
                    errors.add(String.format("Subskribe custom field missing for Order: %s", crmFieldMapping.getSubskribeFieldName()));
                    continue;
                }
                CustomFieldValue cfValue = customFieldValue.get();

                // Check that the HubSpot property field exists
                Optional<HubSpotProperty> property = hubSpotOrderProperties
                    .stream()
                    .filter(p -> StringUtils.equalsIgnoreCase(crmFieldMapping.getCrmFieldName(), p.getName()))
                    .findAny();
                if (property.isEmpty()) {
                    errors.add(String.format("HubSpot field missing for Order: %s", crmFieldMapping.getCrmFieldName()));
                    continue;
                }
                String fieldType = property.get().getType();
                List<String> propertyOptions = property.get().getOptions().stream().map(HubSpotOption::getValue).toList();
                if (
                    StringUtils.equals(fieldType, HUBSPOT_PROPERTY_STRING_TYPE) || new HashSet<>(propertyOptions).containsAll(cfValue.getSelections())
                ) {
                    propertiesMap.put(property.get().getName(), formatHubSpotCustomFieldDisplay(cfValue));
                }
            }
            // Deprecate when crm field mapping is enabled for all tenants
        } else if (shouldSyncCustomFields(tenantIdProvider, billyConfiguration)) {
            List<String> hubSpotOrderPropertyNames = getHubSpotObjectPropertyNames(hubSpotClient, orderQualifiedName);
            Set<String> existingPropertyNames = new HashSet<>(hubSpotOrderPropertyNames);
            // Create the custom field definitions before syncing
            List<CustomFieldDefinition> customFieldDefinitions = customFieldService.getCustomFieldDefinitions(CustomFieldParentType.ORDER);
            createCustomFieldProperties(hubSpotClient, orderQualifiedName, customFieldDefinitions, existingPropertyNames);
            List<HubSpotProperty> hubSpotOrderProperties = hubSpotClient.getObjectProperties(orderQualifiedName);
            updateCustomFieldPropertiesIfNeeded(hubSpotClient, orderQualifiedName, customFieldDefinitions, hubSpotOrderProperties);

            CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ORDER, orderId);
            Map<String, CustomFieldValue> entries = customField.getEntries();
            List<CustomFieldValue> customFieldsToSync = entries.values().stream().toList();

            for (var customFieldValue : customFieldsToSync) {
                Optional<HubSpotProperty> property = hubSpotOrderProperties
                    .stream()
                    .filter(p -> StringUtils.equalsIgnoreCase(p.getName(), customFieldValue.getName()))
                    .findAny();
                if (property.isPresent()) {
                    List<String> propertyOptions = property.get().getOptions().stream().map(HubSpotOption::getValue).toList();
                    if (new HashSet<>(propertyOptions).containsAll(customFieldValue.getOptions())) {
                        propertiesMap.put(property.get().getName(), formatHubSpotCustomFieldDisplay(customFieldValue));
                    }
                }
            }
        }

        var updateRequests = new ArrayList<>();
        if (MapUtils.isNotEmpty(propertiesMap)) {
            HubSpotObject<Map<String, String>> customProperty = new HubSpotObject<>();
            customProperty.setId(hubSpotOrderId);
            customProperty.setProperties(propertiesMap);
            updateRequests.add(customProperty);
        }

        LOGGER.info("Updating order custom fields for {}: {}", orderId, updateRequests);
        if (CollectionUtils.isNotEmpty(updateRequests)) {
            try {
                hubSpotClient.updateObjects(orderQualifiedName, updateRequests);
            } catch (IOException e) {
                String message = String.format("Failed to update HubSpot order custom fields for order %s", orderId);
                LOGGER.info(message, e);
                errors.add(message);
            }
        }
    }

    private String formatHubSpotCustomFieldDisplay(CustomFieldValue value) {
        return switch (value.getType()) {
            case MULTISELECT_PICKLIST -> String.join(";", value.getSelections());
            case PICKLIST -> CollectionUtils.isEmpty(value.getSelections()) ? "" : value.getSelections().get(0);
            case STRING -> value.getValue();
        };
    }

    private void updateCustomFieldPropertiesIfNeeded(
        HubSpotClient hubSpotClient,
        String objectQualifiedName,
        List<CustomFieldDefinition> customFieldDefinitions,
        List<HubSpotProperty> existingProperties
    ) throws IOException {
        AtomicInteger displayCount = new AtomicInteger(0);

        for (var customFieldDefinition : customFieldDefinitions) {
            String customFieldPropertyName = customFieldDefinition.getFieldName().toLowerCase();
            if (propertyShouldBeUpdated(customFieldDefinition, existingProperties)) {
                String hubSpotPropertyType =
                    switch (customFieldDefinition.getFieldType()) {
                        case PICKLIST, MULTISELECT_PICKLIST -> HUBSPOT_PROPERTY_ENUMERATION_TYPE;
                        default -> HUBSPOT_PROPERTY_STRING_TYPE;
                    };

                String hubSpotPropertyFieldType =
                    switch (customFieldDefinition.getFieldType()) {
                        case PICKLIST -> HUBSPOT_PROPERTY_SELECT_FIELD_TYPE;
                        case MULTISELECT_PICKLIST -> HUBSPOT_PROPERTY_CHECKBOX_FIELD_TYPE;
                        default -> HUBSPOT_PROPERTY_TEXT_FIELD_TYPE;
                    };

                List<HubSpotOption> options = customFieldDefinition
                    .getOptions()
                    .stream()
                    .map(option -> {
                        displayCount.getAndIncrement();
                        return new HubSpotOption(option, option, displayCount.get());
                    })
                    .toList();

                HubSpotCustomPropertyUpdate propertyUpdateRequest = ImmutableHubSpotCustomPropertyUpdate.builder()
                    .type(hubSpotPropertyType)
                    .fieldType(hubSpotPropertyFieldType)
                    .options(options)
                    .build();

                hubSpotClient.updateProperty(objectQualifiedName, customFieldPropertyName, propertyUpdateRequest);
                LOGGER.info("updated property: {}", customFieldPropertyName);
            }
        }
    }

    private boolean propertyShouldBeUpdated(CustomFieldDefinition customFieldDefinition, List<HubSpotProperty> existingProperties) {
        return existingProperties
            .stream()
            .filter(p -> StringUtils.equals(p.getName(), customFieldDefinition.getFieldName().toLowerCase()))
            .findAny()
            .map(hubSpotProperty ->
                switch (customFieldDefinition.getFieldType()) {
                    case STRING -> !StringUtils.equals(hubSpotProperty.getType(), HUBSPOT_PROPERTY_STRING_TYPE);
                    case PICKLIST -> !StringUtils.equals(hubSpotProperty.getType(), HUBSPOT_PROPERTY_ENUMERATION_TYPE) ||
                    !StringUtils.equals(hubSpotProperty.getFieldType(), HUBSPOT_PROPERTY_SELECT_FIELD_TYPE) ||
                    !hasMatchingOptions(hubSpotProperty, customFieldDefinition);
                    case MULTISELECT_PICKLIST -> !StringUtils.equals(hubSpotProperty.getType(), HUBSPOT_PROPERTY_ENUMERATION_TYPE) ||
                    !StringUtils.equals(hubSpotProperty.getFieldType(), HUBSPOT_PROPERTY_CHECKBOX_FIELD_TYPE) ||
                    !hasMatchingOptions(hubSpotProperty, customFieldDefinition);
                }
            )
            .orElse(false);
    }

    private boolean hasMatchingOptions(HubSpotProperty hubSpotProperty, CustomFieldDefinition customFieldDefinition) {
        return CollectionUtils.isEqualCollection(
            hubSpotProperty.getOptions().stream().map(HubSpotOption::getValue).toList(),
            customFieldDefinition.getOptions()
        );
    }

    private void createCustomFieldProperties(
        HubSpotClient hubSpotClient,
        String objectQualifiedName,
        List<CustomFieldDefinition> customFieldDefinitions,
        Set<String> existingProperties
    ) {
        List<HubSpotCustomObjectProperties> properties = new ArrayList<>();
        AtomicInteger displayCount = new AtomicInteger(0);

        for (var customFieldDefinition : customFieldDefinitions) {
            String customFieldPropertyName = customFieldDefinition.getFieldName().toLowerCase();
            if (!existingProperties.contains(customFieldPropertyName)) {
                String hubSpotPropertyType =
                    switch (customFieldDefinition.getFieldType()) {
                        case PICKLIST, MULTISELECT_PICKLIST -> HUBSPOT_PROPERTY_ENUMERATION_TYPE;
                        default -> HUBSPOT_PROPERTY_STRING_TYPE;
                    };

                String hubSpotPropertyFieldType =
                    switch (customFieldDefinition.getFieldType()) {
                        case PICKLIST -> HUBSPOT_PROPERTY_SELECT_FIELD_TYPE;
                        case MULTISELECT_PICKLIST -> HUBSPOT_PROPERTY_CHECKBOX_FIELD_TYPE;
                        default -> HUBSPOT_PROPERTY_TEXT_FIELD_TYPE;
                    };

                List<HubSpotOption> options = customFieldDefinition
                    .getOptions()
                    .stream()
                    .map(option -> {
                        displayCount.getAndIncrement();
                        return new HubSpotOption(option, option, displayCount.get());
                    })
                    .toList();

                properties.add(
                    new HubSpotCustomObjectProperties(
                        customFieldPropertyName,
                        SUBSKRIBE_GROUP_NAME,
                        false,
                        StringUtils.isNotBlank(customFieldDefinition.getFieldLabel())
                            ? customFieldDefinition.getFieldLabel()
                            : WordUtils.capitalizeFully(customFieldPropertyName),
                        false,
                        hubSpotPropertyType,
                        hubSpotPropertyFieldType,
                        false,
                        false,
                        options
                    )
                );
                LOGGER.info("created property: {}", customFieldPropertyName);
                existingProperties.add(customFieldPropertyName);
            }
        }
        hubSpotClient.addProperties(objectQualifiedName, properties);
    }

    private void syncCustomFieldsForOrderLinesToHubSpot(
        HubSpotClient hubSpotClient,
        String orderLineQualifiedName,
        List<HubSpotOrderLine> orderLines,
        List<OrderLineItem> lineItems,
        String orderId,
        List<String> errors
    ) throws IOException {
        if (!hasEnterpriseHubspotIntegration() || CollectionUtils.isEmpty(lineItems)) {
            return;
        }

        List<HubSpotProperty> hubSpotOrderLineProperties = hubSpotClient.getObjectProperties(orderLineQualifiedName);
        var updateRequests = new ArrayList<>();

        if (featureService.isEnabled(Feature.CRM_FIELD_MAPPING)) {
            List<CrmFieldMapping> outboundCrmFieldMappings = crmFieldMappingService
                .getCrmFieldMappings(CrmType.HUBSPOT, CrmObjectType.ORDER_ITEM)
                .stream()
                .filter(m -> CrmSyncDirection.OUTBOUND == m.getDirection())
                .toList();

            for (var lineItem : lineItems) {
                Optional<HubSpotOrderLine> match = orderLines
                    .stream()
                    .filter(l -> StringUtils.isNotBlank(l.getProperties().getUUID()))
                    .filter(l -> l.getProperties().getUUID().equals(lineItem.getOrderLineId()))
                    .findAny();
                if (match.isEmpty()) {
                    LOGGER.info("Could not find a line item for {} on HubSpot", lineItem.getChargeId());
                    continue;
                }

                CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ORDER_ITEM, lineItem.getOrderLineId());
                Map<String, CustomFieldValue> entries = customField.getEntries();
                List<CustomFieldValue> customFieldsToSync = entries.values().stream().toList();

                Map<String, String> propertiesMap = new HashMap<>();
                for (var crmFieldMapping : outboundCrmFieldMappings) {
                    // Check that the Subskribe custom field exists
                    Optional<CustomFieldValue> customFieldValue = customFieldsToSync
                        .stream()
                        .filter(c -> StringUtils.equals(crmFieldMapping.getSubskribeFieldName(), c.getName()))
                        .findAny();
                    if (customFieldValue.isEmpty()) {
                        errors.add(String.format("Subskribe custom field missing for Order Item: %s", crmFieldMapping.getSubskribeFieldName()));
                        continue;
                    }
                    CustomFieldValue cfValue = customFieldValue.get();

                    // Check that the HubSpot property field exists
                    Optional<HubSpotProperty> property = hubSpotOrderLineProperties
                        .stream()
                        .filter(p -> StringUtils.equals(crmFieldMapping.getCrmFieldName(), p.getName()))
                        .findAny();
                    if (property.isEmpty()) {
                        errors.add(String.format("HubSpot field missing for Order Item: %s", crmFieldMapping.getCrmFieldName()));
                        continue;
                    }
                    String fieldType = property.get().getType();
                    List<String> propertyOptions = property.get().getOptions().stream().map(HubSpotOption::getValue).toList();
                    if (
                        StringUtils.equals(fieldType, HUBSPOT_PROPERTY_STRING_TYPE) ||
                        new HashSet<>(propertyOptions).containsAll(cfValue.getSelections())
                    ) {
                        propertiesMap.put(property.get().getName(), formatHubSpotCustomFieldDisplay(cfValue));
                    }
                }

                if (MapUtils.isNotEmpty(propertiesMap)) {
                    HubSpotObject<Map<String, String>> customProperty = new HubSpotObject<>();
                    customProperty.setId(match.get().getId());
                    customProperty.setProperties(propertiesMap);
                    updateRequests.add(customProperty);
                }
            }
        } else if (shouldSyncCustomFields(tenantIdProvider, billyConfiguration)) {
            Set<String> existingPropertyNames = hubSpotOrderLineProperties.stream().map(HubSpotProperty::getName).collect(Collectors.toSet());
            // Create the custom field definitions before syncing
            List<CustomFieldDefinition> customFieldDefinitions = customFieldService.getCustomFieldDefinitions(CustomFieldParentType.ORDER_ITEM);
            createCustomFieldProperties(hubSpotClient, orderLineQualifiedName, customFieldDefinitions, existingPropertyNames);
            updateCustomFieldPropertiesIfNeeded(hubSpotClient, orderLineQualifiedName, customFieldDefinitions, hubSpotOrderLineProperties);

            for (var lineItem : lineItems) {
                Optional<HubSpotOrderLine> match = orderLines
                    .stream()
                    .filter(l -> StringUtils.isNotBlank(l.getProperties().getUUID()))
                    .filter(l -> l.getProperties().getUUID().equals(lineItem.getOrderLineId()))
                    .findAny();
                if (match.isEmpty()) {
                    LOGGER.info("Could not find a line item for {} on HubSpot", lineItem.getChargeId());
                    continue;
                }

                CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ORDER_ITEM, lineItem.getOrderLineId());
                Map<String, CustomFieldValue> entries = customField.getEntries();
                List<CustomFieldValue> customFieldsToSync = entries.values().stream().toList();

                Map<String, String> propertiesMap = new HashMap<>();
                for (var customFieldValue : customFieldsToSync) {
                    Optional<HubSpotProperty> property = hubSpotOrderLineProperties
                        .stream()
                        .filter(p -> StringUtils.equalsIgnoreCase(p.getName(), customFieldValue.getName()))
                        .findAny();
                    if (property.isPresent()) {
                        String fieldType = property.get().getType();
                        List<String> propertyOptions = property.get().getOptions().stream().map(HubSpotOption::getValue).toList();
                        if (
                            StringUtils.equals(fieldType, HUBSPOT_PROPERTY_STRING_TYPE) ||
                            new HashSet<>(propertyOptions).containsAll(customFieldValue.getSelections())
                        ) {
                            propertiesMap.put(property.get().getName(), formatHubSpotCustomFieldDisplay(customFieldValue));
                        }
                    }
                }

                if (MapUtils.isNotEmpty(propertiesMap)) {
                    HubSpotObject<Map<String, String>> customProperty = new HubSpotObject<>();
                    customProperty.setId(match.get().getId());
                    customProperty.setProperties(propertiesMap);
                    updateRequests.add(customProperty);
                }
            }
        }

        LOGGER.info("Updating order line custom fields for {}: {}", orderId, updateRequests);
        if (CollectionUtils.isNotEmpty(updateRequests)) {
            try {
                hubSpotClient.updateObjects(orderLineQualifiedName, updateRequests);
            } catch (IOException e) {
                String message = String.format("Failed to update HubSpot order line custom fields for order %s", orderId);
                LOGGER.info(message, e);
                errors.add(message);
            }
        }
    }

    public void syncCustomFieldsForAccount(String accountCrmId, String accountId) {
        if (featureService.isEnabled(Feature.CRM_FIELD_MAPPING)) {
            List<CrmFieldMapping> inboundCrmFieldMappings = crmFieldMappingService
                .getCrmFieldMappings(CrmType.HUBSPOT, CrmObjectType.ACCOUNT)
                .stream()
                .filter(m -> CrmSyncDirection.INBOUND == m.getDirection())
                .toList();

            List<String> hubspotProperties = inboundCrmFieldMappings.stream().map(CrmFieldMapping::getCrmFieldName).toList();

            // No custom field mappings for HubSpot Company -> Subskribe Account
            if (CollectionUtils.isEmpty(hubspotProperties)) {
                return;
            }

            // Collect CRM custom field values in a map
            HubSpotClient hubSpotClient = hubspotClientFactory.forTenantIntegration();
            HubSpotCustomProperties hubSpotCompany = hubSpotClient.getHubSpotObjectWithProperties(
                HubSpotCompanyProperties.API_NAME,
                accountCrmId,
                hubspotProperties
            );
            Map<String, String> crmCustomFields = new HashMap<>();
            for (var crmFieldMapping : inboundCrmFieldMappings) {
                if (hubSpotCompany.properties().containsKey(crmFieldMapping.getCrmFieldName())) {
                    String customFieldName = crmFieldMapping.getSubskribeFieldName();
                    String sourceValue = hubSpotCompany.properties().get(crmFieldMapping.getCrmFieldName());
                    crmCustomFields.put(customFieldName, sourceValue);
                } else {
                    LOGGER.info("HubSpot Company property missing: {}", crmFieldMapping.getCrmFieldName());
                }
            }

            CustomField accountCustomFields = customFieldService.getCustomFields(CustomFieldParentType.ACCOUNT, accountId);

            Map<String, CustomFieldValue> updateAccountCustomFieldEntries = new HashMap<>();
            accountCustomFields
                .getEntries()
                .forEach((customFieldId, customFieldValue) -> {
                    String customFieldName = customFieldValue.getName();
                    String sourceValue = crmCustomFields.get(customFieldName);

                    // Check that CRM custom field value is not null and Subskribe has the custom field
                    if (sourceValue != null) {
                        String updatedValue = sourceValue;
                        List<String> updatedSelections =
                            switch (customFieldValue.getType()) {
                                case STRING -> List.of();
                                case PICKLIST -> List.of(sourceValue);
                                case MULTISELECT_PICKLIST -> Arrays.stream(sourceValue.split(";")).map(String::trim).toList();
                            };

                        // Check that the selections are a subset of the available custom field options
                        // If the selections are not a subset, then use the original custom field value and selections
                        if (!new HashSet<>(customFieldValue.getOptions()).containsAll(updatedSelections)) {
                            updatedValue = customFieldValue.getValue();
                            updatedSelections = customFieldValue.getSelections();
                        }
                        CustomFieldValue updatedAccountCustomField = new CustomFieldValue(
                            customFieldValue.getType(),
                            customFieldValue.getName(),
                            customFieldValue.getLabel(),
                            updatedValue,
                            updatedSelections,
                            customFieldValue.getOptions(),
                            customFieldValue.isRequired(),
                            customFieldValue.getSource(),
                            customFieldValue.getDefaultValue()
                        );
                        updateAccountCustomFieldEntries.put(customFieldId, updatedAccountCustomField);
                    } else {
                        updateAccountCustomFieldEntries.put(customFieldId, customFieldValue);
                    }
                });

            customFieldService.setCustomFieldsBySystem(CustomFieldParentType.ACCOUNT, accountId, new CustomField(updateAccountCustomFieldEntries));
            // Remove this when mapping is available for EP
        } else if (shouldSyncCustomFields(tenantIdProvider, billyConfiguration)) {
            List<String> hubspotProperties = EDUCATION_PERFECT_COMPANY_FIELD_MAPPING.keySet().stream().toList();
            HubSpotClient hubSpotClient = hubspotClientFactory.forTenantIntegration();
            HubSpotCustomProperties hubSpotCompany = hubSpotClient.getHubSpotObjectWithProperties(
                HubSpotCompanyProperties.API_NAME,
                accountCrmId,
                hubspotProperties
            );
            for (var hubspotProperty : hubspotProperties) {
                String customFieldName = EDUCATION_PERFECT_COMPANY_FIELD_MAPPING.get(hubspotProperty);
                String sourceValue = hubSpotCompany.properties().get(hubspotProperty);
                putCustomField(customFieldName, accountId, sourceValue);
            }
        }
    }

    private CustomFieldDefinition getCustomFieldDefinition(String fieldName) {
        Collection<String> epAccountFields = EDUCATION_PERFECT_COMPANY_FIELD_MAPPING.values();
        if (epAccountFields.contains(fieldName)) {
            return new CustomFieldDefinition(
                null,
                null,
                CustomFieldParentType.ACCOUNT,
                CustomFieldType.STRING,
                fieldName,
                null,
                List.of(),
                false,
                null,
                CustomFieldSource.SYSTEM,
                null,
                null
            );
        }
        return switch (fieldName) {
            case HubSpotCompanyProperties.COMPANY_SIZE_API_NAME -> new CustomFieldDefinition(
                null,
                null,
                CustomFieldParentType.ACCOUNT,
                CustomFieldType.STRING,
                fieldName,
                null,
                List.of(),
                false,
                null,
                CustomFieldSource.SYSTEM,
                null,
                null
            );
            case HubSpotDealProperties.LEAD_SOURCE_API_NAME,
                HubSpotDealProperties.DEAL_OWNER_API_NAME,
                HubSpotDealProperties.SALES_ENGINEER_API_NAME -> new CustomFieldDefinition(
                null,
                null,
                CustomFieldParentType.ORDER,
                CustomFieldType.STRING,
                fieldName,
                null,
                List.of(),
                false,
                null,
                CustomFieldSource.SYSTEM,
                null,
                null
            );
            default -> throw new IllegalArgumentException("invalid field name: " + fieldName);
        };
    }

    private void putCustomField(String fieldName, String objectId, String value) {
        var definition = getCustomFieldDefinition(fieldName);
        CustomFieldDefinition fieldDefinition = customFieldService.getOrCreateSystemCustomFieldDefinition(definition);
        customFieldService.putStringValue(fieldDefinition, objectId, value, CustomFieldSource.SYSTEM);
    }

    private Account convertAccountForHubSpotCompany(HubSpotCompany company) {
        Account account = new Account();
        account.setName(company.getProperties().getName());
        String description = StringUtils.truncate(company.getProperties().getDescription(), Account.MAX_DESCRIPTION_LENGTH);
        account.setDescription(description);
        account.setPhoneNumber(company.getProperties().getPhone());
        account.setCurrency(SupportedCurrency.getDefaultCurrency());
        account.setCrmId(company.getId());
        account.setCrmType(CrmType.HUBSPOT);
        // TODO: Take entity ids as input from a custom Hubspot property
        account.setEntityIds(Set.of(EntityContext.ALL_ACCESS_ID));
        return account;
    }

    public void syncOrderToHubSpot(String orderId) throws IOException {
        LOGGER.debug("start of order sync: {}", orderId);
        String tenantId = tenantIdProvider.provideTenantIdString();
        if (!hubSpotIntegrationService.hasCompletedHubSpotIntegration(tenantId)) {
            return;
        }

        List<String> errors = new ArrayList<>();

        HubSpotIntegration hubSpotIntegration = hubSpotIntegrationService.getIntegrationForTenant();
        HubSpotClient hubSpotClient = hubspotClientFactory.fromHubspotIntegration(hubSpotIntegration);

        List<HubSpotProperty> dealProperties = hubSpotClient.getObjectProperties(HubSpotDealProperties.API_NAME);
        List<String> hubSpotDealPropertyNames = dealProperties.stream().map(HubSpotProperty::getName).toList();
        boolean isUtcDate = checkIfUtcDateType(dealProperties);
        Set<String> hubSpotCurrencies = hubSpotGetService.getHubSpotCurrencies(hubSpotClient);
        List<String> customObjectSchemaNames = hubSpotClient.getCustomObjectSchemaList().stream().map(HubSpotCustomObjectSchema::getName).toList();

        try (LockItem ignored = waitForLockOrThrow(orderId)) {
            Order order;
            try {
                order = orderGetService.getOrderByOrderId(orderId);
            } catch (ObjectNotFoundException e) {
                LOGGER.info("Skipping HubSpot deal sync. {} not found.", orderId);
                return;
            }

            if (StringUtils.isBlank(order.getSfdcOpportunityId())) {
                return;
            }

            syncSwitchedOpportunities(order);
            Metrics orderMetrics = metricsService.getOrderMetrics(orderId, Instant.now());
            Opportunity opportunity = opportunityGetService.getOpportunityByCrmOpportunityId(order.getSfdcOpportunityId());

            if (order.getIsPrimaryOrderForSfdcOpportunity()) {
                pushHubSpotDealPropertiesForOrder(
                    hubSpotClient,
                    order,
                    opportunity,
                    orderMetrics,
                    hubSpotDealPropertyNames,
                    hubSpotCurrencies,
                    isUtcDate
                );
            }
            if (!isBasic(tenantIdProvider, billyConfiguration) && customObjectSchemaNames.contains(HubSpotOrderProperties.API_NAME)) {
                HubSpotObject<HubSpotOrderProperties> hubSpotOrder = pushOrder(
                    hubSpotClient,
                    hubSpotIntegration,
                    order,
                    orderMetrics,
                    opportunity,
                    isUtcDate,
                    customObjectSchemaNames,
                    errors
                );
                OrderDetail orderDetail = orderDataAggregator.getOrderDetail(order, true, true);
                List<OrderLineItem> lineItems = orderDetailsMapper.orderLineItemDetailsToLineItems(orderDetail.getLineItemsNetEffect());
                if (customObjectSchemaNames.contains(HubSpotOrderLineProperties.API_NAME)) {
                    List<HubSpotOrderLine> hubSpotOrderLines = pushOrderLines(
                        hubSpotClient,
                        hubSpotIntegration,
                        hubSpotOrder,
                        order,
                        lineItems,
                        isUtcDate,
                        errors
                    );
                    if (featureService.isEnabled(Feature.CRM_RATE_CARD)) {
                        pushRateCardsForOrderLines(hubSpotClient, hubSpotIntegration, lineItems, hubSpotOrderLines, customObjectSchemaNames);
                    }
                }
                verifyPostedOrder(hubSpotClient, hubSpotIntegration, hubSpotOrder);
            }
        }
        LOGGER.debug("end of order sync: {}", orderId);
        throwIfSyncErrorsAreFound(errors);
    }

    private void pushRateCardsForOrderLines(
        HubSpotClient hubSpotClient,
        HubSpotIntegration hubSpotIntegration,
        List<OrderLineItem> lineItems,
        List<HubSpotOrderLine> hubSpotOrderLines,
        List<String> customObjectSchemaNames
    ) {
        if (!customObjectSchemaNames.contains(HubSpotRateCardProperties.API_NAME)) {
            LOGGER.info("Rate card custom object was not found on HubSpot for tenant: {}", hubSpotIntegration.getTenantId());
            return;
        }

        String orderLineQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderLineProperties.API_NAME);
        String rateCardQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotRateCardProperties.API_NAME);
        List<String> hubSpotRateCardProperties = getHubSpotObjectPropertyNames(hubSpotClient, rateCardQualifiedName);
        Optional<HubSpotObjectAssociation> associationOptional = hubSpotGetService.getHubSpotObjectAssociationId(
            hubSpotClient,
            rateCardQualifiedName,
            orderLineQualifiedName
        );

        // No association between Order and Order Lines
        if (associationOptional.isEmpty()) {
            return;
        }

        List<HubSpotObject<HubSpotRateCardProperties>> rateCardsToSync = new ArrayList<>();

        for (var lineItem : lineItems) {
            Optional<HubSpotOrderLine> match = hubSpotOrderLines
                .stream()
                .filter(hubSpotOrderLine -> StringUtils.equals(hubSpotOrderLine.getProperties().getUUID(), lineItem.getOrderLineId()))
                .findAny();

            List<AttributeReference> attributeReferences = lineItem.getAttributeReferences();

            if (match.isEmpty() || CollectionUtils.isEmpty(attributeReferences)) {
                continue;
            }

            for (var attributeReference : attributeReferences) {
                Optional<PriceAttribute> priceAttributeOptional = rateCardService.getPriceAttributeById(
                    attributeReference.getAttributeDefinitionId()
                );
                if (priceAttributeOptional.isEmpty()) {
                    continue;
                }

                Charge charge = productCatalogGetService.getChargeByChargeId(lineItem.getChargeId());
                HubSpotOrderLine hubSpotOrderLine = match.get();

                HubSpotRateCardProperties rateCardProperties = prepareRateCard(
                    charge.getRateCardId(),
                    hubSpotOrderLine.getProperties().getDealId(),
                    hubSpotOrderLine.getProperties().getOrderId(),
                    lineItem.getOrderLineId(),
                    null,
                    null,
                    attributeReference,
                    priceAttributeOptional.get(),
                    hubSpotRateCardProperties
                );
                AssociatedId targetHubSpotId = new AssociatedId(match.get().getId());
                HubSpotObjectAssociation associationType = new HubSpotObjectAssociation(
                    USER_DEFINED_ASSOCIATION,
                    associationOptional.get().getTypeId()
                );
                HubSpotBatchCustomObjectRequestAssociation batchAssociation = new HubSpotBatchCustomObjectRequestAssociation(
                    targetHubSpotId,
                    List.of(associationType)
                );
                rateCardsToSync.add(new HubSpotObject<>(rateCardProperties, List.of(batchAssociation)));
            }
        }

        LOGGER.info("Syncing rate cards to HubSpot for", rateCardsToSync);
        hubSpotClient.postMany(rateCardQualifiedName, rateCardsToSync);
    }

    private HubSpotRateCardProperties prepareRateCard(
        String rateCardId,
        String dealId,
        String orderId,
        String orderLineId,
        String subscriptionId,
        String subscriptionChargeId,
        AttributeReference attributeReference,
        PriceAttribute priceAttribute,
        List<String> hubSpotRateCardProperties
    ) {
        HubSpotRateCardProperties properties = new HubSpotRateCardProperties();

        if (hubSpotRateCardProperties.contains(HubSpotRateCardProperties.PRICE_ATTRIBUTE_NAME_API_NAME)) {
            properties.setPriceAttributeName(priceAttribute.getName());
        }
        if (hubSpotRateCardProperties.contains(HubSpotRateCardProperties.PRICE_ATTRIBUTE_VALUE_API_NAME)) {
            properties.setPriceAttributeValue(attributeReference.getAttributeValue());
        }
        if (hubSpotRateCardProperties.contains(HubSpotRateCardProperties.PRICE_ATTRIBUTE_ID_API_NAME)) {
            properties.setPriceAttributeId(attributeReference.getAttributeDefinitionId());
        }
        if (hubSpotRateCardProperties.contains(HubSpotRateCardProperties.RATE_CARD_ID_API_NAME)) {
            properties.setRateCardId(rateCardId);
        }
        if (hubSpotRateCardProperties.contains(HubSpotRateCardProperties.DEAL_ID_API_NAME)) {
            properties.setDealId(dealId);
        }
        if (hubSpotRateCardProperties.contains(HubSpotRateCardProperties.ORDER_ID_API_NAME)) {
            properties.setOrderId(orderId);
        }
        if (hubSpotRateCardProperties.contains(HubSpotRateCardProperties.ORDER_LINE_ID_API_NAME)) {
            properties.setOrderLineId(orderLineId);
        }
        if (hubSpotRateCardProperties.contains(HubSpotRateCardProperties.SUBSCRIPTION_ID_API_NAME)) {
            properties.setSubscriptionId(subscriptionId);
        }
        if (hubSpotRateCardProperties.contains(HubSpotRateCardProperties.SUBSCRIPTION_CHARGE_ID_API_NAME)) {
            properties.setSubscriptionChargeId(subscriptionChargeId);
        }
        return properties;
    }

    private void verifyPostedOrder(
        HubSpotClient hubSpotClient,
        HubSpotIntegration hubSpotIntegration,
        HubSpotObject<HubSpotOrderProperties> postedOrder
    ) {
        String orderQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderProperties.API_NAME);
        Optional<String> result = hubSpotClient.verifyOrderRecordWithRetries(orderQualifiedName, postedOrder.getId());

        if (result.isPresent()) {
            LOGGER.info("Created HubSpot order: {}", postedOrder.getId());
        }
    }

    // Handles the case where the order is switched to a different opportunity
    private void syncSwitchedOpportunities(Order order) {
        List<Opportunity> opportunities = opportunityGetService.getOpportunitiesByOrderId(order.getOrderId());
        String linkedOpportunityCrmId = order.getSfdcOpportunityId();

        // Clean up unlinked opportunities
        List<Opportunity> switchedOpportunities = opportunities
            .stream()
            .filter(opportunity -> !opportunity.getCrmId().equals(linkedOpportunityCrmId))
            .toList();
        if (CollectionUtils.isNotEmpty(switchedOpportunities)) {
            for (Opportunity opportunity : switchedOpportunities) {
                switchHubSpotDeal(opportunity);
            }
        }
    }

    // Sync next primary order for this opportunity or delete it if it is orphaned
    private void switchHubSpotDeal(Opportunity opportunity) {
        opportunityService.deleteOrphanOpportunityByCrmIdIfNeeded(opportunity.getCrmId());
        List<Order> orders = orderGetService.getOrdersByCrmOpportunityId(opportunity.getCrmId());
        if (CollectionUtils.isEmpty(orders)) {
            resetDeal(opportunity.getCrmId());
            return;
        }
        Order nextPrimaryOrder = orders.stream().max(Comparator.comparing(Order::getCreatedOn)).orElseThrow();
        opportunityService.updatePrimaryOrderIdForOpportunity(nextPrimaryOrder);
        hubSpotJobQueueService.dispatchOrderSync(nextPrimaryOrder);
    }

    public void pushAnnualAmountsForOrder(String orderId) {
        if (!hasEnterpriseHubspotIntegration()) {
            return;
        }

        HubSpotClient hubSpotClient = hubspotClientFactory.forTenantIntegration();
        HubSpotIntegration hubSpotIntegration = hubSpotIntegrationService.getIntegrationForTenant();

        if (!customObjectSchemaNameIsPresent(hubSpotClient, hubSpotIntegration, HubSpotOrderAnnualAmountProperties.API_NAME)) {
            return;
        }

        List<HubSpotProperty> dealProperties = hubSpotClient.getObjectProperties(HubSpotDealProperties.API_NAME);
        boolean isUtcDate = checkIfUtcDateType(dealProperties);

        try (LockItem ignored = waitForLockOrThrow(orderId)) {
            Order order;
            try {
                order = orderGetService.getOrderByOrderId(orderId);
            } catch (ObjectNotFoundException e) {
                LOGGER.info("Skipping HubSpot annual amount sync. {} not found.", orderId);
                return;
            }

            if (StringUtils.isBlank(order.getSfdcOpportunityId())) {
                return;
            }

            Opportunity opportunity = opportunityGetService.getOpportunityByCrmOpportunityId(order.getSfdcOpportunityId());
            if (!StringUtils.equals(opportunity.getPrimaryOrderId(), order.getOrderId())) {
                return;
            }

            LOGGER.info("Syncing order annual amounts to HubSpot for order: {}", orderId);

            String parentDealId = opportunity.getCrmId();

            // Checks if this Deal is part of a merged Deal
            Optional<String> parentDealIdOptional = hubSpotGetService.getParentDealIdForMergedDealId(hubSpotClient, order.getSfdcOpportunityId());
            if (parentDealIdOptional.isPresent() && !StringUtils.equals(parentDealIdOptional.get(), order.getSfdcOpportunityId())) {
                LOGGER.info("Merged deal found. Parent Deal ID: {}, Child Deal ID: {}", parentDealIdOptional.get(), order.getSfdcOpportunityId());
                parentDealId = parentDealIdOptional.get();
            }

            Map<Period, BigDecimal> annualAmounts = metricsService.getOrderAnnualTotal(order.getOrderId());

            String orderAnnualAmountQualifiedName = hubSpotGetService.getFullyQualifiedName(
                hubSpotIntegration,
                HubSpotOrderAnnualAmountProperties.API_NAME
            );

            List<String> existingOrderAnnualAmountIds = hubSpotGetService.getAssociatedObjectIds(
                hubSpotClient,
                HubSpotDealProperties.API_NAME,
                parentDealId,
                orderAnnualAmountQualifiedName
            );
            LOGGER.info("Archiving order annual amounts: {}", existingOrderAnnualAmountIds);
            hubSpotClient.archiveMany(orderAnnualAmountQualifiedName, existingOrderAnnualAmountIds);
            createAnnualAmounts(hubSpotClient, order, parentDealId, annualAmounts, orderAnnualAmountQualifiedName, isUtcDate);
        }
    }

    private void createAnnualAmounts(
        HubSpotClient hubSpotClient,
        Order order,
        String dealId,
        Map<Period, BigDecimal> annualAmounts,
        String orderAnnualAmountQualifiedName,
        boolean isUtcDate
    ) {
        Optional<HubSpotObjectAssociation> associationOptional = hubSpotGetService.getHubSpotObjectAssociationId(
            hubSpotClient,
            orderAnnualAmountQualifiedName,
            HubSpotDealProperties.API_NAME
        );

        // No association between Annual Amounts and Deal
        if (associationOptional.isEmpty()) {
            return;
        }

        List<HubSpotObject<HubSpotOrderAnnualAmountProperties>> annualAmountRequests = prepareAnnualAmountRequests(
            order,
            annualAmounts,
            associationOptional.get().getTypeId(),
            dealId,
            isUtcDate
        );
        LOGGER.info("Creating order annual amounts: {}", annualAmountRequests);
        hubSpotClient.postAnnualAmounts(orderAnnualAmountQualifiedName, annualAmountRequests);
    }

    private List<HubSpotObject<HubSpotOrderAnnualAmountProperties>> prepareAnnualAmountRequests(
        Order order,
        Map<Period, BigDecimal> annualAmounts,
        int annualAmountAssociationId,
        String targetDealHubSpotId,
        boolean isUtcDate
    ) {
        var timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        List<HubSpotObject<HubSpotOrderAnnualAmountProperties>> preparedAnnualAmounts = new ArrayList<>();
        for (Map.Entry<Period, BigDecimal> annualAmount : annualAmounts.entrySet()) {
            HubSpotOrderAnnualAmountProperties annualAmountProperties = prepareAnnualAmount(
                order,
                annualAmount.getKey(),
                annualAmount.getValue(),
                timeZone,
                isUtcDate
            );

            AssociatedId targetHubSpotId = new AssociatedId(targetDealHubSpotId);

            HubSpotObjectAssociation associationType = new HubSpotObjectAssociation(USER_DEFINED_ASSOCIATION, annualAmountAssociationId);

            HubSpotBatchCustomObjectRequestAssociation batchAssociation = new HubSpotBatchCustomObjectRequestAssociation(
                targetHubSpotId,
                List.of(associationType)
            );

            preparedAnnualAmounts.add(new HubSpotObject<>(annualAmountProperties, List.of(batchAssociation)));
        }
        return preparedAnnualAmounts;
    }

    private HubSpotOrderAnnualAmountProperties prepareAnnualAmount(
        Order order,
        Period period,
        BigDecimal amount,
        TimeZone timeZone,
        boolean isUtcDate
    ) {
        HubSpotOrderAnnualAmountProperties properties = new HubSpotOrderAnnualAmountProperties();

        HubSpotFormatter dateFormatter = new HubSpotFormatter(timeZone);

        String startDateString = dateFormatter.dateFormat(period.getStart());
        String endDateString = dateFormatter.endDateFormat(period.getEnd());

        properties.setName(String.format(LINE_ITEM_TERM_FORMAT, startDateString, endDateString));
        properties.setYear(dateFormatter.parseYear(startDateString));
        properties.setAmount(hubSpotMapper.formatValue(amount));
        properties.setStartDate(formatDate(period.getStart(), timeZone, false, isUtcDate));
        properties.setEndDate(formatDate(period.getEnd(), timeZone, true, isUtcDate));
        properties.setCurrency(order.getCurrency().getCurrencyCode());

        return properties;
    }

    private List<String> getHubSpotObjectPropertyNames(HubSpotClient hubSpotClient, String objectLabel) {
        List<HubSpotProperty> objectPropertiesSearchResponse = hubSpotClient.getObjectProperties(objectLabel);
        return objectPropertiesSearchResponse.stream().map(HubSpotProperty::getName).toList();
    }

    private HubSpotObject<HubSpotOrderProperties> pushOrder(
        HubSpotClient hubSpotClient,
        HubSpotIntegration hubSpotIntegration,
        Order order,
        Metrics orderMetrics,
        Opportunity opportunity,
        boolean isUtcDate,
        List<String> customObjectSchemaNames,
        List<String> errors
    ) {
        String orderQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderProperties.API_NAME);
        String orderLineQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderLineProperties.API_NAME);

        Set<String> existingOrderLineIds = new HashSet<>();

        List<String> hubSpotOrderProperties = getHubSpotObjectPropertyNames(hubSpotClient, orderQualifiedName);
        HubSpotObject<HubSpotOrderProperties> preparedOrder = prepareOrder(order, orderMetrics, opportunity, hubSpotOrderProperties, isUtcDate);

        List<HubSpotOrder> existingOrders = hubSpotGetService.getExistingOrderRecords(hubSpotClient, opportunity.getCrmId(), order.getOrderId());
        Set<String> existingOrderIds = existingOrders.stream().map(HubSpotOrder::getId).collect(Collectors.toSet());

        if (customObjectSchemaNames.contains(HubSpotOrderLineProperties.API_NAME)) {
            for (var orderId : existingOrderIds) {
                List<String> orderLineIds = hubSpotGetService.getAssociatedObjectIds(
                    hubSpotClient,
                    orderQualifiedName,
                    orderId,
                    orderLineQualifiedName
                );
                existingOrderLineIds.addAll(orderLineIds);
            }
        }

        if (customObjectSchemaNames.contains(HubSpotRateCardProperties.API_NAME)) {
            Set<String> existingRateCardIds = new HashSet<>();
            String rateCardQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotRateCardProperties.API_NAME);
            for (var orderLineId : existingOrderLineIds) {
                List<String> rateCardIds = hubSpotGetService.getAssociatedObjectIds(
                    hubSpotClient,
                    orderLineQualifiedName,
                    orderLineId,
                    rateCardQualifiedName
                );
                existingRateCardIds.addAll(rateCardIds);
            }
            hubSpotClient.archiveMany(rateCardQualifiedName, existingRateCardIds.stream().toList());
        }

        hubSpotClient.archiveMany(orderLineQualifiedName, existingOrderLineIds.stream().toList());
        hubSpotClient.archiveMany(orderQualifiedName, existingOrderIds.stream().toList());

        // Recreate the order
        LOGGER.info("Syncing order to HubSpot for {}. Request: {}", order.getOrderId(), preparedOrder);
        HubSpotObject<HubSpotOrderProperties> createdOrder = hubSpotClient.createObject(orderQualifiedName, preparedOrder);
        createHubSpotObjectAssociation(
            hubSpotClient,
            HubSpotDealProperties.API_NAME,
            opportunity.getCrmId(),
            orderQualifiedName,
            createdOrder.getId()
        );

        try {
            syncCustomFieldsForOrderToHubSpot(hubSpotClient, orderQualifiedName, createdOrder.getId(), order.getOrderId(), errors);
        } catch (IOException e) {
            LOGGER.info("Failed to sync custom fields for order to HubSpot: {}", createdOrder.getId(), e);
        }
        return createdOrder;
    }

    private HubSpotObject<HubSpotOrderProperties> prepareOrder(
        Order order,
        Metrics orderMetrics,
        Opportunity opportunity,
        List<String> hubSpotOrderProperties,
        boolean isUtcDate
    ) {
        var timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
        String siteUrl = billyConfiguration.getSiteUrl();
        HubSpotObject<HubSpotOrderProperties> hubSpotOrder = new HubSpotObject<>();
        HubSpotOrderProperties properties = hubSpotMapper.fromOrderToProperties(order);
        properties.setOrderExecutionDate(formatDate(order.getExecutedOn(), timeZone, false, isUtcDate));
        properties = hubSpotMapper.updateOrderPropertiesFromMetrics(properties, orderMetrics);
        properties.setOpportunityLink(SubskribeUrlGenerator.getUiUrlForOpportunity(siteUrl, opportunity.getOpportunityId()));
        properties.setPrimaryOrderLink(SubskribeUrlGenerator.getUiUrlForOrder(siteUrl, order.getOrderId()));
        if (
            (order.getStatus() == OrderStatus.EXECUTED || order.getOrderType() == OrderType.AMENDMENT) &&
            StringUtils.isNotBlank(order.getExternalSubscriptionId())
        ) {
            properties.setSubscriptionLink(SubskribeUrlGenerator.getUiUrlForSubscription(siteUrl, order.getExternalSubscriptionId()));
        }
        properties.setName(getOrderName(timeZone, order));
        if (hubSpotOrderProperties.contains(HubSpotOrderProperties.AVERAGE_ACV_API_NAME)) {
            properties.setAverageAcv(hubSpotMapper.formatValue(metricsService.getOrderAverageACV(order)));
        }
        if (hubSpotOrderProperties.contains(HubSpotOrderProperties.CURRENCY_API_NAME)) {
            properties.setCurrency(order.getCurrency().getCurrencyCode());
        }
        if (hubSpotOrderProperties.contains(HubSpotOrderProperties.SUBSKRIBE_ORDER_AUTO_RENEW_API_NAME)) {
            properties.setSubskribeOrderAutoRenew(String.valueOf(order.getAutoRenew()));
        }
        if (hubSpotOrderProperties.contains(HubSpotOrderProperties.SUBSKRIBE_ORDER_DISCOUNT_TOTAL_API_NAME)) {
            properties.setSubskribeOrderDiscountTotal(
                String.valueOf(OrderDiscountService.getNetDiscountAmountCurrencyScaled(order.getLineItemsNetEffect(), chargeMap))
            );
        }
        if (hubSpotOrderProperties.contains(HubSpotOrderProperties.SUBSKRIBE_ORDER_DISCOUNT_PERCENT_API_NAME)) {
            properties.setSubskribeOrderDiscountPercent(
                String.valueOf(OrderDiscountService.calculateOrderLineNetDiscountPercent(order.getLineItemsNetEffect(), chargeMap))
            );
        }
        hubSpotOrder.setProperties(properties);
        return hubSpotOrder;
    }

    private String getOrderName(TimeZone timeZone, Order order) {
        HubSpotFormatter dateFormatter = new HubSpotFormatter(timeZone);
        String startDate = dateFormatter.dateFormat(order.getStartDate());
        String endDate = dateFormatter.endDateFormat(order.getEndDate());
        return String.format(ORDER_NAME_FORMAT, order.getOrderId(), startDate, endDate);
    }

    private List<HubSpotOrderLine> pushOrderLines(
        HubSpotClient hubSpotClient,
        HubSpotIntegration hubSpotIntegration,
        HubSpotObject<HubSpotOrderProperties> hubspotOrder,
        Order order,
        List<OrderLineItem> lineItems,
        boolean isUtcDate,
        List<String> errors
    ) {
        String orderLineQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderLineProperties.API_NAME);
        String orderQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderProperties.API_NAME);
        List<String> hubSpotOrderLineProperties = getHubSpotObjectPropertyNames(hubSpotClient, orderLineQualifiedName);

        List<HubSpotOrderLine> createdOrderLines = createOrderLines(
            hubSpotClient,
            order,
            hubspotOrder,
            lineItems,
            orderLineQualifiedName,
            orderQualifiedName,
            hubSpotOrderLineProperties,
            isUtcDate
        );
        LOGGER.info("created order lines: {}", createdOrderLines.stream().map(HubSpotOrderLine::getId).toList());

        try {
            syncCustomFieldsForOrderLinesToHubSpot(hubSpotClient, orderLineQualifiedName, createdOrderLines, lineItems, order.getOrderId(), errors);
        } catch (IOException e) {
            LOGGER.info(
                "Could not sync custom fields for order lines to HubSpot: {}",
                createdOrderLines.stream().map(HubSpotOrderLine::getId).toList(),
                e
            );
        }

        return createdOrderLines;
    }

    private List<HubSpotOrderLine> createOrderLines(
        HubSpotClient hubSpotClient,
        Order order,
        HubSpotObject<HubSpotOrderProperties> hubspotOrder,
        List<OrderLineItem> lineItems,
        String orderLineQualifiedName,
        String orderQualifiedName,
        List<String> hubSpotOrderLineProperties,
        boolean isUtcDate
    ) {
        Optional<HubSpotObjectAssociation> associationOptional = hubSpotGetService.getHubSpotObjectAssociationId(
            hubSpotClient,
            orderLineQualifiedName,
            orderQualifiedName
        );

        // No association between Order and Order Lines
        if (associationOptional.isEmpty()) {
            return List.of();
        }

        List<HubSpotObject<HubSpotOrderLineProperties>> orderLinesToSync = lineItems
            .stream()
            .filter(lineItem -> shouldSyncLineItem(order, lineItem))
            .map(lineItem -> {
                HubSpotOrderLineProperties orderLineProperties = prepareOrderLine(order, lineItem, hubSpotOrderLineProperties, isUtcDate);
                AssociatedId targetHubSpotId = new AssociatedId(hubspotOrder.getId());
                HubSpotObjectAssociation associationType = new HubSpotObjectAssociation(
                    USER_DEFINED_ASSOCIATION,
                    associationOptional.get().getTypeId()
                );
                HubSpotBatchCustomObjectRequestAssociation batchAssociation = new HubSpotBatchCustomObjectRequestAssociation(
                    targetHubSpotId,
                    List.of(associationType)
                );
                return new HubSpotObject<>(orderLineProperties, List.of(batchAssociation));
            })
            .toList();

        LOGGER.info("Syncing order lines to HubSpot for {}. Request: {}", order.getOrderId(), orderLinesToSync);
        return hubSpotClient.postOrderLines(orderLineQualifiedName, orderLinesToSync);
    }

    private boolean shouldSyncLineItem(Order order, OrderLineItem lineItem) {
        return order.getOrderType() != OrderType.AMENDMENT || lineItem.getAction() != ActionType.NONE;
    }

    private HubSpotOrderLineProperties prepareOrderLine(
        Order order,
        OrderLineItem orderLineItem,
        List<String> hubSpotOrderLineProperties,
        boolean isUtcDate
    ) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        HubSpotOrderLineProperties properties = hubSpotMapper.fromOrderLineItemToProperties(orderLineItem);
        LineItemMetrics metrics = metricsService.getMetricsForOrderLine(orderLineItem.getOrderLineId());
        properties.setArr(hubSpotMapper.formatValue(metrics.arr()));
        properties.setTcv(hubSpotMapper.formatValue(metrics.tcv()));

        Plan plan = productCatalogGetService.getPlan(orderLineItem.getPlanId());
        Product product = productCatalogGetService.getProduct(plan.getProductId());
        Charge charge = productCatalogGetService.getChargeByChargeId(orderLineItem.getChargeId());

        properties.setChargeName(charge.getName());
        properties.setPlanName(plan.getName());
        properties.setProductName(product.getName());
        properties.setName(String.format(HUBSPOT_LINE_ITEM_NAME_FORMAT, plan.getName(), charge.getName()));
        properties.setProductName(product.getName());
        if (Objects.nonNull(product.getProductCategory())) {
            properties.setProductCategory(product.getProductCategory().getName());
        }
        BigDecimal discount = Numbers.isZero(orderLineItem.getListAmount())
            ? BigDecimal.ZERO
            : Numbers.scaledPercent(orderLineItem.getDiscountAmount(), orderLineItem.getListAmount());
        properties.setDiscount(discount.stripTrailingZeros().toPlainString());
        properties.setAcv(hubSpotMapper.formatValue(metricsService.calculateOrderLineACV(order, orderLineItem)));
        if (order.getStatus() == OrderStatus.EXECUTED) {
            properties.setSubscriptionId(order.getExternalSubscriptionId());
        }
        if (hubSpotOrderLineProperties.contains(HubSpotOrderLineProperties.LINE_START_DATE_API_NAME)) {
            properties.setLineStartDate(formatDate(orderLineItem.getEffectiveDate(), timeZone, false, isUtcDate));
        }
        if (hubSpotOrderLineProperties.contains(HubSpotOrderLineProperties.LINE_END_DATE_API_NAME)) {
            properties.setLineEndDate(formatDate(orderLineItem.getEndDate(), timeZone, true, isUtcDate));
        }
        if (hubSpotOrderLineProperties.contains(HubSpotOrderLineProperties.UUID_API_NAME)) {
            properties.setUUID(orderLineItem.getOrderLineId());
        }
        if (hubSpotOrderLineProperties.contains(HubSpotOrderLineProperties.CURRENCY_API_NAME)) {
            properties.setCurrency(order.getCurrency().getCurrencyCode());
        }
        if (hubSpotOrderLineProperties.contains(HubSpotOrderLineProperties.PRODUCT_EXTERNAL_ID_API_NAME)) {
            properties.setProductExternalId(product.getExternalId());
        }
        if (hubSpotOrderLineProperties.contains(HubSpotOrderLineProperties.PLAN_EXTERNAL_ID_API_NAME)) {
            properties.setPlanExternalId(plan.getExternalId());
        }
        if (hubSpotOrderLineProperties.contains(HubSpotOrderLineProperties.CHARGE_EXTERNAL_ID_API_NAME)) {
            properties.setChargeExternalId(charge.getExternalId());
        }
        if (hubSpotOrderLineProperties.contains(HubSpotOrderLineProperties.ORDER_ID_API_NAME)) {
            properties.setOrderId(order.getOrderId());
        }
        if (hubSpotOrderLineProperties.contains(HubSpotOrderLineProperties.ITEM_CODE_API_NAME)) {
            properties.setItemCode(charge.getItemCode());
        }
        if (hubSpotOrderLineProperties.contains(HubSpotOrderLineProperties.DEAL_ID_API_NAME)) {
            properties.setDealId(order.getSfdcOpportunityId());
        }
        return properties;
    }

    private void pushHubSpotDealPropertiesForOrder(
        HubSpotClient hubSpotClient,
        Order order,
        Opportunity opportunity,
        Metrics orderMetrics,
        List<String> hubSpotDealProperties,
        Set<String> hubSpotCurrencies,
        boolean isUtcDate
    ) throws IOException {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
        HubSpotDealProperties propertiesBody = new HubSpotDealProperties();
        propertiesBody.setOrderId(order.getOrderId());
        propertiesBody.setSubskribePrimaryOrderLink(String.format(SUBSKRIBE_ORDER_LINK_FORMAT, billyConfiguration.getSiteUrl(), order.getOrderId()));
        propertiesBody.setSubskribeOrderStatus(StringUtils.capitalize(order.getStatus().name().toLowerCase()));
        propertiesBody.setSubskribeOpportunityLink(
            String.format(SUBSKRIBE_OPPORTUNITY_LINK_FORMAT, billyConfiguration.getSiteUrl(), opportunity.getOpportunityId())
        );
        propertiesBody.setSubskribeTermStartDate(formatDate(order.getStartDate(), timeZone, false, isUtcDate));
        propertiesBody.setSubskribeTermEndDate(formatDate(order.getEndDate(), timeZone, true, isUtcDate));
        propertiesBody.setOrderTotal(hubSpotMapper.formatValue(orderMetrics.getTcv()));
        propertiesBody.setEntryArr(hubSpotMapper.formatValue(orderMetrics.getEntryArr()));
        propertiesBody.setExitArr(hubSpotMapper.formatValue(orderMetrics.getExitArr()));
        if (hubSpotDealProperties.contains(HubSpotDealProperties.AVERAGE_ARR_API_NAME)) {
            propertiesBody.setAverageArr(hubSpotMapper.formatValue(orderMetrics.getAverageArr()));
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.DELTA_ARR_API_NAME)) {
            propertiesBody.setDeltaArr(hubSpotMapper.formatValue(orderMetrics.getDeltaArr()));
        }
        if (OrderStatus.EXECUTED == order.getStatus()) {
            Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
            propertiesBody.setSubscriptionId(subscription.getSubscriptionId());
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_PAYMENT_TERMS_API_NAME)) {
            propertiesBody.setPaymentTerms(order.getPaymentTerm().getDisplayName());
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.BILLING_CYCLE_API_NAME)) {
            propertiesBody.setBillingCycle(order.getBillingCycle().getCycle().getPeriodicityName());
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_AVERAGE_ACV_API_NAME)) {
            propertiesBody.setSubskribeAverageAcv(hubSpotMapper.formatValue(metricsService.getOrderAverageACV(order)));
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_EXPIRATION_DATE_API_NAME)) {
            propertiesBody.setSubskribeOrderExpirationDate(formatDate(order.getExpiresOn(), timeZone, false, isUtcDate));
        }
        String orderCurrency = order.getCurrency().getCurrencyCode();
        if (hubSpotCurrencies.contains(orderCurrency)) {
            propertiesBody.setDealCurrencyCode(orderCurrency);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_AUTO_RENEW_API_NAME)) {
            propertiesBody.setSubskribeOrderAutoRenew(String.valueOf(order.getAutoRenew()));
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_START_DATE_TYPE_API_NAME)) {
            propertiesBody.setSubskribeOrderStartDateType(order.getStartDateType().getDisplayName());
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_DISCOUNT_TOTAL_API_NAME)) {
            propertiesBody.setSubskribeOrderDiscountTotal(
                String.valueOf(OrderDiscountService.getNetDiscountAmountCurrencyScaled(order.getLineItemsNetEffect(), chargeMap))
            );
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_DISCOUNT_PERCENT_API_NAME)) {
            propertiesBody.setSubskribeOrderDiscountPercent(
                String.valueOf(OrderDiscountService.calculateOrderLineNetDiscountPercent(order.getLineItemsNetEffect(), chargeMap))
            );
        }
        LOGGER.debug("pushing deal to hubspot: {} {} {}", order.getSfdcOpportunityId(), order.getOrderId(), order.getTotalAmount());
        hubSpotClient.updateDeal(order.getSfdcOpportunityId(), propertiesBody);
    }

    public void syncHubSpotSubscription(String subscriptionId) {
        LOGGER.info("Start of HubSpot subscription sync for {}", subscriptionId);
        List<String> errors = new ArrayList<>();
        HubSpotClient hubSpotClient = hubspotClientFactory.forTenantIntegration();
        List<HubSpotProperty> dealProperties = hubSpotClient.getObjectProperties(HubSpotDealProperties.API_NAME);
        boolean isUtcDate = checkIfUtcDateType(dealProperties);
        pushHubSpotSubscription(hubSpotClient, subscriptionId, isUtcDate, errors);
        LOGGER.info("End of HubSpot subscription sync for {}", subscriptionId);
        throwIfSyncErrorsAreFound(errors);
    }

    public void syncHubSpotSubscriptionStatus(String subscriptionId, String updatedStatus) {
        if (!hasEnterpriseHubspotIntegration()) {
            return;
        }

        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        Account account = accountGetService.getAccount(subscription.getAccountId());

        if (StringUtils.isBlank(account.getCrmId())) {
            return;
        }

        HubSpotIntegration hubSpotIntegration = hubSpotIntegrationService.getIntegrationForTenant();
        HubSpotClient hubSpotClient = hubspotClientFactory.fromHubspotIntegration(hubSpotIntegration);
        String subscriptionQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotSubscriptionProperties.API_NAME);

        List<String> hubSpotSubscriptionProperties = getHubSpotObjectPropertyNames(hubSpotClient, subscriptionQualifiedName);

        if (!hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.SUBSCRIPTION_STATUS_API_NAME)) {
            LOGGER.info("Hubspot subscription does not have status property");
            return;
        }

        String parentCompanyId = account.getCrmId();
        Optional<String> parentCompanyIdOptional = hubSpotGetService.getParentCompanyIdForMergedCompanyId(hubSpotClient, parentCompanyId);
        if (parentCompanyIdOptional.isPresent() && !StringUtils.equals(parentCompanyIdOptional.get(), parentCompanyId)) {
            LOGGER.info(
                "Merged Company found. Syncing subscription status for parent Company ID: {}, child Company: {}",
                parentCompanyIdOptional.get(),
                account.getCrmId()
            );
            parentCompanyId = parentCompanyIdOptional.get();
        }
        Optional<HubSpotSubscription> subscriptionOptional = hubSpotGetService.getExistingSubscriptionRecord(
            hubSpotClient,
            parentCompanyId,
            subscriptionId
        );

        if (subscriptionOptional.isEmpty()) {
            LOGGER.info("HubSpot subscription does not exist for subscription id: {}", subscriptionId);
            return;
        }

        HubSpotSubscriptionProperties subscriptionProperties = new HubSpotSubscriptionProperties();
        subscriptionProperties.setSubscriptionStatus(updatedStatus);
        HubSpotObject<HubSpotSubscriptionProperties> hubspotSubscription = new HubSpotObject<>();
        hubspotSubscription.setProperties(subscriptionProperties);

        hubSpotClient.updateObject(subscriptionQualifiedName, subscriptionOptional.get().getId(), hubspotSubscription);
    }

    private boolean hasEnterpriseHubspotIntegration() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        if (!hubSpotIntegrationService.hasCompletedHubSpotIntegration(tenantId)) {
            return false;
        }

        return !isBasic(tenantIdProvider, billyConfiguration);
    }

    private void pushHubSpotSubscription(HubSpotClient hubSpotClient, String subscriptionId, boolean isUtcDate, List<String> errors) {
        if (!hasEnterpriseHubspotIntegration()) {
            return;
        }

        HubSpotIntegration hubSpotIntegration = hubSpotIntegrationService.getIntegrationForTenant();
        List<String> customObjectSchemaNames = hubSpotClient.getCustomObjectSchemaList().stream().map(HubSpotCustomObjectSchema::getName).toList();
        if (
            !customObjectSchemaNames.contains(HubSpotSubscriptionProperties.API_NAME) ||
            !customObjectSchemaNames.contains(HubSpotSubscriptionLineItemProperties.API_NAME)
        ) {
            LOGGER.info(
                "Subscription or subscription line custom objects were not found on HubSpot for tenant: {}",
                hubSpotIntegration.getTenantId()
            );
            return;
        }

        String subscriptionQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotSubscriptionProperties.API_NAME);
        String subscriptionLineItemQualifiedName = hubSpotGetService.getFullyQualifiedName(
            hubSpotIntegration,
            HubSpotSubscriptionLineItemProperties.API_NAME
        );

        try (LockItem ignored = waitForLockOrThrow(subscriptionId)) {
            Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
            Account account = accountGetService.getAccount(subscription.getAccountId());

            String parentCompanyId = account.getCrmId();

            // Account does not have a CRM ID for a company on HubSpot
            if (StringUtils.isBlank(parentCompanyId)) {
                return;
            }

            Optional<String> parentCompanyIdOptional = hubSpotGetService.getParentCompanyIdForMergedCompanyId(hubSpotClient, parentCompanyId);
            if (parentCompanyIdOptional.isPresent() && !StringUtils.equals(parentCompanyIdOptional.get(), parentCompanyId)) {
                LOGGER.info(
                    "Merged Company found. Syncing subscription for parent Company ID: {}, child Company: {}",
                    parentCompanyIdOptional.get(),
                    account.getCrmId()
                );
                parentCompanyId = parentCompanyIdOptional.get();
            }
            Optional<HubSpotSubscription> subscriptionOptional = hubSpotGetService.getExistingSubscriptionRecord(
                hubSpotClient,
                parentCompanyId,
                subscriptionId
            );

            List<Order> subscriptionOrders = orderGetService.getExecutedOrdersBySubscriptionId(subscription.getSubscriptionId());
            List<String> hubSpotSubscriptionProperties = getHubSpotObjectPropertyNames(hubSpotClient, subscriptionQualifiedName);
            HubSpotSubscriptionProperties subscriptionRequest = prepareHubSpotSubscription(
                subscription,
                hubSpotSubscriptionProperties,
                isUtcDate,
                subscriptionOrders
            );
            List<String> lineItemProperties = getHubSpotObjectPropertyNames(hubSpotClient, subscriptionLineItemQualifiedName);

            List<HubSpotSubscriptionLineItem> hubSpotSubscriptionLineItems;
            HubSpotSubscription hubSpotSubscriptionResponse;
            LOGGER.info("Syncing subscription to HubSpot for {}. Request: {}", subscription.getSubscriptionId(), subscriptionRequest);
            if (subscriptionOptional.isPresent()) {
                hubSpotSubscriptionResponse = hubSpotClient.upsertSubscription(
                    subscriptionQualifiedName,
                    subscriptionOptional.get(),
                    subscriptionRequest
                );

                // Archive Subscription Lines and Subscription Line rate card price attributes
                Set<String> existingSubscriptionLineIds = new HashSet<>();
                if (customObjectSchemaNames.contains(HubSpotSubscriptionLineItemProperties.API_NAME)) {
                    existingSubscriptionLineIds.addAll(
                        hubSpotGetService.getAssociatedObjectIds(
                            hubSpotClient,
                            subscriptionQualifiedName,
                            subscriptionOptional.get().getId(),
                            subscriptionLineItemQualifiedName
                        )
                    );
                }
                if (customObjectSchemaNames.contains(HubSpotRateCardProperties.API_NAME)) {
                    Set<String> existingRateCardIds = new HashSet<>();
                    String rateCardQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotRateCardProperties.API_NAME);
                    for (var subscriptionLineId : existingSubscriptionLineIds) {
                        List<String> rateCardIds = hubSpotGetService.getAssociatedObjectIds(
                            hubSpotClient,
                            subscriptionLineItemQualifiedName,
                            subscriptionLineId,
                            rateCardQualifiedName
                        );
                        existingRateCardIds.addAll(rateCardIds);
                    }
                    hubSpotClient.archiveMany(rateCardQualifiedName, existingRateCardIds.stream().toList());
                }
                hubSpotClient.archiveMany(subscriptionLineItemQualifiedName, existingSubscriptionLineIds.stream().toList());

                hubSpotSubscriptionLineItems = createSubscriptionLineItems(
                    hubSpotClient,
                    subscription,
                    subscriptionQualifiedName,
                    hubSpotSubscriptionResponse.getId(),
                    subscriptionLineItemQualifiedName,
                    lineItemProperties,
                    isUtcDate
                );
                createHubSpotObjectAssociation(
                    hubSpotClient,
                    subscriptionQualifiedName,
                    hubSpotSubscriptionResponse.getId(),
                    HubSpotCompanyProperties.API_NAME,
                    parentCompanyId
                );
                if (!excludeSubscriptionNote(tenantIdProvider, billyConfiguration)) {
                    sendHubSpotSubscriptionSuccessOnCompanyActivityPage(hubSpotClient, subscription, parentCompanyId);
                }
                LOGGER.info("Subscription updated on HubSpot: {} {}", hubSpotSubscriptionResponse.getId(), subscription.getSubscriptionId());
            } else {
                hubSpotSubscriptionResponse = hubSpotClient.postSubscription(subscriptionQualifiedName, subscriptionRequest);
                hubSpotSubscriptionLineItems = createSubscriptionLineItems(
                    hubSpotClient,
                    subscription,
                    subscriptionQualifiedName,
                    hubSpotSubscriptionResponse.getId(),
                    subscriptionLineItemQualifiedName,
                    lineItemProperties,
                    isUtcDate
                );
                createHubSpotObjectAssociation(
                    hubSpotClient,
                    subscriptionQualifiedName,
                    hubSpotSubscriptionResponse.getId(),
                    HubSpotCompanyProperties.API_NAME,
                    parentCompanyId
                );
                if (!excludeSubscriptionNote(tenantIdProvider, billyConfiguration)) {
                    sendHubSpotSubscriptionSuccessOnCompanyActivityPage(hubSpotClient, subscription, parentCompanyId);
                }
                LOGGER.info("Subscription created on HubSpot: {} {}", hubSpotSubscriptionResponse.getId(), subscription.getSubscriptionId());
            }

            if (featureService.isEnabled(Feature.CRM_RATE_CARD)) {
                pushRateCardsForSubscriptionLines(
                    hubSpotClient,
                    hubSpotIntegration,
                    subscription,
                    hubSpotSubscriptionLineItems,
                    customObjectSchemaNames
                );
            }
            updateRenewedToSubscriptionProperty(hubSpotClient, subscriptionQualifiedName, subscription, parentCompanyId);
            syncCustomFieldsForSubscriptionToHubSpot(hubSpotClient, subscriptionId, subscriptionQualifiedName, hubSpotSubscriptionResponse, errors);
            syncCustomFieldsForSubscriptionLineItemsToHubSpot(
                hubSpotClient,
                subscriptionLineItemQualifiedName,
                hubSpotSubscriptionLineItems,
                subscription.getCharges(),
                subscriptionId,
                errors
            );
        }
    }

    private void syncCustomFieldsForSubscriptionToHubSpot(
        HubSpotClient hubSpotClient,
        String subscriptionId,
        String subscriptionQualifiedName,
        HubSpotSubscription hubSpotSubscription,
        List<String> errors
    ) {
        if (
            !hasEnterpriseHubspotIntegration() ||
            StringUtils.isBlank(hubSpotSubscription.getId()) ||
            !featureService.isEnabled(Feature.CRM_FIELD_MAPPING)
        ) {
            return;
        }
        Map<String, String> propertiesMap = new HashMap<>();
        List<HubSpotProperty> hubSpotSubscriptionProperties = hubSpotClient.getObjectProperties(subscriptionQualifiedName);
        CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.SUBSCRIPTION, subscriptionId);
        Map<String, CustomFieldValue> entries = customField.getEntries();
        List<CustomFieldValue> customFieldsToSync = entries.values().stream().toList();

        List<CrmFieldMapping> outboundCrmFieldMappings = crmFieldMappingService
            .getCrmFieldMappings(CrmType.HUBSPOT, CrmObjectType.SUBSCRIPTION)
            .stream()
            .filter(m -> CrmSyncDirection.OUTBOUND == m.getDirection())
            .toList();

        for (var crmFieldMapping : outboundCrmFieldMappings) {
            // Check that the Subskribe custom field exists
            Optional<CustomFieldValue> customFieldValue = customFieldsToSync
                .stream()
                .filter(c -> StringUtils.equals(crmFieldMapping.getSubskribeFieldName(), c.getName()))
                .findAny();
            if (customFieldValue.isEmpty()) {
                continue;
            }
            CustomFieldValue cfValue = customFieldValue.get();

            // Check that the HubSpot property field exists
            Optional<HubSpotProperty> property = hubSpotSubscriptionProperties
                .stream()
                .filter(p -> StringUtils.equalsIgnoreCase(crmFieldMapping.getCrmFieldName(), p.getName()))
                .findAny();
            if (property.isPresent()) {
                String fieldType = property.get().getType();
                List<String> propertyOptions = property.get().getOptions().stream().map(HubSpotOption::getValue).toList();
                if (
                    StringUtils.equals(fieldType, HUBSPOT_PROPERTY_STRING_TYPE) || new HashSet<>(propertyOptions).containsAll(cfValue.getSelections())
                ) {
                    propertiesMap.put(property.get().getName(), formatHubSpotCustomFieldDisplay(cfValue));
                }
            }
        }

        var updateRequests = new ArrayList<>();
        if (MapUtils.isNotEmpty(propertiesMap)) {
            HubSpotObject<Map<String, String>> customProperty = new HubSpotObject<>();
            customProperty.setId(hubSpotSubscription.getId());
            customProperty.setProperties(propertiesMap);
            updateRequests.add(customProperty);
        }

        LOGGER.info("Updating subscription custom fields for {}: {}", subscriptionId, updateRequests);
        if (CollectionUtils.isNotEmpty(updateRequests)) {
            try {
                hubSpotClient.updateObjects(subscriptionQualifiedName, updateRequests);
            } catch (IOException e) {
                String message = String.format(
                    "Failed to update HubSpot subscription custom fields for subscription %s CRM ID %s",
                    subscriptionId,
                    hubSpotSubscription.getId()
                );
                LOGGER.info(message, e);
                errors.add(message);
            }
        }
    }

    private void pushRateCardsForSubscriptionLines(
        HubSpotClient hubSpotClient,
        HubSpotIntegration hubSpotIntegration,
        Subscription subscription,
        List<HubSpotSubscriptionLineItem> hubSpotSubscriptionLineItems,
        List<String> customObjectSchemaNames
    ) {
        if (!customObjectSchemaNames.contains(HubSpotRateCardProperties.API_NAME)) {
            LOGGER.info("Rate card custom object was not found on HubSpot for tenant: {}", hubSpotIntegration.getTenantId());
            return;
        }

        String rateCardQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotRateCardProperties.API_NAME);
        String subscriptionLineItemQualifiedName = hubSpotGetService.getFullyQualifiedName(
            hubSpotIntegration,
            HubSpotSubscriptionLineItemProperties.API_NAME
        );

        List<String> hubSpotRateCardProperties = getHubSpotObjectPropertyNames(hubSpotClient, rateCardQualifiedName);
        Optional<HubSpotObjectAssociation> associationOptional = hubSpotGetService.getHubSpotObjectAssociationId(
            hubSpotClient,
            rateCardQualifiedName,
            subscriptionLineItemQualifiedName
        );

        // No association between Order and Order Lines
        if (associationOptional.isEmpty()) {
            return;
        }

        List<HubSpotObject<HubSpotRateCardProperties>> rateCardsToSync = new ArrayList<>();

        List<SubscriptionCharge> subscriptionCharges = subscription.getCharges();

        for (var subscriptionCharge : subscriptionCharges) {
            Optional<HubSpotSubscriptionLineItem> match = hubSpotSubscriptionLineItems
                .stream()
                .filter(hubSpotSubscriptionLineItem ->
                    StringUtils.equals(
                        hubSpotSubscriptionLineItem.getProperties().getSubscriptionChargeId(),
                        subscriptionCharge.getSubscriptionChargeId()
                    )
                )
                .findAny();

            List<AttributeReference> attributeReferences = subscriptionCharge.getAttributeReferences();

            if (match.isEmpty() || CollectionUtils.isEmpty(attributeReferences)) {
                continue;
            }

            for (var attributeReference : attributeReferences) {
                Optional<PriceAttribute> priceAttributeOptional = rateCardService.getPriceAttributeById(
                    attributeReference.getAttributeDefinitionId()
                );
                if (priceAttributeOptional.isEmpty()) {
                    continue;
                }

                Charge charge = productCatalogGetService.getChargeByChargeId(subscriptionCharge.getChargeId());

                HubSpotRateCardProperties rateCardProperties = prepareRateCard(
                    charge.getChargeId(),
                    null,
                    null,
                    null,
                    subscriptionCharge.getSubscriptionId(),
                    subscriptionCharge.getSubscriptionChargeId(),
                    attributeReference,
                    priceAttributeOptional.get(),
                    hubSpotRateCardProperties
                );
                AssociatedId targetHubSpotId = new AssociatedId(match.get().getId());
                HubSpotObjectAssociation associationType = new HubSpotObjectAssociation(
                    USER_DEFINED_ASSOCIATION,
                    associationOptional.get().getTypeId()
                );
                HubSpotBatchCustomObjectRequestAssociation batchAssociation = new HubSpotBatchCustomObjectRequestAssociation(
                    targetHubSpotId,
                    List.of(associationType)
                );
                rateCardsToSync.add(new HubSpotObject<>(rateCardProperties, List.of(batchAssociation)));
            }
        }

        hubSpotClient.postMany(rateCardQualifiedName, rateCardsToSync);
    }

    private void updateRenewedToSubscriptionProperty(
        HubSpotClient hubSpotClient,
        String subscriptionQualifiedName,
        Subscription subscription,
        String parentCompanyId
    ) {
        if (StringUtils.isBlank(subscription.getRenewedFromSubscriptionId())) {
            return;
        }

        List<String> hubSpotSubscriptionProperties = getHubSpotObjectPropertyNames(hubSpotClient, subscriptionQualifiedName);

        if (!hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.RENEWED_TO_SUBSCRIPTION_API_NAME)) {
            LOGGER.info("HubSpot subscription does not have renewed_to_subscription property");
            return;
        }

        Optional<HubSpotSubscription> subscriptionOptional = hubSpotGetService.getExistingSubscriptionRecord(
            hubSpotClient,
            parentCompanyId,
            subscription.getRenewedFromSubscriptionId()
        );

        if (subscriptionOptional.isEmpty()) {
            LOGGER.info("HubSpot subscription does not exist for subscription id: {}", subscription.getRenewedFromSubscriptionId());
            return;
        }

        HubSpotSubscriptionProperties subscriptionProperties = new HubSpotSubscriptionProperties();
        subscriptionProperties.setRenewedToSubscription(subscription.getSubscriptionId());
        HubSpotObject<HubSpotSubscriptionProperties> hubspotSubscription = new HubSpotObject<>();
        hubspotSubscription.setProperties(subscriptionProperties);

        hubSpotClient.updateObject(subscriptionQualifiedName, subscriptionOptional.get().getId(), hubspotSubscription);
    }

    private void syncCustomFieldsForSubscriptionLineItemsToHubSpot(
        HubSpotClient hubSpotClient,
        String subscriptionLineItemQualifiedName,
        List<HubSpotSubscriptionLineItem> subscriptionLineItems,
        List<SubscriptionCharge> subscriptionCharges,
        String subscriptionId,
        List<String> errors
    ) {
        if (!hasEnterpriseHubspotIntegration() || CollectionUtils.isEmpty(subscriptionCharges)) {
            return;
        }

        List<HubSpotProperty> hubSpotSubscriptionLineProperties = hubSpotClient.getObjectProperties(subscriptionLineItemQualifiedName);
        var updateRequests = new ArrayList<>();

        if (featureService.isEnabled(Feature.CRM_FIELD_MAPPING)) {
            List<CrmFieldMapping> outboundCrmFieldMappings = crmFieldMappingService
                .getCrmFieldMappings(CrmType.HUBSPOT, CrmObjectType.SUBSCRIPTION_ITEM)
                .stream()
                .filter(m -> CrmSyncDirection.OUTBOUND == m.getDirection())
                .toList();

            for (var subscriptionCharge : subscriptionCharges) {
                Optional<HubSpotSubscriptionLineItem> match = subscriptionLineItems
                    .stream()
                    .filter(l -> StringUtils.isNotBlank(l.getProperties().getSubscriptionChargeId()))
                    .filter(l -> l.getProperties().getSubscriptionChargeId().equals(subscriptionCharge.getSubscriptionChargeId()))
                    .findAny();
                if (match.isEmpty()) {
                    LOGGER.info("Could not find a line item for {} on HubSpot", subscriptionCharge.getSubscriptionChargeId());
                    continue;
                }

                CustomField customField = customFieldService.getCustomFields(
                    CustomFieldParentType.SUBSCRIPTION_ITEM,
                    subscriptionCharge.getSubscriptionChargeId()
                );
                Map<String, CustomFieldValue> entries = customField.getEntries();
                List<CustomFieldValue> customFieldsToSync = entries.values().stream().toList();

                Map<String, String> propertiesMap = new HashMap<>();
                for (var crmFieldMapping : outboundCrmFieldMappings) {
                    // Check that the Subskribe custom field exists
                    Optional<CustomFieldValue> customFieldValue = customFieldsToSync
                        .stream()
                        .filter(c -> StringUtils.equals(crmFieldMapping.getSubskribeFieldName(), c.getName()))
                        .findAny();
                    if (customFieldValue.isEmpty()) {
                        continue;
                    }
                    CustomFieldValue cfValue = customFieldValue.get();

                    // Check that the HubSpot property field exists
                    Optional<HubSpotProperty> property = hubSpotSubscriptionLineProperties
                        .stream()
                        .filter(p -> StringUtils.equals(crmFieldMapping.getCrmFieldName(), p.getName()))
                        .findAny();
                    if (property.isPresent()) {
                        String fieldType = property.get().getType();
                        List<String> propertyOptions = property.get().getOptions().stream().map(HubSpotOption::getValue).toList();
                        if (
                            StringUtils.equals(fieldType, HUBSPOT_PROPERTY_STRING_TYPE) ||
                            new HashSet<>(propertyOptions).containsAll(cfValue.getSelections())
                        ) {
                            propertiesMap.put(property.get().getName(), formatHubSpotCustomFieldDisplay(cfValue));
                        }
                    }
                }

                if (MapUtils.isNotEmpty(propertiesMap)) {
                    HubSpotObject<Map<String, String>> customProperty = new HubSpotObject<>();
                    customProperty.setId(match.get().getId());
                    customProperty.setProperties(propertiesMap);
                    updateRequests.add(customProperty);
                }
            }
        } else if (shouldSyncCustomFields(tenantIdProvider, billyConfiguration)) {
            for (var subscriptionCharge : subscriptionCharges) {
                Optional<HubSpotSubscriptionLineItem> match = subscriptionLineItems
                    .stream()
                    .filter(l -> StringUtils.isNotBlank(l.getProperties().getSubscriptionChargeId()))
                    .filter(l -> l.getProperties().getSubscriptionChargeId().equals(subscriptionCharge.getSubscriptionChargeId()))
                    .findAny();
                if (match.isEmpty()) {
                    LOGGER.info("Could not find a line item for {} on HubSpot", subscriptionCharge.getSubscriptionChargeId());
                    continue;
                }

                CustomField customField = customFieldService.getCustomFields(
                    CustomFieldParentType.SUBSCRIPTION_ITEM,
                    subscriptionCharge.getSubscriptionChargeId()
                );
                Map<String, CustomFieldValue> entries = customField.getEntries();
                List<CustomFieldValue> customFieldsToSync = entries.values().stream().toList();

                Map<String, String> propertiesMap = new HashMap<>();
                for (var customFieldValue : customFieldsToSync) {
                    Optional<HubSpotProperty> property = hubSpotSubscriptionLineProperties
                        .stream()
                        .filter(p -> StringUtils.equalsIgnoreCase(p.getName(), customFieldValue.getName()))
                        .findAny();
                    if (property.isPresent()) {
                        String fieldType = property.get().getType();
                        List<String> propertyOptions = property.get().getOptions().stream().map(HubSpotOption::getValue).toList();
                        if (
                            StringUtils.equals(fieldType, HUBSPOT_PROPERTY_STRING_TYPE) ||
                            new HashSet<>(propertyOptions).containsAll(customFieldValue.getSelections())
                        ) {
                            propertiesMap.put(property.get().getName(), formatHubSpotCustomFieldDisplay(customFieldValue));
                        }
                    }
                }

                if (MapUtils.isNotEmpty(propertiesMap)) {
                    HubSpotObject<Map<String, String>> customProperty = new HubSpotObject<>();
                    customProperty.setId(match.get().getId());
                    customProperty.setProperties(propertiesMap);
                    updateRequests.add(customProperty);
                }
            }
        }

        LOGGER.info("Updating subscription line custom fields for {}: {}", subscriptionId, updateRequests);
        if (CollectionUtils.isNotEmpty(updateRequests)) {
            try {
                hubSpotClient.updateObjects(subscriptionLineItemQualifiedName, updateRequests);
            } catch (IOException e) {
                String message = String.format("Failed to update subscription line custom fields for subscription %s", subscriptionId);
                LOGGER.info(message, e);
                errors.add(message);
            }
        }
    }

    private List<HubSpotSubscriptionLineItem> createSubscriptionLineItems(
        HubSpotClient hubSpotClient,
        Subscription subscription,
        String subscriptionObjectId,
        String subscriptionHubSpotId,
        String subscriptionLineItemQualifiedName,
        List<String> lineItemProperties,
        boolean isUtcDate
    ) {
        int subscriptionLineItemAssociationId = hubSpotGetService.getSubscriptionLineItemAssociationId(
            hubSpotClient,
            subscriptionObjectId,
            subscriptionLineItemQualifiedName
        );
        List<SubscriptionCharge> subscriptionCharges = subscription.getCharges();

        List<HubSpotObject<HubSpotSubscriptionLineItemProperties>> subscriptionLineItems = subscriptionCharges
            .stream()
            .map(subscriptionCharge -> {
                HubSpotSubscriptionLineItemProperties subscriptionLineItemProperties = prepareSubscriptionLineItem(
                    subscription,
                    subscriptionCharge,
                    lineItemProperties,
                    isUtcDate
                );
                AssociatedId targetHubSpotId = new AssociatedId(subscriptionHubSpotId);
                HubSpotObjectAssociation associationType = new HubSpotObjectAssociation(USER_DEFINED_ASSOCIATION, subscriptionLineItemAssociationId);
                HubSpotBatchCustomObjectRequestAssociation batchAssociation = new HubSpotBatchCustomObjectRequestAssociation(
                    targetHubSpotId,
                    List.of(associationType)
                );

                return new HubSpotObject<>(subscriptionLineItemProperties, List.of(batchAssociation));
            })
            .toList();
        LOGGER.info("Syncing subscription lines to HubSpot for {}. Request: {}", subscription.getSubscriptionId(), subscriptionLineItems);
        return hubSpotClient.postSubscriptionLineItems(subscriptionLineItemQualifiedName, subscriptionLineItems);
    }

    private HubSpotSubscriptionProperties prepareHubSpotSubscription(
        Subscription subscription,
        List<String> hubSpotSubscriptionProperties,
        boolean isUtcDate,
        List<Order> subscriptionOrders
    ) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Metrics subscriptionMetrics = metricsService.getSubscriptionMetrics(subscription, Instant.now());

        HubSpotSubscriptionProperties subscriptionRequest = new HubSpotSubscriptionProperties();
        LocalDateTime startDate = DateTimeConverter.instantToLocalDateTime(subscription.getStartDate());
        LocalDateTime endDate = DateTimeConverter.instantToLocalDateTime(subscription.getEndDate().minus(Duration.ofDays(1)));

        String subscriptionNameAndDuration = String.format(
            HUBSPOT_SUBSCRIPTION_NAME_FORMAT,
            subscription.getSubscriptionId(),
            String.format(HUBSPOT_SUBSCRIPTION_DATE_FORMAT, startDate.getMonthValue(), startDate.getDayOfMonth(), startDate.getYear()),
            String.format(HUBSPOT_SUBSCRIPTION_DATE_FORMAT, endDate.getMonthValue(), endDate.getDayOfMonth(), endDate.getYear())
        );

        if (hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.SUBSCRIPTION_START_DATE_API_NAME)) {
            subscriptionRequest.setSubscriptionStartDate(formatDate(subscription.getStartDate(), timeZone, false, isUtcDate));
        }

        if (hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.SUBSCRIPTION_END_DATE_API_NAME)) {
            subscriptionRequest.setSubscriptionEndDate(formatDate(subscription.getEndDate(), timeZone, true, isUtcDate));
        }
        subscriptionRequest.setName(subscriptionNameAndDuration);
        subscriptionRequest.setSubscriptionId(subscription.getSubscriptionId());
        subscriptionRequest.setAccountUrl(SubskribeUrlGenerator.getUiUrlForAccount(billyConfiguration.getSiteUrl(), subscription.getAccountId()));
        subscriptionRequest.setSubscriptionUrl(
            SubskribeUrlGenerator.getUiUrlForSubscription(billyConfiguration.getSiteUrl(), subscription.getSubscriptionId())
        );
        subscriptionRequest.setTcv(hubSpotMapper.formatValue(subscriptionMetrics.getTcv()));
        subscriptionRequest.setEntryArr(hubSpotMapper.formatValue(subscriptionMetrics.getEntryArr()));
        subscriptionRequest.setExitArr(hubSpotMapper.formatValue(subscriptionMetrics.getExitArr()));
        if (hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.CURRENCY_API_NAME)) {
            subscriptionRequest.setCurrency(subscription.getCurrency().getCurrencyCode());
        }
        if (hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.SUBSCRIPTION_STATUS_API_NAME)) {
            subscriptionRequest.setSubscriptionStatus(subscription.getState().name());
        }
        if (hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.DELTA_ARR_API_NAME)) {
            subscriptionRequest.setDeltaArr(hubSpotMapper.formatValue(subscriptionMetrics.getDeltaArr()));
        }
        if (
            hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.RENEWED_TO_SUBSCRIPTION_API_NAME) &&
            StringUtils.isNotBlank(subscription.getRenewedToSubscriptionId())
        ) {
            subscriptionRequest.setRenewedToSubscription(subscription.getRenewedToSubscriptionId());
        }
        if (
            hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.RENEWED_FROM_SUBSCRIPTION_API_NAME) &&
            StringUtils.isNotBlank(subscription.getRenewedFromSubscriptionId())
        ) {
            subscriptionRequest.setRenewedFromSubscription(subscription.getRenewedFromSubscriptionId());
        }
        if (hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.SUBSCRIPTION_CANCELLATION_DATE_API_NAME)) {
            subscriptionRequest.setSubscriptionCancellationDate(formatDate(subscription.getCanceledDate(), timeZone, false, isUtcDate));
        }
        if (
            hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.BILLING_CYCLE_START_DATE_API_NAME) &&
            CollectionUtils.isNotEmpty(subscriptionOrders)
        ) {
            Optional<Order> orderOptional = subscriptionOrders.stream().findAny();
            if (orderOptional.isPresent()) {
                Instant billingCycleStartDate = orderOptional.get().getBillingAnchorDate();
                subscriptionRequest.setBillingCycleStartDate(formatDate(billingCycleStartDate, timeZone, false, isUtcDate));
            }
        }
        if (hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.RECURRING_TOTAL_API_NAME)) {
            subscriptionRequest.setRecurringTotal(hubSpotMapper.formatValue(subscriptionMetrics.getRecurringTotal()));
        }
        if (hubSpotSubscriptionProperties.contains(HubSpotSubscriptionProperties.NON_RECURRING_TOTAL_API_NAME)) {
            subscriptionRequest.setNonRecurringTotal(hubSpotMapper.formatValue(subscriptionMetrics.getNonRecurringTotal()));
        }
        return subscriptionRequest;
    }

    public void updatePrimaryOrderIdForOpportunity(Order order) {
        opportunityService.updatePrimaryOrderIdForOpportunity(order);
        hubSpotJobQueueService.dispatchOrderSync(order);
        hubSpotJobQueueService.dispatchEsignSync(order);
    }

    public void resetDeal(String dealId) {
        HubSpotIntegration hubSpotIntegration = hubSpotIntegrationService.getIntegrationForTenant();
        HubSpotClient hubSpotClient = hubspotClientFactory.fromHubspotIntegration(hubSpotIntegration);
        try (LockItem ignored = waitForLockOrThrow(dealId)) {
            String parentDealId = dealId;

            // Checks if this Deal is part of a merged Deal
            Optional<String> parentDealIdOptional = hubSpotGetService.getParentDealIdForMergedDealId(hubSpotClient, dealId);
            if (parentDealIdOptional.isPresent() && !StringUtils.equals(parentDealIdOptional.get(), dealId)) {
                LOGGER.info("Merged deal found. Parent Deal ID: {}, Child Deal ID: {}", parentDealIdOptional.get(), dealId);
                parentDealId = parentDealIdOptional.get();
            }

            cleanupHubSpotDeal(hubSpotClient, parentDealId);
            cleanupRelatedDealObjects(hubSpotClient, hubSpotIntegration, parentDealId);
        } catch (IOException e) {
            LOGGER.info("Failed to reset HubSpot deal {}", dealId);
        }
    }

    private void cleanupHubSpotDeal(HubSpotClient hubSpotClient, String dealId) throws IOException {
        List<String> hubSpotDealProperties = getHubSpotObjectPropertyNames(hubSpotClient, HubSpotDealProperties.API_NAME);

        HubSpotDealProperties propertiesBody = new HubSpotDealProperties();
        propertiesBody.setOrderId(StringUtils.EMPTY);
        propertiesBody.setSubskribePrimaryOrderLink(StringUtils.EMPTY);
        propertiesBody.setSubskribeOrderStatus(StringUtils.EMPTY);
        propertiesBody.setSubskribeOpportunityLink(StringUtils.EMPTY);
        propertiesBody.setSubskribeTermStartDate(StringUtils.EMPTY);
        propertiesBody.setSubskribeTermEndDate(StringUtils.EMPTY);
        propertiesBody.setOrderTotal(StringUtils.EMPTY);
        propertiesBody.setEntryArr(StringUtils.EMPTY);
        propertiesBody.setExitArr(StringUtils.EMPTY);
        propertiesBody.setAverageArr(StringUtils.EMPTY);
        propertiesBody.setDeltaArr(StringUtils.EMPTY);
        propertiesBody.setSubscriptionId(StringUtils.EMPTY);
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_PAYMENT_TERMS_API_NAME)) {
            propertiesBody.setPaymentTerms(StringUtils.EMPTY);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.BILLING_CYCLE_API_NAME)) {
            propertiesBody.setBillingCycle(StringUtils.EMPTY);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_AVERAGE_ACV_API_NAME)) {
            propertiesBody.setSubskribeAverageAcv(StringUtils.EMPTY);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_EXPIRATION_DATE_API_NAME)) {
            propertiesBody.setSubskribeOrderExpirationDate(StringUtils.EMPTY);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ESIGN_STATUS_API_NAME)) {
            propertiesBody.setSubskribeEsignStatus(StringUtils.EMPTY);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ESIGN_LINK_API_NAME)) {
            propertiesBody.setSubskribeEsignLink(StringUtils.EMPTY);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_AUTO_RENEW_API_NAME)) {
            propertiesBody.setSubskribeOrderAutoRenew(StringUtils.EMPTY);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.DEAL_CURRENCY_CODE_API_NAME)) {
            propertiesBody.setDealCurrencyCode(StringUtils.EMPTY);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_START_DATE_TYPE_API_NAME)) {
            propertiesBody.setSubskribeOrderStartDateType(StringUtils.EMPTY);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_DISCOUNT_TOTAL_API_NAME)) {
            propertiesBody.setSubskribeOrderDiscountTotal(StringUtils.EMPTY);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_DISCOUNT_PERCENT_API_NAME)) {
            propertiesBody.setSubskribeOrderDiscountPercent(StringUtils.EMPTY);
        }
        hubSpotClient.updateDeal(dealId, propertiesBody);
    }

    private void cleanupRelatedDealObjects(HubSpotClient hubSpotClient, HubSpotIntegration hubSpotIntegration, String dealId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        if (isBasic(tenantIdProvider, billyConfiguration) || !hubSpotIntegrationService.hasCompletedHubSpotIntegration(tenantId)) {
            return;
        }

        String orderQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderProperties.API_NAME);
        String orderLineQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderLineProperties.API_NAME);

        Set<String> existingOrderIds = new HashSet<>();
        Set<String> existingOrderLineIds = new HashSet<>();

        List<String> customObjectSchemaNames = hubSpotClient.getCustomObjectSchemaList().stream().map(HubSpotCustomObjectSchema::getName).toList();

        String parentDealId = dealId;

        // Checks if this Deal is part of a merged Deal
        Optional<String> parentDealIdOptional = hubSpotGetService.getParentDealIdForMergedDealId(hubSpotClient, dealId);
        if (parentDealIdOptional.isPresent() && !StringUtils.equals(parentDealIdOptional.get(), dealId)) {
            LOGGER.info("Merged deal found. Parent Deal ID: {}, Child Deal ID: {}", parentDealIdOptional.get(), dealId);
            parentDealId = parentDealIdOptional.get();
        }

        if (customObjectSchemaNames.contains(HubSpotOrderProperties.API_NAME)) {
            existingOrderIds.addAll(
                hubSpotGetService.getAssociatedObjectIds(hubSpotClient, HubSpotDealProperties.API_NAME, parentDealId, orderQualifiedName)
            );
        }

        if (customObjectSchemaNames.contains(HubSpotOrderLineProperties.API_NAME)) {
            for (var orderId : existingOrderIds) {
                List<String> orderLineIds = hubSpotGetService.getAssociatedObjectIds(
                    hubSpotClient,
                    orderQualifiedName,
                    orderId,
                    orderLineQualifiedName
                );
                existingOrderLineIds.addAll(orderLineIds);
            }
        }

        if (customObjectSchemaNames.contains(HubSpotRateCardProperties.API_NAME)) {
            Set<String> existingRateCardIds = new HashSet<>();
            String rateCardQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotRateCardProperties.API_NAME);
            for (var orderLineId : existingOrderLineIds) {
                List<String> rateCardIds = hubSpotGetService.getAssociatedObjectIds(
                    hubSpotClient,
                    orderLineQualifiedName,
                    orderLineId,
                    rateCardQualifiedName
                );
                existingRateCardIds.addAll(rateCardIds);
            }
            hubSpotClient.archiveMany(rateCardQualifiedName, existingRateCardIds.stream().toList());
        }

        hubSpotClient.archiveMany(orderLineQualifiedName, existingOrderLineIds.stream().toList());
        hubSpotClient.archiveMany(orderQualifiedName, existingOrderIds.stream().toList());

        if (shouldSyncCustomFields(tenantIdProvider, billyConfiguration)) {
            if (!customObjectSchemaNames.contains(HubSpotOrderAnnualAmountProperties.API_NAME)) {
                return;
            }

            String orderAnnualAmountQualifiedName = hubSpotGetService.getFullyQualifiedName(
                hubSpotIntegration,
                HubSpotOrderAnnualAmountProperties.API_NAME
            );
            List<String> orderAnnualAmountIds = hubSpotGetService.getAssociatedObjectIds(
                hubSpotClient,
                HubSpotDealProperties.API_NAME,
                parentDealId,
                orderAnnualAmountQualifiedName
            );
            hubSpotClient.archiveMany(orderAnnualAmountQualifiedName, orderAnnualAmountIds);
        }
    }

    private boolean customObjectSchemaNameIsPresent(HubSpotClient hubSpotClient, HubSpotIntegration hubSpotIntegration, String objectSchemaName) {
        List<String> customObjectSchemaNames = hubSpotClient.getCustomObjectSchemaList().stream().map(HubSpotCustomObjectSchema::getName).toList();
        if (!customObjectSchemaNames.contains(objectSchemaName)) {
            LOGGER.info("HubSpot instance of ID {} does not contain custom object type {}", hubSpotIntegration.getHubspotId(), objectSchemaName);
            return false;
        }
        return true;
    }

    // Deletes HubSpot orders that no longer exist on Subskribe. Includes composite orders.
    public void cleanupDeletedOrders(String dealId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        if (isBasic(tenantIdProvider, billyConfiguration) || !hubSpotIntegrationService.hasCompletedHubSpotIntegration(tenantId)) {
            return;
        }

        HubSpotIntegration hubSpotIntegration = hubSpotIntegrationService.getIntegrationForTenant();
        HubSpotClient hubSpotClient = hubspotClientFactory.fromHubspotIntegration(hubSpotIntegration);

        String orderQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderProperties.API_NAME);
        String orderLineQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderLineProperties.API_NAME);

        Set<String> existingOrderIds = new HashSet<>();
        Set<String> existingOrderLineIds = new HashSet<>();

        List<String> customObjectSchemaNames = hubSpotClient.getCustomObjectSchemaList().stream().map(HubSpotCustomObjectSchema::getName).toList();

        // If the order exists on HubSpot but NOT on Subskribe, we should remove it.
        List<HubSpotOrder> hubSpotOrders = hubSpotGetService
            .getHubSpotOrdersByDealId(hubSpotClient, dealId)
            .stream()
            .filter(o -> StringUtils.isNotBlank(o.getProperties().getPrimaryOrderId()))
            .toList();

        Set<String> relatedOrderIds = orderGetService.getOrdersByCrmOpportunityId(dealId).stream().map(Order::getOrderId).collect(Collectors.toSet());
        List<CompositeOrder> existingCompositeOrders = compositeOrderGetService.getCompositeOrdersByCrmOpportunityId(dealId);
        for (var existingCompositeOrder : existingCompositeOrders) {
            List<Order> orders = compositeOrderGetService.getOrdersInCompositeOrder(existingCompositeOrder.getCompositeOrderId());
            List<String> restructureOrderIds = orders
                .stream()
                .filter(subOrder -> subOrder.getOrderType() == OrderType.RESTRUCTURE)
                .map(Order::getOrderId)
                .toList();
            relatedOrderIds.addAll(restructureOrderIds);
        }

        if (customObjectSchemaNames.contains(HubSpotOrderProperties.API_NAME)) {
            List<HubSpotOrder> hubSpotOrdersToDelete = hubSpotOrders
                .stream()
                .filter(o -> !relatedOrderIds.contains(o.getProperties().getPrimaryOrderId()))
                .toList();
            existingOrderIds.addAll(hubSpotOrdersToDelete.stream().map(HubSpotOrder::getId).toList());
        }

        if (customObjectSchemaNames.contains(HubSpotOrderLineProperties.API_NAME)) {
            for (var orderId : existingOrderIds) {
                List<String> orderLineIds = hubSpotGetService.getAssociatedObjectIds(
                    hubSpotClient,
                    orderQualifiedName,
                    orderId,
                    orderLineQualifiedName
                );
                existingOrderLineIds.addAll(orderLineIds);
            }
        }

        if (customObjectSchemaNames.contains(HubSpotRateCardProperties.API_NAME)) {
            Set<String> existingRateCardIds = new HashSet<>();
            String rateCardQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotRateCardProperties.API_NAME);
            for (var orderLineId : existingOrderLineIds) {
                List<String> rateCardIds = hubSpotGetService.getAssociatedObjectIds(
                    hubSpotClient,
                    orderLineQualifiedName,
                    orderLineId,
                    rateCardQualifiedName
                );
                existingRateCardIds.addAll(rateCardIds);
            }
            hubSpotClient.archiveMany(rateCardQualifiedName, existingRateCardIds.stream().toList());
        }

        hubSpotClient.archiveMany(orderLineQualifiedName, existingOrderLineIds.stream().toList());
        hubSpotClient.archiveMany(orderQualifiedName, existingOrderIds.stream().toList());
    }

    private void cleanupRelatedCompositeOrders(
        HubSpotClient hubSpotClient,
        HubSpotIntegration hubSpotIntegration,
        Order syncedSubOrder,
        Opportunity opportunity
    ) {
        String orderQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderProperties.API_NAME);
        String orderLineQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderLineProperties.API_NAME);

        Set<String> existingOrderIds = new HashSet<>();
        Set<String> existingOrderLineIds = new HashSet<>();

        List<String> customObjectSchemaNames = hubSpotClient.getCustomObjectSchemaList().stream().map(HubSpotCustomObjectSchema::getName).toList();

        List<HubSpotOrder> hubSpotOrders = hubSpotGetService
            .getHubSpotOrdersByDealId(hubSpotClient, opportunity.getCrmId())
            .stream()
            .filter(o -> StringUtils.isNotBlank(o.getProperties().getPrimaryOrderId()))
            .toList();

        // Delete all orders that are not the synced order
        if (customObjectSchemaNames.contains(HubSpotOrderProperties.API_NAME)) {
            List<HubSpotOrder> hubSpotOrdersToDelete = hubSpotOrders
                .stream()
                .filter(o -> !Objects.equals(syncedSubOrder.getOrderId(), o.getProperties().getPrimaryOrderId()))
                .toList();
            existingOrderIds.addAll(hubSpotOrdersToDelete.stream().map(HubSpotOrder::getId).toList());
        }

        if (customObjectSchemaNames.contains(HubSpotOrderLineProperties.API_NAME)) {
            for (var orderId : existingOrderIds) {
                List<String> orderLineIds = hubSpotGetService.getAssociatedObjectIds(
                    hubSpotClient,
                    orderQualifiedName,
                    orderId,
                    orderLineQualifiedName
                );
                existingOrderLineIds.addAll(orderLineIds);
            }
        }

        if (customObjectSchemaNames.contains(HubSpotRateCardProperties.API_NAME)) {
            Set<String> existingRateCardIds = new HashSet<>();
            String rateCardQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotRateCardProperties.API_NAME);
            for (var orderLineId : existingOrderLineIds) {
                List<String> rateCardIds = hubSpotGetService.getAssociatedObjectIds(
                    hubSpotClient,
                    orderLineQualifiedName,
                    orderLineId,
                    rateCardQualifiedName
                );
                existingRateCardIds.addAll(rateCardIds);
            }
            hubSpotClient.archiveMany(rateCardQualifiedName, existingRateCardIds.stream().toList());
        }

        hubSpotClient.archiveMany(orderLineQualifiedName, existingOrderLineIds.stream().toList());
        hubSpotClient.archiveMany(orderQualifiedName, existingOrderIds.stream().toList());
    }

    public static Boolean dealHasSubscriptionIdIfNeeded(HubSpotDeal deal) {
        String dealType = deal.getProperties().getDealType();
        return (
            (HubSpotDealType.getHubSpotDealTypesThatNeedSubscriptionId().contains(dealType) &&
                StringUtils.isNotBlank(deal.getProperties().getSubscriptionId())) ||
            HubSpotDealType.NEW.getHubSpotName().equalsIgnoreCase(dealType)
        );
    }

    private void createNoteCompanyAssociation(HubSpotClient hubSpotClient, String noteId, String companyId, int noteCompanyAssociationId) {
        HubSpotObjectAssociation hubSpotObjectAssociation = new HubSpotObjectAssociation();
        hubSpotObjectAssociation.setAssociationCategory(HUBSPOT_DEFINED_ASSOCIATION);
        hubSpotObjectAssociation.setAssociationTypeId(noteCompanyAssociationId);

        hubSpotClient.putNotesAssociation(noteId, "companies", companyId, hubSpotObjectAssociation);
    }

    private void sendHubSpotSubscriptionSuccessOnCompanyActivityPage(HubSpotClient hubSpotClient, Subscription subscription, String companyId) {
        int noteCompanyAssociationId = hubSpotGetService.getNoteCompanyAssociationId(hubSpotClient);

        HubSpotNoteProperties hubSpotNoteRequest = new HubSpotNoteProperties();
        hubSpotNoteRequest.setDatetime(Instant.now().toString());
        hubSpotNoteRequest.setBody(String.format(HUBSPOT_SUBSCRIPTION_SUCCESS_FORMAT, subscription.getSubscriptionId()));

        HubSpotNote response = hubSpotClient.postNote(hubSpotNoteRequest);
        LOGGER.info("Note created on HubSpot: {}", response);

        createNoteCompanyAssociation(hubSpotClient, response.getId(), companyId, noteCompanyAssociationId);
    }

    private HubSpotSubscriptionLineItemProperties prepareSubscriptionLineItem(
        Subscription subscription,
        SubscriptionCharge subscriptionCharge,
        List<String> lineItemProperties,
        boolean isUtcDate
    ) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        HubSpotSubscriptionLineItemProperties line = new HubSpotSubscriptionLineItemProperties();
        line.setSubscriptionId(subscription.getSubscriptionId());

        Charge charge = productCatalogGetService.getChargeByChargeId(subscriptionCharge.getChargeId());
        line.setChargeId(subscriptionCharge.getChargeId());

        if (lineItemProperties.contains(HubSpotSubscriptionLineItemProperties.CURRENCY_API_NAME)) {
            line.setCurrency(subscription.getCurrency().getCurrencyCode());
        }
        line.setLineStartDate(formatDate(subscriptionCharge.getStartDate(), timeZone, false, isUtcDate));
        line.setLineEndDate(formatDate(subscriptionCharge.getEndDate(), timeZone, true, isUtcDate));

        Plan plan = productCatalogGetService.getPlanByChargeId(subscriptionCharge.getChargeId());
        line.setPlanId(plan.getPlanId());
        line.setPlanName(plan.getName());
        line.setProductId(plan.getProductId());
        line.setChargeName(String.format(HUBSPOT_SUBSCRIPTION_CHARGE_NAME_FORMAT, plan.getName(), charge.getName()));

        Product product = productCatalogGetService.getProduct(plan.getProductId());
        line.setProductName(product.getName());
        line.setQuantity(String.valueOf(subscriptionCharge.getQuantity()));
        line.setSubscriptionStartDate(formatDate(subscription.getStartDate(), timeZone, false, isUtcDate));
        line.setSubscriptionEndDate(formatDate(subscription.getEndDate(), timeZone, true, isUtcDate));

        if (Objects.nonNull(subscriptionCharge.getListUnitPrice())) {
            BigDecimal unitDiscountAmount = subscriptionCharge.getListUnitPrice().subtract(subscriptionCharge.getSellUnitPrice());
            line.setDiscount(hubSpotMapper.formatValue(unitDiscountAmount));
            line.setListUnitPrice(hubSpotMapper.formatValue(subscriptionCharge.getListUnitPrice()));
        }
        if (Objects.nonNull(subscriptionCharge.getSellUnitPrice())) {
            line.setSellUnitPrice(hubSpotMapper.formatValue(subscriptionCharge.getSellUnitPrice()));
        }

        LocalDateTime startDate = DateTimeConverter.instantToLocalDateTime(subscriptionCharge.getStartDate());
        LocalDateTime endDate = DateTimeConverter.instantToLocalDateTime(subscriptionCharge.getEndDate().minus(Duration.ofDays(1)));
        line.setName(String.format(HUBSPOT_LINE_ITEM_NAME_FORMAT, plan.getName(), charge.getName()));

        if (subscription.getTermLength() != null) {
            line.setTermLengthInYears(String.valueOf(subscription.getTermLength().getRecurrenceDurationInYears()));
        } else {
            line.setTermLengthInYears(String.valueOf(Period.toDurationInYears(subscription.getStartDate(), subscription.getEndDate(), timeZone)));
        }

        if (lineItemProperties.contains(HubSpotSubscriptionLineItemProperties.TERM_API_NAME)) {
            line.setTerm(
                String.format(
                    LINE_ITEM_TERM_FORMAT,
                    String.format(HUBSPOT_SUBSCRIPTION_DATE_FORMAT, startDate.getMonthValue(), startDate.getDayOfMonth(), startDate.getYear()),
                    String.format(HUBSPOT_SUBSCRIPTION_DATE_FORMAT, endDate.getMonthValue(), endDate.getDayOfMonth(), endDate.getYear())
                )
            );
        }

        if (lineItemProperties.contains(HubSpotSubscriptionLineItemProperties.SUBSCRIPTION_CHARGE_ID_API_NAME)) {
            line.setSubscriptionChargeId(subscriptionCharge.getSubscriptionChargeId());
        }
        return line;
    }

    private HubSpotIntegration verifyHubSpotRequest(String hubspotSignature, String portalId, String method, UriInfo uriInfo) {
        String clientSecret = getClientSecret().orElseThrow(() -> new ForbiddenException("unauthorized access"));
        String expectedSignature = getExpectedSignature(clientSecret, method, uriInfo.getRequestUri().toString());
        if (!expectedSignature.equals(hubspotSignature)) {
            LOGGER.info("request uri: {}", uriInfo.getRequestUri().toString());
            LOGGER.info("signature mismatch: {} {}", hubspotSignature, expectedSignature);
            throw new IllegalArgumentException("signature mismatch in hubspot request");
        }
        return hubSpotDAO.getIntegrationByHubSpotId(portalId).orElseThrow(() -> new ForbiddenException("unauthorized access"));
    }

    private String getExpectedSignature(String clientSecret, String method, String uri) {
        uri = uri.replace(HTTP_SCHEME_PREFIX, HTTPS_SCHEME_PREFIX);
        uri = URLDecoder.decode(uri, StandardCharsets.UTF_8);
        String hash = clientSecret + method + uri;
        return Hashing.sha256().hashString(hash, StandardCharsets.UTF_8).toString();
    }

    public HubSpotCompanyCard getCompanyCard(String crmId, String hubspotSignature, String portalId, String method, UriInfo uriInfo) {
        HubSpotIntegration integration = verifyHubSpotRequest(hubspotSignature, portalId, method, uriInfo);
        return TenantContextInjector.callInTenantContext(integration.getTenantId(), tenantIdProvider, () -> {
            Optional<Account> optionalAccount = accountGetService.getAccountByCrmId(crmId);
            List<HubSpotCompanyCardResult> cardResults = new ArrayList<>();
            if (optionalAccount.isPresent()) {
                Account account = optionalAccount.get();
                String accountUrl = String.format(SUBSKRIBE_ACCOUNT_LINK_FORMAT, billyConfiguration.getSiteUrl(), account.getAccountId());
                cardResults.add(new HubSpotCompanyCardResult(crmId, VIEW_ON_SUBSKRIBE_ACTION, accountUrl, null));
            }
            String syncUrl = String.format(SUBSKRIBE_ACCOUNT_SYNC_FORMAT, billyConfiguration.getSiteUrl(), crmId);
            cardResults.add(new HubSpotCompanyCardResult(crmId, SYNC_TO_SUBSKRIBE_ACTION, syncUrl, null));
            return new HubSpotCompanyCard(cardResults, null);
        });
    }

    public HubSpotCompanyCard getDealCard(String hubSpotDealId, String hubspotSignature, String portalId, String method, UriInfo uriInfo) {
        HubSpotIntegration integration = verifyHubSpotRequest(hubspotSignature, portalId, method, uriInfo);
        return TenantContextInjector.callInTenantContext(integration.getTenantId(), tenantIdProvider, () -> {
            HubSpotClient hubSpotClient = hubspotClientFactory.fromHubspotIntegration(integration);
            Optional<HubSpotDeal> hubSpotDealOptional = hubSpotClient.getDealById(hubSpotDealId);
            HubSpotCompany hubSpotCompany = hubSpotGetService.getAssociatedCompanyByDealId(hubSpotClient, hubSpotDealId);
            if (hubSpotDealOptional.isEmpty() || Objects.isNull(hubSpotCompany)) {
                HubSpotCompanyCardResult cardResult = new HubSpotCompanyCardResult(hubSpotDealId, MISSING_HUBSPOT_COMPANY_CARD_TITLE, null, null);
                List<HubSpotCompanyCardResult> cardResults = List.of(cardResult);
                return new HubSpotCompanyCard(cardResults, null);
            }

            HubSpotDeal hubSpotDeal = hubSpotDealOptional.get();
            Optional<CompositeOrderType> compositeOrderTypeOptional = hubSpotMapper.compositeOrderTypeFromHubSpot(
                hubSpotDeal.getProperties().getSubskribeOrderType()
            );
            if (compositeOrderTypeOptional.isPresent()) {
                return getCompositeOrderCard(compositeOrderTypeOptional.get(), hubSpotDeal, hubSpotCompany, billyConfiguration.getSiteUrl());
            }
            Optional<OrderType> orderTypeOptional = OrderType.fromString(hubSpotDeal.getProperties().getSubskribeOrderType());
            OrderType orderType = orderTypeOptional.orElse(OrderType.NEW);
            return getNewOrderCard(hubSpotDeal, hubSpotCompany, orderType);
        });
    }

    public static HubSpotCompanyCard getCompositeOrderCard(
        CompositeOrderType compositeOrderType,
        HubSpotDeal hubSpotDeal,
        HubSpotCompany hubSpotCompany,
        String siteUrl
    ) {
        String compositeOrderTypeString = compositeOrderType.getCompositeOrderName();

        if (StringUtils.isBlank(compositeOrderTypeString)) {
            HubSpotCompanyCardResult cardResult = new HubSpotCompanyCardResult(
                hubSpotDeal.getId(),
                COMPOSITE_ORDER_TYPE_NOT_SUPPORTED_CARD_TITLE,
                null,
                null
            );
            List<HubSpotCompanyCardResult> cardResults = List.of(cardResult);
            return new HubSpotCompanyCard(cardResults, null);
        }

        String createOrderUrl = String.format(
            SUBSKRIBE_CREATE_ORDER_URL_FORMAT,
            siteUrl,
            hubSpotCompany.getId(),
            hubSpotDeal.getId(),
            compositeOrderTypeString
        );

        HubSpotCompanyCardResult cardResult = new HubSpotCompanyCardResult(
            hubSpotDeal.getId(),
            CREATE_QUOTE_ON_SUBSKRIBE_ACTION,
            createOrderUrl,
            null
        );
        return new HubSpotCompanyCard(List.of(cardResult), null);
    }

    private HubSpotCompanyCard getNewOrderCard(HubSpotDeal hubSpotDeal, HubSpotCompany hubSpotCompany, OrderType orderType) {
        String createOrderUrl = String.format(
            SUBSKRIBE_CREATE_ORDER_URL_FORMAT,
            billyConfiguration.getSiteUrl(),
            hubSpotCompany.getId(),
            hubSpotDeal.getId(),
            orderType.name().toLowerCase()
        );
        HubSpotCompanyCardResult cardResult = new HubSpotCompanyCardResult(
            hubSpotDeal.getId(),
            CREATE_QUOTE_ON_SUBSKRIBE_ACTION,
            createOrderUrl,
            null
        );
        List<HubSpotCompanyCardResult> cardResults = List.of(cardResult);
        return new HubSpotCompanyCard(cardResults, null);
    }

    static boolean isBasic(TenantIdProvider tenantIdProvider, BillyConfiguration billyConfiguration) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        return BooleanUtils.isTrue(tenantScopedConfig.getHubSpotConfiguration().getBasic());
    }

    private static boolean excludeSubscriptionNote(TenantIdProvider tenantIdProvider, BillyConfiguration billyConfiguration) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        return BooleanUtils.isTrue(tenantScopedConfig.getHubSpotConfiguration().getExcludeSubscriptionNote());
    }

    private static boolean shouldSyncCustomFields(TenantIdProvider tenantIdProvider, BillyConfiguration billyConfiguration) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        return BooleanUtils.isTrue(tenantScopedConfig.getHubSpotConfiguration().getSyncCustomFields());
    }

    private void createHubSpotObjectAssociation(
        HubSpotClient hubSpotClient,
        String fromObjectTypeId,
        String fromObjectId,
        String toObjectTypeId,
        String toObjectId
    ) {
        Optional<HubSpotObjectAssociation> associationIdOptional = hubSpotGetService.getHubSpotObjectAssociationId(
            hubSpotClient,
            fromObjectTypeId,
            toObjectTypeId
        );
        if (associationIdOptional.isEmpty()) {
            LOGGER.warn("Failed to find an association ID between object {} and {} on HubSpot", fromObjectTypeId, toObjectTypeId);
            return;
        }

        List<String> associatedObjectIds = hubSpotGetService.getAssociatedObjectIds(hubSpotClient, fromObjectTypeId, fromObjectId, toObjectTypeId);
        if (associatedObjectIds.contains(toObjectId)) {
            return;
        }

        HubSpotObjectAssociation hubSpotObjectAssociation = new HubSpotObjectAssociation();
        hubSpotObjectAssociation.setAssociationCategory(USER_DEFINED_ASSOCIATION);
        hubSpotObjectAssociation.setAssociationTypeId(associationIdOptional.get().getTypeId());

        hubSpotClient.putHubSpotObjectAssociation(fromObjectTypeId, fromObjectId, toObjectTypeId, toObjectId, hubSpotObjectAssociation);
    }

    public static String formatInvoiceNumber(String invoiceNumber) {
        String parsedInvoiceNumber = invoiceNumber.replaceAll(EXCLUDE_NON_NUMERIC_REGEX, "");
        return String.format(INVOICE_NUMBER_FORMAT, DEFAULT_INVOICE_PREFIX, parsedInvoiceNumber);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public AccountJson prepareAccount(String accountCrmId, Optional<String> optionalOpportunityCrmId) {
        HubSpotClient hubSpotClient = hubspotClientFactory.forTenantIntegration();
        HubSpotIntegration hubSpotIntegration = hubSpotClient.getHubSpotIntegration();

        // Query alternate account name if specified
        List<String> companyProperties = new ArrayList<>(HubSpotCompanyProperties.PROPERTIES);
        CrmIntegrationConfiguration crmIntegrationConfiguration = hubSpotIntegration.getConfiguration();

        String alternateAccountNameSourceField = StringUtils.EMPTY;
        if (crmIntegrationConfiguration != null && StringUtils.isNotBlank(crmIntegrationConfiguration.alternateCompanyNameSourceField())) {
            alternateAccountNameSourceField = crmIntegrationConfiguration.alternateCompanyNameSourceField();
            LOGGER.info(
                "Alternate account name found for HubSpot Company ID: {}. Source: {}",
                accountCrmId,
                crmIntegrationConfiguration.alternateCompanyNameSourceField()
            );
            companyProperties.add(crmIntegrationConfiguration.alternateCompanyNameSourceField().trim());
        }

        HubSpotCustomProperties company = hubSpotClient.getHubSpotObjectWithProperties(
            HubSpotCompanyProperties.API_NAME,
            accountCrmId,
            companyProperties
        );

        // Account fields
        AccountJson accountJson = new AccountJson();

        accountJson.setCrmId(company.id());

        String name = company.properties().get(HubSpotCompanyProperties.NAME_API_NAME);
        accountJson.setName(StringUtils.isNotBlank(name) ? name : StringUtils.EMPTY);

        String description = company.properties().get(HubSpotCompanyProperties.DESCRIPTION_API_NAME);
        accountJson.setDescription(StringUtils.isNotBlank(description) ? description : StringUtils.EMPTY);

        // If alternate account name custom field is populated on company, map this to Subskribe account name.
        if (
            StringUtils.isNotBlank(alternateAccountNameSourceField) &&
            StringUtils.isNotBlank(company.properties().get(alternateAccountNameSourceField))
        ) {
            accountJson.setName(company.properties().get(alternateAccountNameSourceField));
        }

        // Account address fields
        AccountAddressJson accountAddressJson = new AccountAddressJson();

        String address = company.properties().get(HubSpotCompanyProperties.ADDRESS_API_NAME);
        accountAddressJson.setStreetAddressLine1(StringUtils.isNotBlank(address) ? address : StringUtils.EMPTY);

        String zip = company.properties().get(HubSpotCompanyProperties.ZIP_API_NAME);
        accountAddressJson.setZipcode(StringUtils.isNotBlank(zip) ? zip : StringUtils.EMPTY);

        String country = company.properties().get(HubSpotCompanyProperties.COUNTRY_API_NAME);
        accountAddressJson.setCountry(StringUtils.isNotBlank(country) ? country : StringUtils.EMPTY);

        String city = company.properties().get(HubSpotCompanyProperties.CITY_API_NAME);
        accountAddressJson.setCity(StringUtils.isNotBlank(city) ? city : StringUtils.EMPTY);

        String state = company.properties().get(HubSpotCompanyProperties.STATE_API_NAME);
        accountAddressJson.setState(StringUtils.isNotBlank(state) ? state : StringUtils.EMPTY);

        // Check for partial address
        if (
            StringUtils.isNotBlank(accountAddressJson.getStreetAddressLine1()) ||
            StringUtils.isNotBlank(accountAddressJson.getCity()) ||
            StringUtils.isNotBlank(accountAddressJson.getState()) ||
            StringUtils.isNotBlank(accountAddressJson.getZipcode()) ||
            StringUtils.isNotBlank(accountAddressJson.getCountry())
        ) {
            accountJson.setAddress(accountAddressJson);
        }
        fetchAccountCurrency(hubSpotClient, accountCrmId, accountJson, optionalOpportunityCrmId);
        return accountJson;
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private void fetchAccountCurrency(
        HubSpotClient hubSpotClient,
        String accountCrmId,
        AccountJson accountJson,
        Optional<String> optionalOpportunityCrmId
    ) {
        HubSpotIntegration hubSpotIntegration = hubSpotIntegrationService.getIntegrationForTenant();
        CrmAccountCurrencySourceType accountCurrencySourceType;
        if (
            Objects.nonNull(hubSpotIntegration.getConfiguration()) &&
            Objects.nonNull(hubSpotIntegration.getConfiguration().accountCurrencySourceType())
        ) {
            accountCurrencySourceType = hubSpotIntegration.getConfiguration().accountCurrencySourceType();
        } else {
            accountCurrencySourceType = CrmAccountCurrencySourceType.DEAL_CURRENCY;
        }
        switch (accountCurrencySourceType) {
            case DEAL_CURRENCY -> fetchAccountCurrencyUsingDealCurrency(hubSpotClient, accountJson, optionalOpportunityCrmId);
            case COMPANY_CURRENCY -> {
                HubSpotAccountDetails hubSpotAccountDetails = hubSpotClient.getHubSpotAccountDetails();
                accountJson.setCurrency(hubSpotAccountDetails.companyCurrency());
            }
            case ACCOUNT_CRM_FIELD -> {
                String currencyField = hubSpotIntegration.getConfiguration().accountCurrencySourceField();
                if (StringUtils.isBlank(currencyField)) {
                    throw new ConflictingStateException("currency field name missing for account currency sync");
                }
                HubSpotCustomProperties hubSpotCompany = hubSpotClient.getHubSpotObjectWithProperties(
                    HubSpotCompanyProperties.API_NAME,
                    accountCrmId,
                    List.of(currencyField)
                );
                String currency = hubSpotCompany.properties().get(currencyField);
                if (StringUtils.isBlank(currency)) {
                    throw new ConflictingStateException("currency value missing for account currency sync");
                }
                accountJson.setCurrency(currency);
            }
        }
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private void fetchAccountCurrencyUsingDealCurrency(
        HubSpotClient hubSpotClient,
        AccountJson accountJson,
        Optional<String> optionalOpportunityCrmId
    ) {
        String currency = StringUtils.EMPTY;
        if (optionalOpportunityCrmId.isPresent()) {
            var deal = hubSpotClient
                .getDealById(optionalOpportunityCrmId.get())
                .orElseThrow(() -> new ConflictingStateException("deal not found on hubspot: " + optionalOpportunityCrmId.get()));
            currency = deal.getProperties().getDealCurrencyCode();
        }
        if (StringUtils.isBlank(currency)) {
            HubSpotAccountDetails hubSpotAccountDetails = hubSpotClient.getHubSpotAccountDetails();
            currency = hubSpotAccountDetails.companyCurrency();
        }
        accountJson.setCurrency(currency);
    }

    private LockItem waitForLockOrThrow(String key) {
        String fullKey = String.format(HUBSPOT_SYNC_LOCK_KEY_FORMAT, tenantIdProvider.provideTenantIdString(), key);
        return dynamoDBLockProvider.waitForLockOrThrow(fullKey, LOCK_RETRY_INTERVAL_SECONDS, LOCK_WAIT_SECONDS);
    }

    public void syncEsignDetailsForOrderToHubSpot(String orderId, String esignStatus, String esignLink) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        if (!hubSpotIntegrationService.hasCompletedHubSpotIntegration(tenantId)) {
            return;
        }

        Order order;
        try {
            order = orderGetService.getOrderByOrderId(orderId);
        } catch (ObjectNotFoundException e) {
            LOGGER.info("Skipping HubSpot eSign sync. {} not found.", orderId);
            return;
        }

        Validator.validateStringNotBlank(order.getSfdcOpportunityId(), "opportunity id is required.");

        HubSpotClient hubSpotClient = hubspotClientFactory.fromHubspotIntegration(hubSpotIntegrationService.getIntegrationForTenant());

        Optional<HubSpotDeal> dealOptional = hubSpotClient.getDealById(order.getSfdcOpportunityId());
        if (dealOptional.isEmpty()) {
            LOGGER.info("Could not find an HubSpot ID for {}", order.getOrderId());
            return;
        }

        List<String> hubSpotDealProperties = getHubSpotObjectPropertyNames(hubSpotClient, HubSpotDealProperties.API_NAME);
        HubSpotDealProperties properties = new HubSpotDealProperties();

        // Only sync if the fields exist
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ESIGN_STATUS_API_NAME)) {
            properties.setSubskribeEsignStatus(esignStatus);
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ESIGN_LINK_API_NAME)) {
            properties.setSubskribeEsignLink(esignLink);
        }

        HubSpotObject<HubSpotDealProperties> hubSpotDeal = new HubSpotObject<>();
        hubSpotDeal.setProperties(properties);

        hubSpotClient.updateObject(HubSpotDealProperties.API_NAME, order.getSfdcOpportunityId(), hubSpotDeal);
    }

    // somewhat hacky (and temporary) way to check if "date" type is used for temporal fields, as opposed
    // to "datetime" field used earlier.
    // this helps in setting the timestamp accordingly for the target fields.
    // it checks the type of subskribe start date property on deal and if it is set as "date",
    // it assumes all fields will be of "date" type.
    // todo - delete this once all clients run set up and migrate to new schema with "date" type fields
    private boolean checkIfUtcDateType(List<HubSpotProperty> dealProperties) {
        return dealProperties
            .stream()
            .filter(property -> Objects.equals(property.getName(), HubSpotDealProperties.SUBSKRIBE_TERM_START_DATE_API_NAME))
            .anyMatch(property -> Objects.equals(property.getType(), DATE_TYPE));
    }

    private static String formatDate(Instant instant, TimeZone timeZone, boolean isEndDate, boolean isUtcDate) {
        if (Objects.isNull(instant)) {
            return StringUtils.EMPTY;
        }
        Instant targetInstant;
        if (isUtcDate) {
            Instant startOfDay = DateTimeConverter.getStartOfCurrentDay(instant, timeZone);
            targetInstant = DateTimeConverter.convertSameDateTime(startOfDay, timeZone.toZoneId(), ZoneOffset.UTC);
        } else {
            targetInstant = instant;
        }
        if (isEndDate) {
            targetInstant = targetInstant.minus(Duration.ofDays(1));
        }
        return String.valueOf(targetInstant);
    }

    public void pushCompositeOrder(String compositeOrderId) throws IOException {
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        switch (compositeOrder.getType()) {
            case UPSELL_AND_EARLY_RENEWAL -> LOGGER.debug("Unsupported composite order type");
            case CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE -> pushCancelAndRestructure(compositeOrderId);
        }
    }

    private void pushCancelAndRestructure(String compositeOrderId) throws IOException {
        String tenantId = tenantIdProvider.provideTenantIdString();
        if (
            !hubSpotIntegrationService.hasCompletedHubSpotIntegration(tenantId) ||
            !featureService.isEnabled(Feature.CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE)
        ) {
            return;
        }
        List<String> errors = new ArrayList<>();

        HubSpotIntegration hubSpotIntegration = hubSpotIntegrationService.getIntegrationForTenant();
        HubSpotClient hubSpotClient = hubspotClientFactory.fromHubspotIntegration(hubSpotIntegration);

        String orderQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderProperties.API_NAME);
        List<HubSpotProperty> dealProperties = hubSpotClient.getObjectProperties(HubSpotDealProperties.API_NAME);
        List<String> hubSpotDealPropertyNames = dealProperties.stream().map(HubSpotProperty::getName).toList();
        boolean isUtcDate = checkIfUtcDateType(dealProperties);
        Set<String> hubSpotCurrencies = hubSpotGetService.getHubSpotCurrencies(hubSpotClient);
        List<String> customObjectSchemaNames = hubSpotClient.getCustomObjectSchemaList().stream().map(HubSpotCustomObjectSchema::getName).toList();

        try (LockItem ignored = waitForLockOrThrow(compositeOrderId)) {
            CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
            if (StringUtils.isBlank(compositeOrder.getCrmOpportunityId())) {
                return;
            }

            Opportunity opportunity = opportunityGetService.getOpportunityByCrmOpportunityId(compositeOrder.getCrmOpportunityId());
            List<Order> orders = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrder.getCompositeOrderId());
            if (CollectionUtils.isEmpty(orders) || StringUtils.isBlank(opportunity.getCrmId())) {
                return;
            }

            Order restructureOrder = orders.stream().filter(subOrder -> subOrder.getOrderType() == OrderType.RESTRUCTURE).findFirst().orElseThrow();
            Order cancelOrder = orders.stream().filter(subOrder -> subOrder.getOrderType() == OrderType.CANCEL).findFirst().orElseThrow();

            syncSwitchedOpportunitiesForCompositeOrder(compositeOrder);

            // Only sync to deal if the composite order is primary
            if (
                StringUtils.isNotBlank(opportunity.getPrimaryOrderId()) &&
                opportunity.getPrimaryOrderId().equals(compositeOrder.getCompositeOrderId())
            ) {
                pushDealPropertiesForCancelAndRestructure(
                    hubSpotClient,
                    compositeOrder,
                    restructureOrder,
                    cancelOrder,
                    opportunity,
                    hubSpotDealPropertyNames,
                    hubSpotCurrencies,
                    isUtcDate
                );
                if (!isBasic(tenantIdProvider, billyConfiguration) && customObjectSchemaNames.contains(HubSpotOrderProperties.API_NAME)) {
                    Metrics orderMetrics = metricsService.getOrderMetrics(restructureOrder.getOrderId(), Instant.now());
                    HubSpotObject<HubSpotOrderProperties> hubspotOrder = pushOrder(
                        hubSpotClient,
                        hubSpotIntegration,
                        restructureOrder,
                        orderMetrics,
                        opportunity,
                        isUtcDate,
                        customObjectSchemaNames,
                        errors
                    );
                    syncCustomFieldsForOrderToHubSpot(hubSpotClient, orderQualifiedName, hubspotOrder.getId(), restructureOrder.getOrderId(), errors);
                    if (customObjectSchemaNames.contains(HubSpotOrderLineProperties.API_NAME)) {
                        pushCancelAndRestructureOrderLines(
                            hubSpotClient,
                            hubSpotIntegration,
                            hubspotOrder,
                            restructureOrder,
                            cancelOrder,
                            isUtcDate,
                            customObjectSchemaNames,
                            errors
                        );
                    }
                    cleanupRelatedCompositeOrders(hubSpotClient, hubSpotIntegration, restructureOrder, opportunity);
                    verifyPostedOrder(hubSpotClient, hubSpotIntegration, hubspotOrder);
                }
            }
        }
        throwIfSyncErrorsAreFound(errors);
    }

    private void syncSwitchedOpportunitiesForCompositeOrder(CompositeOrder compositeOrder) {
        List<Opportunity> opportunities = opportunityGetService.getOpportunitiesByOrderId(compositeOrder.getCompositeOrderId());
        String linkedOpportunityCrmId = compositeOrder.getCrmOpportunityId();
        List<Opportunity> switchedOpportunities = opportunities
            .stream()
            .filter(opportunity -> !opportunity.getCrmId().equals(linkedOpportunityCrmId))
            .toList();
        if (CollectionUtils.isNotEmpty(switchedOpportunities)) {
            for (Opportunity opportunity : switchedOpportunities) {
                switchHubSpotDealForCompositeOrder(opportunity);
            }
        }
    }

    private void switchHubSpotDealForCompositeOrder(Opportunity opportunity) {
        resetDeal(opportunity.getCrmId());
        opportunityService.deleteOrphanOpportunityByCrmIdIfNeeded(opportunity.getCrmId());
        //TODO: Extend this further for other scenario - check next primary composite order and sync it
    }

    private void pushCancelAndRestructureOrderLines(
        HubSpotClient hubSpotClient,
        HubSpotIntegration hubSpotIntegration,
        HubSpotObject<HubSpotOrderProperties> hubspotOrder,
        Order restructureOrder,
        Order cancelOrder,
        boolean isUtcDate,
        List<String> customObjectSchemaNames,
        List<String> errors
    ) {
        try {
            String orderLineQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderLineProperties.API_NAME);
            String orderQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotOrderProperties.API_NAME);
            Optional<HubSpotObjectAssociation> associationOptional = hubSpotGetService.getHubSpotObjectAssociationId(
                hubSpotClient,
                orderLineQualifiedName,
                orderQualifiedName
            );

            // No association between Order and Order Lines
            if (associationOptional.isEmpty()) {
                return;
            }

            List<HubSpotOrderLine> existingOrderLines = hubSpotGetService.getExistingOrderLines(hubSpotClient, hubspotOrder.getId());
            List<String> hubSpotOrderLineProperties = getHubSpotObjectPropertyNames(hubSpotClient, orderLineQualifiedName);

            LOGGER.info("existing order lines: {}", existingOrderLines.stream().map(HubSpotOrderLine::getId).toList());
            if (CollectionUtils.isNotEmpty(existingOrderLines)) {
                // delete existing order lines
                List<String> existingIds = existingOrderLines.stream().map(HubSpotOrderLine::getId).toList();
                hubSpotClient.archiveMany(orderLineQualifiedName, existingIds);
            }
            List<OrderLineItem> lineItems = cancelAndRestructureOrderDataAggregator.getDistinctOrderLinesForCancelAndRestructure(
                restructureOrder,
                List.of(cancelOrder)
            );

            List<HubSpotOrderLine> createdOrderLines = createOrderLines(
                hubSpotClient,
                restructureOrder,
                hubspotOrder,
                lineItems,
                orderLineQualifiedName,
                orderQualifiedName,
                hubSpotOrderLineProperties,
                isUtcDate
            );
            LOGGER.info("created order lines: {}", createdOrderLines.stream().map(HubSpotOrderLine::getId).toList());
            syncCustomFieldsForOrderLinesToHubSpot(
                hubSpotClient,
                orderLineQualifiedName,
                createdOrderLines,
                lineItems,
                restructureOrder.getOrderId(),
                errors
            );
            if (featureService.isEnabled(Feature.CRM_RATE_CARD)) {
                pushRateCardsForOrderLines(hubSpotClient, hubSpotIntegration, lineItems, createdOrderLines, customObjectSchemaNames);
            }
        } catch (IOException e) {
            LOGGER.info("Could not sync HubSpot order line details: {}", restructureOrder.getOrderId(), e);
        }
    }

    private void pushDealPropertiesForCancelAndRestructure(
        HubSpotClient hubSpotClient,
        CompositeOrder compositeOrder,
        Order restructureOrder,
        Order cancelOrder,
        Opportunity opportunity,
        List<String> hubSpotDealProperties,
        Set<String> hubSpotCurrencies,
        boolean isUtcDate
    ) throws IOException {
        String siteUrl = billyConfiguration.getSiteUrl();
        String compositeOrderId = restructureOrder.getCompositeOrderId();
        String compositeOrderLink = SubskribeUrlGenerator.getUiUrlForCompositeOrder(siteUrl, compositeOrderId);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        HubSpotDealProperties properties = new HubSpotDealProperties();
        Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, restructureOrder.getLineItems());

        // order fields populated from composite order
        properties.setSubskribePrimaryOrderLink(compositeOrderLink);
        properties.setSubskribeOrderStatus(StringUtils.capitalize(compositeOrder.getStatus().name().toLowerCase()));
        properties.setOrderId(compositeOrderId);

        // order fields populated from restructure order
        properties.setOrderTotal(hubSpotMapper.formatValue(BigDecimal.ZERO.add(restructureOrder.getTotalAmount()).add(cancelOrder.getTotalAmount())));
        properties.setSubskribeTermStartDate(formatDate(restructureOrder.getStartDate(), timeZone, false, isUtcDate));
        properties.setSubskribeTermEndDate(formatDate(restructureOrder.getEndDate(), timeZone, true, isUtcDate));
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_AUTO_RENEW_API_NAME)) {
            properties.setSubskribeOrderAutoRenew(String.valueOf(restructureOrder.getAutoRenew()));
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_EXPIRATION_DATE_API_NAME)) {
            properties.setSubskribeOrderExpirationDate(formatDate(restructureOrder.getExpiresOn(), timeZone, false, isUtcDate));
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_START_DATE_TYPE_API_NAME)) {
            properties.setSubskribeOrderStartDateType(restructureOrder.getStartDateType().getDisplayName());
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_DISCOUNT_TOTAL_API_NAME)) {
            properties.setSubskribeOrderDiscountTotal(
                String.valueOf(OrderDiscountService.getNetDiscountAmountCurrencyScaled(restructureOrder.getLineItemsNetEffect(), chargeMap))
            );
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_ORDER_DISCOUNT_PERCENT_API_NAME)) {
            properties.setSubskribeOrderDiscountPercent(
                String.valueOf(OrderDiscountService.calculateOrderLineNetDiscountPercent(restructureOrder.getLineItemsNetEffect(), chargeMap))
            );
        }

        // additional order fields from restructure order
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_PAYMENT_TERMS_API_NAME)) {
            properties.setPaymentTerms(restructureOrder.getPaymentTerm().getDisplayName());
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.BILLING_CYCLE_API_NAME)) {
            properties.setBillingCycle(restructureOrder.getBillingCycle().getCycle().getPeriodicityName());
        }
        if (hubSpotDealProperties.contains(HubSpotDealProperties.SUBSKRIBE_AVERAGE_ACV_API_NAME)) {
            properties.setSubskribeAverageAcv(hubSpotMapper.formatValue(metricsService.getOrderAverageACV(restructureOrder)));
        }
        String orderCurrency = restructureOrder.getCurrency().getCurrencyCode();
        if (hubSpotCurrencies.contains(orderCurrency)) {
            properties.setDealCurrencyCode(orderCurrency);
        }

        // order metrics fields populated from metrics of restructure order as well as cancel order
        Metrics restructureOrderMetrics = metricsService.getOrderMetrics(restructureOrder.getOrderId(), Instant.now());
        Metrics cancelOrderMetrics = metricsService.getOrderMetrics(cancelOrder.getOrderId(), Instant.now());
        properties.setEntryArr(hubSpotMapper.formatValue(restructureOrderMetrics.getEntryArr()));
        properties.setExitArr(hubSpotMapper.formatValue(restructureOrderMetrics.getExitArr()));
        properties.setAverageArr(
            hubSpotMapper.formatValue(BigDecimal.ZERO.add(restructureOrderMetrics.getAverageArr()).add(cancelOrderMetrics.getAverageArr()))
        );
        properties.setDeltaArr(
            hubSpotMapper.formatValue(BigDecimal.ZERO.add(restructureOrderMetrics.getDeltaArr()).add(cancelOrderMetrics.getDeltaArr()))
        );
        // TODO: Missing Non-Recurring Total and Recurring Total on Deal

        // opportunity fields
        String opportunityLink = SubskribeUrlGenerator.getUiUrlForOpportunity(siteUrl, opportunity.getOpportunityId());
        properties.setSubskribeOpportunityLink(opportunityLink);

        // subscription fields
        if (OrderStatus.EXECUTED == compositeOrder.getStatus()) {
            Subscription subscription = subscriptionGetService.getSubscription(restructureOrder.getExternalSubscriptionId());
            properties.setSubscriptionId(subscription.getSubscriptionId());
        }
        hubSpotClient.updateDeal(opportunity.getCrmId(), properties);
    }

    public void deleteHubSpotSubscription(String subscriptionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        if (!hubSpotIntegrationService.hasCompletedHubSpotIntegration(tenantId) || isBasic(tenantIdProvider, billyConfiguration)) {
            return;
        }

        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        Account account = accountGetService.getAccount(subscription.getAccountId());

        if (StringUtils.isBlank(account.getCrmId())) {
            return;
        }

        HubSpotIntegration hubSpotIntegration = hubSpotIntegrationService.getIntegrationForTenant();
        HubSpotClient hubSpotClient = hubspotClientFactory.fromHubspotIntegration(hubSpotIntegration);
        List<String> customObjectSchemaNames = hubSpotClient.getCustomObjectSchemaList().stream().map(HubSpotCustomObjectSchema::getName).toList();

        if (!customObjectSchemaNames.contains(HubSpotSubscriptionProperties.API_NAME)) {
            LOGGER.info("Subscription custom object was not found on HubSpot for tenant: {}", hubSpotIntegration.getTenantId());
            return;
        }

        String subscriptionQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotSubscriptionProperties.API_NAME);
        String subscriptionLineItemQualifiedName = hubSpotGetService.getFullyQualifiedName(
            hubSpotIntegration,
            HubSpotSubscriptionLineItemProperties.API_NAME
        );

        String parentCompanyId = account.getCrmId();
        Optional<String> parentCompanyIdOptional = hubSpotGetService.getParentCompanyIdForMergedCompanyId(hubSpotClient, parentCompanyId);
        if (parentCompanyIdOptional.isPresent() && !StringUtils.equals(parentCompanyIdOptional.get(), parentCompanyId)) {
            LOGGER.info(
                "Merged Company found. Deleting subscription for parent Company ID: {}, child Company: {}",
                parentCompanyIdOptional.get(),
                account.getCrmId()
            );
            parentCompanyId = parentCompanyIdOptional.get();
        }
        Optional<HubSpotSubscription> subscriptionOptional = hubSpotGetService.getExistingSubscriptionRecord(
            hubSpotClient,
            parentCompanyId,
            subscriptionId
        );

        if (subscriptionOptional.isPresent()) {
            Set<String> existingSubscriptionLineIds = new HashSet<>();

            if (customObjectSchemaNames.contains(HubSpotSubscriptionLineItemProperties.API_NAME)) {
                existingSubscriptionLineIds.addAll(
                    hubSpotGetService.getAssociatedObjectIds(
                        hubSpotClient,
                        subscriptionQualifiedName,
                        subscriptionOptional.get().getId(),
                        subscriptionLineItemQualifiedName
                    )
                );
            }

            if (customObjectSchemaNames.contains(HubSpotRateCardProperties.API_NAME)) {
                Set<String> existingRateCardIds = new HashSet<>();
                String rateCardQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotIntegration, HubSpotRateCardProperties.API_NAME);
                for (var subscriptionLineId : existingSubscriptionLineIds) {
                    List<String> rateCardIds = hubSpotGetService.getAssociatedObjectIds(
                        hubSpotClient,
                        subscriptionLineItemQualifiedName,
                        subscriptionLineId,
                        rateCardQualifiedName
                    );
                    existingRateCardIds.addAll(rateCardIds);
                }
                hubSpotClient.archiveMany(rateCardQualifiedName, existingRateCardIds.stream().toList());
            }

            hubSpotClient.archiveMany(subscriptionLineItemQualifiedName, existingSubscriptionLineIds.stream().toList());
            hubSpotClient.archiveMany(subscriptionQualifiedName, List.of(subscriptionOptional.get().getId()));
        }
    }

    public Optional<String> getClientSecret() {
        return secretsService.retrieveHubSpotClientInfo(HubspotClientConstants.HUBSPOT_CLIENT_SECRET_SECRET_NAME);
    }

    public List<HubSpotCustomObjectSchema> getCustomObjectSchemaList() {
        HubSpotClient hubSpotClient = hubspotClientFactory.forTenantIntegration();
        return hubSpotClient.getCustomObjectSchemaList();
    }

    public List<?> getObjectRecords(String objectType, List<String> objectIds) {
        HubSpotClient hubSpotClient = hubspotClientFactory.forTenantIntegration();
        Optional<String> objectSchemaName = getObjectSchemaName(hubSpotClient, objectType);
        if (objectSchemaName.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.HUBSPOT_OBJECT, objectType);
        }
        Class<?> clazz = getClassBySchemaName(objectSchemaName.get());
        List<String> properties = getPropertiesBySchemaName(objectSchemaName.get());
        String objectQualifiedName = hubSpotGetService.getFullyQualifiedName(hubSpotClient.getHubSpotIntegration(), objectSchemaName.get());
        return hubSpotClient.readMany(objectQualifiedName, objectIds.stream().toList(), clazz, properties, Optional.empty()).getResults();
    }

    public Optional<String> getObjectSchemaName(HubSpotClient hubSpotClient, String objectType) {
        List<String> schemaNames = hubSpotClient.getCustomObjectSchemaList().stream().map(HubSpotCustomObjectSchema::getName).toList();
        Optional<String> customObjectMatch = schemaNames.stream().filter(s -> s.equalsIgnoreCase(objectType)).findAny();
        if (customObjectMatch.isPresent()) {
            return customObjectMatch;
        }
        return hubSpotNativeObjectNames.stream().filter(s -> s.equalsIgnoreCase(objectType)).findAny();
    }

    public Class<?> getClassBySchemaName(String objectSchemaName) {
        return switch (objectSchemaName) {
            case HubSpotDealProperties.API_NAME -> (Class<HubSpotObject<HubSpotDealProperties>>) (Class<?>) HubSpotObject.class;
            case HubSpotCompanyProperties.API_NAME -> (Class<HubSpotObject<HubSpotCompanyProperties>>) (Class<?>) HubSpotObject.class;
            case HubSpotContactProperties.API_NAME -> (Class<HubSpotObject<HubSpotContactProperties>>) (Class<?>) HubSpotObject.class;
            case HubSpotOrderProperties.API_NAME -> (Class<HubSpotObject<HubSpotOrderProperties>>) (Class<?>) HubSpotObject.class;
            case HubSpotOrderLineProperties.API_NAME -> (Class<HubSpotObject<HubSpotOrderLineProperties>>) (Class<?>) HubSpotObject.class;
            case HubSpotSubscriptionProperties.API_NAME -> (Class<HubSpotObject<HubSpotSubscriptionProperties>>) (Class<?>) HubSpotObject.class;
            case HubSpotSubscriptionLineItemProperties.API_NAME -> (Class<HubSpotObject<HubSpotSubscriptionLineItemProperties>>) (Class<
                    ?
                >) HubSpotObject.class;
            case HubSpotRateCardProperties.API_NAME -> (Class<HubSpotObject<HubSpotRateCardProperties>>) (Class<?>) HubSpotObject.class;
            case HubSpotOrderAnnualAmountProperties.API_NAME -> (Class<HubSpotObject<HubSpotOrderAnnualAmountProperties>>) (Class<
                    ?
                >) HubSpotObject.class;
            default -> throw new ObjectNotFoundException(BillyObjectType.HUBSPOT_OBJECT, objectSchemaName);
        };
    }

    public List<String> getPropertiesBySchemaName(String objectSchemaName) {
        return switch (objectSchemaName) {
            case HubSpotDealProperties.API_NAME -> HubSpotDealProperties.PROPERTIES;
            case HubSpotCompanyProperties.API_NAME -> HubSpotCompanyProperties.PROPERTIES;
            case HubSpotContactProperties.API_NAME -> HubSpotContactProperties.PROPERTIES;
            case HubSpotOrderProperties.API_NAME -> HubSpotOrderProperties.PROPERTIES;
            case HubSpotOrderLineProperties.API_NAME -> HubSpotOrderLineProperties.PROPERTIES;
            case HubSpotSubscriptionProperties.API_NAME -> HubSpotSubscriptionProperties.PROPERTIES;
            case HubSpotSubscriptionLineItemProperties.API_NAME -> HubSpotSubscriptionLineItemProperties.PROPERTIES;
            case HubSpotRateCardProperties.API_NAME -> HubSpotRateCardProperties.PROPERTIES;
            case HubSpotOrderAnnualAmountProperties.API_NAME -> HubSpotOrderAnnualAmountProperties.PROPERTIES;
            default -> List.of();
        };
    }
}
