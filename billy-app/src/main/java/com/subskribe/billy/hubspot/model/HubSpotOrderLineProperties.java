package com.subskribe.billy.hubspot.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import java.util.List;

public class HubSpotOrderLineProperties {

    public static final String API_NAME = "subskribe_order_lines";
    public static final String NAME_API_NAME = "name";
    public static final String CHARGE_NAME_API_NAME = "charge_name";
    public static final String QUANTITY_API_NAME = "quantity";
    public static final String TCV_API_NAME = "tcv";
    public static final String PRODUCT_CATEGORY_API_NAME = "product_category";
    public static final String PRODUCT_NAME_API_NAME = "product_name";
    public static final String PLAN_NAME_API_NAME = "plan_name";
    public static final String CHARGE_ID_API_NAME = "charge_id";
    public static final String CURRENCY_API_NAME = "currency";
    public static final String SUBSCRIPTION_ID_API_NAME = "subscription_id";
    public static final String LIST_UNIT_PRICE_API_NAME = "list_unit_price";
    public static final String SELL_UNIT_PRICE_API_NAME = "sell_unit_price";
    public static final String DISCOUNT_API_NAME = "discount";
    public static final String ARR_API_NAME = "arr";
    public static final String ACV_API_NAME = "acv";
    public static final String LINE_START_DATE_API_NAME = "line_start_date";
    public static final String LINE_END_DATE_API_NAME = "line_end_date";
    public static final String UUID_API_NAME = "uuid";
    public static final String PRODUCT_EXTERNAL_ID_API_NAME = "product_external_id";
    public static final String PLAN_EXTERNAL_ID_API_NAME = "plan_external_id";
    public static final String CHARGE_EXTERNAL_ID_API_NAME = "charge_external_id";
    public static final String ORDER_ID_API_NAME = "order_id";
    public static final String ITEM_CODE_API_NAME = "item_code";
    public static final String DEAL_ID_API_NAME = "deal_id";
    public static final String SUBSKRIBE_FX_RATE_API_NAME = "subskribe_line_item_fx_rate";
    public static final List<String> PROPERTIES = List.of(
        NAME_API_NAME,
        CHARGE_NAME_API_NAME,
        QUANTITY_API_NAME,
        TCV_API_NAME,
        PRODUCT_CATEGORY_API_NAME,
        PRODUCT_NAME_API_NAME,
        PLAN_NAME_API_NAME,
        CHARGE_ID_API_NAME,
        CURRENCY_API_NAME,
        SUBSCRIPTION_ID_API_NAME,
        LIST_UNIT_PRICE_API_NAME,
        SELL_UNIT_PRICE_API_NAME,
        DISCOUNT_API_NAME,
        ARR_API_NAME,
        ACV_API_NAME,
        LINE_START_DATE_API_NAME,
        LINE_END_DATE_API_NAME,
        UUID_API_NAME,
        PRODUCT_EXTERNAL_ID_API_NAME,
        PLAN_EXTERNAL_ID_API_NAME,
        CHARGE_EXTERNAL_ID_API_NAME,
        ORDER_ID_API_NAME,
        ITEM_CODE_API_NAME,
        DEAL_ID_API_NAME,
        SUBSKRIBE_FX_RATE_API_NAME
    );

    @JsonProperty(NAME_API_NAME)
    @SerializedName(NAME_API_NAME)
    private String name;

    @JsonProperty(CHARGE_NAME_API_NAME)
    @SerializedName(CHARGE_NAME_API_NAME)
    private String chargeName;

    @JsonProperty(QUANTITY_API_NAME)
    @SerializedName(QUANTITY_API_NAME)
    private String quantity;

    @JsonProperty(TCV_API_NAME)
    @SerializedName(TCV_API_NAME)
    private String tcv;

    @JsonProperty(PRODUCT_CATEGORY_API_NAME)
    @SerializedName(PRODUCT_CATEGORY_API_NAME)
    private String productCategory;

    @JsonProperty(PRODUCT_NAME_API_NAME)
    @SerializedName(PRODUCT_NAME_API_NAME)
    private String productName;

    @JsonProperty(PLAN_NAME_API_NAME)
    @SerializedName(PLAN_NAME_API_NAME)
    private String planName;

    @JsonProperty(CHARGE_ID_API_NAME)
    @SerializedName(CHARGE_ID_API_NAME)
    private String chargeId;

    @JsonProperty(CURRENCY_API_NAME)
    @SerializedName(CURRENCY_API_NAME)
    private String currency;

    @JsonProperty(SUBSCRIPTION_ID_API_NAME)
    @SerializedName(SUBSCRIPTION_ID_API_NAME)
    private String subscriptionId;

    @JsonProperty(LIST_UNIT_PRICE_API_NAME)
    @SerializedName(LIST_UNIT_PRICE_API_NAME)
    private String listUnitPrice;

    @JsonProperty(SELL_UNIT_PRICE_API_NAME)
    @SerializedName(SELL_UNIT_PRICE_API_NAME)
    private String sellUnitPrice;

    @JsonProperty(DISCOUNT_API_NAME)
    @SerializedName(DISCOUNT_API_NAME)
    private String discount;

    @JsonProperty(ARR_API_NAME)
    @SerializedName(ARR_API_NAME)
    private String arr;

    @JsonProperty(ACV_API_NAME)
    @SerializedName(ACV_API_NAME)
    private String acv;

    @JsonProperty(LINE_START_DATE_API_NAME)
    @SerializedName(LINE_START_DATE_API_NAME)
    private String lineStartDate;

    @JsonProperty(LINE_END_DATE_API_NAME)
    @SerializedName(LINE_END_DATE_API_NAME)
    private String lineEndDate;

    @JsonProperty(UUID_API_NAME)
    @SerializedName(UUID_API_NAME)
    private String uuid;

    @JsonProperty(PRODUCT_EXTERNAL_ID_API_NAME)
    @SerializedName(PRODUCT_EXTERNAL_ID_API_NAME)
    private String productExternalId;

    @JsonProperty(PLAN_EXTERNAL_ID_API_NAME)
    @SerializedName(PLAN_EXTERNAL_ID_API_NAME)
    private String planExternalId;

    @JsonProperty(CHARGE_EXTERNAL_ID_API_NAME)
    @SerializedName(CHARGE_EXTERNAL_ID_API_NAME)
    private String chargeExternalId;

    @JsonProperty(ORDER_ID_API_NAME)
    @SerializedName(ORDER_ID_API_NAME)
    private String orderId;

    @JsonProperty(ITEM_CODE_API_NAME)
    @SerializedName(ITEM_CODE_API_NAME)
    private String itemCode;

    @JsonProperty(DEAL_ID_API_NAME)
    @SerializedName(DEAL_ID_API_NAME)
    private String dealId;

    @JsonProperty(SUBSKRIBE_FX_RATE_API_NAME)
    @SerializedName(SUBSKRIBE_FX_RATE_API_NAME)
    private String subskribeFxRate;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getChargeName() {
        return chargeName;
    }

    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getTcv() {
        return tcv;
    }

    public void setTcv(String tcv) {
        this.tcv = tcv;
    }

    public String getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getChargeId() {
        return chargeId;
    }

    public void setChargeId(String chargeId) {
        this.chargeId = chargeId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getListUnitPrice() {
        return listUnitPrice;
    }

    public void setListUnitPrice(String listUnitPrice) {
        this.listUnitPrice = listUnitPrice;
    }

    public String getSellUnitPrice() {
        return sellUnitPrice;
    }

    public void setSellUnitPrice(String sellUnitPrice) {
        this.sellUnitPrice = sellUnitPrice;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public String getArr() {
        return arr;
    }

    public void setArr(String arr) {
        this.arr = arr;
    }

    public String getAcv() {
        return acv;
    }

    public void setAcv(String acv) {
        this.acv = acv;
    }

    public String getLineStartDate() {
        return lineStartDate;
    }

    public void setLineStartDate(String lineStartDate) {
        this.lineStartDate = lineStartDate;
    }

    public String getLineEndDate() {
        return lineEndDate;
    }

    public void setLineEndDate(String lineEndDate) {
        this.lineEndDate = lineEndDate;
    }

    public String getUUID() {
        return uuid;
    }

    public void setUUID(String uuid) {
        this.uuid = uuid;
    }

    public String getProductExternalId() {
        return productExternalId;
    }

    public void setProductExternalId(String productExternalId) {
        this.productExternalId = productExternalId;
    }

    public String getPlanExternalId() {
        return planExternalId;
    }

    public void setPlanExternalId(String planExternalId) {
        this.planExternalId = planExternalId;
    }

    public String getChargeExternalId() {
        return chargeExternalId;
    }

    public void setChargeExternalId(String chargeExternalId) {
        this.chargeExternalId = chargeExternalId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getDealId() {
        return dealId;
    }

    public void setDealId(String dealId) {
        this.dealId = dealId;
    }

    public String getSubskribeFxRate() {
        return subskribeFxRate;
    }

    public void setSubskribeFxRate(String subskribeFxRate) {
        this.subskribeFxRate = subskribeFxRate;
    }

    @Override
    public String toString() {
        return (
            "HubSpotOrderLineProperties{" +
            "name='" +
            name +
            '\'' +
            ", chargeName='" +
            chargeName +
            '\'' +
            ", quantity='" +
            quantity +
            '\'' +
            ", tcv='" +
            tcv +
            '\'' +
            ", productCategory='" +
            productCategory +
            '\'' +
            ", productName='" +
            productName +
            '\'' +
            ", planName='" +
            planName +
            '\'' +
            ", chargeId='" +
            chargeId +
            '\'' +
            ", currency='" +
            currency +
            '\'' +
            ", subscriptionId='" +
            subscriptionId +
            '\'' +
            ", listUnitPrice='" +
            listUnitPrice +
            '\'' +
            ", sellUnitPrice='" +
            sellUnitPrice +
            '\'' +
            ", discount='" +
            discount +
            '\'' +
            ", arr='" +
            arr +
            '\'' +
            ", acv='" +
            acv +
            '\'' +
            ", lineStartDate='" +
            lineStartDate +
            '\'' +
            ", lineEndDate='" +
            lineEndDate +
            '\'' +
            ", uuid='" +
            uuid +
            '\'' +
            ", productExternalId='" +
            productExternalId +
            '\'' +
            ", planExternalId='" +
            planExternalId +
            '\'' +
            ", chargeExternalId='" +
            chargeExternalId +
            '\'' +
            ", orderId='" +
            orderId +
            '\'' +
            ", itemCode='" +
            itemCode +
            '\'' +
            ", dealId='" +
            dealId +
            '\'' +
            ", subskribeFxRate='" +
            subskribeFxRate +
            '\'' +
            '}'
        );
    }
}
