package com.subskribe.billy.shared.document;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.order.document.TemplateCustomField;
import com.subskribe.billy.productcatalog.model.UnitOfMeasure;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.util.HashMap;
import java.util.Map;

public class TemplateCharge {

    private final CustomField customField;
    private final DocumentRenderFormatter formatter;
    private final String currencyCode;
    private final Cycle billingCycle;
    private final ChargeJson charge;
    private final UnitOfMeasure unitOfMeasure;

    public TemplateCharge(
        CustomField customField,
        DocumentRenderFormatter formatter,
        String currencyCode,
        Cycle billingCycle,
        ChargeJson charge,
        UnitOfMeasure unitOfMeasure
    ) {
        this.customField = customField == null ? new CustomField(new HashMap<>()) : customField;
        this.formatter = formatter;
        this.currencyCode = currencyCode;
        this.billingCycle = billingCycle;
        this.charge = charge;
        this.unitOfMeasure = unitOfMeasure;
    }

    public String getId() {
        return charge.getId();
    }

    public String getName() {
        return charge.getName();
    }

    public String getDisplayName() {
        return charge.getDisplayName();
    }

    public String getDescription() {
        return charge.getDescription();
    }

    public Map<String, TemplateCustomField> getCustomFields() {
        return TemplateCustomField.getCustomFieldsMap(customField);
    }

    public Map<String, CustomFieldValue> getCustomFieldValues() {
        return customField.getEntries();
    }

    public DocumentRenderFormatter getFormatter() {
        return formatter;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public String getBillingCycle() {
        return formatter.formatBillingCycle(billingCycle);
    }

    // Return boolean tag based on charge's billing cycle. If cycle is order billing cycle, "DEFAULT" is returned.
    // e.g: {{#charge.chargeBillingCycle.DEFAULT}} display if charge uses order billing cycle {{/charge.chargeBillingCycle.DEFAULT}}
    // {{#charge.chargeBillingCycle.YEAR}} display if charge is set to annual billing cycle {{/charge.chargeBillingCycle.YEAR}}
    public Map<String, Boolean> getChargeBillingCycle() {
        String cycle = charge.getBillingCycle().getCycle().map(Cycle::name).orElse("DEFAULT");
        return Map.of(cycle, true);
    }

    public UnitOfMeasure getUnitOfMeasure() {
        return unitOfMeasure;
    }
}
