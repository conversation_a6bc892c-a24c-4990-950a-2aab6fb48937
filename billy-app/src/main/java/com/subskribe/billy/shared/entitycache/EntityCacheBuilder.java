package com.subskribe.billy.shared.entitycache;

import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;

public class EntityCacheBuilder {

    private final ProductCatalogGetService productCatalogGetService;

    // todo: move other instantiations of EntityCache here

    @Inject
    public EntityCacheBuilder(ProductCatalogGetService productCatalogGetService) {
        this.productCatalogGetService = productCatalogGetService;
    }

    public EntityCache<String, Charge> getChargeEntityCache(List<String> seedChargeIds) {
        Map<String, Charge> chargeMap = CollectionUtils.isEmpty(seedChargeIds)
            ? new HashMap<>()
            : productCatalogGetService.getChargeMapByChargeIds(seedChargeIds, true);
        return EntityCache.of(productCatalogGetService::getChargeByChargeId, chargeMap);
    }

    public EntityCache<String, Plan> getPlanEntityCache(Set<String> seedPlanIds) {
        Map<String, Plan> planMap = CollectionUtils.isEmpty(seedPlanIds) ? new HashMap<>() : productCatalogGetService.getPlanMap(seedPlanIds, true);
        return EntityCache.of(productCatalogGetService::getPlan, planMap);
    }
}
