package com.subskribe.billy.shared.webhook;

import static com.subskribe.billy.shared.webhook.IncomingWebhookProductMetadata.CANT_DESERIALIZE_WEBHOOK;
import static com.subskribe.billy.shared.webhook.IncomingWebhookProductMetadata.INCOMING_WEBHOOKS_CORE;
import static com.subskribe.billy.shared.webhook.IncomingWebhookProductMetadata.NO_TENANT_FOUND;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.shared.logger.ErrorContext;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.Partitioned;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import com.subskribe.billy.shared.task.queue.scheduler.TaskDispatcher;
import java.util.HashMap;
import java.util.Map;
import javax.inject.Inject;
import org.glassfish.hk2.api.IterableProvider;

public class IncomingWebhookTaskDispatcher {

    private final Map<IncomingWebhookType, IncomingWebhookProcessor> processorMap;
    private final ObjectMapper objectMapper;
    private final TaskDispatcher taskDispatcher;
    private final WebhookDataStore webhookDataStore;

    private static final Logger LOGGER = LoggerFactory.getLogger(IncomingWebhookTaskDispatcher.class);

    @Inject
    public IncomingWebhookTaskDispatcher(
        IterableProvider<IncomingWebhookProcessor> incomingWebhookProcessors,
        TaskDispatcher taskDispatcher,
        WebhookDataStore webhookDataStore
    ) {
        this.taskDispatcher = taskDispatcher;
        this.webhookDataStore = webhookDataStore;
        Map<IncomingWebhookType, IncomingWebhookProcessor> processors = new HashMap<>();
        for (IncomingWebhookProcessor processor : incomingWebhookProcessors) {
            if (processors.containsKey(processor.getWebhookType())) {
                throw new IllegalStateException("Multiple processors for incoming webhook type " + processor.getWebhookType());
            }
            processors.put(processor.getWebhookType(), processor);
        }
        processorMap = Map.copyOf(processors);
        objectMapper = JacksonProvider.defaultMapper();
    }

    @VisibleForTesting
    public IncomingWebhookTaskDispatcher(
        Map<IncomingWebhookType, IncomingWebhookProcessor> processorMap,
        ObjectMapper objectMapper,
        TaskDispatcher taskDispatcher,
        WebhookDataStore webhookDataStore
    ) {
        this.processorMap = processorMap;
        this.objectMapper = objectMapper;
        this.taskDispatcher = taskDispatcher;
        this.webhookDataStore = webhookDataStore;
    }

    public void dispatch(IncomingWebhook incomingWebhook) {
        IncomingWebhookProcessor processor = processorMap.get(incomingWebhook.getWebhookType());
        if (processor == null) {
            throw new IllegalStateException("No processor found for incoming webhook type " + incomingWebhook.getWebhookType().name());
        }

        try {
            if (!processor.validate(incomingWebhook)) {
                throw new WebhookValidationFailedException("Could not validate incoming webhook");
            }
        } catch (WebhookValidationFailedException e) {
            throw e; // Re-throw specific validation exception
        } catch (Exception e) {
            throw new WebhookValidationFailedException("Encountered exception when validating incoming webhook", e);
        }

        IncomingWebhookTenantLookupResult tenantLookupResult = processor.findTenantId(incomingWebhook);
        if (tenantLookupResult.tenantId().isEmpty()) {
            processTenantNotFound(incomingWebhook, tenantLookupResult);
            return;
        }

        String payloadStoreKey = webhookDataStore.save(tenantLookupResult.tenantId().get(), incomingWebhook);

        PersistedWebhook persistedWebhook = ImmutablePersistedWebhook.builder()
            .webhookType(incomingWebhook.getWebhookType())
            .externalStorageKey(payloadStoreKey)
            .receivedOn(incomingWebhook.getReceivedOn())
            .build();

        String taskData;
        try {
            taskData = objectMapper.writeValueAsString(persistedWebhook);
        } catch (JsonProcessingException e) {
            String failureReason = "Failed to serialize incoming webhook";
            LOGGER.error(new ErrorContext(INCOMING_WEBHOOKS_CORE, CANT_DESERIALIZE_WEBHOOK), failureReason, e);
            throw new ServiceFailureException(failureReason);
        }
        String partitionKey = String.format("%s-%s", tenantLookupResult.tenantId().get(), incomingWebhook.getWebhookType().name());

        QueuedTaskRequest queuedTaskRequest = ImmutableQueuedTaskRequest.builder()
            .module(new TaskModule(IncomingWebhookRouter.MODULE_NAME))
            .type(new TaskType(IncomingWebhookRouter.TASK_TYPE))
            .taskData(taskData)
            .tenantId(tenantLookupResult.tenantId().get())
            .taskOrder(Partitioned.with(partitionKey))
            .build();

        taskDispatcher.scheduleTask(queuedTaskRequest);
    }

    private static void processTenantNotFound(IncomingWebhook incomingWebhook, IncomingWebhookTenantLookupResult result) {
        String errorMessage = "Could not find tenant for incoming webhook type " + incomingWebhook.getWebhookType().name();
        if (result.ignoreFailures()) {
            LOGGER.warn(INCOMING_WEBHOOKS_CORE, errorMessage);
            return;
        }
        LOGGER.error(new ErrorContext(INCOMING_WEBHOOKS_CORE, NO_TENANT_FOUND), errorMessage);
        throw new ServiceFailureException(errorMessage);
    }
}
