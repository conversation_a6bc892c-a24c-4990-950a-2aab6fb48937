package com.subskribe.billy.shared.task.queue.scheduler;

import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.task.queue.db.QueuedTaskDAO;
import com.subskribe.billy.shared.task.queue.db.QueuedTaskSelectionDAO;
import com.subskribe.billy.shared.task.queue.distribution.TaskDistributor;
import com.subskribe.billy.shared.task.queue.metrics.TaskMetricPublisher;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.TaskOrder;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.Queue;
import java.util.Set;
import javax.inject.Inject;

public class TaskScheduler {

    private final QueuedTaskSelectionDAO queuedTaskSelectionDAO;
    private final QueuedTaskDAO queuedTaskDAO;
    private final TaskDistributor taskDistributor;
    private final TaskMetricPublisher taskMetricPublisher;

    private static final Logger LOGGER = LoggerFactory.getLogger(TaskScheduler.class);
    private static final Comparator<QueuedTask> TASK_ORDER_COMPARATOR = Comparator.comparingLong(t ->
        // If we task reached this stage we can be sure that the task order is present. However, we'll de-prioritize
        // the tasks that do not have it by using Long.MAX_VALUE for runtime safety
        t.getTaskOrder().map(TaskOrder::sequenceNumber).orElse(Long.MAX_VALUE)
    );

    @Inject
    public TaskScheduler(
        QueuedTaskSelectionDAO queuedTaskSelectionDAO,
        QueuedTaskDAO queuedTaskDAO,
        TaskDistributor taskDistributor,
        TaskMetricPublisher taskMetricPublisher
    ) {
        this.queuedTaskSelectionDAO = queuedTaskSelectionDAO;
        this.queuedTaskDAO = queuedTaskDAO;
        this.taskDistributor = taskDistributor;
        this.taskMetricPublisher = taskMetricPublisher;
    }

    public void scheduleTasks() {
        int currentCapacity = taskDistributor.getTotalCapacity();
        List<QueuedTask> tasksReadyToRunImmediately = queuedTaskSelectionDAO.getTasksReadyToRunImmediately();

        if (currentCapacity == 0) {
            LOGGER.debug("No capacity available to schedule tasks");
            taskMetricPublisher.publishMetrics(currentCapacity, tasksReadyToRunImmediately.size());
            return;
        }

        List<QueuedTask> tasksToDistribute = getTasksToDistribute(currentCapacity, tasksReadyToRunImmediately);
        taskMetricPublisher.publishMetrics(currentCapacity, tasksReadyToRunImmediately.size(), tasksToDistribute.size());
        queuedTaskDAO.setTasksToInProgress(tasksToDistribute.stream().map(QueuedTask::getTaskId).toList());
        taskDistributor.distribute(tasksToDistribute);
    }

    private List<QueuedTask> getTasksToDistribute(int currentCapacity, List<QueuedTask> availableTasks) {
        Map<String, Queue<QueuedTask>> tasksByTenant = convertTasksToTenantQueues(availableTasks);

        List<QueuedTask> scheduledTasks = new ArrayList<>();

        while (currentCapacity > 0 && !tasksByTenant.isEmpty()) {
            Iterator<Map.Entry<String, Queue<QueuedTask>>> iterator = tasksByTenant.entrySet().iterator();
            Set<String> toRemove = new HashSet<>();
            while (iterator.hasNext() && currentCapacity > 0) {
                Map.Entry<String, Queue<QueuedTask>> currentEntry = iterator.next();
                Queue<QueuedTask> tenantTasks = currentEntry.getValue();
                if (!tenantTasks.isEmpty()) {
                    QueuedTask task = tenantTasks.poll();
                    scheduledTasks.add(task);
                    currentCapacity--;
                }
                if (tenantTasks.isEmpty()) {
                    toRemove.add(currentEntry.getKey());
                }
            }
            toRemove.forEach(tasksByTenant::remove);
        }
        return scheduledTasks;
    }

    private static Map<String, Queue<QueuedTask>> convertTasksToTenantQueues(List<QueuedTask> availableTasks) {
        // Using a LinkedHashMap to maintain the order of the tasks by tenant. The tenant with the oldest task
        // will be the first to be inserted, which is preserved when iterating over the map
        Map<String, Queue<QueuedTask>> tasksByTenant = new LinkedHashMap<>();
        List<QueuedTask> sortedTasks = new ArrayList<>(availableTasks);
        sortedTasks.sort(TASK_ORDER_COMPARATOR);
        for (QueuedTask queuedTask : sortedTasks) {
            tasksByTenant.computeIfAbsent(queuedTask.getTenantId(), k -> new PriorityQueue<>(TASK_ORDER_COMPARATOR)).add(queuedTask);
        }
        return tasksByTenant;
    }
}
