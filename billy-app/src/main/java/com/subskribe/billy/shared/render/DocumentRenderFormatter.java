package com.subskribe.billy.shared.render;

import static com.subskribe.billy.shared.enums.Cycle.MONTH;
import static com.subskribe.billy.shared.enums.Cycle.YEAR;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.order.document.temporal.TemplatePeriod;
import com.subskribe.billy.productcatalog.model.UnitOfMeasure;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.FormatStyle;
import java.util.Currency;
import java.util.IllformedLocaleException;
import java.util.Locale;
import java.util.MissingResourceException;
import java.util.Objects;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.TimeZone;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.LocaleUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

public class DocumentRenderFormatter {

    private static final int PERCENTAGE_SCALE = 2;

    public static final String US_DATE_FORMAT = "MM/dd/yyyy";

    public static final String INTERNATIONAL_DATE_FORMAT = "dd/MM/yyyy";

    private static final Logger LOGGER = LoggerFactory.getLogger(DocumentRenderFormatter.class);

    private static final PhoneNumberUtil PHONE_UTIL = PhoneNumberUtil.getInstance();

    private int currencyDisplayMinDecimals = 2;

    private int currencyDisplayMaxDecimals = 2;

    private static final String PAYMENT_TERM_NET = "Net";

    private static final String BILLING_CYCLE_RECURRENCE_FORMAT_KEY = "billingCycle.recurrence.format";

    private static final String LOCALIZATION_FILE_BASE_PATH = "localization/localization";

    private static final Locale EN_US_LOCALE = new Locale("en", "US");

    private final ZoneId zoneId;

    private final TimeZone timeZone;

    private Locale locale;

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private Optional<String> dateFormatPattern;

    public DocumentRenderFormatter(TimeZone timeZone) {
        zoneId = timeZone.toZoneId();
        this.timeZone = timeZone;
        locale = Locale.US;
        dateFormatPattern = Optional.empty();
    }

    public DocumentRenderFormatter(TimeZone timeZone, String currency) {
        zoneId = timeZone.toZoneId();
        this.timeZone = timeZone;
        locale = Locale.US;
        dateFormatPattern = Optional.empty();
        Integer currencyPrecision = SupportedCurrency.getCurrencyPrecision(currency);
        currencyDisplayMinDecimals = currencyPrecision;
        currencyDisplayMaxDecimals = currencyPrecision;
    }

    public TimeZone getTimeZone() {
        return timeZone;
    }

    public void updateLocale(String localeString) {
        Validator.validateStringNotBlank(localeString, "localeString");
        try {
            locale = new Locale.Builder().setLanguageTag(localeString).build();
            if (!LocaleUtils.isAvailableLocale(locale)) {
                throw new InvalidInputException(localeString + " is not a valid locale.");
            }
        } catch (IllformedLocaleException ex) {
            throw new InvalidInputException(localeString + " is a malformed locale code");
        }
    }

    public void setDateFormatPattern(String pattern) {
        if (StringUtils.isBlank(pattern)) {
            return;
        }

        dateFormatPattern = Optional.of(pattern);
    }

    public void setCurrencyDisplayMaxDecimals(int decimals) {
        currencyDisplayMaxDecimals = decimals;
    }

    public void setCurrencyDisplayMinDecimals(int decimals) {
        currencyDisplayMinDecimals = decimals;
    }

    public String currencyFormat(BigDecimal bigDecimal, String currencyCode) {
        return currencyFormat(bigDecimal, currencyCode, currencyDisplayMaxDecimals);
    }

    public String unitPriceCurrencyFormat(BigDecimal bigDecimal, String currencyCode) {
        return currencyFormat(bigDecimal, currencyCode, Numbers.PRICE_DISPLAY_SCALE);
    }

    public String currencyFormat(BigDecimal price, String currencyCode, String precisionString) {
        int defaultPrecision = SupportedCurrency.getCurrencyPrecision(currencyCode);
        int precision = NumberUtils.toInt(precisionString, defaultPrecision);
        if (precision < defaultPrecision || precision > Numbers.PRICE_DISPLAY_SCALE) {
            // if input is < default or greater than price display value, use default
            precision = defaultPrecision;
        }

        return currencyFormat(price, currencyCode, precision);
    }

    public String currencyFormat(BigDecimal bigDecimal, String currencyCode, int maxFractionalDigits) {
        return currencyFormat(bigDecimal, currencyCode, currencyDisplayMinDecimals, maxFractionalDigits);
    }

    public String currencyFormat(BigDecimal bigDecimal, String currencyCode, int minFractionalDigits, int maxFractionalDigits) {
        if (bigDecimal == null) {
            return StringUtils.EMPTY;
        }

        NumberFormat formatter = getCurrencyFormatter(currencyCode);
        formatter.setMaximumFractionDigits(maxFractionalDigits);
        formatter.setMinimumFractionDigits(minFractionalDigits);
        formatter.setRoundingMode(RoundingMode.HALF_UP);

        return formatter.format(bigDecimal);
    }

    private NumberFormat getCurrencyFormatter(String currencyCode) {
        NumberFormat formatter = NumberFormat.getCurrencyInstance(locale);
        if (StringUtils.isBlank(currencyCode)) {
            formatter.setCurrency(SupportedCurrency.getDefaultCurrency());
        } else {
            formatter.setCurrency(Currency.getInstance(currencyCode));
        }
        return (NumberFormat) formatter.clone();
    }

    public String numberFormat(Long number) {
        return (number == null) ? StringUtils.EMPTY : NumberFormat.getIntegerInstance().format(number);
    }

    public String dateFormat(Long instant) {
        return instant == null ? StringUtils.EMPTY : dateFormat(Instant.ofEpochSecond(instant));
    }

    public String dateFormat(Instant instant) {
        if (instant == null) {
            return StringUtils.EMPTY;
        }
        return getDateFormatter().format(instant);
    }

    public String dateFormat(String pattern, Instant instant) {
        if (StringUtils.isEmpty(pattern) || instant == null) {
            return StringUtils.EMPTY;
        }

        return getDateFormatter(pattern).format(instant);
    }

    public String endDateFormat(String pattern, Instant instant) {
        if (StringUtils.isEmpty(pattern) || instant == null) {
            return StringUtils.EMPTY;
        }

        return getDateFormatter(pattern).format(instant.minusSeconds(1));
    }

    public String endDateFormat(Long instant) {
        return instant == null ? StringUtils.EMPTY : endDateFormat(Instant.ofEpochSecond(instant));
    }

    public String endDateFormat(Instant instant) {
        if (instant == null) {
            return StringUtils.EMPTY;
        }
        return getDateFormatter().format(instant.minusSeconds(1));
    }

    private DateTimeFormatter getDateFormatter() {
        return dateFormatPattern
            .map(DateTimeFormatter::ofPattern)
            .orElse(DateTimeFormatter.ofLocalizedDate(FormatStyle.MEDIUM))
            .withZone(zoneId)
            .withLocale(locale);
    }

    private DateTimeFormatter getDateFormatter(String pattern) {
        return DateTimeFormatter.ofPattern(pattern).withZone(zoneId).withLocale(locale);
    }

    public String phoneNumberFormat(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return phoneNumber;
        }

        try {
            Phonenumber.PhoneNumber phone = PHONE_UTIL.parse(phoneNumber, "US"); // todo: remove hard coded region
            return PHONE_UTIL.format(phone, PhoneNumberUtil.PhoneNumberFormat.NATIONAL);
        } catch (NumberParseException e) {
            LOGGER.debug("Unable to parse phone number: {}", phoneNumber);
            return phoneNumber;
        }
    }

    public String formatUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        if (unitOfMeasure == null || unitOfMeasure.getName() == null) {
            return null;
        }
        return unitOfMeasure.getName();
    }

    // Return enum value as title case string
    public <T extends Enum<T>> String formatEnum(T enumVal) {
        if (Objects.isNull(enumVal)) {
            return StringUtils.EMPTY;
        }
        return StringUtils.capitalize(enumVal.name().toLowerCase());
    }

    public String formatPercent(BigDecimal percent) {
        return formatPercent(percent, PERCENTAGE_SCALE);
    }

    public String formatPercent(BigDecimal percent, int scale) {
        NumberFormat numberFormatter = NumberFormat.getNumberInstance(locale);
        numberFormatter.setMaximumFractionDigits(scale);
        return numberFormatter.format(percent);
    }

    public String formatBillingCycle(RecurrenceJson billingCycle) {
        if (billingCycle.getStep() == 1) {
            return translate(billingCycle.getCycle().getPeriodicityName());
        }

        if (billingCycle.getStep() == 3 && billingCycle.getCycle() == MONTH) {
            return translate(Cycle.QUARTER.getPeriodicityName());
        }

        String billingCycleRecurrenceFormat = translate(BILLING_CYCLE_RECURRENCE_FORMAT_KEY);

        return String.format(billingCycleRecurrenceFormat, billingCycle.getStep(), translate(billingCycle.getCycle().getPluralName()));
    }

    public String formatBillingCycle(Cycle billingCycle) {
        if (billingCycle == null) {
            return StringUtils.EMPTY;
        }

        return translate(billingCycle.getPeriodicityName());
    }

    public String formatPaymentTerm(PaymentTerm paymentTerm) {
        String paymentTermNetTranslated = translate(PAYMENT_TERM_NET);
        return paymentTerm.getDisplayName().replace(PAYMENT_TERM_NET, paymentTermNetTranslated);
    }

    public String formatTermLength(RecurrenceJson termLength, long startDate, long endDate, TimeZone timeZone) {
        if (termLength == null) {
            BigDecimal periodInYears = Period.toDurationInYears(Instant.ofEpochSecond(startDate), Instant.ofEpochSecond(endDate), timeZone);
            return String.format(
                "%s %s",
                periodInYears.toPlainString(),
                periodInYears.doubleValue() > 1 ? translate(YEAR.getPluralName()) : translate(YEAR.getSingularName())
            );
        }
        String pluralName = translate(termLength.getCycle().getPluralName());
        String singularName = translate(termLength.getCycle().getSingularName());
        return String.format("%s %s", termLength.getStep(), termLength.getStep() > 1 ? pluralName : singularName);
    }

    private String translate(String key) {
        try {
            return ResourceBundle.getBundle(LOCALIZATION_FILE_BASE_PATH, locale).getString(key);
        } catch (MissingResourceException e) {
            try {
                return ResourceBundle.getBundle(LOCALIZATION_FILE_BASE_PATH, EN_US_LOCALE).getString(key);
            } catch (Exception ex) {
                return key;
            }
        }
    }

    public static String formatCustomFieldDisplay(CustomFieldValue value) {
        return switch (value.getType()) {
            case MULTISELECT_PICKLIST -> String.join(", ", value.getSelections());
            case PICKLIST -> CollectionUtils.isEmpty(value.getSelections()) ? "" : value.getSelections().get(0);
            case STRING -> value.getValue();
        };
    }

    public TemplatePeriod dateRangeToPeriod(long startDate, long endDate) {
        ZonedDateTime start = ZonedDateTime.ofInstant(Instant.ofEpochSecond(startDate), zoneId);
        ZonedDateTime end = ZonedDateTime.ofInstant(Instant.ofEpochSecond(endDate), zoneId);

        return new TemplatePeriod(java.time.Period.between(start.toLocalDate(), end.toLocalDate()));
    }
}
