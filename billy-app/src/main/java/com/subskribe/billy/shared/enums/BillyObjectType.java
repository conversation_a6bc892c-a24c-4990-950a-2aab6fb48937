package com.subskribe.billy.shared.enums;

public enum BillyObjectType {
    USER("User"),
    USER_GROUP("User group"),
    SFDC_ACCOUNT("Salesforce account"),
    SFDC_LOGIN_INFO_FOR_TENANT("Salesforce login information for tenant"),
    SFDC_INTEGRATION("Salesforce integration"),
    ACCOUNT("Account"),
    ACCOUNT_CONTACT("Account contact"),
    INVOICE("Invoice"),
    SUBSCRIPTION("Subscription"),
    SUBSCRIPTION_CHARGE("Subscription charge"),
    DOCUSIGN_INTEGRATION("Docusign integration"),
    DOCUSIGN_INTEGRATION_FOR_TENANT("Docusign integration for tenant"),
    DOCUSIGN_INTEGRATION_ACCESS_TOKEN("Docusign integration access token"),
    DOCUSIGN_USER_INFORMATION("Docusign user information"),
    DOCUSIGN_DOCUMENT("Docusign document"),
    DOCUSIGN_ACCOUNT_FOR_TENANT("Docusign account for tenant"),
    ORDER("Order"),
    ORDER_METRICS("Order Metrics"),
    PAYMENT("Payment"),
    PAYMENT_BY_TRANSACTION_ID("Payment transaction id"),
    PAYMENT_METHOD("Payment method"),
    ROLE("Role"),
    TENANT("Tenant"),
    TENANT_PAYMENT_CONFIGURATION("Tenant payment configuration"),
    DOCUMENT("Document"),
    DOCUMENT_LINK("Document link id"),
    DOCUMENT_MASTER_TEMPLATE("Document template"),
    DOCUMENT_TEMPLATE("Predefined terms"),
    DOCUMENT_SECTION("Document section"),
    DOCUMENT_CUSTOM_CONTENT("Document custom content"),
    DATA_IMPORT("Data import"),
    IMPORT("Import"),
    AVALARA_INTEGRATION("Avalara integration"),
    AVALARA_INTEGRATION_ACCOUNT("Avalara integration account"),
    AVALARA_INTEGRATION_FOR_TENANT("Avalara integration for tenant"),
    UNIT_OF_MEASURE("Unit of measure"),
    PLAN("Plan"),
    CHARGE("Charge"),
    PRODUCT("Product"),
    NOTIFICATION("Notification"),
    NOTIFICATION_INSTANCE("Notification Instance"),
    API_KEY("Api key"),
    COGNITO_AUTH_INFO("Authentication information"),
    TAX_RATE("Tax rate"),
    TAX_RATE_STRATEGY("Tax rate strategy"),
    CREDIT_MEMO("Credit memo"),
    CREDIT_MEMO_LINE_ITEM("Credit memo line item"),
    ACCOUNT_RECEIVABLE_CONTACT("Account receivable contact"),
    REPORT("Report"),
    DISCOUNT("Discount"),
    PAYMENT_INTEGRATION("Payment integration"),
    PAYMENT_LINK("Payment link id"),
    PAYMENT_LINK_INTERNAL("Payment link internal id"),
    USAGE_ALIAS("Usage alias"),
    PAYMENT_PROVIDER_CUSTOMER("Payment provider customer"),
    PAYMENT_STRIPE_CONNECT_INTEGRATION("Payment Stripe Connect integration"),
    PAYMENT_PROVIDER_PAYMENT_METHOD("Payment provider payment method"),
    APPROVAL_FLOW("Approval flow"),
    ATTACHMENT("Attachment"),
    APPROVAL_FLOW_INSTANCE_GROUP("Approval flow instance group"),
    LEDGER_ACCOUNT("Ledger account"),
    RECOGNITION_RULE("Recognition rule"),
    BULK_INVOICE_RUN("Bulk invoice run"),
    BULK_INVOICE_RUN_FOR_AUTOMATED_INVOICE_RULE("Bulk invoice run for automated invoice rule"),
    BULK_INVOICE_RUN_ITEM("Bulk invoice run item"),
    INVOICE_LINE_ITEM("Invoice line item"),
    ACCOUNTING_PERIOD("Accounting period"),
    PRODUCT_CATEGORY("Product Category"),
    CATALOG_RELATIONSHIP("Catalog Relationship"),
    ORDER_LINE_ITEM("Order line item"),
    OPPORTUNITY("Opportunity"),
    SUBSCRIPTION_CHARGE_ALIAS("Subscription Charge Alias"),
    REFUND("Refund"),
    APPROVAL_ROLE("Approval Role"),
    APPROVAL_SEGMENT("Approval Segment"),
    INVOICE_SETTLEMENT_APPLICATION("Invoice Settlement Application"),
    APPROVAL_MATRIX_IMPORT("Approval Matrix Import"),
    HUBSPOT_INTEGRATION("HubSpot Integration"),
    HUBSPOT_OBJECT("HubSpot Object"),
    TENANT_JOB("Tenant Job"),
    PLATFORM_FEATURE("Platform Feature"),
    RECOGNITION_SCHEDULE("Recognition Schedule"),
    RECOGNITION_TRANSACTION("Recognition Transaction"),
    INTEGRATION("Integration"),
    CUSTOM_FIELD("Custom Field"),
    COMPOSITE_ORDER("Composite Order"),
    ESIGNATURE("Electronic Signature"),
    ESIGNATURE_DETAIL("Electronic Signature Detail"),
    PLAN_GROUP("Plan Group"),
    PROPOSAL("Proposal"),
    AUTOMATED_INVOICE_RULE_CONFIGURATION("Automated Invoice Rule Configuration"),
    INVOICE_PAYMENT_MANAGEMENT_LINK("Invoice Payment Management Link"),
    ACCOUNT_PAYMENT_MANAGEMENT_LINK("Account Payment Management Link"),
    CLM_THREAD("Thread"),
    ESCALATION_POLICY("Escalation Policy"),
    RATE_CARD("Rate Card"),
    PRICE_ATTRIBUTE("Price Attribute"),
    ENTITY("Entity"),
    TRANSACTIONAL_EXCHANGE_RATE("Transactional Exchange Rate"),
    REALIZED_GAIN_LOSS("Realized Gain Loss"),
    INVOICE_CONFIG("Invoice Config"),
    PAYMENT_ATTEMPT("Payment Attempt"),
    CHARGE_DEFAULT_ATTRIBUTE_REFERENCES("Charge Default Attribute References"),
    TAX_JAR_INTEGRATION("TaxJar integration"),
    BILLING_EVENT_ENTRY("Billing Event Entry"),
    EXTERNAL_ARR_SCHEDULE("External ARR Schedule"),
    SCHEDULED_MAINTENANCE("Scheduled Maintenance"),
    SALES_ROOM_LINK("Sales Room link id"),
    FLATFILE_SPACE("Flatfile Space"),
    SEARCH_INDEX("Search Index"),
    LOOKER_USER("Looker User"),
    CURRENCY_TYPE_SETTING("Currency Type Setting"),
    CURRENCY_CONVERSION_RATE("Currency Conversion Rate"),
    ERP_INVOICE("ERP Invoice"),
    PAYMENT_BANK_ACCOUNT("Payment Bank Account"),
    AI_AGENT_SESSION("AI Agent Session"),
    PROMPT("AI Prompt"),
    GUIDED_SELLING_USECASE("Guided Selling Usecase"),
    GUIDED_SELLING_QSCRIPT("Guided Selling Usecase Script"),
    CRM_FIELD_MAPPING_IMPORT("CRM Field Mapping Import"),
    APPROVAL_FLOW_DATA_SNAPSHOT("Approval flow data snapshot for group id"),
    BANK_TRANSACTION("Bank Transaction"),
    BULK_REVENUE_RECOGNITION("Bulk Revenue Recognition"),
    BULK_REVENUE_RECOGNITION_ITEM("Bulk Revenue Recognition Item"),
    INTELLIGENT_SALES_ROOM("Intelligent Sales Room"),
    INTELLIGENT_SALES_ROOM_SHARE_LINK("Intelligent Sales Room Share Link"),
    INTELLIGENT_SALES_ROOM_WIDGET("Intelligent Sales Room Widget"),
    INTELLIGENT_SALES_ROOM_FILE("Intelligent Sales Room File"),
    INTELLIGENT_SALES_ROOM_AI_CONTENT("Intelligent Sales Room AI Content"),
    INTELLIGENT_SALES_ROOM_SESSION("Intelligent Sales Room Session"),
    BILLING_CYCLE_DEFINITION("Billing Cycle Definition");

    private final String name;

    BillyObjectType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
