package com.subskribe.billy.banktransactions.services;

import com.subskribe.billy.banktransactions.model.BankTransactionsUploadCsvSchema;
import com.subskribe.billy.banktransactions.model.BankTransactionsUploadData;
import com.subskribe.billy.banktransactions.model.ImmutablePotentialInvoiceMatch;
import com.subskribe.billy.banktransactions.model.PotentialInvoiceMatch;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.TimeZone;
import org.apache.commons.lang3.StringUtils;

public class BankTransactionPotentialInvoiceFinder {

    private static final Logger LOGGER = LoggerFactory.getLogger(BankTransactionPotentialInvoiceFinder.class);

    private final TenantSettingService tenantSettingService;

    BankTransactionPotentialInvoiceFinder(TenantSettingService tenantSettingService) {
        this.tenantSettingService = tenantSettingService;
    }

    public PotentialInvoiceMatch findPotentialInvoice(Invoice invoice, BankTransactionsUploadData bankTransactionsUploadData) {
        String invoiceNumber = invoice.getInvoiceNumber().getNumber();
        String invoiceNumberNumericPart = invoiceNumber.replaceAll("\\D", "");
        String billingPayerName = invoice.getBillingContact().getFullName();
        String shippingPayerName = invoice.getShippingContact().getFullName();
        Date invoiceDueDate = Date.from(invoice.getDueDate());

        String sourceReferenceNumber = bankTransactionsUploadData.getReferenceNumber();
        String sourcePayerName = bankTransactionsUploadData.getPayerName();
        double sourceTransactionAmount = Double.parseDouble(bankTransactionsUploadData.getTransactionAmount());
        String sourceTransactionDateStr = bankTransactionsUploadData.getTransactionDate();
        Date sourceTransactionDate = tryGetDateFromString(sourceTransactionDateStr);

        /*
        Confidence: total percentage score out of 100 if:
            reference number exact/numeric match with invoice number: 60/30
            transaction_amount exact/less match with invoice balance: 20/10
            transaction_date exact/less match with invoice due date: 10/5
            payer name exact match: 9%
        So best case scenario where the invoice number, transaction amount, transaction date and payer name
        matches exactly will yield 60+20+10+9 = 99% confidence, and lesser number as the matches wane.
         */

        int matchingConfidenceScore = 0;
        if (sourceReferenceNumber.toLowerCase().contains(invoiceNumber.toLowerCase())) {
            matchingConfidenceScore += 60;
        } else if (sourceReferenceNumber.contains(invoiceNumberNumericPart)) {
            matchingConfidenceScore += 30;
        }
        if (sourceTransactionAmount == invoice.getTotal().doubleValue()) {
            matchingConfidenceScore += 20;
        } else if (sourceTransactionAmount < invoice.getTotal().doubleValue()) {
            matchingConfidenceScore += 10;
        }
        if (sourceTransactionDate != null) {
            if (sourceTransactionDate.equals(invoiceDueDate)) {
                matchingConfidenceScore += 10;
            } else if (sourceTransactionDate.before(invoiceDueDate)) {
                matchingConfidenceScore += 5;
            }
        }
        if (sourcePayerName.toLowerCase().contains(billingPayerName.toLowerCase()) || sourcePayerName.contains(shippingPayerName.toLowerCase())) {
            matchingConfidenceScore += 9;
        }
        return ImmutablePotentialInvoiceMatch.builder().invoiceNumber(invoiceNumber).matchingConfidence(matchingConfidenceScore).build();
    }

    Date tryGetDateFromString(String sourceTransactionDateStr) {
        if (StringUtils.isNotBlank(sourceTransactionDateStr)) {
            try {
                return Date.from(ZonedDateTime.parse(sourceTransactionDateStr, tenantUsageTimeFormatter()).toInstant());
            } catch (DateTimeParseException e) {
                LOGGER.info("Exception while parsing date: " + sourceTransactionDateStr + " error: " + e.getMessage());
            }
        }
        return null;
    }

    private DateTimeFormatter tenantUsageTimeFormatter() {
        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        return DateTimeFormatter.ofPattern(BankTransactionsUploadCsvSchema.BANK_TRANSACTIONS_UPLOAD_DATE_TIME_FORMAT).withZone(
            tenantTimeZone.toZoneId()
        );
    }
}
