package com.subskribe.billy.banktransactions.services;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.banktransactions.db.BankTransactionDAO;
import com.subskribe.billy.banktransactions.model.BankTransaction;
import com.subskribe.billy.banktransactions.model.BankTransactionPotentialInvoice;
import com.subskribe.billy.banktransactions.model.BankTransactionsUploadData;
import com.subskribe.billy.banktransactions.model.BankTransactionsUploadResult;
import com.subskribe.billy.banktransactions.model.ImmutableBankTransaction;
import com.subskribe.billy.banktransactions.model.ImmutableBankTransactionPotentialInvoice;
import com.subskribe.billy.banktransactions.model.ImmutableBankTransactionsUploadData;
import com.subskribe.billy.banktransactions.model.ImmutableBankTransactionsUploadResult;
import com.subskribe.billy.banktransactions.model.ImmutablePotentialInvoice;
import com.subskribe.billy.banktransactions.model.InvoiceBankTransactionMatchResponse;
import com.subskribe.billy.banktransactions.model.PotentialInvoice;
import com.subskribe.billy.banktransactions.model.PotentialInvoiceMatch;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoicesettlement.model.InvoiceBalance;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.payment.services.PaymentBankAccountService;
import com.subskribe.billy.resources.shared.PaginatedResponse;
import com.subskribe.billy.shared.enums.BankTransactionStatus;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;

public class BankTransactionsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BankTransactionsService.class);

    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final BankTransactionIdGenerator bankTransactionIdGenerator;
    private final BankTransactionCsvHandler bankTransactionCsvHandler;
    private final PaymentBankAccountService paymentBankAccountService;
    private final TenantSettingService tenantSettingService;
    private final InvoiceSettlementService invoiceSettlementService;
    private final AccountGetService accountGetService;
    private final InvoiceRetrievalService invoiceRetrievalService;
    private final BankTransactionDAO bankTransactionDAO;
    private final AccountingPeriodService accountingPeriodService;

    @Inject
    public BankTransactionsService(
        TenantSettingService tenantSettingService,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        PaymentBankAccountService paymentBankAccountService,
        BankTransactionIdGenerator bankTransactionIdGenerator,
        InvoiceSettlementService invoiceSettlementService,
        AccountGetService accountGetService,
        InvoiceRetrievalService invoiceRetrievalService,
        BankTransactionDAO bankTransactionDAO,
        AccountingPeriodService accountingPeriodService
    ) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.bankTransactionIdGenerator = bankTransactionIdGenerator;
        this.paymentBankAccountService = paymentBankAccountService;
        this.tenantSettingService = tenantSettingService;
        this.accountGetService = accountGetService;
        this.invoiceRetrievalService = invoiceRetrievalService;
        bankTransactionCsvHandler = new BankTransactionCsvHandler(tenantSettingService, paymentBankAccountService);
        this.invoiceSettlementService = invoiceSettlementService;
        this.bankTransactionDAO = bankTransactionDAO;
        this.accountingPeriodService = accountingPeriodService;
    }

    public BankTransactionsUploadResult importCSV(InputStream fileInputStream) throws IOException {
        List<BankTransactionsUploadData> bankTransactionsUploadDataList = bankTransactionCsvHandler.getBankTransactionsUploadData(fileInputStream);
        BankTransactionDAO bankTransactionDAO = new BankTransactionDAO(dslContextProvider, tenantIdProvider);
        int failedCount = 0;
        int successCount = 0;
        List<Invoice> invoices = getAllInvoicesInPostedState();
        List<BankTransactionsUploadData> results = new ArrayList<>();
        Map<String, String> paymentBankAccountNameMap = new HashMap<>();

        for (BankTransactionsUploadData bankTransactionsUploadData : bankTransactionsUploadDataList) {
            ImmutableBankTransactionsUploadData.Builder resultBuilder = ImmutableBankTransactionsUploadData.builder()
                .from(bankTransactionsUploadData);
            if (bankTransactionsUploadData.isFailed()) {
                results.add(resultBuilder.build());
                failedCount++;
                continue;
            }
            if (!paymentBankAccountNameMap.containsKey(bankTransactionsUploadData.getBankAccountId())) {
                paymentBankAccountNameMap.put(
                    bankTransactionsUploadData.getBankAccountId(),
                    paymentBankAccountService.getByPaymentBankAccountId(bankTransactionsUploadData.getBankAccountId()).getName()
                );
            }
            BankTransaction bankTransaction = ImmutableBankTransaction.builder()
                .bankAccountId(bankTransactionsUploadData.getBankAccountId())
                .bankAccountName(Objects.requireNonNull(paymentBankAccountNameMap.get(bankTransactionsUploadData.getBankAccountId())))
                .externalTransactionId(bankTransactionsUploadData.getExternalTransactionId())
                .transactionAmount(new BigDecimal(bankTransactionsUploadData.getTransactionAmount()))
                .transactionDate(bankTransactionCsvHandler.datetimeStringToLong(bankTransactionsUploadData.getTransactionDate()))
                .payerName(bankTransactionsUploadData.getPayerName())
                .paymentType(bankTransactionsUploadData.getPaymentType())
                .referenceNumber(bankTransactionsUploadData.getReferenceNumber())
                .transactionCurrency(bankTransactionsUploadData.getTransactionCurrency())
                .transactionType(bankTransactionsUploadData.getTransactionType())
                .bankTransactionId(bankTransactionIdGenerator.generate())
                .potentialInvoices(getPotentialInvoices(invoices, bankTransactionsUploadData))
                .entityId(
                    paymentBankAccountService
                        .getByPaymentBankAccountId(bankTransactionsUploadData.getBankAccountId())
                        .getEntityIds()
                        .iterator()
                        .next()
                )
                .build();
            try {
                BankTransaction bt = bankTransactionDAO.addBankTransaction(bankTransaction);
                LOGGER.info("Added bank transaction with id: " + bt.getBankTransactionId());
                successCount++;
            } catch (Exception e) {
                resultBuilder.failed(true);
                resultBuilder.failureReason(e.getMessage());
                failedCount++;
            }
            results.add(resultBuilder.build());
        }
        return ImmutableBankTransactionsUploadResult.builder()
            .bankTransactionsCount(bankTransactionsUploadDataList.size())
            .bankTransactionsUploadData(results)
            .successfulBankTransactionsCount(successCount)
            .failedBankTransactionsCount(failedCount)
            .build();
    }

    public PaginatedResponse<BankTransactionPotentialInvoice> getBankTransactionPotentialInvoices(
        String bankAccountId,
        String pageTokenString,
        int limit
    ) {
        BankTransactionDAO bankTransactionDAO = new BankTransactionDAO(dslContextProvider, tenantIdProvider);

        PageResult<List<BankTransaction>, String> pageResult = bankTransactionDAO.getBankTransactionPotentialInvoices(
            bankAccountId,
            PageRequest.from(pageTokenString, limit)
        );
        if (pageResult.getResult().isEmpty()) {
            return new PaginatedResponse<>(List.of(), 0, null);
        }

        // collect all unique invoice numbers
        Set<String> allInvoices = pageResult
            .getResult()
            .stream()
            .filter(bt -> bt.getPotentialInvoices() != null && !bt.getPotentialInvoices().isEmpty())
            .flatMap(bt -> Objects.requireNonNull(bt.getPotentialInvoices()).stream())
            .map(PotentialInvoiceMatch::getInvoiceNumber)
            .collect(Collectors.toSet());
        // create invoice number to invoice map
        Map<String, Invoice> invoiceNumberToInvoiceMap = getInvoiceNumberToInvoiceMap(allInvoices);

        // collect all unique account ids
        Set<String> allAccounts = invoiceNumberToInvoiceMap.values().stream().map(Invoice::getCustomerAccountId).collect(Collectors.toSet());

        // create account id to account map
        Map<String, Account> accountIdToAccountMap = getAccountIdToAccountMap(allAccounts);

        // convert bank transactions to bank transaction potential invoices
        List<BankTransactionPotentialInvoice> bankTransactionPotentialInvoices = pageResult
            .getResult()
            .stream()
            .map(bt -> getBankTransactionPotentialInvoice(bt, invoiceNumberToInvoiceMap, accountIdToAccountMap))
            .toList();

        return new PaginatedResponse<>(bankTransactionPotentialInvoices, bankTransactionPotentialInvoices.size(), pageResult.getNextPageToken());
    }

    public void deleteBankTransaction(String bankTransactionId) {
        BankTransactionDAO bankTransactionDAO = new BankTransactionDAO(dslContextProvider, tenantIdProvider);
        BankTransaction bankTransactionRecord = bankTransactionDAO.getBankTransactionFromId(bankTransactionId);
        if (bankTransactionRecord == null) {
            throw new IllegalStateException("Bank transaction with id: " + bankTransactionId + " not found");
        }
        ensureBankTransactionInUnmatchedState(bankTransactionRecord);
        bankTransactionDAO.deleteBankTransaction(bankTransactionId);
    }

    private void ensureBankTransactionInUnmatchedState(BankTransaction bankTransaction) {
        if (!"Unmatched".equalsIgnoreCase(bankTransaction.getStatus())) {
            throw new IllegalStateException("Bank transaction status is not unmatched: " + bankTransaction.getStatus());
        }
    }

    private List<PotentialInvoiceMatch> getPotentialInvoices(List<Invoice> invoices, BankTransactionsUploadData bankTransactionsUploadData) {
        BankTransactionPotentialInvoiceFinder bankTransactionPotentialInvoiceFinder = new BankTransactionPotentialInvoiceFinder(tenantSettingService);
        Date bankTransactionDate = bankTransactionPotentialInvoiceFinder.tryGetDateFromString(bankTransactionsUploadData.getTransactionDate());
        return invoices
            .stream()
            .filter(invoice -> invoice.getCurrencyCode().equalsIgnoreCase(bankTransactionsUploadData.getTransactionCurrency()))
            .filter(invoice -> bankTransactionDate != null && invoice.getInvoiceDate().isBefore(bankTransactionDate.toInstant()))
            .map(invoice -> bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData))
            .filter(p -> p.getMatchingConfidence() > 0)
            .sorted((p1, p2) -> p2.getMatchingConfidence() - p1.getMatchingConfidence())
            .limit(3) // limit to only three potential invoices for now
            .toList();
    }

    private List<Invoice> getAllInvoicesInPostedState() {
        //Get all invoices for the tenant in posted state
        List<Invoice> invoices = new ArrayList<>();
        Instant pageToken = null;
        do {
            PageResult<List<Invoice>, Instant> pageResult = invoiceRetrievalService.getInvoicesByStatus(
                EnumSet.of(InvoiceStatus.POSTED),
                PageRequest.from(pageToken, PageRequest.MAX_LIMIT)
            );
            invoices.addAll(pageResult.getResult());
            pageToken = pageResult.getNextPageToken();
        } while (pageToken != null);
        return invoices;
    }

    private Map<String, Account> getAccountIdToAccountMap(Set<String> accountIds) {
        return accountGetService.getAccountsForAccountIds(accountIds).stream().collect(Collectors.toMap(Account::getAccountId, Function.identity()));
    }

    private Map<String, Invoice> getInvoiceNumberToInvoiceMap(Set<String> invoiceNumbers) {
        Map<String, Invoice> invoiceNumberToInvoiceMap = new HashMap<>();
        Instant pageToken = null;
        do {
            PageResult<List<Invoice>, Instant> pageResult = invoiceRetrievalService.getInvoicesForInvoiceNumbers(
                invoiceNumbers,
                PageRequest.from(pageToken, PageRequest.MAX_LIMIT)
            );
            pageToken = pageResult.getNextPageToken();
            invoiceNumberToInvoiceMap.putAll(
                pageResult.getResult().stream().collect(Collectors.toMap(i -> i.getInvoiceNumber().getNumber(), Function.identity()))
            );
        } while (pageToken != null);

        return invoiceNumberToInvoiceMap;
    }

    private BankTransactionPotentialInvoice getBankTransactionPotentialInvoice(
        BankTransaction bankTransaction,
        Map<String, Invoice> invoiceNumberToInvoiceMap,
        Map<String, Account> accountIdToAccountMap
    ) {
        List<PotentialInvoice> potentialInvoices = Objects.requireNonNull(bankTransaction.getPotentialInvoices())
            .stream()
            .map(potentialInvoiceMatch ->
                potentialInvoiceMatchToPotentialInvoice(potentialInvoiceMatch, invoiceNumberToInvoiceMap, accountIdToAccountMap)
            )
            .toList();
        return ImmutableBankTransactionPotentialInvoice.builder()
            .bankTransactionId(bankTransaction.getBankTransactionId())
            .addAllPotentialInvoices(potentialInvoices)
            .bankAccountName(bankTransaction.getBankAccountName())
            .bankAccountId(bankTransaction.getBankAccountId())
            .externalTransactionId(bankTransaction.getExternalTransactionId())
            .transactionAmount(bankTransaction.getTransactionAmount())
            .entityId(bankTransaction.getEntityId())
            .transactionCurrency(bankTransaction.getTransactionCurrency())
            .id(Objects.requireNonNull(bankTransaction.getId()))
            .transactionDate(bankTransaction.getTransactionDate())
            .payerName(bankTransaction.getPayerName())
            .paymentType(bankTransaction.getPaymentType())
            .referenceNumber(bankTransaction.getReferenceNumber())
            .transactionType(bankTransaction.getTransactionType())
            .createdOn(Objects.requireNonNull(bankTransaction.getCreatedOn()))
            .status(bankTransaction.getStatus())
            .build();
    }

    private PotentialInvoice potentialInvoiceMatchToPotentialInvoice(
        PotentialInvoiceMatch potentialInvoiceMatch,
        Map<String, Invoice> invoiceNumberToInvoiceMap,
        Map<String, Account> accountIdToAccountMap
    ) {
        Invoice invoice = invoiceNumberToInvoiceMap.get(potentialInvoiceMatch.getInvoiceNumber());
        return ImmutablePotentialInvoice.builder()
            .accountName(accountIdToAccountMap.get(invoice.getCustomerAccountId()).getName())
            .invoiceAmount(invoice.getTotal())
            .matchingConfidence(potentialInvoiceMatch.getMatchingConfidence())
            .invoiceNumber(Objects.requireNonNull(potentialInvoiceMatch.getInvoiceNumber()))
            .invoiceDate(invoice.getDueDate())
            .build();
    }

    public InvoiceBankTransactionMatchResponse matchBankTransaction(String invoiceID, List<String> bankTransactionIDs) {
        Validator.checkNonNullInternal(invoiceID, "invoiceID cannot be null");
        Validator.validateStringNotBlank(invoiceID, "invoiceID cannot be blank");

        Validator.checkNonNullInternal(bankTransactionIDs, "bankTransactionIDs cannot be null");
        Validator.validateCollectionElementsNotNull(bankTransactionIDs, "bankTransactionIDs");

        LOGGER.info("Matching Invoice ID: {} with Bank Transaction IDs: {}", invoiceID, bankTransactionIDs);

        // Use only the first bank transaction ID from the list
        String bankTransactionID = bankTransactionIDs.get(0);
        LOGGER.info("Processing Bank Transaction ID: {}", bankTransactionID);

        Invoice.Number invoiceNumber = new Invoice.Number(invoiceID);
        Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);

        BankTransaction bankTransaction = getBankTransactionById(bankTransactionID).orElseThrow(() ->
            new ObjectNotFoundException(BillyObjectType.BANK_TRANSACTION, bankTransactionID)
        );
        InvoiceBalance invoiceBalance = invoiceSettlementService.getInvoiceBalance(invoice.getInvoiceNumber());

        UUID paymentMethodId = invoiceSettlementService.getAccountPaymentMethodId(
            PaymentType.valueOf(StringUtils.upperCase(bankTransaction.getPaymentType())),
            invoiceBalance.getCustomerAccountId()
        );

        // e,g if accounting period is open as of Apr 1, and bank transaction is dated
        // Apr 1 or greater, we use bank transaction date as payment date.
        // if accounting period is open as of Apr 1 and bank transaction date is
        // Mar 31 or earlier, we use Apr 1 open accounting period date as payment date
        Instant transactionDate = DateTimeConverter.epochSecondsToInstant(bankTransaction.getTransactionDate());
        Optional<AccountingPeriod> maybeAccountingPeriod = accountingPeriodService.getCurrentAccountingPeriod(invoice.getEntityId());

        Instant maxDateInstant;
        // If the account period is not present, use bank transaction date
        if (maybeAccountingPeriod.isPresent()) {
            Instant accountStartDate = maybeAccountingPeriod.get().getStartDate();
            maxDateInstant = transactionDate.isAfter(accountStartDate) ? transactionDate : accountStartDate;
        } else {
            maxDateInstant = transactionDate;
        }
        Long maxDate = maxDateInstant.getEpochSecond();

        SettlementApplication settlementApplication = invoiceSettlementService.addAndApplyManualPayment(
            invoice.getInvoiceNumber(),
            Numbers.makeCurrencyScale(invoiceBalance.getBalance(), invoice.getCurrencyCode()),
            paymentMethodId,
            PaymentType.valueOf(StringUtils.upperCase(bankTransaction.getPaymentType())),
            maxDate,
            Numbers.makeCurrencyScale(bankTransaction.getTransactionAmount(), bankTransaction.getTransactionCurrency()),
            "bank transaction import", //TODO: Note is missing
            BigDecimal.ZERO, //TODO: Bank fee is missing
            bankTransaction.getBankAccountId()
        );

        updateBankTransactionStatusById(bankTransactionID);
        return new InvoiceBankTransactionMatchResponse(
            invoiceID,
            List.of(bankTransactionID),
            invoice.getStatus(),
            invoiceBalance.getBalance(),
            settlementApplication.getPaymentId()
        );
    }

    private Optional<BankTransaction> getBankTransactionById(String bankTransactionID) {
        LOGGER.info("Fetching Bank Transaction with ID: {}", bankTransactionID);
        return bankTransactionDAO.getBankTransactionById(bankTransactionID);
    }

    private void updateBankTransactionStatusById(String bankTransactionID) {
        LOGGER.info("Update Bank Transaction Status using ID: {}", bankTransactionID);
        bankTransactionDAO.updateBankTransactionStatus(bankTransactionID, BankTransactionStatus.MATCHED.name());
    }
}
