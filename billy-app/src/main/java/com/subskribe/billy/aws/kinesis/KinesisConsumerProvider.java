package com.subskribe.billy.aws.kinesis;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.event.consumer.EventConsumer;
import com.subskribe.billy.event.streams.Stream;
import com.subskribe.billy.event.streams.kinesis.ConsumerConsumptionMode;
import com.subskribe.billy.event.streams.kinesis.ImmutableKinesisConsumerWorkerConfig;
import com.subskribe.billy.event.streams.kinesis.KinesisConsumerWorker;
import com.subskribe.billy.event.streams.kinesis.KinesisConsumerWorkerConfig;
import com.subskribe.billy.shared.infra.RuntimeInfoProvider;
import java.net.URI;
import software.amazon.awssdk.http.SdkHttpConfigurationOption;
import software.amazon.awssdk.http.async.SdkAsyncHttpClient;
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.cloudwatch.CloudWatchAsyncClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient;
import software.amazon.awssdk.services.kinesis.KinesisAsyncClient;
import software.amazon.awssdk.utils.AttributeMap;
import software.amazon.kinesis.common.KinesisClientUtil;

public class KinesisConsumerProvider {

    private final KinesisAsyncClient kinesisAsyncClient;

    private final DynamoDbAsyncClient dynamoDbAsyncClient;

    private final CloudWatchAsyncClient cloudWatchAsyncClient;

    private final Boolean forceSharedCapacityMode;

    private final String envName;

    public KinesisConsumerProvider(BillyConfiguration billyConfiguration) {
        forceSharedCapacityMode = billyConfiguration.getKinesisConfiguration().getForceSharedCapacityMode();
        String region = billyConfiguration.getKinesisConfiguration().getRegion().toString();

        if (billyConfiguration.getKinesisConfiguration().getUseEndpointOverride()) {
            String uriEndpoint = billyConfiguration.getKinesisConfiguration().getEndpointOverride();
            // this is done only locally because we interact with localstack
            // we need to trust self-signed certificates
            SdkAsyncHttpClient asyncHttpClient = NettyNioAsyncHttpClient.builder()
                .buildWithDefaults(AttributeMap.builder().put(SdkHttpConfigurationOption.TRUST_ALL_CERTIFICATES, Boolean.TRUE).build());
            kinesisAsyncClient = KinesisAsyncClient.builder()
                .endpointOverride(URI.create(uriEndpoint))
                .httpClient(asyncHttpClient)
                .region(Region.of(region))
                .build();

            dynamoDbAsyncClient = DynamoDbAsyncClient.builder()
                .endpointOverride(URI.create(uriEndpoint))
                .httpClient(asyncHttpClient)
                .region(Region.of(region))
                .build();

            cloudWatchAsyncClient = CloudWatchAsyncClient.builder()
                .endpointOverride(URI.create(uriEndpoint))
                .httpClient(asyncHttpClient)
                .region(Region.of(region))
                .build();
        } else {
            kinesisAsyncClient = KinesisClientUtil.createKinesisAsyncClient(KinesisAsyncClient.builder().region(Region.of(region)));
            dynamoDbAsyncClient = DynamoDbAsyncClient.builder().region(Region.of(region)).build();
            cloudWatchAsyncClient = CloudWatchAsyncClient.builder().region(Region.of(region)).build();
        }

        envName = billyConfiguration.getEnvName();
    }

    public KinesisConsumerWorker createConsumerWorkerInstance(Stream stream, String applicationName, EventConsumer eventConsumer) {
        String streamName = stream.getCanonicalStreamName(envName);
        String decoratedApplicationName = String.format("%s-%s", streamName, applicationName);
        // We are hitting this bug in environments where we use localstack instead of live kinesis: https://github.com/localstack/localstack/issues/10000
        // This only seems to apply to Fan Out consumer mode, so in order to avoid this, we can force the shared capacity mode
        ConsumerConsumptionMode consumerConsumptionMode = forceSharedCapacityMode
            ? ConsumerConsumptionMode.SHARED_SHARD_CAPACITY
            : eventConsumer.getConfiguration().getConsumerConsumptionMode();
        KinesisConsumerWorkerConfig workerConfig = ImmutableKinesisConsumerWorkerConfig.builder()
            .streamName(streamName)
            .consumerName(decoratedApplicationName)
            .workerIdentifier(RuntimeInfoProvider.getHostIdentifier())
            .eventConsumer(eventConsumer)
            .consumerConsumptionMode(consumerConsumptionMode)
            .kinesisAsyncClient(kinesisAsyncClient)
            .dynamoDbAsyncClient(dynamoDbAsyncClient)
            .cloudWatchAsyncClient(cloudWatchAsyncClient)
            .initialPositionInStream(eventConsumer.getConfiguration().getInitialPositionInStream())
            .build();
        return new KinesisConsumerWorker(workerConfig);
    }
}
