package com.subskribe.billy.salesforce.service;

import com.amazonaws.services.dynamodbv2.LockItem;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.jooq.default_schema.tables.records.SalesforceRecord;
import com.subskribe.billy.resources.json.salesforce.SalesforceClientIntegrationRequestJson;
import com.subskribe.billy.salesforce.config.SalesforceConfiguration;
import com.subskribe.billy.salesforce.db.SalesforceDAO;
import com.subskribe.billy.salesforce.model.SalesforceLoginInfo;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.lock.DynamoDBLockProvider;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;

public class SalesforceLoginService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesforceLoginService.class);

    private static final String SALESFORCE_LOGIN_URL_FORMAT =
        "https://%s/services/oauth2/authorize?client_id=%s&redirect_uri=%s&response_type=code&state=%s&%s";

    private static final String SALESFORCE_LOGIN_URL_SCOPE = "scope=refresh_token%20full";

    private static final String SALESFORCE_REQUEST_PARAM_GRANT_TYPE = "grant_type";

    private static final String SALESFORCE_REQUEST_PARAM_CLIENT_ID = "client_id";

    private static final String SALESFORCE_REQUEST_PARAM_CLIENT_SECRET = "client_secret";

    private static final String SALESFORCE_REQUEST_PARAM_REDIRECT_URI = "redirect_uri";

    private static final String SALESFORCE_REQUEST_PARAM_CODE = "code";

    private static final String SALESFORCE_REQUEST_PARAM_REFRESH_TOKEN = "refresh_token";

    private static final String SALESFORCE_REQUEST_PARAM_VALUE_AUTHORIZATION_CODE = "authorization_code";

    private static final String SALESFORCE_REQUEST_PARAM_VALUE_REFRESH_TOKEN = "refresh_token";

    private static final String SALESFORCE_LOGIN_SCHEME = "https";

    private static final String DEFAULT_SALESFORCE_LOGIN_HOST = "login.salesforce.com";

    private static final String SALESFORCE_LOGIN_PATH = "/services/oauth2/token";

    private static final String SALESFORCE_LOGIN_LOCK_KEY_FORMAT = "salesforceLogin/%s";

    private static final int SALESFORCE_LOGIN_LOCK_RETRY_INTERVAL_SECONDS = 1;

    private static final int SALESFORCE_LOGIN_LOCK_TIMEOUT_SECONDS = 30;

    private static final Gson GSON = new GsonBuilder().setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES).create();

    private final SalesforceDAO salesforceDAO;

    private final SalesforceConfiguration salesforceConfiguration;

    private final TenantIdProvider tenantIdProvider;

    private final SecretsService secretsService;

    private final CacheService cacheService;

    private final DynamoDBLockProvider dynamoDBLockProvider;

    @Inject
    public SalesforceLoginService(
        SalesforceDAO salesforceDAO,
        BillyConfiguration billyConfiguration,
        TenantIdProvider tenantIdProvider,
        SecretsService secretsService,
        CacheService cacheService,
        DynamoDBLockProvider dynamoDBLockProvider
    ) {
        this.salesforceDAO = salesforceDAO;
        salesforceConfiguration = billyConfiguration.getSalesforceConfiguration();
        this.tenantIdProvider = tenantIdProvider;
        this.secretsService = secretsService;
        this.cacheService = cacheService;
        this.dynamoDBLockProvider = dynamoDBLockProvider;
    }

    public String initiateIntegration(SalesforceClientIntegrationRequestJson clientInformationJson) {
        validateClientInformation(clientInformationJson);
        String tenantId = tenantIdProvider.provideTenantIdString();
        String integrationId = AutoGenerate.getNewId();
        String baseLoginUrlDomainName = getBaseLoginUrlDomainName(clientInformationJson.getBaseLoginUrl());
        SalesforceRecord salesforceRecord = salesforceDAO.initiateIntegration(
            tenantId,
            clientInformationJson.getClientId(),
            integrationId,
            baseLoginUrlDomainName
        );
        String recordIntegrationId = salesforceRecord.getIntegrationId();
        secretsService.storeOrUpdateSalesforceClientSecret(tenantId, clientInformationJson.getClientSecret(), recordIntegrationId);
        String redirectUri = Optional.ofNullable(clientInformationJson.getRedirectUri()).orElse(salesforceConfiguration.getSubskribeRedirectUrl());
        String baseLoginUrl = getBaseLoginUrl(salesforceRecord);
        return String.format(
            SALESFORCE_LOGIN_URL_FORMAT,
            baseLoginUrl,
            clientInformationJson.getClientId(),
            redirectUri,
            recordIntegrationId,
            SALESFORCE_LOGIN_URL_SCOPE
        );
    }

    private String getBaseLoginUrlDomainName(String baseLoginUrl) {
        try {
            URI uri = new URI(baseLoginUrl);
            // If the scheme is null, then the baseLoginUrl is just a domain name.
            if (uri.getScheme() == null) {
                return baseLoginUrl;
            }
            return uri.getHost();
        } catch (URISyntaxException e) {
            throw new InvalidInputException("Failed to create base Login URL");
        }
    }

    private void validateClientInformation(SalesforceClientIntegrationRequestJson clientInformationJson) {
        if (
            StringUtils.isBlank(clientInformationJson.getClientId()) ||
            StringUtils.isBlank(clientInformationJson.getClientSecret()) ||
            StringUtils.isBlank(clientInformationJson.getBaseLoginUrl())
        ) {
            throw new IllegalArgumentException("Missing required field in client information.");
        }
    }

    public void handleAuthorizationCodeCallback(String authorizationCode, String integrationId, String redirectUri) {
        LOGGER.info(
            "Getting a call to complete salesforce integration for integration id {} with redirectUri: {} and authorization code: {}",
            integrationId,
            redirectUri,
            authorizationCode
        );
        SalesforceRecord salesforceRecord = salesforceDAO.lookupByIntegrationId(integrationId);
        Optional<String> clientSecret = secretsService.retrieveSalesforceClientSecret(salesforceRecord.getTenantId(), integrationId);
        if (clientSecret.isEmpty()) {
            throw new IllegalStateException("Failed to get a SFDC client secret.");
        }

        String requestUrl = getRequestForAuthCodeFlow(
            getBaseLoginUrl(salesforceRecord),
            salesforceRecord.getClientId(),
            clientSecret.get(),
            Optional.ofNullable(redirectUri).orElse(salesforceConfiguration.getSubskribeRedirectUrl()),
            authorizationCode
        );

        SalesforceLoginInfo tokens = callSalesforceToGetTokens(requestUrl);
        String refreshToken = tokens.getRefreshToken();
        secretsService.storeOrUpdateSalesforceRefreshToken(salesforceRecord.getTenantId(), refreshToken, salesforceRecord.getIntegrationId());
        salesforceDAO.markSalesforceIntegrationAsCompleted(salesforceRecord.getTenantId(), integrationId);
        cacheService.set(salesforceRecord.getTenantId(), CacheType.TENANT_HAS_SALESFORCE_INTEGRATION, salesforceRecord.getTenantId(), true);
    }

    private Optional<SalesforceLoginInfo> getLoginInfoUsingRefreshToken(String tenantId) {
        SalesforceRecord salesforceRecord = salesforceDAO
            .lookupByTenantId(tenantId)
            .orElseThrow(() -> new IllegalArgumentException("Did not find a Salesforce integration for this tenant."));

        Optional<String> clientSecret = secretsService.retrieveSalesforceClientSecret(
            salesforceRecord.getTenantId(),
            salesforceRecord.getIntegrationId()
        );
        if (clientSecret.isEmpty()) {
            return Optional.empty();
        }

        Optional<String> refreshToken = secretsService.retrieveSalesforceRefreshToken(tenantId, salesforceRecord.getIntegrationId());
        if (refreshToken.isEmpty()) {
            return Optional.empty();
        }

        String requestUrl = getRequestForRefreshTokenFlow(
            getBaseLoginUrl(salesforceRecord),
            salesforceRecord.getClientId(),
            clientSecret.get(),
            salesforceConfiguration.getSubskribeRedirectUrl(),
            refreshToken.get()
        );

        SalesforceLoginInfo loginInfo = callSalesforceToGetTokens(requestUrl);
        return Optional.of(loginInfo);
    }

    private String getBaseLoginUrl(SalesforceRecord salesforceRecord) {
        return Optional.ofNullable(salesforceRecord.getBaseUrl()).orElse(DEFAULT_SALESFORCE_LOGIN_HOST);
    }

    public SalesforceLoginInfo getLoginInfo() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return getLoginInfo(Optional.of(tenantId));
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public SalesforceLoginInfo getLoginInfo(Optional<String> optionalTenantId) {
        String tenantId = optionalTenantId.orElse(tenantIdProvider.provideTenantIdString());
        String loginLockKey = String.format(SALESFORCE_LOGIN_LOCK_KEY_FORMAT, tenantId);

        Optional<SalesforceLoginInfo> loginInfo;
        try (
            LockItem ignored = dynamoDBLockProvider.waitForLockOrThrow(
                loginLockKey,
                SALESFORCE_LOGIN_LOCK_RETRY_INTERVAL_SECONDS,
                SALESFORCE_LOGIN_LOCK_TIMEOUT_SECONDS
            )
        ) {
            LOGGER.info("Getting Salesforce login info for tenantId: {}", tenantId);
            loginInfo = cacheService.getSerialized(
                tenantId,
                CacheType.SALESFORCE_LOGIN_INFO,
                tenantId,
                key -> getLoginInfoUsingRefreshToken(tenantId),
                new TypeReference<>() {}
            );
            LOGGER.info("Got Salesforce login info for tenantId: {}", tenantId);
        }
        return loginInfo.orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.SFDC_LOGIN_INFO, tenantId));
    }

    private SalesforceLoginInfo callSalesforceToGetTokens(String requestUrl) {
        String responseBody = SalesforceRequestService.callSalesforceWithPostRequestWithEmptyBody(requestUrl);
        return GSON.fromJson(responseBody, SalesforceLoginInfo.class);
    }

    private static String getRequestForAuthCodeFlow(
        String baseLoginUri,
        String clientId,
        String clientSecret,
        String redirectUri,
        String authorizationCode
    ) {
        return getBaseAuthUriForSalesforce(baseLoginUri)
            .addParameter(SALESFORCE_REQUEST_PARAM_GRANT_TYPE, SALESFORCE_REQUEST_PARAM_VALUE_AUTHORIZATION_CODE)
            .addParameter(SALESFORCE_REQUEST_PARAM_CLIENT_ID, clientId)
            .addParameter(SALESFORCE_REQUEST_PARAM_CLIENT_SECRET, clientSecret)
            .addParameter(SALESFORCE_REQUEST_PARAM_REDIRECT_URI, redirectUri)
            .addParameter(SALESFORCE_REQUEST_PARAM_CODE, authorizationCode)
            .toString();
    }

    private static String getRequestForRefreshTokenFlow(
        String baseLoginUri,
        String clientId,
        String clientSecret,
        String redirectUri,
        String refreshToken
    ) {
        return getBaseAuthUriForSalesforce(baseLoginUri)
            .addParameter(SALESFORCE_REQUEST_PARAM_GRANT_TYPE, SALESFORCE_REQUEST_PARAM_VALUE_REFRESH_TOKEN)
            .addParameter(SALESFORCE_REQUEST_PARAM_CLIENT_ID, clientId)
            .addParameter(SALESFORCE_REQUEST_PARAM_CLIENT_SECRET, clientSecret)
            .addParameter(SALESFORCE_REQUEST_PARAM_REDIRECT_URI, redirectUri)
            .addParameter(SALESFORCE_REQUEST_PARAM_REFRESH_TOKEN, refreshToken)
            .toString();
    }

    private static URIBuilder getBaseAuthUriForSalesforce(String baseLoginUrl) {
        return new URIBuilder().setScheme(SALESFORCE_LOGIN_SCHEME).setHost(baseLoginUrl).setPath(SALESFORCE_LOGIN_PATH);
    }

    public void storeRefreshTokenAndMarkIntegrationAsCompleted(String tenantId, String refreshToken, String integrationId) {
        secretsService.storeOrUpdateSalesforceRefreshToken(tenantId, refreshToken, integrationId);
        salesforceDAO.markSalesforceIntegrationAsCompleted(tenantId, integrationId);
        cacheService.set(tenantId, CacheType.TENANT_HAS_SALESFORCE_INTEGRATION, tenantId, true);
    }

    public void deleteSecretsAndMarkIntegrationAsDeleted(String tenantId, String integrationId) {
        secretsService.deleteSalesforceRefreshToken(tenantId, integrationId);
        secretsService.deleteSalesforceClientSecret(tenantId, integrationId);
        salesforceDAO.deleteIntegrationForTenantId(tenantId);
    }
}
