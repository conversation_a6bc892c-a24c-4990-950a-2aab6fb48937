package com.subskribe.billy.salesforce.service;

import static com.subskribe.billy.salesforce.service.SalesforceLockService.waitForLockOrThrow;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.compositeorder.model.CancelAndRestructureOrderDataAggregator;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.CrmObjectType;
import com.subskribe.billy.crm.CrmSyncDirection;
import com.subskribe.billy.crm.CrmType;
import com.subskribe.billy.crm.CrmUtils;
import com.subskribe.billy.crm.model.CrmFieldMapping;
import com.subskribe.billy.crm.service.CrmFieldMappingService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.SalesforceResponseException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.order.OrderDataAggregator;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.metricsreporting.model.TransactionalReportingArrMetrics;
import com.subskribe.billy.metricsreporting.service.TransactionalMetricsGetService;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderDiscountService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderServiceHelper;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.account.AccountAddressJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.salesforce.SalesforceCurrencyType;
import com.subskribe.billy.salesforce.SalesforceOpportunityFields;
import com.subskribe.billy.salesforce.config.SalesforceConfiguration;
import com.subskribe.billy.salesforce.format.SalesforceFormatter;
import com.subskribe.billy.salesforce.gson.SalesforceRecordsGsonNamingStrategy;
import com.subskribe.billy.salesforce.mapper.SalesforceAccountMapper;
import com.subskribe.billy.salesforce.mapper.SalesforceContactMapper;
import com.subskribe.billy.salesforce.model.SalesforceCreateOpportunityRequest;
import com.subskribe.billy.salesforce.model.SalesforceCreateOpportunityResponse;
import com.subskribe.billy.salesforce.model.SalesforceErrorMessage;
import com.subskribe.billy.salesforce.model.SalesforceField;
import com.subskribe.billy.salesforce.model.SalesforceLoginInfo;
import com.subskribe.billy.salesforce.model.SalesforceOpportunity;
import com.subskribe.billy.salesforce.model.SalesforcePackage;
import com.subskribe.billy.salesforce.model.SalesforcePicklistValue;
import com.subskribe.billy.salesforce.model.SalesforceQueryResponse;
import com.subskribe.billy.salesforce.model.SubskribeSalesforceBoolean;
import com.subskribe.billy.salesforce.model.SubskribeSalesforceOrder;
import com.subskribe.billy.salesforce.model.SubskribeSalesforceRateCard;
import com.subskribe.billy.salesforce.model.SubskribeSalesforceTransactionalArrMetrics;
import com.subskribe.billy.salesforce.model.account.SalesforceAccount;
import com.subskribe.billy.salesforce.model.account.SalesforceAccountJson;
import com.subskribe.billy.salesforce.model.customfield.SalesforceCustomField;
import com.subskribe.billy.salesforce.model.metrics.SubskribeSalesforceArrTrend;
import com.subskribe.billy.salesforce.model.orderlines.SubskribeSalesforceOrderLine;
import com.subskribe.billy.salesforce.model.subscription.SubskribeSalesforceSubscription;
import com.subskribe.billy.salesforce.model.subscription.SubskribeSalesforceSubscriptionLine;
import com.subskribe.billy.salesforce.test.SalesforceTestOpportunityRequest;
import com.subskribe.billy.salesforce.tools.SalesforceIdConverter;
import com.subskribe.billy.shared.SubskribeUrlGenerator;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class SalesforceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesforceService.class);

    private static final Set<OrderType> ALLOWED_ORDER_TYPES = Set.of(OrderType.NEW, OrderType.RENEWAL, OrderType.CANCEL, OrderType.AMENDMENT);

    public static final String API_PATH_PREFIX_FORMAT = "/services/data/%s/";

    private static final String OBJECTS_API_PATH_FORMAT = API_PATH_PREFIX_FORMAT + "sobjects/";

    private static final String OPPORTUNITY_OBJECT_API_PATH_FORMAT = OBJECTS_API_PATH_FORMAT + "Opportunity/";

    private static final String OPPORTUNITY_OBJECT_WITH_ID_API_PATH_FORMAT = OPPORTUNITY_OBJECT_API_PATH_FORMAT + "%s/";

    private static final String FIND_OPPORTUNITY_ORDER_LINE_CHILDREN_FORMAT =
        "SELECT FIELDS(ALL) FROM subskribe__Subskribe_Order_Line__c WHERE subskribe__Opportunity__c = '%s' LIMIT 200";

    private static final String FIND_ORDER_LINE_CHILDREN_FORMAT =
        "SELECT FIELDS(ALL) FROM subskribe__Subskribe_Order_Line__c WHERE subskribe__Subskribe_Order__c IN (%s) LIMIT 200";

    private static final String FIND_ORPHANED_ORDER_LINES_QUERY =
        "SELECT FIELDS(ALL) FROM subskribe__Subskribe_Order_Line__c WHERE subskribe__Opportunity__c = NULL LIMIT 200";

    private static final String FIND_ORDER_LINE_NON_SUBSKRIBE_CUSTOM_FIELDS_FORMAT =
        """
        SELECT QualifiedApiName,
               DataType
        FROM   FieldDefinition
        WHERE  EntityDefinition.QualifiedApiName = 'subskribe__Subskribe_Order_Line__c'
               AND FieldDefinition.QualifiedApiName like '%__c'
        """;

    private static final String FIND_ORDER_NON_SUBSKRIBE_CUSTOM_FIELDS_FORMAT =
        """
        SELECT QualifiedApiName,
               DataType
        FROM   FieldDefinition
        WHERE  EntityDefinition.QualifiedApiName = 'subskribe__Subskribe_Order__c'
               AND FieldDefinition.QualifiedApiName like '%__c'
        """;
    private static final String FIND_ACCOUNT_ARR_TREND_CHILDREN_FORMAT =
        "SELECT FIELDS(ALL) FROM subskribe__Subskribe_ARR_Trend__c WHERE subskribe__Account__c = '%s' LIMIT 200";

    private static final String FIND_ORDER_FORMAT =
        "SELECT FIELDS(ALL) FROM subskribe__Subskribe_Order__c WHERE subskribe__Opportunity__c = '%s' LIMIT 200";

    private static final String FIND_ORPHANED_ORDERS_QUERY =
        "SELECT FIELDS(ALL) FROM subskribe__Subskribe_Order__c WHERE subskribe__Opportunity__c = NULL LIMIT 200";

    private static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd";

    private static final String TEST_OPPORTUNITY_NAME = "Test-Opportunity";

    public static final String TEST_ACCOUNT_ID_PREFIX = "TEST";

    private static final String TEST_OPPORTUNITY_STAGE_NAME = "Qualification";

    private static final String TEST_OPPORTUNITY_SUBSKRIBE_ORDER_TYPE = "Amendment";

    public static final String INVALID_OPPORTUNITY_ID_PREFIX = "TEST";

    private static final int TEST_OPPORTUNITY_CLOSE_DATE_DAYS_FROM_NOW = 30;

    private static final SalesforceRecordsGsonNamingStrategy SALESFORCE_RECORDS_GSON_NAMING_STRATEGY = new SalesforceRecordsGsonNamingStrategy();

    public static final Gson SALESFORCE_GSON = new GsonBuilder()
        .setFieldNamingStrategy(SALESFORCE_RECORDS_GSON_NAMING_STRATEGY)
        .serializeNulls()
        .create();
    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();
    private static final int LOCK_WAIT_MS = 60000;

    private static final String ARR_LOCK_KEY_FORMAT = "arr-%s";

    private static final String OPPORTUNITY_SYNC_KEY_FORMAT = "opportunity-%s";

    private static final String SUBSCRIPTION_LOCK_KEY_FORMAT = "subscription-%s";

    private static final String TRANSACTIONAL_ARR_METRICS_LOCK_KEY_FORMAT = "transactional-arr-%s";

    private static final Type ORDER_QUERY_RESPONSE_TYPE = new TypeReference<SalesforceQueryResponse<SubskribeSalesforceOrder>>() {}.getType();

    private static final Type ORDER_LINE_QUERY_RESPONSE_TYPE = new TypeReference<
        SalesforceQueryResponse<SubskribeSalesforceOrderLine>
    >() {}.getType();

    public static final Type ARR_QUERY_RESPONSE_TYPE = new TypeReference<SalesforceQueryResponse<SubskribeSalesforceArrTrend>>() {}.getType();

    private static final Type CUSTOM_FIELD_QUERY_RESPONSE_TYPE = new TypeReference<SalesforceQueryResponse<SalesforceCustomField>>() {}.getType();

    private static final String SELECT_PACKAGES_QUERY =
        "SELECT DurableId, Id, IsSalesforce,MajorVersion,MinorVersion, Name,NamespacePrefix FROM Publisher";

    private static final Type PACKAGE_QUERY_RESPONSE_TYPE = new TypeReference<SalesforceQueryResponse<SalesforcePackage>>() {}.getType();

    private static final String SELECT_CURRENCY_QUERY = "SELECT Id, IsoCode, IsActive FROM CurrencyType";

    private static final String INVALID_TYPE_ERROR_CODE = "INVALID_TYPE";

    private static final String SALESFORCE_STRING_FIELD_TYPE = "string";

    private static final Type CURRENCY_QUERY_RESPONSE_TYPE = new TypeReference<SalesforceQueryResponse<SalesforceCurrencyType>>() {}.getType();

    private static final String SUBSKRIBE_PACKAGE_ID = "subskribe";

    private static final String RENEWAL_OPPORTUNITY_ID_FIELD = "subskribe__Subskribe_Expected_ARR__c";

    private static final String FIND_ORPHANED_RATE_CARDS_QUERY =
        "SELECT FIELDS(ALL) FROM subskribe__Subskribe_Rate_Card__c WHERE subskribe__Subskribe_Order_Line__c = NULL LIMIT 200";

    private static final String FIND_ORDER_LINES_QUERY_FORMAT =
        "SELECT FIELDS(ALL) FROM subskribe__Subskribe_Order_Line__c WHERE subskribe__Subskribe_Order_Line_ID__c IN (%s) LIMIT 200";

    private static final String FIND_ALL_RECORDS_QUERY_FORMAT = "SELECT FIELDS(ALL) FROM %s LIMIT 200";

    private static final Type RATE_CARD_QUERY_RESPONSE_TYPE = new TypeReference<SalesforceQueryResponse<SubskribeSalesforceRateCard>>() {}.getType();

    private static final Type ARR_TREND_QUERY_RESPONSE_TYPE = new TypeReference<SalesforceQueryResponse<SubskribeSalesforceArrTrend>>() {}.getType();

    private static final Type ARR_TRANSACTIONAL_METRICS_QUERY_RESPONSE_TYPE = new TypeReference<
        SalesforceQueryResponse<SubskribeSalesforceTransactionalArrMetrics>
    >() {}.getType();

    private static final Type SUBSCRIPTION_LINE_QUERY_RESPONSE_TYPE = new TypeReference<
        SalesforceQueryResponse<SubskribeSalesforceSubscriptionLine>
    >() {}.getType();

    private static final Type SUBSCRIPTION_QUERY_RESPONSE_TYPE = new TypeReference<
        SalesforceQueryResponse<SubskribeSalesforceSubscription>
    >() {}.getType();

    private static final SalesforcePackage VERSION_1_58 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "58");

    private static final SalesforcePackage VERSION_1_63 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "63");

    private static final SalesforcePackage VERSION_1_66 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "66");

    private static final SalesforcePackage VERSION_1_67 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "67");

    private static final SalesforcePackage VERSION_1_71 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "71");

    private static final SalesforcePackage VERSION_1_72 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "72");

    private static final SalesforcePackage VERSION_1_73 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "73");

    private static final SalesforcePackage VERSION_1_75 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "75");

    private static final SalesforcePackage VERSION_1_78 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "78");

    private static final SalesforcePackage VERSION_1_80 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "80");

    private static final SalesforcePackage VERSION_1_81 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "81");

    private static final SalesforcePackage VERSION_1_83 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "83");

    private static final SalesforcePackage VERSION_1_84 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "84");

    private static final SalesforcePackage VERSION_1_86 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "86");
    private static final SalesforcePackage VERSION_1_87 = new SalesforcePackage(SUBSKRIBE_PACKAGE_ID, "1", "87");

    private final SalesforceConfiguration salesforceConfiguration;

    private final SalesforceLoginService salesforceLoginService;

    private final SalesforceJobQueueService salesforceJobQueueService;

    private final OpportunityService opportunityService;
    private final OpportunityGetService opportunityGetService;

    private final AccountGetService accountGetService;

    private final OrderGetService orderGetService;

    private final OrderDataAggregator orderDataAggregator;

    private final CancelAndRestructureOrderDataAggregator cancelAndRestructureOrderDataAggregator;

    private final MetricsService metricsService;

    private final ProductCatalogGetService productCatalogGetService;

    private final SubscriptionGetService subscriptionGetService;

    private final FeatureService featureService;

    private final CustomFieldService customFieldService;

    private final String siteUrl;

    private final BillyConfiguration billyConfiguration;
    private final SalesforceArrTrendService salesforceArrTrendService;
    private final SalesforceSubscriptionService salesforceSubscriptionService;
    private final SalesforceRestService salesforceRestService;
    private final SalesforceQueryService salesforceQueryService;
    private final SalesforceGetService salesforceGetService;
    private final CompositeOrderGetService compositeOrderGetService;
    private final TenantIdProvider tenantIdProvider;
    private final TenantSettingService tenantSettingService;
    private final DSLContextProvider dslContextProvider;
    private final SalesforceAccountMapper accountMapper;
    private final SalesforceContactMapper contactMapper;
    private final TransactionalMetricsGetService transactionalMetricsGetService;
    private final RateCardService rateCardService;
    private final CrmFieldMappingService crmFieldMappingService;

    @Inject
    public SalesforceService(
        SalesforceLoginService salesforceLoginService,
        BillyConfiguration billyConfiguration,
        SalesforceJobQueueService salesforceJobQueueService,
        OpportunityService opportunityService,
        OpportunityGetService opportunityGetService,
        AccountGetService accountGetService,
        OrderGetService orderGetService,
        OrderDataAggregator orderDataAggregator,
        CancelAndRestructureOrderDataAggregator cancelAndRestructureOrderDataAggregator,
        MetricsService metricsService,
        ProductCatalogGetService productCatalogGetService,
        SubscriptionGetService subscriptionGetService,
        FeatureService featureService,
        CustomFieldService customFieldService,
        SalesforceArrTrendService salesforceArrTrendService,
        SalesforceSubscriptionService salesforceSubscriptionService,
        SalesforceRestService salesforceRestService,
        SalesforceQueryService salesforceQueryService,
        SalesforceGetService salesforceGetService,
        CompositeOrderGetService compositeOrderGetService,
        TenantIdProvider tenantIdProvider,
        TenantSettingService tenantSettingService,
        DSLContextProvider dslContextProvider,
        TransactionalMetricsGetService transactionalMetricsGetService,
        RateCardService rateCardService,
        CrmFieldMappingService crmFieldMappingService
    ) {
        this.salesforceLoginService = salesforceLoginService;
        salesforceConfiguration = billyConfiguration.getSalesforceConfiguration();
        siteUrl = billyConfiguration.getSiteUrl();
        this.salesforceJobQueueService = salesforceJobQueueService;
        this.opportunityService = opportunityService;
        this.opportunityGetService = opportunityGetService;
        this.accountGetService = accountGetService;
        this.orderGetService = orderGetService;
        this.orderDataAggregator = orderDataAggregator;
        this.cancelAndRestructureOrderDataAggregator = cancelAndRestructureOrderDataAggregator;
        this.metricsService = metricsService;
        this.productCatalogGetService = productCatalogGetService;
        this.subscriptionGetService = subscriptionGetService;
        this.billyConfiguration = billyConfiguration;
        this.featureService = featureService;
        this.customFieldService = customFieldService;
        this.salesforceArrTrendService = salesforceArrTrendService;
        this.salesforceSubscriptionService = salesforceSubscriptionService;
        this.salesforceRestService = salesforceRestService;
        this.salesforceQueryService = salesforceQueryService;
        this.salesforceGetService = salesforceGetService;
        this.compositeOrderGetService = compositeOrderGetService;
        this.tenantIdProvider = tenantIdProvider;
        this.tenantSettingService = tenantSettingService;
        this.dslContextProvider = dslContextProvider;
        this.transactionalMetricsGetService = transactionalMetricsGetService;
        this.rateCardService = rateCardService;
        this.crmFieldMappingService = crmFieldMappingService;
        accountMapper = Mappers.getMapper(SalesforceAccountMapper.class);
        contactMapper = Mappers.getMapper(SalesforceContactMapper.class);
    }

    private SalesforceLoginInfo getLoginInfoWithMultiCurrency() throws IOException, URISyntaxException {
        SalesforceLoginInfo loginInfo = salesforceLoginService.getLoginInfo();
        loginInfo.setIsMultiCurrency(getMultiCurrency(loginInfo));
        return loginInfo;
    }

    public void updateSalesforceAccount(String accountId) {
        List<String> errors = new ArrayList<>();
        try {
            SalesforceLoginInfo loginInfo = getLoginInfoWithMultiCurrency();
            Account account = accountGetService.getAccount(accountId);
            Optional<SalesforceAccount> existingSalesforceAccount = salesforceGetService.getAccountFromSalesforce(account, errors);
            if (existingSalesforceAccount.isPresent()) {
                SalesforceAccount salesforceAccount = accountMapper.fromSubskribeAccount(account);
                salesforceAccount.setId(null);
                salesforceRestService.updateOne(loginInfo, SalesforceAccountJson.API_NAME, salesforceAccount, account.getCrmId());
                syncAccountCustomFields(loginInfo, account, errors);
            }
        } catch (IOException | URISyntaxException e) {
            errors.add("Salesforce account sync failed.");
        } catch (SalesforceResponseException e) {
            errors.add(String.format("Salesforce account sync failed. Reason: %s", CrmUtils.truncateFailureReason(e.getResponseBody())));
        }
        throwIfSyncErrorsAreFound(errors);
    }

    private void syncAccountCustomFields(SalesforceLoginInfo loginInfo, Account account, List<String> errors) {
        if (!featureService.isEnabled(Feature.CRM_FIELD_MAPPING)) {
            return;
        }
        List<CrmFieldMapping> outboundCrmFieldMappings = crmFieldMappingService
            .getCrmFieldMappings(CrmType.SALESFORCE, CrmObjectType.ACCOUNT)
            .stream()
            .filter(m -> CrmSyncDirection.OUTBOUND == m.getDirection())
            .toList();

        // No custom field mappings found
        if (CollectionUtils.isEmpty(outboundCrmFieldMappings)) {
            return;
        }

        List<SalesforceField> salesforceFields = salesforceGetService.getObjectFields(loginInfo, SalesforceAccountJson.class, errors);
        CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ACCOUNT, account.getAccountId());
        List<CustomFieldValue> customFieldsToSync = customField.getEntries().values().stream().toList();

        Map<String, String> customFieldUpdateMap = new HashMap<>();
        for (var crmFieldMapping : outboundCrmFieldMappings) {
            populateCustomFieldUpdateMap(customFieldUpdateMap, customFieldsToSync, crmFieldMapping, salesforceFields, errors);
        }

        if (!customFieldUpdateMap.isEmpty()) {
            try {
                LOGGER.info(
                    "Custom field update map for account {} crm ID: {}: {}",
                    account.getAccountId(),
                    account.getCrmId(),
                    customFieldUpdateMap
                );
                salesforceRestService.updateOne(loginInfo, SalesforceAccountJson.API_NAME, customFieldUpdateMap, account.getCrmId());
            } catch (SalesforceResponseException | URISyntaxException | IOException e) {
                errors.add(
                    String.format(
                        "Failed to update custom fields for Salesforce account %s with CRM ID %s",
                        account.getAccountId(),
                        account.getCrmId()
                    )
                );
            }
        }
    }

    public void pushAccountARR(String accountId) throws URISyntaxException, IOException {
        LOGGER.info("Start of Salesforce account ARR sync for {}", accountId);
        List<String> errors = new ArrayList<>();
        Account account = accountGetService.getAccount(accountId);
        Optional<SalesforceAccount> salesforceAccount = salesforceGetService.getAccountFromSalesforce(account, errors);
        if (salesforceAccount.isPresent()) {
            updateAccountArrTrends(account);
        }
        LOGGER.info("End of Salesforce account ARR trend sync for {}", accountId);
        throwIfSyncErrorsAreFound(errors);
    }

    private void updateAccountArrTrends(Account account) throws IOException, URISyntaxException {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        SalesforceLoginInfo loginInfo = getLoginInfoWithMultiCurrency();
        dslContext.transaction(configuration -> {
            String lockKey = String.format(ARR_LOCK_KEY_FORMAT, account.getAccountId());
            waitForLockOrThrow(configuration, lockKey, LOCK_WAIT_MS);
            List<SubskribeSalesforceArrTrend> arrTrends = salesforceArrTrendService.getSortedSalesforceArrTrendObjectsForAccount(account);
            List<SubskribeSalesforceArrTrend> currentArrTrends = getSortedArrTrendsForAccountFromSalesforce(account.getCrmId()).toList();
            List<String> currentArrIds = currentArrTrends.stream().map(SubskribeSalesforceArrTrend::getId).toList();
            salesforceRestService.deleteMany(loginInfo, currentArrIds);
            LOGGER.info("Syncing Salesforce account ARR trends for {}: {}", account.getAccountId(), arrTrends);
            salesforceRestService.createMany(loginInfo, SubskribeSalesforceArrTrend.class, arrTrends);
            List<SubskribeSalesforceArrTrend> updatedArrTrends = getSortedArrTrendsForAccountFromSalesforce(account.getCrmId()).toList();
            LOGGER.info("Updated Salesforce account ARR trends for {}: {}", account.getAccountId(), updatedArrTrends);
        });
    }

    public void pushOrder(String orderId) throws URISyntaxException, IOException {
        List<String> errors = new ArrayList<>();
        SalesforcePackage salesforcePackage = getSalesforcePackage();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        SalesforceLoginInfo loginInfo = getLoginInfoWithMultiCurrency();
        Order orderForLockKey = orderGetService.getOrderByOrderId(orderId);

        // If the order is part of a composite order, then we should use the composite order sync instead
        if (StringUtils.isNotBlank(orderForLockKey.getCompositeOrderId())) {
            pushCompositeOrder(orderForLockKey.getCompositeOrderId());
            return;
        }

        String lockKey = String.format(OPPORTUNITY_SYNC_KEY_FORMAT, orderForLockKey.getAccountId());
        dslContext.transaction(configuration -> {
            waitForLockOrThrow(configuration, lockKey, LOCK_WAIT_MS);
            Order order = orderGetService.getOrderByOrderId(orderId);
            Optional<SalesforceOpportunity> salesforceOpportunity = salesforceGetService.getOpportunityFromSalesforce(
                order.getOrderId(),
                order.getSfdcOpportunityId(),
                errors
            );
            if (salesforceOpportunity.isPresent()) {
                syncSwitchedOpportunities(loginInfo, order, salesforcePackage);
                Optional<SubskribeSalesforceOrder> primarySalesforceOrder = replaceOrdersForOpportunity(
                    loginInfo,
                    order.getSfdcOpportunityId(),
                    salesforcePackage,
                    errors
                );
                primarySalesforceOrder.ifPresent(o -> updateOpportunityForPrimarySalesforceOrder(loginInfo, salesforcePackage, o, errors));
            }
        });
        throwIfSyncErrorsAreFound(errors);
    }

    private void throwIfSyncErrorsAreFound(List<String> errors) {
        if (!errors.isEmpty()) {
            throw new IllegalStateException("Following errors are found during the CRM sync: " + String.join("; ", errors));
        }
    }

    private void updateOpportunityForPrimarySalesforceOrder(
        SalesforceLoginInfo loginInfo,
        SalesforcePackage salesforcePackage,
        SubskribeSalesforceOrder salesforceOrder,
        List<String> errors
    ) {
        if (StringUtils.isNotBlank(salesforceOrder.getCompositeOrderId())) {
            CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(salesforceOrder.getCompositeOrderId());
            switch (compositeOrder.getType()) {
                case UPSELL_AND_EARLY_RENEWAL -> updateOpportunityForPrimaryUpsellAndEarlyRenewal(
                    loginInfo,
                    salesforcePackage,
                    salesforceOrder,
                    compositeOrder,
                    errors
                );
                case CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE -> updateOpportunityForPrimaryCancelAndRestructure(
                    loginInfo,
                    salesforcePackage,
                    salesforceOrder,
                    compositeOrder,
                    errors
                );
            }
        } else {
            Order order = orderGetService.getOrderByOrderId(salesforceOrder.getOrder());
            updateOpportunityForPrimaryOrder(loginInfo, salesforcePackage, salesforceOrder, order, errors);
        }
    }

    private void updateOpportunityForPrimaryCancelAndRestructure(
        SalesforceLoginInfo loginInfo,
        SalesforcePackage salesforcePackage,
        SubskribeSalesforceOrder salesforceOrder,
        CompositeOrder compositeOrder,
        List<String> errors
    ) {
        List<Order> orders = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrder.getCompositeOrderId());
        Order restructureOrder = orders.stream().filter(subOrder -> subOrder.getOrderType() == OrderType.RESTRUCTURE).findFirst().orElseThrow();
        Order cancelOrder = orders.stream().filter(subOrder -> subOrder.getOrderType() == OrderType.CANCEL).findFirst().orElseThrow();
        Account account = accountGetService.getAccount(restructureOrder.getAccountId());
        String opportunityId = compositeOrder.getCrmOpportunityId();

        try {
            updateOpportunityForCancelAndRestructure(restructureOrder, cancelOrder, loginInfo, opportunityId, salesforcePackage);
            LOGGER.debug("pushed opportunity: {} {}", opportunityId, compositeOrder.getCompositeOrderId());

            List<OrderLineItem> allLineItemsToAppearInRestructuredOrder =
                cancelAndRestructureOrderDataAggregator.getDistinctOrderLinesForCancelAndRestructure(restructureOrder, List.of(cancelOrder));
            List<SubskribeSalesforceOrderLine> combinedSalesforceOrderLines = getSortedSalesforceOrderLinesForCancelAndRestructure(
                restructureOrder,
                List.of(cancelOrder),
                allLineItemsToAppearInRestructuredOrder,
                salesforceOrder,
                opportunityId,
                salesforcePackage
            );
            if (salesforcePackage.compareTo(VERSION_1_71) < 0) {
                combinedSalesforceOrderLines.forEach(line -> line.setOrderType(OrderType.CANCEL.name()));
            }
            replaceOrderLines(combinedSalesforceOrderLines, loginInfo, opportunityId, salesforcePackage, errors);
            replaceRateCardPriceAttributes(restructureOrder, allLineItemsToAppearInRestructuredOrder, loginInfo, salesforcePackage);
            replaceOrderARR(restructureOrder, account, loginInfo, opportunityId);
        } catch (IOException | URISyntaxException e) {
            errors.add(
                String.format("Failed to update Salesforce opportunity for cancel and restructure order: %s", compositeOrder.getCompositeOrderId())
            );
        } catch (SalesforceResponseException e) {
            errors.add(
                String.format(
                    "Failed to update Salesforce opportunity for cancel and restructure order: %s, Reason: %s",
                    compositeOrder.getCompositeOrderId(),
                    CrmUtils.truncateFailureReason(e.getResponseBody())
                )
            );
        }
    }

    private void updateOpportunityForPrimaryUpsellAndEarlyRenewal(
        SalesforceLoginInfo loginInfo,
        SalesforcePackage salesforcePackage,
        SubskribeSalesforceOrder salesforceOrder,
        CompositeOrder compositeOrder,
        List<String> errors
    ) {
        List<Order> orders = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrder.getCompositeOrderId());
        Order renewalOrder = orders.stream().filter(subOrder -> subOrder.getOrderType() == OrderType.RENEWAL).findFirst().orElseThrow();
        Order amendmentOrder = orders.stream().filter(subOrder -> subOrder.getOrderType() == OrderType.AMENDMENT).findFirst().orElseThrow();
        Account account = accountGetService.getAccount(renewalOrder.getAccountId());
        String opportunityId = compositeOrder.getCrmOpportunityId();

        try {
            updateOpportunity(renewalOrder, loginInfo, opportunityId, salesforcePackage);

            List<OrderLineItem> renewalOrderLineItems = new ArrayList<>(renewalOrder.getLineItems());
            List<OrderLineItem> missingOrderLineItems = renewalOrder.getMissingRenewalOrderLineItems();
            if (CollectionUtils.isNotEmpty(missingOrderLineItems)) {
                renewalOrderLineItems.addAll(missingOrderLineItems);
            }
            List<SubskribeSalesforceOrderLine> renewalOrderLines = getSortedSalesforceOrderLines(
                renewalOrder,
                renewalOrderLineItems,
                salesforceOrder,
                opportunityId,
                salesforcePackage
            );

            List<OrderLineItem> amendmentOrderLineItems = new ArrayList<>(amendmentOrder.getLineItems());
            List<OrderLineItem> missingAmendmentLineItems = amendmentOrder.getMissingRenewalOrderLineItems();
            if (CollectionUtils.isNotEmpty(missingAmendmentLineItems)) {
                amendmentOrderLineItems.addAll(missingAmendmentLineItems);
            }
            List<SubskribeSalesforceOrderLine> amendmentOrderLines = getSortedSalesforceOrderLines(
                amendmentOrder,
                amendmentOrderLineItems,
                salesforceOrder,
                opportunityId,
                salesforcePackage
            );

            List<OrderLineItem> combinedOrderLines = Stream.concat(renewalOrderLineItems.stream(), amendmentOrderLineItems.stream()).toList();
            List<SubskribeSalesforceOrderLine> combinedSalesforceOrderLines = Stream.concat(
                renewalOrderLines.stream(),
                amendmentOrderLines.stream()
            ).toList();
            replaceOrderLines(combinedSalesforceOrderLines, loginInfo, opportunityId, salesforcePackage, errors);
            replaceRateCardPriceAttributes(renewalOrder, combinedOrderLines, loginInfo, salesforcePackage);
            replaceOrderARR(renewalOrder, account, loginInfo, opportunityId);
        } catch (IOException | URISyntaxException e) {
            errors.add(
                String.format("Failed to update Salesforce opportunity for upsell and early renewal order: %s", compositeOrder.getCompositeOrderId())
            );
        } catch (SalesforceResponseException e) {
            errors.add(
                String.format(
                    "Failed to update Salesforce opportunity for upsell and early renewal order: %s, Reason: %s",
                    compositeOrder.getCompositeOrderId(),
                    CrmUtils.truncateFailureReason(e.getResponseBody())
                )
            );
        }
    }

    private void updateOpportunityForPrimaryOrder(
        SalesforceLoginInfo loginInfo,
        SalesforcePackage salesforcePackage,
        SubskribeSalesforceOrder salesforceOrder,
        Order order,
        List<String> errors
    ) {
        try {
            Account account = accountGetService.getAccount(order.getAccountId());
            updateOpportunity(order, loginInfo, order.getSfdcOpportunityId(), salesforcePackage);

            List<OrderLineItem> lineItems = new ArrayList<>(order.getLineItems());
            List<OrderLineItem> missingLineItems = order.getMissingRenewalOrderLineItems();
            if (CollectionUtils.isNotEmpty(missingLineItems)) {
                lineItems.addAll(missingLineItems);
            }
            List<SubskribeSalesforceOrderLine> salesforceOrderLines = getSortedSalesforceOrderLines(
                order,
                lineItems,
                salesforceOrder,
                order.getSfdcOpportunityId(),
                salesforcePackage
            );
            replaceOrderLines(salesforceOrderLines, loginInfo, order.getSfdcOpportunityId(), salesforcePackage, errors);
            replaceRateCardPriceAttributes(order, lineItems, loginInfo, salesforcePackage);
            replaceOrderARR(order, account, loginInfo, order.getSfdcOpportunityId());
        } catch (URISyntaxException | IOException e) {
            errors.add(String.format("Failed to update Salesforce opportunity for order: %s.", order.getOrderId()));
        } catch (SalesforceResponseException e) {
            errors.add(
                String.format(
                    "Failed to update Salesforce opportunity for order: %s, Reason: %s",
                    order.getOrderId(),
                    CrmUtils.truncateFailureReason(e.getResponseBody())
                )
            );
        }
    }

    // when order switches from opportunity A to opportunity B, below needs to happen for opportunity A:
    // A. if it is linked to other orders, elect the most recently created order as primary and sync it
    // B. if it is "orphan", clean up:
    //   1. cleanup data for opportunity A in salesforce
    //   2. delete related order(s) and order line items from salesforce
    //   3. delete the opportunity record from database
    private void syncSwitchedOpportunities(SalesforceLoginInfo loginInfo, Order order, SalesforcePackage salesforcePackage)
        throws URISyntaxException, IOException {
        List<Opportunity> opportunities = opportunityGetService.getOpportunitiesByOrderId(order.getOrderId());
        String linkedOpportunityCrmId = order.getSfdcOpportunityId();
        List<Opportunity> switchedOpportunities = opportunities
            .stream()
            .filter(opportunity -> !opportunity.getCrmId().equals(linkedOpportunityCrmId))
            .toList();
        if (CollectionUtils.isNotEmpty(switchedOpportunities)) {
            for (Opportunity opportunity : switchedOpportunities) {
                switchSalesforceOpportunity(loginInfo, opportunity, salesforcePackage);
            }
        }
    }

    private void syncSwitchedOpportunitiesForCompositeOrder(
        SalesforceLoginInfo loginInfo,
        CompositeOrder compositeOrder,
        SalesforcePackage salesforcePackage
    ) throws URISyntaxException, IOException {
        List<Opportunity> opportunities = opportunityGetService.getOpportunitiesByOrderId(compositeOrder.getCompositeOrderId());
        String linkedOpportunityCrmId = compositeOrder.getCrmOpportunityId();
        List<Opportunity> switchedOpportunities = opportunities
            .stream()
            .filter(opportunity -> !opportunity.getCrmId().equals(linkedOpportunityCrmId))
            .toList();
        if (CollectionUtils.isNotEmpty(switchedOpportunities)) {
            for (Opportunity opportunity : switchedOpportunities) {
                switchSalesforceOpportunityForCompositeOrder(loginInfo, opportunity, salesforcePackage);
            }
        }
    }

    // an order was switched from this opportunity, link to other order or clean up its data
    private void switchSalesforceOpportunity(SalesforceLoginInfo loginInfo, Opportunity opportunity, SalesforcePackage salesforcePackage)
        throws URISyntaxException, IOException {
        resetOpportunity(loginInfo, opportunity, salesforcePackage);
        opportunityService.deleteOrphanOpportunityByCrmIdIfNeeded(opportunity.getCrmId());
        List<Order> orders = orderGetService.getOrdersByCrmOpportunityId(opportunity.getCrmId());
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        Order nextPrimaryOrder = orders.stream().max(Comparator.comparing(Order::getCreatedOn)).orElseThrow();
        opportunityService.updatePrimaryOrderIdForOpportunity(nextPrimaryOrder);
        salesforceJobQueueService.dispatchOrderSync(nextPrimaryOrder);
    }

    private void switchSalesforceOpportunityForCompositeOrder(
        SalesforceLoginInfo loginInfo,
        Opportunity opportunity,
        SalesforcePackage salesforcePackage
    ) throws URISyntaxException, IOException {
        resetOpportunity(loginInfo, opportunity, salesforcePackage);
        opportunityService.deleteOrphanOpportunityByCrmIdIfNeeded(opportunity.getCrmId());
        //TODO: Extend this further for other scenario - check next primary composite order and sync it
    }

    public void resetOpportunity(String opportunityCrmId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String lockKeyId = getLockKeyIdByOpportunityCrmId(opportunityCrmId);
        String lockKey = String.format(OPPORTUNITY_SYNC_KEY_FORMAT, lockKeyId);
        dslContext.transaction(configuration -> {
            waitForLockOrThrow(configuration, lockKey, LOCK_WAIT_MS);
            SalesforceLoginInfo loginInfo = getLoginInfoWithMultiCurrency();
            SalesforcePackage salesforcePackage = getSalesforcePackage();
            Opportunity opportunity = opportunityGetService.getOpportunityByCrmOpportunityId(opportunityCrmId);
            resetOpportunity(loginInfo, opportunity, salesforcePackage);
        });
    }

    // get account id for lock key, fall back to opportunity crm id
    private String getLockKeyIdByOpportunityCrmId(String opportunityCrmId) {
        Optional<Opportunity> optionalOpportunity = opportunityGetService.getOptionalOpportunityByCrmOpportunityId(opportunityCrmId);
        if (optionalOpportunity.isPresent()) {
            return optionalOpportunity.get().getAccountId();
        }
        List<Order> orders = orderGetService.getOrdersByCrmOpportunityId(opportunityCrmId);
        if (CollectionUtils.isNotEmpty(orders)) {
            return orders.stream().findFirst().orElseThrow().getAccountId();
        }
        return opportunityCrmId;
    }

    private void resetOpportunity(SalesforceLoginInfo loginInfo, Opportunity opportunity, SalesforcePackage salesforcePackage) {
        // reset custom fields on opportunity
        cleanupSalesforceOpportunity(loginInfo, opportunity, salesforcePackage);

        try {
            // remove order lines
            List<String> orderLineIds = new ArrayList<>();
            List<String> currentOrderLineIds = getOrderLinesForOpportunityFromSalesforce(opportunity.getCrmId())
                .records()
                .stream()
                .map(SubskribeSalesforceOrderLine::getId)
                .toList();
            List<String> orphanedOrderLineIds = getOrphanedOrderLinesFromSalesforce()
                .records()
                .stream()
                .map(SubskribeSalesforceOrderLine::getId)
                .toList();
            orderLineIds.addAll(currentOrderLineIds);
            orderLineIds.addAll(orphanedOrderLineIds);
            salesforceRestService.deleteMany(loginInfo, orderLineIds);

            // remove order(s)
            List<String> orderIds = new ArrayList<>();
            List<String> currentOrderIds = getOrderObjectsFromSalesforce(opportunity.getCrmId())
                .stream()
                .map(SubskribeSalesforceOrder::getId)
                .toList();
            List<String> orphanedOrderIds = getOrphanedOrderObjectsFromSalesforce().stream().map(SubskribeSalesforceOrder::getId).toList();
            orderIds.addAll(currentOrderIds);
            orderIds.addAll(orphanedOrderIds);
            salesforceRestService.deleteMany(loginInfo, orderIds);

            // remove arr trends
            List<String> arrIds = new ArrayList<>();
            List<String> currentArrIds = salesforceArrTrendService
                .getSortedArrTrendsForOpportunityFromSalesforce(opportunity.getCrmId())
                .map(SubskribeSalesforceArrTrend::getId)
                .toList();
            List<String> orphanedArrIds = salesforceArrTrendService
                .getOrphanedArrTrendsFromSalesforce()
                .map(SubskribeSalesforceArrTrend::getId)
                .toList();
            arrIds.addAll(currentArrIds);
            arrIds.addAll(orphanedArrIds);
            salesforceRestService.deleteMany(loginInfo, arrIds);
        } catch (URISyntaxException | IOException e) {
            LOGGER.info("Could not delete related objects for Salesforce Opportunity: {}", opportunity.getCrmId(), e);
        }
    }

    private void cleanupSalesforceOpportunity(SalesforceLoginInfo loginInfo, Opportunity opportunity, SalesforcePackage salesforcePackage) {
        SalesforceOpportunityFields salesforceOpportunity = new SalesforceOpportunityFields();
        salesforceOpportunity.setNonRecurringTotal(BigDecimal.ZERO);
        salesforceOpportunity.setOrderAverageARR(BigDecimal.ZERO);
        salesforceOpportunity.setOrderDeltaARR(BigDecimal.ZERO);
        salesforceOpportunity.setOrderEntryARR(BigDecimal.ZERO);
        salesforceOpportunity.setOrderExitARR(BigDecimal.ZERO);
        salesforceOpportunity.setRecurringTotal(BigDecimal.ZERO);
        salesforceOpportunity.setSubscriptionARR(BigDecimal.ZERO);
        salesforceOpportunity.setSubscriptionAverageARR(BigDecimal.ZERO);
        salesforceOpportunity.setSubscriptionEntryARR(BigDecimal.ZERO);
        salesforceOpportunity.setSubscriptionExpectedARR(BigDecimal.ZERO);
        salesforceOpportunity.setSubscriptionTCV(BigDecimal.ZERO);
        salesforceOpportunity.setSubscriptionARRExit(BigDecimal.ZERO);
        salesforceOpportunity.setAutoRenewal(false);
        salesforceOpportunity.setSubscriptionExitARR(BigDecimal.ZERO);
        salesforceOpportunity.setOpportunityLink(StringUtils.EMPTY);
        salesforceOpportunity.setOrderTotal(BigDecimal.ZERO);
        salesforceOpportunity.setOrderExecutionDate(null);
        salesforceOpportunity.setOrderLink(StringUtils.EMPTY);
        salesforceOpportunity.setOrderStatus(StringUtils.EMPTY);
        salesforceOpportunity.setPrimaryOrderID(StringUtils.EMPTY);
        salesforceOpportunity.setSubscriptionLink(StringUtils.EMPTY);
        salesforceOpportunity.setTermEndDate(null);
        salesforceOpportunity.setTermStartDate(null);
        if (salesforcePackage.compareTo(VERSION_1_63) < 0) {
            salesforceOpportunity.setOrderDeltaARR(null);
        }
        if (salesforcePackage.compareTo(VERSION_1_75) >= 0) {
            salesforceOpportunity.setBillingCycle(StringUtils.EMPTY);
            salesforceOpportunity.setPaymentTerm(StringUtils.EMPTY);
        }
        if (salesforcePackage.compareTo(VERSION_1_78) >= 0) {
            salesforceOpportunity.setOrderStartDateType(StringUtils.EMPTY);

            // TODO: This displays as 1970-1-1 on SFDC. There needs to be a better way to clear this field.
            salesforceOpportunity.setOrderExpirationDate(Instant.ofEpochSecond(0).toString());
        }

        if (salesforcePackage.compareTo(VERSION_1_84) >= 0) {
            salesforceOpportunity.setOrderDiscountPercent(BigDecimal.ZERO);
            salesforceOpportunity.setOrderDiscountTotal(BigDecimal.ZERO);
        }

        try {
            salesforceRestService.updateOne(loginInfo, SalesforceOpportunity.OPPORTUNITY_API_NAME, salesforceOpportunity, opportunity.getCrmId());
        } catch (SalesforceResponseException | URISyntaxException | IOException e) {
            LOGGER.info("Salesforce opportunity could not be updated: {}", opportunity.getCrmId(), e);
        }
    }

    private boolean getMultiCurrency(SalesforceLoginInfo loginInfo) throws IOException, URISyntaxException {
        try {
            String queryReturn = salesforceRestService.getQuery(loginInfo, SELECT_CURRENCY_QUERY);
            SalesforceQueryResponse<SalesforceCurrencyType> queryResponse = SALESFORCE_GSON.fromJson(queryReturn, CURRENCY_QUERY_RESPONSE_TYPE);
            return !queryResponse.records().isEmpty();
        } catch (SalesforceResponseException e) {
            List<SalesforceErrorMessage> salesforceErrorMessages = OBJECT_MAPPER.readValue(e.getResponseBody(), new TypeReference<>() {});
            if (
                CollectionUtils.isNotEmpty(salesforceErrorMessages) &&
                Objects.equals(salesforceErrorMessages.get(0).errorCode(), INVALID_TYPE_ERROR_CODE)
            ) {
                return false;
            }
        }
        throw new ServiceFailureException("unable to run query on salesforce: " + SELECT_CURRENCY_QUERY);
    }

    private void updateOpportunity(Order order, SalesforceLoginInfo loginInfo, String opportunityId, SalesforcePackage salesforcePackage)
        throws URISyntaxException, IOException {
        SalesforceOpportunityFields salesforceOpportunity = prepareOpportunity(order, opportunityId, salesforcePackage);
        salesforceRestService.updateOne(loginInfo, SalesforceOpportunity.OPPORTUNITY_API_NAME, salesforceOpportunity, opportunityId);
        LOGGER.info("Updated Salesforce opportunity for {}: {}", opportunityId, salesforceOpportunity);
    }

    private void updateOpportunityForCancelAndRestructure(
        Order restructureOrder,
        Order cancelOrder,
        SalesforceLoginInfo loginInfo,
        String opportunityId,
        SalesforcePackage salesforcePackage
    ) throws URISyntaxException, IOException {
        SalesforceOpportunityFields salesforceOpportunity = prepareOpportunityForCancelAndRestructure(
            restructureOrder,
            cancelOrder,
            opportunityId,
            salesforcePackage
        );
        salesforceRestService.updateOne(loginInfo, SalesforceOpportunity.OPPORTUNITY_API_NAME, salesforceOpportunity, opportunityId);
        LOGGER.info("Updated Salesforce opportunity for {}: {}", opportunityId, salesforceOpportunity);
    }

    private SalesforceOpportunityFields prepareOpportunity(Order order, String opportunityCrmId, SalesforcePackage salesforcePackage) {
        SalesforceOpportunityFields salesforceOpportunity = new SalesforceOpportunityFields();
        String orderLink = SubskribeUrlGenerator.getUiUrlForOrder(siteUrl, order.getOrderId());
        OrderDetail orderDetail = orderDataAggregator.getOrderDetail(order, true, true);
        SalesforceFormatter formatter = new SalesforceFormatter(tenantSettingService.getTenantSetting().getDefaultTimeZone());

        // order fields
        salesforceOpportunity.setOrderLink(orderLink);
        if (OrderStatus.EXPIRED != order.getStatus()) {
            salesforceOpportunity.setOrderStatus(order.getStatus().name());
        }
        salesforceOpportunity.setPrimaryOrderID(order.getOrderId());
        salesforceOpportunity.setOrderTotal(order.getTotalAmount());
        salesforceOpportunity.setTermStartDate(formatter.dateFormat(order.getStartDate()));
        salesforceOpportunity.setTermEndDate(formatter.endDateFormat(order.getEndDate()));
        salesforceOpportunity.setAutoRenewal(order.getAutoRenew());
        salesforceOpportunity.setOrderType(null);
        if (salesforcePackage.compareTo(VERSION_1_73) >= 0) {
            if (ALLOWED_ORDER_TYPES.contains(order.getOrderType())) {
                salesforceOpportunity.setOrderType(order.getOrderType().name());
            } else {
                salesforceOpportunity.setOrderType(OrderType.NEW.name());
            }
        }
        if (Objects.nonNull(order.getExecutedOn())) {
            TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
            Instant startOfExecutionDate = DateTimeConverter.getStartOfCurrentDay(order.getExecutedOn(), timeZone);
            salesforceOpportunity.setOrderExecutionDate(startOfExecutionDate.toString());
        }

        // subscription metrics fields
        if (StringUtils.isNotBlank(order.getExternalSubscriptionId())) {
            String subscriptionLink = SubskribeUrlGenerator.getUiUrlForSubscription(siteUrl, order.getExternalSubscriptionId());
            Metrics metrics = metricsService.getSubscriptionMetrics(order.getExternalSubscriptionId(), Instant.now());
            salesforceOpportunity.setSubscriptionLink(subscriptionLink);
            salesforceOpportunity.setSubscriptionTCV(metrics.getTcv());
            salesforceOpportunity.setSubscriptionARR(metrics.getArr());
            salesforceOpportunity.setSubscriptionAverageARR(metrics.getAverageArr());
            salesforceOpportunity.setSubscriptionEntryARR(metrics.getEntryArr());
            salesforceOpportunity.setSubscriptionARRExit(metrics.getExitArr());
            salesforceOpportunity.setSubscriptionExitARR(metrics.getExitArr());
        } else {
            salesforceOpportunity.setSubscriptionLink(StringUtils.EMPTY);
            salesforceOpportunity.setSubscriptionTCV(BigDecimal.ZERO);
            salesforceOpportunity.setSubscriptionARR(BigDecimal.ZERO);
            salesforceOpportunity.setSubscriptionAverageARR(BigDecimal.ZERO);
            salesforceOpportunity.setSubscriptionEntryARR(BigDecimal.ZERO);
            salesforceOpportunity.setSubscriptionARRExit(BigDecimal.ZERO);
            salesforceOpportunity.setSubscriptionExitARR(BigDecimal.ZERO);
        }

        // order metrics fields
        Metrics orderMetrics = metricsService.getOrderMetrics(order.getOrderId(), Instant.now());
        salesforceOpportunity.setOrderEntryARR(orderMetrics.getEntryArr());
        salesforceOpportunity.setOrderExitARR(orderMetrics.getExitArr());
        salesforceOpportunity.setOrderAverageARR(orderMetrics.getAverageArr());
        salesforceOpportunity.setOrderDeltaARR(orderMetrics.getDeltaArr());
        salesforceOpportunity.setNonRecurringTotal(MetricsService.getOrderNonRecurringTotal(orderDetail));
        salesforceOpportunity.setRecurringTotal(MetricsService.getOrderRecurringTotal(orderDetail));

        // opportunity fields
        Opportunity opportunity = opportunityGetService.getOpportunityByCrmOpportunityId(opportunityCrmId);
        String opportunityLink = SubskribeUrlGenerator.getUiUrlForOpportunity(siteUrl, opportunity.getOpportunityId());
        salesforceOpportunity.setOpportunityLink(opportunityLink);

        // version checks
        if (salesforcePackage.compareTo(VERSION_1_63) < 0) {
            salesforceOpportunity.setOrderDeltaARR(null);
        }
        if (salesforcePackage.compareTo(VERSION_1_75) >= 0) {
            salesforceOpportunity.setBillingCycle(order.getBillingCycle().getCycle().getPeriodicityName());
            salesforceOpportunity.setPaymentTerm(order.getPaymentTerm().getDisplayName());
        }
        if (salesforcePackage.compareTo(VERSION_1_78) >= 0) {
            salesforceOpportunity.setOrderStartDateType(order.getStartDateType().getDisplayName());

            if (Objects.nonNull(order.getExpiresOn())) {
                salesforceOpportunity.setOrderExpirationDate(formatter.dateFormat(order.getExpiresOn()));
            } else {
                // TODO: This displays as 1970-1-1 on SFDC. There needs to be a better way to clear this field.
                salesforceOpportunity.setOrderExpirationDate(Instant.ofEpochSecond(0).toString());
            }
        }

        if (salesforcePackage.compareTo(VERSION_1_84) >= 0) {
            Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
            salesforceOpportunity.setOrderDiscountPercent(
                OrderDiscountService.calculateOrderLineNetDiscountPercent(order.getLineItemsNetEffect(), chargeMap)
            );
            salesforceOpportunity.setOrderDiscountTotal(
                OrderDiscountService.getNetDiscountAmountCurrencyScaled(order.getLineItemsNetEffect(), chargeMap)
            );
        }

        return salesforceOpportunity;
    }

    private SalesforceOpportunityFields prepareOpportunityForCancelAndRestructure(
        Order restructureOrder,
        Order cancelOrder,
        String opportunityId,
        SalesforcePackage salesforcePackage
    ) {
        String compositeOrderId = restructureOrder.getCompositeOrderId();
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        SalesforceOpportunityFields salesforceOpportunity = new SalesforceOpportunityFields();
        String compositeOrderLink = SubskribeUrlGenerator.getUiUrlForCompositeOrder(siteUrl, compositeOrderId);
        SalesforceFormatter formatter = new SalesforceFormatter(tenantSettingService.getTenantSetting().getDefaultTimeZone());

        // order fields populated from composite order
        salesforceOpportunity.setOrderLink(compositeOrderLink);
        if (OrderStatus.EXPIRED != compositeOrder.getStatus()) {
            salesforceOpportunity.setOrderStatus(compositeOrder.getStatus().name());
        }
        salesforceOpportunity.setPrimaryOrderID(compositeOrderId);

        // order fields populated from restructure order
        salesforceOpportunity.setOrderTotal(BigDecimal.ZERO.add(restructureOrder.getTotalAmount()).add(cancelOrder.getTotalAmount()));
        salesforceOpportunity.setTermStartDate(formatter.dateFormat(restructureOrder.getStartDate()));
        salesforceOpportunity.setTermEndDate(formatter.endDateFormat(restructureOrder.getEndDate()));
        salesforceOpportunity.setAutoRenewal(restructureOrder.getAutoRenew());
        if (Objects.nonNull(restructureOrder.getExecutedOn())) {
            TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
            Instant startOfExecutionDate = DateTimeConverter.getStartOfCurrentDay(restructureOrder.getExecutedOn(), timeZone);
            salesforceOpportunity.setOrderExecutionDate(startOfExecutionDate.toString());
        }

        // subscription metrics fields
        if (StringUtils.isNotBlank(restructureOrder.getExternalSubscriptionId())) {
            String subscriptionLink = SubskribeUrlGenerator.getUiUrlForSubscription(siteUrl, restructureOrder.getExternalSubscriptionId());
            Metrics metrics = metricsService.getSubscriptionMetrics(restructureOrder.getExternalSubscriptionId(), Instant.now());
            salesforceOpportunity.setSubscriptionLink(subscriptionLink);
            salesforceOpportunity.setSubscriptionTCV(metrics.getTcv());
            salesforceOpportunity.setSubscriptionARR(metrics.getArr());
            salesforceOpportunity.setSubscriptionAverageARR(metrics.getAverageArr());
            salesforceOpportunity.setSubscriptionEntryARR(metrics.getEntryArr());
            salesforceOpportunity.setSubscriptionARRExit(metrics.getExitArr());
            salesforceOpportunity.setSubscriptionExitARR(metrics.getExitArr());
        } else {
            salesforceOpportunity.setSubscriptionLink(StringUtils.EMPTY);
            salesforceOpportunity.setSubscriptionTCV(BigDecimal.ZERO);
            salesforceOpportunity.setSubscriptionARR(BigDecimal.ZERO);
            salesforceOpportunity.setSubscriptionAverageARR(BigDecimal.ZERO);
            salesforceOpportunity.setSubscriptionEntryARR(BigDecimal.ZERO);
            salesforceOpportunity.setSubscriptionARRExit(BigDecimal.ZERO);
            salesforceOpportunity.setSubscriptionExitARR(BigDecimal.ZERO);
        }

        // order metrics fields populated from metrics of restructure order as well as cancel order
        Metrics restructureOrderMetrics = metricsService.getOrderMetrics(restructureOrder.getOrderId(), Instant.now());
        Metrics cancelOrderMetrics = metricsService.getOrderMetrics(cancelOrder.getOrderId(), Instant.now());
        salesforceOpportunity.setOrderEntryARR(BigDecimal.ZERO.add(restructureOrderMetrics.getEntryArr()).add(cancelOrderMetrics.getEntryArr()));
        salesforceOpportunity.setOrderExitARR(BigDecimal.ZERO.add(restructureOrderMetrics.getExitArr()).add(cancelOrderMetrics.getExitArr()));
        salesforceOpportunity.setOrderAverageARR(BigDecimal.ZERO.add(restructureOrderMetrics.getArr()).add(cancelOrderMetrics.getArr()));
        // deltaArr, recurringTotal, non-recurringTotal are calculated from restructure order and cancel order
        // this is similar to what we show on UI for composite view of restructure and cancel order
        salesforceOpportunity.setOrderDeltaARR(BigDecimal.ZERO.add(restructureOrderMetrics.getDeltaArr()).add(cancelOrderMetrics.getDeltaArr()));
        salesforceOpportunity.setNonRecurringTotal(
            BigDecimal.ZERO.add(restructureOrderMetrics.getNonRecurringTotal()).add(cancelOrderMetrics.getNonRecurringTotal())
        );
        salesforceOpportunity.setRecurringTotal(
            BigDecimal.ZERO.add(restructureOrderMetrics.getRecurringTotal()).add(cancelOrderMetrics.getRecurringTotal())
        );

        // opportunity fields
        Opportunity opportunity = opportunityGetService.getOpportunityByCrmOpportunityId(opportunityId);
        String opportunityLink = SubskribeUrlGenerator.getUiUrlForOpportunity(siteUrl, opportunity.getOpportunityId());
        salesforceOpportunity.setOpportunityLink(opportunityLink);

        // version checks
        if (salesforcePackage.compareTo(VERSION_1_63) < 0) {
            salesforceOpportunity.setOrderDeltaARR(null);
        }

        if (salesforcePackage.compareTo(VERSION_1_75) >= 0) {
            salesforceOpportunity.setBillingCycle(restructureOrder.getBillingCycle().getCycle().getPeriodicityName());
            salesforceOpportunity.setPaymentTerm(restructureOrder.getPaymentTerm().getDisplayName());
        }

        if (salesforcePackage.compareTo(VERSION_1_78) >= 0) {
            salesforceOpportunity.setOrderStartDateType(restructureOrder.getStartDateType().getDisplayName());

            if (Objects.nonNull(restructureOrder.getExpiresOn())) {
                salesforceOpportunity.setOrderExpirationDate(formatter.dateFormat(restructureOrder.getExpiresOn()));
            } else {
                // TODO: This displays as 1970-1-1 on SFDC. There needs to be a better way to clear this field.
                salesforceOpportunity.setOrderExpirationDate(Instant.ofEpochSecond(0).toString());
            }
        }

        if (salesforcePackage.compareTo(VERSION_1_84) >= 0) {
            Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, restructureOrder.getLineItems());
            salesforceOpportunity.setOrderDiscountPercent(
                OrderDiscountService.calculateOrderLineNetDiscountPercent(restructureOrder.getLineItemsNetEffect(), chargeMap)
            );
            salesforceOpportunity.setOrderDiscountTotal(
                OrderDiscountService.getNetDiscountAmountCurrencyScaled(restructureOrder.getLineItemsNetEffect(), chargeMap)
            );
        }

        return salesforceOpportunity;
    }

    private SubskribeSalesforceOrder getSubskribeSalesforceOrder(String siteUrl, Order order, String opptyId, SalesforcePackage salesforcePackage) {
        var salesforceOrder = new SubskribeSalesforceOrder();
        var formatter = new SalesforceFormatter(tenantSettingService.getTenantSetting().getDefaultTimeZone());

        salesforceOrder.setOrder(order.getOrderId());
        salesforceOrder.setOpportunity(opptyId);
        String orderLink = SubskribeUrlGenerator.getUiUrlForOrder(siteUrl, order.getOrderId());
        salesforceOrder.setOrderLink(orderLink);
        salesforceOrder.setCurrencyCode(order.getCurrency().getCurrencyCode());

        if (salesforcePackage.compareTo(VERSION_1_72) >= 0 && StringUtils.isNotBlank(order.getCompositeOrderId())) {
            salesforceOrder.setCompositeOrderId(order.getCompositeOrderId());
        }

        if (salesforcePackage.compareTo(VERSION_1_78) >= 0) {
            Metrics orderMetrics = metricsService.getOrderMetrics(order.getOrderId(), Instant.now());
            salesforceOrder.setCurrencyCode(order.getCurrency().getCurrencyCode());
            salesforceOrder.setOrderStartDate(formatter.dateFormat(order.getStartDate()));
            salesforceOrder.setOrderEndDate(formatter.endDateFormat(order.getEndDate()));
            if (OrderStatus.EXPIRED != order.getStatus()) {
                salesforceOrder.setOrderStatus(order.getStatus().name());
            }
            salesforceOrder.setOrderTotal(order.getTotalAmount());
            salesforceOrder.setOrderEntryArr(orderMetrics.getEntryArr());
            salesforceOrder.setOrderExitArr(orderMetrics.getExitArr());
            salesforceOrder.setOrderAverageArr(orderMetrics.getAverageArr());
            salesforceOrder.setPrimaryOrder(order.getIsPrimaryOrderForSfdcOpportunity());
        }

        if (salesforcePackage.compareTo(VERSION_1_80) >= 0) {
            salesforceOrder.setOrderName(order.getName());
        }

        if (salesforcePackage.compareTo(VERSION_1_84) >= 0) {
            Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
            salesforceOrder.setOrderDiscountPercent(
                OrderDiscountService.calculateOrderLineNetDiscountPercent(order.getLineItemsNetEffect(), chargeMap)
            );
            salesforceOrder.setOrderDiscountTotal(OrderDiscountService.getNetDiscountAmountCurrencyScaled(order.getLineItemsNetEffect(), chargeMap));
        }
        return salesforceOrder;
    }

    private List<SubskribeSalesforceOrder> getSubskribeSalesforceOrders(
        String siteUrl,
        List<Order> orders,
        String opptyId,
        SalesforcePackage salesforcePackage
    ) {
        return orders.stream().map(o -> getSubskribeSalesforceOrder(siteUrl, o, opptyId, salesforcePackage)).toList();
    }

    private Optional<SubskribeSalesforceOrder> replaceOrdersForOpportunity(
        SalesforceLoginInfo loginInfo,
        String opportunityId,
        SalesforcePackage salesforcePackage,
        List<String> errors
    ) throws URISyntaxException, IOException {
        // Delete current order lines and orders
        List<String> orderIds = new ArrayList<>();
        List<String> currentOrderIds = getOrderObjectsFromSalesforce(opportunityId).stream().map(SubskribeSalesforceOrder::getId).toList();
        List<String> orphanedOrderIds = getOrphanedOrderObjectsFromSalesforce().stream().map(SubskribeSalesforceOrder::getId).toList();
        orderIds.addAll(currentOrderIds);
        orderIds.addAll(orphanedOrderIds);

        List<String> orderLineIds = new ArrayList<>();
        List<String> currentOrderLineIds = getOrderLinesForOrdersFromSalesforce(orderIds).stream().map(SubskribeSalesforceOrderLine::getId).toList();
        List<String> orphanedOrderLineIds = getOrphanedOrderLinesFromSalesforce()
            .records()
            .stream()
            .map(SubskribeSalesforceOrderLine::getId)
            .toList();
        orderLineIds.addAll(currentOrderLineIds);
        orderLineIds.addAll(orphanedOrderLineIds);
        salesforceRestService.deleteMany(loginInfo, orderLineIds);
        salesforceRestService.deleteMany(loginInfo, orderIds);

        // Collect orders from compositeOrders and set primary
        List<Order> orders = new ArrayList<>(orderGetService.getOrdersByCrmOpportunityId(opportunityId));
        List<SubskribeSalesforceOrder> salesforceOrders = new ArrayList<>(
            getSubskribeSalesforceOrders(siteUrl, orders, opportunityId, salesforcePackage)
        );
        List<CompositeOrder> compositeOrders = compositeOrderGetService.getCompositeOrdersByCrmOpportunityId(opportunityId);
        List<SubskribeSalesforceOrder> salesforceCompositeOrders = getSubskribeSalesforceCompositeOrders(
            siteUrl,
            compositeOrders,
            opportunityId,
            salesforcePackage
        );
        salesforceOrders.addAll(salesforceCompositeOrders);

        compositeOrders.forEach(co -> {
            List<Order> subOrders = compositeOrderGetService.getOrdersInCompositeOrder(co.getCompositeOrderId());
            if (BooleanUtils.isTrue(co.getIsPrimaryCompositeOrderForCrmOpportunity())) {
                subOrders.forEach(o -> o.setIsPrimaryOrderForSfdcOpportunity(true));
            }
            Optional<Order> mainSubOrderOptional = subOrders
                .stream()
                .filter(so -> so.getOrderType() == OrderType.RENEWAL || so.getOrderType() == OrderType.RESTRUCTURE)
                .findFirst();
            mainSubOrderOptional.ifPresent(orders::add);
        });
        List<String> primaryOrderIds = orders
            .stream()
            .filter(o -> BooleanUtils.isTrue(o.getIsPrimaryOrderForSfdcOpportunity()))
            .map(Order::getOrderId)
            .toList();

        // Create updated orders and sync custom fields
        LOGGER.info("Syncing Salesforce orders for opportunity {}: {}", opportunityId, salesforceOrders);
        salesforceRestService.createMany(loginInfo, SubskribeSalesforceOrder.class, salesforceOrders);
        List<SubskribeSalesforceOrder> updatedSalesforceOrders = getOrderObjectsFromSalesforce(opportunityId);
        LOGGER.info("Updated Salesforce orders for opportunity {}: {}", opportunityId, updatedSalesforceOrders);
        if (featureService.isEnabled(Feature.CRM_FIELD_MAPPING)) {
            updatedSalesforceOrders.forEach(o -> syncOrderLevelCustomFieldsWithMapping(loginInfo, o, errors));
        } else if (featureService.isEnabled(Feature.CUSTOM_FIELDS_SYNC_TO_CRM)) {
            updatedSalesforceOrders.forEach(o -> syncOrderLevelCustomFields(loginInfo, o));
        }
        return updatedSalesforceOrders.stream().filter(o -> primaryOrderIds.contains(o.getOrder())).findAny();
    }

    private void syncOrderLevelCustomFieldsWithMapping(
        SalesforceLoginInfo loginInfo,
        SubskribeSalesforceOrder updatedSalesforceOrder,
        List<String> errors
    ) {
        List<CrmFieldMapping> outboundCrmFieldMappings = crmFieldMappingService
            .getCrmFieldMappings(CrmType.SALESFORCE, CrmObjectType.ORDER)
            .stream()
            .filter(m -> CrmSyncDirection.OUTBOUND == m.getDirection())
            .toList();

        // No custom field mappings found
        if (CollectionUtils.isEmpty(outboundCrmFieldMappings)) {
            return;
        }

        List<SalesforceField> salesforceFields = salesforceGetService.getObjectFields(loginInfo, SubskribeSalesforceOrder.class, errors);
        CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ORDER, updatedSalesforceOrder.getOrder());
        List<CustomFieldValue> customFieldsToSync = customField.getEntries().values().stream().toList();

        Map<String, String> customFieldUpdateMap = new HashMap<>();
        for (var crmFieldMapping : outboundCrmFieldMappings) {
            populateCustomFieldUpdateMap(customFieldUpdateMap, customFieldsToSync, crmFieldMapping, salesforceFields, errors);
        }

        if (!customFieldUpdateMap.isEmpty()) {
            try {
                LOGGER.info("Custom field update map for order {}: {}", updatedSalesforceOrder.getOrder(), customFieldUpdateMap);
                salesforceRestService.updateOne(loginInfo, SubskribeSalesforceOrder.API_NAME, customFieldUpdateMap, updatedSalesforceOrder.getId());
            } catch (SalesforceResponseException | URISyntaxException | IOException e) {
                errors.add(String.format("Failed to update custom fields for salesforce order %s", updatedSalesforceOrder.getId()));
            }
        }
    }

    private String formatSalesforceCustomFieldDisplay(CustomFieldValue value) {
        return switch (value.getType()) {
            case MULTISELECT_PICKLIST -> String.join(";", value.getSelections());
            case PICKLIST -> CollectionUtils.isEmpty(value.getSelections()) ? "" : value.getSelections().get(0);
            case STRING -> value.getValue();
        };
    }

    private List<SubskribeSalesforceOrder> getSubskribeSalesforceCompositeOrders(
        String siteUrl,
        List<CompositeOrder> compositeOrders,
        String opptyId,
        SalesforcePackage salesforcePackage
    ) {
        List<SubskribeSalesforceOrder> salesforceOrders = new ArrayList<>();
        compositeOrders.forEach(co -> {
            Optional<SubskribeSalesforceOrder> sfdcOrderOptional = getSubskribeSalesforceCompositeOrder(siteUrl, co, opptyId, salesforcePackage);
            sfdcOrderOptional.ifPresent(salesforceOrders::add);
        });
        return salesforceOrders;
    }

    private Optional<SubskribeSalesforceOrder> getSubskribeSalesforceCompositeOrder(
        String siteUrl,
        CompositeOrder compositeOrder,
        String opptyId,
        SalesforcePackage salesforcePackage
    ) {
        List<Order> subOrders = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrder.getCompositeOrderId());
        if (BooleanUtils.isTrue(compositeOrder.getIsPrimaryCompositeOrderForCrmOpportunity())) {
            subOrders.forEach(o -> o.setIsPrimaryOrderForSfdcOpportunity(true));
        }
        Optional<Order> mainSubOrderOptional = subOrders
            .stream()
            .filter(so -> so.getOrderType() == OrderType.RENEWAL || so.getOrderType() == OrderType.RESTRUCTURE)
            .findFirst();

        if (mainSubOrderOptional.isEmpty()) {
            return Optional.empty();
        }

        Order order = mainSubOrderOptional.get();

        var salesforceOrder = new SubskribeSalesforceOrder();
        var formatter = new SalesforceFormatter(tenantSettingService.getTenantSetting().getDefaultTimeZone());

        salesforceOrder.setOrder(order.getOrderId());
        salesforceOrder.setOpportunity(opptyId);
        String orderLink = SubskribeUrlGenerator.getUiUrlForOrder(siteUrl, order.getOrderId());
        salesforceOrder.setOrderLink(orderLink);
        salesforceOrder.setCurrencyCode(order.getCurrency().getCurrencyCode());

        if (salesforcePackage.compareTo(VERSION_1_72) >= 0 && StringUtils.isNotBlank(order.getCompositeOrderId())) {
            salesforceOrder.setCompositeOrderId(order.getCompositeOrderId());
        }

        if (salesforcePackage.compareTo(VERSION_1_78) >= 0) {
            salesforceOrder.setCurrencyCode(order.getCurrency().getCurrencyCode());
            salesforceOrder.setOrderStartDate(formatter.dateFormat(order.getStartDate()));
            salesforceOrder.setOrderEndDate(formatter.endDateFormat(order.getEndDate()));
            if (OrderStatus.EXPIRED != order.getStatus()) {
                salesforceOrder.setOrderStatus(order.getStatus().name());
            }
            salesforceOrder.setPrimaryOrder(order.getIsPrimaryOrderForSfdcOpportunity());

            // Combined Composite Order Metrics
            BigDecimal tcv = BigDecimal.ZERO;
            BigDecimal entryArr = BigDecimal.ZERO;
            BigDecimal exitArr = BigDecimal.ZERO;
            BigDecimal averageArr = BigDecimal.ZERO;

            for (Order so : subOrders) {
                Metrics soMetrics = metricsService.getOrderMetrics(so, Instant.now());
                tcv = tcv.add(soMetrics.getTcv());
                entryArr = entryArr.add(soMetrics.getEntryArr());
                exitArr = exitArr.add(soMetrics.getExitArr());
                averageArr = averageArr.add(soMetrics.getAverageArr());
            }
            salesforceOrder.setOrderTotal(tcv);
            salesforceOrder.setOrderEntryArr(entryArr);
            salesforceOrder.setOrderExitArr(exitArr);
            salesforceOrder.setOrderAverageArr(averageArr);
        }

        if (salesforcePackage.compareTo(VERSION_1_80) >= 0) {
            salesforceOrder.setOrderName(order.getName());
        }

        if (salesforcePackage.compareTo(VERSION_1_84) >= 0) {
            Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
            salesforceOrder.setOrderDiscountPercent(
                OrderDiscountService.calculateOrderLineNetDiscountPercent(order.getLineItemsNetEffect(), chargeMap)
            );
            salesforceOrder.setOrderDiscountTotal(OrderDiscountService.getNetDiscountAmountCurrencyScaled(order.getLineItemsNetEffect(), chargeMap));
        }

        return Optional.of(salesforceOrder);
    }

    private void replaceOrderLines(
        List<SubskribeSalesforceOrderLine> orderLines,
        SalesforceLoginInfo loginInfo,
        String opportunityId,
        SalesforcePackage salesforcePackage,
        List<String> errors
    ) throws URISyntaxException, IOException {
        List<String> orderLineIds = new ArrayList<>();
        List<String> currentOrderLineIds = getOrderLinesForOpportunityFromSalesforce(opportunityId)
            .records()
            .stream()
            .map(SubskribeSalesforceOrderLine::getId)
            .toList();
        List<String> orphanedOrderLineIds = getOrphanedOrderLinesFromSalesforce()
            .records()
            .stream()
            .map(SubskribeSalesforceOrderLine::getId)
            .toList();
        orderLineIds.addAll(currentOrderLineIds);
        orderLineIds.addAll(orphanedOrderLineIds);

        if (salesforcePackage.compareTo(VERSION_1_58) < 0) {
            for (var orderLine : orderLines) {
                orderLine.setTCVCurrency(null);
            }
        }
        if (salesforcePackage.compareTo(VERSION_1_66) < 0) {
            for (var orderLine : orderLines) {
                orderLine.setAverageArr(null);
                orderLine.setEntryArr(null);
                orderLine.setExitArr(null);
                orderLine.setDeltaArr(null);
            }
        }
        salesforceRestService.deleteMany(loginInfo, orderLineIds);
        LOGGER.info("Syncing Salesforce order lines for opportunity {}: {}", opportunityId, orderLines);
        salesforceRestService.createMany(loginInfo, SubskribeSalesforceOrderLine.class, orderLines);
        List<SubskribeSalesforceOrderLine> updatedOrderLines = getOrderLinesForOpportunityFromSalesforce(opportunityId).records().stream().toList();
        LOGGER.info("Updated Salesforce order lines for opportunity {}: {}", opportunityId, updatedOrderLines);
        // Sync order line level custom fields if feature enabled

        if (featureService.isEnabled(Feature.CRM_FIELD_MAPPING)) {
            syncLineLevelCustomFieldsWithMapping(loginInfo, opportunityId, errors);
        } else if (featureService.isEnabled(Feature.CUSTOM_FIELDS_SYNC_TO_CRM)) {
            syncLineLevelCustomFields(loginInfo, opportunityId);
        }
    }

    private void syncLineLevelCustomFieldsWithMapping(SalesforceLoginInfo loginInfo, String opportunityId, List<String> errors) {
        List<CrmFieldMapping> outboundCrmFieldMappings = crmFieldMappingService
            .getCrmFieldMappings(CrmType.SALESFORCE, CrmObjectType.ORDER_ITEM)
            .stream()
            .filter(m -> CrmSyncDirection.OUTBOUND == m.getDirection())
            .toList();

        // No custom field mappings found
        if (CollectionUtils.isEmpty(outboundCrmFieldMappings)) {
            return;
        }

        List<SalesforceField> salesforceFields = salesforceGetService.getObjectFields(loginInfo, SubskribeSalesforceOrderLine.class, errors);
        SalesforceQueryResponse<SubskribeSalesforceOrderLine> currentOrderLineResponse = getOrderLinesForOpportunityFromSalesforce(opportunityId);
        Map<String, String> subskribeIdToSalesforceIdMap = currentOrderLineResponse
            .records()
            .stream()
            .collect(Collectors.toMap(SubskribeSalesforceOrderLine::getSubskribeOrderLineId, SubskribeSalesforceOrderLine::getId));

        subskribeIdToSalesforceIdMap
            .keySet()
            .forEach(subskribeOrderLineId -> {
                CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ORDER_ITEM, subskribeOrderLineId);
                List<CustomFieldValue> customFieldsToSync = customField.getEntries().values().stream().toList();

                Map<String, String> customFieldUpdateMap = new HashMap<>();
                for (var crmFieldMapping : outboundCrmFieldMappings) {
                    populateCustomFieldUpdateMap(customFieldUpdateMap, customFieldsToSync, crmFieldMapping, salesforceFields, errors);
                    if (!customFieldUpdateMap.isEmpty()) {
                        try {
                            salesforceRestService.updateOne(
                                loginInfo,
                                SubskribeSalesforceOrderLine.API_NAME,
                                customFieldUpdateMap,
                                subskribeIdToSalesforceIdMap.get(subskribeOrderLineId)
                            );
                        } catch (SalesforceResponseException | URISyntaxException | IOException e) {
                            errors.add(
                                String.format(
                                    "Failed to update custom fields for order line %s on opportunity %s",
                                    subskribeOrderLineId,
                                    opportunityId
                                )
                            );
                        }
                    }
                }
            });
    }

    private void populateCustomFieldUpdateMap(
        Map<String, String> customFieldUpdateMap,
        List<CustomFieldValue> customFieldsToSync,
        CrmFieldMapping crmFieldMapping,
        List<SalesforceField> salesforceFields,
        List<String> errors
    ) {
        // Check that the Subskribe custom field exists
        Optional<CustomFieldValue> customFieldValue = customFieldsToSync
            .stream()
            .filter(c -> StringUtils.equals(crmFieldMapping.getSubskribeFieldName(), c.getName()))
            .findAny();
        if (customFieldValue.isEmpty()) {
            errors.add(String.format("Subskribe custom field missing: %s", crmFieldMapping.getSubskribeFieldName()));
            return;
        }
        CustomFieldValue cfValue = customFieldValue.get();

        // Check that the Salesforce field exists
        Optional<SalesforceField> salesforceField = salesforceFields
            .stream()
            .filter(sf -> StringUtils.equals(crmFieldMapping.getCrmFieldName(), sf.getName()))
            .findAny();
        if (salesforceField.isPresent()) {
            String fieldType = salesforceField.get().getType();
            List<String> options = salesforceField.get().getPicklistValues().stream().map(SalesforcePicklistValue::getValue).toList();
            if (StringUtils.equals(fieldType, SALESFORCE_STRING_FIELD_TYPE) || new HashSet<>(options).containsAll(cfValue.getSelections())) {
                customFieldUpdateMap.put(salesforceField.get().getName(), formatSalesforceCustomFieldDisplay(cfValue));
            } else {
                String message = String.format(
                    "Salesforce picklist options missing for: %s. Expected: %s. Found: %s",
                    salesforceField.get().getName(),
                    cfValue.getOptions(),
                    options
                );
                errors.add(message);
            }
        } else {
            LOGGER.info("Failed to find Salesforce field for field mapping: {}", crmFieldMapping);
            errors.add(String.format("Salesforce field missing: %s", crmFieldMapping.getCrmFieldName()));
        }
    }

    private void syncLineLevelCustomFields(SalesforceLoginInfo loginInfo, String opportunityId) {
        Set<String> salesforceCustomFieldNames = getSalesforceCustomFieldNames(FIND_ORDER_LINE_NON_SUBSKRIBE_CUSTOM_FIELDS_FORMAT);
        if (CollectionUtils.isEmpty(salesforceCustomFieldNames)) {
            return;
        }
        SalesforceQueryResponse<SubskribeSalesforceOrderLine> currentOrderLineResponse = getOrderLinesForOpportunityFromSalesforce(opportunityId);
        Map<String, String> subskribeIdToSalesforceIdMap = currentOrderLineResponse
            .records()
            .stream()
            .collect(Collectors.toMap(SubskribeSalesforceOrderLine::getSubskribeOrderLineId, SubskribeSalesforceOrderLine::getId));

        subskribeIdToSalesforceIdMap
            .keySet()
            .forEach(subskribeOrderLineId -> {
                Map<String, String> customFieldNameValueMap = getCustomFieldsForOrderLine(subskribeOrderLineId);
                Map<String, String> orderLineUpdateMap = new HashMap<>();
                salesforceCustomFieldNames.forEach(salesforceCustomFieldName -> {
                    if (customFieldNameValueMap.containsKey(salesforceCustomFieldName)) {
                        orderLineUpdateMap.put(salesforceCustomFieldName, customFieldNameValueMap.get(salesforceCustomFieldName));
                    }
                });
                if (!orderLineUpdateMap.isEmpty()) {
                    LOGGER.info("Salesforce order line custom fields: {}", orderLineUpdateMap);
                    try {
                        salesforceRestService.updateOne(
                            loginInfo,
                            SubskribeSalesforceOrderLine.API_NAME,
                            orderLineUpdateMap,
                            subskribeIdToSalesforceIdMap.get(subskribeOrderLineId)
                        );
                    } catch (URISyntaxException | IOException e) {
                        String message = String.format("Failed to update custom fields for order line %s", subskribeOrderLineId);
                        LOGGER.warn(message, e);
                    }
                }
            });
    }

    private void syncOrderLevelCustomFields(SalesforceLoginInfo loginInfo, SubskribeSalesforceOrder updatedSalesforceOrder) {
        Set<String> salesforceOrderCustomFieldNames = getSalesforceCustomFieldNames(FIND_ORDER_NON_SUBSKRIBE_CUSTOM_FIELDS_FORMAT);
        if (CollectionUtils.isEmpty(salesforceOrderCustomFieldNames)) {
            return;
        }

        Map<String, String> customFieldNameValueMap = getCustomFieldsForOrder(updatedSalesforceOrder.getOrder());
        if (customFieldNameValueMap.isEmpty()) {
            return;
        }

        Map<String, String> orderUpdateMap = new HashMap<>();
        salesforceOrderCustomFieldNames.forEach(salesforceCustomFieldName -> {
            if (customFieldNameValueMap.containsKey(salesforceCustomFieldName)) {
                orderUpdateMap.put(salesforceCustomFieldName, customFieldNameValueMap.get(salesforceCustomFieldName));
            }
        });

        if (orderUpdateMap.isEmpty()) {
            return;
        }

        LOGGER.info("Salesforce order custom fields: {}", orderUpdateMap);
        try {
            salesforceRestService.updateOne(loginInfo, SubskribeSalesforceOrder.API_NAME, orderUpdateMap, updatedSalesforceOrder.getId());
        } catch (URISyntaxException | IOException e) {
            String message = String.format("Failed to update custom fields for order %s", updatedSalesforceOrder.getOrder());
            LOGGER.warn(message, e);
        }
    }

    private Map<String, String> getCustomFieldsForOrderLine(String subskribeOrderLineId) {
        CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ORDER_ITEM, subskribeOrderLineId);
        List<CustomFieldValue> customFieldValues = customField.getEntries().values().stream().filter(v -> v.getValue() != null).toList();
        return customFieldValues.stream().collect(Collectors.toMap(cfv -> cfv.getName() + "__c", CustomFieldValue::getValue));
    }

    private Map<String, String> getCustomFieldsForOrder(String orderId) {
        CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ORDER, orderId);
        List<CustomFieldValue> customFieldValues = customField.getEntries().values().stream().filter(v -> v.getValue() != null).toList();
        return customFieldValues.stream().collect(Collectors.toMap(cfv -> cfv.getName() + "__c", CustomFieldValue::getValue));
    }

    private void replaceOrderARR(Order order, Account account, SalesforceLoginInfo loginInfo, String opportunityId)
        throws URISyntaxException, IOException {
        List<SubskribeSalesforceArrTrend> arrTrends = salesforceArrTrendService.getSortedSalesforceArrTrendObjectsForOrder(
            account,
            order,
            opportunityId
        );

        List<String> arrIds = new ArrayList<>();
        List<String> currentArrIds = salesforceArrTrendService
            .getSortedArrTrendsForOpportunityFromSalesforce(opportunityId)
            .map(SubskribeSalesforceArrTrend::getId)
            .toList();
        List<String> orphanedArrIds = salesforceArrTrendService.getOrphanedArrTrendsFromSalesforce().map(SubskribeSalesforceArrTrend::getId).toList();
        arrIds.addAll(currentArrIds);
        arrIds.addAll(orphanedArrIds);

        salesforceRestService.deleteMany(loginInfo, arrIds);
        LOGGER.info("Syncing Salesforce order ARR trends for opportunity {}: {}", opportunityId, arrTrends);
        salesforceRestService.createMany(loginInfo, SubskribeSalesforceArrTrend.class, arrTrends);
        List<SubskribeSalesforceArrTrend> updatedArrTrends = salesforceArrTrendService
            .getSortedArrTrendsForOpportunityFromSalesforce(opportunityId)
            .toList();
        LOGGER.info("Updated Salesforce order ARR trends for opportunity {}: {}", opportunityId, updatedArrTrends);
    }

    // Todo - use account id based lock key, as used in pushOrder and resetOpportunity
    public void pushCompositeOrder(String compositeOrderId) throws IOException, URISyntaxException {
        List<String> errors = new ArrayList<>();
        SalesforcePackage salesforcePackage = getSalesforcePackage();
        SalesforceLoginInfo loginInfo = getLoginInfoWithMultiCurrency();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        Optional<SalesforceOpportunity> salesforceOpportunity = salesforceGetService.getOpportunityFromSalesforce(
            compositeOrder.getCompositeOrderId(),
            compositeOrder.getCrmOpportunityId(),
            errors
        );
        if (salesforceOpportunity.isPresent()) {
            switch (compositeOrder.getType()) {
                case UPSELL_AND_EARLY_RENEWAL -> pushUpsellAndEarlyRenewal(compositeOrder, salesforcePackage, loginInfo, errors, dslContext);
                case CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE -> pushCancelAndRestructure(
                    compositeOrder,
                    salesforcePackage,
                    loginInfo,
                    errors,
                    dslContext
                );
            }
        }
        throwIfSyncErrorsAreFound(errors);
    }

    private void pushUpsellAndEarlyRenewal(
        CompositeOrder compositeOrder,
        SalesforcePackage salesforcePackage,
        SalesforceLoginInfo loginInfo,
        List<String> errors,
        DSLContext dslContext
    ) {
        dslContext.transaction(configuration -> {
            String lockKey = String.format(OPPORTUNITY_SYNC_KEY_FORMAT, compositeOrder.getCrmOpportunityId());
            waitForLockOrThrow(configuration, lockKey, LOCK_WAIT_MS);
            syncSwitchedOpportunitiesForCompositeOrder(loginInfo, compositeOrder, salesforcePackage);
            Optional<SubskribeSalesforceOrder> primarySalesforceOrder = replaceOrdersForOpportunity(
                loginInfo,
                compositeOrder.getCrmOpportunityId(),
                salesforcePackage,
                errors
            );
            primarySalesforceOrder.ifPresent(o -> updateOpportunityForPrimarySalesforceOrder(loginInfo, salesforcePackage, o, errors));
        });
    }

    private void pushCancelAndRestructure(
        CompositeOrder compositeOrder,
        SalesforcePackage salesforcePackage,
        SalesforceLoginInfo loginInfo,
        List<String> errors,
        DSLContext dslContext
    ) {
        dslContext.transaction(configuration -> {
            String lockKey = String.format(OPPORTUNITY_SYNC_KEY_FORMAT, compositeOrder.getCrmOpportunityId());
            waitForLockOrThrow(configuration, lockKey, LOCK_WAIT_MS);
            syncSwitchedOpportunitiesForCompositeOrder(loginInfo, compositeOrder, salesforcePackage);
            Optional<SubskribeSalesforceOrder> primarySalesforceOrder = replaceOrdersForOpportunity(
                loginInfo,
                compositeOrder.getCrmOpportunityId(),
                salesforcePackage,
                errors
            );
            primarySalesforceOrder.ifPresent(o -> updateOpportunityForPrimarySalesforceOrder(loginInfo, salesforcePackage, o, errors));
        });
    }

    public void pushSubscription(String subscriptionId) throws IOException, URISyntaxException {
        List<String> errors = new ArrayList<>();
        SalesforcePackage salesforcePackage = getSalesforcePackage();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        SalesforceLoginInfo loginInfo = getLoginInfoWithMultiCurrency();
        dslContext.transaction(configuration -> {
            String lockKey = String.format(SUBSCRIPTION_LOCK_KEY_FORMAT, subscriptionId);
            waitForLockOrThrow(configuration, lockKey, LOCK_WAIT_MS);
            Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
            pushExpectedARR(loginInfo, subscription);

            Account account = accountGetService.getAccount(subscription.getAccountId());
            Optional<SalesforceAccount> salesforceAccount = salesforceGetService.getAccountFromSalesforce(account, errors);
            if (salesforceAccount.isEmpty()) {
                return;
            }
            SubskribeSalesforceSubscription salesforceSubscription = salesforceSubscriptionService.getSubscriptionObjectForOrder(
                account,
                subscription,
                siteUrl
            );
            if (salesforcePackage.compareTo(VERSION_1_66) < 0) {
                salesforceSubscription.setSubscriptionStartDate(null);
                salesforceSubscription.setSubscriptionEndDate(null);
            }
            if (salesforcePackage.compareTo(VERSION_1_67) < 0) {
                salesforceSubscription.setStatus(null);
            }
            if (salesforcePackage.compareTo(VERSION_1_86) < 0) {
                salesforceSubscription.setSubscriptionCancellationDate(null);
            }
            Optional<SubskribeSalesforceSubscription> existingSubscription = salesforceGetService.getSubscriptionFromSalesforce(
                subscription.getSubscriptionId()
            );
            SubskribeSalesforceSubscription upsertedSubscription;
            if (existingSubscription.isPresent()) {
                upsertedSubscription = salesforceRestService.updateAndGet(
                    loginInfo,
                    SubskribeSalesforceSubscription.class,
                    salesforceSubscription,
                    existingSubscription.get().getId()
                );
            } else {
                upsertedSubscription = salesforceRestService.createAndGet(loginInfo, SubskribeSalesforceSubscription.class, salesforceSubscription);
            }
            syncSubscriptionCustomFields(loginInfo, upsertedSubscription, errors);
            LOGGER.info(
                "Updated Salesforce subscription for account {} with CRM ID {}: {}",
                account.getAccountId(),
                account.getCrmId(),
                upsertedSubscription
            );
            pushSubscriptionLineItems(loginInfo, subscription, upsertedSubscription, salesforcePackage);
        });

        // Push Account ARR Sync after syncing a subscription
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        pushAccountARR(subscription.getAccountId());

        throwIfSyncErrorsAreFound(errors);
    }

    private void syncSubscriptionCustomFields(
        SalesforceLoginInfo loginInfo,
        SubskribeSalesforceSubscription salesforceSubscription,
        List<String> errors
    ) {
        if (!featureService.isEnabled(Feature.CRM_FIELD_MAPPING)) {
            return;
        }
        List<CrmFieldMapping> outboundCrmFieldMappings = crmFieldMappingService
            .getCrmFieldMappings(CrmType.SALESFORCE, CrmObjectType.SUBSCRIPTION)
            .stream()
            .filter(m -> CrmSyncDirection.OUTBOUND == m.getDirection())
            .toList();

        // No custom field mappings found
        if (CollectionUtils.isEmpty(outboundCrmFieldMappings)) {
            return;
        }

        List<SalesforceField> salesforceFields = salesforceGetService.getObjectFields(loginInfo, SubskribeSalesforceSubscription.class, errors);
        CustomField customField = customFieldService.getCustomFields(
            CustomFieldParentType.SUBSCRIPTION,
            salesforceSubscription.getSubskribeSubscriptionId()
        );
        List<CustomFieldValue> customFieldsToSync = customField.getEntries().values().stream().toList();

        Map<String, String> customFieldUpdateMap = new HashMap<>();
        for (var crmFieldMapping : outboundCrmFieldMappings) {
            populateCustomFieldUpdateMap(customFieldUpdateMap, customFieldsToSync, crmFieldMapping, salesforceFields, errors);
        }

        if (!customFieldUpdateMap.isEmpty()) {
            try {
                LOGGER.info(
                    "Custom field update map for subscription {}: {}",
                    salesforceSubscription.getSubskribeSubscriptionId(),
                    customFieldUpdateMap
                );
                salesforceRestService.updateOne(
                    loginInfo,
                    SubskribeSalesforceSubscription.API_NAME,
                    customFieldUpdateMap,
                    salesforceSubscription.getId()
                );
            } catch (SalesforceResponseException | URISyntaxException | IOException e) {
                errors.add(
                    String.format(
                        "Failed to update custom fields for Salesforce subscription %s with CRM ID %s",
                        salesforceSubscription.getSubskribeSubscriptionId(),
                        salesforceSubscription.getId()
                    )
                );
            }
        }
    }

    private void pushExpectedARR(SalesforceLoginInfo loginInfo, Subscription subscription) throws URISyntaxException, IOException {
        Optional<SalesforceOpportunity> salesforceOpportunity = salesforceGetService.getOpportunity(subscription.getRenewalOpportunityCrmId());
        if (salesforceOpportunity.isEmpty()) {
            LOGGER.info("Renewal opportunity CRM ID missing for subscription ID: %s", subscription.getRenewalOpportunityCrmId());
            return;
        }
        Metrics metrics = metricsService.getSubscriptionMetrics(subscription, Instant.now());
        Map<String, BigDecimal> opportunityUpdateMap = Map.of(RENEWAL_OPPORTUNITY_ID_FIELD, metrics.getExitArr());
        salesforceRestService.updateOne(
            loginInfo,
            SalesforceOpportunity.OPPORTUNITY_API_NAME,
            opportunityUpdateMap,
            subscription.getRenewalOpportunityCrmId()
        );
    }

    private void pushSubscriptionLineItems(
        SalesforceLoginInfo loginInfo,
        Subscription subscription,
        SubskribeSalesforceSubscription salesforceSubscription,
        SalesforcePackage salesforcePackage
    ) throws URISyntaxException, IOException {
        List<String> currentSubscriptionLineIds = salesforceGetService
            .getSortedSubscriptionLinesFromSalesforce(salesforceSubscription.getId())
            .map(SubskribeSalesforceSubscriptionLine::getId)
            .toList();

        List<SubskribeSalesforceSubscriptionLine> subscriptionLines = salesforceSubscriptionService.getSalesforceLines(
            subscription,
            salesforceSubscription,
            subscription.getTenantId(),
            salesforcePackage
        );
        salesforceRestService.deleteMany(loginInfo, currentSubscriptionLineIds);
        LOGGER.info("Syncing Salesforce subscription lines for subscription {}: {}", subscription.getSubscriptionId(), subscriptionLines);
        salesforceRestService.createMany(loginInfo, SubskribeSalesforceSubscriptionLine.class, subscriptionLines);
        List<SubskribeSalesforceSubscriptionLine> updatedSubscriptionLines = salesforceGetService
            .getSortedSubscriptionLinesFromSalesforce(salesforceSubscription.getId())
            .toList();
        LOGGER.info("Updated Salesforce subscription lines for subscription {}: {}", subscription.getSubscriptionId(), updatedSubscriptionLines);
    }

    private List<SubskribeSalesforceOrderLine> getSortedSalesforceOrderLines(
        Order order,
        List<OrderLineItem> lineItems,
        SubskribeSalesforceOrder subskribeSalesforceOrder,
        String opptyId,
        SalesforcePackage salesforcePackage
    ) {
        return lineItems
            .stream()
            .map(lineItem -> getSubskribeSalesforceOrderLine(order, lineItem, subskribeSalesforceOrder, opptyId, salesforcePackage))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .sorted()
            .collect(Collectors.toList());
    }

    private List<SubskribeSalesforceOrderLine> getSortedSalesforceOrderLinesForCancelAndRestructure(
        Order restructureOrder,
        List<Order> cancelOrders,
        List<OrderLineItem> allLineItemsToAppearInRestructuredOrder,
        SubskribeSalesforceOrder salesforceOrder,
        String opportunityId,
        SalesforcePackage salesforcePackage
    ) {
        return allLineItemsToAppearInRestructuredOrder
            .stream()
            .map(lineItem ->
                getSubskribeSalesforceOrderLineForCancelAndRestructure(
                    restructureOrder,
                    cancelOrders,
                    lineItem,
                    salesforceOrder,
                    opportunityId,
                    salesforcePackage
                )
            )
            .filter(Optional::isPresent)
            .map(Optional::get)
            .sorted()
            .collect(Collectors.toList());
    }

    private Optional<SubskribeSalesforceOrderLine> getSubskribeSalesforceOrderLineForCancelAndRestructure(
        Order restructureOrder,
        List<Order> cancelOrders,
        OrderLineItem orderLineItem,
        SubskribeSalesforceOrder subskribeSalesforceOrder,
        String opptyId,
        SalesforcePackage salesforcePackage
    ) {
        SubskribeSalesforceOrderLine subskribeOrderLine = new SubskribeSalesforceOrderLine();

        var formatter = new SalesforceFormatter(tenantSettingService.getTenantSetting().getDefaultTimeZone());
        Charge charge = productCatalogGetService.getChargeByChargeId(orderLineItem.getChargeId());
        var combinedOrderLineMetrics = cancelAndRestructureOrderDataAggregator.getCancelAndRestructureOrderLineItemMetrics(
            orderLineItem,
            restructureOrder,
            cancelOrders
        );

        subskribeOrderLine.setOpportunity(opptyId);
        subskribeOrderLine.setSubskribeOrderLineId(orderLineItem.getOrderLineId());
        subskribeOrderLine.setArr(combinedOrderLineMetrics.getArr());
        subskribeOrderLine.setEntryArr(combinedOrderLineMetrics.getEntryArr());
        subskribeOrderLine.setExitArr(combinedOrderLineMetrics.getExitArr());
        subskribeOrderLine.setAverageArr(combinedOrderLineMetrics.getAverageArr());
        subskribeOrderLine.setDeltaArr(combinedOrderLineMetrics.getDeltaArr());

        subskribeOrderLine.setAcv(metricsService.calculateOrderLineACV(restructureOrder, orderLineItem));
        subskribeOrderLine.setChargeId(orderLineItem.getChargeId());
        subskribeOrderLine.setChargeName(charge.getName());
        subskribeOrderLine.setCurrency(restructureOrder.getCurrency().getCurrencyCode());
        subskribeOrderLine.setListPrice(orderLineItem.getListUnitPrice());
        subskribeOrderLine.setStartDate(restructureOrder.getStartDate().toString());
        subskribeOrderLine.setEndDate(formatter.endDateFormat(restructureOrder.getEndDate()));
        subskribeOrderLine.setOrderType(restructureOrder.getOrderType().toString());
        subskribeOrderLine.setPlanId(orderLineItem.getPlanId());

        var plan = productCatalogGetService.getPlan(orderLineItem.getPlanId());
        subskribeOrderLine.setProductId(plan.getProductId());

        var product = productCatalogGetService.getProduct(plan.getProductId());
        subskribeOrderLine.setProductName(product.getName());

        subskribeOrderLine.setQuantity(BigDecimal.valueOf(orderLineItem.getQuantity()));
        subskribeOrderLine.setIsRamp(
            orderLineItem.getIsRamp() ? SubskribeSalesforceBoolean.YES.toString() : SubskribeSalesforceBoolean.NO.toString()
        );
        subskribeOrderLine.setSellPrice(orderLineItem.getSellUnitPrice());
        if (restructureOrder.getExternalSubscriptionId() != null) {
            subskribeOrderLine.setSubscriptionId(restructureOrder.getExternalSubscriptionId());
        }

        subskribeOrderLine.setTCV(orderLineItem.getAmount());
        subskribeOrderLine.setTCVCurrency(orderLineItem.getAmount());

        subskribeOrderLine.setPlanName(plan.getName());

        BigDecimal discount = BigDecimal.ZERO;
        if (!Numbers.isZero(orderLineItem.getListAmount())) {
            discount = Numbers.scaledPercent(orderLineItem.getDiscountAmount(), orderLineItem.getListAmount());
        }
        subskribeOrderLine.setDiscountPercent(discount);

        subskribeOrderLine.setLineStartDate(orderLineItem.getEffectiveDate().toString());
        subskribeOrderLine.setLineEndDate(formatter.endDateFormat(orderLineItem.getEndDate()));

        String tenantId = tenantIdProvider.provideTenantIdString();
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        boolean shouldSyncProductCategory = tenantScopedConfig.getSalesforceConfiguration().getShouldSyncProductCategory();
        if ((shouldSyncProductCategory || salesforcePackage.compareTo(VERSION_1_71) >= 0) && product.getProductCategory() != null) {
            subskribeOrderLine.setProductCategory(product.getProductCategory().getName());
        }

        subskribeOrderLine.setSalesforceOrder(subskribeSalesforceOrder.getId());
        subskribeOrderLine.setSalesforceCurrencyCode(restructureOrder.getCurrency().getCurrencyCode());

        if (salesforcePackage.compareTo(VERSION_1_72) >= 0) {
            subskribeOrderLine.setChargeExternalId(charge.getExternalId());
            subskribeOrderLine.setPlanExternalId(plan.getExternalId());
            subskribeOrderLine.setProductExternalId(product.getExternalId());
        }

        if (salesforcePackage.compareTo(VERSION_1_87) >= 0) {
            subskribeOrderLine.setItemCode(charge.getItemCode());
        }

        return Optional.of(subskribeOrderLine);
    }

    private Optional<SubskribeSalesforceOrderLine> getSubskribeSalesforceOrderLine(
        Order order,
        OrderLineItem orderLineItem,
        SubskribeSalesforceOrder subskribeSalesforceOrder,
        String opptyId,
        SalesforcePackage salesforcePackage
    ) {
        if (order.getOrderType() == OrderType.AMENDMENT && orderLineItem.getAction() == ActionType.NONE) {
            return Optional.empty();
        }

        SubskribeSalesforceOrderLine subskribeOrderLine = new SubskribeSalesforceOrderLine();

        var formatter = new SalesforceFormatter(tenantSettingService.getTenantSetting().getDefaultTimeZone());
        var orderLineMetrics = metricsService.getMetricsForOrderLine(orderLineItem.getOrderLineId());
        Charge charge = productCatalogGetService.getChargeByChargeId(orderLineItem.getChargeId());

        subskribeOrderLine.setOpportunity(opptyId);
        subskribeOrderLine.setSubskribeOrderLineId(orderLineItem.getOrderLineId());
        subskribeOrderLine.setArr(metricsService.calculateOrderLineARR(order, orderLineItem));
        subskribeOrderLine.setEntryArr(orderLineMetrics.arr());
        subskribeOrderLine.setExitArr(orderLineMetrics.arr());
        subskribeOrderLine.setAverageArr(orderLineMetrics.arr());
        subskribeOrderLine.setDeltaArr(orderLineMetrics.deltaArr());
        subskribeOrderLine.setAcv(metricsService.calculateOrderLineACV(order, orderLineItem));
        subskribeOrderLine.setChargeId(orderLineItem.getChargeId());
        subskribeOrderLine.setChargeName(charge.getName());
        subskribeOrderLine.setCurrency(order.getCurrency().getCurrencyCode());
        subskribeOrderLine.setListPrice(orderLineItem.getListUnitPrice());
        subskribeOrderLine.setStartDate(order.getStartDate().toString());
        subskribeOrderLine.setEndDate(formatter.endDateFormat(order.getEndDate()));
        subskribeOrderLine.setOrderType(order.getOrderType().toString());
        subskribeOrderLine.setPlanId(orderLineItem.getPlanId());

        var plan = productCatalogGetService.getPlan(orderLineItem.getPlanId());
        subskribeOrderLine.setProductId(plan.getProductId());

        var product = productCatalogGetService.getProduct(plan.getProductId());
        subskribeOrderLine.setProductName(product.getName());

        subskribeOrderLine.setQuantity(BigDecimal.valueOf(orderLineItem.getQuantity()));
        subskribeOrderLine.setIsRamp(
            orderLineItem.getIsRamp() ? SubskribeSalesforceBoolean.YES.toString() : SubskribeSalesforceBoolean.NO.toString()
        );
        subskribeOrderLine.setSellPrice(orderLineItem.getSellUnitPrice());
        if (order.getExternalSubscriptionId() != null) {
            subskribeOrderLine.setSubscriptionId(order.getExternalSubscriptionId());
        }

        var tcv = metricsService.getOrderLineTCV(orderLineItem.getOrderLineId());
        subskribeOrderLine.setTCV(tcv);
        subskribeOrderLine.setTCVCurrency(tcv);

        subskribeOrderLine.setPlanName(plan.getName());

        BigDecimal discount = BigDecimal.ZERO;
        if (!Numbers.isZero(orderLineItem.getListAmount())) {
            discount = Numbers.scaledPercent(orderLineItem.getDiscountAmount(), orderLineItem.getListAmount());
        }
        subskribeOrderLine.setDiscountPercent(discount);

        subskribeOrderLine.setLineStartDate(orderLineItem.getEffectiveDate().toString());
        subskribeOrderLine.setLineEndDate(formatter.endDateFormat(orderLineItem.getEndDate()));

        String tenantId = tenantIdProvider.provideTenantIdString();
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        boolean shouldSyncProductCategory = tenantScopedConfig.getSalesforceConfiguration().getShouldSyncProductCategory();
        if ((shouldSyncProductCategory || salesforcePackage.compareTo(VERSION_1_71) >= 0) && product.getProductCategory() != null) {
            subskribeOrderLine.setProductCategory(product.getProductCategory().getName());
        }

        subskribeOrderLine.setSalesforceOrder(subskribeSalesforceOrder.getId());
        subskribeOrderLine.setSalesforceCurrencyCode(order.getCurrency().getCurrencyCode());

        if (salesforcePackage.compareTo(VERSION_1_72) >= 0) {
            subskribeOrderLine.setChargeExternalId(charge.getExternalId());
            subskribeOrderLine.setPlanExternalId(plan.getExternalId());
            subskribeOrderLine.setProductExternalId(product.getExternalId());
        }

        if (salesforcePackage.compareTo(VERSION_1_87) >= 0) {
            subskribeOrderLine.setItemCode(charge.getItemCode());
        }

        return Optional.of(subskribeOrderLine);
    }

    private SalesforceCreateOpportunityRequest generateSalesforceCreateOpportunityRequest(
        SalesforceTestOpportunityRequest salesforceTestOpportunityRequest,
        TimeZone timeZone
    ) {
        String closeDateString = getDateString(Instant.now().plus(TEST_OPPORTUNITY_CLOSE_DATE_DAYS_FROM_NOW, ChronoUnit.DAYS), timeZone);
        SalesforceCreateOpportunityRequest createOpportunityRequest = new SalesforceCreateOpportunityRequest();
        createOpportunityRequest.setAccountId(accountGetService.getAccount(salesforceTestOpportunityRequest.getAccountId()).getCrmId());
        createOpportunityRequest.setName(TEST_OPPORTUNITY_NAME);
        createOpportunityRequest.setStageName(TEST_OPPORTUNITY_STAGE_NAME);
        createOpportunityRequest.setSubskribe__Subskribe_Order_Type__c(TEST_OPPORTUNITY_SUBSKRIBE_ORDER_TYPE);
        createOpportunityRequest.setCloseDate(closeDateString);
        return createOpportunityRequest;
    }

    private String getDateString(Instant instant, TimeZone timeZone) {
        ZoneId zoneId = timeZone.toZoneId();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN).withZone(zoneId);
        return dateTimeFormatter.format(instant);
    }

    public static URIBuilder getBaseUri(String instanceUrl, String path) {
        try {
            return new URIBuilder(instanceUrl).setPath(path);
        } catch (URISyntaxException e) {
            String message = "Failed to create a salesforce request URL.";
            LOGGER.error(message, e);
            throw new IllegalStateException(message);
        }
    }

    private SalesforcePackage getSalesforcePackage() {
        String queryReturn = salesforceQueryService.callSalesforceWithQuery(SELECT_PACKAGES_QUERY);
        SalesforceQueryResponse<SalesforcePackage> queryResponse = SALESFORCE_GSON.fromJson(queryReturn, PACKAGE_QUERY_RESPONSE_TYPE);
        return queryResponse.records().stream().filter(record -> SUBSKRIBE_PACKAGE_ID.equals(record.durableId())).findFirst().orElse(null);
    }

    public Optional<SalesforcePackage> getSalesforcePackageByTenantId(String tenantId) {
        try {
            SalesforceLoginInfo loginInfo = salesforceLoginService.getLoginInfo(Optional.of(tenantId));
            String queryReturn = salesforceRestService.getQuery(loginInfo, SELECT_PACKAGES_QUERY);
            SalesforceQueryResponse<SalesforcePackage> queryResponse = SALESFORCE_GSON.fromJson(queryReturn, PACKAGE_QUERY_RESPONSE_TYPE);
            return queryResponse.records().stream().filter(record -> SUBSKRIBE_PACKAGE_ID.equals(record.durableId())).findFirst();
        } catch (URISyntaxException | IOException e) {
            return Optional.empty();
        }
    }

    public Stream<SubskribeSalesforceArrTrend> getSortedArrTrendsForAccountFromSalesforce(String accountCrmId) {
        String query = String.format(FIND_ACCOUNT_ARR_TREND_CHILDREN_FORMAT, accountCrmId);
        LOGGER.info("Calling salesforce to get account arr trends for order. Query: {}", query);
        String arrTrendResponse = salesforceQueryService.callSalesforceWithQuery(query);
        SalesforceQueryResponse<SubskribeSalesforceArrTrend> arrTrend = SALESFORCE_GSON.fromJson(arrTrendResponse, ARR_QUERY_RESPONSE_TYPE);
        return arrTrend.records().stream().sorted();
    }

    public List<SubskribeSalesforceOrder> getOrderObjectsFromSalesforce(String opportunityId) {
        String query = String.format(FIND_ORDER_FORMAT, opportunityId);
        LOGGER.info("Calling salesforce to get order. Query: {}", query);
        String orderResponse = salesforceQueryService.callSalesforceWithQuery(query);
        SalesforceQueryResponse<SubskribeSalesforceOrder> queryResponse = SALESFORCE_GSON.fromJson(orderResponse, ORDER_QUERY_RESPONSE_TYPE);
        return queryResponse.records();
    }

    public List<SubskribeSalesforceOrder> getOrphanedOrderObjectsFromSalesforce() {
        String query = FIND_ORPHANED_ORDERS_QUERY;
        LOGGER.info("Calling salesforce to get order. Query: {}", query);
        String orderResponse = salesforceQueryService.callSalesforceWithQuery(query);
        SalesforceQueryResponse<SubskribeSalesforceOrder> queryResponse = SALESFORCE_GSON.fromJson(orderResponse, ORDER_QUERY_RESPONSE_TYPE);
        return queryResponse.records();
    }

    public SalesforceQueryResponse<SubskribeSalesforceOrderLine> getOrderLinesForOpportunityFromSalesforce(String opportunityId) {
        String query = String.format(FIND_OPPORTUNITY_ORDER_LINE_CHILDREN_FORMAT, opportunityId);
        LOGGER.info("Calling salesforce to get opportunity order lines for order. Query: {}", query);
        String orderLineResponse = salesforceQueryService.callSalesforceWithQuery(query);
        return SALESFORCE_GSON.fromJson(orderLineResponse, ORDER_LINE_QUERY_RESPONSE_TYPE);
    }

    public List<SubskribeSalesforceOrderLine> getOrderLinesForOrdersFromSalesforce(List<String> orderIds) {
        if (orderIds.isEmpty()) {
            return List.of();
        }
        String idList = "'" + String.join("','", orderIds) + "'";
        String query = String.format(FIND_ORDER_LINE_CHILDREN_FORMAT, idList);
        LOGGER.info("Calling salesforce to get order lines for orders. Query: {}", query);
        String orderLineResponse = salesforceQueryService.callSalesforceWithQuery(query);
        SalesforceQueryResponse<SubskribeSalesforceOrderLine> queryResponse = SALESFORCE_GSON.fromJson(
            orderLineResponse,
            ORDER_LINE_QUERY_RESPONSE_TYPE
        );
        return queryResponse.records();
    }

    public SalesforceQueryResponse<SubskribeSalesforceOrderLine> getOrphanedOrderLinesFromSalesforce() {
        String query = FIND_ORPHANED_ORDER_LINES_QUERY;
        LOGGER.info("Calling salesforce to get orphaned order lines for order. Query: {}", query);
        String orderLineResponse = salesforceQueryService.callSalesforceWithQuery(query);
        return SALESFORCE_GSON.fromJson(orderLineResponse, ORDER_LINE_QUERY_RESPONSE_TYPE);
    }

    public SalesforceCreateOpportunityResponse createTestSalesforceOpportunity(SalesforceTestOpportunityRequest testOpportunityRequest) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        SalesforceCreateOpportunityRequest createOpportunityRequest = generateSalesforceCreateOpportunityRequest(testOpportunityRequest, timeZone);
        String path = String.format(OPPORTUNITY_OBJECT_API_PATH_FORMAT, salesforceConfiguration.getApiVersion());
        String body = SALESFORCE_GSON.toJson(createOpportunityRequest);
        SalesforceLoginInfo loginInfo = salesforceLoginService.getLoginInfo();
        URIBuilder uriBuilder = getBaseUri(loginInfo.getInstanceUrl(), path);
        String responseBody = SalesforceRequestService.callSalesforceWithPostRequest(uriBuilder.toString(), loginInfo.getAccessToken(), body);
        SalesforceCreateOpportunityResponse response = new Gson().fromJson(responseBody, SalesforceCreateOpportunityResponse.class);
        if (!response.getSuccess()) {
            throw new ServiceFailureException("Failed to create an opportunity. Response body: " + responseBody);
        }
        return response;
    }

    public void deleteTestSalesforceOpportunity(String opportunityId) {
        String path = String.format(OPPORTUNITY_OBJECT_WITH_ID_API_PATH_FORMAT, salesforceConfiguration.getApiVersion(), opportunityId);
        SalesforceLoginInfo loginInfo = salesforceLoginService.getLoginInfo();
        URIBuilder uriBuilder = getBaseUri(loginInfo.getInstanceUrl(), path);
        SalesforceRequestService.callSalesforceWithDeleteRequest(uriBuilder.toString(), loginInfo.getAccessToken());
    }

    public String getTestSalesforceOpportunityById(String opportunityId) {
        String path = String.format(OPPORTUNITY_OBJECT_WITH_ID_API_PATH_FORMAT, salesforceConfiguration.getApiVersion(), opportunityId);
        SalesforceLoginInfo loginInfo = salesforceLoginService.getLoginInfo();
        URIBuilder uriBuilder = getBaseUri(loginInfo.getInstanceUrl(), path);
        return SalesforceRequestService.callSalesforceWithGetRequest(uriBuilder.toString(), loginInfo.getAccessToken(), true);
    }

    public AccountJson prepareAccount(String accountCrmId) {
        try {
            SalesforceLoginInfo loginInfo = getLoginInfoWithMultiCurrency();
            SalesforceAccountJson salesforceAccount = salesforceRestService.getOne(loginInfo, SalesforceAccountJson.class, accountCrmId);
            AccountJson accountJson = accountMapper.toSubskribeAccount(salesforceAccount);
            if (Objects.nonNull(salesforceAccount.getBillingAddress())) {
                var billingAddress = salesforceAccount.getBillingAddress();
                AccountAddressJson accountAddressJson = contactMapper.toSubskribeAddress(billingAddress);
                accountJson.setAddress(accountAddressJson);
            }
            return accountJson;
        } catch (SalesforceResponseException | URISyntaxException | IOException e) {
            throw new IllegalStateException(String.format("Unable to fetch Salesforce account with ID: %s", accountCrmId));
        }
    }

    public void syncCustomFieldsForAccount(String accountCrmId, String accountId) {
        List<String> errors = new ArrayList<>();
        SalesforceLoginInfo loginInfo = salesforceLoginService.getLoginInfo();
        List<CrmFieldMapping> inboundCrmFieldMappings = crmFieldMappingService
            .getCrmFieldMappings(CrmType.SALESFORCE, CrmObjectType.ACCOUNT)
            .stream()
            .filter(m -> CrmSyncDirection.INBOUND == m.getDirection())
            .toList();

        // No custom field mappings found
        if (CollectionUtils.isEmpty(inboundCrmFieldMappings)) {
            return;
        }
        // Only get the fields that exist on Salesforce Opportunity
        List<String> salesforceFieldNames = salesforceGetService
            .getObjectFields(loginInfo, SalesforceAccountJson.class, errors)
            .stream()
            .map(SalesforceField::getName)
            .toList();
        List<String> validInboundMappings = inboundCrmFieldMappings
            .stream()
            .map(CrmFieldMapping::getCrmFieldName)
            .filter(salesforceFieldNames::contains)
            .toList();
        String objectType = salesforceRestService.getObjectType(SalesforceAccountJson.class);

        // Collect CRM custom field values in a map
        Map<String, Object> salesforceGetRecordResponse = salesforceRestService.getSalesforceRecordFields(
            loginInfo,
            objectType,
            accountCrmId,
            validInboundMappings
        );
        if (salesforceGetRecordResponse == null || MapUtils.isEmpty(salesforceGetRecordResponse)) {
            LOGGER.info("Failed to find Salesforce record type: {}, ID: {}", objectType, accountCrmId);
            return;
        }

        Map<String, String> customFieldValues = new HashMap<>();
        for (var crmFieldMapping : inboundCrmFieldMappings) {
            if (salesforceGetRecordResponse.containsKey(crmFieldMapping.getCrmFieldName())) {
                String customFieldName = crmFieldMapping.getSubskribeFieldName();
                var sourceValue = salesforceGetRecordResponse.get(crmFieldMapping.getCrmFieldName());
                String customFieldValue = sourceValue == null ? StringUtils.EMPTY : String.valueOf(sourceValue);
                customFieldValues.put(customFieldName, customFieldValue);
            }
        }

        CustomField accountCustomFields = customFieldService.getCustomFields(CustomFieldParentType.ACCOUNT, accountId);

        Map<String, CustomFieldValue> updateAccountCustomFieldEntries = new HashMap<>();
        accountCustomFields
            .getEntries()
            .forEach((customFieldId, customFieldValue) -> {
                String customFieldName = customFieldValue.getName();
                String sourceValue = customFieldValues.get(customFieldName);

                // Check that CRM custom field value is not null and Subskribe has the custom field
                if (sourceValue != null) {
                    String updatedValue = sourceValue;
                    List<String> updatedSelections =
                        switch (customFieldValue.getType()) {
                            case STRING -> List.of();
                            case PICKLIST -> List.of(sourceValue);
                            case MULTISELECT_PICKLIST -> Arrays.stream(sourceValue.split(";")).map(String::trim).toList();
                        };

                    // Check that the selections are a subset of the available custom field options
                    // If the selections are not a subset, then use the original custom field value and selections
                    if (!new HashSet<>(customFieldValue.getOptions()).containsAll(updatedSelections)) {
                        updatedValue = customFieldValue.getValue();
                        updatedSelections = customFieldValue.getSelections();
                    }
                    CustomFieldValue updatedAccountCustomField = new CustomFieldValue(
                        customFieldValue.getType(),
                        customFieldValue.getName(),
                        customFieldValue.getLabel(),
                        updatedValue,
                        updatedSelections,
                        customFieldValue.getOptions(),
                        customFieldValue.isRequired(),
                        customFieldValue.getSource(),
                        customFieldValue.getDefaultValue()
                    );
                    updateAccountCustomFieldEntries.put(customFieldId, updatedAccountCustomField);
                } else {
                    updateAccountCustomFieldEntries.put(customFieldId, customFieldValue);
                }
            });

        customFieldService.setCustomFieldsBySystem(CustomFieldParentType.ACCOUNT, accountId, new CustomField(updateAccountCustomFieldEntries));
    }

    public void pushSubscriptionDeletion(String subscriptionId, String accountId) throws IOException, URISyntaxException {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        SalesforceLoginInfo loginInfo = getLoginInfoWithMultiCurrency();
        dslContext.transaction(configuration -> {
            String lockKey = String.format(SUBSCRIPTION_LOCK_KEY_FORMAT, subscriptionId);
            waitForLockOrThrow(configuration, lockKey, LOCK_WAIT_MS);
            Optional<SubskribeSalesforceSubscription> existingSubscription = salesforceGetService.getSubscriptionFromSalesforce(subscriptionId);
            if (existingSubscription.isPresent()) {
                String objectId = existingSubscription.get().getId();
                //delete all subscription line items
                deleteSubscriptionLineItems(loginInfo, objectId);
                //delete subscription
                salesforceRestService.deleteMany(loginInfo, List.of(objectId));
            }
        });
        //After subscription deletion, the account overall ARR will change, trigger Account ARR Sync
        pushAccountARR(accountId);
    }

    private void deleteSubscriptionLineItems(SalesforceLoginInfo loginInfo, String objectId) throws URISyntaxException, IOException {
        List<String> currentSubscriptionLineIds = salesforceGetService
            .getSortedSubscriptionLinesFromSalesforce(objectId)
            .map(SubskribeSalesforceSubscriptionLine::getId)
            .toList();
        if (CollectionUtils.isEmpty(currentSubscriptionLineIds)) {
            return;
        }
        salesforceRestService.deleteMany(loginInfo, currentSubscriptionLineIds);
    }

    public Set<String> getSalesforceCustomFieldNames(String query) {
        SalesforceQueryResponse<SalesforceCustomField> customFieldMetadata = getSalesforceCustomFieldMetadata(query);
        Set<String> salesforceCustomFieldNames = customFieldMetadata
            .records()
            .stream()
            .map(SalesforceCustomField::getQualifiedApiName)
            .collect(Collectors.toSet());
        LOGGER.info("Fetched Salesforce custom field names: {}", salesforceCustomFieldNames);
        return salesforceCustomFieldNames;
    }

    private SalesforceQueryResponse<SalesforceCustomField> getSalesforceCustomFieldMetadata(String query) {
        LOGGER.info("Calling salesforce to get custom field metadata. Query: {}", query);
        String customFieldsResponse = salesforceQueryService.callSalesforceWithQuery(query);
        return SALESFORCE_GSON.fromJson(customFieldsResponse, CUSTOM_FIELD_QUERY_RESPONSE_TYPE);
    }

    public void pushTransactionalArrMetrics(String orderId) throws IOException, URISyntaxException {
        List<String> errors = new ArrayList<>();

        Order orderForLockKey = orderGetService.getOrderByOrderId(orderId);
        String crmOpportunityId = orderForLockKey.getSfdcOpportunityId();

        // This order is part of a composite order. Use the composite order opportunity ID.
        if (StringUtils.isNotBlank(orderForLockKey.getCompositeOrderId())) {
            CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(orderForLockKey.getCompositeOrderId());
            crmOpportunityId = compositeOrder.getCrmOpportunityId();
        }

        pushTransactionalArrMetrics(orderId, crmOpportunityId, errors);
        throwIfSyncErrorsAreFound(errors);
    }

    public void pushTransactionalArrMetrics(String orderId, String crmOpportunityId, List<String> errors) throws IOException, URISyntaxException {
        LOGGER.info("Start of Salesforce transactional ARR metrics sync for {}", orderId);
        SalesforcePackage salesforcePackage = getSalesforcePackage();
        if (salesforcePackage.compareTo(VERSION_1_81) < 0) {
            errors.add("Salesforce Transactional ARR metrics requires a managed package version of 1.81 or higher");
            return;
        }

        if (StringUtils.isBlank(crmOpportunityId)) {
            errors.add(String.format("Opportunity ID is required for Transactional ARR metrics sync for %s", orderId));
            return;
        }

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        SalesforceLoginInfo loginInfo = getLoginInfoWithMultiCurrency();
        String lockKey = String.format(TRANSACTIONAL_ARR_METRICS_LOCK_KEY_FORMAT, crmOpportunityId);
        dslContext.transaction(configuration -> {
            waitForLockOrThrow(configuration, lockKey, LOCK_WAIT_MS);

            List<Order> ordersToSync = new ArrayList<>();
            Order order = orderGetService.getOrderByOrderId(orderId);
            if (StringUtils.isNotBlank(order.getCompositeOrderId())) {
                CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(order.getCompositeOrderId());
                ordersToSync.addAll(compositeOrderGetService.getOrdersInCompositeOrder(compositeOrder.getCompositeOrderId()));
            } else {
                ordersToSync.add(order);
            }

            Account account = accountGetService.getAccount(order.getAccountId());
            Optional<SalesforceOpportunity> salesforceOpportunity = salesforceGetService.getOpportunityFromSalesforce(
                order.getOrderId(),
                crmOpportunityId,
                errors
            );
            if (salesforceOpportunity.isPresent()) {
                // Archive current transactional ARR objects
                List<SubskribeSalesforceTransactionalArrMetrics> currentTransactionalArrObjects =
                    salesforceGetService.getTransactionalArrMetricsForOpportunityFromSalesforce(crmOpportunityId);
                List<String> currentTransactionalArrIds = currentTransactionalArrObjects
                    .stream()
                    .map(SubskribeSalesforceTransactionalArrMetrics::getId)
                    .toList();
                salesforceRestService.deleteMany(loginInfo, currentTransactionalArrIds);

                String tenantId = tenantIdProvider.provideTenantIdString();
                List<TransactionalReportingArrMetrics> transactionalReportingArrMetrics = new ArrayList<>();
                ordersToSync.forEach(o ->
                    transactionalReportingArrMetrics.addAll(transactionalMetricsGetService.getOrderTransactionalArrMetrics(tenantId, o.getOrderId()))
                );

                List<SubskribeSalesforceTransactionalArrMetrics> salesforceTransactionalArr = prepareTransactionalArrMetricsForOrders(
                    crmOpportunityId,
                    account.getCrmId(),
                    transactionalReportingArrMetrics,
                    salesforcePackage
                );
                LOGGER.info(
                    "Syncing transactional ARR metrics for account {} opportunity {}: {}",
                    account.getAccountId(),
                    crmOpportunityId,
                    salesforceTransactionalArr
                );
                salesforceRestService.createMany(loginInfo, SubskribeSalesforceTransactionalArrMetrics.class, salesforceTransactionalArr);
                List<SubskribeSalesforceTransactionalArrMetrics> updatedTransactionalArrObjects =
                    salesforceGetService.getTransactionalArrMetricsForOpportunityFromSalesforce(crmOpportunityId);
                LOGGER.info(
                    "Updated transactional ARR metrics for account {} opportunity {}: {}",
                    account.getAccountId(),
                    crmOpportunityId,
                    updatedTransactionalArrObjects
                );
            }
        });
        LOGGER.info("End of Salesforce transactional ARR metrics sync for {}", orderId);
    }

    SubskribeSalesforceTransactionalArrMetrics prepareTransactionalArrMetricsForOrder(
        String crmOpportunityId,
        String accountCrmId,
        TransactionalReportingArrMetrics transactionalReportingArrMetrics,
        SalesforcePackage salesforcePackage
    ) {
        SalesforceFormatter formatter = new SalesforceFormatter(tenantSettingService.getTenantSetting().getDefaultTimeZone());

        SubskribeSalesforceTransactionalArrMetrics salesforceTransactionalArr = new SubskribeSalesforceTransactionalArrMetrics();
        salesforceTransactionalArr.setOpportunity(crmOpportunityId);
        if (salesforcePackage.compareTo(VERSION_1_83) >= 0) {
            salesforceTransactionalArr.setOrderLineItemId(transactionalReportingArrMetrics.getOrderLineItemId());
        }
        salesforceTransactionalArr.setOrderId(transactionalReportingArrMetrics.getOrderId());
        salesforceTransactionalArr.setPlanId(transactionalReportingArrMetrics.getPlanId());
        salesforceTransactionalArr.setChargeId(transactionalReportingArrMetrics.getChargeId());
        salesforceTransactionalArr.setAccountId(transactionalReportingArrMetrics.getAccountId());
        salesforceTransactionalArr.setSubscriptionId(transactionalReportingArrMetrics.getSubscriptionId());
        salesforceTransactionalArr.setGeneratedBy(transactionalReportingArrMetrics.getGeneratedBy().name());
        salesforceTransactionalArr.setChargeType(transactionalReportingArrMetrics.getChargeType());
        salesforceTransactionalArr.setEffectiveDate(formatter.dateFormat(transactionalReportingArrMetrics.getEffectiveDate()));
        salesforceTransactionalArr.setCategory(transactionalReportingArrMetrics.getCategory().name());
        salesforceTransactionalArr.setAmount(transactionalReportingArrMetrics.getAmount());
        salesforceTransactionalArr.setAsOf(formatter.dateFormat(transactionalReportingArrMetrics.getAsOf()));
        salesforceTransactionalArr.setAccount(SalesforceIdConverter.convert15To18(accountCrmId));

        return salesforceTransactionalArr;
    }

    List<SubskribeSalesforceTransactionalArrMetrics> prepareTransactionalArrMetricsForOrders(
        String crmOpportunityId,
        String accountCrmId,
        List<TransactionalReportingArrMetrics> transactionalReportingArrMetrics,
        SalesforcePackage salesforcePackage
    ) {
        return transactionalReportingArrMetrics
            .stream()
            .map(t -> prepareTransactionalArrMetricsForOrder(crmOpportunityId, accountCrmId, t, salesforcePackage))
            .toList();
    }

    private void replaceRateCardPriceAttributes(
        Order order,
        List<OrderLineItem> lineItems,
        SalesforceLoginInfo loginInfo,
        SalesforcePackage salesforcePackage
    ) {
        if (!featureService.isEnabled(Feature.CRM_RATE_CARD) || salesforcePackage.compareTo(VERSION_1_86) < 0) {
            return;
        }

        List<SubskribeSalesforceOrderLine> salesforceOrderLines = getOrderLinesFromSalesforce(lineItems).records().stream().toList();
        List<SubskribeSalesforceRateCard> rateCards = new ArrayList<>();
        for (var lineItem : lineItems) {
            Optional<SubskribeSalesforceOrderLine> match = salesforceOrderLines
                .stream()
                .filter(salesforceOrderLine -> StringUtils.equals(salesforceOrderLine.getSubskribeOrderLineId(), lineItem.getOrderLineId()))
                .findAny();

            List<AttributeReference> attributeReferences = lineItem.getAttributeReferences();

            if (match.isEmpty() || CollectionUtils.isEmpty(attributeReferences)) {
                continue;
            }

            for (var attributeReference : attributeReferences) {
                Optional<PriceAttribute> priceAttributeOptional = rateCardService.getPriceAttributeById(
                    attributeReference.getAttributeDefinitionId()
                );
                if (priceAttributeOptional.isEmpty()) {
                    continue;
                }

                rateCards.add(
                    prepareRateCardPriceAttributesForOrderLine(order, lineItem, match.get(), attributeReference, priceAttributeOptional.get())
                );
            }
        }
        replaceRateCardPriceAttributes(rateCards, order, loginInfo);
    }

    private SubskribeSalesforceRateCard prepareRateCardPriceAttributesForOrderLine(
        Order order,
        OrderLineItem orderLineItem,
        SubskribeSalesforceOrderLine salesforceOrderLine,
        AttributeReference attributeReference,
        PriceAttribute priceAttribute
    ) {
        SubskribeSalesforceRateCard salesforceRateCard = new SubskribeSalesforceRateCard();
        Charge charge = productCatalogGetService.getChargeByChargeId(orderLineItem.getChargeId());

        salesforceRateCard.setPriceAttributeId(attributeReference.getAttributeDefinitionId());
        salesforceRateCard.setPriceAttributeName(priceAttribute.getName());
        salesforceRateCard.setPriceAttributeValue(attributeReference.getAttributeValue());
        salesforceRateCard.setRateCardId(charge.getRateCardId());
        salesforceRateCard.setOrderLine(salesforceOrderLine.getId());
        salesforceRateCard.setOrderLineId(orderLineItem.getOrderLineId());
        salesforceRateCard.setOrderId(order.getOrderId());

        return salesforceRateCard;
    }

    private void replaceRateCardPriceAttributes(List<SubskribeSalesforceRateCard> rateCards, Order order, SalesforceLoginInfo loginInfo) {
        try {
            List<String> orphanedRateCardIds = getOrphanedRateCardsFromSalesforce()
                .records()
                .stream()
                .map(SubskribeSalesforceRateCard::getId)
                .toList();
            salesforceRestService.deleteMany(loginInfo, orphanedRateCardIds);
            salesforceRestService.createMany(loginInfo, SubskribeSalesforceRateCard.class, rateCards);
            LOGGER.info(
                "Updated Salesforce rate cards for order {} with opportunity id {}: {}",
                order.getOrderId(),
                order.getSfdcOpportunityId(),
                rateCards
            );
        } catch (URISyntaxException | IOException e) {
            LOGGER.info("Failed to push rate card price attributes to Salesforce.", e);
        }
    }

    public SalesforceQueryResponse<SubskribeSalesforceRateCard> getOrphanedRateCardsFromSalesforce() {
        String query = FIND_ORPHANED_RATE_CARDS_QUERY;
        LOGGER.info("Calling salesforce to get orphaned rate cards. Query: {}", query);
        String orderLineResponse = salesforceQueryService.callSalesforceWithQuery(query);
        return SALESFORCE_GSON.fromJson(orderLineResponse, RATE_CARD_QUERY_RESPONSE_TYPE);
    }

    public SalesforceQueryResponse<SubskribeSalesforceOrderLine> getOrderLinesFromSalesforce(List<OrderLineItem> lineItems) {
        String idList = "'" + String.join("','", lineItems.stream().map(OrderLineItem::getOrderLineId).toList()) + "'";
        String query = String.format(FIND_ORDER_LINES_QUERY_FORMAT, idList);
        LOGGER.info("Calling salesforce to get order lines. Query: {}", query);
        String orderLineResponse = salesforceQueryService.callSalesforceWithQuery(query);
        return SALESFORCE_GSON.fromJson(orderLineResponse, ORDER_LINE_QUERY_RESPONSE_TYPE);
    }

    public <T> SalesforceQueryResponse<T> getObjectRecords(Class<T> classOfT) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("Test API call is not allowed outside of local/ci.");
        }

        return SALESFORCE_GSON.fromJson(
            salesforceQueryService.callSalesforceWithQuery(
                String.format(FIND_ALL_RECORDS_QUERY_FORMAT, salesforceRestService.getObjectType(classOfT))
            ),
            new TypeReference<SalesforceQueryResponse<T>>() {}.getType()
        );
    }

    public void resetSalesforceTestEnvironment() {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("Test API call is not allowed outside of local/ci.");
        }

        SalesforceLoginInfo loginInfo = salesforceLoginService.getLoginInfo();

        SalesforceQueryResponse<SubskribeSalesforceRateCard> rateCards = SALESFORCE_GSON.fromJson(
            salesforceQueryService.callSalesforceWithQuery(String.format(FIND_ALL_RECORDS_QUERY_FORMAT, SubskribeSalesforceRateCard.API_NAME)),
            RATE_CARD_QUERY_RESPONSE_TYPE
        );
        SalesforceQueryResponse<SubskribeSalesforceArrTrend> arrTrends = SALESFORCE_GSON.fromJson(
            salesforceQueryService.callSalesforceWithQuery(String.format(FIND_ALL_RECORDS_QUERY_FORMAT, SubskribeSalesforceArrTrend.API_NAME)),
            ARR_TREND_QUERY_RESPONSE_TYPE
        );
        SalesforceQueryResponse<SubskribeSalesforceTransactionalArrMetrics> arrTransactionalMetrics = SALESFORCE_GSON.fromJson(
            salesforceQueryService.callSalesforceWithQuery(
                String.format(FIND_ALL_RECORDS_QUERY_FORMAT, SubskribeSalesforceTransactionalArrMetrics.API_NAME)
            ),
            ARR_TRANSACTIONAL_METRICS_QUERY_RESPONSE_TYPE
        );
        SalesforceQueryResponse<SubskribeSalesforceOrderLine> orderLines = SALESFORCE_GSON.fromJson(
            salesforceQueryService.callSalesforceWithQuery(String.format(FIND_ALL_RECORDS_QUERY_FORMAT, SubskribeSalesforceOrderLine.API_NAME)),
            ORDER_LINE_QUERY_RESPONSE_TYPE
        );
        SalesforceQueryResponse<SubskribeSalesforceOrder> orders = SALESFORCE_GSON.fromJson(
            salesforceQueryService.callSalesforceWithQuery(String.format(FIND_ALL_RECORDS_QUERY_FORMAT, SubskribeSalesforceOrder.API_NAME)),
            ORDER_QUERY_RESPONSE_TYPE
        );
        SalesforceQueryResponse<SubskribeSalesforceSubscriptionLine> subscriptionLines = SALESFORCE_GSON.fromJson(
            salesforceQueryService.callSalesforceWithQuery(
                String.format(FIND_ALL_RECORDS_QUERY_FORMAT, SubskribeSalesforceSubscriptionLine.API_NAME)
            ),
            SUBSCRIPTION_LINE_QUERY_RESPONSE_TYPE
        );
        SalesforceQueryResponse<SubskribeSalesforceSubscription> subscriptions = SALESFORCE_GSON.fromJson(
            salesforceQueryService.callSalesforceWithQuery(String.format(FIND_ALL_RECORDS_QUERY_FORMAT, SubskribeSalesforceSubscription.API_NAME)),
            SUBSCRIPTION_QUERY_RESPONSE_TYPE
        );

        try {
            salesforceRestService.deleteMany(loginInfo, rateCards.records().stream().map(SubskribeSalesforceRateCard::getId).toList());
            salesforceRestService.deleteMany(loginInfo, arrTrends.records().stream().map(SubskribeSalesforceArrTrend::getId).toList());
            salesforceRestService.deleteMany(
                loginInfo,
                arrTransactionalMetrics.records().stream().map(SubskribeSalesforceTransactionalArrMetrics::getId).toList()
            );
            salesforceRestService.deleteMany(loginInfo, orderLines.records().stream().map(SubskribeSalesforceOrderLine::getId).toList());
            salesforceRestService.deleteMany(loginInfo, orders.records().stream().map(SubskribeSalesforceOrder::getId).toList());
            salesforceRestService.deleteMany(
                loginInfo,
                subscriptionLines.records().stream().map(SubskribeSalesforceSubscriptionLine::getId).toList()
            );
            salesforceRestService.deleteMany(loginInfo, subscriptions.records().stream().map(SubskribeSalesforceSubscription::getId).toList());
        } catch (SalesforceResponseException | URISyntaxException | IOException e) {
            LOGGER.info("Failed to archive Salesforce E2E test records", e);
        }
    }

    public void deleteTestRecords(List<String> recordIds) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("Test API call is not allowed outside of local/ci.");
        }

        SalesforceLoginInfo loginInfo = salesforceLoginService.getLoginInfo();

        try {
            salesforceRestService.deleteMany(loginInfo, recordIds);
        } catch (SalesforceResponseException | URISyntaxException | IOException e) {
            LOGGER.info("Failed to archive Salesforce E2E test records", e);
        }
    }

    public Map<String, List<SalesforceField>> getCustomObjectSchemaList() {
        Map<String, List<SalesforceField>> schemas = new HashMap<>();
        SalesforceLoginInfo loginInfo = salesforceLoginService.getLoginInfo();

        schemas.put(
            SubskribeSalesforceSubscription.API_NAME,
            salesforceGetService.getObjectFields(loginInfo, SubskribeSalesforceSubscription.class, List.of())
        );
        schemas.put(
            SubskribeSalesforceSubscriptionLine.API_NAME,
            salesforceGetService.getObjectFields(loginInfo, SubskribeSalesforceSubscriptionLine.class, List.of())
        );
        schemas.put(SubskribeSalesforceOrder.API_NAME, salesforceGetService.getObjectFields(loginInfo, SubskribeSalesforceOrder.class, List.of()));
        schemas.put(
            SubskribeSalesforceOrderLine.API_NAME,
            salesforceGetService.getObjectFields(loginInfo, SubskribeSalesforceOrderLine.class, List.of())
        );
        schemas.put(
            SubskribeSalesforceArrTrend.API_NAME,
            salesforceGetService.getObjectFields(loginInfo, SubskribeSalesforceArrTrend.class, List.of())
        );
        schemas.put(SalesforceAccountJson.API_NAME, salesforceGetService.getObjectFields(loginInfo, SalesforceAccountJson.class, List.of()));
        schemas.put(
            SubskribeSalesforceTransactionalArrMetrics.API_NAME,
            salesforceGetService.getObjectFields(loginInfo, SubskribeSalesforceTransactionalArrMetrics.class, List.of())
        );
        schemas.put(
            SubskribeSalesforceRateCard.API_NAME,
            salesforceGetService.getObjectFields(loginInfo, SubskribeSalesforceRateCard.class, List.of())
        );
        schemas.put(SalesforceOpportunityFields.API_NAME, salesforceGetService.getObjectFields(loginInfo, SalesforceOpportunity.class, List.of()));

        return schemas;
    }
}
