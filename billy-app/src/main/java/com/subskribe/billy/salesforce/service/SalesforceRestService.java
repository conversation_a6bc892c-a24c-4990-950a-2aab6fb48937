package com.subskribe.billy.salesforce.service;

import static javax.ws.rs.core.Response.Status.CREATED;
import static javax.ws.rs.core.Response.Status.NO_CONTENT;
import static javax.ws.rs.core.Response.Status.OK;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.exception.SalesforceResponseException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.salesforce.model.ImmutableSalesforceAttribute;
import com.subskribe.billy.salesforce.model.SalesforceAttribute;
import com.subskribe.billy.salesforce.model.SalesforceCreateManyPayload;
import com.subskribe.billy.salesforce.model.SalesforceCreatedResponse;
import com.subskribe.billy.salesforce.model.SalesforceErrorMessage;
import com.subskribe.billy.salesforce.model.SalesforceLoginInfo;
import com.subskribe.billy.salesforce.model.SalesforceObject;
import com.subskribe.billy.salesforce.model.SalesforceObjectDescribeResponse;
import com.subskribe.billy.salesforce.model.SalesforceOpportunity;
import com.subskribe.billy.salesforce.model.SubskribeSalesforceOrder;
import com.subskribe.billy.salesforce.model.SubskribeSalesforceRateCard;
import com.subskribe.billy.salesforce.model.SubskribeSalesforceTransactionalArrMetrics;
import com.subskribe.billy.salesforce.model.account.SalesforceAccount;
import com.subskribe.billy.salesforce.model.account.SalesforceAccountJson;
import com.subskribe.billy.salesforce.model.metrics.SubskribeSalesforceArrTrend;
import com.subskribe.billy.salesforce.model.orderlines.SubskribeSalesforceOrderLine;
import com.subskribe.billy.salesforce.model.subscription.SubskribeSalesforceSubscription;
import com.subskribe.billy.salesforce.model.subscription.SubskribeSalesforceSubscriptionLine;
import com.subskribe.billy.shared.enums.HttpRequestType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.validation.Validator;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;

public class SalesforceRestService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesforceRestService.class);
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_TOKEN_FORMAT = "Bearer %s";
    private static final MediaType SALESFORCE_REQUEST_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");
    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
        .connectTimeout(2, TimeUnit.MINUTES)
        .readTimeout(2, TimeUnit.MINUTES)
        .writeTimeout(2, TimeUnit.MINUTES)
        .build();
    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();
    private static final ObjectMapper CUSTOM_JSON_MAPPER = JacksonProvider.defaultMapper();

    private static final String PATH_SEPARATOR = "/";
    private static final String ID_SEPARATOR = ",";
    private static final String API_PREFIX = "/services/data";
    private static final String SOBJECTS_PATH = "/sobjects";
    private static final String COMPOSITE_TREE_PATH = "/composite/tree";
    private static final String DESCRIBE_SOBJECTS_PATH_FORMAT = SOBJECTS_PATH + "/%s/describe";
    private static final String GET_SOBJECT_BY_ID_PATH_FORMAT = SOBJECTS_PATH + "/%s/%s";
    private static final String COMPOSITE_SOBJECTS_PATH = "/composite/sobjects";
    private static final String QUERY_PATH = "/query";
    private static final String IDS_FIELD = "ids";
    private static final String QUERY_PARAMETER_NAME = "q";
    private static final int BULK_CALL_ENTITY_LIMIT = 200;
    private static final Gson GSON = new GsonBuilder().create();

    private final String apiVersion;

    @Inject
    public SalesforceRestService(BillyConfiguration billyConfiguration) {
        apiVersion = billyConfiguration.getSalesforceConfiguration().getApiVersion();
    }

    public Response request(
        SalesforceLoginInfo loginInfo,
        HttpRequestType httpRequestType,
        String path,
        Object payload,
        Map<String, String> queryParams
    ) throws IOException, URISyntaxException {
        String authorizationHeader = String.format(BEARER_TOKEN_FORMAT, loginInfo.getAccessToken());
        URIBuilder uriBuilder = new URIBuilder(loginInfo.getInstanceUrl()).setPath(path);
        Request.Builder requestBuilder = new Request.Builder().addHeader(AUTHORIZATION_HEADER, authorizationHeader);
        RequestBody requestBody = null;
        if (Objects.nonNull(payload)) {
            String payloadText = OBJECT_MAPPER.writeValueAsString(payload);
            requestBody = RequestBody.create(payloadText, SALESFORCE_REQUEST_MEDIA_TYPE);
        }
        for (var param : queryParams.entrySet()) {
            uriBuilder.setParameter(param.getKey(), param.getValue());
        }
        requestBuilder.url(uriBuilder.toString());
        requestBuilder.method(httpRequestType.name(), requestBody);
        LOGGER.info("calling salesforce: {} {}", httpRequestType, path);
        return HTTP_CLIENT.newCall(requestBuilder.build()).execute();
    }

    private String getPath(String path) {
        return API_PREFIX + PATH_SEPARATOR + apiVersion + path;
    }

    public String postRequest(SalesforceLoginInfo loginInfo, String path, Object payload) throws URISyntaxException, IOException {
        try (Response response = request(loginInfo, HttpRequestType.POST, path, payload, Map.of())) {
            String responseBody = getResponseBody(response);
            validateResponse(response, responseBody);
            return responseBody;
        }
    }

    public String patchRequest(SalesforceLoginInfo loginInfo, String path, Object payload) throws URISyntaxException, IOException {
        try (Response response = request(loginInfo, HttpRequestType.PATCH, path, payload, Map.of())) {
            String responseBody = getResponseBody(response);
            validateResponse(response, responseBody);
            return responseBody;
        }
    }

    public String getRequest(SalesforceLoginInfo loginInfo, String path, Map<String, String> params) throws URISyntaxException, IOException {
        try (Response response = request(loginInfo, HttpRequestType.GET, path, null, params)) {
            String responseBody = getResponseBody(response);
            validateResponse(response, responseBody);
            return responseBody;
        }
    }

    public String getQuery(SalesforceLoginInfo loginInfo, String query) throws URISyntaxException, IOException {
        return getRequest(loginInfo, getPath(QUERY_PATH), Map.of(QUERY_PARAMETER_NAME, query));
    }

    public void deleteMany(SalesforceLoginInfo loginInfo, List<String> ids) throws URISyntaxException, IOException {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        String path = getPath(COMPOSITE_SOBJECTS_PATH);

        List<List<String>> batchedIds = Lists.partition(ids, 200);
        for (var batch : batchedIds) {
            LOGGER.info("Deleting Salesforce objects: {}", batch);
            String joinedIds = String.join(ID_SEPARATOR, batch);
            try (Response response = request(loginInfo, HttpRequestType.DELETE, path, null, Map.of(IDS_FIELD, joinedIds))) {
                String responseBody = getResponseBody(response);
                validateResponse(response, responseBody);
            }
        }
    }

    public <T extends SalesforceObject> void createMany(SalesforceLoginInfo loginInfo, Class<T> classOfT, List<T> salesforceObjects)
        throws URISyntaxException, IOException {
        if (CollectionUtils.isEmpty(salesforceObjects)) {
            return;
        }
        String objectType = getObjectType(classOfT);
        salesforceObjects.forEach(salesforceObject -> {
            SalesforceAttribute salesforceAttribute = ImmutableSalesforceAttribute.builder()
                .type(objectType)
                .referenceId(UUID.randomUUID().toString())
                .build();
            salesforceObject.setAttributes(salesforceAttribute);
        });
        salesforceObjects.forEach(salesforceObject -> resetCurrencyIfDisabled(loginInfo, salesforceObject));
        List<List<T>> partitioned = ListUtils.partition(salesforceObjects, BULK_CALL_ENTITY_LIMIT);
        for (List<T> partitionedObjects : partitioned) {
            preparedCreateManyRequest(loginInfo, partitionedObjects, objectType);
        }
    }

    private <T extends SalesforceObject> void preparedCreateManyRequest(SalesforceLoginInfo loginInfo, List<T> salesforceObjects, String objectType)
        throws URISyntaxException, IOException {
        var payload = new SalesforceCreateManyPayload(salesforceObjects);
        String path = getPath(COMPOSITE_TREE_PATH + PATH_SEPARATOR + objectType);
        postRequest(loginInfo, path, payload);
    }

    public <T> SalesforceCreatedResponse createOne(SalesforceLoginInfo loginInfo, Class<T> classOfT, SalesforceObject salesforceObject)
        throws URISyntaxException, IOException {
        if (Objects.isNull(salesforceObject)) {
            return null;
        }
        resetCurrencyIfDisabled(loginInfo, salesforceObject);
        String objectType = getObjectType(classOfT);
        String path = getPath(SOBJECTS_PATH + PATH_SEPARATOR + objectType);
        String responseBody = postRequest(loginInfo, path, salesforceObject);
        return parseResponse(responseBody, SalesforceCreatedResponse.class);
    }

    // Todo - use consistent field name for sfdc currency code and employ an interface
    private void resetCurrencyIfDisabled(SalesforceLoginInfo loginInfo, Object salesforceObject) {
        if (loginInfo.getIsMultiCurrency()) {
            return;
        }
        if (salesforceObject instanceof SalesforceAccount salesforceAccount) {
            salesforceAccount.setCurrencyCode(null);
        }
        if (salesforceObject instanceof SubskribeSalesforceArrTrend salesforceArrTrend) {
            salesforceArrTrend.setSalesforceCurrencyCode(null);
        }
        if (salesforceObject instanceof SubskribeSalesforceOrder salesforceOrder) {
            salesforceOrder.setCurrencyCode(null);
        }
        if (salesforceObject instanceof SubskribeSalesforceOrderLine salesforceOrderLine) {
            salesforceOrderLine.setSalesforceCurrencyCode(null);
        }
        if (salesforceObject instanceof SubskribeSalesforceArrTrend salesforceArrTrend) {
            salesforceArrTrend.setSalesforceCurrencyCode(null);
        }
        if (salesforceObject instanceof SubskribeSalesforceSubscriptionLine salesforceSubscriptionLine) {
            salesforceSubscriptionLine.setCurrencyCode(null);
        }
        if (salesforceObject instanceof SubskribeSalesforceSubscription salesforceSubscription) {
            salesforceSubscription.setCurrencyCode(null);
        }
    }

    public <T> T getOne(SalesforceLoginInfo loginInfo, Class<T> classOfT, String objectId) throws URISyntaxException, IOException {
        Validator.validateStringNotBlank("objectId", objectId);
        String objectType = getObjectType(classOfT);
        String path = getPath(SOBJECTS_PATH + PATH_SEPARATOR + objectType + PATH_SEPARATOR + objectId);
        String responseBody = getRequest(loginInfo, path, Map.of());
        return parseResponse(responseBody, classOfT);
    }

    public <T> T createAndGet(SalesforceLoginInfo loginInfo, Class<T> classOfT, SalesforceObject salesforceObject)
        throws URISyntaxException, IOException {
        SalesforceCreatedResponse createdResponse = createOne(loginInfo, classOfT, salesforceObject);
        return getOne(loginInfo, classOfT, createdResponse.getId());
    }

    public void updateOne(SalesforceLoginInfo loginInfo, String objectType, Object salesforceObject, String objectId)
        throws URISyntaxException, IOException {
        if (Objects.isNull(salesforceObject)) {
            return;
        }
        Validator.validateStringNotBlank(objectId, "id cannot be blank when updating salesforce object");
        resetCurrencyIfDisabled(loginInfo, salesforceObject);
        String path = getPath(SOBJECTS_PATH + PATH_SEPARATOR + objectType + PATH_SEPARATOR + objectId);
        patchRequest(loginInfo, path, salesforceObject);
    }

    public <T> T updateAndGet(SalesforceLoginInfo loginInfo, Class<T> classOfT, SalesforceObject salesforceObject, String objectId)
        throws URISyntaxException, IOException {
        String objectType = getObjectType(classOfT);
        updateOne(loginInfo, objectType, salesforceObject, objectId);
        return getOne(loginInfo, classOfT, objectId);
    }

    public <T> SalesforceObjectDescribeResponse getObjectSchema(SalesforceLoginInfo loginInfo, Class<T> classOfT)
        throws URISyntaxException, IOException {
        String objectType = getObjectType(classOfT);
        String path = getPath(String.format(DESCRIBE_SOBJECTS_PATH_FORMAT, objectType));
        String responseBody = getRequest(loginInfo, path, Map.of());
        return parseResponse(responseBody, SalesforceObjectDescribeResponse.class);
    }

    public Map<String, Object> getSalesforceRecordFields(SalesforceLoginInfo loginInfo, String objectType, String recordId, List<String> fields) {
        try {
            Map<String, String> params = new HashMap<>();
            if (CollectionUtils.isNotEmpty(fields)) {
                params.put("fields", String.join(",", fields));
            }
            String path = getPath(String.format(GET_SOBJECT_BY_ID_PATH_FORMAT, objectType, recordId));
            String responseBody = getRequest(loginInfo, path, params);
            return GSON.fromJson(responseBody, new TypeToken<Map<String, Object>>() {}.getType());
        } catch (SalesforceResponseException | URISyntaxException | IOException e) {
            throw new IllegalStateException(
                String.format("Failed to get Salesforce %s record with ID %s and fields: %s", objectType, recordId, fields)
            );
        }
    }

    public <T> String getObjectType(Class<T> classOfT) {
        if (classOfT.equals(SubskribeSalesforceSubscription.class)) {
            return SubskribeSalesforceSubscription.API_NAME;
        } else if (classOfT.equals(SubskribeSalesforceSubscriptionLine.class)) {
            return SubskribeSalesforceSubscriptionLine.API_NAME;
        } else if (classOfT.equals(SubskribeSalesforceOrder.class)) {
            return SubskribeSalesforceOrder.API_NAME;
        } else if (classOfT.equals(SubskribeSalesforceOrderLine.class)) {
            return SubskribeSalesforceOrderLine.API_NAME;
        } else if (classOfT.equals(SubskribeSalesforceArrTrend.class)) {
            return SubskribeSalesforceArrTrend.API_NAME;
        } else if (classOfT.equals(SalesforceAccountJson.class)) {
            return SalesforceAccountJson.API_NAME;
        } else if (classOfT.equals(SubskribeSalesforceTransactionalArrMetrics.class)) {
            return SubskribeSalesforceTransactionalArrMetrics.API_NAME;
        } else if (classOfT.equals(SubskribeSalesforceRateCard.class)) {
            return SubskribeSalesforceRateCard.API_NAME;
        } else if (classOfT.equals(SalesforceOpportunity.class)) {
            return SalesforceOpportunity.OPPORTUNITY_API_NAME;
        }
        throw new ServiceFailureException("unknown class " + classOfT);
    }

    private static void validateResponse(Response response, String responseBody) {
        if (response.code() == OK.getStatusCode() || response.code() == NO_CONTENT.getStatusCode() || response.code() == CREATED.getStatusCode()) {
            LOGGER.info("Salesforce response code: {}. Response body: {}", response.code(), responseBody);
            return;
        }
        LOGGER.info("Salesforce request failure. Code: {}. Body: {}", response.code(), responseBody);
        throw new SalesforceResponseException("Salesforce API request failed", responseBody);
    }

    public static String getResponseBody(Response response) {
        if (response == null) {
            return StringUtils.EMPTY;
        }
        ResponseBody body = response.body();
        if (body == null) {
            return StringUtils.EMPTY;
        }
        try {
            return body.string();
        } catch (IOException e) {
            return StringUtils.EMPTY;
        }
    }

    public <T> T parseResponse(String responseBody, Class<T> classOfT) throws JsonProcessingException {
        if (StringUtils.isBlank(responseBody)) {
            return null;
        }
        return CUSTOM_JSON_MAPPER.readValue(responseBody, classOfT);
    }

    public static String extractFailureReason(SalesforceResponseException salesforceResponseException) {
        String responseBody = salesforceResponseException.getResponseBody();
        try {
            return getResponseErrorMessage(responseBody);
        } catch (Exception e) {
            LOGGER.warn("unable to extract salesforce response message: {}", responseBody);
        }
        return StringUtils.EMPTY;
    }

    public static String getResponseErrorMessage(String responseBody) {
        try {
            List<SalesforceErrorMessage> salesforceErrorMessages = OBJECT_MAPPER.readValue(responseBody, new TypeReference<>() {});
            return salesforceErrorMessages.stream().findFirst().map(SalesforceErrorMessage::message).orElse(responseBody);
        } catch (JsonProcessingException ex) {
            LOGGER.info("could not parse salesforce error message. response body: {}", responseBody);
            return responseBody;
        }
    }
}
