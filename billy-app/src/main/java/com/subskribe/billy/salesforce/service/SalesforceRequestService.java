package com.subskribe.billy.salesforce.service;

import static javax.ws.rs.core.Response.Status.CREATED;
import static javax.ws.rs.core.Response.Status.NO_CONTENT;
import static javax.ws.rs.core.Response.Status.OK;

import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import dev.failsafe.Failsafe;
import dev.failsafe.RetryPolicy;
import java.io.IOException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class SalesforceRequestService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesforceRequestService.class);

    //follow timeouts set in https://developer.salesforce.com/docs/atlas.en-us.234.0.salesforce_scheduler_developer_guide.meta/salesforce_scheduler_developer_guide/salesforce_app_limits_platform_api.htm plus an extra minute
    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
        .connectTimeout(11, TimeUnit.MINUTES)
        .readTimeout(11, TimeUnit.MINUTES)
        .writeTimeout(11, TimeUnit.MINUTES)
        .build();

    private static final MediaType SALESFORCE_REQUEST_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");

    private static final String EMPTY_JSON = "{}";

    private static final RequestBody EMPTY_REQUEST_BODY = RequestBody.create(EMPTY_JSON, SALESFORCE_REQUEST_MEDIA_TYPE);

    private static final String AUTHORIZATION_HEADER = "Authorization";

    private static final String BEARER_TOKEN_FORMAT = "Bearer %s";

    private static final int NUMBER_OF_RETRIES_FOR_SALESFORCE_REQUEST = 3;

    private static final RetryPolicy<String> RETRY_POLICY = RetryPolicy.<String>builder()
        .handle(SocketException.class)
        .handle(SocketTimeoutException.class)
        .withMaxRetries(NUMBER_OF_RETRIES_FOR_SALESFORCE_REQUEST)
        .build();

    @Inject
    public SalesforceRequestService() {}

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private static void throwIfSalesforceRequestFails(Response response, Optional<String> responseBodyString, boolean logFailures) {
        if (response.code() == OK.getStatusCode() || response.code() == NO_CONTENT.getStatusCode() || response.code() == CREATED.getStatusCode()) {
            return;
        }
        if (logFailures) {
            String message;
            if (responseBodyString.isEmpty()) {
                message = String.format("Got response code: %s when trying to call salesforce. Response: %s", response.code(), response);
            } else {
                message = String.format(
                    "Got response code: %s when trying to call salesforce. Response: %s. Response body: %s",
                    response.code(),
                    response,
                    responseBodyString.get()
                );
            }

            LOGGER.warn(message);
        }

        throw new IllegalStateException(String.format("Salesforce call failed with string: %s", responseBodyString.orElse("")));
    }

    public static String callSalesforceWithPostRequestWithEmptyBody(String requestUrl) {
        LOGGER.debug("Calling salesforce: {}", requestUrl);
        Request request = new Request.Builder().url(requestUrl).post(EMPTY_REQUEST_BODY).build();
        return callSalesforceWithRetries(request);
    }

    //Only disable failure logging for salesforce calls which we expect to fail and we don't want to cause an incident
    public static String callSalesforceWithGetRequest(String requestUrl, String accessToken, boolean logFailures) {
        LOGGER.debug("Calling salesforce: {}", requestUrl);
        String authorizationHeader = String.format(BEARER_TOKEN_FORMAT, accessToken);
        Request request = new Request.Builder().url(requestUrl).addHeader(AUTHORIZATION_HEADER, authorizationHeader).get().build();
        return callSalesforceWithRetries(request, logFailures);
    }

    public static void callSalesforceWithDeleteRequest(String requestUrl, String accessToken) {
        LOGGER.debug("Calling salesforce to delete: {}", requestUrl);
        String authorizationHeader = String.format(BEARER_TOKEN_FORMAT, accessToken);
        Request request = new Request.Builder()
            .url(requestUrl)
            .addHeader(AUTHORIZATION_HEADER, authorizationHeader)
            .delete(EMPTY_REQUEST_BODY)
            .build();
        callSalesforceWithRetries(request);
    }

    public static String callSalesforceWithPostRequest(String requestUrl, String accessToken, String body) {
        return callSalesforceWithPatchOrPostRequest(requestUrl, accessToken, body, false);
    }

    private static String callSalesforceWithPatchOrPostRequest(String requestUrl, String accessToken, String body, boolean isPatch) {
        LOGGER.debug("Calling salesforce: {}, body: {} isPatch: {}", requestUrl, body, isPatch);
        String authorizationHeader = String.format(BEARER_TOKEN_FORMAT, accessToken);
        RequestBody requestBody = RequestBody.create(body, SALESFORCE_REQUEST_MEDIA_TYPE);
        Request.Builder requestBuilder = new Request.Builder().url(requestUrl).addHeader(AUTHORIZATION_HEADER, authorizationHeader);
        requestBuilder = (isPatch) ? requestBuilder.patch(requestBody) : requestBuilder.post(requestBody);
        return callSalesforceWithRetries(requestBuilder.build());
    }

    private static String callSalesforceWithRetries(Request request) {
        return callSalesforceWithRetries(request, true);
    }

    private static String callSalesforceWithRetries(Request request, boolean logFailures) {
        try {
            return Failsafe.with(RETRY_POLICY).get(() -> callSalesforce(request, logFailures));
        } catch (Exception e) {
            throw new IllegalStateException(e); //even if logging is off we still throw to handle upstream
        }
    }

    private static String callSalesforce(Request request, boolean logFailures) throws IOException {
        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            Optional<String> responseBodyString = getOptionalResponseBodyString(response);
            throwIfSalesforceRequestFails(response, responseBodyString, logFailures);
            return getResponseBodyString(responseBodyString, response);
        } catch (IOException e) {
            LOGGER.debug("Failed to get a response from salesforce.", e);
            throw e; //bubble up IO exceptions to retry handler
        }
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private static String getResponseBodyString(Optional<String> optionalResponseString, Response response) {
        return optionalResponseString.orElseThrow(() -> new IllegalStateException("Invalid response: " + response));
    }

    private static Optional<String> getOptionalResponseBodyString(Response response) throws IOException {
        if (response == null) {
            return Optional.empty();
        }

        ResponseBody body = response.body();
        if (body == null) {
            return Optional.empty();
        }

        return Optional.of(body.string());
    }
}
