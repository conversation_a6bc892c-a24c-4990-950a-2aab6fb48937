package com.subskribe.billy.subscription.services;

import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.resources.shared.PaginatedResponse;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.shared.traits.VersionFinder;
import com.subskribe.billy.shared.traits.VersionReference;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.db.SubscriptionDAO;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.model.SubscriptionMetrics;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import javax.inject.Inject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.Record3;

public class SubscriptionGetService implements VersionFinder<String, Subscription> {

    private static final Logger LOGGER = LoggerFactory.getLogger(SubscriptionGetService.class);
    private final SubscriptionDAO subscriptionDAO;
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;

    private final CustomFieldService customFieldService;

    @Inject
    public SubscriptionGetService(
        SubscriptionDAO subscriptionDAO,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        CustomFieldService customFieldService
    ) {
        this.subscriptionDAO = subscriptionDAO;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.customFieldService = customFieldService;
    }

    public boolean contactAssociatedWithSubscription(String contactId) {
        if (StringUtils.isBlank(contactId)) {
            throw new IllegalArgumentException("contactId is required");
        }

        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return subscriptionDAO.contactAssociatedWithSubscription(dslContext, contactId);
    }

    public List<Record3<UUID, String, UUID>> getSubscriptionIdsForMetricsUpdateLocked(DSLContext txnDslContext, String tenantId, int limit) {
        return subscriptionDAO.getSubscriptionIdsForMetricsUpdateLocked(txnDslContext, tenantId, limit);
    }

    public List<Record3<UUID, String, UUID>> getSubscriptionIdsForInvoicePreviewsLocked(DSLContext txnDslContext, String tenantId, int limit) {
        return subscriptionDAO.getSubscriptionIdsForInvoicePreviewsLocked(txnDslContext, tenantId, limit);
    }

    public Optional<Metrics> getStoredMetrics(String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is required");

        String tenantId = tenantIdProvider.provideTenantIdString();
        SubscriptionEntity subscriptionEntity = getSubscriptionEntity(subscriptionId);
        SubscriptionMetrics subscriptionMetrics = getSubscriptionMetrics(tenantId, subscriptionEntity.getId());

        return Optional.ofNullable(subscriptionMetrics)
            .map(SubscriptionMetrics::getMetrics)
            .filter(metrics -> !metrics.isRecompute())
            .map(storedMetrics -> storedMetrics.getMetrics().toMetrics());
    }

    public SubscriptionMetrics getSubscriptionMetrics(String tenantId, UUID globalSubscriptionId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return subscriptionDAO.getSubscriptionMetrics(dslContext, tenantId, globalSubscriptionId);
    }

    public Optional<ProrationConfig> getPinnedProrationConfig(String subscriptionId) {
        if (StringUtils.isBlank(subscriptionId)) {
            return Optional.empty();
        }
        return subscriptionDAO.getPinnedProrationConfig(subscriptionId);
    }

    public Subscription getSubscription(String subscriptionId) {
        if (StringUtils.isBlank(subscriptionId)) {
            LOGGER.info("subscriptionId is blank");
            throw new IllegalArgumentException("subscriptionId is blank");
        }

        return current(subscriptionId);
    }

    public Subscription getSubscription(DSLContext dslContext, String subscriptionId) {
        return fromEntity(
            dslContext,
            subscriptionDAO.getSubscription(dslContext, new VersionReference<>(subscriptionId, VersionReference.ReferenceType.CURRENT))
        );
    }

    public PaginatedResponse<Subscription> getSubscriptions(String filterByAccountId, String pageTokenString, int limit) {
        String pageToken = null;
        if (StringUtils.isNotBlank(pageTokenString)) {
            if (!Base64.isBase64(pageTokenString)) {
                throw new IllegalArgumentException(String.format("Provided base64 page token %s not properly encoded", pageTokenString));
            }
            pageToken = new String(Base64.decodeBase64(pageTokenString), StandardCharsets.UTF_8);
        }
        PageResult<List<String>, String> subscriptionIdsPageResult = subscriptionDAO.getSubscriptions(
            PageRequest.from(pageToken, limit),
            filterByAccountId
        );

        List<Subscription> subscriptions = subscriptionIdsPageResult.getResult().stream().map(this::current).toList();
        String nextPageToken = subscriptionIdsPageResult.getNextPageToken();
        return new PaginatedResponse<>(
            subscriptions,
            subscriptions.size(),
            nextPageToken != null ? Base64.encodeBase64String(nextPageToken.getBytes(StandardCharsets.UTF_8)) : null
        );
    }

    @Override
    public Subscription at(String id, Instant time) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return fromEntity(dslContext, subscriptionDAO.getSubscriptionAt(dslContext, id, time));
    }

    @Override
    public Subscription find(String id, int version) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return fromEntity(
            dslContext,
            subscriptionDAO.getSubscription(dslContext, new VersionReference<>(id, new VersionReference.ReferenceType.Pinned(version)))
        );
    }

    public SubscriptionEntity getSubscriptionEntity(String subscriptionId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return subscriptionDAO.getSubscription(dslContext, new VersionReference<>(subscriptionId, VersionReference.ReferenceType.CURRENT));
    }

    @Override
    public Subscription current(String id) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Subscription subscription = fromEntity(
            dslContext,
            subscriptionDAO.getSubscription(dslContext, new VersionReference<>(id, VersionReference.ReferenceType.CURRENT))
        );

        subscription.setCustomFields(customFieldService.getCustomFields(CustomFieldParentType.SUBSCRIPTION, subscription.getSubscriptionId()));

        if (CollectionUtils.isNotEmpty(subscription.getCharges())) {
            List<String> subscriptionChargeIds = subscription.getCharges().stream().map(SubscriptionCharge::getSubscriptionChargeId).toList();
            Map<String, CustomField> subscriptionChargeCustomFields = customFieldService.getCustomFields(
                CustomFieldParentType.SUBSCRIPTION_ITEM,
                subscriptionChargeIds
            );
            subscription.getCharges().forEach(c -> c.setCustomFields(subscriptionChargeCustomFields.get(c.getSubscriptionChargeId())));
        }

        return subscription;
    }

    @Override
    public Subscription first(String id) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return fromEntity(
            dslContext,
            subscriptionDAO.getSubscription(dslContext, new VersionReference<>(id, new VersionReference.ReferenceType.Pinned(1)))
        );
    }

    @Override
    public Subscription previous(Subscription instance) {
        // TODO: Implement if version should be visible via API
        return null;
    }

    @Override
    public Subscription next(Subscription instance) {
        // TODO: Implement if version should be visible via API
        return null;
    }

    public Subscription fromEntity(DSLContext dslContext, SubscriptionEntity se) {
        List<SubscriptionCharge> charges = subscriptionDAO.getSubscriptionCharges(dslContext, se.getId());
        return new SubscriptionImpl(se, charges);
    }

    public Set<String> getAllSubscriptionIdsInPeriodForTenant(Period period) {
        return subscriptionDAO.getAllSubscriptionIdsInPeriodForTenant(period);
    }

    public PageResult<List<String>, String> getAllSubscriptionsWithFuzzyPagination(PageRequest<String> pageRequest, Set<String> excludedAccountIds) {
        Validator.validateNonNullArguments(excludedAccountIds);
        return subscriptionDAO.getAllSubscriptionsWithFuzzyPagination(pageRequest, excludedAccountIds);
    }

    public SubscriptionCharge getSubscriptionChargeById(UUID subscriptionChargeUuid) {
        return subscriptionDAO.getSubscriptionChargeById(subscriptionChargeUuid);
    }

    public SubscriptionCharge getSubscriptionChargeBySubscriptionChargeId(String subscriptionChargeId) {
        return getSubscriptionChargeBySubscriptionChargeIdOptional(subscriptionChargeId).orElseThrow(() ->
            new InvariantCheckFailedException(String.format("Subscription charge id %s not found", subscriptionChargeId))
        );
    }

    public Optional<SubscriptionCharge> getSubscriptionChargeBySubscriptionChargeIdOptional(String subscriptionChargeId) {
        return subscriptionDAO.getSubscriptionChargeBySubscriptionChargeId(subscriptionChargeId);
    }

    public Set<String> getAllSubscriptionsInAccountOrderedByStartDateAsc(String accountId) {
        Validator.validateStringNotBlank(accountId, "accountId cannot be null");
        return subscriptionDAO.getAllSubscriptionsInAccountOrderedByStartDateAsc(accountId);
    }

    public Set<String> getUpdatableSubscriptionsByAccountId(String accountId) {
        Validator.validateStringNotBlank(accountId, "Account ID cannot be blank");
        return subscriptionDAO.getUpdatableSubscriptionsByAccountId(accountId);
    }

    public int getNextSubscriptionVersion(String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId cannot be null");
        return subscriptionDAO.getNextSubscriptionVersion(subscriptionId);
    }

    public Instant getEarliestDayOfASubscriptionInAccount(String accountId) {
        return subscriptionDAO.getEarliestDayOfASubscriptionInAccount(accountId);
    }

    public Instant getLatestDayOfASubscriptionInAccount(String accountId) {
        return subscriptionDAO.getLatestDayOfASubscriptionInAccount(accountId);
    }
}
