package com.subskribe.billy.subscription.services;

import com.subskribe.billy.shared.logger.ErrorContext;
import com.subskribe.billy.shared.logger.ErrorInstructions;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.task.queue.model.ImmutableProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskResult;
import com.subskribe.billy.shared.task.queue.model.ProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import com.subskribe.billy.shared.task.queue.processor.TaskProcessor;
import com.subskribe.billy.shared.task.queue.retry.FixedBackoff;
import com.subskribe.billy.shared.task.queue.retry.ImmutableTaskBackoff;
import com.subskribe.billy.shared.task.queue.retry.TaskBackoffResultBuilder;
import com.subskribe.billy.subscription.SubscriptionProductMetadata;
import com.subskribe.billy.subscription.db.SubscriptionChargeChangeScheduleDAO;
import com.subskribe.billy.subscription.db.SubscriptionStatusChangeScheduleDAO;
import com.subskribe.billy.subscription.model.SubscriptionChargeChangeSchedule;
import com.subskribe.billy.subscription.model.SubscriptionStatusChangeSchedule;
import com.subskribe.billy.validation.Validator;
import java.time.Duration;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;

public class SubscriptionChangeTaskProcessor implements TaskProcessor {

    static final TaskModule TASK_MODULE = new TaskModule("subscription-change");
    static final TaskType STATUS_CHANGE_TASK_TYPE = new TaskType("subscription-status-change");
    static final TaskType CHARGE_CHANGE_TASK_TYPE = new TaskType("subscription-charge-change");
    private static final Duration TIMEOUT = Duration.ofMinutes(2);
    private static final int MAX_ATTEMPTS = 3;
    private static final Duration RETRY_DELAY = Duration.ofSeconds(15);
    private static final ErrorContext ERROR_CONTEXT = new ErrorContext(
        SubscriptionProductMetadata.SUBSCRIPTION_LIFECYCLE,
        ErrorInstructions.sev3(
            "Failed to process subscription change task",
            "This should never happen. Investigate the error, and contact #infra for help with troubleshooting if needed."
        )
    );

    private static final Logger LOGGER = LoggerFactory.getLogger(SubscriptionChangeTaskProcessor.class);

    private final SubscriptionStatusChangeScheduleDAO subscriptionStatusChangeScheduleDAO;
    private final SubscriptionChargeChangeScheduleDAO subscriptionChargeChangeScheduleDAO;
    private final SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService;
    private final TaskBackoffResultBuilder taskBackoffResultBuilder;

    @Inject
    public SubscriptionChangeTaskProcessor(
        SubscriptionStatusChangeScheduleDAO subscriptionStatusChangeScheduleDAO,
        SubscriptionChargeChangeScheduleDAO subscriptionChargeChangeScheduleDAO,
        SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService,
        TaskBackoffResultBuilder taskBackoffResultBuilder
    ) {
        this.subscriptionStatusChangeScheduleDAO = subscriptionStatusChangeScheduleDAO;
        this.subscriptionChargeChangeScheduleDAO = subscriptionChargeChangeScheduleDAO;
        this.subscriptionLifecycleScheduleService = subscriptionLifecycleScheduleService;
        this.taskBackoffResultBuilder = taskBackoffResultBuilder;
    }

    @Override
    public TaskResult process(QueuedTask task) {
        UUID changeId = Validator.validateUUID(task.getTaskData(), "changeId");

        try {
            if (STATUS_CHANGE_TASK_TYPE.equals(task.getType())) {
                return processStatusChange(changeId);
            }
            if (CHARGE_CHANGE_TASK_TYPE.equals(task.getType())) {
                return processChargeChange(changeId);
            }
            LOGGER.warn(SubscriptionProductMetadata.SUBSCRIPTION_LIFECYCLE, "Unsupported task type: {}", task.getType());
            return ImmutableTaskResult.builder().successful(false).retryable(false).failureReason("Unsupported task type: " + task.getType()).build();
        } catch (Exception e) {
            LOGGER.warn(SubscriptionProductMetadata.SUBSCRIPTION_LIFECYCLE, "Error processing {} task for {}", task.getType(), changeId, e);

            TaskResult backoffResult = taskBackoffResultBuilder.withBackoff(
                ImmutableTaskBackoff.builder()
                    .backoffProvider(new FixedBackoff(RETRY_DELAY))
                    .task(task)
                    .maxAttempts(MAX_ATTEMPTS)
                    .failureReason("Unhandled exception when processing: " + e.getMessage())
                    .build()
            );

            if (!backoffResult.isRetryable()) {
                LOGGER.error(ERROR_CONTEXT, "Max retry attempts reached for {} task {}", task.getType(), changeId);
            }

            return backoffResult;
        }
    }

    private TaskResult processStatusChange(UUID changeId) {
        Optional<SubscriptionStatusChangeSchedule> changeOptional = subscriptionStatusChangeScheduleDAO.getChangeById(changeId);

        if (changeOptional.isEmpty()) {
            return ImmutableTaskResult.builder().successful(true).build();
        }

        subscriptionLifecycleScheduleService.processStatusChange(changeOptional.get());

        return ImmutableTaskResult.builder().successful(true).build();
    }

    private TaskResult processChargeChange(UUID changeId) {
        Optional<SubscriptionChargeChangeSchedule> changeOptional = subscriptionChargeChangeScheduleDAO.getChangeById(changeId);

        if (changeOptional.isEmpty()) {
            return ImmutableTaskResult.builder().successful(true).build();
        }

        subscriptionLifecycleScheduleService.processChange(changeOptional.get());
        return ImmutableTaskResult.builder().successful(true).build();
    }

    @Override
    public ProcessorConfiguration getConfiguration() {
        return ImmutableProcessorConfiguration.builder().taskModule(TASK_MODULE).taskTimeout(TIMEOUT).build();
    }
}
