package com.subskribe.billy.subscription.services;

import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.crm.CrmType;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.hubspot.service.HubSpotJobQueueService;
import com.subskribe.billy.invoice.model.EmailNotifiersList;
import com.subskribe.billy.invoice.model.PurchaseOrder;
import com.subskribe.billy.metrics.datadog.AllMetrics;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.resources.json.subscription.SubscriptionUpdateJson;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.shared.traits.VersionFinder;
import com.subskribe.billy.shared.traits.VersionReference;
import com.subskribe.billy.shared.traits.VersionReference.ReferenceType;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.db.SubscriptionDAO;
import com.subskribe.billy.subscription.model.SubscriptionChargeEntity;
import com.subskribe.billy.subscription.model.SubscriptionChargeMapEntity;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.model.SubscriptionMapper;
import com.subskribe.billy.subscription.model.SubscriptionState;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

@AllMetrics
public class SubscriptionService implements VersionFinder<String, Subscription> {

    private static final Logger LOGGER = LoggerFactory.getLogger(SubscriptionService.class);

    private final SubscriptionDAO subscriptionDAO;

    private final DSLContextProvider dslContextProvider;

    private final TenantIdProvider tenantIdProvider;

    private final AccountGetService accountGetService;

    private final SubscriptionIdGenerator subscriptionIdGenerator;

    private final SubscriptionMapper subscriptionMapper;

    private final SubscriptionGetService subscriptionGetService;

    private final SalesforceJobQueueService salesforceJobQueueService;

    private final HubSpotJobQueueService hubSpotJobQueueService;

    private final EmailContactListService emailContactListService;

    private final CustomFieldService customFieldService;

    private final CustomFieldAPIMapper customFieldAPIMapper;

    private final CrmService crmService;

    private final SubscriptionEventService subscriptionEventService;

    @Inject
    public SubscriptionService(
        SubscriptionDAO subscriptionDAO,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider,
        AccountGetService accountGetService,
        SubscriptionIdGenerator subscriptionIdGenerator,
        SubscriptionGetService subscriptionGetService,
        SalesforceJobQueueService salesforceJobQueueService,
        HubSpotJobQueueService hubSpotJobQueueService,
        EmailContactListService emailContactListService,
        CustomFieldService customFieldService,
        CrmService crmService,
        SubscriptionEventService subscriptionEventService
    ) {
        this.subscriptionDAO = subscriptionDAO;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.accountGetService = accountGetService;
        this.subscriptionIdGenerator = subscriptionIdGenerator;
        this.subscriptionGetService = subscriptionGetService;
        this.salesforceJobQueueService = salesforceJobQueueService;
        this.hubSpotJobQueueService = hubSpotJobQueueService;
        this.emailContactListService = emailContactListService;
        this.customFieldService = customFieldService;
        this.crmService = crmService;
        this.subscriptionEventService = subscriptionEventService;
        subscriptionMapper = Mappers.getMapper(SubscriptionMapper.class);
        customFieldAPIMapper = Mappers.getMapper(CustomFieldAPIMapper.class);
    }

    public Subscription createSubscription(DSLContext dslContext, Order order) {
        // generating subscription and charge ids to avoid multiple trips to db
        var subscriptionId = AutoGenerate.getNewUuid();
        order.setExternalSubscriptionId(subscriptionIdGenerator.generate());

        List<PurchaseOrder> purchaseOrders = addPurchaseOrdersToSubscription(null, order.getPurchaseOrderNumber(), order.getOrderId());
        SubscriptionEntity se = createSubscriptionEntity(
            order,
            subscriptionId,
            null,
            List.of(order.getOrderId()),
            order.getStartDate(),
            purchaseOrders,
            order.getPurchaseOrderRequiredForInvoicing()
        );
        addRenewalDetails(se, order, null);
        addRestructureDetails(se, order, null);

        List<SubscriptionChargeEntity> charges = new ArrayList<>();
        List<SubscriptionChargeMapEntity> subscriptionChargeMap = new ArrayList<>();

        // use the same externalSubscriptionChargeId for ramp deals (identified by same chargeId)
        Map<String, String> chargeIdSubChargeGrpIdMap = new HashMap<>();
        for (var lineItem : order.getLineItems()) {
            fillNewCharge(order, subscriptionId, charges, subscriptionChargeMap, chargeIdSubChargeGrpIdMap, lineItem, lineItem.getRank());
        }

        subscriptionDAO.addSubscription(dslContext, se, charges, subscriptionChargeMap);
        Subscription subscription = fromEntity(
            dslContext,
            subscriptionDAO.getSubscription(dslContext, new VersionReference<>(order.getExternalSubscriptionId(), ReferenceType.CURRENT))
        );
        copyOrderCustomFieldsToSubscription(order, subscription);
        return subscription;
    }

    public Subscription updateSubscription(DSLContext dslContext, Order order) {
        var currentSubscription = subscriptionGetService.getSubscription(dslContext, order.getExternalSubscriptionId());

        var subscriptionId = AutoGenerate.getNewUuid();
        List<SubscriptionChargeEntity> subscriptionChargeList = new ArrayList<>();
        List<SubscriptionChargeMapEntity> subscriptionChargeMapList = new ArrayList<>();

        fillNewCharges(order, currentSubscription, subscriptionId, subscriptionChargeList, subscriptionChargeMapList);
        Map<UUID, String> chargeIdOrderLineIdMap = fillExistingCharges(
            order,
            currentSubscription,
            subscriptionId,
            subscriptionChargeList,
            subscriptionChargeMapList
        );

        var ordersList = currentSubscription.getOrders();
        ordersList.add(order.getOrderId());
        List<PurchaseOrder> purchaseOrders = addPurchaseOrdersToSubscription(
            currentSubscription.getPurchaseOrders(),
            order.getPurchaseOrderNumber(),
            order.getOrderId()
        );
        boolean purchaseOrderRequired = currentSubscription.getPurchaseOrderRequiredForInvoicing() || order.getPurchaseOrderRequiredForInvoicing();
        SubscriptionEntity se = createSubscriptionEntity(
            order,
            subscriptionId,
            currentSubscription.getExternalId(),
            ordersList,
            currentSubscription.getStartDate(),
            purchaseOrders,
            purchaseOrderRequired
        );
        addRenewalDetails(se, order, currentSubscription);
        addRestructureDetails(se, order, currentSubscription);
        // WARNING: the proration pinning option needs to be carried across through all versions of subscription
        se.setPinnedProrationOption(currentSubscription.getPinnedProrationOption());
        se.setRenewalOpportunityCrmId(currentSubscription.getRenewalOpportunityCrmId());

        subscriptionDAO.addSubscription(dslContext, se, subscriptionChargeList, subscriptionChargeMapList);
        updatePreviousVersion(dslContext, currentSubscription, order, chargeIdOrderLineIdMap);
        Subscription updatedSubscription = fromEntity(
            dslContext,
            subscriptionDAO.getSubscription(dslContext, new VersionReference<>(order.getExternalSubscriptionId(), ReferenceType.CURRENT))
        );
        subscriptionEventService.publishSubscriptionEvent(updatedSubscription, dslContext, EventType.SUBSCRIPTION_UPDATED);
        copyOrderCustomFieldsToSubscription(order, updatedSubscription);
        return updatedSubscription;
    }

    private List<PurchaseOrder> addPurchaseOrdersToSubscription(
        List<PurchaseOrder> purchaseOrders,
        String purchaseOrderNumber,
        String originOrderId
    ) {
        purchaseOrders = Optional.ofNullable(purchaseOrders).orElse(new ArrayList<>());
        if (StringUtils.isBlank(purchaseOrderNumber)) {
            return purchaseOrders;
        }

        PurchaseOrder purchaseOrder = new PurchaseOrder(purchaseOrderNumber, originOrderId, Instant.now().getEpochSecond());
        purchaseOrder.validate();
        purchaseOrders.add(0, purchaseOrder);
        return purchaseOrders;
    }

    private Map<UUID, String> fillExistingCharges(
        Order order,
        Subscription currentSubscription,
        UUID subscriptionUuid,
        List<SubscriptionChargeEntity> subscriptionChargeList,
        List<SubscriptionChargeMapEntity> subscriptionChargeMapList
    ) {
        var existingChargesMap = order
            .getLineItemsHavingBaseSubscriptionCharge()
            .stream()
            .filter(orderLineItem -> orderLineItem.getAction() != ActionType.NONE)
            .collect(Collectors.groupingBy(OrderLineItem::getBaseExternalSubscriptionChargeId));

        Set<String> affectedChargeGroups = new HashSet<>();
        currentSubscription
            .getCharges()
            .forEach(c -> {
                if (existingChargesMap.containsKey(c.getSubscriptionChargeId())) {
                    affectedChargeGroups.add(c.getSubscriptionChargeGroupId());
                }
            });

        Set<String> unaffectedChargeGroups = currentSubscription
            .getCharges()
            .stream()
            .map(SubscriptionCharge::getSubscriptionChargeGroupId)
            .collect(Collectors.toSet());

        unaffectedChargeGroups.removeAll(affectedChargeGroups);

        currentSubscription
            .getCharges()
            .forEach(c -> addUnaffectedGroupChargesToChargeMap(order, subscriptionUuid, subscriptionChargeMapList, unaffectedChargeGroups, c));

        return populateAffectedGroupCharges(
            order,
            currentSubscription,
            subscriptionUuid,
            subscriptionChargeList,
            subscriptionChargeMapList,
            existingChargesMap,
            affectedChargeGroups
        );
    }

    private Map<UUID, String> populateAffectedGroupCharges(
        Order order,
        Subscription currentSubscription,
        UUID subscriptionUuid,
        List<SubscriptionChargeEntity> subscriptionChargeList,
        List<SubscriptionChargeMapEntity> subscriptionChargeMapList,
        Map<String, List<OrderLineItem>> existingChargesMap,
        Set<String> affectedChargeGroups
    ) {
        Map<UUID, String> chargeIdOrderLineIdMap = new HashMap<>(); // for updating to new version of the charges
        Set<String> chargeGroups = new HashSet<>();
        Set<String> affectedChargeIdsList = new HashSet<>();
        currentSubscription
            .getCharges()
            .forEach(charge ->
                addAffectedSubscriptionCharge(
                    order,
                    subscriptionUuid,
                    subscriptionChargeList,
                    subscriptionChargeMapList,
                    existingChargesMap,
                    affectedChargeGroups,
                    chargeIdOrderLineIdMap,
                    chargeGroups,
                    affectedChargeIdsList,
                    charge
                )
            );
        return chargeIdOrderLineIdMap;
    }

    private void addAffectedSubscriptionCharge(
        Order order,
        UUID subscriptionUuid,
        List<SubscriptionChargeEntity> subscriptionChargeList,
        List<SubscriptionChargeMapEntity> subscriptionChargeMapList,
        Map<String, List<OrderLineItem>> existingChargesMap,
        Set<String> affectedChargeGroups,
        Map<UUID, String> chargeIdOrderLineIdMap,
        Set<String> chargeGroups,
        Set<String> affectedChargeIdsList,
        SubscriptionCharge charge
    ) {
        var orderLines = existingChargesMap.get(charge.getSubscriptionChargeId());
        if (orderLines == null) {
            if (affectedChargeGroups.contains(charge.getSubscriptionChargeGroupId())) {
                populateExistingCharge(order, subscriptionChargeList, chargeIdOrderLineIdMap, charge);
            }
        } else {
            orderLines.forEach(orderLineItem ->
                populateAffectedCharge(order, subscriptionChargeList, chargeIdOrderLineIdMap, affectedChargeIdsList, charge, orderLineItem)
            );
        }

        if (!chargeGroups.contains(charge.getSubscriptionChargeGroupId())) {
            subscriptionChargeMapList.add(
                createSubscriptionChargeMapEntity(
                    order,
                    subscriptionUuid,
                    charge.getSubscriptionChargeGroupId(),
                    order.getSubscriptionTargetVersion()
                )
            );
            chargeGroups.add(charge.getSubscriptionChargeGroupId());
        }
    }

    private void populateAffectedCharge(
        Order order,
        List<SubscriptionChargeEntity> subscriptionChargeList,
        Map<UUID, String> chargeIdOrderLineIdMap,
        Set<String> affectedChargeIdsList,
        SubscriptionCharge subscriptionCharge,
        OrderLineItem orderLineItem
    ) {
        if (!affectedChargeIdsList.contains(subscriptionCharge.getSubscriptionChargeId())) {
            populateChargeTillEffectiveDate(order, subscriptionChargeList, chargeIdOrderLineIdMap, orderLineItem, subscriptionCharge);
            affectedChargeIdsList.add(subscriptionCharge.getSubscriptionChargeId());
        }
        if (orderLineItem.getAction() != ActionType.REMOVE && orderLineItem.getQuantity() > 0) {
            subscriptionChargeList.add(
                createSubscriptionChargeEntity(
                    order,
                    orderLineItem,
                    subscriptionCharge.getSubscriptionChargeGroupId(),
                    List.of(orderLineItem.getOrderLineId()),
                    orderLineItem.getEffectiveDate(),
                    orderLineItem.getEndDate(),
                    subscriptionCharge.getRank()
                )
            );
        }
    }

    private void addUnaffectedGroupChargesToChargeMap(
        Order order,
        UUID subscriptionUuid,
        List<SubscriptionChargeMapEntity> subscriptionChargeMapList,
        Set<String> unaffectedChargeGroups,
        SubscriptionCharge c
    ) {
        if (unaffectedChargeGroups.contains(c.getSubscriptionChargeGroupId())) {
            subscriptionChargeMapList.add(
                createSubscriptionChargeMapEntity(order, subscriptionUuid, c.getSubscriptionChargeGroupId(), c.getVersion())
            );
            unaffectedChargeGroups.remove(c.getSubscriptionChargeGroupId());
        }
    }

    private void populateExistingCharge(
        Order order,
        List<SubscriptionChargeEntity> subscriptionChargeList,
        Map<UUID, String> chargeIdOrderLineIdMap,
        SubscriptionCharge subscriptionCharge
    ) {
        chargeIdOrderLineIdMap.put(subscriptionCharge.getId(), StringUtils.EMPTY);
        subscriptionChargeList.add(
            createSubscriptionChargeEntity(
                subscriptionCharge,
                subscriptionCharge.getStartDate(),
                subscriptionCharge.getEndDate(),
                subscriptionCharge.getOrderLines(),
                order.getSubscriptionTargetVersion(),
                Instant.now(),
                order.getEndDate(),
                subscriptionCharge.getRank()
            )
        );
    }

    private void populateChargeTillEffectiveDate(
        Order order,
        List<SubscriptionChargeEntity> subscriptionChargeList,
        Map<UUID, String> chargeIdOrderLineIdMap,
        OrderLineItem orderLineItem,
        SubscriptionCharge subscriptionCharge
    ) {
        if (
            subscriptionCharge.getEndDate().isBefore(orderLineItem.getEffectiveDate()) ||
            subscriptionCharge.getEndDate().equals(orderLineItem.getEffectiveDate())
        ) {
            chargeIdOrderLineIdMap.put(subscriptionCharge.getId(), orderLineItem.getOrderLineId());
            subscriptionChargeList.add(
                createSubscriptionChargeEntity(
                    subscriptionCharge,
                    subscriptionCharge.getStartDate(),
                    subscriptionCharge.getEndDate(),
                    subscriptionCharge.getOrderLines(),
                    order.getSubscriptionTargetVersion(),
                    Instant.now(),
                    order.getEndDate(),
                    subscriptionCharge.getRank()
                )
            );
        } else if (
            subscriptionCharge.getStartDate().isBefore(orderLineItem.getEffectiveDate()) &&
            subscriptionCharge.getEndDate().isAfter(orderLineItem.getEffectiveDate())
        ) {
            chargeIdOrderLineIdMap.put(subscriptionCharge.getId(), orderLineItem.getOrderLineId());
            var lines = subscriptionCharge.getOrderLines();
            lines.add(orderLineItem.getOrderLineId());
            subscriptionChargeList.add(
                createSubscriptionChargeEntity(
                    subscriptionCharge,
                    subscriptionCharge.getStartDate(),
                    orderLineItem.getEffectiveDate(),
                    lines,
                    order.getSubscriptionTargetVersion(),
                    Instant.now(),
                    order.getEndDate(),
                    subscriptionCharge.getRank()
                )
            );
        }
    }

    private void fillNewCharges(
        Order order,
        Subscription currentSubscription,
        UUID subscriptionUuid,
        List<SubscriptionChargeEntity> charges,
        List<SubscriptionChargeMapEntity> subscriptionChargeMapEntityList
    ) {
        var newCharges = order
            .getLineItemsWithEmptyBaseSubscriptionCharge()
            .stream()
            .filter(orderLineItem -> orderLineItem.getAction() != ActionType.NONE)
            .sorted(Comparator.comparing(OrderLineItem::getRank));

        Map<String, String> chargeIdSubChargeIdMap = new HashMap<>();
        int startingRank = getStartingRank(currentSubscription);
        AtomicInteger rank = new AtomicInteger(startingRank);
        newCharges.forEach(lineItem ->
            fillNewCharge(order, subscriptionUuid, charges, subscriptionChargeMapEntityList, chargeIdSubChargeIdMap, lineItem, rank.getAndIncrement())
        );
    }

    private void fillNewCharge(
        Order order,
        UUID subscriptionUuid,
        List<SubscriptionChargeEntity> charges,
        List<SubscriptionChargeMapEntity> subscriptionChargeMapEntityList,
        Map<String, String> chargeIdSubChargeGrpIdMap,
        OrderLineItem lineItem,
        int rank
    ) {
        boolean found = false;
        String subscriptionChargeGroupId;
        if (lineItem.getIsRamp()) {
            // ramped line items are part of the same charge group
            found = chargeIdSubChargeGrpIdMap.containsKey(lineItem.getChargeId());
            if (!found) {
                chargeIdSubChargeGrpIdMap.put(lineItem.getChargeId(), AutoGenerate.getNewId());
            }
            subscriptionChargeGroupId = chargeIdSubChargeGrpIdMap.get(lineItem.getChargeId());
        } else {
            // new line items are part of their own charge group
            subscriptionChargeGroupId = AutoGenerate.getNewId();
        }

        charges.add(
            createSubscriptionChargeEntity(
                order,
                lineItem,
                subscriptionChargeGroupId,
                List.of(lineItem.getOrderLineId()),
                lineItem.getEffectiveDate(),
                lineItem.getEndDate(),
                rank
            )
        );

        if (!found) {
            subscriptionChargeMapEntityList.add(
                createSubscriptionChargeMapEntity(order, subscriptionUuid, subscriptionChargeGroupId, order.getSubscriptionTargetVersion())
            );
        }
    }

    private int getStartingRank(Subscription subscription) {
        return subscription.getCharges().stream().map(SubscriptionCharge::getRank).max(Integer::compareTo).orElse(0) + 1;
    }

    private void updatePreviousVersion(
        DSLContext dslContext,
        Subscription currentSubscription,
        Order order,
        Map<UUID, String> chargeIdOrderLineIdMap
    ) {
        var instant = Instant.now();
        subscriptionDAO.updateSubscriptionVersionEnd(dslContext, currentSubscription, order.getOrderId(), instant);
        subscriptionDAO.updateSubscriptionChargesVersionEnd(dslContext, currentSubscription, chargeIdOrderLineIdMap, instant);
    }

    private SubscriptionChargeMapEntity createSubscriptionChargeMapEntity(
        Order order,
        UUID subscriptionUuid,
        String subscriptionChargeGroupId,
        int version
    ) {
        return new SubscriptionChargeMapEntity(null, order.getTenantId(), subscriptionUuid, subscriptionChargeGroupId, version);
    }

    private SubscriptionChargeEntity createSubscriptionChargeEntity(
        Order order,
        OrderLineItem lineItem,
        String externalSubscriptionChargeGroupId,
        List<String> orderLines,
        Instant startDate,
        Instant endDate,
        int rank
    ) {
        return new SubscriptionChargeEntity(
            AutoGenerate.getNewUuid(),
            lineItem.getChargeId(),
            AutoGenerate.getNewId(),
            externalSubscriptionChargeGroupId,
            order.getExternalSubscriptionId(),
            order.getTenantId(),
            order.getAccountId(),
            lineItem.getCurrencyConversionRateId(),
            orderLines,
            startDate,
            endDate,
            lineItem.getQuantity(),
            lineItem.getIsRamp(),
            subscriptionMapper.discountDetailToDiscount(lineItem.getDiscounts()),
            lineItem.getPredefinedDiscounts(),
            lineItem.getAttributeReferences(),
            lineItem.getListUnitPrice(),
            lineItem.getSellUnitPrice(),
            lineItem.getDiscountAmount(),
            lineItem.getPricingOverride(),
            lineItem.getListPriceOverrideRatio(),
            lineItem.getArrOverride(),
            order.getSubscriptionTargetVersion(),
            Instant.now(),
            order.getEndDate(),
            rank
        );
    }

    private SubscriptionChargeEntity createSubscriptionChargeEntity(
        SubscriptionCharge charge,
        Instant startDate,
        Instant endDate,
        List<String> orderLines,
        int version,
        Instant versionStartDate,
        Instant versionEndDate,
        int rank
    ) {
        return new SubscriptionChargeEntity(
            AutoGenerate.getNewUuid(),
            charge.getChargeId(),
            AutoGenerate.getNewId(),
            charge.getSubscriptionChargeGroupId(),
            charge.getSubscriptionId(),
            charge.getTenantId(),
            charge.getAccountId(),
            charge.getCurrencyConversionRateId(),
            orderLines,
            startDate,
            endDate,
            charge.getQuantity(),
            charge.getIsRamp(),
            charge.getDiscounts(),
            charge.getPredefinedDiscounts(),
            charge.getAttributeReferences(),
            charge.getListUnitPrice(),
            charge.getSellUnitPrice(),
            charge.getDiscountAmount(),
            charge.getPricingOverride(),
            charge.getListPriceOverrideRatio(),
            charge.getArrOverride(),
            version,
            versionStartDate,
            versionEndDate,
            rank
        );
    }

    private SubscriptionEntity createSubscriptionEntity(
        Order order,
        UUID subscriptionUuid,
        String externalId,
        List<String> orders,
        Instant startDate,
        List<PurchaseOrder> purchaseOrders,
        boolean purchaseOrderRequired
    ) {
        var subscriptionEntity = new SubscriptionEntity(
            subscriptionUuid,
            order.getExternalSubscriptionId(), // todo: perhaps someone confusing, this is our internal ID (SUB-xxxx), can we consolidate?
            externalId, // this is the subscription ID from an external system,
            order.getTenantId(),
            order.getEntityId(),
            order.getAccountId(),
            order.getResellerAccountId().orElse(null),
            order.getShippingContactId(),
            order.getBillingContactId(),
            order.getCurrency(),
            order.getPaymentTerm(),
            SubscriptionState.ACTIVE,
            startDate,
            order.getEndDate(),
            order.getBillingAnchorDate(),
            order.getOrderType() == OrderType.CANCEL ? order.getStartDate() : null,
            order.getTermLength(),
            order.getBillingCycle(),
            order.getBillingTerm(),
            order.getPredefinedDiscounts(),
            order.getRampInterval(),
            orders,
            purchaseOrders,
            purchaseOrderRequired,
            order.getAutoRenew(),
            null,
            Instant.now(),
            order.getSubscriptionTargetVersion(),
            Instant.now(),
            order.getEndDate(),
            order.getSubscriptionDurationModel()
        );

        subscriptionEntity.setState(SubscriptionGetService.getStateForSubscriptionEntity(subscriptionEntity));
        return subscriptionEntity;
    }

    private void addRenewalDetails(SubscriptionEntity se, Order order, Subscription currentSubscription) {
        if (order.getOrderType() == OrderType.RESTRUCTURE) {
            return;
        }
        // RenewedFromSubscriptionId needs to be updated in 2 scenarios
        // a. When the order type is renewal, then copy the renewalFromSubscriptionId on the existing order
        // b. If the order is amendment, we need to store the renewedFromSubscriptionId from previous subscription version

        if (order.getOrderType() == OrderType.RENEWAL) {
            se.setRenewedFromSubscriptionId(order.getRenewalForSubscriptionId());
            se.setRenewedFromDate(Instant.now());
            return;
        }

        if (order.getOrderType() != OrderType.NEW) {
            se.setRenewedFromSubscriptionId(currentSubscription.getRenewedFromSubscriptionId());
            se.setRenewedFromDate(currentSubscription.getRenewedFromDate());
            se.setRenewedToSubscriptionId(currentSubscription.getRenewedToSubscriptionId());
            se.setRenewedToDate(currentSubscription.getRenewedToDate());
        }
    }

    private void addRestructureDetails(SubscriptionEntity se, Order order, Subscription currentSubscription) {
        // RestructuredFromSubscriptionId needs to be updated in 2 scenarios
        // 1. When the order type is RESTRUCTURE, then copy the restructuredFromSubscriptionId from the order request
        // 2. If the order is amendment of the previously restructured order, we need to store the restructuredFromSubscriptionId from previous subscription version
        // TODO case #2 needs more discussion based with product if this is required

        if (order.getOrderType() == OrderType.RESTRUCTURE) {
            se.setRestructuredFromSubscriptionId(order.getRestructureForSubscriptionId());
            se.setRestructuredFromDate(Instant.now());
            return;
        }

        // If the order is a CANCEL order while the subscription is being restructured, then we need to store the restructuredToSubscriptionId from previous subscription version
        if (order.getOrderType() == OrderType.CANCEL && StringUtils.isNotBlank(currentSubscription.getRestructuredToSubscriptionId())) {
            se.setRestructuredToSubscriptionId(currentSubscription.getRestructuredToSubscriptionId());
            se.setRestructuredFromDate(currentSubscription.getRestructuredToDate());
            return;
        }

        if (order.getOrderType() == OrderType.AMENDMENT) {
            se.setRestructuredFromSubscriptionId(currentSubscription.getRestructuredFromSubscriptionId());
            se.setRestructuredFromDate(currentSubscription.getRestructuredFromDate());
        }
    }

    public void updateSubscriptionAttributes(String subscriptionId, SubscriptionUpdateJson subscriptionUpdate) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is blank");

        subscriptionUpdate.validate();

        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var subscription = subscriptionDAO.getSubscription(dslContext, new VersionReference<>(subscriptionId, ReferenceType.CURRENT));

        String shippingContactId = subscriptionUpdate.getShippingContactId();
        if (StringUtils.isNotBlank(shippingContactId)) {
            verifyContactUpdate(shippingContactId, subscription, false);
            subscription.setShippingContactId(shippingContactId);
        }

        String billingContactId = subscriptionUpdate.getBillingContactId();
        if (StringUtils.isNotBlank(billingContactId)) {
            verifyContactUpdate(billingContactId, subscription, true);
            subscription.setBillingContactId(billingContactId);
        }

        String purchaseOrderNumber = subscriptionUpdate.getPurchaseOrderNumber();
        if (StringUtils.isNotBlank(purchaseOrderNumber)) {
            Validator.validateNotEmptyStringLength(purchaseOrderNumber, PurchaseOrder.MAX_PURCHASE_ORDER_NUMBER_LENGTH, "purchaseOrderNumber");
            List<PurchaseOrder> purchaseOrders = addPurchaseOrdersToSubscription(subscription.getPurchaseOrders(), purchaseOrderNumber, null);
            subscription.setPurchaseOrders(purchaseOrders);
        }

        boolean purchaseOrderRequired = BooleanUtils.toBooleanDefaultIfNull(
            subscriptionUpdate.getPurchaseOrderRequiredForInvoicing(),
            subscription.getPurchaseOrderRequiredForInvoicing()
        );
        subscription.setPurchaseOrderRequiredForInvoicing(purchaseOrderRequired);

        if (StringUtils.isNotBlank(subscriptionUpdate.getExternalId())) {
            // update external ID if a new one is passed in
            subscription.setExternalId(subscriptionUpdate.getExternalId());
        }

        boolean autoRenew = BooleanUtils.toBooleanDefaultIfNull(subscriptionUpdate.getAutoRenew(), subscription.getAutoRenew());
        subscription.setAutoRenew(autoRenew);

        Instant activationDate = DateTimeConverter.epochSecondsToInstant(subscriptionUpdate.getActivationDate());
        subscription.setActivationDate(activationDate);

        subscription.setName(subscriptionUpdate.getName());

        subscription.validate();
        dslContext.transaction(configuration -> {
            subscriptionDAO.updateSubscriptionAttributes(configuration.dsl(), subscription);
            // Update email contact list
            updateEmailContactsForSubscription(configuration.dsl(), subscriptionId, subscriptionUpdate);
        });
    }

    public void updateRenewalOpportunity(String subscriptionId, String renewalOpportunityCrmId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is blank");
        Validator.validateStringNotBlank(renewalOpportunityCrmId, "renewalOpportunityCrmId is blank");
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        SubscriptionEntity subscription = subscriptionDAO.getSubscription(dslContext, new VersionReference<>(subscriptionId, ReferenceType.CURRENT));
        subscriptionDAO.updateRenewalOpportunity(subscription.getId(), renewalOpportunityCrmId);
        salesforceJobQueueService.dispatchSubscriptionSync(subscriptionId);
    }

    private void verifyContactUpdate(String contactId, SubscriptionEntity subscription, boolean allowResellerContact) {
        var contact = accountGetService.getContact(contactId);
        if (contact.getAddress() == null) {
            String message = String.format("contact id %s is missing address field", contactId);
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }
        if (!contact.getAccountId().equals(subscription.getAccountId())) {
            var account = accountGetService.getAccount(contact.getAccountId());
            if (!allowResellerContact || !account.getIsReseller()) {
                String message = String.format(
                    "contact id %s is associated with account %s, but subscription is associated with account %s",
                    contact.getContactId(),
                    contact.getAccountId(),
                    subscription.getAccountId()
                );
                LOGGER.info(message);
                throw new IllegalArgumentException(message);
            }
        }
    }

    public void updateSubscriptionEntityState(UUID id, SubscriptionState newState) {
        subscriptionDAO.updateSubscriptionEntityState(id, newState);
    }

    @Override
    public Subscription at(String id, Instant time) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return fromEntity(dslContext, subscriptionDAO.getSubscriptionAt(dslContext, id, time));
    }

    @Override
    public Subscription find(String id, int version) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return fromEntity(dslContext, subscriptionDAO.getSubscription(dslContext, new VersionReference<>(id, new ReferenceType.Pinned(version))));
    }

    @Override
    public Subscription current(String id) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return fromEntity(dslContext, subscriptionDAO.getSubscription(dslContext, new VersionReference<>(id, ReferenceType.CURRENT)));
    }

    @Override
    public Subscription first(String id) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return fromEntity(dslContext, subscriptionDAO.getSubscription(dslContext, new VersionReference<>(id, new ReferenceType.Pinned(1))));
    }

    @Override
    public Subscription previous(Subscription instance) {
        // TODO: Implement if version should be visible via API
        return null;
    }

    @Override
    public Subscription next(Subscription instance) {
        // TODO: Implement if version should be visible via API
        return null;
    }

    private Subscription fromEntity(DSLContext dslContext, SubscriptionEntity se) {
        List<SubscriptionCharge> charges = subscriptionDAO.getSubscriptionCharges(dslContext, se.getId());
        return new SubscriptionImpl(se, charges);
    }

    public void updateSubscriptionMetrics(
        DSLContext txnDslContext,
        String tenantId,
        UUID internalId,
        String subscriptionId,
        UUID metricsId,
        JSONB json
    ) {
        subscriptionDAO.updateSubscriptionMetrics(txnDslContext, tenantId, internalId, subscriptionId, metricsId, json);
    }

    public void setSubscriptionInvoicePreviews(
        DSLContext txnDslContext,
        String tenantId,
        UUID internalId,
        String subscriptionId,
        UUID metricsId,
        JSONB json
    ) {
        subscriptionDAO.setSubscriptionInvoicePreviews(txnDslContext, tenantId, internalId, subscriptionId, metricsId, json);
    }

    private void updateEmailContactsForSubscription(DSLContext dslContext, String subscriptionId, SubscriptionUpdateJson subscriptionUpdate) {
        EmailNotifiersList emailNotifiersList = subscriptionUpdate.getEmailNotifiersList();
        if (Objects.nonNull(emailNotifiersList)) {
            emailContactListService.updateEmailContactsForSubscription(dslContext, subscriptionId, emailNotifiersList);
        }
    }

    private void copyOrderCustomFieldsToSubscription(Order order, Subscription subscription) {
        String orderId = order.getOrderId();
        String subscriptionId = subscription.getSubscriptionId();
        CustomField subscriptionField = customFieldService.getCustomFields(CustomFieldParentType.SUBSCRIPTION, subscriptionId);
        Map<String, CustomFieldValue> subscriptionCustomFieldEntries = subscriptionField.getEntries();
        subscriptionCustomFieldEntries = transferCustomFields(CustomFieldParentType.ORDER, orderId, subscriptionCustomFieldEntries);
        CustomField updatedSubscriptionCustomField = customFieldAPIMapper.toCustomField(subscriptionCustomFieldEntries);
        customFieldService.setCustomFieldsBySystem(CustomFieldParentType.SUBSCRIPTION, subscriptionId, updatedSubscriptionCustomField);

        Map<String, List<OrderLineItem>> subscriptionChargeIdToOrderLines = order
            .getLineItems()
            .stream()
            .filter(li -> StringUtils.isNotBlank(li.getBaseExternalSubscriptionChargeId()))
            .collect(Collectors.groupingBy(OrderLineItem::getBaseExternalSubscriptionChargeId));
        for (var subscriptionCharge : subscription.getCharges()) {
            updateSubscriptionChargeCustomFields(subscriptionCharge, subscriptionChargeIdToOrderLines);
        }
    }

    private void updateSubscriptionChargeCustomFields(
        SubscriptionCharge subscriptionCharge,
        Map<String, List<OrderLineItem>> subscriptionChargeIdToOrderLines
    ) {
        CustomField subscriptionItemField = customFieldService.getCustomFields(
            CustomFieldParentType.SUBSCRIPTION_ITEM,
            subscriptionCharge.getSubscriptionChargeId()
        );
        Map<String, CustomFieldValue> fieldsToMap = subscriptionItemField.getEntries();
        List<String> orderLineIds = getOrderLineIdsForSubscriptionCharge(subscriptionChargeIdToOrderLines, subscriptionCharge);
        for (var orderLineId : orderLineIds) {
            fieldsToMap = transferCustomFields(CustomFieldParentType.ORDER_ITEM, orderLineId, fieldsToMap);
        }
        CustomField customField = customFieldAPIMapper.toCustomField(fieldsToMap);
        customFieldService.setCustomFieldsBySystem(
            CustomFieldParentType.SUBSCRIPTION_ITEM,
            subscriptionCharge.getSubscriptionChargeId(),
            customField
        );
    }

    private static List<String> getOrderLineIdsForSubscriptionCharge(
        Map<String, List<OrderLineItem>> subscriptionChargeIdToOrderLines,
        SubscriptionCharge subscriptionCharge
    ) {
        List<String> subscriptionItemNewLines = subscriptionChargeIdToOrderLines.containsKey(subscriptionCharge.getSubscriptionChargeId())
            ? subscriptionChargeIdToOrderLines.get(subscriptionCharge.getSubscriptionChargeId()).stream().map(OrderLineItem::getOrderLineId).toList()
            : List.of();
        List<String> rawOrderLines = subscriptionCharge.getOrderLines().stream().toList();
        return Stream.of(rawOrderLines, subscriptionItemNewLines).flatMap(List::stream).distinct().toList();
    }

    private Map<String, CustomFieldValue> transferCustomFields(
        CustomFieldParentType sourceType,
        String sourceParentId,
        Map<String, CustomFieldValue> destinationCustomFieldEntries
    ) {
        Optional<CustomField> sourceCustomField = customFieldService.getCustomFieldsOptional(sourceType, sourceParentId);
        if (sourceCustomField.isEmpty()) {
            return destinationCustomFieldEntries;
        }

        Map<String, CustomFieldValue> sourceCustomFieldEntryMap = sourceCustomField
            .get()
            .getEntries()
            .values()
            .stream()
            .collect(Collectors.toMap(CustomFieldValue::getName, Function.identity()));

        return destinationCustomFieldEntries
            .entrySet()
            .stream()
            .map(entry -> {
                if (!sourceCustomFieldEntryMap.containsKey(entry.getValue().getName())) {
                    return entry;
                }

                return Map.entry(entry.getKey(), sourceCustomFieldEntryMap.get(entry.getValue().getName()));
            })
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public void deleteSubscription(Configuration configuration, Subscription subscription) {
        String subscriptionId = subscription.getSubscriptionId();
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is blank");
        DSLContext dslContext = DSL.using(configuration);
        String tenantId = tenantIdProvider.provideTenantIdString();
        subscriptionDAO.deleteSubscriptionMetrics(dslContext, tenantId, subscriptionId);
        subscriptionDAO.deleteSubscription(dslContext, subscriptionId);
        subscriptionEventService.publishSubscriptionEvent(subscription, dslContext, EventType.SUBSCRIPTION_DELETED);

        Optional<CrmType> optionalCrmType = crmService.getCrmType();
        if (optionalCrmType.isEmpty()) {
            return;
        }
        CrmType crmType = optionalCrmType.get();
        switch (crmType) {
            case SALESFORCE -> salesforceJobQueueService.dispatchSubscriptionDeletionSync(subscriptionId, subscription.getAccountId());
            case HUBSPOT -> hubSpotJobQueueService.dispatchSubscriptionDeletionSync(subscription);
        }
    }

    public void deleteSubscriptionVersion(Configuration configuration, Subscription subscription) {
        Validator.validateNonNullArgument(subscription, "subscription");
        DSLContext dslContext = DSL.using(configuration);

        subscriptionDAO.deleteSubscriptionMetricsVersion(dslContext, subscription.getId());
        subscriptionDAO.deleteSubscriptionVersion(dslContext, subscription);
    }
}
