package com.subskribe.billy.subscription.services;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toSet;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.event.model.DefaultStreamPartitionKey;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventStatus;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.notification.service.TestNotificationService;
import com.subskribe.billy.resources.json.subscription.SubscriptionChargeJson;
import com.subskribe.billy.resources.json.subscription.SubscriptionJson;
import com.subskribe.billy.resources.json.subscription.SubscriptionJsonMapper;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskParent;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.scheduler.TaskDispatcher;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.db.SubscriptionChargeChangeScheduleDAO;
import com.subskribe.billy.subscription.db.SubscriptionStatusChangeScheduleDAO;
import com.subskribe.billy.subscription.model.ImmutableSubscriptionChargeChangeSchedule;
import com.subskribe.billy.subscription.model.ImmutableSubscriptionSchedules;
import com.subskribe.billy.subscription.model.ImmutableSubscriptionStatusChangeSchedule;
import com.subskribe.billy.subscription.model.SubscriptionChargeChangeSchedule;
import com.subskribe.billy.subscription.model.SubscriptionSchedules;
import com.subskribe.billy.subscription.model.SubscriptionState;
import com.subskribe.billy.subscription.model.SubscriptionStatusChangeSchedule;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.jetbrains.annotations.NotNull;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class SubscriptionLifecycleScheduleService {

    private final SubscriptionGetService subscriptionGetService;
    private final SubscriptionChargeChangeScheduleDAO subscriptionChargeChangeScheduleDAO;
    private final SubscriptionStatusChangeScheduleDAO subscriptionStatusChangeScheduleDAO;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final EventPublishingService eventPublishingService;
    private final Clock clock;
    private final SubscriptionJsonMapper subscriptionJsonMapper;
    private final ObjectMapper objectMapper;
    private final SubscriptionEventService subscriptionEventService;
    private final TestNotificationService testNotificationService;
    private final TaskDispatcher taskDispatcher;
    private final SubscriptionService subscriptionService;

    private static final Logger LOGGER = LoggerFactory.getLogger(SubscriptionLifecycleScheduleService.class);
    private static final Duration FUTURE_EVENT_BEFORE_DURATION = Duration.ofHours(1);
    private static final UncheckedObjectMapper UNCHECKED_OBJECT_MAPPER = UncheckedObjectMapper.defaultMapper();

    @Inject
    public SubscriptionLifecycleScheduleService(
        SubscriptionGetService subscriptionGetService,
        SubscriptionChargeChangeScheduleDAO subscriptionChargeChangeScheduleDAO,
        SubscriptionStatusChangeScheduleDAO subscriptionStatusChangeScheduleDAO,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider,
        EventPublishingService eventPublishingService,
        Clock clock,
        SubscriptionEventService subscriptionEventService,
        TestNotificationService testNotificationService,
        TaskDispatcher taskDispatcher,
        SubscriptionService subscriptionService
    ) {
        this.subscriptionGetService = subscriptionGetService;
        this.subscriptionChargeChangeScheduleDAO = subscriptionChargeChangeScheduleDAO;
        this.subscriptionStatusChangeScheduleDAO = subscriptionStatusChangeScheduleDAO;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.eventPublishingService = eventPublishingService;
        this.clock = clock;
        this.subscriptionEventService = subscriptionEventService;
        this.testNotificationService = testNotificationService;
        this.taskDispatcher = taskDispatcher;
        this.subscriptionService = subscriptionService;
        objectMapper = JacksonProvider.defaultMapper();
        subscriptionJsonMapper = Mappers.getMapper(SubscriptionJsonMapper.class);
    }

    public void scheduleNewChanges(String subscriptionId) {
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        scheduleNewChargeChanges(subscription);
        scheduleStatusChanges(subscription);
    }

    void scheduleStatusChanges(Subscription subscription) {
        scheduleStatusChanges(subscription, false);
    }

    @VisibleForTesting
    void scheduleStatusChanges(Subscription subscription, boolean isBackfill) {
        List<SubscriptionStatusChangeSchedule> previouslyProcessedSchedules = subscriptionStatusChangeScheduleDAO.getPreviouslyProcessedSchedules(
            subscription.getSubscriptionId()
        );
        Set<EventType> processedEvents = previouslyProcessedSchedules
            .stream()
            .map(SubscriptionStatusChangeSchedule::getChangeEventType)
            .collect(toSet());
        List<SubscriptionStatusChangeSchedule> schedulesToCreate = new ArrayList<>();

        Instant now = clock.instant();

        // If the subscription start date is in the future, schedule a SUBSCRIPTION_ACTIVATING and SUBSCRIPTION_ACTIVATED event for the future
        if (subscription.getStartDate().isAfter(now)) {
            schedulesToCreate.add(
                createStatusChangeSchedule(
                    subscription,
                    EventType.SUBSCRIPTION_ACTIVATING,
                    subscription.getStartDate().minus(FUTURE_EVENT_BEFORE_DURATION),
                    isBackfill
                )
            );
            schedulesToCreate.add(createStatusChangeSchedule(subscription, EventType.SUBSCRIPTION_ACTIVATED, subscription.getStartDate(), false));
        }

        // If the subscription start date is now or in the past, schedule a SUBSCRIPTION_ACTIVATED event
        // only if it has not been sent before
        if (
            (subscription.getStartDate().isBefore(now) || subscription.getStartDate().equals(now)) &&
            !processedEvents.contains(EventType.SUBSCRIPTION_ACTIVATED)
        ) {
            schedulesToCreate.add(
                createStatusChangeSchedule(subscription, EventType.SUBSCRIPTION_ACTIVATED, subscription.getStartDate(), isBackfill)
            );
        }

        if (subscription.getCanceledDate() != null) {
            // If the subscription has a cancellation date, we want to schedule cancellation events and skip expiry related events
            // If the subscription cancellation date is in the future, schedule a SUBSCRIPTION_CANCELING and SUBSCRIPTION_CANCELLED event for the future
            if (subscription.getCanceledDate().isAfter(now)) {
                schedulesToCreate.add(
                    createStatusChangeSchedule(
                        subscription,
                        EventType.SUBSCRIPTION_CANCELLING,
                        subscription.getCanceledDate().minus(FUTURE_EVENT_BEFORE_DURATION),
                        false
                    )
                );
                schedulesToCreate.add(
                    createStatusChangeSchedule(subscription, EventType.SUBSCRIPTION_CANCELLED, subscription.getCanceledDate(), false)
                );
            }

            // If the subscription cancellation date is now or in the past, schedule a SUBSCRIPTION_CANCELLED event
            // only if it has not been sent before
            if (
                (subscription.getCanceledDate().isBefore(now) || subscription.getCanceledDate().equals(now)) &&
                !processedEvents.contains(EventType.SUBSCRIPTION_CANCELLED)
            ) {
                schedulesToCreate.add(
                    createStatusChangeSchedule(subscription, EventType.SUBSCRIPTION_CANCELLED, subscription.getCanceledDate(), isBackfill)
                );
            }
        } else {
            // If the subscription has no cancellation date, we want to schedule expiry related events
            // If the subscription expiry date is in the future, schedule a SUBSCRIPTION_EXPIRING and SUBSCRIPTION_EXPIRED event for the future
            if (subscription.getEndDate().isAfter(now)) {
                schedulesToCreate.add(
                    createStatusChangeSchedule(
                        subscription,
                        EventType.SUBSCRIPTION_EXPIRING,
                        subscription.getEndDate().minus(FUTURE_EVENT_BEFORE_DURATION),
                        false
                    )
                );
                schedulesToCreate.add(createStatusChangeSchedule(subscription, EventType.SUBSCRIPTION_EXPIRED, subscription.getEndDate(), false));
            }

            // If the subscription expiry date is now or in the past, schedule a SUBSCRIPTION_EXPIRED event
            // only if it has not been sent before
            if (
                (subscription.getEndDate().isBefore(now) || subscription.getEndDate().equals(now)) &&
                !processedEvents.contains(EventType.SUBSCRIPTION_EXPIRED)
            ) {
                schedulesToCreate.add(
                    createStatusChangeSchedule(subscription, EventType.SUBSCRIPTION_EXPIRED, subscription.getEndDate(), isBackfill)
                );
            }
        }

        DSLContext tenantDslContext = dslContextProvider.get(tenantIdProvider.provideTenantIdString());

        tenantDslContext.transaction(configuration -> {
            var dsl = DSL.using(configuration);
            subscriptionStatusChangeScheduleDAO.deleteUnprocessedSchedulesForOtherVersions(
                subscription.getSubscriptionId(),
                subscription.getVersion(),
                dsl
            );
            List<SubscriptionStatusChangeSchedule> schedules = subscriptionStatusChangeScheduleDAO.createSchedules(schedulesToCreate, dsl);
            taskDispatcher.scheduleTasks(buildQueuedTaskRequestsForStatusChanges(subscription, schedules), configuration);
        });
    }

    List<QueuedTaskRequest> buildQueuedTaskRequestsForStatusChanges(
        Subscription subscription,
        List<SubscriptionStatusChangeSchedule> schedulesToCreate
    ) {
        return schedulesToCreate
            .stream()
            .map(schedule ->
                ImmutableQueuedTaskRequest.builder()
                    .module(SubscriptionChangeTaskProcessor.TASK_MODULE)
                    .type(SubscriptionChangeTaskProcessor.STATUS_CHANGE_TASK_TYPE)
                    .taskData(Objects.requireNonNull(schedule.getId()).toString())
                    .tenantId(schedule.getTenantId())
                    .entityId(subscription.getEntityId())
                    .delayedUntil(schedule.getChangesOn())
                    .parent(
                        ImmutableTaskParent.builder()
                            .parentType(SubscriptionStatusChangeSchedule.class.getSimpleName())
                            .parentId(Objects.requireNonNull(schedule.getId()).toString())
                            .build()
                    )
                    .build()
            )
            .collect(Collectors.toList());
    }

    List<QueuedTaskRequest> buildQueuedTaskRequestsForChargeChanges(
        Subscription subscription,
        List<SubscriptionChargeChangeSchedule> schedulesToCreate
    ) {
        return schedulesToCreate
            .stream()
            .map(schedule ->
                ImmutableQueuedTaskRequest.builder()
                    .module(SubscriptionChangeTaskProcessor.TASK_MODULE)
                    .type(SubscriptionChangeTaskProcessor.CHARGE_CHANGE_TASK_TYPE)
                    .taskData(Objects.requireNonNull(schedule.getId()).toString())
                    .tenantId(schedule.getTenantId())
                    .entityId(subscription.getEntityId())
                    .delayedUntil(schedule.getChangesOn())
                    .parent(
                        ImmutableTaskParent.builder()
                            .parentType(SubscriptionChargeChangeSchedule.class.getSimpleName())
                            .parentId(Objects.requireNonNull(schedule.getId()).toString())
                            .build()
                    )
                    .build()
            )
            .collect(Collectors.toList());
    }

    private SubscriptionStatusChangeSchedule createStatusChangeSchedule(
        Subscription subscription,
        EventType eventType,
        Instant changesOn,
        boolean isProcessed
    ) {
        return ImmutableSubscriptionStatusChangeSchedule.builder()
            .tenantId(tenantIdProvider.provideTenantIdString())
            .subscriptionId(subscription.getSubscriptionId())
            .subscriptionVersion(subscription.getVersion())
            .changesOn(changesOn)
            .changeEventType(eventType)
            .isProcessed(isProcessed)
            .isDeleted(false)
            .build();
    }

    @VisibleForTesting
    void scheduleNewChargeChanges(Subscription subscription) {
        List<SubscriptionCharge> chargesWithChange = getChargesForScheduleSubjectToChangeInTheFuture(subscription);

        if (chargesWithChange.isEmpty()) {
            return;
        }

        // Idempotency check - if we already have records for this version, we don't need to do anything
        if (subscriptionChargeChangeScheduleDAO.hasUnprocessedRecordsForVersion(subscription.getSubscriptionId(), subscription.getVersion())) {
            return;
        }

        List<SubscriptionChargeChangeSchedule> changeSchedules = createChangeSchedules(subscription, chargesWithChange);
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        tenantDslContext.transaction(configuration -> {
            var dsl = DSL.using(configuration);
            subscriptionChargeChangeScheduleDAO.deleteUnprocessedSchedulesForOtherVersions(
                subscription.getSubscriptionId(),
                subscription.getVersion(),
                dsl
            );
            List<SubscriptionChargeChangeSchedule> schedules = subscriptionChargeChangeScheduleDAO.createSchedules(changeSchedules, dsl);
            taskDispatcher.scheduleTasks(buildQueuedTaskRequestsForChargeChanges(subscription, schedules), configuration);
        });
    }

    // What we are doing here is essentially to group all future changes by the timestamp of the change. In most cases
    // this means some charges will be starting and some will be ending. To me, it makes sense to track them together like
    // this because they are not really independent of each other.
    private List<SubscriptionChargeChangeSchedule> createChangeSchedules(Subscription subscription, List<SubscriptionCharge> chargesToSchedule) {
        Map<Instant, List<SubscriptionCharge>> chargesStartingGroupedByDate = chargesToSchedule
            .stream()
            .collect(groupingBy(SubscriptionCharge::getStartDate));
        Map<Instant, List<SubscriptionCharge>> chargesEndingGroupedByDate = chargesToSchedule
            .stream()
            .collect(groupingBy(SubscriptionCharge::getEndDate));

        Set<Instant> uniqueStartOrEndDatesAfterNow = Stream.of(chargesStartingGroupedByDate.keySet(), chargesEndingGroupedByDate.keySet())
            .flatMap(Set::stream)
            // If changes are in the past, we don't care about them
            // we also don't care about changes that are occurring on the end date of the subscription
            .filter(instant -> instant.isAfter(clock.instant()) && !instant.equals(subscription.getEndDate()))
            .collect(toSet());

        List<SubscriptionChargeChangeSchedule> recordsToCreate = new ArrayList<>();

        for (Instant changeDate : uniqueStartOrEndDatesAfterNow) {
            String chargeIdsStarting = chargesStartingGroupedByDate
                .getOrDefault(changeDate, List.of())
                .stream()
                .map(SubscriptionCharge::getSubscriptionChargeId)
                .collect(joining(","));
            String chargeIdsEnding = chargesEndingGroupedByDate
                .getOrDefault(changeDate, List.of())
                .stream()
                .map(SubscriptionCharge::getSubscriptionChargeId)
                .collect(joining(","));
            ImmutableSubscriptionChargeChangeSchedule toCreate = ImmutableSubscriptionChargeChangeSchedule.builder()
                .changesOn(changeDate)
                .subscriptionId(subscription.getSubscriptionId())
                .subscriptionVersion(subscription.getVersion())
                .tenantId(subscription.getTenantId())
                .isDeleted(false)
                .isProcessed(false)
                .chargeIdsEnding(chargeIdsEnding)
                .chargeIdsStarting(chargeIdsStarting)
                .build();
            recordsToCreate.add(toCreate);
        }
        return Collections.unmodifiableList(recordsToCreate);
    }

    private List<SubscriptionCharge> getChargesForScheduleSubjectToChangeInTheFuture(Subscription subscription) {
        Instant now = clock.instant();
        return subscription
            .getCharges()
            .stream()
            .filter(
                charge ->
                    // If the charge is starting or ending at the same time as the subscription, we can ignore it
                    !(charge.getStartDate().equals(subscription.getStartDate()) && charge.getEndDate().equals(subscription.getEndDate())) &&
                    // If the change is in the past, we can also ignore it
                    (charge.getEndDate().isAfter(now) || charge.getStartDate().isAfter(now))
            )
            .toList();
    }

    public void processScheduledStatusChanges() {
        List<SubscriptionStatusChangeSchedule> unprocessedOverdueChanges = subscriptionStatusChangeScheduleDAO.getUnprocessedOverdueChanges();
        for (SubscriptionStatusChangeSchedule change : unprocessedOverdueChanges) {
            processStatusChange(change);
        }
    }

    void processStatusChange(SubscriptionStatusChangeSchedule change) {
        Optional<Subscription> foundSubscription = getSubscription(change.getSubscriptionId(), change.getSubscriptionVersion());
        if (foundSubscription.isEmpty()) {
            return;
        }

        Subscription subscription = foundSubscription.get();

        // Adding a safety net of one minute here to avoid issues with clock skew
        SubscriptionState state = SubscriptionStateResolver.getStateForSubscription(subscription, change.getChangesOn().plus(Duration.ofMinutes(1)));
        if (state != subscription.getState()) {
            subscriptionService.updateSubscriptionState(subscription, state);
        }

        dslContextProvider
            .get(tenantIdProvider.provideTenantIdString())
            .transaction(configuration -> {
                DSLContext transactionContext = DSL.using(configuration);
                subscriptionEventService.publishSubscriptionEvent(subscription, transactionContext, change.getChangeEventType());
                subscriptionStatusChangeScheduleDAO.markAsProcessed(change.getId(), transactionContext);
            });
    }

    public void processScheduledChargeChanges() {
        List<SubscriptionChargeChangeSchedule> unprocessedOverdueChanges = subscriptionChargeChangeScheduleDAO.getUnprocessedOverdueChanges();
        for (SubscriptionChargeChangeSchedule change : unprocessedOverdueChanges) {
            processChange(change);
        }
    }

    void processChange(SubscriptionChargeChangeSchedule change) {
        Optional<Subscription> foundSubscription = getSubscription(change.getSubscriptionId(), change.getSubscriptionVersion());

        if (foundSubscription.isEmpty()) return;

        SubscriptionChargeChange event = buildSubscriptionChargeChange(change, foundSubscription.get());

        String eventJson;
        try {
            eventJson = objectMapper.writeValueAsString(event);
        } catch (JsonProcessingException e) {
            LOGGER.error("Error serializing subscription charge change event", e);
            return;
        }
        dslContextProvider
            .get(tenantIdProvider.provideTenantIdString())
            .transaction(configuration -> {
                DSLContext transactionContext = DSL.using(configuration);
                sendEvent(event, eventJson, transactionContext);
                subscriptionChargeChangeScheduleDAO.markAsProcessed(change.getId(), transactionContext);
            });
    }

    private SubscriptionChargeChange buildSubscriptionChargeChange(SubscriptionChargeChangeSchedule change, Subscription subscription) {
        SubscriptionJson subscriptionJson = subscriptionJsonMapper.subscriptionToJson(subscription);
        List<SubscriptionChargeJson> chargesStarting = filterChargesById(subscriptionJson, change.getChargeIdsStarting());
        List<SubscriptionChargeJson> chargesEnding = filterChargesById(subscriptionJson, change.getChargeIdsEnding());

        return ImmutableSubscriptionChargeChange.builder()
            .subscription(subscriptionJson)
            .changesOn(change.getChangesOn())
            .chargesStarting(chargesStarting)
            .chargesEnding(chargesEnding)
            .build();
    }

    private Optional<Subscription> getSubscription(String subscriptionId, int expectedVersion) {
        Subscription subscription;
        try {
            subscription = subscriptionGetService.getSubscription(subscriptionId);
        } catch (ObjectNotFoundException e) {
            LOGGER.warn("Subscription {} not found for scheduled charge change. Skipping change.", subscriptionId);
            return Optional.empty();
        }

        if (subscription.getVersion() != expectedVersion) {
            LOGGER.warn(
                "Subscription version has changed for {} from {} to {} since this change was scheduled. Skipping change.",
                subscriptionId,
                expectedVersion,
                subscription.getVersion()
            );
            return Optional.empty();
        }
        return Optional.of(subscription);
    }

    private void sendEvent(SubscriptionChargeChange subscriptionChargeChange, String messageBody, DSLContext transactionContext) {
        eventPublishingService.publishEventInTransaction(
            transactionContext,
            EventType.SUBSCRIPTION_CHARGE_CHANGE,
            tenantIdProvider.provideTenantIdString(),
            subscriptionChargeChange.getSubscription().getEntityId(),
            subscriptionChargeChange.getSubscription().getAccountId(),
            messageBody.getBytes(Charset.defaultCharset())
        );
    }

    private static List<SubscriptionChargeJson> filterChargesById(SubscriptionJson subscriptionJson, String commaDelimitedChargeIds) {
        Set<String> chargeIds = Set.of(commaDelimitedChargeIds.split(","));
        return subscriptionJson.getCharges().stream().filter(charge -> chargeIds.contains(charge.getId())).toList();
    }

    public void deleteSchedulesForSubscriptionVersion(Configuration configuration, Subscription subscription) {
        DSLContext dslContext = DSL.using(configuration);
        subscriptionChargeChangeScheduleDAO.deleteSchedulesForVersion(subscription.getSubscriptionId(), subscription.getVersion(), dslContext);
    }

    public SubscriptionSchedules getSchedulesForSubscription(String subscriptionId) {
        List<SubscriptionChargeChangeSchedule> chargeChanges = subscriptionChargeChangeScheduleDAO.getChangesForSubscriptionId(subscriptionId);
        List<SubscriptionStatusChangeSchedule> statusChanges = subscriptionStatusChangeScheduleDAO.getChangesForSubscriptionId(subscriptionId);
        return ImmutableSubscriptionSchedules.builder().chargeChangeSchedules(chargeChanges).statusChangeSchedules(statusChanges).build();
    }

    public void sendTestEventsForSubscription(String subscriptionId, String notificationTargetId) {
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        List<SubscriptionChargeChangeSchedule> chargeChanges = subscriptionChargeChangeScheduleDAO.getChangesForSubscriptionId(subscriptionId);
        List<SubscriptionStatusChangeSchedule> statusChanges = subscriptionStatusChangeScheduleDAO.getChangesForSubscriptionId(subscriptionId);
        List<Event> chargeChangeEvents = chargeChanges.stream().map(schedule -> createTestChargeChangeEvent(schedule, subscription)).toList();
        List<Event> statusChangeEvents = statusChanges.stream().map(schedule -> createTestStatusChangeEvent(schedule, subscription)).toList();
        testNotificationService.sendTestNotification(
            notificationTargetId,
            Stream.concat(chargeChangeEvents.stream(), statusChangeEvents.stream()).toList()
        );
    }

    private Event createTestChargeChangeEvent(SubscriptionChargeChangeSchedule chargeChangeSchedule, Subscription subscription) {
        SubscriptionChargeChange event = buildSubscriptionChargeChange(chargeChangeSchedule, subscription);
        String eventJson = UNCHECKED_OBJECT_MAPPER.writeValueAsString(event);
        return createMockEvent(eventJson, EventType.SUBSCRIPTION_CHARGE_CHANGE);
    }

    private Event createTestStatusChangeEvent(SubscriptionStatusChangeSchedule subscriptionStatusChangeSchedule, Subscription subscription) {
        SubscriptionJson subscriptionJson = subscriptionJsonMapper.subscriptionToJson(subscription);
        return createMockEvent(UNCHECKED_OBJECT_MAPPER.writeValueAsString(subscriptionJson), subscriptionStatusChangeSchedule.getChangeEventType());
    }

    @NotNull
    private Event createMockEvent(String payload, EventType eventType) {
        return Event.builder()
            .id(UUID.randomUUID().toString())
            .tenantId(tenantIdProvider.provideTenantIdString())
            .partitionKey(new DefaultStreamPartitionKey(tenantIdProvider.provideTenantIdString()))
            .generatedTime(clock.instant())
            .type(eventType)
            .sequenceNumber(0L)
            .payload(ByteBuffer.wrap(payload.getBytes(StandardCharsets.UTF_8)))
            .status(EventStatus.STREAMED)
            .build();
    }
}
