package com.subskribe.billy.subscription.services;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.shared.task.queue.model.ImmutableProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskResult;
import com.subskribe.billy.shared.task.queue.model.Partitioned;
import com.subskribe.billy.shared.task.queue.model.ProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import com.subskribe.billy.shared.task.queue.processor.TaskProcessor;
import com.subskribe.billy.shared.task.queue.scheduler.TaskDispatcher;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.db.SubscriptionChargeChangeScheduleDAO;
import com.subskribe.billy.subscription.model.SubscriptionChargeChangeSchedule;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import javax.inject.Inject;

public class SubscriptionChargeChangeTaskBackfiller implements TaskProcessor {

    private final SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService;
    private final SubscriptionGetService subscriptionGetService;
    private final SubscriptionChargeChangeScheduleDAO subscriptionChargeChangeScheduleDAO;
    private final TaskDispatcher taskDispatcher;
    private final Clock clock;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;

    private static final String MODULE_NAME = "subscription-charge-change-backfiller";
    private static final Duration TIMEOUT = Duration.ofMinutes(5);
    private static final int BATCH_SIZE = 100;

    @Inject
    public SubscriptionChargeChangeTaskBackfiller(
        SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService,
        SubscriptionGetService subscriptionGetService,
        SubscriptionChargeChangeScheduleDAO subscriptionChargeChangeScheduleDAO,
        TaskDispatcher taskDispatcher,
        Clock clock,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider
    ) {
        this.subscriptionLifecycleScheduleService = subscriptionLifecycleScheduleService;
        this.subscriptionGetService = subscriptionGetService;
        this.subscriptionChargeChangeScheduleDAO = subscriptionChargeChangeScheduleDAO;
        this.taskDispatcher = taskDispatcher;
        this.clock = clock;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
    }

    public void dispatchNewTaskForTenant(String tenantId) {
        int randomJitterSeconds = ThreadLocalRandom.current().nextInt(10, 20);
        Instant delayUntil = clock.instant().plusSeconds(randomJitterSeconds);
        QueuedTaskRequest queuedTaskRequest = ImmutableQueuedTaskRequest.builder()
            .module(new TaskModule(MODULE_NAME))
            .type(new TaskType(MODULE_NAME))
            .taskData(tenantId)
            .tenantId(tenantId)
            .taskOrder(Partitioned.with(tenantId))
            .delayedUntil(delayUntil)
            .build();
        taskDispatcher.scheduleTask(queuedTaskRequest);
    }

    @Override
    public TaskResult process(QueuedTask task) {
        List<SubscriptionChargeChangeSchedule> changesForBackfill = subscriptionChargeChangeScheduleDAO.getUnprocessedChangesForTaskBackfill(
            BATCH_SIZE
        );

        if (changesForBackfill.isEmpty()) {
            return ImmutableTaskResult.builder().successful(true).build();
        }

        Map<String, List<SubscriptionChargeChangeSchedule>> changesBySubscriptionId = changesForBackfill
            .stream()
            .collect(Collectors.groupingBy(SubscriptionChargeChangeSchedule::getSubscriptionId));

        for (Map.Entry<String, List<SubscriptionChargeChangeSchedule>> entry : changesBySubscriptionId.entrySet()) {
            List<UUID> idList = entry.getValue().stream().map(SubscriptionChargeChangeSchedule::getId).toList();
            Subscription subscription;
            try {
                subscription = subscriptionGetService.getSubscription(entry.getKey());
            } catch (ObjectNotFoundException ignore) {
                TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider).transaction(configuration ->
                    subscriptionChargeChangeScheduleDAO.markTaskScheduled(idList, configuration)
                );
                continue;
            }
            List<QueuedTaskRequest> queuedTaskRequests = subscriptionLifecycleScheduleService.buildQueuedTaskRequestsForChargeChanges(
                subscription,
                entry.getValue()
            );
            TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider).transaction(configuration -> {
                taskDispatcher.scheduleTasks(queuedTaskRequests, configuration);
                subscriptionChargeChangeScheduleDAO.markTaskScheduled(idList, configuration);
            });
        }

        dispatchNewTaskForTenant(task.getTaskData());

        return ImmutableTaskResult.builder().successful(true).build();
    }

    @Override
    public ProcessorConfiguration getConfiguration() {
        return ImmutableProcessorConfiguration.builder().taskModule(new TaskModule(MODULE_NAME)).taskTimeout(TIMEOUT).build();
    }
}
