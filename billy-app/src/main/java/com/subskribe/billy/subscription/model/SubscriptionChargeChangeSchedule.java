package com.subskribe.billy.subscription.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import java.time.Instant;
import java.util.UUID;
import javax.annotation.Nullable;
import org.immutables.value.Value;

@Value.Immutable
@BillyModelStyle
@JsonDeserialize(as = ImmutableSubscriptionChargeChangeSchedule.class)
public interface SubscriptionChargeChangeSchedule {
    @Nullable
    UUID getId();

    String getTenantId();

    String getSubscriptionId();

    Integer getSubscriptionVersion();

    Instant getChangesOn();

    String getChargeIdsStarting();

    String getChargeIdsEnding();

    Boolean getIsProcessed();

    Boolean getIsDeleted();

    @Nullable
    Instant getCreatedOn();

    @Nullable
    Instant getUpdatedOn();
}
