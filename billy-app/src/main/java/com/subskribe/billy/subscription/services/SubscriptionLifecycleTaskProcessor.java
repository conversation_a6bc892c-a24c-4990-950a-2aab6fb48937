package com.subskribe.billy.subscription.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.TenantAccountPartitionKey;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.resources.json.subscription.SubscriptionJson;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.task.queue.event.OrderPreservingEventTaskFactory;
import com.subskribe.billy.shared.task.queue.model.ImmutableProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskResult;
import com.subskribe.billy.shared.task.queue.model.Partitioned;
import com.subskribe.billy.shared.task.queue.model.ProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import com.subskribe.billy.shared.task.queue.processor.TaskProcessor;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Duration;
import java.util.Set;
import javax.inject.Inject;

public class SubscriptionLifecycleTaskProcessor extends OrderPreservingEventTaskFactory implements TaskProcessor {

    private static final TaskModule TASK_MODULE = new TaskModule("subscription-lifecycle");
    private static final TaskType SCHEDULE_TASK_TYPE = new TaskType("schedule-new-subscription-events");
    private static final Set<EventType> SUPPORTED_EVENT_TYPES = Set.of(EventType.SUBSCRIPTION_CREATED);
    private static final Duration TIMEOUT = Duration.ofMinutes(2);
    private static final Logger LOGGER = LoggerFactory.getLogger(SubscriptionLifecycleTaskProcessor.class);

    private final ObjectMapper objectMapper = JacksonProvider.defaultMapper();

    private final TenantIdProvider tenantIdProvider;
    private final SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService;

    @Inject
    public SubscriptionLifecycleTaskProcessor(
        TenantIdProvider tenantIdProvider,
        SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService
    ) {
        super(tenantIdProvider);
        this.tenantIdProvider = tenantIdProvider;
        this.subscriptionLifecycleScheduleService = subscriptionLifecycleScheduleService;
    }

    @Override
    public QueuedTaskRequest createTaskForEvent(Event event) {
        validateEventType(event);

        SubscriptionJson subscriptionJson = getSubscriptionFromEventPayload(event.getPayloadAsString());

        if (event.getType() == EventType.SUBSCRIPTION_CREATED) {
            TenantAccountPartitionKey tenantAccountPartitionKey = TenantAccountPartitionKey.builder()
                .withTenantId(tenantIdProvider.provideTenantIdString())
                .withAccountId(subscriptionJson.getAccountId())
                .build();
            return ImmutableQueuedTaskRequest.builder()
                .module(TASK_MODULE)
                .type(SCHEDULE_TASK_TYPE)
                .taskOrder(Partitioned.with(tenantAccountPartitionKey.getKey()))
                .taskData(subscriptionJson.getId())
                .tenantId(tenantIdProvider.provideTenantIdString())
                .entityId(subscriptionJson.getEntityId())
                .build();
        }

        throw new InvalidInputException("Unsupported event type for task creation: " + event.getType());
    }

    @Override
    public Set<EventType> getEventTypes() {
        return SUPPORTED_EVENT_TYPES;
    }

    @Override
    public TaskResult process(QueuedTask task) {
        if (!task.getType().equals(SCHEDULE_TASK_TYPE)) {
            LOGGER.info("Skipping task with unsupported type: {}", task.getType());
            return ImmutableTaskResult.builder().successful(false).build();
        }

        subscriptionLifecycleScheduleService.scheduleNewChanges(task.getTaskData());

        return ImmutableTaskResult.builder().successful(true).build();
    }

    @Override
    public ProcessorConfiguration getConfiguration() {
        return ImmutableProcessorConfiguration.builder().taskModule(TASK_MODULE).taskTimeout(TIMEOUT).build();
    }

    private void validateEventType(Event event) {
        if (!SUPPORTED_EVENT_TYPES.contains(event.getType())) {
            throw new InvalidInputException("Unsupported event type: " + event.getType());
        }
    }

    private SubscriptionJson getSubscriptionFromEventPayload(String eventPayload) {
        try {
            return objectMapper.readValue(eventPayload, SubscriptionJson.class);
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Failed to parse subscription from event payload", e);
        }
    }
}
