package com.subskribe.billy.subscription.db;

import static com.subskribe.billy.jooq.default_schema.tables.Subscription.SUBSCRIPTION;
import static com.subskribe.billy.jooq.default_schema.tables.SubscriptionBillingPeriod.SUBSCRIPTION_BILLING_PERIOD;
import static com.subskribe.billy.jooq.default_schema.tables.SubscriptionCharge.SUBSCRIPTION_CHARGE;
import static com.subskribe.billy.jooq.default_schema.tables.SubscriptionChargeMap.SUBSCRIPTION_CHARGE_MAP;
import static com.subskribe.billy.jooq.default_schema.tables.SubscriptionMetrics.SUBSCRIPTION_METRICS;
import static org.jooq.impl.DSL.max;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.model.ProrationOptions;
import com.subskribe.billy.jooq.default_schema.tables.records.SubscriptionChargeRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.SubscriptionMetricsRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.SubscriptionRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.invariants.InvariantChecker;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.shared.traits.VersionReference;
import com.subskribe.billy.shared.traits.VersionReference.ReferenceType;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.model.SubscriptionBillingPeriodMapper;
import com.subskribe.billy.subscription.model.SubscriptionBillingPeriodRecord;
import com.subskribe.billy.subscription.model.SubscriptionChargeEntity;
import com.subskribe.billy.subscription.model.SubscriptionChargeMapEntity;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.model.SubscriptionMapper;
import com.subskribe.billy.subscription.model.SubscriptionMetrics;
import com.subskribe.billy.subscription.model.SubscriptionMetricsMapper;
import com.subskribe.billy.subscription.model.SubscriptionState;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record2;
import org.jooq.Record3;
import org.jooq.TableField;
import org.mapstruct.factory.Mappers;

public class SubscriptionDAO {

    private static final String UNIQUE_EXTERNAL_ID_CONSTRAINT = "index_subscription_external_id";

    private static final Logger LOGGER = LoggerFactory.getLogger(SubscriptionDAO.class);

    private final SubscriptionMapper subscriptionMapper;
    private final SubscriptionBillingPeriodMapper subscriptionBillingPeriodMapper;
    private final SubscriptionMetricsMapper subscriptionMetricsMapper;

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    @Inject
    public SubscriptionDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        subscriptionMapper = Mappers.getMapper(SubscriptionMapper.class);
        subscriptionBillingPeriodMapper = Mappers.getMapper(SubscriptionBillingPeriodMapper.class);
        subscriptionMetricsMapper = Mappers.getMapper(SubscriptionMetricsMapper.class);
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
    }

    public void addSubscription(
        DSLContext dslContext,
        SubscriptionEntity se,
        List<SubscriptionChargeEntity> charges,
        List<SubscriptionChargeMapEntity> chargesMap
    ) {
        var localTime = LocalDateTime.now(ZoneOffset.UTC);
        var sr = subscriptionMapper.subscriptionEntityToRecord(se);
        sr.setCreationTime(localTime);
        dslContext.insertInto(SUBSCRIPTION).set(sr).execute();

        addSubscriptionCharges(dslContext, charges);
        addSubscriptionChargesMap(dslContext, chargesMap);

        // update renewed to of previous subscription
        if (StringUtils.isNotBlank(se.getRenewedFromSubscriptionId())) {
            updateSubscriptionWithRenewedToDetails(
                dslContext,
                false,
                se.getRenewedFromSubscriptionId(),
                se.getSubscriptionId(),
                se.getRenewedFromDate()
            );
        }

        // update restructured to subscription detail
        if (StringUtils.isNotBlank(se.getRestructuredFromSubscriptionId())) {
            updateSubscriptionWithRestructuredToDetails(
                dslContext,
                false,
                se.getRestructuredFromSubscriptionId(),
                se.getSubscriptionId(),
                se.getRestructuredFromDate()
            );
        }
    }

    private void updateSubscriptionWithRenewedToDetails(
        DSLContext dslContext,
        boolean updateAllVersions,
        String subscriptionId,
        String subscriptionRenewedTo,
        Instant renewedToDate
    ) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .orderBy(SUBSCRIPTION.VERSION.desc())
            .limit(1)
            .fetchOneInto(SubscriptionRecord.class);
        if (record == null) {
            throw new IllegalStateException("Couldn't find the subscription id: " + subscriptionId);
        }

        var updateStatement = dslContext
            .update(SUBSCRIPTION)
            .set(SUBSCRIPTION.RENEWED_TO_SUBSCRIPTION_ID, subscriptionRenewedTo)
            .set(SUBSCRIPTION.RENEWED_TO_DATE, subscriptionMapper.toLocalDateTime(renewedToDate))
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId));

        if (updateAllVersions) {
            updateStatement.and(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionId)).execute();
        } else {
            updateStatement.and(SUBSCRIPTION.ID.eq(record.getId())).execute();
        }
    }

    private void updateSubscriptionWithRestructuredToDetails(
        DSLContext dslContext,
        boolean updateAllVersions,
        String subscriptionId,
        String subscriptionRestructuredTo,
        Instant restructuredToDate
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        SubscriptionRecord record = dslContext
            .select()
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .orderBy(SUBSCRIPTION.VERSION.desc())
            .limit(1)
            .fetchOneInto(SubscriptionRecord.class);
        if (record == null) {
            throw new IllegalStateException("Couldn't find the subscription id: " + subscriptionId);
        }

        var updateStatement = dslContext
            .update(SUBSCRIPTION)
            .set(SUBSCRIPTION.RESTRUCTURED_TO_SUBSCRIPTION_ID, subscriptionRestructuredTo)
            .set(SUBSCRIPTION.RESTRUCTURED_TO_DATE, subscriptionMapper.toLocalDateTime(restructuredToDate))
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId));

        if (updateAllVersions) {
            updateStatement.and(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionId)).execute();
        } else {
            updateStatement.and(SUBSCRIPTION.ID.eq(record.getId())).execute();
        }
    }

    private void addSubscriptionCharges(DSLContext dslContext, List<SubscriptionChargeEntity> charges) {
        if (CollectionUtils.isEmpty(charges)) {
            return;
        }

        var records = subscriptionMapper.subscriptionChargeListToRecords(charges);
        dslContext.batchInsert(records).execute();
    }

    private void addSubscriptionChargesMap(DSLContext dslContext, List<SubscriptionChargeMapEntity> subscriptionChargesMap) {
        if (CollectionUtils.isEmpty(subscriptionChargesMap)) {
            return;
        }

        var records = subscriptionMapper.subscriptionChargeMapListToRecords(subscriptionChargesMap);
        records.forEach(r -> r.reset(SUBSCRIPTION_CHARGE_MAP.ID));
        dslContext.batchInsert(records).execute();
    }

    public void updateSubscriptionAttributes(DSLContext dslContext, SubscriptionEntity subscription) {
        Validator.validateNonNullArguments(subscription);
        Validator.validateNonNullArgument(subscription.getId(), "subscriptionId");

        String tenantId = tenantIdProvider.provideTenantIdString();
        PostgresErrorHandler.withConstraintAsConflict(
            () ->
                dslContext
                    .update(SUBSCRIPTION)
                    .set(SUBSCRIPTION.SHIPPING_CONTACT_ID, subscription.getShippingContactId())
                    .set(SUBSCRIPTION.BILLING_CONTACT_ID, subscription.getBillingContactId())
                    .set(SUBSCRIPTION.PURCHASE_ORDERS, subscriptionMapper.serializePurchaseOrders(subscription.getPurchaseOrders()))
                    .set(SUBSCRIPTION.PO_REQUIRED_FOR_INVOICING, subscription.getPurchaseOrderRequiredForInvoicing())
                    .set(SUBSCRIPTION.EXTERNAL_ID, subscription.getExternalId())
                    .set(SUBSCRIPTION.AUTO_RENEW, subscription.getAutoRenew())
                    .set(SUBSCRIPTION.ACTIVATION_DATE, DateTimeConverter.instantToLocalDateTime(subscription.getActivationDate()))
                    .set(SUBSCRIPTION.NAME, subscription.getName())
                    .where(SUBSCRIPTION.ID.eq(subscription.getId()))
                    .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
                    .execute(),
            UNIQUE_EXTERNAL_ID_CONSTRAINT,
            String.format("Subscription exists with external ID: %s", subscription.getExternalId())
        );
    }

    public void updateRenewalOpportunity(UUID subscriptionUuid, String renewalOpportunityCrmId) {
        Validator.validateNonNullArguments(subscriptionUuid);
        Validator.validateNonNullArguments(renewalOpportunityCrmId);
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        dslContext
            .update(SUBSCRIPTION)
            .set(SUBSCRIPTION.RENEWAL_OPPORTUNITY_CRM_ID, renewalOpportunityCrmId)
            .where(SUBSCRIPTION.ID.eq(subscriptionUuid))
            .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .execute();
    }

    public boolean contactAssociatedWithSubscription(DSLContext dslContext, String contactId) {
        Objects.requireNonNull(contactId, "contactId is required");
        var tenantId = tenantIdProvider.provideTenantIdString();
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(SUBSCRIPTION)
                .where(SUBSCRIPTION.SHIPPING_CONTACT_ID.eq(contactId).or(SUBSCRIPTION.BILLING_CONTACT_ID.eq(contactId)))
                .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
                .and(SUBSCRIPTION.IS_DELETED.isFalse())
        );
    }

    public SubscriptionEntity getSubscription(DSLContext dslContext, VersionReference<String> subscriptionRef) {
        Objects.requireNonNull(subscriptionRef, "Subscription reference must be specified");
        var tenantId = tenantIdProvider.provideTenantIdString();
        SubscriptionRecord record;
        if (subscriptionRef.getReferenceType() instanceof ReferenceType.Pinned) {
            int version = ((ReferenceType.Pinned) subscriptionRef.getReferenceType()).getVersion();
            record = dslContext
                .selectFrom(SUBSCRIPTION)
                .where(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionRef.getId()))
                .and(SUBSCRIPTION.VERSION.le(version))
                .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
                .and(SUBSCRIPTION.IS_DELETED.isFalse())
                .orderBy(SUBSCRIPTION.VERSION.desc())
                .limit(1)
                .fetchOne();
        } else if (
            subscriptionRef.getReferenceType() instanceof ReferenceType.Current ||
            subscriptionRef.getReferenceType() instanceof ReferenceType.PrivateCopy
        ) {
            // TODO: For subscriptions, , private_copy may not apply, and may mean latest.
            // Revisit.
            record = dslContext
                .selectFrom(SUBSCRIPTION)
                .where(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionRef.getId()))
                .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
                .and(SUBSCRIPTION.IS_DELETED.isFalse())
                .orderBy(SUBSCRIPTION.VERSION.desc())
                .limit(1)
                .fetchOne();
        } else {
            var errorMassage = String.format("Unknown reference type: %s", subscriptionRef.getReferenceType().getClass().getName());
            LOGGER.error(errorMassage);
            throw new IllegalArgumentException(errorMassage);
        }

        return Optional.ofNullable(record)
            .map(subscriptionMapper::recordToSubscriptionEntity)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.SUBSCRIPTION, subscriptionRef.serialize()));
    }

    public SubscriptionEntity getSubscriptionAt(DSLContext dslContext, String subscriptionId, Instant time) {
        Objects.requireNonNull(dslContext, "tenantId reference must be specified");
        Objects.requireNonNull(subscriptionId, "Subscription reference must be specified");
        Objects.requireNonNull(time, "time reference must be specified");

        LocalDateTime utcLocal = LocalDateTime.ofInstant(time, ZoneOffset.UTC);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .selectFrom(SUBSCRIPTION)
            .where(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.VERSION_START.isNull().or(SUBSCRIPTION.VERSION_START.lessOrEqual(utcLocal)))
            .and(SUBSCRIPTION.VERSION_END.isNull().or(SUBSCRIPTION.VERSION_END.greaterThan(utcLocal)))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record)
            .map(subscriptionMapper::recordToSubscriptionEntity)
            .orElseThrow(() -> {
                LOGGER.error("No such subscription {}:{}", subscriptionId, time);
                return new ObjectNotFoundException(BillyObjectType.SUBSCRIPTION, subscriptionId);
            });
    }

    public List<SubscriptionCharge> getSubscriptionCharges(DSLContext dslContext, UUID subscriptionUuid) {
        Objects.requireNonNull(dslContext, "dslContext reference must be specified");
        Objects.requireNonNull(subscriptionUuid, "subscriptionId must be specified");

        String tenantId = tenantIdProvider.provideTenantIdString();

        var records = dslContext
            .select()
            .from(SUBSCRIPTION_CHARGE)
            .join(SUBSCRIPTION_CHARGE_MAP)
            .on(
                SUBSCRIPTION_CHARGE_MAP.SUBSCRIPTION_CHARGE_GROUP_ID.eq(SUBSCRIPTION_CHARGE.SUBSCRIPTION_CHARGE_GROUP_ID)
                    .and(SUBSCRIPTION_CHARGE_MAP.VERSION.eq(SUBSCRIPTION_CHARGE.VERSION))
                    .and(SUBSCRIPTION_CHARGE_MAP.SUBSCRIPTION_ID.eq(subscriptionUuid))
                    .and(SUBSCRIPTION_CHARGE_MAP.TENANT_ID.eq(tenantId))
                    .and(SUBSCRIPTION_CHARGE_MAP.IS_DELETED.isFalse())
            )
            .where(SUBSCRIPTION_CHARGE.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION_CHARGE.IS_DELETED.isFalse())
            .orderBy(SUBSCRIPTION_CHARGE.SUBSCRIPTION_CHARGE_GROUP_ID, SUBSCRIPTION_CHARGE.START_DATE)
            .fetchInto(SubscriptionChargeRecord.class);
        return subscriptionMapper.recordsToSubscriptionChargeList(records);
    }

    public List<BillingPeriod> getSubscriptionBillingPeriods(String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId must be specified");

        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var records = Optional.of(
            dslContext
                .select()
                .from(SUBSCRIPTION_BILLING_PERIOD)
                .where(SUBSCRIPTION_BILLING_PERIOD.TENANT_ID.eq(tenantId))
                .and(SUBSCRIPTION_BILLING_PERIOD.SUBSCRIPTION_ID.eq(subscriptionId))
                .orderBy(SUBSCRIPTION_BILLING_PERIOD.PERIOD_START)
                .fetchInto(SubscriptionBillingPeriodRecord.class)
        ).orElse(Collections.emptyList());

        return subscriptionBillingPeriodMapper.recordsToBillingPeriods(records);
    }

    public void setSubscriptionBillingPeriods(String subscriptionId, List<BillingPeriod> periods) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId must be specified");
        Validator.validateCollectionNotEmpty(periods, "periods");

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var count = dslContext
            .insertInto(SUBSCRIPTION_BILLING_PERIOD)
            .values(subscriptionBillingPeriodMapper.billingPeriodsToRecords(subscriptionId, tenantId, Instant.now(), periods))
            .returning()
            .stream()
            .count();

        Validator.checkStateInternal(periods.size() == count, "number of records inserted does not equal collection size");
    }

    public boolean billingPeriodsExist(String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId must be specified");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        Integer count = dslContext
            .selectCount()
            .from(SUBSCRIPTION_BILLING_PERIOD)
            .where(SUBSCRIPTION_BILLING_PERIOD.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION_BILLING_PERIOD.TENANT_ID.eq(tenantId))
            .fetchOne(0, Integer.class);

        return count != null && count > 0;
    }

    public void updateSubscriptionVersionEnd(DSLContext dslContext, Subscription subscription, String orderId, Instant versionEndTime) {
        Objects.requireNonNull(dslContext, "dslContext reference must be specified");
        Objects.requireNonNull(subscription, "subscription must be specified");
        Objects.requireNonNull(orderId, "orderId must be specified");
        Objects.requireNonNull(versionEndTime, "versionEndTime must be specified");

        String tenantId = tenantIdProvider.provideTenantIdString();
        var ordersSet = new HashSet<>(subscription.getOrders());
        ordersSet.add(orderId);
        dslContext
            .update(SUBSCRIPTION)
            .set(SUBSCRIPTION.VERSION_END, subscriptionMapper.toLocalDateTime(versionEndTime))
            .set(SUBSCRIPTION.ORDERS, subscriptionMapper.setIdsToString(ordersSet))
            .where(SUBSCRIPTION.ID.eq(subscription.getId()))
            .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .execute();
    }

    public void updateSubscriptionChargesVersionEnd(
        DSLContext dslContext,
        Subscription subscription,
        Map<UUID, String> chargeOrderLineMap,
        Instant versionEndTime
    ) {
        Objects.requireNonNull(dslContext, "dslContext reference must be specified");
        Objects.requireNonNull(subscription, "subscription must be specified");
        Objects.requireNonNull(chargeOrderLineMap, "orderId must be specified");
        Objects.requireNonNull(versionEndTime, "versionEndTime must be specified");

        var chargesToUpdate = subscription
            .getCharges()
            .stream()
            .map(SubscriptionCharge::getId)
            .filter(chargeOrderLineMap::containsKey)
            .collect(Collectors.toList());

        String tenantId = tenantIdProvider.provideTenantIdString();
        var updateRecords = dslContext
            .select()
            .from(SUBSCRIPTION_CHARGE)
            .where(SUBSCRIPTION_CHARGE.ID.in(chargesToUpdate))
            .and(SUBSCRIPTION_CHARGE.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION_CHARGE.IS_DELETED.isFalse())
            .fetchInto(SubscriptionChargeRecord.class);

        updateRecords.forEach(c -> updateChargeOrderLineAndVersionTime(c, chargeOrderLineMap.get(c.getId()), versionEndTime));
        dslContext.batchUpdate(updateRecords).execute();
    }

    private void updateChargeOrderLineAndVersionTime(SubscriptionChargeRecord chargeRecord, String orderLineId, Instant versionEndTime) {
        if (StringUtils.isNotBlank(orderLineId)) {
            var orderLines = subscriptionMapper.stringToSetIds(chargeRecord.getOrderLines());
            orderLines.add(orderLineId);
            chargeRecord.setOrderLines(subscriptionMapper.setIdsToString(orderLines));
        }

        chargeRecord.setVersionEnd(subscriptionMapper.toLocalDateTime(versionEndTime));
    }

    public void ensureUniqueSubscriptionId(DSLContext dslContext, String subscriptionId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .fetchOneInto(SubscriptionRecord.class);

        if (record == null) {
            return;
        }

        throwDuplicateSubscriptionIdException(subscriptionId);
    }

    public PageResult<List<String>, String> getSubscriptions(PageRequest<String> pageRequest, String filterAccountId) {
        Validator.checkNonNullInternal(pageRequest, "pageRequest cannot be null");

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var selectConditionStep = dslContext
            .select(SUBSCRIPTION.SUBSCRIPTION_ID)
            .distinctOn(SUBSCRIPTION.SUBSCRIPTION_ID)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse());

        if (StringUtils.isNotBlank(filterAccountId)) {
            selectConditionStep = selectConditionStep.and(SUBSCRIPTION.ACCOUNT_ID.eq(filterAccountId));
        }

        var seekStep = selectConditionStep.orderBy(SUBSCRIPTION.SUBSCRIPTION_ID);

        List<String> subscriptionIds;
        if (pageRequest.getPageToken() != null) {
            subscriptionIds = seekStep.seek(pageRequest.getPageToken()).limit(pageRequest.getLimit()).fetchInto(String.class);
        } else {
            subscriptionIds = seekStep.limit(pageRequest.getLimit()).fetchInto(String.class);
        }

        return PageResult.fromCollectionAndRequest(subscriptionIds, pageRequest, Function.identity());
    }

    /**
     * fetch all subscriptions in pages without any canonical/stable sort order, please note that the sort order
     * is based on subscription id
     * if new subscriptions are added while paginating, there are two cases
     *  - if the sub id has lower sort order than the current page token then next iteration will fetch it
     *  - if the sub id has higher sort order then the current page token then future page calls will include it
     *  hence, the "Fuzzy" in the method name
     *  -- CAUTION --
     *  do not use this method for UI displays or where stable sort order matters this is only for full iteration
     *  of a query space in fuzzy fashion; typically used by workflows/jobs that frequently query the key space
     *
     * @param pageRequest page request containing page token and limit (cannot be null)
     * @return page result containing page result and next page token, will NEVER be null
     */
    public PageResult<List<String>, String> getAllSubscriptionsWithFuzzyPagination(PageRequest<String> pageRequest, Set<String> excludedAccountIds) {
        Validator.checkNonNullInternal(pageRequest, "pageRequest cannot be null");
        Validator.checkNonNullInternal(excludedAccountIds, "excludedAccountIds cannot be null");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var selectSeekStep = dslContext
            .select(SUBSCRIPTION.SUBSCRIPTION_ID, SUBSCRIPTION.ACCOUNT_ID)
            .distinctOn(SUBSCRIPTION.SUBSCRIPTION_ID)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .orderBy(SUBSCRIPTION.SUBSCRIPTION_ID);

        List<SubscriptionRecord> subscriptions;
        if (pageRequest.getPageToken() != null) {
            subscriptions = selectSeekStep.seek(pageRequest.getPageToken()).limit(pageRequest.getLimit()).fetchInto(SubscriptionRecord.class);
        } else {
            subscriptions = selectSeekStep.limit(pageRequest.getLimit()).fetchInto(SubscriptionRecord.class);
        }

        List<String> filteredSubscriptionIds = subscriptions
            .stream()
            .filter(subscription -> !excludedAccountIds.contains(subscription.getAccountId()))
            .map(SubscriptionRecord::getSubscriptionId)
            .toList();

        if (subscriptions.size() < pageRequest.getLimit()) {
            return PageResult.withResultAndNextToken(filteredSubscriptionIds, null);
        }
        return PageResult.withResultAndNextToken(filteredSubscriptionIds, subscriptions.get(subscriptions.size() - 1).getSubscriptionId());
    }

    public Set<String> getAllSubscriptionIdsForTenant(Set<String> excludedAccountIds, Instant startDate) {
        Validator.checkNonNullInternal(excludedAccountIds, "excludedAccountIds cannot be null");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var result = dslContext
            .select(SUBSCRIPTION.SUBSCRIPTION_ID, max(SUBSCRIPTION.VERSION))
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.START_DATE.le(DateTimeConverter.instantToLocalDateTime(startDate)))
            .and(SUBSCRIPTION.END_DATE.gt(DateTimeConverter.instantToLocalDateTime(startDate)))
            .and(SUBSCRIPTION.ACCOUNT_ID.notIn(excludedAccountIds))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .groupBy(SUBSCRIPTION.SUBSCRIPTION_ID)
            .fetch();

        return result.stream().map(Record2::value1).collect(Collectors.toSet());
    }

    //TODO: at what point do we add pagination to this call ?
    //TODO: at some point fetching all subscriptions will become onerous, how long should we hang on this cliff ?
    // todo: might need a grace period after a subscription ends
    public Set<String> getAllSubscriptionsActiveAsOf(Instant targetDate) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        // there is index on tenant_id and end_date
        var result = dslContext
            .select(SUBSCRIPTION.SUBSCRIPTION_ID)
            .distinctOn(SUBSCRIPTION.SUBSCRIPTION_ID)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            // Make sure periods SUB.END_DATE falls on or after the given target date
            .and(SUBSCRIPTION.END_DATE.greaterOrEqual(DateTimeConverter.instantToLocalDateTime(targetDate)))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            // fetchInto never returns null
            .fetchInto(String.class);
        return new HashSet<>(result);
    }

    public Set<String> getUpdatableSubscriptionsByAccountId(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var result = dslContext
            .select(SUBSCRIPTION.SUBSCRIPTION_ID)
            .distinctOn(SUBSCRIPTION.SUBSCRIPTION_ID)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.ACCOUNT_ID.eq(accountId))
            .and(
                SUBSCRIPTION.STATE.eq(SubscriptionState.ACTIVE.name())
                    .or(SUBSCRIPTION.STATE.eq(SubscriptionState.EXPIRED.name()))
                    .or(SUBSCRIPTION.STATE.eq(SubscriptionState.PENDING.name()))
            )
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .fetchInto(String.class);
        return new HashSet<>(result);
    }

    public Set<String> getAllSubscriptionsInAccountOrderedByStartDateAsc(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var result = dslContext
            .select(SUBSCRIPTION.SUBSCRIPTION_ID)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.ACCOUNT_ID.eq(accountId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .orderBy(SUBSCRIPTION.START_DATE.asc())
            .fetchInto(String.class);
        return new LinkedHashSet<>(result);
    }

    public Instant getEarliestDayOfASubscriptionInAccount(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<LocalDateTime> localDateTime = dslContext
            .select(SUBSCRIPTION.START_DATE)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.ACCOUNT_ID.eq(accountId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .orderBy(SUBSCRIPTION.START_DATE.asc())
            .limit(1)
            .fetchInto(LocalDateTime.class);
        if (CollectionUtils.isEmpty(localDateTime)) {
            return null;
        }

        return subscriptionMapper.fromLocalDateTime(localDateTime.get(0));
    }

    public Instant getLatestDayOfASubscriptionInAccount(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<LocalDateTime> localDateTime = dslContext
            .select(SUBSCRIPTION.END_DATE)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.ACCOUNT_ID.eq(accountId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .orderBy(SUBSCRIPTION.END_DATE.desc())
            .limit(1)
            .fetchInto(LocalDateTime.class);
        if (CollectionUtils.isEmpty(localDateTime)) {
            return null;
        }

        return subscriptionMapper.fromLocalDateTime(localDateTime.get(0));
    }

    public Set<String> getAllSubscriptionIdsInPeriodForTenant(Period period) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        LocalDateTime periodStartDate = period.getStart().atZone(ZoneOffset.UTC).toLocalDateTime();
        LocalDateTime periodEndDate = period.getEnd().atZone(ZoneOffset.UTC).toLocalDateTime();
        var result = dslContext
            .select(SUBSCRIPTION.SUBSCRIPTION_ID, max(SUBSCRIPTION.VERSION))
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            // Make sure periods [SUB.START_DATE, SUB.END_DATE] and [periodStartDate, periodEndDate] overlap
            // For this we need: SUB.START_DATE <= periodEndDate AND periodStartDate <= SUB.END_DATE
            .and(SUBSCRIPTION.END_DATE.greaterOrEqual(periodStartDate).and(SUBSCRIPTION.START_DATE.lessOrEqual(periodEndDate)))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .groupBy(SUBSCRIPTION.SUBSCRIPTION_ID)
            .fetch();

        return result.stream().map(Record2::value1).collect(Collectors.toSet());
    }

    public Set<SubscriptionEntity> getAllRecentlyUpdatedSubscriptionsForTenant(int minutesBeforeNow) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        LocalDateTime lastRunTime = LocalDateTime.now(ZoneOffset.UTC).minusMinutes(minutesBeforeNow);
        var result = dslContext
            .select(
                SUBSCRIPTION.ID,
                SUBSCRIPTION.SUBSCRIPTION_ID,
                SUBSCRIPTION.START_DATE,
                SUBSCRIPTION.END_DATE,
                SUBSCRIPTION.CANCELED_DATE,
                SUBSCRIPTION.STATE
            )
            .distinctOn(SUBSCRIPTION.SUBSCRIPTION_ID)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.UPDATED_ON.gt(lastRunTime))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .orderBy(SUBSCRIPTION.SUBSCRIPTION_ID, SUBSCRIPTION.VERSION.desc())
            .fetchInto(SubscriptionRecord.class);

        return result.stream().map(subscriptionMapper::recordToSubscriptionEntity).collect(Collectors.toSet());
    }

    public Set<SubscriptionEntity> getAllSubscriptionsWhichNeedStatusUpdateForTenant() {
        Set<SubscriptionEntity> activeWithPendingStatus = getAllActiveSubscriptionsWithPendingStatusForTenant();
        Set<SubscriptionEntity> cancelledWithPendingCancellationStatus = getAllCancelledSubscriptionsWithPendingCancellationStatusForTenant();
        Set<SubscriptionEntity> expiredWithActiveStatus = getAllExpiredSubscriptionsWithActiveStatusForTenant();
        return Stream.of(activeWithPendingStatus, cancelledWithPendingCancellationStatus, expiredWithActiveStatus)
            .flatMap(Set::stream)
            .collect(Collectors.toSet());
    }

    public Set<SubscriptionEntity> getAllSubscriptionsWhichNeedStatusUpdateForTenant(LocalDateTime compareTime) {
        Set<SubscriptionEntity> activeWithPendingStatus = getAllActiveSubscriptionsWithPendingStatusForTenant(compareTime);
        Set<SubscriptionEntity> cancelledWithPendingCancellationStatus = getAllCancelledSubscriptionsWithPendingCancellationStatusForTenant(
            compareTime
        );
        Set<SubscriptionEntity> expiredWithActiveStatus = getAllExpiredSubscriptionsWithActiveStatusForTenant(compareTime);
        return Stream.of(activeWithPendingStatus, cancelledWithPendingCancellationStatus, expiredWithActiveStatus)
            .flatMap(Set::stream)
            .collect(Collectors.toSet());
    }

    public List<Record3<UUID, String, UUID>> getSubscriptionIdsForMetricsUpdateLocked(DSLContext txnDslContext, String tenantId, int limit) {
        com.subskribe.billy.jooq.default_schema.tables.Subscription sub = SUBSCRIPTION.as("sub");
        com.subskribe.billy.jooq.default_schema.tables.SubscriptionMetrics metrics = SUBSCRIPTION_METRICS.as("metrics");

        return txnDslContext
            .select(sub.ID, sub.SUBSCRIPTION_ID, metrics.ID)
            .from(sub)
            .leftOuterJoin(metrics)
            .on(sub.ID.eq(metrics.ID).and(metrics.TENANT_ID.eq(tenantId)))
            .where(sub.TENANT_ID.eq(tenantId))
            .and(metrics.TENANT_ID.eq(tenantId).or(metrics.TENANT_ID.isNull()))
            .and(metrics.METRICS.isNull().or("(metrics.metrics->>'recompute')::boolean = true"))
            .and(sub.IS_DELETED.isFalse())
            .limit(limit)
            .forUpdate()
            .of(sub) // All metrics related updates need to lock rows on the outer side of the join.
            .skipLocked()
            .fetch();
    }

    public void updateSubscriptionMetrics(DSLContext txnDslContext, String tenantId, UUID id, String subscriptionId, UUID metricsId, JSONB metrics) {
        if (metricsId == null) { // Record does not exist, add under lock
            txnDslContext
                .insertInto(SUBSCRIPTION_METRICS)
                .columns(SUBSCRIPTION_METRICS.ID, SUBSCRIPTION_METRICS.TENANT_ID, SUBSCRIPTION_METRICS.SUBSCRIPTION_ID, SUBSCRIPTION_METRICS.METRICS)
                .values(id, tenantId, subscriptionId, metrics)
                .execute();
        } else {
            txnDslContext
                .update(SUBSCRIPTION_METRICS)
                .set(SUBSCRIPTION_METRICS.METRICS, metrics)
                .where(SUBSCRIPTION_METRICS.TENANT_ID.eq(tenantId))
                .and(SUBSCRIPTION_METRICS.ID.eq(id))
                .execute();
        }
    }

    public List<Record3<UUID, String, UUID>> getSubscriptionIdsForInvoicePreviewsLocked(DSLContext txnDslContext, String tenantId, int limit) {
        com.subskribe.billy.jooq.default_schema.tables.Subscription sub = SUBSCRIPTION.as("sub");
        com.subskribe.billy.jooq.default_schema.tables.SubscriptionMetrics metrics = SUBSCRIPTION_METRICS.as("metrics");

        return txnDslContext
            .select(sub.ID, sub.SUBSCRIPTION_ID, metrics.ID)
            .from(sub)
            .leftOuterJoin(metrics)
            .on(sub.ID.eq(metrics.ID).and(metrics.TENANT_ID.eq(tenantId)))
            .where(sub.TENANT_ID.eq(tenantId))
            .and(metrics.ID.isNull().or(metrics.RECOMPUTE_PREVIEWS.eq(true)))
            .and(sub.IS_DELETED.isFalse())
            .limit(limit)
            .forUpdate()
            .of(sub) // All metrics related updates need to lock rows on the outer side of the join.
            .skipLocked()
            .fetch();
    }

    public void setSubscriptionInvoicePreviews(
        DSLContext txnDslContext,
        String tenantId,
        UUID id,
        String subscriptionId,
        UUID metricsId,
        JSONB invoicePreviews
    ) {
        if (metricsId == null) { // Record does not exist, add under lock
            txnDslContext
                .insertInto(SUBSCRIPTION_METRICS)
                .columns(
                    SUBSCRIPTION_METRICS.ID,
                    SUBSCRIPTION_METRICS.TENANT_ID,
                    SUBSCRIPTION_METRICS.SUBSCRIPTION_ID,
                    SUBSCRIPTION_METRICS.INVOICE_PREVIEWS,
                    SUBSCRIPTION_METRICS.RECOMPUTE_PREVIEWS
                )
                .values(id, tenantId, subscriptionId, invoicePreviews, false)
                .execute();
        } else {
            txnDslContext
                .update(SUBSCRIPTION_METRICS)
                .set(SUBSCRIPTION_METRICS.INVOICE_PREVIEWS, invoicePreviews)
                .set(SUBSCRIPTION_METRICS.RECOMPUTE_PREVIEWS, false)
                .where(SUBSCRIPTION_METRICS.TENANT_ID.eq(tenantId))
                .and(SUBSCRIPTION_METRICS.ID.eq(id))
                .execute();
        }
    }

    private Set<SubscriptionEntity> getAllActiveSubscriptionsWithPendingStatusForTenant() {
        return getAllSubscriptionsWithStatusAndDateRestrictionForTenant(SubscriptionState.PENDING, SUBSCRIPTION.START_DATE);
    }

    private Set<SubscriptionEntity> getAllActiveSubscriptionsWithPendingStatusForTenant(LocalDateTime compareTime) {
        return getAllSubscriptionsWithStatusAndDateRestrictionForTenant(SubscriptionState.PENDING, SUBSCRIPTION.START_DATE, compareTime);
    }

    private Set<SubscriptionEntity> getAllCancelledSubscriptionsWithPendingCancellationStatusForTenant() {
        return getAllSubscriptionsWithStatusAndDateRestrictionForTenant(SubscriptionState.PENDING_CANCELLATION, SUBSCRIPTION.CANCELED_DATE);
    }

    private Set<SubscriptionEntity> getAllCancelledSubscriptionsWithPendingCancellationStatusForTenant(LocalDateTime compareTime) {
        return getAllSubscriptionsWithStatusAndDateRestrictionForTenant(
            SubscriptionState.PENDING_CANCELLATION,
            SUBSCRIPTION.CANCELED_DATE,
            compareTime
        );
    }

    private Set<SubscriptionEntity> getAllExpiredSubscriptionsWithActiveStatusForTenant() {
        return getAllSubscriptionsWithStatusAndDateRestrictionForTenant(SubscriptionState.ACTIVE, SUBSCRIPTION.END_DATE);
    }

    private Set<SubscriptionEntity> getAllExpiredSubscriptionsWithActiveStatusForTenant(LocalDateTime compareTime) {
        return getAllSubscriptionsWithStatusAndDateRestrictionForTenant(SubscriptionState.ACTIVE, SUBSCRIPTION.END_DATE, compareTime);
    }

    private Set<SubscriptionEntity> getAllSubscriptionsWithStatusAndDateRestrictionForTenant(
        SubscriptionState state,
        TableField<SubscriptionRecord, LocalDateTime> timestampField,
        LocalDateTime compareTime
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var result = dslContext
            .select(
                SUBSCRIPTION.ID,
                SUBSCRIPTION.SUBSCRIPTION_ID,
                SUBSCRIPTION.START_DATE,
                SUBSCRIPTION.END_DATE,
                SUBSCRIPTION.CANCELED_DATE,
                SUBSCRIPTION.STATE
            )
            .from(SUBSCRIPTION)
            .where(
                SUBSCRIPTION.ID.in(
                    dslContext
                        .select(SUBSCRIPTION.ID)
                        .distinctOn(SUBSCRIPTION.SUBSCRIPTION_ID)
                        .from(SUBSCRIPTION)
                        .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
                        .and(SUBSCRIPTION.IS_DELETED.isFalse())
                        .orderBy(SUBSCRIPTION.SUBSCRIPTION_ID, SUBSCRIPTION.VERSION.desc())
                )
            )
            .and(SUBSCRIPTION.STATE.eq(state.name()))
            .and(timestampField.le(compareTime))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .fetchInto(SubscriptionRecord.class);
        return result.stream().map(subscriptionMapper::recordToSubscriptionEntity).collect(Collectors.toSet());
    }

    private Set<SubscriptionEntity> getAllSubscriptionsWithStatusAndDateRestrictionForTenant(
        SubscriptionState state,
        TableField<SubscriptionRecord, LocalDateTime> timestampField
    ) {
        return getAllSubscriptionsWithStatusAndDateRestrictionForTenant(state, timestampField, LocalDateTime.now(ZoneOffset.UTC));
    }

    public void updateSubscriptionEntityState(UUID id, SubscriptionState newState) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        dslContext
            .update(SUBSCRIPTION)
            .set(SUBSCRIPTION.STATE, newState.name())
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.ID.eq(id))
            .execute();
    }

    private void throwDuplicateSubscriptionIdException(String subscriptionId) {
        var message = "Duplicate SubscriptionId generated. SubscriptionId = " + subscriptionId;
        LOGGER.info(message);
        throw new DuplicateIdException(message);
    }

    public SubscriptionMetrics getSubscriptionMetrics(DSLContext txnDslContext, String tenantId, UUID globalSubscriptionId) {
        var record = txnDslContext
            .select()
            .from(SUBSCRIPTION_METRICS)
            .where(SUBSCRIPTION_METRICS.ID.eq(globalSubscriptionId))
            .and(SUBSCRIPTION_METRICS.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION_METRICS.IS_DELETED.isFalse())
            .fetchOneInto(SubscriptionMetricsRecord.class);
        return record != null ? subscriptionMetricsMapper.recordToSubscriptionMetrics(record) : null;
    }

    public Optional<ProrationConfig> getPinnedProrationConfig(String subscriptionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        String prorationOptionOverride = dslContext
            .select(SUBSCRIPTION.PRORATION_OVERRIDE_OPTION)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            // we always get the latest pinned option
            // pinned proration option will be copied through subscription versions
            .orderBy(SUBSCRIPTION.VERSION.desc())
            .limit(1)
            .fetchOneInto(String.class);

        if (prorationOptionOverride == null) {
            return Optional.empty();
        }

        ProrationOptions prorationOption = EnumUtils.getEnum(ProrationOptions.class, prorationOptionOverride);
        if (prorationOption == null) {
            throw new ServiceFailureException(String.format("cannot find proration option with the value: %s", prorationOptionOverride));
        }
        return Optional.of(prorationOption.getProrationConfig());
    }

    public SubscriptionCharge getSubscriptionChargeById(UUID subscriptionChargeUuid) {
        Objects.requireNonNull(subscriptionChargeUuid, "subscriptionChargeUuid must be specified");

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var record = dslContext
            .select()
            .from(SUBSCRIPTION_CHARGE)
            .where(SUBSCRIPTION_CHARGE.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION_CHARGE.ID.eq(subscriptionChargeUuid))
            .and(SUBSCRIPTION_CHARGE.IS_DELETED.isFalse())
            .fetchOneInto(SubscriptionChargeRecord.class);
        return subscriptionMapper.recordToSubscriptionChargeEntity(record);
    }

    public Optional<SubscriptionCharge> getSubscriptionChargeBySubscriptionChargeId(String subscriptionChargeId) {
        InvariantChecker.nonNullArg(subscriptionChargeId, "subscriptionChargeId");

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var record = dslContext
            .select()
            .from(SUBSCRIPTION_CHARGE)
            .where(SUBSCRIPTION_CHARGE.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION_CHARGE.SUBSCRIPTION_CHARGE_ID.eq(subscriptionChargeId))
            .and(SUBSCRIPTION_CHARGE.IS_DELETED.isFalse())
            .fetchOneInto(SubscriptionChargeRecord.class);

        return Optional.ofNullable(record).map(subscriptionMapper::recordToSubscriptionChargeEntity);
    }

    public void deleteSubscriptionMetrics(DSLContext txnDslContext, String tenantId, String subscriptionId) {
        txnDslContext
            .update(SUBSCRIPTION_METRICS)
            .set(SUBSCRIPTION_METRICS.IS_DELETED, true)
            .where(SUBSCRIPTION_METRICS.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION_METRICS.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION_METRICS.IS_DELETED.isFalse())
            .execute();
    }

    public void deleteSubscriptionMetricsVersion(DSLContext txnDslContext, UUID subscriptionUuid) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        txnDslContext
            .update(SUBSCRIPTION_METRICS)
            .set(SUBSCRIPTION_METRICS.IS_DELETED, true)
            .where(SUBSCRIPTION_METRICS.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION_METRICS.ID.eq(subscriptionUuid))
            .and(SUBSCRIPTION_METRICS.IS_DELETED.isFalse())
            .execute();
    }

    public void deleteSubscription(DSLContext dslContext, String subscriptionId) {
        SubscriptionEntity se = getSubscription(dslContext, new VersionReference<>(subscriptionId, ReferenceType.CURRENT));

        // update renewed to of previous subscription
        if (StringUtils.isNotBlank(se.getRenewedFromSubscriptionId())) {
            updateSubscriptionWithRenewedToDetails(dslContext, true, se.getRenewedFromSubscriptionId(), null, null);
        }

        // update restructured to of previous subscription
        if (StringUtils.isNotBlank(se.getRestructuredFromSubscriptionId())) {
            updateSubscriptionWithRestructuredToDetails(dslContext, true, se.getRestructuredFromSubscriptionId(), null, null);
        }

        // Subscription charges map uses internal subscription id
        List<UUID> ids = getIdsBySubscriptionId(dslContext, subscriptionId);
        ids.forEach(id -> deleteSubscriptionChargesMap(dslContext, id));
        deleteSubscriptionCharges(dslContext, subscriptionId);

        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .update(SUBSCRIPTION)
            .set(SUBSCRIPTION.IS_DELETED, true)
            .where(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .execute();
    }

    public void deleteSubscriptionVersion(DSLContext dslContext, Subscription subscription) {
        deleteSubscriptionChargesMap(dslContext, subscription.getId());
        deleteSubscriptionChargesForSubscriptionVersion(dslContext, subscription.getSubscriptionId(), subscription.getVersion());

        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .update(SUBSCRIPTION)
            .set(SUBSCRIPTION.IS_DELETED, true)
            .where(SUBSCRIPTION.ID.eq(subscription.getId()))
            .and(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .execute();
    }

    // return the current highest version number for given subscription, including soft deleted subscriptions
    public int getNextSubscriptionVersion(String subscriptionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        Integer currentVersion = dslContext
            .select(max(SUBSCRIPTION.VERSION))
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionId))
            .fetchOneInto(Integer.class);

        if (currentVersion == null) {
            return 1;
        }

        return currentVersion + 1;
    }

    private List<UUID> getIdsBySubscriptionId(DSLContext dslContext, String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId must be specified");

        String tenantId = tenantIdProvider.provideTenantIdString();

        return dslContext
            .select(SUBSCRIPTION.ID)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION.IS_DELETED.isFalse())
            .fetchInto(UUID.class);
    }

    private void deleteSubscriptionChargesMap(DSLContext dslContext, UUID subscriptionUuid) {
        Validator.validateNonNullArguments(subscriptionUuid);

        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .update(SUBSCRIPTION_CHARGE_MAP)
            .set(SUBSCRIPTION_CHARGE_MAP.IS_DELETED, true)
            .where(SUBSCRIPTION_CHARGE_MAP.SUBSCRIPTION_ID.eq(subscriptionUuid))
            .and(SUBSCRIPTION_CHARGE_MAP.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION_CHARGE_MAP.IS_DELETED.isFalse())
            .execute();
    }

    private void deleteSubscriptionCharges(DSLContext dslContext, String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId must be specified");

        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .update(SUBSCRIPTION_CHARGE)
            .set(SUBSCRIPTION_CHARGE.IS_DELETED, true)
            .where(SUBSCRIPTION_CHARGE.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION_CHARGE.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION_CHARGE.IS_DELETED.isFalse())
            .execute();
    }

    private void deleteSubscriptionChargesForSubscriptionVersion(DSLContext dslContext, String subscriptionId, int version) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .update(SUBSCRIPTION_CHARGE)
            .set(SUBSCRIPTION_CHARGE.IS_DELETED, true)
            .where(SUBSCRIPTION_CHARGE.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(SUBSCRIPTION_CHARGE.VERSION.eq(version))
            .and(SUBSCRIPTION_CHARGE.TENANT_ID.eq(tenantId))
            .and(SUBSCRIPTION_CHARGE.IS_DELETED.isFalse())
            .execute();
    }
}
