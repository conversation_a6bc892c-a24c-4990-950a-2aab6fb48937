package com.subskribe.billy.subscription.services;

import com.google.common.annotations.VisibleForTesting;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.InvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.BillingScheduleService;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.order.model.CustomBillingPeriod;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.inject.Inject;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class SubscriptionBillingPeriodService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SubscriptionBillingPeriodService.class);
    private static final Integer ONE_STEP = 1;

    private final SubscriptionGetService subscriptionGetService;
    private final TenantSettingService tenantSettingService;
    private final FeatureService featureService;
    private final ProrationConfigurationGetService prorationConfigurationGetService;

    @Inject
    public SubscriptionBillingPeriodService(
        SubscriptionGetService subscriptionGetService,
        TenantSettingService tenantSettingService,
        FeatureService featureService,
        ProrationConfigurationGetService prorationConfigurationGetService
    ) {
        this.subscriptionGetService = subscriptionGetService;
        this.tenantSettingService = tenantSettingService;
        this.featureService = featureService;
        this.prorationConfigurationGetService = prorationConfigurationGetService;
    }

    public static boolean isInstantInBillingPeriod(Instant instant, BillingPeriod billingPeriod) {
        return (instant.compareTo(billingPeriod.getStart()) >= 0 && instant.compareTo(billingPeriod.getEnd()) < 0);
    }

    private List<BillingPeriod> getBillingPeriodsForSubscription(String subscriptionId) {
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        return getBillingPeriods(subscription, timeZone, subscription.getEndDate());
    }

    // get billing period start dates for a subscription
    public List<Instant> getFutureBillingPeriodStartDates(String subscriptionId) {
        return getBillingPeriodsForSubscription(subscriptionId)
            .stream()
            .filter(billingPeriod -> billingPeriod.getEnd().isAfter(Instant.now()))
            .map(BillingPeriod::getStart)
            .collect(Collectors.toList());
    }

    public List<Instant> getAllBillingPeriodStartDates(String subscriptionId) {
        return getBillingPeriodsForSubscription(subscriptionId).stream().map(BillingPeriod::getStart).collect(Collectors.toList());
    }

    public Instant getNextBillingPeriodStart(Subscription subscription, TimeZone timeZone) {
        Instant now = Instant.now();
        List<BillingPeriod> billingPeriods = getBillingPeriods(subscription, timeZone, subscription.getEndDate());
        var nextPeriod = billingPeriods.stream().filter(billingPeriod -> billingPeriod.getStart().isAfter(now)).findFirst();
        if (nextPeriod.isPresent()) {
            return nextPeriod.get().getStart();
        } else {
            return subscription.getEndDate();
        }
    }

    public List<BillingPeriod> getBillingPeriods(Subscription subscription, TimeZone timeZone, Instant invoiceTargetDate) {
        var invoiceStartDate = subscription.getBillingAnchorDate();

        return getBillingPeriods(
            subscription.getStartDate(),
            subscription.getEndDate(),
            invoiceStartDate,
            invoiceTargetDate,
            timeZone.toZoneId(),
            subscription.getBillingCycle(),
            subscription.getBillingTerm()
        );
    }

    public List<BillingPeriod> getOrderLineBillingPeriods(
        OrderLineItem orderItem,
        Map<String, Charge> chargeMap,
        InvoiceBillingInfo invoiceBillingInfo,
        List<BillingPeriod> invoiceBillingPeriods
    ) {
        Charge charge = chargeMap.get(orderItem.getChargeId());
        BillingCycle billingCycle = charge.getBillingCycle();

        // For event based charges, order lines should be calculated based on charge cycle and not be affected by subscription billing cycle,
        // hence we treat the billing cycle as paid in full.
        if (charge.isEventBased()) {
            billingCycle = BillingCycle.PAID_IN_FULL;
        }

        if (billingCycle == BillingCycle.DEFAULT) {
            return invoiceBillingPeriods;
        }

        Recurrence chargeBillingCycleRecurrence = billingCycle == BillingCycle.CHARGE_RECURRENCE
            ? charge.getRecurrence()
            : mapBillingCycleToRecurrence(billingCycle);

        return getBillingPeriods(
            invoiceBillingInfo.getSubscriptionStart(),
            invoiceBillingInfo.getSubscriptionEnd(),
            invoiceBillingInfo.getBillingAnchorDate(),
            invoiceBillingInfo.getInvoiceTargetDate(),
            invoiceBillingInfo.getTimeZone().toZoneId(),
            chargeBillingCycleRecurrence,
            invoiceBillingInfo.getBillingTerm() // todo: we should actually remove this or maybe fetch the charge billing term instead
        );
    }

    private static Recurrence mapBillingCycleToRecurrence(BillingCycle billingCycle) {
        Cycle cycle = billingCycle.getCycle().orElseThrow(() -> new ServiceFailureException("invalid billing cycle for recurrence: " + billingCycle));
        return new Recurrence(cycle, ONE_STEP);
    }

    public List<BillingPeriod> getBillingPeriods(
        Instant subscriptionStartDate,
        Instant subscriptionEndDate,
        Instant billingStartDate,
        Instant invoiceTargetDate,
        ZoneId zoneId,
        Recurrence recurrence,
        BillingTerm billingTerm
    ) {
        if (ObjectUtils.anyNull(subscriptionStartDate, billingStartDate, invoiceTargetDate, zoneId, recurrence)) {
            throw new IllegalArgumentException("One or more required parameters are null.");
        }

        if (invoiceTargetDate.isBefore(subscriptionStartDate) || invoiceTargetDate.isAfter(subscriptionEndDate)) {
            throw new IllegalArgumentException(String.format("Invalid invoiceTargetDate %s", invoiceTargetDate));
        }

        return generateAndFilterBillingPeriods(
            subscriptionStartDate,
            subscriptionEndDate,
            billingStartDate,
            invoiceTargetDate,
            zoneId,
            recurrence,
            billingTerm
        );
    }

    public List<List<BillingPeriod>> getBillingPeriodsForCustomBilling(
        TimeZone timeZone,
        Order order,
        CustomBillingSchedule customBillingSchedule,
        Optional<List<MemoizedInvoiceLineItem>> memoizedInvoiceLineItems,
        InvoiceBillingInfo invoiceBillingInfo,
        boolean forOrderLineCalculation
    ) {
        String orderId = order.getOrderId();
        ZoneId zoneId = invoiceBillingInfo != null ? invoiceBillingInfo.getTimeZone().toZoneId() : timeZone.toZoneId();
        Instant orderStartDate = invoiceBillingInfo != null ? invoiceBillingInfo.getSubscriptionStart() : order.getStartDate();
        Instant orderEndDate = invoiceBillingInfo != null ? invoiceBillingInfo.getSubscriptionEnd() : order.getEndDate();
        Instant billingStartDate = invoiceBillingInfo != null ? invoiceBillingInfo.getBillingAnchorDate() : order.getBillingAnchorDate();
        Instant invoiceTargetDate = invoiceBillingInfo != null ? invoiceBillingInfo.getInvoiceTargetDate() : order.getEndDate();
        Recurrence recurrence = invoiceBillingInfo != null ? invoiceBillingInfo.getBillingCycle() : order.getBillingCycle();
        BillingTerm billingTerm = invoiceBillingInfo != null ? invoiceBillingInfo.getBillingTerm() : order.getBillingTerm();

        LOGGER.info("Generating billing periods for custom billing, order id: {}", orderId);
        validateCustomBillingPeriodGenerationInput(
            customBillingSchedule,
            orderStartDate,
            billingStartDate,
            invoiceTargetDate,
            zoneId,
            recurrence,
            orderId,
            orderEndDate
        );

        // FOR ORDER LINE AMOUNT CALCULATION OF CUSTOM BILLING CYCLE WE TREAT IT AS PAID_IN_FULL SO THE AMOUNT IS CALCULATED AS PER CHARGE AMOUNT
        if (forOrderLineCalculation) {
            var fullBillingPeriod = new BillingPeriod(orderStartDate, orderEndDate, orderEndDate, new Recurrence(Cycle.PAID_IN_FULL, 1));
            return List.of(List.of(fullBillingPeriod));
        }

        // There is an asymmetry in how billing periods are generated for adhoc billing vs recurrence based billing. For recurrence based it's directly calculated
        // However, for adhoc billing, we need to calculate the weighted distribution of the amount across order lines for each adhoc billing period
        if (customBillingSchedule.getCustomBillingType() == CustomBillingSchedule.CustomBillingType.ADHOC) {
            return generateBillingPeriodsForAdhocCustomBilling(
                timeZone,
                order,
                customBillingSchedule,
                memoizedInvoiceLineItems,
                invoiceTargetDate,
                orderEndDate,
                billingTerm,
                forOrderLineCalculation
            );
        }

        return generateBillingPeriodsForRecurrenceCustomBilling(
            customBillingSchedule,
            orderStartDate,
            orderEndDate,
            invoiceTargetDate,
            zoneId,
            billingTerm
        );
    }

    private List<List<BillingPeriod>> generateBillingPeriodsForAdhocCustomBilling(
        TimeZone timeZone,
        Order order,
        CustomBillingSchedule customBillingSchedule,
        Optional<List<MemoizedInvoiceLineItem>> memoizedInvoiceLineItems,
        Instant invoiceTargetDate,
        Instant orderEndDate,
        BillingTerm billingTerm,
        boolean forOrderLineCalculation
    ) {
        // This will be the case during custom billing input validation
        if (memoizedInvoiceLineItems.isEmpty()) {
            List<List<InvoiceItem>> allInvoiceItems = generateInvoiceItemsForAdhocCustomBilling(
                customBillingSchedule,
                order,
                timeZone,
                forOrderLineCalculation
            );
            // TODO: see if this can be refactored to flatten the list
            // Each entry in allInvoiceItems corresponds to an adhoc billing period + weighted distribution of that amount across order lines
            return allInvoiceItems
                .stream()
                .map(invoiceItems ->
                    invoiceItems
                        .stream()
                        .map(invoiceItem ->
                            new BillingPeriod(
                                invoiceItem.getPeriodStartDate(),
                                invoiceItem.getPeriodEndDate(),
                                invoiceItem.getPeriodEndDate(),
                                new Recurrence(Cycle.PAID_IN_FULL, 1)
                            )
                        )
                        .filter(billingPeriod -> includeBillingPeriodForInvoiceGeneration(billingPeriod, invoiceTargetDate, orderEndDate, billingTerm)
                        )
                        .collect(Collectors.toSet())
                        .stream()
                        .toList()
                )
                .toList();
        }

        // During getInvoiceItems we can return all billing periods from the memoized lines for ad hoc billing over here as,
        // During invoice generation we will filter out memoized lines whose trigger on dates are post invoice target date,
        // in reality billing periods for ad hoc billing are only of a relevance for denoting service periods and don't affect invoice generation
        return memoizedInvoiceLineItems
            .get()
            .stream()
            .collect(
                Collectors.groupingBy(
                    MemoizedInvoiceLineItem::getTriggerOn,
                    Collectors.mapping(
                        memoizedInvoiceLineItem ->
                            new BillingPeriod(
                                memoizedInvoiceLineItem.getPeriodStartDate(),
                                memoizedInvoiceLineItem.getPeriodEndDate(),
                                memoizedInvoiceLineItem.getPeriodEndDate(),
                                new Recurrence(Cycle.PAID_IN_FULL, 1)
                            ),
                        Collectors.toList()
                    )
                )
            )
            .values()
            .stream()
            .toList();
    }

    private void validateCustomBillingPeriodGenerationInput(
        CustomBillingSchedule customBillingSchedule,
        Instant orderStartDate,
        Instant billingStartDate,
        Instant invoiceTargetDate,
        ZoneId zoneId,
        Recurrence recurrence,
        String orderId,
        Instant orderEndDate
    ) {
        if (ObjectUtils.anyNull(orderStartDate, billingStartDate, invoiceTargetDate, zoneId, recurrence, orderId, customBillingSchedule)) {
            throw new InvalidInputException("One or more required parameters are null.");
        }

        if (invoiceTargetDate.isBefore(orderStartDate) || invoiceTargetDate.isAfter(orderEndDate)) {
            throw new InvalidInputException(String.format("Invalid invoiceTargetDate %s", invoiceTargetDate));
        }

        if (recurrence.getCycle() != Cycle.CUSTOM) {
            throw new InvalidInputException("Recurrence cycle must be CUSTOM for custom billing, order id: " + orderId);
        }
    }

    public List<List<InvoiceItem>> generateInvoiceItemsForAdhocCustomBilling(
        CustomBillingSchedule customBillingSchedule,
        Order order,
        TimeZone timeZone,
        boolean forOrderLineCalculation
    ) {
        List<List<InvoiceItem>> allInvoiceItems = new ArrayList<>();
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);

        List<CustomBillingPeriod> customBillingPeriods = customBillingSchedule.schedules();
        Map<String, Pair<Instant, BigDecimal>> orderLineUsedServicePeriodAndAmounts = new LinkedHashMap<>();
        BigDecimal adhocBillingTotalAmount = customBillingPeriods
            .stream()
            .map(CustomBillingPeriod::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        List<String> orderLineIds = customBillingSchedule.orderLines();
        Map<String, OrderLineItem> orderLineItemMap = order
            .getLineItemsNetEffect()
            .stream()
            .filter(orderLineItem -> orderLineIds.contains(orderLineItem.getOrderLineId()))
            .collect(Collectors.toMap(OrderLineItem::getOrderLineId, orderLineItem -> orderLineItem));
        BillingScheduleService.CustomBillingOrderLineAmountGrouping customBillingOrderLineAmountGrouping =
            BillingScheduleService.getOrderLineAmountGrouping(orderLineItemMap);

        IntStream.range(0, customBillingPeriods.size()).forEach(index -> {
            CustomBillingPeriod period = customBillingPeriods.get(index);

            boolean isLastSegment = index == customBillingPeriods.size() - 1;

            List<InvoiceItem> invoiceItems = BillingScheduleService.generateInvoiceItemsForAdhocCustomBillingPeriod(
                adhocBillingTotalAmount,
                period,
                isLastSegment,
                order.getOrderId(),
                order.getCurrency().getCurrencyCode(),
                customBillingOrderLineAmountGrouping,
                orderLineUsedServicePeriodAndAmounts,
                prorationConfig,
                timeZone,
                forOrderLineCalculation
            );

            allInvoiceItems.add(invoiceItems);
        });

        if (BooleanUtils.isFalse(forOrderLineCalculation)) {
            BillingScheduleService.validateCustomBillingInvoiceGenerationInvariants(
                order.getOrderId(),
                allInvoiceItems,
                customBillingOrderLineAmountGrouping,
                adhocBillingTotalAmount
            );
        }

        return allInvoiceItems;
    }

    @VisibleForTesting
    List<List<BillingPeriod>> generateBillingPeriodsForRecurrenceCustomBilling(
        CustomBillingSchedule customBillingSchedule,
        Instant orderStartDate,
        Instant orderEndDate,
        Instant invoiceTargetDate,
        ZoneId zoneId,
        BillingTerm billingTerm
    ) {
        List<CustomBillingPeriod> customBillingPeriods = customBillingSchedule.schedules();

        List<List<BillingPeriod>> billingPeriods = new ArrayList<>();
        AtomicReference<Instant> billingPeriodStart = new AtomicReference<>();
        AtomicReference<Instant> billingPeriodEnd = new AtomicReference<>();

        IntStream.range(0, customBillingPeriods.size()).forEach(index -> {
            CustomBillingPeriod period = customBillingPeriods.get(index);
            Recurrence periodRecurrence = Recurrence.fromRecurrenceJson(period.getRecurrenceWithCount().recurrence());
            CustomBillingPeriodCalculationInput customBillingPeriodCalculationInput = new CustomBillingPeriodCalculationInput(
                orderStartDate,
                orderEndDate,
                billingPeriodStart,
                billingPeriodEnd,
                customBillingPeriods
            );

            // NOTE : this is where recurrence is defined for custom billing schedule, for ex : first 3 months - amt 1 each, next 6 months - amt 2 each
            // This will set the billingPeriod start and end by taking the recurrence cycle multiplied by the count, for ex - Jan 1 - March 31 for 3 months
            setPeriodStartAndEndForRecurrenceCustomBilling(customBillingPeriodCalculationInput, index, periodRecurrence, zoneId, period);

            billingPeriods.add(
                generateAndFilterBillingPeriods(
                    billingPeriodStart.get(),
                    billingPeriodEnd.get(),
                    billingPeriodStart.get(),
                    invoiceTargetDate,
                    zoneId,
                    periodRecurrence,
                    billingTerm
                )
            );
        });

        return billingPeriods;
    }

    private void setPeriodStartAndEndForRecurrenceCustomBilling(
        CustomBillingPeriodCalculationInput customBillingPeriodCalculationInput,
        int index,
        Recurrence periodRecurrence,
        ZoneId zoneId,
        CustomBillingPeriod period
    ) {
        Instant orderStartDate = customBillingPeriodCalculationInput.orderStartDate();
        Instant orderEndDate = customBillingPeriodCalculationInput.orderEndDate();
        AtomicReference<Instant> billingPeriodStart = customBillingPeriodCalculationInput.billingPeriodStart();
        AtomicReference<Instant> billingPeriodEnd = customBillingPeriodCalculationInput.billingPeriodEnd();
        List<CustomBillingPeriod> customBillingPeriods = customBillingPeriodCalculationInput.customBillingPeriods();

        // The first service period should start from order line start date and last service period should end at order line end date
        // Rest the service periods should be as per the recurrence defined, 1 month is Jan 1 - Feb 1, 3 month is Feb 1 - May 1 and so on
        if (index == 0) {
            billingPeriodStart.set(orderStartDate);
        } else {
            billingPeriodStart.set(billingPeriodEnd.get());
        }

        Instant cyclePeriodEnd = getNextBillingCycleStart(
            ZonedDateTime.ofInstant(billingPeriodStart.get(), zoneId),
            periodRecurrence,
            // This is the count of billing cycles we need to consider for the current period
            period.getRecurrenceWithCount().count()
        ).toInstant();

        if (index == customBillingPeriods.size() - 1) {
            if (cyclePeriodEnd.compareTo(orderEndDate) != 0) {
                throw new ConflictingStateException(
                    String.format(
                        "cyclePeriodEnd is not equal to subscriptionEndDate, cyclePeriodEnd: %s, subscriptionEndDate: %s",
                        cyclePeriodEnd,
                        orderEndDate
                    )
                );
            }
            billingPeriodEnd.set(orderEndDate);
        } else {
            billingPeriodEnd.set(cyclePeriodEnd);
        }
    }

    private List<BillingPeriod> generateAndFilterBillingPeriods(
        Instant subscriptionStartDate,
        Instant subscriptionEndDate,
        Instant billingStartDate,
        Instant invoiceTargetDate,
        ZoneId zoneId,
        Recurrence recurrence,
        BillingTerm billingTerm
    ) {
        return generateBillingPeriods(subscriptionStartDate, subscriptionEndDate, billingStartDate, zoneId, recurrence)
            .stream()
            .filter(billingPeriod -> includeBillingPeriodForInvoiceGeneration(billingPeriod, invoiceTargetDate, subscriptionEndDate, billingTerm))
            .toList();
    }

    private boolean includeBillingPeriodForInvoiceGeneration(
        BillingPeriod billingPeriod,
        Instant invoiceTargetDate,
        Instant subscriptionEndDate,
        BillingTerm billingTerm
    ) {
        if (invoiceTargetDate.compareTo(subscriptionEndDate) >= 0) {
            return true;
        }
        Instant billingDate =
            switch (billingTerm) {
                case UP_FRONT -> billingPeriod.getStart();
                case IN_ARREARS -> billingPeriod.getEnd();
            };
        return invoiceTargetDate.compareTo(billingDate) >= 0;
    }

    private List<BillingPeriod> generateBillingPeriods(
        Instant subscriptionStartDate,
        Instant subscriptionEndDate,
        Instant billingStartDate,
        ZoneId zoneId,
        Recurrence recurrence
    ) {
        var zonedSubscriptionStartDate = ZonedDateTime.ofInstant(subscriptionStartDate, zoneId);
        var zonedSubscriptionEndDate = ZonedDateTime.ofInstant(subscriptionEndDate, zoneId);

        // For custom billing, there can be order lines which are not included in the custom schedule, for those we need to consider billing periods as the full subscription period
        // todo: align custom billing and event based billing to use a separate cycle instead of overloading with PAID_IN_FULL
        if (recurrence.getCycle() == Cycle.PAID_IN_FULL || recurrence.getCycle() == Cycle.CUSTOM) {
            List<BillingPeriod> billingPeriods = new ArrayList<>();

            if (subscriptionStartDate.isBefore(billingStartDate)) {
                // in cases of event based (which has cycle set to PAID_IN_FULL) and custom billing schedule,
                // if there is a billing anchor date that's different from subscription start date, respect that and create an initial stub period
                var stubBillingPeriod = new BillingPeriod(
                    subscriptionStartDate,
                    billingStartDate,
                    billingStartDate,
                    new Recurrence(Cycle.PAID_IN_FULL, 1)
                );
                billingPeriods.add(stubBillingPeriod);
            }

            var fullBillingPeriod = new BillingPeriod(
                billingStartDate,
                subscriptionEndDate,
                subscriptionEndDate,
                new Recurrence(Cycle.PAID_IN_FULL, 1)
            );

            billingPeriods.add(fullBillingPeriod);

            return billingPeriods;
        }

        List<BillingPeriod> billingPeriods = new ArrayList<>();

        // subscription starts before first billing cycle, add first partial period.
        if (subscriptionStartDate.isBefore(billingStartDate)) {
            validateBillingAnchorDate(subscriptionStartDate, subscriptionEndDate, billingStartDate, recurrence, zoneId);
            Instant fullPeriodEnd = getNextBillingCycleStart(zonedSubscriptionStartDate, recurrence, 1).toInstant();
            Instant stubPeriodEnd = fullPeriodEnd.isAfter(billingStartDate) ? fullPeriodEnd : billingStartDate;
            Recurrence stubPeriodRecurrence = billingStartDate.isAfter(fullPeriodEnd) ? new Recurrence(Cycle.PAID_IN_FULL, 1) : recurrence;
            billingPeriods.add(new BillingPeriod(subscriptionStartDate, billingStartDate, stubPeriodEnd, stubPeriodRecurrence));
        }

        int billingCycleNumber = 1;
        var zonedBillingStart = ZonedDateTime.ofInstant(billingStartDate, zoneId);

        // first full billing period starts at the billing start time
        var nextPeriodStart = zonedBillingStart;

        // calculate and add more billing periods if target date is on or after nextPeriodStart and before subscription ends
        while (nextPeriodStart.isBefore(zonedSubscriptionEndDate)) {
            var nextPeriodEnd = getNextBillingCycleStart(zonedBillingStart, recurrence, billingCycleNumber);

            Instant periodEnd = nextPeriodEnd.isAfter(zonedSubscriptionEndDate) ? zonedSubscriptionEndDate.toInstant() : nextPeriodEnd.toInstant();

            billingPeriods.add(new BillingPeriod(nextPeriodStart.toInstant(), periodEnd, nextPeriodEnd.toInstant(), recurrence));
            nextPeriodStart = nextPeriodEnd;
            billingCycleNumber++;
        }

        return billingPeriods;
    }

    public void validateBillingAnchorDate(
        Instant subscriptionStart,
        Instant subscriptionEnd,
        Instant billingAnchor,
        Recurrence recurrence,
        ZoneId zoneId
    ) {
        if (billingAnchor.isBefore(subscriptionStart)) {
            throw new IllegalArgumentException("Billing anchor date cannot be before subscription start date");
        }

        if (subscriptionEnd != null && billingAnchor.isAfter(subscriptionEnd)) {
            throw new InvalidInputException("Billing anchor date cannot be after subscription end date");
        }

        if (recurrence.getCycle() == Cycle.PAID_IN_FULL) {
            // fill paid in full billing anchor date is a no-op
            return;
        }

        var zonedSubscriptionStartDate = ZonedDateTime.ofInstant(subscriptionStart, zoneId);
        var zonedBillingStartDate = ZonedDateTime.ofInstant(billingAnchor, zoneId);
        var nextCycleStart = getNextBillingCycleStart(zonedSubscriptionStartDate, recurrence, 1);

        if (!featureService.isEnabled(Feature.FLEXIBLE_BILLING_ANCHOR_DATE) && !zonedBillingStartDate.isBefore(nextCycleStart)) {
            throw new IllegalArgumentException("Billing anchor date must be within one billing cycle of subscription start");
        }
    }

    public static Instant getBillingEndDate(Instant startDate, ZoneId zoneId, Recurrence termLength) {
        if (ObjectUtils.anyNull(startDate, zoneId, termLength)) {
            throw new IllegalArgumentException("One or more required parameters are null.");
        }

        ZonedDateTime periodStart = ZonedDateTime.ofInstant(startDate, zoneId);
        return getNextBillingCycleStart(periodStart, termLength, 1).toInstant();
    }

    // calculate the nth billing period start based on recurrence and period number
    public static ZonedDateTime getNextBillingCycleStart(ZonedDateTime billingStart, Recurrence recurrence, int billingCycleNumber) {
        int step = recurrence.getStep();
        return switch (recurrence.getCycle()) {
            case DAY -> billingStart.plusDays((long) step * billingCycleNumber);
            case MONTH -> billingStart.plusMonths((long) step * billingCycleNumber);
            case QUARTER -> billingStart.plusMonths((long) step * Cycle.QUARTER.getNumberOfMonths() * billingCycleNumber);
            case SEMI_ANNUAL -> billingStart.plusMonths((long) step * Cycle.SEMI_ANNUAL.getNumberOfMonths() * billingCycleNumber);
            case YEAR -> billingStart.plusYears((long) step * billingCycleNumber);
            case PAID_IN_FULL, CUSTOM -> throw new UnsupportedOperationException(
                String.format("Unsupported recurrence cycle: %s", recurrence.getCycle())
            );
        };
    }

    public static Period getItemOverlapPeriod(Instant itemStart, Instant itemEnd, BillingPeriod billingPeriod) {
        return getItemOverlapPeriod(itemStart, itemEnd, Period.between(billingPeriod.getStart(), billingPeriod.getEnd()));
    }

    public static Period getItemOverlapPeriod(Instant itemStart, Instant itemEnd, Period overlapPeriod) {
        if (overlapPeriod.getStart().isAfter(itemEnd)) {
            return Period.between(itemEnd, itemEnd);
        }

        if (itemStart.isAfter(overlapPeriod.getEnd())) {
            return Period.between(itemStart, itemStart);
        }

        Instant from = itemStart.isAfter(overlapPeriod.getStart()) ? itemStart : overlapPeriod.getStart();
        Instant to = itemEnd.isBefore(overlapPeriod.getEnd()) ? itemEnd : overlapPeriod.getEnd();
        return Period.between(from, to);
    }

    private record CustomBillingPeriodCalculationInput(
        Instant orderStartDate,
        Instant orderEndDate,
        AtomicReference<Instant> billingPeriodStart,
        AtomicReference<Instant> billingPeriodEnd,
        List<CustomBillingPeriod> customBillingPeriods
    ) {}
}
