package com.subskribe.billy.subscription.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.subskribe.billy.invoice.model.PurchaseOrder;
import com.subskribe.billy.jooq.default_schema.tables.SubscriptionChargeMap;
import com.subskribe.billy.jooq.default_schema.tables.records.SubscriptionChargeMapRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.SubscriptionChargeRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.SubscriptionRecord;
import com.subskribe.billy.shared.mapper.OrderObjectMapper;
import com.subskribe.billy.shared.pecuniary.Discount;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.subscription.SubscriptionCharge;
import java.lang.reflect.Type;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.jooq.JSON;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
public interface SubscriptionMapper extends OrderObjectMapper {
    ObjectMapper CUSTOM_JSON_MAPPER = JacksonProvider.emptyFieldExcludingMapper();
    Gson GSON = new Gson();

    @Mapping(target = "termLength", expression = "java(getRecurrence(sr.getTermLengthCycle(), sr.getTermLengthStep()))")
    @Mapping(target = "billingCycle", expression = "java(getRecurrence(sr.getBillingCycle(), sr.getBillingStep()))")
    @Mapping(source = "orders", target = "orders", qualifiedByName = "deserializeList")
    @Mapping(source = "predefinedDiscounts", target = "predefinedDiscounts", qualifiedByName = "deserializeTenantDiscounts")
    @Mapping(source = "poRequiredForInvoicing", target = "purchaseOrderRequiredForInvoicing")
    @Mapping(source = "prorationOverrideOption", target = "pinnedProrationOption")
    SubscriptionEntity recordToSubscriptionEntity(SubscriptionRecord sr);

    @Mapping(source = "billingCycle.cycle", target = "billingCycle")
    @Mapping(source = "billingCycle.step", target = "billingStep")
    @Mapping(source = "termLength.cycle", target = "termLengthCycle")
    @Mapping(source = "termLength.step", target = "termLengthStep")
    @Mapping(source = "orders", target = "orders", qualifiedByName = "serializeList")
    @Mapping(source = "predefinedDiscounts", target = "predefinedDiscounts", qualifiedByName = "serializeTenantDiscounts")
    @Mapping(source = "purchaseOrderRequiredForInvoicing", target = "poRequiredForInvoicing")
    @Mapping(source = "pinnedProrationOption", target = "prorationOverrideOption")
    SubscriptionRecord subscriptionEntityToRecord(SubscriptionEntity se);

    @Mapping(source = "orderLines", target = "orderLines", qualifiedByName = "deserializeList")
    @Mapping(source = "predefinedDiscounts", target = "predefinedDiscounts", qualifiedByName = "deserializeTenantDiscountLineItems")
    @Mapping(source = "attributeReferences", target = "attributeReferences", qualifiedByName = "jsonToAttributeReferences")
    @Mapping(source = "pricingOverride", target = "pricingOverride", qualifiedByName = "deserializePricingOverride")
    SubscriptionChargeEntity recordToSubscriptionChargeEntity(SubscriptionChargeRecord scr);

    @Mapping(source = "orderLines", target = "orderLines", qualifiedByName = "serializeList")
    @Mapping(source = "predefinedDiscounts", target = "predefinedDiscounts", qualifiedByName = "serializeTenantDiscountLineItems")
    @Mapping(source = "attributeReferences", target = "attributeReferences", qualifiedByName = "attributeReferencesToJson")
    @Mapping(source = "pricingOverride", target = "pricingOverride", qualifiedByName = "serializePricingOverride")
    SubscriptionChargeRecord subscriptionChargeEntityToRecord(SubscriptionChargeEntity sce);

    @Mapping(source = "orderLines", target = "orderLines", qualifiedByName = "serializeList")
    List<SubscriptionChargeRecord> subscriptionChargeListToRecords(List<SubscriptionChargeEntity> sceList);

    @Mapping(source = "orderLines", target = "orderLines", qualifiedByName = "deserializeList")
    List<SubscriptionChargeEntity> recordsToSubscriptionChargeEntityList(List<SubscriptionChargeRecord> scrList);

    @Mapping(source = "orderLines", target = "orderLines", qualifiedByName = "deserializeList")
    List<SubscriptionCharge> recordsToSubscriptionChargeList(List<SubscriptionChargeRecord> scrList);

    List<SubscriptionChargeMapRecord> subscriptionChargeMapListToRecords(List<SubscriptionChargeMapEntity> scmList);

    List<SubscriptionChargeMap> recordsToSubscriptionChargeMapList(List<SubscriptionChargeMapRecord> scmRecords);

    default List<Discount> deserializeDiscount(String discounts) throws JsonProcessingException {
        if (discounts == null) {
            return null;
        }
        return CUSTOM_JSON_MAPPER.readValue(discounts, new TypeReference<>() {});
    }

    default String serializeDiscount(List<Discount> discounts) throws JsonProcessingException {
        if (discounts == null) {
            return null;
        }
        return CUSTOM_JSON_MAPPER.writeValueAsString(discounts);
    }

    default Instant fromLocalDateTime(LocalDateTime ldt) {
        return ldt == null ? null : ldt.toInstant(ZoneOffset.UTC);
    }

    default LocalDateTime toLocalDateTime(Instant inst) {
        return inst == null ? null : LocalDateTime.ofInstant(inst, ZoneOffset.UTC);
    }

    @Named("deserializeList")
    default List<String> stringToListIds(String s) {
        return StringUtils.isBlank(s) ? new ArrayList<>() : Arrays.stream(s.split(",")).collect(Collectors.toList());
    }

    @Named("serializeList")
    default String listIdsToString(List<String> ids) {
        return ids == null ? null : String.join(",", ids);
    }

    default Set<String> stringToSetIds(String s) {
        return StringUtils.isBlank(s) ? new HashSet<>() : Arrays.stream(s.split(",")).collect(Collectors.toSet());
    }

    default String setIdsToString(Set<String> ids) {
        return ids == null ? null : String.join(",", ids);
    }

    default JSON serializePurchaseOrders(List<PurchaseOrder> purchaseOrders) {
        if (purchaseOrders == null) {
            return null;
        }
        return JSON.json(GSON.toJson(purchaseOrders));
    }

    default List<PurchaseOrder> deserializePurchaseOrders(JSON purchaseOrders) {
        if (purchaseOrders == null || StringUtils.isBlank(purchaseOrders.data())) {
            return null;
        }
        Type type = new TypeToken<List<PurchaseOrder>>() {}.getType();
        return GSON.fromJson(purchaseOrders.data(), type);
    }
}
