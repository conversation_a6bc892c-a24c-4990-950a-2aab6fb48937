package com.subskribe.billy.document.service;

import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import java.io.IOException;
import java.io.InputStream;
import javax.ws.rs.core.StreamingOutput;
import org.apache.poi.poifs.crypt.HashAlgorithm;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

public interface DocxConversionService {
    Logger LOGGER = LoggerFactory.getLogger(DocxConversionService.class);

    InputStream convertPdfToDocx(InputStream pdfInputStream);

    @SuppressWarnings("unused")
    static StreamingOutput makeDocxReadonlyOutput(InputStream inputStream) {
        return output -> {
            try (XWPFDocument document = new XWPFDocument(inputStream)) {
                document.enforceReadonlyProtection();
                document.write(output);
            } catch (IOException e) {
                String message = "Failed to make DOCX read only";
                LOGGER.warn(message, e);
                throw new ServiceFailureException(message, e);
            }
        };
    }

    static StreamingOutput makeDocxPasswordProtected(InputStream inputStream, String docxPassword) {
        return output -> {
            try (XWPFDocument document = new XWPFDocument(inputStream)) {
                document.enforceReadonlyProtection(docxPassword, HashAlgorithm.sha256);
                document.write(output);
            } catch (IOException e) {
                String message = "Failed to make DOCX read only";
                LOGGER.warn(message, e);
                throw new ServiceFailureException(message, e);
            }
        };
    }
}
