package com.subskribe.billy.document.db;

import static com.subskribe.billy.jooq.default_schema.tables.DocumentLink.DOCUMENT_LINK;

import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.document.mapper.DocumentLinkMapper;
import com.subskribe.billy.document.model.DocumentLink;
import com.subskribe.billy.jooq.default_schema.tables.records.DocumentLinkRecord;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Optional;
import javax.inject.Inject;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class DocumentLinkDAO {

    private static final DocumentLinkMapper DOCUMENT_LINK_MAPPER = Mappers.getMapper(DocumentLinkMapper.class);

    private final DSLContextProvider dslContextProvider;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public DocumentLinkDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
    }

    public DocumentLink getOrCreateDocumentLink(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DocumentLinkRecord record = getDocumentLinkByOrderId(tenantId, orderId).orElseGet(() -> createDocumentLink(tenantId, orderId));
        return DOCUMENT_LINK_MAPPER.fromRecord(record);
    }

    public void deleteDocumentLink(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        tenantDslContext
            .update(DOCUMENT_LINK)
            .set(DOCUMENT_LINK.IS_DELETED, true)
            .where(DOCUMENT_LINK.ORDER_ID.eq(orderId))
            .and(DOCUMENT_LINK.TENANT_ID.eq(tenantId))
            .execute();
    }

    public void deleteDocumentLink(DSLContext dslContext, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .update(DOCUMENT_LINK)
            .set(DOCUMENT_LINK.IS_DELETED, true)
            .where(DOCUMENT_LINK.ORDER_ID.eq(orderId))
            .and(DOCUMENT_LINK.TENANT_ID.eq(tenantId))
            .execute();
    }

    private DocumentLinkRecord createDocumentLink(String tenantId, String orderId) {
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return tenantDslContext
            .insertInto(DOCUMENT_LINK)
            .set(DOCUMENT_LINK.LINK_ID, AutoGenerate.getNewId())
            .set(DOCUMENT_LINK.TENANT_ID, tenantId)
            .set(DOCUMENT_LINK.ORDER_ID, orderId)
            .returning()
            .fetchOne();
    }

    private Optional<DocumentLinkRecord> getDocumentLinkByOrderId(String tenantId, String orderId) {
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        DocumentLinkRecord record = tenantDslContext
            .select()
            .from(DOCUMENT_LINK)
            .where(DOCUMENT_LINK.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_LINK.ORDER_ID.eq(orderId))
            .and(DOCUMENT_LINK.IS_DELETED.isFalse())
            .fetchOneInto(DocumentLinkRecord.class);

        return Optional.ofNullable(record);
    }

    @AllowNonRlsDataAccess
    public Optional<DocumentLink> getUnauthenticatedDocumentLinkByLinkId(String linkId) {
        DocumentLinkRecord record = dslContextProvider
            .get()
            .select()
            .from(DOCUMENT_LINK)
            .where(DOCUMENT_LINK.LINK_ID.eq(linkId))
            .fetchOneInto(DocumentLinkRecord.class);

        if (null == record) {
            return Optional.empty();
        }

        if (!record.getIsDeleted()) {
            return Optional.of(record).map(DOCUMENT_LINK_MAPPER::fromRecord);
        }

        var orderLinkRecord = getDocumentLinkByOrderId(record.getTenantId(), record.getOrderId());
        return orderLinkRecord.map(DOCUMENT_LINK_MAPPER::fromRecord);
    }
}
