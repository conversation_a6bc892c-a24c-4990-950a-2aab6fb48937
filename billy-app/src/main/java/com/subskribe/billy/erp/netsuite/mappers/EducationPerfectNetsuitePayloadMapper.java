package com.subskribe.billy.erp.netsuite.mappers;

import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.ACCOUNTING_PERIOD;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.ACCOUNTS_PAGE_FORMAT;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.ADDRESS_BOOK;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.ADDRESS_BOOK_ADDRESS;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.ADDR_1;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.ADDR_2;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.AMOUNT;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.AMOUNT_REMAINING;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.CITY;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.COMPANY_NAME;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.COUNTRY;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.CURRENCY;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.CUSTOMER;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.DATE_TIME_FORMATTER;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.DEFAULT_BILLING;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.DESCRIPTION;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.DISCOUNT_TOTAL;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.DUE_DATE;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.EMAIL;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.IDENTIFIER;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.IS_PERSON;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.IS_PRIMARY_SUB;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.IS_RESIDENTIAL;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.ITEM;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.ITEMS;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.MEMO;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.OPEN_STATUS;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.OTHER_REF_NUM;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.PAYMENT_TERM_FORMAT;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.PHONE_NUMBER;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.POSTING_PERIOD;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.POSTING_PERIOD_FORMAT;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.QUANTITY;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.RATE;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.REF_NAME;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.STATE;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.STATUS;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.SUBSIDIARY;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.SUBTOTAL;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.TERMS;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.TOTAL;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.TO_BE_EMAILED;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.TRANSACTION_DATE;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.TRANSACTION_ID;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.TYPE;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.UNAPPLIED;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.ZIPCODE;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.formatDate;
import static com.subskribe.billy.erp.netsuite.mappers.NetsuiteMapperConstants.toOffsetDateTime;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.BooleanNode;
import com.fasterxml.jackson.databind.node.DoubleNode;
import com.fasterxml.jackson.databind.node.LongNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.graphql.invoice.EmailNotifierContact;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.model.CreditMemoLineItem;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.entitycache.EntityCache;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

public class EducationPerfectNetsuitePayloadMapper implements NetsuitePayloadMapper {

    private static final String CUSTBODY_CRM_ID = "Custentity4";
    private static final String CUSTBODY_ACCOUNT_LINK = "custentity_subskribe_account_link";
    private static final String CUSTBODY_START_DATE = "custbody9";
    private static final String CUSTBODY_END_DATE = "custbody10";
    private static final String CUSTBODY_SUBSCRIPTION_ID = "custbody11";
    private static final String SECTOR_ATTRIBUTE = "Sector";
    private static final String TIER_ATTRIBUTE = "Tier";
    private static final String YEAR_CUSTOM_FIELD_NAME = "years";

    private final ProductCatalogGetService productCatalogGetService;
    private final AccountGetService accountGetService;
    private final EntityGetService entityGetService;
    private final EmailContactListService emailContactlistService;
    private final OrderGetService orderGetService;
    private final RateCardService rateCardService;
    private final CustomFieldService customFieldService;
    private final BillyConfiguration billyConfiguration;

    @Inject
    public EducationPerfectNetsuitePayloadMapper(
            ProductCatalogGetService productCatalogGetService,
            AccountGetService accountGetService,
            EntityGetService entityGetService,
            EmailContactListService emailContactlistService,
            OrderGetService orderGetService,
            RateCardService rateCardService, CustomFieldService customFieldService,
            BillyConfiguration billyConfiguration
    ) {
        this.productCatalogGetService = productCatalogGetService;
        this.accountGetService = accountGetService;
        this.entityGetService = entityGetService;
        this.emailContactlistService = emailContactlistService;
        this.orderGetService = orderGetService;
        this.rateCardService = rateCardService;
        this.customFieldService = customFieldService;
        this.billyConfiguration = billyConfiguration;
    }

    @Override
    public JsonNode buildInvoiceRequest(Invoice invoice, String customerErpId, String subsidiaryId, ZoneId zoneId) {
        var mapper = JacksonProvider.defaultMapper();
        var invoiceNode = JacksonProvider.defaultMapper().createObjectNode();
        invoiceNode.putIfAbsent(TRANSACTION_ID, TextNode.valueOf(invoice.getInvoiceNumber().toString()));
        invoiceNode.putIfAbsent(STATUS, TextNode.valueOf(OPEN_STATUS));

        invoiceNode.putIfAbsent(TRANSACTION_DATE, formatDate(invoice.getInvoiceDate(), zoneId));
        invoiceNode.putIfAbsent(DUE_DATE, formatDate(invoice.getDueDate(), zoneId));

        setInvoiceCustomBodyFields(invoiceNode, invoice, zoneId);

        var postingPeriod = mapper.createObjectNode();
        var offsetDateTime = toOffsetDateTime(invoice.getInvoiceDate(), zoneId);
        String postingPeriodDate = POSTING_PERIOD_FORMAT.format(offsetDateTime);
        postingPeriod.putIfAbsent(REF_NAME, TextNode.valueOf(postingPeriodDate));
        postingPeriod.putIfAbsent(TYPE, TextNode.valueOf(ACCOUNTING_PERIOD));
        invoiceNode.putIfAbsent(POSTING_PERIOD, postingPeriod);

        invoiceNode.putIfAbsent(SUBTOTAL, DoubleNode.valueOf(invoice.getSubTotal().doubleValue()));
        invoiceNode.putIfAbsent(TOTAL, DoubleNode.valueOf(invoice.getTotal().doubleValue()));
        invoiceNode.putIfAbsent(AMOUNT_REMAINING, DoubleNode.valueOf(invoice.getTotal().doubleValue()));
        invoiceNode.putIfAbsent(DISCOUNT_TOTAL, DoubleNode.valueOf(invoice.getTotalDiscount().doubleValue()));

        var currency = mapper.createObjectNode();
        currency.putIfAbsent(REF_NAME, TextNode.valueOf(invoice.getCurrencyCode()));
        invoiceNode.putIfAbsent(CURRENCY, currency);

        String paymentTerms = String.format(PAYMENT_TERM_FORMAT, invoice.getPaymentTerm().getPaymentDueInDays());
        var terms = mapper.createObjectNode();
        terms.putIfAbsent(REF_NAME, TextNode.valueOf(paymentTerms));
        invoiceNode.putIfAbsent(TERMS, terms);

        var customer = getCustomerNode(customerErpId);
        invoiceNode.putIfAbsent(CUSTOMER, customer);

        var subsidiary = mapper.createObjectNode();
        subsidiary.putIfAbsent(IDENTIFIER, TextNode.valueOf(subsidiaryId));
        invoiceNode.putIfAbsent(SUBSIDIARY, subsidiary);

        String emailList = getEmailList(invoice);
        invoiceNode.putIfAbsent(EMAIL, TextNode.valueOf(emailList));
        invoiceNode.putIfAbsent(TO_BE_EMAILED, BooleanNode.getFalse());
        invoiceNode.putIfAbsent(MEMO, TextNode.valueOf(invoice.getNote()));
        invoiceNode.putIfAbsent(OTHER_REF_NUM, TextNode.valueOf(invoice.getPurchaseOrderNumber()));

        var itemObject = buildInvoiceLineItemRequests(invoice);
        invoiceNode.putIfAbsent(ITEM, itemObject);

        return invoiceNode;
    }

    private String getEmailList(Invoice invoice) {
        var emailContacts = emailContactlistService.getEmailNotifiersDetail(invoice.getEmailNotifiersList().getToIds(), List.of(), List.of());
        var toEmailStream = emailContacts.getToContacts().stream().map(EmailNotifierContact::getEmail);
        return Stream.concat(Stream.of(invoice.getBillingContact().getEmail()), toEmailStream).distinct().collect(Collectors.joining(","));
    }

    private void setInvoiceCustomBodyFields(ObjectNode invoiceNode, Invoice invoice, ZoneId zoneId) {
        // subscription id
        invoiceNode.putIfAbsent(CUSTBODY_SUBSCRIPTION_ID, TextNode.valueOf(invoice.getSubscriptionId()));

        // contract start date
        Instant startDate = invoice
            .getInvoiceItems()
            .stream()
            .map(InvoiceItem::getPeriodStartDate)
            .min(DateTimeCalculator::compareInstants)
            .orElseThrow(() -> new ConflictingStateException("no line items present"));
        invoiceNode.putIfAbsent(CUSTBODY_START_DATE, formatDate(startDate, zoneId));

        // contract end date
        Instant endDate = invoice
            .getInvoiceItems()
            .stream()
            .map(InvoiceItem::getPeriodEndDate)
            .max(DateTimeCalculator::compareInstants)
            .orElseThrow(() -> new ConflictingStateException("no line items present"));
        String endDateStr = toOffsetDateTime(endDate, zoneId).minusDays(1).format(DATE_TIME_FORMATTER);
        invoiceNode.putIfAbsent(CUSTBODY_END_DATE, TextNode.valueOf(endDateStr));
    }

    @Override
    public JsonNode buildCustomerRequest(Account account, AccountContact billingContact, String subsidiaryId) {
        var mapper = JacksonProvider.defaultMapper();
        var customerNode = JacksonProvider.defaultMapper().createObjectNode();
        customerNode.putIfAbsent(COMPANY_NAME, TextNode.valueOf(account.getName()));
        customerNode.putIfAbsent(EMAIL, TextNode.valueOf(billingContact.getEmail()));
        customerNode.putIfAbsent(PHONE_NUMBER, TextNode.valueOf(account.getPhoneNumber()));

        var currency = mapper.createObjectNode();
        currency.putIfAbsent(REF_NAME, TextNode.valueOf(account.getCurrency().getCurrencyCode()));
        customerNode.putIfAbsent(CURRENCY, currency);

        var addressBook = buildAddressBook(account, billingContact);
        customerNode.putIfAbsent(ADDRESS_BOOK, addressBook);

        var subsidiary = mapper.createObjectNode();
        subsidiary.putIfAbsent(IDENTIFIER, TextNode.valueOf(subsidiaryId));
        customerNode.putIfAbsent(SUBSIDIARY, subsidiary);

        String accountLink = String.format(ACCOUNTS_PAGE_FORMAT, billyConfiguration.getSiteUrl(), account.getAccountId());
        customerNode.putIfAbsent(CUSTBODY_ACCOUNT_LINK, TextNode.valueOf(accountLink));
        customerNode.putIfAbsent(CUSTBODY_CRM_ID, TextNode.valueOf(account.getCrmId()));
        customerNode.putIfAbsent(IS_PERSON, BooleanNode.getFalse());

        return customerNode;
    }

    private JsonNode buildAddressBook(Account account, AccountContact billingContact) {
        var mapper = JacksonProvider.defaultMapper();

        AccountAddress address = accountGetService.getAccountAddress(account.getAddressId()).orElse(billingContact.getAddress());
        var addressNode = mapper.createObjectNode();
        addressNode.putIfAbsent(ADDR_1, TextNode.valueOf(address.getStreetAddressLine1()));
        addressNode.putIfAbsent(ADDR_2, TextNode.valueOf(address.getStreetAddressLine2()));
        addressNode.putIfAbsent(CITY, TextNode.valueOf(address.getCity()));
        addressNode.putIfAbsent(STATE, TextNode.valueOf(address.getState()));
        addressNode.putIfAbsent(ZIPCODE, TextNode.valueOf(address.getZipcode()));

        var country = mapper.createObjectNode();
        country.putIfAbsent(IDENTIFIER, TextNode.valueOf(address.getCountry()));
        addressNode.putIfAbsent(COUNTRY, country);

        var addressBookElement = mapper.createObjectNode();
        addressBookElement.putIfAbsent(DEFAULT_BILLING, BooleanNode.getTrue());
        addressBookElement.putIfAbsent(IS_RESIDENTIAL, BooleanNode.getFalse());
        addressBookElement.putIfAbsent(ADDRESS_BOOK_ADDRESS, addressNode);

        var items = mapper.createArrayNode();
        items.add(addressBookElement);

        var addressBook = mapper.createObjectNode();
        addressBook.putIfAbsent(ITEMS, items);

        return addressBook;
    }

    @Override
    public JsonNode buildAddSubsidiaryToCustomerRequest(Account account, String subsidiaryId) {
        var mapper = JacksonProvider.defaultMapper();
        var customerNode = JacksonProvider.defaultMapper().createObjectNode();

        var customer = mapper.createObjectNode();
        customer.putIfAbsent(IDENTIFIER, TextNode.valueOf(account.getErpId()));
        customerNode.putIfAbsent(CUSTOMER, customer);

        var subsidiary = mapper.createObjectNode();
        subsidiary.putIfAbsent(IDENTIFIER, TextNode.valueOf(subsidiaryId));
        customerNode.putIfAbsent(SUBSIDIARY, subsidiary);

        customerNode.putIfAbsent(IS_PRIMARY_SUB, BooleanNode.getFalse());

        return customerNode;
    }

    private record NetsuiteOrderLineCache(
        EntityCache<String, Charge> chargeCache,
        EntityCache<String, Optional<OrderLineItem>> orderLineCache,
        EntityCache<String, Optional<PriceAttribute>> priceAttributeCache,
        EntityCache<String, Optional<CustomField>> customFieldCache
    ) {}

    private JsonNode buildInvoiceLineItemRequests(Invoice invoice) {
        NetsuiteOrderLineCache lineItemCache = new NetsuiteOrderLineCache(
            EntityCache.of(productCatalogGetService::getChargeByChargeId),
            EntityCache.of(orderGetService::getOrderLineItemByOrderLineItemId),
            EntityCache.of(rateCardService::getPriceAttributeById),
            EntityCache.of(orderLineId -> customFieldService.getCustomFieldsOptional(CustomFieldParentType.ORDER_ITEM, orderLineId))
        );

        var mapper = JacksonProvider.defaultMapper();
        var items = mapper.createArrayNode();
        invoice
            .getInvoiceItems()
            .stream()
            .filter(invoiceItem -> invoiceItem.getQuantity() != 0)
            .forEach(invoiceItem -> {
                var item = buildInvoiceLineItemRequest(invoiceItem, lineItemCache);

                items.add(item);
            });
        var itemObject = mapper.createObjectNode();
        itemObject.putIfAbsent(ITEMS, items);
        return itemObject;
    }

    private JsonNode buildInvoiceLineItemRequest(
        InvoiceItem invoiceItem,
        NetsuiteOrderLineCache lineItemCache
    ) {
        var mapper = JacksonProvider.defaultMapper();
        var item = mapper.createObjectNode();

        Charge charge = lineItemCache.chargeCache.get(invoiceItem.getChargeId());
        if (StringUtils.isBlank(charge.getErpId())) {
            throw new ConflictingStateException(String.format("charge %s does not have an erp id", charge.getChargeId()));
        }
        var itemId = mapper.createObjectNode();
        itemId.putIfAbsent(IDENTIFIER, TextNode.valueOf(charge.getErpId()));
        item.putIfAbsent(ITEM, itemId);

        long quantity = Math.abs(invoiceItem.getQuantity());
        if (quantity != 0) {
            BigDecimal rate = Numbers.scaledDivide(invoiceItem.getAmount(), BigDecimal.valueOf(quantity));
            item.putIfAbsent(RATE, DoubleNode.valueOf(rate.doubleValue()));
        }
        item.putIfAbsent(AMOUNT, DoubleNode.valueOf(invoiceItem.getAmount().doubleValue()));
        item.putIfAbsent(QUANTITY, LongNode.valueOf(quantity));

        // add description
        String description = lineItemCache.orderLineCache
            .get(invoiceItem.getOrderLineItemId())
            .map(orderLineId -> getItemDescription(orderLineId, lineItemCache))
            .orElse(StringUtils.EMPTY);
        item.putIfAbsent(DESCRIPTION, TextNode.valueOf(description));

        return item;
    }

    private String getItemDescription(OrderLineItem lineItem, NetsuiteOrderLineCache lineItemCache) {
        Optional<String> rateCardDescription = getItemDescriptionFromRateCard(lineItem, lineItemCache.priceAttributeCache);
        Optional<String> yearSelections = getYearSelection(lineItem, lineItemCache.customFieldCache);

        StringBuilder description = new StringBuilder();
        rateCardDescription.ifPresent(description::append);
        if (rateCardDescription.isPresent() && yearSelections.isPresent()) {
            description.append(" - ");
        }
        yearSelections.ifPresent(description::append);
        return description.toString();
    }

    private Optional<String> getYearSelection(OrderLineItem lineItem, EntityCache<String, Optional<CustomField>> customFieldCache) {
        Optional<CustomField> customFieldOptional = customFieldCache.get(lineItem.getOrderLineId());
        if (customFieldOptional.isEmpty()) {
            return Optional.empty();
        }
        CustomField customFields = customFieldOptional.get();
        return customFields
            .getEntries()
            .values()
            .stream()
            .filter(value -> value.getName().equals(YEAR_CUSTOM_FIELD_NAME))
            .map(CustomFieldValue::getSelections)
            .filter(CollectionUtils::isNotEmpty)
            .findFirst()
            .map(years -> String.format("Years %s", String.join(",", years)));
    }

    private Optional<String> getItemDescriptionFromRateCard(
        OrderLineItem orderLine,
        EntityCache<String, Optional<PriceAttribute>> priceAttributeCache
    ) {
        var attributeReferences = orderLine.getAttributeReferences();
        if (CollectionUtils.isEmpty(attributeReferences)) {
            return Optional.empty();
        }

        Optional<String> sectorOptional = Optional.empty();
        Optional<String> tierOptional = Optional.empty();
        for (var attributeReference : attributeReferences) {
            PriceAttribute priceAttributeOptional = priceAttributeCache
                .get(attributeReference.getAttributeDefinitionId())
                .orElseThrow(() ->
                    new InvariantCheckFailedException(String.format("Price attribute %s not found", attributeReference.getAttributeDefinitionId()))
                );
            switch (priceAttributeOptional.getName()) {
                case SECTOR_ATTRIBUTE -> sectorOptional = Optional.of(attributeReference.getAttributeValue());
                case TIER_ATTRIBUTE -> tierOptional = Optional.of(attributeReference.getAttributeValue());
                default -> {}
            }
        }
        if (sectorOptional.isEmpty() || tierOptional.isEmpty()) {
            return Optional.empty();
        }
        String description = String.format("%s - EP %s", sectorOptional.get(), tierOptional.get());
        return Optional.of(description);
    }

    @Override
    public JsonNode buildCreditMemoRequest(CreditMemo creditMemo, String customerErpId, ZoneId zoneId) {
        var mapper = JacksonProvider.defaultMapper();
        var cmNode = JacksonProvider.defaultMapper().createObjectNode();
        cmNode.putIfAbsent(TRANSACTION_ID, TextNode.valueOf(creditMemo.getCreditMemoNumber()));
        cmNode.putIfAbsent(STATUS, TextNode.valueOf(OPEN_STATUS));

        cmNode.putIfAbsent(TRANSACTION_DATE, formatDate(creditMemo.getCreditMemoDate(), zoneId));

        var postingPeriod = mapper.createObjectNode();
        var offsetDateTime = toOffsetDateTime(creditMemo.getCreditMemoDate(), zoneId);
        String postingPeriodDate = POSTING_PERIOD_FORMAT.format(offsetDateTime);
        postingPeriod.putIfAbsent(REF_NAME, TextNode.valueOf(postingPeriodDate));
        postingPeriod.putIfAbsent(TYPE, TextNode.valueOf(ACCOUNTING_PERIOD));
        cmNode.putIfAbsent(POSTING_PERIOD, postingPeriod);

        cmNode.putIfAbsent(SUBTOTAL, DoubleNode.valueOf(creditMemo.getAmount().doubleValue()));
        cmNode.putIfAbsent(TOTAL, DoubleNode.valueOf(creditMemo.getAmount().doubleValue()));
        cmNode.putIfAbsent(AMOUNT_REMAINING, DoubleNode.valueOf(creditMemo.getAmount().doubleValue()));
        cmNode.putIfAbsent(UNAPPLIED, DoubleNode.valueOf(creditMemo.getAmount().doubleValue()));

        var currency = mapper.createObjectNode();
        currency.putIfAbsent(REF_NAME, TextNode.valueOf(creditMemo.getCurrencyCode()));
        cmNode.putIfAbsent(CURRENCY, currency);

        // temporary hard coded ID to unblock EP until we add this to subskribe entity.
        String subsidiaryId = getSubsidiaryId(creditMemo.getEntityId());
        var subsidiary = mapper.createObjectNode();
        subsidiary.putIfAbsent(IDENTIFIER, TextNode.valueOf(subsidiaryId));
        cmNode.putIfAbsent(SUBSIDIARY, subsidiary);

        // account id
        var customer = getCustomerNode(customerErpId);
        cmNode.putIfAbsent(CUSTOMER, customer);

        cmNode.putIfAbsent(TO_BE_EMAILED, BooleanNode.getFalse());

        var itemObject = buildCreditMemoLineItemRequests(creditMemo);
        cmNode.putIfAbsent(ITEM, itemObject);

        return cmNode;
    }

    private JsonNode buildCreditMemoLineItemRequests(CreditMemo creditMemo) {
        EntityCache<String, Charge> chargeCache = EntityCache.of(productCatalogGetService::getChargeByChargeId);

        var mapper = JacksonProvider.defaultMapper();
        var items = mapper.createArrayNode();
        creditMemo
            .getLineItems()
            .forEach(cmItem -> {
                var item = buildCreditMemoLineItemRequest(chargeCache, cmItem);

                items.add(item);
            });
        var itemObject = mapper.createObjectNode();
        itemObject.putIfAbsent(ITEMS, items);
        return itemObject;
    }

    private JsonNode buildCreditMemoLineItemRequest(EntityCache<String, Charge> chargeCache, CreditMemoLineItem cmItem) {
        var mapper = JacksonProvider.defaultMapper();
        var item = mapper.createObjectNode();

        Charge charge = chargeCache.get(cmItem.getChargeId());
        if (StringUtils.isBlank(charge.getErpId())) {
            throw new ConflictingStateException(String.format("charge %s does not have an erp id", charge.getChargeId()));
        }
        var itemId = mapper.createObjectNode();
        itemId.putIfAbsent(IDENTIFIER, TextNode.valueOf(charge.getErpId()));
        item.putIfAbsent(ITEM, itemId);

        item.putIfAbsent(AMOUNT, DoubleNode.valueOf(cmItem.getAmount().doubleValue()));
        return item;
    }

    @Override
    public JsonNode buildVoidInvoiceRequest(Invoice invoice, String customerErpId, ZoneId zoneId) {
        var mapper = JacksonProvider.defaultMapper();
        var cmNode = JacksonProvider.defaultMapper().createObjectNode();
        cmNode.putIfAbsent(TRANSACTION_ID, TextNode.valueOf(invoice.getInvoiceNumber().getNumber()));
        cmNode.putIfAbsent(STATUS, TextNode.valueOf(OPEN_STATUS));

        ZonedDateTime zonedInvoiceDate = ZonedDateTime.ofInstant(invoice.getInvoiceDate(), zoneId);
        cmNode.putIfAbsent(TRANSACTION_DATE, TextNode.valueOf(zonedInvoiceDate.format(DATE_TIME_FORMATTER)));

        var postingPeriod = mapper.createObjectNode();
        String postingPeriodDate = POSTING_PERIOD_FORMAT.format(zonedInvoiceDate);
        postingPeriod.putIfAbsent(REF_NAME, TextNode.valueOf(postingPeriodDate));
        postingPeriod.putIfAbsent(TYPE, TextNode.valueOf(ACCOUNTING_PERIOD));
        cmNode.putIfAbsent(POSTING_PERIOD, postingPeriod);

        cmNode.putIfAbsent(SUBTOTAL, DoubleNode.valueOf(invoice.getSubTotal().doubleValue()));
        cmNode.putIfAbsent(TOTAL, DoubleNode.valueOf(invoice.getTotal().doubleValue()));
        cmNode.putIfAbsent(AMOUNT_REMAINING, DoubleNode.valueOf(invoice.getTotal().doubleValue()));
        cmNode.putIfAbsent(UNAPPLIED, DoubleNode.valueOf(invoice.getTotal().doubleValue()));

        setInvoiceCustomBodyFields(cmNode, invoice, zoneId);

        var currency = mapper.createObjectNode();
        currency.putIfAbsent(REF_NAME, TextNode.valueOf(invoice.getCurrencyCode()));
        cmNode.putIfAbsent(CURRENCY, currency);

        // temporary hard coded ID to unblock EP until we add this to subskribe entity.
        String subsidiaryId = getSubsidiaryId(invoice.getEntityId());
        var subsidiary = mapper.createObjectNode();
        subsidiary.putIfAbsent(IDENTIFIER, TextNode.valueOf(subsidiaryId));
        cmNode.putIfAbsent(SUBSIDIARY, subsidiary);

        // account id
        var customer = getCustomerNode(customerErpId);
        cmNode.putIfAbsent(CUSTOMER, customer);

        cmNode.putIfAbsent(TO_BE_EMAILED, BooleanNode.getFalse());
        cmNode.putIfAbsent(MEMO, TextNode.valueOf(invoice.getNote()));

        var itemObject = buildInvoiceLineItemRequests(invoice);
        cmNode.putIfAbsent(ITEM, itemObject);

        return cmNode;
    }

    private JsonNode getCustomerNode(String customerErpId) {
        var mapper = JacksonProvider.defaultMapper();
        var entity = mapper.createObjectNode();
        entity.putIfAbsent(IDENTIFIER, TextNode.valueOf(customerErpId));
        return entity;
    }

    private String getSubsidiaryId(String entityId) {
        Entity entity = entityGetService.getEntityById(entityId);
        if (StringUtils.isBlank(entity.getErpId())) {
            throw new ConflictingStateException(String.format("entity %s does not have an erp id", entity.getEntityId()));
        }
        return entity.getErpId();
    }
}
