package com.subskribe.billy.erp.quickbooks.service;

import static com.subskribe.billy.erp.ErpProductMetadata.QUICKBOOKS_FEATURE;
import static com.subskribe.billy.erp.ErpProductMetadata.QUICKBOOKS_PING_FAILED;
import static com.subskribe.billy.erp.service.ErpSyncService.JOURNAL_ENTRY_PAGE_SIZE;

import com.intuit.ipp.core.Context;
import com.intuit.ipp.core.ServiceType;
import com.intuit.ipp.data.CompanyInfo;
import com.intuit.ipp.data.Customer;
import com.intuit.ipp.data.EntityTypeEnum;
import com.intuit.ipp.data.EntityTypeRef;
import com.intuit.ipp.data.JournalEntryLineDetail;
import com.intuit.ipp.data.Line;
import com.intuit.ipp.data.LineDetailTypeEnum;
import com.intuit.ipp.data.PostingTypeEnum;
import com.intuit.ipp.data.ReferenceType;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.security.OAuth2Authorizer;
import com.intuit.ipp.services.DataService;
import com.intuit.ipp.services.QueryResult;
import com.intuit.ipp.util.Config;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.JournalEntry;
import com.subskribe.billy.accounting.model.JournalEntryPageToken;
import com.subskribe.billy.accounting.model.JournalLine;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.erp.externaljounralentrymapping.model.ExternalJournalEntryMapping;
import com.subskribe.billy.erp.externaljounralentrymapping.service.ExternalJournalEntryMappingService;
import com.subskribe.billy.erp.quickbooks.QuickbooksConfiguration;
import com.subskribe.billy.erp.service.ErpSyncService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.ErpSyncException;
import com.subskribe.billy.integration.IntegrationTargetService;
import com.subskribe.billy.integration.model.Integration;
import com.subskribe.billy.shared.logger.ErrorContext;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.tenant.services.TenantSettingService;
import dev.failsafe.Failsafe;
import dev.failsafe.RetryPolicy;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.time.Duration;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class QuickbooksDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(QuickbooksDataService.class);
    private static final String LEDGER_ACCOUNT_FIND_ALL_REQUEST_ID = "LEDGER_ACCOUNT_FIND_ALL";
    private static final String CREATE_JE_REQUEST_ID_FORMAT = "create-je-%s";
    private static final String CREATE_CUSTOMER_ACCOUNT_REQUEST_ID_FORMAT = "create-customer-%s";
    private static final String DELETE_JOURNAL_ENTRY_REQUEST_ID_FORMAT = "delete-je-%s";
    private static final String OBJECT_NOT_FOUND_ERROR_CODE = "610";
    private static final int MAX_SELECT_LIMIT = 1000;
    private static final String SELECT_ACCOUNT_QUERY = "SELECT * FROM Account MAXRESULTS %s";
    private static final String SELECT_COMPANY_INFO_QUERY = "SELECT * FROM CompanyInfo MAXRESULTS 1";
    private static final String QUOTE_PATTERN = "'";
    private static final String QUOTE_ESCAPED = "\\\\'";
    private static final int JE_SYNC_MAX_RETRY = 3;
    private static final long JE_SYNC_DELAY_MS = 1000;
    private static final long JE_SYNC_JITTER_MS = 500;
    private static final RetryPolicy<Object> JE_SYNC_RETRY_POLICY = RetryPolicy.builder()
        .handle(SocketException.class)
        .handle(SocketTimeoutException.class)
        .withMaxRetries(JE_SYNC_MAX_RETRY)
        .withDelay(Duration.ofMillis(JE_SYNC_DELAY_MS))
        .withJitter(Duration.ofMillis(JE_SYNC_JITTER_MS))
        .build();

    private final QuickbooksAuthService quickbooksAuthService;
    private final AccountingGetService accountingGetService;
    private final AccountGetService accountGetService;
    private final TenantSettingService tenantSettingService;
    private final ExternalJournalEntryMappingService externalJournalEntryMappingService;
    private final AccountService accountService;
    private final QuickbooksConfiguration quickbooksConfiguration;

    @Inject
    public QuickbooksDataService(
        QuickbooksAuthService quickbooksAuthService,
        AccountingGetService accountingGetService,
        AccountGetService accountGetService,
        TenantSettingService tenantSettingService,
        ExternalJournalEntryMappingService externalJournalEntryMappingService,
        AccountService accountService,
        BillyConfiguration billyConfiguration
    ) {
        this.quickbooksAuthService = quickbooksAuthService;
        this.accountingGetService = accountingGetService;
        this.accountGetService = accountGetService;
        this.tenantSettingService = tenantSettingService;
        this.externalJournalEntryMappingService = externalJournalEntryMappingService;
        this.accountService = accountService;
        quickbooksConfiguration = billyConfiguration.getQuickbooksConfiguration();
    }

    /*
     provide a data service instance to make a request to quickbooks api
     @requestId - idempotency key to uniquely identify the request
        (https://help.developer.intuit.com/s/article/What-is-RequestId-and-its-usage)
    */
    private DataService provideDataServiceInstance(Integration integration, String requestId) {
        try {
            String accessToken = quickbooksAuthService.getAccessToken(Optional.of(integration.getTenantId()));
            Config.setProperty(Config.BASE_URL_QBO, quickbooksConfiguration.getBaseUrl());
            OAuth2Authorizer oauth = new OAuth2Authorizer(accessToken);
            Context context = new Context(oauth, ServiceType.QBO, integration.getRealmId());
            if (StringUtils.isNotBlank(requestId)) {
                context.setRequestID(requestId);
            }
            return new DataService(context);
        } catch (FMSException e) {
            LOGGER.error("unable to initialize quickbooks client", e);
            throw new ErpSyncException("unable to initialize quickbooks client", e);
        }
    }

    /*
        prepare cache of ledger accounts from quickbooks
        read journal entries, in batches (using pagination)
        send each journal entry to quickbooks
        use an idempotency key (request id) to allow for retries
     */
    public void sendJournalEntries(AccountingPeriod accountingPeriod) {
        ZoneId tenantZoneId = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        Integration integration = quickbooksAuthService
            .getIntegration()
            .orElseThrow(() -> new IllegalStateException("no integration found for " + IntegrationTargetService.QUICKBOOKS));
        Period accountingPeriodInterval = Period.between(accountingPeriod.getStartDate(), accountingPeriod.getEndDate());
        Map<String, com.intuit.ipp.data.Account> targetLedgerAccountCache = prepareTargetLedgerAccountCache(integration);

        LOGGER.info(
            "sending journal entries to quickbooks for accounting period: {} from {} to {}",
            accountingPeriod.getAccountingPeriodId(),
            accountingPeriod.getStartDate(),
            accountingPeriod.getEndDate()
        );

        JournalEntryPageToken pageToken = null;
        int pageIndex = 1;
        do {
            PageResult<List<JournalEntry>, JournalEntryPageToken> pageResult = accountingGetService.getJournalEntriesOrderedByAccountingDate(
                PageRequest.from(pageToken, JOURNAL_ENTRY_PAGE_SIZE),
                accountingPeriodInterval
            );

            if (CollectionUtils.isNotEmpty(pageResult.getResult())) {
                List<JournalEntry> journalEntries = pageResult.getResult();
                LOGGER.info("Page {}. Sending {} journal entries", pageIndex++, journalEntries.size());
                syncJournalEntriesPage(journalEntries, integration, targetLedgerAccountCache, tenantZoneId);
            }
            pageToken = pageResult.getNextPageToken();
        } while (pageToken != null);
        LOGGER.info("successfully created journal entries on quickbooks for accounting period: {}", accountingPeriod.getAccountingPeriodId());
    }

    private void syncJournalEntriesPage(
        List<JournalEntry> journalEntries,
        Integration integration,
        Map<String, com.intuit.ipp.data.Account> targetLedgerAccountCache,
        ZoneId tenantZoneId
    ) {
        Set<String> syncedJournalEntryIds = externalJournalEntryMappingService.getSyncedJournalEntryIds(
            integration.getIntegrationId(),
            journalEntries
        );

        journalEntries
            .stream()
            .filter(journalEntry -> {
                if (syncedJournalEntryIds.contains(journalEntry.getJournalEntryId())) {
                    LOGGER.info("Journal entry {} is already synced, skipping", journalEntry.getJournalEntryId());
                    return false;
                }
                return true;
            })
            .forEach(journalEntry -> sendJournalEntry(integration, targetLedgerAccountCache, tenantZoneId, journalEntry));
    }

    private void sendJournalEntry(
        Integration integration,
        Map<String, com.intuit.ipp.data.Account> targetLedgerAccountCache,
        ZoneId tenantZoneId,
        JournalEntry inputJournalEntry
    ) {
        String requestId = String.format(CREATE_JE_REQUEST_ID_FORMAT, inputJournalEntry.getJournalEntryId());
        DataService dataService = provideDataServiceInstance(integration, requestId);
        com.intuit.ipp.data.JournalEntry targetJournalEntry = prepareTargetJournalEntry(
            integration,
            targetLedgerAccountCache,
            tenantZoneId,
            inputJournalEntry
        );

        com.intuit.ipp.data.JournalEntry response = Failsafe.with(JE_SYNC_RETRY_POLICY).get(() -> dataService.add(targetJournalEntry));
        externalJournalEntryMappingService.createJournalEntryMapping(
            integration.getIntegrationId(),
            inputJournalEntry.getJournalEntryId(),
            response.getId()
        );
    }

    public void pingQuickbooks() {
        Optional<Integration> integrationOptional = quickbooksAuthService.getIntegration();
        if (integrationOptional.isEmpty()) {
            return;
        }
        Integration integration = integrationOptional.get();
        if (getCompanyInfo(integration).isEmpty()) {
            LOGGER.error(new ErrorContext(QUICKBOOKS_FEATURE, QUICKBOOKS_PING_FAILED), QUICKBOOKS_PING_FAILED.description());
        }
    }

    private Optional<String> getCompanyInfo(Integration integration) {
        try {
            DataService dataService = provideDataServiceInstance(integration, null);
            QueryResult queryResult = dataService.executeQuery(SELECT_COMPANY_INFO_QUERY);
            CompanyInfo companyInfo = (CompanyInfo) queryResult.getEntities().get(0);
            return Optional.ofNullable(companyInfo.getCompanyName());
        } catch (FMSException e) {
            // do not throw exception as this is a ping operation
            LOGGER.info("unable to fetch company info from quickbooks", e);
        }
        return Optional.empty();
    }

    private Map<String, com.intuit.ipp.data.Account> prepareTargetLedgerAccountCache(Integration integration) {
        try {
            DataService dataService = provideDataServiceInstance(integration, LEDGER_ACCOUNT_FIND_ALL_REQUEST_ID);
            String query = String.format(SELECT_ACCOUNT_QUERY, MAX_SELECT_LIMIT);
            QueryResult queryResult = dataService.executeQuery(query);
            List<com.intuit.ipp.data.Account> targetLedgerAccounts = new ArrayList<>();
            if (Objects.nonNull(queryResult.getEntities())) {
                for (Object entity : queryResult.getEntities()) {
                    if (entity instanceof com.intuit.ipp.data.Account targetLedgerAccount) {
                        targetLedgerAccounts.add(targetLedgerAccount);
                    }
                }
            }
            LOGGER.info("quickbooks target ledger accounts retrieved: {}", targetLedgerAccounts.size());
            if (targetLedgerAccounts.size() >= MAX_SELECT_LIMIT) {
                LOGGER.warn(QUICKBOOKS_FEATURE, "quickbooks target ledger accounts limit exceeded. tenant id: {}", integration.getTenantId());
            }
            return targetLedgerAccounts
                .stream()
                .filter(targetAccount -> StringUtils.isNotBlank(targetAccount.getAcctNum()))
                .collect(Collectors.toMap(com.intuit.ipp.data.Account::getAcctNum, Function.identity()));
        } catch (FMSException e) {
            LOGGER.info("unable to fetch ledger accounts from quickbooks", e);
            throw new ErpSyncException(e);
        }
    }

    private com.intuit.ipp.data.JournalEntry prepareTargetJournalEntry(
        Integration integration,
        Map<String, com.intuit.ipp.data.Account> targetLedgerAccountCache,
        ZoneId tenantZoneId,
        JournalEntry inputJournalEntry
    ) {
        com.intuit.ipp.data.JournalEntry targetJournalEntry = new com.intuit.ipp.data.JournalEntry();
        ZonedDateTime accountingDateZoned = ZonedDateTime.ofInstant(inputJournalEntry.getAccountingDate(), tenantZoneId);
        targetJournalEntry.setTxnDate(DateTimeCalculator.representZonedTimeInJavaDate(accountingDateZoned));
        List<Line> targetJournalLines = inputJournalEntry
            .getJournalLines()
            .stream()
            .map(sourceJournalLine -> getTargetJournalLine(integration, targetLedgerAccountCache, inputJournalEntry, sourceJournalLine))
            .collect(Collectors.toList());
        targetJournalEntry.setLine(targetJournalLines);
        targetJournalEntry.setPrivateNote(ErpSyncService.getJournalEntryNote(inputJournalEntry.getJournalEntryId()));
        targetJournalEntry.setDomain("QBO");

        return targetJournalEntry;
    }

    private Line getTargetJournalLine(
        Integration integration,
        Map<String, com.intuit.ipp.data.Account> targetLedgerAccountCache,
        JournalEntry inputJournalEntry,
        JournalLine sourceJournalLine
    ) {
        Line targetJournalLine = new Line();
        targetJournalLine.setDetailType(LineDetailTypeEnum.JOURNAL_ENTRY_LINE_DETAIL);
        JournalEntryLineDetail journalEntryLineDetail = new JournalEntryLineDetail();
        switch (sourceJournalLine.getEntryType()) {
            case CREDIT -> journalEntryLineDetail.setPostingType(PostingTypeEnum.CREDIT);
            case DEBIT -> journalEntryLineDetail.setPostingType(PostingTypeEnum.DEBIT);
            default -> throw new ConflictingStateException("unknown journal entry type: " + sourceJournalLine.getEntryType());
        }

        // send account details for all journal entries
        journalEntryLineDetail.setEntity(getCustomerAccountEntity(integration, inputJournalEntry));

        ReferenceType accountReference = getLedgerAccountReference(targetLedgerAccountCache, sourceJournalLine);
        journalEntryLineDetail.setAccountRef(accountReference);
        targetJournalLine.setJournalEntryLineDetail(journalEntryLineDetail);
        String journalLineDescription = ErpSyncService.getSubskribeTransactionNote(inputJournalEntry.getSourceTransactionId());
        targetJournalLine.setDescription(journalLineDescription);
        targetJournalLine.setAmount(sourceJournalLine.getAmount());
        return targetJournalLine;
    }

    private EntityTypeRef getCustomerAccountEntity(Integration integration, JournalEntry journalEntry) {
        // sync and get customer object
        Account sourceCustomerAccount = accountGetService.getAccount(journalEntry.getAccountId());
        Customer targetCustomerAccount = getAndSyncCustomerAccount(integration, sourceCustomerAccount);

        // wrap inside reference type object
        ReferenceType targetCustomerAccountReference = new ReferenceType();
        targetCustomerAccountReference.setValue(targetCustomerAccount.getId());
        targetCustomerAccountReference.setName(targetCustomerAccount.getDisplayName());

        // wrap inside entity type object
        EntityTypeRef targetCustomerAccountEntity = new EntityTypeRef();
        targetCustomerAccountEntity.setType(EntityTypeEnum.CUSTOMER);
        targetCustomerAccountEntity.setEntityRef(targetCustomerAccountReference);
        return targetCustomerAccountEntity;
    }

    private Customer getAndSyncCustomerAccount(Integration integration, Account sourceCustomerAccount) {
        Optional<Customer> targetCustomerAccountOptional = StringUtils.isNotBlank(sourceCustomerAccount.getErpId())
            ? getTargetCustomerAccountByErpId(integration, sourceCustomerAccount.getErpId())
            : getTargetCustomerAccountByName(integration, sourceCustomerAccount.getName());
        return targetCustomerAccountOptional.orElseGet(() -> createAndSyncCustomerAccount(integration, sourceCustomerAccount));
    }

    private Customer createAndSyncCustomerAccount(Integration integration, Account sourceCustomerAccount) {
        try {
            return createTargetCustomerAccount(integration, sourceCustomerAccount);
        } catch (FMSException e) {
            String message = String.format("unable to sync account %s to quickbooks: %s", sourceCustomerAccount.getAccountId(), e.getMessage());
            LOGGER.info(message, e);
            throw new ErpSyncException(message, e);
        }
    }

    private Optional<Customer> getTargetCustomerAccountByErpId(Integration integration, String erpId) {
        DataService service = provideDataServiceInstance(integration, null);
        Customer findCustomer = new Customer();
        findCustomer.setId(erpId);
        try {
            Customer targetCustomerAccount = service.findById(findCustomer);
            if (targetCustomerAccount == null) {
                LOGGER.info(String.format("no customer found with erp id: %s", erpId));
                return Optional.empty();
            }
            return Optional.of(targetCustomerAccount);
        } catch (FMSException e) {
            String message = String.format("error trying to fetch customer account by erp id: %s", erpId);
            LOGGER.info(message, e);
            throw new ErpSyncException(message, e);
        }
    }

    private Optional<Customer> getTargetCustomerAccountByName(Integration integration, String name) {
        try {
            name = name.replaceAll(QUOTE_PATTERN, QUOTE_ESCAPED);
            String sql = String.format("select * from customer where DisplayName = '%s'", name);
            DataService service = provideDataServiceInstance(integration, null);
            QueryResult queryResult = service.executeQuery(sql);
            if (CollectionUtils.isEmpty(queryResult.getEntities())) {
                LOGGER.info(String.format("no customer found with name: %s", name));
                return Optional.empty();
            }
            return Optional.ofNullable((Customer) queryResult.getEntities().get(0));
        } catch (FMSException e) {
            String message = String.format("error trying to fetch customer account by name: %s", name);
            LOGGER.info(message, e);
            throw new ErpSyncException(message, e);
        }
    }

    private Customer createTargetCustomerAccount(Integration integration, Account sourceCustomerAccount) throws FMSException {
        String requestId = String.format(CREATE_CUSTOMER_ACCOUNT_REQUEST_ID_FORMAT, sourceCustomerAccount.getAccountId());
        DataService dataService = provideDataServiceInstance(integration, requestId);
        Customer targetCustomerAccount = new Customer();
        targetCustomerAccount.setDisplayName(sourceCustomerAccount.getName());
        targetCustomerAccount.setOrganization(true);
        Customer newCustomer = dataService.add(targetCustomerAccount);
        LOGGER.info("created customer account {} on quickbooks: {}", sourceCustomerAccount.getAccountId(), newCustomer.getId());

        accountService.updateAccountErpId(sourceCustomerAccount.getAccountId(), newCustomer.getId(), false);

        return newCustomer;
    }

    private ReferenceType getLedgerAccountReference(
        Map<String, com.intuit.ipp.data.Account> targetLedgerAccountCache,
        JournalLine sourceJournalLine
    ) {
        ReferenceType accountReference = new ReferenceType();
        if (!targetLedgerAccountCache.containsKey(sourceJournalLine.getLedgerAccountCode())) {
            throw new ConflictingStateException("unable to find GL account in Quickbooks: " + sourceJournalLine.getLedgerAccountCode());
        }
        com.intuit.ipp.data.Account targetAccount = targetLedgerAccountCache.get(sourceJournalLine.getLedgerAccountCode());
        accountReference.setName(targetAccount.getName());
        accountReference.setValue(targetAccount.getId());
        return accountReference;
    }

    public void deleteJournalEntries(AccountingPeriod accountingPeriod) throws FMSException {
        Integration integration = quickbooksAuthService
            .getIntegration()
            .orElseThrow(() -> new IllegalStateException("no integration found for " + IntegrationTargetService.QUICKBOOKS));
        Period accountingPeriodInterval = Period.between(accountingPeriod.getStartDate(), accountingPeriod.getEndDate());

        LOGGER.info(
            "deleting journal entries from quickbooks for accounting period: {} from {} to {}",
            accountingPeriod.getAccountingPeriodId(),
            accountingPeriod.getStartDate(),
            accountingPeriod.getEndDate()
        );

        JournalEntryPageToken pageToken = null;
        int pageIndex = 1;
        do {
            PageResult<List<JournalEntry>, JournalEntryPageToken> pageResult = accountingGetService.getJournalEntriesOrderedByAccountingDate(
                PageRequest.from(pageToken, JOURNAL_ENTRY_PAGE_SIZE),
                accountingPeriodInterval
            );

            if (CollectionUtils.isNotEmpty(pageResult.getResult())) {
                List<JournalEntry> journalEntries = pageResult.getResult();
                LOGGER.info("page {}. deleting {} journal entries", pageIndex++, journalEntries.size());
                deleteJournalEntriesPage(integration, journalEntries);
            }
            pageToken = pageResult.getNextPageToken();
        } while (pageToken != null);
        LOGGER.info("successfully deleted journal entries from quickbooks for accounting period: {}", accountingPeriod.getAccountingPeriodId());
    }

    private void deleteJournalEntriesPage(Integration integration, List<JournalEntry> journalEntries) throws FMSException {
        List<String> journalEntryIds = journalEntries.stream().map(JournalEntry::getJournalEntryId).collect(Collectors.toList());
        List<ExternalJournalEntryMapping> journalEntryMappings = externalJournalEntryMappingService.getJournalEntryMappings(
            integration.getIntegrationId(),
            journalEntryIds
        );
        for (ExternalJournalEntryMapping journalEntryMapping : journalEntryMappings) {
            deleteJournalEntry(integration, journalEntryMapping);
        }
    }

    public void deleteJournalEntriesByIds(String tenantId, List<String> externalIds) {
        Integration integration = quickbooksAuthService
            .getIntegrationByTenantId(tenantId)
            .orElseThrow(() -> new IllegalStateException("no integration found for " + tenantId));
        for (String externalId : externalIds) {
            try {
                deleteExternalJournalEntry(integration, externalId);
            } catch (FMSException e) {
                if (CollectionUtils.isNotEmpty(e.getErrorList()) && Objects.equals(e.getErrorList().get(0).getCode(), OBJECT_NOT_FOUND_ERROR_CODE)) {
                    LOGGER.info("delete journal entries: journal entry not present on quickbooks. id = {}", externalId);
                    return;
                }
                String message = "unable to delete journal entry from quickbooks. id = " + externalId;
                LOGGER.info(message, e);
                throw new ErpSyncException(e);
            }
        }
    }

    public void cleanupJournalEntries(String tenantId, String integrationId) {
        List<ExternalJournalEntryMapping> journalEntryMappings = externalJournalEntryMappingService.getJournalEntryMappingsForCleanup(
            tenantId,
            integrationId
        );
        if (CollectionUtils.isNotEmpty(journalEntryMappings)) {
            List<String> externalIds = journalEntryMappings.stream().map(ExternalJournalEntryMapping::getExternalJournalEntryId).toList();
            deleteJournalEntriesByIds(tenantId, externalIds);
            journalEntryMappings.forEach(externalJournalEntryMapping ->
                externalJournalEntryMappingService.markMappingDeleted(externalJournalEntryMapping.getId(), Optional.of(tenantId))
            );
        }
        LOGGER.info(
            "journal entry cleanup completed. tenant id: {}, integration id: {}, deleted: {}",
            tenantId,
            integrationId,
            journalEntryMappings.size()
        );
    }

    private void deleteJournalEntry(Integration integration, ExternalJournalEntryMapping journalEntryMapping) throws FMSException {
        deleteExternalJournalEntry(integration, journalEntryMapping.getExternalJournalEntryId());
        externalJournalEntryMappingService.markMappingDeleted(journalEntryMapping.getId(), Optional.empty());
    }

    private void deleteExternalJournalEntry(Integration integration, String externalId) throws FMSException {
        DataService readDataService = provideDataServiceInstance(integration, null);
        com.intuit.ipp.data.JournalEntry targetJournalEntry = new com.intuit.ipp.data.JournalEntry();
        targetJournalEntry.setId(externalId);
        targetJournalEntry = readDataService.findById(targetJournalEntry);
        String requestId = String.format(DELETE_JOURNAL_ENTRY_REQUEST_ID_FORMAT, externalId);
        DataService deleteDataService = provideDataServiceInstance(integration, requestId);
        deleteDataService.delete(targetJournalEntry);
    }
}
