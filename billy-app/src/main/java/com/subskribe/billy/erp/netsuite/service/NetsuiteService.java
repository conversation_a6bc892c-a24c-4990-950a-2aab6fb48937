package com.subskribe.billy.erp.netsuite.service;

import static com.subskribe.billy.erp.service.ErpSyncService.JOURNAL_ENTRY_PAGE_SIZE;
import static com.subskribe.billy.integration.IntegrationTargetService.NETSUITE;

import com.fasterxml.jackson.databind.JsonNode;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.JournalEntry;
import com.subskribe.billy.accounting.model.JournalEntryPageToken;
import com.subskribe.billy.accounting.model.JournalLine;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.erp.externaljounralentrymapping.model.ExternalJournalEntryMapping;
import com.subskribe.billy.erp.externaljounralentrymapping.service.ExternalJournalEntryMappingService;
import com.subskribe.billy.erp.model.ErpInvoiceJson;
import com.subskribe.billy.erp.model.ImmutableErpInvoiceJson;
import com.subskribe.billy.erp.netsuite.config.NetsuiteConfiguration;
import com.subskribe.billy.erp.netsuite.config.NetsuitePayloadMapperType;
import com.subskribe.billy.erp.netsuite.mappers.DefaultNetsuitePayloadMapper;
import com.subskribe.billy.erp.netsuite.mappers.EducationPerfectNetsuitePayloadMapper;
import com.subskribe.billy.erp.netsuite.mappers.NetsuitePayloadMapper;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.model.CreditMemoStatus;
import com.subskribe.billy.invoicesettlement.services.CreditMemoRetrievalService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoService;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

public class NetsuiteService {

    private static final Logger LOGGER = LoggerFactory.getLogger(NetsuiteService.class);

    private final InvoiceRetrievalService invoiceRetrievalService;
    private final CreditMemoRetrievalService creditMemoRetrievalService;
    private final TenantSettingService tenantSettingService;
    private final InvoiceService invoiceService;
    private final CreditMemoService creditMemoService;
    private final AccountGetService accountGetService;
    private final AccountService accountService;
    private final FeatureService featureService;
    private final EntityGetService entityGetService;
    private final AccountingGetService accountingGetService;
    private final ExternalJournalEntryMappingService externalJournalEntryMappingService;
    private final NetsuiteClient netsuiteClient;
    private final EducationPerfectNetsuitePayloadMapper educationPerfectNetsuitePayloadMapper;
    private final DefaultNetsuitePayloadMapper defaultNetsuitePayloadMapper;
    private final BillyConfiguration billyConfiguration;
    private final TenantIdProvider tenantIdProvider;

    @Inject
    public NetsuiteService(
        InvoiceRetrievalService invoiceRetrievalService,
        CreditMemoRetrievalService creditMemoRetrievalService,
        TenantSettingService tenantSettingService,
        InvoiceService invoiceService,
        CreditMemoService creditMemoService,
        AccountGetService accountGetService,
        AccountService accountService,
        FeatureService featureService,
        EntityGetService entityGetService,
        AccountingGetService accountingGetService,
        ExternalJournalEntryMappingService externalJournalEntryMappingService,
        NetsuiteClient netsuiteClient,
        EducationPerfectNetsuitePayloadMapper educationPerfectNetsuitePayloadMapper,
        DefaultNetsuitePayloadMapper defaultNetsuitePayloadMapper,
        BillyConfiguration billyConfiguration,
        TenantIdProvider tenantIdProvider
    ) {
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.creditMemoRetrievalService = creditMemoRetrievalService;
        this.tenantSettingService = tenantSettingService;
        this.invoiceService = invoiceService;
        this.creditMemoService = creditMemoService;
        this.accountGetService = accountGetService;
        this.accountService = accountService;
        this.featureService = featureService;
        this.entityGetService = entityGetService;
        this.accountingGetService = accountingGetService;
        this.externalJournalEntryMappingService = externalJournalEntryMappingService;
        this.netsuiteClient = netsuiteClient;
        this.educationPerfectNetsuitePayloadMapper = educationPerfectNetsuitePayloadMapper;
        this.defaultNetsuitePayloadMapper = defaultNetsuitePayloadMapper;
        this.billyConfiguration = billyConfiguration;
        this.tenantIdProvider = tenantIdProvider;
    }

    private void checkFeatureEnabled() {
        if (!featureService.isEnabled(Feature.NETSUITE_INVOICE_SYNC)) {
            throw new UnsupportedOperationException("Netsuite invoice sync not enabled");
        }
    }

    private NetsuiteConfiguration getNetsuiteConfiguration() {
        BillyConfiguration tenantConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantIdProvider.provideTenantIdString());
        return tenantConfig.getNetsuiteConfiguration();
    }

    private void checkInvoiceSyncEnabled() {
        if (!getNetsuiteConfiguration().getInvoiceSyncEnabled()) {
            throw new ConflictingStateException("Netsuite invoice sync not enabled");
        }
    }

    private void checkJournalEntrySyncEnabled() {
        if (!getNetsuiteConfiguration().getJournalEntrySyncEnabled()) {
            throw new ConflictingStateException("Netsuite journal entry sync not enabled");
        }
    }

    private NetsuitePayloadMapper payloadMapper() {
        NetsuitePayloadMapperType payloadMapperType = getNetsuiteConfiguration().getPayloadMapper();
        return switch (payloadMapperType) {
            case EDUCATION_PERFECT -> educationPerfectNetsuitePayloadMapper;
            case DEFAULT -> defaultNetsuitePayloadMapper;
            default -> throw new UnsupportedOperationException(String.format("Unsupported payload mapper %s", payloadMapperType));
        };
    }

    public void pushInvoice(String invoiceNumber) {
        checkFeatureEnabled();
        checkInvoiceSyncEnabled();
        Invoice invoice = invoiceRetrievalService.getInvoice(new Invoice.Number(invoiceNumber));
        if (invoice.getStatus() != InvoiceStatus.POSTED) {
            throw new ConflictingStateException(String.format("invoice %s must be POSTED", invoiceNumber));
        }
        if (StringUtils.isNotBlank(invoice.getErpId())) {
            LOGGER.info("skipping invoice {} sync to epr because it already has erp id {}", invoiceNumber, invoice.getErpId());
            return;
        }
        ZoneId zoneId = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();

        String subsidiaryId = getSubsidiaryId(invoice.getEntityId());
        String customerErpId = getAccountErpIdCreateIfNeeded(invoice.getCustomerAccountId(), invoice.getBillingContact(), subsidiaryId);

        JsonNode invoiceRequest = payloadMapper().buildInvoiceRequest(invoice, customerErpId, subsidiaryId, zoneId);
        String invoiceErpId = netsuiteClient.syncInvoiceToNetsuite(invoiceRequest, invoice);
        invoiceService.updateInvoiceErpId(invoice.getInvoiceNumber(), invoiceErpId);
    }

    public ErpInvoiceJson getNetsuiteInvoice(String invoiceErpId) {
        if (invoiceErpId.contains(",")) {
            int indexOfComma = invoiceErpId.indexOf(",");
            invoiceErpId = invoiceErpId.substring("INV: ".length(), indexOfComma);
        }
        JsonNode invoiceNode = getInvoiceFromNetsuite(invoiceErpId);

        return ImmutableErpInvoiceJson.builder()
            .id(invoiceErpId)
            .erpInvoiceNumber(invoiceNode.has("tranId") ? invoiceNode.get("tranId").asText() : StringUtils.EMPTY)
            .status(invoiceNode.get("status").get("refName").asText())
            .amountPaid(invoiceNode.get("amountPaid").decimalValue())
            .amountRemaining(invoiceNode.get("amountRemaining").decimalValue())
            .subtotal(invoiceNode.get("subtotal").decimalValue())
            .total(invoiceNode.get("total").decimalValue())
            .build();
    }

    public void pushCreditMemo(String creditMemoNumber) {
        checkFeatureEnabled();
        checkInvoiceSyncEnabled();
        CreditMemo creditMemo = creditMemoRetrievalService.getCreditMemoByNumber(creditMemoNumber);
        if (creditMemo.getStatus() != CreditMemoStatus.POSTED) {
            throw new ConflictingStateException(String.format("credit memo %s must be POSTED", creditMemo));
        }
        if (StringUtils.isNotBlank(creditMemo.getErpId())) {
            LOGGER.info("skipping credit memo {} sync to epr because it already has erp id {}", creditMemoNumber, creditMemo.getErpId());
            return;
        }
        ZoneId zoneId = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();

        String subsidiaryId = getSubsidiaryId(creditMemo.getEntityId());
        var billingContact = accountGetService.getContact(creditMemo.getBillingContactId());
        String customerErpId = getAccountErpIdCreateIfNeeded(creditMemo.getAccountId(), billingContact, subsidiaryId);
        JsonNode creditMemoRequest = payloadMapper().buildCreditMemoRequest(creditMemo, customerErpId, zoneId);

        String creditMemoErpId = netsuiteClient.syncCreditMemoToNetsuite(creditMemoRequest, creditMemo.getCreditMemoNumber());
        if (StringUtils.isBlank(creditMemoErpId)) {
            throw new ConflictingStateException("unable to fetch credit memo erp id");
        }
        creditMemoService.updateCreditMemoErpId(creditMemo.getCreditMemoNumber(), creditMemoErpId);
    }

    private String getAccountErpIdCreateIfNeeded(String accountId, AccountContact billingContact, String subsidiaryId) {
        Account account = accountGetService.getAccount(accountId);
        if (StringUtils.isNotBlank(account.getErpId())) {
            checkAccountHasSubsidiaryCreateIfNeeded(account, subsidiaryId);
            return account.getErpId();
        }
        JsonNode accountRequest = payloadMapper().buildCustomerRequest(account, billingContact, subsidiaryId);
        String accountErpId = netsuiteClient.syncAccountToNetsuite(accountRequest, accountId);
        accountService.updateAccountErpId(account.getAccountId(), accountErpId, false);
        return accountErpId;
    }

    private void checkAccountHasSubsidiaryCreateIfNeeded(Account account, String subsidiaryId) {
        Validator.checkNonNullInternal(account.getErpId(), "account erp id cannot be null");
        Set<String> subsidiaries = netsuiteClient.getAccountSubsidiaryIds(account.getErpId());
        if (subsidiaries.contains(subsidiaryId)) {
            return;
        }
        JsonNode addSubsidiaryRequest = payloadMapper().buildAddSubsidiaryToCustomerRequest(account, subsidiaryId);
        netsuiteClient.addSubsidiaryToCustomer(addSubsidiaryRequest, subsidiaryId);
    }

    @SuppressWarnings("unused")
    private JsonNode getCreditMemoFromNetsuite(String creditMemoErpId) {
        return netsuiteClient.getFromNetsuite(creditMemoErpId, "creditMemo");
    }

    private JsonNode getInvoiceFromNetsuite(String invoiceErpId) {
        return netsuiteClient.getFromNetsuite(invoiceErpId, "invoice");
    }

    public void voidInvoiceOnNetsuite(String invoiceNumber) {
        checkFeatureEnabled();
        checkInvoiceSyncEnabled();
        Invoice invoice = invoiceRetrievalService.getInvoice(new Invoice.Number(invoiceNumber));
        if (invoice.getStatus() != InvoiceStatus.VOIDED) {
            throw new ConflictingStateException(String.format("invoice %s must be VOIDED", invoiceNumber));
        }
        if (StringUtils.isNotBlank(invoice.getErpId()) && invoice.getErpId().contains("CM")) {
            LOGGER.info("skipping void invoice {} sync to epr because it already has erp id {}", invoiceNumber, invoice.getErpId());
            return;
        }

        String creditMemoErpId = netsuiteClient.transformInvoiceToCreditMemo(invoice.getErpId());
        if (StringUtils.isBlank(creditMemoErpId)) {
            throw new ConflictingStateException("unable to fetch credit memo erp id");
        }
        String voidedErpId = String.format("INV: %s, CM: %s", invoice.getErpId(), creditMemoErpId);
        invoiceService.updateInvoiceErpId(invoice.getInvoiceNumber(), voidedErpId);
    }

    private String getSubsidiaryId(String entityId) {
        Entity entity = entityGetService.getEntityById(entityId);
        if (StringUtils.isBlank(entity.getErpId())) {
            throw new ConflictingStateException(String.format("entity %s does not have an erp id", entity.getEntityId()));
        }
        return entity.getErpId();
    }

    @SuppressWarnings("unused")
    public JsonNode getCustomerFromNetsuite(String accountErpId) {
        return netsuiteClient.getFromNetsuite(accountErpId, "customer");
    }

    public void sendJournalEntries(AccountingPeriod accountingPeriod) {
        checkJournalEntrySyncEnabled();
        ZoneId zoneId = tenantSettingService.getTenantSetting().getDefaultTimeZone().toZoneId();
        Period accountingPeriodInterval = Period.between(accountingPeriod.getStartDate(), accountingPeriod.getEndDate());
        List<String> ledgerAccountCodes = accountingGetService.getLedgerAccounts().stream().map(LedgerAccount::getAccountCode).toList();
        Map<String, String> ledgerAccountCodeToNetsuiteIdMap = new HashMap<>(ledgerAccountCodes.size());

        JournalEntryPageToken pageToken = null;
        int pageIndex = 1;
        do {
            PageResult<List<JournalEntry>, JournalEntryPageToken> pageResult = accountingGetService.getJournalEntriesOrderedByAccountingDate(
                PageRequest.from(pageToken, JOURNAL_ENTRY_PAGE_SIZE),
                accountingPeriodInterval
            );

            if (CollectionUtils.isNotEmpty(pageResult.getResult())) {
                List<JournalEntry> journalEntries = pageResult.getResult();
                LOGGER.info("Page {}. Sending {} journal entries", pageIndex, journalEntries.size());
                syncJournalEntriesPage(journalEntries, zoneId, ledgerAccountCodeToNetsuiteIdMap);
                LOGGER.info("Page {}. Sent {} journal entries", pageIndex, journalEntries.size());
                pageIndex++;
            }
            pageToken = pageResult.getNextPageToken();
        } while (pageToken != null);
        LOGGER.info(
            "successfully created journal entries on {} for accounting period: {}",
            NETSUITE.getDisplayName(),
            accountingPeriod.getAccountingPeriodId()
        );
    }

    private void updateAccountCodeMap(Map<String, String> ledgerAccountCodeToNetsuiteIdMap, List<JournalEntry> journalEntries) {
        List<String> ledgerAccountCodesToFetch = journalEntries
            .stream()
            .map(JournalEntry::getJournalLines)
            .flatMap(List::stream)
            .map(JournalLine::getLedgerAccountCode)
            .distinct()
            .filter(glCode -> !ledgerAccountCodeToNetsuiteIdMap.containsKey(glCode))
            .toList();
        Map<String, String> ledgerAccountCodeToNetsuiteIdMapUpdated = netsuiteClient.getLedgerAccountCodeToNetsuiteIdMap(ledgerAccountCodesToFetch);
        ledgerAccountCodeToNetsuiteIdMap.putAll(ledgerAccountCodeToNetsuiteIdMapUpdated);
    }

    private void syncJournalEntriesPage(List<JournalEntry> journalEntries, ZoneId zoneId, Map<String, String> ledgerAccountCodeToNetsuiteIdMap) {
        updateAccountCodeMap(ledgerAccountCodeToNetsuiteIdMap, journalEntries);
        Set<String> syncedJournalEntryIds = externalJournalEntryMappingService.getSyncedJournalEntryIds(
            netsuiteClient.getIntegrationUuid(),
            journalEntries
        );
        journalEntries
            .stream()
            .filter(journalEntry -> {
                if (!syncedJournalEntryIds.contains(journalEntry.getJournalEntryId())) {
                    return true;
                }
                LOGGER.info("Journal entry {} is already synced, skipping", journalEntry.getJournalEntryId());
                return false;
            })
            .forEach(journalEntry -> sendJournalEntry(journalEntry, zoneId, ledgerAccountCodeToNetsuiteIdMap));
    }

    private void sendJournalEntry(JournalEntry journalEntry, ZoneId zoneId, Map<String, String> ledgerAccountCodeToNetsuiteIdMap) {
        LOGGER.debug("input journal entry: {}", journalEntry);

        String subsidiaryId = getSubsidiaryId(journalEntry.getEntityId());
        JsonNode journalEntryRequest = payloadMapper().buildJournalEntryRequest(journalEntry, subsidiaryId, zoneId, ledgerAccountCodeToNetsuiteIdMap);

        String jeErpId = netsuiteClient.syncJournalEntry(journalEntryRequest, journalEntry.getJournalEntryId());
        externalJournalEntryMappingService.createJournalEntryMapping(netsuiteClient.getIntegrationUuid(), journalEntry.getJournalEntryId(), jeErpId);
        LOGGER.debug("journal entry with id {} created: {}", journalEntry.getJournalEntryId());
    }

    public void deleteJournalEntries(AccountingPeriod accountingPeriod) {
        checkJournalEntrySyncEnabled();
        UUID integrationId = netsuiteClient.getIntegrationUuid();
        Period accountingPeriodInterval = Period.between(accountingPeriod.getStartDate(), accountingPeriod.getEndDate());

        LOGGER.info(
            "deleting journal entries from Netsuite for accounting period: {} from {} to {}",
            accountingPeriod.getAccountingPeriodId(),
            accountingPeriod.getStartDate(),
            accountingPeriod.getEndDate()
        );

        JournalEntryPageToken pageToken = null;
        int pageIndex = 1;
        do {
            PageResult<List<JournalEntry>, JournalEntryPageToken> pageResult = accountingGetService.getJournalEntriesOrderedByAccountingDate(
                PageRequest.from(pageToken, JOURNAL_ENTRY_PAGE_SIZE),
                accountingPeriodInterval
            );

            if (CollectionUtils.isNotEmpty(pageResult.getResult())) {
                List<JournalEntry> journalEntries = pageResult.getResult();
                LOGGER.info("page {}. deleting {} journal entries", pageIndex++, journalEntries.size());
                deleteJournalEntriesPage(integrationId, journalEntries);
            }
            pageToken = pageResult.getNextPageToken();
        } while (pageToken != null);
        LOGGER.info("successfully deleted journal entries from Netsuite for accounting period: {}", accountingPeriod.getAccountingPeriodId());
    }

    private void deleteJournalEntriesPage(UUID integrationId, List<JournalEntry> journalEntries) {
        List<String> journalEntryIds = journalEntries.stream().map(JournalEntry::getJournalEntryId).distinct().toList();
        List<ExternalJournalEntryMapping> journalEntryMappings = externalJournalEntryMappingService.getJournalEntryMappings(
            integrationId,
            journalEntryIds
        );
        for (ExternalJournalEntryMapping journalEntryMapping : journalEntryMappings) {
            deleteJournalEntry(journalEntryMapping);
        }
    }

    private void deleteJournalEntry(ExternalJournalEntryMapping journalEntryMapping) {
        netsuiteClient.deleteJournalEntry(journalEntryMapping.getExternalJournalEntryId());
        externalJournalEntryMappingService.markMappingDeleted(journalEntryMapping.getId(), Optional.empty());
        LOGGER.info("deleted journal entry {} from Netsuite", journalEntryMapping.getJournalEntryId());
    }
}
