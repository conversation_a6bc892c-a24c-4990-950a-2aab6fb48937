package com.subskribe.billy.esign.services;

import static com.subskribe.billy.aws.secretsmanager.model.SecretType.PANDA_DOC_API_KEY;

import com.docusign.esign.model.EnvelopeSummary;
import com.google.gson.reflect.TypeToken;
import com.pandadoc.client.ApiClient;
import com.pandadoc.client.ApiException;
import com.pandadoc.client.Configuration;
import com.pandadoc.client.api.DocumentsApi;
import com.pandadoc.client.auth.ApiKeyAuth;
import com.pandadoc.client.models.DocumentCreateLinkRequest;
import com.pandadoc.client.models.DocumentCreateLinkResponse;
import com.pandadoc.client.models.DocumentCreateResponse;
import com.pandadoc.client.models.DocumentDetailsResponse;
import com.pandadoc.client.models.DocumentDetailsResponseRecipients;
import com.pandadoc.client.models.DocumentSendRequest;
import com.pandadoc.client.models.DocumentStatusChangeRequest;
import com.pandadoc.client.models.DocumentStatusRequestEnum;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.model.AccountStub;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.auth.apikey.ApiKeyService;
import com.subskribe.billy.auth.model.ApiKeyMetaData;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.compositeorder.service.CompositeOrderDocumentService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.docusign.service.DocuSignService;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.email.model.EmailContacts;
import com.subskribe.billy.email.model.EntityType;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.esign.db.EsignDAO;
import com.subskribe.billy.esign.db.EsignTenantSignatoryDAO;
import com.subskribe.billy.esign.model.ElectronicSignature;
import com.subskribe.billy.esign.model.ElectronicSignatureProvider;
import com.subskribe.billy.esign.model.ElectronicSignatureStatus;
import com.subskribe.billy.esign.model.EsignDetails;
import com.subskribe.billy.esign.model.EsignEmailRequest;
import com.subskribe.billy.esign.model.EsignRequest;
import com.subskribe.billy.esign.model.EsignTenantSignatory;
import com.subskribe.billy.esign.model.ImmutableElectronicSignatureAuditLog;
import com.subskribe.billy.esign.model.ImmutableEsignDetails;
import com.subskribe.billy.esign.model.ImmutableEsignEmailRequest;
import com.subskribe.billy.esign.model.pandadoc.PandaDocDocumentStatus;
import com.subskribe.billy.esign.model.pandadoc.PandaDocMetadata;
import com.subskribe.billy.esign.model.pandadoc.PandaDocRecipient;
import com.subskribe.billy.esign.model.pandadoc.PandaDocWebhookPayload;
import com.subskribe.billy.esign.model.pandadoc.PandaDocWebhookPayloadData;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.ExternalDependencyException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.invoice.model.EmailNotifiersList;
import com.subskribe.billy.notification.model.payload.ElectronicSignatureEvent;
import com.subskribe.billy.notification.model.payload.ImmutableElectronicSignatureEvent;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderStub;
import com.subskribe.billy.order.services.OrderDocumentService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderResellerService;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.salesforce.service.SalesforceService;
import com.subskribe.billy.shared.contactfetcher.ContactType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.SigningOrder;
import com.subskribe.billy.shared.enums.Status;
import com.subskribe.billy.shared.image.ImageUtils;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import com.subskribe.billy.tenant.model.TenantInfo;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.model.ImmutableTenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobModule;
import com.subskribe.billy.tenantjob.model.TenantJobObjectModel;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.inject.Inject;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.mapstruct.factory.Mappers;

@SuppressWarnings({ "deprecation" })
public class EsignService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EsignService.class);

    private static final String PANDA_DOC_API_BASE_URL = "https://api.pandadoc.com";

    private static final String PANDA_DOC_DOCUMENT_BASE_URL = "https://app.pandadoc.com/s/";

    private static final String PANDA_DOC_API_DOCUMENTS_ENDPOINT = PANDA_DOC_API_BASE_URL + "/public/v1/documents";

    private static final String PANDA_DOC_TEMPLATE_NAME_FORMAT = "%s - %s";

    private static final String HTTP_CLIENT_AUTHORIZATION_HEADER = "Authorization";

    private static final String HTTP_CLIENT_API_KEY_PREFIX = "API-Key ";

    private static final long HTTP_CLIENT_CONNECT_TIMEOUT_IN_SECONDS = 30;

    private static final long HTTP_CLIENT_WRITE_TIMEOUT_IN_SECONDS = 30;

    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
        .connectTimeout(HTTP_CLIENT_CONNECT_TIMEOUT_IN_SECONDS, TimeUnit.SECONDS)
        .writeTimeout(HTTP_CLIENT_WRITE_TIMEOUT_IN_SECONDS, TimeUnit.SECONDS)
        .build();

    private static final long ONE_YEAR_IN_SECONDS_MINUS_ONE = 60 * 60 * 24 * 365 - 1;

    private static final BigDecimal ONE_YEAR_DOCUMENT_LINK_LIFETIME = BigDecimal.valueOf(ONE_YEAR_IN_SECONDS_MINUS_ONE);

    private static final UncheckedObjectMapper OBJECT_MAPPER = UncheckedObjectMapper.defaultMapper();

    private final EsignTenantSignatoryDAO esignTenantSignatoryDAO;

    private final EsignDAO esignDAO;

    private final SecretsService secretsService;

    private final TenantService tenantService;

    private final OrderDocumentService orderDocumentService;

    private final EsignDocumentService esignDocumentService;

    private final UserService userService;

    private final ApiKeyService apiKeyService;

    private final TenantIdProvider tenantIdProvider;

    private final EsignEmailService esignEmailService;

    private final BillyConfiguration billyConfiguration;

    private final PlatformFeatureService platformFeatureService;

    private final AccountGetService accountGetService;

    private final OrderGetService orderGetService;

    private final EmailContactListService emailContactListService;

    private final TenantSettingService tenantSettingService;

    private final EventPublishingService eventPublishingService;

    private final DSLContextProvider dslContextProvider;

    private final DocuSignService docuSignService;

    private final EsignIntegrationService esignIntegrationService;

    private final CompositeOrderDocumentService compositeOrderDocumentService;

    private final TenantJobDispatcherService tenantJobDispatcherService;

    private final HubSpotService hubSpotService;

    private final HubSpotIntegrationService hubSpotIntegrationService;

    private final FeatureService featureService;

    private final OrderResellerService orderResellerService;

    private final OrderMapper orderMapper;

    @Inject
    public EsignService(
        EsignTenantSignatoryDAO esignTenantSignatoryDAO,
        EsignDAO esignDAO,
        SecretsService secretsService,
        TenantService tenantService,
        OrderDocumentService orderDocumentService,
        EsignDocumentService esignDocumentService,
        UserService userService,
        ApiKeyService apiKeyService,
        TenantIdProvider tenantIdProvider,
        EsignEmailService esignEmailService,
        BillyConfiguration billyConfiguration,
        PlatformFeatureService platformFeatureService,
        AccountGetService accountGetService,
        OrderGetService orderGetService,
        EmailContactListService emailContactListService,
        TenantSettingService tenantSettingService,
        EventPublishingService eventPublishingService,
        DSLContextProvider dslContextProvider,
        DocuSignService docuSignService,
        EsignIntegrationService esignIntegrationService,
        CompositeOrderDocumentService compositeOrderDocumentService,
        TenantJobDispatcherService tenantJobDispatcherService,
        HubSpotService hubSpotService,
        HubSpotIntegrationService hubSpotIntegrationService,
        FeatureService featureService,
        OrderResellerService orderResellerService
    ) {
        this.esignTenantSignatoryDAO = esignTenantSignatoryDAO;
        this.esignDAO = esignDAO;
        this.secretsService = secretsService;
        this.tenantService = tenantService;
        this.orderDocumentService = orderDocumentService;
        this.esignDocumentService = esignDocumentService;
        this.userService = userService;
        this.apiKeyService = apiKeyService;
        this.tenantIdProvider = tenantIdProvider;
        this.esignEmailService = esignEmailService;
        this.billyConfiguration = billyConfiguration;
        this.platformFeatureService = platformFeatureService;
        this.accountGetService = accountGetService;
        this.orderGetService = orderGetService;
        this.emailContactListService = emailContactListService;
        this.tenantSettingService = tenantSettingService;
        this.eventPublishingService = eventPublishingService;
        this.dslContextProvider = dslContextProvider;
        this.docuSignService = docuSignService;
        this.esignIntegrationService = esignIntegrationService;
        this.compositeOrderDocumentService = compositeOrderDocumentService;
        this.tenantJobDispatcherService = tenantJobDispatcherService;
        this.hubSpotService = hubSpotService;
        this.hubSpotIntegrationService = hubSpotIntegrationService;
        this.featureService = featureService;
        this.orderResellerService = orderResellerService;
        orderMapper = Mappers.getMapper(OrderMapper.class);
    }

    public List<EsignTenantSignatory> getTenantSignatories(String tenantId) {
        List<EsignTenantSignatory> signatories = esignTenantSignatoryDAO.getTenantSignatories(tenantId);
        return signatories.stream().filter(this::doesActiveUserExistForTenantSignatory).collect(Collectors.toList());
    }

    private boolean doesActiveUserExistForTenantSignatory(EsignTenantSignatory signatory) {
        if (StringUtils.isBlank(signatory.getUserId())) {
            return false;
        }

        Optional<User> optionalUser = userService.getUserOptional(signatory.getUserId());
        return optionalUser.filter(user -> user.getState() == Status.ACTIVE).isPresent();
    }

    public void insertTenantSignatory(EsignTenantSignatory signatory, String tenantId) {
        Validator.validateStringNotBlank(signatory.getUserId(), "userId is blank");
        Optional<EsignTenantSignatory> tenantSignatory = esignTenantSignatoryDAO.getTenantSignatory(signatory.getUserId(), tenantId);
        if (tenantSignatory.isPresent()) {
            throw new InvalidInputException(String.format("User %s is already a signatory", signatory.getUserId()));
        }

        esignTenantSignatoryDAO.insertTenantSignatory(signatory, tenantId);
    }

    public void deleteTenantSignatory(String userId, String tenantId) {
        Validator.validateStringNotBlank(userId, "userId");
        esignTenantSignatoryDAO.deleteTenantSignatory(userId, tenantId);
    }

    // FIXME: We should really stick to either eSign or ElectronicSignature. We have both now on methods and APIs and fields and it is confusing.
    public ElectronicSignature getElectronicSignatureByOrderId(String orderId) {
        Validator.validateStringNotBlank(orderId, "orderId");

        ElectronicSignatureProvider provider = esignIntegrationService
            .getEsignProvider()
            .orElseThrow(() -> new IllegalStateException("No esign integration found"));
        return esignDAO
            .getSignatureByOrderId(provider, orderId, false)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ESIGNATURE, orderId));
    }

    public Optional<ElectronicSignature> getOptionalElectronicSignatureByOrderId(String orderId, boolean includeDeleted) {
        Validator.validateStringNotBlank(orderId, "orderId");

        Optional<ElectronicSignatureProvider> provider = esignIntegrationService.getEsignProvider();
        if (provider.isEmpty()) {
            return Optional.empty();
        }

        return esignDAO.getSignatureByOrderId(provider.get(), orderId, includeDeleted);
    }

    private Optional<ElectronicSignature> getElectronicSignatureByOrderIdIncludeVoided(String orderId) {
        Validator.validateStringNotBlank(orderId, "orderId");
        Optional<ElectronicSignatureProvider> provider = esignIntegrationService.getEsignProvider();
        if (provider.isEmpty()) {
            LOGGER.warn("eSign sync to HS failed. No eSign integration found. Order Id: " + orderId);
            return Optional.empty();
        }

        return esignDAO.getSignatureByOrderId(provider.get(), orderId, true);
    }

    public List<ImmutableElectronicSignatureAuditLog> getElectronicSignatureAuditLogs(String electronicSignatureId) {
        Validator.validateStringNotBlank(electronicSignatureId, "electronicSignatureId");

        return esignDAO.getSignatureAuditLogs(electronicSignatureId);
    }

    public void updateStatusFromWebhook(PandaDocWebhookPayload payload, boolean isTest) {
        validatePandaDocWebhookPayload(payload);

        String documentId = payload.getData().getId();
        Optional<ElectronicSignature> signatureOptional = esignDAO.getSignatureByDocumentId(ElectronicSignatureProvider.PANDADOC, documentId);
        if (signatureOptional.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.ESIGNATURE, documentId);
        }
        ElectronicSignature signature = signatureOptional.get();

        TenantContextInjector.runInTenantContext(signature.getTenantId(), tenantIdProvider, () -> {
            Optional<PandaDocDocumentStatus> documentStatus = fromPandaDocDocumentStatus(payload.getData().getStatus());
            if (documentStatus.isEmpty()) {
                LOGGER.warn("Unable to parse PandaDoc document status: {}", payload.getData().getStatus());
                return;
            }
            switch (payload.getEvent()) {
                case DOCUMENT_STATE_CHANGED -> handleDocumentStateChanged(payload, signature, documentStatus.get(), isTest);
                case RECIPIENT_COMPLETED -> handleRecipientCompleted(payload, signature);
                case DOCUMENT_CREATION_FAILED -> handleDocumentCreationFailed(signature);
                default -> {}
            }
        });
    }

    private void handleDocumentStateChanged(
        PandaDocWebhookPayload payload,
        ElectronicSignature signature,
        PandaDocDocumentStatus documentStatus,
        boolean isTest
    ) {
        Optional<ElectronicSignatureStatus> esignStatus = mapPandaDocStatusToElectronicSignatureStatus(documentStatus);
        esignStatus.ifPresent(electronicSignatureStatus -> {
            ElectronicSignature updatedElectronicSignature = esignDAO.updateSignatureStatus(
                ElectronicSignatureProvider.PANDADOC,
                signature.getExternalDocumentId(),
                electronicSignatureStatus,
                resolveInitiatedBy(electronicSignatureStatus, signature, payload.getData())
            );
            dispatchJobToSyncToHubSpot(updatedElectronicSignature);
        });

        if (isTest && billyConfiguration.isLocalOrCi()) {
            LOGGER.info("Test mode enabled, not sending email");
            return;
        }

        switch (documentStatus) {
            case DRAFT -> silentlySendPandaDocDocument(signature.getExternalDocumentId());
            case SENT -> {
                sendEmail(payload.getData());

                if (signature.getInitiatedOn() == null) {
                    try {
                        String initiatedOnString = payload.getData().getDateCreated();
                        Instant initiatedOn = Instant.parse(initiatedOnString);
                        esignDAO.setSignatureInitiatedOn(signature.getId(), initiatedOn);
                    } catch (Exception e) {
                        LOGGER.warn("Unable to get initiated_on from PandaDoc payload with id={}", signature.getExternalDocumentId(), e);
                    }
                }
            }
            case VOIDED -> forceRegenerateOrderFormPdfForOrder(signature.getOrderId(), signature.getIsComposite());
            case COMPLETED -> {
                ElectronicSignature savedSignature = signature;
                if (signature.getCompletedOn() == null) {
                    try {
                        String completedOnString = payload.getData().getDateModified();
                        Instant initiatedOn = Instant.parse(completedOnString);
                        savedSignature = esignDAO.setSignatureCompletedOn(signature.getId(), initiatedOn);
                    } catch (Exception e) {
                        LOGGER.warn("Unable to get completed_on from PandaDoc payload with id={}", signature.getExternalDocumentId(), e);
                    }
                }

                downloadFromPandaDocAndUploadToS3(savedSignature, true);
                sendCompletedEmail(savedSignature, payload.getData());
                sendNotificationForCompletedEvent(savedSignature);
            }
            default -> {}
        }
    }

    private void forceRegenerateOrderFormPdfForOrder(String orderId, boolean isCompositeOrder) {
        if (isCompositeOrder) {
            compositeOrderDocumentService.createCompositeOrderDocumentWithoutChecks(orderId);
        } else {
            orderDocumentService.createOrderDocumentWithoutChecks(orderId);
        }
    }

    private Optional<ElectronicSignatureStatus> mapPandaDocStatusToElectronicSignatureStatus(PandaDocDocumentStatus documentStatus) {
        return switch (documentStatus) {
            case SENT -> Optional.of(ElectronicSignatureStatus.SENT);
            case VOIDED -> Optional.of(ElectronicSignatureStatus.VOIDED);
            case VIEWED -> Optional.of(ElectronicSignatureStatus.VIEWED);
            case COMPLETED -> Optional.of(ElectronicSignatureStatus.COMPLETED);
            default -> Optional.empty();
        };
    }

    private void handleRecipientCompleted(PandaDocWebhookPayload payload, ElectronicSignature signature) {
        Optional<PandaDocRecipient> nextRecipient = getNextRecipient(payload.getData(), false);
        if (nextRecipient.isPresent()) {
            ElectronicSignature updatedElectronicSignature = esignDAO.updateSignatureStatus(
                ElectronicSignatureProvider.PANDADOC,
                signature.getExternalDocumentId(),
                ElectronicSignatureStatus.PARTIALLY_SIGNED,
                resolveInitiatedBy(ElectronicSignatureStatus.PARTIALLY_SIGNED, signature, payload.getData())
            );

            dispatchJobToSyncToHubSpot(updatedElectronicSignature);
            sendEmail(payload.getData());
            downloadFromPandaDocAndUploadToS3(signature, false);
        }
    }

    private void handleDocumentCreationFailed(ElectronicSignature signature) {
        esignDAO.updateSignatureStatus(ElectronicSignatureProvider.PANDADOC, signature.getExternalDocumentId(), ElectronicSignatureStatus.FAILED);
    }

    private void validatePandaDocWebhookPayload(PandaDocWebhookPayload payload) {
        PandaDocMetadata metadata = payload.getData().getMetadata();
        if (metadata == null || metadata.getEnvName() == null) {
            String errorMessage = String.format("PandaDoc payload does not have env name in metadata. Payload: %s", payload);
            throw new ServiceFailureException(errorMessage);
        }

        if (!metadata.getEnvName().equals(billyConfiguration.getEnvName())) {
            String errorMessage = String.format("ignoring PandaDoc webhook from different environment: %s", metadata.getEnvName());
            throw new InvalidInputException(errorMessage);
        }
    }

    private String resolveInitiatedBy(ElectronicSignatureStatus status, ElectronicSignature signature, PandaDocWebhookPayloadData payloadData) {
        switch (status) {
            // TODO: Person initiating void may not be the same as person initiating the signature
            case PENDING, SENT, VOIDED -> {
                return signature.getInitiatedBy();
            }
            case VIEWED -> {
                PandaDocRecipient nextRecipient = getNextRecipient(payloadData, false).orElseThrow(() ->
                    new ServiceFailureException("PandaDoc next recipient is empty")
                );
                return nextRecipient.getEmail();
            }
            case PARTIALLY_SIGNED -> {
                PandaDocRecipient actionBy = payloadData.getActionBy();
                if (actionBy == null) {
                    throw new ServiceFailureException("PandaDoc actionBy is empty");
                }
                return actionBy.getEmail();
            }
            case COMPLETED -> {
                PandaDocRecipient lastRecipient = getLastRecipient(payloadData).orElseThrow(() ->
                    new ServiceFailureException("PandaDoc last recipient is empty")
                );
                return lastRecipient.getEmail();
            }
            default -> throw new ServiceFailureException("PandaDoc status is not supported: " + status);
        }
    }

    private Optional<PandaDocDocumentStatus> fromPandaDocDocumentStatus(String status) {
        try {
            return Optional.of(PandaDocDocumentStatus.fromPandaDocStatus(status));
        } catch (Exception e) {
            LOGGER.warn("Unable to parse PandaDoc document status: {}", status);
            return Optional.empty();
        }
    }

    private void sendEmail(PandaDocWebhookPayloadData payloadData) {
        var esignEmailRequest = buildEsignEmailRequestFromPandaDocWebhookPayload(payloadData.getId(), payloadData);
        esignEmailRequest.ifPresent(esignEmailService::sendEmail);
    }

    private Optional<ImmutableEsignEmailRequest> buildEsignEmailRequestFromPandaDocWebhookPayload(
        String documentId,
        PandaDocWebhookPayloadData payloadData
    ) {
        var nextRecipient = getNextRecipient(payloadData, true);
        if (nextRecipient.isEmpty()) {
            LOGGER.error("No next recipient found for document id: {}", documentId);
            return Optional.empty();
        }

        var esign = esignDAO
            .getSignatureByDocumentId(ElectronicSignatureProvider.PANDADOC, documentId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ESIGNATURE, documentId));

        var recipient = nextRecipient.get();
        Optional<ImmutableEsignEmailRequest> response = buildEmailRequest(recipient, documentId, payloadData.getName(), esign);
        response.ifPresent(esignEmailRequest -> updateSignatureLinkIfNecessary(esignEmailRequest, esign));
        return response;
    }

    private void updateSignatureLinkIfNecessary(ImmutableEsignEmailRequest esignEmailRequest, ElectronicSignature electronicSignature) {
        String link = esignEmailRequest.getDocumentUrl();
        if (link == null || link.equals(electronicSignature.getLink())) {
            return;
        }

        esignDAO.updateSignatureLink(electronicSignature, link);
        dispatchJobToSyncToHubSpot(electronicSignature);
    }

    private static Optional<PandaDocRecipient> getNextRecipient(PandaDocWebhookPayloadData payloadData, boolean includeCc) {
        return payloadData
            .getRecipients()
            .stream()
            .filter(pandaDocRecipient -> !pandaDocRecipient.getHasCompleted())
            .filter(pandaDocRecipient -> includeCc || !"CC".equals(pandaDocRecipient.getRecipientType()))
            .min(Comparator.comparing(PandaDocRecipient::getSigningOrder));
    }

    private static Optional<PandaDocRecipient> getLastRecipient(PandaDocWebhookPayloadData payloadData) {
        return payloadData
            .getRecipients()
            .stream()
            .filter(PandaDocRecipient::getHasCompleted)
            .filter(pandaDocRecipient -> !"CC".equals(pandaDocRecipient.getRecipientType()))
            .max(Comparator.comparing(PandaDocRecipient::getSigningOrder));
    }

    private Optional<ImmutableEsignEmailRequest> buildEmailRequest(
        PandaDocRecipient recipient,
        String documentId,
        String documentName,
        ElectronicSignature signature
    ) {
        String documentUrl = getDocumentUrl(recipient, documentId);
        LOGGER.info("PandaDoc Document URL: {}", documentUrl);

        String orderId = signature.getOrderId();
        Order order = retrieveOrder(orderId, signature.getIsComposite());
        Account account = accountGetService.getAccount(order.getAccountId());

        var userOptional = userService.getUserByEmail(signature.getInitiatedBy());
        return userOptional.map(user ->
            ImmutableEsignEmailRequest.builder()
                .externalDocumentId(documentId)
                .documentName(documentName)
                .documentUrl(documentUrl)
                .recipientEmail(recipient.getEmail())
                .recipientName(getRecipientName(recipient.getFirstName(), recipient.getLastName()))
                .userName(user.getDisplayName())
                .base64EncodedLogo(getBase64EncodedLogo())
                .tenantName(getTenantName())
                .orderId(orderId)
                .accountName(account.getName())
                .build()
        );
    }

    private String getDocumentUrl(PandaDocRecipient recipient, String documentId) {
        ApiClient client = getPandaDocApiClient();
        DocumentsApi documentsApi = new DocumentsApi(client);
        DocumentCreateLinkRequest documentCreateLinkRequest = new DocumentCreateLinkRequest();
        documentCreateLinkRequest.setRecipient(recipient.getEmail());
        documentCreateLinkRequest.setLifetime(ONE_YEAR_DOCUMENT_LINK_LIFETIME);
        DocumentCreateLinkResponse result = callInPandaDocExceptionContext(() ->
            documentsApi.createDocumentLink(documentId, documentCreateLinkRequest)
        );

        return PANDA_DOC_DOCUMENT_BASE_URL + result.getId();
    }

    private String getRecipientName(String firstName, String lastName) {
        if (StringUtils.isNotBlank(firstName) && StringUtils.isNotBlank(lastName)) {
            // TODO: There is a bug in PandaDoc that for registered users, it adds the last name twice. Temporary hacky solution.
            String spacePlusLastName = " " + lastName;
            if (firstName.endsWith(spacePlusLastName)) {
                return firstName;
            }

            return firstName + spacePlusLastName;
        }

        if (StringUtils.isNotBlank(firstName)) {
            return firstName;
        }

        if (StringUtils.isNotBlank(lastName)) {
            return lastName;
        }

        return StringUtils.EMPTY;
    }

    private void sendCompletedEmail(ElectronicSignature signature, PandaDocWebhookPayloadData payloadData) {
        var initiatorOptional = userService.getUserByEmail(signature.getInitiatedBy());
        if (initiatorOptional.isEmpty()) {
            LOGGER.warn("Unable to find an active user with email {}", signature.getInitiatedBy());
        } else {
            var initiator = new PandaDocRecipient();
            initiator.setEmail(signature.getInitiatedBy());
            initiator.setFirstName(initiatorOptional.get().getDisplayName());
            EsignEmailRequest request = buildCompletedEmailRequest(signature, initiator);
            esignEmailService.sendCompletedEmail(request);
        }

        List<PandaDocRecipient> recipients = payloadData.getRecipients();
        recipients.forEach(recipient -> {
            EsignEmailRequest request = buildCompletedEmailRequest(signature, recipient);
            esignEmailService.sendCompletedEmail(request);
        });
    }

    private EsignEmailRequest buildCompletedEmailRequest(ElectronicSignature signature, PandaDocRecipient recipient) {
        String tenantName = tenantService.getCurrentTenant().getName();
        String orderId = signature.getOrderId();
        Order order = retrieveOrder(orderId, signature.getIsComposite());
        Account account = accountGetService.getAccount(order.getAccountId());

        return ImmutableEsignEmailRequest.builder()
            .documentName(String.format(PANDA_DOC_TEMPLATE_NAME_FORMAT, account.getName(), tenantName))
            .recipientEmail(recipient.getEmail())
            .recipientName(getRecipientName(recipient.getFirstName(), recipient.getLastName()))
            .externalDocumentId(signature.getExternalDocumentId())
            .orderId(orderId)
            .base64EncodedLogo(getBase64EncodedLogo())
            .tenantName(getTenantName())
            .accountName(account.getName())
            .composite(signature.getIsComposite())
            .build();
    }

    private void sendNotificationForCompletedEvent(ElectronicSignature electronicSignature) {
        String tenantId = electronicSignature.getTenantId();
        String orderId = electronicSignature.getOrderId();
        String accountId = retrieveOrder(orderId, electronicSignature.getIsComposite()).getAccountId();
        SigningOrder signingOrder = tenantSettingService.getSigningOrder();

        EmailContacts emailContacts = emailContactListService.getEmailContacts(orderId, EntityType.ORDER);
        EmailContact accountSignatory = emailContacts
            .getToContacts()
            .stream()
            .filter(e -> e.getType() == ContactType.ACCOUNT_CONTACT)
            .findFirst()
            .orElseThrow(() -> new ServiceFailureException("Unable to find account signatory for order " + orderId));
        EmailContact tenantSignatory = null;
        if (signingOrder != SigningOrder.ACCOUNT_ONLY) {
            tenantSignatory = emailContacts
                .getToContacts()
                .stream()
                .filter(e -> e.getType() == ContactType.USER)
                .findFirst()
                .orElseThrow(() -> new ServiceFailureException("Unable to find tenant signatory for order " + orderId));
        }

        ElectronicSignatureEvent event = ImmutableElectronicSignatureEvent.builder()
            .id(electronicSignature.getExternalDocumentId())
            .orderId(orderId)
            .initiatedBy(electronicSignature.getInitiatedBy())
            .accountSignatory(accountSignatory)
            .tenantSignatory(tenantSignatory)
            .initiatedOn(electronicSignature.getInitiatedOn())
            .completedOn(electronicSignature.getCompletedOn())
            .build();

        byte[] payload = OBJECT_MAPPER.writeValueAsBytes(event);

        OrderStub orderStub = orderGetService.getOrderStub(orderId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ORDER, orderId));

        eventPublishingService.publishEventInTransaction(
            dslContextProvider.get(tenantId),
            EventType.ESIGNATURE_COMPLETED,
            tenantId,
            orderStub.getEntityId(),
            accountId,
            payload
        );
    }

    private String getBase64EncodedLogo() {
        TenantInfo tenantInfo = tenantService.getCurrentTenantInfo();
        byte[] logoContent = tenantInfo.getLogo();

        if (logoContent == null) {
            return null;
        }

        var encodedContent = Base64.getEncoder().encodeToString(logoContent);
        var imageType = ImageUtils.getImageType(logoContent);
        return String.format("data:image/%s;base64,%s", imageType, encodedContent);
    }

    private String getTenantName() {
        TenantInfo tenantInfo = tenantService.getCurrentTenantInfo();
        return tenantInfo.getName();
    }

    public void downloadFromPandaDocAndUploadToS3(ElectronicSignature signature, boolean isFullySignedDocument) {
        throwIfFeatureNotEnabled();
        var document = downloadDocumentFromPandaDoc(signature.getExternalDocumentId());
        esignDocumentService.uploadEsignatureDocument(signature, document, isFullySignedDocument);
    }

    private void silentlySendPandaDocDocument(String id) {
        ApiClient client = getPandaDocApiClient();
        DocumentsApi documentsApi = new DocumentsApi(client);

        DocumentDetailsResponse documentDetails = getPandaDocDocumentDetails(id);
        // Check if document is in sendable status
        fromPandaDocDocumentStatus(documentDetails.getStatus())
            .filter(PandaDocDocumentStatus::isSendable)
            .orElseThrow(() -> new ConflictingStateException("Document must be in DRAFT status to silently send"));

        var request = new DocumentSendRequest();
        request.setSilent(true);
        callInPandaDocExceptionContext(() -> documentsApi.sendDocument(id, request));
    }

    private DocumentDetailsResponse getPandaDocDocumentDetails(String id) {
        try {
            ApiClient client = getPandaDocApiClient();
            DocumentsApi documentsApi = new DocumentsApi(client);
            var response = documentsApi.detailsDocumentWithHttpInfo(id);
            if (response.getStatusCode() == 200) {
                return response.getData();
            }
            if (response.getStatusCode() == 404) {
                esignDAO.updateSignatureStatus(ElectronicSignatureProvider.PANDADOC, id, ElectronicSignatureStatus.FAILED);
                throw new ExternalDependencyException(
                    "Document not found, updating status to FAILED. Please try requesting a new e-signature document."
                );
            }

            throw new ExternalDependencyException("Failed to get PandaDoc document status");
        } catch (ApiException e) {
            LOGGER.error("Exception while trying to get PandaDoc document status for id={}", id, e);
            throw new ExternalDependencyException(e.getResponseBody());
        }
    }

    public void voidPandaDocDocument(String id) {
        throwIfFeatureNotEnabled();

        ApiClient client = getPandaDocApiClient();
        DocumentsApi documentsApi = new DocumentsApi(client);
        DocumentDetailsResponse documentDetails = getPandaDocDocumentDetails(id);
        PandaDocDocumentStatus documentStatus = fromPandaDocDocumentStatus(documentDetails.getStatus()).orElseThrow(() ->
            new ServiceFailureException("Unable to get PandaDoc document status")
        );
        if (documentStatus == PandaDocDocumentStatus.VOIDED) {
            throw new ConflictingStateException("Document is already voided");
        } else if (!documentStatus.isVoidable()) {
            throw new ConflictingStateException("Document must be in SENT or VIEWED status to void");
        }

        var request = new DocumentStatusChangeRequest();
        request.setStatus(DocumentStatusRequestEnum.NUMBER_11);
        callInPandaDocExceptionContext(() -> {
            documentsApi.changeDocumentStatus(id, request);
            return null;
        });
    }

    private byte[] downloadDocumentFromPandaDoc(String id) {
        ApiClient client = getPandaDocApiClient();
        DocumentsApi documentsApi = new DocumentsApi(client);

        File result = callInPandaDocExceptionContext(() -> documentsApi.downloadDocument(id, null, null, null, null, null));
        try (var inputStream = new FileInputStream(result)) {
            return inputStream.readAllBytes();
        } catch (IOException e) {
            LOGGER.error("Failed to read downloaded PandaDoc document with id={}", id, e);
            throw new ServiceFailureException("Failed to read downloaded document", e);
        }
    }

    public void resendEsignatureDocument(UUID id) {
        ElectronicSignature signature = esignDAO.getSignatureById(id);
        if (signature.getStatus().isTerminal()) {
            throw new ConflictingStateException("Cannot resend a document in terminal state");
        }
        switch (signature.getDocumentProvider()) {
            case DOCUSIGN -> docuSignService.resendEnvelope(signature.getExternalDocumentId());
            case PANDADOC -> resendPandaDocDocument(signature.getExternalDocumentId());
        }
    }

    public void voidEsignatureDocument(UUID id) {
        ElectronicSignature signature = esignDAO.getSignatureById(id);
        if (signature.getStatus().isTerminal()) {
            throw new ConflictingStateException("Cannot void a document in terminal state");
        }
        switch (signature.getDocumentProvider()) {
            case DOCUSIGN -> docuSignService.voidEnvelope(signature.getExternalDocumentId());
            case PANDADOC -> {
                voidPandaDocDocument(signature.getExternalDocumentId());
                dispatchJobToSyncToHubSpot(signature);
            }
        }

        // TODO: Track who initiated void?
        esignDAO.updateSignatureStatus(signature.getDocumentProvider(), signature.getExternalDocumentId(), ElectronicSignatureStatus.VOIDED);

        publishEsignatureVoidedEvent(signature);
    }

    private void publishEsignatureVoidedEvent(ElectronicSignature signature) {
        try {
            Order order = orderGetService.getOrderByOrderId(signature.getOrderId());
            OrderJson orderJson = orderMapper.orderToJson(order);

            eventPublishingService.publishEventInTransaction(
                dslContextProvider.get(order.getTenantId()),
                EventType.ESIGNATURE_VOIDED,
                order.getTenantId(),
                order.getEntityId(),
                order.getAccountId(),
                OBJECT_MAPPER.writeValueAsBytes(orderJson)
            );
        } catch (Exception e) {
            LOGGER.warn("Failed to publish eSign voided event for orderId={}", signature.getOrderId(), e);
        }
    }

    public void forceVoidEsignatureDocumentForOrder(String orderId) {
        ElectronicSignatureProvider provider = esignIntegrationService
            .getEsignProvider()
            .orElseThrow(() -> new IllegalStateException("No e-sign integration found"));
        ElectronicSignature signature = esignDAO
            .getOngoingElectronicSignatureByOrderId(provider, orderId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ESIGNATURE, orderId));
        if (signature.getStatus().isTerminal()) {
            throw new ConflictingStateException("Cannot void a document in terminal state");
        }
        try {
            voidEsignatureDocument(signature.getId());
        } catch (Exception e) {
            String documentId = signature.getExternalDocumentId();
            LOGGER.warn("Forcefully marking {} document with externalDocumentId={} as VOIDED", provider, documentId, e);
            esignDAO.markSignatureAsVoided(provider, documentId);
        }
    }

    public void voidSignedDocumentForOrderInternal(String orderId) {
        Order order = orderGetService.getOrderByOrderId(orderId);
        if (order.getStatus() == OrderStatus.EXECUTED) {
            throw new ConflictingStateException("cannot void e-signature for an executed order");
        }

        ElectronicSignature signature = getElectronicSignatureByOrderId(orderId);
        if (signature.getStatus() != ElectronicSignatureStatus.COMPLETED) {
            throw new ConflictingStateException(String.format("cannot internally void e-signature for a %s document.", signature.getStatus()));
        }

        // for a completed document, we are just marking them as voided internally. We are not voiding them at the integrations fyi.
        esignDAO.markSignatureAsVoided(signature.getDocumentProvider(), signature.getExternalDocumentId());
        forceRegenerateOrderFormPdfForOrder(signature.getOrderId(), signature.getIsComposite());
    }

    private void resendPandaDocDocument(String documentId) {
        ElectronicSignature signature = esignDAO
            .getSignatureByDocumentId(ElectronicSignatureProvider.PANDADOC, documentId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ESIGNATURE, documentId));

        DocumentDetailsResponse documentDetails = getPandaDocDocumentDetails(documentId);
        // Check if document is in resendable status
        fromPandaDocDocumentStatus(documentDetails.getStatus())
            .filter(PandaDocDocumentStatus::isResendable)
            .orElseThrow(() -> new ConflictingStateException("Document must be in SENT or VIEWED status to resend"));
        if (CollectionUtils.isEmpty(documentDetails.getRecipients())) {
            return;
        }

        var recipients = documentDetails.getRecipients().stream().map(EsignService::convertPandaDocRecipient).toList();
        Optional<PandaDocRecipient> nextRecipient = recipients
            .stream()
            .filter(Predicate.not(PandaDocRecipient::getHasCompleted))
            .min(Comparator.comparing(PandaDocRecipient::getSigningOrder));
        if (nextRecipient.isEmpty()) {
            LOGGER.info("PandaDoc document {} has no recipients to resend to", documentId);
            throw new IllegalArgumentException("Document has no recipients to resend to");
        }

        var recipient = nextRecipient.get();
        var request = buildEmailRequest(recipient, documentId, documentDetails.getName(), signature).orElseThrow(() -> {
            LOGGER.error("Unable to build email request for PandaDoc document {}", documentId);
            return new ServiceFailureException("Unable to build email request");
        });
        esignEmailService.sendEmail(request);
    }

    private static PandaDocRecipient convertPandaDocRecipient(DocumentDetailsResponseRecipients recipient) {
        var mappedRecipient = new PandaDocRecipient();
        mappedRecipient.setHasCompleted(Boolean.TRUE.equals(recipient.getHasCompleted()));
        mappedRecipient.setEmail(recipient.getEmail());
        mappedRecipient.setFirstName(recipient.getFirstName());
        mappedRecipient.setLastName(recipient.getLastName());
        mappedRecipient.setSigningOrder((int) Float.parseFloat(Objects.requireNonNull(recipient.getSigningOrder()).toString()));
        return mappedRecipient;
    }

    // TODO: Need to think about recovery steps if this fails midway
    //  case 1: document is created in PD, but failed to send
    //  case 2: document is sent, but failed to record in DB
    public void requestEsignatureViaPandaDoc(EsignRequest esignRequest, boolean isTest) {
        throwIfFeatureNotEnabled();

        validateEsignRequest(esignRequest, ElectronicSignatureProvider.PANDADOC);

        // Create document
        DocumentCreateResponse documentCreateResponse;
        if (isTest && billyConfiguration.isLocalOrCi()) {
            LOGGER.info("Test mode enabled, not creating document");
            documentCreateResponse = new DocumentCreateResponse();
            documentCreateResponse.setId(UUID.randomUUID().toString());
        } else {
            documentCreateResponse = createDocumentViaPdfUpload(esignRequest);
        }

        // Create record in esignature table
        var esign = createEsignRecord(esignRequest, documentCreateResponse.getId(), ElectronicSignatureProvider.PANDADOC);
        upsertEmailContacts(esignRequest);

        esignDAO.insertSignature(esign);
    }

    public void requestEsignatureViaDocusign(EsignRequest esignRequest) {
        validateEsignRequest(esignRequest, ElectronicSignatureProvider.DOCUSIGN);

        // Add esign initiator to additional recipients
        User initiatedBy = getInitiatedByUser(esignRequest.getOrderId());
        EmailContact initiatorEmailContact = new EmailContact(initiatedBy);
        if (CollectionUtils.isEmpty(esignRequest.getAdditionalRecipients())) {
            esignRequest.setAdditionalRecipients(List.of(initiatorEmailContact));
        } else {
            esignRequest.getAdditionalRecipients().add(initiatorEmailContact);
        }

        EnvelopeSummary envelopeSummary = docuSignService.requestEsignature(esignRequest);
        if (featureService.isEnabled(Feature.DOCUSIGN_V2)) {
            var esign = createEsignRecord(esignRequest, envelopeSummary.getEnvelopeId(), ElectronicSignatureProvider.DOCUSIGN);
            upsertEmailContacts(esignRequest);
            esignDAO.insertSignature(esign);
        }
    }

    private void validateEsignRequest(EsignRequest esignRequest, ElectronicSignatureProvider electronicSignatureProvider) {
        validateNoExistingEsignatureRequest(esignRequest.getOrderId());
        validateOrderInApprovedStatus(esignRequest.getOrderId(), esignRequest.getIsComposite());

        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(
            billyConfiguration,
            tenantIdProvider.provideTenantIdString()
        );
        if (
            tenantScopedConfig.getElectronicSignatureConfiguration().isIgnoreSignatureVerificationForResellers() &&
            orderResellerService.isResellerOrder(esignRequest.getOrderId())
        ) {
            LOGGER.info("Reseller order {} is ignored for esignature verification", esignRequest.getOrderId());
            return;
        }

        validateAccountSignatory(esignRequest);
        var signingOrder = tenantSettingService.getSigningOrder();
        if (signingOrder != SigningOrder.ACCOUNT_ONLY) {
            validateTenantSignatory(esignRequest);
            validateAccountAndTenantSignatoryEmailsDoNotMatch(esignRequest);
        }
        validateAdditionalRecipients(esignRequest);

        // TODO: Not validating PDF for composite order for now
        if (!esignRequest.getIsComposite()) {
            validateOrderDocumentSupportsEsign(esignRequest.getOrderId(), signingOrder, electronicSignatureProvider);
        }
    }

    private void validateOrderInApprovedStatus(String orderId, boolean isComposite) {
        Order order = retrieveOrder(orderId, isComposite);
        if (order.getStatus() != OrderStatus.APPROVED) {
            throw new InvalidInputException("Order must be in APPROVED status to request eSignature");
        }
    }

    private void upsertEmailContacts(EsignRequest esignRequest) {
        EmailNotifiersList emailNotifiersList = new EmailNotifiersList();

        List<String> toIds = new ArrayList<>();
        toIds.add(esignRequest.getAccountSignatory().getContactId());
        if (esignRequest.getTenantSignatory() != null) {
            toIds.add(esignRequest.getTenantSignatory().getContactId());
        }
        emailNotifiersList.setToIds(toIds);

        if (CollectionUtils.isNotEmpty(esignRequest.getAdditionalRecipients())) {
            List<String> ccIds = esignRequest.getAdditionalRecipients().stream().map(EmailContact::getContactId).toList();
            emailNotifiersList.setCcIds(ccIds);
        }

        emailContactListService.updateEmailContactsForOrder(esignRequest.getOrderId(), emailNotifiersList);
    }

    private void validateAccountSignatory(EsignRequest esignRequest) {
        Order order = retrieveOrder(esignRequest.getOrderId(), esignRequest.getIsComposite());
        String accountId = order.getAccountId();

        Optional<AccountStub> resellerAccount = orderResellerService.getResellerAccount(order);
        if (resellerAccount.isPresent()) {
            accountId = resellerAccount.get().getAccountId();
        }

        String accountSignatoryEmail = esignRequest.getAccountSignatory().getEmail();
        List<AccountContact> accountContacts = accountGetService.getAccountContacts(accountId, false);
        accountContacts
            .stream()
            .filter(accountContact -> StringUtils.equalsIgnoreCase(accountContact.getEmail(), accountSignatoryEmail))
            .findFirst()
            .orElseThrow(() -> new InvalidInputException(String.format("Customer email %s not in account contacts", accountSignatoryEmail)));
    }

    private void validateOrderDocumentSupportsEsign(
        String orderId,
        SigningOrder signingOrder,
        ElectronicSignatureProvider electronicSignatureProvider
    ) {
        Optional<Integer> numberOfSignatureBlocks = orderDocumentService.getNumberOfSignatureBlocksForOrderDocument(
            orderId,
            electronicSignatureProvider
        );
        numberOfSignatureBlocks.ifPresent(integer ->
            validateNumberOfSignaturesOnOrderForm(orderId, integer, signingOrder, electronicSignatureProvider)
        );
    }

    private static void validateNumberOfSignaturesOnOrderForm(
        String orderId,
        int numberOfBlocks,
        SigningOrder signingOrder,
        ElectronicSignatureProvider electronicSignatureProvider
    ) {
        Validator.validateNonNullArgument(signingOrder);
        int expectedNumberOfSignatureBlocks = signingOrder.getExpectedNumberOfSignatures();
        if ((numberOfBlocks == 0) || (expectedNumberOfSignatureBlocks != numberOfBlocks)) {
            LOGGER.info(
                "Order Form: # of signature blocks does not match signing order. Order id: {}, number of blocks: {}, signing order: {}, eSign provider: {}",
                orderId,
                numberOfBlocks,
                signingOrder,
                electronicSignatureProvider
            );
            throw new InvalidInputException("This order is not eligible for eSignature, please contact your admin.");
        }
    }

    private void validateNoExistingEsignatureRequest(String orderId) {
        ElectronicSignatureProvider provider = esignIntegrationService
            .getEsignProvider()
            .orElseThrow(() -> new IllegalStateException("No esign integration found"));
        Optional<ElectronicSignature> signature = esignDAO.getSignatureByOrderId(provider, orderId, false);
        if (signature.isPresent()) {
            throw new IllegalStateException("An electronic signature request is pending.");
        }
    }

    private void validateTenantSignatory(EsignRequest esignRequest) {
        var signatories = esignTenantSignatoryDAO.getTenantSignatories(tenantIdProvider.provideTenantIdString());
        var emails = signatories
            .stream()
            .map(EsignTenantSignatory::getUserId)
            .map(id -> {
                try {
                    User user = userService.getUser(id);
                    return user.getEmail();
                } catch (ObjectNotFoundException e) {
                    return StringUtils.EMPTY;
                }
            });

        String tenantSignatoryEmail = esignRequest.getTenantSignatory().getEmail();
        if (emails.noneMatch(e -> e.equalsIgnoreCase(tenantSignatoryEmail))) {
            throw new IllegalArgumentException(String.format("Tenant signatory email %s not in tenant signatories", tenantSignatoryEmail));
        }
    }

    private void validateAccountAndTenantSignatoryEmailsDoNotMatch(EsignRequest esignRequest) {
        String tenantSignatoryEmail = esignRequest.getTenantSignatory().getEmail();
        String accountSignatoryEmail = esignRequest.getAccountSignatory().getEmail();
        if (accountSignatoryEmail.equalsIgnoreCase(tenantSignatoryEmail)) {
            throw new InvalidInputException("Account signatory and tenant signatory emails cannot be the same");
        }
    }

    private void validateAdditionalRecipients(EsignRequest esignRequest) {
        List<EmailContact> additionalRecipients = esignRequest.getAdditionalRecipients();
        if (CollectionUtils.isEmpty(additionalRecipients)) {
            return;
        }

        List<String> emailList = additionalRecipients.stream().map(EmailContact::getEmail).toList();
        String tenantSignatoryEmail = Optional.ofNullable(esignRequest.getTenantSignatory()).map(EmailContact::getEmail).orElse(null);
        boolean containsSignatory = emailList
            .stream()
            .anyMatch(
                e ->
                    StringUtils.equalsIgnoreCase(e, esignRequest.getAccountSignatory().getEmail()) ||
                    (tenantSignatoryEmail != null && StringUtils.equalsIgnoreCase(e, tenantSignatoryEmail))
            );
        if (containsSignatory) {
            throw new InvalidInputException("Additional recipients cannot contain account signatory or tenant signatory");
        }
    }

    private Order retrieveOrder(String orderId, boolean isComposite) {
        if (isComposite) {
            return orderGetService
                .getOrderIdsInCompositeOrder(orderId)
                .stream()
                .findFirst()
                .map(orderGetService::getOrderByOrderId)
                .orElseThrow(() -> new ServiceFailureException("Unable to find any order in composite order with id %s".formatted(orderId)));
        } else {
            return orderGetService.getOrderByOrderId(orderId);
        }
    }

    private ElectronicSignature createEsignRecord(EsignRequest esignRequest, String documentId, ElectronicSignatureProvider provider) {
        var esign = new ElectronicSignature();
        esign.setExternalDocumentId(documentId);
        esign.setStatus(ElectronicSignatureStatus.PENDING);
        esign.setOrderId(esignRequest.getOrderId());
        esign.setDocumentProvider(provider);
        // FIXME: Why are we storing email and not user ID when the eSign flow checks for a USER before sending?
        // FIXME: Also this call is redundant. We are potentially making two calls to order and user services.
        esign.setInitiatedBy(getInitiatedByEmail(esignRequest.getOrderId()));
        esign.setIsComposite(esignRequest.getIsComposite());
        return esign;
    }

    private DocumentCreateResponse createDocumentViaPdfUpload(EsignRequest esignRequest) {
        try (
            InputStream orderPdf = esignRequest.getIsComposite()
                ? compositeOrderDocumentService
                    .getCompositeOrderDocumentCreateIfNeeded(esignRequest.getOrderId())
                    .orElseThrow(() -> {
                        LOGGER.warn("Error getting composite order PDF for {}", esignRequest.getOrderId());
                        return new ServiceFailureException("Composite order document is not available.");
                    })
                : orderDocumentService
                    .getOrderDocumentCreateIfNeeded(esignRequest.getOrderId())
                    .orElseThrow(() -> {
                        LOGGER.warn("Error getting order PDF for {}", esignRequest.getOrderId());
                        return new ServiceFailureException("Order document is not available.");
                    })
        ) {
            JSONObject jsonData = buildDocumentCreateRequestJson(esignRequest);

            RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", "file.pdf", RequestBody.create(orderPdf.readAllBytes(), MediaType.parse("application/pdf")))
                .addFormDataPart("data", jsonData.toString())
                .build();

            Request request = new Request.Builder()
                .addHeader(HTTP_CLIENT_AUTHORIZATION_HEADER, HTTP_CLIENT_API_KEY_PREFIX + secretsService.retrieveStaticSecret(PANDA_DOC_API_KEY))
                .url(PANDA_DOC_API_DOCUMENTS_ENDPOINT)
                .post(requestBody)
                .build();

            Response response = HTTP_CLIENT.newCall(request).execute();
            return getPandaDocApiClient().handleResponse(response, new TypeToken<DocumentCreateResponse>() {}.getType());
        } catch (Exception e) {
            LOGGER.warn("Error uploading PDF to PandaDoc", e);
            throw new ServiceFailureException("error processing your request, please try again later");
        }
    }

    // TODO: Refactor hard-coded values
    private JSONObject buildDocumentCreateRequestJson(EsignRequest esignRequest) {
        JSONObject jsonData = new JSONObject();

        String tenantName = tenantService.getCurrentTenant().getName();
        Order order = retrieveOrder(esignRequest.getOrderId(), esignRequest.getIsComposite());
        Account account = accountGetService.getAccount(order.getAccountId());
        jsonData.put("name", String.format(PANDA_DOC_TEMPLATE_NAME_FORMAT, account.getName(), tenantName));

        SigningOrder signingOrder = tenantSettingService.getSigningOrder();
        JSONArray recipients = new JSONArray();

        JSONObject accountSignatory = new JSONObject();
        String accountSignatoryName = esignRequest.getAccountSignatory().getName();
        String accountSignatoryEmail = esignRequest.getAccountSignatory().getEmail();
        Pair<String, String> customerNameParts = getFirstAndLastName(accountSignatoryName);
        accountSignatory.put("first_name", customerNameParts.getLeft());
        accountSignatory.put("last_name", customerNameParts.getRight());
        accountSignatory.put("email", accountSignatoryEmail);
        accountSignatory.put("role", "s1");
        accountSignatory.put("signing_order", signingOrder == SigningOrder.TENANT_FIRST ? "2" : "1");
        recipients.put(accountSignatory);

        if (signingOrder != SigningOrder.ACCOUNT_ONLY) {
            JSONObject tenantSignatory = new JSONObject();
            String tenantSignatoryName = esignRequest.getTenantSignatory().getName();
            String tenantSignatoryEmail = esignRequest.getTenantSignatory().getEmail();
            Pair<String, String> sellerNameParts = getFirstAndLastName(tenantSignatoryName);
            tenantSignatory.put("first_name", sellerNameParts.getLeft());
            tenantSignatory.put("last_name", sellerNameParts.getRight());
            tenantSignatory.put("email", tenantSignatoryEmail);
            tenantSignatory.put("role", "s2");
            tenantSignatory.put("signing_order", signingOrder == SigningOrder.TENANT_FIRST ? "1" : "2");
            recipients.put(tenantSignatory);
        }

        List<EmailContact> additionalRecipients = esignRequest.getAdditionalRecipients();
        if (CollectionUtils.isNotEmpty(additionalRecipients)) {
            additionalRecipients.forEach(additionalRecipient -> {
                JSONObject recipient = new JSONObject();
                Pair<String, String> additionalRecipientNameParts = getFirstAndLastName(additionalRecipient.getName());
                recipient.put("first_name", additionalRecipientNameParts.getLeft());
                recipient.put("last_name", additionalRecipientNameParts.getRight());
                recipient.put("email", additionalRecipient.getEmail());
                recipient.put("signing_order", signingOrder == SigningOrder.ACCOUNT_ONLY ? "2" : "3");
                recipients.put(recipient);
            });
        }

        jsonData.put("recipients", recipients);

        JSONObject metadata = new JSONObject();
        metadata.put("env_name", billyConfiguration.getEnvName());

        jsonData.put("metadata", metadata);

        return jsonData;
    }

    private Pair<String, String> getFirstAndLastName(String fullName) {
        Validator.validateStringNotBlank(fullName, "fullName");

        String[] nameParts = fullName.trim().split("\\s+");
        if (nameParts.length == 1) {
            return Pair.of(nameParts[0], StringUtils.EMPTY);
        }

        String firstName = String.join(" ", Arrays.copyOfRange(nameParts, 0, nameParts.length - 1));
        String lastName = nameParts[nameParts.length - 1];
        return Pair.of(firstName, lastName);
    }

    private ApiClient getPandaDocApiClient() {
        ApiClient client = Configuration.getDefaultApiClient();
        client.setBasePath(PANDA_DOC_API_BASE_URL);

        String apiKeySecret = secretsService.retrieveStaticSecret(PANDA_DOC_API_KEY);
        ApiKeyAuth apiKey = (ApiKeyAuth) client.getAuthentication("apiKey");
        apiKey.setApiKey(apiKeySecret);
        apiKey.setApiKeyPrefix("API-Key");
        return client;
    }

    private User getInitiatedByUser(String orderId) {
        Optional<String> emailOfCurrentAuthPrincipal = getEmailOfCurrentAuthPrincipal();
        if (emailOfCurrentAuthPrincipal.isPresent()) {
            Optional<User> currentAuthPrincipalUser = userService.getUserByEmail(emailOfCurrentAuthPrincipal.get());
            if (currentAuthPrincipalUser.isEmpty()) {
                LOGGER.error("Was not able to find a user with email: {}", emailOfCurrentAuthPrincipal.get());
                throw new ServiceFailureException("Unable to initiate electronic signature.");
            }
            return currentAuthPrincipalUser.get();
        }

        return getOrderOwnerOrThrow(orderId);
    }

    private String getInitiatedByEmail(String orderId) {
        Optional<String> emailOfCurrentAuthPrincipal = getEmailOfCurrentAuthPrincipal();
        if (emailOfCurrentAuthPrincipal.isPresent()) {
            return emailOfCurrentAuthPrincipal.get();
        }

        User orderOwner = getOrderOwnerOrThrow(orderId);
        return orderOwner.getEmail();
    }

    private User getOrderOwnerOrThrow(String orderId) {
        Optional<String> orderOwnerUserId = orderGetService.getOrderOwner(orderId);
        if (orderOwnerUserId.isEmpty()) {
            LOGGER.info("Failed to initiate eSign: Order {} has no owner and no user is logged in.", orderId);
            throw new IllegalStateException("Requester not found.");
        }

        return userService.getUser(orderOwnerUserId.get());
    }

    private Optional<String> getEmailOfCurrentAuthPrincipal() {
        Optional<BillyAuthPrincipal> authPrincipal = CurrentUserProvider.getAuthPrincipal();
        return authPrincipal.flatMap(this::getEmailOfAuthPrincipal);
    }

    private Optional<String> getEmailOfAuthPrincipal(BillyAuthPrincipal billyAuthPrincipal) {
        Optional<String> emailOfLoggedInUser = getEmailOfLoggedInUser(billyAuthPrincipal);
        if (emailOfLoggedInUser.isPresent()) {
            return emailOfLoggedInUser;
        }

        return getEmailOfUserOrTenantFromApiKey(billyAuthPrincipal);
    }

    private Optional<String> getEmailOfLoggedInUser(BillyAuthPrincipal billyAuthPrincipal) {
        if (billyAuthPrincipal.getAuthUser().isEmpty()) {
            return Optional.empty();
        }

        return billyAuthPrincipal.getAuthUser().get().getEmailAddress();
    }

    private Optional<String> getEmailOfUserOrTenantFromApiKey(BillyAuthPrincipal billyAuthPrincipal) {
        if (billyAuthPrincipal.getApiKeyAuthUser().isEmpty()) {
            return Optional.empty();
        }

        UUID keyId = billyAuthPrincipal.getApiKeyAuthUser().get().getApiKeyId();
        ApiKeyMetaData apiKeyMetaData = apiKeyService.getApiKeyById(keyId.toString());
        if (StringUtils.isBlank(apiKeyMetaData.getUserId())) {
            return Optional.ofNullable(tenantService.getCurrentTenant().getEmail());
        }

        Optional<User> user = userService.getUserOptional(apiKeyMetaData.getUserId());
        return user.map(User::getEmail);
    }

    private void throwIfFeatureNotEnabled() {
        if (platformFeatureService.getFeatureEnablement(PlatformFeature.ESIGN).isEmpty()) {
            throw new UnsupportedOperationException("Esign feature is not enabled");
        }
    }

    public EsignRequest upsertEsignDetails(EsignRequest esignRequest) {
        if (esignRequest.getAccountSignatory() != null) {
            validateAccountSignatory(esignRequest);
        }
        if (esignRequest.getTenantSignatory() != null) {
            validateTenantSignatory(esignRequest);
        }
        if (esignRequest.getAccountSignatory() != null && esignRequest.getTenantSignatory() != null) {
            validateAccountAndTenantSignatoryEmailsDoNotMatch(esignRequest);
        }
        validateAdditionalRecipients(esignRequest);
        validateNoExistingEsignatureRequest(esignRequest.getOrderId());

        esignDAO.upsertEsignDetails(toEsignDetails(esignRequest));
        return getEsignDetailsForOrder(esignRequest.getOrderId());
    }

    public EsignRequest getEsignDetailsForOrder(String orderId) {
        Validator.validateNonNullArgument(orderId);
        EsignDetails esignDetails = esignDAO.getEsignDetails(orderId);
        return toEsignRequest(esignDetails);
    }

    private EsignDetails toEsignDetails(EsignRequest esignRequest) {
        String accountSignatoryContactId = Optional.ofNullable(esignRequest.getAccountSignatory()).map(EmailContact::getContactId).orElse(null);
        String tenantSignatoryUserId = Optional.ofNullable(esignRequest.getTenantSignatory()).map(EmailContact::getContactId).orElse(null);
        List<String> additionalRecipientsUserId = getAdditionalRecipientsUserIdFromEsignRequest(esignRequest.getAdditionalRecipients());

        accountGetService.getContact(accountSignatoryContactId);
        var signingOrder = tenantSettingService.getSigningOrder();
        if (signingOrder != SigningOrder.ACCOUNT_ONLY) {
            Validator.validateStringNotBlank(tenantSignatoryUserId, "Tenant signatory user must be chosen.");
            userService.getUser(tenantSignatoryUserId);
        }
        additionalRecipientsUserId.forEach(userService::getUser);

        return ImmutableEsignDetails.builder()
            .id(null)
            .orderId(esignRequest.getOrderId())
            .isComposite(BooleanUtils.isTrue(esignRequest.getIsComposite()))
            .accountSignatoryContactId(accountSignatoryContactId)
            .tenantSignatoryUserId(tenantSignatoryUserId)
            .additionalRecipientsUserId(additionalRecipientsUserId)
            .build();
    }

    private EsignRequest toEsignRequest(EsignDetails esignDetail) {
        EsignRequest esignRequest = new EsignRequest();
        esignRequest.setOrderId(esignDetail.getOrderId());
        esignRequest.setIsComposite(esignDetail.getIsComposite());
        addAccountSignatoryToEsignRequest(esignRequest, esignDetail.getAccountSignatoryContactId());
        addTenantSignatoryToEsignRequest(esignRequest, esignDetail.getTenantSignatoryUserId());
        addAdditionalRecipientsToEsignRequest(esignRequest, esignDetail.getAdditionalRecipientsUserId());
        return esignRequest;
    }

    private List<String> getAdditionalRecipientsUserIdFromEsignRequest(List<EmailContact> additionalRecipients) {
        if (CollectionUtils.isEmpty(additionalRecipients)) {
            return List.of();
        }

        return additionalRecipients.stream().map(EmailContact::getContactId).toList();
    }

    private void addAccountSignatoryToEsignRequest(EsignRequest esignRequest, String accountSignatoryContactId) {
        if (StringUtils.isBlank(accountSignatoryContactId)) {
            return;
        }

        AccountContact contact = accountGetService.getContact(accountSignatoryContactId);
        EmailContact emailContact = new EmailContact();
        emailContact.setEmail(contact.getEmail());
        emailContact.setName(contact.getFullName());
        emailContact.setType(ContactType.ACCOUNT_CONTACT);
        emailContact.setContactId(accountSignatoryContactId);

        esignRequest.setAccountSignatory(emailContact);
    }

    private void addTenantSignatoryToEsignRequest(EsignRequest esignRequest, String tenantSignatoryUserId) {
        if (StringUtils.isBlank(tenantSignatoryUserId)) {
            return;
        }

        User user = userService.getUser(tenantSignatoryUserId);
        EmailContact contact = emailContactForUser(user);
        esignRequest.setTenantSignatory(contact);
    }

    private void addAdditionalRecipientsToEsignRequest(EsignRequest esignRequest, List<String> additionalRecipientsUserId) {
        if (CollectionUtils.isEmpty(additionalRecipientsUserId)) {
            esignRequest.setAdditionalRecipients(List.of());
            return;
        }

        List<User> users = userService.getUsersByUserIds(additionalRecipientsUserId);
        List<EmailContact> contacts = users.stream().map(this::emailContactForUser).toList();
        esignRequest.setAdditionalRecipients(contacts);
    }

    private EmailContact emailContactForUser(User user) {
        EmailContact emailContact = new EmailContact();
        emailContact.setType(ContactType.USER);
        emailContact.setContactId(user.getUserId());
        emailContact.setEmail(user.getNormalizedEmail());
        emailContact.setName(user.getDisplayName());
        return emailContact;
    }

    public void syncEsignToHubSpot(String orderId) {
        if (orderId == null) {
            return;
        }

        Optional<ElectronicSignature> electronicSignature = getElectronicSignatureByOrderIdIncludeVoided(orderId);
        if (electronicSignature.isEmpty()) {
            LOGGER.warn("Did not find an eSign record for order: {}", orderId);
            return;
        }

        ElectronicSignatureStatus status = electronicSignature.get().getStatus();
        if (status == ElectronicSignatureStatus.VOIDED) {
            // Clear both fields from HubSpot
            hubSpotService.syncEsignDetailsForOrderToHubSpot(orderId, StringUtils.EMPTY, StringUtils.EMPTY);
            return;
        }

        String link = Optional.ofNullable(electronicSignature.get().getLink()).orElse(StringUtils.EMPTY);
        hubSpotService.syncEsignDetailsForOrderToHubSpot(orderId, status.name(), link);
    }

    private void dispatchJobToSyncToHubSpot(ElectronicSignature electronicSignature) {
        try {
            String tenantId = tenantIdProvider.provideTenantIdString();
            if (!hubSpotIntegrationService.hasCompletedHubSpotIntegration(tenantId)) {
                return;
            }

            var esignProvider = esignIntegrationService.getEsignProvider();
            if (esignProvider.isEmpty() || esignProvider.get() != ElectronicSignatureProvider.PANDADOC) {
                return;
            }

            Order order = retrieveOrder(electronicSignature.getOrderId(), electronicSignature.getIsComposite());
            if (
                StringUtils.isBlank(order.getSfdcOpportunityId()) ||
                StringUtils.startsWith(order.getSfdcOpportunityId(), SalesforceService.INVALID_OPPORTUNITY_ID_PREFIX) ||
                !order.getIsPrimaryOrderForSfdcOpportunity()
            ) {
                return;
            }

            tenantJobDispatcherService.dispatch(
                ImmutableTenantJob.builder()
                    .module(TenantJobModule.HUBSPOT)
                    .jobType(TenantJobType.HUBSPOT_ELECTRONIC_SIGNATURE_SYNC)
                    .objectModel(electronicSignature.getIsComposite() ? TenantJobObjectModel.COMPOSITE_ORDER : TenantJobObjectModel.ORDER)
                    .objectId(electronicSignature.getOrderId())
                    .partitionKey(tenantId)
                    .build()
            );
        } catch (Exception e) {
            LOGGER.error("Error dispatching job to sync eSign to HubSpot", e);
        }
    }

    private static <T> T callInPandaDocExceptionContext(Callable<T> callable) {
        try {
            return callable.call();
        } catch (ApiException e) {
            if (e.getCode() >= 400 && e.getCode() < 500) {
                throw new InvalidInputException("PandaDoc API returned client-side error: " + e.getMessage(), e);
            }
            throw new ExternalDependencyException("PandaDoc API returned server error: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new ServiceFailureException("Error calling PandaDoc API", e);
        }
    }
}
