package com.subskribe.billy.esign.services;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.compositeorder.service.CompositeOrderDocumentService;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.email.model.EmailContacts;
import com.subskribe.billy.email.model.EmailData;
import com.subskribe.billy.email.model.EmailType;
import com.subskribe.billy.email.model.ImmutableEmailAttachmentData;
import com.subskribe.billy.email.model.ImmutableEmailData;
import com.subskribe.billy.email.services.EmailService;
import com.subskribe.billy.esign.model.EsignEmailRequest;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.order.services.OrderDocumentService;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import javax.mail.MessagingException;
import org.apache.commons.lang3.StringUtils;

public class EsignEmailService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EsignEmailService.class);

    private static final String ESIGN_SIGNATURE_EMAIL_TEMPLATE = "html/esign-signature-email.html";

    private static final String ESIGN_SIGNATURE_EMAIL_SUBJECT_FORMAT = "%s - Signature requested from %s";

    private static final String ESIGN_COMPLETED_EMAIL_TEMPLATE = "html/esign-completed-email.html";

    private static final String ESIGN_COMPLETED_EMAIL_SUBJECT_FORMAT = "%s - Signature completed from %s";

    // FIXME: Move to config
    private static final String PROD_FROM_EMAIL_ADDRESS = "<EMAIL>";

    private static final String NON_PROD_FROM_EMAIL_ADDRESS = "<EMAIL>";

    private final String fromEmailAddress;

    private final EmailService emailService;

    private final PlatformFeatureService platformFeatureService;

    private final OrderDocumentService orderDocumentService;

    private final CompositeOrderDocumentService compositeOrderDocumentService;

    private final DocumentTemplateGetService documentTemplateGetService;

    @Inject
    public EsignEmailService(
        EmailService emailService,
        BillyConfiguration billyConfiguration,
        PlatformFeatureService platformFeatureService,
        OrderDocumentService orderDocumentService,
        CompositeOrderDocumentService compositeOrderDocumentService,
        DocumentTemplateGetService documentTemplateGetService
    ) {
        fromEmailAddress = (billyConfiguration.isProd()) ? PROD_FROM_EMAIL_ADDRESS : NON_PROD_FROM_EMAIL_ADDRESS;
        this.emailService = emailService;
        this.platformFeatureService = platformFeatureService;
        this.orderDocumentService = orderDocumentService;
        this.compositeOrderDocumentService = compositeOrderDocumentService;
        this.documentTemplateGetService = documentTemplateGetService;
    }

    public void sendEmail(EsignEmailRequest request) {
        throwIfFeatureNotEnabled();

        EmailContacts emailContacts = new EmailContacts();
        List<EmailContact> toContacts = List.of(new EmailContact(request.getRecipientEmail()));
        emailContacts.setToContacts(toContacts);

        try {
            // TODO: Refactor
            Map<String, String> replacementValuesMap = Map.of(
                "{{recipient_name}}",
                request.getRecipientName(),
                "{{user_name}}",
                request.getUserName(),
                "{{document_name}}",
                request.getDocumentName(),
                "{{document_url}}",
                request.getDocumentUrl(),
                "{{base64_encoded_logo}}",
                StringUtils.EMPTY, // TODO: Bring back `request.getBase64EncodedLogo()` once the Gmail issue is fixed.
                "{{tenant_name}}",
                request.getTenantName()
            );

            EmailData emailData = ImmutableEmailData.builder()
                .emailTemplate(EmailService.getTemplateFromResourceFile(ESIGN_SIGNATURE_EMAIL_TEMPLATE))
                .emailContacts(emailContacts)
                .fromEmail(fromEmailAddress)
                .subject(String.format(ESIGN_SIGNATURE_EMAIL_SUBJECT_FORMAT, request.getAccountName(), request.getTenantName()))
                .emailType(EmailType.ESIGN)
                .relatedObjectId(request.getExternalDocumentId())
                .relatedObjectParentId(Optional.ofNullable(request.getOrderId()))
                .putAllReplacementValuesMap(replacementValuesMap)
                .build();

            emailService.sendEmail(emailData);
        } catch (IOException | MessagingException e) {
            LOGGER.error("Sending esign email threw an exception: ", e);
            throw new ServiceFailureException(e);
        }
    }

    public void sendCompletedEmail(EsignEmailRequest request) {
        throwIfFeatureNotEnabled();

        EmailContacts emailContacts = new EmailContacts();
        List<EmailContact> toContacts = List.of(new EmailContact(request.getRecipientEmail()));
        emailContacts.setToContacts(toContacts);

        try {
            // TODO: Refactor
            Map<String, String> replacementValuesMap = Map.of(
                "{{recipient_name}}",
                request.getRecipientName(),
                "{{document_name}}",
                request.getDocumentName(),
                "{{base64_encoded_logo}}",
                StringUtils.EMPTY, // TODO: Bring back `request.getBase64EncodedLogo()` once the Gmail issue is fixed.
                "{{tenant_name}}",
                request.getTenantName()
            );
            Optional<ImmutableEmailAttachmentData> attachmentData = getOrderDocumentAttachmentData(request);
            EmailData emailData = ImmutableEmailData.builder()
                .emailTemplate(getEmailTemplate())
                .emailContacts(emailContacts)
                .fromEmail(fromEmailAddress)
                .subject(String.format(ESIGN_COMPLETED_EMAIL_SUBJECT_FORMAT, request.getAccountName(), request.getTenantName()))
                .emailType(EmailType.ESIGN)
                .relatedObjectId(request.getExternalDocumentId())
                .putAllReplacementValuesMap(replacementValuesMap)
                .attachmentData(attachmentData)
                .build();

            emailService.sendEmail(emailData);
        } catch (IOException | MessagingException e) {
            LOGGER.error("Sending esign email threw an exception: ", e);
            throw new ServiceFailureException(e.getMessage(), e);
        }
    }

    private String getEmailTemplate() throws IOException {
        Optional<DocumentMasterTemplate> customTemplateOptional = documentTemplateGetService
            .getDocumentMasterTemplates(DocumentTemplateType.ESIGN)
            .stream()
            .findFirst();
        if (customTemplateOptional.isPresent()) {
            return customTemplateOptional.get().getContent();
        }

        return EmailService.getTemplateFromResourceFile(ESIGN_COMPLETED_EMAIL_TEMPLATE);
    }

    private Optional<ImmutableEmailAttachmentData> getOrderDocumentAttachmentData(EsignEmailRequest request) {
        String orderId = request.getOrderId();
        Optional<InputStream> maybeOrderDocumentStream;
        if (request.isComposite()) {
            maybeOrderDocumentStream = compositeOrderDocumentService.getCompositeOrderDocumentCreateIfNeeded(orderId);
        } else {
            maybeOrderDocumentStream = orderDocumentService.getOrderDocumentCreateIfNeeded(orderId);
        }

        if (maybeOrderDocumentStream.isEmpty()) {
            LOGGER.info("Empty order document stream for order {}", orderId);
            return Optional.empty();
        }

        InputStream orderDocumentStream = maybeOrderDocumentStream.get();
        return Optional.of(
            ImmutableEmailAttachmentData.builder()
                .attachmentDocument(orderDocumentStream)
                .attachmentDocumentName(String.format("%s.pdf", orderId))
                .build()
        );
    }

    private void throwIfFeatureNotEnabled() {
        if (platformFeatureService.getFeatureEnablement(PlatformFeature.ESIGN).isEmpty()) {
            throw new UnsupportedOperationException("Esign feature is not enabled");
        }
    }
}
