package com.subskribe.billy.esign.db;

import static com.subskribe.billy.esign.model.ElectronicSignatureStatus.DELETED_STATUSES;
import static com.subskribe.billy.esign.model.ElectronicSignatureStatus.TERMINAL_STATUSES;
import static com.subskribe.billy.jooq.default_schema.Tables.ELECTRONIC_SIGNATURE;
import static com.subskribe.billy.jooq.default_schema.Tables.ELECTRONIC_SIGNATURE_AUDIT_LOG;
import static com.subskribe.billy.jooq.default_schema.Tables.ELECTRONIC_SIGNATURE_REQUEST;

import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.esign.mapper.EsignMapper;
import com.subskribe.billy.esign.model.ElectronicSignature;
import com.subskribe.billy.esign.model.ElectronicSignatureAuditLog;
import com.subskribe.billy.esign.model.ElectronicSignatureAuditLogStatus;
import com.subskribe.billy.esign.model.ElectronicSignatureProvider;
import com.subskribe.billy.esign.model.ElectronicSignatureStatus;
import com.subskribe.billy.esign.model.EsignDetails;
import com.subskribe.billy.esign.model.ImmutableElectronicSignatureAuditLog;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.jooq.default_schema.tables.records.ElectronicSignatureAuditLogRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.ElectronicSignatureRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.ElectronicSignatureRequestRecord;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class EsignDAO {

    private static final Logger LOGGER = LoggerFactory.getLogger(EsignDAO.class);

    private final EsignMapper mapper;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;

    @Inject
    public EsignDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        mapper = Mappers.getMapper(EsignMapper.class);
    }

    public ElectronicSignature insertSignature(ElectronicSignature signature) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = mapper.toRecord(signature);
        record.reset(ELECTRONIC_SIGNATURE.ID);
        record.setTenantId(tenantIdProvider.provideTenantIdString());
        record = dslContext.insertInto(ELECTRONIC_SIGNATURE).set(record).returning().fetchOne();
        return mapper.fromRecord(record);
    }

    public ElectronicSignature getSignatureById(UUID id) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = dslContext
            .select()
            .from(ELECTRONIC_SIGNATURE)
            .where(ELECTRONIC_SIGNATURE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(ELECTRONIC_SIGNATURE.ID.eq(id))
            .fetchOneInto(ElectronicSignatureRecord.class);
        return mapper.fromRecord(record);
    }

    @AllowNonRlsDataAccess
    public Optional<ElectronicSignature> getSignatureByDocumentId(ElectronicSignatureProvider provider, String documentId) {
        var record = dslContextProvider
            .get()
            .select()
            .from(ELECTRONIC_SIGNATURE)
            .where(ELECTRONIC_SIGNATURE.DOCUMENT_PROVIDER.eq(provider.toString()))
            .and(ELECTRONIC_SIGNATURE.EXTERNAL_DOCUMENT_ID.eq(documentId))
            .and(ELECTRONIC_SIGNATURE.STATUS.notIn(DELETED_STATUSES))
            .fetchOptionalInto(ElectronicSignatureRecord.class);
        return record.map(mapper::fromRecord);
    }

    public Optional<ElectronicSignature> getSignatureByOrderId(ElectronicSignatureProvider provider, String orderId, boolean includeDeleted) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var query = dslContext
            .select()
            .from(ELECTRONIC_SIGNATURE)
            .where(ELECTRONIC_SIGNATURE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(ELECTRONIC_SIGNATURE.DOCUMENT_PROVIDER.eq(provider.toString()))
            .and(ELECTRONIC_SIGNATURE.ORDER_ID.eq(orderId));

        if (!includeDeleted) {
            query = query.and(ELECTRONIC_SIGNATURE.STATUS.notIn(DELETED_STATUSES));
            Optional<ElectronicSignatureRecord> record = query.fetchOptionalInto(ElectronicSignatureRecord.class);
            return record.map(mapper::fromRecord);
        }

        List<ElectronicSignatureRecord> records = query.fetchInto(ElectronicSignatureRecord.class);
        if (CollectionUtils.isEmpty(records)) {
            return Optional.empty();
        }

        if (records.size() == 1) {
            return records.stream().findAny().map(mapper::fromRecord);
        }

        // If we have more than one record, there are two cases:
        // a) All but one is deleted: return the active one
        Optional<ElectronicSignatureRecord> result = records
            .stream()
            .filter(r -> !DELETED_STATUSES.contains(Validator.enumFromString(r.getStatus(), ElectronicSignatureStatus.class, "status")))
            .findAny();
        if (result.isPresent()) {
            return result.map(mapper::fromRecord);
        }

        // b) All are deleted: return the latest one.
        return records.stream().max(Comparator.comparing(ElectronicSignatureRecord::getUpdatedOn)).map(mapper::fromRecord).or(Optional::empty);
    }

    public Optional<ElectronicSignature> getOngoingElectronicSignatureByOrderId(ElectronicSignatureProvider provider, String orderId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = dslContext
            .select()
            .from(ELECTRONIC_SIGNATURE)
            .where(ELECTRONIC_SIGNATURE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(ELECTRONIC_SIGNATURE.DOCUMENT_PROVIDER.eq(provider.toString()))
            .and(ELECTRONIC_SIGNATURE.ORDER_ID.eq(orderId))
            .and(ELECTRONIC_SIGNATURE.STATUS.notIn(TERMINAL_STATUSES))
            .fetchOptionalInto(ElectronicSignatureRecord.class);
        return record.map(mapper::fromRecord);
    }

    @Deprecated
    public ElectronicSignature updateSignatureStatus(
        ElectronicSignatureProvider provider,
        String documentId,
        ElectronicSignatureStatus status,
        String initiatedBy,
        Configuration configuration
    ) {
        DSLContext dslContext = DSL.using(configuration);

        validateCurrentStatus(provider, documentId);

        ElectronicSignatureRecord updated = dslContext
            .update(ELECTRONIC_SIGNATURE)
            .set(ELECTRONIC_SIGNATURE.STATUS, status.toString())
            .where(ELECTRONIC_SIGNATURE.DOCUMENT_PROVIDER.eq(provider.toString()))
            .and(ELECTRONIC_SIGNATURE.EXTERNAL_DOCUMENT_ID.eq(documentId))
            .and(ELECTRONIC_SIGNATURE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .returning()
            .fetchOne();

        Optional<ElectronicSignatureAuditLogStatus> auditLogStatus = convertToAuditLogStatus(status);
        if (updated != null && auditLogStatus.isPresent()) {
            ImmutableElectronicSignatureAuditLog esignAuditLog = ImmutableElectronicSignatureAuditLog.builder()
                .status(auditLogStatus.get())
                .electronicSignatureId(updated.getId())
                .initiatedBy(initiatedBy)
                .build();
            insertSignatureAuditLog(configuration, esignAuditLog);
        }

        return mapper.fromRecord(updated);
    }

    public void setSignatureInitiatedOn(UUID id, Instant initiatedOn) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        int count = dslContext
            .update(ELECTRONIC_SIGNATURE)
            .set(ELECTRONIC_SIGNATURE.INITIATED_ON, DateTimeConverter.instantToLocalDateTime(initiatedOn))
            .where(ELECTRONIC_SIGNATURE.ID.eq(id))
            .and(ELECTRONIC_SIGNATURE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(ELECTRONIC_SIGNATURE.STATUS.notIn(DELETED_STATUSES))
            .and(ELECTRONIC_SIGNATURE.INITIATED_ON.isNull())
            .execute();
        if (count != 1) {
            LOGGER.warn("Unable to set initiated_on for electronic signature with id: {}", id);
        }
    }

    public ElectronicSignature setSignatureCompletedOn(UUID id, Instant completedOn) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        ElectronicSignatureRecord record = dslContext
            .update(ELECTRONIC_SIGNATURE)
            .set(ELECTRONIC_SIGNATURE.COMPLETED_ON, DateTimeConverter.instantToLocalDateTime(completedOn))
            .where(ELECTRONIC_SIGNATURE.ID.eq(id))
            .and(ELECTRONIC_SIGNATURE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(ELECTRONIC_SIGNATURE.STATUS.notIn(DELETED_STATUSES))
            .and(ELECTRONIC_SIGNATURE.COMPLETED_ON.isNull())
            .returning()
            .fetchOne();
        return mapper.fromRecord(record);
    }

    // TODO: need to ensure proper state transitions
    public ElectronicSignature updateSignatureStatus(ElectronicSignatureProvider provider, String documentId, ElectronicSignatureStatus status) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        validateCurrentStatus(provider, documentId);

        ElectronicSignatureRecord updated = dslContext
            .update(ELECTRONIC_SIGNATURE)
            .set(ELECTRONIC_SIGNATURE.STATUS, status.toString())
            .where(ELECTRONIC_SIGNATURE.DOCUMENT_PROVIDER.eq(provider.toString()))
            .and(ELECTRONIC_SIGNATURE.EXTERNAL_DOCUMENT_ID.eq(documentId))
            .and(ELECTRONIC_SIGNATURE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .returning()
            .fetchOne();

        return mapper.fromRecord(updated);
    }

    private Optional<ElectronicSignatureAuditLogStatus> convertToAuditLogStatus(ElectronicSignatureStatus status) {
        return switch (status) {
            case SENT -> Optional.of(ElectronicSignatureAuditLogStatus.SENT);
            case VIEWED -> Optional.of(ElectronicSignatureAuditLogStatus.VIEWED);
            case PARTIALLY_SIGNED, COMPLETED -> Optional.of(ElectronicSignatureAuditLogStatus.SIGNED);
            default -> Optional.empty();
        };
    }

    private void validateCurrentStatus(ElectronicSignatureProvider provider, String documentId) {
        ElectronicSignature signature = getSignatureByDocumentId(provider, documentId).orElseThrow(() ->
            new IllegalStateException(
                String.format("Could not find electronic signature with document provider %s and document id %s", provider, documentId)
            )
        );
        if (signature.getStatus().isTerminal()) {
            throw new IllegalStateException(
                String.format(
                    "Electronic signature with document provider %s and document id %s has already been completed, declined, or voided",
                    provider,
                    documentId
                )
            );
        }
    }

    @Deprecated
    private ImmutableElectronicSignatureAuditLog insertSignatureAuditLog(
        Configuration configuration,
        ImmutableElectronicSignatureAuditLog signatureAuditLog
    ) {
        DSLContext dslContext = DSL.using(configuration);
        ElectronicSignatureAuditLogRecord record = mapper.toRecord(signatureAuditLog);
        record.reset(ELECTRONIC_SIGNATURE_AUDIT_LOG.ID);
        record.setTenantId(tenantIdProvider.provideTenantIdString());
        ElectronicSignatureAuditLogRecord insertedRecord = dslContext
            .insertInto(ELECTRONIC_SIGNATURE_AUDIT_LOG)
            .set(record)
            .returning()
            .fetchOneInto(ElectronicSignatureAuditLogRecord.class);
        return mapper.fromRecord(insertedRecord);
    }

    public ElectronicSignatureAuditLog insertSignatureAuditLog(ElectronicSignatureAuditLog signatureAuditLog, ElectronicSignature signature) {
        DSLContext transactionContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        ElectronicSignatureAuditLogRecord record = mapper.toRecord(signatureAuditLog);
        record.reset(ELECTRONIC_SIGNATURE_AUDIT_LOG.ID);
        record.setTenantId(tenantIdProvider.provideTenantIdString());
        return transactionContext.transactionResult(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            ElectronicSignatureAuditLogRecord insertedRecord = dslContext
                .insertInto(ELECTRONIC_SIGNATURE_AUDIT_LOG)
                .set(record)
                .returning()
                .fetchOneInto(ElectronicSignatureAuditLogRecord.class);

            // Update signature status
            Optional<ElectronicSignatureStatus> newStatus = convertToSignatureStatus(signatureAuditLog.getStatus(), signature.getStatus());
            if (insertedRecord != null && newStatus.isPresent()) {
                dslContext
                    .update(ELECTRONIC_SIGNATURE)
                    .set(ELECTRONIC_SIGNATURE.STATUS, newStatus.get().toString())
                    .where(ELECTRONIC_SIGNATURE.ID.eq(signatureAuditLog.getElectronicSignatureId()))
                    .execute();
            }

            return mapper.fromRecord(insertedRecord);
        });
    }

    public void markSignatureAsCompleted(ElectronicSignatureProvider provider, String documentId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext
            .update(ELECTRONIC_SIGNATURE)
            .set(ELECTRONIC_SIGNATURE.STATUS, ElectronicSignatureStatus.COMPLETED.toString())
            .where(ELECTRONIC_SIGNATURE.DOCUMENT_PROVIDER.eq(provider.toString()))
            .and(ELECTRONIC_SIGNATURE.EXTERNAL_DOCUMENT_ID.eq(documentId))
            .and(ELECTRONIC_SIGNATURE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .execute();
    }

    public void markSignatureAsVoided(ElectronicSignatureProvider provider, String documentId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext
            .update(ELECTRONIC_SIGNATURE)
            .set(ELECTRONIC_SIGNATURE.STATUS, ElectronicSignatureStatus.VOIDED.toString())
            .where(ELECTRONIC_SIGNATURE.DOCUMENT_PROVIDER.eq(provider.toString()))
            .and(ELECTRONIC_SIGNATURE.EXTERNAL_DOCUMENT_ID.eq(documentId))
            .and(ELECTRONIC_SIGNATURE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .execute();
    }

    private Optional<ElectronicSignatureStatus> convertToSignatureStatus(
        ElectronicSignatureAuditLogStatus auditLogStatus,
        ElectronicSignatureStatus currentSignatureStatus
    ) {
        ElectronicSignatureStatus newSignatureStatus =
            switch (auditLogStatus) {
                case SENT -> ElectronicSignatureStatus.SENT;
                case SIGNED -> ElectronicSignatureStatus.PARTIALLY_SIGNED;
                case DECLINED -> ElectronicSignatureStatus.DECLINED;
                default -> null;
            };
        if (
            newSignatureStatus == null ||
            !ElectronicSignatureStatus.VALID_STATE_TRANSITION_MAP.getOrDefault(currentSignatureStatus, Set.of()).contains(newSignatureStatus)
        ) {
            return Optional.empty();
        }

        return Optional.of(newSignatureStatus);
    }

    public List<ImmutableElectronicSignatureAuditLog> getSignatureAuditLogs(String electronicSignatureId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        List<ElectronicSignatureAuditLogRecord> records = dslContext
            .select()
            .from(ELECTRONIC_SIGNATURE_AUDIT_LOG)
            .where(ELECTRONIC_SIGNATURE_AUDIT_LOG.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(ELECTRONIC_SIGNATURE_AUDIT_LOG.ELECTRONIC_SIGNATURE_ID.eq(UUID.fromString(electronicSignatureId)))
            .orderBy(ELECTRONIC_SIGNATURE_AUDIT_LOG.CREATED_ON.desc())
            .fetchInto(ElectronicSignatureAuditLogRecord.class);
        return mapper.fromAuditLogRecords(records);
    }

    public void upsertEsignDetails(EsignDetails esignDetails) {
        Validator.validateNonNullArgument(esignDetails.getOrderId());
        String tenantId = tenantIdProvider.provideTenantIdString();
        ElectronicSignatureRequestRecord record = mapper.toRecord(esignDetails);
        record.reset(ELECTRONIC_SIGNATURE_REQUEST.ID);
        record.setTenantId(tenantId);

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        Optional<EsignDetails> optionalEsignDetails = getOptionalEsignDetails(esignDetails.getOrderId());
        if (optionalEsignDetails.isEmpty()) {
            dslContext.insertInto(ELECTRONIC_SIGNATURE_REQUEST).set(record).execute();
        } else {
            dslContext
                .update(ELECTRONIC_SIGNATURE_REQUEST)
                .set(record)
                .where(ELECTRONIC_SIGNATURE_REQUEST.TENANT_ID.eq(tenantId).and(ELECTRONIC_SIGNATURE_REQUEST.ORDER_ID.eq(esignDetails.getOrderId())))
                .execute();
        }
    }

    public EsignDetails getEsignDetails(String orderId) {
        Optional<EsignDetails> optionalEsignDetails = getOptionalEsignDetails(orderId);
        return optionalEsignDetails.orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ESIGNATURE_DETAIL, orderId));
    }

    private Optional<EsignDetails> getOptionalEsignDetails(String orderId) {
        Validator.validateNonNullArgument(orderId);
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        ElectronicSignatureRequestRecord record = dslContext
            .selectFrom(ELECTRONIC_SIGNATURE_REQUEST)
            .where(ELECTRONIC_SIGNATURE_REQUEST.TENANT_ID.eq(tenantId))
            .and(ELECTRONIC_SIGNATURE_REQUEST.ORDER_ID.eq(orderId))
            .fetchOne();

        if (record == null) {
            return Optional.empty();
        }

        EsignDetails result = mapper.fromRecord(record);
        return Optional.of(result);
    }

    public void updateSignatureLink(ElectronicSignature electronicSignature, String link) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        int count = dslContext
            .update(ELECTRONIC_SIGNATURE)
            .set(ELECTRONIC_SIGNATURE.LINK, link)
            .where(ELECTRONIC_SIGNATURE.DOCUMENT_PROVIDER.eq(electronicSignature.getDocumentProvider().name()))
            .and(ELECTRONIC_SIGNATURE.EXTERNAL_DOCUMENT_ID.eq(electronicSignature.getExternalDocumentId()))
            .and(ELECTRONIC_SIGNATURE.ORDER_ID.eq(electronicSignature.getOrderId()))
            .and(ELECTRONIC_SIGNATURE.TENANT_ID.eq(tenantId))
            .execute();

        if (count != 1) {
            LOGGER.warn("Unable to update the link: {} for eSign: {}", link, electronicSignature);
        }
    }
}
