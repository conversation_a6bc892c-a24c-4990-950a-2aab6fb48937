package com.subskribe.billy.user.db;

import static com.subskribe.billy.jooq.default_schema.Tables.TENANT;
import static com.subskribe.billy.jooq.default_schema.Tables.TENANT_USER;
import static com.subskribe.billy.jooq.default_schema.Tables.USER_AUTH_INFO_VIEW;
import static org.jooq.impl.DSL.row;

import com.subskribe.billy.auth.model.AuthMethod;
import com.subskribe.billy.auth.model.UserAuthInfo;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.exception.DuplicateObjectException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.jooq.default_schema.tables.records.TenantUserRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.UserAuthInfoViewRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.Constants;
import com.subskribe.billy.shared.StringConverter;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.Status;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.mapper.UserRecordMapper;
import com.subskribe.billy.user.mapper.UserAuthMapper;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.validation.Validator;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class UserDAO {

    private static final String UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME = "index_tenant_user_external_id";
    private static final Logger LOGGER = LoggerFactory.getLogger(UserDAO.class);

    private static final String[] EMPTY_ARRAY = new String[] {};

    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final UserRecordMapper userRecordMapper;

    @Inject
    public UserDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        userRecordMapper = Mappers.getMapper(UserRecordMapper.class);
    }

    public User addUser(User user, Configuration configuration) {
        return addUserForTenant(user, tenantIdProvider.provideTenantIdString(), configuration);
    }

    public void updateUserSsoOnly(String userId, boolean isSsoOnly) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = dslContextProvider.get(tenantId);

        tenantDSLContext
            .update(TENANT_USER)
            .set(TENANT_USER.SSO_ONLY, isSsoOnly)
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.USER_ID.eq(userId))
            .execute();
    }

    public User addUserForTenant(User user, String tenantId, Configuration configuration) {
        DSLContext dslContext = DSL.using(configuration);
        TenantUserRecord userRecord = userRecordMapper.toUserRecord(user);
        userRecord.reset(TENANT_USER.ID);
        userRecord.setTenantId(tenantId);

        TenantUserRecord savedUser = PostgresErrorHandler.withConstraintAsConflict(
            () -> dslContext.insertInto(TENANT_USER).set(userRecord).returning().fetchOne(),
            UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME,
            String.format("user exists with external id: %s", user.getExternalId())
        );

        if (savedUser == null) {
            throw new IllegalStateException("Failed to add user.");
        }

        return userRecordMapper.fromUserRecord(savedUser);
    }

    public void updateUser(User user) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = dslContextProvider.get(tenantId);

        PostgresErrorHandler.withConstraintAsConflict(
            () ->
                tenantDSLContext
                    .update(TENANT_USER)
                    .set(
                        row(
                            TENANT_USER.DISPLAY_NAME,
                            TENANT_USER.TITLE,
                            TENANT_USER.PHONE_NUMBER,
                            TENANT_USER.ROLE,
                            TENANT_USER.STATE,
                            TENANT_USER.SSO_ONLY,
                            TENANT_USER.ENTITY_IDS,
                            TENANT_USER.UPDATED_ON
                        ),
                        row(
                            user.getDisplayName(),
                            user.getTitle(),
                            user.getPhoneNumber(),
                            user.getRole().name(),
                            user.getState().name(),
                            user.isSsoOnly(),
                            user.getEntityIds().toArray(new String[0]),
                            LocalDateTime.now(ZoneOffset.UTC)
                        )
                    )
                    .where(TENANT_USER.TENANT_ID.eq(tenantId))
                    .and(TENANT_USER.USER_ID.eq(user.getUserId()))
                    .execute(),
            UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME,
            String.format("user exists with external id: %s", user.getExternalId())
        );
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public Optional<User> getUser(String userId, Optional<String> optionalTenantId) {
        String tenantId = optionalTenantId.orElseGet(tenantIdProvider::provideTenantIdString);
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        TenantUserRecord record = tenantDSLContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.USER_ID.eq(userId))
            .and(TENANT_USER.TENANT_ID.eq(tenantId))
            .fetchOneInto(TenantUserRecord.class);
        return Optional.ofNullable(record).map(userRecordMapper::fromUserRecord);
    }

    public Optional<User> getUserByExternalId(String externalId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        TenantUserRecord record = tenantDSLContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.EXTERNAL_ID.eq(externalId))
            .and(TENANT_USER.TENANT_ID.eq(tenantId))
            .fetchOneInto(TenantUserRecord.class);
        return Optional.ofNullable(record).map(userRecordMapper::fromUserRecord);
    }

    public List<User> getAllActiveUsersForTenant(String tenantId) {
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<TenantUserRecord> records = tenantDSLContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.STATE.eq(Status.ACTIVE.name()))
            .fetchInto(TenantUserRecord.class);
        return userRecordMapper.fromUserRecords(records);
    }

    public Set<String> getAllNormalizedUserEmailAddresses() {
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        List<String> records = tenantDSLContext
            .select(TENANT_USER.NORMALIZED_EMAIL)
            .from(TENANT_USER)
            .where(TENANT_USER.STATE.eq(Status.ACTIVE.name()))
            .fetchInto(String.class);
        return new HashSet<>(records);
    }

    public List<User> getUsers(List<String> userIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<TenantUserRecord> records = tenantDSLContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.USER_ID.in(userIds))
            .fetchInto(TenantUserRecord.class);
        return userRecordMapper.fromUserRecords(records);
    }

    public List<User> getUsersByExternalIds(List<String> externalIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<TenantUserRecord> records = tenantDSLContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.EXTERNAL_ID.in(externalIds))
            .fetchInto(TenantUserRecord.class);
        return userRecordMapper.fromUserRecords(records);
    }

    public List<User> getUsersByEmails(List<String> emails) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<String> normalizedEmails = emails.stream().map(StringConverter::toNormalizedEmail).collect(Collectors.toList());

        List<TenantUserRecord> records = tenantDSLContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.NORMALIZED_EMAIL.in(normalizedEmails))
            .and(TENANT_USER.STATE.eq(Status.ACTIVE.name()))
            .fetchInto(TenantUserRecord.class);
        return userRecordMapper.fromUserRecords(records);
    }

    public Optional<User> getUserByEmail(String email) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = dslContextProvider.get(tenantId);
        TenantUserRecord record = tenantDSLContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.NORMALIZED_EMAIL.eq(email.toLowerCase()))
            .and(TENANT_USER.STATE.eq(Status.ACTIVE.name()))
            .fetchOneInto(TenantUserRecord.class);
        return Optional.ofNullable(record).map(userRecordMapper::fromUserRecord);
    }

    @AllowNonRlsDataAccess
    public Optional<User> findUserByEmailFromAllTenants(String email) {
        DSLContext tenantDSLContext = dslContextProvider.get();
        TenantUserRecord record = tenantDSLContext
            .select()
            .from(TENANT_USER)
            .join(TENANT)
            .on(TENANT_USER.TENANT_ID.eq(TENANT.TENANT_ID))
            .where(TENANT_USER.EMAIL.equalIgnoreCase(email))
            .and(TENANT_USER.STATE.eq(Status.ACTIVE.name()))
            .and(TENANT.IS_DELETED.eq(false))
            .fetchOneInto(TenantUserRecord.class);
        return Optional.ofNullable(record).map(userRecordMapper::fromUserRecord);
    }

    @AllowNonRlsDataAccess
    public void throwIfUserWithEmailExists(String emailAddress) {
        Optional<UserAuthInfo> existingUser = getAuthInfoByEmail(dslContextProvider.get(), emailAddress);
        if (existingUser.isPresent()) {
            throw new DuplicateObjectException("User with email already exists: " + emailAddress);
        }
    }

    public static Optional<UserAuthInfo> getAuthInfoByEmail(DSLContext dslContext, String email) {
        var record = dslContext
            .select()
            .from(USER_AUTH_INFO_VIEW)
            .where(USER_AUTH_INFO_VIEW.EMAIL.equalIgnoreCase(email))
            .fetchOneInto(UserAuthInfoViewRecord.class);
        if (record == null) {
            return Optional.empty();
        }

        UserAuthMapper userAuthMapper = Mappers.getMapper(UserAuthMapper.class);
        UserAuthInfo userAuthInfo = userAuthMapper.fromRecord(record);
        userAuthInfo.setAuthMethod(getAuthMethod(record));
        return Optional.of(userAuthInfo);
    }

    private static AuthMethod getAuthMethod(UserAuthInfoViewRecord record) {
        if (!record.getHasSso()) {
            return AuthMethod.PASSWORD_ONLY;
        }

        if (record.getSsoOnly()) {
            return AuthMethod.SSO_ONLY;
        }

        return AuthMethod.PASSWORD_AND_SSO;
    }

    public List<User> getUsers(PaginationQueryParams paginationQueryParams) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = dslContextProvider.get(tenantId);
        var query = dslContext.select().from(TENANT_USER).where(TENANT_USER.TENANT_ID.eq(tenantId)).and(TENANT_USER.STATE.eq(Status.ACTIVE.name()));

        var records = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            TENANT_USER,
            TENANT_USER.ID,
            TENANT_USER.TENANT_ID,
            TENANT_USER.CREATED_ON,
            TenantUserRecord.class
        );

        return userRecordMapper.fromUserRecords(records);
    }

    public void ensureUniqueUserId(String userId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = dslContext.select().from(TENANT_USER).where(TENANT_USER.USER_ID.eq(userId)).fetchOneInto(TenantUserRecord.class);

        if (record == null) {
            return;
        }

        var message = "Duplicate userId generation attempt. UserId = " + userId;
        LOGGER.info(message);
        throw new DuplicateIdException(message);
    }

    public void acceptTermsForGivenUserEmail(String email) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = dslContextProvider.get(tenantId);
        var currentTimestamp = LocalDateTime.now(ZoneOffset.UTC);

        TenantUserRecord tenantUserRecord = dslContext
            .update(TENANT_USER)
            .set(TENANT_USER.TERMS_ACCEPTED_ON, currentTimestamp)
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.EMAIL.equalIgnoreCase(email))
            .returning()
            .fetchOne();

        if (tenantUserRecord == null) {
            throw new ObjectNotFoundException(BillyObjectType.USER, email);
        }
    }

    public User disableUser(String userId, Configuration configuration) {
        return changeUserStatus(userId, Status.DISABLED, configuration);
    }

    public User enableUser(String userId, Configuration configuration) {
        return changeUserStatus(userId, Status.ACTIVE, configuration);
    }

    public static int getUserDisplayNameMaxLength() {
        return TENANT_USER.DISPLAY_NAME.getDataType().length();
    }

    public static int getUserTitleMaxLength() {
        return TENANT_USER.TITLE.getDataType().length();
    }

    public static int getUserEmailMaxLength() {
        return TENANT_USER.EMAIL.getDataType().length();
    }

    public static int getUserPhoneNumberMaxLength() {
        return TENANT_USER.PHONE_NUMBER.getDataType().length();
    }

    private User changeUserStatus(String userId, Status newStatus, Configuration configuration) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = DSL.using(configuration);
        TenantUserRecord newRecord = dslContext
            .update(TENANT_USER)
            .set(TENANT_USER.STATE, newStatus.name())
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.USER_ID.eq(userId))
            .returning()
            .fetchOne();

        return userRecordMapper.fromUserRecord(newRecord);
    }

    public static Optional<User> getUserById(DSLContextProvider dslContextProvider, String tenantId, String userId) {
        Validator.validateNonNullArguments(tenantId, userId);
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        Record record = dslContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.USER_ID.eq(userId))
            .and(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.STATE.eq(Status.ACTIVE.name()))
            .fetchOne();
        if (record == null) {
            return Optional.empty();
        }
        return Optional.of(record.into(User.class));
    }

    public void setUserRoleToBillyEngineer(String email, String tenantId) {
        Optional<User> user = findUserByEmailFromAllTenants(email);
        if (user.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.USER, email);
        }
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        dslContext
            .update(TENANT_USER)
            .set(TENANT_USER.ROLE, Role.BILLY_ENGINEER.name())
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.EMAIL.eq(email))
            .and(TENANT_USER.USER_ID.eq(user.get().getUserId()))
            .execute();
    }

    public List<User> getUsersWithBillyAdminRole() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<TenantUserRecord> records = dslContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.ROLE.equalIgnoreCase(Role.BILLY_ADMIN.name()))
            .and(TENANT_USER.STATE.eq(Status.ACTIVE.name()))
            .fetchInto(TenantUserRecord.class);
        if (CollectionUtils.isEmpty(records)) {
            return List.of();
        }
        return userRecordMapper.fromUserRecords(records);
    }

    @AllowNonRlsDataAccess
    public List<User> getAssociatedUsers(String emailWithoutDomain) {
        DSLContext dslContext = dslContextProvider.get();
        List<TenantUserRecord> records = dslContext
            .select()
            .from(TENANT_USER)
            .where(
                TENANT_USER.EMAIL.likeIgnoreCase(emailWithoutDomain + "+%" + Constants.INTERNAL_EMAIL_DOMAIN_POSTFIX).or(
                    TENANT_USER.EMAIL.equalIgnoreCase(emailWithoutDomain + Constants.INTERNAL_EMAIL_DOMAIN_POSTFIX)
                )
            )
            .and(TENANT_USER.STATE.eq(Status.ACTIVE.name()))
            .fetchInto(TenantUserRecord.class);
        return userRecordMapper.fromUserRecords(records);
    }

    public void acceptTermsForGivenUserEmailInTenant(String email, String tenantId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        dslContext
            .update(TENANT_USER)
            .set(TENANT_USER.TERMS_ACCEPTED_ON, LocalDateTime.now(ZoneOffset.UTC))
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.EMAIL.equalIgnoreCase(email))
            .returning()
            .fetchOne();
    }

    public Optional<User> getUserOfAnyStatusByEmail(String email) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = dslContextProvider.get(tenantId);
        TenantUserRecord record = tenantDSLContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.EMAIL.equalIgnoreCase(email))
            .fetchOneInto(TenantUserRecord.class);
        return Optional.ofNullable(record).map(userRecordMapper::fromUserRecord);
    }

    public List<User> getUsersOfAnyStatusByEmails(List<String> normalizedEmails) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = dslContextProvider.get(tenantId);

        List<TenantUserRecord> records = tenantDSLContext
            .select()
            .from(TENANT_USER)
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.NORMALIZED_EMAIL.in(normalizedEmails))
            .fetchInto(TenantUserRecord.class);
        return userRecordMapper.fromUserRecords(records);
    }

    public void backfillUserEntityIds(String tenantId) {
        DSLContext tenantDSLContext = dslContextProvider.get(tenantId);
        tenantDSLContext
            .update(TENANT_USER)
            .set(TENANT_USER.ENTITY_IDS, new String[] { "*" })
            .where(TENANT_USER.TENANT_ID.eq(tenantId))
            .and(TENANT_USER.ENTITY_IDS.isNull().or(TENANT_USER.ENTITY_IDS.eq(EMPTY_ARRAY)))
            .execute();
    }
}
