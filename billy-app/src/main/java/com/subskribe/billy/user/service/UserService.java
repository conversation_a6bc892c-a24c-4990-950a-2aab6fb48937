package com.subskribe.billy.user.service;

import static com.subskribe.billy.user.UserProductMetadata.USER_CORE;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.auth.model.UserAuthInfo;
import com.subskribe.billy.auth.model.UserPrincipal;
import com.subskribe.billy.auth.services.TenantCognitoService;
import com.subskribe.billy.aws.cognito.CognitoService;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.cache.key.TenantUserCacheKey;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.TenantPartitionKey;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.TenantNotFoundException;
import com.subskribe.billy.looker.LookerUserStatusSwitcher;
import com.subskribe.billy.metrics.datadog.AllMetrics;
import com.subskribe.billy.ratelimiter.ip.IpBasedRequestRateLimiter;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.resources.json.tenant.UserMapper;
import com.subskribe.billy.resources.json.tenant.UserSsoUpdate;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.security.OnlyAllowAdminCallers;
import com.subskribe.billy.shared.Constants;
import com.subskribe.billy.shared.DataValidation;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.Status;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.Tenant;
import com.subskribe.billy.user.db.UserDAO;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.ws.rs.ForbiddenException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.mapstruct.factory.Mappers;
import software.amazon.awssdk.services.cognitoidentityprovider.model.UserStatusType;

@AllMetrics
public class UserService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserService.class);
    private static final int MAX_DISPLAY_NAME_LENGTH = UserDAO.getUserDisplayNameMaxLength();
    private static final int MAX_TITLE_LENGTH = UserDAO.getUserTitleMaxLength();
    private static final int MAX_EMAIL_LENGTH = UserDAO.getUserEmailMaxLength();
    private static final int MAX_PHONE_NUMBER_LENGTH = UserDAO.getUserPhoneNumberMaxLength();

    private final UserDAO userDAO;

    private final CognitoService cognitoService;

    private final BillyConfiguration billyConfiguration;

    private final UserIdGenerator userIdGenerator;

    private final TenantIdProvider tenantIdProvider;

    private final TenantCognitoService tenantCognitoService;

    private final IpBasedRequestRateLimiter ipBasedRequestRateLimiter;

    private final UserGroupAndSegmentRetrievalService userGroupAndSegmentRetrievalService;

    private final FeatureService featureService;

    private final CurrentUserProvider currentUserProvider;

    private final LookerUserStatusSwitcher lookerUserStatusSwitcher;

    private final CacheService cacheService;

    private final EntityContextResolver entityContextResolver;

    private final EntityContextProvider entityContextProvider;

    private final DSLContextProvider dslContextProvider;

    private final UserMapper userMapper;

    private final EventPublishingService eventPublishingService;

    private final UncheckedObjectMapper objectMapper;

    @Inject
    public UserService(
        UserDAO userDAO,
        CognitoService cognitoService,
        BillyConfiguration billyConfiguration,
        UserIdGenerator userIdGenerator,
        TenantIdProvider tenantIdProvider,
        TenantCognitoService tenantCognitoService,
        IpBasedRequestRateLimiter ipBasedRequestRateLimiter,
        UserGroupAndSegmentRetrievalService userGroupAndSegmentRetrievalService,
        FeatureService featureService,
        CurrentUserProvider currentUserProvider,
        LookerUserStatusSwitcher lookerUserStatusSwitcher,
        CacheService cacheService,
        EntityContextResolver entityContextResolver,
        EntityContextProvider entityContextProvider,
        DSLContextProvider dslContextProvider,
        EventPublishingService eventPublishingService
    ) {
        this.userDAO = userDAO;
        this.cognitoService = cognitoService;
        this.billyConfiguration = billyConfiguration;
        this.userIdGenerator = userIdGenerator;
        this.tenantIdProvider = tenantIdProvider;
        this.tenantCognitoService = tenantCognitoService;
        this.ipBasedRequestRateLimiter = ipBasedRequestRateLimiter;
        this.userGroupAndSegmentRetrievalService = userGroupAndSegmentRetrievalService;
        this.featureService = featureService;
        this.currentUserProvider = currentUserProvider;
        this.lookerUserStatusSwitcher = lookerUserStatusSwitcher;
        this.cacheService = cacheService;
        this.entityContextResolver = entityContextResolver;
        this.entityContextProvider = entityContextProvider;
        this.dslContextProvider = dslContextProvider;
        this.eventPublishingService = eventPublishingService;
        this.userMapper = Mappers.getMapper(UserMapper.class);
        objectMapper = UncheckedObjectMapper.defaultMapper();
    }

    @AllowNonRlsDataAccess
    public Optional<UserAuthInfo> getAuthInfoByEmail(String emailAddress) {
        if (StringUtils.isBlank(emailAddress)) {
            return Optional.empty();
        }

        ipBasedRequestRateLimiter.checkRequest();

        Optional<UserAuthInfo> result = UserDAO.getAuthInfoByEmail(dslContextProvider.get(), emailAddress.trim());
        if (result.isEmpty()) {
            LOGGER.info("Attempted login: Did not find the email: {}", emailAddress.trim());
            ipBasedRequestRateLimiter.markBadRequest();
        }

        result.ifPresent(authInfo -> authInfo.setIsEmailLinkLoginEnabled(isLoginWithEmailLinkEnabled(emailAddress)));

        return result;
    }

    public List<User> getAllActiveUsersForTenant(String tenantId) {
        return userDAO.getAllActiveUsersForTenant(tenantId);
    }

    public List<User> getUsers(PaginationQueryParams paginationQueryParams) {
        var users = userDAO.getUsers(paginationQueryParams);
        userGroupAndSegmentRetrievalService.setUserGroupsAndApprovalSegmentsForUsers(users);
        return users;
    }

    public List<User> getUsersByEmails(List<String> emailIds) {
        Validator.validateCollectionNotEmpty(emailIds, "emailIds");
        var users = userDAO.getUsersByEmails(emailIds);
        userGroupAndSegmentRetrievalService.setUserGroupsAndApprovalSegmentsForUsers(users);
        return users;
    }

    public User getUser(String userId) {
        return getUser(userId, Optional.empty());
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public User getUser(String userId, Optional<String> optionalTenantId) {
        Objects.requireNonNull(userId, "userId not provided");
        var user = userDAO.getUser(userId, optionalTenantId);

        if (user.isEmpty()) {
            LOGGER.info("User with id {} not found", userId);
            throw new ObjectNotFoundException(BillyObjectType.USER, userId);
        }

        var receivedUser = user.get();
        userGroupAndSegmentRetrievalService.setUserGroupsAndApprovalSegmentsForUsers(List.of(receivedUser));
        return receivedUser;
    }

    public Optional<User> getUserByExternalId(String externalId) {
        Objects.requireNonNull(externalId, "externalId not provided");
        Optional<User> user = userDAO.getUserByExternalId(externalId);

        if (user.isEmpty()) {
            LOGGER.info("User with id {} not found", externalId);
            return Optional.empty();
        }

        User receivedUser = user.get();
        userGroupAndSegmentRetrievalService.setUserGroupsAndApprovalSegmentsForUsers(List.of(receivedUser));
        return Optional.of(receivedUser);
    }

    public User getUserIncludeCognitoStatus(String userId) {
        User user = getUser(userId);
        setUserStatusType(user);
        userGroupAndSegmentRetrievalService.setUserGroupsAndApprovalSegmentsForUsers(List.of(user));
        return user;
    }

    public Optional<User> getUserByEmail(String email) {
        validateEmail(email);
        return userDAO.getUserByEmail(email.trim());
    }

    // Fetches user irrespective of the status - ACTIVE/DISABLED/EXPIRED
    public Optional<User> getUserOfAnyStatusByEmail(String email) {
        validateEmail(email);
        return userDAO.getUserOfAnyStatusByEmail(email.trim());
    }

    public List<User> getUsersOfAnyStatusByEmails(List<String> emailIds) {
        Validator.validateCollectionNotEmpty(emailIds, "emailIds");
        return userDAO.getUsersOfAnyStatusByEmails(emailIds);
    }

    public Optional<User> findUserByEmailFromAllTenants(String email) {
        validateEmail(email);
        return userDAO.findUserByEmailFromAllTenants(email.trim());
    }

    public Optional<User> getCurrentUser() {
        try {
            Optional<UserPrincipal> userPrincipal = currentUserProvider.provideOptional();
            if (userPrincipal.isEmpty() || userPrincipal.get().getEmailAddress().isEmpty()) {
                return Optional.empty();
            }
            return getUserByEmail(userPrincipal.get().getEmailAddress().get());
        } catch (Exception ex) {
            // when in job context the execution is outside of request scope.
            LOGGER.warn(USER_CORE, "Failed to fetch current user", ex);
            return Optional.empty();
        }
    }

    public Optional<User> getUserOptional(String userId) {
        if (userId == null) {
            return Optional.empty();
        }
        return userDAO.getUser(userId, Optional.empty());
    }

    public String createUser(User user) {
        populateUserFields(user, tenantIdProvider.provideTenantIdString());
        validateUserToBeCreated(user, false);

        Optional<User> optionalUser = userDAO.getUserOfAnyStatusByEmail(user.getEmail());

        if (optionalUser.isPresent()) {
            throw new InvalidInputException("User with email " + user.getEmail() + " already added.");
        }

        if (billyConfiguration.getCognitoConfiguration().getShouldInviteNewUsers()) {
            boolean isSsoOnly = isUserSsoOnly(user);
            cognitoService.createCognitoUser(user.getTenantId(), user.getEmail(), user.getDisplayName(), isSsoOnly);
        }

        return TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider).transactionResult(configuration -> {
            User addedUser = userDAO.addUser(user, configuration);
            sendUserEvent(addedUser, EventType.USER_CREATED, configuration);
            return addedUser.getUserId();
        });
    }

    // Note: This method should be used only for testing purposes or billy admin operations
    @AllowNonRlsDataAccess
    @OnlyAllowAdminCallers
    public User createUserForTenant(User user, String tenantId) {
        populateUserFields(user, tenantId);
        user.setEntityIds(Set.of(EntityContext.ALL_ACCESS_ID));
        validateUserToBeCreated(user, true);

        if (billyConfiguration.getCognitoConfiguration().getShouldInviteNewUsers()) {
            boolean isSsoOnly = isUserSsoOnly(user);
            cognitoService.createCognitoUser(user.getTenantId(), user.getEmail(), user.getDisplayName(), isSsoOnly);
        }

        String userId = dslContextProvider
            .get()
            .transactionResult(configuration -> {
                User addedUser = userDAO.addUserForTenant(user, tenantId, configuration);
                sendUserEvent(addedUser, EventType.USER_CREATED, configuration);
                return addedUser.getUserId();
            });
        return getUser(userId, Optional.of(tenantId));
    }

    private void populateUserFields(User user, String tenantId) {
        if (StringUtils.isBlank(user.getEmail())) {
            throw new InvalidInputException("Email is required to create a user");
        }
        user.setEmail(user.getEmail().trim());
        user.setNormalizedEmail(user.getEmail().toLowerCase());
        if (StringUtils.isBlank(user.getUserId()) || !billyConfiguration.isLocalOrCi()) {
            user.setUserId(userIdGenerator.generate());
        }
        user.setTenantId(tenantId);
    }

    // TODO: multi-entity - replace skipEntityValidation by injecting EntityContext
    private void validateUserToBeCreated(User user, boolean skipEntityValidation) {
        validateUserFields(user);
        validateEmail(user.getEmail());
        userDAO.throwIfUserWithEmailExists(user.getEmail().trim());
        ensureRoleCanBeAssigned(user.getRole());
        if (!skipEntityValidation) {
            Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds());
            user.setEntityIds(resolvedEntityIds);
        }
    }

    public void updateUser(User user) {
        String userId = user.getUserId();
        Objects.requireNonNull(userId, "userId not provided");
        User existingUser = ensureUserWithIdExists(userId, Status.ACTIVE);
        ensureOnlyBillyAdminCanModifyAnotherBillyAdminUser(existingUser);
        ensureRoleCanBeAssigned(user.getRole());
        ensureCurrentUserHasSufficientEntityAccessToModify(existingUser);
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            user.getEntityIds(),
            existingUser.getEntityIds()
        );
        user.setEntityIds(resolvedEntityIds);
        userDAO.updateUser(user);
        String tenantId = tenantIdProvider.provideTenantIdString();
        String emailAddress = user.getEmail().trim();
        if (isUserSsoOnly(existingUser) && !user.isSsoOnly()) {
            cognitoService.resendCognitoInvitationEmailForExistingUser(tenantId, emailAddress, true);
        }
        invalidateUserCache(tenantId, userId, emailAddress);
    }

    private void invalidateUserCache(String tenantId, String userId, String emailAddress) {
        TenantUserCacheKey tenantUserCacheKey = new TenantUserCacheKey(tenantId, userId);
        cacheService.invalidateKey(tenantId, CacheType.TENANT_USER_AUTH, tenantUserCacheKey);
        cacheService.invalidateKey(tenantId, CacheType.EMAIL_TO_USER_AUTH_V2, emailAddress);
    }

    public User disableUser(String userId) {
        Objects.requireNonNull(userId, "userId not provided");
        User existingUser = ensureUserWithIdExists(userId, Status.ACTIVE);
        ensureOnlyBillyAdminCanModifyAnotherBillyAdminUser(existingUser);
        User disabledUser = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider).transactionResult(configuration -> {
            User user = userDAO.disableUser(userId, configuration);
            sendUserEvent(user, EventType.USER_DISABLED, configuration);
            return user;
        });
        // TODO: This should be done asynchronously - need to fire an event here instead with a consumer
        lookerUserStatusSwitcher.changeUserStatusIfExists(userId, true);
        return disabledUser;
    }

    public User enableUser(String userId) {
        Objects.requireNonNull(userId, "userId not provided");
        User existingUser = ensureUserWithIdExists(userId, Status.DISABLED);
        ensureOnlyBillyAdminCanModifyAnotherBillyAdminUser(existingUser);
        User enabledUser = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider).transactionResult(configuration -> {
            User user = userDAO.enableUser(userId, configuration);
            sendUserEvent(user, EventType.USER_REACTIVATED, configuration);
            return user;
        });
        // TODO: This should be done asynchronously - need to fire an event here instead with a consumer
        lookerUserStatusSwitcher.changeUserStatusIfExists(userId, false);
        return enabledUser;
    }

    private void ensureOnlyBillyAdminCanModifyAnotherBillyAdminUser(User userToBeModified) {
        if (userToBeModified.getRole() != Role.BILLY_ADMIN) {
            return;
        }

        Optional<Role> callerRole = CurrentUserProvider.provideRole();
        if (callerRole.isPresent() && callerRole.get() == Role.BILLY_ADMIN) {
            return;
        }

        throw new ForbiddenException("Unauthorized.");
    }

    public void acceptTermsForGivenUser(BillyAuthPrincipal billyAuthPrincipal) {
        var optionalEmail = billyAuthPrincipal.getAuthUser().flatMap(UserPrincipal::getEmailAddress);
        if (optionalEmail.isEmpty()) {
            throw new IllegalArgumentException("email required to accept terms and conditions");
        }

        var email = optionalEmail.get();
        validateEmail(email);
        userDAO.acceptTermsForGivenUserEmail(email);
    }

    public void acceptTermsForGivenTestUser(User testUser, String tenantId) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new IllegalStateException("This method can be called only in local/CI environment");
        }

        userDAO.acceptTermsForGivenUserEmailInTenant(testUser.getEmail(), tenantId);
    }

    public void resendCognitoInvitationEmailForExistingUser(String email) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        Optional<User> user = getUserByEmail(email.trim());
        validateOptionalUserForResendingCognitoInvitationEmail(user, email, tenantId);
        cognitoService.resendCognitoInvitationEmailForExistingUser(tenantId, email.trim(), false);
    }

    public void resendCognitoInvitationEmailForExistingUserInGivenTenant(String email, String tenantId) {
        Optional<User> user = findUserByEmailFromAllTenants(email.trim());
        validateOptionalUserForResendingCognitoInvitationEmail(user, email, tenantId);
        cognitoService.resendCognitoInvitationEmailForExistingUser(tenantId, email.trim(), false);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private void validateOptionalUserForResendingCognitoInvitationEmail(Optional<User> user, String email, String tenantId) {
        if (user.isEmpty() || (StringUtils.isNotBlank(user.get().getTenantId()) && !tenantId.equals(user.get().getTenantId()))) {
            throw new ObjectNotFoundException(BillyObjectType.USER, email.trim());
        }

        if (isUserSsoOnly(user.get())) {
            throw new IllegalArgumentException("User is not allowed to login with password.");
        }
    }

    public void updateUserSsoConfig(String userId, UserSsoUpdate userSsoUpdate) {
        userDAO.updateUserSsoOnly(userId, userSsoUpdate.isSsoOnly());
    }

    private void validateEmail(String email) {
        if (StringUtils.isBlank(email)) {
            throw new IllegalArgumentException("Email is required");
        }

        if (!DataValidation.isEmailValid(email.trim())) {
            throw new IllegalArgumentException("Email provided is not valid");
        }
    }

    private void validateUserFields(User user) {
        Validator.validateStringLength(user.getDisplayName(), 0, MAX_DISPLAY_NAME_LENGTH, "display name");

        var title = user.getTitle();
        if (StringUtils.isNotBlank(title)) {
            Validator.validateStringLength(title, 0, MAX_TITLE_LENGTH, "title");
        }

        Validator.validateStringLength(user.getEmail(), 0, MAX_EMAIL_LENGTH, "email");

        var phoneNumber = user.getPhoneNumber();
        if (StringUtils.isNotBlank(phoneNumber)) {
            Validator.validateStringLength(phoneNumber, 0, MAX_PHONE_NUMBER_LENGTH, "phoneNumber");
        }
    }

    private User ensureUserWithIdExists(String userId, Status expectedUserStatus) {
        var optionalUser = userDAO.getUser(userId, Optional.empty());
        if (optionalUser.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.USER, userId);
        }

        if (optionalUser.get().getState() != expectedUserStatus) {
            throw new IllegalStateException("User is not in the expected state. Current state: " + optionalUser.get().getState());
        }

        return optionalUser.get();
    }

    private void ensureRoleCanBeAssigned(Role requestedRole) {
        if (requestedRole == null) {
            throw new IllegalArgumentException("Role is missing.");
        }

        if (requestedRole.notUserAssignable() && (!billyConfiguration.isLocalOrCi() || !isBillyRole(requestedRole))) {
            throw new IllegalArgumentException("Role is not valid: " + requestedRole);
        }
    }

    public static boolean isBillyRole(Role requestedRole) {
        return requestedRole == Role.BILLY_ENGINEER || requestedRole == Role.BILLY_ADMIN;
    }

    private void ensureCurrentUserHasSufficientEntityAccessToModify(User updatee) {
        EntityContext currentUserEntityContext = entityContextProvider.provideEntityContext();
        EntityContext updateeEntityContext = entityContextResolver.getEntityContextForUser(updatee.getUserId(), Set.of());
        if (!currentUserEntityContext.isSupersetOf(updateeEntityContext)) {
            throw new ForbiddenException("Unauthorized. User does not have sufficient entity access to modify the user.");
        }
    }

    private boolean isUserSsoOnly(User user) {
        if (!user.isSsoOnly()) {
            return false;
        }

        String tenantId = StringUtils.isNotBlank(user.getTenantId()) ? user.getTenantId() : tenantIdProvider.provideTenantIdString();
        return tenantCognitoService.tenantHasSso(tenantId);
    }

    private void setUserStatusType(User user) {
        if (isUserSsoOnly(user)) {
            user.setCognitoUserStatus(UserStatusType.CONFIRMED);
            return;
        }
        TenantId tenantId = tenantIdProvider.provide();

        try {
            UserStatusType userStatusType = cognitoService.getTenantUserStatus(tenantId, user.getEmail());
            user.setCognitoUserStatus(userStatusType);
        } catch (Exception e) {
            LOGGER.warn(USER_CORE, "Unable to get user status from Cognito for user: {}", user.getUserId(), e);
        }
    }

    public void setUserRoleToBillyEngineer(String email, String tenantId) {
        Validator.validateNonNullArgument(email);
        userDAO.setUserRoleToBillyEngineer(email, tenantId);
    }

    public List<User> getUsersByUserIds(List<String> userIds) {
        Validator.validateCollectionNotEmpty(userIds, "userIds");
        var users = userDAO.getUsers(userIds);
        userGroupAndSegmentRetrievalService.setUserGroupsAndApprovalSegmentsForUsers(users);
        return users;
    }

    public List<User> getUsersByExternalIds(List<String> externalIds) {
        Validator.validateCollectionNotEmpty(externalIds, "externalIds");
        List<User> users = userDAO.getUsersByExternalIds(externalIds);
        userGroupAndSegmentRetrievalService.setUserGroupsAndApprovalSegmentsForUsers(users);
        return users;
    }

    public List<User> getUsersWithBillyAdminRole() {
        return userDAO.getUsersWithBillyAdminRole();
    }

    @AllowUnauthenticated(cascade = true)
    public List<User> getAssociatedUsers(String email) {
        if (!StringUtils.endsWithIgnoreCase(email, Constants.INTERNAL_EMAIL_DOMAIN_POSTFIX)) {
            throw new IllegalArgumentException("Invalid email");
        }

        String emailWithoutDomain = StringUtils.removeEndIgnoreCase(email, Constants.INTERNAL_EMAIL_DOMAIN_POSTFIX);
        List<User> users = userDAO.getAssociatedUsers(emailWithoutDomain);
        if (CollectionUtils.isEmpty(users)) {
            return List.of();
        }
        return users.stream().filter(user -> user.getRole().isApiKeyAssignable(billyConfiguration)).toList();
    }

    public static List<User> addTenantNamesToUsers(List<Tenant> tenants, List<User> users) {
        Map<String, String> tenantNameByIdMap = tenants.stream().collect(Collectors.toMap(Tenant::getTenantId, Tenant::getName));
        List<User> usersInActiveTenants = users.stream().filter(u -> tenantNameByIdMap.containsKey(u.getTenantId())).collect(Collectors.toList());
        usersInActiveTenants.forEach(user -> user.setTenantName(tenantNameByIdMap.get(user.getTenantId())));
        return usersInActiveTenants;
    }

    @AllowUnauthenticated(cascade = true)
    private boolean isLoginWithEmailLinkEnabled(String email) {
        if (isCallAuthenticated()) {
            return false;
        }

        Validator.validateNonNullArgument(email);
        Optional<User> user = findUserByEmailFromAllTenants(email.trim().toLowerCase());
        if (user.isEmpty()) {
            return false;
        }

        if (user.get().getRole().notUserAssignable()) {
            return false;
        }
        return featureService.isEnabled(Feature.EMAIL_LINK_LOGIN, user.get().getTenantId());
    }

    private boolean isCallAuthenticated() {
        try {
            return StringUtils.isNotBlank(tenantIdProvider.provideTenantIdString());
        } catch (TenantNotFoundException ignored) {
            return false;
        }
    }

    public void backfillUserEntityIds(String tenantId) {
        userDAO.backfillUserEntityIds(tenantId);
    }

    public Set<String> getAllNormalizedUserEmailAddresses() {
        return userDAO.getAllNormalizedUserEmailAddresses();
    }

    private void sendUserEvent(User user, EventType eventType, Configuration configuration) {
        UserJson userJson = userMapper.userToJson(user);
        eventPublishingService.publishEventInTransaction(
            configuration,
            eventType,
            user.getTenantId(),
            user.getEntityIds(),
            new TenantPartitionKey(user.getTenantId()),
            objectMapper.writeValueAsBytes(userJson)
        );
    }
}
