package com.subskribe.billy.account.services;

import com.subskribe.billy.account.db.AccountPaymentMethodDAO;
import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;

public class AccountPaymentMethodGetService {

    private final AccountPaymentMethodDAO accountPaymentMethodDAO;

    @Inject
    public AccountPaymentMethodGetService(AccountPaymentMethodDAO accountPaymentMethodDAO) {
        this.accountPaymentMethodDAO = accountPaymentMethodDAO;
    }

    public List<AccountPaymentMethod> getPaymentMethodsInAccount(String accountId) {
        if (accountId == null) {
            throw new IllegalArgumentException("accountId is null");
        }

        return accountPaymentMethodDAO.getPaymentMethodsInAccount(accountId);
    }

    public Optional<AccountPaymentMethod> getPaymentMethod(String accountId, UUID paymentMethodId) {
        Validator.validateNonNullArguments(accountId, paymentMethodId);
        return accountPaymentMethodDAO.getPaymentMethod(accountId, paymentMethodId);
    }

    public AccountPaymentMethod getAccountPaymentMethodByPayment(Payment payment) {
        var accountPaymentMethodOptional = getPaymentMethod(payment.getCustomerAccountId(), payment.getPaymentMethodId());
        return accountPaymentMethodOptional.orElseThrow(() ->
            new ServiceFailureException("Unable to get a payment method with id: " + payment.getPaymentMethodId())
        );
    }

    public List<AccountPaymentMethod> getPaymentMethodsFromIdList(List<UUID> accountPaymentMethodIds) {
        return accountPaymentMethodDAO.getPaymentMethodsFromIdList(accountPaymentMethodIds);
    }

    public Optional<AccountPaymentMethod> getPaymentMethodForProviderPaymentMethodId(String accountId, UUID providerPaymentMethodId) {
        Validator.validateNonNullArguments(accountId, providerPaymentMethodId);
        return accountPaymentMethodDAO.getPaymentMethodByProviderPaymentMethodId(accountId, providerPaymentMethodId.toString());
    }
}
