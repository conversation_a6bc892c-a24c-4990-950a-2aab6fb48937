package com.subskribe.billy.account.services;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.StreamPartitionKey;
import com.subskribe.billy.event.model.TenantAccountPartitionKey;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.account.AccountMapper;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Set;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class AccountEventService {

    private final EventPublishingService eventPublishingService;
    private final TenantIdProvider tenantIdProvider;
    private final AccountMapper accountMapper;
    private final UncheckedObjectMapper objectMapper;

    @Inject
    public AccountEventService(EventPublishingService eventPublishingService, TenantIdProvider tenantIdProvider) {
        this.eventPublishingService = eventPublishingService;
        this.tenantIdProvider = tenantIdProvider;
        this.accountMapper = Mappers.getMapper(AccountMapper.class);
        this.objectMapper = UncheckedObjectMapper.defaultMapper();
    }

    public void publishAccountEvent(Account account, EventType eventType, Configuration configuration) {
        AccountJson accountJson = accountMapper.accountToJson(account);
        StreamPartitionKey partitionKey = TenantAccountPartitionKey.builder()
            .withTenantId(tenantIdProvider.provideTenantIdString())
            .withAccountId(account.getAccountId())
            .build();
        eventPublishingService.publishEventInTransaction(
            configuration,
            eventType,
            tenantIdProvider.provideTenantIdString(),
            account.getEntityIds(),
            partitionKey,
            objectMapper.writeValueAsBytes(accountJson)
        );
    }

    public void publishAccountContactEvent(DSLContext dslContext, AccountContact contact, EventType eventType, Set<String> entityIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        AccountContactJson contactJson = accountMapper.accountContactToJson(contact);
        StreamPartitionKey partitionKey = TenantAccountPartitionKey.builder().withTenantId(tenantId).withAccountId(contact.getAccountId()).build();
        eventPublishingService.publishEventInTransaction(
            dslContext,
            eventType,
            tenantIdProvider.provideTenantIdString(),
            entityIds,
            partitionKey,
            objectMapper.writeValueAsBytes(contactJson)
        );
    }
}
