package com.subskribe.billy.account.services;

import com.subskribe.billy.account.db.AccountAddressDAO;
import com.subskribe.billy.account.db.AccountContactDAO;
import com.subskribe.billy.account.db.AccountDAO;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.model.AccountPaymentConfiguration;
import com.subskribe.billy.account.model.ImmutableAccountPaymentConfiguration;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.CrmType;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.AddressValidationException;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.DuplicateAccountCrmIdException;
import com.subskribe.billy.exception.DuplicateAccountException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.invoice.service.AccountInvoiceService;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoRetrievalService;
import com.subskribe.billy.metrics.datadog.AllMetrics;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodService;
import com.subskribe.billy.payment.services.PaymentConfigurationService;
import com.subskribe.billy.salesforce.service.SalesforceIntegrationService;
import com.subskribe.billy.salesforce.service.SalesforceService;
import com.subskribe.billy.shared.DataValidation;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.model.ImmutableTenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobModule;
import com.subskribe.billy.tenantjob.model.TenantJobObjectModel;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;

@AllMetrics
public class AccountService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountService.class);
    private static final Set<String> US_COUNTRY_NAME_ALIASES = Set.of("us", "usa", "us of a", "united states", "united states of america", "america");

    private final AccountDAO accountDAO;
    private final AccountContactDAO accountContactDAO;
    private final AccountAddressDAO accountAddressDAO;
    private final OrderGetService orderGetService;
    private final CreditMemoRetrievalService creditMemoRetrievalService;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final EntityGetService entityGetService;
    private final TaxService taxService;
    private final TenantSettingService tenantSettingService;
    private final AccountIdGenerator accountIdGenerator;
    private final ContactIdGenerator contactIdGenerator;
    private final SubscriptionGetService subscriptionGetService;
    private final AccountGetService accountGetService;
    private final PaymentConfigurationService paymentConfigurationService;
    private final AccountInvoiceService accountInvoiceService;
    private final HubSpotIntegrationService hubSpotIntegrationService;
    private final SalesforceIntegrationService salesforceIntegrationService;
    private final TenantJobDispatcherService tenantJobDispatcherService;
    private final AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService;
    private final AccountCacheInvalidator accountCacheInvalidator;
    private final FeatureService featureService;
    private final UserService userService;
    private final EntityContextResolver entityContextResolver;
    private final EntityContextProvider entityContextProvider;

    @Inject
    public AccountService(
        AccountDAO accountDAO,
        AccountContactDAO accountContactDAO,
        AccountAddressDAO accountAddressDAO,
        OrderGetService orderGetService,
        CreditMemoRetrievalService creditMemoRetrievalService,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider,
        EntityGetService entityGetService,
        TaxService taxService,
        TenantSettingService tenantSettingService,
        AccountIdGenerator accountIdGenerator,
        ContactIdGenerator contactIdGenerator,
        SubscriptionGetService subscriptionGetService,
        AccountGetService accountGetService,
        PaymentConfigurationService paymentConfigurationService,
        AccountInvoiceService accountInvoiceService,
        HubSpotIntegrationService hubSpotIntegrationService,
        SalesforceIntegrationService salesforceIntegrationService,
        TenantJobDispatcherService tenantJobDispatcherService,
        AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService,
        AccountCacheInvalidator accountCacheInvalidator,
        FeatureService featureService,
        UserService userService,
        EntityContextResolver entityContextResolver,
        EntityContextProvider entityContextProvider
    ) {
        this.accountDAO = accountDAO;
        this.accountContactDAO = accountContactDAO;
        this.accountAddressDAO = accountAddressDAO;
        this.orderGetService = orderGetService;
        this.creditMemoRetrievalService = creditMemoRetrievalService;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.entityGetService = entityGetService;
        this.taxService = taxService;
        this.tenantSettingService = tenantSettingService;
        this.accountIdGenerator = accountIdGenerator;
        this.contactIdGenerator = contactIdGenerator;
        this.subscriptionGetService = subscriptionGetService;
        this.accountGetService = accountGetService;
        this.paymentConfigurationService = paymentConfigurationService;
        this.accountInvoiceService = accountInvoiceService;
        this.hubSpotIntegrationService = hubSpotIntegrationService;
        this.salesforceIntegrationService = salesforceIntegrationService;
        this.tenantJobDispatcherService = tenantJobDispatcherService;
        this.accountAutomaticPaymentMethodService = accountAutomaticPaymentMethodService;
        this.accountCacheInvalidator = accountCacheInvalidator;
        this.featureService = featureService;
        this.userService = userService;
        this.entityContextResolver = entityContextResolver;
        this.entityContextProvider = entityContextProvider;
    }

    public Account addAccount(Account account, AccountAddress address) {
        if (address != null) {
            var dsl = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
            var addressId = upsertAddress(dsl, address, null);
            account.setAddressId(addressId);
        }

        return addAccount(account);
    }

    public Account addAccount(Account account) {
        account.sanitize();
        validateAccount(account);
        String accountName = account.getName();
        checkExistingAccountName(accountName);
        checkExistingCrmId(account.getCrmId());

        account.setAccountId(accountIdGenerator.generate());

        setSupportedPaymentTypes(account);

        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(account.getEntityIds());
        account.setEntityIds(entityIds);

        CustomField customFields = account.getCustomFields();
        Account savedAccount = accountDAO.addAccount(account);
        LOGGER.info("Added account {} with id {} and crmId {}", accountName, savedAccount.getAccountId(), savedAccount.getCrmId());
        savedAccount.setCustomFields(customFields);

        postAddAccountTriggers(savedAccount);
        postUpsertAccountTriggers(savedAccount);

        return savedAccount;
    }

    private void checkExistingCrmId(String crmId) {
        if (StringUtils.isBlank(crmId)) {
            return;
        }

        Optional<String> accountId = accountDAO.getAccountIdByCrmId(crmId);

        if (accountId.isPresent()) {
            throw new DuplicateAccountCrmIdException(crmId);
        }
    }

    private void postAddAccountTriggers(Account account) {
        if (CrmType.fromCrmId(account.getCrmId()) == CrmType.HUBSPOT) {
            dispatchHubSpotAccountSyncTenantJob(
                tenantIdProvider.provideTenantIdString(),
                account.getAccountId(),
                hubSpotIntegrationService,
                tenantJobDispatcherService
            );
        }
    }

    private static void dispatchHubSpotAccountSyncTenantJob(
        String tenantId,
        String accountId,
        HubSpotIntegrationService hubSpotIntegrationService,
        TenantJobDispatcherService tenantJobDispatcherService
    ) {
        if (!hubSpotIntegrationService.hasCompletedHubSpotIntegration(tenantId)) {
            LOGGER.info("skipping hubspot account sync job dispatch. hubspot integration missing. account id: {}", accountId);
            return;
        }
        tenantJobDispatcherService.dispatch(
            ImmutableTenantJob.builder()
                .module(TenantJobModule.HUBSPOT)
                .jobType(TenantJobType.HUBSPOT_ACCOUNT_SYNC)
                .objectModel(TenantJobObjectModel.ACCOUNT)
                .objectId(accountId)
                .partitionKey(tenantId)
                .build()
        );
    }

    private static void dispatchSalesforceAccountSyncTenantJob(
        String tenantId,
        String accountId,
        SalesforceIntegrationService salesforceIntegrationService,
        TenantJobDispatcherService tenantJobDispatcherService
    ) {
        if (!salesforceIntegrationService.hasCompletedSalesforceIntegration(tenantId)) {
            LOGGER.info("skipping salesforce account sync job dispatch. salesforce integration not present. account id: {}", accountId);
            return;
        }
        tenantJobDispatcherService.dispatch(
            ImmutableTenantJob.builder()
                .module(TenantJobModule.SALESFORCE)
                .jobType(TenantJobType.SALESFORCE_ACCOUNT_SYNC)
                .objectModel(TenantJobObjectModel.ACCOUNT)
                .objectId(accountId)
                .build()
        );
    }

    private void checkExistingAccountName(String accountName) {
        // Check across entire tenant
        String tenantId = tenantIdProvider.provideTenantIdString();
        var entityContext = entityContextProvider.provideAllEntitiesContext();
        var existingAccount = TenantContextInjector.spawnThreadAndCallInEntityContext(tenantId, entityContext, entityContextProvider, () ->
            accountDAO.getAccountByName(accountName)
        );
        if (existingAccount.isPresent()) {
            throw new DuplicateAccountException(accountName);
        }
    }

    // Set default payment types for an account, if not specified by user
    private void setSupportedPaymentTypes(Account account) {
        if (CollectionUtils.isEmpty(account.getSupportedPaymentTypes())) {
            Set<PaymentType> supportedPaymentTypes = paymentConfigurationService.getTenantPaymentConfiguration().getSupportedPaymentTypes();
            account.setSupportedPaymentTypes(supportedPaymentTypes);
        }
    }

    public void updateAccount(Account account) {
        updateAccount(account, null);
    }

    public void updateAccount(Account account, AccountAddress address) {
        account.sanitize();
        validateAccount(account);

        if (account.getAccountId() == null) {
            throw new IllegalArgumentException("account id is required");
        }

        var optionalExistingAccount = accountDAO.getAccount(account.getAccountId());
        if (optionalExistingAccount.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.ACCOUNT, account.getAccountId());
        }

        var existingAccount = optionalExistingAccount.get();

        String name = account.getName();

        if (!name.equals(existingAccount.getName())) {
            // if updated name is different from previous name, check to see if new name already exists
            checkExistingAccountName(name);
        }

        boolean accountInUse = accountGetService.isAccountInUse(existingAccount.getAccountId());
        if (accountInUse) {
            throwIfCurrencyOfAUsedAccountIsBeingUpdated(existingAccount, account);
        }

        if (existingAccount.getIsReseller() && !account.getIsReseller()) {
            throwIfResellerStatusOfAUsedAccountIsUpdated(existingAccount, accountInUse);
        }

        validateTaxExemptionFieldMutation(existingAccount, account);

        // merge only selected fields
        existingAccount.setName(name);
        existingAccount.setLegalName(account.getLegalName());
        existingAccount.setDescription(account.getDescription());
        existingAccount.setPhoneNumber(account.getPhoneNumber());
        existingAccount.setExternalId(account.getExternalId());
        existingAccount.setCurrency(account.getCurrency());
        existingAccount.setTaxExemptionUseCode(account.getTaxExemptionUseCode());
        existingAccount.setIsReseller(account.getIsReseller());
        existingAccount.setExcludeFromBatchOperations(account.isExcludeFromBatchOperations());
        existingAccount.setExcludeFromDunning(account.getExcludeFromDunning());
        existingAccount.setAddressId(account.getAddressId());
        existingAccount.setErpId(account.getErpId());

        // Added for backwards compatibility, since existing accounts will not have this field set
        setSupportedPaymentTypes(existingAccount);

        if (shouldAccountCrmIdBeUpdated(account.getCrmId(), existingAccount.getCrmId())) {
            checkExistingCrmId(account.getCrmId());
            existingAccount.setCrmId(account.getCrmId());
        }

        if (account.getCrmType() != null) {
            existingAccount.setCrmType(account.getCrmType());
        }

        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            account.getEntityIds(),
            existingAccount.getEntityIds()
        );
        validateEntityIdsForUpdate(existingAccount, entityIds);
        existingAccount.setEntityIds(entityIds);

        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        // invalidate the cache right before the update
        accountCacheInvalidator.invalidateAccountCaches(account.getAccountId());
        dslContext.transaction(configuration -> {
            var dsl = DSL.using(configuration);
            if (address != null) {
                var addressId = upsertAddress(dsl, address, existingAccount.getAddressId());
                existingAccount.setAddressId(addressId);
            } else {
                existingAccount.setAddressId(null);
            }

            accountDAO.updateAccount(dsl, existingAccount);
        });
        postUpsertAccountTriggers(existingAccount);
    }

    public void setAccountErpId(Account account, String erpId) {
        account.setErpId(erpId);
        AccountAddress address = account.getAddressId() == null ? null : accountGetService.getAccountAddress(account.getAddressId()).orElse(null);
        updateAccount(account, address);
        LOGGER.info("updated account {} with erp id {}", account.getAccountId(), account.getErpId());
    }

    public void updateAccountErpId(String accountId, String erpId, boolean override) {
        Validator.validateStringNotBlank(accountId, "accountId");
        Validator.validateStringNotBlank(erpId, "erpId");

        Account account = accountGetService.getAccount(accountId);

        if (!override && StringUtils.isNotBlank(account.getErpId())) {
            throw new ConflictingStateException("Account already has an ERP ID");
        }

        // invalidate account cache first before updating the DB. This way we avoid cache being out of sync in partial failures cases
        accountCacheInvalidator.invalidateAccountCaches(accountId);
        accountDAO.setAccountErpId(accountId, erpId);
    }

    private void validateEntityIdsForUpdate(Account existingAccount, Set<String> entityIds) {
        if (entityIds.contains(EntityContext.ALL_ACCESS_ID)) {
            return;
        }
        Set<String> orderEntities = orderGetService.entitiesUsedByAccount(existingAccount.getAccountId());
        entityGetService.validateParentEntitiesForUpdate(entityIds, orderEntities, "account", "orders");
        Set<String> creditMemoEntities = creditMemoRetrievalService.entitiesUsedByCreditMemosOfAccount(existingAccount.getAccountId());
        entityGetService.validateParentEntitiesForUpdate(entityIds, creditMemoEntities, "account", "credit memos");
    }

    private void postUpsertAccountTriggers(Account account) {
        if (
            StringUtils.isBlank(account.getCrmId()) ||
            StringUtils.startsWith(account.getCrmId(), SalesforceService.TEST_ACCOUNT_ID_PREFIX) ||
            (account.getCrmType() != CrmType.SALESFORCE)
        ) {
            return;
        }

        dispatchSalesforceAccountSyncTenantJob(
            tenantIdProvider.provideTenantIdString(),
            account.getAccountId(),
            salesforceIntegrationService,
            tenantJobDispatcherService
        );
    }

    private void throwIfCurrencyOfAUsedAccountIsBeingUpdated(Account existingAccount, Account newAccount) {
        if (existingAccount.getCurrency() == null && newAccount.getCurrency() == null) {
            return;
        }

        if (
            existingAccount.getCurrency() == null ||
            newAccount.getCurrency() == null ||
            !newAccount.getCurrency().getCurrencyCode().equals(existingAccount.getCurrency().getCurrencyCode())
        ) {
            throw new IllegalArgumentException("Account is in use, its currency cannot be updated.");
        }
    }

    private void throwIfResellerStatusOfAUsedAccountIsUpdated(Account existingAccount, boolean accountInUse) {
        if (CollectionUtils.isEmpty(existingAccount.getContacts())) {
            return;
        }
        boolean resellerContactInUse = existingAccount
            .getContacts()
            .stream()
            .map(AccountContact::getContactId)
            .anyMatch(accountGetService::isContactInUse);
        if (accountInUse || resellerContactInUse) {
            throw new IllegalArgumentException("Account's reseller status cannot be updated because the account is in use");
        }
    }

    public void updateAccountSupportedPaymentTypes(String accountId, Set<PaymentType> supportedPaymentTypes) {
        Validator.validateStringNotBlank(accountId, "accountId");
        accountCacheInvalidator.invalidateAccountCaches(accountId);
        accountDAO.updateAccountSupportedPaymentTypes(accountId, supportedPaymentTypes);
        accountAutomaticPaymentMethodService.removeActiveMethodsForAccountIfNoLongerSupported(accountId, supportedPaymentTypes);
    }

    public Account upsertAccount(Account account, AccountAddress address) {
        if (StringUtils.isBlank(account.getAccountId())) {
            return addAccount(account, address);
        }
        updateAccount(account, address);
        return accountGetService.getAccount(account.getAccountId());
    }

    public Account upsertAccount(Account account) {
        if (StringUtils.isBlank(account.getAccountId())) {
            return addAccount(account);
        }
        updateAccount(account, null);
        return accountGetService.getAccount(account.getAccountId());
    }

    public Account deleteAccount(String accountId) {
        if (accountId == null) {
            throw new IllegalArgumentException("accountId is required");
        }

        if (orderGetService.accountUsedByOrder(accountId)) {
            throw new IllegalStateException("Account containing orders cannot be deleted");
        }

        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var contacts = accountGetService.getAccountContacts(accountId, false);

        // cache invalidation has to be right before the account is deleted
        // otherwise we might get cache staleness
        accountCacheInvalidator.invalidateAccountCaches(accountId);
        return tenantDSLContext.transactionResult(configuration -> {
            var dslContext = DSL.using(configuration);

            for (var contact : contacts) {
                deleteContactTransaction(contact.getContactId(), dslContext);
            }

            return accountDAO.deleteAccountTransaction(accountId, dslContext);
        });
    }

    public AccountContact addContact(AccountContact accountContact, boolean skipAddressValidation, boolean strictValidation) {
        accountContact.sanitize();
        validateContact(accountContact);

        Account account = accountGetService.getAccount(accountContact.getAccountId());

        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var address = accountContact.getAddress();

        if (address != null) {
            address.validate();

            if (!skipAddressValidation) {
                validateAddress(strictValidation, address);
            }
        }

        accountContact.setContactId(contactIdGenerator.generate());

        tenantDSLContext.transaction(configuration -> addContactTransaction(accountContact, account, address, configuration));
        return accountContact;
    }

    private void addContactTransaction(AccountContact accountContact, Account account, AccountAddress address, Configuration configuration) {
        var dslContext = DSL.using(configuration);

        AccountAddress addedAddress = null;
        if (address != null) {
            addedAddress = accountAddressDAO.addAddress(dslContext, address);
            accountContact.setAddressId(addedAddress.getAddressId());
        }

        var addedContact = accountContactDAO.addContact(dslContext, accountContact);
        accountContact.setId(addedContact.getId());
        accountContact.setContactId(addedContact.getContactId());

        // updates account primary contact if there isn't one set
        // todo: Should this be removed or be made updatable?
        if (account.getPrimaryContactId() == null) {
            account.setPrimaryContactId(addedContact.getContactId());
            if (addedAddress != null && account.getAddressId() == null) {
                account.setAddressId(addedAddress.getAddressId());
            }
            accountDAO.updateAccount(dslContext, account);
        }
    }

    public AccountContact updateContact(AccountContact accountContact, boolean skipAddressValidation, boolean strictValidation) {
        Optional<Account> optionalExistingAccount = accountDAO.getAccount(accountContact.getAccountId());
        if (optionalExistingAccount.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.ACCOUNT, accountContact.getAccountId());
        }
        accountContact.sanitize();
        validateContact(accountContact);

        if (accountContact.getAddress() == null && accountContactDAO.contactIsInUseAndHasAddress(accountContact)) {
            throw new IllegalStateException("Cannot remove address for in use contact");
        }

        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var address = accountContact.getAddress();

        if (address != null) {
            address.validate();

            if (!skipAddressValidation) {
                validateAddress(strictValidation, address);
            }
        }

        AccountContact existingContact = accountGetService.getContact(accountContact.getContactId());
        validateAccountIdForContactUpdate(accountContact.getAccountId(), existingContact.getAccountId());

        tenantDSLContext.transaction(configuration -> updateContactTransaction(accountContact, existingContact, address, configuration));

        return accountGetService.getContact(accountContact.getContactId());
    }

    private void updateContactTransaction(
        AccountContact accountContact,
        AccountContact existingContact,
        AccountAddress address,
        Configuration configuration
    ) {
        var dslContext = DSL.using(configuration);

        if (address != null) {
            if (existingContact.getAddressId() != null && existingContact.getAddress() != null) {
                address.setId(existingContact.getAddress().getId());
                address.setAddressId(existingContact.getAddressId());
                accountAddressDAO.updateAddress(dslContext, address);
            } else {
                address = accountAddressDAO.addAddress(dslContext, address);
            }
        } else if (existingContact.getAddressId() != null) {
            accountAddressDAO.deleteAddress(dslContext, existingContact.getAddressId());
        }

        var addressId = address == null ? null : address.getAddressId();

        accountContact.setId(existingContact.getId());
        accountContact.setAddressId(addressId);

        accountContactDAO.updateContact(dslContext, accountContact);
    }

    public String upsertAddress(DSLContext dslContext, AccountAddress address, String existingAddressId) {
        address.validateHardConstraints();
        if (existingAddressId == null) {
            return accountAddressDAO.addAddress(dslContext, address).getAddressId();
        } else {
            return accountAddressDAO.updateAddress(dslContext, address);
        }
    }

    private void validateAccountIdForContactUpdate(String newAccountId, String existingAccountId) {
        if (!newAccountId.equalsIgnoreCase(existingAccountId)) {
            throw new IllegalArgumentException("Contact cannot be moved across different accounts");
        }
    }

    public AccountContact upsertContact(AccountContact accountContact, boolean skipAddressValidation, boolean strictValidation) {
        var address = accountContact.getAddress();
        if (
            address != null &&
            StringUtils.isBlank(address.getStreetAddressLine1()) &&
            StringUtils.isBlank(address.getStreetAddressLine2()) &&
            StringUtils.isBlank(address.getCity()) &&
            StringUtils.isBlank(address.getState()) &&
            StringUtils.isBlank(address.getZipcode()) &&
            StringUtils.isBlank(address.getCountry())
        ) {
            //set address to null so we don't do address validation
            accountContact.setAddress(null);
        }

        if (StringUtils.isBlank(accountContact.getContactId())) {
            return addContact(accountContact, skipAddressValidation, strictValidation);
        }
        updateContact(accountContact, skipAddressValidation, strictValidation);
        return accountGetService.getContact(accountContact.getContactId());
    }

    public AccountContact deleteContact(String contactId) {
        if (orderGetService.contactUsedByOrder(contactId)) {
            throw new IllegalStateException("Contacts with orders cannot be deleted");
        }

        if (subscriptionGetService.contactAssociatedWithSubscription(contactId)) {
            throw new IllegalStateException("Contacts used by subscriptions cannot be deleted");
        }

        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return tenantDSLContext.transactionResult(configuration -> {
            var dslContext = DSL.using(configuration);
            return deleteContactTransaction(contactId, dslContext);
        });
    }

    private AccountContact deleteContactTransaction(String contactId, DSLContext context) {
        var deletedContact = accountContactDAO.deleteContact(context, contactId);
        if (deletedContact == null) {
            throw new ObjectNotFoundException(BillyObjectType.ACCOUNT_CONTACT, contactId);
        }
        var deletedAddress = accountAddressDAO.deleteAddress(context, deletedContact.getAddressId());
        deletedContact.setAddress(deletedAddress);
        return deletedContact;
    }

    private void validateContact(AccountContact contact) {
        Validator.validateNonNullArgument(contact, "contact");
        contact.validate();
        if (!DataValidation.isEmailValid(contact.getEmail())) {
            throw new IllegalArgumentException("Email provided is not valid");
        }

        if (StringUtils.isBlank(contact.getExternalId())) {
            contact.setExternalId(null);
        }

        if (!featureService.isEnabled(Feature.NETSUITE_INVOICE_SYNC) && StringUtils.isNotBlank(contact.getErpId())) {
            throw new InvalidInputException("cannot add erpId to contact because feature is not yet enabled");
        }
    }

    private void validateAccount(Account account) {
        if (account == null) {
            throw new IllegalArgumentException("account is required");
        }

        account.validate();

        // todo: validate crmId exists in crm

        if (StringUtils.isBlank(account.getExternalId())) {
            account.setExternalId(null);
        }

        if (StringUtils.isBlank(account.getErpId())) {
            account.setErpId(null);
        }

        List<String> supportedTenantCurrencyCodes = tenantSettingService.getTenantSettingInternal().getSupportedCurrencies();
        if (!supportedTenantCurrencyCodes.contains(account.getCurrency().getCurrencyCode())) {
            throw new InvalidInputException("Account's currency is not supported by the tenant: " + account.getCurrency());
        }
    }

    private void validateAddress(boolean strictValidation, AccountAddress address) {
        String country = StringUtils.normalizeSpace(address.getCountry().toLowerCase());
        if (!US_COUNTRY_NAME_ALIASES.contains(country)) {
            LOGGER.info("Skipping tax engine validation for non-us country: {}", country);
            return;
        }

        var suggestedAddress = taxService.validateAddress(address);
        if (strictValidation && !suggestedAddress.isSameAddress(address)) {
            throw new AddressValidationException("Alternate address found", suggestedAddress);
        }
    }

    public void markAccountsForMetricsRecalculation(DSLContext txnDslContext, String tenantId, Set<String> accountIds) {
        accountDAO.markAccountsForMetricsRecalculation(txnDslContext, tenantId, accountIds);
        accountIds.forEach(accountCacheInvalidator::invalidateAccountCaches);
    }

    public void updateAccountMetrics(DSLContext txnDslContext, String tenantId, String accountId, JSONB json) {
        accountCacheInvalidator.invalidateAccountCaches(accountId);
        accountDAO.updateAccountMetrics(txnDslContext, tenantId, accountId, json);
    }

    public void updateAccountCrmId(String accountId, String crmId) {
        Validator.validateStringNotBlank(accountId, "accountId");

        Account existingAccount = accountDAO.getAccount(accountId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ACCOUNT, accountId));
        existingAccount.setCrmId(StringUtils.trimToNull(crmId));

        LOGGER.info("admin action: updating account {} crm id from '{}' to '{}'", accountId, existingAccount.getCrmId(), crmId);
        accountDAO.updateAccount(existingAccount);
        accountCacheInvalidator.invalidateAccountCaches(accountId);
    }

    public void updateAccountCrmIdAndCrmTypeInTransaction(DSLContext dslContext, String accountId, String crmId, CrmType crmType) {
        Validator.validateNonNullArgument(accountId, "accountId");
        Validator.validateNonNullArgument(crmId, "crmId");

        Account existingAccount = accountGetService.getAccount(accountId);
        validateCrmIdCanBeSetForAccount(existingAccount);
        existingAccount.setCrmId(crmId);
        existingAccount.setCrmType(crmType);

        accountDAO.updateAccount(dslContext, existingAccount);
        accountCacheInvalidator.invalidateAccountCaches(accountId);
    }

    public AccountPaymentConfiguration upsertAccountPaymentConfiguration(String accountId, AccountPaymentConfiguration config) {
        Validator.validateStringNotBlank(accountId, "accountId");
        Validator.validateNonNullArgument(config, "config");

        if (!accountId.equals(config.getAccountId())) {
            throw new InvalidInputException(
                String.format("Account ID in the configuration (%s) does not match the provided account ID (%s)", config.getAccountId(), accountId)
            );
        }
        accountGetService.getAccount(accountId); // Ensure account exists
        Optional<AccountPaymentConfiguration> accountPaymentConfiguration = accountGetService.getAccountPaymentConfiguration(accountId);
        if (accountPaymentConfiguration.isPresent()) {
            config = ImmutableAccountPaymentConfiguration.builder().from(config).id(accountPaymentConfiguration.get().getId()).build();
        }

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return accountDAO.upsertAccountPaymentConfiguration(dslContext, tenantId, config.getAccountId(), config);
    }

    private void validateCrmIdCanBeSetForAccount(Account account) {
        if (StringUtils.isNotBlank(account.getCrmId()) && accountGetService.accountUsedByOrderWithOpportunity(account.getAccountId())) {
            throw new ConflictingStateException("Account has a stored CRM ID and is used by an opportunity, so it cannot have its CRM ID updated");
        }
    }

    public void updateAddressCountriesInBulk(List<AccountAddress> accountAddresses) {
        if (CollectionUtils.isEmpty(accountAddresses)) {
            return;
        }
        accountAddressDAO.updateAddressCountriesInBulk(accountAddresses);
    }

    private void validateTaxExemptionFieldMutation(Account existingAccount, Account currentAccount) {
        boolean exemptionCodeChanged = currentAccount.getTaxExemptionUseCode() != existingAccount.getTaxExemptionUseCode();
        if (!exemptionCodeChanged) {
            return;
        }
        int draftInvoiceCount = accountInvoiceService.getDraftInvoicesCountForAccount(currentAccount.getAccountId());
        if (draftInvoiceCount > 0) {
            String message = String.format(
                "This Account has %d DRAFT Invoice(s) pending, tax exemption code cannot be modified until they are posted or deleted",
                draftInvoiceCount
            );
            throw new IllegalStateException(message);
        }
    }

    /**
     * Determines if the CRM ID should be updated.
     * Returns true if:
     *  - The existing CRM ID is blank and the new CRM ID is not blank, OR
     *  - Both CRM IDs are not blank, and the logged-in user is an admin.
     */
    private boolean shouldAccountCrmIdBeUpdated(String newAccountCrmId, String existingAccountCrmId) {
        if (StringUtils.isBlank(newAccountCrmId)) {
            return false;
        }

        // newAccountCrmId is not blank, but existingAccountCrmId is not blank
        if (StringUtils.isBlank(existingAccountCrmId)) {
            return true;
        }

        // both are not blank and are different and update is requested by admin user
        Optional<User> optionalCurrentUser = userService.getCurrentUser();
        return (
            !StringUtils.equalsIgnoreCase(newAccountCrmId, existingAccountCrmId) &&
            optionalCurrentUser.filter(user -> user.getRole() == Role.ADMIN).isPresent()
        );
    }
}
