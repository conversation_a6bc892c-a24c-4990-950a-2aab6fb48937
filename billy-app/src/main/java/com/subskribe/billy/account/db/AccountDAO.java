package com.subskribe.billy.account.db;

import static com.subskribe.billy.jooq.default_schema.Tables.ACCOUNT_PAYMENT_CONFIGURATION;
import static com.subskribe.billy.jooq.default_schema.tables.Account.ACCOUNT;

import com.codahale.metrics.annotation.Timed;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.subskribe.billy.account.mapper.AccountDAOMapper;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountPaymentConfiguration;
import com.subskribe.billy.account.model.AccountStub;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountPaymentConfigurationRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountRecord;
import com.subskribe.billy.metrics.model.StoredMetrics;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.AccountType;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.JSON;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class AccountDAO {

    private static final String UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME = "index_account_external_id";
    private static final String UNIQUE_CRM_ID_CONSTRAINT_NAME = "index_tenant_account_crm_id";
    private static final String UNIQUE_ACCOUNT_NAME = "index_account_name";

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountDAO.class);

    private final AccountDAOMapper accountDAOMapper;
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;

    @Inject
    public AccountDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        accountDAOMapper = Mappers.getMapper(AccountDAOMapper.class);
    }

    public Account addAccount(Account account, Configuration configuration) {
        Validator.validateCollectionNotEmpty(account.getEntityIds(), "entityIds");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = DSL.using(configuration);
        var now = LocalDateTime.now(ZoneOffset.UTC);
        var accountRecord = accountDAOMapper.accountToRecord(account);
        accountRecord.reset(ACCOUNT.ID);
        accountRecord.setTenantId(tenantId);
        accountRecord.setCreatedOn(now);
        accountRecord.setUpdatedOn(now);
        accountRecord.setIsDeleted(false);

        var record = PostgresErrorHandler.withAnyConstraintAsConflict(
            () -> dslContext.insertInto(ACCOUNT).set(accountRecord).returning().fetchOne(),
            Map.of(
                UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME,
                String.format("account exists with external id: %s", account.getExternalId()),
                UNIQUE_CRM_ID_CONSTRAINT_NAME,
                String.format("account with CRM ID %s already exists", account.getCrmId()),
                UNIQUE_ACCOUNT_NAME,
                String.format("account with name %s already exists", account.getName())
            )
        );
        return accountDAOMapper.recordToAccount(record);
    }

    public AccountPaymentConfiguration upsertAccountPaymentConfiguration(
        DSLContext dslContext,
        String tenantId,
        String accountId,
        AccountPaymentConfiguration accountPaymentConfiguration
    ) {
        Validator.validateNonNullArgumentInternal(accountPaymentConfiguration, "accountPaymentConfiguration");

        AccountPaymentConfigurationRecord record = accountDAOMapper.accountPaymentConfigurationToRecord(accountPaymentConfiguration);
        record.setTenantId(tenantId);

        LOGGER.info("Upserting account payment configuration for accountId: {} and tenantId: {}", accountId, tenantId);

        if (record.getId() == null) {
            record.reset(ACCOUNT_PAYMENT_CONFIGURATION.ID);
        }
        record.setAccountId(accountId);

        var savedRecord = dslContext
            .insertInto(ACCOUNT_PAYMENT_CONFIGURATION)
            .set(record)
            .onDuplicateKeyUpdate()
            .set(record)
            .returning()
            .fetchOneInto(AccountPaymentConfigurationRecord.class);
        return accountDAOMapper.recordToAccountPaymentConfiguration(savedRecord);
    }

    public Optional<AccountPaymentConfiguration> getAccountPaymentConfiguration(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        Optional<AccountPaymentConfigurationRecord> record = dslContext
            .selectFrom(ACCOUNT_PAYMENT_CONFIGURATION)
            .where(ACCOUNT_PAYMENT_CONFIGURATION.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_PAYMENT_CONFIGURATION.ACCOUNT_ID.eq(accountId))
            .and(ACCOUNT_PAYMENT_CONFIGURATION.IS_DELETED.isFalse())
            .fetchOptionalInto(AccountPaymentConfigurationRecord.class);

        return record.map(accountDAOMapper::recordToAccountPaymentConfiguration);
    }

    public PageResult<List<String>, String> getAccountIdsForTenant(PageRequest<String> pageRequest) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var queryStep = dslContext.select(ACCOUNT.ACCOUNT_ID).from(ACCOUNT).where(ACCOUNT.TENANT_ID.eq(tenantId)).and(ACCOUNT.IS_DELETED.isFalse());

        if (pageRequest.getPageToken() != null) {
            queryStep = queryStep.and(ACCOUNT.ACCOUNT_ID.gt(pageRequest.getPageToken()));
        }

        List<String> accountIds = queryStep.orderBy(ACCOUNT.ACCOUNT_ID).limit(pageRequest.getLimit()).fetchInto(String.class).stream().toList();
        return PageResult.fromCollectionAndRequest(accountIds, pageRequest, Function.identity());
    }

    @Timed
    public List<Account> getAccounts(AccountType accountType, PaginationQueryParams paginationQueryParams, String tenantId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext.select().from(ACCOUNT).where(ACCOUNT.TENANT_ID.eq(tenantId)).and(ACCOUNT.IS_DELETED.isFalse());

        switch (accountType) {
            case RESELLER -> query = query.and(ACCOUNT.IS_RESELLER.isTrue());
            case NON_RESELLER -> query = query.and(ACCOUNT.IS_RESELLER.isFalse());
            case ALL -> {
                // nothing to do here
            }
            default -> throw new IllegalStateException("unknown account type to filter accounts. AccountType found: " + accountType.name());
        }

        var accountRecords = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            ACCOUNT,
            ACCOUNT.ID,
            ACCOUNT.TENANT_ID,
            ACCOUNT.CREATED_ON,
            AccountRecord.class
        );

        if (accountRecords.size() >= PaginationQueryParams.MAX_PAGINATION_LIMIT * 0.9) {
            LOGGER.warn(
                "tenant {} has {} accounts approaching limit of {}",
                tenantId,
                accountRecords.size(),
                PaginationQueryParams.MAX_PAGINATION_LIMIT
            );
        }

        return accountDAOMapper.recordsToAccounts(accountRecords);
    }

    @Timed
    public List<Account> getAccounts(AccountType accountType, PaginationQueryParams paginationQueryParams) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        return getAccounts(accountType, paginationQueryParams, tenantId);
    }

    public List<Account> getPageOfAccountsWithCrmIdsForTenant(int limit) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<AccountRecord> records = dslContext
            .select()
            .from(ACCOUNT)
            .where(ACCOUNT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT.CRM_ID.isNotNull())
            .and(ACCOUNT.CRM_TYPE.isNull())
            .and(ACCOUNT.IS_DELETED.isFalse())
            .limit(limit)
            .fetchInto(AccountRecord.class);

        return accountDAOMapper.recordsToAccounts(records);
    }

    @Timed
    public List<Account> getAccountsExcludedFromBatchOperations(PaginationQueryParams paginationQueryParams) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext
            .select()
            .from(ACCOUNT)
            .where(ACCOUNT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT.IS_DELETED.isFalse())
            .and(ACCOUNT.EXCLUDE_FROM_BATCH_OPERATIONS.isTrue());

        var accountRecords = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            ACCOUNT,
            ACCOUNT.ID,
            ACCOUNT.TENANT_ID,
            ACCOUNT.CREATED_ON,
            AccountRecord.class
        );

        return accountDAOMapper.recordsToAccounts(accountRecords);
    }

    public boolean accountExists(String accountId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return dslContext.fetchExists(
            dslContext
                .selectFrom(ACCOUNT)
                .where(ACCOUNT.TENANT_ID.eq(tenantId))
                .and(ACCOUNT.ACCOUNT_ID.eq(accountId).and(ACCOUNT.IS_DELETED.eq(false)))
        );
    }

    @Timed
    public Optional<Account> getAccount(String accountId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = dslContext.selectFrom(ACCOUNT).where(ACCOUNT.ACCOUNT_ID.eq(accountId)).and(ACCOUNT.IS_DELETED.isFalse()).fetchOne();
        return Optional.ofNullable(accountDAOMapper.recordToAccount(record));
    }

    public Optional<AccountStub> getAccountStub(String accountId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = dslContext.selectFrom(ACCOUNT).where(ACCOUNT.ACCOUNT_ID.eq(accountId)).and(ACCOUNT.IS_DELETED.isFalse()).fetchOne();
        return Optional.ofNullable(accountDAOMapper.recordToAccountStub(record));
    }

    public Optional<Account> getAccountByName(String name) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = dslContext.selectFrom(ACCOUNT).where(ACCOUNT.NAME.eq(name)).and(ACCOUNT.IS_DELETED.isFalse()).fetchOne();
        return Optional.ofNullable(accountDAOMapper.recordToAccount(record));
    }

    public Account deleteAccountTransaction(String accountId, DSLContext context) {
        var deletedRecord = context
            .update(ACCOUNT)
            .set(ACCOUNT.IS_DELETED, true)
            .set(ACCOUNT.UPDATED_ON, LocalDateTime.now(ZoneOffset.UTC))
            .where(ACCOUNT.ACCOUNT_ID.eq(accountId).and(ACCOUNT.IS_DELETED.isFalse()))
            .returning()
            .fetchOne();
        return accountDAOMapper.recordToAccount(deletedRecord);
    }

    public void updateAccount(Configuration configuration, Account account) {
        DSLContext dslContext = DSL.using(configuration);
        var updateAccount = accountDAOMapper.accountToRecord(account);
        updateAccount.setUpdatedOn(LocalDateTime.now(ZoneOffset.UTC));
        updateAccount.reset(ACCOUNT.CREATED_ON);
        updateAccount.reset(ACCOUNT.TENANT_ID);
        PostgresErrorHandler.withAnyConstraintAsConflict(
            () -> dslContext.executeUpdate(updateAccount),
            Map.of(
                UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME,
                String.format("account exists with external id: %s", account.getExternalId()),
                UNIQUE_ACCOUNT_NAME,
                String.format("account with name %s already exists", account.getName()),
                UNIQUE_CRM_ID_CONSTRAINT_NAME,
                String.format("account with CRM ID %s already exists", account.getCrmId())
            )
        );
    }

    public void ensureUniqueAccountId(String accountId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = dslContext.select().from(ACCOUNT).where(ACCOUNT.ACCOUNT_ID.eq(accountId)).fetchOneInto(AccountRecord.class);

        if (record == null) {
            return;
        }

        throwDuplicateAccountIdException(accountId);
    }

    public Optional<String> getAccountIdByCrmId(String crmId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String accountId = dslContext
            .select(ACCOUNT.ACCOUNT_ID)
            .from(ACCOUNT)
            .where(ACCOUNT.CRM_ID.eq(crmId))
            .and(ACCOUNT.IS_DELETED.isFalse())
            .fetchOneInto(String.class);
        return Optional.ofNullable(accountId);
    }

    private void throwDuplicateAccountIdException(String accountId) {
        var message = "Duplicate accountId generated. AccountId = " + accountId;
        LOGGER.info(message);
        throw new DuplicateIdException(message);
    }

    public Set<String> getAllCurrenciesUsedInAccounts() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var result = dslContext
            .selectDistinct(ACCOUNT.CURRENCY)
            .from(ACCOUNT)
            .where(ACCOUNT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT.IS_DELETED.isFalse())
            .fetch();

        return result.stream().map(Record1::value1).collect(Collectors.toSet());
    }

    public Optional<String> getAccountIdByExternalId(String externalId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String accountId = dslContext
            .select(ACCOUNT.ACCOUNT_ID)
            .from(ACCOUNT)
            .where(ACCOUNT.EXTERNAL_ID.eq(externalId))
            .and(ACCOUNT.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(ACCOUNT.IS_DELETED.isFalse())
            .fetchOneInto(String.class);
        return Optional.ofNullable(accountId);
    }

    public void markAccountsForMetricsRecalculation(DSLContext txnDslContext, String tenantId, Set<String> accountIds) {
        txnDslContext
            .update(ACCOUNT)
            .set(ACCOUNT.METRICS, StoredMetrics.RECOMPUTE_METRICS_JSONB)
            .where(ACCOUNT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT.ACCOUNT_ID.in(accountIds))
            .execute();
    }

    public List<String> getAccountIdsForMetricsUpdateLocked(DSLContext txnDslContext, String tenantId, int limit) {
        return txnDslContext
            .select(ACCOUNT.ACCOUNT_ID)
            .from(ACCOUNT)
            .where("(metrics->>'recompute')::boolean = true")
            .and(ACCOUNT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT.IS_DELETED.eq(false))
            .limit(limit)
            .forUpdate()
            .skipLocked()
            .fetch(ACCOUNT.ACCOUNT_ID);
    }

    public void updateAccountMetrics(DSLContext txnDslContext, String tenantId, String accountId, JSONB metrics) {
        txnDslContext
            .update(ACCOUNT)
            .set(ACCOUNT.METRICS, metrics)
            .where(ACCOUNT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT.ACCOUNT_ID.eq(accountId))
            .and(ACCOUNT.IS_DELETED.eq(false))
            .execute();
    }

    public void updateAccountSupportedPaymentTypes(String accountId, Set<PaymentType> supportedPaymentTypes, Configuration configuration) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = DSL.using(configuration);

        try {
            JSON serializedSupportedPaymentTypes = accountDAOMapper.serializeSupportedPaymentTypes(supportedPaymentTypes);
            dslContext
                .update(ACCOUNT)
                .set(ACCOUNT.SUPPORTED_PAYMENT_TYPES, serializedSupportedPaymentTypes)
                .where(ACCOUNT.TENANT_ID.eq(tenantId))
                .and(ACCOUNT.ACCOUNT_ID.eq(accountId))
                .and(ACCOUNT.IS_DELETED.isFalse())
                .execute();
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("failed to serialize supported payment types", e);
        }
    }

    public void setAccountErpId(String accountId, String erpId, Configuration configuration) {
        var dslContext = DSL.using(configuration);

        int rowsUpdated = dslContext
            .update(ACCOUNT)
            .set(ACCOUNT.ERP_ID, erpId)
            .where(ACCOUNT.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(ACCOUNT.ACCOUNT_ID.eq(accountId))
            .execute();

        if (rowsUpdated != 1) {
            throw new ServiceFailureException("More than 1 row updated when setting erpId for account: " + accountId);
        }
    }

    public List<Account> getAccountsForAccountIds(Collection<String> accountIds) {
        return TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider)
            .selectFrom(ACCOUNT)
            .where(ACCOUNT.ACCOUNT_ID.in(accountIds))
            .and(ACCOUNT.IS_DELETED.isFalse())
            .and(ACCOUNT.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .fetch()
            .stream()
            .map(accountDAOMapper::recordToAccount)
            .toList();
    }
}
