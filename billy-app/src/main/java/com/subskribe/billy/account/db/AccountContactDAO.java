package com.subskribe.billy.account.db;

import static com.subskribe.billy.jooq.default_schema.Tables.ACCOUNT_CONTACT;
import static com.subskribe.billy.jooq.default_schema.Tables.ACCOUNT_ORDER;

import com.subskribe.billy.account.mapper.AccountDAOMapper;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountContactRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class AccountContactDAO {

    private static final String UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME = "index_account_contact_external_id";
    private static final String UNIQUE_EMAIL_CONSTRAINT_NAME = "index_account_contact_email";
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountContactDAO.class);

    private final AccountDAOMapper accountDAOMapper;
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;

    @Inject
    public AccountContactDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        accountDAOMapper = Mappers.getMapper(AccountDAOMapper.class);
    }

    public AccountContact addContact(DSLContext dslContext, AccountContact accountContact) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        Optional<AccountContact> existingContact = getContactByEmail(accountContact.getAccountId(), accountContact.getEmail());
        if (existingContact.isPresent()) {
            return existingContact.get();
        }

        LocalDateTime localTime = LocalDateTime.now(ZoneOffset.UTC);
        accountContact.setEmail(accountContact.getEmail().toLowerCase());
        AccountContactRecord accountContactRecord = accountDAOMapper.accountContactToRecord(accountContact);
        accountContactRecord.setCreatedOn(localTime);
        accountContactRecord.setUpdatedOn(localTime);
        accountContactRecord.reset(ACCOUNT_CONTACT.ID);
        accountContactRecord.setTenantId(tenantId);

        AccountContactRecord record = PostgresErrorHandler.withAnyConstraintAsConflict(
            () -> dslContext.insertInto(ACCOUNT_CONTACT).set(accountContactRecord).returning().fetchOne(),
            getAccountContactConstraints(accountContact)
        );

        return accountDAOMapper.recordToAccountContact(record);
    }

    public boolean contactIsInUseAndHasAddress(AccountContact contact) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var contactJoinQuery = ACCOUNT_ORDER.BILLING_CONTACT_ID.eq(ACCOUNT_CONTACT.CONTACT_ID).or(
            ACCOUNT_ORDER.SHIPPING_CONTACT_ID.eq(ACCOUNT_CONTACT.CONTACT_ID)
        );
        var inUseWithAddress = dslContext
            .selectCount()
            .from(ACCOUNT_ORDER)
            .join(ACCOUNT_CONTACT)
            .on(ACCOUNT_ORDER.TENANT_ID.eq(ACCOUNT_CONTACT.TENANT_ID).and(contactJoinQuery))
            .where(ACCOUNT_CONTACT.CONTACT_ID.eq(contact.getContactId()))
            .and(ACCOUNT_CONTACT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_CONTACT.ADDRESS_ID.isNotNull())
            .and(ACCOUNT_ORDER.STATUS.eq(OrderStatus.EXECUTED.toString()))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .and(ACCOUNT_CONTACT.IS_DELETED.isFalse())
            .fetchOne(0, Integer.class);
        return inUseWithAddress != null && inUseWithAddress > 0;
    }

    public Optional<AccountContact> getContact(String contactId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var record = dslContext
            .selectFrom(ACCOUNT_CONTACT)
            .where(ACCOUNT_CONTACT.CONTACT_ID.eq(contactId))
            .and(ACCOUNT_CONTACT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_CONTACT.IS_DELETED.isFalse())
            .fetchOne();
        var accountContact = accountDAOMapper.recordToAccountContact(record);

        return Optional.ofNullable(accountContact);
    }

    public List<AccountContact> getContactsByContactIds(List<String> contacts, boolean includeDeleted) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext.selectFrom(ACCOUNT_CONTACT).where(ACCOUNT_CONTACT.CONTACT_ID.in(contacts)).and(ACCOUNT_CONTACT.TENANT_ID.eq(tenantId));
        if (!includeDeleted) {
            query = query.and(ACCOUNT_CONTACT.IS_DELETED.isFalse());
        }
        List<AccountContactRecord> records = query.fetchInto(AccountContactRecord.class);
        return accountDAOMapper.recordsToAccountContacts(records);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public Optional<AccountContact> getContactByExternalId(String contactExternalId, Optional<String> accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext
            .selectFrom(ACCOUNT_CONTACT)
            .where(ACCOUNT_CONTACT.EXTERNAL_ID.eq(contactExternalId))
            .and(ACCOUNT_CONTACT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_CONTACT.IS_DELETED.isFalse());
        if (accountId.isPresent()) {
            query = query.and(ACCOUNT_CONTACT.ACCOUNT_ID.eq(accountId.get()));
        }
        var record = query.fetchOne();
        var accountContact = accountDAOMapper.recordToAccountContact(record);
        return Optional.ofNullable(accountContact);
    }

    public Optional<AccountContact> getContactByEmail(String accountId, String email) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = dslContext
            .selectFrom(ACCOUNT_CONTACT)
            .where(ACCOUNT_CONTACT.EMAIL.equalIgnoreCase(email))
            .and(ACCOUNT_CONTACT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_CONTACT.ACCOUNT_ID.eq(accountId))
            .and(ACCOUNT_CONTACT.IS_DELETED.isFalse())
            .fetchOne();
        var accountContact = accountDAOMapper.recordToAccountContact(record);

        return Optional.ofNullable(accountContact);
    }

    public List<AccountContact> getContactsByEmails(String accountId, List<String> emails) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var records = dslContext
            .selectFrom(ACCOUNT_CONTACT)
            .where(ACCOUNT_CONTACT.EMAIL.in(emails))
            .and(ACCOUNT_CONTACT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_CONTACT.ACCOUNT_ID.eq(accountId))
            .and(ACCOUNT_CONTACT.IS_DELETED.isFalse())
            .fetchInto(AccountContactRecord.class);
        return accountDAOMapper.recordsToAccountContacts(records);
    }

    public List<AccountContact> getContactsInAccount(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var records = dslContext
            .select()
            .from(ACCOUNT_CONTACT)
            .where(ACCOUNT_CONTACT.ACCOUNT_ID.eq(accountId))
            .and(ACCOUNT_CONTACT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_CONTACT.IS_DELETED.isFalse())
            .fetchInto(AccountContactRecord.class);

        var accountContacts = accountDAOMapper.recordsToAccountContacts(records);

        return accountContacts == null ? List.of() : accountContacts;
    }

    public void updateContact(DSLContext context, AccountContact accountContact) {
        accountContact.setEmail(accountContact.getEmail().toLowerCase());
        AccountContactRecord updateContact = accountDAOMapper.accountContactToRecord(accountContact);
        updateContact.setUpdatedOn(LocalDateTime.now(ZoneOffset.UTC));
        updateContact.reset(ACCOUNT_CONTACT.CREATED_ON);
        updateContact.reset(ACCOUNT_CONTACT.TENANT_ID);

        PostgresErrorHandler.withAnyConstraintAsConflict(() -> context.executeUpdate(updateContact), getAccountContactConstraints(accountContact));
    }

    public AccountContact deleteContact(DSLContext context, String contactId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var deletedRecord = context
            .update(ACCOUNT_CONTACT)
            .set(ACCOUNT_CONTACT.IS_DELETED, true)
            .set(ACCOUNT_CONTACT.UPDATED_ON, LocalDateTime.now(ZoneOffset.UTC))
            .where(ACCOUNT_CONTACT.CONTACT_ID.eq(contactId))
            .and(ACCOUNT_CONTACT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_CONTACT.IS_DELETED.isFalse())
            .returning()
            .fetchOne();

        return accountDAOMapper.recordToAccountContact(deletedRecord);
    }

    public void ensureUniqueContactId(String contactId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = dslContext
            .select()
            .from(ACCOUNT_CONTACT)
            .where(ACCOUNT_CONTACT.CONTACT_ID.eq(contactId))
            .and(ACCOUNT_CONTACT.TENANT_ID.eq(tenantId))
            .fetchOneInto(AccountContactRecord.class);

        if (record == null) {
            return;
        }

        throwDuplicateContactIdException(contactId);
    }

    private void throwDuplicateContactIdException(String contactId) {
        var message = "Duplicate contact generated. ContactId = " + contactId;
        LOGGER.info(message);
        throw new DuplicateIdException(message);
    }

    private Map<String, String> getAccountContactConstraints(AccountContact accountContact) {
        return Map.of(
            UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME,
            String.format("contact exists with external id: %s", accountContact.getExternalId()),
            UNIQUE_EMAIL_CONSTRAINT_NAME,
            String.format("contact exists with email: %s", accountContact.getEmail())
        );
    }
}
