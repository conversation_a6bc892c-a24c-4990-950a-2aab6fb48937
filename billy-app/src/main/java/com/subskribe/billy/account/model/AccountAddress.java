package com.subskribe.billy.account.model;

import com.subskribe.billy.shared.traits.Sanitized;
import java.time.Instant;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;

public class AccountAddress implements Sanitized {

    private static final int MAX_ZIP_CODE_LENGTH = 10;

    private static final Set<String> VALID_COUNTRY_CODES = Locale.getISOCountries(Locale.IsoCountryCode.PART1_ALPHA2);

    private UUID id;

    private String addressId;

    private String streetAddressLine1;

    private String streetAddressLine2;

    private String city;

    private String state;

    private String country;

    private String zipcode;

    private Instant createdOn;

    private Instant updatedOn;

    public AccountAddress() {}

    public AccountAddress(
        UUID id,
        String addressId,
        String streetAddressLine1,
        String streetAddressLine2,
        String city,
        String state,
        String country,
        String zipcode,
        Instant createdOn,
        Instant updatedOn
    ) {
        this.id = id;
        this.addressId = addressId;
        this.streetAddressLine1 = streetAddressLine1;
        this.streetAddressLine2 = streetAddressLine2;
        this.city = city;
        this.state = state;
        this.country = country;
        this.zipcode = zipcode;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getAddressId() {
        return addressId;
    }

    public void setAddressId(String addressId) {
        this.addressId = addressId;
    }

    public String getStreetAddressLine1() {
        return streetAddressLine1;
    }

    public void setStreetAddressLine1(String streetAddressLine1) {
        this.streetAddressLine1 = streetAddressLine1;
    }

    public String getStreetAddressLine2() {
        return streetAddressLine2;
    }

    public void setStreetAddressLine2(String streetAddressLine2) {
        this.streetAddressLine2 = streetAddressLine2;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    public Instant getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Instant createdOn) {
        this.createdOn = createdOn;
    }

    public Instant getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Instant updatedOn) {
        this.updatedOn = updatedOn;
    }

    public void validate(String contactId) {
        if (StringUtils.isBlank(streetAddressLine1) || StringUtils.isBlank(city) || StringUtils.isBlank(country) || StringUtils.isBlank(zipcode)) {
            String errorMessage = String.format(
                "The contact with id: %s is (or will be) linked to an executed order, so a complete address (Street Address 1, City, Country," +
                " and Zipcode) is required. Please provide all required details before proceeding.",
                contactId
            );
            throw new IllegalArgumentException(errorMessage);
        }

        if ("US".equals(country) && StringUtils.isBlank(state)) {
            throw new IllegalArgumentException("state is required for US address");
        }

        validateHardConstraints();
    }

    public void validateHardConstraints() {
        if (StringUtils.isNotBlank(zipcode) && zipcode.length() > MAX_ZIP_CODE_LENGTH) {
            throw new IllegalArgumentException(String.format("zipcode cannot be more than %d characters", MAX_ZIP_CODE_LENGTH));
        }

        if (StringUtils.isNotBlank(country) && !VALID_COUNTRY_CODES.contains(country)) {
            throw new IllegalArgumentException("ISO 3166 alpha-2 country code expected. e.g. \"GB\"");
        }
    }

    // note, this is not an object equality comparison. e.g. if Id's are different this will still return true.
    public boolean isSameAddress(AccountAddress address) {
        return (
            stringsEqual(streetAddressLine1, address.getStreetAddressLine1()) &&
            stringsEqual(streetAddressLine2, address.getStreetAddressLine2()) &&
            stringsEqual(city, address.getCity()) &&
            stringsEqual(state, address.getState()) &&
            stringsEqual(country, address.getCountry()) &&
            stringsEqual(zipcode, address.getZipcode())
        );
    }

    private boolean stringsEqual(String s1, String s2) {
        return (StringUtils.isEmpty(s1) && StringUtils.isEmpty(s2)) || StringUtils.equalsIgnoreCase(s1, s2);
    }

    @Override
    public void sanitize() {
        streetAddressLine1 = safeTrim(streetAddressLine1);
        streetAddressLine2 = safeTrim(streetAddressLine2);
        city = safeTrim(city);
        state = safeTrim(state);
        country = safeTrim(country);
        country = safeToUpperCase(country);
        zipcode = safeTrim(zipcode);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AccountAddress that = (AccountAddress) o;
        return (
            Objects.equals(id, that.id) &&
            Objects.equals(addressId, that.addressId) &&
            Objects.equals(streetAddressLine1, that.streetAddressLine1) &&
            Objects.equals(streetAddressLine2, that.streetAddressLine2) &&
            Objects.equals(city, that.city) &&
            Objects.equals(state, that.state) &&
            Objects.equals(country, that.country) &&
            Objects.equals(zipcode, that.zipcode) &&
            Objects.equals(createdOn, that.createdOn) &&
            Objects.equals(updatedOn, that.updatedOn)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, addressId, streetAddressLine1, streetAddressLine2, city, state, country, zipcode, createdOn, updatedOn);
    }

    @Override
    public String toString() {
        return (
            "AccountAddress{" +
            "id=" +
            id +
            ", addressId='" +
            addressId +
            '\'' +
            ", streetAddressLine1='" +
            streetAddressLine1 +
            '\'' +
            ", streetAddressLine2='" +
            streetAddressLine2 +
            '\'' +
            ", city='" +
            city +
            '\'' +
            ", state='" +
            state +
            '\'' +
            ", country='" +
            country +
            '\'' +
            ", zipcode='" +
            zipcode +
            '\'' +
            ", createdOn=" +
            createdOn +
            ", updatedOn=" +
            updatedOn +
            '}'
        );
    }
}
