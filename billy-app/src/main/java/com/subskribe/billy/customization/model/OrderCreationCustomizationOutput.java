package com.subskribe.billy.customization.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import java.util.List;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
@JsonSerialize(as = ImmutableOrderCreationCustomizationOutput.class)
@JsonDeserialize(as = ImmutableOrderCreationCustomizationOutput.class)
public interface OrderCreationCustomizationOutput {
    @Value.Default
    default Boolean getCustomizationRunSkipped() {
        return Boolean.FALSE;
    }

    @Value.Default
    default Boolean getCustomizationDefinitionMissing() {
        return Boolean.TRUE;
    }

    List<RuleTrace> getRuleTraces();

    static OrderCreationCustomizationOutput skipped() {
        return ImmutableOrderCreationCustomizationOutput.builder().customizationRunSkipped(true).build();
    }
}
