package com.subskribe.billy.customization.service;

import static com.subskribe.billy.customization.CustomizationProductMetadata.ORDER_CREATION_CUSTOMIZATION;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldProxy;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.customization.SelectionCustomizationInput;
import com.subskribe.billy.customization.db.CustomizationDefinitionDAO;
import com.subskribe.billy.customization.model.Action;
import com.subskribe.billy.customization.model.CustomizationActionType;
import com.subskribe.billy.customization.model.CustomizationContext;
import com.subskribe.billy.customization.model.CustomizationDefinition;
import com.subskribe.billy.customization.model.CustomizationDefinitionInput;
import com.subskribe.billy.customization.model.ImmutableCustomizationDefinitionInput;
import com.subskribe.billy.customization.model.ImmutableOrderCreationCustomizationInput;
import com.subskribe.billy.customization.model.OrderCreationCustomizationInput;
import com.subskribe.billy.customization.model.PlanAdditionCustomizationInput;
import com.subskribe.billy.discount.model.Discount;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.paymentterms.services.PaymentTermSettingsService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.opportunity.OpportunityMapper;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.pecuniary.TenantDiscount;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.template.model.DocumentTemplateStatus;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.zeppa.customizations.bindings.AccountBinding;
import com.subskribe.zeppa.customizations.bindings.ChargeBinding;
import com.subskribe.zeppa.customizations.bindings.ContactBinding;
import com.subskribe.zeppa.customizations.bindings.OpportunityBinding;
import com.subskribe.zeppa.customizations.bindings.OrderBinding;
import com.subskribe.zeppa.customizations.bindings.PlanBinding;
import com.subskribe.zeppa.customizations.bindings.ProductBinding;
import com.subskribe.zeppa.customizations.bindings.UserBinding;
import com.subskribe.zeppa.customizations.core.CustomizationAction;
import com.subskribe.zeppa.customizations.core.RuleAction;
import com.subskribe.zeppa.customizations.core.RuleProcessed;
import com.subskribe.zeppa.customizations.core.ZeppaDriver;
import com.subskribe.zeppa.customizations.ordercreation.LineItemDiscount;
import com.subskribe.zeppa.customizations.ordercreation.LineItemPredefinedDiscount;
import com.subskribe.zeppa.customizations.ordercreation.LineItemPriceAttribute;
import com.subskribe.zeppa.customizations.ordercreation.LineItemPriceOverride;
import com.subskribe.zeppa.customizations.ordercreation.LineItemQuantity;
import com.subskribe.zeppa.customizations.ordercreation.LineItemRenewalUplift;
import com.subskribe.zeppa.customizations.ordercreation.OrderLineCustomField;
import com.subskribe.zeppa.customizations.selection.SelectedAttributes;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class CustomizationService {

    public static final String PRODUCT_CATEGORY = "product_category";

    public static final Set<ActionType> ACTIONS_PRICE_ATTRIBUTE_ALLOWED = Set.of(ActionType.ADD, ActionType.RENEWAL, ActionType.RESTRUCTURE);

    private static final Set<ActionType> ACTIONS_PREDEFINED_DISCOUNT_ALLOWED = Set.of(ActionType.ADD, ActionType.RESTRUCTURE, ActionType.RENEWAL);

    private static final Set<ActionType> ACTIONS_RAMP_OVERRIDE_ALLOWED = Set.of(ActionType.ADD, ActionType.RESTRUCTURE, ActionType.RENEWAL);

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomizationService.class);

    private static final BigDecimal ONE_HUNDRED = new BigDecimal(100);

    private final FeatureService featureService;

    private final AccountGetService accountGetService;

    private final CustomFieldProxy customFieldProxy;

    private final OrderGetService orderGetService;

    private final ProductCatalogGetService catalogGetService;

    private final DocumentTemplateGetService templateGetService;

    private final DiscountService discountService;

    private final CustomizationDefinitionDAO customizationDefinitionDAO;

    private final TenantSettingService tenantSettingService;

    private final UserService userService;

    private final RateCardService rateCardService;

    private final PaymentTermSettingsService paymentTermsService;

    private final OpportunityGetService opportunityGetService;

    private final CustomFieldService customFieldService;

    private final OpportunityMapper opportunityMapper;

    @Inject
    public CustomizationService(
        FeatureService featureService,
        AccountGetService accountGetService,
        CustomFieldProxy customFieldProxy,
        OrderGetService orderGetService,
        ProductCatalogGetService catalogGetService,
        DocumentTemplateGetService templateGetService,
        DiscountService discountService,
        CustomizationDefinitionDAO customizationDefinitionDAO,
        TenantSettingService tenantSettingService,
        UserService userService,
        RateCardService rateCardService,
        PaymentTermSettingsService paymentTermsService,
        OpportunityGetService opportunityGetService,
        CustomFieldService customFieldService
    ) {
        this.featureService = featureService;
        this.accountGetService = accountGetService;
        this.customFieldProxy = customFieldProxy;
        this.orderGetService = orderGetService;
        this.catalogGetService = catalogGetService;
        this.templateGetService = templateGetService;
        this.discountService = discountService;
        this.customizationDefinitionDAO = customizationDefinitionDAO;
        this.tenantSettingService = tenantSettingService;
        this.userService = userService;
        this.rateCardService = rateCardService;
        this.paymentTermsService = paymentTermsService;
        this.opportunityGetService = opportunityGetService;
        this.customFieldService = customFieldService;
        opportunityMapper = Mappers.getMapper(OpportunityMapper.class);
    }

    public List<CustomFieldEntry> runOrderLineItemCustomFieldCustomization(
        CustomFieldParentType type,
        String orderLineItemId,
        List<CustomFieldEntry> entries
    ) {
        if (!featureService.isEnabled(Feature.ORDER_LINE_ITEM_CUSTOM_FIELDS_CUSTOMIZATION) || type != CustomFieldParentType.ORDER_ITEM) {
            return entries;
        }

        Optional<OrderLineItem> orderLineItem = orderGetService.getOrderLineItemByOrderLineItemId(orderLineItemId);

        if (orderLineItem.isEmpty()) {
            return entries;
        }

        String planId = orderLineItem.get().getPlanId();
        String chargeId = orderLineItem.get().getChargeId();

        return runOrderLineItemCustomFieldCustomization(planId, chargeId, entries);
    }

    // filter available order line item custom fields based on plan and then charge custom field selections
    // example: line item custom field has options 1, 2, 3, 4. Plan has selections 1, 2, 3, charge has selections 1, 2
    // after filtering line item custom fields only has options 1 and 2 available
    public List<CustomFieldEntry> runOrderLineItemCustomFieldCustomization(
        String planId,
        String chargeId,
        List<CustomFieldEntry> orderLineCustomFieldEntries
    ) {
        if (!featureService.isEnabled(Feature.ORDER_LINE_ITEM_CUSTOM_FIELDS_CUSTOMIZATION)) {
            return orderLineCustomFieldEntries;
        }

        List<CustomFieldEntry> planFilteredEntries;
        if (StringUtils.isBlank(planId)) {
            planFilteredEntries = orderLineCustomFieldEntries;
        } else {
            CustomField planCustomField = customFieldProxy.getCustomFields(CustomFieldParentType.PLAN, planId);
            planFilteredEntries = filterOrderLineCustomFieldsOptions(orderLineCustomFieldEntries, planCustomField);
        }

        if (StringUtils.isBlank(chargeId)) {
            return planFilteredEntries;
        }

        CustomField chargeCustomField = customFieldProxy.getCustomFields(CustomFieldParentType.CHARGE, chargeId);
        return filterOrderLineCustomFieldsOptions(planFilteredEntries, chargeCustomField);
    }

    private List<CustomFieldEntry> filterOrderLineCustomFieldsOptions(List<CustomFieldEntry> customFieldEntries, CustomField filteringCustomField) {
        List<CustomFieldEntry> filteredCustomFieldEntries = new ArrayList<>();

        customFieldEntries.forEach(orderLineCustomFieldEntry ->
            filteringCustomField
                .getEntries()
                .values()
                .stream()
                .filter(value -> value.getName().equals(orderLineCustomFieldEntry.getName()))
                .findFirst()
                .ifPresentOrElse(
                    value -> {
                        CustomFieldEntry filteredEntry = filterOrderLineCustomFieldOptions(orderLineCustomFieldEntry, value);
                        filteredCustomFieldEntries.add(filteredEntry);
                    },
                    () -> filteredCustomFieldEntries.add(orderLineCustomFieldEntry)
                )
        );

        return filteredCustomFieldEntries;
    }

    // filter custom field options and selections list by a whitelist of options allowed
    private static CustomFieldEntry filterOrderLineCustomFieldOptions(CustomFieldEntry orderLineCustomFieldEntry, CustomFieldValue value) {
        Set<String> filteringFieldValues = new HashSet<>(value.getSelections());
        List<String> filteredOptions = orderLineCustomFieldEntry.getOptions().stream().filter(filteringFieldValues::contains).toList();
        List<String> filteredSelections = orderLineCustomFieldEntry.getSelections().stream().filter(filteringFieldValues::contains).toList();

        return new CustomFieldEntry(
            orderLineCustomFieldEntry.getId(),
            orderLineCustomFieldEntry.getType(),
            orderLineCustomFieldEntry.getName(),
            orderLineCustomFieldEntry.getLabel(),
            orderLineCustomFieldEntry.getValue(),
            filteredSelections,
            filteredOptions,
            orderLineCustomFieldEntry.isRequired(),
            orderLineCustomFieldEntry.getSource(),
            orderLineCustomFieldEntry.getDefaultValue()
        );
    }

    public void compileSelectionCustomization(String accountId, String userId, String crmOpportunityId, String zeppaScript) {
        Optional<Opportunity> opportunity = StringUtils.isNotBlank(crmOpportunityId)
            ? opportunityGetService.getOptionalOpportunityByCrmOpportunityId(crmOpportunityId)
            : Optional.empty();
        compileSelectionCustomization(accountId, userId, opportunity.orElse(null), zeppaScript);
    }

    public void compileSelectionCustomization(String accountId, String userId, Opportunity opportunity, String zeppaScript) {
        Account account = accountGetService.getAccount(accountId);
        Optional<AccountAddress> accountAddress = accountGetService.getAccountAddress(account.getAddressId());
        AccountBinding accountBinding = AccountBinding.from(account, accountAddress.orElse(null), account.getCustomFields());
        UserBinding userBinding = getUserBinding(userId);
        OpportunityBinding opportunityBinding = getOpportunityBinding(opportunity);
        ZeppaDriver.compileSelectionCustomization(accountBinding, userBinding, opportunityBinding, zeppaScript);
    }

    public void compileOrderCustomization(String orderId, String userId, String zeppaScript) {
        OrderBinding orderBinding = orderBindingFromId(orderId);
        UserBinding userBinding = getUserBinding(userId);
        ZeppaDriver.compileOrderCreationCustomization(orderBinding, zeppaScript, userBinding);
    }

    public List<RuleProcessed> fireSelectionCustomization(String accountId, String userId, String crmOpportunityId, String zeppaScript) {
        Optional<Opportunity> opportunity = StringUtils.isNotBlank(crmOpportunityId)
            ? opportunityGetService.getOptionalOpportunityByCrmOpportunityId(crmOpportunityId)
            : Optional.empty();
        return fireSelectionCustomization(accountId, userId, opportunity.orElse(null), zeppaScript);
    }

    public List<RuleProcessed> fireSelectionCustomization(String accountId, String userId, Opportunity opportunity, String zeppaScript) {
        Account account = accountGetService.getAccount(accountId);
        Optional<AccountAddress> accountAddress = accountGetService.getAccountAddress(account.getAddressId());
        AccountBinding accountBinding = AccountBinding.from(account, accountAddress.orElse(null), account.getCustomFields());
        UserBinding userBinding = getUserBinding(userId);
        OpportunityBinding opportunityBinding = getOpportunityBinding(opportunity);
        return ZeppaDriver.fireSelectionCustomization(accountBinding, userBinding, opportunityBinding, zeppaScript);
    }

    public List<RuleProcessed> fireOrderCreationCustomization(String orderId, String userId, String zeppaScript) {
        OrderBinding orderBinding = orderBindingFromId(orderId);
        return ZeppaDriver.fireOrderCreationCustomization(orderBinding, zeppaScript, getUserBinding(userId));
    }

    private OrderBinding orderBindingFromId(String orderId) {
        OrderCreationCustomizationInput input = getOrderCreationCustomizationInput(orderId);
        Account account = input.getAccount();
        AccountBinding accountBinding = AccountBinding.from(input.getAccount(), input.getAccountAddress().orElse(null), account.getCustomFields());
        CustomField shippingAccountCustomFields = input.getBillingContactAccount().map(Account::getCustomFields).orElse(CustomField.empty());
        // TODO: populate shipping account address
        AccountBinding shippingAccountBinding = AccountBinding.from(input.getBillingContactAccount().orElse(null), null, shippingAccountCustomFields);
        return OrderBinding.from(
            input.getOrder(),
            accountBinding,
            getPlanBindings(input),
            input.getTimeZoneId(),
            ContactBinding.from(input.getShippingContact().orElse(null), accountBinding),
            ContactBinding.from(input.getBillingContact().orElse(null), shippingAccountBinding)
        );
    }

    private UserBinding getUserBinding(String userId) {
        if (StringUtils.isNotBlank(userId)) {
            return userService.getUserOptional(userId).map(UserBinding::fromUser).orElse(UserBinding.empty());
        }
        return UserBinding.empty();
    }

    private OpportunityBinding getOpportunityBinding(Opportunity opportunity) {
        return Optional.ofNullable(opportunity).map(OpportunityBinding::from).orElse(OpportunityBinding.empty());
    }

    private OrderCreationCustomizationInput getOrderCreationCustomizationInput(String orderId) {
        ImmutableOrderCreationCustomizationInput.Builder inputBuilder = ImmutableOrderCreationCustomizationInput.builder();
        Order order = orderGetService.getOrderByOrderId(orderId);
        Account account = accountGetService.getAccount(order.getAccountId());
        Optional<AccountAddress> accountAddress = accountGetService.getAccountAddress(account.getAddressId());
        List<String> planIds = order.getLineItems().stream().map(OrderLineItem::getPlanId).distinct().toList();

        if (StringUtils.isNotBlank(order.getShippingContactId())) {
            inputBuilder.shippingContact(accountGetService.getContact(order.getShippingContactId()));
        }

        if (StringUtils.isNotBlank(order.getBillingContactId())) {
            AccountContact billingContact = accountGetService.getContact(order.getBillingContactId());
            inputBuilder.billingContact(billingContact);

            if (!billingContact.getAccountId().equals(account.getAccountId())) {
                inputBuilder.billingContactAccount(accountGetService.getAccount(billingContact.getAccountId()));
            } else {
                inputBuilder.billingContactAccount(account);
            }
        }

        Map<String, Plan> planMap = catalogGetService
            .getPlansByPlanIds(planIds)
            .stream()
            .collect(Collectors.toMap(Plan::getPlanId, Function.identity()));
        Map<String, Charge> chargeMap = planMap
            .values()
            .stream()
            .map(Plan::getCharges)
            .flatMap(Collection::stream)
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        Map<String, Product> productMap = planMap
            .values()
            .stream()
            .map(Plan::getProductId)
            .distinct()
            .map(catalogGetService::getProduct)
            .collect(Collectors.toMap(Product::getProductId, Function.identity()));

        Map<String, CustomField> planCustomFieldMap = planMap
            .keySet()
            .stream()
            .collect(Collectors.toMap(Function.identity(), planId -> customFieldProxy.getCustomFields(CustomFieldParentType.PLAN, planId)));
        Map<String, CustomField> chargeCustomFieldMap = chargeMap
            .keySet()
            .stream()
            .collect(Collectors.toMap(Function.identity(), chargeId -> customFieldProxy.getCustomFields(CustomFieldParentType.CHARGE, chargeId)));

        return inputBuilder
            .order(order)
            .account(account)
            .accountAddress(accountAddress)
            .planMap(planMap)
            .productMap(productMap)
            .chargeMap(chargeMap)
            .planCustomFields(planCustomFieldMap)
            .chargeCustomFields(chargeCustomFieldMap)
            .timeZoneId(tenantSettingService.getTenantSetting().getDefaultTimeZone().toZoneId())
            .build();
    }

    public Optional<CustomizationDefinition> getCurrentSelectionCustomization() {
        return customizationDefinitionDAO.getLatestCustomizationDefinition(CustomizationContext.SELECTION_CUSTOMIZATION);
    }

    public Optional<CustomizationDefinition> getCurrentOrderCreationCustomization() {
        return customizationDefinitionDAO.getLatestCustomizationDefinition(CustomizationContext.ORDER_CREATION_CUSTOMIZATION);
    }

    public CustomizationDefinition upsertSelectionCustomization(
        String accountId,
        String userId,
        String crmOpportunityId,
        String zeppaScript,
        int expectedVersion
    ) {
        // we have to test fire successfully before adding customization
        fireSelectionCustomization(accountId, userId, crmOpportunityId, zeppaScript);

        CustomizationDefinitionInput input = ImmutableCustomizationDefinitionInput.builder()
            .customizationContext(CustomizationContext.SELECTION_CUSTOMIZATION)
            .zeppaScript(zeppaScript)
            .build();

        String principalName = CurrentUserProvider.provideAuthPrincipalName();
        return customizationDefinitionDAO.upsertCustomizationDefinition(input, expectedVersion, principalName);
    }

    public CustomizationDefinition upsertOrderCreationCustomization(String orderId, String userId, String zeppaScript, int expectedVersion) {
        // we have to test fire successfully before adding customization
        fireOrderCreationCustomization(orderId, userId, zeppaScript);

        CustomizationDefinitionInput input = ImmutableCustomizationDefinitionInput.builder()
            .customizationContext(CustomizationContext.ORDER_CREATION_CUSTOMIZATION)
            .zeppaScript(zeppaScript)
            .build();

        String principalName = CurrentUserProvider.provideAuthPrincipalName();
        return customizationDefinitionDAO.upsertCustomizationDefinition(input, expectedVersion, principalName);
    }

    public void runOrderCreationCustomization(OrderCreationCustomizationInput orderCreationCustomizationInput) {
        Optional<CustomizationDefinition> customizationDefinitionOptional = getCurrentOrderCreationCustomization();

        if (customizationDefinitionOptional.isEmpty()) {
            return;
        }

        CustomizationDefinition customizationDefinition = customizationDefinitionOptional.get();
        Order order = orderCreationCustomizationInput.getOrder();
        Account orderAccount = orderCreationCustomizationInput.getAccount();

        CustomField shippingAccountCustomFields = orderCreationCustomizationInput
            .getBillingContactAccount()
            .map(Account::getCustomFields)
            .orElse(CustomField.empty());
        // TODO: populate shipping account address
        AccountBinding shippingAccountBinding = AccountBinding.from(
            orderCreationCustomizationInput.getBillingContactAccount().orElse(null),
            null,
            shippingAccountCustomFields
        );

        Optional<AccountAddress> accountAddressOptional = orderCreationCustomizationInput.getAccountAddress();
        List<PlanBinding> planBindings = getPlanBindings(orderCreationCustomizationInput);
        AccountBinding accountBinding = AccountBinding.from(orderAccount, accountAddressOptional.orElse(null), orderAccount.getCustomFields());
        OrderBinding orderBinding = OrderBinding.from(
            order,
            accountBinding,
            planBindings,
            orderCreationCustomizationInput.getTimeZoneId(),
            ContactBinding.from(
                orderCreationCustomizationInput.getShippingContact().orElse(null),
                orderCreationCustomizationInput.getShippingContact().map(c -> accountBinding).orElse(null)
            ),
            ContactBinding.from(
                orderCreationCustomizationInput.getBillingContact().orElse(null),
                orderCreationCustomizationInput.getBillingContact().map(c -> shippingAccountBinding).orElse(null)
            )
        );
        UserBinding userBinding = orderCreationCustomizationInput.getCurrentUser().map(UserBinding::fromUser).orElseGet(UserBinding::empty);
        List<RuleProcessed> rulesProcessed = ZeppaDriver.fireOrderCreationCustomization(
            orderBinding,
            customizationDefinition.getZeppaScript(),
            userBinding
        );

        // rule actions are run in the order in which they are declared
        // of-course only rules that actually fired are picked up
        List<RuleProcessed> rulesFired = rulesProcessed
            .stream()
            .filter(ruleProcessed -> {
                LOGGER.info("Ran rule {} which fired: {}", ruleProcessed.getRuleName(), ruleProcessed.getFired());
                return ruleProcessed.isFired();
            })
            .toList();

        // if there are inverse actions we need to call them as well
        List<RuleProcessed> inverseRulesFired = rulesProcessed
            .stream()
            .filter(ruleProcessed -> !ruleProcessed.getFired())
            .filter(ruleProcessed -> CollectionUtils.isNotEmpty(ruleProcessed.getActions()))
            .peek(ruleProcessed ->
                LOGGER.info("Rule {} has inverse actions {} that needs to be processed", ruleProcessed.getRuleName(), ruleProcessed.getActions())
            )
            .toList();

        Map<String, OrderLineItem> orderLineItemMap = order
            .getLineItemsNetEffect()
            .stream()
            .collect(Collectors.toMap(line -> line.getId().toString(), Function.identity()));

        List<CustomFieldDefinition> orderFieldDefinitions = customFieldService.getCustomFieldDefinitions(CustomFieldParentType.ORDER);
        List<CustomFieldDefinition> orderLineFieldDefinitions = customFieldService.getCustomFieldDefinitions(CustomFieldParentType.ORDER_ITEM);

        // first fire all the positive rules
        rulesFired.forEach(rule ->
            rule
                .getActions()
                .forEach(ruleAction ->
                    processRuleAction(
                        orderCreationCustomizationInput,
                        orderLineItemMap,
                        rule,
                        ruleAction,
                        orderFieldDefinitions,
                        orderLineFieldDefinitions
                    )
                )
        );

        // then fire all the negative rules
        inverseRulesFired.forEach(rule ->
            rule
                .getActions()
                .forEach(ruleAction ->
                    processRuleAction(
                        orderCreationCustomizationInput,
                        orderLineItemMap,
                        rule,
                        ruleAction,
                        orderFieldDefinitions,
                        orderLineFieldDefinitions
                    )
                )
        );
        // TODO: here return all the rules that were fired to the caller for context
    }

    @Deprecated // use runSelectionCustomization instead
    public List<Action> runPlanAdditionCustomization(PlanAdditionCustomizationInput customizationInput) {
        if (!featureService.isEnabled(Feature.PLAN_ADDITION_CUSTOMIZATION) || StringUtils.isBlank(customizationInput.accountId())) {
            return List.of();
        }

        SelectionCustomizationInput selectionCustomizationInput = new SelectionCustomizationInput(
            customizationInput.accountId(),
            /* since this is deprecated I am always passing EMPTY here, no point changing the contract */StringUtils.EMPTY,
            /* since this is deprecated I am always passing null here, no point changing the contract */null
        );

        return runSelectionCustomization(selectionCustomizationInput);
    }

    public List<Action> runSelectionCustomization(SelectionCustomizationInput customizationInput) {
        if (StringUtils.isBlank(customizationInput.accountId())) {
            return List.of();
        }

        Optional<CustomizationDefinition> definitionOptional = getCurrentSelectionCustomization();
        if (definitionOptional.isEmpty()) {
            return List.of();
        }

        String userId = CurrentUserProvider.getSubskribeUserId().orElse(null);
        CustomizationDefinition definition = definitionOptional.get();
        List<RuleProcessed> rulesProcessed = fireCustomization(customizationInput, userId, definition);
        rulesProcessed.forEach(ruleProcessed -> LOGGER.info("Rule {} fired: {}", ruleProcessed.getRuleName(), ruleProcessed.getFired()));
        List<RuleProcessed> rulesFired = rulesProcessed.stream().filter(RuleProcessed::isFired).toList();
        List<Action> actions = new ArrayList<>();
        rulesFired.forEach(fired -> {
            List<Map<String, String>> addPlanSelectionFilters = getSelectionFilters(CustomizationAction.ADD_PLAN_SELECTION_FILTER, fired);
            if (CollectionUtils.isNotEmpty(addPlanSelectionFilters)) {
                actions.add(makeSelectionUiAction(CustomizationActionType.ADD_PLAN_APPLY_FILTER, addPlanSelectionFilters, fired.getRuleName()));
            }

            List<Map<String, String>> attributeSelectionFilters = getSelectionFilters(CustomizationAction.ATTRIBUTE_SELECTION_FILTER, fired);
            if (CollectionUtils.isNotEmpty(attributeSelectionFilters)) {
                actions.add(
                    makeSelectionUiAction(CustomizationActionType.ATTRIBUTE_SELECTION_APPLY_FILTER, attributeSelectionFilters, fired.getRuleName())
                );
            }
        });
        return Collections.unmodifiableList(actions);
    }

    private List<RuleProcessed> fireCustomization(SelectionCustomizationInput customizationInput, String userId, CustomizationDefinition definition) {
        // prefer opportunity object in the request over the crm opportunity id
        if (customizationInput.opportunity() != null) {
            return fireSelectionCustomization(
                customizationInput.accountId(),
                userId,
                opportunityMapper.inputToOpportunity(customizationInput.opportunity()),
                definition.getZeppaScript()
            );
        }

        return fireSelectionCustomization(customizationInput.accountId(), userId, customizationInput.crmOpportunityId(), definition.getZeppaScript());
    }

    private List<Map<String, String>> getSelectionFilters(CustomizationAction customizationAction, RuleProcessed fired) {
        return fired
            .getActions()
            .stream()
            .filter(action -> customizationAction == action.getAction() && action.getMetadata() instanceof SelectedAttributes)
            .map(ruleAction -> ((SelectedAttributes) ruleAction.getMetadata()).getAttributeMap().entrySet())
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(Collection::stream)
            .map(entry -> createFilterItem(entry.getKey(), entry.getValue()))
            .toList();
    }

    private static Action makeSelectionUiAction(CustomizationActionType actionType, List<Map<String, String>> itemList, String ruleName) {
        Map<String, Object> actionMetadata = new HashMap<>() {
            {
                put(
                    "defaultFilterModel",
                    new HashMap<>() {
                        {
                            put("logicOperator", "and");
                            put("items", itemList);
                        }
                    }
                );
            }
        };
        try {
            String actionMetadataString = JacksonProvider.defaultMapper().writeValueAsString(actionMetadata);
            return new Action(actionType, Base64.encodeBase64String(actionMetadataString.getBytes(StandardCharsets.UTF_8)), ruleName);
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Customization failed in unexpected way", e);
        }
    }

    public Map<String, String> createFilterItem(String field, String value) {
        return Map.ofEntries(Map.entry("field", field), Map.entry("value", value), Map.entry("operator", "is"));
    }

    private void processRuleAction(
        OrderCreationCustomizationInput input,
        Map<String, OrderLineItem> orderLineItemMap,
        RuleProcessed rule,
        RuleAction ruleAction,
        List<CustomFieldDefinition> orderFieldDefinitions,
        List<CustomFieldDefinition> orderLineFieldDefinitions
    ) {
        Object metadata = ruleAction.getMetadata();
        Order order = input.getOrder();

        switch (ruleAction.getAction()) {
            case FAIL_ORDER_CREATION -> failOrderCreation(rule, ruleAction);
            case FAIL_ORDER_CREATION_ON_SAVE -> input
                .isDryRun()
                .ifPresent(isDryRun -> {
                    // we call this failure into action only if dryrun is false
                    if (BooleanUtils.isFalse(isDryRun)) {
                        failOrderCreation(rule, ruleAction);
                    }
                });
            case APPLY_LINE_ITEM_DISCOUNT -> runApplyLineItemDiscountAction(orderLineItemMap, rule, metadata);
            case APPLY_LINE_ITEM_QUANTITY, FORCE_LINE_ITEM_QUANTITY -> runSetLineItemQuantityAction(
                orderLineItemMap,
                metadata,
                ruleAction,
                input.getChargeMap()
            );
            case APPLY_LINE_ITEM_PRICE_OVERRIDE, FORCE_LINE_ITEM_PRICE_OVERRIDE -> runPriceOverrideAction(
                orderLineItemMap,
                ruleAction,
                input.getChargeMap()
            );
            case FORCE_UPLIFT_ON_RAMPS -> forceRampPriceUplifts(order, ruleAction, input.getChargeMap());
            case APPLY_LINE_ITEM_PRICE_ATTRIBUTE, FORCE_LINE_ITEM_PRICE_ATTRIBUTE -> runPriceAttributeSelectionAction(
                orderLineItemMap,
                ruleAction,
                input.getChargeMap()
            );
            case APPLY_LINE_ITEM_LIST_PRICE, FORCE_LINE_ITEM_LIST_PRICE -> runSetListPriceAction(orderLineItemMap, ruleAction, input.getChargeMap());
            case APPLY_ORDER_CUSTOM_FIELD, FORCE_ORDER_CUSTOM_FIELD -> runSetOrderCustomFieldAction(
                order,
                metadata,
                orderFieldDefinitions,
                ruleAction
            );
            case APPLY_ORDER_LINE_CUSTOM_FIELD, FORCE_ORDER_LINE_CUSTOM_FIELD -> runSetOrderLineCustomFieldAction(
                orderLineItemMap,
                metadata,
                orderLineFieldDefinitions,
                ruleAction
            );
            case FORCE_START_DATE_TO_END_DATE -> runForceStartDateToEndDate(orderLineItemMap, metadata);
            case REMOVE_LINE_ITEM_PREDEFINED_DISCOUNT -> runRemoveLinePredefinedDiscountAction(orderLineItemMap, metadata);
            case ADD_PREDEFINED_DISCOUNT -> runAddPredefinedDiscountAction(order, orderLineItemMap, metadata);
            case REMOVE_PREDEFINED_DISCOUNT -> runRemovePredefinedDiscountAction(order, orderLineItemMap, metadata);
            case FORCE_DOCUMENT_TEMPLATE -> runForceDocumentTemplateAction(order, metadata);
            case REMOVE_DOCUMENT_TEMPLATE -> runRemoveDocumentTemplateAction(order, metadata);
            case ADD_PREDEFINED_TERM -> runAddPredefinedTermAction(order, metadata);
            case REMOVE_PREDEFINED_TERM -> runRemovePredefinedTermAction(order, metadata);
            case FORCE_PAYMENT_TERM -> runForcePaymentTermAction(order, metadata);
            case FORCE_BILLING_CYCLE -> runForceBillingCycleAction(order, metadata);
            case APPLY_RENEWAL_UPLIFT, FORCE_RENEWAL_UPLIFT -> runSetRenewalUpliftAction(orderLineItemMap, ruleAction, input.getChargeMap());
            default -> LOGGER.warn(
                ORDER_CREATION_CUSTOMIZATION,
                "Unknown action {} cannot be handled in order creation customization",
                ruleAction.getAction()
            );
        }
    }

    private void runForcePaymentTermAction(Order order, Object metadata) {
        if (Objects.nonNull(metadata) && metadata instanceof Integer dueInDays) {
            Optional<PaymentTerm> paymentTermOptional = PaymentTerm.fromDueInDays(dueInDays);
            if (paymentTermOptional.isPresent() && paymentTermsService.isValidPaymentTerm(paymentTermOptional.get())) {
                order.setPaymentTerm(paymentTermOptional.get());
                logAction(String.format("Order payment term was forced to be %s", paymentTermOptional.get()));
            } else {
                LOGGER.warn(ORDER_CREATION_CUSTOMIZATION, "Invalid due in days {} supplied in Zeppa action payment terms will not be set", dueInDays);
            }
        }
    }

    private void runForceBillingCycleAction(Order order, Object metadata) {
        if (Objects.nonNull(metadata) && metadata instanceof String billingCycle && StringUtils.isNotBlank(billingCycle)) {
            boolean validBillingCycle = Set.of(Cycle.values()).stream().map(Enum::name).collect(Collectors.toSet()).contains(billingCycle);
            if (validBillingCycle) {
                Cycle cycle = Cycle.valueOf(billingCycle);
                order.setBillingCycle(new Recurrence(cycle, 1));
                logAction(String.format("Order billing cycle was forced to be %s", billingCycle));
            } else {
                LOGGER.warn(ORDER_CREATION_CUSTOMIZATION, "Invalid billing cycle {} supplied in Zeppa action", billingCycle);
            }
        }
    }

    private void runPriceAttributeSelectionAction(Map<String, OrderLineItem> orderLineItemMap, RuleAction ruleAction, Map<String, Charge> chargeMap) {
        Object metadata = ruleAction.getMetadata();
        boolean force = ruleAction.getAction() == CustomizationAction.FORCE_LINE_ITEM_PRICE_ATTRIBUTE;

        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemPriceAttribute itemPriceAttribute &&
            orderLineItemMap.containsKey(itemPriceAttribute.getLineItemId()) &&
            MapUtils.isNotEmpty(itemPriceAttribute.getAttributeMap())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(itemPriceAttribute.getLineItemId());

            if (!ACTIONS_PRICE_ATTRIBUTE_ALLOWED.contains(lineItem.getAction())) {
                LOGGER.info(
                    String.format("line item action %s not in allowed action types %s", lineItem.getAction(), ACTIONS_PRICE_ATTRIBUTE_ALLOWED)
                );
                return;
            }

            if (!lineItemChargePresent(chargeMap, lineItem)) {
                return;
            }

            Charge charge = chargeMap.get(lineItem.getChargeId());

            if (StringUtils.isBlank(charge.getRateCardId())) {
                LOGGER.warn(
                    ORDER_CREATION_CUSTOMIZATION,
                    String.format("charge %s does not have rate card but attribute selection action called", lineItem.getChargeId())
                );
                return;
            }

            Optional<RateCard> rateCardOptional = rateCardService.getRateCard(charge.getRateCardId());

            // TODO: turn this to invariant check
            if (rateCardOptional.isEmpty()) {
                LOGGER.warn(
                    ORDER_CREATION_CUSTOMIZATION,
                    String.format("charge %s has rate card id %s but rate card object is missing", charge.getChargeId(), charge.getRateCardId())
                );
                return;
            }

            List<AttributeReference> finalList = new ArrayList<>();
            for (Map.Entry<String, String> entry : itemPriceAttribute.getAttributeMap().entrySet()) {
                String attributeName = entry.getKey();
                String attributeValue = entry.getValue();
                Optional<PriceAttribute> attributeDefOptional = rateCardService.getOptionalPriceAttributeByName(attributeName);
                if (attributeDefOptional.isEmpty()) {
                    LOGGER.warn(ORDER_CREATION_CUSTOMIZATION, String.format("price attribute by name: %s does not exist", attributeName));
                    // need to not verify anything else as price attribute is not found
                    // and the combination will not be found in the rate card
                    return;
                }
                finalList.add(new AttributeReference(attributeDefOptional.get().getId(), attributeValue));
            }

            RateCard rateCard = rateCardOptional.get();
            AttributeReferences references = AttributeReferences.wrap(finalList);
            Optional<BigDecimal> price = rateCard.resolvePrice(references);

            if (price.isEmpty()) {
                LOGGER.warn(
                    ORDER_CREATION_CUSTOMIZATION,
                    String.format("attribute references %s not present in rate card %s", references, rateCard.getId())
                );
                // if the combination is not found in the rate card we cannot proceed either
                // we early terminate
                return;
            }

            //finally set the line item attribute combination
            if (force || CollectionUtils.isEmpty(lineItem.getAttributeReferences())) {
                lineItem.setAttributeReferences(references.getReferencesInOrder());
                logAction(String.format("line item %s price attribute references applied as %s", lineItem.getOrderLineId(), references));
            } else {
                LOGGER.info(
                    String.format("line item %s price attribute already set as %s", lineItem.getOrderLineId(), lineItem.getAttributeReferences())
                );
            }
        }
    }

    private void runSetListPriceAction(Map<String, OrderLineItem> orderLineItemMap, RuleAction ruleAction, Map<String, Charge> chargeMap) {
        Object metadata = ruleAction.getMetadata();
        boolean force = ruleAction.getAction() == CustomizationAction.FORCE_LINE_ITEM_LIST_PRICE;

        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemPriceOverride listPriceMetadata &&
            orderLineItemMap.containsKey(listPriceMetadata.getLineItemId()) &&
            Objects.nonNull(listPriceMetadata.getPriceOverride())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(listPriceMetadata.getLineItemId());

            if (!lineItemChargePresent(chargeMap, lineItem)) {
                return;
            }

            if (!chargeMap.get(lineItem.getChargeId()).isCustom()) {
                LOGGER.warn(
                    ORDER_CREATION_CUSTOMIZATION,
                    String.format("charge id %s is not custom and yet the list price was specified", lineItem.getChargeId())
                );
                return;
            }

            if (force) {
                lineItem.setListUnitPrice(listPriceMetadata.getPriceOverride());
                logAction(String.format("line item %s list price forced to %s", lineItem.getOrderLineId(), listPriceMetadata.getPriceOverride()));
                return;
            }

            if (Objects.isNull(lineItem.getListUnitPrice()) || Numbers.isZero(lineItem.getListUnitPrice())) {
                lineItem.setListUnitPrice(listPriceMetadata.getPriceOverride());
                logAction(
                    String.format("line item %s price override applied as %s", lineItem.getOrderLineId(), listPriceMetadata.getPriceOverride())
                );
            }
        }
    }

    private void runPriceOverrideAction(Map<String, OrderLineItem> orderLineItemMap, RuleAction ruleAction, Map<String, Charge> chargeMap) {
        Object metadata = ruleAction.getMetadata();
        boolean force = ruleAction.getAction() == CustomizationAction.FORCE_LINE_ITEM_PRICE_OVERRIDE;
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemPriceOverride priceOverrideMetadata &&
            orderLineItemMap.containsKey(priceOverrideMetadata.getLineItemId()) &&
            Objects.nonNull(priceOverrideMetadata.getPriceOverride())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(priceOverrideMetadata.getLineItemId());

            if (!lineItemChargePresent(chargeMap, lineItem)) {
                return;
            }

            if (!chargeMap.get(lineItem.getChargeId()).getIsListPriceEditable()) {
                LOGGER.warn(
                    ORDER_CREATION_CUSTOMIZATION,
                    String.format("charge id %s is not list price editable but list price override specified", lineItem.getChargeId())
                );
                return;
            }

            if (force) {
                lineItem.setListPriceOverrideRatio(priceOverrideMetadata.getPriceOverride());
                logAction(
                    String.format("line item %s price override forced to %s", lineItem.getOrderLineId(), priceOverrideMetadata.getPriceOverride())
                );
                return;
            }

            // set override ration only when not already set this is just apply action
            if (Objects.isNull(lineItem.getListPriceOverrideRatio())) {
                lineItem.setListPriceOverrideRatio(priceOverrideMetadata.getPriceOverride());
                logAction(
                    String.format("line item %s price override applied as %s", lineItem.getOrderLineId(), priceOverrideMetadata.getPriceOverride())
                );
            }
        }
    }

    private void forceRampPriceUplifts(Order order, RuleAction ruleAction, Map<String, Charge> chargeMap) {
        Object metadata = ruleAction.getMetadata();
        if (Objects.nonNull(metadata) && metadata instanceof BigDecimal upliftPercent) {
            forceUpliftOneRampLines(order, upliftPercent, chargeMap);
        }
    }

    private void forceUpliftOneRampLines(Order order, BigDecimal upliftPercent, Map<String, Charge> chargeMap) {
        // get ramp line items where list prices are editable
        List<OrderLineItem> targetRampLines = order
            .getLineItemsNetEffect()
            .stream()
            .filter(
                lineItem ->
                    lineItem.getIsRamp() &&
                    chargeMap.containsKey(lineItem.getChargeId()) &&
                    chargeMap.get(lineItem.getChargeId()).getIsListPriceEditable() &&
                    ACTIONS_RAMP_OVERRIDE_ALLOWED.contains(lineItem.getAction())
            )
            .toList();

        // group ramps by ramp group id and then process each ramp group for price increment
        targetRampLines
            .stream()
            .collect(Collectors.groupingBy(OrderLineItem::getRampGroupId))
            .values()
            .forEach(groupedRampLines -> forceUpliftInIncrements(groupedRampLines, upliftPercent));
    }

    // force ramp price increment to a group of ramp line items
    //  - order the line items by effective date
    //  - the first line item's list price is not changed
    //  - list price is increased by upliftPercent% compared to the previous line item
    private void forceUpliftInIncrements(List<OrderLineItem> groupedRampLines, BigDecimal upliftPercent) {
        if (groupedRampLines.size() < 2) {
            LOGGER.info("ramp price increment cannot be performed since there are not enough line items");
            return;
        }
        groupedRampLines.sort(Comparator.comparing(OrderLineItem::getEffectiveDate));
        BigDecimal multiplierRatio = Numbers.scaledDivide(upliftPercent, ONE_HUNDRED).add(BigDecimal.ONE);
        BigDecimal runningRatio = BigDecimal.ONE;
        for (var lineItem : groupedRampLines) {
            lineItem.setListPriceOverrideRatio(runningRatio);
            String message = String.format("price override set to %s on line item %s", runningRatio, lineItem.getId());
            runningRatio = runningRatio.multiply(multiplierRatio);
            logAction(message);
        }
    }

    private static void failOrderCreation(RuleProcessed rule, RuleAction ruleAction) {
        String message = String.format("Rule \"%s\" failed order creation, reason: \"%s\"", rule.getRuleName(), ruleAction.getMetadata());
        logAction("Failed order creation");
        throw new InvalidInputException(message);
    }

    private void runRemoveDocumentTemplateAction(Order order, Object metadata) {
        if (Objects.nonNull(metadata) && metadata instanceof String templateName && StringUtils.isNotBlank(templateName)) {
            templateGetService
                .getMasterTemplateByNameAndType(templateName, DocumentTemplateType.ORDER)
                .ifPresent(template -> {
                    // if the template id matches the one in order clear it out
                    if (template.getId().equals(order.getDocumentMasterTemplateId())) {
                        order.setDocumentMasterTemplateId(null);
                        logAction(String.format("template with id %s removed for order %s", template.getId(), order.getOrderId()));
                    }
                });
        }
    }

    private void runForceDocumentTemplateAction(Order order, Object metadata) {
        if (Objects.nonNull(metadata) && metadata instanceof String templateName && StringUtils.isNotBlank(templateName)) {
            templateGetService
                .getMasterTemplateByNameAndType(templateName, DocumentTemplateType.ORDER)
                .ifPresent(template -> {
                    order.setDocumentMasterTemplateId(template.getId());
                    logAction(String.format("master template with id %s forced for order %s", template.getId(), order.getOrderId()));
                });
        }
    }

    private void runSetOrderLineCustomFieldAction(
        Map<String, OrderLineItem> orderLineItemMap,
        Object metadata,
        List<CustomFieldDefinition> lineItemDefinitions,
        RuleAction ruleAction
    ) {
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof OrderLineCustomField fieldValue &&
            StringUtils.isNotBlank(fieldValue.getLineItemId()) &&
            orderLineItemMap.containsKey(fieldValue.getLineItemId()) &&
            StringUtils.isNotBlank(fieldValue.getIdentifier()) &&
            CollectionUtils.isNotEmpty(fieldValue.getValues())
        ) {
            Optional<CustomFieldDefinition> matchedDefinition = lineItemDefinitions
                .stream()
                .filter(def -> def.getFieldName().equals(fieldValue.getIdentifier()) || def.getCustomFieldId().equals(fieldValue.getIdentifier()))
                .findFirst();

            if (matchedDefinition.isEmpty()) {
                LOGGER.warn(
                    ORDER_CREATION_CUSTOMIZATION,
                    "could not find order line custom field definition with identifier {}",
                    fieldValue.getIdentifier()
                );
                return;
            }

            CustomFieldDefinition definition = matchedDefinition.get();
            Optional<CustomFieldValue> valueOptional = CustomFieldValue.fromDefinitionWithValueAndSelections(definition, fieldValue.getValues());
            OrderLineItem lineItem = orderLineItemMap.get(fieldValue.getLineItemId());
            valueOptional.ifPresent(valueBeingSet -> {
                if (CollectionUtils.isEmpty(lineItem.getCustomFields())) {
                    CustomField customField = customFieldService.getCustomFieldsWithOptionalParent(CustomFieldParentType.ORDER, Optional.empty());
                    Map<String, CustomFieldValue> entries = new HashMap<>(customField.getEntries());
                    entries.put(definition.getCustomFieldId(), valueBeingSet);
                    lineItem.setCustomFieldEntriesFromCustomFieldObject(new CustomField(entries));
                    logAction(String.format("order line item custom field value set for identifier: %s", fieldValue.getIdentifier()));
                    return;
                }

                boolean force = ruleAction.getAction() == CustomizationAction.FORCE_ORDER_LINE_CUSTOM_FIELD;
                Map<String, CustomFieldValue> entries = new HashMap<>(
                    lineItem.getCustomFields().stream().collect(Collectors.toMap(CustomFieldEntry::getId, Function.identity()))
                );

                if (force || !entries.containsKey(definition.getCustomFieldId())) {
                    entries.put(definition.getCustomFieldId(), valueBeingSet);
                    lineItem.setCustomFieldEntriesFromCustomFieldObject(new CustomField(entries));
                    logAction(String.format("order line item custom field value set for identifier: %s", fieldValue.getIdentifier()));
                    return;
                }

                // not a force action we need to set the value then
                CustomFieldValue toUpdate = entries.get(definition.getCustomFieldId());
                if (StringUtils.isBlank(toUpdate.getValue())) {
                    entries.put(definition.getCustomFieldId(), valueBeingSet);
                    lineItem.setCustomFieldEntriesFromCustomFieldObject(new CustomField(entries));
                    logAction(String.format("order line item custom field value set for identifier: %s", fieldValue.getIdentifier()));
                }
            });
        }
    }

    private void runSetOrderCustomFieldAction(
        Order order,
        Object metadata,
        List<CustomFieldDefinition> orderFieldDefinitions,
        RuleAction ruleAction
    ) {
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof com.subskribe.zeppa.customizations.ordercreation.CustomFieldValue fieldValue &&
            StringUtils.isNotBlank(fieldValue.getIdentifier()) &&
            CollectionUtils.isNotEmpty(fieldValue.getValues())
        ) {
            Optional<CustomFieldDefinition> matchedDefinition = orderFieldDefinitions
                .stream()
                .filter(def -> def.getFieldName().equals(fieldValue.getIdentifier()) || def.getCustomFieldId().equals(fieldValue.getIdentifier()))
                .findFirst();

            if (matchedDefinition.isEmpty()) {
                LOGGER.warn(
                    ORDER_CREATION_CUSTOMIZATION,
                    "could not find order custom field definition with identifier {}",
                    fieldValue.getIdentifier()
                );
                return;
            }

            CustomFieldDefinition definition = matchedDefinition.get();
            Optional<CustomFieldValue> valueOptional = CustomFieldValue.fromDefinitionWithValueAndSelections(definition, fieldValue.getValues());
            valueOptional.ifPresent(valueBeingSet -> {
                if (order.getCustomFields() == null || MapUtils.isEmpty(order.getCustomFields().getEntries())) {
                    CustomField customField = customFieldService.getCustomFieldsWithOptionalParent(CustomFieldParentType.ORDER, Optional.empty());
                    Map<String, CustomFieldValue> entries = new HashMap<>(customField.getEntries());
                    entries.put(definition.getCustomFieldId(), valueBeingSet);
                    order.setCustomFields(new CustomField(entries));
                    logAction(String.format("order custom field value set for identifier: %s", fieldValue.getIdentifier()));
                    return;
                }

                boolean force = ruleAction.getAction() == CustomizationAction.FORCE_ORDER_CUSTOM_FIELD;
                Map<String, CustomFieldValue> entries = new HashMap<>(order.getCustomFields().getEntries());

                if (force || !entries.containsKey(definition.getCustomFieldId())) {
                    entries.put(definition.getCustomFieldId(), valueBeingSet);
                    order.setCustomFields(new CustomField(entries));
                    logAction(String.format("order custom field value set for identifier: %s", fieldValue.getIdentifier()));
                    return;
                }

                // not a force action we need to set the value then
                CustomFieldValue toUpdate = entries.get(definition.getCustomFieldId());
                if (StringUtils.isBlank(toUpdate.getValue())) {
                    entries.put(definition.getCustomFieldId(), valueBeingSet);
                    order.setCustomFields(new CustomField(entries));
                    logAction(String.format("order custom field value set for identifier: %s", fieldValue.getIdentifier()));
                }
            });
        }
    }

    private static void runSetLineItemQuantityAction(
        Map<String, OrderLineItem> orderLineItemMap,
        Object metadata,
        RuleAction ruleAction,
        Map<String, Charge> chargeMap
    ) {
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemQuantity lineQuantityMetadata &&
            orderLineItemMap.containsKey(lineQuantityMetadata.getLineItemId())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(lineQuantityMetadata.getLineItemId());

            if (!chargeMap.containsKey(lineItem.getChargeId())) {
                LOGGER.warn(
                    ORDER_CREATION_CUSTOMIZATION,
                    "Charge map does not contain line item charge id {} no action will be taken",
                    lineItem.getChargeId()
                );
                return;
            }

            long zeppaSetQuantity = lineQuantityMetadata.getQuantity();

            Charge relevantCharge = chargeMap.get(lineItem.getChargeId());
            if (relevantCharge.getMinQuantity() != null && relevantCharge.getMinQuantity().compareTo(zeppaSetQuantity) > 0) {
                LOGGER.warn(
                    ORDER_CREATION_CUSTOMIZATION,
                    "Charge {} minimum quantity {} is greater than action quantity {} no action will be taken",
                    lineItem.getChargeId(),
                    relevantCharge.getMinQuantity(),
                    zeppaSetQuantity
                );
                return;
            }

            if (relevantCharge.getMaxQuantity() != null && relevantCharge.getMaxQuantity().compareTo(zeppaSetQuantity) < 0) {
                LOGGER.warn(
                    ORDER_CREATION_CUSTOMIZATION,
                    "Charge {} maximum quantity {} is less than action quantity {} no action will be taken",
                    lineItem.getChargeId(),
                    relevantCharge.getMaxQuantity(),
                    zeppaSetQuantity
                );
                return;
            }

            // force or apply action
            boolean force = ruleAction.getAction() == CustomizationAction.FORCE_LINE_ITEM_QUANTITY;

            if (lineItem.getQuantity() == 0 || force) {
                lineItem.setQuantity(zeppaSetQuantity);
                String action = force ? "forced" : "applied";
                logAction(String.format("quantity %s for line item %s to be %s", action, lineItem.getOrderLineId(), lineItem.getQuantity()));
            }
        }
    }

    private void runForceStartDateToEndDate(Map<String, OrderLineItem> orderLineItemMap, Object metadata) {
        if (Objects.nonNull(metadata) && metadata instanceof String lineItemId && orderLineItemMap.containsKey(lineItemId)) {
            OrderLineItem lineItem = orderLineItemMap.get(lineItemId);
            lineItem.setEffectiveDate(lineItem.getEndDate());
            logAction(String.format("line item %s effective date forced to %s", lineItem.getOrderLineId(), lineItem.getEffectiveDate()));
        }
    }

    private static void runApplyLineItemDiscountAction(Map<String, OrderLineItem> orderLineItemMap, RuleProcessed rule, Object metadata) {
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemDiscount lineDiscountMetadata &&
            orderLineItemMap.containsKey(lineDiscountMetadata.getLineItemId())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(lineDiscountMetadata.getLineItemId());
            boolean shouldAddDiscount = CollectionUtils.isEmpty(lineItem.getDiscounts());
            if (shouldAddDiscount) {
                DiscountDetail discountDetail = new DiscountDetail();
                discountDetail.setId(String.format("Rule %s discount", rule.getRuleName()));
                discountDetail.setPercent(lineDiscountMetadata.getDiscount());
                List<DiscountDetail> newList = new ArrayList<>(lineItem.getDiscounts() == null ? List.of() : lineItem.getDiscounts());
                newList.add(discountDetail);
                lineItem.setDiscounts(newList);
                logAction(String.format("line item %s discount applied as %s", lineItem.getOrderLineId(), lineDiscountMetadata.getDiscount()));
            }
        }
    }

    private void runRemoveLinePredefinedDiscountAction(Map<String, OrderLineItem> orderLineItemMap, Object metadata) {
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemPredefinedDiscount itemPredefinedDiscount &&
            orderLineItemMap.containsKey(itemPredefinedDiscount.getLineItemId()) &&
            StringUtils.isNotEmpty(itemPredefinedDiscount.getDiscountName())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(itemPredefinedDiscount.getLineItemId());
            if (CollectionUtils.isEmpty(lineItem.getPredefinedDiscounts())) {
                return;
            }
            discountService
                .getDiscountByName(itemPredefinedDiscount.getDiscountName())
                .ifPresent(discount -> {
                    List<TenantDiscountLineItem> discountsFiltered = lineItem
                        .getPredefinedDiscounts()
                        .stream()
                        .filter(lineDiscount -> !discount.getDiscountId().equals(lineDiscount.getId()))
                        .toList();
                    lineItem.setPredefinedDiscounts(discountsFiltered);
                    logAction(String.format("potentially removed discount %s for line item %s", discount.getDiscountId(), lineItem.getOrderLineId()));
                });
        }
    }

    private void runAddPredefinedTermAction(Order order, Object metadata) {
        if (Objects.nonNull(metadata) && metadata instanceof String termName && StringUtils.isNotBlank(termName)) {
            templateGetService
                .getTemplateByNameAndType(termName, DocumentTemplateType.ORDER, Optional.of(DocumentTemplateStatus.ACTIVE)) // add ACTIVE terms only
                .ifPresent(termTemplate -> {
                    List<String> templateIds = order.getOrderFormTemplateIds();
                    if (CollectionUtils.isEmpty(templateIds)) {
                        order.setOrderFormTemplateIds(List.of(termTemplate.getTemplateId()));
                        logAction(String.format("added template %s to order %s", termTemplate.getTemplateId(), order.getOrderId()));
                    }

                    if (!templateIds.contains(termTemplate.getTemplateId())) {
                        List<String> newList = new ArrayList<>(templateIds);
                        newList.add(termTemplate.getTemplateId());
                        order.setOrderFormTemplateIds(newList);
                        logAction(String.format("added template %s to order %s", termTemplate.getTemplateId(), order.getOrderId()));
                    }
                });
        }
    }

    private void runRemovePredefinedTermAction(Order order, Object metadata) {
        if (Objects.nonNull(metadata) && metadata instanceof String termName && StringUtils.isNotBlank(termName)) {
            templateGetService
                .getTemplateByNameAndType(termName, DocumentTemplateType.ORDER, Optional.empty())
                .ifPresent(termTemplate -> {
                    List<String> templateIds = order.getOrderFormTemplateIds();
                    // if the template ids empty nothing to do
                    if (CollectionUtils.isEmpty(templateIds)) {
                        return;
                    }

                    List<String> filtered = templateIds.stream().filter(templateId -> !templateId.equals(termTemplate.getTemplateId())).toList();
                    order.setOrderFormTemplateIds(filtered);
                    logAction(
                        String.format("potentially removed predefined term %s from order %s", termTemplate.getTemplateId(), order.getOrderId())
                    );
                });
        }
    }

    private void runAddPredefinedDiscountAction(Order order, Map<String, OrderLineItem> orderLineItemMap, Object metadata) {
        if (Objects.nonNull(metadata) && metadata instanceof String discountName && StringUtils.isNotBlank(discountName)) {
            discountService
                .getDiscountByName(discountName)
                .ifPresent(discount -> {
                    if (shouldAddDiscount(discount, order)) {
                        List<TenantDiscount> discountList = CollectionUtils.isEmpty(order.getPredefinedDiscounts())
                            ? new ArrayList<>()
                            : new ArrayList<>(order.getPredefinedDiscounts());
                        discountList.add(toTenantDiscount(discount));
                        order.setPredefinedDiscounts(discountList);
                        logAction(String.format("added discount %s for order %s", discount.getDiscountId(), order.getOrderId()));

                        orderLineItemMap
                            .values()
                            .forEach(lineItem -> {
                                if (lineItem.getAction() != null && ACTIONS_PREDEFINED_DISCOUNT_ALLOWED.contains(lineItem.getAction())) {
                                    List<TenantDiscountLineItem> newList = CollectionUtils.isEmpty(lineItem.getPredefinedDiscounts())
                                        ? new ArrayList<>()
                                        : new ArrayList<>(lineItem.getPredefinedDiscounts());
                                    newList.add(toTenantDiscountLine(discount));
                                    logAction(
                                        String.format("added discount %s to line item %s", discount.getDiscountId(), lineItem.getOrderLineId())
                                    );
                                    lineItem.setPredefinedDiscounts(newList);
                                }
                            });
                    }
                });
        }
    }

    private void runRemovePredefinedDiscountAction(Order order, Map<String, OrderLineItem> orderLineItemMap, Object metadata) {
        if (Objects.nonNull(metadata) && metadata instanceof String discountName && StringUtils.isNotBlank(discountName)) {
            discountService
                .getDiscountByName(discountName)
                .ifPresent(discount -> {
                    if (shouldRemoveDiscount(discount, order)) {
                        List<TenantDiscount> filtered = order
                            .getPredefinedDiscounts()
                            .stream()
                            .filter(tenantDiscount -> !tenantDiscount.getId().equals(discount.getDiscountId()))
                            .toList();
                        order.setPredefinedDiscounts(filtered);
                        logAction(String.format("potentially removed discount %s from order %s", discount.getDiscountId(), order.getOrderId()));

                        if (MapUtils.isEmpty(orderLineItemMap)) {
                            return;
                        }

                        removeDiscountFromLineItems(orderLineItemMap, discount);
                    }
                });
        }
    }

    private void runSetRenewalUpliftAction(Map<String, OrderLineItem> orderLineItemMap, RuleAction ruleAction, Map<String, Charge> chargeMap) {
        Object metadata = ruleAction.getMetadata();
        boolean force = ruleAction.getAction() == CustomizationAction.FORCE_RENEWAL_UPLIFT;
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemRenewalUplift renewalUpliftMetadata &&
            orderLineItemMap.containsKey(renewalUpliftMetadata.getLineItemId()) &&
            Objects.nonNull(renewalUpliftMetadata.getUpliftRatio())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(renewalUpliftMetadata.getLineItemId());

            if (!lineItemChargePresent(chargeMap, lineItem)) {
                return;
            }

            lineItem.setRenewalUpliftRatio(renewalUpliftMetadata.getUpliftRatio());

            if (force) {
                lineItem.setListPriceOverrideRatio(null); // reset user provided override ratio if forcing action
            }

            logAction(
                String.format(
                    "line item %s price override set to %s with force = %s",
                    lineItem.getOrderLineId(),
                    renewalUpliftMetadata.getUpliftRatio(),
                    force
                )
            );
        }
    }

    private static boolean lineItemChargePresent(Map<String, Charge> chargeMap, OrderLineItem lineItem) {
        if (StringUtils.isBlank(lineItem.getChargeId())) {
            LOGGER.warn(ORDER_CREATION_CUSTOMIZATION, String.format("charge id blank for line item %s", lineItem.getId()));
            return false;
        }

        if (!chargeMap.containsKey(lineItem.getChargeId())) {
            LOGGER.warn(
                ORDER_CREATION_CUSTOMIZATION,
                String.format("charge id not present in charge map for %s charge id %s", lineItem.getId(), lineItem.getChargeId())
            );
            return false;
        }

        return true;
    }

    private static void removeDiscountFromLineItems(Map<String, OrderLineItem> orderLineItemMap, Discount discount) {
        orderLineItemMap
            .values()
            .stream()
            .filter(
                line ->
                    CollectionUtils.isNotEmpty(line.getPredefinedDiscounts()) &&
                    line
                        .getPredefinedDiscounts()
                        .stream()
                        .anyMatch(tenantDiscountLineItem -> tenantDiscountLineItem.getId().equals(discount.getDiscountId()))
            )
            .forEach(orderLineItem -> {
                List<TenantDiscountLineItem> filteredForLine = orderLineItem
                    .getPredefinedDiscounts()
                    .stream()
                    .filter(tenantDiscountLineItem -> !tenantDiscountLineItem.getId().equals(discount.getDiscountId()))
                    .toList();
                orderLineItem.setPredefinedDiscounts(filteredForLine);
            });
    }

    private static List<PlanBinding> getPlanBindings(OrderCreationCustomizationInput orderCreationCustomizationInput) {
        return orderCreationCustomizationInput
            .getPlanMap()
            .values()
            .stream()
            .map(plan -> {
                CustomField planCustomField = orderCreationCustomizationInput.getPlanCustomFields().getOrDefault(plan.getPlanId(), null);
                List<ChargeBinding> chargeBindings = getChargeBindings(orderCreationCustomizationInput, plan);
                ProductBinding productBinding = ProductBinding.from(
                    orderCreationCustomizationInput.getProductMap().getOrDefault(plan.getProductId(), null)
                );
                return PlanBinding.from(plan, planCustomField, chargeBindings, productBinding);
            })
            .toList();
    }

    private static List<ChargeBinding> getChargeBindings(OrderCreationCustomizationInput orderCreationCustomizationInput, Plan plan) {
        return CollectionUtils.isEmpty(plan.getCharges())
            ? List.of()
            : plan
                .getCharges()
                .stream()
                .map(charge -> {
                    Charge chargeModel = orderCreationCustomizationInput.getChargeMap().getOrDefault(charge.getChargeId(), null);
                    CustomField chargeCustomFields = orderCreationCustomizationInput.getChargeCustomFields().getOrDefault(charge.getChargeId(), null);
                    return ChargeBinding.from(chargeModel, chargeCustomFields);
                })
                .toList();
    }

    private static TenantDiscount toTenantDiscount(Discount discount) {
        TenantDiscount tenantDiscount = new TenantDiscount();
        tenantDiscount.setId(discount.getDiscountId());
        tenantDiscount.setPercent(discount.getPercent());
        tenantDiscount.setName(discount.getName());
        tenantDiscount.setType(discount.getType());
        tenantDiscount.setDescription(discount.getDescription());
        tenantDiscount.setStatus(discount.getStatus());
        return tenantDiscount;
    }

    private static TenantDiscountLineItem toTenantDiscountLine(Discount discount) {
        TenantDiscountLineItem tenantDiscount = new TenantDiscountLineItem();
        tenantDiscount.setId(discount.getDiscountId());
        tenantDiscount.setPercent(discount.getPercent());
        tenantDiscount.setName(discount.getName());
        tenantDiscount.setType(discount.getType());
        tenantDiscount.setDescription(discount.getDescription());
        tenantDiscount.setStatus(discount.getStatus());
        return tenantDiscount;
    }

    private static boolean shouldAddDiscount(Discount discount, Order order) {
        if (CollectionUtils.isEmpty(order.getPredefinedDiscounts())) {
            return true;
        }

        return order.getPredefinedDiscounts().stream().noneMatch(tenantDiscount -> discount.getDiscountId().equals(tenantDiscount.getId()));
    }

    private static boolean shouldRemoveDiscount(Discount discount, Order order) {
        if (CollectionUtils.isEmpty(order.getPredefinedDiscounts())) {
            return false;
        }

        return order.getPredefinedDiscounts().stream().anyMatch(tenantDiscount -> discount.getDiscountId().equals(tenantDiscount.getId()));
    }

    private static void logAction(String message) {
        LOGGER.info(String.format("CUSTOMIZATION_ACTION_PERFORMED: %s", message));
    }
}
