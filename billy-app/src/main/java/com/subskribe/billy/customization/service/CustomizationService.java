package com.subskribe.billy.customization.service;

import static com.subskribe.billy.customization.CustomizationProductMetadata.ORDER_CREATION_CUSTOMIZATION;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.ImmutableList;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldProxy;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.customization.SelectionCustomizationInput;
import com.subskribe.billy.customization.db.CustomizationDefinitionDAO;
import com.subskribe.billy.customization.model.Action;
import com.subskribe.billy.customization.model.CustomizationActionType;
import com.subskribe.billy.customization.model.CustomizationContext;
import com.subskribe.billy.customization.model.CustomizationDefinition;
import com.subskribe.billy.customization.model.CustomizationDefinitionInput;
import com.subskribe.billy.customization.model.ImmutableCustomizationDefinitionInput;
import com.subskribe.billy.customization.model.ImmutableOrderCreationCustomizationInput;
import com.subskribe.billy.customization.model.ImmutableOrderCreationCustomizationOutput;
import com.subskribe.billy.customization.model.ImmutableRuleTrace;
import com.subskribe.billy.customization.model.OrderCreationCustomizationInput;
import com.subskribe.billy.customization.model.OrderCreationCustomizationOutput;
import com.subskribe.billy.customization.model.PlanAdditionCustomizationInput;
import com.subskribe.billy.customization.model.RuleActionPerformed;
import com.subskribe.billy.customization.model.RuleTrace;
import com.subskribe.billy.discount.model.Discount;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.paymentterms.services.PaymentTermSettingsService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.opportunity.OpportunityMapper;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.pecuniary.TenantDiscount;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.template.model.DocumentTemplateStatus;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.zeppa.customizations.bindings.AccountBinding;
import com.subskribe.zeppa.customizations.bindings.ChargeBinding;
import com.subskribe.zeppa.customizations.bindings.ContactBinding;
import com.subskribe.zeppa.customizations.bindings.OpportunityBinding;
import com.subskribe.zeppa.customizations.bindings.OrderBinding;
import com.subskribe.zeppa.customizations.bindings.PlanBinding;
import com.subskribe.zeppa.customizations.bindings.ProductBinding;
import com.subskribe.zeppa.customizations.bindings.UserBinding;
import com.subskribe.zeppa.customizations.core.CustomizationAction;
import com.subskribe.zeppa.customizations.core.RuleAction;
import com.subskribe.zeppa.customizations.core.RuleProcessed;
import com.subskribe.zeppa.customizations.core.ZeppaDriver;
import com.subskribe.zeppa.customizations.ordercreation.LineItemDiscount;
import com.subskribe.zeppa.customizations.ordercreation.LineItemPredefinedDiscount;
import com.subskribe.zeppa.customizations.ordercreation.LineItemPriceAttribute;
import com.subskribe.zeppa.customizations.ordercreation.LineItemPriceOverride;
import com.subskribe.zeppa.customizations.ordercreation.LineItemQuantity;
import com.subskribe.zeppa.customizations.ordercreation.LineItemRenewalUplift;
import com.subskribe.zeppa.customizations.ordercreation.OrderLineCustomField;
import com.subskribe.zeppa.customizations.selection.SelectedAttributes;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class CustomizationService {

    public static final String PRODUCT_CATEGORY = "product_category";

    public static final Set<ActionType> ACTIONS_PRICE_ATTRIBUTE_ALLOWED = Set.of(ActionType.ADD, ActionType.RENEWAL, ActionType.RESTRUCTURE);

    private static final Set<ActionType> ACTIONS_PREDEFINED_DISCOUNT_ALLOWED = Set.of(ActionType.ADD, ActionType.RESTRUCTURE, ActionType.RENEWAL);

    private static final Set<ActionType> ACTIONS_RAMP_OVERRIDE_ALLOWED = Set.of(ActionType.ADD, ActionType.RESTRUCTURE, ActionType.RENEWAL);

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomizationService.class);

    private static final BigDecimal ONE_HUNDRED = new BigDecimal(100);

    private final FeatureService featureService;

    private final AccountGetService accountGetService;

    private final CustomFieldProxy customFieldProxy;

    private final OrderGetService orderGetService;

    private final ProductCatalogGetService catalogGetService;

    private final DocumentTemplateGetService templateGetService;

    private final DiscountService discountService;

    private final CustomizationDefinitionDAO customizationDefinitionDAO;

    private final TenantSettingService tenantSettingService;

    private final UserService userService;

    private final RateCardService rateCardService;

    private final PaymentTermSettingsService paymentTermsService;

    private final OpportunityGetService opportunityGetService;

    private final CustomFieldService customFieldService;

    private final OpportunityMapper opportunityMapper;

    @Inject
    public CustomizationService(
        FeatureService featureService,
        AccountGetService accountGetService,
        CustomFieldProxy customFieldProxy,
        OrderGetService orderGetService,
        ProductCatalogGetService catalogGetService,
        DocumentTemplateGetService templateGetService,
        DiscountService discountService,
        CustomizationDefinitionDAO customizationDefinitionDAO,
        TenantSettingService tenantSettingService,
        UserService userService,
        RateCardService rateCardService,
        PaymentTermSettingsService paymentTermsService,
        OpportunityGetService opportunityGetService,
        CustomFieldService customFieldService
    ) {
        this.featureService = featureService;
        this.accountGetService = accountGetService;
        this.customFieldProxy = customFieldProxy;
        this.orderGetService = orderGetService;
        this.catalogGetService = catalogGetService;
        this.templateGetService = templateGetService;
        this.discountService = discountService;
        this.customizationDefinitionDAO = customizationDefinitionDAO;
        this.tenantSettingService = tenantSettingService;
        this.userService = userService;
        this.rateCardService = rateCardService;
        this.paymentTermsService = paymentTermsService;
        this.opportunityGetService = opportunityGetService;
        this.customFieldService = customFieldService;
        opportunityMapper = Mappers.getMapper(OpportunityMapper.class);
    }

    public List<CustomFieldEntry> runOrderLineItemCustomFieldCustomization(
        CustomFieldParentType type,
        String orderLineItemId,
        List<CustomFieldEntry> entries
    ) {
        if (!featureService.isEnabled(Feature.ORDER_LINE_ITEM_CUSTOM_FIELDS_CUSTOMIZATION) || type != CustomFieldParentType.ORDER_ITEM) {
            return entries;
        }

        Optional<OrderLineItem> orderLineItem = orderGetService.getOrderLineItemByOrderLineItemId(orderLineItemId);

        if (orderLineItem.isEmpty()) {
            return entries;
        }

        String planId = orderLineItem.get().getPlanId();
        String chargeId = orderLineItem.get().getChargeId();

        return runOrderLineItemCustomFieldCustomization(planId, chargeId, entries);
    }

    public List<CustomFieldEntry> runOrderLineItemCustomFieldCustomization(
        String planId,
        String chargeId,
        List<CustomFieldEntry> orderLineCustomFieldEntries
    ) {
        if (!featureService.isEnabled(Feature.ORDER_LINE_ITEM_CUSTOM_FIELDS_CUSTOMIZATION)) {
            return orderLineCustomFieldEntries;
        }

        CustomField planCustomField = customFieldProxy.getCustomFields(CustomFieldParentType.PLAN, planId);
        CustomField chargeCustomField = customFieldProxy.getCustomFields(CustomFieldParentType.CHARGE, chargeId);

        return filterOrderLineItemCustomFieldsByPlanAndChargeCustomFields(planCustomField, chargeCustomField, orderLineCustomFieldEntries);
    }

    public void runOrderLineItemCustomFieldCustomization(List<OrderLineItemDetail> orderLineItems) {
        if (!featureService.isEnabled(Feature.ORDER_LINE_ITEM_CUSTOM_FIELDS_CUSTOMIZATION)) {
            return;
        }

        List<String> planIds = orderLineItems.stream().map(item -> item.getPlan().getId()).filter(Objects::nonNull).distinct().toList();
        List<String> chargeIds = orderLineItems.stream().map(item -> item.getCharge().getId()).filter(Objects::nonNull).distinct().toList();

        Map<String, CustomField> planCustomFieldMap = customFieldProxy.getCustomFields(CustomFieldParentType.PLAN, planIds);
        Map<String, CustomField> chargeCustomFieldMap = customFieldProxy.getCustomFields(CustomFieldParentType.CHARGE, chargeIds);

        orderLineItems.forEach(orderLineItemDetail -> {
            List<CustomFieldEntry> filteredCustomFields = filterOrderLineItemCustomFieldsByPlanAndChargeCustomFields(
                planCustomFieldMap.get(orderLineItemDetail.getPlan().getId()),
                chargeCustomFieldMap.get(orderLineItemDetail.getCharge().getId()),
                orderLineItemDetail.getCustomFields()
            );
            orderLineItemDetail.setCustomFields(filteredCustomFields);
        });
    }

    // filter available order line item custom fields based on plan and then charge custom field selections
    // example: line item custom field has options 1, 2, 3, 4. Plan has selections 1, 2, 3, charge has selections 1, 2
    // after filtering line item custom fields only has options 1 and 2 available
    private List<CustomFieldEntry> filterOrderLineItemCustomFieldsByPlanAndChargeCustomFields(
        CustomField planCustomField,
        CustomField chargeCustomField,
        List<CustomFieldEntry> orderLineCustomFieldEntries
    ) {
        List<CustomFieldEntry> planFilteredEntries = planCustomField == null
            ? orderLineCustomFieldEntries
            : filterOrderLineCustomFieldsOptions(orderLineCustomFieldEntries, planCustomField);
        return chargeCustomField == null ? planFilteredEntries : filterOrderLineCustomFieldsOptions(planFilteredEntries, chargeCustomField);
    }

    private static List<CustomFieldEntry> filterOrderLineCustomFieldsOptions(
        List<CustomFieldEntry> customFieldEntries,
        CustomField filteringCustomField
    ) {
        List<CustomFieldEntry> filteredCustomFieldEntries = new ArrayList<>();

        customFieldEntries.forEach(orderLineCustomFieldEntry ->
            filteringCustomField
                .getEntries()
                .values()
                .stream()
                .filter(value -> value.getName().equals(orderLineCustomFieldEntry.getName()))
                .findFirst()
                .ifPresentOrElse(
                    value -> {
                        CustomFieldEntry filteredEntry = filterOrderLineCustomFieldOptions(orderLineCustomFieldEntry, value);
                        filteredCustomFieldEntries.add(filteredEntry);
                    },
                    () -> filteredCustomFieldEntries.add(orderLineCustomFieldEntry)
                )
        );

        return filteredCustomFieldEntries;
    }

    // filter custom field options and selections list by a whitelist of options allowed
    private static CustomFieldEntry filterOrderLineCustomFieldOptions(CustomFieldEntry orderLineCustomFieldEntry, CustomFieldValue value) {
        Set<String> filteringFieldValues = new HashSet<>(value.getSelections());
        List<String> filteredOptions = orderLineCustomFieldEntry.getOptions().stream().filter(filteringFieldValues::contains).toList();
        List<String> filteredSelections = orderLineCustomFieldEntry.getSelections().stream().filter(filteringFieldValues::contains).toList();

        return new CustomFieldEntry(
            orderLineCustomFieldEntry.getId(),
            orderLineCustomFieldEntry.getType(),
            orderLineCustomFieldEntry.getName(),
            orderLineCustomFieldEntry.getLabel(),
            orderLineCustomFieldEntry.getValue(),
            filteredSelections,
            filteredOptions,
            orderLineCustomFieldEntry.isRequired(),
            orderLineCustomFieldEntry.getSource(),
            orderLineCustomFieldEntry.getDefaultValue()
        );
    }

    public void compileSelectionCustomization(String accountId, String userId, String crmOpportunityId, String zeppaScript) {
        Optional<Opportunity> opportunity = StringUtils.isNotBlank(crmOpportunityId)
            ? opportunityGetService.getOptionalOpportunityByCrmOpportunityId(crmOpportunityId)
            : Optional.empty();
        compileSelectionCustomization(accountId, userId, opportunity.orElse(null), zeppaScript);
    }

    public void compileSelectionCustomization(String accountId, String userId, Opportunity opportunity, String zeppaScript) {
        Account account = accountGetService.getAccount(accountId);
        Optional<AccountAddress> accountAddress = accountGetService.getAccountAddress(account.getAddressId());
        AccountBinding accountBinding = AccountBinding.from(account, accountAddress.orElse(null), account.getCustomFields());
        UserBinding userBinding = getUserBinding(userId);
        OpportunityBinding opportunityBinding = getOpportunityBinding(opportunity);
        ZeppaDriver.compileSelectionCustomization(accountBinding, userBinding, opportunityBinding, zeppaScript);
    }

    public void compileOrderCustomization(String orderId, String userId, String zeppaScript) {
        OrderBinding orderBinding = orderBindingFromId(orderId);
        UserBinding userBinding = getUserBinding(userId);
        ZeppaDriver.compileOrderCreationCustomization(orderBinding, zeppaScript, userBinding);
    }

    public List<RuleProcessed> fireSelectionCustomization(String accountId, String userId, String crmOpportunityId, String zeppaScript) {
        Optional<Opportunity> opportunity = StringUtils.isNotBlank(crmOpportunityId)
            ? opportunityGetService.getOptionalOpportunityByCrmOpportunityId(crmOpportunityId)
            : Optional.empty();
        return fireSelectionCustomization(accountId, userId, opportunity.orElse(null), zeppaScript);
    }

    public List<RuleProcessed> fireSelectionCustomization(String accountId, String userId, Opportunity opportunity, String zeppaScript) {
        Account account = accountGetService.getAccount(accountId);
        Optional<AccountAddress> accountAddress = accountGetService.getAccountAddress(account.getAddressId());
        AccountBinding accountBinding = AccountBinding.from(account, accountAddress.orElse(null), account.getCustomFields());
        UserBinding userBinding = getUserBinding(userId);
        OpportunityBinding opportunityBinding = getOpportunityBinding(opportunity);
        return ZeppaDriver.fireSelectionCustomization(accountBinding, userBinding, opportunityBinding, zeppaScript);
    }

    public List<RuleProcessed> fireOrderCreationCustomization(String orderId, String userId, String zeppaScript) {
        OrderBinding orderBinding = orderBindingFromId(orderId);
        return ZeppaDriver.fireOrderCreationCustomization(orderBinding, zeppaScript, getUserBinding(userId));
    }

    private OrderBinding orderBindingFromId(String orderId) {
        OrderCreationCustomizationInput input = getOrderCreationCustomizationInput(orderId);
        Account account = input.getAccount();
        AccountBinding accountBinding = AccountBinding.from(input.getAccount(), input.getAccountAddress().orElse(null), account.getCustomFields());
        CustomField shippingAccountCustomFields = input.getBillingContactAccount().map(Account::getCustomFields).orElse(CustomField.empty());
        // TODO: populate shipping account address
        AccountBinding shippingAccountBinding = AccountBinding.from(input.getBillingContactAccount().orElse(null), null, shippingAccountCustomFields);
        return OrderBinding.from(
            input.getOrder(),
            accountBinding,
            getPlanBindings(input),
            input.getTimeZoneId(),
            ContactBinding.from(input.getShippingContact().orElse(null), accountBinding),
            ContactBinding.from(input.getBillingContact().orElse(null), shippingAccountBinding)
        );
    }

    private UserBinding getUserBinding(String userId) {
        if (StringUtils.isNotBlank(userId)) {
            return userService.getUserOptional(userId).map(UserBinding::fromUser).orElse(UserBinding.empty());
        }
        return UserBinding.empty();
    }

    private OpportunityBinding getOpportunityBinding(Opportunity opportunity) {
        return Optional.ofNullable(opportunity).map(OpportunityBinding::from).orElse(OpportunityBinding.empty());
    }

    private OrderCreationCustomizationInput getOrderCreationCustomizationInput(String orderId) {
        ImmutableOrderCreationCustomizationInput.Builder inputBuilder = ImmutableOrderCreationCustomizationInput.builder();
        Order order = orderGetService.getOrderByOrderId(orderId);
        Account account = accountGetService.getAccount(order.getAccountId());
        Optional<AccountAddress> accountAddress = accountGetService.getAccountAddress(account.getAddressId());
        List<String> planIds = order.getLineItems().stream().map(OrderLineItem::getPlanId).distinct().toList();

        if (StringUtils.isNotBlank(order.getShippingContactId())) {
            inputBuilder.shippingContact(accountGetService.getContact(order.getShippingContactId()));
        }

        if (StringUtils.isNotBlank(order.getBillingContactId())) {
            AccountContact billingContact = accountGetService.getContact(order.getBillingContactId());
            inputBuilder.billingContact(billingContact);

            if (!billingContact.getAccountId().equals(account.getAccountId())) {
                inputBuilder.billingContactAccount(accountGetService.getAccount(billingContact.getAccountId()));
            } else {
                inputBuilder.billingContactAccount(account);
            }
        }

        Map<String, Plan> planMap = catalogGetService
            .getPlansByPlanIds(planIds)
            .stream()
            .collect(Collectors.toMap(Plan::getPlanId, Function.identity()));
        Map<String, Charge> chargeMap = planMap
            .values()
            .stream()
            .map(Plan::getCharges)
            .flatMap(Collection::stream)
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        Map<String, Product> productMap = planMap
            .values()
            .stream()
            .map(Plan::getProductId)
            .distinct()
            .map(catalogGetService::getProduct)
            .collect(Collectors.toMap(Product::getProductId, Function.identity()));

        Map<String, CustomField> planCustomFieldMap = planMap
            .keySet()
            .stream()
            .collect(Collectors.toMap(Function.identity(), planId -> customFieldProxy.getCustomFields(CustomFieldParentType.PLAN, planId)));
        Map<String, CustomField> chargeCustomFieldMap = chargeMap
            .keySet()
            .stream()
            .collect(Collectors.toMap(Function.identity(), chargeId -> customFieldProxy.getCustomFields(CustomFieldParentType.CHARGE, chargeId)));

        return inputBuilder
            .order(order)
            .account(account)
            .accountAddress(accountAddress)
            .planMap(planMap)
            .productMap(productMap)
            .chargeMap(chargeMap)
            .planCustomFields(planCustomFieldMap)
            .chargeCustomFields(chargeCustomFieldMap)
            .timeZoneId(tenantSettingService.getTenantSetting().getDefaultTimeZone().toZoneId())
            .build();
    }

    public Optional<CustomizationDefinition> getCurrentSelectionCustomization() {
        return customizationDefinitionDAO.getLatestCustomizationDefinition(CustomizationContext.SELECTION_CUSTOMIZATION);
    }

    public Optional<CustomizationDefinition> getCurrentOrderCreationCustomization() {
        return customizationDefinitionDAO.getLatestCustomizationDefinition(CustomizationContext.ORDER_CREATION_CUSTOMIZATION);
    }

    public CustomizationDefinition upsertSelectionCustomization(
        String accountId,
        String userId,
        String crmOpportunityId,
        String zeppaScript,
        int expectedVersion
    ) {
        // we have to test fire successfully before adding customization
        fireSelectionCustomization(accountId, userId, crmOpportunityId, zeppaScript);

        CustomizationDefinitionInput input = ImmutableCustomizationDefinitionInput.builder()
            .customizationContext(CustomizationContext.SELECTION_CUSTOMIZATION)
            .zeppaScript(zeppaScript)
            .build();

        String principalName = CurrentUserProvider.provideAuthPrincipalName();
        return customizationDefinitionDAO.upsertCustomizationDefinition(input, expectedVersion, principalName);
    }

    public CustomizationDefinition upsertOrderCreationCustomization(String orderId, String userId, String zeppaScript, int expectedVersion) {
        // we have to test fire successfully before adding customization
        fireOrderCreationCustomization(orderId, userId, zeppaScript);

        CustomizationDefinitionInput input = ImmutableCustomizationDefinitionInput.builder()
            .customizationContext(CustomizationContext.ORDER_CREATION_CUSTOMIZATION)
            .zeppaScript(zeppaScript)
            .build();

        String principalName = CurrentUserProvider.provideAuthPrincipalName();
        return customizationDefinitionDAO.upsertCustomizationDefinition(input, expectedVersion, principalName);
    }

    public OrderCreationCustomizationOutput runOrderCreationCustomization(OrderCreationCustomizationInput orderCreationCustomizationInput) {
        ImmutableOrderCreationCustomizationOutput.Builder builder = ImmutableOrderCreationCustomizationOutput.builder();
        Optional<CustomizationDefinition> customizationDefinitionOptional = getCurrentOrderCreationCustomization();

        if (customizationDefinitionOptional.isEmpty()) {
            return builder.customizationRunSkipped(false).customizationDefinitionMissing(true).ruleTraces(List.of()).build();
        }

        CustomizationDefinition customizationDefinition = customizationDefinitionOptional.get();
        Order order = orderCreationCustomizationInput.getOrder();
        Account orderAccount = orderCreationCustomizationInput.getAccount();

        CustomField shippingAccountCustomFields = orderCreationCustomizationInput
            .getBillingContactAccount()
            .map(Account::getCustomFields)
            .orElse(CustomField.empty());
        // TODO: populate shipping account address
        AccountBinding shippingAccountBinding = AccountBinding.from(
            orderCreationCustomizationInput.getBillingContactAccount().orElse(null),
            null,
            shippingAccountCustomFields
        );

        Optional<AccountAddress> accountAddressOptional = orderCreationCustomizationInput.getAccountAddress();
        List<PlanBinding> planBindings = getPlanBindings(orderCreationCustomizationInput);
        AccountBinding accountBinding = AccountBinding.from(orderAccount, accountAddressOptional.orElse(null), orderAccount.getCustomFields());
        OrderBinding orderBinding = OrderBinding.from(
            order,
            accountBinding,
            planBindings,
            orderCreationCustomizationInput.getTimeZoneId(),
            ContactBinding.from(
                orderCreationCustomizationInput.getShippingContact().orElse(null),
                orderCreationCustomizationInput.getShippingContact().map(c -> accountBinding).orElse(null)
            ),
            ContactBinding.from(
                orderCreationCustomizationInput.getBillingContact().orElse(null),
                orderCreationCustomizationInput.getBillingContact().map(c -> shippingAccountBinding).orElse(null)
            )
        );
        UserBinding userBinding = orderCreationCustomizationInput.getCurrentUser().map(UserBinding::fromUser).orElseGet(UserBinding::empty);
        List<RuleProcessed> rulesProcessed = ZeppaDriver.fireOrderCreationCustomization(
            orderBinding,
            customizationDefinition.getZeppaScript(),
            userBinding
        );

        // rule actions are run in the order in which they are declared
        // of-course only rules that actually fired are picked up
        List<RuleProcessed> rulesFired = rulesProcessed
            .stream()
            .filter(ruleProcessed -> {
                LOGGER.info("Ran rule {} which fired: {}", ruleProcessed.getRuleName(), ruleProcessed.getFired());
                return ruleProcessed.isFired();
            })
            .toList();

        // if there are inverse actions we need to call them as well
        List<RuleProcessed> inverseRulesFired = rulesProcessed
            .stream()
            .filter(ruleProcessed -> !ruleProcessed.getFired())
            .filter(ruleProcessed -> CollectionUtils.isNotEmpty(ruleProcessed.getActions()))
            .peek(ruleProcessed ->
                LOGGER.info("Rule {} has inverse actions {} that needs to be processed", ruleProcessed.getRuleName(), ruleProcessed.getActions())
            )
            .toList();

        Map<String, OrderLineItem> orderLineItemMap = order
            .getLineItemsNetEffect()
            .stream()
            .collect(Collectors.toMap(line -> line.getId().toString(), Function.identity()));

        List<CustomFieldDefinition> orderFieldDefinitions = customFieldService.getCustomFieldDefinitions(CustomFieldParentType.ORDER);
        List<CustomFieldDefinition> orderLineFieldDefinitions = customFieldService.getCustomFieldDefinitions(CustomFieldParentType.ORDER_ITEM);

        ImmutableList.Builder<RuleTrace> ruleTraces = ImmutableList.builder();

        // first fire all the positive rules
        processRuleActions(
            orderCreationCustomizationInput,
            rulesFired,
            orderLineItemMap,
            orderFieldDefinitions,
            orderLineFieldDefinitions,
            ruleTraces
        );

        // then fire all the negative rules
        processRuleActions(
            orderCreationCustomizationInput,
            inverseRulesFired,
            orderLineItemMap,
            orderFieldDefinitions,
            orderLineFieldDefinitions,
            ruleTraces
        );

        // finally all traces for rules that did not fire just for tracking
        rulesProcessed
            .stream()
            .filter(ruleProcessed -> !ruleProcessed.isFired())
            .filter(ruleProcessed -> CollectionUtils.isEmpty(ruleProcessed.getActions()))
            .forEach(ruleProcessed -> ruleTraces.add(RuleTrace.notFired(ruleProcessed.getRuleName())));
        return builder.customizationDefinitionMissing(false).ruleTraces(ruleTraces.build()).build();
    }

    private void processRuleActions(
        OrderCreationCustomizationInput orderCreationCustomizationInput,
        List<RuleProcessed> rules,
        Map<String, OrderLineItem> orderLineItemMap,
        List<CustomFieldDefinition> orderFieldDefinitions,
        List<CustomFieldDefinition> orderLineFieldDefinitions,
        ImmutableList.Builder<RuleTrace> ruleTraces
    ) {
        rules.forEach(rule -> {
            ImmutableRuleTrace.Builder ruleTraceBuilder = createRuleTraceBuilder(rule);
            var lineItemActionMap = makeLineItemActionMap(orderLineItemMap);
            var lineItemWarningMap = makeLineItemWarningsMap(orderLineItemMap);
            rule
                .getActions()
                .forEach(ruleAction ->
                    processRuleAction(
                        orderCreationCustomizationInput,
                        orderLineItemMap,
                        rule,
                        ruleAction,
                        orderFieldDefinitions,
                        orderLineFieldDefinitions,
                        ruleTraceBuilder,
                        lineItemActionMap,
                        lineItemWarningMap
                    )
                );
            for (var entry : lineItemActionMap.entrySet()) {
                if (CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                ruleTraceBuilder.putOrderLineActionsPerformed(entry.getKey(), entry.getValue());
            }

            for (var entry : lineItemWarningMap.entrySet()) {
                if (CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                ruleTraceBuilder.putOrderLineRuleWarnings(entry.getKey(), entry.getValue());
            }
            ruleTraces.add(ruleTraceBuilder.build());
        });
    }

    @Deprecated // use runSelectionCustomization instead
    public List<Action> runPlanAdditionCustomization(PlanAdditionCustomizationInput customizationInput) {
        if (!featureService.isEnabled(Feature.PLAN_ADDITION_CUSTOMIZATION) || StringUtils.isBlank(customizationInput.accountId())) {
            return List.of();
        }

        SelectionCustomizationInput selectionCustomizationInput = new SelectionCustomizationInput(
            customizationInput.accountId(),
            /* since this is deprecated I am always passing EMPTY here, no point changing the contract */StringUtils.EMPTY,
            /* since this is deprecated I am always passing null here, no point changing the contract */null
        );

        return runSelectionCustomization(selectionCustomizationInput);
    }

    public List<Action> runSelectionCustomization(SelectionCustomizationInput customizationInput) {
        if (StringUtils.isBlank(customizationInput.accountId())) {
            return List.of();
        }

        Optional<CustomizationDefinition> definitionOptional = getCurrentSelectionCustomization();
        if (definitionOptional.isEmpty()) {
            return List.of();
        }

        String userId = CurrentUserProvider.getSubskribeUserId().orElse(null);
        CustomizationDefinition definition = definitionOptional.get();
        List<RuleProcessed> rulesProcessed = fireCustomization(customizationInput, userId, definition);
        rulesProcessed.forEach(ruleProcessed -> LOGGER.info("Rule {} fired: {}", ruleProcessed.getRuleName(), ruleProcessed.getFired()));
        List<RuleProcessed> rulesFired = rulesProcessed.stream().filter(RuleProcessed::isFired).toList();
        List<Action> actions = new ArrayList<>();
        rulesFired.forEach(fired -> {
            List<Map<String, String>> addPlanSelectionFilters = getSelectionFilters(CustomizationAction.ADD_PLAN_SELECTION_FILTER, fired);
            if (CollectionUtils.isNotEmpty(addPlanSelectionFilters)) {
                actions.add(makeSelectionUiAction(CustomizationActionType.ADD_PLAN_APPLY_FILTER, addPlanSelectionFilters, fired.getRuleName()));
            }

            List<Map<String, String>> attributeSelectionFilters = getSelectionFilters(CustomizationAction.ATTRIBUTE_SELECTION_FILTER, fired);
            if (CollectionUtils.isNotEmpty(attributeSelectionFilters)) {
                actions.add(
                    makeSelectionUiAction(CustomizationActionType.ATTRIBUTE_SELECTION_APPLY_FILTER, attributeSelectionFilters, fired.getRuleName())
                );
            }
        });
        return Collections.unmodifiableList(actions);
    }

    private List<RuleProcessed> fireCustomization(SelectionCustomizationInput customizationInput, String userId, CustomizationDefinition definition) {
        // prefer opportunity object in the request over the crm opportunity id
        if (customizationInput.opportunity() != null) {
            return fireSelectionCustomization(
                customizationInput.accountId(),
                userId,
                opportunityMapper.inputToOpportunity(customizationInput.opportunity()),
                definition.getZeppaScript()
            );
        }

        return fireSelectionCustomization(customizationInput.accountId(), userId, customizationInput.crmOpportunityId(), definition.getZeppaScript());
    }

    private List<Map<String, String>> getSelectionFilters(CustomizationAction customizationAction, RuleProcessed fired) {
        return fired
            .getActions()
            .stream()
            .filter(action -> customizationAction == action.getAction() && action.getMetadata() instanceof SelectedAttributes)
            .map(ruleAction -> ((SelectedAttributes) ruleAction.getMetadata()).getAttributeMap().entrySet())
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(Collection::stream)
            .map(entry -> createFilterItem(entry.getKey(), entry.getValue()))
            .toList();
    }

    private static Action makeSelectionUiAction(CustomizationActionType actionType, List<Map<String, String>> itemList, String ruleName) {
        Map<String, Object> actionMetadata = new HashMap<>() {
            {
                put(
                    "defaultFilterModel",
                    new HashMap<>() {
                        {
                            put("logicOperator", "and");
                            put("items", itemList);
                        }
                    }
                );
            }
        };
        try {
            String actionMetadataString = JacksonProvider.defaultMapper().writeValueAsString(actionMetadata);
            return new Action(actionType, Base64.encodeBase64String(actionMetadataString.getBytes(StandardCharsets.UTF_8)), ruleName);
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Customization failed in unexpected way", e);
        }
    }

    public Map<String, String> createFilterItem(String field, String value) {
        return Map.ofEntries(Map.entry("field", field), Map.entry("value", value), Map.entry("operator", "is"));
    }

    @SuppressWarnings("PMD.LooseCoupling")
    private record LineItemActionData(
        Map<String, OrderLineItem> orderLineItemMap,
        RuleAction ruleAction,
        RuleProcessed ruleProcessed,
        Map<String, Charge> chargeMap,
        Map<String, List<RuleActionPerformed>> lineItemActions,
        Map<String, List<String>> lineItemWarnings
    ) {}

    private static ImmutableRuleTrace.Builder createRuleTraceBuilder(RuleProcessed rule) {
        ImmutableRuleTrace.Builder ruleTraceBuilder = ImmutableRuleTrace.builder();
        ruleTraceBuilder.ruleName(rule.getRuleName());
        ruleTraceBuilder.fired(rule.getFired());
        return ruleTraceBuilder;
    }

    private static Map<String, List<RuleActionPerformed>> makeLineItemActionMap(Map<String, OrderLineItem> orderLineItemMap) {
        return orderLineItemMap
            .values()
            .stream()
            .collect(
                Collectors.toMap(
                    CustomizationService::lineItemIdentifier,
                    li -> new ArrayList<RuleActionPerformed>(),
                    // conflict does not matter here we just need one list
                    (oldVal, newVal) -> newVal
                )
            );
    }

    private static Map<String, List<String>> makeLineItemWarningsMap(Map<String, OrderLineItem> orderLineItemMap) {
        return orderLineItemMap
            .values()
            .stream()
            .collect(
                Collectors.toMap(
                    CustomizationService::lineItemIdentifier,
                    li -> new ArrayList<String>(),
                    // conflict does not matter here we just need one list
                    (oldVal, newVal) -> newVal
                )
            );
    }

    private void processRuleAction(
        OrderCreationCustomizationInput input,
        Map<String, OrderLineItem> orderLineItemMap,
        RuleProcessed rule,
        RuleAction ruleAction,
        List<CustomFieldDefinition> orderFieldDefinitions,
        List<CustomFieldDefinition> orderLineFieldDefinitions,
        ImmutableRuleTrace.Builder ruleTraceBuilder,
        Map<String, List<RuleActionPerformed>> lineItemActionMap,
        Map<String, List<String>> lineItemWarningMap
    ) {
        Object metadata = ruleAction.getMetadata();
        Order order = input.getOrder();
        LineItemActionData lineActionInput = new LineItemActionData(
            orderLineItemMap,
            ruleAction,
            rule,
            input.getChargeMap(),
            lineItemActionMap,
            lineItemWarningMap
        );

        switch (ruleAction.getAction()) {
            case FAIL_ORDER_CREATION -> failOrderCreation(rule, ruleAction);
            case FAIL_ORDER_CREATION_ON_SAVE -> input
                .isDryRun()
                .ifPresent(isDryRun -> {
                    // we call this failure into action only if dryrun is false
                    if (BooleanUtils.isFalse(isDryRun)) {
                        failOrderCreation(rule, ruleAction);
                    }
                });
            case APPLY_LINE_ITEM_DISCOUNT -> runApplyLineItemDiscountAction(lineActionInput);
            case APPLY_LINE_ITEM_QUANTITY, FORCE_LINE_ITEM_QUANTITY -> runSetLineItemQuantityAction(lineActionInput);
            case APPLY_LINE_ITEM_PRICE_OVERRIDE, FORCE_LINE_ITEM_PRICE_OVERRIDE -> runPriceOverrideAction(lineActionInput);
            case FORCE_UPLIFT_ON_RAMPS -> forceRampPriceUplifts(order, lineActionInput);
            case APPLY_LINE_ITEM_PRICE_ATTRIBUTE, FORCE_LINE_ITEM_PRICE_ATTRIBUTE -> runPriceAttributeSelectionAction(lineActionInput);
            case APPLY_LINE_ITEM_LIST_PRICE, FORCE_LINE_ITEM_LIST_PRICE -> runSetListPriceAction(lineActionInput);
            case APPLY_ORDER_CUSTOM_FIELD, FORCE_ORDER_CUSTOM_FIELD -> runSetOrderCustomFieldAction(
                order,
                metadata,
                orderFieldDefinitions,
                ruleAction,
                ruleTraceBuilder
            );
            case APPLY_ORDER_LINE_CUSTOM_FIELD, FORCE_ORDER_LINE_CUSTOM_FIELD -> runSetOrderLineCustomFieldAction(
                lineActionInput,
                orderLineFieldDefinitions
            );
            case FORCE_START_DATE_TO_END_DATE -> runForceStartDateToEndDate(lineActionInput);
            case REMOVE_LINE_ITEM_PREDEFINED_DISCOUNT -> runRemoveLinePredefinedDiscountAction(lineActionInput);
            case ADD_PREDEFINED_DISCOUNT -> runAddPredefinedDiscountAction(order, lineActionInput, ruleTraceBuilder);
            case REMOVE_PREDEFINED_DISCOUNT -> runRemovePredefinedDiscountAction(order, lineActionInput, ruleTraceBuilder);
            case FORCE_DOCUMENT_TEMPLATE -> runForceDocumentTemplateAction(order, metadata, ruleTraceBuilder);
            case REMOVE_DOCUMENT_TEMPLATE -> runRemoveDocumentTemplateAction(order, metadata, ruleTraceBuilder);
            case ADD_PREDEFINED_TERM -> runAddPredefinedTermAction(order, metadata, ruleTraceBuilder);
            case REMOVE_PREDEFINED_TERM -> runRemovePredefinedTermAction(order, metadata, ruleTraceBuilder);
            case FORCE_PAYMENT_TERM -> runForcePaymentTermAction(order, metadata, ruleTraceBuilder);
            case FORCE_BILLING_CYCLE -> runForceBillingCycleAction(order, metadata, ruleTraceBuilder);
            case APPLY_RENEWAL_UPLIFT, FORCE_RENEWAL_UPLIFT -> runSetRenewalUpliftAction(lineActionInput);
            default -> LOGGER.warn(
                ORDER_CREATION_CUSTOMIZATION,
                "Unknown action {} cannot be handled in order creation customization",
                ruleAction.getAction()
            );
        }
    }

    private void runForcePaymentTermAction(Order order, Object metadata, ImmutableRuleTrace.Builder traceBuilder) {
        if (Objects.nonNull(metadata) && metadata instanceof Integer dueInDays) {
            Optional<PaymentTerm> paymentTermOptional = PaymentTerm.fromDueInDays(dueInDays);
            if (paymentTermOptional.isPresent() && paymentTermsService.isValidPaymentTerm(paymentTermOptional.get())) {
                order.setPaymentTerm(paymentTermOptional.get());
                String message = String.format("order payment term was forced to be %s", paymentTermOptional.get());
                traceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(CustomizationAction.FORCE_PAYMENT_TERM, message));
                logAction(message);
            } else {
                String message = String.format("invalid due in days %s supplied, payment terms cannot be set", dueInDays);
                traceBuilder.addOrderRuleWarnings(message);
                LOGGER.warn(ORDER_CREATION_CUSTOMIZATION, message);
            }
        }
    }

    private void runForceBillingCycleAction(Order order, Object metadata, ImmutableRuleTrace.Builder traceBuilder) {
        if (Objects.nonNull(metadata) && metadata instanceof String billingCycle && StringUtils.isNotBlank(billingCycle)) {
            boolean validBillingCycle = Set.of(Cycle.values()).stream().map(Enum::name).collect(Collectors.toSet()).contains(billingCycle);
            if (validBillingCycle) {
                Cycle cycle = Cycle.valueOf(billingCycle);
                order.setBillingCycle(new Recurrence(cycle, 1));
                String message = String.format("order billing cycle was forced to be %s", billingCycle);
                traceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(CustomizationAction.FORCE_BILLING_CYCLE, message));
                logAction(message);
            } else {
                String warning = String.format("invalid billing cycle %s supplied, billing cycle will not be set", billingCycle);
                traceBuilder.addOrderRuleWarnings(warning);
                LOGGER.warn(ORDER_CREATION_CUSTOMIZATION, warning);
            }
        }
    }

    private void runPriceAttributeSelectionAction(LineItemActionData lineActionInput) {
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        RuleAction ruleAction = lineActionInput.ruleAction();
        Map<String, Charge> chargeMap = lineActionInput.chargeMap();
        Object metadata = ruleAction.getMetadata();
        boolean force = ruleAction.getAction() == CustomizationAction.FORCE_LINE_ITEM_PRICE_ATTRIBUTE;

        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemPriceAttribute itemPriceAttribute &&
            orderLineItemMap.containsKey(itemPriceAttribute.getLineItemId()) &&
            MapUtils.isNotEmpty(itemPriceAttribute.getAttributeMap())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(itemPriceAttribute.getLineItemId());

            if (!ACTIONS_PRICE_ATTRIBUTE_ALLOWED.contains(lineItem.getAction())) {
                String message = String.format(
                    "line item action %s not in allowed action types %s for price attribute setting",
                    lineItem.getAction(),
                    ACTIONS_PRICE_ATTRIBUTE_ALLOWED
                );
                addLineActionWarning(lineActionInput, lineItem, message);
                return;
            }

            if (!lineItemChargePresent(chargeMap, lineItem)) {
                return;
            }

            Charge charge = chargeMap.get(lineItem.getChargeId());

            if (StringUtils.isBlank(charge.getRateCardId())) {
                String message = String.format("charge %s does not have rate card but attribute selection action called", lineItem.getChargeId());
                addLineActionWarning(lineActionInput, lineItem, message);
                return;
            }

            Optional<RateCard> rateCardOptional = rateCardService.getRateCard(charge.getRateCardId());
            // TODO: turn this to invariant check
            if (rateCardOptional.isEmpty()) {
                String message = String.format(
                    "charge %s has rate card id %s but rate card object is missing",
                    charge.getChargeId(),
                    charge.getRateCardId()
                );
                addLineActionWarning(lineActionInput, lineItem, message);
                return;
            }

            List<AttributeReference> finalList = new ArrayList<>();
            for (Map.Entry<String, String> entry : itemPriceAttribute.getAttributeMap().entrySet()) {
                String attributeName = entry.getKey();
                String attributeValue = entry.getValue();
                Optional<PriceAttribute> attributeDefOptional = rateCardService.getOptionalPriceAttributeByName(attributeName);
                if (attributeDefOptional.isEmpty()) {
                    addLineActionWarning(
                        lineActionInput,
                        lineItem,
                        String.format("price attribute by name: %s does not exist no action will be performed", attributeName)
                    );
                    // need to not verify anything else as price attribute is not found
                    // and the combination will not be found in the rate card
                    return;
                }
                finalList.add(new AttributeReference(attributeDefOptional.get().getId(), attributeValue));
            }

            RateCard rateCard = rateCardOptional.get();
            AttributeReferences references = AttributeReferences.wrap(finalList);
            Optional<BigDecimal> price = rateCard.resolvePrice(references);

            if (price.isEmpty()) {
                addLineActionWarning(
                    lineActionInput,
                    lineItem,
                    String.format("attribute references %s not present in rate card %s", references, rateCard.getId())
                );
                // if the combination is not found in the rate card we cannot proceed either
                // we early terminate
                return;
            }

            //finally set the line item attribute combination
            if (force || CollectionUtils.isEmpty(lineItem.getAttributeReferences())) {
                lineItem.setAttributeReferences(references.getReferencesInOrder());
                String message = String.format("line item %s price attribute references applied as %s", lineItem.getOrderLineId(), references);
                addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                logAction(message);
                return;
            }
            // just add warning
            String message = String.format(
                "line item %s price attribute already set as %s",
                lineItem.getOrderLineId(),
                lineItem.getAttributeReferences()
            );
            addLineActionWarning(lineActionInput, lineItem, message);
        }
    }

    private void runSetListPriceAction(LineItemActionData lineActionInput) {
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        RuleAction ruleAction = lineActionInput.ruleAction();
        Map<String, Charge> chargeMap = lineActionInput.chargeMap();
        Object metadata = ruleAction.getMetadata();
        boolean force = ruleAction.getAction() == CustomizationAction.FORCE_LINE_ITEM_LIST_PRICE;

        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemPriceOverride listPriceMetadata &&
            orderLineItemMap.containsKey(listPriceMetadata.getLineItemId()) &&
            Objects.nonNull(listPriceMetadata.getPriceOverride())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(listPriceMetadata.getLineItemId());

            if (!lineItemChargePresent(chargeMap, lineItem)) {
                return;
            }

            if (!chargeMap.get(lineItem.getChargeId()).isCustom()) {
                addLineActionWarning(
                    lineActionInput,
                    lineItem,
                    String.format("charge id %s is not custom and yet the list price was specified", lineItem.getChargeId())
                );
                return;
            }

            if (force) {
                lineItem.setListUnitPrice(listPriceMetadata.getPriceOverride());
                String message = String.format(
                    "line item %s list price forced to %s",
                    lineItem.getOrderLineId(),
                    listPriceMetadata.getPriceOverride()
                );
                addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                logAction(message);
                return;
            }

            if (Objects.isNull(lineItem.getListUnitPrice()) || Numbers.isZero(lineItem.getListUnitPrice())) {
                lineItem.setListUnitPrice(listPriceMetadata.getPriceOverride());
                String message = String.format(
                    "line item %s price override applied as %s",
                    lineItem.getOrderLineId(),
                    listPriceMetadata.getPriceOverride()
                );
                addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                logAction(message);
                return;
            }
            addLineActionWarning(lineActionInput, lineItem, "price override already set nothing to do");
        }
    }

    private void runPriceOverrideAction(LineItemActionData lineActionInput) {
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        RuleAction ruleAction = lineActionInput.ruleAction();
        Map<String, Charge> chargeMap = lineActionInput.chargeMap();
        Object metadata = ruleAction.getMetadata();
        boolean force = ruleAction.getAction() == CustomizationAction.FORCE_LINE_ITEM_PRICE_OVERRIDE;
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemPriceOverride priceOverrideMetadata &&
            orderLineItemMap.containsKey(priceOverrideMetadata.getLineItemId()) &&
            Objects.nonNull(priceOverrideMetadata.getPriceOverride())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(priceOverrideMetadata.getLineItemId());

            if (!lineItemChargePresent(chargeMap, lineItem)) {
                String message = String.format("could not find charge in charge map for line item with charge: %s", lineItem.getChargeId());
                addLineActionWarning(lineActionInput, lineItem, message);
                return;
            }

            if (!chargeMap.get(lineItem.getChargeId()).getIsListPriceEditable()) {
                String message = String.format("charge id %s is not list price editable but list price override specified", lineItem.getChargeId());
                addLineActionWarning(lineActionInput, lineItem, message);
                return;
            }

            if (force) {
                lineItem.setListPriceOverrideRatio(priceOverrideMetadata.getPriceOverride());
                String message = String.format("line item price override forced to %s", priceOverrideMetadata.getPriceOverride());
                addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                logAction(message);
                return;
            }

            // set override ration only when not already set this is just apply action
            if (Objects.isNull(lineItem.getListPriceOverrideRatio())) {
                lineItem.setListPriceOverrideRatio(priceOverrideMetadata.getPriceOverride());
                String message = String.format("line item price override applied as %s", priceOverrideMetadata.getPriceOverride());
                addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                logAction(message);
            }
        }
    }

    private void forceRampPriceUplifts(Order order, LineItemActionData lineActionInput) {
        RuleAction ruleAction = lineActionInput.ruleAction();
        Object metadata = ruleAction.getMetadata();
        Map<String, Charge> chargeMap = lineActionInput.chargeMap();

        if (Objects.nonNull(metadata) && metadata instanceof BigDecimal upliftPercent) {
            // get ramp line items where list prices are editable
            List<OrderLineItem> targetRampLines = order
                .getLineItemsNetEffect()
                .stream()
                .filter(
                    lineItem ->
                        lineItem.getIsRamp() &&
                        chargeMap.containsKey(lineItem.getChargeId()) &&
                        chargeMap.get(lineItem.getChargeId()).getIsListPriceEditable() &&
                        ACTIONS_RAMP_OVERRIDE_ALLOWED.contains(lineItem.getAction())
                )
                .toList();

            // group ramps by ramp group id and then process each ramp group for price increment
            targetRampLines
                .stream()
                .collect(Collectors.groupingBy(OrderLineItem::getRampGroupId))
                .values()
                .forEach(groupedRampLines -> forceUpliftInIncrements(groupedRampLines, upliftPercent));
        }
    }

    // force ramp price increment to a group of ramp line items
    //  - order the line items by effective date
    //  - the first line item's list price is not changed
    //  - list price is increased by upliftPercent% compared to the previous line item
    private void forceUpliftInIncrements(List<OrderLineItem> groupedRampLines, BigDecimal upliftPercent) {
        if (groupedRampLines.size() < 2) {
            LOGGER.info("ramp price increment cannot be performed since there are not enough line items");
            return;
        }
        groupedRampLines.sort(Comparator.comparing(OrderLineItem::getEffectiveDate));
        BigDecimal multiplierRatio = Numbers.scaledDivide(upliftPercent, ONE_HUNDRED).add(BigDecimal.ONE);
        BigDecimal runningRatio = BigDecimal.ONE;
        for (var lineItem : groupedRampLines) {
            lineItem.setListPriceOverrideRatio(runningRatio);
            String message = String.format("price override set to %s on line item %s", runningRatio, lineItem.getId());
            runningRatio = runningRatio.multiply(multiplierRatio);
            logAction(message);
        }
    }

    private static void failOrderCreation(RuleProcessed rule, RuleAction ruleAction) {
        String message = String.format("Rule \"%s\" failed order creation, reason: \"%s\"", rule.getRuleName(), ruleAction.getMetadata());
        logAction("Failed order creation");
        throw new InvalidInputException(message);
    }

    private void runRemoveDocumentTemplateAction(Order order, Object metadata, ImmutableRuleTrace.Builder traceBuilder) {
        if (Objects.nonNull(metadata) && metadata instanceof String templateName && StringUtils.isNotBlank(templateName)) {
            templateGetService
                .getMasterTemplateByNameAndType(templateName, DocumentTemplateType.ORDER)
                .ifPresentOrElse(
                    template -> {
                        // if the template id matches the one in order clear it out
                        if (template.getId().equals(order.getDocumentMasterTemplateId())) {
                            order.setDocumentMasterTemplateId(null);
                            String message = String.format("template with name %s id %s removed from order", template.getName(), template.getId());
                            traceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(CustomizationAction.REMOVE_DOCUMENT_TEMPLATE, message));
                            logAction(message);
                        }
                    },
                    () -> traceBuilder.addOrderRuleWarnings(String.format("master template with name %s not found", templateName))
                );
        }
    }

    private void runForceDocumentTemplateAction(Order order, Object metadata, ImmutableRuleTrace.Builder traceBuilder) {
        if (Objects.nonNull(metadata) && metadata instanceof String templateName && StringUtils.isNotBlank(templateName)) {
            templateGetService
                .getMasterTemplateByNameAndType(templateName, DocumentTemplateType.ORDER)
                .ifPresentOrElse(
                    // present
                    template -> {
                        order.setDocumentMasterTemplateId(template.getId());
                        String message = String.format("master template with name %s id %s forced on order", template.getName(), template.getId());
                        traceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(CustomizationAction.FORCE_DOCUMENT_TEMPLATE, message));
                        logAction(message);
                    },
                    // or else
                    () -> traceBuilder.addOrderRuleWarnings(String.format("master template with name %s not found", templateName))
                );
        }
    }

    private void runSetOrderLineCustomFieldAction(LineItemActionData lineActionInput, List<CustomFieldDefinition> lineItemDefinitions) {
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        RuleAction ruleAction = lineActionInput.ruleAction();
        Object metadata = ruleAction.getMetadata();

        if (
            Objects.nonNull(metadata) &&
            metadata instanceof OrderLineCustomField fieldValue &&
            StringUtils.isNotBlank(fieldValue.getLineItemId()) &&
            orderLineItemMap.containsKey(fieldValue.getLineItemId()) &&
            StringUtils.isNotBlank(fieldValue.getIdentifier()) &&
            CollectionUtils.isNotEmpty(fieldValue.getValues())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(fieldValue.getLineItemId());

            Optional<CustomFieldDefinition> matchedDefinition = lineItemDefinitions
                .stream()
                .filter(def -> def.getFieldName().equals(fieldValue.getIdentifier()) || def.getCustomFieldId().equals(fieldValue.getIdentifier()))
                .findFirst();

            if (matchedDefinition.isEmpty()) {
                addLineActionWarning(
                    lineActionInput,
                    lineItem,
                    String.format("custom field with identifier %s not found", fieldValue.getIdentifier())
                );
                return;
            }

            CustomFieldDefinition definition = matchedDefinition.get();
            Optional<CustomFieldValue> valueOptional = CustomFieldValue.fromDefinitionWithValueAndSelections(definition, fieldValue.getValues());

            valueOptional.ifPresentOrElse(
                valueBeingSet -> {
                    if (CollectionUtils.isEmpty(lineItem.getCustomFields())) {
                        CustomField customField = customFieldService.getCustomFieldsWithOptionalParent(CustomFieldParentType.ORDER, Optional.empty());
                        Map<String, CustomFieldValue> entries = new HashMap<>(customField.getEntries());
                        entries.put(definition.getCustomFieldId(), valueBeingSet);
                        lineItem.setCustomFieldEntriesFromCustomFieldObject(new CustomField(entries));
                        String message = String.format(
                            "order line item custom field value set for identifier: %s to value %s",
                            fieldValue.getIdentifier(),
                            fieldValue.getValues()
                        );
                        addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                        logAction(message);
                        return;
                    }

                    boolean force = ruleAction.getAction() == CustomizationAction.FORCE_ORDER_LINE_CUSTOM_FIELD;
                    Map<String, CustomFieldValue> entries = new HashMap<>(
                        lineItem.getCustomFields().stream().collect(Collectors.toMap(CustomFieldEntry::getId, Function.identity()))
                    );

                    if (force || !entries.containsKey(definition.getCustomFieldId())) {
                        entries.put(definition.getCustomFieldId(), valueBeingSet);
                        lineItem.setCustomFieldEntriesFromCustomFieldObject(new CustomField(entries));
                        String message = String.format(
                            "order line item custom field value set for identifier: %s to value %s",
                            fieldValue.getIdentifier(),
                            fieldValue.getValues()
                        );
                        addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                        logAction(message);
                        return;
                    }

                    // not a force action we need to set the value then
                    CustomFieldValue toUpdate = entries.get(definition.getCustomFieldId());
                    if (StringUtils.isBlank(toUpdate.getValue())) {
                        entries.put(definition.getCustomFieldId(), valueBeingSet);
                        lineItem.setCustomFieldEntriesFromCustomFieldObject(new CustomField(entries));
                        String message = String.format(
                            "order line item custom field value set for identifier: %s to value %s",
                            fieldValue.getIdentifier(),
                            fieldValue.getValues()
                        );
                        addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                        logAction(message);
                    }
                },
                () ->
                    addLineActionWarning(
                        lineActionInput,
                        lineItem,
                        String.format("could not create custom field %s from values provided %s", fieldValue.getIdentifier(), fieldValue.getValues())
                    )
            );
        }
    }

    private void runSetOrderCustomFieldAction(
        Order order,
        Object metadata,
        List<CustomFieldDefinition> orderFieldDefinitions,
        RuleAction ruleAction,
        ImmutableRuleTrace.Builder traceBuilder
    ) {
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof com.subskribe.zeppa.customizations.ordercreation.CustomFieldValue fieldValue &&
            StringUtils.isNotBlank(fieldValue.getIdentifier()) &&
            CollectionUtils.isNotEmpty(fieldValue.getValues())
        ) {
            Optional<CustomFieldDefinition> matchedDefinition = orderFieldDefinitions
                .stream()
                .filter(def -> def.getFieldName().equals(fieldValue.getIdentifier()) || def.getCustomFieldId().equals(fieldValue.getIdentifier()))
                .findFirst();

            if (matchedDefinition.isEmpty()) {
                String message = String.format("could not find order custom field definition with identifier %s", fieldValue.getIdentifier());
                traceBuilder.addOrderRuleWarnings(message);
                LOGGER.warn(ORDER_CREATION_CUSTOMIZATION, message);
                return;
            }

            CustomFieldDefinition definition = matchedDefinition.get();
            Optional<CustomFieldValue> valueOptional = CustomFieldValue.fromDefinitionWithValueAndSelections(definition, fieldValue.getValues());
            valueOptional.ifPresentOrElse(
                valueBeingSet -> {
                    if (order.getCustomFields() == null || MapUtils.isEmpty(order.getCustomFields().getEntries())) {
                        CustomField customField = customFieldService.getCustomFieldsWithOptionalParent(CustomFieldParentType.ORDER, Optional.empty());
                        Map<String, CustomFieldValue> entries = new HashMap<>(customField.getEntries());
                        entries.put(definition.getCustomFieldId(), valueBeingSet);
                        order.setCustomFields(new CustomField(entries));
                        String message = String.format("order custom field value set for identifier: %s", fieldValue.getIdentifier());
                        traceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(ruleAction.getAction(), message));
                        logAction(message);
                        return;
                    }

                    boolean force = ruleAction.getAction() == CustomizationAction.FORCE_ORDER_CUSTOM_FIELD;
                    Map<String, CustomFieldValue> entries = new HashMap<>(order.getCustomFields().getEntries());

                    if (force || !entries.containsKey(definition.getCustomFieldId())) {
                        entries.put(definition.getCustomFieldId(), valueBeingSet);
                        order.setCustomFields(new CustomField(entries));
                        String message = String.format("order custom field value set for identifier: %s", fieldValue.getIdentifier());
                        traceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(ruleAction.getAction(), message));
                        logAction(message);
                        return;
                    }

                    // not a force action we need to set the value then
                    CustomFieldValue toUpdate = entries.get(definition.getCustomFieldId());
                    if (StringUtils.isBlank(toUpdate.getValue())) {
                        entries.put(definition.getCustomFieldId(), valueBeingSet);
                        order.setCustomFields(new CustomField(entries));
                        String message = String.format("order custom field value set for identifier: %s", fieldValue.getIdentifier());
                        traceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(ruleAction.getAction(), message));
                        logAction(message);
                    }
                },
                () ->
                    traceBuilder.addOrderRuleWarnings(
                        String.format(
                            "could not create custom field value for definition: %s with value(s): %s",
                            definition.getCustomFieldId(),
                            fieldValue.getValues()
                        )
                    )
            );
        }
    }

    private static void runSetLineItemQuantityAction(LineItemActionData lineActionInput) {
        Map<String, Charge> chargeMap = lineActionInput.chargeMap();
        Object metadata = lineActionInput.ruleAction().getMetadata();
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        RuleAction ruleAction = lineActionInput.ruleAction();

        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemQuantity lineQuantityMetadata &&
            orderLineItemMap.containsKey(lineQuantityMetadata.getLineItemId())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(lineQuantityMetadata.getLineItemId());

            if (!chargeMap.containsKey(lineItem.getChargeId())) {
                String message = String.format(
                    "charge id: %s of line item missing from charge map no action will be performed",
                    lineItem.getChargeId()
                );
                addLineActionWarning(lineActionInput, lineItem, message);
                return;
            }

            long zeppaSetQuantity = lineQuantityMetadata.getQuantity();

            Charge relevantCharge = chargeMap.get(lineItem.getChargeId());
            if (relevantCharge.getMinQuantity() != null && relevantCharge.getMinQuantity().compareTo(zeppaSetQuantity) > 0) {
                String message = String.format(
                    "charge %s minimum quantity %s is greater than action quantity %s no action will be taken",
                    lineItem.getChargeId(),
                    relevantCharge.getMinQuantity(),
                    zeppaSetQuantity
                );
                addLineActionWarning(lineActionInput, lineItem, message);
                return;
            }

            if (relevantCharge.getMaxQuantity() != null && relevantCharge.getMaxQuantity().compareTo(zeppaSetQuantity) < 0) {
                String message = String.format(
                    "charge %s maximum quantity %s is less than action quantity %s no action will be taken",
                    lineItem.getChargeId(),
                    relevantCharge.getMaxQuantity(),
                    zeppaSetQuantity
                );
                addLineActionWarning(lineActionInput, lineItem, message);
                return;
            }

            // force or apply action
            boolean force = ruleAction.getAction() == CustomizationAction.FORCE_LINE_ITEM_QUANTITY;

            if (lineItem.getQuantity() == 0 || force) {
                lineItem.setQuantity(zeppaSetQuantity);
                String action = force ? "forced" : "applied";
                String message = String.format("quantity %s for line item to be %s", action, lineItem.getQuantity());
                addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                logAction(message);
                return;
            }

            addLineActionWarning(lineActionInput, lineItem, "line item quantity already set action will not take effect");
        }
    }

    private void runForceStartDateToEndDate(LineItemActionData lineActionInput) {
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        Object metadata = lineActionInput.ruleAction().getMetadata();
        RuleAction ruleAction = lineActionInput.ruleAction();
        if (Objects.nonNull(metadata) && metadata instanceof String lineItemId && orderLineItemMap.containsKey(lineItemId)) {
            OrderLineItem lineItem = orderLineItemMap.get(lineItemId);
            lineItem.setEffectiveDate(lineItem.getEndDate());
            String message = String.format("line item effective date forced to %s in UTC", lineItem.getEffectiveDate());
            addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
            logAction(message);
        }
    }

    private static void runApplyLineItemDiscountAction(LineItemActionData lineActionInput) {
        Object metadata = lineActionInput.ruleAction().getMetadata();
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        RuleProcessed rule = lineActionInput.ruleProcessed();
        RuleAction ruleAction = lineActionInput.ruleAction();
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemDiscount lineDiscountMetadata &&
            orderLineItemMap.containsKey(lineDiscountMetadata.getLineItemId())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(lineDiscountMetadata.getLineItemId());
            boolean shouldAddDiscount = CollectionUtils.isEmpty(lineItem.getDiscounts());
            if (shouldAddDiscount) {
                DiscountDetail discountDetail = new DiscountDetail();
                discountDetail.setId(String.format("Rule %s discount", rule.getRuleName()));
                discountDetail.setPercent(lineDiscountMetadata.getDiscount());
                List<DiscountDetail> newList = new ArrayList<>(lineItem.getDiscounts() == null ? List.of() : lineItem.getDiscounts());
                newList.add(discountDetail);
                lineItem.setDiscounts(newList);
                String message = String.format("discount with value %s added to line item", lineDiscountMetadata.getDiscount());
                addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                logAction(message);
                return;
            }
            addLineActionWarning(lineActionInput, lineItem, "discount already present on the line item discount will not be applied");
        }
    }

    private void runRemoveLinePredefinedDiscountAction(LineItemActionData lineActionInput) {
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        Object metadata = lineActionInput.ruleAction().getMetadata();
        RuleAction ruleAction = lineActionInput.ruleAction();
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemPredefinedDiscount itemPredefinedDiscount &&
            orderLineItemMap.containsKey(itemPredefinedDiscount.getLineItemId()) &&
            StringUtils.isNotEmpty(itemPredefinedDiscount.getDiscountName())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(itemPredefinedDiscount.getLineItemId());
            if (CollectionUtils.isEmpty(lineItem.getPredefinedDiscounts())) {
                addLineActionWarning(lineActionInput, lineItem, "line item has no predefined discount(s) so cannot remove any");
                return;
            }
            discountService
                .getDiscountByName(itemPredefinedDiscount.getDiscountName())
                .ifPresentOrElse(
                    discount -> {
                        List<TenantDiscountLineItem> discountsFiltered = lineItem
                            .getPredefinedDiscounts()
                            .stream()
                            .filter(lineDiscount -> !discount.getDiscountId().equals(lineDiscount.getId()))
                            .toList();
                        // if the size is the same nothing got filtered so nothing was removed
                        if (discountsFiltered.size() != lineItem.getPredefinedDiscounts().size()) {
                            lineItem.setPredefinedDiscounts(discountsFiltered);
                            String message = String.format("removed predefined discount with name %s from the line", discount.getName());
                            addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
                            logAction(message);
                        }
                    },
                    () ->
                        addLineActionWarning(
                            lineActionInput,
                            lineItem,
                            String.format("could not find pre-defined discount with name: %s", itemPredefinedDiscount.getDiscountName())
                        )
                );
        }
    }

    private void runAddPredefinedTermAction(Order order, Object metadata, ImmutableRuleTrace.Builder traceBuilder) {
        if (Objects.nonNull(metadata) && metadata instanceof String termName && StringUtils.isNotBlank(termName)) {
            templateGetService
                .getTemplateByNameAndType(termName, DocumentTemplateType.ORDER, Optional.of(DocumentTemplateStatus.ACTIVE)) // add ACTIVE terms only
                .ifPresentOrElse(
                    termTemplate -> {
                        List<String> templateIds = order.getOrderFormTemplateIds();
                        if (CollectionUtils.isEmpty(templateIds)) {
                            order.setOrderFormTemplateIds(List.of(termTemplate.getTemplateId()));
                            String message = String.format(
                                "added template with name %s id %s to order",
                                termTemplate.getName(),
                                termTemplate.getTemplateId()
                            );
                            traceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(CustomizationAction.ADD_PREDEFINED_TERM, message));
                            logAction(message);
                        }

                        if (!templateIds.contains(termTemplate.getTemplateId())) {
                            List<String> newList = new ArrayList<>(templateIds);
                            newList.add(termTemplate.getTemplateId());
                            order.setOrderFormTemplateIds(newList);
                            String message = String.format(
                                "added template with name %s id %s to order",
                                termTemplate.getName(),
                                termTemplate.getTemplateId()
                            );
                            traceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(CustomizationAction.ADD_PREDEFINED_TERM, message));
                            logAction(message);
                        }
                    },
                    () -> traceBuilder.addOrderRuleWarnings(String.format("template with name %s not found", termName))
                );
        }
    }

    private void runRemovePredefinedTermAction(Order order, Object metadata, ImmutableRuleTrace.Builder traceBuilder) {
        if (Objects.nonNull(metadata) && metadata instanceof String termName && StringUtils.isNotBlank(termName)) {
            templateGetService
                .getTemplateByNameAndType(termName, DocumentTemplateType.ORDER, Optional.empty())
                .ifPresentOrElse(
                    termTemplate -> {
                        List<String> templateIds = order.getOrderFormTemplateIds();
                        // if the template ids empty nothing to do
                        if (CollectionUtils.isEmpty(templateIds)) {
                            traceBuilder.addOrderRuleWarnings("order does not have any template ids nothing to remove");
                            return;
                        }

                        if (!templateIds.contains(termTemplate.getTemplateId())) {
                            traceBuilder.addOrderRuleWarnings(
                                String.format("template with id %s not in order nothing to remove", termTemplate.getTemplateId())
                            );
                            return;
                        }

                        List<String> filtered = templateIds.stream().filter(templateId -> !templateId.equals(termTemplate.getTemplateId())).toList();
                        order.setOrderFormTemplateIds(filtered);
                        String message = String.format(
                            "removed predefined term with name %s id %s from order",
                            termTemplate.getName(),
                            termTemplate.getTemplateId()
                        );
                        traceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(CustomizationAction.REMOVE_PREDEFINED_TERM, message));
                        logAction(message);
                    },
                    () -> traceBuilder.addOrderRuleWarnings(String.format("template with name %s not found", termName))
                );
        }
    }

    private void runAddPredefinedDiscountAction(Order order, LineItemActionData lineActionInput, ImmutableRuleTrace.Builder ruleTraceBuilder) {
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        Object metadata = lineActionInput.ruleAction().getMetadata();
        RuleAction ruleAction = lineActionInput.ruleAction();

        if (Objects.nonNull(metadata) && metadata instanceof String discountName && StringUtils.isNotBlank(discountName)) {
            discountService
                .getDiscountByName(discountName)
                .ifPresentOrElse(
                    discount -> {
                        if (shouldAddDiscount(discount, order)) {
                            List<TenantDiscount> discountList = CollectionUtils.isEmpty(order.getPredefinedDiscounts())
                                ? new ArrayList<>()
                                : new ArrayList<>(order.getPredefinedDiscounts());
                            discountList.add(toTenantDiscount(discount));
                            order.setPredefinedDiscounts(discountList);
                            String message = String.format(
                                "added pre-defined discount with name %s and id %s to order",
                                discount.getName(),
                                discount.getDiscountId()
                            );
                            ruleTraceBuilder.addOrderActionsPerformed(RuleActionPerformed.of(ruleAction.getAction(), message));
                            logAction(message);

                            orderLineItemMap
                                .values()
                                .forEach(lineItem -> {
                                    if (lineItem.getAction() != null && ACTIONS_PREDEFINED_DISCOUNT_ALLOWED.contains(lineItem.getAction())) {
                                        List<TenantDiscountLineItem> newList = CollectionUtils.isEmpty(lineItem.getPredefinedDiscounts())
                                            ? new ArrayList<>()
                                            : new ArrayList<>(lineItem.getPredefinedDiscounts());
                                        newList.add(toTenantDiscountLine(discount));
                                        String lineMessage = String.format("added pre-defined discount %s to line item", discount.getName());
                                        addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), lineMessage);
                                        lineItem.setPredefinedDiscounts(newList);
                                    }
                                });
                            // NOTE: we return here
                            return;
                        }
                        // add warning the discount is already in the order
                        ruleTraceBuilder.addOrderRuleWarnings(String.format("discount with name %s already in the order", discountName));
                    },
                    () -> ruleTraceBuilder.addOrderRuleWarnings(String.format("discount with name %s not found", discountName))
                );
        }
    }

    private void runRemovePredefinedDiscountAction(Order order, LineItemActionData lineActionInput, ImmutableRuleTrace.Builder ruleTraceBuilder) {
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        Object metadata = lineActionInput.ruleAction().getMetadata();
        if (Objects.nonNull(metadata) && metadata instanceof String discountName && StringUtils.isNotBlank(discountName)) {
            discountService
                .getDiscountByName(discountName)
                .ifPresentOrElse(
                    discount -> {
                        if (shouldRemoveDiscount(discount, order)) {
                            List<TenantDiscount> filtered = order
                                .getPredefinedDiscounts()
                                .stream()
                                .filter(tenantDiscount -> !tenantDiscount.getId().equals(discount.getDiscountId()))
                                .toList();
                            order.setPredefinedDiscounts(filtered);
                            logAction(String.format("potentially removed discount %s from order %s", discount.getDiscountId(), order.getOrderId()));

                            if (MapUtils.isEmpty(orderLineItemMap)) {
                                return;
                            }

                            removeDiscountFromLineItems(orderLineItemMap, discount);
                            return;
                        }
                        ruleTraceBuilder.addOrderRuleWarnings(String.format("discount with name %s not found in order", discountName));
                    },
                    () -> ruleTraceBuilder.addOrderRuleWarnings(String.format("pre-defined discount with name %s not found", discountName))
                );
        }
    }

    private void runSetRenewalUpliftAction(LineItemActionData lineActionInput) {
        Map<String, OrderLineItem> orderLineItemMap = lineActionInput.orderLineItemMap();
        RuleAction ruleAction = lineActionInput.ruleAction();
        Map<String, Charge> chargeMap = lineActionInput.chargeMap();
        Object metadata = ruleAction.getMetadata();
        boolean force = ruleAction.getAction() == CustomizationAction.FORCE_RENEWAL_UPLIFT;
        if (
            Objects.nonNull(metadata) &&
            metadata instanceof LineItemRenewalUplift renewalUpliftMetadata &&
            orderLineItemMap.containsKey(renewalUpliftMetadata.getLineItemId()) &&
            Objects.nonNull(renewalUpliftMetadata.getUpliftRatio())
        ) {
            OrderLineItem lineItem = orderLineItemMap.get(renewalUpliftMetadata.getLineItemId());

            if (!lineItemChargePresent(chargeMap, lineItem)) {
                return;
            }

            lineItem.setRenewalUpliftRatio(renewalUpliftMetadata.getUpliftRatio());

            if (force) {
                lineItem.setListPriceOverrideRatio(null); // reset user provided override ratio if forcing action
            }

            String message = String.format(
                "line item %s price override set to %s with force = %s",
                lineItem.getOrderLineId(),
                renewalUpliftMetadata.getUpliftRatio(),
                force
            );
            addLineActionTrace(lineActionInput, lineItem, ruleAction.getAction(), message);
        }
    }

    private static boolean lineItemChargePresent(Map<String, Charge> chargeMap, OrderLineItem lineItem) {
        if (StringUtils.isBlank(lineItem.getChargeId())) {
            String message = String.format("charge id blank for line item %s", lineItem.getId());
            LOGGER.warn(ORDER_CREATION_CUSTOMIZATION, message);
            return false;
        }

        if (!chargeMap.containsKey(lineItem.getChargeId())) {
            String message = String.format("charge id not present in charge map for %s charge id %s", lineItem.getId(), lineItem.getChargeId());
            LOGGER.warn(ORDER_CREATION_CUSTOMIZATION, message);
            return false;
        }

        return true;
    }

    private static void removeDiscountFromLineItems(Map<String, OrderLineItem> orderLineItemMap, Discount discount) {
        orderLineItemMap
            .values()
            .stream()
            .filter(
                line ->
                    CollectionUtils.isNotEmpty(line.getPredefinedDiscounts()) &&
                    line
                        .getPredefinedDiscounts()
                        .stream()
                        .anyMatch(tenantDiscountLineItem -> tenantDiscountLineItem.getId().equals(discount.getDiscountId()))
            )
            .forEach(orderLineItem -> {
                List<TenantDiscountLineItem> filteredForLine = orderLineItem
                    .getPredefinedDiscounts()
                    .stream()
                    .filter(tenantDiscountLineItem -> !tenantDiscountLineItem.getId().equals(discount.getDiscountId()))
                    .toList();
                orderLineItem.setPredefinedDiscounts(filteredForLine);
            });
    }

    private static List<PlanBinding> getPlanBindings(OrderCreationCustomizationInput orderCreationCustomizationInput) {
        return orderCreationCustomizationInput
            .getPlanMap()
            .values()
            .stream()
            .map(plan -> {
                CustomField planCustomField = orderCreationCustomizationInput.getPlanCustomFields().getOrDefault(plan.getPlanId(), null);
                List<ChargeBinding> chargeBindings = getChargeBindings(orderCreationCustomizationInput, plan);
                ProductBinding productBinding = ProductBinding.from(
                    orderCreationCustomizationInput.getProductMap().getOrDefault(plan.getProductId(), null)
                );
                return PlanBinding.from(plan, planCustomField, chargeBindings, productBinding);
            })
            .toList();
    }

    private static List<ChargeBinding> getChargeBindings(OrderCreationCustomizationInput orderCreationCustomizationInput, Plan plan) {
        return CollectionUtils.isEmpty(plan.getCharges())
            ? List.of()
            : plan
                .getCharges()
                .stream()
                .map(charge -> {
                    Charge chargeModel = orderCreationCustomizationInput.getChargeMap().getOrDefault(charge.getChargeId(), null);
                    CustomField chargeCustomFields = orderCreationCustomizationInput.getChargeCustomFields().getOrDefault(charge.getChargeId(), null);
                    return ChargeBinding.from(chargeModel, chargeCustomFields);
                })
                .toList();
    }

    private static TenantDiscount toTenantDiscount(Discount discount) {
        TenantDiscount tenantDiscount = new TenantDiscount();
        tenantDiscount.setId(discount.getDiscountId());
        tenantDiscount.setPercent(discount.getPercent());
        tenantDiscount.setName(discount.getName());
        tenantDiscount.setType(discount.getType());
        tenantDiscount.setDescription(discount.getDescription());
        tenantDiscount.setStatus(discount.getStatus());
        return tenantDiscount;
    }

    private static TenantDiscountLineItem toTenantDiscountLine(Discount discount) {
        TenantDiscountLineItem tenantDiscount = new TenantDiscountLineItem();
        tenantDiscount.setId(discount.getDiscountId());
        tenantDiscount.setPercent(discount.getPercent());
        tenantDiscount.setName(discount.getName());
        tenantDiscount.setType(discount.getType());
        tenantDiscount.setDescription(discount.getDescription());
        tenantDiscount.setStatus(discount.getStatus());
        return tenantDiscount;
    }

    private static boolean shouldAddDiscount(Discount discount, Order order) {
        if (CollectionUtils.isEmpty(order.getPredefinedDiscounts())) {
            return true;
        }

        return order.getPredefinedDiscounts().stream().noneMatch(tenantDiscount -> discount.getDiscountId().equals(tenantDiscount.getId()));
    }

    private static boolean shouldRemoveDiscount(Discount discount, Order order) {
        if (CollectionUtils.isEmpty(order.getPredefinedDiscounts())) {
            return false;
        }

        return order.getPredefinedDiscounts().stream().anyMatch(tenantDiscount -> discount.getDiscountId().equals(tenantDiscount.getId()));
    }

    private static void logAction(String message) {
        LOGGER.info(String.format("CUSTOMIZATION_ACTION_PERFORMED: %s", message));
    }

    private static String lineItemIdentifier(OrderLineItem orderLineItem) {
        long startDateEpoch = orderLineItem.getEffectiveDate() == null ? 0 : orderLineItem.getEffectiveDate().getEpochSecond();
        return String.format("%s/%s/%s", orderLineItem.getChargeId(), startDateEpoch, orderLineItem.getAction());
    }

    private static void addLineActionTrace(
        LineItemActionData lineActionInput,
        OrderLineItem lineItem,
        CustomizationAction customizationAction,
        String message
    ) {
        if (lineActionInput.lineItemActions().containsKey(lineItemIdentifier(lineItem))) {
            lineActionInput.lineItemActions().get(lineItemIdentifier(lineItem)).add(RuleActionPerformed.of(customizationAction, message));
        }
    }

    private static void addLineActionWarning(LineItemActionData lineActionInput, OrderLineItem lineItem, String message) {
        if (lineActionInput.lineItemWarnings().containsKey(lineItemIdentifier(lineItem)) && StringUtils.isNotBlank(message)) {
            lineActionInput.lineItemWarnings().get(lineItemIdentifier(lineItem)).add(message);
        }
        LOGGER.warn(ORDER_CREATION_CUSTOMIZATION, message);
    }
}
