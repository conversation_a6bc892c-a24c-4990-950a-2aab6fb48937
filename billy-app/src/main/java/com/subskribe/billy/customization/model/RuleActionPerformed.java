package com.subskribe.billy.customization.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import com.subskribe.zeppa.customizations.core.CustomizationAction;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
@JsonSerialize(as = ImmutableRuleActionPerformed.class)
@JsonDeserialize(as = ImmutableRuleActionPerformed.class)
public interface RuleActionPerformed {
    CustomizationAction getAction();

    String getActionMessage();

    static RuleActionPerformed of(CustomizationAction action, String message) {
        return ImmutableRuleActionPerformed.builder().action(action).actionMessage(message).build();
    }
}
