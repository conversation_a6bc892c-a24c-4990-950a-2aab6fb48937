package com.subskribe.billy.customization.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import java.util.List;
import java.util.Map;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
@JsonSerialize(as = ImmutableRuleTrace.class)
@JsonDeserialize(as = ImmutableRuleTrace.class)
public interface RuleTrace {
    String getRuleName();

    Boolean getFired();

    List<RuleActionPerformed> getOrderActionsPerformed();

    Map<String, List<RuleActionPerformed>> getOrderLineActionsPerformed();

    List<String> getOrderRuleWarnings();

    Map<String, List<String>> getOrderLineRuleWarnings();

    static RuleTrace notFired(String ruleName) {
        return ImmutableRuleTrace.builder().ruleName(ruleName).fired(false).build();
    }
}
