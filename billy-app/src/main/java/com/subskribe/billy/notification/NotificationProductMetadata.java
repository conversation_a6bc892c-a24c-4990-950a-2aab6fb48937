package com.subskribe.billy.notification;

import com.subskribe.billy.shared.logger.ErrorInstructions;
import com.subskribe.billy.shared.product.ProductArea;
import com.subskribe.billy.shared.product.ProductFeature;
import com.subskribe.billy.shared.product.ProductFeatureAvailability;

public class NotificationProductMetadata {

    public static final ProductArea NOTIFICATIONS_PRODUCT_AREA = new ProductArea("Notifications");

    // Features
    public static final ProductFeature NOTIFICATIONS_CORE = new ProductFeature(NOTIFICATIONS_PRODUCT_AREA, "Core", ProductFeatureAvailability.GA);
    public static final ProductFeature NOTIFICATIONS_WEBHOOKS = new ProductFeature(
        NOTIFICATIONS_PRODUCT_AREA,
        "Webhooks",
        ProductFeatureAvailability.GA
    );
    public static final ProductFeature NOTIFICATIONS_SLACK = new ProductFeature(NOTIFICATIONS_PRODUCT_AREA, "Slack", ProductFeatureAvailability.GA);
    public static final ProductFeature NOTIFICATIONS_EMAIL = new ProductFeature(NOTIFICATIONS_PRODUCT_AREA, "Email", ProductFeatureAvailability.BETA);

    public static final ErrorInstructions EVENT_DESERIALIZATION_ERROR = ErrorInstructions.sev1(
        "Notification deserialization error",
        """
        We have an event that is enabled for notifications, but we cannot deserialize it properly. Check that NotificationEventAdapter has a mapping for it.
        If there is one, and if the deserialization itself is not working, it is most likely due to a new change. If needed contact the engineer
        who made the change to fix it.
        """
    );

    public static final ErrorInstructions EVENT_OBJECT_ID_NOT_FOUND = ErrorInstructions.sev2(
        "Notification object id not found",
        """
        We have an event that is enabled for notifications, but when we tried to extract the object id from the event payload,
        we could not. Contact the person who added this event or notification type.
        """
    );

    public static final ErrorInstructions EVENT_GENERIC_ERROR = ErrorInstructions.sev3(
        "Generic notification error",
        "We encountered an unexpected general issue while sending a notification. Please contact #infra to investigate further."
    );

    public static final ErrorInstructions EMAIL_FORMATTING_ERROR = ErrorInstructions.sev2(
        "Notification email formatting error",
        """
        We tried to send a notification email but we encountered an exception when populating the email template. Most
        likely you will need to look at the event payload and the template to see what is wrong and provide a fix for the template.
        """
    );

    public static final ErrorInstructions EMAIL_TRANSPORT_ERROR = ErrorInstructions.sev2(
        "Notification email sending error",
        """
        We tried to send a notification email but we encountered an exception when sending the email. Please inspect the
        exception to try to understand what the issue might be. Please contact #infra if you need assistance.
        """
    );
}
