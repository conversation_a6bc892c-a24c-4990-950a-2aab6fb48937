package com.subskribe.billy.notification.model;

import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.event.model.payload.PaymentAttemptFailedEventPayload;
import com.subskribe.billy.event.model.payload.PaymentProcessedEventPayload;
import com.subskribe.billy.event.model.payload.PaymentRetriesExhaustedEventPayload;
import com.subskribe.billy.hubspot.model.event.HubspotSyncFailed;
import com.subskribe.billy.notification.model.payload.ElectronicSignatureEvent;
import com.subskribe.billy.resources.json.invoice.InvoiceJson;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.subscription.SubscriptionJson;
import com.subskribe.billy.salesforce.model.event.SalesforceSyncFailed;
import com.subskribe.billy.subscription.services.SubscriptionChargeChange;

public enum NotificationEventType {
    INVOICE_POSTED(InvoiceJson.class),
    INVOICE_VOIDED(InvoiceJson.class),
    SUBSCRIPTION_CREATED(SubscriptionJson.class),
    SUBSCRIPTION_ACTIVATING(SubscriptionJson.class),
    SUBSCRIPTION_ACTIVATED(SubscriptionJson.class),
    SUBSCRIPTION_CHARGE_CHANGE(SubscriptionChargeChange.class),
    SUBSCRIPTION_CANCELLING(SubscriptionJson.class),
    SUBSCRIPTION_CANCELLED(SubscriptionJson.class),
    SUBSCRIPTION_EXPIRING(SubscriptionJson.class),
    SUBSCRIPTION_EXPIRED(SubscriptionJson.class),
    SUBSCRIPTION_DELETED(SubscriptionJson.class),
    ORDER_SUBMITTED(OrderJson.class),
    ORDER_EXECUTED(OrderJson.class),
    ORDER_APPROVED(OrderJson.class),
    ESIGNATURE_COMPLETED(ElectronicSignatureEvent.class),
    PAYMENT_PROCESSED(PaymentProcessedEventPayload.class),
    PAYMENT_ATTEMPT_FAILED(PaymentAttemptFailedEventPayload.class),
    PAYMENT_RETRIES_EXHAUSTED(PaymentRetriesExhaustedEventPayload.class),
    HUBSPOT_SYNC_FAILED(HubspotSyncFailed.class),
    SALESFORCE_SYNC_FAILED(SalesforceSyncFailed.class);

    private final Class<? extends NotifyingEvent> payloadClass;

    NotificationEventType(Class<? extends NotifyingEvent> payloadClass) {
        this.payloadClass = payloadClass;
    }

    public Class<? extends NotifyingEvent> getPayloadClass() {
        return payloadClass;
    }
}
