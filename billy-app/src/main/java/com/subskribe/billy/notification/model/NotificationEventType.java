package com.subskribe.billy.notification.model;

import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.event.model.payload.AccountPaymentMethodSuspendedEventPayload;
import com.subskribe.billy.event.model.payload.InvoiceGenerationFailedEventPayload;
import com.subskribe.billy.event.model.payload.PaymentAttemptFailedEventPayload;
import com.subskribe.billy.event.model.payload.PaymentProcessedEventPayload;
import com.subskribe.billy.event.model.payload.PaymentRetriesExhaustedEventPayload;
import com.subskribe.billy.hubspot.model.event.HubspotSyncFailed;
import com.subskribe.billy.invoicedunning.model.DunningEmailSentEvent;
import com.subskribe.billy.notification.model.payload.ElectronicSignatureEvent;
import com.subskribe.billy.productcatalog.model.PlanStatusChangeEvent;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.invoice.InvoiceJson;
import com.subskribe.billy.resources.json.invoicesettlement.RefundDetail;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.resources.json.subscription.SubscriptionJson;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.salesforce.model.event.SalesforceSyncFailed;
import com.subskribe.billy.subscription.services.SubscriptionChargeChange;

public enum NotificationEventType {
    INVOICE_POSTED(InvoiceJson.class),
    INVOICE_VOIDED(InvoiceJson.class),
    INVOICE_GENERATION_FAILED(InvoiceGenerationFailedEventPayload.class),
    SUBSCRIPTION_CREATED(SubscriptionJson.class),
    SUBSCRIPTION_ACTIVATING(SubscriptionJson.class),
    SUBSCRIPTION_ACTIVATED(SubscriptionJson.class),
    SUBSCRIPTION_CHARGE_CHANGE(SubscriptionChargeChange.class),
    SUBSCRIPTION_CANCELLING(SubscriptionJson.class),
    SUBSCRIPTION_CANCELLED(SubscriptionJson.class),
    SUBSCRIPTION_EXPIRING(SubscriptionJson.class),
    SUBSCRIPTION_EXPIRED(SubscriptionJson.class),
    SUBSCRIPTION_DELETED(SubscriptionJson.class),
    ORDER_SUBMITTED(OrderJson.class),
    ORDER_EXECUTED(OrderJson.class),
    ORDER_APPROVED(OrderJson.class),
    ESIGNATURE_COMPLETED(ElectronicSignatureEvent.class),
    PAYMENT_PROCESSED(PaymentProcessedEventPayload.class),
    PAYMENT_ATTEMPT_FAILED(PaymentAttemptFailedEventPayload.class),
    PAYMENT_RETRIES_EXHAUSTED(PaymentRetriesExhaustedEventPayload.class),
    ACCOUNT_PAYMENT_METHOD_SUSPENDED(AccountPaymentMethodSuspendedEventPayload.class),
    HUBSPOT_SYNC_FAILED(HubspotSyncFailed.class),
    SALESFORCE_SYNC_FAILED(SalesforceSyncFailed.class),
    CONTACT_CREATED(AccountContactJson.class),
    CONTACT_UPDATED(AccountContactJson.class),
    CONTACT_DELETED(AccountContactJson.class),
    CHARGE_CREATED(ChargeJson.class),
    CHARGE_UPDATED(ChargeJson.class),
    CHARGE_DELETED(ChargeJson.class),
    PRODUCT_CREATED(ProductJson.class),
    PRODUCT_UPDATED(ProductJson.class),
    PRODUCT_DELETED(ProductJson.class),
    PLAN_CREATED(PlanJson.class),
    PLAN_UPDATED(PlanJson.class),
    PLAN_DELETED(PlanJson.class),
    PLAN_STATUS_CHANGED(PlanStatusChangeEvent.class),
    USER_CREATED(UserJson.class),
    USER_DISABLED(UserJson.class),
    USER_REACTIVATED(UserJson.class),
    REFUND_GENERATED(RefundDetail.class),
    ACCOUNTING_PERIOD_CLOSED(AccountingPeriod.class),
    ACCOUNTING_PERIOD_REOPENED(AccountingPeriod.class),
    DUNNING_EMAIL_SENT(DunningEmailSentEvent.class);

    private final Class<? extends NotifyingEvent> payloadClass;

    NotificationEventType(Class<? extends NotifyingEvent> payloadClass) {
        this.payloadClass = payloadClass;
    }

    public Class<? extends NotifyingEvent> getPayloadClass() {
        return payloadClass;
    }
}
