package com.subskribe.billy.notification.event;

import com.subskribe.billy.event.consumer.EventConsumer;
import com.subskribe.billy.event.consumer.EventConsumerConfiguration;
import com.subskribe.billy.event.consumer.ImmutableEventConsumerConfiguration;
import com.subskribe.billy.event.consumer.TenantContextAwareEventConsumer;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.streams.Stream;
import com.subskribe.billy.exception.handling.TryForeverExceptionHandlingStrategy;
import com.subskribe.billy.notification.service.NotificationEventAdapter;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Set;
import javax.inject.Inject;

public class NotificationEventConsumer extends TenantContextAwareEventConsumer implements EventConsumer {

    public static final String NOTIFICATION_CONSUMER_CANONICAL_NAME = "notification-consumer";
    private static final Set<Stream> STREAMS_TO_LISTEN = Set.of(
        Stream.BILLING_DOMAIN_STREAM,
        Stream.SUBSCRIPTION_DOMAIN_STREAM,
        Stream.ORDER_DOMAIN_STREAM,
        Stream.CATALOG_DOMAIN_STREAM,
        Stream.REVENUE_DOMAIN_STREAM,
        Stream.ACCOUNT_DOMAIN_STREAM,
        Stream.TENANT_DOMAIN_STREAM
    );

    private final NotificationEventAdapter notificationEventAdapter;

    @Inject
    public NotificationEventConsumer(NotificationEventAdapter notificationEventAdapter, TenantIdProvider tenantIdProvider) {
        super(tenantIdProvider);
        this.notificationEventAdapter = notificationEventAdapter;
    }

    @Override
    public void tenantContextAwareOnEvent(Event event) {
        notificationEventAdapter.processEvent(event);
    }

    @Override
    public EventConsumerConfiguration getConfiguration() {
        return ImmutableEventConsumerConfiguration.builder()
            .canonicalConsumerName(NOTIFICATION_CONSUMER_CANONICAL_NAME)
            .addAllHandlesEventsFromStream(STREAMS_TO_LISTEN)
            .exceptionHandlingStrategy(TryForeverExceptionHandlingStrategy.DEFAULT_TRY_FOREVER_STRATEGY)
            .build();
    }
}
