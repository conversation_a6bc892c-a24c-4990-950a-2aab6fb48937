package com.subskribe.billy.notification.db;

import static com.subskribe.billy.jooq.default_schema.tables.NotificationDeliveryAttempt.NOTIFICATION_DELIVERY_ATTEMPT;
import static com.subskribe.billy.jooq.default_schema.tables.NotificationInstance.NOTIFICATION_INSTANCE;
import static org.jooq.impl.DSL.noCondition;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.NotificationDeliveryAttemptRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.NotificationInstanceRecord;
import com.subskribe.billy.notification.mapper.NotificationMapper;
import com.subskribe.billy.notification.model.ImmutableNotificationInstance;
import com.subskribe.billy.notification.model.Notification;
import com.subskribe.billy.notification.model.NotificationDeliveryAttempt;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.notification.model.NotificationInstanceStatus;
import com.subskribe.billy.notification.model.NotificationTarget;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Clock;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class NotificationInstanceDAO {

    private final DSLContextProvider dslContextProvider;

    private final TenantIdProvider tenantIdProvider;

    private final NotificationMapper notificationMapper;

    private final Clock clock;

    @Inject
    public NotificationInstanceDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider, Clock clock) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.clock = clock;
        notificationMapper = Mappers.getMapper(NotificationMapper.class);
    }

    public NotificationInstance getOrCreateNotificationInstance(NotificationTarget notificationTarget, Notification notification) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var notificationInstanceRecord = dslContext
            .selectFrom(NOTIFICATION_INSTANCE)
            .where(NOTIFICATION_INSTANCE.TENANT_ID.eq(tenantId))
            .and(NOTIFICATION_INSTANCE.NOTIFICATION_ID.eq(notificationTarget.getNotificationId()))
            .and(NOTIFICATION_INSTANCE.EVENT_TYPE.eq(notification.getNotificationEventType().name()))
            .and(NOTIFICATION_INSTANCE.ENTITY_ID.eq(notification.getEntityId()))
            .and(NOTIFICATION_INSTANCE.SOURCE_EVENT_ID.eq(notification.getSourceEventId()))
            .fetchOne();

        if (notificationInstanceRecord == null) {
            NotificationInstance notificationInstance = ImmutableNotificationInstance.builder()
                .id(UUID.randomUUID())
                .notificationId(notificationTarget.getNotificationId())
                .entityId(notification.getEntityId())
                .eventType(notification.getNotificationEventType())
                .status(NotificationInstanceStatus.CREATED)
                .payload(notification.getMessageBody())
                .deliveryAttempts(0)
                .tenantEntityIds(notification.getTenantEntityIds())
                .sourceEventId(notification.getSourceEventId())
                .build();
            var insertRecord = notificationMapper.notificationInstanceToRecord(notificationInstance);
            insertRecord.setTenantId(tenantId);
            notificationInstanceRecord = dslContext.insertInto(NOTIFICATION_INSTANCE).set(insertRecord).returning().fetchOne();
        }

        return notificationMapper.recordToNotificationInstance(notificationInstanceRecord);
    }

    public Optional<NotificationInstance> getNotificationInstanceById(String notificationInstanceId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        NotificationInstanceRecord notificationInstanceRecord = dslContext
            .selectFrom(NOTIFICATION_INSTANCE)
            .where(NOTIFICATION_INSTANCE.TENANT_ID.eq(tenantId))
            .and(NOTIFICATION_INSTANCE.ID.eq(UUID.fromString(notificationInstanceId)))
            .fetchOneInto(NotificationInstanceRecord.class);

        return Optional.ofNullable(notificationMapper.recordToNotificationInstance(notificationInstanceRecord));
    }

    public List<NotificationInstance> getNotificationsOverdueForRetry(Map<Integer, Integer> attemptsToIntervalMap) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        // We are combining different delivery attempts to their respective retry periods. Please see NotificationInstanceDAOTest
        // for a simplified version of this, and how the final query looks like
        Condition combinedConditionsForTimePassedSinceLastAttempt = attemptsToIntervalMap
            .entrySet()
            .stream()
            .map(e -> {
                LocalDateTime nowMinusMinutesForInterval = LocalDateTime.now(clock).minusMinutes(e.getValue());
                return NOTIFICATION_INSTANCE.DELIVERY_ATTEMPTS.eq(e.getKey()).and(
                    NOTIFICATION_INSTANCE.LAST_DELIVERY_ATTEMPT.le(nowMinusMinutesForInterval)
                );
            })
            .reduce(noCondition(), Condition::or);

        List<NotificationInstanceRecord> records = dslContext
            .selectFrom(NOTIFICATION_INSTANCE)
            .where(NOTIFICATION_INSTANCE.TENANT_ID.eq(tenantId))
            .and(NOTIFICATION_INSTANCE.STATUS.eq(NotificationInstanceStatus.ATTEMPTED.name()))
            .and(combinedConditionsForTimePassedSinceLastAttempt)
            .fetchInto(NotificationInstanceRecord.class);

        return records.stream().map(notificationMapper::recordToNotificationInstance).toList();
    }

    public void logNotificationDeliveryAttempt(NotificationInstance notificationInstance, NotificationDeliveryAttempt deliveryAttempt) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var context = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var notificationInstanceRecord = notificationMapper.notificationInstanceToRecord(notificationInstance);
        notificationInstanceRecord.setTenantId(tenantId);

        var deliveryAttemptRecord = notificationMapper.notificationDeliveryAttemptToRecord(deliveryAttempt);
        deliveryAttemptRecord.setTenantId(tenantId);

        context.transaction(configuration -> {
            try {
                var transactionContext = DSL.using(configuration);
                transactionContext
                    .update(NOTIFICATION_INSTANCE)
                    .set(notificationInstanceRecord)
                    .where(NOTIFICATION_INSTANCE.TENANT_ID.eq(tenantId))
                    .and(NOTIFICATION_INSTANCE.ID.eq(notificationInstance.getId()))
                    .execute();
                transactionContext.insertInto(NOTIFICATION_DELIVERY_ATTEMPT).set(deliveryAttemptRecord).execute();
            } catch (Exception e) {
                String message = String.format("unable to create notification instance %s", notificationInstance);
                throw new ServiceFailureException(message);
            }
        });
    }

    public List<NotificationDeliveryAttempt> getNotificationDeliveryAttempts(String notificationInstanceId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<NotificationDeliveryAttemptRecord> records = dslContext
            .selectFrom(NOTIFICATION_DELIVERY_ATTEMPT)
            .where(NOTIFICATION_DELIVERY_ATTEMPT.TENANT_ID.eq(tenantId))
            .and(NOTIFICATION_DELIVERY_ATTEMPT.NOTIFICATION_INSTANCE_ID.eq(UUID.fromString(notificationInstanceId)))
            .fetchInto(NotificationDeliveryAttemptRecord.class);

        return records.stream().map(notificationMapper::recordToNotificationDeliveryAttempt).toList();
    }

    public void updateNotificationInstance(NotificationInstance notificationInstance) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var context = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var notificationInstanceRecord = notificationMapper.notificationInstanceToRecord(notificationInstance);
        notificationInstanceRecord.setTenantId(tenantId);
        context
            .update(NOTIFICATION_INSTANCE)
            .set(notificationInstanceRecord)
            .where(NOTIFICATION_INSTANCE.TENANT_ID.eq(tenantId))
            .and(NOTIFICATION_INSTANCE.ID.eq(notificationInstance.getId()))
            .execute();
    }
}
