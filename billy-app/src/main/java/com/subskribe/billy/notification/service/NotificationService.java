package com.subskribe.billy.notification.service;

import static com.subskribe.billy.notification.NotificationProductMetadata.EVENT_GENERIC_ERROR;
import static com.subskribe.billy.notification.NotificationProductMetadata.NOTIFICATIONS_CORE;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.notification.db.NotificationInstanceDAO;
import com.subskribe.billy.notification.gql.ImmutableDeliveryAttempt;
import com.subskribe.billy.notification.gql.ImmutableNotificationInstanceWithAttempts;
import com.subskribe.billy.notification.gql.NotificationInstanceWithAttempts;
import com.subskribe.billy.notification.model.ImmutableNotificationDeliveryAttempt;
import com.subskribe.billy.notification.model.ImmutableNotificationInstance;
import com.subskribe.billy.notification.model.ImmutableNotificationProcessorTypeSupport;
import com.subskribe.billy.notification.model.Notification;
import com.subskribe.billy.notification.model.NotificationConfiguration;
import com.subskribe.billy.notification.model.NotificationDeliveryAttempt;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.notification.model.NotificationInstanceStatus;
import com.subskribe.billy.notification.model.NotificationProcessingResult;
import com.subskribe.billy.notification.model.NotificationProcessorTypeSupport;
import com.subskribe.billy.notification.model.NotificationTarget;
import com.subskribe.billy.notification.model.NotificationTargetType;
import com.subskribe.billy.notification.service.email.EmailNotificationProcessor;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.ErrorContext;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.validation.Validator;
import java.time.Clock;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;

public class NotificationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationService.class);

    private final NotificationInstanceDAO notificationInstanceDAO;

    private final NotificationTargetService notificationTargetService;

    private final Map<NotificationTargetType, NotificationProcessor> notificationProcessors;

    private final NotificationConfiguration notificationConfiguration;

    private final FeatureService featureService;

    private final Clock clock;

    @Inject
    public NotificationService(
        NotificationInstanceDAO notificationInstanceDAO,
        NotificationTargetService notificationTargetService,
        SlackNotificationProcessor slackNotificationProcessor,
        WebhookNotificationProcessor webhookNotificationProcessor,
        EmailNotificationProcessor emailNotificationProcessor,
        BillyConfiguration billyConfiguration,
        FeatureService featureService,
        Clock clock
    ) {
        this.notificationInstanceDAO = notificationInstanceDAO;
        this.notificationTargetService = notificationTargetService;
        this.clock = clock;
        notificationProcessors = Map.of(
            slackNotificationProcessor.getTargetType(),
            slackNotificationProcessor,
            webhookNotificationProcessor.getTargetType(),
            webhookNotificationProcessor,
            emailNotificationProcessor.getTargetType(),
            emailNotificationProcessor
        );
        this.featureService = featureService;
        notificationConfiguration = billyConfiguration.getNotificationConfiguration();
    }

    void sendNotification(Notification notification) {
        try {
            Set<NotificationTarget> allTopics = notificationTargetService.getNotificationTargetsForTenantAndEvent(
                notification.getTenantId(),
                notification.getNotificationEventType()
            );
            if (CollectionUtils.isEmpty(allTopics)) {
                return;
            }

            allTopics.forEach(subscription -> createNotificationInstanceAndProcess(notification, subscription));
        } catch (Exception e) {
            LOGGER.error(new ErrorContext(NOTIFICATIONS_CORE, EVENT_GENERIC_ERROR), "Failed to send notification", e);
        }
    }

    private void createNotificationInstanceAndProcess(Notification notification, NotificationTarget notificationTarget) {
        NotificationInstance notificationInstance = notificationInstanceDAO.getOrCreateNotificationInstance(notificationTarget, notification);

        if (notificationInstance.getStatus() == NotificationInstanceStatus.DELIVERED) {
            LOGGER.info(
                String.format("skip sending notification because notification instance has already been delivered: %s", notificationInstance)
            );
            return;
        }

        processNotification(notificationTarget, notificationInstance);
    }

    void processNotification(NotificationTarget notificationTarget, NotificationInstance notificationInstance) {
        NotificationProcessingResult notificationProcessingResult = sendNotificationWithProcessor(notificationTarget, notificationInstance);

        Validator.checkNonNullInternal(notificationInstance, "notification instance cannot be null here");

        int newDeliveryAttempts = notificationInstance.getDeliveryAttempts() + 1;
        NotificationInstanceStatus newStatus = findNewStatusForInstance(notificationProcessingResult, newDeliveryAttempts);

        notificationInstance = ImmutableNotificationInstance.builder()
            .from(notificationInstance)
            .status(newStatus)
            .deliveryAttempts(newDeliveryAttempts)
            .lastDeliveryAttempt(clock.instant())
            .build();
        NotificationDeliveryAttempt deliveryAttempt = ImmutableNotificationDeliveryAttempt.builder()
            .id(UUID.randomUUID())
            .notificationInstanceId(notificationInstance.getId())
            .httpStatusCode(notificationProcessingResult.getStatusCode().orElse(null))
            .errorMessage(notificationProcessingResult.getErrorMessage().orElse(null))
            .isSuccess(notificationProcessingResult.getIsSuccess())
            .build();
        notificationInstanceDAO.logNotificationDeliveryAttempt(notificationInstance, deliveryAttempt);
    }

    NotificationProcessingResult sendNotificationWithProcessor(NotificationTarget notificationTarget, NotificationInstance notificationInstance) {
        NotificationProcessor processor = notificationProcessors.get(notificationTarget.getNotificationTargetType());
        return processor.processNotification(notificationTarget, notificationInstance);
    }

    public NotificationInstanceWithAttempts getNotificationInstanceWithAttempts(String notificationInstanceId) {
        Optional<NotificationInstance> instanceOptional = notificationInstanceDAO.getNotificationInstanceById(notificationInstanceId);
        if (instanceOptional.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.NOTIFICATION_INSTANCE, notificationInstanceId);
        }
        List<NotificationDeliveryAttempt> deliveryAttempts = notificationInstanceDAO.getNotificationDeliveryAttempts(notificationInstanceId);
        NotificationInstance instance = instanceOptional.get();
        return ImmutableNotificationInstanceWithAttempts.builder()
            .id(instance.getId().toString())
            .entityId(instance.getEntityId())
            .eventType(instance.getEventType().name())
            .payload(instance.getPayload())
            .status(instance.getStatus())
            .numberOfDeliveryAttempts(instance.getDeliveryAttempts())
            .lastDeliveryAttempt(instance.getLastDeliveryAttempt())
            .deliveryAttempts(
                deliveryAttempts
                    .stream()
                    .map(da ->
                        ImmutableDeliveryAttempt.builder()
                            .isSuccess(da.getIsSuccess())
                            .errorMessage(da.getErrorMessage())
                            .statusCode(da.getHttpStatusCode())
                            .build()
                    )
                    .toList()
            )
            .build();
    }

    public List<NotificationProcessorTypeSupport> getSupportedNotificationTypesForTargetTypes() {
        return notificationProcessors
            .entrySet()
            .stream()
            .filter(p -> {
                if (!featureService.isEnabled(Feature.EMAIL_NOTIFICATIONS)) {
                    return p.getValue().getTargetType() != NotificationTargetType.EMAIL;
                }
                return true;
            })
            .map(processor ->
                (NotificationProcessorTypeSupport) ImmutableNotificationProcessorTypeSupport.builder()
                    .targetType(processor.getKey().toString())
                    .addAllSupportedTypes(processor.getValue().getSupportedEventTypes().stream().map(NotificationEventType::name).toList())
                    .build()
            )
            .toList();
    }

    private NotificationInstanceStatus findNewStatusForInstance(NotificationProcessingResult processingResult, int deliveryAttempts) {
        if (processingResult.getIsSuccess()) {
            return NotificationInstanceStatus.DELIVERED;
        }

        if (!processingResult.getIsRetryable() || deliveryAttempts >= notificationConfiguration.getNotificationRetryIntervals().size()) {
            return NotificationInstanceStatus.FAILED;
        }

        return NotificationInstanceStatus.ATTEMPTED;
    }
}
