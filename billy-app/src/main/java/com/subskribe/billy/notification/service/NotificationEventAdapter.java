package com.subskribe.billy.notification.service;

import static com.subskribe.billy.notification.NotificationProductMetadata.EVENT_DESERIALIZATION_ERROR;
import static com.subskribe.billy.notification.NotificationProductMetadata.EVENT_OBJECT_ID_NOT_FOUND;
import static com.subskribe.billy.notification.NotificationProductMetadata.NOTIFICATIONS_CORE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.notification.model.ImmutableNotification;
import com.subskribe.billy.notification.model.Notification;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.shared.logger.ErrorContext;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;

public class NotificationEventAdapter {

    private final NotificationService notificationService;
    private final ObjectMapper objectMapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationEventAdapter.class);

    @VisibleForTesting
    static final Map<EventType, NotificationEventType> EVENT_TYPE_NOTIFICATION_TYPE_MAP = Map.ofEntries(
        Map.entry(EventType.INVOICE_POSTED_V2, NotificationEventType.INVOICE_POSTED),
        Map.entry(EventType.INVOICE_VOIDED_V2, NotificationEventType.INVOICE_VOIDED),
        Map.entry(EventType.INVOICE_GENERATION_FAILED, NotificationEventType.INVOICE_GENERATION_FAILED),
        Map.entry(EventType.SUBSCRIPTION_CREATED, NotificationEventType.SUBSCRIPTION_CREATED),
        Map.entry(EventType.SUBSCRIPTION_ACTIVATING, NotificationEventType.SUBSCRIPTION_ACTIVATING),
        Map.entry(EventType.SUBSCRIPTION_ACTIVATED, NotificationEventType.SUBSCRIPTION_ACTIVATED),
        Map.entry(EventType.SUBSCRIPTION_CHARGE_CHANGE, NotificationEventType.SUBSCRIPTION_CHARGE_CHANGE),
        Map.entry(EventType.SUBSCRIPTION_CANCELLING, NotificationEventType.SUBSCRIPTION_CANCELLING),
        Map.entry(EventType.SUBSCRIPTION_CANCELLED, NotificationEventType.SUBSCRIPTION_CANCELLED),
        Map.entry(EventType.SUBSCRIPTION_EXPIRING, NotificationEventType.SUBSCRIPTION_EXPIRING),
        Map.entry(EventType.SUBSCRIPTION_EXPIRED, NotificationEventType.SUBSCRIPTION_EXPIRED),
        Map.entry(EventType.SUBSCRIPTION_DELETED, NotificationEventType.SUBSCRIPTION_DELETED),
        Map.entry(EventType.ORDER_SUBMITTED, NotificationEventType.ORDER_SUBMITTED),
        Map.entry(EventType.ORDER_APPROVED, NotificationEventType.ORDER_APPROVED),
        Map.entry(EventType.ORDER_EXECUTED, NotificationEventType.ORDER_EXECUTED),
        Map.entry(EventType.ESIGNATURE_COMPLETED, NotificationEventType.ESIGNATURE_COMPLETED),
        Map.entry(EventType.PAYMENT_PROCESSED, NotificationEventType.PAYMENT_PROCESSED),
        Map.entry(EventType.PAYMENT_ATTEMPT_FAILED, NotificationEventType.PAYMENT_ATTEMPT_FAILED),
        Map.entry(EventType.PAYMENT_RETRIES_EXHAUSTED, NotificationEventType.PAYMENT_RETRIES_EXHAUSTED),
        Map.entry(EventType.ACCOUNT_PAYMENT_METHOD_SUSPENDED, NotificationEventType.ACCOUNT_PAYMENT_METHOD_SUSPENDED),
        Map.entry(EventType.CONTACT_CREATED, NotificationEventType.CONTACT_CREATED),
        Map.entry(EventType.CONTACT_UPDATED, NotificationEventType.CONTACT_UPDATED),
        Map.entry(EventType.CONTACT_DELETED, NotificationEventType.CONTACT_DELETED),
        Map.entry(EventType.CHARGE_CREATED, NotificationEventType.CHARGE_CREATED),
        Map.entry(EventType.CHARGE_UPDATED, NotificationEventType.CHARGE_UPDATED),
        Map.entry(EventType.CHARGE_DELETED, NotificationEventType.CHARGE_DELETED),
        Map.entry(EventType.PRODUCT_CREATED, NotificationEventType.PRODUCT_CREATED),
        Map.entry(EventType.PRODUCT_UPDATED, NotificationEventType.PRODUCT_UPDATED),
        Map.entry(EventType.PRODUCT_DELETED, NotificationEventType.PRODUCT_DELETED),
        Map.entry(EventType.PLAN_CREATED, NotificationEventType.PLAN_CREATED),
        Map.entry(EventType.PLAN_UPDATED, NotificationEventType.PLAN_UPDATED),
        Map.entry(EventType.PLAN_DELETED, NotificationEventType.PLAN_DELETED),
        Map.entry(EventType.PLAN_STATUS_CHANGED, NotificationEventType.PLAN_STATUS_CHANGED),
        Map.entry(EventType.USER_CREATED, NotificationEventType.USER_CREATED),
        Map.entry(EventType.USER_DISABLED, NotificationEventType.USER_DISABLED),
        Map.entry(EventType.USER_REACTIVATED, NotificationEventType.USER_REACTIVATED),
        Map.entry(EventType.REFUND_GENERATED, NotificationEventType.REFUND_GENERATED),
        Map.entry(EventType.ACCOUNTING_PERIOD_CLOSED, NotificationEventType.ACCOUNTING_PERIOD_CLOSED),
        Map.entry(EventType.ACCOUNTING_PERIOD_REOPENED, NotificationEventType.ACCOUNTING_PERIOD_REOPENED),
        Map.entry(EventType.DUNNING_EMAIL_SENT, NotificationEventType.DUNNING_EMAIL_SENT)
    );

    @Inject
    public NotificationEventAdapter(NotificationService notificationService) {
        this.notificationService = notificationService;
        objectMapper = JacksonProvider.defaultMapper();
    }

    @VisibleForTesting
    NotificationEventAdapter(NotificationService notificationService, ObjectMapper objectMapper) {
        this.notificationService = notificationService;
        this.objectMapper = objectMapper;
    }

    public void processEvent(Event event) {
        buildNotificationForEvent(event).ifPresentOrElse(notificationService::sendNotification, () ->
            LOGGER.debug("No notification type found for event type: {}", event.getType())
        );
    }

    Optional<Notification> buildNotificationForEvent(Event event) {
        Optional<NotificationEventType> notificationTypeForEvent = findNotificationTypeForEvent(event);
        if (notificationTypeForEvent.isEmpty()) {
            return Optional.empty();
        }
        String entityIdForEvent = getEntityIdForEvent(notificationTypeForEvent.get(), event);
        if (entityIdForEvent == null) {
            LOGGER.error(
                new ErrorContext(NOTIFICATIONS_CORE, EVENT_OBJECT_ID_NOT_FOUND),
                "Could not find entity id for event type: {} with event id: {}",
                event.getType(),
                event.getId()
            );
            return Optional.empty();
        }
        return Optional.of(
            ImmutableNotification.builder()
                .notificationEventType(notificationTypeForEvent.get())
                .entityId(entityIdForEvent)
                .tenantId(event.getTenantId())
                .tenantEntityIds(event.getEntityIds())
                .messageBody(event.getPayloadAsString())
                .sourceEventId(event.getId())
                .build()
        );
    }

    private Optional<NotificationEventType> findNotificationTypeForEvent(Event event) {
        return Optional.ofNullable(EVENT_TYPE_NOTIFICATION_TYPE_MAP.get(event.getType()));
    }

    private String getEntityIdForEvent(NotificationEventType notificationEventType, Event event) {
        try {
            Class<? extends NotifyingEvent> payloadClass = notificationEventType.getPayloadClass();
            return objectMapper.readValue(event.getPayloadAsString(), payloadClass).getEventObjectId();
        } catch (JsonProcessingException e) {
            LOGGER.error(
                new ErrorContext(NOTIFICATIONS_CORE, EVENT_DESERIALIZATION_ERROR),
                "Failed to deserialize event payload for event type: {}",
                event.getType(),
                e
            );
            throw new ServiceFailureException("Failed to deserialize event payload for event type: " + event.getType(), e);
        }
    }
}
