package com.subskribe.billy.template.db;

import static com.subskribe.billy.jooq.default_schema.Tables.DOCUMENT_TEMPLATE_PLAN;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.jooq.default_schema.tables.records.DocumentTemplatePlanRecord;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

public class PlanTermsDAO {

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    @Inject
    public PlanTermsDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
    }

    public List<String> getDocumentTemplateIdsForPlan(String planId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var templateIds = dslContext
            .selectFrom(DOCUMENT_TEMPLATE_PLAN)
            .where(DOCUMENT_TEMPLATE_PLAN.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_PLAN.PLAN_ID.eq(planId))
            .and(DOCUMENT_TEMPLATE_PLAN.IS_DELETED.isFalse())
            .fetchInto(DocumentTemplatePlanRecord.class);

        return templateIds.stream().map(DocumentTemplatePlanRecord::getTemplateGroupId).collect(Collectors.toList());
    }

    public List<String> getPlanIdsForDocumentTemplate(String templateId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var planIds = dslContext
            .selectFrom(DOCUMENT_TEMPLATE_PLAN)
            .where(DOCUMENT_TEMPLATE_PLAN.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_PLAN.TEMPLATE_GROUP_ID.eq(templateId))
            .and(DOCUMENT_TEMPLATE_PLAN.IS_DELETED.isFalse())
            .fetchInto(DocumentTemplatePlanRecord.class);

        return planIds.stream().map(DocumentTemplatePlanRecord::getPlanId).collect(Collectors.toList());
    }

    public boolean documentTemplateUsedByPlan(String templateId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(DOCUMENT_TEMPLATE_PLAN)
                .where(DOCUMENT_TEMPLATE_PLAN.TEMPLATE_GROUP_ID.eq(templateId))
                .and(DOCUMENT_TEMPLATE_PLAN.TENANT_ID.eq(tenantId))
                .and(DOCUMENT_TEMPLATE_PLAN.IS_DELETED.isFalse())
                .limit(1)
        );
    }

    public Map<String, List<String>> getDocumentTemplateIdsAssociatedWithPlans(List<String> planIds) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        Map<String, List<String>> templateIdsMap = dslContext
            .select()
            .from(DOCUMENT_TEMPLATE_PLAN)
            .where(
                DOCUMENT_TEMPLATE_PLAN.PLAN_ID.in(planIds)
                    .and(DOCUMENT_TEMPLATE_PLAN.TENANT_ID.eq(tenantId))
                    .and(DOCUMENT_TEMPLATE_PLAN.IS_DELETED.isFalse())
            )
            .fetchGroups(DOCUMENT_TEMPLATE_PLAN.PLAN_ID, DOCUMENT_TEMPLATE_PLAN.TEMPLATE_GROUP_ID);

        return templateIdsMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public Map<String, List<String>> getPlanIdsAssociatedWithDocumentTemplates(List<String> templateIds) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        Map<String, List<String>> planIdsMap = dslContext
            .select()
            .from(DOCUMENT_TEMPLATE_PLAN)
            .where(
                DOCUMENT_TEMPLATE_PLAN.TEMPLATE_GROUP_ID.in(templateIds)
                    .and(DOCUMENT_TEMPLATE_PLAN.TENANT_ID.eq(tenantId))
                    .and(DOCUMENT_TEMPLATE_PLAN.IS_DELETED.isFalse())
            )
            .fetchGroups(DOCUMENT_TEMPLATE_PLAN.TEMPLATE_GROUP_ID, DOCUMENT_TEMPLATE_PLAN.PLAN_ID);

        return planIdsMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public void addDocumentTemplatesForPlan(DSLContext dslContext, String tenantId, String planId, List<DocumentTemplate> templates) {
        if (CollectionUtils.isEmpty(templates)) {
            return;
        }

        var records = templates
            .stream()
            .map(template -> createDocumentTemplatePlanRecord(tenantId, template.getTemplateId(), planId))
            .collect(Collectors.toList());
        dslContext.batchInsert(records).execute();
    }

    private DocumentTemplatePlanRecord createDocumentTemplatePlanRecord(String tenantId, String templateGroupId, String planId) {
        DocumentTemplatePlanRecord record = new DocumentTemplatePlanRecord();
        record.setTenantId(tenantId);
        record.setPlanId(planId);
        record.setTemplateGroupId(templateGroupId);
        return record;
    }

    public void removeDocumentTemplateIdsForPlan(DSLContext dslContext, String tenantId, List<String> templateIds, String planId) {
        dslContext
            .update(DOCUMENT_TEMPLATE_PLAN)
            .set(DOCUMENT_TEMPLATE_PLAN.IS_DELETED, true)
            .where(DOCUMENT_TEMPLATE_PLAN.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_PLAN.PLAN_ID.eq(planId))
            .and(DOCUMENT_TEMPLATE_PLAN.TEMPLATE_GROUP_ID.in(templateIds))
            .and(DOCUMENT_TEMPLATE_PLAN.IS_DELETED.isFalse())
            .execute();
    }

    public void removeAllDocumentTemplateIdsForPlan(String planId, Configuration configuration) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        var context = DSL.using(configuration);
        context
            .update(DOCUMENT_TEMPLATE_PLAN)
            .set(DOCUMENT_TEMPLATE_PLAN.IS_DELETED, true)
            .where(DOCUMENT_TEMPLATE_PLAN.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_PLAN.PLAN_ID.eq(planId))
            .and(DOCUMENT_TEMPLATE_PLAN.IS_DELETED.isFalse())
            .execute();
    }

    public void removeAllPlanIdsForDocumentTemplate(String templateId, Configuration configuration) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        var context = DSL.using(configuration);
        context
            .update(DOCUMENT_TEMPLATE_PLAN)
            .set(DOCUMENT_TEMPLATE_PLAN.IS_DELETED, true)
            .where(DOCUMENT_TEMPLATE_PLAN.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_PLAN.TEMPLATE_GROUP_ID.eq(templateId))
            .and(DOCUMENT_TEMPLATE_PLAN.IS_DELETED.isFalse())
            .execute();
    }
}
