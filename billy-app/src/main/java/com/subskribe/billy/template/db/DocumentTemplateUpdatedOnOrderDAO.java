package com.subskribe.billy.template.db;

import static com.subskribe.billy.jooq.default_schema.tables.DocumentTemplateUpdatedOnOrder.DOCUMENT_TEMPLATE_UPDATED_ON_ORDER;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.jooq.default_schema.tables.records.DocumentTemplateUpdatedOnOrderRecord;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.template.mapper.DocumentTemplateUpdatedOnOrderMapper;
import com.subskribe.billy.template.model.CustomTemplateUpdateOnOrder;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class DocumentTemplateUpdatedOnOrderDAO {

    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final DocumentTemplateUpdatedOnOrderMapper mapper;

    @Inject
    public DocumentTemplateUpdatedOnOrderDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        mapper = Mappers.getMapper(DocumentTemplateUpdatedOnOrderMapper.class);
    }

    public CustomTemplateUpdateOnOrder addCustomTemplate(CustomTemplateUpdateOnOrder customTemplateUpdateOnOrder, DSLContext dslContext) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DocumentTemplateUpdatedOnOrderRecord record = mapper.toRecord(customTemplateUpdateOnOrder);
        record.reset(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.ID);
        record.setTenantId(tenantId);
        record.setCreatedOn(DateTimeConverter.instantToLocalDateTime(Instant.now()));

        DocumentTemplateUpdatedOnOrderRecord savedRecord = dslContext
            .insertInto(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER)
            .set(record)
            .returning()
            .fetchOneInto(DocumentTemplateUpdatedOnOrderRecord.class);
        return mapper.toCustomTemplateUpdatedOnOrder(savedRecord);
    }

    public CustomTemplateUpdateOnOrder updateCustomTemplate(CustomTemplateUpdateOnOrder customTemplateUpdateOnOrder, DSLContext dslContext) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DocumentTemplateUpdatedOnOrderRecord record = mapper.toRecord(customTemplateUpdateOnOrder);
        record.setTenantId(tenantId);

        DocumentTemplateUpdatedOnOrderRecord updatedRecord = dslContext
            .update(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER)
            .set(record)
            .where(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.ID.eq(UUID.fromString(customTemplateUpdateOnOrder.getId())))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.TENANT_ID.eq(tenantId))
            .returning()
            .fetchOneInto(DocumentTemplateUpdatedOnOrderRecord.class);

        return mapper.toCustomTemplateUpdatedOnOrder(updatedRecord);
    }

    public Optional<CustomTemplateUpdateOnOrder> getCustomTemplate(String templateId, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .select()
            .from(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER)
            .where(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.TEMPLATE_ID.eq(templateId))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.IS_DELETED.isFalse())
            .fetchOneInto(DocumentTemplateUpdatedOnOrderRecord.class);
        if (record == null) {
            return Optional.empty();
        }

        return Optional.of(mapper.toCustomTemplateUpdatedOnOrder(record));
    }

    public List<CustomTemplateUpdateOnOrder> getCustomTemplatesOnOrder(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<DocumentTemplateUpdatedOnOrderRecord> records = dslContext
            .select()
            .from(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER)
            .where(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.IS_DELETED.isFalse())
            .fetchInto(DocumentTemplateUpdatedOnOrderRecord.class);
        return mapper.toCustomTemplateUpdatedOnOrders(records);
    }

    public void deleteCustomTemplateOnOrder(String templateId, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        dslContext
            .update(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER)
            .set(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.IS_DELETED, true)
            .where(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.TEMPLATE_ID.eq(templateId))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    public void deleteCustomTemplatesOnOrder(DSLContext dslContext, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        dslContext
            .update(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER)
            .set(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.IS_DELETED, true)
            .where(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_UPDATED_ON_ORDER.IS_DELETED.isFalse())
            .execute();
    }
}
