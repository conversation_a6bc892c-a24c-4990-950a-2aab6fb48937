package com.subskribe.billy.template.services;

import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.template.db.DocumentMasterTemplateDAO;
import com.subskribe.billy.template.db.DocumentSectionDAO;
import com.subskribe.billy.template.db.DocumentTemplateDAO;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.model.DocumentSection;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.model.DocumentTemplateStatus;
import com.subskribe.billy.validation.Validator;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.mapstruct.factory.Mappers;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class DocumentTemplateGetService {

    private final DocumentMasterTemplateDAO documentMasterTemplateDAO;
    private final DocumentTemplateDAO documentTemplateDAO;
    private final DocumentSectionDAO documentSectionDAO;
    private final OrderTermsService orderTermsService;
    private final ProductCatalogGetService productCatalogGetService;
    private final PlanTermsService planTermsService;
    private final PlanMapper planMapper;

    @Inject
    public DocumentTemplateGetService(
        DocumentMasterTemplateDAO documentMasterTemplateDAO,
        DocumentTemplateDAO documentTemplateDAO,
        DocumentSectionDAO documentSectionDAO,
        OrderTermsService orderTermsService,
        ProductCatalogGetService productCatalogGetService,
        PlanTermsService planTermsService
    ) {
        this.documentMasterTemplateDAO = documentMasterTemplateDAO;
        this.documentTemplateDAO = documentTemplateDAO;
        this.documentSectionDAO = documentSectionDAO;
        this.orderTermsService = orderTermsService;
        this.productCatalogGetService = productCatalogGetService;
        this.planTermsService = planTermsService;
        planMapper = Mappers.getMapper(PlanMapper.class);
    }

    public List<DocumentMasterTemplate> getDocumentMasterTemplates(DocumentTemplateType templateType) {
        return documentMasterTemplateDAO.getDocumentMasterTemplates(Optional.empty(), templateType);
    }

    public List<DocumentMasterTemplate> getDocumentMasterTemplatesForTenant(Optional<String> tenantIdOptional, DocumentTemplateType templateType) {
        // This method is able to access templates for all tenants, protected by BILLY_ADMIN and BILLY_ENGINEER roles
        return documentMasterTemplateDAO.getDocumentMasterTemplates(tenantIdOptional, templateType);
    }

    public Optional<DocumentMasterTemplate> getDocumentMasterTemplateById(String documentMasterTemplateId, Optional<String> optionalTenantId) {
        if (StringUtils.isBlank(documentMasterTemplateId)) {
            throw new IllegalArgumentException("document template id is required");
        }
        return documentMasterTemplateDAO.getDocumentMasterTemplateByUUID(UUID.fromString(documentMasterTemplateId), optionalTenantId);
    }

    public Optional<DocumentMasterTemplate> getDefaultMasterTemplate(DocumentTemplateType type) {
        return documentMasterTemplateDAO.getDefaultMasterTemplate(type);
    }

    public Optional<DocumentMasterTemplate> getMasterTemplateByNameAndType(String name, DocumentTemplateType type) {
        return documentMasterTemplateDAO.getMasterTemplateByNameAndType(name, type);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public Optional<DocumentTemplate> getTemplateByNameAndType(String name, DocumentTemplateType type, Optional<DocumentTemplateStatus> status) {
        return documentTemplateDAO.getTemplateByNameAndType(name, type, status);
    }

    public List<DocumentTemplate> getDocumentTemplates(DocumentTemplateType type) {
        List<DocumentTemplate> templates;
        if (type == null) {
            templates = documentTemplateDAO.getDocumentTemplates();
        } else {
            templates = documentTemplateDAO.getDocumentTemplatesByType(type);
        }

        populateInUseFlagForDocumentTemplates(templates);

        return templates.stream().sorted(Comparator.comparing(DocumentTemplate::getName, String.CASE_INSENSITIVE_ORDER)).collect(Collectors.toList());
    }

    public List<DocumentTemplate> getDocumentTemplateVersions(String templateId) {
        Validator.validateStringNotBlank(templateId, "templateId");

        List<DocumentTemplate> templates = documentTemplateDAO.getDocumentTemplateVersions(templateId);

        populateInUseFlagForDocumentTemplates(templates);

        return templates.stream().sorted(Comparator.comparing(DocumentTemplate::getName, String.CASE_INSENSITIVE_ORDER)).collect(Collectors.toList());
    }

    public DocumentTemplate getDocumentTemplateByTemplateId(String templateId) {
        Validator.validateStringNotBlank(templateId, "templateId");

        DocumentTemplate documentTemplate = documentTemplateDAO
            .getDocumentTemplateByTemplateId(templateId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_TEMPLATE, templateId));
        populateDocumentTemplateFields(documentTemplate);
        return documentTemplate;
    }

    public DocumentTemplate getDocumentTemplateByTemplateId(String templateId, int version) {
        Validator.validateStringNotBlank(templateId, "templateId");
        if (version < 1) {
            throw new InvalidInputException("version must be greater than 0");
        }

        DocumentTemplate documentTemplate = documentTemplateDAO
            .getDocumentTemplateByTemplateId(templateId, version)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_TEMPLATE, templateId));
        populateDocumentTemplateFields(documentTemplate);
        return documentTemplate;
    }

    private void populateDocumentTemplateFields(DocumentTemplate documentTemplate) {
        List<Plan> plans = getPlans(documentTemplate);
        documentTemplate.setPlans(planMapper.plansToJson(plans));
        populateInUseFlagForDocumentTemplate(documentTemplate);
    }

    private List<Plan> getPlans(DocumentTemplate documentTemplate) {
        List<String> planIds = planTermsService.getPlanIdsByDocumentTemplateId(documentTemplate.getTemplateId());
        if (CollectionUtils.isEmpty(planIds)) {
            return List.of();
        }
        Set<String> uniquePlanIds = new HashSet<>(planIds);

        List<Plan> plans = productCatalogGetService.getPlansByPlanIdsWithoutChecks(uniquePlanIds);
        return plans.stream().sorted(Comparator.comparing(Plan::getName, String.CASE_INSENSITIVE_ORDER)).collect(Collectors.toList());
    }

    public List<DocumentTemplate> getDocumentTemplatesByTemplateIds(Collection<String> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return List.of();
        }

        List<DocumentTemplate> templates = documentTemplateDAO.getDocumentTemplatesByTemplateIds(templateIds);

        populateInUseFlagForDocumentTemplates(templates);

        return templates;
    }

    public List<DocumentTemplate> getDocumentTemplatesByTemplateIdVersionPairs(List<Pair<String, Integer>> templateIdVersionPairs) {
        if (CollectionUtils.isEmpty(templateIdVersionPairs)) {
            return List.of();
        }

        List<DocumentTemplate> templates = documentTemplateDAO.getDocumentTemplatesByTemplateIdVersionPairs(templateIdVersionPairs);

        populateInUseFlagForDocumentTemplates(templates);

        return templates;
    }

    private void populateInUseFlagForDocumentTemplate(DocumentTemplate documentTemplate) {
        boolean isInUse = orderTermsService.documentTemplateUsedByOrder(documentTemplate.getTemplateId());
        isInUse |= planTermsService.documentTemplateUsedByPlan(documentTemplate.getTemplateId());
        documentTemplate.setInUse(isInUse);
    }

    private void populateInUseFlagForDocumentTemplates(List<DocumentTemplate> documentTemplates) {
        List<String> templateIds = documentTemplates.stream().map(DocumentTemplate::getTemplateId).collect(Collectors.toList());
        Set<String> orderTemplatesInUse = orderTermsService.getTemplatesInUse(templateIds);
        Map<String, List<String>> planTemplatesInUse = planTermsService.getPlanIdsAssociatedWithDocumentTemplates(templateIds);

        for (DocumentTemplate documentTemplate : documentTemplates) {
            String templateId = documentTemplate.getTemplateId();
            boolean isInUse = orderTemplatesInUse.contains(templateId) || planTemplatesInUse.containsKey(templateId);
            documentTemplate.setInUse(isInUse);
        }
    }

    public List<DocumentSection> getDocumentSections() {
        return documentSectionDAO.getDocumentSections();
    }

    public DocumentSection getDocumentSectionById(String sectionId) {
        return documentSectionDAO
            .getDocumentSectionByUUID(UUID.fromString(sectionId))
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_SECTION, sectionId));
    }

    public boolean isDocumentSectionInUse(UUID sectionId) {
        return documentTemplateDAO.isDocumentSectionInUse(sectionId);
    }
}
