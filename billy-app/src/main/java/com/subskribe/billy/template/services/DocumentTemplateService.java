package com.subskribe.billy.template.services;

import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.entity.service.EntityService;
import com.subskribe.billy.exception.DuplicateObjectException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.invoicedunning.model.DunningReminderType;
import com.subskribe.billy.order.services.OrderDocumentService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.template.db.DocumentMasterTemplateDAO;
import com.subskribe.billy.template.db.DocumentSectionDAO;
import com.subskribe.billy.template.db.DocumentTemplateDAO;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.model.DocumentMasterTemplateStatus;
import com.subskribe.billy.template.model.DocumentSection;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.model.DocumentTemplateStatus;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class DocumentTemplateService {

    private final DocumentTemplateGetService documentTemplateGetService;
    private final OrderGetService orderGetService;
    private final EntityContextResolver entityContextResolver;
    private final EntityGetService entityGetService;
    private final DocumentMasterTemplateDAO documentMasterTemplateDAO;
    private final DocumentTemplateDAO documentTemplateDAO;
    private final DocumentSectionDAO documentSectionDAO;
    private final PlanTermsService planTermsService;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final EntityService entityService;
    private final EntityContextProvider entityContextProvider;
    private final FeatureService featureService;
    private final OrderDocumentService orderDocumentService;

    @Inject
    public DocumentTemplateService(
        DocumentTemplateGetService documentTemplateGetService,
        OrderGetService orderGetService,
        EntityContextResolver entityContextResolver,
        EntityGetService entityGetService,
        DocumentMasterTemplateDAO documentMasterTemplateDAO,
        DocumentTemplateDAO documentTemplateDAO,
        DocumentSectionDAO documentSectionDAO,
        PlanTermsService planTermsService,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider,
        EntityService entityService,
        EntityContextProvider entityContextProvider,
        FeatureService featureService,
        OrderDocumentService orderDocumentService
    ) {
        this.documentTemplateGetService = documentTemplateGetService;
        this.orderGetService = orderGetService;
        this.entityContextResolver = entityContextResolver;
        this.entityGetService = entityGetService;
        this.documentMasterTemplateDAO = documentMasterTemplateDAO;
        this.documentTemplateDAO = documentTemplateDAO;
        this.documentSectionDAO = documentSectionDAO;
        this.planTermsService = planTermsService;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.entityService = entityService;
        this.entityContextProvider = entityContextProvider;
        this.featureService = featureService;
        this.orderDocumentService = orderDocumentService;
    }

    public DocumentTemplate addDocumentTemplate(DocumentTemplate documentTemplate) {
        validateDocumentTemplate(documentTemplate);
        validateDuplicateName(documentTemplate);

        if (
            documentTemplate.getStatus() != null &&
            documentTemplate.getStatus() != DocumentTemplateStatus.DRAFT &&
            documentTemplate.getType() == DocumentTemplateType.ORDER
        ) {
            throw new IllegalArgumentException(String.format("Predefined terms: %s is not in draft state", documentTemplate.getName()));
        }

        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(documentTemplate.getEntityIds());
        documentTemplate.setEntityIds(entityIds);
        validateRelatedSectionEntities(documentTemplate);
        documentTemplate.setContent(documentTemplate.getContent());
        documentTemplate.setTemplateId(UUID.randomUUID().toString());
        documentTemplate.setVersion(1);

        if (documentTemplate.getType() == DocumentTemplateType.INVOICE) {
            String entityId = entityContextProvider.provideSelected();
            entityService.updateWireInstruction(entityId, documentTemplate.getContent());
        }

        return documentTemplateDAO.addDocumentTemplate(documentTemplate);
    }

    public void updateDocumentTemplate(DocumentTemplate documentTemplate) {
        validateDocumentTemplate(documentTemplate);
        validateDuplicateName(documentTemplate);

        // check if a record with given ID exists. If not the method throws ObjectNotFoundException
        DocumentTemplate originalDocumentTemplate = documentTemplateGetService.getDocumentTemplateByTemplateId(documentTemplate.getTemplateId());
        boolean documentContentChanged = originalDocumentTemplate.getContent().compareTo(documentTemplate.getContent()) != 0;

        documentTemplate.setId(originalDocumentTemplate.getId());

        // Predefined Terms status is managed through updateDocumentTemplateStatus.
        // UpdateDocumentTemplate should leave the status as is and update other fields on it.
        documentTemplate.setStatus(originalDocumentTemplate.getStatus());

        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            documentTemplate.getEntityIds(),
            originalDocumentTemplate.getEntityIds()
        );
        documentTemplate.setEntityIds(entityIds);
        validateRelatedSectionEntities(documentTemplate);

        if (documentContentChanged) {
            documentTemplate.setContent(documentTemplate.getContent());
        }

        documentTemplate.setTemplateId(originalDocumentTemplate.getTemplateId());
        documentTemplate.setVersion(originalDocumentTemplate.getVersion());

        if (documentTemplate.getType() == DocumentTemplateType.INVOICE) {
            String entityId = entityContextProvider.provideSelected();
            entityService.updateWireInstruction(entityId, documentTemplate.getContent());
        }

        if (
            originalDocumentTemplate.getType() == DocumentTemplateType.ORDER &&
            originalDocumentTemplate.getStatus() != DocumentTemplateStatus.DRAFT &&
            originalDocumentTemplate.getInUse() &&
            documentContentChanged
        ) {
            addDocumentTemplateVersion(documentTemplate, originalDocumentTemplate);
        } else {
            documentTemplateDAO.updateDocumentTemplate(documentTemplate);
        }
    }

    private void validateDocumentTemplate(DocumentTemplate documentTemplate) {
        if (documentTemplate == null) {
            throw new IllegalArgumentException("documentTemplate is required");
        }

        documentTemplate.validate();

        if (documentTemplate.getStatus() == null) {
            if (documentTemplate.getType() == DocumentTemplateType.ORDER) {
                documentTemplate.setStatus(DocumentTemplateStatus.DRAFT);
            } else {
                documentTemplate.setStatus(DocumentTemplateStatus.ACTIVE);
            }
        }

        // Precompile the template to check for errors
        orderDocumentService.previewDocumentTemplate(documentTemplate);
    }

    private void validateDuplicateName(DocumentTemplate documentTemplate) {
        var existingDocumentTemplate = documentTemplateDAO.getTemplateByNameAndType(
            documentTemplate.getName(),
            documentTemplate.getType(),
            Optional.empty()
        );
        if (
            existingDocumentTemplate.isPresent() && !Objects.equals(documentTemplate.getTemplateId(), existingDocumentTemplate.get().getTemplateId())
        ) {
            throw new DuplicateObjectException(String.format("Predefined terms with name %s already exist", documentTemplate.getName()));
        }
    }

    private void validateRelatedSectionEntities(DocumentTemplate documentTemplate) {
        if (CollectionUtils.isEmpty(documentTemplate.getEntityIds())) {
            throw new InvalidInputException("Predefined terms must have at least one entity assigned");
        }
        if (Objects.isNull(documentTemplate.getSectionUuid())) {
            return;
        }
        var section = documentSectionDAO
            .getDocumentSectionByUUID(documentTemplate.getSectionUuid())
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_SECTION, documentTemplate.getSectionUuid().toString()));
        if (!EntityContext.isSubsetOf(documentTemplate.getEntityIds(), section.getEntityIds())) {
            String message = String.format(
                "The section \"%s\" has lesser level of entity access than the predefined terms. The Section must have greater or equal entity access than the Predefined Terms referencing it.",
                section.getName()
            );
            throw new InvalidInputException(message);
        }
    }

    private void addDocumentTemplateVersion(DocumentTemplate documentTemplate, DocumentTemplate originalDocumentTemplate) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        dslContext.transaction(configuration -> {
            originalDocumentTemplate.setHasNewerVersion(true);
            documentTemplateDAO.updateDocumentTemplateInTransaction(originalDocumentTemplate, configuration);

            documentTemplate.setHasNewerVersion(false);
            documentTemplate.setVersion(documentTemplate.getVersion() + 1);
            documentTemplateDAO.addDocumentTemplateInTransaction(documentTemplate, configuration);
        });
    }

    public void updateDocumentTemplateStatus(DocumentTemplate documentTemplate, DocumentTemplateStatus documentTemplateStatus) {
        validateUpdateDocumentTemplateStatusRequest(documentTemplate, documentTemplateStatus);
        validateDocumentTemplateStatusTransition(documentTemplate.getStatus(), documentTemplateStatus);

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        dslContext.transaction(configuration -> {
            documentTemplate.setStatus(documentTemplateStatus);
            documentTemplateDAO.updateDocumentTemplateInTransaction(documentTemplate, configuration);
            if (documentTemplateStatus == DocumentTemplateStatus.DEPRECATED) {
                planTermsService.removeAllPlanIdsForDocumentTemplate(documentTemplate.getTemplateId(), configuration);
            }
        });
    }

    private void validateUpdateDocumentTemplateStatusRequest(DocumentTemplate documentTemplate, DocumentTemplateStatus documentTemplateStatus) {
        if (documentTemplate == null) {
            throw new IllegalArgumentException("predefined terms id is null");
        }

        if (documentTemplateStatus == null) {
            throw new IllegalArgumentException(
                "Invalid DocumentTemplate status. DocumentTemplate status should be either DRAFT, ACTIVE, or DEPRECATED"
            );
        }
    }

    private void validateDocumentTemplateStatusTransition(DocumentTemplateStatus currentStatus, DocumentTemplateStatus newStatus) {
        switch (currentStatus) {
            case DRAFT -> checkDocumentTemplateStatusTransitionOrThrow(currentStatus, newStatus, List.of(DocumentTemplateStatus.ACTIVE));
            case ACTIVE -> checkDocumentTemplateStatusTransitionOrThrow(
                currentStatus,
                newStatus,
                List.of(DocumentTemplateStatus.DRAFT, DocumentTemplateStatus.DEPRECATED)
            );
            case DEPRECATED -> throw new IllegalArgumentException("Predefined terms in deprecated state cannot be updated");
            default -> throw new IllegalStateException(
                "current status of the predefined terms is not recognized. current predefined terms status = " + currentStatus
            );
        }
    }

    private void checkDocumentTemplateStatusTransitionOrThrow(
        DocumentTemplateStatus currentStatus,
        DocumentTemplateStatus newStatus,
        List<DocumentTemplateStatus> validStates
    ) {
        if (!validStates.contains(newStatus)) {
            var message = String.format(
                "cannot transition to predefined terms status %s from status %s. Valid status options are %s",
                newStatus,
                currentStatus,
                String.join(", ", validStates.toString())
            );
            throw new IllegalArgumentException(message);
        }
    }

    public List<DocumentTemplate> deleteDocumentTemplates(String templateId, Optional<String> optionalTenantId) {
        if (StringUtils.isBlank(templateId)) {
            throw new IllegalArgumentException("templateId is required");
        }

        // check if a record with given ID exists. If not the method throws ObjectNotFoundException
        DocumentTemplate originalDocumentTemplate = documentTemplateGetService.getDocumentTemplateByTemplateId(templateId);

        if (originalDocumentTemplate.getInUse()) {
            throw new IllegalArgumentException("Predefined terms cannot be deleted because it is already in use");
        }

        return documentTemplateDAO.deleteDocumentTemplates(templateId, optionalTenantId);
    }

    public DocumentSection upsertDocumentSection(DocumentSection documentSection) {
        if (documentSection.getId() == null) {
            Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(documentSection.getEntityIds());
            documentSection.setEntityIds(entityIds);
            return documentSectionDAO.createDocumentSection(documentSection);
        }
        var existingDocumentSection = documentSectionDAO.getDocumentSectionByUUID(documentSection.getId());
        if (existingDocumentSection.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.DOCUMENT_SECTION, documentSection.getId().toString());
        }

        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            documentSection.getEntityIds(),
            existingDocumentSection.get().getEntityIds()
        );
        documentSection.setEntityIds(entityIds);
        validateRelatedTermsEntities(documentSection);

        return documentSectionDAO.updateDocumentSection(documentSection);
    }

    private void validateRelatedTermsEntities(DocumentSection documentSection) {
        if (CollectionUtils.isEmpty(documentSection.getEntityIds())) {
            throw new InvalidInputException("Predefined terms section must have at least one entity assigned");
        }
        var allTerms = documentTemplateDAO.getDocumentTemplates();
        List<DocumentTemplate> offendingTerms = allTerms
            .stream()
            .filter(term -> Objects.nonNull(term.getSectionUuid()) && term.getSectionUuid().equals(documentSection.getId()))
            .filter(term -> !EntityContext.isSubsetOf(term.getEntityIds(), documentSection.getEntityIds()))
            .toList();
        if (CollectionUtils.isEmpty(offendingTerms)) {
            return;
        }
        String offendingTermsString = offendingTerms.stream().map(DocumentTemplate::getName).collect(Collectors.joining(", ", "[ ", " ]"));
        String message = String.format(
            "This section is being used in Predefined Term(s) %s which has greater level of entity access. The Section must have greater or equal entity access than the Predefined Terms referencing it.",
            offendingTermsString
        );
        throw new InvalidInputException(message);
    }

    public DocumentMasterTemplate upsertDocumentMasterTemplate(DocumentMasterTemplate documentMasterTemplate, Optional<String> optionalTenantId) {
        validateDocumentMasterTemplate(documentMasterTemplate);

        if (documentMasterTemplate.getId() == null) {
            return addDocumentMasterTemplate(documentMasterTemplate, optionalTenantId, true);
        }

        return updateDocumentMasterTemplate(documentMasterTemplate, optionalTenantId);
    }

    public DocumentMasterTemplate upsertDocumentMasterTemplateV2(DocumentMasterTemplate documentMasterTemplate, Optional<String> optionalTenantId) {
        if (!featureService.isEnabled(Feature.MASTER_TEMPLATE_EDITOR_V2)) {
            throw new UnsupportedOperationException("Document template editor v2 is not enabled");
        }

        validateDocumentMasterTemplate(documentMasterTemplate);

        if (documentMasterTemplate.getId() == null) {
            return addDocumentMasterTemplate(documentMasterTemplate, optionalTenantId, false);
        }

        return updateDocumentMasterTemplate(documentMasterTemplate, optionalTenantId);
    }

    private void validateDocumentMasterTemplate(DocumentMasterTemplate documentMasterTemplate) {
        Validator.validateNonNullArgument(documentMasterTemplate, "documentMasterTemplate");
        documentMasterTemplate.validate();

        if (documentMasterTemplate.getType() == DocumentTemplateType.DUNNING) {
            Validator.validateNonNullArgument(documentMasterTemplate.getConfiguration(), "documentMasterTemplate.configuration");
            Validator.validateNonNullArgument(
                documentMasterTemplate.getConfiguration().getDunningReminderType(),
                "documentMasterTemplate.configuration.dunningReminderType"
            );
        }

        if (documentMasterTemplate.getType() == DocumentTemplateType.ORDER) {
            // Precompile the template to check for errors
            orderDocumentService.previewMasterTemplate(documentMasterTemplate);
        }
    }

    private static void validateAndSetDocumentMasterTemplateStatus(DocumentMasterTemplate documentMasterTemplate) {
        if (documentMasterTemplate.getType() != DocumentTemplateType.ORDER) {
            return;
        }

        if (documentMasterTemplate.getStatus() == null) {
            // default new document to draft if status is not set in input
            documentMasterTemplate.setStatus(DocumentMasterTemplateStatus.DRAFT);
        }
    }

    private static void validateAndSetDocumentMasterTemplateIsFullHtml(DocumentMasterTemplate documentMasterTemplate) {
        if (documentMasterTemplate.getType() != DocumentTemplateType.ORDER) {
            return;
        }

        if (documentMasterTemplate.getIsFullHtml() == null) {
            // default new document to partial HTML if isFullHtml is not set in input
            documentMasterTemplate.setIsFullHtml(false);
        } else if (documentMasterTemplate.getIsFullHtml()) {
            throw new InvalidInputException(String.format("Document template: %s is not partial HTML", documentMasterTemplate.getName()));
        }
    }

    private void validateUniqueDocumentMasterTemplateType(DocumentMasterTemplate documentMasterTemplate, Optional<String> optionalTenantId) {
        switch (documentMasterTemplate.getType()) {
            case DUNNING -> {
                DunningReminderType dunningReminderType = documentMasterTemplate.getConfiguration().getDunningReminderType();
                boolean dunningTemplateExists = documentMasterTemplateDAO
                    .getDocumentMasterTemplates(optionalTenantId, DocumentTemplateType.DUNNING)
                    .stream()
                    .anyMatch(template -> dunningReminderType == template.getConfiguration().getDunningReminderType());
                if (dunningTemplateExists) {
                    throw new DuplicateObjectException(String.format("Dunning template with reminder type %s already exists", dunningReminderType));
                }
            }
            case ESIGN -> {
                Optional<DocumentMasterTemplate> esignTemplate = documentMasterTemplateDAO
                    .getDocumentMasterTemplates(optionalTenantId, DocumentTemplateType.ESIGN)
                    .stream()
                    .findFirst();
                if (esignTemplate.isPresent()) {
                    throw new DuplicateObjectException("Esign template already exists");
                }
            }
        }
    }

    private DocumentMasterTemplate addDocumentMasterTemplate(
        DocumentMasterTemplate documentMasterTemplate,
        Optional<String> optionalTenantId,
        boolean isV1
    ) {
        validateAndSetDocumentMasterTemplateStatus(documentMasterTemplate);
        validateUniqueDocumentMasterTemplateType(documentMasterTemplate, optionalTenantId);

        if (isV1) {
            // isFullHtml is always true for new order templates created via V1 API
            documentMasterTemplate.setIsFullHtml(true);
        } else {
            validateAndSetDocumentMasterTemplateIsFullHtml(documentMasterTemplate);
        }

        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(documentMasterTemplate.getEntityIds());
        documentMasterTemplate.setEntityIds(entityIds);

        return documentMasterTemplateDAO.createDocumentMasterTemplate(documentMasterTemplate, optionalTenantId);
    }

    private DocumentMasterTemplate updateDocumentMasterTemplate(DocumentMasterTemplate documentMasterTemplate, Optional<String> optionalTenantId) {
        DocumentMasterTemplate existingMasterTemplate = documentMasterTemplateDAO
            .getDocumentMasterTemplateByUUID(documentMasterTemplate.getId(), optionalTenantId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_MASTER_TEMPLATE, documentMasterTemplate.getId().toString()));

        setDocumentMasterTemplateStatusAndIsFullHtmlIfNotSet(documentMasterTemplate, existingMasterTemplate);

        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            documentMasterTemplate.getEntityIds(),
            existingMasterTemplate.getEntityIds()
        );
        documentMasterTemplate.setEntityIds(entityIds);
        validateOrderEntitiesForTemplateUpdate(documentMasterTemplate, existingMasterTemplate);

        return documentMasterTemplateDAO.updateDocumentMasterTemplate(documentMasterTemplate, optionalTenantId);
    }

    private void setDocumentMasterTemplateStatusAndIsFullHtmlIfNotSet(
        DocumentMasterTemplate documentMasterTemplate,
        DocumentMasterTemplate existingMasterTemplate
    ) {
        if (documentMasterTemplate.getStatus() == null) {
            // if input template status is not set, use existing saved document status.
            documentMasterTemplate.setStatus(existingMasterTemplate.getStatus());
        }

        if (documentMasterTemplate.getIsFullHtml() == null) {
            // if input template isFullHtml is not set, use existing saved document isFullHtml.
            documentMasterTemplate.setIsFullHtml(existingMasterTemplate.getIsFullHtml());
        }
    }

    public void updateDocumentMasterTemplateStatus(
        DocumentMasterTemplate documentMasterTemplate,
        DocumentMasterTemplateStatus documentMasterTemplateStatus,
        Optional<String> optionalTenantId
    ) {
        validateUpdateDocumentMasterTemplateStatusRequest(documentMasterTemplate, documentMasterTemplateStatus);
        validateDocumentMasterTemplateStatusTransition(documentMasterTemplate.getStatus(), documentMasterTemplateStatus);

        documentMasterTemplate.setStatus(documentMasterTemplateStatus);
        documentMasterTemplateDAO.updateDocumentMasterTemplate(documentMasterTemplate, optionalTenantId);
    }

    private void validateUpdateDocumentMasterTemplateStatusRequest(
        DocumentMasterTemplate documentMasterTemplate,
        DocumentMasterTemplateStatus documentMasterTemplateStatus
    ) {
        if (documentMasterTemplate == null) {
            throw new IllegalArgumentException("document template id is null");
        }

        if (documentMasterTemplateStatus == null) {
            throw new IllegalArgumentException(
                "Invalid DocumentMasterTemplate status. DocumentMasterTemplate status should be either DRAFT, ACTIVE, or DEPRECATED"
            );
        }
    }

    private void validateDocumentMasterTemplateStatusTransition(DocumentMasterTemplateStatus currentStatus, DocumentMasterTemplateStatus newStatus) {
        switch (currentStatus) {
            case DRAFT -> checkDocumentMasterTemplateStatusTransitionOrThrow(currentStatus, newStatus, List.of(DocumentMasterTemplateStatus.ACTIVE));
            case ACTIVE -> checkDocumentMasterTemplateStatusTransitionOrThrow(currentStatus, newStatus, List.of(DocumentMasterTemplateStatus.DRAFT));
            default -> throw new IllegalStateException(
                "current status of the document template is not recognized. current document template status = " + currentStatus
            );
        }
    }

    // verify that there are no orders in entities that are being removed from the template
    private void validateOrderEntitiesForTemplateUpdate(
        DocumentMasterTemplate documentMasterTemplate,
        DocumentMasterTemplate existingMasterTemplate
    ) {
        if (documentMasterTemplate.getEntityIds().contains(EntityContext.ALL_ACCESS_ID)) {
            return;
        }
        if (documentMasterTemplate.getEntityIds().equals(existingMasterTemplate.getEntityIds())) {
            return;
        }
        Set<String> orderEntities = orderGetService.entitiesUsedByOrdersUsingTemplate(existingMasterTemplate.getId());
        entityGetService.validateParentEntitiesForUpdate(documentMasterTemplate.getEntityIds(), orderEntities, "template", "orders");
    }

    private void checkDocumentMasterTemplateStatusTransitionOrThrow(
        DocumentMasterTemplateStatus currentStatus,
        DocumentMasterTemplateStatus newStatus,
        List<DocumentMasterTemplateStatus> validStates
    ) {
        if (!validStates.contains(newStatus)) {
            String message = String.format(
                "cannot transition to document template status %s from status %s. Valid status options are %s",
                newStatus,
                currentStatus,
                String.join(", ", validStates.toString())
            );
            throw new IllegalArgumentException(message);
        }
    }

    public DocumentMasterTemplate deleteDocumentMasterTemplate(String documentMasterTemplateId, Optional<String> optionalTenantId) {
        if (StringUtils.isBlank(documentMasterTemplateId)) {
            throw new IllegalArgumentException("document template id is required");
        }

        // check if a record with given ID exists. If not throw ObjectNotFoundException
        documentTemplateGetService
            .getDocumentMasterTemplateById(documentMasterTemplateId, optionalTenantId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_MASTER_TEMPLATE, documentMasterTemplateId));

        // check if this template is in use
        if (orderGetService.isMasterTemplateInUse(UUID.fromString(documentMasterTemplateId))) {
            throw new IllegalArgumentException("this document template is in use and cannot be deleted: " + documentMasterTemplateId);
        }

        return documentMasterTemplateDAO.deleteDocumentMasterTemplate(UUID.fromString(documentMasterTemplateId));
    }

    public void populateInUseFlagForMasterTemplate(DocumentMasterTemplate masterTemplate) {
        masterTemplate.setInUse(orderGetService.isMasterTemplateInUse(masterTemplate.getId()));
    }

    public void deleteDocumentSection(String sectionId) {
        Validator.validateStringNotBlank(sectionId, "term section id");

        DocumentSection originalDocumentSection = documentSectionDAO
            .getDocumentSectionByUUID(UUID.fromString(sectionId))
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_SECTION, sectionId));

        if (documentTemplateGetService.isDocumentSectionInUse(originalDocumentSection.getId())) {
            throw new IllegalArgumentException("Predefined terms section cannot be deleted because it is already in use");
        }

        documentSectionDAO.deleteDocumentSection(UUID.fromString(sectionId));
    }
}
