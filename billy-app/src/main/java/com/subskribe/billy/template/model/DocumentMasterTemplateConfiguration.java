package com.subskribe.billy.template.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.invoicedunning.model.DunningReminderType;
import graphql.annotations.annotationTypes.GraphQLConstructor;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.List;
import javax.validation.constraints.NotNull;

public class DocumentMasterTemplateConfiguration {

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private int version;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private String primaryColor;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private String secondaryColor;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private String fontFamily;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private String fontSize;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private HeaderStyle headerStyle;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private ContractTermsTableStyle contractTermsTableStyle;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private List<ContractTermsColumn> contractTermsColumns;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private LineItemTableStyle lineItemTableStyle;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private List<LineItemColumn> lineItemColumns;

    @GraphQLField
    @JsonProperty
    private Boolean showLineBreakupByPredefinedDiscounts;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private Boolean showBreakupByBillingCycle;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private Boolean showNextInvoiceAmount;

    @GraphQLField
    @JsonProperty
    @GraphQLNonNull
    @NotNull
    private Boolean showArrByYears;

    @GraphQLField
    @JsonProperty
    private DunningReminderType dunningReminderType;

    public DocumentMasterTemplateConfiguration() {}

    @GraphQLConstructor
    @GraphQLNonNull
    public DocumentMasterTemplateConfiguration(
        @GraphQLName("version") int version,
        @GraphQLName("primaryColor") String primaryColor,
        @GraphQLName("secondaryColor") String secondaryColor,
        @GraphQLName("fontFamily") String fontFamily,
        @GraphQLName("fontSize") String fontSize,
        @GraphQLName("headerStyle") HeaderStyle headerStyle,
        @GraphQLName("contractTermsTableStyle") ContractTermsTableStyle contractTermsTableStyle,
        @GraphQLName("contractTermsColumns") List<ContractTermsColumn> contractTermsColumns,
        @GraphQLName("lineItemTableStyle") LineItemTableStyle lineItemTableStyle,
        @GraphQLName("lineItemColumns") List<LineItemColumn> lineItemColumns,
        @GraphQLName("showLineBreakupByPredefinedDiscounts") Boolean showLineBreakupByPredefinedDiscounts,
        @GraphQLName("showBreakupByBillingCycle") Boolean showBreakupByBillingCycle,
        @GraphQLName("showNextInvoiceAmount") Boolean showNextInvoiceAmount,
        @GraphQLName("showArrByYears") Boolean showArrByYears,
        @GraphQLName("dunningReminderType") DunningReminderType dunningReminderType
    ) {
        this.version = version;
        this.primaryColor = primaryColor;
        this.secondaryColor = secondaryColor;
        this.fontFamily = fontFamily;
        this.fontSize = fontSize;
        this.headerStyle = headerStyle;
        this.contractTermsTableStyle = contractTermsTableStyle;
        this.contractTermsColumns = contractTermsColumns;
        this.lineItemTableStyle = lineItemTableStyle;
        this.lineItemColumns = lineItemColumns;
        this.showLineBreakupByPredefinedDiscounts = showLineBreakupByPredefinedDiscounts;
        this.showBreakupByBillingCycle = showBreakupByBillingCycle;
        this.showNextInvoiceAmount = showNextInvoiceAmount;
        this.showArrByYears = showArrByYears;
        this.dunningReminderType = dunningReminderType;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public String getPrimaryColor() {
        return primaryColor;
    }

    public void setPrimaryColor(String primaryColor) {
        this.primaryColor = primaryColor;
    }

    public String getSecondaryColor() {
        return secondaryColor;
    }

    public void setSecondaryColor(String secondaryColor) {
        this.secondaryColor = secondaryColor;
    }

    public String getFontFamily() {
        return fontFamily;
    }

    public void setFontFamily(String fontFamily) {
        this.fontFamily = fontFamily;
    }

    public String getFontSize() {
        return fontSize;
    }

    public void setFontSize(String fontSize) {
        this.fontSize = fontSize;
    }

    public HeaderStyle getHeaderStyle() {
        return headerStyle;
    }

    public void setHeaderStyle(HeaderStyle headerStyle) {
        this.headerStyle = headerStyle;
    }

    public ContractTermsTableStyle getContractTermsTableStyle() {
        return contractTermsTableStyle;
    }

    public void setContractTermsTableStyle(ContractTermsTableStyle contractTermsTableStyle) {
        this.contractTermsTableStyle = contractTermsTableStyle;
    }

    public List<ContractTermsColumn> getContractTermsColumns() {
        return contractTermsColumns;
    }

    public void setContractTermsColumns(List<ContractTermsColumn> contractTermsColumns) {
        this.contractTermsColumns = contractTermsColumns;
    }

    public LineItemTableStyle getLineItemTableStyle() {
        return lineItemTableStyle;
    }

    public void setLineItemTableStyle(LineItemTableStyle lineItemTableStyle) {
        this.lineItemTableStyle = lineItemTableStyle;
    }

    public List<LineItemColumn> getLineItemColumns() {
        return lineItemColumns;
    }

    public void setLineItemColumns(List<LineItemColumn> lineItemColumns) {
        this.lineItemColumns = lineItemColumns;
    }

    public Boolean getShowLineBreakupByPredefinedDiscounts() {
        return showLineBreakupByPredefinedDiscounts;
    }

    public void setShowLineBreakupByPredefinedDiscounts(Boolean showLineBreakupByPredefinedDiscounts) {
        this.showLineBreakupByPredefinedDiscounts = showLineBreakupByPredefinedDiscounts;
    }

    public Boolean getShowBreakupByBillingCycle() {
        return showBreakupByBillingCycle;
    }

    public void setShowBreakupByBillingCycle(Boolean showBreakupByBillingCycle) {
        this.showBreakupByBillingCycle = showBreakupByBillingCycle;
    }

    public Boolean getShowNextInvoiceAmount() {
        return showNextInvoiceAmount;
    }

    public void setShowNextInvoiceAmount(Boolean showNextInvoiceAmount) {
        this.showNextInvoiceAmount = showNextInvoiceAmount;
    }

    public Boolean getShowArrByYears() {
        return showArrByYears;
    }

    public void setShowArrByYears(Boolean showArrByYears) {
        this.showArrByYears = showArrByYears;
    }

    public DunningReminderType getDunningReminderType() {
        return dunningReminderType;
    }

    public void setDunningReminderType(DunningReminderType dunningReminderType) {
        this.dunningReminderType = dunningReminderType;
    }
}
