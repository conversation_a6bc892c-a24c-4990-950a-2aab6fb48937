package com.subskribe.billy.template.db;

import static com.subskribe.billy.jooq.default_schema.Tables.DOCUMENT_TEMPLATE_ACCOUNT_ORDER;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.jooq.default_schema.tables.records.DocumentTemplateAccountOrderRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.template.mapper.DocumentTemplateAccountOrderRecordMapper;
import com.subskribe.billy.template.model.OrderTerms;
import com.subskribe.billy.template.model.OrderTermsLevelType;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class OrderTermsDAO {

    private static final String UNIQUE_TENANT_ID_ORDER_ID_TEMPLATE_GROUP_ID_CONSTRAINT_NAME =
        "index_document_template_account_order_tenant_id_order_id_template_group_id";
    private static final String TERM_EXISTS_WITH_ORDER_ID_TEMPLATE_ID = "another term exists with order id: %s and template id: %s";

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    private final DocumentTemplateAccountOrderRecordMapper recordMapper;

    @Inject
    public OrderTermsDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        recordMapper = Mappers.getMapper(DocumentTemplateAccountOrderRecordMapper.class);
    }

    public List<OrderTerms> getOrderTermsForOrder(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<DocumentTemplateAccountOrderRecord> records = dslContext
            .selectFrom(DOCUMENT_TEMPLATE_ACCOUNT_ORDER)
            .where(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED.isFalse())
            .fetchInto(DocumentTemplateAccountOrderRecord.class);

        return recordMapper.recordsToList(records);
    }

    // use getOrderTermsForOrder
    @Deprecated
    public List<String> getDocumentTemplateIdsForOrder(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var templateIds = dslContext
            .selectFrom(DOCUMENT_TEMPLATE_ACCOUNT_ORDER)
            .where(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED.isFalse())
            .fetchInto(DocumentTemplateAccountOrderRecord.class);

        return templateIds.stream().map(DocumentTemplateAccountOrderRecord::getTemplateGroupId).collect(Collectors.toList());
    }

    public boolean documentTemplateUsedByOrder(String templateId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(DOCUMENT_TEMPLATE_ACCOUNT_ORDER)
                .where(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TEMPLATE_GROUP_ID.eq(templateId))
                .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
                .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED.isFalse())
                .limit(1)
        );
    }

    public Set<String> getTemplatesInUse(List<String> templateIds) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<String> inUseTemplateGroupIds = dslContext
            .selectDistinct(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TEMPLATE_GROUP_ID)
            .from(DOCUMENT_TEMPLATE_ACCOUNT_ORDER)
            .where(
                DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TEMPLATE_GROUP_ID.in(templateIds)
                    .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
                    .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED.isFalse())
            )
            .fetchInto(String.class);

        return new HashSet<>(inUseTemplateGroupIds);
    }

    public void addOrderTermsForOrder(
        DSLContext dslContext,
        String tenantId,
        String orderId,
        String templateGroupId,
        int templateGroupVersion,
        OrderTermsLevelType levelType,
        Set<String> planIds
    ) {
        Optional<DocumentTemplateAccountOrderRecord> savedRecord = getExistingDocumentTemplateAccountOrderRecord(
            dslContext,
            tenantId,
            orderId,
            templateGroupId,
            templateGroupVersion
        );
        if (savedRecord.isPresent()) {
            undeleteExistingDocumentTemplateAccountOrderRecord(dslContext, tenantId, orderId, templateGroupId, templateGroupVersion);
            return;
        }

        var record = createDocumentTemplateAccountOrderRecord(tenantId, templateGroupId, templateGroupVersion, orderId, levelType, planIds);
        PostgresErrorHandler.withConstraintAsConflict(
            () -> dslContext.insertInto(DOCUMENT_TEMPLATE_ACCOUNT_ORDER).set(record).execute(),
            UNIQUE_TENANT_ID_ORDER_ID_TEMPLATE_GROUP_ID_CONSTRAINT_NAME,
            String.format(TERM_EXISTS_WITH_ORDER_ID_TEMPLATE_ID, orderId, templateGroupId)
        );
    }

    private Optional<DocumentTemplateAccountOrderRecord> getExistingDocumentTemplateAccountOrderRecord(
        DSLContext dslContext,
        String tenantId,
        String orderId,
        String templateId,
        int templateVersion
    ) {
        var record = dslContext
            .selectFrom(DOCUMENT_TEMPLATE_ACCOUNT_ORDER)
            .where(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TEMPLATE_GROUP_ID.eq(templateId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TEMPLATE_GROUP_VERSION.eq(templateVersion))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED.isTrue())
            .fetchOne();

        return Optional.ofNullable(record);
    }

    private void undeleteExistingDocumentTemplateAccountOrderRecord(
        DSLContext dslContext,
        String tenantId,
        String orderId,
        String templateId,
        int templateVersion
    ) {
        dslContext
            .update(DOCUMENT_TEMPLATE_ACCOUNT_ORDER)
            .set(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED, false)
            .where(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TEMPLATE_GROUP_ID.eq(templateId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TEMPLATE_GROUP_VERSION.eq(templateVersion))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED.isTrue())
            .execute();
    }

    private DocumentTemplateAccountOrderRecord createDocumentTemplateAccountOrderRecord(
        String tenantId,
        String templateGroupId,
        int templateGroupVersion,
        String orderId,
        OrderTermsLevelType levelType,
        Set<String> planIds
    ) {
        OrderTerms orderTerms = new OrderTerms(null, templateGroupId, templateGroupVersion, orderId, levelType, planIds);
        orderTerms.setTenantId(tenantId);
        return recordMapper.toRecord(orderTerms);
    }

    public void updateOrderTermsVersion(String orderId, String templateId, int newVersion) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        dslContext
            .update(DOCUMENT_TEMPLATE_ACCOUNT_ORDER)
            .set(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TEMPLATE_GROUP_VERSION, newVersion)
            .where(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TEMPLATE_GROUP_ID.eq(templateId))
            .execute();
    }

    public void deleteDocumentTemplateIdsForOrder(DSLContext dslContext, String tenantId, List<String> templateIds, String orderId) {
        dslContext
            .update(DOCUMENT_TEMPLATE_ACCOUNT_ORDER)
            .set(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED, true)
            .where(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TEMPLATE_GROUP_ID.in(templateIds))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    public void deleteAllOrderTermsForOrder(DSLContext dslContext, String tenantId, String orderId) {
        dslContext
            .update(DOCUMENT_TEMPLATE_ACCOUNT_ORDER)
            .set(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED, true)
            .where(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    public void deleteAllOrderTermsForOrder(DSLContext dslContext, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .update(DOCUMENT_TEMPLATE_ACCOUNT_ORDER)
            .set(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED, true)
            .where(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(DOCUMENT_TEMPLATE_ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
    }
}
