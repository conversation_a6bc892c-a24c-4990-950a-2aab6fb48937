package com.subskribe.billy.template.services;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.order.services.OrderDocumentService;
import com.subskribe.billy.order.services.OrderPdfGenerationTrackerService;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.template.db.DocumentTemplateUpdatedOnOrderDAO;
import com.subskribe.billy.template.model.CustomTemplateUpdateOnOrder;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.validation.Validator;
import java.util.Optional;
import javax.inject.Inject;
import javax.ws.rs.ForbiddenException;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

public class CustomTemplateUpdatedOnOrderService {

    private final DocumentTemplateUpdatedOnOrderDAO documentTemplateUpdatedOnOrderDAO;
    private final OrderPdfGenerationTrackerService orderPdfGenerationTrackerService;
    private final FeatureService featureService;
    private final CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final OrderDocumentService orderDocumentService;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;

    @Inject
    public CustomTemplateUpdatedOnOrderService(
        DocumentTemplateUpdatedOnOrderDAO documentTemplateUpdatedOnOrderDAO,
        OrderPdfGenerationTrackerService orderPdfGenerationTrackerService,
        FeatureService featureService,
        CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService,
        DocumentTemplateGetService documentTemplateGetService,
        OrderDocumentService orderDocumentService,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider
    ) {
        this.documentTemplateUpdatedOnOrderDAO = documentTemplateUpdatedOnOrderDAO;
        this.orderPdfGenerationTrackerService = orderPdfGenerationTrackerService;
        this.featureService = featureService;
        this.customTemplateUpdatedOnOrderGetService = customTemplateUpdatedOnOrderGetService;
        this.documentTemplateGetService = documentTemplateGetService;
        this.orderDocumentService = orderDocumentService;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
    }

    public CustomTemplateUpdateOnOrder upsert(CustomTemplateUpdateOnOrder customTemplateUpdateOnOrder) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return upsertInTransaction(customTemplateUpdateOnOrder, dslContext);
    }

    public CustomTemplateUpdateOnOrder upsertInTransaction(CustomTemplateUpdateOnOrder customTemplateUpdateOnOrder, DSLContext dslContext) {
        Validator.validateStringNotBlank(customTemplateUpdateOnOrder.getTemplateId(), "TemplateId cannot be null");
        Validator.validateStringNotBlank(customTemplateUpdateOnOrder.getOrderId(), "orderId cannot be null");
        Validator.validateStringNotBlank(customTemplateUpdateOnOrder.getName(), "Name cannot be empty");

        if (featureService.isEnabled(Feature.RESTRICT_PREDEFINED_TERM_UPDATE_ON_ORDER_TO_ADMIN)) {
            Optional<Role> optionalRole = CurrentUserProvider.provideRole();
            if (optionalRole.isEmpty() || optionalRole.get() != Role.ADMIN) {
                throw new ForbiddenException("Only admin users can update predefined terms");
            }
        }

        // todo: validate order status? Only DRAFT orders should allow term edits

        validateCustomTemplate(customTemplateUpdateOnOrder);

        Optional<CustomTemplateUpdateOnOrder> customTemplateOptional = documentTemplateUpdatedOnOrderDAO.getCustomTemplate(
            customTemplateUpdateOnOrder.getTemplateId(),
            customTemplateUpdateOnOrder.getOrderId()
        );

        orderPdfGenerationTrackerService.setShouldRegeneratePdfInTransaction(dslContext, customTemplateUpdateOnOrder.getOrderId(), true);

        CustomTemplateUpdateOnOrder savedTemplate;
        if (customTemplateOptional.isEmpty()) {
            savedTemplate = documentTemplateUpdatedOnOrderDAO.addCustomTemplate(customTemplateUpdateOnOrder, dslContext);
        } else {
            customTemplateUpdateOnOrder.setId(customTemplateOptional.get().getId());
            savedTemplate = documentTemplateUpdatedOnOrderDAO.updateCustomTemplate(customTemplateUpdateOnOrder, dslContext);
        }

        return savedTemplate;
    }

    private void validateCustomTemplate(CustomTemplateUpdateOnOrder customTemplateUpdateOnOrder) {
        Validator.validateNonNullArgument(customTemplateUpdateOnOrder, "customTemplateUpdateOnOrder");

        // Precompile the template to check for errors
        DocumentTemplate documentTemplate = documentTemplateGetService.getDocumentTemplateByTemplateId(customTemplateUpdateOnOrder.getTemplateId());
        documentTemplate.setName(customTemplateUpdateOnOrder.getName());
        documentTemplate.setDescription(customTemplateUpdateOnOrder.getDescription());
        documentTemplate.setContent(customTemplateUpdateOnOrder.getContent());
        orderDocumentService.previewDocumentTemplate(documentTemplate);
    }

    public CustomTemplateUpdateOnOrder deleteCustomUpdatedTemplate(String templateId, String orderId) {
        Validator.validateStringNotBlank(templateId, "templateId cannot be blank");
        Validator.validateStringNotBlank(orderId, "orderId cannot be blank");
        var customTemplate = customTemplateUpdatedOnOrderGetService.getCustomUpdatedTemplate(templateId, orderId);

        // todo: create a transaction
        documentTemplateUpdatedOnOrderDAO.deleteCustomTemplateOnOrder(templateId, orderId);
        orderPdfGenerationTrackerService.setShouldRegeneratePdf(orderId, true);
        return customTemplate;
    }

    public void deleteUpdatedTemplatesOnOrder(Configuration configuration, String orderId) {
        Validator.validateStringNotBlank(orderId, "orderId cannot be blank");

        DSLContext dslContext = DSL.using(configuration);
        documentTemplateUpdatedOnOrderDAO.deleteCustomTemplatesOnOrder(dslContext, orderId);
        orderPdfGenerationTrackerService.setShouldRegeneratePdf(orderId, true);
    }
}
