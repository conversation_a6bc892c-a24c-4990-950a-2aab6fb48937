package com.subskribe.billy.template.services;

import com.subskribe.billy.template.db.OrderTermsDAO;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.model.OrderTerms;
import com.subskribe.billy.template.model.OrderTermsLevelType;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;

// TODO: Consolidate 'document_template_account_order' into an SQL ARRAY field in 'account_order'?
public class OrderTermsService {

    private final OrderTermsDAO orderTermsDAO;

    @Inject
    public OrderTermsService(OrderTermsDAO orderTermsDAO) {
        this.orderTermsDAO = orderTermsDAO;
    }

    public List<OrderTerms> getOrderTermsByOrderId(String orderId) {
        // Accounting for orderId == null
        if (StringUtils.isBlank(orderId)) {
            return List.of();
        }

        return orderTermsDAO.getOrderTermsForOrder(orderId);
    }

    // use getOrderTermsByOrderId
    @Deprecated
    public List<String> getDocumentTemplateIdsByOrderId(String orderId) {
        // Accounting for orderId == null
        if (StringUtils.isBlank(orderId)) {
            return List.of();
        }

        return orderTermsDAO.getDocumentTemplateIdsForOrder(orderId);
    }

    public boolean documentTemplateUsedByOrder(String templateId) {
        return orderTermsDAO.documentTemplateUsedByOrder(templateId);
    }

    public Set<String> getTemplatesInUse(List<String> templateIds) {
        return orderTermsDAO.getTemplatesInUse(templateIds);
    }

    public void addOrderTermsForOrder(
        DSLContext dslContext,
        String tenantId,
        List<DocumentTemplate> templates,
        String orderId,
        Map<String, Set<String>> templateIdToPlanIdsMap
    ) {
        // Accounting for orderId == null
        if (StringUtils.isBlank(orderId) || CollectionUtils.isEmpty(templates)) {
            return;
        }

        templates.forEach(template -> {
            OrderTermsLevelType levelType = templateIdToPlanIdsMap.containsKey(template.getTemplateId())
                ? OrderTermsLevelType.PLAN
                : OrderTermsLevelType.ORDER;
            orderTermsDAO.addOrderTermsForOrder(
                dslContext,
                tenantId,
                orderId,
                template.getTemplateId(),
                template.getVersion(),
                levelType,
                templateIdToPlanIdsMap.getOrDefault(template.getTemplateId(), Set.of())
            );
        });
    }

    public void updateOrderTermsForOrder(
        DSLContext dslContext,
        String tenantId,
        List<DocumentTemplate> selectedTemplates,
        String orderId,
        Map<String, Set<String>> templateIdToPlanIdsMap
    ) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }

        List<String> oldTemplateIds = orderTermsDAO.getOrderTermsForOrder(orderId).stream().map(OrderTerms::getTemplateGroupId).toList();
        List<String> selectedTemplateIds = selectedTemplates.stream().map(DocumentTemplate::getTemplateId).toList();
        List<String> unselectedTemplateIds = oldTemplateIds
            .stream()
            .filter(templateId -> !selectedTemplateIds.contains(templateId))
            .collect(Collectors.toList());
        orderTermsDAO.deleteDocumentTemplateIdsForOrder(dslContext, tenantId, unselectedTemplateIds, orderId);

        List<DocumentTemplate> newTemplates = selectedTemplates
            .stream()
            .filter(template -> !oldTemplateIds.contains(template.getTemplateId()))
            .toList();
        newTemplates.forEach(template -> {
            OrderTermsLevelType levelType = templateIdToPlanIdsMap.containsKey(template.getTemplateId())
                ? OrderTermsLevelType.PLAN
                : OrderTermsLevelType.ORDER;
            orderTermsDAO.addOrderTermsForOrder(
                dslContext,
                tenantId,
                orderId,
                template.getTemplateId(),
                template.getVersion(),
                levelType,
                templateIdToPlanIdsMap.getOrDefault(template.getTemplateId(), Set.of())
            );
        });
    }

    public void updateOrderTermsVersion(String orderId, String templateId, int newVersion) {
        Validator.validateStringNotBlank(orderId, "orderId");
        Validator.validateStringNotBlank(templateId, "templateId");
        orderTermsDAO.updateOrderTermsVersion(orderId, templateId, newVersion);
    }

    public void deleteAllOrderTermsForOrder(DSLContext dslContext, String tenantId, String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }

        orderTermsDAO.deleteAllOrderTermsForOrder(dslContext, tenantId, orderId);
    }

    public void deleteAllOrderTermsForOrder(DSLContext dslContext, String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }

        orderTermsDAO.deleteAllOrderTermsForOrder(dslContext, orderId);
    }
}
