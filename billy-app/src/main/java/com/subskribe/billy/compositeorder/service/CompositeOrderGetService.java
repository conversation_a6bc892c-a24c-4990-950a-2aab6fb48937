package com.subskribe.billy.compositeorder.service;

import static com.subskribe.billy.compositeorder.CompositeOrderProductMetadata.COMPOSITE_ORDER_CORE;
import static com.subskribe.billy.compositeorder.CompositeOrderProductMetadata.NO_ORDERS_IN_COMPOSITE_ORDER;

import com.subskribe.billy.compositeorder.db.CompositeOrderDAO;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.model.ImmutableCompositeOrder;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.logger.ErrorContext;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Optional;
import javax.annotation.CheckReturnValue;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class CompositeOrderGetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CompositeOrderGetService.class);

    private final FeatureService featureService;

    private final CompositeOrderDAO compositeOrderDAO;

    private final OrderGetService orderGetService;

    private final OpportunityGetService opportunityGetService;

    @Inject
    public CompositeOrderGetService(
        FeatureService featureService,
        CompositeOrderDAO compositeOrderDAO,
        OrderGetService orderGetService,
        OpportunityGetService opportunityGetService
    ) {
        this.featureService = featureService;
        this.compositeOrderDAO = compositeOrderDAO;
        this.orderGetService = orderGetService;
        this.opportunityGetService = opportunityGetService;
    }

    private void checkFeature() {
        if (
            !(featureService.isEnabled(Feature.UPSELL_EARLY_RENEWAL) || featureService.isEnabled(Feature.CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE))
        ) {
            throw new UnsupportedOperationException("Composite order feature (Upsell early renewal, Cancel Restructure) not enabled");
        }
    }

    public boolean compositeOrderExists(String compositeOrderId) {
        Validator.validateStringNotBlank(compositeOrderId, "composite order id is required.");
        return compositeOrderDAO.compositeOrderExists(compositeOrderId);
    }

    public OrderStatus getCompositeOrderStatus(String compositeOrderId) {
        Validator.validateStringNotBlank(compositeOrderId, "composite order id is required.");
        OrderStatus status = compositeOrderDAO.getCompositeOrderStatus(compositeOrderId);
        if (status == null) {
            throw new ObjectNotFoundException(BillyObjectType.COMPOSITE_ORDER, compositeOrderId);
        }

        return status;
    }

    public CompositeOrder getCompositeOrder(String compositeOrderId) {
        Validator.validateStringNotBlank(compositeOrderId, "composite order id must be provided");
        checkFeature();
        CompositeOrder compositeOrder = compositeOrderDAO.getCompositeOrder(compositeOrderId);
        if (StringUtils.isBlank(compositeOrder.getCrmOpportunityId())) {
            return compositeOrder;
        }
        Optional<Opportunity> opportunityOptional = opportunityGetService.getOptionalOpportunityByCrmOpportunityId(
            compositeOrder.getCrmOpportunityId()
        );
        if (opportunityOptional.isPresent()) {
            Opportunity opportunity = opportunityOptional.get();
            compositeOrder = addOpportunityToCompositeOrder(compositeOrder, opportunity);
        }
        return compositeOrder;
    }

    public Optional<CompositeOrder> getCompositeOrderOptional(String compositeOrderId) {
        try {
            return Optional.of(getCompositeOrder(compositeOrderId));
        } catch (ObjectNotFoundException e) {
            return Optional.empty();
        }
    }

    public List<String> getOrderIdsInCompositeOrder(String compositeOrderId) {
        Validator.validateStringNotBlank(compositeOrderId, "compositeOrderId must be provided");
        checkFeature();

        // check that composite order exists
        getCompositeOrder(compositeOrderId);

        return orderGetService.getOrderIdsInCompositeOrder(compositeOrderId);
    }

    public List<Order> getOrdersInCompositeOrder(String compositeOrderId) {
        List<String> orderIds = getOrderIdsInCompositeOrder(compositeOrderId);
        if (CollectionUtils.isEmpty(orderIds)) {
            String message = String.format("no orders in composite order %s", compositeOrderId);
            LOGGER.error(new ErrorContext(COMPOSITE_ORDER_CORE, NO_ORDERS_IN_COMPOSITE_ORDER), message);
            throw new IllegalStateException(message);
        }
        return orderIds.stream().map(orderGetService::getOrderByOrderId).toList();
    }

    public List<CompositeOrder> getCompositeOrdersByCrmOpportunityId(String crmOpportunityId) {
        Validator.validateStringNotBlank(crmOpportunityId, "crmOpportunityId must be provided");
        List<CompositeOrder> compositeOrders = compositeOrderDAO.getCompositeOrdersByCrmOpportunityId(crmOpportunityId);
        if (CollectionUtils.isEmpty(compositeOrders)) {
            return compositeOrders;
        }
        Opportunity opportunity = opportunityGetService.getOpportunityByCrmOpportunityId(crmOpportunityId);
        return compositeOrders.stream().map(co -> addOpportunityToCompositeOrder(co, opportunity)).toList();
    }

    @CheckReturnValue
    public CompositeOrder addOpportunityToCompositeOrder(CompositeOrder compositeOrder, Opportunity opportunity) {
        Validator.validateNonNullArgument(compositeOrder, "compositeOrder must be provided");
        boolean isPrimaryCompositeOrderForOpportunity =
            opportunity.getPrimaryOrderId() != null && opportunity.getPrimaryOrderId().equals(compositeOrder.getCompositeOrderId());
        return ImmutableCompositeOrder.builder()
            .from(compositeOrder)
            .isPrimaryCompositeOrderForCrmOpportunity(isPrimaryCompositeOrderForOpportunity)
            .build();
    }
}
