package com.subskribe.billy.compositeorder.model;

import static com.subskribe.billy.configuration.dynamic.Feature.SEPARATE_REMOVED_LINES_FROM_RESTRUCTURED_LINES;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.approvalflowhierarchy.model.ApprovalSegment;
import com.subskribe.billy.approvalflowhierarchy.service.ApprovalFlowHierarchyService;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.graphql.order.OrderDataAggregator;
import com.subskribe.billy.graphql.order.OrderDetailMinimal;
import com.subskribe.billy.graphql.order.OrderDetailMinimalMapper;
import com.subskribe.billy.graphql.order.OrderDetailsMapper;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.productcatalog.ChargeDataAggregator;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountDetail;
import com.subskribe.billy.graphql.subscription.SubscriptionDataAggregator;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.graphql.template.DocumentTemplateMapper;
import com.subskribe.billy.graphql.template.DocumentTemplateResponse;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.LineItemMetrics;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderServiceHelper;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.account.AccountMapper;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalFlowHierarchyMapper;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalSegmentJson;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.order.OrderLineItemJson;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.resources.json.tenant.UserMapper;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.CompositeOrderType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.user.service.UserService;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class CancelAndRestructureOrderDataAggregator {

    private final AccountGetService accountGetService;
    private final UserService userService;
    private final OrderMapper orderMapper;
    private final OrderDetailsMapper orderDetailMapper;
    private final AccountMapper accountMapper;
    private final UserMapper userMapper;
    private final PlanMapper planMapper;
    private final OrderDetailMinimalMapper orderDetailMinimalMapper;
    private final SubscriptionDataAggregator subscriptionDataAggregator;
    private final ChargeDataAggregator chargeDataAggregator;
    private final ProductCatalogGetService productCatalogGetService;
    private final CompositeOrderGetService compositeOrderGetService;
    private final MetricsService metricsService;
    private final FeatureService featureService;
    private final OrderDataAggregator orderDataAggregator;
    private final DocumentTemplateMapper documentTemplateMapper;
    private final CustomFieldService customFieldService;
    private final CustomFieldAPIMapper customFieldAPIMapper;
    private final ApprovalFlowHierarchyService approvalFlowHierarchyService;
    private final ApprovalFlowHierarchyMapper approvalFlowHierarchyMapper;

    @Inject
    public CancelAndRestructureOrderDataAggregator(
        AccountGetService accountGetService,
        UserService userService,
        SubscriptionDataAggregator subscriptionDataAggregator,
        ChargeDataAggregator chargeDataAggregator,
        ProductCatalogGetService productCatalogGetService,
        CompositeOrderGetService compositeOrderGetService,
        MetricsService metricsService,
        FeatureService featureService,
        OrderDataAggregator orderDataAggregator,
        CustomFieldService customFieldService,
        ApprovalFlowHierarchyService approvalFlowHierarchyService
    ) {
        this.accountGetService = accountGetService;
        this.userService = userService;
        this.subscriptionDataAggregator = subscriptionDataAggregator;
        this.chargeDataAggregator = chargeDataAggregator;
        this.productCatalogGetService = productCatalogGetService;
        this.compositeOrderGetService = compositeOrderGetService;
        this.metricsService = metricsService;
        this.featureService = featureService;
        this.orderDataAggregator = orderDataAggregator;
        this.customFieldService = customFieldService;
        this.approvalFlowHierarchyService = approvalFlowHierarchyService;
        approvalFlowHierarchyMapper = Mappers.getMapper(ApprovalFlowHierarchyMapper.class);
        orderDetailMapper = Mappers.getMapper(OrderDetailsMapper.class);
        accountMapper = Mappers.getMapper(AccountMapper.class);
        userMapper = Mappers.getMapper(UserMapper.class);
        orderMapper = Mappers.getMapper(OrderMapper.class);
        planMapper = Mappers.getMapper(PlanMapper.class);
        orderDetailMinimalMapper = Mappers.getMapper(OrderDetailMinimalMapper.class);
        documentTemplateMapper = Mappers.getMapper(DocumentTemplateMapper.class);
        customFieldAPIMapper = Mappers.getMapper(CustomFieldAPIMapper.class);
    }

    @SuppressFBWarnings({ "NP_NULL_ON_SOME_PATH", "NP_NULL_PARAM_DEREF" })
    public CancelAndRestructureOrderDetail getCancelAndRestructureOrderDetail(Order restructureOrder) {
        OrderJson restructureOrderJson = orderMapper.orderToJson(restructureOrder);

        List<OrderLineItemDetail> lineItemDetails = getOrderLineItemDetails(restructureOrderJson.getLineItems());

        String restructureOrderId = restructureOrder.getOrderId();

        //Order Owner detail
        UserJson owner = null;
        if (StringUtils.isNotBlank(restructureOrderJson.getOwnerId())) {
            owner = userMapper.userToJson(userService.getUser(restructureOrderJson.getOwnerId()));
        }

        ApprovalSegmentJson approvalSegmentJson = null;
        if (StringUtils.isNotBlank(restructureOrderJson.getApprovalSegmentId())) {
            ApprovalSegment approvalSegment = approvalFlowHierarchyService.getApprovalSegmentByApprovalSegmentId(
                restructureOrderJson.getApprovalSegmentId(),
                true
            );
            approvalSegmentJson = approvalFlowHierarchyMapper.approvalSegmentToJson(approvalSegment);
        }

        // Get account detail
        String accountId = restructureOrderJson.getAccountId();
        Account account = accountGetService.getAccount(accountId);
        AccountJson accountJson = accountMapper.accountToJson(account);

        //Get billing and shipping contact detail
        AccountContactJson billingContactJson = Optional.ofNullable(restructureOrderJson.getBillingContactId())
            .map(accountGetService::getContact)
            .map(accountMapper::accountContactToJson)
            .orElse(null);
        AccountContactJson shippingContactJson = Optional.ofNullable(restructureOrderJson.getShippingContactId())
            .map(accountGetService::getContact)
            .map(accountMapper::accountContactToJson)
            .orElse(null);

        //Get Reseller account detail
        AccountJson accountJsonReseller = null;
        if (billingContactJson != null && !accountId.equalsIgnoreCase(billingContactJson.getAccountId())) {
            Account resellerAccount = accountGetService.getAccount(billingContactJson.getAccountId());
            accountJsonReseller = accountMapper.accountToJson(resellerAccount);
        }

        RecurrenceJson termLength = Optional.ofNullable(restructureOrderJson.getTermLength())
            .map(recurrence -> new RecurrenceJson(recurrence.getCycle(), recurrence.getStep()))
            .orElse(null);

        RecurrenceJson billingCycle = Optional.of(restructureOrderJson.getBillingCycle())
            .map(recurrence -> new RecurrenceJson(recurrence.getCycle(), recurrence.getStep()))
            .get();

        Long billingAnchorDate = restructureOrderJson.getBillingAnchorDate();

        BillingTerm billingTerm = restructureOrderJson.getBillingTerm();

        //Other details
        String purchaseOrderNumber = restructureOrderJson.getPurchaseOrderNumber();
        boolean isPurchaseOrderNumberRequired = BooleanUtils.isTrue(restructureOrderJson.getPurchaseOrderRequiredForInvoicing());
        boolean autoRenew = BooleanUtils.isTrue(restructureOrderJson.isAutoRenew());
        String paymentTerm = restructureOrderJson.getPaymentTerm();

        // todo: weird logic here, could be simplified
        List<Long> rampInterval = null;
        if (restructureOrder.getRampInterval() != null) {
            rampInterval = new ArrayList<>(restructureOrderJson.getRampInterval());
        }

        //PredefinedDiscountDetail
        List<PredefinedDiscountDetail> predefinedDiscounts = orderDetailMapper.toOrderDiscountDetailList(restructureOrder.getPredefinedDiscounts());
        populateDiscountAtOrderLineItemLevel(predefinedDiscounts, lineItemDetails);

        //TODO : Subscription detail is yet to be discussed
        String subscriptionId = Optional.ofNullable(restructureOrder.getRestructureForSubscriptionId()).orElse(
            restructureOrder.getExternalSubscriptionId()
        );
        SubscriptionDetail currentSubscription = StringUtils.isNotBlank(subscriptionId)
            ? subscriptionDataAggregator.getSubscriptionDetail(subscriptionId)
            : null;

        List<DocumentTemplate> documentTemplates = orderDataAggregator.getDocumentTemplates(restructureOrder);
        List<DocumentTemplateResponse> orderFormTemplates = documentTemplateMapper.documentTemplatesToResponse(documentTemplates);

        OrderServiceHelper.hydrateAndValidateCustomFields(restructureOrder, true, customFieldService);

        Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, restructureOrder.getLineItems());
        restructureOrder.setCustomBillingEligibleOrderLineIds(orderDataAggregator.getCustomBillingEligibleOrderLines(restructureOrder, chargeMap));

        return ImmutableCancelAndRestructureOrderDetail.builder()
            .id(restructureOrderId)
            .entityId(restructureOrder.getEntityId())
            .type(CompositeOrderType.CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE)
            .status(OrderStatus.DRAFT)
            .lineItems(lineItemDetails)
            .name(restructureOrder.getName())
            .startDate(Optional.ofNullable(restructureOrder.getStartDate()).map(Instant::getEpochSecond).orElse(Instant.now().getEpochSecond()))
            .endDate(Optional.ofNullable(restructureOrder.getEndDate()).map(Instant::getEpochSecond).orElse(null))
            .predefinedDiscounts(predefinedDiscounts)
            .owner(owner)
            .account(accountJson)
            .resoldBy(accountJsonReseller)
            .billingContact(billingContactJson)
            .shippingContact(shippingContactJson)
            .termLength(termLength)
            .billingCycle(billingCycle)
            .billingAnchorDate(billingAnchorDate)
            .billingTerm(billingTerm)
            .purchaseOrderNumber(purchaseOrderNumber)
            .purchaseOrderRequiredForInvoicing(isPurchaseOrderNumberRequired)
            .autoRenew(autoRenew)
            .paymentTerm(paymentTerm)
            .rampInterval(rampInterval)
            .subscriptionId(subscriptionId)
            .currentSubscription(currentSubscription)
            .restructureForSubscriptionId(subscriptionId)
            .orderFormTemplates(orderFormTemplates)
            .documentMasterTemplateId(restructureOrderJson.getDocumentMasterTemplateId())
            .documentCustomContent(restructureOrder.getDocumentCustomContent())
            .customFields(Optional.ofNullable(restructureOrder.getCustomFields()).map(customFieldAPIMapper::toCustomFieldEntries).orElse(null))
            .currency(restructureOrder.getCurrency().getCurrencyCode())
            .approvalSegment(approvalSegmentJson)
            .expiresOn(Optional.ofNullable(restructureOrder.getExpiresOn()).map(Instant::getEpochSecond).orElse(null))
            .subscriptionDurationModel(restructureOrder.getSubscriptionDurationModel())
            .build();
    }

    private void populateDiscountAtOrderLineItemLevel(List<PredefinedDiscountDetail> predefinedDiscounts, List<OrderLineItemDetail> lineItemDetails) {
        if (Objects.isNull(predefinedDiscounts)) {
            return;
        }
        Map<String, PredefinedDiscountDetail> orderDiscountDetailMap = predefinedDiscounts
            .stream()
            .collect(Collectors.toMap(PredefinedDiscountDetail::getId, Function.identity()));
        lineItemDetails.forEach(orderLineItemDetail ->
            orderLineItemDetail
                .getPredefinedDiscounts()
                .forEach(discountLineItem -> {
                    PredefinedDiscountDetail predefinedDiscountDetail = orderDiscountDetailMap.get(discountLineItem.getId());
                    if (predefinedDiscountDetail == null) {
                        return;
                    }
                    discountLineItem.setName(predefinedDiscountDetail.getName());
                    discountLineItem.setType(predefinedDiscountDetail.getType());
                    discountLineItem.setDescription(predefinedDiscountDetail.getDescription());
                    discountLineItem.setStatus(predefinedDiscountDetail.getStatus());
                })
        );
    }

    private List<OrderLineItemDetail> getOrderLineItemDetails(List<OrderLineItemJson> lineItems) {
        List<OrderLineItemDetail> orderLineItemDetails = new ArrayList<>();
        lineItems.forEach(lineItem -> {
            OrderLineItemDetail orderLineItemDetail = orderDetailMapper.orderLineItemJsonToDetail(lineItem);
            Charge charge = productCatalogGetService.getChargeByChargeId(lineItem.getChargeId());
            ChargeJson chargeJson = planMapper.chargeToJson(charge);
            ChargeDetail chargeDetail = chargeDataAggregator.getChargeDetail(charge);
            orderLineItemDetail.setCharge(chargeJson);
            orderLineItemDetail.setChargeDetail(chargeDetail);
            orderLineItemDetail.setPlan(planMapper.planToJson(productCatalogGetService.getPlan(lineItem.getPlanId())));
            orderLineItemDetails.add(orderLineItemDetail);
        });
        return orderLineItemDetails;
    }

    public CancelAndRestructureOrderDetail getCancelAndRestructureOrderDetail(List<Order> orders) {
        Order restructuredOrder = null;
        Order cancelledOrder = null;
        for (Order order : orders) {
            if (order.getOrderType() == OrderType.RESTRUCTURE) restructuredOrder = order;
            else if (order.getOrderType() == OrderType.CANCEL) cancelledOrder = order;
        }
        if (restructuredOrder == null || cancelledOrder == null) {
            throw new InvariantCheckFailedException("Cancel and Restructure must have one Restructure Order Type and one or more Cancel Order Type");
        }

        CancelAndRestructureOrderDetail cancelAndRestructureOrderDetail = getCancelAndRestructureOrderDetail(restructuredOrder);
        //Get status of composite order
        String compositeOrderId = restructuredOrder.getCompositeOrderId();
        CompositeOrder compositeOrder = StringUtils.isNotBlank(compositeOrderId)
            ? compositeOrderGetService.getCompositeOrder(compositeOrderId)
            : null;
        String crmOppId = compositeOrder != null ? compositeOrder.getCrmOpportunityId() : null;
        OrderStatus orderStatus = compositeOrder != null ? compositeOrder.getStatus() : OrderStatus.DRAFT;

        // Fill metrics for the restructured order
        BigDecimal arr = BigDecimal.ZERO;
        BigDecimal tcv = BigDecimal.ZERO;
        BigDecimal deltaArr = BigDecimal.ZERO;
        BigDecimal recurringTotal = BigDecimal.ZERO;
        BigDecimal nonRecurringTotal = BigDecimal.ZERO;
        for (Order order : orders) {
            Metrics orderMetrics = metricsService.getOrderMetrics(order, Instant.now());
            arr = arr.add(orderMetrics.getArr());
            tcv = tcv.add(Optional.ofNullable(orderMetrics.getTcv()).orElse(BigDecimal.ZERO));
            deltaArr = deltaArr.add(orderMetrics.getDeltaArr());
            recurringTotal = recurringTotal.add(Optional.ofNullable(orderMetrics.getRecurringTotal()).orElse(BigDecimal.ZERO));
            nonRecurringTotal = nonRecurringTotal.add(orderMetrics.getNonRecurringTotal());
        }

        // Get order detail minimal to show on Cancel and Restructure Order Page
        List<OrderDetailMinimal> orderDetailMinimals = orderDetailMinimalMapper.ordersToOrderDetailMinimals(orders);

        //if the line item is added(ActionType.ADD), then set BaseExternalSubscriptionChargeId to null this line should be treated as new line item.
        //Not setting BaseExternalSubscriptionChargeId to null will cause the line item to be treated as changed line item - even in order/order line mappers
        for (OrderLineItem li : restructuredOrder.getLineItems()) {
            if (li.getAction() == ActionType.ADD) {
                li.setBaseExternalSubscriptionChargeId(null);
            }
        }

        Set<String> baseExternalSubscriptionChargeIds = restructuredOrder
            .getLineItems()
            .stream()
            .map(OrderLineItem::getBaseExternalSubscriptionChargeId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        List<OrderLineItem> allLineItemsToAppearInRestructuredOrder = new ArrayList<>(restructuredOrder.getLineItems());

        //Line items completely removed while restructuring the subscription
        List<OrderLineItem> lineItemsRemovedDuringRestructure = cancelledOrder
            .getLineItems()
            .stream()
            .filter(orderLineItem -> !baseExternalSubscriptionChargeIds.contains(orderLineItem.getBaseExternalSubscriptionChargeId()))
            .toList();

        //Line items which underwent change while restructuring the subscription
        List<OrderLineItem> lineItemsChangedDuringRestructure = cancelledOrder
            .getLineItems()
            .stream()
            .filter(orderLineItem -> baseExternalSubscriptionChargeIds.contains(orderLineItem.getBaseExternalSubscriptionChargeId()))
            .toList();

        // UI should show the delta of the line item level totals for the lines which are changed during restructure
        Map<String, BigDecimal> orderLineToRestructureAmountMap = recalculateRestructuredOrderLineItemTotals(
            allLineItemsToAppearInRestructuredOrder,
            lineItemsChangedDuringRestructure
        );

        //Some plans have been removed while restructuring the subscription, add them to {allLineItemsToAppearInRestructuredOrder} if
        //feature flag separateRemovedLinedFromRestructuredLines is disabled
        List<OrderLineItemDetail> lineItemDetailsRemovedDuringRestructure;
        if (featureService.isEnabled(SEPARATE_REMOVED_LINES_FROM_RESTRUCTURED_LINES)) {
            lineItemDetailsRemovedDuringRestructure = getOrderLineItemDetails(
                orderDetailMapper.orderLineItemsToJsons(lineItemsRemovedDuringRestructure)
            );
        } else {
            allLineItemsToAppearInRestructuredOrder.addAll(lineItemsRemovedDuringRestructure);
            lineItemDetailsRemovedDuringRestructure = Collections.emptyList();
        }

        restructuredOrder.setLineItems(allLineItemsToAppearInRestructuredOrder);
        List<OrderLineItemDetail> allLineItemDetails = getOrderLineItemDetails(orderMapper.orderToJson(restructuredOrder).getLineItems());
        allLineItemDetails.forEach(orderLineItemDetail -> {
            String orderLineId = orderLineItemDetail.getId();
            // For newly added lines we just put restructure amount = order line amount
            BigDecimal restructureAmount = orderLineToRestructureAmountMap.getOrDefault(orderLineId, orderLineItemDetail.getAmount());
            orderLineItemDetail.setRestructureAmount(restructureAmount);
        });

        if (featureService.isEnabled(Feature.ORDER_LINE_ARR_OVERRIDE)) {
            Map<String, LineItemMetrics> metricsMap = metricsService.getLineItemMetricsForAllOrderLines(restructuredOrder);
            allLineItemDetails.forEach(lineItem -> lineItem.setMetrics(metricsMap.getOrDefault(lineItem.getId(), null)));
        }

        //PredefinedDiscountDetail
        List<PredefinedDiscountDetail> predefinedDiscounts = orderDetailMapper.toOrderDiscountDetailList(restructuredOrder.getPredefinedDiscounts());
        populateDiscountAtOrderLineItemLevel(predefinedDiscounts, allLineItemDetails);

        return ImmutableCancelAndRestructureOrderDetail.builder()
            .from(cancelAndRestructureOrderDetail)
            .id(compositeOrderId)
            .orders(orderDetailMinimals)
            .compositeOrderId(compositeOrderId)
            .crmOpportunityId(crmOppId)
            .status(orderStatus)
            .lineItems(allLineItemDetails)
            .removedLineItems(lineItemDetailsRemovedDuringRestructure)
            .arr(arr)
            .deltaArr(deltaArr)
            .tcv(tcv)
            .recurringTotal(recurringTotal)
            .nonRecurringTotal(nonRecurringTotal)
            .build();
    }

    public Map<String, BigDecimal> recalculateRestructuredOrderLineItemTotals(
        List<OrderLineItem> allLineItemsToAppearInRestructuredOrder,
        List<OrderLineItem> lineItemsChangedDuringRestructure
    ) {
        if (CollectionUtils.isEmpty(lineItemsChangedDuringRestructure)) {
            return Collections.emptyMap();
        }
        Map<String, BigDecimal> orderLineToRestructureAmount = new HashMap<>();
        Map<String, List<OrderLineItem>> existingChargeIdMap = lineItemsChangedDuringRestructure
            .stream()
            .filter(li -> StringUtils.isNotBlank(li.getBaseExternalSubscriptionChargeId()))
            .collect(Collectors.groupingBy(OrderLineItem::getBaseExternalSubscriptionChargeId));

        for (OrderLineItem orderLineItemToAppearInRestructure : allLineItemsToAppearInRestructuredOrder) {
            //newly added plan will not have baseExternalSubscriptionChargeId
            if (StringUtils.isBlank(orderLineItemToAppearInRestructure.getBaseExternalSubscriptionChargeId())) {
                continue;
            }
            List<OrderLineItem> orderLineItems = existingChargeIdMap.get(orderLineItemToAppearInRestructure.getBaseExternalSubscriptionChargeId());
            if (CollectionUtils.isEmpty(orderLineItems)) {
                continue;
            }
            BigDecimal subTotal = Objects.nonNull(orderLineItemToAppearInRestructure.getAmount())
                ? orderLineItemToAppearInRestructure.getAmount()
                : BigDecimal.ZERO;
            BigDecimal subDiscount = Objects.nonNull(orderLineItemToAppearInRestructure.getDiscountAmount())
                ? orderLineItemToAppearInRestructure.getDiscountAmount()
                : BigDecimal.ZERO;

            // We have to set this separately because of existing design, where the amounts being set is an aggregation of cancellation period + restructure period.
            orderLineToRestructureAmount.put(orderLineItemToAppearInRestructure.getOrderLineId(), subTotal);

            // NOTE : We are adding up the restructure amount + cancellation amount for essentially the same logical order line here to aggregate the amount to be shown at composite order level
            for (OrderLineItem ol : orderLineItems) {
                subTotal = Objects.nonNull(ol.getAmount()) ? subTotal.add(ol.getAmount()) : subTotal;
                subDiscount = Objects.nonNull(ol.getDiscountAmount()) ? subDiscount.add(ol.getDiscountAmount()) : subDiscount;
            }
            orderLineItemToAppearInRestructure.setAmount(subTotal);
            orderLineItemToAppearInRestructure.setDiscountAmount(subDiscount);
        }
        return orderLineToRestructureAmount;
    }

    /**
     * This method is used to get the distinct line items for the restructured order.
     * When a subscription is restructured, some line items are completely removed, some are newly added, and some are restructured.
     */
    public List<OrderLineItem> getDistinctOrderLinesForCancelAndRestructure(Order restructureOrder, List<Order> cancelOrders) {
        //get all the base external subscription charge ids from the restructure order
        List<String> baseExternalSubscriptionChargeIds = restructureOrder
            .getLineItems()
            .stream()
            .map(OrderLineItem::getBaseExternalSubscriptionChargeId)
            .toList();

        //get all the line items that were removed during the restructure
        List<OrderLineItem> lineItemsRemovedDuringRestructure = cancelOrders
            .stream()
            .flatMap(order -> order.getLineItems().stream())
            .filter(lineItem -> !baseExternalSubscriptionChargeIds.contains(lineItem.getBaseExternalSubscriptionChargeId()))
            .toList();

        //get all the line items that were restructured
        List<OrderLineItem> lineItemsChangedDuringRestructure = cancelOrders
            .stream()
            .flatMap(order -> order.getLineItems().stream())
            .filter(orderLineItem -> baseExternalSubscriptionChargeIds.contains(orderLineItem.getBaseExternalSubscriptionChargeId()))
            .toList();

        //get all the line items that were added/restructured during the cancel and restructure process
        List<OrderLineItem> allLineItemsToAppearInRestructuredOrder = new ArrayList<>(restructureOrder.getLineItems());

        //delta of line item level totals for the lines which were restructured should get synced to crm
        recalculateRestructuredOrderLineItemTotals(allLineItemsToAppearInRestructuredOrder, lineItemsChangedDuringRestructure);

        //combine all lines that were added and removed during the restructure
        if (CollectionUtils.isNotEmpty(lineItemsRemovedDuringRestructure)) {
            allLineItemsToAppearInRestructuredOrder.addAll(lineItemsRemovedDuringRestructure);
        }

        return allLineItemsToAppearInRestructuredOrder;
    }

    public Metrics getCancelAndRestructureOrderLineItemMetrics(OrderLineItem orderLineItem, Order restructureOrder, List<Order> cancelOrders) {
        Metrics orderLineMetrics = metricsService.getOrderLineMetrics(orderLineItem.getOrderLineId());

        if (CollectionUtils.isEmpty(cancelOrders)) {
            return orderLineMetrics;
        }

        // TODO: Refactor duplicate code and make changes to HubSpot if needed
        if (StringUtils.isBlank(orderLineItem.getBaseExternalSubscriptionChargeId())) {
            return orderLineMetrics;
        }

        List<String> baseExternalSubscriptionChargeIds = restructureOrder
            .getLineItems()
            .stream()
            .map(OrderLineItem::getBaseExternalSubscriptionChargeId)
            .toList();

        List<OrderLineItem> lineItemsChangedDuringRestructure = cancelOrders
            .stream()
            .flatMap(order -> order.getLineItems().stream())
            .filter(ol -> baseExternalSubscriptionChargeIds.contains(ol.getBaseExternalSubscriptionChargeId()))
            .toList();

        Map<String, List<OrderLineItem>> existingChargeIdMap = lineItemsChangedDuringRestructure
            .stream()
            .filter(li -> StringUtils.isNotBlank(li.getBaseExternalSubscriptionChargeId()))
            .collect(Collectors.groupingBy(OrderLineItem::getBaseExternalSubscriptionChargeId));

        List<OrderLineItem> orderLineItems = existingChargeIdMap.get(orderLineItem.getBaseExternalSubscriptionChargeId());
        if (CollectionUtils.isEmpty(orderLineItems)) {
            return metricsService.getOrderLineMetrics(orderLineItem.getOrderLineId());
        }

        // Combine C+R Order Line Metrics
        BigDecimal tcv = orderLineMetrics.getTcv();
        BigDecimal recurringTotal = orderLineMetrics.getRecurringTotal();
        BigDecimal nonRecurringTotal = orderLineMetrics.getNonRecurringTotal();
        BigDecimal arr = metricsService.calculateOrderLineARR(restructureOrder, orderLineItem);
        BigDecimal entryArr = orderLineMetrics.getEntryArr();
        BigDecimal exitArr = orderLineMetrics.getExitArr();
        BigDecimal averageArr = orderLineMetrics.getAverageArr();
        BigDecimal deltaTcv = orderLineMetrics.getDeltaTcv();
        BigDecimal deltaArr = orderLineMetrics.getDeltaArr();

        for (OrderLineItem ol : orderLineItems) {
            var olMetrics = metricsService.getMetricsForOrderLine(ol.getOrderLineId());
            var olArr = metricsService.calculateOrderLineARR(restructureOrder, ol);

            tcv = tcv.add(olMetrics.tcv());
            recurringTotal = recurringTotal.add(olMetrics.recurringTotal());
            nonRecurringTotal = nonRecurringTotal.add(olMetrics.nonRecurringTotal());
            arr = arr.add(olArr);

            entryArr = entryArr.add(olMetrics.arr());
            exitArr = exitArr.add(olMetrics.arr());
            averageArr = averageArr.add(olMetrics.arr());
            deltaTcv = deltaTcv.add(olMetrics.deltaTcv());
            deltaArr = deltaArr.add(olMetrics.deltaArr());
        }

        return new Metrics(
            orderLineItem.getAmount(),
            recurringTotal,
            nonRecurringTotal,
            arr,
            entryArr,
            exitArr,
            averageArr,
            orderLineItem.getAmount(),
            deltaArr,
            null
        );
    }
}
