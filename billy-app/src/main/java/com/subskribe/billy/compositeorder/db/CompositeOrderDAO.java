package com.subskribe.billy.compositeorder.db;

import static com.subskribe.billy.jooq.default_schema.tables.CompositeOrder.COMPOSITE_ORDER;

import com.subskribe.billy.compositeorder.mapper.CompositeOrderDAOMapper;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.model.ImmutableCompositeOrder;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.CompositeOrderRecord;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class CompositeOrderDAO {

    private final CompositeOrderDAOMapper compositeOrderDAOMapper;
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;

    @Inject
    public CompositeOrderDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        compositeOrderDAOMapper = Mappers.getMapper(CompositeOrderDAOMapper.class);
    }

    public void addCompositeOrder(DSLContext dslContext, CompositeOrder compositeOrder) {
        CompositeOrderRecord record = compositeOrderDAOMapper.compositeOrderToRecord(compositeOrder);
        record.reset(COMPOSITE_ORDER.ID);
        record.setTenantId(tenantIdProvider.provideTenantIdString());
        dslContext.insertInto(COMPOSITE_ORDER).set(record).execute();
    }

    public void updateCompositeOrder(CompositeOrder compositeOrder) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);
        updateCompositeOrder(dslContext, tenantId, compositeOrder);
    }

    public void updateCompositeOrder(DSLContext dslContext, String tenantId, CompositeOrder compositeOrder) {
        CompositeOrderRecord record = compositeOrderDAOMapper.compositeOrderToRecord(compositeOrder);
        record.setTenantId(tenantIdProvider.provideTenantIdString());
        dslContext
            .update(COMPOSITE_ORDER)
            .set(record)
            .where(COMPOSITE_ORDER.COMPOSITE_ORDER_ID.eq(compositeOrder.getCompositeOrderId()))
            .and(COMPOSITE_ORDER.TENANT_ID.eq(tenantId))
            .and(COMPOSITE_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    public boolean compositeOrderExists(String compositeOrderId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        return dslContext.fetchExists(
            dslContext
                .selectFrom(COMPOSITE_ORDER)
                .where(COMPOSITE_ORDER.TENANT_ID.eq(tenantId))
                .and(COMPOSITE_ORDER.COMPOSITE_ORDER_ID.eq(compositeOrderId).and(COMPOSITE_ORDER.IS_DELETED.eq(false)))
        );
    }

    public CompositeOrder getCompositeOrder(String compositeOrderId) {
        Optional<CompositeOrder> compositeOrderOptional = getCompositeOrderOptional(compositeOrderId);

        if (compositeOrderOptional.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.COMPOSITE_ORDER, compositeOrderId);
        }

        return compositeOrderOptional.get();
    }

    public OrderStatus getCompositeOrderStatus(String compositeOrderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return dslContext
            .select(COMPOSITE_ORDER.STATUS)
            .from(COMPOSITE_ORDER)
            .where(COMPOSITE_ORDER.TENANT_ID.eq(tenantId))
            .and(COMPOSITE_ORDER.COMPOSITE_ORDER_ID.eq(compositeOrderId))
            .and(COMPOSITE_ORDER.IS_DELETED.isFalse())
            .fetchOneInto(OrderStatus.class);
    }

    public Optional<CompositeOrder> getCompositeOrderOptional(String compositeOrderId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return getCompositeOrderOptional(dslContext, compositeOrderId);
    }

    public Optional<CompositeOrder> getCompositeOrderOptional(DSLContext dslcontext, String compositeOrderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        CompositeOrderRecord record = dslcontext
            .select()
            .from(COMPOSITE_ORDER)
            .where(COMPOSITE_ORDER.TENANT_ID.eq(tenantId))
            .and(COMPOSITE_ORDER.COMPOSITE_ORDER_ID.eq(compositeOrderId))
            .and(COMPOSITE_ORDER.IS_DELETED.isFalse())
            .fetchOneInto(CompositeOrderRecord.class);

        return Optional.ofNullable(compositeOrderDAOMapper.recordToCompositeOrder(record));
    }

    public void updateCompositeOrderCrmDetails(
        DSLContext dslContext,
        String compositeOrderId,
        String oppId,
        String oppName,
        String oppStage,
        String oppType
    ) {
        CompositeOrder compositeOrder = getCompositeOrder(compositeOrderId);
        String tenantId = tenantIdProvider.provideTenantIdString();

        ImmutableCompositeOrder immutableCompositeOrder = ImmutableCompositeOrder.copyOf(compositeOrder)
            .withCrmOpportunityId(oppId)
            .withCrmOpportunityName(oppName)
            .withCrmOpportunityStage(oppStage)
            .withCrmOpportunityType(oppType);
        updateCompositeOrder(dslContext, tenantId, immutableCompositeOrder);
    }

    public void ensureUniqueCompositeOrderId(DSLContext dslContext, String compositeOrderId) {
        CompositeOrderRecord record = dslContext
            .select()
            .from(COMPOSITE_ORDER)
            .where(COMPOSITE_ORDER.COMPOSITE_ORDER_ID.eq(compositeOrderId))
            .fetchOneInto(CompositeOrderRecord.class);

        if (record == null) {
            return;
        }

        throwDuplicateCompositeOrderIdException(compositeOrderId);
    }

    private void throwDuplicateCompositeOrderIdException(String compositeOrderId) {
        var message = "Duplicated CompositeOrderId generated. compositeOrderId = " + compositeOrderId;
        throw new DuplicateIdException(message);
    }

    public void updateCompositeOrderStatus(String compositeOrderId, OrderStatus orderStatus, DSLContext dslContext) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        int count = dslContext
            .update(COMPOSITE_ORDER)
            .set(COMPOSITE_ORDER.STATUS, orderStatus.name())
            .where(COMPOSITE_ORDER.COMPOSITE_ORDER_ID.eq(compositeOrderId))
            .and(COMPOSITE_ORDER.TENANT_ID.eq(tenantId))
            .and(COMPOSITE_ORDER.IS_DELETED.eq(false))
            .execute();
        if (count != 1) {
            throw new ServiceFailureException(
                String.format("The status of the composite order with id: %s could not be updated to %s", compositeOrderId, orderStatus.name())
            );
        }
    }

    public void deleteCompositeOrder(String compositeOrderId, DSLContext transactionalDslContext) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        int count = transactionalDslContext
            .update(COMPOSITE_ORDER)
            .set(COMPOSITE_ORDER.IS_DELETED, true)
            .where(COMPOSITE_ORDER.COMPOSITE_ORDER_ID.eq(compositeOrderId))
            .and(COMPOSITE_ORDER.TENANT_ID.eq(tenantId))
            .and(COMPOSITE_ORDER.IS_DELETED.isFalse())
            .execute();

        if (count != 1) {
            throw new ServiceFailureException(String.format("The composite order with id: %s could not be deleted", compositeOrderId));
        }
    }

    public List<CompositeOrder> getCompositeOrdersByCrmOpportunityId(String crmOpportunityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);

        List<CompositeOrderRecord> records = dslContext
            .select()
            .from(COMPOSITE_ORDER)
            .where(COMPOSITE_ORDER.TENANT_ID.eq(tenantId))
            .and(COMPOSITE_ORDER.CRM_OPPORTUNITY_ID.eq(crmOpportunityId))
            .and(COMPOSITE_ORDER.IS_DELETED.isFalse())
            .orderBy(COMPOSITE_ORDER.UPDATED_ON.desc())
            .fetchInto(CompositeOrderRecord.class);

        if (CollectionUtils.isEmpty(records)) {
            return List.of();
        }

        return compositeOrderDAOMapper.recordsToCompositeOrders(records);
    }
}
