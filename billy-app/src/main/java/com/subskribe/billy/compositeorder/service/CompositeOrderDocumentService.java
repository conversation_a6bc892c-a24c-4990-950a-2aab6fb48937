package com.subskribe.billy.compositeorder.service;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.compositeorder.document.CancelAndRestructureTemplateData;
import com.subskribe.billy.compositeorder.document.UpsellEarlyRenewalTemplateData;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.model.CompositeOrderDocumentJson;
import com.subskribe.billy.compositeorder.model.ImmutableCompositeOrderDocumentJson;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.document.model.DocumentType;
import com.subskribe.billy.document.model.GeneratedDocument;
import com.subskribe.billy.document.service.DocumentService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.invoice.upload.DocumentS3Uploader;
import com.subskribe.billy.order.document.OrderDocumentDataAggregator;
import com.subskribe.billy.order.document.OrderDocumentJson;
import com.subskribe.billy.order.document.OrderTemplateToHtmlConverter;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.OrderPdfGenerationTrackerService;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import com.subskribe.billy.shared.render.HtmlToPdfConverter;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import java.util.UUID;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;

public class CompositeOrderDocumentService {

    private static final DateTimeFormatter FILE_NAME_DATE_FORMAT = DateTimeFormatter.ofPattern("ddMMMyyyy_HHmmss");

    public static final String DOCUMENT_FILE_NAME_FORMAT = "%s/%s.pdf";

    private static final BigDecimal NEGATIVE_ONE = BigDecimal.valueOf(-1);

    private final TenantSettingService tenantSettingService;
    private final BillyConfiguration billyConfiguration;
    private final DocumentService documentService;
    private final CompositeOrderGetService compositeOrderGetService;
    private final OrderDocumentDataAggregator orderDocumentDataAggregator;
    private final CompositeOrderDocumentRenderingService compositeOrderDocumentRenderingService;
    private final TenantService tenantService;
    private final DocumentS3Uploader documentS3Uploader;
    private final TenantIdProvider tenantIdProvider;
    private final HtmlToPdfConverter htmlToPdfConverter;
    private final OrderTemplateToHtmlConverter orderTemplateToHtmlConverter;
    private final OrderPdfGenerationTrackerService orderPdfGenerationTrackerService;

    @Inject
    public CompositeOrderDocumentService(
        TenantSettingService tenantSettingService,
        BillyConfiguration billyConfiguration,
        DocumentService documentService,
        CompositeOrderGetService compositeOrderGetService,
        OrderDocumentDataAggregator orderDocumentDataAggregator,
        CompositeOrderDocumentRenderingService compositeOrderDocumentRenderingService,
        TenantService tenantService,
        DocumentS3Uploader documentS3Uploader,
        TenantIdProvider tenantIdProvider,
        HtmlToPdfConverter htmlToPdfConverter,
        OrderTemplateToHtmlConverter orderTemplateToHtmlConverter,
        OrderPdfGenerationTrackerService orderPdfGenerationTrackerService
    ) {
        this.tenantSettingService = tenantSettingService;
        this.billyConfiguration = billyConfiguration;
        this.documentService = documentService;
        this.compositeOrderGetService = compositeOrderGetService;
        this.orderDocumentDataAggregator = orderDocumentDataAggregator;
        this.compositeOrderDocumentRenderingService = compositeOrderDocumentRenderingService;
        this.tenantService = tenantService;
        this.documentS3Uploader = documentS3Uploader;
        this.tenantIdProvider = tenantIdProvider;
        this.htmlToPdfConverter = htmlToPdfConverter;
        this.orderTemplateToHtmlConverter = orderTemplateToHtmlConverter;
        this.orderPdfGenerationTrackerService = orderPdfGenerationTrackerService;
    }

    public void createCompositeOrderDocumentWithoutChecks(String compositeOrderId) {
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);

        List<Order> relatedOrders = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrderId);
        if (!shouldRegenerateCompositeOrderPdf(relatedOrders)) {
            return;
        }
        byte[] compositePdf = generateCompositeOrderPdfBytes(compositeOrder);

        CompositeOrderDocumentJson compositeOrderDocumentJson = generateAndPopulateDocumentKey(compositeOrderId);

        // todo: generate Word Doc

        String bucketName = compositeOrderDocumentJson.getS3Bucket();

        // upload PDF
        String pdfFileName = compositeOrderDocumentJson.getFileName();
        documentS3Uploader.upload(bucketName, pdfFileName, compositePdf);

        // todo: upload Word Doc

        String documentKey = compositeOrderDocumentJson.getDocumentKey();
        String tenantId = compositeOrderDocumentJson.getTenantInfo().getTenantId();
        documentService.setDocumentLocationIdentifier(tenantId, UUID.fromString(documentKey), pdfFileName);
        updateShouldRegeneratePdf(relatedOrders);
    }

    private boolean shouldRegenerateCompositeOrderPdf(List<Order> relatedOrders) {
        return relatedOrders.stream().anyMatch(Order::getShouldRegeneratePdf);
    }

    private void updateShouldRegeneratePdf(List<Order> relatedOrders) {
        relatedOrders.forEach(order -> orderPdfGenerationTrackerService.setShouldRegeneratePdf(order.getOrderId(), false));
    }

    private byte[] generateCompositeOrderPdfBytes(CompositeOrder compositeOrder) {
        return switch (compositeOrder.getType()) {
            case UPSELL_AND_EARLY_RENEWAL -> getUpsellAndEarlyRenewalPdfBytes(compositeOrder);
            case CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE -> getCancelAndRestructurePdfBytes(compositeOrder);
        };
    }

    private byte[] getUpsellAndEarlyRenewalPdfBytes(CompositeOrder compositeOrder) {
        String htmlForPdf = generateUpsellEarlyRenewalHtml(compositeOrder);
        Optional<String> templateCssFilename = Optional.ofNullable(getOrderFormTemplateCssFileName()); // todo: populate
        // todo: do we need to upload attachments as well?
        return htmlToPdfConverter.generatePdf(htmlForPdf, templateCssFilename);
    }

    private byte[] getCancelAndRestructurePdfBytes(CompositeOrder compositeOrder) {
        String htmlForPdf = generateCancelAndRestructureHtml(compositeOrder);
        Optional<String> templateCssFilename = Optional.ofNullable(getOrderFormTemplateCssFileName());
        return htmlToPdfConverter.generatePdf(htmlForPdf, templateCssFilename);
    }

    private String generateCancelAndRestructureHtml(CompositeOrder compositeOrder) {
        List<String> orderIds = compositeOrderGetService.getOrderIdsInCompositeOrder(compositeOrder.getCompositeOrderId());
        List<OrderDocumentJson> orderDocumentJsons = orderIds.stream().map(orderDocumentDataAggregator::getOrderDocumentJson).toList();
        OrderDocumentJson restructureDocumentJson = orderDocumentJsons
            .stream()
            .filter(doc -> doc.getOrderDetail().getOrderType() == OrderType.RESTRUCTURE)
            .findFirst()
            .orElseThrow();
        OrderDocumentJson cancellationDocumentJson = orderDocumentJsons
            .stream()
            .filter(doc -> doc.getOrderDetail().getOrderType() == OrderType.CANCEL)
            .findFirst()
            .orElseThrow();

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        DocumentRenderFormatter formatter = new DocumentRenderFormatter(timeZone);
        String currency = getCurrency(restructureDocumentJson);

        CancelAndRestructureTemplateData cancelAndRestructureTemplateData = new CancelAndRestructureTemplateData(
            formatter,
            currency,
            compositeOrder,
            restructureDocumentJson,
            cancellationDocumentJson,
            cancellationDocumentJson.getOrderDetail().getTotalAmount().multiply(NEGATIVE_ONE),
            restructureDocumentJson.getOrderDetail().getTotalAmount().add(cancellationDocumentJson.getOrderDetail().getTotalAmount())
        );
        orderTemplateToHtmlConverter.populateOrderDocumentTemplate(cancelAndRestructureTemplateData.getRestructure(), restructureDocumentJson);
        orderTemplateToHtmlConverter.populateOrderDocumentTemplate(cancelAndRestructureTemplateData.getCancel(), cancellationDocumentJson);

        return compositeOrderDocumentRenderingService.renderCancelAndRestructureHtml(
            cancelAndRestructureTemplateData,
            compositeOrder.getDocumentMasterTemplateId()
        );
    }

    private static String getCurrency(OrderDocumentJson restructureDocumentJson) {
        if (StringUtils.isNotBlank(restructureDocumentJson.getOrderDetail().getCurrency())) {
            return restructureDocumentJson.getOrderDetail().getCurrency();
        }
        if (restructureDocumentJson.getOrderDetail().getAccount() == null) {
            return SupportedCurrency.getDefaultCurrency().getCurrencyCode();
        }

        return restructureDocumentJson.getOrderDetail().getAccount().getCurrency();
    }

    public String getOrderFormTemplateCssFileName() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        return tenantScopedConfig.getDocumentConfiguration().getOrderFormTemplateCssFileName();
    }

    public CompositeOrderDocumentJson generateAndPopulateDocumentKey(String compositeOrderId) {
        var compositeOrderDocumentJson = ImmutableCompositeOrderDocumentJson.builder();

        String tenantId = tenantIdProvider.provideTenantIdString();
        compositeOrderDocumentJson.tenantInfo(tenantService.getCurrentTenantInfo());

        var document = new GeneratedDocument();
        document.setDocumentId(compositeOrderId);
        document.setDocumentType(DocumentType.COMPOSITE_ORDER);

        var savedDocument = documentService.insertDocument(document);
        String documentKey = savedDocument.getDocumentKey().toString();
        compositeOrderDocumentJson.documentKey(documentKey);

        String fileName = String.format(DOCUMENT_FILE_NAME_FORMAT, tenantId, documentKey);
        compositeOrderDocumentJson.fileName(fileName);
        compositeOrderDocumentJson.s3Bucket(billyConfiguration.getDocumentConfiguration().getOrderS3Bucket());
        return compositeOrderDocumentJson.build();
    }

    public Optional<InputStream> getCompositeOrderDocument(String compositeOrderId) {
        GeneratedDocument document;
        try {
            document = documentService.getLatestDocument(compositeOrderId, DocumentType.COMPOSITE_ORDER);
        } catch (ObjectNotFoundException e) {
            return Optional.empty();
        }

        if (StringUtils.isBlank(document.getDocumentLocationIdentifier())) {
            return Optional.empty();
        }

        return documentService.getDocumentFromS3(
            billyConfiguration.getDocumentConfiguration().getOrderS3Bucket(),
            document.getDocumentLocationIdentifier()
        );
    }

    public Optional<InputStream> getCompositeOrderDocumentCreateIfNeeded(String compositeOrderId) {
        createCompositeOrderDocumentWithoutChecks(compositeOrderId);
        return getCompositeOrderDocument(compositeOrderId);
    }

    public String getCompositeOrderDocumentFileName(String compositeOrderId) {
        return String.format(
            "%s_%s",
            compositeOrderId,
            FILE_NAME_DATE_FORMAT.format(
                ZonedDateTime.ofInstant(Instant.now(), tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId())
            )
        );
    }

    public String generateUpsellEarlyRenewalHtml(CompositeOrder compositeOrder) {
        List<String> orderIds = compositeOrderGetService.getOrderIdsInCompositeOrder(compositeOrder.getCompositeOrderId());
        List<OrderDocumentJson> orderDocumentJsons = orderIds.stream().map(orderDocumentDataAggregator::getOrderDocumentJson).toList();
        OrderDocumentJson amendmentDocumentJson = orderDocumentJsons
            .stream()
            .filter(doc -> doc.getOrderDetail().getOrderType() == OrderType.AMENDMENT)
            .findFirst()
            .orElseThrow();
        OrderDocumentJson renewalDocumentJson = orderDocumentJsons
            .stream()
            .filter(doc -> doc.getOrderDetail().getOrderType() == OrderType.RENEWAL)
            .findFirst()
            .orElseThrow();

        var upsellEarlyRenewalTemplateData = new UpsellEarlyRenewalTemplateData(compositeOrder, amendmentDocumentJson, renewalDocumentJson);
        orderTemplateToHtmlConverter.populateOrderDocumentTemplate(upsellEarlyRenewalTemplateData.getAmendment(), amendmentDocumentJson);
        orderTemplateToHtmlConverter.populateOrderDocumentTemplate(upsellEarlyRenewalTemplateData.getRenewal(), renewalDocumentJson);

        return compositeOrderDocumentRenderingService.renderUpsellEarlyRenewalHtml(
            upsellEarlyRenewalTemplateData,
            compositeOrder.getDocumentMasterTemplateId()
        );
    }
}
