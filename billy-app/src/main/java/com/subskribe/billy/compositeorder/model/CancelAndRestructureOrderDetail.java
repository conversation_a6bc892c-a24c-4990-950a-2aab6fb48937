package com.subskribe.billy.compositeorder.model;

import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.order.OrderDetailMinimal;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountDetail;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.graphql.template.DocumentTemplateResponse;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalSegmentJson;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.CompositeOrderType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import com.subskribe.billy.template.model.DocumentCustomContent;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import java.util.List;
import javax.annotation.Nullable;
import org.immutables.value.Value;

@GraphQLName("CancelAndRestructureOrder")
@BillyModelStyle
@Value.Immutable
public interface CancelAndRestructureOrderDetail {
    @Nullable
    @GraphQLField
    @GraphQLName("id")
    String getId();

    @Nullable
    @GraphQLField
    @GraphQLName("type")
    CompositeOrderType getType();

    @Nullable
    @GraphQLField
    @GraphQLName("name")
    String getName();

    @Nullable
    @GraphQLField
    @GraphQLName("owner")
    UserJson getOwner();

    @GraphQLField
    @GraphQLName("account")
    @GraphQLNonNull
    AccountJson getAccount();

    @Nullable
    @GraphQLField
    @GraphQLName("resoldBy")
    AccountJson getResoldBy();

    @Nullable
    @GraphQLField
    @GraphQLName("shippingContact")
    AccountContactJson getShippingContact();

    @Nullable
    @GraphQLField
    @GraphQLName("billingContact")
    AccountContactJson getBillingContact();

    @Nullable
    @GraphQLField
    @GraphQLName("currentSubscription")
    SubscriptionDetail getCurrentSubscription();

    @Nullable
    @GraphQLField
    @GraphQLName("subscriptionId")
    String getSubscriptionId();

    @GraphQLField
    @GraphQLName("startDate")
    @GraphQLNonNull
    long getStartDate();

    @Nullable
    @GraphQLField
    @GraphQLName("endDate")
    Long getEndDate();

    @Nullable
    @GraphQLField
    @GraphQLName("expiresOn")
    Long getExpiresOn();

    @Nullable
    @GraphQLField
    @GraphQLName("termLength")
    RecurrenceJson getTermLength();

    @GraphQLField
    @GraphQLName("billingCycle")
    @GraphQLNonNull
    RecurrenceJson getBillingCycle();

    @Nullable
    @GraphQLField
    @GraphQLName("billingAnchorDate")
    Long getBillingAnchorDate();

    @GraphQLField
    @GraphQLName("billingTerm")
    @GraphQLNonNull
    BillingTerm getBillingTerm();

    @Nullable
    @GraphQLField
    @GraphQLName("purchaseOrderNumber")
    String getPurchaseOrderNumber();

    @GraphQLField
    @GraphQLName("purchaseOrderRequiredForInvoicing")
    boolean getPurchaseOrderRequiredForInvoicing();

    @GraphQLField
    @GraphQLName("autoRenew")
    boolean getAutoRenew();

    @Nullable
    @GraphQLField
    @GraphQLName("rampInterval")
    List<Long> getRampInterval();

    @Nullable
    @GraphQLField
    @GraphQLName("predefinedDiscounts")
    List<PredefinedDiscountDetail> getPredefinedDiscounts();

    @Nullable
    @GraphQLField
    @GraphQLName("status")
    OrderStatus getStatus();

    @Nullable
    @GraphQLField
    @GraphQLName("crmOpportunityId")
    String getCrmOpportunityId();

    @Nullable
    @GraphQLField
    @GraphQLName("orders")
    List<OrderDetailMinimal> getOrders();

    @Nullable
    @GraphQLField
    @GraphQLName("documentMasterTemplateId")
    String getDocumentMasterTemplateId();

    @GraphQLNonNull
    @GraphQLField
    @GraphQLName("lineItems")
    List<OrderLineItemDetail> getLineItems();

    @Nullable
    @GraphQLField
    @GraphQLName("removedLineItems")
    List<OrderLineItemDetail> getRemovedLineItems();

    @GraphQLNonNull
    @GraphQLField
    @GraphQLName("paymentTerm")
    String getPaymentTerm();

    // For now restructureForSubscriptionId seems to suffice, we may add  restructureForSubscription detail if needed
    @Nullable
    @GraphQLField
    @GraphQLName("restructureForSubscriptionId")
    String getRestructureForSubscriptionId();

    @Nullable
    @GraphQLField
    @GraphQLName("compositeOrderId")
    String getCompositeOrderId();

    @Nullable
    @GraphQLField
    @GraphQLName("arr")
    BigDecimal getArr();

    @Nullable
    @GraphQLField
    @GraphQLName("deltaArr")
    BigDecimal getDeltaArr();

    @Nullable
    @GraphQLField
    @GraphQLName("tcv")
    BigDecimal getTcv();

    @Nullable
    @GraphQLField
    @GraphQLName("recurringTotal")
    BigDecimal getRecurringTotal();

    @Nullable
    @GraphQLField
    @GraphQLName("nonRecurringTotal")
    BigDecimal getNonRecurringTotal();

    @Nullable
    @GraphQLField
    @GraphQLName("orderFormTemplates")
    List<DocumentTemplateResponse> getOrderFormTemplates();

    @Nullable
    @GraphQLField
    @GraphQLName("documentCustomContent")
    DocumentCustomContent getDocumentCustomContent();

    @Nullable
    @GraphQLField
    @GraphQLName("crmOpportunityName")
    String getCrmOpportunityName();

    @Nullable
    @GraphQLField
    @GraphQLName("crmOpportunityType")
    String getCrmOpportunityType();

    @Nullable
    @GraphQLField
    @GraphQLName("crmOpportunityStage")
    String getCrmOpportunityStage();

    @Nullable
    @GraphQLField
    @GraphQLName("customFields")
    List<@GraphQLNonNull CustomFieldEntry> getCustomFields();

    @Nullable
    @GraphQLField
    @GraphQLName("entityId")
    String getEntityId();

    @Nullable
    @GraphQLField
    @GraphQLName("currency")
    String currency();

    @Nullable
    @GraphQLField
    @GraphQLName("approvalSegment")
    ApprovalSegmentJson getApprovalSegment();
}
