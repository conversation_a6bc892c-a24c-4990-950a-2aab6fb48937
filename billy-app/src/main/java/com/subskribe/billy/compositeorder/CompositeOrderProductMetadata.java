package com.subskribe.billy.compositeorder;

import com.subskribe.billy.shared.logger.ErrorInstructions;
import com.subskribe.billy.shared.product.ProductArea;
import com.subskribe.billy.shared.product.ProductFeature;
import com.subskribe.billy.shared.product.ProductFeatureAvailability;

public interface CompositeOrderProductMetadata {
    ProductArea COMPOSITE_ORDER_PRODUCT_AREA = new ProductArea("CompositeOrder");

    ProductFeature COMPOSITE_ORDER_TEMPLATE = new ProductFeature(
        COMPOSITE_ORDER_PRODUCT_AREA,
        "Composite Order Template",
        ProductFeatureAvailability.GA
    );

    ProductFeature COMPOSITE_ORDER_CORE = new ProductFeature(COMPOSITE_ORDER_PRODUCT_AREA, "Composite Order Core", ProductFeatureAvailability.GA);

    ErrorInstructions NO_ORDERS_IN_COMPOSITE_ORDER = ErrorInstructions.sev2(
        "No orders found in composite order",
        "This should never happen. Please contact Subskribe team for resolution."
    );
}
