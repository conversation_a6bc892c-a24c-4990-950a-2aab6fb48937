package com.subskribe.billy.compositeorder.service;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.compositeorder.db.CompositeOrderDAO;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.model.ImmutableCompositeOrder;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.compositeorder.AmendAndRenewCreateRequest;
import com.subskribe.billy.graphql.compositeorder.CompositeOrderFieldsUpdateRequest;
import com.subskribe.billy.graphql.order.CancelAndRestructureMapper;
import com.subskribe.billy.graphql.order.CancelAndRestructureRequest;
import com.subskribe.billy.hubspot.service.HubSpotJobQueueService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderSource;
import com.subskribe.billy.order.services.ChangeOrderService;
import com.subskribe.billy.order.services.OrderEventService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.order.services.OrderServiceHelper;
import com.subskribe.billy.postgres.PostgresAdvisoryLock;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.opportunity.OpportunityInput;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.CompositeOrderType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Strings;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class CompositeOrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CompositeOrderService.class);
    private final FeatureService featureService;
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final CompositeOrderIdGenerator compositeOrderIdGenerator;
    private final CompositeOrderDAO compositeOrderDAO;
    private final OrderService orderService;
    private final ChangeOrderService changeOrderService;
    private final CompositeOrderGetService compositeOrderGetService;
    private final SalesforceJobQueueService salesforceJobQueueService;
    private final OpportunityService opportunityService;
    private final TenantSettingService tenantSettingService;
    private final OrderEventService orderEventService;
    private final SubscriptionGetService subscriptionGetService;
    private final CancelAndRestructureMapper cancelAndRestructureMapper;
    private final HubSpotJobQueueService hubSpotJobQueueService;
    private final CustomFieldAPIMapper customFieldAPIMapper;
    private final InvoiceService invoiceService;
    private final ProductCatalogGetService productCatalogGetService;
    private final CrmService crmService;
    private final AccountGetService accountGetService;
    private final EventPublishingService eventPublishingService;

    private static final String UPDATE_COMPOSITE_ORDER_STATUS_LOCK_KEY_FORMAT = "updateCompositeOrderStatus/%s/%s";
    private static final Set<CompositeOrderType> ALLOWED_COMPOSITE_ORDER_TYPES = Set.of(
        CompositeOrderType.UPSELL_AND_EARLY_RENEWAL,
        CompositeOrderType.CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE
    );

    @Inject
    public CompositeOrderService(
        FeatureService featureService,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        CompositeOrderIdGenerator compositeOrderIdGenerator,
        CompositeOrderDAO compositeOrderDAO,
        OrderService orderService,
        ChangeOrderService changeOrderService,
        CompositeOrderGetService compositeOrderGetService,
        SalesforceJobQueueService salesforceJobQueueService,
        OpportunityService opportunityService,
        TenantSettingService tenantSettingService,
        OrderEventService orderEventService,
        SubscriptionGetService subscriptionGetService,
        HubSpotJobQueueService hubSpotJobQueueService,
        InvoiceService invoiceService,
        ProductCatalogGetService productCatalogGetService,
        CrmService crmService,
        AccountGetService accountGetService,
        EventPublishingService eventPublishingService
    ) {
        this.featureService = featureService;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.compositeOrderIdGenerator = compositeOrderIdGenerator;
        this.compositeOrderDAO = compositeOrderDAO;
        this.orderService = orderService;
        this.changeOrderService = changeOrderService;
        this.compositeOrderGetService = compositeOrderGetService;
        this.salesforceJobQueueService = salesforceJobQueueService;
        this.opportunityService = opportunityService;
        this.tenantSettingService = tenantSettingService;
        this.orderEventService = orderEventService;
        this.subscriptionGetService = subscriptionGetService;
        this.hubSpotJobQueueService = hubSpotJobQueueService;
        this.invoiceService = invoiceService;
        this.productCatalogGetService = productCatalogGetService;
        this.crmService = crmService;
        this.accountGetService = accountGetService;
        this.eventPublishingService = eventPublishingService;
        cancelAndRestructureMapper = Mappers.getMapper(CancelAndRestructureMapper.class);
        customFieldAPIMapper = Mappers.getMapper(CustomFieldAPIMapper.class);
    }

    private void checkFeature() {
        if (
            !(featureService.isEnabled(Feature.UPSELL_EARLY_RENEWAL) || featureService.isEnabled(Feature.CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE))
        ) {
            throw new UnsupportedOperationException("Composite order feature (Upsell early renewal, Cancel Restructure) not enabled");
        }
    }

    public String createUpsellEarlyRenewal(
        String subscriptionId,
        String crmOpportunityId,
        OpportunityInput opportunityInput,
        Optional<AmendAndRenewCreateRequest> amendAndRenewCreateRequest
    ) {
        checkFeature();
        String tenantId = tenantIdProvider.provideTenantIdString();
        var subscription = subscriptionGetService.getSubscription(subscriptionId);
        String entityId = subscription.getEntityId();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        CustomField customFields = amendAndRenewCreateRequest
            .map(AmendAndRenewCreateRequest::customFields)
            .map(customFieldAPIMapper::inputsToCustomField)
            .orElse(null);

        // todo: create composite order in transaction

        // Create composite order Id
        String compositeOrderId = compositeOrderIdGenerator.generate();

        // create upsell
        Order draftAmendment = changeOrderService.generateDraftAmendment(subscriptionId);
        draftAmendment.setCompositeOrderId(compositeOrderId);
        draftAmendment.setCustomFields(customFields);

        changeOrderService.createChangeOrder(draftAmendment, false, false);

        // create renewal
        Order draftRenewal = new Order();
        draftRenewal.setRenewalForSubscriptionId(subscriptionId);
        draftRenewal.setOrderType(OrderType.RENEWAL);
        draftRenewal.setCompositeOrderId(compositeOrderId);
        draftRenewal.setCustomFields(customFields);
        draftRenewal.setSource(OrderSource.USER);
        draftRenewal.setShouldUseCustomBillingSchedule(true);
        orderService.renewSubscription(draftRenewal, false);

        // create composite order
        CompositeOrder draftCompositeOrder = ImmutableCompositeOrder.builder()
            .compositeOrderId(compositeOrderId)
            .entityId(entityId)
            .type(CompositeOrderType.UPSELL_AND_EARLY_RENEWAL)
            .status(OrderStatus.DRAFT)
            .crmOpportunityId(opportunityInput != null ? opportunityInput.crmId() : crmOpportunityId)
            .crmOpportunityName(opportunityInput != null ? opportunityInput.name() : null)
            .crmOpportunityStage(opportunityInput != null ? opportunityInput.stage() : null)
            .crmOpportunityType(opportunityInput != null ? opportunityInput.type() : null)
            .build();
        compositeOrderDAO.addCompositeOrder(dslContext, draftCompositeOrder);

        // salesforce sync
        opportunityService.upsertOpportunityForCompositeOrderIfNeeded(dslContext, draftCompositeOrder, tenantId, draftRenewal.getAccountId());
        salesforceJobQueueService.dispatchCompositeOrderSync(compositeOrderId, draftRenewal.getAccountId(), draftRenewal.getTenantId());
        hubSpotJobQueueService.dispatchHubSpotCompositeOrderSyncTenantJob(compositeOrderId, draftRenewal.getAccountId(), draftRenewal.getTenantId());
        return compositeOrderId;
    }

    public void updateCompositeOrderFields(String compositeOrderId, CompositeOrderFieldsUpdateRequest compositeOrderFieldsUpdateRequest) {
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);

        // Fetch the opportunity by ID and update the composite order with it
        Opportunity opportunity = null;
        if (StringUtils.isNotBlank(compositeOrderFieldsUpdateRequest.getCrmOpportunityId())) {
            String crmOpportunityId = compositeOrderFieldsUpdateRequest.getCrmOpportunityId().trim();
            String tenantId = tenantIdProvider.provideTenantIdString();
            Optional<Order> order = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrder.getCompositeOrderId()).stream().findAny();
            if (order.isEmpty()) {
                throw new ServiceFailureException(String.format("Sub-orders for composite order %s not found", compositeOrderId));
            }

            Account account = accountGetService.getAccount(order.get().getAccountId());
            opportunity = crmService
                .getOpportunitiesFromCrm(account.getCrmId(), tenantId)
                .stream()
                .filter(o -> Strings.CI.equals(crmOpportunityId, o.getCrmId()))
                .findAny()
                .orElse(null);

            Optional<EventType> eventType = crmService.getEventType();
            if (opportunity == null && eventType.isPresent()) {
                LOGGER.info("Failed to fetch opportunity for composite order {} with opportunity id {}", compositeOrderId, crmOpportunityId);
                eventPublishingService.publishEventInTransaction(
                    dslContextProvider.get(tenantId),
                    eventType.get(),
                    tenantId,
                    order.get().getEntityId(),
                    account.getAccountId(),
                    compositeOrderId.getBytes(StandardCharsets.UTF_8)
                );
            }
        }

        UUID masterTemplateUUID = StringUtils.isBlank(compositeOrderFieldsUpdateRequest.getDocumentMasterTemplateId())
            ? null
            : UUID.fromString(compositeOrderFieldsUpdateRequest.getDocumentMasterTemplateId());
        CompositeOrder updatedCompositeOrder = ImmutableCompositeOrder.builder()
            .from(compositeOrder)
            .crmOpportunityId(opportunity != null ? opportunity.getCrmId() : null)
            .crmOpportunityName(opportunity != null ? opportunity.getName() : null)
            .crmOpportunityType(opportunity != null ? opportunity.getType() : null)
            .crmOpportunityStage(opportunity != null ? opportunity.getStage() : null)
            .documentMasterTemplateId(masterTemplateUUID)
            .updatedOn(Instant.now())
            .build();
        compositeOrderDAO.updateCompositeOrder(updatedCompositeOrder);

        // salesforce sync
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var accountId = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrderId).stream().findFirst().orElseThrow().getAccountId();
        opportunityService.upsertOpportunityForCompositeOrderIfNeeded(dslContext, updatedCompositeOrder, tenantId, accountId);
        salesforceJobQueueService.dispatchCompositeOrderSync(compositeOrderId, accountId, tenantId);
        hubSpotJobQueueService.dispatchHubSpotCompositeOrderSyncTenantJob(compositeOrderId, accountId, tenantId);
    }

    public void executeCompositeOrder(String compositeOrderId, Long executedOn, boolean adminApprovalFlowBypass) {
        OrderStatus currentStatus = compositeOrderGetService.getCompositeOrderStatus(compositeOrderId);
        while (currentStatus != OrderStatus.EXECUTED) {
            switch (currentStatus) {
                case APPROVED -> {
                    Optional<Instant> executedOnInstant = Optional.ofNullable(executedOn).map(Instant::ofEpochSecond);
                    updateCompositeOrderStatus(compositeOrderId, OrderStatus.EXECUTED, executedOnInstant, adminApprovalFlowBypass);
                    // terminal status if successful
                    return;
                }
                case SUBMITTED -> updateCompositeOrderStatus(compositeOrderId, OrderStatus.APPROVED, Optional.empty(), adminApprovalFlowBypass);
                case DRAFT -> updateCompositeOrderStatus(compositeOrderId, OrderStatus.SUBMITTED, Optional.empty(), adminApprovalFlowBypass);
                case EXPIRED -> {
                    if (adminApprovalFlowBypass) {
                        updateCompositeOrderStatus(compositeOrderId, OrderStatus.SUBMITTED, Optional.empty(), true);
                    } else {
                        throw new InvalidInputException(String.format("Expired composite order %s cannot be executed", compositeOrderId));
                    }
                }
                default -> throw new IllegalStateException(
                    String.format("composite order %s is in unknown state %s", compositeOrderId, currentStatus)
                );
            }
            currentStatus = compositeOrderDAO.getCompositeOrderStatus(compositeOrderId);
        }
    }

    public void updateCompositeOrderStatus(
        String compositeOrderId,
        OrderStatus requestedOrderStatus,
        Optional<Instant> statusUpdatedOnInstant,
        boolean adminApprovalByPass
    ) {
        checkFeature();
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        OrderServiceHelper.validateOrderStatusTransition(compositeOrder.getStatus(), requestedOrderStatus);
        List<Order> ordersInCompositeOrder = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrderId);
        if (Objects.isNull(ordersInCompositeOrder)) {
            return;
        }
        validateBillToAndShipToAreEqualOnSubmitted(compositeOrderId, compositeOrder.getType(), ordersInCompositeOrder, requestedOrderStatus);

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        List<Order> mutableCompositeOrdersList = new ArrayList<>(ordersInCompositeOrder);
        tenantDslContext.transaction(configuration -> {
            DSLContext transactionalDslContext = DSL.using(configuration);
            tryAcquireAdvisoryLock(compositeOrderId, transactionalDslContext);
            mutableCompositeOrdersList.forEach(order -> {
                Order updatedOrder = orderService.updateOrderStatusInternalInTransaction(
                    order,
                    requestedOrderStatus,
                    statusUpdatedOnInstant,
                    transactionalDslContext,
                    timeZone
                );
                orderEventService.publishEventsForOrderUpdate(updatedOrder, transactionalDslContext);
            });
            compositeOrderDAO.updateCompositeOrderStatus(compositeOrderId, requestedOrderStatus, transactionalDslContext);
        });

        // trigger individual approval flows if the composite order is submitted
        if (requestedOrderStatus == OrderStatus.DRAFT || requestedOrderStatus == OrderStatus.SUBMITTED) {
            // fetch newly updated composite orders
            List<Order> mutableLatestOrders = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrderId);
            mutableLatestOrders.forEach(order ->
                orderService.triggerApprovalFlowChecksForAutoApprovedOrCancelled(order, tenantDslContext, adminApprovalByPass)
            );
        }

        checkApprovalFlowStatusForCompositeOrder(compositeOrderId);
        triggerExternalDependencies(compositeOrderId);
    }

    public void tryAcquireAdvisoryLock(String compositeOrderId, DSLContext dslContext) {
        String lockKey = String.format(UPDATE_COMPOSITE_ORDER_STATUS_LOCK_KEY_FORMAT, tenantIdProvider.provideTenantIdString(), compositeOrderId);

        String advisoryLockErrorMessage = String.format(
            "Another update in progress when updating the status for composite order: %s. Please retry again.",
            compositeOrderId
        );

        ConflictingStateException conflictingStateException = new ConflictingStateException(System.Logger.Level.WARNING, advisoryLockErrorMessage);
        PostgresAdvisoryLock.tryAcquireLockOrThrow(dslContext, lockKey, () -> conflictingStateException);
    }

    private void triggerExternalDependencies(String compositeOrderId) {
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        List<Order> ordersInCompositeOrder = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrderId);
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        // salesforce sync
        String tenantId = tenantIdProvider.provideTenantIdString();
        String accountId = ordersInCompositeOrder.stream().findFirst().orElseThrow().getAccountId();
        opportunityService.upsertOpportunityForCompositeOrderIfNeeded(tenantDslContext, compositeOrder, tenantId, accountId);
        if (StringUtils.isNotBlank(compositeOrder.getCrmOpportunityId()) && compositeOrder.getStatus() == OrderStatus.EXECUTED) {
            opportunityService.setOpportunityAsClosed(tenantDslContext, compositeOrder.getCrmOpportunityId());
        }
        salesforceJobQueueService.dispatchCompositeOrderSync(compositeOrderId, accountId, tenantId);
        hubSpotJobQueueService.dispatchHubSpotCompositeOrderSyncTenantJob(compositeOrderId, accountId, tenantId);

        // call all downstream dependencies on each of the orders in composite orders.
        ordersInCompositeOrder.forEach(orderService::triggerOrderUpdateDownstreamDependencies);
    }

    private void validateBillToAndShipToAreEqualOnSubmitted(
        String compositeOrderId,
        CompositeOrderType compositeOrderType,
        List<Order> ordersInCompositeOrder,
        OrderStatus requestedOrderStatus
    ) {
        if (!ALLOWED_COMPOSITE_ORDER_TYPES.contains(compositeOrderType)) {
            String errorMessage = String.format(
                "Only composite order types %s are supported. Given composite order type: %s",
                ALLOWED_COMPOSITE_ORDER_TYPES,
                compositeOrderType
            );
            throw new InvalidInputException(errorMessage);
        }

        if (ordersInCompositeOrder.size() != 2) {
            throw new IllegalStateException("Number of orders in composite order must be 2. Orders count = " + ordersInCompositeOrder.size());
        }

        if (requestedOrderStatus != OrderStatus.SUBMITTED) {
            return;
        }

        String billToContactId = ordersInCompositeOrder.stream().findAny().orElseThrow().getBillingContactId();
        if (!ordersInCompositeOrder.stream().allMatch(order -> Strings.CS.equals(order.getBillingContactId(), billToContactId))) {
            throw new IllegalStateException("Bill to contact is not the same on all orders of this composite order: " + compositeOrderId);
        }

        String shipToContactId = ordersInCompositeOrder.stream().findAny().orElseThrow().getShippingContactId();
        if (!ordersInCompositeOrder.stream().allMatch(order -> Strings.CS.equals(order.getShippingContactId(), shipToContactId))) {
            throw new IllegalStateException("Ship to contact is not the same on all orders of this composite order: " + compositeOrderId);
        }
    }

    public void checkApprovalFlowStatusForCompositeOrder(String compositeOrderId) {
        List<Order> ordersInCompositeOrder = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrderId);
        Optional<OrderStatus> anyNonApprovedStatus = ordersInCompositeOrder
            .stream()
            .map(Order::getStatus)
            .filter(orderStatus -> (orderStatus != OrderStatus.APPROVED))
            .findAny();
        if (anyNonApprovedStatus.isEmpty()) {
            DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
            compositeOrderDAO.updateCompositeOrderStatus(compositeOrderId, OrderStatus.APPROVED, dslContext);
        }
    }

    public void moveAllOrdersInCompositeOrderToDraft(String compositeOrderId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext.transaction(configuration -> moveAllOrdersInCompositeOrderToDraftInTransaction(compositeOrderId, DSL.using(configuration)));
    }

    private void moveAllOrdersInCompositeOrderToDraftInTransaction(String compositeOrderId, DSLContext dslContext) {
        List<Order> ordersInCompositeOrder = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrderId);
        TimeZone tenantTimeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        ordersInCompositeOrder.forEach(order -> {
            orderService.updateOrderStatusInternalInTransaction(order, OrderStatus.DRAFT, Optional.empty(), dslContext, tenantTimeZone);
            orderService.triggerApprovalFlowChecksForAutoApprovedOrCancelled(order, dslContext, false);
        });
        compositeOrderDAO.updateCompositeOrderStatus(compositeOrderId, OrderStatus.DRAFT, dslContext);
    }

    public List<Order> generateCancelAndRestructure(String subscriptionId) {
        if (StringUtils.isBlank(subscriptionId)) {
            throw new IllegalArgumentException("subscriptionId is null");
        }
        checkCancelSingleSubscriptionAndRestructureFeature();
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        Instant restructureDate = generateRestructureStartDate(subscription);
        //Restructure subscription
        Order draftRestructureOrder = orderService.generateDraftRestructure(subscription, restructureDate);
        Order draftCancelledOrder = changeOrderService.generateCancelOrder(subscriptionId, Optional.of(restructureDate));
        return List.of(draftRestructureOrder, draftCancelledOrder);
    }

    private void checkCancelSingleSubscriptionAndRestructureFeature() {
        if (!featureService.isEnabled(Feature.CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE)) {
            throw new UnsupportedOperationException("Cancel and restructure is not enabled");
        }
    }

    public List<Order> upsertCancelAndRestructure(CancelAndRestructureRequest cancelAndRestructureRequest, Boolean isDryRun) {
        if (cancelAndRestructureRequest.getOrderType() != OrderType.RESTRUCTURE) {
            throw new InvalidInputException("Only RESTRUCTURE order type is supported using upsertCancelAndRestructure");
        }
        String currentSubscriptionId = cancelAndRestructureRequest.getSubscriptionId();
        Validator.validateStringNotBlank(currentSubscriptionId, "currentSubscriptionId");
        Subscription subscription = subscriptionGetService.getSubscription(currentSubscriptionId);
        validateAccount(cancelAndRestructureRequest, subscription);
        validateCurrency(cancelAndRestructureRequest, subscription);

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        String compositeOrderId = cancelAndRestructureRequest.getCompositeOrderId();
        CompositeOrder compositeOrder = StringUtils.isBlank(compositeOrderId) ? null : compositeOrderDAO.getCompositeOrder(compositeOrderId);
        List<Order> ordersInCompositeOrder = compositeOrder == null ? null : compositeOrderGetService.getOrdersInCompositeOrder(compositeOrderId);

        Order restructureOrderInput = cancelAndRestructureMapper.jsonToOrder(cancelAndRestructureRequest);
        checkCancelSingleSubscriptionAndRestructureFeature();
        validateCancelAndRestructure(restructureOrderInput);
        validateCancelAndRestructureDate(restructureOrderInput, subscription, timeZone);

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        //Create a composite order if dryRun is false and compositeOrderId is null - this will be the first when cancel and restructure is saved
        boolean firstTimeSave = !isDryRun && StringUtils.isBlank(compositeOrderId);

        //Restructure subscription
        List<OrderLineItem> originalOrderLineItems = restructureOrderInput.getLineItems();
        List<OrderLineItem> restructureOrderLineItems = originalOrderLineItems
            .stream()
            .filter(ol -> (ol.getAction() == ActionType.RESTRUCTURE || (ol.getAction() == ActionType.ADD)))
            .toList();
        restructureOrderInput.setLineItems(restructureOrderLineItems);

        if (firstTimeSave) {
            return saveNewCancelAndRestructureOrder(cancelAndRestructureRequest, restructureOrderInput, tenantId, dslContext);
        }

        String cancelledOrderId = null;
        if (CollectionUtils.isNotEmpty(ordersInCompositeOrder)) {
            Optional<Order> existingCancelledOrder = ordersInCompositeOrder
                .stream()
                .filter(order -> order.getOrderType() == OrderType.CANCEL)
                .findAny();
            if (existingCancelledOrder.isEmpty()) {
                throw new IllegalStateException(String.format("Cancel order not found in Composite Order: %s", compositeOrderId));
            }
            cancelledOrderId = existingCancelledOrder.get().getOrderId();
        }

        List<Order> orders = new ArrayList<>();
        Order cancelledOrder = getCancelledOrder(restructureOrderInput, cancelledOrderId, isDryRun);
        orders.add(cancelledOrder);
        Order restructuredOrder = orderService.upsertOrder(restructureOrderInput, isDryRun, false);
        orders.add(restructuredOrder);

        if (!isDryRun) {
            //update composite order
            CompositeOrder updatedCompositeOrder = updateCompositeOrderFields(compositeOrderId, cancelAndRestructureRequest);
            opportunityService.upsertOpportunityForCompositeOrderIfNeeded(
                dslContext,
                updatedCompositeOrder,
                tenantId,
                restructureOrderInput.getAccountId()
            );
            salesforceJobQueueService.dispatchCompositeOrderSync(
                compositeOrderId,
                restructuredOrder.getAccountId(),
                tenantIdProvider.provideTenantIdString()
            );
            hubSpotJobQueueService.dispatchHubSpotCompositeOrderSyncTenantJob(
                compositeOrderId,
                restructuredOrder.getAccountId(),
                tenantIdProvider.provideTenantIdString()
            );
        }
        return orders;
    }

    private void validateAccount(CancelAndRestructureRequest cancelAndRestructureRequest, Subscription subscription) {
        if (
            StringUtils.isBlank(cancelAndRestructureRequest.getAccountId()) ||
            !Strings.CS.equals(cancelAndRestructureRequest.getAccountId(), subscription.getAccountId())
        ) {
            throw new InvalidInputException("Account can't be changed during cancel and restructure of a subscription");
        }
    }

    private static void validateCurrency(CancelAndRestructureRequest cancelAndRestructureRequest, Subscription subscription) {
        if (StringUtils.isBlank(cancelAndRestructureRequest.getCurrency())) {
            cancelAndRestructureRequest.setCurrency(subscription.getCurrency().getCurrencyCode());
        } else if (!cancelAndRestructureRequest.getCurrency().equals(subscription.getCurrency().getCurrencyCode())) {
            throw new InvalidInputException("Currency can't be changed during cancel and restructure of a subscription");
        }
    }

    private List<Order> saveNewCancelAndRestructureOrder(
        CancelAndRestructureRequest cancelAndRestructureRequest,
        Order restructureOrderInput,
        String tenantId,
        DSLContext dslContext
    ) {
        List<Order> orders = new ArrayList<>();
        String compositeOrderId = compositeOrderIdGenerator.generate();

        // set input entity based on subscription
        var subscription = subscriptionGetService.getSubscription(cancelAndRestructureRequest.getSubscriptionId());
        restructureOrderInput.setEntityId(subscription.getEntityId());

        restructureOrderInput.setCompositeOrderId(compositeOrderId);
        //create restructure order
        Order restructuredOrder = orderService.upsertOrder(restructureOrderInput, false, false);
        //create cancel order
        Order cancelledOrder = getCancelledOrder(restructureOrderInput, null, false);
        //create composite order
        CompositeOrder compositeOrder = addCancelAndRestructureOrder(compositeOrderId, cancelAndRestructureRequest);

        orders.add(restructuredOrder);
        orders.add(cancelledOrder);

        // salesforce sync
        opportunityService.upsertOpportunityForCompositeOrderIfNeeded(
            dslContext,
            compositeOrder,
            tenantId,
            cancelAndRestructureRequest.getAccountId()
        );
        salesforceJobQueueService.dispatchCompositeOrderSync(compositeOrderId, restructuredOrder.getAccountId(), tenantId);
        hubSpotJobQueueService.dispatchHubSpotCompositeOrderSyncTenantJob(compositeOrderId, restructuredOrder.getAccountId(), tenantId);
        return orders;
    }

    private Order getCancelledOrder(Order restructureOrderInput, String existingCancelOrderId, boolean isDryRun) {
        Order draftCancellation = new Order();
        draftCancellation.setExternalSubscriptionId(restructureOrderInput.getRestructureForSubscriptionId());
        draftCancellation.setShippingContactId(restructureOrderInput.getShippingContactId());
        draftCancellation.setBillingContactId(restructureOrderInput.getBillingContactId());

        draftCancellation.setStartDate(restructureOrderInput.getStartDate());
        draftCancellation.setOrderType(OrderType.CANCEL);

        draftCancellation.setAutoRenew(false);
        draftCancellation.setOwnerId(restructureOrderInput.getOwnerId());

        draftCancellation.setCompositeOrderId(restructureOrderInput.getCompositeOrderId());
        draftCancellation.setOrderId(existingCancelOrderId);
        draftCancellation.setApprovalSegmentId(restructureOrderInput.getApprovalSegmentId());

        draftCancellation.setCustomFields(restructureOrderInput.getCustomFields());

        return changeOrderService.upsertChangeOrder(draftCancellation, isDryRun, false);
    }

    private static void validateCancelAndRestructure(Order order) {
        if (order.getOrderType() != OrderType.RESTRUCTURE) {
            String message = "Only RESTRUCTURE order type is supported using upsertCancelAndRestructure. Given OrderType: " + order.getOrderType();
            throw new InvalidInputException(message);
        }
    }

    private void validateCancelAndRestructureDate(Order restructureOrderInput, Subscription subscription, TimeZone timeZone) {
        Instant startDate = DateTimeConverter.getStartOfCurrentDay(restructureOrderInput.getStartDate(), timeZone);
        //Restructure date should be greater than or equal to current subscription start date
        //Restructure date should be less than or equal to current subscription end date
        if (startDate.isBefore(subscription.getStartDate()) || startDate.isAfter(subscription.getEndDate())) {
            String message =
                "Restructure start date should be greater than or equal to current subscription start date and less than or equal to current subscription end date";
            throw new InvalidInputException(message);
        }
    }

    private Instant generateRestructureStartDate(Subscription subscription) {
        Optional<Instant> latestUsageInvoicedDate = OrderServiceHelper.getLatestUsageInvoiceForSubscription(
            subscription,
            invoiceService,
            OrderServiceHelper.createChargeMap(null, subscription, productCatalogGetService)
        );

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Instant restructureDate = DateTimeConverter.getStartOfCurrentDay(Instant.now(), timeZone);

        if (latestUsageInvoicedDate.isPresent() && latestUsageInvoicedDate.get().isAfter(restructureDate)) {
            // if there are usage invoices and the latest end date is after the default restructure date, use the latest usage invoice date instead
            return latestUsageInvoicedDate.get();
        }

        if (restructureDate.isBefore(subscription.getStartDate()) || restructureDate.isAfter(subscription.getEndDate())) {
            restructureDate = DateTimeConverter.getStartOfCurrentDay(subscription.getStartDate(), timeZone);
        }
        return restructureDate;
    }

    private CompositeOrder addCancelAndRestructureOrder(String compositeOrderId, CancelAndRestructureRequest cancelAndRestructureRequest) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var subscription = subscriptionGetService.getSubscription(cancelAndRestructureRequest.getSubscriptionId());
        String entityId = subscription.getEntityId();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        CompositeOrder draftCompositeOrder = ImmutableCompositeOrder.builder()
            .compositeOrderId(compositeOrderId)
            .entityId(entityId)
            .type(CompositeOrderType.CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE)
            .status(OrderStatus.DRAFT)
            .crmOpportunityId(cancelAndRestructureRequest.getCrmOpportunityId())
            .crmOpportunityName(cancelAndRestructureRequest.getCrmOpportunityName())
            .crmOpportunityStage(cancelAndRestructureRequest.getCrmOpportunityStage())
            .crmOpportunityType(cancelAndRestructureRequest.getCrmOpportunityType())
            .build();
        compositeOrderDAO.addCompositeOrder(dslContext, draftCompositeOrder);
        return draftCompositeOrder;
    }

    public CompositeOrder updateCompositeOrderFields(String compositeOrderId, CancelAndRestructureRequest cancelAndRestructureRequest) {
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        UUID masterTemplateUUID = StringUtils.isBlank(cancelAndRestructureRequest.getDocumentMasterTemplateId())
            ? null
            : UUID.fromString(cancelAndRestructureRequest.getDocumentMasterTemplateId());
        CompositeOrder updatedCompositeOrder = ImmutableCompositeOrder.builder()
            .from(compositeOrder)
            .crmOpportunityId(cancelAndRestructureRequest.getCrmOpportunityId())
            .crmOpportunityName(cancelAndRestructureRequest.getCrmOpportunityName())
            .crmOpportunityStage(cancelAndRestructureRequest.getCrmOpportunityStage())
            .crmOpportunityType(cancelAndRestructureRequest.getCrmOpportunityType())
            .documentMasterTemplateId(masterTemplateUUID)
            .updatedOn(Instant.now())
            .build();
        compositeOrderDAO.updateCompositeOrder(updatedCompositeOrder);
        return updatedCompositeOrder;
    }

    public void deleteCompositeOrder(String compositeOrderId) {
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        switch (compositeOrder.getType()) {
            case UPSELL_AND_EARLY_RENEWAL -> {
                //check feature is enabled
                checkFeature();
                processCompositeOrderDeletion(compositeOrder);
            }
            case CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE -> {
                //check feature is enabled
                checkCancelSingleSubscriptionAndRestructureFeature();
                processCompositeOrderDeletion(compositeOrder);
            }
        }
    }

    private void processCompositeOrderDeletion(CompositeOrder compositeOrder) {
        List<Order> ordersInCompositeOrder = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrder.getCompositeOrderId());
        Validator.validateCollectionNotEmpty(ordersInCompositeOrder, "ordersInCompositeOrder");

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        List<Order> mutableCompositeOrdersList = new ArrayList<>(ordersInCompositeOrder);
        tenantDslContext.transaction(configuration -> {
            DSLContext transactionalDslContext = DSL.using(configuration);
            tryAcquireAdvisoryLock(compositeOrder.getCompositeOrderId(), transactionalDslContext);
            mutableCompositeOrdersList.forEach(order -> orderService.deleteOrderInTransaction(order, transactionalDslContext));
            deleteCompositeOrderInTransaction(transactionalDslContext, compositeOrder);
        });
        deleteCompositeOrderPostTransactionEvents(
            compositeOrder,
            ordersInCompositeOrder.get(0).getTenantId(),
            ordersInCompositeOrder.get(0).getAccountId()
        );
    }

    public void deleteCompositeOrderInTransaction(DSLContext transactionalDslContext, CompositeOrder compositeOrder) {
        compositeOrderDAO.deleteCompositeOrder(compositeOrder.getCompositeOrderId(), transactionalDslContext);
    }

    public void deleteCompositeOrderPostTransactionEvents(CompositeOrder compositeOrder, String tenantId, String accountId) {
        salesforceJobQueueService.dispatchCompositeOrderDeletionSync(compositeOrder, tenantId);
        hubSpotJobQueueService.dispatchCompositeOrderDeletionSync(compositeOrder, tenantId, accountId);
    }

    public void updateCompositeOrderCrmDetails(
        DSLContext dslContext,
        String compositeOrderId,
        String opportunityId,
        String opportunityName,
        String opportunityStage,
        String opportunityType
    ) {
        compositeOrderDAO.updateCompositeOrderCrmDetails(
            dslContext,
            compositeOrderId,
            opportunityId,
            opportunityName,
            opportunityStage,
            opportunityType
        );
    }

    // Deletes the composite order and detach the individual orders from it.
    // e.g. cancel restructure order -> individual cancel and restructure orders
    public void splitCompositeOrder(Configuration configuration, String compositeOrderId) {
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        List<Order> ordersInCompositeOrder = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrder.getCompositeOrderId());
        Validator.validateCollectionNotEmpty(ordersInCompositeOrder, "ordersInCompositeOrder");

        DSLContext dslContext = DSL.using(configuration);
        tryAcquireAdvisoryLock(compositeOrder.getCompositeOrderId(), dslContext);
        deleteCompositeOrderInTransaction(dslContext, compositeOrder);
        ordersInCompositeOrder.forEach(order -> orderService.removeCompositeOrderAssociation(dslContext, order.getOrderId()));
    }
}
