package com.subskribe.billy.opportunity.service;

import static com.subskribe.billy.opportunity.OpportunityProductMetadata.OPPORTUNITY_CORE;

import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.auth.authorizers.JobOnly;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.crm.CrmType;
import com.subskribe.billy.crm.model.CrmOpportunityNameChangeNotificationRequest;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.exception.DuplicateObjectException;
import com.subskribe.billy.hubspot.service.HubSpotGetService;
import com.subskribe.billy.opportunity.db.OpportunityDAO;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.salesforce.service.SalesforceGetService;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

public class OpportunityService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpportunityService.class);

    private static final String INVALID_OPPORTUNITY_ID_PREFIX = "TEST";

    private final OpportunityGetService opportunityGetService;
    private final OrderGetService orderGetService;
    private final OpportunityDAO opportunityDAO;
    private final TenantIdProvider tenantIdProvider;
    private final EntityContextResolver entityContextResolver;
    private final DSLContextProvider dslContextProvider;
    private final AccountGetService accountGetService;
    private final CompositeOrderGetService compositeOrderGetService;
    private final CustomFieldService customFieldService;
    private final HubSpotGetService hubSpotGetService;
    private final SalesforceGetService salesforceGetService;

    @Inject
    public OpportunityService(
        OpportunityGetService opportunityGetService,
        OrderGetService orderGetService,
        OpportunityDAO opportunityDAO,
        TenantIdProvider tenantIdProvider,
        EntityContextResolver entityContextResolver,
        DSLContextProvider dslContextProvider,
        AccountGetService accountGetService,
        CompositeOrderGetService compositeOrderGetService,
        CustomFieldService customFieldService,
        HubSpotGetService hubSpotGetService,
        SalesforceGetService salesforceGetService
    ) {
        this.opportunityGetService = opportunityGetService;
        this.orderGetService = orderGetService;
        this.opportunityDAO = opportunityDAO;
        this.tenantIdProvider = tenantIdProvider;
        this.entityContextResolver = entityContextResolver;
        this.dslContextProvider = dslContextProvider;
        this.accountGetService = accountGetService;
        this.compositeOrderGetService = compositeOrderGetService;
        this.customFieldService = customFieldService;
        this.hubSpotGetService = hubSpotGetService;
        this.salesforceGetService = salesforceGetService;
    }

    public Optional<Opportunity> upsertOpportunityForOrderIfNeeded(
        DSLContext dslContext,
        Order order,
        String tenantId,
        boolean fetchCustomFieldsFromCrm
    ) {
        String crmOpportunityId = getCrmOpportunityId(order);
        if (StringUtils.isBlank(crmOpportunityId)) {
            return Optional.empty();
        }

        Optional<Opportunity> existingOpportunity = opportunityDAO.getOpportunityByCrmOpportunityId(crmOpportunityId, tenantId);
        if (existingOpportunity.isEmpty()) {
            order.setIsPrimaryOrderForSfdcOpportunity(true);
            Opportunity opportunity = opportunityDAO.addOpportunityForOrder(dslContext, order, tenantId);
            if (fetchCustomFieldsFromCrm) {
                Map<String, String> crmCustomFields = getCustomFieldsForOpportunityFromCrm(opportunity);
                setOpportunityCustomFields(opportunity.getOpportunityId(), crmCustomFields);
            }
            return Optional.of(opportunity);
        }

        if (needToUpdateOpportunity(order, existingOpportunity.get())) {
            String primaryOrderId = (order.getIsPrimaryOrderForSfdcOpportunity() || (existingOpportunity.get().getPrimaryOrderId() == null))
                ? order.getOrderId()
                : existingOpportunity.get().getPrimaryOrderId();
            Opportunity opportunity = opportunityDAO.updateOpportunityForOrder(dslContext, order, tenantId, primaryOrderId);
            return Optional.of(opportunity);
        }

        return existingOpportunity;
    }

    private String getCrmOpportunityId(Order order) {
        //TODO once UI is fully migrated to use opportunity object, remove this logic
        if (order.getOpportunity().isPresent()) {
            return order.getOpportunity().get().getCrmId();
        }
        return order.getSfdcOpportunityId();
    }

    private void setOpportunityCustomFields(String opportunityId, Map<String, String> crmCustomFields) {
        if (crmCustomFields.isEmpty()) {
            return;
        }

        CustomField opportunityCustomField = customFieldService.getCustomFields(CustomFieldParentType.OPPORTUNITY, opportunityId);
        Map<String, CustomFieldValue> updateOpportunityCustomFieldEntries = new HashMap<>();
        opportunityCustomField
            .getEntries()
            .forEach((customFieldId, customFieldValue) -> {
                String customFieldName = customFieldValue.getName();
                String sourceValue = crmCustomFields.get(customFieldName);

                // Check that CRM custom field value is not null and Subskribe has the custom field
                if (sourceValue != null) {
                    String updatedValue = sourceValue;
                    List<String> updatedSelections =
                        switch (customFieldValue.getType()) {
                            case STRING -> List.of();
                            case PICKLIST -> List.of(sourceValue);
                            case MULTISELECT_PICKLIST -> Arrays.stream(sourceValue.split(";")).map(String::trim).toList();
                        };

                    // Check that the selections are a subset of the available custom field options
                    // If the selections are not a subset, then use the original custom field value and selections
                    if (!new HashSet<>(customFieldValue.getOptions()).containsAll(updatedSelections)) {
                        updatedValue = customFieldValue.getValue();
                        updatedSelections = customFieldValue.getSelections();
                    }
                    CustomFieldValue updatedOpportunityCustomField = new CustomFieldValue(
                        customFieldValue.getType(),
                        customFieldValue.getName(),
                        customFieldValue.getLabel(),
                        updatedValue,
                        updatedSelections,
                        customFieldValue.getOptions(),
                        customFieldValue.isRequired(),
                        customFieldValue.getSource(),
                        customFieldValue.getDefaultValue()
                    );
                    updateOpportunityCustomFieldEntries.put(customFieldId, updatedOpportunityCustomField);
                } else {
                    updateOpportunityCustomFieldEntries.put(customFieldId, customFieldValue);
                }
            });

        customFieldService.setCustomFieldsBySystem(
            CustomFieldParentType.OPPORTUNITY,
            opportunityId,
            new CustomField(updateOpportunityCustomFieldEntries)
        );
    }

    private Map<String, String> getCustomFieldsForOpportunityFromCrm(Opportunity opportunity) {
        CrmType crmType = CrmType.fromCrmId(opportunity.getCrmId());
        if (crmType == null) {
            return Map.of();
        }

        return switch (crmType) {
            case SALESFORCE -> salesforceGetService.getCustomFieldsForOpportunity(opportunity.getCrmId());
            case HUBSPOT -> hubSpotGetService.getCustomFieldsForDeal(opportunity.getCrmId());
        };
    }

    private boolean needToUpdateOpportunity(Order order, Opportunity existingOpportunity) {
        if (existingOpportunity.getPrimaryOrderId() == null) {
            return true;
        }

        String opportunityName = order.getOpportunity().isPresent() ? order.getOpportunity().get().getName() : order.getSfdcOpportunityName();
        if (opportunityName != null && !opportunityName.equals(existingOpportunity.getName())) {
            return true;
        }

        String opportunityStage = order.getOpportunity().isPresent() ? order.getOpportunity().get().getStage() : order.getSfdcOpportunityStage();
        if (opportunityStage != null && !opportunityStage.equals(existingOpportunity.getStage())) {
            return true;
        }

        if (order.getIsPrimaryOrderForSfdcOpportunity() && !order.getOrderId().equals(existingOpportunity.getPrimaryOrderId())) {
            return true;
        }

        String opportunityType = order.getOpportunity().isPresent() ? order.getOpportunity().get().getType() : order.getSfdcOpportunityType();
        return opportunityType != null && !opportunityType.equals(existingOpportunity.getType());
    }

    public void upsertOpportunityForCompositeOrderIfNeeded(DSLContext dslContext, CompositeOrder compositeOrder, String tenantId, String accountId) {
        if (StringUtils.isBlank(compositeOrder.getCrmOpportunityId())) {
            return;
        }

        opportunityDAO.upsertOpportunityForCompositeOrderIfNeeded(dslContext, compositeOrder, tenantId, accountId);
    }

    //return false if opportunity is closed, else close it and return true
    private boolean compareAndSwapOpportunityClosedState(String crmOpportunityId) {
        var dslContext = dslContextProvider.get(tenantIdProvider.provideTenantIdString());
        return dslContext.transactionResult(configuration -> {
            var dsl = DSL.using(configuration);
            if (opportunityGetService.isOpportunityClosed(dsl, crmOpportunityId)) {
                return false;
            }
            setOpportunityAsClosed(dsl, crmOpportunityId);
            return true;
        });
    }

    public void setOpportunityAsClosed(DSLContext dslContext, String crmOpportunityId) {
        opportunityDAO.setOpportunityAsClosed(dslContext, crmOpportunityId);
    }

    public Opportunity resetOpportunityClosedState(String opportunityId) {
        DSLContext dslContext = dslContextProvider.get(tenantIdProvider.provideTenantIdString());
        return opportunityDAO.resetOpportunityClosedState(dslContext, opportunityId);
    }

    public void resetOpportunityClosedState(Configuration configuration, String crmOpportunityId, String orderId) {
        DSLContext dslContext = DSL.using(configuration);
        Optional<Opportunity> opportunity = opportunityDAO.getOpportunityByCrmOpportunityId(
            dslContext,
            crmOpportunityId,
            tenantIdProvider.provideTenantIdString()
        );
        if (opportunity.isEmpty()) {
            LOGGER.warn(OPPORTUNITY_CORE, String.format("Opportunity with id: %s not found", crmOpportunityId));
            return;
        }

        if (!opportunity.get().getIsClosed()) {
            LOGGER.info("Opportunity with id {} is not closed", crmOpportunityId);
            return;
        }

        if (opportunity.get().getPrimaryOrderId() != null && opportunity.get().getPrimaryOrderId().equals(orderId)) {
            opportunityDAO.resetOpportunityClosedState(dslContext, crmOpportunityId);
        } else {
            String message = String.format(
                "Opportunity with id %s is closed, but the order with id %s is not the primary order for the opportunity",
                crmOpportunityId,
                orderId
            );
            LOGGER.warn(OPPORTUNITY_CORE, message);
        }
    }

    private void updatePrimaryOrderForOpportunity(Order order) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(order.getTenantId(), dslContextProvider);
        opportunityDAO.updatePrimaryOrderForOpportunity(dslContext, order.getSfdcOpportunityId(), order.getTenantId(), order.getOrderId());
    }

    private void updatePrimaryOrderForOpportunity(DSLContext dslContext, String opportunityId, String tenantId, String primaryOrderId) {
        opportunityDAO.updatePrimaryOrderForOpportunity(dslContext, opportunityId, tenantId, primaryOrderId);
    }

    public void updatePrimaryOrderIdForOpportunity(Order order) {
        Validator.validateNonNullArguments(order, order.getSfdcOpportunityId());
        if (order.getIsPrimaryOrderForSfdcOpportunity()) {
            return;
        }
        updatePrimaryOrderForOpportunity(order);
        order.setIsPrimaryOrderForSfdcOpportunity(true);
    }

    @JobOnly
    public void updateAccountIdForOpportunity(Opportunity opportunity, String accountId) {
        Validator.validateNonNullArguments(opportunity, accountId);
        opportunityDAO.updateAccountIdForOpportunity(opportunity, accountId);
    }

    @JobOnly
    public void backfillOpportunityId(Opportunity opportunity) {
        opportunityDAO.backfillOpportunityId(opportunity);
    }

    // TODO: Add advisory lock for opportunity
    public void closeOpportunityAndThrowIfAlreadyClosed(String crmOpportunityId) {
        if (crmOpportunityId.startsWith(INVALID_OPPORTUNITY_ID_PREFIX)) {
            return;
        }

        boolean isOpportunityClosed = !compareAndSwapOpportunityClosedState(crmOpportunityId);
        logAndThrowIfOpportunityClosed(crmOpportunityId, isOpportunityClosed);
    }

    private static void logAndThrowIfOpportunityClosed(String opportunityId, boolean isOpportunityClosed) {
        if (isOpportunityClosed) {
            LOGGER.warn(OPPORTUNITY_CORE, String.format("Order is being executed while the SFDC opportunity: %s is closed.", opportunityId));
            throw new IllegalStateException("Order cannot be executed. The SFDC opportunity is already closed: " + opportunityId);
        }
    }

    public void deleteOrphanOpportunityByCrmId(String opportunityCrmId) {
        Opportunity opportunity = opportunityGetService.getOpportunityByCrmOpportunityId(opportunityCrmId);
        opportunityDAO.deleteOpportunity(opportunity.getOpportunityId());
    }

    // delete an opportunity if it is not referenced by any order
    public void deleteOrphanOpportunityByCrmIdIfNeeded(String opportunityCrmId) {
        Opportunity opportunity = opportunityGetService.getOpportunityByCrmOpportunityId(opportunityCrmId);
        List<Order> linkedOrders = orderGetService.getOrdersByCrmOpportunityId(opportunityCrmId);
        List<CompositeOrder> linkedCompositeOrders = compositeOrderGetService.getCompositeOrdersByCrmOpportunityId(opportunityCrmId);
        if (CollectionUtils.isEmpty(linkedOrders) && CollectionUtils.isEmpty(linkedCompositeOrders)) {
            opportunityDAO.deleteOpportunity(opportunity.getOpportunityId());
        }
    }

    public Opportunity addOpportunity(Opportunity opportunity) {
        Validator.validateNonNullArgument(opportunity);
        Validator.validateNonNullArgument(opportunity.getCrmId(), "crmId");
        Validator.validateNonNullArgument(opportunity.getOpportunityCrmType(), "opportunityCrmType");
        verifyAccountExists(opportunity.getAccountId());
        if (opportunity.getIsClosed() == null) {
            opportunity.setIsClosed(false);
        }
        String tenantId = tenantIdProvider.provideTenantIdString();
        String entityId = entityContextResolver.resolveInputEntityIdForIsolatedObject(opportunity.getEntityId());
        opportunity.setEntityId(entityId);
        Optional<Opportunity> existingOpportunity = opportunityDAO.getOpportunityByCrmOpportunityId(opportunity.getCrmId(), tenantId);
        if (existingOpportunity.isPresent()) {
            throw new DuplicateObjectException("Opportunity with this crmId already exists: " + opportunity.getCrmId());
        }
        return opportunityDAO.addOpportunity(opportunity);
    }

    public Opportunity updateOpportunity(Opportunity opportunity) {
        Validator.validateNonNullArgument(opportunity);
        Validator.validateNonNullArgument(opportunity.getOpportunityId());
        verifyAccountExists(opportunity.getAccountId());
        if (opportunity.getIsClosed() == null) {
            opportunity.setIsClosed(false);
        }

        return opportunityDAO.updateOpportunity(opportunity);
    }

    private void verifyAccountExists(String accountId) {
        Validator.validateNonNullArgument(accountId);
        accountGetService.getAccount(accountId);
    }

    public void updatePrimaryCompositeOrderIdForCrmOpportunity(CompositeOrder compositeOrder, Order order) {
        Validator.validateNonNullArguments(compositeOrder, compositeOrder.getCrmOpportunityId());
        if (BooleanUtils.isTrue(compositeOrder.getIsPrimaryCompositeOrderForCrmOpportunity())) {
            return;
        }
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(order.getTenantId(), dslContextProvider);
        updatePrimaryOrderForOpportunity(dslContext, compositeOrder.getCrmOpportunityId(), order.getTenantId(), compositeOrder.getCompositeOrderId());
    }

    public void processOpportunityNameChangeNotification(
        String opportunityCrmId,
        CrmOpportunityNameChangeNotificationRequest crmOpportunityNameChangeNotificationRequest
    ) {
        if (crmOpportunityNameChangeNotificationRequest == null || crmOpportunityNameChangeNotificationRequest.getNewName() == null) {
            LOGGER.warn(OPPORTUNITY_CORE, String.format("Opportunity name change request or new name field is null for id: %s", opportunityCrmId));
            return;
        }

        Opportunity opportunity = opportunityGetService.getOpportunityByCrmOpportunityId(opportunityCrmId);
        if (opportunity == null) {
            LOGGER.warn(OPPORTUNITY_CORE, String.format("Opportunity with id: %s not found", opportunityCrmId));
            return;
        }

        Optional<String> maybeOldName = crmOpportunityNameChangeNotificationRequest.getOldName();
        if (maybeOldName.isPresent() && !opportunity.getName().equals(maybeOldName.get())) {
            LOGGER.warn(
                OPPORTUNITY_CORE,
                String.format(
                    "Name mismatch for opportunity id: %s. DB name: %s does not match old name: %s in the request",
                    opportunityCrmId,
                    opportunity.getName(),
                    maybeOldName.get()
                )
            );
            return;
        }
        opportunityDAO.updateNameForOpportunity(
            opportunityCrmId,
            crmOpportunityNameChangeNotificationRequest.getNewName(),
            crmOpportunityNameChangeNotificationRequest.getOldName().orElse(null)
        );
    }
}
