package com.subskribe.billy.opportunity.db;

import static com.subskribe.billy.jooq.default_schema.tables.Opportunity.OPPORTUNITY;
import static com.subskribe.billy.opportunity.OpportunityProductMetadata.OPPORTUNITY_CORE;

import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.jooq.default_schema.tables.records.OpportunityRecord;
import com.subskribe.billy.opportunity.mapper.OpportunityRecordMapper;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityIdGenerator;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.inject.Inject;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class OpportunityDAO {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpportunityDAO.class);
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final OpportunityRecordMapper opportunityRecordMapper;
    private final OpportunityIdGenerator opportunityIdGenerator;

    @Inject
    public OpportunityDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider, OpportunityIdGenerator opportunityIdGenerator) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.opportunityIdGenerator = opportunityIdGenerator;
        opportunityRecordMapper = Mappers.getMapper(OpportunityRecordMapper.class);
    }

    public Optional<Opportunity> getOpportunityByCrmOpportunityId(String crmOpportunityId, String tenantId) {
        var dslContext = dslContextProvider.get(tenantId);
        return getOpportunityByCrmOpportunityId(dslContext, crmOpportunityId, tenantId);
    }

    public Optional<Opportunity> getOpportunityByCrmOpportunityId(DSLContext dslContext, String crmOpportunityId, String tenantId) {
        OpportunityRecord record = dslContext
            .select()
            .from(OPPORTUNITY)
            .where(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.CRM_ID.eq(crmOpportunityId))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .fetchOneInto(OpportunityRecord.class);
        return Optional.ofNullable(opportunityRecordMapper.fromRecord(record));
    }

    public List<Opportunity> getOpportunitiesByOrderId(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<OpportunityRecord> records = dslContext
            .select()
            .from(OPPORTUNITY)
            .where(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.PRIMARY_ORDER_ID.eq(orderId))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .fetchInto(OpportunityRecord.class);
        return opportunityRecordMapper.fromRecords(records);
    }

    private Optional<Opportunity> getOpportunityByOpportunityId(String opportunityId, String tenantId) {
        DSLContext dslContext = dslContextProvider.get(tenantId);
        OpportunityRecord record = dslContext
            .select()
            .from(OPPORTUNITY)
            .where(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.OPPORTUNITY_ID.eq(opportunityId))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .fetchOneInto(OpportunityRecord.class);
        return Optional.ofNullable(opportunityRecordMapper.fromRecord(record));
    }

    public Optional<Opportunity> getOpportunityByOpportunityId(String opportunityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return getOpportunityByOpportunityId(opportunityId, tenantId);
    }

    public List<Opportunity> getOpportunities(PaginationQueryParams paginationQueryParams, String accountId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = dslContextProvider.get(tenantId);
        var query = dslContext
            .select()
            .from(OPPORTUNITY)
            .where(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.ACCOUNT_ID.eq(accountId))
            .and(OPPORTUNITY.IS_DELETED.isFalse());
        var records = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            OPPORTUNITY,
            OPPORTUNITY.ID,
            OPPORTUNITY.TENANT_ID,
            OPPORTUNITY.CREATED_ON,
            OpportunityRecord.class
        );
        return opportunityRecordMapper.fromRecords(records);
    }

    public Map<String, Opportunity> getIdOpportunityMap(Set<String> opportunityIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<OpportunityRecord> records = dslContext
            .select()
            .from(OPPORTUNITY)
            .where(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.CRM_ID.in(opportunityIds))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .fetchInto(OpportunityRecord.class);

        List<Opportunity> opportunities = opportunityRecordMapper.fromRecords(records);
        return opportunities.stream().collect(Collectors.toMap(Opportunity::getCrmId, Function.identity()));
    }

    public Opportunity addOpportunityForOrder(DSLContext dslContext, Order order, String tenantId) {
        OpportunityRecord record = getOpportunityRecord(order);
        record.setOpportunityId(opportunityIdGenerator.generate());
        record.setTenantId(tenantId);
        record.reset(OPPORTUNITY.ID);
        if (record.getIsClosed() == null) {
            record.setIsClosed(false);
        }
        // If the opportunity is coming from the CRM through the order, it does not have context primary order id
        // Therefore, we must check and set the primary order id here
        if (order.getIsPrimaryOrderForSfdcOpportunity()) {
            record.setPrimaryOrderId(order.getOrderId());
        }
        return opportunityRecordMapper.fromRecord(dslContext.insertInto(OPPORTUNITY).set(record).returning().fetchOne());
    }

    private OpportunityRecord getOpportunityRecord(Order order) {
        //TODO once UI is fully migrated to use opportunity object, remove this logic
        OpportunityRecord record;
        if (order.getOpportunity().isPresent()) {
            record = opportunityRecordMapper.toRecord(order.getOpportunity().get());
            record.setAccountId(order.getAccountId());
            record.setEntityId(order.getEntityId());
        } else {
            Validator.validateStringNotBlank(order.getSfdcOpportunityId(), "opportunity id is required.");
            Validator.validateStringNotBlank(order.getSfdcOpportunityName(), "opportunity name is required.");
            record = opportunityRecordMapper.toRecord(order);
        }
        return record;
    }

    private OpportunityRecord addOpportunityForCompositeOrder(
        DSLContext dslContext,
        CompositeOrder compositeOrder,
        String tenantId,
        String accountId
    ) {
        Validator.validateStringNotBlank(compositeOrder.getCrmOpportunityId(), "opportunity id is required.");

        var record = opportunityRecordMapper.toRecord(compositeOrder);
        record.reset(OPPORTUNITY.ID);
        record.setOpportunityId(opportunityIdGenerator.generate());
        record.setTenantId(tenantId);
        record.setAccountId(accountId);
        return dslContext.insertInto(OPPORTUNITY).set(record).returning().fetchOne();
    }

    public Opportunity updateOpportunityForOrder(DSLContext dslContext, Order order, String tenantId, String primaryOrderId) {
        String crmOpportunityId, crmOpportunityName, crmOpportunityStage, crmOpportunityType;
        if (order.getOpportunity().isPresent()) {
            crmOpportunityId = order.getOpportunity().get().getCrmId();
            crmOpportunityName = order.getOpportunity().get().getName();
            crmOpportunityStage = order.getOpportunity().get().getStage();
            crmOpportunityType = order.getOpportunity().get().getType();
        } else {
            crmOpportunityId = order.getSfdcOpportunityId();
            crmOpportunityName = order.getSfdcOpportunityName();
            crmOpportunityStage = order.getSfdcOpportunityStage();
            crmOpportunityType = order.getSfdcOpportunityType();
        }
        Validator.validateStringNotBlank(crmOpportunityId, "opportunity id is required.");
        Validator.validateStringNotBlank(crmOpportunityName, "opportunity name is required.");

        return opportunityRecordMapper.fromRecord(
            dslContext
                .update(OPPORTUNITY)
                .set(OPPORTUNITY.STAGE, crmOpportunityStage)
                .set(OPPORTUNITY.TYPE, crmOpportunityType)
                .set(OPPORTUNITY.NAME, crmOpportunityName)
                .set(OPPORTUNITY.PRIMARY_ORDER_ID, primaryOrderId)
                .where(OPPORTUNITY.CRM_ID.eq(crmOpportunityId))
                .and(OPPORTUNITY.TENANT_ID.eq(tenantId))
                .and(OPPORTUNITY.IS_DELETED.isFalse())
                .returning()
                .fetchOne()
        );
    }

    public Opportunity upsertOpportunityForCompositeOrderIfNeeded(
        DSLContext dslContext,
        CompositeOrder compositeOrder,
        String tenantId,
        String accountId
    ) {
        Optional<Opportunity> existingOpportunity = getOpportunityByCrmOpportunityId(compositeOrder.getCrmOpportunityId(), tenantId);

        //TODO: add primary order logic, for now we assume that composite orders are always the primary
        return existingOpportunity.orElseGet(() ->
            opportunityRecordMapper.fromRecord(addOpportunityForCompositeOrder(dslContext, compositeOrder, tenantId, accountId))
        );
    }

    public void updatePrimaryOrderForOpportunity(DSLContext dslContext, String opportunityId, String tenantId, String primaryOrderId) {
        Validator.validateNonNullArgument(opportunityId);
        dslContext
            .update(OPPORTUNITY)
            .set(OPPORTUNITY.PRIMARY_ORDER_ID, primaryOrderId)
            .where(OPPORTUNITY.CRM_ID.eq(opportunityId))
            .and(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .execute();
    }

    public void setOpportunityAsClosed(DSLContext dslContext, String crmOpportunityId) {
        Validator.validateNonNullArgument(crmOpportunityId);
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .update(OPPORTUNITY)
            .set(OPPORTUNITY.IS_CLOSED, true)
            .where(OPPORTUNITY.CRM_ID.eq(crmOpportunityId))
            .and(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .execute();
    }

    public Opportunity resetOpportunityClosedState(DSLContext dslContext, String opportunityId) {
        Validator.validateNonNullArgument(opportunityId);
        String tenantId = tenantIdProvider.provideTenantIdString();
        OpportunityRecord result = dslContext
            .update(OPPORTUNITY)
            .set(OPPORTUNITY.IS_CLOSED, false)
            .where(OPPORTUNITY.CRM_ID.eq(opportunityId))
            .or(OPPORTUNITY.OPPORTUNITY_ID.eq(opportunityId))
            .and(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .returning()
            .fetchOne();
        return opportunityRecordMapper.fromRecord(result);
    }

    public List<Opportunity> getPageOfOpportunitiesForTenant(int limit) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<OpportunityRecord> records = dslContext
            .select()
            .from(OPPORTUNITY)
            .where(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .limit(limit)
            .fetchInto(OpportunityRecord.class);

        return opportunityRecordMapper.fromRecords(records);
    }

    public void updateAccountIdForOpportunity(Opportunity opportunity, String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        dslContext
            .update(OPPORTUNITY)
            .set(OPPORTUNITY.ACCOUNT_ID, accountId)
            .where(OPPORTUNITY.CRM_ID.eq(opportunity.getCrmId()))
            .and(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.ID.eq(opportunity.getId()))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .execute();
    }

    public void backfillOpportunityId(Opportunity opportunity) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        dslContext
            .update(OPPORTUNITY)
            .set(OPPORTUNITY.OPPORTUNITY_ID, opportunityIdGenerator.generate())
            .where(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.ID.eq(opportunity.getId()))
            .execute();
    }

    public void deleteOpportunity(String opportunityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        dslContext
            .update(OPPORTUNITY)
            .set(OPPORTUNITY.IS_DELETED, true)
            .where(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.OPPORTUNITY_ID.eq(opportunityId))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .execute();
    }

    public Opportunity addOpportunity(Opportunity opportunity) {
        OpportunityRecord record = opportunityRecordMapper.toRecord(opportunity);
        String tenantId = tenantIdProvider.provideTenantIdString();
        record.reset(OPPORTUNITY.ID);
        record.setTenantId(tenantId);
        record.setOpportunityId(opportunityIdGenerator.generate());
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        OpportunityRecord result = dslContext.insertInto(OPPORTUNITY).set(record).returning().fetchOne();
        return opportunityRecordMapper.fromRecord(result);
    }

    public Opportunity updateOpportunity(Opportunity opportunity) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        String opportunityId = opportunity.getOpportunityId();
        Optional<Opportunity> existingOpportunity = getOpportunityByOpportunityId(opportunityId, tenantId);
        if (existingOpportunity.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.OPPORTUNITY, opportunityId);
        }
        OpportunityRecord record = opportunityRecordMapper.toRecord(opportunity);
        record.setId(existingOpportunity.get().getId());
        record.setTenantId(tenantId);
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        OpportunityRecord result = dslContext
            .update(OPPORTUNITY)
            .set(record)
            .where(OPPORTUNITY.OPPORTUNITY_ID.eq(opportunityId))
            .and(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .returning()
            .fetchOne();
        return opportunityRecordMapper.fromRecord(result);
    }

    public List<Opportunity> getOpportunitiesByAccountId(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<OpportunityRecord> records = dslContext
            .select()
            .from(OPPORTUNITY)
            .where(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.ACCOUNT_ID.eq(accountId))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .fetchInto(OpportunityRecord.class);
        return opportunityRecordMapper.fromRecords(records);
    }

    public void updateNameForOpportunity(String opportunityCrmId, String newName, @Nullable String oldName) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var updateStep = dslContext
            .update(OPPORTUNITY)
            .set(OPPORTUNITY.NAME, newName)
            .where(OPPORTUNITY.CRM_ID.eq(opportunityCrmId))
            .and(OPPORTUNITY.TENANT_ID.eq(tenantId))
            .and(OPPORTUNITY.IS_DELETED.isFalse());

        if (oldName != null) {
            updateStep = updateStep.and(OPPORTUNITY.NAME.eq(oldName));
        }

        int rowsUpdated = updateStep.execute();

        if (rowsUpdated == 0) {
            LOGGER.warn(OPPORTUNITY_CORE, String.format("No opportunity updated for crmId: %s and tenant id: %s", opportunityCrmId, tenantId));
        }
    }
}
