package com.subskribe.billy.opportunity.service;

import static com.subskribe.billy.opportunity.OpportunityProductMetadata.OPPORTUNITY_CORE;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.authorizers.JobOnly;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.opportunity.db.OpportunityDAO;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;

public class OpportunityGetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpportunityGetService.class);

    private static final String INVALID_OPPORTUNITY_ID_PREFIX = "TEST";

    private final OpportunityDAO opportunityDAO;

    private final BillyConfiguration billyConfiguration;

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    private final CustomFieldService customFieldService;

    @Inject
    public OpportunityGetService(
        OpportunityDAO opportunityDAO,
        BillyConfiguration billyConfiguration,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        CustomFieldService customFieldService
    ) {
        this.opportunityDAO = opportunityDAO;
        this.billyConfiguration = billyConfiguration;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.customFieldService = customFieldService;
    }

    public List<Opportunity> getOpportunities(PaginationQueryParams paginationQueryParams, String accountId) {
        List<Opportunity> opportunities = opportunityDAO.getOpportunities(paginationQueryParams, accountId);
        return addCustomFieldsToOpportunities(opportunities);
    }

    public Opportunity getOpportunityByCrmOpportunityId(String crmOpportunityId) {
        return getOptionalOpportunityByCrmOpportunityId(crmOpportunityId).orElseThrow(() -> {
            LOGGER.info("Opportunity with id {} not found", crmOpportunityId);
            return new ObjectNotFoundException(BillyObjectType.OPPORTUNITY, crmOpportunityId);
        });
    }

    public Optional<Opportunity> getOptionalOpportunityByCrmOpportunityId(String crmOpportunityId) {
        Validator.validateStringNotBlank(crmOpportunityId, "crmOpportunityId not provided");
        String tenantId = tenantIdProvider.provideTenantIdString();
        return opportunityDAO.getOpportunityByCrmOpportunityId(crmOpportunityId, tenantId).map(this::addCustomFieldsToOpportunity);
    }

    public List<Opportunity> getOpportunitiesByOrderId(String orderId) {
        Validator.validateStringNotBlank(orderId, "orderId not provided");
        List<Opportunity> opportunities = opportunityDAO.getOpportunitiesByOrderId(orderId);
        return addCustomFieldsToOpportunities(opportunities);
    }

    public Opportunity getOpportunityByOpportunityId(String opportunityId) {
        Validator.validateStringNotBlank(opportunityId, "opportunityId not provided");
        Optional<Opportunity> optionalOpportunity = opportunityDAO.getOpportunityByOpportunityId(opportunityId);
        if (optionalOpportunity.isEmpty()) {
            LOGGER.info("Opportunity with id {} not found", opportunityId);
            throw new ObjectNotFoundException(BillyObjectType.OPPORTUNITY, opportunityId);
        }

        return addCustomFieldsToOpportunity(optionalOpportunity.get());
    }

    private Opportunity addCustomFieldsToOpportunity(Opportunity opportunity) {
        opportunity.setCustomFields(customFieldService.getCustomFields(CustomFieldParentType.OPPORTUNITY, opportunity.getOpportunityId()));
        return opportunity;
    }

    public void addOpportunitiesToOrders(List<Order> orders) {
        Set<String> allOpportunityIds = orders.stream().map(Order::getSfdcOpportunityId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(allOpportunityIds)) {
            return;
        }

        Map<String, Opportunity> opportunityIdMap = opportunityDAO.getIdOpportunityMap(allOpportunityIds);
        orders.forEach(o -> addOpportunityToOrder(o, opportunityIdMap));
    }

    private void addOpportunityToOrder(Order order, Map<String, Opportunity> opportunityIdMap) {
        if (StringUtils.isBlank(order.getSfdcOpportunityId())) {
            return;
        }

        if (order.getSfdcOpportunityId().startsWith(INVALID_OPPORTUNITY_ID_PREFIX) && !billyConfiguration.isLocalOrCi()) {
            return;
        }

        Opportunity opportunity = opportunityIdMap.get(order.getSfdcOpportunityId());
        if (opportunity == null) {
            LOGGER.info("Unable to get opportunity with id: {}", order.getSfdcOpportunityId());
            return;
        }

        //set custom fields for the opportunity
        addCustomFieldsToOpportunity(opportunity);

        order.setSfdcOpportunityName(opportunity.getName());
        order.setSfdcOpportunityStage(opportunity.getStage());
        order.setSfdcOpportunityType(opportunity.getType());
        boolean isPrimaryOrderForOpportunity = opportunity.getPrimaryOrderId() != null && opportunity.getPrimaryOrderId().equals(order.getOrderId());
        order.setIsPrimaryOrderForSfdcOpportunity(isPrimaryOrderForOpportunity);
        order.setOpportunity(opportunity);

        order.setSfdcOrderCanBeExecuted(BooleanUtils.isNotTrue(opportunity.getIsClosed()));
    }

    boolean isOpportunityClosed(DSLContext dslContext, String opportunityId) {
        Validator.checkArgumentNotBlankInternal(opportunityId, "opportunityId cannot be null");
        String tenantId = tenantIdProvider.provideTenantIdString();
        Optional<Opportunity> opportunity = opportunityDAO.getOpportunityByCrmOpportunityId(dslContext, opportunityId, tenantId);
        if (opportunity.isEmpty()) {
            throw new ServiceFailureException("Opportunity not found: " + opportunityId);
        }
        return opportunity.get().getIsClosed();
    }

    @JobOnly
    public List<Opportunity> getPageOfOpportunitiesForTenant(int limit) {
        if (limit <= 0) {
            throw new IllegalArgumentException("Limit is not positive: " + limit);
        }

        List<Opportunity> opportunities = opportunityDAO.getPageOfOpportunitiesForTenant(limit);
        return addCustomFieldsToOpportunities(opportunities);
    }

    public void throwIfOpportunityIsAlreadyClosed(String opportunityId) {
        if (opportunityId.startsWith(INVALID_OPPORTUNITY_ID_PREFIX)) {
            return;
        }

        boolean isOpportunityClosed = isOpportunityClosed(dslContextProvider.get(tenantIdProvider.provideTenantIdString()), opportunityId);
        logAndThrowIfOpportunityClosed(opportunityId, isOpportunityClosed);
    }

    private static void logAndThrowIfOpportunityClosed(String opportunityId, boolean isOpportunityClosed) {
        if (isOpportunityClosed) {
            LOGGER.warn(OPPORTUNITY_CORE, String.format("Order is being executed while the opportunity %s is closed.", opportunityId));
            throw new IllegalStateException("Order cannot be executed. The opportunity is already closed: " + opportunityId);
        }
    }

    public List<Opportunity> getOpportunitiesByAccountId(String accountId) {
        return opportunityDAO.getOpportunitiesByAccountId(accountId).stream().map(this::addCustomFieldsToOpportunity).toList();
    }

    private List<Opportunity> addCustomFieldsToOpportunities(List<Opportunity> opportunities) {
        if (CollectionUtils.isEmpty(opportunities)) {
            return opportunities;
        }

        return opportunities.stream().map(this::addCustomFieldsToOpportunity).toList();
    }
}
