package com.subskribe.billy.platformfeature.db;

import static com.subskribe.billy.jooq.default_schema.Indexes.INDEX_PLATFORM_FEATURE_ENTITY_TENANT_ID_PLATFORM_FEATURE_ID_ENT;
import static com.subskribe.billy.jooq.default_schema.Indexes.INDEX_PLATFORM_FEATURE_NAME;
import static com.subskribe.billy.jooq.default_schema.tables.PlatformFeature.PLATFORM_FEATURE;
import static com.subskribe.billy.jooq.default_schema.tables.PlatformFeatureEntity.PLATFORM_FEATURE_ENTITY;
import static com.subskribe.billy.shared.tenant.TenantDSLContextProvider.getTenantDslContext;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.PlatformFeatureEntityRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.PlatformFeatureRecord;
import com.subskribe.billy.platformfeature.model.EnabledPlatformFeature;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.model.PlatformFeatureStatus;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.jooq.DSLContext;

public class PlatformFeatureDAO {

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    private final EntityGetService entityGetService;

    private final EntityContextProvider entityContextProvider;

    @Inject
    public PlatformFeatureDAO(
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        EntityGetService entityGetService,
        EntityContextProvider entityContextProvider
    ) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.entityGetService = entityGetService;
        this.entityContextProvider = entityContextProvider;
    }

    public Optional<EnabledPlatformFeature> getFeatureEnablement(PlatformFeature platformFeature) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return getFeatureEnablement(platformFeature, tenantId);
    }

    public Optional<EnabledPlatformFeature> getFeatureEnablement(PlatformFeature platformFeature, String tenantId) {
        DSLContext dslContext = getTenantDslContext(tenantId, dslContextProvider);

        var feature = dslContext
            .selectFrom(PLATFORM_FEATURE)
            .where(PLATFORM_FEATURE.TENANT_ID.eq(tenantId))
            .and(PLATFORM_FEATURE.NAME.eq(platformFeature.name()))
            .and(PLATFORM_FEATURE.ENABLED.isTrue())
            .fetchOne();
        if (feature == null) {
            return Optional.empty();
        }

        EnabledPlatformFeature enabledPlatformFeature = EnabledPlatformFeature.builder()
            .platformFeature(platformFeature)
            .enabledByUserId(feature.getEnabledByUserId())
            .enabledAt(DateTimeConverter.localDateTimeToInstant(feature.getCreatedOn()))
            .build();
        return Optional.of(enabledPlatformFeature);
    }

    public boolean isFeatureReady(PlatformFeature platformFeature) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = getTenantDslContext(tenantId, dslContextProvider);

        PlatformFeatureRecord feature = dslContext
            .selectFrom(PLATFORM_FEATURE)
            .where(PLATFORM_FEATURE.TENANT_ID.eq(tenantId))
            .and(PLATFORM_FEATURE.NAME.eq(platformFeature.name()))
            .and(PLATFORM_FEATURE.ENABLED.isTrue())
            .fetchOne();
        if (feature == null) {
            return false;
        }

        // Entity RLS is applied here
        // In some cases, the consumer of this method might not be in an entity context (e.g. in a batch job)
        // For backward compatibility, if the feature is ready for any entity, we return true
        return dslContext.fetchExists(
            dslContext
                .selectFrom(PLATFORM_FEATURE_ENTITY)
                .where(PLATFORM_FEATURE_ENTITY.TENANT_ID.eq(tenantId))
                .and(PLATFORM_FEATURE_ENTITY.PLATFORM_FEATURE_ID.eq(feature.getId()))
                .and(PLATFORM_FEATURE_ENTITY.STATUS.eq(PlatformFeatureStatus.READY.name()))
        );
    }

    public Set<EnabledPlatformFeature> getEnabledFeatures() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return getEnabledFeatures(tenantId);
    }

    public Set<EnabledPlatformFeature> getEnabledFeatures(String tenantId) {
        DSLContext dslContext = getTenantDslContext(tenantId, dslContextProvider);

        return dslContext
            .selectFrom(PLATFORM_FEATURE)
            .where(PLATFORM_FEATURE.TENANT_ID.eq(tenantId))
            .and(PLATFORM_FEATURE.ENABLED.isTrue())
            .fetch()
            .stream()
            .map(feature ->
                EnabledPlatformFeature.builder()
                    .platformFeature(PlatformFeature.valueOf(feature.getName()))
                    .enabledByUserId(feature.getEnabledByUserId())
                    .enabledAt(DateTimeConverter.localDateTimeToInstant(feature.getCreatedOn()))
                    .build()
            )
            .collect(Collectors.toSet());
    }

    public EnabledPlatformFeature enablePlatformFeature(PlatformFeature platformFeature, String enabledByUserId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return enablePlatformFeatureForTenant(platformFeature, enabledByUserId, tenantId);
    }

    public void purgePlatformFeatureForTesting() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext context = getTenantDslContext(tenantId, dslContextProvider);
        context.deleteFrom(PLATFORM_FEATURE_ENTITY).where(PLATFORM_FEATURE_ENTITY.TENANT_ID.eq(tenantId)).execute();
        context.deleteFrom(PLATFORM_FEATURE).where(PLATFORM_FEATURE.TENANT_ID.eq(tenantId)).execute();
    }

    public EnabledPlatformFeature enablePlatformFeatureForTenant(PlatformFeature platformFeature, String enabledByUserId, String tenantId) {
        PlatformFeatureRecord record = new PlatformFeatureRecord();
        record.setTenantId(tenantId);
        record.setName(platformFeature.name());
        record.setEnabledByUserId(enabledByUserId);
        record.setEnabled(Boolean.TRUE);

        DSLContext context = getTenantDslContext(tenantId, dslContextProvider);
        PlatformFeatureRecord stored = PostgresErrorHandler.withConstraintAsConflict(
            () -> context.insertInto(PLATFORM_FEATURE).set(record).returning().fetchOne(),
            INDEX_PLATFORM_FEATURE_NAME.getName(),
            String.format("%s feature already enabled or in progress", platformFeature.name())
        );

        List<PlatformFeatureEntityRecord> entityRecords = entityGetService
            .getEntities()
            .stream()
            .map(entity -> {
                PlatformFeatureEntityRecord entityRecord = new PlatformFeatureEntityRecord();
                entityRecord.setTenantId(tenantId);
                entityRecord.setEntityId(entity.getEntityId());
                entityRecord.setPlatformFeatureId(stored.getId());
                entityRecord.setStatus(PlatformFeatureStatus.ENABLED.name());
                return entityRecord;
            })
            .toList();

        PostgresErrorHandler.withConstraintAsConflict(
            () -> context.batchInsert(entityRecords).execute(),
            INDEX_PLATFORM_FEATURE_ENTITY_TENANT_ID_PLATFORM_FEATURE_ID_ENT.getName(),
            String.format("Entities already have %s feature enabled", platformFeature.name())
        );

        return EnabledPlatformFeature.builder()
            .platformFeature(platformFeature)
            .enabledByUserId(stored.getEnabledByUserId())
            .enabledAt(DateTimeConverter.localDateTimeToInstant(stored.getCreatedOn()))
            .build();
    }

    public void movePlatformFeatureToReady(PlatformFeature platformFeature) {
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext context = getTenantDslContext(tenantId, dslContextProvider);

        PlatformFeatureRecord feature = context
            .selectFrom(PLATFORM_FEATURE)
            .where(PLATFORM_FEATURE.TENANT_ID.eq(tenantId))
            .and(PLATFORM_FEATURE.NAME.eq(platformFeature.name()))
            .and(PLATFORM_FEATURE.ENABLED.isTrue())
            .fetchOne();
        if (feature == null) {
            throw new IllegalStateException("Feature not enabled");
        }

        int result = context
            .update(PLATFORM_FEATURE_ENTITY)
            .set(PLATFORM_FEATURE_ENTITY.STATUS, PlatformFeatureStatus.READY.name())
            .where(PLATFORM_FEATURE_ENTITY.TENANT_ID.eq(tenantId))
            .and(PLATFORM_FEATURE_ENTITY.PLATFORM_FEATURE_ID.eq(feature.getId()))
            .execute();
        if (result == 0) {
            result = context
                .insertInto(PLATFORM_FEATURE_ENTITY)
                .set(PLATFORM_FEATURE_ENTITY.TENANT_ID, tenantId)
                .set(PLATFORM_FEATURE_ENTITY.ENTITY_ID, entityId)
                .set(PLATFORM_FEATURE_ENTITY.PLATFORM_FEATURE_ID, feature.getId())
                .set(PLATFORM_FEATURE_ENTITY.STATUS, PlatformFeatureStatus.READY.name())
                .execute();
            if (result == 0) {
                throw new ServiceFailureException("Failed to move feature to ready");
            }
        }
    }
}
