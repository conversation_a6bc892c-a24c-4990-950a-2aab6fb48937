package com.subskribe.billy.approvalflow.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.approvalflow.model.ApprovalFlow;
import com.subskribe.billy.approvalflow.model.ApprovalState;
import com.subskribe.billy.approvalflow.model.ApprovalStateAction;
import com.subskribe.billy.approvalflow.model.ApprovalTransitionRule;
import com.subskribe.billy.jooq.default_schema.tables.records.ApprovalFlowRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.ApprovalStateRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.ApprovalTransitionRuleRecord;
import com.subskribe.billy.shared.mapper.BaseMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.util.List;
import org.jooq.JSON;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueMappingStrategy;

@Mapper(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface ApprovalFlowDAOMapper extends BaseMapper {
    ObjectMapper OBJECT_MAPPER = JacksonProvider.emptyFieldExcludingMapper();

    ApprovalFlow toApprovalFlow(ApprovalFlowRecord record);

    @Mapping(source = "action", target = "action", qualifiedByName = "deserializeAction")
    List<ApprovalState> toApprovalStates(List<ApprovalStateRecord> records);

    @Mapping(source = "action", target = "action", qualifiedByName = "deserializeAction")
    ApprovalState toApprovalState(ApprovalStateRecord record);

    @Mapping(source = "orderCondition", target = "condition")
    @Mapping(source = "orderCondition", target = "ruleConditions.orderCondition")
    @Mapping(source = "orderLineCondition", target = "ruleConditions.orderLineCondition")
    List<ApprovalTransitionRule> toApprovalTransitionRules(List<ApprovalTransitionRuleRecord> record);

    @Mapping(source = "orderCondition", target = "condition")
    @Mapping(source = "orderCondition", target = "ruleConditions.orderCondition")
    @Mapping(source = "orderLineCondition", target = "ruleConditions.orderLineCondition")
    ApprovalTransitionRule toApprovalTransitionRule(ApprovalTransitionRuleRecord record);

    ApprovalFlowRecord toApprovalFlowRecord(ApprovalFlow approvalFlow);

    @Mapping(source = "action", target = "action", qualifiedByName = "serializeAction")
    List<ApprovalStateRecord> toApprovalStateRecords(List<ApprovalState> approvalStates);

    @Mapping(source = "action", target = "action", qualifiedByName = "serializeAction")
    ApprovalStateRecord toApprovalStateRecord(ApprovalState approvalState);

    @Mapping(source = "ruleConditions.orderCondition", target = "orderCondition")
    @Mapping(source = "ruleConditions.orderLineCondition", target = "orderLineCondition")
    List<ApprovalTransitionRuleRecord> toApprovalTransitionRuleRecords(List<ApprovalTransitionRule> transitionRules);

    @Mapping(source = "ruleConditions.orderCondition", target = "orderCondition")
    @Mapping(source = "ruleConditions.orderLineCondition", target = "orderLineCondition")
    ApprovalTransitionRuleRecord toApprovalTransitionRule(ApprovalTransitionRule transitionRule);

    @Named("deserializeAction")
    default ApprovalStateAction deserializeAction(JSON action) throws JsonProcessingException {
        if (action == null) {
            return null;
        }

        return OBJECT_MAPPER.readValue(action.toString(), new TypeReference<>() {});
    }

    @Named("serializeAction")
    default JSON serializeAction(ApprovalStateAction action) throws JsonProcessingException {
        if (action == null) {
            return null;
        }

        return JSON.json(OBJECT_MAPPER.writeValueAsString(action));
    }
}
