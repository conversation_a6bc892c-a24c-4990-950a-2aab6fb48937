package com.subskribe.billy.productcatalog.services;

import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.productcatalog.db.TaxRateDAO;
import com.subskribe.billy.productcatalog.model.TaxRate;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;

public class TaxRateGetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TaxRateGetService.class);

    private final TaxRateDAO taxRateDAO;
    private final ProductCatalogGetService productCatalogGetService;

    @Inject
    public TaxRateGetService(TaxRateDAO taxRateDAO, ProductCatalogGetService productCatalogGetService) {
        this.taxRateDAO = taxRateDAO;
        this.productCatalogGetService = productCatalogGetService;
    }

    // todo: implement proper pagination
    public List<TaxRate> getTaxRates(PaginationQueryParams paginationQueryParams) {
        List<TaxRate> taxRates = taxRateDAO.getTaxRates(paginationQueryParams);
        // TODO: Optimize as bulk fetch
        for (TaxRate taxRate : taxRates) {
            taxRate.setInUse(isTaxRateInUse(taxRate.getId()));
        }
        return taxRates;
    }

    public Map<UUID, TaxRate> getTaxRatesMap(Set<UUID> taxRateIds) {
        Validator.validateNonNullArgument(taxRateIds, "taxRateIds required");
        List<TaxRate> taxRates = taxRateDAO.getTaxRates(taxRateIds);
        return taxRates.stream().collect(Collectors.toMap(TaxRate::getId, Function.identity()));
    }

    public TaxRate getTaxRate(UUID id) {
        if (id == null) {
            LOGGER.warn("tax rate id is null");
            throw new InvalidInputException("tax rate id cannot be null");
        }
        Optional<TaxRate> optionalTaxRate = taxRateDAO.getTaxRate(id);
        if (optionalTaxRate.isEmpty()) {
            LOGGER.warn("tax rate with id {} not found", id);
            throw new ObjectNotFoundException(BillyObjectType.TAX_RATE, id.toString());
        }

        TaxRate taxRate = optionalTaxRate.get();
        taxRate.setInUse(isTaxRateInUse(taxRate.getId()));
        return taxRate;
    }

    public Optional<UUID> getTaxRateIdByName(String taxRateName) {
        Validator.validateStringNotBlank(taxRateName, "tax rate name cannot be blank");
        return taxRateDAO.getTaxRateIdByName(taxRateName);
    }

    public Optional<UUID> getTaxRateIdByCode(String taxRateCode) {
        Validator.validateStringNotBlank(taxRateCode, "tax rate code cannot be blank");
        return taxRateDAO.getTaxRateIdByCode(taxRateCode);
    }

    private boolean isTaxRateInUse(UUID taxRateId) {
        return productCatalogGetService.getActiveChargesCountWithTaxRateId(taxRateId) > 0;
    }

    public boolean tenantHasPercentBasedTaxRates() {
        return taxRateDAO.getTaxRatesWithPercentCount() > 0;
    }
}
