package com.subskribe.billy.productcatalog.services;

import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationship;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationshipType;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.productcatalog.db.ChargeDAO;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.LedgerAccountMapping;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.UpdateCatalogRelationships;
import com.subskribe.billy.productcatalog.ratecard.model.ChargeDefaultAttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.validation.CatalogValidation;
import com.subskribe.billy.productcatalog.validation.ChargeValidation;
import com.subskribe.billy.resources.json.plan.ChargePartialJson;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

public class ChargeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChargeService.class);
    private static final Set<ChargeType> ALLOWED_CHARGE_TYPES_FOR_PRICE_ATTRIBUTION = Set.of(ChargeType.RECURRING, ChargeType.ONE_TIME);

    private final ChargeDAO chargeDAO;
    private final ChargeValidation chargeValidation;
    private final ProductCatalogGetService productCatalogGetService;
    private final FeatureService featureService;
    private final CatalogEventService catalogEventService;
    private final CatalogValidation catalogValidation;
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final CatalogRelationshipService catalogRelationshipService;
    private final AccountingService accountingService;
    private final AccountingGetService accountingGetService;
    private final CustomFieldService customFieldService;
    private final RateCardService rateCardService;

    @Inject
    public ChargeService(
        ChargeDAO chargeDAO,
        ChargeValidation chargeValidation,
        ProductCatalogGetService productCatalogGetService,
        FeatureService featureService,
        CatalogEventService catalogEventService,
        CatalogValidation catalogValidation,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        CatalogRelationshipService catalogRelationshipService,
        AccountingService accountingService,
        AccountingGetService accountingGetService,
        CustomFieldService customFieldService,
        RateCardService rateCardService
    ) {
        this.chargeDAO = chargeDAO;
        this.chargeValidation = chargeValidation;
        this.productCatalogGetService = productCatalogGetService;
        this.featureService = featureService;
        this.catalogEventService = catalogEventService;
        this.catalogValidation = catalogValidation;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.catalogRelationshipService = catalogRelationshipService;
        this.accountingService = accountingService;
        this.accountingGetService = accountingGetService;
        this.customFieldService = customFieldService;
        this.rateCardService = rateCardService;
    }

    // this updates select fields that aren't financial transaction impacting.
    // note: this is not a PATCH semantic
    public Charge updateChargePartial(String planId, ChargePartialJson chargePartial) {
        Validator.validateStringNotBlank(planId, "planId must be provided");
        Validator.validateNonNullArgument(chargePartial, "charge");
        Validator.validateNonNullArgument(chargePartial.getId(), "charge.chargeId");

        // make sure plan exists
        Plan plan = productCatalogGetService.getPlan(planId);
        String chargeId = chargePartial.getId();
        Charge existingCharge = plan
            .getCharges()
            .stream()
            .filter(c -> c.getChargeId().equals(chargeId))
            .findFirst()
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.CHARGE, chargeId));

        Charge updatedCharge = updateChargeFromChargePartial(existingCharge, chargePartial);

        chargeValidation.validatePriceOverrideForActiveCharge(existingCharge, updatedCharge);
        chargeValidation.validateChargeRecognitionRuleForActiveCharge(existingCharge, updatedCharge);
        chargeValidation.validateCharge(updatedCharge);
        chargeValidation.validateIsCreditableForActiveCharge(existingCharge, updatedCharge, productCatalogGetService.isPlanInUse(planId));

        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return tenantDSLContext.transactionResult(configuration -> {
            Charge savedCharge = chargeDAO.updateChargePartial(plan.getId(), updatedCharge, configuration);
            catalogEventService.sendChargeEvent(savedCharge, EventType.CHARGE_UPDATED, configuration);
            return savedCharge;
        });
    }

    Charge addChargeForPlanInTx(Charge charge, Plan plan, Configuration configuration) {
        Charge savedCharge = chargeDAO.insertCharge(DSL.using(configuration), charge, plan.getPlanId(), plan.getId());
        catalogEventService.sendChargeEvent(savedCharge, EventType.CHARGE_CREATED, configuration);
        return savedCharge;
    }

    public Charge updateCharge(String planId, Charge charge) {
        Validator.validateStringNotBlank(planId, "planId must be provided");
        Validator.validateNonNullArgument(charge, "charge");
        Validator.validateNonNullArgument(charge.getChargeId(), "charge.chargeId");

        // make sure plan exists
        Plan plan = productCatalogGetService.getPlan(planId);
        if (plan.getStatus() != PlanStatus.DRAFT) {
            throw new IllegalArgumentException("Only charges in plans in draft status can be updated");
        }

        // propagate entity ids from plan to charge
        charge.setEntityIds(plan.getEntityIds());

        // replace the old version of the charge with the new version and validate the plan and charges
        plan.setCharges(replaceCharge(plan.getCharges(), charge));
        catalogValidation.validatePlanAndCharges(plan);
        Optional<UpdateCatalogRelationships> updatePercentOfRelationships = validatePercentOfCharge(plan, charge);

        var context = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return context.transactionResult(configuration -> {
            Charge savedCharge = updateChargeForPlanInTx(charge, plan, configuration);
            updatePercentOfCharge(planId, charge, updatePercentOfRelationships, configuration);
            return savedCharge;
        });
    }

    Charge updateChargeForPlanInTx(Charge charge, Plan plan, Configuration configuration) {
        Charge savedCharge = chargeDAO.updateCharge(plan.getId(), plan.getPlanId(), charge);
        catalogEventService.sendChargeEvent(savedCharge, EventType.CHARGE_UPDATED, configuration);
        return savedCharge;
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private void updatePercentOfCharge(
        String planId,
        Charge charge,
        Optional<UpdateCatalogRelationships> updatePercentOfRelationships,
        Configuration configuration
    ) {
        if (!featureService.isEnabled(Feature.PERCENT_OF_DIALOG)) {
            updatePercentOfCharge(planId, charge);
        }

        updatePercentOfRelationships.ifPresent(updateCatalogRelationships -> {
            updateCatalogRelationships
                .getAddCatalogRelationships()
                .forEach(relationship -> catalogRelationshipService.addCatalogRelationshipInTransaction(configuration, relationship));
            updateCatalogRelationships
                .getDeleteCatalogRelationships()
                .forEach(relationship -> catalogRelationshipService.deleteCatalogRelationship(configuration, relationship));
        });
    }

    private List<Charge> replaceCharge(List<Charge> charges, Charge updatedCharge) {
        int existingIndex = charges.stream().map(Charge::getChargeId).toList().indexOf(updatedCharge.getChargeId());
        if (existingIndex == -1) {
            throw new IllegalArgumentException("Cannot update charge that is not present in plan");
        }
        List<Charge> newCharges = new ArrayList<>(charges);
        newCharges.remove(existingIndex);
        newCharges.add(existingIndex, updatedCharge);
        return newCharges;
    }

    public Charge deleteCharge(String planId, String chargeId) {
        Validator.validateStringNotBlank(planId, "planId must be provided");
        Validator.validateStringNotBlank(chargeId, "chargeId must be provided");

        Plan plan = productCatalogGetService.getPlan(planId);
        if (plan.getStatus() != PlanStatus.DRAFT) {
            throw new IllegalArgumentException("Only charges in plans in draft status can be deleted");
        }

        if (CollectionUtils.isEmpty(plan.getCharges())) {
            throw new IllegalArgumentException("Plan currently contains no charges");
        }

        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        return tenantDSLContext.transactionResult(configuration -> {
            Charge deletedCharge = deleteChargeForPlanInTx(chargeId, plan, configuration);
            accountingService.deleteLedgerAccountMappingsForCharges(configuration, List.of(chargeId));
            // todo: delete catalog relationship if any
            return deletedCharge;
        });
    }

    Charge deleteChargeForPlanInTx(String chargeId, Plan plan, Configuration configuration) {
        Charge deletedCharge = chargeDAO
            .deleteCharge(configuration, plan.getId(), chargeId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.CHARGE, chargeId));
        catalogEventService.sendChargeEvent(deletedCharge, EventType.CHARGE_DELETED, configuration);
        return deletedCharge;
    }

    private void updatePercentOfCharge(String planId, Charge charge) {
        if (charge.getType() != ChargeType.PERCENTAGE_OF) {
            return;
        }

        if (CollectionUtils.isEmpty(charge.getTargetPlanIds())) {
            // todo: move this to Charge.validate() once UI catches up with updated API
            throw new IllegalArgumentException("targetPlanIds required for PERCENTAGE_OF charge types.");
        }

        List<CatalogRelationship> catalogRelationships = charge
            .getTargetPlanIds()
            .stream()
            .map(targetPlanId -> new CatalogRelationship(null, null, planId, CatalogRelationshipType.IS_PERCENT_OF, null, targetPlanId))
            .collect(Collectors.toList());
        catalogRelationshipService.mergeAndPersistCatalogRelationships(planId, catalogRelationships, CatalogRelationshipType.IS_PERCENT_OF);
    }

    private Optional<UpdateCatalogRelationships> validatePercentOfCharge(Plan plan, Charge charge) {
        if (!featureService.isEnabled(Feature.PERCENT_OF_DIALOG) || charge.getType() != ChargeType.PERCENTAGE_OF) {
            return Optional.empty();
        }

        // todo: validate current plan is not a percent of percent plan and creating a percent of percent relationship

        if (CollectionUtils.isEmpty(charge.getTargetPlanIds())) {
            throw new InvalidInputException("targetPlanIds required for PERCENTAGE_OF charge types.");
        }

        List<CatalogRelationship> percentOfRelationships = charge
            .getTargetPlanIds()
            .stream()
            .map(targetPlanId ->
                new CatalogRelationship(null, plan.getProductId(), plan.getPlanId(), CatalogRelationshipType.IS_PERCENT_OF, null, targetPlanId)
            )
            .toList();

        UpdateCatalogRelationships updateCatalogRelationships = catalogRelationshipService.validateAndMergePercentOfRelationships(
            plan,
            percentOfRelationships,
            CatalogRelationshipType.IS_PERCENT_OF
        );
        return Optional.of(updateCatalogRelationships);
    }

    void updateMetadataForCharges(Plan plan, DSLContext dslContext) {
        Map<String, Charge> existingChargeMap = chargeDAO
            .getChargesByPlan(plan)
            .stream()
            .collect(Collectors.toMap(Charge::getChargeId, charge -> charge));
        for (var charge : plan.getCharges()) {
            Charge existingCharge = existingChargeMap.get(charge.getChargeId());
            if (existingCharge == null) {
                /*
                 TODO: This should not be possible, but there is an E2E test that triggers it. It should be a validation error
                 somewhere upstream, where if new charges (without ID) are found in a metadata-only update, they should be
                 rejected. Handling it gracefully here. I'm also curious if and how often this will happen in production.
                 Once that validation is added, this can be removed.
                */
                LOGGER.info("Existing charge with id {} not found in plan {} for update", charge.getChargeId(), plan.getPlanId());
                updateMetadataForCharge(plan, dslContext, charge);
            }
            if (!charge.isMetadataEquals(existingCharge)) {
                updateMetadataForCharge(plan, dslContext, charge);
            }
        }
    }

    private void updateMetadataForCharge(Plan plan, DSLContext dslContext, Charge charge) {
        Charge updatedCharge = chargeDAO.updateChargeMetadata(dslContext, charge, plan.getId());
        String recognitionRuleId = charge.getRecognitionRuleId();
        if (StringUtils.isNotBlank(recognitionRuleId)) {
            chargeDAO.attachRecognitionRuleToCharge(dslContext, charge, plan.getId());
        }

        // This can happen if the charge was not found in the database. Similar to  the comment in
        // updateMetadataForCharges above. Simply logging it for now and skipping the event emission.
        if (updatedCharge == null) {
            LOGGER.info(
                "Charge with id {} not found in plan {} for metadata update, skipping event emission",
                charge.getChargeId(),
                plan.getPlanId()
            );
            return;
        }

        catalogEventService.sendChargeEvent(updatedCharge, EventType.CHARGE_UPDATED, dslContext.configuration());
    }

    public List<Charge> getChargesByPlan(Plan plan) {
        return chargeDAO.getChargesByPlan(plan);
    }

    void duplicateCharges(Plan originalPlan, Plan newPlan, List<Charge> originalCharges) {
        Map<String, String> chargeIdMap = new HashMap<>(originalPlan.getCharges().size());
        // sort charges so usage and percent of charges are created last
        List<Charge> sortedCharges = sortCharges(originalCharges);
        for (Charge charge : sortedCharges) {
            String originalChargeId = charge.getChargeId();
            charge.setId(null);
            charge.setEntityIds(charge.getEntityIds());
            charge.setChargeId(null);
            charge.setExternalId(null);
            charge.setPlanUuid(newPlan.getId());
            charge.setPlanId(newPlan.getPlanId());

            charge.setMinimumCommitBaseChargeId(getMappedChargeId(charge.getMinimumCommitBaseChargeId(), chargeIdMap));
            charge.setOverageBaseChargeId(getMappedChargeId(charge.getOverageBaseChargeId(), chargeIdMap));

            String newChargeId = addChargeToExistingPlan(newPlan.getPlanId(), charge);
            chargeIdMap.put(originalChargeId, newChargeId);

            // todo: move to addCharge and updateCharge
            List<LedgerAccount> ledgerAccountsForOriginalCharge = accountingGetService.getLedgerAccountsForCharge(originalChargeId);
            LedgerAccountMapping ledgerAccountMapping = new LedgerAccountMapping(ledgerAccountsForOriginalCharge);
            accountingService.mapLedgerAccountsToCharge(newChargeId, ledgerAccountMapping.getLedgerAccountIds());

            //set charge default attributes if required
            if (ALLOWED_CHARGE_TYPES_FOR_PRICE_ATTRIBUTION.contains(charge.getType()) && charge.getChargeModel() == ChargeModel.RATE_CARD_LOOKUP) {
                addChargeDefaultAttributeReferences(originalChargeId, newChargeId);
            }
            customFieldService.duplicateCustomFields(CustomFieldParentType.CHARGE, originalChargeId, newChargeId);
        }
    }

    private List<Charge> sortCharges(List<Charge> charges) {
        return charges
            .stream()
            .sorted((charge1, charge2) -> {
                int priority1 = getChargeSortPriority(charge1.getType());
                int priority2 = getChargeSortPriority(charge2.getType());
                return Integer.compare(priority1, priority2);
            })
            .toList();
    }

    private int getChargeSortPriority(ChargeType chargeType) {
        return switch (chargeType) {
            case USAGE -> 3;
            case PERCENTAGE_OF -> 2;
            default -> 1;
        };
    }

    private void addChargeDefaultAttributeReferences(String originalChargeId, String newChargeId) {
        Optional<ChargeDefaultAttributeReferences> chargeDefaultAttributeReferences = rateCardService.getChargeDefaultAttributeReferencesByChargeId(
            originalChargeId
        );
        chargeDefaultAttributeReferences.ifPresentOrElse(
            references -> {
                ChargeDefaultAttributeReferences.Builder builder = ChargeDefaultAttributeReferences.builder()
                    .chargeId(newChargeId)
                    .attributeReferences(references.getAttributeReferences());
                rateCardService.upsertChargeDefaultAttributeReferences(builder.build());
            },
            () -> LOGGER.error("No charge default attribute references found for charge id {}", originalChargeId)
        );
    }

    private static String getMappedChargeId(String originalId, Map<String, String> chargeIdMap) {
        if (StringUtils.isBlank(originalId)) {
            return null;
        }

        if (!chargeIdMap.containsKey(originalId)) {
            throw new InvariantCheckFailedException(String.format("could not find id %s in chargeId map", originalId));
        }

        return chargeIdMap.get(originalId);
    }

    public String addChargeToExistingPlan(String existingPlanId, Charge charge) {
        Validator.validateStringNotBlank(existingPlanId, "planId must be provided");
        Validator.validateNonNullArgument(charge, "charge");

        // make sure plan exists
        Plan plan = productCatalogGetService.getPlan(existingPlanId);
        if (plan.getStatus() != PlanStatus.DRAFT) {
            throw new IllegalStateException("only plans in draft status can be updated");
        }

        // propagate entity ids from plan to charge
        charge.setEntityIds(plan.getEntityIds());

        List<Charge> newCharges = new ArrayList<>(plan.getCharges());
        newCharges.add(charge);
        plan.setCharges(newCharges);
        catalogValidation.validatePlanAndCharges(plan);
        Optional<UpdateCatalogRelationships> updatePercentOfRelationships = validatePercentOfCharge(plan, charge);

        var context = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return context.transactionResult(configuration -> {
            Charge savedCharge = addChargeForPlanInTx(charge, plan, configuration);
            updatePercentOfCharge(existingPlanId, charge, updatePercentOfRelationships, configuration);
            return savedCharge.getChargeId();
        });
    }

    private static Charge updateChargeFromChargePartial(Charge charge, ChargePartialJson chargePartial) {
        return new Charge(
            charge.getId(),
            charge.getEntityIds(),
            charge.getChargeId(),
            charge.getPlanUuid(),
            charge.getPlanId(),
            chargePartial.getName(),
            chargePartial.getDisplayName(),
            chargePartial.getDescription(),
            charge.getAmount(),
            charge.getType(),
            charge.getChargeModel(),
            charge.getRecurrence(),
            charge.getDurationInMonths(),
            charge.getPriceTiers(),
            chargePartial.getTaxRateId(),
            chargePartial.getUnitOfMeasureId(),
            chargePartial.getRecognitionRuleId(),
            charge.isDrawdown(),
            charge.getMinimumCommitBaseChargeId(),
            charge.getOverageBaseChargeId(),
            charge.isCustom(),
            chargePartial.getIsListPriceEditable(),
            chargePartial.getIsRenewable(),
            chargePartial.isCreditable(),
            charge.getPercent(),
            charge.getPercentDerivedFrom(),
            charge.getTargetPlanIds(),
            chargePartial.getMinQuantity(),
            chargePartial.getDefaultQuantity(),
            chargePartial.getMaxQuantity(),
            chargePartial.getExternalId(),
            chargePartial.getErpId(),
            chargePartial.getMinAmount(),
            chargePartial.getMaxAmount(),
            charge.isEventBased(),
            charge.getIsDiscount(),
            charge.getRateCardId(),
            charge.getBillingCycle(),
            charge.getBillingTerm(),
            charge.getShouldTrackArr(),
            chargePartial.getItemCode(),
            null
        );
    }
}
