package com.subskribe.billy.productcatalog.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import com.subskribe.billy.shared.traits.ValidatedModel;
import java.time.Instant;
import java.util.UUID;
import javax.annotation.Nullable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
@JsonDeserialize(as = ImmutableTaxRateStrategy.class)
public interface TaxRateStrategy extends ValidatedModel {
    UUID getId();

    @NotBlank(message = "name is required")
    @Size(max = 100, message = "name should not be more than 100 characters")
    String getName();

    String getTaxRateStrategyId();

    String getCountryCode();

    UUID getTaxRateId();

    @Nullable
    Instant getCreatedOn();
}
