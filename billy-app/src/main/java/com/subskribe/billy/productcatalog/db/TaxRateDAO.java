package com.subskribe.billy.productcatalog.db;

import static com.subskribe.billy.jooq.default_schema.tables.TaxRate.TAX_RATE;
import static com.subskribe.billy.jooq.default_schema.tables.TaxRateStrategy.TAX_RATE_STRATEGY;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.TaxRateRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.TaxRateStrategyRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.productcatalog.mapper.TaxRateRecordMapper;
import com.subskribe.billy.productcatalog.mapper.TaxRateRecordMapperImpl;
import com.subskribe.billy.productcatalog.mapper.TaxRateStrategyRecordMapper;
import com.subskribe.billy.productcatalog.mapper.TaxRateStrategyRecordMapperImpl;
import com.subskribe.billy.productcatalog.model.TaxRate;
import com.subskribe.billy.productcatalog.model.TaxRateStrategy;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import javax.inject.Inject;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.SelectSeekStep1;
import org.jooq.impl.DSL;

public class TaxRateDAO {

    private static final String UNIQUE_TAX_RATE_STRATEGY_ID_CONSTRAINT_NAME = "index_tax_rate_strategy_tenant_id_tax_rate_strategy_id";
    private static final String UNIQUE_COUNTRY_CODE_TAX_RATE_ID_CONSTRAINT_NAME = "index_tax_rate_strategy_tenant_id_country_code_tax_rate_id";

    private static final Condition TAX_RATE_NOT_DELETED = TAX_RATE.IS_DELETED.isFalse();

    private static final Condition TAX_RATE_STRATEGY_NOT_DELETED = TAX_RATE_STRATEGY.IS_DELETED.isFalse();

    private final TaxRateRecordMapper taxRateRecordMapper;
    private final TaxRateStrategyRecordMapper taxRateStrategyRecordMapper;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;

    @Inject
    public TaxRateDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        taxRateRecordMapper = new TaxRateRecordMapperImpl();
        taxRateStrategyRecordMapper = new TaxRateStrategyRecordMapperImpl();
    }

    public TaxRate addTaxRate(TaxRate taxRate) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = dslContextProvider.get(tenantId);

        var record = taxRateRecordMapper.toTaxRecord(taxRate);
        record.setTenantId(tenantId);
        record.reset(TAX_RATE.ID);
        var insertedRecord = dslContext.insertInto(TAX_RATE).set(record).returning().fetchOne();
        return taxRateRecordMapper.fromTaxRecord(insertedRecord);
    }

    public void updateTaxRate(TaxRate taxRate) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var record = taxRateRecordMapper.toTaxRecord(taxRate);
        record.setTenantId(tenantIdProvider.provideTenantIdString());
        record.setId(taxRate.getId());

        dslContext.executeUpdate(record);
    }

    public TaxRate deleteTaxRate(UUID taxRateId) {
        if (taxRateId == null) {
            throw new IllegalArgumentException("taxRateId parameter cannot be null");
        }
        var tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return tenantDslContext.transactionResult(configuration -> deleteTaxRate(DSL.using(configuration), taxRateId));
    }

    private TaxRate deleteTaxRate(DSLContext dslContext, UUID taxRateId) {
        var taxRateRecord = dslContext
            .selectFrom(TAX_RATE)
            .where(TAX_RATE.ID.eq(taxRateId))
            .and(TAX_RATE_NOT_DELETED)
            .and(TAX_RATE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .fetchOne();
        if (taxRateRecord == null) {
            String message = String.format("tax record with id:%s not found", taxRateId);
            throw new IllegalArgumentException(message);
        }
        dslContext
            .update(TAX_RATE)
            .set(TAX_RATE.IS_DELETED, true)
            .where(TAX_RATE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(TAX_RATE.ID.eq(taxRateId))
            .execute();
        return taxRateRecordMapper.fromTaxRecord(taxRateRecord);
    }

    public List<TaxRate> getTaxRates(PaginationQueryParams paginationQueryParams) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext.select().from(TAX_RATE).where(TAX_RATE.TENANT_ID.eq(tenantId)).and(TAX_RATE.IS_DELETED.eq(false));

        var taxRateRecords = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            TAX_RATE,
            TAX_RATE.ID,
            TAX_RATE.TENANT_ID,
            TAX_RATE.CREATED_ON,
            TaxRateRecord.class
        );

        return taxRateRecordMapper.fromTaxRecords(taxRateRecords);
    }

    public List<TaxRate> getTaxRates(Set<UUID> taxRateIds) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var taxRateRecords = dslContext
            .selectFrom(TAX_RATE)
            .where(TAX_RATE.ID.in(taxRateIds))
            .and(TAX_RATE_NOT_DELETED)
            .and(TAX_RATE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .fetchInto(TaxRateRecord.class);

        return taxRateRecordMapper.fromTaxRecords(taxRateRecords);
    }

    public Optional<UUID> getTaxRateIdByName(String taxRateName) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        UUID taxRateId = dslContext
            .select(TAX_RATE.ID)
            .from(TAX_RATE)
            .where(TAX_RATE.NAME.equalIgnoreCase(taxRateName))
            .and(TAX_RATE_NOT_DELETED)
            .and(TAX_RATE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .fetchOneInto(UUID.class);
        return Optional.ofNullable(taxRateId);
    }

    public Optional<UUID> getTaxRateIdByCode(String taxRateCode) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        UUID taxRateId = dslContext
            .select(TAX_RATE.ID)
            .from(TAX_RATE)
            .where(TAX_RATE.TAX_CODE.equalIgnoreCase(taxRateCode))
            .and(TAX_RATE_NOT_DELETED)
            .and(TAX_RATE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .fetchOneInto(UUID.class);
        return Optional.ofNullable(taxRateId);
    }

    public Optional<TaxRate> getTaxRate(UUID id) {
        if (id == null) {
            throw new IllegalArgumentException("tax rate id is required");
        }

        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var taxRateRecord = dslContext
            .selectFrom(TAX_RATE)
            .where(TAX_RATE.ID.eq(id))
            .and(TAX_RATE_NOT_DELETED)
            .and(TAX_RATE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .fetchOne();
        return Optional.ofNullable(taxRateRecordMapper.fromTaxRecord(taxRateRecord));
    }

    public int getTaxRatesWithPercentCount() {
        var context = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var matchesTenantId = TAX_RATE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString());
        return context.fetchCount(
            context.select().from(TAX_RATE).where(TAX_RATE.TAX_PERCENTAGE.isNotNull()).and(matchesTenantId).and(TAX_RATE.IS_DELETED.isFalse())
        );
    }

    public TaxRateStrategy addTaxRateStrategy(TaxRateStrategy taxRateStrategy) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);

        TaxRateStrategyRecord record = taxRateStrategyRecordMapper.toTaxStrategyRecord(taxRateStrategy);
        record.setTenantId(tenantId);
        record.reset(TAX_RATE_STRATEGY.ID);
        TaxRateStrategyRecord insertedRecord = PostgresErrorHandler.withAnyConstraintAsConflict(
            () -> dslContext.insertInto(TAX_RATE_STRATEGY).set(record).returning().fetchOne(),
            Map.of(
                UNIQUE_TAX_RATE_STRATEGY_ID_CONSTRAINT_NAME,
                String.format("tax rate strategy exists with tax rate strategy id: %s", taxRateStrategy.getTaxRateStrategyId()),
                UNIQUE_COUNTRY_CODE_TAX_RATE_ID_CONSTRAINT_NAME,
                String.format(
                    "tax rate strategy with country code %s and tax rate id %s already exists",
                    taxRateStrategy.getCountryCode(),
                    taxRateStrategy.getTaxRateId()
                )
            )
        );
        if (insertedRecord == null) {
            throw new ServiceFailureException("Failed to insert tax rate strategy record");
        }
        return taxRateStrategyRecordMapper.fromTaxStrategyRecord(insertedRecord);
    }

    public void updateTaxRateStrategy(TaxRateStrategy taxRateStrategy) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        TaxRateStrategyRecord record = taxRateStrategyRecordMapper.toTaxStrategyRecord(taxRateStrategy);
        record.setTenantId(tenantIdProvider.provideTenantIdString());
        record.setId(taxRateStrategy.getId());

        int rowsUpdated = PostgresErrorHandler.withAnyConstraintAsConflict(
            () -> dslContext.executeUpdate(record),
            Map.of(
                UNIQUE_TAX_RATE_STRATEGY_ID_CONSTRAINT_NAME,
                String.format("tax rate strategy exists with tax rate strategy id: %s", taxRateStrategy.getTaxRateStrategyId()),
                UNIQUE_COUNTRY_CODE_TAX_RATE_ID_CONSTRAINT_NAME,
                String.format(
                    "tax rate strategy with country code %s and tax rate id %s already exists",
                    taxRateStrategy.getCountryCode(),
                    taxRateStrategy.getTaxRateId()
                )
            )
        );
        if (rowsUpdated == 0) {
            throw new ServiceFailureException("Failed to update tax rate strategy record");
        }
    }

    public void deleteTaxRateStrategy(String taxRateStrategyId) {
        Validator.validateStringNotBlank(taxRateStrategyId, "taxRateStrategyId parameter cannot be blank");
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        tenantDslContext.transaction(configuration -> deleteTaxRateStrategy(DSL.using(configuration), taxRateStrategyId));
    }

    private void deleteTaxRateStrategy(DSLContext dslContext, String taxRateStrategyId) {
        int rowsUpdated = dslContext
            .update(TAX_RATE_STRATEGY)
            .set(TAX_RATE_STRATEGY.IS_DELETED, true)
            .where(TAX_RATE_STRATEGY.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(TAX_RATE_STRATEGY.TAX_RATE_STRATEGY_ID.eq(taxRateStrategyId))
            .execute();
        if (rowsUpdated == 0) {
            throw new ServiceFailureException("Failed to delete tax rate strategy record");
        }
    }

    public PageResult<List<TaxRateStrategy>, Instant> getTaxRateStrategies(PageRequest<Instant> pageRequest) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        SelectSeekStep1<TaxRateStrategyRecord, LocalDateTime> query = dslContext
            .selectFrom(TAX_RATE_STRATEGY)
            .where(TAX_RATE_STRATEGY.TENANT_ID.eq(tenantId))
            .and(TAX_RATE_STRATEGY.IS_DELETED.eq(false))
            .orderBy(TAX_RATE_STRATEGY.CREATED_ON);

        List<TaxRateStrategyRecord> records;
        if (pageRequest.getPageToken() != null) {
            records = query.seek(DateTimeConverter.instantToLocalDateTime(pageRequest.getPageToken())).limit(pageRequest.getLimit()).fetch();
        } else {
            records = query.limit(pageRequest.getLimit()).fetch();
        }
        return PageResult.fromCollectionAndRequest(
            taxRateStrategyRecordMapper.fromTaxStrategyRecords(records),
            pageRequest,
            TaxRateStrategy::getCreatedOn
        );
    }

    public Optional<TaxRateStrategy> getTaxRateStrategy(String taxRateStrategyId) {
        Validator.validateStringNotBlank(taxRateStrategyId, "taxRateStrategyId parameter cannot be blank");

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Optional<TaxRateStrategyRecord> taxRateStrategyRecord = dslContext
            .selectFrom(TAX_RATE_STRATEGY)
            .where(TAX_RATE_STRATEGY.TAX_RATE_STRATEGY_ID.eq(taxRateStrategyId))
            .and(TAX_RATE_STRATEGY_NOT_DELETED)
            .and(TAX_RATE_STRATEGY.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .fetchOptional();
        return taxRateStrategyRecord.map(taxRateStrategyRecordMapper::fromTaxStrategyRecord);
    }
}
