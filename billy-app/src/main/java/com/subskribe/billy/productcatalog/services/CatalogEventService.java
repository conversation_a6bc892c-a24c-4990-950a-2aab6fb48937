package com.subskribe.billy.productcatalog.services;

import com.google.common.annotations.VisibleForTesting;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.TenantPartitionKey;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.ImmutablePlanStatusChangeEvent;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.PlanStatusChangeEvent;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.resources.json.product.ProductMapper;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.tenant.TenantIdProvider;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.mapstruct.factory.Mappers;

public class CatalogEventService {

    private final PlanMapper planMapper;
    private final ProductMapper productMapper;
    private final EventPublishingService eventPublishingService;
    private final TenantIdProvider tenantIdProvider;
    private final UncheckedObjectMapper objectMapper;

    @Inject
    public CatalogEventService(EventPublishingService eventPublishingService, TenantIdProvider tenantIdProvider) {
        this.eventPublishingService = eventPublishingService;
        this.tenantIdProvider = tenantIdProvider;
        this.planMapper = Mappers.getMapper(PlanMapper.class);
        this.productMapper = Mappers.getMapper(ProductMapper.class);
        this.objectMapper = UncheckedObjectMapper.defaultMapper();
    }

    @VisibleForTesting
    public CatalogEventService(
        EventPublishingService eventPublishingService,
        TenantIdProvider tenantIdProvider,
        PlanMapper planMapper,
        ProductMapper productMapper,
        UncheckedObjectMapper objectMapper
    ) {
        this.eventPublishingService = eventPublishingService;
        this.tenantIdProvider = tenantIdProvider;
        this.planMapper = planMapper;
        this.productMapper = productMapper;
        this.objectMapper = objectMapper;
    }

    public void sendChargeEvent(Charge charge, EventType eventType, Configuration transactionContext) {
        ChargeJson chargeJson = planMapper.chargeToJson(charge);
        String tenantId = tenantIdProvider.provideTenantIdString();
        eventPublishingService.publishEventInTransaction(
            transactionContext,
            eventType,
            tenantId,
            charge.getEntityIds(),
            new TenantPartitionKey(tenantId),
            objectMapper.writeValueAsBytes(chargeJson)
        );
    }

    public void sendPlanEvent(Plan plan, EventType eventType, Configuration transactionContext) {
        PlanJson planJson = planMapper.planToJson(plan);
        String tenantId = tenantIdProvider.provideTenantIdString();
        eventPublishingService.publishEventInTransaction(
            transactionContext,
            eventType,
            tenantId,
            plan.getEntityIds(),
            new TenantPartitionKey(tenantId),
            objectMapper.writeValueAsBytes(planJson)
        );
    }

    public void sendPlanStatusChangeEvent(Plan plan, PlanStatus from, PlanStatus to, Configuration transactionContext) {
        PlanJson planJson = planMapper.planToJson(plan);
        PlanStatusChangeEvent planStatusChangeEvent = ImmutablePlanStatusChangeEvent.builder()
            .planJson(planJson)
            .fromStatus(from)
            .toStatus(to)
            .build();
        String tenantId = tenantIdProvider.provideTenantIdString();
        eventPublishingService.publishEventInTransaction(
            transactionContext,
            EventType.PLAN_STATUS_CHANGED,
            tenantId,
            plan.getEntityIds(),
            new TenantPartitionKey(tenantId),
            objectMapper.writeValueAsBytes(planStatusChangeEvent)
        );
    }

    public void sendProductEvent(Product product, EventType eventType, Configuration transactionContext) {
        ProductJson productJson = productMapper.productToJson(product);
        String tenantId = tenantIdProvider.provideTenantIdString();
        eventPublishingService.publishEventInTransaction(
            transactionContext,
            eventType,
            tenantId,
            product.getEntityIds(),
            new TenantPartitionKey(tenantId),
            objectMapper.writeValueAsBytes(productJson)
        );
    }
}
