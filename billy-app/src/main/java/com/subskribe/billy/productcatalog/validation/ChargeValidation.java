package com.subskribe.billy.productcatalog.validation;

import static com.subskribe.billy.shared.enums.ChargeModel.FLAT_FEE;
import static com.subskribe.billy.shared.enums.ChargeModel.PER_UNIT;
import static com.subskribe.billy.shared.enums.ChargeModel.RATE_CARD_LOOKUP;
import static com.subskribe.billy.shared.enums.ChargeType.ONE_TIME;
import static com.subskribe.billy.shared.enums.ChargeType.PERCENTAGE_OF;
import static com.subskribe.billy.shared.enums.ChargeType.PREPAID;
import static com.subskribe.billy.shared.enums.ChargeType.RECURRING;
import static com.subskribe.billy.shared.enums.ChargeType.USAGE;

import com.subskribe.billy.anrok.service.AnrokIntegrationGetService;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.avalara.service.AvalaraIntegrationGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.platformfeature.model.EnabledPlatformFeature;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.UnitOfMeasureStatus;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.PriceTiersHelper;
import com.subskribe.billy.productcatalog.services.UnitOfMeasureService;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.RecognitionSource;
import com.subskribe.billy.shared.enums.RecognitionType;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.taxjar.service.TaxJarIntegrationGetService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Provider;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public final class ChargeValidation {

    private static final BigDecimal PERCENT_OF_LOWER_BOUND_EXCLUSIVE = BigDecimal.ZERO;
    private static final BigDecimal PERCENT_OF_UPPER_BOUND_INCLUSIVE = BigDecimal.valueOf(100);
    private static final long DURATION_IN_MONTHS_UPPER_BOUND = 120L; // 10 years
    private static final Set<ChargeType> ALLOWED_CHARGE_TYPES_FOR_RATE_CARD = Set.of(USAGE, RECURRING, ONE_TIME);
    private static final Set<ChargeType> ALLOWED_CHARGE_TYPES_FOR_PRICE_ATTRIBUTION = Set.of(RECURRING, ONE_TIME);

    private static final List<ChargeType> ALLOWED_TYPES_FOR_PRICE_OVERRIDE = List.of(RECURRING, ONE_TIME, PREPAID, USAGE, PERCENTAGE_OF);
    private static final List<ChargeType> ALLOWED_TYPES_FOR_DISCOUNT = List.of(ONE_TIME, RECURRING);
    static final Predicate<ChargeModel> DISCOUNT_CHARGE_MODEL_PREDICATE = chargeModel -> chargeModel == FLAT_FEE || chargeModel == PER_UNIT;

    private final TenantSettingService tenantSettingService;
    private final UnitOfMeasureService unitOfMeasureService;
    private final Provider<RevenueRecognitionGetService> revenueRecognitionGetServiceProvider;
    private final Provider<PlatformFeatureService> platformFeatureServiceProvider;
    private final AvalaraIntegrationGetService avalaraIntegrationGetService;
    private final FeatureService featureService;
    private final TaxJarIntegrationGetService taxJarIntegrationGetService;
    private final AnrokIntegrationGetService anrokIntegrationGetService;

    private final RateCardService rateCardService;

    @Inject
    public ChargeValidation(
        TenantSettingService tenantSettingService,
        UnitOfMeasureService unitOfMeasureService,
        Provider<RevenueRecognitionGetService> revenueRecognitionGetServiceProvider,
        Provider<PlatformFeatureService> platformFeatureServiceProvider,
        AvalaraIntegrationGetService avalaraIntegrationGetService,
        FeatureService featureService,
        RateCardService rateCardService,
        TaxJarIntegrationGetService taxJarIntegrationGetService,
        AnrokIntegrationGetService anrokIntegrationGetService
    ) {
        this.tenantSettingService = tenantSettingService;
        this.unitOfMeasureService = unitOfMeasureService;
        this.revenueRecognitionGetServiceProvider = revenueRecognitionGetServiceProvider;
        this.platformFeatureServiceProvider = platformFeatureServiceProvider;
        this.avalaraIntegrationGetService = avalaraIntegrationGetService;
        this.featureService = featureService;
        this.rateCardService = rateCardService;
        this.taxJarIntegrationGetService = taxJarIntegrationGetService;
        this.anrokIntegrationGetService = anrokIntegrationGetService;
    }

    public void validateCharge(Charge charge) {
        if (charge == null) {
            throw new IllegalArgumentException("Charge cannot be null");
        }

        if (StringUtils.isBlank(charge.getExternalId())) {
            charge.setExternalId(null);
        }

        if (StringUtils.isBlank(charge.getErpId())) {
            charge.setErpId(null);
        }

        if (StringUtils.isBlank(charge.getItemCode())) {
            charge.setItemCode(null);
        }

        if (charge.getType() == PERCENTAGE_OF && charge.getPercentDerivedFrom() == null) {
            Validator.checkNonNullInternal(
                tenantSettingService.getTenantSettingInternal().getPercentDerivedFrom(),
                "percent of target cannot be null for tenant"
            );
            charge.setPercentDerivedFrom(tenantSettingService.getTenantSettingInternal().getPercentDerivedFrom());
        }

        if (featureService.isEnabled(Feature.MIN_MAX_PERCENT_OF_AMOUNT)) {
            if (charge.getType() != PERCENTAGE_OF && (charge.getMinAmount() != null || charge.getMaxAmount() != null)) {
                throw new InvalidInputException("min and max amounts are only allowed for percentage of charges");
            }
        } else {
            if (charge.getMinAmount() != null || charge.getMaxAmount() != null) {
                throw new UnsupportedOperationException("min and max amounts are not enabled");
            }
        }

        if (avalaraIntegrationGetService.hasAvalaraIntegration() && charge.getTaxRateId() == null) {
            throw new InvalidInputException("Tax rate is required when Avalara integration is enabled");
        } else if (taxJarIntegrationGetService.getTaxJarIntegration().isPresent() && charge.getTaxRateId() == null) {
            throw new InvalidInputException("Tax rate is required when TaxJar integration is enabled");
        } else if (anrokIntegrationGetService.getAnrokIntegration().isPresent() && charge.getTaxRateId() == null) {
            throw new InvalidInputException("Tax rate is required when Anrok integration is enabled");
        }

        validateChargeInternal(charge);

        var unitOfMeasureId = charge.getUnitOfMeasureId();
        validateUnitOfMeasure(unitOfMeasureId);
    }

    public void validateChargeRecognitionRuleForActiveCharge(Charge existingCharge, Charge updateCharge) {
        if (
            StringUtils.isNotBlank(existingCharge.getRecognitionRuleId()) &&
            !StringUtils.equals(existingCharge.getRecognitionRuleId(), updateCharge.getRecognitionRuleId())
        ) {
            throw new InvalidInputException("Cannot change revenue recognition rule for an active charge");
        }
    }

    public void validatePriceOverrideForActiveCharge(Charge existingCharge, Charge updateCharge) {
        if (existingCharge.getIsListPriceEditable() && !updateCharge.getIsListPriceEditable()) {
            throw new InvalidInputException("cannot disable price override for an active charge");
        }
    }

    void validateChargeInternal(Charge charge) {
        Validator.checkNonNullInternal(charge, "charge must be present");

        // validate charge field annotations
        charge.validate();

        performChargeAttributeValidation(charge);
        validateChargeRecurrence(charge);
        validateChargeDuration(charge);
        validateBillingCycle(charge);

        switch (charge.getType()) {
            case PERCENTAGE_OF -> performPercentOfChargeValidation(charge);
            case USAGE -> performUsageChargeValidation(charge);
            case RECURRING -> performRecurringChargeValidation(charge);
            case PREPAID -> performPrepaidChargeValidation(charge);
            case ONE_TIME -> performOneTimeChargeValidation(charge);
            default -> {
                String message = String.format("cannot validate unknown charge type %s", charge.getType());
                throw new ServiceFailureException(message);
            }
        }
    }

    private void validateChargeRecurrence(Charge charge) {
        var recurrence = charge.getRecurrence();
        if (recurrence == null) {
            return;
        }

        if (recurrence.getStep() != 1 && (featureService.isNotEnabled(Feature.DAY_CYCLE) || recurrence.getCycle() != Cycle.DAY)) {
            throw new InvalidInputException(String.format("charge recurrence step must be 1, but was %s", recurrence.getStep()));
        }

        validateChargeTypeRecurrence(charge, USAGE, List.of(Cycle.MONTH, Cycle.QUARTER));

        validateChargeTypeRecurrence(charge, PREPAID, List.of(Cycle.MONTH, Cycle.QUARTER, Cycle.YEAR));

        if (featureService.isEnabled(Feature.DAY_CYCLE)) {
            validateChargeTypeRecurrence(charge, RECURRING, List.of(Cycle.DAY, Cycle.MONTH, Cycle.YEAR));
        } else {
            validateChargeTypeRecurrence(charge, RECURRING, List.of(Cycle.MONTH, Cycle.YEAR));
        }
    }

    private void validateChargeTypeRecurrence(Charge charge, ChargeType chargeType, List<Cycle> recurrenceOptions) {
        if (charge.getType() == chargeType && !recurrenceOptions.contains(charge.getRecurrence().getCycle())) {
            throw new InvalidInputException(
                String.format("%s charge recurrence must be one of %s, but was %s", chargeType, recurrenceOptions, charge.getRecurrence().getCycle())
            );
        }
    }

    private void validateChargeDuration(Charge charge) {
        var durationInMonths = charge.getDurationInMonths();
        if (durationInMonths == null) {
            return;
        }

        if (charge.getType() != ONE_TIME) {
            throw new InvalidInputException(String.format("One time charge duration is present for a charge which is of type %s", charge.getType()));
        }

        if (durationInMonths <= 0) {
            throw new InvalidInputException("One time charge duration in months must be positive");
        }

        if (durationInMonths > DURATION_IN_MONTHS_UPPER_BOUND) {
            throw new InvalidInputException(
                String.format("One time charge duration cannot be greater than 10 years, was %s months", durationInMonths)
            );
        }
    }

    private void performPercentOfChargeValidation(Charge charge) {
        // safety measure in case method is called elsewhere
        if (PERCENTAGE_OF != charge.getType()) {
            return;
        }

        recurrenceNotRequired(charge);

        if (PER_UNIT != charge.getChargeModel() && FLAT_FEE != charge.getChargeModel()) {
            throw new InvalidInputException("Percentage of charge type supports only Per Unit or Flat Fee charge model");
        }

        if (CollectionUtils.isNotEmpty(charge.getPriceTiers()) || charge.getAmount() != null) {
            throw new InvalidInputException("Percentage of charge has no notion of price or price tiers");
        }

        var percent = charge.getPercent();
        if (percent == null) {
            throw new InvalidInputException("Percentage of charge requires a percent value");
        }
        Numbers.validateInputAmountScale(percent);

        if (percent.compareTo(PERCENT_OF_LOWER_BOUND_EXCLUSIVE) <= 0 || percent.compareTo(PERCENT_OF_UPPER_BOUND_INCLUSIVE) > 0) {
            String message = String.format(
                "Percent of charge needs to be greater than or equal to %d and lesser than %d",
                PERCENT_OF_LOWER_BOUND_EXCLUSIVE.longValue(),
                PERCENT_OF_UPPER_BOUND_INCLUSIVE.longValue()
            );
            throw new InvalidInputException(message);
        }

        // todo: uncomment this once backfill for percent of charges is complete
        // Validator.checkNonNullInternal(percentDerivedFrom, "Percentage of charge requires an applied to type");

        if (charge.getMinAmount() != null && charge.getMaxAmount() != null && charge.getMinAmount().compareTo(charge.getMaxAmount()) > 0) {
            throw new InvalidInputException("Minimum amount cannot be greater than maximum amount");
        }
    }

    private void performCustomPricingValidation(Charge charge) {
        // safety measure in case method is called elsewhere
        if (!charge.isCustom()) {
            return;
        }

        // todo: support recurring custom charge once amendments are figured out
        if (charge.getType() != ONE_TIME && charge.getType() != RECURRING) {
            throw new InvalidInputException("custom pricing charge can only be one time or recurring");
        }

        if (charge.getChargeModel() != FLAT_FEE && charge.getChargeModel() != PER_UNIT) {
            throw new InvalidInputException("custom pricing charge can only be have a flat fee or per unit model");
        }

        if (charge.getIsListPriceEditable()) {
            throw new InvalidInputException("custom pricing charge cannot also have list price override");
        }

        if (charge.getAmount() != null) {
            throw new InvalidInputException("custom pricing charge cannot have a predefined amount");
        }

        if (CollectionUtils.isNotEmpty(charge.getPriceTiers())) {
            throw new InvalidInputException("custom pricing charge cannot have price tiers");
        }

        if (charge.getPercent() != null) {
            throw new InvalidInputException("custom pricing charge cannot have percent");
        }
    }

    private void validateListPriceEditable(Charge charge) {
        if (!charge.getIsListPriceEditable()) {
            return;
        }

        if (!ALLOWED_TYPES_FOR_PRICE_OVERRIDE.contains(charge.getType())) {
            throw new InvalidInputException(String.format("Type %s does not support override list price", charge.getType()));
        }

        if (charge.getType() == USAGE && charge.getChargeModel() == RATE_CARD_LOOKUP) {
            throw new InvalidInputException("Rate card usage charges cannot have price override");
        }

        if (charge.isCustom()) {
            throw new InvalidInputException("custom pricing charge cannot also have list price override");
        }

        if (charge.getAmount() != null && Numbers.isZero(charge.getAmount())) {
            throw new InvalidInputException("Price override is not allowed for zero amount charges, consider using custom pricing instead");
        }
    }

    private void validateDiscountCharge(Charge charge) {
        if (!charge.getIsDiscount()) {
            return;
        }
        if (!ALLOWED_TYPES_FOR_DISCOUNT.contains(charge.getType())) {
            throw new InvalidInputException("Discount charges can only be ONE_TIME or RECURRING");
        }
        if (!DISCOUNT_CHARGE_MODEL_PREDICATE.test(charge.getChargeModel())) {
            throw new InvalidInputException("Charge can be a discount only if charge model is FLAT_FEE or PER_UNIT");
        }
        if (charge.isEventBased()) {
            throw new InvalidInputException("Discount charges cannot be event based");
        }
    }

    // include add common negation rules here
    private void performChargeAttributeValidation(Charge charge) {
        if (charge.getType() != USAGE) {
            if (charge.isDrawdown()) {
                throw new InvalidInputException("Drawdown is only applicable for usage charge type");
            }

            if (StringUtils.isNotBlank(charge.getOverageBaseChargeId())) {
                throw new InvalidInputException("Overage is only applicable for usage charge type");
            }

            if (StringUtils.isNotBlank(charge.getMinimumCommitBaseChargeId())) {
                throw new InvalidInputException("minimum commit charges must be of type usage");
            }
        }

        validateEventBasedCharge(charge);
        validateRateCardAndChargeTypeCombination(charge);
        performCustomPricingValidation(charge);
        validateListPriceEditable(charge);
        validateQuantityConstraints(charge);
        validatePercentOfAttributes(charge);
        validateCreditableCharge(charge);
        validateDiscountCharge(charge);
    }

    private void validateCreditableCharge(Charge charge) {
        if (!charge.isCreditable()) {
            return;
        }

        if (charge.isCreditable() && charge.getType() != ONE_TIME) {
            String errorMessage = String.format("Only one time charges can be marked creditable. Given charge type is: %s.", charge.getType());
            throw new InvalidInputException(errorMessage);
        }
    }

    private void validateEventBasedCharge(Charge charge) {
        if (!charge.isEventBased()) {
            return;
        }

        if (charge.getType() == ONE_TIME || charge.getType() == RECURRING) {
            return;
        }

        throw new InvalidInputException("Only one time and recurring charges can be event based.");
    }

    void validateChargeDefaultAttribution(Charge charge) {
        if (!ALLOWED_CHARGE_TYPES_FOR_PRICE_ATTRIBUTION.contains(charge.getType()) || RATE_CARD_LOOKUP != charge.getChargeModel()) {
            return;
        }
        rateCardService
            .getChargeDefaultAttributeReferencesByChargeId(charge.getChargeId())
            .orElseThrow(() -> new InvalidInputException(String.format("Charge : %s is missing default attribute references", charge.getChargeId())));
    }

    private void validateRateCardAndChargeTypeCombination(Charge charge) {
        var chargeModel = charge.getChargeModel();
        if (RATE_CARD_LOOKUP == chargeModel && !ALLOWED_CHARGE_TYPES_FOR_RATE_CARD.contains(charge.getType())) {
            throw new InvalidInputException(
                String.format("Charge Model %s can be used with only %s, %s and %s charge types", RATE_CARD_LOOKUP, USAGE, RECURRING, ONE_TIME)
            );
        }
        if (StringUtils.isNotBlank(charge.getRateCardId()) && chargeModel != RATE_CARD_LOOKUP) {
            throw new InvalidInputException(
                String.format(
                    "Rate Card Id reference can be present only for Charge Model: %s and Charge Types: %s, %s and %s combination",
                    RATE_CARD_LOOKUP,
                    USAGE,
                    RECURRING,
                    ONE_TIME
                )
            );
        }
    }

    private void validatePercentOfAttributes(Charge charge) {
        if (
            charge.getType() != PERCENTAGE_OF &&
            (charge.getPercent() != null || charge.getPercentDerivedFrom() != null || CollectionUtils.isNotEmpty(charge.getTargetPlanIds()))
        ) {
            throw new InvalidInputException("percent, percentDerivedFrom, and targetPlanIds cannot be present for non PERCENTAGE_OF charges");
        }
    }

    private void performUsageChargeValidation(Charge charge) {
        var recurrence = charge.getRecurrence();
        if (recurrence == null) {
            throw new InvalidInputException("Usage charge recurrence is required");
        }
        if (Cycle.MONTH != recurrence.getCycle() && Cycle.QUARTER != recurrence.getCycle()) {
            throw new InvalidInputException("Usage charge recurring frequency can only be Monthly or Quarterly");
        }

        if (StringUtils.isNotBlank(charge.getOverageBaseChargeId()) && StringUtils.isNotBlank(charge.getMinimumCommitBaseChargeId())) {
            throw new InvalidInputException("overage and minimum commit cannot both be active for a charge");
        }

        if (StringUtils.isNotBlank(charge.getRateCardId()) && RATE_CARD_LOOKUP != charge.getChargeModel()) {
            throw new InvalidInputException(String.format("Rate card id cannot be present unless the charge model is %s", RATE_CARD_LOOKUP));
        }

        if (RATE_CARD_LOOKUP == charge.getChargeModel() && StringUtils.isNotBlank(charge.getOverageBaseChargeId())) {
            throw new InvalidInputException("quantity overage is not compatible with rate card lookup");
        }

        recurrenceRequired(charge);
        flatFeeChargeModelIsInvalid(charge);
        validatePriceBasedOnChargeModel(charge);
        validateBillingTerm(charge, BillingTerm.IN_ARREARS);
    }

    private void performRecurringChargeValidation(Charge charge) {
        recurrenceRequired(charge);
        validatePriceBasedOnChargeModel(charge);
    }

    private void performPrepaidChargeValidation(Charge charge) {
        optionalRecurrence(charge, List.of(Cycle.MONTH, Cycle.QUARTER, Cycle.YEAR));
        flatFeeChargeModelIsInvalid(charge);
        validatePriceBasedOnChargeModel(charge);
        validateBillingTerm(charge, BillingTerm.UP_FRONT);
    }

    private void performOneTimeChargeValidation(Charge charge) {
        recurrenceNotRequired(charge);
        validatePriceBasedOnChargeModel(charge);
        validateBillingTerm(charge, BillingTerm.UP_FRONT);
    }

    private void recurrenceRequired(Charge charge) {
        if (charge.getRecurrence() == null) {
            throw new InvalidInputException("recurrence is required for recurring and usage charge types");
        }
    }

    private void recurrenceNotRequired(Charge charge) {
        if (charge.getRecurrence() != null) {
            throw new InvalidInputException("recurrence is not required for this charge type");
        }
    }

    private void optionalRecurrence(Charge charge, List<Cycle> availableCycles) {
        if (charge.getRecurrence() == null) {
            return;
        }

        if (!availableCycles.contains(charge.getRecurrence().getCycle())) {
            throw new InvalidInputException(
                String.format("prepaid charge cycle must be one of %s, but was %s", availableCycles, charge.getRecurrence())
            );
        }
    }

    private void validateBillingTerm(Charge charge, BillingTerm expectedBillingTerm) {
        if (charge.getBillingTerm() != expectedBillingTerm) {
            throw new InvalidInputException(String.format("Charge of type %s must have billing term %s", charge.getType(), expectedBillingTerm));
        }
    }

    private void flatFeeChargeModelIsInvalid(Charge charge) {
        if (charge.getChargeModel() == FLAT_FEE) {
            throw new InvalidInputException("Flat fee is not applicable to usage or prepaid charge types");
        }
    }

    private void validatePriceBasedOnChargeModel(Charge charge) {
        // assuming that custom charge validation does the right thing here
        if (charge.isCustom()) {
            return;
        }

        var chargeModel = charge.getChargeModel();
        var amount = charge.getAmount();
        switch (chargeModel) {
            case RATE_CARD_LOOKUP -> {
                if (amount != null) {
                    throw new InvalidInputException(String.format("When charge model is %s the amount in the charge should be null", chargeModel));
                }

                if (StringUtils.isBlank(charge.getRateCardId())) {
                    // TODO: check if rate card exists by taking dependency on rate card service
                    throw new InvalidInputException(String.format("When charge model is %s rate card id should be referenced", chargeModel));
                }

                if (CollectionUtils.isNotEmpty(charge.getPriceTiers())) {
                    throw new InvalidInputException(
                        String.format("Charge Model %s cannot have price tiers, needs to point to rate card", chargeModel)
                    );
                }
            }
            case VOLUME, TIERED, BLOCK -> {
                if (CollectionUtils.isEmpty(charge.getPriceTiers())) {
                    throw new InvalidInputException("Price tier is required");
                }

                PriceTiersHelper.validatePriceTiers(charge.getPriceTiers(), charge.getMinQuantityOptional(), charge.getMaxQuantityOptional());
            }
            case PER_UNIT, FLAT_FEE -> {
                // TODO: add price tiers are empty validation check here, for the want of regression the

                if (amount == null) {
                    throw new InvalidInputException("amount is required for PER_UNIT and FLAT_FEE charge models");
                }

                if (amount.compareTo(BigDecimal.ZERO) < 0 && !charge.getIsDiscount()) {
                    throw new InvalidInputException("amount for charge cannot be negative");
                }

                if (amount.compareTo(BigDecimal.ZERO) > 0 && charge.getIsDiscount()) {
                    throw new InvalidInputException("amount for discount charge cannot be positive");
                }

                Numbers.validateInputAmountScale(amount);
            }
        }
    }

    private void validateQuantityConstraints(Charge charge) {
        if (charge.getMinQuantity() == null && charge.getDefaultQuantity() == null && charge.getMaxQuantity() == null) {
            return;
        }

        var type = charge.getType();
        var chargeModel = charge.getChargeModel();
        switch (type) {
            case USAGE -> throw new InvalidInputException(String.format("cannot set quantity constraints for charge type %s", type));
            case PERCENTAGE_OF -> {
                if (chargeModel != PER_UNIT) {
                    throw new InvalidInputException(
                        String.format("cannot set quantity constraints for charge type %s and charge model %s", type, chargeModel)
                    );
                }
            }
        }

        switch (chargeModel) {
            case FLAT_FEE -> throw new InvalidInputException(String.format("cannot set quantity constraints for charge model %s", chargeModel));
            case BLOCK, TIERED, VOLUME -> PriceTiersHelper.validatePriceTiers(
                charge.getPriceTiers(),
                charge.getMinQuantityOptional(),
                charge.getMaxQuantityOptional()
            );
        }

        validateQuantityConstraintIsNotNegative(charge.getMinQuantity(), "minQuantity");
        validateQuantityConstraintIsNotNegative(charge.getMaxQuantity(), "maxQuantity");
        validateQuantityConstraintIsNotNegative(charge.getDefaultQuantity(), "defaultQuantity");

        validateQuantityIsNotGreaterThanIgnoreNull(charge.getMinQuantity(), charge.getMaxQuantity(), "minQuantity", "maxQuantity");
        validateQuantityIsNotGreaterThanIgnoreNull(charge.getMinQuantity(), charge.getDefaultQuantity(), "minQuantity", "defaultQuantity");
        validateQuantityIsNotGreaterThanIgnoreNull(charge.getDefaultQuantity(), charge.getMaxQuantity(), "defaultQuantity", "maxQuantity");
    }

    private void validateQuantityConstraintIsNotNegative(Long quantity, String name) {
        if (quantity != null && quantity < 0L) {
            throw new InvalidInputException(String.format("%s %s cannot be negative", quantity, name));
        }
    }

    private void validateQuantityIsNotGreaterThanIgnoreNull(Long quantity1, Long quantity2, String name1, String name2) {
        if (quantity1 != null && quantity2 != null && quantity1 > quantity2) {
            throw new InvalidInputException(String.format("%s %s cannot be greater than %s %s", quantity1, name1, quantity2, name2));
        }
    }

    void validateChargeRecognitionRule(Charge charge) {
        // We should not prevent the specification of a recognition rule if the feature is enabled but not ready
        // (e.g. if the entity onboarding is in progress)
        // Therefore, we cannot check for readiness here
        Optional<EnabledPlatformFeature> revrecEnabledOptional = platformFeatureServiceProvider
            .get()
            .getFeatureEnablement(PlatformFeature.REVENUE_RECOGNITION);
        if (revrecEnabledOptional.isEmpty()) {
            if (StringUtils.isNotBlank(charge.getRecognitionRuleId())) {
                throw new InvalidInputException("Cannot specify revenue rule until revenue recognition is enabled");
            }

            charge.setRecognitionRuleId(null);
            return;
        }
        String ruleId = charge.getRecognitionRuleId();
        if (charge.getType() == USAGE) {
            if (StringUtils.isNotBlank(ruleId)) {
                throw new InvalidInputException("Usage charges should not have revenue recognition rule");
            }
            return; // if the charge type is usage, do not check for rev rec rules and just return
        }

        if (StringUtils.isBlank(ruleId)) {
            // by this time, we've validated accounting period exists and therefore rev rec rule is required
            throw new InvalidInputException("Revenue recognition rule is required");
        }

        RecognitionRule rule = revenueRecognitionGetServiceProvider
            .get()
            .getRecognitionRuleByRuleId(ruleId)
            .orElseThrow(() -> new InvalidInputException(String.format("Recognition rule with id: '%s' does not exist", ruleId)));

        if (
            !featureService.isEnabled(Feature.REVREC_AMOUNT_BASED_COMPLETION) &&
            rule.getRecognitionType() == RecognitionType.EVENT &&
            charge.getType() != ONE_TIME
        ) {
            throw new InvalidInputException("Event based revenue recognition rule can only be attached to one-time charges");
        }

        Optional<EnabledPlatformFeature> accountingEnabledOptional = platformFeatureServiceProvider
            .get()
            .getFeatureEnablement(PlatformFeature.ACCOUNTING);
        if (accountingEnabledOptional.isEmpty() && rule.getSource() == RecognitionSource.ORDER) {
            throw new IllegalArgumentException("Cannot select Order based revenue recognition rule until accounting is enabled");
        }
    }

    private void validateBillingCycle(Charge charge) {
        // ok if billing cycle is not set
        if (Objects.isNull(charge.getBillingCycle())) {
            return;
        }
        // billing cycle default is allowed for all charges
        if (charge.getBillingCycle() == BillingCycle.DEFAULT) {
            return;
        }

        if (charge.getBillingCycle() == BillingCycle.CHARGE_RECURRENCE && charge.getType() != RECURRING) {
            throw new InvalidInputException("Charge recurrence billing cycle can only be combined with recurring charges");
        }

        // setting billing cycle (other than default) is allowed only for recurring and percentage of charges
        if (charge.getType() == RECURRING || charge.getType() == PERCENTAGE_OF || charge.getType() == PREPAID) {
            return;
        }
        String message = String.format("%s billing cycle is allowed only for recurring or percentage of charges", charge.getBillingCycle().name());
        throw new InvalidInputException(message);
    }

    private void validateUnitOfMeasure(UUID unitOfMeasureId) {
        if (unitOfMeasureId == null) {
            return;
        }
        try {
            var unitOfMeasure = unitOfMeasureService.getUnitOfMeasure(unitOfMeasureId);
            if (unitOfMeasure.getStatus() != UnitOfMeasureStatus.ACTIVE) {
                throw new IllegalArgumentException(String.format("The unit of measure \"%s\" is not active", unitOfMeasure.getName()));
            }
        } catch (ObjectNotFoundException ex) {
            throw new IllegalArgumentException(String.format("Unit of measure with Id %s does not exist", unitOfMeasureId));
        }
    }

    void validatePercentOfCharges(List<Charge> charges) {
        boolean hasPercentOfCharge = charges.stream().anyMatch(charge -> PERCENTAGE_OF == charge.getType());

        if (hasPercentOfCharge && charges.size() > 1) {
            throw new InvalidInputException("No other charges are allowed to be in the plan if percent of charge is present");
        }
    }

    void validateEntityIds(List<Charge> charges, Set<String> entityIds) {
        if (!featureService.isEnabled(Feature.MULTI_ENTITY_AUTH)) {
            charges.forEach(c -> c.setEntityIds(Set.of(EntityContext.ALL_ACCESS_ID)));
            return;
        }

        charges.forEach(c -> {
            if (!SetUtils.isEqualSet(c.getEntityIds(), entityIds)) {
                throw new InvalidInputException("charge entity ids must match the plan entity ids");
            }
        });
    }

    void validateDrawdownCharges(List<Charge> charges) {
        List<Charge> drawdownCharges = charges.stream().filter(charge -> charge.getType() == USAGE && charge.isDrawdown()).toList();

        if (CollectionUtils.isEmpty(drawdownCharges)) {
            return;
        }

        if (drawdownCharges.size() > 1) {
            throw new InvalidInputException("Only one drawdown charge allowed per plan");
        }

        Charge drawdownCharge = drawdownCharges.stream().findFirst().orElseThrow();
        if (PER_UNIT != drawdownCharge.getChargeModel()) {
            throw new InvalidInputException("Only \"Per Unit\" charge model is supported for drawdown charge");
        }

        List<Charge> prepaidCharges = charges.stream().filter(charge -> charge.getType() == PREPAID).toList();

        if (CollectionUtils.isEmpty(prepaidCharges)) {
            throw new InvalidInputException("Prepaid charge is required for a plan containing a drawdown charge");
        }

        prepaidCharges.forEach(prepaidCharge -> {
            if (
                prepaidCharge.getRecurrence() != null &&
                prepaidCharge.getRecurrence().getCycle().compareTo(drawdownCharge.getRecurrence().getCycle()) < 0
            ) {
                throw new InvalidInputException(
                    String.format(
                        "Prepaid charge %s recurrence %s must be greater or equal to usage drawdown charge recurrence %s",
                        prepaidCharge.getChargeId(),
                        prepaidCharge.getRecurrence().getCycle(),
                        drawdownCharge.getRecurrence().getCycle()
                    )
                );
            }
        });
        // TODO: add Unit of Measure comparison and validation once we agree on if UOM is required or not
    }

    void validateOverageCharges(List<Charge> charges) {
        List<Charge> overageCharges = charges.stream().filter(charge -> StringUtils.isNotBlank(charge.getOverageBaseChargeId())).toList();
        Map<String, Charge> chargeMap = charges
            .stream()
            .filter(charge -> StringUtils.isNotBlank(charge.getChargeId()))
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        if (!featureService.isEnabled(Feature.PRECOMMIT_OVERAGE)) {
            if (CollectionUtils.isNotEmpty(overageCharges)) {
                throw new UnsupportedOperationException("Overage charges not enabled");
            }
            return;
        }

        overageCharges.forEach(usageCharge -> {
            String baseChargeId = usageCharge.getOverageBaseChargeId();
            if (!chargeMap.containsKey(baseChargeId)) {
                throw new InvalidInputException("Invalid base charge for overage charge. Target charge must be in the same plan.");
            }
            Charge baseCharge = chargeMap.get(baseChargeId);

            if (baseCharge.getType() != RECURRING || baseCharge.getChargeModel() == FLAT_FEE) {
                throw new InvalidInputException("Overage base charge must be recurring charge type and cannot be Flat Fee charge model");
            }

            if (!usageCharge.getRecurrence().equals(baseCharge.getRecurrence())) {
                throw new InvalidInputException("Overage charge and base charge must have the same price cycles");
            }
        });
    }

    void validateMinimumCommitCharges(List<Charge> charges) {
        List<Charge> usageCharges = charges.stream().filter(charge -> StringUtils.isNotBlank(charge.getMinimumCommitBaseChargeId())).toList();
        Map<String, Charge> chargeMap = charges
            .stream()
            .filter(charge -> StringUtils.isNotBlank(charge.getChargeId()))
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        usageCharges.forEach(usageCharge -> {
            String baseChargeId = usageCharge.getMinimumCommitBaseChargeId();
            if (!chargeMap.containsKey(baseChargeId)) {
                throw new InvalidInputException("Invalid base charge for minimum commit charge. Target charge must be in the same plan.");
            }
            Charge baseCharge = chargeMap.get(baseChargeId);

            if (baseCharge.getType() != RECURRING || baseCharge.getChargeModel() != FLAT_FEE) {
                throw new InvalidInputException(
                    String.format(
                        "Minimum commit base charge must be recurring flat fee, but was %s %s",
                        baseCharge.getType(),
                        baseCharge.getChargeModel()
                    )
                );
            }

            if (!usageCharge.getRecurrence().equals(baseCharge.getRecurrence())) {
                throw new InvalidInputException(
                    String.format(
                        "minimum commit and usage charges must have same price cycles, but were %s and %s",
                        baseCharge.getRecurrence().getCycle(),
                        usageCharge.getRecurrence().getCycle()
                    )
                );
            }

            if (
                baseCharge.getBillingCycle() != BillingCycle.DEFAULT &&
                baseCharge.getBillingCycle().getCycle().orElse(null) != usageCharge.getRecurrence().getCycle()
            ) {
                throw new InvalidInputException(
                    String.format(
                        "minimum commit base charge billing cycle must be %s or %s, but was %s",
                        BillingCycle.DEFAULT.name(),
                        usageCharge.getRecurrence().getCycle().name(),
                        baseCharge.getBillingCycle().name()
                    )
                );
            }
        });
    }

    public void validateIsCreditableForActiveCharge(Charge existingCharge, Charge updateCharge, boolean planInUse) {
        boolean isCreditableFlipped = updateCharge.isCreditable() != existingCharge.isCreditable();
        if (isCreditableFlipped && planInUse) {
            throw new InvalidInputException("cannot change creditable flag for an active charge");
        }
    }
}
