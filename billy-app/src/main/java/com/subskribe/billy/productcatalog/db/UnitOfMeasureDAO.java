package com.subskribe.billy.productcatalog.db;

import static com.subskribe.billy.jooq.default_schema.tables.Charge.CHARGE;
import static com.subskribe.billy.jooq.default_schema.tables.UnitOfMeasure.UNIT_OF_MEASURE;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateObjectException;
import com.subskribe.billy.jooq.default_schema.tables.records.UnitOfMeasureRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.productcatalog.mapper.UnitOfMeasureRecordMapper;
import com.subskribe.billy.productcatalog.model.UnitOfMeasure;
import com.subskribe.billy.productcatalog.model.UnitOfMeasureStatus;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.DbRecordDeletedStatus;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;
import org.jooq.DSLContext;
import org.jooq.exception.DataAccessException;
import org.mapstruct.factory.Mappers;

public class UnitOfMeasureDAO {

    private final UnitOfMeasureRecordMapper unitOfMeasureRecordMapper;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;

    @Inject
    public UnitOfMeasureDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider) {
        unitOfMeasureRecordMapper = Mappers.getMapper(UnitOfMeasureRecordMapper.class);
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
    }

    public UnitOfMeasure addUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = dslContextProvider.get(tenantId);

        var record = unitOfMeasureRecordMapper.toUnitOfMeasureRecord(unitOfMeasure);
        record.setTenantId(tenantId);
        record.reset(UNIT_OF_MEASURE.ID);
        var insertedRecord = dslContext.insertInto(UNIT_OF_MEASURE).set(record).returning().fetchOne();
        return unitOfMeasureRecordMapper.fromUnitOfMeasureRecord(insertedRecord);
    }

    public List<UnitOfMeasure> getUnitsOfMeasure(PaginationQueryParams paginationQueryParams) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext.select().from(UNIT_OF_MEASURE).where(UNIT_OF_MEASURE.TENANT_ID.eq(tenantId)).and(UNIT_OF_MEASURE.IS_DELETED.eq(false));

        var unitOfMeasureRecords = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            UNIT_OF_MEASURE,
            UNIT_OF_MEASURE.ID,
            UNIT_OF_MEASURE.TENANT_ID,
            UNIT_OF_MEASURE.CREATED_ON,
            UnitOfMeasureRecord.class
        );

        return unitOfMeasureRecordMapper.fromUnitOfMeasureRecords(unitOfMeasureRecords);
    }

    public boolean unitOfMeasureUsed(UUID id) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .selectFrom(CHARGE)
            .where(CHARGE.UNIT_OF_MEASURE_ID.eq(id))
            .and(CHARGE.TENANT_ID.eq(tenantId))
            .and(CHARGE.IS_DELETED.isFalse())
            .limit(1)
            .fetchOne();
        return record != null;
    }

    public UnitOfMeasure deleteUnitOfMeasure(UUID id) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .update(UNIT_OF_MEASURE)
            .set(UNIT_OF_MEASURE.IS_DELETED, true)
            .where(UNIT_OF_MEASURE.ID.eq(id))
            .and(UNIT_OF_MEASURE.TENANT_ID.eq(tenantId))
            .and(UNIT_OF_MEASURE.IS_DELETED.isFalse())
            .returning()
            .fetchOne();
        return unitOfMeasureRecordMapper.fromUnitOfMeasureRecord(record);
    }

    public void updateUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        var context = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = unitOfMeasureRecordMapper.toUnitOfMeasureRecord(unitOfMeasure);
        record.setTenantId(tenantIdProvider.provideTenantIdString());

        try {
            context.executeUpdate(record);
        } catch (DataAccessException e) {
            if (PostgresErrorHandler.causeIsUniqueConstraintViolation(e)) {
                throw new DuplicateObjectException(String.format("Unit of measure with name %s already exists", unitOfMeasure.getName()));
            } else {
                throw e;
            }
        }
    }

    public Optional<UnitOfMeasure> updateUnitOfMeasureStatus(UUID id, UnitOfMeasureStatus status) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .update(UNIT_OF_MEASURE)
            .set(UNIT_OF_MEASURE.STATUS, status.toString())
            .where(UNIT_OF_MEASURE.ID.eq(id))
            .and(UNIT_OF_MEASURE.TENANT_ID.eq(tenantId))
            .and(UNIT_OF_MEASURE.IS_DELETED.isFalse())
            .returning()
            .fetchOne();
        return Optional.ofNullable(record).map(unitOfMeasureRecordMapper::fromUnitOfMeasureRecord);
    }

    public List<UnitOfMeasure> getUnitsOfMeasureByIds(List<UUID> ids) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        var records = dslContext
            .selectFrom(UNIT_OF_MEASURE)
            .where(UNIT_OF_MEASURE.ID.in(ids))
            .and(UNIT_OF_MEASURE.TENANT_ID.eq(tenantId))
            .and(UNIT_OF_MEASURE.IS_DELETED.isFalse())
            .fetchInto(UnitOfMeasureRecord.class);
        return records.stream().map(unitOfMeasureRecordMapper::fromUnitOfMeasureRecord).toList();
    }

    public Optional<UnitOfMeasure> getUnitOfMeasure(UUID id) {
        if (id == null) {
            throw new IllegalArgumentException("Unit of measure id is required");
        }

        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .selectFrom(UNIT_OF_MEASURE)
            .where(UNIT_OF_MEASURE.ID.eq(id))
            .and(UNIT_OF_MEASURE.TENANT_ID.eq(tenantId))
            .and(UNIT_OF_MEASURE.IS_DELETED.isFalse())
            .fetchOne();
        return Optional.ofNullable(record).map(unitOfMeasureRecordMapper::fromUnitOfMeasureRecord);
    }

    public Optional<UUID> getUnitOfMeasureIdByName(String unitOfMeasureName) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        // todo: address equalsIgnoreCase
        UUID unitOfMeasureId = dslContext
            .select(UNIT_OF_MEASURE.ID)
            .from(UNIT_OF_MEASURE)
            .where(UNIT_OF_MEASURE.NAME.equalIgnoreCase(unitOfMeasureName))
            .and(UNIT_OF_MEASURE.TENANT_ID.eq(tenantId))
            .and(UNIT_OF_MEASURE.IS_DELETED.isFalse())
            .fetchOneInto(UUID.class);
        return Optional.ofNullable(unitOfMeasureId);
    }

    public DbRecordDeletedStatus getDeletedStatusByName(String name) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        // todo: address equalsIgnoreCase
        UnitOfMeasureRecord record = dslContext
            .selectFrom(UNIT_OF_MEASURE)
            .where(UNIT_OF_MEASURE.NAME.equalIgnoreCase(name))
            .and(UNIT_OF_MEASURE.TENANT_ID.eq(tenantId))
            .fetchOne();
        if (record == null) {
            return DbRecordDeletedStatus.NOT_EXISTS;
        }
        return record.getIsDeleted() ? DbRecordDeletedStatus.EXISTS_DELETED : DbRecordDeletedStatus.EXISTS_NOT_DELETED;
    }

    public UnitOfMeasure undeleteUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);

        UnitOfMeasureRecord record = dslContext
            .update(UNIT_OF_MEASURE)
            .set(UNIT_OF_MEASURE.IS_DELETED, false)
            .set(UNIT_OF_MEASURE.CREATED_ON, now)
            .set(UNIT_OF_MEASURE.UPDATED_ON, now)
            .where(UNIT_OF_MEASURE.NAME.equalIgnoreCase(unitOfMeasure.getName()))
            .and(UNIT_OF_MEASURE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(UNIT_OF_MEASURE.IS_DELETED.isTrue())
            .returning()
            .fetchOne();

        return unitOfMeasureRecordMapper.fromUnitOfMeasureRecord(record);
    }
}
