package com.subskribe.billy.productcatalog.services;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.productcatalog.db.TaxRateDAO;
import com.subskribe.billy.productcatalog.model.TaxRate;
import java.math.BigDecimal;
import java.util.UUID;
import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

public class TaxRateService {

    private final TaxRateDAO taxRateDAO;
    private final TaxRateGetService taxRateGetService;
    private final ProductCatalogGetService productCatalogGetService;
    private final TaxService taxService;
    private final FeatureService featureService;

    @Inject
    public TaxRateService(
        TaxRateDAO taxRateDAO,
        TaxRateGetService taxRateGetService,
        ProductCatalogGetService productCatalogGetService,
        TaxService taxService,
        FeatureService featureService
    ) {
        this.taxRateDAO = taxRateDAO;
        this.taxRateGetService = taxRateGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.taxService = taxService;
        this.featureService = featureService;
    }

    public TaxRate addTaxRate(TaxRate taxRate) {
        validateTaxRate(taxRate);
        return taxRateDAO.addTaxRate(taxRate);
    }

    public void updateTaxRate(TaxRate taxRate) {
        validateTaxRate(taxRate);
        //trigger not found if trying to update non-existing record
        taxRateGetService.getTaxRate(taxRate.getId());
        taxRateMutationBarrier(taxRate.getId());
        taxRateDAO.updateTaxRate(taxRate);
    }

    public TaxRate deleteTaxRate(UUID taxRateId) {
        //trigger not found if trying to delete non-existing record
        taxRateGetService.getTaxRate(taxRateId);
        taxRateMutationBarrier(taxRateId);
        return taxRateDAO.deleteTaxRate(taxRateId);
    }

    private void taxRateMutationBarrier(@NotNull UUID taxRateId) {
        int count = productCatalogGetService.getActiveChargesCountWithTaxRateId(taxRateId);
        if (count > 0) {
            throw new InvalidInputException("Tax rate cannot be edited because it is already in use");
        }
    }

    private void validateTaxRate(TaxRate taxRate) {
        taxRate.validate();

        if (StringUtils.isBlank(taxRate.getTaxCode()) && taxRate.getTaxPercentage() == null) {
            throw new InvalidInputException("tax rate requires either a tax code or a tax percentage, but none was provided");
        }

        if (StringUtils.isNotBlank(taxRate.getTaxCode()) && taxRate.getTaxPercentage() != null) {
            throw new InvalidInputException("cannot add a tax rate with both tax code and tax percentage");
        }

        if (taxRate.isTaxInclusive()) {
            throw new InvalidInputException("cannot add a tax rate that is tax inclusive");
        }

        if (taxService.tenantHasTaxServiceIntegration()) {
            validateWithIntegration(taxRate);
        } else {
            validateWithoutIntegration(taxRate);
        }
    }

    private static void validateWithIntegration(TaxRate taxRate) {
        if (taxRate.getTaxPercentage() != null) {
            throw new InvalidInputException("tax rate percent cannot be present if a tax integration is present");
        }
    }

    private void validateWithoutIntegration(TaxRate taxRate) {
        validateTaxRatePercent(taxRate);
    }

    private void validateTaxRatePercent(TaxRate taxRate) {
        if (taxRate.getTaxPercentage() == null) {
            return;
        }

        if (!featureService.isEnabled(Feature.TAX_PERCENT)) {
            throw new InvalidInputException("cannot add tax rate with percentage because feature is not yet enabled");
        }
        if (taxRate.getTaxPercentage().compareTo(BigDecimal.ZERO) < 0 || taxRate.getTaxPercentage().compareTo(BigDecimal.ONE) > 0) {
            throw new InvalidInputException(
                String.format("tax rate invalid. taxPercentage must be between 0 and 1, but was %s", taxRate.getTaxPercentage())
            );
        }
    }
}
