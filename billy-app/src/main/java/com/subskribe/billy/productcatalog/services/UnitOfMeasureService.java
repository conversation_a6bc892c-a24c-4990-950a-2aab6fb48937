package com.subskribe.billy.productcatalog.services;

import com.subskribe.billy.exception.DuplicateObjectException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.productcatalog.db.UnitOfMeasureDAO;
import com.subskribe.billy.productcatalog.model.UnitOfMeasure;
import com.subskribe.billy.productcatalog.model.UnitOfMeasureStatus;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.DbRecordDeletedStatus;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;

public class UnitOfMeasureService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UnitOfMeasureService.class);

    private final UnitOfMeasureDAO unitOfMeasureDAO;

    @Inject
    public UnitOfMeasureService(UnitOfMeasureDAO unitOfMeasureDAO) {
        this.unitOfMeasureDAO = unitOfMeasureDAO;
    }

    public List<UnitOfMeasure> getUnitsOfMeasure(PaginationQueryParams paginationQueryParams) {
        return unitOfMeasureDAO.getUnitsOfMeasure(paginationQueryParams);
    }

    public UnitOfMeasure addUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        unitOfMeasure.validate();

        DbRecordDeletedStatus dbStatus = unitOfMeasureDAO.getDeletedStatusByName(unitOfMeasure.getName());
        return switch (dbStatus) {
            case EXISTS_NOT_DELETED -> throw new DuplicateObjectException(
                String.format("Unit of measure with name %s already exist", unitOfMeasure.getName())
            );
            case EXISTS_DELETED -> undeleteUnitOfMeasure(unitOfMeasure);
            case NOT_EXISTS -> addNewUnitOfMeasure(unitOfMeasure);
        };
    }

    private UnitOfMeasure undeleteUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        return unitOfMeasureDAO.undeleteUnitOfMeasure(unitOfMeasure);
    }

    private UnitOfMeasure addNewUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        return unitOfMeasureDAO.addUnitOfMeasure(unitOfMeasure);
    }

    public void updateUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        var existingUOM = unitOfMeasureDAO.getUnitOfMeasure(unitOfMeasure.getId());

        if (unitOfMeasure.getId() == null) {
            throw new IllegalArgumentException("id is required");
        }

        if (existingUOM.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.UNIT_OF_MEASURE, unitOfMeasure.getId().toString());
        }

        unitOfMeasure.validate();

        unitOfMeasureDAO.updateUnitOfMeasure(unitOfMeasure);
    }

    public void updateUnitOfMeasureStatus(UUID id, UnitOfMeasureStatus status) {
        var unitOfMeasure = unitOfMeasureDAO.updateUnitOfMeasureStatus(id, status);
        if (unitOfMeasure.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.UNIT_OF_MEASURE, id.toString());
        }
    }

    public UnitOfMeasure deleteUnitOfMeasure(UUID id) {
        if (unitOfMeasureDAO.unitOfMeasureUsed(id)) {
            throw new IllegalArgumentException("Unit of measure in use and cannot be deleted");
        }

        return unitOfMeasureDAO.deleteUnitOfMeasure(id);
    }

    public Map<UUID, UnitOfMeasure> getUnitsOfMeasureByIds(List<UUID> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Map.of();
        }
        List<UnitOfMeasure> unitsOfMeasure = unitOfMeasureDAO.getUnitsOfMeasureByIds(ids);
        return unitsOfMeasure.stream().collect(Collectors.toMap(UnitOfMeasure::getId, Function.identity()));
    }

    public UnitOfMeasure getUnitOfMeasure(UUID id) {
        if (id == null) {
            LOGGER.warn("Unit of measure id is required");
            throw new IllegalArgumentException("Id is required");
        }

        var unitOfMeasure = unitOfMeasureDAO.getUnitOfMeasure(id);

        if (unitOfMeasure.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.UNIT_OF_MEASURE, id.toString());
        }

        return unitOfMeasure.get();
    }

    public Optional<UUID> getUnitOfMeasureIdByName(String unitOfMeasureName) {
        Validator.validateStringNotBlank(unitOfMeasureName, "Unit of measure argument cannot be blank");
        return unitOfMeasureDAO.getUnitOfMeasureIdByName(unitOfMeasureName);
    }
}
