package com.subskribe.billy.productcatalog.services;

import static java.util.stream.Collectors.toList;

import com.subskribe.billy.catalogrelationship.model.CatalogRelationship;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationshipType;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipGetService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.foreignexchange.model.CurrencyTypeSetting;
import com.subskribe.billy.foreignexchange.model.PlanCurrencySettingType;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.foreignexchange.service.CurrencyTypeSettingGetService;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.graphql.productcatalog.PlanDetail;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.productcatalog.db.ChargeDAO;
import com.subskribe.billy.productcatalog.db.ProductCatalogDAO;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.template.services.PlanTermsService;
import com.subskribe.billy.validation.Validator;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.validation.constraints.NotEmpty;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class ProductCatalogGetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductCatalogGetService.class);

    private final ProductCatalogDAO productCatalogDAO;
    private final CatalogRelationshipGetService catalogRelationshipGetService;
    private final PlanTermsService planTermsService;
    private final OrderGetService orderGetService;
    private final CustomFieldService customFieldService;
    private final CurrencyConversionRateGetService currencyConversionRateGetService;
    private final CurrencyTypeSettingGetService currencyTypeSettingGetService;
    private final ChargeDAO chargeDAO;

    @Inject
    public ProductCatalogGetService(
        ProductCatalogDAO productCatalogDAO,
        CatalogRelationshipGetService catalogRelationshipGetService,
        PlanTermsService planTermsService,
        OrderGetService orderGetService,
        CustomFieldService customFieldService,
        CurrencyConversionRateGetService currencyConversionRateGetService,
        CurrencyTypeSettingGetService currencyTypeSettingGetService,
        ChargeDAO chargeDAO
    ) {
        this.productCatalogDAO = productCatalogDAO;
        this.catalogRelationshipGetService = catalogRelationshipGetService;
        this.planTermsService = planTermsService;
        this.orderGetService = orderGetService;
        this.customFieldService = customFieldService;
        this.currencyConversionRateGetService = currencyConversionRateGetService;
        this.currencyTypeSettingGetService = currencyTypeSettingGetService;
        this.chargeDAO = chargeDAO;
    }

    public ProductCategory getProductCategory(String productCategoryId) {
        Validator.validateStringNotBlank(productCategoryId, "productCategoryId is null or empty");

        Optional<ProductCategory> optionalProductCategory = productCatalogDAO.getProductCategory(productCategoryId);
        if (optionalProductCategory.isEmpty()) {
            LOGGER.warn("getProductCategory(): product category with id {} not found", productCategoryId);
            throw new ObjectNotFoundException(BillyObjectType.PRODUCT_CATEGORY, productCategoryId);
        }
        var category = optionalProductCategory.get();
        var inUse = isProductCategoryInUse(category.getProductCategoryId());
        category.setInUse(inUse);

        return category;
    }

    public Optional<ProductCategory> getProductCategoryByName(String name) {
        Optional<ProductCategory> optionalProductCategory = productCatalogDAO.getProductCategoryByName(name);

        if (optionalProductCategory.isEmpty()) {
            return Optional.empty();
        }

        ProductCategory category = optionalProductCategory.get();
        var inUse = isProductCategoryInUse(category.getProductCategoryId());
        category.setInUse(inUse);

        return Optional.of(category);
    }

    public boolean isProductCategoryInUse(String productCategoryId) {
        return productCatalogDAO.isProductCategoryUsedByProduct(productCategoryId);
    }

    public static Map<String, ChargeDetail> getChargeDetailMapFromPlanDetailMap(Map<UUID, PlanDetail> planDetailMap) {
        return planDetailMap
            .values()
            .stream()
            .map(PlanDetail::getCharges)
            .flatMap(List::stream)
            .collect(Collectors.toMap(ChargeDetail::getId, Function.identity(), (first, second) -> first));
    }

    public List<Product> getProducts(PaginationQueryParams paginationQueryParams) {
        var products = productCatalogDAO.getProducts(paginationQueryParams);
        List<String> productIds = products.stream().map(Product::getProductId).distinct().toList();
        Set<String> productIdsInUse = productCatalogDAO.productIdsUsedByPlan(productIds);

        for (Product product : products) {
            var productInUse = productIdsInUse.contains(product.getProductId());
            product.setInUse(productInUse);
        }
        return products;
    }

    public Product getProduct(String productId) {
        Validator.validateStringNotBlank(productId, "productId");

        Optional<Product> optionalProduct = productCatalogDAO.getProduct(productId);
        if (optionalProduct.isEmpty()) {
            LOGGER.warn("getProduct(): product with id {} not found", productId);
            throw new ObjectNotFoundException(BillyObjectType.PRODUCT, productId);
        }
        var product = optionalProduct.get();
        var productInUse = isProductInUse(product.getProductId());
        product.setInUse(productInUse);

        return product;
    }

    boolean isProductInUse(String productId) {
        return productCatalogDAO.isProductUsedByPlan(productId);
    }

    public boolean planExists(String planId) {
        Validator.validateStringNotBlank(planId, "planId is required");

        return productCatalogDAO.planExists(planId);
    }

    public Map<String, Product> getProductMap(Set<String> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Map.of();
        }

        List<Product> products = productCatalogDAO.getProductsByProductIds(productIds);

        if (CollectionUtils.isEmpty(products)) {
            return Map.of();
        }

        return products.stream().collect(Collectors.toMap(Product::getProductId, Function.identity()));
    }

    public Map<String, Plan> getPlanMap(Set<String> planIds, boolean includeRelatedFields) {
        if (CollectionUtils.isEmpty(planIds)) {
            return Map.of();
        }

        List<Plan> plans = includeRelatedFields ? getPlansByPlanIds(planIds) : getPlansByPlanIdsWithoutChecks(planIds);

        if (CollectionUtils.isEmpty(plans)) {
            return Map.of();
        }

        return plans.stream().collect(Collectors.toMap(Plan::getPlanId, Function.identity()));
    }

    public Plan getPlan(String planId) {
        Validator.validateStringNotBlank(planId, "planId");

        Plan plan = productCatalogDAO
            .getPlan(planId)
            .orElseThrow(() -> {
                LOGGER.warn("Plan with planId {} not found", planId);
                return new ObjectNotFoundException(BillyObjectType.PLAN, planId);
            });

        setChargeRelatedFields(plan.getCharges());
        setPlanRelatedFields(List.of(plan));

        return plan;
    }

    private void setPlanRelatedFields(List<Plan> plans) {
        List<String> planIds = plans.stream().map(Plan::getPlanId).toList();
        Map<String, List<String>> planTemplatesMap = planTermsService.getDocumentTemplateIdsAssociatedWithPlans(planIds);
        Map<String, Set<String>> planReplacementPlanIdsMap = catalogRelationshipGetService.getTargetPlanIdsForPlans(
            planIds,
            CatalogRelationshipType.IS_REPLACED_BY
        );
        Map<String, CustomField> plansCustomFieldsMap = customFieldService.getCustomFields(CustomFieldParentType.PLAN, planIds);

        for (Plan plan : plans) {
            plan.setTemplateIds(planTemplatesMap.getOrDefault(plan.getPlanId(), List.of()));
            plan.setReplacementPlanIds(new ArrayList<>(planReplacementPlanIdsMap.getOrDefault(plan.getPlanId(), Set.of())));
            plan.setCustomFields(plansCustomFieldsMap.getOrDefault(plan.getPlanId(), new CustomField(Map.of())));
        }
    }

    public boolean isPlanInUse(String planId) {
        return orderGetService.planUsedByOrder(planId);
    }

    public Set<String> entitiesUsedByOrdersUsingPlan(String planId) {
        return orderGetService.entitiesUsedByOrdersUsingPlan(planId);
    }

    public List<String> getChargesThatUseRateCard(String rateCardId) {
        return productCatalogDAO.getChargesThatUseRateCard(rateCardId);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Plan> getPlans(
        String productId,
        Optional<PlanStatus> planStatus,
        String accountCurrency,
        String orderCurrencyCode,
        PaginationQueryParams paginationQueryParams,
        boolean populateChargeAndTemplates
    ) {
        List<Plan> plans = productCatalogDAO.getPlans(Optional.ofNullable(productId), planStatus, populateChargeAndTemplates, paginationQueryParams);

        plans = getPlansWithSupportedCurrencies(plans, accountCurrency, orderCurrencyCode);

        if (populateChargeAndTemplates) {
            populateTemplateIdsForPlans(plans);
        }

        return plans;
    }

    private List<Plan> getPlansWithSupportedCurrencies(List<Plan> plans, String accountCurrency, String orderCurrencyCode) {
        if (StringUtils.isBlank(accountCurrency) && StringUtils.isBlank(orderCurrencyCode)) {
            return plans;
        }
        String currencyCodeForPlanFilter = StringUtils.isNotBlank(orderCurrencyCode) ? orderCurrencyCode : accountCurrency;
        Set<String> supportedPlanCurrencies = new HashSet<>();
        supportedPlanCurrencies.add(currencyCodeForPlanFilter);
        Optional<CurrencyTypeSetting> currencyTypeSettingOptional = currencyTypeSettingGetService.getCurrencyTypeSetting();
        if (
            currencyTypeSettingOptional.isPresent() &&
            currencyTypeSettingOptional.get().getPlanCurrencySettingType() == PlanCurrencySettingType.LEVERAGE_CURRENCY_CONVERSION
        ) {
            supportedPlanCurrencies.addAll(currencyConversionRateGetService.getSupportedCurrenciesForCurrency(currencyCodeForPlanFilter));
        }
        return plans.stream().filter(plan -> supportedPlanCurrencies.contains(plan.getCurrency().getCurrencyCode())).collect(toList());
    }

    public Plan getPlanByChargeId(String chargeId) {
        List<Plan> plans = getPlansFromChargeIds(List.of(chargeId));
        // we can safely do this here because of contract from getPlansFromChargeIds
        return plans.stream().findFirst().orElseThrow();
    }

    public List<Plan> getPlansByPlanIdsWithoutChecks(Set<String> planIds) {
        if (CollectionUtils.isEmpty(planIds)) {
            return List.of();
        }

        return productCatalogDAO.getPlansByPlanIds(planIds);
    }

    public List<Plan> getAllPlansForProduct(String productId) {
        List<String> planIds = productCatalogDAO.getPlanIdsForProductId(productId);

        if (CollectionUtils.isEmpty(planIds)) {
            return List.of();
        }
        return planIds.stream().distinct().map(this::getPlan).collect(toList());
    }

    public List<Plan> getPlansByPlanIds(Collection<String> planIds) {
        var distinctPlans = new HashSet<>(planIds);
        var plans = getPlansByPlanIdsWithoutChecks(distinctPlans);
        if (plans.size() != distinctPlans.size()) {
            plans.stream().map(Plan::getPlanId).toList().forEach(distinctPlans::remove);
            var message = String.format("invalid plan ids provided: %s", String.join(",", distinctPlans));
            LOGGER.warn(message);
            throw new IllegalArgumentException(message);
        }

        setPlanRelatedFields(plans);

        return plans;
    }

    private void populateTemplateIdsForPlans(List<Plan> plans) {
        List<String> planIds = plans.stream().map(Plan::getPlanId).toList();
        Map<String, List<String>> planTemplates = planTermsService.getDocumentTemplateIdsAssociatedWithPlans(planIds);

        for (Plan plan : plans) {
            List<String> templateIds = planTemplates.getOrDefault(plan.getPlanId(), List.of());
            plan.setTemplateIds(templateIds);
        }
    }

    public List<Plan> getPlansByIds(List<UUID> ids) {
        Validator.validateCollectionElementsNotNull(ids, "plan uuids");

        var distinctPlans = ids.stream().distinct().collect(toList());
        var plans = productCatalogDAO.getPlansByIds(distinctPlans);
        if (plans.size() != distinctPlans.size()) {
            distinctPlans.removeAll(plans.stream().map(Plan::getId).toList());
            var message = String.format("invalid plan ids provided: %s", String.join(",", distinctPlans.toString()));
            LOGGER.warn(message);
            throw new IllegalArgumentException(message);
        }

        return plans;
    }

    public Plan getPlanByUuid(UUID uuid) {
        return getPlansByIds(List.of(uuid))
            .stream()
            .findFirst()
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PLAN, uuid.toString()));
    }

    public List<Plan> getPlansFromChargeIds(List<String> chargeIds) {
        Validator.validateCollectionElementsNotNull(chargeIds, "chargeIds");

        var plans = productCatalogDAO.getPlansByChargeIds(chargeIds);
        var foundCharges = plans.stream().flatMap(p -> p.getCharges().stream().map(Charge::getChargeId)).collect(Collectors.toSet());
        if (!foundCharges.containsAll(chargeIds)) {
            chargeIds.removeAll(foundCharges);
            String message = "Following charges are not found: " + String.join(", ", chargeIds.toString());
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        populateTemplateIdsForPlans(plans);

        return plans;
    }

    public List<Charge> getCharges() {
        return productCatalogDAO.getCharges();
    }

    public Charge getCharge(String planId, String chargeId) {
        Validator.validateStringNotBlank(planId, "planId must be provided");
        Validator.validateStringNotBlank(chargeId, "chargeId must be provided");

        Charge charge = productCatalogDAO
            .getChargeByPlanAndChargeId(planId, chargeId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.CHARGE, chargeId));
        setChargeRelatedFields(List.of(charge));
        return charge;
    }

    private void setChargeRelatedFields(List<Charge> charges) {
        List<String> percentOfChargePlanIds = charges
            .stream()
            .filter(charge -> charge.getType() == ChargeType.PERCENTAGE_OF)
            .map(Charge::getPlanId)
            .toList();
        Map<String, Set<String>> planIdTargetPlansMap = catalogRelationshipGetService.getTargetPlanIdsForPlans(
            percentOfChargePlanIds,
            CatalogRelationshipType.IS_PERCENT_OF
        );

        List<String> chargeIds = charges.stream().map(Charge::getChargeId).toList();
        Map<String, CustomField> chargeCustomFieldsMap = customFieldService.getCustomFields(CustomFieldParentType.CHARGE, chargeIds);

        for (Charge charge : charges) {
            charge.setTargetPlanIds(new ArrayList<>(planIdTargetPlansMap.getOrDefault(charge.getPlanId(), Set.of())));
            charge.setCustomFields(chargeCustomFieldsMap.getOrDefault(charge.getChargeId(), new CustomField(Map.of())));
        }
    }

    public Map<String, Charge> getChargeMapByChargeIds(List<String> chargeIds, boolean includeRelatedFields) {
        if (CollectionUtils.isEmpty(chargeIds)) {
            return Map.of();
        }

        List<Charge> charges = getChargesByChargeId(chargeIds);

        if (charges == null) {
            return Map.of();
        }

        if (includeRelatedFields) {
            setChargeRelatedFields(charges);
        }

        return charges.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
    }

    public Map<String, Charge> getChargeMapByChargeIds(List<String> chargeIds) {
        return getChargeMapByChargeIds(chargeIds, false);
    }

    public List<Charge> getChargesByChargeId(List<String> chargeIds) {
        Validator.validateCollectionNotEmpty(chargeIds, "chargeIds");

        return chargeDAO.getChargesByChargeId(chargeIds);
    }

    public Set<String> getAllCurrenciesUsedInPlans() {
        return productCatalogDAO.getAllCurrenciesUsedInPlans();
    }

    public Optional<String> getPlanIdByExternalId(String externalId) {
        Validator.validateStringNotBlank(externalId, "externalId");
        return productCatalogDAO.getPlanIdByExternalId(externalId);
    }

    public List<String> getPlanIdsByName(String planName) {
        Validator.validateStringNotBlank(planName, "externalId");
        return productCatalogDAO.getLatestPlanIdByName(planName);
    }

    public Optional<String> getProductIdByExternalId(String externalId) {
        Validator.validateStringNotBlank(externalId, "externalId");
        return productCatalogDAO.getProductIdByExternalId(externalId);
    }

    public PageResult<List<String>, String> getProductIdsForTenant(PageRequest<String> pageRequest) {
        return productCatalogDAO.getProductIdsForTenant(pageRequest);
    }

    public Optional<String> getChargeIdByExternalId(String externalId) {
        Validator.validateStringNotBlank(externalId, "externalId");
        return productCatalogDAO.getChargeIdByExternalId(externalId);
    }

    public Optional<String> getPlanExternalIdByPlanId(String planId) {
        Validator.validateStringNotBlank(planId, "planId");
        return productCatalogDAO.getPlanExternalIdByPlanId(planId);
    }

    public int getActiveChargesCountWithTaxRateId(UUID taxRateId) {
        return chargeDAO.getActiveChargesCountWithTaxRateId(taxRateId);
    }

    public List<Charge> getChargesByPlanId(String planId) {
        Plan plan = getPlan(planId);
        return chargeDAO.getChargesByPlan(plan);
    }

    public Charge getChargeByChargeId(String chargeId) {
        Validator.validateStringNotBlank(chargeId, "chargeId is required");

        Charge charge = chargeDAO.getChargeByChargeId(chargeId);
        setChargeRelatedFields(List.of(charge));
        return charge;
    }

    public boolean isRecognitionRuleUsedByCharge(String recognitionRuleId) {
        Validator.validateStringNotBlank(recognitionRuleId, "recognition rule id is null or empty");
        return productCatalogDAO.isRecognitionRuleUsedByCharge(recognitionRuleId);
    }

    public List<Plan> getTargetPlansForPercentOfPlan(String fromPlanId) {
        Set<String> toPlanIds = getTargetPlanIdsForPercentOfPlan(fromPlanId);
        if (CollectionUtils.isEmpty(toPlanIds)) {
            return List.of();
        }

        return getPlansByPlanIdsWithoutChecks(toPlanIds);
    }

    public Set<String> getTargetPlanIdsForPercentOfPlan(String fromPlanId) {
        Validator.validateStringNotBlank(fromPlanId, "plan id cannot be blank");
        Plan plan = getPlan(fromPlanId);
        if (!plan.hasPercentageOfCharge()) {
            String message = String.format("provided plan %s does not have any percent of charges", fromPlanId);
            throw new IllegalArgumentException(message);
        }
        return getTargetPlanIdsForPercentOfPlanWithoutChecks(fromPlanId);
    }

    public Set<String> getTargetPlanIdsForPercentOfPlanWithoutChecks(String fromPlanId) {
        return catalogRelationshipGetService.getTargetPlansIdsByRelationshipType(fromPlanId, CatalogRelationshipType.IS_PERCENT_OF);
    }

    public Set<String> entitiesUsedByPlansOfProduct(String productId) {
        return productCatalogDAO.entitiesUsedByPlansOfProduct(productId);
    }

    public List<Plan> getReplacedPlans(String planId) {
        Validator.validateStringNotBlank(planId, "planId");
        Set<String> planIds = catalogRelationshipGetService
            .getCatalogRelationshipsByToPlanId(planId, CatalogRelationshipType.IS_REPLACED_BY)
            .stream()
            .map(CatalogRelationship::getFromPlanId)
            .collect(Collectors.toUnmodifiableSet());
        if (CollectionUtils.isEmpty(planIds)) {
            return List.of();
        }
        return getPlansByPlanIdsWithoutChecks(planIds);
    }

    // TODO: Add a cache
    public List<Plan> getPlansByCustomField(@NotEmpty String customFieldName, @NotEmpty String customFieldValue) {
        Set<String> planIds = customFieldService.getParentObjectIdsByCustomFieldValues(CustomFieldParentType.PLAN, customFieldName, customFieldValue);

        if (CollectionUtils.isEmpty(planIds)) {
            return List.of();
        }

        List<Plan> plans = getPlansByPlanIdsWithoutChecks(planIds);
        setPlanRelatedFields(plans);
        return plans;
    }
}
