package com.subskribe.billy.productcatalog.mapper;

import com.subskribe.billy.jooq.default_schema.tables.records.TaxRateStrategyRecord;
import com.subskribe.billy.productcatalog.model.TaxRateStrategy;
import com.subskribe.billy.shared.mapper.BaseMapper;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper
public interface TaxRateStrategyRecordMapper extends BaseMapper {
    List<TaxRateStrategy> fromTaxStrategyRecords(List<TaxRateStrategyRecord> taxRateStrategyRecords);
    TaxRateStrategy fromTaxStrategyRecord(TaxRateStrategyRecord taxRateStrategyRecord);
    TaxRateStrategyRecord toTaxStrategyRecord(TaxRateStrategy taxRateStrategy);
}
