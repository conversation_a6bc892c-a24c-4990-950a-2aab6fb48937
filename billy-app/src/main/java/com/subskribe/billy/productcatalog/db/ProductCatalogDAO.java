package com.subskribe.billy.productcatalog.db;

import static com.subskribe.billy.jooq.default_schema.tables.Charge.CHARGE;
import static com.subskribe.billy.jooq.default_schema.tables.Plan.PLAN;
import static com.subskribe.billy.jooq.default_schema.tables.Product.PRODUCT;
import static com.subskribe.billy.jooq.default_schema.tables.ProductCategory.PRODUCT_CATEGORY;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.jooq.default_schema.Indexes;
import com.subskribe.billy.jooq.default_schema.tables.records.ChargeRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.PlanRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.ProductCategoryRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.ProductRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.productcatalog.mapper.PlanRecordMapper;
import com.subskribe.billy.productcatalog.mapper.ProductCategoryRecordMapper;
import com.subskribe.billy.productcatalog.mapper.ProductRecordMapper;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Currency;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class ProductCatalogDAO {

    private static final int MAX_PRODUCT_CATEGORIES = 100;

    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final ProductRecordMapper productRecordMapper;
    private final PlanRecordMapper planRecordMapper;
    private final ChargeDAO chargeDAO;
    private final ProductCategoryRecordMapper productCategoryRecordMapper;

    @Inject
    public ProductCatalogDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider, ChargeDAO chargeDAO) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.chargeDAO = chargeDAO;
        productRecordMapper = Mappers.getMapper(ProductRecordMapper.class);
        planRecordMapper = Mappers.getMapper(PlanRecordMapper.class);
        productCategoryRecordMapper = Mappers.getMapper(ProductCategoryRecordMapper.class);
    }

    public Product addProduct(Product product, Configuration configuration) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = DSL.using(configuration);

        if (!isValidProductCategory(product, tenantDSLContext, tenantId)) {
            throw new IllegalArgumentException("Product category must be null or an existing category");
        }

        var record = productRecordMapper.productToProductRecords(product);
        record.setTenantId(tenantId);
        record.setIsDeleted(false);
        record.reset(PRODUCT.ID);

        if (StringUtils.isBlank(product.getExternalId())) {
            record.setExternalId(null);
        }

        var insertedRecord = PostgresErrorHandler.withConstraintAsConflict(
            () -> tenantDSLContext.insertInto(PRODUCT).set(record).returning().fetchOne(),
            Indexes.INDEX_PRODUCT_EXTERNAL_ID.getName(),
            String.format("Product with external Id: %s already exists", product.getExternalId())
        );

        Product insertedProduct = productRecordMapper.productRecordToProduct(insertedRecord);
        if (product.getProductCategory() != null) {
            ProductCategory hydratedCategory = getProductCategory(product.getProductCategory().getProductCategoryId()).orElseThrow();
            insertedProduct.setProductCategory(hydratedCategory);
        }

        return insertedProduct;
    }

    public void updateProduct(UUID id, Product product, Configuration configuration) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = DSL.using(configuration);

        if (!isValidProductCategory(product, dslContext, tenantId)) {
            throw new IllegalArgumentException("Product category must be null or an existing category");
        }

        var record = productRecordMapper.productToProductRecords(product);
        record.setTenantId(tenantId);
        record.setIsDeleted(false);
        record.setId(id);

        if (StringUtils.isBlank(product.getExternalId())) {
            record.setExternalId(null);
        }

        PostgresErrorHandler.withConstraintAsConflict(
            () -> dslContext.executeUpdate(record),
            Indexes.INDEX_PRODUCT_EXTERNAL_ID.getName(),
            String.format("Product with external Id: %s already exists", product.getExternalId())
        );
    }

    public Product deleteProduct(UUID id, Configuration configuration) {
        var dslContext = DSL.using(configuration);
        var deletedRecord = dslContext
            .update(PRODUCT)
            .set(PRODUCT.IS_DELETED, true)
            .where(PRODUCT.ID.eq(id))
            .and(PRODUCT.IS_DELETED.isFalse())
            .returning()
            .fetchOne();

        if (deletedRecord == null) {
            return null;
        }
        Product deletedProduct = productRecordMapper.productRecordToProduct(deletedRecord);
        deletedProduct.setProductCategory(getProductCategory(deletedRecord.getProductCategoryId()).orElse(null));

        return deletedProduct;
    }

    public List<Product> getProductsByProductIds(Set<String> productIds) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var productRecords = dslContext
            .select()
            .from(PRODUCT)
            .where(PRODUCT.TENANT_ID.eq(tenantId))
            .and(PRODUCT.PRODUCT_ID.in(productIds))
            .and(PRODUCT.IS_DELETED.eq(false))
            .fetchInto(ProductRecord.class);
        List<Product> products = productRecordMapper.productRecordsToProducts(productRecords);

        return populateProductCategory(products);
    }

    public List<Product> getProducts(PaginationQueryParams paginationQueryParams) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext.select().from(PRODUCT).where(PRODUCT.TENANT_ID.eq(tenantId)).and(PRODUCT.IS_DELETED.isFalse());
        var productRecords = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            PRODUCT,
            PRODUCT.ID,
            PRODUCT.TENANT_ID,
            PRODUCT.CREATED_ON,
            ProductRecord.class
        );

        var products = productRecordMapper.productRecordsToProducts(productRecords);

        return populateProductCategory(products);
    }

    private List<Product> populateProductCategory(List<Product> products) {
        Set<String> productCategoryIds = products
            .stream()
            .filter(product -> product.getProductCategory() != null && product.getProductCategory().getProductCategoryId() != null)
            .map(product -> product.getProductCategory().getProductCategoryId())
            .collect(Collectors.toSet());

        Map<String, ProductCategoryRecord> categoryMap = getProductCategoryRecords(productCategoryIds);
        products.forEach(p -> {
            String productCategoryId = p.getProductCategory() != null ? p.getProductCategory().getProductCategoryId() : null;
            if (productCategoryId != null) {
                p.setProductCategory(productCategoryRecordMapper.productCategoryRecordToProductCategory(categoryMap.get(productCategoryId)));
            }
        });

        return products;
    }

    public Optional<Product> getProduct(String productId) {
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var productRecord = tenantDSLContext
            .selectFrom(PRODUCT)
            .where(PRODUCT.PRODUCT_ID.eq(productId))
            .and(PRODUCT.TENANT_ID.eq(tenantId))
            .and(PRODUCT.IS_DELETED.isFalse())
            .fetchOne();

        Product product = productRecordMapper.productRecordToProduct(productRecord);
        if (product != null && product.getProductCategory() != null) {
            product.setProductCategory(getProductCategory(product.getProductCategory().getProductCategoryId()).orElse(null));
        }
        return Optional.ofNullable(product);
    }

    public ProductCategory addProductCategory(ProductCategory productCategory) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = dslContextProvider.get(tenantId);

        var record = productCategoryRecordMapper.productCategoryToProductCategoryRecords(productCategory);
        record.setTenantId(tenantId);
        record.setIsDeleted(false);
        record.reset(PRODUCT_CATEGORY.ID);
        var insertedRecord = PostgresErrorHandler.withConstraintAsConflict(
            () -> tenantDSLContext.insertInto(PRODUCT_CATEGORY).set(record).returning().fetchOne(),
            Indexes.INDEX_PRODUCT_CATEGORY_TENANT_ID_NAME.getName(),
            String.format("Product Category with name: %s already exists", productCategory.getName())
        );
        return productCategoryRecordMapper.productCategoryRecordToProductCategory(insertedRecord);
    }

    public void updateProductCategory(UUID pkId, ProductCategory productCategory) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = dslContextProvider.get(tenantId);
        var record = productCategoryRecordMapper.productCategoryToProductCategoryRecords(productCategory);
        record.setTenantId(tenantId);
        record.setIsDeleted(false);
        record.setId(pkId);

        int recordsUpdated = PostgresErrorHandler.withConstraintAsConflict(
            () -> dslContext.executeUpdate(record),
            Indexes.INDEX_PRODUCT_CATEGORY_TENANT_ID_NAME.getName(),
            String.format("Product Category with name: %s already exists", productCategory.getName())
        );

        if (recordsUpdated != 1) {
            throw new DataAccessException("expected 1 record update got " + recordsUpdated);
        }
    }

    public ProductCategory deleteProductCategory(UUID pkId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var deletedRecord = dslContext
            .update(PRODUCT_CATEGORY)
            .set(PRODUCT_CATEGORY.IS_DELETED, true)
            .where(PRODUCT_CATEGORY.ID.eq(pkId))
            .and(PRODUCT_CATEGORY.IS_DELETED.isFalse())
            .returning()
            .fetchOne();
        return productCategoryRecordMapper.productCategoryRecordToProductCategory(deletedRecord);
    }

    public List<ProductCategory> getProductCategories(PaginationQueryParams paginationQueryParams) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext
            .select()
            .from(PRODUCT_CATEGORY)
            .where(PRODUCT_CATEGORY.TENANT_ID.eq(tenantId))
            .and(PRODUCT_CATEGORY.IS_DELETED.isFalse());

        var productCategoryRecords = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            PRODUCT_CATEGORY,
            PRODUCT_CATEGORY.ID,
            PRODUCT_CATEGORY.TENANT_ID,
            PRODUCT_CATEGORY.CREATED_ON,
            ProductCategoryRecord.class
        );

        return productCategoryRecordMapper.productCategoryRecordsToProductCategories(productCategoryRecords);
    }

    public Optional<String> getProductIdByExternalId(String externalId) {
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        String productId = tenantDSLContext
            .select(PRODUCT.PRODUCT_ID)
            .from(PRODUCT)
            .where(PRODUCT.TENANT_ID.eq(tenantId))
            .and(PRODUCT.EXTERNAL_ID.eq(externalId))
            .and(PRODUCT.IS_DELETED.isFalse())
            .fetchOneInto(String.class);
        return Optional.ofNullable(productId);
    }

    public Optional<ProductCategory> getProductCategory(String productCategoryId) {
        if (StringUtils.isBlank(productCategoryId)) {
            return Optional.empty();
        }

        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var productCategoryRecord = tenantDSLContext
            .selectFrom(PRODUCT_CATEGORY)
            .where(PRODUCT_CATEGORY.PRODUCT_CATEGORY_ID.eq(productCategoryId))
            .and(PRODUCT_CATEGORY.TENANT_ID.eq(tenantId))
            .and(PRODUCT_CATEGORY.IS_DELETED.isFalse())
            .fetchOne();
        return Optional.ofNullable(productCategoryRecordMapper.productCategoryRecordToProductCategory(productCategoryRecord));
    }

    public Optional<ProductCategory> getProductCategoryByName(String name) {
        if (StringUtils.isBlank(name)) {
            throw new InvalidInputException("Product category name is required");
        }

        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var productCategoryRecord = tenantDSLContext
            .selectFrom(PRODUCT_CATEGORY)
            .where(PRODUCT_CATEGORY.NAME.eq(name))
            .and(PRODUCT_CATEGORY.TENANT_ID.eq(tenantId))
            .and(PRODUCT_CATEGORY.IS_DELETED.isFalse())
            .fetchOne();
        return Optional.ofNullable(productCategoryRecordMapper.productCategoryRecordToProductCategory(productCategoryRecord));
    }

    private Map<String, ProductCategoryRecord> getProductCategoryRecords(Set<String> productCategoryIds) {
        if (productCategoryIds == null || productCategoryIds.isEmpty()) {
            return Collections.emptyMap();
        }

        Validator.checkStateInternal(productCategoryIds.size() < MAX_PRODUCT_CATEGORIES, "number of product categories exceeds query maximum");

        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        return tenantDSLContext
            .selectFrom(PRODUCT_CATEGORY)
            .where(PRODUCT_CATEGORY.PRODUCT_CATEGORY_ID.in(productCategoryIds))
            .and(PRODUCT_CATEGORY.TENANT_ID.eq(tenantId))
            .and(PRODUCT_CATEGORY.IS_DELETED.isFalse())
            .fetchMap(PRODUCT_CATEGORY.PRODUCT_CATEGORY_ID);
    }

    public Plan insertPlan(Configuration configuration, Plan plan) {
        var dslContext = DSL.using(configuration);
        var tenantId = tenantIdProvider.provideTenantIdString();

        var record = planRecordMapper.planToPlanRecord(plan);
        record.setTenantId(tenantId);
        record.reset(PLAN.ID);

        var planRecord = PostgresErrorHandler.withConstraintAsConflict(
            () -> dslContext.insertInto(PLAN).set(record).returning().fetchOne(),
            Indexes.INDEX_PLAN_EXTERNAL_ID.getName(),
            String.format("Plan with external Id: %s already exists", plan.getExternalId())
        );

        if (planRecord == null) {
            throw new IllegalStateException("Returned plan record is null");
        }

        return planRecordMapper.planRecordToPlan(planRecord);
    }

    public void updatePlan(DSLContext dslContext, Plan plan) {
        var planRecord = planRecordMapper.planToPlanRecord(plan);
        updatePlanRecord(dslContext, planRecord);
    }

    // update non-transactional fields for plan and charge: name, description, taxRateId
    public void updatePlanMetaData(DSLContext dslContext, Plan plan) {
        PostgresErrorHandler.withConstraintAsConflict(
            () ->
                dslContext
                    .update(PLAN)
                    .set(PLAN.NAME, plan.getName())
                    .set(PLAN.DISPLAY_NAME, plan.getDisplayName())
                    .set(PLAN.DESCRIPTION, plan.getDescription())
                    .set(PLAN.EXTERNAL_ID, plan.getExternalId())
                    .where(PLAN.ID.eq(plan.getId()))
                    .execute(),
            Indexes.INDEX_PLAN_EXTERNAL_ID.getName(),
            String.format("Plan with external Id: %s already exists", plan.getExternalId())
        );
    }

    public void updateEntityIdsForActivePlan(DSLContext dslContext, Plan plan) {
        dslContext.update(PLAN).set(PLAN.ENTITY_IDS, plan.getEntityIds().toArray(new String[0])).where(PLAN.ID.eq(plan.getId())).execute();
    }

    private static int updatePlanRecord(DSLContext context, PlanRecord planRecord) {
        return PostgresErrorHandler.withConstraintAsConflict(
            () -> context.executeUpdate(planRecord),
            Indexes.INDEX_PLAN_EXTERNAL_ID.getName(),
            String.format("Plan with external Id: %s already exists", planRecord.getExternalId())
        );
    }

    public void updateChargeEntitiesToMatchPlan(DSLContext dslContext, Plan plan) {
        dslContext
            .update(CHARGE)
            .set(CHARGE.ENTITY_IDS, plan.getEntityIds().toArray(new String[0]))
            .where(CHARGE.PLAN_ID.eq(plan.getId()))
            .and(CHARGE.IS_DELETED.isFalse())
            .execute();
    }

    public Plan deletePlanAndChargesInTransaction(String planId, Configuration configuration) {
        var optionalPlan = getPlan(planId);

        if (optionalPlan.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.PLAN, planId);
        }

        Plan plan = optionalPlan.get();

        var context = DSL.using(configuration);
        var tenantId = tenantIdProvider.provideTenantIdString();
        context.update(PLAN).set(PLAN.IS_DELETED, true).where(PLAN.PLAN_ID.eq(planId)).and(PLAN.TENANT_ID.eq(tenantId)).execute();
        plan.getCharges().forEach(charge -> chargeDAO.deleteCharge(context, charge.getChargeId(), plan.getId()));

        return plan;
    }

    public Plan updatePlanStatus(Plan plan, PlanStatus currentStatus, PlanStatus newStatus) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var context = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var updatePlanStatusRecord = context
            .update(PLAN)
            .set(PLAN.STATUS, newStatus.name())
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.PLAN_ID.eq(plan.getPlanId()))
            .and(PLAN.STATUS.eq(currentStatus.name()))
            .and(PLAN.IS_DELETED.isFalse())
            .returning()
            .fetchOne();

        if (updatePlanStatusRecord == null) {
            throw new IllegalStateException("unable to update plan status");
        }

        return planRecordMapper.planRecordToPlan(updatePlanStatusRecord);
    }

    public boolean planExists(String planId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return dslContext.fetchExists(
            dslContext.selectFrom(PLAN).where(PLAN.TENANT_ID.eq(tenantId)).and(PLAN.PLAN_ID.eq(planId).and(PLAN.IS_DELETED.eq(false)))
        );
    }

    public Optional<Plan> getPlan(String planId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var planRecords = dslContext
            .select()
            .from(PLAN)
            .where(PLAN.PLAN_ID.eq(planId))
            .and(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.IS_DELETED.eq(false))
            .fetchInto(PlanRecord.class);
        var plans = getPlansWithCharges(planRecordMapper.planRecordsToPlans(planRecords));

        if (plans.isEmpty()) {
            return Optional.empty();
        }

        // shouldn't happen
        if (plans.size() > 1) {
            throw new IllegalStateException("more than one matching record found");
        }

        return Optional.of(plans.get(0));
    }

    public Optional<String> getPlanIdByExternalId(String externalId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        String planId = dslContext
            .select(PLAN.PLAN_ID)
            .from(PLAN)
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.EXTERNAL_ID.eq(externalId))
            .and(PLAN.IS_DELETED.isFalse())
            .fetchOneInto(String.class);
        return Optional.ofNullable(planId);
    }

    public List<String> getLatestPlanIdByName(String planName) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        return dslContext
            .select(PLAN.PLAN_ID)
            .from(PLAN)
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.NAME.equal(planName))
            .and(PLAN.IS_DELETED.isFalse())
            .fetchInto(String.class);
    }

    public Optional<String> getChargeIdByExternalId(String externalId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        String chargeId = dslContext
            .select(CHARGE.CHARGE_ID)
            .from(CHARGE)
            .where(CHARGE.TENANT_ID.eq(tenantId))
            .and(CHARGE.EXTERNAL_ID.eq(externalId))
            .and(CHARGE.IS_DELETED.isFalse())
            .fetchOneInto(String.class);
        return Optional.ofNullable(chargeId);
    }

    public Optional<String> getPlanExternalIdByPlanId(String planId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        String planExternalId = dslContext
            .select(PLAN.EXTERNAL_ID)
            .from(PLAN)
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.PLAN_ID.eq(planId))
            .and(PLAN.IS_DELETED.isFalse())
            .fetchOneInto(String.class);
        return StringUtils.isBlank(planExternalId) ? Optional.empty() : Optional.of(planExternalId);
    }

    private List<Plan> getPlansWithCharges(List<Plan> plans) {
        var planIds = plans.stream().map(Plan::getId).collect(Collectors.toList());
        var charges = chargeDAO.getBulkCharges(planIds);
        var groupedCharges = charges.stream().collect(Collectors.groupingBy(Charge::getPlanUuid));

        plans.forEach(plan -> plan.setCharges(groupedCharges.getOrDefault(plan.getId(), List.of())));
        return plans;
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Plan> getPlans(
        Optional<String> productId,
        Optional<PlanStatus> planStatus,
        boolean includeCharges,
        PaginationQueryParams paginationQueryParams
    ) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext.select().from(PLAN).where(PLAN.TENANT_ID.eq(tenantId)).and(PLAN.IS_DELETED.eq(false));

        if (productId.isPresent()) {
            query = query.and(PLAN.PRODUCT_ID.eq(productId.get()));
        }

        if (planStatus.isPresent()) {
            query = query.and(PLAN.STATUS.eq(planStatus.get().name()));
        }

        var planRecords = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            PLAN,
            PLAN.ID,
            PLAN.TENANT_ID,
            PLAN.CREATED_ON,
            PlanRecord.class
        );

        var plans = planRecordMapper.planRecordsToPlans(planRecords);

        return includeCharges ? getPlansWithCharges(plans) : plans;
    }

    public List<String> getPlanIdsForProductId(String productId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return dslContext
            .select(PLAN.PLAN_ID)
            .from(PLAN)
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.PRODUCT_ID.eq(productId))
            .and(PLAN.IS_DELETED.isFalse())
            .fetchInto(String.class);
    }

    public List<Plan> getPlansByPlanIds(Collection<String> planIds) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var planRecords = dslContext
            .select()
            .from(PLAN)
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.PLAN_ID.in(planIds))
            .and(PLAN.IS_DELETED.eq(false))
            .fetchInto(PlanRecord.class);
        return getPlansWithCharges(planRecordMapper.planRecordsToPlans(planRecords));
    }

    public List<Plan> getPlansByIds(List<UUID> ids) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var planRecords = dslContext
            .select()
            .from(PLAN)
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.ID.in(ids))
            .and(PLAN.IS_DELETED.eq(false))
            .fetchInto(PlanRecord.class);
        return getPlansWithCharges(planRecordMapper.planRecordsToPlans(planRecords));
    }

    public List<Plan> getPlansByChargeIds(List<String> chargeIds) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var planIds = dslContext
            .selectDistinct(CHARGE.PLAN_ID)
            .from(CHARGE)
            .where(CHARGE.TENANT_ID.eq(tenantId))
            .and(CHARGE.CHARGE_ID.in(chargeIds))
            .and(CHARGE.IS_DELETED.eq(false));
        var planRecords = dslContext
            .select()
            .from(PLAN)
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.ID.in(planIds))
            .and(PLAN.IS_DELETED.eq(false))
            .fetchInto(PlanRecord.class);
        return getPlansWithCharges(planRecordMapper.planRecordsToPlans(planRecords));
    }

    public void ensureUniqueProductId(String productId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(PRODUCT)
            .where(PRODUCT.PRODUCT_ID.eq(productId))
            .and(PRODUCT.TENANT_ID.eq(tenantId))
            .fetchOneInto(ProductRecord.class);

        if (record == null) {
            return;
        }

        throwDuplicateProductOrPlanIdException(productId, "productId");
    }

    public Optional<Charge> getChargeByPlanAndChargeId(String planId, String chargeId) {
        var plan = getPlan(planId);
        return plan.flatMap(p -> p.getCharges().stream().filter(c -> c.getChargeId().equals(chargeId)).findFirst());
    }

    public void ensureUniqueProductCategoryId(String productCategoryId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(PRODUCT_CATEGORY)
            .where(PRODUCT_CATEGORY.PRODUCT_CATEGORY_ID.eq(productCategoryId))
            .and(PRODUCT_CATEGORY.TENANT_ID.eq(tenantId))
            .fetchOneInto(ProductRecord.class);

        if (record == null) {
            return;
        }

        throwDuplicateProductOrPlanIdException(productCategoryId, "productCategoryId");
    }

    public void ensureUniquePlanId(String planId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext.select().from(PLAN).where(PLAN.PLAN_ID.eq(planId)).and(PLAN.TENANT_ID.eq(tenantId)).fetchOneInto(ProductRecord.class);

        if (record == null) {
            return;
        }

        throwDuplicateProductOrPlanIdException(planId, "planId");
    }

    public boolean isProductUsedByPlan(String productId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext.fetchExists(
            dslContext.selectOne().from(PLAN).where(PLAN.PRODUCT_ID.eq(productId).and(PLAN.TENANT_ID.eq(tenantId)).and(PLAN.IS_DELETED.isFalse()))
        );
    }

    // get a list of products that have attached plans
    public Set<String> productIdsUsedByPlan(List<String> productIds) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext
            .selectDistinct(PLAN.PRODUCT_ID)
            .from(PLAN)
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.PRODUCT_ID.in(productIds))
            .and(PLAN.IS_DELETED.isFalse())
            .fetchSet(PLAN.PRODUCT_ID);
    }

    public boolean isProductCategoryUsedByProduct(String productCategoryId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(PRODUCT)
                .where(PRODUCT.PRODUCT_CATEGORY_ID.eq(productCategoryId))
                .and(PRODUCT.TENANT_ID.eq(tenantId))
                .and(PRODUCT.IS_DELETED.isFalse())
        );
    }

    public boolean isRecognitionRuleUsedByCharge(String recognitionRuleId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(CHARGE)
                .where(CHARGE.RECOGNITION_RULE_ID.eq(recognitionRuleId))
                .and(CHARGE.TENANT_ID.eq(tenantId))
                .and(CHARGE.IS_DELETED.isFalse())
        );
    }

    private void throwDuplicateProductOrPlanIdException(String id, String fieldName) {
        var message = String.format("Duplicate %s generated. %s = %s", fieldName, fieldName, id);
        throw new DuplicateIdException(message);
    }

    public Set<String> getAllCurrenciesUsedInPlans() {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        List<PlanRecord> records = dslContext
            .select()
            .from(PLAN)
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.IS_DELETED.isFalse())
            .fetchInto(PlanRecord.class);

        List<Plan> plans = planRecordMapper.planRecordsToPlans(records);
        return plans.stream().map(Plan::getCurrency).map(Currency::getCurrencyCode).collect(Collectors.toSet());
    }

    /**
     * If the product has a category set make sure it is in the list of valid
     * categories for the particular tenant
     */
    private boolean isValidProductCategory(Product product, DSLContext context, String tenantId) {
        if (product.getProductCategory() == null || product.getProductCategory().getProductCategoryId() == null) {
            return true;
        }

        String productCategoryId = product.getProductCategory().getProductCategoryId();

        Integer count = context
            .selectCount()
            .from(PRODUCT_CATEGORY)
            .where(PRODUCT_CATEGORY.TENANT_ID.eq(tenantId))
            .and(PRODUCT_CATEGORY.PRODUCT_CATEGORY_ID.eq(productCategoryId))
            .and(PRODUCT_CATEGORY.IS_DELETED.isFalse())
            .fetchOne(0, Integer.class);

        return count != null && count > 0;
    }

    public List<Charge> getCharges() {
        DSLContext context = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        List<ChargeRecord> chargeRecords = context
            .select()
            .from(CHARGE)
            .where(CHARGE.IS_DELETED.isFalse())
            .and(CHARGE.TENANT_ID.eq(tenantId))
            .fetchInto(ChargeRecord.class);
        return planRecordMapper.chargeRecordsToCharges(chargeRecords);
    }

    public PageResult<List<String>, String> getProductIdsForTenant(PageRequest<String> pageRequest) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var queryStep = dslContext.select(PRODUCT.PRODUCT_ID).from(PRODUCT).where(PRODUCT.TENANT_ID.eq(tenantId)).and(PRODUCT.IS_DELETED.isFalse());

        if (pageRequest.getPageToken() != null) {
            queryStep = queryStep.and(PRODUCT.PRODUCT_ID.gt(pageRequest.getPageToken()));
        }

        List<String> productIds = queryStep.orderBy(PRODUCT.PRODUCT_ID).limit(pageRequest.getLimit()).fetchInto(String.class).stream().toList();
        return PageResult.fromCollectionAndRequest(productIds, pageRequest, Function.identity());
    }

    public List<String> getChargesThatUseRateCard(String rateCardId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return dslContext
            .select(CHARGE.CHARGE_ID)
            .from(CHARGE)
            .where(CHARGE.RATE_CARD_ID.eq(rateCardId))
            .and(CHARGE.TENANT_ID.eq(tenantId))
            .and(CHARGE.IS_DELETED.isFalse())
            .fetchInto(String.class);
    }

    public Set<String> entitiesUsedByPlansOfProduct(String productId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<PlanRecord> plans = dslContext
            .select()
            .from(PLAN)
            .where(PLAN.TENANT_ID.eq(tenantId))
            .and(PLAN.PRODUCT_ID.eq(productId))
            .and(PLAN.IS_DELETED.isFalse())
            .fetchInto(PlanRecord.class);
        return plans.stream().map(PlanRecord::getEntityIds).flatMap(Arrays::stream).collect(Collectors.toSet());
    }

    public String getProductNameByChargeId(String chargeId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext
            .select(PRODUCT.NAME)
            .from(CHARGE)
            .join(PLAN)
            .on(PLAN.TENANT_ID.eq(tenantId).and(CHARGE.PLAN_ID.eq(PLAN.ID)))
            .join(PRODUCT)
            .on(PRODUCT.TENANT_ID.eq(tenantId).and(PLAN.PRODUCT_ID.eq(PRODUCT.PRODUCT_ID)))
            .where(CHARGE.TENANT_ID.eq(tenantId))
            .and(CHARGE.CHARGE_ID.eq(chargeId))
            .and(CHARGE.IS_DELETED.isFalse())
            .fetchOneInto(String.class);
    }

    public Map<String, String> getChargeIdToProductNameMap(List<String> chargeIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext
            .select(CHARGE.CHARGE_ID, PRODUCT.NAME)
            .from(CHARGE)
            .join(PLAN)
            .on(PLAN.TENANT_ID.eq(tenantId).and(CHARGE.PLAN_ID.eq(PLAN.ID)))
            .join(PRODUCT)
            .on(PRODUCT.TENANT_ID.eq(tenantId).and(PLAN.PRODUCT_ID.eq(PRODUCT.PRODUCT_ID)))
            .where(CHARGE.TENANT_ID.eq(tenantId))
            .and(CHARGE.CHARGE_ID.in(chargeIds))
            .and(CHARGE.IS_DELETED.isFalse())
            .fetchMap(CHARGE.CHARGE_ID, PRODUCT.NAME);
    }

    public String getPlanNameByChargeId(String chargeId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext
            .select(PLAN.NAME)
            .from(CHARGE)
            .join(PLAN)
            .on(PLAN.TENANT_ID.eq(tenantId).and(CHARGE.PLAN_ID.eq(PLAN.ID)))
            .where(CHARGE.TENANT_ID.eq(tenantId))
            .and(CHARGE.CHARGE_ID.eq(chargeId))
            .and(CHARGE.IS_DELETED.isFalse())
            .fetchOneInto(String.class);
    }

    public Map<String, String> getChargeIdToPlanNameMap(List<String> chargeIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext
            .select(CHARGE.CHARGE_ID, PLAN.NAME)
            .from(CHARGE)
            .join(PLAN)
            .on(PLAN.TENANT_ID.eq(tenantId).and(CHARGE.PLAN_ID.eq(PLAN.ID)))
            .where(CHARGE.TENANT_ID.eq(tenantId))
            .and(CHARGE.CHARGE_ID.in(chargeIds))
            .and(CHARGE.IS_DELETED.isFalse())
            .fetchMap(CHARGE.CHARGE_ID, PLAN.NAME);
    }
}
