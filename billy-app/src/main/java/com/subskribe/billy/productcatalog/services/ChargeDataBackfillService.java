package com.subskribe.billy.productcatalog.services;

import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.BULK_UPDATE_CHARGE_REV_REC_FIELDS_REQUEST_HEADERS;
import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.BULK_UPDATE_CHARGE_REV_REC_FIELDS_RESPONSE_HEADERS;
import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.CHARGE_ID_HEADER;
import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.CHARGE_NAME_HEADER;
import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.LEDGER_ACCOUNT_CONTRACT_ASSET_HEADER;
import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.LEDGER_ACCOUNT_DEFERRED_REVENUE_HEADER;
import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.LEDGER_ACCOUNT_RECOGNIZED_REVENUE_HEADER;
import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.LEDGER_ACCOUNT_TAX_LIABILITY_HEADER;
import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.PLAN_NAME_HEADER;
import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.PRODUCT_NAME_HEADER;
import static com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsRequestCsvSchema.RECOGNITION_RULE_NAME_HEADER;

import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.productcatalog.db.ProductCatalogDAO;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.ImmutableUpdateChargeRevRecFieldsDetail;
import com.subskribe.billy.productcatalog.model.LedgerAccountMapping;
import com.subskribe.billy.productcatalog.model.UpdateChargeRevRecFieldsDetail;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.shared.csv.SecureCSVPrinter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.jooq.DSLContext;

public class ChargeDataBackfillService {

    private static final String CSV_UPLOAD_MEDIA_TYPE = "text/csv";
    private final ProductCatalogDAO productCatalogDAO;
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final RevenueRecognitionGetService revenueRecognitionGetService;
    private final AccountingGetService accountingGetService;
    private final AccountingService accountingService;

    private static final int MAX_CHARGES_PER_SUBMISSION = 200;
    private static final CSVFormat BULK_CHARGE_UPDATE_REV_REC_FIELDS_CSV_FORMAT = CSVFormat.DEFAULT.builder()
        .setHeader(BULK_UPDATE_CHARGE_REV_REC_FIELDS_REQUEST_HEADERS.toArray(String[]::new))
        .setAllowMissingColumnNames(false)
        .setDuplicateHeaderMode(DuplicateHeaderMode.DISALLOW)
        .setTrim(true)
        .build();

    @Inject
    public ChargeDataBackfillService(
        ProductCatalogDAO productCatalogDAO,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        RevenueRecognitionGetService revenueRecognitionGetService,
        AccountingGetService accountingGetService,
        AccountingService accountingService
    ) {
        this.productCatalogDAO = productCatalogDAO;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.revenueRecognitionGetService = revenueRecognitionGetService;
        this.accountingGetService = accountingGetService;
        this.accountingService = accountingService;
    }

    public Pair<InputStream, String> updateChargeRevRecFields(FormDataBodyPart formDataBodyPart, InputStream inputStream, boolean isDryRun) {
        if (inputStream == null) {
            throw new InvalidInputException("uploaded csv stream to update charge revenue recognition fields is null");
        }

        String contentMediaType = formDataBodyPart.getMediaType().toString();
        if (!StringUtils.equalsIgnoreCase(contentMediaType, CSV_UPLOAD_MEDIA_TYPE)) {
            throw new IllegalArgumentException("Expected text/csv media type. Media type found: " + contentMediaType);
        }

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            // step 1: extract the csv
            List<UpdateChargeRevRecFieldsDetail> bulkUpdateRequests = extractUpdateChargeRevRecFieldsRequestRows(reader);

            // step 2: perform the operation
            List<UpdateChargeRevRecFieldsDetail> updatedResponses = processBulkUpdateChargeRevRecFieldsRequest(bulkUpdateRequests, isDryRun);

            // step 3: transform the response back to csv output
            return generateCsvResponseForUpdateRevRecFields(updatedResponses);
        } catch (IOException e) {
            throw new InvalidInputException(e.getMessage());
        }
    }

    private Pair<InputStream, String> generateCsvResponseForUpdateRevRecFields(List<UpdateChargeRevRecFieldsDetail> updatedResponses)
        throws IOException {
        try (
            ByteArrayOutputStream writeStream = new ByteArrayOutputStream();
            OutputStreamWriter streamWriter = new OutputStreamWriter(writeStream, StandardCharsets.UTF_8);
            SecureCSVPrinter csvPrinter = SecureCSVPrinter.wrap(CSVFormat.DEFAULT.print(streamWriter))
        ) {
            csvPrinter.printRecord(BULK_UPDATE_CHARGE_REV_REC_FIELDS_RESPONSE_HEADERS);
            updatedResponses.forEach(response -> {
                List<String> row = new ArrayList<>();
                row.add(response.getProductName());
                row.add(response.getPlanName());
                row.add(response.getChargeId());
                row.add(response.getChargeName());
                row.add(response.getRequestedRecognitionRuleName());
                row.add(response.getPreviousRecognitionRuleName());
                row.add(response.getUpdatedRecognitionRuleName());
                row.add(response.getRequestedLedgerAccountDeferredRevenue());
                row.add(response.getPreviousLedgerAccountDeferredRevenue());
                row.add(response.getUpdatedLedgerAccountDeferredRevenue());
                row.add(response.getRequestedLedgerAccountRecognizedRevenue());
                row.add(response.getPreviousLedgerAccountRecognizedRevenue());
                row.add(response.getUpdatedLedgerAccountRecognizedRevenue());
                row.add(response.getRequestedLedgerAccountContractAsset());
                row.add(response.getPreviousLedgerAccountContractAsset());
                row.add(response.getUpdatedLedgerAccountContractAsset());
                row.add(response.getRequestedLedgerAccountTaxLiability());
                row.add(response.getPreviousLedgerAccountTaxLiability());
                row.add(response.getUpdatedLedgerAccountTaxLiability());
                row.add(String.valueOf(response.isFailed()));
                row.add(response.getFailureReason());

                try {
                    csvPrinter.printRecord(row);
                } catch (IOException e) {
                    throw new ServiceFailureException(e);
                }
            });
            csvPrinter.flush();
            ByteBuffer byteBuffer = ByteBuffer.wrap(writeStream.toByteArray());
            InputStream inputStream = new ByteArrayInputStream(byteBuffer.array());
            return Pair.of(inputStream, "BulkUpdateRevRecFieldsOnCharges_" + Instant.now().getEpochSecond() + ".csv");
        } catch (IOException e) {
            throw new ServiceFailureException(e);
        }
    }

    private List<UpdateChargeRevRecFieldsDetail> processBulkUpdateChargeRevRecFieldsRequest(
        List<UpdateChargeRevRecFieldsDetail> updateRequests,
        boolean isDryRun
    ) {
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        List<UpdateChargeRevRecFieldsDetail> bulkUpdateResponses = new ArrayList<>();
        for (UpdateChargeRevRecFieldsDetail request : updateRequests) {
            performUpdateRevRecFieldsOnCharge(isDryRun, tenantDslContext, bulkUpdateResponses, request);
        }

        return bulkUpdateResponses;
    }

    private void performUpdateRevRecFieldsOnCharge(
        boolean isDryRun,
        DSLContext tenantDslContext,
        List<UpdateChargeRevRecFieldsDetail> bulkUpdateResponses,
        UpdateChargeRevRecFieldsDetail request
    ) {
        Charge originalCharge;
        Optional<LedgerAccountMapping> originalLedgerAccountMappings;
        try {
            originalCharge = productCatalogDAO.getChargeByChargeId(request.getChargeId());
            originalLedgerAccountMappings = getLedgerAccountMappings(originalCharge);
        } catch (ObjectNotFoundException ex) {
            UpdateChargeRevRecFieldsDetail response = ImmutableUpdateChargeRevRecFieldsDetail.builder()
                .chargeId(request.getChargeId())
                .requestedRecognitionRuleName(request.getRequestedRecognitionRuleName())
                .requestedLedgerAccountDeferredRevenue(request.getRequestedLedgerAccountDeferredRevenue())
                .requestedLedgerAccountRecognizedRevenue(request.getRequestedLedgerAccountRecognizedRevenue())
                .requestedLedgerAccountContractAsset(request.getRequestedLedgerAccountContractAsset())
                .requestedLedgerAccountTaxLiability(request.getRequestedLedgerAccountTaxLiability())
                .dryRun(isDryRun)
                .failed(true)
                .failureReason("Charge not found")
                .build();

            bulkUpdateResponses.add(response);
            return;
        }

        boolean failed = false;
        String failureReason = null;
        Charge updatedCharge = null;
        Optional<LedgerAccountMapping> updatedLedgerAccountMappings = Optional.empty();
        if (!isDryRun) {
            try {
                originalCharge.setRecognitionRuleId(
                    revenueRecognitionGetService.getRecognitionRuleIdByName(request.getRequestedRecognitionRuleName()).orElse(null)
                );
                productCatalogDAO.reattachRecognitionRuleToCharge(tenantDslContext, originalCharge);
                updatedCharge = productCatalogDAO.getChargeByChargeId(request.getChargeId());
                LedgerAccountMapping ledgerAccountMapping = getLedgerAccountMappingsIfPresent(request);
                populateLedgerAccountMappings(request.getChargeId(), ledgerAccountMapping);
                updatedLedgerAccountMappings = getLedgerAccountMappings(updatedCharge);
            } catch (Exception ex) {
                failed = true;
                failureReason = ex.getMessage();
            }
        }
        String productName = productCatalogDAO.getProductNameByChargeId(request.getChargeId());
        String planName = productCatalogDAO.getPlanNameByChargeId(request.getChargeId());
        String previousRecognitionRuleName = revenueRecognitionGetService
            .getRecognitionRuleByRuleId(originalCharge.getRecognitionRuleId())
            .map(RecognitionRule::getName)
            .orElse(StringUtils.EMPTY);
        String updatedRecognitionRuleName = isDryRun || updatedCharge == null
            ? request.getRequestedRecognitionRuleName()
            : revenueRecognitionGetService
                .getRecognitionRuleByRuleId(updatedCharge.getRecognitionRuleId())
                .map(RecognitionRule::getName)
                .orElse(StringUtils.EMPTY);
        String previousLedgerAccountDeferredRevenue = getLedgerAccountCodeIfPresent(
            originalLedgerAccountMappings.map(LedgerAccountMapping::getDeferredRevenueAccountId)
        );
        String updatedLedgerAccountDeferredRevenue = isDryRun || updatedCharge == null
            ? request.getRequestedLedgerAccountDeferredRevenue()
            : getLedgerAccountCodeIfPresent(updatedLedgerAccountMappings.map(LedgerAccountMapping::getDeferredRevenueAccountId));
        String previousLedgerAccountRecognizedRevenue = getLedgerAccountCodeIfPresent(
            originalLedgerAccountMappings.map(LedgerAccountMapping::getRecognizedRevenueAccountId)
        );
        String updatedLedgerAccountRecognizedRevenue = isDryRun || updatedCharge == null
            ? request.getRequestedLedgerAccountRecognizedRevenue()
            : getLedgerAccountCodeIfPresent(updatedLedgerAccountMappings.map(LedgerAccountMapping::getRecognizedRevenueAccountId));
        String previousLedgerAccountContractAsset = getLedgerAccountCodeIfPresent(
            originalLedgerAccountMappings.map(LedgerAccountMapping::getContractAssetAccountId)
        );
        String updatedLedgerAccountContractAsset = isDryRun || updatedCharge == null
            ? request.getRequestedLedgerAccountContractAsset()
            : getLedgerAccountCodeIfPresent(updatedLedgerAccountMappings.map(LedgerAccountMapping::getContractAssetAccountId));
        String previousLedgerAccountTaxLiability = getLedgerAccountCodeIfPresent(
            originalLedgerAccountMappings.map(LedgerAccountMapping::getTaxLiabilityAccountId)
        );
        String updatedLedgerAccountTaxLiability = isDryRun || updatedCharge == null
            ? request.getRequestedLedgerAccountTaxLiability()
            : getLedgerAccountCodeIfPresent(updatedLedgerAccountMappings.map(LedgerAccountMapping::getTaxLiabilityAccountId));
        UpdateChargeRevRecFieldsDetail response = ImmutableUpdateChargeRevRecFieldsDetail.builder()
            .productName(productName)
            .planName(planName)
            .chargeId(request.getChargeId())
            .chargeName(originalCharge.getName())
            .requestedRecognitionRuleName(request.getRequestedRecognitionRuleName())
            .previousRecognitionRuleName(previousRecognitionRuleName)
            .updatedRecognitionRuleName(updatedRecognitionRuleName)
            .requestedLedgerAccountDeferredRevenue(request.getRequestedLedgerAccountDeferredRevenue())
            .previousLedgerAccountDeferredRevenue(previousLedgerAccountDeferredRevenue)
            .updatedLedgerAccountDeferredRevenue(updatedLedgerAccountDeferredRevenue)
            .requestedLedgerAccountRecognizedRevenue(request.getRequestedLedgerAccountRecognizedRevenue())
            .previousLedgerAccountRecognizedRevenue(previousLedgerAccountRecognizedRevenue)
            .updatedLedgerAccountRecognizedRevenue(updatedLedgerAccountRecognizedRevenue)
            .requestedLedgerAccountContractAsset(request.getRequestedLedgerAccountContractAsset())
            .previousLedgerAccountContractAsset(previousLedgerAccountContractAsset)
            .updatedLedgerAccountContractAsset(updatedLedgerAccountContractAsset)
            .requestedLedgerAccountTaxLiability(request.getRequestedLedgerAccountTaxLiability())
            .previousLedgerAccountTaxLiability(previousLedgerAccountTaxLiability)
            .updatedLedgerAccountTaxLiability(updatedLedgerAccountTaxLiability)
            .dryRun(isDryRun)
            .failed(failed)
            .failureReason(failureReason)
            .build();

        bulkUpdateResponses.add(response);
    }

    private LedgerAccountMapping getLedgerAccountMappingsIfPresent(UpdateChargeRevRecFieldsDetail request) {
        List<LedgerAccount> ledgerAccounts = new ArrayList<>();

        String deferredRevenueNameOrCode = request.getRequestedLedgerAccountDeferredRevenue();
        getLedgerAccountForImport(deferredRevenueNameOrCode).ifPresent(ledgerAccounts::add);

        String recognizedRevenueNameOrCode = request.getRequestedLedgerAccountRecognizedRevenue();
        getLedgerAccountForImport(recognizedRevenueNameOrCode).ifPresent(ledgerAccounts::add);

        String contractAssetNameOrCode = request.getRequestedLedgerAccountContractAsset();
        getLedgerAccountForImport(contractAssetNameOrCode).ifPresent(ledgerAccounts::add);

        String taxLiabilityNameOrCode = request.getRequestedLedgerAccountTaxLiability();
        getLedgerAccountForImport(taxLiabilityNameOrCode).ifPresent(ledgerAccounts::add);

        if (CollectionUtils.isNotEmpty(ledgerAccounts)) {
            return new LedgerAccountMapping(ledgerAccounts);
        }
        return null;
    }

    private Optional<LedgerAccount> getLedgerAccountForImport(String ledgerAccountNameOrCode) {
        if (StringUtils.isBlank(ledgerAccountNameOrCode)) {
            return Optional.empty();
        }
        return accountingGetService
            .getLedgerAccountByCode(ledgerAccountNameOrCode)
            .or(() -> accountingGetService.getLedgerAccountByName(ledgerAccountNameOrCode));
    }

    private void populateLedgerAccountMappings(String chargeId, LedgerAccountMapping ledgerAccountMapping) {
        if (ledgerAccountMapping == null) {
            return;
        }
        accountingService.mapLedgerAccountsToCharge(chargeId, ledgerAccountMapping.getLedgerAccountIds());
    }

    private List<UpdateChargeRevRecFieldsDetail> extractUpdateChargeRevRecFieldsRequestRows(BufferedReader csvReader) throws IOException {
        Iterator<CSVRecord> records = BULK_CHARGE_UPDATE_REV_REC_FIELDS_CSV_FORMAT.parse(csvReader).iterator();
        // no data simply return with empty
        if (!records.hasNext()) {
            return List.of();
        }

        // should match header
        List<String> headersFromFile = records.next().toList();
        if (!headersFromFile.equals(BULK_UPDATE_CHARGE_REV_REC_FIELDS_REQUEST_HEADERS)) {
            String message = String.format(
                "Column list in file %s does not match expected column list %s",
                headersFromFile,
                BULK_UPDATE_CHARGE_REV_REC_FIELDS_REQUEST_HEADERS
            );
            throw new InvalidInputException(message);
        }

        List<UpdateChargeRevRecFieldsDetail> updateRequests = new ArrayList<>();
        while (records.hasNext()) {
            updateRequests.add(extractUpdateChargeRevRecFieldsCsvInputRow(records.next()));
        }

        return updateRequests;
    }

    private UpdateChargeRevRecFieldsDetail extractUpdateChargeRevRecFieldsCsvInputRow(CSVRecord record) {
        long rowNum = record.getRecordNumber() - 1;
        if (rowNum > MAX_CHARGES_PER_SUBMISSION) {
            String message = String.format(
                "Only %d entries are allowed to update charge revenue recognition fields upload",
                MAX_CHARGES_PER_SUBMISSION
            );
            throw new InvalidInputException(message);
        }

        try {
            String chargeId = record.get(CHARGE_ID_HEADER);
            String recognitionRuleName = record.get(RECOGNITION_RULE_NAME_HEADER);
            String ledgerAccountDeferredRevenue = record.get(LEDGER_ACCOUNT_DEFERRED_REVENUE_HEADER);
            String ledgerAccountRecognizedRevenue = record.get(LEDGER_ACCOUNT_RECOGNIZED_REVENUE_HEADER);
            String ledgerAccountContractAsset = record.get(LEDGER_ACCOUNT_CONTRACT_ASSET_HEADER);
            String ledgerAccountTaxLiability = record.get(LEDGER_ACCOUNT_TAX_LIABILITY_HEADER);

            return ImmutableUpdateChargeRevRecFieldsDetail.builder()
                .chargeId(chargeId)
                .requestedRecognitionRuleName(recognitionRuleName)
                .requestedLedgerAccountDeferredRevenue(ledgerAccountDeferredRevenue)
                .requestedLedgerAccountRecognizedRevenue(ledgerAccountRecognizedRevenue)
                .requestedLedgerAccountContractAsset(ledgerAccountContractAsset)
                .requestedLedgerAccountTaxLiability(ledgerAccountTaxLiability)
                .dryRun(true)
                .failed(false)
                .build();
        } catch (InvalidInputException | ObjectNotFoundException | IllegalStateException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceFailureException(ex.getMessage(), ex);
        }
    }

    public Pair<InputStream, String> getChargeRevRecFieldsAsCsv() {
        List<Charge> charges = productCatalogDAO.getCharges();

        ByteBuffer byteBuffer;
        try (
            ByteArrayOutputStream writeStream = new ByteArrayOutputStream();
            OutputStreamWriter streamWriter = new OutputStreamWriter(writeStream, StandardCharsets.UTF_8);
            SecureCSVPrinter csvPrinter = SecureCSVPrinter.wrap(CSVFormat.DEFAULT.print(streamWriter))
        ) {
            byteBuffer = populateChargeRevRecFieldsAsCsv(charges, writeStream, csvPrinter);
        } catch (IOException ioException) {
            throw new ServiceFailureException("Generating Charge revenue recognition fields as a csv failed. Message: " + ioException.getMessage());
        }
        InputStream inputStream = new ByteArrayInputStream(byteBuffer.array());
        return Pair.of(inputStream, "ChargeRevRecFields_" + Instant.now().getEpochSecond() + ".csv");
    }

    private ByteBuffer populateChargeRevRecFieldsAsCsv(List<Charge> charges, ByteArrayOutputStream writeStream, SecureCSVPrinter csvPrinter)
        throws IOException {
        csvPrinter.printRecord(
            PRODUCT_NAME_HEADER,
            PLAN_NAME_HEADER,
            CHARGE_ID_HEADER,
            CHARGE_NAME_HEADER,
            RECOGNITION_RULE_NAME_HEADER,
            LEDGER_ACCOUNT_DEFERRED_REVENUE_HEADER,
            LEDGER_ACCOUNT_RECOGNIZED_REVENUE_HEADER,
            LEDGER_ACCOUNT_CONTRACT_ASSET_HEADER,
            LEDGER_ACCOUNT_TAX_LIABILITY_HEADER
        );
        List<String> chargeIds = charges.stream().map(Charge::getChargeId).toList();
        Map<String, String> chargeIdToProductNameMap = productCatalogDAO.getChargeIdToProductNameMap(chargeIds);
        Map<String, String> chargeIdToPlanNameMap = productCatalogDAO.getChargeIdToPlanNameMap(chargeIds);
        List<String> recognitionRuleIds = charges.stream().map(Charge::getRecognitionRuleId).toList();
        Map<String, String> recognitionRuleIdToNameMap = revenueRecognitionGetService.getRecognitionRuleIdToNameMap(recognitionRuleIds);
        charges.forEach(charge -> {
            Optional<LedgerAccountMapping> ledgerAccountMappings = getLedgerAccountMappings(charge);
            String ledgerAccountDeferredRevenue = getLedgerAccountCodeIfPresent(
                ledgerAccountMappings.map(LedgerAccountMapping::getDeferredRevenueAccountId)
            );
            String ledgerAccountRecognizedRevenue = getLedgerAccountCodeIfPresent(
                ledgerAccountMappings.map(LedgerAccountMapping::getRecognizedRevenueAccountId)
            );
            String ledgerAccountContractAsset = getLedgerAccountCodeIfPresent(
                ledgerAccountMappings.map(LedgerAccountMapping::getContractAssetAccountId)
            );
            String ledgerAccountTaxLiability = getLedgerAccountCodeIfPresent(
                ledgerAccountMappings.map(LedgerAccountMapping::getTaxLiabilityAccountId)
            );
            try {
                csvPrinter.printRecord(
                    chargeIdToProductNameMap.get(charge.getChargeId()),
                    chargeIdToPlanNameMap.get(charge.getChargeId()),
                    charge.getChargeId(),
                    charge.getName(),
                    recognitionRuleIdToNameMap.get(charge.getRecognitionRuleId()),
                    ledgerAccountDeferredRevenue,
                    ledgerAccountRecognizedRevenue,
                    ledgerAccountContractAsset,
                    ledgerAccountTaxLiability
                );
            } catch (IOException e) {
                throw new ServiceFailureException(e);
            }
        });
        csvPrinter.flush();
        return ByteBuffer.wrap(writeStream.toByteArray());
    }

    private Optional<LedgerAccountMapping> getLedgerAccountMappings(Charge charge) {
        List<LedgerAccount> ledgerAccounts = accountingGetService.getLedgerAccountsForCharge(charge.getChargeId());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(ledgerAccounts)) {
            return Optional.empty();
        }
        return Optional.of(new LedgerAccountMapping(ledgerAccounts));
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private String getLedgerAccountCodeIfPresent(Optional<String> ledgerAccountId) {
        if (ledgerAccountId.isEmpty()) {
            return StringUtils.EMPTY;
        }
        return accountingGetService.getLedgerAccountById(ledgerAccountId.get()).getAccountCode();
    }
}
