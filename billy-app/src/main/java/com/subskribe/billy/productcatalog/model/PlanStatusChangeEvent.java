package com.subskribe.billy.productcatalog.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
@JsonSerialize(as = ImmutablePlanStatusChangeEvent.class)
@JsonDeserialize(as = ImmutablePlanStatusChangeEvent.class)
public interface PlanStatusChangeEvent extends NotifyingEvent {
    PlanJson getPlanJson();
    PlanStatus getFromStatus();
    PlanStatus getToStatus();

    @Override
    default String getEventObjectId() {
        return getPlanJson().getEventObjectId();
    }
}
