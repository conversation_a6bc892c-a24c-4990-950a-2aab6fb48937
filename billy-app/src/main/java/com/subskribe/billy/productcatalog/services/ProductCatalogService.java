package com.subskribe.billy.productcatalog.services;

import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationship;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationshipType;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.productcatalog.db.ProductCatalogDAO;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.productcatalog.validation.CatalogValidation;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.resources.json.plan.PlanMetadataJson;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.PlanTermsService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class ProductCatalogService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductCatalogService.class);

    private final ProductCatalogDAO productCatalogDAO;
    private final ProductIdGenerator productIdGenerator;
    private final ProductCategoryIdGenerator productCategoryIdGenerator;
    private final PlanIdGenerator planIdGenerator;
    private final CatalogValidation catalogValidation;
    private final CatalogRelationshipService catalogRelationshipService;
    private final DSLContextProvider dslContextProvider;
    private final PlanTermsService planTermsService;
    private final TenantIdProvider tenantIdProvider;
    private final ProductCatalogGetService productCatalogGetService;
    private final AccountingService accountingService;
    private final EntityGetService entityGetService;
    private final EntityContextResolver entityContextResolver;
    private final FeatureService featureService;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final CustomFieldService customFieldService;
    private final PlanMapper planMapper;
    private final ChargeService chargeService;
    private final CatalogEventService catalogEventService;

    @Inject
    public ProductCatalogService(
        ProductCatalogDAO productCatalogDAO,
        ProductIdGenerator productIdGenerator,
        ProductCategoryIdGenerator productCategoryIdGenerator,
        PlanIdGenerator planIdGenerator,
        CatalogValidation catalogValidation,
        CatalogRelationshipService catalogRelationshipService,
        DSLContextProvider dslContextProvider,
        PlanTermsService planTermsService,
        TenantIdProvider tenantIdProvider,
        ProductCatalogGetService productCatalogGetService,
        AccountingService accountingService,
        EntityGetService entityGetService,
        EntityContextResolver entityContextResolver,
        FeatureService featureService,
        DocumentTemplateGetService documentTemplateGetService,
        CustomFieldService customFieldService,
        ChargeService chargeService,
        CatalogEventService catalogEventService
    ) {
        this.productCatalogDAO = productCatalogDAO;
        this.productIdGenerator = productIdGenerator;
        this.productCategoryIdGenerator = productCategoryIdGenerator;
        this.planIdGenerator = planIdGenerator;
        this.catalogValidation = catalogValidation;
        this.catalogRelationshipService = catalogRelationshipService;
        this.dslContextProvider = dslContextProvider;
        this.planTermsService = planTermsService;
        this.tenantIdProvider = tenantIdProvider;
        this.productCatalogGetService = productCatalogGetService;
        this.accountingService = accountingService;
        this.entityGetService = entityGetService;
        this.entityContextResolver = entityContextResolver;
        this.featureService = featureService;
        this.documentTemplateGetService = documentTemplateGetService;
        this.customFieldService = customFieldService;
        this.chargeService = chargeService;
        this.catalogEventService = catalogEventService;
        planMapper = Mappers.getMapper(PlanMapper.class);
    }

    public Product addProduct(Product product) {
        product.setProductId(productIdGenerator.generate());

        // Set entity id
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(product.getEntityIds());
        product.setEntityIds(resolvedEntityIds);

        product.validate();
        validateEntityIds(product);
        return TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider).transactionResult(configuration -> {
            Product savedProduct = productCatalogDAO.addProduct(product, configuration);
            catalogEventService.sendProductEvent(savedProduct, EventType.PRODUCT_CREATED, configuration);
            return savedProduct;
        });
    }

    public void updateProduct(Product product) {
        product.validate();
        var existingProduct = checkProductExists(product.getProductId());

        // Set entity id
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            product.getEntityIds(),
            existingProduct.getEntityIds()
        );
        product.setEntityIds(resolvedEntityIds);

        validateEntityIds(product);
        validatePlanEntitiesForProductUpdate(product, existingProduct);
        TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider).transaction(configuration -> {
            productCatalogDAO.updateProduct(existingProduct.getId(), product, configuration);
            catalogEventService.sendProductEvent(product, EventType.PRODUCT_UPDATED, configuration);
        });
    }

    public Product deleteProduct(String productId) {
        Validator.validateStringNotBlank(productId, "productId");

        if (productCatalogGetService.isProductInUse(productId)) {
            LOGGER.info("Unable to delete product due to linked plans");
            throw new IllegalArgumentException("Product contains plans, delete plans first");
        }

        var existingProduct = checkProductExists(productId);
        TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider).transaction(configuration -> {
            productCatalogDAO.deleteProduct(existingProduct.getId(), configuration);
            catalogEventService.sendProductEvent(existingProduct, EventType.PRODUCT_DELETED, configuration);
        });
        return existingProduct;
    }

    private Product checkProductExists(String productId) {
        var existingProduct = productCatalogDAO.getProduct(productId);
        if (existingProduct.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.PRODUCT, productId);
        }

        return existingProduct.get();
    }

    private void validateEntityIds(Product product) {
        Validator.validateCollectionNotEmpty(product.getEntityIds(), "entityIds is required");
        if (!featureService.isEnabled(Feature.MULTI_ENTITY_AUTH) || product.getProductCategory() == null) {
            return;
        }

        // validate that the entity ids for the product is a subset of the product category
        ProductCategory productCategory = productCatalogGetService.getProductCategory(product.getProductCategory().getProductCategoryId());
        if (!EntityContext.isSubsetOf(product.getEntityIds(), productCategory.getEntityIds())) {
            throw new InvalidInputException("entity ids must be a subset of the product category entity ids");
        }
    }

    public List<ProductCategory> getProductCategories(PaginationQueryParams paginationQueryParams) {
        var categories = productCatalogDAO.getProductCategories(paginationQueryParams);
        for (ProductCategory category : categories) {
            var inUse = productCatalogGetService.isProductCategoryInUse(category.getProductCategoryId());
            category.setInUse(inUse);
        }
        return categories;
    }

    public ProductCategory addProductCategory(ProductCategory category) {
        // todo: generate a more user friendly Id
        category.setProductCategoryId(productCategoryIdGenerator.generate());

        // Set entity id
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(category.getEntityIds());
        category.setEntityIds(resolvedEntityIds);

        category.validate();
        if (featureService.isEnabled(Feature.MULTI_ENTITY_AUTH)) {
            Validator.validateCollectionNotEmpty(category.getEntityIds(), "entityIds is required");
        }
        return productCatalogDAO.addProductCategory(category);
    }

    public void updateProductCategory(ProductCategory category) {
        category.validate();
        var existingCategory = checkProductCategoryExists(category.getProductCategoryId());

        // Set entity id
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            category.getEntityIds(),
            existingCategory.getEntityIds()
        );
        category.setEntityIds(resolvedEntityIds);
        if (featureService.isEnabled(Feature.MULTI_ENTITY_AUTH)) {
            Validator.validateCollectionNotEmpty(category.getEntityIds(), "entityIds is required");
        }
        productCatalogDAO.updateProductCategory(existingCategory.getPkId(), category);
    }

    public ProductCategory deleteProductCategory(String productCategoryId) {
        Validator.validateStringNotBlank(productCategoryId, "productCategoryId is required");

        if (productCatalogGetService.isProductCategoryInUse(productCategoryId)) {
            LOGGER.info("Unable to delete product category due to linked products");
            throw new IllegalStateException("Must remove references to product category before deletion");
        }

        var existingCategory = checkProductCategoryExists(productCategoryId);
        return productCatalogDAO.deleteProductCategory(existingCategory.getPkId());
    }

    private ProductCategory checkProductCategoryExists(String productCategoryId) {
        var existingCategory = productCatalogDAO.getProductCategory(productCategoryId);
        if (existingCategory.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.PRODUCT_CATEGORY, productCategoryId);
        }

        return existingCategory.get();
    }

    public Plan addPlanWithoutCharges(Plan plan, List<CatalogRelationship> relationships) {
        Plan newPlan = addPlanAndCharges(plan);
        if (CollectionUtils.isNotEmpty(relationships)) {
            relationships.forEach(relationship -> relationship.setFromPlanId(newPlan.getPlanId()));
            catalogRelationshipService.mergeAndPersistPercentOfRelationships(newPlan, relationships);
            catalogRelationshipService.mergeAndPersistInclusionExclusionRelationships(newPlan, relationships);
        }
        return newPlan;
    }

    public Plan addPlanAndCharges(Plan plan) {
        plan.setPlanId(planIdGenerator.generate());

        // Set entity ids for plan & charges
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(plan.getEntityIds());
        plan.setEntityIds(resolvedEntityIds);
        plan.getCharges().forEach(c -> c.setEntityIds(resolvedEntityIds));
        catalogValidation.validatePlanAndCharges(plan);
        // make sure product exists
        productCatalogGetService.getProduct(plan.getProductId());

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Plan savedPlan = tenantDslContext.transactionResult(configuration -> addPlanAndChargesTransaction(configuration, plan));

        // add after plan transaction because otherwise planId does not exist yet
        catalogRelationshipService.mergeAndPersistCatalogRelationships(
            savedPlan.getPlanId(),
            savedPlan.getProductId(),
            plan.getReplacementPlanIds(),
            CatalogRelationshipType.IS_REPLACED_BY
        );
        savedPlan.setReplacementPlanIds(plan.getReplacementPlanIds());

        return savedPlan;
    }

    private Plan addPlanAndChargesTransaction(Configuration configuration, Plan inputPlan) {
        DSLContext dslContext = DSL.using(configuration);
        String tenantId = tenantIdProvider.provideTenantIdString();
        List<DocumentTemplate> templates = documentTemplateGetService.getDocumentTemplatesByTemplateIds(inputPlan.getTemplateIds());
        Plan savedPlan = productCatalogDAO.insertPlan(configuration, inputPlan);
        List<Charge> savedCharges = inputPlan
            .getCharges()
            .stream()
            .map(charge -> chargeService.addChargeForPlanInTx(charge, savedPlan, configuration))
            .toList();
        savedPlan.setCharges(savedCharges);
        planTermsService.addDocumentTemplateIdsForPlan(dslContext, tenantId, templates, savedPlan.getPlanId());
        savedPlan.setTemplateIds(inputPlan.getTemplateIds());
        catalogEventService.sendPlanEvent(savedPlan, EventType.PLAN_CREATED, configuration);
        return savedPlan;
    }

    public void updatePlanWithoutCharges(Plan plan, List<CatalogRelationship> planRulesRelationships) {
        updatePlan(plan, true);
        catalogRelationshipService.mergeAndPersistInclusionExclusionRelationships(plan, planRulesRelationships);
    }

    public void updatePlan(Plan plan, boolean skipChargeValidation) {
        // make sure product and plans exist
        Plan existingPlan = productCatalogGetService.getPlan(plan.getPlanId());
        populatePlanFieldsFromExistingPlanForUpdate(plan, existingPlan);

        productCatalogGetService.getProduct(plan.getProductId());
        catalogValidation.validatePlan(plan, skipChargeValidation);

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        tenantDslContext.transaction(configuration -> {
            switch (existingPlan.getStatus()) {
                case DRAFT:
                    updatePlanTransaction(configuration, plan, existingPlan, false);
                    break;
                case ACTIVE:
                case DEPRECATED:
                    throwIfCurrencyOfAUsedPlanIsBeingUpdated(existingPlan, plan);
                    updatePlanTransaction(configuration, plan, existingPlan, true);
                    break;
                default:
                    throw new IllegalStateException(String.format("unexpected plan status: %s", existingPlan.getStatus()));
            }
            catalogEventService.sendPlanEvent(plan, EventType.PLAN_UPDATED, configuration);
        });
        catalogRelationshipService.mergeAndPersistCatalogRelationships(
            plan.getPlanId(),
            plan.getProductId(),
            plan.getReplacementPlanIds(),
            CatalogRelationshipType.IS_REPLACED_BY
        );
    }

    private void updatePlanTransaction(Configuration configuration, Plan plan, Plan existingPlan, Boolean isMetaData) {
        DSLContext dslContext = DSL.using(configuration);
        String tenantId = tenantIdProvider.provideTenantIdString();
        if (isMetaData) {
            productCatalogDAO.updatePlanMetaData(dslContext, plan);
            chargeService.updateMetadataForCharges(plan, dslContext);
            updatePlanEntitiesTransaction(dslContext, plan, existingPlan);
        } else {
            productCatalogDAO.updatePlan(dslContext, plan);
            productCatalogDAO.updateChargeEntitiesToMatchPlan(dslContext, plan);
        }

        List<DocumentTemplate> templates = documentTemplateGetService.getDocumentTemplatesByTemplateIds(plan.getTemplateIds());
        planTermsService.updateDocumentTemplateIdsForPlan(dslContext, tenantId, templates, plan.getPlanId());
    }

    /*
        update plan entities for non-draft plans:
        this use case is required when a tenant migrates from single entity to multi entity
        the plan, which was earlier tagged with 'All Entities' can be now required to be tagged with specific entities
        to allow this, we must ensure that the entities in use by the plan, are present in the new entity list
     */
    private void updatePlanEntitiesTransaction(DSLContext dslContext, Plan plan, Plan existingPlan) {
        if (Objects.equals(existingPlan.getEntityIds(), plan.getEntityIds())) {
            return;
        }
        validateOrderEntitiesForPlanUpdate(plan, existingPlan);
        productCatalogDAO.updateEntityIdsForActivePlan(dslContext, plan);
        updateChargeEntitiesForPlanWhenNeeded(dslContext, plan, existingPlan);
    }

    private void updateChargeEntitiesForPlanWhenNeeded(DSLContext dslContext, Plan plan, Plan existingPlan) {
        List<Charge> existingChargesForPlan = chargeService.getChargesByPlan(plan);
        for (Charge existingCharge : existingChargesForPlan) {
            if (!plan.getEntityIds().equals(existingCharge.getEntityIds())) {
                existingCharge.setEntityIds(plan.getEntityIds());
                chargeService.updateChargeForPlanInTx(existingCharge, existingPlan, dslContext.configuration());
            }
        }
    }

    // verify that there are no orders in entities that are absent in the new plan entity list
    private void validateOrderEntitiesForPlanUpdate(Plan plan, Plan existingPlan) {
        if (plan.getEntityIds().contains(EntityContext.ALL_ACCESS_ID)) {
            return;
        }
        Set<String> orderEntities = productCatalogGetService.entitiesUsedByOrdersUsingPlan(existingPlan.getPlanId());
        entityGetService.validateParentEntitiesForUpdate(plan.getEntityIds(), orderEntities, "plan", "orders");
    }

    // verify that there are no plans in entities that are absent in the new product entity list
    private void validatePlanEntitiesForProductUpdate(Product product, Product existingProduct) {
        if (product.getEntityIds().contains(EntityContext.ALL_ACCESS_ID)) {
            return;
        }
        Set<String> planEntities = productCatalogGetService.entitiesUsedByPlansOfProduct(existingProduct.getProductId());
        entityGetService.validateParentEntitiesForUpdate(product.getEntityIds(), planEntities, "product", "plans");
    }

    private void populatePlanFieldsFromExistingPlanForUpdate(Plan plan, Plan existingPlan) {
        plan.setId(existingPlan.getId());

        // Set entity ids for plan & charges
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            plan.getEntityIds(),
            existingPlan.getEntityIds()
        );
        plan.setEntityIds(resolvedEntityIds);
        plan.getCharges().forEach(c -> c.setEntityIds(resolvedEntityIds));
    }

    public void updatePlanAndCharges(Plan plan) {
        Validator.validateNonNullArgument(plan, "plan");

        Plan existingPlan = productCatalogGetService.getPlan(plan.getPlanId());
        populatePlanFieldsFromExistingPlanForUpdate(plan, existingPlan);

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        catalogValidation.validatePlanAndCharges(plan);
        if (existingPlan.getStatus() == PlanStatus.DRAFT) {
            // make sure product exists
            productCatalogGetService.getProduct(plan.getProductId());
            tenantDslContext.transaction(configuration -> updatePlanAndChargesTransaction(configuration, plan));
            // todo: this should be moved to its own endpoint eventually
            catalogRelationshipService.mergeAndPersistPercentOfRelationships(plan, List.of());
        } else {
            throwIfCurrencyOfAUsedPlanIsBeingUpdated(existingPlan, plan);
            tenantDslContext.transaction(configuration -> {
                updatePlanTransaction(configuration, plan, existingPlan, true);
                catalogEventService.sendPlanEvent(plan, EventType.PLAN_UPDATED, configuration);
            });
        }

        catalogRelationshipService.mergeAndPersistInclusionExclusionRelationships(plan, List.of());
        catalogRelationshipService.mergeAndPersistCatalogRelationships(
            plan.getPlanId(),
            plan.getProductId(),
            plan.getReplacementPlanIds(),
            CatalogRelationshipType.IS_REPLACED_BY
        );
    }

    private void throwIfCurrencyOfAUsedPlanIsBeingUpdated(Plan existingPlan, Plan newPlan) {
        if (existingPlan.getCurrency() == null && newPlan.getCurrency() == null) {
            return;
        }

        if (
            existingPlan.getCurrency() == null ||
            newPlan.getCurrency() == null ||
            !newPlan.getCurrency().getCurrencyCode().equals(existingPlan.getCurrency().getCurrencyCode())
        ) {
            throw new IllegalArgumentException("Plan's currency cannot be updated.");
        }
    }

    private void updatePlanAndChargesTransaction(Configuration configuration, Plan plan) {
        DSLContext dslContext = DSL.using(configuration);
        String tenantId = tenantIdProvider.provideTenantIdString();
        productCatalogDAO.updatePlan(dslContext, plan);
        Set<String> existingChargeIds = chargeService.getChargesByPlan(plan).stream().map(Charge::getChargeId).collect(Collectors.toSet());
        List<Charge> finalCharges = upsertNewChargesAndRemoveOldCharges(configuration, plan, existingChargeIds);
        List<String> chargeIds = plan.getCharges().stream().map(Charge::getChargeId).toList();
        plan.setCharges(finalCharges);
        catalogEventService.sendPlanEvent(plan, EventType.PLAN_UPDATED, configuration);
        accountingService.deleteLedgerAccountMappingsForCharges(configuration, chargeIds);
        List<DocumentTemplate> templates = documentTemplateGetService.getDocumentTemplatesByTemplateIds(plan.getTemplateIds());
        planTermsService.updateDocumentTemplateIdsForPlan(dslContext, tenantId, templates, plan.getPlanId());
    }

    private List<Charge> upsertNewChargesAndRemoveOldCharges(Configuration configuration, Plan plan, Set<String> recordedChargeIds) {
        // TODO: Need a better way than 'chargeId == null' to distinguish between new vs existing charges
        Map<Boolean, List<Charge>> chargesMap = plan.getCharges().stream().collect(Collectors.partitioningBy(charge -> charge.getChargeId() == null));
        var chargesToUpdate = chargesMap.get(false);
        List<Charge> updatedCharges = chargesToUpdate
            .stream()
            .map(charge -> chargeService.updateChargeForPlanInTx(charge, plan, configuration))
            .toList();

        var chargesToAdd = chargesMap.get(true);
        List<Charge> addedCharges = chargesToAdd.stream().map(charge -> chargeService.addChargeForPlanInTx(charge, plan, configuration)).toList();

        Set<String> updatedChargeIds = chargesToUpdate.stream().map(Charge::getChargeId).collect(Collectors.toSet());
        var chargeIdsToDelete = SetUtils.difference(recordedChargeIds, updatedChargeIds).toSet();
        chargeIdsToDelete.forEach(chargeId -> chargeService.deleteChargeForPlanInTx(chargeId, plan, configuration));
        return Stream.concat(updatedCharges.stream(), addedCharges.stream()).collect(Collectors.toList());
    }

    public Plan duplicatePlan(String planId) {
        Validator.validateStringNotBlank(planId, "planId cannot be blank");
        Plan plan = productCatalogGetService.getPlan(planId);
        List<Charge> charges = plan.getCharges();

        String originalPlanId = plan.getPlanId();
        plan.setId(null);
        plan.setPlanId(null);
        plan.setEntityIds(plan.getEntityIds());
        plan.setExternalId(null);
        plan.setName(plan.getName() + " Copy");
        plan.setStatus(PlanStatus.DRAFT);
        plan.setCharges(List.of());
        Plan newPlan = addPlanAndCharges(plan);
        customFieldService.duplicateCustomFields(CustomFieldParentType.PLAN, originalPlanId, newPlan.getPlanId());

        chargeService.duplicateCharges(plan, newPlan, charges);

        return productCatalogGetService.getPlan(newPlan.getPlanId());
    }

    public Plan deletePlan(String planId) {
        Validator.validateStringNotBlank(planId, "planId cannot be blank");

        Plan existingPlan = productCatalogGetService.getPlan(planId); // make sure plan exists

        if (productCatalogGetService.isPlanInUse(planId)) {
            throw new IllegalStateException("Plan used by existing subscription or order and cannot be deleted.");
        }

        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return dslContext.transactionResult(configuration -> {
            Plan result = productCatalogDAO.deletePlanAndChargesInTransaction(planId, configuration);
            catalogRelationshipService.deleteAllCatalogRelationshipsForPlan(planId, configuration);
            planTermsService.removeAllDocumentTemplateIdsForPlan(planId, configuration);

            List<String> chargeIds = result.getCharges().stream().map(Charge::getChargeId).toList();
            accountingService.deleteLedgerAccountMappingsForCharges(configuration, chargeIds);
            catalogEventService.sendPlanEvent(existingPlan, EventType.PLAN_DELETED, configuration);
            return result;
        });
    }

    public Plan deprecatePlan(String planId) {
        return changePlanStatus(planId, PlanStatus.ACTIVE, PlanStatus.DEPRECATED);
    }

    public Plan activatePlan(String planId) {
        return changePlanStatus(planId, PlanStatus.DRAFT, PlanStatus.ACTIVE);
    }

    public Plan revertToDraft(String planId) {
        Validator.validateStringNotBlank(planId, "plan id cannot be blank");
        // todo: check in use as part of a transaction
        if (productCatalogGetService.isPlanInUse(planId)) {
            String errorMessage = String.format("Cannot revert plan %s to draft because it is in use", planId);
            throw new IllegalArgumentException(errorMessage);
        }

        return changePlanStatus(planId, PlanStatus.ACTIVE, PlanStatus.DRAFT);
    }

    public Plan reactivatePlan(String planId) {
        return changePlanStatus(planId, PlanStatus.DEPRECATED, PlanStatus.ACTIVE);
    }

    private Plan changePlanStatus(String planId, PlanStatus from, PlanStatus to) {
        Validator.validateNonNullArgument(planId, "planId");

        Plan plan = productCatalogGetService.getPlan(planId);
        if (plan.getStatus() == to) {
            return plan;
        }

        if (plan.getStatus() != from) {
            throw new IllegalStateException(String.format("plan must be %s to be set to %s", from, to));
        }

        // validate the plan is valid with the new status
        plan.setStatus(to);
        catalogValidation.validatePlanAndCharges(plan);
        catalogValidation.validateChargeDefaultAttribution(plan, to);

        if (to == PlanStatus.DRAFT) {
            catalogRelationshipService.deleteAllToPlanRelationshipsForPlan(plan.getPlanId(), CatalogRelationshipType.IS_REPLACED_BY);
        }
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return dslContext.transactionResult(configuration -> {
            Plan updatedPlan = productCatalogDAO.updatePlanStatus(plan, from, to);
            catalogEventService.sendPlanStatusChangeEvent(plan, from, to, configuration);
            return updatedPlan;
        });
    }

    // patch plan metadata: accept fields to be updated, as optional inputs
    // for now, only entityIds field is supported
    // see more details in PlanMetadataJson
    public Plan patchPlanMetadata(String planId, PlanMetadataJson planMetadataJson) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        if (CollectionUtils.isNotEmpty(planMetadataJson.getEntityIds())) {
            Plan existingPlan = productCatalogGetService.getPlan(planId);
            Plan plan = planMapper.clonePlan(existingPlan);
            plan.setEntityIds(planMetadataJson.getEntityIds());
            dslContext.transaction(configuration -> {
                updatePlanEntitiesTransaction(DSL.using(configuration), plan, existingPlan);
                catalogEventService.sendPlanEvent(plan, EventType.PLAN_UPDATED, configuration);
            });
        }
        return productCatalogGetService.getPlan(planId);
    }
}
