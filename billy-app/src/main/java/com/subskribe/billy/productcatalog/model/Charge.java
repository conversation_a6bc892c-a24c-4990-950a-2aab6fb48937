package com.subskribe.billy.productcatalog.model;

import static com.subskribe.billy.shared.enums.ChargeType.PREPAID;
import static com.subskribe.billy.shared.enums.ChargeType.USAGE;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.WithCustomField;
import com.subskribe.billy.order.model.PricingOverride;
import com.subskribe.billy.productcatalog.services.PriceTiersHelper;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.PercentDerivedFrom;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.shared.traits.ValidatedModel;
import com.subskribe.billy.shared.traits.validators.NoSpecialCharsInId;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

public class Charge implements ValidatedModel, WithCustomField {

    /**
     * this is the primary key identifier for {@link Charge} that is referenced
     * in all the other tables
     */
    private UUID id;
    private Set<String> entityIds;

    // todo: migrate to public planId because charge apis are now independent of plan
    /**
     * this is the short id or logical identifier used for primarily dealing with
     * interface both GQL and API
     */
    @Deprecated
    private UUID planUuid;

    private String planId;
    private String chargeId;

    @NotBlank(message = "name is required")
    @Size(max = 100, message = "Name should not be more than 100 characters")
    private String name;

    @Size(max = 100, message = "Display name should not be more than 100 characters")
    private String displayName;

    private String description;
    private BigDecimal amount;

    @NotNull(message = "type is required")
    private ChargeType type;

    @NotNull(message = "chargeModel is required")
    private ChargeModel chargeModel;

    private Recurrence recurrence;
    private Long durationInMonths; // one time charge duration in months
    private List<PriceTier> priceTiers;
    private UUID taxRateId;
    private UUID unitOfMeasureId;
    private String recognitionRuleId;
    private boolean isDrawdown;
    private String minimumCommitBaseChargeId;
    private String overageBaseChargeId;
    private boolean isCustom;
    private boolean isListPriceEditable;
    private boolean isRenewable;
    private boolean isCreditable;
    private BigDecimal percent;
    private PercentDerivedFrom percentDerivedFrom;
    private List<String> targetPlanIds;
    private Long minQuantity;
    private Long defaultQuantity;
    private Long maxQuantity;
    private BigDecimal minAmount;
    private BigDecimal maxAmount;
    private boolean isEventBased;
    private boolean isDiscount;
    private BillingCycle billingCycle;
    private Boolean shouldTrackArr;

    private CustomField customFields;

    @Size(max = 36, message = "Rate card ID should not be more than 36 characters")
    @NoSpecialCharsInId
    private String rateCardId;

    @Size(max = 100, message = "External ID should not be more than 100 characters")
    @NoSpecialCharsInId
    private String externalId;

    @Size(max = 100, message = "ERP ID should not be more than 100 characters")
    private String erpId;

    @Size(max = 36, message = "Item Code should not be more than 36 characters")
    private String itemCode;

    private BillingTerm billingTerm;

    public Charge() {
        customFields = new CustomField(new HashMap<>());
    }

    public Charge(
        UUID id,
        Set<String> entityIds,
        String chargeId,
        UUID planUuid,
        String planId,
        String name,
        String displayName,
        String description,
        BigDecimal amount,
        ChargeType type,
        ChargeModel chargeModel,
        Recurrence recurrence,
        Long durationInMonths,
        List<PriceTier> priceTiers,
        UUID taxRateId,
        UUID unitOfMeasureId,
        String recognitionRuleId,
        boolean isDrawdown,
        String minimumCommitBaseChargeId,
        String overageBaseChargeId,
        boolean isCustom,
        boolean isListPriceEditable,
        boolean isRenewable,
        boolean isCreditable,
        BigDecimal percent,
        PercentDerivedFrom percentDerivedFrom,
        List<String> targetPlanIds,
        Long minQuantity,
        Long defaultQuantity,
        Long maxQuantity,
        String externalId,
        String erpId,
        BigDecimal minAmount,
        BigDecimal maxAmount,
        boolean isEventBased,
        boolean isDiscount,
        String rateCardId,
        BillingCycle billingCycle,
        BillingTerm billingTerm,
        Boolean shouldTrackArr,
        String itemCode,
        CustomField customFields
    ) {
        this.id = id;
        this.entityIds = entityIds;
        this.chargeId = chargeId;
        this.planUuid = planUuid;
        this.planId = planId;
        this.name = name;
        this.displayName = displayName;
        this.description = description;
        this.amount = amount == null ? null : amount.stripTrailingZeros();
        this.type = type;
        this.chargeModel = chargeModel;
        this.recurrence = recurrence;
        this.durationInMonths = durationInMonths;
        this.priceTiers = PriceTiersHelper.sanitizePriceTiers(priceTiers);
        this.taxRateId = taxRateId;
        this.unitOfMeasureId = unitOfMeasureId;
        this.recognitionRuleId = recognitionRuleId;
        this.isDrawdown = isDrawdown;
        this.minimumCommitBaseChargeId = minimumCommitBaseChargeId;
        this.overageBaseChargeId = overageBaseChargeId;
        this.isCustom = isCustom;
        this.isListPriceEditable = isListPriceEditable;
        this.isRenewable = isRenewable;
        this.isCreditable = isCreditable;
        this.percent = percent;
        this.percentDerivedFrom = percentDerivedFrom;
        this.targetPlanIds = targetPlanIds;
        this.minQuantity = minQuantity;
        this.defaultQuantity = defaultQuantity;
        this.maxQuantity = maxQuantity;
        this.externalId = externalId;
        this.erpId = erpId;
        this.minAmount = minAmount;
        this.maxAmount = maxAmount;
        this.isEventBased = isEventBased;
        this.rateCardId = rateCardId;
        this.billingCycle = billingCycle;
        this.billingTerm = billingTerm;
        this.shouldTrackArr = shouldTrackArr;
        this.itemCode = itemCode;
        this.isDiscount = isDiscount;
        this.customFields = customFields == null ? new CustomField(new HashMap<>()) : customFields;
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public Set<String> getEntityIds() {
        return entityIds;
    }

    public void setEntityIds(Set<String> entityIds) {
        this.entityIds = entityIds;
    }

    public UUID getPlanUuid() {
        return planUuid;
    }

    public void setPlanUuid(UUID planUuid) {
        this.planUuid = planUuid;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getChargeId() {
        return chargeId;
    }

    public void setChargeId(String chargeId) {
        this.chargeId = chargeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount == null ? null : amount.stripTrailingZeros();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setType(ChargeType type) {
        this.type = type;
    }

    public ChargeType getType() {
        return type;
    }

    public void setChargeModel(ChargeModel chargeModel) {
        this.chargeModel = chargeModel;
    }

    public ChargeModel getChargeModel() {
        return chargeModel;
    }

    public void setRecurrence(Recurrence recurrence) {
        this.recurrence = recurrence;
    }

    public Recurrence getRecurrence() {
        return recurrence;
    }

    public Long getDurationInMonths() {
        return durationInMonths;
    }

    public void setDurationInMonths(Long durationInMonths) {
        this.durationInMonths = durationInMonths;
    }

    public void setPriceTiers(List<PriceTier> priceTiers) {
        this.priceTiers = PriceTiersHelper.sanitizePriceTiers(priceTiers);
    }

    public List<PriceTier> getPriceTiers() {
        return Optional.ofNullable(priceTiers).orElse(List.of());
    }

    public UUID getTaxRateId() {
        return taxRateId;
    }

    public void setTaxRateId(UUID taxRateId) {
        this.taxRateId = taxRateId;
    }

    public UUID getUnitOfMeasureId() {
        return unitOfMeasureId;
    }

    public void setUnitOfMeasureId(UUID unitOfMeasureId) {
        this.unitOfMeasureId = unitOfMeasureId;
    }

    public String getRecognitionRuleId() {
        return recognitionRuleId;
    }

    public void setRecognitionRuleId(String recognitionRuleId) {
        this.recognitionRuleId = recognitionRuleId;
    }

    public boolean isDrawdown() {
        return isDrawdown;
    }

    public void setDrawdown(boolean drawdown) {
        isDrawdown = drawdown;
    }

    public String getMinimumCommitBaseChargeId() {
        return minimumCommitBaseChargeId;
    }

    public void setMinimumCommitBaseChargeId(String minimumCommitBaseChargeId) {
        this.minimumCommitBaseChargeId = minimumCommitBaseChargeId;
    }

    public String getOverageBaseChargeId() {
        return overageBaseChargeId;
    }

    public void setOverageBaseChargeId(String overageBaseChargeId) {
        this.overageBaseChargeId = overageBaseChargeId;
    }

    public boolean isCustom() {
        return isCustom;
    }

    public void setCustom(boolean custom) {
        isCustom = custom;
    }

    public boolean getIsListPriceEditable() {
        return isListPriceEditable;
    }

    public void setIsListPriceEditable(boolean listPriceEditable) {
        isListPriceEditable = listPriceEditable;
    }

    public boolean getIsRenewable() {
        return isRenewable;
    }

    public void setIsRenewable(boolean renewable) {
        isRenewable = renewable;
    }

    public boolean isCreditable() {
        return isCreditable;
    }

    public void setCreditable(boolean creditable) {
        isCreditable = creditable;
    }

    public BigDecimal getPercent() {
        return percent;
    }

    public void setPercent(BigDecimal percent) {
        this.percent = percent;
    }

    public PercentDerivedFrom getPercentDerivedFrom() {
        return percentDerivedFrom;
    }

    public void setPercentDerivedFrom(PercentDerivedFrom percentDerivedFrom) {
        this.percentDerivedFrom = percentDerivedFrom;
    }

    public List<String> getTargetPlanIds() {
        return targetPlanIds;
    }

    public void setTargetPlanIds(List<String> targetPlanIds) {
        this.targetPlanIds = targetPlanIds;
    }

    public Long getMinQuantity() {
        return minQuantity;
    }

    public Optional<Long> getMinQuantityOptional() {
        return Optional.ofNullable(minQuantity);
    }

    public void setMinQuantity(Long minQuantity) {
        this.minQuantity = minQuantity;
    }

    public Long getDefaultQuantity() {
        return defaultQuantity;
    }

    public Optional<Long> getDefaultQuantityOptional() {
        return Optional.ofNullable(defaultQuantity);
    }

    public void setDefaultQuantity(Long defaultQuantity) {
        this.defaultQuantity = defaultQuantity;
    }

    public Long getMaxQuantity() {
        return maxQuantity;
    }

    public Optional<Long> getMaxQuantityOptional() {
        return Optional.ofNullable(maxQuantity);
    }

    public void setMaxQuantity(Long maxQuantity) {
        this.maxQuantity = maxQuantity;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getErpId() {
        return erpId;
    }

    public void setErpId(String erpId) {
        this.erpId = erpId;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }

    public boolean isEventBased() {
        return isEventBased;
    }

    public void setEventBased(boolean eventBased) {
        isEventBased = eventBased;
    }

    public boolean getIsDiscount() {
        return isDiscount;
    }

    public void setIsDiscount(boolean isDiscount) {
        this.isDiscount = isDiscount;
    }

    public String getRateCardId() {
        return rateCardId;
    }

    public void setRateCardId(String rateCardId) {
        this.rateCardId = rateCardId;
    }

    public BillingCycle getBillingCycle() {
        if (type == PREPAID && recurrence != null) {
            // special case for PREPAID. If prepaid has recurrence, the billing cycle should be the same.
            return switch (recurrence.getCycle()) {
                case PAID_IN_FULL -> BillingCycle.PAID_IN_FULL;
                case YEAR -> BillingCycle.YEAR;
                case SEMI_ANNUAL -> BillingCycle.SEMI_ANNUAL;
                case QUARTER -> BillingCycle.QUARTER;
                case MONTH -> BillingCycle.MONTH;
                case DAY -> throw new UnsupportedOperationException("Day cycle is not supported."); // todo: configure charge recurrence and billing cycle as DAY
                case CUSTOM -> BillingCycle.DEFAULT;
            };
        }
        return billingCycle;
    }

    public void setBillingCycle(BillingCycle billingCycle) {
        this.billingCycle = billingCycle;
    }

    public boolean isRecurring() {
        return type.isRecurring() || (type == PREPAID && getRecurrence() != null);
    }

    private BigDecimal getListUnitPriceByQuantity(Long quantity, List<PriceTier> priceTiers) {
        switch (chargeModel) {
            case PER_UNIT, FLAT_FEE -> {
                return amount;
            }
            case VOLUME -> {
                PriceTier priceTier = PriceTiersHelper.findPriceTier(priceTiers, Math.abs(quantity));
                return priceTier.getAmount();
            }
            case TIERED -> {
                return getTieredListUnitPrice(quantity, priceTiers);
            }
            case RATE_CARD_LOOKUP -> {
                /*
                 * for rate card lookup it is hard to establish the canonical notion
                 * of unit price since the price is based on temporal quantity, for tiered and rate card
                 * we need to bind the quantity with several other dimensions to establish the list unit price
                 * list unit price essentially is a moving target here!
                 */
                return null;
            }
            case BLOCK -> {
                PriceTier priceTier = PriceTiersHelper.findPriceTier(priceTiers, Math.abs(quantity));
                if (quantity == 0 && priceTier.getUntilQuantity().orElse(-1L).equals(0L)) {
                    return null;
                }
                return Numbers.makePriceDisplayScale(priceTier.getAmount());
            }
            default -> throw new IllegalStateException("Unexpected value: " + chargeModel);
        }
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public BigDecimal getListUnitPriceByQuantity(Long quantity, Optional<PricingOverride> pricingOverride) {
        if (pricingOverride.isEmpty()) {
            return getListUnitPriceByQuantity(quantity);
        }
        Validator.checkNonNullInternal(pricingOverride.get().getPriceTiers(), "PricingOverride.priceTiers");
        return getListUnitPriceByQuantity(quantity, pricingOverride.get().getPriceTiers());
    }

    @Deprecated(since = "Quantity alone is not enough to establish the unit price for several cases including MAP and Tiered")
    BigDecimal getListUnitPriceByQuantity(Long quantity) {
        return getListUnitPriceByQuantity(quantity, priceTiers);
    }

    private BigDecimal getTieredListUnitPrice(Long quantity, List<PriceTier> priceTiers) {
        boolean sameAmount = true;
        BigDecimal firstTierPrice = priceTiers.get(0).getAmount();

        for (PriceTier priceTier : priceTiers) {
            if (priceTier.getAmount().compareTo(firstTierPrice) != 0) {
                sameAmount = false;
                break;
            }

            if (priceTier.getUntilQuantity().isPresent() && priceTier.getUntilQuantity().get() >= quantity) {
                break;
            }
        }

        if (sameAmount) {
            return firstTierPrice;
        } else {
            return null;
        }
    }

    @Override
    public void validate() {
        ValidatedModel.super.validate();
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public BillingTerm getBillingTerm() {
        // todo: this is a bit hacky for now
        if (billingTerm != null) {
            return billingTerm;
        }

        if (type == USAGE) {
            return BillingTerm.IN_ARREARS;
        }
        return BillingTerm.UP_FRONT;
    }

    public void setBillingTerm(BillingTerm billingTerm) {
        this.billingTerm = billingTerm;
    }

    public Boolean getShouldTrackArr() {
        return shouldTrackArr;
    }

    public void setShouldTrackArr(Boolean shouldTrackArr) {
        this.shouldTrackArr = shouldTrackArr;
    }

    @Override
    public CustomField getCustomFields() {
        return customFields;
    }

    @Override
    public void setCustomFields(CustomField customFields) {
        this.customFields = customFields == null ? new CustomField(new HashMap<>()) : customFields;
    }

    public boolean isMetadataEquals(Charge charge2) {
        if (charge2 == null) {
            return false;
        }
        return (
            Objects.equals(getName(), charge2.getName()) &&
            Objects.equals(getDisplayName(), charge2.getDisplayName()) &&
            Objects.equals(getDescription(), charge2.getDescription()) &&
            Objects.equals(getTaxRateId(), charge2.getTaxRateId()) &&
            Objects.equals(getUnitOfMeasureId(), charge2.getUnitOfMeasureId()) &&
            getIsRenewable() == charge2.getIsRenewable() &&
            Objects.equals(getMinQuantity(), charge2.getMinQuantity()) &&
            Objects.equals(getDefaultQuantity(), charge2.getDefaultQuantity()) &&
            Objects.equals(getMaxQuantity(), charge2.getMaxQuantity()) &&
            Objects.equals(getExternalId(), charge2.getExternalId())
        );
    }
}
