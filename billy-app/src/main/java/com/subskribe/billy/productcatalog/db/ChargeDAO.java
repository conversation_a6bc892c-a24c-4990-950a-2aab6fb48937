package com.subskribe.billy.productcatalog.db;

import static com.subskribe.billy.jooq.default_schema.tables.Charge.CHARGE;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.jooq.default_schema.Indexes;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.productcatalog.mapper.ChargeRecordMapper;
import com.subskribe.billy.productcatalog.mapper.PlanRecordMapper;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.ChargeStub;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;
import org.mapstruct.factory.Mappers;

public class ChargeDAO {

    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final PlanRecordMapper planRecordMapper;
    private final ChargeRecordMapper chargeRecordMapper;

    @Inject
    public ChargeDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        planRecordMapper = Mappers.getMapper(PlanRecordMapper.class);
        chargeRecordMapper = Mappers.getMapper(ChargeRecordMapper.class);
    }

    public boolean chargeExists(String chargeId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return dslContext.fetchExists(
            dslContext.selectFrom(CHARGE).where(CHARGE.TENANT_ID.eq(tenantId)).and(CHARGE.CHARGE_ID.eq(chargeId).and(CHARGE.IS_DELETED.eq(false)))
        );
    }

    public Charge updateChargePartial(UUID planInternalId, String planId, Charge charge) {
        var context = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();

        var updatedRecord = PostgresErrorHandler.withConstraintAsConflict(
            () ->
                context
                    .update(CHARGE)
                    .set(CHARGE.NAME, charge.getName())
                    .set(CHARGE.DISPLAY_NAME, charge.getDisplayName())
                    .set(CHARGE.DESCRIPTION, charge.getDescription())
                    .set(CHARGE.TAX_RATE_ID, charge.getTaxRateId())
                    .set(CHARGE.UNIT_OF_MEASURE_ID, charge.getUnitOfMeasureId())
                    .set(CHARGE.RECOGNITION_RULE_ID, charge.getRecognitionRuleId())
                    .set(CHARGE.IS_RENEWABLE, charge.getIsRenewable())
                    .set(CHARGE.MIN_QUANTITY, charge.getMinQuantity())
                    .set(CHARGE.DEFAULT_QUANTITY, charge.getDefaultQuantity())
                    .set(CHARGE.MAX_QUANTITY, charge.getMaxQuantity())
                    .set(CHARGE.IS_LIST_PRICE_EDITABLE, charge.getIsListPriceEditable())
                    .set(CHARGE.EXTERNAL_ID, charge.getExternalId())
                    .set(CHARGE.ERP_ID, charge.getErpId())
                    .set(CHARGE.ITEM_CODE, charge.getItemCode())
                    .where(CHARGE.TENANT_ID.eq(tenantId))
                    .and(CHARGE.CHARGE_ID.eq(charge.getChargeId()))
                    .and(CHARGE.PLAN_ID.eq(planInternalId))
                    .and(CHARGE.IS_DELETED.isFalse())
                    .returning()
                    .fetchOne(),
            Indexes.INDEX_CHARGE_EXTERNAL_ID.getName(),
            String.format("Charge with external Id: %s already exists", charge.getExternalId())
        );

        if (updatedRecord == null) {
            throw new IllegalStateException("Update failed, please try again");
        }

        return planRecordMapper.chargeRecordToCharge(updatedRecord);
    }

    public Optional<ChargeStub> getChargeStub(String chargeId) {
        var context = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();

        var record = context
            .selectFrom(CHARGE)
            .where(CHARGE.TENANT_ID.eq(tenantId))
            .and(CHARGE.CHARGE_ID.eq(chargeId))
            .and(CHARGE.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(chargeRecordMapper::recordToChargeStub);
    }
}
