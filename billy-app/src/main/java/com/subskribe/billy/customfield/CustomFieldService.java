package com.subskribe.billy.customfield;

import static com.subskribe.billy.customfield.CustomFieldProductMetadata.CUSTOM_FIELD_CORE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.customfield.db.CustomFieldDAO;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldDefinitionUpdate;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldSource;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class CustomFieldService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomFieldService.class);

    private final CustomFieldDAO customFieldDAO;

    private final CustomFieldIdGenerator customFieldIdGenerator;

    private final CustomFieldConfiguration customFieldConfiguration;

    private final CacheService cacheService;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public CustomFieldService(
        CustomFieldDAO customFieldDAO,
        CustomFieldIdGenerator customFieldIdGenerator,
        BillyConfiguration billyConfiguration,
        CacheService cacheService,
        TenantIdProvider tenantIdProvider
    ) {
        this.customFieldDAO = customFieldDAO;
        this.customFieldIdGenerator = customFieldIdGenerator;
        customFieldConfiguration = billyConfiguration.getCustomFieldConfiguration();
        this.cacheService = cacheService;
        this.tenantIdProvider = tenantIdProvider;
    }

    // parentObjectId here can be empty. If so, return a list of empty custom field values hydrated with the definitions for custom field type
    // this is used primarily by the UI (or other clients) to render custom field form for a newly created object
    // that is not yet persisted in the backend as an alternative to fetching custom field definitions for a parent object type
    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public CustomField getCustomFieldsWithOptionalParent(CustomFieldParentType parentObjectType, Optional<String> parentObjectId) {
        Validator.validateNonNullArguments(parentObjectType, "parentObjectType");

        // if parentObjectId is not provided, return empty custom field values
        CustomField customField = parentObjectId
            .map(id -> customFieldDAO.getCustomFields(parentObjectType, id))
            .orElse(new CustomField(Collections.emptyMap()));

        return hydrateCustomFields(getCustomFieldDefinitions(parentObjectType), customField, true);
    }

    public Set<String> getParentObjectIdsByCustomFieldValues(
        CustomFieldParentType parentObjectType,
        String customFieldName,
        String customFieldValue
    ) {
        Validator.validateNonNullArguments(parentObjectType, customFieldName, customFieldValue);
        Set<String> parentObjectIds = customFieldDAO.getParentObjectIdsByCustomFieldValues(parentObjectType, customFieldName, customFieldValue);
        return Optional.ofNullable(parentObjectIds).orElse(Set.of());
    }

    public Optional<CustomField> getCustomFieldsOptional(CustomFieldParentType parentObjectType, String parentObjectId) {
        Validator.validateNonNullArguments(parentObjectType, "parentObjectType");
        Validator.validateStringNotBlank(parentObjectId, "parentObjectId is required");

        Optional<CustomField> customField = customFieldDAO.getCustomFieldsOptional(parentObjectType, parentObjectId);
        return customField.map(cf -> hydrateCustomFields(getCustomFieldDefinitions(parentObjectType), cf, true));
    }

    public CustomField getCustomFields(CustomFieldParentType parentObjectType, String parentObjectId) {
        Validator.validateNonNullArguments(parentObjectType, "parentObjectType");
        Validator.validateStringNotBlank(parentObjectId, "parentObjectId is required");

        CustomField customField = customFieldDAO.getCustomFields(parentObjectType, parentObjectId);

        return hydrateCustomFields(getCustomFieldDefinitions(parentObjectType), customField, true);
    }

    public Map<String, CustomField> getCustomFields(CustomFieldParentType parentObjectType, List<String> parentObjectIds) {
        Validator.validateNonNullArguments(parentObjectType, "parentObjectType");

        if (CollectionUtils.isEmpty(parentObjectIds)) {
            return Collections.emptyMap();
        }

        Map<String, CustomField> customFieldByParentObjectId = customFieldDAO.getCustomFields(parentObjectType, parentObjectIds);

        List<CustomFieldDefinition> definitions = getCustomFieldDefinitions(parentObjectType);

        return customFieldByParentObjectId
            .entrySet()
            .stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> hydrateCustomFields(definitions, entry.getValue(), true)));
    }

    public CustomField getEmptyHydratedCustomFieldForParentObjectType(CustomFieldParentType parentObjectType) {
        List<CustomFieldDefinition> definitions = getCustomFieldDefinitions(parentObjectType);
        return hydrateCustomFields(definitions, new CustomField(Map.of()), true);
    }

    public CustomField setCustomFields(CustomFieldParentType parentObjectType, String parentObjectId, CustomField customField) {
        return setCustomFields(parentObjectType, parentObjectId, customField, false);
    }

    // Caution: this method should only be called by Billy services, NOT user accessible APIs
    public CustomField setCustomFieldsBySystem(CustomFieldParentType parentObjectType, String parentObjectId, CustomField customField) {
        return setCustomFields(parentObjectType, parentObjectId, customField, true);
    }

    private CustomField setCustomFields(
        CustomFieldParentType parentObjectType,
        String parentObjectId,
        CustomField customField,
        boolean updateBySystem
    ) {
        Validator.validateNonNullArgument(parentObjectType, "parentObjectType");
        Validator.validateStringNotBlank(parentObjectId, "parentObjectId is required");
        Validator.validateNonNullArgument(customField, "customField");

        List<CustomFieldDefinition> definitions = getCustomFieldDefinitions(parentObjectType);
        Set<String> systemCustomFieldIds = definitions
            .stream()
            .filter(customFieldDefinition -> customFieldDefinition.getSource() == CustomFieldSource.SYSTEM)
            .map(CustomFieldDefinition::getCustomFieldId)
            .collect(Collectors.toSet());

        validateCustomFieldDefinitionIds(definitions, customField, updateBySystem);

        if (!updateBySystem) {
            // if not updated by system calls, fill in existing system fields
            var existingCustomFields = customFieldDAO.getCustomFields(parentObjectType, parentObjectId);

            existingCustomFields
                .getEntries()
                .forEach((key, value) -> {
                    if (systemCustomFieldIds.contains(key)) {
                        customField.getEntries().put(key, value);
                    }
                });
        }

        // populate fieldName and required fields from definitions
        CustomField hydratedCustomField = hydrateCustomFields(definitions, customField, false);
        hydratedCustomField.validate();

        CustomField storedCustomField = customFieldDAO.setCustomFields(parentObjectType, parentObjectId, customField);

        return hydrateCustomFields(getCustomFieldDefinitions(parentObjectType), storedCustomField, false);
    }

    public CustomField putStringValue(CustomFieldDefinition fieldDefinition, String parentId, String value, CustomFieldSource source) {
        CustomFieldParentType parentType = fieldDefinition.getParentObjectType();
        if (fieldDefinition.getFieldType() != CustomFieldType.STRING) {
            throw new IllegalArgumentException("put string value must be used with string field");
        }
        // get existing field:value pairs
        CustomField existingField = getCustomFields(parentType, parentId);
        Map<String, CustomFieldValue> existingFieldEntries = existingField.getEntries();
        // upsert this field:value pair (using put)
        CustomFieldValue fieldValue = new CustomFieldValue(
            fieldDefinition.getFieldType(),
            fieldDefinition.getFieldName(),
            fieldDefinition.getFieldLabel(),
            value,
            List.of(),
            fieldDefinition.getOptions(),
            fieldDefinition.isRequired(),
            source,
            fieldDefinition.getDefaultValue()
        );
        Map<String, CustomFieldValue> updatedEntries = new HashMap<>(existingFieldEntries);
        updatedEntries.put(fieldDefinition.getCustomFieldId(), fieldValue);
        // save to database
        CustomField updatedField = new CustomField(updatedEntries);
        return setCustomFields(parentType, parentId, updatedField, true);
    }

    private void validateCustomFieldDefinitionIds(List<CustomFieldDefinition> definitions, CustomField customField, boolean updateBySystem) {
        Set<String> customFieldDefinitionIds;
        Set<String> customFieldDefinitionIdsToUpdate = customField.getEntries().keySet();

        if (updateBySystem) {
            customFieldDefinitionIds = definitions.stream().map(CustomFieldDefinition::getCustomFieldId).collect(Collectors.toSet());
        } else {
            // if not system generated, match only non system fields
            customFieldDefinitionIds = definitions
                .stream()
                .filter(definition -> definition.getSource() == CustomFieldSource.USER)
                .map(CustomFieldDefinition::getCustomFieldId)
                .collect(Collectors.toSet());
            Set<String> systemCustomFieldDefinitionIds = definitions
                .stream()
                .filter(definition -> definition.getSource() == CustomFieldSource.SYSTEM)
                .map(CustomFieldDefinition::getCustomFieldId)
                .collect(Collectors.toSet());
            if (!Collections.disjoint(systemCustomFieldDefinitionIds, customFieldDefinitionIdsToUpdate)) {
                throw new InvalidInputException(String.format("Cannot update system generated custom fields %s", systemCustomFieldDefinitionIds));
            }
        }

        if (!customFieldDefinitionIds.equals(customFieldDefinitionIdsToUpdate)) {
            throw new InvalidInputException(
                String.format("expected custom field ids %s but only got %s", customFieldDefinitionIds, customFieldDefinitionIdsToUpdate)
            );
        }
    }

    // a parent object may not have custom field values set, new custom fields could be introduced or names updated. Hydrate from definition.
    public static CustomField hydrateCustomFields(
        List<CustomFieldDefinition> customFieldDefinitions,
        CustomField customField,
        boolean hydrateForGet
    ) {
        Map<String, CustomFieldValue> existingEntries = customField.getEntries();
        ImmutableMap.Builder<String, CustomFieldValue> builder = ImmutableMap.builder();

        for (var definition : customFieldDefinitions) {
            String customFieldId = definition.getCustomFieldId();
            if (existingEntries.containsKey(customFieldId)) {
                // keep the value if type is String, otherwise leave it null so the value can be constructed by the getter logic
                String storedValue = definition.getFieldType() == CustomFieldType.STRING ? existingEntries.get(customFieldId).getValue() : null;

                List<String> storedSelections = existingEntries.get(customFieldId).getSelections();
                if (hydrateForGet) {
                    // if the operation is to get a custom field, filter out invalid selections
                    // if the operation is to set / add custom fields, then leave any potential invalid selections so validation errors can be triggered
                    storedSelections = removeInvalidSelections(definition.getOptions(), storedSelections);
                }

                // populate field name from definition. Field name is not stored with the object since it can be updated subsequently
                // If field label is missing, default it to field name.
                builder.put(
                    customFieldId,
                    new CustomFieldValue(
                        definition.getFieldType(),
                        definition.getFieldName(),
                        StringUtils.isNotBlank(definition.getFieldLabel()) ? definition.getFieldLabel() : definition.getFieldName(),
                        storedValue,
                        storedSelections,
                        definition.getOptions(),
                        definition.isRequired(),
                        definition.getSource(),
                        null
                    )
                );
            } else {
                builder.put(
                    customFieldId,
                    new CustomFieldValue(
                        definition.getFieldType(),
                        definition.getFieldName(),
                        StringUtils.isNotBlank(definition.getFieldLabel()) ? definition.getFieldLabel() : definition.getFieldName(),
                        null,
                        List.of(),
                        definition.getOptions(),
                        definition.isRequired(),
                        definition.getSource(),
                        definition.getDefaultValue()
                    )
                );
            }
        }

        return new CustomField(builder.build());
    }

    private static List<String> removeInvalidSelections(List<String> options, List<String> selections) {
        if (CollectionUtils.isEmpty(options)) {
            return List.of();
        }
        if (CollectionUtils.isEmpty(selections)) {
            return List.of();
        }
        return selections.stream().filter(options::contains).collect(Collectors.toList());
    }

    public static List<CustomFieldEntry> populateCustomFieldDefaults(
        List<CustomFieldDefinition> customFieldDefinitions,
        List<CustomFieldEntry> customFieldEntries
    ) {
        Map<String, CustomFieldDefinition> customFieldDefinitionMap = customFieldDefinitions
            .stream()
            .collect(Collectors.toMap(CustomFieldDefinition::getCustomFieldId, Function.identity()));

        return customFieldEntries
            .stream()
            .map(entry ->
                new CustomFieldEntry(
                    entry.getId(),
                    entry.getType(),
                    entry.getName(),
                    entry.getLabel(),
                    entry.getValue(),
                    entry.getSelections(),
                    entry.getOptions(),
                    entry.isRequired(),
                    entry.getSource(),
                    customFieldDefinitionMap.get(entry.getId()).getDefaultValue()
                )
            )
            .toList();
    }

    public List<CustomFieldDefinition> getCustomFieldDefinitions(CustomFieldParentType parentObjectType) {
        byte[] cachedBytes = cacheService.getBytes(
            tenantIdProvider.provideTenantIdString(),
            CacheType.CUSTOM_FIELD_DEFINITION,
            parentObjectType.name()
        );
        if (cachedBytes == null) {
            return getAndCacheDefinitions(parentObjectType);
        }
        return deserializeDefinitions(parentObjectType, cachedBytes).orElseGet(() -> loadCustomFieldDefinitionsFromDb(parentObjectType));
    }

    public List<CustomFieldDefinition> getCustomFieldDefinitions() {
        return customFieldDAO.getCustomFieldDefinitions();
    }

    private List<CustomFieldDefinition> getAndCacheDefinitions(CustomFieldParentType parentObjectType) {
        List<CustomFieldDefinition> definitions = loadCustomFieldDefinitionsFromDb(parentObjectType);
        if (CollectionUtils.isEmpty(definitions)) {
            return definitions;
        }
        serializeDefinitions(parentObjectType, definitions).map(bytes ->
            cacheService.putBytes(tenantIdProvider.provideTenantIdString(), CacheType.CUSTOM_FIELD_DEFINITION, parentObjectType.name(), bytes)
        );
        return definitions;
    }

    private List<CustomFieldDefinition> loadCustomFieldDefinitionsFromDb(CustomFieldParentType parentObjectType) {
        List<CustomFieldDefinition> customFieldDefinitions = customFieldDAO.getCustomFieldDefinitions(parentObjectType);

        // the mapper shouldn't return null, but is potentially sensitive due to mapper config changes. Safer to check again here.
        return customFieldDefinitions == null ? List.of() : customFieldDefinitions;
    }

    public CustomFieldDefinition createCustomFieldDefinition(CustomFieldDefinition customFieldDefinition) {
        return createCustomFieldDefinition(customFieldDefinition, false);
    }

    // Caution: this method should only be called by Billy services, NOT user accessible APIs
    public CustomFieldDefinition createCustomFieldDefinitionBySystem(CustomFieldDefinition customFieldDefinition) {
        return createCustomFieldDefinition(customFieldDefinition, true);
    }

    private CustomFieldDefinition createCustomFieldDefinition(CustomFieldDefinition customFieldDefinition, boolean updateBySystem) {
        Validator.validateNonNullArgument(customFieldDefinition, "customFieldDefinition");
        customFieldDefinition.validate();

        if (!updateBySystem && customFieldDefinition.getSource() == CustomFieldSource.SYSTEM) {
            throw new InvalidInputException("Cannot create system generated custom field definitions");
        }

        if (customFieldDefinition.getParentObjectType() == CustomFieldParentType.SUBSCRIPTION_ITEM) {
            throw new InvalidInputException("subscription item custom fields cannot be manually created");
        }

        int customFieldDefinitionsCount = customFieldDAO.getUserGeneratedCustomFieldDefinitionsCount(customFieldDefinition.getParentObjectType());

        int customFieldsPerObject = customFieldConfiguration.getCustomFieldsPerObject();

        if (customFieldDefinitionsCount >= customFieldsPerObject) {
            throw new ConflictingStateException(String.format("A maximum of %d custom fields are allowed per object type", customFieldsPerObject));
        }

        CustomFieldDefinition result = customFieldDAO.createCustomFieldDefinition(customFieldIdGenerator.generate(), customFieldDefinition);
        // we need to invalidate the cache on create as well because we cache a list based on parent object type
        invalidateDefinitionCache(customFieldDefinition.getParentObjectType());

        if (customFieldDefinition.getParentObjectType() == CustomFieldParentType.ORDER_ITEM) {
            createSubscriptionItemCustomFieldDefinition(result);
        }
        return result;
    }

    public void createSubscriptionItemCustomFieldDefinition(CustomFieldDefinition orderItemDefinition) {
        CustomFieldDefinition subscriptionItemDefinition = new CustomFieldDefinition(
            null,
            null,
            CustomFieldParentType.SUBSCRIPTION_ITEM,
            orderItemDefinition.getFieldType(),
            orderItemDefinition.getFieldName(),
            orderItemDefinition.getFieldLabel(),
            orderItemDefinition.getOptions(),
            orderItemDefinition.isRequired(),
            orderItemDefinition.getDefaultValue(),
            CustomFieldSource.SYSTEM,
            null,
            null
        );
        customFieldDAO.createCustomFieldDefinition(customFieldIdGenerator.generate(), subscriptionItemDefinition);
        invalidateDefinitionCache(CustomFieldParentType.SUBSCRIPTION_ITEM);
    }

    public CustomFieldDefinition updateCustomFieldDefinition(CustomFieldDefinitionUpdate customFieldDefinitionUpdate) {
        return updateCustomFieldDefinition(customFieldDefinitionUpdate, false);
    }

    private CustomFieldDefinition updateCustomFieldDefinition(CustomFieldDefinitionUpdate customFieldDefinitionUpdate, boolean updateBySystem) {
        Validator.validateNonNullArgument(customFieldDefinitionUpdate, "customFieldDefinition");
        Validator.validateStringNotBlank(customFieldDefinitionUpdate.getCustomFieldId(), "customFieldId");

        CustomFieldDefinition customFieldDefinition = customFieldDAO.getCustomFieldDefinition(customFieldDefinitionUpdate.getCustomFieldId());

        if (customFieldDefinitionUpdate.getFieldType() == null) {
            customFieldDefinitionUpdate.setFieldType(customFieldDefinition.getFieldType());
        }

        if (customFieldDefinition.getParentObjectType() == CustomFieldParentType.SUBSCRIPTION_ITEM) {
            throw new InvalidInputException("subscription item custom fields cannot be manually updated");
        }

        customFieldDefinitionUpdate.validate();

        // todo: CustomFieldDefinitionUpdate should be deprecated and we should use the CustomFieldDefinition's own validation
        customFieldDefinitionUpdate.validateDefaultValue(
            customFieldDefinitionUpdate.getFieldType(),
            customFieldDefinition.getParentObjectType(),
            customFieldDefinitionUpdate.getDefaultValue(),
            customFieldDefinitionUpdate.getOptions()
        );

        if (!updateBySystem && customFieldDefinition.getSource() == CustomFieldSource.SYSTEM) {
            // system generated custom field definitions cannot be updated by users
            throw new IllegalArgumentException("System generated custom field definition cannot be updated");
        }

        if (customFieldDefinition.getParentObjectType() == CustomFieldParentType.ORDER_ITEM) {
            // todo: add backfill job
            Optional<CustomFieldDefinition> existingSubscriptionItemDefinition = findMatchingSubscriptionItemDefinition(customFieldDefinition);
            if (existingSubscriptionItemDefinition.isEmpty()) {
                createSubscriptionItemCustomFieldDefinition(customFieldDefinition);
            } else {
                updateSubscriptionItemCustomFieldDefinition(existingSubscriptionItemDefinition.get().getCustomFieldId(), customFieldDefinitionUpdate);
            }
        }
        CustomFieldDefinition updated = customFieldDAO.updateCustomFieldDefinition(customFieldDefinitionUpdate);
        invalidateDefinitionCache(customFieldDefinition.getParentObjectType());
        return updated;
    }

    private void updateSubscriptionItemCustomFieldDefinition(String subscriptionItemDefinitionId, CustomFieldDefinitionUpdate orderItemDefinition) {
        var updatedSubscriptionItemsDefinition = new CustomFieldDefinitionUpdate(
            subscriptionItemDefinitionId,
            orderItemDefinition.getFieldName(),
            orderItemDefinition.getFieldLabel(),
            orderItemDefinition.getFieldType(),
            orderItemDefinition.getOptions(),
            orderItemDefinition.getDefaultValue()
        );
        CustomFieldDefinition updated = customFieldDAO.updateCustomFieldDefinition(updatedSubscriptionItemsDefinition);
        invalidateDefinitionCache(updated.getParentObjectType());
    }

    private Optional<CustomFieldDefinition> findMatchingSubscriptionItemDefinition(CustomFieldDefinition orderItemDefinition) {
        // todo: replace with relationship between definitions of different types
        List<CustomFieldDefinition> subscriptionItemDefinitions = getCustomFieldDefinitions(CustomFieldParentType.SUBSCRIPTION_ITEM);
        for (var subscriptionItemDefinition : subscriptionItemDefinitions) {
            if (subscriptionItemDefinition.getFieldName().equals(orderItemDefinition.getFieldName())) {
                return Optional.of(subscriptionItemDefinition);
            }
        }
        return Optional.empty();
    }

    // get or create - get custom field by name, if not present create it using given definition
    public CustomFieldDefinition getOrCreateSystemCustomFieldDefinition(CustomFieldDefinition customFieldDefinition) {
        List<CustomFieldDefinition> existingCustomFields = getCustomFieldDefinitions(customFieldDefinition.getParentObjectType());
        Optional<CustomFieldDefinition> existingFieldByName = existingCustomFields
            .stream()
            .filter(def -> def.getFieldName().equals(customFieldDefinition.getFieldName()))
            .findFirst();
        return existingFieldByName.orElseGet(() -> createCustomFieldDefinitionBySystem(customFieldDefinition));
    }

    public CustomFieldDefinition deleteCustomFieldDefinition(String id) {
        return deleteCustomFieldDefinition(id, false);
    }

    public CustomFieldDefinition deleteCustomFieldDefinitionBySystem(String id) {
        return deleteCustomFieldDefinition(id, true);
    }

    private CustomFieldDefinition deleteCustomFieldDefinition(String id, boolean updateBySystem) {
        CustomFieldDefinition definition = customFieldDAO.getCustomFieldDefinition(id);

        if (!updateBySystem && definition.getSource() == CustomFieldSource.SYSTEM) {
            // system generated custom field definitions cannot be updated by users
            throw new IllegalArgumentException("System generated custom field definition cannot be deleted");
        }

        CustomFieldDefinition deleted = customFieldDAO.deleteCustomFieldDefinition(id);
        invalidateDefinitionCache(deleted.getParentObjectType());

        if (deleted.getParentObjectType() == CustomFieldParentType.ORDER_ITEM) {
            findMatchingSubscriptionItemDefinition(deleted).ifPresent(subscriptionItemDefinition ->
                deleteCustomFieldDefinitionBySystem(subscriptionItemDefinition.getCustomFieldId())
            );
        }

        return deleted;
    }

    public void invalidateDefinitionCache(CustomFieldParentType parentType) {
        cacheService.invalidateKey(tenantIdProvider.provideTenantIdString(), CacheType.CUSTOM_FIELD_DEFINITION, parentType.name());
    }

    public void duplicateCustomFields(CustomFieldParentType parentType, String originalId, String newId) {
        var customFields = getCustomFields(parentType, originalId);
        setCustomFields(parentType, newId, new CustomField(customFields.getEntries()));
    }

    private static Optional<byte[]> serializeDefinitions(CustomFieldParentType parentType, List<CustomFieldDefinition> definitions) {
        try {
            return Optional.of(JacksonProvider.emptyFieldExcludingMapper().writeValueAsBytes(definitions));
        } catch (JsonProcessingException e) {
            LOGGER.warn(CUSTOM_FIELD_CORE, "error serializing custom field definitions parent type: {}", parentType, e);
            // we do not fail since cache is only an optimization
            return Optional.empty();
        }
    }

    private static Optional<List<CustomFieldDefinition>> deserializeDefinitions(CustomFieldParentType parentType, byte[] bytes) {
        try {
            return Optional.of(JacksonProvider.emptyFieldExcludingMapper().readValue(bytes, new TypeReference<>() {}));
        } catch (IOException e) {
            LOGGER.warn(CUSTOM_FIELD_CORE, "error de-serializing custom field definitions parent type: {}", parentType, e);
            // we do not fail since cache is only an optimization
            return Optional.empty();
        }
    }
}
