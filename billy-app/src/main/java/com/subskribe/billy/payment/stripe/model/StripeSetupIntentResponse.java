package com.subskribe.billy.payment.stripe.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;

@GraphQLName("StripeSetupIntentResponse")
public class StripeSetupIntentResponse {

    @GraphQLField
    @GraphQLNonNull
    @JsonProperty
    private String setupIntentId;

    @GraphQLField
    @GraphQLNonNull
    @JsonProperty
    private String clientSecret;

    public StripeSetupIntentResponse() {}

    public StripeSetupIntentResponse(String setupIntentId, String clientSecret) {
        this.setupIntentId = setupIntentId;
        this.clientSecret = clientSecret;
    }

    public String getSetupIntentId() {
        return setupIntentId;
    }

    public void setSetupIntentId(String setupIntentId) {
        this.setupIntentId = setupIntentId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }
}
