package com.subskribe.billy.payment.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
@JsonSerialize(as = ImmutablePaymentMetadata.class)
@JsonDeserialize(as = ImmutablePaymentMetadata.class)
public interface PaymentMetadata {
    @GraphQLField
    @GraphQLName("retryMetadata")
    Optional<PaymentRetryMetadata> getRetryMetadata();

    record PaymentRetryMetadata(
        @GraphQLField @GraphQLName("paymentRetryConfigId") UUID paymentRetryConfigId,
        @GraphQLField @GraphQLName("accountPaymentMethodId") UUID accountPaymentMethodId,
        @GraphQLField @GraphQLName("nextAutomatedRetryDetail") Optional<NextAutomatedRetryDetail> nextAutomatedRetryDetail
    ) {
        public PaymentRetryMetadata updateNextAutomatedRetryDetail(Optional<NextAutomatedRetryDetail> nextAutomatedRetryDetail) {
            return new PaymentRetryMetadata(paymentRetryConfigId, accountPaymentMethodId, nextAutomatedRetryDetail);
        }
    }

    record NextAutomatedRetryDetail(
        @GraphQLField @GraphQLName("delayedUntil") Instant delayedUntil,
        @GraphQLField @GraphQLName("tenantJobId") String tenantJobId
    ) {}
}
