package com.subskribe.billy.payment.stripe.service;

import static com.subskribe.billy.payment.model.BulkImportStripePaymentMethodCsvSchema.ACCOUNT_ID_HEADER;
import static com.subskribe.billy.payment.model.BulkImportStripePaymentMethodCsvSchema.BULK_IMPORT_STRIPE_PAYMENT_METHOD_REQUEST_HEADERS;
import static com.subskribe.billy.payment.model.BulkImportStripePaymentMethodCsvSchema.BULK_IMPORT_STRIPE_PAYMENT_METHOD_RESPONSE_HEADERS;
import static com.subskribe.billy.payment.model.BulkImportStripePaymentMethodCsvSchema.SET_AS_AUTOMATIC_PAYMENT_METHOD_HEADER;
import static com.subskribe.billy.payment.model.BulkImportStripePaymentMethodCsvSchema.STRIPE_CUSTOMER_ID_HEADER;
import static com.subskribe.billy.payment.model.BulkImportStripePaymentMethodCsvSchema.STRIPE_PAYMENT_METHOD_ID_HEADER;
import static com.subskribe.billy.payment.stripe.service.StripeService.PAYMENT_METHOD_NAME_FORMAT;

import com.stripe.model.PaymentMethod;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.account.model.AccountPaymentMethodStatus;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.payment.SupportedPaymentProvider;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.model.AccountAutomaticPaymentMethod;
import com.subskribe.billy.payment.model.ImmutableStripeMethodImportRequest;
import com.subskribe.billy.payment.model.ImmutableStripeMethodImportResult;
import com.subskribe.billy.payment.model.PaymentProviderCustomer;
import com.subskribe.billy.payment.model.PaymentProviderPaymentMethod;
import com.subskribe.billy.payment.model.StripeMethodImportRequest;
import com.subskribe.billy.payment.model.StripeMethodImportResult;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodGetService;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodService;
import com.subskribe.billy.payment.services.PaymentProviderService;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.shared.csv.SecureCSVPrinter;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;

public class StripeImportService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StripeImportService.class);

    private static final int MAX_REQUESTS = 100;
    private static final String CSV_UPLOAD_MEDIA_TYPE = "text/csv";
    private static final String CSV_OUTPUT_FILENAME_FORMAT = "StripeImportResult_%d.csv";

    private static final CSVFormat BULK_IMPORT_STRIPE_PAYMENT_METHOD_CSV_FORMAT = CSVFormat.DEFAULT.builder()
        .setHeader(BULK_IMPORT_STRIPE_PAYMENT_METHOD_REQUEST_HEADERS.toArray(String[]::new))
        .setAllowMissingColumnNames(false)
        .setDuplicateHeaderMode(DuplicateHeaderMode.DISALLOW)
        .setTrim(true)
        .build();

    private static final String STRIPE_PAYMENT_METHOD_EXPIRY_DATE_FORMAT = "MM/yy";

    private final StripeService stripeService;
    private final AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService;
    private final PaymentIntegrationGetService paymentIntegrationGetService;
    private final TenantIdProvider tenantIdProvider;
    private final AccountGetService accountGetService;
    private final PaymentProviderService paymentProviderService;
    private final AccountPaymentMethodGetService accountPaymentMethodGetService;
    private final AccountPaymentMethodService accountPaymentMethodService;
    private final DSLContextProvider dslContextProvider;
    private final AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService;

    @Inject
    public StripeImportService(
        StripeService stripeService,
        AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService,
        PaymentIntegrationGetService paymentIntegrationGetService,
        TenantIdProvider tenantIdProvider,
        AccountGetService accountGetService,
        PaymentProviderService paymentProviderService,
        AccountPaymentMethodGetService accountPaymentMethodGetService,
        AccountPaymentMethodService accountPaymentMethodService,
        DSLContextProvider dslContextProvider,
        AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService
    ) {
        this.stripeService = stripeService;
        this.accountAutomaticPaymentMethodService = accountAutomaticPaymentMethodService;
        this.paymentIntegrationGetService = paymentIntegrationGetService;
        this.tenantIdProvider = tenantIdProvider;
        this.accountGetService = accountGetService;
        this.paymentProviderService = paymentProviderService;
        this.accountPaymentMethodGetService = accountPaymentMethodGetService;
        this.accountPaymentMethodService = accountPaymentMethodService;
        this.dslContextProvider = dslContextProvider;
        this.accountAutomaticPaymentMethodGetService = accountAutomaticPaymentMethodGetService;
    }

    public Pair<InputStream, String> processBulkImport(InputStream inputStream, FormDataBodyPart formDataBodyPart) {
        if (inputStream == null) {
            throw new InvalidInputException("CSV stream for bulk import of Stripe payment method is null");
        }

        String contentMediaType = formDataBodyPart.getMediaType().toString();
        if (!StringUtils.equalsIgnoreCase(contentMediaType, CSV_UPLOAD_MEDIA_TYPE)) {
            throw new IllegalArgumentException("Expected text/csv media type. Media type found: " + contentMediaType);
        }

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            List<StripeMethodImportRequest> importRequests = extractStripeMethodImportRequests(reader);
            List<StripeMethodImportResult> importResults = processImportRequests(importRequests);
            return generateCsvResponse(importResults);
        } catch (IOException e) {
            throw new InvalidInputException(e.getMessage());
        }
    }

    private List<StripeMethodImportRequest> extractStripeMethodImportRequests(BufferedReader csvReader) throws IOException {
        Iterator<CSVRecord> records = BULK_IMPORT_STRIPE_PAYMENT_METHOD_CSV_FORMAT.parse(csvReader).iterator();
        if (!records.hasNext()) {
            return List.of();
        }

        List<String> headersFromFile = records.next().toList();
        if (!headersFromFile.equals(BULK_IMPORT_STRIPE_PAYMENT_METHOD_REQUEST_HEADERS)) {
            String message = String.format(
                "Column list in file %s does not match expected column list %s",
                headersFromFile,
                BULK_IMPORT_STRIPE_PAYMENT_METHOD_REQUEST_HEADERS
            );
            throw new InvalidInputException(message);
        }

        List<StripeMethodImportRequest> importRequests = new ArrayList<>();
        while (records.hasNext()) {
            importRequests.add(extractStripeMethodImportRequest(records.next()));
        }

        return importRequests;
    }

    private StripeMethodImportRequest extractStripeMethodImportRequest(CSVRecord record) {
        long rowNum = record.getRecordNumber() - 1;
        if (rowNum > MAX_REQUESTS) {
            String message = String.format("Only %d entries are allowed for each Stripe import", MAX_REQUESTS);
            throw new InvalidInputException(message);
        }

        try {
            String accountId = record.get(ACCOUNT_ID_HEADER);
            String stripeCustomerId = record.get(STRIPE_CUSTOMER_ID_HEADER);
            String stripePaymentMethodId = record.get(STRIPE_PAYMENT_METHOD_ID_HEADER);
            String setAsAutomaticPaymentMethod = record.get(SET_AS_AUTOMATIC_PAYMENT_METHOD_HEADER);

            return ImmutableStripeMethodImportRequest.builder()
                .accountId(accountId)
                .stripeCustomerId(stripeCustomerId)
                .stripePaymentMethodId(stripePaymentMethodId)
                .setAsAutomaticPaymentMethod(Boolean.parseBoolean(setAsAutomaticPaymentMethod))
                .build();
        } catch (Exception ex) {
            throw new ServiceFailureException(ex.getMessage(), ex);
        }
    }

    private List<StripeMethodImportResult> processImportRequests(List<StripeMethodImportRequest> importRequests) {
        List<StripeMethodImportResult> results = new ArrayList<>();
        for (StripeMethodImportRequest request : importRequests) {
            StripeMethodImportResult result = processImportRequest(request);
            results.add(result);
        }
        return results;
    }

    private Pair<InputStream, String> generateCsvResponse(List<StripeMethodImportResult> results) {
        try (
            ByteArrayOutputStream writeStream = new ByteArrayOutputStream();
            OutputStreamWriter streamWriter = new OutputStreamWriter(writeStream, StandardCharsets.UTF_8);
            SecureCSVPrinter csvPrinter = SecureCSVPrinter.wrap(CSVFormat.DEFAULT.print(streamWriter))
        ) {
            csvPrinter.printRecord(BULK_IMPORT_STRIPE_PAYMENT_METHOD_RESPONSE_HEADERS);
            results.forEach(result -> {
                List<String> row = new ArrayList<>();
                row.add(result.accountId());
                if (result.getPaymentProviderPaymentMethod() != null) {
                    PaymentProviderPaymentMethod paymentProviderPaymentMethod = result.getPaymentProviderPaymentMethod();
                    row.add(paymentProviderPaymentMethod.getCustomerId());
                    row.add(paymentProviderPaymentMethod.getPaymentMethodId());
                    row.add(paymentProviderPaymentMethod.getPaymentMethodType().name());
                    row.add(paymentProviderPaymentMethod.getBankName());
                    row.add(paymentProviderPaymentMethod.getAccountType());
                    row.add(paymentProviderPaymentMethod.getAccountNumberLastFour());
                    row.add(paymentProviderPaymentMethod.getLastFour());
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(STRIPE_PAYMENT_METHOD_EXPIRY_DATE_FORMAT);
                    String formattedExpiryDate = paymentProviderPaymentMethod.getExpiryDate() == null
                        ? StringUtils.EMPTY
                        : formatter.format(paymentProviderPaymentMethod.getExpiryDate());
                    row.add(formattedExpiryDate);
                }
                try {
                    csvPrinter.printRecord(row);
                } catch (IOException e) {
                    throw new ServiceFailureException(e);
                }
            });
            csvPrinter.flush();
            ByteBuffer byteBuffer = ByteBuffer.wrap(writeStream.toByteArray());
            InputStream inputStream = new ByteArrayInputStream(byteBuffer.array());
            return Pair.of(inputStream, String.format(CSV_OUTPUT_FILENAME_FORMAT, Instant.now().getEpochSecond()));
        } catch (IOException e) {
            throw new ServiceFailureException(e);
        }
    }

    public StripeMethodImportResult processImportRequest(StripeMethodImportRequest stripeMethodImportRequest) {
        var resultBuilder = ImmutableStripeMethodImportResult.builder()
            .accountId(stripeMethodImportRequest.accountId())
            .stripePaymentMethodId(stripeMethodImportRequest.stripePaymentMethodId());
        try {
            String tenantId = tenantIdProvider.provideTenantIdString();
            Account account = getAccountFromRequest(stripeMethodImportRequest);
            String accountId = account.getAccountId();
            // We end up doing this all over the place in Stripe service, but better to fail here early
            PaymentStripeConnectIntegration stripeConnectIntegration = paymentIntegrationGetService
                .getCompletedStripeConnectIntegration()
                .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PAYMENT_STRIPE_CONNECT_INTEGRATION, tenantId));

            LOGGER.info("Importing payment method id {}", stripeMethodImportRequest.stripePaymentMethodId());

            PaymentMethod paymentMethod = stripeService.retrieveStripePaymentMethod(stripeMethodImportRequest.stripePaymentMethodId());
            String customerId;
            if (stripeMethodImportRequest.stripeCustomerId().isPresent()) {
                customerId = stripeMethodImportRequest.stripeCustomerId().get();
                // we make this call to ensure the account exists
                stripeService.retrieveCustomer(customerId, stripeConnectIntegration.getConnectAccountId());
            } else {
                customerId = paymentMethod.getCustomer();
            }

            PaymentProviderCustomer paymentProviderCustomer;
            // This payment method does not have a customer record associated with it. We need to create one
            if (StringUtils.isEmpty(customerId)) {
                LOGGER.info("Payment method does not have a stipe customer, need to create");
                paymentProviderCustomer = stripeService.addNewOrGetExistingCustomer(tenantId, accountId);
                LOGGER.info("Created customer {}, attaching payment method to customer", paymentProviderCustomer.getCustomerId());
                stripeService.attachPaymentMethodToCustomer(paymentMethod, paymentProviderCustomer.getCustomerId());
                LOGGER.info("Customer attached to payment method");
            }
            // The stripe payment method has a customer associated with it. We need to update our own records if needed
            else {
                paymentProviderCustomer = getOrCreateCustomer(tenantId, accountId, customerId);
            }

            // Finally we create the payment method if needed. Stripe service handles get or create logic
            PaymentProviderPaymentMethod paymentProviderPaymentMethod = stripeService.createOrGetPaymentProviderPaymentMethod(
                paymentMethod,
                paymentProviderCustomer
            );
            LOGGER.info("Payment provider payment method retrieved with id {}", paymentProviderPaymentMethod.getPaymentMethodId());

            // But we have to handle the creation of account payment method if needed
            Optional<AccountPaymentMethod> accountPaymentMethodOptional = accountPaymentMethodGetService.getPaymentMethodForProviderPaymentMethodId(
                accountId,
                paymentProviderPaymentMethod.getId()
            );

            AccountPaymentMethod accountPaymentMethod = accountPaymentMethodOptional.orElseGet(() -> {
                LOGGER.info("Account payment method not found, creating");
                AccountPaymentMethod toSave = new AccountPaymentMethod();
                toSave.setAccountId(accountId);
                String paymentMethodName = String.format(PAYMENT_METHOD_NAME_FORMAT, paymentMethod.getId());
                toSave.setName(paymentMethodName);
                toSave.setStatus(AccountPaymentMethodStatus.ACTIVE);
                toSave.setPaymentMethodId(paymentProviderPaymentMethod.getId().toString());
                toSave.setPaymentType(paymentProviderPaymentMethod.getPaymentMethodType().toLegacyPaymentType().orElseThrow());
                return accountPaymentMethodService.addAccountPaymentMethod(toSave, dslContextProvider.get(tenantId));
            });

            if (stripeMethodImportRequest.setAsAutomaticPaymentMethod()) {
                setAutoPaymentIfNeeded(accountId, accountPaymentMethod);
            }

            return resultBuilder
                .accountPaymentMethod(accountPaymentMethod)
                .paymentProviderPaymentMethod(paymentProviderPaymentMethod)
                .failed(false)
                .build();
        } catch (Exception e) {
            return resultBuilder.failed(true).failureReason(e.getMessage()).build();
        }
    }

    private Account getAccountFromRequest(StripeMethodImportRequest stripeMethodImportRequest) {
        String accountIdentifier = stripeMethodImportRequest.accountId();
        Optional<Account> accountOptional = accountGetService.getAccountByExternalId(accountIdentifier);
        return accountOptional.orElseGet(() -> accountGetService.getAccount(accountIdentifier));
    }

    private void setAutoPaymentIfNeeded(String accountId, AccountPaymentMethod accountPaymentMethod) {
        Optional<AccountAutomaticPaymentMethod> activeMethodForAccount = accountAutomaticPaymentMethodGetService.getEnrolledMethodForAccount(
            accountId
        );
        if (activeMethodForAccount.isPresent() && activeMethodForAccount.get().getAccountPaymentMethodId().equals(accountPaymentMethod.getId())) {
            LOGGER.info("{} is already set as the automatic payment method", accountPaymentMethod.getId());
            return;
        }
        LOGGER.info("Setting {} as the automatic payment method", accountPaymentMethod.getId());
        accountAutomaticPaymentMethodService.setPaymentMethodAsActive(accountPaymentMethod);
    }

    private PaymentProviderCustomer getOrCreateCustomer(String tenantId, String accountId, String customerId) {
        LOGGER.info("Payment method has customer, need to get or create local copy");
        Optional<PaymentProviderCustomer> existingCustomer = paymentProviderService.getPaymentProviderCustomerByAccountId(tenantId, accountId);
        // If this account is already associated, we can reuse it
        if (existingCustomer.isPresent() && existingCustomer.get().getCustomerId().equals(customerId)) {
            LOGGER.info("Existing customer {} found, reusing it", existingCustomer.get().getCustomerId());
            return existingCustomer.get();
        }
        // If this account is already associated with another customer, we need to delete that record
        if (existingCustomer.isPresent() && !existingCustomer.get().getCustomerId().equals(customerId)) {
            LOGGER.info("Account already associated with customer {}, deleting it", existingCustomer.get().getCustomerId());
            paymentProviderService.deletePaymentProviderCustomer(customerId);
        }
        // Save the new customer record;
        PaymentProviderCustomer toSave = new PaymentProviderCustomer(customerId, SupportedPaymentProvider.STRIPE, accountId);
        LOGGER.info("Creating new customer {}", toSave.getCustomerId());
        return paymentProviderService.addPaymentProviderCustomer(toSave);
    }
}
