package com.subskribe.billy.payment.services;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.foreignexchange.model.TransactionalExchangeRate;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateService;
import com.subskribe.billy.payment.db.PaymentDAO;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.model.PaymentAttempt;
import com.subskribe.billy.payment.model.PaymentAttemptState;
import com.subskribe.billy.payment.model.PaymentBankAccount;
import com.subskribe.billy.payment.model.PaymentMetadata;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.PaymentState;
import com.subskribe.billy.shared.enums.PaymentStatus;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class PaymentService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentService.class);

    private final PaymentDAO paymentDAO;

    private final PaymentIdGenerator paymentIdGenerator;

    private final Clock clock;

    private final EntityGetService entityGetService;

    private final TransactionalExchangeRateService transactionalExchangeRateService;

    private final PaymentBankAccountService paymentBankAccountService;

    private final FeatureService featureService;

    private final DSLContextProvider dslContextProvider;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public PaymentService(
        PaymentDAO paymentDAO,
        PaymentIdGenerator paymentIdGenerator,
        Clock clock,
        EntityGetService entityGetService,
        TransactionalExchangeRateService transactionalExchangeRateService,
        PaymentBankAccountService paymentBankAccountService,
        FeatureService featureService,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider
    ) {
        this.paymentDAO = paymentDAO;
        this.paymentIdGenerator = paymentIdGenerator;
        this.clock = clock;
        this.entityGetService = entityGetService;
        this.transactionalExchangeRateService = transactionalExchangeRateService;
        this.paymentBankAccountService = paymentBankAccountService;
        this.featureService = featureService;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
    }

    public Payment addPayment(Payment payment, DSLContext transactionContext) {
        Validator.validateNonNullArgument(payment, "Payment");
        Validator.validateNonNullArgument(payment.getEntityId(), "entity id");

        validateAndSetPaymentDate(payment);
        populatePaymentId(payment);
        populateFunctionalAmounts(payment);
        return paymentDAO.addPayment(payment, transactionContext);
    }

    private void populateFunctionalAmounts(Payment payment) {
        if (!featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            return;
        }
        String transactionalCurrencyCode = payment.getCurrencyCode();
        Entity entity = entityGetService.getEntityById(payment.getEntityId());
        String functionalCurrencyCode = entity.getFunctionalCurrency();
        Optional<TransactionalExchangeRate> optionalExchangeRate = getExchangeRateByPaymentDate(
            Optional.ofNullable(payment.getPaymentDate()),
            transactionalCurrencyCode,
            functionalCurrencyCode
        );
        if (transactionalCurrencyCode.equals(functionalCurrencyCode)) {
            payment.setFunctionalAmount(payment.getAmount());
            payment.setFunctionalAmountCaptured(payment.getAmountCaptured());
            payment.setFunctionalBankFee(payment.getBankFee());
        } else {
            TransactionalExchangeRate exchangeRate = optionalExchangeRate.orElseThrow();
            payment.setExchangeRateId(exchangeRate.getId().orElse(null));
            payment.setExchangeRate(exchangeRate.getExchangeRate());
            payment.setExchangeRateDate(Instant.ofEpochSecond(exchangeRate.getEffectiveDate()));
            payment.setFunctionalAmount(Numbers.applyExchangeRate(payment.getAmount(), exchangeRate.getExchangeRate(), functionalCurrencyCode));
            if (Objects.nonNull(payment.getAmountCaptured())) {
                payment.setFunctionalAmountCaptured(
                    Numbers.applyExchangeRate(payment.getAmountCaptured(), exchangeRate.getExchangeRate(), functionalCurrencyCode)
                );
            }
            if (Objects.nonNull(payment.getBankFee())) {
                payment.setFunctionalBankFee(Numbers.applyExchangeRate(payment.getBankFee(), exchangeRate.getExchangeRate(), functionalCurrencyCode));
            }
        }
    }

    private Optional<TransactionalExchangeRate> getExchangeRateByPaymentDate(
        Optional<Instant> paymentDate,
        String transactionalCurrency,
        String functionalCurrency
    ) {
        if (transactionalCurrency.equals(functionalCurrency)) {
            return Optional.empty();
        }
        Optional<TransactionalExchangeRate> exchangeRateOptional = paymentDate.isPresent()
            ? transactionalExchangeRateService.getRefreshExchangeRateAsOf(transactionalCurrency, functionalCurrency, paymentDate.get())
            : transactionalExchangeRateService.getRefreshExchangeRateAsOf(transactionalCurrency, functionalCurrency, clock.instant());
        if (exchangeRateOptional.isEmpty()) {
            throw new ConflictingStateException("Exchange rate not found for payment date");
        }
        return exchangeRateOptional;
    }

    public Payment updatePayment(Payment payment, DSLContext transactionContext) {
        Validator.validateNonNullArgument(payment, "Payment");
        Validator.validateNonNullArgument(payment.getPaymentId(), "payment ID");

        var optionalPayment = paymentDAO.getPaymentByPaymentId(payment.getPaymentId());
        if (optionalPayment.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.PAYMENT, payment.getPaymentId());
        }

        validateAndSetPaymentDate(payment);

        paymentDAO.updatePayment(payment, Optional.ofNullable(transactionContext));

        var optionalPaymentDetails = paymentDAO.getPaymentByPaymentId(payment.getPaymentId());
        if (optionalPaymentDetails.isEmpty()) {
            throw new IllegalArgumentException(String.format("payment with id %s not found", payment.getPaymentId()));
        }

        return optionalPaymentDetails.get();
    }

    public String deletePayment(String paymentId) {
        if (paymentId == null) {
            throw new IllegalArgumentException("paymentId is null");
        }

        var optionalPayment = paymentDAO.getPaymentByPaymentId(paymentId);
        if (optionalPayment.isEmpty()) {
            throw new IllegalArgumentException(String.format("invalid payment id %s", paymentId));
        }

        if (optionalPayment.get().getState() != PaymentState.SUCCEED) {
            throw new IllegalArgumentException(String.format("payment id %s is not in SUCCEED. Cannot delete the payment in this state.", paymentId));
        }

        paymentDAO.deletePayment(paymentId);
        return paymentId;
    }

    private static void validateAndSetPaymentDate(Payment payment) {
        Instant now = Instant.now();
        if (payment.getPaymentDate() == null) {
            payment.setPaymentDate(now);
        } else {
            if (payment.getPaymentDate().isAfter(now)) {
                throw new IllegalArgumentException("Payment date cannot be in the future");
            }
        }
    }

    public void updateVoidedPaymentAttributesInTransaction(
        String paymentId,
        PaymentState newState,
        PaymentStatus newStatus,
        DSLContext dslContext,
        Instant voidDate
    ) {
        Validator.validateNonNullArguments(paymentId, newState, newStatus);
        paymentDAO.updateVoidedPaymentAttributesInTransaction(paymentId, newState, newStatus, dslContext, voidDate);
    }

    private void populatePaymentId(Payment payment) {
        if (StringUtils.isEmpty(payment.getPaymentId())) {
            payment.setPaymentId(paymentIdGenerator.generate());
        }
    }

    public PaymentAttempt addPaymentAttempt(PaymentAttempt paymentAttempt, DSLContext transactionContext) {
        Validator.validateNonNullArgument(paymentAttempt, "paymentAttempt");
        return paymentDAO.addPaymentAttempt(paymentAttempt, transactionContext);
    }

    public PaymentAttempt updatePaymentAttempt(
        PaymentAttempt currentPaymentAttempt,
        PaymentAttempt newPaymentAttempt,
        DSLContext transactionContext
    ) {
        Validator.validateNonNullArguments(currentPaymentAttempt, newPaymentAttempt);
        validateStateTransitionForPaymentAttempt(currentPaymentAttempt.getState(), newPaymentAttempt.getState());
        return paymentDAO.updatePaymentAttempt(currentPaymentAttempt, newPaymentAttempt, transactionContext);
    }

    /**
     * Only to be used for migration purposes
     * Attach a default bank account and 0 bank fee to existing payments for a tenant
     * This is more of an orchestration layer hence it's a large method
     */
    public List<String> migrateExistingPaymentsForTenant(String paymentBankAccountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        LOGGER.info("Migrating existing payments for resolved tenant {}", tenantId);
        if (!featureService.isEnabled(Feature.PAYMENT_WITH_BANK_ACCOUNT)) {
            throw new ConflictingStateException(
                "Feature " + Feature.PAYMENT_WITH_BANK_ACCOUNT + " is not enabled. Cannot migrate existing payments for tenant " + tenantId
            );
        }
        Map<String, Entity> entityById = entityGetService
            .getEntitiesByTenantId(tenantId)
            .stream()
            .collect(Collectors.toMap(Entity::getEntityId, entity -> entity));
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return tenantDslContext.transactionResult(configuration -> {
            DSLContext transactionContext = DSL.using(configuration);

            // Fetch all existing payments for the tenant where bank account and bank fee
            // are null, use for update clause to lock rows
            List<Payment> paymentsForMigration = paymentDAO.getPaymentsByTenantIdAndNullBankAccountAndBankFeeForUpdate(tenantId, transactionContext);

            if (paymentsForMigration.isEmpty()) {
                LOGGER.info("No payments found for tenant {} with null bank account and bank fee", tenantId);
                return new ArrayList<>();
            }

            // Group payments by entity
            Map<String, List<Payment>> paymentsByEntity = paymentsForMigration.stream().collect(Collectors.groupingBy(Payment::getEntityId));
            if (paymentsByEntity.size() > 1) {
                throw new InvariantCheckFailedException(
                    "Multiple entities found for tenant " + tenantId + " while migrating payments. Expected only one entity with payment per tenant"
                );
            }

            // Migrate existing payments with a default payment bank account
            List<String> successfullyMigratedPayments = new ArrayList<>();
            paymentsByEntity.forEach((entityId, payments) -> {
                Entity entity = entityById.get(entityId);
                PaymentBankAccount migrationPaymentBankAccountForEntity;
                if (StringUtils.isNotEmpty(paymentBankAccountId)) {
                    LOGGER.info(
                        "migrateExistingPaymentsForTenant : using provided payment bank account id {} for entity {}",
                        paymentBankAccountId,
                        entityId
                    );
                    migrationPaymentBankAccountForEntity = paymentBankAccountService.getByPaymentBankAccountId(paymentBankAccountId);
                } else {
                    LOGGER.info("migrateExistingPaymentsForTenant : using default payment bank account for entity {}", entityId);
                    migrationPaymentBankAccountForEntity = paymentBankAccountService.getOrCreateDefaultPaymentBankAccountForEntity(
                        transactionContext,
                        entity
                    );
                }
                payments.forEach(payment -> {
                    payment.setPaymentBankAccountId(migrationPaymentBankAccountForEntity.getPaymentBankAccountId());
                    payment.setBankFee(BigDecimal.ZERO);
                    payment.setFunctionalBankFee(BigDecimal.ZERO);
                    Payment migratedPayment = updatePayment(payment, transactionContext);
                    successfullyMigratedPayments.add(migratedPayment.getPaymentId());
                });
            });

            // Validate that post migration all payments have a bank account and bank fee
            List<Payment> paymentsWithNullBankAccountAndBankFeeAfterMigration = paymentDAO.getPaymentsByTenantIdAndNullBankAccountAndBankFeeForUpdate(
                tenantId,
                transactionContext
            );
            if (!paymentsWithNullBankAccountAndBankFeeAfterMigration.isEmpty()) {
                throw new InvariantCheckFailedException(
                    "Migration failed. Some payments do not have bank account and bank fee set. The list is : " +
                    paymentsWithNullBankAccountAndBankFeeAfterMigration
                );
            }

            LOGGER.info("Successfully migrated payments {} for tenant {}", successfullyMigratedPayments, tenantId);
            return successfullyMigratedPayments;
        });
    }

    public void updatePaymentMetadata(DSLContext transactionContext, String tenantId, String paymentId, PaymentMetadata metadata) {
        paymentDAO.updatePaymentMetadata(transactionContext, tenantId, paymentId, metadata);
    }

    private void validateStateTransitionForPaymentAttempt(PaymentAttemptState currentState, PaymentAttemptState newState) {
        Validator.validateNonNullArguments(currentState, newState);
        if (currentState.isTerminal()) {
            throw new ServiceFailureException(String.format("Cannot update from terminal state %s to %s", currentState, newState));
        }
        if (PaymentAttemptState.VALID_STATE_TRANSITION_MAP.get(currentState).contains(newState)) {
            return;
        }
        throw new ServiceFailureException(
            String.format(
                "Invalid state transition from %s to %s. Valid new states for %s are: %s",
                currentState,
                newState,
                currentState,
                PaymentAttemptState.VALID_STATE_TRANSITION_MAP.get(currentState)
            )
        );
    }
}
