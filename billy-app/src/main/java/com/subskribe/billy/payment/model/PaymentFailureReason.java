package com.subskribe.billy.payment.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.payment.SupportedPaymentProvider;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.Objects;

@GraphQLName("PaymentFailureReason")
public class PaymentFailureReason {

    @GraphQLField
    @JsonProperty
    private final String type;

    @GraphQLField
    @JsonProperty
    private final String message;

    @GraphQLField
    @JsonProperty
    private final String errorCode;

    @GraphQLField
    @JsonProperty
    private final String declineCode;

    @GraphQLField
    @JsonProperty
    private final SupportedPaymentProvider paymentProvider;

    @GraphQLField
    @GraphQLNonNull
    @JsonProperty
    @JsonIgnore
    private final boolean isInternal;

    @GraphQLField
    @JsonProperty
    private final String status;

    @JsonCreator
    public static PaymentFailureReason create(
        @JsonProperty("type") String type,
        @JsonProperty("message") String message,
        @JsonProperty("errorCode") String errorCode,
        @JsonProperty("declineCode") String declineCode,
        @JsonProperty("paymentProvider") SupportedPaymentProvider paymentProvider,
        @JsonProperty("isInternal") boolean isInternal,
        @JsonProperty("status") String status
    ) {
        return builder()
            .type(type)
            .message(message)
            .errorCode(errorCode)
            .declineCode(declineCode)
            .paymentProvider(paymentProvider)
            .isInternal(isInternal)
            .status(status)
            .build();
    }

    private PaymentFailureReason(Builder builder) {
        type = builder.type;
        message = builder.message;
        errorCode = builder.errorCode;
        declineCode = builder.declineCode;
        paymentProvider = builder.paymentProvider;
        isInternal = builder.isInternal;
        status = builder.status;
    }

    public String getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getDeclineCode() {
        return declineCode;
    }

    public SupportedPaymentProvider getPaymentProvider() {
        return paymentProvider;
    }

    @JsonIgnore
    public boolean isInternal() {
        return isInternal;
    }

    public String getStatus() {
        return status;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static Builder builder(PaymentFailureReason failureReason) {
        return new Builder(failureReason);
    }

    public static class Builder {

        private String type;

        private String message;

        private String errorCode;

        private String declineCode;

        private SupportedPaymentProvider paymentProvider;

        private boolean isInternal;

        private String status;

        private Builder() {}

        private Builder(PaymentFailureReason failureReason) {
            type = failureReason.type;
            message = failureReason.message;
            errorCode = failureReason.errorCode;
            declineCode = failureReason.declineCode;
            paymentProvider = failureReason.paymentProvider;
            isInternal = failureReason.isInternal;
            status = failureReason.status;
        }

        public Builder type(String type) {
            this.type = type;
            return this;
        }

        public Builder message(String message) {
            this.message = message;
            return this;
        }

        public Builder errorCode(String errorCode) {
            this.errorCode = errorCode;
            return this;
        }

        public Builder declineCode(String declineCode) {
            this.declineCode = declineCode;
            return this;
        }

        public Builder paymentProvider(SupportedPaymentProvider paymentProvider) {
            this.paymentProvider = paymentProvider;
            return this;
        }

        public Builder isInternal(boolean isInternal) {
            this.isInternal = isInternal;
            return this;
        }

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public PaymentFailureReason build() {
            return new PaymentFailureReason(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PaymentFailureReason that = (PaymentFailureReason) o;
        return (
            isInternal == that.isInternal &&
            Objects.equals(type, that.type) &&
            Objects.equals(message, that.message) &&
            Objects.equals(errorCode, that.errorCode) &&
            Objects.equals(declineCode, that.declineCode) &&
            paymentProvider == that.paymentProvider &&
            Objects.equals(status, that.status)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(type, message, errorCode, declineCode, paymentProvider, isInternal, status);
    }
}
