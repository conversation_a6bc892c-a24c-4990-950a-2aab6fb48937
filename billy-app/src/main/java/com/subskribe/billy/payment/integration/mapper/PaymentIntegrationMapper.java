package com.subskribe.billy.payment.integration.mapper;

import com.subskribe.billy.configuration.SharedMapperConfig;
import com.subskribe.billy.jooq.default_schema.tables.records.PaymentStripeConnectIntegrationRecord;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper(config = SharedMapperConfig.class)
public abstract class PaymentIntegrationMapper {

    public abstract PaymentStripeConnectIntegration fromRecord(PaymentStripeConnectIntegrationRecord record);

    public abstract List<PaymentStripeConnectIntegration> fromRecords(List<PaymentStripeConnectIntegrationRecord> records);

    public abstract PaymentStripeConnectIntegrationRecord toRecord(PaymentStripeConnectIntegration paymentIntegration);
}
