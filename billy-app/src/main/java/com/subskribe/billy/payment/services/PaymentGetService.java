package com.subskribe.billy.payment.services;

import com.subskribe.billy.auth.authorizers.JobOnly;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.payment.db.PaymentDAO;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.model.PaymentAttempt;
import com.subskribe.billy.payment.model.PaymentLifecycleType;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.jooq.DSLContext;

public class PaymentGetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentGetService.class);

    private final PaymentDAO paymentDAO;

    @Inject
    public PaymentGetService(PaymentDAO paymentDAO) {
        this.paymentDAO = paymentDAO;
    }

    public List<Payment> getPayments(PaginationQueryParams paginationQueryParams) {
        return paymentDAO.getPayments(paginationQueryParams);
    }

    public Payment getPaymentByPaymentId(String paymentId) {
        if (paymentId == null) {
            LOGGER.info("paymentId is null");
            throw new InvalidInputException("paymentId is null");
        }

        var payment = paymentDAO.getPaymentByPaymentId(paymentId);
        if (payment.isEmpty()) {
            LOGGER.info("payment with id {} not found", paymentId);
            throw new ObjectNotFoundException(BillyObjectType.PAYMENT, paymentId);
        }

        return payment.get();
    }

    public Payment getPaymentByPaymentIdInTransaction(DSLContext txnContext, String paymentId) {
        return paymentDAO
            .getPaymentByPaymentIdInTransaction(txnContext, paymentId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PAYMENT, paymentId));
    }

    @Deprecated
    public Payment getPaymentByTransactionId(String tenantId, String transactionId) {
        Validator.validateNonNullArguments(tenantId, transactionId);
        Optional<Payment> payment = paymentDAO.getPaymentByTransactionId(tenantId, transactionId);
        if (payment.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.PAYMENT_BY_TRANSACTION_ID, transactionId);
        }

        return payment.get();
    }

    public List<Payment> getPaymentsByAccountId(String accountId) {
        if (accountId == null) {
            LOGGER.error("accountId is null");
            throw new IllegalArgumentException("accountId is null");
        }

        try {
            return paymentDAO.getPaymentsByAccountId(accountId);
        } catch (Exception ex) {
            LOGGER.error("error getting payments for account {}", accountId, ex);
            throw ex;
        }
    }

    public List<Payment> getPaymentsByPaymentIds(List<String> paymentIds) {
        Validator.validateStringCollectionNotBlank(paymentIds, "paymentIds");
        return paymentDAO.getPaymentsByPaymentIds(paymentIds);
    }

    public List<Payment> getAutomaticPaymentsByPaymentIds(List<String> paymentIds) {
        Validator.validateStringCollectionNotBlank(paymentIds, "paymentIds");
        return paymentDAO.getAutomaticPaymentsByPaymentIds(paymentIds);
    }

    public boolean checkIfAnyOfThePaymentsAreNotVoided(List<String> paymentIds) {
        Validator.validateStringCollectionNotBlank(paymentIds, "paymentIds");
        return paymentDAO.checkWhetherAllThesePaymentsAreVoided(paymentIds);
    }

    public boolean paymentCannotBeVoided(Payment payment) {
        return payment.getLifecycleType() != PaymentLifecycleType.OFFLINE;
    }

    @JobOnly
    public List<Payment> getPaymentsToReconcile(int maxRecords) {
        return paymentDAO.getPaymentsToReconcile(maxRecords);
    }

    public List<Payment> getSucceededPayments(String accountId) {
        return paymentDAO.getSucceededPayments(accountId);
    }

    public List<PaymentAttempt> getPaymentAttemptsByPaymentId(String paymentId) {
        Validator.validateNonNullArgument(paymentId, "paymentId");
        return paymentDAO.getPaymentAttemptsByPaymentId(paymentId);
    }

    public Optional<PaymentAttempt> getPaymentAttemptByTransactionId(String transactionId) {
        Validator.validateNonNullArgument(transactionId, "transactionId");
        return paymentDAO.getPaymentAttemptByTransactionId(transactionId);
    }

    public List<Payment> fetchPaymentsForBankAccount(String paymentBankAccountId) {
        return paymentDAO.getPaymentsByPaymentBankAccountId(paymentBankAccountId);
    }
}
