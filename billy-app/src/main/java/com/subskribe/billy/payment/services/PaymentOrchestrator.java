package com.subskribe.billy.payment.services;

import static com.subskribe.billy.payment.stripe.service.StripeService.STRIPE_CALL_FAILED_ERROR_MESSAGE;
import static com.subskribe.billy.payment.task.PaymentRetryQueuedTaskProcessor.AUTOMATED_PAYMENT_RETRY_ATTEMPT_NAME;
import static com.subskribe.billy.payment.task.PaymentRetryQueuedTaskProcessor.AUTOMATED_PAYMENT_RETRY_NUMBER_METADATA_KEY;
import static com.subskribe.billy.payment.task.PaymentRetryQueuedTaskProcessor.LAST_PAYMENT_ATTEMPT_ID_METADATA_KEY;
import static com.subskribe.billy.payment.task.PaymentRetryQueuedTaskProcessor.RETRY_ACTION.SKIPPED;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.model.PaymentIntent;
import com.stripe.net.RequestOptions;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.AccountPaymentConfiguration;
import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.account.model.AccountPaymentMethodStatus;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.TenantAccountPartitionKey;
import com.subskribe.billy.event.model.payload.ImmutablePaymentAttemptFailedEventPayload;
import com.subskribe.billy.event.model.payload.ImmutablePaymentRetriesExhaustedEventPayload;
import com.subskribe.billy.event.model.payload.PaymentAttemptFailedEventPayload;
import com.subskribe.billy.event.model.payload.PaymentRetriesExhaustedEventPayload;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoicedunning.model.DunningReminderType;
import com.subskribe.billy.invoicedunning.services.InvoiceDunningService;
import com.subskribe.billy.invoicesettlement.model.InvoiceBalance;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationStatus;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.model.AccountAutomaticPaymentMethod;
import com.subskribe.billy.payment.model.FailedPaymentAttemptData;
import com.subskribe.billy.payment.model.ImmutablePaymentAttemptMetadata;
import com.subskribe.billy.payment.model.ImmutablePaymentMetadata;
import com.subskribe.billy.payment.model.IntervalBasedPaymentRetryPolicy.NextRetryAttemptDetail;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.model.PaymentAttempt;
import com.subskribe.billy.payment.model.PaymentAttemptMetadata;
import com.subskribe.billy.payment.model.PaymentAttemptMetadata.AttemptRetryMetadata;
import com.subskribe.billy.payment.model.PaymentAttemptState;
import com.subskribe.billy.payment.model.PaymentErrorCode;
import com.subskribe.billy.payment.model.PaymentFailureReason;
import com.subskribe.billy.payment.model.PaymentLifecycleType;
import com.subskribe.billy.payment.model.PaymentMetadata;
import com.subskribe.billy.payment.model.PaymentMetadata.PaymentRetryMetadata;
import com.subskribe.billy.payment.model.PaymentProcessingData;
import com.subskribe.billy.payment.model.PaymentProviderCustomer;
import com.subskribe.billy.payment.model.PaymentProviderPaymentMethod;
import com.subskribe.billy.payment.model.PaymentRetryConfig;
import com.subskribe.billy.payment.model.PaymentRetryExceptionModels.PaymentIntentBaseException;
import com.subskribe.billy.payment.model.PaymentRetryExceptionModels.PaymentIntentFailedException;
import com.subskribe.billy.payment.model.PaymentRetryPolicy;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.payment.stripe.service.StripeService;
import com.subskribe.billy.payment.task.PaymentRetryQueuedTaskProcessor;
import com.subskribe.billy.payment.task.PaymentRetryQueuedTaskProcessor.PaymentRetryResult;
import com.subskribe.billy.postgres.PostgresAdvisoryLock;
import com.subskribe.billy.shared.enums.PaymentState;
import com.subskribe.billy.shared.invariants.InvariantChecker;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.product.ProductArea;
import com.subskribe.billy.shared.product.ProductFeature;
import com.subskribe.billy.shared.product.ProductFeatureAvailability;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenantjob.model.ImmutableTenantJob;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobModule;
import com.subskribe.billy.tenantjob.model.TenantJobObjectModel;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.tenantjob.service.TenantJobGetService;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

@SuppressWarnings("PMD.UnusedPrivateMethod")
public class PaymentOrchestrator {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentOrchestrator.class);

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    private static final String PAYMENT_SETTLEMENT_NOTE_FORMAT = "Payment method identifier: %s";

    private static final String INVOICE_PAYMENT_LOCK_FORMAT = "%s/%s/invoice_payment";

    private static final String PAYMENT_ATTEMPT_FAILURE_LOCK_FORMAT = "%s/%s/payment_attempt_failure";

    private static final ProductArea PAYMENT_PRODUCT_AREA = new ProductArea("Payment");

    private static final ProductFeature AUTOMATED_PAYMENT_RETRY = new ProductFeature(
        PAYMENT_PRODUCT_AREA,
        "Automated payment retry",
        ProductFeatureAvailability.BETA
    );

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    private final PaymentService paymentService;

    private final PaymentGetService paymentGetService;

    private final InvoiceDunningService invoiceDunningService;

    private final InvoiceSettlementService invoiceSettlementService;

    private final AccountPaymentMethodService accountPaymentMethodService;

    private final AccountPaymentMethodGetService accountPaymentMethodGetService;

    private final AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService;

    private final InvoiceRetrievalService invoiceRetrievalService;

    private final PaymentProviderService paymentProviderService;

    private final StripeService stripeService;

    private final PaymentStateProcessor paymentStateProcessor;

    private final PaymentConfigurationService paymentConfigurationService;

    private final TenantJobDispatcherService tenantJobDispatcherService;

    private final Clock clock;

    private final FeatureService featureService;

    private final BillyConfiguration billyConfiguration;

    private final TenantJobGetService tenantJobGetService;

    private final EventPublishingService eventPublishingService;

    private final PaymentIntegrationGetService paymentIntegrationGetService;

    private final AccountGetService accountGetService;

    @Inject
    public PaymentOrchestrator(
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        PaymentService paymentService,
        PaymentGetService paymentGetService,
        InvoiceDunningService invoiceDunningService,
        InvoiceSettlementService invoiceSettlementService,
        AccountPaymentMethodService accountPaymentMethodService,
        AccountPaymentMethodGetService accountPaymentMethodGetService,
        AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService,
        InvoiceRetrievalService invoiceRetrievalService,
        PaymentProviderService paymentProviderService,
        StripeService stripeService,
        PaymentStateProcessor paymentStateProcessor,
        PaymentConfigurationService paymentConfigurationService,
        TenantJobDispatcherService tenantJobDispatcherService,
        Clock clock,
        FeatureService featureService,
        BillyConfiguration billyConfiguration,
        TenantJobGetService tenantJobGetService,
        EventPublishingService eventPublishingService,
        PaymentIntegrationGetService paymentIntegrationGetService,
        AccountGetService accountGetService
    ) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.paymentService = paymentService;
        this.paymentGetService = paymentGetService;
        this.invoiceDunningService = invoiceDunningService;
        this.invoiceSettlementService = invoiceSettlementService;
        this.accountPaymentMethodService = accountPaymentMethodService;
        this.accountPaymentMethodGetService = accountPaymentMethodGetService;
        this.accountAutomaticPaymentMethodGetService = accountAutomaticPaymentMethodGetService;
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.paymentProviderService = paymentProviderService;
        this.stripeService = stripeService;
        this.paymentStateProcessor = paymentStateProcessor;
        this.paymentConfigurationService = paymentConfigurationService;
        this.tenantJobDispatcherService = tenantJobDispatcherService;
        this.clock = clock;
        this.featureService = featureService;
        this.billyConfiguration = billyConfiguration;
        this.tenantJobGetService = tenantJobGetService;
        this.eventPublishingService = eventPublishingService;
        this.paymentIntegrationGetService = paymentIntegrationGetService;
        this.accountGetService = accountGetService;
    }

    public Optional<String> processPayment(
        Invoice invoice,
        AccountPaymentMethod accountPaymentMethod,
        PaymentProviderCustomer paymentProviderCustomer,
        PaymentProviderPaymentMethod paymentProviderPaymentMethod,
        PaymentAttemptType paymentAttemptType,
        PaymentRetryProcessingData paymentRetryProcessingData
    ) {
        try {
            DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
            PaymentAttemptAndInvoiceBalance paymentAttemptAndInvoiceBalance = dslContext.transactionResult(configuration -> {
                DSLContext transactionContext = DSL.using(configuration);

                tryAndAcquireInvoicePaymentLock(transactionContext, invoice.getInvoiceNumber().getNumber());
                InvoiceBalance invoiceBalance = invoiceSettlementService.getInvoiceBalance(invoice.getInvoiceNumber());
                try {
                    // This should be checked within a lock as we are relying on it to be correct
                    // It's okay to release the lock once settlement application is created as we have a db state check on insert
                    stripeService.validateInvoiceAndInvoiceBalance(invoice, invoiceBalance); // TODO : move this out of StripeService later on
                } catch (ConflictingStateException e) {
                    // Invoice is already paid or voided
                    LOGGER.info("Invoice {} is already paid or voided, skipping payment processing", invoice.getInvoiceNumber());
                    return new PaymentAttemptAndInvoiceBalance(Optional.empty(), invoiceBalance);
                }
                PaymentAttempt savedPaymentAttempt = initiatePaymentAttemptForAutomaticPayment(
                    transactionContext,
                    invoice,
                    accountPaymentMethod,
                    invoiceBalance,
                    paymentProviderPaymentMethod,
                    paymentAttemptType,
                    paymentRetryProcessingData
                );
                return new PaymentAttemptAndInvoiceBalance(Optional.of(savedPaymentAttempt), invoiceBalance);
            });

            Optional<PaymentAttempt> savedPaymentAttempt = paymentAttemptAndInvoiceBalance.paymentAttempt();
            InvoiceBalance invoiceBalance = paymentAttemptAndInvoiceBalance.invoiceBalance();

            if (savedPaymentAttempt.isEmpty()) {
                LOGGER.info("No initial payment attempt created for invoice {}", invoice.getInvoiceNumber());
                return Optional.empty();
            }

            return stripeService.processPayment(
                invoice,
                accountPaymentMethod,
                paymentProviderCustomer,
                paymentProviderPaymentMethod,
                savedPaymentAttempt.get(),
                invoiceBalance
            );
        } catch (PaymentIntentBaseException e) {
            LOGGER.info(
                "[processPayment] Payment intent processing failed for invoice {} with error: {}",
                invoice.getInvoiceNumber(),
                e.getMessage()
            );
            boolean isPaymentIntentFailed = e instanceof PaymentIntentFailedException;
            triggerPaymentAttemptFailureActions(e.getFailedPaymentAttemptData(), isPaymentIntentFailed);
        }
        LOGGER.info("[processPayment] Payment processing failed for invoice {}, returning empty object", invoice.getInvoiceNumber());
        return Optional.empty();
    }

    public PaymentRetryResult attemptRetry(String paymentId, String lastPaymentAttemptId, int automatedPaymentRetryNumber, String attemptName) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        LOGGER.info(
            "[attemptRetry] Attempting retry for tenant {} payment {} and last payment attempt {}",
            tenantId,
            paymentId,
            lastPaymentAttemptId
        );

        Payment payment = paymentGetService.getPaymentByPaymentId(paymentId);
        SettlementApplication settlementApplication = getActiveSettlementApplication(payment);

        if (isAccountExcludedFromRetries(payment)) {
            return new PaymentRetryResult(SKIPPED, String.format("Account %s is excluded from payment retries", payment.getCustomerAccountId()));
        }

        Optional<PaymentRetryResult> validatedPaymentStateForRetry = validatePaymentStateForRetry(
            settlementApplication,
            payment,
            paymentId,
            lastPaymentAttemptId
        );
        if (validatedPaymentStateForRetry.isPresent()) {
            return validatedPaymentStateForRetry.get();
        }

        String customerAccountId = payment.getCustomerAccountId();
        Pair<String, PaymentMethodData> validatedPaymentMethodStateForRetry = validatePaymentMethodStateForRetry(
            tenantId,
            payment,
            customerAccountId
        );

        if (validatedPaymentMethodStateForRetry.getRight() == null) {
            String message = validatedPaymentMethodStateForRetry.getLeft();
            LOGGER.info("Could not validate payment method for retry - {}", message);
            return new PaymentRetryResult(SKIPPED, message);
        }

        PaymentMethodData paymentMethodData = validatedPaymentMethodStateForRetry.getRight();
        Invoice invoice = invoiceRetrievalService.getInvoice(new Invoice.Number(settlementApplication.getInvoiceNumber()));
        processPayment(
            invoice,
            paymentMethodData.accountPaymentMethod,
            paymentMethodData.paymentProviderCustomer,
            paymentMethodData.paymentProviderPaymentMethod,
            PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
            new PaymentRetryProcessingData(paymentId, lastPaymentAttemptId, automatedPaymentRetryNumber, attemptName)
        );

        return new PaymentRetryResult(
            PaymentRetryQueuedTaskProcessor.RETRY_ACTION.RETRIED,
            String.format("Payment %s retried successfully for account %s", paymentId, customerAccountId)
        );
    }

    // TODO : probably a better name for this can be handleCreatedPaymentAttempt? But intent seems common enough across PGs
    public void handleCreatedPaymentIntent(PaymentProcessingData paymentProcessingData) {
        Payment payment = paymentProcessingData.getPayment();
        PaymentAttempt createdPaymentAttempt = paymentProcessingData.getPaymentAttempt();
        PaymentProviderCustomer paymentProviderCustomer = paymentProcessingData.getPaymentProviderCustomer();
        PaymentProviderPaymentMethod paymentProviderPaymentMethod = paymentProcessingData.getPaymentProviderPaymentMethod();
        Invoice.Number invoiceNumber = paymentProcessingData.getInvoiceNumber();

        LOGGER.info(
            "[handleCreatedPaymentIntent] Handling created payment intent for payment attempt {} and invoice {}",
            createdPaymentAttempt.getId(),
            invoiceNumber.getNumber()
        );

        try {
            // TODO : handle multiple PGs here
            // TODO : remove direct Stripe object access here and probably return just the transaction id
            RequestOptions requestOptions = stripeService.getRequestOptions(createdPaymentAttempt.getId().toString());
            PaymentIntent paymentIntent = stripeService.createPaymentIntentForAutomaticPayment(
                createdPaymentAttempt,
                paymentProviderCustomer,
                paymentProviderPaymentMethod,
                invoiceNumber,
                payment.getAmount(),
                payment.getCurrencyCode(),
                requestOptions
            );
            paymentStateProcessor.toConfirmedPaymentAttempt(createdPaymentAttempt, paymentIntent.getId());
        } catch (IllegalStateException e) {
            // Invalid state, mark as failed
            PaymentFailureReason failureReason = PaymentFailureReason.builder()
                .errorCode(PaymentErrorCode.API_CALL_FAILED.name())
                .isInternal(true)
                .build();
            DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

            dslContext.transaction(configuration -> {
                DSLContext transactionContext = DSL.using(configuration);
                handleFailedPaymentAttempt(transactionContext, payment, createdPaymentAttempt, failureReason, Instant.now());
            });
        } catch (PaymentIntentBaseException e) {
            boolean paymentIntentFailed = e instanceof PaymentIntentFailedException;
            triggerPaymentAttemptFailureActions(e.getFailedPaymentAttemptData(), paymentIntentFailed);
        } catch (Exception e) {
            LOGGER.warn(STRIPE_CALL_FAILED_ERROR_MESSAGE, e);
        }
    }

    public void handleConfirmedOrProcessingPaymentIntent(String paymentIntentId, PaymentAttempt confirmedPaymentAttempt) {
        LOGGER.info(
            "[handleConfirmedOrProcessingPaymentIntent] Handling confirmed or processing payment intent for payment attempt {} and payment intent {}",
            confirmedPaymentAttempt.getId(),
            paymentIntentId
        );

        try {
            // TODO : this return value doesn't seem to be needed anymore, we can just rely on the exception handling
            Optional<FailedPaymentAttemptData> failedPaymentAttemptData = stripeService.handleConfirmedOrProcessingPaymentIntent(
                paymentIntentId,
                confirmedPaymentAttempt
            );
            failedPaymentAttemptData.ifPresent(data -> triggerPaymentAttemptFailureActions(data, true));
        } catch (PaymentIntentBaseException e) {
            boolean isPaymentIntentFailed = e instanceof PaymentIntentFailedException;
            triggerPaymentAttemptFailureActions(e.getFailedPaymentAttemptData(), isPaymentIntentFailed);
        }
    }

    // TODO : handle retry logic here
    public void triggerPaymentAttemptFailureActions(FailedPaymentAttemptData failedPaymentAttemptData, boolean paymentIntentFailed) {
        LOGGER.info(
            "Triggering failure actions for payment attempt {} and paymentIntentFailed: {}",
            failedPaymentAttemptData.getPaymentAttempt(),
            paymentIntentFailed
        );
        Payment paymentData = failedPaymentAttemptData.getPayment();
        PaymentAttempt paymentAttempt = failedPaymentAttemptData.getPaymentAttempt();
        PaymentFailureReason paymentFailureReason = failedPaymentAttemptData.getPaymentFailureReason();
        Instant failedOn = failedPaymentAttemptData.getFailedOn();
        Optional<AccountPaymentMethod> accountPaymentMethodToUpdate = failedPaymentAttemptData.getAccountPaymentMethodToUpdate();
        SettlementApplication settlementApplication = getActiveSettlementApplication(paymentData);
        String invoiceNumber = settlementApplication.getInvoiceNumber();

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        dslContext.transaction(configuration -> {
            DSLContext transactionContext = DSL.using(configuration);

            if (paymentAttemptInTerminalState(transactionContext, paymentAttempt)) {
                return;
            }

            boolean paymentRetryScheduled = schedulePaymentRetryIfApplicable(transactionContext, paymentData, paymentAttempt);
            Payment payment = paymentGetService.getPaymentByPaymentIdInTransaction(transactionContext, paymentData.getPaymentId());
            if (paymentRetryScheduled) {
                LOGGER.info(
                    "Payment retry scheduled for last payment attempt {}, marking attempt as failed but ignoring side-effects",
                    paymentAttempt.getId()
                );
                // We have to mark the payment attempt as failed even in case of retry, but we don't need to handle account payment method failure or send dunning
                // The advantage of doing this after job is scheduled is in case of a distributed failure we will have reconciliation job to handle the payment attempt later,
                // and for the retry job we will have a check that the last payment attempt is indeed marked as failed
                paymentStateProcessor.toFailedPaymentAttempt(transactionContext, paymentAttempt, paymentFailureReason);

                sendPaymentAttemptFailedWebhook(configuration, payment, paymentAttempt, invoiceNumber, settlementApplication, paymentFailureReason);

                return;
            }

            LOGGER.info("No more attempts left for payment attempt {}, triggering side-effects", paymentAttempt.getId());

            handleAccountPaymentMethodFailure(transactionContext, payment, accountPaymentMethodToUpdate);

            paymentStateProcessor.toFailedPaymentAttempt(transactionContext, paymentAttempt, paymentFailureReason);

            triggerPaymentAttemptFailedStateTransition(transactionContext, payment, paymentAttempt, failedOn, paymentFailureReason);

            if (paymentIntentFailed && payment.getLifecycleType() == PaymentLifecycleType.AUTOMATIC) {
                sendAutoPayFailedDunning(new Invoice.Number(invoiceNumber), payment);
            }
        });
    }

    private void sendPaymentAttemptFailedWebhook(
        Configuration configuration,
        Payment payment,
        PaymentAttempt paymentAttempt,
        String invoiceNumber,
        SettlementApplication settlementApplication,
        PaymentFailureReason failureReason
    ) {
        if (featureService.isNotEnabled(Feature.PAYMENT_RETRY) || payment.getLifecycleType() != PaymentLifecycleType.AUTOMATIC) {
            return;
        }

        LOGGER.info("Sending payment attempt failed webhook for payment attempt {} and invoice {}", paymentAttempt.getId(), invoiceNumber);
        PaymentProviderPaymentMethod paymentProviderPaymentMethod = validateAndGetPaymentProviderPaymentMethodForWebhook(payment);

        PaymentAttemptMetadata paymentAttemptMetadata = InvariantChecker.nonNullArg(paymentAttempt.getMetadata(), "paymentAttemptMetadata");
        AttemptRetryMetadata attemptRetryMetadata = paymentAttemptMetadata
            .getRetryMetadata()
            .orElseThrow(() ->
                new ServiceFailureException(
                    String.format("Retry metadata is not present in payment attempt metadata for payment attempt %s", paymentAttempt.getId())
                )
            );
        String attemptName = attemptRetryMetadata.attemptName();

        PaymentAttemptFailedEventPayload paymentAttemptFailedEventPayload = ImmutablePaymentAttemptFailedEventPayload.builder()
            .paymentGatewayIntegrationId(paymentProviderPaymentMethod.getIntegrationId().toString())
            .accountPaymentMethodId(payment.getPaymentMethodId().toString())
            .invoiceNumber(invoiceNumber)
            .paymentId(payment.getPaymentId())
            .paymentAttemptId(paymentAttempt.getId().toString())
            .failureReason(failureReason.getErrorCode() + " : " + failureReason.getMessage())
            .attemptNumber(paymentAttempt.getAttemptNumber())
            .attemptName(attemptName)
            .build();

        LOGGER.info("Publishing payment attempt failed event for payment attempt {}", paymentAttempt.getId());

        try {
            byte[] payloadBytes = OBJECT_MAPPER.writeValueAsBytes(paymentAttemptFailedEventPayload);
            eventPublishingService.publishEventInTransaction(
                configuration,
                EventType.PAYMENT_ATTEMPT_FAILED,
                paymentAttempt.getTenantId(),
                settlementApplication.getEntityId(),
                settlementApplication.getCustomerAccountId(),
                payloadBytes
            );
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Failed to serialize payment attempt failed event payload", e);
        }
    }

    private PaymentProviderPaymentMethod validateAndGetPaymentProviderPaymentMethodForWebhook(Payment payment) {
        PaymentMetadata paymentMetadata = InvariantChecker.nonNullArg(payment.getMetadata(), "paymentMetadata");
        PaymentRetryMetadata paymentRetryMetadata = paymentMetadata
            .getRetryMetadata()
            .orElseThrow(() ->
                new ServiceFailureException(String.format("Retry metadata is not present in payment metadata for payment %s", payment.getPaymentId()))
            );

        UUID accountPaymentMethodId = paymentRetryMetadata.accountPaymentMethodId();
        return paymentProviderService
            .getPaymentProviderPaymentMethodById(accountPaymentMethodId, true)
            .orElseThrow(() -> new ServiceFailureException("Payment provider payment method not found for ID: " + accountPaymentMethodId));
    }

    private void sendPaymentRetriesExhaustedWebhook(
        Configuration configuration,
        Payment payment,
        PaymentAttempt paymentAttempt,
        String invoiceNumber,
        SettlementApplication settlementApplication,
        PaymentFailureReason failureReason
    ) {
        if (
            featureService.isNotEnabled(Feature.PAYMENT_RETRY) ||
            payment.getLifecycleType() != PaymentLifecycleType.AUTOMATIC ||
            payment.getMetadata() == null
        ) { // NOTE : This gets called irrespective of retries being configured, so we should fire this only when retry was configured at time of payment creation
            return;
        }

        PaymentProviderPaymentMethod paymentProviderPaymentMethod = validateAndGetPaymentProviderPaymentMethodForWebhook(payment);

        PaymentRetriesExhaustedEventPayload paymentRetriesExhaustedEventPayload = ImmutablePaymentRetriesExhaustedEventPayload.builder()
            .paymentGatewayIntegrationId(paymentProviderPaymentMethod.getIntegrationId().toString())
            .accountPaymentMethodId(payment.getPaymentMethodId().toString())
            .invoiceNumber(invoiceNumber)
            .paymentId(payment.getPaymentId())
            .paymentAttemptId(paymentAttempt.getId().toString())
            .failureReason(failureReason.getErrorCode() + " : " + failureReason.getMessage())
            .build();

        LOGGER.info("Sending payment retries exhausted webhook for payment attempt {} and invoice {}", paymentAttempt.getId(), invoiceNumber);

        try {
            byte[] payloadBytes = OBJECT_MAPPER.writeValueAsBytes(paymentRetriesExhaustedEventPayload);
            eventPublishingService.publishEventInTransaction(
                configuration,
                EventType.PAYMENT_RETRIES_EXHAUSTED,
                paymentAttempt.getTenantId(),
                settlementApplication.getEntityId(),
                settlementApplication.getCustomerAccountId(),
                payloadBytes
            );
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Failed to serialize payment retries exhausted event payload", e);
        }
    }

    private SettlementApplication getActiveSettlementApplication(Payment payment) {
        List<SettlementApplication> settlementApplications = invoiceSettlementService.getSettlementApplicationsByPaymentId(payment.getPaymentId());
        if (settlementApplications.size() != 1) {
            throw new ServiceFailureException(
                String.format("[attemptRetry] Multiple settlement applications found for payment %s, expected only one", payment.getPaymentId())
            );
        }
        return settlementApplications.get(0);
    }

    private boolean paymentAttemptInTerminalState(DSLContext transactionContext, PaymentAttempt paymentAttempt) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        String key = String.format(PAYMENT_ATTEMPT_FAILURE_LOCK_FORMAT, tenantId, paymentAttempt.getId());
        Optional<Long> lock = PostgresAdvisoryLock.tryAcquireLock(transactionContext, key);
        if (lock.isEmpty()) {
            String message = String.format(
                "[triggerPaymentAttemptFailureActions] Failed to acquire advisory lock for payment attempt %s, skipping failure actions",
                paymentAttempt.getId()
            );
            LOGGER.info(message);
            throw new ConflictingStateException(message);
        }

        // We should avoid double processing, if a webhook arrives for a payment attempt that is already in terminal state,
        // there can be a race condition here as well, which is evident in local because of no delay, so we take a lock and read the state from the database
        PaymentAttempt paymentAttemptRefreshedFromDb = paymentGetService
            .getPaymentAttemptByTransactionId(paymentAttempt.getTransactionId())
            .orElseThrow(() ->
                new ServiceFailureException(
                    String.format("[triggerPaymentAttemptFailureActions] Payment attempt %s not found in database", paymentAttempt.getTransactionId())
                )
            );

        if (paymentAttemptRefreshedFromDb.getState().isTerminal()) {
            LOGGER.info(
                "[triggerPaymentAttemptFailureActions] Payment attempt {} is already in terminal state {}, skipping failure actions",
                paymentAttemptRefreshedFromDb.getId(),
                paymentAttemptRefreshedFromDb.getState()
            );
            return true;
        }
        return false;
    }

    private boolean schedulePaymentRetryIfApplicable(DSLContext dslContext, Payment payment, PaymentAttempt paymentAttempt) {
        if (featureService.isNotEnabled(Feature.PAYMENT_RETRY)) {
            LOGGER.info("Payment retry feature is disabled, skipping retry for payment attempt {}", paymentAttempt.getId());
            return false;
        }

        PaymentMetadata paymentMetadata = payment.getMetadata();

        Optional<PaymentRetryMetadata> paymentRetryMetadata = paymentMetadata != null ? paymentMetadata.getRetryMetadata() : Optional.empty();

        // TODO : check for account enabled for retries or not, if not then skip retry
        //  Even if account is enabled for payment retries enabled then retry config might not have existed
        if (shouldSkipPaymentRetry(payment, paymentAttempt, paymentRetryMetadata)) {
            return false;
        }

        PaymentRetryConfig paymentRetryConfig = paymentConfigurationService.getPaymentRetryConfigById(
            dslContext,
            paymentRetryMetadata.get().paymentRetryConfigId()
        );
        PaymentRetryPolicy policy = paymentRetryConfig.getPolicy();

        List<PaymentAttempt> automatedPaymentAttempts = paymentGetService
            .getPaymentAttemptsByPaymentId(payment.getPaymentId())
            .stream()
            .filter(
                attempt ->
                    attempt.getMetadata() != null &&
                    attempt.getMetadata().getRetryMetadata().isPresent() &&
                    attempt.getMetadata().getRetryMetadata().get().type() != PaymentAttemptType.MANUAL_RETRY_ATTEMPT // Exclude manual retry attempts
            )
            .sorted(Comparator.comparingInt(PaymentAttempt::getAttemptNumber))
            .toList();
        int paymentAttemptsCount = automatedPaymentAttempts.size();
        PaymentAttempt lastPaymentAttempt = automatedPaymentAttempts.get(paymentAttemptsCount - 1);

        // This is a hard safety check in case of any synchronization/race-condition issue that can happen unexpectedly.
        // Because we can have a delay in receiving webhook/reconciling the payment, if there's any bug,
        // some new payment attempt could have been created in the system, in that case this particular attempt is not the latest one and
        // we don't want to schedule a retry job based on it.
        if (lastPaymentAttempt.getId().compareTo(paymentAttempt.getId()) != 0) {
            throw new ServiceFailureException(
                String.format(
                    "Last payment attempt %s doesn't match the failed payment attempt to process %s",
                    lastPaymentAttempt.getId(),
                    paymentAttempt.getId()
                )
            );
        }

        // The first attempt is the original attempt, so we start counting from 1, hence <=
        if (paymentAttemptsCount <= policy.getTotalAttempts()) {
            TenantJob dispatchedJob = dispatchPaymentRetryJob(dslContext, payment, paymentAttempt, paymentAttemptsCount, policy);
            LOGGER.info(
                "Dispatched payment retry job {} for payment attempt {} with delay until {}",
                dispatchedJob.getJobId(),
                paymentAttempt.getId(),
                dispatchedJob.getDelayedUntil()
            );

            updateNextAttemptDetailInPaymentMetadata(
                dslContext,
                paymentMetadata,
                paymentRetryMetadata.get(),
                payment,
                paymentAttempt,
                Optional.of(new PaymentMetadata.NextAutomatedRetryDetail(dispatchedJob.getDelayedUntil().get(), dispatchedJob.getJobId()))
            );
            return true;
        }

        updateNextAttemptDetailInPaymentMetadata(dslContext, paymentMetadata, paymentRetryMetadata.get(), payment, paymentAttempt, Optional.empty());
        return false;
    }

    private boolean shouldSkipPaymentRetry(Payment payment, PaymentAttempt paymentAttempt, Optional<PaymentRetryMetadata> retryMetadata) {
        if (isAccountExcludedFromRetries(payment)) return true;

        if (retryMetadata.isEmpty()) {
            LOGGER.info("No retry metadata found for payment", payment.getPaymentId());
            return true;
        }

        PaymentAttemptMetadata paymentAttemptMetadata = paymentAttempt.getMetadata();
        Optional<AttemptRetryMetadata> attemptRetryMetadata = paymentAttemptMetadata != null
            ? paymentAttemptMetadata.getRetryMetadata()
            : Optional.empty();

        if (attemptRetryMetadata.isPresent() && attemptRetryMetadata.get().type() == PaymentAttemptType.SELF_SERVE) {
            LOGGER.info("Skipping payment retry for self-serve payment attempt {}", paymentAttempt.getId());
            return true;
        }

        if (payment.getLifecycleType() == PaymentLifecycleType.ONE_TIME) {
            LOGGER.info("Skipping payment retry for one-time payment {}", payment.getPaymentId());
            return true;
        }

        Optional<TenantJob> existingTenantJob = tenantJobGetService.getActiveJobByTypeAndObjectId(
            TenantJobType.PAYMENT_RETRY,
            payment.getPaymentId()
        );

        if (existingTenantJob.isPresent()) {
            TenantJob existingTenantJobValue = existingTenantJob.get();
            String lastFailedPaymentAttemptId = existingTenantJobValue.getMetadata().get(LAST_PAYMENT_ATTEMPT_ID_METADATA_KEY);

            // This is fine because this flow can occur within the context of a retry job, for the previous failed payment attempt, which is still in processing state.
            // We will schedule a new retry job for the current payment attempt and this job would be marked successful.
            if (!lastFailedPaymentAttemptId.equals(paymentAttempt.getId().toString())) {
                LOGGER.info(
                    "Schedule a new payment retry job for payment attempt {}, existing job {} has last failed attempt {}",
                    paymentAttempt.getId(),
                    existingTenantJobValue.getJobId(),
                    lastFailedPaymentAttemptId
                );
                return false;
            }

            // We can't have multiple payment retry jobs for the same last failed payment attempt
            // NOTE : This should never happen as we are marking the payment attempt as failed and scheduling the retry job in the same transaction
            throw new ConflictingStateException(
                String.format(
                    "Payment retry job already exists for payment attempt %s with job id %s",
                    paymentAttempt.getId(),
                    existingTenantJobValue.getJobId()
                )
            );
        }
        return false;
    }

    private boolean isAccountExcludedFromRetries(Payment payment) {
        String accountId = payment.getCustomerAccountId();
        Optional<AccountPaymentConfiguration> accountPaymentConfiguration = accountGetService.getAccountPaymentConfiguration(accountId);
        if (accountPaymentConfiguration.isPresent() && accountPaymentConfiguration.get().getExcludeFromPaymentRetries()) {
            LOGGER.info("Skipping payment retry for account {} as it is excluded from payment retries", accountId);
            return true;
        }
        return false;
    }

    private TenantJob dispatchPaymentRetryJob(
        DSLContext txnContext,
        Payment payment,
        PaymentAttempt paymentAttempt,
        int retryAttemptNumber,
        PaymentRetryPolicy policy
    ) {
        LOGGER.info(
            "[triggerPaymentAttemptFailureActions] Scheduling retry for last payment attempt {} with retry attempt number {} and policy total attempts {}",
            paymentAttempt.getId(),
            retryAttemptNumber,
            policy.getTotalAttempts()
        );

        NextRetryAttemptDetail nextRetryAttemptDetail = policy.getNextRetryAttempt(clock.instant(), retryAttemptNumber);

        String attemptName = nextRetryAttemptDetail.attemptName();
        Instant delayedUntil = billyConfiguration.isLocalOrCi()
            ? clock.instant().plusSeconds(5) // This is to be able to test the retries practically in local/ci environments
            : nextRetryAttemptDetail.attemptInstant();

        Map<String, String> metadata = Map.of(
            LAST_PAYMENT_ATTEMPT_ID_METADATA_KEY,
            paymentAttempt.getId().toString(),
            AUTOMATED_PAYMENT_RETRY_NUMBER_METADATA_KEY,
            String.valueOf(retryAttemptNumber),
            AUTOMATED_PAYMENT_RETRY_ATTEMPT_NAME,
            attemptName
        );

        // TODO : Attempt retry
        String partitionKey = TenantAccountPartitionKey.builder()
            .withTenantId(paymentAttempt.getTenantId())
            .withAccountId(payment.getCustomerAccountId())
            .build()
            .getKey();
        TenantJob retryJob = ImmutableTenantJob.builder()
            .module(TenantJobModule.PAYMENT)
            .jobType(TenantJobType.PAYMENT_RETRY)
            .objectModel(TenantJobObjectModel.PAYMENT)
            .objectId(payment.getPaymentId())
            .partitionKey(partitionKey)
            .delayedUntil(delayedUntil)
            .metadata(metadata)
            .build();
        TenantJob dispatchedJob = tenantJobDispatcherService.dispatchInTransaction(txnContext, retryJob);

        if (dispatchedJob == null) {
            String errorMessage = String.format("Failed to dispatch payment retry job for last payment attempt %s", paymentAttempt.getId());
            LOGGER.error(errorMessage);
            throw new ServiceFailureException(errorMessage);
        }

        if (
            dispatchedJob.getDelayedUntil().isEmpty() || Math.abs(Duration.between(dispatchedJob.getDelayedUntil().get(), delayedUntil).toHours()) > 1
        ) {
            // Nothing active to do here, we can try to check passively.
            // Given that our setup is retry after x days atm, we can take upto 1 day to investigate.
            LOGGER.warn(
                AUTOMATED_PAYMENT_RETRY,
                String.format(
                    "Dispatched job delayedUntil %s is not within 1 hour difference of expected delayedUntil %s for payment attempt %s",
                    dispatchedJob.getDelayedUntil().orElse(null),
                    delayedUntil,
                    paymentAttempt.getId()
                )
            );
        }

        return dispatchedJob;
    }

    private void handleFailedPaymentAttempt(
        DSLContext transactionContext,
        Payment paymentData,
        PaymentAttempt paymentAttempt,
        PaymentFailureReason failureReason,
        Instant failedOn
    ) {
        boolean paymentRetryScheduled = schedulePaymentRetryIfApplicable(transactionContext, paymentData, paymentAttempt);
        Payment payment = paymentGetService.getPaymentByPaymentIdInTransaction(transactionContext, paymentData.getPaymentId());
        if (paymentRetryScheduled) {
            LOGGER.info(
                "[handleFailedPaymentAttempt] Payment retry scheduled for last payment attempt {}, marking attempt as failed but ignoring side-effects",
                paymentAttempt.getId()
            );
            // We have to mark the payment attempt as failed even in case of retry, but we don't need to handle account payment method failure or send dunning
            // The advantage of doing this after job is scheduled is in case of a distributed failure we will have reconciliation job to handle the payment attempt later,
            // and for the retry job we will have a check that the last payment attempt is indeed marked as failed
            paymentStateProcessor.toFailedPaymentAttempt(transactionContext, paymentAttempt, failureReason);
            SettlementApplication settlementApplication = getActiveSettlementApplication(payment);
            String invoiceNumber = settlementApplication.getInvoiceNumber();
            sendPaymentAttemptFailedWebhook(
                transactionContext.configuration(),
                payment,
                paymentAttempt,
                invoiceNumber,
                settlementApplication,
                failureReason
            );
            return;
        }

        paymentStateProcessor.toFailedPaymentAttempt(transactionContext, paymentAttempt, failureReason);
        triggerPaymentAttemptFailedStateTransition(transactionContext, payment, paymentAttempt, failedOn, failureReason);
    }

    private void handleAccountPaymentMethodFailure(
        DSLContext transactionContext,
        Payment payment,
        Optional<AccountPaymentMethod> accountPaymentMethodToUpdate
    ) {
        String accountId = payment.getCustomerAccountId();
        Optional<AccountPaymentMethod> savedAccountPaymentMethod = accountPaymentMethodGetService.getPaymentMethod(
            accountId,
            payment.getPaymentMethodId()
        );

        savedAccountPaymentMethod.ifPresent(accountPaymentMethod -> {
            // We do this in Stripe flow to update dangling account payment methods as we
            // receive the payment method detail in intent response
            if (accountPaymentMethod.getStatus() == AccountPaymentMethodStatus.PENDING && accountPaymentMethodToUpdate.isPresent()) {
                AccountPaymentMethod accountPaymentMethodToUpdateValue = accountPaymentMethodToUpdate.get();
                LOGGER.info(
                    "[handleAccountPaymentMethodFailure] Updating account payment method {} for account {} with payment provider method id {} and status {}",
                    accountPaymentMethod.getId(),
                    accountId,
                    accountPaymentMethodToUpdateValue.getPaymentMethodId(),
                    accountPaymentMethodToUpdateValue.getStatus()
                );
                accountPaymentMethodService.updateAccountPaymentMethodById(
                    accountPaymentMethod.getId(),
                    accountPaymentMethodToUpdateValue,
                    transactionContext
                );
                return;
            }
            LOGGER.info(
                "[handleAccountPaymentMethodFailure] Disabling account payment method {} for account {} with status {}",
                accountPaymentMethod.getId(),
                accountId,
                accountPaymentMethod.getStatus()
            );

            // Otherwise, just disable the account payment method
            accountPaymentMethodService.updateAccountPaymentMethodStatus(
                accountPaymentMethod.getId(),
                accountPaymentMethod.getStatus(),
                AccountPaymentMethodStatus.DISABLED,
                transactionContext
            );
        });
    }

    // TODO : handle retry config here
    public void triggerPaymentAttemptFailedStateTransition(
        DSLContext transactionContext,
        Payment payment,
        PaymentAttempt paymentAttempt,
        Instant failedOn,
        PaymentFailureReason failureReason
    ) {
        LOGGER.info("[triggerPaymentAttemptFailedStateTransition] Triggering failed state transition for payment attempt {}", paymentAttempt);
        String paymentId = paymentAttempt.getPaymentId();
        SettlementApplication settlementApplication = invoiceSettlementService
            .getPendingSettlementApplicationByPaymentId(paymentId)
            .orElseThrow(() -> new ServiceFailureException("No pending settlement application found for payment " + paymentId));

        // Mark payment as failed
        payment.setState(PaymentState.FAILED);
        paymentService.updatePayment(payment, transactionContext);

        invoiceSettlementService.markSettlementApplicationAsFailed(settlementApplication, failedOn, transactionContext);

        // Update account payment method status
        AccountPaymentMethod accountPaymentMethod = accountPaymentMethodGetService.getAccountPaymentMethodByPayment(payment);
        switch (payment.getLifecycleType()) {
            case ONE_TIME -> accountPaymentMethodService.deleteAccountPaymentMethodById(accountPaymentMethod.getId(), transactionContext);
            case AUTOMATIC -> {
                LOGGER.info("Suspending account payment method {} for account {}", accountPaymentMethod.getId(), payment.getCustomerAccountId());
                accountPaymentMethod.setStatus(AccountPaymentMethodStatus.SUSPENDED);
                accountPaymentMethodService.updateAccountPaymentMethodById(accountPaymentMethod.getId(), accountPaymentMethod, transactionContext);
                sendPaymentRetriesExhaustedWebhook(
                    transactionContext.configuration(),
                    payment,
                    paymentAttempt,
                    settlementApplication.getInvoiceNumber(),
                    settlementApplication,
                    failureReason
                );
            }
            default -> {}
        }
    }

    private void tryAndAcquireInvoicePaymentLock(DSLContext transactionContext, String invoiceNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        String key = String.format(INVOICE_PAYMENT_LOCK_FORMAT, tenantId, invoiceNumber);
        Optional<Long> lock = PostgresAdvisoryLock.tryAcquireLock(transactionContext, key);
        if (lock.isEmpty()) {
            LOGGER.info("Invoice payment lock acquisition failed for invoice {}", invoiceNumber);
            throw new ConflictingStateException("A payment is already in progress for this invoice. Please try again later.");
        }
    }

    private PaymentAttempt initiatePaymentAttemptForAutomaticPayment(
        DSLContext transactionContext,
        Invoice invoice,
        AccountPaymentMethod accountPaymentMethod,
        InvoiceBalance invoiceBalance,
        PaymentProviderPaymentMethod paymentProviderPaymentMethod,
        PaymentAttemptType paymentAttemptType,
        PaymentRetryProcessingData paymentRetryProcessingData
    ) {
        Optional<PaymentRetryConfig> paymentRetryConfig = paymentConfigurationService.getLatestPaymentRetryConfig();
        PaymentMetadata paymentMetadata = buildPaymentMetadata(accountPaymentMethod, paymentRetryConfig);

        ImmutablePaymentAttemptMetadata.Builder paymentAttemptMetadataBuilder = ImmutablePaymentAttemptMetadata.builder();

        return switch (paymentAttemptType) {
            case SELF_SERVE, AUTOMATIC_FIRST_ATTEMPT -> {
                // TODO : move this out of StripeService later on
                // Create payment
                Payment payment = stripeService.createPayment(
                    invoice,
                    accountPaymentMethod.getId(),
                    invoiceBalance,
                    PaymentState.INITIATED,
                    null,
                    PaymentLifecycleType.AUTOMATIC,
                    paymentMetadata,
                    transactionContext
                );

                // Create payment attempt
                PaymentAttempt paymentAttempt = PaymentAttempt.builder()
                    .entityId(invoice.getEntityId())
                    .paymentId(payment.getPaymentId())
                    .state(PaymentAttemptState.CREATED)
                    .attemptedOn(Instant.now())
                    .attemptNumber(1)
                    .metadata(
                        paymentAttemptMetadataBuilder
                            .retryMetadata(new AttemptRetryMetadata(paymentAttemptType, "Initial automated attempt", Optional.empty()))
                            .build()
                    )
                    .build();
                PaymentAttempt savedPaymentAttempt = paymentService.addPaymentAttempt(paymentAttempt, transactionContext);

                // TODO : move this out of StripeService later on
                // Create settlement application
                stripeService.addPendingSettlementApplicationForPayment(
                    payment,
                    invoiceBalance.getBalance(),
                    invoice,
                    String.format(PAYMENT_SETTLEMENT_NOTE_FORMAT, paymentProviderPaymentMethod.getPaymentMethodId()),
                    transactionContext.configuration()
                );

                yield savedPaymentAttempt;
            }
            case MANUAL_RETRY_ATTEMPT, AUTOMATIC_RETRY_ATTEMPT -> {
                InvariantChecker.nonNullArg(paymentRetryProcessingData, "paymentRetryProcessingData");

                PaymentAttemptMetadata paymentAttemptMetadata = paymentAttemptMetadataBuilder
                    .retryMetadata(
                        new AttemptRetryMetadata(
                            paymentAttemptType,
                            paymentRetryProcessingData.attemptName,
                            Optional.of(paymentRetryProcessingData.automatedPaymentRetryNumber)
                        )
                    )
                    .build();

                List<PaymentAttempt> paymentsAttempts = paymentGetService.getPaymentAttemptsByPaymentId(paymentRetryProcessingData.paymentId);
                PaymentAttempt lastPaymentAttempt = paymentsAttempts.get(paymentsAttempts.size() - 1);
                if (lastPaymentAttempt.getId().compareTo(UUID.fromString(paymentRetryProcessingData.lastPaymentAttemptId)) != 0) {
                    throw new ServiceFailureException(
                        String.format(
                            "[initiatePaymentAttemptForAutomaticPayment] Last payment attempt %s doesn't match the failed payment attempt to process %s",
                            lastPaymentAttempt.getId(),
                            paymentRetryProcessingData.lastPaymentAttemptId
                        )
                    );
                }

                PaymentAttempt paymentAttempt = PaymentAttempt.builder()
                    .entityId(invoice.getEntityId())
                    .paymentId(paymentRetryProcessingData.paymentId)
                    .state(PaymentAttemptState.CREATED)
                    .attemptedOn(Instant.now())
                    .attemptNumber(lastPaymentAttempt.getAttemptNumber() + 1)
                    .metadata(paymentAttemptMetadata)
                    .build();
                yield paymentService.addPaymentAttempt(paymentAttempt, transactionContext);
            }
        };
    }

    private PaymentMetadata buildPaymentMetadata(AccountPaymentMethod accountPaymentMethod, Optional<PaymentRetryConfig> paymentRetryConfig) {
        if (paymentRetryConfig.isEmpty()) {
            return null;
        }

        UUID paymentRetryConfigId = InvariantChecker.nonNullArg(paymentRetryConfig.get().getId().orElse(null), "paymentRetryConfigId");
        return ImmutablePaymentMetadata.builder()
            .retryMetadata(
                new PaymentRetryMetadata(paymentRetryConfigId, UUID.fromString(accountPaymentMethod.getPaymentMethodId()), Optional.empty())
            ) // this is populated when retry job is scheduled
            .build();
    }

    private void updateNextAttemptDetailInPaymentMetadata(
        DSLContext transactionContext,
        PaymentMetadata paymentMetadata,
        PaymentRetryMetadata paymentRetryMetadata,
        Payment payment,
        PaymentAttempt paymentAttempt,
        Optional<PaymentMetadata.NextAutomatedRetryDetail> nextRetryAttemptDetail
    ) {
        PaymentRetryMetadata updatedRetryMetadata = paymentRetryMetadata.updateNextAutomatedRetryDetail(nextRetryAttemptDetail);
        paymentService.updatePaymentMetadata(
            transactionContext,
            paymentAttempt.getTenantId(),
            payment.getPaymentId(),
            ImmutablePaymentMetadata.builder().from(paymentMetadata).retryMetadata(updatedRetryMetadata).build()
        );
    }

    private void sendAutoPayFailedDunning(Invoice.Number invoiceNumber, Payment payment) {
        PaymentProviderPaymentMethod paymentProviderPaymentMethod = getPaymentProviderPaymentMethodByPayment(payment);
        invoiceDunningService.sendInvoiceReminderEmail(
            invoiceNumber,
            Optional.of(DunningReminderType.AUTO_PAYMENT_FAILED),
            Optional.of(paymentProviderPaymentMethod)
        );
    }

    private PaymentProviderPaymentMethod getPaymentProviderPaymentMethodByPayment(Payment payment) {
        AccountPaymentMethod accountPaymentMethod = accountPaymentMethodGetService.getAccountPaymentMethodByPayment(payment);

        Optional<PaymentProviderPaymentMethod> paymentProviderPaymentMethod = paymentProviderService.getPaymentProviderPaymentMethodById(
            UUID.fromString(accountPaymentMethod.getPaymentMethodId()),
            true
        );

        return paymentProviderPaymentMethod.orElseThrow(() ->
            new ServiceFailureException("Unable to get a provider payment method with id: " + payment.getPaymentMethodId())
        );
    }

    /**
     * There are 2 valid cases to think about here, where external action could have happened in between the time of scheduling and execution of the retry job -
     * 1. Manual payment application - In that case we should have failed this pending payment attempt and settlement application already while payment application.
     * 2. Manual retry for automatic payment - A new payment attempt would have been made so in that case the lastPaymentAttemptId here should not be used as trigger for retry.
     */
    private Optional<PaymentRetryResult> validatePaymentStateForRetry(
        SettlementApplication settlementApplication,
        Payment payment,
        String paymentId,
        String lastPaymentAttemptId
    ) {
        PaymentState paymentState = payment.getState();

        if (paymentState.isTerminal()) {
            LOGGER.info("[attemptRetry] Payment {} is in terminal state {}, cannot retry", paymentId, paymentState);
            return Optional.of(
                new PaymentRetryResult(SKIPPED, String.format("Payment %s is in terminal state %s, cannot retry", paymentId, paymentState))
            );
        }

        if (settlementApplication.getStatus() != SettlementApplicationStatus.ATTEMPTING_PAYMENT_COLLECTION) {
            String message = String.format(
                "[attemptRetry] Settlement application for payment %s is in state %s, cannot retry",
                paymentId,
                settlementApplication.getStatus()
            );
            LOGGER.info(message);
            return Optional.of(new PaymentRetryResult(SKIPPED, message));
        }

        return validatePaymentAttemptStateForRetry(paymentId, lastPaymentAttemptId);
    }

    private Optional<PaymentRetryResult> validatePaymentAttemptStateForRetry(String paymentId, String lastPaymentAttemptId) {
        List<PaymentAttempt> paymentAttempts = paymentGetService.getPaymentAttemptsByPaymentId(paymentId);

        int paymentAttemptsCount = paymentAttempts.size();
        PaymentAttempt lastPaymentAttemptFromDb = paymentAttempts.get(paymentAttemptsCount - 1);

        // This should only happen if a manual retry was attempted in between
        if (lastPaymentAttemptFromDb.getId().compareTo(UUID.fromString(lastPaymentAttemptId)) != 0) {
            String message = String.format(
                "[attemptRetry] Last payment attempt %s doesn't match the failed payment attempt to process %s, skipping retry",
                lastPaymentAttemptFromDb.getId(),
                lastPaymentAttemptId
            );
            LOGGER.info(message);

            PaymentAttemptMetadata lastPaymentAttemptMetadata = InvariantChecker.nonNullArg(
                lastPaymentAttemptFromDb.getMetadata(),
                "paymentAttemptMetadata"
            );
            // NOTE : we are already attempting a retry here so none of this should be null
            AttemptRetryMetadata attemptRetryMetadata = lastPaymentAttemptMetadata
                .getRetryMetadata()
                .orElseThrow(() ->
                    new ServiceFailureException(
                        String.format(
                            "[attemptRetry] Retry metadata is not present in payment attempt metadata for payment attempt %s",
                            lastPaymentAttemptFromDb.getId()
                        )
                    )
                );

            PaymentAttemptType lastPaymentAttemptType = attemptRetryMetadata.type();
            if (lastPaymentAttemptType != PaymentAttemptType.MANUAL_RETRY_ATTEMPT) {
                throw new ServiceFailureException(
                    String.format(
                        "[attemptRetry] Last payment attempt from db %s is expected to be a manual retry attempt but instead is %s",
                        lastPaymentAttemptFromDb.getId(),
                        lastPaymentAttemptType
                    )
                );
            }
            return Optional.of(new PaymentRetryResult(SKIPPED, message));
        }

        if (lastPaymentAttemptFromDb.getState() != PaymentAttemptState.FAILED) {
            throw new ServiceFailureException(
                String.format(
                    "[attemptRetry] Last payment attempt %s for payment %s is not in FAILED state, cannot retry",
                    lastPaymentAttemptId,
                    paymentId
                )
            );
        }

        return Optional.empty();
    }

    private Pair<String, PaymentMethodData> validatePaymentMethodStateForRetry(String tenantId, Payment payment, String customerAccountId) {
        String messageFormat = "[attemptRetry] No %s found for account : " + customerAccountId + ", cannot retry payment : " + payment.getPaymentId();

        // NOTE : we should fetch the account payment method attached to the payment, as it might have been replaced
        Optional<AccountPaymentMethod> accountPaymentMethod = accountPaymentMethodGetService.getPaymentMethod(
            customerAccountId,
            payment.getPaymentMethodId()
        );
        if (accountPaymentMethod.isEmpty() || accountPaymentMethod.get().getStatus() != AccountPaymentMethodStatus.ACTIVE) {
            return Pair.of(messageFormat.formatted("active automatic payment method"), null);
        }

        Optional<PaymentProviderPaymentMethod> paymentProviderPaymentMethod = paymentProviderService.getPaymentProviderPaymentMethodById(
            UUID.fromString(accountPaymentMethod.get().getPaymentMethodId()),
            false
        );
        if (paymentProviderPaymentMethod.isEmpty()) {
            return Pair.of(messageFormat.formatted("payment provider payment method"), null);
        }

        Optional<AccountAutomaticPaymentMethod> enrolledMethod = accountAutomaticPaymentMethodGetService.getEnrolledMethodForAccount(
            customerAccountId
        );
        if (enrolledMethod.isEmpty()) {
            return Pair.of(messageFormat.formatted("automatic payment method enrollment"), null);
        }
        if (enrolledMethod.get().getAccountPaymentMethodId().compareTo(payment.getPaymentMethodId()) != 0) {
            return Pair.of(
                messageFormat.formatted(
                    "automatic payment method enrollment id : " +
                    enrolledMethod.get().getAccountPaymentMethodId() +
                    " match with payment method id : " +
                    payment.getPaymentMethodId()
                ),
                null
            );
        }

        // Stripe integration could have been deleted
        UUID integrationId = paymentProviderPaymentMethod.get().getIntegrationId();
        Optional<PaymentStripeConnectIntegration> integrationOptional = paymentIntegrationGetService.getStripeConnectIntegrationByIntegrationId(
            tenantId,
            integrationId
        );
        if (integrationOptional.isEmpty()) {
            return Pair.of(messageFormat.formatted("payment integration with id : " + integrationId + " for payment provider payment method"), null);
        }

        // NOTE : important to fetch the customer by original Stripe integration id, as it might have been replaced
        Optional<PaymentProviderCustomer> paymentProviderCustomer = paymentProviderService.getPaymentProviderCustomerByAccountAndIntegrationId(
            tenantId,
            customerAccountId,
            integrationId
        );
        if (paymentProviderCustomer.isEmpty()) {
            return Pair.of(messageFormat.formatted("payment provider customer"), null);
        }

        stripeService.checkAndAttachPaymentMethodToCustomer(
            paymentProviderPaymentMethod.get().getPaymentMethodId(),
            paymentProviderCustomer.get().getCustomerId()
        );

        return Pair.of(null, new PaymentMethodData(accountPaymentMethod.get(), paymentProviderCustomer.get(), paymentProviderPaymentMethod.get()));
    }

    private record PaymentMethodData(
        AccountPaymentMethod accountPaymentMethod,
        PaymentProviderCustomer paymentProviderCustomer,
        PaymentProviderPaymentMethod paymentProviderPaymentMethod
    ) {}

    private record PaymentAttemptAndInvoiceBalance(Optional<PaymentAttempt> paymentAttempt, InvoiceBalance invoiceBalance) {}

    public record PaymentRetryProcessingData(String paymentId, String lastPaymentAttemptId, int automatedPaymentRetryNumber, String attemptName) {}

    public enum PaymentAttemptType {
        SELF_SERVE,
        AUTOMATIC_FIRST_ATTEMPT,
        AUTOMATIC_RETRY_ATTEMPT,
        MANUAL_RETRY_ATTEMPT,
    }
}
