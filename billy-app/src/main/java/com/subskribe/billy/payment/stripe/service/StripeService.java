package com.subskribe.billy.payment.stripe.service;

import com.stripe.exception.ApiConnectionException;
import com.stripe.exception.ApiException;
import com.stripe.exception.StripeException;
import com.stripe.model.Account;
import com.stripe.model.Customer;
import com.stripe.model.PaymentIntent;
import com.stripe.model.PaymentMethod;
import com.stripe.model.SetupIntent;
import com.stripe.model.StripeError;
import com.stripe.model.oauth.TokenResponse;
import com.stripe.net.OAuth;
import com.stripe.net.RequestOptions;
import com.stripe.param.CustomerCreateParams;
import com.stripe.param.PaymentIntentCancelParams;
import com.stripe.param.PaymentIntentConfirmParams;
import com.stripe.param.PaymentIntentCreateParams;
import com.stripe.param.PaymentIntentUpdateParams;
import com.stripe.param.PaymentMethodAttachParams;
import com.stripe.param.SetupIntentCreateParams;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.account.model.AccountPaymentMethodStatus;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodService;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.aws.secretsmanager.model.SecretType;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoicedunning.model.DunningReminderType;
import com.subskribe.billy.invoicedunning.services.InvoiceDunningService;
import com.subskribe.billy.invoicesettlement.model.InvoiceBalance;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.payment.SupportedPaymentProvider;
import com.subskribe.billy.payment.integration.db.PaymentIntegrationDAO;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.model.AccountPaymentManagementLink;
import com.subskribe.billy.payment.model.FailedPaymentAttemptData;
import com.subskribe.billy.payment.model.ImmutableFailedPaymentAttemptData;
import com.subskribe.billy.payment.model.InvoicePaymentManagementLink;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.model.PaymentAttempt;
import com.subskribe.billy.payment.model.PaymentAttemptState;
import com.subskribe.billy.payment.model.PaymentFailureReason;
import com.subskribe.billy.payment.model.PaymentLifecycleType;
import com.subskribe.billy.payment.model.PaymentMetadata;
import com.subskribe.billy.payment.model.PaymentMethodType;
import com.subskribe.billy.payment.model.PaymentProviderCustomer;
import com.subskribe.billy.payment.model.PaymentProviderPaymentMethod;
import com.subskribe.billy.payment.model.PaymentRetryExceptionModels.PaymentIntentFailedException;
import com.subskribe.billy.payment.model.PaymentRetryExceptionModels.PaymentIntentMissingException;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodService;
import com.subskribe.billy.payment.services.AccountPaymentManagementLinkService;
import com.subskribe.billy.payment.services.InvoicePaymentManagementLinkService;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.payment.services.PaymentProviderService;
import com.subskribe.billy.payment.services.PaymentService;
import com.subskribe.billy.payment.services.PaymentStateProcessor;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.payment.stripe.model.StripeErrorType;
import com.subskribe.billy.payment.stripe.model.StripeIntentResponse;
import com.subskribe.billy.payment.stripe.model.StripeSetupIntentResponse;
import com.subskribe.billy.postgres.PostgresAdvisoryLock;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.PaymentState;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.ConcurrentModificationException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import javax.inject.Inject;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

public class StripeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StripeService.class);

    private static final String STRIPE_METADATA_PAYMENT_LINK_ID_KEY = "secret_payment_link_id";

    public static final String STRIPE_METADATA_TENANT_ID_KEY = "tid";

    private static final String STRIPE_METADATA_INVOICE_NUMBER_KEY = "inv";

    private static final String STRIPE_METADATA_INVOICE_BALANCE_KEY = "bal";

    private static final String STRIPE_METADATA_PAYMENT_METHOD_ID = "pm";

    private static final String STRIPE_METADATA_ACCOUNT_ID = "acc";

    private static final String STRIPE_METADATA_AUTOMATIC_PAYMENT_OPT_IN = "auto_opt_in";

    public static final String STRIPE_METADATA_ENV_NAME = "sbsk_env";

    public static final String STRIPE_CALL_FAILED_ERROR_MESSAGE = "Call to Stripe failed";

    public static final String STRIPE_PAYMENT_INTENT_SUCCEEDED_STATUS = "succeeded";

    public static final String STRIPE_PAYMENT_INTENT_FAILED_STATUS = "failed";

    public static final String STRIPE_PAYMENT_INTENT_CANCELED_STATUS = "canceled";

    public static final String STRIPE_PAYMENT_INTENT_PROCESSING_STATUS = "processing";

    private static final String STRIPE_PAYMENT_INTENT_REQUIRES_CONFIRMATION_STATUS = "requires_confirmation";

    private static final String STRIPE_PAYMENT_INTENT_REQUIRES_PAYMENT_METHOD_STATUS = "requires_payment_method";

    private static final String STRIPE_PAYMENT_INTENT_REQUIRES_ACTION_METHOD_STATUS = "requires_action";

    public static final String PAYMENT_METHOD_NAME_FORMAT = "Payment method with id: %s";

    private static final String ISO_COUNTRY_CODE_US = "US";

    private static final String ISO_CURRENCY_CODE_US = "usd";

    public static final String CARD_DECLINED_ERROR_CODE = "card_declined";

    private static final String STRIPE_PAYMENT_LOCK_FORMAT = "%s/%s/stripe_payment";

    private final SecretsService secretsService;

    private final InvoiceSettlementService invoiceSettlementService;

    private final PaymentService paymentService;

    private final PaymentIntegrationGetService paymentIntegrationGetService;

    private final TenantIdProvider tenantIdProvider;

    private final InvoiceRetrievalService invoiceRetrievalService;

    private final AccountPaymentMethodService accountPaymentMethodService;

    private final AccountGetService accountGetService;

    private final PaymentIntegrationDAO paymentIntegrationDAO;

    private final PaymentProviderService paymentProviderService;

    private final PaymentGetService paymentGetService;

    private final InvoicePaymentManagementLinkService invoicePaymentManagementLinkService;

    private final AccountPaymentManagementLinkService accountPaymentManagementLinkService;

    private final DSLContextProvider dslContextProvider;

    private final AccountPaymentMethodGetService accountPaymentMethodGetService;

    private final AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService;

    private final InvoiceDunningService invoiceDunningService;

    private final PaymentStateProcessor paymentStateProcessor;

    private final BillyConfiguration billyConfiguration;

    @Inject
    public StripeService(
        SecretsService secretsService,
        InvoiceSettlementService invoiceSettlementService,
        PaymentService paymentService,
        PaymentIntegrationGetService paymentIntegrationGetService,
        TenantIdProvider tenantIdProvider,
        InvoiceRetrievalService invoiceRetrievalService,
        AccountPaymentMethodService accountPaymentMethodService,
        AccountGetService accountGetService,
        PaymentIntegrationDAO paymentIntegrationDAO,
        PaymentProviderService paymentProviderService,
        PaymentGetService paymentGetService,
        InvoicePaymentManagementLinkService invoicePaymentManagementLinkService,
        AccountPaymentManagementLinkService accountPaymentManagementLinkService,
        DSLContextProvider dslContextProvider,
        AccountPaymentMethodGetService accountPaymentMethodGetService,
        AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService,
        InvoiceDunningService invoiceDunningService,
        PaymentStateProcessor paymentStateProcessor,
        BillyConfiguration billyConfiguration
    ) {
        this.secretsService = secretsService;
        this.invoiceSettlementService = invoiceSettlementService;
        this.paymentService = paymentService;
        this.paymentIntegrationGetService = paymentIntegrationGetService;
        this.tenantIdProvider = tenantIdProvider;
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.accountPaymentMethodService = accountPaymentMethodService;
        this.accountGetService = accountGetService;
        this.paymentIntegrationDAO = paymentIntegrationDAO;
        this.paymentProviderService = paymentProviderService;
        this.paymentGetService = paymentGetService;
        this.invoicePaymentManagementLinkService = invoicePaymentManagementLinkService;
        this.accountPaymentManagementLinkService = accountPaymentManagementLinkService;
        this.dslContextProvider = dslContextProvider;
        this.accountPaymentMethodGetService = accountPaymentMethodGetService;
        this.accountAutomaticPaymentMethodService = accountAutomaticPaymentMethodService;
        this.invoiceDunningService = invoiceDunningService;
        this.paymentStateProcessor = paymentStateProcessor;
        this.billyConfiguration = billyConfiguration;
    }

    private String getStripeApiKey() {
        return secretsService.getStripeSecret(SecretType.STRIPE_API_KEY, true);
    }

    private Customer createCustomer(com.subskribe.billy.account.model.Account account) {
        RequestOptions requestOptions = getRequestOptions(account.getId().toString());
        CustomerCreateParams customerCreateParams = CustomerCreateParams.builder().setName(account.getName()).build();

        return callInStripeExceptionContext(() -> Customer.create(customerCreateParams, requestOptions));
    }

    private SetupIntent createSetupIntent(String paymentLinkId, PaymentProviderCustomer customer, com.subskribe.billy.account.model.Account account) {
        RequestOptions requestOptions = getRequestOptions();

        var builder = SetupIntentCreateParams.builder()
            .setCustomer(customer.getCustomerId())
            .putMetadata(STRIPE_METADATA_PAYMENT_LINK_ID_KEY, paymentLinkId)
            .putMetadata(STRIPE_METADATA_ACCOUNT_ID, account.getAccountId())
            .putMetadata(STRIPE_METADATA_ENV_NAME, billyConfiguration.getEnvName())
            .putMetadata(STRIPE_METADATA_TENANT_ID_KEY, tenantIdProvider.provideTenantIdString());

        if (account.getSupportedPaymentTypes().contains(PaymentType.CARD)) {
            builder = builder.addPaymentMethodType(PaymentMethodType.CARD.getStripeName());
        }

        PaymentStripeConnectIntegration stripeConnectIntegration = paymentIntegrationGetService
            .getCompletedStripeConnectIntegration()
            .orElseThrow(() ->
                new ObjectNotFoundException(BillyObjectType.PAYMENT_STRIPE_CONNECT_INTEGRATION, tenantIdProvider.provideTenantIdString())
            );
        String connectAccountId = stripeConnectIntegration.getConnectAccountId();
        if (shouldAddAchPaymentMethodType(account, connectAccountId)) {
            builder.addPaymentMethodType(PaymentMethodType.ACH.getStripeName());
        }

        // This is needed to allow future usages - https://docs.stripe.com/api/setup_intents/create#create_setup_intent-usage
        builder.setUsage(SetupIntentCreateParams.Usage.OFF_SESSION);

        SetupIntentCreateParams setupIntentCreateParams = builder.build();
        return callInStripeExceptionContext(() -> SetupIntent.create(setupIntentCreateParams, requestOptions));
    }

    private boolean shouldAddAchPaymentMethodType(com.subskribe.billy.account.model.Account account, String connectAccountId) {
        if (
            account.getSupportedPaymentTypes().contains(PaymentType.ACH) &&
            ISO_CURRENCY_CODE_US.equalsIgnoreCase(account.getCurrency().getCurrencyCode())
        ) {
            Account connectAccountDetails = getConnectAccount(connectAccountId);
            return ISO_COUNTRY_CODE_US.equalsIgnoreCase(connectAccountDetails.getCountry());
        }

        return false;
    }

    private Account getConnectAccount(String connectAccountId) {
        RequestOptions requestOptions = buildRequestOptions(getStripeApiKey(), connectAccountId);
        return callInStripeExceptionContext(() -> Account.retrieve(connectAccountId, requestOptions));
    }

    private static RequestOptions buildRequestOptions(String apiKey) {
        return RequestOptions.builder().setApiKey(apiKey).build();
    }

    private static RequestOptions buildRequestOptions(String apiKey, String connectAccountId) {
        return buildRequestOptions(apiKey, connectAccountId, null);
    }

    private static RequestOptions buildRequestOptions(String apiKey, String connectAccountId, String idempotencyKey) {
        return RequestOptions.builder().setApiKey(apiKey).setStripeAccount(connectAccountId).setIdempotencyKey(idempotencyKey).build();
    }

    private static <T> T callInStripeExceptionContext(Callable<T> callable) {
        try {
            return callable.call();
        } catch (StripeException e) {
            LOGGER.error(STRIPE_CALL_FAILED_ERROR_MESSAGE, e);
            throw new ServiceFailureException(STRIPE_CALL_FAILED_ERROR_MESSAGE);
        } catch (Exception e) {
            throw new ServiceFailureException(e.toString());
        }
    }

    public Payment createPayment(
        Invoice invoice,
        UUID accountPaymentMethodId,
        InvoiceBalance invoiceBalance,
        PaymentState paymentState,
        String transactionId,
        PaymentLifecycleType paymentLifecycleType,
        PaymentMetadata paymentMetadata,
        DSLContext transactionContext
    ) {
        Payment payment = new Payment();
        payment.setEntityId(invoice.getEntityId());
        payment.setCustomerAccountId(invoice.getCustomerAccountId());
        payment.setPaymentMethodId(accountPaymentMethodId);
        payment.setAmount(invoiceBalance.getBalance());
        payment.setCurrencyCode(invoiceBalance.getCurrencyCode());
        payment.setState(paymentState);
        payment.setTransactionId(transactionId);
        payment.setLifecycleType(paymentLifecycleType);
        payment.setMetadata(paymentMetadata);
        return paymentService.addPayment(payment, transactionContext);
    }

    public PaymentIntent createPaymentIntentForAutomaticPayment(
        PaymentAttempt paymentAttempt,
        PaymentProviderCustomer paymentProviderCustomer,
        PaymentProviderPaymentMethod paymentProviderPaymentMethod,
        Invoice.Number invoiceNumber,
        BigDecimal amount,
        String currencyCode,
        RequestOptions requestOptions
    ) throws PaymentIntentFailedException, PaymentIntentMissingException {
        try {
            long stripeAmount = SupportedCurrency.convertAmountToSmallestUnit(amount, currencyCode);
            SupportedCurrency.validateAmountForStripe(stripeAmount, currencyCode);
            var builder = PaymentIntentCreateParams.builder()
                .setCurrency(currencyCode.toLowerCase())
                .setAmount(stripeAmount)
                .setCustomer(paymentProviderCustomer.getCustomerId())
                .setPaymentMethod(paymentProviderPaymentMethod.getPaymentMethodId())
                .addPaymentMethodType(paymentProviderPaymentMethod.getPaymentMethodType().getStripeName())
                .setConfirm(false)
                .putMetadata(STRIPE_METADATA_TENANT_ID_KEY, paymentProviderCustomer.getTenantId())
                .putMetadata(STRIPE_METADATA_INVOICE_NUMBER_KEY, invoiceNumber.getNumber())
                .putMetadata(STRIPE_METADATA_INVOICE_BALANCE_KEY, amount.toPlainString())
                .putMetadata(STRIPE_METADATA_ENV_NAME, billyConfiguration.getEnvName())
                .putMetadata(STRIPE_METADATA_PAYMENT_METHOD_ID, paymentProviderPaymentMethod.getPaymentMethodId());
            var params = builder.build();

            return PaymentIntent.create(params, requestOptions);
        } catch (StripeException e) {
            handleStripeError(e.getStripeError(), paymentAttempt);
            throw new ServiceFailureException("Error creating payment intent for " + invoiceNumber.getNumber());
        }
    }

    public SettlementApplication addPendingSettlementApplicationForPayment(
        Payment payment,
        BigDecimal amount,
        Invoice invoice,
        String note,
        Configuration configuration
    ) {
        return invoiceSettlementService.addSettlementApplicationForAttemptedPayment(
            invoice.getCustomerAccountId(),
            invoice.getInvoiceNumber().getNumber(),
            payment,
            amount,
            note,
            configuration
        );
    }

    PaymentProviderCustomer addNewOrGetExistingCustomer(String tenantId, String accountId) {
        Optional<PaymentProviderCustomer> customerOptional = paymentProviderService.getPaymentProviderCustomerByAccountId(tenantId, accountId);
        return customerOptional.orElseGet(() -> addNewStripeCustomer(accountId));
    }

    // TODO: Probably add some customer metadata
    private PaymentProviderCustomer addNewStripeCustomer(String accountId) {
        var account = accountGetService.getAccount(accountId);
        Customer customer = createCustomer(account);
        String customerId = customer.getId();
        PaymentProviderCustomer paymentProviderCustomer = new PaymentProviderCustomer(customerId, SupportedPaymentProvider.STRIPE, accountId);
        return paymentProviderService.addPaymentProviderCustomer(paymentProviderCustomer);
    }

    public StripeIntentResponse createPaymentIntentWithPaymentLink(String paymentLinkId) {
        InvoicePaymentManagementLink paymentLink = invoicePaymentManagementLinkService.getLinkById(paymentLinkId);
        String tenantId = paymentLink.getTenantId();

        PaymentStripeConnectIntegration stripeConnectIntegration = paymentIntegrationGetService
            .getCompletedStripeConnectIntegration()
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PAYMENT_STRIPE_CONNECT_INTEGRATION, tenantId));
        String connectAccountId = stripeConnectIntegration.getConnectAccountId();

        // Validate invoice
        Invoice.Number invoiceNumber = new Invoice.Number(paymentLink.getInvoiceNumber());
        Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);
        InvoiceBalance invoiceBalance = invoiceSettlementService.getInvoiceBalance(invoiceNumber);
        validateInvoiceAndInvoiceBalance(invoice, invoiceBalance);

        // Create Stripe customer
        String accountId = invoice.getCustomerAccountId();
        PaymentProviderCustomer stripeCustomer = addNewOrGetExistingCustomer(tenantId, accountId);

        // Build payment intent
        com.subskribe.billy.account.model.Account account = accountGetService.getAccount(accountId);
        RequestOptions requestOptions = getRequestOptions();
        PaymentIntent stripePaymentIntent = createPaymentIntentForOneTimePayment(
            invoiceBalance,
            paymentLink,
            stripeCustomer,
            account,
            connectAccountId,
            requestOptions
        );

        StripeIntentResponse response = new StripeIntentResponse();
        response.setPaymentIntentId(stripePaymentIntent.getId());
        response.setClientSecret(stripePaymentIntent.getClientSecret());
        return response;
    }

    public StripeSetupIntentResponse createSetupIntentWithPaymentLink(String paymentLinkId) {
        AccountPaymentManagementLink paymentLink = accountPaymentManagementLinkService.getLinkById(paymentLinkId);
        String tenantId = paymentLink.getTenantId();

        // Create Stripe customer
        PaymentProviderCustomer stripeCustomer = addNewOrGetExistingCustomer(tenantId, paymentLink.getAccountId());

        // Build payment intent
        com.subskribe.billy.account.model.Account account = accountGetService.getAccount(paymentLink.getAccountId());
        SetupIntent setupIntent = createSetupIntent(paymentLink.getLinkId(), stripeCustomer, account);

        StripeSetupIntentResponse response = new StripeSetupIntentResponse();
        response.setSetupIntentId(setupIntent.getId());
        response.setClientSecret(setupIntent.getClientSecret());
        return response;
    }

    private PaymentIntent createPaymentIntentForOneTimePayment(
        InvoiceBalance invoiceBalance,
        InvoicePaymentManagementLink invoicePaymentLink,
        PaymentProviderCustomer paymentProviderCustomer,
        com.subskribe.billy.account.model.Account account,
        String connectAccountId,
        RequestOptions requestOptions
    ) {
        long stripeAmount = SupportedCurrency.convertAmountToSmallestUnit(invoiceBalance.getBalance(), invoiceBalance.getCurrencyCode());
        SupportedCurrency.validateAmountForStripe(stripeAmount, invoiceBalance.getCurrencyCode());
        var builder = PaymentIntentCreateParams.builder()
            .setCurrency(invoiceBalance.getCurrencyCode().toLowerCase())
            .setAmount(stripeAmount)
            .setConfirm(false)
            .setCustomer(paymentProviderCustomer.getCustomerId())
            .putMetadata(STRIPE_METADATA_ENV_NAME, billyConfiguration.getEnvName())
            .putMetadata(STRIPE_METADATA_TENANT_ID_KEY, paymentProviderCustomer.getTenantId())
            .putMetadata(STRIPE_METADATA_INVOICE_NUMBER_KEY, invoicePaymentLink.getInvoiceNumber())
            .putMetadata(STRIPE_METADATA_INVOICE_BALANCE_KEY, invoiceBalance.getBalance().toPlainString())
            .putMetadata(STRIPE_METADATA_PAYMENT_LINK_ID_KEY, invoicePaymentLink.getLinkId());

        Set<PaymentType> supportedPaymentTypes = account.getSupportedPaymentTypes();
        List<String> stripePaymentMethods = new ArrayList<>();
        if (supportedPaymentTypes.contains(PaymentType.CARD)) {
            stripePaymentMethods.add(PaymentMethodType.CARD.getStripeName());
        }

        if (shouldAddAchPaymentMethodType(account, connectAccountId) && ISO_CURRENCY_CODE_US.equalsIgnoreCase(invoiceBalance.getCurrencyCode())) {
            stripePaymentMethods.add(PaymentMethodType.ACH.getStripeName());
        }

        if (stripePaymentMethods.isEmpty()) {
            throw new ConflictingStateException("No supported payment methods for account " + account.getAccountId());
        }

        stripePaymentMethods.forEach(builder::addPaymentMethodType);

        PaymentIntentCreateParams params = builder.build();
        return callInStripeExceptionContext(() -> PaymentIntent.create(params, requestOptions));
    }

    // Note: The entry point is from an unauthenticated payment endpoint
    public void updatePaymentIntentWithPaymentLink(
        InvoicePaymentManagementLink paymentLink,
        String stripePaymentIntentId,
        boolean optInForAutomaticPayments
    ) {
        // Validate invoice
        Invoice.Number invoiceNumber = new Invoice.Number(paymentLink.getInvoiceNumber());
        Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);
        InvoiceBalance invoiceBalance = invoiceSettlementService.getInvoiceBalance(invoiceNumber);
        validateInvoiceAndInvoiceBalance(invoice, invoiceBalance);

        boolean ongoingPayment = invoiceSettlementService.checkIfOngoingPaymentExistsForInvoice(invoice.getInvoiceNumber());
        if (ongoingPayment) {
            throw new ConflictingStateException(
                String.format("There is an ongoing payment for invoice %s. Please try again later.", invoice.getInvoiceNumber().getNumber())
            );
        }

        RequestOptions requestOptions = getRequestOptions();
        PaymentIntent stripePaymentIntent = callInStripeExceptionContext(() -> PaymentIntent.retrieve(stripePaymentIntentId, requestOptions));

        // Check invoice balance has not changed
        BigDecimal stripeAmount = SupportedCurrency.convertFromSmallestUnitToAmount(
            stripePaymentIntent.getAmount(),
            invoiceBalance.getCurrencyCode()
        );
        if (!Numbers.equals(stripeAmount, invoiceBalance.getBalance())) {
            throw new ConcurrentModificationException("Invoice balance has changed recently. Please try again.");
        }

        if (optInForAutomaticPayments) {
            PaymentIntentUpdateParams parameters = PaymentIntentUpdateParams.builder()
                .setSetupFutureUsage(PaymentIntentUpdateParams.SetupFutureUsage.OFF_SESSION) // https://docs.stripe.com/api/payment_intents/create#create_payment_intent-setup_future_usage
                .putMetadata(STRIPE_METADATA_ENV_NAME, billyConfiguration.getEnvName())
                .putMetadata(STRIPE_METADATA_AUTOMATIC_PAYMENT_OPT_IN, BooleanUtils.TRUE)
                .build();
            callInStripeExceptionContext(() -> stripePaymentIntent.update(parameters, requestOptions));
        }

        initiatePaymentAttemptForOneTimePayment(invoice, invoiceBalance, stripePaymentIntent);
    }

    private void initiatePaymentAttemptForOneTimePayment(Invoice invoice, InvoiceBalance invoiceBalance, PaymentIntent paymentIntent) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext.transaction(configuration -> {
            DSLContext transactionContext = DSL.using(configuration);

            tryAndAcquireStripePaymentLock(transactionContext, invoice.getInvoiceNumber().getNumber());

            String accountId = invoice.getCustomerAccountId();
            String stripePaymentIntentId = paymentIntent.getId();

            // Create account payment method
            AccountPaymentMethod accountPaymentMethod = new AccountPaymentMethod();
            accountPaymentMethod.setAccountId(accountId);
            // TODO : WHY payment intent id?
            String paymentMethodName = String.format(PAYMENT_METHOD_NAME_FORMAT, stripePaymentIntentId);
            accountPaymentMethod.setName(paymentMethodName);
            accountPaymentMethod.setStatus(AccountPaymentMethodStatus.PENDING);
            UUID accountPaymentMethodId = accountPaymentMethodService.addAccountPaymentMethod(accountPaymentMethod, transactionContext).getId();

            // Create payment
            Payment payment = createPayment(
                invoice,
                accountPaymentMethodId,
                invoiceBalance,
                PaymentState.INITIATED,
                null,
                PaymentLifecycleType.ONE_TIME,
                null,
                transactionContext
            );

            // Create payment attempt
            PaymentAttempt paymentAttempt = PaymentAttempt.builder()
                .entityId(invoice.getEntityId())
                .paymentId(payment.getPaymentId())
                .state(PaymentAttemptState.CONFIRMED)
                .transactionId(stripePaymentIntentId)
                .attemptedOn(Instant.now())
                .attemptNumber(1)
                .build();
            paymentService.addPaymentAttempt(paymentAttempt, transactionContext);

            // Create settlement application
            addPendingSettlementApplicationForPayment(payment, invoiceBalance.getBalance(), invoice, StringUtils.EMPTY, configuration);
        });
    }

    public PaymentMethod retrieveStripePaymentMethod(String stripePaymentMethodId) {
        RequestOptions requestOptions = getRequestOptions();
        return callInStripeExceptionContext(() -> PaymentMethod.retrieve(stripePaymentMethodId, requestOptions));
    }

    public void checkAndAttachPaymentMethodToCustomer(String paymentMethodId, String stripeCustomerId) {
        PaymentMethod paymentMethod = retrieveStripePaymentMethod(paymentMethodId);

        // Check if the payment method is already attached to the customer
        if (paymentMethod.getCustomer() != null && paymentMethod.getCustomer().equals(stripeCustomerId)) {
            LOGGER.info("Payment method {} is already attached to customer {}", paymentMethodId, stripeCustomerId);
            return;
        }

        LOGGER.info("Attaching payment method {} to customer {}", paymentMethodId, stripeCustomerId);
        attachPaymentMethodToCustomer(paymentMethodId, stripeCustomerId);
    }

    public void attachPaymentMethodToCustomer(String stripePaymentMethodId, String stripeCustomerId) {
        RequestOptions requestOptions = getRequestOptions();
        PaymentMethod paymentMethod = retrieveStripePaymentMethod(stripePaymentMethodId);
        PaymentMethodAttachParams paymentMethodAttachParams = PaymentMethodAttachParams.builder().setCustomer(stripeCustomerId).build();
        callInStripeExceptionContext(() -> paymentMethod.attach(paymentMethodAttachParams, requestOptions));
    }

    private RequestOptions getRequestOptions() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        PaymentStripeConnectIntegration stripeConnectIntegration = paymentIntegrationGetService
            .getCompletedStripeConnectIntegration()
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PAYMENT_STRIPE_CONNECT_INTEGRATION, tenantId));
        String connectAccountId = stripeConnectIntegration.getConnectAccountId();

        return buildRequestOptions(getStripeApiKey(), connectAccountId);
    }

    public RequestOptions getRequestOptions(String idempotencyKey) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        PaymentStripeConnectIntegration stripeConnectIntegration = paymentIntegrationGetService
            .getCompletedStripeConnectIntegration()
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PAYMENT_STRIPE_CONNECT_INTEGRATION, tenantId));
        String connectAccountId = stripeConnectIntegration.getConnectAccountId();

        return buildRequestOptions(getStripeApiKey(), connectAccountId, idempotencyKey);
    }

    public void validateInvoiceAndInvoiceBalance(Invoice invoice, InvoiceBalance invoiceBalance) {
        if (invoice.getStatus() != InvoiceStatus.POSTED) {
            throw new ConflictingStateException(
                String.format("Cannot process payment for invoice %s in %s status", invoice.getInvoiceNumber().getNumber(), invoice.getStatus())
            );
        }

        if (Numbers.isZero(invoiceBalance.getBalance())) {
            throw new ConflictingStateException(String.format("Invoice balance for invoice %s is zero", invoice.getInvoiceNumber().getNumber()));
        }
    }

    public void completeSuccessfulPaymentIntent(PaymentIntent paymentIntent) {
        String tenantId = paymentIntent.getMetadata().get(STRIPE_METADATA_TENANT_ID_KEY);
        if (StringUtils.isBlank(tenantId)) {
            LOGGER.info("Payment intent {} does not have tenant id in metadata", paymentIntent.getId());
            return;
        }

        TenantContextInjector.runInTenantContext(tenantId, tenantIdProvider, () -> {
            PaymentAttempt paymentAttempt = paymentGetService
                .getPaymentAttemptByTransactionId(paymentIntent.getId())
                .orElseThrow(() -> new ServiceFailureException("Unable to get payment attempt for transaction id: " + paymentIntent.getId()));
            handleSucceededPaymentIntent(paymentIntent, paymentAttempt);
        });
    }

    private PaymentProviderPaymentMethod getPaymentProviderPaymentMethodByPayment(Payment payment) {
        AccountPaymentMethod accountPaymentMethod = accountPaymentMethodGetService.getAccountPaymentMethodByPayment(payment);

        Optional<PaymentProviderPaymentMethod> paymentProviderPaymentMethod = paymentProviderService.getPaymentProviderPaymentMethodById(
            UUID.fromString(accountPaymentMethod.getPaymentMethodId()),
            true
        );

        return paymentProviderPaymentMethod.orElseThrow(() ->
            new ServiceFailureException("Unable to get a provider payment method with id: " + payment.getPaymentMethodId())
        );
    }

    public PaymentProviderPaymentMethod createOrGetPaymentProviderPaymentMethod(PaymentMethod stripePaymentMethod, PaymentProviderCustomer customer) {
        Optional<PaymentProviderPaymentMethod> paymentMethodOptional = paymentProviderService.getPaymentProviderPaymentMethodByStripePaymentMethodId(
            customer.getCustomerId(),
            stripePaymentMethod.getId()
        );
        if (paymentMethodOptional.isPresent()) {
            return paymentMethodOptional.get();
        }

        PaymentProviderPaymentMethod paymentMethod = new PaymentProviderPaymentMethod();
        paymentMethod.setTenantId(customer.getTenantId());
        paymentMethod.setCustomerId(customer.getCustomerId());
        paymentMethod.setPaymentMethodId(stripePaymentMethod.getId());
        paymentMethod.setIsDeleted(false);

        PaymentMethodType paymentMethodType = PaymentMethodType.fromStripePaymentMethodType(stripePaymentMethod.getType());
        switch (paymentMethodType) {
            case CARD -> {
                PaymentMethod.Card card = stripePaymentMethod.getCard();
                paymentMethod.setPaymentMethodType(PaymentMethodType.CARD);
                paymentMethod.setLastFour(card.getLast4());
                YearMonth expiryYearMonth = YearMonth.of(Math.toIntExact(card.getExpYear()), Math.toIntExact(card.getExpMonth()));
                paymentMethod.setExpiryDate(expiryYearMonth.atEndOfMonth());
            }
            case ACH -> {
                PaymentMethod.UsBankAccount achAccount = stripePaymentMethod.getUsBankAccount();
                paymentMethod.setPaymentMethodType(PaymentMethodType.ACH);
                // TODO: check to see if we need any more details from the bank
                paymentMethod.setBankName(achAccount.getBankName());
                paymentMethod.setAccountType(achAccount.getAccountType());
                paymentMethod.setAccountNumberLastFour(achAccount.getLast4());
            }
            default -> {
                String message = String.format("unsupported payment method: %s", paymentMethodType);
                throw new IllegalStateException(message);
            }
        }

        return paymentProviderService.addPaymentProviderPaymentMethod(paymentMethod);
    }

    public Customer retrieveCustomer(String customerId, String connectAccountId) {
        RequestOptions requestOptions = buildRequestOptions(getStripeApiKey(), connectAccountId);

        return callInStripeExceptionContext(() -> Customer.retrieve(customerId, requestOptions));
    }

    @SuppressWarnings("unused")
    private void deletePaymentMethodFromStripe(String paymentMethodId, String connectAccountId) {
        PaymentMethod paymentMethod = retrieveStripePaymentMethod(paymentMethodId);
        RequestOptions requestOptions = getRequestOptions();
        callInStripeExceptionContext(() -> paymentMethod.detach(requestOptions));
    }

    public String getTenantIdFromPaymentIntent(PaymentIntent paymentIntent) {
        return paymentIntent.getMetadata().get(STRIPE_METADATA_TENANT_ID_KEY);
    }

    public FailedPaymentAttemptData completeFailedPaymentIntent(PaymentIntent paymentIntent) {
        PaymentAttempt paymentAttempt = paymentGetService
            .getPaymentAttemptByTransactionId(paymentIntent.getId())
            .orElseThrow(() -> new ServiceFailureException("Unable to get payment attempt for transaction id: " + paymentIntent.getId()));
        return handleFailedPaymentIntent(paymentIntent, paymentAttempt);
    }

    public PaymentIntent retrievePaymentIntent(String paymentIntentId) throws StripeException {
        RequestOptions requestOptions = getRequestOptions();
        return PaymentIntent.retrieve(paymentIntentId, requestOptions);
    }

    public void handleAuthorizationCodeCallback(String authorizationCode, String scope, String integrationId) {
        Validator.validateNonNullArguments(authorizationCode, scope, integrationId);

        Optional<PaymentStripeConnectIntegration> stripeConnectIntegrationOptional = paymentIntegrationDAO.getStripeConnectIntegrationByIntegrationId(
            integrationId
        );
        if (stripeConnectIntegrationOptional.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.PAYMENT_STRIPE_CONNECT_INTEGRATION, integrationId);
        }

        TokenResponse tokenResponse = getAccessToken(authorizationCode);

        String connectAccountId = tokenResponse.getStripeUserId();
        paymentIntegrationDAO.markStripePaymentIntegrationAsCompleted(integrationId, connectAccountId);
    }

    private TokenResponse getAccessToken(String authorizationCode) {
        RequestOptions requestOptions = buildRequestOptions(getStripeApiKey());

        Map<String, Object> params = new HashMap<>();
        params.put("grant_type", "authorization_code");
        params.put("code", authorizationCode);

        return callInStripeExceptionContext(() -> OAuth.token(params, requestOptions));
    }

    private void sendAutoPaySucceededDunning(Invoice.Number invoiceNumber, Payment payment) {
        PaymentProviderPaymentMethod paymentProviderPaymentMethod = getPaymentProviderPaymentMethodByPayment(payment);
        invoiceDunningService.sendInvoiceReminderEmail(
            invoiceNumber,
            Optional.of(DunningReminderType.AUTO_PAYMENT_SUCCEEDED),
            Optional.of(paymentProviderPaymentMethod)
        );
    }

    public Optional<String> processPayment(
        Invoice invoice,
        AccountPaymentMethod accountPaymentMethod,
        PaymentProviderCustomer paymentProviderCustomer,
        PaymentProviderPaymentMethod paymentProviderPaymentMethod,
        PaymentAttempt initialPaymentAttempt,
        InvoiceBalance invoiceBalance
    ) throws PaymentIntentFailedException, PaymentIntentMissingException {
        PaymentIntent paymentIntent = createPaymentIntentForAutomaticPayment(
            initialPaymentAttempt,
            paymentProviderCustomer,
            paymentProviderPaymentMethod,
            invoice.getInvoiceNumber(),
            invoiceBalance.getBalance(),
            invoiceBalance.getCurrencyCode(),
            getRequestOptions(initialPaymentAttempt.getId().toString())
        );

        // Mark payment attempt as confirmed
        PaymentAttempt confirmedPaymentAttempt = paymentStateProcessor.toConfirmedPaymentAttempt(initialPaymentAttempt, paymentIntent.getId());

        // Confirm payment intent
        Optional<PaymentIntent> confirmedPaymentIntent = confirmPaymentIntent(
            paymentIntent,
            invoice.getInvoiceNumber(),
            confirmedPaymentAttempt,
            true,
            getRequestOptions()
        );
        if (confirmedPaymentIntent.isEmpty()) {
            // Payment intent was not confirmed successfully, so let reconciliation job handle it
            return Optional.empty();
        }

        // TODO : Invoke this within payment attempt exception handler, which should later be moved to payment orchestrator
        Optional<FailedPaymentAttemptData> failedPaymentAttemptData = handleConfirmedOrProcessingPaymentIntent(
            confirmedPaymentIntent.get(),
            confirmedPaymentAttempt
        );
        if (failedPaymentAttemptData.isPresent()) {
            throw new PaymentIntentFailedException(failedPaymentAttemptData.get());
        }

        return Optional.ofNullable(confirmedPaymentAttempt.getPaymentId());
    }

    public void completeSuccessfulSetupIntent(SetupIntent setupIntent) {
        String tenantId = setupIntent.getMetadata().get(STRIPE_METADATA_TENANT_ID_KEY);
        if (StringUtils.isBlank(tenantId)) {
            LOGGER.info("Setup intent {} does not have tenant id in metadata", setupIntent.getId());
            return;
        }

        TenantContextInjector.runInTenantContext(tenantId, tenantIdProvider, () -> processSetupIntent(setupIntent));
    }

    private void processSetupIntent(SetupIntent setupIntent) {
        String accountId = setupIntent.getMetadata().get(STRIPE_METADATA_ACCOUNT_ID);
        String stripePaymentMethodId = setupIntent.getPaymentMethod();

        PaymentMethod stripePaymentMethod = retrieveStripePaymentMethod(stripePaymentMethodId);

        AccountPaymentMethod accountPaymentMethod = createAccountPaymentMethodFromStripePaymentMethod(
            stripePaymentMethod,
            accountId,
            AccountPaymentMethodStatus.ACTIVE
        );

        AccountPaymentMethod savedMethod = accountPaymentMethodService.addAccountPaymentMethod(accountPaymentMethod, null);
        accountAutomaticPaymentMethodService.setPaymentMethodAsActive(savedMethod);
    }

    private void tryAndAcquireStripePaymentLock(DSLContext transactionContext, String invoiceNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        String key = String.format(STRIPE_PAYMENT_LOCK_FORMAT, tenantId, invoiceNumber);
        Optional<Long> lock = PostgresAdvisoryLock.tryAcquireLock(transactionContext, key);
        if (lock.isEmpty()) {
            LOGGER.info("Stripe payment lock acquisition failed for invoice {}", invoiceNumber);
            throw new ConflictingStateException("A stripe payment is already in progress for this invoice. Please try again later.");
        }
    }

    private Optional<PaymentIntent> confirmPaymentIntent(
        PaymentIntent paymentIntent,
        Invoice.Number invoiceNumber,
        PaymentAttempt confirmedPaymentAttempt,
        boolean isAutomaticPayment,
        RequestOptions requestOptions
    ) throws PaymentIntentFailedException, PaymentIntentMissingException {
        try {
            PaymentIntentConfirmParams parameters = PaymentIntentConfirmParams.builder().setOffSession(isAutomaticPayment).build();
            return Optional.ofNullable(paymentIntent.confirm(parameters, requestOptions));
        } catch (ApiConnectionException | ApiException e) {
            // Cannot determine if payment intent was successful or not, so let reconciliation job handle it
            return Optional.empty();
        } catch (StripeException e) {
            LOGGER.warn("Error confirming payment intent {} for {}", paymentIntent.getId(), invoiceNumber.getNumber(), e);
            handleStripeError(e.getStripeError(), confirmedPaymentAttempt);
            return Optional.empty();
        }
    }

    public Optional<FailedPaymentAttemptData> handleConfirmedOrProcessingPaymentIntent(String paymentIntentId, PaymentAttempt paymentAttempt)
        throws PaymentIntentFailedException, PaymentIntentMissingException {
        try {
            PaymentIntent paymentIntent = retrievePaymentIntent(paymentIntentId);
            return handleConfirmedOrProcessingPaymentIntent(paymentIntent, paymentAttempt);
        } catch (StripeException e) {
            handleStripeError(e.getStripeError(), paymentAttempt);
        }
        return Optional.empty();
    }

    public Optional<FailedPaymentAttemptData> handleConfirmedOrProcessingPaymentIntent(PaymentIntent paymentIntent, PaymentAttempt paymentAttempt)
        throws PaymentIntentFailedException, PaymentIntentMissingException {
        Validator.validateNonNullArguments(paymentIntent, paymentAttempt);

        switch (paymentIntent.getStatus()) {
            case STRIPE_PAYMENT_INTENT_REQUIRES_CONFIRMATION_STATUS -> {
                // If payment intent requires confirmation, we need to confirm it
                Payment payment = paymentGetService.getPaymentByPaymentId(paymentAttempt.getPaymentId());
                boolean isAutomaticPayment = payment.getLifecycleType() == PaymentLifecycleType.AUTOMATIC;
                Optional<PaymentIntent> confirmedPaymentIntentOptional = confirmPaymentIntent(
                    paymentIntent,
                    new Invoice.Number(paymentIntent.getMetadata().get(STRIPE_METADATA_INVOICE_NUMBER_KEY)),
                    paymentAttempt,
                    isAutomaticPayment,
                    getRequestOptions()
                );
                if (confirmedPaymentIntentOptional.isEmpty()) {
                    LOGGER.warn("Payment intent {} could not be confirmed, reconciling later", paymentIntent.getId());
                    return Optional.empty();
                }
                paymentIntent = confirmedPaymentIntentOptional.get();
            }
            case STRIPE_PAYMENT_INTENT_REQUIRES_PAYMENT_METHOD_STATUS -> {
                Optional<PaymentIntent> canceledPaymentIntentOptional = cancelPaymentIntent(paymentIntent);
                if (canceledPaymentIntentOptional.isEmpty()) {
                    LOGGER.warn("Payment intent {} could not be canceled, reconciling later", paymentIntent.getId());
                    return Optional.empty();
                }
                paymentIntent = canceledPaymentIntentOptional.get();
            }
            default -> {
                /* Do nothing */
            }
        }

        PaymentAttemptState newState = mapPaymentIntentStatusToPaymentAttemptState(paymentIntent, paymentAttempt);
        switch (newState) {
            case SUCCEEDED -> handleSucceededPaymentIntent(paymentIntent, paymentAttempt);
            case FAILED -> {
                FailedPaymentAttemptData failedPaymentAttemptData = handleFailedPaymentIntent(paymentIntent, paymentAttempt);
                return Optional.of(failedPaymentAttemptData);
            }
            case PROCESSING -> {
                if (paymentAttempt.getState() != PaymentAttemptState.PROCESSING) {
                    paymentStateProcessor.toProcessingPaymentAttempt(paymentAttempt);
                }
            }
            default -> {
                /* Do nothing */
            }
        }

        return Optional.empty();
    }

    private Optional<PaymentIntent> cancelPaymentIntent(PaymentIntent paymentIntent) {
        try {
            RequestOptions requestOptions = getRequestOptions();
            PaymentIntentCancelParams cancelParams = PaymentIntentCancelParams.builder()
                .setCancellationReason(PaymentIntentCancelParams.CancellationReason.ABANDONED)
                .build();
            return Optional.of(paymentIntent.cancel(cancelParams, requestOptions));
        } catch (StripeException e) {
            // Failed to cancel payment, let reconciliation job handle it
            return Optional.empty();
        }
    }

    private void handleSucceededPaymentIntent(PaymentIntent succeededPaymentIntent, PaymentAttempt confirmedPaymentAttempt) {
        Payment payment = paymentGetService.getPaymentByPaymentId(confirmedPaymentAttempt.getPaymentId());
        if (payment.getState().isTerminal()) {
            LOGGER.info("Payment {} is already processed, skipping", payment.getPaymentId());
            return;
        }
        BigDecimal amountCaptured = SupportedCurrency.convertFromSmallestUnitToAmount(
            succeededPaymentIntent.getAmountReceived(),
            payment.getCurrencyCode()
        );
        Instant appliedOn = getAppliedOnForSuccessfulPaymentIntent(succeededPaymentIntent);
        switch (payment.getLifecycleType()) {
            case AUTOMATIC -> paymentStateProcessor.toSucceededPaymentAttempt(
                confirmedPaymentAttempt,
                amountCaptured,
                appliedOn,
                TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider)
            );
            case ONE_TIME -> {
                AccountPaymentMethod savedAccountPaymentMethod = accountPaymentMethodGetService.getAccountPaymentMethodByPayment(payment);
                PaymentMethod stripePaymentMethod = retrieveStripePaymentMethod(succeededPaymentIntent.getPaymentMethod());
                AccountPaymentMethod newAccountPaymentMethod = createAccountPaymentMethodFromStripePaymentMethod(
                    stripePaymentMethod,
                    payment.getCustomerAccountId(),
                    AccountPaymentMethodStatus.ACTIVE
                );
                newAccountPaymentMethod.setId(savedAccountPaymentMethod.getId());

                boolean optInForAutomaticPayments = BooleanUtils.toBoolean(
                    succeededPaymentIntent.getMetadata().get(STRIPE_METADATA_AUTOMATIC_PAYMENT_OPT_IN)
                );
                DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
                dslContext.transaction(configuration -> {
                    DSLContext transactionContext = DSL.using(configuration);

                    paymentStateProcessor.toSucceededPaymentAttempt(confirmedPaymentAttempt, amountCaptured, appliedOn, transactionContext);

                    AccountPaymentMethod updatedAccountPaymentMethod = accountPaymentMethodService.updateAccountPaymentMethodById(
                        savedAccountPaymentMethod.getId(),
                        newAccountPaymentMethod,
                        transactionContext
                    );
                    if (optInForAutomaticPayments) {
                        accountAutomaticPaymentMethodService.setPaymentMethodAsActive(updatedAccountPaymentMethod);
                    }
                });
            }
            default -> throw new UnsupportedOperationException("Unsupported payment lifecycle type: " + payment.getLifecycleType());
        }

        Invoice.Number invoiceNumber = new Invoice.Number(succeededPaymentIntent.getMetadata().get(STRIPE_METADATA_INVOICE_NUMBER_KEY));
        sendAutoPaySucceededDunning(invoiceNumber, payment);
    }

    private Instant getAppliedOnForSuccessfulPaymentIntent(PaymentIntent paymentIntent) {
        try {
            return Instant.ofEpochSecond(paymentIntent.getLatestChargeObject().getCreated());
        } catch (Exception e) {
            LOGGER.warn("Unable to get applied on time for successful payment intent: {}", paymentIntent);
            return Instant.now();
        }
    }

    public void handleStripeError(StripeError stripeError, PaymentAttempt paymentAttempt)
        throws PaymentIntentFailedException, PaymentIntentMissingException {
        StripeErrorType errorType = StripeErrorType.fromString(stripeError.getType());
        if (StripeErrorType.API_ERROR == errorType) {
            LOGGER.warn("Stripe server is temporarily unavailable, please try again later. Stripe error message={}", stripeError.getMessage());
            return;
        }

        LOGGER.info(
            "Stripe error occurred while processing payment attempt {}: type={}, message={}, code={}",
            paymentAttempt.getId(),
            stripeError.getType(),
            stripeError.getMessage(),
            stripeError.getCode()
        );

        if (stripeError.getPaymentIntent() != null) {
            FailedPaymentAttemptData failedPaymentAttemptData = handleFailedPaymentIntent(stripeError.getPaymentIntent(), paymentAttempt);
            throw new PaymentIntentFailedException(failedPaymentAttemptData);
        }

        PaymentFailureReason failureReason = PaymentFailureReason.builder()
            .type(stripeError.getType())
            .message(stripeError.getMessage())
            .errorCode(stripeError.getCode())
            .paymentProvider(SupportedPaymentProvider.STRIPE)
            .isInternal(false)
            .build();

        Payment payment = paymentGetService.getPaymentByPaymentId(paymentAttempt.getPaymentId());

        Instant failedOn = getFailedOnForFailedPaymentIntent(stripeError.getPaymentIntent());

        FailedPaymentAttemptData failedPaymentAttemptData = ImmutableFailedPaymentAttemptData.builder()
            .payment(payment)
            .paymentAttempt(paymentAttempt)
            .paymentFailureReason(failureReason)
            .failedOn(failedOn)
            .invoiceNumber(Optional.empty())
            .accountPaymentMethodToUpdate(Optional.empty())
            .build();

        throw new PaymentIntentMissingException(failedPaymentAttemptData);
    }

    private Instant getFailedOnForFailedPaymentIntent(PaymentIntent paymentIntent) {
        try {
            return Instant.ofEpochSecond(paymentIntent.getLatestChargeObject().getCreated());
        } catch (Exception e) {
            LOGGER.warn("Unable to get failed on time for failed payment intent: {}", paymentIntent);
            return Instant.now();
        }
    }

    private FailedPaymentAttemptData handleFailedPaymentIntent(PaymentIntent failedPaymentIntent, PaymentAttempt confirmedPaymentAttempt) {
        LOGGER.info("Handling failed payment intent {} for payment attempt {}", failedPaymentIntent.getId(), confirmedPaymentAttempt.getId());

        var builder = PaymentFailureReason.builder()
            .paymentProvider(SupportedPaymentProvider.STRIPE)
            .status(failedPaymentIntent.getStatus())
            .isInternal(false);

        Optional<StripeError> lastPaymentError = Optional.ofNullable(failedPaymentIntent.getLastPaymentError());
        if (lastPaymentError.isPresent()) {
            builder = builder
                .errorCode(lastPaymentError.get().getCode())
                .declineCode(lastPaymentError.get().getDeclineCode())
                .message(lastPaymentError.get().getMessage());
        }

        PaymentFailureReason failureReason = builder.build();
        Payment payment = paymentGetService.getPaymentByPaymentId(confirmedPaymentAttempt.getPaymentId());

        String accountId = payment.getCustomerAccountId();

        Optional<AccountPaymentMethod> accountPaymentMethodToUpdate = Optional.empty();
        if (lastPaymentError.isPresent()) {
            PaymentMethod stripePaymentMethod = lastPaymentError.get().getPaymentMethod();
            accountPaymentMethodToUpdate = Optional.of(
                createAccountPaymentMethodFromStripePaymentMethod(stripePaymentMethod, accountId, AccountPaymentMethodStatus.DISABLED)
            );
        }
        Instant failedOn = getFailedOnForFailedPaymentIntent(failedPaymentIntent);

        return ImmutableFailedPaymentAttemptData.builder()
            .payment(payment)
            .paymentAttempt(confirmedPaymentAttempt)
            .paymentFailureReason(failureReason)
            .failedOn(failedOn)
            .invoiceNumber(new Invoice.Number(failedPaymentIntent.getMetadata().get(STRIPE_METADATA_INVOICE_NUMBER_KEY)))
            .accountPaymentMethodToUpdate(accountPaymentMethodToUpdate)
            .build();
    }

    private PaymentAttemptState mapPaymentIntentStatusToPaymentAttemptState(PaymentIntent paymentIntent, PaymentAttempt paymentAttempt) {
        if (StringUtils.isBlank(paymentIntent.getStatus())) {
            throw new ServiceFailureException(String.format("Payment intent %s has no status", paymentIntent.getId()));
        }
        return switch (paymentIntent.getStatus()) {
            case STRIPE_PAYMENT_INTENT_PROCESSING_STATUS -> PaymentAttemptState.PROCESSING;
            case STRIPE_PAYMENT_INTENT_SUCCEEDED_STATUS -> PaymentAttemptState.SUCCEEDED;
            case STRIPE_PAYMENT_INTENT_FAILED_STATUS, STRIPE_PAYMENT_INTENT_CANCELED_STATUS -> PaymentAttemptState.FAILED;
            case STRIPE_PAYMENT_INTENT_REQUIRES_ACTION_METHOD_STATUS -> PaymentAttemptState.CONFIRMED;
            default -> {
                // unhandled status, keep the current state
                LOGGER.warn("Unsupported payment intent status: {}", paymentIntent.getStatus());
                yield paymentAttempt.getState();
            }
        };
    }

    private AccountPaymentMethod createAccountPaymentMethodFromStripePaymentMethod(
        PaymentMethod stripePaymentMethod,
        String accountId,
        AccountPaymentMethodStatus status
    ) {
        PaymentProviderCustomer customer = paymentProviderService
            .getPaymentProviderCustomerByAccountId(tenantIdProvider.provideTenantIdString(), accountId)
            .orElseThrow(() -> new ServiceFailureException("Unable to get payment provider customer for account id: " + accountId));
        var paymentTypeOptional = PaymentMethodType.fromStripePaymentMethodType(stripePaymentMethod.getType()).toLegacyPaymentType();
        if (paymentTypeOptional.isEmpty()) {
            throw new ServiceFailureException("Unable to convert payment method type: " + stripePaymentMethod.getType());
        }
        PaymentProviderPaymentMethod paymentProviderPaymentMethod = createOrGetPaymentProviderPaymentMethod(stripePaymentMethod, customer);

        AccountPaymentMethod accountPaymentMethod = new AccountPaymentMethod();
        accountPaymentMethod.setAccountId(accountId);
        String paymentMethodName = String.format(PAYMENT_METHOD_NAME_FORMAT, stripePaymentMethod.getId());
        accountPaymentMethod.setName(paymentMethodName);
        accountPaymentMethod.setStatus(status);
        accountPaymentMethod.setPaymentMethodId(String.valueOf(paymentProviderPaymentMethod.getId()));
        accountPaymentMethod.setPaymentType(paymentTypeOptional.get());
        return accountPaymentMethod;
    }
}
