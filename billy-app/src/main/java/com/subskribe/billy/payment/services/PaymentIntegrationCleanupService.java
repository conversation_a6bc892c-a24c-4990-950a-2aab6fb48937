package com.subskribe.billy.payment.services;

import com.subskribe.billy.account.services.AccountPaymentMethodService;
import com.subskribe.billy.payment.db.PaymentProviderDAO;
import com.subskribe.billy.payment.model.PaymentProviderPaymentMethod;
import java.util.List;
import java.util.UUID;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

public class PaymentIntegrationCleanupService {

    private final AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService;
    private final AccountPaymentMethodService accountPaymentMethodService;
    private final PaymentProviderDAO paymentProviderDAO;

    @Inject
    public PaymentIntegrationCleanupService(
        AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService,
        AccountPaymentMethodService accountPaymentMethodService,
        PaymentProviderDAO paymentProviderDAO
    ) {
        this.accountAutomaticPaymentMethodService = accountAutomaticPaymentMethodService;
        this.accountPaymentMethodService = accountPaymentMethodService;
        this.paymentProviderDAO = paymentProviderDAO;
    }

    public void deleteAllPaymentMethodsForIntegration(UUID integrationId, Configuration configuration) {
        DSLContext txnContext = DSL.using(configuration);
        List<PaymentProviderPaymentMethod> paymentProviderPaymentMethods =
            paymentProviderDAO.deactivateAllPaymentMethodsForIntegrationIdInTransaction(txnContext, integrationId);
        List<UUID> deletedProviderPaymentMethodIds = paymentProviderPaymentMethods.stream().map(PaymentProviderPaymentMethod::getId).toList();
        List<UUID> deletedAccountPaymentMethodIds = accountPaymentMethodService.deletePaymentMethodsByProviderIdsReturningMethodIds(
            txnContext,
            deletedProviderPaymentMethodIds
        );
        accountAutomaticPaymentMethodService.deactivateAutomaticPaymentsForAccountPaymentMethodIds(txnContext, deletedAccountPaymentMethodIds);
    }
}
