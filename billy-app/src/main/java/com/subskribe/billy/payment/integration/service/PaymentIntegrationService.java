package com.subskribe.billy.payment.integration.service;

import com.stripe.Stripe;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.aws.secretsmanager.model.SecretType;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.payment.integration.db.PaymentIntegrationDAO;
import com.subskribe.billy.payment.services.PaymentIntegrationCleanupService;
import com.subskribe.billy.payment.stripe.StripeConfiguration;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegrationSetupResponse;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.http.client.utils.URIBuilder;
import org.jooq.DSLContext;

public class PaymentIntegrationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentIntegrationService.class);

    private static final String STRIPE_PATH_OAUTH_AUTHORIZE = "/oauth/authorize";

    private static final String STRIPE_REQUEST_PARAM_CLIENT_ID = "client_id";

    private static final String STRIPE_REQUEST_PARAM_RESPONSE_TYPE = "response_type";

    private static final String STRIPE_REQUEST_PARAM_CODE = "code";

    private static final String STRIPE_REQUEST_PARAM_REDIRECT_URI = "redirect_uri";

    private static final String STRIPE_REQUEST_PARAM_SCOPE = "scope";

    private static final String STRIPE_REQUEST_PARAM_READ_WRITE = "read_write";

    private static final String STRIPE_REQUEST_PARAM_STATE = "state";

    private final PaymentIntegrationDAO paymentIntegrationDAO;

    private final SecretsService secretsService;

    private final StripeConfiguration stripeConfiguration;

    private final PaymentIntegrationGetService paymentIntegrationGetService;

    private final BillyConfiguration billyConfiguration;

    private final PaymentIntegrationCleanupService paymentIntegrationCleanupService;

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    @Inject
    public PaymentIntegrationService(
        PaymentIntegrationDAO paymentIntegrationDAO,
        SecretsService secretsService,
        BillyConfiguration billyConfiguration,
        PaymentIntegrationGetService paymentIntegrationGetService,
        PaymentIntegrationCleanupService paymentIntegrationCleanupService,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider
    ) {
        this.paymentIntegrationDAO = paymentIntegrationDAO;
        this.secretsService = secretsService;
        stripeConfiguration = billyConfiguration.getStripeConfiguration();
        this.paymentIntegrationGetService = paymentIntegrationGetService;
        this.billyConfiguration = billyConfiguration;
        this.paymentIntegrationCleanupService = paymentIntegrationCleanupService;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
    }

    // TODO: Support re-enabling of soft deleted integration with same connect_account_id
    //  Also think about re-enabling of soft deleted payment methods?
    public PaymentStripeConnectIntegrationSetupResponse initiateStripeConnectIntegration() {
        if (!paymentIntegrationGetService.isPaymentIntegrationEnabled()) {
            throw new UnsupportedOperationException("Payment integration is not enabled.");
        }

        Optional<PaymentStripeConnectIntegration> stripeConnectIntegrationOptional = paymentIntegrationGetService.getStripeConnectIntegration();
        if (stripeConnectIntegrationOptional.isPresent()) {
            return createStripeConnectResponseFromExistingIntegration(stripeConnectIntegrationOptional.get());
        }

        String integrationId = AutoGenerate.getNewId();
        PaymentStripeConnectIntegration newStripeConnectIntegration = paymentIntegrationDAO.createStripePaymentIntegration(integrationId);

        URI oAuthUrl = createStripeOAuthUri(newStripeConnectIntegration.getIntegrationId());
        return new PaymentStripeConnectIntegrationSetupResponse(oAuthUrl.toString());
    }

    private PaymentStripeConnectIntegrationSetupResponse createStripeConnectResponseFromExistingIntegration(
        PaymentStripeConnectIntegration stripeConnectIntegration
    ) {
        if (stripeConnectIntegration.getIsCompleted()) {
            throw new IllegalStateException("Current tenant already has a stripe payment integration. Please delete the existing integration first.");
        } else {
            URI oAuthUrl = createStripeOAuthUri(stripeConnectIntegration.getIntegrationId());
            return new PaymentStripeConnectIntegrationSetupResponse(oAuthUrl.toString());
        }
    }

    private URI createStripeOAuthUri(String integrationId) {
        String baseUrl = String.format("%s%s", Stripe.getConnectBase(), STRIPE_PATH_OAUTH_AUTHORIZE);
        String clientId = secretsService.getStripeSecret(SecretType.STRIPE_CLIENT_ID, true);
        try {
            return new URIBuilder(baseUrl)
                .addParameter(STRIPE_REQUEST_PARAM_CLIENT_ID, clientId)
                .addParameter(STRIPE_REQUEST_PARAM_RESPONSE_TYPE, STRIPE_REQUEST_PARAM_CODE)
                .addParameter(STRIPE_REQUEST_PARAM_REDIRECT_URI, stripeConfiguration.getRedirectUri())
                .addParameter(STRIPE_REQUEST_PARAM_SCOPE, STRIPE_REQUEST_PARAM_READ_WRITE)
                .addParameter(STRIPE_REQUEST_PARAM_STATE, integrationId)
                .build();
        } catch (URISyntaxException e) {
            String errorMessage = "Failed to create Stripe OAuth URL";
            LOGGER.warn(errorMessage);
            throw new ServiceFailureException(errorMessage);
        }
    }

    public PaymentStripeConnectIntegration deleteCurrentStripePaymentIntegration() {
        PaymentStripeConnectIntegration integration = paymentIntegrationGetService
            .getCompletedStripeConnectIntegration()
            .orElseThrow(() -> new IllegalStateException("Stripe integration does not exist"));

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return tenantDslContext.transactionResult(configuration -> {
            paymentIntegrationCleanupService.deleteAllPaymentMethodsForIntegration(integration.getId(), configuration);
            // TODO : also delete existing retry config attached for now, use a mapping table in the future

            return paymentIntegrationDAO.deleteCurrentStripePaymentIntegrationForTenant(configuration);
        });
    }

    public void createTestStripeIntegration(String stripeConnectAccountId) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new UnsupportedOperationException("Invalid operation.");
        }
        String integrationId = AutoGenerate.getNewId();
        paymentIntegrationDAO.createStripePaymentIntegration(integrationId);
        paymentIntegrationDAO.markStripePaymentIntegrationAsCompleted(integrationId, stripeConnectAccountId);
    }
}
