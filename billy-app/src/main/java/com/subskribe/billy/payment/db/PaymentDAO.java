package com.subskribe.billy.payment.db;

import static com.subskribe.billy.jooq.default_schema.tables.AccountPaymentMethod.ACCOUNT_PAYMENT_METHOD;
import static com.subskribe.billy.jooq.default_schema.tables.Payment.PAYMENT;
import static com.subskribe.billy.jooq.default_schema.tables.PaymentAttempt.PAYMENT_ATTEMPT;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.PaymentAttemptRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.PaymentRecord;
import com.subskribe.billy.payment.mapper.PaymentRecordMapper;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.model.PaymentAttempt;
import com.subskribe.billy.payment.model.PaymentAttemptState;
import com.subskribe.billy.payment.model.PaymentMetadata;
import com.subskribe.billy.payment.model.PaymentReconciliationStatus;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.PaymentState;
import com.subskribe.billy.shared.enums.PaymentStatus;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.JSON;
import org.mapstruct.factory.Mappers;

public class PaymentDAO {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentDAO.class);

    private static final int MINUTES_SINCE_LAST_RECONCILIATION_ATTEMPT = 30;

    private static final Set<String> NON_TERMINAL_PAYMENT_STATE_NAMES = Stream.of(PaymentState.values())
        .filter(Predicate.not(PaymentState::isTerminal))
        .map(Enum::name)
        .collect(Collectors.toSet());

    private static final Set<String> NON_TERMINAL_PAYMENT_ATTEMPT_STATE_NAMES = Stream.of(PaymentAttemptState.values())
        .filter(Predicate.not(PaymentAttemptState::isTerminal))
        .map(Enum::name)
        .collect(Collectors.toSet());

    private final DSLContextProvider dslContextProvider;
    private final PaymentRecordMapper paymentRecordMapper;
    private final TenantIdProvider tenantIdProvider;

    @Inject
    public PaymentDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider) {
        this.dslContextProvider = dslContextProvider;
        paymentRecordMapper = Mappers.getMapper(PaymentRecordMapper.class);
        this.tenantIdProvider = tenantIdProvider;
    }

    public Payment addPayment(Payment payment, DSLContext transactionContext) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = Optional.ofNullable(transactionContext).orElseGet(() -> dslContextProvider.get(tenantId));

        PaymentRecord accountPaymentRecord = paymentRecordMapper.paymentToRecord(payment);
        accountPaymentRecord.setTenantId(tenantId);
        accountPaymentRecord.reset(PAYMENT.ID);
        PaymentRecord result = tenantDSLContext.insertInto(PAYMENT).set(accountPaymentRecord).returning().fetchOne();
        return paymentRecordMapper.recordToPayment(result);
    }

    public List<Payment> getPayments(PaginationQueryParams paginationQueryParams) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext.select().from(PAYMENT).where(PAYMENT.TENANT_ID.eq(tenantId));

        List<PaymentRecord> paymentRecords = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            PAYMENT,
            PAYMENT.ID,
            PAYMENT.TENANT_ID,
            PAYMENT.CREATED_ON,
            PaymentRecord.class
        );

        return paymentRecordMapper.recordsToPayments(paymentRecords);
    }

    public List<Payment> getPaymentsByPaymentIds(List<String> paymentIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<PaymentRecord> paymentRecords = dslContext
            .select()
            .from(PAYMENT)
            .where(PAYMENT.TENANT_ID.eq(tenantId))
            .and(PAYMENT.PAYMENT_ID.in(paymentIds))
            .fetchInto(PaymentRecord.class);
        return paymentRecordMapper.recordsToPayments(paymentRecords);
    }

    public List<Payment> getAutomaticPaymentsByPaymentIds(List<String> paymentIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<PaymentRecord> paymentRecords = dslContext
            .select()
            .from(PAYMENT)
            .join(ACCOUNT_PAYMENT_METHOD)
            .on(ACCOUNT_PAYMENT_METHOD.ID.eq(PAYMENT.PAYMENT_METHOD_ID))
            .where(PAYMENT.TENANT_ID.eq(tenantId))
            .and(PAYMENT.PAYMENT_ID.in(paymentIds))
            .and(ACCOUNT_PAYMENT_METHOD.PAYMENT_TYPE.in(PaymentType.SUPPORTED_AUTOMATIC_PAYMENT_TYPES))
            .fetchInto(PaymentRecord.class);
        return paymentRecordMapper.recordsToPayments(paymentRecords);
    }

    public boolean checkWhetherAllThesePaymentsAreVoided(List<String> paymentIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<PaymentRecord> records = dslContext
            .select(PAYMENT.PAYMENT_ID, PAYMENT.STATE)
            .from(PAYMENT)
            .where(PAYMENT.PAYMENT_ID.in(paymentIds))
            .and(PAYMENT.STATE.notEqualIgnoreCase(PaymentState.FAILED.name()))
            .fetchInto(PaymentRecord.class);
        int unvoidedPaymentsCount = records
            .stream()
            .filter(r -> StringUtils.isBlank(r.getState()) || !r.getState().equalsIgnoreCase(PaymentState.VOIDED.name()))
            .toList()
            .size();
        return unvoidedPaymentsCount == 0;
    }

    private List<PaymentRecord> getPaymentRecordsToReconcile(int maxRecords) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        /*
        There are two cases:

        1) Either a record is not in a RECONCILING status already --> we can pick this up for reconciliation.

        2) A "zombie" record could be in reconciling status that needs to be picked up:
           Imagine for example if we put it in reconciling status and the job dies in the middle.
           For those, and for the purpose of OCC, I am going to allow a time limit of say 30 minutes:
           If a record has been put in RECONCILING status for more than 30 minutes: Assume it can be picked up again.


         In both cases, if the payment is recently updated (less than 30 minutes), we can ignore it. It will be picked up
         in the next run of the reconciliation job.
         */

        LocalDateTime nowMinusMinutesSinceLastReconciliationAttempt = LocalDateTime.now(ZoneOffset.UTC).minusMinutes(
            MINUTES_SINCE_LAST_RECONCILIATION_ATTEMPT
        );

        // TODO : ignore scheduled for retry payments for reconciliation, maybe introduce a new status for that like AWAITING_RETRY
        //  once the retry attempt starts we should change the state to PROCESSING or something like that so it gets picked up for reconciliation later on
        return tenantDSLContext
            .update(PAYMENT)
            .set(PAYMENT.STATUS, PaymentReconciliationStatus.RECONCILING.name())
            .where(PAYMENT.STATE.in(NON_TERMINAL_PAYMENT_STATE_NAMES)) // TODO : exclude AWAITING_RETRY state
            .and(PAYMENT.TENANT_ID.eq(tenantId))
            .and(PAYMENT.UPDATED_ON.le(nowMinusMinutesSinceLastReconciliationAttempt))
            .and(PAYMENT.STATUS.isNull().or(PAYMENT.STATUS.ne(PaymentReconciliationStatus.NOT_ELECTRONIC.name())))
            .limit(maxRecords)
            .returning()
            .fetch();
    }

    public List<Payment> getPaymentsToReconcile(int maxRecords) {
        List<PaymentRecord> accountPaymentRecords = getPaymentRecordsToReconcile(maxRecords);
        return paymentRecordMapper.recordsToPayments(accountPaymentRecords);
    }

    public List<Payment> getPaymentsByAccountId(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<PaymentRecord> accountPaymentRecords = tenantDSLContext
            .select()
            .from(PAYMENT)
            .where(PAYMENT.CUSTOMER_ACCOUNT_ID.eq(accountId))
            .and(PAYMENT.TENANT_ID.eq(tenantId))
            .fetchInto(PaymentRecord.class);
        return paymentRecordMapper.recordsToPayments(accountPaymentRecords);
    }

    public Optional<Payment> getPaymentByPaymentId(String paymentId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return getPaymentByPaymentId(tenantDSLContext, tenantId, paymentId);
    }

    public Optional<Payment> getPaymentByPaymentIdInTransaction(DSLContext txnContext, String paymentId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return getPaymentByPaymentId(txnContext, tenantId, paymentId);
    }

    private Optional<Payment> getPaymentByPaymentId(DSLContext dslContext, String tenantId, String paymentId) {
        PaymentRecord accountPaymentRecord = dslContext
            .select()
            .from(PAYMENT)
            .where(PAYMENT.PAYMENT_ID.eq(paymentId))
            .and(PAYMENT.TENANT_ID.eq(tenantId))
            .fetchOneInto(PaymentRecord.class);
        Payment payment = paymentRecordMapper.recordToPayment(accountPaymentRecord);
        return Optional.ofNullable(payment);
    }

    public Optional<Payment> getPaymentByTransactionId(String tenantId, String transactionId) {
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        PaymentRecord accountPaymentRecord = tenantDSLContext
            .select()
            .from(PAYMENT)
            .where(PAYMENT.TRANSACTION_ID.eq(transactionId))
            .and(PAYMENT.TENANT_ID.eq(tenantId))
            .and(PAYMENT.STATE.in(NON_TERMINAL_PAYMENT_STATE_NAMES))
            .fetchOneInto(PaymentRecord.class);
        Payment payment = paymentRecordMapper.recordToPayment(accountPaymentRecord);
        return Optional.ofNullable(payment);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public void updatePayment(Payment payment, Optional<DSLContext> transactionContext) {
        throwIfPaymentNotFound(payment.getPaymentId());
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = transactionContext.orElseGet(() -> TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider));
        PaymentRecord updatePayment = paymentRecordMapper.paymentToRecord(payment);
        updatePayment.setTenantId(tenantId);
        updatePayment.reset(PAYMENT.CREATED_ON);
        tenantDSLContext.executeUpdate(updatePayment);
    }

    private void throwIfPaymentNotFound(String paymentId) {
        if (paymentId == null) {
            throw new IllegalArgumentException("Payment id is null.");
        }

        Optional<Payment> originalPayment = getPaymentByPaymentId(paymentId);
        if (originalPayment.isEmpty()) {
            String paymentIdString = Optional.of(paymentId).orElse(null);
            LOGGER.error("payment with id {} doesn't exist", paymentIdString);
            throw new ObjectNotFoundException(BillyObjectType.PAYMENT, paymentIdString);
        }
    }

    public void deletePayment(String paymentId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        tenantDSLContext.delete(PAYMENT).where(PAYMENT.PAYMENT_ID.eq(paymentId)).and(PAYMENT.TENANT_ID.eq(tenantId)).execute();
    }

    public boolean updateVoidedPaymentAttributesInTransaction(
        String paymentId,
        PaymentState newState,
        PaymentStatus newStatus,
        DSLContext dslContext,
        Instant voidDate
    ) {
        LocalDateTime voidRequestedOnTimeStamp = LocalDateTime.now(ZoneOffset.UTC);
        LocalDateTime voidTimeStamp = LocalDateTime.ofInstant(voidDate, ZoneOffset.UTC);
        Validator.validateNonNullArguments(paymentId, newState, newStatus);
        String tenantId = tenantIdProvider.provideTenantIdString();
        int updatedRowCount = dslContext
            .update(PAYMENT)
            .set(PAYMENT.STATE, newState.name())
            .set(PAYMENT.STATUS, newStatus.name())
            .set(PAYMENT.VOID_DATE, voidTimeStamp)
            .set(PAYMENT.VOID_REQUESTED_ON, voidRequestedOnTimeStamp)
            .where(PAYMENT.TENANT_ID.eq(tenantId))
            .and(PAYMENT.PAYMENT_ID.eq(paymentId))
            .execute();
        return updatedRowCount == 1;
    }

    public void ensureUniquePaymentId(DSLContext dslContext, String paymentId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        PaymentRecord paymentRecord = dslContext
            .select()
            .from(PAYMENT)
            .where(PAYMENT.TENANT_ID.eq(tenantId))
            .and(PAYMENT.PAYMENT_ID.eq(paymentId))
            .fetchOneInto(PaymentRecord.class);
        if (Objects.isNull(paymentRecord)) {
            return;
        }
        throwDuplicatePaymentIdException(paymentId);
    }

    public List<Payment> getSucceededPayments(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext
            .select()
            .from(PAYMENT)
            .where(PAYMENT.TENANT_ID.eq(tenantId))
            .and(PAYMENT.STATE.eq(PaymentState.SUCCEED.toString()))
            .and(PAYMENT.CUSTOMER_ACCOUNT_ID.eq(accountId))
            .fetchInto(PaymentRecord.class);

        return paymentRecordMapper.recordsToPayments(query);
    }

    private void throwDuplicatePaymentIdException(String paymentId) {
        var message = "Duplicated PaymentId generated. PaymentId = " + paymentId;
        LOGGER.info(message);
        throw new DuplicateIdException(message);
    }

    public List<PaymentAttempt> getPaymentAttemptsByPaymentId(String paymentId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<PaymentAttemptRecord> records = dslContext
            .select()
            .from(PAYMENT_ATTEMPT)
            .where(PAYMENT_ATTEMPT.TENANT_ID.eq(tenantId))
            .and(PAYMENT_ATTEMPT.PAYMENT_ID.eq(paymentId))
            .fetchInto(PaymentAttemptRecord.class);

        if (CollectionUtils.isEmpty(records)) {
            return List.of();
        }

        return paymentRecordMapper.recordsToPaymentAttempts(records);
    }

    public PaymentAttempt addPaymentAttempt(PaymentAttempt paymentAttempt, DSLContext transactionContext) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = Optional.ofNullable(transactionContext).orElseGet(() -> dslContextProvider.get(tenantId));

        PaymentAttemptRecord record = paymentRecordMapper.paymentAttemptToRecord(paymentAttempt);
        record.reset(PAYMENT_ATTEMPT.ID);
        record.setTenantId(tenantId);

        PaymentAttemptRecord savedRecord = dslContext.insertInto(PAYMENT_ATTEMPT).set(record).returning().fetchOneInto(PaymentAttemptRecord.class);

        return paymentRecordMapper.recordToPaymentAttempt(savedRecord);
    }

    public PaymentAttempt updatePaymentAttempt(
        PaymentAttempt currentPaymentAttempt,
        PaymentAttempt newPaymentAttempt,
        DSLContext transactionContext
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = Optional.ofNullable(transactionContext).orElseGet(() -> dslContextProvider.get(tenantId));

        JSON failureReason = paymentRecordMapper.serializeFailureReason(newPaymentAttempt.getFailureReason());
        PaymentAttemptRecord savedRecord = dslContext
            .update(PAYMENT_ATTEMPT)
            .set(PAYMENT_ATTEMPT.STATE, newPaymentAttempt.getState().name())
            .set(PAYMENT_ATTEMPT.TRANSACTION_ID, newPaymentAttempt.getTransactionId())
            .set(PAYMENT_ATTEMPT.ATTEMPTED_ON, DateTimeConverter.instantToLocalDateTime(newPaymentAttempt.getAttemptedOn()))
            .set(PAYMENT_ATTEMPT.FAILURE_REASON, failureReason)
            .where(PAYMENT_ATTEMPT.ID.eq(currentPaymentAttempt.getId()))
            .and(PAYMENT_ATTEMPT.STATE.eq(currentPaymentAttempt.getState().name()))
            .returning()
            .fetchOneInto(PaymentAttemptRecord.class);

        if (savedRecord == null) {
            throw new IllegalStateException(
                String.format(
                    "Failed to update payment attempt with id %s from expected state %s to new state %s",
                    currentPaymentAttempt.getId(),
                    currentPaymentAttempt.getState(),
                    newPaymentAttempt.getState()
                )
            );
        }

        return paymentRecordMapper.recordToPaymentAttempt(savedRecord);
    }

    public Optional<PaymentAttempt> getPaymentAttemptByTransactionId(String transactionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        PaymentAttemptRecord record = dslContext
            .select()
            .from(PAYMENT_ATTEMPT)
            .where(PAYMENT_ATTEMPT.TENANT_ID.eq(tenantId))
            .and(PAYMENT_ATTEMPT.TRANSACTION_ID.eq(transactionId))
            .fetchOneInto(PaymentAttemptRecord.class);

        return Optional.ofNullable(paymentRecordMapper.recordToPaymentAttempt(record));
    }

    public List<PaymentAttempt> getPaymentAttemptsToReconcile(int maxRecords) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        LocalDateTime nowMinusMinutesSinceLastReconciliationAttempt = LocalDateTime.now(ZoneOffset.UTC).minusMinutes(
            MINUTES_SINCE_LAST_RECONCILIATION_ATTEMPT
        );

        var records = tenantDSLContext
            .selectFrom(PAYMENT_ATTEMPT)
            .where(PAYMENT_ATTEMPT.STATE.in(NON_TERMINAL_PAYMENT_ATTEMPT_STATE_NAMES))
            .and(PAYMENT_ATTEMPT.TENANT_ID.eq(tenantId))
            .and(PAYMENT_ATTEMPT.ATTEMPTED_ON.le(nowMinusMinutesSinceLastReconciliationAttempt).or(PAYMENT_ATTEMPT.ATTEMPTED_ON.isNull()))
            .limit(maxRecords)
            .fetchInto(PaymentAttemptRecord.class);
        return paymentRecordMapper.recordsToPaymentAttempts(records);
    }

    public List<Payment> getPaymentsByTenantIdAndNullBankAccountAndBankFeeForUpdate(String tenantId, DSLContext dslContext) {
        List<PaymentRecord> paymentRecords = dslContext
            .select()
            .from(PAYMENT)
            .where(PAYMENT.TENANT_ID.eq(tenantId))
            .and(PAYMENT.PAYMENT_BANK_ACCOUNT_ID.isNull())
            .and(PAYMENT.BANK_FEE.isNull())
            .forUpdate()
            .fetchInto(PaymentRecord.class);
        return paymentRecordMapper.recordsToPayments(paymentRecords);
    }

    public List<Payment> getPaymentsByPaymentBankAccountId(String paymentBankAccountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<PaymentRecord> paymentRecords = dslContext
            .select()
            .from(PAYMENT)
            .where(PAYMENT.PAYMENT_BANK_ACCOUNT_ID.eq(paymentBankAccountId))
            .and(PAYMENT.TENANT_ID.eq(tenantId))
            .fetchInto(PaymentRecord.class);
        return paymentRecordMapper.recordsToPayments(paymentRecords);
    }

    public void updatePaymentMetadata(DSLContext transactionContext, String tenantId, String paymentId, PaymentMetadata paymentMetadata) {
        int count = transactionContext
            .update(PAYMENT)
            .set(PAYMENT.METADATA, paymentRecordMapper.serializePaymentMetadata(paymentMetadata))
            .where(PAYMENT.TENANT_ID.eq(tenantId))
            .and(PAYMENT.PAYMENT_ID.eq(paymentId))
            .execute();

        if (count != 1) {
            throw new ServiceFailureException(
                String.format(
                    "Failed to update payment metadata for paymentId %s in tenant %s. Expected 1 row to be updated, but got %d.",
                    paymentId,
                    tenantId,
                    count
                )
            );
        }
    }
}
