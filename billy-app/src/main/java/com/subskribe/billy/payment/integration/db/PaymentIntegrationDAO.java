package com.subskribe.billy.payment.integration.db;

import static com.subskribe.billy.jooq.default_schema.tables.PaymentStripeConnectIntegration.PAYMENT_STRIPE_CONNECT_INTEGRATION;

import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.PaymentStripeConnectIntegrationRecord;
import com.subskribe.billy.payment.integration.mapper.PaymentIntegrationMapper;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class PaymentIntegrationDAO {

    private final TenantIdProvider tenantIdProvider;

    private final PaymentIntegrationMapper mapper;

    private final DSLContext dslContext;

    @Inject
    public PaymentIntegrationDAO(TenantIdProvider tenantIdProvider, DSLContext dslContext) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContext = dslContext;
        mapper = Mappers.getMapper(PaymentIntegrationMapper.class);
    }

    public Optional<PaymentStripeConnectIntegration> getStripeConnectIntegrationByTenantId(String tenantId) {
        PaymentStripeConnectIntegrationRecord record = dslContext
            .select()
            .from(PAYMENT_STRIPE_CONNECT_INTEGRATION)
            .where(PAYMENT_STRIPE_CONNECT_INTEGRATION.TENANT_ID.eq(tenantId))
            .and(PAYMENT_STRIPE_CONNECT_INTEGRATION.IS_DELETED.isFalse())
            .fetchOneInto(PaymentStripeConnectIntegrationRecord.class);

        if (record == null) {
            return Optional.empty();
        }

        PaymentStripeConnectIntegration result = mapper.fromRecord(record);
        return Optional.of(result);
    }

    public List<PaymentStripeConnectIntegration> getCompletedStripeConnectIntegrationsByTenantId(String tenantId) {
        List<PaymentStripeConnectIntegrationRecord> records = dslContext
            .select()
            .from(PAYMENT_STRIPE_CONNECT_INTEGRATION)
            .where(PAYMENT_STRIPE_CONNECT_INTEGRATION.TENANT_ID.eq(tenantId))
            .and(PAYMENT_STRIPE_CONNECT_INTEGRATION.IS_DELETED.isFalse())
            .and(PAYMENT_STRIPE_CONNECT_INTEGRATION.IS_COMPLETED.isTrue())
            .fetchInto(PaymentStripeConnectIntegrationRecord.class);

        if (CollectionUtils.isEmpty(records)) {
            return List.of();
        }

        return mapper.fromRecords(records);
    }

    public Optional<PaymentStripeConnectIntegration> getStripeConnectIntegrationByIntegrationId(String integrationId) {
        PaymentStripeConnectIntegrationRecord record = dslContext
            .select()
            .from(PAYMENT_STRIPE_CONNECT_INTEGRATION)
            .where(PAYMENT_STRIPE_CONNECT_INTEGRATION.INTEGRATION_ID.eq(integrationId))
            .and(PAYMENT_STRIPE_CONNECT_INTEGRATION.IS_DELETED.isFalse())
            .fetchOneInto(PaymentStripeConnectIntegrationRecord.class);

        if (record == null) {
            return Optional.empty();
        }

        PaymentStripeConnectIntegration result = mapper.fromRecord(record);
        return Optional.of(result);
    }

    public PaymentStripeConnectIntegration deleteCurrentStripePaymentIntegrationForTenant(Configuration configuration) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        Optional<PaymentStripeConnectIntegration> currentStripePaymentIntegration = getStripeConnectIntegrationByTenantId(tenantId);
        if (currentStripePaymentIntegration.isEmpty()) {
            throw new IllegalArgumentException("No stripe integration found.");
        }

        DSLContext dslContext = DSL.using(configuration);
        PaymentStripeConnectIntegrationRecord record = dslContext
            .update(PAYMENT_STRIPE_CONNECT_INTEGRATION)
            .set(PAYMENT_STRIPE_CONNECT_INTEGRATION.IS_DELETED, true)
            .where(PAYMENT_STRIPE_CONNECT_INTEGRATION.TENANT_ID.eq(tenantId))
            .and(PAYMENT_STRIPE_CONNECT_INTEGRATION.IS_DELETED.isFalse())
            .returning()
            .fetchOne();

        return mapper.fromRecord(record);
    }

    public PaymentStripeConnectIntegration createStripePaymentIntegration(String integrationId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        Optional<PaymentStripeConnectIntegration> currentIntegration = getStripeConnectIntegrationByTenantId(tenantId);
        if (currentIntegration.isPresent()) {
            throw new IllegalStateException("Current tenant already has a stripe payment integration. Please delete the existing integration first.");
        }

        PaymentStripeConnectIntegrationRecord record = new PaymentStripeConnectIntegrationRecord();
        record.setTenantId(tenantId);
        record.setIntegrationId(integrationId);

        PaymentStripeConnectIntegrationRecord newRecord = dslContext
            .insertInto(PAYMENT_STRIPE_CONNECT_INTEGRATION)
            .set(record)
            .returning()
            .fetchOne();

        if (newRecord == null) {
            throw new ServiceFailureException("Failed to save stripe payment integration: " + tenantId);
        }

        return mapper.fromRecord(record);
    }

    public void markStripePaymentIntegrationAsCompleted(String integrationId, String stripeConnectAccountId) {
        Validator.validateNonNullArguments(integrationId, stripeConnectAccountId);
        dslContext
            .update(PAYMENT_STRIPE_CONNECT_INTEGRATION)
            .set(PAYMENT_STRIPE_CONNECT_INTEGRATION.IS_COMPLETED, true)
            .set(PAYMENT_STRIPE_CONNECT_INTEGRATION.CONNECT_ACCOUNT_ID, stripeConnectAccountId)
            .where(PAYMENT_STRIPE_CONNECT_INTEGRATION.INTEGRATION_ID.eq(integrationId))
            .execute();
    }
}
