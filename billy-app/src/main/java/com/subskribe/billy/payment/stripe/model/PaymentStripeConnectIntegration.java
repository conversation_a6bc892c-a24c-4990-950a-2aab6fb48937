package com.subskribe.billy.payment.stripe.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.Objects;
import java.util.UUID;
import org.apache.commons.lang3.BooleanUtils;

@GraphQLName("PaymentStripeConnectIntegration")
public class PaymentStripeConnectIntegration {

    @JsonIgnore
    private UUID id;

    @JsonProperty
    private String tenantId;

    @JsonProperty
    @GraphQLField
    private String entityId;

    @JsonProperty
    @GraphQLField
    private String ledgerAccountId;

    @JsonProperty
    private String integrationId;

    @JsonProperty
    @GraphQLField
    private String connectAccountId;

    @JsonProperty
    @GraphQLField
    @GraphQLNonNull
    private Boolean isCompleted;

    @JsonProperty
    @GraphQLField
    @GraphQLNonNull
    private Boolean isDeleted;

    public PaymentStripeConnectIntegration() {}

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getLedgerAccountId() {
        return ledgerAccountId;
    }

    public void setLedgerAccountId(String ledgerAccountId) {
        this.ledgerAccountId = ledgerAccountId;
    }

    public String getIntegrationId() {
        return integrationId;
    }

    public void setIntegrationId(String integrationId) {
        this.integrationId = integrationId;
    }

    public String getConnectAccountId() {
        return connectAccountId;
    }

    public void setConnectAccountId(String connectAccountId) {
        this.connectAccountId = connectAccountId;
    }

    public Boolean getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(Boolean isCompleted) {
        this.isCompleted = isCompleted;
    }

    public boolean getIsDeleted() {
        return BooleanUtils.isTrue(isDeleted);
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public String toString() {
        return (
            "PaymentStripeConnectIntegration{" +
            "id=" +
            id +
            ", tenantId='" +
            tenantId +
            '\'' +
            ", entityId='" +
            entityId +
            '\'' +
            ", ledgerAccountId='" +
            ledgerAccountId +
            '\'' +
            ", integrationId='" +
            integrationId +
            '\'' +
            ", connectAccountId='" +
            connectAccountId +
            '\'' +
            ", isCompleted=" +
            isCompleted +
            ", isDeleted=" +
            isDeleted +
            '}'
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PaymentStripeConnectIntegration that = (PaymentStripeConnectIntegration) o;
        return (
            Objects.equals(id, that.id) &&
            Objects.equals(tenantId, that.tenantId) &&
            Objects.equals(entityId, that.entityId) &&
            Objects.equals(ledgerAccountId, that.ledgerAccountId) &&
            Objects.equals(integrationId, that.integrationId) &&
            Objects.equals(connectAccountId, that.connectAccountId) &&
            Objects.equals(isCompleted, that.isCompleted) &&
            Objects.equals(isDeleted, that.isDeleted)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, tenantId, entityId, ledgerAccountId, integrationId, connectAccountId, isCompleted, isDeleted);
    }
}
