package com.subskribe.billy.payment.stripe.webhook;

import static com.subskribe.billy.payment.stripe.service.StripeService.STRIPE_METADATA_TENANT_ID_KEY;

import com.stripe.exception.SignatureVerificationException;
import com.stripe.model.Event;
import com.stripe.model.MetadataStore;
import com.stripe.net.Webhook;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.aws.secretsmanager.model.SecretType;
import com.subskribe.billy.payment.stripe.model.StripeEventType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.webhook.IncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhookProcessor;
import com.subskribe.billy.shared.webhook.IncomingWebhookTenantLookupResult;
import com.subskribe.billy.shared.webhook.IncomingWebhookType;
import com.subskribe.billy.shared.webhook.WebhookActionUnsupportedException;
import java.util.Optional;
import java.util.Set;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;

public class StripeIncomingWebhookProcessor implements IncomingWebhookProcessor {

    public static final IncomingWebhookType WEBHOOK_TYPE = new IncomingWebhookType("stripe");

    private static final String STRIPE_SIGNATURE_HEADER = "Stripe-Signature";
    private static final Set<String> SUPPORTED_EVENT_TYPES = Set.of(
        StripeEventType.PAYMENT_INTENT_SUCCEEDED,
        StripeEventType.SETUP_INTENT_SUCCEEDED,
        StripeEventType.PAYMENT_INTENT_PAYMENT_FAILED,
        StripeEventType.PAYMENT_INTENT_CANCELLED
    );

    private static final Logger LOGGER = LoggerFactory.getLogger(StripeIncomingWebhookProcessor.class);

    private final StripeWebhookEventProcessor stripeWebhookEventProcessor;

    private final BillyConfiguration billyConfiguration;

    private final SecretsService secretsService;

    @Inject
    public StripeIncomingWebhookProcessor(
        StripeWebhookEventProcessor stripeWebhookEventProcessor,
        BillyConfiguration billyConfiguration,
        SecretsService secretsService
    ) {
        this.stripeWebhookEventProcessor = stripeWebhookEventProcessor;
        this.billyConfiguration = billyConfiguration;
        this.secretsService = secretsService;
    }

    @Override
    public IncomingWebhookTenantLookupResult findTenantId(IncomingWebhook incomingWebhook) {
        Event stripeEvent = Event.GSON.fromJson(incomingWebhook.getPayload(), Event.class);

        MetadataStore<?> stripeObject = (MetadataStore<?>) stripeEvent
            .getDataObjectDeserializer()
            .getObject()
            .orElseThrow(() ->
                new IllegalArgumentException(String.format("Stripe object not found for given Stripe event with id '%s'", stripeEvent.getId()))
            );

        String tenantId = stripeObject.getMetadata().get(STRIPE_METADATA_TENANT_ID_KEY);
        if (StringUtils.isBlank(tenantId)) {
            LOGGER.info("Stripe object does not have tenant id in metadata {}", stripeObject.getMetadata());
            return new IncomingWebhookTenantLookupResult(Optional.empty(), true);
        }

        return new IncomingWebhookTenantLookupResult(Optional.of(tenantId), true);
    }

    @Override
    public boolean validate(IncomingWebhook incomingWebhook) {
        if (billyConfiguration.isLocalOrCi()) {
            return true;
        }

        String header = incomingWebhook.getHeaders().getFirst(STRIPE_SIGNATURE_HEADER);

        if (StringUtils.isBlank(header)) {
            throw new IllegalArgumentException("Webhook is missing proper signature header.");
        }

        String endpointSecret = secretsService.getStripeSecret(SecretType.STRIPE_ENDPOINT_SECRET, false);
        if (StringUtils.isBlank(endpointSecret)) {
            LOGGER.error("Stripe webhook endpoint secret is not pushed to secret manager for this environment.");
            return false;
        }

        try {
            Event stripeEvent = Webhook.constructEvent(incomingWebhook.getPayload(), header, endpointSecret);
            if (stripeEvent != null && !SUPPORTED_EVENT_TYPES.contains(stripeEvent.getType())) {
                throw new WebhookActionUnsupportedException(
                    String.format("Received unsupported Stripe event with id: %s, type: %s", stripeEvent.getId(), stripeEvent.getType())
                );
            }
            return true;
        } catch (SignatureVerificationException e) {
            LOGGER.warn("Stripe webhook signature verification failed.", e);
            return false;
        }
    }

    @Override
    public void process(IncomingWebhook incomingWebhook) {
        Event event = Event.GSON.fromJson(incomingWebhook.getPayload(), Event.class);
        try {
            stripeWebhookEventProcessor.processWebhook(event);
        } catch (Exception e) {
            LOGGER.warn("Error processing Stripe webhook", e);
        }
    }

    @Override
    public IncomingWebhookType getWebhookType() {
        return WEBHOOK_TYPE;
    }
}
