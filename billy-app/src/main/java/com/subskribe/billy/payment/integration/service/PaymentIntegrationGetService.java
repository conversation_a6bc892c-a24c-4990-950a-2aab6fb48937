package com.subskribe.billy.payment.integration.service;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.payment.SupportedPaymentProvider;
import com.subskribe.billy.payment.integration.db.PaymentIntegrationDAO;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import com.subskribe.billy.validation.Validator;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;

public class PaymentIntegrationGetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentIntegrationGetService.class);

    private static final long INTEGRATION_CACHE_TTL_IN_MINUTES = 2;

    private final LoadingCache<String, Optional<PaymentStripeConnectIntegration>> integrationCache;

    private final TenantIdProvider tenantIdProvider;

    private final BillyConfiguration billyConfiguration;

    private final PaymentIntegrationDAO paymentIntegrationDAO;

    @Inject
    public PaymentIntegrationGetService(
        TenantIdProvider tenantIdProvider,
        BillyConfiguration billyConfiguration,
        PaymentIntegrationDAO paymentIntegrationDAO
    ) {
        this.tenantIdProvider = tenantIdProvider;
        this.billyConfiguration = billyConfiguration;
        this.paymentIntegrationDAO = paymentIntegrationDAO;
        integrationCache = CacheBuilder.newBuilder()
            .expireAfterWrite(INTEGRATION_CACHE_TTL_IN_MINUTES, TimeUnit.MINUTES)
            .build(getCacheLoaderForIntegrationCache());
    }

    private CacheLoader<String, Optional<PaymentStripeConnectIntegration>> getCacheLoaderForIntegrationCache() {
        return new CacheLoader<>() {
            @Override
            public Optional<PaymentStripeConnectIntegration> load(String key) {
                return getStripeConnectIntegrationFromDB(key);
            }
        };
    }

    public Optional<PaymentStripeConnectIntegration> getStripeConnectIntegration() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return paymentIntegrationDAO.getStripeConnectIntegrationByTenantId(tenantId);
    }

    public Optional<PaymentStripeConnectIntegration> getCompletedStripeConnectIntegration() {
        Optional<PaymentStripeConnectIntegration> integration = getStripeConnectIntegration();
        if (integration.isEmpty() || !integration.get().getIsCompleted()) {
            return Optional.empty();
        }

        return integration;
    }

    public List<PaymentStripeConnectIntegration> getCompletedStripeConnectIntegrations() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return paymentIntegrationDAO.getCompletedStripeConnectIntegrationsByTenantId(tenantId);
    }

    public Optional<PaymentStripeConnectIntegration> getStripeConnectIntegrationWithTenantId(String tenantId) {
        try {
            return integrationCache.get(tenantId);
        } catch (ExecutionException e) {
            LOGGER.error("Guava integration cache failed for tenant id: {}", tenantId, e);
            return getStripeConnectIntegrationFromDB(tenantId);
        }
    }

    private Optional<PaymentStripeConnectIntegration> getStripeConnectIntegrationFromDB(String tenantId) {
        return paymentIntegrationDAO.getStripeConnectIntegrationByTenantId(tenantId);
    }

    public PaymentStripeConnectIntegration getStripeConnectIntegrationThrowIfNotExists(String tenantId) {
        Optional<PaymentStripeConnectIntegration> integrationOptional = paymentIntegrationDAO.getStripeConnectIntegrationByTenantId(tenantId);
        return integrationOptional.orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PAYMENT_INTEGRATION, tenantId));
    }

    public boolean isPaymentIntegrationEnabled() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        return tenantScopedConfig.getStripeConfiguration().getEnabled();
    }

    public Optional<PaymentStripeConnectIntegration> getStripeConnectIntegrationByIntegrationId(String tenantId, UUID integrationId) {
        Validator.validateNonNullArguments(tenantId, integrationId);
        return paymentIntegrationDAO.getStripeConnectIntegrationByTenantId(tenantId).filter(i -> integrationId.equals(i.getId()));
    }

    @SuppressWarnings("SwitchStatementWithTooFewBranches")
    public boolean hasIntegration(SupportedPaymentProvider paymentProvider) {
        if (!isPaymentIntegrationEnabled()) {
            return false;
        }

        switch (paymentProvider) {
            case STRIPE -> {
                return getCompletedStripeConnectIntegration().isPresent();
            }
            default -> {
                return false;
            }
        }
    }

    public boolean hasIntegration() {
        return Arrays.stream(SupportedPaymentProvider.values()).anyMatch(this::hasIntegration);
    }
}
