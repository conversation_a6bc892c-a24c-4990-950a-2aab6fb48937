package com.subskribe.billy.payment.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.PaymentRetryConfigRecord;
import com.subskribe.billy.payment.model.IntervalBasedPaymentRetryPolicy;
import com.subskribe.billy.payment.model.PaymentRetryConfig;
import com.subskribe.billy.payment.model.PaymentRetryConfigInput;
import com.subskribe.billy.payment.model.PaymentRetryConfigOutput;
import com.subskribe.billy.payment.model.PaymentRetryPolicy;
import com.subskribe.billy.shared.mapper.BaseMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import org.jooq.JSONB;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
public interface PaymentRetryConfigMapper extends BaseMapper {
    ObjectMapper objectMapper = JacksonProvider.defaultMapper();

    @Mapping(source = "policy", target = "policy", qualifiedByName = "deserializePolicy")
    PaymentRetryConfig recordToModel(PaymentRetryConfigRecord record);

    @Mapping(source = "policy", target = "policy", qualifiedByName = "serializePolicy")
    PaymentRetryConfigRecord modelToRecord(PaymentRetryConfig model);

    @Mapping(target = "status", constant = "LATEST")
    @Mapping(target = "isDeleted", constant = "false")
    PaymentRetryConfig inputToModel(PaymentRetryConfigInput input);

    PaymentRetryConfigOutput modelToOutput(PaymentRetryConfig model);

    @Named("deserializePolicy")
    default PaymentRetryPolicy deserializePolicy(JSONB policy) {
        try {
            String type = objectMapper.readTree(policy.data()).get("type").asText();
            PaymentRetryPolicy.PaymentRetryPolicyType policyType = PaymentRetryPolicy.PaymentRetryPolicyType.valueOf(type);

            switch (policyType) {
                case INTERVAL_BASED:
                    return objectMapper.readValue(policy.data(), IntervalBasedPaymentRetryPolicy.class);
                default:
                    throw new ServiceFailureException("Unsupported PaymentRetryPolicyType: " + type);
            }
        } catch (Exception e) {
            throw new ServiceFailureException("Failed to deserialize PaymentRetryPolicy", e);
        }
    }

    @Named("serializePolicy")
    default JSONB serializePolicy(PaymentRetryPolicy policy) {
        try {
            return JSONB.valueOf(objectMapper.writeValueAsString(policy));
        } catch (Exception e) {
            throw new ServiceFailureException("Failed to serialize PaymentRetryPolicy", e);
        }
    }
}
