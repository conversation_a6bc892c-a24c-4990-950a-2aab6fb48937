package com.subskribe.billy.payment.task;

import com.subskribe.billy.payment.services.PaymentOrchestrator;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.shared.task.queue.model.ImmutableProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskResult;
import com.subskribe.billy.shared.task.queue.model.ProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.processor.TaskProcessor;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobModule;
import com.subskribe.billy.tenantjob.model.TenantJobObjectModel;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import javax.inject.Inject;

@SuppressWarnings("PMD.UnusedPrivateField")
public class PaymentRetryQueuedTaskProcessor implements TaskProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentRetryQueuedTaskProcessor.class);

    private static final UncheckedObjectMapper OBJECT_MAPPER = UncheckedObjectMapper.defaultMapper();

    public static final String LAST_PAYMENT_ATTEMPT_ID_METADATA_KEY = "lastPaymentAttemptId";

    public static final String AUTOMATED_PAYMENT_RETRY_NUMBER_METADATA_KEY = "automatedPaymentRetryNumber";

    public static final String AUTOMATED_PAYMENT_RETRY_ATTEMPT_NAME = "automatedPaymentRetryAttemptName";

    private final TenantIdProvider tenantIdProvider;

    private final PaymentOrchestrator paymentOrchestrator;

    @Inject
    public PaymentRetryQueuedTaskProcessor(TenantIdProvider tenantIdProvider, PaymentOrchestrator paymentOrchestrator) {
        this.tenantIdProvider = tenantIdProvider;
        this.paymentOrchestrator = paymentOrchestrator;
    }

    public record PaymentRetryResult(RETRY_ACTION retryAction, String detail) {}

    public enum RETRY_ACTION {
        RETRIED,
        SKIPPED,
    }

    @Override
    public TaskResult process(QueuedTask task) {
        String tenantId = task.getTenantId();

        return TenantContextInjector.callInTenantContext(tenantId, tenantIdProvider, () -> {
            TenantJob tenantJob = OBJECT_MAPPER.readValue(task.getTaskData(), TenantJob.class);
            TenantJobType jobType = tenantJob.getJobType();
            TenantJobObjectModel jobObjectModel = tenantJob.getObjectModel();

            if (jobType != TenantJobType.PAYMENT_RETRY) {
                String failureReason = String.format(
                    "job id %s, Invalid job type for PaymentRetryQueuedTaskProcessor: expected %s, got %s",
                    tenantJob.getJobId(),
                    TenantJobType.PAYMENT_RETRY,
                    jobType
                );
                LOGGER.error(failureReason);
                return ImmutableTaskResult.builder().successful(false).retryable(false).failureReason(failureReason).build();
            }

            if (jobObjectModel != TenantJobObjectModel.PAYMENT) {
                String failureReason = String.format(
                    "job id %s, Invalid job object model for PaymentRetryQueuedTaskProcessor: expected %s, got %s",
                    tenantJob.getJobId(),
                    TenantJobObjectModel.PAYMENT,
                    jobObjectModel
                );
                LOGGER.error(failureReason);
                return ImmutableTaskResult.builder().successful(false).retryable(false).failureReason(failureReason).build();
            }

            try {
                LOGGER.info("Processing payment retry for job: {}", tenantJob.getJobId());

                String paymentId = tenantJob.getObjectId();
                Map<String, String> jobMetadata = tenantJob.getMetadata();

                String lastPaymentAttemptId = jobMetadata.get(LAST_PAYMENT_ATTEMPT_ID_METADATA_KEY);
                int automatedPaymentRetryNumber = Integer.parseInt(jobMetadata.get(AUTOMATED_PAYMENT_RETRY_NUMBER_METADATA_KEY));
                String attemptName = jobMetadata.get(AUTOMATED_PAYMENT_RETRY_ATTEMPT_NAME);

                LOGGER.info(
                    "Payment retry for job id {} : paymentId={}, lastPaymentAttemptId={}",
                    tenantJob.getJobId(),
                    paymentId,
                    lastPaymentAttemptId
                );

                PaymentRetryResult result = paymentOrchestrator.attemptRetry(
                    paymentId,
                    lastPaymentAttemptId,
                    automatedPaymentRetryNumber,
                    attemptName
                );

                Map<String, String> resultMetadata = Map.of("result", OBJECT_MAPPER.writeValueAsString(result));

                return ImmutableTaskResult.builder().successful(true).metadata(resultMetadata).build();
            } catch (Exception e) { // TODO : catch more granular exceptions as needed
                String failureReason = String.format("job id %s, Failed to process payment retry", tenantJob.getJobId());
                LOGGER.error(failureReason, e);
                return ImmutableTaskResult.builder().successful(false).retryable(false).failureReason(failureReason).build();
            }
        });
    }

    @Override
    public ProcessorConfiguration getConfiguration() {
        return ImmutableProcessorConfiguration.builder()
            .taskModule(new TaskModule(TenantJobModule.PAYMENT.name()))
            .taskTimeout(Duration.of(5, ChronoUnit.MINUTES)) // Assuming a retry attempt takes 2 minutes worst case and with locking in place for any concurrent manual action, we allow 5 minutes for the job to complete
            .build();
    }
}
