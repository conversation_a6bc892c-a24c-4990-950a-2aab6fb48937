package com.subskribe.billy.payment.stripe.webhook;

import com.google.common.annotations.VisibleForTesting;
import com.stripe.model.Event;
import com.stripe.model.MetadataStore;
import com.stripe.model.PaymentIntent;
import com.stripe.model.SetupIntent;
import com.stripe.model.StripeObject;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.payment.model.FailedPaymentAttemptData;
import com.subskribe.billy.payment.services.PaymentOrchestrator;
import com.subskribe.billy.payment.stripe.model.StripeEventType;
import com.subskribe.billy.payment.stripe.service.StripeService;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.shared.webhook.IncomingWebhookType;
import com.subskribe.billy.tenant.TenantIdProvider;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;

public class StripeWebhookEventProcessor {

    public static final IncomingWebhookType WEBHOOK_TYPE = new IncomingWebhookType("stripe");

    private static final Logger LOGGER = LoggerFactory.getLogger(StripeWebhookEventProcessor.class);

    private final StripeService stripeService;

    private final BillyConfiguration billyConfiguration;

    private final PaymentOrchestrator paymentOrchestrator;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public StripeWebhookEventProcessor(
        StripeService stripeService,
        BillyConfiguration billyConfiguration,
        PaymentOrchestrator paymentOrchestrator,
        TenantIdProvider tenantIdProvider
    ) {
        this.stripeService = stripeService;
        this.billyConfiguration = billyConfiguration;
        this.paymentOrchestrator = paymentOrchestrator;
        this.tenantIdProvider = tenantIdProvider;
    }

    public void processWebhook(Event stripeEvent) {
        StripeObject stripeObject = stripeEvent
            .getDataObjectDeserializer()
            .getObject()
            .orElseThrow(() ->
                new IllegalArgumentException(String.format("Stripe object not found for given Stripe event with id '%s'", stripeEvent.getId()))
            );

        switch (stripeEvent.getType()) {
            case StripeEventType.PAYMENT_INTENT_SUCCEEDED -> processSucceededPaymentIntentEvent(stripeObject);
            case StripeEventType.SETUP_INTENT_SUCCEEDED -> processSucceededSetupIntentEvent(stripeObject);
            case StripeEventType.PAYMENT_INTENT_PAYMENT_FAILED, StripeEventType.PAYMENT_INTENT_CANCELLED -> processFailedPaymentIntentEvent(
                stripeObject
            );
            default -> LOGGER.info("Webhook processing for stripe event type {} not supported yet", stripeEvent.getType());
        }
    }

    private void processSucceededPaymentIntentEvent(StripeObject stripeObject) {
        PaymentIntent paymentIntent = (PaymentIntent) stripeObject;
        if (isObjectForDifferentEnvironment(paymentIntent)) {
            return;
        }
        stripeService.completeSuccessfulPaymentIntent(paymentIntent);
    }

    private void processFailedPaymentIntentEvent(StripeObject stripeObject) {
        PaymentIntent paymentIntent = (PaymentIntent) stripeObject;
        if (isObjectForDifferentEnvironment(paymentIntent)) {
            return;
        }

        String tenantId = stripeService.getTenantIdFromPaymentIntent(paymentIntent);
        if (StringUtils.isBlank(tenantId)) {
            LOGGER.info("Payment intent {} does not have tenant id in metadata", paymentIntent.getId());
            return;
        }

        TenantContextInjector.runInTenantContext(tenantId, tenantIdProvider, () -> {
            LOGGER.info("[StripeWebhookEventProcessor] Processing failed payment intent event for payment intent {}", paymentIntent.getId());
            FailedPaymentAttemptData failedPaymentAttemptData = stripeService.completeFailedPaymentIntent(paymentIntent);
            paymentOrchestrator.triggerPaymentAttemptFailureActions(failedPaymentAttemptData, true);
        });
    }

    private void processSucceededSetupIntentEvent(StripeObject stripeObject) {
        SetupIntent setupIntent = (SetupIntent) stripeObject;
        if (isObjectForDifferentEnvironment(setupIntent)) {
            return;
        }
        stripeService.completeSuccessfulSetupIntent(setupIntent);
    }

    @VisibleForTesting
    boolean isObjectForDifferentEnvironment(MetadataStore<?> stripeObject) {
        if (stripeObject.getMetadata().containsKey(StripeService.STRIPE_METADATA_ENV_NAME)) {
            return !stripeObject.getMetadata().get(StripeService.STRIPE_METADATA_ENV_NAME).equals(billyConfiguration.getEnvName());
        }

        return false;
    }
}
