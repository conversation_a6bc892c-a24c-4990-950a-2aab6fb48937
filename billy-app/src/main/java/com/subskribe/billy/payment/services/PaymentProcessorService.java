package com.subskribe.billy.payment.services;

import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.payment.model.PaymentProcessorJobUnit;
import com.subskribe.billy.payment.processor.service.PaymentProcessorJobQueueService;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;

public class PaymentProcessorService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentProcessorService.class);
    private final InvoiceRetrievalService invoiceRetrievalService;

    private final InvoiceSettlementService invoiceSettlementService;

    private final PaymentProcessorJobQueueService paymentProcessorJobQueueService;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public PaymentProcessorService(
        InvoiceRetrievalService invoiceRetrievalService,
        InvoiceSettlementService invoiceSettlementService,
        PaymentProcessorJobQueueService paymentProcessorJobQueueService,
        TenantIdProvider tenantIdProvider
    ) {
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.invoiceSettlementService = invoiceSettlementService;
        this.paymentProcessorJobQueueService = paymentProcessorJobQueueService;
        this.tenantIdProvider = tenantIdProvider;
    }

    public void triggerAutomaticPaymentForDueInvoices(String accountId) {
        List<Invoice> invoicesDueForPayment = invoiceRetrievalService
            .getInvoicesForAccount(accountId, InvoiceStatus.POSTED)
            .stream()
            .filter(InvoiceRetrievalService::isInvoiceDueForPayment)
            .toList();
        if (CollectionUtils.isNotEmpty(invoicesDueForPayment)) {
            // Check if there is an ongoing payment
            invoicesDueForPayment.forEach(this::processInvoicesDueForPayment);
        }
    }

    private void processInvoicesDueForPayment(Invoice invoice) {
        // TODO : fail existing settlement application and payment if payment method is replaced or re-enabled
        boolean ongoingPayment = invoiceSettlementService.checkIfOngoingPaymentExistsForInvoice(invoice.getInvoiceNumber());
        if (ongoingPayment) {
            LOGGER.info("Ongoing payment exists for invoice: {}, skipping processing", invoice.getInvoiceNumber().getNumber());
            return;
        }

        Optional<PaymentProcessorJobUnit> jobUnit = paymentProcessorJobQueueService.getPaymentProcessorJobUnitByInvoiceNumber(
            tenantIdProvider.provideTenantIdString(),
            invoice.getInvoiceNumber()
        );
        if (jobUnit.isEmpty()) {
            paymentProcessorJobQueueService.addPaymentProcessorJobUnit(
                invoice.getInvoiceNumber().getNumber(),
                tenantIdProvider.provideTenantIdString()
            );
        } else {
            paymentProcessorJobQueueService.updateReadyJobUnits(List.of(jobUnit.get()), tenantIdProvider.provideTenantIdString());
        }
    }
}
