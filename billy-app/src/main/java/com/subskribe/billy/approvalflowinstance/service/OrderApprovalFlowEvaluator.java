package com.subskribe.billy.approvalflowinstance.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.ImmutableMap;
import com.subskribe.billy.account.model.AccountStub;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.approvalflow.model.ApprovalRuleConditions;
import com.subskribe.billy.approvalflow.model.ApprovalTransitionRule;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowContext;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstance;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceGroup;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceState;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceStatus;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceWorkflowStatus;
import com.subskribe.billy.approvalflowinstance.model.ImmutableApprovalFlowContext;
import com.subskribe.billy.approvalflowinstance.model.UserActionTime;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderDiscountService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderResellerService;
import com.subskribe.billy.order.services.OrderServiceHelper;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.entitycache.EntityCache;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.TenantDiscount;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.BillyDuration;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.template.model.CustomTemplateUpdateOnOrder;
import com.subskribe.billy.template.model.DocumentCustomContent;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.model.OrderTerms;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderGetService;
import com.subskribe.billy.template.services.DocumentCustomContentGetService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.OrderTermsService;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.validation.Validator;
import io.github.jamsesso.jsonlogic.JsonLogic;
import io.github.jamsesso.jsonlogic.JsonLogicException;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.json.JSONObject;

@SuppressWarnings("ALL")
public class OrderApprovalFlowEvaluator {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderApprovalFlowEvaluator.class);

    private final OrderGetService orderGetService;
    private final MetricsService metricsService;
    private final DocumentCustomContentGetService documentCustomContentGetService;
    private final ProductCatalogGetService productCatalogGetService;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final OrderTermsService orderTermsService;
    private final CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService;
    private final CustomFieldService customFieldService;
    private final CompositeOrderGetService compositeOrderGetService;
    private final OpportunityGetService opportunityGetService;
    private final AccountGetService accountGetService;
    private final OrderResellerService orderResellerService;

    @Inject
    public OrderApprovalFlowEvaluator(
        OrderGetService orderGetService,
        MetricsService metricsService,
        DocumentCustomContentGetService documentCustomContentGetService,
        ProductCatalogGetService productCatalogGetService,
        DocumentTemplateGetService documentTemplateGetService,
        OrderTermsService orderTermsService,
        CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService,
        CustomFieldService customFieldService,
        CompositeOrderGetService compositeOrderGetService,
        OpportunityGetService opportunityGetService,
        AccountGetService accountGetService,
        OrderResellerService orderResellerService
    ) {
        this.orderGetService = orderGetService;
        this.metricsService = metricsService;
        this.documentCustomContentGetService = documentCustomContentGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.documentTemplateGetService = documentTemplateGetService;
        this.orderTermsService = orderTermsService;
        this.customTemplateUpdatedOnOrderGetService = customTemplateUpdatedOnOrderGetService;
        this.customFieldService = customFieldService;
        this.compositeOrderGetService = compositeOrderGetService;
        this.opportunityGetService = opportunityGetService;
        this.accountGetService = accountGetService;
        this.orderResellerService = orderResellerService;
    }

    void evaluateOrderApprovalFlowStatus(ApprovalFlowInstanceGroup approvalFlowInstanceGroup, TimeZone timeZone) {
        Validator.checkNonNullInternal(orderGetService, "OrderGetService cannot be null");
        Validator.checkNonNullInternal(approvalFlowInstanceGroup, "ApprovalFlowInstanceGroup cannot be null");
        Validator.checkArgumentNotBlankInternal(approvalFlowInstanceGroup.getOrderId(), "ApprovalFlowInstanceGroup's orderId cannot be empty");

        var order = orderGetService.getOrderByOrderId(approvalFlowInstanceGroup.getOrderId());

        Metrics orderMetrics = metricsService.getOrderMetrics(order, order.getStartDate());
        Map<String, Metrics> orderLineMetrics = ImmutableMap.copyOf(metricsService.getMetricsForAllOrderLines(order));
        BigDecimal deltaArrPercent = OrderServiceHelper.getDeltaArrPercent(order, orderMetrics, metricsService);

        evaluateOrderApprovalFlowStatus(
            order,
            approvalFlowInstanceGroup,
            orderMetrics,
            deltaArrPercent,
            orderLineMetrics,
            timeZone,
            Optional.empty()
        );
    }

    public static Metrics getBaseSubscriptionMetrics(MetricsService metricsService, Order order) {
        String baseSubscriptionId = null;
        if (order.getOrderType() == OrderType.AMENDMENT || order.getOrderType() == OrderType.CANCEL) {
            baseSubscriptionId = order.getExternalSubscriptionId();
        } else if (order.getOrderType() == OrderType.RENEWAL) {
            baseSubscriptionId = order.getRenewalForSubscriptionId();
        } else if (order.getOrderType() == OrderType.RESTRUCTURE) {
            baseSubscriptionId = order.getRestructureForSubscriptionId();
        }

        Instant targetDate = order.getStartDate().minusSeconds(1);
        return baseSubscriptionId == null ? null : metricsService.getSubscriptionMetrics(baseSubscriptionId, targetDate);
    }

    void evaluateOrderApprovalFlowStatus(
        Order order,
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup,
        Metrics orderMetrics,
        BigDecimal deltaArrPercent,
        Map<String, Metrics> orderLineMetrics,
        TimeZone timeZone,
        Optional<User> adminByPassByUser
    ) {
        Validator.checkNonNullInternal(order, "Order cannot be null");
        Validator.checkNonNullInternal(approvalFlowInstanceGroup, "ApprovalFlowInstanceGroup cannot be null");

        if (CollectionUtils.isEmpty(approvalFlowInstanceGroup.getApprovalFlowInstances())) {
            // No ApprovalFlows are present. This will be auto approved.
            LOGGER.info("No approval flows found and hence order is auto approved. OrderId = {}", order.getOrderId());

            approvalFlowInstanceGroup.setApprovalStatus(ApprovalFlowInstanceStatus.APPROVED);
            approvalFlowInstanceGroup.setWorkflowStatus(ApprovalFlowInstanceWorkflowStatus.COMPLETED);
            return;
        }

        // remove empty states caused by replacing roles with user/group
        ApprovalFlowGraphTraverser.updateApprovalFlowInstanceGroupForEmptyStates(approvalFlowInstanceGroup);

        EntityCache<String, Plan> planEntityCache = EntityCache.of(productCatalogGetService::getPlan);
        EntityCache<String, Product> productEntityCache = EntityCache.of(productCatalogGetService::getProduct);

        String customTerms = getCustomTerms(order);
        List<DocumentTemplate> predefinedTermsOnOrder = getAllPredefinedTermsOnOrder(documentTemplateGetService, orderTermsService, order);
        Set<String> modifiedPredefinedTermsOnOrder = getUpdatedPredefinedTermsOnOrder(customTemplateUpdatedOnOrderGetService, order.getOrderId())
            .stream()
            .map(CustomTemplateUpdateOnOrder::getTemplateId)
            .collect(Collectors.toSet());

        List<String> planIdsOnOrder = order
            .getLineItems()
            .stream()
            .filter(orderLineItem -> orderLineItem.getAction() != ActionType.NONE)
            .map(OrderLineItem::getPlanId)
            .collect(Collectors.toList());

        List<String> chargeIdsOnOrder = order
            .getLineItems()
            .stream()
            .filter(orderLineItem -> orderLineItem.getAction() != ActionType.NONE)
            .map(OrderLineItem::getChargeId)
            .collect(Collectors.toList());

        List<String> productIdsOnOrder = order
            .getLineItems()
            .stream()
            .filter(orderLineItem -> orderLineItem.getAction() != ActionType.NONE)
            .map(orderLineItem -> planEntityCache.get(orderLineItem.getPlanId()).getProductId())
            .collect(Collectors.toList());

        Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
        CustomField customFieldsOnOrder = order.getCustomFields();
        CustomField customFieldOnAccount = customFieldService.getCustomFields(CustomFieldParentType.ACCOUNT, order.getAccountId());

        Map<String, CustomField> customFieldOnOrderLinesMap = getCustomFieldOnOrderLinesMap(order);

        Map<String, CustomField> customFieldsOnPlansMap = getCustomFieldsOnPlansMap(chargeMap);

        Map<String, CustomField> customFieldsOnChargesMap = getCustomFieldsOnChargesMap(chargeMap);

        String opportunityId = getOpportunityId(order);

        boolean isReseller = orderResellerService.getResellerAccount(order).isPresent();

        CustomField customFieldsOnOpportunity = getCustomFieldsOnOpportunity(opportunityId);

        ApprovalFlowContext approvalFlowContext = ImmutableApprovalFlowContext.builder()
            .predefinedTermsOnOrder(predefinedTermsOnOrder)
            .modifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder)
            .customTerms(customTerms)
            .timeZone(timeZone)
            .opportunityId(opportunityId)
            .chargeMap(chargeMap)
            .customFieldOnAccount(customFieldOnAccount)
            .customFieldsOnOrder(customFieldsOnOrder)
            .customFieldsOnOpportunity(customFieldsOnOpportunity)
            .customFieldOnOrderLinesMap(customFieldOnOrderLinesMap)
            .customFieldsOnPlansMap(customFieldsOnPlansMap)
            .customFieldsOnChargesMap(customFieldsOnChargesMap)
            .isReseller(isReseller)
            .planIds(planIdsOnOrder)
            .chargeIds(chargeIdsOnOrder)
            .productIds(productIdsOnOrder)
            .build();

        // evaluate each of the approvalFlow with the current order
        for (var approvalFlowInstance : approvalFlowInstanceGroup.getApprovalFlowInstances()) {
            updateApprovalFlowInstanceStatus(
                order,
                approvalFlowInstance,
                orderMetrics,
                deltaArrPercent,
                new HashMap<>(orderLineMetrics),
                planEntityCache,
                productEntityCache,
                approvalFlowContext
            );
        }

        // update group status based on individual workflow statuses
        updateGroupStatusFromIndividualInstances(approvalFlowInstanceGroup, adminByPassByUser);
    }

    private static void updateApprovalFlowInstanceStatus(
        Order order,
        ApprovalFlowInstance approvalFlowInstance,
        Metrics orderMetrics,
        BigDecimal deltaArrPercent,
        Map<String, Metrics> orderLineMetrics,
        EntityCache<String, Plan> planEntityCache,
        EntityCache<String, Product> productEntityCache,
        ApprovalFlowContext approvalFlowContext
    ) {
        // Ignore any Approval flow instances which are already set to error status
        if (approvalFlowInstance.getStatus() != null && ApprovalFlowInstanceStatus.getErrorStates().contains(approvalFlowInstance.getStatus())) {
            return;
        }

        Map<String, ApprovalTransitionRule> transitionRuleByStartingStateMap = ApprovalFlowGraphTraverser.transitionRuleByStartingStateMap(
            approvalFlowInstance.getApprovalFlowData()
        );

        // check if the main rule is satisfied
        ApprovalRuleConditions approvalRuleConditions = ApprovalFlowUtils.getRuleConditions(transitionRuleByStartingStateMap);

        boolean approvalFlowRuleApplies = checkApprovalFlowConditions(
            order,
            approvalRuleConditions,
            orderMetrics,
            deltaArrPercent,
            orderLineMetrics,
            planEntityCache,
            productEntityCache,
            approvalFlowContext
        );
        if (!approvalFlowRuleApplies) {
            approvalFlowInstance.setStatus(ApprovalFlowInstanceStatus.NOT_APPLICABLE);
            approvalFlowInstance.getApprovalFlowData().setStatus(ApprovalFlowInstanceStatus.NOT_APPLICABLE);
            approvalFlowInstance
                .getApprovalFlowData()
                .getStates()
                .forEach(approvalFlowInstanceState -> approvalFlowInstanceState.setStatus(ApprovalFlowInstanceStatus.NOT_APPLICABLE));
            return;
        }

        // todo: Skipping the rules evaluator for each stage and just checking the status instead for V1
        for (ApprovalFlowInstanceState approvalFlowInstanceState : ApprovalFlowGraphTraverser.traverseStateGraph(
            approvalFlowInstance.getApprovalFlowData()
        )) {
            if (approvalFlowInstanceState.getStatus() == null) {
                approvalFlowInstanceState.setStatus(ApprovalFlowInstanceStatus.AWAITING_APPROVAL);
                approvalFlowInstance.setStatus(ApprovalFlowInstanceStatus.AWAITING_APPROVAL);
                approvalFlowInstance.setActiveStateId(approvalFlowInstanceState.getApprovalStateId());
                approvalFlowInstance.getApprovalFlowData().setStatus(ApprovalFlowInstanceStatus.AWAITING_APPROVAL);
                approvalFlowInstance.setLastApprovedOn(Instant.now());
                return;
            }

            switch (approvalFlowInstanceState.getStatus()) {
                case INACTIVE:
                case AWAITING_APPROVAL:
                    approvalFlowInstanceState.setStatus(ApprovalFlowInstanceStatus.AWAITING_APPROVAL);
                    approvalFlowInstance.setStatus(ApprovalFlowInstanceStatus.AWAITING_APPROVAL);
                    approvalFlowInstance.setActiveStateId(approvalFlowInstanceState.getApprovalStateId());
                    approvalFlowInstance.getApprovalFlowData().setStatus(ApprovalFlowInstanceStatus.AWAITING_APPROVAL);
                    approvalFlowInstance.setLastApprovedOn(Instant.now());
                    return;
                case REJECTED:
                    approvalFlowInstance.setStatus(ApprovalFlowInstanceStatus.REJECTED);
                    approvalFlowInstance.getApprovalFlowData().setStatus(ApprovalFlowInstanceStatus.REJECTED);
                    approvalFlowInstance.setActiveStateId(null);
                    return;
                case NEEDS_APPROVAL:
                    // This block has been added to support smart approval processing in the preview functionality
                    approvalFlowInstance.setStatus(ApprovalFlowInstanceStatus.NEEDS_APPROVAL);
                    approvalFlowInstance.setActiveStateId(approvalFlowInstanceState.getApprovalStateId());
                    approvalFlowInstance.getApprovalFlowData().setStatus(ApprovalFlowInstanceStatus.NEEDS_APPROVAL);
                    approvalFlowInstance.setLastApprovedOn(Instant.now());
                    return;
                default:
                // do nothing
            }
        }

        // all the transition rules have been traversed and all states are approved. Time to set the instance to approve.
        approvalFlowInstance.setActiveStateId(null);
        approvalFlowInstance.setStatus(ApprovalFlowInstanceStatus.APPROVED);
        approvalFlowInstance.getApprovalFlowData().setStatus(ApprovalFlowInstanceStatus.APPROVED);
    }

    private static List<CustomTemplateUpdateOnOrder> getUpdatedPredefinedTermsOnOrder(
        CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService,
        String orderId
    ) {
        return StringUtils.isBlank(orderId) ? List.of() : customTemplateUpdatedOnOrderGetService.getUpdatedTemplatesOnOrder(orderId);
    }

    private static List<DocumentTemplate> getAllPredefinedTermsOnOrder(
        DocumentTemplateGetService documentTemplateGetService,
        OrderTermsService orderTermsService,
        Order order
    ) {
        List<OrderTerms> orderTerms = orderTermsService.getOrderTermsByOrderId(order.getOrderId());
        List<Pair<String, Integer>> templateIdVersionPairs = orderTerms
            .stream()
            .map(orderTerm -> Pair.of(orderTerm.getTemplateGroupId(), orderTerm.getTemplateGroupVersion()))
            .collect(Collectors.toList());
        return documentTemplateGetService.getDocumentTemplatesByTemplateIdVersionPairs(templateIdVersionPairs);
    }

    public static boolean checkApprovalFlowConditions(
        Order order,
        ApprovalRuleConditions approvalRuleConditions,
        Metrics orderMetrics,
        BigDecimal deltaArrPercent,
        Map<String, Metrics> orderLineMetrics,
        EntityCache<String, Plan> planEntityCache,
        EntityCache<String, Product> productEntityCache,
        ApprovalFlowContext approvalFlowContext
    ) {
        String base64OrderCondition = approvalRuleConditions.getOrderCondition();
        JSONObject orderConditionJsonObject = ApprovalFlowUtils.convertBase64ConditionToJsonObject(base64OrderCondition);

        boolean orderLevelConditionCheck = checkIfOrderLevelConditionApplies(
            order,
            orderConditionJsonObject,
            orderMetrics,
            deltaArrPercent,
            approvalFlowContext
        );
        LOGGER.info("OrderId: {}, orderCondition: {}, conditionResult: {}", order.getOrderId(), orderConditionJsonObject, orderLevelConditionCheck);

        // short circuit if the order level condition is not satisfied
        if (!orderLevelConditionCheck) {
            return false;
        }

        String base64OrderLineCondition = approvalRuleConditions.getOrderLineCondition();
        JSONObject orderLineConditionJsonObject = ApprovalFlowUtils.convertBase64ConditionToJsonObject(base64OrderLineCondition);
        boolean orderLineConditionCheck = checkIfOrderLineConditionApplies(
            order,
            orderLineConditionJsonObject,
            orderLineMetrics,
            planEntityCache,
            productEntityCache,
            approvalFlowContext
        );

        LOGGER.info(
            "OrderId: {}, orderLineCondition: {}, conditionResult: {}",
            order.getOrderId(),
            orderLineConditionJsonObject,
            orderLineConditionCheck
        );
        return orderLineConditionCheck;
    }

    // method is public for testing purposes
    static boolean checkIfOrderLineConditionApplies(
        Order order,
        JSONObject orderLineCondition,
        Map<String, Metrics> orderLineMetrics,
        EntityCache<String, Plan> planEntityCache,
        EntityCache<String, Product> productEntityCache,
        ApprovalFlowContext approvalFlowContext
    ) {
        // A null condition is a valid condition and implies that it is always satisfied
        if (orderLineCondition == null) {
            return true;
        }

        Map<String, Object> values = new HashMap<>();
        List<OrderLineItem> orderLineItems = OrderServiceHelper.consolidateOrderLineItemsByBaseExternalSubscriptionChargeId(
            order.getLineItemsNetEffect(),
            orderLineMetrics
        );
        Map<String, Charge> chargeMap = approvalFlowContext.getChargeMap();
        for (var orderLine : orderLineItems) {
            BigDecimal lineItemDiscountPercent = OrderDiscountService.calculateOrderLineNetDiscountPercent(List.of(orderLine), chargeMap);

            // replace with line item values
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_DISCOUNT_VARIABLE, lineItemDiscountPercent);
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_ID_VARIABLE, orderLine.getChargeId());
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_QUANTITY_VARIABLE, orderLine.getQuantity());
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_AMOUNT_VARIABLE, orderLine.getAmount());
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE, orderLineMetrics.get(orderLine.getOrderLineId()).getEntryArr());
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_PLAN_ID_VARIABLE, orderLine.getPlanId());
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_HAS_OVERRIDE, orderLine.getListAmountBeforeOverride() != null);
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_HAS_ARR_OVERRIDE, orderLine.getArrOverride() != null);
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_TYPE_VARIABLE, chargeMap.get(orderLine.getChargeId()).getType().name());
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_IS_CREDITABLE_VARIABLE, chargeMap.get(orderLine.getChargeId()).isCreditable());
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_ACTION_TYPE_VARIABLE, orderLine.getAction().name());
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING, chargeMap.get(orderLine.getChargeId()).isCustom());
            if (orderLine.getListUnitPrice() != null) {
                values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, orderLine.getListUnitPrice());
            }

            String productId = planEntityCache.get(orderLine.getPlanId()).getProductId();
            values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_PRODUCT_ID_VARIABLE, productId);

            //check if product has product category associated
            ProductCategory productCategory = productEntityCache.get(productId).getProductCategory();
            if (Objects.nonNull(productCategory) && StringUtils.isNotEmpty(productCategory.getProductCategoryId())) {
                values.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_PRODUCT_CATEGORY_ID_VARIABLE, productCategory.getProductCategoryId());
            }

            // add orderLine custom fields
            addCustomFieldsAndValuesForOrderLineEvaluation(
                approvalFlowContext.getCustomFieldOnOrderLinesMap(),
                orderLine.getOrderLineId(),
                ApprovalFlowUtils.ORDER_LINE_CONDITION_ORDER_LINE_CUSTOM_FIELD_PREFIX,
                values
            );

            // add plan custom fields
            addCustomFieldsAndValuesForOrderLineEvaluation(
                approvalFlowContext.getCustomFieldsOnPlansMap(),
                orderLine.getPlanId(),
                ApprovalFlowUtils.ORDER_LINE_CONDITION_PLAN_CUSTOM_FIELD_PREFIX,
                values
            );

            // add charge custom fields
            addCustomFieldsAndValuesForOrderLineEvaluation(
                approvalFlowContext.getCustomFieldsOnChargesMap(),
                orderLine.getChargeId(),
                ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_CUSTOM_FIELD_PREFIX,
                values
            );

            if (performJsonLogicConditionEvaluation(orderLineCondition, values)) {
                // short circuit even if one orderLine satisfies the condition
                return true;
            }
        }

        return false;
    }

    private static void addCustomFieldsAndValuesForOrderLineEvaluation(
        Map<String, CustomField> customFieldMap,
        String key,
        String prefix,
        Map<String, Object> valuesMap
    ) {
        if (MapUtils.isNotEmpty(customFieldMap) && customFieldMap.containsKey(key)) {
            customFieldMap.get(key).getEntries().values().forEach(entry -> valuesMap.put(prefix + entry.getName(), entry.getValue()));
        }
    }

    static boolean checkIfOrderLevelConditionApplies(
        Order order,
        JSONObject orderCondition,
        Metrics orderMetrics,
        BigDecimal deltaArrPercent,
        ApprovalFlowContext approvalFlowContext
    ) {
        // A null condition is a valid condition and implies that it is always satisfied
        if (orderCondition == null) {
            return true;
        }

        Map<String, Charge> chargeMap = approvalFlowContext.getChargeMap();
        BigDecimal orderDiscountPercent = OrderDiscountService.calculateOrderLineNetDiscountPercent(order.getLineItemsNetEffect(), chargeMap);

        List<OrderLineItem> recurringOrderLineItems = OrderServiceHelper.getRecurringOrderLineItems(order.getLineItemsNetEffect(), chargeMap);
        BigDecimal recurringDiscountPercent = OrderDiscountService.calculateOrderLineNetDiscountPercent(recurringOrderLineItems, chargeMap);

        List<OrderLineItem> nonRecurringOrderLineItems = OrderServiceHelper.getNonRecurringOrderLineItems(order.getLineItemsNetEffect(), chargeMap);
        BigDecimal nonRecurringDiscountPercent = OrderDiscountService.calculateOrderLineNetDiscountPercent(nonRecurringOrderLineItems, chargeMap);

        BigDecimal maxOrderLineItemDiscountPercent = OrderDiscountService.calculateMaxOrderLineItemDiscountPercent(
            order.getLineItemsNetEffect(),
            chargeMap
        );

        long termLengthInMonths = getTermLengthForOrder(order, approvalFlowContext.getTimeZone());

        Map<String, Object> values = new HashMap<>();
        values.put(ApprovalFlowUtils.ORDER_CONDITION_ORDER_TYPE_VARIABLE, order.getOrderType().name());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_TCV_VARIABLE, order.getTotalAmount().doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_ORDER_DISCOUNT_VARIABLE, orderDiscountPercent.doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_RECURRING_DISCOUNT_VARIABLE, recurringDiscountPercent.doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_NON_RECURRING_DISCOUNT_VARIABLE, nonRecurringDiscountPercent.doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_PAYMENT_TERM_VARIABLE, order.getPaymentTerm().name());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_BILLING_CYCLE_VARIABLE, order.getBillingCycle().getCycle().name());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_AVG_ARR_VARIABLE, orderMetrics.getAverageArr().doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_EXIT_ARR_VARIABLE, orderMetrics.getExitArr().doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_ENTRY_ARR_VARIABLE, orderMetrics.getEntryArr().doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_TERM_LENGTH_VARIABLE, termLengthInMonths);
        values.put(ApprovalFlowUtils.ORDER_CONDITION_CUSTOM_TERM_VARIABLE, approvalFlowContext.getCustomTerms());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_DELTA_ARR_VARIABLE, orderMetrics.getDeltaArr().doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_DELTA_ARR_PERCENT_VARIABLE, deltaArrPercent.doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_AUTO_RENEW_VARIABLE, order.getAutoRenew());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_OPPORTUNITY_ID_VARIABLE, approvalFlowContext.getOpportunityId());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, approvalFlowContext.getIsReseller());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_CHARGE_IDS, approvalFlowContext.getChargeIds());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_PLAN_IDS, approvalFlowContext.getPlanIds());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_PRODUCT_IDS, approvalFlowContext.getProductIds());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_MAX_ORDER_LINE_DISCOUNT_PERCENT, maxOrderLineItemDiscountPercent.doubleValue());
        values.put(
            ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_TERM_NAMES,
            approvalFlowContext.getPredefinedTermsOnOrder().stream().map(DocumentTemplate::getName).collect(Collectors.toList())
        );

        //Populate custom fields on account, order and opportunity
        populateCustomFieldsOnAccount(approvalFlowContext.getCustomFieldOnAccount(), values);
        populateCustomFieldsOnOrder(approvalFlowContext.getCustomFieldsOnOrder(), values);
        populateCustomFieldsOnOpportunity(approvalFlowContext.getCustomFieldsOnOpportunity(), values);

        if (Objects.nonNull(order.getPredefinedDiscounts())) {
            List<String> predefinedDiscounts = order.getPredefinedDiscounts().stream().map(TenantDiscount::getId).toList();
            for (String discount : predefinedDiscounts) {
                values.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, discount);
                if (performJsonLogicConditionEvaluation(orderCondition, values)) {
                    return true;
                }
            }
        }

        List<DocumentTemplate> predefinedTermsOnOrder = approvalFlowContext.getPredefinedTermsOnOrder();
        Set<String> modifiedPredefinedTerms = approvalFlowContext.getModifiedPredefinedTermsOnOrder();
        if (CollectionUtils.isNotEmpty(predefinedTermsOnOrder)) {
            for (DocumentTemplate predefinedTerm : predefinedTermsOnOrder) {
                values.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_TERM_NAME_VARIABLE, predefinedTerm.getName());
                boolean isPredefinedTermModified =
                    modifiedPredefinedTerms != null && modifiedPredefinedTerms.contains(predefinedTerm.getTemplateId());
                values.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_TERMS_MODIFIED_VARIABLE, isPredefinedTermModified);
                values.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_TERMS_OUTDATED_VARIABLE, predefinedTerm.getHasNewerVersion());
                if (performJsonLogicConditionEvaluation(orderCondition, values)) {
                    return true;
                }
            }
        }

        return performJsonLogicConditionEvaluation(orderCondition, values);
    }

    private static long getTermLengthInMonthsFromDates(Order order, TimeZone timeZone) {
        BillyDuration duration = DateTimeCalculator.getDurationBetween(order.getStartDate(), order.getEndDate(), timeZone);
        return duration.getMonths();
    }

    private static boolean performJsonLogicConditionEvaluation(JSONObject condition, Map<String, Object> values) {
        try {
            JsonLogic jsonLogic = new JsonLogic();
            Object result = jsonLogic.apply(condition.toString(), values);
            return (boolean) result;
        } catch (JsonLogicException e) {
            LOGGER.error("ApprovalFlowConditionCheck exception for condition: {}", condition, e);
            return false;
        }
    }

    private static void updateGroupStatusFromIndividualInstances(
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup,
        Optional<User> adminByPassByUser
    ) {
        var approvalFlowErrorInstances = approvalFlowInstanceGroup
            .getApprovalFlowInstances()
            .stream()
            .filter(approvalFlowInstance -> ApprovalFlowInstanceStatus.getErrorStates().contains(approvalFlowInstance.getStatus()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(approvalFlowErrorInstances)) {
            ApprovalFlowInstance firstErrorInstance = approvalFlowErrorInstances.stream().findAny().orElseThrow();
            approvalFlowInstanceGroup.setApprovalStatus(firstErrorInstance.getStatus());
            approvalFlowInstanceGroup.setWorkflowStatus(ApprovalFlowInstanceWorkflowStatus.COMPLETED);
            return;
        }

        if (
            approvalFlowInstanceGroup
                .getApprovalFlowInstances()
                .stream()
                .allMatch(
                    approvalFlowInstance ->
                        approvalFlowInstance.getStatus() == ApprovalFlowInstanceStatus.APPROVED ||
                        approvalFlowInstance.getStatus() == ApprovalFlowInstanceStatus.NOT_APPLICABLE
                )
        ) {
            approvalFlowInstanceGroup.setApprovalStatus(ApprovalFlowInstanceStatus.APPROVED);
            approvalFlowInstanceGroup.setWorkflowStatus(ApprovalFlowInstanceWorkflowStatus.COMPLETED);
            return;
        }

        if (
            approvalFlowInstanceGroup
                .getApprovalFlowInstances()
                .stream()
                .anyMatch(approvalFlowInstance -> approvalFlowInstance.getStatus() == ApprovalFlowInstanceStatus.REJECTED)
        ) {
            approvalFlowInstanceGroup.setApprovalStatus(ApprovalFlowInstanceStatus.REJECTED);
            approvalFlowInstanceGroup.setWorkflowStatus(ApprovalFlowInstanceWorkflowStatus.COMPLETED);
            return;
        }

        if (adminByPassByUser.isPresent()) {
            approvalFlowInstanceGroup.setApprovalStatus(ApprovalFlowInstanceStatus.APPROVED);
            approvalFlowInstanceGroup.setWorkflowStatus(ApprovalFlowInstanceWorkflowStatus.COMPLETED);
            approvalFlowInstanceGroup.setNote(
                String.format(ApprovalFlowUtils.ADMIN_APPROVAL_FLOW_BYPASS_MESSAGE, adminByPassByUser.get().getDisplayName())
            );

            approvalFlowInstanceGroup
                .getApprovalFlowInstances()
                .stream()
                .flatMap(instance -> instance.getApprovalFlowData().getStates().stream())
                .filter(
                    state ->
                        state.getStatus() == ApprovalFlowInstanceStatus.AWAITING_APPROVAL || state.getStatus() == ApprovalFlowInstanceStatus.INACTIVE
                )
                .forEach(state -> {
                    state.setStatus(ApprovalFlowInstanceStatus.ADMIN_BYPASS);
                    state.setApprovedBy(List.of(new UserActionTime(adminByPassByUser.get().getUserId(), Instant.now(), null)));
                });

            approvalFlowInstanceGroup
                .getApprovalFlowInstances()
                .stream()
                .filter(instance -> instance.getStatus() == ApprovalFlowInstanceStatus.AWAITING_APPROVAL)
                .forEach(approvalFlowInstance -> {
                    approvalFlowInstance.setStatus(ApprovalFlowInstanceStatus.ADMIN_BYPASS);
                    approvalFlowInstance.getApprovalFlowData().setStatus(ApprovalFlowInstanceStatus.ADMIN_BYPASS);
                });
            return;
        }

        if (
            approvalFlowInstanceGroup
                .getApprovalFlowInstances()
                .stream()
                .anyMatch(approvalFlowInstance -> approvalFlowInstance.getStatus() == ApprovalFlowInstanceStatus.AWAITING_APPROVAL)
        ) {
            approvalFlowInstanceGroup.setApprovalStatus(ApprovalFlowInstanceStatus.AWAITING_APPROVAL);
            approvalFlowInstanceGroup.setWorkflowStatus(ApprovalFlowInstanceWorkflowStatus.IN_PROGRESS);
        }
    }

    public Map<String, Object> prepareApprovalFlowDataSnapshot(
        Order order,
        Metrics orderMetrics,
        BigDecimal deltaArrPercent,
        Map<String, Metrics> orderLineMetrics,
        TimeZone timeZone
    ) {
        Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
        BigDecimal orderDiscountPercent = OrderDiscountService.calculateOrderLineNetDiscountPercent(order.getLineItemsNetEffect(), chargeMap);
        List<OrderLineItem> recurringOrderLineItems = OrderServiceHelper.getRecurringOrderLineItems(order.getLineItemsNetEffect(), chargeMap);
        BigDecimal recurringDiscountPercent = OrderDiscountService.calculateOrderLineNetDiscountPercent(recurringOrderLineItems, chargeMap);
        List<OrderLineItem> nonRecurringOrderLineItems = OrderServiceHelper.getNonRecurringOrderLineItems(order.getLineItemsNetEffect(), chargeMap);
        BigDecimal nonRecurringDiscountPercent = OrderDiscountService.calculateOrderLineNetDiscountPercent(nonRecurringOrderLineItems, chargeMap);
        BigDecimal maxOrderLineItemDiscountPercent = OrderDiscountService.calculateMaxOrderLineItemDiscountPercent(
            order.getLineItemsNetEffect(),
            chargeMap
        );
        long termLengthInMonths = getTermLengthInMonthsFromDates(order, timeZone);

        String opportunityId = getOpportunityId(order);

        Map<String, Object> values = new HashMap<>();
        values.put(ApprovalFlowUtils.ORDER_CONDITION_ORDER_TYPE_VARIABLE, order.getOrderType().name());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_TCV_VARIABLE, order.getTotalAmount().doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_ORDER_DISCOUNT_VARIABLE, orderDiscountPercent.doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_RECURRING_DISCOUNT_VARIABLE, recurringDiscountPercent.doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_NON_RECURRING_DISCOUNT_VARIABLE, nonRecurringDiscountPercent.doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_PAYMENT_TERM_VARIABLE, order.getPaymentTerm().name());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_BILLING_CYCLE_VARIABLE, order.getBillingCycle().getCycle().name());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_AVG_ARR_VARIABLE, orderMetrics.getAverageArr().doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_EXIT_ARR_VARIABLE, orderMetrics.getExitArr().doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_ENTRY_ARR_VARIABLE, orderMetrics.getEntryArr().doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_TERM_LENGTH_VARIABLE, termLengthInMonths);
        values.put(ApprovalFlowUtils.ORDER_CONDITION_CUSTOM_TERM_VARIABLE, getCustomTerms(order));
        values.put(ApprovalFlowUtils.ORDER_CONDITION_DELTA_ARR_VARIABLE, orderMetrics.getDeltaArr().doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_DELTA_ARR_PERCENT_VARIABLE, deltaArrPercent.doubleValue());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_AUTO_RENEW_VARIABLE, order.getAutoRenew());
        values.put(ApprovalFlowUtils.ORDER_CONDITION_OPPORTUNITY_ID_VARIABLE, opportunityId);
        values.put(ApprovalFlowUtils.ORDER_CONDITION_MAX_ORDER_LINE_DISCOUNT_PERCENT, maxOrderLineItemDiscountPercent.doubleValue());

        // Populate resller account details
        Optional<AccountStub> accountStubOptional = orderResellerService.getResellerAccount(order);
        values.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, accountStubOptional.isPresent());
        values.put(ApprovalFlowUtils.RESELLER_ACCOUNT_ID, accountStubOptional.map(AccountStub::getAccountId).orElse(null));

        //Populate custom fields on account, order and opportunity
        CustomField customFieldOnAccount = customFieldService.getCustomFields(CustomFieldParentType.ACCOUNT, order.getAccountId());
        populateCustomFieldsOnAccount(customFieldOnAccount, values);
        populateCustomFieldsOnOrder(order.getCustomFields(), values);
        populateCustomFieldsOnOpportunity(getCustomFieldsOnOpportunity(opportunityId), values);

        //Populate Predefined Discounts where Map.Entry will be of form <predefinedDiscounts, List<String> predefinedDiscounts>
        populatePredefinedDiscounts(order, values);

        //Populate Predefined Terms on Order
        List<DocumentTemplate> predefinedTermsOnOrder = getAllPredefinedTermsOnOrder(documentTemplateGetService, orderTermsService, order);
        Map<String, CustomTemplateUpdateOnOrder> modifiedPredefinedTermsOnOrder = getUpdatedPredefinedTermsOnOrder(
            customTemplateUpdatedOnOrderGetService,
            order.getOrderId()
        )
            .stream()
            .collect(Collectors.toMap(CustomTemplateUpdateOnOrder::getTemplateId, Function.identity()));
        if (CollectionUtils.isNotEmpty(predefinedTermsOnOrder)) {
            Map<String, String> termTemplateIdContentMap = new HashMap<>();
            Map<String, String> termTemplateIdNameMap = new HashMap<>();
            for (DocumentTemplate predefinedTerm : predefinedTermsOnOrder) {
                String name = modifiedPredefinedTermsOnOrder.containsKey(predefinedTerm.getTemplateId())
                    ? modifiedPredefinedTermsOnOrder.get(predefinedTerm.getTemplateId()).getName()
                    : predefinedTerm.getName();
                String content = modifiedPredefinedTermsOnOrder.containsKey(predefinedTerm.getTemplateId())
                    ? modifiedPredefinedTermsOnOrder.get(predefinedTerm.getTemplateId()).getContent()
                    : predefinedTerm.getContent();
                termTemplateIdContentMap.put(predefinedTerm.getTemplateId(), ApprovalFlowUtils.getPredefinedTermHash(content));
                termTemplateIdNameMap.put(predefinedTerm.getTemplateId(), name);
            }
            values.put(ApprovalFlowUtils.PREDEFINED_TERMS_CONTENT_ON_ORDER, termTemplateIdContentMap);
            values.put(ApprovalFlowUtils.PREDEFINED_TERMS_NAME_ON_ORDER, termTemplateIdNameMap);
        }

        // Populate Order line level data
        EntityCache<String, Plan> planEntityCache = EntityCache.of(productCatalogGetService::getPlan);
        EntityCache<String, Product> productEntityCache = EntityCache.of(productCatalogGetService::getProduct);
        Map<String, CustomField> customFieldOnOrderLinesMap = getCustomFieldOnOrderLinesMap(order);
        Map<String, CustomField> customFieldsOnPlansMap = getCustomFieldsOnPlansMap(chargeMap);
        Map<String, CustomField> customFieldsOnChargesMap = getCustomFieldsOnChargesMap(chargeMap);

        Map<String, Metrics> mutableOrderLineMetrics = new HashMap<>(orderLineMetrics);
        List<OrderLineItem> orderLineItems = OrderServiceHelper.consolidateOrderLineItemsByBaseExternalSubscriptionChargeId(
            order.getLineItemsNetEffect(),
            mutableOrderLineMetrics
        );
        values.put(ApprovalFlowUtils.ORDER_CONDITION_NUMBER_OF_CONSOLIDATED_LINES, orderLineItems.size());

        for (OrderLineItem orderLineItem : orderLineItems) {
            Map<String, Object> orderLineValues = new HashMap<>();
            BigDecimal lineItemDiscountPercent = OrderDiscountService.calculateOrderLineNetDiscountPercent(List.of(orderLineItem), chargeMap);

            orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_DISCOUNT_VARIABLE, lineItemDiscountPercent);
            orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_ID_VARIABLE, orderLineItem.getChargeId());
            orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_QUANTITY_VARIABLE, orderLineItem.getQuantity());
            orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_AMOUNT_VARIABLE, orderLineItem.getAmount());
            orderLineValues.put(
                ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE,
                mutableOrderLineMetrics.get(orderLineItem.getOrderLineId()).getEntryArr()
            );
            orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_PLAN_ID_VARIABLE, orderLineItem.getPlanId());
            orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_HAS_OVERRIDE, orderLineItem.getListAmountBeforeOverride() != null);
            orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_HAS_ARR_OVERRIDE, orderLineItem.getArrOverride() != null);
            orderLineValues.put(
                ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_TYPE_VARIABLE,
                chargeMap.get(orderLineItem.getChargeId()).getType().name()
            );
            orderLineValues.put(
                ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_IS_CREDITABLE_VARIABLE,
                chargeMap.get(orderLineItem.getChargeId()).isCreditable()
            );
            orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_ACTION_TYPE_VARIABLE, orderLineItem.getAction().name());
            orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING, chargeMap.get(orderLineItem.getChargeId()).isCustom());
            if (orderLineItem.getListUnitPrice() != null) {
                orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, orderLineItem.getListUnitPrice());
            }

            String productId = planEntityCache.get(orderLineItem.getPlanId()).getProductId();
            orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_PRODUCT_ID_VARIABLE, productId);

            ProductCategory productCategory = productEntityCache.get(productId).getProductCategory();
            if (Objects.nonNull(productCategory) && StringUtils.isNotEmpty(productCategory.getProductCategoryId())) {
                orderLineValues.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_PRODUCT_CATEGORY_ID_VARIABLE, productCategory.getProductCategoryId());
            }

            //orderLine custom fields
            addCustomFieldsAndValuesForOrderLineEvaluation(
                customFieldOnOrderLinesMap,
                orderLineItem.getOrderLineId(),
                ApprovalFlowUtils.ORDER_LINE_CONDITION_ORDER_LINE_CUSTOM_FIELD_PREFIX,
                orderLineValues
            );

            //plan custom fields
            addCustomFieldsAndValuesForOrderLineEvaluation(
                customFieldsOnPlansMap,
                orderLineItem.getPlanId(),
                ApprovalFlowUtils.ORDER_LINE_CONDITION_PLAN_CUSTOM_FIELD_PREFIX,
                orderLineValues
            );

            //charge custom fields
            addCustomFieldsAndValuesForOrderLineEvaluation(
                customFieldsOnChargesMap,
                orderLineItem.getChargeId(),
                ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_CUSTOM_FIELD_PREFIX,
                orderLineValues
            );
            values.put(String.valueOf(orderLineItem.getRank()), orderLineValues);
        }

        return values;
    }

    private String getOpportunityId(Order order) {
        String opportunityId = order.getSfdcOpportunityId();
        if (StringUtils.isNotEmpty(order.getCompositeOrderId())) {
            CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(order.getCompositeOrderId());
            opportunityId = compositeOrder.getCrmOpportunityId();
        }
        return opportunityId;
    }

    private static long getTermLengthForOrder(Order order, TimeZone timeZone) {
        if (order.getTermLength() != null) {
            return order.getTermLength().getRecurrenceDurationInMonths();
        }
        BillyDuration duration = DateTimeCalculator.getDurationBetween(order.getStartDate(), order.getEndDate(), timeZone);
        return duration.getMonths();
    }

    private String getCustomTerms(Order order) {
        Optional<DocumentCustomContent> optionalDocumentCustomContent = documentCustomContentGetService.getDocumentCustomContentByOrderId(
            order.getOrderId()
        );
        return optionalDocumentCustomContent.map(DocumentCustomContent::getContent).orElse(StringUtils.EMPTY);
    }

    private static void populateCustomFieldsOnOrder(CustomField customFieldsOnOrder, Map<String, Object> values) {
        if (customFieldsOnOrder != null && !customFieldsOnOrder.getEntries().isEmpty()) {
            for (var orderEntrySet : customFieldsOnOrder.getEntries().entrySet()) {
                values.put(
                    ApprovalFlowUtils.ORDER_CONDITION_ORDER_CUSTOM_FIELD_PREFIX + orderEntrySet.getValue().getName(),
                    orderEntrySet.getValue().getValue()
                );
            }
        }
    }

    private static void populateCustomFieldsOnOpportunity(CustomField customFieldsOnOpportunity, Map<String, Object> values) {
        if (customFieldsOnOpportunity != null && !customFieldsOnOpportunity.getEntries().isEmpty()) {
            for (var orderEntrySet : customFieldsOnOpportunity.getEntries().entrySet()) {
                values.put(
                    ApprovalFlowUtils.ORDER_CONDITION_OPPORTUNITY_CUSTOM_FIELD_PREFIX + orderEntrySet.getValue().getName(),
                    orderEntrySet.getValue().getValue()
                );
            }
        }
    }

    private void populatePredefinedDiscounts(Order order, Map<String, Object> values) {
        if (Objects.nonNull(order.getPredefinedDiscounts())) {
            Map<String, String> predefinedDiscountMap = order
                .getPredefinedDiscounts()
                .stream()
                .collect(Collectors.toMap(TenantDiscount::getId, this::getPredefinedDiscountHash));
            values.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, predefinedDiscountMap);
        }
    }

    private static void populateCustomFieldsOnAccount(CustomField customFieldsOnAccount, Map<String, Object> values) {
        if (customFieldsOnAccount != null && !customFieldsOnAccount.getEntries().isEmpty()) {
            for (var accountEntrySet : customFieldsOnAccount.getEntries().entrySet()) {
                values.put(
                    ApprovalFlowUtils.ORDER_CONDITION_ACCOUNT_CUSTOM_FIELD_PREFIX + accountEntrySet.getValue().getName(),
                    accountEntrySet.getValue().getValue()
                );
            }
        }
    }

    private CustomField getCustomFieldsOnOpportunity(String opportunityId) {
        if (StringUtils.isBlank(opportunityId)) {
            return null;
        }
        return opportunityGetService.getOptionalOpportunityByCrmOpportunityId(opportunityId).map(Opportunity::getCustomFields).orElse(null);
    }

    private Map<String, CustomField> getCustomFieldOnOrderLinesMap(Order order) {
        List<String> orderLineIds = order.getLineItems().stream().map(OrderLineItem::getOrderLineId).collect(Collectors.toList());
        Map<String, CustomField> customFieldOnOrderLinesMap = CollectionUtils.isEmpty(orderLineIds)
            ? Map.of()
            : customFieldService.getCustomFields(CustomFieldParentType.ORDER_ITEM, orderLineIds);
        return customFieldOnOrderLinesMap;
    }

    private Map<String, CustomField> getCustomFieldsOnPlansMap(Map<String, Charge> chargeMap) {
        List<String> planIds = chargeMap.values().stream().map(Charge::getPlanId).distinct().collect(Collectors.toList());
        Map<String, CustomField> customFieldsOnPlansMap = CollectionUtils.isEmpty(planIds)
            ? Map.of()
            : customFieldService.getCustomFields(CustomFieldParentType.PLAN, planIds);
        return customFieldsOnPlansMap;
    }

    private Map<String, CustomField> getCustomFieldsOnChargesMap(Map<String, Charge> chargeMap) {
        List<String> chargeIds = chargeMap.values().stream().map(Charge::getChargeId).distinct().collect(Collectors.toList());
        Map<String, CustomField> customFieldsOnChargesMap = CollectionUtils.isEmpty(chargeIds)
            ? Map.of()
            : customFieldService.getCustomFields(CustomFieldParentType.CHARGE, chargeIds);
        return customFieldsOnChargesMap;
    }

    private String getPredefinedDiscountHash(TenantDiscount tenantDiscount) {
        if (Objects.isNull(tenantDiscount)) {
            return null;
        }
        try {
            String predefinedDiscountJson = JacksonProvider.defaultMapper().writeValueAsString(tenantDiscount);
            if (StringUtils.isBlank(predefinedDiscountJson)) {
                return null;
            }
            return DigestUtils.md5Hex(predefinedDiscountJson);
        } catch (JsonProcessingException e) {
            LOGGER.info("error serializing tenantDiscount object with name: {}", tenantDiscount.getName(), e);
            return null;
        }
    }
}
