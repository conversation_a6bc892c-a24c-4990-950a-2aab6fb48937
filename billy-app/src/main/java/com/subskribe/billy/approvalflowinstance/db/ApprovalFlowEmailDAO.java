package com.subskribe.billy.approvalflowinstance.db;

import static com.subskribe.billy.jooq.default_schema.tables.ApprovalFlowEmailCode.APPROVAL_FLOW_EMAIL_CODE;
import static com.subskribe.billy.jooq.default_schema.tables.ApprovalFlowEmails.APPROVAL_FLOW_EMAILS;

import com.subskribe.billy.approvalflowinstance.mapper.ApprovalFlowEmailMapper;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowEmail;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowEmailCode;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceStatus;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.jooq.default_schema.tables.records.ApprovalFlowEmailCodeRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.ApprovalFlowEmailsRecord;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.util.List;
import javax.inject.Inject;
import org.mapstruct.factory.Mappers;

public class ApprovalFlowEmailDAO {

    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final ApprovalFlowEmailMapper mapper;

    @Inject
    public ApprovalFlowEmailDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        mapper = Mappers.getMapper(ApprovalFlowEmailMapper.class);
    }

    public ApprovalFlowEmail addApprovalFlowEmail(
        String groupId,
        String approvalFlowId,
        String approvalStateId,
        List<String> toEmailList,
        ApprovalFlowInstanceStatus status
    ) {
        ApprovalFlowEmail approvalFlowEmail = new ApprovalFlowEmail();
        approvalFlowEmail.setGroupId(groupId);
        approvalFlowEmail.setApprovalFlowId(approvalFlowId);
        approvalFlowEmail.setApprovalStateId(approvalStateId);
        approvalFlowEmail.setToEmailList(toEmailList);
        approvalFlowEmail.setApprovalStatus(status);

        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        ApprovalFlowEmailsRecord approvalFlowEmailsRecord = mapper.emailToRecord(approvalFlowEmail);
        approvalFlowEmailsRecord.setTenantId(tenantId);
        approvalFlowEmailsRecord.reset(APPROVAL_FLOW_EMAILS.ID);
        ApprovalFlowEmailsRecord insertedRecord = dslContext
            .insertInto(APPROVAL_FLOW_EMAILS)
            .set(approvalFlowEmailsRecord)
            .returning()
            .fetchOneInto(ApprovalFlowEmailsRecord.class);
        return mapper.recordToEmail(insertedRecord);
    }

    @AllowNonRlsDataAccess
    public void addEmailCode(ApprovalFlowEmailCode emailCode) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        ApprovalFlowEmailCodeRecord record = mapper.approvalFlowEmailCodeToRecord(emailCode);
        record.setTenantId(tenantId);
        dslContextProvider.get().insertInto(APPROVAL_FLOW_EMAIL_CODE).set(record).execute();
    }

    @AllowNonRlsDataAccess
    public ApprovalFlowEmailCode getEmailCode(String code) {
        Validator.validateNonNullArgument(code);
        ApprovalFlowEmailCodeRecord record = dslContextProvider
            .get()
            .select()
            .from(APPROVAL_FLOW_EMAIL_CODE)
            .where(APPROVAL_FLOW_EMAIL_CODE.CODE.eq(code))
            .and(APPROVAL_FLOW_EMAIL_CODE.IS_DELETED.isFalse())
            .and(APPROVAL_FLOW_EMAIL_CODE.IS_SUCCESSFULLY_USED.isFalse())
            .fetchOneInto(ApprovalFlowEmailCodeRecord.class);

        if (record == null) {
            throw new IllegalArgumentException("Code invalid or already used.");
        }

        return mapper.recordToApprovalFlowEmailCode(record);
    }

    @AllowNonRlsDataAccess
    public void markCodeAsUsed(String code) {
        Validator.validateNonNullArguments(code);
        ApprovalFlowEmailCode emailCode = getEmailCode(code);
        emailCode.setIsSuccessfullyUsed(true);
        emailCode.setSuccessfullyUsedOn(Instant.now());
        updateEmailCode(emailCode);
    }

    @AllowNonRlsDataAccess
    private void updateEmailCode(ApprovalFlowEmailCode emailCode) {
        ApprovalFlowEmailCodeRecord record = mapper.approvalFlowEmailCodeToRecord(emailCode);
        dslContextProvider.get().update(APPROVAL_FLOW_EMAIL_CODE).set(record).where(APPROVAL_FLOW_EMAIL_CODE.CODE.eq(emailCode.getCode())).execute();
    }
}
