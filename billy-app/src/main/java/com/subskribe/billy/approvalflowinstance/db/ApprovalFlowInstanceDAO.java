package com.subskribe.billy.approvalflowinstance.db;

import static com.subskribe.billy.jooq.default_schema.Tables.APPROVAL_FLOW_SUBMITTER_NOTE;
import static com.subskribe.billy.jooq.default_schema.tables.ApprovalFlowInstance.APPROVAL_FLOW_INSTANCE;
import static com.subskribe.billy.jooq.default_schema.tables.ApprovalFlowInstanceGroup.APPROVAL_FLOW_INSTANCE_GROUP;

import com.subskribe.billy.approvalflowinstance.mapper.ApprovalFlowInstanceDAOMapper;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstance;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceGroup;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceStatus;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceWorkflowStatus;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowSubmitterNote;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.jooq.default_schema.Indexes;
import com.subskribe.billy.jooq.default_schema.tables.records.ApprovalFlowInstanceGroupRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.ApprovalFlowInstanceRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.ApprovalFlowSubmitterNoteRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class ApprovalFlowInstanceDAO {

    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final ApprovalFlowInstanceDAOMapper approvalFlowInstanceDAOMapper;

    @Inject
    public ApprovalFlowInstanceDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        approvalFlowInstanceDAOMapper = Mappers.getMapper(ApprovalFlowInstanceDAOMapper.class);
    }

    public void addApprovalFlowInstance(DSLContext dslContext, ApprovalFlowInstanceGroup approvalFlowInstanceGroup) {
        var tenantId = tenantIdProvider.provideTenantIdString();

        dslContext.transaction(configuration -> addApprovalFlowInstanceTransaction(configuration, approvalFlowInstanceGroup, tenantId));
    }

    private void addApprovalFlowInstanceTransaction(
        Configuration configuration,
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup,
        String tenantId
    ) {
        var dslContext = DSL.using(configuration);
        var record = approvalFlowInstanceDAOMapper.toGroupRecord(approvalFlowInstanceGroup);
        record.setTenantId(tenantId);
        record.reset(APPROVAL_FLOW_INSTANCE_GROUP.ID);

        dslContext.insertInto(APPROVAL_FLOW_INSTANCE_GROUP).set(record).execute();

        dslContext
            .update(APPROVAL_FLOW_SUBMITTER_NOTE)
            .set(APPROVAL_FLOW_SUBMITTER_NOTE.INSTANCE_GROUP_ID, approvalFlowInstanceGroup.getGroupId())
            .where(APPROVAL_FLOW_SUBMITTER_NOTE.TENANT_ID.eq(tenantId))
            .and(APPROVAL_FLOW_SUBMITTER_NOTE.ORDER_ID.eq(approvalFlowInstanceGroup.getOrderId()))
            .and(APPROVAL_FLOW_SUBMITTER_NOTE.IS_DELETED.isFalse())
            .and(APPROVAL_FLOW_SUBMITTER_NOTE.INSTANCE_GROUP_ID.isNull())
            .execute();

        approvalFlowInstanceGroup
            .getApprovalFlowInstances()
            .forEach(approvalFlowInstance -> approvalFlowInstance.setGroupId(approvalFlowInstanceGroup.getGroupId()));
        bulkInsertApprovalFlowInstances(dslContext, approvalFlowInstanceGroup.getApprovalFlowInstances(), tenantId);
    }

    private void bulkInsertApprovalFlowInstances(DSLContext dslContext, List<ApprovalFlowInstance> approvalFlowInstances, String tenantId) {
        var records = approvalFlowInstanceDAOMapper.toInstanceRecords(approvalFlowInstances);
        records.forEach(approvalFlowInstanceRecord -> {
            approvalFlowInstanceRecord.setTenantId(tenantId);
            approvalFlowInstanceRecord.reset(APPROVAL_FLOW_INSTANCE.ID);
        });

        dslContext.batchInsert(records).execute();
    }

    public void updateApprovalFlowInstance(DSLContext dslContext, ApprovalFlowInstanceGroup approvalFlowInstanceGroup) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        // get latest saved group
        var existingApprovalFlowOptional = getApprovalFlowInstanceByGroupId(approvalFlowInstanceGroup.getGroupId());
        ApprovalFlowInstanceGroup existingApprovalFlowInstanceGroup = existingApprovalFlowOptional.orElseThrow(() ->
            new ObjectNotFoundException(BillyObjectType.APPROVAL_FLOW_INSTANCE_GROUP, approvalFlowInstanceGroup.getGroupId())
        );

        var record = approvalFlowInstanceDAOMapper.toGroupRecord(approvalFlowInstanceGroup);
        record.setTenantId(tenantId);
        record.setId(existingApprovalFlowInstanceGroup.getId());
        dslContext.executeUpdate(record);

        updateApprovalFlowInstances(
            dslContext,
            approvalFlowInstanceGroup.getApprovalFlowInstances(),
            existingApprovalFlowInstanceGroup.getApprovalFlowInstances(),
            tenantId
        );
    }

    private void updateApprovalFlowInstances(
        DSLContext dslContext,
        List<ApprovalFlowInstance> approvalFlowInstances,
        List<ApprovalFlowInstance> existingApprovalFlowInstances,
        String tenantId
    ) {
        var existingApprovalFlowIds = existingApprovalFlowInstances
            .stream()
            .collect(Collectors.toMap(ApprovalFlowInstance::getApprovalFlowId, Function.identity()));
        var records = approvalFlowInstanceDAOMapper.toInstanceRecords(approvalFlowInstances);
        for (ApprovalFlowInstanceRecord record : records) {
            record.setTenantId(tenantId);
            record.setId(existingApprovalFlowIds.get(record.getApprovalFlowId()).getId());
        }

        dslContext.batchUpdate(records).execute();
    }

    public void cancelInProgressWorkflowsByOrderId(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        dslContext
            .update(APPROVAL_FLOW_INSTANCE_GROUP)
            .set(APPROVAL_FLOW_INSTANCE_GROUP.WORKFLOW_STATUS, ApprovalFlowInstanceWorkflowStatus.COMPLETED.name())
            .set(APPROVAL_FLOW_INSTANCE_GROUP.APPROVAL_STATUS, ApprovalFlowInstanceStatus.CANCELLED.name())
            .where(APPROVAL_FLOW_INSTANCE_GROUP.TENANT_ID.eq(tenantId))
            .and(APPROVAL_FLOW_INSTANCE_GROUP.ORDER_ID.eq(orderId))
            .and(APPROVAL_FLOW_INSTANCE_GROUP.WORKFLOW_STATUS.eq(ApprovalFlowInstanceWorkflowStatus.IN_PROGRESS.name()))
            .execute();
    }

    public Optional<ApprovalFlowInstanceGroup> getApprovalFlowInstanceByGroupId(String approvalFlowInstanceGroupId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<ApprovalFlowInstanceGroupRecord> groupRecords = dslContext
            .select()
            .from(APPROVAL_FLOW_INSTANCE_GROUP)
            .where(APPROVAL_FLOW_INSTANCE_GROUP.GROUP_ID.eq(approvalFlowInstanceGroupId))
            .and(APPROVAL_FLOW_INSTANCE_GROUP.TENANT_ID.eq(tenantId))
            .fetchInto(ApprovalFlowInstanceGroupRecord.class);

        if (CollectionUtils.isEmpty(groupRecords)) {
            return Optional.empty();
        }

        List<ApprovalFlowInstanceGroup> approvalFlowInstanceGroups = getApprovalFlowsFromGroupRecords(dslContext, groupRecords, tenantId);
        return approvalFlowInstanceGroups.stream().findFirst();
    }

    public List<ApprovalFlowInstanceGroup> getApprovalFlowInstancesByOrderId(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<ApprovalFlowInstanceGroupRecord> groupRecords = dslContext
            .select()
            .from(APPROVAL_FLOW_INSTANCE_GROUP)
            .where(APPROVAL_FLOW_INSTANCE_GROUP.ORDER_ID.eq(orderId))
            .and(APPROVAL_FLOW_INSTANCE_GROUP.TENANT_ID.eq(tenantId))
            .orderBy(APPROVAL_FLOW_INSTANCE_GROUP.VERSION.desc())
            .fetchInto(ApprovalFlowInstanceGroupRecord.class);

        if (CollectionUtils.isEmpty(groupRecords)) {
            return new ArrayList<>();
        }

        return getApprovalFlowsFromGroupRecords(dslContext, groupRecords, tenantId);
    }

    public List<ApprovalFlowInstanceGroup> getApprovalFlowsFromGroupRecords(
        DSLContext dslContext,
        List<ApprovalFlowInstanceGroupRecord> groupRecords,
        String tenantId
    ) {
        List<String> groupIds = groupRecords.stream().map(ApprovalFlowInstanceGroupRecord::getGroupId).collect(Collectors.toList());
        List<ApprovalFlowInstanceRecord> instanceRecords = dslContext
            .select()
            .from(APPROVAL_FLOW_INSTANCE)
            .where(APPROVAL_FLOW_INSTANCE.TENANT_ID.eq(tenantId))
            .and(APPROVAL_FLOW_INSTANCE.GROUP_ID.in(groupIds))
            .fetchInto(ApprovalFlowInstanceRecord.class);

        Map<String, List<ApprovalFlowInstanceRecord>> instanceRecordsByGroupId = instanceRecords
            .stream()
            .collect(Collectors.groupingBy(ApprovalFlowInstanceRecord::getGroupId));
        return getApprovalFlowsFromRecords(groupRecords, instanceRecordsByGroupId);
    }

    private List<ApprovalFlowInstanceGroup> getApprovalFlowsFromRecords(
        List<ApprovalFlowInstanceGroupRecord> groupRecords,
        Map<String, List<ApprovalFlowInstanceRecord>> instanceRecordsByGroupId
    ) {
        List<ApprovalFlowInstanceGroup> approvalFlowInstanceGroups = approvalFlowInstanceDAOMapper.toApprovalFlowInstanceGroups(groupRecords);

        for (ApprovalFlowInstanceGroup group : approvalFlowInstanceGroups) {
            List<ApprovalFlowInstanceRecord> instanceRecords = instanceRecordsByGroupId.getOrDefault(group.getGroupId(), List.of());
            List<ApprovalFlowInstance> approvalFlowInstances = approvalFlowInstanceDAOMapper.toApprovalFlowInstances(instanceRecords);
            group.setApprovalFlowInstances(approvalFlowInstances);
        }

        return approvalFlowInstanceGroups;
    }

    public Integer getApprovalGroupsCountForOrder(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        return dslContext
            .selectCount()
            .from(APPROVAL_FLOW_INSTANCE_GROUP)
            .where(APPROVAL_FLOW_INSTANCE_GROUP.ORDER_ID.eq(orderId))
            .and(APPROVAL_FLOW_INSTANCE_GROUP.TENANT_ID.eq(tenantId))
            .fetchOne(0, Integer.class);
    }

    public void ensureUniqueApprovalFlowInstanceGroupId(String approvalFlowInstanceGroupId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .select()
            .from(APPROVAL_FLOW_INSTANCE_GROUP)
            .where(APPROVAL_FLOW_INSTANCE_GROUP.TENANT_ID.eq(tenantId).and(APPROVAL_FLOW_INSTANCE_GROUP.GROUP_ID.eq(approvalFlowInstanceGroupId)))
            .fetchOneInto(ApprovalFlowInstanceGroupRecord.class);
        if (record == null) {
            return;
        }

        throwDuplicateApprovalFlowIdException(approvalFlowInstanceGroupId);
    }

    private void throwDuplicateApprovalFlowIdException(String approvalFlowInstanceGroupId) {
        var message = "Duplicate approvalFlowInstanceGroupId generated. approvalFlowInstanceGroupId = " + approvalFlowInstanceGroupId;
        throw new DuplicateIdException(message);
    }

    public List<ApprovalFlowInstanceGroup> getPendingApprovalFlowInstanceGroups() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<ApprovalFlowInstanceGroupRecord> groupRecords = dslContext
            .select()
            .from(APPROVAL_FLOW_INSTANCE_GROUP)
            .where(APPROVAL_FLOW_INSTANCE_GROUP.TENANT_ID.eq(tenantId))
            .and(APPROVAL_FLOW_INSTANCE_GROUP.WORKFLOW_STATUS.eq(ApprovalFlowInstanceWorkflowStatus.IN_PROGRESS.name()))
            .orderBy(APPROVAL_FLOW_INSTANCE_GROUP.VERSION.desc())
            .fetchInto(ApprovalFlowInstanceGroupRecord.class);

        if (CollectionUtils.isEmpty(groupRecords)) {
            return List.of();
        }

        return getApprovalFlowsFromGroupRecords(dslContext, groupRecords, tenantId);
    }

    public ApprovalFlowSubmitterNote addApprovalFlowSubmitterNote(ApprovalFlowSubmitterNote approvalFlowSubmitterNote) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = approvalFlowInstanceDAOMapper.toApprovalFlowSubmitterNoteRecord(approvalFlowSubmitterNote);
        record.setTenantId(tenantId);
        record.setNoteId(AutoGenerate.getNewId());
        record.reset(APPROVAL_FLOW_SUBMITTER_NOTE.ID);

        ApprovalFlowSubmitterNoteRecord savedRecord = PostgresErrorHandler.withAnyConstraintAsConflict(
            () -> dslContext.insertInto(APPROVAL_FLOW_SUBMITTER_NOTE).set(record).returning().fetchOneInto(ApprovalFlowSubmitterNoteRecord.class),
            Map.of(
                Indexes.INDEX_APPROVAL_FLOW_SUBMITTER_NOTE_TENANT_ID_NOTE_ID.getName(),
                String.format("Submitter note id exists for id: %s", approvalFlowSubmitterNote.getNoteId()),
                Indexes.INDEX_APPROVAL_FLOW_SUBMITTER_NOTE_TENANT_ID_GROUP_ID.getName(),
                String.format("Submitter note exists for the approval flow instance group: %s", approvalFlowSubmitterNote.getInstanceGroupId())
            )
        );

        return approvalFlowInstanceDAOMapper.toApprovalFlowSubmitterNote(savedRecord);
    }

    public ApprovalFlowSubmitterNote updateApprovalFlowSubmitterNote(ApprovalFlowSubmitterNote approvalFlowSubmitterNote) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = approvalFlowInstanceDAOMapper.toApprovalFlowSubmitterNoteRecord(approvalFlowSubmitterNote);
        record.setTenantId(tenantId);

        ApprovalFlowSubmitterNoteRecord savedRecord = dslContext
            .update(APPROVAL_FLOW_SUBMITTER_NOTE)
            .set(APPROVAL_FLOW_SUBMITTER_NOTE.NOTE, approvalFlowSubmitterNote.getNote())
            .where(APPROVAL_FLOW_SUBMITTER_NOTE.TENANT_ID.eq(tenantId))
            .and(APPROVAL_FLOW_SUBMITTER_NOTE.ID.eq(UUID.fromString(approvalFlowSubmitterNote.getId())))
            .returning()
            .fetchOneInto(ApprovalFlowSubmitterNoteRecord.class);

        return approvalFlowInstanceDAOMapper.toApprovalFlowSubmitterNote(savedRecord);
    }

    public List<ApprovalFlowSubmitterNote> getApprovalFlowSubmitterNotesForOrder(String orderId, boolean isPreview) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var query = dslContext
            .select()
            .from(APPROVAL_FLOW_SUBMITTER_NOTE)
            .where(APPROVAL_FLOW_SUBMITTER_NOTE.TENANT_ID.eq(tenantId))
            .and(APPROVAL_FLOW_SUBMITTER_NOTE.ORDER_ID.eq(orderId))
            .and(APPROVAL_FLOW_SUBMITTER_NOTE.IS_DELETED.isFalse());

        if (!isPreview) {
            query = query.and(APPROVAL_FLOW_SUBMITTER_NOTE.INSTANCE_GROUP_ID.isNotNull());
        } else {
            query = query.and(APPROVAL_FLOW_SUBMITTER_NOTE.INSTANCE_GROUP_ID.isNull());
        }

        List<ApprovalFlowSubmitterNoteRecord> records = query.fetchInto(ApprovalFlowSubmitterNoteRecord.class);
        return approvalFlowInstanceDAOMapper.toApprovalFlowSubmitterNotes(records);
    }

    public Optional<ApprovalFlowSubmitterNote> getLatestApprovalFlowSubmitterNotesForOrder(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        ApprovalFlowSubmitterNoteRecord approvalFlowSubmitterNoteRecord = dslContext
            .select()
            .from(APPROVAL_FLOW_SUBMITTER_NOTE)
            .where(APPROVAL_FLOW_SUBMITTER_NOTE.TENANT_ID.eq(tenantId))
            .and(APPROVAL_FLOW_SUBMITTER_NOTE.ORDER_ID.eq(orderId))
            .and(APPROVAL_FLOW_SUBMITTER_NOTE.IS_DELETED.isFalse())
            .orderBy(APPROVAL_FLOW_SUBMITTER_NOTE.CREATED_ON.desc())
            .limit(1)
            .fetchOneInto(ApprovalFlowSubmitterNoteRecord.class);
        if (approvalFlowSubmitterNoteRecord == null) {
            return Optional.empty();
        }
        return Optional.of(approvalFlowInstanceDAOMapper.toApprovalFlowSubmitterNote(approvalFlowSubmitterNoteRecord));
    }

    public Optional<ApprovalFlowInstanceGroup> getApprovalFlowInstancesByOrderIdAndVersion(String orderId, Integer version) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        ApprovalFlowInstanceGroupRecord groupRecord = dslContext
            .select()
            .from(APPROVAL_FLOW_INSTANCE_GROUP)
            .where(APPROVAL_FLOW_INSTANCE_GROUP.ORDER_ID.eq(orderId))
            .and(APPROVAL_FLOW_INSTANCE_GROUP.VERSION.eq(version))
            .and(APPROVAL_FLOW_INSTANCE_GROUP.TENANT_ID.eq(tenantId))
            .fetchOneInto(ApprovalFlowInstanceGroupRecord.class);

        if (groupRecord == null) {
            return Optional.empty();
        }

        List<ApprovalFlowInstanceGroup> approvalFlowInstanceGroups = getApprovalFlowsFromGroupRecords(dslContext, List.of(groupRecord), tenantId);
        return approvalFlowInstanceGroups.stream().findFirst();
    }
}
