package com.subskribe.billy.dataimport.processors;

import com.subskribe.billy.aws.s3.S3ClientProvider;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.dataimport.schema.CsvImportRow;
import com.subskribe.billy.dataimport.schema.order.OrderDomainCreateRow;
import com.subskribe.billy.dataimport.schema.order.OrderDomainCreateSchema;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.SellingPriceCalculator;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.DiscountCalculator;
import com.subskribe.billy.invoice.service.InvoiceAmountCalculator;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderSource;
import com.subskribe.billy.order.services.ChangeOrderService;
import com.subskribe.billy.order.services.MissingOrderChargesService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.pecuniary.TenantDiscount;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
abstract class BaseOrderImportProcessor extends BaseProcessor implements ImportProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseOrderImportProcessor.class);

    protected final ProductCatalogService catalogService;

    protected final ProductCatalogGetService catalogGetService;

    private final SellingPriceCalculator priceCalculator;

    protected final OrderService orderService;

    protected final ChangeOrderService changeOrderService;

    private final ProrationConfigurationGetService prorationConfigurationGetService;

    private final InvoiceAmountCalculator invoiceAmountCalculator;

    private final MetricsService metricsService;

    private final TenantSettingService tenantSettingService;

    protected final SubscriptionGetService subscriptionGetService;

    private final MissingOrderChargesService missingOrderChargesService;

    protected final UserService userService;

    private final FeatureService featureService;

    protected final OrderDomainCreateSchema orderCreateSchema;

    public BaseOrderImportProcessor(
        S3ClientProvider s3ClientProvider,
        TenantIdProvider tenantIdProvider,
        ProductCatalogService catalogService,
        ProductCatalogGetService catalogGetService,
        SellingPriceCalculator priceCalculator,
        ProrationConfigurationGetService prorationConfigurationGetService,
        OrderService orderService,
        ChangeOrderService changeOrderService,
        InvoiceAmountCalculator invoiceAmountCalculator,
        MetricsService metricsService,
        TenantSettingService tenantSettingService,
        SubscriptionGetService subscriptionGetService,
        MissingOrderChargesService missingOrderChargesService,
        UserService userService,
        FeatureService featureService,
        OrderDomainCreateSchema orderCreateSchema
    ) {
        super(s3ClientProvider, tenantIdProvider);
        this.catalogService = catalogService;
        this.catalogGetService = catalogGetService;
        this.priceCalculator = priceCalculator;
        this.prorationConfigurationGetService = prorationConfigurationGetService;
        this.orderService = orderService;
        this.changeOrderService = changeOrderService;
        this.invoiceAmountCalculator = invoiceAmountCalculator;
        this.metricsService = metricsService;
        this.tenantSettingService = tenantSettingService;
        this.subscriptionGetService = subscriptionGetService;
        this.missingOrderChargesService = missingOrderChargesService;
        this.userService = userService;
        this.featureService = featureService;
        this.orderCreateSchema = orderCreateSchema;
    }

    // Add new order taking into account potential percent_of charges which requires a dryrun first to get the list amount
    protected Order addNewOrder(Order order, Boolean isDryRun) {
        // if there are any percent of charge items, store their target amounts first
        Map<String, BigDecimal> percentOfChargeItemTargetAmounts = getPercentOfChargeItemAmounts(order);
        missingOrderChargesService.findAndAddMissingOrderCharges(order);
        orderService.findAndAddMissingPlanTemplates(order);

        calculateLineItemPriceAndDiscounts(order, order.getStartDate(), order.getEndDate(), Optional.empty());
        setLineItemIsRamp(order);
        Order addedOrder;

        if (percentOfChargeItemTargetAmounts.isEmpty()) {
            addedOrder = orderService.addOrder(order, isDryRun);
        } else {
            // if there are any percent of charges, run dry run first to determine total order amount, including percent of charge list amount with no discount
            addedOrder = orderService.addOrder(order, true);
            // based on the target amount, calculate the appropriate discount amount for percent of charge items
            setPercentOfDiscount(percentOfChargeItemTargetAmounts, addedOrder);
            addedOrder = orderService.addOrder(addedOrder, isDryRun);
        }
        return addedOrder;
    }

    protected void markRowsAfterCreation(
        List<CsvImportRow<OrderDomainCreateRow, Order>> importRows,
        Map<String, Exception> failedCreations,
        Map<String, Order> successfulOrders
    ) {
        // pass through import rows once more for classification
        importRows
            .stream()
            .filter(CsvImportRow::isValid)
            .forEach(row -> {
                String externalId = row.getImportEntity().getOrderExternalId();
                if (successfulOrders.containsKey(externalId)) {
                    row.setOutputEntity(successfulOrders.get(externalId));
                } else if (failedCreations.containsKey(externalId)) {
                    row.markError(failedCreations.get(externalId));
                } else {
                    row.markError("invalid import state valid row neither succeeded or failed contact support");
                }
            });
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    protected void calculateLineItemPriceAndDiscounts(
        Order order,
        Instant subscriptionStart,
        Instant subscriptionEnd,
        Optional<Subscription> subscription
    ) {
        if (CollectionUtils.isEmpty(order.getLineItems())) {
            return;
        }

        List<String> planIds = order.getLineItems().stream().map(OrderLineItem::getPlanId).filter(StringUtils::isNotBlank).distinct().toList();
        List<Plan> plans = catalogGetService.getPlansByPlanIds(planIds);
        List<Charge> charges = plans.stream().map(Plan::getCharges).flatMap(Collection::stream).toList();

        orderService.updateOrderLineItemWithCurrencyConversionRateId(order, plans, charges);

        Set<String> zeroListAmountSubscriptionChargeIds = getZeroListAmountSubscriptionChargeIds(order, subscription);

        order
            .getLineItems()
            .stream()
            .peek(item -> populatePredefinedDiscountPercents(order, item))
            .filter(item -> shouldCalculateItemPrice(order, item, zeroListAmountSubscriptionChargeIds))
            .forEach(item -> {
                Charge charge = catalogGetService.getChargeByChargeId(item.getChargeId());
                calculateItemPriceAndDiscount(item, charge, order, subscriptionStart, subscriptionEnd == null ? item.getEndDate() : subscriptionEnd);
            });
    }

    private boolean shouldCalculateItemPrice(Order order, OrderLineItem item, Set<String> zeroListAmountSubscriptionChargeIds) {
        if (item.getAmount() == null) {
            return false;
        }

        if (order.getOrderType() == OrderType.NEW || order.getOrderType() == OrderType.RENEWAL || item.getAction() == ActionType.ADD) {
            return true;
        }

        return item.getAction() == ActionType.UPDATE && zeroListAmountSubscriptionChargeIds.contains(item.getBaseExternalSubscriptionChargeId());
    }

    protected void setLineItemIsRamp(Order order) {
        List<OrderLineItem> lineItems = order.getLineItems();
        if (CollectionUtils.isEmpty(lineItems)) {
            return;
        }
        List<List<OrderLineItem>> rampItems = getRampItems(lineItems);
        if (rampItems.isEmpty()) {
            return;
        }

        // set ramp flag to true for all ramp items
        rampItems.forEach(li -> li.forEach(ri -> ri.setIsRamp(true)));
    }

    private List<List<OrderLineItem>> getRampItems(List<OrderLineItem> lineItems) {
        return lineItems
            .stream()
            .filter(li -> li.getEffectiveDate() != null && li.getEndDate() != null)
            .collect(Collectors.groupingBy(OrderLineItem::getChargeId))
            .values()
            .stream()
            .filter(li -> li.size() > 1 && areLineItemsContinuous(li))
            .toList();
    }

    private boolean areLineItemsContinuous(List<OrderLineItem> lineItems) {
        AtomicBoolean isContinuous = new AtomicBoolean(true);

        lineItems.sort(Comparator.comparing(OrderLineItem::getEffectiveDate));
        lineItems
            .stream()
            .reduce((a, b) -> {
                if (!a.getEndDate().equals(b.getEffectiveDate())) {
                    isContinuous.set(false);
                }
                return b;
            });

        return isContinuous.get();
    }

    protected Map<String, BigDecimal> getPercentOfChargeItemAmounts(Order order) {
        Map<String, BigDecimal> percentOfChargeItemAmounts = new HashMap<>();

        List<Charge> charges;
        try {
            charges = getOrderCharges(order);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("no line items in order {}", order.getExternalId());
            throw e;
        }
        Set<String> percentOfChargeIds = charges
            .stream()
            .filter(charge -> charge.getType() == ChargeType.PERCENTAGE_OF)
            .map(Charge::getChargeId)
            .collect(Collectors.toSet());

        List<OrderLineItem> percentOfOrderLineItems = order
            .getLineItems()
            .stream()
            .filter(orderLineItem -> percentOfChargeIds.contains(orderLineItem.getChargeId()))
            .toList();
        percentOfOrderLineItems.forEach(orderLineItem ->
            percentOfChargeItemAmounts.put(orderLineItem.getChargeId() + orderLineItem.getEffectiveDate(), orderLineItem.getAmount())
        );
        return percentOfChargeItemAmounts;
    }

    /**
     * use the {@link SellingPriceCalculator#calculateDiscountPercent(SellingPriceCalculator.SoldOrderLineItem, ProrationConfig, TimeZone, Charge)} to calculate
     * discounts if any for every line item in the order, the discount calculation is activated by {@link OrderLineItem#getAmount()}
     *
     * the {@link OrderLineItem#getAmount()} is called to copy the value and is set to null, the actual line item amount is calculated
     * by {@link OrderService}
     *
     * NOTE: both {@link OrderLineItem#getEffectiveDate()} and {@link OrderLineItem#getEndDate()} should be present if Order amount is provided
     * otherwise the function throws an exception and errors out
     * for the actual discount calculation logic please refer {@link SellingPriceCalculator#calculateDiscountPercent(SellingPriceCalculator.SoldOrderLineItem, ProrationConfig, TimeZone, Charge)}
     * @param item the item under consideration for which discount needs to be calculated
     * @param order the order to import
     * @param subscriptionStart subscription start date, used to calculate the correct billing cycle if different from order start date
     * @param subscriptionEnd subscription end date, used to calculate proration amounts
     */
    void calculateItemPriceAndDiscount(OrderLineItem item, Charge charge, Order order, Instant subscriptionStart, Instant subscriptionEnd) {
        BigDecimal amount = item.getAmount();
        // amount will be calculated by order service we just copy value for use here
        item.setAmount(null);

        if (amount.compareTo(BigDecimal.ZERO) < 0 && !charge.isCustom()) {
            String message = String.format("amount %s should be a decimal greater then 0.00", amount);
            throw new IllegalArgumentException(message);
        }
        if (item.getEffectiveDate() == null || item.getEndDate() == null) {
            throw new IllegalArgumentException("if order amount is provided then item start and end date both should be present");
        }
        if (charge.isCustom() && item.getListUnitPrice() == null) {
            setCustomChargeListUnitPrice(
                order,
                order.getBillingCycle(),
                order.getBillingAnchorDate(),
                item,
                subscriptionStart,
                subscriptionEnd,
                amount,
                charge
            );
            return;
        }

        if (charge.getType() == ChargeType.PERCENTAGE_OF) {
            LOGGER.info("import for percent of charge {} will not calculate discounts", charge.getChargeId());
            return;
        }

        BigDecimal discountAmount = calculateItemDiscount(order, item, amount, charge, subscriptionStart);

        if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
            DiscountDetail discount = new DiscountDetail();
            discount.setPercent(discountAmount);
            item.setDiscounts(List.of(discount));
        }

        if (discountAmount.compareTo(BigDecimal.ZERO) < 0) {
            // negative discount indicates a price override
            BigDecimal overrideRatio = discountAmount.negate();
            // override ratio is expressed relative to 1 (100%)
            item.setListPriceOverrideRatio(Numbers.makeInputScale(overrideRatio.add(BigDecimal.ONE)));
        }
    }

    private void setCustomChargeListUnitPrice(
        Order order,
        Recurrence billingCycle,
        Instant billingAnchorDate,
        OrderLineItem item,
        Instant subscriptionStart,
        Instant subscriptionEnd,
        BigDecimal amount,
        Charge charge
    ) {
        BigDecimal quantity = getQuantity(item, charge);

        BigDecimal listUnitPrice = calculateCustomChargeListUnitPrice(
            order,
            billingCycle,
            billingAnchorDate,
            item,
            subscriptionStart,
            subscriptionEnd,
            amount,
            charge,
            quantity
        );

        item.setListUnitPrice(Numbers.makePriceDisplayScale(listUnitPrice));
    }

    private BigDecimal calculateCustomChargeListUnitPrice(
        Order order,
        Recurrence billingCycle,
        Instant billingAnchorDate,
        OrderLineItem item,
        Instant subscriptionStart,
        Instant subscriptionEnd,
        BigDecimal amount,
        Charge charge,
        BigDecimal quantity
    ) {
        // if quantity is 0, try to get charge list price.
        if (quantity.compareTo(BigDecimal.ZERO) == 0 && charge.getListUnitPriceByQuantity(0L, Optional.empty()) == null) {
            // if charge doesn't have a list price (tiered, MAP, custom etc.), set to 0
            return BigDecimal.ZERO;
        }

        if (charge.getType() == ChargeType.RECURRING) {
            // for recurring charges, calculate the proration ratio of the item term
            BigDecimal prorationRatio = priceCalculator.calculateTotalProrationRatio(
                subscriptionStart,
                subscriptionEnd,
                item.getEffectiveDate(),
                item.getEndDate(),
                prorationConfigurationGetService.resolveProrationConfig(order),
                billingCycle,
                billingAnchorDate == null ? subscriptionStart : billingAnchorDate,
                tenantSettingService.getTenantSetting().getDefaultTimeZone()
            );
            BigDecimal amountPerBillingCycle = prorationRatio.compareTo(BigDecimal.ZERO) == 0
                ? BigDecimal.ZERO
                : Numbers.scaledDivide(amount, prorationRatio);
            return Numbers.scaledDivide(amountPerBillingCycle, quantity);
        } else if (charge.getType() == ChargeType.ONE_TIME) {
            // for one time, proration isn't considered, if there is a quantity, unit price is total price / quantity
            return Numbers.scaledDivide(amount, quantity);
        } else {
            throw new IllegalStateException(
                String.format("custom charge with id '%s' has invalid charge type '%s'", charge.getChargeId(), charge.getType())
            );
        }
    }

    private static BigDecimal getQuantity(OrderLineItem item, Charge charge) {
        BigDecimal quantity;

        if (charge.getChargeModel() == ChargeModel.FLAT_FEE) {
            quantity = BigDecimal.ONE;
        } else if (charge.getChargeModel() == ChargeModel.PER_UNIT) {
            quantity = BigDecimal.valueOf(item.getQuantity());
        } else {
            throw new IllegalStateException(
                String.format("custom charge with id '%s' has invalid charge model '%s'", charge.getChargeId(), charge.getChargeModel())
            );
        }
        return quantity;
    }

    protected void setPercentOfDiscount(Map<String, BigDecimal> percentOfChargeItemAmounts, Order order) {
        order
            .getLineItems()
            .forEach(orderLineItem -> {
                String percentOfItemKey = orderLineItem.getChargeId() + orderLineItem.getEffectiveDate();
                // todo: assuming only a single instance per chargeId
                if (percentOfChargeItemAmounts.containsKey(percentOfItemKey)) {
                    BigDecimal targetAmount = percentOfChargeItemAmounts.get(percentOfItemKey);
                    BigDecimal listAmount = orderLineItem.getListAmount();

                    if (targetAmount.compareTo(listAmount) > 0) {
                        throw new IllegalArgumentException(
                            String.format(
                                "target amount (%s) > list amount (%s) for charge %s",
                                targetAmount,
                                listAmount,
                                orderLineItem.getChargeId()
                            )
                        );
                    }

                    BigDecimal calculatedDiscount = DiscountCalculator.calculateDiscountRatio(targetAmount, listAmount);

                    DiscountDetail discount = new DiscountDetail();
                    discount.setPercent(calculatedDiscount);
                    orderLineItem.setDiscounts(List.of(discount));
                }
            });
    }

    //TODO: push this method to OrderService ASAP after this iteration
    BigDecimal calculateItemDiscount(Order order, OrderLineItem item, BigDecimal amount, Charge charge, Instant subscriptionStart) {
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);

        boolean skipV1 = order.getBillingAnchorDate() != null && order.getBillingAnchorDate().compareTo(subscriptionStart) > 0;

        BigDecimal calculatedDiscountV2 = calculateItemDiscountV2(order, item, amount, charge, subscriptionStart, prorationConfig);

        if (skipV1) {
            LOGGER.info("Billing anchor date != subscription start date. Skipping V1 discount calculation");
        } else {
            BigDecimal calculatedDiscountV1 = calculateItemDiscountV1(order, item, amount, charge, subscriptionStart, prorationConfig);
            if (calculatedDiscountV1.compareTo(calculatedDiscountV2) != 0) {
                LOGGER.warn(
                    String.format(
                        "Calculated discount ratios different for order external Id %s, charge %s. V1: %s, V2: %s",
                        order.getExternalId(),
                        charge.getChargeId(),
                        calculatedDiscountV1,
                        calculatedDiscountV2
                    )
                );
            }
        }

        if (
            (calculatedDiscountV2.compareTo(BigDecimal.ZERO) < 0 && !charge.getIsListPriceEditable()) ||
            calculatedDiscountV2.compareTo(BigDecimal.ONE) > 0
        ) {
            throw new InvalidInputException(
                String.format(
                    "Order discount for charge %s should be a number between 0 and 1 but was %s",
                    charge.getChargeId(),
                    calculatedDiscountV2
                )
            );
        }

        return calculatedDiscountV2;
    }

    // todo: Keeping this here temporarily to run in parallel against V2 and log any difference. Remove when V2 fully vetted
    private BigDecimal calculateItemDiscountV1(
        Order order,
        OrderLineItem item,
        BigDecimal amount,
        Charge charge,
        Instant subscriptionStart,
        ProrationConfig prorationConfig
    ) {
        BigDecimal billingPeriodListAmount;

        Recurrence orderBillingCycle = order.getBillingCycle();

        if (orderBillingCycle.getCycle() == Cycle.PAID_IN_FULL) {
            // if billing cycle is paid in full use charge cycle instead
            // todo: should consolidate with invoice calculation logic
            orderBillingCycle = charge.getRecurrence();
        }

        if (charge.getType().isRecurring()) {
            var chargeCycleListAmount = invoiceAmountCalculator.calculateRecurringItemPrice(item, charge).getListAmount();
            billingPeriodListAmount = invoiceAmountCalculator.getScaledCost(chargeCycleListAmount, charge.getRecurrence(), orderBillingCycle);
        } else if (charge.getType() == ChargeType.PREPAID) {
            billingPeriodListAmount = invoiceAmountCalculator.calculatePrepaidListAmount(item, charge).getListAmount();
        } else if (charge.getType() == ChargeType.ONE_TIME) {
            billingPeriodListAmount = invoiceAmountCalculator.calculateOneTimeListAmount(item, charge).getListAmount();
        } else if (charge.getType() == ChargeType.USAGE) {
            // order line amount for usage is zero since quantity is not known
            billingPeriodListAmount = BigDecimal.ZERO;
        } else {
            throw new InvalidInputException(
                String.format(
                    "Unexpected charge type %s, for order %s, order line item %s",
                    charge.getType(),
                    item.getOrderId(),
                    item.getOrderLineId()
                )
            );
        }

        if (Numbers.isZero(billingPeriodListAmount)) {
            return BigDecimal.ZERO;
        }

        if (item.getListPriceOverrideRatio() != null) {
            billingPeriodListAmount = Numbers.makeCurrencyScale(billingPeriodListAmount.multiply(item.getListPriceOverrideRatio()));
        }

        // since normal order calculation billing period amounts are rounded, round here too to remove division rounding artifacts
        billingPeriodListAmount = Numbers.makeCurrencyScale(billingPeriodListAmount);

        var soldItem = new SellingPriceCalculator.SoldOrderLineItem()
            .withOrderLineItemAmount(amount)
            .withSubscriptionStart(subscriptionStart)
            .withLineItemStart(item.getEffectiveDate())
            .withLineItemEnd(item.getEndDate())
            .withBillingCycleListPrice(billingPeriodListAmount)
            .withQuantity(item.getQuantity())
            .withBillingCycle(orderBillingCycle)
            .withBillingAnchorDate(order.getBillingAnchorDate() == null ? subscriptionStart : order.getBillingAnchorDate());

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        return priceCalculator.calculateDiscountPercent(soldItem, prorationConfig, timeZone, charge);
    }

    private BigDecimal calculateItemDiscountV2(
        Order order,
        OrderLineItem item,
        BigDecimal amount,
        Charge charge,
        Instant subscriptionStart,
        ProrationConfig prorationConfig
    ) {
        BigDecimal listAmount = priceCalculator.calculateItemListAmount(subscriptionStart, order, item, charge, prorationConfig);
        if (Numbers.isZero(listAmount)) {
            return BigDecimal.ZERO;
        }

        BigDecimal discountAmount = listAmount.subtract(amount);

        return Numbers.scaledDivide(discountAmount, listAmount);
    }

    private static void populatePredefinedDiscountPercents(Order order, OrderLineItem item) {
        if (CollectionUtils.isEmpty(item.getPredefinedDiscounts())) {
            return;
        }
        if (CollectionUtils.isEmpty(order.getPredefinedDiscounts())) {
            throw new InvalidInputException("order line has predefined discount but order does not");
        }
        Map<String, TenantDiscount> predefinedDiscountMap = order
            .getPredefinedDiscounts()
            .stream()
            .collect(Collectors.toMap(TenantDiscount::getId, Function.identity()));
        for (var discount : item.getPredefinedDiscounts()) {
            if (!predefinedDiscountMap.containsKey(discount.getId())) {
                throw new InvalidInputException(String.format("item discount %s is not present on order", discount.getName()));
            }
            var percent = predefinedDiscountMap.get(discount.getId()).getPercent();
            discount.setPercent(percent);
        }
    }

    protected List<Order> extractValidOrders(List<CsvImportRow<OrderDomainCreateRow, Order>> importRows, Optional<User> importedByUser) {
        Map<String, CsvImportRow<OrderDomainCreateRow, Order>> validOrderRows = getValidOrderRows(importRows);
        // process line items
        importRows
            .stream()
            .filter(row -> row.isValid() && row.getImportEntity().getOrderLineItem() != null)
            .forEach(lineItemRow -> processLineItem(validOrderRows, lineItemRow));

        validOrderRows.values().forEach(orderRow -> validateOrderMetrics(orderRow, importedByUser));

        return validOrderRows.values().stream().map(CsvImportRow::getImportEntity).map(OrderDomainCreateRow::getOrder).toList();
    }

    private Map<String, CsvImportRow<OrderDomainCreateRow, Order>> getValidOrderRows(List<CsvImportRow<OrderDomainCreateRow, Order>> importRows) {
        Map<String, List<String>> invalidOrderExternalIdToErrorMessagesMap = importRows
            .stream()
            .filter(row -> !row.isValid() && row.getImportEntity() != null && row.getImportEntity().getOrderExternalId() != null)
            .collect(
                Collectors.groupingBy(
                    row -> row.getImportEntity().getOrderExternalId(),
                    Collectors.mapping(CsvImportRow::getErrorMessage, Collectors.toList())
                )
            );

        // mark any valid order or line items based on previous find
        importRows
            .stream()
            .filter(row -> row.isValid() && invalidOrderExternalIdToErrorMessagesMap.containsKey(row.getImportEntity().getOrderExternalId()))
            .forEach(row -> {
                String message = String.join(", ", invalidOrderExternalIdToErrorMessagesMap.get(row.getImportEntity().getOrderExternalId()));
                row.markError(message);
            });

        // if there duplicate valid order(s) then fail the rest except one of them
        importRows
            .stream()
            .filter(row -> row.isValid() && row.getImportEntity().getOrder() != null)
            .collect(Collectors.groupingBy(row -> row.getImportEntity().getOrderExternalId()))
            .values()
            .forEach(groupedRows -> {
                // if there is one ignore
                if (groupedRows.size() <= 1) {
                    return;
                }

                // more than one validate that they are all the same
                Order firstOrder = groupedRows.get(0).getImportEntity().getOrder();
                AtomicBoolean allSame = new AtomicBoolean(true);
                groupedRows
                    .stream()
                    .skip(1)
                    .forEach(row -> {
                        if (!orderDataEquals(row.getImportEntity().getOrder(), firstOrder)) {
                            allSame.set(false);
                        }
                    });
                if (!allSame.get()) {
                    groupedRows.forEach(row -> {
                        String message = String.format(
                            "duplicate order external id: %s with different order data",
                            row.getImportEntity().getOrderExternalId()
                        );
                        row.markError(message);
                    });
                }
            });

        return importRows
            .stream()
            .filter(row -> row.isValid() && row.getImportEntity().getOrder() != null)
            .collect(Collectors.toMap(row -> row.getImportEntity().getOrderExternalId(), Function.identity(), (a, b) -> a, LinkedHashMap::new));
    }

    private List<Charge> getOrderCharges(Order order) {
        Validator.validateCollectionNotEmpty(order.getLineItems(), "order line items");
        List<String> chargeIds = order.getLineItems().stream().map(OrderLineItem::getChargeId).distinct().collect(Collectors.toList());
        return catalogGetService.getChargesByChargeId(chargeIds);
    }

    private void processLineItem(
        Map<String, CsvImportRow<OrderDomainCreateRow, Order>> validOrderRows,
        CsvImportRow<OrderDomainCreateRow, Order> lineItemRow
    ) {
        String orderExternalId = lineItemRow.getImportEntity().getOrderExternalId();
        if (validOrderRows.containsKey(orderExternalId)) {
            var order = validOrderRows.get(orderExternalId).getImportEntity().getOrder();
            var orderLineItem = lineItemRow.getImportEntity().getOrderLineItem();
            order.addLineItem(orderLineItem);
        } else {
            String message = String.format("line item does not point valid order external id:%s in this import", orderExternalId);
            lineItemRow.markError(message);
        }
    }

    private void validateOrderMetrics(CsvImportRow<OrderDomainCreateRow, Order> orderRow, Optional<User> importedByUser) {
        Order order = orderRow.getImportEntity().getOrder();

        BigDecimal expectedEntryArr = orderRow.getImportEntity().getExpectedEntryArr();
        BigDecimal expectedExitArr = orderRow.getImportEntity().getExpectedExitArr();
        BigDecimal expectedTcv = orderRow.getImportEntity().getExpectedTcv();
        if (expectedEntryArr == null && expectedExitArr == null && expectedTcv == null) {
            return;
        }

        boolean isAutoType = orderRow.getImportEntity().isAutoType();
        CSVRecord record = orderRow.getImportEntity().getInputRecord();

        Order addedOrder;
        try {
            addedOrder = addOrder(order, importedByUser, isAutoType, record, true);
        } catch (Exception e) {
            orderRow.markError(e.getMessage());
            return;
        }
        Metrics orderMetrics = metricsService.getOrderMetrics(addedOrder, addedOrder.getStartDate());
        if (expectedEntryArr != null && orderMetrics.getEntryArr().compareTo(expectedEntryArr) != 0) {
            String message = String.format("expected entry arr %s does not match actual entry arr %s", expectedEntryArr, orderMetrics.getEntryArr());
            orderRow.markError(message);
        }
        if (expectedExitArr != null && orderMetrics.getExitArr().compareTo(expectedExitArr) != 0) {
            String message = String.format("expected exit arr %s does not match actual exit arr %s", expectedExitArr, orderMetrics.getExitArr());
            orderRow.markError(message);
        }
        if (expectedTcv != null && orderMetrics.getTcv().compareTo(expectedTcv) != 0) {
            String message = String.format("expected tcv %s does not match actual tcv %s", expectedTcv, orderMetrics.getTcv());
            orderRow.markError(message);
        }
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    protected Order addOrder(Order order, Optional<User> importedByUser, boolean isAutoType, CSVRecord record, Boolean isDryRun) {
        if (isAutoType) {
            reverifyOrderType(order, order.getOrderType());
        } else if (
            order.getOrderType() == OrderType.AMENDMENT || order.getOrderType() == OrderType.RENEWAL || order.getOrderType() == OrderType.CANCEL
        ) {
            Subscription subscription = orderCreateSchema.findSubscription(record, order.getAccountId(), order.getOrderType());
            if (!subscription.getAccountId().equals(order.getAccountId())) {
                throw new InvalidInputException("Subscription does not belong to the account entered");
            }
            orderCreateSchema.setChangeOrderFields(order, subscription);
            orderCreateSchema.setAutoRenew(record, order, subscription);
        }

        Order addedOrder;
        order.setSource(OrderSource.IMPORT);
        order.setOwnerId(importedByUser.map(User::getUserId).orElse(null));
        if (order.getOrderType() == OrderType.AMENDMENT) {
            Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
            order.setBillingAnchorDate(subscription.getBillingAnchorDate());
            order.setEndDate(subscription.getEndDate());
            mergeDebookRebookLineItems(order);
            hydrateOrderLineItems(order.getLineItems(), subscription);
            missingOrderChargesService.findAndAddMissingOrderCharges(order);
            orderService.findAndAddMissingPlanTemplates(order);
            calculateLineItemPriceAndDiscounts(order, subscription.getStartDate(), subscription.getEndDate(), Optional.of(subscription));
            setLineItemIsRamp(order);
            addedOrder = changeOrderService.createChangeOrder(order, isDryRun, true, true);
        } else if (order.getOrderType() == OrderType.CANCEL) {
            Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
            order.setExternalSubscriptionId(subscription.getSubscriptionId());
            order.setAutoRenew(false);
            addedOrder = changeOrderService.createChangeOrder(order, isDryRun, false, true);
        } else if (order.getOrderType() == OrderType.RENEWAL) {
            missingOrderChargesService.findAndAddMissingOrderCharges(order);
            calculateLineItemPriceAndDiscounts(order, order.getStartDate(), order.getEndDate(), Optional.empty());
            setLineItemIsRamp(order);
            addedOrder = orderService.renewSubscription(order, isDryRun);
        } else {
            if (order.getBillingAnchorDate() == null) {
                order.setBillingAnchorDate(order.getStartDate());
            }
            addedOrder = addNewOrder(order, isDryRun);
        }
        return addedOrder;
    }

    // a previous NEW or RENEW order in the same import batch could've been executed for the same account, causing this order to become a different type and have different field values
    private void reverifyOrderType(Order order, OrderType previousOrderType) {
        ImmutablePair<OrderType, Subscription> orderTypeAndSubscription = orderCreateSchema.determineOrderType(
            order.getStartDate(),
            order.getEndDate(),
            order.getAccountId()
        );

        if (orderTypeAndSubscription.getLeft() != previousOrderType) {
            order.setOrderType(orderTypeAndSubscription.getLeft());
            Subscription subscription = orderTypeAndSubscription.getRight();
            orderCreateSchema.setChangeOrderFields(order, subscription);
        }
    }

    // Find subscription charge being updated with $0 list amount
    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private Set<String> getZeroListAmountSubscriptionChargeIds(Order order, Optional<Subscription> subscriptionOptional) {
        if (
            !featureService.isEnabled(Feature.UPDATE_ZERO_BASE_LINE_ITEM) ||
            order.getOrderType() != OrderType.AMENDMENT ||
            subscriptionOptional.isEmpty()
        ) {
            return Set.of();
        }

        Subscription subscription = subscriptionOptional.get();

        List<OrderLineItem> updateLineItems = order.getLineItems().stream().filter(lineItem -> lineItem.getAction() == ActionType.UPDATE).toList();
        List<String> baseSubscriptionChargeIds = updateLineItems
            .stream()
            .map(OrderLineItem::getBaseExternalSubscriptionChargeId)
            .filter(Objects::nonNull)
            .toList();

        // base subscription charges being updated
        List<SubscriptionCharge> baseSubscriptionCharges = subscription
            .getCharges()
            .stream()
            .filter(subscriptionCharge -> baseSubscriptionChargeIds.contains(subscriptionCharge.getSubscriptionChargeId()))
            .toList();

        List<String> chargeIds = baseSubscriptionCharges.stream().map(SubscriptionCharge::getChargeId).toList();
        Map<String, Charge> chargeMap = catalogGetService.getChargeMapByChargeIds(chargeIds);

        // ids of subscription charges with $0 list amount being updated
        return baseSubscriptionCharges
            .stream()
            .filter(subscriptionCharge -> ChangeOrderService.hasZeroListAmount(subscriptionCharge, chargeMap.get(subscriptionCharge.getChargeId())))
            .map(SubscriptionCharge::getSubscriptionChargeId)
            .collect(Collectors.toSet());
    }

    protected void mergeDebookRebookLineItems(Order order) {
        Map<String, OrderLineItem> orderLineItemMap = order
            .getLineItems()
            .stream()
            .filter(item -> item.getAction() == ActionType.UPDATE)
            .collect(
                Collectors.toMap(
                    item -> item.getChargeId() + item.getAction(),
                    Function.identity(),
                    (a, b) -> {
                        a.setQuantity(a.getQuantity() + b.getQuantity());
                        // amount will be calculated by order service
                        a.setAmount(null);
                        return a;
                    }
                )
            );

        List<OrderLineItem> nonUpdateLineItems = order.getLineItems().stream().filter((item -> item.getAction() != ActionType.UPDATE)).toList();
        List<OrderLineItem> mergedLineItems = new ArrayList<>(orderLineItemMap.values());
        mergedLineItems.addAll(nonUpdateLineItems);
        order.setLineItems(mergedLineItems);
    }

    protected void hydrateOrderLineItems(List<OrderLineItem> orderLineItems, Subscription subscription) {
        orderLineItems
            .stream()
            .filter(orderLineItem -> orderLineItem.getAction() != ActionType.ADD)
            .forEach(orderLineItem -> {
                // If the order line item is already associated with a subscription charge, skip hydration
                if (orderLineItem.getSubscriptionChargeId() != null || StringUtils.isNotBlank(orderLineItem.getBaseExternalSubscriptionChargeId())) {
                    return;
                }

                SubscriptionCharge matchingSubscriptionCharge = getMatchingSubscriptionCharge(orderLineItem, subscription);

                switch (orderLineItem.getAction()) {
                    case UPDATE -> {
                        setOrderLineItemChargeIds(orderLineItem, matchingSubscriptionCharge);
                        orderLineItem.setQuantity(orderLineItem.getQuantity() + matchingSubscriptionCharge.getQuantity());
                        BigDecimal priceOverrideRatio = matchingSubscriptionCharge.getListPriceOverrideRatio();
                        orderLineItem.setListPriceOverrideRatio(priceOverrideRatio);
                    }
                    case REMOVE -> {
                        setOrderLineItemChargeIds(orderLineItem, matchingSubscriptionCharge);
                        orderLineItem.setQuantity(0);
                    }
                    case NONE -> {
                        setOrderLineItemChargeIds(orderLineItem, matchingSubscriptionCharge);
                        orderLineItem.setQuantity(matchingSubscriptionCharge.getQuantity());
                    }
                    default -> LOGGER.info(
                        "Received order action {} and hydrateOrderLineItems NOTE decorating order line item data",
                        orderLineItem.getAction()
                    );
                }
            });
    }

    private static SubscriptionCharge getMatchingSubscriptionCharge(OrderLineItem orderLineItem, Subscription subscription) {
        List<SubscriptionCharge> matchingCharges = subscription
            .getCharges()
            .stream()
            .filter(subscriptionCharge -> subscriptionCharge.getChargeId().equals(orderLineItem.getChargeId()))
            .toList();

        if (matchingCharges.isEmpty()) {
            throw new InvalidInputException(
                String.format("No matching charge %s found in target subscription %s", orderLineItem.getChargeId(), subscription.getSubscriptionId())
            );
        }

        if (matchingCharges.size() == 1) {
            // if there is only a single charge found, return that
            return matchingCharges.get(0);
        }

        // find subscription charges with matching end dates
        List<SubscriptionCharge> subscriptionChargesWithMatchingEndDates = matchingCharges
            .stream()
            .filter(subscriptionCharge -> subscriptionCharge.getEndDate().equals(orderLineItem.getEndDate()))
            .toList();

        if (subscriptionChargesWithMatchingEndDates.size() == 1) {
            return subscriptionChargesWithMatchingEndDates.get(0);
        }

        // find subscription charges with matching quantities in the case of removal
        List<SubscriptionCharge> subscriptionChargesWithMatchingQuantity = matchingCharges
            .stream()
            .filter(subscriptionCharge -> subscriptionCharge.getQuantity() == orderLineItem.getQuantity())
            .toList();
        if (subscriptionChargesWithMatchingQuantity.size() == 1) {
            return subscriptionChargesWithMatchingQuantity.get(0);
        }

        throw new InvalidInputException("More than 1 matching subscription charge found for charge Id: " + orderLineItem.getChargeId());
    }

    private static void setOrderLineItemChargeIds(OrderLineItem orderLineItem, SubscriptionCharge matchingSubscriptionCharge) {
        orderLineItem.setSubscriptionChargeId(matchingSubscriptionCharge.getId());
        orderLineItem.setBaseExternalSubscriptionChargeId(matchingSubscriptionCharge.getSubscriptionChargeId());
    }

    private boolean orderDataEquals(Order order1, Order order2) {
        Validator.validateNonNullArgument(order1, "order1");
        Validator.validateNonNullArgument(order2, "order2");
        return (
            Objects.equals(order1.getExternalId(), order2.getExternalId()) &&
            Objects.equals(order1.getSfdcOpportunityId(), order2.getSfdcOpportunityId()) &&
            Objects.equals(order1.getSfdcOpportunityName(), order2.getSfdcOpportunityName()) &&
            Objects.equals(order1.getName(), order2.getName()) &&
            order1.getOrderType() == order2.getOrderType() &&
            Objects.equals(order1.getSubscriptionUuid(), order2.getSubscriptionUuid()) &&
            Objects.equals(order1.getAccountId(), order2.getAccountId()) &&
            Objects.equals(order1.getBillingContactId(), order2.getBillingContactId()) &&
            Objects.equals(order1.getShippingContactId(), order2.getShippingContactId()) &&
            Objects.equals(order1.getStartDate(), order2.getStartDate()) &&
            Objects.equals(order1.getEndDate(), order2.getEndDate()) &&
            Objects.equals(order1.getBillingCycle(), order2.getBillingCycle()) &&
            Objects.equals(order1.getBillingAnchorDate(), order2.getBillingAnchorDate()) &&
            order1.getBillingTerm() == order2.getBillingTerm() &&
            Objects.equals(order1.getPaymentTerm(), order2.getPaymentTerm())
        );
    }
}
