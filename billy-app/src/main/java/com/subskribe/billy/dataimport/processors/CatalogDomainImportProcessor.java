package com.subskribe.billy.dataimport.processors;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.aws.s3.S3ClientProvider;
import com.subskribe.billy.customfield.CustomFieldProxy;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.dataimport.model.DataImport;
import com.subskribe.billy.dataimport.model.ImportStatus;
import com.subskribe.billy.dataimport.schema.CsvImportRow;
import com.subskribe.billy.dataimport.schema.catalog.CatalogDomainInputRow;
import com.subskribe.billy.dataimport.schema.catalog.CatalogDomainOutputRow;
import com.subskribe.billy.dataimport.schema.catalog.CatalogDomainSchema;
import com.subskribe.billy.document.model.DocumentConfiguration;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.LedgerAccountMapping;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.model.ChargeDefaultAttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ChargeService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogService;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.resources.json.product.ProductInputJson;
import com.subskribe.billy.resources.json.product.ProductMapper;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class CatalogDomainImportProcessor extends BaseProcessor implements ImportProcessor {

    private final CatalogDomainSchema catalogDomainSchema;

    private final ProductCatalogService productCatalogService;

    private final ProductCatalogGetService productCatalogGetService;

    private final ProductMapper productMapper;

    private final PlanMapper planMapper;

    private final DocumentConfiguration documentConfiguration;

    private final AccountingService accountingService;

    private final CustomFieldProxy customFieldProxy;

    private final RateCardService rateCardService;

    private final CustomFieldAPIMapper customFieldAPIMapper;

    private final ChargeService chargeService;

    @Inject
    public CatalogDomainImportProcessor(
        S3ClientProvider s3ClientProvider,
        TenantIdProvider tenantIdProvider,
        CatalogDomainSchema catalogDomainSchema,
        ProductCatalogService productCatalogService,
        ProductCatalogGetService productCatalogGetService,
        BillyConfiguration billyConfiguration,
        AccountingService accountingService,
        CustomFieldProxy customFieldProxy,
        RateCardService rateCardService,
        ChargeService chargeService
    ) {
        super(s3ClientProvider, tenantIdProvider);
        this.catalogDomainSchema = catalogDomainSchema;
        this.productCatalogService = productCatalogService;
        this.productCatalogGetService = productCatalogGetService;
        this.accountingService = accountingService;
        this.customFieldProxy = customFieldProxy;
        this.rateCardService = rateCardService;
        this.chargeService = chargeService;
        productMapper = Mappers.getMapper(ProductMapper.class);
        planMapper = Mappers.getMapper(PlanMapper.class);
        documentConfiguration = billyConfiguration.getDocumentConfiguration();
        customFieldAPIMapper = Mappers.getMapper(CustomFieldAPIMapper.class);
    }

    @Override
    public void processImport(DataImport dataImport) {
        Validator.checkNonNullInternal(dataImport, "data import cannot be null here");
        Validator.checkStateInternal(ImportStatus.PROCESSING == dataImport.getStatus(), "import status should be processing");
        List<CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow>> entityRows = extractImportCsvRows(dataImport, catalogDomainSchema);
        EntityCaches entityCaches = new EntityCaches();
        createEntities(entityRows, entityCaches);
        markRowsAfterCreation(entityRows, entityCaches);
        writeOutputCsv(dataImport, documentConfiguration.getImportS3Bucket(), entityRows, catalogDomainSchema);
    }

    private void createEntities(List<CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow>> entityRows, EntityCaches entityCaches) {
        createProducts(entityRows, entityCaches);
        createPlans(entityRows, entityCaches);
        createCharges(entityRows, entityCaches);
    }

    private void createCharges(List<CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow>> entityRows, EntityCaches entityCaches) {
        var chargeRows = entityRows.stream().filter(CsvImportRow::isValid).filter(row -> row.getImportEntity().getChargeJson() != null).toList();
        chargeRows.forEach(row -> {
            try {
                upsertCharge(entityCaches, row);
            } catch (Exception ex) {
                entityCaches.failedCreations.put(Objects.requireNonNull(row.getImportEntity().getChargeJson()).getExternalId(), ex);
            }
        });
    }

    private void upsertCharge(EntityCaches entityCaches, CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow> row) {
        ChargeJson chargeJson = row.getImportEntity().getChargeJson();
        if (chargeJson == null) {
            return;
        }

        Collection<String> targetPlanExternalIds = row.getImportEntity().getTargetPlanExternalIds();
        String planExternalId = row.getImportEntity().getPlanExternalIdRef();

        String planId = getPlanIdByExternalId(entityCaches, planExternalId);

        populateTargetPlanIds(chargeJson, targetPlanExternalIds);

        Charge charge;
        if (StringUtils.isBlank(chargeJson.getId())) {
            String chargeId = chargeService.addChargeToExistingPlan(planId, planMapper.jsonToCharge(chargeJson));
            insertDefaultPriceAttributes(row, chargeId, chargeJson.getRateCardId());
            charge = productCatalogGetService.getChargeByChargeId(chargeId);
        } else {
            charge = chargeService.updateCharge(planId, planMapper.jsonToCharge(chargeJson));
        }
        updateChargeCustomFields(chargeJson, charge.getChargeId());
        populateLedgerAccountMappings(charge.getChargeId(), chargeJson.getLedgerAccountMapping());
        entityCaches.successfulCharges.put(chargeJson.getExternalId(), charge);
    }

    private void insertDefaultPriceAttributes(CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow> row, String chargeId, String rateCardId) {
        if (StringUtils.isBlank(rateCardId)) {
            return;
        }

        Map<String, String> attributeMap = row.getImportEntity().getDefaultAttributes();

        if (MapUtils.isEmpty(attributeMap)) {
            return;
        }

        List<PriceAttribute> chargeAttributes = rateCardService.getRateCardPriceAttributes(rateCardId);
        List<AttributeReference> attributeReferences = rateCardService.getAttributeReferencesFromNameAndValue(chargeAttributes, attributeMap);

        ChargeDefaultAttributeReferences chargeDefaultAttributeReferences = new ChargeDefaultAttributeReferences(null, chargeId, attributeReferences);
        rateCardService.upsertChargeDefaultAttributeReferences(chargeDefaultAttributeReferences);
    }

    private String getPlanIdByExternalId(EntityCaches entityCaches, String planExternalId) {
        if (StringUtils.isBlank(planExternalId)) {
            return null;
        }

        if (entityCaches.successfulPlans.containsKey(planExternalId)) {
            return entityCaches.successfulPlans.get(planExternalId).getPlanId();
        }

        Optional<String> planIdOptional = productCatalogGetService.getPlanIdByExternalId(planExternalId);
        return planIdOptional.orElseThrow(() ->
            new IllegalArgumentException(String.format("No plan with external id %s found for importing charge", planExternalId))
        );
    }

    private void populateTargetPlanIds(ChargeJson chargeJson, Collection<String> targetPlanExternalIds) {
        // get the list of plan ids corresponding to the target plan external ids
        if (CollectionUtils.isNotEmpty(targetPlanExternalIds)) {
            List<String> targetPlanIds = targetPlanExternalIds
                .stream()
                .map(targetPlanExternalId -> {
                    Optional<String> targetPlanIdOptional = productCatalogGetService.getPlanIdByExternalId(targetPlanExternalId);
                    return targetPlanIdOptional.orElseThrow(() ->
                        new IllegalArgumentException(
                            String.format("Could not find a target plan for percent of relationship with external id: %s", targetPlanExternalId)
                        )
                    );
                })
                .collect(Collectors.toList());
            chargeJson.setTargetPlanIds(targetPlanIds);
        }
    }

    private void populateLedgerAccountMappings(String chargeId, LedgerAccountMapping ledgerAccountMapping) {
        if (ledgerAccountMapping == null) {
            return;
        }
        accountingService.mapLedgerAccountsToCharge(chargeId, ledgerAccountMapping.getLedgerAccountIds());
    }

    private void createPlans(List<CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow>> entityRows, EntityCaches entityCaches) {
        var planRows = entityRows.stream().filter(CsvImportRow::isValid).filter(row -> row.getImportEntity().getPlanJson() != null).toList();

        planRows.forEach(row -> {
            try {
                upsertPlan(entityCaches, row);
            } catch (Exception ex) {
                entityCaches.failedCreations.put(Objects.requireNonNull(row.getImportEntity().getPlanJson()).getExternalId(), ex);
            }
        });
    }

    private void upsertPlan(EntityCaches entityCaches, CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow> row) {
        String productExternalId = row.getImportEntity().getProductExternalIdRef();

        PlanJson planJson = row.getImportEntity().getPlanJson();
        if (planJson == null) {
            return;
        }

        String planId = getProductIdByExternalId(entityCaches, productExternalId);
        planJson.setProductId(planId);

        // if externalId is duplicated then we need to validate that they are the same
        if (isPlanDataEqualAndMarkErrorIfNot(entityCaches, row, planJson)) return;

        Plan plan;
        if (StringUtils.isBlank(planJson.getId())) {
            plan = productCatalogService.addPlanAndCharges(planMapper.jsonToPlan(planJson));
        } else {
            productCatalogService.updatePlan(planMapper.jsonToPlan(planJson), false);
            plan = productCatalogGetService.getPlan(planJson.getId());
        }
        updatePlanCustomFields(planJson, plan.getPlanId());
        entityCaches.successfulPlans.put(planJson.getExternalId(), plan);
    }

    private String getProductIdByExternalId(EntityCaches entityCaches, String productExternalId) {
        if (StringUtils.isBlank(productExternalId)) {
            return null;
        }

        if (entityCaches.successfulProducts.containsKey(productExternalId)) {
            return entityCaches.successfulProducts.get(productExternalId).getProductId();
        }

        Optional<String> productIdOptional = productCatalogGetService.getProductIdByExternalId(productExternalId);
        return productIdOptional.orElseThrow(() ->
            new IllegalArgumentException(String.format("Product with external Id: %s not created or not found", productExternalId))
        );
    }

    private boolean isPlanDataEqualAndMarkErrorIfNot(
        EntityCaches entityCaches,
        CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow> row,
        PlanJson planJson
    ) {
        if (!entityCaches.successfulPlans.containsKey(planJson.getExternalId())) {
            return false;
        }

        Plan existing = entityCaches.successfulPlans.get(planJson.getExternalId());
        if (!planDataEquals(existing, planMapper.jsonToPlan(planJson))) {
            String message = String.format("duplicate external id: %s with different plan data", planJson.getExternalId());
            row.markError(message);
        }
        return true;
    }

    private void createProducts(List<CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow>> entityRows, EntityCaches entityCaches) {
        var productRows = entityRows
            .stream()
            .filter(CsvImportRow::isValid)
            .filter(row -> row.getImportEntity().getProductInputJson() != null)
            .toList();
        productRows.forEach(row -> {
            try {
                upsertProduct(entityCaches, row);
            } catch (Exception ex) {
                entityCaches.failedCreations.put(Objects.requireNonNull(row.getImportEntity().getProductInputJson()).getExternalId(), ex);
            }
        });
    }

    private void upsertProduct(EntityCaches entityCaches, CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow> row) {
        ProductInputJson productInputJson = row.getImportEntity().getProductInputJson();
        if (productInputJson == null) {
            return;
        }

        // if externalId is duplicated then we need to validate that they are the same
        if (isProductDataEqualAndMarkErrorIfNot(entityCaches, row, productInputJson)) return;

        Product product;
        if (StringUtils.isBlank(productInputJson.getId())) {
            product = productCatalogService.addProduct(productMapper.inputJsonToProduct(productInputJson));
        } else {
            productCatalogService.updateProduct(productMapper.inputJsonToProduct(productInputJson));
            product = productCatalogGetService.getProduct(productInputJson.getId());
        }
        entityCaches.successfulProducts.put(productInputJson.getExternalId(), product);
    }

    private boolean isProductDataEqualAndMarkErrorIfNot(
        EntityCaches entityCaches,
        CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow> row,
        ProductInputJson productInputJson
    ) {
        if (!entityCaches.successfulProducts.containsKey(productInputJson.getExternalId())) {
            return false;
        }

        Product existing = entityCaches.successfulProducts.get(productInputJson.getExternalId());
        if (!productDataEquals(existing, productMapper.inputJsonToProduct(productInputJson))) {
            String message = String.format("duplicate external id: %s with different product data", productInputJson.getExternalId());
            row.markError(message);
        }
        return true;
    }

    private void markRowsAfterCreation(List<CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow>> importRows, EntityCaches entityCaches) {
        // pass through import rows once more for classification
        importRows
            .stream()
            .filter(CsvImportRow::isValid)
            .forEach(row -> {
                String productExternalId = Optional.ofNullable(row.getImportEntity().getProductInputJson())
                    .map(ProductInputJson::getExternalId)
                    .orElse(null);
                String planExternalId = Optional.ofNullable(row.getImportEntity().getPlanJson()).map(PlanJson::getExternalId).orElse(null);
                String chargeExternalId = Optional.ofNullable(row.getImportEntity().getChargeJson()).map(ChargeJson::getExternalId).orElse(null);
                getFirstError(entityCaches, chargeExternalId, planExternalId, productExternalId).ifPresentOrElse(row::markError, () ->
                    buildAndSetOutputRowWithEntityCaches(entityCaches, row, productExternalId, planExternalId, chargeExternalId)
                );
            });
    }

    private Optional<Exception> getFirstError(EntityCaches entityCaches, String chargeExternalId, String planExternalId, String productExternalId) {
        return Arrays.stream(new String[] { chargeExternalId, planExternalId, productExternalId })
            .map(entityCaches.failedCreations::get)
            .filter(Objects::nonNull)
            .findFirst();
    }

    private void buildAndSetOutputRowWithEntityCaches(
        EntityCaches entityCaches,
        CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow> row,
        String productExternalId,
        String planExternalId,
        String chargeExternalId
    ) {
        CatalogDomainOutputRow.Builder outputRowBuilder = CatalogDomainOutputRow.builder();

        if (entityCaches.successfulProducts.containsKey(productExternalId)) {
            outputRowBuilder.product(entityCaches.successfulProducts.get(productExternalId));
        }

        if (entityCaches.successfulPlans.containsKey(planExternalId)) {
            outputRowBuilder.plan(entityCaches.successfulPlans.get(planExternalId));
        }

        if (entityCaches.successfulCharges.containsKey(chargeExternalId)) {
            outputRowBuilder.charge(entityCaches.successfulCharges.get(chargeExternalId));
        }

        row.setOutputEntity(outputRowBuilder.build());
    }

    private boolean productDataEquals(Product product1, Product product2) {
        Validator.validateNonNullArgument(product1, "product1");
        Validator.validateNonNullArgument(product2, "product2");
        return (
            Objects.equals(product1.getExternalId(), product2.getExternalId()) &&
            Objects.equals(product1.getSku(), product2.getSku()) &&
            Objects.equals(product1.getName(), product2.getName()) &&
            Objects.equals(product1.getDescription(), product2.getDescription()) &&
            Objects.equals(product1.getProductCategory(), product2.getProductCategory()) &&
            Objects.equals(product1.getEntityIds(), product2.getEntityIds())
        );
    }

    private boolean planDataEquals(Plan plan1, Plan plan2) {
        Validator.validateNonNullArgument(plan1, "plan1");
        Validator.validateNonNullArgument(plan2, "plan2");
        return (
            Objects.equals(plan1.getExternalId(), plan2.getExternalId()) &&
            Objects.equals(plan1.getName(), plan2.getName()) &&
            Objects.equals(plan1.getDescription(), plan2.getDescription()) &&
            Objects.equals(plan1.getCurrency(), plan2.getCurrency()) &&
            Objects.equals(plan1.getEntityIds(), plan2.getEntityIds())
        );
    }

    private static class EntityCaches {

        private final Map<String, Exception> failedCreations = new HashMap<>();
        private final Map<String, Product> successfulProducts = new HashMap<>();
        private final Map<String, Plan> successfulPlans = new HashMap<>();
        private final Map<String, Charge> successfulCharges = new HashMap<>();
    }

    private void updateChargeCustomFields(ChargeJson chargeJson, String chargeId) {
        CustomField customField = customFieldAPIMapper.toCustomField(chargeJson.getCustomFields());
        customFieldProxy.updateCustomFields(CustomFieldParentType.CHARGE, chargeId, customField, false);
    }

    private void updatePlanCustomFields(PlanJson planJson, String planId) {
        CustomField customField = customFieldAPIMapper.toCustomField(planJson.getCustomFields());
        customFieldProxy.updateCustomFields(CustomFieldParentType.PLAN, planId, customField, false);
    }
}
