package com.subskribe.billy.dataimport.schema.catalog;

import static com.subskribe.billy.dataimport.schema.CsvSchemaUtils.areAnyColumnValuesPresent;
import static com.subskribe.billy.dataimport.schema.ImportConstants.ALLOWED_CHARGE_BILLING_CYCLES;
import static com.subskribe.billy.dataimport.schema.ImportConstants.ALLOWED_CHARGE_CYCLES;
import static com.subskribe.billy.dataimport.schema.ImportConstants.ALLOWED_CHARGE_MODELS;
import static com.subskribe.billy.dataimport.schema.ImportConstants.ALLOWED_CHARGE_TYPES;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CATALOG_COLUMNS;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CATALOG_DOMAIN_OUTPUT_COLUMNS_ARRAY;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CATALOG_OUTPUT_COLUMNS;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_AMOUNT_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_BILLING_CYCLE_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_COLUMNS;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_DEFAULT_PRICE_ATTRIBUTES;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_DEFAULT_QUANTITY_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_DESCRIPTION_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_DISPLAY_NAME_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_DURATION_IN_MONTHS_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_ERP_ID_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_EXTERNAL_ID_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_ID_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_IS_CREDITABLE_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_IS_CUSTOM_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_IS_DISCOUNT;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_IS_DRAWDOWN_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_IS_EVENT_BASED;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_IS_LIST_PRICE_EDITABLE;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_IS_RENEWABLE_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_ITEM_CODE_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_LEDGER_ACCOUNT_CONTRACT_ASSET_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_LEDGER_ACCOUNT_DEFERRED_REVENUE_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_LEDGER_ACCOUNT_RECOGNIZED_REVENUE_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_LEDGER_ACCOUNT_TAX_LIABILITY_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_MAX_QUANTITY_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_MIN_QUANTITY_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_MODEL_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_NAME_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_PERCENT_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_PERCENT_TARGET_PLANS_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_PRICE_TIER_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_RATE_CARD_NAME;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_RECOGNITION_RULE_NAME_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_RECURRENCE_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_SHOULD_TRACK_ARR;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_STEP_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_TAX_RATE_CODE_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_TAX_RATE_NAME_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_TYPE_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.CHARGE_UNIT_OF_MEASURE_NAME_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PLAN_CURRENCY_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PLAN_DATA_COLUMNS;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PLAN_DESCRIPTION_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PLAN_DISPLAY_NAME_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PLAN_ENTITIES_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PLAN_EXTERNAL_ID_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PLAN_ID_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PLAN_NAME_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PRODUCT_CATEGORY_NAME_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PRODUCT_DATA_COLUMNS;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PRODUCT_DESCRIPTION_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PRODUCT_DISPLAY_NAME_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PRODUCT_ENTITIES_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PRODUCT_EXTERNAL_ID_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PRODUCT_ID_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PRODUCT_NAME_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.PRODUCT_SKU_HEADER;
import static com.subskribe.billy.dataimport.schema.catalog.CatalogDomainColumns.REQUIRED_CATALOG_COLUMNS;

import com.google.common.base.Splitter;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.dataimport.model.ImmutableImportSchemaDetails;
import com.subskribe.billy.dataimport.model.ImportDomain;
import com.subskribe.billy.dataimport.model.ImportOperation;
import com.subskribe.billy.dataimport.model.ImportSchemaDetails;
import com.subskribe.billy.dataimport.schema.CsvImportRow;
import com.subskribe.billy.dataimport.schema.CsvSchema;
import com.subskribe.billy.dataimport.schema.CsvSchemaUtils;
import com.subskribe.billy.dataimport.service.StringParser;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.LedgerAccountMapping;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.productcatalog.services.UnitOfMeasureService;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.plan.PriceTierJson;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.product.ProductInputJson;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.shared.pecuniary.Numbers;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.mapstruct.factory.Mappers;

public class CatalogDomainSchema implements CsvSchema<CatalogDomainInputRow, CatalogDomainOutputRow> {

    private static final String CATALOG_DOMAIN_SCHEMA = "CatalogDomainSchema";
    private static final CSVFormat CATALOG_DOMAIN_INPUT_CSV_FORMAT = CSVFormat.DEFAULT.builder()
        .setHeader()
        .setSkipHeaderRecord(true)
        .setAllowMissingColumnNames(true)
        .build();

    public static final String STANDARD_ELEMENT_STRING_SEPARATOR = "|";
    private static final String PRICE_TIER_ELEMENT_SEPARATOR = "/";

    private static final String REQUIRED_PRICE_TIER_FORMAT = "<quantity1>/<price1>|<quantity2>/<price2> e.g 10/5.00|30/2.00";
    private static final String REQUIRED_PRICE_ELEMENT_FORMAT = "<quantity>/<price> e.g 10/5.00 or 30/2.00";

    private static final String REQUIRED_TARGET_PLAN_IDS_FORMAT = "<plan_external_id1>|<plan_external_id2> e.g plan-001|plan-007";

    private static final CSVFormat CATALOG_DOMAIN_OUTPUT_FORMAT = CSVFormat.DEFAULT.builder().setHeader(CATALOG_DOMAIN_OUTPUT_COLUMNS_ARRAY).build();

    private final ProductCatalogGetService productCatalogGetService;

    private final TaxRateGetService taxRateGetService;

    private final UnitOfMeasureService unitOfMeasureService;

    private final RevenueRecognitionGetService recognitionGetService;

    private final RateCardService rateCardService;

    private final AccountingGetService accountingGetService;

    private final EntityGetService entityGetService;

    private final EntityContextResolver entityContextResolver;

    private final CustomFieldService customFieldService;

    private final CustomFieldAPIMapper customFieldAPIMapper;

    @Inject
    public CatalogDomainSchema(
        ProductCatalogGetService productCatalogGetService,
        TaxRateGetService taxRateGetService,
        UnitOfMeasureService unitOfMeasureService,
        RevenueRecognitionGetService recognitionGetService,
        RateCardService rateCardService,
        AccountingGetService accountingGetService,
        EntityGetService entityGetService,
        EntityContextResolver entityContextResolver,
        CustomFieldService customFieldService
    ) {
        this.productCatalogGetService = productCatalogGetService;
        this.taxRateGetService = taxRateGetService;
        this.unitOfMeasureService = unitOfMeasureService;
        this.recognitionGetService = recognitionGetService;
        this.rateCardService = rateCardService;
        this.accountingGetService = accountingGetService;
        this.entityGetService = entityGetService;
        this.entityContextResolver = entityContextResolver;
        this.customFieldService = customFieldService;
        customFieldAPIMapper = Mappers.getMapper(CustomFieldAPIMapper.class);
    }

    @Override
    public Optional<ImportSchemaDetails> matchSchema(List<String> columnHeaders) {
        if (new HashSet<>(columnHeaders).containsAll(REQUIRED_CATALOG_COLUMNS)) {
            return Optional.of(ImmutableImportSchemaDetails.builder().domain(ImportDomain.CATALOG).operation(ImportOperation.CREATE).build());
        }
        return Optional.empty();
    }

    @Override
    public CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow> validateAndExtract(CSVRecord record, int rowNum) {
        if (record.size() < REQUIRED_CATALOG_COLUMNS.size()) {
            String message = String.format("expected at least %d data columns but found %d", REQUIRED_CATALOG_COLUMNS.size(), record.size());
            return CsvImportRow.ofError(rowNum, message, null);
        }

        ImmutableCatalogDomainInputRow.Builder inputRowBuilder = ImmutableCatalogDomainInputRow.builder();

        boolean productDataIsPresent = areAnyColumnValuesPresent(record, PRODUCT_DATA_COLUMNS);
        boolean planDataIsPresent = areAnyColumnValuesPresent(record, PLAN_DATA_COLUMNS);
        boolean chargeDataIsPresent = areAnyColumnValuesPresent(record, CHARGE_COLUMNS);

        if (!(productDataIsPresent || planDataIsPresent || chargeDataIsPresent)) {
            return CsvSchemaUtils.errorRow(
                rowNum,
                "import row is missing product, plan and charge data one should be present",
                inputRowBuilder.build()
            );
        }

        try {
            if (productDataIsPresent) {
                inputRowBuilder.productInputJson(getProduct(record));
            }

            if (planDataIsPresent) {
                String productExternalId = CsvSchemaUtils.getRequiredField(record, PRODUCT_EXTERNAL_ID_HEADER);
                inputRowBuilder.productExternalIdRef(productExternalId);

                inputRowBuilder.planJson(getPlan(record));
            }

            if (chargeDataIsPresent) {
                String planExternalId = CsvSchemaUtils.getRequiredField(record, PLAN_EXTERNAL_ID_HEADER);
                inputRowBuilder.planExternalIdRef(planExternalId);

                List<String> targetPlanIds = getTargetPlanIds(CsvSchemaUtils.getOptionalField(record, CHARGE_PERCENT_TARGET_PLANS_HEADER));
                inputRowBuilder.targetPlanExternalIds(targetPlanIds);

                inputRowBuilder.chargeJson(getCharge(record));
                inputRowBuilder.defaultAttributes(getDefaultAttributes(record));
            }
        } catch (Exception ex) {
            return CsvImportRow.ofException(rowNum, inputRowBuilder.build(), ex);
        }

        return CsvImportRow.ofEntity(rowNum, inputRowBuilder.build());
    }

    private Map<String, String> getDefaultAttributes(CSVRecord record) {
        String defaultPriceAttributesString = CsvSchemaUtils.getOptionalField(record, CHARGE_DEFAULT_PRICE_ATTRIBUTES);
        return StringParser.parseKeyValuePairs(defaultPriceAttributesString);
    }

    private ChargeJson getCharge(CSVRecord record) {
        String chargeExternalId = CsvSchemaUtils.getRequiredField(record, CHARGE_EXTERNAL_ID_HEADER);
        ChargeJson chargeJson = new ChargeJson();
        chargeJson.setExternalId(chargeExternalId);
        Optional<String> chargeIdOptional = productCatalogGetService.getChargeIdByExternalId(chargeExternalId);
        chargeJson.setId(chargeIdOptional.orElse(null));
        chargeJson.setName(CsvSchemaUtils.getOptionalField(record, CHARGE_NAME_HEADER));
        chargeJson.setDisplayName(CsvSchemaUtils.getOptionalField(record, CHARGE_DISPLAY_NAME_HEADER));
        chargeJson.setDescription(CsvSchemaUtils.getOptionalField(record, CHARGE_DESCRIPTION_HEADER));
        chargeJson.setType(CsvSchemaUtils.extractRequiredEnum(record, CHARGE_TYPE_HEADER, ALLOWED_CHARGE_TYPES, ChargeType.class));
        chargeJson.setChargeModel(CsvSchemaUtils.extractRequiredEnum(record, CHARGE_MODEL_HEADER, ALLOWED_CHARGE_MODELS, ChargeModel.class));
        if (StringUtils.isNotBlank(CsvSchemaUtils.getOptionalField(record, CHARGE_RECURRENCE_HEADER))) {
            Cycle chargeCycle = CsvSchemaUtils.extractRequiredEnum(record, CHARGE_RECURRENCE_HEADER, ALLOWED_CHARGE_CYCLES, Cycle.class);
            String chargeStepString = CsvSchemaUtils.getOptionalField(record, CHARGE_STEP_HEADER);
            Integer step = tryParseInteger(chargeStepString, 1);
            chargeJson.setRecurrence(new RecurrenceJson(chargeCycle, step));
        }
        if (StringUtils.isNotBlank(CsvSchemaUtils.getOptionalField(record, CHARGE_AMOUNT_HEADER))) {
            chargeJson.setAmount(Numbers.bigDecimalFromString(CsvSchemaUtils.getOptionalField(record, CHARGE_AMOUNT_HEADER)));
        }

        List<PriceTierJson> priceTiers = getPriceTiers(record);
        chargeJson.setPriceTiers(CollectionUtils.isNotEmpty(priceTiers) ? priceTiers : null);

        String chargePercentString = CsvSchemaUtils.getOptionalField(record, CHARGE_PERCENT_HEADER);
        if (StringUtils.isNotBlank(chargePercentString)) {
            chargeJson.setPercent(
                Numbers.bigDecimalFromString(
                    chargePercentString,
                    String.format("%s column value %s not in required percent format", CHARGE_PERCENT_HEADER, chargePercentString)
                )
            );
        }

        chargeJson.setCustom(CsvSchemaUtils.YES_VALUE.equalsIgnoreCase(CsvSchemaUtils.getOptionalField(record, CHARGE_IS_CUSTOM_HEADER)));
        chargeJson.setIsListPriceEditable(
            CsvSchemaUtils.YES_VALUE.equalsIgnoreCase(CsvSchemaUtils.getOptionalField(record, CHARGE_IS_LIST_PRICE_EDITABLE))
        );
        chargeJson.setCreditable(CsvSchemaUtils.YES_VALUE.equalsIgnoreCase(CsvSchemaUtils.getOptionalField(record, CHARGE_IS_CREDITABLE_HEADER)));
        chargeJson.setIsRenewable(CsvSchemaUtils.YES_VALUE.equalsIgnoreCase(CsvSchemaUtils.getOptionalField(record, CHARGE_IS_RENEWABLE_HEADER)));

        String taxRateName = CsvSchemaUtils.getOptionalField(record, CHARGE_TAX_RATE_NAME_HEADER);
        String taxRateCode = CsvSchemaUtils.getOptionalField(record, CHARGE_TAX_RATE_CODE_HEADER);
        if (StringUtils.isNotBlank(taxRateCode)) {
            chargeJson.setTaxRateId(taxRateGetService.getTaxRateIdByCode(taxRateCode).orElse(null));
        } else if (StringUtils.isNotBlank(taxRateName)) {
            chargeJson.setTaxRateId(taxRateGetService.getTaxRateIdByName(taxRateName).orElse(null));
        }

        String unitOfMeasureName = CsvSchemaUtils.getOptionalField(record, CHARGE_UNIT_OF_MEASURE_NAME_HEADER);
        if (StringUtils.isNotBlank(unitOfMeasureName)) {
            chargeJson.setUnitOfMeasureId(unitOfMeasureService.getUnitOfMeasureIdByName(unitOfMeasureName).orElse(null));
        }

        String erpId = CsvSchemaUtils.getOptionalField(record, CHARGE_ERP_ID_HEADER);
        if (StringUtils.isNotBlank(erpId)) {
            chargeJson.setErpId(erpId);
        }

        String itemCode = CsvSchemaUtils.getOptionalField(record, CHARGE_ITEM_CODE_HEADER);
        if (StringUtils.isNotBlank(itemCode)) {
            chargeJson.setItemCode(itemCode);
        }

        String recognitionRuleName = CsvSchemaUtils.getOptionalField(record, CHARGE_RECOGNITION_RULE_NAME_HEADER);
        if (StringUtils.isNotBlank(recognitionRuleName)) {
            chargeJson.setRecognitionRuleId(recognitionGetService.getRecognitionRuleIdByName(recognitionRuleName).orElse(null));
        }

        populateLedgerAccountMappingsIfPresent(chargeJson, record);

        chargeJson.setDrawdown(CsvSchemaUtils.YES_VALUE.equalsIgnoreCase(CsvSchemaUtils.getOptionalField(record, CHARGE_IS_DRAWDOWN_HEADER)));
        chargeJson.setEventBased(CsvSchemaUtils.YES_VALUE.equalsIgnoreCase(CsvSchemaUtils.getOptionalField(record, CHARGE_IS_EVENT_BASED)));
        chargeJson.setIsDiscount(CsvSchemaUtils.YES_VALUE.equalsIgnoreCase(CsvSchemaUtils.getOptionalField(record, CHARGE_IS_DISCOUNT)));

        String rateCardName = CsvSchemaUtils.getOptionalField(record, CHARGE_RATE_CARD_NAME);
        if (StringUtils.isNotBlank(rateCardName)) {
            chargeJson.setRateCardId(rateCardService.getRateCardIdByName(rateCardName).orElse(null));
        }

        if (StringUtils.isNotBlank(CsvSchemaUtils.getOptionalField(record, CHARGE_BILLING_CYCLE_HEADER))) {
            chargeJson.setBillingCycle(
                CsvSchemaUtils.extractRequiredEnum(record, CHARGE_BILLING_CYCLE_HEADER, ALLOWED_CHARGE_BILLING_CYCLES, BillingCycle.class)
            );
        }

        chargeJson.setMinQuantity(getQuantityFromRecord(record, CHARGE_MIN_QUANTITY_HEADER));
        chargeJson.setMaxQuantity(getQuantityFromRecord(record, CHARGE_MAX_QUANTITY_HEADER));
        chargeJson.setDefaultQuantity(getQuantityFromRecord(record, CHARGE_DEFAULT_QUANTITY_HEADER));
        chargeJson.setDurationInMonths(getQuantityFromRecord(record, CHARGE_DURATION_IN_MONTHS_HEADER));
        if (StringUtils.isNotBlank(CsvSchemaUtils.getOptionalField(record, CHARGE_SHOULD_TRACK_ARR))) {
            chargeJson.setShouldTrackArr(CsvSchemaUtils.YES_VALUE.equalsIgnoreCase(CsvSchemaUtils.getOptionalField(record, CHARGE_SHOULD_TRACK_ARR)));
        } else {
            var shouldTrackArr =
                switch (chargeJson.getType()) {
                    case RECURRING, PERCENTAGE_OF -> true;
                    case ONE_TIME, USAGE, PREPAID -> false;
                };
            chargeJson.setShouldTrackArr(shouldTrackArr);
        }

        addCustomFieldsToChargeIfNeeded(chargeJson, record);

        return chargeJson;
    }

    private void addCustomFieldsToChargeIfNeeded(ChargeJson chargeJson, CSVRecord record) {
        List<CustomFieldEntry> customFieldEntries = getCustomFieldEntriesForRecord(CustomFieldParentType.CHARGE, record);
        if (CollectionUtils.isEmpty(customFieldEntries)) {
            return;
        }
        CustomField customFields = customFieldAPIMapper.entriesToCustomField(customFieldEntries);
        chargeJson.setCustomFields(customFields.getEntries());
    }

    private void addCustomFieldsToPlanIfNeeded(PlanJson planJson, CSVRecord record) {
        List<CustomFieldEntry> customFieldEntries = getCustomFieldEntriesForRecord(CustomFieldParentType.PLAN, record);
        if (CollectionUtils.isEmpty(customFieldEntries)) {
            return;
        }
        CustomField customFields = customFieldAPIMapper.entriesToCustomField(customFieldEntries);
        planJson.setCustomFields(customFields.getEntries());
    }

    private List<CustomFieldEntry> getCustomFieldEntriesForRecord(CustomFieldParentType customFieldParentType, CSVRecord record) {
        List<CustomFieldDefinition> customFieldDefinitions = customFieldService.getCustomFieldDefinitions(customFieldParentType);
        if (CollectionUtils.isEmpty(customFieldDefinitions)) {
            return List.of();
        }

        CustomField customFields = CustomFieldService.hydrateCustomFields(customFieldDefinitions, new CustomField(Collections.emptyMap()));
        if (MapUtils.isEmpty(customFields.getEntries())) {
            return List.of();
        }

        List<CustomFieldEntry> customFieldEntries = new ArrayList<>();
        for (Map.Entry<String, CustomFieldValue> entry : customFields.getEntries().entrySet()) {
            String customFieldValueRaw = CsvSchemaUtils.getOptionalField(
                record,
                CatalogDomainColumns.getCustomFieldHeader(customFieldParentType, entry.getValue().getName())
            );
            List<String> selections = CsvSchemaUtils.getCustomFieldSelections(customFieldValueRaw);
            CustomFieldEntry customFieldEntry = CollectionUtils.isNotEmpty(selections)
                ? CsvSchemaUtils.getCustomFieldEntryForSelections(entry.getKey(), entry.getValue(), selections)
                : CustomFieldEntry.fromDefaultValue(entry.getKey(), entry.getValue());
            customFieldEntries.add(customFieldEntry);
        }

        return customFieldEntries;
    }

    private static Long getQuantityFromRecord(CSVRecord record, String columnHeader) {
        String recordStr = CsvSchemaUtils.getOptionalField(record, columnHeader);
        if (StringUtils.isBlank(recordStr)) {
            return null;
        }
        Optional<Long> minQuantityOptional = getLongFromString(recordStr);
        if (minQuantityOptional.isEmpty()) {
            throw new IllegalArgumentException(String.format("Charge column %s value %s is not a proper quantity", columnHeader, recordStr));
        }
        return minQuantityOptional.get();
    }

    private List<String> getTargetPlanIds(String percentTargetPlans) {
        if (StringUtils.isNotBlank(percentTargetPlans)) {
            StringTokenizer stringTokenizer = new StringTokenizer(percentTargetPlans, STANDARD_ELEMENT_STRING_SEPARATOR);
            if (stringTokenizer.countTokens() <= 0) {
                throw new IllegalArgumentException(
                    String.format(
                        "Charge column %s is not in the required format %s",
                        CHARGE_PERCENT_TARGET_PLANS_HEADER,
                        REQUIRED_TARGET_PLAN_IDS_FORMAT
                    )
                );
            }
            List<String> targetPlanExternalIds = new ArrayList<>();
            while (stringTokenizer.hasMoreTokens()) {
                targetPlanExternalIds.add(stringTokenizer.nextToken());
            }
            return targetPlanExternalIds;
        }
        return List.of();
    }

    private List<PriceTierJson> getPriceTiers(CSVRecord record) {
        List<PriceTierJson> priceTierList = new ArrayList<>();
        if (StringUtils.isNotBlank(CsvSchemaUtils.getOptionalField(record, CHARGE_PRICE_TIER_HEADER))) {
            String priceTierString = CsvSchemaUtils.getOptionalField(record, CHARGE_PRICE_TIER_HEADER);
            StringTokenizer stringTokenizer = new StringTokenizer(priceTierString, STANDARD_ELEMENT_STRING_SEPARATOR);
            if (stringTokenizer.countTokens() <= 0) {
                throw new IllegalArgumentException(
                    String.format("Charge column %s is not in the required format %s", CHARGE_PRICE_TIER_HEADER, REQUIRED_PRICE_TIER_FORMAT)
                );
            }

            while (stringTokenizer.hasMoreTokens()) {
                String priceTierElement = stringTokenizer.nextToken();
                String[] tierElements = priceTierElement.split(PRICE_TIER_ELEMENT_SEPARATOR);
                if (tierElements.length != 2) {
                    throw new IllegalArgumentException(
                        String.format(
                            "Charge column %s, price element %s not in required format %s",
                            CHARGE_PRICE_TIER_HEADER,
                            priceTierElement,
                            REQUIRED_PRICE_ELEMENT_FORMAT
                        )
                    );
                }
                String quantityString = tierElements[0].trim();
                String priceString = tierElements[1].trim();

                // now split and set the price and quantity
                if (!(PriceTierJson.INF.equals(quantityString) || NumberUtils.isDigits(quantityString))) {
                    throw new IllegalArgumentException(
                        String.format("Charge column %s, price element %s quantity is not integer", CHARGE_PRICE_TIER_HEADER, priceTierElement)
                    );
                }

                if (!NumberUtils.isCreatable(priceString)) {
                    throw new IllegalArgumentException(
                        String.format("Charge column %s, price element %s price is not decimal value", CHARGE_PRICE_TIER_HEADER, priceTierElement)
                    );
                }

                BigDecimal price = Numbers.bigDecimalFromString(priceString);
                PriceTierJson priceTierJson = new PriceTierJson();
                priceTierJson.setUntilQuantity(quantityString);
                priceTierJson.setAmount(price);
                priceTierList.add(priceTierJson);
            }
        }
        return Collections.unmodifiableList(priceTierList);
    }

    private PlanJson getPlan(CSVRecord record) {
        String planExternalId = CsvSchemaUtils.getRequiredField(record, PLAN_EXTERNAL_ID_HEADER);
        Optional<String> planIdOptional = productCatalogGetService.getPlanIdByExternalId(planExternalId);

        Set<String> entityIds = getPlanEntityIds(record);
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(entityIds);

        PlanJson planJson = new PlanJson();
        planJson.setEntityIds(resolvedEntityIds);
        planJson.setExternalId(CsvSchemaUtils.getRequiredField(record, PLAN_EXTERNAL_ID_HEADER));
        planJson.setName(CsvSchemaUtils.getOptionalField(record, PLAN_NAME_HEADER));
        planJson.setDisplayName(CsvSchemaUtils.getOptionalField(record, PLAN_DISPLAY_NAME_HEADER));
        planJson.setDescription(CsvSchemaUtils.getOptionalField(record, PLAN_DESCRIPTION_HEADER));
        planJson.setId(planIdOptional.orElse(null));
        planJson.setCurrency(CsvSchemaUtils.getOptionalField(record, PLAN_CURRENCY_HEADER));
        addCustomFieldsToPlanIfNeeded(planJson, record);
        return planJson;
    }

    private Set<String> getPlanEntityIds(CSVRecord record) {
        String entitiesInput = CsvSchemaUtils.getOptionalField(record, PLAN_ENTITIES_HEADER);
        return getEntityIds(entitiesInput);
    }

    private Set<String> getProductEntityIds(CSVRecord record) {
        String entitiesInput = CsvSchemaUtils.getOptionalField(record, PRODUCT_ENTITIES_HEADER);
        return getEntityIds(entitiesInput);
    }

    private Set<String> getEntityIds(String entitiesInput) {
        if (StringUtils.isBlank(entitiesInput)) {
            return Set.of();
        }
        List<String> entityDisplayIds = Splitter.on(STANDARD_ELEMENT_STRING_SEPARATOR).splitToList(entitiesInput);
        if (CollectionUtils.isEmpty(entityDisplayIds)) {
            throw new IllegalArgumentException("entities are not in the required format");
        }
        return entityDisplayIds.stream().map(this::getEntityId).collect(Collectors.toSet());
    }

    private String getEntityId(String entityDisplayId) {
        if (Objects.equals(entityDisplayId, EntityContext.ALL_ACCESS_ID)) {
            return EntityContext.ALL_ACCESS_ID;
        }
        return entityGetService.getEntityByDisplayId(entityDisplayId).getEntityId();
    }

    private ProductInputJson getProduct(CSVRecord record) {
        String productExternalId = CsvSchemaUtils.getRequiredField(record, PRODUCT_EXTERNAL_ID_HEADER);
        Optional<String> productIdOptional = productCatalogGetService.getProductIdByExternalId(productExternalId);

        ProductInputJson productInput = new ProductInputJson();

        String categoryName = CsvSchemaUtils.getOptionalField(record, PRODUCT_CATEGORY_NAME_HEADER);
        if (StringUtils.isNotBlank(categoryName)) {
            Optional<ProductCategory> optionalCategory = productCatalogGetService.getProductCategoryByName(categoryName);
            optionalCategory.ifPresent(productCategory -> productInput.setProductCategoryId(productCategory.getProductCategoryId()));
        }

        Set<String> entityIds = getProductEntityIds(record);
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(entityIds);

        productInput.setExternalId(productExternalId);
        productInput.setSku(CsvSchemaUtils.getOptionalField(record, PRODUCT_SKU_HEADER));
        productInput.setName(CsvSchemaUtils.getOptionalField(record, PRODUCT_NAME_HEADER));
        productInput.setDisplayName(CsvSchemaUtils.getOptionalField(record, PRODUCT_DISPLAY_NAME_HEADER));
        productInput.setDescription(CsvSchemaUtils.getOptionalField(record, PRODUCT_DESCRIPTION_HEADER));
        productInput.setId(productIdOptional.orElse(null));
        productInput.setEntityIds(resolvedEntityIds);
        return productInput;
    }

    @Override
    public Iterable<String> writeOutputRow(CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow> importRow) {
        return CsvSchemaUtils.writeOutputRow(importRow, CATALOG_OUTPUT_COLUMNS, this::writeEntityOutput);
    }

    private void writeEntityOutput(CsvImportRow<CatalogDomainInputRow, CatalogDomainOutputRow> row, Map<String, String> output) {
        if (!row.isValid()) {
            // if the row is not valid nothing to do
            return;
        }

        if (row.getOutputEntity().getProduct() != null) {
            Product product = row.getOutputEntity().getProduct();
            output.put(PRODUCT_EXTERNAL_ID_HEADER, product.getExternalId());
            output.put(PRODUCT_ID_HEADER, product.getProductId());
            output.put(PRODUCT_NAME_HEADER, product.getName());
            output.put(PRODUCT_DISPLAY_NAME_HEADER, product.getDisplayName());
        }

        if (row.getImportEntity().getProductExternalIdRef() != null) {
            output.put(PRODUCT_EXTERNAL_ID_HEADER, row.getImportEntity().getProductExternalIdRef());
        }

        if (row.getOutputEntity().getPlan() != null) {
            Plan plan = row.getOutputEntity().getPlan();
            output.put(PLAN_EXTERNAL_ID_HEADER, plan.getExternalId());
            output.put(PLAN_ID_HEADER, plan.getPlanId());
            output.put(PLAN_NAME_HEADER, plan.getName());
            output.put(PLAN_DISPLAY_NAME_HEADER, plan.getDisplayName());
        }

        if (row.getOutputEntity().getCharge() != null) {
            Charge charge = row.getOutputEntity().getCharge();
            output.put(CHARGE_EXTERNAL_ID_HEADER, charge.getExternalId());
            output.put(CHARGE_ID_HEADER, charge.getChargeId());
            output.put(CHARGE_NAME_HEADER, charge.getName());
            output.put(CHARGE_DISPLAY_NAME_HEADER, charge.getDisplayName());
        }
    }

    @Override
    public CSVFormat getImportCsvParseFormat() {
        return CATALOG_DOMAIN_INPUT_CSV_FORMAT;
    }

    @Override
    public CSVFormat getOutputCsvFormat() {
        return CATALOG_DOMAIN_OUTPUT_FORMAT;
    }

    @Override
    public Map<String, List<String>> getSchemaHeaders() {
        return Map.of(CATALOG_DOMAIN_SCHEMA, CATALOG_COLUMNS);
    }

    private static Optional<Long> getLongFromString(String stringInt) {
        if (!NumberUtils.isDigits(stringInt)) {
            return Optional.empty();
        }
        return Optional.of(Long.parseLong(stringInt));
    }

    private static Integer tryParseInteger(String stringInt, Integer defaultValue) {
        if (StringUtils.isEmpty(stringInt)) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(stringInt);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    private void populateLedgerAccountMappingsIfPresent(ChargeJson chargeJson, CSVRecord record) {
        List<LedgerAccount> ledgerAccounts = new ArrayList<>();

        String deferredRevenueNameOrCode = CsvSchemaUtils.getOptionalField(record, CHARGE_LEDGER_ACCOUNT_DEFERRED_REVENUE_HEADER);
        getLedgerAccountForImport(deferredRevenueNameOrCode).ifPresent(ledgerAccounts::add);

        String recognizedRevenueNameOrCode = CsvSchemaUtils.getOptionalField(record, CHARGE_LEDGER_ACCOUNT_RECOGNIZED_REVENUE_HEADER);
        getLedgerAccountForImport(recognizedRevenueNameOrCode).ifPresent(ledgerAccounts::add);

        String contractAssetNameOrCode = CsvSchemaUtils.getOptionalField(record, CHARGE_LEDGER_ACCOUNT_CONTRACT_ASSET_HEADER);
        getLedgerAccountForImport(contractAssetNameOrCode).ifPresent(ledgerAccounts::add);

        String taxLiabilityNameOrCode = CsvSchemaUtils.getOptionalField(record, CHARGE_LEDGER_ACCOUNT_TAX_LIABILITY_HEADER);
        getLedgerAccountForImport(taxLiabilityNameOrCode).ifPresent(ledgerAccounts::add);

        if (CollectionUtils.isNotEmpty(ledgerAccounts)) {
            chargeJson.setLedgerAccountMapping(new LedgerAccountMapping(ledgerAccounts));
        }
    }

    // todo: use a cache for ledger accounts
    private Optional<LedgerAccount> getLedgerAccountForImport(String ledgerAccountNameOrCode) {
        if (StringUtils.isBlank(ledgerAccountNameOrCode)) {
            return Optional.empty();
        }
        return accountingGetService
            .getLedgerAccountByCode(ledgerAccountNameOrCode)
            .or(() -> accountingGetService.getLedgerAccountByName(ledgerAccountNameOrCode));
    }
}
