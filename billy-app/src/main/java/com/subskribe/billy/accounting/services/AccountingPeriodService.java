package com.subskribe.billy.accounting.services;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.accounting.db.AccountingPeriodDAO;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.AccountingPeriodStatus;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.LockAcquisitionFailureException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.postgres.PostgresAdvisoryLock;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.resources.json.tenant.UserMapper;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ExternalSyncStatus;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.time.TimeZoneService;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import dev.failsafe.Failsafe;
import dev.failsafe.RetryPolicy;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class AccountingPeriodService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountingPeriodService.class);
    private static final long ONBOARDING_GENERATION_WINDOW_IN_MONTHS = 120;
    private static final String ACCOUNTING_PERIOD_LOCK_FORMAT = "%s/accountingPeriod";
    private static final List<AccountingPeriodStatus> ACCOUNTING_PERIOD_STATUS_FOR_SYNCS = List.of(
        AccountingPeriodStatus.OPEN,
        AccountingPeriodStatus.CLOSED
    );

    private static final int LOCK_MAX_RETRY = 100;
    private static final long LOCK_RETRY_DELAY_MS = 1000;
    private static final long LOCK_RANDOM_JITTER_MS = 500;
    private static final RetryPolicy<Object> LOCK_RETRY_POLICY = RetryPolicy.builder()
        .handle(LockAcquisitionFailureException.class)
        .withMaxRetries(LOCK_MAX_RETRY)
        .withDelay(Duration.ofMillis(LOCK_RETRY_DELAY_MS))
        .withJitter(Duration.ofMillis(LOCK_RANDOM_JITTER_MS))
        .build();

    private final AccountingPeriodDAO accountingPeriodDAO;
    private final AccountingPeriodIdGenerator accountingPeriodIdGenerator;
    private final CurrentUserProvider currentUserProvider;
    private final UserService userService;
    private final TenantSettingService tenantSettingService;
    private final UserMapper userMapper;
    private final TenantIdProvider tenantIdProvider;
    private final EntityContextProvider entityContextProvider;
    private final DSLContextProvider dslContextProvider;
    private final AccountingService accountingService;
    private final BillyConfiguration billyConfiguration;

    @Inject
    public AccountingPeriodService(
        AccountingPeriodDAO accountingPeriodDAO,
        AccountingPeriodIdGenerator accountingPeriodIdGenerator,
        CurrentUserProvider currentUserProvider,
        UserService userService,
        TenantSettingService tenantSettingService,
        TenantIdProvider tenantIdProvider,
        EntityContextProvider entityContextProvider,
        DSLContextProvider dslContextProvider,
        AccountingService accountingService,
        BillyConfiguration billyConfiguration
    ) {
        this.accountingPeriodDAO = accountingPeriodDAO;
        this.accountingPeriodIdGenerator = accountingPeriodIdGenerator;
        this.billyConfiguration = billyConfiguration;
        this.currentUserProvider = currentUserProvider;
        this.userService = userService;
        this.tenantSettingService = tenantSettingService;
        this.tenantIdProvider = tenantIdProvider;
        this.entityContextProvider = entityContextProvider;
        this.dslContextProvider = dslContextProvider;
        this.accountingService = accountingService;
        userMapper = Mappers.getMapper(UserMapper.class);
    }

    public AccountingPeriod getAccountingPeriod(String accountingPeriodId) {
        Validator.validateStringNotBlank(accountingPeriodId, "accountingPeriodId");

        return accountingPeriodDAO
            .getAccountingPeriodByAccountingPeriodId(accountingPeriodId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ACCOUNTING_PERIOD, accountingPeriodId));
    }

    public List<AccountingPeriod> getAccountingPeriodsByAccountingPeriodIds(List<String> accountingPeriodIds) {
        Validator.validateCollectionNotEmpty(accountingPeriodIds, "accountingPeriodIds");
        return accountingPeriodDAO.getAccountingPeriodsByAccountingPeriodIds(accountingPeriodIds);
    }

    public List<AccountingPeriod> getAccountingPeriodsBetween(Period period) {
        if (!period.isValid()) {
            throw new IllegalArgumentException("the specified period is invalid");
        }
        return accountingPeriodDAO.getAccountingPeriodsByPeriod(period);
    }

    public boolean inClosedAccountingPeriod(String entityId, Instant instant) {
        return getCurrentAccountingPeriod(entityId).map(AccountingPeriod::getStartDate).filter(instant::isBefore).isPresent();
    }

    public Optional<AccountingPeriod> getCurrentAccountingPeriod() {
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        return getCurrentAccountingPeriod(entityId);
    }

    public Optional<AccountingPeriod> getCurrentAccountingPeriod(String entityId) {
        Validator.validateStringNotBlank(entityId, "entityId");
        return accountingPeriodDAO.getFirstOpen(entityId);
    }

    public Optional<AccountingPeriod> getPreviousAccountingPeriod(AccountingPeriod accountingPeriod) {
        Validator.validateNonNullArgument(accountingPeriod, "accountingPeriod");
        return accountingPeriodDAO.getPreviousAccountingPeriod(accountingPeriod);
    }

    public Optional<AccountingPeriod> getLastClosed() {
        return accountingPeriodDAO.getLastClosed();
    }

    // Fetch recent accounting periods for given number of months, including current
    public List<AccountingPeriod> getRecentAccountingPeriods(Long recentNumberOfMonths) {
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        AccountingPeriod currentAccountingPeriod = getCurrentAccountingPeriod(entityId).orElseThrow(() ->
            new IllegalStateException("no open accounting period found")
        );
        ZoneId zoneId = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        Instant startDate = DateTimeCalculator.minusMonths(zoneId, currentAccountingPeriod.getStartDate(), recentNumberOfMonths);
        Instant endDate = DateTimeCalculator.plusMonths(zoneId, currentAccountingPeriod.getEndDate(), 1);
        AccountingPeriod firstAccountingPeriod = accountingPeriodDAO.getFirst(entityId).orElseThrow(); // this is guaranteed as current accounting period exists
        if (startDate.isBefore(firstAccountingPeriod.getStartDate())) {
            generateAccountingPeriods(Period.between(startDate, firstAccountingPeriod.getStartDate()));
        }
        return accountingPeriodDAO.getAccountingPeriodsByPeriod(Period.between(startDate, endDate));
    }

    public List<AccountingPeriod> getAccountingPeriodsUntilDate(Instant targetDate) {
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        AccountingPeriod currentAccountingPeriod = getCurrentAccountingPeriod(entityId).orElseThrow(() ->
            new IllegalStateException("No open accounting periods found")
        );
        ZoneId zoneId = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        Instant startDate = currentAccountingPeriod.getStartDate();
        Instant endDate = DateTimeCalculator.getStartOfMonth(targetDate, zoneId);
        return accountingPeriodDAO.getAccountingPeriodsByPeriod(Period.between(startDate, endDate));
    }

    public AccountingPeriod getAccountingPeriodByInstant(Instant instant) {
        Validator.validateNonNullArgument(instant, "instant");
        var optionalAccountingPeriod = accountingPeriodDAO.getAccountingPeriodByInstant(instant);
        if (optionalAccountingPeriod.isPresent()) {
            return optionalAccountingPeriod.get();
        }
        generateAccountingPeriods(Period.between(instant, instant.plusSeconds(1)));
        return accountingPeriodDAO
            .getAccountingPeriodByInstant(instant)
            .orElseThrow(() -> new IllegalStateException(String.format("unable to generate accounting period for instant: %s", instant)));
    }

    public void fetchOpenedByUser(AccountingPeriod accountingPeriod) {
        Optional<User> userOptional = userService.getUserOptional(accountingPeriod.getOpenedBy());
        userOptional.ifPresent(user -> {
            UserJson userJson = userMapper.userToJson(user);
            accountingPeriod.setOpenedByUser(userJson);
        });
    }

    public void fetchClosedByUser(AccountingPeriod accountingPeriod) {
        Optional<User> userOptional = userService.getUserOptional(accountingPeriod.getClosedBy());
        userOptional.ifPresent(user -> {
            UserJson userJson = userMapper.userToJson(user);
            accountingPeriod.setClosedByUser(userJson);
        });
    }

    // generate purely synthetic accounting periods without persisting
    public List<AccountingPeriod> generateSyntheticAccountingPeriod(Period relevantPeriod) {
        // using the default timezone here
        List<Period> newPeriods = DateTimeCalculator.getMonthPeriods(
            relevantPeriod.getStart(),
            relevantPeriod.getEnd(),
            TimeZoneService.getDefaultTimeZone().toZoneId(),
            true
        );
        return newPeriods
            .stream()
            .map(period -> createSyntheticAccountingPeriod(period.getStart(), period.getEnd(), AccountingPeriodStatus.UPCOMING))
            .collect(Collectors.toList());
    }

    // TODO: Assumes monthly accounting periods which are aligned to 1st day of the month.
    public List<AccountingPeriod> generateAccountingPeriods(Period period) {
        throwIfTenantSettingIsNotSealed();

        List<AccountingPeriod> newAccountingPeriods = getNewAccountingPeriodsForGivenPeriod(period);
        if (CollectionUtils.isNotEmpty(newAccountingPeriods)) {
            DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
            dslContext.transaction(configuration -> generateAccountingPeriodsTransaction(configuration, newAccountingPeriods));
            assertAccountingPeriodListInvariantsForEntity();
        }

        // return the periods which are between the requested period
        return accountingPeriodDAO.getAccountingPeriodsByPeriod(period);
    }

    private List<AccountingPeriod> getNewAccountingPeriodsForGivenPeriod(Period period) {
        if (!period.isValid()) {
            throw new IllegalArgumentException("the specified period is invalid");
        }

        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        AccountingPeriod firstAccountingPeriod = accountingPeriodDAO
            .getFirst(entityId)
            .orElseThrow(() -> new IllegalStateException("could not find any accounting period"));
        AccountingPeriod lastAccountingPeriod = accountingPeriodDAO
            .getLast(entityId)
            .orElseThrow(() -> new IllegalStateException("could not find any accounting period"));

        // "stretch" the period to make it continuous
        Instant firstStartDate = firstAccountingPeriod.getStartDate();
        Instant lastEndDate = lastAccountingPeriod.getEndDate();
        Period generationPeriod = Period.between(period.getStart(), period.getEnd());
        if (generationPeriod.getStart().isAfter(lastEndDate)) {
            generationPeriod = Period.between(lastEndDate, generationPeriod.getEnd());
        }
        if (generationPeriod.getEnd().isBefore(firstStartDate)) {
            generationPeriod = Period.between(generationPeriod.getStart(), firstStartDate);
        }

        Period existingPeriod = Period.between(firstStartDate, lastEndDate);
        AccountingPeriod currentAccountingPeriod = getCurrentAccountingPeriod(entityId).orElseThrow(() ->
            new IllegalStateException("current accounting period not found")
        );
        ZoneId zoneId = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        Instant generationPeriodStart = DateTimeCalculator.getStartOfMonth(generationPeriod.getStart(), zoneId);
        List<Period> newPeriods = DateTimeCalculator.getMonthPeriods(generationPeriodStart, generationPeriod.getEnd(), zoneId, true);

        List<AccountingPeriod> newAccountingPeriods = new ArrayList<>();
        for (Period newPeriod : newPeriods) {
            if (Period.overlapOf(newPeriod, existingPeriod).isEmpty()) {
                Instant startDate = newPeriod.getStart();
                AccountingPeriodStatus accountingPeriodStatus = startDate.isBefore(currentAccountingPeriod.getStartDate())
                    ? AccountingPeriodStatus.CLOSED
                    : AccountingPeriodStatus.UPCOMING;
                AccountingPeriod accountingPeriod = createAccountingPeriod(startDate, newPeriod.getEnd(), accountingPeriodStatus);
                newAccountingPeriods.add(accountingPeriod);
            }
        }

        return newAccountingPeriods;
    }

    private void generateAccountingPeriodsTransaction(Configuration configuration, List<AccountingPeriod> newAccountingPeriods) {
        DSLContext dslContext = DSL.using(configuration);
        Failsafe.with(LOCK_RETRY_POLICY).run(() -> tryAndAcquireAccountingPeriodLock(dslContext));

        // Filter accounting periods again under protection of lock to ensure no duplicates
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        AccountingPeriod firstAccountingPeriod = accountingPeriodDAO
            .getFirst(entityId)
            .orElseThrow(() -> new IllegalStateException("could not find any accounting period"));
        AccountingPeriod lastAccountingPeriod = accountingPeriodDAO
            .getLast(entityId)
            .orElseThrow(() -> new IllegalStateException("could not find any accounting period"));
        Period existingPeriod = Period.between(firstAccountingPeriod.getStartDate(), lastAccountingPeriod.getEndDate());
        List<AccountingPeriod> filteredNewAccountingPeriods = newAccountingPeriods
            .stream()
            .filter(ap -> Period.overlapOf(existingPeriod, Period.between(ap.getStartDate(), ap.getEndDate())).isEmpty())
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(filteredNewAccountingPeriods)) {
            batchInsertAccountingPeriods(dslContext, newAccountingPeriods);
        }
    }

    private Optional<String> getCurrentUserIdOrApiKey() {
        Optional<BillyAuthPrincipal> billyAuthPrincipalOptional = currentUserProvider.getAuthPrincipalFromHeaders();
        if (billyAuthPrincipalOptional.isEmpty()) {
            return Optional.empty();
        }
        BillyAuthPrincipal billyAuthPrincipal = billyAuthPrincipalOptional.get();
        return switch (billyAuthPrincipal.getPrincipalType()) {
            case USER -> userService.getUserByEmail(billyAuthPrincipal.getName()).map(User::getUserId);
            case API_KEY -> Optional.of(billyAuthPrincipal.getName());
            case API_KEY_USER -> billyAuthPrincipal.getApiKeyAuthUser().isEmpty()
                ? Optional.empty()
                : billyAuthPrincipal.getApiKeyAuthUser().get().getUser().map(User::getUserId);
            default -> Optional.empty();
        };
    }

    private AccountingPeriod createAccountingPeriod(Instant startDate, Instant endDate, AccountingPeriodStatus status) {
        AccountingPeriod accountingPeriod = createAccountingPeriodStub(startDate, endDate, status);
        accountingPeriod.setAccountingPeriodId(accountingPeriodIdGenerator.generate());
        return accountingPeriod;
    }

    private static AccountingPeriod createSyntheticAccountingPeriod(Instant startDate, Instant endDate, AccountingPeriodStatus status) {
        AccountingPeriod accountingPeriod = createAccountingPeriodStub(startDate, endDate, status);
        String syntheticAccountingPeriodId = String.format(
            "%s-%d-%d",
            AccountingPeriodIdGenerator.PREFIX,
            startDate.getEpochSecond(),
            endDate.getEpochSecond()
        );
        accountingPeriod.setAccountingPeriodId(syntheticAccountingPeriodId);
        accountingPeriod.setSynthetic(true);
        return accountingPeriod;
    }

    private static AccountingPeriod createAccountingPeriodStub(Instant startDate, Instant endDate, AccountingPeriodStatus status) {
        Validator.validateNonNullArguments(startDate, endDate, status);
        AccountingPeriod accountingPeriod = new AccountingPeriod();
        accountingPeriod.setStartDate(startDate);
        accountingPeriod.setEndDate(endDate);
        accountingPeriod.setStatus(status);
        return accountingPeriod;
    }

    private void batchInsertAccountingPeriods(DSLContext dslContext, List<AccountingPeriod> accountingPeriods) {
        Validator.validateCollectionNotEmpty(accountingPeriods, "accountingPeriods");
        accountingPeriodDAO.batchInsertAccountingPeriods(dslContext, accountingPeriods);
    }

    /**
     * Invariants:
     * 1. Accounting period start date should be before end date.
     * 2. Accounting periods should be continuous (i.e. no gaps).
     * 3. Accounting periods should not overlap with each other.
     * 4. Only one accounting period should be open at any time.
     *
     * @param accountingPeriods a list of accounting periods to assert
     * @throws IllegalStateException if the invariants are not met
     */
    private static void assertAccountingPeriodListInvariants(List<AccountingPeriod> accountingPeriods) {
        Validator.validateCollectionNotEmpty(accountingPeriods, "accountingPeriods");

        // 1. Accounting period start date should be before end date.
        accountingPeriods.forEach(AccountingPeriod::validate);

        // 2. Accounting periods should be continuous (i.e. no gaps).
        // 3. Accounting periods should not overlap with each other.
        accountingPeriods.sort(Comparator.comparing(AccountingPeriod::getStartDate));
        accountingPeriods
            .stream()
            .reduce((a, b) -> {
                if (!a.getEndDate().equals(b.getStartDate())) {
                    String message = String.format("Accounting periods are not continuous: %s, %s", a, b);
                    LOGGER.error(message);
                    throw new IllegalStateException(message);
                }
                return b;
            });

        // 4. Only one accounting period should be open at any time.
        long openPeriodCount = accountingPeriods.stream().filter(a -> a.getStatus() == AccountingPeriodStatus.OPEN).count();
        if (openPeriodCount > 1) {
            String message = String.format("More than one accounting period is open: %s", accountingPeriods);
            LOGGER.error(message);
            throw new IllegalStateException(message);
        }
    }

    public void assertAccountingPeriodListInvariantsForEntity() {
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        List<AccountingPeriod> accountingPeriods = accountingPeriodDAO.getAccountingPeriods(entityId);
        if (CollectionUtils.isEmpty(accountingPeriods)) {
            return;
        }
        assertAccountingPeriodListInvariants(accountingPeriods);
    }

    public AccountingPeriod specifyCurrentAccountingPeriod(Instant periodStart) {
        throwIfTenantSettingIsNotSealed();

        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        Optional<AccountingPeriod> firstOpenAccountingPeriod = accountingPeriodDAO.getFirstOpen(entityId);
        if (firstOpenAccountingPeriod.isPresent()) {
            throw new IllegalStateException("the current open accounting period has already been specified");
        }

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext.transaction(configuration -> {
            specifyCurrentAccountingPeriodTransaction(configuration, periodStart);
            accountingService.createDefaultLedgerAccountsInTransaction(configuration);
        });
        return accountingPeriodDAO.getFirstOpen(entityId).orElseThrow(() -> new IllegalStateException("current accounting period not found"));
    }

    private void specifyCurrentAccountingPeriodTransaction(Configuration configuration, Instant periodStart) {
        DSLContext dslContext = DSL.using(configuration);
        tryAndAcquireAccountingPeriodLock(dslContext);

        ZoneId zoneId = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        Instant startDate = DateTimeCalculator.getStartOfMonth(periodStart, zoneId);
        Instant endDate = DateTimeCalculator.plusMonths(zoneId, startDate, ONBOARDING_GENERATION_WINDOW_IN_MONTHS);
        List<Period> periods = DateTimeCalculator.getMonthPeriods(startDate, endDate, zoneId, true);

        List<AccountingPeriod> accountingPeriods = new ArrayList<>();
        for (Period period : periods) {
            AccountingPeriod accountingPeriod = createAccountingPeriod(period.getStart(), period.getEnd(), AccountingPeriodStatus.UPCOMING);
            accountingPeriods.add(accountingPeriod);
        }

        AccountingPeriod currentAccountingPeriod = accountingPeriods.stream().findFirst().orElseThrow();
        currentAccountingPeriod.setStatus(AccountingPeriodStatus.OPEN);
        currentAccountingPeriod.setOpenedOn(Instant.now());
        getCurrentUserIdOrApiKey().ifPresent(currentAccountingPeriod::setOpenedBy);

        batchInsertAccountingPeriods(dslContext, accountingPeriods);
    }

    public void touchAccountingPeriodExpectingStatus(
        DSLContext dslContext,
        AccountingPeriod accountingPeriod,
        AccountingPeriodStatus expectedStatus
    ) {
        Validator.checkNonNullInternal(accountingPeriod, "accounting period cannot be null here");
        Validator.checkNonNullInternal(expectedStatus, "expectedStatus cannot be null here");
        accountingPeriodDAO.touchAccountingPeriodExpectingStatus(dslContext, accountingPeriod, expectedStatus);
    }

    public AccountingPeriod updateAccountingPeriodStatus(
        DSLContext dslContext,
        AccountingPeriod accountingPeriod,
        AccountingPeriodStatus statusFrom,
        AccountingPeriodStatus statusTo
    ) {
        throwIfTenantSettingIsNotSealed();
        accountingPeriod.sanitize();
        accountingPeriod.validate();
        Validator.validateNonNullArgument(statusFrom, "statusFrom");
        Validator.validateNonNullArgument(statusTo, "statusTo");

        populateStatusWithAuditTrailInfo(accountingPeriod, statusTo);
        return accountingPeriodDAO.updateAccountingPeriodStatus(dslContext, accountingPeriod, statusFrom, statusTo);
    }

    public void updateAccountingPeriodSyncStatus(AccountingPeriod accountingPeriod, ExternalSyncStatus statusFrom, ExternalSyncStatus statusTo) {
        Validator.validateNonNullArgument(statusFrom, "statusFrom");
        Validator.validateNonNullArgument(statusTo, "statusTo");
        if (!ACCOUNTING_PERIOD_STATUS_FOR_SYNCS.contains(accountingPeriod.getStatus())) {
            throw new ConflictingStateException(
                String.format(
                    "accounting period sync status cannot be updated to %s because status %s is not one of %s",
                    statusTo,
                    accountingPeriod.getStatus(),
                    ACCOUNTING_PERIOD_STATUS_FOR_SYNCS
                )
            );
        }

        accountingPeriodDAO.updateAccountingPeriodSyncStatus(accountingPeriod, statusFrom, statusTo);
    }

    private void populateStatusWithAuditTrailInfo(AccountingPeriod accountingPeriod, AccountingPeriodStatus newStatus) {
        accountingPeriod.setStatus(newStatus);
        Optional<String> userIdOrApiKey = getCurrentUserIdOrApiKey();
        if (newStatus == AccountingPeriodStatus.CLOSED) {
            accountingPeriod.setClosedOn(Instant.now());
            userIdOrApiKey.ifPresent(accountingPeriod::setClosedBy);
        } else if (newStatus == AccountingPeriodStatus.OPEN) {
            accountingPeriod.setOpenedOn(Instant.now());
            userIdOrApiKey.ifPresent(accountingPeriod::setOpenedBy);
        }
    }

    void tryAndAcquireAccountingPeriodLock(DSLContext dslContext) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        String lockKey = accountingPeriodLockKey(tenantId);

        Optional<Long> lockHashOptional = PostgresAdvisoryLock.tryAcquireLock(dslContext, lockKey);
        if (lockHashOptional.isEmpty()) {
            LOGGER.warn("Could not acquire accounting period lock on {}", lockKey);
            throw new LockAcquisitionFailureException("Accounting period generation already in progress, please try later");
        }
    }

    static String accountingPeriodLockKey(String tenantId) {
        return String.format(ACCOUNTING_PERIOD_LOCK_FORMAT, tenantId);
    }

    private void throwIfTenantSettingIsNotSealed() {
        if (BooleanUtils.isTrue(billyConfiguration.getTenantSealProtectionConfiguration().getEnabled()) && !tenantSettingService.isSealed()) {
            throw new IllegalStateException("tenant setting (time zone) must be sealed before any accounting period operation");
        }
    }

    public boolean doesAccountingPeriodExistForEntity(String entityId) {
        return getCurrentAccountingPeriod(entityId).isPresent();
    }

    public void purgeAllAccountingPeriods(Configuration configuration) {
        DSLContext dslContext = DSL.using(configuration);
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        accountingPeriodDAO.purgeAllAccountingPeriodsForEntity(dslContext, entityId);
    }
}
