package com.subskribe.billy.accounting.services;

import com.subskribe.billy.accounting.db.JournalEntryDAO;
import com.subskribe.billy.accounting.db.LedgerAccountDAO;
import com.subskribe.billy.accounting.model.AccountingEvent;
import com.subskribe.billy.accounting.model.AccountingEventPageToken;
import com.subskribe.billy.accounting.model.AccountingFxBalance;
import com.subskribe.billy.accounting.model.JournalEntry;
import com.subskribe.billy.accounting.model.JournalEntryPageToken;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.model.LedgerAccountType;
import com.subskribe.billy.accounting.model.RunningBalance;
import com.subskribe.billy.accounting.model.RunningBalanceType;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.payment.services.PaymentBankAccountGetService;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;

public class AccountingGetService {

    private static final String DEFAULT_ACCOUNT_DESCRIPTION_FORMAT = "Default %s, applicable for all charges unless overridden";
    private final LedgerAccountDAO ledgerAccountDAO;
    private final PaymentBankAccountGetService paymentBankAccountGetService;

    private final JournalEntryDAO journalEntryDAO;
    private final FeatureService featureService;

    @Inject
    public AccountingGetService(
        LedgerAccountDAO ledgerAccountDAO,
        PaymentBankAccountGetService paymentBankAccountGetService,
        JournalEntryDAO journalEntryDAO,
        FeatureService featureService
    ) {
        this.ledgerAccountDAO = ledgerAccountDAO;
        this.paymentBankAccountGetService = paymentBankAccountGetService;
        this.journalEntryDAO = journalEntryDAO;
        this.featureService = featureService;
    }

    public List<LedgerAccount> getLedgerAccountsForCharge(String chargeId) {
        Map<LedgerAccountType, LedgerAccount> defaultAccountsByType = getDefaultLedgerAccounts()
            .stream()
            .collect(Collectors.toMap(LedgerAccount::getAccountType, Function.identity()));

        List<LedgerAccount> chargeLedgerAccounts = ledgerAccountDAO.getLedgerAccountsForCharge(chargeId);
        Map<LedgerAccountType, LedgerAccount> chargeAccountsByType = chargeLedgerAccounts
            .stream()
            .collect(Collectors.toMap(LedgerAccount::getAccountType, Function.identity()));

        Set<LedgerAccountType> accountTypeSet = new HashSet<>(defaultAccountsByType.keySet());
        accountTypeSet.addAll(chargeAccountsByType.keySet());

        return accountTypeSet
            .stream()
            .map(type -> {
                if (chargeAccountsByType.containsKey(type)) {
                    return chargeAccountsByType.get(type);
                }
                return defaultAccountsByType.get(type);
            })
            .collect(Collectors.toList());
    }

    public List<LedgerAccount> getLedgerAccounts() {
        List<LedgerAccount> ledgerAccounts = ledgerAccountDAO.getLedgerAccounts();
        updateLedgerAccountInUse(ledgerAccounts);
        return ledgerAccounts;
    }

    private void updateLedgerAccountInUse(List<LedgerAccount> ledgerAccounts) {
        List<String> ledgerAccountIds = ledgerAccounts.stream().map(LedgerAccount::getLedgerAccountId).collect(Collectors.toList());
        List<String> ledgerAccountIdsInUse = ledgerAccountDAO.getLedgerAccountIdsInUse(ledgerAccountIds);
        List<String> ledgerAccountIdsInUseForPaymentBankAccounts = paymentBankAccountGetService.getLedgerAccountIdsInUseForPaymentBankAccounts(
            ledgerAccountIds
        );
        ledgerAccounts.forEach(ledgerAccount ->
            ledgerAccount.setInUse(
                ledgerAccount.isDefault() ||
                ledgerAccountIdsInUse.contains(ledgerAccount.getLedgerAccountId()) ||
                ledgerAccountIdsInUseForPaymentBankAccounts.contains(ledgerAccount.getLedgerAccountId())
            )
        );
    }

    public List<LedgerAccount> getDefaultLedgerAccountTemplates() {
        return Arrays.stream(LedgerAccountType.values())
            .filter(ledgerAccountType -> ledgerAccountType != LedgerAccountType.EXPENSE || featureService.isEnabled(Feature.EXPENSE_LEDGER_ACCOUNT))
            .map(AccountingGetService::defaultLedgerAccountTemplateFromType)
            .collect(Collectors.toList());
    }

    /**
     * This is used by the FE to show all available types for ledger account creation
     * @return {@link LedgerAccount}
     */
    public List<LedgerAccount> getAllLedgerAccountTemplates() {
        // TODO : Presently this might not seem important as all ledger types are enabled by default but this will be useful when we have feature flags for ledger types
        List<LedgerAccount> defaultLedgerAccounts = getDefaultLedgerAccountTemplates();
        return new ArrayList<>(defaultLedgerAccounts);
    }

    public List<LedgerAccount> getDefaultLedgerAccounts() {
        return ledgerAccountDAO.getDefaultLedgerAccounts();
    }

    public LedgerAccount getLedgerAccountById(String ledgerAccountId) {
        Validator.validateStringNotBlank(ledgerAccountId, "ledgerAccountId is required");

        return ledgerAccountDAO
            .getLedgerAccountByLedgerAccountId(ledgerAccountId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.LEDGER_ACCOUNT, ledgerAccountId));
    }

    public List<LedgerAccount> getLedgerAccountsByIds(List<String> ledgerAccountIds) {
        return ledgerAccountDAO.getLedgerAccountsByIds(ledgerAccountIds);
    }

    public Optional<LedgerAccount> getLedgerAccountByName(String ledgerAccountId) {
        Validator.validateStringNotBlank(ledgerAccountId, "ledgerAccountName is required");

        return ledgerAccountDAO.getLedgerAccountByName(ledgerAccountId);
    }

    public Optional<LedgerAccount> getLedgerAccountByCode(String ledgerAccountCode) {
        Validator.validateStringNotBlank(ledgerAccountCode, "ledgerAccountCode is required");

        return ledgerAccountDAO.getLedgerAccountByCode(ledgerAccountCode);
    }

    private static LedgerAccount defaultLedgerAccountTemplateFromType(LedgerAccountType ledgerAccountType) {
        String defaultAccountName = String.format(ledgerAccountType.getUserFriendlyName());
        String defaultAccountDescription = ledgerAccountType == LedgerAccountType.EXPENSE
            ? String.format("Default %s, applicable for bank fees", ledgerAccountType.getUserFriendlyName())
            : String.format(DEFAULT_ACCOUNT_DESCRIPTION_FORMAT, ledgerAccountType.getUserFriendlyName());
        return new LedgerAccount(
            null/*id will be generated on insert*/,
            defaultAccountName,
            ledgerAccountType.getDefaultAccountCode(),
            defaultAccountDescription,
            ledgerAccountType,
            // this is a default account
            true
        );
    }

    public RunningBalance getRunningBalance(
        String entityId,
        String tenantEntityId,
        RunningBalanceType runningBalanceType,
        Instant asOf,
        Long sourceEventSequenceNumber
    ) {
        return journalEntryDAO
            .getRunningBalance(entityId, runningBalanceType, asOf, sourceEventSequenceNumber)
            .orElse(
                RunningBalance.builder()
                    .id(UUID.randomUUID().toString())
                    .tenantEntityId(tenantEntityId)
                    .entityId(entityId)
                    .type(runningBalanceType)
                    .asOf(asOf)
                    .sourceEventSequenceNumber(sourceEventSequenceNumber)
                    .balance(BigDecimal.ZERO)
                    .build()
            );
    }

    public List<AccountingFxBalance> getAccountingFxBalances(String runningBalanceId) {
        return journalEntryDAO.getAccountingFxBalances(runningBalanceId);
    }

    public PageResult<List<AccountingEvent>, AccountingEventPageToken> getAccountingEventsInRequisiteOrder(
        PageRequest<AccountingEventPageToken> pageRequest,
        Period period
    ) {
        return journalEntryDAO.getAccountingEventsInRequisiteOrder(pageRequest, period);
    }

    public PageResult<List<JournalEntry>, JournalEntryPageToken> getJournalEntriesOrderedByAccountingDate(
        PageRequest<JournalEntryPageToken> pageRequest,
        Period period
    ) {
        return journalEntryDAO.getJournalEntriesOrderedByAccountingDate(pageRequest, period);
    }

    public boolean areAccountingEventsPresentForAllTransactionTypes() {
        return (
            areAccountingEventsPresentForAllPostedInvoices() &&
            areAccountingEventsPresentForAllProcessedPayments() &&
            areAccountingEventsPresentForAllPostedCreditMemos() &&
            areAccountingEventsPresentForAllRecognizedRevenues() &&
            areAccountingEventsPresentForAllVoidedPayments() &&
            areAccountingEventsPresentForAllVoidedInvoices() &&
            areAccountingEventsPresentForAllPostedRealizedGainLosses()
        );
    }

    private boolean areAccountingEventsPresentForAllPostedInvoices() {
        return journalEntryDAO.areAccountingEventsPresentForAllPostedInvoices();
    }

    private boolean areAccountingEventsPresentForAllVoidedInvoices() {
        return journalEntryDAO.areAccountingEventsPresentForAllVoidedInvoices();
    }

    private boolean areAccountingEventsPresentForAllProcessedPayments() {
        return journalEntryDAO.areAccountingEventsPresentForAllProcessedPayments();
    }

    private boolean areAccountingEventsPresentForAllVoidedPayments() {
        return journalEntryDAO.areAccountingEventsPresentForAllVoidedPayments();
    }

    private boolean areAccountingEventsPresentForAllPostedCreditMemos() {
        return journalEntryDAO.areAccountingEventsPresentForAllPostedCreditMemos();
    }

    private boolean areAccountingEventsPresentForAllRecognizedRevenues() {
        return journalEntryDAO.areAccountingEventsPresentForAllRecognizedRevenues();
    }

    private boolean areAccountingEventsPresentForAllPostedRealizedGainLosses() {
        return journalEntryDAO.areAccountingEventsPresentForAllPostedRealizedGainLosses();
    }

    public List<JournalEntry> getJournalEntriesBySourceTransactionId(String transactionId) {
        Validator.validateStringNotBlank(transactionId, "transactionId is required");

        return journalEntryDAO.getJournalEntriesBySourceTransactionId(transactionId);
    }
}
