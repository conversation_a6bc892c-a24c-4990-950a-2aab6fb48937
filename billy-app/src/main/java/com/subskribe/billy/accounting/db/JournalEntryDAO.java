package com.subskribe.billy.accounting.db;

import static com.subskribe.billy.jooq.default_schema.tables.AccountingEventLog.ACCOUNTING_EVENT_LOG;
import static com.subskribe.billy.jooq.default_schema.tables.AccountingFxBalance.ACCOUNTING_FX_BALANCE;
import static com.subskribe.billy.jooq.default_schema.tables.AccountingJournalEntry.ACCOUNTING_JOURNAL_ENTRY;
import static com.subskribe.billy.jooq.default_schema.tables.AccountingJournalEntryLine.ACCOUNTING_JOURNAL_ENTRY_LINE;
import static com.subskribe.billy.jooq.default_schema.tables.AccountingRunningBalance.ACCOUNTING_RUNNING_BALANCE;
import static com.subskribe.billy.jooq.default_schema.tables.CreditMemo.CREDIT_MEMO;
import static com.subskribe.billy.jooq.default_schema.tables.Invoice.INVOICE;
import static com.subskribe.billy.jooq.default_schema.tables.RealizedGainLoss.REALIZED_GAIN_LOSS;
import static com.subskribe.billy.jooq.default_schema.tables.RecognitionTransaction.RECOGNITION_TRANSACTION;
import static com.subskribe.billy.jooq.default_schema.tables.SettlementApplication.SETTLEMENT_APPLICATION;

import com.google.common.collect.Streams;
import com.subskribe.billy.accounting.model.AccountingEvent;
import com.subskribe.billy.accounting.model.AccountingEventPageToken;
import com.subskribe.billy.accounting.model.AccountingFxBalance;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.JournalEntry;
import com.subskribe.billy.accounting.model.JournalEntryPageToken;
import com.subskribe.billy.accounting.model.JournalLine;
import com.subskribe.billy.accounting.model.RunningBalance;
import com.subskribe.billy.accounting.model.RunningBalanceType;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoicesettlement.model.CreditMemoStatus;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationStatus;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationType;
import com.subskribe.billy.jooq.default_schema.Indexes;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountingEventLogRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountingFxBalanceRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountingJournalEntryLineRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountingJournalEntryRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountingRunningBalanceRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

@SuppressFBWarnings("RV_RETURN_VALUE_IGNORED")
public class JournalEntryDAO {

    private static final Logger LOGGER = LoggerFactory.getLogger(JournalEntryDAO.class);

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    private final JournalEntryMapper journalEntryMapper;

    @Inject
    public JournalEntryDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        journalEntryMapper = Mappers.getMapper(JournalEntryMapper.class);
    }

    public void deleteAccountingEventLog(
        Configuration configuration,
        JournalEntry.TransactionType sourceTransactionType,
        String sourceTransactionId
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = DSL.using(configuration);

        int rows = dslContext
            .deleteFrom(ACCOUNTING_EVENT_LOG)
            .where(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_TYPE.eq(sourceTransactionType.name()))
            .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.eq(sourceTransactionId))
            .execute();

        if (rows > 1) {
            throw new InvariantCheckFailedException(
                String.format("More than one accounting event log record deleted with type %s and id %s", sourceTransactionType, sourceTransactionId)
            );
        }
    }

    public boolean areAccountingEventsPendingJournalEntryCreation(AccountingPeriod accountingPeriod) {
        Validator.checkNonNullInternal(accountingPeriod, "accounting period cannot be null here");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        // left join ACCOUNTING_EVENT_LOG with ACCOUNTING_JOURNAL_ENTRY and look for NULL journal entry ids
        AccountingEventLogRecord recordWithNoJournalEntry = dslContext
            .select(ACCOUNTING_EVENT_LOG.fields())
            .from(ACCOUNTING_EVENT_LOG)
            .leftJoin(ACCOUNTING_JOURNAL_ENTRY)
            .on(
                ACCOUNTING_EVENT_LOG.TENANT_ID.eq(ACCOUNTING_JOURNAL_ENTRY.TENANT_ID).and(
                    ACCOUNTING_EVENT_LOG.ID.eq(ACCOUNTING_JOURNAL_ENTRY.SOURCE_EVENT_ID.cast(UUID.class))
                )
            )
            .where(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_EVENT_LOG.ACCOUNTING_DATE.ge(DateTimeConverter.instantToLocalDateTime(accountingPeriod.getStartDate())))
            .and(ACCOUNTING_EVENT_LOG.ACCOUNTING_DATE.lt(DateTimeConverter.instantToLocalDateTime(accountingPeriod.getEndDate())))
            // if there is a NULL journal entry id then at least one event does not have corresponding journal entry
            .and(ACCOUNTING_JOURNAL_ENTRY.JOURNAL_ENTRY_ID.isNull())
            .limit(1)
            //NOTE: we can do fetch one since we have a limit of 1
            .fetchOneInto(AccountingEventLogRecord.class);
        return recordWithNoJournalEntry != null;
    }

    public void writeAccountingEventToLog(AccountingEvent accountingEvent) {
        Validator.checkNonNullInternal(accountingEvent, "accounting event cannot be null here");

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        AccountingEventLogRecord logRecord = journalEntryMapper.toRecord(accountingEvent);
        logRecord.setTenantId(tenantId);

        try {
            int insertedCount = dslContext.insertInto(ACCOUNTING_EVENT_LOG).set(logRecord).execute();
            if (insertedCount != 1) {
                String message = String.format(
                    "Could not insert accounting event for source entity type %s and id %s",
                    accountingEvent.getSourceTransactionType(),
                    accountingEvent.getSourceTransactionId()
                );
                throw new ServiceFailureException(message);
            }
        } catch (DataAccessException ex) {
            if (
                PostgresErrorHandler.causeIsThisSpecificUniqueConstraintViolation(ex, Indexes.INDEX_ACCOUNTING_EVENT_LOG_TENANT_ID_EVENT_ID.getName())
            ) {
                LOGGER.info("accounting event already created for event with id: {} nothing to do!", accountingEvent.getSourceEventId());
                return;
            }
            throw ex;
        }
    }

    public PageResult<List<AccountingEvent>, AccountingEventPageToken> getAccountingEventsInRequisiteOrder(
        PageRequest<AccountingEventPageToken> pageRequest,
        Period period
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var queryStrep = dslContext
            .select()
            .from(ACCOUNTING_EVENT_LOG)
            .where(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_EVENT_LOG.ACCOUNTING_DATE.ge(DateTimeConverter.instantToLocalDateTime(period.getStart())))
            .and(ACCOUNTING_EVENT_LOG.ACCOUNTING_DATE.lt(DateTimeConverter.instantToLocalDateTime(period.getEnd())));

        // deal with page token if not null
        if (pageRequest.getPageToken() != null) {
            AccountingEventPageToken pageToken = pageRequest.getPageToken();
            queryStrep = queryStrep
                .and(ACCOUNTING_EVENT_LOG.ACCOUNTING_DATE.gt(DateTimeConverter.instantToLocalDateTime(pageToken.getAccountingDate())))
                .or(
                    // NOTE: in case the accounting date is the same, the source event sequence number acts as a tiebreaker
                    ACCOUNTING_EVENT_LOG.ACCOUNTING_DATE.eq(DateTimeConverter.instantToLocalDateTime(pageToken.getAccountingDate())).and(
                        ACCOUNTING_EVENT_LOG.SOURCE_EVENT_SEQUENCE_NUMBER.gt(pageToken.getSourceEventSequenceNumber())
                    )
                );
        }

        // finally order by accounting date and id NOTE: id is for sort stability since there might two events with same accounting date
        List<AccountingEventLogRecord> logRecords = queryStrep
            .orderBy(
                ACCOUNTING_EVENT_LOG.ACCOUNTING_DATE,
                /* CAUTION : change here has a lot of consequences.
                    When accounting events have the same accounting date the sequence number decides which one is processed first
                    so the order in which they are published in the stream is important */
                ACCOUNTING_EVENT_LOG.SOURCE_EVENT_SEQUENCE_NUMBER
            )
            .limit(pageRequest.getLimit())
            .fetchInto(AccountingEventLogRecord.class);

        List<AccountingEvent> accountingEvents = logRecords.stream().map(journalEntryMapper::toAccountingEvent).toList();
        return PageResult.fromCollectionAndRequest(accountingEvents, pageRequest, event ->
            AccountingEventPageToken.from(event.getAccountingDate(), event.getSourceEventSequenceNumber())
        );
    }

    public PageResult<List<JournalEntry>, JournalEntryPageToken> getJournalEntriesOrderedByAccountingDate(
        PageRequest<JournalEntryPageToken> pageRequest,
        Period period
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var query = dslContext
            .select()
            .from(ACCOUNTING_JOURNAL_ENTRY)
            .where(ACCOUNTING_JOURNAL_ENTRY.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_JOURNAL_ENTRY.ACCOUNTING_DATE.ge(DateTimeConverter.instantToLocalDateTime(period.getStart())))
            .and(ACCOUNTING_JOURNAL_ENTRY.ACCOUNTING_DATE.lt(DateTimeConverter.instantToLocalDateTime(period.getEnd())));

        // deal with page token if not null
        if (pageRequest.getPageToken() != null) {
            JournalEntryPageToken pageToken = pageRequest.getPageToken();
            query = query
                .and(ACCOUNTING_JOURNAL_ENTRY.ACCOUNTING_DATE.ge(DateTimeConverter.instantToLocalDateTime(pageToken.getAccountingDate())))
                // NOTE: in case the accounting date is the same ID provides the stable sort order
                // whence it is safe to greater than operator here
                .and(ACCOUNTING_JOURNAL_ENTRY.ID.gt(UUID.fromString(pageToken.getJournalEntryId())));
        }

        // finally, order by accounting date and id
        // NOTE: id is for sort stability since there might two events with same accounting date
        List<AccountingJournalEntryRecord> records = query
            .orderBy(ACCOUNTING_JOURNAL_ENTRY.ACCOUNTING_DATE, ACCOUNTING_JOURNAL_ENTRY.ID)
            .limit(pageRequest.getLimit())
            .fetchInto(AccountingJournalEntryRecord.class);

        if (CollectionUtils.isEmpty(records)) {
            return PageResult.withResultAndNextToken(Collections.emptyList(), null);
        }

        // populate journal lines
        List<JournalEntry.Builder> journalEntryBuilders = records.stream().map(journalEntryMapper::fromRecordToBuilder).toList();
        List<String> journalEntryIds = records.stream().map(AccountingJournalEntryRecord::getJournalEntryId).collect(Collectors.toList());
        Map<String, List<JournalLine>> journalLinesByJournalEntryId = getJournalLinesByJournalEntryIds(journalEntryIds)
            .stream()
            .collect(Collectors.groupingBy(JournalLine::getJournalEntryId));
        journalEntryBuilders.forEach(journalEntryBuilder ->
            journalEntryBuilder.setJournalLines(journalLinesByJournalEntryId.get(journalEntryBuilder.build().getJournalEntryId()))
        );

        // validate journal lines
        List<JournalEntry> journalEntries = journalEntryBuilders.stream().map(JournalEntry.Builder::build).toList();
        journalEntries.forEach(journalEntry -> {
            if (!journalEntry.hasValidState()) {
                throw new ServiceFailureException("invalid journal entry state: " + journalEntry.getJournalEntryId());
            }
        });

        // prepare and share the page result
        JournalEntry lastEntry = Streams.findLast(journalEntries.stream()).orElseThrow();
        return PageResult.withResultAndNextToken(
            journalEntries,
            JournalEntryPageToken.from(lastEntry.getAccountingDate(), lastEntry.getId().toString())
        );
    }

    public List<JournalLine> getJournalLinesByJournalEntryIds(List<String> journalEntryIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var records = dslContext
            .select()
            .from(ACCOUNTING_JOURNAL_ENTRY_LINE)
            .where(ACCOUNTING_JOURNAL_ENTRY_LINE.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_JOURNAL_ENTRY_LINE.JOURNAL_ENTRY_ID.in(journalEntryIds))
            .and(ACCOUNTING_JOURNAL_ENTRY_LINE.IS_DELETED.isFalse())
            .fetchInto(AccountingJournalEntryLineRecord.class);
        return journalEntryMapper.fromRecords(records);
    }

    private Optional<RunningBalance> getRunningBalanceForForSameAsOf(
        String entityId,
        RunningBalanceType runningBalanceType,
        Instant asOf,
        Long sourceEventSequenceNumber
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<AccountingRunningBalanceRecord> sameAsOfBalances = dslContext
            .select()
            .from(ACCOUNTING_RUNNING_BALANCE)
            .where(ACCOUNTING_RUNNING_BALANCE.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_RUNNING_BALANCE.ENTITY_ID.eq(entityId))
            .and(ACCOUNTING_RUNNING_BALANCE.TYPE.eq(runningBalanceType.name()))
            .and(ACCOUNTING_RUNNING_BALANCE.AS_OF.eq(DateTimeConverter.instantToLocalDateTime(asOf)))
            /*
            Because we update the balance on conflict we need to know the balance as_of <= accounting_date e.g.
            two accounting events E1 and E2 published with the same accounting_date but different sequence numbers
            S1 and S2 where S2 > S1
            when processing event E2 with sequence number S2 (will be processed after S1) we need to know the balance
            as_of <= accounting_date rather than as_of < accounting_date because we need to include the balance as seen
            when processing S1 which as the same accounting_date
             */
            .orderBy(ACCOUNTING_RUNNING_BALANCE.SOURCE_EVENT_SEQUENCE_NUMBER.desc())
            .fetchInto(AccountingRunningBalanceRecord.class);

        if (CollectionUtils.isEmpty(sameAsOfBalances)) {
            return Optional.empty();
        }

        // for the same as of find the balance that is lower in sequence number
        Optional<AccountingRunningBalanceRecord> balanceOptional = sameAsOfBalances
            .stream()
            .filter(record -> record.getSourceEventSequenceNumber().compareTo(sourceEventSequenceNumber) < 0)
            .findFirst();

        return balanceOptional.map(journalEntryMapper::fromRecord);
    }

    public Optional<RunningBalance> getRunningBalance(
        String entityId,
        RunningBalanceType runningBalanceType,
        Instant asOf,
        Long sourceEventSequenceNumber
    ) {
        Validator.checkArgumentNotBlankInternal(entityId, "entity id cannot be blank");
        Validator.checkNonNullInternal(runningBalanceType, "running balance type cannot be null");
        Validator.checkNonNullInternal(asOf, "as of time for querying running balances cannot be null");
        Validator.checkNonNullInternal(sourceEventSequenceNumber, "source event sequence number cannot be null");
        Validator.checkStateInternal(sourceEventSequenceNumber.compareTo(0L) >= 0, "source event sequence number cannot be negative");

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        // if we find a balance with same asOf but lower sequence number we return that
        Optional<RunningBalance> sameAsOfBalance = getRunningBalanceForForSameAsOf(entityId, runningBalanceType, asOf, sourceEventSequenceNumber);
        if (sameAsOfBalance.isPresent()) {
            return sameAsOfBalance;
        }

        // if did not find a balance with same asOf we simply return a with lower asOf
        AccountingRunningBalanceRecord record = dslContext
            .select()
            .from(ACCOUNTING_RUNNING_BALANCE)
            .where(ACCOUNTING_RUNNING_BALANCE.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_RUNNING_BALANCE.ENTITY_ID.eq(entityId))
            .and(ACCOUNTING_RUNNING_BALANCE.TYPE.eq(runningBalanceType.name()))
            //NOTE: here we are checking for asOf < asked for
            .and(ACCOUNTING_RUNNING_BALANCE.AS_OF.lessThan(DateTimeConverter.instantToLocalDateTime(asOf)))
            .orderBy(ACCOUNTING_RUNNING_BALANCE.AS_OF.desc())
            .limit(1)
            .fetchOneInto(AccountingRunningBalanceRecord.class);

        return Optional.ofNullable(journalEntryMapper.fromRecord(record));
    }

    public List<AccountingFxBalance> getAccountingFxBalances(String runningBalanceId) {
        Validator.checkArgumentNotBlankInternal(runningBalanceId, "running balance id cannot be blank");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<AccountingFxBalanceRecord> records = dslContext
            .select()
            .from(ACCOUNTING_FX_BALANCE)
            .where(ACCOUNTING_FX_BALANCE.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_FX_BALANCE.RUNNING_BALANCE_ID.eq(UUID.fromString(runningBalanceId)))
            .fetchInto(AccountingFxBalanceRecord.class);

        return journalEntryMapper.fromFxBalanceRecords(records);
    }

    public void persistJournalEntry(
        JournalEntry journalEntry,
        Collection<RunningBalance> runningBalances,
        Collection<AccountingFxBalance> fxBalances,
        Long sourceEventSequenceNumber
    ) {
        Validator.checkNonNullInternal(journalEntry, "journal entry to persist cannot be null");
        Collection<RunningBalance> runningBalancesNonNull = CollectionUtils.isEmpty(runningBalances) ? List.of() : runningBalances;

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        AccountingJournalEntryRecord journalEntryRecord = journalEntryMapper.toRecord(journalEntry);
        journalEntryRecord.setTenantId(tenantId);
        List<AccountingJournalEntryLineRecord> journalEntryLineRecords = journalEntry
            .getJournalLines()
            .stream()
            .map(jeLine -> {
                AccountingJournalEntryLineRecord lineRecord = journalEntryMapper.toRecord(jeLine);
                lineRecord.setTenantId(tenantId);
                return lineRecord;
            })
            .collect(Collectors.toList());

        Collection<AccountingRunningBalanceRecord> balanceRecords = runningBalancesNonNull
            .stream()
            .map(journalEntryMapper::toRecord)
            .collect(Collectors.toList());

        Collection<AccountingFxBalanceRecord> fxBalanceRecords = journalEntryMapper.toFxBalanceRecords(fxBalances);

        dslContext.transaction(configuration -> {
            DSLContext txnContext = DSL.using(configuration);
            writeAllInTransaction(
                txnContext,
                journalEntryRecord,
                journalEntryLineRecords,
                balanceRecords,
                fxBalanceRecords,
                sourceEventSequenceNumber
            );
        });
    }

    public List<JournalEntry> getJournalEntriesBySourceTransactionId(String transactionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<AccountingJournalEntryRecord> records = dslContext
            .selectFrom(ACCOUNTING_JOURNAL_ENTRY)
            .where(ACCOUNTING_JOURNAL_ENTRY.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_JOURNAL_ENTRY.SOURCE_TRANSACTION_ID.eq(transactionId))
            .fetchInto(AccountingJournalEntryRecord.class);

        return journalEntryMapper.fromJournalEntryRecords(records);
    }

    private void writeAllInTransaction(
        DSLContext txnContext,
        AccountingJournalEntryRecord journalEntryRecord,
        List<AccountingJournalEntryLineRecord> journalEntryLineRecords,
        Collection<AccountingRunningBalanceRecord> balanceRecords,
        Collection<AccountingFxBalanceRecord> fxBalanceRecords,
        Long sourceEventSequenceNumber
    ) {
        try {
            writeRecordsInTransaction(txnContext, journalEntryRecord, journalEntryLineRecords, balanceRecords, fxBalanceRecords);
        } catch (DataAccessException ex) {
            if (
                PostgresErrorHandler.causeIsThisSpecificUniqueConstraintViolation(
                    ex,
                    Indexes.INDEX_ACCOUNTING_JOURNAL_ENTRY_TENANT_ID_EVENT_ID.getName()
                )
            ) {
                LOGGER.info(
                    "journal entry already created for event with id: {} entries will be purged and recreated!",
                    journalEntryRecord.getSourceEventId()
                );
                purgeAndWriteEntries(journalEntryRecord, journalEntryLineRecords, balanceRecords, fxBalanceRecords, sourceEventSequenceNumber);
                LOGGER.info("successfully rewrote journal entries for source event id: {}", journalEntryRecord.getSourceEventId());
                return;
            }
            throw ex;
        }
    }

    private void purgeAndWriteEntries(
        AccountingJournalEntryRecord journalEntryRecord,
        List<AccountingJournalEntryLineRecord> journalEntryLineRecords,
        Collection<AccountingRunningBalanceRecord> balanceRecords,
        Collection<AccountingFxBalanceRecord> fxBalanceRecords,
        Long sourceEventSequenceNumber
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        // we have to start a new transaction here since the previous one has been rolled back
        // because of the unique constraint violation
        DSLContext newTransactionContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        newTransactionContext.transaction(configuration -> {
            purgeJournalEntryAndBalances(configuration.dsl(), journalEntryRecord, balanceRecords, sourceEventSequenceNumber);
            // now simply write the new replacing journal entry, journal lines and balances
            writeRecordsInTransaction(configuration.dsl(), journalEntryRecord, journalEntryLineRecords, balanceRecords, fxBalanceRecords);
        });
    }

    private void purgeJournalEntryAndBalances(
        DSLContext txnContext,
        AccountingJournalEntryRecord journalEntryRecord,
        Collection<AccountingRunningBalanceRecord> balanceRecords,
        Long sourceEventSequenceNumber
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        // first get the existing journal entry
        String storedJournalEntryId = txnContext
            .select(ACCOUNTING_JOURNAL_ENTRY.JOURNAL_ENTRY_ID)
            .from(ACCOUNTING_JOURNAL_ENTRY)
            .where(ACCOUNTING_JOURNAL_ENTRY.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_JOURNAL_ENTRY.SOURCE_EVENT_ID.eq(journalEntryRecord.getSourceEventId()))
            .fetchOneInto(String.class);

        if (StringUtils.isBlank(storedJournalEntryId)) {
            throw new ServiceFailureException(
                String.format(
                    "Conflicting journal entry with source event id: %s and journal entry id: %s not found while reading",
                    journalEntryRecord.getSourceEventId(),
                    journalEntryRecord.getJournalEntryId()
                )
            );
        }

        // purge journal lines matching the journal entry id
        txnContext
            .deleteFrom(ACCOUNTING_JOURNAL_ENTRY_LINE)
            .where(ACCOUNTING_JOURNAL_ENTRY_LINE.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_JOURNAL_ENTRY_LINE.JOURNAL_ENTRY_ID.eq(storedJournalEntryId))
            .execute();

        // now purge the journal entry itself
        txnContext
            .deleteFrom(ACCOUNTING_JOURNAL_ENTRY)
            .where(ACCOUNTING_JOURNAL_ENTRY.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_JOURNAL_ENTRY.JOURNAL_ENTRY_ID.eq(storedJournalEntryId))
            .execute();

        // gather and delete any running balances as of the journal entry accounting date
        Set<UUID> runningBalanceIdsToDelete = new HashSet<>();
        balanceRecords.forEach(balance -> {
            List<UUID> runningBalanceIds = txnContext
                .select(ACCOUNTING_RUNNING_BALANCE.ID)
                .from(ACCOUNTING_RUNNING_BALANCE)
                .where(ACCOUNTING_RUNNING_BALANCE.TENANT_ID.eq(tenantId))
                .and(ACCOUNTING_RUNNING_BALANCE.ENTITY_ID.eq(balance.getEntityId()))
                //NOTE: we are purging balances of all types
                .and(ACCOUNTING_RUNNING_BALANCE.AS_OF.gt(journalEntryRecord.getAccountingDate()))
                .or(
                    // when there is a tie with the as of date source event sequence number acts as a tiebreaker
                    ACCOUNTING_RUNNING_BALANCE.AS_OF.eq(journalEntryRecord.getAccountingDate()).and(
                        ACCOUNTING_RUNNING_BALANCE.SOURCE_EVENT_SEQUENCE_NUMBER.ge(sourceEventSequenceNumber)
                    )
                )
                .fetchInto(UUID.class);
            runningBalanceIdsToDelete.addAll(runningBalanceIds);
        });

        if (!runningBalanceIdsToDelete.isEmpty()) {
            // delete FX balances
            var fxBalanceDeleteCount = txnContext
                .deleteFrom(ACCOUNTING_FX_BALANCE)
                .where(ACCOUNTING_FX_BALANCE.TENANT_ID.eq(tenantId))
                .and(ACCOUNTING_FX_BALANCE.RUNNING_BALANCE_ID.in(runningBalanceIdsToDelete))
                .execute();
            LOGGER.info("purged {} accounting fx balance records", fxBalanceDeleteCount);

            // delete running balances
            var runningBalanceDeleteCount = txnContext
                .deleteFrom(ACCOUNTING_RUNNING_BALANCE)
                .where(ACCOUNTING_RUNNING_BALANCE.TENANT_ID.eq(tenantId))
                .and(ACCOUNTING_RUNNING_BALANCE.ID.in(runningBalanceIdsToDelete))
                .execute();
            LOGGER.info("purged {} accounting running balance records", runningBalanceDeleteCount);
        }
    }

    private void writeRecordsInTransaction(
        DSLContext txnContext,
        AccountingJournalEntryRecord journalEntryRecord,
        List<AccountingJournalEntryLineRecord> journalEntryLineRecords,
        Collection<AccountingRunningBalanceRecord> balanceRecords,
        Collection<AccountingFxBalanceRecord> fxBalanceRecords
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        AccountingJournalEntryRecord stored = txnContext.insertInto(ACCOUNTING_JOURNAL_ENTRY).set(journalEntryRecord).returning().fetchOne();
        if (stored == null) {
            throw new ServiceFailureException(
                String.format("could not insert journal entry for source transaction: %s", journalEntryRecord.getSourceTransactionId())
            );
        }
        writeJournalLinesInTransaction(txnContext, journalEntryLineRecords, stored);
        insertBalancesInTransactionContext(txnContext, balanceRecords, tenantId);
        insertFxBalancesInTransactionContext(txnContext, fxBalanceRecords, tenantId);
    }

    private void insertBalancesInTransactionContext(
        DSLContext txnContext,
        Collection<AccountingRunningBalanceRecord> balanceRecords,
        String tenantId
    ) {
        balanceRecords.forEach(record -> {
            record.setTenantId(tenantId);
            int insertedCount = txnContext.insertInto(ACCOUNTING_RUNNING_BALANCE).set(record).execute();

            if (insertedCount != 1) {
                String message = String.format("Could not insert balance for entity id %s and type %s", record.getEntityId(), record.getType());
                throw new ServiceFailureException(message);
            }
        });
    }

    private void insertFxBalancesInTransactionContext(
        DSLContext txnContext,
        Collection<AccountingFxBalanceRecord> fxBalanceRecords,
        String tenantId
    ) {
        fxBalanceRecords.forEach(record -> record.setTenantId(tenantId));
        int insertedCount = txnContext.insertInto(ACCOUNTING_FX_BALANCE).set(fxBalanceRecords).execute();
        if (insertedCount != fxBalanceRecords.size()) {
            throw new ServiceFailureException("Could not insert fx balances");
        }
    }

    private void writeJournalLinesInTransaction(
        DSLContext txnContext,
        List<AccountingJournalEntryLineRecord> journalEntryLineRecords,
        AccountingJournalEntryRecord stored
    ) {
        journalEntryLineRecords.forEach(record -> record.setJournalId(stored.getId()));

        // the 0 index and skip 1 is because of JOOQ batch insert semantics
        var insertSetMoreStep = txnContext.insertInto(ACCOUNTING_JOURNAL_ENTRY_LINE).set(journalEntryLineRecords.get(0));
        journalEntryLineRecords.stream().skip(1).forEach(record -> insertSetMoreStep.newRecord().set(record));
        int insertedCount = insertSetMoreStep.execute();
        if (insertedCount != journalEntryLineRecords.size()) {
            throw new ServiceFailureException("could not insert all journal entry lines");
        }
    }

    public boolean areAccountingEventsPresentForAllPostedInvoices() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return !dslContext.fetchExists(
            dslContext
                .select()
                .from(INVOICE)
                .leftJoin(ACCOUNTING_EVENT_LOG)
                .on(
                    ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.eq(INVOICE.INVOICE_NUMBER)
                        .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_TYPE.eq(JournalEntry.TransactionType.INVOICE_POSTED.name()))
                        .and(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
                )
                .where(INVOICE.TENANT_ID.eq(tenantId))
                .and(INVOICE.IS_DELETED.isFalse())
                .and(INVOICE.STATUS.eq(InvoiceStatus.POSTED.name()))
                .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.isNull())
        );
    }

    public boolean areAccountingEventsPresentForAllVoidedInvoices() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return !dslContext.fetchExists(
            dslContext
                .select()
                .from(INVOICE)
                .leftJoin(ACCOUNTING_EVENT_LOG)
                .on(
                    ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.eq(INVOICE.INVOICE_NUMBER)
                        .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_TYPE.eq(JournalEntry.TransactionType.INVOICE_VOIDED.name()))
                        .and(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
                )
                .where(INVOICE.TENANT_ID.eq(tenantId))
                .and(INVOICE.IS_DELETED.isFalse())
                .and(INVOICE.STATUS.eq(InvoiceStatus.VOIDED.name()))
                .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.isNull())
        );
    }

    public boolean areAccountingEventsPresentForAllProcessedPayments() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return !dslContext.fetchExists(
            dslContext
                .select()
                .from(SETTLEMENT_APPLICATION)
                .leftJoin(ACCOUNTING_EVENT_LOG)
                .on(
                    ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.eq(SETTLEMENT_APPLICATION.ID.cast(String.class))
                        .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_TYPE.eq(JournalEntry.TransactionType.PAYMENT_PROCESSED.name()))
                        .and(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
                )
                .where(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
                .and(SETTLEMENT_APPLICATION.STATUS.isNull().or(SETTLEMENT_APPLICATION.STATUS.eq(SettlementApplicationStatus.APPLIED_PAYMENT.name())))
                .and(SETTLEMENT_APPLICATION.APPLICATION_TYPE.eq(SettlementApplicationType.PAYMENT.name()))
                .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.isNull())
        );
    }

    public boolean areAccountingEventsPresentForAllVoidedPayments() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return !dslContext.fetchExists(
            dslContext
                .select()
                .from(SETTLEMENT_APPLICATION)
                .leftJoin(ACCOUNTING_EVENT_LOG)
                .on(
                    ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.eq(SETTLEMENT_APPLICATION.ID.cast(String.class))
                        .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_TYPE.eq(JournalEntry.TransactionType.PAYMENT_VOIDED.name()))
                        .and(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
                )
                .where(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
                .and(SETTLEMENT_APPLICATION.STATUS.isNull().or(SETTLEMENT_APPLICATION.STATUS.eq(SettlementApplicationStatus.APPLIED_PAYMENT.name())))
                .and(SETTLEMENT_APPLICATION.APPLICATION_TYPE.eq(SettlementApplicationType.VOID_PAYMENT.name()))
                .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.isNull())
        );
    }

    public boolean areAccountingEventsPresentForAllPostedCreditMemos() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return !dslContext.fetchExists(
            dslContext
                .select()
                .from(CREDIT_MEMO)
                .leftJoin(ACCOUNTING_EVENT_LOG)
                .on(
                    ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.eq(CREDIT_MEMO.CREDIT_MEMO_NUMBER)
                        .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_TYPE.eq(JournalEntry.TransactionType.CREDIT_MEMO_POSTED.name()))
                        .and(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
                )
                .where(CREDIT_MEMO.TENANT_ID.eq(tenantId))
                .and(CREDIT_MEMO.IS_DELETED.isFalse())
                .and(CREDIT_MEMO.STATUS.eq(CreditMemoStatus.POSTED.name()))
                .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.isNull())
        );
    }

    public boolean areAccountingEventsPresentForAllRecognizedRevenues() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return !dslContext.fetchExists(
            dslContext
                .select()
                .from(RECOGNITION_TRANSACTION)
                .leftJoin(ACCOUNTING_EVENT_LOG)
                .on(
                    ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.eq(RECOGNITION_TRANSACTION.RECOGNITION_TRANSACTION_ID)
                        .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_TYPE.eq(JournalEntry.TransactionType.REVENUE_RECOGNIZED.name()))
                        .and(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
                )
                .where(RECOGNITION_TRANSACTION.TENANT_ID.eq(tenantId))
                .and(RECOGNITION_TRANSACTION.IS_DELETED.isFalse())
                .and(RECOGNITION_TRANSACTION.IS_RECOGNIZED.isTrue())
                .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.isNull())
        );
    }

    public boolean areAccountingEventsPresentForAllPostedRealizedGainLosses() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return !dslContext.fetchExists(
            dslContext
                .select()
                .from(REALIZED_GAIN_LOSS)
                .leftJoin(ACCOUNTING_EVENT_LOG)
                .on(
                    ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.eq(REALIZED_GAIN_LOSS.ID.cast(String.class))
                        .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_TYPE.eq(JournalEntry.TransactionType.REALIZED_GAIN_LOSS_POSTED.name()))
                        .and(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
                )
                .where(REALIZED_GAIN_LOSS.TENANT_ID.eq(tenantId))
                .and(ACCOUNTING_EVENT_LOG.SOURCE_TRANSACTION_ID.isNull())
        );
    }

    public void purgeAllJournalEntryLineItemsForEntity(DSLContext dslContext, String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .deleteFrom(ACCOUNTING_JOURNAL_ENTRY_LINE)
            .where(ACCOUNTING_JOURNAL_ENTRY_LINE.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_JOURNAL_ENTRY_LINE.ENTITY_ID.eq(entityId))
            .execute();
    }

    public void purgeAllJournalEntriesForEntity(DSLContext dslContext, String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .deleteFrom(ACCOUNTING_JOURNAL_ENTRY)
            .where(ACCOUNTING_JOURNAL_ENTRY.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_JOURNAL_ENTRY.ENTITY_ID.eq(entityId))
            .execute();
    }

    public void purgeAllAccountingRunningBalancesForEntity(DSLContext dslContext, String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .deleteFrom(ACCOUNTING_RUNNING_BALANCE)
            .where(ACCOUNTING_RUNNING_BALANCE.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_RUNNING_BALANCE.ENTITY_ID.eq(entityId))
            .execute();
    }

    public void purgeAllAccountingEventLogsForEntity(DSLContext dslContext, String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .deleteFrom(ACCOUNTING_EVENT_LOG)
            .where(ACCOUNTING_EVENT_LOG.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_EVENT_LOG.ENTITY_ID.eq(entityId))
            .execute();
    }
}
