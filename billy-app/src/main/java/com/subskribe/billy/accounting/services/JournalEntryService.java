package com.subskribe.billy.accounting.services;

import static com.subskribe.billy.accounting.journalentry.JournalingHelper.parseRevenueRecognizedEventPayload;

import com.subskribe.billy.accounting.journalentry.CreditMemoFxJournalProcessor;
import com.subskribe.billy.accounting.journalentry.InvoicePostedFxJournalProcessor;
import com.subskribe.billy.accounting.journalentry.InvoicePostedJournalCreator;
import com.subskribe.billy.accounting.journalentry.InvoiceVoidedJournalCreator;
import com.subskribe.billy.accounting.journalentry.JournalingHelper;
import com.subskribe.billy.accounting.journalentry.PaymentProcessedJournalCreator;
import com.subskribe.billy.accounting.journalentry.PaymentVoidedJournalCreator;
import com.subskribe.billy.accounting.journalentry.RealizedGainLossJournalCreator;
import com.subskribe.billy.accounting.journalentry.RevenueRecognizedFxJournalProcessor;
import com.subskribe.billy.accounting.journalentry.RevenueRecognizedJournalCreator;
import com.subskribe.billy.accounting.journalentry.SettlementFxJournalProcessor;
import com.subskribe.billy.accounting.journalentry.StandAloneCreditMemoJournalProcessor;
import com.subskribe.billy.accounting.model.AccountingEvent;
import com.subskribe.billy.accounting.model.AccountingEventPageToken;
import com.subskribe.billy.accounting.model.AccountingFxBalance;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.AccountingPeriodStatus;
import com.subskribe.billy.accounting.model.FxBalanceContext;
import com.subskribe.billy.accounting.model.ImmutableInvoiceLineItemBalances;
import com.subskribe.billy.accounting.model.InvoiceLineItemBalances;
import com.subskribe.billy.accounting.model.JournalEntry;
import com.subskribe.billy.accounting.model.JournalEntryAndBalances;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.model.RevenueRecognitionJournalMetadata;
import com.subskribe.billy.accounting.model.RunningBalance;
import com.subskribe.billy.accounting.model.RunningBalanceContext;
import com.subskribe.billy.accounting.model.RunningBalanceType;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.foreignexchange.model.RealizedGainLoss;
import com.subskribe.billy.foreignexchange.service.RealizedGainLossService;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.model.CreditMemoLineItem;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationType;
import com.subskribe.billy.invoicesettlement.services.CreditMemoRetrievalService;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.postgres.PostgresAdvisoryLock;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.report.service.ReportService;
import com.subskribe.billy.revrec.model.RecognitionEventPayload;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.revrec.model.RecognitionSchedule;
import com.subskribe.billy.revrec.model.RecognitionTransaction;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.shared.entitycache.EntityCache;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenantjob.model.ImmutableTenantJob;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobModule;
import com.subskribe.billy.tenantjob.model.TenantJobObjectModel;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.tenantjob.service.TenantJobGetService;
import com.subskribe.billy.validation.Validator;
import java.io.InputStream;
import java.time.Instant;
import java.util.Collection;
import java.util.Currency;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

public class JournalEntryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(JournalEntryService.class);

    private static final Set<AccountingPeriodStatus> VALID_ACCT_PERIOD_STATUS_FOR_JE_CREATION = Set.of(
        AccountingPeriodStatus.OPEN,
        AccountingPeriodStatus.CLOSE_IN_PROGRESS
    );

    private static final String JOURNAL_ENTRY_LOCK_FORMAT = "%s/je_creation_lock";

    // TODO: the selection of the batch size is arbitrary, can be tweaked based on perf numbers
    // TODO: increasing the page size to 1000 is a broken fix, ordered pagination of accounting events
    // TODO: sorted by accounting_date and source_event_seq number is not working as expected seems like
    // TODO: comparison paging is busted, until then increasing the number of AEL entries we fetch needs revisit
    private static final int JOURNAL_ENTRY_CREATION_BATCH_SIZE = 1000;

    private static final String JOURNAL_ENTRY_REPORT_ID = "journal_entries";
    private static final String JOURNAL_ENTRY_SUMMARY_REPORT_ID = "journal_entries_summary";

    private static final long JOURNAL_ENTRY_REPORT_DEFAULT_NUMBER_OF_MONTHS = 12L;

    private static final String REPORT_ACCOUNTING_PERIOD_START_NAME = "start";
    private static final String REPORT_ACCOUNTING_PERIOD_END_NAME = "end";

    private final InvoiceRetrievalService invoiceRetrievalService;

    private final InvoiceService invoiceService;

    private final ProductCatalogGetService productCatalogGetService;

    private final CreditMemoRetrievalService creditMemoRetrievalService;

    private final InvoiceSettlementService invoiceSettlementService;

    private final AccountingService accountingService;
    private final AccountingGetService accountingGetService;

    private final RevenueRecognitionGetService revenueRecognitionGetService;

    private final RealizedGainLossService realizedGainLossService;

    private final OrderGetService orderGetService;

    private final AccountingPeriodService accountingPeriodService;

    private final EntityGetService entityGetService;

    private final InvoicePostedJournalCreator invoicePostedJournalCreator;

    private final InvoicePostedFxJournalProcessor invoicePostedFxJournalProcessor;

    private final PaymentProcessedJournalCreator paymentProcessedJournalCreator;

    private final SettlementFxJournalProcessor settlementFxJournalProcessor;

    private final CreditMemoFxJournalProcessor creditMemoFxJournalProcessor;

    private final PaymentVoidedJournalCreator paymentVoidedJournalCreator;

    private final StandAloneCreditMemoJournalProcessor standAloneCreditMemoJournalProcessor;

    private final RevenueRecognizedJournalCreator revenueRecognizedJournalCreator;

    private final RevenueRecognizedFxJournalProcessor revenueRecognizedFxJournalProcessor;

    private final InvoiceVoidedJournalCreator invoiceVoidedJournalCreator;

    private final RealizedGainLossJournalCreator realizedGainLossJournalCreator;

    private final ReportService reportService;

    private final TenantJobDispatcherService tenantJobDispatcherService;

    private final TenantJobGetService tenantJobGetService;

    private final FeatureService featureService;

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    @Inject
    public JournalEntryService(
        InvoiceRetrievalService invoiceRetrievalService,
        InvoiceService invoiceService,
        ProductCatalogGetService productCatalogGetService,
        CreditMemoRetrievalService creditMemoRetrievalService,
        InvoiceSettlementService invoiceSettlementService,
        AccountingService accountingService,
        AccountingGetService accountingGetService,
        RevenueRecognitionGetService revenueRecognitionGetService,
        RealizedGainLossService realizedGainLossService,
        OrderGetService orderGetService,
        AccountingPeriodService accountingPeriodService,
        EntityGetService entityGetService,
        InvoicePostedJournalCreator invoicePostedJournalCreator,
        InvoicePostedFxJournalProcessor invoicePostedFxJournalProcessor,
        PaymentProcessedJournalCreator paymentProcessedJournalCreator,
        SettlementFxJournalProcessor settlementFxJournalProcessor,
        CreditMemoFxJournalProcessor creditMemoFxJournalProcessor,
        PaymentVoidedJournalCreator paymentVoidedJournalCreator,
        StandAloneCreditMemoJournalProcessor creditMemoPostedJournalCreator,
        RevenueRecognizedJournalCreator revenueRecognizedJournalCreator,
        RevenueRecognizedFxJournalProcessor revenueRecognizedFxJournalProcessor,
        InvoiceVoidedJournalCreator invoiceVoidedJournalCreator,
        RealizedGainLossJournalCreator realizedGainLossJournalCreator,
        ReportService reportService,
        TenantJobDispatcherService tenantJobDispatcherService,
        TenantJobGetService tenantJobGetService,
        FeatureService featureService,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider
    ) {
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.invoiceService = invoiceService;
        this.productCatalogGetService = productCatalogGetService;
        this.creditMemoRetrievalService = creditMemoRetrievalService;
        this.invoiceSettlementService = invoiceSettlementService;
        this.accountingService = accountingService;
        this.accountingGetService = accountingGetService;
        this.revenueRecognitionGetService = revenueRecognitionGetService;
        this.realizedGainLossService = realizedGainLossService;
        this.orderGetService = orderGetService;
        this.accountingPeriodService = accountingPeriodService;
        this.entityGetService = entityGetService;
        this.invoicePostedJournalCreator = invoicePostedJournalCreator;
        this.invoicePostedFxJournalProcessor = invoicePostedFxJournalProcessor;
        this.paymentProcessedJournalCreator = paymentProcessedJournalCreator;
        this.settlementFxJournalProcessor = settlementFxJournalProcessor;
        this.creditMemoFxJournalProcessor = creditMemoFxJournalProcessor;
        this.paymentVoidedJournalCreator = paymentVoidedJournalCreator;
        standAloneCreditMemoJournalProcessor = creditMemoPostedJournalCreator;
        this.revenueRecognizedJournalCreator = revenueRecognizedJournalCreator;
        this.revenueRecognizedFxJournalProcessor = revenueRecognizedFxJournalProcessor;
        this.invoiceVoidedJournalCreator = invoiceVoidedJournalCreator;
        this.realizedGainLossJournalCreator = realizedGainLossJournalCreator;
        this.reportService = reportService;
        this.tenantJobDispatcherService = tenantJobDispatcherService;
        this.tenantJobGetService = tenantJobGetService;
        this.featureService = featureService;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
    }

    public void writeJournalEntriesForAccountingPeriodAsync(String accountingPeriodId) {
        Optional<TenantJob> activeJobOptional = tenantJobGetService.getActiveJobByType(TenantJobType.CREATE_JOURNAL_ENTRIES);

        if (activeJobOptional.isPresent()) {
            TenantJob activeJob = activeJobOptional.get();
            String message = String.format(
                "Journal entry creation in progress state: %s created on: %s UTC, New journal entry creation possible only after this",
                activeJob.getStatus(),
                activeJob.getCreatedOn()
            );
            throw new InvalidInputException(message);
        }

        tenantJobDispatcherService.dispatch(
            ImmutableTenantJob.builder()
                .module(TenantJobModule.ACCOUNTING)
                .jobType(TenantJobType.CREATE_JOURNAL_ENTRIES)
                .objectModel(TenantJobObjectModel.ACCOUNTING_PERIOD)
                .objectId(accountingPeriodId)
                .build()
        );
    }

    public void writeJournalEntriesForAccountingPeriod(String accountingPeriodId) {
        try {
            String tenantId = tenantIdProvider.provideTenantIdString();
            DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
            dslContext.transaction(configuration -> {
                // TODO: once journal entry creation process moves offline we need not do this locking
                // TODO: do not lock once the journal entry creation process is moved offline
                // since the journal entry creation process is inline and on demand we need to protect
                // the creation of journal entries when concurrent users kick of JE creation for same accounting period
                String lockIdentifier = String.format(JOURNAL_ENTRY_LOCK_FORMAT, tenantId);
                Optional<Long> keyHashOptional = PostgresAdvisoryLock.tryAcquireLock(DSL.using(configuration), lockIdentifier);
                if (keyHashOptional.isEmpty()) {
                    String message = String.format(
                        "Journal entry creation is already in progress for Accounting Period: %s, Please try again later",
                        accountingPeriodId
                    );
                    throw new ConflictingStateException(message);
                }
                writeJournalEntries(accountingPeriodId);
            });
        } catch (ObjectNotFoundException ex) {
            String message = String.format(
                "could not find required entity of type: %s and id: %s while creating journal entries for accounting period: %s",
                ex.getObjectName(),
                ex.getObjectId(),
                accountingPeriodId
            );
            throw new ServiceFailureException(message, ex);
        }
    }

    public Pair<InputStream, String> generateJournalEntryReport(String accountingPeriodId) {
        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(accountingPeriodId);
        Map<String, Object> params = getReportParamMap(accountingPeriod);
        return reportService.runPredefinedReport(JOURNAL_ENTRY_REPORT_ID, params);
    }

    public Pair<InputStream, String> generateJournalEntryReport() {
        List<AccountingPeriod> accountingPeriods = accountingPeriodService.getRecentAccountingPeriods(JOURNAL_ENTRY_REPORT_DEFAULT_NUMBER_OF_MONTHS);
        Map<String, Object> params = getReportParamMap(accountingPeriods);
        return reportService.runPredefinedReport(JOURNAL_ENTRY_REPORT_ID, params);
    }

    public Pair<InputStream, String> generateJournalEntrySummaryReport(String accountingPeriodId) {
        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(accountingPeriodId);
        Map<String, Object> params = getReportParamMap(accountingPeriod);
        return reportService.runPredefinedReport(JOURNAL_ENTRY_SUMMARY_REPORT_ID, params);
    }

    public Pair<InputStream, String> generateJournalEntrySummaryReport() {
        List<AccountingPeriod> accountingPeriods = accountingPeriodService.getRecentAccountingPeriods(JOURNAL_ENTRY_REPORT_DEFAULT_NUMBER_OF_MONTHS);
        Map<String, Object> params = getReportParamMap(accountingPeriods);
        return reportService.runPredefinedReport(JOURNAL_ENTRY_SUMMARY_REPORT_ID, params);
    }

    private Map<String, Object> getReportParamMap(AccountingPeriod accountingPeriod) {
        Map<String, Object> intervalParam = Map.of(
            REPORT_ACCOUNTING_PERIOD_START_NAME,
            accountingPeriod.getStartDate().getEpochSecond(),
            REPORT_ACCOUNTING_PERIOD_END_NAME,
            accountingPeriod.getEndDate().getEpochSecond()
        );
        return Map.of("accounting_date", intervalParam);
    }

    private Map<String, Object> getReportParamMap(List<AccountingPeriod> accountingPeriods) {
        Validator.validateCollectionNotEmpty(accountingPeriods, "accounting periods");
        Map<String, Object> intervalParam = Map.of(
            REPORT_ACCOUNTING_PERIOD_START_NAME,
            accountingPeriods.get(0).getStartDate().getEpochSecond(),
            REPORT_ACCOUNTING_PERIOD_END_NAME,
            accountingPeriods.get(accountingPeriods.size() - 1).getEndDate().getEpochSecond()
        );
        return Map.of("accounting_date", intervalParam);
    }

    private void writeJournalEntries(String accountingPeriodId) {
        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(accountingPeriodId);
        if (!VALID_ACCT_PERIOD_STATUS_FOR_JE_CREATION.contains(accountingPeriod.getStatus())) {
            throw new ConflictingStateException(
                String.format(
                    "accounting period: %s not in one of the required states: %s for journal entry creation",
                    accountingPeriodId,
                    VALID_ACCT_PERIOD_STATUS_FOR_JE_CREATION
                )
            );
        }

        Period accountingPeriodInterval = Period.between(accountingPeriod.getStartDate(), accountingPeriod.getEndDate());
        // process in ordered batches accounting events and generate journal entries
        LOGGER.info(
            "Generating journal entries for accounting period: {} from {} to {}",
            accountingPeriod.getAccountingPeriodId(),
            accountingPeriod.getStartDate(),
            accountingPeriod.getEndDate()
        );

        EntityCacheContext entityCacheContext = makeEntityCacheContext();
        AccountingEventPageToken pageToken = null;
        do {
            PageResult<List<AccountingEvent>, AccountingEventPageToken> pageResult = accountingGetService.getAccountingEventsInRequisiteOrder(
                PageRequest.from(pageToken, JOURNAL_ENTRY_CREATION_BATCH_SIZE),
                accountingPeriodInterval
            );

            if (CollectionUtils.isNotEmpty(pageResult.getResult())) {
                List<AccountingEvent> accountingEvents = pageResult.getResult();
                LOGGER.info("Generating journal entries for {} accounting events", accountingEvents.size());
                accountingEvents.forEach(event -> createJournalEntry(event, entityCacheContext));
            }
            pageToken = pageResult.getNextPageToken();
        } while (pageToken != null);
        LOGGER.info("Successfully generated journal entries for accounting period: {}", accountingPeriod.getAccountingPeriodId());
    }

    private EntityCacheContext makeEntityCacheContext() {
        return new EntityCacheContext(
            EntityCache.of(productCatalogGetService::getChargeByChargeId),
            EntityCache.of(accountingGetService::getLedgerAccountsForCharge),
            EntityCache.of(ruleId ->
                revenueRecognitionGetService
                    .getRecognitionRuleByRuleId(ruleId)
                    .orElseThrow(() ->
                        new ServiceFailureException(String.format("Recognition rule with id %s not found during journal creation", ruleId))
                    )
            ),
            EntityCache.of(productCatalogGetService::getPlanByUuid),
            EntityCache.of(invoiceRetrievalService::getInvoiceWithActiveItems)
        );
    }

    private void createJournalEntry(AccountingEvent event, EntityCacheContext entityCacheContext) {
        switch (event.getSourceTransactionType()) {
            case INVOICE_POSTED -> writeInvoicePostedJournalEntries(event, event.getSourceTransactionId(), entityCacheContext);
            case INVOICE_VOIDED -> writeInvoiceVoidedJournalEntries(event, entityCacheContext);
            case PAYMENT_PROCESSED -> writePaymentProcessedJournalEntry(event, entityCacheContext);
            case CREDIT_MEMO_POSTED -> writeCreditMemoPostedJournalEntry(event, entityCacheContext);
            case REVENUE_RECOGNIZED -> writeRevenueRecognizedJournalEntry(event, entityCacheContext);
            case PAYMENT_VOIDED -> writePaymentVoidedJournalEntry(event, entityCacheContext);
            case REALIZED_GAIN_LOSS_POSTED -> writeRealizedGainLossJournalEntry(event, entityCacheContext);
            default -> throw new UnsupportedOperationException("Unknown event: " + event.getSourceTransactionType());
        }
    }

    private void writeRevenueRecognizedJournalEntry(AccountingEvent event, EntityCacheContext entityCacheContext) {
        RecognitionEventPayload eventPayload = parseRevenueRecognizedEventPayload(event);
        RecognitionSchedule recognitionSchedule = revenueRecognitionGetService.getRecognitionScheduleByScheduleId(eventPayload.getScheduleId());

        RevenueRecognitionJournalMetadata journalMetadata = getRevRecJournalMetadata(recognitionSchedule, entityCacheContext);

        RecognitionTransaction recognitionTransaction = getRecognitionTransactionFromPayload(eventPayload);

        Entity entity = entityGetService.getEntityById(recognitionTransaction.getEntityId());
        Map<String, List<LedgerAccount>> chargeLedgerAccountsMap = getChargeLedgerAccountsMap(
            List.of(journalMetadata.getCharge().getChargeId()),
            entityCacheContext.chargeLedgerAccountsEntityCache
        );
        RecognitionRule recognitionRule = entityCacheContext.recognitionRuleEntityCache.get(journalMetadata.getCharge().getRecognitionRuleId());

        RunningBalance deferredRevenueBalance = accountingGetService.getRunningBalance(
            recognitionTransaction.getScheduleId(),
            recognitionTransaction.getEntityId(),
            RunningBalanceType.DEFERRED_REVENUE,
            event.getAccountingDate(),
            event.getSourceEventSequenceNumber()
        );
        RunningBalance contractAssetBalance = accountingGetService.getRunningBalance(
            recognitionTransaction.getScheduleId(),
            recognitionTransaction.getEntityId(),
            RunningBalanceType.CONTRACT_ASSET,
            event.getAccountingDate(),
            event.getSourceEventSequenceNumber()
        );
        // load fx balances for each running balance
        FxBalanceContext fxBalanceContext = loadFxBalancesByRunningBalances(contractAssetBalance, deferredRevenueBalance);

        JournalEntryAndBalances journalEntryAndBalances = revenueRecognizedJournalCreator.createJournalEntry(
            event,
            eventPayload,
            recognitionTransaction,
            journalMetadata,
            deferredRevenueBalance,
            contractAssetBalance,
            chargeLedgerAccountsMap,
            recognitionRule
        );

        if (featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            JournalEntryAndBalances decoratedJournalEntryAndBalances = revenueRecognizedFxJournalProcessor.decorateJournalEntry(
                journalEntryAndBalances,
                entity,
                deferredRevenueBalance,
                contractAssetBalance,
                fxBalanceContext
            );

            Collection<AccountingFxBalance> updatedFxBalances = filterUpdatedFxBalances(journalEntryAndBalances, fxBalanceContext);

            accountingService.persistJournalEntry(
                decoratedJournalEntryAndBalances.getJournalEntry(),
                decoratedJournalEntryAndBalances.getBalances(),
                updatedFxBalances,
                event.getSourceEventSequenceNumber()
            );
        } else {
            accountingService.persistJournalEntry(
                journalEntryAndBalances.getJournalEntry(),
                journalEntryAndBalances.getBalances(),
                event.getSourceEventSequenceNumber()
            );
        }

        LOGGER.info("Journal entry processed with {} lines", journalEntryAndBalances.getJournalEntry().getJournalLines().size());
    }

    private RecognitionTransaction getRecognitionTransactionFromPayload(RecognitionEventPayload eventPayload) {
        List<RecognitionTransaction> transactions = revenueRecognitionGetService.getRecognitionTransactionsByScheduleId(eventPayload.getScheduleId());
        Map<String, RecognitionTransaction> transactionById = transactions
            .stream()
            .collect(Collectors.toMap(RecognitionTransaction::getRecognitionTransactionId, Function.identity()));
        if (!transactionById.containsKey(eventPayload.getTransactionId())) {
            String message = String.format(
                "Could not find revrec transaction %s in schedule %s",
                eventPayload.getTransactionId(),
                eventPayload.getScheduleId()
            );
            throw new ServiceFailureException(message);
        }

        RecognitionTransaction recognitionTransaction = transactionById.get(eventPayload.getTransactionId());
        if (BooleanUtils.isFalse(recognitionTransaction.getIsRecognized())) {
            String message = String.format("unrecognized revrec transaction %s published in the event stream", eventPayload.getTransactionId());
            throw new ServiceFailureException(message);
        }
        return recognitionTransaction;
    }

    private RevenueRecognitionJournalMetadata getRevRecJournalMetadata(
        RecognitionSchedule recognitionSchedule,
        EntityCacheContext entityCacheContext
    ) {
        switch (recognitionSchedule.getSourceTransactionType()) {
            case ORDER -> {
                OrderLineItem orderLineItem = orderGetService
                    .getOrderLineItemByOrderLineItemId(recognitionSchedule.getOrderLineItemId())
                    .orElseThrow(() ->
                        new ServiceFailureException(
                            String.format(
                                "could not find order line item with id: %s for revrec schedule with id %s",
                                recognitionSchedule.getOrderLineItemId(),
                                recognitionSchedule.getScheduleId()
                            )
                        )
                    );
                Charge ordLineCharge = entityCacheContext.chargeEntityCache.get(orderLineItem.getChargeId());
                Plan ordLinePlan = entityCacheContext.planEntityCache.get(ordLineCharge.getPlanUuid());
                return RevenueRecognitionJournalMetadata.builder()
                    .charge(ordLineCharge)
                    .plan(ordLinePlan)
                    .currency(ordLinePlan.getCurrency())
                    .build();
            }
            case ORDER_RAMP_GROUP -> {
                List<OrderLineItem> orderLineItems = orderGetService.getOrderLineItemsByRampGroupId(recognitionSchedule.getRampGroupId());
                OrderLineItem firstLineItem = orderLineItems
                    .stream()
                    .findFirst()
                    .orElseThrow(() ->
                        new ServiceFailureException(
                            String.format(
                                "could not find order line item for ramp group with id %s for revrec schedule with id %s",
                                recognitionSchedule.getRampGroupId(),
                                recognitionSchedule.getScheduleId()
                            )
                        )
                    );
                Charge ordLineCharge = entityCacheContext.chargeEntityCache.get(firstLineItem.getChargeId());
                Plan ordLinePlan = entityCacheContext.planEntityCache.get(ordLineCharge.getPlanUuid());
                return RevenueRecognitionJournalMetadata.builder()
                    .charge(ordLineCharge)
                    .plan(ordLinePlan)
                    .currency(ordLinePlan.getCurrency())
                    .build();
            }
            case INVOICE -> {
                InvoiceItem invoiceItem = invoiceService
                    .getInvoiceItemById(recognitionSchedule.getInvoiceLineItemId())
                    .orElseThrow(() ->
                        new ServiceFailureException(
                            String.format(
                                "could not find invoice line item with id: %s for revrec schedule with id %s",
                                recognitionSchedule.getInvoiceLineItemId(),
                                recognitionSchedule.getScheduleId()
                            )
                        )
                    );
                Charge invCharge = entityCacheContext.chargeEntityCache.get(invoiceItem.getChargeId());
                Plan invPlan = entityCacheContext.planEntityCache.get(invCharge.getPlanUuid());
                return RevenueRecognitionJournalMetadata.builder().charge(invCharge).plan(invPlan).currency(invPlan.getCurrency()).build();
            }
            case CREDIT_MEMO -> {
                CreditMemo creditMemo = creditMemoRetrievalService.getCreditMemoByLineItemId(recognitionSchedule.getCreditMemoLineItemId());
                CreditMemoLineItem creditMemoLineItem = creditMemo
                    .getLineItems()
                    .stream()
                    .filter(cmLine -> cmLine.getId().equals(recognitionSchedule.getCreditMemoLineItemId()))
                    .findFirst()
                    .orElseThrow(() ->
                        new ServiceFailureException(
                            String.format(
                                "could not find CM with line item :%s for revrec schedule %s",
                                recognitionSchedule.getCreditMemoLineItemId(),
                                recognitionSchedule.getScheduleId()
                            )
                        )
                    );
                // TODO: figure out a way to this for stand alone credit memo since they might not have charge or plan
                Charge cmCharge = entityCacheContext.chargeEntityCache.get(creditMemoLineItem.getChargeId());
                Plan cmPlan = entityCacheContext.planEntityCache.get(cmCharge.getPlanUuid());
                return RevenueRecognitionJournalMetadata.builder()
                    .charge(cmCharge)
                    .plan(cmPlan)
                    .currency(Currency.getInstance(creditMemo.getCurrencyCode()))
                    .build();
            }
            default -> {
                String message = String.format(
                    "unknown source transaction type %s for recognition schedule %s",
                    recognitionSchedule.getSourceTransactionType(),
                    recognitionSchedule.getScheduleId()
                );
                throw new ServiceFailureException(message);
            }
        }
    }

    private void writeInvoicePostedJournalEntries(AccountingEvent event, String invoiceNumberStr, EntityCacheContext entityCacheContext) {
        Invoice.Number invoiceNumber = new Invoice.Number(invoiceNumberStr);
        Invoice invoice = entityCacheContext.invoiceEntityCache.get(invoiceNumber);
        List<InvoiceItem> invoiceItems = invoice.getInvoiceItems();

        List<String> chargeIds = invoiceItems.stream().map(InvoiceItem::getChargeId).distinct().collect(Collectors.toList());
        Map<String, Charge> chargeMap = chargeIds
            .stream()
            .map(entityCacheContext.chargeEntityCache::get)
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        Map<String, RecognitionRule> recognitionRuleMap = chargeMap
            .values()
            .stream()
            .map(Charge::getRecognitionRuleId)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toMap(Function.identity(), entityCacheContext.recognitionRuleEntityCache::get));
        Map<String, List<LedgerAccount>> chargeLedgerAccountsMap = getChargeLedgerAccountsMap(
            chargeIds,
            entityCacheContext.chargeLedgerAccountsEntityCache
        );
        Map<UUID, Plan> planMap = getPlanMap(chargeMap.values(), entityCacheContext.planEntityCache);

        // collect balances by order invoice line item id
        RunningBalanceContext runningBalanceContext = getRunningBalanceContext(invoiceItems, event, chargeMap, recognitionRuleMap);
        RunningBalanceContext prevRunningBalanceContext = RunningBalanceContext.copy(runningBalanceContext);

        FxBalanceContext fxBalanceContext = loadFxBalancesByContext(runningBalanceContext);
        Entity entity = entityGetService.getEntityById(invoice.getEntityId());
        JournalEntryAndBalances journalEntryAndBalances = invoicePostedJournalCreator.createJournalEntry(
            event,
            invoice,
            chargeMap,
            chargeLedgerAccountsMap,
            planMap,
            recognitionRuleMap,
            runningBalanceContext
        );

        if (featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            JournalEntryAndBalances decoratedJournalEntryAndBalances = invoicePostedFxJournalProcessor.decorateJournalEntry(
                journalEntryAndBalances,
                invoice,
                entity,
                fxBalanceContext,
                prevRunningBalanceContext,
                runningBalanceContext
            );

            Collection<AccountingFxBalance> updatedFxBalances = filterUpdatedFxBalances(journalEntryAndBalances, fxBalanceContext);

            accountingService.persistJournalEntry(
                decoratedJournalEntryAndBalances.getJournalEntry(),
                decoratedJournalEntryAndBalances.getBalances(),
                updatedFxBalances,
                event.getSourceEventSequenceNumber()
            );
        } else {
            accountingService.persistJournalEntry(
                journalEntryAndBalances.getJournalEntry(),
                journalEntryAndBalances.getBalances(),
                event.getSourceEventSequenceNumber()
            );
        }
        LOGGER.info("Journal entry processed with {} lines", journalEntryAndBalances.getJournalEntry().getJournalLines().size());
    }

    // select only the fx balances for which running balances are updated
    private Collection<AccountingFxBalance> filterUpdatedFxBalances(
        JournalEntryAndBalances journalEntryAndBalances,
        FxBalanceContext fxBalanceContext
    ) {
        Set<String> runningBalanceIds = journalEntryAndBalances.getBalances().stream().map(RunningBalance::getId).collect(Collectors.toSet());
        return fxBalanceContext.getBalancesByRunningBalanceIds(runningBalanceIds);
    }

    public FxBalanceContext loadFxBalancesByContext(RunningBalanceContext runningBalanceContext) {
        var runningBalances = runningBalanceContext.getAllBalances();
        Map<String, List<AccountingFxBalance>> fxBalancesMap = runningBalances
            .stream()
            .collect(
                HashMap::new,
                (map, runningBalance) -> map.put(runningBalance.getId(), accountingGetService.getAccountingFxBalances(runningBalance.getId())),
                Map::putAll
            );
        return new FxBalanceContext(fxBalancesMap);
    }

    public FxBalanceContext loadFxBalancesByRunningBalances(RunningBalance contractAssetBalance, RunningBalance deferredRevenueBalance) {
        var runningBalances = List.of(contractAssetBalance, deferredRevenueBalance);
        Map<String, List<AccountingFxBalance>> fxBalancesMap = runningBalances
            .stream()
            .collect(
                HashMap::new,
                (map, runningBalance) -> map.put(runningBalance.getId(), accountingGetService.getAccountingFxBalances(runningBalance.getId())),
                Map::putAll
            );
        return new FxBalanceContext(fxBalancesMap);
    }

    private void writeInvoiceVoidedJournalEntries(AccountingEvent event, EntityCacheContext entityCacheContext) {
        Invoice invoice = entityCacheContext.invoiceEntityCache().get(new Invoice.Number(event.getSourceTransactionId()));
        List<InvoiceItem> invoiceItems = invoice.getInvoiceItems();

        List<String> chargeIds = invoiceItems.stream().map(InvoiceItem::getChargeId).distinct().collect(Collectors.toList());
        Map<String, Charge> chargeMap = chargeIds
            .stream()
            .map(entityCacheContext.chargeEntityCache::get)
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        Map<String, RecognitionRule> recognitionRuleMap = chargeMap
            .values()
            .stream()
            .map(Charge::getRecognitionRuleId)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toMap(Function.identity(), entityCacheContext.recognitionRuleEntityCache::get));
        Map<String, List<LedgerAccount>> chargeLedgerAccountsMap = getChargeLedgerAccountsMap(
            chargeIds,
            entityCacheContext.chargeLedgerAccountsEntityCache
        );
        Map<UUID, Plan> planMap = getPlanMap(chargeMap.values(), entityCacheContext.planEntityCache);

        // collect balances by order invoice line item id
        RunningBalanceContext runningBalanceContext = getRunningBalanceContext(invoiceItems, event, chargeMap, recognitionRuleMap);
        RunningBalanceContext prevRunningBalanceContext = RunningBalanceContext.copy(runningBalanceContext);

        FxBalanceContext fxBalanceContext = loadFxBalancesByContext(runningBalanceContext);
        Entity entity = entityGetService.getEntityById(invoice.getEntityId());

        JournalEntryAndBalances journalEntryAndBalances = invoiceVoidedJournalCreator.createJournalEntry(
            event,
            invoice,
            chargeMap,
            chargeLedgerAccountsMap,
            planMap,
            recognitionRuleMap,
            runningBalanceContext
        );

        if (featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            JournalEntryAndBalances decoratedJournalEntryAndBalances = invoicePostedFxJournalProcessor.decorateJournalEntry(
                journalEntryAndBalances,
                invoice,
                entity,
                fxBalanceContext,
                prevRunningBalanceContext,
                runningBalanceContext
            );

            Collection<AccountingFxBalance> updatedFxBalances = filterUpdatedFxBalances(journalEntryAndBalances, fxBalanceContext);

            accountingService.persistJournalEntry(
                decoratedJournalEntryAndBalances.getJournalEntry(),
                decoratedJournalEntryAndBalances.getBalances(),
                updatedFxBalances,
                event.getSourceEventSequenceNumber()
            );
        } else {
            accountingService.persistJournalEntry(
                journalEntryAndBalances.getJournalEntry(),
                journalEntryAndBalances.getBalances(),
                event.getSourceEventSequenceNumber()
            );
        }

        LOGGER.info("Journal entry processed with {} lines", journalEntryAndBalances.getJournalEntry().getJournalLines().size());
    }

    private RunningBalanceContext getRunningBalanceContext(
        List<InvoiceItem> invoiceItems,
        AccountingEvent accountingEvent,
        Map<String, Charge> chargeMap,
        Map<String, RecognitionRule> recognitionRuleMap
    ) {
        Map<String, String> invoiceItemIdToRevenueScheduleMap = invoiceLineItemToScheduleIdMap(invoiceItems, chargeMap, recognitionRuleMap);
        Map<String, InvoiceLineItemBalances> scheduleIdToBalancesMap = invoiceItemIdToRevenueScheduleMap
            .values()
            .stream()
            .distinct()
            .collect(Collectors.toMap(Function.identity(), scheduleId -> getRunningBalancesForSchedule(scheduleId, accountingEvent)));
        return RunningBalanceContext.from(invoiceItemIdToRevenueScheduleMap, scheduleIdToBalancesMap);
    }

    private Map<String, String> invoiceLineItemToScheduleIdMap(
        List<InvoiceItem> invoiceItems,
        Map<String, Charge> chargeMap,
        Map<String, RecognitionRule> recognitionRuleMap
    ) {
        return invoiceItems
            .stream()
            .filter(item -> JournalingHelper.isInvoiceItemASC606Candidate(item, chargeMap, recognitionRuleMap))
            // we process only non-zero items for JE creation
            .filter(InvoicePostedJournalCreator.INVOICE_ITEM_NON_ZERO_AMOUNT)
            .collect(Collectors.toMap(item -> item.getId().id(), item -> getRecognitionScheduleForInvoiceItem(item).getScheduleId()));
    }

    RecognitionSchedule getRecognitionScheduleForInvoiceItem(InvoiceItem item) {
        OrderLineItem orderLineItem = orderGetService
            .getOrderLineItemByOrderLineItemId(item.getOrderLineItemId())
            .orElseThrow(() ->
                new ServiceFailureException(String.format("Order line item not found for Order based invoice item: %s", item.getId().id()))
            );
        return fetchRevenueRecognitionSchedule(item, orderLineItem);
    }

    private InvoiceLineItemBalances getRunningBalancesForSchedule(String scheduleId, AccountingEvent accountingEvent) {
        RunningBalance contractAssetBalance = accountingGetService.getRunningBalance(
            scheduleId,
            accountingEvent.getEntityId(),
            RunningBalanceType.CONTRACT_ASSET,
            accountingEvent.getAccountingDate(),
            accountingEvent.getSourceEventSequenceNumber()
        );
        RunningBalance deferredRevenueBalance = accountingGetService.getRunningBalance(
            scheduleId,
            accountingEvent.getEntityId(),
            RunningBalanceType.DEFERRED_REVENUE,
            accountingEvent.getAccountingDate(),
            accountingEvent.getSourceEventSequenceNumber()
        );

        return ImmutableInvoiceLineItemBalances.builder()
            .contractAssetBalance(contractAssetBalance)
            .deferredRevenueBalance(deferredRevenueBalance)
            .recognitionScheduleId(scheduleId)
            .build();
    }

    public InvoiceLineItemBalances getRunningBalancesAsOf(String scheduleId, Instant asOf) {
        AccountingEvent accountingEvent = AccountingEvent.builder()
            .entityId(scheduleId)
            .accountingDate(asOf)
            .sourceEventSequenceNumber(Long.MAX_VALUE)
            .build();
        RunningBalance contractAssetBalance = accountingGetService.getRunningBalance(
            scheduleId,
            accountingEvent.getEntityId(),
            RunningBalanceType.CONTRACT_ASSET,
            accountingEvent.getAccountingDate(),
            accountingEvent.getSourceEventSequenceNumber()
        );
        RunningBalance deferredRevenueBalance = accountingGetService.getRunningBalance(
            scheduleId,
            accountingEvent.getEntityId(),
            RunningBalanceType.DEFERRED_REVENUE,
            accountingEvent.getAccountingDate(),
            accountingEvent.getSourceEventSequenceNumber()
        );

        return ImmutableInvoiceLineItemBalances.builder()
            .contractAssetBalance(contractAssetBalance)
            .deferredRevenueBalance(deferredRevenueBalance)
            .recognitionScheduleId(scheduleId)
            .build();
    }

    private RecognitionSchedule fetchRevenueRecognitionSchedule(InvoiceItem item, OrderLineItem orderLineItem) {
        if (orderLineItem.getIsRamp() && orderLineItem.getRampGroupId() != null) {
            return revenueRecognitionGetService
                .getRecognitionScheduleByOrderRampGroupId(orderLineItem.getRampGroupId())
                .orElseThrow(() ->
                    new ServiceFailureException(String.format("Recognition schedule not found for Order based invoice item: %s", item.getId().id()))
                );
        }
        return revenueRecognitionGetService
            .getRecognitionScheduleByOrderLineItemId(orderLineItem.getOrderLineId())
            .orElseThrow(() ->
                new ServiceFailureException(String.format("Recognition schedule not found for Order based invoice item: %s", item.getId().id()))
            );
    }

    private void writePaymentProcessedJournalEntry(AccountingEvent event, EntityCacheContext entityCacheContext) {
        // todo: should we introduce a short ID here?
        UUID settlementApplicationId = UUID.fromString(event.getSourceTransactionId());

        SettlementApplication settlementApplication = invoiceSettlementService.getSettlementApplicationById(settlementApplicationId);

        // CM application is not a journaling event so no journal entries
        if (settlementApplication.getApplicationType() == SettlementApplicationType.CREDIT) {
            LOGGER.info("Credit memo application is not an journal creation event");
            return;
        }

        Invoice invoice = entityCacheContext.invoiceEntityCache.get(new Invoice.Number(settlementApplication.getInvoiceNumber()));
        JournalEntry journalEntry = paymentProcessedJournalCreator.createJournalEntry(event, settlementApplication, invoice.getCurrency());
        JournalEntry decoratedJournalEntry = settlementFxJournalProcessor.decorateJournalEntry(journalEntry, settlementApplication);
        accountingService.persistJournalEntry(decoratedJournalEntry, event.getSourceEventSequenceNumber());

        LOGGER.info("Journal entry processed with {} lines", journalEntry.getJournalLines().size());
    }

    private void writePaymentVoidedJournalEntry(AccountingEvent event, EntityCacheContext entityCacheContext) {
        UUID settlementApplicationId = UUID.fromString(event.getSourceTransactionId());
        SettlementApplication settlementApplication = invoiceSettlementService.getSettlementApplicationById(settlementApplicationId);
        Invoice invoice = entityCacheContext.invoiceEntityCache().get(new Invoice.Number(settlementApplication.getInvoiceNumber()));
        JournalEntry journalEntry = paymentVoidedJournalCreator.createJournalEntry(event, settlementApplication, invoice.getCurrency());
        JournalEntry decoratedJournalEntry = settlementFxJournalProcessor.decorateJournalEntry(journalEntry, settlementApplication);
        accountingService.persistJournalEntry(decoratedJournalEntry, event.getSourceEventSequenceNumber());

        LOGGER.info("Journal entry processed with {} lines", journalEntry.getJournalLines().size());
    }

    private void writeRealizedGainLossJournalEntry(AccountingEvent event, EntityCacheContext entityCacheContext) {
        String realizedGainLossId = event.getSourceTransactionId();
        RealizedGainLoss realizedGainLoss = realizedGainLossService.getRealizedGainLossById(realizedGainLossId);
        Invoice invoice = entityCacheContext.invoiceEntityCache.get(new Invoice.Number(realizedGainLoss.getInvoiceNumber()));
        JournalEntry journalEntry = realizedGainLossJournalCreator.createJournalEntry(event, realizedGainLoss, invoice);
        accountingService.persistJournalEntry(journalEntry, event.getSourceEventSequenceNumber());

        LOGGER.info("Journal entry processed with {} lines", journalEntry.getJournalLines().size());
    }

    private void writeCreditMemoPostedJournalEntry(AccountingEvent event, EntityCacheContext entityCacheContext) {
        String creditMemoNumber = event.getSourceTransactionId();

        CreditMemo creditMemo = creditMemoRetrievalService.getCreditMemoByNumber(creditMemoNumber);

        if (StringUtils.isBlank(creditMemo.getCreatedFrom())) {
            // this is a standalone CM
            writeStandAloneCreditMemoJournalEntry(event, creditMemo, entityCacheContext);
        } else {
            // this is negative invoice that got converted to CM
            writeInvoicePostedJournalEntries(event, creditMemo.getCreatedFrom(), entityCacheContext);
        }
    }

    private void writeStandAloneCreditMemoJournalEntry(AccountingEvent event, CreditMemo creditMemo, EntityCacheContext entityCacheContext) {
        List<CreditMemoLineItem> creditMemoItems = creditMemo.getLineItems();
        List<String> chargeIds = creditMemoItems.stream().map(CreditMemoLineItem::getChargeId).distinct().collect(Collectors.toList());
        Map<String, Charge> chargeMap = chargeIds
            .stream()
            .map(entityCacheContext.chargeEntityCache::get)
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        Map<String, List<LedgerAccount>> chargeLedgerAccountsMap = getChargeLedgerAccountsMap(
            chargeIds,
            entityCacheContext.chargeLedgerAccountsEntityCache
        );
        Map<UUID, Plan> planMap = getPlanMap(chargeMap.values(), entityCacheContext.planEntityCache);

        JournalEntry journalEntry = standAloneCreditMemoJournalProcessor.createJournalEntry(
            event,
            creditMemo,
            chargeMap,
            chargeLedgerAccountsMap,
            planMap
        );
        JournalEntry decoratedJournalEntry = creditMemoFxJournalProcessor.decorateJournalEntry(journalEntry, creditMemo);
        accountingService.persistJournalEntry(decoratedJournalEntry, event.getSourceEventSequenceNumber());
        LOGGER.info("Journal entry processed with {} lines", journalEntry.getJournalLines().size());
    }

    private Map<String, List<LedgerAccount>> getChargeLedgerAccountsMap(
        List<String> chargeIds,
        EntityCache<String, List<LedgerAccount>> chargeLedgerAccountsEntityCache
    ) {
        return chargeIds.stream().collect(Collectors.toMap(Function.identity(), chargeLedgerAccountsEntityCache::get));
    }

    private Map<UUID, Plan> getPlanMap(Collection<Charge> charges, EntityCache<UUID, Plan> planEntityCache) {
        return charges.stream().map(Charge::getPlanUuid).distinct().collect(Collectors.toMap(Function.identity(), planEntityCache::get));
    }

    private record EntityCacheContext(
        EntityCache<String, Charge> chargeEntityCache,
        EntityCache<String, List<LedgerAccount>> chargeLedgerAccountsEntityCache,
        EntityCache<String, RecognitionRule> recognitionRuleEntityCache,
        EntityCache<UUID, Plan> planEntityCache,
        EntityCache<Invoice.Number, Invoice> invoiceEntityCache
    ) {}
}
