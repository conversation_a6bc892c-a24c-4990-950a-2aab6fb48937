package com.subskribe.billy.accounting.services;

import com.subskribe.billy.accounting.db.JournalEntryDAO;
import com.subskribe.billy.accounting.db.LedgerAccountDAO;
import com.subskribe.billy.accounting.model.AccountingEvent;
import com.subskribe.billy.accounting.model.AccountingFxBalance;
import com.subskribe.billy.accounting.model.JournalEntry;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.model.LedgerAccountType;
import com.subskribe.billy.accounting.model.RunningBalance;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.payment.services.PaymentBankAccountGetService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

public class AccountingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountingService.class);

    private final AccountingGetService accountingGetService;
    private final LedgerAccountDAO ledgerAccountDAO;

    private final JournalEntryDAO journalEntryDAO;

    private final LedgerAccountIdGenerator ledgerAccountIdGenerator;

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    private final ProductCatalogGetService productCatalogGetService;

    private final PaymentBankAccountGetService paymentBankAccountGetService;

    private final FeatureService featureService;

    private final EntityContextProvider entityContextProvider;

    private static final List<LedgerAccountType> SUPPORTED_CHARGE_LEDGER_ACCOUNT_MAPPINGS = List.of(
        LedgerAccountType.TAX_LIABILITY,
        LedgerAccountType.DEFERRED_REVENUE,
        LedgerAccountType.RECOGNIZED_REVENUE,
        LedgerAccountType.CONTRACT_ASSET
    );

    @Inject
    public AccountingService(
        AccountingGetService accountingGetService,
        LedgerAccountDAO ledgerAccountDAO,
        JournalEntryDAO journalEntryDAO,
        LedgerAccountIdGenerator ledgerAccountIdGenerator,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        ProductCatalogGetService productCatalogGetService,
        PaymentBankAccountGetService paymentBankAccountGetService,
        FeatureService featureService,
        EntityContextProvider entityContextProvider
    ) {
        this.accountingGetService = accountingGetService;
        this.ledgerAccountDAO = ledgerAccountDAO;
        this.journalEntryDAO = journalEntryDAO;
        this.ledgerAccountIdGenerator = ledgerAccountIdGenerator;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.productCatalogGetService = productCatalogGetService;
        this.paymentBankAccountGetService = paymentBankAccountGetService;
        this.featureService = featureService;
        this.entityContextProvider = entityContextProvider;
    }

    public List<LedgerAccount> mapLedgerAccountsToCharge(String chargeId, List<String> ledgerAccountIdsList) {
        // if the input is null/empty the intent is to delete all existing mappings
        List<String> nonNullInputList = CollectionUtils.isEmpty(ledgerAccountIdsList) ? List.of() : ledgerAccountIdsList;
        Set<String> ledgerAccountIds = new HashSet<>(nonNullInputList);
        if (ledgerAccountIds.size() != nonNullInputList.size()) {
            throw new InvalidInputException("Duplicate ledger account ids are not allowed during mapping");
        }

        accountingGetService
            .getLedgerAccountsByIds(ledgerAccountIdsList)
            .stream()
            .filter(ledgerAccount -> ledgerAccountIds.contains(ledgerAccount.getLedgerAccountId()))
            .filter(ledgerAccount -> !SUPPORTED_CHARGE_LEDGER_ACCOUNT_MAPPINGS.contains(ledgerAccount.getAccountType()))
            .findAny()
            .ifPresent(ledgerAccount -> {
                throw new InvalidInputException(
                    String.format(
                        "Unsupported ledger account type %s found during charge mapping. Supported types are %s",
                        ledgerAccount.getAccountType(),
                        SUPPORTED_CHARGE_LEDGER_ACCOUNT_MAPPINGS
                    )
                );
            });

        //make sure charge exists
        Charge charge = productCatalogGetService.getChargeByChargeId(chargeId);
        ledgerAccountDAO.addLedgerAccountMappingsForCharge(charge.getChargeId(), ledgerAccountIds);
        return accountingGetService.getLedgerAccountsForCharge(chargeId);
    }

    public void deleteLedgerAccountMappingsForCharges(Configuration configuration, Collection<String> chargeIds) {
        ledgerAccountDAO.deleteLedgerAccountMappingsForCharges(configuration, chargeIds);
    }

    //NOTE: method is package private can be accessible only within services in accounting
    List<LedgerAccount> createDefaultLedgerAccountsInTransaction(Configuration configuration) {
        DSLContext dslContext = DSL.using(configuration);
        List<LedgerAccount> defaultAccountTemplates = accountingGetService.getDefaultLedgerAccountTemplates();
        defaultAccountTemplates.forEach(defaultLedgerAccount -> {
            if (ledgerAccountDAO.getLedgerAccountByCode(defaultLedgerAccount.getAccountCode()).isEmpty()) {
                upsertLedgerAccountInTransaction(dslContext, defaultLedgerAccount);
            }
        });
        return accountingGetService.getDefaultLedgerAccounts();
    }

    public LedgerAccount upsertLedgerAccount(LedgerAccount ledgerAccount) {
        if (ledgerAccount.getAccountType() == LedgerAccountType.EXPENSE && !featureService.isEnabled(Feature.EXPENSE_LEDGER_ACCOUNT)) {
            throw new UnsupportedOperationException(String.format("Feature %s is not enabled", Feature.EXPENSE_LEDGER_ACCOUNT));
        }
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return dslContext.transactionResult(configuration -> {
            DSLContext txnContext = DSL.using(configuration);
            return upsertLedgerAccountInTransaction(txnContext, ledgerAccount);
        });
    }

    /**
     * This method is used for migration purposes only.
     * @param dslContext
     * @param ledgerAccount
     * @return {@link LedgerAccount}
     */
    private LedgerAccount upsertLedgerAccountForMigrationInTransaction(DSLContext dslContext, LedgerAccount ledgerAccount) {
        if (ledgerAccount.getAccountType() == LedgerAccountType.EXPENSE && !featureService.isEnabled(Feature.EXPENSE_LEDGER_ACCOUNT)) {
            throw new UnsupportedOperationException(String.format("Feature %s is not enabled", Feature.EXPENSE_LEDGER_ACCOUNT));
        }
        return upsertLedgerAccountInTransaction(dslContext, ledgerAccount);
    }

    public LedgerAccount createDefaultExpenseLedgerAccount(DSLContext dslContext) {
        var expenseAccountType = LedgerAccountType.EXPENSE;
        LedgerAccount newDefaultExpenseLedgerAccount = new LedgerAccount(
            null,
            "Default Expense Ledger Account",
            expenseAccountType.getDefaultAccountCode(),
            "Default expense ledger for existing payments with 0 bank fees",
            expenseAccountType,
            true
        );
        return upsertLedgerAccountForMigrationInTransaction(dslContext, newDefaultExpenseLedgerAccount);
    }

    private LedgerAccount upsertLedgerAccountInTransaction(DSLContext dslContext, LedgerAccount ledgerAccount) {
        ledgerAccount.sanitize();
        ledgerAccount.validate();

        if (StringUtils.isBlank(ledgerAccount.getLedgerAccountId())) {
            ledgerAccountDAO.checkForUniqueLedgerAccount(ledgerAccount);
            ledgerAccount.setLedgerAccountId(ledgerAccountIdGenerator.generate());

            // Set entity id
            ledgerAccount.setEntityIds(Set.of(EntityContext.ALL_ACCESS_ID));

            return ledgerAccountDAO.createLedgerAccount(dslContext, ledgerAccount);
        }

        Optional<LedgerAccount> storedLedgerAccountOptional = ledgerAccountDAO.getLedgerAccountByLedgerAccountId(ledgerAccount.getLedgerAccountId());
        if (storedLedgerAccountOptional.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.LEDGER_ACCOUNT, ledgerAccount.getLedgerAccountId());
        }

        LedgerAccount stored = storedLedgerAccountOptional.get();

        if (stored.isDefault() && !ledgerAccount.isDefault()) {
            throw new IllegalStateException("Cannot update default account to be non-default");
        }

        if (!stored.isDefault() && ledgerAccount.isDefault()) {
            throw new IllegalStateException("Cannot update non-default account to be default");
        }

        List<String> ledgerAccountIdsInUseForPaymentBankAccounts = paymentBankAccountGetService.getLedgerAccountIdsInUseForPaymentBankAccounts(
            List.of(ledgerAccount.getLedgerAccountId())
        );
        boolean ledgerAccountInUse =
            ledgerAccountDAO.isLedgerAccountIdInUse(ledgerAccount.getLedgerAccountId()) ||
            ledgerAccountIdsInUseForPaymentBankAccounts.contains(ledgerAccount.getLedgerAccountId());
        if (stored.getAccountType() != ledgerAccount.getAccountType() && ledgerAccountInUse) {
            throw new IllegalStateException(
                String.format("Ledger account: %s is used in charge mappings so account type cannot be modified", ledgerAccount.getLedgerAccountId())
            );
        }

        boolean ledgerAccountModified = !stored.getAccountCode().equals(ledgerAccount.getAccountCode());

        if (ledgerAccountModified && ledgerAccountInUse) {
            String message = String.format(
                "Ledger account: %s is used in charge mappings so account code cannot be modified from %s to %s",
                ledgerAccount.getLedgerAccountId(),
                stored.getAccountCode(),
                ledgerAccount.getAccountCode()
            );
            throw new ConflictingStateException(message);
        }
        ledgerAccount.setId(storedLedgerAccountOptional.get().getId());

        // Explicitly set entity ids to ALL_ACCESS_ID as this object is being used for accepting FE payloads as well and it's null by default
        ledgerAccount.setEntityIds(Set.of(EntityContext.ALL_ACCESS_ID));
        return ledgerAccountDAO.updateLedgerAccount(dslContext, ledgerAccount);
    }

    public LedgerAccount deleteLedgerAccount(String ledgerAccountId) {
        Validator.validateStringNotBlank(ledgerAccountId, "ledgerAccountId is required");

        if (ledgerAccountDAO.isLedgerAccountIdInUse(ledgerAccountId)) {
            LOGGER.info("Unable to delete ledger account due to linked recognition rules");
            throw new IllegalStateException("Must remove references to ledger account before deletion");
        }

        List<String> ledgerAccountIdsInUseForPaymentBankAccounts = paymentBankAccountGetService.getLedgerAccountIdsInUseForPaymentBankAccounts(
            List.of(ledgerAccountId)
        );
        if (ledgerAccountIdsInUseForPaymentBankAccounts.contains(ledgerAccountId)) {
            LOGGER.info("Unable to delete ledger account due to linked payment bank accounts");
            throw new IllegalStateException("Must remove payment bank account references to ledger account before deletion");
        }

        LedgerAccount existingLedgerAccount = ledgerAccountDAO
            .getLedgerAccountByLedgerAccountId(ledgerAccountId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.LEDGER_ACCOUNT, ledgerAccountId));

        if (existingLedgerAccount.isDefault()) {
            throw new IllegalStateException("Default ledger accounts cannot be deleted, only updated.");
        }

        return ledgerAccountDAO.deleteLedgerAccount(existingLedgerAccount.getLedgerAccountId());
    }

    public void persistJournalEntry(JournalEntry journalEntry, Long sourceEventSequenceNumber) {
        Validator.checkNonNullInternal(journalEntry, "journal entry cannot be null here");
        journalEntryDAO.persistJournalEntry(journalEntry, List.of(), List.of(), sourceEventSequenceNumber);
    }

    public void persistJournalEntry(JournalEntry journalEntry, Collection<RunningBalance> balances, Long sourceEventSequenceNumber) {
        Validator.checkNonNullInternal(journalEntry, "journal entry cannot be null here");
        journalEntryDAO.persistJournalEntry(journalEntry, balances, List.of(), sourceEventSequenceNumber);
    }

    public void persistJournalEntry(
        JournalEntry journalEntry,
        Collection<RunningBalance> balances,
        Collection<AccountingFxBalance> fxBalances,
        Long sourceEventSequenceNumber
    ) {
        Validator.checkNonNullInternal(journalEntry, "journal entry cannot be null here");
        journalEntryDAO.persistJournalEntry(journalEntry, balances, fxBalances, sourceEventSequenceNumber);
    }

    public void writeAccountingEventToLog(AccountingEvent accountingEvent) {
        journalEntryDAO.writeAccountingEventToLog(accountingEvent);
    }

    public void deleteAccountingEventLog(
        Configuration configuration,
        JournalEntry.TransactionType sourceTransactionType,
        String sourceTransactionId
    ) {
        Validator.validateNonNullArgument(sourceTransactionType, "sourceTransactionType");
        Validator.validateStringNotBlank(sourceTransactionId, "sourceTransactionId");

        journalEntryDAO.deleteAccountingEventLog(configuration, sourceTransactionType, sourceTransactionId);
    }

    public void purgeAllJournalEntriesAndAccountingData(Configuration configuration) {
        DSLContext dslContext = DSL.using(configuration);
        String entityId = entityContextProvider.provideSelected();
        journalEntryDAO.purgeAllJournalEntryLineItemsForEntity(dslContext, entityId);
        journalEntryDAO.purgeAllJournalEntriesForEntity(dslContext, entityId);
        journalEntryDAO.purgeAllAccountingRunningBalancesForEntity(dslContext, entityId);
        journalEntryDAO.purgeAllAccountingEventLogsForEntity(dslContext, entityId);
    }
}
