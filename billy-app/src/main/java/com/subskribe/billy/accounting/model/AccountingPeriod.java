package com.subskribe.billy.accounting.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.shared.enums.ExternalSyncStatus;
import com.subskribe.billy.shared.traits.Sanitized;
import com.subskribe.billy.shared.traits.ValidatedModel;
import com.subskribe.billy.validation.Validator;
import graphql.annotations.annotationTypes.GraphQLConstructor;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

public class AccountingPeriod implements Sanitized, ValidatedModel, NotifyingEvent {

    @JsonIgnore
    private UUID id;

    @GraphQLField
    @GraphQLName("id")
    @JsonProperty("id")
    private String accountingPeriodId;

    @GraphQLField
    @GraphQLName("entityId")
    @JsonProperty("entityId")
    private String entityId;

    @GraphQLField
    @GraphQLNonNull
    @JsonProperty
    private Instant startDate;

    @GraphQLField
    @GraphQLNonNull
    @JsonProperty
    private Instant endDate;

    @GraphQLField
    @JsonProperty
    private BigDecimal deferredRevenueBalance;

    @GraphQLField
    @GraphQLNonNull
    @JsonProperty
    private AccountingPeriodStatus status;

    @GraphQLField
    @GraphQLNonNull
    @JsonProperty
    private ExternalSyncStatus syncStatus;

    @GraphQLField
    @JsonProperty
    private String openedBy;

    @GraphQLField
    @JsonProperty
    private Instant openedOn;

    @GraphQLField
    @JsonProperty
    private String closedBy;

    @GraphQLField
    @JsonProperty
    private Instant closedOn;

    @GraphQLField
    @JsonProperty
    private AccountingPeriodCalculation calculation;

    @GraphQLField
    @JsonProperty
    private UserJson openedByUser;

    @GraphQLField
    @JsonProperty
    private UserJson closedByUser;

    // not persisted used only for in memory data model
    private boolean synthetic;

    public AccountingPeriod() {}

    @GraphQLConstructor
    public AccountingPeriod(
        @GraphQLName("id") String accountingPeriodId,
        @GraphQLName("entityId") String entityId,
        @GraphQLName("startDate") Long startDate,
        @GraphQLName("endDate") Long endDate,
        @GraphQLName("status") AccountingPeriodStatus status
    ) {
        this.accountingPeriodId = accountingPeriodId;
        this.entityId = entityId;
        this.startDate = Instant.ofEpochSecond(startDate);
        this.endDate = Instant.ofEpochSecond(endDate);
        this.status = status;
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getAccountingPeriodId() {
        return accountingPeriodId;
    }

    public void setAccountingPeriodId(String accountingPeriodId) {
        this.accountingPeriodId = accountingPeriodId;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public Instant getStartDate() {
        return startDate;
    }

    public void setStartDate(Instant startDate) {
        this.startDate = startDate;
    }

    public Instant getEndDate() {
        return endDate;
    }

    public void setEndDate(Instant endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getDeferredRevenueBalance() {
        return deferredRevenueBalance;
    }

    public void setDeferredRevenueBalance(BigDecimal deferredRevenueBalance) {
        this.deferredRevenueBalance = deferredRevenueBalance;
    }

    public AccountingPeriodStatus getStatus() {
        return status;
    }

    public void setStatus(AccountingPeriodStatus status) {
        this.status = status;
    }

    public ExternalSyncStatus getSyncStatus() {
        return Optional.ofNullable(syncStatus).orElse(ExternalSyncStatus.NONE);
    }

    public void setSyncStatus(ExternalSyncStatus syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getOpenedBy() {
        return openedBy;
    }

    public void setOpenedBy(String openedBy) {
        this.openedBy = openedBy;
    }

    public Instant getOpenedOn() {
        return openedOn;
    }

    public void setOpenedOn(Instant openedOn) {
        this.openedOn = openedOn;
    }

    public String getClosedBy() {
        return closedBy;
    }

    public void setClosedBy(String closedBy) {
        this.closedBy = closedBy;
    }

    public Instant getClosedOn() {
        return closedOn;
    }

    public void setClosedOn(Instant closedOn) {
        this.closedOn = closedOn;
    }

    public AccountingPeriodCalculation getCalculation() {
        return calculation;
    }

    public void setCalculation(AccountingPeriodCalculation calculation) {
        this.calculation = calculation;
    }

    public UserJson getOpenedByUser() {
        return openedByUser;
    }

    public void setOpenedByUser(UserJson openedByUser) {
        this.openedByUser = openedByUser;
    }

    public UserJson getClosedByUser() {
        return closedByUser;
    }

    public void setClosedByUser(UserJson closedByUser) {
        this.closedByUser = closedByUser;
    }

    public boolean isSynthetic() {
        return synthetic;
    }

    public void setSynthetic(boolean synthetic) {
        this.synthetic = synthetic;
    }

    @Override
    @JsonIgnore
    public String getEventObjectId() {
        return getAccountingPeriodId();
    }

    @Override
    public void sanitize() {
        accountingPeriodId = safeTrim(accountingPeriodId);
    }

    @Override
    public void validate() {
        ValidatedModel.super.validate();

        Validator.validateNonNullArgument(startDate, "startDate");
        Validator.validateNonNullArgument(endDate, "endDate");
        Validator.validateNonNullArgument(status, "status");

        if (!startDate.isBefore(endDate)) {
            throw new IllegalArgumentException("startDate must be before endDate");
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AccountingPeriod that = (AccountingPeriod) o;
        return (
            id.equals(that.id) &&
            accountingPeriodId.equals(that.accountingPeriodId) &&
            Objects.equals(entityId, that.entityId) &&
            startDate.equals(that.startDate) &&
            endDate.equals(that.endDate) &&
            status == that.status
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, accountingPeriodId, entityId, startDate, endDate, status);
    }

    @Override
    public String toString() {
        return (
            "AccountingPeriod{" +
            "id=" +
            id +
            ", accountingPeriodId='" +
            accountingPeriodId +
            '\'' +
            ", entityId='" +
            entityId +
            '\'' +
            ", startDate=" +
            startDate +
            ", endDate=" +
            endDate +
            ", deferredRevenueBalance=" +
            deferredRevenueBalance +
            ", status=" +
            status +
            ", syncStatus=" +
            syncStatus +
            ", openedBy='" +
            openedBy +
            '\'' +
            ", openedOn=" +
            openedOn +
            ", closedBy='" +
            closedBy +
            '\'' +
            ", closedOn=" +
            closedOn +
            ", calculation=" +
            calculation +
            ", openedByUser=" +
            openedByUser +
            ", closedByUser=" +
            closedByUser +
            ", synthetic=" +
            synthetic +
            '}'
        );
    }
}
