package com.subskribe.billy.accounting.services;

import com.subskribe.billy.accounting.db.JournalEntryDAO;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.AccountingPeriodStatus;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.erp.service.ErpSyncQueueService;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.StreamPartitionKey;
import com.subskribe.billy.event.model.TenantEntityPartitionKey;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.revrec.model.RecognitionTransaction;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobStatus;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import com.subskribe.billy.tenantjob.service.TenantJobGetService;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

public class AccountingPeriodStatusUpdateService {

    private final AccountingPeriodService accountingPeriodService;
    private final AccountingPeriodCalculationService accountingPeriodCalculationService;
    private final RevenueRecognitionGetService revenueRecognitionService;
    private final TenantSettingService tenantSettingService;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final PlatformFeatureService platformFeatureService;
    private final ErpSyncQueueService erpSyncQueueService;
    private final JournalEntryDAO journalEntryDAO;
    private final TenantJobGetService tenantJobGetService;
    private final EventPublishingService eventPublishingService;
    private final UncheckedObjectMapper objectMapper;

    @Inject
    public AccountingPeriodStatusUpdateService(
        AccountingPeriodService accountingPeriodService,
        AccountingPeriodCalculationService accountingPeriodCalculationService,
        RevenueRecognitionGetService revenueRecognitionService,
        TenantSettingService tenantSettingService,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider,
        PlatformFeatureService platformFeatureService,
        ErpSyncQueueService erpSyncQueueService,
        JournalEntryDAO journalEntryDAO,
        TenantJobGetService tenantJobGetService,
        EventPublishingService eventPublishingService
    ) {
        this.accountingPeriodService = accountingPeriodService;
        this.accountingPeriodCalculationService = accountingPeriodCalculationService;
        this.revenueRecognitionService = revenueRecognitionService;
        this.tenantSettingService = tenantSettingService;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.platformFeatureService = platformFeatureService;
        this.erpSyncQueueService = erpSyncQueueService;
        this.journalEntryDAO = journalEntryDAO;
        this.tenantJobGetService = tenantJobGetService;
        this.eventPublishingService = eventPublishingService;
        this.objectMapper = UncheckedObjectMapper.defaultMapper();
    }

    public AccountingPeriod moveAccountingPeriodToCloseInProgress(String accountingPeriodId) {
        // if accounting platform feature is not enabled the CLOSE_IN_PROGRESS state transition is not valid
        if (!isAccountingEnabled()) {
            throw new ConflictingStateException(
                String.format(
                    "Accounting is not enabled, accounting period %s cannot move to %s",
                    accountingPeriodId,
                    AccountingPeriodStatus.CLOSE_IN_PROGRESS
                )
            );
        }

        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(accountingPeriodId);
        if (AccountingPeriodStatus.OPEN != accountingPeriod.getStatus()) {
            throw new ConflictingStateException(
                String.format(
                    "accounting period %s not the required %s state, cannot move accounting period to %s",
                    accountingPeriodId,
                    AccountingPeriodStatus.OPEN,
                    AccountingPeriodStatus.CLOSE_IN_PROGRESS
                )
            );
        }
        // previous accounting period should be CLOSED
        Optional<AccountingPeriod> prevAccountingPeriodOptional = accountingPeriodService.getPreviousAccountingPeriod(accountingPeriod);
        boolean isPreviousAccountingPeriodClosed = prevAccountingPeriodOptional
            .map(prevPeriod -> AccountingPeriodStatus.CLOSED == prevPeriod.getStatus())
            .orElse(true);
        if (!isPreviousAccountingPeriodClosed) {
            throw new ConflictingStateException(
                String.format(
                    "Accounting period prior to accounting period:%s is not in %s state, cannot transition to %s",
                    accountingPeriodId,
                    AccountingPeriodStatus.CLOSED,
                    AccountingPeriodStatus.CLOSE_IN_PROGRESS
                )
            );
        }
        performAccountingPeriodCloseWorkflowChecks(accountingPeriod);
        performAccountingPeriodCloseTransition(accountingPeriod, AccountingPeriodStatus.CLOSE_IN_PROGRESS);
        return accountingPeriodService.getAccountingPeriod(accountingPeriodId);
    }

    public AccountingPeriod abandonAccountingPeriodClose(String accountingPeriodId) {
        Validator.validateStringNotBlank(accountingPeriodId, "Accounting period Id cannot be blank");
        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(accountingPeriodId);
        if (accountingPeriod.getStatus() == AccountingPeriodStatus.OPEN) {
            // accounting period already open nothing to do
            return accountingPeriod;
        }
        abandonAccountingPeriodClose(accountingPeriod);
        return accountingPeriodService.getAccountingPeriod(accountingPeriodId);
    }

    private void abandonAccountingPeriodClose(AccountingPeriod accountingPeriod) {
        if (accountingPeriod.getStatus() != AccountingPeriodStatus.CLOSE_IN_PROGRESS) {
            throw new ConflictingStateException(
                String.format(
                    "Accounting period %s is not in required state %s for abandoning close in progress",
                    accountingPeriod.getAccountingPeriodId(),
                    AccountingPeriodStatus.CLOSE_IN_PROGRESS
                )
            );
        }
        AccountingPeriod nextAccountingPeriod = accountingPeriodService.getAccountingPeriodByInstant(accountingPeriod.getEndDate());
        if (nextAccountingPeriod.getStatus() != AccountingPeriodStatus.OPEN) {
            throw new ServiceFailureException(
                String.format(
                    "next accounting period:%s to accounting period:%s not in required state: %s for abandoning close in progress",
                    nextAccountingPeriod.getAccountingPeriodId(),
                    accountingPeriod.getAccountingPeriodId(),
                    AccountingPeriodStatus.OPEN
                )
            );
        }
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext.transaction(configuration -> {
            DSLContext transactionContext = DSL.using(configuration);
            accountingPeriodService.updateAccountingPeriodStatus(
                transactionContext,
                nextAccountingPeriod,
                AccountingPeriodStatus.OPEN,
                AccountingPeriodStatus.UPCOMING
            );
            accountingPeriodService.updateAccountingPeriodStatus(
                transactionContext,
                accountingPeriod,
                AccountingPeriodStatus.CLOSE_IN_PROGRESS,
                AccountingPeriodStatus.OPEN
            );
        });
    }

    public AccountingPeriod reopenAccountingPeriod(String accountingPeriodId) {
        Validator.validateStringNotBlank(accountingPeriodId, "Accounting period Id cannot be blank");
        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(accountingPeriodId);
        if (accountingPeriod.getStatus() == AccountingPeriodStatus.OPEN) {
            // accounting period already open nothing to do
            return accountingPeriod;
        }
        reopenAccountingPeriod(accountingPeriod);
        return accountingPeriodService.getAccountingPeriod(accountingPeriodId);
    }

    public AccountingPeriod closeAccountingPeriod(String accountingPeriodId) {
        Validator.validateStringNotBlank(accountingPeriodId, "Accounting period Id cannot be blank");
        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(accountingPeriodId);

        if (isAccountingEnabled()) {
            closeWhenAccountingEnabled(accountingPeriod);
        } else {
            // TODO: after ASC605 support is removed we can get rid of all of this modality
            closeWhenAccountingDisabled(accountingPeriod);
        }
        return accountingPeriodService.getAccountingPeriod(accountingPeriodId);
    }

    private void reopenAccountingPeriod(AccountingPeriod accountingPeriod) {
        if (accountingPeriod.getStatus() != AccountingPeriodStatus.CLOSED) {
            throw new ConflictingStateException(
                String.format(
                    "Accounting period: %s is not required state: %s for re-open",
                    accountingPeriod.getAccountingPeriodId(),
                    AccountingPeriodStatus.CLOSED
                )
            );
        }

        checkAccountingPeriodCanBeOpened(accountingPeriod);
        erpSyncQueueService.checkNoErpSyncTaskInProgress();

        AccountingPeriod currentAccountingPeriod = accountingPeriodService.getCurrentAccountingPeriod(accountingPeriod.getEntityId()).orElseThrow();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext.transaction(configuration -> {
            DSLContext transactionContext = DSL.using(configuration);
            accountingPeriodService.updateAccountingPeriodStatus(
                transactionContext,
                currentAccountingPeriod,
                AccountingPeriodStatus.OPEN,
                AccountingPeriodStatus.UPCOMING
            );
            AccountingPeriod reopenedPeriod = accountingPeriodService.updateAccountingPeriodStatus(
                transactionContext,
                accountingPeriod,
                AccountingPeriodStatus.CLOSED,
                AccountingPeriodStatus.OPEN
            );
            publishAccountingPeriodEvent(reopenedPeriod, EventType.ACCOUNTING_PERIOD_REOPENED, configuration);
        });
        if (isAccountingEnabled()) {
            erpSyncQueueService.dispatchDeletionJob(accountingPeriod.getAccountingPeriodId());
        }
    }

    private void closeWhenAccountingEnabled(AccountingPeriod accountingPeriod) {
        if (accountingPeriod.getStatus() != AccountingPeriodStatus.CLOSE_IN_PROGRESS) {
            throw new ConflictingStateException(
                String.format(
                    "Accounting period %s not in required state %s for closing, please initiate accounting period closing",
                    accountingPeriod.getAccountingPeriodId(),
                    AccountingPeriodStatus.CLOSE_IN_PROGRESS
                )
            );
        }

        // there should be at lease one successful JE creation for this accounting period before it can be closed
        Optional<TenantJob> latestJobOptional = tenantJobGetService.getLatestJob(
            TenantJobType.CREATE_JOURNAL_ENTRIES,
            accountingPeriod.getAccountingPeriodId()
        );

        TenantJob latestJob = latestJobOptional.orElseThrow(() ->
            new ConflictingStateException(
                String.format(
                    "Journal Entries for accounting period: %s may not have been created, please create them before closing",
                    accountingPeriod.getAccountingPeriodId()
                )
            )
        );

        if (latestJob.getStatus() != TenantJobStatus.SUCCESSFUL) {
            throw new ConflictingStateException(
                String.format(
                    "Journal entry creation In Progress or Failed for accounting period: %s with status: %s, Accounting period can only be closed on successful journal entry creation",
                    accountingPeriod.getAccountingPeriodId(),
                    latestJob.getStatus()
                )
            );
        }

        if (journalEntryDAO.areAccountingEventsPendingJournalEntryCreation(accountingPeriod)) {
            throw new ConflictingStateException(
                String.format(
                    "There are accounting events yet to be converted to Journal Entries in accounting period: %s",
                    accountingPeriod.getAccountingPeriodId()
                )
            );
        }
        erpSyncQueueService.checkNoErpSyncTaskInProgress();

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        setDeferredRevenueBalance(accountingPeriod);
        dslContext.transaction(configuration -> {
            DSLContext transactionContext = DSL.using(configuration);
            AccountingPeriod closedAccountingPeriod = accountingPeriodService.updateAccountingPeriodStatus(
                transactionContext,
                accountingPeriod,
                AccountingPeriodStatus.CLOSE_IN_PROGRESS,
                AccountingPeriodStatus.CLOSED
            );
            publishAccountingPeriodEvent(closedAccountingPeriod, EventType.ACCOUNTING_PERIOD_CLOSED, configuration);
        });
        if (isAccountingEnabled()) {
            erpSyncQueueService.dispatchCreationJob(accountingPeriod.getAccountingPeriodId());
        }
    }

    private void closeWhenAccountingDisabled(AccountingPeriod accountingPeriod) {
        performAccountingPeriodCloseWorkflowChecks(accountingPeriod);
        setDeferredRevenueBalance(accountingPeriod);
        performAccountingPeriodCloseTransition(accountingPeriod, AccountingPeriodStatus.CLOSED);
    }

    private void performAccountingPeriodCloseTransition(AccountingPeriod accountingPeriod, AccountingPeriodStatus closeStatus) {
        Validator.checkStateInternal(
            closeStatus == AccountingPeriodStatus.CLOSED || closeStatus == AccountingPeriodStatus.CLOSE_IN_PROGRESS,
            String.format("Unknown close state: %s for close transition", closeStatus)
        );
        ZoneId zoneId = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        Instant nextStartDate = DateTimeCalculator.plusMonths(zoneId, accountingPeriod.getStartDate(), 1);
        Instant nextEndDate = DateTimeCalculator.plusMonths(zoneId, nextStartDate, 1);
        List<AccountingPeriod> accountingPeriods = accountingPeriodService.generateAccountingPeriods(Period.between(nextStartDate, nextEndDate));
        AccountingPeriod nextAccountingPeriod = accountingPeriods.stream().findFirst().orElseThrow();

        // get the previous accounting period
        Optional<AccountingPeriod> prevAccountingOptional = accountingPeriodService.getPreviousAccountingPeriod(accountingPeriod);

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext.transaction(configuration -> {
            DSLContext transactionContext = DSL.using(configuration);

            // no matter what close state we move to CLOSE or CLOSE_IN_PROGRESS
            // we expected the previous accounting period to be CLOSED, if not we fail the transaction
            prevAccountingOptional.ifPresent(prevAccountingPeriod ->
                accountingPeriodService.touchAccountingPeriodExpectingStatus(dslContext, prevAccountingPeriod, AccountingPeriodStatus.CLOSED)
            );

            accountingPeriodService.updateAccountingPeriodStatus(
                transactionContext,
                nextAccountingPeriod,
                AccountingPeriodStatus.UPCOMING,
                AccountingPeriodStatus.OPEN
            );
            AccountingPeriod closedAccountingPeriod = accountingPeriodService.updateAccountingPeriodStatus(
                transactionContext,
                accountingPeriod,
                AccountingPeriodStatus.OPEN,
                closeStatus
            );
            if (closeStatus == AccountingPeriodStatus.CLOSED) {
                publishAccountingPeriodEvent(closedAccountingPeriod, EventType.ACCOUNTING_PERIOD_CLOSED, configuration);
            }
        });
    }

    private void checkAccountingPeriodCanBeOpened(AccountingPeriod accountingPeriod) {
        AccountingPeriod lastClosedPeriod = accountingPeriodService
            .getLastClosed()
            .orElseThrow(() -> new IllegalStateException("previously closed accounting period not found"));
        if (!accountingPeriod.getAccountingPeriodId().equals(lastClosedPeriod.getAccountingPeriodId())) {
            throw new ConflictingStateException("Could not open specified accounting period, please open accounting periods in order");
        }
    }

    public void performAccountingPeriodCloseWorkflowChecks(AccountingPeriod accountingPeriod) {
        AccountingPeriod currentAccountingPeriod = accountingPeriodService
            .getCurrentAccountingPeriod(accountingPeriod.getEntityId())
            .orElseThrow(() -> new IllegalStateException("current accounting period not found"));
        if (!accountingPeriod.getAccountingPeriodId().equals(currentAccountingPeriod.getAccountingPeriodId())) {
            throw new ConflictingStateException("Could not close specified accounting period, please close accounting periods in order");
        }

        // Ensure that there are no pending events for this accounting period
        if (revenueRecognitionService.doesAccountingPeriodHaveAcceptedEvents(accountingPeriod.getAccountingPeriodId())) {
            String message = String.format(
                "There are unprocessed revenue recognition events for the accounting period: %s, please try closing the accounting period after a while",
                accountingPeriod.getAccountingPeriodId()
            );
            throw new ConflictingStateException(message);
        }

        // Ensure there is no unrecognized revenue in given accounting period
        List<RecognitionTransaction> transactions = revenueRecognitionService.getRecognitionTransactionsByScheduledAccountingPeriod(
            accountingPeriod.getAccountingPeriodId()
        );
        transactions
            .stream()
            .filter(Predicate.not(RecognitionTransaction::getIsRecognized))
            .findAny()
            .ifPresent(unrecognizedTransaction -> {
                throw new ConflictingStateException(
                    "Could not close specified accounting period, there are unrecognized transactions in the current accounting period"
                );
            });
    }

    // todo - current calculation is brittle as it relies on the previous balance
    //  if there is any error in past, it will be carried forward forever.
    //  to address this, during close in progress, we should verify the DR balance by going through all
    //  relevant revenue transactions to arrive at the balance independently
    private void setDeferredRevenueBalance(AccountingPeriod accountingPeriod) {
        accountingPeriodCalculationService.populateAccountingPeriodCalculation(accountingPeriod);
        accountingPeriod.setDeferredRevenueBalance(accountingPeriod.getCalculation().getDeferredRevenueEndingBalance());
    }

    private boolean isAccountingEnabled() {
        return platformFeatureService.getFeatureEnablement(PlatformFeature.ACCOUNTING).isPresent();
    }

    private void publishAccountingPeriodEvent(AccountingPeriod accountingPeriod, EventType eventType, Configuration txConfiguration) {
        StreamPartitionKey partitionKey = TenantEntityPartitionKey.builder()
            .withTenantId(tenantIdProvider.provideTenantIdString())
            .withEntityId(accountingPeriod.getEntityId())
            .build();
        eventPublishingService.publishEventInTransaction(
            txConfiguration,
            eventType,
            tenantIdProvider.provideTenantIdString(),
            Set.of(accountingPeriod.getEntityId()),
            partitionKey,
            objectMapper.writeValueAsBytes(accountingPeriod)
        );
    }
}
