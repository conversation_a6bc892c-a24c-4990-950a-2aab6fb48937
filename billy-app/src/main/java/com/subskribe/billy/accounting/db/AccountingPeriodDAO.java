package com.subskribe.billy.accounting.db;

import static com.subskribe.billy.jooq.default_schema.tables.AccountingPeriod.ACCOUNTING_PERIOD;

import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.AccountingPeriodStatus;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountingPeriodRecord;
import com.subskribe.billy.shared.enums.ExternalSyncStatus;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class AccountingPeriodDAO {

    private final TenantIdProvider tenantIdProvider;
    private final EntityContextProvider entityContextProvider;
    private final FeatureService featureService;
    private final DSLContextProvider dslContextProvider;
    private final AccountingPeriodRecordMapper recordMapper;

    @Inject
    public AccountingPeriodDAO(
        TenantIdProvider tenantIdProvider,
        EntityContextProvider entityContextProvider,
        FeatureService featureService,
        DSLContextProvider dslContextProvider
    ) {
        this.tenantIdProvider = tenantIdProvider;
        this.entityContextProvider = entityContextProvider;
        this.featureService = featureService;
        this.dslContextProvider = dslContextProvider;
        recordMapper = Mappers.getMapper(AccountingPeriodRecordMapper.class);
    }

    public void batchInsertAccountingPeriods(DSLContext dslContext, List<AccountingPeriod> accountingPeriods) {
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        // if any of the accounting periods are synthetic throw
        if (accountingPeriods.stream().anyMatch(AccountingPeriod::isSynthetic)) {
            throw new ServiceFailureException("synthetic accounting periods cannot be persisted");
        }

        String tenantId = tenantIdProvider.provideTenantIdString();
        List<AccountingPeriodRecord> records = recordMapper.toRecords(accountingPeriods);
        records.forEach(record -> {
            record.setTenantId(tenantId);
            record.setEntityId(entityId);
            record.reset(ACCOUNTING_PERIOD.ID);
        });

        dslContext.batchInsert(records).execute();
    }

    public void touchAccountingPeriodExpectingStatus(
        DSLContext dslContext,
        AccountingPeriod accountingPeriod,
        AccountingPeriodStatus expectedStatus
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        String accountingPeriodId = accountingPeriod.getAccountingPeriodId();

        int updatedCount = dslContext
            .update(ACCOUNTING_PERIOD)
            .set(ACCOUNTING_PERIOD.STATUS, ACCOUNTING_PERIOD.STATUS)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ACCOUNTING_PERIOD_ID.eq(accountingPeriodId))
            .and(ACCOUNTING_PERIOD.STATUS.eq(expectedStatus.name()))
            .execute();
        if (updatedCount != 1) {
            throw new IllegalStateException(String.format("Accounting period: %s not in expected status while updating", expectedStatus));
        }
    }

    public AccountingPeriod updateAccountingPeriodStatus(
        DSLContext dslContext,
        AccountingPeriod accountingPeriod,
        AccountingPeriodStatus statusFrom,
        AccountingPeriodStatus statusTo
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        String accountingPeriodId = accountingPeriod.getAccountingPeriodId();

        var query = dslContext.update(ACCOUNTING_PERIOD).set(ACCOUNTING_PERIOD.STATUS, statusTo.name());

        switch (statusTo) {
            case OPEN -> {
                query = query
                    .set(ACCOUNTING_PERIOD.OPENED_BY, accountingPeriod.getOpenedBy())
                    .set(ACCOUNTING_PERIOD.OPENED_ON, DateTimeConverter.instantToLocalDateTime(accountingPeriod.getOpenedOn()));
                if (statusFrom == AccountingPeriodStatus.CLOSED) {
                    query = query.set(ACCOUNTING_PERIOD.SYNC_STATUS, ExternalSyncStatus.WAITING.name());
                }
            }
            case CLOSED -> query = query
                .set(ACCOUNTING_PERIOD.SYNC_STATUS, ExternalSyncStatus.WAITING.name())
                .set(ACCOUNTING_PERIOD.DEFERRED_REVENUE_BALANCE, accountingPeriod.getDeferredRevenueBalance())
                .set(ACCOUNTING_PERIOD.CLOSED_BY, accountingPeriod.getClosedBy())
                .set(ACCOUNTING_PERIOD.CLOSED_ON, DateTimeConverter.instantToLocalDateTime(accountingPeriod.getClosedOn()));
            default -> {}
        }
        AccountingPeriodRecord updatedRecord = query
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ACCOUNTING_PERIOD_ID.eq(accountingPeriodId))
            .and(ACCOUNTING_PERIOD.STATUS.eq(statusFrom.name()))
            .returning()
            .fetchOne();
        if (updatedRecord == null) {
            throw new ConflictingStateException("could not update accounting period since another thread is modifying it");
        }
        return recordMapper.fromRecord(updatedRecord);
    }

    public void updateAccountingPeriodSyncStatus(AccountingPeriod accountingPeriod, ExternalSyncStatus statusFrom, ExternalSyncStatus statusTo) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        String accountingPeriodId = accountingPeriod.getAccountingPeriodId();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        int count = dslContext
            .update(ACCOUNTING_PERIOD)
            .set(ACCOUNTING_PERIOD.SYNC_STATUS, statusTo.name())
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ACCOUNTING_PERIOD_ID.eq(accountingPeriodId))
            .and(ACCOUNTING_PERIOD.SYNC_STATUS.eq(statusFrom.name()))
            .and(ACCOUNTING_PERIOD.STATUS.eq(accountingPeriod.getStatus().name()))
            .execute();
        if (count < 1) {
            throw new ConflictingStateException(
                String.format(
                    "Accounting Period was not in expected state %s while transitioning to %s or was not in state %s",
                    statusFrom,
                    statusTo,
                    accountingPeriod.getStatus()
                )
            );
        }
    }

    public List<AccountingPeriod> getAccountingPeriods(String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var accountingPeriodRecords = dslContext
            .select()
            .from(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ENTITY_ID.eq(entityId))
            .orderBy(ACCOUNTING_PERIOD.START_DATE.asc())
            .fetchInto(AccountingPeriodRecord.class);
        return recordMapper.fromRecords(accountingPeriodRecords);
    }

    public Optional<AccountingPeriod> getAccountingPeriodByInstant(Instant instant) {
        ensureSingleEntityContext();
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        AccountingPeriodRecord record = dslContext
            .selectFrom(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.END_DATE.greaterThan(DateTimeConverter.instantToLocalDateTime(instant)))
            .and(ACCOUNTING_PERIOD.START_DATE.lessOrEqual(DateTimeConverter.instantToLocalDateTime(instant)))
            .and(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .fetchOne();
        return Optional.ofNullable(recordMapper.fromRecord(record));
    }

    public List<AccountingPeriod> getAccountingPeriodsByPeriod(Period period) {
        ensureSingleEntityContext();
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var accountingPeriodRecords = dslContext
            .select()
            .from(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.END_DATE.greaterThan(DateTimeConverter.instantToLocalDateTime(period.getStart())))
            .and(ACCOUNTING_PERIOD.START_DATE.lessThan(DateTimeConverter.instantToLocalDateTime(period.getEnd())))
            .and(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .orderBy(ACCOUNTING_PERIOD.START_DATE.asc())
            .fetchInto(AccountingPeriodRecord.class);
        return recordMapper.fromRecords(accountingPeriodRecords);
    }

    public Optional<AccountingPeriod> getAccountingPeriodByAccountingPeriodId(String accountingPeriodId) {
        Validator.validateStringNotBlank(accountingPeriodId, "accounting period id cannot be blank");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        AccountingPeriodRecord record = dslContext
            .selectFrom(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ACCOUNTING_PERIOD_ID.eq(accountingPeriodId))
            .fetchOne();
        return Optional.ofNullable(recordMapper.fromRecord(record));
    }

    public List<AccountingPeriod> getAccountingPeriodsByAccountingPeriodIds(List<String> accountingPeriodIds) {
        Validator.validateCollectionNotEmpty(accountingPeriodIds, "accounting period ids");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var accountingPeriodRecords = dslContext
            .selectFrom(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ACCOUNTING_PERIOD_ID.in(accountingPeriodIds))
            .fetchInto(AccountingPeriodRecord.class);
        return recordMapper.fromRecords(accountingPeriodRecords);
    }

    public Optional<AccountingPeriod> getLast(String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        AccountingPeriodRecord record = dslContext
            .selectFrom(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ENTITY_ID.eq(entityId))
            .orderBy(ACCOUNTING_PERIOD.START_DATE.desc())
            .limit(1)
            .fetchOne();
        return Optional.ofNullable(recordMapper.fromRecord(record));
    }

    public Optional<AccountingPeriod> getLastClosed() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        AccountingPeriodRecord record = dslContext
            .selectFrom(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.STATUS.eq(AccountingPeriodStatus.CLOSED.name()))
            .orderBy(ACCOUNTING_PERIOD.END_DATE.desc())
            .limit(1)
            .fetchOne();
        return Optional.ofNullable(recordMapper.fromRecord(record));
    }

    public Optional<AccountingPeriod> getFirst(String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        AccountingPeriodRecord record = dslContext
            .selectFrom(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ENTITY_ID.eq(entityId))
            .orderBy(ACCOUNTING_PERIOD.START_DATE.asc())
            .limit(1)
            .fetchOne();
        return Optional.ofNullable(recordMapper.fromRecord(record));
    }

    public Optional<AccountingPeriod> getFirstOpen(String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        AccountingPeriodRecord record = dslContext
            .selectFrom(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ENTITY_ID.eq(entityId))
            .and(ACCOUNTING_PERIOD.STATUS.eq(AccountingPeriodStatus.OPEN.name()))
            .orderBy(ACCOUNTING_PERIOD.END_DATE.asc())
            .limit(1)
            .fetchOne();
        return Optional.ofNullable(recordMapper.fromRecord(record));
    }

    public Optional<AccountingPeriod> getPreviousAccountingPeriod(AccountingPeriod accountingPeriod) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        AccountingPeriodRecord record = dslContext
            .selectFrom(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ENTITY_ID.eq(accountingPeriod.getEntityId()))
            .and(ACCOUNTING_PERIOD.END_DATE.eq(DateTimeConverter.instantToLocalDateTime(accountingPeriod.getStartDate())))
            .fetchOne();
        return Optional.ofNullable(recordMapper.fromRecord(record));
    }

    private void ensureSingleEntityContext() {
        if (featureService.isEnabled(Feature.MULTI_ENTITY_ACCOUNTING)) {
            // this throws an exception if multiple entities are selected
            entityContextProvider.provideSingleEntityOrElseThrow();
        }
    }

    public void ensureUniqueId(String accountingPeriodId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ACCOUNTING_PERIOD_ID.eq(accountingPeriodId))
            .fetchOneInto(AccountingPeriodRecord.class);
        if (record == null) {
            return;
        }
        throwDuplicateIdException(accountingPeriodId);
    }

    private void throwDuplicateIdException(String id) {
        var message = "Duplicate accounting period id generated. AccountingPeriodId = " + id;
        throw new DuplicateIdException(message);
    }

    public void purgeAllAccountingPeriodsForEntity(DSLContext dslContext, String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .deleteFrom(ACCOUNTING_PERIOD)
            .where(ACCOUNTING_PERIOD.TENANT_ID.eq(tenantId))
            .and(ACCOUNTING_PERIOD.ENTITY_ID.eq(entityId))
            .execute();
    }
}
