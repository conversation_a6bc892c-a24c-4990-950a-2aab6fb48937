package com.subskribe.billy.configuration.dynamic;

import java.util.Arrays;
import java.util.Optional;

public enum Feature {
    ACCOUNTING_V1_0("accountingV1_0"),
    EMAIL_APPROVAL("emailApproval"),
    ALWAYS_ALLOW_EDITING_DISCOUNT_PERCENT("alwaysAllowEditingDiscountPercent"),
    VOID_INVOICE("voidInvoice"),
    PRODUCT_RULES_DEMO("productRulesDemo"),
    REV_REC_INVOICE("revRecInvoice"),
    UPSELL_EARLY_RENEWAL("upsellEarlyRenewal"),
    PRECOMMIT_OVERAGE("precommitOverage"),
    CREDIT_BUCKETS("creditBuckets"),
    CLM("clm"),
    EMAIL_LINK_LOGIN("emailLinkLogin"),
    GUIDED_SELLING("guidedSelling"),
    EXPIRE_CANCEL_DELTA_ARR("expireCancelDeltaArr"),
    DOCUMENT_LINK("documentLink"),
    MIN_MAX_PERCENT_OF_AMOUNT("minMaxPercentOfAmount"),
    BULK_PAYMENT_UPLOAD_CSV("bulkPaymentUploadCsv"),
    APPROVAL_ESCALATION("approvalEscalation"),
    ADD_NEGATIVE_ARR_FOR_CANCEL_ON_LAST_PLUS_ONE_DAY("addNegativeArrForCancelOnLastPlusOneDay"),
    ENABLE_PAYMENT_JOB("enablePaymentJob"),
    ADD_MISSED_RENEWAL_ORDER_LINES("addMissedRenewalOrderLines"),
    CHANGE_DELTA_ARR_FOR_RENEWALS("changeDeltaArrForRenewals"),
    MERGE_POC("mergePOC"),
    SALES_ROOM("salesRoom"),
    INTELLIGENT_SALES_ROOM("intelligentSalesRoom"),
    TAX_PERCENT("taxPercent"),
    QUICK_ACCESS("quickAccess"),
    ORDER_FORM_TEMPLATE_IDE("orderFormTemplateIde"),
    INVOICE_PREVIEW_REPORT_V2("invoicePreviewReportV2"),
    PLAN_ADDITION_CUSTOMIZATION("planAdditionCustomization"),
    FLEXIBLE_BILLING_ANCHOR_DATE("flexibleBillingAnchorDate"),
    DOCUSIGN_V2("docusignV2"),
    ORDER_LINE_ITEM_CUSTOM_FIELDS_CUSTOMIZATION("orderLineItemCustomFieldsCustomization"),
    CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE("cancelSingleSubscriptionAndRestructure"),
    SEPARATE_REMOVED_LINES_FROM_RESTRUCTURED_LINES("separateRemovedLinedFromRestructuredLines"),
    UI_CONFIGURATION("uiConfiguration"),
    UI_SINGLE_CLICK_APPLY_RAMP("uiSingleClickApplyRamp"),
    MULTI_ENTITY_AUTH("multiEntityAuth"),
    THOMSON_REUTERS_TAX("thomsonReutersTax"),
    MULTI_ENTITY_ACCOUNTING("multiEntityAccounting"),
    REVREC_AMOUNT_BASED_COMPLETION("revrecAmountBasedCompletion"),
    POPULATE_MISSING_ITEMS("populateMissingItems"),
    UI_LABORATORY("uiLaboratory"),
    NETSUITE_INVOICE_SYNC("netsuiteInvoiceSync"),
    MASTER_TEMPLATE_EDITOR_V2("masterTemplateEditorV2"),
    REBASE_AMENDMENT("rebaseAmendment"),
    FOREIGN_EXCHANGE("foreignExchange"),
    CUSTOM_FIELDS_SYNC_TO_CRM("customFieldsSyncToCrm"),
    FETCH_CLOSED_WON_OPPORTUNITY_FROM_CRM("fetchClosedWonOpportunityFromCrm"),
    FLATFILE_BULK_OPERATIONS("flatfileBulkOperations"),
    DOCUSIGN_SINGLE_SIGNER("docusignSingleSigner"),
    ALLOW_ZERO_DURATION_ORDER_ITEMS("allowZeroDurationOrderItems"),
    UPDATE_ORDER_START_DATE("updateOrderStartDate"),
    RESTRICT_PREDEFINED_TERM_UPDATE_ON_ORDER_TO_ADMIN("restrictPredefinedTermUpdateOnOrderToAdmin"),
    UI_LINE_ITEMS_EDIT_TABLE("uiLineItemsEditTable"),
    UI_LINE_ITEMS_EDIT_TABLE_DGP("uiLineItemsEditTableDgp"),
    UI_SAVE_DGP_TABLE_STATE("uiSaveDgpTableState"),
    ORDER_LINE_ARR_OVERRIDE("orderLineArrOverride"),
    CPQ_QTR_TRANSITION("cpqQtrTransition"),
    TRANSACTIONAL_FOREIGN_EXCHANGE("transactionalForeignExchange"),
    BACKFILL_RAMP_GROUP_ID_JOB("backfillRampGroupIdJob"),
    HANDLE_NEGATIVE_REV_REC_EVENT("handleNegativeRevRecEvent"),
    TENANT_UI_CUSTOMIZATION("tenantUiCustomization"),
    EXPENSE_LEDGER_ACCOUNT("expenseLedgerAccount"),
    RENEWAL_REPLACE_PLAN("renewalReplacePlan"),
    PAYMENT_BANK_ACCOUNT("paymentBankAccount"),
    PAYMENT_WITH_BANK_ACCOUNT("paymentWithBankAccount"),
    ORDER_LINE_PLAN_GROUPING("orderLinePlanGrouping"),
    SUBSKRIBE_AI_AGENT("subskribeAiAgent"),
    ENABLE_JPY_CURRENCY("enableJpyCurrency"),
    BULK_EMAIL_INVOICE("bulkEmailInvoice"),
    SYNC_OPPORTUNITY_CUSTOM_FIELDS_FROM_CRM("syncOpportunityCustomFieldsFromCrm"),
    APPROVAL_FLOW_ADMIN_BYPASS("approvalFlowAdminBypass"),
    INCLUDE_USAGE_IN_PROGRESS("includeUsageInProgress"),
    CRM_RATE_CARD("crmRateCard"),
    UPDATE_ZERO_BASE_LINE_ITEM("updateZeroBaseLineItem"),
    ONE_TIME_CHARGE_DEBOOK("oneTimeChargeDebook"),
    CUSTOM_BILLING("customBilling"),
    ROUNDED_DISCOUNTS("roundedDiscounts"),
    QUOTE_BUILDER("quoteBuilder"),
    CRM_FIELD_MAPPING("crmFieldMapping"),
    USE_BACKFILLED_MEMOIZATION("useBackfilledMemoization"),
    INVOICE_PER_ORDER("invoicePerOrder"),
    UPDATE_NON_DRAFT_ORDERS("updateNonDraftOrders"),
    THOMSON_REUTERS("thomsonReuters"),
    SMART_APPROVALS("smartApprovals"),
    BANK_TRANSACTIONS("bankTransactions"),
    PERCENT_OF_DIALOG("percentOfDialog"),
    SHOW_ALL_ESIGNED_DOCUMENTS_ON_UI("showAllEsignedDocumentsOnUI"),
    NEGOTIATED_PRICE_TIERS("negotiatedPriceTiers"),
    SLACK_QUOTE_BUILDER("slackQuoteBuilder"),
    ORDER_DOCX("orderDocx"),
    EMAIL_NOTIFICATIONS("emailNotifications"),
    DAY_CYCLE("dayCycle"),
    RENEWAL_UPLIFT_CUSTOMIZATION("renewalUpliftCustomization"),
    OPPORTUNITY_CUSTOM_FIELD_PLAN_CUSTOMIZATION("opportunityCustomFieldPlanCustomization"),
    HIGH_PRECISION_UNIT_DISPLAY("highPrecisionUnitDisplay"),
    EVERGREEN("evergreen"),
    FX_ROUNDING_V2("fxRoundingV2"),
    PLAN_LEVEL_DISCOUNT("planLevelDiscount"),
    BILLING_CYCLE_SETTING("billingCycleSetting"),
    PLAN_ES_SEARCH("planEsSearch"),
    PAYMENT_RETRY("paymentRetry"),
    MUSTACHE_INVOICE_EMAIL_TEMPLATE("mustacheInvoiceEmailTemplate"),
    VIVUN_ASC_606_MIGRATION("vivunAsc606Migration"),
    INVOICE_ON_ACTIVATION("invoiceOnActivation"),
    PREVIEW_ORDER_PDF("previewOrderPdf"),
    INVOICE_PREVIEW_CHART("invoicePreviewChart"),
    UI_LINE_ITEMS_VIEW_GRID_TABLE("uiLineItemsViewGridTable"),
    ZENDESK_TICKET_SUPPORT("zendeskTicketSupport"),
    PERCENT_OF_DISTRIBUTION("percentOfDistribution"),
    MULTI_ENTITY_PAYMENT_GATEWAY("multiEntityPaymentGateway"),
    CC_AR_CONTACT("ccArContact");

    private final String key;

    Feature(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    static Optional<Feature> fromKey(String key) {
        return Arrays.stream(values()).filter(r -> r.getKey().equalsIgnoreCase(key)).findAny();
    }
}
