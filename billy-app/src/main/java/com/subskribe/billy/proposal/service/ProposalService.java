package com.subskribe.billy.proposal.service;

import static com.subskribe.billy.invoice.service.InvoiceServiceInternal.INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION;

import com.google.common.collect.ImmutableSet;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.invoice.model.InvoiceItemPreview;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderServiceHelper;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.plangroup.model.PlanGroup;
import com.subskribe.billy.productcatalog.plangroup.model.PlanGroupItem;
import com.subskribe.billy.productcatalog.plangroup.service.PlanGroupService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.proposal.model.CreateProposalRequest;
import com.subskribe.billy.proposal.model.Proposal;
import com.subskribe.billy.proposal.model.ProposalLine;
import com.subskribe.billy.proposal.model.Variation;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Currency;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.inject.Inject;
import org.apache.commons.lang3.RandomStringUtils;

public class ProposalService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProposalService.class);

    private static final boolean DO_NOT_SKIP_PERCENT_OF = false;

    private static final Long DEFAULT_PROPOSAL_QUANTITY = 1L;

    private static final Long ZERO_QUANTITY = 0L;

    private final PlanGroupService planGroupService;

    private final ProductCatalogGetService productCatalogGetService;

    private final InvoiceService invoiceService;

    private final TenantSettingService tenantSettingService;

    private final EntityContextProvider entityContextProvider;

    private final ProrationConfigurationGetService prorationConfigurationGetService;

    @Inject
    public ProposalService(
        PlanGroupService planGroupService,
        ProductCatalogGetService productCatalogGetService,
        InvoiceService invoiceService,
        EntityContextProvider entityContextProvider,
        TenantSettingService tenantSettingService,
        ProrationConfigurationGetService prorationConfigurationGetService
    ) {
        this.planGroupService = planGroupService;
        this.productCatalogGetService = productCatalogGetService;
        this.invoiceService = invoiceService;
        this.entityContextProvider = entityContextProvider;
        this.tenantSettingService = tenantSettingService;
        this.prorationConfigurationGetService = prorationConfigurationGetService;
    }

    public Proposal createProposal(CreateProposalRequest createProposalRequest) {
        Validator.validateNonNullArgument(createProposalRequest, "Create proposal request cannot be null");
        Validator.validateCollectionNotEmpty(createProposalRequest.planGroupIds(), "Plan group ids");
        Validator.validateStringNotBlank(createProposalRequest.currencyCode(), "Currency code is missing from create proposal request");

        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();

        Set<String> planGroupIds = ImmutableSet.copyOf(createProposalRequest.planGroupIds());

        List<PlanGroup> planGroups = planGroupIds
            .stream()
            .map(planGroupId ->
                planGroupService.getPlanGroup(planGroupId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PLAN_GROUP, planGroupId))
            )
            .toList();

        validateCreateProposalRequest(createProposalRequest, planGroups);

        return createProposal(createProposalRequest, planGroups, entityId);
    }

    private static void validateCreateProposalRequest(CreateProposalRequest createProposalRequest, List<PlanGroup> planGroups) {
        Set<String> currencyCodeInPlanGroups = planGroups
            .stream()
            .map(PlanGroup::getCurrency)
            .map(Currency::getCurrencyCode)
            .collect(Collectors.toSet());
        if (currencyCodeInPlanGroups.size() > 1) {
            throw new InvalidInputException(String.format("more than one currency present in plan groups %s", currencyCodeInPlanGroups));
        }

        String planGroupCurrencyCode = currencyCodeInPlanGroups.stream().findFirst().orElseThrow();
        if (!planGroupCurrencyCode.equals(createProposalRequest.currencyCode())) {
            throw new InvalidInputException(
                String.format(
                    "Proposal creation currency code %s does not match plan group(s) currency code %s",
                    createProposalRequest.currencyCode(),
                    planGroupCurrencyCode
                )
            );
        }
    }

    private Proposal createProposal(CreateProposalRequest createProposalRequest, List<PlanGroup> planGroups, String entityId) {
        ProrationConfig prorationConfig = prorationConfigurationGetService.getEntityProrationConfiguration(entityId);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Map<PlanChargeKey, List<PlanGroupItem>> planGroupItemsByPlanChargeKey = planGroups
            .stream()
            .map(PlanGroup::getPlanGroupItems)
            .flatMap(Collection::stream)
            .collect(Collectors.groupingBy(planGroupItem -> new PlanChargeKey(planGroupItem.getPlanId(), planGroupItem.getChargeId())));
        List<String> distinctPlanIds = planGroupItemsByPlanChargeKey
            .keySet()
            .stream()
            .map(planGroupItems -> planGroupItems.planId)
            .distinct()
            .toList();
        Map<String, Plan> planMap = productCatalogGetService
            .getPlansByPlanIds(distinctPlanIds)
            .stream()
            .collect(Collectors.toMap(Plan::getPlanId, Function.identity()));
        Map<String, Charge> chargeMap = planMap
            .values()
            .stream()
            .map(Plan::getCharges)
            .flatMap(Collection::stream)
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        List<Variation> variations = IntStream.range(1, 4)
            .mapToObj(numYears ->
                getVariation(
                    planGroupItemsByPlanChargeKey,
                    entityId,
                    createProposalRequest.currencyCode(),
                    numYears,
                    planMap,
                    chargeMap,
                    prorationConfig,
                    timeZone
                )
            )
            .toList();

        return Proposal.builder()
            .proposalId("PROP-" + RandomStringUtils.randomAlphanumeric(10).toUpperCase())
            .proposalFor(createProposalRequest.proposalFor())
            .currencyCode(createProposalRequest.currencyCode())
            .variations(variations)
            .build();
    }

    private Variation getVariation(
        Map<PlanChargeKey, List<PlanGroupItem>> planGroupItemsByPlanChargeKey,
        String entityId,
        String currencyCode,
        int numYears,
        Map<String, Plan> planMap,
        Map<String, Charge> chargeMap,
        ProrationConfig prorationConfig,
        TimeZone timeZone
    ) {
        Order tempOrder = new Order();
        tempOrder.setEntityId(entityId);
        Period orderPeriod = getOrderPeriod(timeZone, numYears);
        tempOrder.setStartDate(orderPeriod.getStart());
        tempOrder.setEndDate(orderPeriod.getEnd());
        tempOrder.setBillingAnchorDate(orderPeriod.getStart());
        // TODO: probably accept billing cycle as input as well
        tempOrder.setBillingCycle(new Recurrence(Cycle.YEAR, 1));
        tempOrder.setCurrency(SupportedCurrency.validateCurrency(currencyCode));
        tempOrder.setLineItems(generateOrderLines(planGroupItemsByPlanChargeKey, planMap.values(), orderPeriod));
        tempOrder.setOrderType(OrderType.NEW);
        OrderServiceHelper.fillNetEffectOrderLines(tempOrder);

        Map<String, OrderLineItem> orderLineItemMap = tempOrder
            .getLineItems()
            .stream()
            .collect(Collectors.toMap(OrderLineItem::getOrderLineId, Function.identity()));

        InvoicePreview invoicePreview = invoiceService.generateInvoicePreview(
            tempOrder,
            DO_NOT_SKIP_PERCENT_OF,
            !INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION,
            prorationConfig,
            timeZone
        );

        Variation proposalVariation = Variation.builder()
            .termInYears(numYears)
            .sellAmountTotal(invoicePreview.getTotal())
            .listAmountTotal(invoicePreview.getTotal().add(invoicePreview.getTotalDiscount()))
            .proposalLines(makeProposalLines(invoicePreview.getLineItems(), orderLineItemMap, planMap, chargeMap))
            .build();
        LOGGER.info("created invoice preview for total amount {}", invoicePreview.getTotal());
        return proposalVariation;
    }

    public Optional<Proposal> getProposal(String proposalId) {
        LOGGER.info("Getting proposal {}", proposalId);
        return Optional.of(makeMockProposal("Awesome Company"));
    }

    private static Period getOrderPeriod(TimeZone timeZone, int yearsToAdd) {
        Instant startDate = Instant.now();
        Instant endDate = DateTimeCalculator.plusYears(timeZone.toZoneId(), startDate, yearsToAdd);
        return Period.between(startDate, endDate);
    }

    private List<ProposalLine> makeProposalLines(
        List<InvoiceItemPreview> previewItems,
        Map<String, OrderLineItem> orderLineItemMap,
        Map<String, Plan> planMap,
        Map<String, Charge> chargeMap
    ) {
        return previewItems
            .stream()
            .map(invoicePreviewItem -> {
                OrderLineItem orderLineItem = orderLineItemMap.get(invoicePreviewItem.orderLineItemId());
                Plan plan = planMap.get(orderLineItem.getPlanId());
                Charge charge = chargeMap.get(orderLineItem.getChargeId());
                return ProposalLine.builder()
                    .id(invoicePreviewItem.orderLineItemId())
                    .planId(plan.getPlanId())
                    .planName(plan.getName())
                    .chargeId(charge.getChargeId())
                    .chargeName(charge.getName())
                    .quantity(orderLineItem.getQuantity())
                    .sellAmount(invoicePreviewItem.amount())
                    .listAmount(invoicePreviewItem.listAmount())
                    .discount(invoicePreviewItem.discountAmount())
                    .sellUnitPrice(invoicePreviewItem.sellUnitPrice())
                    .listUnitPrice(invoicePreviewItem.listUnitPrice())
                    .build();
            })
            .toList();
    }

    private static List<OrderLineItem> generateOrderLines(
        Map<PlanChargeKey, List<PlanGroupItem>> planGroupItemsByPlanChargeKey,
        Collection<Plan> plans,
        Period orderPeriod
    ) {
        List<OrderLineItem> orderLineItems = new ArrayList<>();
        plans.forEach(plan ->
            plan
                .getCharges()
                .forEach(charge -> {
                    OrderLineItem orderLineItem = new OrderLineItem();
                    orderLineItem.setAction(ActionType.ADD);
                    orderLineItem.setPlanId(plan.getPlanId());
                    orderLineItem.setChargeId(charge.getChargeId());
                    orderLineItem.setQuantity(
                        resolveQuantity(planGroupItemsByPlanChargeKey.get(new PlanChargeKey(plan.getPlanId(), charge.getChargeId())), charge)
                    );
                    orderLineItem.setEffectiveDate(orderPeriod.getStart());
                    orderLineItem.setEndDate(orderPeriod.getEnd());
                    orderLineItem.setId(AutoGenerate.getNewUuid());
                    orderLineItem.setOrderLineId(AutoGenerate.getNewId());
                    // TODO: possibly accept list unit price as well in the future
                    // TODO: but input can get really tricky
                    if (charge.isCustom()) {
                        orderLineItem.setListUnitPrice(BigDecimal.ZERO);
                    }
                    // TODO: accept discount from input as well
                    orderLineItems.add(orderLineItem);
                })
        );
        return Collections.unmodifiableList(orderLineItems);
    }

    private static long resolveQuantity(List<PlanGroupItem> planGroupItems, Charge charge) {
        return planGroupItems
            .stream()
            .map(PlanGroupItem::getDefaultQuantity)
            .filter(Objects::nonNull)
            .reduce(Long::max)
            .orElseGet(() -> {
                if (charge.getType() == ChargeType.USAGE || charge.isCustom()) {
                    return ZERO_QUANTITY;
                }
                return Optional.ofNullable(charge.getDefaultQuantity()).orElse(DEFAULT_PROPOSAL_QUANTITY);
            });
    }

    public Proposal makeMockProposal(String proposalFor) {
        return Proposal.builder()
            .proposalId("PROP-" + RandomStringUtils.randomAlphanumeric(7).toUpperCase())
            .proposalFor(proposalFor)
            .variations(List.of(makeVariation()))
            .build();
    }

    private static Variation makeVariation() {
        return Variation.builder()
            .termInYears(1)
            .sellAmountTotal(new BigDecimal("100.0"))
            .listAmountTotal(new BigDecimal("110.0"))
            .proposalLines(List.of())
            .build();
    }

    private record PlanChargeKey(String planId, String chargeId) {}
}
