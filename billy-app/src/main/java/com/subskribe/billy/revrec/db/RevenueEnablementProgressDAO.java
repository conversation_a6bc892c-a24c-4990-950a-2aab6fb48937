package com.subskribe.billy.revrec.db;

import static com.subskribe.billy.jooq.default_schema.tables.RevenueEnablementProgress.REVENUE_ENABLEMENT_PROGRESS;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.jooq.default_schema.tables.records.RevenueEnablementProgressRecord;
import com.subskribe.billy.revrec.db.mapper.RevenueEnablementProgressRecordMapper;
import com.subskribe.billy.revrec.model.RevenueEnablementProgress;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.List;
import javax.inject.Inject;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class RevenueEnablementProgressDAO {

    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final EntityContextProvider entityContextProvider;
    private final RevenueEnablementProgressRecordMapper recordMapper;

    @Inject
    public RevenueEnablementProgressDAO(
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        EntityContextProvider entityContextProvider
    ) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.entityContextProvider = entityContextProvider;
        recordMapper = Mappers.getMapper(RevenueEnablementProgressRecordMapper.class);
    }

    public RevenueEnablementProgress createRevenueEnablementProgress(RevenueEnablementProgress progress) {
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        RevenueEnablementProgressRecord record = recordMapper.toRecord(progress);
        record.setTenantId(tenantId);
        record.setEntityId(entityId);
        record.reset(REVENUE_ENABLEMENT_PROGRESS.ID);
        RevenueEnablementProgressRecord result = tenantDSLContext.insertInto(REVENUE_ENABLEMENT_PROGRESS).set(record).returning().fetchOne();
        return recordMapper.fromRecord(result);
    }

    public RevenueEnablementProgress updateRevenueEnablementProgress(RevenueEnablementProgress progress) {
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();

        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        RevenueEnablementProgressRecord record = recordMapper.toRecord(progress);
        record.setEntityId(entityId);
        record.reset(REVENUE_ENABLEMENT_PROGRESS.CREATED_ON);
        record.reset(REVENUE_ENABLEMENT_PROGRESS.TENANT_ID);
        tenantDSLContext.executeUpdate(record);
        return progress;
    }

    public List<RevenueEnablementProgress> getRevenueEnablementProgress() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<RevenueEnablementProgressRecord> records = dslContext
            .selectFrom(REVENUE_ENABLEMENT_PROGRESS)
            .where(REVENUE_ENABLEMENT_PROGRESS.TENANT_ID.eq(tenantId))
            .fetchInto(RevenueEnablementProgressRecord.class);
        return recordMapper.toList(records);
    }
}
