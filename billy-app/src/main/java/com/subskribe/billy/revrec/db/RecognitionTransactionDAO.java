package com.subskribe.billy.revrec.db;

import static com.subskribe.billy.jooq.default_schema.Tables.INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW;
import static com.subskribe.billy.jooq.default_schema.Tables.ORDER_REVENUE_TRANSACTION_VIEW;
import static com.subskribe.billy.jooq.default_schema.tables.AccountingPeriod.ACCOUNTING_PERIOD;
import static com.subskribe.billy.jooq.default_schema.tables.RecognitionTransaction.RECOGNITION_TRANSACTION;

import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.RecognitionTransactionRecord;
import com.subskribe.billy.revrec.db.mapper.RecognitionTransactionRecordMapper;
import com.subskribe.billy.revrec.model.RecognitionTransaction;
import com.subskribe.billy.revrec.model.RevenueWaterfallMetadata;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import javax.inject.Inject;
import org.jooq.Cursor;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class RecognitionTransactionDAO {

    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final RecognitionTransactionRecordMapper recordMapper;

    @Inject
    public RecognitionTransactionDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        recordMapper = Mappers.getMapper(RecognitionTransactionRecordMapper.class);
    }

    public RecognitionTransaction insertRecognitionTransactionInTransaction(
        DSLContext dslContext,
        String tenantId,
        RecognitionTransaction transaction
    ) {
        RecognitionTransactionRecord record = recordMapper.toRecord(transaction);
        record.setTenantId(tenantId);
        // default null to false
        if (record.getIsRecognized() == null) {
            record.setIsRecognized(false);
        }
        record.reset(RECOGNITION_TRANSACTION.ID);
        RecognitionTransactionRecord insertedRecord = dslContext.insertInto(RECOGNITION_TRANSACTION).set(record).returning().fetchOne();
        Optional.ofNullable(insertedRecord).orElseThrow(() -> new ServiceFailureException("Failed to save the recognition transaction."));
        return recordMapper.fromRecord(insertedRecord);
    }

    public List<RecognitionTransaction> getRecognitionTransactionsByScheduledAccountingPeriod(String scheduledAccountingPeriodId) {
        Validator.validateStringNotBlank(scheduledAccountingPeriodId, "accounting period id cannot be blank");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var records = dslContext
            .selectFrom(RECOGNITION_TRANSACTION)
            .where(RECOGNITION_TRANSACTION.SCHEDULED_ACCOUNTING_PERIOD_ID.eq(scheduledAccountingPeriodId))
            .and(RECOGNITION_TRANSACTION.TENANT_ID.eq(tenantId))
            .and(RECOGNITION_TRANSACTION.IS_DELETED.isFalse())
            .fetch();
        return recordMapper.recordsToList(records);
    }

    public List<RecognitionTransaction> getRecognitionTransactionsByRecognizedAccountingPeriod(String scheduledAccountingPeriodId) {
        Validator.validateStringNotBlank(scheduledAccountingPeriodId, "accounting period id cannot be blank");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var records = dslContext
            .selectFrom(RECOGNITION_TRANSACTION)
            .where(RECOGNITION_TRANSACTION.RECOGNIZED_ACCOUNTING_PERIOD_ID.eq(scheduledAccountingPeriodId))
            .and(RECOGNITION_TRANSACTION.TENANT_ID.eq(tenantId))
            .and(RECOGNITION_TRANSACTION.IS_DELETED.isFalse())
            .fetch();
        return recordMapper.recordsToList(records);
    }

    public List<RecognitionTransaction> getRecognitionTransactionsByScheduleId(String recognitionScheduleId) {
        Validator.validateStringNotBlank(recognitionScheduleId, "recognition schedule id cannot be blank");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var records = dslContext
            .selectFrom(RECOGNITION_TRANSACTION)
            .where(RECOGNITION_TRANSACTION.SCHEDULE_ID.eq(recognitionScheduleId))
            .and(RECOGNITION_TRANSACTION.TENANT_ID.eq(tenantId))
            .and(RECOGNITION_TRANSACTION.IS_DELETED.isFalse())
            .fetch();
        return recordMapper.recordsToList(records);
    }

    public List<RecognitionTransaction> getRecognitionTransactionsByScheduleIds(List<String> recognitionScheduleIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var records = dslContext
            .selectFrom(RECOGNITION_TRANSACTION)
            .where(RECOGNITION_TRANSACTION.SCHEDULE_ID.in(recognitionScheduleIds))
            .and(RECOGNITION_TRANSACTION.TENANT_ID.eq(tenantId))
            .and(RECOGNITION_TRANSACTION.IS_DELETED.isFalse())
            .fetch();
        return recordMapper.recordsToList(records);
    }

    public List<RecognitionTransaction> getDeletedRevTransactionsById(List<String> revTransactionIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var records = dslContext
            .selectFrom(RECOGNITION_TRANSACTION)
            .where(RECOGNITION_TRANSACTION.RECOGNITION_TRANSACTION_ID.in(revTransactionIds))
            .and(RECOGNITION_TRANSACTION.TENANT_ID.eq(tenantId))
            .fetch();
        return recordMapper.recordsToList(records);
    }

    public List<RecognitionTransaction> updateRecognitionTransactions(DSLContext dslContext, AccountingPeriod currentAccountingPeriod) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var openStartDate = DateTimeConverter.instantToLocalDateTime(currentAccountingPeriod.getStartDate());
        var transactionRecords = dslContext
            .update(RECOGNITION_TRANSACTION)
            .set(RECOGNITION_TRANSACTION.IS_RECOGNIZED, true)
            .set(RECOGNITION_TRANSACTION.RECOGNIZED_ACCOUNTING_PERIOD_ID, currentAccountingPeriod.getAccountingPeriodId())
            .set(RECOGNITION_TRANSACTION.RECOGNIZED_ON, LocalDateTime.now(ZoneOffset.UTC))
            .from(ACCOUNTING_PERIOD)
            .where(RECOGNITION_TRANSACTION.SCHEDULED_ACCOUNTING_PERIOD_ID.eq(ACCOUNTING_PERIOD.ACCOUNTING_PERIOD_ID))
            .and(RECOGNITION_TRANSACTION.TENANT_ID.eq(ACCOUNTING_PERIOD.TENANT_ID))
            .and(ACCOUNTING_PERIOD.START_DATE.lessOrEqual(openStartDate))
            .and(RECOGNITION_TRANSACTION.TENANT_ID.eq(tenantId))
            .and(RECOGNITION_TRANSACTION.IS_DELETED.isFalse())
            .and(RECOGNITION_TRANSACTION.IS_RECOGNIZED.isFalse())
            .returning()
            .fetchInto(RecognitionTransactionRecord.class);
        return recordMapper.recordsToList(transactionRecords);
    }

    public void deleteRecognitionTransactionsById(List<String> revTransactionIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        dslContext
            .update(RECOGNITION_TRANSACTION)
            .set(RECOGNITION_TRANSACTION.IS_DELETED, true)
            .where(RECOGNITION_TRANSACTION.TENANT_ID.eq(tenantId))
            .and(RECOGNITION_TRANSACTION.RECOGNITION_TRANSACTION_ID.in(revTransactionIds))
            .execute();
    }

    public void ensureUniqueId(String transactionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var record = dslContext
            .selectFrom(RECOGNITION_TRANSACTION)
            .where(RECOGNITION_TRANSACTION.RECOGNITION_TRANSACTION_ID.eq(transactionId))
            .and(RECOGNITION_TRANSACTION.TENANT_ID.eq(tenantId))
            .and(RECOGNITION_TRANSACTION.IS_DELETED.isFalse())
            .fetchOne();
        if (record == null) {
            return;
        }
        throwDuplicateIdException(transactionId);
    }

    private void throwDuplicateIdException(String transactionId) {
        throw new DuplicateIdException("Duplicate recognition transaction id generated. TransactionId = " + transactionId);
    }

    // get waterfall report using list of accounting periods
    // - fetch revenue transactions using `order_revenue_transaction_view`
    // - group by schedule
    // - sum revenue amount for each accounting period
    public Cursor<Record> getWaterfallByAccountingDates(List<Instant> accountingPeriodDates, TimeZone timezone) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        List<Field<?>> fields = new ArrayList<>(getWaterfallFields());
        addAccountingPeriodFields(fields, accountingPeriodDates, timezone);
        var dslContext = dslContextProvider.getReadOnly(tenantId);
        var query = dslContext
            .select(fields)
            .from(ORDER_REVENUE_TRANSACTION_VIEW)
            .where(ORDER_REVENUE_TRANSACTION_VIEW.TENANT_ID.eq(tenantId))
            .groupBy(ORDER_REVENUE_TRANSACTION_VIEW.SCHEDULE_ID)
            .orderBy(DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.ENTITY_ID), DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.TRANSACTION_DATE).desc());
        return query.fetchLazy();
    }

    private List<Field<?>> getWaterfallFields() {
        return List.of(
            ORDER_REVENUE_TRANSACTION_VIEW.SCHEDULE_ID.as(RevenueWaterfallMetadata.REVENUE_SCHEDULE_ID.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.ACCOUNT_NAME).as(RevenueWaterfallMetadata.ACCOUNT_NAME.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.CURRENCY).as(RevenueWaterfallMetadata.TRANSACTION_CURRENCY.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.TRANSACTION_NUMBER).as(RevenueWaterfallMetadata.TRANSACTION_NUMBER.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.TRANSACTION_DATE).as(RevenueWaterfallMetadata.TRANSACTION_DATE.key()),
            DSL.min(ORDER_REVENUE_TRANSACTION_VIEW.LINE_START_DATE).as(RevenueWaterfallMetadata.LINE_START_DATE.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.LINE_END_DATE).as(RevenueWaterfallMetadata.LINE_END_DATE.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.SUBSCRIPTION_ID).as(RevenueWaterfallMetadata.SUBSCRIPTION_ID.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.PLAN_NAME).as(RevenueWaterfallMetadata.PLAN_NAME.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.CHARGE_NAME).as(RevenueWaterfallMetadata.CHARGE_NAME.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.SCHEDULE_TOTAL_REVENUE).as(RevenueWaterfallMetadata.SCHEDULE_TOTAL_REVENUE.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.SCHEDULE_POSTED_REVENUE).as(RevenueWaterfallMetadata.SCHEDULE_POSTED_REVENUE.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.SCHEDULE_UNPOSTED_REVENUE).as(RevenueWaterfallMetadata.SCHEDULE_UNPOSTED_REVENUE.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.ENTITY_ID).as(RevenueWaterfallMetadata.ENTITY_ID.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.CHARGE_ID).as(RevenueWaterfallMetadata.CHARGE_ID.key()),
            DSL.max(ORDER_REVENUE_TRANSACTION_VIEW.RECOGNITION_RULE_ID).as(RevenueWaterfallMetadata.RECOGNITION_RULE_ID.key())
        );
    }

    private void addAccountingPeriodFields(List<Field<?>> fields, List<Instant> accountingPeriodDates, TimeZone timezone) {
        Validator.validateCollectionNotEmpty(accountingPeriodDates, "accounting periods");
        for (Instant startDate : accountingPeriodDates) {
            // for each accounting period, add revenue amounts as a column
            // sample sql generated:
            // sum(case when start_date = '2024-01-01 00:00:00.0' then amount else 0 end) as month_20240101
            String formattedFieldName = RevenueWaterfallMetadata.getDateKey(startDate, timezone);
            var transactionAmountForAccountingPeriod = DSL.when(
                ORDER_REVENUE_TRANSACTION_VIEW.START_DATE.eq(DateTimeConverter.instantToLocalDateTime(startDate)),
                ORDER_REVENUE_TRANSACTION_VIEW.AMOUNT
            ).otherwise(BigDecimal.ZERO);
            fields.add(DSL.sum(transactionAmountForAccountingPeriod).as(formattedFieldName));
        }
        // add a post lump sum column to sum all revenue after last accounting period
        // sample sql generated:
        // sum(case when start_date > '2024-12-01 00:00:00.0' then amount else 0 end) as post_lump_sump
        Instant lastAccountingDate = accountingPeriodDates.get(accountingPeriodDates.size() - 1);
        var transactionAmountForLastPeriod = DSL.when(
            ORDER_REVENUE_TRANSACTION_VIEW.START_DATE.gt(DateTimeConverter.instantToLocalDateTime(lastAccountingDate)),
            ORDER_REVENUE_TRANSACTION_VIEW.AMOUNT
        ).otherwise(BigDecimal.ZERO);
        fields.add(DSL.sum(transactionAmountForLastPeriod).as(RevenueWaterfallMetadata.POST_LUMP_SUM.key()));
    }

    // invoice-based waterfall report
    public Cursor<Record> getInvoiceWaterfallByAccountingDates(List<Instant> accountingPeriodDates, TimeZone timezone) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        List<Field<?>> fields = new ArrayList<>(getInvoiceWaterfallFields());
        addInvoiceWaterfallAccountingPeriodFields(fields, accountingPeriodDates, timezone);
        var dslContext = dslContextProvider.getReadOnly(tenantId);
        var query = dslContext
            .select(fields)
            .from(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW)
            .where(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.TENANT_ID.eq(tenantId))
            .groupBy(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.SCHEDULE_ID)
            .orderBy(
                DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.ENTITY_ID),
                DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.TRANSACTION_DATE).desc()
            );
        return query.fetchLazy();
    }

    private List<Field<?>> getInvoiceWaterfallFields() {
        return List.of(
            INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.SCHEDULE_ID.as(RevenueWaterfallMetadata.REVENUE_SCHEDULE_ID.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.ACCOUNT_NAME).as(RevenueWaterfallMetadata.ACCOUNT_NAME.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.CURRENCY).as(RevenueWaterfallMetadata.TRANSACTION_CURRENCY.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.TRANSACTION_NUMBER).as(RevenueWaterfallMetadata.TRANSACTION_NUMBER.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.TRANSACTION_DATE).as(RevenueWaterfallMetadata.TRANSACTION_DATE.key()),
            DSL.min(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.LINE_START_DATE).as(RevenueWaterfallMetadata.LINE_START_DATE.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.LINE_END_DATE).as(RevenueWaterfallMetadata.LINE_END_DATE.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.SUBSCRIPTION_ID).as(RevenueWaterfallMetadata.SUBSCRIPTION_ID.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.PLAN_NAME).as(RevenueWaterfallMetadata.PLAN_NAME.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.CHARGE_NAME).as(RevenueWaterfallMetadata.CHARGE_NAME.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.SCHEDULE_TOTAL_REVENUE).as(RevenueWaterfallMetadata.SCHEDULE_TOTAL_REVENUE.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.SCHEDULE_POSTED_REVENUE).as(RevenueWaterfallMetadata.SCHEDULE_POSTED_REVENUE.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.SCHEDULE_UNPOSTED_REVENUE).as(RevenueWaterfallMetadata.SCHEDULE_UNPOSTED_REVENUE.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.ENTITY_ID).as(RevenueWaterfallMetadata.ENTITY_ID.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.CHARGE_ID).as(RevenueWaterfallMetadata.CHARGE_ID.key()),
            DSL.max(INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.RECOGNITION_RULE_ID).as(RevenueWaterfallMetadata.RECOGNITION_RULE_ID.key())
        );
    }

    private void addInvoiceWaterfallAccountingPeriodFields(List<Field<?>> fields, List<Instant> accountingPeriodDates, TimeZone timezone) {
        Validator.validateCollectionNotEmpty(accountingPeriodDates, "accounting periods");
        for (Instant startDate : accountingPeriodDates) {
            String formattedFieldName = RevenueWaterfallMetadata.getDateKey(startDate, timezone);
            var transactionAmountForAccountingPeriod = DSL.when(
                INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.START_DATE.eq(DateTimeConverter.instantToLocalDateTime(startDate)),
                INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.AMOUNT
            ).otherwise(BigDecimal.ZERO);
            fields.add(DSL.sum(transactionAmountForAccountingPeriod).as(formattedFieldName));
        }
        Instant lastAccountingDate = accountingPeriodDates.get(accountingPeriodDates.size() - 1);
        var transactionAmountForLastPeriod = DSL.when(
            INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.START_DATE.gt(DateTimeConverter.instantToLocalDateTime(lastAccountingDate)),
            INVOICE_CREDIT_REVENUE_TRANSACTION_VIEW.AMOUNT
        ).otherwise(BigDecimal.ZERO);
        fields.add(DSL.sum(transactionAmountForLastPeriod).as(RevenueWaterfallMetadata.POST_LUMP_SUM.key()));
    }

    public void purgeAllRevenueTransactionsForEntity(DSLContext dslContext, String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .deleteFrom(RECOGNITION_TRANSACTION)
            .where(RECOGNITION_TRANSACTION.TENANT_ID.eq(tenantId))
            .and(RECOGNITION_TRANSACTION.ENTITY_ID.eq(entityId))
            .execute();
    }
}
