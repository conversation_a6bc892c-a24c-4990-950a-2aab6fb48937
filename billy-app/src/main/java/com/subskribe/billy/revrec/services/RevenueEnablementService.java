package com.subskribe.billy.revrec.services;

import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.revrec.db.RevenueEnablementProgressDAO;
import com.subskribe.billy.revrec.model.RevenueEnablementProgress;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.Set;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;

public class RevenueEnablementService {

    private final PlatformFeatureService platformFeatureService;

    private final AccountingPeriodService accountingPeriodService;

    private final ProductCatalogGetService productCatalogGetService;

    private final AccountingGetService accountingGetService;

    private final AccountingService accountingService;

    private final RevenueRecognitionGetService revenueRecognitionGetService;

    private final RevenueRecognitionService revenueRecognitionService;

    private final EntityContextProvider entityContextProvider;

    private final RevenueEnablementProgressDAO revenueEnablementProgressDAO;

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    private final TenantSettingService tenantSettingService;

    private final FeatureService featureService;

    @Inject
    public RevenueEnablementService(
        PlatformFeatureService platformFeatureService,
        AccountingPeriodService accountingPeriodService,
        ProductCatalogGetService productCatalogGetService,
        AccountingGetService accountingGetService,
        AccountingService accountingService,
        RevenueRecognitionGetService revenueRecognitionGetService,
        RevenueRecognitionService revenueRecognitionService,
        EntityContextProvider entityContextProvider,
        RevenueEnablementProgressDAO revenueEnablementProgressDAO,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        TenantSettingService tenantSettingService,
        FeatureService featureService
    ) {
        this.platformFeatureService = platformFeatureService;
        this.accountingPeriodService = accountingPeriodService;
        this.productCatalogGetService = productCatalogGetService;
        this.accountingGetService = accountingGetService;
        this.accountingService = accountingService;
        this.revenueRecognitionGetService = revenueRecognitionGetService;
        this.revenueRecognitionService = revenueRecognitionService;
        this.entityContextProvider = entityContextProvider;
        this.revenueEnablementProgressDAO = revenueEnablementProgressDAO;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.tenantSettingService = tenantSettingService;
        this.featureService = featureService;
    }

    public void enableRevenueRecognition() {
        validateRevenueRecognitionEnablement();
        platformFeatureService.movePlatformFeatureToReady(PlatformFeature.REVENUE_RECOGNITION);
    }

    private void validateRevenueRecognitionEnablement() {
        if (!doesOpenAccountingPeriodExist()) {
            throw new IllegalStateException("An open accounting period must exist to enable revenue recognition.");
        }

        if (!isAccountingPlatformFeatureEnabled()) {
            throw new IllegalStateException("The accounting platform feature must be enabled to enable revenue recognition.");
        }

        if (!isRevenueRecognitionPlatformFeatureEnabled()) {
            throw new IllegalStateException("The revenue recognition platform feature must be enabled to enable revenue recognition.");
        }

        if (!areAllChargesTaggedWithRevenueRules()) {
            throw new IllegalStateException("All charges must be tagged with revenue rules to enable revenue recognition.");
        }

        if (!areAllChargesTaggedWithGLAccounts()) {
            throw new IllegalStateException("All charges must be tagged with GL accounts to enable revenue recognition.");
        }

        if (!doAllOrderLinesHaveSchedules()) {
            throw new IllegalStateException("All order lines must have schedules to enable revenue recognition.");
        }

        if (!areAccountingEventsPresentForAllTransactionTypes()) {
            throw new IllegalStateException("Accounting events must be present for all transaction types to enable revenue recognition.");
        }
    }

    // These methods are for the UI to check if the conditions are met to enable revenue recognition
    public boolean doesOpenAccountingPeriodExist() {
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        return accountingPeriodService.doesAccountingPeriodExistForEntity(entityId);
    }

    // Platform feature query is already available via `enabledPlatformFeature`, so these two methods can be private
    private boolean isAccountingPlatformFeatureEnabled() {
        return platformFeatureService.getFeatureEnablement(PlatformFeature.ACCOUNTING).isPresent();
    }

    private boolean isRevenueRecognitionPlatformFeatureEnabled() {
        // We should not prevent the enablement of revenue recognition if the feature is enabled but not ready
        // (e.g. if the entity onboarding is in progress)
        // Therefore, we cannot check for readiness here
        return platformFeatureService.getFeatureEnablement(PlatformFeature.REVENUE_RECOGNITION).isPresent();
    }

    public boolean areAllChargesTaggedWithRevenueRules() {
        List<Charge> charges = productCatalogGetService.getCharges();
        return charges.stream().allMatch(charge -> StringUtils.isNotBlank(charge.getRecognitionRuleId()));
    }

    public boolean areAllChargesTaggedWithGLAccounts() {
        List<Charge> charges = productCatalogGetService.getCharges();
        return charges
            .stream()
            .map(charge -> accountingGetService.getLedgerAccountsForCharge(charge.getChargeId()))
            .allMatch(CollectionUtils::isNotEmpty);
    }

    public boolean doAllOrderLinesHaveSchedules() {
        return revenueRecognitionGetService.doAllOrderLinesHaveSchedules();
    }

    public boolean areAccountingEventsPresentForAllTransactionTypes() {
        return accountingGetService.areAccountingEventsPresentForAllTransactionTypes();
    }

    public void ensureRevenueEnablementProgressExistsForEntity(String entityId) {
        EntityContext entityContext = EntityContext.buildEntityContextByEntityIds(Set.of(entityId));
        TenantContextInjector.spawnThreadAndRunInEntityContext(tenantIdProvider.provideTenantIdString(), entityContext, entityContextProvider, () -> {
            List<RevenueEnablementProgress> existingProgress = getRevenueEnablementProgress();
            if (CollectionUtils.isNotEmpty(existingProgress)) {
                return;
            }
            revenueEnablementProgressDAO.createRevenueEnablementProgress(new RevenueEnablementProgress());
        });
    }

    public RevenueEnablementProgress upsertRevenueEnablementProgress(RevenueEnablementProgress progress) {
        validateGoLiveDate(progress);

        List<RevenueEnablementProgress> existingProgress = getRevenueEnablementProgress();
        if (CollectionUtils.isEmpty(existingProgress)) {
            return revenueEnablementProgressDAO.createRevenueEnablementProgress(progress);
        }

        if (existingProgress.size() > 1) {
            throw new InvalidInputException("Multiple revenue enablement progress records found.");
        }
        progress.setId(existingProgress.get(0).getId());
        return revenueEnablementProgressDAO.updateRevenueEnablementProgress(progress);
    }

    private void validateGoLiveDate(RevenueEnablementProgress progress) {
        if (progress.getGoLiveDate() == null) {
            return;
        }

        // Check if the go-live date is more than one year from the start date of the current accounting period
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        AccountingPeriod currentAccountingPeriod = accountingPeriodService
            .getCurrentAccountingPeriod(entityId)
            .orElseThrow(() -> new IllegalStateException("No open accounting periods found"));
        ZoneId zoneId = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        Instant yearFromCurrentAccountingPeriodStartDate = DateTimeCalculator.plusYears(zoneId, currentAccountingPeriod.getStartDate(), 1);
        if (
            progress.getGoLiveDate().isAfter(yearFromCurrentAccountingPeriodStartDate) && featureService.isNotEnabled(Feature.VIVUN_ASC_606_MIGRATION)
        ) { // TODO: Remove this check once Vivun ASC 606 migration is complete
            throw new InvalidInputException("Go-live date cannot be more than one year from the start date of the current accounting period.");
        }
    }

    public List<RevenueEnablementProgress> getRevenueEnablementProgress() {
        return revenueEnablementProgressDAO.getRevenueEnablementProgress();
    }

    public void purgeAllAccountingAndRevenueRecognitionData() {
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        tenantDslContext.transaction(this::purgeAllAccountingAndRevenueRecognitionDataInTransaction);
    }

    private void purgeAllAccountingAndRevenueRecognitionDataInTransaction(Configuration configuration) {
        accountingService.purgeAllJournalEntriesAndAccountingData(configuration);
        revenueRecognitionService.purgeAllRevenueTransactionsAndSchedules(configuration);
        accountingPeriodService.purgeAllAccountingPeriods(configuration);
    }
}
