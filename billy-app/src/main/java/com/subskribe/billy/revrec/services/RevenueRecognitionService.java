package com.subskribe.billy.revrec.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.AccountingPeriodStatus;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.DuplicateObjectException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.subscription.SubscriptionChargeRecognition;
import com.subskribe.billy.invoice.ProrationCalculator;
import com.subskribe.billy.invoice.SellingPriceCalculator;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.services.CreditMemoRetrievalService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderStub;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.revrec.db.RecognitionEventDAO;
import com.subskribe.billy.revrec.db.RecognitionRuleDAO;
import com.subskribe.billy.revrec.db.RecognitionScheduleDAO;
import com.subskribe.billy.revrec.db.RecognitionTransactionDAO;
import com.subskribe.billy.revrec.mapper.TransactionLineItemMapper;
import com.subskribe.billy.revrec.model.RecognitionEventCompletion;
import com.subskribe.billy.revrec.model.RecognitionEventCompletionProcessingKey;
import com.subskribe.billy.revrec.model.RecognitionEventPayload;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.revrec.model.RecognitionSchedule;
import com.subskribe.billy.revrec.model.RecognitionTransaction;
import com.subskribe.billy.revrec.model.RevenueRecognitionJobPayload;
import com.subskribe.billy.revrec.model.RevenueWaterfallItem;
import com.subskribe.billy.revrec.model.SyntheticRevenueProjection;
import com.subskribe.billy.revrec.model.TransactionLineItem;
import com.subskribe.billy.revrec.model.TransactionType;
import com.subskribe.billy.shared.StreamUtils;
import com.subskribe.billy.shared.entitycache.EntityCache;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.RecognitionDateAlignment;
import com.subskribe.billy.shared.enums.RecognitionEventCompletionStatus;
import com.subskribe.billy.shared.enums.RecognitionSource;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.model.ImmutableTenantJob;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobModule;
import com.subskribe.billy.tenantjob.model.TenantJobObjectModel;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.tenantjob.service.TenantJobGetService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class RevenueRecognitionService {

    private static final Gson GSON = new Gson();

    private static final int MAX_RECOGNITION_EVENTS_TO_PROCESS = 500;
    private static final BigDecimal HUNDRED = BigDecimal.valueOf(100);

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    private static final Logger LOGGER = LoggerFactory.getLogger(RevenueRecognitionService.class);
    private static final int DEFAULT_CURRENCY_SCALE = 2;

    private final RecognitionRuleDAO recognitionRuleDAO;
    private final RecognitionRuleIdGenerator recognitionRuleIdGenerator;
    private final RecognitionScheduleDAO recognitionScheduleDAO;
    private final RecognitionScheduleIdGenerator recognitionScheduleIdGenerator;
    private final RecognitionTransactionDAO recognitionTransactionDAO;
    private final RecognitionEventDAO recognitionEventDAO;
    private final RecognitionTransactionIdGenerator recognitionTransactionIdGenerator;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final ProductCatalogGetService productCatalogGetService;
    private final InvoiceService invoiceService;
    private final InvoiceRetrievalService invoiceRetrievalService;
    private final AccountingPeriodService accountingPeriodService;
    private final FeatureService featureService;
    private final PlatformFeatureService platformFeatureService;
    private final RevenueRecognitionGetService revenueRecognitionGetService;
    private final RevenueRecognitionEventService revenueRecognitionEventService;
    private final TenantSettingService tenantSettingService;
    private final TransactionLineItemMapper transactionLineItemMapper;
    private final OrderGetService orderGetService;
    private final SubscriptionGetService subscriptionGetService;
    private final EventPublishingService eventPublishingService;
    private final CreditMemoRetrievalService creditMemoRetrievalService;
    private final SellingPriceCalculator sellingPriceCalculator;
    private final ProrationConfigurationGetService prorationConfigurationGetService;
    private final EntityContextResolver entityContextResolver;

    private final TenantJobDispatcherService tenantJobDispatcherService;

    private final TenantJobGetService tenantJobGetService;

    private final EntityContextProvider entityContextProvider;

    @Inject
    public RevenueRecognitionService(
        RecognitionRuleDAO recognitionRuleDAO,
        RecognitionRuleIdGenerator recognitionRuleIdGenerator,
        RecognitionScheduleDAO recognitionScheduleDAO,
        RecognitionScheduleIdGenerator recognitionScheduleIdGenerator,
        RecognitionTransactionDAO recognitionTransactionDAO,
        RecognitionEventDAO recognitionEventDAO,
        RecognitionTransactionIdGenerator recognitionTransactionIdGenerator,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider,
        ProductCatalogGetService productCatalogGetService,
        InvoiceService invoiceService,
        InvoiceRetrievalService invoiceRetrievalService,
        AccountingPeriodService accountingPeriodService,
        FeatureService featureService,
        PlatformFeatureService platformFeatureService,
        RevenueRecognitionGetService revenueRecognitionGetService,
        RevenueRecognitionEventService revenueRecognitionEventService,
        TenantSettingService tenantSettingService,
        OrderGetService orderGetService,
        SubscriptionGetService subscriptionGetService,
        EventPublishingService eventPublishingService,
        CreditMemoRetrievalService creditMemoRetrievalService,
        SellingPriceCalculator sellingPriceCalculator,
        ProrationConfigurationGetService prorationConfigurationGetService,
        EntityContextResolver entityContextResolver,
        TenantJobDispatcherService tenantJobDispatcherService,
        TenantJobGetService tenantJobGetService,
        EntityContextProvider entityContextProvider
    ) {
        this.recognitionRuleDAO = recognitionRuleDAO;
        this.recognitionRuleIdGenerator = recognitionRuleIdGenerator;
        this.recognitionScheduleDAO = recognitionScheduleDAO;
        this.recognitionScheduleIdGenerator = recognitionScheduleIdGenerator;
        this.recognitionTransactionDAO = recognitionTransactionDAO;
        this.recognitionEventDAO = recognitionEventDAO;
        this.recognitionTransactionIdGenerator = recognitionTransactionIdGenerator;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.productCatalogGetService = productCatalogGetService;
        this.invoiceService = invoiceService;
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.accountingPeriodService = accountingPeriodService;
        this.featureService = featureService;
        this.platformFeatureService = platformFeatureService;
        this.revenueRecognitionGetService = revenueRecognitionGetService;
        this.revenueRecognitionEventService = revenueRecognitionEventService;
        this.tenantSettingService = tenantSettingService;
        this.orderGetService = orderGetService;
        this.subscriptionGetService = subscriptionGetService;
        this.eventPublishingService = eventPublishingService;
        this.creditMemoRetrievalService = creditMemoRetrievalService;
        this.sellingPriceCalculator = sellingPriceCalculator;
        this.prorationConfigurationGetService = prorationConfigurationGetService;
        this.entityContextResolver = entityContextResolver;
        this.tenantJobDispatcherService = tenantJobDispatcherService;
        this.tenantJobGetService = tenantJobGetService;
        this.entityContextProvider = entityContextProvider;
        transactionLineItemMapper = Mappers.getMapper(TransactionLineItemMapper.class);
    }

    public RecognitionRule upsertRecognitionRule(RecognitionRule recognitionRule) {
        recognitionRule.sanitize();
        validateRecognitionRule(recognitionRule);
        validateDuplicateName(recognitionRule);

        // We should not prevent the creation of a recognition rule if the feature is enabled but not ready
        // (e.g. if the entity onboarding is in progress)
        // Therefore, we cannot check for readiness here
        if (platformFeatureService.getFeatureEnablement(PlatformFeature.REVENUE_RECOGNITION).isEmpty()) {
            throw new InvalidInputException("Revenue Recognition feature is not enabled");
        }

        if (StringUtils.isBlank(recognitionRule.getRuleId())) {
            // Create Rule
            recognitionRule.setRuleId(recognitionRuleIdGenerator.generate());
            Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(recognitionRule.getEntityIds());
            recognitionRule.setEntityIds(entityIds);
            return recognitionRuleDAO.createRecognitionRule(recognitionRule);
        }

        // Update Existing Rule
        var existingRecognitionRule = recognitionRuleDAO.getRecognitionRuleByRuleId(recognitionRule.getRuleId());
        if (existingRecognitionRule.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.RECOGNITION_RULE, recognitionRule.getRuleId());
        }
        recognitionRule.setId(existingRecognitionRule.get().getId());
        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            recognitionRule.getEntityIds(),
            existingRecognitionRule.get().getEntityIds()
        );
        recognitionRule.setEntityIds(entityIds);
        return recognitionRuleDAO.updateRecognitionRule(recognitionRule);
    }

    private void validateDuplicateName(RecognitionRule recognitionRule) {
        var existingRecognitionRule = recognitionRuleDAO.getRecognitionRuleByName(recognitionRule.getName());
        if (existingRecognitionRule.isPresent() && !Objects.equals(recognitionRule.getRuleId(), existingRecognitionRule.get().getRuleId())) {
            throw new DuplicateObjectException(String.format("Recognition rule with name %s already exist", recognitionRule.getName()));
        }
    }

    private void validateRecognitionRule(RecognitionRule recognitionRule) {
        recognitionRule.validate();
        if (!featureService.isEnabled(Feature.REV_REC_INVOICE) && recognitionRule.getSource() == RecognitionSource.INVOICE) {
            throw new IllegalArgumentException("invoice based recognition rules are not allowed");
        }
    }

    public RecognitionRule deleteRecognitionRule(String recognitionRuleId) {
        Validator.validateStringNotBlank(recognitionRuleId, "recognitionRuleId is required");

        if (productCatalogGetService.isRecognitionRuleUsedByCharge(recognitionRuleId)) {
            LOGGER.info("Unable to delete recognition rule due to linked charges");
            throw new IllegalStateException("Must remove references to recognition rule before deletion");
        }

        var existingRecognitionRule = recognitionRuleDAO
            .getRecognitionRuleByRuleId(recognitionRuleId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.RECOGNITION_RULE, recognitionRuleId));
        return recognitionRuleDAO.deleteRecognitionRule(existingRecognitionRule.getRuleId());
    }

    public void processRevenueRecognitionEvents() {
        DSLContext context = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        // revenue recognition feature may not be setup for tenant (sentinel accounting period not set)
        // so if current accounting period is not found we simply return
        Optional<AccountingPeriod> currentAccountingPeriodOptional = accountingPeriodService.getCurrentAccountingPeriod();
        if (currentAccountingPeriodOptional.isEmpty()) {
            LOGGER.debug("current accounting period not found for tenant nothing to do!!!");
            return;
        }
        AccountingPeriod currentAccountingPeriod = currentAccountingPeriodOptional.get();
        // these are the things we do in a transaction
        // - read events to be processed
        // - process them and generate schedule and transactions
        // - mark the relevant events as processed
        context.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            List<RecognitionEventCompletion> toProcess = recognitionEventDAO.getAcceptedEventsInArrivalOrderInTransaction(
                context,
                currentAccountingPeriod.getAccountingPeriodId(),
                MAX_RECOGNITION_EVENTS_TO_PROCESS
            );

            // Todo - skip events for last x minutes - to avoid processing records on the fly (being inserted now)

            Map<RecognitionEventCompletionProcessingKey, List<RecognitionEventCompletion>> eventsGrouped = toProcess
                .stream()
                .collect(
                    Collectors.groupingBy(event ->
                        new RecognitionEventCompletionProcessingKey.Builder()
                            .withSubscriptionId(event.getSubscriptionId())
                            .withChargeId(event.getChargeId())
                            .withAccountingPeriodId(event.getAccountingPeriodId())
                            .build()
                    )
                );

            List<RecognitionEventCompletion> processedEvents = processSelectedEvents(context, eventsGrouped);

            recognitionEventDAO.markRecognitionEventsAsProcessedInTransaction(dslContext, processedEvents);
        });
    }

    private List<RecognitionEventCompletion> processSelectedEvents(
        DSLContext context,
        Map<RecognitionEventCompletionProcessingKey, List<RecognitionEventCompletion>> eventsGrouped
    ) {
        List<RecognitionEventCompletion> processedEvents = new ArrayList<>();
        eventsGrouped.forEach((key, events) -> {
            // sort here just to be sure
            List<RecognitionEventCompletion> sortedOnArrival = events
                .stream()
                .sorted(Comparator.comparing(RecognitionEventCompletion::getArrivedOn))
                .toList();
            RecognitionEventCompletion earliest = sortedOnArrival.stream().findFirst().orElseThrow();

            Optional<RecognitionEventCompletion> previousEvent = recognitionEventDAO.getPreviousEventBasedOnArrivalTime(earliest);
            if (previousEvent.isPresent()) {
                // assert that the previous event is PROCESSED otherwise there is a bug in our code, we should fail
                // to continue to process events and log error and alert operator
                Validator.checkStateInternal(
                    RecognitionEventCompletionStatus.PROCESSED == previousEvent.get().getStatus(),
                    String.format("recognition event %s not in expected state %s", previousEvent, RecognitionEventCompletionStatus.PROCESSED)
                );
            }

            for (RecognitionEventCompletion event : sortedOnArrival) {
                BigDecimal previousCompletion = previousEvent.map(RecognitionEventCompletion::getUnitOfCompletion).orElse(BigDecimal.ZERO);
                processSelectedEvent(context, event, previousCompletion);
                previousEvent = Optional.of(event);
            }
            processedEvents.addAll(events);
        });
        return Collections.unmodifiableList(processedEvents);
    }

    private void processSelectedEvent(DSLContext context, RecognitionEventCompletion recognitionEventCompletion, BigDecimal previousCompletion) {
        // get accounting period to use
        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(recognitionEventCompletion.getAccountingPeriodId());
        if (accountingPeriod.getStatus() != AccountingPeriodStatus.OPEN) {
            throw new IllegalStateException(
                String.format(
                    "unable to process event for accounting period which is not open. event id = %s, accounting period id = %s",
                    recognitionEventCompletion.getId(),
                    recognitionEventCompletion.getAccountingPeriodId()
                )
            );
        }

        // find the delta percent completion
        BigDecimal deltaPercentCompletion = recognitionEventCompletion.getUnitOfCompletion().subtract(previousCompletion);

        // get transaction items for this subscription + charge
        List<TransactionLineItem> transactionLineItems = revenueRecognitionEventService.getTransactionLineItemsForEvent(recognitionEventCompletion);

        // ramp line items -> group by ramp and generate one schedule for each group
        List<TransactionLineItem> rampLineItems = transactionLineItems
            .stream()
            .filter(li -> li.getTransactionType() == TransactionType.ORDER_RAMP_GROUP)
            .toList();
        Map<String, List<TransactionLineItem>> rampGroups = rampLineItems
            .stream()
            .collect(Collectors.groupingBy(TransactionLineItem::getRampGroupId));
        rampGroups.forEach((rampGroupId, rampGroup) ->
            processEventForTransactionLineItem(context, rampGroup.get(0), recognitionEventCompletion, deltaPercentCompletion, accountingPeriod)
        );

        // non ramp items -> generate one schedule per line item
        List<TransactionLineItem> nonRampLineItems = transactionLineItems
            .stream()
            .filter(li -> li.getTransactionType() != TransactionType.ORDER_RAMP_GROUP)
            .toList();
        nonRampLineItems.forEach(transactionLineItem ->
            processEventForTransactionLineItem(context, transactionLineItem, recognitionEventCompletion, deltaPercentCompletion, accountingPeriod)
        );
    }

    private void processEventForTransactionLineItem(
        DSLContext context,
        TransactionLineItem transactionLineItem,
        RecognitionEventCompletion recognitionEventCompletion,
        BigDecimal deltaPercentCompletion,
        AccountingPeriod accountingPeriod
    ) {
        String tenantId = recognitionEventCompletion.getTenantId();

        // retrieve schedule
        Optional<RecognitionSchedule> optionalRecognitionSchedule = revenueRecognitionGetService.getRecognitionScheduleForLineItem(
            context,
            tenantId,
            transactionLineItem
        );

        // create schedule, if not found
        RecognitionRule recognitionRule = getAndValidateRecognitionRuleByChargeId(transactionLineItem.getChargeId()).orElseThrow();
        if (optionalRecognitionSchedule.isEmpty()) {
            var recognitionSchedule = createRecognitionSchedule(
                transactionLineItem,
                transactionLineItem.getAmount(),
                Period.between(transactionLineItem.getStartDate(), transactionLineItem.getEndDate()),
                recognitionRule,
                accountingPeriod
            );
            RecognitionSchedule insertedSchedule = recognitionScheduleDAO.createRecognitionScheduleInTransaction(
                context,
                tenantId,
                recognitionSchedule
            );
            optionalRecognitionSchedule = Optional.of(insertedSchedule);
        }
        RecognitionSchedule recognitionSchedule = optionalRecognitionSchedule.get();

        BigDecimal transactionAmount =
            switch (recognitionRule.getRecognitionEventType()) {
                case PERCENTAGE_OF_COMPLETION -> Numbers.scaledDivide(
                    // TODO: assuming CUMULATIVE configuration // tx amount = delta percent completion * schedule amount / 100
                    deltaPercentCompletion.multiply(recognitionSchedule.getTotalRevenue()),
                    HUNDRED
                );
                case AMOUNT -> recognitionEventCompletion.getUnitOfCompletion(); // validate that the input amount + recognized amount + any unrecognized amount is not greater than the schedule amount // TODO: assuming ABSOLUTE configuration
            };

        RecognitionTransaction recognitionTransaction = createRecognitionTransaction(
            transactionAmount,
            recognitionSchedule,
            accountingPeriod.getAccountingPeriodId()
        );

        // insert transaction
        recognitionTransactionDAO.insertRecognitionTransactionInTransaction(context, tenantId, recognitionTransaction);
    }

    public void processRecognitionScheduleJob(String message) {
        RevenueRecognitionJobPayload payload;
        try {
            payload = GSON.fromJson(message, RevenueRecognitionJobPayload.class);
        } catch (JsonSyntaxException e) {
            LOGGER.error("unable to parse message body: {}", message);
            throw new ServiceFailureException("unable to parse message body", e);
        }
        generateRecognitionSchedulesForSourceTransaction(payload.getSourceTransactionType(), payload.getTransactionId());
    }

    public Optional<SubscriptionChargeRecognition> getSubscriptionChargeRecognition(String subscriptionId, String chargeId) {
        // Initialize with defaults
        var subscriptionChargeRecognition = new SubscriptionChargeRecognition();
        subscriptionChargeRecognition.setSubscriptionId(subscriptionId);
        subscriptionChargeRecognition.setChargeId(chargeId);
        subscriptionChargeRecognition.setTotalRevenue(BigDecimal.ZERO);
        subscriptionChargeRecognition.setRecognizedRevenue(BigDecimal.ZERO);
        subscriptionChargeRecognition.setDeferredRevenue(BigDecimal.ZERO);
        subscriptionChargeRecognition.setRevenueWaterfallItems(new ArrayList<>());

        // get subscription
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);

        // set current accounting period, if present
        Optional<AccountingPeriod> optionalAccountingPeriod = accountingPeriodService.getCurrentAccountingPeriod(subscription.getEntityId());
        optionalAccountingPeriod.ifPresent(subscriptionChargeRecognition::setAccountingPeriod);

        // get charge
        Charge charge = productCatalogGetService.getChargeByChargeId(chargeId);
        if (StringUtils.isBlank(charge.getRecognitionRuleId())) {
            return Optional.of(subscriptionChargeRecognition);
        }

        // get recognition rule
        RecognitionRule recognitionRule = recognitionRuleDAO
            .getRecognitionRuleByRuleId(charge.getRecognitionRuleId())
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.RECOGNITION_RULE, charge.getRecognitionRuleId()));

        // get line items
        List<TransactionLineItem> lineItems = revenueRecognitionEventService.getTransactionLineItemsForSubscriptionCharge(
            recognitionRule.getSource(),
            subscriptionId,
            chargeId
        );

        // get recognition schedules
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        List<RecognitionSchedule> recognitionSchedules = lineItems
            .stream()
            .map(lineItem -> revenueRecognitionGetService.getRecognitionScheduleForLineItem(dslContext, tenantId, lineItem))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .filter(StreamUtils.distinctByKey(RecognitionSchedule::getScheduleId))
            .toList();

        // get recognition transactions
        List<RecognitionTransaction> recognitionTransactions = recognitionSchedules
            .stream()
            .map(recognitionSchedule -> recognitionTransactionDAO.getRecognitionTransactionsByScheduleId(recognitionSchedule.getScheduleId()))
            .flatMap(Collection::stream)
            .collect(Collectors.toList());

        // invoiced value
        BigDecimal totalAmount = lineItems.stream().map(TransactionLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // recognized revenue
        BigDecimal recognizedRevenue = recognitionSchedules
            .stream()
            .map(RecognitionSchedule::getRecognizedRevenue)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // deferred revenue
        BigDecimal deferredRevenue = totalAmount.subtract(recognizedRevenue);

        // fetch list of events for this subscription charge
        List<RevenueWaterfallItem> revenueWaterfallItems = getWaterfallForSubscriptionChargeEvents(subscriptionId, chargeId, recognitionTransactions);

        // update subscription charge details
        subscriptionChargeRecognition.setRecognitionRule(recognitionRule);
        subscriptionChargeRecognition.setInvoicedValue(totalAmount);
        subscriptionChargeRecognition.setTotalRevenue(totalAmount);
        subscriptionChargeRecognition.setRecognizedRevenue(recognizedRevenue);
        subscriptionChargeRecognition.setDeferredRevenue(deferredRevenue);
        subscriptionChargeRecognition.setRevenueWaterfallItems(revenueWaterfallItems);
        return Optional.of(subscriptionChargeRecognition);
    }

    private List<RevenueWaterfallItem> getWaterfallForSubscriptionChargeEvents(
        String subscriptionId,
        String chargeId,
        List<RecognitionTransaction> transactions
    ) {
        // get events for subscription id, charge id
        List<RecognitionEventCompletion> recognitionEvents = revenueRecognitionEventService.getRecognitionEventsBySubscriptionIdChargeId(
            subscriptionId,
            chargeId
        );

        // map of the latest events for each accounting period ids
        Map<String, RecognitionEventCompletion> recentEventsByAccountingPeriod = recognitionEvents
            .stream()
            .collect(
                Collectors.toMap(
                    RecognitionEventCompletion::getAccountingPeriodId,
                    Function.identity(),
                    BinaryOperator.maxBy(Comparator.comparing(RecognitionEventCompletion::getArrivedOn))
                )
            );

        var transactionsByAccountingPeriod = transactions
            .stream()
            .collect(Collectors.groupingBy(RecognitionTransaction::getScheduledAccountingPeriodId));

        // map of accounting periods by accounting period ids
        Set<String> accountingPeriodIds = recentEventsByAccountingPeriod.keySet();
        Map<String, AccountingPeriod> accountingPeriodsById = accountingPeriodIds
            .stream()
            .map(accountingPeriodService::getAccountingPeriod)
            .collect(Collectors.toMap(AccountingPeriod::getAccountingPeriodId, Function.identity()));

        // prepare and return waterfall items
        return accountingPeriodIds
            .stream()
            .map(accountingPeriodId ->
                buildRevenueWaterfallItem(
                    accountingPeriodsById.get(accountingPeriodId),
                    transactionsByAccountingPeriod.get(accountingPeriodId),
                    Optional.ofNullable(recentEventsByAccountingPeriod.get(accountingPeriodId).getUnitOfCompletion()).orElse(BigDecimal.ZERO)
                )
            )
            .sorted(Comparator.comparing(waterfallItem -> waterfallItem.getAccountingPeriod().getStartDate()))
            .collect(Collectors.toList());
    }

    private RevenueWaterfallItem buildRevenueWaterfallItem(
        AccountingPeriod accountingPeriod,
        List<RecognitionTransaction> transactions,
        BigDecimal unitOfCompletion
    ) {
        if (CollectionUtils.isEmpty(transactions)) {
            return new RevenueWaterfallItem.Builder()
                .withRevenueAmount(BigDecimal.ZERO)
                .withStatus(RevenueWaterfallItem.RevenueStatus.PROCESSING)
                .build();
        }

        var builder = new RevenueWaterfallItem.Builder().withAccountingPeriod(accountingPeriod);

        BigDecimal recognizedRevenue = transactions
            .stream()
            .filter(RecognitionTransaction::getIsRecognized)
            .map(RecognitionTransaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unrecognizedRevenue = transactions
            .stream()
            .filter(Predicate.not(RecognitionTransaction::getIsRecognized))
            .map(RecognitionTransaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        boolean hasUnrecognized = transactions.stream().anyMatch(Predicate.not(RecognitionTransaction::getIsRecognized));
        if (hasUnrecognized) {
            builder = builder.withRevenueAmount(recognizedRevenue.add(unrecognizedRevenue)).withStatus(RevenueWaterfallItem.RevenueStatus.OPEN);
        } else {
            builder = builder.withRevenueAmount(recognizedRevenue).withStatus(RevenueWaterfallItem.RevenueStatus.POSTED);
        }

        String scheduleId = transactions.stream().findFirst().orElseThrow().getScheduleId();
        RecognitionSchedule schedule = recognitionScheduleDAO
            .getRecognitionSchedulesByScheduleIds(List.of(scheduleId))
            .stream()
            .findFirst()
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.RECOGNITION_SCHEDULE, scheduleId));
        RecognitionRule rule = recognitionRuleDAO
            .getRecognitionRuleByRuleId(schedule.getRuleId())
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.RECOGNITION_RULE, schedule.getRuleId()));
        switch (rule.getRecognitionEventType()) {
            // TODO: assuming CUMULATIVE configuration
            case PERCENTAGE_OF_COMPLETION -> builder = builder.withUnitOfCompletion(unitOfCompletion);
            // TODO: assuming ABSOLUTE configuration
            case AMOUNT -> builder = builder.withUnitOfCompletion(recognizedRevenue);
        }

        return builder.build();
    }

    public SyntheticRevenueProjection createSyntheticRevenueTransactions(InvoiceItem invoiceItem, RecognitionRule recognitionRule) {
        var transactionLineItem = transactionLineItemMapper.fromInvoiceItem(invoiceItem);

        // first generate synthetic accounting periods
        List<AccountingPeriod> accountingPeriods = accountingPeriodService.generateSyntheticAccountingPeriod(
            Period.between(invoiceItem.getPeriodStartDate(), invoiceItem.getPeriodEndDate())
        );

        return createSyntheticRevenueTransactionsForAccountingPeriods(transactionLineItem, recognitionRule, accountingPeriods);
    }

    public SyntheticRevenueProjection createSyntheticRevenueTransactionsForAccountingPeriods(
        TransactionLineItem transactionLineItem,
        RecognitionRule recognitionRule,
        List<AccountingPeriod> accountingPeriods
    ) {
        if (accountingPeriods.isEmpty()) {
            throw new IllegalArgumentException("the provided invoice item needs to span at least one accounting period");
        }

        // filter out accounting period outside of transaction period
        accountingPeriods = accountingPeriods
            .stream()
            .filter(accountingPeriod ->
                Period.overlapOf(
                    Period.between(accountingPeriod.getStartDate(), accountingPeriod.getEndDate()),
                    Period.between(transactionLineItem.getStartDate(), transactionLineItem.getEndDate())
                ).isPresent()
            )
            .collect(Collectors.toList());

        // assume first accounting period is the current accounting period
        // TODO: we can accept this as an argument as well, if needed
        AccountingPeriod currentAccountingPeriod = accountingPeriods.stream().findFirst().orElseThrow();
        currentAccountingPeriod.setStatus(AccountingPeriodStatus.OPEN);

        // first generate schedule
        RecognitionSchedule recognitionSchedule = createRecognitionSchedule(
            transactionLineItem,
            transactionLineItem.getAmount(),
            Period.between(transactionLineItem.getStartDate(), transactionLineItem.getEndDate()),
            recognitionRule,
            currentAccountingPeriod
        );
        List<RecognitionTransaction> recognitionTransactions = createRevenueRecognitionTransactions(
            transactionLineItem,
            recognitionRule,
            currentAccountingPeriod,
            accountingPeriods,
            recognitionSchedule
        );

        return new SyntheticRevenueProjection.Builder()
            .withCurrentAccountingPeriod(currentAccountingPeriod)
            .withSyntheticSchedule(recognitionSchedule)
            .withSyntheticTransactions(recognitionTransactions)
            .build();
    }

    public void regenerateRevScheduleByOrderId(String orderId) {
        safelyRemoveRevSchedulesByOrderId(orderId);
        generateRecognitionSchedulesForSourceTransaction(TransactionType.ORDER, orderId);
    }

    public void generateAllRevSchedulesForTenant() {
        Instant pageToken = null;
        do {
            PageResult<List<OrderStub>, Instant> pageResult = orderGetService.getOrderStubs(PageRequest.from(pageToken, PageRequest.MAX_LIMIT));
            List<OrderStub> orderStubs = pageResult.getResult().stream().filter(o -> o.getStatus() == OrderStatus.EXECUTED).toList();
            orderStubs.forEach(orderStub -> generateRecognitionSchedulesForSourceTransaction(TransactionType.ORDER, orderStub.getOrderId()));
            pageToken = pageResult.getNextPageToken();
        } while (pageToken != null);
    }

    public void regenerateAllRevSchedulesForTenant() {
        Instant pageToken = null;
        do {
            PageResult<List<OrderStub>, Instant> pageResult = orderGetService.getOrderStubs(PageRequest.from(pageToken, PageRequest.MAX_LIMIT));
            List<OrderStub> orderStubs = pageResult.getResult().stream().filter(o -> o.getStatus() == OrderStatus.EXECUTED).toList();
            orderStubs.forEach(orderStub -> regenerateRevScheduleByOrderId(orderStub.getOrderId()));
            pageToken = pageResult.getNextPageToken();
        } while (pageToken != null);
    }

    // fetch all rev transactions for given order
    // check if no revenue is recognized
    // soft delete the rev transactions, if no revenue is recognized
    // soft delete the rev schedules
    // throw exception if revenue is recognized
    public void safelyRemoveRevSchedulesByOrderId(String orderId) {
        List<RecognitionTransaction> recognitionTransactions = revenueRecognitionGetService.getRecognitionTransactionsByOrderId(orderId);
        boolean isAnyRecognized = recognitionTransactions.stream().anyMatch(RecognitionTransaction::getIsRecognized);
        if (isAnyRecognized) {
            throw new InvalidInputException(
                "some of the rev transactions are recognized. cannot proceed with deletion of rev schedules. order id: " + orderId
            );
        }
        List<String> revTransactionIds = recognitionTransactions.stream().map(RecognitionTransaction::getRecognitionTransactionId).toList();
        recognitionTransactionDAO.deleteRecognitionTransactionsById(revTransactionIds);
        List<String> revScheduleIds = recognitionTransactions.stream().map(RecognitionTransaction::getScheduleId).distinct().toList();
        recognitionScheduleDAO.deleteRecognitionSchedulesById(revScheduleIds);
        List<RecognitionTransaction> deletedRecognitionTransactions = recognitionTransactionDAO.getDeletedRevTransactionsById(revTransactionIds);
        boolean isAnyRecognizedDuringDeletion = deletedRecognitionTransactions.stream().anyMatch(RecognitionTransaction::getIsRecognized);
        if (isAnyRecognizedDuringDeletion) {
            // on call notes:
            // this is an edge case where concurrently revenue was recognized, while the schedule deletion was in progress.
            // revert this deletion.
            // 1. query recognition_schedule table using this order id
            // 2. query recognition_transaction table using all the schedule ids obtained in step 1. also query the soft deleted records.
            // 3. undelete these recognition_transaction records by setting is_deleted false
            throw new ServiceFailureException("invalid state reached. rev transactions recognized during deletion. order id: " + orderId);
        }
    }

    public void generateRecognitionSchedulesForSourceTransaction(TenantJobObjectModel objectModel, String transactionId) {
        TransactionType transactionType =
            switch (objectModel) {
                case ORDER -> TransactionType.ORDER;
                case INVOICE -> TransactionType.INVOICE;
                case CREDIT_MEMO -> TransactionType.CREDIT_MEMO;
                default -> throw new ServiceFailureException("unexpected object type for revenue recognition: " + objectModel);
            };
        generateRecognitionSchedulesForSourceTransaction(transactionType, transactionId);
    }

    // Todo - invoice, credit memo based revrec -> mark them as specific methods for Vivun under a tenant config
    public void generateRecognitionSchedulesForSourceTransaction(TransactionType sourceTransactionType, String transactionId) {
        List<TransactionLineItem> transactionLineItems = revenueRecognitionGetService.getTransactionLineItemsForSourceTransaction(
            sourceTransactionType,
            transactionId
        );
        switch (sourceTransactionType) {
            case ORDER -> generateRecognitionSchedulesForOrderSource(transactionLineItems);
            case INVOICE, CREDIT_MEMO -> transactionLineItems
                .stream()
                .filter(this::checkScheduleGenerationForLineItem)
                .forEach(transactionLineItem -> generateRecognitionSchedule(sourceTransactionType, transactionLineItem));
            default -> throw new IllegalStateException("unknown transaction type: " + sourceTransactionType);
        }
    }

    private void generateRecognitionSchedulesForOrderSource(List<TransactionLineItem> transactionLineItems) {
        // ramp items -> group and then generate schedules
        List<TransactionLineItem> rampLineItems = transactionLineItems
            .stream()
            .filter(li -> li.getTransactionType() == TransactionType.ORDER_RAMP_GROUP)
            .map(this::recalculateAmountForRampLineItem)
            .collect(Collectors.toList());
        generateSchedulesForRampLineItems(rampLineItems);

        // non ramp items -> generate schedule
        List<TransactionLineItem> nonRampLineItems = transactionLineItems
            .stream()
            .filter(li -> li.getTransactionType() == TransactionType.ORDER)
            .toList();
        nonRampLineItems
            .stream()
            .filter(this::checkScheduleGenerationForLineItem)
            .forEach(transactionLineItem -> generateRecognitionSchedule(TransactionType.ORDER, transactionLineItem));
    }

    private TransactionLineItem recalculateAmountForRampLineItem(TransactionLineItem transactionLineItem) {
        if (transactionLineItem.getAmount().compareTo(BigDecimal.ZERO) != 0) {
            return transactionLineItem;
        }

        OrderLineItem orderLineItem = orderGetService
            .getOrderLineItemByOrderLineItemId(transactionLineItem.getId())
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ORDER_LINE_ITEM, transactionLineItem.getId()));
        Order order = orderGetService.getOrderByOrderId(orderLineItem.getOrderId());
        Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
        Charge charge = productCatalogGetService.getChargeByChargeId(orderLineItem.getChargeId());
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);
        BigDecimal amount = sellingPriceCalculator.calculateItemListAmount(
            subscription.getStartDate(),
            order,
            orderLineItem,
            charge,
            prorationConfig
        );
        transactionLineItem.setAmount(amount);

        return transactionLineItem;
    }

    private void generateSchedulesForRampLineItems(List<TransactionLineItem> rampLineItems) {
        Map<String, List<TransactionLineItem>> rampGroups = rampLineItems
            .stream()
            .collect(Collectors.groupingBy(TransactionLineItem::getRampGroupId));

        // For each ramp group, generate a schedule
        rampGroups.forEach((rampGroupId, rampGroup) -> generateScheduleForRampGroup(rampGroup));
    }

    private Map<Period, List<TransactionLineItem>> groupRampLineItemsByQuantity(List<TransactionLineItem> rampGroup) {
        List<TransactionLineItem> sortedRampGroup = rampGroup.stream().sorted(Comparator.comparing(TransactionLineItem::getStartDate)).toList();

        Map<Period, List<TransactionLineItem>> quantityGroups = new HashMap<>();
        TransactionLineItem prevLineItem = null;
        List<TransactionLineItem> quantityGroup = new ArrayList<>();
        Period period = Period.between(sortedRampGroup.get(0).getStartDate(), sortedRampGroup.get(0).getEndDate());
        for (TransactionLineItem lineItem : sortedRampGroup) {
            if (prevLineItem == null || prevLineItem.getQuantity().equals(lineItem.getQuantity())) {
                quantityGroup.add(lineItem);
                period = Period.between(period.getStart(), lineItem.getEndDate());
            } else {
                quantityGroups.put(period, quantityGroup);
                quantityGroup = new ArrayList<>();
                quantityGroup.add(lineItem);
                period = Period.between(lineItem.getStartDate(), lineItem.getEndDate());
            }
            prevLineItem = lineItem;
        }
        if (CollectionUtils.isNotEmpty(quantityGroup)) {
            quantityGroups.put(period, quantityGroup);
        }

        return quantityGroups;
    }

    private void generateScheduleForRampGroup(List<TransactionLineItem> rampGroup) {
        Optional<TransactionLineItem> firstLineItemOptional = rampGroup.stream().findFirst();
        if (firstLineItemOptional.isEmpty()) {
            return;
        }
        TransactionLineItem firstLineItem = firstLineItemOptional.get();
        String rampGroupId = firstLineItem.getRampGroupId();
        String chargeId = firstLineItem.getChargeId();

        BigDecimal totalAmount = rampGroup.stream().map(TransactionLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (Numbers.isZero(totalAmount)) {
            LOGGER.warn("skipping generation of revrec schedule for ramp group with 0 amount: id = {}", rampGroupId);
            return;
        }

        if (getExistingRecognitionSchedule(TransactionType.ORDER_RAMP_GROUP, rampGroupId).isPresent()) {
            LOGGER.warn("Recognition schedule already created for ramp group {}", rampGroupId);
            return;
        }

        // fetch recognition rule
        Optional<RecognitionRule> recognitionRuleOptional = getAndValidateRecognitionRuleByChargeId(chargeId);
        if (recognitionRuleOptional.isEmpty()) {
            LOGGER.info("Recognition schedule cannot be created for ramp group id {} chargeId {} missing rule", rampGroupId, chargeId);
            return;
        }
        RecognitionRule rule = recognitionRuleOptional.get();
        if (rule.getSource() != RecognitionSource.ORDER) {
            LOGGER.info("Ignoring ramp group {} as the related rule {} is not order based", rampGroupId, rule.getRuleId());
            return;
        }

        // fetch reference accounting period
        AccountingPeriod referenceAccountingPeriod = getReferenceAccountingPeriod(firstLineItem);
        if (AccountingPeriodStatus.CLOSE_STATES.contains(referenceAccountingPeriod.getStatus())) {
            throw new IllegalStateException("accounting date is in the closed/close in progress accounting period. ramp group id: " + rampGroupId);
        }

        // fetch accounting periods for given time period, sorted by start date
        Instant rampGroupStartDate = rampGroup
            .stream()
            .min(Comparator.comparing(TransactionLineItem::getStartDate))
            .map(TransactionLineItem::getStartDate)
            .orElseThrow();
        Instant rampGroupEndDate = rampGroup
            .stream()
            .max(Comparator.comparing(TransactionLineItem::getEndDate))
            .map(TransactionLineItem::getEndDate)
            .orElseThrow();
        List<AccountingPeriod> accountingPeriods = accountingPeriodService.generateAccountingPeriods(
            Period.between(rampGroupStartDate, rampGroupEndDate)
        );
        if (CollectionUtils.isEmpty(accountingPeriods)) {
            LOGGER.info("Recognition schedule cannot be created for ramp group {}: accounting periods missing", rampGroupId);
            return;
        }

        // prepare schedule
        RecognitionSchedule schedule = createRecognitionSchedule(
            firstLineItem,
            totalAmount,
            Period.between(rampGroupStartDate, rampGroupEndDate),
            rule,
            referenceAccountingPeriod
        );
        schedule.setTotalRevenue(totalAmount);
        schedule.setRecognizedRevenue(BigDecimal.ZERO);
        schedule.setDeferredRevenue(totalAmount);
        schedule.setSourceTransactionType(TransactionType.ORDER_RAMP_GROUP);
        schedule.setRampGroupId(UUID.fromString(rampGroupId));
        schedule.setScheduleId(recognitionScheduleIdGenerator.generate());
        schedule.setStartDate(rampGroupStartDate);
        schedule.setEndDate(rampGroupEndDate);
        schedule.setRuleId(rule.getRuleId());
        schedule.setPostedAccountingPeriodId(referenceAccountingPeriod.getAccountingPeriodId());
        schedule.setOrderId(firstLineItem.getTransactionId());

        // prepare transactions
        List<RecognitionTransaction> transactions =
            switch (rule.getRecognitionType()) {
                case OVER_TIME -> prepareOverTimeScheduleForRampGroup(rule, rampGroup, accountingPeriods, schedule, referenceAccountingPeriod);
                case POINT_IN_TIME -> preparePointInTimeSchedule(
                    rule,
                    Period.between(rampGroupStartDate, rampGroupEndDate),
                    accountingPeriods,
                    schedule,
                    referenceAccountingPeriod
                );
                case EVENT -> List.of(); // generate empty recognition schedule
            };
        transactions = roundingCorrectionForRevenueTransactions(schedule, transactions);

        // insert schedule and transactions
        insertScheduleAndTransactions(schedule, transactions);
        LOGGER.info("Recognition schedule successfully generated for ramp group {} chargeId {}", rampGroupId, chargeId);
    }

    private List<RecognitionTransaction> prepareOverTimeScheduleForRampGroup(
        RecognitionRule rule,
        List<TransactionLineItem> rampGroup,
        List<AccountingPeriod> accountingPeriods,
        RecognitionSchedule schedule,
        AccountingPeriod referenceAccountingPeriod
    ) {
        if (CollectionUtils.isEmpty(rampGroup)) {
            throw new ServiceFailureException("cannot prepare over time schedule for empty ramp group");
        }

        // Check if the ramp group is debook line items
        if (rampGroup.stream().allMatch(li -> li.getQuantity() < 0)) {
            return prepareDebookScheduleForRampGroup(rampGroup, schedule, accountingPeriods, referenceAccountingPeriod);
        }

        Map<Period, List<TransactionLineItem>> quantityGroups = groupRampLineItemsByQuantity(rampGroup);

        Map<String, RecognitionTransaction> transactionsMap = accountingPeriods
            .stream()
            .collect(
                Collectors.toMap(AccountingPeriod::getAccountingPeriodId, ap ->
                    createRecognitionTransaction(BigDecimal.ZERO, schedule, ap.getAccountingPeriodId())
                )
            );
        ProrationConfig.Mode prorationMode =
            switch (rule.getDistributionMethod()) {
                case DAYS -> ProrationConfig.Mode.EXACT_DAYS;
                case MONTHS_PARTIAL_PRORATED -> ProrationConfig.Mode.NORMALIZED;
                default -> throw new UnsupportedOperationException(
                    "%s distribution method is not supported for ramp group".formatted(rule.getDistributionMethod())
                );
            };
        for (var quantityGroup : quantityGroups.entrySet()) {
            BigDecimal quantityGroupAmount = quantityGroup
                .getValue()
                .stream()
                .map(TransactionLineItem::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            Map<String, BigDecimal> proratedAmounts = getProratedAmounts(
                quantityGroup.getKey(),
                accountingPeriods,
                quantityGroupAmount,
                prorationMode
            );
            proratedAmounts.forEach((apId, amount) -> {
                RecognitionTransaction transaction = transactionsMap.get(apId);
                transaction.setAmount(transaction.getAmount().add(amount));
            });
        }

        // Sort by accounting period start date
        List<RecognitionTransaction> transactions = revenueRecognitionGetService.sortTransactionsByAccountingPeriod(
            transactionsMap.values().stream().toList()
        );
        if (!rule.isCatchupRequired()) {
            return transactions;
        }

        Set<String> pastAccountingPeriodIds = accountingPeriods
            .stream()
            .filter(p -> p.getStartDate().isBefore(referenceAccountingPeriod.getStartDate()))
            .map(AccountingPeriod::getAccountingPeriodId)
            .collect(Collectors.toSet());

        // No past accounting periods, so no need for catch-up
        if (CollectionUtils.isEmpty(pastAccountingPeriodIds)) {
            return transactions;
        }

        return catchUpPastTransactions(transactions, schedule, referenceAccountingPeriod, pastAccountingPeriodIds);
    }

    private List<RecognitionTransaction> prepareDebookScheduleForRampGroup(
        List<TransactionLineItem> rampGroup,
        RecognitionSchedule schedule,
        List<AccountingPeriod> accountingPeriods,
        AccountingPeriod referenceAccountingPeriod
    ) {
        List<OrderLineItem> previousRampLineItems = getPreviousRampLineItemsForRampGroup(rampGroup);
        List<RecognitionTransaction> previousTransactions = new ArrayList<>();
        // Backwards compatibility for orders created before ramp group id was introduced
        if (previousRampLineItems.stream().anyMatch(li -> li.getRampGroupId() == null)) {
            for (OrderLineItem previousRampLineItem : previousRampLineItems) {
                RecognitionSchedule previousSchedule = revenueRecognitionGetService
                    .getRecognitionScheduleByOrderLineItemId(previousRampLineItem.getOrderLineId())
                    .orElseThrow(() ->
                        new ServiceFailureException(
                            "Previous recognition schedule not found for order line item %s".formatted(previousRampLineItem.getOrderLineId())
                        )
                    );
                previousTransactions.addAll(recognitionTransactionDAO.getRecognitionTransactionsByScheduleId(previousSchedule.getScheduleId()));
            }
        } else {
            String previousRampGroupId = previousRampLineItems.get(0).getRampGroupId().toString();
            RecognitionSchedule previousSchedule = getExistingRecognitionSchedule(TransactionType.ORDER_RAMP_GROUP, previousRampGroupId).orElseThrow(
                () -> new ServiceFailureException("Previous recognition schedule not found for ramp group %s".formatted(previousRampGroupId))
            );
            previousTransactions.addAll(recognitionTransactionDAO.getRecognitionTransactionsByScheduleId(previousSchedule.getScheduleId()));
        }

        return prepareDebookScheduleTransactionsForRampGroup(rampGroup, schedule, previousTransactions, accountingPeriods, referenceAccountingPeriod);
    }

    private List<OrderLineItem> getPreviousRampLineItemsForRampGroup(List<TransactionLineItem> rampGroup) {
        String orderLineItemId = rampGroup.get(0).getId();
        OrderLineItem orderLineItem = orderGetService
            .getOrderLineItemByOrderLineItemId(orderLineItemId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ORDER_LINE_ITEM, orderLineItemId));
        Order order = orderGetService.getOrderByOrderId(orderLineItem.getOrderId());
        Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscription.getSubscriptionId());

        int previousVersion = order.getSubscriptionTargetVersion() - 1;
        while (previousVersion > 0) {
            int previousVersionFinal = previousVersion;
            Optional<Order> previousOrder = orders.stream().filter(o -> o.getSubscriptionTargetVersion() == previousVersionFinal).findFirst();
            if (previousOrder.isEmpty()) {
                previousVersion--;
                continue;
            }
            List<OrderLineItem> previousRampLineItems = previousOrder
                .get()
                .getLineItemsNetEffect()
                .stream()
                .filter(
                    li ->
                        li.getIsRamp() &&
                        li.getSubscriptionChargeGroupId().equals(orderLineItem.getSubscriptionChargeGroupId()) &&
                        li.getQuantity() > 0
                )
                .toList();
            if (CollectionUtils.isNotEmpty(previousRampLineItems)) {
                return previousRampLineItems;
            }
            previousVersion--;
        }

        throw new ConflictingStateException("Could not find previous ramp line items for ramp group %s".formatted(rampGroup.get(0).getRampGroupId()));
    }

    private List<RecognitionTransaction> prepareDebookScheduleTransactionsForRampGroup(
        List<TransactionLineItem> rampGroup,
        RecognitionSchedule schedule,
        List<RecognitionTransaction> previousTransactions,
        List<AccountingPeriod> accountingPeriods,
        AccountingPeriod referenceAccountingPeriod
    ) {
        Set<String> openAccountingPeriodIds = accountingPeriods
            .stream()
            .filter(accountingPeriod -> accountingPeriod.getStatus() != AccountingPeriodStatus.CLOSED)
            .map(AccountingPeriod::getAccountingPeriodId)
            .collect(Collectors.toSet());
        // No open accounting periods, meaning this is a backdated order. Catch-up entire amount in current period.
        if (CollectionUtils.isEmpty(openAccountingPeriodIds)) {
            RecognitionTransaction transaction = createRecognitionTransaction(
                rampGroup.stream().map(TransactionLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add),
                schedule,
                referenceAccountingPeriod.getAccountingPeriodId()
            );
            return List.of(transaction);
        }

        List<RecognitionTransaction> openTransactions = revenueRecognitionGetService
            .sortTransactionsByAccountingPeriod(previousTransactions)
            .stream()
            .filter(t -> openAccountingPeriodIds.contains(t.getScheduledAccountingPeriodId()))
            .toList();
        List<RecognitionTransaction> negatedTransactions = openTransactions
            .stream()
            .map(t -> createRecognitionTransaction(t.getAmount().negate(), schedule, t.getScheduledAccountingPeriodId()))
            .toList();

        // Correction on first transaction
        BigDecimal rampGroupAmount = rampGroup.stream().map(TransactionLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal transactionTotal = negatedTransactions.stream().map(RecognitionTransaction::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal correctionAmount = rampGroupAmount.subtract(transactionTotal);
        if (correctionAmount.signum() != 0) {
            RecognitionTransaction firstTransaction = negatedTransactions.get(0);
            firstTransaction.setAmount(firstTransaction.getAmount().add(correctionAmount));
        }
        return negatedTransactions;
    }

    private boolean checkScheduleGenerationForLineItem(TransactionLineItem transactionLineItem) {
        if (Numbers.isZero(transactionLineItem.getAmount())) {
            LOGGER.warn(
                "skipping generation of revrec schedule for line item with 0 amount: type = {}, id = {}",
                transactionLineItem.getTransactionType(),
                transactionLineItem.getId()
            );
            return false;
        }
        return getAndValidateRecognitionRuleByChargeId(transactionLineItem.getChargeId())
            .map(r -> isRecognitionSourceMatchingTransactionType(r.getSource(), transactionLineItem.getTransactionType()))
            .orElse(false);
    }

    private boolean isRecognitionSourceMatchingTransactionType(RecognitionSource recognitionSource, TransactionType transactionType) {
        return switch (recognitionSource) {
            case INVOICE -> transactionType == TransactionType.INVOICE || transactionType == TransactionType.CREDIT_MEMO;
            case ORDER -> transactionType == TransactionType.ORDER || transactionType == TransactionType.ORDER_RAMP_GROUP;
        };
    }

    // Create Recognition Transactions
    // 1. obtain accounting periods where
    // accountingPeriod.startDate >= schedule.startDate
    // accountingPeriod.startDate < schedule.endDate
    // periodCount = number of account periods
    // 2. create one transaction per accounting period (for OVER_TIME)
    // for months_even:
    //    transaction.amount = schedule.totalRevenue / periodCount
    // for days:
    //    periodDays = days in one period
    //    totalDays = sum of all periodDays
    //    transaction.amount = schedule.totalRevenue * periodDays / totalDays
    // for months_prorated
    //    periodDays = days in one period
    //    totalDays = sum of all periodDays
    //    (first) transaction.amount = schedule.totalRevenue * periodDays / totalDays
    //    (last) transaction.amount = schedule.totalRevenue * periodDays / totalDays
    //    remainderAmount = totalRevenue - firstAmount - lastAmount
    //    (remaining) transaction.amount = remainderAmount / (periodCount - 2)

    // Todo - move this to Revenue Recognition Job to avoid concurrency issues related to schedule creation
    private void generateRecognitionSchedule(TransactionType sourceTransactionType, TransactionLineItem transactionLineItem) {
        if (getExistingRecognitionSchedule(sourceTransactionType, transactionLineItem.getId()).isPresent()) {
            LOGGER.warn(
                "Recognition schedule already created for source transaction type {} and item {}",
                sourceTransactionType,
                transactionLineItem.getId()
            );
            return;
        }

        // fetch recognition rule
        Optional<RecognitionRule> recognitionRuleOptional = getAndValidateRecognitionRuleByChargeId(transactionLineItem.getChargeId());
        if (recognitionRuleOptional.isEmpty()) {
            LOGGER.info(
                "Recognition schedule cannot be created for transaction type {} and item {} chargeId {} missing rule",
                sourceTransactionType,
                transactionLineItem.getId(),
                transactionLineItem.getChargeId()
            );
            return;
        }
        RecognitionRule rule = recognitionRuleOptional.get();

        // fetch reference accounting period
        AccountingPeriod referenceAccountingPeriod = getReferenceAccountingPeriod(transactionLineItem);
        if (AccountingPeriodStatus.CLOSE_STATES.contains(referenceAccountingPeriod.getStatus())) {
            throw new IllegalStateException(
                "accounting date is in the closed/close in progress accounting period. line item id: " + transactionLineItem.getId()
            );
        }

        // fetch accounting periods for given time period, sorted by start date
        List<AccountingPeriod> accountingPeriods = accountingPeriodService.generateAccountingPeriods(
            Period.between(transactionLineItem.getStartDate(), transactionLineItem.getEndDate())
        );
        if (CollectionUtils.isEmpty(accountingPeriods)) {
            LOGGER.info(
                "Recognition schedule cannot be created for transaction type {} and item {} accounting periods missing",
                sourceTransactionType,
                transactionLineItem.getId()
            );
            return;
        }

        // prepare schedule and transactions
        RecognitionSchedule schedule = createRecognitionSchedule(
            transactionLineItem,
            transactionLineItem.getAmount(),
            Period.between(transactionLineItem.getStartDate(), transactionLineItem.getEndDate()),
            rule,
            referenceAccountingPeriod
        );
        List<RecognitionTransaction> transactions = createRevenueRecognitionTransactions(
            transactionLineItem,
            rule,
            referenceAccountingPeriod,
            accountingPeriods,
            schedule
        );

        // insert schedule and transactions
        insertScheduleAndTransactions(schedule, transactions);
        LOGGER.info(
            "Recognition schedule successfully generated for transaction type {} and item {} chargeId {}",
            sourceTransactionType,
            transactionLineItem.getId(),
            transactionLineItem.getChargeId()
        );
    }

    private AccountingPeriod getReferenceAccountingPeriod(TransactionLineItem transactionLineItem) {
        Instant accountingDate;
        switch (transactionLineItem.getTransactionType()) {
            case CREDIT_MEMO -> {
                var creditMemo = creditMemoRetrievalService.getCreditMemoByLineItemId(UUID.fromString(transactionLineItem.getId()));
                accountingDate = creditMemo.getCreditMemoDate();
                return accountingPeriodService.getAccountingPeriodByInstant(accountingDate);
            }
            case INVOICE -> {
                var invoice = invoiceRetrievalService.getInvoiceById(UUID.fromString(transactionLineItem.getTransactionId()));
                accountingDate = invoice.getInvoiceDate();
                return accountingPeriodService.getAccountingPeriodByInstant(accountingDate);
            }
            case ORDER, ORDER_RAMP_GROUP -> {
                return getAccountingPeriodForOrderLineItem(transactionLineItem);
            }
            default -> throw new IllegalStateException("unsupported transaction type: " + transactionLineItem.getTransactionType());
        }
    }

    private AccountingPeriod getAccountingPeriodForOrderLineItem(TransactionLineItem transactionLineItem) {
        var order = orderGetService.getOrderByOrderId(transactionLineItem.getTransactionId());

        // TODO: order execution date is the default for now until a new canonical transaction date is introduced
        AccountingPeriod accountingPeriodExecutedIn = accountingPeriodService.getAccountingPeriodByInstant(order.getExecutedOn());
        // if executed date is in closed accounting period, use the open accounting period
        AccountingPeriod currentAccountingPeriod = accountingPeriodService
            .getCurrentAccountingPeriod(transactionLineItem.getEntityId())
            .orElseThrow(() -> new IllegalStateException("unable to find current accounting period"));
        if (AccountingPeriodStatus.CLOSE_STATES.contains(accountingPeriodExecutedIn.getStatus())) {
            return currentAccountingPeriod;
        }
        return accountingPeriodExecutedIn;
    }

    private Optional<RecognitionSchedule> getExistingRecognitionSchedule(TransactionType sourceTransactionType, String lineItemId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return revenueRecognitionGetService.getRecognitionScheduleForLineItemInTransaction(dslContext, tenantId, sourceTransactionType, lineItemId);
    }

    private List<RecognitionTransaction> createRevenueRecognitionTransactions(
        TransactionLineItem transactionLineItem,
        RecognitionRule rule,
        AccountingPeriod referenceAccountingPeriod,
        List<AccountingPeriod> accountingPeriods,
        RecognitionSchedule schedule
    ) {
        // Update Schedule and Generate Recognition Transactions
        List<RecognitionTransaction> transactions =
            switch (rule.getRecognitionType()) {
                case OVER_TIME -> prepareOverTimeSchedule(rule, transactionLineItem, accountingPeriods, schedule, referenceAccountingPeriod);
                case POINT_IN_TIME -> preparePointInTimeSchedule(
                    rule,
                    Period.between(transactionLineItem.getStartDate(), transactionLineItem.getEndDate()),
                    accountingPeriods,
                    schedule,
                    referenceAccountingPeriod
                );
                case EVENT -> List.of(); // generate empty recognition schedule
            };

        return roundingCorrectionForRevenueTransactions(schedule, transactions);
    }

    private Optional<RecognitionRule> getAndValidateRecognitionRuleByChargeId(String chargeId) {
        Charge charge = productCatalogGetService.getChargeByChargeId(chargeId);
        if (StringUtils.isBlank(charge.getRecognitionRuleId())) {
            return Optional.empty();
        }

        return recognitionRuleDAO.getRecognitionRuleByRuleId(charge.getRecognitionRuleId());
    }

    private RecognitionSchedule createRecognitionSchedule(
        TransactionLineItem transactionLineItem,
        BigDecimal totalRevenue,
        Period schedulePeriod,
        RecognitionRule rule,
        AccountingPeriod referenceAccountingPeriod
    ) {
        if (Numbers.isZero(totalRevenue)) {
            LOGGER.error("cannot create revrec schedule for line item with zero amount {}", transactionLineItem);
            throw new IllegalStateException("cannot create revrec schedule for line item with zero amount");
        }

        if (transactionLineItem.getTransactionType() == TransactionType.CREDIT_MEMO) {
            totalRevenue = totalRevenue.negate();
        }

        RecognitionSchedule schedule = new RecognitionSchedule();
        schedule.setEntityId(transactionLineItem.getEntityId());
        schedule.setTotalRevenue(totalRevenue);
        schedule.setRecognizedRevenue(BigDecimal.ZERO);
        schedule.setDeferredRevenue(totalRevenue);
        schedule.setSourceTransactionType(transactionLineItem.getTransactionType());
        schedule.setScheduleId(recognitionScheduleIdGenerator.generate());
        schedule.setStartDate(schedulePeriod.getStart());
        schedule.setEndDate(schedulePeriod.getEnd());
        schedule.setRuleId(rule.getRuleId());
        schedule.setPostedAccountingPeriodId(referenceAccountingPeriod.getAccountingPeriodId());

        switch (transactionLineItem.getTransactionType()) {
            case CREDIT_MEMO -> schedule.setCreditMemoLineItemId(UUID.fromString(transactionLineItem.getId()));
            case INVOICE -> schedule.setInvoiceLineItemId(UUID.fromString(transactionLineItem.getId()));
            case ORDER -> {
                schedule.setOrderLineItemId(transactionLineItem.getId());
                schedule.setOrderId(transactionLineItem.getTransactionId());
            }
            case ORDER_RAMP_GROUP -> {
                schedule.setRampGroupId(UUID.fromString(transactionLineItem.getRampGroupId()));
                schedule.setOrderId(transactionLineItem.getTransactionId());
            }
        }

        return schedule;
    }

    private List<RecognitionTransaction> preparePointInTimeSchedule(
        RecognitionRule rule,
        Period recognitionPeriod,
        List<AccountingPeriod> accountingPeriods,
        RecognitionSchedule schedule,
        AccountingPeriod referenceAccountingPeriod
    ) {
        Instant recognitionDate;
        AccountingPeriod recognitionAccountingPeriod;
        if (rule.getRecognitionDateAlignment() == RecognitionDateAlignment.INVOICE_START_DATE) {
            recognitionDate = recognitionPeriod.getStart();
            recognitionAccountingPeriod = accountingPeriods.get(0);
        } else if (rule.getRecognitionDateAlignment() == RecognitionDateAlignment.INVOICE_END_DATE) {
            recognitionDate = recognitionPeriod.getEnd();
            recognitionAccountingPeriod = accountingPeriods.get(accountingPeriods.size() - 1);
        } else {
            throw new IllegalStateException("unknown recognition date alignment: " + rule.getRecognitionDateAlignment().name());
        }

        // Catch up to current period if necessary
        Instant referenceAccountingPeriodStartDate = referenceAccountingPeriod.getStartDate();
        if (recognitionDate.isBefore(referenceAccountingPeriodStartDate)) {
            recognitionAccountingPeriod = referenceAccountingPeriod;
        }

        RecognitionTransaction transaction = createRecognitionTransaction(
            schedule.getTotalRevenue(),
            schedule,
            recognitionAccountingPeriod.getAccountingPeriodId()
        );
        return List.of(transaction);
    }

    List<RecognitionTransaction> prepareOverTimeSchedule(
        RecognitionRule rule,
        TransactionLineItem transactionLineItem,
        List<AccountingPeriod> accountingPeriods,
        RecognitionSchedule schedule,
        AccountingPeriod referenceAccountingPeriod
    ) {
        List<RecognitionTransaction> transactions =
            switch (rule.getDistributionMethod()) {
                case MONTHS_EVEN -> prepareMonthsEvenSchedule(accountingPeriods, schedule);
                case DAYS -> prepareProratedSchedule(
                    Period.between(transactionLineItem.getStartDate(), transactionLineItem.getEndDate()),
                    accountingPeriods,
                    schedule,
                    ProrationConfig.Mode.EXACT_DAYS
                );
                case MONTHS_PARTIAL_PRORATED -> prepareProratedSchedule(
                    Period.between(transactionLineItem.getStartDate(), transactionLineItem.getEndDate()),
                    accountingPeriods,
                    schedule,
                    ProrationConfig.Mode.NORMALIZED
                );
            };

        prepareAmendmentDebookSchedule(transactions, schedule, transactionLineItem);

        Set<String> pastAccountingPeriodIds = accountingPeriods
            .stream()
            .filter(p -> p.getStartDate().isBefore(referenceAccountingPeriod.getStartDate()))
            .map(AccountingPeriod::getAccountingPeriodId)
            .collect(Collectors.toSet());

        // No past accounting periods, so no need for catch-up
        if (CollectionUtils.isEmpty(pastAccountingPeriodIds)) {
            return transactions;
        }

        return catchUpPastTransactions(transactions, schedule, referenceAccountingPeriod, pastAccountingPeriodIds);
    }

    private void prepareAmendmentDebookSchedule(
        List<RecognitionTransaction> transactions,
        RecognitionSchedule schedule,
        TransactionLineItem transactionLineItem
    ) {
        // assuming this is a debook entry, find the corresponding original entry
        List<RecognitionTransaction> originalRevenueTransactions = getOriginalRevenueTransactions(transactionLineItem);
        if (CollectionUtils.isEmpty(originalRevenueTransactions)) {
            return;
        }

        // map original revenue transactions by accounting period
        Map<String, RecognitionTransaction> originalTransactionsByAccountingPeriodId = originalRevenueTransactions
            .stream()
            .collect(Collectors.toMap(RecognitionTransaction::getScheduledAccountingPeriodId, Function.identity()));

        // make all debook revenue transactions, reverse of original revenue transactions
        transactions.forEach(transaction -> {
            RecognitionTransaction originalRevenueTransaction = originalTransactionsByAccountingPeriodId.get(
                transaction.getScheduledAccountingPeriodId()
            );
            if (!Objects.isNull(originalRevenueTransaction)) {
                transaction.setAmount(originalRevenueTransaction.getAmount().negate());
            }
        });

        // find the offset and adjust in the first revenue transaction
        adjustFirstRevenueTransaction(transactions, schedule);
    }

    private List<RecognitionTransaction> getOriginalRevenueTransactions(TransactionLineItem transactionLineItem) {
        switch (transactionLineItem.getTransactionType()) {
            case INVOICE -> {
                Optional<InvoiceItem> originalInvoiceItemForDebook = getOriginalInvoiceItemForInvoiceTransaction(transactionLineItem);
                if (originalInvoiceItemForDebook.isEmpty()) {
                    return List.of();
                }
                return revenueRecognitionGetService.getRecognitionTransactionsByInvoiceItem(
                    UUID.fromString(originalInvoiceItemForDebook.get().getId().id())
                );
            }
            case CREDIT_MEMO -> {
                Optional<InvoiceItem> originalInvoiceItemForCreditMemo = getOriginalInvoiceItemForCreditMemoTransaction(transactionLineItem);
                if (originalInvoiceItemForCreditMemo.isEmpty()) {
                    return List.of();
                }
                return revenueRecognitionGetService.getRecognitionTransactionsByInvoiceItem(
                    UUID.fromString(originalInvoiceItemForCreditMemo.get().getId().id())
                );
            }
            case ORDER, ORDER_RAMP_GROUP -> {
                Optional<OrderLineItem> originalOrderLineItem = getOriginalOrderLineItemForOrderTransaction(transactionLineItem);
                if (originalOrderLineItem.isEmpty()) {
                    return List.of();
                }
                return revenueRecognitionGetService.getRecognitionTransactionsByOrderLine(originalOrderLineItem.get().getOrderLineId());
            }
            default -> throw new IllegalStateException("unexpected transaction type: " + transactionLineItem.getTransactionType());
        }
    }

    // 1. get the debook order line item for given debook invoice line item
    // 2. get the original order line item for given debook order line item
    // 3. get original invoice line item for the original order line item for the period matching debook invoice line item
    private Optional<InvoiceItem> getOriginalInvoiceItemForInvoiceTransaction(TransactionLineItem transactionLineItem) {
        // Note: For usage charges, the invoice line item is not associated with an order line item.
        Optional<OrderLineItem> debookOrderLineItemOptional = orderGetService.getOrderLineItemByOrderLineItemId(
            transactionLineItem.getOrderLineItemId()
        );
        return debookOrderLineItemOptional.flatMap(lineItem ->
            getOriginalOrderLineItemForDebookOrderLineItem(lineItem).flatMap(orderLineItem ->
                invoiceService.getPostedInvoiceItemByOrderLineIdAndDebookPeriod(
                    orderLineItem.getOrderLineId(),
                    Period.between(transactionLineItem.getStartDate(), transactionLineItem.getEndDate())
                )
            )
        );
    }

    private Optional<OrderLineItem> getOriginalOrderLineItemForOrderTransaction(TransactionLineItem transactionLineItem) {
        Optional<OrderLineItem> debookOrderLineItemOptional = orderGetService.getOrderLineItemByOrderLineItemId(transactionLineItem.getId());
        if (debookOrderLineItemOptional.isEmpty()) {
            return Optional.empty();
        }
        return getOriginalOrderLineItemForDebookOrderLineItem(debookOrderLineItemOptional.get());
    }

    private Optional<OrderLineItem> getOriginalOrderLineItemForDebookOrderLineItem(OrderLineItem debookOrderLineItem) {
        if (debookOrderLineItem.getSubscriptionChargeId() == null) {
            return Optional.empty();
        }

        return subscriptionGetService
            .getSubscriptionChargeById(debookOrderLineItem.getSubscriptionChargeId())
            .getOrderLines()
            .stream()
            .map(orderGetService::getOrderLineItemByOrderLineItemId)
            .flatMap(Optional::stream)
            .filter(orderLineItem -> orderLineItem.getQuantity() == Math.negateExact(debookOrderLineItem.getQuantity()))
            .filter(orderLineItem -> !orderLineItem.getEffectiveDate().isAfter(debookOrderLineItem.getEffectiveDate()))
            .filter(orderLineItem -> !orderLineItem.getEndDate().isBefore(debookOrderLineItem.getEndDate()))
            .findFirst();
    }

    private Optional<InvoiceItem> getOriginalInvoiceItemForCreditMemoTransaction(TransactionLineItem transactionLineItem) {
        // get credit memo
        var creditMemo = creditMemoRetrievalService.getCreditMemoByLineItemId(UUID.fromString(transactionLineItem.getId()));

        // get invoice
        if (StringUtils.isBlank(creditMemo.getCreatedFrom())) {
            return Optional.empty();
        }
        Invoice.Number invoiceNumber = new Invoice.Number(creditMemo.getCreatedFrom());
        Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);

        // get invoice item corresponding to this credit memo line item
        var negatedAmount = transactionLineItem.getAmount().negate();
        var optionalInvoiceItem = invoice
            .getInvoiceItems()
            .stream()
            .filter(
                invoiceItem ->
                    invoiceItem.getChargeId().equals(transactionLineItem.getChargeId()) &&
                    Numbers.equals(invoiceItem.getAmount(), negatedAmount) &&
                    invoiceItem.getPeriodStartDate().equals(transactionLineItem.getStartDate()) &&
                    invoiceItem.getPeriodEndDate().equals(transactionLineItem.getEndDate())
            )
            .findFirst();
        if (optionalInvoiceItem.isEmpty()) {
            return Optional.empty();
        }

        // get the "original" invoice line item for this
        var invoiceTransactionLineItem = transactionLineItemMapper.fromInvoiceItem(optionalInvoiceItem.get());
        return getOriginalInvoiceItemForInvoiceTransaction(invoiceTransactionLineItem);
    }

    private void adjustFirstRevenueTransaction(List<RecognitionTransaction> transactions, RecognitionSchedule schedule) {
        BigDecimal newTotalRevenue = transactions.stream().map(RecognitionTransaction::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal offsetAmount = schedule.getTotalRevenue().subtract(newTotalRevenue);
        RecognitionTransaction firstRevenueTransaction = transactions
            .stream()
            .findFirst()
            .orElseThrow(() -> new IllegalStateException("unexpected empty list of revenue transactions"));
        BigDecimal adjustedFirstAmount = firstRevenueTransaction.getAmount().add(offsetAmount);
        firstRevenueTransaction.setAmount(adjustedFirstAmount);
    }

    private List<RecognitionTransaction> catchUpPastTransactions(
        List<RecognitionTransaction> transactions,
        RecognitionSchedule schedule,
        AccountingPeriod referenceAccountingPeriod,
        Set<String> pastAccountingPeriodIds
    ) {
        // Calculate catchup amount
        Map<Boolean, List<RecognitionTransaction>> partitionedTransactions = transactions
            .stream()
            .collect(Collectors.partitioningBy(t -> pastAccountingPeriodIds.contains(t.getScheduledAccountingPeriodId())));
        List<RecognitionTransaction> pastTransactions = partitionedTransactions.get(true);
        BigDecimal catchUpAmount = pastTransactions
            .stream()
            .reduce(BigDecimal.ZERO, (sum, transaction) -> sum.add(transaction.getAmount()), BigDecimal::add);

        // Distribute catchup amount to relevant transactions
        List<RecognitionTransaction> catchUpTransactions = partitionedTransactions.get(false);
        if (CollectionUtils.isEmpty(catchUpTransactions)) {
            RecognitionTransaction catchUpTransaction = createRecognitionTransaction(
                catchUpAmount,
                schedule,
                referenceAccountingPeriod.getAccountingPeriodId()
            );
            return List.of(catchUpTransaction);
        }

        RecognitionTransaction firstTransaction = catchUpTransactions.get(0);
        firstTransaction.setAmount(firstTransaction.getAmount().add(catchUpAmount));
        return catchUpTransactions;
    }

    private RecognitionTransaction createRecognitionTransaction(BigDecimal amount, RecognitionSchedule schedule, String accountingPeriodId) {
        RecognitionTransaction transaction = new RecognitionTransaction();
        transaction.setEntityId(schedule.getEntityId());
        transaction.setAmount(amount);
        transaction.setRecognitionTransactionId(recognitionTransactionIdGenerator.generate());
        transaction.setScheduleId(schedule.getScheduleId());
        transaction.setScheduledAccountingPeriodId(accountingPeriodId);
        transaction.setIsRecognized(false);
        return transaction;
    }

    // Note: Remainder is added to last transaction, as opposed to evenly distributing it to all transactions
    private void monthsEvenDistribution(BigDecimal amount, List<RecognitionTransaction> transactions) {
        BigDecimal divisor = BigDecimal.valueOf(transactions.size());
        BigDecimal amountPerTransaction = amount.divide(divisor, DEFAULT_CURRENCY_SCALE, RoundingMode.DOWN);
        for (RecognitionTransaction transaction : transactions) {
            transaction.setAmount(transaction.getAmount().add(amountPerTransaction));
        }
        BigDecimal remainder = amount.subtract(amountPerTransaction.multiply(divisor));
        RecognitionTransaction lastTransaction = transactions.get(transactions.size() - 1);
        lastTransaction.setAmount(lastTransaction.getAmount().add(remainder));
    }

    private List<RecognitionTransaction> prepareMonthsEvenSchedule(List<AccountingPeriod> accountingPeriods, RecognitionSchedule schedule) {
        List<RecognitionTransaction> transactions = new ArrayList<>();
        for (AccountingPeriod accountingPeriod : accountingPeriods) {
            RecognitionTransaction transaction = createRecognitionTransaction(BigDecimal.ZERO, schedule, accountingPeriod.getAccountingPeriodId());
            transactions.add(transaction);
        }

        monthsEvenDistribution(schedule.getTotalRevenue(), transactions);
        return transactions;
    }

    private Map<String, BigDecimal> getProratedAmounts(
        Period prorationPeriod,
        List<AccountingPeriod> accountingPeriods,
        BigDecimal amount,
        ProrationConfig.Mode prorationMode
    ) {
        ProrationConfig prorationConfig = new ProrationConfig(ProrationConfig.Scheme.CALENDAR_DAYS, prorationMode);
        TimeZone timeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();

        return accountingPeriods
            .stream()
            .collect(
                Collectors.toMap(AccountingPeriod::getAccountingPeriodId, ap ->
                    calculateProratedAmountForAccountingPeriod(ap, prorationPeriod, prorationConfig, timeZone, amount)
                )
            );
    }

    private BigDecimal calculateProratedAmountForAccountingPeriod(
        AccountingPeriod accountingPeriod,
        Period prorationPeriod,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        BigDecimal amount
    ) {
        BigDecimal prorationRatio = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            Period.between(accountingPeriod.getStartDate(), accountingPeriod.getEndDate()),
            prorationConfig,
            timeZone,
            Optional.empty()
        );
        return prorationRatio.multiply(amount);
    }

    private List<RecognitionTransaction> prepareProratedSchedule(
        Period recognitionPeriod,
        List<AccountingPeriod> accountingPeriods,
        RecognitionSchedule schedule,
        ProrationConfig.Mode prorationMode
    ) {
        Map<String, BigDecimal> proratedAmounts = getProratedAmounts(recognitionPeriod, accountingPeriods, schedule.getTotalRevenue(), prorationMode);
        return accountingPeriods
            .stream()
            .map(ap -> createRecognitionTransaction(proratedAmounts.get(ap.getAccountingPeriodId()), schedule, ap.getAccountingPeriodId()))
            .toList();
    }

    // match up all transaction amounts to schedule's total revenue amount
    // apply the adjustment to last transaction
    private List<RecognitionTransaction> roundingCorrectionForRevenueTransactions(
        RecognitionSchedule schedule,
        List<RecognitionTransaction> transactions
    ) {
        if (transactions.isEmpty()) {
            return transactions;
        }

        // Sort transactions by accounting period start date
        List<RecognitionTransaction> sortedTransactions = revenueRecognitionGetService.sortTransactionsByAccountingPeriod(transactions);

        // Set all transactions to rounding upto 2 digits only
        sortedTransactions.forEach(tx -> tx.setAmount(tx.getAmount().setScale(DEFAULT_CURRENCY_SCALE, RoundingMode.HALF_UP)));

        // Add up all transaction amounts
        BigDecimal sumOfTransactions = sortedTransactions.stream().map(RecognitionTransaction::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // Calculate excess of sum as compared to schedule amount
        BigDecimal surplus = sumOfTransactions.subtract(schedule.getTotalRevenue());

        // Deduct the excess from the last transaction
        RecognitionTransaction lastTransaction = sortedTransactions.get(sortedTransactions.size() - 1);
        BigDecimal adjustedAmount = lastTransaction.getAmount().subtract(surplus);
        lastTransaction.setAmount(adjustedAmount);

        return sortedTransactions;
    }

    public RecognitionSchedule insertScheduleAndTransactions(RecognitionSchedule schedule, List<RecognitionTransaction> transactions) {
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();
        return tenantDslContext.transactionResult(configuration -> {
            DSLContext transactionContext = DSL.using(configuration);
            RecognitionSchedule savedSchedule = recognitionScheduleDAO.createRecognitionScheduleInTransaction(transactionContext, tenantId, schedule);
            for (RecognitionTransaction transaction : transactions) {
                recognitionTransactionDAO.insertRecognitionTransactionInTransaction(transactionContext, tenantId, transaction);
            }
            return savedSchedule;
        });
    }

    public void recognizeRevenueAsync(String accountingPeriodId) {
        Optional<TenantJob> activeJobOptional = tenantJobGetService.getActiveJobByType(TenantJobType.RECOGNIZE_REVENUE);

        // TODO: We may be able to remove this since we added a partition key to this job - they will be processed
        // sequentially in the order they are submitted
        if (activeJobOptional.isPresent()) {
            TenantJob activeJob = activeJobOptional.get();
            String message = String.format(
                "Revenue recognition already in progress for accounting period: %s and in status %s",
                accountingPeriodId,
                activeJob.getStatus()
            );
            throw new InvalidInputException(message);
        }

        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(accountingPeriodId);

        tenantJobDispatcherService.dispatch(
            ImmutableTenantJob.builder()
                .module(TenantJobModule.REVENUE_RECOGNITION)
                .jobType(TenantJobType.RECOGNIZE_REVENUE)
                .objectModel(TenantJobObjectModel.ACCOUNTING_PERIOD)
                .objectId(accountingPeriodId)
                .entityIds(Set.of(accountingPeriod.getEntityId()))
                .partitionKey(String.format("%s-%s", tenantIdProvider.provideTenantIdString(), TenantJobType.RECOGNIZE_REVENUE.name()))
                .build()
        );
    }

    public void recognizeRevenue(String accountingPeriodId) {
        AccountingPeriod currentAccountingPeriod = accountingPeriodService
            .getCurrentAccountingPeriod()
            .orElseThrow(() -> new IllegalStateException("unable to find open accounting period"));

        String currentAccountingPeriodId = currentAccountingPeriod.getAccountingPeriodId();
        if (!currentAccountingPeriodId.equals(accountingPeriodId)) {
            throw new InvalidInputException(
                String.format("Current accounting period is %s but provided accounting period is %s", currentAccountingPeriodId, accountingPeriodId)
            );
        }

        recognizeRevenue(currentAccountingPeriod);
    }

    // get transactions for open period
    // update all of these transactions to recognized = true
    // and update corresponding schedules for deferred vs recognized amounts
    private void recognizeRevenue(AccountingPeriod currentAccountingPeriod) {
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        // database transaction to update recognition transactions and schedules
        tenantDSLContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            // update transactions falling under open accounting period
            List<RecognitionTransaction> transactions = recognitionTransactionDAO.updateRecognitionTransactions(dslContext, currentAccountingPeriod);
            // update the schedule objects for deferred and recognized revenues
            List<RecognitionSchedule> updatedSchedules = prepareRecognizeRevenueScheduleUpdates(transactions);
            // commit the schedule object changes to database
            recognitionScheduleDAO.updateRecognitionSchedules(dslContext, updatedSchedules);

            // publish recognized revenue events
            publishRecognizedRevenueEvents(configuration, transactions, updatedSchedules);
        });
    }

    private void publishRecognizedRevenueEvents(
        Configuration configuration,
        List<RecognitionTransaction> transactions,
        List<RecognitionSchedule> schedules
    ) {
        if (CollectionUtils.isEmpty(transactions)) {
            return;
        }

        Map<String, RecognitionSchedule> scheduleById = schedules
            .stream()
            .collect(Collectors.toMap(RecognitionSchedule::getScheduleId, Function.identity()));
        EntityCache<String, OrderStub> orderStubEntityCache = EntityCache.of(orderId ->
            orderGetService
                .getOrderStub(orderId)
                .orElseThrow(() -> new ServiceFailureException(String.format("Order with id %s not found during revenue recognition", orderId)))
        );
        transactions.forEach(transaction -> {
            String transactionId = transaction.getRecognitionTransactionId();
            String scheduleId = transaction.getScheduleId();
            RecognitionSchedule recognitionSchedule = scheduleById.computeIfAbsent(scheduleId, key -> {
                throw new ServiceFailureException(
                    String.format("schedule with id %s not found for rev-rec transaction %s", scheduleId, transactionId)
                );
            });
            resolveSourceTypeAndPublishEvent(configuration, transaction, recognitionSchedule, orderStubEntityCache);
        });
    }

    private void resolveSourceTypeAndPublishEvent(
        Configuration configuration,
        RecognitionTransaction transaction,
        RecognitionSchedule recognitionSchedule,
        EntityCache<String, OrderStub> orderStubEntityCache
    ) {
        String transactionId = transaction.getRecognitionTransactionId();
        String scheduleId = recognitionSchedule.getScheduleId();
        String entityId = recognitionSchedule.getEntityId();
        switch (recognitionSchedule.getSourceTransactionType()) {
            case INVOICE -> {
                UUID invoiceLineItemId = recognitionSchedule.getInvoiceLineItemId();
                InvoiceItem invoiceItem = invoiceService
                    .getInvoiceItemById(invoiceLineItemId)
                    .orElseThrow(() ->
                        new ServiceFailureException(
                            String.format("invoice line item with id %s not found for rev-rec transaction schedule %s", invoiceLineItemId, scheduleId)
                        )
                    );
                OrderStub stub = orderStubEntityCache.get(invoiceItem.getOrderId());
                publishEvent(configuration, entityId, transactionId, scheduleId, stub.getAccountId());
            }
            case CREDIT_MEMO -> {
                UUID cmLineItemId = recognitionSchedule.getCreditMemoLineItemId();
                CreditMemo creditMemo = creditMemoRetrievalService.getCreditMemoByLineItemId(cmLineItemId);
                publishEvent(configuration, entityId, transactionId, scheduleId, creditMemo.getAccountId());
            }
            case ORDER, ORDER_RAMP_GROUP -> {
                OrderStub stub = orderStubEntityCache.get(recognitionSchedule.getOrderId());
                publishEvent(configuration, entityId, transactionId, scheduleId, stub.getAccountId());
            }
            default -> throw new ServiceFailureException(
                "cannot publish revenue recognition event for unknown source transaction: " + recognitionSchedule.getSourceTransactionType()
            );
        }
    }

    private void publishEvent(Configuration configuration, String entityId, String transactionId, String scheduleId, String accountId) {
        try {
            RecognitionEventPayload eventPayload = RecognitionEventPayload.builder()
                .transactionId(transactionId)
                .scheduleId(scheduleId)
                .accountId(accountId)
                .build();
            byte[] eventPayloadBytes = OBJECT_MAPPER.writeValueAsBytes(eventPayload);
            eventPublishingService.publishEventInTransaction(
                configuration,
                EventType.REVENUE_RECOGNIZED,
                tenantIdProvider.provideTenantIdString(),
                entityId,
                accountId,
                eventPayloadBytes
            );
        } catch (JsonProcessingException ex) {
            String message = String.format("Error publishing revenue recognition event with transaction %s schedule %s", transactionId, scheduleId);
            throw new ServiceFailureException(message, ex);
        }
    }

    private List<RecognitionSchedule> prepareRecognizeRevenueScheduleUpdates(List<RecognitionTransaction> transactions) {
        // fetch all schedules in bulk
        var scheduleIds = transactions.stream().map(RecognitionTransaction::getScheduleId).collect(Collectors.toSet());
        List<RecognitionSchedule> schedules = recognitionScheduleDAO.getRecognitionSchedulesByScheduleIds(scheduleIds);

        // prepare map of schedules by id
        Map<String, RecognitionSchedule> scheduleMapById = new HashMap<>();
        schedules.forEach(schedule -> scheduleMapById.put(schedule.getScheduleId(), schedule));

        // update recognized and deferred revenues on schedule objects
        // add up recognized revenue by transaction amount
        // subtract deferred revenue by transaction amount
        for (RecognitionTransaction transaction : transactions) {
            RecognitionSchedule schedule = scheduleMapById.get(transaction.getScheduleId());
            schedule.setRecognizedRevenue(schedule.getRecognizedRevenue().add(transaction.getAmount()));
            schedule.setDeferredRevenue(schedule.getDeferredRevenue().subtract(transaction.getAmount()));
        }

        // return list of schedules with updated deferred and recognized revenues
        return new ArrayList<>(scheduleMapById.values());
    }

    public void purgeAllRevenueTransactionsAndSchedules(Configuration configuration) {
        DSLContext dslContext = DSL.using(configuration);
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        recognitionTransactionDAO.purgeAllRevenueTransactionsForEntity(dslContext, entityId);
        recognitionScheduleDAO.purgeAllRevenueSchedulesForEntity(dslContext, entityId);
    }
}
