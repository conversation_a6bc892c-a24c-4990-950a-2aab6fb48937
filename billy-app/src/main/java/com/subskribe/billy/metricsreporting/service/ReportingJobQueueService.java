package com.subskribe.billy.metricsreporting.service;

import static com.subskribe.billy.invoice.model.InvoiceStatus.PAID;
import static com.subskribe.billy.invoice.model.InvoiceStatus.POSTED;
import static com.subskribe.billy.invoice.model.InvoiceStatus.VOIDED;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.metricsreporting.db.ReportingJobQueueDAO;
import com.subskribe.billy.metricsreporting.model.ReportingJobCategory;
import com.subskribe.billy.metricsreporting.model.ReportingJobQueueEntry;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.account.AccountMapper;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.resources.shared.PaginationResponseJson;
import com.subskribe.billy.shared.enums.AccountType;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class ReportingJobQueueService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReportingJobQueueService.class);
    private final ReportingJobQueueDAO reportingJobQueueDAO;
    private final AccountGetService accountGetService;
    private final OrderGetService orderGetService;
    private final SubscriptionGetService subscriptionGetService;
    private final ProductCatalogGetService productCatalogGetService;
    private final FeatureService featureService;
    private final AccountMapper accountMapper;
    private static final int PAGINATION_LIMIT = 100;

    @Inject
    public ReportingJobQueueService(
        ReportingJobQueueDAO reportingJobQueueDAO,
        AccountGetService accountGetService,
        OrderGetService orderGetService,
        SubscriptionGetService subscriptionGetService,
        ProductCatalogGetService productCatalogGetService,
        FeatureService featureService
    ) {
        this.reportingJobQueueDAO = reportingJobQueueDAO;
        this.accountGetService = accountGetService;
        this.orderGetService = orderGetService;
        this.subscriptionGetService = subscriptionGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.featureService = featureService;
        accountMapper = Mappers.getMapper(AccountMapper.class);
    }

    public static void customSortOrders(List<Order> orders) {
        orders.sort((order1, order2) -> {
            // if the orderType is the same, sort based on the start dates
            // if the order start dates are the same, then the last resort is to sort by order executedOn date
            if (order1.getOrderType() == order2.getOrderType()) {
                int compareResult = order1.getStartDate().compareTo(order2.getStartDate());
                if (compareResult != 0) {
                    return compareResult;
                }

                return order1.getExecutedOn().compareTo(order2.getExecutedOn());
            }

            // if the orderTypes are different, then sort by New (or Renewal), Amendment and Cancel order
            return (orderTypeSortOrder(order1.getOrderType()) < orderTypeSortOrder(order2.getOrderType()) ? -1 : 1);
        });
    }

    private static int orderTypeSortOrder(OrderType orderType) {
        int result;
        switch (orderType) {
            case NEW -> result = 1;
            case RENEWAL -> result = 2;
            case RESTRUCTURE -> result = 3;
            case AMENDMENT -> result = 4;
            case CANCEL -> result = 5;
            default -> throw new ServiceFailureException("Unknown order type found: " + orderType.name());
        }

        return result;
    }

    public List<Account> bulkSyncTenant(PaginationQueryParams params, String tenantId) {
        var accounts = accountGetService.getAccounts(AccountType.ALL, params, tenantId);
        for (var account : accounts) {
            Set<String> subscriptionIds = subscriptionGetService.getAllSubscriptionsInAccountOrderedByStartDateAsc(account.getAccountId());
            for (String subscriptionId : subscriptionIds) {
                addArrJobsForSubscription(subscriptionId);
            }
        }
        return accounts;
    }

    public void addArrJobsForAccount(String accountId) {
        Account account = accountGetService.getAccount(accountId);
        Set<String> subscriptionIds = subscriptionGetService.getAllSubscriptionsInAccountOrderedByStartDateAsc(account.getAccountId());
        for (String subscriptionId : subscriptionIds) {
            addArrJobsForSubscription(subscriptionId);
        }
    }

    public void addArrJobsForSubscription(String subscriptionId) {
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);
        if (CollectionUtils.isEmpty(orders)) {
            // bad data in chainguard where orphaned subscription left over causing sorting to throw
            LOGGER.warn("No executed orders found for subscriptionId: {}", subscriptionId);
            return;
        }

        customSortOrders(orders);

        // delete the old data associated with the orders
        for (var order : orders) {
            ReportingJobQueueEntry entry = new ReportingJobQueueEntry(order.getTenantId(), ReportingJobCategory.DELETE_ORDER_ARR);
            entry.setOrderId(order.getOrderId());
            reportingJobQueueDAO.addJob(entry);
        }

        for (var order : orders) {
            addOrderLineArrJobs(order);
        }
    }

    public void addOrderLineArrJobs(Order order) {
        for (var lineItem : order.getLineItemsNetEffect()) {
            var entry = new ReportingJobQueueEntry(order.getTenantId(), ReportingJobCategory.ORDER_LINE_ARR);
            entry.setOrderId(order.getOrderId());
            entry.setAccountId(order.getAccountId());
            entry.setOrderLineItemId(lineItem.getOrderLineId());

            addReportingJobUnit(entry);
        }

        if (order.getOrderType() == OrderType.RENEWAL) {
            ReportingJobQueueEntry entry = new ReportingJobQueueEntry(order.getTenantId(), ReportingJobCategory.MISSED_RENEWAL_LINES);
            entry.setOrderId(order.getOrderId());
            entry.setAccountId(order.getAccountId());
            addReportingJobUnit(entry);
        }
    }

    public void addReportingJobUnit(ReportingJobQueueEntry entry) {
        reportingJobQueueDAO.addJob(entry);
    }

    public void addReportingJobUnitInTransaction(DSLContext dslcontext, ReportingJobQueueEntry entry) {
        reportingJobQueueDAO.addJob(dslcontext, entry);
    }

    public void deleteReportingJobUnits(List<UUID> ids, String tenantId) {
        reportingJobQueueDAO.deleteJobs(ids, tenantId);
    }

    public List<ReportingJobQueueEntry> getJobUnits(String tenantId, int numJobUnits) {
        if (numJobUnits <= 0) {
            throw new ServiceFailureException("number of job units should be greater than 0");
        }
        return reportingJobQueueDAO.getJobs(tenantId, numJobUnits);
    }

    public void addUsageArrForInvoiceInTransaction(Configuration configuration, Invoice invoice, String tenantId) {
        InvoiceStatus invoiceStatus = invoice.getStatus();
        if (!featureService.isEnabled(Feature.USAGE_ARR) || !(invoiceStatus == POSTED || invoiceStatus == PAID || invoiceStatus == VOIDED)) {
            return;
        }

        DSLContext dslContext = DSL.using(configuration);
        invoice.getInvoiceItems().forEach(invoiceItem -> checkAndAddInvoiceItemToReportingJobQueue(dslContext, invoice, invoiceItem, tenantId));
    }

    private void checkAndAddInvoiceItemToReportingJobQueue(DSLContext dslContext, Invoice invoice, InvoiceItem invoiceItem, String tenantId) {
        Charge charge = productCatalogGetService.getChargeByChargeId(invoiceItem.getChargeId());
        if (charge.getType() != ChargeType.USAGE) {
            return;
        }

        ReportingJobCategory category = invoice.getStatus() == POSTED || invoice.getStatus() == PAID
            ? ReportingJobCategory.INVOICE_POSTED
            : ReportingJobCategory.INVOICE_VOIDED;
        ReportingJobQueueEntry entry = new ReportingJobQueueEntry(tenantId, category);
        entry.setInvoiceId(invoice.getInvoiceNumber().getNumber());
        entry.setInvoiceLineItemId(invoiceItem.getInvoiceLineNumber());
        reportingJobQueueDAO.addJob(dslContext, entry);
    }

    public void addDeleteTaskToRemoveArrRowsForOrder(Configuration configuration, String tenantId, String orderId) {
        DSLContext dslContext = DSL.using(configuration);
        ReportingJobQueueEntry entry = new ReportingJobQueueEntry(tenantId, ReportingJobCategory.DELETE_ORDER_ARR);
        entry.setOrderId(orderId);
        reportingJobQueueDAO.addJob(dslContext, entry);
    }

    public void deleteReportingJobUnit(ReportingJobQueueEntry job) {
        reportingJobQueueDAO.deleteJob(job);
    }

    public void addArrJobsToValidateAccountData(String tenantId) {
        UUID cursor = null;
        do {
            var paginationQueryParams = new PaginationQueryParams(cursor, PAGINATION_LIMIT);
            var accountsSynced = addJobToValidateAccountArrData(paginationQueryParams, tenantId);
            var paginationResponse = new PaginationResponseJson<>(accountsSynced, PAGINATION_LIMIT, accountMapper::accountsToJson, Account::getId);
            cursor = paginationResponse.getNextCursor();
        } while (cursor != null);
    }

    public List<Account> addJobToValidateAccountArrData(PaginationQueryParams params, String tenantId) {
        List<ReportingJobQueueEntry> jobs = new ArrayList<>();
        var accounts = accountGetService.getAccounts(AccountType.ALL, params, tenantId);
        for (var account : accounts) {
            ReportingJobQueueEntry job = new ReportingJobQueueEntry(tenantId, ReportingJobCategory.VALIDATE_ACCOUNT_ARR);
            job.setAccountId(account.getAccountId());
            jobs.add(job);
        }

        reportingJobQueueDAO.addJobs(jobs, tenantId);
        return accounts;
    }
}
