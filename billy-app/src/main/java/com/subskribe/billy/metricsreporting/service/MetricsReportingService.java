package com.subskribe.billy.metricsreporting.service;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.LineItemMetrics;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.metricsreporting.db.OrderLineMetricsReportingDAO;
import com.subskribe.billy.metricsreporting.db.TransactionalMetricsReportingDAO;
import com.subskribe.billy.metricsreporting.model.ArrMetricsGeneratedBy;
import com.subskribe.billy.metricsreporting.model.ArrMetricsReportingConfiguration;
import com.subskribe.billy.metricsreporting.model.ExternalArrSchedule;
import com.subskribe.billy.metricsreporting.model.ImmutableOrderLineArrCategoryRow;
import com.subskribe.billy.metricsreporting.model.ImmutableTransactionalArrMetricsRow;
import com.subskribe.billy.metricsreporting.model.OrderLineArrCategory;
import com.subskribe.billy.metricsreporting.model.OrderLineArrCategoryRow;
import com.subskribe.billy.metricsreporting.model.ProcessedArrCategory;
import com.subskribe.billy.metricsreporting.model.ReportingJobCategory;
import com.subskribe.billy.metricsreporting.model.ReportingJobQueueEntry;
import com.subskribe.billy.metricsreporting.model.TransactionalArrMetricsRow;
import com.subskribe.billy.order.OrderLineUtils;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionOrderService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import com.subskribe.billy.tenant.model.Tenant;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.ws.rs.ForbiddenException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

@AllowNonRlsDataAccess
public class MetricsReportingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetricsReportingService.class);
    private static final DateTimeFormatter PERIOD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");
    private static final int NUMBER_OF_MONTHS_IN_YEAR = 12;
    private static final int MAX_TIMES_FOR_PENDING_RENEWALS = 3; // considering renewal line, current and its own debook case
    private static final BigDecimal NEGATIVE_ONE = BigDecimal.valueOf(-1);

    private static final int MAX_ROWS_TO_PROCESS = 100;

    private final OrderLineMetricsReportingDAO orderLineMetricsReportingDAO;
    private final OrderGetService orderGetService;
    private final ProductCatalogGetService productCatalogGetService;
    private final MetricsService metricsService;
    private final TenantSettingService tenantSettingService;
    private final BillyConfiguration billyConfiguration;
    private final DSLContextProvider nonRLSContextProvider;
    private final ReportingJobQueueService reportingJobQueueService;
    private final SubscriptionGetService subscriptionGetService;
    private final ExternalArrScheduleService externalArrScheduleService;
    private final TenantIdProvider tenantIdProvider;
    private final InvoiceRetrievalService invoiceRetrievalService;
    private final SubscriptionOrderService subscriptionOrderService;
    private final TransactionalMetricsReportingDAO transactionalMetricsReportingDAO;
    private final CrmService crmService;

    @Inject
    public MetricsReportingService(
        OrderLineMetricsReportingDAO orderLineMetricsReportingDAO,
        OrderGetService orderGetService,
        ProductCatalogGetService productCatalogGetService,
        MetricsService metricsService,
        TenantSettingService tenantSettingService,
        BillyConfiguration billyConfiguration,
        DSLContextProvider nonRLSContextProvider,
        ReportingJobQueueService reportingJobQueueService,
        SubscriptionGetService subscriptionGetService,
        ExternalArrScheduleService externalArrScheduleService,
        TenantIdProvider tenantIdProvider,
        InvoiceRetrievalService invoiceRetrievalService,
        SubscriptionOrderService subscriptionOrderService,
        TransactionalMetricsReportingDAO transactionalMetricsReportingDAO,
        CrmService crmService
    ) {
        this.orderLineMetricsReportingDAO = orderLineMetricsReportingDAO;
        this.orderGetService = orderGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.metricsService = metricsService;
        this.tenantSettingService = tenantSettingService;
        this.billyConfiguration = billyConfiguration;
        this.nonRLSContextProvider = nonRLSContextProvider;
        this.reportingJobQueueService = reportingJobQueueService;
        this.subscriptionGetService = subscriptionGetService;
        this.externalArrScheduleService = externalArrScheduleService;
        this.tenantIdProvider = tenantIdProvider;
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.subscriptionOrderService = subscriptionOrderService;
        this.transactionalMetricsReportingDAO = transactionalMetricsReportingDAO;
        this.crmService = crmService;
    }

    public void processReportingJobUnits(Tenant tenant) {
        List<ReportingJobQueueEntry> jobs = reportingJobQueueService.getJobUnits(tenant.getTenantId(), MAX_ROWS_TO_PROCESS);
        if (CollectionUtils.isEmpty(jobs)) {
            return;
        }

        for (var job : jobs) {
            processReportingJobUnit(job);
            reportingJobQueueService.deleteReportingJobUnit(job);
        }

        updateLatestOrderLineMetricsTimestamp(tenant.getTenantId(), LocalDateTime.now());
    }

    public void processReportingJobUnit(ReportingJobQueueEntry jobUnit) {
        switch (jobUnit.getJobCategory()) {
            case ORDER_LINE_ARR -> updateOrderLineArrCategory(jobUnit.getOrderId(), jobUnit.getOrderLineItemId());
            case MISSED_RENEWAL_LINES -> adjustArrForMissedRenewalLines(jobUnit);
            case EXTERNAL_ARR -> addExternalArrMetrics(jobUnit);
            case INVOICE_POSTED -> addMetricsForInvoicePosted(jobUnit);
            case INVOICE_VOIDED -> addMetricsForInvoiceVoided(jobUnit);
            case DELETE_ORDER_ARR -> deleteOrderArrData(jobUnit);
            case VALIDATE_ACCOUNT_ARR -> validateAccountArrData(jobUnit);
            default -> throw new ServiceFailureException("Unknown arr job category found: " + jobUnit.getJobCategory());
        }
    }

    private void validateAccountArrData(ReportingJobQueueEntry jobUnit) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Instant startDate = subscriptionGetService.getEarliestDayOfASubscriptionInAccount(jobUnit.getAccountId());
        if (startDate == null) {
            LOGGER.info("AccountArrValidation: No subscriptions found in account:{} for tenant {}", jobUnit.getAccountId(), jobUnit.getTenantId());
            return;
        }

        ZonedDateTime zonedStartDate = startDate.atZone(timeZone.toZoneId());
        Instant endDate = subscriptionGetService.getLatestDayOfASubscriptionInAccount(jobUnit.getAccountId());
        ZonedDateTime zonedEndDate = endDate.atZone(timeZone.toZoneId());

        Instant currentStartDate;
        Instant currentEndDate;
        boolean arrDataMismatchFound = false;

        BigDecimal previousEndingBalance = BigDecimal.ZERO;
        BigDecimal currentPeriodOpeningBalance;
        while (zonedStartDate.isBefore(zonedEndDate)) {
            currentStartDate = DateTimeConverter.getStartOfCurrentMonth(zonedStartDate.toInstant(), timeZone);
            currentEndDate = DateTimeConverter.getEndOfCurrentMonth(zonedStartDate.toInstant(), timeZone);

            currentPeriodOpeningBalance = orderLineMetricsReportingDAO.getCurrentPeriodOpeningBalanceForAccount(
                jobUnit.getTenantId(),
                jobUnit.getAccountId(),
                currentStartDate,
                currentEndDate
            );
            if (!Numbers.equals(currentPeriodOpeningBalance, previousEndingBalance)) {
                arrDataMismatchFound = true;
                LOGGER.info(
                    "AccountArrValidation: Balance mismatch in account: {} for tenant: {} for the period: {} to {}",
                    jobUnit.getAccountId(),
                    jobUnit.getTenantId(),
                    currentStartDate,
                    currentEndDate
                );
            }

            previousEndingBalance = orderLineMetricsReportingDAO.getCurrentPeriodEndingBalanceForAccount(
                jobUnit.getTenantId(),
                jobUnit.getAccountId(),
                currentStartDate,
                currentEndDate
            );

            zonedStartDate = zonedStartDate.plusMonths(1);
        }

        if (arrDataMismatchFound) {
            LOGGER.warn("AccountArrValidation: ARR Data Mismatch found for account: {} in tenant {}", jobUnit.getAccountId(), jobUnit.getTenantId());
        } else {
            LOGGER.info("AccountArrValidation: ARR Data is good for account: {} in tenant {}", jobUnit.getAccountId(), jobUnit.getTenantId());
        }
    }

    public void deleteTenantLevelArrData(String tenantId) {
        DSLContext nonTenantDslContext = nonRLSContextProvider.get();
        nonTenantDslContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            orderLineMetricsReportingDAO.deleteTenantArrData(dslContext, tenantId);
            transactionalMetricsReportingDAO.deleteTenantArrData(dslContext, tenantId);
        });
    }

    private void deleteOrderArrData(ReportingJobQueueEntry jobUnit) {
        DSLContext nonTenantDslContext = nonRLSContextProvider.get();
        nonTenantDslContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            orderLineMetricsReportingDAO.deleteRows(dslContext, jobUnit.getTenantId(), jobUnit.getOrderId());
            transactionalMetricsReportingDAO.deleteRows(dslContext, jobUnit.getTenantId(), jobUnit.getOrderId());
        });
    }

    private void addMetricsForInvoiceVoided(ReportingJobQueueEntry jobUnit) {
        // retrieve voided invoices even if deleted, so existing metrics can be offset
        Optional<Invoice> optionalInvoice = invoiceRetrievalService.getInvoiceIncludingDeleted(new Invoice.Number(jobUnit.getInvoiceId()));

        if (optionalInvoice.isEmpty()) {
            // We expect the invoice to be there, even if deleted
            throw new ObjectNotFoundException(BillyObjectType.INVOICE, jobUnit.getInvoiceId());
        }

        Invoice invoice = optionalInvoice.get();

        if (invoice.getStatus() != InvoiceStatus.VOIDED) {
            LOGGER.info("Invoice: {} is not in voided state. Current state is: {}", jobUnit.getInvoiceId(), invoice.getStatus());
            return;
        }

        List<OrderLineArrCategoryRow> existingRowsForInvoice = orderLineMetricsReportingDAO.getArrRowsForInvoiceLine(
            jobUnit.getTenantId(),
            jobUnit.getInvoiceId(),
            jobUnit.getInvoiceLineItemId()
        );
        if (CollectionUtils.isEmpty(existingRowsForInvoice)) {
            LOGGER.info("No rows populated with arr to void for Invoice: {}", jobUnit.getInvoiceId());
            return;
        }

        // for any of the existing rows that are greater than or equal to the month period of the voided invoice date,
        // we need to add an offset with the same category as the row
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        LocalDateTime currentLocalTime = LocalDateTime.ofInstant(Instant.now(), timeZone.toZoneId());
        List<OrderLineArrCategoryRow> voidRows = new ArrayList<>();

        for (OrderLineArrCategoryRow row : existingRowsForInvoice) {
            OrderLineArrCategoryRow voidRow = ImmutableOrderLineArrCategoryRow.copyOf(row)
                .withAmount(row.getAmount().multiply(NEGATIVE_ONE))
                .withInsertedOn(currentLocalTime)
                .withOrderExecutedOn(invoice.getVoidedDate())
                .withQuantity(row.getQuantity() * -1);

            voidRows.add(voidRow);
        }

        DSLContext nonTenantDslContext = nonRLSContextProvider.get();
        nonTenantDslContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            orderLineMetricsReportingDAO.addBulkOrderLineMetrics(dslContext, jobUnit.getTenantId(), voidRows);
        });
    }

    public void updateArrMetricsForInvoices(String invoiceNumber) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new ForbiddenException("Not allowed.");
        }

        String tenantId = tenantIdProvider.provideTenantIdString();
        Invoice invoice = invoiceRetrievalService.getInvoice(new Invoice.Number(invoiceNumber));
        if (
            !(invoice.getStatus() == InvoiceStatus.POSTED || invoice.getStatus() == InvoiceStatus.PAID || invoice.getStatus() == InvoiceStatus.VOIDED)
        ) {
            LOGGER.info("invoice is not in posted, paid or voided state");
            return;
        }

        // generate or refresh the usage arr metrics on posting/paid invoices
        ReportingJobQueueEntry jobUnit = new ReportingJobQueueEntry(tenantId, ReportingJobCategory.INVOICE_POSTED);
        jobUnit.setInvoiceId(invoiceNumber);
        for (var lineItem : invoice.getInvoiceItems()) {
            generateUsageArrMetricsOnInvoice(jobUnit, lineItem);
        }

        if (invoice.getStatus() != InvoiceStatus.VOIDED) {
            return;
        }

        // generate the void invoice usage arr metrics if the invoice is voided
        ReportingJobQueueEntry VoidInvoiceJobUnit = new ReportingJobQueueEntry(tenantId, ReportingJobCategory.INVOICE_VOIDED);
        VoidInvoiceJobUnit.setInvoiceId(invoiceNumber);
        for (var lineItem : invoice.getInvoiceItems()) {
            generateUsageArrMetricsOnInvoice(VoidInvoiceJobUnit, lineItem);
        }
    }

    private void generateUsageArrMetricsOnInvoice(ReportingJobQueueEntry jobUnit, InvoiceItem lineItem) {
        Charge charge = productCatalogGetService.getChargeByChargeId(lineItem.getChargeId());
        if (charge.getType() == ChargeType.USAGE) {
            jobUnit.setInvoiceLineItemId(lineItem.getInvoiceLineNumber());
            if (jobUnit.getJobCategory() == ReportingJobCategory.INVOICE_POSTED) {
                addMetricsForInvoicePosted(jobUnit);
            } else if (jobUnit.getJobCategory() == ReportingJobCategory.INVOICE_VOIDED) {
                addMetricsForInvoiceVoided(jobUnit);
            }
        }
    }

    private void addMetricsForInvoicePosted(ReportingJobQueueEntry jobUnit) {
        Optional<Invoice> optionalInvoice = invoiceRetrievalService.getInvoiceByInvoiceNumber(new Invoice.Number(jobUnit.getInvoiceId()));

        // Invoice could be deleted since posting. In this case skip processing.
        if (optionalInvoice.isEmpty()) {
            LOGGER.info("Invoice: {} not found for tenant: {}. It may have been deleted.", jobUnit.getInvoiceId(), jobUnit.getTenantId());
            return;
        }

        Invoice invoice = optionalInvoice.get();

        List<InvoiceItem> lineItems = invoice
            .getInvoiceItems()
            .stream()
            .filter(invoiceItem -> invoiceItem.getInvoiceLineNumber().equals(jobUnit.getInvoiceLineItemId()))
            .toList();
        if (lineItems.isEmpty()) {
            LOGGER.warn(
                "Couldn't find the invoiceItem with invoice line number: {} for invoice: {}. Hence not generating Usage ARR Metrics for this line",
                jobUnit.getInvoiceLineItemId(),
                jobUnit.getInvoiceId()
            );
            return;
        }

        if (lineItems.size() > 1) {
            LOGGER.warn(
                "Multiple invoiceItems found with invoice line number: {} for invoice: {}. Hence not generating Usage ARR Metrics for this line",
                jobUnit.getInvoiceLineItemId(),
                jobUnit.getInvoiceId()
            );
            return;
        }

        InvoiceItem invoiceItem = lineItems.get(0);
        Charge charge = productCatalogGetService.getChargeByChargeId(invoiceItem.getChargeId());
        if (BooleanUtils.isFalse(charge.getShouldTrackArr())) {
            LOGGER.info(
                "Invoice item with invoice line number: {} for invoice: {} is not configured to track ARR. Hence not generating Usage ARR Metrics for this line",
                jobUnit.getInvoiceLineItemId(),
                jobUnit.getInvoiceId()
            );
            return;
        }
        Subscription subscription = subscriptionGetService.getSubscription(invoice.getSubscriptionId());
        List<OrderLineArrCategoryRow> rowsToInsert = addDeltaUsageArrRows(jobUnit, invoice, invoiceItem, subscription);

        DSLContext nonTenantDslContext = nonRLSContextProvider.get();
        nonTenantDslContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            orderLineMetricsReportingDAO.deleteUsageArrRows(
                dslContext,
                jobUnit.getTenantId(),
                invoiceItem.getOrderLineItemId(),
                invoice.getInvoiceNumber().toString(),
                invoiceItem.getInvoiceLineNumber()
            );
            orderLineMetricsReportingDAO.addBulkOrderLineMetrics(dslContext, jobUnit.getTenantId(), rowsToInsert);
        });
    }

    public List<OrderLineArrCategoryRow> addDeltaUsageArrRows(
        ReportingJobQueueEntry jobUnit,
        Invoice invoice,
        InvoiceItem invoiceItem,
        Subscription subscription
    ) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        BigDecimal invoiceArrAmount = getInvoiceItemArrAmount(invoiceItem, timeZone);
        Integer postedInvoicesCount = invoiceRetrievalService.getPostedInvoicesCountForSubscription(invoice.getSubscriptionId());
        BigDecimal deltaArr = findDeltaForCurrentInvoiceLine(jobUnit, invoice, invoiceItem, invoiceArrAmount, timeZone);
        if (StringUtils.isNotBlank(subscription.getRenewedFromSubscriptionId())) {
            // need to find upsell / downsell based on the previous subscriptions values
            deltaArr = getDeltaArrForUsageLineRenewal(jobUnit, invoiceItem, invoiceArrAmount, timeZone);
        }

        OrderLineArrCategory arrCategory = OrderLineArrCategory.NEW;
        boolean useUsageDelta = shouldUseUsageDelta(invoice, subscription, postedInvoicesCount);
        if (useUsageDelta) {
            arrCategory = deltaArr.compareTo(BigDecimal.ZERO) > 0 ? OrderLineArrCategory.UPSELL : OrderLineArrCategory.DOWNSELL;
        }

        var baseRowBuilder = ImmutableOrderLineArrCategoryRow.builder()
            .tenantId(jobUnit.getTenantId())
            .generatedBy(ArrMetricsGeneratedBy.SYSTEM)
            .chargeType(ChargeType.USAGE.name())
            .accountId(invoice.getCustomerAccountId())
            .subscriptionId(invoice.getSubscriptionId())
            .orderId(invoiceItem.getOrderId())
            .orderLineItemId(invoiceItem.getOrderLineItemId())
            .planId(invoiceItem.getPlanId())
            .chargeId(invoiceItem.getChargeId())
            .invoiceId(invoice.getInvoiceNumber().getNumber())
            .invoiceLineItemId(invoiceItem.getInvoiceLineNumber())
            .insertedOn(LocalDateTime.now())
            .orderExecutedOn(invoice.getInvoiceDate());

        List<OrderLineArrCategoryRow> orderLineArrCategoryRows = new ArrayList<>();
        addRow(baseRowBuilder, arrCategory, invoiceItem.getPeriodStartDate(), deltaArr, orderLineArrCategoryRows, invoiceItem.getQuantity());

        BigDecimal openingBalanceAmount = postedInvoicesCount <= 1 && invoice.getStatus() != InvoiceStatus.VOIDED ? invoiceArrAmount : deltaArr;
        addOpeningBalanceRows(
            baseRowBuilder,
            invoiceItem.getPeriodStartDate(),
            subscription.getEndDate(),
            orderLineArrCategoryRows,
            timeZone,
            openingBalanceAmount,
            invoiceItem.getQuantity()
        );

        addRow(
            baseRowBuilder,
            OrderLineArrCategory.EXPIRATION,
            subscription.getEndDate(),
            openingBalanceAmount.multiply(NEGATIVE_ONE),
            orderLineArrCategoryRows,
            invoiceItem.getQuantity() * -1
        );

        ArrMetricsReportingConfiguration reportingConfiguration = getTenantScopedArrReportingConfig(jobUnit.getTenantId());
        if (reportingConfiguration.getIncludePendingForUsageArr()) {
            addPendingRenewalRowsInternal(
                baseRowBuilder,
                jobUnit.getTenantId(),
                subscription.getEndDate(),
                openingBalanceAmount,
                invoiceItem.getQuantity(),
                orderLineArrCategoryRows,
                timeZone
            );
        }

        if (postedInvoicesCount == 1 && StringUtils.isNotBlank(subscription.getRenewedFromSubscriptionId())) {
            // need to clear out the previous orderlines pending renewals
            addExpirationOffsetForBaseUsageArr(jobUnit.getTenantId(), invoice, invoiceItem, orderLineArrCategoryRows, timeZone);
            addPendingRenewalsOffsetForBaseUsageArr(jobUnit.getTenantId(), invoice, invoiceItem, orderLineArrCategoryRows, timeZone);
        }

        return orderLineArrCategoryRows;
    }

    private static boolean shouldUseUsageDelta(Invoice invoice, Subscription subscription, Integer postedInvoicesCount) {
        return (
            postedInvoicesCount > 1 ||
            (postedInvoicesCount == 1 && invoice.getStatus() == InvoiceStatus.VOIDED) ||
            StringUtils.isNotBlank(subscription.getRenewedFromSubscriptionId())
        );
    }

    private BigDecimal getDeltaArrForUsageLineRenewal(
        ReportingJobQueueEntry jobUnit,
        InvoiceItem invoiceItem,
        BigDecimal invoiceArrAmount,
        TimeZone timeZone
    ) {
        OrderLineItem parentOrderLine = getParentOrderLineFromInvoiceItem(invoiceItem);
        if (parentOrderLine == null) {
            return invoiceArrAmount;
        }

        Instant usagePeriodStart = DateTimeConverter.getStartOfCurrentMonth(invoiceItem.getPeriodStartDate(), timeZone);
        Instant usagePeriodEnd = DateTimeConverter.getStartOfCurrentMonth(invoiceItem.getPeriodEndDate(), timeZone);

        List<OrderLineArrCategoryRow> openingBalanceRows = orderLineMetricsReportingDAO.getArrRowsForCategoryForOrderLineId(
            jobUnit.getTenantId(),
            parentOrderLine.getOrderLineId(),
            OrderLineArrCategory.OPENING_BALANCE,
            Optional.of(usagePeriodStart),
            Optional.of(usagePeriodEnd)
        );

        BigDecimal currentOpeningBalanceTotal = openingBalanceRows
            .stream()
            .map(OrderLineArrCategoryRow::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        return invoiceArrAmount.subtract(currentOpeningBalanceTotal);
    }

    private void addExpirationOffsetForBaseUsageArr(
        String tenantId,
        Invoice invoice,
        InvoiceItem invoiceItem,
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows,
        TimeZone timeZone
    ) {
        OrderLineItem parentOrderLine = getParentOrderLineFromInvoiceItem(invoiceItem);
        if (parentOrderLine == null) {
            return;
        }

        Instant usagePeriodStart = DateTimeConverter.getStartOfCurrentMonth(invoiceItem.getPeriodStartDate(), timeZone);
        Instant usagePeriodEnd = DateTimeConverter.getStartOfCurrentMonth(invoiceItem.getPeriodEndDate(), timeZone);
        List<OrderLineArrCategoryRow> expirationRows = orderLineMetricsReportingDAO.getArrRowsForCategoryForOrderLineId(
            tenantId,
            parentOrderLine.getOrderLineId(),
            OrderLineArrCategory.EXPIRATION,
            Optional.of(usagePeriodStart),
            Optional.of(usagePeriodEnd)
        );

        BigDecimal expirationAmount = expirationRows.stream().map(OrderLineArrCategoryRow::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        long expirationQuantity = expirationRows.stream().map(OrderLineArrCategoryRow::getQuantity).reduce(0L, Long::sum);

        var baseRowBuilder = ImmutableOrderLineArrCategoryRow.builder()
            .tenantId(tenantId)
            .generatedBy(ArrMetricsGeneratedBy.SYSTEM)
            .chargeType(ChargeType.USAGE.name())
            .accountId(invoice.getCustomerAccountId())
            .subscriptionId(invoice.getSubscriptionId())
            .orderId(parentOrderLine.getOrderId())
            .orderLineItemId(parentOrderLine.getOrderLineId())
            .planId(parentOrderLine.getPlanId())
            .chargeId(parentOrderLine.getChargeId())
            .invoiceId(invoice.getInvoiceNumber().getNumber())
            .invoiceLineItemId(invoiceItem.getInvoiceLineNumber())
            .insertedOn(LocalDateTime.now())
            .orderExecutedOn(invoice.getInvoiceDate());

        // add negative amount and quantity to compensate with the original expiration rows
        addRow(
            baseRowBuilder,
            OrderLineArrCategory.EXPIRATION,
            parentOrderLine.getEndDate(),
            expirationAmount.multiply(NEGATIVE_ONE),
            orderLineArrCategoryRows,
            expirationQuantity * -1
        );
    }

    private void addPendingRenewalsOffsetForBaseUsageArr(
        String tenantId,
        Invoice invoice,
        InvoiceItem invoiceItem,
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows,
        TimeZone timeZone
    ) {
        ArrMetricsReportingConfiguration arrReportingConfig = getTenantScopedArrReportingConfig(tenantId);
        if (!arrReportingConfig.getIncludePendingForUsageArr()) {
            return;
        }

        OrderLineItem parentOrderLine = getParentOrderLineFromInvoiceItem(invoiceItem);
        if (parentOrderLine == null) {
            return;
        }

        List<OrderLineArrCategoryRow> pendingRenewalRows = orderLineMetricsReportingDAO.getArrRowsForCategoryForOrderLineId(
            tenantId,
            parentOrderLine.getOrderLineId(),
            OrderLineArrCategory.PENDING_RENEWAL,
            Optional.empty(),
            Optional.empty()
        );
        if (CollectionUtils.isEmpty(pendingRenewalRows)) {
            return;
        }

        var baseRowBuilder = ImmutableOrderLineArrCategoryRow.builder()
            .tenantId(tenantId)
            .generatedBy(ArrMetricsGeneratedBy.SYSTEM)
            .chargeType(ChargeType.USAGE.name())
            .accountId(invoice.getCustomerAccountId())
            .subscriptionId(invoice.getSubscriptionId())
            .orderId(parentOrderLine.getOrderId())
            .orderLineItemId(parentOrderLine.getOrderLineId())
            .planId(parentOrderLine.getPlanId())
            .chargeId(parentOrderLine.getChargeId())
            .invoiceId(invoice.getInvoiceNumber().getNumber())
            .invoiceLineItemId(invoiceItem.getInvoiceLineNumber())
            .insertedOn(LocalDateTime.now())
            .orderExecutedOn(invoice.getInvoiceDate());

        ZonedDateTime currentMonthInZoneTime = invoiceItem.getPeriodStartDate().atZone(timeZone.toZoneId());
        for (int i = 0; i < arrReportingConfig.getPendingRenewalMonths(); i++) {
            Instant startingDayOfMonth = DateTimeConverter.getStartOfCurrentMonth(currentMonthInZoneTime.toInstant(), timeZone);
            Instant endingDayOfMonth = DateTimeConverter.getEndOfCurrentMonth(currentMonthInZoneTime.toInstant(), timeZone);

            BigDecimal pendingRenewalAmountOffsetInPeriod = pendingRenewalRows
                .stream()
                .filter(row -> !row.getEffectiveDate().isBefore(startingDayOfMonth) && !row.getEffectiveDate().isAfter(endingDayOfMonth))
                .map(OrderLineArrCategoryRow::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .multiply(NEGATIVE_ONE);
            if (!Numbers.isZero(pendingRenewalAmountOffsetInPeriod)) {
                addRow(
                    baseRowBuilder,
                    OrderLineArrCategory.PENDING_RENEWAL,
                    currentMonthInZoneTime.toInstant(),
                    pendingRenewalAmountOffsetInPeriod,
                    orderLineArrCategoryRows,
                    parentOrderLine.getQuantity()
                );
            }
            currentMonthInZoneTime = currentMonthInZoneTime.plusMonths(1);
        }
    }

    private OrderLineItem getParentOrderLineFromInvoiceItem(InvoiceItem invoiceItem) {
        Optional<OrderLineItem> currentOrderLineItemOptional = orderGetService.getOrderLineItemByOrderLineItemId(invoiceItem.getOrderLineItemId());
        if (currentOrderLineItemOptional.isEmpty()) {
            LOGGER.warn("Couldn't find the orderLineItem with id: " + invoiceItem.getOrderLineItemId());
            return null;
        }

        OrderLineItem currentOrderLineItem = currentOrderLineItemOptional.get();
        Optional<OrderLineItem> originalOrderLineItemOptional = subscriptionOrderService.getAmendedOrRenewedFromLineItem(currentOrderLineItem);
        if (originalOrderLineItemOptional.isEmpty()) {
            LOGGER.warn("Couldn't find the baseOrderLineItem for orderLineItemId: {}", invoiceItem.getOrderLineItemId());
            return null;
        }
        return originalOrderLineItemOptional.get();
    }

    private BigDecimal findDeltaForCurrentInvoiceLine(
        ReportingJobQueueEntry jobUnit,
        Invoice invoice,
        InvoiceItem invoiceItem,
        BigDecimal usageLineArr,
        TimeZone timeZone
    ) {
        // Fetch the sum of amount in the period
        Instant startingInstantOfCurrentPeriod = DateTimeConverter.getStartOfCurrentMonth(invoice.getInvoiceDate(), timeZone);
        Instant endingInstantOfCurrentPeriod = DateTimeConverter.getEndOfCurrentMonth(invoice.getInvoiceDate(), timeZone);
        List<OrderLineArrCategoryRow> storedRecordsInTheCurrentPeriod = orderLineMetricsReportingDAO.getUsageArrRecordsForOrderLineForSpecificPeriod(
            jobUnit.getTenantId(),
            invoiceItem.getOrderLineItemId(),
            startingInstantOfCurrentPeriod,
            endingInstantOfCurrentPeriod
        );

        BigDecimal recordedArr = storedRecordsInTheCurrentPeriod
            .stream()
            .map(OrderLineArrCategoryRow::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        return usageLineArr.subtract(recordedArr);
    }

    private BigDecimal getInvoiceItemArrAmount(InvoiceItem invoiceItem, TimeZone timeZone) {
        long months = Period.toDurationInMonthsFloor(invoiceItem.getPeriodStartDate(), invoiceItem.getPeriodEndDate(), timeZone);
        if (months == 0) {
            // for days less than a month, treat it as a month for Usage
            months = 1;
        }
        return invoiceItem
            .getAmount()
            .multiply(BigDecimal.valueOf(NUMBER_OF_MONTHS_IN_YEAR))
            .divide(BigDecimal.valueOf(months), RoundingMode.HALF_UP);
    }

    public void updateOrderLineArrCategoryForOrder(String orderId) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new ForbiddenException("Not allowed.");
        }

        Order order = orderGetService.getOrderByOrderId(orderId);
        List<OrderLineItem> orderLineItems = order
            .getLineItems()
            .stream()
            .filter(orderLineItem -> orderLineItem.getAction() != ActionType.NONE)
            .toList();
        orderLineItems.forEach(lineItem -> updateOrderLineArrCategory(order, lineItem));

        if (order.getOrderType() == OrderType.RENEWAL) {
            adjustArrForMissingRenewalLinesOnRenewalOrder(order);
        }
    }

    private void adjustArrForMissedRenewalLines(ReportingJobQueueEntry jobUnit) {
        var renewalOrderOptional = orderGetService.getOrderByOrderIdOptional(jobUnit.getOrderId());
        if (renewalOrderOptional.isEmpty()) {
            LOGGER.info("renewal Order not found: {}", jobUnit.getOrderId());
            return;
        }

        if (renewalOrderOptional.get().getOrderType() != OrderType.RENEWAL) {
            LOGGER.info("order {} is not a renewal order", renewalOrderOptional.get().getOrderId());
            return;
        }

        Order renewalOrder = renewalOrderOptional.get();
        LOGGER.info(
            "Finding missed renewal lines for renewal order: {} and subscription: {}",
            renewalOrder.getOrderId(),
            renewalOrder.getExternalSubscriptionId()
        );

        adjustArrForMissingRenewalLinesOnRenewalOrder(renewalOrder);
    }

    private void adjustArrForMissingRenewalLinesOnRenewalOrder(Order renewalOrder) {
        List<OrderLineItem> missingOrderLineItems = getMissingRenewalLineItems(renewalOrder);
        if (CollectionUtils.isEmpty(missingOrderLineItems)) {
            return;
        }

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        LocalDateTime currentLocalTime = LocalDateTime.ofInstant(Instant.now(), timeZone.toZoneId());
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows = new ArrayList<>();
        List<TransactionalArrMetricsRow> transactionalArrMetricsRows = new ArrayList<>();
        for (OrderLineItem missingOrderLineItem : missingOrderLineItems) {
            Charge charge = productCatalogGetService.getChargeByChargeId(missingOrderLineItem.getChargeId());
            try {
                if (BooleanUtils.isTrue(charge.getShouldTrackArr())) {
                    LOGGER.info(
                        "adding missing renewal rows for orderline: {} for order: {}",
                        missingOrderLineItem.getOrderLineId(),
                        missingOrderLineItem.getOrderId()
                    );
                    LineItemMetrics metrics = metricsService.getMetricsForOrderLine(missingOrderLineItem.getOrderLineId());
                    ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder = ImmutableOrderLineArrCategoryRow.builder()
                        .tenantId(missingOrderLineItem.getTenantId())
                        .orderLineItemId(missingOrderLineItem.getOrderLineId())
                        .orderId(missingOrderLineItem.getOrderId())
                        .planId(missingOrderLineItem.getPlanId())
                        .chargeId(missingOrderLineItem.getChargeId())
                        .accountId(renewalOrder.getAccountId())
                        .generatedBy(ArrMetricsGeneratedBy.SYSTEM)
                        .chargeType(charge.getType().name())
                        .subscriptionId(renewalOrder.getRenewalForSubscriptionId())
                        .insertedOn(LocalDateTime.now())
                        .orderExecutedOn(renewalOrder.getExecutedOn());

                    addExpirationOffsetRowForMissingRenewalOrderLine(baseRowBuilder, missingOrderLineItem, metrics, orderLineArrCategoryRows);
                    orderLineArrCategoryRows.addAll(
                        getPendingRenewalOffsetRowsForMissedRenewalLine(baseRowBuilder, missingOrderLineItem, renewalOrder.getExecutedOn(), metrics)
                    );

                    ImmutableOrderLineArrCategoryRow.Builder missedRenewalDebookLine = ImmutableOrderLineArrCategoryRow.builder()
                        .tenantId(missingOrderLineItem.getTenantId())
                        .orderLineItemId(missingOrderLineItem.getOrderLineId())
                        .orderId(renewalOrder.getOrderId())
                        .planId(missingOrderLineItem.getPlanId())
                        .chargeId(missingOrderLineItem.getChargeId())
                        .accountId(renewalOrder.getAccountId())
                        .generatedBy(ArrMetricsGeneratedBy.SYSTEM)
                        .chargeType(charge.getType().name())
                        .subscriptionId(renewalOrder.getRenewalForSubscriptionId())
                        .insertedOn(LocalDateTime.now())
                        .orderExecutedOn(renewalOrder.getExecutedOn());

                    addDebookLineForMissingOrderLine(
                        missedRenewalDebookLine,
                        renewalOrder,
                        missingOrderLineItem,
                        charge,
                        metrics,
                        currentLocalTime,
                        orderLineArrCategoryRows,
                        transactionalArrMetricsRows
                    );
                } else {
                    LOGGER.info(
                        "Skipping generating ARR for missing renewal line: {} on order: {} as track arr is not set",
                        missingOrderLineItem.getOrderLineId(),
                        missingOrderLineItem.getOrderId()
                    );
                }
            } catch (Exception ex) {
                // log error and continue for that charge
                LOGGER.warn("Exception thrown when adding arr for missed renewal rows", ex);
            }
        }

        String missingOrderLinesString = String.join(",", missingOrderLineItems.stream().map(OrderLineItem::getOrderLineId).toList());
        DSLContext nonTenantDslContext = nonRLSContextProvider.get();
        nonTenantDslContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);

            if (CollectionUtils.isNotEmpty(orderLineArrCategoryRows)) {
                LOGGER.info("Adding waterfall metrics lines for missing order lines: {}", missingOrderLinesString);
                orderLineMetricsReportingDAO.addBulkOrderLineMetrics(dslContext, renewalOrder.getTenantId(), orderLineArrCategoryRows);
            }

            if (CollectionUtils.isNotEmpty(transactionalArrMetricsRows)) {
                LOGGER.info("Adding transactional metrics lines for missing order lines: {}", missingOrderLinesString);

                transactionalMetricsReportingDAO.addBulkTransactionalMetrics(dslContext, renewalOrder.getTenantId(), transactionalArrMetricsRows);
            }
        });
    }

    private void addDebookLineForMissingOrderLine(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        Order renewalOrder,
        OrderLineItem missingOrderLineItem,
        Charge charge,
        LineItemMetrics metrics,
        LocalDateTime timestamp,
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows,
        List<TransactionalArrMetricsRow> transactionalArrMetricsRows
    ) {
        addRow(
            baseRowBuilder,
            OrderLineArrCategory.DEBOOK,
            missingOrderLineItem.getEndDate(),
            metrics.arr().multiply(NEGATIVE_ONE),
            orderLineArrCategoryRows,
            missingOrderLineItem.getQuantity() * NEGATIVE_ONE.intValue()
        );

        addTransactionalRow(
            renewalOrder,
            missingOrderLineItem,
            OrderLineArrCategory.DEBOOK,
            metrics.arr().multiply(NEGATIVE_ONE),
            missingOrderLineItem.getEndDate(),
            charge.getType(),
            timestamp,
            transactionalArrMetricsRows
        );

        LOGGER.info(
            "Adding debook line for missing order line: {} for order: {}",
            missingOrderLineItem.getOrderLineId(),
            missingOrderLineItem.getOrderId()
        );
    }

    private void addExpirationOffsetRowForMissingRenewalOrderLine(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        OrderLineItem missingOrderLineItem,
        LineItemMetrics metrics,
        List<OrderLineArrCategoryRow> rowsToBeAdded
    ) {
        addRow(
            baseRowBuilder,
            OrderLineArrCategory.EXPIRATION,
            missingOrderLineItem.getEndDate(),
            metrics.arr(),
            rowsToBeAdded,
            missingOrderLineItem.getQuantity()
        );
    }

    private List<OrderLineArrCategoryRow> getPendingRenewalOffsetRowsForMissedRenewalLine(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        OrderLineItem missingOrderLineItem,
        Instant executedOn,
        LineItemMetrics metrics
    ) {
        List<OrderLineArrCategoryRow> rowsToAdd = new ArrayList<>();
        TimeZone tenantTimeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Instant orderLineEffectiveDate = missingOrderLineItem.getEndDate().isAfter(executedOn) ? missingOrderLineItem.getEndDate() : executedOn;

        ZonedDateTime zonedPendingRenewalOffsetStart = orderLineEffectiveDate.atZone(tenantTimeZone.toZoneId());
        List<OrderLineArrCategoryRow> pendingRenewalRows = orderLineMetricsReportingDAO.getArrRowsForCategoryForOrderLineId(
            missingOrderLineItem.getTenantId(),
            missingOrderLineItem.getOrderLineId(),
            OrderLineArrCategory.PENDING_RENEWAL,
            Optional.empty(),
            Optional.empty()
        );
        if (CollectionUtils.isEmpty(pendingRenewalRows)) {
            return rowsToAdd;
        }

        getPendingRenewalOffsetRows(
            missingOrderLineItem,
            rowsToAdd,
            metrics,
            baseRowBuilder,
            tenantTimeZone,
            zonedPendingRenewalOffsetStart,
            pendingRenewalRows
        );

        return rowsToAdd;
    }

    private void getPendingRenewalOffsetRows(
        OrderLineItem orderLineItem,
        List<OrderLineArrCategoryRow> rows,
        LineItemMetrics metrics,
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        TimeZone tenantTimeZone,
        ZonedDateTime zonedPendingRenewalOffsetStart,
        List<OrderLineArrCategoryRow> existingPendingRenewalRows
    ) {
        String pendingRenewalOffsetStartPeriod = getPeriodFormat(zonedPendingRenewalOffsetStart);
        ZonedDateTime zonedPendingRenewalOffset = zonedPendingRenewalOffsetStart;
        for (OrderLineArrCategoryRow pendingRenewalRow : existingPendingRenewalRows) {
            ZonedDateTime zonedPendingRenewalEffectiveDate = pendingRenewalRow.getEffectiveDate().atZone(tenantTimeZone.toZoneId());
            String pendingRenewalRowPeriod = getPeriodFormat(zonedPendingRenewalEffectiveDate);
            if (pendingRenewalRowPeriod.compareTo(pendingRenewalOffsetStartPeriod) < 0) {
                continue;
            }

            addRow(
                baseRowBuilder,
                OrderLineArrCategory.PENDING_RENEWAL,
                zonedPendingRenewalOffset.toInstant(),
                metrics.arr().multiply(NEGATIVE_ONE),
                rows,
                orderLineItem.getQuantity() * -1
            );
            zonedPendingRenewalOffset = zonedPendingRenewalOffset.plusMonths(1);
        }
    }

    public List<OrderLineItem> getMissingRenewalLineItems(Order order) {
        Subscription subscription = subscriptionGetService.find(order.getRenewalForSubscriptionId(), order.getRenewalForSubscriptionVersion());
        Map<String, SubscriptionCharge> subscriptionChargeMap = subscription
            .getCharges()
            .stream()
            .filter(charge -> charge.getEndDate().equals(order.getStartDate()))
            .collect(Collectors.toMap(SubscriptionCharge::getSubscriptionChargeId, Function.identity()));
        List<String> renewedSubscriptionCharges = order
            .getLineItemsNetEffect()
            .stream()
            .filter(orderLineItem -> orderLineItem.getAction() == ActionType.RENEWAL)
            .map(OrderLineItem::getBaseExternalSubscriptionChargeId)
            .toList();

        List<OrderLineItem> missingOrderLines = new ArrayList<>();
        Set<String> missingRenewalCharges = new HashSet<>(subscriptionChargeMap.keySet());
        renewedSubscriptionCharges.forEach(missingRenewalCharges::remove);
        if (CollectionUtils.isEmpty(missingRenewalCharges)) {
            LOGGER.info("No missing renewal charges found for sub: {}", order.getRenewalForSubscriptionId());
            return missingOrderLines;
        }

        for (String missingRenewalChargeId : missingRenewalCharges) {
            Optional<OrderLineItem> orderLineItem = subscriptionOrderService.getOrderLineItemFromSubscriptionChargeId(missingRenewalChargeId);
            if (orderLineItem.isEmpty()) {
                LOGGER.warn(
                    "Couldn't find the orderLine for subscriptionCharge: {} for subscription: {}",
                    missingRenewalChargeId,
                    order.getRenewalForSubscriptionId()
                );
            } else {
                missingOrderLines.add(orderLineItem.get());
            }
        }

        List<String> missedRenewalOrderIds = missingOrderLines.stream().map(OrderLineItem::getOrderLineId).toList();
        LOGGER.info(
            "Orderlines missed renewals: {} from subscription: {}",
            String.join(", ", missedRenewalOrderIds),
            order.getRenewalForSubscriptionId()
        );
        return missingOrderLines;
    }

    public void updateOrderLineArrCategory(String orderId, String orderLineItemId) {
        var orderOptional = orderGetService.getOrderByOrderIdOptional(orderId);
        if (orderOptional.isEmpty()) {
            LOGGER.warn("Order not found: ", orderId);
            return;
        }

        var lineItemOptional = orderGetService.getOrderLineItemByOrderLineItemId(orderLineItemId);
        if (lineItemOptional.isEmpty()) {
            LOGGER.warn("OrderLineItemId not found: ", orderLineItemId);
            return;
        }
        var lineItem = lineItemOptional.get();
        updateOrderLineArrCategory(orderOptional.get(), lineItem);
    }

    public void updateOrderLineArrCategory(Order order, OrderLineItem lineItem) {
        LocalDateTime now = LocalDateTime.now();
        Metrics metrics = metricsService.getOrderLineMetrics(lineItem.getOrderLineId());
        Charge charge = productCatalogGetService.getChargeByChargeId(lineItem.getChargeId());

        Instant effectiveDate = getLineItemEffectiveDate(order, lineItem);
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows = new ArrayList<>();
        List<TransactionalArrMetricsRow> transactionalArrMetricsRows = new ArrayList<>();
        getOrderLineItemCategoryRows(order, lineItem, charge, now, metrics, effectiveDate, orderLineArrCategoryRows, transactionalArrMetricsRows);

        DSLContext nonTenantDslContext = nonRLSContextProvider.get();
        nonTenantDslContext.transaction(configuration ->
            updateArrMetricsTransaction(order, lineItem, orderLineArrCategoryRows, transactionalArrMetricsRows, configuration)
        );

        if (CollectionUtils.isNotEmpty(transactionalArrMetricsRows)) {
            crmService.pushTransactionalArrMetricsSync(order.getOrderId());
        }
    }

    private void updateArrMetricsTransaction(
        Order order,
        OrderLineItem lineItem,
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows,
        List<TransactionalArrMetricsRow> transactionalArrMetricsRows,
        Configuration configuration
    ) {
        DSLContext dslContext = DSL.using(configuration);
        orderLineMetricsReportingDAO.deleteRows(dslContext, order.getTenantId(), List.of(lineItem.getOrderLineId()));

        if (CollectionUtils.isNotEmpty(orderLineArrCategoryRows)) {
            orderLineMetricsReportingDAO.addBulkOrderLineMetrics(dslContext, order.getTenantId(), orderLineArrCategoryRows);
        }

        if (CollectionUtils.isNotEmpty(transactionalArrMetricsRows)) {
            transactionalMetricsReportingDAO.deleteRows(dslContext, order.getTenantId(), List.of(lineItem.getOrderLineId()));
            transactionalMetricsReportingDAO.addBulkTransactionalMetrics(dslContext, order.getTenantId(), transactionalArrMetricsRows);
        }
    }

    void getOrderLineItemCategoryRows(
        Order order,
        OrderLineItem orderLineItem,
        Charge charge,
        LocalDateTime timestamp,
        Metrics metrics,
        Instant lineItemEffectiveDate,
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows,
        List<TransactionalArrMetricsRow> transactionalArrMetricsRows
    ) {
        TenantSetting tenantSetting = tenantSettingService.getTenantSetting();
        // usage arr is calculated only when invoice is posted or voided
        if (BooleanUtils.isNotTrue(charge.getShouldTrackArr()) || charge.getType() == ChargeType.USAGE) {
            return;
        }

        var orderLineItemAction = orderLineItem.getAction();
        var baseRowBuilder = ImmutableOrderLineArrCategoryRow.builder()
            .tenantId(order.getTenantId())
            .orderLineItemId(orderLineItem.getOrderLineId())
            .orderId(order.getOrderId())
            .planId(orderLineItem.getPlanId())
            .chargeId(orderLineItem.getChargeId())
            .generatedBy(ArrMetricsGeneratedBy.SYSTEM)
            .chargeType(charge.getType().name())
            .accountId(order.getAccountId())
            .subscriptionId(order.getExternalSubscriptionId())
            .insertedOn(timestamp)
            .orderExecutedOn(order.getExecutedOn());

        boolean orderLineStartsWithOrder = orderLineItem.getEffectiveDate().equals(order.getStartDate());
        if (order.getOrderType() == OrderType.NEW && orderLineStartsWithOrder) {
            var category = OrderLineArrCategory.NEW;
            addRow(baseRowBuilder, category, lineItemEffectiveDate, metrics.getEntryArr(), orderLineArrCategoryRows, orderLineItem.getQuantity());
            addTransactionalRow(
                order,
                orderLineItem,
                category,
                metrics.getEntryArr(),
                orderLineItem.getEffectiveDate(),
                charge.getType(),
                timestamp,
                transactionalArrMetricsRows
            );
        }

        if (order.getOrderType() == OrderType.RESTRUCTURE && orderLineItem.getAction() == ActionType.RESTRUCTURE) {
            Optional<OrderLineArrCategory> category = getOrderLineArrCategoryForCancelRestructure(order, orderLineItem);

            category.ifPresent(orderLineArrCategory -> {
                addRow(
                    baseRowBuilder,
                    orderLineArrCategory,
                    lineItemEffectiveDate,
                    metrics.getEntryArr(),
                    orderLineArrCategoryRows,
                    orderLineItem.getQuantity()
                );
                addTransactionalRow(
                    order,
                    orderLineItem,
                    orderLineArrCategory,
                    metrics.getEntryArr(),
                    orderLineItem.getEffectiveDate(),
                    charge.getType(),
                    timestamp,
                    transactionalArrMetricsRows
                );
            });
        }

        if (order.getOrderType() != OrderType.CANCEL && orderLineItemAction != ActionType.NONE) {
            addOpeningBalanceRows(
                baseRowBuilder,
                orderLineItem,
                lineItemEffectiveDate,
                metrics,
                orderLineArrCategoryRows,
                tenantSetting.getDefaultTimeZone()
            );
            addPendingRenewalRows(baseRowBuilder, order, orderLineItem, metrics, orderLineArrCategoryRows, tenantSetting.getDefaultTimeZone());
        }

        if (order.getOrderType() == OrderType.RENEWAL && orderLineItemAction == ActionType.ADD && orderLineStartsWithOrder) {
            var category = OrderLineArrCategory.RENEWAL_ADD_ON;
            addRow(baseRowBuilder, category, lineItemEffectiveDate, metrics.getEntryArr(), orderLineArrCategoryRows, orderLineItem.getQuantity());
            addTransactionalRow(
                order,
                orderLineItem,
                category,
                metrics.getEntryArr(),
                orderLineItem.getEffectiveDate(),
                charge.getType(),
                timestamp,
                transactionalArrMetricsRows
            );
        }

        if (order.getOrderType() == OrderType.AMENDMENT && orderLineItemAction == ActionType.ADD && orderLineStartsWithOrder) {
            var category = OrderLineArrCategory.ADD_ON;
            addRow(baseRowBuilder, category, lineItemEffectiveDate, metrics.getEntryArr(), orderLineArrCategoryRows, orderLineItem.getQuantity());
            addTransactionalRow(
                order,
                orderLineItem,
                category,
                metrics.getEntryArr(),
                orderLineItem.getEffectiveDate(),
                charge.getType(),
                timestamp,
                transactionalArrMetricsRows
            );
        }

        if (
            (order.getOrderType() == OrderType.AMENDMENT && (orderLineItemAction == ActionType.UPDATE || orderLineItemAction == ActionType.REMOVE)) ||
            (order.getOrderType() == OrderType.AMENDMENT && orderLineItemAction == ActionType.ADD && !orderLineStartsWithOrder) ||
            (order.getOrderType() == OrderType.NEW && orderLineItemAction == ActionType.ADD && !orderLineStartsWithOrder) ||
            (order.getOrderType() == OrderType.RENEWAL && orderLineItemAction == ActionType.ADD && !orderLineStartsWithOrder) ||
            (order.getOrderType() == OrderType.RESTRUCTURE && orderLineItemAction == ActionType.ADD)
        ) {
            addRowsAddedOrUpdatedInMiddleOfSubscription(
                baseRowBuilder,
                order,
                orderLineItem,
                metrics,
                lineItemEffectiveDate,
                charge,
                timestamp,
                orderLineArrCategoryRows,
                transactionalArrMetricsRows
            );
        }

        if (order.getOrderType() == OrderType.RENEWAL && orderLineItemAction == ActionType.RENEWAL) {
            addReactivationRow(baseRowBuilder, orderLineItem, lineItemEffectiveDate, orderLineArrCategoryRows, tenantSetting.getDefaultTimeZone());
            addRenewalRow(
                baseRowBuilder,
                order,
                orderLineItem,
                lineItemEffectiveDate,
                metrics,
                charge,
                timestamp,
                orderLineArrCategoryRows,
                transactionalArrMetricsRows
            );
            addExpirationOffset(baseRowBuilder, orderLineItem, lineItemEffectiveDate, orderLineArrCategoryRows, tenantSetting.getDefaultTimeZone());
            addRowsToOffsetPreviousPendingRenewals(
                order,
                orderLineItem,
                orderLineArrCategoryRows,
                tenantSetting.getDefaultTimeZone(),
                charge.getType()
            );
        }

        if (order.getOrderType() == OrderType.CANCEL) {
            if (StringUtils.isBlank(order.getCompositeOrderId())) {
                addTerminationRow(
                    baseRowBuilder,
                    orderLineItem,
                    lineItemEffectiveDate,
                    metrics.getExitArr(),
                    orderLineArrCategoryRows,
                    tenantSetting.getDefaultTimeZone()
                );
                addTransactionalRow(
                    order,
                    orderLineItem,
                    OrderLineArrCategory.TERMINATION,
                    metrics.getEntryArr(),
                    orderLineItem.getEffectiveDate(),
                    charge.getType(),
                    timestamp,
                    transactionalArrMetricsRows
                );
            } else {
                Optional<OrderLineArrCategory> category = getOrderLineArrCategoryForCancelRestructure(order, orderLineItem);

                category.ifPresent(orderLineArrCategory -> {
                    addRow(
                        baseRowBuilder,
                        orderLineArrCategory,
                        lineItemEffectiveDate,
                        metrics.getEntryArr(),
                        orderLineArrCategoryRows,
                        orderLineItem.getQuantity()
                    );
                    addTransactionalRow(
                        order,
                        orderLineItem,
                        orderLineArrCategory,
                        metrics.getEntryArr(),
                        orderLineItem.getEffectiveDate(),
                        charge.getType(),
                        timestamp,
                        transactionalArrMetricsRows
                    );
                });
            }

            addOpeningBalanceRows(
                baseRowBuilder,
                orderLineItem,
                lineItemEffectiveDate,
                metrics,
                orderLineArrCategoryRows,
                tenantSetting.getDefaultTimeZone()
            );
            addRowsToOffsetPreviousPendingRenewals(
                order,
                orderLineItem,
                orderLineArrCategoryRows,
                tenantSetting.getDefaultTimeZone(),
                charge.getType()
            );
        }

        addExpirationOrDebookRow(
            baseRowBuilder,
            order,
            orderLineItem,
            metrics,
            charge,
            orderLineArrCategoryRows,
            transactionalArrMetricsRows,
            timestamp,
            tenantSetting.getDefaultTimeZone()
        );
    }

    private void addTransactionalRow(
        Order order,
        OrderLineItem orderLineItem,
        OrderLineArrCategory category,
        BigDecimal arr,
        Instant effectiveDate,
        ChargeType chargeType,
        LocalDateTime timeStamp,
        List<TransactionalArrMetricsRow> transactionalArrMetricsRows
    ) {
        ImmutableTransactionalArrMetricsRow row = ImmutableTransactionalArrMetricsRow.builder()
            .tenantId(order.getTenantId())
            .accountId(order.getAccountId())
            .orderId(order.getOrderId())
            .orderLineItemId(orderLineItem.getOrderLineId())
            .planId(orderLineItem.getPlanId())
            .chargeId(orderLineItem.getChargeId())
            .chargeType(chargeType.name())
            .amount(arr)
            .category(category)
            .insertedOn(timeStamp)
            .subscriptionId(order.getExternalSubscriptionId())
            .effectiveDate(effectiveDate)
            .asOf(order.getExecutedOn())
            .generatedBy(ArrMetricsGeneratedBy.SYSTEM)
            .build();

        transactionalArrMetricsRows.add(row);
    }

    private Optional<OrderLineArrCategory> getOrderLineArrCategoryForCancelRestructure(Order order, OrderLineItem orderLineItem) {
        if (
            StringUtils.isBlank(order.getCompositeOrderId()) ||
            (!(orderLineItem.getAction() == ActionType.REMOVE || orderLineItem.getAction() == ActionType.RESTRUCTURE))
        ) {
            return Optional.empty();
        }

        BigDecimal deltaArrForRestructure = BigDecimal.ZERO;
        long deltaQuantity = 0L;

        List<OrderLineItem> executedOrderLinesForBaseSubscriptionChargeId = StringUtils.isBlank(orderLineItem.getBaseExternalSubscriptionChargeId())
            ? List.of(orderLineItem)
            : orderGetService.getExecutedOrderLinesWithSameBaseSubscriptionChargeId(orderLineItem.getBaseExternalSubscriptionChargeId());

        for (OrderLineItem orderLine : executedOrderLinesForBaseSubscriptionChargeId) {
            deltaQuantity = deltaQuantity + orderLine.getQuantity();
            LineItemMetrics metrics = metricsService.getMetricsForOrderLine(orderLine.getOrderLineId());
            deltaArrForRestructure = deltaArrForRestructure.add(metrics.arr());
        }

        return getMarkupArrCategory(deltaQuantity, deltaArrForRestructure);
    }

    private void addExpirationOrDebookRow(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        Order order,
        OrderLineItem orderLineItem,
        Metrics metrics,
        Charge charge,
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows,
        List<TransactionalArrMetricsRow> transactionalArrMetricsRows,
        LocalDateTime timeStamp,
        TimeZone timeZone
    ) {
        // Any newly added ramp segments which are not ending on the last day of the subscription shall skip the expiration rows
        if (orderLineItem.getIsRamp() && orderLineItem.getAction() == ActionType.ADD && !orderLineItem.getEndDate().equals(order.getEndDate())) {
            return;
        }

        BigDecimal dropOffArr = metrics.getEntryArr().negate();
        if (!orderLineItem.getIsRamp() && orderLineItem.getEndDate().isBefore(order.getEndDate())) {
            addRow(
                baseRowBuilder,
                OrderLineArrCategory.DEBOOK,
                orderLineItem.getEndDate(),
                dropOffArr,
                orderLineArrCategoryRows,
                orderLineItem.getQuantity()
            );
            addTransactionalRow(
                order,
                orderLineItem,
                OrderLineArrCategory.DEBOOK,
                dropOffArr,
                orderLineItem.getEndDate(),
                charge.getType(),
                timeStamp,
                transactionalArrMetricsRows
            );
            return;
        }

        if (order.getOrderType() == OrderType.CANCEL) {
            ZonedDateTime executedOnZonedTime = order.getExecutedOn().atZone(timeZone.toZoneId());
            String executedOnPeriod = getPeriodFormat(executedOnZonedTime);

            ZonedDateTime lineEndDateZonedTime = orderLineItem.getEndDate().atZone(timeZone.toZoneId());
            String lineEndDatePeriod = getPeriodFormat(lineEndDateZonedTime);
            if (executedOnPeriod.compareTo(lineEndDatePeriod) > 0) {
                return;
            }
        }

        OrderLineArrCategory arrCategory = OrderLineArrCategory.EXPIRATION;
        if (orderLineItem.getIsRamp() && order.getOrderType() == OrderType.CANCEL && !orderLineItem.getEndDate().equals(order.getEndDate())) {
            arrCategory = OrderLineArrCategory.TERMINATION;

            // add the negative offset for termination for the ramp rows that end in the middle
            // for the transactional metrics to compensate for the below expiration
            // todo: add e2e test for ramps with duplicated lines with cancel
            addTransactionalRow(
                order,
                orderLineItem,
                arrCategory,
                dropOffArr,
                orderLineItem.getEndDate(),
                charge.getType(),
                timeStamp,
                transactionalArrMetricsRows
            );
        }

        addRow(baseRowBuilder, arrCategory, orderLineItem.getEndDate(), dropOffArr, orderLineArrCategoryRows, orderLineItem.getQuantity() * -1);
    }

    private void addRowsAddedOrUpdatedInMiddleOfSubscription(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        Order order,
        OrderLineItem orderLineItem,
        Metrics metrics,
        Instant lineItemEffectiveDate,
        Charge charge,
        LocalDateTime timestamp,
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows,
        List<TransactionalArrMetricsRow> transactionalArrMetricsRows
    ) {
        var processedArrCategoryOptional = processUpdatedOrAddedMiddleOfSubscriptionRow(order, orderLineItem, metrics);
        if (processedArrCategoryOptional.isEmpty()) {
            return;
        }

        var processedRow = processedArrCategoryOptional.get();
        addRow(
            baseRowBuilder,
            processedRow.category(),
            lineItemEffectiveDate,
            processedRow.amount(),
            orderLineArrCategoryRows,
            processedRow.quantity()
        );
        addTransactionalRow(
            order,
            orderLineItem,
            processedRow.category(),
            processedRow.amount(),
            orderLineItem.getEffectiveDate(),
            charge.getType(),
            timestamp,
            transactionalArrMetricsRows
        );

        // If the order is an amendment order and subscription is already renewed, we need to add the debook category for the Orderline to bring back the ARR down
        if (order.getOrderType() == OrderType.AMENDMENT) {
            // need to find if the subscription before this order has been renewed or not
            Subscription subscription = subscriptionGetService.find(order.getExternalSubscriptionId(), order.getSubscriptionTargetVersion() - 1);

            if (StringUtils.isNotBlank(subscription.getRenewedToSubscriptionId())) {
                ImmutableTransactionalArrMetricsRow debookRow = ImmutableTransactionalArrMetricsRow.builder()
                    .tenantId(order.getTenantId())
                    .accountId(order.getAccountId())
                    .orderId(order.getOrderId())
                    .orderLineItemId(orderLineItem.getOrderLineId())
                    .planId(orderLineItem.getPlanId())
                    .chargeId(orderLineItem.getChargeId())
                    .chargeType(charge.getType().name())
                    .amount(processedRow.amount().negate())
                    .category(OrderLineArrCategory.DEBOOK)
                    .insertedOn(timestamp)
                    .subscriptionId(subscription.getRenewedToSubscriptionId())
                    .effectiveDate(orderLineItem.getEndDate())
                    .asOf(order.getExecutedOn())
                    .generatedBy(ArrMetricsGeneratedBy.SYSTEM)
                    .build();

                transactionalArrMetricsRows.add(debookRow);
            }
        }
    }

    private void addRenewalRow(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        Order order,
        OrderLineItem orderLineItem,
        Instant lineItemEffectiveDate,
        Metrics metrics,
        Charge charge,
        LocalDateTime timestamp,
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows,
        List<TransactionalArrMetricsRow> transactionalArrMetricsRows
    ) {
        var processedArrCategoryOptional = processRenewalRow(orderLineItem, metrics);
        if (processedArrCategoryOptional.isEmpty()) {
            return;
        }

        var renewalRow = processedArrCategoryOptional.get();
        addRow(baseRowBuilder, renewalRow.category(), lineItemEffectiveDate, renewalRow.amount(), orderLineArrCategoryRows, renewalRow.quantity());
        addTransactionalRow(
            order,
            orderLineItem,
            renewalRow.category(),
            renewalRow.amount(),
            orderLineItem.getEffectiveDate(),
            charge.getType(),
            timestamp,
            transactionalArrMetricsRows
        );
    }

    private void addReactivationRow(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        OrderLineItem orderLineItem,
        Instant lineItemEffectiveDate,
        List<OrderLineArrCategoryRow> rows,
        TimeZone timeZone
    ) {
        if (orderLineItem.getAction() != ActionType.RENEWAL) {
            return;
        }

        Optional<OrderLineItem> originalOrderLineOptional = subscriptionOrderService.getAmendedOrRenewedFromLineItem(orderLineItem);
        if (originalOrderLineOptional.isEmpty()) {
            LOGGER.warn(
                "Couldn't find the original line for id: {} and so arr data could be not right for order: {} for reactivation row",
                orderLineItem.getOrderLineId(),
                orderLineItem.getOrderId()
            );
            return;
        }
        OrderLineItem originalOrderLineItem = originalOrderLineOptional.get();

        ZonedDateTime lineItemEffectiveDateAtZone = lineItemEffectiveDate.atZone(timeZone.toZoneId());
        String lineItemEffectivePeriod = getPeriodFormat(lineItemEffectiveDateAtZone);
        ZonedDateTime originalOrderLineEndDate = originalOrderLineItem.getEndDate().atZone(timeZone.toZoneId());
        String originalOrderLineEndDatePeriod = getPeriodFormat(originalOrderLineEndDate);

        if (lineItemEffectivePeriod.compareTo(originalOrderLineEndDatePeriod) <= 0) {
            return;
        }

        LineItemMetrics metrics = metricsService.getMetricsForOrderLine(originalOrderLineItem.getOrderLineId());
        addRow(baseRowBuilder, OrderLineArrCategory.REACTIVATION, lineItemEffectiveDate, metrics.arr(), rows, originalOrderLineItem.getQuantity());
    }

    private void addTerminationRow(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        OrderLineItem orderLineItem,
        Instant lineItemEffectiveDate,
        BigDecimal exitArr,
        List<OrderLineArrCategoryRow> rows,
        TimeZone timeZone
    ) {
        ZonedDateTime lineItemEffectiveDateAtZone = lineItemEffectiveDate.atZone(timeZone.toZoneId());
        String lineItemEffectivePeriod = getPeriodFormat(lineItemEffectiveDateAtZone);
        ZonedDateTime orderLineEndDateAtZone = orderLineItem.getEndDate().atZone(timeZone.toZoneId());
        String orderLineEndDatePeriod = getPeriodFormat(orderLineEndDateAtZone);

        if (lineItemEffectivePeriod.compareTo(orderLineEndDatePeriod) <= 0) {
            addRow(baseRowBuilder, OrderLineArrCategory.TERMINATION, lineItemEffectiveDate, exitArr, rows, orderLineItem.getQuantity());
        }
    }

    private void addExpirationOffset(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        OrderLineItem orderLineItem,
        Instant lineItemEffectiveDate,
        List<OrderLineArrCategoryRow> rows,
        TimeZone timeZone
    ) {
        Optional<OrderLineItem> originalLineItemOptional = subscriptionOrderService.getAmendedOrRenewedFromLineItem(orderLineItem);
        if (originalLineItemOptional.isEmpty()) {
            LOGGER.warn(
                "Couldn't find the original line for id: {} and so arr data could be not right for order: {} for expiration offset rows",
                orderLineItem.getOrderLineId(),
                orderLineItem.getOrderId()
            );
            return;
        }
        OrderLineItem originalOrderLineItem = originalLineItemOptional.get();

        ZonedDateTime lineItemEffectiveDateAtZone = lineItemEffectiveDate.atZone(timeZone.toZoneId());
        String lineItemEffectivePeriod = getPeriodFormat(lineItemEffectiveDateAtZone);

        ZonedDateTime originalOrderLineEndDateAtZone = originalOrderLineItem.getEndDate().atZone(timeZone.toZoneId());
        String originalOrderLineEndDatePeriod = getPeriodFormat(originalOrderLineEndDateAtZone);

        if (lineItemEffectivePeriod.compareTo(originalOrderLineEndDatePeriod) > 0) {
            // No expiration offset needed for lines which are executed after the last end date.
            return;
        }

        LineItemMetrics metrics = metricsService.getMetricsForOrderLine(originalOrderLineItem.getOrderLineId());
        Instant cancelEffectiveDate = lineItemEffectiveDate.isBefore(originalOrderLineItem.getEndDate())
            ? originalOrderLineItem.getEndDate()
            : lineItemEffectiveDate;
        addRow(baseRowBuilder, OrderLineArrCategory.EXPIRATION, cancelEffectiveDate, metrics.arr(), rows, originalOrderLineItem.getQuantity());
    }

    private void addRowsToOffsetPreviousPendingRenewals(
        Order order,
        OrderLineItem orderLineItem,
        List<OrderLineArrCategoryRow> rows,
        TimeZone tenantTimeZone,
        ChargeType chargeType
    ) {
        // When removing a line item, we need to make sure this line is removed till the end
        if (orderLineItem.getAction() == ActionType.REMOVE && orderLineItem.getEndDate().isBefore(order.getEndDate())) {
            return;
        }

        Optional<OrderLineItem> originalOrderLineOptional = subscriptionOrderService.getAmendedOrRenewedFromLineItem(orderLineItem);
        if (originalOrderLineOptional.isEmpty()) {
            LOGGER.warn(
                "Couldn't find the original line for id: {} and so arr data could be not right for order: {} for pending renewals offset",
                orderLineItem.getOrderLineId(),
                orderLineItem.getOrderId()
            );
            return;
        }

        OrderLineItem originalOrderLine = originalOrderLineOptional.get();
        LineItemMetrics metrics = metricsService.getMetricsForOrderLine(originalOrderLine.getOrderLineId());
        var baseRowBuilder = ImmutableOrderLineArrCategoryRow.builder()
            .tenantId(orderLineItem.getTenantId())
            .orderLineItemId(orderLineItem.getOrderLineId())
            .orderId(orderLineItem.getOrderId())
            .planId(orderLineItem.getPlanId())
            .chargeId(orderLineItem.getChargeId())
            .accountId(order.getAccountId())
            .subscriptionId(order.getExternalSubscriptionId())
            .generatedBy(ArrMetricsGeneratedBy.SYSTEM)
            .chargeType(chargeType.name())
            .insertedOn(LocalDateTime.now())
            .orderExecutedOn(order.getExecutedOn()); // For the Pending Renewal Offset lines, orderExecutedOn date will be its renewal orders executed on date

        Instant orderLineEffectiveDate = order.getExecutedOn().isAfter(originalOrderLine.getEndDate())
            ? order.getExecutedOn()
            : originalOrderLine.getEndDate();
        ZonedDateTime zonedPendingRenewalOffsetStart = orderLineEffectiveDate.atZone(tenantTimeZone.toZoneId());
        List<OrderLineArrCategoryRow> pendingRenewalRows = orderLineMetricsReportingDAO.getArrRowsForCategoryForOrderLineId(
            order.getTenantId(),
            originalOrderLine.getOrderLineId(),
            OrderLineArrCategory.PENDING_RENEWAL,
            Optional.empty(),
            Optional.empty()
        );
        if (CollectionUtils.isEmpty(pendingRenewalRows)) {
            return;
        }

        ArrMetricsReportingConfiguration arrConfig = getTenantScopedArrReportingConfig(order.getTenantId());
        if (pendingRenewalRows.size() > arrConfig.getPendingRenewalMonths() * MAX_TIMES_FOR_PENDING_RENEWALS) {
            LOGGER.warn(
                "Need manual verification: OrderLine: {} has more than {} pending renewal rows and hence skipping generating pending renewals.",
                originalOrderLine.getOrderLineId(),
                arrConfig.getPendingRenewalMonths()
            );
            return;
        }

        getPendingRenewalOffsetRows(
            originalOrderLine,
            rows,
            metrics,
            baseRowBuilder,
            tenantTimeZone,
            zonedPendingRenewalOffsetStart,
            pendingRenewalRows
        );
    }

    public static Instant getLineItemEffectiveDate(Order order, OrderLineItem orderLineItem) {
        Instant executionDate = order.getExecutedOn();
        Instant serviceStartDate = orderLineItem.getEffectiveDate();
        if (executionDate == null) {
            return serviceStartDate;
        }

        return executionDate.isAfter(serviceStartDate) ? executionDate : serviceStartDate;
    }

    private static String getPeriodFormat(ZonedDateTime dateTime) {
        return PERIOD_FORMATTER.format(dateTime);
    }

    private void addOpeningBalanceRows(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        OrderLineItem orderLineItem,
        Instant effectiveDate,
        Metrics metrics,
        List<OrderLineArrCategoryRow> rows,
        TimeZone tenantTimeZone
    ) {
        OrderLineArrCategory category = OrderLineArrCategory.OPENING_BALANCE;
        ZonedDateTime endDate = orderLineItem.getEndDate().atZone(tenantTimeZone.toZoneId());
        String endPeriod = getPeriodFormat(endDate);

        ZonedDateTime openingBalanceDate = effectiveDate.atZone(tenantTimeZone.toZoneId()).plusMonths(1);
        var openingBalancePeriod = getPeriodFormat(openingBalanceDate);

        while (openingBalancePeriod.compareTo(endPeriod) <= 0) {
            addRow(baseRowBuilder, category, openingBalanceDate.toInstant(), metrics.getEntryArr(), rows, orderLineItem.getQuantity());
            openingBalanceDate = openingBalanceDate.plusMonths(1);
            openingBalancePeriod = getPeriodFormat(openingBalanceDate);
        }
    }

    private void addPendingRenewalRows(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        Order order,
        OrderLineItem orderLineItem,
        Metrics metrics,
        List<OrderLineArrCategoryRow> rows,
        TimeZone tenantTimeZone
    ) {
        if (orderLineItem.getEndDate().isBefore(order.getEndDate())) {
            // if the order line ends before the order's end date, we shouldn't populate the pending renewal lines
            return;
        }

        addPendingRenewalRowsInternal(
            baseRowBuilder,
            order.getTenantId(),
            orderLineItem.getEndDate(),
            metrics.getEntryArr(),
            orderLineItem.getQuantity(),
            rows,
            tenantTimeZone
        );
    }

    private void addPendingRenewalRowsInternal(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        String tenantId,
        Instant endingDate,
        BigDecimal arr,
        long quantity,
        List<OrderLineArrCategoryRow> rows,
        TimeZone tenantTimeZone
    ) {
        ZonedDateTime pendingRenewalZonedDate = endingDate.atZone(tenantTimeZone.toZoneId());
        ArrMetricsReportingConfiguration reportingConfiguration = getTenantScopedArrReportingConfig(tenantId);

        int pendingRenewalMonths = reportingConfiguration.getPendingRenewalMonths();
        for (int i = 0; i < pendingRenewalMonths; i++) {
            addRow(baseRowBuilder, OrderLineArrCategory.PENDING_RENEWAL, pendingRenewalZonedDate.toInstant(), arr, rows, quantity);
            pendingRenewalZonedDate = pendingRenewalZonedDate.plusMonths(1);
        }
    }

    ArrMetricsReportingConfiguration getTenantScopedArrReportingConfig(String tenantId) {
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        return tenantScopedConfig.getArrMetricsReportingConfiguration();
    }

    private void addRow(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        OrderLineArrCategory category,
        Instant effectiveDate,
        BigDecimal amount,
        List<OrderLineArrCategoryRow> rows,
        long quantity
    ) {
        var rowBuilder = baseRowBuilder.category(category).effectiveDate(effectiveDate).amount(amount).quantity(quantity);
        rows.add(rowBuilder.build());
    }

    private Optional<ProcessedArrCategory> processUpdatedOrAddedMiddleOfSubscriptionRow(Order order, OrderLineItem orderLineItem, Metrics metrics) {
        if (orderLineItem.getAction() == ActionType.ADD || StringUtils.isBlank(orderLineItem.getBaseExternalSubscriptionChargeId())) {
            if (!orderLineItem.getIsRamp()) {
                // This shall process markups to handle duplicate lines added on new or amendment orders
                // for new lines the netArr and netQuantity are the same as the orderLines arr and quantity
                return processMarkups(metrics.getEntryArr(), orderLineItem.getQuantity(), metrics.getEntryArr(), orderLineItem.getQuantity());
            }

            // this is a new ramp line segment added. We need to find the net values based on its previous ramp segment
            return getProcessedArrCategoryForRampSegment(order, orderLineItem, metrics);
        }

        return getProcessedArrCategoryForAmendmentLines(orderLineItem, metrics);
    }

    private Optional<ProcessedArrCategory> getProcessedArrCategoryForAmendmentLines(OrderLineItem orderLineItem, Metrics metrics) {
        List<OrderLineItem> amendmentOrderLines = orderGetService.getExecutedOrderLinesWithSameBaseSubscriptionChargeId(
            orderLineItem.getBaseExternalSubscriptionChargeId()
        );

        long netQuantity = amendmentOrderLines.stream().reduce(0L, (partialResult, orderLine) -> partialResult + orderLine.getQuantity(), Long::sum);

        AtomicReference<BigDecimal> netArr = new AtomicReference<>(BigDecimal.ZERO);
        amendmentOrderLines.forEach(ol -> netArr.set(netArr.get().add(metricsService.getOrderLineMetrics(ol.getOrderLineId()).getEntryArr())));

        // for amendments, markups are actual values
        return processMarkups(metrics.getEntryArr(), orderLineItem.getQuantity(), netArr.get(), netQuantity);
    }

    private Optional<ProcessedArrCategory> getProcessedArrCategoryForRampSegment(Order order, OrderLineItem orderLineItem, Metrics metrics) {
        List<OrderLineItem> sortedRampItems = OrderLineUtils.getSortedRampItems(order, orderLineItem);
        if (CollectionUtils.isEmpty(sortedRampItems)) {
            return Optional.empty();
        }

        OrderLineItem previousRampLine = null;
        for (OrderLineItem currentLine : sortedRampItems) {
            if (currentLine.getOrderLineId().equals(orderLineItem.getOrderLineId())) {
                break;
            }
            previousRampLine = currentLine;
        }

        if (previousRampLine == null) {
            // for first ramp segment, netArr and netQuantity are the same as the orderLines arr and quantity
            return processMarkups(metrics.getEntryArr(), orderLineItem.getQuantity(), metrics.getEntryArr(), orderLineItem.getQuantity());
        }

        Metrics previousLineMetrics = metricsService.getOrderLineMetrics(previousRampLine.getOrderLineId());
        BigDecimal netArr = metrics.getEntryArr().subtract(previousLineMetrics.getExitArr());
        long netQuantity = orderLineItem.getQuantity() - previousRampLine.getQuantity();
        // for ramp lines, the markups are net values
        return processMarkups(netArr, netQuantity, netArr, netQuantity);
    }

    Optional<OrderLineArrCategory> getMarkupArrCategory(long netQuantity, BigDecimal netArr) {
        OrderLineArrCategory category;

        if (netQuantity > 0) {
            category = OrderLineArrCategory.UPSELL;
        } else if (netQuantity < 0) {
            category = OrderLineArrCategory.DOWNSELL;
        } else if (netArr.compareTo(BigDecimal.ZERO) > 0) {
            category = OrderLineArrCategory.MARKUP;
        } else if (netArr.compareTo(BigDecimal.ZERO) < 0) {
            category = OrderLineArrCategory.MARKDOWN;
        } else {
            category = null;
        }

        return Optional.ofNullable(category);
    }

    private Optional<ProcessedArrCategory> processMarkups(BigDecimal arr, long quantity, BigDecimal netArr, long netQuantity) {
        // net values are used to find the category while the actual arr is used to represent the arr value
        // so for new ramp lines, arr should be equal to netArr
        // whereas for amendment lines, arr is either debook or rebook values and netArr will be the actual diff
        Optional<OrderLineArrCategory> optionalCategory = getMarkupArrCategory(netQuantity, netArr);
        if (optionalCategory.isEmpty()) {
            return Optional.empty();
        }

        OrderLineArrCategory category = optionalCategory.get();
        return Optional.of(new ProcessedArrCategory(category, arr, quantity));
    }

    private Optional<ProcessedArrCategory> processRenewalRow(OrderLineItem orderLineItem, Metrics metrics) {
        long quantity = orderLineItem.getQuantity();
        Optional<OrderLineItem> originalOrderLineOptional = subscriptionOrderService.getAmendedOrRenewedFromLineItem(orderLineItem);
        if (originalOrderLineOptional.isEmpty()) {
            LOGGER.warn(
                "Couldn't find the original line for id: {} and so arr data could be not right for order: {} for renewal row",
                orderLineItem.getOrderLineId(),
                orderLineItem.getOrderId()
            );

            // If the original line isn't found, we should categorize it as renewal add on instead of just dropping it off
            return Optional.of(new ProcessedArrCategory(OrderLineArrCategory.RENEWAL_ADD_ON, metrics.getEntryArr(), orderLineItem.getQuantity()));
        }
        OrderLineItem originalOrderLineItem = originalOrderLineOptional.get();

        var prevMetrics = metricsService.getOrderLineMetrics(originalOrderLineItem.getOrderLineId());
        var prevQuantity = originalOrderLineItem.getQuantity();
        var netQuantity = orderLineItem.getQuantity() - prevQuantity;
        var prevArr = prevMetrics.getExitArr();
        var arr = metrics.getEntryArr();
        var netArr = arr.subtract(prevArr);
        OrderLineArrCategory category;

        if (quantity > prevQuantity) {
            category = OrderLineArrCategory.RENEWAL_UPSELL;
        } else if (quantity < prevQuantity) {
            category = OrderLineArrCategory.RENEWAL_DOWNSELL;
        } else if (arr.compareTo(prevArr) > 0) {
            category = OrderLineArrCategory.RENEWAL_MARKUP;
        } else if (arr.compareTo(prevArr) < 0) {
            category = OrderLineArrCategory.RENEWAL_MARKDOWN;
        } else {
            return Optional.empty();
        }

        return Optional.of(new ProcessedArrCategory(category, netArr, netQuantity));
    }

    public void updateLatestOrderLineMetricsTimestamp(String tenantId, LocalDateTime insertedOn) {
        orderLineMetricsReportingDAO.addLatestTimestamp(tenantId, insertedOn);
    }

    public void updateArrMetricsForExternalScheduleId(String externalScheduleId) {
        if (!billyConfiguration.isLocalOrCi()) {
            throw new ForbiddenException("Not allowed.");
        }

        String tenantId = tenantIdProvider.provideTenantIdString();
        ReportingJobQueueEntry jobUnit = new ReportingJobQueueEntry(tenantId, ReportingJobCategory.EXTERNAL_ARR);
        jobUnit.setExternalScheduleId(externalScheduleId);
        addExternalArrMetrics(jobUnit);
    }

    private void addExternalArrMetrics(ReportingJobQueueEntry jobUnit) {
        ExternalArrSchedule externalArrSchedule = externalArrScheduleService.getExternalScheduleByScheduleId(jobUnit.getExternalScheduleId());
        List<OrderLineArrCategoryRow> externalArrCategoryRows = populateExternalArrCategoryRows(externalArrSchedule, jobUnit);

        DSLContext nonTenantDslContext = nonRLSContextProvider.get();
        nonTenantDslContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            orderLineMetricsReportingDAO.deleteExternalArrMetricsByScheduleId(dslContext, jobUnit.getTenantId(), jobUnit.getExternalScheduleId());
            orderLineMetricsReportingDAO.addBulkOrderLineMetrics(dslContext, jobUnit.getTenantId(), externalArrCategoryRows);
        });
    }

    private List<OrderLineArrCategoryRow> populateExternalArrCategoryRows(ExternalArrSchedule externalArrSchedule, ReportingJobQueueEntry jobUnit) {
        List<OrderLineArrCategoryRow> orderLineArrCategoryRows;
        switch (externalArrSchedule.getCategory()) {
            case NEW, ADD_ON, RENEWAL_ADD_ON, UPSELL, DOWNSELL, MARKUP, MARKDOWN, TERMINATION, REACTIVATION -> orderLineArrCategoryRows =
                addExternalArrNoDependentRows(externalArrSchedule, jobUnit.getTenantId());
            case RENEWAL_UPSELL, RENEWAL_DOWNSELL, RENEWAL_MARKUP, RENEWAL_MARKDOWN -> orderLineArrCategoryRows = addExternalArrDependentRows(
                externalArrSchedule,
                jobUnit.getTenantId()
            );
            default -> throw new ServiceFailureException("Unhandled arr job category found: " + externalArrSchedule.getCategory());
        }

        return orderLineArrCategoryRows;
    }

    public List<OrderLineArrCategoryRow> addExternalArrNoDependentRows(ExternalArrSchedule externalArrSchedule, String tenantId) {
        TenantSetting tenantSetting = tenantSettingService.getTenantSetting();
        var baseRowBuilder = ImmutableOrderLineArrCategoryRow.builder()
            .tenantId(tenantId)
            .generatedBy(ArrMetricsGeneratedBy.EXTERNAL)
            .chargeType("RECURRING") // todo: Should we accept the chargeType from the external?
            .externalScheduleId(externalArrSchedule.getScheduleId())
            .insertedOn(LocalDateTime.now())
            .orderExecutedOn(externalArrSchedule.getStartDate());

        List<OrderLineArrCategoryRow> orderLineArrCategoryRows = new ArrayList<>();
        addRow(
            baseRowBuilder,
            externalArrSchedule.getCategory(),
            externalArrSchedule.getStartDate(),
            externalArrSchedule.getAmount(),
            orderLineArrCategoryRows,
            externalArrSchedule.getQuantity()
        );
        addOpeningBalanceRows(
            baseRowBuilder,
            externalArrSchedule.getStartDate(),
            externalArrSchedule.getEndDate(),
            orderLineArrCategoryRows,
            tenantSetting.getDefaultTimeZone(),
            externalArrSchedule.getAmount(),
            externalArrSchedule.getQuantity()
        );
        addRow(
            baseRowBuilder,
            OrderLineArrCategory.EXPIRATION,
            externalArrSchedule.getEndDate(),
            externalArrSchedule.getAmount().multiply(NEGATIVE_ONE),
            orderLineArrCategoryRows,
            externalArrSchedule.getQuantity() * -1
        );

        return orderLineArrCategoryRows;
    }

    private void addOpeningBalanceRows(
        ImmutableOrderLineArrCategoryRow.Builder baseRowBuilder,
        Instant startDate,
        Instant endDate,
        List<OrderLineArrCategoryRow> rows,
        TimeZone tenantTimeZone,
        BigDecimal amount,
        long quantity
    ) {
        OrderLineArrCategory category = OrderLineArrCategory.OPENING_BALANCE;
        ZonedDateTime zonedEndDate = endDate.atZone(tenantTimeZone.toZoneId());
        String endPeriod = getPeriodFormat(zonedEndDate);

        ZonedDateTime openingBalanceDate = startDate.atZone(tenantTimeZone.toZoneId()).plusMonths(1);
        var openingBalancePeriod = getPeriodFormat(openingBalanceDate);

        while (openingBalancePeriod.compareTo(endPeriod) <= 0) {
            addRow(baseRowBuilder, category, openingBalanceDate.toInstant(), amount, rows, quantity);
            openingBalanceDate = openingBalanceDate.plusMonths(1);
            openingBalancePeriod = getPeriodFormat(openingBalanceDate);
        }
    }

    public List<OrderLineArrCategoryRow> addExternalArrDependentRows(ExternalArrSchedule externalArrSchedule, String tenantId) {
        TenantSetting tenantSetting = tenantSettingService.getTenantSetting();
        var baseRowBuilder = ImmutableOrderLineArrCategoryRow.builder()
            .tenantId(tenantId)
            .generatedBy(ArrMetricsGeneratedBy.EXTERNAL)
            .chargeType("RECURRING") // todo: Should we accept the chargeType from the external?
            .externalScheduleId(externalArrSchedule.getScheduleId())
            .insertedOn(LocalDateTime.now())
            .orderExecutedOn(externalArrSchedule.getStartDate());

        ExternalArrSchedule baseExternalSchedule = externalArrScheduleService.getExternalScheduleByScheduleId(
            externalArrSchedule.getPreviousScheduleId()
        );
        OrderLineArrCategoryRow baseExternalArrLine = getBaseExternalArrLine(tenantId, externalArrSchedule.getPreviousScheduleId());

        List<OrderLineArrCategoryRow> orderLineArrCategoryRows = new ArrayList<>();
        if (!externalArrSchedule.getStartDate().isAfter(baseExternalSchedule.getEndDate())) {
            var previousRowBuilder = ImmutableOrderLineArrCategoryRow.builder()
                .tenantId(tenantId)
                .generatedBy(ArrMetricsGeneratedBy.EXTERNAL)
                .chargeType("RECURRING") // todo: Should we accept the chargeType from the external?
                .externalScheduleId(baseExternalSchedule.getScheduleId())
                .insertedOn(LocalDateTime.now())
                .orderExecutedOn(externalArrSchedule.getStartDate());

            addRow(
                previousRowBuilder,
                OrderLineArrCategory.EXPIRATION,
                baseExternalSchedule.getEndDate(),
                baseExternalSchedule.getAmount(),
                orderLineArrCategoryRows,
                baseExternalSchedule.getQuantity()
            );
        }

        addRow(
            baseRowBuilder,
            externalArrSchedule.getCategory(),
            externalArrSchedule.getStartDate(),
            externalArrSchedule.getAmount().subtract(baseExternalArrLine.getAmount()),
            orderLineArrCategoryRows,
            externalArrSchedule.getQuantity() - baseExternalArrLine.getQuantity()
        );

        addOpeningBalanceRows(
            baseRowBuilder,
            externalArrSchedule.getStartDate(),
            externalArrSchedule.getEndDate(),
            orderLineArrCategoryRows,
            tenantSetting.getDefaultTimeZone(),
            externalArrSchedule.getAmount(),
            externalArrSchedule.getQuantity()
        );
        addRow(
            baseRowBuilder,
            OrderLineArrCategory.EXPIRATION,
            externalArrSchedule.getEndDate(),
            externalArrSchedule.getAmount().multiply(NEGATIVE_ONE),
            orderLineArrCategoryRows,
            externalArrSchedule.getQuantity() * -1
        );

        return orderLineArrCategoryRows;
    }

    private OrderLineArrCategoryRow getBaseExternalArrLine(String tenantId, String previousScheduleId) {
        Validator.validateStringNotBlank(previousScheduleId, "previous scheduleId is null");
        Optional<OrderLineArrCategoryRow> optionalRow = orderLineMetricsReportingDAO.getBaseExternalArrLine(tenantId, previousScheduleId);

        if (optionalRow.isEmpty()) {
            LOGGER.warn("Couldn't find the base arr row for external scheduleId: " + previousScheduleId);
            return ImmutableOrderLineArrCategoryRow.builder()
                .generatedBy(ArrMetricsGeneratedBy.EXTERNAL)
                .tenantId(tenantId)
                .chargeType("RECURRING") // doesn't matter whatever we put here
                .insertedOn(LocalDateTime.now())
                .effectiveDate(Instant.now())
                .category(OrderLineArrCategory.NEW) // doesn't matter whatever we put here
                .amount(BigDecimal.ZERO)
                .quantity(0)
                .build();
        }

        return optionalRow.get();
    }

    public void deleteSubscriptionArrData(String tenantId, String subscriptionId) {
        Validator.validateStringNotBlank(tenantId, "tenantId is blank");
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is blank");

        DSLContext nonTenantDslContext = nonRLSContextProvider.get();
        nonTenantDslContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            orderLineMetricsReportingDAO.deleteSubscriptionArrData(dslContext, tenantId, subscriptionId);
            transactionalMetricsReportingDAO.deleteSubscriptionArrData(dslContext, tenantId, subscriptionId);
        });
    }

    public void deleteAccountArrData(String tenantId, String accountId) {
        Validator.validateStringNotBlank(tenantId, "tenantId is blank");
        Validator.validateStringNotBlank(accountId, "accountId is blank");

        DSLContext nonTenantDslContext = nonRLSContextProvider.get();
        nonTenantDslContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            orderLineMetricsReportingDAO.deleteAccountArrData(dslContext, tenantId, accountId);
            transactionalMetricsReportingDAO.deleteAccountArrData(dslContext, tenantId, accountId);
        });
    }
}
