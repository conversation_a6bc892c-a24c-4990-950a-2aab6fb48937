package com.subskribe.billy.admin;

import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.accounting.model.JournalEntry;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.admin.model.InvoiceDeletableResponse;
import com.subskribe.billy.admin.model.PaymentDeletableResponse;
import com.subskribe.billy.admin.model.SubscriptionModifiableResponse;
import com.subskribe.billy.admin.model.SubscriptionTypeUpdateInput;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.compositeorder.service.CompositeOrderService;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoicesettlement.model.PaymentBalance;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.metrics.MetricsUpdater;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.notification.service.ApprovalFlowNotificationService;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.OrderCommentService;
import com.subskribe.billy.order.services.OrderEventService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.payment.services.PaymentService;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.revrec.model.RecognitionTransaction;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.revrec.services.RevenueRecognitionService;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.enums.PaymentState;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.model.SubscriptionType;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionLifecycleScheduleService;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderService;
import com.subskribe.billy.template.services.DocumentCustomContentService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

public class AdminOperationsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminOperationsService.class);

    private final SubscriptionService subscriptionService;
    private final SubscriptionGetService subscriptionGetService;
    private final InvoiceRetrievalService invoiceRetrievalService;
    private final OrderGetService orderGetService;
    private final RevenueRecognitionGetService revenueRecognitionGetService;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final InvoiceService invoiceService;
    private final OrderService orderService;
    private final PlatformFeatureService platformFeatureService;
    private final DocumentCustomContentService documentCustomContentService;
    private final CustomTemplateUpdatedOnOrderService customTemplateUpdatedOnOrderService;
    private final OrderCommentService orderCommentService;
    private final ApprovalFlowNotificationService approvalFlowNotificationService;
    private final ReportingJobQueueService reportingJobQueueService;
    private final OpportunityService opportunityService;
    private final RevenueRecognitionService revenueRecognitionService;
    private final CustomFieldService customFieldService;
    private final AccountService accountService;
    private final SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService;
    private final CrmService crmService;
    private final AccountingGetService accountingGetService;
    private final AccountingService accountingService;
    private final InvoiceSettlementService invoiceSettlementService;
    private final CompositeOrderService compositeOrderService;
    private final CompositeOrderGetService compositeOrderGetService;
    private final OrderEventService orderEventService;
    private final PaymentService paymentService;
    private final PaymentGetService paymentGetService;
    private final MetricsUpdater metricsUpdater;

    @Inject
    public AdminOperationsService(
        SubscriptionService subscriptionService,
        SubscriptionGetService subscriptionGetService,
        InvoiceRetrievalService invoiceRetrievalService,
        OrderGetService orderGetService,
        RevenueRecognitionGetService revenueRecognitionGetService,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider,
        InvoiceService invoiceService,
        OrderService orderService,
        PlatformFeatureService platformFeatureService,
        DocumentCustomContentService documentCustomContentService,
        CustomTemplateUpdatedOnOrderService customTemplateUpdatedOnOrderService,
        OrderCommentService orderCommentService,
        ApprovalFlowNotificationService approvalFlowNotificationService,
        ReportingJobQueueService reportingJobQueueService,
        OpportunityService opportunityService,
        RevenueRecognitionService revenueRecognitionService,
        CustomFieldService customFieldService,
        AccountService accountService,
        SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService,
        CrmService crmService,
        AccountingGetService accountingGetService,
        AccountingService accountingService,
        InvoiceSettlementService invoiceSettlementService,
        CompositeOrderService compositeOrderService,
        CompositeOrderGetService compositeOrderGetService,
        OrderEventService orderEventService,
        PaymentService paymentService,
        PaymentGetService paymentGetService,
        MetricsUpdater metricsUpdater
    ) {
        this.subscriptionService = subscriptionService;
        this.subscriptionGetService = subscriptionGetService;
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.orderGetService = orderGetService;
        this.revenueRecognitionGetService = revenueRecognitionGetService;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.invoiceService = invoiceService;
        this.orderService = orderService;
        this.platformFeatureService = platformFeatureService;
        this.documentCustomContentService = documentCustomContentService;
        this.customTemplateUpdatedOnOrderService = customTemplateUpdatedOnOrderService;
        this.orderCommentService = orderCommentService;
        this.approvalFlowNotificationService = approvalFlowNotificationService;
        this.reportingJobQueueService = reportingJobQueueService;
        this.opportunityService = opportunityService;
        this.revenueRecognitionService = revenueRecognitionService;
        this.customFieldService = customFieldService;
        this.accountService = accountService;
        this.subscriptionLifecycleScheduleService = subscriptionLifecycleScheduleService;
        this.crmService = crmService;
        this.accountingGetService = accountingGetService;
        this.accountingService = accountingService;
        this.invoiceSettlementService = invoiceSettlementService;
        this.compositeOrderService = compositeOrderService;
        this.compositeOrderGetService = compositeOrderGetService;
        this.orderEventService = orderEventService;
        this.paymentService = paymentService;
        this.paymentGetService = paymentGetService;
        this.metricsUpdater = metricsUpdater;
    }

    // Remap a subscription from one type to another, or remap renewed from or restructured from subscriptions
    public void updateSubscriptionType(String subscriptionId, SubscriptionTypeUpdateInput subscriptionTypeUpdateInput) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is required");
        Validator.validateNonNullArgument(subscriptionTypeUpdateInput, "subscriptionTypeUpdateInput is required");
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        Order firstOrderInSubscription = orderGetService.getFirstOrderInSubscription(subscriptionId);

        // todo: check if first order in subscription has been signed. Return error if true. (provided a force / bypass)

        switch (firstOrderInSubscription.getOrderType()) {
            case NEW -> throw new UnsupportedOperationException("Update new subscription type not implemented yet");
            case RENEWAL -> handleUpdateRenewalSubscriptionType(subscription, firstOrderInSubscription, subscriptionTypeUpdateInput);
            case RESTRUCTURE -> handleUpdateRestructureSubscriptionType(subscription, firstOrderInSubscription, subscriptionTypeUpdateInput);
            default -> throwInvalidFirstOrderTypeError(firstOrderInSubscription, subscriptionId);
        }
    }

    // updating subscription type from RENEWAL -> NEW || RESTRUCTURE
    // Since this does not change the order's monetary values and dates, there are no rev rec, accounting or invoice impacts
    // However, since delta ARR may be updated for both the initial renewal order and subscription, these need to be recalculated
    private void handleUpdateRenewalSubscriptionType(
        Subscription subscription,
        Order firstOrderInSubscription,
        SubscriptionTypeUpdateInput subscriptionTypeUpdateInput
    ) {
        if (StringUtils.isBlank(subscription.getRenewedFromSubscriptionId()) || firstOrderInSubscription.getOrderType() != OrderType.RENEWAL) {
            throw new IllegalArgumentException(String.format("Subscription %s is not a renewal subscription", subscription.getSubscriptionId()));
        }

        SubscriptionType updateToType = subscriptionTypeUpdateInput.getUpdateToType();

        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        dslContext.transaction(configuration -> {
            switch (updateToType) {
                case RENEWAL -> updateRenewedFromSubscription(
                    configuration,
                    subscription,
                    firstOrderInSubscription,
                    subscriptionTypeUpdateInput.getRenewedFromSubscriptionId()
                );
                case NEW -> {
                    subscriptionService.updateRenewedFromSubscriptionId(
                        configuration,
                        subscription.getSubscriptionId(),
                        subscription.getRenewedFromSubscriptionId(),
                        Optional.empty()
                    );

                    orderService.setOrderTypeToNew(configuration, firstOrderInSubscription);
                }
                case RESTRUCTURE -> throw new UnsupportedOperationException(
                    "Update subscription type from RENEWAL to RESTRUCTURE directly not supported. Update RENEWAL to NEW first, then RESTRUCTURE."
                );
            }

            triggerTransactionalSubscriptionUpdatedDependencies(dslContext, subscription);
        });

        // this is called after the transaction is committed so that dependencies that are not in the same transactional scope can get the updated data
        triggerNonTransactionalSubscriptionUpdatedDependencies(subscription, firstOrderInSubscription);
    }

    private void triggerTransactionalSubscriptionUpdatedDependencies(DSLContext dslContext, Subscription subscription) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        // stored order metrics are updated with order object update
        metricsUpdater.setSubscriptionMetricsForRecompute(dslContext, tenantId, subscription.getId(), subscription.getSubscriptionId());
    }

    private void triggerNonTransactionalSubscriptionUpdatedDependencies(Subscription subscription, Order firstOrderInSubscription) {
        Order order = orderGetService.getOrderByOrderId(firstOrderInSubscription.getOrderId());
        crmService.pushOrderSync(order);
        // this also triggers ARR update for orders in the subscription
        reportingJobQueueService.addArrJobsForSubscription(subscription.getSubscriptionId());
    }

    private void handleUpdateRestructureSubscriptionType(
        Subscription subscription,
        Order firstOrderInSubscription,
        SubscriptionTypeUpdateInput subscriptionTypeUpdateInput
    ) {
        if (
            StringUtils.isBlank(subscription.getRestructuredFromSubscriptionId()) || firstOrderInSubscription.getOrderType() != OrderType.RESTRUCTURE
        ) {
            throw new IllegalArgumentException(String.format("Subscription %s is not a restructure subscription", subscription.getSubscriptionId()));
        }

        SubscriptionType updateToType = subscriptionTypeUpdateInput.getUpdateToType();

        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        dslContext.transaction(configuration -> {
            switch (updateToType) {
                case RENEWAL -> throw new UnsupportedOperationException(
                    "Update subscription type from RESTRUCTURE to RENEWAL directly not supported. Update RESTRUCTURE to NEW first, then RENEWAL."
                );
                case NEW -> {
                    // todo: move CRM ID to new order if it exists on composite order, also update opportunity to use new order as primary if applicable
                    orderService.setOrderTypeToNew(configuration, firstOrderInSubscription);
                    subscriptionService.clearRestructuredFromSubscriptionId(
                        configuration,
                        subscription.getSubscriptionId(),
                        subscription.getRestructuredFromSubscriptionId()
                    );
                    compositeOrderService.splitCompositeOrder(configuration, firstOrderInSubscription.getCompositeOrderId());
                }
                case RESTRUCTURE -> throw new UnsupportedOperationException(
                    "Update subscription type from RESTRUCTURE to RESTRUCTURE not yet implemented."
                );
            }

            triggerTransactionalSubscriptionUpdatedDependencies(dslContext, subscription);
        });

        // this is called after the transaction is committed so that dependencies that are not in the same transactional scope can get the updated data
        triggerNonTransactionalSubscriptionUpdatedDependencies(subscription, firstOrderInSubscription);
    }

    // remaps a subscriptions renewedFromSubscriptionId to a different subscription. Only subscription that renewed from another subscription can be updated.
    private void updateRenewedFromSubscription(
        Configuration configuration,
        Subscription subscription,
        Order firstOrderInSubscription,
        String updatedRenewedFromSubscriptionId
    ) {
        String subscriptionId = subscription.getSubscriptionId();
        if (subscriptionId.equals(updatedRenewedFromSubscriptionId)) {
            throw new InvalidInputException("updatedRenewedFromSubscriptionId cannot be the same as subscriptionId");
        }

        Subscription updatedRenewedFromSubscription = subscriptionGetService.getSubscription(updatedRenewedFromSubscriptionId);

        // todo: validate RENEWAL line items are still valid for the updated renewed from subscription
        validateUpdatedRenewedFromSubscription(subscription, updatedRenewedFromSubscription);

        subscriptionService.updateRenewedFromSubscriptionId(
            configuration,
            subscription.getSubscriptionId(),
            subscription.getRenewedFromSubscriptionId(),
            Optional.of(updatedRenewedFromSubscriptionId)
        );
        orderService.updateOrderRenewedFromSubscriptionId(
            configuration,
            firstOrderInSubscription.getOrderId(),
            firstOrderInSubscription.getRenewalForSubscriptionId(),
            updatedRenewedFromSubscriptionId,
            updatedRenewedFromSubscription.getVersion()
        );
    }

    private static void validateUpdatedRenewedFromSubscription(Subscription subscription, Subscription updatedRenewedFromSubscription) {
        String subscriptionId = subscription.getSubscriptionId();
        String updatedRenewedFromSubscriptionId = updatedRenewedFromSubscription.getSubscriptionId();

        if (!subscription.getAccountId().equals(updatedRenewedFromSubscription.getAccountId())) {
            throw new InvalidInputException("Subscriptions must belong to the same account");
        }

        if (!subscription.getEntityId().equals(updatedRenewedFromSubscription.getEntityId())) {
            throw new InvalidInputException("Subscriptions must belong to the same entity");
        }

        if (StringUtils.isNotBlank(updatedRenewedFromSubscription.getRenewedToSubscriptionId())) {
            String errorMessage = subscriptionId.equals(updatedRenewedFromSubscription.getRenewedToSubscriptionId())
                ? String.format("subscription %s is already renewed to subscription %s", updatedRenewedFromSubscriptionId, subscriptionId)
                : String.format(
                    "Subscription %s is already renewed to another subscription %s",
                    updatedRenewedFromSubscriptionId,
                    updatedRenewedFromSubscription.getRenewedToSubscriptionId()
                );
            throw new InvalidInputException(errorMessage);
        }

        if (updatedRenewedFromSubscription.getEndDate().compareTo(subscription.getStartDate()) != 0) {
            throw new InvalidInputException(
                String.format(
                    "Updated renewal from subscription %s end date %s does not match subscription %s start date %s",
                    updatedRenewedFromSubscriptionId,
                    updatedRenewedFromSubscription.getEndDate(),
                    subscriptionId,
                    subscription.getStartDate()
                )
            );
        }
    }

    private void throwInvalidFirstOrderTypeError(Order firstOrderInSubscription, String subscriptionId) {
        throw new InvariantCheckFailedException(
            String.format(
                "Invalid first order %s of type %s for subscription %s",
                firstOrderInSubscription.getOrderId(),
                firstOrderInSubscription.getOrderType(),
                subscriptionId
            )
        );
    }

    // todo: should this be moved to SubscriptionService?
    public void deleteSubscription(String subscriptionId, int version) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is required");

        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);

        validateSubscriptionForDelete(subscription, version, false);

        List<Order> executedOrders = orderGetService.getExecutedOrdersBySubscriptionId(subscription.getSubscriptionId());
        Optional<String> compositeOrderId = getCompositeDeleteSubscriptionOrder(executedOrders).map(Order::getCompositeOrderId);
        if (compositeOrderId.isPresent()) {
            revertExecutedCompositeOrder(compositeOrderId.get());
            return;
        }

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        tenantDslContext.transaction(configuration -> deleteSubscriptionInTransaction(configuration, subscription));
    }

    private void deleteSubscriptionInTransaction(Configuration configuration, Subscription subscription) {
        invoiceService.deleteInvoiceDataForSubscription(configuration, subscription.getSubscriptionId());
        subscriptionService.deleteSubscription(configuration, subscription);
        deleteOrdersBySubscriptionId(configuration, subscription.getSubscriptionId());
        markSubscriptionAccountForMetricsRecalculation(configuration, subscription);
        subscriptionLifecycleScheduleService.deleteSchedulesForSubscriptionVersion(configuration, subscription);
        // todo: delete other related objects: custom fields, sales_room_link, approval flow, approval_flow_notifications_queue, customer_error_log
    }

    // rollback the latest update to the subscription. This removes the current subscription version as well as any related data and rolls back the subscription to the previous version.
    public void revertSubscription(String subscriptionId, int version) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is required");

        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);

        subscriptionCanBeReverted(subscription, version, false);

        Order targetOrder = getTargetOrder(subscription);

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        tenantDslContext.transaction(configuration -> revertSubscriptionInTransaction(configuration, subscription, targetOrder));

        triggerSubscriptionDependenciesWhenReverted(subscriptionId);
    }

    private void revertSubscriptionInTransaction(Configuration configuration, Subscription subscription, Order targetOrder) {
        invoiceService.deleteInvoiceDataForOrder(configuration, targetOrder.getOrderId());
        subscriptionService.deleteSubscriptionVersion(configuration, subscription);
        deleteOrderInTransaction(configuration, targetOrder);
        markSubscriptionAccountForMetricsRecalculation(configuration, subscription);
        subscriptionLifecycleScheduleService.deleteSchedulesForSubscriptionVersion(configuration, subscription);
        Subscription newLatestSubscription = subscriptionGetService.getSubscription(DSL.using(configuration), subscription.getSubscriptionId());
        orderEventService.publishEventsForSubscriptionCreated(newLatestSubscription, DSL.using(configuration));
        // todo: delete other related objects: custom fields, sales_room_link, approval flow, approval_flow_notifications_queue, customer_error_log
    }

    private void triggerSubscriptionDependenciesWhenReverted(String subscriptionId) {
        crmService.pushSubscriptionSync(subscriptionId);
    }

    public Payment deleteVoidedPayment(String paymentId) {
        Validator.validateStringNotBlank(paymentId, "paymentId is required");
        Payment payment = paymentGetService.getPaymentByPaymentId(paymentId);
        validatePaymentForDelete(payment);

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        List<SettlementApplication> settlementApplications = invoiceSettlementService.getSettlementApplicationsByPaymentId(paymentId);
        Set<UUID> settlementApplicationIds = settlementApplications.stream().map(SettlementApplication::getId).collect(Collectors.toSet());
        Set<String> paymentApplicationIds = settlementApplications
            .stream()
            .filter(application -> application.getApplicationType().isApply())
            .map(application -> application.getId().toString())
            .collect(Collectors.toSet());
        Set<String> voidPaymentIds = settlementApplications
            .stream()
            .filter(application -> application.getApplicationType().isUnapply())
            .map(application -> application.getId().toString())
            .collect(Collectors.toSet());

        PaymentBalance balance = invoiceSettlementService.getPaymentBalance(paymentId);

        tenantDslContext.transaction(configuration -> {
            accountingService.deleteAccountingEventLogs(configuration, JournalEntry.TransactionType.PAYMENT_PROCESSED, paymentApplicationIds);
            accountingService.deleteAccountingEventLogs(configuration, JournalEntry.TransactionType.PAYMENT_VOIDED, voidPaymentIds);
            invoiceSettlementService.deleteSettlementApplicationsForPayment(paymentId, settlementApplicationIds);
            invoiceSettlementService.deletePaymentBalance(paymentId, balance.getBalance());
            paymentService.deletePayment(paymentId);
        });

        return payment;
    }

    public PaymentDeletableResponse canDeletePayment(String paymentId) {
        Validator.validateStringNotBlank(paymentId, "paymentId is required");
        Payment payment = paymentGetService.getPaymentByPaymentId(paymentId);

        try {
            validatePaymentForDelete(payment);
            return new PaymentDeletableResponse(true, StringUtils.EMPTY);
        } catch (InvalidInputException e) {
            LOGGER.info("Payment cannot be deleted: " + e.getMessage());
            return new PaymentDeletableResponse(false, e.getMessage());
        }
    }

    private void validatePaymentForDelete(Payment payment) {
        if (payment.getState() != PaymentState.VOIDED) {
            throw new InvalidInputException("Only voided payments can be deleted.");
        }

        if (accountingGetService.journalEntriesExistForSourceTransactionIds(Set.of(payment.getPaymentId()))) {
            throw new InvalidInputException("Applied payment(s) have journal entries and cannot be deleted.");
        }
    }

    public Invoice deleteVoidedInvoice(Invoice.Number invoiceNumber) {
        Validator.validateNonNullArgument(invoiceNumber, "invoiceNumber is required");

        Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);
        validateVoidedInvoiceForDelete(invoice);

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        tenantDslContext.transaction(configuration -> {
            accountingService.deleteAccountingEventLog(configuration, JournalEntry.TransactionType.INVOICE_POSTED, invoiceNumber.getNumber());
            accountingService.deleteAccountingEventLog(configuration, JournalEntry.TransactionType.INVOICE_VOIDED, invoiceNumber.getNumber());
            invoiceService.deleteVoidedInvoice(configuration, invoiceNumber);
        });

        return invoice;
    }

    public InvoiceDeletableResponse canDeleteInvoice(Invoice.Number invoiceNumber) {
        Validator.validateNonNullArgument(invoiceNumber, "invoiceNumber is required");

        Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);

        if (invoice.getStatus() == InvoiceStatus.DRAFT) {
            // draft invoices can always be deleted
            return new InvoiceDeletableResponse(true, "");
        }

        try {
            validateVoidedInvoiceForDelete(invoice);
            return new InvoiceDeletableResponse(true, "");
        } catch (InvalidInputException e) {
            LOGGER.info("Invoice cannot be deleted: " + e.getMessage());
            return new InvoiceDeletableResponse(false, e.getMessage());
        }
    }

    private void validateVoidedInvoiceForDelete(Invoice invoice) {
        if (invoice.getStatus() != InvoiceStatus.VOIDED) {
            throw new InvalidInputException("Only voided invoices can be deleted.");
        }

        Invoice.Number invoiceNumber = invoice.getInvoiceNumber();

        List<SettlementApplication> settlementApplications = invoiceSettlementService.getSettlementApplications(invoiceNumber);

        if (CollectionUtils.isNotEmpty(settlementApplications)) {
            throw new InvalidInputException("Invoice has credit or payment applications and cannot be deleted.");
        }

        if (accountingGetService.journalEntriesExistForSourceTransactionIds(Set.of(invoiceNumber.getNumber()))) {
            throw new InvalidInputException("Invoice has journal entries and cannot be deleted.");
        }
        // todo: check for ERP sync?
    }

    private void markSubscriptionAccountForMetricsRecalculation(Configuration configuration, Subscription subscription) {
        DSLContext dslContext = DSL.using(configuration);
        String tenantId = tenantIdProvider.provideTenantIdString();
        accountService.markAccountsForMetricsRecalculation(dslContext, tenantId, Set.of(subscription.getAccountId()));
    }

    public SubscriptionModifiableResponse canRevertSubscription(String subscriptionId) {
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);

        try {
            subscriptionCanBeReverted(subscription, subscription.getVersion(), false);
            return new SubscriptionModifiableResponse(true, "");
        } catch (InvalidInputException e) {
            LOGGER.info("Subscription cannot be modified: " + e.getMessage());
            return new SubscriptionModifiableResponse(false, e.getMessage());
        }
    }

    public SubscriptionModifiableResponse canDeleteSubscription(String subscriptionId) {
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        try {
            validateSubscriptionForDelete(subscription, subscription.getVersion(), false);
            return new SubscriptionModifiableResponse(true, "");
        } catch (InvalidInputException e) {
            LOGGER.info("Subscription cannot be modified: " + e.getMessage());
            return new SubscriptionModifiableResponse(false, e.getMessage());
        }
    }

    // back-fill subscription charges that were created prior to the capability to automatically map order line custom field to subscription charge custom fields
    public int backFillSubscriptionItemCustomFields(String subscriptionId) {
        List<CustomFieldDefinition> subscriptionItemCustomFieldDefinitions = customFieldService.getCustomFieldDefinitions(
            CustomFieldParentType.SUBSCRIPTION_ITEM
        );
        List<CustomFieldDefinition> orderItemCustomFieldDefinitions = customFieldService.getCustomFieldDefinitions(CustomFieldParentType.ORDER_ITEM);

        Set<String> matchingCustomFieldNames = findMatchingCustomFieldNames(subscriptionItemCustomFieldDefinitions, orderItemCustomFieldDefinitions);

        if (matchingCustomFieldNames.isEmpty()) {
            return 0;
        }

        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);

        Map<String, String> subscriptionChargeIdOrderLineIdMap = subscription
            .getCharges()
            .stream()
            // first item in order lines list is the order line that created this subscription charge
            .collect(Collectors.toMap(SubscriptionCharge::getSubscriptionChargeId, subscriptionCharge -> subscriptionCharge.getOrderLines().get(0)));

        AtomicInteger subscriptionChargesUpdated = new AtomicInteger();
        subscriptionChargeIdOrderLineIdMap.forEach((subscriptionChargeId, orderItemId) -> {
            boolean chargeCustomFieldUpdated = updateSubscriptionItemCustomField(subscriptionChargeId, orderItemId);
            if (chargeCustomFieldUpdated) {
                subscriptionChargesUpdated.getAndIncrement();
            }
        });

        return subscriptionChargesUpdated.get();
    }

    private Order getTargetOrder(Subscription subscription) {
        List<Order> executedOrders = orderGetService.getOrderHeadersBySubscriptionUuid(subscription.getId(), Optional.of(OrderStatus.EXECUTED));

        if (executedOrders.size() != 1) {
            throw new InvariantCheckFailedException("Number of executed orders for subscription version is: " + executedOrders.size());
        }

        Order targetOrder = executedOrders.get(0);

        if (targetOrder.getSubscriptionTargetVersion() != subscription.getVersion()) {
            throw new InvariantCheckFailedException("Subscription version does not match order subscription target version");
        }

        return targetOrder;
    }

    private void subscriptionCanBeReverted(Subscription subscription, int version, boolean allowCompositeOrders) {
        if (subscription.getVersion() == 1) {
            throw new InvalidInputException("Subscription has not been amended, nothing to revert");
        }

        validateSubscriptionForRevert(subscription, version, allowCompositeOrders);
    }

    private void validateSubscriptionForRevert(Subscription subscription, int version, boolean allowCompositeOrders) {
        validateSubscriptionCanBeModified(subscription, version, allowCompositeOrders);

        // check if any invoice items are associated with the order to be reverted
        Order targetOrder = getTargetOrder(subscription);
        List<InvoiceItem> invoiceItems = invoiceRetrievalService.getInvoiceItemsByOrderId(targetOrder.getOrderId());
        if (CollectionUtils.isNotEmpty(invoiceItems)) {
            throw new InvalidInputException("Order to be reverted has generated invoices so cannot be reverted.");
        }

        checkForRecognitionTransactions(targetOrder.getOrderId());

        // Check if order to be reverted is involved with executed composite order
        if (!allowCompositeOrders && StringUtils.isNotBlank(targetOrder.getCompositeOrderId())) {
            throw new InvalidInputException("Subscription is associated with executed composite order, cannot be modified.");
        }
        validateNoDraftCompositeOrders(subscription.getSubscriptionId());
        // todo: additional validations: electronic_signature, electronic_signature_request
    }

    private void validateSubscriptionForDelete(Subscription subscription, int version, boolean allowCompositeOrders) {
        validateSubscriptionCanBeModified(subscription, version, allowCompositeOrders);

        List<Invoice> invoices = invoiceRetrievalService.getInvoices(
            subscription.getSubscriptionId(),
            Optional.empty(),
            new PaginationQueryParams(null, 1)
        );
        if (CollectionUtils.isNotEmpty(invoices)) {
            throw new InvalidInputException("Subscription has invoices and cannot be modified.");
        }

        List<Order> orders = orderGetService.getOrdersBySubscriptionIdOrRenewalSubscriptionId(
            subscription.getSubscriptionId(),
            Optional.of(OrderStatus.EXECUTED)
        );

        orders.forEach(order -> checkForRecognitionTransactions(order.getOrderId()));

        validateNoDraftCompositeOrders(subscription.getSubscriptionId());
        // todo: additional validations: electronic_signature, electronic_signature_request
    }

    private void deleteOrdersBySubscriptionId(Configuration configuration, String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is blank");

        List<Order> orders = orderGetService.getOrdersBySubscriptionIdOrRenewalSubscriptionId(subscriptionId, Optional.empty());
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        orders.forEach(order -> deleteOrderInTransaction(configuration, order));
    }

    private void validateSubscriptionCanBeModified(Subscription subscription, int version, boolean allowCompositeOrders) {
        if (subscription.getVersion() != version) {
            throw new InvalidInputException("Subscription has been updated, please refresh and try again.");
        }

        if (!allowCompositeOrders && StringUtils.isNotBlank(subscription.getRenewedToSubscriptionId())) {
            throw new InvalidInputException("Renewed subscriptions cannot be modified.");
        }

        if (!allowCompositeOrders && StringUtils.isNotBlank(subscription.getRestructuredToSubscriptionId())) {
            throw new InvalidInputException("Restructured subscriptions cannot be modified.");
        }
    }

    private void checkForRecognitionTransactions(String orderId) {
        List<RecognitionTransaction> recognitionTransactions = revenueRecognitionGetService.getRecognitionTransactionsByOrderId(orderId);

        boolean isAnyRecognized = recognitionTransactions.stream().anyMatch(RecognitionTransaction::getIsRecognized);
        if (isAnyRecognized) {
            throw new InvalidInputException("Subscription has orders with recognized recognition transactions and cannot be modified.");
        }
    }

    private void validateNoDraftCompositeOrders(String subscriptionId) {
        List<Order> allOrders = orderGetService.getOrdersBySubscriptionIdOrRenewalSubscriptionId(subscriptionId, Optional.empty());

        allOrders.forEach(order -> {
            if (StringUtils.isNotBlank(order.getCompositeOrderId()) && order.getStatus() != OrderStatus.EXECUTED) {
                throw new InvalidInputException(
                    "Subscription is associated with non-executed composite order, delete composite order before reverting subscription update"
                );
            }
        });
    }

    private void deleteOrderInTransaction(Configuration configuration, Order order) {
        documentCustomContentService.deleteDocumentCustomContentByOrderId(configuration, order.getOrderId());
        customTemplateUpdatedOnOrderService.deleteUpdatedTemplatesOnOrder(configuration, order.getOrderId());
        orderCommentService.deleteCommentsByOrderId(configuration, order.getOrderId());
        approvalFlowNotificationService.deleteSlackApprovalRecordsByOrderId(order.getOrderId());
        orderService.deleteOrder(configuration, order.getOrderId());
        triggerOrderDependenciesWhenDeleted(configuration, order);
        crmService.pushOrderDeletionSync(order);
    }

    // Inverse of OrderService#triggerOrderDependenciesWhenExecuted
    private void triggerOrderDependenciesWhenDeleted(Configuration configuration, Order order) {
        if (OrderStatus.EXECUTED != order.getStatus()) {
            return;
        }

        // kick off ASC606 rev-rec job
        // TODO: just remove the feature flag check when feature is ready
        if (platformFeatureService.isFeatureEnabledAndReady(PlatformFeature.REVENUE_RECOGNITION)) {
            revenueRecognitionService.safelyRemoveRevSchedulesByOrderId(order.getOrderId());
        }

        String crmOpportunityId = order.getSfdcOpportunityId();
        if (crmOpportunityId != null) {
            opportunityService.resetOpportunityClosedState(configuration, crmOpportunityId, order.getOrderId());
        }

        reportingJobQueueService.addDeleteTaskToRemoveArrRowsForOrder(configuration, tenantIdProvider.provideTenantIdString(), order.getOrderId());
    }

    // find custom field names that are common between order item and subscription item custom fields
    private Set<String> findMatchingCustomFieldNames(
        List<CustomFieldDefinition> subscriptionItemCustomFieldDefinitions,
        List<CustomFieldDefinition> orderItemCustomFieldDefinitions
    ) {
        Map<String, CustomFieldDefinition> orderItemCustomFieldNameToDefinition = orderItemCustomFieldDefinitions
            .stream()
            .collect(Collectors.toMap(CustomFieldDefinition::getFieldName, Function.identity()));

        Set<String> matchingCustomFieldNames = new HashSet<>();
        subscriptionItemCustomFieldDefinitions.forEach(subscriptionItemCustomFieldDefinition -> {
            String customFieldName = subscriptionItemCustomFieldDefinition.getFieldName();
            CustomFieldDefinition matchingOrderItemDefinition = orderItemCustomFieldNameToDefinition.get(customFieldName);

            if (
                matchingOrderItemDefinition != null &&
                matchingOrderItemDefinition.getFieldType() == subscriptionItemCustomFieldDefinition.getFieldType()
            ) {
                matchingCustomFieldNames.add(subscriptionItemCustomFieldDefinition.getFieldName());
            }
        });

        return matchingCustomFieldNames;
    }

    private boolean updateSubscriptionItemCustomField(String subscriptionChargeId, String orderItemId) {
        CustomField subscriptionChargeCustomField = customFieldService.getCustomFields(CustomFieldParentType.SUBSCRIPTION_ITEM, subscriptionChargeId);
        Optional<CustomField> orderItemCustomField = customFieldService.getCustomFieldsOptional(CustomFieldParentType.ORDER_ITEM, orderItemId);

        if (orderItemCustomField.isEmpty()) {
            // if there is no order item custom field value, return
            return false;
        }

        // map of order custom fields keyed by field name
        Map<String, CustomFieldValue> orderItemCustomFieldsMap = orderItemCustomField
            .map(CustomField::getEntries)
            .orElse(new HashMap<>())
            .values()
            .stream()
            .collect(Collectors.toMap(CustomFieldValue::getName, Function.identity()));

        AtomicBoolean updated = new AtomicBoolean(false);

        Map<String, CustomFieldValue> updatedSubscriptionChargeCustomFieldEntries = new HashMap<>();
        subscriptionChargeCustomField
            .getEntries()
            .forEach((customFieldId, customFieldValue) -> {
                String customFieldName = customFieldValue.getName();
                CustomFieldValue matchingOrderItemCustomField = orderItemCustomFieldsMap.get(customFieldName);
                if (matchingOrderItemCustomField != null) {
                    // if there is a matching order item custom field entry, update with its value if not already set
                    String updatedValue = StringUtils.isBlank(customFieldValue.getValue())
                        ? matchingOrderItemCustomField.getValue()
                        : customFieldValue.getValue();
                    List<String> updatedSelections = CollectionUtils.isEmpty(customFieldValue.getSelections())
                        ? matchingOrderItemCustomField.getSelections()
                        : customFieldValue.getSelections();
                    CustomFieldValue updatedSubscriptionItemCustomField = new CustomFieldValue(
                        customFieldValue.getType(),
                        customFieldValue.getName(),
                        customFieldValue.getLabel(),
                        updatedValue,
                        updatedSelections,
                        customFieldValue.getOptions(),
                        customFieldValue.isRequired(),
                        customFieldValue.getSource(),
                        customFieldValue.getDefaultValue()
                    );
                    updatedSubscriptionChargeCustomFieldEntries.put(customFieldId, updatedSubscriptionItemCustomField);
                    updated.set(true);
                } else {
                    // if no matching order item custom field entry, keep existing values
                    updatedSubscriptionChargeCustomFieldEntries.put(customFieldId, customFieldValue);
                }
            });

        CustomField updatedSubscriptionCustomField = new CustomField(updatedSubscriptionChargeCustomFieldEntries);

        customFieldService.setCustomFieldsBySystem(CustomFieldParentType.SUBSCRIPTION_ITEM, subscriptionChargeId, updatedSubscriptionCustomField);
        return updated.get();
    }

    public void revertExecutedCompositeOrder(String compositeOrderId) {
        Validator.validateStringNotBlank(compositeOrderId, "compositeOrderId is required");
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        if (compositeOrder.getStatus() != OrderStatus.EXECUTED) {
            throw new InvalidInputException(String.format("Composite Order %s is not EXECUTED", compositeOrderId));
        }
        List<String> orderIds = orderGetService.getOrderIdsInCompositeOrder(compositeOrderId).stream().toList();
        List<Order> orders = orderGetService.getOrdersByOrderIds(orderIds);

        Order revertSubscriptionOrder = getCompositeRevertSubscriptionOrder(compositeOrderId, orders);
        Subscription revertSubscription = subscriptionGetService.getSubscription(revertSubscriptionOrder.getExternalSubscriptionId());
        if (revertSubscription.getVersion() != revertSubscriptionOrder.getSubscriptionTargetVersion()) {
            throw new InvalidInputException(
                String.format(
                    "revert subscription %s is version %s instead of target version %s",
                    revertSubscription.getSubscriptionId(),
                    revertSubscription.getVersion(),
                    revertSubscriptionOrder.getSubscriptionTargetVersion()
                )
            );
        }

        Order deleteSubscriptionOrder = getCompositeDeleteSubscriptionOrder(orders).orElseThrow(() ->
            new ServiceFailureException(String.format("Cannot find order of type RENEWAL or RESTRUCTURE in composite order %s", compositeOrderId))
        );
        Subscription deleteSubscription = subscriptionGetService.getSubscription(deleteSubscriptionOrder.getExternalSubscriptionId());
        if (deleteSubscription.getVersion() != 1) {
            throw new InvalidInputException(
                String.format(
                    "delete subscription %s is version %s instead of version 1",
                    deleteSubscription.getSubscriptionId(),
                    deleteSubscription.getVersion()
                )
            );
        }

        validateSubscriptionForRevert(revertSubscription, revertSubscription.getVersion(), true);
        validateSubscriptionForDelete(deleteSubscription, deleteSubscription.getVersion(), true);

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        tenantDslContext.transaction(configuration -> {
            DSLContext transactionalDslContext = DSL.using(configuration);
            compositeOrderService.tryAcquireAdvisoryLock(compositeOrder.getCompositeOrderId(), transactionalDslContext);
            revertSubscriptionInTransaction(configuration, revertSubscription, revertSubscriptionOrder);
            deleteSubscriptionInTransaction(configuration, deleteSubscription);
            compositeOrderService.deleteCompositeOrderInTransaction(transactionalDslContext, compositeOrder);
        });

        triggerSubscriptionDependenciesWhenReverted(revertSubscription.getSubscriptionId());
        compositeOrderService.deleteCompositeOrderPostTransactionEvents(
            compositeOrder,
            tenantIdProvider.provideTenantIdString(),
            revertSubscription.getAccountId()
        );
    }

    private Order getCompositeRevertSubscriptionOrder(String compositeOrderId, List<Order> orders) {
        return orders
            .stream()
            .filter(order -> order.getStatus() == OrderStatus.EXECUTED)
            .filter(order -> StringUtils.isNotBlank(order.getCompositeOrderId()))
            .filter(order -> order.getOrderType() == OrderType.CANCEL || order.getOrderType() == OrderType.AMENDMENT)
            .findFirst()
            .orElseThrow(() ->
                new ServiceFailureException(String.format("Cannot find order of type CANCEL or AMENDMENT in composite order %s", compositeOrderId))
            );
    }

    private Optional<Order> getCompositeDeleteSubscriptionOrder(List<Order> orders) {
        return orders
            .stream()
            .filter(order -> order.getStatus() == OrderStatus.EXECUTED)
            .filter(order -> order.getSubscriptionTargetVersion() == 1)
            .filter(order -> StringUtils.isNotBlank(order.getCompositeOrderId()))
            .filter(order -> order.getOrderType() == OrderType.RESTRUCTURE || order.getOrderType() == OrderType.RENEWAL)
            .findFirst();
    }
}
