package com.subskribe.billy.admin.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import com.subskribe.billy.subscription.model.SubscriptionType;
import javax.annotation.Nullable;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
@JsonDeserialize(as = ImmutableSubscriptionTypeUpdateInput.class)
public interface SubscriptionTypeUpdateInput {
    SubscriptionType getUpdateToType();

    @Nullable
    String getRenewedFromSubscriptionId();
}
