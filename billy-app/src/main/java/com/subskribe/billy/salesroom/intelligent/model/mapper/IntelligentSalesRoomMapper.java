package com.subskribe.billy.salesroom.intelligent.model.mapper;

import com.subskribe.billy.configuration.StrictMapperConfig;
import com.subskribe.billy.jooq.default_schema.tables.records.IntelligentSalesRoomActivityLogRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.IntelligentSalesRoomAiGeneratedContentRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.IntelligentSalesRoomFileRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.IntelligentSalesRoomRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.IntelligentSalesRoomShareLinkAccessRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.IntelligentSalesRoomThemeRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.IntelligentSalesRoomWidgetRecord;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoom;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomAIGeneratedContent;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomActivityLog;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomFile;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomShareLinkAccess;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomStatus;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomTheme;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWidget;
import com.subskribe.billy.salesroom.intelligent.model.response.IntelligentSalesRoomOverviewResponse;
import com.subskribe.billy.shared.mapper.BaseMapper;
import java.util.List;
import org.jooq.Result;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(config = StrictMapperConfig.class)
public interface IntelligentSalesRoomMapper extends BaseMapper {
    IntelligentSalesRoom fromRecord(IntelligentSalesRoomRecord record);

    List<IntelligentSalesRoom> fromRecords(Result<IntelligentSalesRoomRecord> records);

    @Mapping(target = "isDeleted", ignore = true)
    IntelligentSalesRoomRecord toRecord(IntelligentSalesRoom salesRoom);

    @Mapping(source = "widgetType", target = "type")
    @Mapping(target = "name", expression = "java(java.util.Optional.ofNullable(record.getWidgetName()))")
    IntelligentSalesRoomWidget fromWidgetRecord(IntelligentSalesRoomWidgetRecord record);

    List<IntelligentSalesRoomWidget> fromWidgetRecords(List<IntelligentSalesRoomWidgetRecord> records);

    @Mapping(source = "type", target = "widgetType")
    @Mapping(target = "widgetName", expression = "java(widget.getName().orElse(null))")
    @Mapping(target = "isDeleted", ignore = true)
    IntelligentSalesRoomWidgetRecord toWidgetRecord(IntelligentSalesRoomWidget widget);

    @Mapping(target = "validFileSize", ignore = true)
    IntelligentSalesRoomFile fromFileRecord(IntelligentSalesRoomFileRecord record);

    List<IntelligentSalesRoomFile> fromFileRecords(List<IntelligentSalesRoomFileRecord> record);

    @Mapping(target = "isDeleted", ignore = true)
    IntelligentSalesRoomFileRecord toFileRecord(IntelligentSalesRoomFile file);

    IntelligentSalesRoomTheme fromThemeRecord(IntelligentSalesRoomThemeRecord record);

    @Mapping(target = "isDeleted", ignore = true)
    IntelligentSalesRoomThemeRecord toThemeRecord(IntelligentSalesRoomTheme theme);

    IntelligentSalesRoomActivityLog fromActivityLogRecord(IntelligentSalesRoomActivityLogRecord record);

    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "createdOn", ignore = true)
    @Mapping(target = "updatedOn", ignore = true)
    IntelligentSalesRoomActivityLogRecord toActivityLogRecord(IntelligentSalesRoomActivityLog activityLog);

    List<IntelligentSalesRoomActivityLog> fromActivityLogRecords(List<IntelligentSalesRoomActivityLogRecord> records);

    IntelligentSalesRoomShareLinkAccess fromShareLinkAccessRecord(IntelligentSalesRoomShareLinkAccessRecord record);

    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "createdOn", ignore = true)
    @Mapping(target = "updatedOn", ignore = true)
    IntelligentSalesRoomShareLinkAccessRecord toShareLinkAccessRecord(IntelligentSalesRoomShareLinkAccess access);

    List<IntelligentSalesRoomShareLinkAccess> fromShareLinkAccessRecords(List<IntelligentSalesRoomShareLinkAccessRecord> records);

    IntelligentSalesRoomAIGeneratedContent fromAiGeneratedContentRecord(IntelligentSalesRoomAiGeneratedContentRecord record);

    List<IntelligentSalesRoomAIGeneratedContent> fromAiGeneratedContentRecords(List<IntelligentSalesRoomAiGeneratedContentRecord> records);

    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "createdOn", ignore = true)
    @Mapping(target = "updatedOn", ignore = true)
    IntelligentSalesRoomAiGeneratedContentRecord toAiGeneratedContentRecord(IntelligentSalesRoomAIGeneratedContent record);

    @Mapping(target = "canRetract", expression = "java(mapCanRetract(salesRoom))")
    @Mapping(target = "canEdit", expression = "java(salesRoom.canEdit())")
    @Mapping(target = "canShare", expression = "java(salesRoom.canShare())")
    @Mapping(target = "theme", expression = "java(java.util.Optional.empty())")
    @Mapping(target = "status", expression = "java(mapStatus(salesRoom))")
    @Mapping(target = "tenantName", ignore = true)
    @Mapping(target = "tenantAddress", ignore = true)
    @Mapping(target = "electronicSignatureStatus", ignore = true)
    IntelligentSalesRoomOverviewResponse toOverviewResponse(IntelligentSalesRoom salesRoom);

    default boolean mapCanRetract(IntelligentSalesRoom salesRoom) {
        return salesRoom != null && salesRoom.getStatus() == IntelligentSalesRoomStatus.ACTIVE;
    }

    default IntelligentSalesRoomStatus mapStatus(IntelligentSalesRoom salesRoom) {
        return salesRoom.isExpired() ? IntelligentSalesRoomStatus.EXPIRED : salesRoom.getStatus();
    }
}
