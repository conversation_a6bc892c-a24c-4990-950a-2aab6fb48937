package com.subskribe.billy.salesroom.intelligent.model;

public class IntelligentSalesRoomConstants {

    // 25MB
    public static final long MAX_FILE_SIZE_BYTES = 25 * 1024 * 1024;
    public static final int MAX_FILES_PER_SALES_ROOM = 10;
    public static final int MAX_WIDGETS_PER_SALES_ROOM = 10;
    public static final int SESSION_TIMEOUT_MINUTES = 30;
    // 100KB
    public static final int MAX_WIDGET_CONTENT_LENGTH = 100000;
    public static final int MAX_FILE_NAME_LENGTH = 255;
}
