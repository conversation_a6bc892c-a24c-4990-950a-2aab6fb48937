package com.subskribe.billy.salesroom.intelligent.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import java.time.Instant;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
public interface IntelligentSalesRoomActivityLog {
    @JsonIgnore
    UUID getId();

    @JsonIgnore
    String getTenantId();

    String getSalesRoomId();

    String getUserEmail();

    IntelligentSalesRoomActivity getEventType();

    Instant getEventTimestamp();

    Optional<Map<String, Object>> getMetadata();
}
