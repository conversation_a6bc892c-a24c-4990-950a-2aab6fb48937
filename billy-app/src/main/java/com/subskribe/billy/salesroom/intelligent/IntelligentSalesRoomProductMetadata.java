package com.subskribe.billy.salesroom.intelligent;

import com.subskribe.billy.shared.logger.ErrorInstructions;
import com.subskribe.billy.shared.product.ProductArea;
import com.subskribe.billy.shared.product.ProductFeature;
import com.subskribe.billy.shared.product.ProductFeatureAvailability;

public final class IntelligentSalesRoomProductMetadata {

    private static final ProductFeatureAvailability CURRENT_FEATURE_AVAILABILITY = ProductFeatureAvailability.BETA;

    private static final ProductArea PRODUCT_AREA = new ProductArea("Intelligent Sales Room");

    private static final String RUNBOOK = "Product is still under development. Please reach out to the team in #salesroom channel.";

    public static final ProductFeature SALES_ROOM_CORE = new ProductFeature(PRODUCT_AREA, "Sales Room Core", CURRENT_FEATURE_AVAILABILITY);

    public static final ProductFeature THEME_CUSTOMIZATION = new ProductFeature(PRODUCT_AREA, "Theme Customization", CURRENT_FEATURE_AVAILABILITY);

    public static final ProductFeature FILE_MANAGEMENT = new ProductFeature(PRODUCT_AREA, "File Management", CURRENT_FEATURE_AVAILABILITY);

    public static final ProductFeature AI_CONTENT_GENERATION = new ProductFeature(
        PRODUCT_AREA,
        "AI Content Generation",
        CURRENT_FEATURE_AVAILABILITY
    );

    public static final ProductFeature ENGAGEMENT_TRACKING = new ProductFeature(PRODUCT_AREA, "Engagement Tracking", CURRENT_FEATURE_AVAILABILITY);

    public static final ProductFeature CUSTOMER_INFORMATION = new ProductFeature(PRODUCT_AREA, "Customer Information", CURRENT_FEATURE_AVAILABILITY);

    public static final ErrorInstructions SALES_ROOM_CREATION_FAILED = ErrorInstructions.sev2(
        "Failed to create intelligent sales room for order",
        RUNBOOK
    );

    public static final ErrorInstructions WIDGET_OPERATION_FAILED = ErrorInstructions.sev2(
        "Failed to perform widget operation in sales room",
        RUNBOOK
    );

    public static final ErrorInstructions FILE_UPLOAD_FAILED = ErrorInstructions.sev2("Failed to upload file to sales room", RUNBOOK);

    public static final ErrorInstructions AI_CONTENT_GENERATION_FAILED = ErrorInstructions.sev2(
        "Failed to generate AI content for sales room widget",
        RUNBOOK
    );

    public static final ErrorInstructions THEME_EXTRACTION_FAILED = ErrorInstructions.sev3(
        "Failed to extract theme from website - user can manually set theme",
        RUNBOOK
    );

    public static final ErrorInstructions ENGAGEMENT_TRACKING_FAILED = ErrorInstructions.sev3(
        "Failed to track user engagement in sales room",
        RUNBOOK
    );

    public static final ErrorInstructions ORDER_STATUS_SYNC_FAILED = ErrorInstructions.sev2(
        "Failed to sync sales room status with order status change",
        RUNBOOK
    );

    public static final ErrorInstructions CUSTOMER_INFO_UPDATE_FAILED = ErrorInstructions.sev2(
        "Failed to update customer information and regenerate PDF",
        RUNBOOK
    );

    public static final ErrorInstructions LOGO_URL_UPLOAD_FAILED = ErrorInstructions.sev2("Failed to upload logo URL for sales room", RUNBOOK);
}
