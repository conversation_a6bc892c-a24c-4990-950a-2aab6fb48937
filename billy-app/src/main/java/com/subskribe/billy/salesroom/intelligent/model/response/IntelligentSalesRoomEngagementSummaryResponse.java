package com.subskribe.billy.salesroom.intelligent.model.response;

import com.subskribe.billy.shared.immutables.BillyModelStyle;
import java.time.Instant;
import java.util.Optional;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
public interface IntelligentSalesRoomEngagementSummaryResponse {
    int getTotalVisitors();

    int getTotalSessions();

    long getTotalTimeSpentSeconds();

    String getAverageTimeSpent();

    Optional<Instant> getLastActivity();

    Optional<String> getMostActiveUser();
}
