package com.subskribe.billy.salesroom.intelligent.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
public interface IntelligentSalesRoomEngagementSession {
    @JsonIgnore
    UUID getId();

    @JsonIgnore
    String getTenantId();

    String getSalesRoomId();

    String getUserName();

    String getUserEmail();

    String getSessionId();

    Instant getSessionStart();

    Optional<Instant> getSessionEnd();

    Optional<Instant> getLastActivityTime();

    Long getDurationSeconds();

    Optional<String> getIpAddress();

    Optional<String> getUserAgent();

    List<String> getTabsVisited();

    Optional<Map<String, Object>> getActivityData();

    @Value.Default
    default boolean getIsActive() {
        return getSessionEnd().isEmpty();
    }

    default boolean isSessionExpired() {
        if (getSessionEnd().isPresent()) {
            return true;
        }

        Instant lastActivity = getLastActivityTime().orElse(getSessionStart());
        return Instant.now().isAfter(lastActivity.plusSeconds(IntelligentSalesRoomConstants.SESSION_TIMEOUT_SECONDS));
    }
}
