package com.subskribe.billy.salesroom.intelligent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.io.Resources;
import com.subskribe.ai.service.bedrock.BedrockClientProvider;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWebsiteMetadata;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWebsiteMetadata.ColorMetadata;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWebsiteMetadata.LogoMetadata;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWebsiteMetadata.TypographyMetadata;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Pattern;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeAsyncClient;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelRequest;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelResponse;

public class WebsiteMetadataExtractor {

    private static final Logger LOGGER = LoggerFactory.getLogger(WebsiteMetadataExtractor.class);

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    private static final String CLAUDE_MODEL_ID = "us.anthropic.claude-3-7-sonnet-20250219-v1:0";

    private static final int TIMEOUT_SECONDS = 30;

    private static final int MAX_HTML_SIZE = 30000;

    private static final int MAX_CSS_SIZE = 8000;

    private static final Pattern HEX_COLOR_PATTERN = Pattern.compile("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$");

    private static final String PROMPT = "ai/salesroom/claude_salesroom_website_metadata_prompt.txt";

    private static final Cache<String, IntelligentSalesRoomWebsiteMetadata> METADATA_CACHE = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(24, TimeUnit.HOURS)
        .build();

    private final WebScraper webScraper;

    private final BedrockClientProvider bedrockClientProvider;

    @Inject
    public WebsiteMetadataExtractor(WebScraper webScraper, BedrockClientProvider bedrockClientProvider) {
        this.webScraper = webScraper;
        this.bedrockClientProvider = bedrockClientProvider;
    }

    Optional<IntelligentSalesRoomWebsiteMetadata> extractMetadata(String url) {
        var bedrockClient = bedrockClientProvider.getBedrockRuntimeAsyncClient();
        if (StringUtils.isEmpty(url)) {
            LOGGER.info("URL cannot be empty");
            return Optional.empty();
        }

        if (bedrockClient == null) {
            LOGGER.info("Bedrock client cannot be null");
            return Optional.empty();
        }

        String domain = extractDomain(url);

        IntelligentSalesRoomWebsiteMetadata cachedResult = METADATA_CACHE.getIfPresent(domain);
        if (cachedResult != null) {
            LOGGER.info("Returning cached metadata for domain: {}", domain);
            return Optional.of(cachedResult);
        }

        try {
            LOGGER.info("Extracting HTML from URL: {}", url);
            String html = webScraper.extractHtml(url);

            LOGGER.info("Extracting CSS from URL: {}", url);
            String cssContent = webScraper.extractCssFromUrl(url);

            LOGGER.info("Calling Claude via Bedrock to analyze HTML and CSS");
            String claudeResponse = callClaudeViaBedrock(html, cssContent, url, bedrockClient);

            LOGGER.info("Parsing Claude response");
            JsonNode metadataNode = parseClaudeResponse(claudeResponse);
            IntelligentSalesRoomWebsiteMetadata metadata = convertToWebsiteMetadata(metadataNode, url);

            METADATA_CACHE.put(domain, metadata);
            return Optional.of(metadata);
        } catch (Exception e) {
            LOGGER.info("Error extracting metadata from {}", url, e);
            return Optional.empty();
        }
    }

    private static String extractDomain(String url) {
        try {
            URI uri = new URI(url);
            String domain = uri.getHost();
            return domain.startsWith("www.") ? domain.substring(4) : domain;
        } catch (Exception e) {
            return url;
        }
    }

    private static String callClaudeViaBedrock(String html, String cssContent, String url, BedrockRuntimeAsyncClient bedrockClient)
        throws IOException, InterruptedException, ExecutionException, TimeoutException {
        String prompt = createPrompt(html, cssContent, url);
        String requestBody = createBedrockRequestBody(prompt);

        InvokeModelRequest request = InvokeModelRequest.builder()
            .modelId(CLAUDE_MODEL_ID)
            .contentType("application/json")
            .accept("application/json")
            .body(SdkBytes.fromUtf8String(requestBody))
            .build();

        CompletableFuture<InvokeModelResponse> responseFuture = bedrockClient.invokeModel(request);
        InvokeModelResponse response = responseFuture.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        return response.body().asUtf8String();
    }

    private static String createPrompt(String html, String cssContent, String url) {
        String truncatedHtml = html.length() > MAX_HTML_SIZE ? html.substring(0, MAX_HTML_SIZE) + "..." : html;
        String truncatedCssContent = cssContent.length() > MAX_CSS_SIZE ? cssContent.substring(0, MAX_CSS_SIZE) + "..." : cssContent;

        try {
            String promptTemplate = Resources.toString(Resources.getResource(PROMPT), StandardCharsets.UTF_8);
            return String.format(promptTemplate, url, truncatedHtml, truncatedCssContent);
        } catch (IOException e) {
            throw new ServiceFailureException("Failed to read prompt template file for website metadata extractor", e);
        }
    }

    private static String createBedrockRequestBody(String prompt) throws IOException {
        ObjectNode messageNode = OBJECT_MAPPER.createObjectNode();
        messageNode.put("role", "user");
        messageNode.put("content", prompt);

        ObjectNode requestNode = OBJECT_MAPPER.createObjectNode();
        requestNode.put("anthropic_version", "bedrock-2023-05-31");
        requestNode.put("max_tokens", 1000);
        requestNode.put("temperature", 0.0);
        requestNode.put(
            "system",
            "You are an expert web designer who extracts branding information from websites. " +
            "You analyze HTML and CSS to identify logos, brand colors, typography, and other visual elements with high accuracy."
        );

        requestNode.putArray("messages").add(messageNode);

        return OBJECT_MAPPER.writeValueAsString(requestNode);
    }

    private static JsonNode parseClaudeResponse(String claudeResponse) throws IOException {
        JsonNode responseJson = OBJECT_MAPPER.readTree(claudeResponse);

        String content = extractContentFromResponse(responseJson);

        int jsonStart = content.indexOf("{");
        int jsonEnd = content.lastIndexOf("}") + 1;

        if (jsonStart == -1 || jsonEnd == 0 || jsonEnd <= jsonStart) {
            throw new IOException("Failed to extract JSON from Claude response: " + content);
        }

        String jsonString = content.substring(jsonStart, jsonEnd);
        return OBJECT_MAPPER.readTree(jsonString);
    }

    private static String extractContentFromResponse(JsonNode responseJson) throws IOException {
        if (responseJson.has("completion")) {
            return responseJson.path("completion").asText();
        }

        if (responseJson.has("content") && responseJson.path("content").isArray()) {
            return responseJson.path("content").path(0).path("text").asText();
        }

        if (responseJson.has("messages") && responseJson.get("messages").isArray() && !responseJson.get("messages").isEmpty()) {
            return responseJson.path("messages").path(0).path("content").path(0).path("text").asText();
        }

        LOGGER.info("Unknown Bedrock response format: {}", responseJson);
        throw new IOException("Unknown Bedrock response format");
    }

    private IntelligentSalesRoomWebsiteMetadata convertToWebsiteMetadata(JsonNode metadata, String baseUrl) {
        Optional<LogoMetadata> logo = processLogo(metadata, baseUrl);
        Optional<ColorMetadata> primary = processColor(metadata, "primary");
        Optional<ColorMetadata> secondary = processColor(metadata, "secondary");
        Optional<TypographyMetadata> fontFamily = processTypography(metadata, "fontFamily");
        Optional<TypographyMetadata> fontSize = processTypography(metadata, "fontSize");

        boolean complete = logo.isPresent() && primary.isPresent() && secondary.isPresent();

        return new IntelligentSalesRoomWebsiteMetadata(
            logo.orElse(null),
            primary.orElse(null),
            secondary.orElse(null),
            fontFamily.orElse(null),
            fontSize.orElse(null),
            complete
        );
    }

    private Optional<LogoMetadata> processLogo(JsonNode metadata, String baseUrl) {
        JsonNode logoNode = metadata.path("logo");
        if (logoNode.isMissingNode() || logoNode.isNull()) {
            return Optional.empty();
        }

        String logoUrl = logoNode.path("url").asText(null);
        String absoluteLogoUrl = logoNode.path("absoluteUrl").asText(null);

        if (StringUtils.isNotEmpty(logoUrl) && !logoUrl.startsWith("http")) {
            absoluteLogoUrl = resolveRelativeUrl(baseUrl, logoUrl);
        }

        if (StringUtils.isNotEmpty(absoluteLogoUrl) && webScraper.isUrlAccessible(absoluteLogoUrl) && webScraper.isImageUrl(absoluteLogoUrl)) {
            return Optional.of(new LogoMetadata(logoUrl, absoluteLogoUrl, logoNode.path("confidence").asDouble(0.0)));
        } else {
            LOGGER.info("Logo URL is not accessible or not an image: {}", absoluteLogoUrl);
            return Optional.empty();
        }
    }

    private static Optional<ColorMetadata> processColor(JsonNode metadata, String colorType) {
        JsonNode colorNode = metadata.path(colorType);
        if (colorNode.isMissingNode() || colorNode.isNull()) {
            return Optional.empty();
        }

        String colorHex = colorNode.path("color").asText(null);
        if (StringUtils.isNotEmpty(colorHex) && isValidHexColor(colorHex)) {
            return Optional.of(new ColorMetadata(colorHex, colorNode.path("confidence").asDouble(0.0), colorNode.path("location").asText("unknown")));
        } else {
            LOGGER.info("Invalid {} color hex code: {}", colorType, colorHex);
            return Optional.empty();
        }
    }

    private static Optional<TypographyMetadata> processTypography(JsonNode metadata, String typographyType) {
        JsonNode typographyNode = metadata.path(typographyType);
        if (typographyNode.isMissingNode() || typographyNode.isNull()) {
            return Optional.empty();
        }

        String value = typographyNode.path("value").asText(null);
        if (StringUtils.isNotEmpty(value)) {
            return Optional.of(new TypographyMetadata(value, typographyNode.path("confidence").asDouble(0.0)));
        } else {
            return Optional.empty();
        }
    }

    private static String resolveRelativeUrl(String baseUrl, String relativeUrl) {
        if (relativeUrl == null || relativeUrl.isEmpty()) {
            return null;
        }

        if (relativeUrl.startsWith("http://") || relativeUrl.startsWith("https://")) {
            return relativeUrl;
        }

        try {
            URL base = new URL(baseUrl);
            URL absolute = new URL(base, relativeUrl);
            return absolute.toString();
        } catch (Exception e) {
            LOGGER.info("Failed to resolve relative URL: {}", relativeUrl);
            return relativeUrl;
        }
    }

    private static boolean isValidHexColor(String color) {
        return color != null && HEX_COLOR_PATTERN.matcher(color).matches();
    }
}
