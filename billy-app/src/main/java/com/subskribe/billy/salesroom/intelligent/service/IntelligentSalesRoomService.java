package com.subskribe.billy.salesroom.intelligent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.io.Resources;
import com.google.common.util.concurrent.UncheckedExecutionException;
import com.subskribe.ai.service.bedrock.BedrockClientProvider;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldProxy;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.esign.model.ElectronicSignature;
import com.subskribe.billy.esign.model.ElectronicSignatureProvider;
import com.subskribe.billy.esign.model.ElectronicSignatureStatus;
import com.subskribe.billy.esign.model.EsignRequest;
import com.subskribe.billy.esign.services.EsignIntegrationService;
import com.subskribe.billy.esign.services.EsignService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.tenant.TenantDetails;
import com.subskribe.billy.graphql.tenant.TenantDetailsMapper;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderStub;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderPdfGenerationTrackerService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountMapper;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.resources.json.tenant.UserMapper;
import com.subskribe.billy.salesroom.intelligent.IntelligentSalesRoomProductMetadata;
import com.subskribe.billy.salesroom.intelligent.db.IntelligentSalesRoomDAO;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoom;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomAIGeneratedContent;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomActivityLog;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomEngagementSession;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomFile;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomShareLinkAccess;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomTheme;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomWidget;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoom;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomAIGeneratedContent;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomActivity;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomActivityLog;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomConstants;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomEngagementSession;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomFile;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomFileCategory;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomFileType;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomFileValidationResult;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomShareLinkAccess;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomStatus;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomTheme;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomThemeTemplate;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWebsiteMetadata;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWidget;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWidgetSortOrderUpdate;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWidgetType;
import com.subskribe.billy.salesroom.intelligent.model.mapper.IntelligentSalesRoomMapper;
import com.subskribe.billy.salesroom.intelligent.model.response.ImmutableIntelligentSalesRoomEngagementSummaryResponse;
import com.subskribe.billy.salesroom.intelligent.model.response.ImmutableIntelligentSalesRoomOrderMetadataResponse;
import com.subskribe.billy.salesroom.intelligent.model.response.ImmutableIntelligentSalesRoomOverviewResponse;
import com.subskribe.billy.salesroom.intelligent.model.response.IntelligentSalesRoomEngagementSummaryResponse;
import com.subskribe.billy.salesroom.intelligent.model.response.IntelligentSalesRoomOrderMetadataResponse;
import com.subskribe.billy.salesroom.intelligent.model.response.IntelligentSalesRoomOverviewResponse;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.DataValidation;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.logger.ErrorContext;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.Tenant;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.ws.rs.ForbiddenException;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;
import org.mapstruct.factory.Mappers;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelRequest;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelResponse;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class IntelligentSalesRoomService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IntelligentSalesRoomService.class);

    private static final List<IntelligentSalesRoomWidgetType> SYSTEM_WIDGET_TYPES = Arrays.stream(IntelligentSalesRoomWidgetType.values())
        .filter(IntelligentSalesRoomWidgetType::isSystemGenerated)
        .toList();

    private static final int SALES_ROOM_EXPIRATION_SECONDS = 60 * 60 * 24 * 60;

    private static final String AI_GENERATION_PROMPT = "ai/salesroom/claude_salesroom_content_generation_prompt.txt";

    private static final String DURATION_FORMAT_PATTERN = "H'h' m'm' s's'";

    private static final String AI_CONTENT_TYPE_FIELD = "type";

    private static final String AI_CONTENT_ID_FIELD = "aiContentId";

    private static final String AI_DISPLAY_CONTENT_FIELD = "displayContent";

    private static final String LOGO_URL_FORMAT = "/intelligent-sales-rooms/share/%s/image/%s";

    private static final Cache<String, IntelligentSalesRoom> SALES_ROOM_BY_SHARE_LINK_CACHE = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(15, TimeUnit.MINUTES)
        .build();

    private final IntelligentSalesRoomDAO intelligentSalesRoomDAO;

    private final OrderGetService orderGetService;

    private final FeatureService featureService;

    private final IntelligentSalesRoomMapper mapper;

    private final IntelligentSalesRoomS3Service intelligentSalesRoomS3Service;

    private final WebsiteMetadataExtractor websiteMetadataExtractor;

    private final AccountGetService accountGetService;

    private final UserService userService;

    private final ProductCatalogGetService productCatalogGetService;

    private final BedrockClientProvider bedrockClientProvider;

    private final TenantIdProvider tenantIdProvider;

    private final OrderPdfGenerationTrackerService orderPdfGenerationTrackerService;

    private final EsignIntegrationService esignIntegrationService;

    private final OrderService orderService;

    private final EsignService esignService;

    private final TenantDetailsMapper tenantDetailsMapper;

    private final TenantService tenantService;

    private final CustomFieldAPIMapper customFieldAPIMapper;

    private final CustomFieldProxy customFieldProxy;

    private final UserMapper userMapper;

    private final AccountMapper accountMapper;

    @Inject
    public IntelligentSalesRoomService(
        IntelligentSalesRoomDAO intelligentSalesRoomDAO,
        OrderGetService orderGetService,
        FeatureService featureService,
        IntelligentSalesRoomS3Service intelligentSalesRoomS3Service,
        WebsiteMetadataExtractor websiteMetadataExtractor,
        AccountGetService accountGetService,
        UserService userService,
        ProductCatalogGetService productCatalogGetService,
        BedrockClientProvider bedrockClientProvider,
        TenantIdProvider tenantIdProvider,
        OrderPdfGenerationTrackerService orderPdfGenerationTrackerService,
        EsignIntegrationService esignIntegrationService,
        OrderService orderService,
        EsignService esignService,
        TenantService tenantService,
        CustomFieldProxy customFieldProxy
    ) {
        this.intelligentSalesRoomDAO = intelligentSalesRoomDAO;
        this.orderGetService = orderGetService;
        this.featureService = featureService;
        this.intelligentSalesRoomS3Service = intelligentSalesRoomS3Service;
        this.websiteMetadataExtractor = websiteMetadataExtractor;
        this.accountGetService = accountGetService;
        this.userService = userService;
        this.productCatalogGetService = productCatalogGetService;
        this.bedrockClientProvider = bedrockClientProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.orderPdfGenerationTrackerService = orderPdfGenerationTrackerService;
        this.esignIntegrationService = esignIntegrationService;
        this.orderService = orderService;
        this.esignService = esignService;
        this.tenantService = tenantService;
        this.customFieldProxy = customFieldProxy;
        mapper = Mappers.getMapper(IntelligentSalesRoomMapper.class);
        tenantDetailsMapper = Mappers.getMapper(TenantDetailsMapper.class);
        customFieldAPIMapper = Mappers.getMapper(CustomFieldAPIMapper.class);
        userMapper = Mappers.getMapper(UserMapper.class);
        accountMapper = Mappers.getMapper(AccountMapper.class);
    }

    public IntelligentSalesRoomOverviewResponse createSalesRoom(String orderId) {
        if (featureService.isNotEnabled(Feature.INTELLIGENT_SALES_ROOM)) {
            throw new ForbiddenException("Not allowed.");
        }

        Validator.validateStringNotBlank(orderId, "Order ID is required.");

        Order order = orderGetService.getOrderByOrderId(orderId);
        validateOrderStatusForNewSalesRoom(order.getStatus());

        List<IntelligentSalesRoom> activeSalesRooms = intelligentSalesRoomDAO.getActiveSalesRoomsForOrder(orderId);
        if (!activeSalesRooms.isEmpty()) {
            throw new ConflictingStateException(String.format("Active sales room already exists for order: %s", orderId));
        }

        IntelligentSalesRoomStatus initialStatus = (order.getStatus() == OrderStatus.APPROVED)
            ? IntelligentSalesRoomStatus.READY_TO_SHARE
            : IntelligentSalesRoomStatus.DRAFT;

        IntelligentSalesRoom salesRoom = ImmutableIntelligentSalesRoom.builder()
            .id(AutoGenerate.getNewUuid())
            .tenantId(order.getTenantId())
            .accountId(order.getAccountId())
            .orderId(orderId)
            .status(initialStatus)
            .shareLink(AutoGenerate.getNewUuid())
            .isFirstAccess(true)
            .createdOn(Instant.now())
            .updatedOn(Instant.now())
            .expiresOn(Instant.now().plusSeconds(SALES_ROOM_EXPIRATION_SECONDS))
            .build();

        IntelligentSalesRoom createdSalesRoom = intelligentSalesRoomDAO.createSalesRoom(salesRoom);

        createDefaultSystemWidgets(createdSalesRoom.getId().toString(), order.getTenantId());

        orderPdfGenerationTrackerService.setShouldRegeneratePdf(orderId, true);

        IntelligentSalesRoomOverviewResponse salesRoomOverview = mapper.toOverviewResponse(createdSalesRoom);
        var response = addTenantAndEsignInfoToSalesRoomOverview(salesRoomOverview);
        LOGGER.info("Created intelligent sales room {}", response);
        return response;
    }

    private void validateOrderStatusForNewSalesRoom(OrderStatus status) {
        if (status != OrderStatus.DRAFT && status != OrderStatus.SUBMITTED && status != OrderStatus.APPROVED) {
            throw new InvalidInputException(String.format("Cannot create sales room for order in status: %s", status));
        }
    }

    private void createDefaultSystemWidgets(String salesRoomId, String tenantId) {
        int sortOrder = 1;

        for (IntelligentSalesRoomWidgetType widgetType : SYSTEM_WIDGET_TYPES) {
            if (
                widgetType != IntelligentSalesRoomWidgetType.E_SIGNATURE_BUTTON && widgetType != IntelligentSalesRoomWidgetType.ACCEPT_PROPOSAL_BUTTON
            ) {
                createSystemWidget(salesRoomId, tenantId, widgetType, sortOrder++);
            }
        }

        IntelligentSalesRoomWidgetType signatureWidgetType = determineSignatureWidgetType();
        createSystemWidget(salesRoomId, tenantId, signatureWidgetType, sortOrder);
    }

    private IntelligentSalesRoomWidgetType determineSignatureWidgetType() {
        Optional<ElectronicSignatureProvider> esignProvider = esignIntegrationService.getEsignProvider();
        return esignProvider.isPresent() ? IntelligentSalesRoomWidgetType.E_SIGNATURE_BUTTON : IntelligentSalesRoomWidgetType.ACCEPT_PROPOSAL_BUTTON;
    }

    private void createSystemWidget(String salesRoomId, String tenantId, IntelligentSalesRoomWidgetType widgetType, int sortOrder) {
        String widgetName = widgetType.getSystemGeneratedName().orElse(widgetType.name());
        IntelligentSalesRoomWidget widget = ImmutableIntelligentSalesRoomWidget.builder()
            .id(AutoGenerate.getNewUuid())
            .tenantId(tenantId)
            .salesRoomId(salesRoomId)
            .type(widgetType)
            .name(widgetName)
            .sortOrder(sortOrder)
            .isUserGenerated(false)
            .isDeletable(false)
            .createdOn(Instant.now())
            .updatedOn(Instant.now())
            .build();

        intelligentSalesRoomDAO.createWidget(widget);
    }

    public IntelligentSalesRoomOverviewResponse getSalesRoomByOrderId(String orderId) {
        if (featureService.isNotEnabled(Feature.INTELLIGENT_SALES_ROOM)) {
            throw new ForbiddenException("Not allowed.");
        }

        Validator.validateStringNotBlank(orderId, "Order ID is required.");

        Optional<IntelligentSalesRoom> salesRoomOptional = intelligentSalesRoomDAO.getSalesRoomByOrderId(orderId);
        if (salesRoomOptional.isEmpty()) {
            return createSalesRoom(orderId);
        }

        if (salesRoomOptional.get().getIsFirstAccess()) {
            intelligentSalesRoomDAO.setIsFirstAccessToFalse(salesRoomOptional.get().getId(), salesRoomOptional.get().getTenantId());
        }

        IntelligentSalesRoomOverviewResponse salesRoomOverview = mapper.toOverviewResponse(salesRoomOptional.get());
        return addTenantAndEsignInfoToSalesRoomOverview(salesRoomOverview);
    }

    public IntelligentSalesRoomOverviewResponse getSalesRoomById(String salesRoomId) {
        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);
        if (salesRoom.getIsFirstAccess()) {
            intelligentSalesRoomDAO.setIsFirstAccessToFalse(salesRoom.getId(), salesRoom.getTenantId());
        }

        IntelligentSalesRoomOverviewResponse salesRoomOverview = mapper.toOverviewResponse(salesRoom);
        return addTenantAndEsignInfoToSalesRoomOverview(salesRoomOverview);
    }

    public IntelligentSalesRoomOverviewResponse getSalesRoomByShareLink(String shareLink) {
        IntelligentSalesRoom salesRoom = lookupSalesRoomByShareLink(shareLink);

        if (salesRoom.getStatus() == IntelligentSalesRoomStatus.DRAFT) {
            throw new InvalidInputException("Sales room is not ready yet.");
        }

        if (salesRoom.getStatus() == IntelligentSalesRoomStatus.EXPIRED) {
            throw new InvalidInputException("Sales room has expired.");
        }

        IntelligentSalesRoomOverviewResponse salesRoomOverview = mapper.toOverviewResponse(salesRoom);
        return addTenantAndEsignInfoToSalesRoomOverview(salesRoomOverview);
    }

    public List<IntelligentSalesRoomWidget> getWidgetsBySalesRoomId(String salesRoomId) {
        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");
        return intelligentSalesRoomDAO.getWidgetsBySalesRoomId(salesRoomUuid);
    }

    private IntelligentSalesRoomWidget getWidgetById(String widgetId) {
        Validator.validateStringNotBlank(widgetId, "Widget ID is required.");
        UUID widgetUuid = Validator.validateUUID(widgetId, "Widget ID is required.");

        Optional<IntelligentSalesRoomWidget> widget = intelligentSalesRoomDAO.getWidgetById(widgetUuid);
        if (widget.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.INTELLIGENT_SALES_ROOM_WIDGET, widgetId);
        }

        return widget.get();
    }

    public IntelligentSalesRoomWidget createUserWidget(String salesRoomId, IntelligentSalesRoomWidgetType type, String name, String content) {
        validateCreateUserWidgetArguments(salesRoomId, type, name);

        String validatedContent = validateAndFormatWidgetContent(type, content);

        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");

        List<IntelligentSalesRoomWidget> existingWidgets = intelligentSalesRoomDAO.getWidgetsBySalesRoomId(salesRoomUuid);
        int nextSortOrder = existingWidgets.stream().mapToInt(IntelligentSalesRoomWidget::getSortOrder).max().orElse(0) + 1;

        IntelligentSalesRoomWidget widget = ImmutableIntelligentSalesRoomWidget.builder()
            .id(AutoGenerate.getNewUuid())
            .tenantId(tenantIdProvider.provideTenantIdString())
            .salesRoomId(salesRoomId)
            .type(type)
            .name(name)
            .sortOrder(nextSortOrder)
            .isUserGenerated(true)
            .isDeletable(true)
            .content(validatedContent)
            .createdOn(Instant.now())
            .updatedOn(Instant.now())
            .build();

        return intelligentSalesRoomDAO.createWidget(widget);
    }

    private String validateAndFormatWidgetContent(IntelligentSalesRoomWidgetType type, String content) {
        if (content != null) {
            if (content.length() > IntelligentSalesRoomConstants.MAX_WIDGET_CONTENT_LENGTH) {
                throw new InvalidInputException(
                    "Widget content exceeds maximum length of " + IntelligentSalesRoomConstants.MAX_WIDGET_CONTENT_LENGTH + " characters"
                );
            }

            if (type == IntelligentSalesRoomWidgetType.RICH_TEXT || type == IntelligentSalesRoomWidgetType.AI_GENERATED_TEXT) {
                validateHtmlContent(content);
            }
        }

        if (content == null || content.trim().isEmpty()) {
            return "{}";
        }

        try {
            JacksonProvider.defaultMapper().readTree(content);
            return content;
        } catch (Exception e) {
            // Content is not valid JSON, so we need to wrap it appropriately based on the widget type
            return wrapContentAsJson(type, content);
        }
    }

    private String wrapContentAsJson(IntelligentSalesRoomWidgetType type, String content) {
        try {
            ObjectNode jsonObject = getJsonObjectForWidgetType(type, content);
            return JacksonProvider.defaultMapper().writeValueAsString(jsonObject);
        } catch (Exception e) {
            throw new InvalidInputException("Failed to format widget content as JSON.", e);
        }
    }

    private static ObjectNode getJsonObjectForWidgetType(IntelligentSalesRoomWidgetType type, String content) {
        return switch (type) {
            case RICH_TEXT, AI_GENERATED_TEXT -> {
                ObjectNode jsonObject = JacksonProvider.defaultMapper().createObjectNode();
                jsonObject.put("html", content);
                yield jsonObject;
            }
            case MEDIA_CONTENT -> {
                ObjectNode jsonObject = JacksonProvider.defaultMapper().createObjectNode();
                jsonObject.put("url", content);
                jsonObject.put("type", "link");
                yield jsonObject;
            }
            default -> {
                ObjectNode jsonObject = JacksonProvider.defaultMapper().createObjectNode();
                jsonObject.put("text", content);
                yield jsonObject;
            }
        };
    }

    private void validateCreateUserWidgetArguments(String salesRoomId, IntelligentSalesRoomWidgetType type, String name) {
        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");
        Validator.validateNonNullArgument(type, "Widget type is required.");
        Validator.validateStringNotBlank(name, "Widget name is required.");

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);

        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot add widgets to sales room in status: " + salesRoom.getStatus());
        }

        if (type.isSystemGenerated()) {
            throw new InvalidInputException("Cannot create system-generated widget type: " + type);
        }

        int currentCount = intelligentSalesRoomDAO.getUserGeneratedWidgetCount(salesRoomUuid);
        if (currentCount >= IntelligentSalesRoomConstants.MAX_WIDGETS_PER_SALES_ROOM) {
            throw new InvalidInputException("Maximum number of widgets reached: " + IntelligentSalesRoomConstants.MAX_WIDGETS_PER_SALES_ROOM);
        }
    }

    public IntelligentSalesRoomWidget updateWidget(String widgetId, String name, String content) {
        IntelligentSalesRoomWidget existingWidget = getWidgetById(widgetId);

        String validatedContent = validateAndFormatWidgetContent(existingWidget.getType(), content);

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(existingWidget.getSalesRoomId());
        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot edit widgets in sales room with status: " + salesRoom.getStatus());
        }

        if (!existingWidget.getIsUserGenerated() && !existingWidget.getName().map(n -> n.equals(name)).orElse(false)) {
            throw new InvalidInputException("Cannot change name of system-generated widget");
        }

        IntelligentSalesRoomWidget updatedWidget = ImmutableIntelligentSalesRoomWidget.builder()
            .from(existingWidget)
            .name(name)
            .content(validatedContent)
            .build();

        IntelligentSalesRoomWidget response = intelligentSalesRoomDAO.updateWidget(updatedWidget);
        if (response.getType() == IntelligentSalesRoomWidgetType.CUSTOMER_INFORMATION) {
            orderPdfGenerationTrackerService.setShouldRegeneratePdf(salesRoom.getOrderId(), true);
        }
        return response;
    }

    public void deleteWidget(String widgetId) {
        IntelligentSalesRoomWidget widget = getWidgetById(widgetId);

        if (!widget.getIsDeletable()) {
            throw new InvalidInputException("Widget is not deletable: " + widgetId);
        }

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(widget.getSalesRoomId());
        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot delete widgets in sales room with status: " + salesRoom.getStatus());
        }

        intelligentSalesRoomDAO.deleteWidget(widget.getId());
    }

    public List<IntelligentSalesRoomFile> getFilesBySalesRoomId(String salesRoomId) {
        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Salesroom ID is required.");
        return intelligentSalesRoomDAO.getFilesBySalesRoomId(salesRoomUuid);
    }

    public IntelligentSalesRoomFile getFileById(String fileId) {
        Validator.validateStringNotBlank(fileId, "File ID is required.");

        UUID fileUuid = Validator.validateUUID(fileId, "fileId");

        Optional<IntelligentSalesRoomFile> file = intelligentSalesRoomDAO.getFileById(fileUuid);
        if (file.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.INTELLIGENT_SALES_ROOM_FILE, fileId);
        }

        return file.get();
    }

    public IntelligentSalesRoomFile uploadFile(
        String salesRoomId,
        String originalFileName,
        InputStream fileStream,
        long fileSize,
        String contentType,
        IntelligentSalesRoomFileCategory category
    ) {
        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");
        Validator.validateStringNotBlank(originalFileName, "Original file name is required.");
        Validator.validateNonNullArgument(fileStream, "File stream is required.");
        Validator.validateNonNullArgument(category, "File category is required.");

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);
        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot upload files to sales room in status: " + salesRoom.getStatus());
        }

        int currentFileCount = intelligentSalesRoomDAO.getFileCount(salesRoomUuid);
        if (currentFileCount >= IntelligentSalesRoomConstants.MAX_FILES_PER_SALES_ROOM) {
            throw new InvalidInputException("Maximum number of files reached: " + IntelligentSalesRoomConstants.MAX_FILES_PER_SALES_ROOM);
        }

        IntelligentSalesRoomFileValidationResult validation = intelligentSalesRoomS3Service.validateFile(originalFileName, fileSize, contentType);
        if (!validation.getIsValid()) {
            throw new InvalidInputException("File validation failed: " + validation.getErrorMessage().orElse("Unknown error"));
        }

        try {
            String fileName = intelligentSalesRoomS3Service.uploadFile(salesRoomId, originalFileName, fileStream, fileSize, contentType);
            IntelligentSalesRoomFile file = ImmutableIntelligentSalesRoomFile.builder()
                .id(AutoGenerate.getNewUuid())
                .tenantId(salesRoom.getTenantId())
                .salesRoomId(salesRoomId)
                .fileName(fileName)
                .originalFileName(originalFileName)
                .fileSize(fileSize)
                .mimeType(contentType != null ? contentType : intelligentSalesRoomS3Service.detectContentType(originalFileName))
                .fileType(validation.getFileType().orElse(IntelligentSalesRoomFileType.OTHER))
                .fileCategory(category.name())
                .updatedOn(Instant.now())
                .createdOn(Instant.now())
                .build();

            return intelligentSalesRoomDAO.createFile(file);
        } catch (InvalidInputException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error(
                new ErrorContext(IntelligentSalesRoomProductMetadata.FILE_MANAGEMENT, IntelligentSalesRoomProductMetadata.FILE_UPLOAD_FAILED),
                "Failed to upload file for sales room: {}",
                salesRoomId,
                e
            );
            throw new ServiceFailureException("Failed to upload file", e);
        }
    }

    public void deleteFile(String fileId) {
        IntelligentSalesRoomFile file = getFileById(fileId);

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(file.getSalesRoomId());
        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot delete files from sales room in status: " + salesRoom.getStatus());
        }

        try {
            intelligentSalesRoomS3Service.deleteFile(file.getSalesRoomId(), file.getFileName());
            intelligentSalesRoomDAO.deleteFile(file.getId());
        } catch (Exception e) {
            LOGGER.error(
                new ErrorContext(IntelligentSalesRoomProductMetadata.FILE_MANAGEMENT, IntelligentSalesRoomProductMetadata.FILE_UPLOAD_FAILED),
                "Failed to delete a file: {}",
                fileId,
                e
            );
            throw new ServiceFailureException("Failed to delete a file", e);
        }
    }

    public String generateFileDownloadUrl(String fileId, Duration expiration) {
        IntelligentSalesRoomFile file = getFileById(fileId);
        return intelligentSalesRoomS3Service.generatePresignedUrl(file.getSalesRoomId(), file.getFileName(), expiration);
    }

    public List<IntelligentSalesRoomFile> getFilesByCategory(String salesRoomId, IntelligentSalesRoomFileCategory category) {
        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");
        Validator.validateNonNullArgument(category, "File category is required.");
        return intelligentSalesRoomDAO.getFilesByCategory(salesRoomUuid, category);
    }

    private IntelligentSalesRoom getSalesRoomByIdInternal(String salesRoomId) {
        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");

        Optional<IntelligentSalesRoom> salesRoomOptional = intelligentSalesRoomDAO.getSalesRoomById(salesRoomUuid);
        if (salesRoomOptional.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.INTELLIGENT_SALES_ROOM, salesRoomId);
        }

        return salesRoomOptional.get();
    }

    public Optional<IntelligentSalesRoomTheme> getThemeBySalesRoomId(String salesRoomId) {
        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");
        return intelligentSalesRoomDAO.getThemeBySalesRoomId(salesRoomUuid);
    }

    public IntelligentSalesRoomTheme updateTheme(
        String salesRoomId,
        Optional<String> websiteUrl,
        Optional<String> logoUrl,
        Optional<String> primaryColor,
        Optional<String> secondaryColor,
        IntelligentSalesRoomThemeTemplate template
    ) {
        Validator.validateNonNullArgument(template, "Theme template is required.");
        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);
        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot update theme for sales room in status: " + salesRoom.getStatus());
        }

        IntelligentSalesRoomTheme theme = ImmutableIntelligentSalesRoomTheme.builder()
            .id(AutoGenerate.getNewUuid())
            .tenantId(salesRoom.getTenantId())
            .salesRoomId(salesRoomId)
            .websiteUrl(websiteUrl)
            .logoUrl(logoUrl)
            .primaryColor(primaryColor)
            .secondaryColor(secondaryColor)
            .template(template)
            .isLogoAutoExtracted(false)
            .isColorAutoExtracted(false)
            .createdOn(Instant.now())
            .updatedOn(Instant.now())
            .build();

        return intelligentSalesRoomDAO.createOrUpdateTheme(theme);
    }

    public IntelligentSalesRoomTheme extractThemeFromWebsite(String salesRoomId, String websiteUrl) {
        Validator.validateStringNotBlank(websiteUrl, "Website URL is required.");
        validateUrl(websiteUrl);

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);
        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot extract theme for sales room in status: " + salesRoom.getStatus());
        }

        try {
            Optional<IntelligentSalesRoomWebsiteMetadata> metadata = websiteMetadataExtractor.extractMetadata(websiteUrl);

            if (metadata.isEmpty()) {
                LOGGER.warn(IntelligentSalesRoomProductMetadata.THEME_CUSTOMIZATION, "Failed to extract metadata from website: {}", websiteUrl);
                IntelligentSalesRoomTheme theme = ImmutableIntelligentSalesRoomTheme.builder()
                    .id(AutoGenerate.getNewUuid())
                    .tenantId(salesRoom.getTenantId())
                    .salesRoomId(salesRoomId)
                    .websiteUrl(sanitizeUrlForDatabase(websiteUrl))
                    .template(IntelligentSalesRoomThemeTemplate.TEMPLATE_1)
                    .isLogoAutoExtracted(false)
                    .isColorAutoExtracted(false)
                    .createdOn(Instant.now())
                    .updatedOn(Instant.now())
                    .build();

                return intelligentSalesRoomDAO.createOrUpdateTheme(theme);
            }

            IntelligentSalesRoomWebsiteMetadata extractedData = metadata.get();
            LOGGER.info("Extracted metadata from website {}: {}", websiteUrl, extractedData);

            ImmutableIntelligentSalesRoomTheme.Builder themeBuilder = ImmutableIntelligentSalesRoomTheme.builder()
                .id(AutoGenerate.getNewUuid())
                .tenantId(salesRoom.getTenantId())
                .salesRoomId(salesRoomId)
                .websiteUrl(sanitizeUrlForDatabase(websiteUrl))
                .template(IntelligentSalesRoomThemeTemplate.TEMPLATE_1)
                .isLogoAutoExtracted(extractedData.logo() != null)
                .isColorAutoExtracted(extractedData.primary() != null)
                .createdOn(Instant.now())
                .updatedOn(Instant.now());

            if (extractedData.logo() != null) {
                uploadAndAddLogoToTheme(salesRoomId, extractedData, themeBuilder);
            }

            if (extractedData.primary() != null) {
                themeBuilder.primaryColor(sanitizeUrlForDatabase(extractedData.primary().color()));
            }

            if (extractedData.secondary() != null) {
                themeBuilder.secondaryColor(sanitizeUrlForDatabase(extractedData.secondary().color()));
            }

            IntelligentSalesRoomTheme theme = themeBuilder.build();

            LOGGER.info("Successfully extracted theme from website {} for sales room {}", websiteUrl, salesRoomId);
            return intelligentSalesRoomDAO.createOrUpdateTheme(theme);
        } catch (Exception e) {
            LOGGER.warn(
                IntelligentSalesRoomProductMetadata.THEME_CUSTOMIZATION,
                "Failed to extract theme from website {} for sales room {}",
                websiteUrl,
                salesRoomId,
                e
            );
            return ImmutableIntelligentSalesRoomTheme.builder()
                .id(AutoGenerate.getNewUuid())
                .tenantId(salesRoom.getTenantId())
                .salesRoomId(salesRoomId)
                .websiteUrl(sanitizeUrlForDatabase(websiteUrl))
                .template(IntelligentSalesRoomThemeTemplate.TEMPLATE_1)
                .isLogoAutoExtracted(false)
                .isColorAutoExtracted(false)
                .createdOn(Instant.now())
                .updatedOn(Instant.now())
                .build();
        }
    }

    private void uploadAndAddLogoToTheme(
        String salesRoomId,
        IntelligentSalesRoomWebsiteMetadata extractedData,
        ImmutableIntelligentSalesRoomTheme.Builder themeBuilder
    ) {
        try {
            Optional<String> logoFileName = intelligentSalesRoomS3Service.uploadLogoFromUrl(salesRoomId, extractedData.logo().absoluteUrl());

            if (logoFileName.isPresent()) {
                String logoApiUrl = String.format(LOGO_URL_FORMAT, getSalesRoomByIdInternal(salesRoomId).getShareLink(), logoFileName.get());

                themeBuilder.logoUrl(sanitizeUrlForDatabase(logoApiUrl));
                themeBuilder.isLogoAutoExtracted(true);
                LOGGER.info("Successfully uploaded logo to S3 for sales room {} from URL {}", salesRoomId, extractedData.logo().absoluteUrl());
            } else {
                themeBuilder.isLogoAutoExtracted(false);
                LOGGER.warn(
                    IntelligentSalesRoomProductMetadata.THEME_CUSTOMIZATION,
                    "Logo was detected but failed to upload for sales room {} from URL {}",
                    salesRoomId,
                    extractedData.logo().absoluteUrl()
                );
            }
        } catch (Exception e) {
            themeBuilder.isLogoAutoExtracted(false);
            LOGGER.warn(
                IntelligentSalesRoomProductMetadata.THEME_CUSTOMIZATION,
                "Failed to process logo for sales room {} from URL {}, continuing without logo",
                salesRoomId,
                extractedData.logo().absoluteUrl(),
                e
            );
        }
    }

    public void activateSalesRoom(String salesRoomId) {
        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);

        if (salesRoom.getStatus() != IntelligentSalesRoomStatus.READY_TO_SHARE) {
            throw new InvalidInputException(String.format("Cannot activate sales room in status %s. Must be READY_TO_SHARE.", salesRoom.getStatus()));
        }

        if (salesRoom.isExpired()) {
            throw new InvalidInputException("Cannot activate expired sales room");
        }

        intelligentSalesRoomDAO.updateStatusAndSharedOn(salesRoom.getId(), IntelligentSalesRoomStatus.ACTIVE);
        SALES_ROOM_BY_SHARE_LINK_CACHE.invalidate(salesRoom.getShareLink().toString());
        LOGGER.info("Activated sales room {}.", salesRoomId);
    }

    public void retractSalesRoom(String salesRoomId) {
        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);

        if (salesRoom.getStatus() != IntelligentSalesRoomStatus.ACTIVE) {
            throw new InvalidInputException(String.format("Cannot retract sales room in status %s. Must be ACTIVE.", salesRoom.getStatus()));
        }

        intelligentSalesRoomDAO.updateStatus(salesRoom.getId(), IntelligentSalesRoomStatus.READY_TO_SHARE);
        LOGGER.info("Retracted sales room {} back to READY_TO_SHARE", salesRoomId);
    }

    public void acceptSalesRoom(String salesRoomId) {
        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);
        acceptSalesRoomAndUpdateStatus(salesRoom);
    }

    private void acceptSalesRoomAndUpdateStatus(IntelligentSalesRoom salesRoom) {
        if (salesRoom.getStatus() != IntelligentSalesRoomStatus.READY_TO_SHARE && salesRoom.getStatus() != IntelligentSalesRoomStatus.ACTIVE) {
            throw new InvalidInputException(
                String.format("Cannot accept sales room in status %s. Must be READY_TO_SHARE or ACTIVE.", salesRoom.getStatus())
            );
        }

        if (salesRoom.isExpired()) {
            throw new InvalidInputException("Cannot accept expired sales room");
        }

        intelligentSalesRoomDAO.updateStatusAndAcceptedOn(salesRoom.getId(), IntelligentSalesRoomStatus.ACCEPTED);
        LOGGER.info("Accepted sales room {}.", salesRoom.getSalesRoomId());
    }

    public void transitionToReadyToShare(String salesRoomId) {
        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);

        if (salesRoom.getStatus() != IntelligentSalesRoomStatus.DRAFT) {
            throw new InvariantCheckFailedException(
                String.format("Cannot transition to READY_TO_SHARE from status %s. Must be DRAFT.", salesRoom.getStatus())
            );
        }

        intelligentSalesRoomDAO.updateStatus(salesRoom.getId(), IntelligentSalesRoomStatus.READY_TO_SHARE);
        LOGGER.info("Transitioned sales room {} from DRAFT to READY_TO_SHARE", salesRoomId);
    }

    private void transitionToDraft(String salesRoomId) {
        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);

        if (salesRoom.getStatus() != IntelligentSalesRoomStatus.READY_TO_SHARE && salesRoom.getStatus() != IntelligentSalesRoomStatus.ACTIVE) {
            throw new InvariantCheckFailedException(
                String.format("Cannot transition to DRAFT from status %s. Must be READY_TO_SHARE or ACTIVE.", salesRoom.getStatus())
            );
        }

        intelligentSalesRoomDAO.updateStatus(salesRoom.getId(), IntelligentSalesRoomStatus.DRAFT);
        LOGGER.info("Transitioned sales room {} to DRAFT", salesRoomId);
    }

    public IntelligentSalesRoomAIGeneratedContent generateAIContent(String salesRoomId, String widgetId) {
        Validator.validateStringNotBlank(widgetId, "Widget ID is required.");

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);
        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot generate AI content for sales room in status: " + salesRoom.getStatus());
        }

        IntelligentSalesRoomWidget widget = getWidgetById(widgetId);
        if (widget.getType() != IntelligentSalesRoomWidgetType.AI_GENERATED_TEXT) {
            throw new InvalidInputException("Widget must be of type AI_GENERATED_TEXT");
        }

        String orderContext = buildOrderContext(salesRoom.getOrderId(), salesRoom.getId());

        try {
            String generatedContent = generateContentWithClaude(orderContext);
            IntelligentSalesRoomAIGeneratedContent aiContent = ImmutableIntelligentSalesRoomAIGeneratedContent.builder()
                .id(AutoGenerate.getNewUuid())
                .tenantId(salesRoom.getTenantId())
                .salesRoomId(salesRoomId)
                .widgetId(widgetId)
                .generatedContent(generatedContent)
                .orderFormContext(orderContext)
                .isEdited(false)
                .generatedOn(Instant.now())
                .build();

            IntelligentSalesRoomAIGeneratedContent createdContent = intelligentSalesRoomDAO.addAIContent(aiContent);

            String widgetContent = createWidgetContentForAIGeneratedContent(createdContent.getId().toString(), generatedContent);
            updateWidget(widgetId, widget.getName().orElse("AI Generated Content"), widgetContent);

            LOGGER.info("Generated AI content for widget {} in sales room {}", widgetId, salesRoomId);
            return createdContent;
        } catch (Exception e) {
            LOGGER.warn(
                IntelligentSalesRoomProductMetadata.AI_CONTENT_GENERATION,
                "Failed to generate AI content for widget {} in sales room {}",
                widgetId,
                salesRoomId,
                e
            );

            return ImmutableIntelligentSalesRoomAIGeneratedContent.builder()
                .id(AutoGenerate.getNewUuid())
                .tenantId(salesRoom.getTenantId())
                .salesRoomId(salesRoomId)
                .widgetId(widgetId)
                .generatedContent(StringUtils.EMPTY)
                .orderFormContext(orderContext)
                .isEdited(false)
                .generatedOn(Instant.now())
                .build();
        }
    }

    public IntelligentSalesRoomAIGeneratedContent updateAIContent(String contentId, String editedContent) {
        UUID contentUuid = Validator.validateUUID(contentId, "Content ID is required.");
        Validator.validateStringNotBlank(editedContent, "Edited content is required.");

        Optional<IntelligentSalesRoomAIGeneratedContent> existingContent = intelligentSalesRoomDAO.getAIContentById(contentUuid);
        if (existingContent.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.INTELLIGENT_SALES_ROOM_AI_CONTENT, contentId);
        }

        IntelligentSalesRoomAIGeneratedContent content = existingContent.get();

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(content.getSalesRoomId());
        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot edit AI content for sales room in status: " + salesRoom.getStatus());
        }

        IntelligentSalesRoomAIGeneratedContent updatedContent = ImmutableIntelligentSalesRoomAIGeneratedContent.builder()
            .from(content)
            .generatedContent(editedContent)
            .isEdited(true)
            .lastEditedOn(Instant.now())
            .build();

        IntelligentSalesRoomAIGeneratedContent savedContent = intelligentSalesRoomDAO.updateAIContent(updatedContent);

        String widgetContent = createWidgetContentForAIGeneratedContent(contentId, editedContent);
        IntelligentSalesRoomWidget widget = getWidgetById(content.getWidgetId());
        updateWidget(content.getWidgetId(), widget.getName().orElse("AI Generated Content"), widgetContent);

        LOGGER.info("Updated AI content {} for sales room {}", contentId, content.getSalesRoomId());
        return savedContent;
    }

    private String buildOrderContext(String orderId, UUID salesRoomId) {
        try {
            Order order = orderGetService.getOrderByOrderId(orderId);
            Account account = accountGetService.getAccount(order.getAccountId());
            User salesRep = getSalesRep(order);

            Optional<IntelligentSalesRoomTheme> theme = intelligentSalesRoomDAO.getThemeBySalesRoomId(salesRoomId);
            String productInfo = getProductInformation(order);

            StringBuilder context = new StringBuilder();
            context.append("Order Information:\n");
            context.append("- Order Name: ").append(order.getName()).append("\n");
            context.append("- Total Amount: ").append(order.getTotalAmount()).append(" ").append(order.getCurrency()).append("\n");
            context.append("- Start Date: ").append(order.getStartDate()).append("\n");
            context.append("- End Date: ").append(order.getEndDate()).append("\n");
            context.append("- Payment Terms: ").append(order.getPaymentTerm()).append("\n");

            context.append("\nBuyer Information:\n");
            context.append("- Company: ").append(account.getName()).append("\n");

            if (salesRep != null) {
                context.append("\nSales Representative:\n");
                context.append("- Name: ").append(salesRep.getDisplayName()).append("\n");
                if (StringUtils.isNotBlank(salesRep.getTitle())) {
                    context.append("- Title: ").append(salesRep.getTitle()).append("\n");
                }
            }

            if (theme.isPresent() && theme.get().getWebsiteUrl().isPresent()) {
                context.append("\nSeller Website: ").append(theme.get().getWebsiteUrl().get()).append("\n");
            }

            context.append("\nProducts/Services:\n");
            context.append(productInfo);

            return context.toString();
        } catch (Exception e) {
            LOGGER.warn(IntelligentSalesRoomProductMetadata.AI_CONTENT_GENERATION, "Failed to build complete order context for order {}", orderId, e);
            return String.format("Order ID: %s%nBasic order information available.", orderId);
        }
    }

    private String getProductInformation(Order order) {
        StringBuilder productInfo = new StringBuilder();
        for (OrderLineItem lineItem : order.getLineItems()) {
            try {
                Plan plan = productCatalogGetService.getPlan(lineItem.getPlanId());
                Charge charge = productCatalogGetService.getChargeByChargeId(lineItem.getChargeId());

                productInfo.append(
                    String.format(
                        "- %s (%s): Quantity %d, Amount %s%n",
                        plan.getName(),
                        charge.getName(),
                        lineItem.getQuantity(),
                        lineItem.getAmount()
                    )
                );
            } catch (Exception e) {
                LOGGER.warn(
                    IntelligentSalesRoomProductMetadata.AI_CONTENT_GENERATION,
                    "Failed to get product info for plan {} charge {}",
                    lineItem.getPlanId(),
                    lineItem.getChargeId(),
                    e
                );
                productInfo.append(
                    String.format("- Plan %s, Charge %s: Quantity %d%n", lineItem.getPlanId(), lineItem.getChargeId(), lineItem.getQuantity())
                );
            }
        }
        return productInfo.toString();
    }

    private User getSalesRep(Order order) {
        User salesRep = null;
        if (StringUtils.isNotBlank(order.getOwnerId())) {
            try {
                salesRep = userService.getUser(order.getOwnerId());
            } catch (Exception e) {
                LOGGER.warn(
                    IntelligentSalesRoomProductMetadata.AI_CONTENT_GENERATION,
                    "Failed to get sales rep info for user {}",
                    order.getOwnerId(),
                    e
                );
            }
        }
        return salesRep;
    }

    private String generateContentWithClaude(String orderContext) throws IOException, InterruptedException, ExecutionException, TimeoutException {
        String prompt = String.format(loadPrompt(), orderContext);
        return callClaudeViaBedrock(prompt);
    }

    private static String loadPrompt() {
        try {
            return Resources.toString(Resources.getResource(AI_GENERATION_PROMPT), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new ServiceFailureException("Failed to read prompt template file for ai content generation", e);
        }
    }

    private String callClaudeViaBedrock(String prompt) throws IOException, InterruptedException, ExecutionException, TimeoutException {
        ObjectNode messageNode = JacksonProvider.defaultMapper().createObjectNode();
        messageNode.put("role", "user");
        messageNode.put("content", prompt);

        ObjectNode requestNode = JacksonProvider.defaultMapper().createObjectNode();
        requestNode.put("anthropic_version", "bedrock-2023-05-31");
        requestNode.put("max_tokens", 1000);
        requestNode.put("temperature", 0.3);
        requestNode.put("system", "You are a professional sales assistant that creates personalized content for sales rooms.");
        requestNode.putArray("messages").add(messageNode);

        String requestBody = JacksonProvider.defaultMapper().writeValueAsString(requestNode);

        InvokeModelRequest request = InvokeModelRequest.builder()
            .modelId("anthropic.claude-3-5-sonnet-20240620-v1:0")
            .contentType("application/json")
            .accept("application/json")
            .body(SdkBytes.fromUtf8String(requestBody))
            .build();

        CompletableFuture<InvokeModelResponse> responseFuture = bedrockClientProvider.getBedrockRuntimeAsyncClient().invokeModel(request);
        InvokeModelResponse response = responseFuture.get(30, TimeUnit.SECONDS);

        JsonNode responseJson = JacksonProvider.defaultMapper().readTree(response.body().asUtf8String());
        return responseJson.path("content").path(0).path("text").asText();
    }

    private String createWidgetContentForAIGeneratedContent(String aiContentId, String displayContent) {
        try {
            ObjectNode content = JacksonProvider.defaultMapper().createObjectNode();
            content.put(AI_CONTENT_TYPE_FIELD, IntelligentSalesRoomWidgetType.AI_GENERATED_TEXT.name());
            content.put(AI_CONTENT_ID_FIELD, aiContentId);
            content.put(AI_DISPLAY_CONTENT_FIELD, displayContent);

            return JacksonProvider.defaultMapper().writeValueAsString(content);
        } catch (Exception e) {
            LOGGER.warn(
                IntelligentSalesRoomProductMetadata.AI_CONTENT_GENERATION,
                "Failed to create widget content for AI generated content ID {}",
                aiContentId,
                e
            );
            return StringUtils.EMPTY;
        }
    }

    public IntelligentSalesRoomEngagementSession startUserSession(
        String salesRoomId,
        String userName,
        String userEmail,
        Optional<String> ipAddress,
        Optional<String> userAgent
    ) {
        UUID salesroomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");
        Validator.validateStringNotBlank(userName, "User name is required.");
        Validator.validateStringNotBlank(userEmail, "User email is required.");
        if (!DataValidation.isEmailValid(userEmail)) {
            throw new InvalidInputException("Invalid email: " + userEmail);
        }

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);
        if (salesRoom.getStatus() != IntelligentSalesRoomStatus.ACTIVE && salesRoom.getStatus() != IntelligentSalesRoomStatus.READY_TO_SHARE) {
            throw new InvalidInputException("Sales room is not accessible in status: " + salesRoom.getStatus());
        }

        Optional<IntelligentSalesRoomEngagementSession> activeSession = intelligentSalesRoomDAO.getActiveSessionByUserAndSalesRoom(
            salesroomUuid,
            userEmail
        );

        if (activeSession.isPresent()) {
            IntelligentSalesRoomEngagementSession session = activeSession.get();

            if (session.isSessionExpired()) {
                endUserSession(session.getSessionId());
            } else {
                IntelligentSalesRoomEngagementSession updatedSession = ImmutableIntelligentSalesRoomEngagementSession.builder()
                    .from(session)
                    .lastActivityTime(Instant.now())
                    .build();

                intelligentSalesRoomDAO.updateEngagementSession(updatedSession);

                LOGGER.info("Returning existing active session for user {} in sales room {} with updated activity time", userEmail, salesRoomId);
                return updatedSession;
            }
        }

        intelligentSalesRoomDAO.expireInactiveSessions(salesroomUuid);

        String sessionId = UUID.randomUUID().toString();
        IntelligentSalesRoomEngagementSession session = ImmutableIntelligentSalesRoomEngagementSession.builder()
            .id(AutoGenerate.getNewUuid())
            .tenantId(salesRoom.getTenantId())
            .salesRoomId(salesRoomId)
            .userName(userName)
            .userEmail(userEmail)
            .sessionId(sessionId)
            .sessionStart(Instant.now())
            .lastActivityTime(Instant.now())
            .durationSeconds(0L)
            .ipAddress(ipAddress)
            .userAgent(userAgent)
            .tabsVisited(List.of())
            .isActive(true)
            .build();

        IntelligentSalesRoomEngagementSession createdSession = intelligentSalesRoomDAO.createEngagementSession(session);

        logActivity(
            salesRoomId,
            sessionId,
            userName,
            userEmail,
            IntelligentSalesRoomActivity.SESSION_START,
            Optional.empty(),
            Optional.empty(),
            0L,
            Optional.empty()
        );

        LOGGER.info("Started new session {} for user {} in sales room {}", sessionId, userEmail, salesRoomId);
        return createdSession;
    }

    public void endUserSession(String sessionId) {
        UUID sessionUuid = Validator.validateUUID(sessionId, "Session ID is required.");
        Optional<IntelligentSalesRoomEngagementSession> sessionOptional = intelligentSalesRoomDAO.getSessionById(sessionUuid);
        if (sessionOptional.isEmpty()) {
            LOGGER.warn(IntelligentSalesRoomProductMetadata.ENGAGEMENT_TRACKING, "Session not found for ID: {}", sessionId);
            return;
        }

        IntelligentSalesRoomEngagementSession session = sessionOptional.get();

        if (!session.getIsActive()) {
            LOGGER.warn(IntelligentSalesRoomProductMetadata.ENGAGEMENT_TRACKING, "Session {} is already inactive", sessionId);
            return;
        }

        Instant now = Instant.now();
        long durationSeconds = Duration.between(session.getSessionStart(), now).getSeconds();

        IntelligentSalesRoomEngagementSession updatedSession = ImmutableIntelligentSalesRoomEngagementSession.builder()
            .from(session)
            .sessionEnd(now)
            .durationSeconds(durationSeconds)
            .isActive(false)
            .build();

        intelligentSalesRoomDAO.updateEngagementSession(updatedSession);

        logActivity(
            session.getSalesRoomId(),
            sessionId,
            session.getUserName(),
            session.getUserEmail(),
            IntelligentSalesRoomActivity.SESSION_END,
            Optional.empty(),
            Optional.empty(),
            durationSeconds,
            Optional.empty()
        );

        LOGGER.info("Ended session {} for user {} after {} seconds", sessionId, session.getUserEmail(), durationSeconds);
    }

    public void trackTabVisit(String sessionId, String tabName) {
        Validator.validateStringNotBlank(tabName, "Tab name is required.");
        var optionalSession = getSession(sessionId);
        if (optionalSession.isEmpty()) {
            return;
        }
        var session = optionalSession.get();
        List<String> updatedTabsVisited = new ArrayList<>(session.getTabsVisited());
        if (!updatedTabsVisited.contains(tabName)) {
            updatedTabsVisited.add(tabName);

            IntelligentSalesRoomEngagementSession updatedSession = ImmutableIntelligentSalesRoomEngagementSession.builder()
                .from(session)
                .tabsVisited(updatedTabsVisited)
                .lastActivityTime(Instant.now())
                .build();

            intelligentSalesRoomDAO.updateEngagementSession(updatedSession);
        }

        logActivity(
            session.getSalesRoomId(),
            sessionId,
            session.getUserName(),
            session.getUserEmail(),
            IntelligentSalesRoomActivity.TAB_VISIT,
            Optional.of(tabName),
            Optional.empty(),
            0L,
            Optional.empty()
        );

        LOGGER.info("Tracked tab visit to {} for session {}", tabName, sessionId);
    }

    public void trackActivity(String sessionId, Optional<String> widgetId, IntelligentSalesRoomActivity activity, long timeSpentSeconds) {
        Validator.validateNonNullArgument(activity, "Activity is required.");

        var optionalSession = getSession(sessionId);
        if (optionalSession.isEmpty()) {
            return;
        }
        var session = optionalSession.get();

        IntelligentSalesRoomEngagementSession updatedSession = ImmutableIntelligentSalesRoomEngagementSession.builder()
            .from(session)
            .lastActivityTime(Instant.now())
            .build();

        intelligentSalesRoomDAO.updateEngagementSession(updatedSession);

        logActivity(
            session.getSalesRoomId(),
            sessionId,
            session.getUserName(),
            session.getUserEmail(),
            activity,
            Optional.empty(),
            widgetId,
            timeSpentSeconds,
            Optional.empty()
        );

        LOGGER.info("Tracked {} activity with widget {} for session {}", activity, widgetId, sessionId);
    }

    public List<IntelligentSalesRoomActivityLog> getActivityLogs(String salesRoomId, Optional<Instant> fromDate, Optional<Instant> toDate) {
        var salesRoom = getSalesRoomByIdInternal(salesRoomId);
        return intelligentSalesRoomDAO.getActivityLogsBySalesRoomId(salesRoom.getId(), fromDate.orElse(null), toDate.orElse(null));
    }

    public List<IntelligentSalesRoomEngagementSession> getEngagementSessions(String salesRoomId) {
        var salesRoom = getSalesRoomByIdInternal(salesRoomId);
        return intelligentSalesRoomDAO.getSessionsBySalesRoomId(salesRoom.getId());
    }

    public IntelligentSalesRoomEngagementSummaryResponse getEngagementSummary(
        String salesRoomId,
        Optional<Instant> fromDate,
        Optional<Instant> toDate
    ) {
        var salesRoom = getSalesRoomByIdInternal(salesRoomId);
        List<IntelligentSalesRoomEngagementSession> sessions = intelligentSalesRoomDAO.getSessionsBySalesRoomId(
            salesRoom.getId(),
            fromDate.orElse(null),
            toDate.orElse(null)
        );

        List<IntelligentSalesRoomActivityLog> activities = intelligentSalesRoomDAO.getActivityLogsBySalesRoomId(
            salesRoom.getId(),
            fromDate.orElse(null),
            toDate.orElse(null)
        );

        Set<String> uniqueVisitors = sessions.stream().map(IntelligentSalesRoomEngagementSession::getUserEmail).collect(Collectors.toSet());

        long totalTimeSpent = sessions.stream().mapToLong(IntelligentSalesRoomEngagementSession::getDurationSeconds).sum();

        Optional<Instant> lastActivity = activities.stream().map(IntelligentSalesRoomActivityLog::getTimestamp).max(Instant::compareTo);

        Optional<String> mostActiveUser = sessions
            .stream()
            .collect(
                Collectors.groupingBy(
                    IntelligentSalesRoomEngagementSession::getUserEmail,
                    Collectors.summingLong(IntelligentSalesRoomEngagementSession::getDurationSeconds)
                )
            )
            .entrySet()
            .stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey);

        long seconds = uniqueVisitors.isEmpty() ? 0 : totalTimeSpent / uniqueVisitors.size();
        long durationMillis = seconds * 1000;

        String averageTimeSpent = DurationFormatUtils.formatDuration(durationMillis, DURATION_FORMAT_PATTERN);

        return ImmutableIntelligentSalesRoomEngagementSummaryResponse.builder()
            .totalVisitors(uniqueVisitors.size())
            .totalSessions(sessions.size())
            .totalTimeSpentSeconds(totalTimeSpent)
            .averageTimeSpent(averageTimeSpent)
            .lastActivity(lastActivity)
            .mostActiveUser(mostActiveUser)
            .build();
    }

    private void logActivity(
        String salesRoomId,
        String sessionId,
        String userName,
        String userEmail,
        IntelligentSalesRoomActivity activity,
        Optional<String> tabName,
        Optional<String> widgetId,
        long timeSpentSeconds,
        Optional<Map<String, Object>> metadata
    ) {
        try {
            IntelligentSalesRoomActivityLog activityLog = ImmutableIntelligentSalesRoomActivityLog.builder()
                .id(AutoGenerate.getNewUuid())
                .tenantId(tenantIdProvider.provideTenantIdString())
                .salesRoomId(salesRoomId)
                .sessionId(sessionId)
                .userName(userName)
                .userEmail(userEmail)
                .activity(activity)
                .tabName(tabName)
                .widgetId(widgetId)
                .timeSpentSeconds(timeSpentSeconds)
                .timestamp(Instant.now())
                .metadata(metadata)
                .build();

            intelligentSalesRoomDAO.createActivityLog(activityLog);
        } catch (Exception e) {
            LOGGER.error(
                new ErrorContext(
                    IntelligentSalesRoomProductMetadata.ENGAGEMENT_TRACKING,
                    IntelligentSalesRoomProductMetadata.ENGAGEMENT_TRACKING_FAILED
                ),
                "Failed to log activity {} for session {}",
                activity,
                sessionId,
                e
            );
        }
    }

    private Optional<IntelligentSalesRoomEngagementSession> getSession(String sessionId) {
        UUID sessionUuid = Validator.validateUUID(sessionId, "Session ID is required.");

        Optional<IntelligentSalesRoomEngagementSession> sessionOptional = intelligentSalesRoomDAO.getSessionById(sessionUuid);
        if (sessionOptional.isEmpty()) {
            LOGGER.warn(IntelligentSalesRoomProductMetadata.ENGAGEMENT_TRACKING, "Session not found for ID: {}", sessionId);
            return Optional.empty();
        }

        IntelligentSalesRoomEngagementSession session = sessionOptional.get();

        if (!session.getIsActive() || session.isSessionExpired()) {
            LOGGER.warn(IntelligentSalesRoomProductMetadata.ENGAGEMENT_TRACKING, "Session {} is not active or expired", sessionId);
            return Optional.empty();
        }

        return Optional.of(session);
    }

    public void reorderWidgets(String salesRoomId, List<String> orderedWidgetIds) {
        Validator.validateNonNullArgument(orderedWidgetIds, "Ordered widget IDs list is required.");

        if (orderedWidgetIds.isEmpty()) {
            throw new InvalidInputException("Ordered widget IDs list cannot be empty.");
        }

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);
        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot reorder widgets for sales room in status: " + salesRoom.getStatus());
        }

        List<IntelligentSalesRoomWidget> currentWidgets = intelligentSalesRoomDAO.getWidgetsBySalesRoomId(salesRoom.getId());
        Set<String> currentWidgetIds = currentWidgets.stream().map(widget -> widget.getId().toString()).collect(Collectors.toSet());

        Set<String> providedWidgetIds = new HashSet<>(orderedWidgetIds);
        throwIfWidgetIdsDoNotMatch(currentWidgetIds, providedWidgetIds);

        validateStickyWidgetPlacement(currentWidgets, orderedWidgetIds);

        List<IntelligentSalesRoomWidgetSortOrderUpdate> sortOrderUpdates = new ArrayList<>();
        for (int i = 0; i < orderedWidgetIds.size(); i++) {
            UUID widgetId = UUID.fromString(orderedWidgetIds.get(i));
            int newSortOrder = i + 1;
            sortOrderUpdates.add(IntelligentSalesRoomWidgetSortOrderUpdate.of(widgetId, newSortOrder));
        }

        intelligentSalesRoomDAO.updateWidgetSortOrders(salesRoomId, sortOrderUpdates);
        LOGGER.info("Reordered {} widgets for sales room {}", orderedWidgetIds.size(), salesRoomId);
    }

    private static void throwIfWidgetIdsDoNotMatch(Set<String> currentWidgetIds, Set<String> providedWidgetIds) {
        if (currentWidgetIds.equals(providedWidgetIds)) {
            return;
        }

        Set<String> missing = new HashSet<>(currentWidgetIds);
        missing.removeAll(providedWidgetIds);

        Set<String> extra = new HashSet<>(providedWidgetIds);
        extra.removeAll(currentWidgetIds);

        StringBuilder errorMsg = new StringBuilder("Widget ID mismatch:");
        if (!missing.isEmpty()) {
            errorMsg.append(" Missing: ").append(missing);
        }
        if (!extra.isEmpty()) {
            errorMsg.append(" Extra: ").append(extra);
        }

        throw new InvalidInputException(errorMsg.toString());
    }

    private void validateStickyWidgetPlacement(List<IntelligentSalesRoomWidget> currentWidgets, List<String> orderedWidgetIds) {
        List<IntelligentSalesRoomWidget> stickyWidgets = currentWidgets.stream().filter(this::isStickyWidget).toList();

        if (stickyWidgets.isEmpty()) {
            return;
        }

        int totalWidgets = orderedWidgetIds.size();
        int stickyCount = stickyWidgets.size();
        int firstStickyPosition = totalWidgets - stickyCount;
        for (IntelligentSalesRoomWidget stickyWidget : stickyWidgets) {
            String stickyWidgetId = stickyWidget.getId().toString();
            int newPosition = orderedWidgetIds.indexOf(stickyWidgetId);

            if (newPosition < firstStickyPosition) {
                throw new InvalidInputException(String.format("Widget type %s must remain at the bottom", stickyWidget.getType()));
            }
        }
    }

    private boolean isStickyWidget(IntelligentSalesRoomWidget widget) {
        return (
            widget.getType() == IntelligentSalesRoomWidgetType.E_SIGNATURE_BUTTON ||
            widget.getType() == IntelligentSalesRoomWidgetType.ACCEPT_PROPOSAL_BUTTON
        );
    }

    public IntelligentSalesRoomOverviewResponse accessSalesRoomViaShareLink(
        String shareLink,
        String userName,
        String userEmail,
        Optional<String> ipAddress,
        Optional<String> userAgent
    ) {
        Validator.validateStringNotBlank(shareLink, "Share link is required.");
        Validator.validateStringNotBlank(userName, "User name is required.");
        Validator.validateStringNotBlank(userEmail, "User email is required.");
        if (!DataValidation.isEmailValid(userEmail)) {
            throw new InvalidInputException("Invalid email: " + userEmail);
        }

        IntelligentSalesRoomOverviewResponse salesRoom = getSalesRoomByShareLink(shareLink);

        if (salesRoom.getStatus() != IntelligentSalesRoomStatus.ACTIVE && salesRoom.getStatus() != IntelligentSalesRoomStatus.READY_TO_SHARE) {
            throw new InvalidInputException("Sales room is not accessible in status: " + salesRoom.getStatus());
        }

        if (salesRoom.getStatus() == IntelligentSalesRoomStatus.DRAFT) {
            throw new InvalidInputException("Sales room is not ready yet.");
        }

        if (salesRoom.getStatus() == IntelligentSalesRoomStatus.EXPIRED) {
            throw new InvalidInputException("Sales room has expired");
        }

        Instant now = Instant.now();
        IntelligentSalesRoomShareLinkAccess access = ImmutableIntelligentSalesRoomShareLinkAccess.builder()
            .id(AutoGenerate.getNewUuid())
            .tenantId(salesRoom.getTenantId())
            .salesRoomId(salesRoom.getSalesRoomId().toString())
            .userName(userName)
            .userEmail(userEmail)
            .firstAccess(now)
            .lastAccess(now)
            .ipAddress(ipAddress)
            .userAgent(userAgent)
            .isActive(true)
            .build();

        intelligentSalesRoomDAO.createOrUpdateShareLinkAccess(access);
        LOGGER.info("User {} accessed sales room {} via share link", userEmail, salesRoom.getSalesRoomId());
        return salesRoom;
    }

    public List<IntelligentSalesRoomShareLinkAccess> getShareLinkAccesses(String salesRoomId) {
        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");
        return intelligentSalesRoomDAO.getShareLinkAccessesBySalesRoomId(salesRoomUuid);
    }

    public int getUniqueVisitorCount(String salesRoomId) {
        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");
        return intelligentSalesRoomDAO.getUniqueVisitorCount(salesRoomUuid);
    }

    public List<IntelligentSalesRoomAIGeneratedContent> getAIContentBySalesRoomId(String salesRoomId) {
        UUID salesRoomUuid = Validator.validateUUID(salesRoomId, "Sales Room ID is required.");
        return intelligentSalesRoomDAO.getAIContentBySalesRoomId(salesRoomUuid);
    }

    private void validateHtmlContent(String htmlContent) {
        String lowerContent = htmlContent.toLowerCase();
        if (
            lowerContent.contains("<script") ||
            lowerContent.contains("javascript:") ||
            lowerContent.contains("onload=") ||
            lowerContent.contains("onclick=")
        ) {
            throw new InvalidInputException("Widget content contains potentially dangerous HTML elements");
        }
    }

    private static void validateUrl(String url) {
        try {
            URI uri = new URI(url);
            if (!"http".equalsIgnoreCase(uri.getScheme()) && !"https".equalsIgnoreCase(uri.getScheme())) {
                throw new InvalidInputException("Website URL must use HTTP or HTTPS protocol");
            }
            if (uri.getHost() == null || uri.getHost().trim().isEmpty()) {
                throw new InvalidInputException("Website URL must have a valid host");
            }
        } catch (URISyntaxException e) {
            throw new InvalidInputException("Invalid website URL format: " + url);
        }
    }

    public void updateCustomerInformation(String salesRoomId, Optional<String> billingContactId, Optional<String> shippingContactId) {
        if (billingContactId.isEmpty() && shippingContactId.isEmpty()) {
            throw new InvalidInputException("At least one of billing or shipping contact ID must be provided.");
        }

        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);
        Order order = orderGetService.getOrderByOrderId(salesRoom.getOrderId());

        if (order.getStatus() == OrderStatus.EXECUTED) {
            throw new InvalidInputException("Cannot update an already executed order.");
        }

        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot update customer information for sales room in status: " + salesRoom.getStatus());
        }

        if (billingContactId.isPresent()) {
            throwIfAccountContactIdIsInvalid(billingContactId.get());
            order.setBillingContactId(billingContactId.get());
        }

        if (shippingContactId.isPresent()) {
            throwIfAccountContactIdIsInvalid(shippingContactId.get());
            order.setShippingContactId(shippingContactId.get());
        }

        orderService.updateOrderContacts(order, order.getBillingContactId(), order.getShippingContactId());
        orderPdfGenerationTrackerService.setShouldRegeneratePdf(salesRoom.getOrderId(), true);

        LOGGER.info("Updated customer information for sales room {} and triggered PDF regeneration", salesRoomId);
    }

    private void throwIfAccountContactIdIsInvalid(String contactId) {
        accountGetService.getContact(contactId);
    }

    void handleOrderApproved(String orderId) {
        Optional<IntelligentSalesRoom> salesRoom = intelligentSalesRoomDAO.getSalesRoomByOrderId(orderId);
        if (salesRoom.isPresent() && salesRoom.get().getStatus() == IntelligentSalesRoomStatus.DRAFT) {
            transitionToReadyToShare(salesRoom.get().getSalesRoomId());
            LOGGER.info("Transitioned sales room {} from DRAFT to READY_TO_SHARE due to order approval", salesRoom.get().getSalesRoomId());
        }
    }

    void handleOrderReverted(String orderId) {
        Optional<IntelligentSalesRoom> salesRoom = intelligentSalesRoomDAO.getSalesRoomByOrderId(orderId);
        if (
            (salesRoom.isPresent()) &&
            (salesRoom.get().getStatus() == IntelligentSalesRoomStatus.READY_TO_SHARE ||
                salesRoom.get().getStatus() == IntelligentSalesRoomStatus.ACTIVE)
        ) {
            transitionToDraft(salesRoom.get().getSalesRoomId());
            LOGGER.info("Transitioned sales room {} to DRAFT due to order being reverted back to DRAFT", salesRoom.get().getSalesRoomId());
        }
    }

    void handleOrderSubmitted(String orderId) {
        Optional<IntelligentSalesRoom> salesRoom = intelligentSalesRoomDAO.getSalesRoomByOrderId(orderId);
        if (salesRoom.isEmpty()) {
            return;
        }

        Order order = orderGetService.getOrderByOrderId(orderId);
        // Check if Order is auto approved
        if (order.getStatus() != OrderStatus.APPROVED) {
            return;
        }

        if (salesRoom.get().getStatus() == IntelligentSalesRoomStatus.DRAFT) {
            transitionToReadyToShare(salesRoom.get().getSalesRoomId());
            LOGGER.info(
                "Transitioned sales room {} from DRAFT to READY_TO_SHARE due to order being auto approved after submission",
                salesRoom.get().getSalesRoomId()
            );
        }
    }

    void handleESignatureVoided(String orderId) {
        Optional<IntelligentSalesRoom> salesRoomOptional = intelligentSalesRoomDAO.getSalesRoomByOrderId(orderId);
        if (salesRoomOptional.isEmpty()) {
            return;
        }

        IntelligentSalesRoom salesRoom = salesRoomOptional.get();
        if (salesRoom.getStatus() == IntelligentSalesRoomStatus.ACCEPTED) {
            transitionToReadyToShare(salesRoom.getId().toString());
            LOGGER.info("Transitioned sales room {} from ACCEPTED to READY_TO_SHARE due to eSignature voiding", salesRoom.getId());
        }
    }

    public String getTenantIdByShareLink(String shareLink) {
        IntelligentSalesRoom salesRoom = lookupSalesRoomByShareLink(shareLink);
        return salesRoom.getTenantId();
    }

    private String getSalesRoomIdByShareLink(String shareLink) {
        IntelligentSalesRoom salesRoom = lookupSalesRoomByShareLink(shareLink);
        return salesRoom.getSalesRoomId();
    }

    public String getOrderIdByShareLink(String shareLink) {
        IntelligentSalesRoom salesRoom = lookupSalesRoomByShareLink(shareLink);
        return salesRoom.getOrderId();
    }

    public String getAccountIdByShareLink(String shareLink) {
        IntelligentSalesRoom salesRoom = lookupSalesRoomByShareLink(shareLink);
        return salesRoom.getAccountId();
    }

    private IntelligentSalesRoom lookupSalesRoomByShareLink(String shareLink) {
        try {
            return SALES_ROOM_BY_SHARE_LINK_CACHE.get(shareLink, () -> lookupSalesRoomByShareLinkFromDB(shareLink));
        } catch (ExecutionException e) {
            LOGGER.info("Failed to lookup sales room by share link due to checked exception: {}", shareLink, e);
            throw new ServiceFailureException(e.getCause());
        } catch (UncheckedExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof IllegalArgumentException) {
                throw (IllegalArgumentException) cause;
            }

            if (cause instanceof ObjectNotFoundException) {
                throw (ObjectNotFoundException) cause;
            }
            LOGGER.info("Failed to lookup sales room by share link due to unchecked exception: {}", shareLink, e);
            throw new ServiceFailureException(e.getCause());
        }
    }

    private IntelligentSalesRoom lookupSalesRoomByShareLinkFromDB(String shareLink) {
        UUID shareLinkUuid = Validator.validateUUID(shareLink, "Share Link is required.");
        Optional<IntelligentSalesRoom> salesRoomOptional = intelligentSalesRoomDAO.getSalesRoomByShareLink(shareLinkUuid);
        if (salesRoomOptional.isEmpty()) {
            throw new ObjectNotFoundException(BillyObjectType.INTELLIGENT_SALES_ROOM_SHARE_LINK, shareLink);
        }
        return salesRoomOptional.get();
    }

    public InputStream downloadFileViaShareLink(String shareLink, String fileName) {
        String salesRoomId = getSalesRoomIdByShareLink(shareLink);
        return intelligentSalesRoomS3Service.downloadFile(salesRoomId, fileName);
    }

    private static String sanitizeUrlForDatabase(String input) {
        if (input == null) {
            return null;
        }
        // Remove null bytes
        return input.replace("\u0000", "").trim();
    }

    public Optional<String> getResellerAccountId(String salesRoomId) {
        Validator.validateStringNotBlank(salesRoomId, "Sales Room ID is required.");
        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);
        Order order = orderGetService.getOrderByOrderId(salesRoom.getOrderId());
        return order.getResellerAccountId();
    }

    public void sendEmailForEsign(String salesRoomId, EmailContact accountSignatory) {
        Validator.validateStringNotBlank(salesRoomId, "Sales Room ID is required.");
        IntelligentSalesRoom salesRoom = getSalesRoomByIdInternal(salesRoomId);

        if (!salesRoom.canEdit()) {
            throw new InvalidInputException("Cannot send eSign email for sales room in status: " + salesRoom.getStatus());
        }

        ElectronicSignatureProvider esignProvider = esignIntegrationService
            .getEsignProvider()
            .orElseThrow(() -> new InvalidInputException("No eSign integration found."));

        EsignRequest esign = esignService.getEsignDetailsForOrder(salesRoom.getOrderId());
        if (accountSignatory != null) {
            esign.setAccountSignatory(accountSignatory);
            esign = esignService.upsertEsignDetails(esign);
        }

        if (esignProvider == ElectronicSignatureProvider.PANDADOC) {
            esignService.requestEsignatureViaPandaDoc(esign, false);
            return;
        }

        if (esignProvider == ElectronicSignatureProvider.DOCUSIGN) {
            esignService.requestEsignatureViaDocusign(esign);
        }
    }

    void handleESignatureCompleted(String orderId) {
        Optional<IntelligentSalesRoom> salesRoomOptional = intelligentSalesRoomDAO.getSalesRoomByOrderId(orderId);
        if (salesRoomOptional.isEmpty()) {
            return;
        }

        acceptSalesRoomAndUpdateStatus(salesRoomOptional.get());
    }

    private IntelligentSalesRoomOverviewResponse addTenantAndEsignInfoToSalesRoomOverview(IntelligentSalesRoomOverviewResponse salesRoomOverview) {
        return TenantContextInjector.callInTenantContext(salesRoomOverview.getTenantId(), tenantIdProvider, () -> {
            TenantDetails tenantDetails = getTenantDetails(salesRoomOverview.getTenantId());
            Optional<ElectronicSignatureStatus> esignStatus = getEsignStatus(salesRoomOverview.getOrderId());
            return ImmutableIntelligentSalesRoomOverviewResponse.builder()
                .from(salesRoomOverview)
                .tenantName(Optional.ofNullable(tenantDetails.getName()))
                .tenantAddress(Optional.ofNullable(tenantDetails.getAddress()))
                .electronicSignatureStatus(esignStatus)
                .build();
        });
    }

    private TenantDetails getTenantDetails(String tenantId) {
        Tenant tenant = tenantService.getTenant(tenantId);
        return tenantDetailsMapper.tenantToTenantInfo(tenant);
    }

    private Optional<ElectronicSignatureStatus> getEsignStatus(String orderId) {
        if (esignIntegrationService.getEsignProvider().isEmpty()) {
            return Optional.empty();
        }

        try {
            ElectronicSignature electronicSignature = esignService.getElectronicSignatureByOrderId(orderId);
            return Optional.ofNullable(electronicSignature.getStatus());
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    public Map<String, CustomFieldValue> getCustomFields(String shareLink) {
        String salesRoomId = getSalesRoomIdByShareLink(shareLink);
        CustomField customField = customFieldProxy.getCustomFields(CustomFieldParentType.SALES_ROOM, salesRoomId);
        return customFieldAPIMapper.toCustomFieldsMap(customField);
    }

    public Map<String, CustomFieldValue> updateCustomFields(String shareLink, Map<String, CustomFieldValue> customFields) {
        String salesRoomId = getSalesRoomIdByShareLink(shareLink);
        CustomField customField = customFieldAPIMapper.toCustomField(customFields);
        CustomField response = customFieldProxy.updateCustomFields(CustomFieldParentType.SALES_ROOM, salesRoomId, customField, false);
        return customFieldAPIMapper.toCustomFieldsMap(response);
    }

    public IntelligentSalesRoomOrderMetadataResponse getSalesRoomOrderMetadata(String shareLink) {
        String orderId = getOrderIdByShareLink(shareLink);
        Optional<OrderStub> orderStub = orderGetService.getOrderStub(orderId);
        if (orderStub.isEmpty()) {
            throw new ServiceFailureException("Unable to load the order for sales room with share link: " + shareLink);
        }

        Optional<UserJson> orderOwner = orderStub.get().getOwnerId().flatMap(userService::getUserOptional).map(userMapper::userToJson);

        Optional<AccountContactJson> billingContact = orderStub
            .get()
            .getBillingContactId()
            .map(accountGetService::getContact)
            .map(accountMapper::accountContactToJson);

        Optional<AccountContactJson> shippingContact = orderStub
            .get()
            .getShippingContactId()
            .map(accountGetService::getContact)
            .map(accountMapper::accountContactToJson);

        return ImmutableIntelligentSalesRoomOrderMetadataResponse.builder()
            .owner(orderOwner)
            .billingContact(billingContact)
            .shippingContact(shippingContact)
            .build();
    }
}
