package com.subskribe.billy.salesroom.intelligent.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.notification.model.payload.ElectronicSignatureEvent;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.task.queue.event.OrderPreservingEventTaskFactory;
import com.subskribe.billy.shared.task.queue.model.ImmutableProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskResult;
import com.subskribe.billy.shared.task.queue.model.ProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import com.subskribe.billy.shared.task.queue.processor.TaskProcessor;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.Duration;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;

public class IntelligentSalesRoomTaskProcessor extends OrderPreservingEventTaskFactory implements TaskProcessor {

    private static final TaskModule TASK_MODULE = new TaskModule("intelligent-sales-room");

    private static final TaskType SCHEDULE_TASK_TYPE = new TaskType("intelligent-sales-room-status-update-based-on-order");

    private static final String EVENT_TYPE_TASK_METADATA_KEY = "eventType";

    private static final Set<EventType> SUPPORTED_EVENT_TYPES = Set.of(
        EventType.ORDER_SUBMITTED,
        EventType.ORDER_APPROVED,
        EventType.ORDER_REVERTED_TO_DRAFT,
        EventType.ESIGNATURE_VOIDED,
        EventType.ESIGNATURE_COMPLETED
    );

    private static final Duration TIMEOUT = Duration.ofMinutes(1);

    private final IntelligentSalesRoomService intelligentSalesRoomService;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    IntelligentSalesRoomTaskProcessor(IntelligentSalesRoomService intelligentSalesRoomService, TenantIdProvider tenantIdProvider) {
        super(tenantIdProvider);
        this.intelligentSalesRoomService = intelligentSalesRoomService;
        this.tenantIdProvider = tenantIdProvider;
    }

    @Override
    public QueuedTaskRequest createTaskForEvent(Event event) {
        if (!SUPPORTED_EVENT_TYPES.contains(event.getType())) {
            throw new InvalidInputException("Unsupported event type: " + event.getType());
        }

        if (event.getType() == EventType.ESIGNATURE_COMPLETED) {
            return createEsignatureCompletedTask(event);
        }

        OrderJson orderJson = getOrderFromEventPayload(event.getPayloadAsString());

        return ImmutableQueuedTaskRequest.builder()
            .module(TASK_MODULE)
            .type(SCHEDULE_TASK_TYPE)
            .taskData(orderJson.getId())
            .tenantId(tenantIdProvider.provideTenantIdString())
            .entityId(orderJson.getEntityId())
            .entityIds(event.getEntityIds())
            .taskMetadata(Map.of(EVENT_TYPE_TASK_METADATA_KEY, event.getType().name()))
            .build();
    }

    private QueuedTaskRequest createEsignatureCompletedTask(Event event) {
        ElectronicSignatureEvent electronicSignatureEvent = getElectronicSignatureEventFromPayload(event.getPayloadAsString());

        return ImmutableQueuedTaskRequest.builder()
            .module(TASK_MODULE)
            .type(SCHEDULE_TASK_TYPE)
            .taskData(electronicSignatureEvent.getOrderId())
            .tenantId(tenantIdProvider.provideTenantIdString())
            .entityIds(event.getEntityIds())
            .taskMetadata(Map.of(EVENT_TYPE_TASK_METADATA_KEY, event.getType().name()))
            .build();
    }

    @Override
    public Set<EventType> getEventTypes() {
        return SUPPORTED_EVENT_TYPES;
    }

    @Override
    public TaskResult process(QueuedTask task) {
        try {
            EventType eventType = Validator.enumFromString(task.getTaskMetadata().get(EVENT_TYPE_TASK_METADATA_KEY), EventType.class, "Event Type");
            return switch (eventType) {
                case ORDER_SUBMITTED -> {
                    intelligentSalesRoomService.handleOrderSubmitted(task.getTaskData());
                    yield ImmutableTaskResult.builder().successful(true).build();
                }
                case ORDER_APPROVED -> {
                    intelligentSalesRoomService.handleOrderApproved(task.getTaskData());
                    yield ImmutableTaskResult.builder().successful(true).build();
                }
                case ORDER_REVERTED_TO_DRAFT -> {
                    intelligentSalesRoomService.handleOrderReverted(task.getTaskData());
                    yield ImmutableTaskResult.builder().successful(true).build();
                }
                case ESIGNATURE_VOIDED -> {
                    intelligentSalesRoomService.handleESignatureVoided(task.getTaskData());
                    yield ImmutableTaskResult.builder().successful(true).build();
                }
                case ESIGNATURE_COMPLETED -> {
                    intelligentSalesRoomService.handleESignatureCompleted(task.getTaskData());
                    yield ImmutableTaskResult.builder().successful(true).build();
                }
                default -> ImmutableTaskResult.builder().successful(false).failureReason("Invalid event type").build();
            };
        } catch (Exception e) {
            return ImmutableTaskResult.builder().successful(false).failureReason(e.getMessage()).build();
        }
    }

    @Override
    public ProcessorConfiguration getConfiguration() {
        return ImmutableProcessorConfiguration.builder().taskModule(TASK_MODULE).taskTimeout(TIMEOUT).build();
    }

    private OrderJson getOrderFromEventPayload(String eventPayload) {
        try {
            return JacksonProvider.defaultMapper().readValue(eventPayload, OrderJson.class);
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Failed to parse order from event payload", e);
        }
    }

    private ElectronicSignatureEvent getElectronicSignatureEventFromPayload(String eventPayload) {
        try {
            return JacksonProvider.defaultMapper().readValue(eventPayload, ElectronicSignatureEvent.class);
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Failed to parse electronic signature event from payload", e);
        }
    }
}
