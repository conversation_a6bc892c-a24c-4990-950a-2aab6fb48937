package com.subskribe.billy.salesroom.intelligent.db;

import static com.subskribe.billy.jooq.default_schema.tables.IntelligentSalesRoom.INTELLIGENT_SALES_ROOM;
import static com.subskribe.billy.jooq.default_schema.tables.IntelligentSalesRoomActivityLog.INTELLIGENT_SALES_ROOM_ACTIVITY_LOG;
import static com.subskribe.billy.jooq.default_schema.tables.IntelligentSalesRoomAiGeneratedContent.INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT;
import static com.subskribe.billy.jooq.default_schema.tables.IntelligentSalesRoomEngagementSession.INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION;
import static com.subskribe.billy.jooq.default_schema.tables.IntelligentSalesRoomFile.INTELLIGENT_SALES_ROOM_FILE;
import static com.subskribe.billy.jooq.default_schema.tables.IntelligentSalesRoomShareLinkAccess.INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS;
import static com.subskribe.billy.jooq.default_schema.tables.IntelligentSalesRoomTheme.INTELLIGENT_SALES_ROOM_THEME;
import static com.subskribe.billy.jooq.default_schema.tables.IntelligentSalesRoomWidget.INTELLIGENT_SALES_ROOM_WIDGET;

import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.jooq.default_schema.tables.records.IntelligentSalesRoomRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoom;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomAIGeneratedContent;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomActivityLog;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomEngagementSession;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomFile;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomShareLinkAccess;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomTheme;
import com.subskribe.billy.salesroom.intelligent.model.ImmutableIntelligentSalesRoomWidget;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoom;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomAIGeneratedContent;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomActivityLog;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomConstants;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomEngagementSession;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomFile;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomFileCategory;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomShareLinkAccess;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomStatus;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomTheme;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWidget;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWidgetSortOrderUpdate;
import com.subskribe.billy.salesroom.intelligent.model.mapper.IntelligentSalesRoomMapper;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.shared.utility.UUIDConverter;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.Query;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class IntelligentSalesRoomDAO {

    private static final String UNIQUE_ORDER_ID_CONSTRAINT_NAME = "index_intelligent_sales_rooms_tenant_order";

    private final DSLContextProvider dslContextProvider;

    private final TenantIdProvider tenantIdProvider;

    private final IntelligentSalesRoomMapper mapper;

    @Inject
    public IntelligentSalesRoomDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        mapper = Mappers.getMapper(IntelligentSalesRoomMapper.class);
    }

    public IntelligentSalesRoom createSalesRoom(IntelligentSalesRoom salesRoom) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        IntelligentSalesRoom salesRoomToInsert = ImmutableIntelligentSalesRoom.builder()
            .from(salesRoom)
            .id(AutoGenerate.getNewUuid())
            .shareLink(AutoGenerate.getNewUuid())
            .tenantId(tenantId)
            .build();

        IntelligentSalesRoomRecord record = mapper.toRecord(salesRoomToInsert);

        var output = PostgresErrorHandler.withAnyConstraintAsConflict(
            () -> dslContext.insertInto(INTELLIGENT_SALES_ROOM).set(record).returning().fetchOne(),
            Map.of(UNIQUE_ORDER_ID_CONSTRAINT_NAME, String.format("Sales room already exists for order: %s", salesRoom.getOrderId()))
        );

        return mapper.fromRecord(output);
    }

    public List<IntelligentSalesRoom> getActiveSalesRoomsForOrder(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var records = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM)
            .where(INTELLIGENT_SALES_ROOM.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM.ORDER_ID.eq(orderId))
            .and(
                INTELLIGENT_SALES_ROOM.STATUS.in(
                    IntelligentSalesRoomStatus.DRAFT.name(),
                    IntelligentSalesRoomStatus.READY_TO_SHARE.name(),
                    IntelligentSalesRoomStatus.ACTIVE.name()
                )
            )
            .and(INTELLIGENT_SALES_ROOM.IS_DELETED.eq(false))
            .fetch();

        return mapper.fromRecords(records);
    }

    public IntelligentSalesRoomWidget createWidget(IntelligentSalesRoomWidget widget) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        IntelligentSalesRoomWidget widgetToInsert = ImmutableIntelligentSalesRoomWidget.builder()
            .from(widget)
            .id(AutoGenerate.getNewUuid())
            .tenantId(tenantId)
            .build();

        var record = mapper.toWidgetRecord(widgetToInsert);

        var output = dslContext.insertInto(INTELLIGENT_SALES_ROOM_WIDGET).set(record).returning().fetchOne();
        return mapper.fromWidgetRecord(output);
    }

    public Optional<IntelligentSalesRoom> getSalesRoomByOrderId(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM)
            .where(INTELLIGENT_SALES_ROOM.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM.ORDER_ID.eq(orderId))
            .and(INTELLIGENT_SALES_ROOM.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(mapper::fromRecord);
    }

    public Optional<IntelligentSalesRoom> getSalesRoomById(UUID salesRoomId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM)
            .where(INTELLIGENT_SALES_ROOM.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM.ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(mapper::fromRecord);
    }

    @AllowNonRlsDataAccess
    public Optional<IntelligentSalesRoom> getSalesRoomByShareLink(UUID shareLink) {
        var record = dslContextProvider
            .get()
            .selectFrom(INTELLIGENT_SALES_ROOM)
            .where(INTELLIGENT_SALES_ROOM.SHARE_LINK.eq(shareLink))
            .and(INTELLIGENT_SALES_ROOM.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(mapper::fromRecord);
    }

    public void setIsFirstAccessToFalse(UUID salesRoomId, String tenantId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var updatedRecord = dslContext
            .update(INTELLIGENT_SALES_ROOM)
            .set(INTELLIGENT_SALES_ROOM.IS_FIRST_ACCESS, false)
            .where(INTELLIGENT_SALES_ROOM.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM.ID.eq(salesRoomId))
            .returning()
            .fetchOne();

        if (updatedRecord == null) {
            throw new ServiceFailureException("Failed to update is_first_access for sales room with ID: " + salesRoomId);
        }
    }

    public List<IntelligentSalesRoomWidget> getWidgetsBySalesRoomId(UUID salesRoomId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var records = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_WIDGET)
            .where(INTELLIGENT_SALES_ROOM_WIDGET.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_WIDGET.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_WIDGET.IS_DELETED.isFalse())
            .orderBy(INTELLIGENT_SALES_ROOM_WIDGET.SORT_ORDER.asc())
            .fetch();

        return mapper.fromWidgetRecords(records);
    }

    public Optional<IntelligentSalesRoomWidget> getWidgetById(UUID widgetId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_WIDGET)
            .where(INTELLIGENT_SALES_ROOM_WIDGET.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_WIDGET.ID.eq(widgetId))
            .and(INTELLIGENT_SALES_ROOM_WIDGET.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(mapper::fromWidgetRecord);
    }

    public IntelligentSalesRoomWidget updateWidget(IntelligentSalesRoomWidget widget) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = mapper.toWidgetRecord(widget);
        record.setTenantId(tenantId);

        var updatedRecord = dslContext
            .update(INTELLIGENT_SALES_ROOM_WIDGET)
            .set(record)
            .where(INTELLIGENT_SALES_ROOM_WIDGET.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_WIDGET.ID.eq(widget.getId()))
            .returning()
            .fetchOne();

        if (updatedRecord == null) {
            throw new ServiceFailureException("Failed to update widget with ID: " + widget.getId());
        }

        return mapper.fromWidgetRecord(updatedRecord);
    }

    public void deleteWidget(UUID widgetId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        int updatedRows = dslContext
            .update(INTELLIGENT_SALES_ROOM_WIDGET)
            .set(INTELLIGENT_SALES_ROOM_WIDGET.IS_DELETED, true)
            .where(INTELLIGENT_SALES_ROOM_WIDGET.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_WIDGET.ID.eq(widgetId))
            .and(INTELLIGENT_SALES_ROOM_WIDGET.IS_DELETED.isFalse())
            .execute();

        if (updatedRows == 0) {
            throw new ServiceFailureException("Failed to delete widget with ID: " + widgetId);
        }
    }

    public int getUserGeneratedWidgetCount(UUID salesRoomId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        Integer response = dslContext
            .selectCount()
            .from(INTELLIGENT_SALES_ROOM_WIDGET)
            .where(INTELLIGENT_SALES_ROOM_WIDGET.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_WIDGET.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_WIDGET.IS_USER_GENERATED.isTrue())
            .and(INTELLIGENT_SALES_ROOM_WIDGET.IS_DELETED.isFalse())
            .fetchOne(0, Integer.class);

        if (response == null) {
            throw new ServiceFailureException("Failed to retrieve user-generated widget count for sales room: " + salesRoomId);
        }

        return response;
    }

    public IntelligentSalesRoomFile createFile(IntelligentSalesRoomFile file) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        IntelligentSalesRoomFile fileToInsert = ImmutableIntelligentSalesRoomFile.builder()
            .from(file)
            .id(AutoGenerate.getNewUuid())
            .tenantId(tenantId)
            .build();

        var record = mapper.toFileRecord(fileToInsert);

        var output = dslContext.insertInto(INTELLIGENT_SALES_ROOM_FILE).set(record).returning().fetchOne();
        return mapper.fromFileRecord(output);
    }

    public List<IntelligentSalesRoomFile> getFilesBySalesRoomId(UUID salesRoomId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var records = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_FILE)
            .where(INTELLIGENT_SALES_ROOM_FILE.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_FILE.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_FILE.IS_DELETED.isFalse())
            .orderBy(INTELLIGENT_SALES_ROOM_FILE.CREATED_ON.desc())
            .fetch();

        return mapper.fromFileRecords(records);
    }

    public Optional<IntelligentSalesRoomFile> getFileById(UUID fileId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_FILE)
            .where(INTELLIGENT_SALES_ROOM_FILE.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_FILE.ID.eq(fileId))
            .and(INTELLIGENT_SALES_ROOM_FILE.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(mapper::fromFileRecord);
    }

    public void deleteFile(UUID fileId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        int updatedRows = dslContext
            .update(INTELLIGENT_SALES_ROOM_FILE)
            .set(INTELLIGENT_SALES_ROOM_FILE.IS_DELETED, true)
            .where(INTELLIGENT_SALES_ROOM_FILE.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_FILE.ID.eq(fileId))
            .and(INTELLIGENT_SALES_ROOM_FILE.IS_DELETED.isFalse())
            .execute();

        if (updatedRows == 0) {
            throw new ServiceFailureException("Failed to delete file with ID: " + fileId);
        }
    }

    public int getFileCount(UUID salesRoomId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        Integer response = dslContext
            .selectCount()
            .from(INTELLIGENT_SALES_ROOM_FILE)
            .where(INTELLIGENT_SALES_ROOM_FILE.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_FILE.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_FILE.IS_DELETED.isFalse())
            .fetchOne(0, Integer.class);

        if (response == null) {
            throw new ServiceFailureException("Failed to retrieve file count for sales room: " + salesRoomId);
        }

        return response;
    }

    public List<IntelligentSalesRoomFile> getFilesByCategory(UUID salesRoomId, IntelligentSalesRoomFileCategory category) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var records = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_FILE)
            .where(INTELLIGENT_SALES_ROOM_FILE.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_FILE.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_FILE.FILE_CATEGORY.eq(category.name()))
            .and(INTELLIGENT_SALES_ROOM_FILE.IS_DELETED.isFalse())
            .orderBy(INTELLIGENT_SALES_ROOM_FILE.CREATED_ON.desc())
            .fetch();

        return mapper.fromFileRecords(records);
    }

    public IntelligentSalesRoomTheme createOrUpdateTheme(IntelligentSalesRoomTheme theme) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        UUID salesRoomUuid = UUIDConverter.toUUID(theme.getSalesRoomId()).orElseThrow(() ->
            new ServiceFailureException("Invalid sales room ID: " + theme.getSalesRoomId())
        );

        Optional<IntelligentSalesRoomTheme> existingTheme = getThemeBySalesRoomId(salesRoomUuid);

        if (existingTheme.isPresent()) {
            return updateTheme(theme, existingTheme.get(), tenantId, dslContext);
        }

        IntelligentSalesRoomTheme themeToInsert = ImmutableIntelligentSalesRoomTheme.builder()
            .from(theme)
            .id(AutoGenerate.getNewUuid())
            .tenantId(tenantId)
            .build();

        var record = mapper.toThemeRecord(themeToInsert);
        var output = dslContext.insertInto(INTELLIGENT_SALES_ROOM_THEME).set(record).returning().fetchOne();
        return mapper.fromThemeRecord(output);
    }

    private IntelligentSalesRoomTheme updateTheme(
        IntelligentSalesRoomTheme theme,
        IntelligentSalesRoomTheme existingTheme,
        String tenantId,
        DSLContext dslContext
    ) {
        IntelligentSalesRoomTheme themeToUpdate = ImmutableIntelligentSalesRoomTheme.builder()
            .from(theme)
            .id(existingTheme.getId())
            .tenantId(tenantId)
            .build();

        var record = mapper.toThemeRecord(themeToUpdate);

        var updatedRecord = dslContext
            .update(INTELLIGENT_SALES_ROOM_THEME)
            .set(record)
            .where(INTELLIGENT_SALES_ROOM_THEME.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_THEME.SALES_ROOM_ID.eq(UUID.fromString(theme.getSalesRoomId())))
            .returning()
            .fetchOne();

        if (updatedRecord == null) {
            throw new ServiceFailureException("Failed to update theme for sales room: " + theme.getSalesRoomId());
        }

        return mapper.fromThemeRecord(updatedRecord);
    }

    public Optional<IntelligentSalesRoomTheme> getThemeBySalesRoomId(UUID salesRoomId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_THEME)
            .where(INTELLIGENT_SALES_ROOM_THEME.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_THEME.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_THEME.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(mapper::fromThemeRecord);
    }

    public void updateStatus(UUID salesRoomId, IntelligentSalesRoomStatus status) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        int updatedRows = dslContext
            .update(INTELLIGENT_SALES_ROOM)
            .set(INTELLIGENT_SALES_ROOM.STATUS, status.name())
            .where(INTELLIGENT_SALES_ROOM.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM.ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM.IS_DELETED.isFalse())
            .execute();

        if (updatedRows == 0) {
            throw new ServiceFailureException("Failed to update status for sales room with ID: " + salesRoomId);
        }
    }

    public void updateStatusAndSharedOn(UUID salesRoomId, IntelligentSalesRoomStatus status) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        int updatedRows = dslContext
            .update(INTELLIGENT_SALES_ROOM)
            .set(INTELLIGENT_SALES_ROOM.STATUS, status.name())
            .set(INTELLIGENT_SALES_ROOM.SHARED_ON, LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC))
            .where(INTELLIGENT_SALES_ROOM.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM.ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM.IS_DELETED.isFalse())
            .execute();

        if (updatedRows == 0) {
            throw new ServiceFailureException("Failed to update status and shared_on for sales room with ID: " + salesRoomId);
        }
    }

    public void updateStatusAndAcceptedOn(UUID salesRoomId, IntelligentSalesRoomStatus status) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        int updatedRows = dslContext
            .update(INTELLIGENT_SALES_ROOM)
            .set(INTELLIGENT_SALES_ROOM.STATUS, status.name())
            .set(INTELLIGENT_SALES_ROOM.ACCEPTED_ON, LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC))
            .where(INTELLIGENT_SALES_ROOM.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM.ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM.IS_DELETED.isFalse())
            .execute();

        if (updatedRows == 0) {
            throw new ServiceFailureException("Failed to update status and accepted_on for sales room with ID: " + salesRoomId);
        }
    }

    public IntelligentSalesRoomAIGeneratedContent addAIContent(IntelligentSalesRoomAIGeneratedContent content) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        IntelligentSalesRoomAIGeneratedContent contentToInsert = ImmutableIntelligentSalesRoomAIGeneratedContent.builder()
            .from(content)
            .id(AutoGenerate.getNewUuid())
            .tenantId(tenantId)
            .build();

        var record = mapper.toAiGeneratedContentRecord(contentToInsert);

        var output = dslContext.insertInto(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT).set(record).returning().fetchOne();
        return mapper.fromAiGeneratedContentRecord(output);
    }

    public Optional<IntelligentSalesRoomAIGeneratedContent> getAIContentById(UUID contentId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT)
            .where(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT.ID.eq(contentId))
            .and(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(mapper::fromAiGeneratedContentRecord);
    }

    public IntelligentSalesRoomAIGeneratedContent updateAIContent(IntelligentSalesRoomAIGeneratedContent content) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = mapper.toAiGeneratedContentRecord(content);
        record.setTenantId(tenantId);

        var updatedRecord = dslContext
            .update(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT)
            .set(record)
            .where(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT.ID.eq(content.getId()))
            .returning()
            .fetchOne();

        if (updatedRecord == null) {
            throw new ServiceFailureException("Failed to update AI content with ID: " + content.getId());
        }

        return mapper.fromAiGeneratedContentRecord(updatedRecord);
    }

    public IntelligentSalesRoomEngagementSession createEngagementSession(IntelligentSalesRoomEngagementSession session) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        IntelligentSalesRoomEngagementSession sessionToInsert = ImmutableIntelligentSalesRoomEngagementSession.builder()
            .from(session)
            .id(AutoGenerate.getNewUuid())
            .tenantId(tenantId)
            .build();

        var record = mapper.toEngagementSessionRecord(sessionToInsert);

        var output = dslContext.insertInto(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION).set(record).returning().fetchOne();
        return mapper.fromEngagementSessionRecord(output);
    }

    public Optional<IntelligentSalesRoomEngagementSession> getActiveSessionByUserAndSalesRoom(UUID salesRoomId, String userEmail) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION)
            .where(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.USER_EMAIL.eq(userEmail))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.IS_ACTIVE.isTrue())
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(mapper::fromEngagementSessionRecord);
    }

    public Optional<IntelligentSalesRoomEngagementSession> getSessionById(UUID sessionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION)
            .where(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SESSION_ID.eq(sessionId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(mapper::fromEngagementSessionRecord);
    }

    public void updateEngagementSession(IntelligentSalesRoomEngagementSession session) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = mapper.toEngagementSessionRecord(session);
        record.setTenantId(tenantId);

        var updatedRecord = dslContext
            .update(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION)
            .set(record)
            .where(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.ID.eq(session.getId()))
            .returning()
            .fetchOne();

        if (updatedRecord == null) {
            throw new ServiceFailureException("Failed to update engagement session with ID: " + session.getId());
        }
    }

    public List<IntelligentSalesRoomEngagementSession> getSessionsBySalesRoomId(UUID salesRoomId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var records = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION)
            .where(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.IS_DELETED.isFalse())
            .orderBy(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SESSION_START.desc())
            .fetch();

        return mapper.fromEngagementSessionRecords(records);
    }

    public List<IntelligentSalesRoomEngagementSession> getSessionsBySalesRoomId(UUID salesRoomId, Instant fromDate, Instant toDate) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var query = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION)
            .where(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.IS_DELETED.isFalse());

        if (fromDate != null) {
            query = query.and(
                INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SESSION_START.greaterOrEqual(LocalDateTime.ofInstant(fromDate, ZoneOffset.UTC))
            );
        }

        if (toDate != null) {
            query = query.and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SESSION_START.lessOrEqual(LocalDateTime.ofInstant(toDate, ZoneOffset.UTC)));
        }

        var records = query.orderBy(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SESSION_START.desc()).fetch();
        return mapper.fromEngagementSessionRecords(records);
    }

    public void createActivityLog(IntelligentSalesRoomActivityLog activityLog) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        IntelligentSalesRoomActivityLog logToInsert = ImmutableIntelligentSalesRoomActivityLog.builder()
            .from(activityLog)
            .id(AutoGenerate.getNewUuid())
            .tenantId(tenantId)
            .build();

        var record = mapper.toActivityLogRecord(logToInsert);

        dslContext.insertInto(INTELLIGENT_SALES_ROOM_ACTIVITY_LOG).set(record).execute();
    }

    public List<IntelligentSalesRoomActivityLog> getActivityLogsBySalesRoomId(UUID salesRoomId, Instant fromDate, Instant toDate) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var query = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_ACTIVITY_LOG)
            .where(INTELLIGENT_SALES_ROOM_ACTIVITY_LOG.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_ACTIVITY_LOG.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_ACTIVITY_LOG.IS_DELETED.isFalse());

        if (fromDate != null) {
            query = query.and(INTELLIGENT_SALES_ROOM_ACTIVITY_LOG.TIMESTAMP.greaterOrEqual(LocalDateTime.ofInstant(fromDate, ZoneOffset.UTC)));
        }

        if (toDate != null) {
            query = query.and(INTELLIGENT_SALES_ROOM_ACTIVITY_LOG.TIMESTAMP.lessOrEqual(LocalDateTime.ofInstant(toDate, ZoneOffset.UTC)));
        }

        var records = query.orderBy(INTELLIGENT_SALES_ROOM_ACTIVITY_LOG.TIMESTAMP.desc()).fetch();

        return mapper.fromActivityLogRecords(records);
    }

    public void expireInactiveSessions(UUID salesRoomId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        Instant cutoffTime = Instant.now().minusSeconds(IntelligentSalesRoomConstants.SESSION_TIMEOUT_MINUTES * 60L);
        Instant now = Instant.now();

        var sessionRecordsThatNeedToBeExpired = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION)
            .where(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.IS_ACTIVE.isTrue())
            .and(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SESSION_START.lessOrEqual(LocalDateTime.ofInstant(cutoffTime, ZoneOffset.UTC)))
            .fetch();

        if (sessionRecordsThatNeedToBeExpired.isEmpty()) {
            return;
        }

        var queries = sessionRecordsThatNeedToBeExpired
            .stream()
            .map(record -> {
                IntelligentSalesRoomEngagementSession session = mapper.fromEngagementSessionRecord(record);
                long durationSeconds = Duration.between(session.getSessionStart(), now).getSeconds();

                return dslContext
                    .update(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION)
                    .set(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.IS_ACTIVE, false)
                    .set(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.SESSION_END, LocalDateTime.ofInstant(now, ZoneOffset.UTC))
                    .set(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.DURATION_SECONDS, durationSeconds)
                    .where(INTELLIGENT_SALES_ROOM_ENGAGEMENT_SESSION.ID.eq(session.getId()));
            })
            .toArray(Query[]::new);

        dslContext.batch(queries).execute();
    }

    public void createOrUpdateShareLinkAccess(IntelligentSalesRoomShareLinkAccess access) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        UUID salesRoomUuid = UUIDConverter.toUUID(access.getSalesRoomId()).orElseThrow(() ->
            new ServiceFailureException("Invalid sales room ID: " + access.getSalesRoomId())
        );

        Optional<IntelligentSalesRoomShareLinkAccess> existingAccess = getShareLinkAccessByUserAndSalesRoom(
            salesRoomUuid,
            access.getUserName(),
            access.getUserEmail()
        );

        if (existingAccess.isPresent()) {
            updateShareLinkAccess(access, existingAccess.get(), tenantId, dslContext);
            return;
        }

        IntelligentSalesRoomShareLinkAccess accessToInsert = ImmutableIntelligentSalesRoomShareLinkAccess.builder()
            .from(access)
            .id(AutoGenerate.getNewUuid())
            .firstAccess(access.getFirstAccess())
            .lastAccess(access.getLastAccess())
            .isActive(true)
            .build();

        var record = mapper.toShareLinkAccessRecord(accessToInsert);
        record.setTenantId(tenantId);

        dslContext.insertInto(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS).set(record).execute();
    }

    private void updateShareLinkAccess(
        IntelligentSalesRoomShareLinkAccess access,
        IntelligentSalesRoomShareLinkAccess existingAccess,
        String tenantId,
        DSLContext dslContext
    ) {
        IntelligentSalesRoomShareLinkAccess accessToUpdate = ImmutableIntelligentSalesRoomShareLinkAccess.builder()
            .from(existingAccess)
            .lastAccess(access.getLastAccess())
            .ipAddress(access.getIpAddress())
            .userAgent(access.getUserAgent())
            .isActive(true)
            .build();

        var record = mapper.toShareLinkAccessRecord(accessToUpdate);
        record.setTenantId(tenantId);

        var updatedRecord = dslContext
            .update(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS)
            .set(record)
            .where(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.ID.eq(existingAccess.getId()))
            .returning()
            .fetchOne();

        if (updatedRecord == null) {
            throw new ServiceFailureException("Failed to update share link access for user: " + access.getUserEmail());
        }
    }

    private Optional<IntelligentSalesRoomShareLinkAccess> getShareLinkAccessByUserAndSalesRoom(UUID salesRoomId, String userName, String userEmail) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS)
            .where(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.USER_NAME.eq(userName))
            .and(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.USER_EMAIL.eq(userEmail))
            .and(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.IS_DELETED.isFalse())
            .fetchOne();

        return Optional.ofNullable(record).map(mapper::fromShareLinkAccessRecord);
    }

    public List<IntelligentSalesRoomShareLinkAccess> getShareLinkAccessesBySalesRoomId(UUID salesRoomId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var records = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS)
            .where(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.IS_DELETED.isFalse())
            .orderBy(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.FIRST_ACCESS.desc())
            .fetch();

        return mapper.fromShareLinkAccessRecords(records);
    }

    public Integer getUniqueVisitorCount(UUID salesRoomId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        Integer response = dslContext
            .selectCount()
            .from(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS)
            .where(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.SALES_ROOM_ID.eq(salesRoomId))
            .and(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.IS_ACTIVE.isTrue())
            .and(INTELLIGENT_SALES_ROOM_SHARE_LINK_ACCESS.IS_DELETED.isFalse())
            .fetchOne(0, Integer.class);

        if (response == null) {
            throw new ServiceFailureException("Failed to retrieve visitor count for sales room: " + salesRoomId);
        }

        return response;
    }

    public void updateWidgetSortOrders(String salesRoomId, List<IntelligentSalesRoomWidgetSortOrderUpdate> sortOrderUpdates) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        tenantDslContext.transaction(configuration -> updateWidgetSortOrdersTransaction(configuration, salesRoomId, sortOrderUpdates));
    }

    private void updateWidgetSortOrdersTransaction(
        Configuration configuration,
        String salesRoomId,
        List<IntelligentSalesRoomWidgetSortOrderUpdate> sortOrderUpdates
    ) {
        DSLContext dslContext = DSL.using(configuration);
        String tenantId = tenantIdProvider.provideTenantIdString();

        for (IntelligentSalesRoomWidgetSortOrderUpdate update : sortOrderUpdates) {
            int updatedRows = dslContext
                .update(INTELLIGENT_SALES_ROOM_WIDGET)
                .set(INTELLIGENT_SALES_ROOM_WIDGET.SORT_ORDER, update.getNewSortOrder())
                .where(INTELLIGENT_SALES_ROOM_WIDGET.TENANT_ID.eq(tenantId))
                .and(INTELLIGENT_SALES_ROOM_WIDGET.SALES_ROOM_ID.eq(UUID.fromString(salesRoomId)))
                .and(INTELLIGENT_SALES_ROOM_WIDGET.ID.eq(update.getWidgetId()))
                .and(INTELLIGENT_SALES_ROOM_WIDGET.IS_DELETED.isFalse())
                .execute();

            if (updatedRows == 0) {
                throw new ServiceFailureException("Failed to update sort order for widget: " + update.getWidgetId());
            }
        }
    }

    public List<IntelligentSalesRoomAIGeneratedContent> getAIContentBySalesRoomId(UUID salesRoomUuid) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var records = dslContext
            .selectFrom(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT)
            .where(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT.TENANT_ID.eq(tenantId))
            .and(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT.SALES_ROOM_ID.eq(salesRoomUuid))
            .and(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT.IS_DELETED.isFalse())
            .orderBy(INTELLIGENT_SALES_ROOM_AI_GENERATED_CONTENT.GENERATED_ON.desc())
            .fetch();

        return mapper.fromAiGeneratedContentRecords(records);
    }
}
