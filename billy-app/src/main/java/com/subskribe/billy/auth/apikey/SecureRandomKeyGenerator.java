package com.subskribe.billy.auth.apikey;

import java.security.SecureRandom;
import java.util.Base64;

public class SecureRandomKeyGenerator {

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    private static final Base64.Encoder BASE_64_ENCODER = Base64.getUrlEncoder();

    private static final int KEY_LENGTH_IN_BYTES = 32;

    public String generateKey() {
        byte[] randomBytes = new byte[KEY_LENGTH_IN_BYTES];
        SECURE_RANDOM.nextBytes(randomBytes);
        return BASE_64_ENCODER.encodeToString(randomBytes);
    }
}
