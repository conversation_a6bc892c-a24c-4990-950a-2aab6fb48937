package com.subskribe.billy.auth.db;

import static com.subskribe.billy.jooq.default_schema.tables.AuthTenantCognito.AUTH_TENANT_COGNITO;

import com.subskribe.billy.auth.mapper.AuthTenantCognitoMapper;
import com.subskribe.billy.auth.model.AuthTenantCognito;
import com.subskribe.billy.jooq.default_schema.tables.records.AuthTenantCognitoRecord;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import java.util.Optional;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class AuthTenantCognitoDAO {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthTenantCognitoDAO.class);

    private static final AuthTenantCognitoMapper MAPPER = Mappers.getMapper(AuthTenantCognitoMapper.class);

    public AuthTenantCognito addTenantCognitoInformation(DSLContext context, AuthTenantCognito authTenantCognito) {
        try {
            AuthTenantCognitoRecord record = MAPPER.toRecord(authTenantCognito);
            record.reset(AUTH_TENANT_COGNITO.ID);
            AuthTenantCognitoRecord savedRecord = context.insertInto(AUTH_TENANT_COGNITO).set(record).returning().fetchOne();
            return MAPPER.fromRecord(savedRecord);
        } catch (Exception e) {
            LOGGER.error("Unable to add tenant auth (cognito) information for tenantId: {}", authTenantCognito.getTenantId(), e);
            throw e;
        }
    }

    public Optional<AuthTenantCognito> getTenantCognitoInformationByTenantId(DSLContext context, String tenantId) {
        try {
            AuthTenantCognitoRecord record = context
                .select()
                .from(AUTH_TENANT_COGNITO)
                .where(AUTH_TENANT_COGNITO.TENANT_ID.eq(tenantId))
                .and(AUTH_TENANT_COGNITO.IS_DELETED.isFalse())
                .fetchOneInto(AuthTenantCognitoRecord.class);
            return Optional.ofNullable(record).map(MAPPER::fromRecord);
        } catch (Exception e) {
            LOGGER.error("Unable to get tenant auth (cognito) information for tenantId: {}", tenantId, e);
            throw e;
        }
    }

    public Optional<AuthTenantCognito> getTenantCognitoInformationByClientId(DSLContext context, String clientId) {
        try {
            AuthTenantCognitoRecord record = context
                .select()
                .from(AUTH_TENANT_COGNITO)
                .where(AUTH_TENANT_COGNITO.CLIENT_ID.eq(clientId))
                .and(AUTH_TENANT_COGNITO.IS_DELETED.isFalse())
                .fetchOneInto(AuthTenantCognitoRecord.class);
            return Optional.ofNullable(record).map(MAPPER::fromRecord);
        } catch (Exception e) {
            LOGGER.error("Unable to get tenant auth (cognito) information for clientId: {}", clientId, e);
            throw e;
        }
    }

    public void deactivateTenantRecord(DSLContext context, String tenantId) {
        updateIsDeleted(context, tenantId, true);
    }

    public void reactivateTenantRecord(DSLContext context, String tenantId) {
        updateIsDeleted(context, tenantId, false);
    }

    private void updateIsDeleted(DSLContext context, String tenantId, boolean isDeleted) {
        context
            .update(AUTH_TENANT_COGNITO)
            .set(AUTH_TENANT_COGNITO.IS_DELETED, isDeleted)
            .where(AUTH_TENANT_COGNITO.TENANT_ID.eq(tenantId))
            .execute();
    }

    public void setHasSsoForTenant(DSLContext context, String tenantId) {
        context.update(AUTH_TENANT_COGNITO).set(AUTH_TENANT_COGNITO.HAS_SSO, true).where(AUTH_TENANT_COGNITO.TENANT_ID.eq(tenantId)).execute();
    }
}
