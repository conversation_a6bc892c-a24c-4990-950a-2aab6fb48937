package com.subskribe.billy.di.hk2;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.ai.service.PromptService;
import com.subskribe.ai.service.bedrock.BedrockAgentSessionFactory;
import com.subskribe.ai.service.bedrock.BedrockClientProvider;
import com.subskribe.ai.service.bedrock.BedrockGuidedSelling;
import com.subskribe.ai.service.bedrock.BedrockSummarizationService;
import com.subskribe.ai.service.bedrock.MessageStorage;
import com.subskribe.ai.service.db.PromptDAO;
import com.subskribe.ai.service.functions.FunctionCallService;
import com.subskribe.ai.service.openai.OpenAIAgentSessionFactory;
import com.subskribe.ai.service.openai.OpenAIAssistantsClientProvider;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.bulk.service.AccountBulkOperationService;
import com.subskribe.billy.account.db.AccountAddressDAO;
import com.subskribe.billy.account.db.AccountContactDAO;
import com.subskribe.billy.account.db.AccountDAO;
import com.subskribe.billy.account.db.AccountPaymentMethodDAO;
import com.subskribe.billy.account.services.AccountCacheInvalidator;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountIdGenerator;
import com.subskribe.billy.account.services.AccountPaymentMethodGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodService;
import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.account.services.ContactIdGenerator;
import com.subskribe.billy.accounting.db.AccountingPeriodDAO;
import com.subskribe.billy.accounting.db.JournalEntryDAO;
import com.subskribe.billy.accounting.db.JournalEntryIdDAO;
import com.subskribe.billy.accounting.db.LedgerAccountDAO;
import com.subskribe.billy.accounting.journalentry.AccountingEventLogWriter;
import com.subskribe.billy.accounting.journalentry.AccountingLogWritingEventConsumer;
import com.subskribe.billy.accounting.journalentry.CreditMemoFxJournalProcessor;
import com.subskribe.billy.accounting.journalentry.InvoicePostedFxJournalProcessor;
import com.subskribe.billy.accounting.journalentry.InvoicePostedJournalCreator;
import com.subskribe.billy.accounting.journalentry.InvoiceVoidedJournalCreator;
import com.subskribe.billy.accounting.journalentry.PaymentProcessedJournalCreator;
import com.subskribe.billy.accounting.journalentry.PaymentVoidedJournalCreator;
import com.subskribe.billy.accounting.journalentry.RealizedGainLossJournalCreator;
import com.subskribe.billy.accounting.journalentry.RevenueRecognizedFxJournalProcessor;
import com.subskribe.billy.accounting.journalentry.RevenueRecognizedJournalCreator;
import com.subskribe.billy.accounting.journalentry.SettlementFxJournalProcessor;
import com.subskribe.billy.accounting.journalentry.StandAloneCreditMemoJournalProcessor;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.accounting.services.AccountingPeriodCalculationService;
import com.subskribe.billy.accounting.services.AccountingPeriodIdGenerator;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.accounting.services.AccountingPeriodStatusUpdateService;
import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.accounting.services.JournalEntryIdGenerator;
import com.subskribe.billy.accounting.services.JournalEntryService;
import com.subskribe.billy.accounting.services.JournalLineIdGenerator;
import com.subskribe.billy.accounting.services.LedgerAccountIdGenerator;
import com.subskribe.billy.accounting.task.JournalQueuedTaskProcessor;
import com.subskribe.billy.admin.AdminOperationsService;
import com.subskribe.billy.alias.db.SubscriptionChargeAliasDAO;
import com.subskribe.billy.alias.service.AliasService;
import com.subskribe.billy.anrok.service.AnrokIntegrationGetService;
import com.subskribe.billy.anrok.service.AnrokIntegrationService;
import com.subskribe.billy.anrok.service.AnrokService;
import com.subskribe.billy.approvalflow.db.ApprovalFlowDAO;
import com.subskribe.billy.approvalflow.service.ApprovalFlowIdGenerator;
import com.subskribe.billy.approvalflow.service.ApprovalFlowMembershipService;
import com.subskribe.billy.approvalflow.service.ApprovalFlowService;
import com.subskribe.billy.approvalflowhierarchy.db.ApprovalFlowHierarchyDao;
import com.subskribe.billy.approvalflowhierarchy.service.ApprovalFlowHierarchyService;
import com.subskribe.billy.approvalflowhierarchy.service.ApprovalMatrixCsvService;
import com.subskribe.billy.approvalflowhierarchy.service.ApprovalRoleIdGenerator;
import com.subskribe.billy.approvalflowhierarchy.service.ApprovalSegmentIdGenerator;
import com.subskribe.billy.approvalflowinstance.db.ApprovalFlowEmailDAO;
import com.subskribe.billy.approvalflowinstance.db.ApprovalFlowInstanceDAO;
import com.subskribe.billy.approvalflowinstance.db.ApprovalFlowPendingUserActionDAO;
import com.subskribe.billy.approvalflowinstance.event.ApprovalEventConsumer;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowEmailService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceGetService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceGroupIdGenerator;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowPendingUserActionService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowStateUpdaterService;
import com.subskribe.billy.approvalflowinstance.service.OrderApprovalFlowEvaluator;
import com.subskribe.billy.attachments.db.AttachmentDAO;
import com.subskribe.billy.attachments.service.AttachmentsService;
import com.subskribe.billy.auditlog.AuditlogPartitionManager;
import com.subskribe.billy.auth.apikey.ApiKeyGenerator;
import com.subskribe.billy.auth.apikey.ApiKeyRsaKeyPairProvider;
import com.subskribe.billy.auth.apikey.ApiKeyService;
import com.subskribe.billy.auth.apikey.ApiKeyValidator;
import com.subskribe.billy.auth.apikey.SecureRandomKeyGenerator;
import com.subskribe.billy.auth.apikey.db.ApiKeyDAO;
import com.subskribe.billy.auth.authorization.BillyInterceptionService;
import com.subskribe.billy.auth.db.AuthTenantCognitoDAO;
import com.subskribe.billy.auth.jwt.JWTTokenValidator;
import com.subskribe.billy.auth.services.EmailLinkLoginService;
import com.subskribe.billy.auth.services.TenantCognitoService;
import com.subskribe.billy.auth.services.TenantRlsEncryptionService;
import com.subskribe.billy.avalara.db.AvalaraDAO;
import com.subskribe.billy.avalara.service.AvalaraIntegrationGetService;
import com.subskribe.billy.avalara.service.AvalaraIntegrationService;
import com.subskribe.billy.avalara.service.AvalaraService;
import com.subskribe.billy.aws.appconfig.AwsAppConfigurationService;
import com.subskribe.billy.aws.cognito.CognitoService;
import com.subskribe.billy.aws.dynamodb.DynamoDbClientProvider;
import com.subskribe.billy.aws.dynamodb.DynamoDbHelper;
import com.subskribe.billy.aws.es.ElasticSearchAdminService;
import com.subskribe.billy.aws.es.ElasticSearchClientProvider;
import com.subskribe.billy.aws.es.ElasticSearchService;
import com.subskribe.billy.aws.kinesis.KinesisConsumerStarter;
import com.subskribe.billy.aws.kinesis.KinesisWriterProvider;
import com.subskribe.billy.aws.lambda.LambdaClientProvider;
import com.subskribe.billy.aws.lambda.LambdaService;
import com.subskribe.billy.aws.s3.S3ClientProvider;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.aws.ses.SesClientProvider;
import com.subskribe.billy.aws.sns.SnsClientProvider;
import com.subskribe.billy.aws.sqs.SqsClientProvider;
import com.subskribe.billy.banktransactions.db.BankTransactionDAO;
import com.subskribe.billy.banktransactions.services.BankTransactionIdGenerator;
import com.subskribe.billy.banktransactions.services.BankTransactionsService;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.MemcachedClientProvider;
import com.subskribe.billy.catalogrelationship.db.CatalogRelationshipDAO;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipGetService;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipIdGenerator;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipService;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipValidator;
import com.subskribe.billy.clm.db.CLMDAO;
import com.subskribe.billy.clm.service.CLMService;
import com.subskribe.billy.compositeorder.db.CompositeOrderDAO;
import com.subskribe.billy.compositeorder.model.CancelAndRestructureOrderDataAggregator;
import com.subskribe.billy.compositeorder.service.CompositeOrderDocumentRenderingService;
import com.subskribe.billy.compositeorder.service.CompositeOrderDocumentService;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.compositeorder.service.CompositeOrderIdGenerator;
import com.subskribe.billy.compositeorder.service.CompositeOrderService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.CrmIntegrationService;
import com.subskribe.billy.crm.db.CrmFieldMappingDAO;
import com.subskribe.billy.crm.db.CrmFieldMappingImportDAO;
import com.subskribe.billy.crm.service.CrmFieldMappingImportService;
import com.subskribe.billy.crm.service.CrmFieldMappingService;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.customfield.CustomFieldIdGenerator;
import com.subskribe.billy.customfield.CustomFieldProxy;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.db.CustomFieldDAO;
import com.subskribe.billy.customization.db.CustomizationDefinitionDAO;
import com.subskribe.billy.customization.service.CustomizationService;
import com.subskribe.billy.customization.service.ui.UIConfigurationService;
import com.subskribe.billy.dataimport.db.FlatfileDAO;
import com.subskribe.billy.dataimport.db.ImportDAO;
import com.subskribe.billy.dataimport.export.AccountDomainExporter;
import com.subskribe.billy.dataimport.export.CatalogDomainExporter;
import com.subskribe.billy.dataimport.export.OrderDomainExporter;
import com.subskribe.billy.dataimport.processors.AccountDomainImportProcessor;
import com.subskribe.billy.dataimport.processors.CatalogDomainImportProcessor;
import com.subskribe.billy.dataimport.processors.OrderDomainImportProcessor;
import com.subskribe.billy.dataimport.schema.account.AccountDomainCreateSchema;
import com.subskribe.billy.dataimport.schema.account.AccountDomainUpdateSchema;
import com.subskribe.billy.dataimport.schema.catalog.CatalogDomainSchema;
import com.subskribe.billy.dataimport.schema.order.OrderDomainCreateSchema;
import com.subskribe.billy.dataimport.service.DataImportService;
import com.subskribe.billy.dataimport.service.FlatfileService;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.di.hk2.providers.EntityPolicyProvider;
import com.subskribe.billy.di.hk2.providers.auditlog.DbSessionParams;
import com.subskribe.billy.di.hk2.providers.auditlog.DbSessionParamsListenerProvider;
import com.subskribe.billy.discount.db.DiscountDAO;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.dlq.db.DlqDAO;
import com.subskribe.billy.dlq.service.DLQMessageHandler;
import com.subskribe.billy.dlq.service.DLQService;
import com.subskribe.billy.document.db.DocumentDAO;
import com.subskribe.billy.document.db.DocumentLinkDAO;
import com.subskribe.billy.document.service.AdobeConversionService;
import com.subskribe.billy.document.service.DocumentLinkService;
import com.subskribe.billy.document.service.DocumentService;
import com.subskribe.billy.document.service.DocxConversionService;
import com.subskribe.billy.document.service.TemplateLambdaInvoker;
import com.subskribe.billy.docusign.db.DocuSignDAO;
import com.subskribe.billy.docusign.service.DocuSignAuthService;
import com.subskribe.billy.docusign.service.DocuSignService;
import com.subskribe.billy.docusign.service.DocusignWebhookProcessor;
import com.subskribe.billy.email.db.EmailContactListDAO;
import com.subskribe.billy.email.db.EmailDAO;
import com.subskribe.billy.email.db.EmailSettingDAO;
import com.subskribe.billy.email.db.EmailSubscriptionContactDAO;
import com.subskribe.billy.email.services.CreditMemoEmailService;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.email.services.EmailNotificationQueueMessageHandler;
import com.subskribe.billy.email.services.EmailService;
import com.subskribe.billy.email.services.EmailSettingService;
import com.subskribe.billy.email.services.EmailTransport;
import com.subskribe.billy.email.services.EmailValidationService;
import com.subskribe.billy.email.services.InvoiceEmailService;
import com.subskribe.billy.email.services.SESEmailTransport;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.db.EntityAuthDAO;
import com.subskribe.billy.entity.db.EntityDAO;
import com.subskribe.billy.entity.db.EntityMigrationDAO;
import com.subskribe.billy.entity.service.EntityAuthContextResolver;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.entity.service.EntityIdGenerator;
import com.subskribe.billy.entity.service.EntityInvariantsService;
import com.subskribe.billy.entity.service.EntityMigrationService;
import com.subskribe.billy.entity.service.EntityService;
import com.subskribe.billy.entity.service.RlsInvariantsService;
import com.subskribe.billy.erp.externaljounralentrymapping.db.ExternalJournalEntryMappingDAO;
import com.subskribe.billy.erp.externaljounralentrymapping.service.ExternalJournalEntryMappingService;
import com.subskribe.billy.erp.merge.service.MergeAccountingService;
import com.subskribe.billy.erp.netsuite.mappers.DefaultNetsuitePayloadMapper;
import com.subskribe.billy.erp.netsuite.mappers.EducationPerfectNetsuitePayloadMapper;
import com.subskribe.billy.erp.netsuite.service.NetsuiteAuthService;
import com.subskribe.billy.erp.netsuite.service.NetsuiteClient;
import com.subskribe.billy.erp.netsuite.service.NetsuiteService;
import com.subskribe.billy.erp.quickbooks.service.QuickbooksAuthService;
import com.subskribe.billy.erp.quickbooks.service.QuickbooksDataService;
import com.subskribe.billy.erp.service.ErpIntegrationService;
import com.subskribe.billy.erp.service.ErpSyncQueueService;
import com.subskribe.billy.erp.service.ErpSyncService;
import com.subskribe.billy.erp.task.ErpQueuedTaskProcessor;
import com.subskribe.billy.erp.xero.service.XeroAuthService;
import com.subskribe.billy.erp.xero.service.XeroService;
import com.subskribe.billy.escalationpolicy.db.EscalationPolicyDAO;
import com.subskribe.billy.escalationpolicy.service.EscalationPolicyIdGenerator;
import com.subskribe.billy.escalationpolicy.service.EscalationPolicyService;
import com.subskribe.billy.esign.db.EsignDAO;
import com.subskribe.billy.esign.db.EsignTenantSignatoryDAO;
import com.subskribe.billy.esign.services.EsignDocumentService;
import com.subskribe.billy.esign.services.EsignEmailService;
import com.subskribe.billy.esign.services.EsignIntegrationService;
import com.subskribe.billy.esign.services.EsignService;
import com.subskribe.billy.esign.services.EsignWebhookProcessor;
import com.subskribe.billy.event.consumer.EventConsumer;
import com.subskribe.billy.event.db.EventDAO;
import com.subskribe.billy.event.job.EventPumpTask;
import com.subskribe.billy.event.service.AllEventConsumerService;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.event.service.EventPumpingService;
import com.subskribe.billy.event.task.EventQueuedTaskProcessor;
import com.subskribe.billy.foreignexchange.db.CurrencyConversionRateDAO;
import com.subskribe.billy.foreignexchange.db.CurrencyTypeSettingDAO;
import com.subskribe.billy.foreignexchange.db.RealizedGainLossDAO;
import com.subskribe.billy.foreignexchange.db.TransactionalExchangeRateDAO;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateService;
import com.subskribe.billy.foreignexchange.service.CurrencyTypeSettingGetService;
import com.subskribe.billy.foreignexchange.service.CurrencyTypeSettingService;
import com.subskribe.billy.foreignexchange.service.ExchangeRateProvider;
import com.subskribe.billy.foreignexchange.service.QuoddExchangeRateProvider;
import com.subskribe.billy.foreignexchange.service.RandomExchangeRateProvider;
import com.subskribe.billy.foreignexchange.service.RealizedGainLossService;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeBackfillService;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateGetService;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateService;
import com.subskribe.billy.foreignexchange.task.ExchangeRateRefreshTaskProcessor;
import com.subskribe.billy.graphql.GqlAuthContext;
import com.subskribe.billy.graphql.account.AccountContactDataAggregator;
import com.subskribe.billy.graphql.account.AccountDataAggregator;
import com.subskribe.billy.graphql.approvalflow.ApprovalFlowDataAggregator;
import com.subskribe.billy.graphql.creditmemo.CreditMemoDataAggregator;
import com.subskribe.billy.graphql.esign.EsignDataAggregator;
import com.subskribe.billy.graphql.invoice.InvoiceDataAggregator;
import com.subskribe.billy.graphql.invoice.SettlementApplicationDataAggregator;
import com.subskribe.billy.graphql.order.OrderDataAggregator;
import com.subskribe.billy.graphql.payment.PaymentDataAggregator;
import com.subskribe.billy.graphql.productcatalog.ChargeDataAggregator;
import com.subskribe.billy.graphql.productcatalog.PlanDataAggregator;
import com.subskribe.billy.graphql.productcatalog.ProductDataAggregator;
import com.subskribe.billy.graphql.refund.RefundDataAggregator;
import com.subskribe.billy.graphql.subscription.SubscriptionDataAggregator;
import com.subskribe.billy.graphql.user.UserDataAggregator;
import com.subskribe.billy.health.service.HealthCheckService;
import com.subskribe.billy.hubspot.client.HubspotClientFactory;
import com.subskribe.billy.hubspot.db.HubSpotDAO;
import com.subskribe.billy.hubspot.service.HubSpotContactService;
import com.subskribe.billy.hubspot.service.HubSpotGetService;
import com.subskribe.billy.hubspot.service.HubSpotInstallService;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.hubspot.service.HubSpotJobQueueService;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.hubspot.task.HubspotQueuedTaskProcessor;
import com.subskribe.billy.idempotency.db.IdempotencyDAO;
import com.subskribe.billy.idempotency.filters.IdempotencyRequestResponseFilter;
import com.subskribe.billy.idempotency.service.IdempotencyService;
import com.subskribe.billy.infra.SystemDataProvider;
import com.subskribe.billy.infra.maintenance.MaintenanceMessageAdminService;
import com.subskribe.billy.infra.maintenance.MaintenanceMessageService;
import com.subskribe.billy.infra.maintenance.MaintenanceService;
import com.subskribe.billy.infra.maintenance.db.MaintenanceMessageAdminDAO;
import com.subskribe.billy.infra.maintenance.db.MaintenanceMessageDAO;
import com.subskribe.billy.infra.maintenance.db.ScheduledMaintenanceDAO;
import com.subskribe.billy.integration.db.IntegrationDAO;
import com.subskribe.billy.integration.service.IntegrationAuthFacadeService;
import com.subskribe.billy.integration.service.IntegrationFacadeService;
import com.subskribe.billy.integration.service.IntegrationPersistenceService;
import com.subskribe.billy.integration.service.MergeAuthService;
import com.subskribe.billy.integration.service.MergeIntegrationService;
import com.subskribe.billy.invoice.SellingPriceCalculator;
import com.subskribe.billy.invoice.automated.db.AutomatedInvoiceRuleDAO;
import com.subskribe.billy.invoice.automated.service.AutomatedInvoiceRuleGetService;
import com.subskribe.billy.invoice.automated.service.AutomatedInvoiceRuleIdGenerator;
import com.subskribe.billy.invoice.automated.service.AutomatedInvoiceRuleService;
import com.subskribe.billy.invoice.bulk.db.BulkInvoiceDAO;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceRunIdGenerator;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceSelectorService;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceService;
import com.subskribe.billy.invoice.bulk.task.AutomatedInvoiceRuleTaskProcessor;
import com.subskribe.billy.invoice.db.InvoiceConfigDAO;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.document.InvoiceDocumentDataAggregator;
import com.subskribe.billy.invoice.document.InvoiceTemplateToHtmlConverter;
import com.subskribe.billy.invoice.number.InvoiceNumberGenerator;
import com.subskribe.billy.invoice.number.RandomNumberGenerator;
import com.subskribe.billy.invoice.number.SequenceInvoiceNumberGenerator;
import com.subskribe.billy.invoice.service.AccountInvoiceService;
import com.subskribe.billy.invoice.service.BillingScheduleService;
import com.subskribe.billy.invoice.service.DiscountCalculator;
import com.subskribe.billy.invoice.service.InvoiceAmountCalculator;
import com.subskribe.billy.invoice.service.InvoiceConfigurationService;
import com.subskribe.billy.invoice.service.InvoiceDocumentGenerationTrackerService;
import com.subskribe.billy.invoice.service.InvoiceDocumentRenderService;
import com.subskribe.billy.invoice.service.InvoiceDocumentService;
import com.subskribe.billy.invoice.service.InvoiceEventService;
import com.subskribe.billy.invoice.service.InvoicePreviewReportService;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.service.InvoiceServiceInternal;
import com.subskribe.billy.invoice.service.InvoiceStatusUpdater;
import com.subskribe.billy.invoice.service.PercentOfChargeInvoiceHelper;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.invoice.service.RatingService;
import com.subskribe.billy.invoice.service.VoidInvoiceService;
import com.subskribe.billy.invoice.service.processor.OneTimeInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PercentOfInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PrepaidInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.RecurringInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.UsageInvoiceProcessor;
import com.subskribe.billy.invoice.task.InvoiceEmailQueuedTaskProcessor;
import com.subskribe.billy.invoice.tax.service.AllTaxService;
import com.subskribe.billy.invoice.tax.service.PercentBasedTaxService;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.invoice.tax.service.TaxVerifierService;
import com.subskribe.billy.invoice.upload.DocumentS3Uploader;
import com.subskribe.billy.invoicedunning.db.InvoiceDunningDAO;
import com.subskribe.billy.invoicedunning.services.InvoiceDunningService;
import com.subskribe.billy.invoicesettlement.db.CreditMemoDAO;
import com.subskribe.billy.invoicesettlement.db.InvoiceSettlementDAO;
import com.subskribe.billy.invoicesettlement.db.RefundDAO;
import com.subskribe.billy.invoicesettlement.document.CreditMemoDocumentDataAggregator;
import com.subskribe.billy.invoicesettlement.document.render.CreditMemoRenderer;
import com.subskribe.billy.invoicesettlement.document.render.CreditMemoTemplateToHtmlConverter;
import com.subskribe.billy.invoicesettlement.services.CreditMemoDocumentRenderService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoDocumentService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoNumberGenerator;
import com.subskribe.billy.invoicesettlement.services.CreditMemoRetrievalService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoService;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.invoicesettlement.services.RefundIdGenerator;
import com.subskribe.billy.invoicesettlement.services.RefundService;
import com.subskribe.billy.invoicesettlement.services.SequenceCreditMemoNumberGenerator;
import com.subskribe.billy.invoicesettlement.services.TenantCreditMemoConfigurationService;
import com.subskribe.billy.invoicesettlement.upload.CreditMemoS3Uploader;
import com.subskribe.billy.log.LogStorageProvider;
import com.subskribe.billy.log.S3LogStorageProvider;
import com.subskribe.billy.looker.LookerConfigurationExportService;
import com.subskribe.billy.looker.LookerService;
import com.subskribe.billy.looker.LookerTenantGroupService;
import com.subskribe.billy.looker.LookerTenantService;
import com.subskribe.billy.looker.LookerUserStatusSwitcher;
import com.subskribe.billy.looker.client.LookerClient;
import com.subskribe.billy.looker.db.LookerTenantDAO;
import com.subskribe.billy.looker.db.LookerUserDAO;
import com.subskribe.billy.looker.util.LookerNameResolver;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metricsreporting.db.ExternalArrScheduleDAO;
import com.subskribe.billy.metricsreporting.db.OrderLineMetricsReportingDAO;
import com.subskribe.billy.metricsreporting.db.ReportingJobQueueDAO;
import com.subskribe.billy.metricsreporting.db.TransactionalMetricsReportingDAO;
import com.subskribe.billy.metricsreporting.service.ExternalArrScheduleIdGenerator;
import com.subskribe.billy.metricsreporting.service.ExternalArrScheduleService;
import com.subskribe.billy.metricsreporting.service.MetricsReportingService;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.metricsreporting.service.TransactionalMetricsGetService;
import com.subskribe.billy.notification.db.NotificationDAO;
import com.subskribe.billy.notification.db.NotificationInstanceDAO;
import com.subskribe.billy.notification.db.SlackApprovalMessageDetailDAO;
import com.subskribe.billy.notification.event.NotificationEventConsumer;
import com.subskribe.billy.notification.service.ApprovalFlowNotificationService;
import com.subskribe.billy.notification.service.NotificationEventAdapter;
import com.subskribe.billy.notification.service.NotificationRetryService;
import com.subskribe.billy.notification.service.NotificationService;
import com.subskribe.billy.notification.service.NotificationTargetService;
import com.subskribe.billy.notification.service.SlackNotificationProcessor;
import com.subskribe.billy.notification.service.TestNotificationService;
import com.subskribe.billy.notification.service.WebhookNotificationProcessor;
import com.subskribe.billy.notification.service.WebhookSigningKeyService;
import com.subskribe.billy.notification.service.WebhookSigningService;
import com.subskribe.billy.notification.service.email.EmailNotificationProcessor;
import com.subskribe.billy.notification.service.email.EmailNotificationRateLimiter;
import com.subskribe.billy.notification.service.email.NotificationEmailFormatter;
import com.subskribe.billy.notification.slack.SlackNotificationService;
import com.subskribe.billy.opportunity.db.OpportunityDAO;
import com.subskribe.billy.opportunity.db.OpportunityIdDAO;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.opportunity.service.OpportunityIdGenerator;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.EvergreenUtils;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.document.OrderDocumentDataAggregator;
import com.subskribe.billy.order.document.OrderTemplateToHtmlConverter;
import com.subskribe.billy.order.quotebuilder.db.QscriptDAO;
import com.subskribe.billy.order.quotebuilder.db.QuestionStoreDAO;
import com.subskribe.billy.order.quotebuilder.db.SlackWorkspaceInfoDAO;
import com.subskribe.billy.order.quotebuilder.service.GuidedSellingUsecaseIdGenerator;
import com.subskribe.billy.order.quotebuilder.service.QuestionAnswerService;
import com.subskribe.billy.order.quotebuilder.service.QuoteBuilderService;
import com.subskribe.billy.order.quotebuilder.service.SlackQuoteBuilderService;
import com.subskribe.billy.order.quotebuilder.service.UserGuidedSellingConversationStorage;
import com.subskribe.billy.order.services.ChangeOrderService;
import com.subskribe.billy.order.services.MissingOrderChargesService;
import com.subskribe.billy.order.services.OrderAttachmentService;
import com.subskribe.billy.order.services.OrderAttributesUpdateService;
import com.subskribe.billy.order.services.OrderCommentService;
import com.subskribe.billy.order.services.OrderCustomBillingService;
import com.subskribe.billy.order.services.OrderDataBackfillService;
import com.subskribe.billy.order.services.OrderDiscountService;
import com.subskribe.billy.order.services.OrderDocumentRenderingService;
import com.subskribe.billy.order.services.OrderDocumentService;
import com.subskribe.billy.order.services.OrderDocumentTemplateService;
import com.subskribe.billy.order.services.OrderEventService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderIdGenerator;
import com.subskribe.billy.order.services.OrderPdfGenerationTrackerService;
import com.subskribe.billy.order.services.OrderResellerService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.order.services.OrderValidationService;
import com.subskribe.billy.payment.db.AccountAutomaticPaymentMethodDAO;
import com.subskribe.billy.payment.db.AccountPaymentManagementLinkDAO;
import com.subskribe.billy.payment.db.InvoicePaymentManagementLinkDAO;
import com.subskribe.billy.payment.db.PaymentBankAccountDAO;
import com.subskribe.billy.payment.db.PaymentConfigurationDAO;
import com.subskribe.billy.payment.db.PaymentDAO;
import com.subskribe.billy.payment.db.PaymentProviderDAO;
import com.subskribe.billy.payment.integration.db.PaymentIntegrationDAO;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationService;
import com.subskribe.billy.payment.link.db.PaymentLinkDAO;
import com.subskribe.billy.payment.link.service.PaymentLinkService;
import com.subskribe.billy.payment.processor.db.PaymentProcessorJobQueueDAO;
import com.subskribe.billy.payment.processor.service.PaymentProcessorJobQueueService;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodGetService;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodService;
import com.subskribe.billy.payment.services.AccountPaymentManagementLinkService;
import com.subskribe.billy.payment.services.InvoicePaymentManagementLinkService;
import com.subskribe.billy.payment.services.LinkBasedPaymentService;
import com.subskribe.billy.payment.services.PaymentBankAccountGetService;
import com.subskribe.billy.payment.services.PaymentBankAccountIdGenerator;
import com.subskribe.billy.payment.services.PaymentBankAccountService;
import com.subskribe.billy.payment.services.PaymentConfigurationService;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.payment.services.PaymentIdGenerator;
import com.subskribe.billy.payment.services.PaymentIntegrationCleanupService;
import com.subskribe.billy.payment.services.PaymentOrchestrator;
import com.subskribe.billy.payment.services.PaymentProcessorService;
import com.subskribe.billy.payment.services.PaymentProviderService;
import com.subskribe.billy.payment.services.PaymentReconciliationService;
import com.subskribe.billy.payment.services.PaymentRetryConfigGroupingIdGenerator;
import com.subskribe.billy.payment.services.PaymentService;
import com.subskribe.billy.payment.services.PaymentStateProcessor;
import com.subskribe.billy.payment.stripe.service.StripeImportService;
import com.subskribe.billy.payment.stripe.service.StripeRefundService;
import com.subskribe.billy.payment.stripe.service.StripeSelfServeService;
import com.subskribe.billy.payment.stripe.service.StripeService;
import com.subskribe.billy.payment.stripe.webhook.StripeIncomingWebhookProcessor;
import com.subskribe.billy.payment.stripe.webhook.StripeWebhookEventProcessor;
import com.subskribe.billy.payment.task.PaymentRetryQueuedTaskProcessor;
import com.subskribe.billy.paymentterms.db.PaymentTermSettingsDAO;
import com.subskribe.billy.paymentterms.services.PaymentTermSettingsService;
import com.subskribe.billy.platformfeature.db.PlatformFeatureDAO;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.pricing.services.PricingService;
import com.subskribe.billy.productcatalog.db.ChargeDAO;
import com.subskribe.billy.productcatalog.db.ChargeIdDAO;
import com.subskribe.billy.productcatalog.db.ProductCatalogDAO;
import com.subskribe.billy.productcatalog.db.TaxRateDAO;
import com.subskribe.billy.productcatalog.db.UnitOfMeasureDAO;
import com.subskribe.billy.productcatalog.plangroup.db.PlanGroupDAO;
import com.subskribe.billy.productcatalog.plangroup.service.PlanGroupIdGenerator;
import com.subskribe.billy.productcatalog.plangroup.service.PlanGroupService;
import com.subskribe.billy.productcatalog.priceattribute.db.PriceAttributeDAO;
import com.subskribe.billy.productcatalog.priceattribute.service.PriceAttributeIdGenerator;
import com.subskribe.billy.productcatalog.ratecard.db.ChargeDefaultAttributeReferencesDAO;
import com.subskribe.billy.productcatalog.ratecard.db.RateCardDAO;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardIdGenerator;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ChargeDataBackfillService;
import com.subskribe.billy.productcatalog.services.ChargeGetService;
import com.subskribe.billy.productcatalog.services.ChargeIdGenerator;
import com.subskribe.billy.productcatalog.services.ChargeService;
import com.subskribe.billy.productcatalog.services.PlanIdGenerator;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogService;
import com.subskribe.billy.productcatalog.services.ProductCategoryIdGenerator;
import com.subskribe.billy.productcatalog.services.ProductIdGenerator;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.productcatalog.services.TaxRateService;
import com.subskribe.billy.productcatalog.services.UnitOfMeasureService;
import com.subskribe.billy.productcatalog.validation.CatalogValidation;
import com.subskribe.billy.productcatalog.validation.ChargeValidation;
import com.subskribe.billy.proposal.service.ProposalService;
import com.subskribe.billy.ratelimiter.filter.DistributedRateLimiter;
import com.subskribe.billy.ratelimiter.filter.RateLimiterFilter;
import com.subskribe.billy.ratelimiter.ip.IpBasedRequestRateLimiter;
import com.subskribe.billy.report.service.ReportService;
import com.subskribe.billy.revrec.bulk.db.BulkRevenueDAO;
import com.subskribe.billy.revrec.bulk.service.BulkRevenueRecognitionIdGenerator;
import com.subskribe.billy.revrec.bulk.service.BulkRevenueService;
import com.subskribe.billy.revrec.db.RecognitionEventDAO;
import com.subskribe.billy.revrec.db.RecognitionRuleDAO;
import com.subskribe.billy.revrec.db.RecognitionScheduleDAO;
import com.subskribe.billy.revrec.db.RecognitionTransactionDAO;
import com.subskribe.billy.revrec.db.RevenueEnablementProgressDAO;
import com.subskribe.billy.revrec.services.RecognitionRuleIdGenerator;
import com.subskribe.billy.revrec.services.RecognitionScheduleIdGenerator;
import com.subskribe.billy.revrec.services.RecognitionTransactionIdGenerator;
import com.subskribe.billy.revrec.services.RevenueEnablementService;
import com.subskribe.billy.revrec.services.RevenueRecognitionEventService;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.revrec.services.RevenueRecognitionJobService;
import com.subskribe.billy.revrec.services.RevenueRecognitionService;
import com.subskribe.billy.revrec.services.RevenueWaterfallService;
import com.subskribe.billy.revrec.task.RevrecQueuedTaskProcessor;
import com.subskribe.billy.salesforce.db.SalesforceDAO;
import com.subskribe.billy.salesforce.service.SalesforceAccountService;
import com.subskribe.billy.salesforce.service.SalesforceArrTrendService;
import com.subskribe.billy.salesforce.service.SalesforceContactService;
import com.subskribe.billy.salesforce.service.SalesforceGetService;
import com.subskribe.billy.salesforce.service.SalesforceIntegrationService;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.salesforce.service.SalesforceLoginService;
import com.subskribe.billy.salesforce.service.SalesforceQueryService;
import com.subskribe.billy.salesforce.service.SalesforceRequestService;
import com.subskribe.billy.salesforce.service.SalesforceRestService;
import com.subskribe.billy.salesforce.service.SalesforceService;
import com.subskribe.billy.salesforce.service.SalesforceSubscriptionService;
import com.subskribe.billy.salesforce.task.SalesforceQueuedTaskProcessor;
import com.subskribe.billy.salesroom.db.SalesRoomLinkDAO;
import com.subskribe.billy.salesroom.intelligent.db.IntelligentSalesRoomDAO;
import com.subskribe.billy.salesroom.intelligent.service.IntelligentSalesRoomS3Service;
import com.subskribe.billy.salesroom.intelligent.service.IntelligentSalesRoomService;
import com.subskribe.billy.salesroom.intelligent.service.IntelligentSalesRoomTaskProcessor;
import com.subskribe.billy.salesroom.intelligent.service.WebScraper;
import com.subskribe.billy.salesroom.intelligent.service.WebsiteMetadataExtractor;
import com.subskribe.billy.salesroom.service.SalesRoomLinkService;
import com.subskribe.billy.salesroom.service.SalesRoomService;
import com.subskribe.billy.scheduler.db.QuartzBillyDatabaseConnectionProvider;
import com.subskribe.billy.scheduler.job.JobContext;
import com.subskribe.billy.scheduler.job.QuartzDependencyInjectingJobFactory;
import com.subskribe.billy.scheduler.job.QuartzJobSchedulerProvider;
import com.subskribe.billy.scheduler.log.LogHeartbeat;
import com.subskribe.billy.scheduler.queue.QuartzQueueMessageHandler;
import com.subskribe.billy.scheduler.service.QuartzManagerService;
import com.subskribe.billy.scheduler.service.QuartzSchedulerService;
import com.subskribe.billy.search.SearchReindexTask;
import com.subskribe.billy.search.SearchUpdateTask;
import com.subskribe.billy.search.db.SearchDAO;
import com.subskribe.billy.search.db.SearchIndexMappingDAO;
import com.subskribe.billy.search.db.SearchUpdaterDbMaintenance;
import com.subskribe.billy.search.reindex.db.SearchReindexDAO;
import com.subskribe.billy.search.reindex.service.SearchReindexService;
import com.subskribe.billy.search.service.SearchAdminService;
import com.subskribe.billy.search.service.SearchIndexMappingService;
import com.subskribe.billy.search.service.SearchUpdateFilter;
import com.subskribe.billy.search.service.SearchUpdateService;
import com.subskribe.billy.settings.billingcycle.db.BillingCycleDefinitionDAO;
import com.subskribe.billy.settings.billingcycle.service.BillingCycleDefinitionService;
import com.subskribe.billy.shared.UniqueIdGenerator;
import com.subskribe.billy.shared.contactfetcher.ContactGroupOrUserFetcher;
import com.subskribe.billy.shared.db.DatabaseMaintenance;
import com.subskribe.billy.shared.db.DatabaseMaintenanceRunner;
import com.subskribe.billy.shared.entitycache.EntityCacheBuilder;
import com.subskribe.billy.shared.lock.DynamoDBLockProvider;
import com.subskribe.billy.shared.ratelimit.RateLimiterFactory;
import com.subskribe.billy.shared.render.HtmlToPdfConverter;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.task.LeaderElectedBackgroundTaskFactory;
import com.subskribe.billy.shared.task.queue.db.CapacityDAO;
import com.subskribe.billy.shared.task.queue.db.QueuedTaskDAO;
import com.subskribe.billy.shared.task.queue.db.QueuedTaskDbMaintenance;
import com.subskribe.billy.shared.task.queue.db.QueuedTaskMapper;
import com.subskribe.billy.shared.task.queue.db.QueuedTaskSelectionDAO;
import com.subskribe.billy.shared.task.queue.db.TaskDispatchDAO;
import com.subskribe.billy.shared.task.queue.db.TaskExecutionDAO;
import com.subskribe.billy.shared.task.queue.db.TaskMaintenanceDAO;
import com.subskribe.billy.shared.task.queue.db.TenantQueuedTaskDAO;
import com.subskribe.billy.shared.task.queue.db.TenantTaskExecutionDAO;
import com.subskribe.billy.shared.task.queue.distribution.CapacityManager;
import com.subskribe.billy.shared.task.queue.distribution.InProcessTaskDistributor;
import com.subskribe.billy.shared.task.queue.distribution.SqsBackedTaskDistributor;
import com.subskribe.billy.shared.task.queue.distribution.SqsTaskListener;
import com.subskribe.billy.shared.task.queue.distribution.TaskDistributor;
import com.subskribe.billy.shared.task.queue.event.EventSourcedTaskFactory;
import com.subskribe.billy.shared.task.queue.event.EventToTaskBridge;
import com.subskribe.billy.shared.task.queue.event.QueuedTaskEventConsumer;
import com.subskribe.billy.shared.task.queue.event.QueuedTaskEventRouter;
import com.subskribe.billy.shared.task.queue.event.TaskBridgeEventConsumer;
import com.subskribe.billy.shared.task.queue.event.TaskCompletedListener;
import com.subskribe.billy.shared.task.queue.event.TaskExecutingListener;
import com.subskribe.billy.shared.task.queue.metrics.TaskMetricPublisher;
import com.subskribe.billy.shared.task.queue.processor.TaskExecutor;
import com.subskribe.billy.shared.task.queue.processor.TaskProcessor;
import com.subskribe.billy.shared.task.queue.processor.TaskResultHandler;
import com.subskribe.billy.shared.task.queue.processor.TaskRouter;
import com.subskribe.billy.shared.task.queue.processor.stub.StubTaskProcessor;
import com.subskribe.billy.shared.task.queue.retry.TaskBackoffResultBuilder;
import com.subskribe.billy.shared.task.queue.scheduler.DefaultDispatcher;
import com.subskribe.billy.shared.task.queue.scheduler.TaskCleaner;
import com.subskribe.billy.shared.task.queue.scheduler.TaskDispatcher;
import com.subskribe.billy.shared.task.queue.scheduler.TaskScheduler;
import com.subskribe.billy.shared.task.queue.scheduler.TaskSchedulerRunner;
import com.subskribe.billy.shared.task.queue.service.TenantQueuedTaskExecutionService;
import com.subskribe.billy.shared.task.queue.service.TenantQueuedTaskService;
import com.subskribe.billy.shared.webhook.IncomingWebhookProcessor;
import com.subskribe.billy.shared.webhook.IncomingWebhookRouter;
import com.subskribe.billy.shared.webhook.IncomingWebhookTaskDispatcher;
import com.subskribe.billy.shared.webhook.S3WebhookDataStore;
import com.subskribe.billy.shared.webhook.WebhookDataStore;
import com.subskribe.billy.smartapproval.db.ApprovalFlowDataSnapshotDAO;
import com.subskribe.billy.smartapproval.service.ApprovalFlowDataSnapshotGetService;
import com.subskribe.billy.smartapproval.service.ApprovalFlowDataSnapshotService;
import com.subskribe.billy.subscription.db.SubscriptionChargeChangeScheduleDAO;
import com.subskribe.billy.subscription.db.SubscriptionDAO;
import com.subskribe.billy.subscription.db.SubscriptionStatusChangeScheduleDAO;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionChargeChangeTaskBackfiller;
import com.subskribe.billy.subscription.services.SubscriptionEventService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionIdGenerator;
import com.subskribe.billy.subscription.services.SubscriptionLifecycleNotificationsService;
import com.subskribe.billy.subscription.services.SubscriptionLifecycleScheduleService;
import com.subskribe.billy.subscription.services.SubscriptionLifecycleTaskProcessor;
import com.subskribe.billy.subscription.services.SubscriptionOrderService;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.subscription.services.SubscriptionStatusChangeTaskBackfiller;
import com.subskribe.billy.tax.tros.client.ThomsonReutersApiClient;
import com.subskribe.billy.tax.tros.service.ThomsonReutersIntegrationService;
import com.subskribe.billy.tax.tros.service.ThomsonReutersService;
import com.subskribe.billy.taxjar.db.TaxJarDAO;
import com.subskribe.billy.taxjar.service.TaxJarIntegrationGetService;
import com.subskribe.billy.taxjar.service.TaxJarIntegrationService;
import com.subskribe.billy.taxjar.service.TaxJarService;
import com.subskribe.billy.template.db.DocumentCustomContentDAO;
import com.subskribe.billy.template.db.DocumentMasterTemplateDAO;
import com.subskribe.billy.template.db.DocumentSectionDAO;
import com.subskribe.billy.template.db.DocumentTemplateDAO;
import com.subskribe.billy.template.db.DocumentTemplateUpdatedOnOrderDAO;
import com.subskribe.billy.template.db.OrderTermsDAO;
import com.subskribe.billy.template.db.PlanTermsDAO;
import com.subskribe.billy.template.db.TemplateScriptDAO;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderGetService;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderService;
import com.subskribe.billy.template.services.DocumentCustomContentGetService;
import com.subskribe.billy.template.services.DocumentCustomContentService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.DocumentTemplateService;
import com.subskribe.billy.template.services.OrderTermsService;
import com.subskribe.billy.template.services.PlanTermsService;
import com.subskribe.billy.template.services.TemplateScriptService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import com.subskribe.billy.tenant.db.TenantDAO;
import com.subskribe.billy.tenant.db.TenantSettingDAO;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenant.settings.accountreceivablecontact.db.AccountReceivableContactDAO;
import com.subskribe.billy.tenant.settings.accountreceivablecontact.services.AccountReceivableContactService;
import com.subskribe.billy.tenant.settings.dunningsettings.db.DunningSettingDAO;
import com.subskribe.billy.tenant.settings.dunningsettings.services.DunningSettingService;
import com.subskribe.billy.tenant.task.TenantManagementTaskProcessor;
import com.subskribe.billy.tenantjob.db.TenantJobQueueDAO;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.tenantjob.service.TenantJobGetService;
import com.subskribe.billy.tenantjob.task.TenantJobTaskCompletedListener;
import com.subskribe.billy.tenantjob.task.TenantJobTaskExecutingListener;
import com.subskribe.billy.tracing.JobRequestIdProvider;
import com.subskribe.billy.tracing.RequestIdProvider;
import com.subskribe.billy.tracing.UriInfoProvider;
import com.subskribe.billy.usage.db.UsageDAO;
import com.subskribe.billy.usage.service.UsageAggregationService;
import com.subskribe.billy.usage.service.UsageReportRenderingService;
import com.subskribe.billy.usage.service.UsageService;
import com.subskribe.billy.usage.service.UsageStatisticsService;
import com.subskribe.billy.user.db.UserDAO;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.BulkUserCreationService;
import com.subskribe.billy.user.service.UserGroupAndSegmentRetrievalService;
import com.subskribe.billy.user.service.UserIdGenerator;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.usergroup.db.UserGroupDAO;
import com.subskribe.billy.usergroup.service.UserGroupIdGenerator;
import com.subskribe.billy.usergroup.service.UserGroupService;
import io.dropwizard.setup.Environment;
import java.time.Clock;
import javax.inject.Singleton;
import javax.sql.DataSource;
import org.glassfish.hk2.api.Immediate;
import org.glassfish.hk2.api.InterceptionService;
import org.glassfish.hk2.utilities.binding.AbstractBinder;
import org.jooq.DSLContext;
import org.quartz.utils.ConnectionProvider;

/**
 * Simple HK2 binder for the Billy app. TODO:
 *
 * <ul>
 *   <li>Convert it to bundle if bootstrap time bindings are needed
 *   <li>Resource bindings can be moved here from Application if that works better
 *   <li>Resource bindings can be moved here from Application if that works better
 *   <li>Guice bridge can be added as a bundle if enhanced binding functionality is needed.
 * </ul>
 */
public class BillyBinder extends AbstractBinder {

    public static final String DATASOURCE_NAME = "postgres";

    private final Environment environment;

    private final BillyConfiguration billyConfig;

    private final DSLContextProvider dslContextProvider;

    private final MetricRegistry metricRegistry;

    private final SecretsService secretsService;

    private final TenantRlsEncryptionService tenantRlsEncryptionService;

    public BillyBinder(
        BillyConfiguration billyConfig,
        Environment env,
        SecretsService secretsService,
        DSLContextProvider dslContextProvider,
        TenantRlsEncryptionService tenantRlsEncryptionService
    ) {
        this.billyConfig = billyConfig;
        environment = env;
        this.dslContextProvider = dslContextProvider;
        metricRegistry = env.metrics();
        this.secretsService = secretsService;
        this.tenantRlsEncryptionService = tenantRlsEncryptionService;
    }

    @Override
    @AllowNonRlsDataAccess
    protected void configure() {
        bindDAOs();
        bindServices();
        bindCacheInvalidators();
        bindImportSchemas();
        bindJournalCreators();
        bindInvoiceProcessors();
        bindKinesisEventConsumers();
        bindKinesisWorkerStarter();
        bindTaskQueuePlatform();
        bindTaskProcessors();
        bindAIServices();
        bind(dslContextProvider.get()).to(DSLContext.class);
        bind(dslContextProvider.getManagedDatasource()).to(DataSource.class);
        bind(billyConfig).to(BillyConfiguration.class);
        bind(JacksonProvider.defaultMapper()).to(ObjectMapper.class);
        bindAsContract(ApiKeyValidator.class).in(Singleton.class);
        bindAsContract(ApiKeyGenerator.class).in(Singleton.class);
        bindAsContract(JWTTokenValidator.class).in(Singleton.class);
        bindAsContract(InvoiceNumberGenerator.class).in(Singleton.class);
        bindAsContract(RandomNumberGenerator.class).in(Singleton.class);
        bindAsContract(SequenceInvoiceNumberGenerator.class).in(Singleton.class);
        bind(dslContextProvider).to(DSLContextProvider.class);
        bind(dslContextProvider.getDbSessionParams()).to(DbSessionParams.class);
        bind(dslContextProvider.getDbSessionParamsListenerProvider()).to(DbSessionParamsListenerProvider.class);
        bindAsContract(EntityPolicyProvider.class).in(Singleton.class);
        bindAsContract(ApiKeyRsaKeyPairProvider.class).in(Singleton.class);
        bindAsContract(TenantIdProvider.class).in(Singleton.class);
        bindAsContract(CurrentUserProvider.class).in(Singleton.class);
        bindAsContract(UriInfoProvider.class).in(Singleton.class).proxy(true);
        bind(environment).to(Environment.class);
        bindAsContract(AuditlogPartitionManager.class).to(DatabaseMaintenance.class);
        bindAsContract(RequestIdProvider.class).in(Singleton.class);
        bind(QuartzBillyDatabaseConnectionProvider.class).to(ConnectionProvider.class).in(Singleton.class);
        bindAsContract(QuartzJobSchedulerProvider.class).in(Singleton.class);
        bindAsContract(QuartzQueueMessageHandler.class).in(Singleton.class);
        bindAsContract(DLQMessageHandler.class).in(Singleton.class);
        bindAsContract(QuartzDependencyInjectingJobFactory.class).in(Singleton.class);
        bindAsContract(GqlAuthContext.class).in(Singleton.class);
        bindAsContract(HtmlToPdfConverter.class).in(Singleton.class);
        bindAsContract(InvoiceTemplateToHtmlConverter.class).in(Singleton.class);
        bindAsContract(DocumentS3Uploader.class).in(Singleton.class);
        bindAsContract(InvoiceDocumentDataAggregator.class).in(Singleton.class);
        bindAsContract(SqsClientProvider.class).in(Singleton.class);
        bindAsContract(S3ClientProvider.class).in(Singleton.class);
        bindAsContract(KinesisWriterProvider.class).in(Singleton.class);
        bindAsContract(AwsAppConfigurationService.class).in(Immediate.class);
        bindAsContract(FeatureService.class).in(Immediate.class);
        bind(metricRegistry).to(MetricRegistry.class);
        bindAsContract(SnsClientProvider.class).in(Singleton.class);
        bindAsContract(PlanDataAggregator.class).in(Singleton.class);
        bindAsContract(ChargeDataAggregator.class).in(Singleton.class);
        bindAsContract(OrderIdGenerator.class).in(Singleton.class);
        bindAsContract(CompositeOrderIdGenerator.class).in(Singleton.class);
        bindAsContract(SubscriptionIdGenerator.class).in(Singleton.class);
        bindAsContract(AccountIdGenerator.class).in(Singleton.class);
        bindAsContract(ContactIdGenerator.class).in(Singleton.class);
        bindAsContract(ProductIdGenerator.class).in(Singleton.class);
        bindAsContract(ProductCategoryIdGenerator.class).in(Singleton.class);
        bindAsContract(PlanIdGenerator.class).in(Singleton.class);
        bindAsContract(OrderDataAggregator.class).in(Singleton.class);
        bindAsContract(SubscriptionDataAggregator.class).in(Singleton.class);
        bindAsContract(InvoiceDataAggregator.class).in(Singleton.class);
        bindAsContract(CreditMemoDataAggregator.class).in(Singleton.class);
        bindAsContract(AccountDataAggregator.class).in(Singleton.class);
        bindAsContract(UserIdGenerator.class).in(Singleton.class);
        bindAsContract(OpportunityIdGenerator.class).in(Singleton.class);
        bindAsContract(AccountContactDataAggregator.class).in(Singleton.class);
        bindAsContract(OrderTemplateToHtmlConverter.class).in(Singleton.class);
        bindAsContract(PaymentDataAggregator.class).in(Singleton.class);
        bindAsContract(DynamoDbClientProvider.class).in(Singleton.class);
        bindAsContract(RateLimiterFilter.class).to(Singleton.class);
        bindAsContract(DynamoDbHelper.class).in(Singleton.class);
        bindAsContract(JobRequestIdProvider.class).in(Singleton.class);
        bindAsContract(IdempotencyRequestResponseFilter.class).to(Singleton.class);
        bindAsContract(MemcachedClientProvider.class).in(Immediate.class);
        bindAsContract(SesClientProvider.class).in(Singleton.class);
        bindAsContract(AccountDomainImportProcessor.class).in(Singleton.class);
        bindAsContract(ElasticSearchClientProvider.class).in(Singleton.class);
        bindAsContract(ChargeIdGenerator.class).in(Singleton.class);
        bindAsContract(OrderDomainImportProcessor.class).in(Singleton.class);
        bindAsContract(IpBasedRequestRateLimiter.class).in(Singleton.class);
        bindAsContract(SellingPriceCalculator.class).in(Singleton.class);
        bindAsContract(TenantScopedConfigProvider.class).in(Immediate.class);
        bindAsContract(UserGroupIdGenerator.class).in(Singleton.class);
        bindAsContract(ApprovalFlowIdGenerator.class).in(Singleton.class);
        bindAsContract(ApprovalRoleIdGenerator.class).in(Singleton.class);
        bindAsContract(ApprovalSegmentIdGenerator.class).in(Singleton.class);
        bindAsContract(ApprovalFlowDataAggregator.class).in(Singleton.class);
        bindAsContract(CatalogValidation.class).in(Singleton.class);
        bindAsContract(ChargeValidation.class).in(Singleton.class);
        bindAsContract(ApprovalFlowInstanceGroupIdGenerator.class).in(Singleton.class);
        bindAsContract(LedgerAccountIdGenerator.class).in(Singleton.class);
        bindAsContract(RecognitionRuleIdGenerator.class).in(Singleton.class);
        bindAsContract(AccountingPeriodIdGenerator.class).in(Singleton.class);
        bindAsContract(RecognitionScheduleIdGenerator.class).in(Singleton.class);
        bindAsContract(RecognitionTransactionIdGenerator.class).in(Singleton.class);
        bindAsContract(CatalogRelationshipIdGenerator.class).in(Singleton.class);
        bindAsContract(PercentOfChargeInvoiceHelper.class).in(Singleton.class);
        bindAsContract(PercentOfInvoiceProcessor.class).in(Singleton.class);
        bindAsContract(CreditMemoNumberGenerator.class).in(Singleton.class);
        bindAsContract(SequenceCreditMemoNumberGenerator.class).in(Singleton.class);
        bindAsContract(InvoiceStatusUpdater.class).in(Singleton.class);
        bindAsContract(CreditMemoRenderer.class).in(Singleton.class);
        bindAsContract(CreditMemoTemplateToHtmlConverter.class).in(Singleton.class);
        bindAsContract(CreditMemoS3Uploader.class).in(Singleton.class);
        bindAsContract(RefundIdGenerator.class).in(Singleton.class);
        bindAsContract(SystemDataProvider.class).in(Singleton.class);
        bindAsContract(OrderDomainExporter.class).in(Singleton.class);
        bindAsContract(JournalEntryIdGenerator.class).in(Singleton.class);
        bindAsContract(JournalLineIdGenerator.class).in(Singleton.class);
        bindAsContract(S3LogStorageProvider.class).to(LogStorageProvider.class).in(Singleton.class);
        bindAsContract(LookerClient.class).in(Singleton.class);
        bindAsContract(EmailNotificationQueueMessageHandler.class).in(Singleton.class);
        bindAsContract(CatalogDomainImportProcessor.class).in(Singleton.class);
        bindAsContract(PaymentIdGenerator.class).in(Singleton.class);
        bindAsContract(CustomFieldIdGenerator.class).in(Singleton.class);
        bindAsContract(AccountDomainExporter.class).in(Singleton.class);
        bindAsContract(CatalogDomainExporter.class).in(Singleton.class);
        bindAsContract(StripeWebhookEventProcessor.class).in(Singleton.class);
        bindAsContract(PlanGroupIdGenerator.class).in(Singleton.class);
        bindAsContract(EscalationPolicyIdGenerator.class).in(Singleton.class);
        bindAsContract(PriceAttributeIdGenerator.class).in(Singleton.class);
        bindAsContract(RateCardIdGenerator.class).in(Singleton.class);
        bindAsContract(EntityIdGenerator.class).in(Singleton.class);
        bindAsContract(SettlementApplicationDataAggregator.class).in(Singleton.class);
        bind(Clock.systemUTC()).to(Clock.class);
        bindAsContract(NotificationEventAdapter.class).in(Singleton.class);
        bindAsContract(WebhookNotificationProcessor.class).in(Singleton.class);
        bindAsContract(SecureRandomKeyGenerator.class).in(Singleton.class);
        bindAsContract(LookerUserStatusSwitcher.class).in(Singleton.class);
        bindAsContract(LookerNameResolver.class).in(Singleton.class);
        bindAsContract(ExternalArrScheduleIdGenerator.class).in(Singleton.class);
        bindAsContract(DynamoDBLockProvider.class).in(Singleton.class);
        bindAsContract(SearchUpdateTask.class).in(Singleton.class);
        bindAsContract(LeaderElectedBackgroundTaskFactory.class).in(Immediate.class);
        bindAsContract(CancelAndRestructureOrderDataAggregator.class).in(Singleton.class);
        bindAsContract(SearchReindexTask.class).in(Singleton.class);
        bindAsContract(LogHeartbeat.class).in(Immediate.class);
        bindAsContract(EventPumpTask.class).in(Singleton.class);
        bindAsContract(SearchUpdateFilter.class).in(Singleton.class);
        bindAsContract(RateLimiterFactory.class).in(Singleton.class);
        bindAsContract(DistributedRateLimiter.class).in(Singleton.class);
        bindAsContract(DatabaseMaintenanceRunner.class).in(Singleton.class);
        bindAsContract(SearchUpdaterDbMaintenance.class).to(DatabaseMaintenance.class);
        bindAsContract(PaymentBankAccountIdGenerator.class).in(Singleton.class);
        bindAsContract(PaymentRetryConfigGroupingIdGenerator.class).in(Singleton.class);
        bindAsContract(BulkInvoiceRunIdGenerator.class).in(Singleton.class);
        bindAsContract(AutomatedInvoiceRuleIdGenerator.class).in(Singleton.class);
        bindAsContract(UniqueIdGenerator.class).in(Singleton.class);
        bindAsContract(ThomsonReutersApiClient.class).in(Singleton.class);
        bindAsContract(BankTransactionIdGenerator.class).in(Singleton.class);
        bindAsContract(GuidedSellingUsecaseIdGenerator.class).in(Singleton.class);
        bindAsContract(LambdaClientProvider.class).in(Singleton.class);
        bindAsContract(TemplateLambdaInvoker.class).in(Singleton.class);
        bindAsContract(SESEmailTransport.class).to(EmailTransport.class);
        bindAsContract(BulkRevenueRecognitionIdGenerator.class).in(Singleton.class);
        bindAsContract(NotificationEmailFormatter.class).in(Singleton.class);
        bindAsContract(EmailNotificationProcessor.class).in(Singleton.class);
        bindAsContract(WebScraper.class).in(Singleton.class);
        bindAsContract(WebsiteMetadataExtractor.class).in(Singleton.class);
        bindAsContract(IncomingWebhookTaskDispatcher.class).in(Singleton.class);
        bindAsContract(EntityAuthContextResolver.class).in(Singleton.class);
        bindAsContract(EsignWebhookProcessor.class).to(IncomingWebhookProcessor.class).in(Singleton.class);
        bindAsContract(DocusignWebhookProcessor.class).to(IncomingWebhookProcessor.class).in(Singleton.class);
        bindAsContract(JobContext.class).in(Singleton.class);
        bindAsContract(StripeIncomingWebhookProcessor.class).to(IncomingWebhookProcessor.class).in(Singleton.class);
        bindAsContract(EmailNotificationRateLimiter.class).in(Singleton.class);
        bindAsContract(S3WebhookDataStore.class).to(WebhookDataStore.class).in(Singleton.class);
    }

    private void bindKinesisWorkerStarter() {
        bindAsContract(KinesisConsumerStarter.class).in(Immediate.class);
    }

    private void bindImportSchemas() {
        bindAsContract(AccountDomainCreateSchema.class).in(Singleton.class);
        bindAsContract(AccountDomainUpdateSchema.class).in(Singleton.class);
        bindAsContract(OrderDomainCreateSchema.class).in(Singleton.class);
        bindAsContract(CatalogDomainSchema.class).in(Singleton.class);
    }

    private void bindJournalCreators() {
        bindAsContract(StandAloneCreditMemoJournalProcessor.class).in(Singleton.class);
        bindAsContract(InvoicePostedJournalCreator.class).in(Singleton.class);
        bindAsContract(InvoicePostedFxJournalProcessor.class).in(Singleton.class);
        bindAsContract(PaymentProcessedJournalCreator.class).in(Singleton.class);
        bindAsContract(SettlementFxJournalProcessor.class).in(Singleton.class);
        bindAsContract(CreditMemoFxJournalProcessor.class).in(Singleton.class);
        bindAsContract(RevenueRecognizedJournalCreator.class).in(Singleton.class);
        bindAsContract(RevenueRecognizedFxJournalProcessor.class).in(Singleton.class);
        bindAsContract(PaymentVoidedJournalCreator.class).in(Singleton.class);
        bindAsContract(RealizedGainLossJournalCreator.class).in(Singleton.class);
        bindAsContract(InvoiceVoidedJournalCreator.class).in(Singleton.class);
    }

    private void bindInvoiceProcessors() {
        bindAsContract(RecurringInvoiceProcessor.class).in(Singleton.class);
        bindAsContract(OneTimeInvoiceProcessor.class).in(Singleton.class);
        bindAsContract(PrepaidInvoiceProcessor.class).in(Singleton.class);
        bindAsContract(UsageInvoiceProcessor.class).in(Singleton.class);
    }

    private void bindAIServices() {
        //DAOs
        bindAsContract(PromptDAO.class).in(Singleton.class);

        bindAsContract(BedrockClientProvider.class).in(Singleton.class);

        bindAsContract(PromptService.class).in(Singleton.class);
        bindAsContract(FunctionCallService.class).in(Singleton.class);
        bindAsContract(OpenAIAgentSessionFactory.class).in(Singleton.class);
        bindAsContract(OpenAIAssistantsClientProvider.class).in(Singleton.class);
        bindAsContract(BedrockAgentSessionFactory.class).in(Singleton.class);

        bindAsContract(MessageStorage.class).in(Singleton.class);
        bindAsContract(UserGuidedSellingConversationStorage.class).in(Singleton.class);

        bindAsContract(BedrockSummarizationService.class).in(Singleton.class);
        bindAsContract(BedrockGuidedSelling.class).in(Singleton.class);
    }

    private void bindServices() {
        bind(secretsService).to(SecretsService.class);
        bind(tenantRlsEncryptionService).to(TenantRlsEncryptionService.class);
        bindAsContract(AccountService.class).in(Singleton.class);
        bindAsContract(AttachmentsService.class).in(Singleton.class);
        bindAsContract(ChangeOrderService.class).in(Singleton.class);
        bindAsContract(OrderGetService.class).in(Singleton.class);
        bindAsContract(OrderAttachmentService.class).in(Singleton.class);
        bindAsContract(OrderDiscountService.class).in(Singleton.class);
        bindAsContract(SalesforceJobQueueService.class).in(Singleton.class);
        bindAsContract(OrderService.class).in(Singleton.class);
        bindAsContract(ProductCatalogService.class).in(Singleton.class);
        bindAsContract(UserService.class).in(Singleton.class);
        bindAsContract(SubscriptionService.class).in(Singleton.class);
        bindAsContract(TenantService.class).in(Singleton.class);
        bindAsContract(PricingService.class).in(Singleton.class);
        bindAsContract(InvoiceServiceInternal.class).to(InvoiceService.class).in(Singleton.class);
        bindAsContract(InvoiceConfigurationService.class).in(Singleton.class);
        bindAsContract(InvoiceDunningService.class).in(Singleton.class);
        bindAsContract(PaymentService.class).in(Singleton.class);
        bindAsContract(PaymentOrchestrator.class).in(Singleton.class);
        bindAsContract(QuartzSchedulerService.class).in(Singleton.class);
        bindAsContract(InvoiceDocumentService.class).in(Singleton.class);
        bindAsContract(InvoiceSettlementService.class).in(Singleton.class);
        bindAsContract(ElasticSearchService.class).in(Singleton.class);
        bindAsContract(UnitOfMeasureService.class).in(Singleton.class);
        bindAsContract(TaxRateService.class).in(Singleton.class);
        bindAsContract(TaxRateGetService.class).in(Singleton.class);
        bindAsContract(SubscriptionBillingPeriodService.class).in(Singleton.class);
        bindAsContract(CognitoService.class).in(Singleton.class);
        bindAsContract(InvoiceDocumentRenderService.class).in(Singleton.class);
        bindAsContract(TenantSettingService.class).in(Singleton.class);
        bindAsContract(InvoiceRetrievalService.class).in(Singleton.class);
        bindAsContract(CreditMemoService.class).in(Singleton.class);
        bindAsContract(TenantCognitoService.class).in(Singleton.class);
        bindAsContract(MetricsService.class).in(Singleton.class);
        bindAsContract(OrderDocumentDataAggregator.class).in(Singleton.class);
        bindAsContract(OrderDocumentService.class).in(Singleton.class);
        bindAsContract(OrderDocumentRenderingService.class).in(Singleton.class);
        bindAsContract(DocumentService.class).in(Singleton.class);
        bindAsContract(SubscriptionGetService.class).in(Singleton.class);
        bindAsContract(SubscriptionLifecycleNotificationsService.class).in(Singleton.class);
        bindAsContract(BulkUserCreationService.class).in(Singleton.class);
        bind(BillyInterceptionService.class).to(InterceptionService.class).in(Singleton.class);
        bindAsContract(DLQService.class).in(Singleton.class);
        bindAsContract(NotificationService.class).in(Singleton.class);
        bindAsContract(SalesforceService.class).in(Singleton.class);
        bindAsContract(SalesforceGetService.class).in(Singleton.class);
        bindAsContract(OpportunityService.class).in(Singleton.class);
        bindAsContract(OpportunityGetService.class).in(Singleton.class);
        bindAsContract(SalesforceLoginService.class).in(Singleton.class);
        bindAsContract(CrmService.class).in(Singleton.class);
        bindAsContract(AvalaraIntegrationService.class).in(Singleton.class);
        bindAsContract(AvalaraIntegrationGetService.class).in(Singleton.class);
        bindAsContract(IdempotencyService.class).in(Singleton.class);
        bindAsContract(AvalaraService.class).in(Singleton.class);
        bindAsContract(PercentBasedTaxService.class).in(Singleton.class);
        bindAsContract(ApiKeyService.class).in(Singleton.class);
        bindAsContract(CacheService.class).in(Singleton.class);
        bindAsContract(DataImportService.class).in(Singleton.class);
        bindAsContract(FlatfileService.class).in(Singleton.class);
        bindAsContract(DocumentTemplateService.class).in(Singleton.class);
        bindAsContract(DocumentTemplateGetService.class).in(Singleton.class);
        bindAsContract(DocumentCustomContentService.class).in(Singleton.class);
        bindAsContract(DocumentCustomContentGetService.class).in(Singleton.class);
        bindAsContract(InvoiceEmailService.class).in(Singleton.class);
        bindAsContract(EmailContactListService.class).in(Singleton.class);
        bindAsContract(DocuSignService.class).in(Singleton.class);
        bindAsContract(ProductCatalogGetService.class).in(Singleton.class);
        bindAsContract(ChargeService.class).in(Singleton.class);
        bindAsContract(ChargeGetService.class).in(Singleton.class);
        bindAsContract(AccountGetService.class).in(Singleton.class);
        bindAsContract(DocuSignAuthService.class).in(Singleton.class);
        bindAsContract(IntegrationAuthFacadeService.class).in(Singleton.class);
        bindAsContract(IntegrationFacadeService.class).in(Singleton.class);
        bindAsContract(MergeIntegrationService.class).in(Singleton.class);
        bindAsContract(MergeAuthService.class).in(Singleton.class);
        bindAsContract(MergeAccountingService.class).in(Singleton.class);
        bindAsContract(QuickbooksAuthService.class).in(Singleton.class);
        bindAsContract(QuickbooksDataService.class).in(Singleton.class);
        bindAsContract(ErpSyncQueueService.class).in(Singleton.class);
        bindAsContract(ErpIntegrationService.class).in(Singleton.class);
        bindAsContract(ErpSyncService.class).in(Singleton.class);
        bindAsContract(XeroAuthService.class).in(Singleton.class);
        bindAsContract(XeroService.class).in(Singleton.class);
        bindAsContract(NetsuiteAuthService.class).in(Singleton.class);
        bindAsContract(NetsuiteService.class).in(Singleton.class);
        bindAsContract(NetsuiteClient.class).in(Singleton.class);
        bindAsContract(EducationPerfectNetsuitePayloadMapper.class).in(Singleton.class);
        bindAsContract(DefaultNetsuitePayloadMapper.class).in(Singleton.class);
        bindAsContract(ExternalJournalEntryMappingService.class).in(Singleton.class);
        bindAsContract(ReportService.class).in(Singleton.class);
        bindAsContract(OrderTermsService.class).in(Singleton.class);
        bindAsContract(DunningSettingService.class).in(Singleton.class);
        bindAsContract(AccountReceivableContactService.class).in(Singleton.class);
        bindAsContract(DiscountService.class).in(Singleton.class);
        bindAsContract(PaymentIntegrationService.class).in(Singleton.class);
        bindAsContract(PaymentLinkService.class).in(Singleton.class);
        bindAsContract(PaymentProcessorJobQueueService.class).in(Singleton.class);
        bindAsContract(AliasService.class).in(Singleton.class);
        bindAsContract(UsageService.class).in(Singleton.class);
        bindAsContract(StripeService.class).in(Singleton.class);
        bindAsContract(StripeRefundService.class).in(Singleton.class);
        bindAsContract(UsageAggregationService.class).in(Singleton.class);
        bindAsContract(SearchAdminService.class).in(Singleton.class);
        bindAsContract(UserGroupService.class).in(Singleton.class);
        bindAsContract(UsageStatisticsService.class).in(Singleton.class);
        bindAsContract(ApprovalFlowService.class).in(Singleton.class);
        bindAsContract(ApprovalFlowEmailService.class).in(Singleton.class);
        bindAsContract(ApprovalFlowHierarchyService.class).in(Singleton.class);
        bindAsContract(ApprovalMatrixCsvService.class).in(Singleton.class);
        bindAsContract(UsageReportRenderingService.class).in(Singleton.class);
        bindAsContract(ApprovalFlowInstanceService.class).in(Singleton.class);
        bindAsContract(ApprovalFlowStateUpdaterService.class).in(Singleton.class);
        bindAsContract(OrderApprovalFlowEvaluator.class).in(Singleton.class);
        bindAsContract(BulkInvoiceService.class).in(Singleton.class);
        bindAsContract(RevenueRecognitionService.class).in(Singleton.class);
        bindAsContract(RevenueRecognitionGetService.class).in(Singleton.class);
        bindAsContract(RevenueRecognitionJobService.class).in(Singleton.class);
        bindAsContract(ApprovalFlowPendingUserActionService.class).in(Singleton.class);
        bindAsContract(ApprovalFlowMembershipService.class).in(Singleton.class);
        bindAsContract(RevenueWaterfallService.class).in(Singleton.class);
        bindAsContract(RevenueRecognitionEventService.class).in(Singleton.class);
        bindAsContract(TenantCreditMemoConfigurationService.class).in(Singleton.class);
        bindAsContract(OrderCommentService.class).in(Singleton.class);
        bindAsContract(HealthCheckService.class).in(Singleton.class);
        bindAsContract(CreditMemoDocumentRenderService.class).in(Singleton.class);
        bindAsContract(CreditMemoDocumentService.class).in(Singleton.class);
        bindAsContract(CreditMemoDocumentDataAggregator.class).in(Singleton.class);
        bindAsContract(CreditMemoRetrievalService.class).in(Singleton.class);
        bindAsContract(AccountingService.class).in(Singleton.class);
        bindAsContract(AccountingGetService.class).in(Singleton.class);
        bindAsContract(AccountingPeriodService.class).in(Singleton.class);
        bindAsContract(AccountingPeriodCalculationService.class).in(Singleton.class);
        bindAsContract(AccountingPeriodStatusUpdateService.class).in(Singleton.class);
        bindAsContract(CreditMemoEmailService.class).in(Singleton.class);
        bindAsContract(PaymentConfigurationService.class).in(Singleton.class);
        bindAsContract(BulkInvoiceSelectorService.class).in(Singleton.class);
        bindAsContract(EventPublishingService.class).in(Singleton.class);
        bindAsContract(RefundService.class).in(Singleton.class);
        bindAsContract(AccountingLogWritingEventConsumer.class).to(EventConsumer.class).in(Singleton.class);
        bindAsContract(JournalEntryService.class).in(Singleton.class);
        bindAsContract(HubspotClientFactory.class).in(Singleton.class);
        bindAsContract(HubSpotService.class).in(Singleton.class);
        bindAsContract(HubSpotGetService.class).in(Singleton.class);
        bindAsContract(HubSpotIntegrationService.class).in(Singleton.class);
        bindAsContract(HubSpotInstallService.class).in(Singleton.class);
        bindAsContract(TenantJobGetService.class).in(Singleton.class);
        bindAsContract(TenantJobDispatcherService.class).in(Singleton.class);
        bindAsContract(SalesforceArrTrendService.class).in(Singleton.class);
        bindAsContract(SalesforceSubscriptionService.class).in(Singleton.class);
        bindAsContract(SalesforceAccountService.class).in(Singleton.class);
        bindAsContract(SalesforceIntegrationService.class).in(Singleton.class);
        bindAsContract(SalesforceRestService.class).in(Singleton.class);
        bindAsContract(PlatformFeatureService.class).in(Singleton.class);
        bindAsContract(CrmIntegrationService.class).in(Singleton.class);
        bindAsContract(PaymentIntegrationGetService.class).in(Singleton.class);
        bindAsContract(RefundDataAggregator.class).in(Singleton.class);
        bindAsContract(SalesforceQueryService.class).in(Singleton.class);
        bindAsContract(SalesforceRequestService.class).in(Singleton.class);
        bindAsContract(SalesforceContactService.class).in(Singleton.class);
        bindAsContract(UserGroupAndSegmentRetrievalService.class).in(Singleton.class);
        bindAsContract(ProrationConfigurationGetService.class).in(Singleton.class);
        bindAsContract(UserDataAggregator.class).in(Singleton.class);
        bindAsContract(ContactGroupOrUserFetcher.class).in(Singleton.class);
        bindAsContract(AccountingEventLogWriter.class).in(Singleton.class);
        bindAsContract(LookerService.class).in(Singleton.class);
        bindAsContract(LookerTenantService.class).in(Singleton.class);
        bindAsContract(LookerTenantGroupService.class).in(Singleton.class);
        bindAsContract(LookerConfigurationExportService.class).in(Singleton.class);
        bindAsContract(CustomFieldService.class).in(Singleton.class);
        bindAsContract(EmailService.class).in(Singleton.class);
        bindAsContract(PlanTermsService.class).in(Singleton.class);
        bindAsContract(AccountInvoiceService.class).in(Singleton.class);
        bindAsContract(PaymentGetService.class).in(Singleton.class);
        bindAsContract(PaymentProviderService.class).in(Singleton.class);
        bindAsContract(MetricsReportingService.class).in(Singleton.class);
        bindAsContract(VoidInvoiceService.class).in(Singleton.class);
        bindAsContract(CustomFieldProxy.class).in(Singleton.class);
        bindAsContract(ProductDataAggregator.class).in(Singleton.class);
        bindAsContract(PaymentTermSettingsService.class).in(Singleton.class);
        bindAsContract(ReportingJobQueueService.class).in(Singleton.class);
        bindAsContract(QuartzManagerService.class).in(Singleton.class);
        bindAsContract(CompositeOrderService.class).in(Singleton.class);
        bindAsContract(CompositeOrderGetService.class).in(Singleton.class);
        bindAsContract(CompositeOrderDocumentService.class).in(Singleton.class);
        bindAsContract(CompositeOrderDocumentRenderingService.class).in(Singleton.class);
        bindAsContract(EsignService.class).in(Singleton.class);
        bindAsContract(EsignDocumentService.class).in(Singleton.class);
        bindAsContract(EsignEmailService.class).in(Singleton.class);
        bindAsContract(HubSpotContactService.class).in(Singleton.class);
        bindAsContract(PlanGroupService.class).in(Singleton.class);
        bindAsContract(OrderPdfGenerationTrackerService.class).in(Singleton.class);
        bindAsContract(InvoiceDocumentGenerationTrackerService.class).in(Singleton.class);
        bindAsContract(ProposalService.class).in(Singleton.class);
        bindAsContract(AutomatedInvoiceRuleService.class).in(Singleton.class);
        bindAsContract(AutomatedInvoiceRuleGetService.class).in(Singleton.class);
        bindAsContract(DocumentLinkService.class).in(Singleton.class);
        bindAsContract(InvoicePaymentManagementLinkService.class).in(Singleton.class);
        bindAsContract(AccountPaymentManagementLinkService.class).in(Singleton.class);
        bindAsContract(AccountAutomaticPaymentMethodService.class).in(Singleton.class);
        bindAsContract(LinkBasedPaymentService.class).in(Singleton.class);
        bindAsContract(AccountPaymentMethodGetService.class).in(Singleton.class);
        bindAsContract(AccountPaymentMethodService.class).in(Singleton.class);
        bindAsContract(PaymentIntegrationCleanupService.class).in(Singleton.class);
        bindAsContract(BillingScheduleService.class).in(Singleton.class);
        bindAsContract(EscalationPolicyService.class).in(Singleton.class);
        bindAsContract(CLMService.class).in(Singleton.class);
        bindAsContract(StripeImportService.class).in(Singleton.class);
        bindAsContract(EmailLinkLoginService.class).in(Singleton.class);
        bindAsContract(CatalogRelationshipService.class).in(Singleton.class);
        bindAsContract(CatalogRelationshipGetService.class).in(Singleton.class);
        bindAsContract(CatalogRelationshipValidator.class).in(Singleton.class);
        bindAsContract(RateCardService.class).in(Singleton.class);
        bindAsContract(InvoiceAmountCalculator.class).in(Singleton.class);
        bindAsContract(DiscountCalculator.class).in(Singleton.class);
        bindAsContract(EntityGetService.class).in(Singleton.class);
        bindAsContract(EntityService.class).in(Singleton.class);
        bindAsContract(EntityInvariantsService.class).in(Singleton.class);
        bindAsContract(EntityMigrationService.class).in(Singleton.class);
        bindAsContract(RlsInvariantsService.class).in(Singleton.class);
        bindAsContract(EntityContextProvider.class).in(Singleton.class);
        bindAsContract(PaymentStateProcessor.class).in(Singleton.class);
        bindAsContract(PaymentReconciliationService.class).in(Singleton.class);
        bindAsContract(OrderEventService.class).in(Singleton.class);
        bindAsContract(InvoiceEventService.class).in(Singleton.class);
        bindAsContract(SubscriptionLifecycleScheduleService.class).in(Singleton.class);
        bindAsContract(AccountAutomaticPaymentMethodGetService.class).in(Singleton.class);
        bindAsContract(PaymentProcessorService.class).in(Singleton.class);
        bindAsContract(RatingService.class).in(Singleton.class);
        bindAsContract(WebhookSigningService.class).in(Singleton.class);
        bindAsContract(WebhookSigningKeyService.class).in(Singleton.class);
        bindAsContract(NotificationTargetService.class).in(Singleton.class);
        bindAsContract(TaxJarIntegrationService.class).in(Singleton.class);
        bindAsContract(TaxJarIntegrationGetService.class).in(Singleton.class);
        bindAsContract(TaxJarService.class).in(Singleton.class);
        bindAsContract(NotificationRetryService.class).in(Singleton.class);
        bindAsContract(AllTaxService.class).to(TaxService.class).in(Singleton.class);
        bindAsContract(EsignDataAggregator.class).in(Singleton.class);
        bindAsContract(ApprovalFlowNotificationService.class).in(Singleton.class);
        bindAsContract(OrderDataBackfillService.class).in(Singleton.class);
        bindAsContract(EsignIntegrationService.class).in(Singleton.class);
        bindAsContract(InvoicePreviewReportService.class).in(Singleton.class);
        bindAsContract(CustomizationService.class).in(Singleton.class);
        bindAsContract(UIConfigurationService.class).in(Singleton.class);
        bindAsContract(SearchIndexMappingService.class).in(Singleton.class);
        bindAsContract(SlackNotificationProcessor.class).in(Singleton.class);
        bindAsContract(SlackNotificationService.class).in(Singleton.class);
        bindAsContract(MaintenanceService.class).in(Singleton.class);
        bindAsContract(CustomTemplateUpdatedOnOrderService.class).in(Singleton.class);
        bindAsContract(CustomTemplateUpdatedOnOrderGetService.class).in(Singleton.class);
        bindAsContract(ExternalArrScheduleService.class).in(Singleton.class);
        bindAsContract(SearchUpdateService.class).in(Singleton.class);
        bindAsContract(SalesRoomLinkService.class).in(Singleton.class);
        bindAsContract(SalesRoomService.class).in(Singleton.class);
        bindAsContract(IntelligentSalesRoomService.class).in(Singleton.class);
        bindAsContract(IntelligentSalesRoomS3Service.class).in(Singleton.class);
        bindAsContract(SearchReindexService.class).in(Singleton.class);
        bindAsContract(ElasticSearchAdminService.class).in(Singleton.class);
        bindAsContract(EventPumpingService.class).in(Singleton.class);
        bindAsContract(MissingOrderChargesService.class).in(Singleton.class);
        bindAsContract(SubscriptionOrderService.class).in(Singleton.class);
        bindAsContract(AdminOperationsService.class).in(Singleton.class);
        bindAsContract(EntityContextResolver.class).in(Singleton.class);
        bindAsContract(HubSpotJobQueueService.class).in(Singleton.class);
        bindAsContract(AnrokIntegrationService.class).in(Singleton.class);
        bindAsContract(AnrokIntegrationGetService.class).in(Singleton.class);
        bindAsContract(AnrokService.class).in(Singleton.class);
        bindAsContract(IntegrationPersistenceService.class).in(Singleton.class);
        bindAsContract(CurrencyTypeSettingService.class).in(Singleton.class);
        bindAsContract(CurrencyTypeSettingGetService.class).in(Singleton.class);
        bindAsContract(CurrencyConversionRateService.class).in(Singleton.class);
        bindAsContract(QuoddExchangeRateProvider.class).to(ExchangeRateProvider.class).in(Singleton.class);
        bindAsContract(RandomExchangeRateProvider.class).to(RandomExchangeRateProvider.class).in(Singleton.class);
        bindAsContract(CurrencyConversionRateGetService.class).in(Singleton.class);
        bindAsContract(TransactionalExchangeRateService.class).in(Singleton.class);
        bindAsContract(TransactionalExchangeRateGetService.class).in(Singleton.class);
        bindAsContract(TransactionalExchangeBackfillService.class).in(Singleton.class);
        bindAsContract(RealizedGainLossService.class).in(Singleton.class);
        bindAsContract(TransactionalMetricsGetService.class).in(Singleton.class);
        bindAsContract(PaymentBankAccountService.class).in(Singleton.class);
        bindAsContract(PaymentBankAccountGetService.class).in(Singleton.class);
        bindAsContract(AccountBulkOperationService.class).in(Singleton.class);
        bindAsContract(MaintenanceMessageService.class).in(Singleton.class);
        bindAsContract(MaintenanceMessageAdminService.class).in(Singleton.class);
        bindAsContract(RevenueEnablementService.class).in(Singleton.class);
        bindAsContract(ThomsonReutersService.class).in(Singleton.class);
        bindAsContract(ThomsonReutersIntegrationService.class).in(Singleton.class);
        bindAsContract(EmailValidationService.class).in(Singleton.class);
        bindAsContract(AllEventConsumerService.class).in(Singleton.class);
        bindAsContract(QuoteBuilderService.class).in(Singleton.class);
        bindAsContract(CrmFieldMappingService.class).in(Singleton.class);
        bindAsContract(CrmFieldMappingImportService.class).in(Singleton.class);
        bindAsContract(EmailSettingService.class).in(Singleton.class);
        bindAsContract(ChargeDataBackfillService.class).in(Singleton.class);
        bindAsContract(QuestionAnswerService.class).in(Singleton.class);
        bindAsContract(BankTransactionsService.class).in(Singleton.class);
        bindAsContract(EntityCacheBuilder.class).in(Singleton.class);
        bindAsContract(LambdaService.class).in(Singleton.class);
        bindAsContract(ApprovalFlowDataSnapshotService.class).in(Singleton.class);
        bindAsContract(ApprovalFlowDataSnapshotGetService.class).in(Singleton.class);
        bindAsContract(TemplateScriptService.class).in(Singleton.class);
        bindAsContract(ApprovalFlowInstanceGetService.class).in(Singleton.class);
        bindAsContract(SlackQuoteBuilderService.class).in(Singleton.class);
        bindAsContract(OrderCustomBillingService.class).in(Singleton.class);
        bindAsContract(AdobeConversionService.class).to(DocxConversionService.class).in(Singleton.class);
        bindAsContract(BulkRevenueService.class).in(Singleton.class);
        bindAsContract(OrderAttributesUpdateService.class).in(Singleton.class);
        bindAsContract(OrderDocumentTemplateService.class).in(Singleton.class);
        bindAsContract(OrderResellerService.class).in(Singleton.class);
        bindAsContract(OrderValidationService.class).in(Singleton.class);
        bindAsContract(StripeSelfServeService.class).in(Singleton.class);
        bindAsContract(EvergreenUtils.class).in(Singleton.class);
        bindAsContract(TaxVerifierService.class).in(Singleton.class);
        bindAsContract(BillingCycleDefinitionService.class).in(Singleton.class);
        bindAsContract(SubscriptionEventService.class).in(Singleton.class);
        bindAsContract(TestNotificationService.class).in(Singleton.class);
    }

    private void bindCacheInvalidators() {
        bindAsContract(AccountCacheInvalidator.class).in(Singleton.class);
    }

    private void bindDAOs() {
        bindAsContract(AuthTenantCognitoDAO.class).in(Singleton.class);
        bindAsContract(AccountAddressDAO.class).in(Singleton.class);
        bindAsContract(AccountContactDAO.class).in(Singleton.class);
        bindAsContract(AccountDAO.class).in(Singleton.class);
        bindAsContract(AttachmentDAO.class).in(Singleton.class);
        bindAsContract(OrderDAO.class).in(Singleton.class);
        bindAsContract(SubscriptionDAO.class).in(Singleton.class);
        bindAsContract(ProductCatalogDAO.class).in(Singleton.class);
        bindAsContract(ChargeDAO.class).in(Singleton.class);
        bindAsContract(UserDAO.class).in(Singleton.class);
        bindAsContract(TenantDAO.class).in(Singleton.class);
        bindAsContract(AccountPaymentMethodDAO.class).in(Singleton.class);
        bindAsContract(InvoiceDAO.class).in(Singleton.class);
        bindAsContract(InvoiceDunningDAO.class).in(Singleton.class);
        bindAsContract(PaymentDAO.class).in(Singleton.class);
        bindAsContract(InvoiceSettlementDAO.class).in(Singleton.class);
        bindAsContract(SearchDAO.class).in(Singleton.class);
        bindAsContract(UnitOfMeasureDAO.class).in(Singleton.class);
        bindAsContract(TaxRateDAO.class).in(Singleton.class);
        bindAsContract(TenantSettingDAO.class).in(Singleton.class);
        bindAsContract(CreditMemoDAO.class).in(Singleton.class);
        bindAsContract(DocumentDAO.class).in(Singleton.class);
        bindAsContract(NotificationDAO.class).in(Singleton.class);
        bindAsContract(SalesforceDAO.class).in(Singleton.class);
        bindAsContract(OpportunityDAO.class).in(Singleton.class);
        bindAsContract(OpportunityIdDAO.class).in(Singleton.class);
        bindAsContract(AvalaraDAO.class).in(Singleton.class);
        bindAsContract(IdempotencyDAO.class).in(Immediate.class);
        bindAsContract(DlqDAO.class).in(Singleton.class);
        bindAsContract(ImportDAO.class).in(Singleton.class);
        bindAsContract(FlatfileDAO.class).in(Singleton.class);
        bindAsContract(DocumentTemplateDAO.class).in(Singleton.class);
        bindAsContract(EmailDAO.class).in(Singleton.class);
        bindAsContract(EmailSubscriptionContactDAO.class).in(Singleton.class);
        bindAsContract(DocuSignDAO.class).in(Singleton.class);
        bindAsContract(ChargeIdDAO.class).in(Singleton.class);
        bindAsContract(OrderTermsDAO.class).in(Singleton.class);
        bindAsContract(DunningSettingDAO.class).in(Singleton.class);
        bindAsContract(AccountReceivableContactDAO.class).in(Singleton.class);
        bindAsContract(ApiKeyDAO.class).in(Singleton.class);
        bindAsContract(DiscountDAO.class).in(Singleton.class);
        bindAsContract(PaymentIntegrationDAO.class).in(Singleton.class);
        bindAsContract(PaymentLinkDAO.class).in(Singleton.class);
        bindAsContract(PaymentProcessorJobQueueDAO.class).in(Singleton.class);
        bindAsContract(UsageDAO.class).in(Singleton.class);
        bindAsContract(UserGroupDAO.class).in(Singleton.class);
        bindAsContract(ApprovalFlowDAO.class).in(Singleton.class);
        bindAsContract(ApprovalFlowInstanceDAO.class).in(Singleton.class);
        bindAsContract(ApprovalFlowHierarchyDao.class).in(Singleton.class);
        bindAsContract(BulkInvoiceDAO.class).in(Singleton.class);
        bindAsContract(LedgerAccountDAO.class).in(Singleton.class);
        bindAsContract(ApprovalFlowEmailDAO.class).in(Singleton.class);
        bindAsContract(RecognitionRuleDAO.class).in(Singleton.class);
        bindAsContract(ApprovalFlowPendingUserActionDAO.class).in(Singleton.class);
        bindAsContract(AccountingPeriodDAO.class).in(Singleton.class);
        bindAsContract(RecognitionScheduleDAO.class).in(Singleton.class);
        bindAsContract(RecognitionTransactionDAO.class).in(Singleton.class);
        bindAsContract(CatalogRelationshipDAO.class).in(Singleton.class);
        bindAsContract(DocumentSectionDAO.class).in(Singleton.class);
        bindAsContract(DocumentCustomContentDAO.class).in(Singleton.class);
        bindAsContract(DocumentMasterTemplateDAO.class).in(Singleton.class);
        bindAsContract(SubscriptionChargeAliasDAO.class).in(Singleton.class);
        bindAsContract(RecognitionEventDAO.class).in(Singleton.class);
        bindAsContract(PaymentConfigurationDAO.class).in(Singleton.class);
        bindAsContract(RefundDAO.class).in(Singleton.class);
        bindAsContract(EventDAO.class).in(Singleton.class);
        bindAsContract(PlatformFeatureDAO.class).in(Singleton.class);
        bindAsContract(HubSpotDAO.class).in(Singleton.class);
        bindAsContract(TenantJobQueueDAO.class).in(Singleton.class);
        bindAsContract(JournalEntryDAO.class).in(Singleton.class);
        bindAsContract(JournalEntryIdDAO.class).in(Singleton.class);
        bindAsContract(IntegrationDAO.class).in(Singleton.class);
        bindAsContract(ExternalJournalEntryMappingDAO.class).in(Singleton.class);
        bindAsContract(CustomFieldDAO.class).in(Singleton.class);
        bindAsContract(PlanTermsDAO.class).in(Singleton.class);
        bindAsContract(OrderLineMetricsReportingDAO.class).in(Singleton.class);
        bindAsContract(PaymentProviderDAO.class).in(Singleton.class);
        bindAsContract(PaymentTermSettingsDAO.class).in(Singleton.class);
        bindAsContract(EmailContactListDAO.class).in(Singleton.class);
        bindAsContract(ReportingJobQueueDAO.class).in(Singleton.class);
        bindAsContract(CompositeOrderDAO.class).in(Singleton.class);
        bindAsContract(EsignTenantSignatoryDAO.class).in(Singleton.class);
        bindAsContract(EsignDAO.class).in(Singleton.class);
        bindAsContract(PlanGroupDAO.class).in(Singleton.class);
        bindAsContract(AutomatedInvoiceRuleDAO.class).in(Singleton.class);
        bindAsContract(DocumentLinkDAO.class).in(Singleton.class);
        bindAsContract(InvoicePaymentManagementLinkDAO.class).in(Singleton.class);
        bindAsContract(AccountPaymentManagementLinkDAO.class).in(Singleton.class);
        bindAsContract(AccountAutomaticPaymentMethodDAO.class).in(Singleton.class);
        bindAsContract(EscalationPolicyDAO.class).in(Singleton.class);
        bindAsContract(CLMDAO.class).in(Singleton.class);
        bindAsContract(PriceAttributeDAO.class).in(Singleton.class);
        bindAsContract(RateCardDAO.class).in(Singleton.class);
        bindAsContract(EntityDAO.class).in(Singleton.class);
        bindAsContract(EntityMigrationDAO.class).in(Singleton.class);
        bindAsContract(InvoiceConfigDAO.class).in(Singleton.class);
        bindAsContract(ChargeDefaultAttributeReferencesDAO.class);
        bindAsContract(SubscriptionChargeChangeScheduleDAO.class);
        bindAsContract(NotificationInstanceDAO.class);
        bindAsContract(TaxJarDAO.class).in(Singleton.class);
        bindAsContract(SlackApprovalMessageDetailDAO.class).in(Singleton.class);
        bindAsContract(LookerUserDAO.class).in(Singleton.class);
        bindAsContract(LookerTenantDAO.class).in(Singleton.class);
        bindAsContract(SearchIndexMappingDAO.class).in(Singleton.class);
        bindAsContract(ExternalArrScheduleDAO.class).in(Singleton.class);
        bindAsContract(ScheduledMaintenanceDAO.class).in(Singleton.class);
        bindAsContract(DocumentTemplateUpdatedOnOrderDAO.class).in(Singleton.class);
        bindAsContract(SalesRoomLinkDAO.class).in(Singleton.class);
        bindAsContract(IntelligentSalesRoomDAO.class).in(Singleton.class);
        bindAsContract(SearchReindexDAO.class).in(Singleton.class);
        bindAsContract(CustomizationDefinitionDAO.class).in(Singleton.class);
        bindAsContract(TransactionalMetricsReportingDAO.class).in(Singleton.class);
        bindAsContract(EntityAuthDAO.class).in(Singleton.class);
        bindAsContract(CurrencyTypeSettingDAO.class).in(Singleton.class);
        bindAsContract(CurrencyConversionRateDAO.class).in(Singleton.class);
        bindAsContract(TransactionalExchangeRateDAO.class).in(Singleton.class);
        bindAsContract(RealizedGainLossDAO.class).in(Singleton.class);
        bindAsContract(PaymentBankAccountDAO.class).in(Singleton.class);
        bindAsContract(MaintenanceMessageDAO.class).in(Singleton.class);
        bindAsContract(MaintenanceMessageAdminDAO.class).in(Singleton.class);
        bindAsContract(CrmFieldMappingDAO.class).in(Singleton.class);
        bindAsContract(CrmFieldMappingImportDAO.class).in(Singleton.class);
        bindAsContract(EmailSettingDAO.class).in(Singleton.class);
        bindAsContract(RevenueEnablementProgressDAO.class).in(Singleton.class);
        bindAsContract(BankTransactionDAO.class).in(Singleton.class);
        bindAsContract(QuestionStoreDAO.class).in(Singleton.class);
        bindAsContract(QscriptDAO.class).in(Singleton.class);
        bindAsContract(ApprovalFlowDataSnapshotDAO.class).in(Singleton.class);
        bindAsContract(TemplateScriptDAO.class).in(Singleton.class);
        bindAsContract(SlackWorkspaceInfoDAO.class).in(Singleton.class);
        bindAsContract(BulkRevenueDAO.class).in(Singleton.class);
        bindAsContract(BillingCycleDefinitionDAO.class).in(Singleton.class);
        bindAsContract(SubscriptionStatusChangeScheduleDAO.class).in(Singleton.class);
    }

    private void bindKinesisEventConsumers() {
        bindAsContract(NotificationEventConsumer.class).to(NotificationEventConsumer.class).in(Singleton.class);
        bindAsContract(ApprovalEventConsumer.class).to(NotificationEventConsumer.class).in(Singleton.class);
    }

    private void bindTaskQueuePlatform() {
        bindAsContract(DefaultDispatcher.class).to(TaskDispatcher.class).in(Singleton.class);
        bindAsContract(QueuedTaskMapper.class).to(QueuedTaskMapper.class).in(Singleton.class);
        bindAsContract(TaskRouter.class).to(TaskRouter.class).in(Singleton.class);
        bindAsContract(TaskResultHandler.class).to(TaskResultHandler.class).in(Singleton.class);
        bindAsContract(TaskExecutor.class).to(TaskExecutor.class).in(Singleton.class);
        bindAsContract(CapacityManager.class).to(CapacityManager.class).in(Singleton.class);
        bindAsContract(TaskScheduler.class).to(TaskScheduler.class).in(Singleton.class);
        bindAsContract(TaskSchedulerRunner.class).to(TaskSchedulerRunner.class).in(Singleton.class);
        bindAsContract(TaskCleaner.class).to(TaskCleaner.class).in(Singleton.class);
        if (billyConfig.getTaskQueueConfiguration().isUseQueue()) {
            bindAsContract(SqsBackedTaskDistributor.class).to(TaskDistributor.class).in(Singleton.class);
            bindAsContract(SqsTaskListener.class).to(TaskDistributor.class).in(Immediate.class);
        } else {
            bindAsContract(InProcessTaskDistributor.class).to(TaskDistributor.class).in(Singleton.class);
        }
        bindAsContract(TaskBackoffResultBuilder.class).to(TaskBackoffResultBuilder.class).in(Singleton.class);
        bindAsContract(TenantQueuedTaskService.class).to(TenantQueuedTaskService.class).in(Singleton.class);
        bindAsContract(TenantQueuedTaskExecutionService.class).to(TenantQueuedTaskService.class).in(Singleton.class);
        bindAsContract(QueuedTaskEventConsumer.class).to(QueuedTaskEventConsumer.class).in(Singleton.class);
        bindAsContract(TaskBridgeEventConsumer.class).to(TaskBridgeEventConsumer.class).in(Singleton.class);
        bindAsContract(QueuedTaskEventRouter.class).to(QueuedTaskEventRouter.class).in(Singleton.class);
        bindAsContract(TaskMetricPublisher.class).to(TaskMetricPublisher.class).in(Singleton.class);
        bindAsContract(QueuedTaskDbMaintenance.class).to(DatabaseMaintenance.class).in(Singleton.class);
        bindAsContract(EventToTaskBridge.class).in(Singleton.class);

        // DAOs
        bindAsContract(TaskExecutionDAO.class).to(TaskExecutionDAO.class).in(Singleton.class);
        bindAsContract(TenantTaskExecutionDAO.class).to(TenantTaskExecutionDAO.class).in(Singleton.class);
        bindAsContract(TaskDispatchDAO.class).to(TaskDispatchDAO.class).in(Singleton.class);
        bindAsContract(CapacityDAO.class).to(CapacityDAO.class).in(Singleton.class);
        bindAsContract(QueuedTaskDAO.class).to(QueuedTaskDAO.class).in(Singleton.class);
        bindAsContract(TaskMaintenanceDAO.class).to(TaskMaintenanceDAO.class).in(Singleton.class);
        bindAsContract(QueuedTaskSelectionDAO.class).to(TaskMaintenanceDAO.class).in(Singleton.class);
        bindAsContract(TenantQueuedTaskDAO.class).to(TenantQueuedTaskDAO.class).in(Singleton.class);

        // Event Listeners for tasks

        bindAsContract(TenantJobTaskCompletedListener.class).to(TaskCompletedListener.class).in(Singleton.class);
        bindAsContract(TenantJobTaskExecutingListener.class).to(TaskExecutingListener.class).in(Singleton.class);
    }

    private void bindTaskProcessors() {
        bindAsContract(StubTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(RevrecQueuedTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(SalesforceQueuedTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(HubspotQueuedTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(JournalQueuedTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(ErpQueuedTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(AutomatedInvoiceRuleTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(InvoiceEmailQueuedTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(ExchangeRateRefreshTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(TenantManagementTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(EventQueuedTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(IncomingWebhookRouter.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(SubscriptionLifecycleTaskProcessor.class).to(TaskProcessor.class).to(EventSourcedTaskFactory.class).in(Singleton.class);
        bindAsContract(IntelligentSalesRoomTaskProcessor.class).to(TaskProcessor.class).to(EventSourcedTaskFactory.class).in(Singleton.class);
        bindAsContract(PaymentRetryQueuedTaskProcessor.class).to(TaskProcessor.class).in(Singleton.class);
        bindAsContract(SubscriptionChargeChangeTaskBackfiller.class)
            .to(TaskProcessor.class)
            .to(SubscriptionChargeChangeTaskBackfiller.class)
            .in(Singleton.class);
        bindAsContract(SubscriptionStatusChangeTaskBackfiller.class)
            .to(TaskProcessor.class)
            .to(SubscriptionStatusChangeTaskBackfiller.class)
            .in(Singleton.class);
    }
}
