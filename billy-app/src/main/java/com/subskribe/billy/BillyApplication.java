package com.subskribe.billy;

import com.subskribe.billy.auth.apikey.ApiKeyRsaKeyPairProvider;
import com.subskribe.billy.auth.apikey.ApiKeyValidator;
import com.subskribe.billy.auth.apikey.db.ApiKeyDAO;
import com.subskribe.billy.auth.authenticators.ApiKeyAuthenticator;
import com.subskribe.billy.auth.authenticators.JWTTokenAuthenticator;
import com.subskribe.billy.auth.authorizers.ApiKeyAuthorizer;
import com.subskribe.billy.auth.authorizers.JWTTokenAuthorizer;
import com.subskribe.billy.auth.db.AuthTenantCognitoDAO;
import com.subskribe.billy.auth.filters.ApiKeyAuthFilter;
import com.subskribe.billy.auth.filters.AuthResetResponseFilter;
import com.subskribe.billy.auth.filters.ClientIdAwareOAuthCredentialAuthFilter;
import com.subskribe.billy.auth.jwt.JWTTokenValidator;
import com.subskribe.billy.auth.model.ApiKeyJwtToken;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.ClientIdJwtTokens;
import com.subskribe.billy.auth.services.TenantCognitoService;
import com.subskribe.billy.auth.services.TenantRlsEncryptionService;
import com.subskribe.billy.aws.localstack.LocalStackCommand;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.aws.secretsmanager.model.DatabaseSecret;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.MemcachedClientProvider;
import com.subskribe.billy.di.hk2.BillyBinder;
import com.subskribe.billy.di.hk2.feature.ImmediateFeature;
import com.subskribe.billy.di.hk2.feature.InjectionAssist;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.di.hk2.providers.TenantDbCredentialProvider;
import com.subskribe.billy.entity.db.EntityAuthDAO;
import com.subskribe.billy.entity.service.EntityAuthContextResolver;
import com.subskribe.billy.exception.mappers.AddressValidationExceptionMapper;
import com.subskribe.billy.exception.mappers.ConflictingStateExceptionMapper;
import com.subskribe.billy.exception.mappers.DocuSignRefreshTokenExpiredExceptionMapper;
import com.subskribe.billy.exception.mappers.DuplicateObjectExceptionMapper;
import com.subskribe.billy.exception.mappers.EntityNotFoundExceptionMapper;
import com.subskribe.billy.exception.mappers.IOExceptionMapper;
import com.subskribe.billy.exception.mappers.IllegalArgumentExceptionMapper;
import com.subskribe.billy.exception.mappers.IllegalStateExceptionMapper;
import com.subskribe.billy.exception.mappers.InvalidInputExceptionMapper;
import com.subskribe.billy.exception.mappers.InvariantCheckFailedExceptionMapper;
import com.subskribe.billy.exception.mappers.InvoiceAlreadyExistsExceptionMapper;
import com.subskribe.billy.exception.mappers.ModelValidationExceptionMapper;
import com.subskribe.billy.exception.mappers.NegativeInvoicePostExceptionMapper;
import com.subskribe.billy.exception.mappers.RateLimitExceededExceptionMapper;
import com.subskribe.billy.exception.mappers.RuntimeExceptionMapper;
import com.subskribe.billy.exception.mappers.ServiceFailureExceptionMapper;
import com.subskribe.billy.exception.mappers.ServiceUnavailableExceptionMapper;
import com.subskribe.billy.exception.mappers.TemplateRenderExceptionMapper;
import com.subskribe.billy.exception.mappers.TenantNotFoundExceptionMapper;
import com.subskribe.billy.exception.mappers.UnknownOperationExceptionMapper;
import com.subskribe.billy.exception.mappers.UnsupportedOperationExceptionMapper;
import com.subskribe.billy.exception.mappers.UserMessageExceptionMapper;
import com.subskribe.billy.exception.mappers.WebhookValidationFailedExceptionMapper;
import com.subskribe.billy.filter.google.GoogleIdTokenFilter;
import com.subskribe.billy.filter.http.DosFilterConfiguration;
import com.subskribe.billy.filter.http.DosFilterRegistrar;
import com.subskribe.billy.filter.http.LargeRequestLoggingFilter;
import com.subskribe.billy.filter.logger.LoggerMetadataFilter;
import com.subskribe.billy.filter.logger.RequestHeaderLoggerFilter;
import com.subskribe.billy.filter.logger.TestLoggerFilter;
import com.subskribe.billy.health.BillyHealthCheck;
import com.subskribe.billy.idempotency.filters.IdempotencyRequestResponseFilter;
import com.subskribe.billy.infra.maintenance.filter.MaintenanceFilter;
import com.subskribe.billy.legal.TermsAndConditionsFilter;
import com.subskribe.billy.log.RequestResponseLogFilter;
import com.subskribe.billy.ratelimiter.filter.RateLimiterFilter;
import com.subskribe.billy.resources.AccountReceivableContactResource;
import com.subskribe.billy.resources.AccountResource;
import com.subskribe.billy.resources.AccountingPeriodResource;
import com.subskribe.billy.resources.AiResource;
import com.subskribe.billy.resources.AliasResource;
import com.subskribe.billy.resources.AnrokResource;
import com.subskribe.billy.resources.ApiKeyResource;
import com.subskribe.billy.resources.ApprovalFlowNotificationResource;
import com.subskribe.billy.resources.ApprovalFlowResource;
import com.subskribe.billy.resources.ApprovalMatrixResource;
import com.subskribe.billy.resources.ApprovalRoleResource;
import com.subskribe.billy.resources.ApprovalSegmentResource;
import com.subskribe.billy.resources.AttachmentsResource;
import com.subskribe.billy.resources.AuthResource;
import com.subskribe.billy.resources.AutomatedInvoiceRuleResource;
import com.subskribe.billy.resources.AvalaraResource;
import com.subskribe.billy.resources.BankTransactionsResource;
import com.subskribe.billy.resources.ChargeResource;
import com.subskribe.billy.resources.CompositeOrderResource;
import com.subskribe.billy.resources.CreditMemoResource;
import com.subskribe.billy.resources.CrmFieldMappingResource;
import com.subskribe.billy.resources.CrmResource;
import com.subskribe.billy.resources.CustomFieldDefinitionResource;
import com.subskribe.billy.resources.CustomFieldResource;
import com.subskribe.billy.resources.CustomizationResource;
import com.subskribe.billy.resources.DemoResource;
import com.subskribe.billy.resources.DiscountResource;
import com.subskribe.billy.resources.DocuSignResource;
import com.subskribe.billy.resources.DocumentLinkResource;
import com.subskribe.billy.resources.DocumentMasterTemplateResource;
import com.subskribe.billy.resources.DocumentTemplateResource;
import com.subskribe.billy.resources.DunningResource;
import com.subskribe.billy.resources.DunningSettingResource;
import com.subskribe.billy.resources.EmailSettingResource;
import com.subskribe.billy.resources.EntityResource;
import com.subskribe.billy.resources.ErpSyncResource;
import com.subskribe.billy.resources.EsignResource;
import com.subskribe.billy.resources.GqlResource;
import com.subskribe.billy.resources.GuidedSellingResource;
import com.subskribe.billy.resources.HealthResource;
import com.subskribe.billy.resources.HubSpotResource;
import com.subskribe.billy.resources.ImportResource;
import com.subskribe.billy.resources.IntegrationAuthResource;
import com.subskribe.billy.resources.IntelligentSalesRoomResource;
import com.subskribe.billy.resources.InvoiceResource;
import com.subskribe.billy.resources.InvoiceSettlementResource;
import com.subskribe.billy.resources.JournalEntryResource;
import com.subskribe.billy.resources.LedgerAccountingResource;
import com.subskribe.billy.resources.LocalAdminResource;
import com.subskribe.billy.resources.MaintenanceMessageResource;
import com.subskribe.billy.resources.MetricsReportingResource;
import com.subskribe.billy.resources.NotificationResource;
import com.subskribe.billy.resources.OpportunityResource;
import com.subskribe.billy.resources.OrderResource;
import com.subskribe.billy.resources.PaymentResource;
import com.subskribe.billy.resources.PlanResource;
import com.subskribe.billy.resources.PlatformFeatureResource;
import com.subskribe.billy.resources.PredefinedTermSectionResource;
import com.subskribe.billy.resources.ProductCategoryResource;
import com.subskribe.billy.resources.ProductResource;
import com.subskribe.billy.resources.RateCardResource;
import com.subskribe.billy.resources.RefundResource;
import com.subskribe.billy.resources.ReportResource;
import com.subskribe.billy.resources.RevenueEnablementResource;
import com.subskribe.billy.resources.RevenueRecognitionResource;
import com.subskribe.billy.resources.SalesRoomResource;
import com.subskribe.billy.resources.SalesforceResource;
import com.subskribe.billy.resources.SearchResource;
import com.subskribe.billy.resources.SlackQuoteBuilderResource;
import com.subskribe.billy.resources.StripeImportResource;
import com.subskribe.billy.resources.SubscriptionResource;
import com.subskribe.billy.resources.TaxJarResource;
import com.subskribe.billy.resources.TaxRateResource;
import com.subskribe.billy.resources.TemplateScriptResource;
import com.subskribe.billy.resources.TenantJobResource;
import com.subskribe.billy.resources.TenantResource;
import com.subskribe.billy.resources.TenantSettingResource;
import com.subskribe.billy.resources.TransactionalFxResource;
import com.subskribe.billy.resources.UnitOfMeasureResource;
import com.subskribe.billy.resources.UsageResource;
import com.subskribe.billy.resources.UserGroupResource;
import com.subskribe.billy.resources.UserResource;
import com.subskribe.billy.resources.VersionResource;
import com.subskribe.billy.resources.admin.AdminResource;
import com.subskribe.billy.resources.admin.ApiKeyAdminResource;
import com.subskribe.billy.resources.admin.MaintenanceAdminResource;
import com.subskribe.billy.resources.admin.MaintenanceMessageAdminResource;
import com.subskribe.billy.resources.admin.SearchAdminResource;
import com.subskribe.billy.resources.admin.TaskAdminResource;
import com.subskribe.billy.resources.admin.TenantAdminResource;
import com.subskribe.billy.resources.admin.TenantJobAdminResource;
import com.subskribe.billy.resources.shared.PromptResource;
import com.subskribe.billy.scheduler.QuartzApprovalFlowEscalationJobFeature;
import com.subskribe.billy.scheduler.QuartzExchangeRateRefreshFeature;
import com.subskribe.billy.scheduler.QuartzReportingFeature;
import com.subskribe.billy.scheduler.accounting.QuartzAccountingInvariantsFeature;
import com.subskribe.billy.scheduler.admin.QuartzBillyAdminReporterFeature;
import com.subskribe.billy.scheduler.auth.QuartzApiKeyGarbageCollectorJobFeature;
import com.subskribe.billy.scheduler.context.QuartzJobIdFeature;
import com.subskribe.billy.scheduler.dunning.QuartzDunningFeature;
import com.subskribe.billy.scheduler.entity.QuartzEntityInvariantsFeature;
import com.subskribe.billy.scheduler.erp.QuartzQuickbooksPingFeature;
import com.subskribe.billy.scheduler.invoice.QuartzBulkInvoiceRunFeature;
import com.subskribe.billy.scheduler.invoice.QuartzInvoiceGeneratorFeature;
import com.subskribe.billy.scheduler.looker.LookerTenantConfigExportFeature;
import com.subskribe.billy.scheduler.metrics.QuartzMetricsUpdaterFeature;
import com.subskribe.billy.scheduler.notification.QuartzNotificationRetryFeature;
import com.subskribe.billy.scheduler.order.QuartzOrderExpiryFeature;
import com.subskribe.billy.scheduler.payment.QuartzPaymentProcessorFeature;
import com.subskribe.billy.scheduler.payment.QuartzPaymentReconcilerFeature;
import com.subskribe.billy.scheduler.queue.MonitoringFeature;
import com.subskribe.billy.scheduler.queue.QuartzQueuePollerFeature;
import com.subskribe.billy.scheduler.revrec.QuartzBulkRevenueRecognitionFeature;
import com.subskribe.billy.scheduler.revrec.QuartzRevenueRecognitionFeature;
import com.subskribe.billy.scheduler.usage.QuartzUsageAggregationFeature;
import com.subskribe.billy.search.SearchCommand;
import com.subskribe.billy.shared.bundles.BillyFlywayBundle;
import com.subskribe.billy.shared.bundles.BillySwaggerBundle;
import com.subskribe.billy.shared.bundles.TypeSafeConfigurationBundle;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.modules.InstantModule;
import com.subskribe.billy.shared.modules.TimeZoneModule;
import com.subskribe.billy.tenant.db.TenantNameDAO;
import com.subskribe.billy.tenant.services.TenantNameCache;
import com.subskribe.billy.tracing.RequestIdFilter;
import com.subskribe.billy.tracing.RequestIdResponseFilter;
import com.subskribe.billy.tracing.UriInfoProvider;
import io.dropwizard.Application;
import io.dropwizard.assets.AssetsBundle;
import io.dropwizard.auth.AuthDynamicFeature;
import io.dropwizard.auth.AuthFilter;
import io.dropwizard.auth.AuthValueFactoryProvider;
import io.dropwizard.auth.chained.ChainedAuthFilter;
import io.dropwizard.forms.MultiPartBundle;
import io.dropwizard.jersey.jackson.JsonProcessingExceptionMapper;
import io.dropwizard.setup.Bootstrap;
import io.dropwizard.setup.Environment;
import java.io.IOException;
import java.util.EnumSet;
import java.util.List;
import javax.servlet.DispatcherType;
import javax.servlet.FilterRegistration;
import javax.ws.rs.Path;
import org.eclipse.jetty.servlets.CrossOriginFilter;
import org.glassfish.jersey.server.filter.RolesAllowedDynamicFeature;
import org.jooq.DSLContext;

public class BillyApplication extends Application<BillyConfiguration> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BillyApplication.class);

    public static void main(String[] args) throws Exception {
        setAwsSdkSystemProps();
        new BillyApplication().run(args);
    }

    public static void setAwsSdkSystemProps() {
        // Set the default synchronous HTTP client to ApacheHttpClient
        // we need to set this to resolve the correct SDK while starting
        System.setProperty("software.amazon.awssdk.http.service.impl", "software.amazon.awssdk.http.apache.ApacheSdkHttpService");
    }

    @Override
    public String getName() {
        return "billy";
    }

    @SuppressWarnings("rawtypes,unchecked")
    @Override
    public void run(BillyConfiguration configuration, Environment environment) throws IOException {
        // first thing we do is instantiate the secret service for various resource dependencies
        SecretsService secretsService = new SecretsService(configuration);
        setDataSourceCredentials(configuration, secretsService);
        configuration.getDataSourceFactory().setDbcpConfiguration(configuration.getDbcpConfiguration());
        configuration.getReadOnlyDataSourceFactory().setDbcpConfiguration(configuration.getDbcpReadOnlyConfiguration());

        TenantRlsEncryptionService tenantRlsEncryptionService = new TenantRlsEncryptionService(secretsService);

        environment.jersey().getResourceConfig().register(ImmediateFeature.class);
        DSLContextProvider dslContextProvider = createDslContextProvider(configuration, environment, tenantRlsEncryptionService);
        environment.jersey().register(new BillyBinder(configuration, environment, secretsService, dslContextProvider, tenantRlsEncryptionService));
        environment.jersey().getResourceConfig().register(InjectionAssist.class);

        registerResources(environment, isLocal(configuration));
        registerExceptionMappers(environment);

        environment.healthChecks().register("billy", new BillyHealthCheck());

        List<AuthFilter> filters = getChainedListOfAuthFilters(dslContextProvider, configuration, secretsService);

        environment.lifecycle().manage(dslContextProvider.getManagedDatasource());

        environment.jersey().register(new AuthDynamicFeature(new ChainedAuthFilter(filters)));
        environment.jersey().register(new AuthValueFactoryProvider.Binder<>(BillyAuthPrincipal.class));
        environment.jersey().getResourceConfig().register(QuartzJobIdFeature.class);
        environment.jersey().getResourceConfig().register(QuartzQueuePollerFeature.class);
        environment.jersey().getResourceConfig().register(MonitoringFeature.class);
        environment.jersey().getResourceConfig().register(QuartzInvoiceGeneratorFeature.class);
        environment.jersey().getResourceConfig().register(QuartzDunningFeature.class);
        environment.jersey().getResourceConfig().register(QuartzUsageAggregationFeature.class);
        environment.jersey().getResourceConfig().register(QuartzPaymentProcessorFeature.class);
        environment.jersey().getResourceConfig().register(QuartzPaymentReconcilerFeature.class);
        environment.jersey().getResourceConfig().register(QuartzBulkInvoiceRunFeature.class);
        environment.jersey().getResourceConfig().register(QuartzMetricsUpdaterFeature.class);
        environment.jersey().getResourceConfig().register(QuartzRevenueRecognitionFeature.class);
        environment.jersey().getResourceConfig().register(QuartzBillyAdminReporterFeature.class);
        environment.jersey().getResourceConfig().register(QuartzReportingFeature.class);
        environment.jersey().getResourceConfig().register(QuartzApprovalFlowEscalationJobFeature.class);
        environment.jersey().getResourceConfig().register(QuartzApiKeyGarbageCollectorJobFeature.class);
        environment.jersey().getResourceConfig().register(QuartzOrderExpiryFeature.class);
        environment.jersey().getResourceConfig().register(QuartzNotificationRetryFeature.class);
        environment.jersey().getResourceConfig().register(QuartzEntityInvariantsFeature.class);
        environment.jersey().getResourceConfig().register(QuartzAccountingInvariantsFeature.class);
        environment.jersey().getResourceConfig().register(QuartzExchangeRateRefreshFeature.class);
        environment.jersey().getResourceConfig().register(QuartzQuickbooksPingFeature.class);
        environment.jersey().getResourceConfig().register(QuartzBulkRevenueRecognitionFeature.class);
        environment.jersey().getResourceConfig().register(LookerTenantConfigExportFeature.class);
        environment.jersey().register(RolesAllowedDynamicFeature.class);
        environment.jersey().register(AuthResetResponseFilter.class);
        environment.jersey().register(RequestIdFilter.class);
        environment.jersey().register(RequestIdResponseFilter.class);
        environment.jersey().register(RateLimiterFilter.class);
        environment.jersey().register(TermsAndConditionsFilter.class);
        environment.jersey().register(IdempotencyRequestResponseFilter.class);
        environment.jersey().register(RequestHeaderLoggerFilter.class);
        environment.jersey().register(LoggerMetadataFilter.class);
        environment.jersey().register(RequestResponseLogFilter.class);
        environment.jersey().register(LargeRequestLoggingFilter.class);
        environment.jersey().register(UriInfoProvider.class);
        environment.jersey().register(GoogleIdTokenFilter.class);
        environment.jersey().register(MaintenanceFilter.class);
        if (configuration.getRequestHeaderLoggerConfiguration().getLogTestId()) {
            environment.jersey().register(TestLoggerFilter.class);
        }

        environment.jersey().register(new JsonProcessingExceptionMapper(true));

        registerCors(environment);
        registerDosFilter(environment, configuration);

        setupTenantNameCache(dslContextProvider, configuration);
    }

    private void setDataSourceCredentials(BillyConfiguration configuration, SecretsService secretsService) {
        DatabaseSecret databaseSecret = secretsService.getDatabaseSecret();
        configuration.getDbcpConfiguration().setUsername(databaseSecret.getUsername());
        configuration.getDbcpConfiguration().setPassword(databaseSecret.getPassword());
        configuration.getDbcpReadOnlyConfiguration().setUsername(databaseSecret.getUsername());
        configuration.getDbcpReadOnlyConfiguration().setPassword(databaseSecret.getPassword());
    }

    private void registerResources(Environment environment, boolean isLocal) {
        // admin resources
        environment.jersey().register(ApiKeyAdminResource.class);
        environment.jersey().register(SearchAdminResource.class);
        environment.jersey().register(TenantAdminResource.class);
        environment.jersey().register(AdminResource.class);
        environment.jersey().register(MaintenanceAdminResource.class);
        environment.jersey().register(TaskAdminResource.class);
        environment.jersey().register(MaintenanceMessageAdminResource.class);
        if (isLocal) {
            environment.jersey().register(LocalAdminResource.class);
        }

        // demo resources
        environment.jersey().register(DemoResource.class);

        environment.jersey().register(EntityResource.class);
        environment.jersey().register(TransactionalFxResource.class);
        environment.jersey().register(AccountResource.class);
        environment.jersey().register(AttachmentsResource.class);
        environment.jersey().register(OrderResource.class);
        environment.jersey().register(SubscriptionResource.class);
        environment.jersey().register(ProductResource.class);
        environment.jersey().register(ProductCategoryResource.class);
        environment.jersey().register(PlanResource.class);
        environment.jersey().register(ChargeResource.class);
        environment.jersey().register(ApiKeyResource.class);
        environment.jersey().register(UserResource.class);
        environment.jersey().register(OpportunityResource.class);
        environment.jersey().register(TenantResource.class);
        environment.jersey().register(InvoiceResource.class);
        environment.jersey().register(PaymentResource.class);
        environment.jersey().register(TaxRateResource.class);
        environment.jersey().register(GqlResource.class);
        environment.jersey().register(InvoiceSettlementResource.class);
        environment.jersey().register(UnitOfMeasureResource.class);
        environment.jersey().register(SearchResource.class);
        environment.jersey().register(TenantSettingResource.class);
        environment.jersey().register(CreditMemoResource.class);
        environment.jersey().register(SalesforceResource.class);
        environment.jersey().register(NotificationResource.class);
        environment.jersey().register(AvalaraResource.class);
        environment.jersey().register(ImportResource.class);
        environment.jersey().register(DocumentTemplateResource.class);
        environment.jersey().register(PredefinedTermSectionResource.class);
        environment.jersey().register(DocumentMasterTemplateResource.class);
        environment.jersey().register(AuthResource.class);
        environment.jersey().register(DocuSignResource.class);
        environment.jersey().register(ReportResource.class);
        environment.jersey().register(VersionResource.class);
        environment.jersey().register(DunningSettingResource.class);
        environment.jersey().register(DunningResource.class);
        environment.jersey().register(AccountReceivableContactResource.class);
        environment.jersey().register(DiscountResource.class);
        environment.jersey().register(AliasResource.class);
        environment.jersey().register(UsageResource.class);
        environment.jersey().register(UserGroupResource.class);
        environment.jersey().register(RevenueRecognitionResource.class);
        environment.jersey().register(HealthResource.class);
        environment.jersey().register(AccountingPeriodResource.class);
        environment.jersey().register(RefundResource.class);
        environment.jersey().register(ApprovalRoleResource.class);
        environment.jersey().register(ApprovalSegmentResource.class);
        environment.jersey().register(LedgerAccountingResource.class);
        environment.jersey().register(ApprovalMatrixResource.class);
        environment.jersey().register(PlatformFeatureResource.class);
        environment.jersey().register(HubSpotResource.class);
        environment.jersey().register(JournalEntryResource.class);
        environment.jersey().register(IntegrationAuthResource.class);
        environment.jersey().register(ErpSyncResource.class);
        environment.jersey().register(CustomFieldDefinitionResource.class);
        environment.jersey().register(CustomFieldResource.class);
        environment.jersey().register(EsignResource.class);
        environment.jersey().register(CompositeOrderResource.class);
        environment.jersey().register(AutomatedInvoiceRuleResource.class);
        environment.jersey().register(DocumentLinkResource.class);
        environment.jersey().register(TenantJobResource.class);
        environment.jersey().register(TenantJobAdminResource.class);
        environment.jersey().register(StripeImportResource.class);
        environment.jersey().register(TaxJarResource.class);
        environment.jersey().register(ApprovalFlowNotificationResource.class);
        environment.jersey().register(MetricsReportingResource.class);
        environment.jersey().register(SalesRoomResource.class);
        environment.jersey().register(CustomizationResource.class);
        environment.jersey().register(AnrokResource.class);
        environment.jersey().register(RateCardResource.class);
        environment.jersey().register(ApprovalFlowResource.class);
        environment.jersey().register(MaintenanceMessageResource.class);
        environment.jersey().register(RevenueEnablementResource.class);
        environment.jersey().register(CrmFieldMappingResource.class);
        environment.jersey().register(EmailSettingResource.class);
        environment.jersey().register(BankTransactionsResource.class);
        environment.jersey().register(GuidedSellingResource.class);
        environment.jersey().register(CrmResource.class);
        environment.jersey().register(TemplateScriptResource.class);
        environment.jersey().register(IntelligentSalesRoomResource.class);

        // AI related resources
        environment.jersey().register(AiResource.class);
        environment.jersey().register(PromptResource.class);
        environment.jersey().register(SlackQuoteBuilderResource.class);
    }

    private void registerExceptionMappers(Environment environment) {
        environment.jersey().register(new TenantNotFoundExceptionMapper());
        environment.jersey().register(new IllegalArgumentExceptionMapper());
        environment.jersey().register(new DuplicateObjectExceptionMapper());
        environment.jersey().register(new UserMessageExceptionMapper());
        environment.jersey().register(new IllegalStateExceptionMapper());
        environment.jersey().register(new UnsupportedOperationExceptionMapper());
        environment.jersey().register(new ServiceFailureExceptionMapper());
        environment.jersey().register(new InvariantCheckFailedExceptionMapper());
        environment.jersey().register(new ServiceUnavailableExceptionMapper());
        environment.jersey().register(new ModelValidationExceptionMapper());
        environment.jersey().register(new InvoiceAlreadyExistsExceptionMapper());
        environment.jersey().register(new IOExceptionMapper());
        environment.jersey().register(new DocuSignRefreshTokenExpiredExceptionMapper());
        environment.jersey().register(new ConflictingStateExceptionMapper());
        environment.jersey().register(new NegativeInvoicePostExceptionMapper());
        environment.jersey().register(new InvalidInputExceptionMapper());
        environment.jersey().register(new TemplateRenderExceptionMapper());
        environment.jersey().register(new EntityNotFoundExceptionMapper());
        environment.jersey().register(new RateLimitExceededExceptionMapper());
        environment.jersey().register(new UnknownOperationExceptionMapper());
        environment.jersey().register(new WebhookValidationFailedExceptionMapper());
        environment.jersey().register(new AddressValidationExceptionMapper());
        environment.jersey().register(new RuntimeExceptionMapper());
    }

    @AllowNonRlsDataAccess
    @SuppressWarnings("rawtypes")
    private List<AuthFilter> getChainedListOfAuthFilters(
        DSLContextProvider dslContextProvider,
        BillyConfiguration configuration,
        SecretsService secretsService
    ) {
        ApiKeyRsaKeyPairProvider apiKeyRsaKeyPairProvider = new ApiKeyRsaKeyPairProvider(secretsService);
        ApiKeyDAO apiKeyDAO = new ApiKeyDAO(dslContextProvider);
        EntityAuthDAO entityAuthDAO = new EntityAuthDAO(dslContextProvider);
        EntityAuthContextResolver entityAuthContextResolver = new EntityAuthContextResolver(
            dslContextProvider,
            getInstanceOfCacheService(configuration),
            entityAuthDAO
        );
        ApiKeyValidator apiKeyValidator = new ApiKeyValidator(secretsService, apiKeyRsaKeyPairProvider, apiKeyDAO, entityAuthContextResolver);
        ApiKeyAuthenticator apiKeyAuthenticator = new ApiKeyAuthenticator(apiKeyValidator, dslContextProvider, configuration);
        AuthFilter<ApiKeyJwtToken, BillyAuthPrincipal> apiKeyAuthFilter = new ApiKeyAuthFilter.Builder()
            .setAuthenticator(apiKeyAuthenticator)
            .setAuthorizer(new ApiKeyAuthorizer())
            .setPrefix("ApiKey")
            .buildAuthFilter();

        return List.of(apiKeyAuthFilter, getOAuthFilter(dslContextProvider, configuration, entityAuthContextResolver));
    }

    private AuthFilter<ClientIdJwtTokens, BillyAuthPrincipal> getOAuthFilter(
        DSLContextProvider dslContextProvider,
        BillyConfiguration configuration,
        EntityAuthContextResolver entityAuthContextResolver
    ) {
        return new ClientIdAwareOAuthCredentialAuthFilter.Builder()
            .setAuthenticator(new JWTTokenAuthenticator(getJWTTokenValidator(dslContextProvider, configuration, entityAuthContextResolver)))
            .setAuthorizer(new JWTTokenAuthorizer())
            .setPrefix("Bearer")
            .buildAuthFilter();
    }

    @AllowNonRlsDataAccess
    private JWTTokenValidator getJWTTokenValidator(
        DSLContextProvider dslContextProvider,
        BillyConfiguration configuration,
        EntityAuthContextResolver entityAuthContextResolver
    ) {
        DSLContext dslContext = dslContextProvider.get();
        AuthTenantCognitoDAO authTenantCognitoDAO = new AuthTenantCognitoDAO();
        TenantCognitoService tenantCognitoService = new TenantCognitoService(authTenantCognitoDAO, dslContext);
        CacheService cacheService = getInstanceOfCacheService(configuration);
        return new JWTTokenValidator(
            configuration.getCognitoConfiguration().getRegion(),
            tenantCognitoService,
            dslContext,
            cacheService,
            entityAuthContextResolver
        );
    }

    private CacheService getInstanceOfCacheService(BillyConfiguration configuration) {
        MemcachedClientProvider memcachedClientProvider = new MemcachedClientProvider(configuration);
        return new CacheService(memcachedClientProvider);
    }

    private DSLContextProvider createDslContextProvider(
        BillyConfiguration configuration,
        Environment environment,
        TenantRlsEncryptionService tenantRlsEncryptionService
    ) {
        return new DSLContextProvider(
            environment,
            configuration,
            BillyBinder.DATASOURCE_NAME,
            new TenantDbCredentialProvider(tenantRlsEncryptionService)
        );
    }

    private void registerCors(Environment environment) {
        FilterRegistration.Dynamic cors = environment.servlets().addFilter("CORS", CrossOriginFilter.class);
        cors.setInitParameter(CrossOriginFilter.ALLOWED_ORIGINS_PARAM, "*");
        String customAllowHeaders = String.join(",", ClientIdAwareOAuthCredentialAuthFilter.ID_TOKEN_HEADER);
        String allowedHeaders = "X-Requested-With,Content-Type,Accept,Origin," + customAllowHeaders;
        cors.setInitParameter(CrossOriginFilter.ALLOWED_HEADERS_PARAM, allowedHeaders);
        cors.setInitParameter(CrossOriginFilter.ALLOWED_METHODS_PARAM, "OPTIONS,POST");

        String graphqlPath = GqlResource.class.getAnnotation(Path.class).value();
        cors.addMappingForUrlPatterns(EnumSet.allOf(DispatcherType.class), true, graphqlPath);
        cors.setInitParameter(CrossOriginFilter.CHAIN_PREFLIGHT_PARAM, Boolean.FALSE.toString());
    }

    private void registerDosFilter(Environment environment, BillyConfiguration configuration) {
        DosFilterConfiguration dosFilterConfiguration = configuration.getDosFilterConfiguration();
        if (dosFilterConfiguration.getEnabled()) {
            LOGGER.info("DoS filter is enabled, registering...");
            DosFilterRegistrar.registerWithConfig(environment, dosFilterConfiguration);
        }
    }

    @Override
    public void initialize(Bootstrap<BillyConfiguration> bootstrap) {
        bootstrap.addBundle(new TypeSafeConfigurationBundle());
        bootstrap.addBundle(new BillySwaggerBundle());
        bootstrap.addBundle(new BillyFlywayBundle());
        bootstrap.addBundle(new AssetsBundle("/assets", "/graphql-playground", "index.htm", "graphql-playground"));
        bootstrap.addBundle(new MultiPartBundle());
        bootstrap.getObjectMapper().registerModule(new InstantModule());
        bootstrap.getObjectMapper().registerModule(new TimeZoneModule());
        bootstrap.addCommand(new SearchCommand());
        bootstrap.addCommand(new LocalStackCommand());
    }

    private boolean isLocal(BillyConfiguration billyConfiguration) {
        return billyConfiguration.isLocalOrCi();
    }

    @AllowNonRlsDataAccess
    private void setupTenantNameCache(DSLContextProvider dslContextProvider, BillyConfiguration billyConfiguration) {
        DSLContext dslContext = dslContextProvider.get();
        TenantNameDAO tenantNameDAO = new TenantNameDAO(dslContext);
        TenantNameCache.initialize(tenantNameDAO, billyConfiguration);
    }
}
