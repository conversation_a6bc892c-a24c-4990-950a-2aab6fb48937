package com.subskribe.billy.order.document;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.document.model.DocumentConfiguration;
import com.subskribe.billy.document.model.DocumentType;
import com.subskribe.billy.document.model.GeneratedDocument;
import com.subskribe.billy.document.service.DocumentService;
import com.subskribe.billy.docusign.service.DocuSignAuthService;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.esign.model.ElectronicSignatureProvider;
import com.subskribe.billy.esign.model.EsignDocumentTags;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.order.OrderDataAggregator;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.order.OrderDetailsMapper;
import com.subskribe.billy.graphql.order.OrderDocumentTemplate;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountDetail;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.graphql.template.DocumentTemplateResponse;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.payment.model.AccountPaymentManagementLink;
import com.subskribe.billy.payment.services.AccountPaymentManagementLinkService;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.order.OrderLineItemJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.resources.json.product.ProductMapper;
import com.subskribe.billy.shared.SubskribeUrlGenerator;
import com.subskribe.billy.shared.document.DocumentArrProjection;
import com.subskribe.billy.shared.document.DocumentDataAggregator;
import com.subskribe.billy.shared.document.TemplateAccount;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.enums.SigningOrder;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.template.model.DocumentCustomContent;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.model.DocumentMasterTemplateConfiguration;
import com.subskribe.billy.template.model.DocumentSection;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import com.subskribe.billy.tenant.model.TenantInfo;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.SortedMap;
import java.util.SortedSet;
import java.util.TimeZone;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class OrderDocumentDataAggregator {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderDocumentDataAggregator.class);

    private static final ProductMapper PRODUCT_MAPPER = Mappers.getMapper(ProductMapper.class);

    private static final String DOCUMENT_FILE_NAME_FORMAT = "%s/%s.pdf";
    private static final int ONE_YEAR_IN_SECONDS = 365 * 24 * 60 * 60;

    private final OrderDataAggregator orderDataAggregator;
    private final TenantService tenantService;
    private final TenantIdProvider tenantIdProvider;
    private final DocumentConfiguration documentConfiguration;
    private final DocumentService documentService;
    private final ProductCatalogGetService productCatalogGetService;
    private final InvoiceService invoiceService;
    private final BillyConfiguration billyConfiguration;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final SubscriptionGetService subscriptionGetService;
    private final MetricsService metricsService;
    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;
    private final OrderDetailsMapper orderDetailsMapper;
    private final CustomFieldService customFieldService;
    private final PlatformFeatureService platformFeatureService;
    private final TenantSettingService tenantSettingService;
    private final EntityGetService entityGetService;
    private final DocuSignAuthService docuSignAuthService;
    private final AccountPaymentManagementLinkService accountPaymentManagementLinkService;
    private final RateCardService rateCardService;
    private final FeatureService featureService;
    private final AccountGetService accountGetService;
    private final OrderGetService orderGetService;
    private final OpportunityGetService opportunityGetService;

    @Inject
    public OrderDocumentDataAggregator(
        OrderDataAggregator orderDataAggregator,
        TenantService tenantService,
        TenantIdProvider tenantIdProvider,
        DocumentService documentService,
        ProductCatalogGetService productCatalogGetService,
        InvoiceService invoiceService,
        BillyConfiguration billyConfiguration,
        DocumentTemplateGetService documentTemplateGetService,
        SubscriptionGetService subscriptionGetService,
        MetricsService metricsService,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        CustomFieldService customFieldService,
        PlatformFeatureService platformFeatureService,
        TenantSettingService tenantSettingService,
        EntityGetService entityGetService,
        DocuSignAuthService docuSignAuthService,
        AccountPaymentManagementLinkService accountPaymentManagementLinkService,
        RateCardService rateCardService,
        FeatureService featureService,
        AccountGetService accountGetService,
        OrderGetService orderGetService,
        OpportunityGetService opportunityGetService
    ) {
        documentConfiguration = billyConfiguration.getDocumentConfiguration();
        this.orderDataAggregator = orderDataAggregator;
        this.tenantService = tenantService;
        this.tenantIdProvider = tenantIdProvider;
        this.documentService = documentService;
        this.productCatalogGetService = productCatalogGetService;
        this.invoiceService = invoiceService;
        this.billyConfiguration = billyConfiguration;
        this.documentTemplateGetService = documentTemplateGetService;
        this.subscriptionGetService = subscriptionGetService;
        this.metricsService = metricsService;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.customFieldService = customFieldService;
        this.platformFeatureService = platformFeatureService;
        this.tenantSettingService = tenantSettingService;
        this.entityGetService = entityGetService;
        this.docuSignAuthService = docuSignAuthService;
        this.accountPaymentManagementLinkService = accountPaymentManagementLinkService;
        this.rateCardService = rateCardService;
        this.featureService = featureService;
        this.accountGetService = accountGetService;
        this.orderGetService = orderGetService;
        this.opportunityGetService = opportunityGetService;
        orderDetailsMapper = Mappers.getMapper(OrderDetailsMapper.class);
    }

    public OrderDocumentJson getOrderDocumentJson(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        OrderDetail orderDetail = orderDataAggregator.getOrderDetail(orderId, true, false);
        orderDocumentJson.setOrderDetail(orderDetail);

        DocumentDataAggregator.populateEntity(orderDocumentJson, entityGetService, orderDetail.getEntityId());
        DocumentDataAggregator.populateTimeZone(orderDocumentJson, tenantSettingService);

        CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.ORDER, orderId);
        orderDocumentJson.setCustomField(customField);

        Map<String, CustomField> lineItemCustomFields = new HashMap<>();
        orderDetail
            .getLineItems()
            .forEach(lineItem -> {
                CustomField lineItemCustomField = customFieldService.getCustomFields(CustomFieldParentType.ORDER_ITEM, lineItem.getId());
                lineItemCustomFields.put(lineItem.getId(), lineItemCustomField);
            });

        orderDocumentJson.setLineItemCustomFields(lineItemCustomFields);

        if (StringUtils.isNotBlank(orderDetail.getSfdcOpportunityId())) {
            Optional<Opportunity> optionalOpportunity = opportunityGetService.getOptionalOpportunityByCrmOpportunityId(
                orderDetail.getSfdcOpportunityId()
            );
            optionalOpportunity.ifPresent(opportunity -> orderDocumentJson.setOpportunityCustomFields(opportunity.getCustomFields()));
        }

        populatePriceAttributeMap(orderDocumentJson, orderDetail);
        populateInvoicePreviews(orderDocumentJson, orderDetail);
        populateArrProjections(orderDocumentJson, orderDetail);
        populateExecutedLineItemDetails(orderDocumentJson, orderDetail);

        var chargeIds = orderDetail.getLineItems().stream().map(item -> item.getChargeDetail().getId()).toList();
        orderDocumentJson.setChargeCustomFields(customFieldService.getCustomFields(CustomFieldParentType.CHARGE, chargeIds));

        var planIds = orderDetail.getLineItems().stream().map(item -> item.getChargeDetail().getPlanId()).toList();
        orderDocumentJson.setPlanCustomFields(customFieldService.getCustomFields(CustomFieldParentType.PLAN, planIds));

        Set<String> orderLineItemProductIds = orderDetail
            .getLineItems()
            .stream()
            .map(OrderLineItemDetail::getPlan)
            .map(PlanJson::getProductId)
            .collect(Collectors.toSet());

        Set<String> executedOrderLineItemProductIds = orderDocumentJson
            .getExecutedLineItemsForSubscription()
            .stream()
            .map(OrderLineItemDetail::getPlan)
            .map(PlanJson::getProductId)
            .collect(Collectors.toSet());

        Map<String, ProductJson> products = Stream.concat(orderLineItemProductIds.stream(), executedOrderLineItemProductIds.stream())
            .distinct()
            .map(productCatalogGetService::getProduct)
            .map(PRODUCT_MAPPER::productToJson)
            .collect(Collectors.toMap(ProductJson::getId, Function.identity()));

        orderDocumentJson.setProducts(products);

        TenantInfo tenantInfo = tenantService.getCurrentTenantInfo();
        orderDocumentJson.setTenantInfo(tenantInfo);

        // populate logo from entity or tenant, in that order
        DocumentDataAggregator.populateLogoContent(orderDocumentJson, entityGetService, orderDetail.getEntityId(), tenantInfo);

        orderDocumentJson.setTemplateFileName(getOrderFormTemplateFileName(tenantId));
        orderDocumentJson.setTemplateCssFileName(getOrderFormTemplateCssFileName(tenantId));
        orderDocumentJson.setTemplateContainerFileName(getOrderFormTemplateContainerFileName(tenantId));

        getDocumentMasterTemplateContent(orderDetail).ifPresent(orderDocumentJson::setDocumentMasterTemplateContent);
        getDocumentMasterTemplateIsFullHtml(orderDetail).ifPresent(orderDocumentJson::setDocumentMasterTemplateIsFullHtml);
        getDocumentMasterTemplateConfiguration(orderDetail).ifPresent(orderDocumentJson::setDocumentMasterTemplateConfiguration);

        Map<String, Metrics> orderLineMetrics = metricsService.getMetricsForAllOrderLines(orderId);
        fetchMetricsForExecutedLines(orderLineMetrics, orderDocumentJson.getExecutedLineItemsForSubscription());
        orderDocumentJson.setOrderLineMetrics(orderLineMetrics);

        List<OrderDocumentTemplate> documentTemplates = orderDetail.getOrderDocumentTemplates();
        if (CollectionUtils.isNotEmpty(documentTemplates)) {
            populateDocumentTemplatesBySection(orderDocumentJson, documentTemplates);
        }

        TemplateAccount templateAccount = new TemplateAccount(orderDetail.getAccount());
        orderDocumentJson.setTemplateAccount(templateAccount);

        setEsignDocumentTags(orderDocumentJson);
        orderDocumentJson.setPaymentLink(getPaymentLink(orderDetail.getAccountDetail().getId()));

        if (featureService.isEnabled(Feature.ORDER_FORM_RAW_JSON_INJECTION)) {
            populateRawJson(orderDocumentJson, orderDetail);
        }

        populateSubscriptionOrderChain(orderDocumentJson, orderDetail);

        return orderDocumentJson;
    }

    // if the order is a renewal, there can be a chain of subscriptions leading up to the current order.
    // Go back through the subscription chain to find the original order that created the chain
    private void populateSubscriptionOrderChain(OrderDocumentJson orderDocumentJson, OrderDetail orderDetail) {
        if (orderDetail.getOrderType() != OrderType.RENEWAL) {
            return;
        }

        OrderType orderType = orderDetail.getOrderType();
        int subscriptionsTraversed = 0;

        SubscriptionDetail subscriptionDetail = orderDetail.getRenewalForSubscription();
        String subscriptionId = subscriptionDetail.getId();

        // limit to 10 subscriptions to avoid long processing time
        while (orderType == OrderType.RENEWAL && subscriptionsTraversed < 10) {
            Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
            String firstOrderId = subscription.getOrders().get(0);
            Order order = orderGetService.getOrderByOrderId(firstOrderId);
            orderType = order.getOrderType();
            if (orderType == OrderType.NEW || orderType == OrderType.RESTRUCTURE) {
                // first subscription in a chain can only be created by NEW or RESTRUCTURE order
                OrderDetail originalOrder = orderDataAggregator.getOrderDetail(order, true, false);
                orderDocumentJson.setOriginalOrderInChain(originalOrder);
            } else if (orderType == OrderType.RENEWAL) {
                subscriptionId = order.getRenewalForSubscriptionId();
            } else {
                throw new InvariantCheckFailedException(String.format("First order for subscription %s is type %s", subscriptionId, orderType));
            }
            subscriptionsTraversed++;
        }
    }

    public OrderDocumentJson getOrderDocumentJson(String orderId, List<DocumentTemplate> documentTemplates) {
        OrderDocumentJson orderDocumentJson = getOrderDocumentJson(orderId);

        List<OrderDocumentTemplate> orderDocumentTemplates = orderDataAggregator.getOrderDocumentTemplates(documentTemplates);
        if (CollectionUtils.isNotEmpty(orderDocumentTemplates)) {
            populateDocumentTemplatesBySection(orderDocumentJson, orderDocumentTemplates);
        }

        return orderDocumentJson;
    }

    private void populatePriceAttributeMap(OrderDocumentJson orderDocumentJson, OrderDetail orderDetail) {
        Map<String, PriceAttribute> priceAttributeMap = orderDetail
            .getLineItems()
            .stream()
            .map(OrderLineItemDetail::getAttributeReferences)
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(Collection::stream)
            .map(AttributeReference::getAttributeDefinitionId)
            .distinct()
            .map(rateCardService::getPriceAttributeById)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .collect(Collectors.toMap(PriceAttribute::getId, Function.identity()));
        orderDocumentJson.setPriceAttributeMap(priceAttributeMap);
    }

    private void fetchMetricsForExecutedLines(Map<String, Metrics> orderLineMetrics, List<OrderLineItemDetail> executedLineItemsForSubscription) {
        if (CollectionUtils.isEmpty(executedLineItemsForSubscription)) {
            return;
        }

        executedLineItemsForSubscription.forEach(orderLineItemDetail ->
            orderLineMetrics.put(orderLineItemDetail.getId(), metricsService.getOrderLineMetrics(orderLineItemDetail.getId()))
        );
    }

    private void populateExecutedLineItemDetails(OrderDocumentJson orderDocumentJson, OrderDetail orderDetail) {
        if (StringUtils.isBlank(orderDetail.getSubscriptionId())) {
            return;
        }

        // get executed order line items, excluding current order
        List<OrderLineItem> lineItems = orderDataAggregator
            .getExecutedLineItemsBySubscriptionId(orderDetail.getSubscriptionId())
            .stream()
            .filter(lineItem -> !lineItem.getOrderId().equals(orderDetail.getId()))
            .collect(Collectors.toList());
        Map<String, PredefinedDiscountDetail> discountDetailMap = orderDataAggregator.getDiscountsMapForLineItems(lineItems);
        List<OrderLineItemJson> lineItemJsons = orderDetailsMapper.orderLineItemsToJsons(lineItems);
        List<OrderLineItemDetail> lineItemDetails = orderDataAggregator.getLineItemDetails(lineItemJsons, discountDetailMap);
        orderDocumentJson.setExecutedLineItemsForSubscription(lineItemDetails);

        var executedLineItemIds = lineItems.stream().map(OrderLineItem::getOrderLineId).toList();
        orderDocumentJson.setExecutedLineItemCustomFields(customFieldService.getCustomFields(CustomFieldParentType.ORDER_ITEM, executedLineItemIds));

        var executedLinesChargeIds = lineItems.stream().map(OrderLineItem::getChargeId).toList();
        orderDocumentJson.setExecutedLineItemChargeCustomFields(
            customFieldService.getCustomFields(CustomFieldParentType.CHARGE, executedLinesChargeIds)
        );
    }

    public void generateAndPopulateDocumentKey(OrderDocumentJson orderDocumentJson) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var document = new GeneratedDocument();
        document.setDocumentId(orderDocumentJson.getOrderDetail().getId());
        document.setDocumentType(DocumentType.ORDER);

        var savedDocument = documentService.insertDocument(document);
        String documentKey = savedDocument.getDocumentKey().toString();
        orderDocumentJson.setDocumentKey(documentKey);
        populateS3Info(orderDocumentJson, tenantId, documentKey);
    }

    private void populateInvoicePreviews(OrderDocumentJson orderDocumentJson, OrderDetail orderDetail) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        List<InvoicePreview> orderInvoicePreviews = invoiceService.previewAllInvoicesForOrder(orderDetail.getId());

        List<DocumentInvoicePreview> documentInvoicePreviews = getOrderInvoicePreviews(orderDetail, orderInvoicePreviews, timeZone);
        orderDocumentJson.setDocumentInvoicePreviews(documentInvoicePreviews);

        orderDocumentJson.setSubscriptionInvoicePreviews(getForwardSubscriptionInvoicePreviews(orderDetail, orderInvoicePreviews, timeZone));
    }

    private List<DocumentInvoicePreview> getOrderInvoicePreviews(
        OrderDetail orderDetail,
        List<InvoicePreview> orderInvoicePreviews,
        TimeZone timeZone
    ) {
        var sortedInvoicePreviews = orderInvoicePreviews
            .stream()
            .sorted(Comparator.comparing(invoicePreview -> invoicePreview.getBillingPeriod().getStart()))
            .toList();
        if (orderDetail.getBillingCycle() != null && orderDetail.getBillingCycle().getCycle() == Cycle.CUSTOM) {
            return getOrderInvoicePreviewsForCustomBilling(sortedInvoicePreviews, timeZone);
        }
        List<DocumentInvoicePreview> documentInvoicePreviews = new ArrayList<>();
        for (var i = 0; i < sortedInvoicePreviews.size(); i++) {
            InvoicePreview preview = sortedInvoicePreviews.get(i);
            documentInvoicePreviews.add(
                new DocumentInvoicePreview(
                    Period.between(preview.getBillingPeriod().getStart(), preview.getBillingPeriod().getEnd()),
                    preview.getSubtotal(),
                    preview.getTaxEstimate(),
                    preview.getTotal(),
                    i,
                    timeZone
                )
            );
        }
        return documentInvoicePreviews;
    }

    // populate subscription invoice previews set to be generated starting at order start date.
    // If order is amendment type, include invoice preview for existing subscription
    private List<DocumentInvoicePreview> getForwardSubscriptionInvoicePreviews(
        OrderDetail orderDetail,
        List<InvoicePreview> orderInvoicePreviews,
        TimeZone timeZone
    ) {
        if (orderDetail.getBillingCycle() != null && orderDetail.getBillingCycle().getCycle() == Cycle.CUSTOM) {
            return getForwardSubscriptionInvoicePreviewsForCustomBilling(orderDetail, orderInvoicePreviews, timeZone);
        }
        List<InvoicePreview> subscriptionInvoicePreviews = new ArrayList<>();

        if (orderDetail.getOrderType() == OrderType.AMENDMENT || orderDetail.getOrderType() == OrderType.CANCEL) {
            subscriptionInvoicePreviews.addAll(invoiceService.previewAllInvoicesBySubscriptionId(orderDetail.getSubscriptionId()));
        }

        Map<Period, BigDecimal> amountByPeriod = new HashMap<>();
        Map<Period, BigDecimal> taxByPeriod = new HashMap<>();

        // todo: the billing cycle grouping logic should be moved to InvoiceService
        orderInvoicePreviews.forEach(invoicePreview ->
            invoicePreview
                .getInvoiceItems()
                .forEach(item -> {
                    BillingPeriod billingPeriod = invoicePreview.getBillingPeriod();

                    // if order start date is within billing period, use the order line item period since this will be a separate invoice
                    // Otherwise use the billing period since these are future consolidated invoices
                    boolean inBillingPeriod =
                        billingPeriod.getStart().getEpochSecond() < orderDetail.getStartDate() &&
                        orderDetail.getStartDate() < billingPeriod.getEnd().getEpochSecond();
                    Instant periodStart = inBillingPeriod ? item.getPeriodStartDate() : billingPeriod.getStart();
                    Instant periodEnd = inBillingPeriod ? item.getPeriodEndDate() : billingPeriod.getEnd();
                    Period period = Period.between(periodStart, periodEnd);
                    // group by invoice period and aggregate amounts
                    amountByPeriod.merge(period, item.getAmount(), BigDecimal::add);
                    taxByPeriod.merge(period, item.getTaxAmount(), BigDecimal::add);
                })
        );

        subscriptionInvoicePreviews
            .stream()
            // filter out any billing periods that start before the order start date line items
            .filter(invoicePreview -> invoicePreview.getBillingPeriod().getStart().getEpochSecond() >= orderDetail.getStartDate())
            .forEach(invoicePreview ->
                invoicePreview
                    .getInvoiceItems()
                    .stream()
                    // remove line items from the order itself (e.g. order is already executed)
                    .filter(item -> !item.getOrderId().equals(orderDetail.getId()))
                    .forEach(item -> {
                        BillingPeriod billingPeriod = invoicePreview.getBillingPeriod();
                        Period period = Period.between(billingPeriod.getStart(), billingPeriod.getEnd());
                        // group by invoice period and aggregate amounts
                        amountByPeriod.merge(period, item.getAmount(), BigDecimal::add);
                        taxByPeriod.merge(period, item.getTaxAmount(), BigDecimal::add);
                    })
            );

        List<DocumentInvoicePreview> subscriptionDocumentInvoicePreviews = new ArrayList<>();

        for (Map.Entry<Period, BigDecimal> entry : amountByPeriod.entrySet()) {
            // set placeholder index of 0 while aggregating. At this time the list is unsorted.
            BigDecimal taxAmount = taxByPeriod.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            BigDecimal subtotal = entry.getValue();
            subscriptionDocumentInvoicePreviews.add(
                new DocumentInvoicePreview(entry.getKey(), subtotal, taxAmount, subtotal.add(taxAmount), 0, timeZone)
            );
        }

        // sort list by billing period start date and then end date
        return sortDocumentInvoicePreviews(subscriptionDocumentInvoicePreviews);
    }

    private List<DocumentInvoicePreview> getForwardSubscriptionInvoicePreviewsForCustomBilling(
        OrderDetail orderDetail,
        List<InvoicePreview> orderInvoicePreviews,
        TimeZone timeZone
    ) {
        Map<Instant, BigDecimal> amountByPeriod = new HashMap<>();
        Map<Instant, BigDecimal> taxByPeriod = new HashMap<>();

        // todo: the billing cycle grouping logic should be moved to InvoiceService
        orderInvoicePreviews.forEach(invoicePreview ->
            invoicePreview
                .getInvoiceItems()
                .forEach(item -> {
                    Instant triggerOn = item.getTriggerOn();
                    Validator.checkNonNullInternal(triggerOn, "trigger on cannot be null for custom billing items");
                    // group by invoice period and aggregate amounts
                    amountByPeriod.merge(triggerOn, item.getAmount(), BigDecimal::add);
                    taxByPeriod.merge(triggerOn, item.getTaxAmount(), BigDecimal::add);
                })
        );

        List<InvoicePreview> subscriptionInvoicePreviews = new ArrayList<>();
        if (orderDetail.getOrderType() == OrderType.AMENDMENT || orderDetail.getOrderType() == OrderType.CANCEL) {
            subscriptionInvoicePreviews.addAll(invoiceService.previewAllInvoicesBySubscriptionId(orderDetail.getSubscriptionId()));
        }
        subscriptionInvoicePreviews
            .stream()
            // filter out any billing periods that start before the order start date line items
            .filter(invoicePreview -> invoicePreview.getBillingPeriod().getStart().getEpochSecond() >= orderDetail.getStartDate())
            .forEach(invoicePreview ->
                invoicePreview
                    .getInvoiceItems()
                    .stream()
                    // remove line items from the order itself (e.g. order is already executed)
                    .filter(item -> !item.getOrderId().equals(orderDetail.getId()))
                    .forEach(item -> {
                        // group by invoice period and aggregate amounts
                        amountByPeriod.merge(item.getTriggerOn(), item.getAmount(), BigDecimal::add);
                        taxByPeriod.merge(item.getTriggerOn(), item.getTaxAmount(), BigDecimal::add);
                    })
            );

        List<DocumentInvoicePreview> subscriptionDocumentInvoicePreviews = new ArrayList<>();
        for (Map.Entry<Instant, BigDecimal> entry : amountByPeriod.entrySet()) {
            // set placeholder index of 0 while aggregating. At this time the list is unsorted.
            BigDecimal taxAmount = taxByPeriod.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            BigDecimal subtotal = entry.getValue();
            subscriptionDocumentInvoicePreviews.add(
                // todo: there is no real "range" for custom billing periods. We should use just "trigger on" date.
                // (e.g. if first invoice is on order start date, start will be order start date, end will be the day before)
                new DocumentInvoicePreview(Period.between(entry.getKey(), entry.getKey()), subtotal, taxAmount, subtotal.add(taxAmount), 0, timeZone)
            );
        }

        // sort list by billing period start date and then end date
        return sortDocumentInvoicePreviews(subscriptionDocumentInvoicePreviews);
    }

    private List<DocumentInvoicePreview> getOrderInvoicePreviewsForCustomBilling(List<InvoicePreview> orderInvoicePreviews, TimeZone timeZone) {
        Map<Instant, BigDecimal> amountByPeriod = new HashMap<>();
        Map<Instant, BigDecimal> taxByPeriod = new HashMap<>();

        // todo: the billing cycle grouping logic should be moved to InvoiceService
        orderInvoicePreviews.forEach(invoicePreview ->
            invoicePreview
                .getInvoiceItems()
                .forEach(item -> {
                    Instant triggerOn = item.getTriggerOn();
                    Validator.checkNonNullInternal(triggerOn, "trigger on cannot be null for custom billing items");
                    // group by invoice period and aggregate amounts
                    amountByPeriod.merge(triggerOn, item.getAmount(), BigDecimal::add);
                    taxByPeriod.merge(triggerOn, item.getTaxAmount(), BigDecimal::add);
                })
        );

        List<DocumentInvoicePreview> subscriptionDocumentInvoicePreviews = new ArrayList<>();
        for (Map.Entry<Instant, BigDecimal> entry : amountByPeriod.entrySet()) {
            // set placeholder index of 0 while aggregating. At this time the list is unsorted.
            BigDecimal taxAmount = taxByPeriod.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            BigDecimal subtotal = entry.getValue();
            subscriptionDocumentInvoicePreviews.add(
                // todo: there is no real "range" for custom billing periods. We should use just "trigger on" date.
                // (e.g. if first invoice is on order start date, start will be order start date, end will be the day before)
                new DocumentInvoicePreview(Period.between(entry.getKey(), entry.getKey()), subtotal, taxAmount, subtotal.add(taxAmount), 0, timeZone)
            );
        }

        // sort list by billing period start date and then end date
        return sortDocumentInvoicePreviews(subscriptionDocumentInvoicePreviews);
    }

    private static List<DocumentInvoicePreview> sortDocumentInvoicePreviews(List<DocumentInvoicePreview> subscriptionDocumentInvoicePreviews) {
        Comparator<DocumentInvoicePreview> startComparator = Comparator.comparing(invoicePreview -> invoicePreview.getBillingPeriod().getStart());
        Comparator<DocumentInvoicePreview> endComparator = Comparator.comparing(invoicePreview -> invoicePreview.getBillingPeriod().getEnd());

        List<DocumentInvoicePreview> sortedDocumentInvoicePreviews = subscriptionDocumentInvoicePreviews
            .stream()
            .sorted(startComparator.thenComparing(endComparator))
            .toList();

        // set period index to be used for display on OF
        for (var i = 0; i < sortedDocumentInvoicePreviews.size(); i++) {
            sortedDocumentInvoicePreviews.get(i).setPeriodIndex(i);
        }

        return sortedDocumentInvoicePreviews;
    }

    // get year-wise ARRs to display on OF
    private void populateArrProjections(OrderDocumentJson orderDocumentJson, OrderDetail orderDetail) {
        TimeZone timeZone = orderDocumentJson.getTimeZone();
        Optional<Subscription> optionalSubscription = Optional.ofNullable(orderDetail.getSubscriptionId()).map(
            subscriptionGetService::getSubscription
        );

        Instant startDate = optionalSubscription.map(Subscription::getStartDate).orElse(Instant.ofEpochSecond(orderDetail.getStartDate()));
        Instant endDate = optionalSubscription.map(Subscription::getEndDate).orElse(Instant.ofEpochSecond(orderDetail.getEndDate()));

        List<BillingPeriod> billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            startDate,
            endDate,
            startDate,
            endDate,
            timeZone.toZoneId(),
            new Recurrence(Cycle.YEAR, 1),
            BillingTerm.UP_FRONT
        );
        List<DocumentArrProjection> arrProjections = billingPeriods
            .stream()
            .map(billingPeriod -> getArrProjectionForBillingPeriod(orderDetail, optionalSubscription, endDate, billingPeriod))
            .collect(Collectors.toList());

        orderDocumentJson.setArrProjections(arrProjections);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    // get ARR for given billing period (a year). last billing period may extend beyond end of service period.
    private DocumentArrProjection getArrProjectionForBillingPeriod(
        OrderDetail orderDetail,
        Optional<Subscription> optionalSubscription,
        Instant endDate,
        BillingPeriod billingPeriod
    ) {
        Instant arrDate = billingPeriod.getEnd().isAfter(endDate) ? endDate.minusSeconds(1) : billingPeriod.getEnd().minusSeconds(1);
        BigDecimal subscriptionArr = optionalSubscription
            .map(subscription -> metricsService.getSubscriptionARR(subscription.getSubscriptionId(), arrDate))
            .orElse(BigDecimal.ZERO);
        BigDecimal orderArr = orderDetail.getStatus() == OrderStatus.EXECUTED
            ? BigDecimal.ZERO
            : metricsService.getOrderMetrics(orderDetail.getId(), arrDate).getArr();
        BigDecimal totalArr = subscriptionArr.add(orderArr);
        return new DocumentArrProjection(totalArr, billingPeriod);
    }

    private Optional<String> getDocumentMasterTemplateContent(OrderDetail orderDetail) {
        var optionalMasterTemplate = Optional.ofNullable(orderDetail.getDocumentMasterTemplate());
        if (optionalMasterTemplate.isEmpty()) {
            optionalMasterTemplate = documentTemplateGetService.getDefaultMasterTemplate(DocumentTemplateType.ORDER);
        }
        return optionalMasterTemplate.map(DocumentMasterTemplate::getContent);
    }

    private Optional<Boolean> getDocumentMasterTemplateIsFullHtml(OrderDetail orderDetail) {
        var optionalMasterTemplate = Optional.ofNullable(orderDetail.getDocumentMasterTemplate());
        if (optionalMasterTemplate.isEmpty()) {
            optionalMasterTemplate = documentTemplateGetService.getDefaultMasterTemplate(DocumentTemplateType.ORDER);
        }
        return optionalMasterTemplate.map(DocumentMasterTemplate::getIsFullHtml);
    }

    private Optional<DocumentMasterTemplateConfiguration> getDocumentMasterTemplateConfiguration(OrderDetail orderDetail) {
        var optionalMasterTemplate = Optional.ofNullable(orderDetail.getDocumentMasterTemplate());
        if (optionalMasterTemplate.isEmpty()) {
            optionalMasterTemplate = documentTemplateGetService.getDefaultMasterTemplate(DocumentTemplateType.ORDER);
        }
        return optionalMasterTemplate.map(DocumentMasterTemplate::getConfiguration);
    }

    private String getOrderFormTemplateFileName(String tenantId) {
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        return tenantScopedConfig.getDocumentConfiguration().getOrderFormTemplateFileName();
    }

    public String getOrderFormTemplateCssFileName(String tenantId) {
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        return tenantScopedConfig.getDocumentConfiguration().getOrderFormTemplateCssFileName();
    }

    private String getOrderFormTemplateContainerFileName(String tenantId) {
        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId);
        return tenantScopedConfig.getDocumentConfiguration().getOrderFormTemplateContainerFileName();
    }

    private void populateS3Info(OrderDocumentJson orderDocumentJson, String tenantId, String documentKey) {
        String fileName = String.format(DOCUMENT_FILE_NAME_FORMAT, tenantId, documentKey);
        orderDocumentJson.setFileName(fileName);
        orderDocumentJson.setS3Bucket(documentConfiguration.getOrderS3Bucket());
    }

    private void populateDocumentTemplatesBySection(OrderDocumentJson orderDocumentJson, List<OrderDocumentTemplate> documentTemplates) {
        Map<String, DocumentSection> documentSectionByUuid = documentTemplateGetService
            .getDocumentSections()
            .stream()
            .collect(Collectors.toMap(s -> s.getId().toString(), Function.identity()));

        Map<Boolean, List<DocumentTemplateResponse>> documentTemplatesPartitionedBySectionUuid = documentTemplates
            .stream()
            .filter(t -> StringUtils.isNotBlank(t.getContent()))
            .collect(Collectors.partitioningBy(t -> t.getSectionUuid() == null));
        List<DocumentTemplateResponse> documentTemplatesWithoutSection = documentTemplatesPartitionedBySectionUuid.get(true);
        orderDocumentJson.setDocumentTemplatesWithoutSection(documentTemplatesWithoutSection);

        // Note: Using TreeMap & TreeSet with Comparator for consistent ordering of the document sections & templates when rendering PDF
        List<DocumentTemplateResponse> documentTemplatesWithSection = documentTemplatesPartitionedBySectionUuid.get(false);
        SortedMap<DocumentSection, SortedSet<DocumentTemplateResponse>> documentTemplatesBySection = documentTemplatesWithSection
            .stream()
            .collect(
                Collectors.groupingBy(
                    t -> documentSectionByUuid.get(t.getSectionUuid()),
                    () -> new TreeMap<>(Comparator.comparing(DocumentSection::getName)),
                    Collectors.mapping(
                        Function.identity(),
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DocumentTemplateResponse::getName)))
                    )
                )
            );
        orderDocumentJson.setDocumentTemplatesBySection(documentTemplatesBySection);
    }

    private void setEsignDocumentTags(OrderDocumentJson orderDocumentJson) {
        String tenantId = orderDocumentJson.getTenantInfo().getTenantId();
        SigningOrder signingOrder = tenantSettingService.getSigningOrder();
        if (platformFeatureService.getFeatureEnablement(PlatformFeature.ESIGN).isPresent()) {
            orderDocumentJson.setCustomerEsignDocumentTags(new EsignDocumentTags("s1", ElectronicSignatureProvider.PANDADOC));
            if (shouldAddSignatoryEsignDocumentTags(signingOrder)) {
                orderDocumentJson.setSignatoryEsignDocumentTags(new EsignDocumentTags("s2", ElectronicSignatureProvider.PANDADOC));
            }
        } else if (docuSignAuthService.hasDocusignIntegration(tenantId)) {
            orderDocumentJson.setCustomerEsignDocumentTags(new EsignDocumentTags("1", ElectronicSignatureProvider.DOCUSIGN));
            if (shouldAddSignatoryEsignDocumentTags(signingOrder)) {
                orderDocumentJson.setSignatoryEsignDocumentTags(new EsignDocumentTags("2", ElectronicSignatureProvider.DOCUSIGN));
            }
        }
    }

    private boolean shouldAddSignatoryEsignDocumentTags(SigningOrder signingOrder) {
        return signingOrder.getExpectedNumberOfSignatures() != 1;
    }

    private void populateRawJson(OrderDocumentJson orderDocumentJson, OrderDetail orderDetail) {
        try {
            OrderDetail jsonOrderDetail = new OrderDetail();
            jsonOrderDetail.setLineItems(orderDetail.getLineItems());
            String rawJson = JacksonProvider.defaultMapper().writeValueAsString(jsonOrderDetail);
            orderDocumentJson.setRawJson(rawJson);
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("unable to serialize the order form JSON");
        }
    }

    private String getPaymentLink(String accountId) {
        if (!accountGetService.doesAccountSupportAutomaticPayment(accountId)) {
            return null;
        }

        try {
            Optional<AccountPaymentManagementLink> accountPaymentLink = accountPaymentManagementLinkService.getOrCreateAccountPaymentManagementLink(
                accountId
            );
            if (accountPaymentLink.isPresent()) {
                return SubskribeUrlGenerator.getUiAccountPaymentManagementUrl(billyConfiguration.getSiteUrl(), accountPaymentLink.get().getLinkId());
            }
        } catch (Exception e) {
            LOGGER.warn("error generating order payment link for account {}", accountId, e);
        }

        return null;
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public OrderDocumentJson getEmptyOrderDocumentJson(Optional<DocumentCustomContent> documentCustomContent) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();

        Order order = new Order();
        order.setTotalAmount(BigDecimal.ZERO);
        order.setTotalListAmount(BigDecimal.ZERO);
        order.setTotalListAmountBeforeOverride(BigDecimal.ZERO);
        order.setBillingCycle(new Recurrence(Cycle.YEAR, 1));
        order.setPaymentTerm(PaymentTerm.NET30);
        order.setOrderType(OrderType.NEW);
        order.setStartDate(Instant.now());
        order.setEndDate(Instant.now().plusSeconds(ONE_YEAR_IN_SECONDS));
        documentCustomContent.ifPresent(order::setDocumentCustomContent);
        OrderDetail orderDetail = orderDataAggregator.getOrderDetail(order, true, true);
        orderDocumentJson.setOrderDetail(orderDetail);
        orderDocumentJson.setLogoContent(Optional.empty());
        orderDocumentJson.setTimeZone(timeZone);

        TenantInfo tenantInfo = tenantService.getCurrentTenantInfo();
        orderDocumentJson.setTenantInfo(tenantInfo);

        orderDocumentJson.setTemplateFileName(getOrderFormTemplateFileName(tenantId));
        orderDocumentJson.setTemplateCssFileName(getOrderFormTemplateCssFileName(tenantId));
        orderDocumentJson.setTemplateContainerFileName(getOrderFormTemplateContainerFileName(tenantId));

        getDocumentMasterTemplateContent(orderDetail).ifPresent(orderDocumentJson::setDocumentMasterTemplateContent);
        getDocumentMasterTemplateIsFullHtml(orderDetail).ifPresent(orderDocumentJson::setDocumentMasterTemplateIsFullHtml);
        getDocumentMasterTemplateConfiguration(orderDetail).ifPresent(orderDocumentJson::setDocumentMasterTemplateConfiguration);

        return orderDocumentJson;
    }

    public OrderDocumentJson getEmptyOrderDocumentJson(List<DocumentTemplate> documentTemplates) {
        OrderDocumentJson orderDocumentJson = getEmptyOrderDocumentJson(Optional.empty());

        List<OrderDocumentTemplate> orderDocumentTemplates = orderDataAggregator.getOrderDocumentTemplates(documentTemplates);
        if (CollectionUtils.isNotEmpty(orderDocumentTemplates)) {
            populateDocumentTemplatesBySection(orderDocumentJson, orderDocumentTemplates);
        }

        return orderDocumentJson;
    }

    public OrderDocumentJson getEmptyOrderDocumentJson(DocumentCustomContent documentCustomContent) {
        return getEmptyOrderDocumentJson(Optional.ofNullable(documentCustomContent));
    }
}
