package com.subskribe.billy.order.document;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.esign.model.EsignDocumentTags;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.template.DocumentTemplateResponse;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.shared.document.BaseDocumentJson;
import com.subskribe.billy.shared.document.DocumentArrProjection;
import com.subskribe.billy.shared.document.TemplateAccount;
import com.subskribe.billy.template.model.DocumentMasterTemplateConfiguration;
import com.subskribe.billy.template.model.DocumentSection;
import com.subskribe.billy.tenant.model.TenantInfo;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.SortedMap;
import java.util.SortedSet;

public class OrderDocumentJson extends BaseDocumentJson {

    private String documentKey;

    private String fileName;

    private String templateFileName;

    private String templateCssFileName;

    private String templateContainerFileName;

    private String documentMasterTemplateContent;

    private Boolean documentMasterTemplateIsFullHtml;

    private DocumentMasterTemplateConfiguration documentMasterTemplateConfiguration;

    private String s3Bucket;

    // TODO: Should we be using OrderDetail object here?
    //  Potential GQL layer leak into PDF rendering service.
    private OrderDetail orderDetail;

    private CustomField customField;

    private Map<String, CustomField> lineItemCustomFields;

    private Map<String, CustomField> chargeCustomFields;

    private Map<String, CustomField> planCustomFields;

    private Map<String, CustomField> executedLineItemCustomFields;

    private Map<String, CustomField> executedLineItemChargeCustomFields;

    private CustomField opportunityCustomFields;

    private Map<String, PriceAttribute> priceAttributeMap;

    private TenantInfo tenantInfo;

    private String paymentLink;

    @JsonIgnore
    private SortedMap<DocumentSection, SortedSet<DocumentTemplateResponse>> documentTemplatesBySection;

    private List<DocumentTemplateResponse> documentTemplatesWithoutSection;

    private List<OrderLineItemDetail> executedLineItemsForSubscription;

    private List<DocumentInvoicePreview> documentInvoicePreviews;

    private List<DocumentInvoicePreview> subscriptionInvoicePreviews;

    private List<DocumentArrProjection> arrProjections;

    private DocumentRenderingOptions documentRenderingOptions;

    Map<String, ProductJson> products;

    private Map<String, Metrics> orderLineMetrics;

    private TemplateAccount templateAccount;

    private EsignDocumentTags customerEsignDocumentTags;

    private EsignDocumentTags signatoryEsignDocumentTags;

    private String rawJson;

    private OrderDetail originalOrderInChain;

    public OrderDocumentJson() {
        lineItemCustomFields = new HashMap<>();
        chargeCustomFields = new HashMap<>();
        planCustomFields = new HashMap<>();
        opportunityCustomFields = new CustomField(new HashMap<>());
    }

    public String getDocumentKey() {
        return documentKey;
    }

    public void setDocumentKey(String documentKey) {
        this.documentKey = documentKey;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getS3Bucket() {
        return s3Bucket;
    }

    public void setS3Bucket(String s3Bucket) {
        this.s3Bucket = s3Bucket;
    }

    public OrderDetail getOrderDetail() {
        return orderDetail;
    }

    public void setOrderDetail(OrderDetail orderDetail) {
        this.orderDetail = orderDetail;
    }

    public Map<String, ProductJson> getProducts() {
        return products;
    }

    public void setProducts(Map<String, ProductJson> products) {
        this.products = products;
    }

    public CustomField getCustomField() {
        return customField;
    }

    public void setCustomField(CustomField customField) {
        this.customField = customField;
    }

    public Map<String, CustomField> getLineItemCustomFields() {
        return lineItemCustomFields;
    }

    public void setLineItemCustomFields(Map<String, CustomField> lineItemCustomFields) {
        this.lineItemCustomFields = lineItemCustomFields == null ? new HashMap<>() : lineItemCustomFields;
    }

    public Map<String, CustomField> getChargeCustomFields() {
        return chargeCustomFields;
    }

    public void setChargeCustomFields(Map<String, CustomField> chargeCustomFields) {
        this.chargeCustomFields = chargeCustomFields == null ? new HashMap<>() : chargeCustomFields;
    }

    public Map<String, CustomField> getPlanCustomFields() {
        return planCustomFields;
    }

    public void setPlanCustomFields(Map<String, CustomField> planCustomFields) {
        this.planCustomFields = planCustomFields;
    }

    public CustomField getOpportunityCustomFields() {
        return opportunityCustomFields;
    }

    public void setOpportunityCustomFields(CustomField opportunityCustomFields) {
        this.opportunityCustomFields = opportunityCustomFields == null ? new CustomField(new HashMap<>()) : opportunityCustomFields;
    }

    public Map<String, CustomField> getExecutedLineItemCustomFields() {
        return executedLineItemCustomFields;
    }

    public void setExecutedLineItemCustomFields(Map<String, CustomField> executedLineItemCustomFields) {
        this.executedLineItemCustomFields = executedLineItemCustomFields;
    }

    public Map<String, CustomField> getExecutedLineItemChargeCustomFields() {
        return executedLineItemChargeCustomFields;
    }

    public void setExecutedLineItemChargeCustomFields(Map<String, CustomField> executedLineItemChargeCustomFields) {
        this.executedLineItemChargeCustomFields = executedLineItemChargeCustomFields;
    }

    public Map<String, PriceAttribute> getPriceAttributeMap() {
        return priceAttributeMap;
    }

    public void setPriceAttributeMap(Map<String, PriceAttribute> priceAttributeMap) {
        this.priceAttributeMap = priceAttributeMap;
    }

    public TenantInfo getTenantInfo() {
        return tenantInfo;
    }

    public void setTenantInfo(TenantInfo tenantInfo) {
        this.tenantInfo = tenantInfo;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public String getTemplateCssFileName() {
        return templateCssFileName;
    }

    public void setTemplateCssFileName(String templateCssFileName) {
        this.templateCssFileName = templateCssFileName;
    }

    public String getTemplateContainerFileName() {
        return templateContainerFileName;
    }

    public void setTemplateContainerFileName(String templateContainerFileName) {
        this.templateContainerFileName = templateContainerFileName;
    }

    public String getDocumentMasterTemplateContent() {
        return documentMasterTemplateContent;
    }

    public void setDocumentMasterTemplateContent(String documentMasterTemplateContent) {
        this.documentMasterTemplateContent = documentMasterTemplateContent;
    }

    public Boolean getDocumentMasterTemplateIsFullHtml() {
        return documentMasterTemplateIsFullHtml;
    }

    public void setDocumentMasterTemplateIsFullHtml(Boolean documentMasterTemplateIsFullHtml) {
        this.documentMasterTemplateIsFullHtml = documentMasterTemplateIsFullHtml;
    }

    public DocumentMasterTemplateConfiguration getDocumentMasterTemplateConfiguration() {
        return documentMasterTemplateConfiguration;
    }

    public void setDocumentMasterTemplateConfiguration(DocumentMasterTemplateConfiguration documentMasterTemplateConfiguration) {
        this.documentMasterTemplateConfiguration = documentMasterTemplateConfiguration;
    }

    public String getPaymentLink() {
        return paymentLink;
    }

    public void setPaymentLink(String paymentLink) {
        this.paymentLink = paymentLink;
    }

    public SortedMap<DocumentSection, SortedSet<DocumentTemplateResponse>> getDocumentTemplatesBySection() {
        return documentTemplatesBySection;
    }

    public void setDocumentTemplatesBySection(SortedMap<DocumentSection, SortedSet<DocumentTemplateResponse>> documentTemplatesBySection) {
        this.documentTemplatesBySection = documentTemplatesBySection;
    }

    public List<DocumentTemplateResponse> getDocumentTemplatesWithoutSection() {
        return documentTemplatesWithoutSection;
    }

    public void setDocumentTemplatesWithoutSection(List<DocumentTemplateResponse> documentTemplatesWithoutSection) {
        this.documentTemplatesWithoutSection = documentTemplatesWithoutSection;
    }

    public List<OrderLineItemDetail> getExecutedLineItemsForSubscription() {
        return executedLineItemsForSubscription == null ? List.of() : executedLineItemsForSubscription;
    }

    public void setExecutedLineItemsForSubscription(List<OrderLineItemDetail> executedLineItemsForSubscription) {
        this.executedLineItemsForSubscription = executedLineItemsForSubscription;
    }

    public List<DocumentInvoicePreview> getDocumentInvoicePreviews() {
        return documentInvoicePreviews;
    }

    public void setDocumentInvoicePreviews(List<DocumentInvoicePreview> documentInvoicePreviews) {
        this.documentInvoicePreviews = documentInvoicePreviews;
    }

    public List<DocumentInvoicePreview> getSubscriptionInvoicePreviews() {
        return subscriptionInvoicePreviews;
    }

    public void setSubscriptionInvoicePreviews(List<DocumentInvoicePreview> subscriptionInvoicePreviews) {
        this.subscriptionInvoicePreviews = subscriptionInvoicePreviews;
    }

    public List<DocumentArrProjection> getArrProjections() {
        return arrProjections;
    }

    public void setArrProjections(List<DocumentArrProjection> arrProjections) {
        this.arrProjections = arrProjections;
    }

    public DocumentRenderingOptions getDocumentRenderingOptions() {
        return documentRenderingOptions;
    }

    public void setDocumentRenderingOptions(DocumentRenderingOptions documentRenderingOptions) {
        this.documentRenderingOptions = documentRenderingOptions;
    }

    public Map<String, Metrics> getOrderLineMetrics() {
        return orderLineMetrics;
    }

    public void setOrderLineMetrics(Map<String, Metrics> orderLineMetrics) {
        this.orderLineMetrics = orderLineMetrics;
    }

    public TemplateAccount getTemplateAccount() {
        return templateAccount;
    }

    public void setTemplateAccount(TemplateAccount templateAccount) {
        this.templateAccount = templateAccount;
    }

    public EsignDocumentTags getCustomerEsignDocumentTags() {
        return customerEsignDocumentTags;
    }

    public void setCustomerEsignDocumentTags(EsignDocumentTags customerEsignDocumentTags) {
        this.customerEsignDocumentTags = customerEsignDocumentTags;
    }

    public EsignDocumentTags getSignatoryEsignDocumentTags() {
        return signatoryEsignDocumentTags;
    }

    public void setSignatoryEsignDocumentTags(EsignDocumentTags signatoryEsignDocumentTags) {
        this.signatoryEsignDocumentTags = signatoryEsignDocumentTags;
    }

    public String getRawJson() {
        return rawJson;
    }

    public void setRawJson(String rawJson) {
        this.rawJson = rawJson;
    }

    @Override
    public Optional<String> getTemplate() {
        return Optional.ofNullable(getDocumentMasterTemplateContent());
    }

    @Override
    public void setTemplate(String template) {
        setDocumentMasterTemplateContent(template);
    }

    public OrderDetail getOriginalOrderInChain() {
        return originalOrderInChain;
    }

    public void setOriginalOrderInChain(OrderDetail originalOrderInChain) {
        this.originalOrderInChain = originalOrderInChain;
    }
}
