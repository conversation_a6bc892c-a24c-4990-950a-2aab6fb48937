package com.subskribe.billy.order.services;

import static java.util.stream.Collectors.groupingBy;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.model.AccountStub;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceGroup;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceStatus;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceService;
import com.subskribe.billy.attachments.service.AttachmentsService;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationshipType;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customization.model.ImmutableOrderCreationCustomizationInput;
import com.subskribe.billy.customization.model.OrderCreationCustomizationInput;
import com.subskribe.billy.customization.model.OrderCreationCustomizationOutput;
import com.subskribe.billy.customization.service.CustomizationService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.document.service.DocumentLinkService;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.esign.services.EsignService;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrder;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrderMapper;
import com.subskribe.billy.hubspot.service.HubSpotJobQueueService;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.datadog.AllMetrics;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.EvergreenUtils;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.model.CreateOrderContext;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.ImmutableCreateOrderContext;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderProcessingMetadata;
import com.subskribe.billy.order.model.OrderSource;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.order.model.OrderStub;
import com.subskribe.billy.paymentterms.model.PaymentTermSettings;
import com.subskribe.billy.paymentterms.services.PaymentTermSettingsService;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.postgres.PostgresAdvisoryLock;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.resources.json.order.OrderAttributesUpdateRequest;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.revrec.model.TransactionType;
import com.subskribe.billy.revrec.services.RevenueRecognitionJobService;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.salesroom.model.SalesRoomLink;
import com.subskribe.billy.salesroom.service.SalesRoomLinkService;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.Sequencer;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.template.model.CustomTemplateUpdateOnOrder;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderGetService;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderService;
import com.subskribe.billy.template.services.DocumentCustomContentService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.OrderTermsService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

@AllMetrics
@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class OrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderService.class);

    private static final Instant MAX_ALLOWED_DATE = LocalDateTime.of(3000, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC);

    private static final Set<PlanStatus> ALLOWED_PLAN_STATUS = Set.of(PlanStatus.ACTIVE, PlanStatus.DEPRECATED);

    private static final Set<OrderStatus> ALLOWED_DELETABLE_ORDER_STATUS = Set.of(OrderStatus.DRAFT, OrderStatus.EXPIRED);
    private static final Set<OrderType> ALLOWED_ORDER_TYPES_WITH_ORDER_SERVICE_ROUTE = Set.of(
        OrderType.NEW,
        OrderType.RENEWAL,
        OrderType.RESTRUCTURE
    );
    private static final Set<ActionType> ALLOWED_ACTION_TYPES_WITH_ORDER_SERVICE_ROUTE = Set.of(
        ActionType.ADD,
        ActionType.RENEWAL,
        ActionType.MISSING_RENEWAL,
        ActionType.RESTRUCTURE
    );

    private static final int SUBSCRIPTION_TARGET_VERSION_NEW = 1;

    private static final long DEFAULT_RENEWAL_NUMBER_OF_YEARS = 1;

    private final OrderDAO orderDAO;
    private final AccountGetService accountGetService;
    private final ProductCatalogGetService productCatalogGetService;
    private final SubscriptionService subscriptionService;
    private final InvoiceService invoiceService;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final EntityContextProvider entityContextProvider;
    private final EntityContextResolver entityContextResolver;
    private final OrderIdGenerator orderIdGenerator;
    private final OrderMapper orderMapper;
    private final EmailContactListService emailContactListService;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final DocumentCustomContentService documentCustomContentService;
    private final OpportunityGetService opportunityGetService;
    private final OpportunityService opportunityService;
    private final OrderTermsService orderTermsService;
    private final OrderGetService orderGetService;
    private final OrderValidationService orderValidationService;
    private final SalesforceJobQueueService salesforceJobQueueService;
    private final DiscountService discountService;
    private final ApprovalFlowInstanceService approvalFlowInstanceService;
    private final UserService userService;
    private final FeatureService featureService;
    private final PlatformFeatureService platformFeatureService;
    private final TenantSettingService tenantSettingService;
    private final BillyConfiguration billyConfiguration;
    private final RevenueRecognitionJobService revenueRecognitionJobService;
    private final CustomFieldService customFieldService;
    private final AttachmentsService attachmentsService;
    private final PaymentTermSettingsService paymentTermSettingsService;
    private final DocumentLinkService documentLinkService;
    private final ReportingJobQueueService reportingJobQueueService;
    private final SubscriptionGetService subscriptionGetService;
    private final CatalogRelationshipGetService catalogRelationshipGetService;
    private final RateCardService rateCardService;
    private final OrderEventService orderEventService;
    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;
    private final TaxService taxService;
    private final TaxRateGetService taxRateGetService;
    private final SalesRoomLinkService salesRoomLinkService;
    private final CustomizationService customizationService;
    private final MissingOrderChargesService missingOrderChargesService;
    private final MetricsService metricsService;
    private final CrmService crmService;
    private final CurrencyConversionRateGetService currencyConversionRateGetService;
    private final EsignService esignService;
    private final OrderCustomBillingService orderCustomBillingService;
    private final OrderAttributesUpdateService orderAttributesUpdateService;
    private final OrderDocumentTemplateService orderDocumentTemplateService;
    private final CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService;
    private final CustomPredefinedTemplateOnOrderMapper customPredefinedTemplateOnOrderMapper;
    private final CustomTemplateUpdatedOnOrderService customTemplateUpdatedOnOrderService;
    private final HubSpotJobQueueService hubSpotJobQueueService;

    private static final String UPDATE_ORDER_STATUS_LOCK_KEY_FORMAT = "updateOrderStatus/%s/%s";

    @Inject
    public OrderService(
        OrderDAO orderDAO,
        AccountGetService accountGetService,
        ProductCatalogGetService productCatalogGetService,
        SubscriptionService subscriptionService,
        InvoiceService invoiceService,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider,
        EntityContextProvider entityContextProvider,
        EntityContextResolver entityContextResolver,
        OrderIdGenerator orderIdGenerator,
        EmailContactListService emailContactListService,
        DocumentTemplateGetService documentTemplateGetService,
        DocumentCustomContentService documentCustomContentService,
        OpportunityGetService opportunityGetService,
        OpportunityService opportunityService,
        OrderTermsService orderTermsService,
        OrderGetService orderGetService,
        OrderValidationService orderValidationService,
        DiscountService discountService,
        ApprovalFlowInstanceService approvalFlowInstanceService,
        UserService userService,
        SalesforceJobQueueService salesforceJobQueueService,
        FeatureService featureService,
        PlatformFeatureService platformFeatureService,
        TenantSettingService tenantSettingService,
        BillyConfiguration billyConfiguration,
        RevenueRecognitionJobService revenueRecognitionJobService,
        CustomFieldService customFieldService,
        AttachmentsService attachmentsService,
        PaymentTermSettingsService paymentTermSettingsService,
        ReportingJobQueueService reportingJobQueueService,
        SubscriptionGetService subscriptionGetService,
        DocumentLinkService documentLinkService,
        CatalogRelationshipGetService catalogRelationshipGetService,
        RateCardService rateCardService,
        OrderEventService orderEventService,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        TaxService taxService,
        TaxRateGetService taxRateGetService,
        SalesRoomLinkService salesRoomLinkService,
        CustomizationService customizationService,
        MissingOrderChargesService missingOrderChargesService,
        MetricsService metricsService,
        CrmService crmService,
        CurrencyConversionRateGetService currencyConversionRateGetService,
        EsignService esignService,
        OrderCustomBillingService orderCustomBillingService,
        OrderAttributesUpdateService orderAttributesUpdateService,
        OrderDocumentTemplateService orderDocumentTemplateService,
        CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService,
        CustomTemplateUpdatedOnOrderService customTemplateUpdatedOnOrderService,
        HubSpotJobQueueService hubSpotJobQueueService
    ) {
        this.orderDAO = orderDAO;
        this.accountGetService = accountGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.subscriptionService = subscriptionService;
        this.invoiceService = invoiceService;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.entityContextProvider = entityContextProvider;
        this.entityContextResolver = entityContextResolver;
        this.orderIdGenerator = orderIdGenerator;
        this.emailContactListService = emailContactListService;
        this.documentTemplateGetService = documentTemplateGetService;
        this.documentCustomContentService = documentCustomContentService;
        this.opportunityGetService = opportunityGetService;
        this.orderTermsService = orderTermsService;
        this.orderGetService = orderGetService;
        this.orderValidationService = orderValidationService;
        this.salesforceJobQueueService = salesforceJobQueueService;
        this.billyConfiguration = billyConfiguration;
        this.revenueRecognitionJobService = revenueRecognitionJobService;
        this.customFieldService = customFieldService;
        this.attachmentsService = attachmentsService;
        this.paymentTermSettingsService = paymentTermSettingsService;
        this.documentLinkService = documentLinkService;
        this.opportunityService = opportunityService;
        this.discountService = discountService;
        this.approvalFlowInstanceService = approvalFlowInstanceService;
        this.userService = userService;
        this.featureService = featureService;
        this.platformFeatureService = platformFeatureService;
        this.tenantSettingService = tenantSettingService;
        this.reportingJobQueueService = reportingJobQueueService;
        this.subscriptionGetService = subscriptionGetService;
        this.catalogRelationshipGetService = catalogRelationshipGetService;
        this.rateCardService = rateCardService;
        this.orderEventService = orderEventService;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.taxService = taxService;
        this.taxRateGetService = taxRateGetService;
        this.salesRoomLinkService = salesRoomLinkService;
        this.customizationService = customizationService;
        this.missingOrderChargesService = missingOrderChargesService;
        this.metricsService = metricsService;
        this.crmService = crmService;
        this.currencyConversionRateGetService = currencyConversionRateGetService;
        this.esignService = esignService;
        this.orderCustomBillingService = orderCustomBillingService;
        this.orderAttributesUpdateService = orderAttributesUpdateService;
        this.orderDocumentTemplateService = orderDocumentTemplateService;
        this.customTemplateUpdatedOnOrderGetService = customTemplateUpdatedOnOrderGetService;
        this.customTemplateUpdatedOnOrderService = customTemplateUpdatedOnOrderService;
        this.hubSpotJobQueueService = hubSpotJobQueueService;
        orderMapper = Mappers.getMapper(OrderMapper.class);
        customPredefinedTemplateOnOrderMapper = Mappers.getMapper(CustomPredefinedTemplateOnOrderMapper.class);
    }

    public Order addOrder(Order order, Boolean isDryRun) {
        throwIfTenantSettingIsNotSealed();
        // we're using the same request object for both add and update order. In the case of add, if an orderId is provided in the request, throw an input validation error
        if (StringUtils.isNotBlank(order.getOrderId())) {
            throw new InvalidInputException("Id field should be blank when creating a new order");
        }

        var tenantId = tenantIdProvider.provideTenantIdString();
        String entityId = entityContextResolver.resolveInputEntityIdForIsolatedObject(order.getEntityId());

        // ensure single entity is selected
        String selectedEntityId = entityContextProvider.provideSingleEntityOrElseThrow(); // throw if multiple entities selected
        if (!entityId.equals(selectedEntityId)) {
            throw new InvalidInputException("Order entityId does not match selected entity");
        }

        order.getLineItems().forEach(li -> li.setTenantId(tenantId));
        order.getLineItems().forEach(li -> li.setEntityId(entityId));
        order.setTenantId(tenantId);
        order.setEntityId(entityId);
        order.setStatus(OrderStatus.DRAFT);

        Optional<Subscription> currentSubscription = getCurrentSubscription(order);

        CreateOrderContext createOrderContext = makeCreateOrderContext(order, currentSubscription, isDryRun);
        checkOrderTypeIsNewOrRenew(order);
        validateOrder(order, createOrderContext, isDryRun);
        OrderServiceHelper.addOrderCreatedByAndOwner(order, userService);

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return tenantDslContext.transactionResult(configuration -> addOrderTransaction(configuration, order, createOrderContext, isDryRun));
    }

    private CreateOrderContext makeCreateOrderContext(Order order, Optional<Subscription> currentSubscription, Boolean isDryRun) {
        ImmutableCreateOrderContext.Builder contextBuilder = ImmutableCreateOrderContext.builder();

        Account primaryAccount = accountGetService.getAccount(order.getAccountId());
        contextBuilder.orderAccount(primaryAccount);

        if (StringUtils.isNotBlank(order.getShippingContactId())) {
            contextBuilder.shippingContact(accountGetService.getContact(order.getShippingContactId()));
        }

        if (StringUtils.isNotBlank(order.getBillingContactId())) {
            AccountContact billingContact = accountGetService.getContact(order.getBillingContactId());
            contextBuilder.billingContact(billingContact);

            if (!billingContact.getAccountId().equals(primaryAccount.getAccountId())) {
                contextBuilder.billingContactAccount(accountGetService.getAccount(billingContact.getAccountId()));
            } else {
                contextBuilder.billingContactAccount(primaryAccount);
            }
        }

        if (CollectionUtils.isEmpty(order.getLineItems())) {
            return contextBuilder
                .productsInOrder(List.of())
                .chargesInOrder(List.of())
                .plansInOrder(List.of())
                .subscription(currentSubscription)
                .plansInSubscription(List.of())
                .planCustomFields(Map.of())
                .chargeCustomFields(Map.of())
                .build();
        }

        List<String> planIds = order.getLineItems().stream().map(OrderLineItem::getPlanId).filter(StringUtils::isNotBlank).distinct().toList();
        List<Plan> plans = productCatalogGetService.getPlansByPlanIds(planIds);
        List<Product> products = plans.stream().map(Plan::getProductId).distinct().map(productCatalogGetService::getProduct).toList();
        List<Charge> charges = plans.stream().map(Plan::getCharges).flatMap(Collection::stream).toList();

        Map<String, CustomField> planCustomFields = customFieldService.getCustomFields(CustomFieldParentType.PLAN, planIds);
        Map<String, CustomField> chargeCustomFields = customFieldService.getCustomFields(
            CustomFieldParentType.CHARGE,
            charges.stream().map(Charge::getChargeId).distinct().toList()
        );

        // TODO: move this to some other place
        OrderServiceHelper.hydrateAndValidateCustomFields(order, isDryRun, customFieldService);

        List<Plan> plansInSubscription = List.of();
        if (currentSubscription.isPresent()) {
            List<String> chargeIdsInSubscription = currentSubscription
                .get()
                .getCharges()
                .stream()
                .map(SubscriptionCharge::getChargeId)
                .distinct()
                .toList();
            if (CollectionUtils.isNotEmpty(chargeIdsInSubscription)) {
                plansInSubscription = productCatalogGetService.getPlansFromChargeIds(chargeIdsInSubscription);
            }
        }

        return contextBuilder
            .productsInOrder(products)
            .plansInOrder(plans)
            .subscription(currentSubscription)
            .plansInSubscription(plansInSubscription)
            .chargesInOrder(charges)
            .planCustomFields(planCustomFields)
            .chargeCustomFields(chargeCustomFields)
            .build();
    }

    private void throwIfTenantSettingIsNotSealed() {
        if (BooleanUtils.isTrue(billyConfiguration.getTenantSealProtectionConfiguration().getEnabled()) && !tenantSettingService.isSealed()) {
            throw new IllegalStateException(
                "Tenant settings need to be finalized/sealed for Order creation, please finish your on-boarding process and make sure tenant settings are finalized"
            );
        }
    }

    // TODO: An order request may contain more data than what is persisted in an Order object.
    //  i.e. define an OrderRequest object instead of using an Order object.
    private Order addOrderTransaction(Configuration configuration, Order order, CreateOrderContext createOrderContext, Boolean isDryRun) {
        var dslContext = DSL.using(configuration);
        OrderServiceHelper.populateOrderAndOrderLineIds(orderIdGenerator, order);
        if (order.getOrderType() == OrderType.RENEWAL) {
            Subscription subscription = subscriptionGetService.getSubscription(order.getRenewalForSubscriptionId());
            addMissingRenewalSubscriptionCharges(order, subscription);
        }
        OrderServiceHelper.fillNetEffectOrderLines(order);
        OrderServiceHelper.addOrderRankToLineItems(order);

        // we need to run customization right before preview and after validation
        OrderCreationCustomizationOutput zeppaOutput = runOrderCreationCustomization(order, createOrderContext, isDryRun);
        order.setZeppaOutput(zeppaOutput);

        calculateRenewalUpliftRatio(order, createOrderContext);
        updateOrderLineItemWithCurrencyConversionRateId(order, createOrderContext);

        InvoicePreview invoicePreview = invoiceService.previewInvoiceByOrderPeriodForOrderLinesCalculation(order);
        OrderServiceHelper.updateOrderAmountsFromInvoice(order, invoicePreview);
        OrderServiceHelper.validateOrderTotalAmount(order.getTotalAmount());

        Map<String, BigDecimal> orderLineItemAnnualizedAmounts = metricsService.getOrderLineItemAnnualizedAmounts(
            order,
            createOrderContext.getChargesInOrder()
        );
        OrderServiceHelper.updateOrderLineItemAnnualizedAmounts(order, orderLineItemAnnualizedAmounts);

        if (EvergreenUtils.isEvergreenOrder(order, featureService)) {
            Map<String, InvoiceItemAmounts> orderLineItemBillingCycleAmounts = metricsService.getOrderLineItemBillingCycleAmounts(
                order,
                createOrderContext.getChargesInOrder()
            );
            OrderServiceHelper.updateOrderTotalAndOrderLineItemAmountsForEvergreenOrder(order, orderLineItemBillingCycleAmounts);
        }

        orderCustomBillingService.validateAndSetCustomBillingDetailsInOrder(order, isDryRun);

        if (!isDryRun) {
            String tenantId = Optional.ofNullable(order.getTenantId()).orElse(tenantIdProvider.provideTenantIdString());
            Optional<Opportunity> opportunity = opportunityService.upsertOpportunityForOrderIfNeeded(dslContext, order, tenantId, true);
            boolean isPrimaryOrderForOpportunity = order.getIsPrimaryOrderForSfdcOpportunity();
            List<String> orderTemplateIds = order.getOrderFormTemplateIds();
            CustomField orderCustomFields = order.getCustomFields();

            Map<Integer, List<CustomFieldEntry>> orderLineCustomFieldsByOrderLineItemRank = OrderServiceHelper.getCustomFieldsByOrderLineItemRankMap(
                order
            );
            OrderServiceHelper.calculateAndApplyTaxEstimates(order, taxService, taxRateGetService, accountGetService, productCatalogGetService);

            // We need to store the original input values as they get reset on addOrder call
            CustomBillingSchedule customBillingSchedule = order.getCustomBillingSchedule();
            // This will be true/set only during renew subscription flows, this is needed for custom billing branching logic in invoice preview
            Boolean shouldUseCustomBillingSchedule = order.getShouldUseCustomBillingSchedule();
            List<CustomPredefinedTemplateOnOrder> customOrderTemplatesSavedOnOrder = order.getCustomPredefinedTemplatesOnOrder();

            // todo: re-eval why do we need to overwrite this object? Is this to fetch the DB generated ids here?
            // this is forcing all the fields to be saved and re-added back on order object, which is not ideal.
            order = orderDAO.addOrder(dslContext, order);
            order.setOrderFormTemplateIds(orderTemplateIds);

            // ⚠️order custom fields get overwritten while saving, so we need to add it back here ⚠️
            order.setCustomFields(orderCustomFields);
            OrderServiceHelper.addCustomFieldsBackToOrderLineItems(order, orderLineCustomFieldsByOrderLineItemRank, customizationService);

            // set the custom predefined templates back on order as they get reset during add order call
            order.setCustomPredefinedTemplatesOnOrder(customOrderTemplatesSavedOnOrder);

            // ⚠️zeppa output also get destroyed when saving order we need surface it back TODO: implement persistence ⚠️
            order.setZeppaOutput(zeppaOutput);

            order.setShouldUseCustomBillingSchedule(shouldUseCustomBillingSchedule);

            OrderServiceHelper.fillNetEffectOrderLines(order);
            OrderServiceHelper.addSfdcFieldsToOrder(order, opportunity, isPrimaryOrderForOpportunity);

            List<DocumentTemplate> orderTemplates = documentTemplateGetService.getDocumentTemplatesByTemplateIds(orderTemplateIds);
            orderDocumentTemplateService.addPredefinedTermsForOrder(order, dslContext, tenantId, orderTemplates, false);
            processCustomPredefinedTemplatesOnOrder(order, dslContext);

            orderCustomBillingService.saveOrInvalidateCustomBillingSchedule(
                dslContext,
                order,
                createOrderContext.getChargesInOrder(),
                customBillingSchedule
            );
        } else {
            // TODO: Need a better value than null for dry run
            order.setOrderId(null);
            order.getLineItems().forEach(li -> li.setOrderId(null));
        }

        // ❌❌❌ The custom field persistence is non-transactional and can fail partially ❌❌❌
        // ❌❌❌ even though it called inside the transaction method it does nothing transactional ❌❌❌
        OrderServiceHelper.persistCustomFields(order, isDryRun, customFieldService);

        return order;
    }

    private void calculateRenewalUpliftRatio(Order order, CreateOrderContext createOrderContext) {
        if (featureService.isNotEnabled(Feature.RENEWAL_UPLIFT_CUSTOMIZATION) || order.getOrderType() != OrderType.RENEWAL) {
            return;
        }

        Subscription subscription = createOrderContext
            .getSubscription()
            .orElseThrow(() -> new InvariantCheckFailedException("Subscription expected for renewal order"));
        Map<String, SubscriptionCharge> subscriptionChargeMap = subscription
            .getCharges()
            .stream()
            .collect(Collectors.toMap(SubscriptionCharge::getSubscriptionChargeId, Function.identity()));

        order
            .getLineItems()
            .forEach(lineItem -> {
                if (lineItemEligibleForRenewalUpliftCalculation(lineItem, createOrderContext)) {
                    SubscriptionCharge subscriptionCharge = subscriptionChargeMap.get(lineItem.getBaseExternalSubscriptionChargeId());
                    BigDecimal existingListPriceOverrideRatio = subscriptionCharge.getListPriceOverrideRatio() == null
                        ? BigDecimal.ONE
                        : subscriptionCharge.getListPriceOverrideRatio();
                    BigDecimal priceOverrideRatioWithUplift = lineItem.getRenewalUpliftRatio().multiply(existingListPriceOverrideRatio);
                    lineItem.setListPriceOverrideRatio(priceOverrideRatioWithUplift);
                }
            });
    }

    private boolean lineItemEligibleForRenewalUpliftCalculation(OrderLineItem lineItem, CreateOrderContext createOrderContext) {
        // if there is a user provided price override, skip renewal uplift
        if (lineItem.getListPriceOverrideRatio() != null || lineItem.getPricingOverride() != null) {
            return false;
        }

        // if no renewal uplift ratio is set, skip renewal uplift
        if (lineItem.getRenewalUpliftRatio() == null) {
            return false;
        }

        List<Charge> charges = createOrderContext.getChargesInOrder();
        Map<String, Charge> chargeIdChargeMap = charges.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        Charge charge = chargeIdChargeMap.get(lineItem.getChargeId());

        // if charge list price is not editable, skip renewal uplift
        if (!charge.getIsListPriceEditable()) {
            return false;
        }

        // if there is an existing subscription charge item that the order item is renewing from, then eligible for uplift
        return lineItem.getBaseExternalSubscriptionChargeId() != null;
    }

    private OrderCreationCustomizationOutput runOrderCreationCustomization(Order order, CreateOrderContext createOrderContext, Boolean isDryRun) {
        if (order.shouldSkipOrderCreationCustomization()) {
            LOGGER.info("Skipping order creation customization for order: ", order);
            return OrderCreationCustomizationOutput.skipped();
        }

        Account account = createOrderContext.getOrderAccount();
        Optional<User> currentUserOptional = CurrentUserProvider.getSubskribeUserId().flatMap(userService::getUserOptional);
        OrderCreationCustomizationInput input = ImmutableOrderCreationCustomizationInput.builder()
            .order(order)
            .account(account)
            .accountAddress(
                StringUtils.isBlank(account.getAddressId()) ? Optional.empty() : accountGetService.getAccountAddress(account.getAddressId())
            )
            .productMap(createOrderContext.getProductsInOrder().stream().collect(Collectors.toMap(Product::getProductId, Function.identity())))
            .planMap(createOrderContext.getPlansInOrder().stream().collect(Collectors.toMap(Plan::getPlanId, Function.identity())))
            .chargeMap(createOrderContext.getChargesInOrder().stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity())))
            .planCustomFields(createOrderContext.getPlanCustomFields())
            .chargeCustomFields(createOrderContext.getChargeCustomFields())
            .dryRun(BooleanUtils.isTrue(isDryRun))
            .currentUser(currentUserOptional)
            .shippingContact(createOrderContext.getShippingContact())
            .billingContact(createOrderContext.getBillingContact())
            .billingContactAccount(createOrderContext.getBillingContactAccount())
            .timeZoneId(tenantSettingService.getTenantSetting().getDefaultTimeZone().toZoneId())
            .build();
        return customizationService.runOrderCreationCustomization(input);
    }

    /**
     * facade method that moves an order to executed based on current order state
     * @param orderId the orderId that needs to be moved to executed
     */
    public void moveOrderToExecuted(String orderId, Long executedOn, boolean adminApprovalFlowBypass) {
        Order order = orderGetService.getOrderByOrderId(orderId);

        while (order.getStatus() != OrderStatus.EXECUTED) {
            order = orderGetService.getOrderByOrderId(orderId);
            switch (order.getStatus()) {
                case APPROVED -> {
                    Optional<Instant> executedOnInstant = Optional.ofNullable(executedOn).map(Instant::ofEpochSecond);
                    updateOrderStatus(orderId, OrderStatus.EXECUTED, executedOnInstant, adminApprovalFlowBypass);
                    // if move order to EXECUTED is successful we can avoid another read just return
                    return;
                }
                case SUBMITTED -> updateOrderStatus(orderId, OrderStatus.APPROVED, Optional.empty(), adminApprovalFlowBypass);
                case DRAFT -> updateOrderStatus(orderId, OrderStatus.SUBMITTED, Optional.empty(), adminApprovalFlowBypass);
                case EXPIRED -> {
                    if (adminApprovalFlowBypass) {
                        // if executing imported orders, then allow expired orders to be executed
                        updateOrderStatus(orderId, OrderStatus.SUBMITTED, Optional.empty(), adminApprovalFlowBypass);
                    } else {
                        throw new InvalidInputException(String.format("Expired order %s cannot be executed", orderId));
                    }
                }
                default -> throw new IllegalStateException(String.format("order id:%s is in unknown state %s", orderId, order.getStatus()));
            }
        }
    }

    private void createJobToGenerateRecognitionSchedule(Order order) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        revenueRecognitionJobService.createRecognitionScheduleJob(tenantId, TransactionType.ORDER, order.getOrderId(), order.getEntityId());
    }

    public void updateOrderStatus(String orderId, OrderStatus newOrderStatus) {
        updateOrderStatus(orderId, newOrderStatus, Optional.empty(), false);
    }

    public void updateOrderStatus(String orderId, OrderStatus newOrderStatus, Optional<Instant> statusUpdatedOn, boolean adminApprovalFlowBypass) {
        if (!adminApprovalFlowBypass && newOrderStatus == OrderStatus.APPROVED) {
            throw new IllegalArgumentException("Cannot move the order to approved state. It has to go through Approval flow process");
        }

        updateOrderStatusInternal(orderId, newOrderStatus, statusUpdatedOn, adminApprovalFlowBypass);
    }

    public void updateOrderStatusInternal(String orderId, OrderStatus newOrderStatus, Optional<Instant> statusUpdatedOn) {
        updateOrderStatusInternal(orderId, newOrderStatus, statusUpdatedOn, false);
    }

    private void updateOrderStatusInternal(
        String orderId,
        OrderStatus newOrderStatus,
        Optional<Instant> statusUpdatedOn,
        boolean adminApprovalFlowBypass
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        TimeZone tenantTimeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        tenantDslContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            tryAcquireAdvisoryLock(orderId, dslContext);

            // fetch the latest order after the advisory lock
            Order currentOrderInDb = orderGetService.getOrderByOrderId(orderId);
            Order updatedOrder = updateOrderStatusInternalInTransaction(
                currentOrderInDb,
                newOrderStatus,
                statusUpdatedOn,
                dslContext,
                tenantTimeZone
            );
            orderEventService.publishEventsForOrderUpdate(updatedOrder, dslContext);
        });

        Order savedOrder = orderGetService.getOrderByOrderId(orderId);
        if (savedOrder.getStatus() == OrderStatus.EXPIRED) {
            throw new IllegalStateException("Order has expired: " + savedOrder.getOrderId());
        }

        triggerApprovalFlowChecksForAutoApprovedOrCancelled(savedOrder, tenantDslContext, adminApprovalFlowBypass);
        triggerOrderUpdateDownstreamDependencies(savedOrder);

        cancelOpenSalesRoomIfOrderIsRevertedBackToDraft(savedOrder);
    }

    private void cancelOpenSalesRoomIfOrderIsRevertedBackToDraft(Order order) {
        if (order.getStatus() != OrderStatus.DRAFT) {
            return;
        }

        Optional<SalesRoomLink> salesRoomLink = salesRoomLinkService.getLinkByOrderId(order.getOrderId());
        if (salesRoomLink.isEmpty()) {
            return;
        }

        salesRoomLinkService.disableLink(salesRoomLink.get().getLinkId());
    }

    private void tryAcquireAdvisoryLock(String orderId, DSLContext dslContext) {
        String lockKey = String.format(UPDATE_ORDER_STATUS_LOCK_KEY_FORMAT, tenantIdProvider.provideTenantIdString(), orderId);

        String advisoryLockErrorMessage = String.format(
            "Another update in progress when updating the status for order: %s. Please retry again.",
            orderId
        );

        ConflictingStateException conflictingStateException = new ConflictingStateException(System.Logger.Level.WARNING, advisoryLockErrorMessage);
        PostgresAdvisoryLock.tryAcquireLockOrThrow(dslContext, lockKey, () -> conflictingStateException);
    }

    public Order updateOrderStatusInternalInTransaction(
        Order order,
        OrderStatus newOrderStatus,
        Optional<Instant> statusUpdatedOn,
        DSLContext dslContext,
        TimeZone tenantTimeZone
    ) {
        OrderServiceHelper.validateCurrentAuthPrincipalCanExecuteOrders(newOrderStatus);
        validateUpdateOrderStatusRequest(order, newOrderStatus);
        OrderServiceHelper.validateOrderExpiryDate(order, newOrderStatus, tenantTimeZone);
        if (order.getStatus() == OrderStatus.EXPIRED) {
            return updateOrderStatusInDB(dslContext, order);
        }
        validateSubscriptionIfPresent(order, newOrderStatus);
        if (newOrderStatus != OrderStatus.DRAFT) {
            validateAndFetchRenewalSubscription(order);
        }

        OrderServiceHelper.validateOrderStatusTransition(order.getStatus(), newOrderStatus);
        order.setStatus(newOrderStatus);
        orderValidationService.validateAccountDetails(order);
        orderValidationService.validateContactsOnOrder(order);

        if (newOrderStatus == OrderStatus.SUBMITTED) {
            OrderServiceHelper.addOrderSubmittedBy(order);
        }

        return prepareAndUpdateOrderStatusInDB(order, statusUpdatedOn, dslContext);
    }

    private void validateSubscriptionIfPresent(Order order, OrderStatus newOrderStatus) {
        if (StringUtils.isBlank(order.getExternalSubscriptionId())) {
            return;
        }

        var subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
        int nextSubscriptionVersion = subscriptionGetService.getNextSubscriptionVersion(subscription.getSubscriptionId());
        if (order.getSubscriptionTargetVersion() != nextSubscriptionVersion && newOrderStatus != OrderStatus.DRAFT) {
            var message = "Subscription has been updated since the time this order was created. Please delete this order and recreate.";
            LOGGER.info("{} OrderId = {}", message, order.getOrderId());
            throw new IllegalArgumentException(message);
        }

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        ZoneId accountTimeZoneId = timeZone.toZoneId();
        Map<String, Charge> chargeMap = OrderServiceHelper.createChargeMap(order, subscription, productCatalogGetService);

        if (!(newOrderStatus == OrderStatus.DRAFT || newOrderStatus == OrderStatus.EXPIRED)) {
            // we shouldn't validate whether something is invoiced or not when the order is moving back to the above states
            OrderServiceHelper.validateChangeOrderWithUsage(order, subscription, invoiceService, chargeMap, accountTimeZoneId);
        }
    }

    private Order prepareAndUpdateOrderStatusInDB(Order order, Optional<Instant> statusUpdatedOn, DSLContext dslContext) {
        if (order.getStatus() == OrderStatus.EXECUTED) {
            order.setExecutedOn(statusUpdatedOn.orElse(Instant.now()));

            if (order.getStartDateType() == OrderStartDateType.EXECUTION_DATE) {
                TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
                Instant startOfExecutionDate = DateTimeConverter.getStartOfCurrentDay(order.getExecutedOn(), timeZone);
                order.setStartDate(startOfExecutionDate);

                // Reset the end date and billing anchor date, and let the system calculate them
                order.setEndDate(null);
                order.setBillingAnchorDate(null);
                validateAndSetOrderDates(order, timeZone);

                order
                    .getLineItems()
                    .forEach(orderLineItem -> {
                        resetOrderLineItemDates(orderLineItem);

                        // Temporarily set the end date to the order end date for validation. The actual end date will be set by the system.
                        orderLineItem.setEndDate(order.getEndDate());

                        Charge charge = productCatalogGetService.getChargeByChargeId(orderLineItem.getChargeId());
                        OrderServiceHelper.validateLineItemDates(
                            featureService,
                            orderLineItem,
                            charge,
                            order.getStartDate(),
                            order.getEndDate(),
                            timeZone.toZoneId()
                        );
                    });
            }
            OrderServiceHelper.validateEsignStatus(order, esignService);
        }

        OrderServiceHelper.validateApprovalSegmentIdInOrder(order, approvalFlowInstanceService);
        return updateOrderStatusInDB(dslContext, order);
    }

    public void triggerOrderUpdateDownstreamDependencies(Order order) {
        // all the below actions are candidates for Eventing 2.0
        documentLinkService.deleteDocumentLink(order.getOrderId());
        triggerOrderDependenciesWhenExecuted(order);

        // below jobs for CRM sync are dependent on `triggerOrderDependenciesWhenExecuted`, which marks the order primary on opportunity
        hubSpotJobQueueService.dispatchOrderSync(order);
        salesforceJobQueueService.dispatchOrderSync(order);
    }

    private void triggerOrderDependenciesWhenExecuted(Order order) {
        if (OrderStatus.EXECUTED != order.getStatus()) {
            return;
        }

        // kick off ASC606 rev-rec job
        if (platformFeatureService.isFeatureEnabledAndReady(PlatformFeature.REVENUE_RECOGNITION)) {
            createJobToGenerateRecognitionSchedule(order);
        }

        invoiceService.triggerOrderExecutionInvoiceDependencies(order.getOrderId());

        String crmOpportunityId = order.getSfdcOpportunityId();
        if (crmOpportunityId != null) {
            opportunityService.updatePrimaryOrderIdForOpportunity(order);
            opportunityService.closeOpportunityAndThrowIfAlreadyClosed(crmOpportunityId);
        }

        reportingJobQueueService.addOrderLineArrJobs(order);
    }

    public void triggerApprovalFlowChecksForAutoApprovedOrCancelled(Order order, DSLContext dslContext, boolean adminApprovalFlowBypass) {
        if (order.getStatus() != OrderStatus.DRAFT && order.getStatus() != OrderStatus.SUBMITTED) {
            return;
        }

        List<ApprovalFlowInstanceGroup> approvalFlowInstanceGroups = OrderServiceHelper.createOrUpdateApprovalFlowsOnOrder(
            approvalFlowInstanceService,
            order,
            adminApprovalFlowBypass
        );

        // Special handling due to sync calls.
        // todo: make this async by pushing this flow to a queue
        if (CollectionUtils.isEmpty(approvalFlowInstanceGroups)) {
            return;
        }

        orderEventService.publishOrderEvent(EventType.ORDER_APPROVAL_FLOWS_EVALUATED, order, dslContext);

        // Sort by recent versions first
        approvalFlowInstanceGroups.sort(Comparator.comparingInt(ApprovalFlowInstanceGroup::getVersion).reversed());
        if (
            order.getStatus() == OrderStatus.SUBMITTED && approvalFlowInstanceGroups.get(0).getApprovalStatus() == ApprovalFlowInstanceStatus.APPROVED
        ) {
            order.setStatus(OrderStatus.APPROVED);
            prepareAndUpdateOrderStatusInDB(order, Optional.empty(), dslContext);
            orderEventService.publishOrderEvent(EventType.ORDER_APPROVED, order, dslContext);
        }
    }

    private void validateUpdateOrderStatusRequest(Order order, OrderStatus newOrderStatus) {
        if (order == null) {
            throw new IllegalArgumentException("order id is null");
        }

        if (newOrderStatus == null) {
            throw new IllegalArgumentException(
                "Invalid Order status. Order status should be either DRAFT, SUBMITTED, APPROVED, EXECUTED or CANCELLED"
            );
        }

        if (order.getStatus() != OrderStatus.DRAFT && order.getStatus() == newOrderStatus) {
            throw new IllegalStateException(String.format("Order %s is already in %s state", order.getOrderId(), newOrderStatus.name()));
        }

        if (StringUtils.isNotBlank(order.getSfdcOpportunityId()) && newOrderStatus == OrderStatus.EXECUTED) {
            opportunityGetService.throwIfOpportunityIsAlreadyClosed(order.getSfdcOpportunityId());
        }
    }

    private Order updateOrderStatusInDB(DSLContext dslContext, Order order) {
        return dslContext.transactionResult(configuration -> {
            DSLContext dslTransactionContext = DSL.using(configuration);
            if (order.getStatus() == OrderStatus.EXECUTED) {
                Subscription subscription = upsertSubscription(dslTransactionContext, order);
                addDefaultEmailContact(dslTransactionContext, subscription);
            }

            return orderDAO.updateOrder(dslTransactionContext, order);
        });
    }

    private Subscription upsertSubscription(DSLContext dslContext, Order order) {
        Subscription subscription;
        if (StringUtils.isBlank(order.getExternalSubscriptionId())) {
            subscription = subscriptionService.createSubscription(dslContext, order);
        } else {
            subscription = subscriptionService.updateSubscription(dslContext, order);
        }
        order.setSubscriptionUuid(subscription.getId());
        order.setExternalSubscriptionId(subscription.getSubscriptionId());
        OrderServiceHelper.updateSubscriptionChargeIdInOrderLine(order, subscription);
        if (order.getExecutedOn() == null) {
            order.setExecutedOn(Instant.now());
        }
        orderEventService.publishEventsForSubscriptionCreated(subscription, dslContext);
        return subscription;
    }

    private void addDefaultEmailContact(DSLContext dslContext, Subscription subscription) {
        emailContactListService.addDefaultEmailContact(dslContext, subscription);
        emailContactListService.addDefaultEmailContactAtSubscription(dslContext, subscription);
    }

    /*
        This method is tested by class OrderValidationTest
     */
    public void validateOrder(Order order, CreateOrderContext createOrderContext, boolean isDryRun) {
        Validator.validateNonNullArgument(order, "order");

        boolean allowNonDraftUpdate = order.getOrderProcessingMetadata().map(OrderProcessingMetadata::allowNonDraftUpdate).orElse(false);
        if (!allowNonDraftUpdate) {
            order.setExternalSubscriptionId(null);
        }
        order.setSubscriptionTargetVersion(SUBSCRIPTION_TARGET_VERSION_NEW);
        order.setTotalAmount(BigDecimal.ZERO);

        // current subscription is expected to be null for new orders
        Subscription currentSubscription = validateAndFetchRenewalSubscription(order);
        if (order.getOrderType() == OrderType.RENEWAL && currentSubscription != null) {
            order.setRenewalForSubscriptionVersion(currentSubscription.getVersion());
        }

        OrderServiceHelper.validateOrderName(order);
        OrderServiceHelper.validateOpportunity(order);
        orderValidationService.validateAccountDetails(order);
        OrderServiceHelper.validateOrderEntityScope(order, createOrderContext.getChargesInOrder());
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        validateExternalId(order);
        validateBillingAndPayment(order);
        validateAndSetOrderDates(order, timeZone);
        OrderServiceHelper.validatePurchaseOrder(order);
        OrderDiscountService.validateOrderDiscounts(order, discountService, Optional.empty());
        validateLineItems(order, createOrderContext, currentSubscription, timeZone.toZoneId(), isDryRun);
        validateOrderTemplateIds(order);
        orderDocumentTemplateService.validateDocumentMasterTemplate(order);
        orderDocumentTemplateService.findAndAddMissingPlanTemplates(order);
        orderDocumentTemplateService.validateOrderTemplate(order);
        orderDocumentTemplateService.filterOutPredefinedTermsBroughtByPlans(order, createOrderContext.getPlansInOrder());
        OrderServiceHelper.validateRampIntervals(order);
        OrderServiceHelper.validateApprovalSegmentIdInOrder(order, approvalFlowInstanceService);
        OrderServiceHelper.validateOrderAttachment(order, attachmentsService);
        OrderServiceHelper.checkCrmIdIsEmptyForCompositeOrders(order);
        validateExecutionDateBasedOrder(order);
        checkProductRules(order, isDryRun);
        EvergreenUtils.validateEvergreenOrder(order, featureService);
    }

    private Subscription validateAndFetchRenewalSubscription(Order order) {
        if (order.getOrderType() != OrderType.RENEWAL) {
            return null;
        }

        Validator.validateStringNotBlank(order.getRenewalForSubscriptionId(), "RenewalSubscriptionId cannot be blank for Renewals");
        Subscription subscription = subscriptionGetService.getSubscription(order.getRenewalForSubscriptionId());

        if (subscription.getCanceledDate() != null) {
            throw new IllegalArgumentException(
                "Subscription cannot be renewed as it is cancelled. SubscriptionId:" + subscription.getSubscriptionId()
            );
        }

        if (StringUtils.isNotBlank(subscription.getRenewedToSubscriptionId())) {
            // We allow subscription to be renewed again if the renewed subscription is fully cancelled
            Subscription renewedSubscription = subscriptionGetService.getSubscription(subscription.getRenewedToSubscriptionId());
            boolean allowNonDraftUpdate = order.getOrderProcessingMetadata().map(OrderProcessingMetadata::allowNonDraftUpdate).orElse(false);
            if (
                (renewedSubscription.getCanceledDate() == null ||
                    !renewedSubscription.getCanceledDate().equals(renewedSubscription.getStartDate())) &&
                !allowNonDraftUpdate
            ) {
                throw new IllegalArgumentException("Subscription is already renewed and the renewed subscription isn't fully cancelled.");
            }
        }

        if (StringUtils.isBlank(order.getOrderId())) {
            return subscription;
        }

        // validate the version of the saved order to check if this order is still valid
        OrderStub existingOrder = orderGetService.getOrderStubOrThrow(order.getOrderId());
        if (subscription.getVersion() != existingOrder.getRenewalForSubscriptionVersion().orElse(0)) {
            throw new IllegalStateException("Cannot update the order as the renewal subscription has been modified after this order is created.");
        }

        return subscription;
    }

    private void validateExternalId(Order order) {
        if (order.getExternalId() != null && StringUtils.isBlank(order.getExternalId())) {
            throw new IllegalArgumentException("order external id cannot be blank");
        }
    }

    private void validateBillingAndPayment(Order order) {
        if (order.getBillingCycle() == null) {
            throw new IllegalArgumentException("billing cycle is null");
        }

        if (order.getBillingCycle().getStep() == null || order.getBillingCycle().getStep() <= 0) {
            throw new IllegalArgumentException("billing cycle step should not be null and be greater than 0");
        }

        if (order.getBillingTerm() == null) {
            throw new IllegalArgumentException("invalid billing term, should be UP_FRONT or IN_ARREARS");
        }

        if (!featureService.isEnabled(Feature.CUSTOM_BILLING) && order.getBillingCycle().getCycle() == Cycle.CUSTOM) {
            throw new InvalidInputException("Custom billing cycle is not enabled");
        }
        validatePaymentTerm(order);
    }

    private void validatePaymentTerm(Order order) {
        PaymentTermSettings paymentTermSettings = paymentTermSettingsService.getPaymentTermSettings();
        if (
            order.getPaymentTerm() == null ||
            (BooleanUtils.isFalse(paymentTermSettings.isCustomPaymentTermsAllowed()) &&
                !paymentTermSettings.getDefaultPaymentTerms().contains(order.getPaymentTerm()))
        ) {
            throw new IllegalArgumentException(
                String.format("invalid payment term, should be one of %s", paymentTermSettings.getDefaultPaymentTerms())
            );
        }
    }

    private void validateAndSetOrderDates(Order order, TimeZone timeZone) {
        if (order.getStartDate() == null) {
            var message = "order start date cannot be null";
            throw new InvalidInputException(message);
        }

        if (order.getStartDate().isAfter(MAX_ALLOWED_DATE)) {
            var message = "invalid start date too far away. Send a valid start date in epoch seconds";
            throw new InvalidInputException(message);
        }

        EvergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone, featureService);

        if (order.getBillingAnchorDate() == null) {
            order.setBillingAnchorDate(order.getStartDate());
        }

        Recurrence billingCycle = order.getBillingCycle();
        if (featureService.isEnabled(Feature.CUSTOM_BILLING) && order.getBillingCycle().getCycle() == Cycle.CUSTOM) {
            billingCycle = validateAndGetBillingCycleForCustomBilling(order);
        }

        validateAndSetOrderEndDate(order, timeZone);

        subscriptionBillingPeriodService.validateBillingAnchorDate(
            order.getStartDate(),
            order.getEndDate(),
            order.getBillingAnchorDate(),
            billingCycle,
            timeZone.toZoneId()
        );
    }

    public Recurrence validateAndGetBillingCycleForCustomBilling(Order order) {
        CustomBillingSchedule customBillingSchedule = order.getCustomBillingSchedule();

        boolean shouldUseCustomBillingSchedule = orderCustomBillingService.shouldUseCustomBillingInvoicingInput(order);

        if (Objects.isNull(customBillingSchedule) && shouldUseCustomBillingSchedule) {
            throw new InvalidInputException("Custom billing schedule is required for custom billing cycle");
        }

        if (order.getBillingAnchorDate() != null && order.getBillingAnchorDate().compareTo(order.getStartDate()) != 0) {
            throw new InvalidInputException("Billing anchor date should be empty or same as start date for custom billing cycle");
        }

        // TODO : Handle validation of billing anchor date for recurrence based custom billing later on, presently it's only PAID_IN_FULL
        // If there are no eligible lines for custom billing we won't have a custom billing schedule for the order
        return new Recurrence(Cycle.PAID_IN_FULL, 1);
    }

    private void validateAndSetOrderEndDate(Order order, TimeZone timeZone) {
        if (EvergreenUtils.isEvergreenOrder(order, featureService)) {
            Instant lastRampInterval = Optional.ofNullable(order.getRampInterval())
                .filter(CollectionUtils::isNotEmpty)
                .map(Collections::max)
                .orElse(null);
            order.setEndDate(EvergreenUtils.EVERGREEN_SENTINEL_END_DATE);
            order
                .getLineItems()
                .forEach(lineItem -> {
                    if (!lineItem.getIsRamp() || lineItem.getEffectiveDate().equals(lastRampInterval)) {
                        lineItem.setEndDate(EvergreenUtils.EVERGREEN_SENTINEL_END_DATE);
                    }
                });

            return;
        }

        if (order.getTermLength() == null && order.getEndDate() == null) {
            throw new InvalidInputException("At least one of termLength or endDate is expected");
        }

        if (order.getTermLength() != null) {
            validateAndSetOrderEndDateFromTermLength(order, timeZone);
        }

        Validator.checkNonNullInternal(order.getEndDate(), "endDate");
        if (order.getEndDate().isBefore(order.getStartDate())) {
            var message = "order end date cannot be before order start date";
            throw new InvalidInputException(message);
        }
    }

    @VisibleForTesting
    static void validateAndSetOrderEndDateFromTermLength(Order order, TimeZone timeZone) {
        Validator.checkNonNullInternal(order.getTermLength(), "termLength");

        if (order.getTermLength().getStep() == null || order.getTermLength().getCycle() == null) {
            throw new InvalidInputException("termLength cycle and step are required");
        }

        if (order.getTermLength().getStep() <= 0) {
            throw new InvalidInputException("termLength step must be greater than 0");
        }

        Instant endDateFromTermLength = SubscriptionBillingPeriodService.getBillingEndDate(
            order.getStartDate(),
            timeZone.toZoneId(),
            order.getTermLength()
        );
        if (order.getEndDate() != null && DateTimeCalculator.compareInstants(endDateFromTermLength, order.getEndDate()) != 0) {
            String errorMsg = String.format(
                "termLength {cycle=%s, step=%s} end date %s does not match provided endDate %s",
                order.getTermLength().getCycle(),
                order.getTermLength().getStep(),
                endDateFromTermLength.getEpochSecond(),
                order.getEndDate().getEpochSecond()
            );
            throw new InvalidInputException(errorMsg);
        }
        order.setEndDate(endDateFromTermLength);
    }

    private void validateLineItems(
        Order order,
        CreateOrderContext createOrderContext,
        Subscription currentSubscription,
        ZoneId zoneId,
        boolean isDryRun
    ) {
        if (isDryRun && CollectionUtils.isEmpty(order.getLineItems())) {
            order.setLineItems(List.of());
            return;
        }
        if (order.getOrderType() != OrderType.RENEWAL && CollectionUtils.isEmpty(order.getLineItems())) {
            throw new InvalidInputException("Order line items required.");
        }

        var plans = createOrderContext.getPlansInOrder();

        OrderServiceHelper.validateCurrencyOnPlans(currencyConversionRateGetService, order.getCurrency(), plans);

        var plansInOrderMap = plans.stream().collect(Collectors.toMap(Plan::getPlanId, Function.identity()));

        if (plansInOrderMap.values().stream().anyMatch(plan -> !ALLOWED_PLAN_STATUS.contains(plan.getStatus()))) {
            throw new IllegalArgumentException("Only active plans can be used to create a new subscription order");
        }

        List<String> existingPlanIdsInSubscription = createOrderContext.getPlansInSubscription().stream().map(Plan::getPlanId).toList();
        order.getLineItems().forEach(li -> validateLineItem(order, li, plansInOrderMap, zoneId, existingPlanIdsInSubscription));

        OrderServiceHelper.validateDrawdownCharges(order, plans, Optional.empty());

        // validateAllChargesInPlanExist must execute before validateUniqueUsageChargePerOrder
        // for new orders, all the charges in the plans are expected to be present in the order
        // for renewals, new plans or existing plans added with ADD action are expected to have all the charges in the order
        // and any of the charges added with renewal action are supposed to have only the renewable charges from the subscription
        List<OrderLineItem> renewingOrderLines = order
            .getLineItems()
            .stream()
            .filter(orderLineItem -> orderLineItem.getAction() == ActionType.RENEWAL)
            .collect(Collectors.toList());

        Sequencer.runInOrder(
            () -> OrderServiceHelper.validateAllChargesInNewPlansExist(order.getLineItems(), plans, existingPlanIdsInSubscription),
            () -> validateRenewingOrderLineItems(order.getOrderType(), renewingOrderLines, currentSubscription, plans),
            () -> OrderServiceHelper.validateUniqueUsageChargePerOrder(order, plans)
        );

        var chargeIdLineItemMap = order.getLineItems().stream().collect(groupingBy(OrderLineItem::getChargeId));
        var chargesMap = plans.stream().flatMap(p -> p.getCharges().stream()).collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        chargeIdLineItemMap
            .keySet()
            .forEach(c ->
                OrderServiceHelper.updateLineItemEndDateByCharge(
                    featureService,
                    chargeIdLineItemMap.get(c),
                    chargesMap.get(c),
                    order.getStartDate(),
                    order.getEndDate(),
                    zoneId
                )
            );

        List<BillingPeriod> billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            order.getStartDate(),
            order.getEndDate(),
            order.getBillingAnchorDate() != null ? order.getBillingAnchorDate() : order.getStartDate(),
            order.getEndDate(),
            zoneId,
            order.getBillingCycle(),
            BillingTerm.UP_FRONT
        );
        List<Instant> billingPeriodStartDates = billingPeriods.stream().map(BillingPeriod::getStart).toList();
        OrderServiceHelper.validateMinimumCommitLineItems(order.getLineItems(), chargesMap, billingPeriodStartDates);

        OrderServiceHelper.updateFlatFeeQuantityInOrder(order, plans);
    }

    private void validateLineItem(
        Order order,
        OrderLineItem lineItem,
        Map<String, Plan> plansMap,
        ZoneId zoneId,
        List<String> existingPlanIdsInSubscription
    ) {
        Validator.validateNonNullArgument(lineItem.getPlanId(), "planId");
        Validator.validateNonNullArguments(lineItem.getChargeId(), "chargeId");

        Plan plan = Optional.ofNullable(plansMap.get(lineItem.getPlanId())).orElseThrow(() ->
            new IllegalArgumentException(String.format("plan %s provided in the order line item do not exist", lineItem.getPlanId()))
        );
        Charge charge = plan
            .getCharges()
            .stream()
            .filter(c -> lineItem.getChargeId().equals(c.getChargeId()))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException(String.format("charge id %s does not exist", lineItem.getChargeId())));

        OrderServiceHelper.validateOrderLineItemPriceAttribution(lineItem, charge, rateCardService);
        OrderServiceHelper.validateLineItemDates(featureService, lineItem, charge, order.getStartDate(), order.getEndDate(), zoneId);
        OrderDiscountService.validateDiscountList(lineItem, billyConfiguration.getFixedAmountDiscountConfiguration());
        OrderServiceHelper.validateDeprecatedPlan(plan, existingPlanIdsInSubscription);
        OrderServiceHelper.validateListPriceOverride(lineItem, charge);
        OrderServiceHelper.validatePricingOverride(lineItem, charge, featureService);
        OrderServiceHelper.validateLineItemQuantity(lineItem, charge, order.getOrderType());
        OrderServiceHelper.validateArrOverride(lineItem, charge, featureService);
        OrderDiscountService.validateDiscountLine(lineItem, charge);

        if (charge.isCustom()) {
            OrderServiceHelper.validateCustomChargeLineItem(lineItem);
        } else {
            lineItem.setListUnitPrice(BigDecimal.ZERO);
        }
        lineItem.setSellUnitPrice(BigDecimal.ZERO);
        lineItem.setListAmount(BigDecimal.ZERO);
        lineItem.setAmount(BigDecimal.ZERO);
        if (lineItem.getAction() != null) {
            validateActionType(lineItem.getAction());
        } else {
            lineItem.setAction(ActionType.ADD);
        }

        lineItem.setExternalSubscriptionChargeId(null);
    }

    private void validateActionType(ActionType action) {
        Validator.validateNonNullArgument(action, "action type cannot be null");

        if (!ALLOWED_ACTION_TYPES_WITH_ORDER_SERVICE_ROUTE.contains(action)) {
            throw new InvalidInputException(
                "Invalid action type: " + action + ". Allowed action types are: " + ALLOWED_ACTION_TYPES_WITH_ORDER_SERVICE_ROUTE
            );
        }
    }

    private void validateRenewingOrderLineItems(
        OrderType orderType,
        List<OrderLineItem> renewingOrderLines,
        Subscription currentSubscription,
        List<Plan> plans
    ) {
        if (CollectionUtils.isEmpty(renewingOrderLines)) {
            return;
        }

        if (orderType != OrderType.RENEWAL) {
            throw new IllegalArgumentException("Renewal action lineItems found with no renewal order");
        }

        Set<String> chargeIdsInSubscription = currentSubscription
            .getCharges()
            .stream()
            .map(SubscriptionCharge::getChargeId)
            .collect(Collectors.toSet());
        Set<String> chargeIdsInRenewals = renewingOrderLines.stream().map(OrderLineItem::getChargeId).collect(Collectors.toSet());
        Map<String, Charge> chargesInPlansByIdMap = plans
            .stream()
            .flatMap(plan -> plan.getCharges().stream())
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        // make sure all the charges in the renewalOrderLines are renewable charges
        boolean checkAllChargesAreRenewable = chargeIdsInRenewals.stream().allMatch(charge -> chargesInPlansByIdMap.get(charge).getIsRenewable());
        if (!checkAllChargesAreRenewable) {
            Set<String> nonRenewableCharges = chargeIdsInRenewals
                .stream()
                .filter(chargeId -> !chargesInPlansByIdMap.get(chargeId).getIsRenewable())
                .collect(Collectors.toSet());
            throw new IllegalArgumentException("Non Renewable charges found in renewal. ChargeIds: " + String.join(", " + nonRenewableCharges));
        }

        // validate charge ids are present in subscription
        chargeIdsInRenewals.removeAll(chargeIdsInSubscription);
        if (CollectionUtils.isNotEmpty(chargeIdsInRenewals)) {
            throw new IllegalArgumentException("Renewal charge ids missing in subscription: " + String.join(", ", chargeIdsInRenewals));
        }
    }

    private void validateOrderTemplateIds(Order order) {
        if (order.getOrderFormTemplateIds() == null) {
            order.setOrderFormTemplateIds(List.of());
        }
    }

    private void validateExecutionDateBasedOrder(Order order) {
        if (order.getStartDateType() != OrderStartDateType.EXECUTION_DATE) {
            return;
        }

        // TODO: Remove the feature flag check but keep the tenant setting check when the feature is ready
        if (!featureService.isEnabled(Feature.UPDATE_ORDER_START_DATE) || !tenantSettingService.getIsUpdateOrderStartDateEnabled()) {
            throw new UnsupportedOperationException("Execution date based orders are not supported");
        }

        if (order.getOrderType() != OrderType.NEW) {
            throw new InvalidInputException("Execution date based orders are only allowed for new orders");
        }

        if (order.getTermLength() == null && order.getEndDate() != null) {
            throw new InvalidInputException("Date range cannot be specified for execution date based orders");
        }

        if (order.getTermLength() != null && order.getTermLength().getCycle() != Cycle.YEAR) {
            throw new InvalidInputException("Term length cycle should be YEAR for execution date based orders");
        }

        if (CollectionUtils.isNotEmpty(order.getRampInterval())) {
            throw new InvalidInputException("Ramp interval is not allowed for execution date based orders");
        }
    }

    public Order upsertOrder(Order order, Boolean isDryRun, Boolean sanitizeLineItemDates) {
        if (order == null) {
            throw new IllegalArgumentException("Order request object is null");
        }

        if (sanitizeLineItemDates && CollectionUtils.isNotEmpty(order.getLineItems())) {
            order.getLineItems().forEach(this::resetOrderLineItemDates);
        }

        OrderServiceHelper.validateAndUpdateOpportunityInfo(order);

        Order resultOrder;
        if (order.getOrderType() == OrderType.RENEWAL && order.getOrderId() == null && CollectionUtils.isEmpty(order.getLineItems())) {
            resultOrder = renewSubscription(order, isDryRun);
        } else if (order.getOrderId() == null) {
            resultOrder = addOrder(order, isDryRun);
        } else {
            resultOrder = updateOrder(order, isDryRun);
        }

        if (!isDryRun) {
            resultOrder.setDocumentCustomContent(order.getDocumentCustomContent());
            resultOrder.setCustomPredefinedTemplatesOnOrder(order.getCustomPredefinedTemplatesOnOrder());
            syncUpdatedOrderToCRM(resultOrder);
            OrderServiceHelper.processDocumentCustomContent(resultOrder, documentCustomContentService);
        }
        return resultOrder;
    }

    private void processCustomPredefinedTemplatesOnOrder(Order order, DSLContext dslContext) {
        if (order.getCustomPredefinedTemplatesOnOrder() == null) {
            return;
        }
        order
            .getCustomPredefinedTemplatesOnOrder()
            .forEach(customPredefinedTemplate -> {
                customPredefinedTemplate.setOrderId(order.getOrderId());
                CustomTemplateUpdateOnOrder inputTemplate = customPredefinedTemplateOnOrderMapper.toCustomTemplateUpdateOnOrder(
                    customPredefinedTemplate
                );
                customTemplateUpdatedOnOrderService.upsertInTransaction(inputTemplate, dslContext);
            });
    }

    private void syncUpdatedOrderToCRM(Order order) {
        hubSpotJobQueueService.dispatchOrderSync(order);
        salesforceJobQueueService.dispatchOrderSync(order);
    }

    private void resetOrderLineItemDates(OrderLineItem orderLineItem) {
        orderLineItem.setEffectiveDate(null);
        orderLineItem.setEndDate(null);
    }

    private void resetOrderLineItemIds(OrderLineItem orderLineItem) {
        orderLineItem.setId(null);
        orderLineItem.setOrderLineId(AutoGenerate.getNewId());
        orderLineItem.setOrderId(null);
        orderLineItem.setExternalSubscriptionChargeId(null);
        orderLineItem.setSubscriptionChargeGroupId(null);
        orderLineItem.setSubscriptionChargeId(null);
        orderLineItem.setBaseExternalSubscriptionChargeId(null);
        orderLineItem.setMetrics(null);
    }

    public Order updateOrder(Order order, Boolean isDryRun) {
        if (order.getOrderId() == null) {
            throw new IllegalArgumentException("order id is null");
        }

        var tenantId = tenantIdProvider.provideTenantIdString();
        order.getLineItems().forEach(li -> li.setTenantId(tenantId));
        order.setTenantId(tenantId);

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Order existingOrder = orderDAO
            .getOrderByOrderId(tenantDslContext, order.getOrderId())
            .orElseThrow(() -> {
                LOGGER.info("order id {} is invalid", order.getOrderId());
                return new ObjectNotFoundException(BillyObjectType.ORDER, order.getOrderId());
            });

        String entityId = entityContextResolver.resolveInputEntityIdForIsolatedObjectForUpdate(order.getEntityId(), existingOrder.getEntityId());
        order.setEntityId(entityId);
        order.getLineItems().forEach(li -> li.setEntityId(entityId));

        checkOrderTypeIsNewOrRenew(order);
        validateUpdateOrder(order, existingOrder);
        order.setStatus(existingOrder.getStatus());

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        OrderServiceHelper.updateOrderStatusByExpiryDate(order, timeZone);

        Optional<Subscription> currentSubscription = getCurrentSubscription(order);

        CreateOrderContext createOrderContext = makeCreateOrderContext(order, currentSubscription, isDryRun);
        validateOrder(order, createOrderContext, isDryRun);
        validateRenewalUpdateOrder(order, existingOrder);
        order.setId(existingOrder.getId());
        order.setCompositeOrderId(existingOrder.getCompositeOrderId());
        OrderServiceHelper.updateOrderOwner(order, userService);
        order.setId(existingOrder.getId());

        return tenantDslContext.transactionResult(configuration -> updateOrderTransaction(configuration, order, createOrderContext, isDryRun));
    }

    private void validateRenewalUpdateOrder(Order order, Order existingOrderToUpdate) {
        if (order.getOrderType() != OrderType.RENEWAL) {
            return;
        }

        Validator.validateStringNotBlank(order.getRenewalForSubscriptionId(), "RenewalSubscriptionId cannot be blank for Renewals");
        Subscription subscription = subscriptionGetService.getSubscription(order.getRenewalForSubscriptionId());
        if (subscription.getVersion() != existingOrderToUpdate.getRenewalForSubscriptionVersion()) {
            throw new IllegalStateException(
                "Subscription has changed after this order is created. Hence this order is invalid and cannot be updated."
            );
        }

        order.setRenewalForSubscriptionVersion(existingOrderToUpdate.getRenewalForSubscriptionVersion());
        addMissingRenewalSubscriptionCharges(order, subscription);
        OrderServiceHelper.validateOrderCurrency(order, subscription.getCurrency());
    }

    private void addMissingRenewalSubscriptionCharges(Order order, Subscription subscription) {
        // fill missing renewable charges from the previous subscription with action MISSING_RENEWAL
        if (!featureService.isEnabled(Feature.ADD_MISSED_RENEWAL_ORDER_LINES)) {
            return;
        }

        Map<String, SubscriptionCharge> subscriptionChargeMap = subscription
            .getCharges()
            .stream()
            .collect(Collectors.toMap(SubscriptionCharge::getSubscriptionChargeId, Function.identity()));
        Map<String, OrderLineItem> updateOrderBaseSubscriptionChargeMap = order
            .getLineItems()
            .stream()
            .filter(
                orderLine -> orderLine.getAction() == ActionType.RENEWAL && StringUtils.isNotBlank(orderLine.getBaseExternalSubscriptionChargeId())
            )
            .collect(Collectors.toMap(OrderLineItem::getBaseExternalSubscriptionChargeId, Function.identity()));

        List<String> chargeIds = subscription.getCharges().stream().map(SubscriptionCharge::getChargeId).collect(Collectors.toList());
        List<Plan> plans = productCatalogGetService.getPlansFromChargeIds(chargeIds);
        Map<String, String> chargeIdPlanIdMap = OrderServiceHelper.getChargeIdPlanIdMap(plans);

        order.setMissingRenewalOrderLineItems(new ArrayList<>());
        for (var subscriptionChargeMapPair : subscriptionChargeMap.entrySet()) {
            String subscriptionChargeId = subscriptionChargeMapPair.getKey();
            if (!updateOrderBaseSubscriptionChargeMap.containsKey(subscriptionChargeId)) {
                checkAndAddMissingRenewalOrderLine(order, subscriptionChargeMap.get(subscriptionChargeMapPair.getKey()), chargeIdPlanIdMap);
            }
        }
    }

    private void checkAndAddMissingRenewalOrderLine(Order order, SubscriptionCharge subscriptionCharge, Map<String, String> chargeIdPlanIdMap) {
        if (!subscriptionCharge.getEndDate().equals(order.getStartDate())) {
            // if the subscriptionCharge's end date is not on the renewal order's start date, ignore
            return;
        }

        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setId(AutoGenerate.getNewUuid());
        orderLineItem.setOrderLineId(AutoGenerate.getNewId());
        orderLineItem.setOrderId(order.getOrderId());
        orderLineItem.setTenantId(subscriptionCharge.getTenantId());
        orderLineItem.setEntityId(order.getEntityId());
        orderLineItem.setEffectiveDate(order.getStartDate());
        orderLineItem.setEndDate(order.getStartDate());
        orderLineItem.setAction(ActionType.MISSING_RENEWAL);
        orderLineItem.setPlanId(chargeIdPlanIdMap.get(subscriptionCharge.getChargeId()));
        orderLineItem.setChargeId(subscriptionCharge.getChargeId());
        orderLineItem.setQuantity(0);
        orderLineItem.setBaseExternalSubscriptionChargeId(subscriptionCharge.getSubscriptionChargeId());

        orderLineItem.setAmount(BigDecimal.ZERO);
        orderLineItem.setListAmount(BigDecimal.ZERO);
        orderLineItem.setSellUnitPrice(subscriptionCharge.getSellUnitPrice());
        orderLineItem.setListUnitPrice(subscriptionCharge.getListUnitPrice());
        orderLineItem.setCurrencyConversionRateId(subscriptionCharge.getCurrencyConversionRateId());
        order.getMissingRenewalOrderLineItems().add(orderLineItem);
    }

    private void checkOrderTypeIsNewOrRenew(Order order) {
        if (!ALLOWED_ORDER_TYPES_WITH_ORDER_SERVICE_ROUTE.contains(order.getOrderType())) {
            throw new ServiceFailureException("use /change_order route for change orders");
        }
    }

    private Order updateOrderTransaction(Configuration configuration, Order order, CreateOrderContext createOrderContext, Boolean isDryRun) {
        var dslContext = DSL.using(configuration);
        OrderServiceHelper.populateOrderAndOrderLineIds(orderIdGenerator, order);
        OrderServiceHelper.addOrderRankToLineItems(order);
        OrderServiceHelper.fillNetEffectOrderLines(order);

        // we need to run customization right before preview and after validation
        OrderCreationCustomizationOutput zeppaOutput = runOrderCreationCustomization(order, createOrderContext, isDryRun);
        order.setZeppaOutput(zeppaOutput);
        // FX handling
        updateOrderLineItemWithCurrencyConversionRateId(order, createOrderContext);

        InvoicePreview invoicePreview = invoiceService.previewInvoiceByOrderPeriodForOrderLinesCalculation(order);
        OrderServiceHelper.updateOrderAmountsFromInvoice(order, invoicePreview);
        OrderServiceHelper.validateOrderTotalAmount(order.getTotalAmount());

        Map<String, BigDecimal> orderLineItemAnnualizedAmounts = metricsService.getOrderLineItemAnnualizedAmounts(
            order,
            createOrderContext.getChargesInOrder()
        );
        OrderServiceHelper.updateOrderLineItemAnnualizedAmounts(order, orderLineItemAnnualizedAmounts);

        if (EvergreenUtils.isEvergreenOrder(order, featureService)) {
            Map<String, InvoiceItemAmounts> orderLineItemBillingCycleAmounts = metricsService.getOrderLineItemBillingCycleAmounts(
                order,
                createOrderContext.getChargesInOrder()
            );
            OrderServiceHelper.updateOrderTotalAndOrderLineItemAmountsForEvergreenOrder(order, orderLineItemBillingCycleAmounts);
        }

        orderCustomBillingService.validateAndSetCustomBillingDetailsInOrder(order, isDryRun);

        if (!isDryRun) {
            String tenantId = Optional.ofNullable(order.getTenantId()).orElse(tenantIdProvider.provideTenantIdString());
            Optional<Opportunity> opportunity = opportunityService.upsertOpportunityForOrderIfNeeded(dslContext, order, tenantId, true);
            boolean isPrimaryOrderForOpportunity = order.getIsPrimaryOrderForSfdcOpportunity();
            List<String> orderTemplateIds = order.getOrderFormTemplateIds();
            CustomField orderCustomFields = order.getCustomFields();
            Map<Integer, List<CustomFieldEntry>> orderLineCustomFieldsByOrderLineItemRank = OrderServiceHelper.getCustomFieldsByOrderLineItemRankMap(
                order
            );
            OrderServiceHelper.calculateAndApplyTaxEstimates(order, taxService, taxRateGetService, accountGetService, productCatalogGetService);
            CustomBillingSchedule customBillingSchedule = order.getCustomBillingSchedule();
            List<CustomPredefinedTemplateOnOrder> customOrderTemplatesSavedOnOrder = order.getCustomPredefinedTemplatesOnOrder();

            order = orderDAO.updateOrder(dslContext, order);
            order.setOrderFormTemplateIds(orderTemplateIds);
            // ⚠️order custom fields get overwritten while saving, so we need to add it back here ⚠️
            order.setCustomFields(orderCustomFields);
            OrderServiceHelper.addCustomFieldsBackToOrderLineItems(order, orderLineCustomFieldsByOrderLineItemRank, customizationService);

            // set the custom predefined templates back on order as they get reset during update order call
            order.setCustomPredefinedTemplatesOnOrder(customOrderTemplatesSavedOnOrder);

            // ⚠️zeppa output also get destroyed when saving order we need surface it back TODO: implement persistence ⚠️
            order.setZeppaOutput(zeppaOutput);

            OrderServiceHelper.fillNetEffectOrderLines(order);
            OrderServiceHelper.addSfdcFieldsToOrder(order, opportunity, isPrimaryOrderForOpportunity);

            List<DocumentTemplate> orderTemplates = documentTemplateGetService.getDocumentTemplatesByTemplateIds(orderTemplateIds);
            orderDocumentTemplateService.addPredefinedTermsForOrder(order, dslContext, tenantId, orderTemplates, true);
            processCustomPredefinedTemplatesOnOrder(order, dslContext);

            orderCustomBillingService.saveOrInvalidateCustomBillingSchedule(
                dslContext,
                order,
                createOrderContext.getChargesInOrder(),
                customBillingSchedule
            );
        }

        // ❌❌❌ The custom field persistence is non-transactional and can fail partially ❌❌❌
        // ❌❌❌ even though it called inside the transaction method it does nothing transactional ❌❌❌
        OrderServiceHelper.persistCustomFields(order, isDryRun, customFieldService);

        return order;
    }

    private void validateUpdateOrder(Order updateOrder, Order originalOrder) {
        boolean allowNonDraftUpdate = updateOrder.getOrderProcessingMetadata().map(OrderProcessingMetadata::allowNonDraftUpdate).orElse(false);
        if (!allowNonDraftUpdate && originalOrder.getStatus() != OrderStatus.DRAFT && originalOrder.getStatus() != OrderStatus.EXPIRED) {
            var message = "updating an order is supported only in the DRAFT or EXPIRED state. Current order status: " + originalOrder.getStatus();
            LOGGER.warn(message);
            throw new IllegalArgumentException(message);
        }

        Map<String, OrderLineItem> updateOrderlineItemsMap = updateOrder
            .getLineItems()
            .stream()
            .filter(li -> StringUtils.isNotBlank(li.getOrderLineId()))
            .collect(Collectors.toMap(OrderLineItem::getOrderLineId, Function.identity()));
        Map<String, OrderLineItem> originalOrderLineItemsMap = originalOrder
            .getLineItems()
            .stream()
            .collect(Collectors.toMap(OrderLineItem::getOrderLineId, Function.identity()));

        // todo: invalidLineItemsSet are set by dry runs. Introduced a new field called isDryRun which we expect
        // the UI to populate and helps to identify that these are new dry run updates.
        // Once UI is onboarded with this line item, we can ignore those lines for validation.
        // Until that change is taken on the UI, we keep ignoring the invalid ids on the lineItems and
        // treat them as new line items
        Set<String> invalidUpdateLineItemsSet = new HashSet<>(updateOrderlineItemsMap.keySet());
        Set<String> originalLineItemsSet = new HashSet<>(originalOrderLineItemsMap.keySet());

        invalidUpdateLineItemsSet.removeAll(originalLineItemsSet);
        if (!invalidUpdateLineItemsSet.isEmpty()) {
            String message = String.format(
                "orderLineItem id(s) %s not found on order: %s",
                String.join(", ", invalidUpdateLineItemsSet),
                updateOrder.getOrderId()
            );
            LOGGER.info(message);
        }

        // update the db pk ids of already existing order lines
        updateOrderlineItemsMap.forEach((key, value) ->
            populateOrderLineItemIdsFromExistingOnes(updateOrderlineItemsMap, originalOrderLineItemsMap, key)
        );
    }

    private static void populateOrderLineItemIdsFromExistingOnes(
        Map<String, OrderLineItem> updateOrderlineItemsMap,
        Map<String, OrderLineItem> originalOrderLineItemsMap,
        String orderLineItemId
    ) {
        OrderLineItem updatingOrderLineItem = updateOrderlineItemsMap.get(orderLineItemId);
        OrderLineItem originalOrderLineItem = originalOrderLineItemsMap.get(orderLineItemId);

        updatingOrderLineItem.setId(originalOrderLineItem == null ? null : originalOrderLineItem.getId());
        updatingOrderLineItem.setOrderLineId(originalOrderLineItem == null ? null : originalOrderLineItem.getOrderLineId());

        // copy the original base subscription charge from which the renewal line item is created from
        updatingOrderLineItem.setBaseExternalSubscriptionChargeId(
            originalOrderLineItem == null ? null : originalOrderLineItem.getBaseExternalSubscriptionChargeId()
        );
    }

    public Order deleteOrder(String orderId) {
        if (orderId == null) {
            throw new IllegalArgumentException("orderId is null");
        }

        var tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var optionalOrder = orderDAO.getOrderByOrderId(tenantDslContext, orderId);
        if (optionalOrder.isEmpty()) {
            var message = "invalid order id: " + orderId;
            throw new IllegalArgumentException(message);
        }

        Order order = optionalOrder.get();

        if (!ALLOWED_DELETABLE_ORDER_STATUS.contains(order.getStatus())) {
            var message = String.format("order id %s is in %s state. Cannot delete the order in this state.", orderId, order.getStatus().name());
            throw new IllegalArgumentException(message);
        }

        tenantDslContext.transaction(configuration -> {
            var dslContext = DSL.using(configuration);
            orderDAO.deleteOrder(dslContext, orderId);
            crmService.pushOrderDeletionSync(order);
            String tenantId = Optional.ofNullable(order.getTenantId()).orElse(tenantIdProvider.provideTenantIdString());
            orderTermsService.deleteAllOrderTermsForOrder(dslContext, tenantId, orderId);
        });

        return order;
    }

    public void deleteOrder(Configuration configuration, String orderId) {
        Validator.validateStringNotBlank(orderId, "order Id is required");

        DSLContext dslContext = DSL.using(configuration);

        documentLinkService.deleteDocumentLink(configuration, orderId);
        orderTermsService.deleteAllOrderTermsForOrder(dslContext, orderId);
        orderDAO.deleteOrder(dslContext, orderId);
    }

    public Order duplicateOrder(String orderId, String accountId) {
        // validate orderId
        Validator.validateStringNotBlank(orderId, "OrderId should be present");
        Order order = orderGetService.getOrderByOrderId(orderId);

        if (!(order.getOrderType() == OrderType.NEW || order.getOrderType() == OrderType.RENEWAL)) {
            var message = "Only NEW and RENEWAL orderTypes are supported for duplicateOrder. Given OrderType: " + order.getOrderType();
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        if (StringUtils.isNotBlank(accountId)) {
            accountGetService
                .getAccountOptional(accountId)
                .ifPresent(account -> {
                    order.setAccountId(accountId);
                    // TODO: if there is default shipping and billing contact
                    // TODO: then populate it
                    order.setShippingContactId(null);
                    order.setBillingContactId(null);
                });
        }

        Optional<Opportunity> opportunity = opportunityGetService.getOpportunityForDuplicateOrder(order.getSfdcOpportunityId());

        order.setId(null);
        order.setName((StringUtils.isNotBlank(order.getName()) ? order.getName() : order.getOrderId()) + " Copy");
        order.setOrderId(null);
        order.setExternalId(null);
        order.setExternalSubscriptionId(null);
        order.setOpportunity(opportunity.orElse(null));
        order.setSfdcOpportunityId(opportunity.map(Opportunity::getCrmId).orElse(null));
        order.setSfdcOpportunityName(opportunity.map(Opportunity::getName).orElse(null));
        order.setSfdcOpportunityType(opportunity.map(Opportunity::getType).orElse(null));
        order.setSfdcOpportunityStage(opportunity.map(Opportunity::getStage).orElse(null));
        order.setSfdcOrderCanBeExecuted(opportunity.isPresent());
        order.setIsPrimaryOrderForSfdcOpportunity(false);
        order.setOwnerId(null);
        order.getLineItems().forEach(this::resetOrderLineItemIds);
        order.setExpiresOn(null);
        order.setStatus(OrderStatus.DRAFT);
        order.setCustomBillingEligibleOrderLineIds(Collections.emptyList());

        // If there are any templates updated on order, we need to copy them to the new order
        List<CustomTemplateUpdateOnOrder> updatedTemplatesOnOrder = customTemplateUpdatedOnOrderGetService.getUpdatedTemplatesOnOrder(orderId);
        if (CollectionUtils.isNotEmpty(updatedTemplatesOnOrder)) {
            List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder =
                customPredefinedTemplateOnOrderMapper.toCustomPredefinedTemplatesOnOrder(updatedTemplatesOnOrder);
            customPredefinedTemplatesOnOrder.forEach(template -> template.setOrderId(null));
            order.setCustomPredefinedTemplatesOnOrder(customPredefinedTemplatesOnOrder);
        }

        return order;
    }

    public Order generateRenewalOrder(String subscriptionId) {
        Order order = new Order();
        order.setRenewalForSubscriptionId(subscriptionId);
        order.setOrderType(OrderType.RENEWAL);
        order.setSource(OrderSource.USER);
        order.setShouldUseCustomBillingSchedule(true);
        return renewSubscription(order, true);
    }

    public Order renewSubscription(Order order, Boolean isDryRun) {
        validateRenewSubscriptionPayload(order);

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Subscription subscription = validateAndFetchRenewalSubscription(order);
        order.setRenewalForSubscriptionVersion(subscription.getVersion());
        fillRenewalOrderDetailsFromSubscription(order, subscription);

        if (!order.getStartDate().equals(subscription.getEndDate())) {
            throw new IllegalArgumentException("startDate should be the same as Subscription endDate");
        }

        checkProductRules(order, isDryRun);

        // todo: add full suite of order validations
        OrderDiscountService.validateOrderDiscounts(order, discountService, Optional.of(subscription));

        Optional<Subscription> currentSubscription = Optional.empty();
        if (order.getOrderType() == OrderType.RENEWAL) {
            currentSubscription = Optional.of(subscriptionGetService.getSubscription(order.getRenewalForSubscriptionId()));
        }

        CreateOrderContext createOrderContext = makeCreateOrderContext(order, currentSubscription, isDryRun);

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        validateLineItems(order, createOrderContext, currentSubscription.orElse(null), timeZone.toZoneId(), isDryRun);

        return tenantDslContext.transactionResult(configuration -> addOrderTransaction(configuration, order, createOrderContext, isDryRun));
    }

    public void updateOrderLineMetrics(DSLContext txnDslContext, String tenantId, String orderLineId, JSONB json) {
        orderDAO.updateOrderLineMetrics(txnDslContext, tenantId, orderLineId, json);
    }

    public void updateOrderMetrics(DSLContext txnDslContext, String tenantId, String orderId, JSONB json) {
        orderDAO.updateOrderMetrics(txnDslContext, tenantId, orderId, json);
    }

    public void updateOrderMetricsIfCurrent(Order order, JSONB json) {
        orderDAO.updateOrderMetricsIfCurrent(order, json);
    }

    private void validateRenewSubscriptionPayload(Order order) {
        Validator.validateNonNullArgument(order, "order");

        if (order.getOrderType() != OrderType.RENEWAL) {
            throw new IllegalArgumentException("OrderType needs to be Renewal for renewal orders");
        }

        Validator.validateStringNotBlank(order.getRenewalForSubscriptionId(), "RenewalSubscriptionId is blank");
    }

    private void fillRenewalOrderDetailsFromSubscription(Order order, Subscription subscription) {
        if (order.getStartDate() == null || order.getStartDate() == Instant.EPOCH) {
            order.setStartDate(subscription.getEndDate());
        }

        if (StringUtils.isNotBlank(order.getAccountId())) {
            Validator.validateStringEqualsIgnoreCase(
                subscription.getAccountId(),
                order.getAccountId(),
                "AccountId in order doesn't match with subscription accountId"
            );
        }

        String tenantId = tenantIdProvider.provideTenantIdString();
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        Optional<Instant> orderExpiresOn = OrderServiceHelper.getOrderExpiresOn(tenantSettingService, timeZone);

        order.setTenantId(tenantId);
        order.setEntityId(subscription.getEntityId());
        order.setExternalSubscriptionId(null);
        order.setRenewalForSubscriptionVersion(subscription.getVersion());
        order.setSubscriptionTargetVersion(SUBSCRIPTION_TARGET_VERSION_NEW);
        order.setAccountId(subscription.getAccountId());
        order.setCurrency(subscription.getCurrency());
        order.setPaymentTerm(subscription.getPaymentTerm());
        order.setBillingTerm(subscription.getBillingTerm());
        order.setPredefinedDiscounts(
            CollectionUtils.isEmpty(order.getPredefinedDiscounts()) ? subscription.getPredefinedDiscounts() : order.getPredefinedDiscounts()
        );
        OrderServiceHelper.addOrderCreatedByAndOwner(order, userService);
        order.setExpiresOn(orderExpiresOn.orElse(null));

        if (StringUtils.isNotBlank(order.getShippingContactId())) {
            accountGetService.getContact(order.getShippingContactId());
        } else {
            order.setShippingContactId(subscription.getShippingContactId());
        }

        if (StringUtils.isNotBlank(order.getBillingContactId())) {
            accountGetService.getContact(order.getBillingContactId());
        } else {
            order.setBillingContactId(subscription.getBillingContactId());
        }

        if (order.getTermLength() == null && order.getEndDate() == null) {
            if (subscription.getTermLength() == null) {
                ZonedDateTime zonedStartDate = order.getStartDate().atZone(timeZone.toZoneId());
                order.setEndDate(zonedStartDate.plusYears(DEFAULT_RENEWAL_NUMBER_OF_YEARS).toInstant());
            } else {
                order.setTermLength(subscription.getTermLength());
            }
        }

        order.setBillingCycle(subscription.getBillingCycle());
        validateAndSetOrderDates(order, timeZone);
        order.setStatus(OrderStatus.DRAFT);
        order.setAutoRenew(subscription.getAutoRenew());

        if (CollectionUtils.isEmpty(order.getLineItems())) {
            fillOrderLineDetailsFromRenewableSubscriptionCharges(order, subscription);
        } else {
            order.getLineItems().forEach(li -> li.setTenantId(tenantId));
            order.getLineItems().forEach(li -> li.setEntityId(subscription.getEntityId()));
        }

        orderDocumentTemplateService.consolidatePlanTemplates(order);
    }

    private void fillOrderLineDetailsFromRenewableSubscriptionCharges(Order order, Subscription subscription) {
        var charges = subscription
            .getCharges()
            .stream()
            .filter(c -> c.getEndDate().equals(subscription.getEndDate()))
            .sorted(Comparator.comparing(SubscriptionCharge::getRank))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(charges)) {
            var message = "No active charges to renew in subscription id: " + order.getExternalSubscriptionId();
            throw new IllegalArgumentException(message);
        }

        populateRenewalOrderLinesFromCharges(order, charges);
    }

    private void populateRenewalOrderLinesFromCharges(Order order, List<SubscriptionCharge> subscriptionCharges) {
        var chargeIds = subscriptionCharges.stream().map(SubscriptionCharge::getChargeId).collect(Collectors.toList());
        var plans = productCatalogGetService.getPlansFromChargeIds(chargeIds);
        Map<String, String> chargeIdPlanIdMap = OrderServiceHelper.getChargeIdPlanIdMap(plans);

        List<Charge> chargeList = plans.stream().flatMap(plan -> plan.getCharges().stream()).toList();
        Map<String, Charge> chargeMapById = chargeList.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        order.setLineItems(new ArrayList<>());
        int rank = 1;
        for (var subscriptionCharge : subscriptionCharges) {
            if (chargeMapById.get(subscriptionCharge.getChargeId()).getIsRenewable()) {
                OrderLineItem orderLineItem = orderLineItemFromSubscriptionCharge(order, chargeIdPlanIdMap, rank++, subscriptionCharge);
                order.getLineItems().add(orderLineItem);
            }
        }

        if (featureService.isEnabled(Feature.POPULATE_MISSING_ITEMS)) {
            missingOrderChargesService.findAndAddMissingOrderCharges(order);
        }

        if (featureService.isEnabled(Feature.RENEWAL_REPLACE_PLAN) && tenantSettingService.getTenantSettingInternal().isAutoReplacePlans()) {
            order.setLineItems(autoReplacePlansInRenewal(order, plans, chargeMapById));
        }
    }

    @SuppressWarnings("unused")
    private List<OrderLineItem> autoReplacePlansInRenewal(Order order, List<Plan> plans, Map<String, Charge> chargeMapById) {
        Map<String, Plan> planReplacementMap = getReplacementPlanMap(plans);

        // todo: fix ordering of line items
        // add all the order line items that are not part of the replacement plan
        List<OrderLineItem> orderLineItems = new ArrayList<>();
        order
            .getLineItems()
            .stream()
            .filter(orderLineItem -> !planReplacementMap.containsKey(orderLineItem.getPlanId()))
            .forEach(orderLineItems::add);

        List<String> replacedPlanIds = new ArrayList<>();
        // add all the order line items that are part of the replacement plan
        for (var entry : planReplacementMap.entrySet()) {
            String planId = entry.getKey();
            Plan replacementPlan = entry.getValue();
            if (!replacementPlanIsValidForOrder(order, replacementPlan)) {
                continue;
            }
            Plan currentPlan = plans.stream().filter(p -> p.getPlanId().equals(planId)).findFirst().orElseThrow();
            List<OrderLineItem> replacedOrderLines = autoReplacePlanInRenewal(order, currentPlan, replacementPlan);

            replacedPlanIds.add(planId);
            orderLineItems.addAll(replacedOrderLines);
        }
        orderDocumentTemplateService.carryOverReplacedPlanTermsToRenewal(order, replacedPlanIds);

        IntStream.range(0, orderLineItems.size()).forEach(i -> orderLineItems.get(i).setRank(i));
        return orderLineItems;
    }

    private boolean replacementPlanIsValidForOrder(Order order, Plan replacementPlan) {
        if (!EntityContext.containsEntity(replacementPlan.getEntityIds(), order.getEntityId())) {
            return false;
        }
        return replacementPlan.getStatus() != PlanStatus.DRAFT;
    }

    private List<OrderLineItem> autoReplacePlanInRenewal(Order order, Plan currentPlan, Plan replacementPlan) {
        List<OrderLineItem> originalLineItems = order.getLineItems().stream().filter(li -> li.getPlanId().equals(currentPlan.getPlanId())).toList();

        Set<String> replacementChargeIds = new HashSet<>();
        List<OrderLineItem> replacementLineItems = new ArrayList<>();
        for (OrderLineItem originalLineItem : originalLineItems) {
            Charge charge = currentPlan
                .getCharges()
                .stream()
                .filter(c -> c.getChargeId().equals(originalLineItem.getChargeId()))
                .findFirst()
                .orElseThrow(() ->
                    new InvariantCheckFailedException(
                        String.format("Charge with id %s not found in plan %s", originalLineItem.getChargeId(), currentPlan.getPlanId())
                    )
                );

            replacementPlan
                .getCharges()
                .stream()
                .filter(replacementCharge -> canReplaceCharge(charge, replacementCharge))
                .findFirst()
                .ifPresent(replacementCharge -> {
                    OrderLineItem replacementLineItem = getReplacementLineItem(originalLineItem, replacementCharge);
                    replacementLineItems.add(replacementLineItem);
                    replacementChargeIds.add(replacementCharge.getChargeId());
                });
        }

        // add remaining charges which have no replacement
        replacementPlan
            .getCharges()
            .stream()
            .filter(replacementCharge -> !replacementChargeIds.contains(replacementCharge.getChargeId()))
            .forEach(replacementCharge -> {
                OrderLineItem orderLineItem = getReplacementLineItemWithNoMapping(order, replacementCharge, currentPlan.getPlanId());
                replacementLineItems.add(orderLineItem);
            });

        return replacementLineItems;
    }

    private boolean canReplaceCharge(Charge charge, Charge replacementCharge) {
        // todo: add more checks (e.g. custom pricing, rate card, etc)
        if (
            !StringUtils.equals(charge.getName(), replacementCharge.getName()) ||
            charge.getType() != replacementCharge.getType() ||
            charge.getChargeModel() != replacementCharge.getChargeModel()
        ) {
            return false;
        }
        return charge.isCustom() == replacementCharge.isCustom();
    }

    private OrderLineItem getReplacementLineItem(OrderLineItem originalLineItem, Charge replacementCharge) {
        OrderLineItem replacementLineItem = new OrderLineItem(originalLineItem);
        replacementLineItem.setPlanId(replacementCharge.getPlanId());
        replacementLineItem.setChargeId(replacementCharge.getChargeId());
        replacementLineItem.setReplacedPlanId(originalLineItem.getPlanId());
        replacementLineItem.setAction(ActionType.ADD);

        replacementLineItem.setOrderId(null);
        replacementLineItem.setOrderLineId(null);
        replacementLineItem.setItemGroupId(null);
        replacementLineItem.setRampGroupId(null);
        replacementLineItem.setSubscriptionChargeGroupId(null);
        replacementLineItem.setSubscriptionChargeId(null);
        replacementLineItem.setBaseExternalSubscriptionChargeId(null);
        replacementLineItem.setExternalSubscriptionChargeId(null);

        replacementLineItem.setListUnitPriceBeforeOverride(null);
        replacementLineItem.setListAmountBeforeOverride(null);

        return replacementLineItem;
    }

    private OrderLineItem getReplacementLineItemWithNoMapping(Order order, Charge charge, String replacedPlanId) {
        return new OrderLineItem(
            null,
            null,
            null,
            tenantIdProvider.provideTenantIdString(),
            order.getEntityId(),
            null,
            1, // todo: update the rank once all replacements are done
            false,
            ActionType.ADD,
            charge.getPlanId(),
            null,
            null,
            null,
            null,
            charge.getChargeId(),
            null,
            null, //TODO revisit this as FX progresses
            charge.getDefaultQuantity() != null ? charge.getDefaultQuantity() : Optional.ofNullable(charge.getMinQuantity()).orElse(0L),
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            List.of(),
            List.of(),
            charge.getChargeModel() == ChargeModel.RATE_CARD_LOOKUP ? List.of() : null,
            null,
            null,
            null,
            null,
            null,
            null,
            order.getStartDate(),
            order.getEndDate(),
            null,
            null,
            replacedPlanId,
            null,
            null
        );
    }

    private Map<String, Plan> getReplacementPlanMap(List<Plan> plans) {
        if (!featureService.isEnabled(Feature.RENEWAL_REPLACE_PLAN) || !tenantSettingService.getTenantSettingInternal().isAutoReplacePlans()) {
            return Map.of();
        }
        List<String> planIds = plans.stream().map(Plan::getPlanId).distinct().toList();
        Map<String, String> planReplacementMap = catalogRelationshipGetService.getRelationshipMap(planIds, CatalogRelationshipType.IS_REPLACED_BY);
        // todo: get last plan in chain that is active
        Set<String> replacementPlanIds = planReplacementMap.values().stream().collect(Collectors.toUnmodifiableSet());
        List<Plan> replacementPlans = productCatalogGetService.getPlansByPlanIdsWithoutChecks(replacementPlanIds);
        return planReplacementMap
            .entrySet()
            .stream()
            .collect(
                Collectors.toMap(Map.Entry::getKey, entry ->
                    replacementPlans.stream().filter(plan -> plan.getPlanId().equals(entry.getValue())).findFirst().orElseThrow()
                )
            );
    }

    private OrderLineItem orderLineItemFromSubscriptionCharge(
        Order order,
        Map<String, String> chargeIdPlanIdMap,
        int rank,
        SubscriptionCharge charge
    ) {
        return new OrderLineItem(
            null,
            null,
            null,
            tenantIdProvider.provideTenantIdString(),
            order.getEntityId(),
            null,
            rank,
            false,
            ActionType.RENEWAL,
            chargeIdPlanIdMap.get(charge.getChargeId()),
            null,
            null,
            null,
            charge.getSubscriptionChargeId(),
            charge.getChargeId(),
            null,
            null, //TODO revisit this as FX progresses
            charge.getQuantity(),
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            orderMapper.discountToDiscountDetail(charge.getDiscounts()),
            charge.getPredefinedDiscounts(),
            charge.getAttributeReferences(),
            charge.getPricingOverride(),
            charge.getListPriceOverrideRatio(),
            null,
            null,
            null,
            null,
            order.getStartDate(),
            order.getEndDate(),
            List.of(),
            null,
            null,
            null,
            null
        );
    }

    private void checkProductRules(Order order, Boolean isDryRun) {
        if (!featureService.isEnabled(Feature.PRODUCT_RULES_DEMO) || BooleanUtils.isTrue(isDryRun)) {
            return;
        }

        Set<String> orderPlanIds = order.getLineItems().stream().map(OrderLineItem::getPlanId).collect(Collectors.toSet());
        OrderServiceHelper.checkProductRules(featureService, catalogRelationshipGetService, orderPlanIds, Set.of());
    }

    public void updateBaseSubscriptionChargeIdForOrderLine(Map<String, String> orderLineSubscriptionChargeIdMap) {
        orderDAO.updateBaseSubscriptionChargeIdsForOrderLines(orderLineSubscriptionChargeIdMap);
    }

    public Order deleteOrderInTransaction(Order order, DSLContext tenantDslContext) {
        Validator.validateNonNullArgument(order, "order");
        Validator.validateNonNullArgument(order.getTenantId(), "tenantId");

        String orderId = order.getOrderId();

        if (!ALLOWED_DELETABLE_ORDER_STATUS.contains(order.getStatus())) {
            var message = String.format("order id %s is in %s state. Cannot delete the order in this state.", orderId, order.getStatus().name());
            throw new IllegalArgumentException(message);
        }

        tenantDslContext.transaction(configuration -> {
            var dslContext = DSL.using(configuration);
            orderDAO.deleteOrder(dslContext, orderId);
            orderTermsService.deleteAllOrderTermsForOrder(dslContext, order.getTenantId(), orderId);
        });

        return order;
    }

    public void findAndAddMissingPlanTemplates(Order order) {
        orderDocumentTemplateService.findAndAddMissingPlanTemplates(order);
    }

    public void updateOrderContacts(Order order, String billingContactId, String shippingContactId) {
        Validator.validateNonNullArguments(billingContactId, shippingContactId);
        orderDAO.updateOrderContacts(order.getOrderId(), billingContactId, shippingContactId);
    }

    public Order generateDraftRestructure(Subscription subscription, Instant restructureDate) {
        OrderServiceHelper.checkForCanceledSubscription(subscription);
        checkForRenewedSubscription(subscription);

        return generateDraftRestructureFromSubscription(subscription, restructureDate);
    }

    private Order generateDraftRestructureFromSubscription(Subscription subscription, Instant restructureDate) {
        CustomField customFields = customFieldService.getEmptyHydratedCustomFieldForParentObjectType(CustomFieldParentType.ORDER);

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Instant endDate = subscription.getEndDate();
        if (
            subscription.getBillingCycle().getCycle() != null &&
            subscription.getBillingCycle().getCycle() == Cycle.CUSTOM &&
            subscription.getTermLength() != null
        ) {
            // Use the term length to calculate the end date as it directly impacts the order line amount calculations and trigger dates for custom billing cadence
            endDate = SubscriptionBillingPeriodService.getBillingEndDate(restructureDate, timeZone.toZoneId(), subscription.getTermLength());
        }

        Order order = new Order(
            null,
            null,
            null,
            null,
            subscription.getEntityId(),
            null,
            subscription.getAccountId(),
            subscription.getResellerAccountId().orElse(null),
            OrderType.RESTRUCTURE,
            subscription.getCurrency(),
            subscription.getPaymentTerm(),
            null,
            null,
            subscription.getShippingContactId(),
            subscription.getBillingContactId(),
            subscription.getPredefinedDiscounts(),
            Lists.newArrayList(),
            Lists.newArrayList(),
            restructureDate,
            endDate,
            restructureDate,
            subscription.getTermLength(),
            subscription.getBillingCycle(),
            subscription.getBillingTerm(),
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            null,
            BigDecimal.ZERO,
            OrderStatus.DRAFT,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            0,
            subscription.getSubscriptionId(),
            0,
            null,
            false,
            subscription.getAutoRenew(),
            null,
            customFields,
            null,
            true,
            null,
            null,
            OrderSource.USER,
            OrderStartDateType.FIXED,
            null,
            Optional.empty(),
            subscription.getDurationModel(),
            null
        );

        order.setShouldUseCustomBillingSchedule(true);
        order.setSubscriptionTargetVersion(subscriptionGetService.getNextSubscriptionVersion(subscription.getSubscriptionId()));

        List<SubscriptionCharge> subscriptionCharges = subscription
            .getCharges()
            .stream()
            .sorted(Comparator.comparing(SubscriptionCharge::getRank))
            .toList();
        List<String> chargeIds = subscriptionCharges.stream().map(SubscriptionCharge::getChargeId).collect(Collectors.toList());
        List<Charge> charges = productCatalogGetService.getChargesByChargeId(chargeIds);
        List<Plan> plans = productCatalogGetService.getPlansFromChargeIds(chargeIds);
        Map<String, String> chargeIdPlanIdMap = OrderServiceHelper.getChargeIdPlanIdMap(plans);

        //get all ramp charges id
        List<String> rampChargeIds = subscriptionCharges
            .stream()
            .filter(SubscriptionCharge::getIsRamp)
            .map(SubscriptionCharge::getChargeId)
            .distinct()
            .toList();
        List<String> plansWithRampCharge = chargeIdPlanIdMap
            .entrySet()
            .stream()
            .filter(entry -> rampChargeIds.contains(entry.getKey()))
            .map(Map.Entry::getValue)
            .collect(Collectors.toList());

        int rank = 1;
        for (SubscriptionCharge charge : subscriptionCharges) {
            if (charge.getEndDate().isAfter(order.getStartDate())) {
                order
                    .getLineItems()
                    .add(
                        generateRestructureOrderLineFromCharge(
                            charge,
                            chargeIdPlanIdMap,
                            plansWithRampCharge,
                            order.getStartDate(),
                            order.getEndDate(),
                            rank++
                        )
                    );
            }
        }

        order.setLineItemsNetEffect(new ArrayList<>(order.getLineItems()));

        orderDocumentTemplateService.consolidatePlanTemplates(order);

        // Populate order line id - it is used for grouping invoice line items during invoice preview processing
        // Order line items have been created with 0 amount, invoice preview will update the amounts
        order.getLineItems().forEach(li -> li.setOrderLineId(AutoGenerate.getNewId()));
        InvoicePreview invoicePreview = invoiceService.previewInvoiceByOrderPeriodForOrderLinesCalculation(order);
        OrderServiceHelper.updateOrderAmountsFromInvoice(order, invoicePreview);

        Map<String, BigDecimal> orderLineItemAnnualizedAmounts = metricsService.getOrderLineItemAnnualizedAmounts(order, charges);
        OrderServiceHelper.updateOrderLineItemAnnualizedAmounts(order, orderLineItemAnnualizedAmounts);

        if (EvergreenUtils.isEvergreenOrder(order, featureService)) {
            Map<String, InvoiceItemAmounts> orderLineItemBillingCycleAmounts = metricsService.getOrderLineItemBillingCycleAmounts(order, charges);
            OrderServiceHelper.updateOrderTotalAndOrderLineItemAmountsForEvergreenOrder(order, orderLineItemBillingCycleAmounts);
        }

        if (featureService.isEnabled(Feature.CUSTOM_BILLING)) {
            Map<String, Charge> chargeMap = charges.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
            List<String> orderLineItemsEligibleForCustomBilling = orderCustomBillingService
                .getOrderLineItemsEligibleForCustomBilling(order, chargeMap)
                .stream()
                .map(OrderLineItem::getOrderLineId)
                .toList();
            order.setCustomBillingEligibleOrderLineIds(orderLineItemsEligibleForCustomBilling);
        }

        return order;
    }

    private OrderLineItem generateRestructureOrderLineFromCharge(
        SubscriptionCharge charge,
        Map<String, String> chargeIdPlanIdMap,
        List<String> planIdWithRampCharge,
        Instant startDate,
        Instant endDate,
        int rank
    ) {
        Instant lineItemStartDate = charge.getStartDate();
        Instant lineItemEndDate = charge.getEndDate();
        if (startDate.isAfter(charge.getStartDate()) && startDate.isBefore(charge.getEndDate())) {
            lineItemStartDate = startDate;
            lineItemEndDate = endDate;
        }
        ActionType actionType = ActionType.RESTRUCTURE;
        if (planIdWithRampCharge.contains(chargeIdPlanIdMap.get(charge.getChargeId()))) {
            actionType = ActionType.REMOVE;
        }

        return new OrderLineItem(
            null,
            null,
            null,
            null,
            null,
            null,
            rank,
            charge.getIsRamp(),
            actionType,
            chargeIdPlanIdMap.get(charge.getChargeId()),
            null,
            charge.getSubscriptionChargeGroupId(),
            null,
            charge.getSubscriptionChargeId(),
            charge.getChargeId(),
            null,
            null, //TODO revisit this as FX progresses
            charge.getQuantity(),
            charge.getListUnitPrice(),
            charge.getSellUnitPrice(),
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            getSubscriptionChargeDiscounts(charge),
            charge.getPredefinedDiscounts(),
            charge.getAttributeReferences(),
            charge.getPricingOverride(),
            charge.getListPriceOverrideRatio(),
            null,
            null,
            null,
            null,
            lineItemStartDate,
            lineItemEndDate,
            List.of(),
            null,
            null,
            null,
            null
        );
    }

    private List<DiscountDetail> getSubscriptionChargeDiscounts(SubscriptionCharge subscriptionCharge) {
        if (subscriptionCharge == null) {
            return null;
        }

        var discounts = subscriptionCharge.getDiscounts();
        var discountDetails = orderMapper.discountToDiscountDetail(discounts);
        return OrderDiscountService.populateDiscountIdsAndResetAmounts(discountDetails);
    }

    private void checkForRenewedSubscription(Subscription subscription) {
        if (subscription.getRenewedToSubscriptionId() != null) {
            String message = String.format(
                "Subscription restructure is not allowed as the current subscription: %s is already renewed to subscription: %s",
                subscription.getSubscriptionId(),
                subscription.getRenewedToSubscriptionId()
            );
            LOGGER.info(message);
            throw new InvalidInputException(message);
        }
    }

    public void expireOrder(Order order, Instant currentDateTime, TimeZone tenantTimeZone) {
        Instant startOfCurrentDay = DateTimeConverter.getStartOfCurrentDay(currentDateTime, tenantTimeZone);
        Instant startOfExpiryDate = DateTimeConverter.getStartOfCurrentDay(order.getExpiresOn(), tenantTimeZone);

        if (startOfExpiryDate.compareTo(startOfCurrentDay) < 0) {
            updateOrderStatus(order.getOrderId(), OrderStatus.EXPIRED);
        }
    }

    // todo: can this be extract out into a common function?
    private void updateOrderLineItemWithCurrencyConversionRateId(Order order, CreateOrderContext createOrderContext) {
        updateOrderLineItemWithCurrencyConversionRateId(order, createOrderContext.getPlansInOrder(), createOrderContext.getChargesInOrder());
    }

    public void updateOrderLineItemWithCurrencyConversionRateId(Order order, List<Plan> plans, List<Charge> charges) {
        if (CollectionUtils.isEmpty(order.getLineItems())) {
            return;
        }

        if (CollectionUtils.isEmpty(plans) || CollectionUtils.isEmpty(charges)) {
            throw new InvalidInputException("Plans and Charges are required to update currency conversion rate id");
        }

        if (!featureService.isEnabled(Feature.FOREIGN_EXCHANGE)) {
            return;
        }

        Optional<AccountStub> accountOptional = accountGetService.getAccountStub(order.getAccountId());
        String accountCurrencyCode = Objects.nonNull(order.getCurrency())
            ? order.getCurrency().getCurrencyCode()
            : (accountOptional.isPresent() ? accountOptional.get().getCurrency().getCurrencyCode() : StringUtils.EMPTY);
        Map<String, Plan> planIdPlanMap = plans.stream().collect(Collectors.toMap(Plan::getPlanId, Function.identity()));
        Map<String, Charge> chargeIdChargeMap = charges.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        order
            .getLineItems()
            .forEach(orderLineItem -> {
                if (StringUtils.isNotBlank(orderLineItem.getPlanId())) {
                    Plan plan = planIdPlanMap.get(orderLineItem.getPlanId());
                    Charge charge = chargeIdChargeMap.get(orderLineItem.getChargeId());
                    if (Objects.nonNull(plan) && !plan.getCurrency().getCurrencyCode().equalsIgnoreCase(accountCurrencyCode) && !charge.isCustom()) {
                        CurrencyConversionRate currencyConversionRate =
                            currencyConversionRateGetService.getConversionRateForCurrencyPairByEffectiveDate(
                                plan.getCurrency().getCurrencyCode(),
                                accountCurrencyCode,
                                order.getStartDate()
                            );
                        if (currencyConversionRate == null) {
                            TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
                            throw new InvalidInputException(
                                String.format(
                                    "There is no %s > %s currency conversion rate found for %s effective %s. To save the order, " +
                                    "request your Admin create a conversion rate for %s > %s effective %s, or remove %s.",
                                    plan.getCurrency().getCurrencyCode(),
                                    accountCurrencyCode,
                                    plan.getPlanId(),
                                    OrderServiceHelper.formatInstant(order.getStartDate(), timeZone),
                                    plan.getCurrency().getCurrencyCode(),
                                    accountCurrencyCode,
                                    OrderServiceHelper.formatInstant(order.getStartDate(), timeZone),
                                    plan.getPlanId()
                                )
                            );
                        }
                        orderLineItem.setCurrencyConversionRateId(UUID.fromString(currencyConversionRate.getId()));
                    }
                }
            });
    }

    public void backfillRampGroupId() {
        LOGGER.info("backfilling ramp group id. tenantId: {}", tenantIdProvider.provideTenantIdString());
        List<String> orderIds = orderDAO.getOrderIdsWithoutRampGroupId();
        if (CollectionUtils.isEmpty(orderIds)) {
            LOGGER.info("no orders found without ramp group id.");
            return;
        }
        for (String orderId : orderIds) {
            backfillRampGroupIdForOrder(orderId);
        }
    }

    private void backfillRampGroupIdForOrder(String orderId) {
        var order = orderGetService.getOrderByOrderId(orderId);
        // ensure none of the line items have rampGroupId assigned
        List<OrderLineItem> rampLinesWithRampGroupId = order
            .getLineItems()
            .stream()
            .filter(li -> li.getIsRamp() && li.getRampGroupId() != null)
            .toList();
        if (CollectionUtils.isNotEmpty(rampLinesWithRampGroupId)) {
            throw new ConflictingStateException(String.format("order %s has ramp line items with ramp group id", orderId));
        }
        OrderServiceHelper.populateOrderAndOrderLineIds(orderIdGenerator, order);
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext.transaction(configuration -> {
            DSLContext txnDslContext = DSL.using(configuration);
            order.getLineItems().forEach(li -> orderDAO.updateOrderLineItemRampGroupIdInTransaction(txnDslContext, li));
        });
        LOGGER.info("ramp group id backfilled for order: {}. lines: {}", orderId, order.getLineItems().size());
    }

    public void updateOrderAttributes(String orderId, OrderAttributesUpdateRequest updateRequest) {
        orderAttributesUpdateService.updateOrderAttributes(orderId, updateRequest);

        Order updatedOrder = orderGetService.getOrderByOrderId(orderId);
        syncUpdatedOrderToCRM(updatedOrder);
    }

    private Optional<Subscription> getCurrentSubscription(Order order) {
        if (order == null || order.getOrderType() == null) {
            return Optional.empty();
        }
        return switch (order.getOrderType()) {
            case RENEWAL -> Optional.of(subscriptionGetService.getSubscription(order.getRenewalForSubscriptionId()));
            case RESTRUCTURE -> Optional.of(subscriptionGetService.getSubscription(order.getRestructureForSubscriptionId()));
            default -> Optional.empty();
        };
    }

    public void updateOrderRenewedFromSubscriptionId(
        Configuration configuration,
        String orderId,
        String currentRenewedFromSubscriptionId,
        String updatedRenewedFromSubscriptionId,
        int updatedRenewedFromSubscriptionVersion
    ) {
        DSLContext dslContext = DSL.using(configuration);
        int rowsUpdated = orderDAO.updateOrderRenewedFromSubscriptionId(
            dslContext,
            orderId,
            currentRenewedFromSubscriptionId,
            updatedRenewedFromSubscriptionId,
            updatedRenewedFromSubscriptionVersion
        );
        if (rowsUpdated != 1) {
            throw new ServiceFailureException(
                String.format(
                    "no rows updated when updating order %s renewed from subscription id from %s to %s",
                    orderId,
                    currentRenewedFromSubscriptionId,
                    updatedRenewedFromSubscriptionId
                )
            );
        }

        Subscription updatedRenewedFromSubscription = subscriptionGetService.getSubscription(updatedRenewedFromSubscriptionId);

        Map<String, List<SubscriptionCharge>> subscriptionChargesByChargeId = updatedRenewedFromSubscription
            .getCharges()
            .stream()
            // only consider charges that end at the end of the subscription
            .filter(charge -> charge.getEndDate().compareTo(updatedRenewedFromSubscription.getEndDate()) == 0)
            .collect(groupingBy(SubscriptionCharge::getChargeId));

        Optional<Order> orderOptional = orderDAO.getOrderByOrderId(dslContext, orderId);
        Order order = orderOptional.orElseThrow(() -> new ServiceFailureException(String.format("order %s not found", orderId)));

        // filter for line items that start at the start of the subscription and is RENEWAL action type
        List<OrderLineItem> renewalLineItems = order
            .getLineItems()
            .stream()
            .filter(lineItem -> lineItem.getAction() == ActionType.RENEWAL && lineItem.getEffectiveDate().compareTo(order.getStartDate()) == 0)
            .toList();

        updateRenewalOrderLineItems(dslContext, renewalLineItems, subscriptionChargesByChargeId);
    }

    // look for matching subscription charges in the updated renewed from subscription based on chargeId
    // if no subscription charge is found, set the order line item action to ADD and clear baseExternalSubscriptionChargeId
    // if a single subscription charge is found, set to the baseExternalSubscriptionChargeId and save in a set to avoid re-use
    // if multiple subscription charges are found, look for one that hasn't been used by another order line item already
    private void updateRenewalOrderLineItems(
        DSLContext dslContext,
        List<OrderLineItem> renewalLineItems,
        Map<String, List<SubscriptionCharge>> subscriptionChargesByChargeId
    ) {
        Set<String> renewedSubscriptionChargeIds = new HashSet<>();

        renewalLineItems.forEach(lineItem -> {
            List<SubscriptionCharge> subscriptionCharges = subscriptionChargesByChargeId.get(lineItem.getChargeId());
            if (CollectionUtils.isEmpty(subscriptionCharges)) {
                LOGGER.info("no matching subscription charge found for order line item {}, setting action to ADD", lineItem.getId());
                orderDAO.setOrderLineItemToAdd(dslContext, lineItem.getId());
            } else {
                boolean orderLineItemUpdated = false;
                for (SubscriptionCharge subscriptionCharge : subscriptionCharges) {
                    // look for matching chargeIds in updated renewed from subscription charges and hasn't been used by another orderLineItem already
                    if (!renewedSubscriptionChargeIds.contains(subscriptionCharge.getSubscriptionChargeId())) {
                        // found a matching subscription charge. Update order line item and save in set to avoid re-use
                        String subscriptionChargeId = subscriptionCharge.getSubscriptionChargeId();
                        orderDAO.updateOrderLineItemBaseExternalSubscriptionChargeId(dslContext, lineItem.getId(), subscriptionChargeId);
                        renewedSubscriptionChargeIds.add(subscriptionChargeId);
                        orderLineItemUpdated = true;
                    }
                }

                if (!orderLineItemUpdated) {
                    LOGGER.info("no matching subscription charge found for order line item {}, setting action to ADD", lineItem.getId());
                    orderDAO.setOrderLineItemToAdd(dslContext, lineItem.getId());
                }
            }
        });
    }

    public void setOrderTypeToNew(Configuration configuration, Order order) {
        DSLContext dslContext = DSL.using(configuration);
        String orderId = order.getOrderId();

        int rowsUpdated = orderDAO.setOrderTypeToNew(dslContext, orderId);
        if (rowsUpdated != 1) {
            throw new ServiceFailureException(String.format("no rows updated when updating order %s type to NEW", orderId));
        }

        if (StringUtils.isNotBlank(order.getCompositeOrderId())) {
            orderDAO.setShouldRegeneratePdfInTransaction(dslContext, orderId, true);
        }

        // action types that can cause the creation of new subscription charges.
        // When a restructure or renewal order is converted to new, order line items with these action types need to be updated to ADD
        Set<ActionType> updatableActionTypes = Set.of(ActionType.RENEWAL, ActionType.RESTRUCTURE);
        List<OrderLineItem> orderLineItems = orderDAO.getOrderLineItemsByOrderId(dslContext, order.getOrderId());
        long numberOfLineItemsToUpdate = orderLineItems.stream().filter(lineItem -> updatableActionTypes.contains(lineItem.getAction())).count();
        rowsUpdated = orderDAO.setOrderLineItemsToAdd(dslContext, orderId, updatableActionTypes);

        if (rowsUpdated != numberOfLineItemsToUpdate) {
            throw new ServiceFailureException(
                String.format(
                    "number of order line items updated %d does not match expected number %d when updating order %s line item action types to ADD",
                    rowsUpdated,
                    numberOfLineItemsToUpdate,
                    orderId
                )
            );
        }
    }

    public void removeCompositeOrderAssociation(DSLContext context, String orderId) {
        orderDAO.removeCompositeOrderAssociation(context, orderId);
    }
}
