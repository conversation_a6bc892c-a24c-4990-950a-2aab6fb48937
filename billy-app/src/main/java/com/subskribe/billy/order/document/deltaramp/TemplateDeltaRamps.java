package com.subskribe.billy.order.document.deltaramp;

import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

// A representation of ramped line items split by quantity instead of by time.
// This would be similar to a flat multi-year order with additional quantities added via a new line item at each ramp step.
// the amounts of each of these new stacked items are re-computed based on the contribution of each time slice and quantity. Using the ramp above:
// For example, and order with line items ramped each year for 3 years with quantities of 10, 15 and 20 and unit price of $100 / year would be presented as:
// item 1: starting at the start of year 1 with quantity of 10, ending at the end of the order. Total amount = 10 * 100 * 3 = 3000 (quantity of 10 for all 3 years)
// item 2: starting at the start of year 2 with quantity of 5, ending at the end of the order. Total amount = 5 * 100 * 2 = 1000 (quantity of 5 for 2 years)
// item 3: starting at the start of year 3 with quantity of 5, ending at the end of the order. Total amount = 5 * 100 * 1 = 500 (quantity of 5 for 1 year)
// Regular ramps can be graphically represented as a series of items separated by time:
//          [-]
//      [-] [-]
//  [-] [-] [-]
//  10  15  20
// This class flips the orientation to represent the ramps as a series of line stacked on top of each other, separated by quantity:
//         [--] 5
//      [-----] 5
//  [---------] 10
// NOTE: because the computation is synthetic, this representation is only valid if the unit prices of each item in the ramp are the same and quantities increase for each ramp step
public class TemplateDeltaRamps {

    private final DocumentRenderFormatter formatter;
    private final TimeZone timeZone;
    private final String currency;
    private final Instant orderEndDate;
    private final List<TemplateRampDeltaItems> rampDeltaItems;
    private boolean hasValidRampSteps;
    private String errorMessage = StringUtils.EMPTY;

    public TemplateDeltaRamps(
        List<OrderLineItemDetail> lineItems,
        long orderEndDate,
        DocumentRenderFormatter formatter,
        String currency,
        TimeZone timeZone,
        boolean verifyDeltaRampTotals
    ) {
        this.formatter = formatter;
        this.currency = currency;
        this.timeZone = timeZone;
        List<OrderLineItemDetail> rampedItems = lineItems.stream().filter(OrderLineItemDetail::getIsRamp).toList();
        List<OrderLineItemDetail> recurringItems = lineItems.stream().filter(item -> item.getCharge().isRecurring()).toList();
        this.orderEndDate = Instant.ofEpochSecond(orderEndDate);
        Map<String, List<OrderLineItemDetail>> recurringItemsByCharge = groupItemsByCharge(recurringItems);
        hasValidRampSteps = hasValidRampedItems(rampedItems, recurringItemsByCharge);
        rampDeltaItems = buildRampDeltaItems(recurringItemsByCharge, verifyDeltaRampTotals);
    }

    private boolean hasValidRampedItems(List<OrderLineItemDetail> rampedItems, Map<String, List<OrderLineItemDetail>> recurringItemsByCharge) {
        boolean rampItemsHaveUnitPrices = allRampItemsHaveUnitPrice(rampedItems);
        if (!rampItemsHaveUnitPrices) {
            errorMessage = "Some ramped items do not have valid unit prices";
            return false;
        }

        boolean rampItemsHaveIncreasingQuantity = allRampsHaveSameOrIncreasingQuantity(recurringItemsByCharge);

        if (!rampItemsHaveIncreasingQuantity) {
            errorMessage = "Ramped items have decreases in quantity.";
            return false;
        }

        return true;
    }

    public boolean isHasValidRampSteps() {
        return hasValidRampSteps;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public List<DeltaRampDateGroup> getRampDeltaItemDateGroups() {
        List<DeltaRampDateGroup> deltaRampDateGroups = new ArrayList<>();
        if (rampDeltaItems.isEmpty()) {
            return deltaRampDateGroups;
        }

        Map<Instant, List<TemplateRampDeltaItems>> rampDeltaItemsByStartDate = rampDeltaItems
            .stream()
            .collect(Collectors.groupingBy(TemplateRampDeltaItems::getRawStartDate));

        for (var entry : rampDeltaItemsByStartDate.entrySet()) {
            Instant startDate = entry.getKey();
            deltaRampDateGroups.add(new DeltaRampDateGroup(formatter, startDate, entry.getValue()));
        }

        return deltaRampDateGroups.stream().sorted(Comparator.comparing(DeltaRampDateGroup::getRawStartDate)).toList();
    }

    private List<TemplateRampDeltaItems> buildRampDeltaItems(Map<String, List<OrderLineItemDetail>> itemsByCharge, boolean verifyDeltaRampTotals) {
        List<TemplateRampDeltaItems> rampDeltaItems = new ArrayList<>();

        if (!hasValidRampSteps) {
            return rampDeltaItems;
        }

        for (var rampItems : itemsByCharge.values()) {
            // create stacked ramp delta items for each item of the same charge grouped by unit
            var lineItemsByUnitPrice = groupLineItemsByUnitPrice(rampItems);
            for (var lineItems : lineItemsByUnitPrice) {
                var nonZeroLineItems = lineItems.stream().filter(item -> item.getAction() != ActionType.NONE && item.getQuantity() != 0).toList();
                rampDeltaItems.addAll(buildRampDeltaItemsForCharge(verifyDeltaRampTotals, nonZeroLineItems));
            }
        }

        boolean itemAmountsMatch = deltaItemAmountsMatch(rampDeltaItems, itemsByCharge);

        if (!verifyDeltaRampTotals || itemAmountsMatch) {
            return rampDeltaItems;
        } else {
            hasValidRampSteps = false;
            errorMessage = "WARNING: Items amount mismatch";
            return List.of();
        }
    }

    private boolean deltaItemAmountsMatch(List<TemplateRampDeltaItems> rampDeltaItems, Map<String, List<OrderLineItemDetail>> itemsByCharge) {
        BigDecimal deltaTotal = rampDeltaItems.stream().map(TemplateRampDeltaItems::getUnformattedTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal lineItemTotal = itemsByCharge
            .values()
            .stream()
            .flatMap(Collection::stream)
            .map(OrderLineItemDetail::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        return deltaTotal.compareTo(lineItemTotal) == 0;
    }

    private List<List<OrderLineItemDetail>> groupLineItemsByUnitPrice(List<OrderLineItemDetail> lineItems) {
        return lineItems.stream().collect(Collectors.groupingBy(OrderLineItemDetail::getSellUnitPrice)).values().stream().toList();
    }

    // compute stacked ramp delta items for ramped items of the same charge
    private List<TemplateRampDeltaItems> buildRampDeltaItemsForCharge(boolean verifyDeltaRampTotals, List<OrderLineItemDetail> rampedItems) {
        List<TemplateRampDeltaItems> rampDeltaItems = new ArrayList<>();

        Optional<Long> groupEndDate = rampedItems.stream().map(OrderLineItemDetail::getEndDate).max(Comparator.naturalOrder());

        for (int i = 0; i < rampedItems.size(); i++) {
            var item = rampedItems.get(i);
            long rampDeltaQuantity;

            if (i > 0) {
                TemplateRampDeltaItems previousItem = rampDeltaItems.get(rampDeltaItems.size() - 1);
                rampDeltaQuantity = item.getQuantity() - previousItem.getTotalQuantity();
            } else {
                // if first ramp item, delta quantity = quantity of the item
                rampDeltaQuantity = item.getQuantity();
            }

            BigDecimal amountPerUnit = Numbers.scaledDivide(item.getAmount(), BigDecimal.valueOf(item.getQuantity()));
            BigDecimal listAmountPerUnit = Numbers.scaledDivide(item.getListAmount(), BigDecimal.valueOf(item.getQuantity()));

            for (TemplateRampDeltaItems previousRampDeltaItem : rampDeltaItems) {
                // for each previous delta item, compute the amount this ramp item contributes to the total based on the previous delta quantity and this item's per quantity amount
                BigDecimal previousItemDeltaQuantity = BigDecimal.valueOf(previousRampDeltaItem.getDeltaQuantity());
                previousRampDeltaItem.addToTotal(amountPerUnit.multiply(previousItemDeltaQuantity));
                previousRampDeltaItem.addToListTotal(listAmountPerUnit.multiply(previousItemDeltaQuantity));
            }

            if (rampDeltaQuantity > 0) {
                // if the ramp item has an increase in quantity, create a delta item. Otherwise, update previous ramp delta item amounts only
                TemplateRampDeltaItems deltaItem = new TemplateRampDeltaItems(
                    formatter,
                    timeZone,
                    item,
                    currency,
                    rampDeltaQuantity,
                    groupEndDate.map(Instant::ofEpochSecond).orElse(orderEndDate)
                );

                // proportionally assign the amount of the current ramp item to the delta item
                deltaItem.addToTotal(amountPerUnit.multiply(BigDecimal.valueOf(rampDeltaQuantity)));
                deltaItem.addToListTotal(listAmountPerUnit.multiply(BigDecimal.valueOf(rampDeltaQuantity)));
                rampDeltaItems.add(deltaItem);
            }
        }

        if (verifyDeltaRampTotals && !rampItemsTotalsMatch(rampDeltaItems, rampedItems)) {
            // if the total of the delta items doesn't match the total of the items, we don't have valid ramp delta items. Return empty list
            hasValidRampSteps = false;
            return List.of();
        }

        return rampDeltaItems;
    }

    // validate the line items total and delta items total match
    private boolean rampItemsTotalsMatch(List<TemplateRampDeltaItems> rampDeltaItems, List<OrderLineItemDetail> lineItems) {
        BigDecimal deltaItemTotalAmount = rampDeltaItems
            .stream()
            .map(TemplateRampDeltaItems::getUnformattedTotal)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal lineItemsTotalAmount = lineItems.stream().map(OrderLineItemDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        return deltaItemTotalAmount.compareTo(lineItemsTotalAmount) == 0;
    }

    private boolean allRampItemsHaveUnitPrice(List<OrderLineItemDetail> lineItems) {
        return lineItems.stream().allMatch(item -> item.getSellUnitPrice() != null);
    }

    private boolean allRampsHaveSameOrIncreasingQuantity(Map<String, List<OrderLineItemDetail>> itemsByCharge) {
        for (var rampItems : itemsByCharge.values()) {
            if (!rampItemsHaveSameOrIncreasingQuantity(rampItems)) {
                return false;
            }
        }

        return true;
    }

    private boolean rampItemsHaveSameOrIncreasingQuantity(List<OrderLineItemDetail> rampItems) {
        long quantity = 0;
        for (var item : rampItems) {
            long itemQuantity = item.getQuantity();
            if (itemQuantity < quantity) {
                return false;
            }

            quantity = itemQuantity;
        }

        return true;
    }

    private Map<String, List<OrderLineItemDetail>> groupItemsByCharge(List<OrderLineItemDetail> lineItems) {
        if (lineItems == null) {
            return Map.of();
        }

        // preserve line item sorting order while grouping
        return lineItems.stream().collect(Collectors.groupingBy(lineItem -> lineItem.getCharge().getId(), LinkedHashMap::new, Collectors.toList()));
    }
}
