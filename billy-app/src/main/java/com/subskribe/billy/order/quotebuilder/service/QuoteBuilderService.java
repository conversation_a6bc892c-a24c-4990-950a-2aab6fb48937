package com.subskribe.billy.order.quotebuilder.service;

import com.google.common.collect.ImmutableList;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.customfield.CustomFieldProxy;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldUpdateInput;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.quotebuilder.QuoteBuilderMetadata;
import com.subskribe.billy.order.quotebuilder.model.Answer;
import com.subskribe.billy.order.quotebuilder.model.BuildOrderFromAnswersRequest;
import com.subskribe.billy.order.quotebuilder.model.GuidedSellingUsecase;
import com.subskribe.billy.order.quotebuilder.model.ImmutableBuildOrderFromAnswersRequest;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.PlanIdGenerator;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.model.DocumentTemplateStatus;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.zeppa.customizations.bindings.AccountBinding;
import com.subskribe.zeppa.customizations.bindings.OrderInternalBinding;
import com.subskribe.zeppa.customizations.core.ChargeShape;
import com.subskribe.zeppa.customizations.core.PlanShape;
import com.subskribe.zeppa.customizations.core.ZeppaDriver;
import com.subskribe.zeppa.customizations.output.OrderBuilderOutput;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

public class QuoteBuilderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(QuoteBuilderService.class);

    private static final BigDecimal HUNDRED = new BigDecimal("100.00");

    private final AccountGetService accountGetService;

    private final EntityContextProvider entityContextProvider;

    private final ProductCatalogGetService catalogGetService;

    private final OrderService orderService;

    private final QuestionAnswerService questionAnswerService;

    private final CustomFieldProxy customFieldProxy;

    private final TenantSettingService tenantSettingService;

    private final DocumentTemplateGetService templateGetService;

    private final RateCardService rateCardService;

    @Inject
    public QuoteBuilderService(
        AccountGetService accountGetService,
        EntityContextProvider entityContextProvider,
        ProductCatalogGetService catalogGetService,
        OrderService orderService,
        QuestionAnswerService questionAnswerService,
        CustomFieldProxy customFieldProxy,
        TenantSettingService tenantSettingService,
        DocumentTemplateGetService templateGetService,
        RateCardService rateCardService
    ) {
        this.accountGetService = accountGetService;
        this.entityContextProvider = entityContextProvider;
        this.catalogGetService = catalogGetService;
        this.orderService = orderService;
        this.questionAnswerService = questionAnswerService;
        this.customFieldProxy = customFieldProxy;
        this.tenantSettingService = tenantSettingService;
        this.templateGetService = templateGetService;
        this.rateCardService = rateCardService;
    }

    public Order testQScriptOnUsecase(String accountId, String usecase, List<Answer> answers, String zeppaScript) {
        if (StringUtils.isBlank(zeppaScript)) {
            throw new InvalidInputException("cannot pass empty Zepp Q script for order creation");
        }

        questionAnswerService.validateAnswerList(answers, usecase);

        BuildOrderFromAnswersRequest buildOrderRequest = ImmutableBuildOrderFromAnswersRequest.builder()
            .accountId(accountId)
            .usecaseIdentifier(usecase)
            .answers(answers)
            // testing should never create order
            .shouldSaveOrder(false)
            .build();
        return buildOrderInternal(buildOrderRequest, zeppaScript);
    }

    public Order buildOrderFromAnswers(BuildOrderFromAnswersRequest buildOrderRequest) {
        String usecase = buildOrderRequest.getUsecaseIdentifier();
        List<Answer> answers = buildOrderRequest.getAnswers();
        GuidedSellingUsecase guidedSellingUsecase = questionAnswerService.validateAnswerList(answers, usecase);
        String zeppaQScript = questionAnswerService
            .getQScript(guidedSellingUsecase.getId())
            .orElseThrow(() ->
                new InvalidInputException(
                    String.format("qscript not added for usecase: %s, please add qscript to create order for guided selling", usecase)
                )
            );

        return buildOrderInternal(buildOrderRequest, zeppaQScript);
    }

    private Order buildOrderInternal(BuildOrderFromAnswersRequest buildOrderFromAnswersRequest, String zeppaScript) {
        // TODO: deal with multi entity shortly in this context
        String entityId = entityContextProvider.provideSingleEntityOrElseThrow();
        String accountId = buildOrderFromAnswersRequest.getAccountId();
        List<Answer> answers = buildOrderFromAnswersRequest.getAnswers();

        // check for account
        Account account = accountGetService.getAccount(accountId);
        Optional<AccountAddress> accountAddressOptional = accountGetService.getAccountAddress(account.getAddressId());
        AccountBinding accountBinding = AccountBinding.from(account, accountAddressOptional.orElse(null), account.getCustomFields());
        OrderBuilderOutput orderBuilderOutput = ZeppaDriver.fireOrderBuilderCustomization(
            accountBinding,
            answers,
            tenantSettingService.getTenantSetting().getDefaultTimeZone(),
            zeppaScript
        );

        OrderInternalBinding orderBinding = orderBuilderOutput.getInternalBinding();
        Order protoOder = orderBinding.sourceOrder();
        protoOder.setEntityId(entityId);
        List<PlanShape> planShapes = orderBuilderOutput.getPlanShapes();

        // set the currency for the order to be account currency
        // TODO: add support to select currencies
        protoOder.setCurrency(account.getCurrency());

        getCustomFieldFromInput(CustomFieldParentType.ORDER, orderBinding.getOrderCustomFields()).ifPresent(protoOder::setCustomFields);

        addPredefinedTerms(protoOder, orderBinding);

        if (CollectionUtils.isEmpty(planShapes)) {
            throw new InvalidInputException("Cannot create an order with empty plan list, At least one plan needs to be present for order creation");
        }

        Set<String> planIdentifiers = planShapes.stream().map(PlanShape::getPlanIdentifier).collect(Collectors.toSet());
        Map<String, List<Plan>> planIdentifierToPlan = new HashMap<>(planIdentifiers.size());
        for (String planIdentifier : planIdentifiers) {
            planIdentifierToPlan.computeIfAbsent(planIdentifier, identifier -> coercePlansFromIdentifier(identifier, protoOder));
        }

        // now walk through all the plan shapes and fill in the line items
        List<OrderLineItem> lineItems = new ArrayList<>();
        for (PlanShape planShape : planShapes) {
            List<Plan> plans = planIdentifierToPlan.get(planShape.getPlanIdentifier());
            Map<String, ChargeShape> chargeShapes = planShape.getChargeShapes();
            for (Plan plan : plans) {
                for (Charge charge : plan.getCharges()) {
                    Optional<ChargeShape> chargeShapeOptional = getChargeShape(chargeShapes, charge);
                    int numberOfLines = getNumberOfLinesToAdd(chargeShapeOptional.orElse(null), charge);

                    IntStream.range(0, numberOfLines).forEach(idx -> {
                        OrderLineItem orderLineItem = makeOrderLineItem(charge, plan, protoOder);
                        if (chargeShapeOptional.isPresent()) {
                            ChargeShape chargeShape = chargeShapeOptional.get();
                            if (chargeShape.getQuantity() != null) {
                                orderLineItem.setQuantity(chargeShape.getQuantity());
                            }

                            if (chargeShape.getDiscountPercent() != null) {
                                DiscountDetail discountDetail = new DiscountDetail();
                                discountDetail.setId(UUID.randomUUID().toString());
                                discountDetail.setPercent(chargeShape.getDiscountPercent().divide(HUNDRED));
                                orderLineItem.setDiscounts(List.of(discountDetail));
                            }

                            getCustomFieldFromInput(CustomFieldParentType.ORDER_ITEM, chargeShape.getLineCustomFields()).ifPresent(
                                orderLineItem::setCustomFieldEntriesFromCustomFieldObject
                            );

                            setOrderLinePriceAttributes(orderLineItem, chargeShape.getPriceAttributes(), charge);
                        }

                        // set the custom list price to 0.00 if not already set
                        if (charge.isCustom() && orderLineItem.getListUnitPrice() == null) {
                            orderLineItem.setListUnitPrice(BigDecimal.ZERO);
                        }

                        lineItems.add(orderLineItem);
                    });
                }
            }
        }
        protoOder.setLineItems(lineItems);

        // now try to create the order in dry run mode

        // NOTE: dryrun = NOT(shouldSaveOrder), the important thing here is NOT operator
        boolean isDryRun = !buildOrderFromAnswersRequest.getShouldSaveOrder().orElse(false);
        return orderService.addOrder(protoOder, isDryRun);
    }

    private void setOrderLinePriceAttributes(OrderLineItem orderLineItem, Map<String, String> priceAttributes, Charge charge) {
        if (MapUtils.isEmpty(priceAttributes)) {
            LOGGER.info("Line item price attributes not set since attributes are empty");
            return;
        }

        if (charge == null || charge.getChargeModel() != ChargeModel.RATE_CARD_LOOKUP || StringUtils.isEmpty(charge.getRateCardId())) {
            LOGGER.info("Could not find the right charge configuration to set the price attributes");
            return;
        }

        Optional<RateCard> rateCardOptional = rateCardService.getRateCard(charge.getRateCardId());

        if (rateCardOptional.isEmpty()) {
            LOGGER.info("Could not find rate card with id {} cannot set attributes", charge.getRateCardId());
            return;
        }

        RateCard rateCard = rateCardOptional.get();

        List<AttributeReference> finalList = new ArrayList<>();
        for (Map.Entry<String, String> entry : priceAttributes.entrySet()) {
            String attributeName = entry.getKey();
            String attributeValue = entry.getValue();
            Optional<PriceAttribute> attributeDefOptional = rateCardService.getOptionalPriceAttributeByName(attributeName);
            if (attributeDefOptional.isEmpty()) {
                LOGGER.info(String.format("price attribute by name: %s does not exist", attributeName));
                continue;
            }
            finalList.add(new AttributeReference(attributeDefOptional.get().getId(), attributeValue));
        }
        AttributeReferences attributeReferences = AttributeReferences.wrap(finalList);

        if (rateCard.resolvePrice(attributeReferences).isPresent()) {
            orderLineItem.setAttributeReferences(attributeReferences.getReferencesInOrder());
            LOGGER.info("Order line item attribute references set to {}", attributeReferences);
            return;
        }

        LOGGER.info("Could not resolve {} to any references in rate card {}", priceAttributes, rateCard.getId());
    }

    private void addPredefinedTerms(Order protoOder, OrderInternalBinding orderBinding) {
        if (CollectionUtils.isEmpty(orderBinding.getPredefinedTermNames())) {
            LOGGER.info("Predefined terms not added for order creation");
            return;
        }

        orderBinding
            .getPredefinedTermNames()
            .forEach(termName -> {
                if (StringUtils.isBlank(termName)) {
                    LOGGER.info("blank term name found while setting predefined term");
                    return;
                }

                Optional<DocumentTemplate> documentTemplateOptional = templateGetService.getTemplateByNameAndType(
                    termName,
                    DocumentTemplateType.ORDER,
                    Optional.of(DocumentTemplateStatus.ACTIVE)
                ); // add ACTIVE terms only

                if (documentTemplateOptional.isEmpty()) {
                    LOGGER.info("predefined term with name {} not found during order building", termName);
                    return;
                }

                DocumentTemplate termTemplate = documentTemplateOptional.get();

                if (!termTemplate.getIsUserSelectable()) {
                    LOGGER.info("predefined term with name {} not user selectable", termName);
                    return;
                }

                List<String> templateIds = protoOder.getOrderFormTemplateIds();
                if (!templateIds.contains(termTemplate.getTemplateId())) {
                    ImmutableList.Builder<String> builder = ImmutableList.builder();
                    builder.addAll(templateIds);
                    builder.add(termTemplate.getTemplateId());
                    protoOder.setOrderFormTemplateIds(builder.build());
                    LOGGER.info("predefined term with name {} added to order", termName);
                } else {
                    LOGGER.info("predefined term with name {} already in order", termName);
                }
            });
    }

    private int getNumberOfLinesToAdd(ChargeShape chargeShape, Charge charge) {
        int defaultCount = 1;
        // if the charge type is usage then simply add only once
        if (charge.getType() == ChargeType.USAGE) {
            return defaultCount;
        }

        if (chargeShape != null) {
            return defaultCount + chargeShape.getDuplicates();
        }
        return defaultCount;
    }

    private static Optional<ChargeShape> getChargeShape(Map<String, ChargeShape> chargeShapes, Charge charge) {
        if (chargeShapes.containsKey(charge.getName())) {
            return Optional.ofNullable(chargeShapes.get(charge.getName()));
        }

        if (StringUtils.isNotBlank(charge.getExternalId()) && chargeShapes.containsKey(charge.getExternalId())) {
            return Optional.ofNullable(chargeShapes.get(charge.getExternalId()));
        }

        if (chargeShapes.containsKey(charge.getChargeId())) {
            return Optional.ofNullable(chargeShapes.get(charge.getChargeId()));
        }

        return Optional.empty();
    }

    private static OrderLineItem makeOrderLineItem(Charge charge, Plan plan, Order protoOder) {
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setId(AutoGenerate.getNewUuid());
        orderLineItem.setOrderLineId(AutoGenerate.getNewId());
        orderLineItem.setAction(ActionType.ADD);
        orderLineItem.setPlanId(plan.getPlanId());
        orderLineItem.setChargeId(charge.getChargeId());
        orderLineItem.setEffectiveDate(protoOder.getStartDate());

        if (charge.getDefaultQuantity() != null) {
            orderLineItem.setQuantity(charge.getDefaultQuantity());
        } else if (charge.getMinQuantity() != null) {
            orderLineItem.setQuantity(charge.getMinQuantity());
        }

        return orderLineItem;
    }

    private List<Plan> coercePlansFromIdentifier(String identifier, Order order) {
        List<Plan> plans = coercePlansFromIdentifier(identifier);

        // filter out any DRAFT plans as well
        List<Plan> nonDraftPlans = plans.stream().filter(plan -> plan.getStatus() != PlanStatus.DRAFT).toList();
        if (nonDraftPlans.size() != plans.size()) {
            LOGGER.warn(QuoteBuilderMetadata.BUILD_ORDER, "Order builder tried to add DRAFT plans for order build");
        }

        // use only ACTIVE plans for new order
        if (order.getOrderType() == OrderType.NEW) {
            List<Plan> activePlans = nonDraftPlans.stream().filter(plan -> plan.getStatus() == PlanStatus.ACTIVE).toList();
            if (activePlans.size() != nonDraftPlans.size()) {
                LOGGER.warn(QuoteBuilderMetadata.BUILD_ORDER, "Order builder tried to add NON ACTIVE plans for order build");
            }
            return activePlans;
        }

        // other type of orders all plans are valid including DEPRECATED
        return nonDraftPlans;
    }

    private List<Plan> coercePlansFromIdentifier(String identifier) {
        if (identifier.startsWith(PlanIdGenerator.getPrefix())) {
            return List.of(catalogGetService.getPlan(identifier));
        }

        Optional<String> planId = catalogGetService.getPlanIdByExternalId(identifier);
        if (planId.isPresent()) {
            return List.of(catalogGetService.getPlan(planId.get()));
        }

        List<String> planIds = catalogGetService.getPlanIdsByName(identifier);
        if (CollectionUtils.isNotEmpty(planIds)) {
            return catalogGetService.getPlansByPlanIds(planIds);
        }
        String message = String.format("Plan with identifier: %s not found", identifier);
        throw new InvalidInputException(message);
    }

    private Optional<CustomField> getCustomFieldFromInput(CustomFieldParentType parentType, Map<String, Object> fieldsInput) {
        if (MapUtils.isEmpty(fieldsInput)) {
            return Optional.empty();
        }

        Map<String, CustomFieldUpdateInput> inputMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : fieldsInput.entrySet()) {
            String customFieldName = entry.getKey();
            Object value = entry.getValue();
            Optional<CustomFieldUpdateInput> inputOptional = objectValueToInput(value);
            inputOptional.ifPresent(input -> inputMap.put(customFieldName, input));
        }

        if (inputMap.isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(customFieldProxy.customFieldObjectFromInput(parentType, inputMap));
    }

    private static Optional<CustomFieldUpdateInput> objectValueToInput(Object value) {
        if (value == null) {
            return Optional.empty();
        }

        if (value instanceof String stringVal) {
            return Optional.of(new CustomFieldUpdateInput(stringVal, List.of()));
        }

        if (value instanceof List<?> listVal) {
            List<String> valueList = listVal.stream().filter(v -> v instanceof String).map(v -> (String) v).toList();
            return Optional.of(new CustomFieldUpdateInput(null, valueList));
        }

        return Optional.empty();
    }
}
