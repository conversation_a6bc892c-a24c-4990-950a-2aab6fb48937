package com.subskribe.billy.order.document.deltaramp;

import static com.subskribe.billy.shared.enums.Cycle.YEAR;

import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.invoice.service.DiscountCalculator;
import com.subskribe.billy.order.document.temporal.TemplatePeriod;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import com.subskribe.billy.shared.temporal.Period;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.TimeZone;
import java.util.function.Function;
import org.apache.commons.lang3.StringUtils;

public class TemplateRampDeltaItems {

    private final DocumentRenderFormatter formatter;
    private final TimeZone timeZone;
    private final OrderLineItemDetail orderLineItemDetail;
    private final String currency;
    private final long deltaQuantity;
    private final Instant endDate;
    private BigDecimal listTotal;
    private BigDecimal total;

    public TemplateRampDeltaItems(
        DocumentRenderFormatter formatter,
        TimeZone timeZone,
        OrderLineItemDetail orderLineItemDetail,
        String currency,
        long deltaQuantity,
        Instant endDate
    ) {
        this.formatter = formatter;
        this.timeZone = timeZone;
        this.orderLineItemDetail = orderLineItemDetail;
        this.currency = currency;
        this.deltaQuantity = deltaQuantity;
        this.endDate = endDate;
        listTotal = BigDecimal.ZERO;
        total = BigDecimal.ZERO;
    }

    public String getPlanName() {
        PlanJson plan = orderLineItemDetail.getPlan();
        return StringUtils.isBlank(plan.getDisplayName()) ? plan.getName() : plan.getDisplayName();
    }

    public String getChargeName() {
        ChargeJson charge = orderLineItemDetail.getCharge();
        return StringUtils.isBlank(charge.getDisplayName()) ? charge.getName() : charge.getDisplayName();
    }

    public long getDeltaQuantity() {
        return deltaQuantity;
    }

    public long getTotalQuantity() {
        return orderLineItemDetail.getQuantity();
    }

    public String getStartDate() {
        return formatter.dateFormat(getRawStartDate());
    }

    public Instant getRawStartDate() {
        return Instant.ofEpochSecond(orderLineItemDetail.getEffectiveDate());
    }

    public String getFormattedTermLength() {
        BigDecimal years = Period.toDurationInYears(Instant.ofEpochSecond(orderLineItemDetail.getEffectiveDate()), endDate, timeZone);

        BigDecimal periodInYears = years.stripTrailingZeros();
        return String.format("%s %s", periodInYears.toPlainString(), periodInYears.doubleValue() > 1 ? YEAR.getPluralName() : YEAR.getSingularName());
    }

    public String getTermLengthInYears() {
        BigDecimal years = Period.toDurationInYears(Instant.ofEpochSecond(orderLineItemDetail.getEffectiveDate()), endDate, timeZone);
        return years.stripTrailingZeros().toPlainString();
    }

    public TemplatePeriod getPeriod() {
        return formatter.dateRangeToPeriod(getRawStartDate().getEpochSecond(), endDate.getEpochSecond());
    }

    public BigDecimal getUnformattedTotal() {
        return Numbers.makeCurrencyScale(total);
    }

    public String getTotal() {
        return formatter.currencyFormat(getUnformattedTotal(), currency);
    }

    public void addToTotal(BigDecimal amount) {
        total = total.add(amount);
    }

    public BigDecimal getUnformattedListTotal() {
        return Numbers.makeCurrencyScale(listTotal);
    }

    public String getListTotal() {
        return formatter.currencyFormat(getUnformattedListTotal(), currency);
    }

    public void addToListTotal(BigDecimal amount) {
        listTotal = listTotal.add(amount);
    }

    public Function<String, String> getRoundedListPrice() {
        return input -> formatter.currencyFormat(orderLineItemDetail.getListUnitPrice(), currency, input);
    }

    public Function<String, String> getRoundedSellPrice() {
        return input -> formatter.currencyFormat(orderLineItemDetail.getSellUnitPrice(), currency, input);
    }

    public boolean getIsRecurringYearly() {
        return (
            orderLineItemDetail.getCharge().getType() == ChargeType.RECURRING && orderLineItemDetail.getCharge().getRecurrence().getCycle() == YEAR
        );
    }

    public String getLineItemDiscountPercent() {
        BigDecimal lineItemDiscount = getItemDiscount();
        if (Numbers.isZero(lineItemDiscount)) {
            return StringUtils.EMPTY;
        }
        return formatter.formatPercent(lineItemDiscount);
    }

    private BigDecimal getItemDiscount() {
        return DiscountCalculator.getLineItemDiscount(orderLineItemDetail);
    }
}
