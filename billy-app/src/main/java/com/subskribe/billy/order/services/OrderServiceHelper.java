package com.subskribe.billy.order.services;

import static com.subskribe.billy.order.services.OrderDiscountService.updateDiscountAmounts;
import static com.subskribe.billy.order.services.OrderDiscountService.updatePredefinedDiscountAmounts;

import com.google.common.collect.Lists;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceGroup;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceService;
import com.subskribe.billy.attachments.model.Attachment;
import com.subskribe.billy.attachments.service.AttachmentsService;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationship;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationshipType;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.customization.service.CustomizationService;
import com.subskribe.billy.esign.model.ElectronicSignature;
import com.subskribe.billy.esign.services.EsignService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.DuplicateChargeException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceItemPreview;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.model.PurchaseOrder;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.tax.model.ImmutableTaxPreviewInput;
import com.subskribe.billy.invoice.tax.model.TaxLineItem;
import com.subskribe.billy.invoice.tax.model.TaxPreviewInput;
import com.subskribe.billy.invoice.tax.model.TaxTransaction;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.PricingOverride;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.model.TaxRate;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.model.ChargeDefaultAttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.model.PriceTableElement;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.PriceTiersHelper;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.DataValidation;
import com.subskribe.billy.shared.entitycache.EntityCache;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.enums.Status;
import com.subskribe.billy.shared.enums.TaxExemptionUseCode;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.template.services.DocumentCustomContentService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Currency;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.ws.rs.ForbiddenException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class OrderServiceHelper {

    private static final Set<ChargeType> ALLOWED_CHARGE_TYPES_FOR_PRICE_ATTRIBUTION = Set.of(ChargeType.RECURRING, ChargeType.ONE_TIME);

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderServiceHelper.class);
    private static final String CLOSED_OPPORTUNITY_REGEX = "(?i)closed";

    // maximum value TOTAL_AMOUNT can be in the db currently
    private static final BigDecimal MAX_TOTAL_AMOUNT_FOR_ORDER = BigDecimal.valueOf(1_000_000_000_000L);

    private static final Set<ActionType> AMENDMENT_ACTION_TYPES_FOR_USAGE_RELATED_CHARGES = Set.of(
        ActionType.ADD,
        ActionType.REMOVE,
        ActionType.UPDATE
    );

    private static final List<ActionType> AMENDMENT_ACTION_TYPES_FOR_PREPAID_DRAWDOWN = List.of(ActionType.NONE, ActionType.ADD);

    private static final Set<OrderType> ORDER_TYPES_WITH_USAGE_CHARGE_ALLOWED_FOR_UPDATE = Set.of(OrderType.AMENDMENT, OrderType.RESTRUCTURE);

    public static void validateOrderName(Order order) {
        if (StringUtils.isBlank(order.getName())) {
            return;
        }

        Validator.validateStringLength(order.getName(), 1, 255, "name");
    }

    static void validateOpportunity(Order order) {
        if (StringUtils.isBlank(order.getSfdcOpportunityId())) {
            return;
        }

        Validator.validateStringNotBlank(order.getSfdcOpportunityName(), "sfdcOpportunityName is required if sfdcOpportunityId is present");
    }

    static void validatePurchaseOrder(Order order) {
        if (StringUtils.isBlank(order.getPurchaseOrderNumber())) {
            return;
        }

        Validator.validateNotEmptyStringLength(order.getPurchaseOrderNumber(), PurchaseOrder.MAX_PURCHASE_ORDER_NUMBER_LENGTH, "purchaseOrderNumber");
    }

    static void updateLineItemEndDateByCharge(
        FeatureService featureService,
        List<OrderLineItem> orderLineItemList,
        Charge charge,
        Instant startDate,
        Instant endDate,
        ZoneId zoneId
    ) {
        if (CollectionUtils.isEmpty(orderLineItemList)) {
            return;
        }

        orderLineItemList
            .stream()
            // for non-ramp line items, set effective date and end date if not present and validate if present
            .filter(orderLineItem -> !orderLineItem.getIsRamp())
            .forEach(li -> {
                if (li.getEffectiveDate() == null) {
                    li.setEffectiveDate(startDate);
                }
                if (li.getEndDate() == null) {
                    li.setEndDate(endDate);
                }
                validateLineItemDates(featureService, li, charge, startDate, endDate, zoneId);
            });

        updateRampOnlyItems(orderLineItemList, endDate);
    }

    static void addSfdcFieldsToOrder(Order order, Optional<Opportunity> salesforceOpportunity, boolean isPrimaryOrderForOpportunity) {
        if (salesforceOpportunity.isEmpty()) {
            return;
        }

        // TODO: add this to the mapper
        order.setSfdcOpportunityName(salesforceOpportunity.get().getName());
        order.setSfdcOpportunityType(salesforceOpportunity.get().getType());
        order.setSfdcOpportunityStage(salesforceOpportunity.get().getStage());
        order.setIsPrimaryOrderForSfdcOpportunity(isPrimaryOrderForOpportunity);
    }

    private static void updateRampOnlyItems(List<OrderLineItem> orderLineItemList, Instant endDate) {
        var rampLineItems = orderLineItemList
            .stream()
            .filter(OrderLineItem::getIsRamp)
            .sorted(Comparator.comparing(OrderLineItem::getEffectiveDate))
            .collect(Collectors.toList());

        for (int i = 1; i < rampLineItems.size(); i++) {
            OrderLineItem currentLineItem = rampLineItems.get(i - 1);
            OrderLineItem nextLineItem = rampLineItems.get(i);
            if (currentLineItem.getEffectiveDate().compareTo(nextLineItem.getEffectiveDate()) == 0) {
                var message = "Same effective dates for line items identified in the ramp deal for charge id " + nextLineItem.getChargeId();
                LOGGER.info(message);
                throw new IllegalArgumentException(message);
            }

            validateAndSetRampedLineItemEndDate(currentLineItem, nextLineItem.getEffectiveDate());
        }

        if (CollectionUtils.isNotEmpty(rampLineItems)) {
            OrderLineItem currentLineItem = rampLineItems.get(rampLineItems.size() - 1);
            validateAndSetRampedLineItemEndDate(currentLineItem, endDate);
        }
    }

    private static void validateAndSetRampedLineItemEndDate(OrderLineItem currentLineItem, Instant endDate) {
        if (currentLineItem.getEndDate() != null && currentLineItem.getEndDate().compareTo(endDate) != 0) {
            String message = String.format("Provided end date %s did not match next ramp interval %s", currentLineItem.getEndDate(), endDate);
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }
        currentLineItem.setEndDate(endDate);
    }

    static void fillNetEffectOrderLines(List<Order> orders) {
        orders.forEach(OrderServiceHelper::fillNetEffectOrderLines);
    }

    public static void fillNetEffectOrderLines(Order order) {
        order.setLineItemsNetEffect(getNetEffectOrderLines(order.getLineItems()));
    }

    private static List<OrderLineItem> getNetEffectOrderLines(List<OrderLineItem> orderLineItems) {
        if (CollectionUtils.isEmpty(orderLineItems)) {
            return Lists.newArrayList();
        }

        return orderLineItems.stream().filter(orderLineItem -> orderLineItem.getAction() != ActionType.NONE).collect(Collectors.toList());
    }

    static void updateSubscriptionChargeIdInOrderLine(Order order, Subscription subscription) {
        var orderLineToSubscriptionChargeMap = subscription
            .getCharges()
            .stream()
            .flatMap(c -> c.getOrderLines().stream().map(o -> new AbstractMap.SimpleImmutableEntry<>(o, c)))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (val1, val2) -> val1)); // Todo: Ramp deals can have multiple charges

        order.getLineItems().forEach(li -> updateSubscriptionChargeId(orderLineToSubscriptionChargeMap, li));
    }

    @SuppressWarnings("PMD.UnusedPrivateMethod") // PMD bug, it does not detect the usage
    private static void updateSubscriptionChargeId(Map<String, SubscriptionCharge> orderLineToSubscriptionChargeMap, OrderLineItem li) {
        if (li.getSubscriptionChargeId() == null && orderLineToSubscriptionChargeMap.containsKey(li.getOrderLineId())) {
            li.setSubscriptionChargeId(orderLineToSubscriptionChargeMap.get(li.getOrderLineId()).getId());
            li.setExternalSubscriptionChargeId(orderLineToSubscriptionChargeMap.get(li.getOrderLineId()).getSubscriptionChargeId());
            li.setSubscriptionChargeGroupId(orderLineToSubscriptionChargeMap.get(li.getOrderLineId()).getSubscriptionChargeGroupId());
        }
    }

    static void updateOrderAmountsFromInvoice(Order order, InvoicePreview invoicePreview) {
        order.setTotalAmount(invoicePreview.getSubtotal());
        order.setTotalListAmount(invoicePreview.getSubtotal().add(invoicePreview.getTotalDiscount()));
        order.setTotalListAmountBeforeOverride(invoicePreview.getTotalListAmountBeforeOverrides());

        Map<String, InvoiceItemPreview> orderLineInvoiceItemMap = new HashMap<>();
        invoicePreview.getLineItems().forEach(li -> orderLineInvoiceItemMap.put(li.orderLineItemId(), li));
        order.getLineItems().forEach(li -> updateOrderLineFromInvoicePreviewLine(orderLineInvoiceItemMap, li));
        order.getLineItemsNetEffect().forEach(li -> updateOrderLineFromInvoicePreviewLine(orderLineInvoiceItemMap, li));
    }

    private static void updateOrderLineFromInvoicePreviewLine(Map<String, InvoiceItemPreview> orderLineInvoiceItemMap, OrderLineItem li) {
        InvoiceItemPreview invoiceItemPreview = orderLineInvoiceItemMap.get(li.getOrderLineId());
        if (invoiceItemPreview == null) {
            return;
        }

        li.setListAmountBeforeOverride(invoiceItemPreview.listAmountBeforeOverride());
        li.setListAmount(invoiceItemPreview.listAmount());
        li.setAmount(invoiceItemPreview.amount());
        li.setSellUnitPrice(invoiceItemPreview.sellUnitPrice());
        li.setListUnitPrice(invoiceItemPreview.listUnitPrice());
        li.setListUnitPriceBeforeOverride(invoiceItemPreview.listUnitPriceBeforeOverride());
        li.setDiscountAmount(invoiceItemPreview.discountAmount());

        if (CollectionUtils.isNotEmpty(li.getDiscounts())) {
            updateDiscountAmounts(li.getDiscounts(), invoiceItemPreview.inlineDiscountAmountMap());
        }

        if (CollectionUtils.isNotEmpty(li.getPredefinedDiscounts())) {
            updatePredefinedDiscountAmounts(li.getPredefinedDiscounts(), invoiceItemPreview.predefinedDiscountAmountMap());
        }
    }

    static void populateOrderAndOrderLineIds(OrderIdGenerator orderIdGenerator, Order order) {
        if (order == null) {
            return;
        }

        if (order.getId() == null) {
            order.setId(AutoGenerate.getNewUuid());
        }

        if (StringUtils.isBlank(order.getOrderId())) {
            order.setOrderId(orderIdGenerator.generate());
        }

        if (order.getLineItems() == null) {
            return;
        }

        order
            .getLineItems()
            .forEach(li -> {
                if (li.getId() == null) {
                    li.setId(AutoGenerate.getNewUuid());
                }
                if (StringUtils.isBlank(li.getOrderLineId())) {
                    li.setOrderLineId(AutoGenerate.getNewId());
                }
                li.setOrderId(order.getOrderId());
            });

        Map<String, List<OrderLineItem>> rampGroups = order
            .getLineItems()
            .stream()
            .filter(OrderLineItem::getIsRamp)
            .collect(Collectors.groupingBy(li -> "%s_%s".formatted(li.getChargeId(), li.getQuantity() > 0)));
        rampGroups.forEach((chargeId, lineItems) -> {
            UUID rampGroupId = AutoGenerate.getNewUuid();
            lineItems.forEach(li -> li.setRampGroupId(rampGroupId));
        });
    }

    public static void validateOrderStatusTransition(OrderStatus currentStatus, OrderStatus newStatus) {
        switch (currentStatus) {
            case DRAFT -> validateOrderStatusErrorException(
                currentStatus,
                newStatus,
                List.of(OrderStatus.DRAFT, OrderStatus.SUBMITTED, OrderStatus.EXPIRED)
            );
            case SUBMITTED -> validateOrderStatusErrorException(currentStatus, newStatus, List.of(OrderStatus.DRAFT, OrderStatus.APPROVED));
            case APPROVED -> validateOrderStatusErrorException(currentStatus, newStatus, List.of(OrderStatus.DRAFT, OrderStatus.EXECUTED));
            case EXECUTED -> {
                LOGGER.info("Order in executed state cannot be updated");
                throw new IllegalStateException("Order in executed state cannot be updated");
            }
            default -> {
                LOGGER.info("current status of the order is not recognized. current order status = {}", currentStatus);
                throw new IllegalStateException("current status of the order is not recognized. current order status = " + currentStatus);
            }
        }
    }

    private static void validateOrderStatusErrorException(OrderStatus currentStatus, OrderStatus newStatus, List<OrderStatus> validStates) {
        if (!validStates.contains(newStatus)) {
            var message = String.format(
                "cannot transition to order status %s from status %s. Valid status options are %s",
                newStatus,
                currentStatus,
                String.join(", ", validStates.toString())
            );
            LOGGER.info(message);
            throw new IllegalStateException(message);
        }
    }

    static void addOrderRankToLineItems(Order order) {
        if (order == null || order.getLineItems() == null) {
            return;
        }

        AtomicInteger i = new AtomicInteger(1);
        order.getLineItems().forEach(li -> li.setRank(i.getAndIncrement()));
    }

    static void addOrderCreatedByAndOwner(Order order, UserService userService) {
        setOrderCreatedBy(order);

        User owner = null;
        if (StringUtils.isNotBlank(order.getOwnerId())) {
            owner = userService.getUser(order.getOwnerId());
        } else if (StringUtils.isNotBlank(order.getCreatedBy()) && DataValidation.isEmailValid(order.getCreatedBy())) {
            Optional<User> ownerOptional = userService.getUserByEmail(order.getCreatedBy());
            if (ownerOptional.isPresent()) {
                owner = ownerOptional.get();
            }
        }

        setOrderOwner(order, owner);
    }

    static void updateOrderOwner(Order order, UserService userService) {
        if (StringUtils.isBlank(order.getOwnerId())) {
            return;
        }

        User owner = userService.getUser(order.getOwnerId());
        setOrderOwner(order, owner);
    }

    private static void setOrderCreatedBy(Order order) {
        if (order == null) {
            return;
        }

        String orderCreatedByUser = StringUtils.EMPTY;
        Optional<BillyAuthPrincipal> authPrincipal = CurrentUserProvider.getAuthPrincipal();
        if (authPrincipal.isPresent()) {
            String getCurrentUser = authPrincipal.get().getName();
            if (StringUtils.isNotBlank(getCurrentUser)) {
                orderCreatedByUser = getCurrentUser;
            }
        }

        order.setCreatedBy(orderCreatedByUser);
    }

    private static void setOrderOwner(Order order, User owner) {
        if (order == null || owner == null) {
            return;
        }
        if (owner.getState() != Status.ACTIVE) {
            throw new IllegalArgumentException("Cannot assign a disabled user as an owner to an order");
        }

        order.setOwnerId(owner.getUserId());
    }

    // todo - handle failures and return them as warnings along with order object
    static void processDocumentCustomContent(Order order, DocumentCustomContentService documentCustomContentService) {
        if (order.getDocumentCustomContent() == null) {
            return;
        }
        // empty content -> delete it
        if (StringUtils.isBlank(order.getDocumentCustomContent().getContent()) && order.getDocumentCustomContent().getId() != null) {
            documentCustomContentService.deleteDocumentCustomContent(order.getDocumentCustomContent().getId().toString());
            return;
        }
        // content present -> update or create
        order.getDocumentCustomContent().setOrderId(order.getOrderId());
        documentCustomContentService.upsertDocumentCustomContent(order.getDocumentCustomContent());
    }

    static void validateUniqueUsageChargePerOrder(Order order, List<Plan> plansInOrder) {
        if (CollectionUtils.isEmpty(order.getLineItems())) {
            return;
        }

        Map<String, Charge> planChargesById = plansInOrder
            .stream()
            .flatMap(plan -> plan.getCharges().stream())
            .filter(charge -> ChargeType.USAGE == charge.getType())
            // If order is a renewal, only renewable usage charges are expected to be present in the order
            .filter(charge -> {
                if (order.getOrderType() == OrderType.RENEWAL) {
                    return charge.getIsRenewable();
                }
                return true;
            })
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        // if there are no usage charges for plans in the order just return
        if (planChargesById.isEmpty()) {
            return;
        }

        // check to see if any of the usage charges have more than one count
        Map<String, Long> usageChargeOccurrences = order
            .getLineItems()
            .stream()
            .map(OrderLineItem::getChargeId)
            .filter(planChargesById::containsKey)
            .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        if (usageChargeOccurrences.isEmpty()) {
            // this should not happen the plan had a usage charge but order is missing it
            String message = String.format("expected line item for charges %s missing in order", planChargesById);
            LOGGER.error(message);
            throw new ServiceFailureException(message);
        }

        usageChargeOccurrences.forEach((chargeId, value) -> {
            if (value > 1L) {
                throw new DuplicateChargeException(planChargesById.get(chargeId).getType(), chargeId);
            }
        });
    }

    static void validateAllChargesInNewPlansExist(List<OrderLineItem> orderLineItems, List<Plan> plans, List<String> existingPlanIdsInSubscription) {
        if (CollectionUtils.isEmpty(orderLineItems)) {
            return;
        }

        if (CollectionUtils.isEmpty(plans)) {
            LOGGER.info("Plan list is empty");
            throw new IllegalStateException("Plan list is empty");
        }

        Map<String, Set<String>> missingOrderCharges = findMissingOrderChargesInNewPlan(plans, orderLineItems, existingPlanIdsInSubscription);
        if (MapUtils.isEmpty(missingOrderCharges)) {
            return;
        }

        missingOrderCharges
            .keySet()
            .forEach(planId -> {
                var message = String.format(
                    "charges %s from plan id %s are missing in order",
                    String.join(", ", missingOrderCharges.get(planId)),
                    planId
                );
                LOGGER.info(message);
                throw new IllegalArgumentException(message);
            });
    }

    private static Map<String, Set<String>> findMissingOrderChargesInNewPlan(
        List<Plan> plans,
        List<OrderLineItem> orderLineItems,
        List<String> existingPlanIdsInSubscription
    ) {
        Map<String, Set<String>> orderNewPlanIdToChargeIdsMap = getNewPlanIdToChargeIdsMapFromOrderLines(
            existingPlanIdsInSubscription,
            orderLineItems
        );
        if (MapUtils.isEmpty(orderNewPlanIdToChargeIdsMap)) {
            return Map.of();
        }

        Map<String, Set<String>> planIdToFullChargeIdsMap = getPlanIdToFullChargeIdsMapFromPlans(plans);
        if (MapUtils.isEmpty(planIdToFullChargeIdsMap)) {
            return Map.of();
        }

        Map<String, Set<String>> missingOrderCharges = new HashMap<>();
        orderNewPlanIdToChargeIdsMap
            .keySet()
            .forEach(planId -> {
                if (!orderNewPlanIdToChargeIdsMap.get(planId).containsAll(planIdToFullChargeIdsMap.get(planId))) {
                    planIdToFullChargeIdsMap.get(planId).removeAll(orderNewPlanIdToChargeIdsMap.get(planId));
                    missingOrderCharges.put(planId, planIdToFullChargeIdsMap.get(planId));
                }
            });
        return missingOrderCharges;
    }

    static Map<String, Set<String>> getNewPlanIdToChargeIdsMapFromOrderLines(
        List<String> existingPlanIdsInSubscription,
        List<OrderLineItem> orderLineItems
    ) {
        Validator.validateCollectionNotEmpty(orderLineItems, "orderLineItems");

        Map<String, Set<String>> newPlansInOrder = new HashMap<>();
        orderLineItems
            .stream()
            .filter(
                li ->
                    (li.getAction() == ActionType.ADD || li.getAction() == ActionType.RENEWAL || li.getAction() == ActionType.RESTRUCTURE) &&
                    !existingPlanIdsInSubscription.contains(li.getPlanId())
            )
            .forEach(li -> {
                newPlansInOrder.putIfAbsent(li.getPlanId(), new HashSet<>());
                newPlansInOrder.get(li.getPlanId()).add(li.getChargeId());
            });
        return newPlansInOrder;
    }

    static Map<String, Set<String>> getPlanIdToFullChargeIdsMapFromPlans(List<Plan> plans) {
        Map<String, Set<String>> chargesInPlansMap = new HashMap<>();
        plans.forEach(p -> {
            chargesInPlansMap.putIfAbsent(p.getPlanId(), new HashSet<>());
            p.getCharges().forEach(c -> chargesInPlansMap.get(p.getPlanId()).add(c.getChargeId()));
        });
        return chargesInPlansMap;
    }

    static void validateRampIntervals(Order order) {
        if (CollectionUtils.isEmpty(order.getRampInterval())) {
            return;
        }

        order
            .getRampInterval()
            .forEach(interval -> {
                if (interval == null) {
                    throw new InvalidInputException("Ramp intervals cannot be empty");
                }
            });

        order.getRampInterval().sort(Comparator.comparing(Instant::getEpochSecond));

        order
            .getRampInterval()
            .forEach(interval -> {
                if (interval.isAfter(order.getEndDate())) {
                    var message = String.format("Invalid ramp interval %s as it is after the order end date %s", interval, order.getEndDate());
                    LOGGER.info(message);
                    throw new IllegalArgumentException(message);
                }
            });

        var distinctIntervals = order.getRampInterval().stream().distinct().toList();
        if (order.getRampInterval().size() != distinctIntervals.size()) {
            LOGGER.info("Duplicate interval values are not allowed in rampIntervals");
            throw new IllegalArgumentException("Duplicate interval values are not allowed in rampIntervals");
        }
    }

    public static void validateDrawdownCharges(Order order, List<Plan> plans, Optional<Subscription> subscription) {
        if (CollectionUtils.isEmpty(order.getLineItems())) {
            return;
        }
        Map<String, Charge> chargeMap = plans
            .stream()
            .flatMap(plan -> plan.getCharges().stream())
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        Set<String> chargeIdsInSubscription = subscription
            .map(Subscription::getCharges)
            .map(charges -> charges.stream().map(SubscriptionCharge::getChargeId).collect(Collectors.toUnmodifiableSet()))
            .orElse(Set.of());

        // Get Line Items, which are not marked for Removal
        // Filter Line Items, which are related to Drawdown Charges
        // There can only be one instance of a drawdown charge per order/subscription
        order
            .getLineItems()
            .stream()
            .filter(li -> li.getAction() != ActionType.REMOVE && li.getAction() != ActionType.NONE)
            .map(OrderLineItem::getChargeId)
            .filter(chargeId -> chargeMap.get(chargeId).isDrawdown())
            .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
            .forEach((chargeId, count) -> {
                if (count > 1) {
                    String message = String.format("cannot have multiple line items for drawdown charge %s in order", chargeId);
                    LOGGER.info(message);
                    throw new InvalidInputException(message);
                }
                if (chargeIdsInSubscription.contains(chargeId)) {
                    String message = String.format("cannot have multiple line items for drawdown charge %s in subscription", chargeId);
                    LOGGER.info(message);
                    throw new InvalidInputException(message);
                }
            });
    }

    public static void validateLineItemDates(
        FeatureService featureService,
        OrderLineItem lineItem,
        Charge charge,
        Instant orderStartDate,
        Instant orderEndDate,
        ZoneId zoneId
    ) {
        Validator.validateNonNullArgument(orderStartDate, "order start date");
        Validator.validateNonNullArgument(orderEndDate, "order end date");
        if (lineItem.getEffectiveDate() == null) {
            lineItem.setEffectiveDate(orderStartDate);
        } else if (lineItem.getEffectiveDate().isBefore(orderStartDate) || lineItem.getEffectiveDate().isAfter(orderEndDate)) {
            var message = String.format(
                "lineItem effective date (%s) should be between the order start date (%s) and order end date (%s)",
                lineItem.getEffectiveDate(),
                orderStartDate,
                orderEndDate
            );
            throw new IllegalArgumentException(message);
        }

        validateLineItemEndDate(featureService, lineItem, charge, orderStartDate, orderEndDate, zoneId);
    }

    private static void validateLineItemEndDate(
        FeatureService featureService,
        OrderLineItem lineItem,
        Charge charge,
        Instant orderStartDate,
        Instant orderEndDate,
        ZoneId zoneId
    ) {
        if (lineItem.getEndDate() == null) {
            return;
        }

        if (featureService.isEnabled(Feature.ALLOW_ZERO_DURATION_ORDER_ITEMS)) {
            if (lineItem.getEffectiveDate().isAfter(lineItem.getEndDate())) {
                throw new InvalidInputException("Line item effective date cannot be after end date");
            }
        } else if (!lineItem.getEndDate().isAfter(lineItem.getEffectiveDate())) {
            throw new InvalidInputException("lineItem end date must be after effective date");
        }

        if (lineItem.getEndDate().isBefore(orderStartDate) || lineItem.getEndDate().isAfter(orderEndDate)) {
            var message = String.format(
                "lineItem end date (%s) should be between the order start date (%s) and order end date (%s)",
                lineItem.getEndDate(),
                orderStartDate,
                orderEndDate
            );
            throw new InvalidInputException(message);
        }

        validateAndSetOneTimeChargeDates(lineItem, charge, orderEndDate, zoneId);
    }

    private static void validateAndSetOneTimeChargeDates(OrderLineItem lineItem, Charge charge, Instant orderEndDate, ZoneId zoneId) {
        Validator.checkNonNullInternal(zoneId, "zoneId");
        Validator.checkNonNullInternal(lineItem.getEffectiveDate(), "effectiveDate");
        Validator.checkNonNullInternal(lineItem.getEndDate(), "endDate");

        if (charge.getType() != ChargeType.ONE_TIME) {
            return;
        }

        // null durationInMonths means a subscription term charge
        if (charge.getDurationInMonths() == null) {
            return;
        }

        Instant chargeDurationEndDate = DateTimeCalculator.plusMonths(zoneId, lineItem.getEffectiveDate(), charge.getDurationInMonths());
        if (
            lineItem.getEndDate() != null &&
            DateTimeCalculator.compareInstants(lineItem.getEndDate(), chargeDurationEndDate) != 0 &&
            DateTimeCalculator.compareInstants(lineItem.getEndDate(), orderEndDate) != 0
        ) {
            throw new IllegalArgumentException(
                String.format(
                    "line item end date %s does not match charge duration end date %s or order end date %s",
                    lineItem.getEndDate(),
                    chargeDurationEndDate,
                    orderEndDate
                )
            );
        }

        if (DateTimeCalculator.compareInstants(chargeDurationEndDate, orderEndDate) < 0) {
            lineItem.setEndDate(chargeDurationEndDate);
        } else {
            lineItem.setEndDate(orderEndDate);
        }
    }

    static Map<String, String> getChargeIdPlanIdMap(List<Plan> plans) {
        Map<String, String> chargeIdPlanIdMap = new HashMap<>();
        for (var plan : plans) {
            for (var charge : plan.getCharges()) {
                chargeIdPlanIdMap.putIfAbsent(charge.getChargeId(), plan.getPlanId());
            }
        }
        return chargeIdPlanIdMap;
    }

    private static Map<String, Charge> getChargeIdChargeMap(List<Plan> plans) {
        if (plans == null) {
            LOGGER.error("plans list should not be null");
            throw new ServiceFailureException("plans list should not be null");
        }

        Map<String, Charge> chargeIdChargeMap = new HashMap<>();
        for (Plan p : plans) {
            p.getCharges().forEach(c -> chargeIdChargeMap.put(c.getChargeId(), c));
        }

        return chargeIdChargeMap;
    }

    public static void updateFlatFeeQuantityInOrder(Order order, List<Plan> plans) {
        if (order == null || order.getLineItems() == null) {
            LOGGER.error("order and orderLines should not be null");
            throw new ServiceFailureException("order and orderLines should not be null");
        }

        var chargeIdChargeMap = getChargeIdChargeMap(plans);
        order
            .getLineItems()
            .forEach(orderLineItem -> updateFlatFeeQuantityInOrderLine(orderLineItem, chargeIdChargeMap.get(orderLineItem.getChargeId())));
    }

    public static void updateFlatFeeQuantityInOrderLine(OrderLineItem orderLineItem, Charge charge) {
        if (charge == null) {
            LOGGER.error("Charge cannot be null");
            throw new ServiceFailureException("Charge cannot be null");
        }

        if (!orderLineItem.getChargeId().equals(charge.getChargeId())) {
            LOGGER.error("Expected the charge object of the chargeId for the orderLineItem");
            throw new ServiceFailureException("Expected the charge object of the chargeId for the orderLineItem");
        }

        if (charge.getChargeModel() != ChargeModel.FLAT_FEE) {
            return;
        }

        if (charge.getType() == ChargeType.USAGE) {
            String message = "FLAT_FEE model shouldn't exist for USAGE charge type. ChargeId: " + charge.getChargeId();
            LOGGER.warn(message, charge);
            throw new IllegalStateException(message);
        }

        if (orderLineItem.getAction() == ActionType.UPDATE && charge.getType() != ChargeType.PERCENTAGE_OF) {
            var message = "OrderLine Action for flat fee charge type cannot be UPDATE. It has to be either of ADD/NONE/REMOVE";
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        if (orderLineItem.getQuantity() < 0 || orderLineItem.getQuantity() > 1) {
            String message = String.format(
                "FLAT_FEE quantity should be 1. Quantity specified is %d for charge %s",
                orderLineItem.getQuantity(),
                orderLineItem.getChargeId()
            );
            LOGGER.warn(message);
            throw new IllegalArgumentException(message);
        }

        if (orderLineItem.getAction() == ActionType.REMOVE) {
            orderLineItem.setQuantity(-1);
        } else if (orderLineItem.getAction() == ActionType.ADD) {
            orderLineItem.setQuantity(1);
        }
    }

    static void validateDeprecatedPlan(Plan plan, List<String> existingPlanIdsInSubscription) {
        // do not allow adding deprecated plans to an order if they are not present on existing subscription
        if (!existingPlanIdsInSubscription.contains(plan.getPlanId()) && plan.getStatus() == PlanStatus.DEPRECATED) {
            throw new IllegalArgumentException(String.format("Plan '%s' is deprecated and cannot be added to order", plan.getName()));
        }
    }

    static void validateCustomChargeLineItem(OrderLineItem lineItem) {
        if (lineItem.getListUnitPrice() == null) {
            throw new IllegalArgumentException(String.format("must provide custom listUnitPrice for custom charge %s", lineItem.getChargeId()));
        }

        Numbers.validateInputAmountScale(lineItem.getListUnitPrice());
    }

    static void validateListPriceOverride(OrderLineItem lineItem, Charge charge) {
        if (lineItem.getListPriceOverrideRatio() == null) {
            return;
        }

        if (!charge.getIsListPriceEditable()) {
            throw new IllegalArgumentException(String.format("cannot override list price for charge %s", charge.getChargeId()));
        }

        if (lineItem.getListPriceOverrideRatio().compareTo(BigDecimal.ZERO) < 0) {
            String message = String.format("list price override ratio cannot be negative, was %s", lineItem.getListPriceOverrideRatio());
            throw new IllegalArgumentException(message);
        }

        Numbers.validateInputRatioScale(lineItem.getListPriceOverrideRatio());
    }

    static void validatePricingOverride(OrderLineItem lineItem, Charge charge, FeatureService featureService) {
        PricingOverride pricingOverride = lineItem.getPricingOverride();
        if (pricingOverride == null) {
            return;
        }
        if (!featureService.isEnabled(Feature.NEGOTIATED_PRICE_TIERS)) {
            throw new InvalidInputException("Pricing override is not enabled");
        }
        if (!charge.getIsListPriceEditable()) {
            throw new InvalidInputException(String.format("cannot override list price for charge %s", charge.getChargeId()));
        }
        validatePricingOverridePriceTiers(pricingOverride, charge.getName());
        if (lineItem.getListPriceOverrideRatio() != null) {
            throw new InvalidInputException(
                String.format("List price override ratio cannot be present when pricing override is present for charge '%s'", charge.getName())
            );
        }
    }

    private static void validatePricingOverridePriceTiers(PricingOverride pricingOverride, String chargeName) {
        if (CollectionUtils.isEmpty(pricingOverride.getPriceTiers())) {
            throw new InvalidInputException("Price tiers are required for pricing override");
        }
        validatePricingOverrideQuantityConstraints(pricingOverride);
        Optional<String> error = PriceTiersHelper.validatePriceTiersOrReturnError(
            pricingOverride.getPriceTiers(),
            pricingOverride.getMinQuantity(),
            pricingOverride.getMaxQuantity()
        );
        if (error.isPresent()) {
            throw new InvalidInputException(String.format("Price tiers are not valid for charge '%s': %s", chargeName, error.get()));
        }
    }

    private static void validatePricingOverrideQuantityConstraints(PricingOverride pricingOverride) {
        Optional<Long> minQuantity = pricingOverride.getMinQuantity();
        Optional<Long> maxQuantity = pricingOverride.getMaxQuantity();
        if (minQuantity.isPresent() && minQuantity.get() < 0) {
            throw new InvalidInputException(String.format("Minimum quantity %s cannot be negative", minQuantity.get()));
        }
        if (maxQuantity.isPresent() && maxQuantity.get() < 0) {
            throw new InvalidInputException(String.format("Maximum quantity %s cannot be negative", maxQuantity.get()));
        }
        if (minQuantity.isPresent() && maxQuantity.isPresent() && minQuantity.get() > maxQuantity.get()) {
            throw new InvalidInputException(
                String.format("Minimum quantity %s cannot be greater than maximum quantity %s", minQuantity.get(), maxQuantity.get())
            );
        }
    }

    static void validateCurrencyOnPlans(
        CurrencyConversionRateGetService currencyConversionRateGetService,
        Currency expectedCurrency,
        List<Plan> plans
    ) {
        EntityCache<String, Set<String>> supportedCurrenciesCache = EntityCache.of(
            currencyConversionRateGetService::getSupportedCurrenciesForCurrency
        );
        validateCurrencies(supportedCurrenciesCache, plans, expectedCurrency);
    }

    private static void validateCurrencies(EntityCache<String, Set<String>> supportedCurrenciesCache, List<Plan> plans, Currency expectedCurrency) {
        List<Plan> plansWithDifferentCurrency = plans
            .stream()
            .filter(plan -> !expectedCurrency.getCurrencyCode().equals(plan.getCurrency().getCurrencyCode()))
            .collect(Collectors.toList());
        plansWithDifferentCurrency = plansWithDifferentCurrency
            .stream()
            .filter(plan -> !supportedCurrenciesCache.get(expectedCurrency.getCurrencyCode()).contains(plan.getCurrency().getCurrencyCode()))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(plansWithDifferentCurrency)) {
            return;
        }

        String message = String.format(
            "Currency mismatch for plans: [%s], Expected currency: %s",
            plansWithDifferentCurrency.stream().map(Plan::getName).collect(Collectors.joining(", ")),
            expectedCurrency.getCurrencyCode()
        );
        LOGGER.info(message);
        throw new InvalidInputException(message);
    }

    static void validateLineItemQuantity(OrderLineItem lineItem, Charge charge, OrderType orderType) {
        Validator.validateNonNullArguments(lineItem, charge, orderType);
        Optional<Long> minQuantity = Optional.ofNullable(charge.getMinQuantity());
        Optional<Long> maxQuantity = Optional.ofNullable(charge.getMaxQuantity());
        if (lineItem.getPricingOverride() != null) {
            minQuantity = lineItem.getPricingOverride().getMinQuantity();
            maxQuantity = lineItem.getPricingOverride().getMaxQuantity();
        }
        if (
            minQuantity.isPresent() &&
            lineItem.getQuantity() < minQuantity.get() &&
            (OrderType.AMENDMENT != orderType || lineItem.getQuantity() != 0L)
        ) {
            throw new InvalidInputException(
                String.format("line item quantity %s cannot be less than minimum quantity %s", lineItem.getQuantity(), minQuantity.get())
            );
        }

        if (maxQuantity.isPresent() && lineItem.getQuantity() > maxQuantity.get()) {
            throw new InvalidInputException(
                String.format("line item quantity %s cannot be greater than maximum quantity %s", lineItem.getQuantity(), maxQuantity.get())
            );
        }
    }

    static List<ApprovalFlowInstanceGroup> createOrUpdateApprovalFlowsOnOrder(
        ApprovalFlowInstanceService approvalFlowInstanceService,
        Order order,
        boolean adminApprovalFlowBypass
    ) {
        if (order.getStatus() == OrderStatus.SUBMITTED) {
            approvalFlowInstanceService.addApprovalFlowInstance(order.getOrderId(), adminApprovalFlowBypass);
        } else if (order.getStatus() == OrderStatus.DRAFT) {
            approvalFlowInstanceService.cancelInProgressWorkflowsByOrderId(order.getOrderId());
        }

        return approvalFlowInstanceService.getApprovalFlowInstances(order.getOrderId(), false);
    }

    public static void updateOrderLineItemAnnualizedAmounts(Order order, Map<String, BigDecimal> lineItemAnnualizedAmounts) {
        if (lineItemAnnualizedAmounts == null || lineItemAnnualizedAmounts.isEmpty()) {
            return;
        }

        order
            .getLineItems()
            .forEach(item -> {
                if (lineItemAnnualizedAmounts.containsKey(item.getOrderLineId())) {
                    item.setAnnualizedAmount(lineItemAnnualizedAmounts.get(item.getOrderLineId()));
                }
            });
    }

    public static void updateOrderTotalAndOrderLineItemAmountsForEvergreenOrder(
        Order order,
        Map<String, InvoiceItemAmounts> lineItemBillingCycleAmounts
    ) {
        if (MapUtils.isNotEmpty(lineItemBillingCycleAmounts)) {
            order
                .getLineItems()
                .forEach(item -> {
                    if (lineItemBillingCycleAmounts.containsKey(item.getOrderLineId())) {
                        InvoiceItemAmounts billingCycleAmounts = lineItemBillingCycleAmounts.get(item.getOrderLineId());
                        if (billingCycleAmounts != null) {
                            item.setListAmount(billingCycleAmounts.getListAmount());
                            item.setListAmountBeforeOverride(billingCycleAmounts.getListAmountBeforeOverride());
                            item.setListUnitPrice(billingCycleAmounts.getListUnitPrice());
                            item.setDiscountAmount(billingCycleAmounts.getDiscountAmount());
                            item.setAmount(billingCycleAmounts.getSellAmount());
                            item.setSellUnitPrice(billingCycleAmounts.getSellUnitPrice());
                        }
                    }
                });
        }

        order.setTotalAmount(BigDecimal.ZERO);
        order.setTotalListAmount(BigDecimal.ZERO);
        order.setTotalListAmountBeforeOverride(BigDecimal.ZERO);
    }

    public static void validateOrderTotalAmount(BigDecimal totalAmount) {
        if (totalAmount.compareTo(MAX_TOTAL_AMOUNT_FOR_ORDER) >= 0) {
            throw new IllegalArgumentException(String.format("order total amount cannot exceed %s, was %s", MAX_TOTAL_AMOUNT_FOR_ORDER, totalAmount));
        } else if (totalAmount.compareTo(MAX_TOTAL_AMOUNT_FOR_ORDER.negate()) <= 0) {
            throw new IllegalArgumentException(
                String.format("Order total cannot exceed %s, was %s", MAX_TOTAL_AMOUNT_FOR_ORDER.negate(), totalAmount)
            );
        }
    }

    static void validateApprovalSegmentIdInOrder(Order order, ApprovalFlowInstanceService approvalFlowInstanceService) {
        approvalFlowInstanceService.validateOrderOwnerApprovalSegment(order);
    }

    public static List<OrderLineItem> consolidateOrderLineItemsByBaseExternalSubscriptionChargeId(
        List<OrderLineItem> orderLineItemList,
        Map<String, Metrics> orderLineMetrics
    ) {
        // TODO: Refactor this code for a better readability.
        List<OrderLineItem> consolidatedOrderLineItems = new ArrayList<>();
        Map<String, List<OrderLineItem>> existingChargeIdMap = orderLineItemList
            .stream()
            .filter(ol -> StringUtils.isNotBlank(ol.getBaseExternalSubscriptionChargeId()))
            .collect(Collectors.groupingBy(OrderLineItem::getBaseExternalSubscriptionChargeId));
        existingChargeIdMap
            .keySet()
            .forEach(id -> mapSameChargeOrderLinesToSingleLine(consolidatedOrderLineItems, existingChargeIdMap.get(id), orderLineMetrics));

        Map<String, List<OrderLineItem>> sChargeIdMap = orderLineItemList
            .stream()
            .filter(ol -> StringUtils.isBlank(ol.getBaseExternalSubscriptionChargeId()))
            .collect(Collectors.groupingBy(OrderLineItem::getChargeId));
        sChargeIdMap.keySet().forEach(id -> mapSameChargeOrderLinesToSingleLine(consolidatedOrderLineItems, sChargeIdMap.get(id), orderLineMetrics));

        consolidatedOrderLineItems.sort(Comparator.comparing(OrderLineItem::getRank));
        return consolidatedOrderLineItems;
    }

    private static void mapSameChargeOrderLinesToSingleLine(
        List<OrderLineItem> consolidatedOrderLineItems,
        List<OrderLineItem> orderLineItemList,
        Map<String, Metrics> orderLineMetrics
    ) {
        boolean amendedLineFound = false;
        for (OrderLineItem lineItem : orderLineItemList) {
            if (lineItem.getQuantity() > 0 || lineItem.getAction() == ActionType.ADD) {
                amendedLineFound = true;
                var orderLineItem = new OrderLineItem(lineItem);
                updateLineLevelParametersForAmendedLine(orderLineItemList, orderLineItem, orderLineMetrics);
                consolidatedOrderLineItems.add(orderLineItem);
            }
        }

        if (!amendedLineFound && CollectionUtils.isNotEmpty(orderLineItemList)) {
            consolidatedOrderLineItems.add(orderLineItemList.get(0));
        }
    }

    private static void updateLineLevelParametersForAmendedLine(
        List<OrderLineItem> orderLineItemList,
        OrderLineItem lineItem,
        Map<String, Metrics> orderLineMetrics
    ) {
        if (lineItem.getAction() != ActionType.NONE && lineItem.getAction() != ActionType.UPDATE) {
            return;
        }
        BigDecimal tcv = BigDecimal.ZERO;
        BigDecimal exitArr = BigDecimal.ZERO;
        BigDecimal arr = BigDecimal.ZERO;
        BigDecimal entryArr = BigDecimal.ZERO;
        BigDecimal nonRecurringTotal = BigDecimal.ZERO;
        BigDecimal averageArr = BigDecimal.ZERO;
        BigDecimal deltaArr = BigDecimal.ZERO;
        BigDecimal deltaTcv = BigDecimal.ZERO;
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal listAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;
        for (OrderLineItem ol : orderLineItemList) {
            Metrics lineMetrics = orderLineMetrics.get(ol.getOrderLineId());
            tcv = tcv.add(lineMetrics.getTcv());
            exitArr = exitArr.add(lineMetrics.getExitArr());
            arr = arr.add(lineMetrics.getArr());
            entryArr = entryArr.add(lineMetrics.getEntryArr());
            nonRecurringTotal = nonRecurringTotal.add(lineMetrics.getNonRecurringTotal());
            averageArr = averageArr.add(lineMetrics.getAverageArr());
            deltaArr = deltaArr.add(lineMetrics.getDeltaArr());
            deltaTcv = deltaTcv.add(lineMetrics.getDeltaTcv());
            amount = amount.add(ol.getAmount());
            listAmount = listAmount.add(ol.getListAmount());
            discountAmount = discountAmount.add(ol.getDiscountAmount());
            orderLineMetrics.remove(ol.getOrderLineId());
        }
        lineItem.setAmount(amount);
        lineItem.setListAmount(listAmount);
        lineItem.setDiscountAmount(discountAmount);
        Metrics metrics = new Metrics(tcv, lineItem.getAmount(), nonRecurringTotal, arr, entryArr, exitArr, averageArr, deltaTcv, deltaArr, null);
        orderLineMetrics.put(lineItem.getOrderLineId(), metrics);
    }

    // checks order line item plans for any inclusion or exclusion rules
    static void checkProductRules(
        FeatureService featureService,
        CatalogRelationshipGetService catalogRelationshipGetService,
        Set<String> newPlanIds,
        Set<String> existingPlanIds
    ) {
        if (!featureService.isEnabled(Feature.PRODUCT_RULES_DEMO)) {
            // todo: this is a temporary implementation gated by feature flag. If FF is disable, do nothing and return
            return;
        }

        Set<String> requireAllPlanIds = new HashSet<>();
        Set<String> requireAnyPlanIds = new HashSet<>();
        Set<String> excludedPlanIds = new HashSet<>();

        newPlanIds.forEach(planId -> {
            List<CatalogRelationship> requireAllRelationships = catalogRelationshipGetService.getCatalogRelationships(
                planId,
                CatalogRelationshipType.REQUIRE_ALL_PLANS
            );

            List<CatalogRelationship> requireAnyRelationships = catalogRelationshipGetService.getCatalogRelationships(
                planId,
                CatalogRelationshipType.REQUIRE_ANY_PLANS
            );

            List<CatalogRelationship> exclusionRelationships = catalogRelationshipGetService.getCatalogRelationships(
                planId,
                CatalogRelationshipType.EXCLUDED_PLANS
            );

            requireAllRelationships.forEach(catalogRelationship -> requireAllPlanIds.add(catalogRelationship.getToPlanId()));
            requireAnyRelationships.forEach(catalogRelationship -> requireAnyPlanIds.add(catalogRelationship.getToPlanId()));
            exclusionRelationships.forEach(catalogRelationship -> excludedPlanIds.add(catalogRelationship.getToPlanId()));
        });

        Set<String> allPlanIds = Stream.concat(newPlanIds.stream(), existingPlanIds.stream()).collect(Collectors.toSet());

        if (!allPlanIds.containsAll(requireAllPlanIds)) {
            throw new IllegalArgumentException("Order missing 1 or more required plans.");
        }

        if (!requireAnyPlanIds.isEmpty() && Collections.disjoint(allPlanIds, requireAnyPlanIds)) {
            throw new IllegalArgumentException("Order missing 1 or more required plans.");
        }

        if (!Collections.disjoint(allPlanIds, excludedPlanIds)) {
            throw new IllegalArgumentException("Order contains 1 or more excluded plans.");
        }
    }

    static void validateCurrentAuthPrincipalCanExecuteOrders(OrderStatus orderStatus) {
        if (orderStatus != OrderStatus.EXECUTED) {
            return;
        }

        Optional<Role> optionalRequestRole = CurrentUserProvider.provideRole();
        if (
            optionalRequestRole.isEmpty() ||
            optionalRequestRole.get() == Role.ADMIN ||
            optionalRequestRole.get() == Role.FINANCE ||
            optionalRequestRole.get() == Role.SALES_MANAGER ||
            (optionalRequestRole.get() == Role.BILLY_JOB)
        ) {
            return;
        }
        throw new ForbiddenException("Unauthorized access to resource.");
    }

    static void validateOrderAttachment(Order order, AttachmentsService attachmentsService) {
        String attachmentId = order.getAttachmentId();
        if (StringUtils.isBlank(attachmentId)) {
            return;
        }

        Attachment attachment = attachmentsService.getAttachmentById(UUID.fromString(attachmentId));
        String extension = FilenameUtils.getExtension(attachment.getName());
        if (StringUtils.isNotBlank(extension) && !"pdf".equalsIgnoreCase(extension)) {
            throw new IllegalArgumentException("The Account Document must be a PDF.");
        }
    }

    static void checkCrmIdIsEmptyForCompositeOrders(Order order) {
        if (StringUtils.isNotBlank(order.getCompositeOrderId()) && StringUtils.isNotBlank(order.getSfdcOpportunityId())) {
            throw new IllegalArgumentException("OpportunityId shouldn't be present on individual orders when they are part of Composite orders");
        }
    }

    static void updateOrderStatusByExpiryDate(Order order, TimeZone timeZone) {
        if (order.getExpiresOn() == null || order.getStatus() != OrderStatus.EXPIRED) {
            return;
        }

        Instant startOfCurrentDay = DateTimeConverter.getStartOfCurrentDay(Instant.now(), timeZone);
        Instant startOfExpiryDate = DateTimeConverter.getStartOfCurrentDay(order.getExpiresOn(), timeZone);

        if (startOfExpiryDate.compareTo(startOfCurrentDay) >= 0) {
            order.setStatus(OrderStatus.DRAFT);
        }
    }

    static void validateOrderExpiryDate(Order order, OrderStatus newStatus, TimeZone tenantTimeZone) {
        if (order.getExpiresOn() == null || order.getStatus() == OrderStatus.EXPIRED) {
            return;
        }

        // we should be checking for expiry only when the status of the order is changing to submitted or expired
        if (newStatus != OrderStatus.SUBMITTED && newStatus != OrderStatus.EXPIRED) {
            return;
        }

        Instant startOfCurrentDay = DateTimeConverter.getStartOfCurrentDay(Instant.now(), tenantTimeZone);
        Instant startOfExpiryDate = DateTimeConverter.getStartOfCurrentDay(order.getExpiresOn(), tenantTimeZone);

        if (startOfExpiryDate.compareTo(startOfCurrentDay) < 0) {
            order.setStatus(OrderStatus.EXPIRED);
            LOGGER.info("Order has expired: {}, old status: {}, new status: {}", order.getOrderId(), order.getStatus(), newStatus);
        }
    }

    static void validateOrderLineItemPriceAttribution(OrderLineItem lineItem, Charge charge, RateCardService rateCardService) {
        if (
            (!ALLOWED_CHARGE_TYPES_FOR_PRICE_ATTRIBUTION.contains(charge.getType()) || charge.getChargeModel() != ChargeModel.RATE_CARD_LOOKUP) &&
            Objects.isNull(lineItem.getAttributeReferences())
        ) {
            return;
        }

        if (
            (!ALLOWED_CHARGE_TYPES_FOR_PRICE_ATTRIBUTION.contains(charge.getType()) || charge.getChargeModel() != ChargeModel.RATE_CARD_LOOKUP) &&
            Objects.nonNull(lineItem.getAttributeReferences())
        ) {
            throw new InvalidInputException(
                "Price attribution at order line is allowed with charge types- (recurring, one time) and charge model rate card lookup"
            );
        }

        if (Objects.nonNull(lineItem.getAttributeReferences())) {
            validateOrderLineItemPriceAttributionWithRateCard(lineItem.getAttributeReferences(), charge, rateCardService);
            return;
        }

        defaultOrderLineItemWithAttributeReferencesFromCharge(lineItem, charge, rateCardService);
    }

    private static void defaultOrderLineItemWithAttributeReferencesFromCharge(
        OrderLineItem lineItem,
        Charge charge,
        RateCardService rateCardService
    ) {
        Optional<ChargeDefaultAttributeReferences> defaultAttributeReferencesOptional = rateCardService.getChargeDefaultAttributeReferencesByChargeId(
            charge.getChargeId()
        );

        defaultAttributeReferencesOptional
            .map(ChargeDefaultAttributeReferences::getAttributeReferences)
            .map(attributeReferences -> {
                lineItem.setAttributeReferences(attributeReferences);
                return true;
            })
            .orElseThrow(() ->
                new InvariantCheckFailedException(
                    String.format(
                        "charge :%s is of type %s and model RATE_CARD_LOOKUP but does not have a default attribute references associated with it",
                        charge.getChargeId(),
                        charge.getType()
                    )
                )
            );
    }

    private static void validateOrderLineItemPriceAttributionWithRateCard(
        List<AttributeReference> attributeReferences,
        Charge charge,
        RateCardService rateCardService
    ) {
        String rateCardId = charge.getRateCardId();
        if (StringUtils.isBlank(rateCardId)) {
            throw new ServiceFailureException(
                String.format("charge :%s is of type RATE_CARD_LOOKUP but does not have a rate card associated with it", charge.getChargeId())
            );
        }
        Optional<RateCard> rateCard = rateCardService.getRateCard(rateCardId);
        if (rateCard.isEmpty()) {
            throw new ServiceFailureException(String.format("charge :%s has empty rate card associated with it", charge.getChargeId()));
        }
        AttributeReferences inputAttributeReferences = AttributeReferences.wrap(attributeReferences);
        boolean matchFound = rateCard
            .get()
            .getPriceTable()
            .stream()
            .map(PriceTableElement::getAttributeReferences)
            .map(AttributeReferences::wrap)
            .distinct()
            .anyMatch(references -> references.equals(inputAttributeReferences));
        if (!matchFound) {
            throw new InvalidInputException(
                String.format(
                    "OrderLineItem attribute references :%s does not match with any of the price table attribute references in rate card :%s",
                    inputAttributeReferences,
                    rateCardId
                )
            );
        }
    }

    static void validateMinimumCommitLineItems(
        List<OrderLineItem> orderLineItems,
        Map<String, Charge> chargeMap,
        List<Instant> billingPeriodStartDates
    ) {
        Set<String> minimumCommitChargeIds = orderLineItems
            .stream()
            .map(OrderLineItem::getChargeId)
            .map(chargeMap::get)
            .map(Charge::getMinimumCommitBaseChargeId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Optional<OrderLineItem> minimumCommitNotAligned = orderLineItems
            .stream()
            .filter(li -> minimumCommitChargeIds.contains(li.getChargeId()))
            .filter(li -> !billingPeriodStartDates.contains(li.getEffectiveDate()))
            .findAny();
        if (minimumCommitNotAligned.isPresent()) {
            var lineItem = minimumCommitNotAligned.get();
            String message = String.format(
                "minimum commit with charge id %s effective date %s does not align with any billing period start date %s",
                lineItem.getChargeId(),
                lineItem.getEffectiveDate(),
                billingPeriodStartDates
            );
            throw new InvalidInputException(message);
        }

        Optional<OrderLineItem> usageLineItemNotAligned = orderLineItems
            .stream()
            .filter(li -> StringUtils.isNotBlank(chargeMap.get(li.getChargeId()).getMinimumCommitBaseChargeId()))
            .filter(li -> !billingPeriodStartDates.contains(li.getEffectiveDate()))
            .findAny();
        if (usageLineItemNotAligned.isPresent()) {
            var lineItem = usageLineItemNotAligned.get();
            String message = String.format(
                "minimum commit usage line with charge id %s effective date %s does not align with any billing period start dates %s",
                lineItem.getChargeId(),
                lineItem.getEffectiveDate(),
                billingPeriodStartDates
            );
            throw new InvalidInputException(message);
        }
    }

    static void validatePrepaidAmendments(Order order, Subscription subscription, Map<String, Charge> chargeMap, Map<UUID, Plan> plansByUuid) {
        if (order.getOrderType() != OrderType.AMENDMENT) {
            return;
        }

        subscription
            .getCharges()
            .stream()
            .map(SubscriptionCharge::getChargeId)
            .map(chargeMap::get)
            .filter(Charge::isDrawdown)
            .map(Charge::getPlanUuid)
            .distinct()
            .map(plansByUuid::get)
            .forEach(plan -> validatePrepaidDrawdownPlanInAmendment(order, subscription, plan));
    }

    private static void validatePrepaidDrawdownPlanInAmendment(Order order, Subscription subscription, Plan plan) {
        Set<String> chargeIds = plan.getCharges().stream().map(Charge::getChargeId).collect(Collectors.toUnmodifiableSet());
        List<OrderLineItem> lineItems = order.getLineItems().stream().filter(li -> chargeIds.contains(li.getChargeId())).toList();

        // validate removal removes all charges in plan
        List<OrderLineItem> removedLines = lineItems.stream().filter(li -> li.getAction() == ActionType.REMOVE).toList();
        var subscriptionLines = subscription
            .getCharges()
            .stream()
            .filter(sc -> order.getStartDate().isBefore(sc.getEndDate()))
            .map(SubscriptionCharge::getChargeId)
            .filter(chargeIds::contains)
            .toList();
        if (CollectionUtils.isNotEmpty(removedLines) && removedLines.size() != subscriptionLines.size()) {
            throw new InvalidInputException(
                String.format(
                    "all lines from the prepaid drawdown %s '%s' must be removed at once or not at all. Expected %s, but only %s were removed",
                    plan.getPlanId(),
                    Optional.ofNullable(plan.getDisplayName()).orElse(plan.getName()),
                    subscriptionLines.size(),
                    removedLines.size()
                )
            );
        }

        // validate top up
        List<OrderLineItem> invalidAmendmentLines = lineItems
            .stream()
            .filter(li -> li.getAction() != ActionType.REMOVE)
            .filter(lineItem -> !AMENDMENT_ACTION_TYPES_FOR_PREPAID_DRAWDOWN.contains(lineItem.getAction()))
            .toList();
        if (CollectionUtils.isNotEmpty(invalidAmendmentLines)) {
            throw new InvalidInputException(String.format("cannot amend prepaid charges because of lines: %s", invalidAmendmentLines));
        }
    }

    static void validateChangeOrderWithUsage(
        Order changeOrder,
        Subscription subscription,
        InvoiceService invoiceService,
        Map<String, Charge> chargeMap,
        ZoneId accountZoneId
    ) {
        EntityCache<String, Optional<InvoiceItem>> latestUsageInvoiceCache = EntityCache.of(
            invoiceService::getLatestInvoiceItemBySubscriptionChargeGroupId
        );
        List<SubscriptionCharge> usageSubscriptionCharges = subscription
            .getCharges()
            .stream()
            .filter(charge -> chargeMap.get(charge.getChargeId()).getType() == ChargeType.USAGE)
            .filter(sc -> latestUsageInvoiceCache.get(sc.getSubscriptionChargeGroupId()).isPresent())
            .toList();

        if (CollectionUtils.isEmpty(usageSubscriptionCharges)) {
            return;
        }

        if (changeOrder.getOrderType() == OrderType.CANCEL) {
            validateCancelBeforeLatestUsageInvoice(subscription, changeOrder.getStartDate(), invoiceService, chargeMap);
            return;
        }

        if (!ORDER_TYPES_WITH_USAGE_CHARGE_ALLOWED_FOR_UPDATE.contains(changeOrder.getOrderType())) {
            throw new InvariantCheckFailedException(
                String.format("expected order of type %s but was %s", ORDER_TYPES_WITH_USAGE_CHARGE_ALLOWED_FOR_UPDATE, changeOrder.getOrderType())
            );
        }

        for (SubscriptionCharge subscriptionCharge : usageSubscriptionCharges) {
            validateSubscriptionUsageChargeForAmendment(changeOrder, chargeMap, subscriptionCharge, latestUsageInvoiceCache, accountZoneId);
        }
    }

    private static void validateSubscriptionUsageChargeForAmendment(
        Order changeOrder,
        Map<String, Charge> chargeMap,
        SubscriptionCharge subscriptionUsageCharge,
        EntityCache<String, Optional<InvoiceItem>> latestUsageInvoiceCache,
        ZoneId accountZoneId
    ) {
        Charge usageCharge = chargeMap.get(subscriptionUsageCharge.getChargeId());
        Optional<Instant> latestInvoiceUsageDate = latestUsageInvoiceCache
            .get(subscriptionUsageCharge.getSubscriptionChargeGroupId())
            .map(InvoiceItem::getPeriodEndDate);
        if (latestInvoiceUsageDate.isEmpty()) {
            return;
        }

        validateRemoveUsageLine(changeOrder, subscriptionUsageCharge, latestInvoiceUsageDate.get(), accountZoneId);

        // todo: add check for the usage charge itself when we allow updating usage charges in amendments
        List<OrderLineItem> minCommitLineItems = getMinimumCommitLineItems(changeOrder, usageCharge);
        List<OrderLineItem> overageLineItems = getOverageLineItems(changeOrder, usageCharge);
        List<OrderLineItem> prepaidDrawdownLineItems = getPrepaidDrawdownTopUpLines(changeOrder, usageCharge, chargeMap);
        Stream.of(minCommitLineItems, overageLineItems, prepaidDrawdownLineItems)
            .flatMap(Collection::stream)
            .forEach(li -> validateAmendmentLineItemDateAfterLastUsageInvoice(usageCharge, latestInvoiceUsageDate.get(), li));
    }

    private static void validateRemoveUsageLine(
        Order changeOrder,
        SubscriptionCharge usageCharge,
        Instant latestUsageInvoice,
        ZoneId accountTimeZoneId
    ) {
        if (!changeOrder.getStartDate().isBefore(latestUsageInvoice)) {
            return;
        }

        changeOrder
            .getLineItems()
            .stream()
            .filter(li -> usageCharge.getSubscriptionChargeId().equals(li.getBaseExternalSubscriptionChargeId()))
            .filter(li -> li.getAction() == ActionType.REMOVE)
            .findAny()
            .ifPresent(li -> {
                ZonedDateTime lastInvoiceDateZoned = ZonedDateTime.ofInstant(latestUsageInvoice, accountTimeZoneId);
                throw new InvalidInputException(
                    String.format(
                        "cannot remove usage charge %s because it has already been invoiced at %s",
                        usageCharge.getChargeId(),
                        lastInvoiceDateZoned
                    )
                );
            });
    }

    public static Optional<Instant> getLatestUsageInvoiceForSubscription(
        Subscription subscription,
        InvoiceService invoiceService,
        Map<String, Charge> chargeMap
    ) {
        EntityCache<String, Optional<InvoiceItem>> latestUsageInvoiceCache = EntityCache.of(
            invoiceService::getLatestInvoiceItemBySubscriptionChargeGroupId
        );
        List<SubscriptionCharge> usageSubscriptionCharges = subscription
            .getCharges()
            .stream()
            .filter(charge -> chargeMap.get(charge.getChargeId()).getType() == ChargeType.USAGE)
            .filter(sc -> latestUsageInvoiceCache.get(sc.getSubscriptionChargeGroupId()).isPresent())
            .toList();

        if (CollectionUtils.isEmpty(usageSubscriptionCharges)) {
            return Optional.empty();
        }

        return usageSubscriptionCharges
            .stream()
            .map(sc -> latestUsageInvoiceCache.get(sc.getSubscriptionChargeGroupId()))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .map(InvoiceItem::getPeriodEndDate)
            .max(Instant::compareTo);
    }

    private static void validateCancelBeforeLatestUsageInvoice(
        Subscription subscription,
        Instant cancelOrderEffectiveDate,
        InvoiceService invoiceService,
        Map<String, Charge> chargeMap
    ) {
        Optional<Instant> latestUsageInvoicedDate = getLatestUsageInvoiceForSubscription(subscription, invoiceService, chargeMap);
        if (latestUsageInvoicedDate.isEmpty()) {
            return;
        }

        if (cancelOrderEffectiveDate.isBefore(latestUsageInvoicedDate.get())) {
            throw new InvalidInputException(
                String.format(
                    "cancel order cannot start at %s because usage charge was invoiced at %s",
                    cancelOrderEffectiveDate,
                    latestUsageInvoicedDate.get()
                )
            );
        }
    }

    private static List<OrderLineItem> getMinimumCommitLineItems(Order changeOrder, Charge usageCharge) {
        if (StringUtils.isBlank(usageCharge.getMinimumCommitBaseChargeId())) {
            return List.of();
        }
        return changeOrder
            .getLineItems()
            .stream()
            .filter(li -> AMENDMENT_ACTION_TYPES_FOR_USAGE_RELATED_CHARGES.contains(li.getAction()))
            .filter(li -> usageCharge.getMinimumCommitBaseChargeId().equals(li.getChargeId()))
            .toList();
    }

    private static List<OrderLineItem> getOverageLineItems(Order changeOrder, Charge usageCharge) {
        if (StringUtils.isBlank(usageCharge.getOverageBaseChargeId())) {
            return List.of();
        }
        return changeOrder
            .getLineItems()
            .stream()
            .filter(li -> AMENDMENT_ACTION_TYPES_FOR_USAGE_RELATED_CHARGES.contains(li.getAction()))
            .filter(li -> usageCharge.getOverageBaseChargeId().equals(li.getChargeId()))
            .toList();
    }

    private static List<OrderLineItem> getPrepaidDrawdownTopUpLines(Order changeOrder, Charge usageCharge, Map<String, Charge> chargeMap) {
        if (!usageCharge.isDrawdown()) {
            return List.of();
        }
        return changeOrder
            .getLineItems()
            .stream()
            .filter(li -> chargeMap.get(li.getChargeId()).getPlanUuid().equals(usageCharge.getPlanUuid()))
            .filter(li -> chargeMap.get(li.getChargeId()).getType() == ChargeType.PREPAID)
            .filter(li -> li.getAction() == ActionType.ADD)
            .toList();
    }

    private static void validateAmendmentLineItemDateAfterLastUsageInvoice(Charge usageCharge, Instant latestUsageInvoiceDate, OrderLineItem li) {
        if (latestUsageInvoiceDate.isAfter(li.getEffectiveDate())) {
            throw new InvalidInputException(
                String.format(
                    "cannot amend line with charge id %s at %s because usage charge %s has already been invoiced at %s",
                    li.getChargeId(),
                    li.getEffectiveDate(),
                    usageCharge.getChargeId(),
                    latestUsageInvoiceDate
                )
            );
        }
    }

    static void addOrderSubmittedBy(Order order) {
        Optional<BillyAuthPrincipal> authPrincipal = CurrentUserProvider.getAuthPrincipal();
        if (
            authPrincipal.isEmpty() ||
            authPrincipal.get().getAuthUser().isEmpty() ||
            authPrincipal.get().getAuthUser().get().getEmailAddress().isEmpty()
        ) {
            return;
        }

        order.setSubmittedBy(authPrincipal.get().getAuthUser().get().getEmailAddress().get());
    }

    public static Map<String, Charge> createChargeMap(Order order, Subscription subscription, ProductCatalogGetService productCatalogGetService) {
        List<String> orderChargeIds = order == null ? List.of() : order.getLineItems().stream().map(OrderLineItem::getChargeId).toList();
        List<String> subscriptionChargeIds = subscription == null
            ? List.of()
            : subscription.getCharges().stream().map(SubscriptionCharge::getChargeId).toList();
        List<String> allChargeIds = Stream.of(orderChargeIds, subscriptionChargeIds).flatMap(List::stream).distinct().toList();
        return productCatalogGetService.getChargeMapByChargeIds(allChargeIds);
    }

    private static Map<String, Charge> getChargeMapFromLineItems(List<OrderLineItem> lineItems, ProductCatalogGetService productCatalogGetService) {
        var uniqueChargeIds = lineItems.stream().map(OrderLineItem::getChargeId).distinct().collect(Collectors.toList());
        return productCatalogGetService.getChargeMapByChargeIds(uniqueChargeIds);
    }

    static void calculateAndApplyTaxEstimates(
        Order order,
        TaxService taxService,
        TaxRateGetService taxRateGetService,
        AccountGetService accountGetService,
        ProductCatalogGetService productCatalogGetService
    ) {
        EntityCache<String, Account> accountEntityCache = EntityCache.of(accountGetService::getAccount);
        EntityCache<String, AccountContact> contactEntityCache = EntityCache.of(accountGetService::getContact);
        Account account = accountEntityCache.get(order.getAccountId());
        Optional<AccountAddress> shippingAddress = Optional.ofNullable(order.getShippingContactId())
            .map(contactEntityCache::get)
            .map(AccountContact::getAddress);
        Optional<TaxExemptionUseCode> taxExemptionUseCode = Optional.ofNullable(order.getBillingContactId())
            .map(contactEntityCache::get)
            .map(AccountContact::getAccountId)
            .map(accountEntityCache::get)
            .map(Account::getTaxExemptionUseCode);

        List<TaxLineItem> taxLines = generateTaxLineItems(order.getLineItems(), taxService, taxRateGetService, productCatalogGetService);
        if (CollectionUtils.isEmpty(taxLines)) {
            return;
        }

        TaxPreviewInput taxPreviewInput = ImmutableTaxPreviewInput.builder()
            .taxTransactionCode(Optional.empty())
            .account(account)
            .accountAddress(shippingAddress)
            .taxLineItems(taxLines)
            .taxExceptionUseCode(taxExemptionUseCode)
            .currency(order.getCurrency())
            .build();
        Optional<TaxTransaction> taxTransaction = taxService.previewTaxTransaction(taxPreviewInput);
        if (taxTransaction.isEmpty()) {
            return;
        }

        applyTaxEstimates(order, taxTransaction.get());
    }

    private static void applyTaxEstimates(Order order, TaxTransaction taxTransaction) {
        Map<String, OrderLineItem> orderLineItemIdToNetEffectLine = order
            .getLineItemsNetEffect()
            .stream()
            .collect(Collectors.toMap(OrderLineItem::getOrderLineId, Function.identity()));
        for (OrderLineItem lineItem : order.getLineItems()) {
            BigDecimal taxEstimate = taxTransaction.getComputedTaxByLineNo().getOrDefault(lineItem.getOrderLineId(), null);
            lineItem.setTaxEstimate(taxEstimate);
            if (orderLineItemIdToNetEffectLine.containsKey(lineItem.getOrderLineId())) {
                orderLineItemIdToNetEffectLine.get(lineItem.getOrderLineId()).setTaxEstimate(taxEstimate);
            }
        }
        order.setTaxEstimate(taxTransaction.getTotalTax());
    }

    private static List<TaxLineItem> generateTaxLineItems(
        List<OrderLineItem> lineItems,
        TaxService taxService,
        TaxRateGetService taxRateGetService,
        ProductCatalogGetService productCatalogGetService
    ) {
        if (CollectionUtils.isEmpty(lineItems)) {
            return List.of();
        }
        EntityCache<UUID, TaxRate> taxRateCache = EntityCache.of(taxRateGetService::getTaxRate);
        Map<String, Charge> chargeMap = getChargeMapFromLineItems(lineItems, productCatalogGetService);
        Map<String, Product> chargeIdToProductMap = productMapByChargeId(chargeMap.keySet(), productCatalogGetService);

        if (taxService.tenantHasTaxServiceIntegration()) {
            return getTaxLineItemsWithIntegration(lineItems, chargeMap, taxRateCache, chargeIdToProductMap);
        }
        return getTaxLineItemsWithoutIntegration(lineItems, chargeMap, taxRateCache, chargeIdToProductMap);
    }

    private static List<TaxLineItem> getTaxLineItemsWithIntegration(
        List<OrderLineItem> lineItems,
        Map<String, Charge> chargeMap,
        EntityCache<UUID, TaxRate> taxRateCache,
        Map<String, Product> chargeIdToProductMap
    ) {
        Set<String> chargesMissingTaxRate = lineItems
            .stream()
            .map(lineItem -> chargeMap.get(lineItem.getChargeId()))
            .filter(charge -> charge.getTaxRateId() == null)
            .map(Charge::getChargeId)
            .collect(Collectors.toUnmodifiableSet());
        if (!chargesMissingTaxRate.isEmpty()) {
            throw new ConflictingStateException(
                String.format(
                    "Tax rate is required for tax calculation, the following charges are missing tax rates: %s",
                    String.join(", ", chargesMissingTaxRate)
                )
            );
        }

        return lineItems
            .stream()
            .filter(lineItem -> chargeMap.get(lineItem.getChargeId()).getTaxRateId() != null)
            .map(lineItem -> generateTaxLineItem(lineItem, chargeMap, taxRateCache, chargeIdToProductMap))
            .toList();
    }

    private static List<TaxLineItem> getTaxLineItemsWithoutIntegration(
        List<OrderLineItem> lineItems,
        Map<String, Charge> chargeMap,
        EntityCache<UUID, TaxRate> taxRateCache,
        Map<String, Product> chargeIdToProductMap
    ) {
        List<TaxRate> taxRates = lineItems
            .stream()
            .map(OrderLineItem::getChargeId)
            .map(chargeMap::get)
            .map(Charge::getTaxRateId)
            .filter(Objects::nonNull)
            .distinct()
            .map(taxRateCache::get)
            .toList();
        boolean hasTaxRateWithPercent = taxRates.stream().anyMatch(taxRate -> taxRate.getTaxPercentage() != null);
        boolean hasTaxRateWithoutPercent = taxRates.stream().anyMatch(taxRate -> taxRate.getTaxPercentage() == null);
        if (hasTaxRateWithoutPercent && hasTaxRateWithPercent) {
            throw new InvalidInputException("order cannot mix tax rates with percentage and tax rates without percentage");
        }
        if (hasTaxRateWithoutPercent) {
            return List.of();
        }
        return lineItems
            .stream()
            .filter(lineItem -> chargeMap.get(lineItem.getChargeId()).getTaxRateId() != null)
            .map(lineItem -> generateTaxLineItem(lineItem, chargeMap, taxRateCache, chargeIdToProductMap))
            .toList();
    }

    private static TaxLineItem generateTaxLineItem(
        OrderLineItem lineItem,
        Map<String, Charge> chargeMap,
        EntityCache<UUID, TaxRate> taxRateMap,
        Map<String, Product> chargeIdToProductMap
    ) {
        var charge = chargeMap.get(lineItem.getChargeId());
        var taxRateId = charge.getTaxRateId();
        TaxRate taxRate = taxRateMap.get(taxRateId);
        Product product = chargeIdToProductMap.get(lineItem.getChargeId());
        return TaxLineItem.builder()
            .lineNo(lineItem.getOrderLineId())
            .taxCode(taxRate.getTaxCode())
            .taxPercentage(Optional.ofNullable(taxRate.getTaxPercentage()))
            .amount(lineItem.getAmount())
            .quantity(new BigDecimal(lineItem.getQuantity()))
            .itemCode(product.getSku())
            .itemDescription(product.getName())
            .build();
    }

    private static Map<String, Product> productMapByChargeId(Set<String> chargeIds, ProductCatalogGetService productCatalogGetService) {
        List<Plan> plans = productCatalogGetService.getPlansFromChargeIds(new ArrayList<>(chargeIds));
        Map<String, Product> chargeIdToProductMap = new HashMap<>(chargeIds.size());
        for (var plan : plans) {
            for (var charge : plan.getCharges()) {
                chargeIdToProductMap.put(charge.getChargeId(), productCatalogGetService.getProduct(plan.getProductId()));
            }
        }
        return chargeIdToProductMap;
    }

    static void validateCustomFields(Order order) {
        validateCustomFields(order.getCustomFields());
        order.getLineItems().forEach(lineItem -> validateCustomFields(lineItem.getCustomFieldsAsCustomFieldObject()));
    }

    private static void validateCustomFields(CustomField customField) {
        if (customField == null) {
            return;
        }
        List<String> missingRequiredValues = customField
            .getEntries()
            .values()
            .stream()
            .filter(CustomFieldValue::isRequired)
            .filter(customFieldValue -> StringUtils.isBlank(customFieldValue.getValue()))
            .map(customFieldValue -> String.format("%s (%s)", customFieldValue.getLabel(), customFieldValue.getName()))
            .toList();
        if (CollectionUtils.isNotEmpty(missingRequiredValues)) {
            throw new InvalidInputException("Missing required custom field values: " + missingRequiredValues);
        }
    }

    static void addCustomFieldsBackToOrderLineItems(
        Order result,
        Map<Integer, List<CustomFieldEntry>> customFieldsByOrderLineItemRank,
        CustomizationService customizationService
    ) {
        result
            .getLineItems()
            .forEach(orderLineItem -> addCustomFieldBackToOrderLineItem(customFieldsByOrderLineItemRank, orderLineItem, customizationService));
    }

    private static void addCustomFieldBackToOrderLineItem(
        Map<Integer, List<CustomFieldEntry>> customFieldsByOrderLineItemRank,
        OrderLineItem orderLineItem,
        CustomizationService customizationService
    ) {
        if (CollectionUtils.isEmpty(orderLineItem.getCustomFields())) {
            List<CustomFieldEntry> customFieldEntries = customFieldsByOrderLineItemRank.getOrDefault(orderLineItem.getRank(), List.of());
            List<CustomFieldEntry> filteredCustomFieldEntries = customizationService.runOrderLineItemCustomFieldCustomization(
                orderLineItem.getPlanId(),
                orderLineItem.getChargeId(),
                customFieldEntries
            );
            orderLineItem.setCustomFields(filteredCustomFieldEntries);
        }
    }

    static Map<Integer, List<CustomFieldEntry>> getCustomFieldsByOrderLineItemRankMap(Order order) {
        if (order == null || order.getLineItems() == null) {
            return Map.of();
        }

        return order
            .getLineItems()
            .stream()
            .collect(
                Collectors.toMap(OrderLineItem::getRank, OrderLineItem::getCustomFields, (existing, replacement) -> {
                    // Line items are assigned unique ranks to establish their sequence in requests, with custom fields
                    // hydrated for each item (see `OrderServiceHelper.addOrderRankToLineItems`). However, change orders
                    // can introduce UPDATE or CANCEL line items that share ranks with existing items. When this happens,
                    // custom fields are neither rehydrated nor copied to the new line items, leaving them without
                    // custom fields. Since only one line item per rank will have custom fields (always the original
                    // requested item), we return whichever line item contains custom fields. Two line items with
                    // the same rank should never both have custom fields.
                    boolean existingHasFields = CollectionUtils.isNotEmpty(existing);
                    boolean replacementHasFields = CollectionUtils.isNotEmpty(replacement);

                    if (existingHasFields && replacementHasFields) {
                        throw new ConflictingStateException(
                            "Two line items with the same rank cannot both have custom fields: " + existing + ", " + replacement
                        );
                    }

                    return existingHasFields ? existing : replacement;
                })
            );
    }

    public static void checkForCanceledSubscription(Subscription subscription) {
        if (subscription.getCanceledDate() != null) {
            var message =
                "amendment/restructure are not allowed as the current subscription is set for cancellation. subscription id: " +
                subscription.getSubscriptionId();
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }
    }

    static Optional<Instant> getOrderExpiresOn(TenantSettingService tenantSettingService, TimeZone tenantTimeZone) {
        Integer expirationInDays = tenantSettingService.getOrderExpiryDurationInDays();
        if (expirationInDays == null) {
            return Optional.empty();
        }

        Instant orderExpiresOn = DateTimeCalculator.getStartOfDay(
            DateTimeCalculator.plusDays(tenantTimeZone.toZoneId(), Instant.now(), expirationInDays),
            tenantTimeZone.toZoneId()
        );
        return Optional.of(orderExpiresOn);
    }

    public static Map<String, Charge> getChargeMap(ProductCatalogGetService productCatalogGetService, List<OrderLineItem> orderLineItems) {
        List<String> chargeIds = orderLineItems.stream().map(OrderLineItem::getChargeId).distinct().collect(Collectors.toList());
        return productCatalogGetService.getChargeMapByChargeIds(chargeIds);
    }

    public static List<OrderLineItem> getRecurringOrderLineItems(List<OrderLineItem> currentOrderLineItems, Map<String, Charge> chargeMap) {
        if (CollectionUtils.isEmpty(currentOrderLineItems)) {
            return List.of();
        }

        return currentOrderLineItems
            .stream()
            .filter(orderLineItem -> chargeMap.get(orderLineItem.getChargeId()).isRecurring())
            .collect(Collectors.toList());
    }

    public static List<OrderLineItem> getNonRecurringOrderLineItems(List<OrderLineItem> currentOrderLineItems, Map<String, Charge> chargeMap) {
        if (CollectionUtils.isEmpty(currentOrderLineItems)) {
            return List.of();
        }

        return currentOrderLineItems
            .stream()
            .filter(orderLineItem -> !chargeMap.get(orderLineItem.getChargeId()).isRecurring())
            .collect(Collectors.toList());
    }

    public static void hydrateAndValidateCustomFields(Order order, boolean isDryRun, CustomFieldService customFieldService) {
        Validator.checkNonNullInternal(order.getCustomFields(), "order custom fields");
        order.getLineItems().forEach(lineItem -> Validator.checkNonNullInternal(lineItem.getCustomFields(), "order line item custom fields"));

        List<CustomFieldDefinition> orderDefinitions = customFieldService.getCustomFieldDefinitions(CustomFieldParentType.ORDER);
        CustomField hydratedOrderCustomFields = CustomFieldService.hydrateCustomFields(orderDefinitions, order.getCustomFields(), false);
        order.setCustomFields(hydratedOrderCustomFields);

        List<CustomFieldDefinition> orderItemDefinitions = customFieldService.getCustomFieldDefinitions(CustomFieldParentType.ORDER_ITEM);
        order
            .getLineItems()
            .forEach(lineItem -> {
                CustomField hydratedOrderItemCustomFields = CustomFieldService.hydrateCustomFields(
                    orderItemDefinitions,
                    lineItem.getCustomFieldsAsCustomFieldObject(),
                    false
                );
                lineItem.setCustomFieldEntriesFromCustomFieldObject(hydratedOrderItemCustomFields);
            });

        if (!isDryRun) {
            validateCustomFields(order);
        }
    }

    public static List<CustomFieldEntry> populateCustomFieldDefaults(
        List<CustomFieldEntry> customFieldEntries,
        CustomFieldParentType parentType,
        CustomFieldService customFieldService
    ) {
        if (customFieldEntries == null) {
            return null;
        }
        var customFieldDefinitions = customFieldService.getCustomFieldDefinitions(parentType);
        return CustomFieldService.populateCustomFieldDefaults(customFieldDefinitions, customFieldEntries);
    }

    public static void persistCustomFields(Order order, boolean isDryRun, CustomFieldService customFieldService) {
        if (isDryRun) {
            return;
        }
        persistOrderCustomFields(order, customFieldService);
        order.getLineItems().forEach(lineItem -> persistOrderItemCustomFields(lineItem, customFieldService));
    }

    private static void persistOrderCustomFields(Order order, CustomFieldService customFieldService) {
        if (order.getCustomFields() == null || order.getCustomFields().isEmpty()) {
            return;
        }
        CustomField updatedCustomFields = customFieldService.setCustomFields(
            CustomFieldParentType.ORDER,
            order.getOrderId(),
            order.getCustomFields()
        );
        order.setCustomFields(updatedCustomFields);
    }

    private static void persistOrderItemCustomFields(OrderLineItem lineItem, CustomFieldService customFieldService) {
        if (lineItem.getCustomFields() == null || lineItem.getCustomFields().isEmpty()) {
            return;
        }
        CustomField updatedCustomFields = customFieldService.setCustomFields(
            CustomFieldParentType.ORDER_ITEM,
            lineItem.getOrderLineId(),
            lineItem.getCustomFieldsAsCustomFieldObject()
        );
        lineItem.setCustomFieldEntriesFromCustomFieldObject(updatedCustomFields);
    }

    public static void validateArrOverride(OrderLineItem lineItem, Charge charge, FeatureService featureService) {
        if (lineItem.getArrOverride() == null) {
            return;
        }
        if (!featureService.isEnabled(Feature.ORDER_LINE_ARR_OVERRIDE)) {
            throw new InvalidInputException("arr override is not enabled");
        }
        if (BooleanUtils.isFalse(charge.getShouldTrackArr())) {
            throw new InvalidInputException("arr override is not allowed for charges without arr tracking");
        }
        if (charge.getType() == ChargeType.USAGE) {
            throw new InvalidInputException("arr override is not allowed for usage charges");
        }
        Numbers.validateInputAmountScale(lineItem.getArrOverride());
    }

    public static String formatInstant(Instant instant, TimeZone timeZone) {
        DocumentRenderFormatter formatter = new DocumentRenderFormatter(timeZone);
        return formatter.dateFormat(instant);
    }

    public static void validateOrderCurrency(Order orderRequest, Currency subscriptionCurrency) {
        if (
            !(orderRequest.getOrderType() == OrderType.CANCEL ||
                orderRequest.getOrderType() == OrderType.AMENDMENT ||
                orderRequest.getOrderType() == OrderType.RENEWAL)
        ) {
            return;
        }
        // if currency is not set on the change order, it should be the same as the subscription currency
        if (Objects.isNull(orderRequest.getCurrency())) {
            orderRequest.setCurrency(subscriptionCurrency);
            return;
        }
        // if currency is set on the change order, it must be the same as the subscription currency
        if (!orderRequest.getCurrency().getCurrencyCode().equalsIgnoreCase(subscriptionCurrency.getCurrencyCode())) {
            throw new InvalidInputException("Currency can't be changed during cancel, amendment, or renewal of a subscription");
        }
    }

    public static BigDecimal getDeltaArrPercent(Order order, Metrics orderMetrics, MetricsService metricsService) {
        Metrics subscriptionMetrics;

        switch (order.getOrderType()) {
            case AMENDMENT, CANCEL -> subscriptionMetrics = metricsService.getSubscriptionMetrics(
                order.getExternalSubscriptionId(),
                order.getStartDate().minusSeconds(1)
            );
            case RENEWAL -> subscriptionMetrics = metricsService.getSubscriptionMetrics(
                order.getRenewalForSubscriptionId(),
                order.getStartDate().minusSeconds(1)
            );
            case RESTRUCTURE -> subscriptionMetrics = metricsService.getSubscriptionMetrics(
                order.getRestructureForSubscriptionId(),
                order.getStartDate().minusSeconds(1)
            );
            default -> {
                String message = String.format("order type %s while calculating delta arr percent", order.getOrderType());
                LOGGER.info(message);
                subscriptionMetrics = null;
            }
        }

        Metrics currentOrderMetrics = orderMetrics != null ? orderMetrics : metricsService.getOrderMetrics(order, order.getStartDate());

        return Numbers.scaledPercentHandlingZeros(
            currentOrderMetrics.getDeltaArr(),
            subscriptionMetrics == null ? BigDecimal.ZERO : subscriptionMetrics.getExitArr()
        ).setScale(2, RoundingMode.HALF_UP);
    }

    static void validateNonDraftCrmOpportunityUpdate(Order order) {
        if (StringUtils.isBlank(order.getSfdcOpportunityStage()) || !order.getSfdcOpportunityStage().matches(CLOSED_OPPORTUNITY_REGEX)) {
            // no validation needed if the opportunity is not closed
            return;
        }
        // closed opportunity validation
        Role role = CurrentUserProvider.provideRole().orElseThrow(() -> new ForbiddenException("User must have role to perform this action"));
        if (role != Role.ADMIN) {
            throw new ForbiddenException("Only admins can update the CRM Opportunity");
        }
    }

    public static void validateEsignStatus(Order order, EsignService esignService) {
        ElectronicSignature esign;
        try {
            esign = esignService.getElectronicSignatureByOrderId(order.getOrderId());
        } catch (Exception e) {
            return;
        }

        if (
            order.getStatus() == OrderStatus.EXECUTED &&
            esign != null &&
            esign.getStatus() != null &&
            BooleanUtils.isFalse(esign.getStatus().isTerminal())
        ) {
            throw new InvalidInputException(
                String.format("Order %s has an active esign status %s so it cannot be executed", order.getOrderId(), esign.getStatus())
            );
        }
    }

    public static void validateOrderEntityScope(Order order, List<Charge> chargesInOrder) {
        String orderEntityId = order.getEntityId();
        List<Charge> chargesOutsideEntityScope = chargesInOrder
            .stream()
            .filter(charge -> !charge.getEntityIds().contains(EntityContext.ALL_ACCESS_ID) && !charge.getEntityIds().contains(orderEntityId))
            .toList();
        if (CollectionUtils.isNotEmpty(chargesOutsideEntityScope)) {
            List<String> chargeIds = chargesOutsideEntityScope.stream().map(Charge::getChargeId).distinct().toList();
            throw new InvalidInputException(String.format("Charges not scoped to order's entity: %s", String.join(", ", chargeIds)));
        }
    }

    /**
     * If a fully embedded Opportunity object is present on the Order, its values take precedence over
     * individual Opportunity-related attributes on the Order.
     * <p>
     * During the transition period, when the embedded Opportunity object exists, its relevant fields
     * will be copied to the corresponding flat Opportunity attributes on the Order for backward compatibility.
     * <p>
     * Note: In the underlying order database table, the Opportunity is represented solely by
     * the SfdcOpportunityId field; no other Opportunity fields are persisted.
     */
    public static void validateAndUpdateOpportunityInfo(Order order) {
        if (order.getOpportunity().isEmpty()) {
            return;
        }
        Opportunity opportunity = order.getOpportunity().get();

        // If opportunity is present, CrmID and Name are mandatory
        if (StringUtils.isBlank(opportunity.getCrmId())) {
            throw new InvalidInputException("Opportunity CRM ID cannot be blank");
        }
        if (StringUtils.isBlank(opportunity.getName())) {
            throw new InvalidInputException("Opportunity name cannot be blank");
        }
        order.setSfdcOpportunityId(opportunity.getCrmId());
        order.setSfdcOpportunityStage(opportunity.getStage());
        order.setSfdcOpportunityType(opportunity.getType());
        order.setSfdcOpportunityName(opportunity.getName());
    }
}
