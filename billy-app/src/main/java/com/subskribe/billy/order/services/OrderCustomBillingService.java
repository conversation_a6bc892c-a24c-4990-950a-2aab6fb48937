package com.subskribe.billy.order.services;

import com.google.common.annotations.VisibleForTesting;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.service.processor.model.ImmutableInvoicePreviewInput;
import com.subskribe.billy.invoice.service.processor.model.InvoicePreviewInput;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.model.CustomBillingPeriod;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.DSLContext;

public class OrderCustomBillingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCustomBillingService.class);
    private final FeatureService featureService;
    private final ProductCatalogGetService productCatalogGetService;
    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;
    private final TenantSettingService tenantSettingService;
    private final OrderDAO orderDAO;

    @Inject
    public OrderCustomBillingService(
        FeatureService featureService,
        ProductCatalogGetService productCatalogGetService,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        TenantSettingService tenantSettingService,
        OrderDAO orderDAO
    ) {
        this.featureService = featureService;
        this.productCatalogGetService = productCatalogGetService;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.tenantSettingService = tenantSettingService;
        this.orderDAO = orderDAO;
    }

    public void validateAndSetCustomBillingDetailsInOrder(Order order, boolean isDryRun) {
        if (featureService.isEnabled(Feature.CUSTOM_BILLING)) {
            if (order.getBillingCycle().getCycle() == Cycle.CUSTOM && shouldUseCustomBillingInvoicingInput(order)) {
                CustomBillingSchedule customBillingScheduleValidated = processCustomBillingInput(order, isDryRun).getLeft();
                order.setCustomBillingSchedule(customBillingScheduleValidated);
                order.setCustomBillingEligibleOrderLineIds(customBillingScheduleValidated.orderLines());
            }
            // NOTE : This is required for FE to be able to calculate the total amount eligible for custom billing when billing cycle is changed to custom from other billing cycles, such as yearly, monthly etc.
            else {
                Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
                List<String> customBillingEligibleOrderLines = getOrderLineItemsEligibleForCustomBilling(order, chargeMap)
                    .stream()
                    .map(OrderLineItem::getOrderLineId)
                    .toList();
                order.setCustomBillingEligibleOrderLineIds(customBillingEligibleOrderLines);
            }
        }
    }

    /**
     * This handles scenarios where a custom billed order might not have a custom billing schedule associated with it, in those cases we treat billing cycle as PAID_IN_FULL essentially
     *
     * @param order
     * @return whether to depend on custom billing schedule for custom billed order
     */
    public boolean shouldUseCustomBillingInvoicingInput(Order order) {
        String orderId = order.getOrderId();
        OrderType orderType = order.getOrderType();
        // For cancel orders we don't accept a custom billing schedule, and neither we are supposed to use it for invoicing purposes
        if (orderType == OrderType.CANCEL) {
            LOGGER.info("Order id, type : {}, {} - not using custom billing schedule", orderId, orderType);
            return false;
        }

        // This is to support UI flow, where on initial generation of a draft restructure or renewal order (by user's update subscription action)
        // as we don't have an UX to accept custom billing schedule input. Also, overloading source=USER for this check seems error prone so we are using a separate flag
        if (
            (orderType == OrderType.RESTRUCTURE || orderType == OrderType.RENEWAL) && Boolean.TRUE.equals(order.getShouldUseCustomBillingSchedule())
        ) {
            LOGGER.info(
                "Order id, type, source : {}, {}, {} - not using custom billing schedule as it's via update subscription ui action",
                orderId,
                orderType
            );
            return false;
        }

        var chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
        // If there are no eligible order lines for custom billing, we don't need to depend on custom billing schedule
        if (
            (order.getOrderType() == OrderType.NEW || order.getOrderType() == OrderType.AMENDMENT) &&
            CollectionUtils.isEmpty(getOrderLineItemsEligibleForCustomBilling(order, chargeMap))
        ) {
            LOGGER.info("Order id, type : {}, {} - not using custom billing schedule", orderId, orderType);
            return false;
        }
        LOGGER.info("Order id, type : {}, {} - using custom billing schedule", orderId, orderType);
        return true;
    }

    private Pair<CustomBillingSchedule, List<List<BillingPeriod>>> processCustomBillingInput(Order order, boolean isDryRun) {
        CustomBillingSchedule customBillingScheduleValidated = validateCustomBillingSchedule(order, order.getCustomBillingSchedule(), isDryRun);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        List<List<BillingPeriod>> billingPeriods = subscriptionBillingPeriodService.getBillingPeriodsForCustomBilling(
            timeZone,
            order,
            customBillingScheduleValidated,
            Optional.empty(),
            null,
            isDryRun // For dry run calculate preview as paid in full for order lines calculation, for actual order save calculate based on the custom billing schedules
        );
        LOGGER.info("Custom billing schedule validated and billing periods generated successfully : {}", billingPeriods);
        return Pair.of(customBillingScheduleValidated, billingPeriods);
    }

    private CustomBillingSchedule validateCustomBillingSchedule(Order order, CustomBillingSchedule customBillingSchedule, boolean isDryRun) {
        if (!featureService.isEnabled(Feature.CUSTOM_BILLING)) {
            throw new ConflictingStateException("Custom billing feature is not enabled");
        }
        Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
        return validateCustomBillingInput(order, chargeMap, customBillingSchedule, isDryRun);
    }

    @VisibleForTesting
    CustomBillingSchedule validateCustomBillingInput(
        Order order,
        Map<String, Charge> chargeMap,
        CustomBillingSchedule customBillingSchedule,
        boolean isDryRun
    ) {
        if (order.getBillingCycle().getCycle() != Cycle.CUSTOM || order.getBillingCycle().getStep() != 1) {
            throw new InvalidInputException("Custom billing schedule is only allowed for order with billing cycle CUSTOM and step 1");
        }

        if (order.getStatus() != null && order.getStatus() != OrderStatus.DRAFT) {
            throw new InvalidInputException("Defining custom billing schedule is only allowed for draft orders");
        }

        if (Objects.isNull(customBillingSchedule)) {
            throw new InvalidInputException("Custom billing schedule is required for custom billing cycle");
        }

        if (customBillingSchedule.orderId() != null && !StringUtils.equals(order.getOrderId(), customBillingSchedule.orderId())) {
            throw new InvalidInputException("For draft order custom billing schedule, order ids should match");
        }

        if (order.getOrderType() == OrderType.AMENDMENT) {
            validateExistingOrderLinesUpdateForAmendment(order, chargeMap);
        }

        // TODO : For amendments add a check that we follow the same ad hoc or recurrence based variants throughout the orders? Don't allow mix of adhoc and recurrence based variants per order

        // Trigger instant and amount should only be provided for paid in full billing cycle, this is to support ad hoc billing dates, for ex - Feb 1 30k, July 1 20k and so on
        // Otherwise the billing dates should be calculated based on the billing cycle.
        customBillingSchedule
            .schedules()
            .forEach(customBillingPeriod -> {
                if (
                    customBillingPeriod.getRecurrenceWithCount().recurrence().getCycle() != Cycle.PAID_IN_FULL &&
                    (customBillingPeriod.getTriggerInstant() != null || customBillingPeriod.getAmount() != null)
                ) {
                    throw new InvalidInputException("Trigger instant and/or amount is only allowed for paid in full billing cycle");
                }
            });

        List<OrderLineItem> eligibleOrderLines = getOrderLineItemsEligibleForCustomBilling(order, chargeMap);
        List<String> eligibleOrderLineIds = eligibleOrderLines.stream().map(OrderLineItem::getOrderLineId).toList();

        CustomBillingSchedule customBillingScheduleValidated = customBillingSchedule;
        // Make sure that the custom billing schedule order lines match the eligible order line items
        if (!CollectionUtils.isEqualCollection(eligibleOrderLineIds, customBillingSchedule.orderLines())) {
            List<String> dryRunItems = eligibleOrderLines.stream().filter(OrderLineItem::isDryRunItem).map(OrderLineItem::getOrderLineId).toList();
            LOGGER.info(
                "Custom billing schedule order lines : {}, do not match order line items, found dryRunItems : {}, updating the input with eligible order lines : {}",
                customBillingSchedule.orderLines(),
                dryRunItems,
                eligibleOrderLineIds
            );
            customBillingScheduleValidated = customBillingScheduleValidated.withOrderLines(eligibleOrderLineIds);
        }

        if (isDryRun) {
            // Bypass user input validations for trigger dates and amount during dryRun to not block order line amount calculations, as user can play around with it.
            return customBillingScheduleValidated;
        }

        // NOTE : During dry run invocation FE doesn't know the total amount eligible for custom billing in case there is a change in qty or new lines are added.
        // This check should only be done when the order is being saved
        if (customBillingScheduleValidated.getCustomBillingType() == CustomBillingSchedule.CustomBillingType.ADHOC) {
            validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts(customBillingScheduleValidated, order.getCurrency().getCurrencyCode());
            validateAdhocCustomBillingScheduleTotalAmount(customBillingScheduleValidated, eligibleOrderLines);
        }
        validateCustomBillingScheduleTermLength(order, customBillingScheduleValidated);
        return customBillingScheduleValidated;
    }

    private void validateExistingOrderLinesUpdateForAmendment(Order order, Map<String, Charge> chargeMap) {
        var updatedExistingLines = order.getLineItemsNetEffect().stream().filter(item -> item.getAction() == ActionType.UPDATE).toList();

        if (CollectionUtils.isNotEmpty(updatedExistingLines)) {
            // Allow percent of charge UPDATE action if a new line item for the target plan is added

            // Get unique charges for the updated lines
            Set<Charge> updatedCharges = updatedExistingLines
                .stream()
                .map(OrderLineItem::getChargeId)
                .map(chargeMap::get)
                .collect(Collectors.toSet());

            // Ensure all these are percent of plan charges
            Set<Charge> percentOfCharges = updatedCharges
                .stream()
                .filter(charge -> {
                    if (charge.getType() != ChargeType.PERCENTAGE_OF) {
                        LOGGER.error(
                            String.format(
                                "Found amendment for non percent of plan charge %s for updated line item %s, in custom billing.",
                                charge.getChargeId(),
                                charge.getPlanId()
                            )
                        );
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toSet());

            // This means some non percent of charge line is being modified, which is not allowed in custom billing, this should be gated by UI though
            if (updatedCharges.size() != percentOfCharges.size()) {
                throw new InvalidInputException(
                    String.format(
                        "Updating quantity for existing line items %s is not allowed for custom billing cycle, dropping lines is allowed!",
                        updatedExistingLines
                    )
                );
            }

            Set<String> uniqueAddedOrRemovedTargetPlanIds = order
                .getLineItemsNetEffect()
                .stream()
                .filter(
                    item ->
                        item.getAction() == ActionType.ADD ||
                        item.getAction() == ActionType.REMOVE || // Even for debooked target plans we set corresponding percent of charge line's action as UPDATE internally
                        item.getAction() == ActionType.NONE
                ) // If you debook then revert it on UI then the action passed is NONE but we still need to treat percent of line as UPDATE
                .map(OrderLineItem::getPlanId)
                .collect(Collectors.toSet());

            // Ensure that the percent of charge lines with update action have at least one mapping to the newly added target plans
            for (OrderLineItem percentOfChargeOrderLine : updatedExistingLines) {
                Set<String> targetPlanIds = productCatalogGetService.getTargetPlanIdsForPercentOfPlanWithoutChecks(
                    percentOfChargeOrderLine.getPlanId()
                );
                var matchedTargetPlanIdsForPercentOfChargePlan = targetPlanIds.stream().filter(uniqueAddedOrRemovedTargetPlanIds::contains).toList();
                if (CollectionUtils.isEmpty(matchedTargetPlanIdsForPercentOfChargePlan)) {
                    throw new InvalidInputException(
                        String.format(
                            "Found amendment for percent of plan charge %s for updated line item %s, in custom billing. But no matching target plan found in the added lines %s.",
                            percentOfChargeOrderLine.getChargeId(),
                            percentOfChargeOrderLine.getPlanId(),
                            uniqueAddedOrRemovedTargetPlanIds
                        )
                    );
                }
            }
        }
    }

    @VisibleForTesting
    void validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts(CustomBillingSchedule customBillingSchedule, String currencyCode) {
        customBillingSchedule
            .schedules()
            .forEach(customBillingPeriod -> {
                if (customBillingPeriod.getAmount() == null) {
                    throw new InvalidInputException(
                        String.format("[Currency : %s] - amount cannot be empty for adhoc custom billing schedule!", currencyCode)
                    );
                }
                if (Numbers.isZero(customBillingPeriod.getAmount())) {
                    throw new InvalidInputException(
                        String.format("[Currency : %s] - amount cannot be zero for adhoc custom billing schedule!", currencyCode)
                    );
                }
                if (Numbers.hasInvalidCurrencyScale(customBillingPeriod.getAmount(), currencyCode)) {
                    throw new InvalidInputException(
                        String.format(
                            "[Currency : %s] - amount %s should have correct currency precision for adhoc custom billing schedule!",
                            currencyCode,
                            customBillingPeriod.getAmount()
                        )
                    );
                }
            });

        // Check if all amounts are either all positive or all negative, a mix is not allowed
        boolean allPositive = customBillingSchedule
            .schedules()
            .stream()
            .allMatch(customBillingPeriod -> Numbers.isPositive(customBillingPeriod.getAmount()));
        boolean allNegative = customBillingSchedule
            .schedules()
            .stream()
            .allMatch(customBillingPeriod -> Numbers.isNegative(customBillingPeriod.getAmount()));
        if (!allPositive && !allNegative) {
            throw new InvalidInputException("Custom billing schedule amounts should be either all positive or all negative, not both");
        }

        // Check if any of the triggerInstants in the schedules are duplicated
        Set<Instant> triggerInstants = new HashSet<>();
        customBillingSchedule
            .schedules()
            .stream()
            .map(CustomBillingPeriod::getTriggerInstant)
            .forEach(triggerInstant -> {
                if (!triggerInstants.add(triggerInstant)) {
                    var localDateTime = DateTimeConverter.instantToLocalDateTime(triggerInstant);
                    throw new InvalidInputException(
                        String.format("Invoice trigger date %s is duplicated in the custom billing schedule", localDateTime.toLocalDate())
                    );
                }
            });
    }

    private void validateAdhocCustomBillingScheduleTotalAmount(CustomBillingSchedule customBillingSchedule, List<OrderLineItem> eligibleOrderLines) {
        BigDecimal orderLinesTotalAmount = eligibleOrderLines.stream().map(OrderLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal customBillingScheduleTotalAmount = customBillingSchedule
            .schedules()
            .stream()
            .map(CustomBillingPeriod::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Make sure that the custom billing schedule total amount matches the eligible order line total amount
        if (!Numbers.equals(orderLinesTotalAmount, customBillingScheduleTotalAmount)) {
            BigDecimal amountDiff = orderLinesTotalAmount.subtract(customBillingScheduleTotalAmount);
            throw new InvalidInputException(
                String.format(
                    "[NO_MATCH] Invoice cadence total amount %s eligible order lines total amount %s. Please update the cadence to adjust the remaining/extra amount of %s",
                    customBillingScheduleTotalAmount,
                    orderLinesTotalAmount,
                    amountDiff
                )
            );
        }
    }

    private void validateCustomBillingScheduleTermLength(Order order, CustomBillingSchedule customBillingSchedule) {
        if (customBillingSchedule.getCustomBillingType() == CustomBillingSchedule.CustomBillingType.ADHOC) {
            ZoneId tenantZoneId = tenantSettingService.getTenantSetting().getDefaultTimeZone().toZoneId();
            // Make sure all triggerInstants are between the order term duration
            Instant orderStartDate = order.getStartDate();
            Instant orderEndDate = order.getEndDate();
            customBillingSchedule
                .schedules()
                .stream()
                .map(CustomBillingPeriod::getTriggerInstant)
                .forEach(triggerInstant -> {
                    if (triggerInstant.isBefore(orderStartDate) || triggerInstant.isAfter(orderEndDate)) {
                        throw new InvalidInputException(
                            String.format(
                                "Input invoice trigger date %s is not between order start date %s and end date %s",
                                LocalDate.ofInstant(triggerInstant, tenantZoneId),
                                LocalDate.ofInstant(orderStartDate, tenantZoneId),
                                LocalDate.ofInstant(orderEndDate.minus(1, ChronoUnit.DAYS), tenantZoneId)
                            )
                        );
                    }
                });
            customBillingSchedule.schedules().sort(Comparator.comparing(CustomBillingPeriod::getTriggerInstant));
            return;
        }
        //Make sure custom billing schedule recurrence total duration matches order term length
        Integer customBillingScheduleDurationInMonths = customBillingSchedule
            .schedules()
            .stream()
            .map(schedule -> {
                CustomBillingSchedule.CustomBillingRecurrence recurrenceWithCount = schedule.getRecurrenceWithCount();
                return (
                    Recurrence.fromRecurrenceJson(recurrenceWithCount.recurrence()).getRecurrenceDurationInMonths() * recurrenceWithCount.count()
                );
            })
            .reduce(0, Integer::sum);
        Integer orderTermLengthInMonths = order.getTermLength().getRecurrenceDurationInMonths();
        if (customBillingScheduleDurationInMonths.compareTo(orderTermLengthInMonths) != 0) {
            throw new InvalidInputException(
                String.format(
                    "Custom billing schedule recurrence total duration %s does not match order term length %s",
                    customBillingScheduleDurationInMonths,
                    orderTermLengthInMonths
                )
            );
        }
    }

    public List<OrderLineItem> getOrderLineItemsEligibleForCustomBilling(Order order, Map<String, Charge> chargeMap) {
        // For cancellations we don't expect a custom billing schedule to be defined
        if (order.getOrderType() == OrderType.CANCEL) {
            return List.of();
        }

        List<OrderLineItem> lineItems = order.getOrderType() == OrderType.AMENDMENT ? order.getLineItemsNetEffect() : order.getLineItems();
        Map<String, ActionType> lineIdAndActionMap = lineItems
            .stream()
            .filter(orderLineItem -> orderLineItem.getOrderLineId() != null)
            .collect(Collectors.toMap(OrderLineItem::getOrderLineId, OrderLineItem::getAction));
        var eligibleLineItems = lineItems
            .stream()
            .filter(orderLineItem -> {
                var charge = chargeMap.get(orderLineItem.getChargeId());
                return isLineItemEligibleForCustomBilling(orderLineItem, charge, order.getOrderType());
            })
            .toList();
        LOGGER.info(
            "Order : {}, with original lines : {}, and eligible for custom billing schedule : {}",
            order.getOrderId(),
            lineIdAndActionMap,
            eligibleLineItems.stream().map(OrderLineItem::getOrderLineId).toList()
        );
        return eligibleLineItems;
    }

    public static boolean isLineItemEligibleForCustomBilling(OrderLineItem orderLineItem, Charge charge, OrderType orderType) {
        if (orderType == OrderType.CANCEL) {
            return false;
        }

        // These lines/charges are not be included for custom billing schedules but can be present on the order
        if (
            charge.isEventBased() ||
            charge.getType() == ChargeType.USAGE ||
            charge.getBillingCycle() != BillingCycle.DEFAULT ||
            (orderType == OrderType.AMENDMENT && orderLineItem.getAction() == ActionType.REMOVE) // Don't include removed lines in custom billing schedule
        ) {
            return false;
        }

        BigDecimal amount = orderLineItem.getAmount();
        if (amount == null) {
            return true;
        }

        // Leaving out 0 amount, rest of the charge/order lines are eligible for custom billing schedule
        return !Numbers.isZero(amount);
    }

    /**
     * This is used to check if the line item should be included for custom billing schedule or not
     * This is different from isLineItemEligibleForCustomBilling, as this is used to check if the line item should be included for invoicing or not
     * @param order
     * @param previewInput
     * @param orderType
     * @param orderLineItem
     * @return true if the line item is not invoiced via custom billing logic, false if it is invoiced via custom billing logic
     */
    public static boolean notInvoicedForCustomBilling(
        Order order,
        ImmutableInvoicePreviewInput previewInput,
        OrderType orderType,
        OrderLineItem orderLineItem
    ) {
        Map<String, Charge> chargeMap = previewInput.getChargeMap();
        Map<String, InvoicePreviewInput.CustomBillingInvoicingInput> customBillingInvoicingInput = previewInput.getCustomBillingInvoicingInput();
        // NOTE : This check is contextual and has to be here, isLineItemEligibleForCustomBilling only checks if lines are eligible for custom billing cycle (irrespective of the order billing cycle), invoicing logic is not its responsibility
        // If it's not a custom billed order then include this line item for regular processing
        if (order.getBillingCycle() == null || order.getBillingCycle().getCycle() != Cycle.CUSTOM) {
            return true;
        }

        if (MapUtils.isEmpty(customBillingInvoicingInput) || !customBillingInvoicingInput.containsKey(order.getOrderId())) {
            return true;
        }

        // If it is a custom billed order but the preview is for order line amount calculation then include it as it will be treated as PAID_IN_FULL billing cycle
        InvoicePreviewInput.CustomBillingInvoicingInput customBillingInvoicingInputForOrder = customBillingInvoicingInput.get(order.getOrderId());
        if (customBillingInvoicingInputForOrder.forOrderLineCalculation()) {
            return true;
        }

        return !isLineItemEligibleForCustomBilling(orderLineItem, chargeMap.get(orderLineItem.getChargeId()), orderType);
    }

    public void saveOrInvalidateCustomBillingSchedule(
        DSLContext dslContext,
        Order order,
        List<Charge> chargesInOrder,
        CustomBillingSchedule customBillingSchedule
    ) {
        if (featureService.isEnabled(Feature.CUSTOM_BILLING) && order.getBillingCycle().getCycle() == Cycle.CUSTOM) {
            if (shouldUseCustomBillingInvoicingInput(order)) {
                saveCustomBillingSchedule(dslContext, chargesInOrder, order, customBillingSchedule);
            } else {
                orderDAO.invalidateExistingCustomBillingSchedule(dslContext, order.getTenantId(), order.getOrderId());
            }
        }
    }

    private void saveCustomBillingSchedule(
        DSLContext dslContext,
        List<Charge> chargesInOrder,
        Order order,
        CustomBillingSchedule customBillingSchedule
    ) {
        LOGGER.info("Saving custom billing schedule for order: {}", order.getOrderId());
        Map<String, Charge> chargeMap = chargesInOrder.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        // orderDAO.addOrder changes order lines ids when saved, so we need to update the custom billing schedule with the new ids
        CustomBillingSchedule toSave = customBillingSchedule.withOrderLines(
            getOrderLineItemsEligibleForCustomBilling(order, chargeMap).stream().map(OrderLineItem::getOrderLineId).toList()
        );
        CustomBillingSchedule savedSchedule = orderDAO.saveCustomBillingSchedule(dslContext, order, toSave);
        order.setCustomBillingSchedule(savedSchedule);
        order.setCustomBillingEligibleOrderLineIds(savedSchedule.orderLines());
    }
}
