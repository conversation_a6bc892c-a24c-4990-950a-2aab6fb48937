package com.subskribe.billy.order;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.TimeZone;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;

public class EvergreenUtils {

    // maximum start date from now() allowed for evergreen. This is to ensure start date + ramp schedule doesn't go past sentinel date. Value of 5 is fairly arbitrary
    private static final int MAX_EVERGREEN_START_DATE_FROM_NOW = 5;
    private static final int MAX_EVERGREEN_RAMP_DURATION = 10;
    private static final Duration EVERGREEN_INVOICE_PREVIEW_WINDOW = Duration.ofDays(365);

    // used as a temporary sentinel marker end date for evergreen order and subscriptions
    public static final Instant EVERGREEN_SENTINEL_END_DATE = Instant.ofEpochSecond(2524608000L); // 2050-01-01 00:00:00

    private final FeatureService featureService;

    @Inject
    public EvergreenUtils(FeatureService featureService) {
        this.featureService = featureService;
    }

    public void validateEvergreenStartDateAndRamps(Order order, TimeZone timeZone) {
        if (featureService.isNotEnabled(Feature.EVERGREEN) || !isEvergreenOrder(order)) {
            return;
        }

        Instant maxEvergreenStartDate = DateTimeCalculator.plusYears(timeZone.toZoneId(), Instant.now(), MAX_EVERGREEN_START_DATE_FROM_NOW);
        if (order.getStartDate().isAfter(maxEvergreenStartDate)) {
            var message = String.format("Start date must be within %s years from now", MAX_EVERGREEN_START_DATE_FROM_NOW);
            throw new InvalidInputException(message);
        }

        List<Instant> rampIntervals = order.getRampInterval();

        if (CollectionUtils.isNotEmpty(rampIntervals)) {
            Instant maxEvergreenRampDuration = DateTimeCalculator.plusYears(timeZone.toZoneId(), order.getStartDate(), MAX_EVERGREEN_RAMP_DURATION);
            Instant lastRampInterval = Collections.max(rampIntervals);
            if (lastRampInterval.isAfter(maxEvergreenRampDuration)) {
                var message = String.format("Ramp intervals can only extend up to %s years from start date", MAX_EVERGREEN_START_DATE_FROM_NOW);
                throw new InvalidInputException(message);
            }
        }
    }

    public static boolean isEvergreenOrder(Order order) {
        return order.getSubscriptionDurationModel() == SubscriptionDurationModel.EVERGREEN;
    }

    public static void sanitizeEvergreenOrder(Order order) {
        if (!isEvergreenOrder(order)) {
            return;
        }

        order.setEndDate(null);
        var lineItems = order.getLineItems();
        lineItems.forEach(li -> {
            if (li.getEndDate() != null && li.getEndDate().compareTo(EVERGREEN_SENTINEL_END_DATE) >= 0) {
                li.setEndDate(null);
            }
        });
    }

    public static boolean isEvergreenOrderDetail(OrderDetail orderDetail) {
        return orderDetail.getSubscriptionDurationModel() == SubscriptionDurationModel.EVERGREEN;
    }

    public static void sanitizeEvergreenOrderDetail(OrderDetail orderDetail) {
        if (!isEvergreenOrderDetail(orderDetail)) {
            return;
        }

        orderDetail.setEndDate(null);
        var lineItems = orderDetail.getLineItems();
        lineItems.forEach(li -> {
            if (li.getEndDate() != null && Instant.ofEpochSecond(li.getEndDate()).compareTo(EVERGREEN_SENTINEL_END_DATE) >= 0) {
                li.setEndDate(null);
            }
        });
    }

    public static void extendInvoicePreviewWindowForEvergreenOrder(Order order) {
        if (!isEvergreenOrder(order)) {
            return;
        }

        order.setEndDate(order.getStartDate().plus(EVERGREEN_INVOICE_PREVIEW_WINDOW));
    }
}
