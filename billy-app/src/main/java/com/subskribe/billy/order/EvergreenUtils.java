package com.subskribe.billy.order;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.validation.Validator;
import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.TimeZone;
import org.apache.commons.collections4.CollectionUtils;

public class EvergreenUtils {

    // maximum start date from now() allowed for evergreen. This is to ensure start date + ramp schedule doesn't go past sentinel date. Value of 5 is fairly arbitrary
    private static final int MAX_EVERGREEN_START_DATE_FROM_NOW = 5;
    private static final int MAX_EVERGREEN_RAMP_DURATION = 10;
    private static final Duration EVERGREEN_INVOICE_PREVIEW_WINDOW = Duration.ofDays(365);

    // used as a temporary sentinel marker end date for evergreen order and subscriptions
    public static final Instant EVERGREEN_SENTINEL_END_DATE = Instant.ofEpochSecond(2524608000L); // 2050-01-01 00:00:00

    public static void validateEvergreenOrder(Order order, FeatureService featureService) {
        if (!isEvergreenOrder(order, featureService)) {
            return;
        }

        Recurrence billingCycle = order.getBillingCycle();
        Validator.checkNonNullInternal(billingCycle, "billingCycle cannot be null");
        if (billingCycle.getCycle() == Cycle.PAID_IN_FULL || billingCycle.getCycle() == Cycle.CUSTOM) {
            throw new InvalidInputException("Evergreen orders cannot have UPFRONT or CUSTOM billing");
        }
    }

    public static void validateEvergreenStartDateAndRamps(Order order, TimeZone timeZone, FeatureService featureService) {
        if (!isEvergreenOrder(order, featureService)) {
            return;
        }

        Instant maxEvergreenStartDate = DateTimeCalculator.plusYears(timeZone.toZoneId(), Instant.now(), MAX_EVERGREEN_START_DATE_FROM_NOW);
        if (order.getStartDate().isAfter(maxEvergreenStartDate)) {
            var message = String.format("Start date must be within %s years from now", MAX_EVERGREEN_START_DATE_FROM_NOW);
            throw new InvalidInputException(message);
        }

        List<Instant> rampIntervals = order.getRampInterval();

        if (CollectionUtils.isNotEmpty(rampIntervals)) {
            Instant maxEvergreenRampDuration = DateTimeCalculator.plusYears(timeZone.toZoneId(), order.getStartDate(), MAX_EVERGREEN_RAMP_DURATION);
            Instant lastRampInterval = Collections.max(rampIntervals);
            if (lastRampInterval.isAfter(maxEvergreenRampDuration)) {
                var message = String.format("Ramp intervals can only extend up to %s years from start date", MAX_EVERGREEN_START_DATE_FROM_NOW);
                throw new InvalidInputException(message);
            }
        }
    }

    public static boolean isEvergreenOrder(Order order, FeatureService featureService) {
        return featureService.isEnabled(Feature.EVERGREEN) && order.getSubscriptionDurationModel() == SubscriptionDurationModel.EVERGREEN;
    }

    public static void sanitizeEvergreenOrder(Order order, FeatureService featureService) {
        if (!isEvergreenOrder(order, featureService)) {
            return;
        }

        order.setEndDate(null);
        var lineItems = order.getLineItems();
        lineItems.forEach(li -> {
            if (li.getEndDate() != null && li.getEndDate().compareTo(EVERGREEN_SENTINEL_END_DATE) >= 0) {
                li.setEndDate(null);
            }
        });
    }

    public static boolean isEvergreenOrderDetail(OrderDetail orderDetail) {
        return orderDetail.getSubscriptionDurationModel() == SubscriptionDurationModel.EVERGREEN;
    }

    public static void sanitizeEvergreenOrderDetail(OrderDetail orderDetail) {
        if (!isEvergreenOrderDetail(orderDetail)) {
            return;
        }

        orderDetail.setEndDate(null);
        var lineItems = orderDetail.getLineItems();
        lineItems.forEach(li -> {
            if (li.getEndDate() != null && Instant.ofEpochSecond(li.getEndDate()).compareTo(EVERGREEN_SENTINEL_END_DATE) >= 0) {
                li.setEndDate(null);
            }
        });
    }

    public static void extendInvoicePreviewWindowForEvergreenOrder(Order order, FeatureService featureService) {
        if (!isEvergreenOrder(order, featureService)) {
            return;
        }

        order.setEndDate(order.getStartDate().plus(EVERGREEN_INVOICE_PREVIEW_WINDOW));
    }

    public static boolean isEvergreenSubscriptionDetail(SubscriptionDetail subscriptionDetail) {
        return subscriptionDetail.getDurationModel() == SubscriptionDurationModel.EVERGREEN;
    }

    public static void sanitizeEvergreenSubscriptionDetail(SubscriptionDetail subscriptionDetail) {
        if (!isEvergreenSubscriptionDetail(subscriptionDetail)) {
            return;
        }

        subscriptionDetail.setEndDate(null);
        var items = subscriptionDetail.getCharges();
        items.forEach(c -> {
            if (c.getEndDate() != null && Instant.ofEpochSecond(c.getEndDate()).compareTo(EVERGREEN_SENTINEL_END_DATE) >= 0) {
                c.setEndDate(null);
            }
        });
    }
}
