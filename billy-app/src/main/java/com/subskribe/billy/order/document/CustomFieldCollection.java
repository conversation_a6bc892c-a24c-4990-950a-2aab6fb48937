package com.subskribe.billy.order.document;

import com.subskribe.billy.customfield.model.CustomField;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

public class CustomFieldCollection {

    private final List<TemplateCustomField> customFields;

    // used to determine if any of the custom fields have a given value:
    // eg: {{#anyPlanCustomField.<fieldName>.hasValue.<value>}}
    // or: {{#anyPlanCustomField.<fieldName>.isTrue}}
    public CustomFieldCollection(List<TemplateCustomField> customFields) {
        this.customFields = customFields == null ? List.of() : customFields;
    }

    // used to determine if any of the custom field has a given value:
    // {{#anyPlanCustomField.<fieldName>.hasValue.<value>}}
    public Map<String, Boolean> hasValue() {
        return customFields
            .stream()
            .map(TemplateCustomField::getValue)
            .filter(StringUtils::isNotEmpty)
            .distinct()
            .collect(Collectors.toMap(Function.identity(), value -> true));
    }

    // used to determine if any of the custom fields contain a non-empty value
    public boolean anyHasValue() {
        return customFields.stream().anyMatch(customField -> StringUtils.isNotEmpty(customField.getValue()));
    }

    // truthiness check. Returns true if any of the custom field value is "yes", "y" or "true"
    // {{#anyPlanCustomField.<fieldName>.isTrue}}
    public Boolean isTrue() {
        return customFields.stream().anyMatch(TemplateCustomField::isTrue);
    }

    public static Map<String, CustomFieldCollection> getCustomFieldsMap(Map<String, CustomField> customFieldsByName) {
        if (Objects.isNull(customFieldsByName) || customFieldsByName.isEmpty()) {
            return Map.of();
        }

        // regroup custom field values by name
        Map<String, List<TemplateCustomField>> customFieldValues = customFieldsByName
            .values()
            .stream()
            .map(CustomField::getEntries)
            .flatMap(entries -> entries.values().stream())
            .map(TemplateCustomField::new)
            .collect(Collectors.groupingBy(TemplateCustomField::getName));

        // prepare map of custom field name to custom field collection (aggregated values)
        return customFieldValues
            .entrySet()
            .stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> new CustomFieldCollection(entry.getValue())));
    }
}
