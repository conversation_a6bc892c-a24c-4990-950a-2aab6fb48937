package com.subskribe.billy.order.db;

import static com.subskribe.billy.jooq.default_schema.Tables.ACCOUNT_CONTACT;
import static com.subskribe.billy.jooq.default_schema.Tables.OPPORTUNITY;
import static com.subskribe.billy.jooq.default_schema.Tables.ORDER_CUSTOM_BILLING_SCHEDULE;
import static com.subskribe.billy.jooq.default_schema.tables.AccountOrder.ACCOUNT_ORDER;
import static com.subskribe.billy.jooq.default_schema.tables.AccountOrderComment.ACCOUNT_ORDER_COMMENT;
import static com.subskribe.billy.jooq.default_schema.tables.AccountOrderLineItem.ACCOUNT_ORDER_LINE_ITEM;
import static com.subskribe.billy.metrics.model.StoredMetrics.RECOMPUTE_METRICS_JSONB;
import static org.jooq.impl.DSL.selectDistinct;

import com.google.common.collect.Sets;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountOrderCommentRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountOrderLineItemRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountOrderRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.OrderCustomBillingScheduleRecord;
import com.subskribe.billy.order.EvergreenUtils;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderComment;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderStub;
import com.subskribe.billy.order.model.UpdateOrderAttributes;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class OrderDAO {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderDAO.class);

    private static final String UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME = "index_account_order_external_id";
    private static final String ORDER_EXISTS_WITH_EXTERNAL_ID = "another order exists with external id: %s";
    private static final String DATE_FORMAT_FOR_MOMENT = "yyyy-MM-dd";

    private final OrderDAOMapper orderDAOMapper;

    private final TenantIdProvider tenantIdProvider;

    private final DSLContextProvider dslContextProvider;

    private final CustomBillingScheduleRecordMapper customBillingScheduleRecordMapper;

    private final FeatureService featureService;

    @Inject
    public OrderDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider, FeatureService featureService) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.featureService = featureService;
        orderDAOMapper = Mappers.getMapper(OrderDAOMapper.class);
        customBillingScheduleRecordMapper = Mappers.getMapper(CustomBillingScheduleRecordMapper.class);
    }

    public Order addOrder(DSLContext dslContext, Order order) {
        var localTime = LocalDateTime.now(ZoneOffset.UTC);

        var accountOrderRecord = orderDAOMapper.orderToRecord(order);
        accountOrderRecord.setCreatedOn(localTime);
        accountOrderRecord.setUpdatedOn(localTime);
        accountOrderRecord.setShouldRegeneratePdf(true);
        if (EvergreenUtils.isEvergreenOrder(order, featureService)) {
            accountOrderRecord.setSubscriptionDurationModel(SubscriptionDurationModel.EVERGREEN.name());
            accountOrderRecord.setEndDate(DateTimeConverter.instantToLocalDateTime(EvergreenUtils.EVERGREEN_SENTINEL_END_DATE));
        }

        PostgresErrorHandler.withConstraintAsConflict(
            () -> dslContext.insertInto(ACCOUNT_ORDER).set(accountOrderRecord).execute(),
            UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME,
            String.format(ORDER_EXISTS_WITH_EXTERNAL_ID, order.getExternalId())
        );

        List<OrderLineItem> orderLines = new ArrayList<>(order.getLineItems());
        if (CollectionUtils.isNotEmpty(order.getMissingRenewalOrderLineItems())) {
            orderLines.addAll(order.getMissingRenewalOrderLineItems());
        }
        bulkInsertOrderLines(dslContext, orderLines, localTime);

        var result = getOrderByOrderId(dslContext, order.getOrderId());
        if (result.isEmpty()) {
            var message = String.format("order with order id %s not found", order.getOrderId());
            LOGGER.error(message);
            throw new IllegalStateException(message);
        }
        return result.get();
    }

    private void bulkInsertOrderLines(DSLContext dslContext, List<OrderLineItem> orderLines, LocalDateTime localTime) {
        var orderLineRecords = orderDAOMapper.orderLineItemsToRecords(orderLines);
        orderLineRecords.forEach(li -> {
            li.reset(ACCOUNT_ORDER_LINE_ITEM.ID);
            li.setOrderLineId(AutoGenerate.getNewId()); // new lines should get new ids
            li.setCreatedOn(localTime);
            li.setUpdatedOn(localTime);
        });

        dslContext.batchInsert(orderLineRecords).execute();
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Order> getOrders(PaginationQueryParams paginationQueryParams, Optional<OrderStatus> orderStatus) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var query = dslContext.select().from(ACCOUNT_ORDER).where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId)).and(ACCOUNT_ORDER.IS_DELETED.isFalse());
        if (orderStatus.isPresent()) {
            query = query.and(ACCOUNT_ORDER.STATUS.eq(orderStatus.get().name()));
        }

        return getOrdersFromPaginatedQuery(dslContext, tenantId, query, paginationQueryParams);
    }

    public List<Order> getOrders(String accountId, Set<String> orderIds) {
        Validator.validateStringNotBlank(accountId, "accountId is required");
        Validator.validateCollectionNotEmpty(orderIds, "orderIds");
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var accountOrderRecords = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.ACCOUNT_ID.eq(accountId))
            .and(ACCOUNT_ORDER.ORDER_ID.in(orderIds).and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId)).and(ACCOUNT_ORDER.IS_DELETED.isFalse()))
            .fetchInto(AccountOrderRecord.class);

        if (CollectionUtils.isEmpty(accountOrderRecords)) {
            return List.of();
        }

        var orders = orderDAOMapper.recordsToOrders(accountOrderRecords);
        return getOrdersWithOrderLineItems(dslContext, orders);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Order> getOrdersInAccount(String accountId, PaginationQueryParams paginationQueryParams, Optional<OrderStatus> orderStatus) {
        Validator.validateStringNotBlank(accountId, "accountId is required");
        Validator.validateNonNullArgumentInternal(paginationQueryParams, "paginationQueryParams");

        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.ACCOUNT_ID.eq(accountId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse());

        if (orderStatus.isPresent()) {
            query = query.and(ACCOUNT_ORDER.STATUS.eq(orderStatus.get().name()));
        }

        return getOrdersFromPaginatedQuery(dslContext, tenantId, query, paginationQueryParams);
    }

    private List<Order> getOrdersFromPaginatedQuery(
        DSLContext dslContext,
        String tenantId,
        SelectConditionStep<Record> query,
        PaginationQueryParams paginationQueryParams
    ) {
        var accountOrderRecords = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            ACCOUNT_ORDER,
            ACCOUNT_ORDER.ID,
            ACCOUNT_ORDER.TENANT_ID,
            ACCOUNT_ORDER.CREATED_ON,
            AccountOrderRecord.class
        );

        var orders = orderDAOMapper.recordsToOrders(accountOrderRecords);
        return getOrdersWithOrderLineItems(dslContext, orders);
    }

    public List<Order> getOrdersByAccountId(DSLContext dslContext, String accountId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var accountOrderRecords = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.ACCOUNT_ID.eq(accountId).and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId)).and(ACCOUNT_ORDER.IS_DELETED.isFalse()))
            .orderBy(ACCOUNT_ORDER.EXECUTED_ON.asc())
            .fetchInto(AccountOrderRecord.class);

        if (CollectionUtils.isEmpty(accountOrderRecords)) {
            return List.of();
        }

        var orders = orderDAOMapper.recordsToOrders(accountOrderRecords);
        return getOrdersWithOrderLineItems(dslContext, orders);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Order> getOrderHeadersBySubscriptionUuid(UUID subscriptionUuid, Optional<OrderStatus> status) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(
                ACCOUNT_ORDER.SUBSCRIPTION_ID.eq(subscriptionUuid).and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId)).and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            );

        if (status.isPresent()) {
            // if querying by specific order status, add to where clause. Otherwise, return all statuses are retrieved
            query = query.and(ACCOUNT_ORDER.STATUS.eq(status.get().toString()));
        }

        var accountOrderRecords = query.fetchInto(AccountOrderRecord.class);
        if (CollectionUtils.isEmpty(accountOrderRecords)) {
            return List.of();
        }

        return orderDAOMapper.recordsToOrders(accountOrderRecords);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Order> getOrdersByExternalSubscriptionId(DSLContext dslContext, String subscriptionId, Optional<OrderStatus> orderStatus) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var query = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.EXTERNAL_SUBSCRIPTION_ID.eq(subscriptionId))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId));

        return getOrdersFromQuery(dslContext, orderStatus, query);
    }

    public List<String> getOrderIdsByExternalSubscriptionId(DSLContext dslContext, String subscriptionId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var query = dslContext
            .select(ACCOUNT_ORDER.ORDER_ID)
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.EXTERNAL_SUBSCRIPTION_ID.eq(subscriptionId))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse());

        return query.fetchInto(String.class);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Order> getOrdersBySubscriptionIdOrRenewalForSubscriptionId(
        DSLContext dslContext,
        String subscriptionId,
        Optional<OrderStatus> orderStatus
    ) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var query = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.EXTERNAL_SUBSCRIPTION_ID.eq(subscriptionId).or(ACCOUNT_ORDER.RENEWAL_FOR_SUBSCRIPTION_ID.eq(subscriptionId)))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse());

        return getOrdersFromQuery(dslContext, orderStatus, query);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private List<Order> getOrdersFromQuery(DSLContext dslContext, Optional<OrderStatus> orderStatus, SelectConditionStep<Record> query) {
        if (orderStatus.isPresent()) {
            // if querying by specific order status, add to where clause. Otherwise, return all statuses are retrieved
            query = query.and(ACCOUNT_ORDER.STATUS.eq(orderStatus.get().toString()));
        }

        var accountOrderRecords = query
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .orderBy(ACCOUNT_ORDER.START_DATE.asc())
            .fetchInto(AccountOrderRecord.class);

        if (CollectionUtils.isEmpty(accountOrderRecords)) {
            return List.of();
        }

        var orders = orderDAOMapper.recordsToOrders(accountOrderRecords);
        return getOrdersWithOrderLineItems(dslContext, orders);
    }

    public List<Order> getOrdersByCrmOpportunityId(DSLContext dslContext, String crmOpportunityId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var accountOrderRecords = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.SFDC_OPPORTUNITY_ID.eq(crmOpportunityId))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .orderBy(ACCOUNT_ORDER.UPDATED_ON.asc())
            .fetchInto(AccountOrderRecord.class);

        if (CollectionUtils.isEmpty(accountOrderRecords)) {
            return List.of();
        }

        var orders = orderDAOMapper.recordsToOrders(accountOrderRecords);
        return getOrdersWithOrderLineItems(dslContext, orders);
    }

    public List<Order> getDeletedOrdersByCrmOpportunityIds(DSLContext dslContext, List<String> crmOpportunityIds) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var accountOrderRecords = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.SFDC_OPPORTUNITY_ID.in(crmOpportunityIds))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isTrue())
            .orderBy(ACCOUNT_ORDER.UPDATED_ON.asc())
            .fetchInto(AccountOrderRecord.class);

        if (CollectionUtils.isEmpty(accountOrderRecords)) {
            return List.of();
        }

        return orderDAOMapper.recordsToOrders(accountOrderRecords);
    }

    public boolean contactUsedByOrder(DSLContext dslContext, String contactId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(ACCOUNT_ORDER)
                .where(ACCOUNT_ORDER.IS_DELETED.isFalse())
                .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
                .and(ACCOUNT_ORDER.BILLING_CONTACT_ID.eq(contactId).or(ACCOUNT_ORDER.SHIPPING_CONTACT_ID.eq(contactId)))
        );
    }

    public boolean accountUsedByOrder(DSLContext dslContext, String accountId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(ACCOUNT_ORDER)
                .where(ACCOUNT_ORDER.ACCOUNT_ID.eq(accountId).and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId)).and(ACCOUNT_ORDER.IS_DELETED.isFalse()))
        );
    }

    public boolean accountUsedByOrderAsReseller(DSLContext dslContext, String accountId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(ACCOUNT_ORDER)
                .where(
                    ACCOUNT_ORDER.BILLING_CONTACT_ID.in(
                        selectDistinct(ACCOUNT_CONTACT.CONTACT_ID)
                            .from(ACCOUNT_CONTACT)
                            .where(ACCOUNT_CONTACT.TENANT_ID.eq(tenantId).and(ACCOUNT_CONTACT.ACCOUNT_ID.eq(accountId)))
                    )
                        .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
                        .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
                )
        );
    }

    public boolean accountUsedByOrderWithOpportunity(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(ACCOUNT_ORDER)
                .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
                .and(ACCOUNT_ORDER.ACCOUNT_ID.eq(accountId))
                .and(ACCOUNT_ORDER.SFDC_OPPORTUNITY_ID.isNotNull())
                .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
        );
    }

    public boolean discountUsedByOrderLineItem(String discountId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(ACCOUNT_ORDER_LINE_ITEM)
                .where(ACCOUNT_ORDER_LINE_ITEM.PREDEFINED_DISCOUNTS.contains(discountId))
                .and(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
                .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
                .limit(1)
        );
    }

    public boolean planUsedByOrderLineItem(DSLContext dslContext, String planId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(ACCOUNT_ORDER_LINE_ITEM)
                .where(ACCOUNT_ORDER_LINE_ITEM.PLAN_ID.eq(planId))
                .and(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
                .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
        );
    }

    @AllowNonRlsDataAccess
    public Set<String> entitiesUsedByOrdersUsingPlan(String planId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get();
        List<String> orderEntityIds = dslContext
            .selectDistinct(ACCOUNT_ORDER.ENTITY_ID)
            .from(ACCOUNT_ORDER)
            .join(ACCOUNT_ORDER_LINE_ITEM)
            .on(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId).and(ACCOUNT_ORDER.ORDER_ID.eq(ACCOUNT_ORDER_LINE_ITEM.ORDER_ID)))
            .where(ACCOUNT_ORDER_LINE_ITEM.PLAN_ID.eq(planId))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .fetch(ACCOUNT_ORDER.ENTITY_ID);
        return Set.copyOf(orderEntityIds);
    }

    @AllowNonRlsDataAccess
    public Set<String> entitiesUsedByOrdersUsingTemplate(UUID templateId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get();
        List<String> orderEntityIds = dslContext
            .selectDistinct(ACCOUNT_ORDER.ENTITY_ID)
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.DOCUMENT_MASTER_TEMPLATE_ID.eq(templateId))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .fetch(ACCOUNT_ORDER.ENTITY_ID);
        return Set.copyOf(orderEntityIds);
    }

    @AllowNonRlsDataAccess
    public Set<String> entitiesUsedByAccount(String accountId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get();
        List<String> orderEntityIds = dslContext
            .selectDistinct(ACCOUNT_ORDER.ENTITY_ID)
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.ACCOUNT_ID.eq(accountId))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .fetch(ACCOUNT_ORDER.ENTITY_ID);
        return Set.copyOf(orderEntityIds);
    }

    private List<Order> getOrdersWithOrderLineItems(DSLContext dslContext, List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return List.of();
        }

        var orderIds = orders.stream().map(Order::getOrderId).collect(Collectors.toList());
        var orderLineItems = getBulkOrderLineItems(dslContext, orderIds);
        var groupedOrderLineItems = orderLineItems.stream().collect(Collectors.groupingBy(OrderLineItem::getOrderId));

        orders.forEach(order -> fillDifferentLineItemsInOrder(order, groupedOrderLineItems.getOrDefault(order.getOrderId(), List.of())));
        return orders;
    }

    private Optional<Order> getOrderWithOrderLineItems(DSLContext dslContext, Order order) {
        List<OrderLineItem> orderLineItems = getOrderLineItemsByOrderId(dslContext, order.getOrderId());
        fillDifferentLineItemsInOrder(order, orderLineItems);
        return Optional.of(order);
    }

    public List<OrderLineItem> getOrderLineItemsByOrderId(DSLContext dslContext, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        List<AccountOrderLineItemRecord> accountOrderLineItemRecords = dslContext
            .select()
            .from(ACCOUNT_ORDER_LINE_ITEM)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .orderBy(ACCOUNT_ORDER_LINE_ITEM.CHARGE_ID, ACCOUNT_ORDER_LINE_ITEM.EFFECTIVE_DATE.asc())
            .fetchInto(AccountOrderLineItemRecord.class);

        List<OrderLineItem> orderLineItems = orderDAOMapper.recordsToOrderLineItems(accountOrderLineItemRecords);
        if (CollectionUtils.isEmpty(orderLineItems)) {
            LOGGER.info("orderLineItems are empty for order id = {}", orderId);
            // TODO: Should an error be thrown if there are no order line items for an order?
            return List.of();
        }
        return orderLineItems;
    }

    private static void fillDifferentLineItemsInOrder(Order order, List<OrderLineItem> orderLineItems) {
        List<OrderLineItem> netEffectLines = orderLineItems
            .stream()
            .filter(ol -> ol.getAction() != ActionType.NONE && ol.getAction() != ActionType.MISSING_RENEWAL)
            .collect(Collectors.toList());
        order.setLineItemsNetEffect(netEffectLines);

        List<OrderLineItem> missingRenewalLines = orderLineItems.stream().filter(ol -> ol.getAction() == ActionType.MISSING_RENEWAL).toList();
        order.setMissingRenewalOrderLineItems(missingRenewalLines);

        List<OrderLineItem> mainOrderLines = orderLineItems.stream().filter(ol -> ol.getAction() != ActionType.MISSING_RENEWAL).toList();
        order.setLineItems(mainOrderLines);
    }

    private List<OrderLineItem> getBulkOrderLineItems(DSLContext dslContext, List<String> orderIds) {
        var tenantId = tenantIdProvider.provideTenantIdString();

        var accountOrderLineItemRecords = dslContext
            .select()
            .from(ACCOUNT_ORDER_LINE_ITEM)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.ORDER_ID.in(orderIds))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .orderBy(ACCOUNT_ORDER_LINE_ITEM.ORDER_ID, ACCOUNT_ORDER_LINE_ITEM.CHARGE_ID, ACCOUNT_ORDER_LINE_ITEM.EFFECTIVE_DATE.asc())
            .fetchInto(AccountOrderLineItemRecord.class);

        return orderDAOMapper.recordsToOrderLineItems(accountOrderLineItemRecords);
    }

    public PageResult<List<OrderStub>, Instant> getOrderStubs(PageRequest<Instant> pageRequest) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var query = dslContext
            .selectFrom(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .orderBy(ACCOUNT_ORDER.CREATED_ON);

        List<AccountOrderRecord> records;
        if (pageRequest.getPageToken() != null) {
            records = query.seek(DateTimeConverter.instantToLocalDateTime(pageRequest.getPageToken())).limit(pageRequest.getLimit()).fetch();
        } else {
            records = query.limit(pageRequest.getLimit()).fetch();
        }
        return PageResult.fromCollectionAndRequest(orderDAOMapper.recordsToOrderStubs(records), pageRequest, OrderStub::getCreatedOn);
    }

    public Optional<OrderStub> getOrderStub(String orderId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var accountOrderRecord = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.ORDER_ID.eq(orderId).and(ACCOUNT_ORDER.IS_DELETED.isFalse()))
            .fetchOneInto(AccountOrderRecord.class);

        return Optional.ofNullable(orderDAOMapper.recordToOrderStub(accountOrderRecord));
    }

    public boolean orderExists(String orderId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        return dslContext.fetchExists(
            dslContext
                .selectFrom(ACCOUNT_ORDER)
                .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
                .and(ACCOUNT_ORDER.ORDER_ID.eq(orderId).and(ACCOUNT_ORDER.IS_DELETED.eq(false)))
        );
    }

    public boolean orderExistsByEntityId(String entityId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext.fetchExists(
            dslContext
                .selectFrom(ACCOUNT_ORDER)
                .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
                .and(ACCOUNT_ORDER.ENTITY_ID.eq(entityId))
                .and(ACCOUNT_ORDER.IS_DELETED.eq(false))
        );
    }

    public Optional<Order> getOrderByOrderId(DSLContext dslContext, String orderId) {
        var accountOrderRecord = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.ORDER_ID.eq(orderId).and(ACCOUNT_ORDER.IS_DELETED.isFalse()))
            .fetchOneInto(AccountOrderRecord.class);

        if (accountOrderRecord == null) {
            return Optional.empty();
        }

        Order order = orderDAOMapper.recordToOrder(accountOrderRecord);
        return getOrderWithOrderLineItems(dslContext, order);
    }

    public Optional<Order> getOrderByExternalId(DSLContext dslContext, String externalId) {
        var accountOrderRecord = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.EXTERNAL_ID.eq(externalId).and(ACCOUNT_ORDER.IS_DELETED.isFalse()))
            .fetchOneInto(AccountOrderRecord.class);

        if (accountOrderRecord == null) {
            return Optional.empty();
        }

        Order order = orderDAOMapper.recordToOrder(accountOrderRecord);
        return getOrderWithOrderLineItems(dslContext, order);
    }

    public List<OrderLineItem> getOrderLineItems(DSLContext dslContext, List<String> orderLineItems) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var orderLineItemRecords = dslContext
            .selectFrom(ACCOUNT_ORDER_LINE_ITEM)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.ORDER_LINE_ID.in(orderLineItems))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .fetchInto(AccountOrderLineItemRecord.class);

        return orderDAOMapper.recordsToOrderLineItems(orderLineItemRecords);
    }

    public Optional<OrderLineItem> getOrderLineItemByOrderLineId(DSLContext dslContext, String id) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var lineItemRecord = dslContext
            .select()
            .from(ACCOUNT_ORDER_LINE_ITEM)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.ORDER_LINE_ID.eq(id))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .fetchOneInto(AccountOrderLineItemRecord.class);

        return Optional.ofNullable(orderDAOMapper.recordToOrderLineItem(lineItemRecord));
    }

    public List<OrderLineItem> getOrderLineItemsByRampGroupId(UUID rampGroupId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var lineItemRecords = dslContext
            .select()
            .from(ACCOUNT_ORDER_LINE_ITEM)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.RAMP_GROUP_ID.eq(rampGroupId))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .fetchInto(AccountOrderLineItemRecord.class);

        return orderDAOMapper.recordsToOrderLineItems(lineItemRecords);
    }

    private List<Order> aggregateOrdersByDate(List<Order> ordersList, ZoneId timeZoneId) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT_FOR_MOMENT).withZone(timeZoneId);

        Map<String, Order> aggregatedOrders = new HashMap<>();
        ordersList.forEach(order -> {
            String orderDate = dateTimeFormatter.format(order.getExecutedOn());
            order.setExecutedOnFormatted(orderDate);
            if (aggregatedOrders.containsKey(orderDate)) {
                aggregatedOrders.get(orderDate).setTotalAmount(aggregatedOrders.get(orderDate).getTotalAmount().add(order.getTotalAmount()));
            } else {
                aggregatedOrders.put(orderDate, order);
            }
        });

        return new ArrayList<>(aggregatedOrders.values());
    }

    public int updateSubscriptionTargetVersion(String orderId, int subscriptionTargetVersion) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        return dslContext
            .update(ACCOUNT_ORDER)
            .set(ACCOUNT_ORDER.SUBSCRIPTION_TARGET_VERSION, subscriptionTargetVersion)
            .where(ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.eq(false))
            .and(
                ACCOUNT_ORDER.STATUS.ne(OrderStatus.EXECUTED.toString()).and(ACCOUNT_ORDER.SUBSCRIPTION_TARGET_VERSION.lt(subscriptionTargetVersion))
            )
            .execute();
    }

    public Map<String, List<Order>> getOrdersOverviewInPeriod(Period period, ZoneId timeZoneId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        LocalDateTime periodStartDate = period.getStart().atZone(ZoneOffset.UTC).toLocalDateTime();
        LocalDateTime periodEndDate = period.getEnd().atZone(ZoneOffset.UTC).toLocalDateTime();

        var accountOrders = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .and(ACCOUNT_ORDER.EXECUTED_ON.between(periodStartDate, periodEndDate))
            .fetchGroups(ACCOUNT_ORDER.CURRENCY, AccountOrderRecord.class);

        Map<String, List<Order>> orders = new HashMap<>();
        accountOrders.forEach((currency, accountOrderRecords) -> {
            List<Order> ordersList = orderDAOMapper.recordsToOrders(accountOrderRecords);
            // TODO: do we need line items for bookings reports?
            getOrdersWithOrderLineItems(dslContext, ordersList);

            List<Order> aggregatedOrdersList = aggregateOrdersByDate(ordersList, timeZoneId);

            orders.put(currency, aggregatedOrdersList);
        });

        return orders;
    }

    public List<Order> getOrdersByOrderIds(List<String> orderIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<AccountOrderRecord> accountOrderRecords = getOrdersByOrderIdsInternal(dslContext, orderIds);
        List<Order> orders = orderDAOMapper.recordsToOrders(accountOrderRecords);

        return getOrdersWithOrderLineItems(dslContext, orders);
    }

    private List<AccountOrderRecord> getOrdersByOrderIdsInternal(DSLContext dslContext, List<String> orderIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        return dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .and(ACCOUNT_ORDER.ORDER_ID.in(orderIds))
            .fetchInto(AccountOrderRecord.class);
    }

    public Order updateOrder(DSLContext dslContext, Order order) {
        var originalOrderOptional = getOrderByOrderId(dslContext, order.getOrderId());
        if (originalOrderOptional.isEmpty()) {
            var message = String.format("order with id %s doesn't exist", order.getId());
            LOGGER.error(message);
            throw new IllegalArgumentException(message);
        }
        var originalOrder = originalOrderOptional.get();
        order.setCreatedBy(originalOrder.getCreatedBy());

        var localTime = LocalDateTime.now(ZoneOffset.UTC);

        updateOrderRecord(dslContext, order, localTime);

        var originalOrderLineIds = originalOrder.getLineItems().stream().map(OrderLineItem::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(originalOrder.getMissingRenewalOrderLineItems())) {
            originalOrderLineIds.addAll(originalOrder.getMissingRenewalOrderLineItems().stream().map(OrderLineItem::getId).toList());
        }

        var updateOrderLineIds = order.getLineItems().stream().map(OrderLineItem::getId).collect(Collectors.toSet());
        var lineIdsToAdd = Sets.difference(updateOrderLineIds, originalOrderLineIds);
        var lineIdsToUpdate = Sets.intersection(originalOrderLineIds, updateOrderLineIds);
        var lineIdsToDelete = Sets.difference(originalOrderLineIds, updateOrderLineIds);

        var newLines = order.getLineItems().stream().filter(li -> lineIdsToAdd.contains(li.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(order.getMissingRenewalOrderLineItems())) {
            newLines.addAll(order.getMissingRenewalOrderLineItems());
        }

        if (CollectionUtils.isNotEmpty(newLines)) {
            bulkInsertOrderLines(dslContext, newLines, localTime);
        }

        var updateLineItemsList = order.getLineItems().stream().filter(li -> lineIdsToUpdate.contains(li.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateLineItemsList)) {
            updateBulkOrderLineItems(dslContext, updateLineItemsList, localTime);
        }

        if (CollectionUtils.isNotEmpty(lineIdsToDelete)) {
            bulkDeleteLineItems(dslContext, lineIdsToDelete);
        }

        var result = getOrderByOrderId(dslContext, order.getOrderId());
        if (result.isEmpty()) {
            var message = String.format("order with order id %s not found", order.getOrderId());
            LOGGER.error(message);
            throw new IllegalStateException(message);
        }
        return result.get();
    }

    private void updateOrderRecord(DSLContext dslContext, Order order, LocalDateTime localTime) {
        var updateOrder = orderDAOMapper.orderToRecord(order);
        updateOrder.reset(ACCOUNT_ORDER.CREATED_ON);
        updateOrder.setUpdatedOn(localTime);
        updateOrder.setMetrics(RECOMPUTE_METRICS_JSONB);
        updateOrder.setShouldRegeneratePdf(order.getStatus() != OrderStatus.EXECUTED || order.getShouldRegeneratePdf());
        if (EvergreenUtils.isEvergreenOrder(order, featureService)) {
            updateOrder.setSubscriptionDurationModel(SubscriptionDurationModel.EVERGREEN.name());
            updateOrder.setEndDate(DateTimeConverter.instantToLocalDateTime(EvergreenUtils.EVERGREEN_SENTINEL_END_DATE));
        }
        PostgresErrorHandler.withConstraintAsConflict(
            () -> dslContext.executeUpdate(updateOrder),
            UNIQUE_EXTERNAL_ID_CONSTRAINT_NAME,
            String.format(ORDER_EXISTS_WITH_EXTERNAL_ID, order.getExternalId())
        );
    }

    private void updateBulkOrderLineItems(DSLContext dslContext, List<OrderLineItem> orderLineItem, LocalDateTime localTime) {
        var lineItemRecords = orderDAOMapper.orderLineItemsToRecords(orderLineItem);
        lineItemRecords.forEach(li -> {
            li.setUpdatedOn(localTime);
            li.reset(ACCOUNT_ORDER_LINE_ITEM.CREATED_ON);
        });
        dslContext.batchUpdate(lineItemRecords).execute();
    }

    private void bulkDeleteLineItems(DSLContext dslContext, Set<UUID> listToDelete) {
        dslContext
            .update(ACCOUNT_ORDER_LINE_ITEM)
            .set(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED, true)
            .where(ACCOUNT_ORDER_LINE_ITEM.ID.in(listToDelete).and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse()))
            .and(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .execute();
    }

    public void deleteOrder(DSLContext dslContext, String orderId) {
        dslContext
            .update(ACCOUNT_ORDER_LINE_ITEM)
            .set(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED, true)
            .where(ACCOUNT_ORDER_LINE_ITEM.ORDER_ID.eq(orderId).and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse()))
            .and(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .execute();
        dslContext.update(ACCOUNT_ORDER).set(ACCOUNT_ORDER.IS_DELETED, true).where(ACCOUNT_ORDER.ORDER_ID.eq(orderId)).execute();
    }

    public void ensureUniqueOrderId(DSLContext dslContext, String orderId) {
        var record = dslContext.select().from(ACCOUNT_ORDER).where(ACCOUNT_ORDER.ORDER_ID.eq(orderId)).fetchOneInto(AccountOrderRecord.class);

        if (record == null) {
            return;
        }

        throwDuplicateOrderIdException(orderId);
    }

    public List<String> getOrderlineIdsForMetricsUpdateLocked(DSLContext txnDslContext, String tenantId, int limit) {
        return txnDslContext
            .select(ACCOUNT_ORDER_LINE_ITEM.ORDER_LINE_ID)
            .from(ACCOUNT_ORDER_LINE_ITEM)
            .where("(metrics->>'recompute')::boolean = true")
            .and(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .limit(limit)
            .forUpdate()
            .skipLocked()
            .fetch(ACCOUNT_ORDER_LINE_ITEM.ORDER_LINE_ID);
    }

    public void updateOrderLineMetrics(DSLContext txnDslContext, String tenantId, String orderLineId, JSONB metrics) {
        txnDslContext
            .update(ACCOUNT_ORDER_LINE_ITEM)
            .set(ACCOUNT_ORDER_LINE_ITEM.METRICS, metrics)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.ORDER_LINE_ID.eq(orderLineId))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .execute();
    }

    public List<String> getExecutedOrderIdsForMetricsUpdateLocked(DSLContext txnDslContext, String tenantId, int limit) {
        return txnDslContext
            .select(ACCOUNT_ORDER.ORDER_ID)
            .from(ACCOUNT_ORDER)
            .where("(metrics->>'recompute')::boolean = true")
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .and(ACCOUNT_ORDER.STATUS.eq(OrderStatus.EXECUTED.toString()))
            .limit(limit)
            .forUpdate()
            .skipLocked()
            .fetch(ACCOUNT_ORDER.ORDER_ID);
    }

    public List<String> getNonExecutedOrderIdsForMetricsUpdate(String tenantId, int limit) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext
            .select(ACCOUNT_ORDER.ORDER_ID)
            .from(ACCOUNT_ORDER)
            .where("(metrics->>'recompute')::boolean = true")
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .and(ACCOUNT_ORDER.STATUS.ne(OrderStatus.EXECUTED.toString()))
            .limit(limit)
            .fetch(ACCOUNT_ORDER.ORDER_ID);
    }

    public void updateOrderMetrics(DSLContext txnDslContext, String tenantId, String orderId, JSONB metrics) {
        txnDslContext
            .update(ACCOUNT_ORDER)
            .set(ACCOUNT_ORDER.METRICS, metrics)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    public void updateOrderMetricsIfCurrent(Order order, JSONB metrics) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(order.getTenantId(), dslContextProvider);
        int rowsUpdated = dslContext
            .update(ACCOUNT_ORDER)
            .set(ACCOUNT_ORDER.METRICS, metrics)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(order.getTenantId()))
            .and(ACCOUNT_ORDER.ORDER_ID.eq(order.getOrderId()))
            .and(ACCOUNT_ORDER.UPDATED_ON.eq(DateTimeConverter.instantToLocalDateTime(order.getUpdatedOn())))
            .execute();
        if (rowsUpdated == 0) {
            LOGGER.info("Metrics not updated for order {} since it changed since we retrieved it", order.getOrderId());
        }
    }

    private void throwDuplicateOrderIdException(String orderId) {
        var message = "Duplicated OrderId generated. OrderId = " + orderId;
        LOGGER.info(message);
        throw new DuplicateIdException(message);
    }

    public OrderComment addComment(OrderComment orderComment) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        AccountOrderCommentRecord commentRecord = orderDAOMapper.toCommentRecord(orderComment);
        LocalDateTime currentTime = DateTimeConverter.instantToLocalDateTime(Instant.now());
        updateTenantAndPostedDateOnComment(tenantId, currentTime, commentRecord);

        AccountOrderCommentRecord savedRecord = dslContext.insertInto(ACCOUNT_ORDER_COMMENT).set(commentRecord).returning().fetchOne();
        return orderDAOMapper.toOrderComment(savedRecord);
    }

    public void bulkInsertComments(DSLContext dslContext, List<OrderComment> comments) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        List<AccountOrderCommentRecord> commentRecords = orderDAOMapper.toCommentRecords(comments);
        LocalDateTime currentTime = DateTimeConverter.instantToLocalDateTime(Instant.now());
        commentRecords.forEach(commentRecord -> updateTenantAndPostedDateOnComment(tenantId, currentTime, commentRecord));
        dslContext.batchInsert(commentRecords).execute();
    }

    private void updateTenantAndPostedDateOnComment(String tenantId, LocalDateTime currentTime, AccountOrderCommentRecord commentRecord) {
        commentRecord.setTenantId(tenantId);
        commentRecord.setPostedOn(currentTime);
    }

    public List<OrderComment> getCommentsOnOrder(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<AccountOrderCommentRecord> comments = dslContext
            .select()
            .from(ACCOUNT_ORDER_COMMENT)
            .where(ACCOUNT_ORDER_COMMENT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_COMMENT.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER_COMMENT.IS_DELETED.isFalse())
            .fetchInto(AccountOrderCommentRecord.class);
        return orderDAOMapper.toOrderComments(comments);
    }

    public OrderComment getCommentByCommentId(String commentId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        AccountOrderCommentRecord comment = dslContext
            .select()
            .from(ACCOUNT_ORDER_COMMENT)
            .where(ACCOUNT_ORDER_COMMENT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_COMMENT.COMMENT_ID.eq(commentId))
            .and(ACCOUNT_ORDER_COMMENT.IS_DELETED.isFalse())
            .fetchOneInto(AccountOrderCommentRecord.class);
        return orderDAOMapper.toOrderComment(comment);
    }

    public void deleteCommentsOnOrder(DSLContext dslContext, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        dslContext
            .update(ACCOUNT_ORDER_COMMENT)
            .set(ACCOUNT_ORDER_COMMENT.IS_DELETED, true)
            .where(ACCOUNT_ORDER_COMMENT.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_COMMENT.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER_COMMENT.IS_DELETED.isFalse())
            .execute();
    }

    public boolean isMasterTemplateInUse(UUID masterTemplateId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<AccountOrderRecord> accountOrders = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .and(ACCOUNT_ORDER.DOCUMENT_MASTER_TEMPLATE_ID.eq(masterTemplateId))
            .limit(1)
            .fetchInto(AccountOrderRecord.class);

        return !accountOrders.isEmpty();
    }

    public boolean attachmentUsedByOrder(String attachmentId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(ACCOUNT_ORDER)
                .where(ACCOUNT_ORDER.ATTACHMENT_ID.eq(UUID.fromString(attachmentId)))
                .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
                .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
        );
    }

    public Map<String, List<String>> getOrderIdsAssociatedWithAttachments(List<UUID> attachmentIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        Map<UUID, List<String>> orderIdsMap = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.ATTACHMENT_ID.in(attachmentIds).and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId)).and(ACCOUNT_ORDER.IS_DELETED.isFalse()))
            .fetchGroups(ACCOUNT_ORDER.ATTACHMENT_ID, ACCOUNT_ORDER.ORDER_ID);

        return orderIdsMap.entrySet().stream().collect(Collectors.toMap(e -> e.getKey().toString(), Map.Entry::getValue));
    }

    public List<String> getOrderIdsInCompositeOrder(String compositeOrderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        return dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.COMPOSITE_ORDER_ID.eq(compositeOrderId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .fetch(ACCOUNT_ORDER.ORDER_ID);
    }

    public void setShouldRegeneratePdf(String orderId, Boolean shouldRegeneratePdf) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        setShouldRegeneratePdfInTransaction(dslContext, tenantId, orderId, shouldRegeneratePdf);
    }

    public void setShouldRegeneratePdfInTransaction(DSLContext dslContext, String orderId, Boolean shouldRegeneratePdf) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        setShouldRegeneratePdfInTransaction(dslContext, tenantId, orderId, shouldRegeneratePdf);
    }

    private void setShouldRegeneratePdfInTransaction(DSLContext dslContext, String tenantId, String orderId, Boolean shouldRegeneratePdf) {
        dslContext
            .update(ACCOUNT_ORDER)
            .set(ACCOUNT_ORDER.SHOULD_REGENERATE_PDF, shouldRegeneratePdf)
            .where(ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .execute();
    }

    public List<Order> getOrdersByBillingContacts(List<String> contactIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<AccountOrderRecord> accountOrders = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.BILLING_CONTACT_ID.in(contactIds))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .fetchInto(AccountOrderRecord.class);

        List<Order> orders = orderDAOMapper.recordsToOrders(accountOrders);
        return getOrdersWithOrderLineItems(dslContext, orders);
    }

    public List<Order> getExpirableOrders(Instant currentDateTime) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        LocalDateTime localDateTime = DateTimeConverter.instantToLocalDateTime(currentDateTime);

        List<AccountOrderRecord> accountOrders = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.STATUS.eq(OrderStatus.DRAFT.toString()))
            .and(ACCOUNT_ORDER.EXPIRES_ON.lessThan(localDateTime))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .fetchInto(AccountOrderRecord.class);

        return orderDAOMapper.recordsToOrders(accountOrders);
    }

    public List<OrderLineItem> getExecutedOrderLinesWithSameBaseSubscriptionChargeId(String baseSubscriptionChargeId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<AccountOrderLineItemRecord> orderLineRecords = dslContext
            .select()
            .from(ACCOUNT_ORDER_LINE_ITEM)
            .join(ACCOUNT_ORDER)
            .on(ACCOUNT_ORDER.ORDER_ID.eq(ACCOUNT_ORDER_LINE_ITEM.ORDER_ID).and(ACCOUNT_ORDER.TENANT_ID.eq(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID)))
            .where(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .and(ACCOUNT_ORDER.STATUS.eq(OrderStatus.EXECUTED.name()))
            .and(ACCOUNT_ORDER_LINE_ITEM.BASE_EXTERNAL_SUBSCRIPTION_CHARGE_ID.eq(baseSubscriptionChargeId))
            .and(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .fetchInto(AccountOrderLineItemRecord.class);

        return orderDAOMapper.recordsToOrderLineItems(orderLineRecords);
    }

    public List<OrderLineItem> getRenewalOrderLinesWithMissingBaseSubscriptionCharge() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        List<AccountOrderLineItemRecord> records = dslContext
            .select()
            .from(ACCOUNT_ORDER_LINE_ITEM)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.ACTION.eq(ActionType.RENEWAL.name()))
            .and(ACCOUNT_ORDER_LINE_ITEM.BASE_EXTERNAL_SUBSCRIPTION_CHARGE_ID.isNull())
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .fetchInto(AccountOrderLineItemRecord.class);

        return orderDAOMapper.recordsToOrderLineItems(records);
    }

    public void updateBaseSubscriptionChargeIdsForOrderLines(Map<String, String> orderLineSubscriptionChargeIdMap) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        tenantDslContext.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            orderLineSubscriptionChargeIdMap.forEach((key, value) ->
                dslContext
                    .update(ACCOUNT_ORDER_LINE_ITEM)
                    .set(ACCOUNT_ORDER_LINE_ITEM.BASE_EXTERNAL_SUBSCRIPTION_CHARGE_ID, value)
                    .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
                    .and(ACCOUNT_ORDER_LINE_ITEM.ORDER_LINE_ID.eq(key))
                    .and(ACCOUNT_ORDER_LINE_ITEM.BASE_EXTERNAL_SUBSCRIPTION_CHARGE_ID.isNull())
                    .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
                    .execute()
            );
        });
    }

    public void updateOrderExecutedOnAndCrmIdOnOrder(DSLContext dslContext, String orderId, String crmId, Instant executedOn) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        LocalDateTime executeOnLocal = DateTimeConverter.instantToLocalDateTime(executedOn);

        dslContext
            .update(ACCOUNT_ORDER)
            .set(ACCOUNT_ORDER.EXECUTED_ON, executeOnLocal)
            .set(ACCOUNT_ORDER.SFDC_OPPORTUNITY_ID, crmId)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    public void updateCrmIdOnOrder(DSLContext dslContext, String orderId, String crmId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .update(ACCOUNT_ORDER)
            .set(ACCOUNT_ORDER.SFDC_OPPORTUNITY_ID, crmId)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    public void updateOrderContacts(String orderId, String billingContactId, String shippingContactId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext
            .update(ACCOUNT_ORDER)
            .set(ACCOUNT_ORDER.BILLING_CONTACT_ID, billingContactId)
            .set(ACCOUNT_ORDER.SHIPPING_CONTACT_ID, shippingContactId)
            .set(ACCOUNT_ORDER.SHOULD_REGENERATE_PDF, true)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER.STATUS.ne(OrderStatus.EXECUTED.toString()))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    //todo: cleanup after backfill for ramp group id
    public List<String> getOrderIdsWithoutRampGroupId() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext
            .selectDistinct(ACCOUNT_ORDER_LINE_ITEM.ORDER_ID)
            .from(ACCOUNT_ORDER_LINE_ITEM)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_RAMP.isTrue())
            .and(ACCOUNT_ORDER_LINE_ITEM.RAMP_GROUP_ID.isNull())
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .fetch(ACCOUNT_ORDER_LINE_ITEM.ORDER_ID);
    }

    public void updateOrderLineItemRampGroupIdInTransaction(DSLContext dslContext, OrderLineItem lineItem) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        dslContext
            .update(ACCOUNT_ORDER_LINE_ITEM)
            .set(ACCOUNT_ORDER_LINE_ITEM.RAMP_GROUP_ID, lineItem.getRampGroupId())
            .where(ACCOUNT_ORDER_LINE_ITEM.ORDER_LINE_ID.eq(lineItem.getOrderLineId()))
            .and(ACCOUNT_ORDER_LINE_ITEM.RAMP_GROUP_ID.isNull())
            .and(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .execute();
    }

    public CustomBillingSchedule saveCustomBillingSchedule(DSLContext dslContext, Order order, CustomBillingSchedule customBillingSchedule) {
        String tenantId = order.getTenantId();
        String orderId = order.getOrderId();
        OrderCustomBillingScheduleRecord record = customBillingScheduleRecordMapper.toRecord(customBillingSchedule);
        record.reset(ORDER_CUSTOM_BILLING_SCHEDULE.ID);
        record.reset(ORDER_CUSTOM_BILLING_SCHEDULE.IS_DELETED);
        record.setTenantId(tenantId);
        record.setOrderId(orderId);
        invalidateExistingCustomBillingSchedule(dslContext, tenantId, orderId);
        OrderCustomBillingScheduleRecord savedRecord = dslContext.insertInto(ORDER_CUSTOM_BILLING_SCHEDULE).set(record).returning().fetchOne();
        return customBillingScheduleRecordMapper.fromRecord(savedRecord);
    }

    /**
     * This method is important to invoke to invalidate existing custom billing schedules when the order is getting saved so we don't have any dangling schedules
     * @param dslContext
     * @param tenantId
     * @param orderId
     */
    public void invalidateExistingCustomBillingSchedule(DSLContext dslContext, String tenantId, String orderId) {
        int deletedRecordsCount = dslContext
            .update(ORDER_CUSTOM_BILLING_SCHEDULE)
            .set(ORDER_CUSTOM_BILLING_SCHEDULE.IS_DELETED, true)
            .where(ORDER_CUSTOM_BILLING_SCHEDULE.ORDER_ID.eq(orderId))
            .and(ORDER_CUSTOM_BILLING_SCHEDULE.TENANT_ID.eq(tenantId))
            .and(ORDER_CUSTOM_BILLING_SCHEDULE.IS_DELETED.isFalse())
            .execute();
        LOGGER.info("Deleted {} existing custom billing schedules for order id: {}", deletedRecordsCount, orderId);
    }

    public int getCountOfCrmPrimaryOrdersUpdatedAfterSpecificDate(Instant updateInstant) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        LocalDateTime localDateTime = DateTimeConverter.instantToLocalDateTime(updateInstant);

        return dslContext
            .selectCount()
            .from(ACCOUNT_ORDER)
            .join(OPPORTUNITY)
            .on(OPPORTUNITY.TENANT_ID.eq(ACCOUNT_ORDER.TENANT_ID).and(OPPORTUNITY.CRM_ID.eq(ACCOUNT_ORDER.SFDC_OPPORTUNITY_ID)))
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .and(ACCOUNT_ORDER.SFDC_OPPORTUNITY_ID.isNotNull())
            .and(ACCOUNT_ORDER.UPDATED_ON.ge(localDateTime))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .execute();
    }

    public List<Order> getOrdersToSyncToCrmUpdatedAfterSpecificDate(String accountId, Instant updateInstant) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        LocalDateTime localDateTime = DateTimeConverter.instantToLocalDateTime(updateInstant);

        List<AccountOrderRecord> orders = dslContext
            .select()
            .from(ACCOUNT_ORDER)
            .join(OPPORTUNITY)
            .on(OPPORTUNITY.TENANT_ID.eq(ACCOUNT_ORDER.TENANT_ID).and(OPPORTUNITY.CRM_ID.eq(ACCOUNT_ORDER.SFDC_OPPORTUNITY_ID)))
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.ACCOUNT_ID.eq(accountId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .and(ACCOUNT_ORDER.SFDC_OPPORTUNITY_ID.isNotNull())
            .and(ACCOUNT_ORDER.UPDATED_ON.ge(localDateTime))
            .and(OPPORTUNITY.IS_DELETED.isFalse())
            .fetchInto(AccountOrderRecord.class);

        return orderDAOMapper.recordsToOrders(orders);
    }

    public List<CustomBillingSchedule> getCustomBillingSchedules(List<String> orderIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        List<OrderCustomBillingScheduleRecord> records = dslContext
            .select()
            .from(ORDER_CUSTOM_BILLING_SCHEDULE)
            .where(ORDER_CUSTOM_BILLING_SCHEDULE.ORDER_ID.in(orderIds))
            .and(ORDER_CUSTOM_BILLING_SCHEDULE.TENANT_ID.eq(tenantId))
            .and(ORDER_CUSTOM_BILLING_SCHEDULE.IS_DELETED.isFalse())
            .fetchInto(OrderCustomBillingScheduleRecord.class);
        return customBillingScheduleRecordMapper.fromRecords(records);
    }

    public Optional<Instant> getLatestOrderLineEndDate() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        return dslContext
            .select(ACCOUNT_ORDER_LINE_ITEM.END_DATE)
            .from(ACCOUNT_ORDER_LINE_ITEM)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.isFalse())
            .orderBy(ACCOUNT_ORDER_LINE_ITEM.END_DATE.desc())
            .limit(1)
            .fetchOptionalInto(Instant.class);
    }

    public void updateOrderAttributes(String orderId, UpdateOrderAttributes attributes, boolean shouldRegeneratePdf) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext.update(ACCOUNT_ORDER).set(ACCOUNT_ORDER.IS_DELETED, false);
        if (shouldRegeneratePdf) {
            query = query.set(ACCOUNT_ORDER.SHOULD_REGENERATE_PDF, true);
        }

        if (attributes.getName().isPresent()) {
            query = query.set(ACCOUNT_ORDER.NAME, attributes.getName().get());
        }

        if (attributes.getCrmOpportunityId().isPresent()) {
            query = query.set(ACCOUNT_ORDER.SFDC_OPPORTUNITY_ID, attributes.getCrmOpportunityId().get());
        }

        if (attributes.getShippingContactId().isPresent()) {
            query = query.set(ACCOUNT_ORDER.SHIPPING_CONTACT_ID, attributes.getShippingContactId().get());
        }

        if (attributes.getBillingContactId().isPresent()) {
            query = query.set(ACCOUNT_ORDER.BILLING_CONTACT_ID, attributes.getBillingContactId().get());
        }

        if (attributes.getPurchaseOrderNumber().isPresent()) {
            query = query.set(ACCOUNT_ORDER.PURCHASE_ORDER_NUMBER, attributes.getPurchaseOrderNumber().get());
        }

        int count = query
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
        if (count == 0) {
            throw new IllegalStateException("Order not found");
        }
    }

    public Optional<Order> getFirstOrderInSubscription(String subscriptionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        Optional<AccountOrderRecord> orderRecord = dslContext
            .selectFrom(ACCOUNT_ORDER)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.STATUS.eq(OrderStatus.EXECUTED.toString()))
            .and(ACCOUNT_ORDER.EXTERNAL_SUBSCRIPTION_ID.eq(subscriptionId))
            .and(ACCOUNT_ORDER.SUBSCRIPTION_TARGET_VERSION.eq(1))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .fetchOptionalInto(AccountOrderRecord.class);

        return orderRecord.map(orderDAOMapper::recordToOrder);
    }

    public int updateOrderRenewedFromSubscriptionId(
        DSLContext context,
        String orderId,
        String currentRenewedFromSubscriptionId,
        String updatedRenewedFromSubscriptionId,
        int updatedRenewedFromSubscriptionVersion
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return context
            .update(ACCOUNT_ORDER)
            .set(ACCOUNT_ORDER.RENEWAL_FOR_SUBSCRIPTION_ID, updatedRenewedFromSubscriptionId)
            .set(ACCOUNT_ORDER.RENEWAL_FOR_SUBSCRIPTION_VERSION, updatedRenewedFromSubscriptionVersion)
            .set(ACCOUNT_ORDER.METRICS, RECOMPUTE_METRICS_JSONB)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER.RENEWAL_FOR_SUBSCRIPTION_ID.eq(currentRenewedFromSubscriptionId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    public int setOrderTypeToNew(DSLContext context, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        return context
            .update(ACCOUNT_ORDER)
            .setNull(ACCOUNT_ORDER.RENEWAL_FOR_SUBSCRIPTION_ID)
            .setNull(ACCOUNT_ORDER.RENEWAL_FOR_SUBSCRIPTION_VERSION)
            .setNull(ACCOUNT_ORDER.RESTRUCTURE_FOR_SUBSCRIPTION_ID)
            .setNull(ACCOUNT_ORDER.RESTRUCTURE_FOR_SUBSCRIPTION_VERSION)
            .set(ACCOUNT_ORDER.METRICS, RECOMPUTE_METRICS_JSONB)
            .set(ACCOUNT_ORDER.ORDER_TYPE, OrderType.NEW.name())
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    public int setOrderLineItemsToAdd(DSLContext context, String orderId, Set<ActionType> actionTypesToUpdate) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return context
            .update(ACCOUNT_ORDER_LINE_ITEM)
            .set(ACCOUNT_ORDER_LINE_ITEM.ACTION, ActionType.ADD.name())
            .setNull(ACCOUNT_ORDER_LINE_ITEM.BASE_EXTERNAL_SUBSCRIPTION_CHARGE_ID)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.eq(false))
            .and(ACCOUNT_ORDER_LINE_ITEM.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER_LINE_ITEM.ACTION.in(actionTypesToUpdate))
            .execute();
    }

    public int setOrderLineItemToAdd(DSLContext context, UUID id) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return context
            .update(ACCOUNT_ORDER_LINE_ITEM)
            .set(ACCOUNT_ORDER_LINE_ITEM.ACTION, ActionType.ADD.name())
            .set(ACCOUNT_ORDER_LINE_ITEM.METRICS, RECOMPUTE_METRICS_JSONB)
            .setNull(ACCOUNT_ORDER_LINE_ITEM.BASE_EXTERNAL_SUBSCRIPTION_CHARGE_ID)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.eq(false))
            .and(ACCOUNT_ORDER_LINE_ITEM.ID.eq(id))
            .execute();
    }

    public void removeCompositeOrderAssociation(DSLContext context, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        context
            .update(ACCOUNT_ORDER)
            .setNull(ACCOUNT_ORDER.COMPOSITE_ORDER_ID)
            .set(ACCOUNT_ORDER.METRICS, RECOMPUTE_METRICS_JSONB)
            .where(ACCOUNT_ORDER.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER.ORDER_ID.eq(orderId))
            .and(ACCOUNT_ORDER.IS_DELETED.isFalse())
            .execute();
    }

    public int updateOrderLineItemBaseExternalSubscriptionChargeId(DSLContext context, UUID id, String baseExternalSubscriptionChargeId) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        return context
            .update(ACCOUNT_ORDER_LINE_ITEM)
            .set(ACCOUNT_ORDER_LINE_ITEM.BASE_EXTERNAL_SUBSCRIPTION_CHARGE_ID, baseExternalSubscriptionChargeId)
            .set(ACCOUNT_ORDER_LINE_ITEM.METRICS, RECOMPUTE_METRICS_JSONB)
            .where(ACCOUNT_ORDER_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(ACCOUNT_ORDER_LINE_ITEM.ID.eq(id))
            .and(ACCOUNT_ORDER_LINE_ITEM.IS_DELETED.eq(false))
            .execute();
    }
}
