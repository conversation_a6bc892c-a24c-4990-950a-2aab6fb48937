package com.subskribe.billy.order.services;

import com.subskribe.billy.order.db.OrderDAO;
import javax.inject.Inject;
import org.jooq.DSLContext;

public class OrderPdfGenerationTrackerService {

    private final OrderDAO orderDAO;

    @Inject
    public OrderPdfGenerationTrackerService(OrderDAO orderDAO) {
        this.orderDAO = orderDAO;
    }

    public void setShouldRegeneratePdf(String orderId, Boolean shouldRegeneratePdf) {
        orderDAO.setShouldRegeneratePdf(orderId, shouldRegeneratePdf);
    }

    public void setShouldRegeneratePdfInTransaction(DSLContext dslcontext, String orderId, Boolean shouldRegeneratePdf) {
        orderDAO.setShouldRegeneratePdfInTransaction(dslcontext, orderId, shouldRegeneratePdf);
    }
}
