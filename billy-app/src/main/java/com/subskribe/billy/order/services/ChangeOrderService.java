package com.subskribe.billy.order.services;

import static com.subskribe.billy.invoice.service.InvoiceServiceInternal.INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION;

import com.google.common.collect.Lists;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceService;
import com.subskribe.billy.attachments.service.AttachmentsService;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customization.model.ImmutableOrderCreationCustomizationInput;
import com.subskribe.billy.customization.model.OrderCreationCustomizationInput;
import com.subskribe.billy.customization.service.CustomizationService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.order.CreditableAmount;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.hubspot.service.HubSpotJobQueueService;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.EvergreenUtils;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.model.ChangeOrderContext;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.ImmutableChangeOrderContext;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderSource;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.order.model.PricingOverride;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Discount;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionOrderService;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.services.DocumentCustomContentService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.ws.rs.InternalServerErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class ChangeOrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChangeOrderService.class);

    private static final Set<OrderType> NOT_ALLOWED_ORDER_TYPES_WITH_CHANGE_ORDER_SERVICE_ROUTE = Set.of(
        OrderType.NEW,
        OrderType.RENEWAL,
        OrderType.RESTRUCTURE
    );
    private static final Set<ActionType> NOT_ALLOWED_ACTION_TYPES_WITH_CHANGE_ORDER_SERVICE_ROUTE = Set.of(
        ActionType.RENEWAL,
        ActionType.MISSING_RENEWAL,
        ActionType.RESTRUCTURE
    );

    private static final Set<OrderType> ALLOWED_ORDER_TYPES_FOR_OTC_DEBOOK_REFUND = Set.of(OrderType.CANCEL, OrderType.AMENDMENT);

    private final OrderDAO orderDAO;
    private final AccountGetService accountGetService;
    private final ProductCatalogGetService productCatalogGetService;
    private final InvoiceService invoiceService;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final OrderIdGenerator orderIdGenerator;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final DocumentCustomContentService documentCustomContentService;
    private final TenantSettingService tenantSettingService;
    private final OpportunityService opportunityService;
    private final OrderMapper orderMapper;
    private final DiscountService discountService;
    private final ApprovalFlowInstanceService approvalFlowInstanceService;
    private final SalesforceJobQueueService salesforceJobQueueService;
    private final HubSpotJobQueueService hubSpotJobQueueService;
    private final UserService userService;
    private final FeatureService featureService;
    private final BillyConfiguration billyConfiguration;
    private final HubSpotIntegrationService hubSpotIntegrationService;
    private final TenantJobDispatcherService tenantJobDispatcherService;
    private final CustomFieldService customFieldService;
    private final AttachmentsService attachmentsService;
    private final SubscriptionGetService subscriptionGetService;
    private final CatalogRelationshipGetService catalogRelationshipGetService;
    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;
    private final RateCardService rateCardService;
    private final TaxService taxService;
    private final TaxRateGetService taxRateGetService;
    private final CustomizationService customizationService;
    private final MissingOrderChargesService missingOrderChargesService;
    private final MetricsService metricsService;
    private final OrderGetService orderGetService;
    private final OrderValidationService orderValidationService;
    private final CurrencyConversionRateGetService currencyConversionRateGetService;
    private final SubscriptionOrderService subscriptionOrderService;
    private final OrderCustomBillingService orderCustomBillingService;
    private final OrderDocumentTemplateService orderDocumentTemplateService;

    @Inject
    public ChangeOrderService(
        OrderDAO orderDAO,
        AccountGetService accountGetService,
        ProductCatalogGetService productCatalogGetService,
        DSLContextProvider dslContextProvider,
        InvoiceService invoiceService,
        TenantIdProvider tenantIdProvider,
        OrderIdGenerator orderIdGenerator,
        DocumentTemplateGetService documentTemplateGetService,
        DocumentCustomContentService documentCustomContentService,
        TenantSettingService tenantSettingService,
        OpportunityService opportunityService,
        DiscountService discountService,
        ApprovalFlowInstanceService approvalFlowInstanceService,
        HubSpotJobQueueService hubSpotJobQueueService,
        UserService userService,
        SalesforceJobQueueService salesforceJobQueueService,
        FeatureService featureService,
        BillyConfiguration billyConfiguration,
        HubSpotIntegrationService hubSpotIntegrationService,
        TenantJobDispatcherService tenantJobDispatcherService,
        CustomFieldService customFieldService,
        AttachmentsService attachmentsService,
        SubscriptionGetService subscriptionGetService,
        CatalogRelationshipGetService catalogRelationshipGetService,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        RateCardService rateCardService,
        TaxService taxService,
        TaxRateGetService taxRateGetService,
        CustomizationService customizationService,
        MissingOrderChargesService missingOrderChargesService,
        MetricsService metricsService,
        OrderGetService orderGetService,
        OrderValidationService orderValidationService,
        CurrencyConversionRateGetService currencyConversionRateGetService,
        SubscriptionOrderService subscriptionOrderService,
        OrderCustomBillingService orderCustomBillingService,
        OrderDocumentTemplateService orderDocumentTemplateService
    ) {
        this.orderDAO = orderDAO;
        this.accountGetService = accountGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.invoiceService = invoiceService;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.orderIdGenerator = orderIdGenerator;
        this.documentTemplateGetService = documentTemplateGetService;
        this.documentCustomContentService = documentCustomContentService;
        this.tenantSettingService = tenantSettingService;
        this.opportunityService = opportunityService;
        this.discountService = discountService;
        this.approvalFlowInstanceService = approvalFlowInstanceService;
        this.hubSpotJobQueueService = hubSpotJobQueueService;
        this.userService = userService;
        this.salesforceJobQueueService = salesforceJobQueueService;
        this.featureService = featureService;
        this.billyConfiguration = billyConfiguration;
        this.hubSpotIntegrationService = hubSpotIntegrationService;
        this.tenantJobDispatcherService = tenantJobDispatcherService;
        this.customFieldService = customFieldService;
        this.attachmentsService = attachmentsService;
        this.subscriptionGetService = subscriptionGetService;
        this.catalogRelationshipGetService = catalogRelationshipGetService;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.rateCardService = rateCardService;
        this.taxService = taxService;
        this.taxRateGetService = taxRateGetService;
        this.customizationService = customizationService;
        this.missingOrderChargesService = missingOrderChargesService;
        this.metricsService = metricsService;
        this.orderGetService = orderGetService;
        this.orderValidationService = orderValidationService;
        this.currencyConversionRateGetService = currencyConversionRateGetService;
        this.subscriptionOrderService = subscriptionOrderService;
        this.orderCustomBillingService = orderCustomBillingService;
        this.orderDocumentTemplateService = orderDocumentTemplateService;
        orderMapper = Mappers.getMapper(OrderMapper.class);
    }

    public Order rebaseAmendment(String orderId) {
        Validator.validateStringNotBlank(orderId, "Order id is required");
        Order order = orderGetService.getOrderByOrderId(orderId);

        BigDecimal originalOrderAmount = order.getTotalAmount();

        validateOrderForRebase(order);

        int nextSubscriptionVersion = subscriptionGetService.getNextSubscriptionVersion(order.getExternalSubscriptionId());

        if (nextSubscriptionVersion <= order.getSubscriptionTargetVersion()) {
            throw new InvalidInputException("Amendment already targeting latest subscription version");
        }

        order.setSubscriptionTargetVersion(nextSubscriptionVersion);

        Map<String, Metrics> orderLineMetrics = metricsService.getMetricsForAllOrderLines(order);

        List<OrderLineItem> consolidatedOrderLineItems = OrderServiceHelper.consolidateOrderLineItemsByBaseExternalSubscriptionChargeId(
            order.getLineItems(),
            orderLineMetrics
        );

        Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());

        Set<String> subscriptionChargeIds = subscription
            .getCharges()
            .stream()
            .map(SubscriptionCharge::getSubscriptionChargeId)
            .collect(Collectors.toSet());

        checkForRebaseConflicts(consolidatedOrderLineItems, subscriptionChargeIds);

        if (order.getStatus() == OrderStatus.DRAFT) {
            // if order is in DRAFT status, update the order to reflect the updated subscription line items
            List<OrderLineItem> updatedOrderLineItems = removeRemovedSubscriptionChargeItems(consolidatedOrderLineItems, subscriptionChargeIds);

            order.setLineItems(updatedOrderLineItems);

            // dryrun the rebased order first and validate if the new amount is the same as the original order amount
            Order dryrunOrder = updateChangeOrder(order, true, true, true);

            if (dryrunOrder.getTotalAmount().compareTo(originalOrderAmount) != 0) {
                throw new InvalidInputException("Updating the amendment with the latest subscription would change the total amount of the order");
            }

            return updateChangeOrder(order, false, true, true);
        } else {
            // if order is SUBMITTED or APPROVED already, update target version only
            int rowsUpdated = orderDAO.updateSubscriptionTargetVersion(orderId, nextSubscriptionVersion);

            if (rowsUpdated == 0) {
                throw new InvariantCheckFailedException("Failed to update order");
            }

            return orderGetService.getOrderByOrderId(orderId);
        }
    }

    // keep only the line items that are not based on existing subscription charges or are not referencing an existing subscription charge (items with ADD action)
    static List<OrderLineItem> removeRemovedSubscriptionChargeItems(List<OrderLineItem> orderLineItems, Set<String> subscriptionChargeIds) {
        return orderLineItems
            .stream()
            .filter(
                item ->
                    item.getBaseExternalSubscriptionChargeId() == null || subscriptionChargeIds.contains(item.getBaseExternalSubscriptionChargeId())
            )
            .collect(Collectors.toList());
    }

    static void checkForRebaseConflicts(List<OrderLineItem> orderLineItems, Set<String> subscriptionChargeIds) {
        List<OrderLineItem> updateOrRemoveItems = orderLineItems
            .stream()
            .filter(item -> item.getAction() == ActionType.UPDATE || item.getAction() == ActionType.REMOVE)
            .toList();

        if (CollectionUtils.isEmpty(updateOrRemoveItems)) {
            return;
        }

        Set<String> removeOrUpdateSubscriptionChargeIds = updateOrRemoveItems
            .stream()
            .map(OrderLineItem::getBaseExternalSubscriptionChargeId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (removeOrUpdateSubscriptionChargeIds.size() != updateOrRemoveItems.size()) {
            String message = String.format(
                "Number of line items updating or removing subscription charge is not equal to the number of unique subscription charge ids for order %s",
                orderLineItems.get(0).getOrderId()
            );
            throw new InvariantCheckFailedException(message);
        }

        if (!subscriptionChargeIds.containsAll(removeOrUpdateSubscriptionChargeIds)) {
            // if the subscription charges to be updated by the amendment have been updated already, their ids have changed and won't be in the current set
            throw new InvalidInputException(
                "Updated subscription contains conflicting changes. Amendment cannot be automatically refreshed against current subscription."
            );
        }
    }

    private static void validateOrderForRebase(Order order) {
        if (order.getStatus() == OrderStatus.EXECUTED) {
            throw new InvalidInputException("Cannot rebase executed orders");
        }

        if (order.getOrderType() != OrderType.AMENDMENT) {
            throw new InvalidInputException("Only amendments can be rebased");
        }
    }

    public Order createChangeOrder(Order changeOrderRequest, Boolean isDryRun, Boolean populateMissingLineItems) {
        // todo: this is here temporary to facilitate data import, should move to tenant config instead
        return createChangeOrder(changeOrderRequest, isDryRun, populateMissingLineItems, false);
    }

    public Order createChangeOrder(Order changeOrderRequest, Boolean isDryRun, Boolean populateMissingLineItems, boolean skipAmendPercentOf) {
        Subscription subscription = validateAndFetchSubscription(changeOrderRequest);
        changeOrderRequest.setEndDate(subscription.getEndDate());
        validateChangeOrderRequest(changeOrderRequest, subscription.getBillingAnchorDate());
        OrderServiceHelper.validateOrderCurrency(changeOrderRequest, subscription.getCurrency());
        changeOrderRequest.setAccountId(subscription.getAccountId());
        Account account = orderValidationService.validateAccountDetails(changeOrderRequest);
        ChangeOrderContext changeOrderContext = makeChangeOrderContext(changeOrderRequest, subscription, account, isDryRun);
        validateOneTimeChargeDebookRefund(changeOrderRequest, changeOrderContext);

        var tenantId = tenantIdProvider.provideTenantIdString();
        String entityId = subscription.getEntityId();
        if (changeOrderRequest.getOrderType() == OrderType.AMENDMENT) {
            changeOrderRequest.getLineItems().forEach(li -> li.setTenantId(tenantId));
            changeOrderRequest.getLineItems().forEach(li -> li.setEntityId(entityId));
        }
        changeOrderRequest.setTenantId(tenantId);
        changeOrderRequest.setEntityId(entityId);
        changeOrderRequest.setStatus(OrderStatus.DRAFT);
        OrderServiceHelper.addOrderCreatedByAndOwner(changeOrderRequest, userService);

        addDatesToLineItems(changeOrderRequest, changeOrderContext);

        validateOrderLineChargesForUpdates(changeOrderRequest, changeOrderContext);

        if (!isDryRun) {
            checkProductRules(changeOrderRequest, changeOrderContext);
        }
        validatePaymentTermCannotBeChangedDuringAmendment(changeOrderRequest, changeOrderContext);
        Order changeOrder = toOrderCreateFromRequest(changeOrderRequest, changeOrderContext, populateMissingLineItems);
        orderDocumentTemplateService.filterOutPredefinedTermsBroughtByPlans(changeOrder, changeOrderContext.getPlansInOrder());
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        Order resultOrder = dslContext.transactionResult(configuration ->
            createChangeOrderTransaction(configuration, changeOrder, isDryRun, skipAmendPercentOf, changeOrderContext)
        );

        if (!isDryRun) {
            hubSpotJobQueueService.dispatchOrderSync(resultOrder);
            salesforceJobQueueService.dispatchOrderSync(resultOrder);
        }

        return resultOrder;
    }

    private void setEvergreenOrderEndDates(Order order) {
        if (featureService.isEnabled(Feature.EVERGREEN) && EvergreenUtils.isEvergreenOrder(order)) {
            order.setEndDate(EvergreenUtils.EVERGREEN_SENTINEL_END_DATE);
            order
                .getLineItems()
                .forEach(lineItem -> {
                    if (lineItem.getEndDate() == null) {
                        lineItem.setEndDate(EvergreenUtils.EVERGREEN_SENTINEL_END_DATE);
                    }
                });
        }
    }

    private ChangeOrderContext makeChangeOrderContext(Order changeOrderRequest, Subscription subscription, Account account, Boolean isDryRun) {
        Map<String, Charge> subscriptionChargeMap = getSubscriptionChargeMap(subscription);
        Map<String, Charge> orderChargeMap = getOrderChargeMap(changeOrderRequest, subscriptionChargeMap);

        Set<String> distinctChargeIds = new HashSet<>(subscriptionChargeMap.keySet());
        distinctChargeIds.addAll(orderChargeMap.keySet());

        ImmutableChangeOrderContext.Builder contextBuilder = ImmutableChangeOrderContext.builder();

        if (StringUtils.isNotBlank(changeOrderRequest.getShippingContactId())) {
            contextBuilder.shippingContact(accountGetService.getContact(changeOrderRequest.getShippingContactId()));
        }

        if (StringUtils.isNotBlank(changeOrderRequest.getBillingContactId())) {
            AccountContact billingContact = accountGetService.getContact(changeOrderRequest.getBillingContactId());
            contextBuilder.billingContact(billingContact);

            if (!billingContact.getAccountId().equals(account.getAccountId())) {
                contextBuilder.billingContactAccount(accountGetService.getAccount(billingContact.getAccountId()));
            } else {
                contextBuilder.billingContactAccount(account);
            }
        }

        if (distinctChargeIds.isEmpty()) {
            return contextBuilder
                .chargesInSubscription(List.of())
                .plansInSubscription(List.of())
                .chargesInOrder(List.of())
                .plansInOrder(List.of())
                .allPlans(List.of())
                .allCharges(List.of())
                .allProducts(List.of())
                .planCustomFields(Map.of())
                .chargeCustomFields(Map.of())
                .subscription(subscription)
                .orderAccount(account)
                .build();
        }

        Map<UUID, Plan> allPlansMap = productCatalogGetService
            .getPlansFromChargeIds(distinctChargeIds.stream().toList())
            .stream()
            .collect(Collectors.toMap(Plan::getId, Function.identity()));

        List<Plan> subscriptionPlans = subscriptionChargeMap
            .values()
            .stream()
            .map(Charge::getPlanUuid)
            .distinct()
            .map(allPlansMap::get)
            .filter(Objects::nonNull)
            .toList();
        List<Plan> orderPlans = orderChargeMap
            .values()
            .stream()
            .map(Charge::getPlanUuid)
            .distinct()
            .map(allPlansMap::get)
            .filter(Objects::nonNull)
            .toList();

        List<Charge> allCharges = distinctChargeIds
            .stream()
            .map(chargeId -> subscriptionChargeMap.containsKey(chargeId) ? subscriptionChargeMap.get(chargeId) : orderChargeMap.get(chargeId))
            .filter(Objects::nonNull)
            .toList();

        List<Product> products = allPlansMap.values().stream().map(Plan::getProductId).distinct().map(productCatalogGetService::getProduct).toList();

        // load custom fields
        List<String> allPlanIds = allPlansMap.values().stream().map(Plan::getPlanId).toList();
        Map<String, CustomField> plansCustomFields = customFieldService.getCustomFields(CustomFieldParentType.PLAN, allPlanIds);
        Map<String, CustomField> chargesCustomFields = customFieldService.getCustomFields(
            CustomFieldParentType.CHARGE,
            distinctChargeIds.stream().toList()
        );

        // TODO: move this to another place p
        OrderServiceHelper.hydrateAndValidateCustomFields(changeOrderRequest, isDryRun, customFieldService);

        return contextBuilder
            .chargesInSubscription(subscriptionChargeMap.values())
            .chargesInOrder(orderChargeMap.values())
            .allCharges(allCharges)
            .plansInSubscription(subscriptionPlans)
            .plansInOrder(orderPlans)
            .allPlans(allPlansMap.values())
            .subscription(subscription)
            .allProducts(products)
            .planCustomFields(plansCustomFields)
            .chargeCustomFields(chargesCustomFields)
            .orderAccount(account)
            .build();
    }

    private Map<String, Charge> getSubscriptionChargeMap(Subscription subscription) {
        if (CollectionUtils.isNotEmpty(subscription.getCharges())) {
            List<String> chargeIdsInSubscription = subscription
                .getCharges()
                .stream()
                .map(SubscriptionCharge::getChargeId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();
            return productCatalogGetService
                .getChargesByChargeId(chargeIdsInSubscription)
                .stream()
                .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        }
        return Map.of();
    }

    private Map<String, Charge> getOrderChargeMap(Order changeOrderRequest, Map<String, Charge> subscriptionChargeMap) {
        if (CollectionUtils.isNotEmpty(changeOrderRequest.getLineItems())) {
            List<String> chargeIdsInOrder = changeOrderRequest
                .getLineItems()
                .stream()
                .map(OrderLineItem::getChargeId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();
            return chargeIdsInOrder
                .stream()
                .map(chargeId ->
                    subscriptionChargeMap.containsKey(chargeId)
                        ? subscriptionChargeMap.get(chargeId)
                        : productCatalogGetService.getChargeByChargeId(chargeId)
                )
                .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        }
        return Map.of();
    }

    private void validateChangeOrderRequest(Order changeOrderRequest, Instant subscriptionBillingAnchorDate) {
        if (changeOrderRequest == null) {
            LOGGER.info("change order is null");
            throw new IllegalArgumentException("change order is null");
        }

        if (changeOrderRequest.getExternalSubscriptionId() == null) {
            LOGGER.info("subscription id is null");
            throw new IllegalArgumentException("subscription id is null");
        }

        if (NOT_ALLOWED_ORDER_TYPES_WITH_CHANGE_ORDER_SERVICE_ROUTE.contains(changeOrderRequest.getOrderType())) {
            LOGGER.info("new/renew/restructure orders should go through Order Service instead of Change Order Service");
            throw new InternalServerErrorException("new/renew/restructure orders should go through Order Service instead of Change Order Service");
        }

        if (changeOrderRequest.getOrderType() == OrderType.CANCEL && CollectionUtils.isNotEmpty(changeOrderRequest.getLineItems())) {
            LOGGER.info("order type cancel shouldn't have any line items");
            throw new IllegalArgumentException("order type cancel shouldn't have any line items");
        }

        if (changeOrderRequest.getLineItems().stream().anyMatch(item -> item.getAction() == null)) {
            throw new InvalidInputException("line item action is required");
        }

        if (changeOrderRequest.getOrderType() == OrderType.AMENDMENT && CollectionUtils.isEmpty(changeOrderRequest.getLineItems())) {
            LOGGER.info("order type amendment should have non empty line items");
            throw new IllegalArgumentException("order type amendment should have non empty line items");
        }

        if (changeOrderRequest.getOrderType() == OrderType.AMENDMENT) {
            changeOrderRequest.getLineItems().forEach(this::validateAmendmentLineItems);
        }

        if (
            changeOrderRequest.getOrderType() == OrderType.AMENDMENT &&
            !Objects.isNull(changeOrderRequest.getBillingAnchorDate()) &&
            !changeOrderRequest.getBillingAnchorDate().equals(subscriptionBillingAnchorDate)
        ) {
            throw new InvalidInputException(
                "Billing anchor date is not null and does not match the subscription's billing anchor date for amendment order"
            );
        }

        OrderServiceHelper.validateRampIntervals(changeOrderRequest);
        orderDocumentTemplateService.validateDocumentMasterTemplate(changeOrderRequest);
        orderDocumentTemplateService.findAndAddMissingPlanTemplates(changeOrderRequest);
        orderDocumentTemplateService.validateOrderTemplate(changeOrderRequest);
        // todo: add `filterOutPredefinedTermsBroughtByPlans`?
        OrderServiceHelper.validateOrderName(changeOrderRequest);
        OrderServiceHelper.validateOrderAttachment(changeOrderRequest, attachmentsService);
        OrderServiceHelper.checkCrmIdIsEmptyForCompositeOrders(changeOrderRequest);
    }

    // todo: match validation parity with OrderService::validateLineItem
    private void validateAmendmentLineItems(OrderLineItem li) {
        if (StringUtils.isBlank(li.getBaseExternalSubscriptionChargeId()) && (li.getPlanId() == null || li.getChargeId() == null)) {
            LOGGER.info("planId and chargeId are expected for all new line items");
            throw new IllegalArgumentException("planId and chargeId are expected for all new line items");
        }

        if (NOT_ALLOWED_ACTION_TYPES_WITH_CHANGE_ORDER_SERVICE_ROUTE.contains(li.getAction())) {
            LOGGER.info("action type {} is not allowed for change order service route", li.getAction());
            throw new InvalidInputException("action type " + li.getAction() + " is not allowed for change order service route");
        }

        if (li.getAction() == ActionType.ADD && StringUtils.isNotBlank(li.getBaseExternalSubscriptionChargeId())) {
            LOGGER.info("line items with ADD action shouldn't have subscription charge id");
            throw new IllegalArgumentException("line items with ADD action shouldn't have subscription charge id");
        }

        if (li.getAction() != ActionType.ADD && StringUtils.isBlank(li.getBaseExternalSubscriptionChargeId())) {
            LOGGER.info("line items with UPDATE or CANCEL action should have subscription charge id");
            throw new IllegalArgumentException("line items with UPDATE or CANCEL action should have subscription charge id");
        }

        if (li.getAction() == ActionType.REMOVE) {
            li.setQuantity(0L);
        }
    }

    private Order createChangeOrderTransaction(
        Configuration configuration,
        Order changeOrder,
        Boolean isDryRun,
        boolean skipAmendPercentOf,
        ChangeOrderContext changeOrderContext
    ) {
        var dslContext = DSL.using(configuration);
        OrderServiceHelper.populateOrderAndOrderLineIds(orderIdGenerator, changeOrder);
        OrderServiceHelper.fillNetEffectOrderLines(changeOrder);

        // order is important here, customization needs to run before preview to allow for
        // several actions to be previewed with invoice preview
        runOrderCreationCustomization(changeOrder, changeOrderContext, isDryRun);

        //FX handling
        updateOrderLineItemWithCurrencyConversionRateId(changeOrder, changeOrderContext);

        var invoicePreview = invoiceService.previewInvoiceByOrderPeriod(changeOrder, skipAmendPercentOf, INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION);
        OrderServiceHelper.updateOrderAmountsFromInvoice(changeOrder, invoicePreview);
        OrderServiceHelper.validateOrderTotalAmount(changeOrder.getTotalAmount());

        orderCustomBillingService.validateAndSetCustomBillingDetailsInOrder(changeOrder, isDryRun);

        if (EvergreenUtils.isEvergreenOrder(changeOrder)) {
            Map<String, InvoiceItemAmounts> orderLineItemBillingCycleAmounts = metricsService.getOrderLineItemBillingCycleAmounts(
                changeOrder,
                changeOrderContext.getAllCharges()
            );
            OrderServiceHelper.updateOrderTotalAndOrderLineItemAmountsForEvergreenOrder(changeOrder, orderLineItemBillingCycleAmounts);
        }

        if (!isDryRun) {
            String tenantId = Optional.ofNullable(changeOrder.getTenantId()).orElse(tenantIdProvider.provideTenantIdString());
            Optional<Opportunity> opportunity = opportunityService.upsertOpportunityForOrderIfNeeded(dslContext, changeOrder, tenantId, true);
            boolean isPrimaryOrderForOpportunity = changeOrder.getIsPrimaryOrderForSfdcOpportunity();
            List<String> orderTemplateIds = changeOrder.getOrderFormTemplateIds();
            CustomField orderCustomFields = changeOrder.getCustomFields();
            Map<Integer, List<CustomFieldEntry>> orderLineCustomFieldsByOrderLineItemRank = OrderServiceHelper.getCustomFieldsByOrderLineItemRankMap(
                changeOrder
            );
            OrderServiceHelper.calculateAndApplyTaxEstimates(changeOrder, taxService, taxRateGetService, accountGetService, productCatalogGetService);
            CustomBillingSchedule customBillingSchedule = changeOrder.getCustomBillingSchedule();

            changeOrder = orderDAO.addOrder(dslContext, changeOrder);
            changeOrder.setOrderFormTemplateIds(orderTemplateIds);
            // ⚠️order custom fields get overwritten while saving, so we need to add it back here ⚠️
            changeOrder.setCustomFields(orderCustomFields);
            OrderServiceHelper.addCustomFieldsBackToOrderLineItems(changeOrder, orderLineCustomFieldsByOrderLineItemRank, customizationService);

            OrderServiceHelper.fillNetEffectOrderLines(changeOrder);
            OrderServiceHelper.addSfdcFieldsToOrder(changeOrder, opportunity, isPrimaryOrderForOpportunity);

            List<DocumentTemplate> orderTemplates = documentTemplateGetService.getDocumentTemplatesByTemplateIds(orderTemplateIds);
            orderDocumentTemplateService.addPredefinedTermsForOrder(changeOrder, dslContext, tenantId, orderTemplates, false);

            orderCustomBillingService.saveOrInvalidateCustomBillingSchedule(
                dslContext,
                changeOrder,
                changeOrderContext.getChargesInOrder(),
                customBillingSchedule
            );
        } else {
            changeOrder.setOrderId(null);
            changeOrder.getLineItems().forEach(li -> li.setOrderId(null));
        }

        // ❌❌❌ The custom field persistence is non-transactional and can fail partially ❌❌❌
        // ❌❌❌ even though it called inside the transaction method it does nothing transactional ❌❌❌
        OrderServiceHelper.persistCustomFields(changeOrder, isDryRun, customFieldService);

        return changeOrder;
    }

    private void validateOrderLineChargesForUpdates(Order order, ChangeOrderContext changeOrderContext) {
        Subscription subscription = changeOrderContext.getSubscription();
        Map<String, Charge> chargeMap = changeOrderContext.allChargesMap();
        Map<UUID, Plan> plansByUuid = changeOrderContext.getAllPlans().stream().collect(Collectors.toMap(Plan::getId, Function.identity()));

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        ZoneId accountZoneId = timeZone.toZoneId();

        setEvergreenOrderEndDates(order);

        if (order.getOrderType() == OrderType.CANCEL) {
            OrderServiceHelper.validateChangeOrderWithUsage(order, subscription, invoiceService, chargeMap, accountZoneId);
            return;
        }

        order
            .getLineItems()
            .forEach(lineItem -> OrderDiscountService.validateDiscountList(lineItem, billyConfiguration.getFixedAmountDiscountConfiguration()));

        var existingChargeLines = order.getLineItemsHavingBaseSubscriptionCharge();
        var subscriptionChargesMap = subscription
            .getCharges()
            .stream()
            .collect(Collectors.toMap(SubscriptionCharge::getSubscriptionChargeId, Function.identity()));

        existingChargeLines.forEach(orderLineItem ->
            validateExistingOrderLineDetails(
                order.getStartDate(),
                orderLineItem,
                subscriptionChargesMap.get(orderLineItem.getBaseExternalSubscriptionChargeId()),
                chargeMap.get(orderLineItem.getChargeId())
            )
        );

        order
            .getLineItems()
            .forEach(lineItem -> {
                Charge charge = chargeMap.get(lineItem.getChargeId());
                if (charge.isCustom()) {
                    OrderServiceHelper.validateCustomChargeLineItem(lineItem);
                }
                OrderServiceHelper.validateListPriceOverride(lineItem, charge);
                OrderServiceHelper.validatePricingOverride(lineItem, charge, featureService);
                OrderServiceHelper.validateArrOverride(lineItem, charge, featureService);
                OrderDiscountService.validateDiscountLine(lineItem, charge);
            });
        validateDeprecatedPlansCannotBeAddedToAmendment(order.getLineItems(), changeOrderContext);

        List<Instant> billingPeriodStartDates = subscriptionBillingPeriodService.getAllBillingPeriodStartDates(subscription.getSubscriptionId());

        // todo: check against latest usage
        OrderServiceHelper.validateMinimumCommitLineItems(order.getLineItems(), chargeMap, billingPeriodStartDates);
        OrderServiceHelper.validatePrepaidAmendments(order, subscription, chargeMap, plansByUuid);
        OrderServiceHelper.validateChangeOrderWithUsage(order, subscription, invoiceService, chargeMap, accountZoneId);
        order
            .getLineItems()
            .forEach(li -> OrderServiceHelper.validateOrderLineItemPriceAttribution(li, chargeMap.get(li.getChargeId()), rateCardService));

        OrderServiceHelper.validateOrderEntityScope(order, changeOrderContext.getChargesInOrder());
    }

    private void validateDeprecatedPlansCannotBeAddedToAmendment(List<OrderLineItem> lineItems, ChangeOrderContext changeOrderContext) {
        List<OrderLineItem> newlyAddedLineItems = lineItems.stream().filter(li -> li.getAction() == ActionType.ADD).toList();

        if (CollectionUtils.isEmpty(newlyAddedLineItems)) {
            return;
        }

        Map<String, Plan> planByIdMap = changeOrderContext.getAllPlans().stream().collect(Collectors.toMap(Plan::getPlanId, Function.identity()));
        List<String> existingPlanIdsInSubscription = changeOrderContext.getPlansInSubscription().stream().map(Plan::getPlanId).toList();

        newlyAddedLineItems.forEach(lineItem -> {
            Plan plan = planByIdMap.get(lineItem.getPlanId());
            if (plan == null) {
                return;
            }
            OrderServiceHelper.validateDeprecatedPlan(plan, existingPlanIdsInSubscription);
        });
    }

    private void validateExistingOrderLineDetails(
        Instant orderStartDate,
        OrderLineItem orderLineItem,
        SubscriptionCharge subscriptionCharge,
        Charge charge
    ) {
        validateActionTypeAndQuantity(orderLineItem, subscriptionCharge, charge);

        if (orderStartDate.compareTo(subscriptionCharge.getEndDate()) >= 0 && orderLineItem.getAction() != ActionType.NONE) {
            var message = "cannot amend an orderLineItem which ended before the order start date";
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        validateExistingLineItemsCustomCharge(orderLineItem, subscriptionCharge, charge);
        validateUnitPricesOnExistingChargeLines(orderLineItem, subscriptionCharge, charge);
    }

    private static void validateExistingLineItemsCustomCharge(OrderLineItem orderLineItem, SubscriptionCharge subscriptionCharge, Charge charge) {
        if (!charge.isCustom()) {
            return;
        }
        if (orderLineItem.getListUnitPrice() == null) {
            orderLineItem.setListUnitPrice(subscriptionCharge.getListUnitPrice());
        }
        if (!Numbers.equals(orderLineItem.getListUnitPrice(), subscriptionCharge.getListUnitPrice())) {
            var message = "Cannot change the list unit price for custom charge in amendment";
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }
    }

    private static void validateActionTypeAndQuantity(OrderLineItem orderLineItem, SubscriptionCharge subscriptionCharge, Charge charge) {
        // When a plan is removed altogether, the individual orderLineItem.getAction() is REMOVE
        // and in this case no further checks are required
        if (orderLineItem.getAction() == ActionType.REMOVE) {
            return;
        }
        //When charge type is percentage of, checks based on quantity and action type are not required
        if (ChargeType.PERCENTAGE_OF == charge.getType()) {
            return;
        }

        if (orderLineItem.getAction() == ActionType.NONE && orderLineItem.getQuantity() != subscriptionCharge.getQuantity()) {
            LOGGER.info("Quantity should be the same if the actionType is none.");
            throw new IllegalArgumentException("Quantity should be the same if the actionType is NONE.");
        }

        if (
            charge.getType() != ChargeType.USAGE &&
            orderLineItem.getQuantity() == subscriptionCharge.getQuantity() &&
            orderLineItem.getAction() != ActionType.NONE
        ) {
            LOGGER.info("actionType should be NONE if there is no change in the quantity");
            throw new IllegalArgumentException("actionType should be NONE if there is no change in the quantity");
        }
    }

    private void validateUnitPricesOnExistingChargeLines(OrderLineItem orderLineItem, SubscriptionCharge subscriptionCharge, Charge charge) {
        if (featureService.isEnabled(Feature.UPDATE_ZERO_BASE_LINE_ITEM) && hasZeroListAmount(subscriptionCharge, charge)) {
            // if original subscription charge has $0 list amount, nothing to validate
            return;
        }

        if (!Numbers.equals(subscriptionCharge.getListPriceOverrideRatio(), orderLineItem.getListPriceOverrideRatio())) {
            // if list price override ratio is not set, then use ratio of 1 (no override)
            BigDecimal subscriptionChargeListPriceOverrideRatio = subscriptionCharge.getListPriceOverrideRatio() == null
                ? BigDecimal.ONE
                : subscriptionCharge.getListPriceOverrideRatio();

            String message = String.format(
                "cannot modify list price override ratio for an existing line item. Expected %s but got %s",
                subscriptionChargeListPriceOverrideRatio,
                orderLineItem.getListPriceOverrideRatio()
            );
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        if (CollectionUtils.isEmpty(orderLineItem.getDiscounts())) {
            return;
        }

        if (CollectionUtils.isEmpty(subscriptionCharge.getDiscounts())) {
            String message = String.format("Subscription charge discounts are empty, but line item discounts are %s", orderLineItem.getDiscounts());
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        if (orderLineItem.getDiscounts().stream().anyMatch(discount -> StringUtils.isBlank(discount.getName()))) {
            LOGGER.info("discount name on existing charges cannot be null");
            throw new IllegalArgumentException("discount name on existing charges cannot be null");
        }

        var orderLineDiscountsByName = orderLineItem.getDiscounts().stream().collect(Collectors.groupingBy(Discount::getName));
        var subscriptionDiscountsByName = subscriptionCharge.getDiscounts().stream().collect(Collectors.groupingBy(Discount::getName));

        if (orderLineDiscountsByName.size() != subscriptionDiscountsByName.size()) {
            String message = String.format(
                "discounts by name sets do not match. Expected %s but got %s",
                subscriptionDiscountsByName.size(),
                orderLineDiscountsByName.size()
            );
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        orderLineDiscountsByName.keySet().forEach(key -> compareDiscounts(orderLineDiscountsByName, subscriptionDiscountsByName, key));
    }

    // determine if subscription charge has $0 list amount. If so, any update can change discount and list amount override values without impact
    public static boolean hasZeroListAmount(SubscriptionCharge subscriptionCharge, Charge charge) {
        if (subscriptionCharge.getQuantity() == 0) {
            // if quantity is 0, amount must be 0
            return true;
        }

        if (subscriptionCharge.getListUnitPrice() != null && Numbers.isZero(subscriptionCharge.getListUnitPrice())) {
            // if subscription charge has explicitly 0 list unit price
            return true;
        }

        BigDecimal chargeUnitPrice = charge.getListUnitPriceByQuantity(
            subscriptionCharge.getQuantity(),
            Optional.ofNullable(subscriptionCharge.getPricingOverride())
        );

        // if charge list unit price calculated from quantity is 0
        return chargeUnitPrice != null && Numbers.isZero(chargeUnitPrice);
    }

    private static void compareDiscounts(
        Map<String, List<DiscountDetail>> orderLineDiscountsByName,
        Map<String, List<Discount>> subscriptionDiscountsByName,
        String discountName
    ) {
        if (!subscriptionDiscountsByName.containsKey(discountName)) {
            String message = String.format("subscription discounts do not contain key %s, they are %s", discountName, subscriptionDiscountsByName);
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        var sortedOrderLineDiscountValues = orderLineDiscountsByName.get(discountName).stream().sorted().toList();
        var sortedSubscriptionDiscountValues = subscriptionDiscountsByName.get(discountName).stream().sorted().toList();
        if (!sortedOrderLineDiscountValues.equals(sortedSubscriptionDiscountValues)) {
            String message = String.format(
                "discounts do not match. Expected %s but got %s",
                sortedSubscriptionDiscountValues,
                sortedOrderLineDiscountValues
            );
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }
    }

    private Map<String, Plan> validateAndFetchPlans(
        Order changeOrderRequest,
        ChangeOrderContext changeOrderContext,
        Map<String, List<SubscriptionCharge>> subscriptionChargesMap
    ) {
        // todo: validate plan. If adding new plan to subscription plan should be ACTIVE. If updating existing plan, status doesn't matter

        Subscription subscription = changeOrderContext.getSubscription();
        List<Plan> allPlans = changeOrderContext.getAllPlans();

        if (changeOrderRequest.getOrderType() == OrderType.AMENDMENT) {
            validateUpdateOrderDetails(changeOrderRequest, subscriptionChargesMap);
        } else {
            validateCancelOrderDetails(changeOrderRequest);
        }

        OrderServiceHelper.validateCurrencyOnPlans(currencyConversionRateGetService, subscription.getCurrency(), allPlans);
        Map<String, Plan> planIdToPlanMap = changeOrderContext.allPlansMap();
        validatePlansAndCharges(changeOrderRequest, planIdToPlanMap);

        // Validate and Block Multiple Drawdown Charges
        OrderServiceHelper.validateDrawdownCharges(changeOrderRequest, allPlans, Optional.of(subscription));

        // validate all charges in plans exists if orderType is not cancel
        if (changeOrderRequest.getOrderType() != OrderType.CANCEL) {
            List<String> planIdsOnSubscription = changeOrderContext.getPlansInSubscription().stream().map(Plan::getPlanId).toList();
            OrderServiceHelper.validateAllChargesInNewPlansExist(changeOrderRequest.getLineItems(), allPlans, planIdsOnSubscription);
        }
        return planIdToPlanMap;
    }

    private Subscription validateAndFetchSubscription(Order changeOrderRequest) {
        var subscription = subscriptionGetService.getSubscription(changeOrderRequest.getExternalSubscriptionId());
        if (changeOrderRequest.getStartDate() == null) {
            changeOrderRequest.setStartDate(generateAmendmentStartDate(subscription));
        } else if (
            changeOrderRequest.getStartDate().isBefore(subscription.getStartDate()) ||
            changeOrderRequest.getStartDate().isAfter(subscription.getEndDate())
        ) {
            String message = String.format(
                "Invalid date for change order request. Subscription StartDate: %d, Subscription EndDate: %d, Amendment Date: %d",
                subscription.getStartDate().getEpochSecond(),
                subscription.getEndDate().getEpochSecond(),
                changeOrderRequest.getStartDate().getEpochSecond()
            );
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        OrderServiceHelper.checkForCanceledSubscription(subscription);

        if (changeOrderRequest.getOrderType() == OrderType.AMENDMENT) {
            var chargesInOrder = changeOrderRequest
                .getLineItemsHavingBaseSubscriptionCharge()
                .stream()
                .collect(Collectors.groupingBy(OrderLineItem::getBaseExternalSubscriptionChargeId));

            var chargesInSubscription = subscription
                .getCharges()
                .stream()
                .collect(Collectors.groupingBy(SubscriptionCharge::getSubscriptionChargeId));

            chargesInOrder.keySet().forEach(sc -> validateExistingSubscriptionChargeLines(chargesInOrder.get(sc), chargesInSubscription.get(sc), sc));
        }

        return subscription;
    }

    private void validateExistingSubscriptionChargeLines(
        List<OrderLineItem> updatingOrderLinesList,
        List<SubscriptionCharge> subscriptionChargeList,
        String subscriptionChargeId
    ) {
        if (subscriptionChargeList == null) {
            var message = "invalid subscription charge id provided: " + subscriptionChargeId;
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        updatingOrderLinesList.forEach(orderLineItem -> {
            validateExistingLineItemsChargeId(orderLineItem, subscriptionChargeList.get(0));
            validateExistingLineItemsAttributeReferences(orderLineItem, subscriptionChargeList.get(0));
        });
    }

    private void validateExistingLineItemsAttributeReferences(OrderLineItem orderLineItem, SubscriptionCharge subscriptionCharge) {
        boolean attributeReferencesInRequestIsNullOrEmpty = CollectionUtils.isEmpty(orderLineItem.getAttributeReferences());
        boolean attributeReferencesInSubscriptionChargeIsNullOrEmpty = CollectionUtils.isEmpty(subscriptionCharge.getAttributeReferences());
        // if both are null or empty, no further validation is required
        if (attributeReferencesInRequestIsNullOrEmpty && attributeReferencesInSubscriptionChargeIsNullOrEmpty) {
            return;
        }

        // in request object attribute references is null, but in subscription charge it present, copy from subscription charge to order line item
        if (attributeReferencesInRequestIsNullOrEmpty) {
            orderLineItem.setAttributeReferences(subscriptionCharge.getAttributeReferences());
            return;
        }

        // if attribute references is present in request object, but not in subscription charge, throw exception as this is not allowed during amendment
        if (attributeReferencesInSubscriptionChargeIsNullOrEmpty) {
            throw new InvalidInputException("Attribute references cannot be added while creating amendment order");
        }

        // check for equality
        AttributeReferences newAttributeReferences = AttributeReferences.wrap(orderLineItem.getAttributeReferences());
        AttributeReferences existingAttributeReferences = AttributeReferences.wrap(subscriptionCharge.getAttributeReferences());
        if (!Objects.equals(newAttributeReferences.getReferencesInOrder(), existingAttributeReferences.getReferencesInOrder())) {
            throw new InvalidInputException("Attribute references cannot be changed while creating amendment order");
        }
    }

    private void validateExistingLineItemsChargeId(OrderLineItem ol, SubscriptionCharge subscriptionCharge) {
        if (!ol.getChargeId().equals(subscriptionCharge.getChargeId())) {
            var message = String.format(
                "orderLine charge id doesn't match with existing subscription's charge Id. chargeId provided: %s, existing chargeId: %s for subscriptionChargeId %s",
                ol.getChargeId(),
                subscriptionCharge.getChargeId(),
                subscriptionCharge.getSubscriptionChargeId()
            );
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }
    }

    private Order toOrderCreateFromRequest(Order changeOrderRequest, ChangeOrderContext changeOrderContext, Boolean populateMissingLineItems) {
        Subscription subscription = changeOrderContext.getSubscription();
        var changeOrder = new Order(
            null,
            changeOrderRequest.getName(),
            null,
            subscription.getTenantId(),
            subscription.getEntityId(),
            changeOrderRequest.getExternalId(),
            subscription.getAccountId(),
            subscription.getResellerAccountId().orElse(null),
            changeOrderRequest.getOrderType(),
            subscription.getCurrency(),
            subscription.getPaymentTerm(),
            null,
            subscription.getSubscriptionId(),
            changeOrderRequest.getShippingContactId(),
            changeOrderRequest.getBillingContactId(),
            changeOrderRequest.getPredefinedDiscounts() == null ? subscription.getPredefinedDiscounts() : changeOrderRequest.getPredefinedDiscounts(),
            Lists.newArrayList(),
            Lists.newArrayList(),
            changeOrderRequest.getStartDate(),
            subscription.getEndDate(),
            subscription.getBillingAnchorDate(),
            subscription.getTermLength(),
            subscription.getBillingCycle(),
            subscription.getBillingTerm(),
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            null,
            BigDecimal.ZERO,
            OrderStatus.DRAFT,
            changeOrderRequest.getCreatedBy(),
            null,
            null,
            null,
            subscription.getRampInterval(),
            changeOrderRequest.getOrderFormTemplateIds(),
            changeOrderRequest.getOrderTerms(),
            changeOrderRequest.getSfdcOpportunityId(),
            changeOrderRequest.getIsPrimaryOrderForSfdcOpportunity(),
            changeOrderRequest.getSfdcOpportunityName(),
            changeOrderRequest.getSfdcOpportunityType(),
            changeOrderRequest.getSfdcOpportunityStage(),
            changeOrderRequest.getSfdcOrderCanBeExecuted(),
            changeOrderRequest.getOpportunityCrmType(),
            changeOrderRequest.getOwnerId(),
            changeOrderRequest.getRenewalForSubscriptionId(),
            changeOrderRequest.getRenewalForSubscriptionVersion(),
            changeOrderRequest.getRestructureForSubscriptionId(),
            changeOrderRequest.getRestructureForSubscriptionVersion(),
            changeOrderRequest.getPurchaseOrderNumber(),
            changeOrderRequest.getPurchaseOrderRequiredForInvoicing(),
            changeOrderRequest.getAutoRenew(),
            changeOrderRequest.getApprovalSegmentId(),
            // custom fields will be copied over form the request
            changeOrderRequest.getCustomFields(),
            changeOrderRequest.getCompositeOrderId(),
            true,
            changeOrderRequest.getExpiresOn(),
            null,
            changeOrderRequest.getSource(),
            changeOrderRequest.getStartDateType(),
            changeOrderRequest.getCreditableAmounts(),
            changeOrderRequest.getOrderProcessingMetadata(),
            subscription.getDurationModel(),
            changeOrderRequest.getOpportunity().orElse(null)
        );

        changeOrder.setSubscriptionTargetVersion(subscriptionGetService.getNextSubscriptionVersion(subscription.getSubscriptionId()));

        changeOrder.setDocumentMasterTemplateId(changeOrderRequest.getDocumentMasterTemplateId());
        changeOrder.setDocumentCustomContent(changeOrderRequest.getDocumentCustomContent());
        changeOrder.setAttachmentId(changeOrderRequest.getAttachmentId());
        changeOrder.setCustomBillingSchedule(changeOrderRequest.getCustomBillingSchedule());

        addChangeOrderLineItems(changeOrderRequest, changeOrderContext, changeOrder, populateMissingLineItems);

        orderDocumentTemplateService.consolidatePlanTemplates(changeOrder);

        return changeOrder;
    }

    private void addDatesToLineItems(Order order, ChangeOrderContext changeOrderContext) {
        if (order.getOrderType() == OrderType.CANCEL) {
            return;
        }
        Subscription subscription = changeOrderContext.getSubscription();
        var chargeMap = changeOrderContext.allChargesMap();

        var subscriptionChargeMap = subscription
            .getCharges()
            .stream()
            .collect(Collectors.toMap(SubscriptionCharge::getSubscriptionChargeId, Function.identity()));

        var subChargeExistingLinesMap = order
            .getLineItemsHavingBaseSubscriptionCharge()
            .stream()
            .collect(Collectors.toMap(OrderLineItem::getBaseExternalSubscriptionChargeId, Function.identity()));

        subChargeExistingLinesMap
            .keySet()
            .forEach(k -> setExistingOrderLineItemDates(subChargeExistingLinesMap.get(k), subscriptionChargeMap.get(k), order.getStartDate()));

        // set endDate for the new charges
        var chargeIdLineItemMap = order
            .getLineItemsWithEmptyBaseSubscriptionCharge()
            .stream()
            .collect(Collectors.groupingBy(OrderLineItem::getChargeId));

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        var zoneId = timeZone.toZoneId();

        chargeIdLineItemMap
            .keySet()
            .forEach(c ->
                OrderServiceHelper.updateLineItemEndDateByCharge(
                    featureService,
                    chargeIdLineItemMap.get(c),
                    chargeMap.get(c),
                    order.getStartDate(),
                    subscription.getEndDate(),
                    zoneId
                )
            );
    }

    private void setExistingOrderLineItemDates(OrderLineItem orderLineItem, SubscriptionCharge subscriptionCharge, Instant orderEffectiveDate) {
        Instant effectiveDate = subscriptionCharge.getStartDate();
        if (orderEffectiveDate.isAfter(subscriptionCharge.getStartDate()) && orderEffectiveDate.isBefore(subscriptionCharge.getEndDate())) {
            effectiveDate = orderEffectiveDate;
        }

        // todo: should we allow an existing line item with the effective date the same as the subscription start date if the action is 'NONE'?
        if (orderLineItem.getEffectiveDate() != null && DateTimeCalculator.compareInstants(orderLineItem.getEffectiveDate(), effectiveDate) != 0) {
            String message = String.format(
                "Line item effective date %s does not match expected effective date %s",
                orderLineItem.getEffectiveDate(),
                effectiveDate
            );
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }
        orderLineItem.setEffectiveDate(effectiveDate);

        if (orderLineItem.getEndDate() != null && orderLineItem.getEndDate().compareTo(subscriptionCharge.getEndDate()) != 0) {
            String message = String.format(
                "Line item end date %s does not match existing end date %s",
                orderLineItem.getEndDate(),
                subscriptionCharge.getEndDate()
            );
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }
        orderLineItem.setEndDate(subscriptionCharge.getEndDate());
    }

    private void addChangeOrderLineItems(
        Order changeOrderRequest,
        ChangeOrderContext changeOrderContext,
        Order changeOrder,
        Boolean populateMissingLineItems
    ) {
        Subscription subscription = changeOrderContext.getSubscription();
        var subscriptionChargesMap = subscription.getCharges().stream().collect(Collectors.groupingBy(SubscriptionCharge::getSubscriptionChargeId));

        Map<String, Plan> planIdToPlanMap = validateAndFetchPlans(changeOrderRequest, changeOrderContext, subscriptionChargesMap);

        // todo - find better way to create this map
        Map<String, Plan> chargeIdToPlanMap = new HashMap<>();
        for (var plan : planIdToPlanMap.values()) {
            for (var charge : plan.getCharges()) {
                chargeIdToPlanMap.put(charge.getChargeId(), plan);
            }
        }

        OrderServiceHelper.addOrderRankToLineItems(changeOrderRequest);
        if (changeOrderRequest.getOrderType() == OrderType.AMENDMENT || changeOrderRequest.getOrderType() == OrderType.RESTRUCTURE) {
            addLineItemsForUpdate(changeOrder, changeOrderRequest, subscriptionChargesMap, chargeIdToPlanMap, populateMissingLineItems);
        } else {
            addLineItemsForCancel(changeOrder, subscription, chargeIdToPlanMap, changeOrderRequest);
        }

        // On adding the lineItems, there may be some predefined discounts which are populated from the Subscription.
        // Need to validate to make sure the predefined discounts are present at the order level
        OrderDiscountService.validateOrderDiscounts(changeOrder, discountService, Optional.of(subscription));

        // validate order approval segments
        OrderServiceHelper.validateApprovalSegmentIdInOrder(changeOrder, approvalFlowInstanceService);
    }

    private void addLineItemsForUpdate(
        Order changeOrder,
        Order changeOrderRequest,
        Map<String, List<SubscriptionCharge>> chargesBySubscriptionChargeId,
        Map<String, Plan> chargeIdToPlanMap,
        Boolean populateMissingLineItems
    ) {
        if (populateMissingLineItems) {
            populateMissingLinesForAmendments(changeOrder, changeOrderRequest, chargesBySubscriptionChargeId, chargeIdToPlanMap);
        }

        Map<String, BigDecimal> creditableAmountMap = Optional.ofNullable(changeOrderRequest.getCreditableAmounts())
            .orElse(Collections.emptyList())
            .stream()
            .collect(Collectors.toMap(CreditableAmount::getSubscriptionChargeId, CreditableAmount::getCreditableAmount));

        changeOrderRequest
            .getLineItems()
            .forEach(li -> removeExistingOrderLineAndAddNewOne(changeOrder, chargesBySubscriptionChargeId, chargeIdToPlanMap, li, creditableAmountMap)
            );
    }

    private void removeExistingOrderLineAndAddNewOne(
        Order changeOrder,
        Map<String, List<SubscriptionCharge>> chargesBySubscriptionChargeId,
        Map<String, Plan> chargeIdToPlanMap,
        OrderLineItem requestedOrderLineItem,
        Map<String, BigDecimal> creditableAmountMap
    ) {
        SubscriptionCharge subscriptionCharge = null;
        if (requestedOrderLineItem.getBaseExternalSubscriptionChargeId() != null) {
            subscriptionCharge = chargesBySubscriptionChargeId.get(requestedOrderLineItem.getBaseExternalSubscriptionChargeId()).get(0);
            negateSubscriptionCharge(
                changeOrder,
                requestedOrderLineItem.getAction(),
                chargeIdToPlanMap,
                subscriptionCharge,
                requestedOrderLineItem.getEffectiveDate(),
                requestedOrderLineItem.getRank(),
                Optional.ofNullable(creditableAmountMap.get(requestedOrderLineItem.getBaseExternalSubscriptionChargeId()))
            );
            requestedOrderLineItem.setIsRamp(subscriptionCharge.getIsRamp());
        }

        if (requestedOrderLineItem.getQuantity() > 0 || requestedOrderLineItem.getAction() == ActionType.ADD) {
            addLineItem(changeOrder, chargeIdToPlanMap, requestedOrderLineItem, subscriptionCharge);
        }
    }

    private void populateMissingLinesForAmendments(
        Order changeOrder,
        Order changeOrderRequest,
        Map<String, List<SubscriptionCharge>> chargesBySubscriptionChargeId,
        Map<String, Plan> chargeIdToPlanMap
    ) {
        var existingChargeLines = changeOrderRequest
            .getLineItems()
            .stream()
            .filter(orderLine -> StringUtils.isNotBlank(orderLine.getBaseExternalSubscriptionChargeId()))
            .collect(Collectors.groupingBy(OrderLineItem::getBaseExternalSubscriptionChargeId));
        chargesBySubscriptionChargeId
            .keySet()
            .forEach(subscriptionChargeId ->
                populateMissingSubscriptionChargeId(
                    changeOrder,
                    changeOrderRequest,
                    chargesBySubscriptionChargeId,
                    chargeIdToPlanMap,
                    existingChargeLines,
                    subscriptionChargeId
                )
            );
    }

    // This will not hit from the UI unless we are changing the subscription amendment date.
    // we should remove modifying the subscription amendment changing date.
    // We cannot guarantee the order when populating the missing lines and hence their rank is being passed as 0.
    private void populateMissingSubscriptionChargeId(
        Order changeOrder,
        Order changeOrderRequest,
        Map<String, List<SubscriptionCharge>> chargesBySubscriptionChargeId,
        Map<String, Plan> chargeIdToPlanMap,
        Map<String, List<OrderLineItem>> existingChargeLines,
        String subscriptionChargeId
    ) {
        var subscriptionCharge = chargesBySubscriptionChargeId.get(subscriptionChargeId).get(0);
        if (!existingChargeLines.containsKey(subscriptionChargeId) && subscriptionCharge.getEndDate().isAfter(changeOrderRequest.getStartDate())) {
            negateSubscriptionCharge(
                changeOrder,
                ActionType.NONE,
                chargeIdToPlanMap,
                subscriptionCharge,
                changeOrderRequest.getStartDate(),
                0,
                Optional.empty()
            );
            addLineItemFromSubscriptionCharge(changeOrder, chargeIdToPlanMap, subscriptionCharge, changeOrderRequest.getStartDate());
        }
    }

    private void addLineItem(Order changeOrder, Map<String, Plan> chargeIdToPlanMap, OrderLineItem lineItem, SubscriptionCharge subscriptionCharge) {
        if (lineItem.getAction() == ActionType.REMOVE) {
            return;
        }

        var plan = chargeIdToPlanMap.get(lineItem.getChargeId());
        var charge = plan
            .getCharges()
            .stream()
            .filter(c -> lineItem.getChargeId().equals(c.getChargeId()))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException(String.format("Charge %s doesn't exist in Plan %s", lineItem.getChargeId(), plan.getId()))
            );
        OrderServiceHelper.updateFlatFeeQuantityInOrderLine(lineItem, charge);

        BigDecimal listUnitPrice = null;
        if (subscriptionCharge != null) {
            listUnitPrice = subscriptionCharge.getListUnitPrice();
        } else if (charge.isCustom()) {
            // if the charge is a custom charge, use the user entered unit price
            listUnitPrice = lineItem.getListUnitPrice();
        }

        boolean updatingZeroAmountSubscriptionCharge =
            featureService.isEnabled(Feature.UPDATE_ZERO_BASE_LINE_ITEM) &&
            subscriptionCharge != null &&
            hasZeroListAmount(subscriptionCharge, charge);

        List<DiscountDetail> subscriptionChargeDiscounts = getSubscriptionChargeDiscounts(subscriptionCharge);
        changeOrder
            .getLineItems()
            .add(
                createOrderLineItem(
                    changeOrder.getTenantId(),
                    changeOrder.getEntityId(),
                    lineItem.getAction(),
                    plan.getPlanId(),
                    null,
                    subscriptionCharge == null ? null : subscriptionCharge.getSubscriptionChargeGroupId(),
                    null,
                    lineItem.getBaseExternalSubscriptionChargeId(),
                    lineItem.getChargeId(),
                    lineItem.getQuantity(),
                    lineItem.getRank(),
                    listUnitPrice,
                    subscriptionCharge == null ? null : subscriptionCharge.getSellUnitPrice(),
                    BigDecimal.ZERO,
                    lineItem.getIsRamp(),
                    (subscriptionChargeDiscounts == null || updatingZeroAmountSubscriptionCharge)
                        ? lineItem.getDiscounts()
                        : subscriptionChargeDiscounts,
                    subscriptionCharge == null ? lineItem.getPredefinedDiscounts() : subscriptionCharge.getPredefinedDiscounts(),
                    lineItem.getAttributeReferences(),
                    (subscriptionCharge == null || updatingZeroAmountSubscriptionCharge)
                        ? lineItem.getPricingOverride()
                        : subscriptionCharge.getPricingOverride(),
                    (subscriptionCharge == null || updatingZeroAmountSubscriptionCharge)
                        ? lineItem.getListPriceOverrideRatio()
                        : subscriptionCharge.getListPriceOverrideRatio(),
                    lineItem.getArrOverride(),
                    lineItem.getEffectiveDate(),
                    lineItem.getEndDate(),
                    lineItem.getCustomFields()
                )
            );
    }

    private List<DiscountDetail> getSubscriptionChargeDiscounts(SubscriptionCharge subscriptionCharge) {
        if (subscriptionCharge == null) {
            return null;
        }

        var discounts = subscriptionCharge.getDiscounts();
        var discountDetails = orderMapper.discountToDiscountDetail(discounts);
        return OrderDiscountService.populateDiscountIdsAndResetAmounts(discountDetails);
    }

    private void addLineItemFromSubscriptionCharge(
        Order changeOrder,
        Map<String, Plan> chargeIdToPlanMap,
        SubscriptionCharge subscriptionCharge,
        Instant effectiveDate
    ) {
        // if the subscription charge starts after the order effective date, use the line start date, otherwise use the order effective date
        Instant lineEffectiveDate = effectiveDate.isBefore(subscriptionCharge.getStartDate()) ? subscriptionCharge.getStartDate() : effectiveDate;
        changeOrder
            .getLineItems()
            .add(
                createOrderLineItem(
                    changeOrder.getTenantId(),
                    changeOrder.getEntityId(),
                    ActionType.NONE,
                    chargeIdToPlanMap.get(subscriptionCharge.getChargeId()).getPlanId(),
                    null,
                    subscriptionCharge.getSubscriptionChargeGroupId(),
                    null,
                    subscriptionCharge.getSubscriptionChargeId(),
                    subscriptionCharge.getChargeId(),
                    subscriptionCharge.getQuantity(),
                    0,
                    subscriptionCharge.getListUnitPrice(),
                    subscriptionCharge.getSellUnitPrice(),
                    BigDecimal.ZERO,
                    subscriptionCharge.getIsRamp(),
                    getSubscriptionChargeDiscounts(subscriptionCharge),
                    subscriptionCharge.getPredefinedDiscounts(),
                    subscriptionCharge.getAttributeReferences(),
                    subscriptionCharge.getPricingOverride(),
                    subscriptionCharge.getListPriceOverrideRatio(),
                    subscriptionCharge.getArrOverride(),
                    lineEffectiveDate,
                    subscriptionCharge.getEndDate(),
                    List.of()
                )
            );
    }

    private void addLineItemsForCancel(Order changeOrder, Subscription subscription, Map<String, Plan> chargeIdToPlanMap, Order changeOrderRequest) {
        List<SubscriptionCharge> charges = subscription.getCharges().stream().sorted(Comparator.comparing(SubscriptionCharge::getRank)).toList();
        Map<String, BigDecimal> creditableAmountMap = Optional.ofNullable(changeOrderRequest.getCreditableAmounts())
            .orElse(Collections.emptyList())
            .stream()
            .collect(Collectors.toMap(CreditableAmount::getSubscriptionChargeId, CreditableAmount::getCreditableAmount));
        AtomicInteger i = new AtomicInteger(1);
        charges.forEach(c ->
            negateSubscriptionCharge(
                changeOrder,
                ActionType.REMOVE,
                chargeIdToPlanMap,
                c,
                changeOrderRequest.getStartDate(),
                i.getAndIncrement(),
                Optional.ofNullable(creditableAmountMap.get(c.getSubscriptionChargeId()))
            )
        );
    }

    private void negateSubscriptionCharge(
        Order changeOrder,
        ActionType action,
        Map<String, Plan> chargeIdToPlanMap,
        SubscriptionCharge subscriptionCharge,
        Instant effectiveStartDate,
        int rank,
        Optional<BigDecimal> creditAmount
    ) {
        var effectiveDate = effectiveStartDate.isAfter(subscriptionCharge.getStartDate()) ? effectiveStartDate : subscriptionCharge.getStartDate();
        boolean shouldShowNegativeLine = subscriptionCharge.getEndDate().isAfter(effectiveDate);
        if (
            featureService.isEnabled(Feature.ADD_NEGATIVE_ARR_FOR_CANCEL_ON_LAST_PLUS_ONE_DAY) &&
            changeOrder.getOrderType() == OrderType.CANCEL &&
            subscriptionCharge.getEndDate().getEpochSecond() == changeOrder.getEndDate().getEpochSecond() &&
            effectiveDate.getEpochSecond() == changeOrder.getEndDate().getEpochSecond()
        ) {
            shouldShowNegativeLine = !subscriptionCharge.getEndDate().isBefore(effectiveDate); // include on or after
        }

        if (shouldShowNegativeLine) {
            OrderLineItem newOrderLineItem = createOrderLineItem(
                changeOrder.getTenantId(),
                changeOrder.getEntityId(),
                action,
                chargeIdToPlanMap.get(subscriptionCharge.getChargeId()).getPlanId(),
                subscriptionCharge.getSubscriptionChargeId(),
                subscriptionCharge.getSubscriptionChargeGroupId(),
                subscriptionCharge.getId(),
                subscriptionCharge.getSubscriptionChargeId(),
                subscriptionCharge.getChargeId(),
                -1 * subscriptionCharge.getQuantity(),
                rank,
                subscriptionCharge.getListUnitPrice(),
                subscriptionCharge.getSellUnitPrice(),
                BigDecimal.ZERO,
                subscriptionCharge.getIsRamp(),
                getSubscriptionChargeDiscounts(subscriptionCharge),
                subscriptionCharge.getPredefinedDiscounts(),
                subscriptionCharge.getAttributeReferences(),
                subscriptionCharge.getPricingOverride(),
                subscriptionCharge.getListPriceOverrideRatio(),
                subscriptionCharge.getArrOverride() != null ? subscriptionCharge.getArrOverride().negate() : null,
                effectiveDate,
                subscriptionCharge.getEndDate(),
                List.of()
            );
            populateOneTimeChargeDebookRefund(
                changeOrder,
                newOrderLineItem,
                subscriptionCharge.getChargeId(),
                action,
                creditAmount,
                chargeIdToPlanMap
            );
            changeOrder.getLineItems().add(newOrderLineItem);
        }
    }

    private void populateOneTimeChargeDebookRefund(
        Order order,
        OrderLineItem newOrderLineItem,
        String chargeId,
        ActionType actionType,
        Optional<BigDecimal> creditAmount,
        Map<String, Plan> chargeIdToPlanMap
    ) {
        if (!featureService.isEnabled(Feature.ONE_TIME_CHARGE_DEBOOK)) {
            return;
        }

        if (!ALLOWED_ORDER_TYPES_FOR_OTC_DEBOOK_REFUND.contains(order.getOrderType())) {
            return;
        }

        Map<String, Charge> chargeIdMap = chargeIdToPlanMap
            .get(chargeId)
            .getCharges()
            .stream()
            .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
        if (!isOneTimeChargeRefundEligible(chargeId, actionType, creditAmount, chargeIdMap)) {
            return;
        }
        // Approach #1: Allow the customer to enter the refund amount, and let the system determine
        // whether the amount should be positive or negative based on the original charged amount.
        // Approach #2: Allow the customer to enter the refund amount exactly as is. The system assumes
        // that if the original charge was positive, the refund amount will be negative, and vice versa.
        newOrderLineItem.setAmount(creditAmount.orElseThrow()); // Approach #2
    }

    // todo: convert this into a builder pattern
    private OrderLineItem createOrderLineItem(
        String tenantId,
        String entityId,
        ActionType action,
        String planId,
        String externalSubscriptionChargeId,
        String subscriptionChargeGroupId,
        UUID subscriptionChargeId,
        String baseSubscriptionChargeId,
        String chargeId,
        long quantity,
        int rank,
        BigDecimal listUnitPrice,
        BigDecimal sellUnitPrice,
        BigDecimal discountAmount,
        boolean isRamp,
        List<DiscountDetail> discounts,
        List<TenantDiscountLineItem> orderDiscounts,
        List<AttributeReference> attributeReferences,
        PricingOverride pricingOverride,
        BigDecimal listPriceOverrideRatio,
        BigDecimal arrOverride,
        Instant startDate,
        Instant endDate,
        List<CustomFieldEntry> customFieldEntries
    ) {
        return new OrderLineItem(
            null,
            null,
            null,
            tenantId,
            entityId,
            null,
            rank,
            isRamp,
            action,
            planId,
            externalSubscriptionChargeId,
            subscriptionChargeGroupId,
            subscriptionChargeId,
            baseSubscriptionChargeId,
            chargeId,
            null,
            null,
            quantity,
            listUnitPrice,
            sellUnitPrice,
            BigDecimal.ZERO, //amount at the order line item level is set to 0 for all cases
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            discountAmount,
            discounts,
            orderDiscounts,
            attributeReferences,
            pricingOverride,
            listPriceOverrideRatio,
            null,
            null, // todo: populate
            null,
            null,
            startDate,
            endDate,
            customFieldEntries == null ? List.of() : customFieldEntries,
            arrOverride,
            null,
            null,
            null
        );
    }

    private void validateCancelOrderDetails(Order changeOrderRequest) {
        if (changeOrderRequest.getOrderType() == OrderType.CANCEL && CollectionUtils.isNotEmpty(changeOrderRequest.getLineItems())) {
            LOGGER.info("change order line items should be empty for cancel order");
            throw new IllegalArgumentException("change order line items should be empty for cancel order");
        }
    }

    private void validateUpdateOrderDetails(Order changeOrderRequest, Map<String, List<SubscriptionCharge>> subscriptionChargesMap) {
        if (changeOrderRequest.getOrderType() == OrderType.AMENDMENT && CollectionUtils.isEmpty(changeOrderRequest.getLineItems())) {
            LOGGER.info("line items list should not be empty for update order");
            throw new IllegalArgumentException("line items list should not be empty for update order");
        }

        var subscriptionChargesInChangeOrder = changeOrderRequest
            .getLineItemsHavingBaseSubscriptionCharge()
            .stream()
            .map(OrderLineItem::getBaseExternalSubscriptionChargeId)
            .collect(Collectors.toSet());

        subscriptionChargesInChangeOrder.removeAll(subscriptionChargesMap.keySet());
        if (!subscriptionChargesInChangeOrder.isEmpty()) {
            var errorMessage = String.format(
                "subscriptionChargeId(s) %s not found in subscription",
                String.join(", ", subscriptionChargesInChangeOrder)
            );
            LOGGER.info(errorMessage);
            throw new IllegalArgumentException(errorMessage);
        }
    }

    private void validatePlansAndCharges(Order changeOrderRequest, Map<String, Plan> plansMap) {
        if (CollectionUtils.isEmpty(changeOrderRequest.getLineItems())) {
            return;
        }

        for (var lineItem : changeOrderRequest.getLineItems()) {
            Optional<Plan> plan = Optional.ofNullable(plansMap.get(lineItem.getPlanId()));
            if (plan.isEmpty()) {
                String message = String.format("invalid plan id %s provided in the change order line item", lineItem.getPlanId());
                LOGGER.info(message);
                throw new IllegalArgumentException(message);
            }

            Optional<Charge> charge = plan.get().getCharges().stream().filter(c -> lineItem.getChargeId().equals(c.getChargeId())).findFirst();
            if (charge.isEmpty()) {
                String message = String.format("charge id %s does not exist", lineItem.getChargeId());
                LOGGER.info(message);
                throw new IllegalArgumentException(message);
            }

            if (lineItem.getAction() != ActionType.REMOVE) {
                OrderServiceHelper.validateLineItemQuantity(lineItem, charge.get(), changeOrderRequest.getOrderType());
            }
        }
    }

    public Order upsertChangeOrder(Order changeOrder, Boolean isDryRun, Boolean populateMissingLineItems) {
        if (changeOrder == null) {
            LOGGER.info("change order request object is null");
            throw new IllegalArgumentException("change order request object is null");
        }

        OrderServiceHelper.validateAndUpdateOpportunityInfo(changeOrder);

        Order resultOrder = changeOrder.getOrderId() == null
            ? createChangeOrder(changeOrder, isDryRun, populateMissingLineItems)
            : updateChangeOrder(changeOrder, isDryRun, populateMissingLineItems, false);

        if (!isDryRun) {
            resultOrder.setDocumentCustomContent(changeOrder.getDocumentCustomContent());
            OrderServiceHelper.processDocumentCustomContent(resultOrder, documentCustomContentService);
        }

        return resultOrder;
    }

    public Order updateChangeOrder(Order changeOrderRequest, Boolean isDryRun, Boolean populateMissingLineItems, boolean useUpdatedTargetVersion) {
        if (changeOrderRequest == null || changeOrderRequest.getOrderId() == null) {
            LOGGER.info("orderId is null");
            throw new IllegalArgumentException("orderId is null");
        }
        OrderServiceHelper.updateOrderOwner(changeOrderRequest, userService);

        Subscription currentSubscription = validateAndFetchSubscription(changeOrderRequest);
        changeOrderRequest.setEndDate(currentSubscription.getEndDate());
        validateChangeOrderRequest(changeOrderRequest, currentSubscription.getBillingAnchorDate());
        OrderServiceHelper.validateOrderCurrency(changeOrderRequest, currentSubscription.getCurrency());

        changeOrderRequest.setAccountId(currentSubscription.getAccountId());
        Account account = orderValidationService.validateAccountDetails(changeOrderRequest);
        ChangeOrderContext changeOrderContext = makeChangeOrderContext(changeOrderRequest, currentSubscription, account, isDryRun);
        orderDocumentTemplateService.filterOutPredefinedTermsBroughtByPlans(changeOrderRequest, changeOrderContext.getPlansInOrder());
        validateOneTimeChargeDebookRefund(changeOrderRequest, changeOrderContext);
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var result = orderDAO.getOrderByOrderId(dslContext, changeOrderRequest.getOrderId());
        if (result.isEmpty()) {
            var message = String.format("invalid order id: %s", changeOrderRequest.getId());
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        var currentOrder = result.get();
        validateUpdateOrder(currentOrder, useUpdatedTargetVersion);
        changeOrderRequest.setStatus(currentOrder.getStatus());

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        OrderServiceHelper.updateOrderStatusByExpiryDate(changeOrderRequest, timeZone);

        int targetVersion = useUpdatedTargetVersion ? changeOrderRequest.getSubscriptionTargetVersion() : currentOrder.getSubscriptionTargetVersion();

        int nextSubscriptionVersion = subscriptionGetService.getNextSubscriptionVersion(currentSubscription.getSubscriptionId());

        if (targetVersion != nextSubscriptionVersion) {
            throw new InvalidInputException("change order cannot be updated as subscription has changed from the time this is created");
        }

        changeOrderRequest.setSubscriptionTargetVersion(targetVersion);
        changeOrderRequest.setAccountId(currentOrder.getAccountId());
        changeOrderRequest.setId(currentOrder.getId());
        changeOrderRequest.setEntityId(currentOrder.getEntityId());

        addDatesToLineItems(changeOrderRequest, changeOrderContext);
        validateOrderLineChargesForUpdates(changeOrderRequest, changeOrderContext);

        if (!isDryRun) {
            checkProductRules(changeOrderRequest, changeOrderContext);
        }
        validatePaymentTermCannotBeChangedDuringAmendment(changeOrderRequest, changeOrderContext);
        Order changeOrder = toOrderUpdateFromRequest(changeOrderRequest, currentOrder, changeOrderContext, populateMissingLineItems);

        Order resultOrder = dslContext.transactionResult(configuration ->
            updateChangeOrderTransaction(configuration, changeOrder, isDryRun, changeOrderContext)
        );

        if (!isDryRun) {
            hubSpotJobQueueService.dispatchOrderSync(resultOrder);
            salesforceJobQueueService.dispatchOrderSync(resultOrder);
        }
        return resultOrder;
    }

    private void validateUpdateOrder(Order currentOrder, boolean updateSubscriptionTargetVersion) {
        OrderStatus orderStatus = currentOrder.getStatus();
        if (orderStatus == OrderStatus.EXECUTED) {
            throw new InvalidInputException("Cannot update executed order");
        }

        if (updateSubscriptionTargetVersion && currentOrder.getOrderType() == OrderType.AMENDMENT) {
            // if rebasing amendment, then allow order updates
            return;
        }

        if (orderStatus != OrderStatus.DRAFT && orderStatus != OrderStatus.EXPIRED) {
            throw new IllegalArgumentException(String.format("Cannot update orders in %s status", currentOrder.getStatus()));
        }
    }

    // todo: merge with createChangeOrder
    private Order toOrderUpdateFromRequest(
        Order changeOrderRequest,
        Order currentOrder,
        ChangeOrderContext changeOrderContext,
        Boolean populateMissingLineItems
    ) {
        Subscription currentSubscription = changeOrderContext.getSubscription();
        currentOrder.setStartDate(changeOrderRequest.getStartDate());
        currentOrder.setShippingContactId(changeOrderRequest.getShippingContactId());
        currentOrder.setBillingContactId(changeOrderRequest.getBillingContactId());

        currentOrder.setName(changeOrderRequest.getName());
        currentOrder.setOwnerId(changeOrderRequest.getOwnerId());
        currentOrder.setEntityId(currentSubscription.getEntityId());
        currentOrder.setSfdcOpportunityId(changeOrderRequest.getSfdcOpportunityId());
        currentOrder.setSfdcOpportunityName(changeOrderRequest.getSfdcOpportunityName());
        currentOrder.setSfdcOpportunityType(changeOrderRequest.getSfdcOpportunityType());
        currentOrder.setSfdcOpportunityStage(changeOrderRequest.getSfdcOpportunityStage());
        currentOrder.setDocumentMasterTemplateId(changeOrderRequest.getDocumentMasterTemplateId());
        currentOrder.setOrderFormTemplateIds(changeOrderRequest.getOrderFormTemplateIds());
        currentOrder.setAutoRenew(changeOrderRequest.getAutoRenew());
        currentOrder.setApprovalSegmentId(changeOrderRequest.getApprovalSegmentId());
        currentOrder.setPurchaseOrderNumber(changeOrderRequest.getPurchaseOrderNumber());
        currentOrder.setPurchaseOrderRequiredForInvoicing(changeOrderRequest.getPurchaseOrderRequiredForInvoicing());
        currentOrder.setStatus(changeOrderRequest.getStatus());
        currentOrder.setExpiresOn(changeOrderRequest.getExpiresOn());
        currentOrder.setDocumentCustomContent(changeOrderRequest.getDocumentCustomContent());
        currentOrder.setAttachmentId(changeOrderRequest.getAttachmentId());
        currentOrder.setSubscriptionTargetVersion(changeOrderRequest.getSubscriptionTargetVersion());
        // custom fields will be copied over from the request
        currentOrder.setCustomFields(changeOrderRequest.getCustomFields());
        currentOrder.setCustomBillingSchedule(changeOrderRequest.getCustomBillingSchedule());
        currentOrder.setSubscriptionDurationModel(currentSubscription.getDurationModel());

        if (currentOrder.getOrderType() == OrderType.AMENDMENT) {
            currentOrder.setPredefinedDiscounts(changeOrderRequest.getPredefinedDiscounts());
        }

        // Remove existing lines and populate them fresh again
        currentOrder.setLineItems(new ArrayList<>());
        addChangeOrderLineItems(changeOrderRequest, changeOrderContext, currentOrder, populateMissingLineItems);

        return currentOrder;
    }

    private Order updateChangeOrderTransaction(
        Configuration configuration,
        Order changeOrder,
        Boolean isDryRun,
        ChangeOrderContext changeOrderContext
    ) {
        var dslContext = DSL.using(configuration);
        OrderServiceHelper.populateOrderAndOrderLineIds(orderIdGenerator, changeOrder);
        OrderServiceHelper.fillNetEffectOrderLines(changeOrder);

        // order is important here, customization needs to run before preview to allow for
        // several actions to be previewed with invoice preview
        runOrderCreationCustomization(changeOrder, changeOrderContext, isDryRun);

        updateOrderLineItemWithCurrencyConversionRateId(changeOrder, changeOrderContext);

        InvoicePreview invoicePreview = invoiceService.previewInvoiceByOrderPeriodForOrderLinesCalculation(changeOrder);
        OrderServiceHelper.updateOrderAmountsFromInvoice(changeOrder, invoicePreview);
        OrderServiceHelper.validateOrderTotalAmount(changeOrder.getTotalAmount());

        Map<String, BigDecimal> orderLineItemAnnualizedAmounts = metricsService.getOrderLineItemAnnualizedAmounts(
            changeOrder,
            changeOrderContext.getAllCharges()
        );
        OrderServiceHelper.updateOrderLineItemAnnualizedAmounts(changeOrder, orderLineItemAnnualizedAmounts);

        if (EvergreenUtils.isEvergreenOrder(changeOrder)) {
            Map<String, InvoiceItemAmounts> orderLineItemBillingCycleAmounts = metricsService.getOrderLineItemBillingCycleAmounts(
                changeOrder,
                changeOrderContext.getChargesInOrder()
            );
            OrderServiceHelper.updateOrderTotalAndOrderLineItemAmountsForEvergreenOrder(changeOrder, orderLineItemBillingCycleAmounts);
        }

        orderCustomBillingService.validateAndSetCustomBillingDetailsInOrder(changeOrder, isDryRun);

        if (!isDryRun) {
            String tenantId = Optional.ofNullable(changeOrder.getTenantId()).orElse(tenantIdProvider.provideTenantIdString());
            Optional<Opportunity> opportunity = opportunityService.upsertOpportunityForOrderIfNeeded(dslContext, changeOrder, tenantId, true);
            boolean isPrimaryOrderForOpportunity = changeOrder.getIsPrimaryOrderForSfdcOpportunity();
            List<String> orderTemplateIds = changeOrder.getOrderFormTemplateIds();
            CustomField orderCustomFields = changeOrder.getCustomFields();
            Map<Integer, List<CustomFieldEntry>> orderLineCustomFieldsByOrderLineItemRank = OrderServiceHelper.getCustomFieldsByOrderLineItemRankMap(
                changeOrder
            );
            OrderServiceHelper.calculateAndApplyTaxEstimates(changeOrder, taxService, taxRateGetService, accountGetService, productCatalogGetService);
            CustomBillingSchedule customBillingSchedule = changeOrder.getCustomBillingSchedule();

            changeOrder = orderDAO.updateOrder(dslContext, changeOrder);
            changeOrder.setOrderFormTemplateIds(orderTemplateIds);
            // ⚠️order custom fields get overwritten while saving, so we need to add it back here ⚠️
            changeOrder.setCustomFields(orderCustomFields);
            OrderServiceHelper.addCustomFieldsBackToOrderLineItems(changeOrder, orderLineCustomFieldsByOrderLineItemRank, customizationService);

            OrderServiceHelper.fillNetEffectOrderLines(changeOrder);
            OrderServiceHelper.addSfdcFieldsToOrder(changeOrder, opportunity, isPrimaryOrderForOpportunity);

            List<DocumentTemplate> orderTemplates = documentTemplateGetService.getDocumentTemplatesByTemplateIds(orderTemplateIds);
            orderDocumentTemplateService.addPredefinedTermsForOrder(changeOrder, dslContext, tenantId, orderTemplates, true);

            orderCustomBillingService.saveOrInvalidateCustomBillingSchedule(
                dslContext,
                changeOrder,
                changeOrderContext.getChargesInOrder(),
                customBillingSchedule
            );
        }

        // ❌❌❌ The custom field persistence is non-transactional and can fail partially ❌❌❌
        // ❌❌❌ even though it called inside the transaction method it does nothing transactional ❌❌❌
        OrderServiceHelper.persistCustomFields(changeOrder, isDryRun, customFieldService);

        return changeOrder;
    }

    public Order generateCancelOrder(String subscriptionId, Optional<Instant> effectiveDate) {
        if (StringUtils.isBlank(subscriptionId)) {
            throw new IllegalArgumentException("subscriptionId is null");
        }

        var subscription = subscriptionGetService.getSubscription(subscriptionId);
        OrderServiceHelper.checkForCanceledSubscription(subscription);

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Optional<Instant> orderExpiresOn = OrderServiceHelper.getOrderExpiresOn(tenantSettingService, timeZone);

        var cancelOrder = new Order();
        cancelOrder.setExternalSubscriptionId(subscriptionId);
        cancelOrder.setShippingContactId(subscription.getShippingContactId());
        cancelOrder.setBillingContactId(subscription.getBillingContactId());

        Instant cancelOrderStartDate = getAndValidateCancelOrderStartDate(subscription, effectiveDate).orElse(null);
        cancelOrder.setStartDate(cancelOrderStartDate);
        cancelOrder.setOrderType(OrderType.CANCEL);
        cancelOrder.setAutoRenew(false); // cancelled order cannot be auto-renewed
        cancelOrder.setExpiresOn(orderExpiresOn.orElse(null));
        cancelOrder = createChangeOrder(cancelOrder, true, false);

        //populate creditable amounts
        List<SubscriptionCharge> subscriptionCharges = subscription.getCharges();
        List<String> chargeIds = subscriptionCharges.stream().map(SubscriptionCharge::getChargeId).collect(Collectors.toList());
        populateCreditAmounts(cancelOrder, subscriptionCharges, chargeIds);
        return cancelOrder;
    }

    private Optional<Instant> getAndValidateCancelOrderStartDate(Subscription subscription, Optional<Instant> effectiveDateOverride) {
        Optional<Instant> latestUsageInvoicedDate = OrderServiceHelper.getLatestUsageInvoiceForSubscription(
            subscription,
            invoiceService,
            OrderServiceHelper.createChargeMap(null, subscription, productCatalogGetService)
        );
        if (latestUsageInvoicedDate.isEmpty()) {
            return effectiveDateOverride;
        }

        if (effectiveDateOverride.isPresent() && effectiveDateOverride.get().isBefore(latestUsageInvoicedDate.get())) {
            throw new InvalidInputException(
                String.format(
                    "cancel order cannot start at %s because usage has been invoiced at %s",
                    effectiveDateOverride.get(),
                    latestUsageInvoicedDate.get()
                )
            );
        }

        return latestUsageInvoicedDate;
    }

    private Instant generateAmendmentStartDate(Subscription subscription) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Instant amendmentDate = DateTimeConverter.getStartOfCurrentDay(Instant.now(), timeZone);
        if (amendmentDate.isBefore(subscription.getStartDate()) || amendmentDate.isAfter(subscription.getEndDate())) {
            amendmentDate = subscription.getStartDate();
        }

        return amendmentDate;
    }

    public Boolean isAmendmentCurrent(String orderId) {
        Validator.validateStringNotBlank(orderId, "orderId is required.");
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Order order = orderDAO.getOrderByOrderId(dslContext, orderId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ORDER, orderId));

        if (order.getOrderType() != OrderType.AMENDMENT) {
            return true;
        }

        int nextSubscriptionVersion = subscriptionGetService.getNextSubscriptionVersion(order.getExternalSubscriptionId());

        return nextSubscriptionVersion == order.getSubscriptionTargetVersion();
    }

    public Order generateDraftAmendment(String subscriptionId) {
        if (StringUtils.isBlank(subscriptionId)) {
            LOGGER.info("subscriptionId is null");
            throw new IllegalArgumentException("subscriptionId is null");
        }

        var subscription = subscriptionGetService.getSubscription(subscriptionId);
        OrderServiceHelper.checkForCanceledSubscription(subscription);

        return generateDraftAmendmentFromSubscription(subscription);
    }

    private Order generateDraftAmendmentFromSubscription(Subscription subscription) {
        long amendmentDate = generateAmendmentStartDate(subscription).getEpochSecond();
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Optional<Instant> orderExpiresOn = OrderServiceHelper.getOrderExpiresOn(tenantSettingService, timeZone);
        CustomField customFields = customFieldService.getEmptyHydratedCustomFieldForParentObjectType(CustomFieldParentType.ORDER);

        var order = new Order(
            null,
            null,
            null,
            null,
            subscription.getEntityId(),
            null,
            subscription.getAccountId(),
            subscription.getResellerAccountId().orElse(null),
            OrderType.AMENDMENT,
            subscription.getCurrency(),
            subscription.getPaymentTerm(),
            null,
            subscription.getSubscriptionId(),
            subscription.getShippingContactId(),
            subscription.getBillingContactId(),
            subscription.getPredefinedDiscounts(),
            Lists.newArrayList(),
            Lists.newArrayList(),
            Instant.ofEpochSecond(amendmentDate),
            subscription.getEndDate(),
            subscription.getBillingAnchorDate(),
            subscription.getTermLength(),
            subscription.getBillingCycle(),
            subscription.getBillingTerm(),
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            null,
            BigDecimal.ZERO,
            OrderStatus.DRAFT,
            null,
            null,
            null,
            null,
            subscription.getRampInterval(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            0,
            null,
            0,
            null,
            false,
            subscription.getAutoRenew(),
            null,
            customFields,
            null,
            true,
            orderExpiresOn.orElse(null),
            null,
            OrderSource.USER,
            OrderStartDateType.FIXED,
            null,
            Optional.empty(),
            subscription.getDurationModel(),
            null
        );

        order.setSubscriptionTargetVersion(subscriptionGetService.getNextSubscriptionVersion(subscription.getSubscriptionId()));

        List<SubscriptionCharge> charges = subscription.getCharges().stream().sorted(Comparator.comparing(SubscriptionCharge::getRank)).toList();
        var chargeIds = charges.stream().map(SubscriptionCharge::getChargeId).collect(Collectors.toList());
        var plans = productCatalogGetService.getPlansFromChargeIds(chargeIds);
        var chargeIdPlanIdMap = OrderServiceHelper.getChargeIdPlanIdMap(plans);

        int rank = 1;
        for (SubscriptionCharge charge : charges) {
            if (charge.getEndDate().isAfter(order.getStartDate())) {
                order
                    .getLineItems()
                    .add(generateAmendmentOrderLineFromCharge(subscription.getEntityId(), charge, chargeIdPlanIdMap, order.getStartDate(), rank++));
            }
        }

        populateCreditAmounts(order, charges, chargeIds);

        if (featureService.isEnabled(Feature.POPULATE_MISSING_ITEMS)) {
            missingOrderChargesService.findAndAddMissingOrderCharges(order);
        }

        order.setLineItemsNetEffect(Lists.newArrayList());

        orderDocumentTemplateService.consolidatePlanTemplates(order);

        return order;
    }

    private void populateCreditAmounts(Order order, List<SubscriptionCharge> charges, List<String> chargeIds) {
        Map<String, Charge> chargeMap = productCatalogGetService.getChargeMapByChargeIds(chargeIds);
        List<CreditableAmount> creditableAmounts = new ArrayList<>();
        charges.forEach(subscriptionCharge -> {
            Charge charge = chargeMap.get(subscriptionCharge.getChargeId());
            BigDecimal maxCreditableAmount = subscriptionOrderService.getCreditableAmountFromSubscriptionChargeId(
                subscriptionCharge.getSubscriptionChargeId()
            );
            BigDecimal creditAmount = BigDecimal.ZERO;
            if (subscriptionCharge.getStartDate().equals(order.getStartDate())) {
                creditAmount = maxCreditableAmount.negate();
            }
            if (charge.getType() == ChargeType.ONE_TIME && charge.isCreditable() && Objects.nonNull(subscriptionCharge.getSubscriptionChargeId())) {
                CreditableAmount creditableAmount = new CreditableAmount();
                creditableAmount.setSubscriptionChargeId(subscriptionCharge.getSubscriptionChargeId());
                creditableAmount.setCreditableAmount(creditAmount);
                creditableAmount.setMaxCreditableAmount(maxCreditableAmount.negate());
                creditableAmounts.add(creditableAmount);
            }
        });
        order.setCreditableAmounts(creditableAmounts);
    }

    private OrderLineItem generateAmendmentOrderLineFromCharge(
        String entityId,
        SubscriptionCharge charge,
        Map<String, String> chargeIdPlanIdMap,
        Instant startDate,
        int rank
    ) {
        Instant lineItemStartDate = charge.getStartDate();
        if (startDate.isAfter(charge.getStartDate()) && startDate.isBefore(charge.getEndDate())) {
            lineItemStartDate = startDate;
        }

        return new OrderLineItem(
            null,
            null,
            null,
            null,
            entityId,
            null,
            rank,
            charge.getIsRamp(),
            ActionType.NONE,
            chargeIdPlanIdMap.get(charge.getChargeId()),
            null,
            charge.getSubscriptionChargeGroupId(),
            null,
            charge.getSubscriptionChargeId(),
            charge.getChargeId(),
            null,
            null, //TODO revisit this as FX progresses
            charge.getQuantity(),
            charge.getListUnitPrice(),
            charge.getSellUnitPrice(),
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            getSubscriptionChargeDiscounts(charge),
            charge.getPredefinedDiscounts(),
            charge.getAttributeReferences(),
            charge.getPricingOverride(),
            charge.getListPriceOverrideRatio(),
            null,
            null,
            null,
            null,
            lineItemStartDate,
            charge.getEndDate(),
            List.of(),
            null,
            null,
            null,
            null
        );
    }

    private void checkProductRules(Order order, ChangeOrderContext changeOrderContext) {
        if (!featureService.isEnabled(Feature.PRODUCT_RULES_DEMO)) {
            return;
        }

        if (order.getOrderType() == OrderType.CANCEL) {
            // don't check for product inclusion / exclusion when cancelling
            return;
        }

        Set<String> orderPlanIds = changeOrderContext.getPlansInOrder().stream().map(Plan::getPlanId).collect(Collectors.toSet());
        Set<String> subscriptionPlanIds = changeOrderContext.getPlansInSubscription().stream().map(Plan::getPlanId).collect(Collectors.toSet());

        OrderServiceHelper.checkProductRules(featureService, catalogRelationshipGetService, orderPlanIds, subscriptionPlanIds);
    }

    private void runOrderCreationCustomization(Order order, ChangeOrderContext changeOrderContext, Boolean isDryRun) {
        if (order.shouldSkipOrderCreationCustomization()) {
            LOGGER.info("Skipping order creation customization for order: ", order);
            return;
        }

        Optional<User> currentUserOptional = CurrentUserProvider.getSubskribeUserId().flatMap(userService::getUserOptional);

        Account account = changeOrderContext.getOrderAccount();
        Map<String, Plan> allPlansMap = changeOrderContext.allPlansMap();
        Map<String, Charge> allChargesMap = changeOrderContext.allChargesMap();
        Map<String, Product> allProductsMap = changeOrderContext.allProductsMap();

        Map<String, Product> relevantProducts = allPlansMap
            .values()
            .stream()
            .map(Plan::getProductId)
            .distinct()
            .map(allProductsMap::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Product::getProductId, Function.identity()));

        OrderCreationCustomizationInput input = ImmutableOrderCreationCustomizationInput.builder()
            .order(order)
            .account(account)
            .accountAddress(
                StringUtils.isBlank(account.getAddressId()) ? Optional.empty() : accountGetService.getAccountAddress(account.getAddressId())
            )
            .productMap(relevantProducts)
            .planMap(allPlansMap)
            .chargeMap(allChargesMap)
            .planCustomFields(changeOrderContext.getPlanCustomFields())
            .chargeCustomFields(changeOrderContext.getChargeCustomFields())
            .dryRun(BooleanUtils.isTrue(isDryRun))
            .currentUser(currentUserOptional)
            .shippingContact(changeOrderContext.getShippingContact())
            .billingContact(changeOrderContext.getBillingContact())
            .billingContactAccount(changeOrderContext.getBillingContactAccount())
            .timeZoneId(tenantSettingService.getTenantSetting().getDefaultTimeZone().toZoneId())
            .build();
        customizationService.runOrderCreationCustomization(input);
    }

    private void updateOrderLineItemWithCurrencyConversionRateId(Order changeOrder, ChangeOrderContext changeOrderContext) {
        Subscription subscription = changeOrderContext.getSubscription();
        Map<String, SubscriptionCharge> subscriptionChargeIdMap = subscription
            .getCharges()
            .stream()
            .collect(Collectors.toMap(SubscriptionCharge::getSubscriptionChargeId, Function.identity()));

        List<OrderLineItem> lineItemsHavingBaseSubscriptionCharge = changeOrder.getLineItemsHavingBaseSubscriptionCharge();
        // all other lines will have currency conversion rate id set based on existing subscription charge
        lineItemsHavingBaseSubscriptionCharge.forEach(li -> {
            SubscriptionCharge subscriptionCharge = subscriptionChargeIdMap.get(li.getBaseExternalSubscriptionChargeId());
            if (subscriptionCharge != null) {
                li.setCurrencyConversionRateId(subscriptionCharge.getCurrencyConversionRateId());
            }
        });

        // Lines with action ADD will have currency conversion rate id set based on amendment start date
        updateNewOrderLineItemWithCurrencyConversionRateId(changeOrder, changeOrderContext);
    }

    private void updateNewOrderLineItemWithCurrencyConversionRateId(Order changeOrder, ChangeOrderContext changeOrderContext) {
        if (featureService.isEnabled(Feature.FOREIGN_EXCHANGE)) {
            Account account = changeOrderContext.getOrderAccount();
            String accountCurrencyCode = Objects.nonNull(changeOrder.getCurrency())
                ? changeOrder.getCurrency().getCurrencyCode()
                : account.getCurrency().getCurrencyCode();
            Map<String, Plan> planIdPlanMap = changeOrderContext
                .getAllPlans()
                .stream()
                .collect(Collectors.toMap(Plan::getPlanId, Function.identity()));
            Map<String, Charge> chargeIdChargeMap = changeOrderContext
                .getAllCharges()
                .stream()
                .collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

            List<OrderLineItem> addLines = changeOrder
                .getLineItems()
                .stream()
                .filter(li -> li.getAction() == ActionType.ADD)
                .collect(Collectors.toList());
            addLines.forEach(orderLineItem -> {
                if (StringUtils.isNotBlank(orderLineItem.getPlanId())) {
                    Plan plan = planIdPlanMap.get(orderLineItem.getPlanId());
                    Charge charge = chargeIdChargeMap.get(orderLineItem.getChargeId());
                    if (Objects.nonNull(plan) && !plan.getCurrency().getCurrencyCode().equalsIgnoreCase(accountCurrencyCode) && !charge.isCustom()) {
                        CurrencyConversionRate currencyConversionRate =
                            currencyConversionRateGetService.getConversionRateForCurrencyPairByEffectiveDate(
                                plan.getCurrency().getCurrencyCode(),
                                accountCurrencyCode,
                                changeOrder.getStartDate()
                            );
                        if (currencyConversionRate == null) {
                            TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
                            throw new InvalidInputException(
                                String.format(
                                    "There is no %s > %s currency conversion rate found for %s effective %s. To save the order, " +
                                    "request your Admin create a conversion rate for %s > %s effective %s, or remove %s.",
                                    plan.getCurrency().getCurrencyCode(),
                                    accountCurrencyCode,
                                    plan.getPlanId(),
                                    OrderServiceHelper.formatInstant(changeOrder.getStartDate(), timeZone),
                                    plan.getCurrency().getCurrencyCode(),
                                    accountCurrencyCode,
                                    OrderServiceHelper.formatInstant(changeOrder.getStartDate(), timeZone),
                                    plan.getPlanId()
                                )
                            );
                        }
                        orderLineItem.setCurrencyConversionRateId(UUID.fromString(currencyConversionRate.getId()));
                    }
                }
            });
        }
    }

    private void validatePaymentTermCannotBeChangedDuringAmendment(Order changeOrderRequest, ChangeOrderContext changeOrderContext) {
        if (changeOrderRequest.getOrderType() != OrderType.AMENDMENT && changeOrderRequest.getOrderType() != OrderType.CANCEL) {
            return;
        }
        if (
            Objects.nonNull(changeOrderRequest.getPaymentTerm()) &&
            !changeOrderRequest.getPaymentTerm().equals(changeOrderContext.getSubscription().getPaymentTerm())
        ) {
            throw new InvalidInputException("Payment term cannot be changed during amendment/cancellation");
        }
    }

    private void validateOneTimeChargeDebookRefund(Order changeOrderRequest, ChangeOrderContext changeOrderContext) {
        if (!featureService.isEnabled(Feature.ONE_TIME_CHARGE_DEBOOK)) {
            return;
        }

        Map<String, Charge> chargeMap = changeOrderContext.allChargesMap();
        Map<String, BigDecimal> creditableAmountMap = Optional.ofNullable(changeOrderRequest.getCreditableAmounts())
            .orElse(Collections.emptyList())
            .stream()
            .collect(Collectors.toMap(CreditableAmount::getSubscriptionChargeId, CreditableAmount::getCreditableAmount));

        if (changeOrderRequest.getOrderType() == OrderType.CANCEL) {
            // There will not be any line items for cancel order, it needs a separate validation
            validateCancelOrderCreditableAmounts(chargeMap, creditableAmountMap);
        } else {
            changeOrderRequest.getLineItems().forEach(lineItem -> validateLineItemRefund(lineItem, chargeMap, creditableAmountMap));
        }
    }

    private void validateCancelOrderCreditableAmounts(Map<String, Charge> chargeMap, Map<String, BigDecimal> creditableAmountMap) {
        creditableAmountMap.forEach((subscriptionChargeId, refundAmount) -> {
            // Retrieve the original line item for the subscription charge ID
            Optional<OrderLineItem> originalLineItemOpt = subscriptionOrderService.getOrderLineItemFromSubscriptionChargeId(subscriptionChargeId);

            // If the original line item doesn't exist, skip validation for this entry
            if (originalLineItemOpt.isEmpty()) {
                return;
            }

            OrderLineItem originalLineItem = originalLineItemOpt.get();

            // Check if the charge is eligible for a refund
            if (!isOneTimeChargeRefundEligible(originalLineItem.getChargeId(), ActionType.REMOVE, Optional.ofNullable(refundAmount), chargeMap)) {
                return;
            }

            // Retrieve the creditable amount for the subscription charge ID
            BigDecimal creditableAmount = subscriptionOrderService.getCreditableAmountFromSubscriptionChargeId(subscriptionChargeId);

            // Validate that the refund amount is within the acceptable range
            validateRefundAmountInRange(refundAmount, creditableAmount);

            // Validate that the charge itself is creditable
            validateChargeIsCreditable(chargeMap.get(originalLineItem.getChargeId()));
        });
    }

    private void validateLineItemRefund(OrderLineItem lineItem, Map<String, Charge> chargeMap, Map<String, BigDecimal> creditableAmountMap) {
        // Check if the line item is eligible for refund (i.e., REMOVE action, ONE_TIME charge, and non-zero amount)
        Optional<BigDecimal> creditableAmountOpt = Optional.ofNullable(creditableAmountMap.get(lineItem.getBaseExternalSubscriptionChargeId()));
        if (!isOneTimeChargeRefundEligible(lineItem.getChargeId(), lineItem.getAction(), creditableAmountOpt, chargeMap)) {
            return;
        }

        // Retrieve the original line item to compare amounts
        BigDecimal creditableAmount = subscriptionOrderService.getCreditableAmountFromSubscriptionChargeId(
            lineItem.getBaseExternalSubscriptionChargeId()
        );
        validateRefundAmountInRange(creditableAmountOpt.orElseThrow(), creditableAmount);

        // Validate that the charge associated with the line item is creditable
        validateChargeIsCreditable(chargeMap.get(lineItem.getChargeId()));
    }

    private boolean isOneTimeChargeRefundEligible(
        String chargeId,
        ActionType actionType,
        Optional<BigDecimal> amount,
        Map<String, Charge> chargeMap
    ) {
        // Check if the line item action is REMOVE, charge type is ONE_TIME, and the refund amount is valid
        Charge charge = chargeMap.get(chargeId);
        return (actionType == ActionType.REMOVE && charge.getType() == ChargeType.ONE_TIME && amount.isPresent());
    }

    private void validateRefundAmountInRange(BigDecimal refundAmount, BigDecimal originalAmount) {
        if (!Numbers.isWithinRange(refundAmount, BigDecimal.ZERO, originalAmount.negate())) {
            String message = String.format(
                "Originally charged amount was: %s, refund amount must be in the range of 0 to %s",
                Numbers.makeCurrencyScale(originalAmount),
                Numbers.makeCurrencyScale(originalAmount.negate())
            );
            throw new InvalidInputException(message);
        }
    }

    private void validateChargeIsCreditable(Charge charge) {
        if (Objects.isNull(charge) || !charge.isCreditable()) {
            throw new InvalidInputException("Cannot refund a non-creditable one-time charge");
        }
    }
}
