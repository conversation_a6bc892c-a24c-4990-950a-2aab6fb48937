package com.subskribe.billy.order.services;

import com.subskribe.billy.discount.FixedAmountDiscountConfiguration;
import com.subskribe.billy.discount.model.DiscountStatus;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.metrics.datadog.AllMetrics;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Discount;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.pecuniary.TenantDiscount;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import com.subskribe.billy.subscription.Subscription;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@AllMetrics
public class OrderDiscountService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderDiscountService.class);

    private static final String DISCOUNT_ID_PREFIX = "DISC_";

    private final OrderDAO orderDAO;

    @Inject
    public OrderDiscountService(OrderDAO orderDAO) {
        this.orderDAO = orderDAO;
    }

    public boolean discountUsedByOrder(String discountId) {
        return orderDAO.discountUsedByOrderLineItem(discountId);
    }

    static void updatePredefinedDiscountAmounts(
        List<TenantDiscountLineItem> predefinedDiscounts,
        Map<String, BigDecimal> predefinedDiscountAmountMap
    ) {
        for (TenantDiscountLineItem predefinedDiscount : predefinedDiscounts) {
            if (predefinedDiscountAmountMap.containsKey(predefinedDiscount.getId())) {
                predefinedDiscount.setAmount(predefinedDiscountAmountMap.get(predefinedDiscount.getId()));
            }
        }
    }

    static void updateDiscountAmounts(List<DiscountDetail> discounts, Map<String, BigDecimal> inlineDiscountAmountMap) {
        for (DiscountDetail discount : discounts) {
            if (inlineDiscountAmountMap.containsKey(discount.getId())) {
                discount.setAmount(inlineDiscountAmountMap.get(discount.getId()));
            }
        }
    }

    static void validateDiscountList(OrderLineItem orderLineItem, FixedAmountDiscountConfiguration fixedAmountDiscountConfiguration) {
        List<DiscountDetail> discounts = orderLineItem.getDiscounts();
        if (CollectionUtils.isEmpty(discounts)) {
            orderLineItem.setDiscounts(List.of());
            return;
        }

        // remove any 0 discount (0% and $0 amount). 0 discounts have no effect on the order line item amount.
        List<DiscountDetail> filteredDiscounts = discounts.stream().filter(Discount::isNonZeroDiscount).collect(Collectors.toList());

        filteredDiscounts.forEach(discount -> validateDiscountValue(discount, fixedAmountDiscountConfiguration));

        // populate the discountIds for each of the discounts
        populateDiscountIdsAndResetAmounts(filteredDiscounts);

        // since discounts list has been filtered, it's now detached from the orderLineItem object. Set it back
        // todo: should this method return filtered and validated list of discount instead?
        orderLineItem.setDiscounts(filteredDiscounts);
    }

    static List<DiscountDetail> populateDiscountIdsAndResetAmounts(List<DiscountDetail> discountDetails) {
        if (discountDetails != null) {
            int i = 1;
            for (DiscountDetail discountDetail : discountDetails) {
                discountDetail.setId(DISCOUNT_ID_PREFIX + i++);
                discountDetail.setAmount(BigDecimal.ZERO);
            }
        }

        return discountDetails;
    }

    private static void validateDiscountValue(Discount discount, FixedAmountDiscountConfiguration fixedAmountDiscountConfiguration) {
        if (discount.getDiscountAmount() != null && !fixedAmountDiscountConfiguration.isEnabled()) {
            throw new UnsupportedOperationException("Fixed amount discount not supported");
        }

        if (discount.getPercent() != null && discount.getDiscountAmount() != null) {
            LOGGER.info("Discount contains percent and fixed amount");
            throw new IllegalArgumentException("Discount cannot contain both percent and fixed amount");
        }

        if (discount.getPercent() != null && (discount.getPercent().floatValue() < 0 || discount.getPercent().floatValue() > 1)) {
            LOGGER.info("percent value should be between 0 and 1");
            throw new IllegalArgumentException("percent value should be between 0 and 1");
        }

        if (discount.getDiscountAmount() != null && discount.getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0) {
            String errorMessage = "discount amount must be greater than 0";
            LOGGER.info(errorMessage);
            throw new IllegalArgumentException(errorMessage);
        }
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    static void validateOrderDiscounts(Order order, DiscountService discountService, Optional<Subscription> baseSubscription) {
        // When creating a new amendment/renewal, we should populate the predefinedDiscounts at the order level.
        if (baseSubscription.isPresent() && StringUtils.isBlank(order.getOrderId()) && CollectionUtils.isEmpty(order.getPredefinedDiscounts())) {
            order.setPredefinedDiscounts(baseSubscription.get().getPredefinedDiscounts());
            return;
        }

        validateOrderDiscounts(order, discountService, true);
    }

    static void validateOrderDiscounts(Order order, DiscountService discountService, boolean throwIfNotFound) {
        if (CollectionUtils.isEmpty(order.getPredefinedDiscounts()) && areAllOrderLineItemDiscountsMissing(order)) {
            return;
        }

        verifyPredefinedDiscountsOnLineItemsAndOnOrder(order);

        boolean throwIfDeprecated = throwIfNotFound && checkOrderTypeIsNewOrRenew(order);

        // todo: find a better way to alias types with the same names or push for renaming discounts
        List<com.subskribe.billy.discount.model.Discount> discountsList = discountService.getDiscounts();
        Map<String, com.subskribe.billy.discount.model.Discount> discountsMap = discountsList
            .stream()
            .collect(Collectors.toMap(com.subskribe.billy.discount.model.Discount::getDiscountId, Function.identity()));

        order
            .getPredefinedDiscounts()
            .forEach(discount -> validateAndUpdateDiscountDetails(discount, discountsMap.get(discount.getId()), throwIfNotFound, throwIfDeprecated));

        Map<String, TenantDiscount> orderDiscountsMap = order
            .getPredefinedDiscounts()
            .stream()
            .collect(Collectors.toMap(TenantDiscount::getId, Function.identity()));
        order
            .getLineItems()
            .forEach(orderLineItem ->
                validateOrderLineItemDiscounts(orderLineItem.getPredefinedDiscounts(), orderDiscountsMap, throwIfNotFound, throwIfDeprecated)
            );
    }

    private static boolean checkOrderTypeIsNewOrRenew(Order order) {
        return order.getOrderType() == OrderType.NEW || order.getOrderType() == OrderType.RENEWAL;
    }

    private static void verifyPredefinedDiscountsOnLineItemsAndOnOrder(Order order) {
        Set<String> discountIdsInLineItems = order
            .getLineItems()
            .stream()
            .map(OrderLineItem::getPredefinedDiscounts)
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(discounts -> discounts.stream().map(TenantDiscount::getId))
            .collect(Collectors.toSet());

        Set<String> discountsAtOrderLevel = CollectionUtils.isEmpty(order.getPredefinedDiscounts())
            ? Set.of()
            : order.getPredefinedDiscounts().stream().map(TenantDiscount::getId).collect(Collectors.toSet());
        discountIdsInLineItems.removeAll(discountsAtOrderLevel);

        if (CollectionUtils.isNotEmpty(discountIdsInLineItems)) {
            throw new IllegalArgumentException(
                "Following discounts are missing at the order level. Discount Ids: " + String.join(", ", discountIdsInLineItems)
            );
        }
    }

    private static boolean areAllOrderLineItemDiscountsMissing(Order order) {
        return (
            CollectionUtils.isEmpty(order.getLineItems()) ||
            order.getLineItems().stream().allMatch(lineItem -> CollectionUtils.isEmpty(lineItem.getPredefinedDiscounts()))
        );
    }

    private static void validateAndUpdateDiscountDetails(
        TenantDiscount orderDiscount,
        com.subskribe.billy.discount.model.Discount discountInTheSystem,
        boolean throwIfNotFound,
        boolean throwIfDeprecated
    ) {
        if (discountInTheSystem == null) {
            if (!throwIfNotFound) {
                return;
            }
            throw new IllegalArgumentException("Discount not found for id: " + orderDiscount.getId());
        }

        if (discountInTheSystem.getStatus() == DiscountStatus.DEPRECATED && throwIfDeprecated) {
            throw new IllegalArgumentException("Discount in deprecated state cannot be used");
        }

        orderDiscount.setName(discountInTheSystem.getName());
        orderDiscount.setDescription(discountInTheSystem.getDescription());
        orderDiscount.setType(discountInTheSystem.getType());
        orderDiscount.setStatus(discountInTheSystem.getStatus());
    }

    private static void validateOrderLineItemDiscounts(
        List<TenantDiscountLineItem> orderLineItemDiscounts,
        Map<String, TenantDiscount> orderDiscountsMap,
        boolean throwIfNotFound,
        boolean throwIfDeprecated
    ) {
        if (CollectionUtils.isEmpty(orderLineItemDiscounts)) {
            return;
        }

        orderLineItemDiscounts.forEach(discount ->
            validateAndUpdateTenantDiscountLineItem(discount, orderDiscountsMap.get(discount.getId()), throwIfNotFound, throwIfDeprecated)
        );
    }

    private static void validateAndUpdateTenantDiscountLineItem(
        TenantDiscountLineItem tenantDiscountLineItem,
        TenantDiscount tenantDiscount,
        boolean throwIfNotFound,
        boolean throwIfDeprecated
    ) {
        if (tenantDiscount == null) {
            if (!throwIfNotFound) {
                return;
            }
            throw new IllegalArgumentException("Discount not found on Order for id: " + tenantDiscountLineItem.getId());
        }

        if (tenantDiscount.getStatus() == DiscountStatus.DEPRECATED && throwIfDeprecated) {
            throw new IllegalArgumentException("Discount in deprecated state cannot be used");
        }

        tenantDiscountLineItem.setName(tenantDiscount.getName());
        tenantDiscountLineItem.setType(tenantDiscount.getType());
        tenantDiscountLineItem.setDescription(tenantDiscount.getDescription());
        tenantDiscountLineItem.setPercent(tenantDiscount.getPercent());
        tenantDiscountLineItem.setStatus(tenantDiscount.getStatus());
    }

    public static void validateDiscountLine(OrderLineItem lineItem, Charge charge) {
        if (!charge.getIsDiscount()) {
            return;
        }
        if (CollectionUtils.isNotEmpty(lineItem.getDiscounts())) {
            throw new InvalidInputException(
                String.format("Discount line item cannot have discounts: %s - %s", charge.getChargeId(), charge.getName())
            );
        }
        if (CollectionUtils.isNotEmpty(lineItem.getPredefinedDiscounts())) {
            throw new InvalidInputException(
                String.format("Discount line item cannot have predefined discounts: %s - %s", charge.getChargeId(), charge.getName())
            );
        }
        if (charge.isCustom() && lineItem.getListUnitPrice() != null && lineItem.getListUnitPrice().compareTo(BigDecimal.ZERO) > 0) {
            throw new InvalidInputException(
                String.format("Custom discount line cannot have positive price: %s - %s", charge.getChargeId(), charge.getName())
            );
        }
    }

    public static BigDecimal calculateOrderLineNetDiscountPercent(List<OrderLineItem> orderLineItems, Map<String, Charge> chargeMap) {
        BigDecimal netDiscount = getNetDiscountAmount(orderLineItems, chargeMap);
        if (Numbers.isZero(netDiscount)) {
            return netDiscount.setScale(2, RoundingMode.HALF_UP);
        }

        BigDecimal netListAmount = orderLineItems
            .stream()
            .filter(orderLineItem -> !chargeMap.get(orderLineItem.getChargeId()).getIsDiscount())
            .map(orderLineItem -> Optional.ofNullable(orderLineItem.getListAmountBeforeOverride()).orElse(orderLineItem.getListAmount()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal netListAmountScaled = Numbers.makeCalculationScale(netListAmount.abs());

        return Numbers.scaledPercent(netDiscount, netListAmountScaled).setScale(2, RoundingMode.HALF_UP);
    }

    public static BigDecimal getNetDiscountAmountCurrencyScaled(List<OrderLineItem> orderLineItems, Map<String, Charge> chargeMap) {
        return Numbers.makeCurrencyScale(getNetDiscountAmount(orderLineItems, chargeMap));
    }

    private static BigDecimal getNetDiscountAmount(List<OrderLineItem> orderLineItems, Map<String, Charge> chargeMap) {
        if (CollectionUtils.isEmpty(orderLineItems)) {
            return BigDecimal.ZERO;
        }

        BigDecimal netListAmount = orderLineItems
            .stream()
            .filter(orderLineItem -> !chargeMap.get(orderLineItem.getChargeId()).getIsDiscount())
            .map(orderLineItem -> Optional.ofNullable(orderLineItem.getListAmountBeforeOverride()).orElse(orderLineItem.getListAmount()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal netAmount = orderLineItems.stream().map(OrderLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        LOGGER.debug("netListAmount = {},  netAmount = {}", netListAmount, netAmount);
        BigDecimal netListAmountScaled = Numbers.makeCalculationScale(netListAmount);
        BigDecimal zeroScaledToCalculation = Numbers.makeCalculationScale(BigDecimal.ZERO);

        if (Numbers.equals(netListAmountScaled, zeroScaledToCalculation)) {
            return netListAmountScaled;
        }

        // if sell amount is greater than list amount before override, make discount 0
        int signum = netListAmountScaled.signum() == -1 && netAmount.signum() == -1 ? -1 : 1;
        BigDecimal netDiscount = netListAmountScaled.subtract(netAmount).multiply(BigDecimal.valueOf(signum));
        return Numbers.isNegative(netDiscount) ? zeroScaledToCalculation : netDiscount;
    }

    public static BigDecimal calculateMaxOrderLineItemDiscountPercent(List<OrderLineItem> lineItemsNetEffect, Map<String, Charge> chargeMap) {
        BigDecimal maxOrderLineItemDiscountPercent = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(lineItemsNetEffect)) {
            return maxOrderLineItemDiscountPercent;
        }

        for (OrderLineItem orderLineItem : lineItemsNetEffect) {
            BigDecimal lineDiscountPercent = calculateOrderLineNetDiscountPercent(List.of(orderLineItem), chargeMap);
            maxOrderLineItemDiscountPercent = maxOrderLineItemDiscountPercent.max(lineDiscountPercent);
        }

        return maxOrderLineItemDiscountPercent.setScale(2, RoundingMode.HALF_UP);
    }
}
