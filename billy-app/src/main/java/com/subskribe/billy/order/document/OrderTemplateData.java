package com.subskribe.billy.order.document;

import static com.subskribe.billy.shared.enums.Cycle.MONTH;
import static com.subskribe.billy.shared.enums.Cycle.YEAR;
import static com.subskribe.billy.shared.render.DocumentRenderFormatter.INTERNATIONAL_DATE_FORMAT;
import static com.subskribe.billy.shared.render.DocumentRenderFormatter.US_DATE_FORMAT;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.esign.model.EsignDocumentTags;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.order.OrderDetailsMapper;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountDetailMapper;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.graphql.template.DocumentTemplateResponse;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoice.service.DiscountCalculator;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.document.deltaramp.TemplateDeltaRamps;
import com.subskribe.billy.order.document.temporal.OrderTemplateDate;
import com.subskribe.billy.order.document.temporal.TemplatePeriod;
import com.subskribe.billy.productcatalog.model.UnitOfMeasure;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.resources.json.account.AccountAddressJson;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.metrics.MetricsJson;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.shared.StreamUtils;
import com.subskribe.billy.shared.document.BaseTemplateData;
import com.subskribe.billy.shared.document.DocumentArrProjection;
import com.subskribe.billy.shared.document.TemplateAccount;
import com.subskribe.billy.shared.document.TemplateCalculations;
import com.subskribe.billy.shared.document.TemplateCharge;
import com.subskribe.billy.shared.document.TemplateDiscounts;
import com.subskribe.billy.shared.document.TemplateError;
import com.subskribe.billy.shared.document.TemplateMathFunctions;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.image.ImageUtils;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.template.model.DocumentSection;
import com.subskribe.billy.template.model.DocumentSectionLocation;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedMap;
import java.util.SortedSet;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.mapstruct.factory.Mappers;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class OrderTemplateData extends BaseTemplateData {

    private static final String CHARGE_ID_PERIOD_STRING = "%s_%s_%s";
    private static final String RECURRENCE_MONTHLY = "Monthly";
    private static final String RECURRENCE_YEARLY = "Yearly";
    private static final String EMPTY_OBJECT_JSON = "{}";

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    private final OrderDocumentJson orderDocumentJson;

    private final List<OrderTemplateLineItem> lineItems;

    private final List<OrderTemplateLineItem> executedLineItems;

    private final List<OrderTemplateLineItem> postSubscriptionLineItemsView;

    private final Map<OrderTemplateDate, List<OrderTemplateLineItem>> formattedDateToLineItems;

    private Map<String, String> predefinedTermsMap;

    private Map<DocumentSectionLocation, String> formattedDocumentSectionsByLocation;

    private final PredefinedDiscountDetailMapper predefinedDiscountDetailMapper;

    private String formattedDocumentTemplatesWithoutSection;

    private String formattedCustomContent;

    private String lineItemOrderByCustomFieldName;

    private OrderTemplateItemSortBy sortBy;

    private List<String> sortOrder;

    private List<OrderTemplateBundle> bundles;

    private OrderTemplateBundles itemBundles;

    private List<OrderTemplateBundles> bundlesByDate;

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private final Optional<UserJson> orderCreator;

    private final OrderDetailsMapper orderDetailsMapper;

    private boolean verifyDeltaRampTotals;

    private String planGroupByCustomFieldName;

    public OrderTemplateData(OrderDocumentJson orderDocumentJson) {
        this.orderDocumentJson = orderDocumentJson;
        orderDetailsMapper = Mappers.getMapper(OrderDetailsMapper.class);
        predefinedDiscountDetailMapper = Mappers.getMapper(PredefinedDiscountDetailMapper.class);

        initBaseDocumentJson(orderDocumentJson, getCurrency());

        Map<String, OrderTemplatePlan> orderTemplatePlanMap = getOrderTemplatePlansMap(orderDocumentJson.getOrderDetail().getLineItems());
        lineItems = initializeLineItems(orderDocumentJson, formatter, orderTemplatePlanMap);
        addPlansToLineItems(orderTemplatePlanMap);
        addLineItemsToPlans(orderTemplatePlanMap);
        executedLineItems = initializeExecutedLineItems(orderDocumentJson, formatter);
        formattedDateToLineItems = formatDateToLineItems(formatter, lineItems);
        postSubscriptionLineItemsView = getPostSubscriptionLineItemsView(orderDocumentJson.getOrderDetail(), formatter, orderTemplatePlanMap);

        // apply formatting to subscription charge details
        applyFormattingToSubscriptionCharges();

        // add formatter to invoice previews
        applyFormattingToInvoicePreviews();
        applyFormattingToSubscriptionPreviews();

        applyFormattingToArrProjections();

        orderCreator = Optional.ofNullable(orderDocumentJson.getOrderDetail().getCreatedBy());

        verifyDeltaRampTotals = true;
    }

    private List<OrderTemplateLineItem> getPostSubscriptionLineItemsView(
        OrderDetail orderDetail,
        DocumentRenderFormatter formatter,
        Map<String, OrderTemplatePlan> orderTemplatePlanMap
    ) {
        if (CollectionUtils.isEmpty(orderDetail.getLineItems())) {
            return List.of();
        }

        var currencyCode = getCurrency();
        var customFields = orderDocumentJson.getLineItemCustomFields();
        var lineItems = orderDocumentJson.getOrderDetail().getLineItems();
        var chargeCustomFields = orderDocumentJson.getChargeCustomFields();

        return initializeLineItems(currencyCode, chargeCustomFields, customFields, lineItems, formatter, orderTemplatePlanMap);
    }

    private void addLineItemsToPlans(Map<String, OrderTemplatePlan> orderTemplatePlanMap) {
        for (OrderTemplateLineItem lineItem : lineItems) {
            OrderTemplatePlan orderTemplatePlan = orderTemplatePlanMap.get(lineItem.getPlan().getId());
            if (orderTemplatePlan != null) {
                orderTemplatePlan.addLineItem(lineItem);
            }
        }
    }

    /**
     * CAUTION: this method is used to change the formatter state for the entire template document and should be invoked first in the template header
     *
     * @deprecated Use {@link #useDateFormat()} instead
     */
    @Deprecated
    public void useUSDateFormat() {
        formatter.setDateFormatPattern(US_DATE_FORMAT);
    }

    /**
     * CAUTION: this method is used to change the formatter state for the entire template document and should be invoked first in the template header
     *
     * @deprecated Use {@link #useDateFormat()} instead
     */
    @Deprecated
    public void useInternationalDateFormat() {
        formatter.setDateFormatPattern(INTERNATIONAL_DATE_FORMAT);
    }

    /**
     * CAUTION: this method is used to change the formatter state for the entire template document and should be invoked first in the template header
     * From template, this can be invoked with the following syntax: {{#useDateFormat}}dd/MM/yyyy{{/useDateFormat}}
     */
    public Function<String, String> useDateFormat() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Date format is required");
            }

            formatter.setDateFormatPattern(input);
            return null;
        };
    }

    public boolean hasRamps() {
        return lineItems.stream().anyMatch(OrderTemplateLineItem::isRampLineItem);
    }

    // CAUTION: this method is used to change the number of decimals to display for currency amounts for the entire template document and should be invoked first in the template header
    public void hideCurrencyDecimals() {
        formatter.setCurrencyDisplayMaxDecimals(0);
        formatter.setCurrencyDisplayMinDecimals(0);
    }

    // line level custom field to order items by
    public Function<String, String> sortByCustomField() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            lineItemOrderByCustomFieldName = input;
            return null;
        };
    }

    // set key to sort by: {{#setItemSortKey}}PRODUCT_ID{{/setItemSortKey}}
    public Function<String, String> setItemSortKey() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Sort by type is required");
            }

            try {
                sortBy = OrderTemplateItemSortBy.valueOf(input.trim());
                return null;
            } catch (IllegalArgumentException e) {
                return null;
            }
        };
    }

    // json array to determine sort order based on sort key. If sort key is PRODUCT_ID sort order is list of product ids:
    // {{#setItemSortOrder}}["PROD-1234", "PROD-5678]{{/setItemSortOrder}}
    public Function<String, String> setItemSortOrder() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Sort order is required");
            }

            try {
                sortOrder = Arrays.asList(OBJECT_MAPPER.readValue(input.trim(), String[].class));
                return null;
            } catch (JsonProcessingException e) {
                sortOrder = List.of();
                return null;
            }
        };
    }

    public Function<String, String> bundleByPlanCustomFieldWithSeparatedRamps() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            itemBundles = OrderTemplateBundles.buildItemBundlesByRampSegments(
                OrderTemplateBundles.OrderTemplateBundleBy.PLAN_CUSTOM_FIELD,
                input.trim(),
                getLineItems(),
                formatter,
                getCurrency()
            );
            return null;
        };
    }

    public Function<String, String> bundleByPlanCustomField() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            itemBundles = OrderTemplateBundles.buildItemBundles(
                OrderTemplateBundles.OrderTemplateBundleBy.PLAN_CUSTOM_FIELD,
                input.trim(),
                getLineItems(),
                formatter,
                getCurrency()
            );
            return null;
        };
    }

    public Function<String, String> bundleByChargeCustomField() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            itemBundles = OrderTemplateBundles.buildItemBundles(
                OrderTemplateBundles.OrderTemplateBundleBy.CHARGE_CUSTOM_FIELD,
                input.trim(),
                getLineItems(),
                formatter,
                getCurrency()
            );
            return null;
        };
    }

    public Function<String, String> bundleByPlanCustomFieldAndDate() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            Map<String, List<OrderTemplateLineItem>> orderTemplateLineItemsByRampDatesMap = getLineItems()
                .stream()
                .collect(Collectors.groupingBy(lineItem -> lineItem.getStartDate() + lineItem.getEndDate(), LinkedHashMap::new, Collectors.toList()));

            bundlesByDate = new ArrayList<>();
            for (var pair : orderTemplateLineItemsByRampDatesMap.entrySet()) {
                bundlesByDate.add(
                    OrderTemplateBundles.buildItemBundles(
                        OrderTemplateBundles.OrderTemplateBundleBy.PLAN_CUSTOM_FIELD,
                        input.trim(),
                        pair.getValue(),
                        formatter,
                        getCurrency()
                    )
                );
            }

            return null;
        };
    }

    public Function<String, String> bundleByPlanCustomFieldAndStartDate() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            Map<String, List<OrderTemplateLineItem>> orderTemplateLineItemsByRampDatesMap = getLineItems()
                .stream()
                .collect(Collectors.groupingBy(OrderTemplateLineItem::getStartDate, LinkedHashMap::new, Collectors.toList()));

            bundlesByDate = new ArrayList<>();
            for (var pair : orderTemplateLineItemsByRampDatesMap.entrySet()) {
                bundlesByDate.add(
                    OrderTemplateBundles.buildItemBundles(
                        OrderTemplateBundles.OrderTemplateBundleBy.PLAN_CUSTOM_FIELD,
                        input.trim(),
                        pair.getValue(),
                        formatter,
                        getCurrency()
                    )
                );
            }

            return null;
        };
    }

    public Function<String, String> bundleByOrderLineCustomFieldAndDate() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            Map<String, List<OrderTemplateLineItem>> orderTemplateLineItemsByRampDatesMap = getLineItems()
                .stream()
                .collect(Collectors.groupingBy(lineItem -> lineItem.getStartDate() + lineItem.getEndDate(), LinkedHashMap::new, Collectors.toList()));

            bundlesByDate = new ArrayList<>();
            for (var pair : orderTemplateLineItemsByRampDatesMap.entrySet()) {
                bundlesByDate.add(
                    OrderTemplateBundles.buildItemBundles(
                        OrderTemplateBundles.OrderTemplateBundleBy.ORDER_ITEM_CUSTOM_FIELD,
                        input.trim(),
                        pair.getValue(),
                        formatter,
                        getCurrency()
                    )
                );
            }

            return null;
        };
    }

    public Function<String, String> bundleByOrderLineCustomFieldAndStartDate() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            Map<String, List<OrderTemplateLineItem>> orderTemplateLineItemsByRampDatesMap = getLineItems()
                .stream()
                .collect(Collectors.groupingBy(OrderTemplateLineItem::getStartDate, LinkedHashMap::new, Collectors.toList()));

            bundlesByDate = new ArrayList<>();
            for (var pair : orderTemplateLineItemsByRampDatesMap.entrySet()) {
                bundlesByDate.add(
                    OrderTemplateBundles.buildItemBundles(
                        OrderTemplateBundles.OrderTemplateBundleBy.ORDER_ITEM_CUSTOM_FIELD,
                        input.trim(),
                        pair.getValue(),
                        formatter,
                        getCurrency()
                    )
                );
            }

            return null;
        };
    }

    public Function<String, Void> orderLineQuantityMultiplier() {
        return input -> {
            List<OrderTemplateLineItem> lineItems = getLineItems();
            for (var lineItem : lineItems) {
                updateLineItemQuantityBasedOnChargeCustomField(input, lineItem);
            }

            return null;
        };
    }

    public static void updateLineItemQuantityBasedOnChargeCustomField(String lineItemCustomFieldName, OrderTemplateLineItem lineItem) {
        if (
            lineItem.getCharge().getCustomFields() != null &&
            !lineItem.getCharge().getCustomFields().isEmpty() &&
            lineItem.getCharge().getCustomFields().get(lineItemCustomFieldName) != null
        ) {
            BigDecimal quantityMultiplier = new BigDecimal(lineItem.getCharge().getCustomFields().get(lineItemCustomFieldName).getValue());
            lineItem.setAdjustedQuantity(new BigDecimal(lineItem.getQuantity()).multiply(quantityMultiplier));
        }
    }

    public Function<String, Void> orderLineChargeRollupWithinBundlesByDate() {
        return input -> {
            for (OrderTemplateBundles orderTemplateBundles : bundlesByDate) {
                for (OrderTemplateBundle bundle : orderTemplateBundles.getBundles()) {
                    List<OrderTemplateLineItem> bundledLineItems = bundle.getItems();
                    rollUpChargeWithinBundleBasedOnCustomField(bundledLineItems, input);
                }

                rollUpChargeWithinBundleBasedOnCustomField(orderTemplateBundles.getUnbundledItems(), input);
            }
            return null;
        };
    }

    public static void rollUpChargeWithinBundleBasedOnCustomField(List<OrderTemplateLineItem> lineItems, String chargeCustomFieldName) {
        // find charges which need to be rolled up
        List<OrderTemplateLineItem> lineItemsToBeRolledUp = new ArrayList<>();
        for (OrderTemplateLineItem lineItem : lineItems) {
            if (lineItem.getCharge().getCustomFields().get(chargeCustomFieldName) != null) {
                lineItemsToBeRolledUp.add(lineItem);
            }
        }

        if (CollectionUtils.isEmpty(lineItemsToBeRolledUp)) {
            return;
        }

        for (OrderTemplateLineItem lineItemToRollUp : lineItemsToBeRolledUp) {
            // find the charge to be rolled up to
            // caveat: we will roll up to the first charge we find with that charge id
            // if we don't find the charge to be rolled up to, we will skip the step
            String chargeId = lineItemToRollUp.getCharge().getCustomFields().get(chargeCustomFieldName).getValue();
            rollupChargeToAnotherLineMentionedInChargeCustomField(lineItems, lineItemToRollUp, chargeId);
        }
    }

    private static void rollupChargeToAnotherLineMentionedInChargeCustomField(
        List<OrderTemplateLineItem> lineItems,
        OrderTemplateLineItem lineItemToRollUp,
        String chargeId
    ) {
        if (chargeId == null) {
            return;
        }

        List<OrderTemplateLineItem> lineWithRollUpChargeId = lineItems
            .stream()
            .filter(l -> l.getCharge().getId().equals(chargeId))
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(lineWithRollUpChargeId)) {
            OrderTemplateLineItem lineWithRollUpCharge = lineWithRollUpChargeId.get(0);
            lineWithRollUpCharge.setAdjustedQuantity(
                lineWithRollUpCharge.getUnformattedQuantityAsBigDecimal().add(lineItemToRollUp.getUnformattedQuantityAsBigDecimal())
            );

            lineWithRollUpCharge.setAdjustedAmount(lineWithRollUpCharge.getAmountUnformatted().add(lineItemToRollUp.getAmountUnformatted()));
            lineWithRollUpCharge.setAdjustedListAmount(
                lineWithRollUpCharge.getListAmountUnformatted().add(lineItemToRollUp.getListAmountUnformatted())
            );
            lineWithRollUpCharge.setAdjustedYearlyAmount(
                lineWithRollUpCharge
                    .getYearlyAmountValue()
                    .orElse(BigDecimal.ZERO)
                    .add(lineItemToRollUp.getYearlyAmountValue().orElse(BigDecimal.ZERO))
            );

            // LineItem to rollup need to adjust its amounts so that bundle totals are not double counting them
            lineItemToRollUp.setAdjustedQuantity(BigDecimal.ZERO);
            lineItemToRollUp.setAdjustedAmount(BigDecimal.ZERO);
            lineItemToRollUp.setAdjustedListAmount(BigDecimal.ZERO);
            lineItemToRollUp.setAdjustedYearlyAmount(BigDecimal.ZERO);
        }
    }

    public Function<String, String> bundleByCustomField() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            initializeBundle(input);
            return null;
        };
    }

    // Lambda invoked per template to set locale to use when rendering date and currency values.
    // Example: {{#setLocale}}de-DE{{/setLocale}}
    public Function<String, Void> setLocale() {
        return input -> {
            formatter.updateLocale(input);
            return null;
        };
    }

    // Lambda to format a numeric string as currency with rounding control
    // e.g. {{#formatCurrency}}10.00, 0{{/formatCurrency}} -> returns $10 if USD
    public Function<String, String> formatCurrencyRounded() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return StringUtils.EMPTY;
            }

            try {
                Pair<BigDecimal, BigDecimal> operants = TemplateMathFunctions.parseTwoOperants(input);
                BigDecimal amount = operants.getLeft();
                int precision = operants.getRight().intValue();
                amount = amount.setScale(precision, RoundingMode.HALF_UP).stripTrailingZeros();
                return formatter.currencyFormat(amount, getCurrency(), precision, precision);
            } catch (InvalidInputException ex) {
                return ex.getMessage();
            }
        };
    }

    // Lambda to format a numeric string as currency
    // e.g. {{#formatCurrency}}10.00{{/formatCurrency}} -> returns $10.00 if USD
    public Function<String, String> formatCurrency() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return StringUtils.EMPTY;
            }

            if (!TemplateMathFunctions.isNumber(input.trim())) {
                // if input is not a number, do nothing and return the value
                return input;
            }

            try {
                BigDecimal amount = TemplateMathFunctions.parseNumber(input);
                return formatter.currencyFormat(amount, getCurrency());
            } catch (NumberFormatException ex) {
                return "Invalid Number Value: " + input;
            }
        };
    }

    // Lambda to format a numeric string using number format
    // e.g. {{#formatNumber}}1000000{{/formatNumber}} -> returns 1,000,000
    public Function<String, String> formatNumber() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return StringUtils.EMPTY;
            }

            if (!TemplateMathFunctions.isNumber(input.trim())) {
                // if input is not a number, do nothing and return the value
                return input;
            }

            try {
                int value = TemplateMathFunctions.parseInt(input);
                return formatter.numberFormat((long) value);
            } catch (NumberFormatException ex) {
                return "Invalid Number Value: " + input;
            }
        };
    }

    // Lambda to format a numeric string as percent respecting any locale set
    // e.g. {{#formatPercent}}10{{/formatPercent}} -> 10%
    public Function<String, String> formatPercent() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return StringUtils.EMPTY;
            }

            if (!TemplateMathFunctions.isNumber(input.trim())) {
                // if input is not a number, do nothing and return the value
                return input;
            }

            try {
                BigDecimal amount = TemplateMathFunctions.parseNumber(input);
                return formatter.formatPercent(amount) + "%";
            } catch (NumberFormatException ex) {
                return "Invalid Number Value: " + input;
            }
        };
    }

    // todo: migrate existing templates to use itemBundles instead
    private void initializeBundle(String bundleByCustomFieldName) {
        Map<Optional<String>, List<OrderTemplateLineItem>> bundlesMap = getLineItems()
            .stream()
            .collect(
                Collectors.groupingBy(
                    item ->
                        item.getCustomFields().get(bundleByCustomFieldName) == null
                            ? Optional.empty()
                            : Optional.ofNullable(item.getCustomFields().get(bundleByCustomFieldName).getValue()),
                    LinkedHashMap::new,
                    Collectors.toList()
                )
            );

        bundles = new ArrayList<>();

        bundlesMap.forEach((key, value) -> {
            if (key.isPresent()) {
                OrderTemplateBundle bundle = new OrderTemplateBundle(key.get(), value, formatter, getCurrency());
                bundles.add(bundle);
            }
        });

        List<OrderTemplateLineItem> unbundledItems = bundlesMap.get(Optional.empty());

        if (unbundledItems != null) {
            bundles.add(new OrderTemplateBundle("", unbundledItems, formatter, getCurrency()));
        }
    }

    public OrderTemplateBundles getItemBundles() {
        return itemBundles;
    }

    public List<OrderTemplateBundle> getBundles() {
        return bundles;
    }

    public List<OrderTemplateBundles> getBundlesByDate() {
        return bundlesByDate;
    }

    // TODO: Do not use OrderLineItemDetail (GQL layer leak), instead use OrderLineItem (model layer)
    private List<OrderTemplateLineItem> initializeLineItems(
        OrderDocumentJson orderDocumentJson,
        DocumentRenderFormatter formatter,
        Map<String, OrderTemplatePlan> orderTemplatePlanMap
    ) {
        if (CollectionUtils.isEmpty(orderDocumentJson.getOrderDetail().getLineItemsNetEffect())) {
            return List.of();
        }

        var currencyCode = getCurrency();
        var customFields = orderDocumentJson.getLineItemCustomFields();
        var lineItems = orderDocumentJson.getOrderDetail().getLineItemsNetEffect();
        var chargeCustomFields = orderDocumentJson.getChargeCustomFields();

        return initializeLineItems(currencyCode, chargeCustomFields, customFields, lineItems, formatter, orderTemplatePlanMap);
    }

    private List<OrderTemplateLineItem> initializeLineItems(
        String currencyCode,
        Map<String, CustomField> chargeCustomFields,
        Map<String, CustomField> customFields,
        List<OrderLineItemDetail> lineItems,
        DocumentRenderFormatter formatter,
        Map<String, OrderTemplatePlan> orderTemplatePlanMap
    ) {
        if (CollectionUtils.isEmpty(lineItems)) {
            return List.of();
        }

        Map<String, List<OrderLineItemDetail>> groupedOrderLines = groupLineItems(lineItems);

        List<OrderTemplateLineItem> orderTemplateLineItems = new ArrayList<>();

        groupedOrderLines.forEach((key, orderLineItemDetails) -> {
            if (!orderLineItemDetails.isEmpty()) {
                BigDecimal totalEntryArr = orderLineItemDetails
                    .stream()
                    .map(line -> getOrderLineMetrics(line.getId()).getEntryArr())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                // todo: this function should be revisited. Currently it modifies OrderLineItemDetail objects which can cause unexpected side effects.
                //  Numerous calculations are also repeated. Should consider using net effect lines instead and updating the quantity or creating a duplicate object instead of updating in place.
                OrderLineItemDetail reducedOrderLineDetail = orderLineItemDetails
                    .stream()
                    .sorted(Comparator.comparingLong(OrderLineItemDetail::getQuantity))
                    .reduce(this::addAmountAndQuantityForLineItem)
                    .orElseThrow();

                ChargeJson charge = reducedOrderLineDetail.getCharge();
                CustomField chargeCustomField = chargeCustomFields.get(charge.getId());

                Cycle chargeBillingCycle;
                if (charge.getBillingCycle() == null || charge.getBillingCycle() == BillingCycle.DEFAULT) {
                    chargeBillingCycle = orderDocumentJson.getOrderDetail().getBillingCycle().getCycle();
                } else {
                    chargeBillingCycle = charge.getBillingCycle().getCycle().orElse(null);
                }

                UnitOfMeasure unitOfMeasure = charge.getUnitOfMeasureId() == null
                    ? null
                    : orderDocumentJson.getUnitOfMeasureMap().get(charge.getUnitOfMeasureId());
                var orderTemplateCharge = new TemplateCharge(chargeCustomField, formatter, currencyCode, chargeBillingCycle, charge, unitOfMeasure);

                List<PriceAttributeData> priceAttributeData = getPriceAttributeData(orderDocumentJson.getPriceAttributeMap(), reducedOrderLineDetail);
                ProductJson product = orderDocumentJson.getProducts().get(reducedOrderLineDetail.getPlan().getProductId());

                List<OrderTemplateLineItem> rawLineItems = new ArrayList<>();
                for (OrderLineItemDetail lineItem : orderLineItemDetails) {
                    List<PriceAttributeData> lineAttributeData = getPriceAttributeData(orderDocumentJson.getPriceAttributeMap(), lineItem);
                    OrderTemplateLineItem rawLineItem = new OrderTemplateLineItem(
                        lineItem,
                        product,
                        orderTemplatePlanMap.get(lineItem.getPlan().getId()),
                        orderTemplateCharge,
                        getOrderLineMetrics(lineItem.getId()).getEntryArr(),
                        customFields.get(lineItem.getId()),
                        formatter,
                        currencyCode,
                        lineAttributeData,
                        timeZone
                    );
                    rawLineItems.add(rawLineItem);
                }

                OrderTemplateLineItem templateLineItem = new OrderTemplateLineItem(
                    reducedOrderLineDetail,
                    product,
                    null,
                    orderTemplateCharge,
                    totalEntryArr,
                    customFields.get(reducedOrderLineDetail.getId()),
                    formatter,
                    currencyCode,
                    priceAttributeData,
                    timeZone
                );

                templateLineItem.setRawLineItems(rawLineItems);

                orderTemplateLineItems.add(templateLineItem);
            }
        });

        return orderTemplateLineItems
            .stream()
            .sorted(Comparator.comparing(item -> item.getOrderLineItemDetail().getRank()))
            .collect(Collectors.toList());
    }

    private Map<String, OrderTemplatePlan> getOrderTemplatePlansMap(List<OrderLineItemDetail> lineItems) {
        Map<String, OrderTemplatePlan> orderTemplatePlans = new LinkedHashMap<>();
        String currencyCode = getCurrency();

        for (var lineItem : lineItems) {
            PlanJson planJson = lineItem.getPlan();
            ProductJson product = orderDocumentJson.getProducts().get(planJson.getProductId());
            String planId = planJson.getId();

            OrderTemplatePlan createdOrderTemplatePlan = new OrderTemplatePlan(
                planJson.getId(),
                planJson.getExternalId(),
                StringUtils.isBlank(product.getDisplayName()) ? product.getName() : product.getDisplayName(),
                StringUtils.isBlank(planJson.getDisplayName()) ? planJson.getName() : planJson.getDisplayName(),
                planJson.getDescription(),
                planJson.getCustomFields(),
                currencyCode,
                formatter,
                StringUtils.EMPTY
            );

            // add plan to the keyed map and retrieve it back (there can be multiple line items for the same plan)
            orderTemplatePlans.putIfAbsent(planId, createdOrderTemplatePlan);
        }

        return orderTemplatePlans;
    }

    private void addPlansToLineItems(Map<String, OrderTemplatePlan> orderTemplatePlanMap) {
        for (var lineItem : lineItems) {
            var planJson = lineItem.getOrderLineItemDetail().getPlan();
            OrderTemplatePlan plan = orderTemplatePlanMap.get(planJson.getId());
            lineItem.setOrderTemplatePlan(plan);
        }
    }

    static List<PriceAttributeData> getPriceAttributeData(Map<String, PriceAttribute> priceAttributeMap, OrderLineItemDetail reducedOrderLineDetail) {
        Collection<AttributeReference> attributeReferences = StreamUtils.nullAsEmpty(reducedOrderLineDetail.getAttributeReferences());
        return attributeReferences
            .stream()
            .map(attributeReference -> {
                Optional<PriceAttribute> priceAttributeDef = Optional.ofNullable(
                    priceAttributeMap.getOrDefault(attributeReference.getAttributeDefinitionId(), null)
                );
                return new PriceAttributeData(
                    priceAttributeDef.map(PriceAttribute::getName).orElse(StringUtils.EMPTY),
                    attributeReference.getAttributeValue()
                );
            })
            .toList();
    }

    private Metrics getOrderLineMetrics(String orderLineItemId) {
        if (orderDocumentJson.getOrderLineMetrics() == null) {
            return new Metrics(
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                List.of()
            );
        }
        return orderDocumentJson.getOrderLineMetrics().get(orderLineItemId);
    }

    private List<OrderTemplateLineItem> initializeExecutedLineItems(OrderDocumentJson orderDocumentJson, DocumentRenderFormatter formatter) {
        var lineItems = orderDocumentJson.getExecutedLineItemsForSubscription();
        if (CollectionUtils.isEmpty(lineItems)) {
            return List.of();
        }

        var currencyCode = getCurrency();
        var customFields = orderDocumentJson.getExecutedLineItemCustomFields();
        var chargeCustomFields = orderDocumentJson.getExecutedLineItemChargeCustomFields();

        return initializeLineItems(currencyCode, chargeCustomFields, customFields, lineItems, formatter, getOrderTemplatePlansMap(lineItems));
    }

    private void applyFormattingToSubscriptionCharges() {
        if (orderDocumentJson.getOrderDetail().getCurrentSubscription() == null) {
            return;
        }
        var subscriptionChargeDetails = orderDocumentJson.getOrderDetail().getCurrentSubscription().getCharges();
        String currency = getCurrency();
        subscriptionChargeDetails.forEach(chargeDetail -> {
            chargeDetail.setFormattedStartDate(formatter.dateFormat(chargeDetail.getStartDate()));
            chargeDetail.setFormattedEndDate(formatter.endDateFormat(chargeDetail.getEndDate()));
            String discountPercent = formatter.formatPercent(DiscountCalculator.getSubscriptionChargeDiscount(chargeDetail));
            chargeDetail.setFormattedDiscountPercent(discountPercent);
            chargeDetail.setFormattedListUnitPrice(formatter.currencyFormat(chargeDetail.getListUnitPrice(), currency));
            chargeDetail.setFormattedSellUnitPrice(formatter.currencyFormat(chargeDetail.getSellUnitPrice(), currency));
        });
    }

    // todo: set currency during invoice preview generation
    private void applyFormattingToInvoicePreviews() {
        if (CollectionUtils.isEmpty(orderDocumentJson.getDocumentInvoicePreviews())) {
            return;
        }
        orderDocumentJson
            .getDocumentInvoicePreviews()
            .forEach(documentInvoicePreview -> {
                documentInvoicePreview.setFormatter(formatter);
                documentInvoicePreview.setCurrencyCode(getCurrency());
            });
    }

    private void applyFormattingToSubscriptionPreviews() {
        if (CollectionUtils.isEmpty(orderDocumentJson.getSubscriptionInvoicePreviews())) {
            return;
        }
        orderDocumentJson
            .getSubscriptionInvoicePreviews()
            .forEach(subscriptionInvoicePreview -> {
                subscriptionInvoicePreview.setFormatter(formatter);
                subscriptionInvoicePreview.setCurrencyCode(getCurrency());
            });
    }

    private void applyFormattingToArrProjections() {
        if (CollectionUtils.isEmpty(orderDocumentJson.getArrProjections())) {
            return;
        }

        orderDocumentJson
            .getArrProjections()
            .forEach(arrProjection -> {
                arrProjection.setFormatter(formatter);
                arrProjection.setCurrencyCode(getCurrency());
            });
    }

    private Map<String, List<OrderLineItemDetail>> groupLineItems(List<OrderLineItemDetail> lineItems) {
        return lineItems.stream().collect(Collectors.groupingBy(OrderTemplateData::chargeIdAndPeriodString, Collectors.toList()));
    }

    private static String chargeIdAndPeriodString(OrderLineItemDetail lineItem) {
        String chargeGroupId = Optional.ofNullable(lineItem.getSubscriptionChargeGroupId()).orElse(lineItem.getId());
        return String.format(CHARGE_ID_PERIOD_STRING, chargeGroupId, lineItem.getEffectiveDate(), lineItem.getEndDate());
    }

    private OrderLineItemDetail addAmountAndQuantityForLineItem(OrderLineItemDetail firstLineItem, OrderLineItemDetail secondLineItem) {
        OrderLineItemDetail firstLineItemClone = orderDetailsMapper.duplicateOrderLineItemDetail(firstLineItem);

        firstLineItemClone.setAmount(
            Objects.requireNonNullElse(firstLineItem.getAmount(), BigDecimal.ZERO).add(
                Objects.requireNonNullElse(secondLineItem.getAmount(), BigDecimal.ZERO)
            )
        );
        firstLineItemClone.setListAmount(
            Objects.requireNonNullElse(firstLineItem.getListAmount(), BigDecimal.ZERO).add(
                Objects.requireNonNullElse(secondLineItem.getListAmount(), BigDecimal.ZERO)
            )
        );
        firstLineItemClone.setDiscountAmount(
            Objects.requireNonNullElse(firstLineItem.getDiscountAmount(), BigDecimal.ZERO).add(
                Objects.requireNonNullElse(secondLineItem.getDiscountAmount(), BigDecimal.ZERO)
            )
        );

        if (firstLineItem.getQuantity() <= 0 && firstLineItem.getAction() == ActionType.UPDATE && secondLineItem.getAction() == ActionType.UPDATE) {
            // todo: this is a temporary measure to use the rebook ID if line is an update with debook + rebook
            firstLineItemClone.setId(secondLineItem.getId());
            firstLineItemClone.setListUnitPrice(secondLineItem.getListUnitPrice());
            firstLineItemClone.setSellUnitPrice(secondLineItem.getSellUnitPrice());
            firstLineItemClone.setAnnualizedAmount(
                Objects.requireNonNullElse(firstLineItem.getAnnualizedAmount(), BigDecimal.ZERO).add(
                    Objects.requireNonNullElse(secondLineItem.getAnnualizedAmount(), BigDecimal.ZERO)
                )
            );
        }
        firstLineItemClone.setQuantity(firstLineItem.getQuantity() + secondLineItem.getQuantity());
        firstLineItemClone.setTaxEstimate(
            Objects.requireNonNullElse(firstLineItem.getTaxEstimate(), BigDecimal.ZERO).add(
                Objects.requireNonNullElse(secondLineItem.getTaxEstimate(), BigDecimal.ZERO)
            )
        );
        return firstLineItemClone;
    }

    /**
     * Instead of building a map of OrderTemplateDate to List<OrderTemplateLineItem>, embed order line items list into OrderTemplateDate object instead
     * Prior to this being removed, existing templates need to be updated
     *
     * @deprecated Use {@link OrderTemplateDate#from} instead to build a list of OrderTemplateDate objects with embedded line items
     */
    @Deprecated
    private Map<OrderTemplateDate, List<OrderTemplateLineItem>> formatDateToLineItems(
        DocumentRenderFormatter formatter,
        List<OrderTemplateLineItem> lineItems
    ) {
        // LinkedHashMap guarantees insertion order
        Map<OrderTemplateDate, List<OrderTemplateLineItem>> formattedDateToLineItems = new LinkedHashMap<>();

        Map<Period, List<OrderTemplateLineItem>> sortedPeriodToLineItems = OrderTemplateDate.groupLineItemsByPeriod(lineItems);
        int periodIndex = 1; // 1 indexed for display
        for (Map.Entry<Period, List<OrderTemplateLineItem>> entry : sortedPeriodToLineItems.entrySet()) {
            insertFormattedDateToLineItems(entry.getKey(), entry.getValue(), formattedDateToLineItems, formatter, periodIndex);
            periodIndex++;
        }

        return formattedDateToLineItems;
    }

    private void insertFormattedDateToLineItems(
        Period period,
        List<OrderTemplateLineItem> lineItems,
        Map<OrderTemplateDate, List<OrderTemplateLineItem>> formattedDateToLineItems,
        DocumentRenderFormatter formatter,
        int periodIndex
    ) {
        if (period.getStart().compareTo(period.getEnd()) == 0) {
            // skip 0 duration intervals
            return;
        }

        BigDecimal intervalTotal = lineItems.stream().map(item -> item.getOrderLineItemDetail().getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        String currencyCode = getCurrency();
        OrderTemplateDate formattedDate = new OrderTemplateDate(
            period.getStart(),
            period.getEnd(),
            intervalTotal,
            formatter,
            currencyCode,
            lineItems,
            periodIndex
        );

        formattedDateToLineItems.put(formattedDate, lineItems);
    }

    public Map<OrderTemplateDate, List<OrderTemplateLineItem>> getFormattedDateToLineItems() {
        formattedDateToLineItems
            .entrySet()
            .forEach(entry -> {
                List<OrderTemplateLineItem> sortedItems = getSortedLineItems(entry.getValue());
                entry.setValue(sortedItems);
            });
        return formattedDateToLineItems;
    }

    public List<OrderTemplateDate> getRampedItemDateGroups() {
        List<OrderTemplateLineItem> rampedLineItems = getLineItems().stream().filter(OrderTemplateLineItem::isRampLineItem).toList();
        return getSortedLineItemDateGroups(rampedLineItems);
    }

    public List<OrderTemplateDate> getNonRampedItemDateGroups() {
        List<OrderTemplateLineItem> nonRampedLineItems = getLineItems().stream().filter(item -> !item.isRampLineItem()).toList();
        return getSortedLineItemDateGroups(nonRampedLineItems);
    }

    public List<OrderTemplateDate> getNonRecurringItemDateGroups() {
        List<OrderTemplateLineItem> nonRecurringItems = getLineItems().stream().filter(item -> !item.getIsRecurring()).toList();
        return getSortedLineItemDateGroups(nonRecurringItems);
    }

    private List<OrderTemplateDate> getSortedLineItemDateGroups(List<OrderTemplateLineItem> lineItems) {
        List<OrderTemplateDate> orderTemplateDates = OrderTemplateDate.from(lineItems, formatter, getCurrency());

        for (OrderTemplateDate orderTemplateDate : orderTemplateDates) {
            List<OrderTemplateLineItem> sortedItems = getSortedLineItems(orderTemplateDate.getLineItems());
            orderTemplateDate.setLineItems(sortedItems);
        }

        return orderTemplateDates;
    }

    private List<OrderTemplateLineItem> getSortedLineItems(List<OrderTemplateLineItem> lineItems) {
        return StringUtils.isBlank(lineItemOrderByCustomFieldName)
            ? OrderLineItemSorter.sortBy(sortBy, sortOrder, lineItems)
            : OrderLineItemSorter.sortByChargeCustomField(lineItemOrderByCustomFieldName, lineItems);
    }

    public boolean hasMultipleDateRanges() {
        return formattedDateToLineItems.size() > 1;
    }

    public String getBase64EncodedLogo() {
        Optional<byte[]> logoContent = orderDocumentJson.getLogoContent();
        if (logoContent.isEmpty()) {
            return null;
        }

        var encodedContent = Base64.getEncoder().encodeToString(logoContent.get());
        var imageType = ImageUtils.getImageType(logoContent.get());
        return String.format("data:image/%s;base64,%s", imageType, encodedContent);
    }

    public boolean getIsDraft() {
        return (
            orderDocumentJson.getOrderDetail().getStatus() == OrderStatus.DRAFT ||
            orderDocumentJson.getOrderDetail().getStatus() == OrderStatus.SUBMITTED
        );
    }

    public String getOrderApprovedOnDate() {
        OrderDetail orderDetail = orderDocumentJson.getOrderDetail();
        if (orderDetail.getStatus() == OrderStatus.APPROVED) {
            return formatter.dateFormat(orderDetail.getUpdatedOn());
        } else {
            return StringUtils.EMPTY;
        }
    }

    public String getOrderUpdatedPlus30() {
        if (Objects.nonNull(orderDocumentJson.getOrderDetail().getUpdatedOn())) {
            Instant updatedOn = Instant.ofEpochSecond(orderDocumentJson.getOrderDetail().getUpdatedOn());
            var result = ZonedDateTime.ofInstant(updatedOn, timeZone.toZoneId()).plusDays(30).toInstant();
            return formatter.dateFormat(result);
        }
        return StringUtils.EMPTY;
    }

    public String getExpiryDate() {
        if (Objects.nonNull(orderDocumentJson.getOrderDetail().getExpiresOn())) {
            return formatter.dateFormat(orderDocumentJson.getOrderDetail().getExpiresOn());
        }
        return StringUtils.EMPTY;
    }

    /**
     * Tag used to for conditional on order type: {{#orderType.NEW}}...{{/orderType.NEW}}
     */
    public Map<String, Boolean> getOrderType() {
        if (orderDocumentJson.getOrderDetail().getOrderType() == null) {
            return Map.of();
        }

        return Map.of(orderDocumentJson.getOrderDetail().getOrderType().name(), true);
    }

    /**
     * @deprecated Use {@link #getOrderType()}
     */
    @Deprecated
    public boolean getIsAmendment() {
        return orderDocumentJson.getOrderDetail().getOrderType() == OrderType.AMENDMENT;
    }

    /**
     * @deprecated Use {@link #getOrderType()}
     */
    @Deprecated
    public boolean getIsNew() {
        return orderDocumentJson.getOrderDetail().getOrderType() == OrderType.NEW;
    }

    /**
     * @deprecated Use {@link #getOrderType()}
     */
    @Deprecated
    public boolean getIsCancel() {
        return orderDocumentJson.getOrderDetail().getOrderType() == OrderType.CANCEL;
    }

    /**
     * @deprecated Use {@link #getOrderType()}
     */
    @Deprecated
    public boolean getIsRenewal() {
        return orderDocumentJson.getOrderDetail().getOrderType() == OrderType.RENEWAL;
    }

    public String getStatus() {
        return orderDocumentJson.getOrderDetail().getStatus().name();
    }

    public String getTenantName() {
        return orderDocumentJson.getTenantInfo().getName();
    }

    public String getTenantEmail() {
        return orderDocumentJson.getTenantInfo().getEmail();
    }

    public String getTenantPhoneNumber() {
        return formatter.phoneNumberFormat(orderDocumentJson.getTenantInfo().getPhoneNumber());
    }

    public AccountAddressJson getTenantAddress() {
        return orderDocumentJson.getTenantInfo().getAddress();
    }

    public String getOrderId() {
        return orderDocumentJson.getOrderDetail().getId();
    }

    public String getOrderOwner() {
        return orderDocumentJson.getOrderDetail().getOwner() == null ? null : orderDocumentJson.getOrderDetail().getOwner().getDisplayName();
    }

    public String getOrderOwnerEmail() {
        return orderDocumentJson.getOrderDetail().getOwner() == null ? null : orderDocumentJson.getOrderDetail().getOwner().getEmail();
    }

    public String getOrderOwnerPhone() {
        return orderDocumentJson.getOrderDetail().getOwner() == null
            ? null
            : formatter.phoneNumberFormat(orderDocumentJson.getOrderDetail().getOwner().getPhoneNumber());
    }

    public UserJson getOwner() {
        return orderDocumentJson.getOrderDetail().getOwner();
    }

    public String getOrderCreator() {
        return orderCreator.map(UserJson::getDisplayName).orElse("");
    }

    public OrderTemplateData getOrder() {
        return this;
    }

    public String getOrderCreatorPhone() {
        return orderCreator.map(UserJson::getPhoneNumber).orElse("");
    }

    public String getOrderCreatorEmail() {
        return orderCreator.map(UserJson::getEmail).orElse("");
    }

    public boolean isAutoRenew() {
        return orderDocumentJson.getOrderDetail().isAutoRenew();
    }

    public String getOrderName() {
        return orderDocumentJson.getOrderDetail().getName();
    }

    public String getDateCreated() {
        return formatter.dateFormat(Instant.now());
    }

    public String getOrderStartDate() {
        return formatter.dateFormat(orderDocumentJson.getOrderDetail().getStartDate());
    }

    public String getOrderEndDate() {
        return formatter.endDateFormat(orderDocumentJson.getOrderDetail().getEndDate());
    }

    public String getBillingCycle() {
        RecurrenceJson billingCycle = orderDocumentJson.getOrderDetail().getBillingCycle();
        if (billingCycle == null) {
            return StringUtils.EMPTY;
        }

        return formatter.formatBillingCycle(billingCycle);
    }

    public BigDecimal getOrderTermLengthInYears() {
        BigDecimal years = Period.toDurationInYears(
            Instant.ofEpochSecond(orderDocumentJson.getOrderDetail().getStartDate()),
            Instant.ofEpochSecond(orderDocumentJson.getOrderDetail().getEndDate()),
            timeZone
        );

        return years.stripTrailingZeros();
    }

    public String getTermLength() {
        return formatter.formatTermLength(
            orderDocumentJson.getOrderDetail().getTermLength(),
            orderDocumentJson.getOrderDetail().getStartDate(),
            orderDocumentJson.getOrderDetail().getEndDate(),
            timeZone
        );
    }

    public TemplatePeriod getOrderPeriod() {
        return formatter.dateRangeToPeriod(orderDocumentJson.getOrderDetail().getStartDate(), orderDocumentJson.getOrderDetail().getEndDate());
    }

    public String getOrderLengthInYears() {
        BigDecimal periodInYears = Period.toDurationInYears(
            Instant.ofEpochSecond(orderDocumentJson.getOrderDetail().getStartDate()),
            Instant.ofEpochSecond(orderDocumentJson.getOrderDetail().getEndDate()),
            timeZone
        );
        return String.format("%s %s", periodInYears.toPlainString(), periodInYears.doubleValue() > 1 ? YEAR.getPluralName() : YEAR.getSingularName());
    }

    public String getOrderLengthInMonths() {
        long periodInMonths = Period.toDurationInMonthsFloor(
            Instant.ofEpochSecond(orderDocumentJson.getOrderDetail().getStartDate()),
            Instant.ofEpochSecond(orderDocumentJson.getOrderDetail().getEndDate()),
            timeZone
        );
        return String.format("%d %s", periodInMonths, periodInMonths > 1 ? MONTH.getPluralName() : MONTH.getSingularName());
    }

    public String getPaymentTerm() {
        if (orderDocumentJson.getOrderDetail().getPaymentTerm() == null) {
            return StringUtils.EMPTY;
        }

        return formatter.formatPaymentTerm(new PaymentTerm(orderDocumentJson.getOrderDetail().getPaymentTerm()));
    }

    public TemplateAccount getAccount() {
        return orderDocumentJson.getTemplateAccount();
    }

    public AccountContactJson getShippingContact() {
        return orderDocumentJson.getOrderDetail().getShippingContact();
    }

    public AccountContactJson getBillingContact() {
        return orderDocumentJson.getOrderDetail().getBillingContact();
    }

    public AccountJson getReseller() {
        return orderDocumentJson.getOrderDetail().getResoldBy();
    }

    public String getResellerName() {
        AccountJson resoldBy = orderDocumentJson.getOrderDetail().getResoldBy();
        if (Objects.isNull(resoldBy)) {
            return StringUtils.EMPTY;
        }

        return resoldBy.getName();
    }

    public AccountAddressJson getShippingAddress() {
        AccountContactJson shippingContact = getShippingContact();
        return shippingContact == null ? null : shippingContact.getAddress();
    }

    public AccountAddressJson getBillingAddress() {
        AccountContactJson billingContact = getBillingContact();
        return billingContact == null ? null : billingContact.getAddress();
    }

    public String getBillingContactPhoneNumber() {
        AccountContactJson billingContact = getBillingContact();
        return billingContact == null ? null : formatter.phoneNumberFormat(billingContact.getPhoneNumber());
    }

    public String getShippingContactPhoneNumber() {
        AccountContactJson shippingContact = getShippingContact();
        return shippingContact == null ? null : formatter.phoneNumberFormat(shippingContact.getPhoneNumber());
    }

    public String getOrderTotal() {
        return formatter.currencyFormat(orderDocumentJson.getOrderDetail().getTotalAmount(), getCurrency());
    }

    public String getTotal() {
        return getOrderTotal();
    }

    public BigDecimal getOrderTotalUnformatted() {
        return Numbers.makeCurrencyScale(orderDocumentJson.getOrderDetail().getTotalAmount(), getCurrency());
    }

    public String getTotalWithTax() {
        BigDecimal taxEstimate = Optional.ofNullable(orderDocumentJson.getOrderDetail().getTaxEstimate()).orElse(BigDecimal.ZERO);
        BigDecimal orderTotal = Optional.ofNullable(orderDocumentJson.getOrderDetail().getTotalAmount()).orElse(BigDecimal.ZERO);
        return formatter.currencyFormat(orderTotal.add(taxEstimate), getCurrency());
    }

    public String getTaxEstimate() {
        return formatter.currencyFormat(orderDocumentJson.getOrderDetail().getTaxEstimate(), getCurrency());
    }

    public List<OrderTemplateLineItem> getLineItems() {
        return getSortedLineItems(lineItems);
    }

    // return list price of recurring charge if there is a single charge
    // todo: what should happen if there are more than 1 charge, or no recurring charges?
    public String getListPrice() {
        List<OrderTemplateLineItem> recurringLineItems = getRecurringLineItems();

        if (recurringLineItems.size() == 1) {
            return recurringLineItems.get(0).getListPrice();
        }

        return StringUtils.EMPTY;
    }

    public String getOrderListPrice() {
        return formatter.currencyFormat(orderDocumentJson.getOrderDetail().getTotalListAmount(), getCurrency());
    }

    // @deprecated Use {@link #getOrderDiscountTotal()} instead
    @Deprecated
    public String getOrderListSellDelta() {
        BigDecimal list = orderDocumentJson.getOrderDetail().getTotalListAmount();
        BigDecimal sell = orderDocumentJson.getOrderDetail().getTotalAmount();

        return formatter.currencyFormat(list.subtract(sell), getCurrency());
    }

    public BigDecimal getOrderDiscountTotalUnformatted() {
        BigDecimal list = orderDocumentJson.getOrderDetail().getTotalListAmount();
        BigDecimal sell = orderDocumentJson.getOrderDetail().getTotalAmount();

        return list.subtract(sell);
    }

    public String getOrderDiscountTotal() {
        return formatter.currencyFormat(getOrderDiscountTotalUnformatted(), getCurrency());
    }

    public TemplateDiscounts getDiscounts() {
        return new TemplateDiscounts(orderDocumentJson.getOrderDetail(), formatter, getCurrency());
    }

    public String getDiscountPercent() {
        BigDecimal discountPercent = DiscountCalculator.getDiscountPercent(
            orderDocumentJson.getOrderDetail().getTotalAmount(),
            orderDocumentJson.getOrderDetail().getTotalListAmount()
        );

        if (Numbers.isZero(discountPercent)) {
            return StringUtils.EMPTY;
        }

        return formatter.formatPercent(discountPercent);
    }

    public String getRecurringTotal() {
        BigDecimal totalAmount = MetricsService.getOrderRecurringTotal(orderDocumentJson.getOrderDetail());
        return formatter.currencyFormat(totalAmount, getCurrency());
    }

    public String getNonRecurringTotal() {
        BigDecimal totalAmount = MetricsService.getOrderNonRecurringTotal(orderDocumentJson.getOrderDetail());
        return formatter.currencyFormat(totalAmount, getCurrency());
    }

    public boolean getHasRecurring() {
        return lineItems.stream().anyMatch(OrderTemplateLineItem::getIsRecurring);
    }

    public boolean getHasUsage() {
        return lineItems.stream().anyMatch(OrderTemplateLineItem::getIsUsage);
    }

    public boolean getIsRecurringMonthly() {
        return lineItems.stream().filter(OrderTemplateLineItem::getIsChargeTypeRecurring).allMatch(OrderTemplateLineItem::getIsRecurringMonthly);
    }

    public boolean getIsRecurringYearly() {
        return lineItems.stream().filter(OrderTemplateLineItem::getIsChargeTypeRecurring).allMatch(OrderTemplateLineItem::getIsRecurringYearly);
    }

    public MetricsJson getOrderMetrics() {
        return orderDocumentJson.getOrderDetail().getMetrics();
    }

    public String getOrderEntryArr() {
        if (orderDocumentJson.getOrderDetail().getMetrics() == null) {
            return StringUtils.EMPTY;
        }

        return formatter.currencyFormat(orderDocumentJson.getOrderDetail().getMetrics().getEntryArr(), getCurrency());
    }

    public String getOrderEntryMrr() {
        if (orderDocumentJson.getOrderDetail().getMetrics() == null) {
            return StringUtils.EMPTY;
        }

        return formatter.currencyFormat(
            TemplateCalculations.yearlyToMonthlyAmount(orderDocumentJson.getOrderDetail().getMetrics().getEntryArr()),
            getCurrency()
        );
    }

    public String getExecutedOrderMonthlyEntryArr() {
        if (CollectionUtils.isEmpty(executedLineItems)) {
            return StringUtils.EMPTY;
        }

        BigDecimal executedOrderEntryArr = executedLineItems
            .stream()
            .filter(item -> checkOrderLineItemActiveOnDate(item.getOrderLineItemDetail(), orderDocumentJson.getOrderDetail().getStartDate()))
            .map(OrderTemplateLineItem::getMonthlyAmountRawValue)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        return formatter.currencyFormat(executedOrderEntryArr, getCurrency());
    }

    public static boolean checkOrderLineItemActiveOnDate(OrderLineItemDetail orderLineItem, Long date) {
        Instant itemStartDate = Instant.ofEpochSecond(orderLineItem.getEffectiveDate());
        Instant itemEndDate = Instant.ofEpochSecond(orderLineItem.getEndDate());

        Instant dateInstant = Instant.ofEpochSecond(date);
        return !itemStartDate.isAfter(dateInstant) && itemEndDate.isAfter(dateInstant);
    }

    public String getCommonChargeRecurrence() {
        if (getIsRecurringMonthly()) {
            return RECURRENCE_MONTHLY;
        } else if (getIsRecurringYearly()) {
            return RECURRENCE_YEARLY;
        } else {
            return StringUtils.EMPTY;
        }
    }

    public boolean getHasNonRecurring() {
        return lineItems.stream().anyMatch(Predicate.not(OrderTemplateLineItem::getIsRecurring));
    }

    // todo: need to rethink the structure here
    public String getProductName() {
        List<OrderTemplateLineItem> recurringLineItems = getRecurringLineItems();

        var plans = recurringLineItems.stream().map(item -> item.getOrderLineItemDetail().getPlan()).collect(Collectors.groupingBy(PlanJson::getId));
        if (plans.size() == 1) {
            var entry = plans.entrySet().iterator().next();
            return entry.getValue().get(0).getName();
        } else {
            return StringUtils.EMPTY;
        }
    }

    public List<OrderTemplateProduct> getOrderProducts() {
        List<OrderTemplateLineItem> lineItems = getLineItems();
        Map<String, OrderTemplateProduct> products = new LinkedHashMap<>();

        getOrderProductsForLineItems(lineItems, products);

        return products.values().stream().toList();
    }

    public List<OrderTemplateProduct> getOrderProductsForExecutedLineItems() {
        Map<String, OrderTemplateProduct> products = new LinkedHashMap<>();

        getOrderProductsForLineItems(executedLineItems, products);

        return products.values().stream().toList();
    }

    public List<OrderTemplateProduct> getOrderProductsForPostSubscriptionLineItems() {
        Map<String, OrderTemplateProduct> products = new LinkedHashMap<>();
        getOrderProductsForLineItems(postSubscriptionLineItemsView, products);

        return products.values().stream().toList();
    }

    private void getOrderProductsForLineItems(List<OrderTemplateLineItem> lineItems, Map<String, OrderTemplateProduct> products) {
        for (OrderTemplateLineItem lineItem : lineItems) {
            PlanJson plan = lineItem.getOrderLineItemDetail().getPlan();
            ProductJson productJson = orderDocumentJson.getProducts().get(plan.getProductId());
            String productName = StringUtils.isBlank(productJson.getDisplayName()) ? productJson.getName() : productJson.getDisplayName();
            String sku = productJson.getSku();
            String productDescription = productJson.getDescription();
            products.putIfAbsent(
                plan.getProductId(),
                new OrderTemplateProduct(
                    plan.getProductId(),
                    productName,
                    sku,
                    productDescription,
                    formatter,
                    getCurrency(),
                    lineItemOrderByCustomFieldName
                )
            );
            var product = products.get(plan.getProductId());
            product.addLineItem(
                plan.getId(),
                plan.getExternalId(),
                StringUtils.isBlank(plan.getDisplayName()) ? plan.getName() : plan.getDisplayName(),
                plan.getDescription(),
                lineItem,
                plan.getCustomFields()
            );
        }
    }

    public List<OrderTemplateProductCategory> getProductCategories() {
        List<OrderTemplateLineItem> lineItems = getLineItems();
        Map<String, OrderTemplateProductCategory> productCategoriesMap = new HashMap<>();

        for (OrderTemplateLineItem lineItem : lineItems) {
            PlanJson plan = lineItem.getOrderLineItemDetail().getPlan();
            ProductJson productJson = orderDocumentJson.getProducts().get(plan.getProductId());
            if (StringUtils.isNotBlank(productJson.getProductCategoryId())) {
                OrderTemplateProductCategory orderTemplateProductCategory = productCategoriesMap.get(productJson.getProductCategoryId());
                if (orderTemplateProductCategory == null) {
                    orderTemplateProductCategory = new OrderTemplateProductCategory();
                    orderTemplateProductCategory.setCategoryId(productJson.getProductCategoryId());
                    orderTemplateProductCategory.setCategoryName(productJson.getProductCategory().getName());
                    orderTemplateProductCategory.setFirstQuantity(lineItem.getQuantity());
                    orderTemplateProductCategory.setTotalArrAmount(BigDecimal.ZERO);
                    orderTemplateProductCategory.setOneTimeTotalAmount(BigDecimal.ZERO);
                    orderTemplateProductCategory.setProducts(new ArrayList<>());
                    orderTemplateProductCategory.setTermLengthInMonths(getOrderLengthInMonths());
                    orderTemplateProductCategory.setOneTime(lineItem.getChargeType() == ChargeType.ONE_TIME);
                }

                // add ARRs of line items to the total ARR of the product category
                BigDecimal yearlyAmount = lineItem.getYearlyAmountValue().orElse(BigDecimal.ZERO);
                BigDecimal productTotalArr = orderTemplateProductCategory.getTotalArrAmount().add(yearlyAmount);
                orderTemplateProductCategory.setTotalArrAmount(productTotalArr);

                // add one-time charges to the total one-time charges of the product category
                if (lineItem.getChargeType() == ChargeType.ONE_TIME) {
                    BigDecimal oneTimeAmount = lineItem.getOrderLineItemDetail().getAmount();
                    BigDecimal productOneTimeTotal = orderTemplateProductCategory.getOneTimeTotalAmount().add(oneTimeAmount);
                    orderTemplateProductCategory.setOneTimeTotalAmount(productOneTimeTotal);
                }

                Set<ProductJson> productsSet = new HashSet<>(orderTemplateProductCategory.getProducts());
                productsSet.add(productJson);
                orderTemplateProductCategory.setProducts(productsSet.stream().toList());
                productCategoriesMap.put(productJson.getProductCategoryId(), orderTemplateProductCategory);
            }
        }

        var productCategories = productCategoriesMap.values().stream().toList();
        // format amounts
        for (var productCategory : productCategories) {
            productCategory.setTotalArr(formatter.currencyFormat(productCategory.getTotalArrAmount(), getCurrency()));
            productCategory.setOneTimeTotal(formatter.currencyFormat(productCategory.getOneTimeTotalAmount(), getCurrency()));
        }

        return productCategories;
    }

    public List<OrderTemplateLineItem> getRecurringLineItems() {
        List<OrderTemplateLineItem> recurringLineItems = lineItems
            .stream()
            .filter(OrderTemplateLineItem::getIsRecurring)
            .collect(Collectors.toList());
        return getSortedLineItems(recurringLineItems);
    }

    public List<OrderTemplateLineItem> getNonRecurringLineItems() {
        List<OrderTemplateLineItem> nonRecurringLineItems = lineItems
            .stream()
            .filter(Predicate.not(OrderTemplateLineItem::getIsRecurring))
            .collect(Collectors.toList());
        return getSortedLineItems(nonRecurringLineItems);
    }

    public String getPaymentLink() {
        return orderDocumentJson.getPaymentLink();
    }

    public Map<DocumentSectionLocation, String> getFormattedDocumentSectionsByLocation() {
        return formattedDocumentSectionsByLocation;
    }

    public SortedMap<DocumentSection, SortedSet<DocumentTemplateResponse>> getDocumentTemplatesBySection() {
        return orderDocumentJson.getDocumentTemplatesBySection();
    }

    public void setFormattedDocumentSectionsByLocation(Map<DocumentSectionLocation, String> formattedDocumentSectionsByLocation) {
        this.formattedDocumentSectionsByLocation = formattedDocumentSectionsByLocation;
    }

    public String getFormattedBeforeSignatureSection() {
        return formattedDocumentSectionsByLocation.get(DocumentSectionLocation.BEFORE_SIGNATURE);
    }

    public String getFormattedAfterSignatureSection() {
        return formattedDocumentSectionsByLocation.get(DocumentSectionLocation.AFTER_SIGNATURE);
    }

    public Map<String, Boolean> hasPredefinedTerm() {
        return predefinedTermsMap.keySet().stream().collect(Collectors.toMap(Function.identity(), termName -> true));
    }

    public Map<String, String> getPredefinedTerms() {
        return predefinedTermsMap;
    }

    public void setPredefinedTermsMap(Map<String, String> predefinedTermsMap) {
        this.predefinedTermsMap = predefinedTermsMap;
    }

    public List<DocumentTemplateResponse> getDocumentTemplatesWithoutSection() {
        return orderDocumentJson.getDocumentTemplatesWithoutSection();
    }

    public boolean hasDocumentTemplatesWithoutSection() {
        return CollectionUtils.isNotEmpty(orderDocumentJson.getDocumentTemplatesWithoutSection());
    }

    public String getFormattedDocumentTemplatesWithoutSection() {
        return formattedDocumentTemplatesWithoutSection;
    }

    public void setFormattedDocumentTemplatesWithoutSection(String formattedDocumentTemplatesWithoutSection) {
        this.formattedDocumentTemplatesWithoutSection = formattedDocumentTemplatesWithoutSection;
    }

    public String getFormattedCustomContent() {
        return formattedCustomContent;
    }

    public void setFormattedCustomContent(String formattedCustomContent) {
        this.formattedCustomContent = formattedCustomContent;
    }

    public String getCustomContentTitle() {
        if (orderDocumentJson.getOrderDetail().getDocumentCustomContent() == null) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.isBlank(orderDocumentJson.getOrderDetail().getDocumentCustomContent().getContent())) {
            return StringUtils.EMPTY;
        }
        return orderDocumentJson.getOrderDetail().getDocumentCustomContent().getTitle();
    }

    public List<DocumentInvoicePreview> getInvoicePreviews() {
        List<DocumentInvoicePreview> previews = orderDocumentJson.getDocumentInvoicePreviews();

        if (CollectionUtils.isEmpty(previews)) {
            return List.of();
        }

        // filter out non-zero previews to maintain backwards compatibility
        return previews.stream().filter(preview -> !preview.hasZeroAmount()).toList();
    }

    public List<DocumentInvoicePreview> getFullInvoicePreviews() {
        return orderDocumentJson.getDocumentInvoicePreviews();
    }

    public List<DocumentInvoicePreview> getFullSubscriptionInvoicePreviews() {
        return orderDocumentJson.getSubscriptionInvoicePreviews();
    }

    public List<DocumentInvoicePreview> getSubscriptionInvoicePreviews() {
        if (orderDocumentJson.getSubscriptionInvoicePreviews() == null) {
            return List.of();
        }

        return orderDocumentJson.getSubscriptionInvoicePreviews().stream().filter(preview -> !preview.hasZeroAmount()).toList();
    }

    public List<DocumentArrProjection> getArrProjections() {
        return orderDocumentJson.getArrProjections();
    }

    // get first non-zero invoice amount
    public String getFirstInvoiceAmount() {
        if (CollectionUtils.isEmpty(getInvoicePreviews())) {
            return formatter.currencyFormat(BigDecimal.ZERO, getCurrency());
        }
        return getInvoicePreviews().get(0).getAmount();
    }

    public DocumentInvoicePreview getFirstInvoice() {
        if (CollectionUtils.isEmpty(getInvoicePreviews())) {
            return null;
        }
        return getInvoicePreviews().get(0);
    }

    // get first invoice amount even if $0
    public String getFullPreviewFirstInvoiceAmount() {
        if (CollectionUtils.isEmpty(getFullInvoicePreviews())) {
            return formatter.currencyFormat(BigDecimal.ZERO, getCurrency());
        }
        return getFullInvoicePreviews().get(0).getAmount();
    }

    public DocumentInvoicePreview getFullPreviewFirstInvoice() {
        if (CollectionUtils.isEmpty(getFullInvoicePreviews())) {
            return null;
        }
        return getFullInvoicePreviews().get(0);
    }

    public String getFullSubscriptionInvoicePreviewFirstInvoiceAmount() {
        if (CollectionUtils.isEmpty(getFullSubscriptionInvoicePreviews())) {
            return formatter.currencyFormat(BigDecimal.ZERO, getCurrency());
        }
        return getFullSubscriptionInvoicePreviews().get(0).getAmount();
    }

    public DocumentInvoicePreview getFullSubscriptionInvoicePreviewFirstInvoice() {
        if (CollectionUtils.isEmpty(getFullSubscriptionInvoicePreviews())) {
            return null;
        }
        return getFullSubscriptionInvoicePreviews().get(0);
    }

    public String getFirstSubscriptionInvoiceAmount() {
        if (CollectionUtils.isEmpty(getSubscriptionInvoicePreviews())) {
            return formatter.currencyFormat(BigDecimal.ZERO, getCurrency());
        }
        return getSubscriptionInvoicePreviews().get(0).getAmount();
    }

    public DocumentInvoicePreview getFirstSubscriptionInvoice() {
        if (CollectionUtils.isEmpty(getSubscriptionInvoicePreviews())) {
            return null;
        }
        return getSubscriptionInvoicePreviews().get(0);
    }

    public SubscriptionDetail getCurrentSubscription() {
        return orderDocumentJson.getOrderDetail().getCurrentSubscription();
    }

    public String getFirstOrderIdInSubscriptionChain() {
        OrderJson order = orderDocumentJson.getOriginalOrderInChain();
        if (order == null) {
            return null;
        }

        return order.getId();
    }

    public String getFirstOrderStartDateInSubscriptionChain() {
        OrderJson order = orderDocumentJson.getOriginalOrderInChain();
        if (order == null) {
            return null;
        }

        return formatter.dateFormat(order.getStartDate());
    }

    public String getOriginalOrderId() {
        if (getCurrentSubscription() != null) {
            return getCurrentSubscription().getOrders().get(0);
        }

        return StringUtils.EMPTY;
    }

    public String getSubscriptionStartDate() {
        if (orderDocumentJson.getOrderDetail().getCurrentSubscription() == null) {
            return StringUtils.EMPTY;
        }
        return formatter.dateFormat(orderDocumentJson.getOrderDetail().getCurrentSubscription().getStartDate());
    }

    public String getSubscriptionEndDate() {
        if (orderDocumentJson.getOrderDetail().getCurrentSubscription() == null) {
            return StringUtils.EMPTY;
        }
        return formatter.endDateFormat(orderDocumentJson.getOrderDetail().getCurrentSubscription().getEndDate());
    }

    private BigDecimal getCurrentSubscriptionTcv() {
        if (orderDocumentJson.getOrderDetail().getCurrentSubscription() == null) {
            return BigDecimal.ZERO;
        }
        return orderDocumentJson.getOrderDetail().getCurrentSubscription().getMetrics().getTcv();
    }

    public String getRemainingTermInDays() {
        if (orderDocumentJson.getOrderDetail().getCurrentSubscription() == null) {
            return StringUtils.EMPTY;
        }

        long days = DateTimeCalculator.getDaysBetween(
            Instant.ofEpochSecond(orderDocumentJson.getOrderDetail().getStartDate()),
            Instant.ofEpochSecond(orderDocumentJson.getOrderDetail().getCurrentSubscription().getEndDate()),
            timeZone
        );
        return String.format("%d Days", days);
    }

    public String getCurrentSubscriptionTotal() {
        return formatter.currencyFormat(getCurrentSubscriptionTcv(), getCurrency());
    }

    public String getNewContractTotal() {
        var newContractTotal = getCurrentSubscriptionTcv().add(orderDocumentJson.getOrderDetail().getTotalAmount());
        return formatter.currencyFormat(newContractTotal, getCurrency());
    }

    public List<OrderTemplateLineItem> getExecutedLineItems() {
        return getSortedLineItems(executedLineItems);
    }

    public List<OrderTemplateLineItem> getPostSubscriptionLineItemsView() {
        return postSubscriptionLineItemsView;
    }

    public Map<String, Boolean> getBillingCycles() {
        Map<String, Boolean> billingCycles = new HashMap<>();
        RecurrenceJson billingCycle = orderDocumentJson.getOrderDetail().getBillingCycle();

        if (billingCycle == null) {
            return Map.of();
        }

        if (billingCycle.getCycle() != null) {
            billingCycles.put(billingCycle.getCycle().getPeriodicityName(), true);
        }

        return billingCycles;
    }

    public boolean getIsYearlyBilling() {
        return (
            orderDocumentJson.getOrderDetail() != null &&
            orderDocumentJson.getOrderDetail().getBillingCycle() != null &&
            orderDocumentJson.getOrderDetail().getBillingCycle().getCycle() == YEAR
        );
    }

    public boolean getIsMonthlyBilling() {
        return orderDocumentJson.getOrderDetail().getBillingCycle().getCycle() == MONTH;
    }

    public String getPurchaseOrderNumber() {
        return orderDocumentJson.getOrderDetail().getPurchaseOrderNumber();
    }

    public boolean isPurchaseOrderRequired() {
        return orderDocumentJson.getOrderDetail().getPurchaseOrderRequiredForInvoicing();
    }

    // TODO: rename to supported payment types
    public String getPreferredPaymentType() {
        if (orderDocumentJson.getOrderDetail().getAccount() == null) {
            return StringUtils.EMPTY;
        }

        Set<PaymentType> supportedPaymentTypes = orderDocumentJson.getOrderDetail().getAccount().getSupportedPaymentTypes();
        if (CollectionUtils.isEmpty(supportedPaymentTypes)) {
            return StringUtils.EMPTY;
        }

        return supportedPaymentTypes.stream().sorted().map(PaymentType::getDisplayName).collect(Collectors.joining(", "));
    }

    public DocumentRenderingOptions getRenderingOptions() {
        return orderDocumentJson.getDocumentRenderingOptions();
    }

    // provide alias so children objects (e.g. line items) can access order level custom fields inside their scope
    public Map<String, TemplateCustomField> getOrderCustomFields() {
        return getCustomFields();
    }

    public Map<String, TemplateCustomField> getOpportunityCustomFields() {
        return TemplateCustomField.getCustomFieldsMap(orderDocumentJson.getOpportunityCustomFields());
    }

    // get custom field to display on order form or use as conditional logic.
    // Custom fields can be accessed via {{customFields.<fieldName>}} for display purposes
    // When used for conditional logic, use: {{#customFields.<fieldName>.hasValue.<value>}} (picklist or string types) or
    // {{#customFields.<fieldName>.hasSelection.<selection>}} (picklist or multi-select picklist types)
    public Map<String, TemplateCustomField> getCustomFields() {
        return TemplateCustomField.getCustomFieldsMap(orderDocumentJson.getCustomField());
    }

    // returns list of line level predefined discounts grouped by discount id
    // also, maps each line level predefined discount record to order template predefined discount
    private Map<String, List<OrderTemplatePredefinedDiscount>> getPredefinedDiscountsGrouped() {
        return orderDocumentJson
            .getOrderDetail()
            .getLineItems()
            .stream()
            .filter(orderLineItemDetail -> orderLineItemDetail.getAction() != ActionType.NONE)
            .map(OrderLineItemDetail::getPredefinedDiscounts)
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(List::stream)
            .map(predefinedDiscountDetailMapper::toOrderTemplatePredefinedDiscount)
            .collect(Collectors.groupingBy(OrderTemplatePredefinedDiscount::getId));
    }

    // aggregate individual line item discount records to order level by predefined discount id
    // it picks the first discount record, and sums up the discount amount
    private Map<String, OrderTemplatePredefinedDiscount> aggregatePredefinedDiscountAmounts(
        Map<String, List<OrderTemplatePredefinedDiscount>> groupedPredefinedDiscounts
    ) {
        Map<String, OrderTemplatePredefinedDiscount> aggregatedOrderDiscounts = new HashMap<>();
        groupedPredefinedDiscounts.forEach((discountId, discountList) -> {
            OrderTemplatePredefinedDiscount reducedDiscount = discountList
                .stream()
                .reduce((discount1, discount2) -> {
                    discount1.setAmount(discount1.getAmount().add(discount2.getAmount()));
                    return discount1;
                })
                .orElse(null);
            aggregatedOrderDiscounts.put(discountId, reducedDiscount);
        });
        return aggregatedOrderDiscounts;
    }

    // maintain running total after discount, by subtracting each discount from order total list amount
    private void populateRunningTotalAfterDiscount(Map<String, OrderTemplatePredefinedDiscount> orderDiscountMap) {
        BigDecimal currentTotal = orderDocumentJson.getOrderDetail().getTotalListAmount();
        for (var orderDiscount : orderDiscountMap.values()) {
            currentTotal = currentTotal.subtract(orderDiscount.getAmount());
            orderDiscount.setRunningTotalAfterDiscount(currentTotal);
        }
    }

    // Get predefined (tenant) discounts at order level
    // add up all line level discount amounts and roll up to order level
    // maintain running total after discount
    public List<OrderTemplatePredefinedDiscount> getPredefinedDiscounts() {
        Map<String, List<OrderTemplatePredefinedDiscount>> lineDiscountsMap = getPredefinedDiscountsGrouped();
        Map<String, OrderTemplatePredefinedDiscount> orderDiscountsMap = aggregatePredefinedDiscountAmounts(lineDiscountsMap);
        populateRunningTotalAfterDiscount(orderDiscountsMap);
        return getOrderedPredefinedDiscounts(orderDiscountsMap);
    }

    private List<OrderTemplatePredefinedDiscount> getOrderedPredefinedDiscounts(Map<String, OrderTemplatePredefinedDiscount> orderDiscountsMap) {
        List<PredefinedDiscountDetail> predefinedDiscounts = orderDocumentJson.getOrderDetail().getPredefinedDiscounts();

        if (orderDiscountsMap == null || predefinedDiscounts == null) {
            return List.of();
        }

        List<OrderTemplatePredefinedDiscount> orderedTemplatePredefinedDiscounts = new ArrayList<>();

        for (PredefinedDiscountDetail predefinedDiscount : predefinedDiscounts) {
            if (Objects.nonNull(orderDiscountsMap.get(predefinedDiscount.getId()))) {
                orderedTemplatePredefinedDiscounts.add(orderDiscountsMap.get(predefinedDiscount.getId()));
            }
        }

        return CollectionUtils.isNotEmpty(orderedTemplatePredefinedDiscounts) ? List.copyOf(orderedTemplatePredefinedDiscounts) : List.of();
    }

    public BigDecimal getTotalPredefinedDiscountAmount() {
        List<OrderTemplatePredefinedDiscount> predefinedDiscounts = getPredefinedDiscounts();
        return predefinedDiscounts.stream().map(OrderTemplatePredefinedDiscount::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    public boolean hasPredefinedDiscounts() {
        return !getPredefinedDiscounts().isEmpty();
    }

    public String getCurrency() {
        if (StringUtils.isNotBlank(orderDocumentJson.getOrderDetail().getCurrency())) {
            return orderDocumentJson.getOrderDetail().getCurrency();
        }
        if (orderDocumentJson.getOrderDetail().getAccount() == null) {
            return SupportedCurrency.getDefaultCurrency().getCurrencyCode();
        }

        return orderDocumentJson.getOrderDetail().getAccount().getCurrency();
    }

    public Map<String, Boolean> isCurrency() {
        return Map.of(getCurrency(), true);
    }

    public boolean anyLineItemHasLineDiscount() {
        return lineItems.stream().anyMatch(OrderTemplateLineItem::hasLineItemDiscount);
    }

    // custom field aggregated checks for plan:
    // {{#anyPlanCustomField.<cf_name>.isTrue}} will return true if any of the plan has "cf_name" custom field set to truthy value
    // {{#anyPlanCustomField.<cf_name>.hasValue.<value>}} will return true if any of the plan has "cf_name" custom field set to "value"
    public Map<String, CustomFieldCollection> getAnyPlanCustomField() {
        return CustomFieldCollection.getCustomFieldsMap(orderDocumentJson.getPlanCustomFields());
    }

    // custom field aggregated checks for order line items:
    // {{#anyLineItemCustomField.<cf_name>.isTrue}} will return true if any of the line items has "cf_name" custom field set to truthy value
    // {{#anyLineItemCustomField.<cf_name>.hasValue.<value>}} will return true if any of the line items has "cf_name" custom field set to "value"
    public Map<String, CustomFieldCollection> getAnyLineItemCustomField() {
        return CustomFieldCollection.getCustomFieldsMap(orderDocumentJson.getLineItemCustomFields());
    }

    public EsignDocumentTags getCustomerEsignDocumentTags() {
        return orderDocumentJson.getCustomerEsignDocumentTags();
    }

    public EsignDocumentTags getSignatoryEsignDocumentTags() {
        return orderDocumentJson.getSignatoryEsignDocumentTags();
    }

    // get sum of item quantities by UOM: {{quantityByMeasure.<UOM>}}
    // or "none" for sum of items without unit of measure: {{quantityByMeasure.none}}
    public Map<String, String> getQuantityByMeasure() {
        return OrderTemplateItemAggregator.getQuantityByMeasure(lineItems);
    }

    public Map<String, Boolean> hasProductId() {
        return getOrderProducts().stream().map(OrderTemplateProduct::getProductId).collect(Collectors.toMap(Function.identity(), productId -> true));
    }

    public Map<String, Boolean> hasPlanId() {
        return getLineItems()
            .stream()
            .map(OrderTemplateLineItem::getPlanId)
            .distinct()
            .collect(Collectors.toMap(Function.identity(), planId -> true));
    }

    // template switch to skip verification if needed. Default is true
    public Function<String, Void> verifyDeltaRampTotals() {
        return input -> {
            if (StringUtils.isNotBlank(input)) {
                verifyDeltaRampTotals = "true".equals(input.trim());
            }

            return null;
        };
    }

    public TemplateDeltaRamps getTemplateDeltaRamps() {
        List<OrderLineItemDetail> lineItemDetails = getLineItems().stream().map(OrderTemplateLineItem::getOrderLineItemDetail).toList();
        return new TemplateDeltaRamps(
            lineItemDetails,
            orderDocumentJson.getOrderDetail().getEndDate(),
            formatter,
            getCurrency(),
            timeZone,
            verifyDeltaRampTotals
        );
    }

    public OrderDocumentJson getOrderDocumentJson() {
        return orderDocumentJson;
    }

    // set custom field name to group plans by
    // Example: {{#groupPlansByCustomField}}<cf_name>{{/groupPlansByCustomField}}
    // todo: this functionality should be moved to Groovy based templating engine
    public Function<String, Void> groupPlansByCustomField() {
        return input -> {
            if (StringUtils.isNotBlank(input)) {
                planGroupByCustomFieldName = input.trim();
            }

            return null;
        };
    }

    // return plans grouped by custom field value. Value can optionally contain a list of tokens split by ;
    public Map<String, List<OrderTemplatePlan>> getGroupedPlans() {
        if (StringUtils.isEmpty(planGroupByCustomFieldName)) {
            return Map.of();
        }

        Map<String, List<OrderTemplatePlan>> planGroups = new HashMap<>();
        lineItems.forEach(lineItem -> {
            List<String> tokens = getFieldTokens(lineItem, planGroupByCustomFieldName);

            for (String token : tokens) {
                planGroups.putIfAbsent(token, new ArrayList<>());
                List<OrderTemplatePlan> plans = planGroups.get(token);
                addIfNotExists(plans, lineItem.getPlan());
            }
        });

        // Use TreeMap to sort by key
        return new TreeMap<>(planGroups);
    }

    private static void addIfNotExists(List<OrderTemplatePlan> plans, OrderTemplatePlan plan) {
        if (plans.stream().noneMatch(p -> p.getId().equals(plan.getId()))) {
            plans.add(plan);
        }
    }

    private static List<String> getFieldTokens(OrderTemplateLineItem lineItem, String customFieldName) {
        TemplateCustomField customField = lineItem.getCustomFields().get(customFieldName);
        if (customField == null) {
            return List.of();
        }

        String fieldValue = customField.getValue();
        if (StringUtils.isBlank(fieldValue)) {
            return List.of();
        }

        String[] tokens = fieldValue.split(";");
        return Arrays.stream(tokens).map(String::trim).toList();
    }
}
