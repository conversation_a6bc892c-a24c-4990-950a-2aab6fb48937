package com.subskribe.billy.order.model;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.WithCustomField;
import com.subskribe.billy.customization.model.OrderCreationCustomizationOutput;
import com.subskribe.billy.graphql.order.CreditableAmount;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrder;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.metrics.model.StoredMetrics;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.model.OpportunityCrmType;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.TenantDiscount;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.template.model.DocumentCustomContent;
import com.subskribe.billy.template.model.OrderTerms;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Currency;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class Order implements WithCustomField {

    private static final Logger LOGGER = LoggerFactory.getLogger(Order.class);

    private UUID id;
    private String name;
    private String orderId;
    private String tenantId;
    private String entityId;
    private String externalId;
    private String accountId;
    private String resellerAccountId;
    private OrderType orderType;
    private Currency currency;
    private PaymentTerm paymentTerm;
    private UUID subscriptionUuid;
    private String externalSubscriptionId;
    private int subscriptionTargetVersion;
    private String shippingContactId;
    private String billingContactId;
    private List<TenantDiscount> predefinedDiscounts;
    private List<OrderLineItem> lineItems;
    private List<OrderLineItem> lineItemsNetEffect;
    private List<OrderLineItem> missingRenewalOrderLineItems;
    private Instant startDate;
    private Instant endDate;
    private Instant billingAnchorDate;
    private Recurrence termLength;
    private Recurrence billingCycle;
    private BillingTerm billingTerm;
    private BigDecimal totalListAmount;
    private BigDecimal totalListAmountBeforeOverride;
    private BigDecimal taxEstimate;
    private BigDecimal totalAmount;
    private OrderStatus status;
    private String renewalForSubscriptionId;
    private int renewalForSubscriptionVersion;
    private String restructureForSubscriptionId;
    private int restructureForSubscriptionVersion;
    private String createdBy;
    private Instant createdOn;
    private Instant updatedOn;
    private Instant executedOn;
    private String executedOnFormatted;
    private List<Instant> rampInterval;
    private List<String> orderFormTemplateIds;
    private List<OrderTerms> orderTerms;

    // TODO: Rename and remove _sfdc_ from all following fields.
    private String sfdcOpportunityId;
    private Boolean isPrimaryOrderForSfdcOpportunity;
    private String sfdcOpportunityName;
    private String sfdcOpportunityType;
    private String sfdcOpportunityStage;
    private Boolean sfdcOrderCanBeExecuted;
    private OpportunityCrmType opportunityCrmType;
    private String ownerId;
    private UUID documentMasterTemplateId;
    private DocumentCustomContent documentCustomContent;
    private StoredMetrics metrics;
    private String purchaseOrderNumber;
    private Boolean purchaseOrderRequiredForInvoicing;
    private boolean autoRenew;
    private String approvalSegmentId;
    private String attachmentId;
    private CustomField customFields;
    private String compositeOrderId;
    private Boolean shouldRegeneratePdf;
    private Instant expiresOn;
    private String submittedBy;
    private OrderSource source;
    private OrderStartDateType startDateType;
    private List<CreditableAmount> creditableAmounts;
    private Optional<OrderProcessingMetadata> orderProcessingMetadata;
    private List<String> customBillingEligibleOrderLineIds;
    private CustomBillingSchedule customBillingSchedule;
    private List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder;
    private SubscriptionDurationModel subscriptionDurationModel;
    private Opportunity opportunity;
    private OrderCreationCustomizationOutput zeppaOutput;

    public Order() {
        customFields = new CustomField(new HashMap<>());
        orderProcessingMetadata = Optional.empty();
    }

    public Order(
        UUID id,
        String name,
        String orderId,
        String tenantId,
        String entityId,
        String externalId,
        String accountId,
        String resellerAccountId,
        OrderType orderType,
        Currency currency,
        PaymentTerm paymentTerm,
        UUID subscriptionUuid,
        String externalSubscriptionId,
        String shippingContactId,
        String billingContactId,
        List<TenantDiscount> predefinedDiscounts,
        List<OrderLineItem> lineItems,
        List<OrderLineItem> lineItemsNetEffect,
        Instant startDate,
        Instant endDate,
        Instant billingAnchorDate,
        Recurrence termLength,
        Recurrence billingCycle,
        BillingTerm billingTerm,
        BigDecimal totalListAmount,
        BigDecimal totalListAmountBeforeOverride,
        BigDecimal taxEstimate,
        BigDecimal totalAmount,
        OrderStatus status,
        String createdBy,
        Instant createdOn,
        Instant updatedOn,
        Instant executedOn,
        List<Instant> rampInterval,
        List<String> orderFormTemplateIds,
        List<OrderTerms> orderTerms,
        String sfdcOpportunityId,
        Boolean isPrimaryOrderForSfdcOpportunity,
        String sfdcOpportunityName,
        String sfdcOpportunityType,
        String sfdcOpportunityStage,
        Boolean sfdcOrderCanBeExecuted,
        OpportunityCrmType opportunityCrmType,
        String ownerId,
        String renewalForSubscriptionId,
        int renewalForSubscriptionVersion,
        String restructureForSubscriptionId,
        int restructureForSubscriptionVersion,
        String purchaseOrderNumber,
        Boolean purchaseOrderRequiredForInvoicing,
        boolean autoRenew,
        String approvalSegmentId,
        CustomField customFields,
        String compositeOrderId,
        Boolean shouldRegeneratePdf,
        Instant expiresOn,
        String submittedBy,
        OrderSource source,
        OrderStartDateType startDateType,
        List<CreditableAmount> creditableAmounts,
        Optional<OrderProcessingMetadata> orderProcessingMetadata,
        SubscriptionDurationModel subscriptionDurationModel,
        Opportunity opportunity
    ) {
        this.id = id;
        this.orderId = orderId;
        this.tenantId = tenantId;
        this.entityId = entityId;
        this.externalId = externalId;
        this.accountId = accountId;
        this.resellerAccountId = resellerAccountId;
        this.orderType = orderType;
        this.currency = currency;
        this.paymentTerm = paymentTerm;
        this.subscriptionUuid = subscriptionUuid;
        this.externalSubscriptionId = externalSubscriptionId;
        this.shippingContactId = shippingContactId;
        this.billingContactId = billingContactId;
        this.predefinedDiscounts = predefinedDiscounts;
        this.lineItems = lineItems;
        this.lineItemsNetEffect = lineItemsNetEffect;
        this.startDate = startDate;
        this.endDate = endDate;
        this.billingAnchorDate = billingAnchorDate;
        this.termLength = termLength;
        this.billingCycle = billingCycle;
        this.billingTerm = billingTerm;
        this.totalListAmount = totalListAmount;
        this.totalListAmountBeforeOverride = totalListAmountBeforeOverride;
        this.taxEstimate = taxEstimate;
        this.totalAmount = totalAmount;
        this.status = status;
        this.createdBy = createdBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.name = name;
        this.executedOn = executedOn;
        this.rampInterval = rampInterval;
        this.orderFormTemplateIds = orderFormTemplateIds;
        this.orderTerms = orderTerms;
        this.sfdcOpportunityId = sfdcOpportunityId;
        this.isPrimaryOrderForSfdcOpportunity = isPrimaryOrderForSfdcOpportunity;
        this.sfdcOpportunityName = sfdcOpportunityName;
        this.sfdcOpportunityType = sfdcOpportunityType;
        this.sfdcOpportunityStage = sfdcOpportunityStage;
        this.sfdcOrderCanBeExecuted = sfdcOrderCanBeExecuted;
        this.opportunityCrmType = opportunityCrmType;
        this.ownerId = ownerId;
        this.renewalForSubscriptionId = renewalForSubscriptionId;
        this.renewalForSubscriptionVersion = renewalForSubscriptionVersion;
        this.restructureForSubscriptionId = restructureForSubscriptionId;
        this.restructureForSubscriptionVersion = restructureForSubscriptionVersion;
        this.purchaseOrderNumber = purchaseOrderNumber;
        this.purchaseOrderRequiredForInvoicing = purchaseOrderRequiredForInvoicing;
        this.autoRenew = autoRenew;
        this.approvalSegmentId = approvalSegmentId;
        this.customFields = customFields == null ? new CustomField(new HashMap<>()) : customFields;
        this.compositeOrderId = compositeOrderId;
        this.shouldRegeneratePdf = shouldRegeneratePdf;
        this.expiresOn = expiresOn;
        this.submittedBy = submittedBy;
        this.source = source;
        this.startDateType = startDateType;
        this.creditableAmounts = creditableAmounts;
        this.orderProcessingMetadata = orderProcessingMetadata;
        this.subscriptionDurationModel = subscriptionDurationModel;
        this.opportunity = opportunity;
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public Optional<String> getResellerAccountId() {
        return StringUtils.isNotBlank(resellerAccountId) ? Optional.of(resellerAccountId) : Optional.empty();
    }

    public void setResellerAccountId(String resellerAccountId) {
        this.resellerAccountId = resellerAccountId;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public Currency getCurrency() {
        return currency;
    }

    public void setCurrency(Currency currency) {
        this.currency = currency;
    }

    public PaymentTerm getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(PaymentTerm paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public UUID getSubscriptionUuid() {
        return subscriptionUuid;
    }

    public void setSubscriptionUuid(UUID subscriptionUuid) {
        this.subscriptionUuid = subscriptionUuid;
    }

    public String getExternalSubscriptionId() {
        return externalSubscriptionId;
    }

    public void setExternalSubscriptionId(String externalSubscriptionId) {
        this.externalSubscriptionId = externalSubscriptionId;
    }

    public int getSubscriptionTargetVersion() {
        return subscriptionTargetVersion;
    }

    public void setSubscriptionTargetVersion(int subscriptionTargetVersion) {
        this.subscriptionTargetVersion = subscriptionTargetVersion;
    }

    public String getShippingContactId() {
        return shippingContactId;
    }

    public Optional<String> getShippingContactIdOptional() {
        return StringUtils.isNotBlank(shippingContactId) ? Optional.of(shippingContactId) : Optional.empty();
    }

    public void setShippingContactId(String shippingContactId) {
        this.shippingContactId = shippingContactId;
    }

    public String getBillingContactId() {
        return billingContactId;
    }

    public Optional<String> getBillingContactIdOptional() {
        return StringUtils.isNotBlank(billingContactId) ? Optional.of(billingContactId) : Optional.empty();
    }

    public void setBillingContactId(String billingContactId) {
        this.billingContactId = billingContactId;
    }

    public List<TenantDiscount> getPredefinedDiscounts() {
        return predefinedDiscounts;
    }

    public void setPredefinedDiscounts(List<TenantDiscount> predefinedDiscounts) {
        this.predefinedDiscounts = predefinedDiscounts;
    }

    public List<OrderLineItem> getLineItems() {
        if (lineItems == null) {
            if (getOrderId() != null) {
                LOGGER.debug("Order {} has no line items.", getOrderId());
            }
            return List.of();
        }
        return lineItems;
    }

    public void setLineItems(List<OrderLineItem> lineItems) {
        this.lineItems = lineItems;
    }

    public void addLineItem(OrderLineItem orderLineItem) {
        Validator.checkNonNullInternal(orderLineItem, "order line item cannot be null here");

        if (lineItems == null) {
            lineItems = new ArrayList<>();
        }

        lineItems.add(orderLineItem);
    }

    public List<OrderLineItem> getLineItemsNetEffect() {
        return lineItemsNetEffect;
    }

    public void setLineItemsNetEffect(List<OrderLineItem> lineItemsNetEffect) {
        this.lineItemsNetEffect = lineItemsNetEffect;
    }

    public List<OrderLineItem> getMissingRenewalOrderLineItems() {
        return missingRenewalOrderLineItems;
    }

    public void setMissingRenewalOrderLineItems(List<OrderLineItem> missingRenewalOrderLineItems) {
        this.missingRenewalOrderLineItems = missingRenewalOrderLineItems;
    }

    public Instant getStartDate() {
        return startDate;
    }

    public void setStartDate(Instant startDate) {
        this.startDate = startDate;
    }

    public Instant getEndDate() {
        return endDate;
    }

    public void setEndDate(Instant endDate) {
        this.endDate = endDate;
    }

    public Instant getBillingAnchorDate() {
        return billingAnchorDate;
    }

    public void setBillingAnchorDate(Instant billingAnchorDate) {
        this.billingAnchorDate = billingAnchorDate;
    }

    public Recurrence getTermLength() {
        return termLength;
    }

    public void setTermLength(Recurrence termLength) {
        this.termLength = termLength;
    }

    public Recurrence getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(Recurrence billingCycle) {
        this.billingCycle = billingCycle;
    }

    public BillingTerm getBillingTerm() {
        return billingTerm;
    }

    public void setBillingTerm(BillingTerm billingTerm) {
        this.billingTerm = billingTerm;
    }

    public BigDecimal getTotalListAmount() {
        return totalListAmount;
    }

    public void setTotalListAmount(BigDecimal totalListAmount) {
        this.totalListAmount = totalListAmount;
    }

    public BigDecimal getTaxEstimate() {
        return taxEstimate;
    }

    public void setTaxEstimate(BigDecimal taxEstimate) {
        this.taxEstimate = taxEstimate;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public OrderStatus getStatus() {
        return status;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Instant createdOn) {
        this.createdOn = createdOn;
    }

    public Instant getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Instant updatedOn) {
        this.updatedOn = updatedOn;
    }

    public Instant getExecutedOn() {
        return executedOn;
    }

    public void setExecutedOn(Instant executedOn) {
        this.executedOn = executedOn;
    }

    public String getExecutedOnFormatted() {
        return executedOnFormatted;
    }

    public void setExecutedOnFormatted(String executedOnFormatted) {
        this.executedOnFormatted = executedOnFormatted;
    }

    public List<Instant> getRampInterval() {
        return rampInterval;
    }

    public void setRampInterval(List<Instant> rampInterval) {
        this.rampInterval = rampInterval;
    }

    public List<String> getOrderFormTemplateIds() {
        List<String> orderFormIds = Objects.requireNonNullElseGet(orderFormTemplateIds, List::of);
        return orderFormIds.stream().filter(StringUtils::isNotBlank).toList();
    }

    public void setOrderFormTemplateIds(List<String> orderFormTemplateIds) {
        this.orderFormTemplateIds = orderFormTemplateIds;
    }

    public List<OrderTerms> getOrderTerms() {
        return Objects.requireNonNullElseGet(orderTerms, List::of);
    }

    public void setOrderTerms(List<OrderTerms> orderTerms) {
        this.orderTerms = orderTerms;
    }

    public String getRenewalForSubscriptionId() {
        return renewalForSubscriptionId;
    }

    public void setRenewalForSubscriptionId(String renewalForSubscriptionId) {
        this.renewalForSubscriptionId = renewalForSubscriptionId;
    }

    public int getRenewalForSubscriptionVersion() {
        return renewalForSubscriptionVersion;
    }

    public void setRenewalForSubscriptionVersion(int renewalForSubscriptionVersion) {
        this.renewalForSubscriptionVersion = renewalForSubscriptionVersion;
    }

    public String getRestructureForSubscriptionId() {
        return restructureForSubscriptionId;
    }

    public void setRestructureForSubscriptionId(String restructureForSubscriptionId) {
        this.restructureForSubscriptionId = restructureForSubscriptionId;
    }

    public int getRestructureForSubscriptionVersion() {
        return restructureForSubscriptionVersion;
    }

    public void setRestructureForSubscriptionVersion(int restructureForSubscriptionVersion) {
        this.restructureForSubscriptionVersion = restructureForSubscriptionVersion;
    }

    public UUID getDocumentMasterTemplateId() {
        return documentMasterTemplateId;
    }

    public void setDocumentMasterTemplateId(UUID documentMasterTemplateId) {
        this.documentMasterTemplateId = documentMasterTemplateId;
    }

    public DocumentCustomContent getDocumentCustomContent() {
        return documentCustomContent;
    }

    public void setDocumentCustomContent(DocumentCustomContent documentCustomContent) {
        this.documentCustomContent = documentCustomContent;
    }

    public StoredMetrics getMetrics() {
        return metrics;
    }

    public void setMetrics(StoredMetrics metrics) {
        this.metrics = metrics;
    }

    public String getPurchaseOrderNumber() {
        return purchaseOrderNumber;
    }

    public void setPurchaseOrderNumber(String purchaseOrderNumber) {
        this.purchaseOrderNumber = purchaseOrderNumber;
    }

    public boolean getPurchaseOrderRequiredForInvoicing() {
        return BooleanUtils.isTrue(purchaseOrderRequiredForInvoicing);
    }

    public void setPurchaseOrderRequiredForInvoicing(Boolean purchaseOrderRequiredForInvoicing) {
        this.purchaseOrderRequiredForInvoicing = purchaseOrderRequiredForInvoicing;
    }

    public List<OrderLineItem> getLineItemsHavingBaseSubscriptionCharge() {
        if (CollectionUtils.isEmpty(lineItems)) {
            return List.of();
        }

        return lineItems
            .stream()
            .filter(lineItem -> StringUtils.isNotBlank(lineItem.getBaseExternalSubscriptionChargeId()))
            .collect(Collectors.toList());
    }

    public List<OrderLineItem> getLineItemsWithEmptyBaseSubscriptionCharge() {
        if (CollectionUtils.isEmpty(lineItems)) {
            return new ArrayList<>();
        }

        return lineItems
            .stream()
            .filter(lineItem -> StringUtils.isBlank(lineItem.getBaseExternalSubscriptionChargeId()))
            .collect(Collectors.toList());
    }

    public String getSfdcOpportunityId() {
        return sfdcOpportunityId;
    }

    public void setSfdcOpportunityId(String sfdcOpportunityId) {
        this.sfdcOpportunityId = StringUtils.isNotBlank(sfdcOpportunityId) ? sfdcOpportunityId : null;
    }

    public OpportunityCrmType getOpportunityCrmType() {
        return opportunityCrmType;
    }

    public void setOpportunityCrmType(OpportunityCrmType opportunityCrmType) {
        this.opportunityCrmType = opportunityCrmType;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public boolean getIsPrimaryOrderForSfdcOpportunity() {
        return BooleanUtils.isTrue(isPrimaryOrderForSfdcOpportunity);
    }

    public void setIsPrimaryOrderForSfdcOpportunity(Boolean primaryOrderForSfdcOpportunity) {
        isPrimaryOrderForSfdcOpportunity = primaryOrderForSfdcOpportunity;
    }

    public String getSfdcOpportunityName() {
        return sfdcOpportunityName;
    }

    public void setSfdcOpportunityName(String sfdcOpportunityName) {
        this.sfdcOpportunityName = sfdcOpportunityName;
    }

    public String getSfdcOpportunityType() {
        return sfdcOpportunityType;
    }

    public void setSfdcOpportunityType(String sfdcOpportunityType) {
        this.sfdcOpportunityType = sfdcOpportunityType;
    }

    public String getSfdcOpportunityStage() {
        return sfdcOpportunityStage;
    }

    public void setSfdcOpportunityStage(String sfdcOpportunityStage) {
        this.sfdcOpportunityStage = sfdcOpportunityStage;
    }

    public boolean getSfdcOrderCanBeExecuted() {
        return BooleanUtils.isNotFalse(sfdcOrderCanBeExecuted);
    }

    public void setSfdcOrderCanBeExecuted(Boolean sfdcOrderCanBeExecuted) {
        this.sfdcOrderCanBeExecuted = sfdcOrderCanBeExecuted;
    }

    public boolean getAutoRenew() {
        return autoRenew;
    }

    public void setAutoRenew(boolean autoRenew) {
        this.autoRenew = autoRenew;
    }

    public Optional<OrderLineItem> closestMatchingLineItem(OrderLineItem matchTo) {
        Validator.checkNonNullInternal(matchTo, "order line item cannot be null here");
        if (CollectionUtils.isEmpty(lineItems)) {
            return Optional.empty();
        }

        return lineItems.stream().filter(item -> isBasicMatch(item, matchTo)).findFirst();
    }

    private boolean isBasicMatch(OrderLineItem item, OrderLineItem matchTo) {
        boolean planMatches = item.getPlanId().equals(matchTo.getPlanId());
        boolean chargeMatches = item.getChargeId().equals(matchTo.getChargeId());

        // if removing or updating, don't worry about quantity
        boolean quantityMatches =
            item.getAction() == ActionType.REMOVE || item.getAction() == ActionType.UPDATE || item.getQuantity() == matchTo.getQuantity();
        if (!planMatches || !chargeMatches || !quantityMatches) {
            return false;
        }

        if (matchTo.getEffectiveDate() != null) {
            return matchTo.getEffectiveDate().equals(item.getEffectiveDate());
        }

        if (matchTo.getEndDate() != null) {
            return matchTo.getEndDate().equals(item.getEndDate());
        }

        return true;
    }

    public String getApprovalSegmentId() {
        return approvalSegmentId;
    }

    public void setApprovalSegmentId(String approvalSegmentId) {
        this.approvalSegmentId = approvalSegmentId;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    @Override
    public CustomField getCustomFields() {
        return customFields;
    }

    @Override
    public void setCustomFields(CustomField customFields) {
        this.customFields = customFields == null ? new CustomField(new HashMap<>()) : customFields;
    }

    public BigDecimal getTotalListAmountBeforeOverride() {
        return totalListAmountBeforeOverride;
    }

    public void setTotalListAmountBeforeOverride(BigDecimal totalListAmountBeforeOverride) {
        this.totalListAmountBeforeOverride = totalListAmountBeforeOverride;
    }

    public String getCompositeOrderId() {
        return compositeOrderId;
    }

    public void setCompositeOrderId(String compositeOrderId) {
        this.compositeOrderId = compositeOrderId;
    }

    public Boolean getShouldRegeneratePdf() {
        return shouldRegeneratePdf;
    }

    public void setShouldRegeneratePdf(Boolean shouldRegeneratePdf) {
        this.shouldRegeneratePdf = shouldRegeneratePdf;
    }

    public Instant getExpiresOn() {
        return expiresOn;
    }

    public void setExpiresOn(Instant expiresOn) {
        this.expiresOn = expiresOn;
    }

    public String getSubmittedBy() {
        return submittedBy;
    }

    public void setSubmittedBy(String submittedBy) {
        this.submittedBy = submittedBy;
    }

    public OrderSource getSource() {
        return source == null ? OrderSource.UNKNOWN : source;
    }

    public void setSource(OrderSource source) {
        this.source = source;
    }

    public OrderStartDateType getStartDateType() {
        return startDateType;
    }

    public void setStartDateType(OrderStartDateType startDateType) {
        this.startDateType = startDateType;
    }

    public List<CreditableAmount> getCreditableAmounts() {
        return creditableAmounts;
    }

    public void setCreditableAmounts(List<CreditableAmount> creditableAmounts) {
        this.creditableAmounts = creditableAmounts;
    }

    public Optional<OrderProcessingMetadata> getOrderProcessingMetadata() {
        return orderProcessingMetadata;
    }

    public void setOrderProcessingMetadata(Optional<OrderProcessingMetadata> orderProcessingMetadata) {
        this.orderProcessingMetadata = orderProcessingMetadata;
    }

    public List<String> getCustomBillingEligibleOrderLineIds() {
        return customBillingEligibleOrderLineIds;
    }

    public void setCustomBillingEligibleOrderLineIds(List<String> customBillingEligibleOrderLineIds) {
        this.customBillingEligibleOrderLineIds = customBillingEligibleOrderLineIds;
    }

    public CustomBillingSchedule getCustomBillingSchedule() {
        return customBillingSchedule;
    }

    public void setCustomBillingSchedule(CustomBillingSchedule customBillingSchedule) {
        this.customBillingSchedule = customBillingSchedule;
    }

    public boolean getShouldUseCustomBillingSchedule() {
        if (orderProcessingMetadata.isEmpty()) {
            return false;
        }

        return orderProcessingMetadata.get().shouldUseCustomBillingSchedule();
    }

    public void setShouldUseCustomBillingSchedule(Boolean shouldUseCustomBillingSchedule) {
        orderProcessingMetadata = Optional.of(
            orderProcessingMetadata
                .map(present ->
                    ImmutableOrderProcessingMetadata.builder().from(present).shouldUseCustomBillingSchedule(shouldUseCustomBillingSchedule).build()
                )
                .orElseGet(() -> ImmutableOrderProcessingMetadata.builder().shouldUseCustomBillingSchedule(shouldUseCustomBillingSchedule).build())
        );
    }

    public boolean shouldSkipOrderCreationCustomization() {
        if (orderProcessingMetadata.isEmpty()) {
            return false;
        }

        if (orderProcessingMetadata.get().shouldSkipOrderCreationCustomization().isEmpty()) {
            return false;
        }

        return orderProcessingMetadata.get().shouldSkipOrderCreationCustomization().get();
    }

    public List<CustomPredefinedTemplateOnOrder> getCustomPredefinedTemplatesOnOrder() {
        return customPredefinedTemplatesOnOrder;
    }

    public void setCustomPredefinedTemplatesOnOrder(List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder) {
        this.customPredefinedTemplatesOnOrder = customPredefinedTemplatesOnOrder;
    }

    public SubscriptionDurationModel getSubscriptionDurationModel() {
        return subscriptionDurationModel;
    }

    public void setSubscriptionDurationModel(SubscriptionDurationModel subscriptionDurationModel) {
        this.subscriptionDurationModel = subscriptionDurationModel;
    }

    public Optional<Opportunity> getOpportunity() {
        return Optional.ofNullable(opportunity);
    }

    public void setOpportunity(Opportunity opportunity) {
        this.opportunity = opportunity;
    }

    public Optional<OrderCreationCustomizationOutput> getZeppaOutput() {
        return Optional.ofNullable(zeppaOutput);
    }

    public void setZeppaOutput(OrderCreationCustomizationOutput zeppaOutput) {
        this.zeppaOutput = zeppaOutput;
    }

    @Override
    public String toString() {
        return (
            "Order{" +
            "id=" +
            id +
            ", name='" +
            name +
            "'" +
            ", orderId='" +
            orderId +
            "'" +
            ", tenantId='" +
            tenantId +
            "'" +
            ", entityId='" +
            entityId +
            "'" +
            ", externalId='" +
            externalId +
            "'" +
            ", accountId='" +
            accountId +
            "'" +
            ", resellerAccountId='" +
            resellerAccountId +
            "'" +
            ", orderType=" +
            orderType +
            ", currency=" +
            currency +
            ", paymentTerm=" +
            paymentTerm +
            ", subscriptionUuid=" +
            subscriptionUuid +
            ", externalSubscriptionId='" +
            externalSubscriptionId +
            '\'' +
            ", subscriptionTargetVersion=" +
            subscriptionTargetVersion +
            ", shippingContactId='" +
            shippingContactId +
            '\'' +
            ", billingContactId='" +
            billingContactId +
            '\'' +
            ", predefinedDiscounts=" +
            predefinedDiscounts +
            ", lineItems=" +
            lineItems +
            ", lineItemsNetEffect=" +
            lineItemsNetEffect +
            ", missingRenewalOrderLineItems=" +
            missingRenewalOrderLineItems +
            ", startDate=" +
            startDate +
            ", endDate=" +
            endDate +
            ", billingAnchorDate=" +
            billingAnchorDate +
            ", termLength=" +
            termLength +
            ", billingCycle=" +
            billingCycle +
            ", billingTerm=" +
            billingTerm +
            ", totalListAmount=" +
            totalListAmount +
            ", totalListAmountBeforeOverride=" +
            totalListAmountBeforeOverride +
            ", taxEstimate=" +
            taxEstimate +
            ", totalAmount=" +
            totalAmount +
            ", status=" +
            status +
            ", renewalForSubscriptionId='" +
            renewalForSubscriptionId +
            '\'' +
            ", renewalForSubscriptionVersion=" +
            renewalForSubscriptionVersion +
            ", restructureForSubscriptionId='" +
            restructureForSubscriptionId +
            '\'' +
            ", restructureForSubscriptionVersion=" +
            restructureForSubscriptionVersion +
            ", createdBy='" +
            createdBy +
            '\'' +
            ", createdOn=" +
            createdOn +
            ", updatedOn=" +
            updatedOn +
            ", executedOn=" +
            executedOn +
            ", executedOnFormatted='" +
            executedOnFormatted +
            '\'' +
            ", rampInterval=" +
            rampInterval +
            ", orderFormTemplateIds=" +
            orderFormTemplateIds +
            ", orderTerms=" +
            orderTerms +
            ", sfdcOpportunityId='" +
            sfdcOpportunityId +
            '\'' +
            ", isPrimaryOrderForSfdcOpportunity=" +
            isPrimaryOrderForSfdcOpportunity +
            ", sfdcOpportunityName='" +
            sfdcOpportunityName +
            '\'' +
            ", sfdcOpportunityType='" +
            sfdcOpportunityType +
            '\'' +
            ", sfdcOpportunityStage='" +
            sfdcOpportunityStage +
            '\'' +
            ", sfdcOrderCanBeExecuted=" +
            sfdcOrderCanBeExecuted +
            ", opportunityCrmType=" +
            opportunityCrmType +
            ", ownerId='" +
            ownerId +
            '\'' +
            ", documentMasterTemplateId=" +
            documentMasterTemplateId +
            ", documentCustomContent=" +
            documentCustomContent +
            ", metrics=" +
            metrics +
            ", purchaseOrderNumber='" +
            purchaseOrderNumber +
            '\'' +
            ", purchaseOrderRequiredForInvoicing=" +
            purchaseOrderRequiredForInvoicing +
            ", autoRenew=" +
            autoRenew +
            ", approvalSegmentId='" +
            approvalSegmentId +
            '\'' +
            ", attachmentId='" +
            attachmentId +
            '\'' +
            ", customFields=" +
            customFields +
            ", compositeOrderId='" +
            compositeOrderId +
            '\'' +
            ", shouldRegeneratePdf=" +
            shouldRegeneratePdf +
            ", expiresOn=" +
            expiresOn +
            ", submittedBy='" +
            submittedBy +
            '\'' +
            ", source=" +
            source +
            ", startDateType=" +
            startDateType +
            ", creditableAmounts=" +
            creditableAmounts +
            ", orderProcessingMetadata=" +
            orderProcessingMetadata +
            ", subscriptionDurationModel=" +
            subscriptionDurationModel +
            ", opportunityCrmId=" +
            opportunity.getCrmId() +
            '}'
        );
    }
}
