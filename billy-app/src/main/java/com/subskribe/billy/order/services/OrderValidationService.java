package com.subskribe.billy.order.services;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.enums.OrderStatus;
import java.util.Objects;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;

public class OrderValidationService {

    private final AccountGetService accountGetService;
    private final TaxService taxService;

    @Inject
    public OrderValidationService(AccountGetService accountGetService, TaxService taxService) {
        this.accountGetService = accountGetService;
        this.taxService = taxService;
    }

    void validateContactsOnOrder(Order order) {
        // validate that if contacts are present, they are valid and belong to the correct account
        Optional<AccountContact> shippingContact = order.getShippingContactIdOptional().map(accountGetService::getContact);
        shippingContact.ifPresent(contact -> validateShippingContactMatchesAccount(order, contact));

        Optional<AccountContact> billingContact = order.getBillingContactIdOptional().map(accountGetService::getContact);
        billingContact.ifPresent(contact -> validateBillingContactMatchesAccount(order, contact));

        if (order.getStatus() == OrderStatus.DRAFT || order.getStatus() == OrderStatus.EXPIRED) {
            // if the order is in DRAFT or EXPIRED statues, contacts aren't required
            return;
        }

        if (shippingContact.isEmpty() || billingContact.isEmpty()) {
            // if order is in any other status, shipping and billing contacts are required
            throw new InvalidInputException("Shipping and/or billing contacts cannot be empty");
        }

        if (order.getStatus() != OrderStatus.EXECUTED) {
            return;
        }

        // skip the validation of the address before the order can be executed
        if (shippingContact.get().getAddress() == null) {
            throw new InvalidInputException(String.format("Shipping contact %s must have an address", shippingContact.get().getContactId()));
        }
        taxService.validateAddress(shippingContact.get().getAddress());

        if (billingContact.get().getAddress() == null) {
            throw new InvalidInputException(String.format("Billing contact %s must have an address", billingContact.get().getContactId()));
        }
    }

    private void validateShippingContactMatchesAccount(Order order, AccountContact shippingContact) {
        if (!shippingContact.getAccountId().equals(order.getAccountId())) {
            throw new InvalidInputException(
                String.format(
                    "Shipping contact %s is part of account %s, which is different from the order account %s",
                    shippingContact.getContactId(),
                    shippingContact.getAccountId(),
                    order.getAccountId()
                )
            );
        }
    }

    // this is backwards compatible with empty reseller account id
    private void validateBillingContactMatchesAccount(Order order, AccountContact billingContact) {
        if (billingContact.getAccountId().equals(order.getAccountId())) {
            if (order.getResellerAccountId().isPresent()) {
                throw new InvalidInputException(
                    String.format(
                        "Billing contact %s is from account %s which does not match reseller account %s",
                        billingContact.getContactId(),
                        billingContact.getAccountId(),
                        order.getResellerAccountId().get()
                    )
                );
            }
            return;
        }
        // ideally we don't set this here, but it's currently the best place to set it
        if (order.getResellerAccountId().isEmpty()) {
            order.setResellerAccountId(billingContact.getAccountId());
        }
        String resellerAccountId = order.getResellerAccountId().get();
        if (!billingContact.getAccountId().equals(resellerAccountId)) {
            throw new InvalidInputException(
                String.format(
                    "Billing contact %s is not from the same account as the reseller account %s",
                    billingContact.getContactId(),
                    resellerAccountId
                )
            );
        }
        Account resellerAccount = accountGetService.getAccount(billingContact.getAccountId());
        if (!resellerAccount.getIsReseller()) {
            throw new InvalidInputException(
                String.format("Billing contact %s account %s is not a reseller", billingContact.getContactId(), resellerAccount.getAccountId())
            );
        }
    }

    Account validateAccountDetails(Order order) {
        if (order.getAccountId() == null) {
            throw new IllegalArgumentException("account id is required to create an order");
        }
        var account = accountGetService.getAccount(order.getAccountId());

        if (!canOrderContactsBeNullable(order) && isAnyOrderContactBlank(order)) {
            throw new IllegalArgumentException("shipping contact id and billing contact id are required");
        }

        order
            .getShippingContactIdOptional()
            .map(accountGetService::getContact)
            .ifPresent(contact -> validateShippingContactMatchesAccount(order, contact));
        order
            .getBillingContactIdOptional()
            .map(accountGetService::getContact)
            .ifPresent(contact -> validateBillingContactMatchesAccount(order, contact));

        /*
         * order currency is not set, set the order currency to the account currency
         * order currency is already set, do not touch it
         */
        if (Objects.isNull(order.getCurrency())) {
            order.setCurrency(account.getCurrency());
        }
        return account;
    }

    boolean canOrderContactsBeNullable(Order order) {
        return (
            order.getStatus() == null ||
            order.getStatus() == OrderStatus.DRAFT ||
            order.getStatus() == OrderStatus.EXPIRED ||
            order.getStatus() == OrderStatus.SUBMITTED ||
            order.getStatus() == OrderStatus.APPROVED
        );
    }

    boolean isAnyOrderContactBlank(Order order) {
        return StringUtils.isBlank(order.getBillingContactId()) || StringUtils.isBlank(order.getShippingContactId());
    }
}
