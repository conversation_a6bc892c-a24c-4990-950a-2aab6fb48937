package com.subskribe.billy.order.services;

import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.model.DocumentMasterTemplateStatus;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.model.DocumentTemplateStatus;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.OrderTermsService;
import com.subskribe.billy.template.services.PlanTermsService;
import com.subskribe.billy.validation.Validator;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.jooq.DSLContext;

public class OrderDocumentTemplateService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderDocumentTemplateService.class);

    private final DocumentTemplateGetService documentTemplateGetService;
    private final OrderTermsService orderTermsService;
    private final PlanTermsService planTermsService;
    private final ProductCatalogGetService productCatalogGetService;
    private final OrderPdfGenerationTrackerService orderPdfGenerationTrackerService;

    @Inject
    public OrderDocumentTemplateService(
        DocumentTemplateGetService documentTemplateGetService,
        OrderTermsService orderTermsService,
        PlanTermsService planTermsService,
        ProductCatalogGetService productCatalogGetService,
        OrderPdfGenerationTrackerService orderPdfGenerationTrackerService
    ) {
        this.documentTemplateGetService = documentTemplateGetService;
        this.orderTermsService = orderTermsService;
        this.planTermsService = planTermsService;
        this.productCatalogGetService = productCatalogGetService;
        this.orderPdfGenerationTrackerService = orderPdfGenerationTrackerService;
    }

    public void validateDocumentMasterTemplate(Order order) {
        if (order.getDocumentMasterTemplateId() == null) {
            return;
        }

        DocumentMasterTemplate documentMasterTemplate = documentTemplateGetService
            .getDocumentMasterTemplateById(order.getDocumentMasterTemplateId().toString(), Optional.empty())
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_MASTER_TEMPLATE, order.getDocumentMasterTemplateId().toString()));
        if (documentMasterTemplate.getStatus() != DocumentMasterTemplateStatus.ACTIVE) {
            throw new IllegalArgumentException("Document template must be in active state");
        }
    }

    public void validateOrderTemplate(Order order) {
        List<String> orderFormTemplateIds = order.getOrderFormTemplateIds();

        // if template is not attached, skip
        if (CollectionUtils.isEmpty(orderFormTemplateIds)) {
            return;
        }

        List<DocumentTemplate> documentTemplates = documentTemplateGetService.getDocumentTemplatesByTemplateIds(orderFormTemplateIds);
        Map<String, DocumentTemplate> documentTemplateMap = documentTemplates
            .stream()
            .collect(Collectors.toMap(DocumentTemplate::getTemplateId, Function.identity()));
        for (String orderFormTemplateId : orderFormTemplateIds) {
            try {
                var documentTemplate = documentTemplateMap.get(orderFormTemplateId);
                if (documentTemplate == null) {
                    throw new ObjectNotFoundException(BillyObjectType.DOCUMENT_TEMPLATE, orderFormTemplateId);
                }

                if (documentTemplate.getType() != DocumentTemplateType.ORDER) {
                    LOGGER.warn("Wrong predefined terms type: {}", documentTemplate.getType().toString());
                    throw new IllegalArgumentException("template is not of the correct type");
                }

                if (documentTemplate.getStatus() == DocumentTemplateStatus.DEPRECATED) {
                    throw new IllegalArgumentException("Predefined terms in deprecated state cannot be used: " + documentTemplate.getName());
                }
            } catch (ObjectNotFoundException ex) {
                String message = String.format("order form with id %s does not exist", orderFormTemplateId);
                LOGGER.warn(message);
                throw new IllegalArgumentException(message);
            }
        }
    }

    public void findAndAddMissingPlanTemplates(Order order) {
        Set<String> missingPlanTemplateIds = findMissingPlanTemplateIds(order);
        if (CollectionUtils.isEmpty(missingPlanTemplateIds)) {
            return;
        }

        List<String> orderTemplateIds = new ArrayList<>(order.getOrderFormTemplateIds());
        orderTemplateIds.addAll(missingPlanTemplateIds);

        order.setOrderFormTemplateIds(orderTemplateIds);
    }

    private Set<String> findMissingPlanTemplateIds(Order order) {
        List<String> planIds = getPlanIdsFromOrderForDocumentTemplates(order);
        if (CollectionUtils.isEmpty(planIds)) {
            return Set.of();
        }

        Map<String, List<String>> planTemplatesMap = planTermsService.getDocumentTemplateIdsAssociatedWithPlans(planIds);
        Set<String> planTemplateIds = planTemplatesMap.values().stream().flatMap(List::stream).collect(Collectors.toSet());

        List<String> orderFormTemplateIds = Optional.ofNullable(order.getOrderFormTemplateIds()).orElse(List.of());
        orderFormTemplateIds.forEach(planTemplateIds::remove);
        return planTemplateIds;
    }

    public void consolidatePlanTemplates(Order order) {
        List<String> orderFormTemplateIds = new ArrayList<>(order.getOrderFormTemplateIds());

        List<String> planIds = getPlanIdsFromOrderForDocumentTemplates(order);
        if (CollectionUtils.isEmpty(planIds)) {
            return;
        }
        Map<String, List<String>> planTemplatesMap = planTermsService.getDocumentTemplateIdsAssociatedWithPlans(planIds);
        Set<String> planTemplateIds = planTemplatesMap.values().stream().flatMap(List::stream).collect(Collectors.toSet());
        Set<String> missingTemplateIds = SetUtils.difference(planTemplateIds, new HashSet<>(orderFormTemplateIds));
        orderFormTemplateIds.addAll(missingTemplateIds);

        order.setOrderFormTemplateIds(orderFormTemplateIds);
    }

    private static List<String> getPlanIdsFromOrderForDocumentTemplates(Order order) {
        return order
            .getLineItems()
            .stream()
            .filter(orderLineItem -> orderLineItem.getAction() != ActionType.NONE && orderLineItem.getAction() != ActionType.REMOVE)
            .map(OrderLineItem::getPlanId)
            .collect(Collectors.toList());
    }

    public void filterOutPredefinedTermsBroughtByPlans(Order order, List<Plan> plans) {
        if (CollectionUtils.isEmpty(order.getOrderFormTemplateIds())) {
            return;
        }

        Set<String> templateIdsBroughtByPlans = plans
            .stream()
            .filter(plan -> CollectionUtils.isNotEmpty(plan.getTemplateIds()))
            .flatMap(list -> list.getTemplateIds().stream())
            .collect(Collectors.toSet());

        List<DocumentTemplate> documentTemplatesOnOrder = documentTemplateGetService.getDocumentTemplatesByTemplateIds(
            order.getOrderFormTemplateIds()
        );

        List<DocumentTemplate> nonUserSelectableTemplatesOnOrder = documentTemplatesOnOrder
            .stream()
            .filter(term -> !term.getIsUserSelectable())
            .toList();
        if (CollectionUtils.isEmpty(nonUserSelectableTemplatesOnOrder)) {
            return;
        }

        // remove any non-user selectables templates from the order if they are not in plans
        Set<String> templatesToBeRemoved = nonUserSelectableTemplatesOnOrder
            .stream()
            .map(DocumentTemplate::getTemplateId)
            .filter(id -> !templateIdsBroughtByPlans.contains(id))
            .collect(Collectors.toSet());

        List<String> filteredTemplateIds = order
            .getOrderFormTemplateIds()
            .stream()
            .filter(templateId -> !templatesToBeRemoved.contains(templateId))
            .collect(Collectors.toList());
        order.setOrderFormTemplateIds(filteredTemplateIds);
    }

    public void addPredefinedTermsForOrder(
        Order order,
        DSLContext dslContext,
        String tenantId,
        List<DocumentTemplate> orderTemplates,
        Boolean isUpdate
    ) {
        Map<String, Set<String>> templateIdToPlanIdsMap = getTemplateIdToPlanIdsMapFromOrderLines(order);
        if (isUpdate) {
            orderTermsService.updateOrderTermsForOrder(dslContext, tenantId, orderTemplates, order.getOrderId(), templateIdToPlanIdsMap);
        } else {
            orderTermsService.addOrderTermsForOrder(dslContext, tenantId, orderTemplates, order.getOrderId(), templateIdToPlanIdsMap);
        }
    }

    private Map<String, Set<String>> getTemplateIdToPlanIdsMapFromOrderLines(Order order) {
        List<String> planIds = order.getLineItems().stream().map(OrderLineItem::getPlanId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planIds)) {
            return Map.of();
        }
        List<Plan> plans = productCatalogGetService.getPlansByPlanIds(planIds);

        Map<String, Set<String>> templateIdToPlanIdsMap = new HashMap<>();
        for (Plan plan : plans) {
            plan
                .getTemplateIds()
                .forEach(templateId -> {
                    if (!templateIdToPlanIdsMap.containsKey(templateId)) {
                        templateIdToPlanIdsMap.put(templateId, new HashSet<>());
                    }
                    Set<String> value = templateIdToPlanIdsMap.get(templateId);
                    value.add(plan.getPlanId());
                });
        }

        return templateIdToPlanIdsMap;
    }

    // Carry over user-selectable predefined terms from the order to the renewal order.
    // Any terms which are not included here will be added to the order during order validation/creation
    public void carryOverReplacedPlanTermsToRenewal(Order order, List<String> replacedPlanIds) {
        Validator.checkNonNullInternal(replacedPlanIds, "replacedPlanIds");
        Validator.checkStateInternal(CollectionUtils.isEmpty(order.getOrderFormTemplateIds()), "order.orderFormTemplateIds"); // should not be populated yet

        Map<String, List<String>> planToTermIdsMap = planTermsService.getDocumentTemplateIdsAssociatedWithPlans(replacedPlanIds);
        Set<String> termIdsFromRemovedPlans = planToTermIdsMap.values().stream().flatMap(List::stream).collect(Collectors.toUnmodifiableSet());
        List<String> documentTemplateIdsToKeep = documentTemplateGetService
            .getDocumentTemplatesByTemplateIds(termIdsFromRemovedPlans)
            .stream()
            .filter(DocumentTemplate::getIsUserSelectable)
            .map(DocumentTemplate::getTemplateId)
            .distinct()
            .toList();
        order.setOrderFormTemplateIds(documentTemplateIdsToKeep);
    }

    public void updatePredefinedTermsToLatestForOrder(String orderId, String templateId) {
        Validator.validateStringNotBlank(orderId, "orderId cannot be blank");
        Validator.validateStringNotBlank(templateId, "templateId cannot be blank");
        DocumentTemplate latestTemplate = documentTemplateGetService.getDocumentTemplateByTemplateId(templateId);
        orderTermsService.updateOrderTermsVersion(orderId, templateId, latestTemplate.getVersion());
        orderPdfGenerationTrackerService.setShouldRegeneratePdf(orderId, true);
    }
}
