package com.subskribe.billy.order.document;

import static com.subskribe.billy.shared.document.TemplateCalculations.MONTHS_IN_YEAR;
import static com.subskribe.billy.shared.enums.Cycle.MONTH;
import static com.subskribe.billy.shared.enums.Cycle.YEAR;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountLineItemDetail;
import com.subskribe.billy.invoice.service.DiscountCalculator;
import com.subskribe.billy.productcatalog.model.PriceTier;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.resources.json.shared.DiscountDetailJson;
import com.subskribe.billy.shared.document.TemplateCalculations;
import com.subskribe.billy.shared.document.TemplateCharge;
import com.subskribe.billy.shared.document.TemplateConstants;
import com.subskribe.billy.shared.document.TemplateMathFunctions;
import com.subskribe.billy.shared.document.TemplatePriceTier;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class OrderTemplateLineItem {

    private static final String QUANTITY_ZERO = "0";
    private static final String QUANTITY_ONE = "1";
    private static final String QUANTITY_RANGE_FORMAT = "%s - %s";

    private final OrderLineItemDetail orderLineItemDetail;
    private final ProductJson productJson;
    private OrderTemplatePlan orderTemplatePlan;
    private final TemplateCharge templateCharge;
    private final BigDecimal entryArr;
    private final DocumentRenderFormatter formatter;
    private final String currencyCode;
    private final PlanMapper planMapper;
    private final CustomField customField;
    private final List<PriceAttributeData> priceAttributes;
    private final TimeZone timeZone;

    private BigDecimal customListUnitPrice;
    private BigDecimal adjustedQuantity;
    private BigDecimal adjustedAmount;
    private BigDecimal adjustedListAmount;
    private BigDecimal adjustedYearlyAmount;

    private List<OrderTemplateLineItem> rawLineItems;

    public OrderTemplateLineItem(
        OrderLineItemDetail orderLineItemDetail,
        ProductJson productJson,
        OrderTemplatePlan orderTemplatePlan,
        TemplateCharge templateCharge,
        BigDecimal entryArr,
        CustomField customField,
        DocumentRenderFormatter formatter,
        String currencyCode,
        List<PriceAttributeData> priceAttributes,
        TimeZone timeZone
    ) {
        this.orderLineItemDetail = orderLineItemDetail;
        this.productJson = productJson;
        this.orderTemplatePlan = orderTemplatePlan;
        this.templateCharge = templateCharge;
        this.entryArr = entryArr;
        this.customField = customField == null ? new CustomField(new HashMap<>()) : customField;
        this.formatter = formatter;
        this.currencyCode = currencyCode;
        this.priceAttributes = priceAttributes;
        this.timeZone = timeZone;
        rawLineItems = new ArrayList<>();
        planMapper = Mappers.getMapper(PlanMapper.class);
    }

    public void setOrderTemplatePlan(OrderTemplatePlan orderTemplatePlan) {
        this.orderTemplatePlan = orderTemplatePlan;
    }

    public OrderLineItemDetail getOrderLineItemDetail() {
        return orderLineItemDetail;
    }

    // conditional logic based on plan name {{#hasPlanName.<plan name>}} ... {{/hasPlanName.<plan name>}}
    public Map<String, Boolean> hasPlanName() {
        return Map.of(getPlanName(), true);
    }

    // conditional logic based on plan id {{#hasPlanId.<plan id>}} ... {{/hasPlanId.<plan id>}}
    public Map<String, Boolean> hasPlanId() {
        return Map.of(orderLineItemDetail.getPlan().getId(), true);
    }

    public String getProductCategoryId() {
        if (productJson.getProductCategoryId() == null) {
            // return empty string for sorting purposes
            return StringUtils.EMPTY;
        }
        return productJson.getProductCategoryId();
    }

    public String getProductCategoryName() {
        if (productJson.getProductCategory() == null) {
            return StringUtils.EMPTY;
        }
        return productJson.getProductCategory().getName();
    }

    // Function to set a custom list unit price that may be different from the charge list unit price
    public Function<String, Void> setCustomListUnitPrice() {
        return input -> {
            customListUnitPrice = TemplateMathFunctions.parseNumber(input);
            return null;
        };
    }

    public void setAdjustedQuantity(BigDecimal adjustedQuantity) {
        this.adjustedQuantity = adjustedQuantity;
    }

    public void setAdjustedAmount(BigDecimal adjustedAmount) {
        this.adjustedAmount = adjustedAmount;
    }

    public void setAdjustedListAmount(BigDecimal adjustedListAmount) {
        this.adjustedListAmount = adjustedListAmount;
    }

    public void setAdjustedYearlyAmount(BigDecimal adjustedYearlyAmount) {
        this.adjustedYearlyAmount = adjustedYearlyAmount;
    }

    // return custom discount unit price. If custom list unit price is set, use that, otherwise use line item list unit price
    public String getCustomUnitDiscountAmount() {
        BigDecimal listUnitPrice = customListUnitPrice == null ? orderLineItemDetail.getListUnitPrice() : customListUnitPrice;
        return listUnitPrice.subtract(orderLineItemDetail.getSellUnitPrice()).toPlainString();
    }

    // returns the discount amount based on custom list unit price. If not set, return the line item total discount amount
    // todo: Is there a better way to handle this scenario?
    public String getCustomDiscountAmount() {
        BigDecimal orderLineListUnitPrice = orderLineItemDetail.getListUnitPrice();
        if (customListUnitPrice == null || customListUnitPrice.compareTo(orderLineListUnitPrice) <= 0) {
            // if no custom list unit price is set or custom list unit price is less than or equal to order line list unit price, return standard discount amount
            return orderLineItemDetail.getDiscountAmount().stripTrailingZeros().toPlainString();
        }

        if (Numbers.isZero(orderLineListUnitPrice)) {
            // if order line list unit price is zero return 0
            return "0";
        }

        // calculate the additional discount amount based on custom list unit price
        // since proration can be involved, revert calculate based on the ratio of custom list price vs charge list price
        // and compare against list amount to determine the total based on custom list unit price
        BigDecimal customListTotal = Numbers.scaledDivide(orderLineItemDetail.getListAmount().multiply(customListUnitPrice), orderLineListUnitPrice);

        return customListTotal.subtract(orderLineItemDetail.getAmount()).stripTrailingZeros().toPlainString();
    }

    public ProductJson getProduct() {
        return productJson;
    }

    public String getProductId() {
        return productJson.getId();
    }

    public OrderTemplatePlan getPlan() {
        return orderTemplatePlan;
    }

    public String getPlanId() {
        return orderLineItemDetail.getPlan().getId();
    }

    public String getPlanName() {
        return orderLineItemDetail.getPlan().getName();
    }

    public String getPlanDisplayName() {
        return StringUtils.isBlank(orderLineItemDetail.getPlan().getDisplayName())
            ? orderLineItemDetail.getPlan().getName()
            : orderLineItemDetail.getPlan().getDisplayName();
    }

    public String getPlanDescription() {
        return orderLineItemDetail.getPlan().getDescription();
    }

    public TemplateCharge getCharge() {
        return templateCharge;
    }

    public String getChargeName() {
        return orderLineItemDetail.getCharge().getName();
    }

    public String getChargeDisplayName() {
        return StringUtils.isBlank(orderLineItemDetail.getCharge().getDisplayName())
            ? orderLineItemDetail.getCharge().getName()
            : orderLineItemDetail.getCharge().getDisplayName();
    }

    public String getChargeDescription() {
        return orderLineItemDetail.getCharge().getDescription();
    }

    public String getQuantity() {
        if (adjustedQuantity != null) {
            return formatter.formatPercent(adjustedQuantity);
        }

        return formatter.numberFormat(orderLineItemDetail.getQuantity());
    }

    public BigDecimal getUnformattedQuantityAsBigDecimal() {
        if (adjustedQuantity != null) {
            return adjustedQuantity;
        }

        return new BigDecimal(orderLineItemDetail.getQuantity());
    }

    private Long getRawQuantity() {
        if (adjustedQuantity != null) {
            return Numbers.round(adjustedQuantity);
        }

        return orderLineItemDetail.getQuantity();
    }

    public Map<String, Boolean> itemQuantity() {
        Long quantity = getRawQuantity();

        return Map.of(quantity.toString(), true);
    }

    public String getBlockQuantity() {
        ChargeJson charge = orderLineItemDetail.getCharge();

        if (charge.getChargeModel() == ChargeModel.FLAT_FEE) {
            // Quantity does not apply for flat_fee charge models
            return QUANTITY_ONE;
        }

        long itemQuantity = orderLineItemDetail.getQuantity();
        if (ChargeModel.BLOCK == charge.getChargeModel()) {
            List<PriceTier> priceTiers = getChargePriceTiers();

            Optional<Long> fromPriceTier = Optional.empty();
            Optional<Long> toPriceTier = Optional.empty();

            for (PriceTier priceTier : priceTiers) {
                // find the last price tier with until quantity that is less than order item quantity.
                if (priceTier.getUntilQuantity().isPresent() && priceTier.getUntilQuantity().get() < itemQuantity) {
                    fromPriceTier = priceTier.getUntilQuantity();
                }

                // find the first price tier with until quantity that is greater than or equal to the order item quantity.
                if (toPriceTier.isEmpty() && priceTier.getUntilQuantity().isPresent() && priceTier.getUntilQuantity().get() >= itemQuantity) {
                    toPriceTier = priceTier.getUntilQuantity();
                }
            }

            // if the fromQuantity is present, add 1 to exclude the previous tier end quantity. If fromQuantity is not present, the default start quantity is 0.
            String fromQuantity = fromPriceTier.map(priceTier -> formatter.numberFormat(priceTier + 1)).orElse(QUANTITY_ZERO);

            String topQuantity = Optional.ofNullable(charge.getMaxQuantity()).map(formatter::numberFormat).orElse(TemplateConstants.ANY_QUANTITY);

            // if the toQuantity is present, it's the end of the range. If toQuantity is not present, the default is any (i.e. unlimited quantity).
            String toQuantity = toPriceTier.map(formatter::numberFormat).orElse(topQuantity);

            return String.format(QUANTITY_RANGE_FORMAT, fromQuantity, toQuantity);
        }

        return formatter.numberFormat(orderLineItemDetail.getQuantity());
    }

    public boolean getHasQuantity() {
        return (orderLineItemDetail.getCharge().getChargeModel() == ChargeModel.FLAT_FEE) || (orderLineItemDetail.getQuantity() != 0);
    }

    public String getUnitPrice() {
        return formatter.currencyFormat(orderLineItemDetail.getSellUnitPrice(), currencyCode, 5);
    }

    public String getUnitPriceUnformatted() {
        return orderLineItemDetail.getSellUnitPrice().toString();
    }

    public Function<String, String> getRoundedUnitPrice() {
        return input -> formatter.currencyFormat(orderLineItemDetail.getSellUnitPrice(), currencyCode, input);
    }

    public String getListPrice() {
        return formatter.currencyFormat(orderLineItemDetail.getListUnitPrice(), currencyCode, 5);
    }

    public String getListPriceUnformatted() {
        return orderLineItemDetail.getListUnitPrice().toString();
    }

    public Function<String, String> getRoundedListPrice() {
        return input -> formatter.currencyFormat(orderLineItemDetail.getListUnitPrice(), currencyCode, input);
    }

    public String getUnitDiscountAmount() {
        return formatter.currencyFormat(orderLineItemDetail.getListUnitPrice().subtract(orderLineItemDetail.getSellUnitPrice()), currencyCode, 5);
    }

    public Function<String, String> getRoundedUnitDiscountAmount() {
        BigDecimal discountAmount = orderLineItemDetail.getListUnitPrice().subtract(orderLineItemDetail.getSellUnitPrice());
        return input -> formatter.currencyFormat(discountAmount, currencyCode, input);
    }

    public String getDiscountAmount() {
        return formatter.currencyFormat(orderLineItemDetail.getListAmount().subtract(orderLineItemDetail.getAmount()), currencyCode);
    }

    public BigDecimal getDiscountAmountUnformatted() {
        return Numbers.makeCurrencyScale(orderLineItemDetail.getListAmount().subtract(orderLineItemDetail.getAmount()), currencyCode);
    }

    public boolean hasZeroListAmount() {
        return orderLineItemDetail.getListAmount() == null || Numbers.isZero(orderLineItemDetail.getListAmount());
    }

    public String getListAmount() {
        return formatter.currencyFormat(getListAmountUnformatted(), currencyCode);
    }

    public BigDecimal getListAmountUnformatted() {
        return adjustedListAmount != null ? adjustedListAmount : Numbers.makeCurrencyScale(orderLineItemDetail.getListAmount(), currencyCode);
    }

    public String getTaxEstimate() {
        return formatter.currencyFormat(orderLineItemDetail.getTaxEstimate(), currencyCode);
    }

    public String getAmount() {
        return formatter.currencyFormat(getAmountUnformatted(), currencyCode);
    }

    public BigDecimal getAmountUnformatted() {
        return adjustedAmount != null ? adjustedAmount : Numbers.makeCurrencyScale(orderLineItemDetail.getAmount(), currencyCode);
    }

    public String getStartDate() {
        return formatter.dateFormat(Instant.ofEpochSecond(orderLineItemDetail.getEffectiveDate()));
    }

    public ChargeType getChargeType() {
        return orderLineItemDetail.getCharge().getType();
    }

    public ChargeModel getChargeModel() {
        return orderLineItemDetail.getCharge().getChargeModel();
    }

    public String getEndDate() {
        return formatter.endDateFormat(orderLineItemDetail.getEndDate());
    }

    public DiscountDetailJson getLineItemDiscount() {
        return CollectionUtils.isEmpty(orderLineItemDetail.getDiscounts()) ? null : orderLineItemDetail.getDiscounts().get(0);
    }

    public String getLineItemDiscountPercent() {
        BigDecimal lineItemDiscount = getItemDiscount();
        if (Numbers.isZero(lineItemDiscount)) {
            return StringUtils.EMPTY;
        }
        return formatter.formatPercent(lineItemDiscount);
    }

    public String getPercentOfChargePercent() {
        if (orderLineItemDetail.getCharge().getType() != ChargeType.PERCENTAGE_OF) {
            return StringUtils.EMPTY;
        }
        Validator.checkNonNullInternal(orderLineItemDetail.getCharge().getPercent(), "charge percent");
        return formatter.formatPercent(orderLineItemDetail.getCharge().getPercent());
    }

    public String getPercentOfChargeNetPercent() {
        if (orderLineItemDetail.getCharge().getType() != ChargeType.PERCENTAGE_OF) {
            return StringUtils.EMPTY;
        }

        BigDecimal chargePercent = orderLineItemDetail.getCharge().getPercent();
        Validator.checkNonNullInternal(chargePercent, "charge percent");
        if (Numbers.isZero(chargePercent)) {
            return formatter.formatPercent(BigDecimal.ZERO);
        }

        if (orderLineItemDetail.getListPriceOverrideRatio() != null) {
            chargePercent = chargePercent.multiply(orderLineItemDetail.getListPriceOverrideRatio());
        }

        BigDecimal netLineDiscount = getNetDiscountOnLineItem();
        if (Numbers.isZero(netLineDiscount)) {
            return formatter.formatPercent(chargePercent);
        }
        return formatter.formatPercent(chargePercent.multiply(BigDecimal.ONE.subtract(netLineDiscount)));
    }

    // calculate net discount on line item by applying all discounts in sequence
    // We start with a value of 1 (100%) and apply each discount sequentially.
    // For each discount, we multiply the current value by (1 - discount percent) to get the net value after applying that discount.
    // This means that if we have a 10% discount, we multiply by 0.9 (1 - 0.1).
    // If we have a 20% discount, we multiply by 0.8 (1 - 0.2).
    // This continues for all discounts, effectively compounding the discounts including any predefined discounts.
    // Finally, we subtract the resulting value from 1 to get the net discount percentage.
    private BigDecimal getNetDiscountOnLineItem() {
        BigDecimal value = BigDecimal.ONE;
        for (DiscountDetailJson discount : orderLineItemDetail.getDiscounts()) {
            if (discount.getPercent() != null && discount.getPercent().compareTo(BigDecimal.ZERO) > 0) {
                value = value.multiply(BigDecimal.ONE.subtract(discount.getPercent()));
            }
        }

        for (PredefinedDiscountLineItemDetail discount : orderLineItemDetail.getPredefinedDiscounts()) {
            if (discount.getPercent() != null && discount.getPercent().compareTo(BigDecimal.ZERO) > 0) {
                value = value.multiply(BigDecimal.ONE.subtract(discount.getPercent()));
            }
        }

        return BigDecimal.ONE.subtract(value);
    }

    // lambda to return item discount rounded to the given precision
    // example: for discount of 33.33% {{#roundedLineItemDiscountPercent}}2{{/roundedLineItemDiscountPercent}} will return 33.33
    // {{#roundedLineItemDiscountPercent}}0{{/roundedLineItemDiscountPercent}} will return 33
    public Function<String, String> getRoundedLineItemDiscountPercent() {
        return input -> {
            if (StringUtils.isBlank(input) || !TemplateMathFunctions.isNumber(input)) {
                // if no input or input is not a number return default discount percent treatment
                return getLineItemDiscountPercent();
            }

            try {
                int decimals = TemplateMathFunctions.parseInt(input);
                BigDecimal discountRatio = getItemDiscount();

                if (discountRatio == null) {
                    return StringUtils.EMPTY;
                }

                return formatter.formatPercent(discountRatio, decimals);
            } catch (NumberFormatException ex) {
                return getLineItemDiscountPercent();
            }
        };
    }

    public BigDecimal getItemDiscount() {
        return DiscountCalculator.getLineItemDiscount(orderLineItemDetail);
    }

    public boolean hasLineItemDiscount() {
        return (
            !orderLineItemDetail.getDiscounts().isEmpty() &&
            orderLineItemDetail
                .getDiscounts()
                .stream()
                .anyMatch(discount -> discount.getPercent() != null && discount.getPercent().compareTo(BigDecimal.ZERO) > 0)
        );
    }

    public boolean getIsRecurring() {
        return orderLineItemDetail.getCharge().getType().isRecurring();
    }

    public boolean getIsUsage() {
        return orderLineItemDetail.getCharge().getType() == ChargeType.USAGE;
    }

    public boolean getIsChargeTypeRecurring() {
        return orderLineItemDetail.getCharge().getType() == ChargeType.RECURRING;
    }

    public boolean getIsRecurringMonthly() {
        return (
            orderLineItemDetail.getCharge().getType() == ChargeType.RECURRING && orderLineItemDetail.getCharge().getRecurrence().getCycle() == MONTH
        );
    }

    public boolean getIsRecurringYearly() {
        return (
            orderLineItemDetail.getCharge().getType() == ChargeType.RECURRING && orderLineItemDetail.getCharge().getRecurrence().getCycle() == YEAR
        );
    }

    public boolean getIsRecurringAndRenewable() {
        return getIsRenewable() && getIsRecurring();
    }

    public String getMonthlyListPrice() {
        if (orderLineItemDetail.getListUnitPrice() == null) {
            return StringUtils.EMPTY;
        }

        if (!getIsRecurring() || orderLineItemDetail.getCharge().getType() == ChargeType.PERCENTAGE_OF) {
            return StringUtils.EMPTY;
        }

        if (orderLineItemDetail.getCharge().getRecurrence().getCycle() == MONTH) {
            return formatter.currencyFormat(orderLineItemDetail.getListUnitPrice(), currencyCode);
        }

        if (orderLineItemDetail.getCharge().getRecurrence().getCycle() == YEAR) {
            return formatter.currencyFormat(
                Numbers.scaledDivide(orderLineItemDetail.getListUnitPrice(), BigDecimal.valueOf(MONTHS_IN_YEAR)),
                currencyCode
            );
        }
        return StringUtils.EMPTY;
    }

    public String getMonthlyNetPrice() {
        if (orderLineItemDetail.getSellUnitPrice() == null) {
            return StringUtils.EMPTY;
        }
        if (!getIsRecurring() || orderLineItemDetail.getCharge().getType() == ChargeType.PERCENTAGE_OF) {
            return StringUtils.EMPTY;
        }
        if (orderLineItemDetail.getCharge().getRecurrence().getCycle() == MONTH) {
            return formatter.currencyFormat(orderLineItemDetail.getSellUnitPrice(), currencyCode);
        }
        if (orderLineItemDetail.getCharge().getRecurrence().getCycle() == YEAR) {
            return formatter.currencyFormat(
                Numbers.scaledDivide(orderLineItemDetail.getSellUnitPrice(), BigDecimal.valueOf(MONTHS_IN_YEAR)),
                currencyCode
            );
        }
        return StringUtils.EMPTY;
    }

    public String getYearlyDiscountAmount() {
        return getYearlyDiscountAmountValue()
            .map(yearlyDiscountAmount -> formatter.currencyFormat(yearlyDiscountAmount, currencyCode))
            .orElse(StringUtils.EMPTY);
    }

    public String getMonthlyDiscountAmount() {
        return getYearlyDiscountAmountValue()
            .map(yearlyDiscountAmount -> formatter.currencyFormat(TemplateCalculations.yearlyToMonthlyAmount(yearlyDiscountAmount), currencyCode))
            .orElse(StringUtils.EMPTY);
    }

    private Optional<BigDecimal> getYearlyDiscountAmountValue() {
        Optional<BigDecimal> yearlyListAmountValue = getYearlyListAmountValue();
        Optional<BigDecimal> yearlyAmountValue = getYearlyAmountValue();

        if (yearlyListAmountValue.isEmpty() || yearlyAmountValue.isEmpty()) {
            return Optional.empty();
        }

        return yearlyListAmountValue.map(yearlyListAmount -> yearlyListAmount.subtract(yearlyAmountValue.get()));
    }

    public String getYearlyListAmount() {
        BigDecimal lineDiscount = DiscountCalculator.calculateDiscountRatio(orderLineItemDetail.getAmount(), orderLineItemDetail.getListAmount());
        if (Numbers.isZero(BigDecimal.ONE.subtract(lineDiscount))) {
            return formatter.currencyFormat(BigDecimal.ZERO, currencyCode);
        }

        return getYearlyAmountValue()
            .map(yearlyAmount -> yearlyAmount.divide(BigDecimal.ONE.subtract(lineDiscount), RoundingMode.HALF_UP))
            .map(yearlyListAmount -> formatter.currencyFormat(yearlyListAmount, currencyCode))
            .orElse(StringUtils.EMPTY);
    }

    private Optional<BigDecimal> getYearlyListAmountValue() {
        BigDecimal lineDiscount = DiscountCalculator.calculateDiscountRatio(orderLineItemDetail.getAmount(), orderLineItemDetail.getListAmount());
        if (Numbers.isZero(BigDecimal.ONE.subtract(lineDiscount))) {
            return Optional.of(BigDecimal.ZERO);
        }

        return getYearlyAmountValue().map(yearlyAmount -> yearlyAmount.divide(BigDecimal.ONE.subtract(lineDiscount), RoundingMode.HALF_UP));
    }

    public String getMonthlyListAmount() {
        Optional<BigDecimal> yearAmount = getYearlyListAmountValue();
        return yearAmount
            .map(yearlyAmount -> formatter.currencyFormat(TemplateCalculations.yearlyToMonthlyAmount(yearlyAmount), currencyCode))
            .orElse(StringUtils.EMPTY);
    }

    public String getYearlyAmount() {
        return getYearlyAmountValue().map(yearlyAmount -> formatter.currencyFormat(yearlyAmount, currencyCode)).orElse(StringUtils.EMPTY);
    }

    public String getMonthlyAmount() {
        return getYearlyAmountValue()
            .map(yearlyAmount -> formatter.currencyFormat(TemplateCalculations.yearlyToMonthlyAmount(yearlyAmount), currencyCode))
            .orElse(StringUtils.EMPTY);
    }

    public BigDecimal getMonthlyAmountRawValue() {
        return getYearlyAmountValue().map(TemplateCalculations::yearlyToMonthlyAmount).orElse(BigDecimal.ZERO);
    }

    public Optional<BigDecimal> getYearlyAmountValue() {
        if (adjustedYearlyAmount != null) {
            return Optional.of(adjustedYearlyAmount);
        }

        if (orderLineItemDetail.getAnnualizedAmount() != null) {
            return Optional.of(orderLineItemDetail.getAnnualizedAmount());
        }

        return (entryArr != null && orderLineItemDetail.getCharge().isRecurring()) ? Optional.of(entryArr) : Optional.empty();
    }

    public String getUnitOfMeasure() {
        return formatter.formatUnitOfMeasure(getCharge().getUnitOfMeasure());
    }

    // Workaround process to provide one-time discounts to customers as an independent line item
    // is to create a one-time custom charge with negative $ amount
    public boolean isOneTimeDiscount() {
        return (
            orderLineItemDetail.getCharge().isCustom() &&
            orderLineItemDetail.getCharge().getChargeModel() == ChargeModel.FLAT_FEE &&
            orderLineItemDetail.getCharge().getType() == ChargeType.ONE_TIME &&
            orderLineItemDetail.getAmount().compareTo(BigDecimal.ZERO) < 0
        );
    }

    public String getTermLengthInYears() {
        BigDecimal periodInYears = getRawTermLengthInYears();
        return String.format("%s %s", periodInYears.toPlainString(), periodInYears.doubleValue() > 1 ? YEAR.getPluralName() : YEAR.getSingularName());
    }

    public BigDecimal getRawTermLengthInYears() {
        BigDecimal years = Period.toDurationInYears(
            Instant.ofEpochSecond(getOrderLineItemDetail().getEffectiveDate()),
            Instant.ofEpochSecond(getOrderLineItemDetail().getEndDate()),
            timeZone
        );

        return years.stripTrailingZeros();
    }

    public String getTermLengthInMonths() {
        long periodInMonths = Period.toDurationInMonthsFloor(
            Instant.ofEpochSecond(getOrderLineItemDetail().getEffectiveDate()),
            Instant.ofEpochSecond(getOrderLineItemDetail().getEndDate()),
            timeZone
        );
        return formatMonths(periodInMonths);
    }

    public String getRoundedTermLengthInMonths() {
        long periodInMonths = Period.toDurationInMonthsRounded(
            Instant.ofEpochSecond(getOrderLineItemDetail().getEffectiveDate()),
            Instant.ofEpochSecond(getOrderLineItemDetail().getEndDate()),
            timeZone
        );
        return formatMonths(periodInMonths);
    }

    private String formatMonths(long months) {
        return String.format("%d %s", months, months > 1 ? MONTH.getPluralName() : MONTH.getSingularName());
    }

    public boolean isOneTimeCharge() {
        return orderLineItemDetail.getCharge().getType() == ChargeType.ONE_TIME;
    }

    public boolean isCustomCharge() {
        return orderLineItemDetail.getCharge().isCustom();
    }

    public boolean isCustomChargeWithZeroListAmount() {
        return (
            orderLineItemDetail.getCharge().isCustom() &&
            (orderLineItemDetail.getListAmount() == null || Numbers.isZero(orderLineItemDetail.getListAmount()))
        );
    }

    public boolean getIsRenewable() {
        return orderLineItemDetail.getCharge().getIsRenewable();
    }

    public boolean modified() {
        return orderLineItemDetail.getAction() != ActionType.NONE;
    }

    public Map<String, Boolean> getAction() {
        return Map.of(orderLineItemDetail.getAction().name(), true);
    }

    public List<PredefinedDiscountLineItemDetail> getLineItemPredefinedDiscounts() {
        return orderLineItemDetail.getPredefinedDiscounts();
    }

    public BigDecimal getTotalPredefinedDiscountAmount() {
        List<PredefinedDiscountLineItemDetail> predefinedDiscounts = getLineItemPredefinedDiscounts();
        return predefinedDiscounts.stream().map(PredefinedDiscountLineItemDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private List<PriceTier> getChargePriceTiers() {
        if (orderLineItemDetail.getPricingOverride() != null && orderLineItemDetail.getPricingOverride().priceTiers() != null) {
            return orderLineItemDetail.getPricingOverride().priceTiers().stream().map(planMapper::jsonToPriceTier).toList();
        }
        return orderLineItemDetail.getCharge().getPriceTiers().stream().map(planMapper::jsonToPriceTier).collect(Collectors.toList());
    }

    public boolean hasPriceTiers() {
        return !getPriceTiers().isEmpty();
    }

    public List<TemplatePriceTier> getPriceTiers() {
        List<PriceTier> priceTiers = getChargePriceTiers();

        if (CollectionUtils.isEmpty(priceTiers)) {
            return List.of();
        }

        List<TemplatePriceTier> templatePriceTiers = new ArrayList<>();

        long previousUntilQuantity = 0;

        for (int i = 0; i < priceTiers.size(); i++) {
            PriceTier priceTier = priceTiers.get(i);
            boolean isFirst = i == 0;
            boolean isLast = i == priceTiers.size() - 1;
            Optional<Long> from = isFirst ? getMinQuantity() : Optional.of(previousUntilQuantity + 1);
            Optional<Long> to = isLast ? getMaxQuantity() : priceTier.getUntilQuantity();
            String amount = formatter.unitPriceCurrencyFormat(priceTier.getAmount(), currencyCode);

            templatePriceTiers.add(new TemplatePriceTier(formatter, from, to, amount, isFirst, isLast));
            previousUntilQuantity = priceTier.getUntilQuantity().orElse(0L);
        }

        return templatePriceTiers;
    }

    private Optional<Long> getMinQuantity() {
        if (orderLineItemDetail.getPricingOverride() != null) {
            return Optional.ofNullable(orderLineItemDetail.getPricingOverride().minQuantity());
        }
        return Optional.ofNullable(orderLineItemDetail.getCharge().getMinQuantity());
    }

    private Optional<Long> getMaxQuantity() {
        if (orderLineItemDetail.getPricingOverride() != null) {
            return Optional.ofNullable(orderLineItemDetail.getPricingOverride().maxQuantity());
        }
        return Optional.ofNullable(orderLineItemDetail.getCharge().getMaxQuantity());
    }

    // get custom field to display on order form. Custom fields can be accessed via {{customFields.<fieldName>}}
    public Map<String, TemplateCustomField> getCustomFields() {
        return TemplateCustomField.getCustomFieldsMap(customField);
    }

    public boolean isTieredPricing() {
        return orderLineItemDetail.getCharge().getChargeModel() == ChargeModel.TIERED;
    }

    public boolean isBlockPricing() {
        return orderLineItemDetail.getCharge().getChargeModel() == ChargeModel.BLOCK;
    }

    public boolean isVolumeDiscountPricing() {
        return orderLineItemDetail.getCharge().getChargeModel() == ChargeModel.VOLUME;
    }

    public boolean isZeroAmount() {
        return Numbers.isZero(orderLineItemDetail.getAmount());
    }

    public List<PriceAttributeData> getPriceAttributes() {
        // never return null
        if (priceAttributes == null) {
            return List.of();
        }
        return priceAttributes;
    }

    public Map<String, String> getAttributes() {
        return priceAttributes.stream().collect(Collectors.toMap(PriceAttributeData::name, PriceAttributeData::value));
    }

    /**
     * Tag to be used for conditional check on charge model, eg: {{#isChargeModel.PER_UNIT}}...{{/isChargeModel.PER_UNIT}}
     */
    public Map<String, Boolean> getIsChargeModel() {
        return Map.of(orderLineItemDetail.getCharge().getChargeModel().name(), true);
    }

    /**
     * Tag to be used for conditional check on charge type, eg: {{#isChargeType.ONE_TIME}}...{{/isChargeType.ONE_TIME}}
     */
    public Map<String, Boolean> getIsChargeType() {
        return Map.of(orderLineItemDetail.getCharge().getType().name(), true);
    }

    public boolean isRampLineItem() {
        return getOrderLineItemDetail().getIsRamp();
    }

    public boolean isDiscountLine() {
        return orderLineItemDetail.getCharge().getIsDiscount();
    }

    public List<OrderTemplateLineItem> getRawLineItems() {
        return rawLineItems;
    }

    public void setRawLineItems(List<OrderTemplateLineItem> rawLineItems) {
        this.rawLineItems = rawLineItems;
    }

    public boolean isNegativeAmount() {
        return Numbers.isNegative(orderLineItemDetail.getAmount());
    }
}
