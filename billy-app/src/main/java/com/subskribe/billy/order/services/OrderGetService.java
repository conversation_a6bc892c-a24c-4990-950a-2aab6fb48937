package com.subskribe.billy.order.services;

import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.metrics.datadog.AllMetrics;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderStub;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.template.model.OrderTerms;
import com.subskribe.billy.template.services.DocumentCustomContentGetService;
import com.subskribe.billy.template.services.OrderTermsService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.DSLContext;

@AllMetrics
public class OrderGetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderGetService.class);

    private final OrderDAO orderDAO;
    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final DiscountService discountService;
    private final OpportunityGetService opportunityGetService;
    private final OrderTermsService orderTermsService;
    private final CustomFieldService customFieldService;
    private final DocumentCustomContentGetService documentCustomContentGetService;

    @Inject
    public OrderGetService(
        OrderDAO orderDAO,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider,
        DiscountService discountService,
        OpportunityGetService opportunityGetService,
        OrderTermsService orderTermsService,
        CustomFieldService customFieldService,
        DocumentCustomContentGetService documentCustomContentGetService
    ) {
        this.orderDAO = orderDAO;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.discountService = discountService;
        this.opportunityGetService = opportunityGetService;
        this.orderTermsService = orderTermsService;
        this.customFieldService = customFieldService;
        this.documentCustomContentGetService = documentCustomContentGetService;
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Order> getOrders(PaginationQueryParams paginationQueryParams, Optional<OrderStatus> orderStatus) {
        var orders = orderDAO.getOrders(paginationQueryParams, orderStatus);
        addOpportunitiesToOrders(orders);
        addCustomFieldsToOrders(orders);
        return orders;
    }

    public List<Order> getOrders(String accountId, Set<String> orderIds) {
        return orderDAO.getOrders(accountId, orderIds);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Order> getOrdersInAccount(String accountId, PaginationQueryParams paginationQueryParams, Optional<OrderStatus> orderStatus) {
        var orders = orderDAO.getOrdersInAccount(accountId, paginationQueryParams, orderStatus);
        addOpportunitiesToOrders(orders);
        addCustomFieldsToOrders(orders);
        return orders;
    }

    public List<Order> getOrdersByOrderIds(List<String> orderIds) {
        Validator.validateCollectionNotEmpty(orderIds, "orderIds");
        List<Order> orders = orderDAO.getOrdersByOrderIds(orderIds);
        addOpportunitiesToOrders(orders);

        return orders;
    }

    public Map<String, List<Order>> getOrdersOverviewInPeriod(Period period, ZoneId timeZoneId) {
        return orderDAO.getOrdersOverviewInPeriod(period, timeZoneId);
    }

    public PageResult<List<OrderStub>, Instant> getOrderStubs(PageRequest<Instant> pageRequest) {
        return orderDAO.getOrderStubs(pageRequest);
    }

    public Optional<OrderStub> getOrderStub(String orderId) {
        Validator.validateStringNotBlank(orderId, "orderId is required.");
        return orderDAO.getOrderStub(orderId);
    }

    public OrderStub getOrderStubOrThrow(String orderId) {
        return getOrderStub(orderId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ORDER, orderId));
    }

    public void validateOrderExists(String orderId) {
        Validator.validateStringNotBlank(orderId, "orderId is required.");
        if (!orderDAO.orderExists(orderId)) {
            throw new ObjectNotFoundException(BillyObjectType.ORDER, orderId);
        }
    }

    public OrderStatus getOrderStatus(String orderId) {
        Validator.validateStringNotBlank(orderId, "orderId is required.");

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Optional<Order> order = orderDAO.getOrderByOrderId(tenantDslContext, orderId);
        return order.map(Order::getStatus).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ORDER, orderId));
    }

    public Optional<Order> getOrderByOrderIdOptional(String orderId) {
        Validator.validateStringNotBlank(orderId, "OrderId is null or empty");

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return orderDAO.getOrderByOrderId(tenantDslContext, orderId);
    }

    public Order getOrderByOrderId(String orderId) {
        Validator.validateStringNotBlank(orderId, "OrderId is null or empty");

        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var orderResult = orderDAO.getOrderByOrderId(tenantDslContext, orderId);

        if (orderResult.isEmpty()) {
            LOGGER.info("order with order id {} not found", orderId);
            throw new ObjectNotFoundException(BillyObjectType.ORDER, orderId);
        }

        var order = orderResult.get();

        List<OrderTerms> orderTerms = orderTermsService.getOrderTermsByOrderId(orderId);
        order.setOrderTerms(orderTerms);
        List<String> orderFormTemplateIds = orderTerms.stream().map(OrderTerms::getTemplateGroupId).toList();
        order.setOrderFormTemplateIds(orderFormTemplateIds);

        opportunityGetService.addOpportunitiesToOrders(List.of(order));
        OrderServiceHelper.fillNetEffectOrderLines(order);
        OrderDiscountService.validateOrderDiscounts(order, discountService, false);

        order.setCustomFields(customFieldService.getCustomFields(CustomFieldParentType.ORDER, orderId));

        if (CollectionUtils.isNotEmpty(order.getLineItems())) {
            List<String> orderLineItemIds = order.getLineItems().stream().map(OrderLineItem::getOrderLineId).toList();
            Map<String, CustomField> orderLineItemCustomFields = customFieldService.getCustomFields(
                CustomFieldParentType.ORDER_ITEM,
                orderLineItemIds
            );
            order.getLineItems().forEach(li -> li.setCustomFieldEntriesFromCustomFieldObject(orderLineItemCustomFields.get(li.getOrderLineId())));
        }

        if (order.getBillingCycle() != null && order.getBillingCycle().getCycle() == Cycle.CUSTOM) {
            CustomBillingSchedule customBillingSchedule = getCustomBillingScheduleForOrder(orderId, false);
            List<String> customBillingEligibleOrderLineIds = customBillingSchedule != null
                ? customBillingSchedule.orderLines()
                : Collections.emptyList();
            order.setCustomBillingEligibleOrderLineIds(customBillingEligibleOrderLineIds);
        }

        documentCustomContentGetService.getDocumentCustomContentByOrderId(orderId).ifPresent(order::setDocumentCustomContent);
        return order;
    }

    public Optional<String> getOrderOwner(String orderId) {
        Validator.validateStringNotBlank(orderId, "OrderId is null or empty");
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Optional<Order> order = orderDAO.getOrderByOrderId(tenantDslContext, orderId);
        return order.map(Order::getOwnerId);
    }

    public Optional<Order> getOrderByExternalId(String externalId) {
        DSLContext tenantDslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return orderDAO.getOrderByExternalId(tenantDslContext, externalId);
    }

    public Optional<OrderLineItem> getOrderLineItemByOrderLineItemId(String id) {
        return orderDAO.getOrderLineItemByOrderLineId(TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider), id);
    }

    public List<OrderLineItem> getOrderLineItemsByOrderLineItemIds(List<String> orderLineItemIds) {
        List<OrderLineItem> items = orderDAO.getOrderLineItems(
            TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider),
            orderLineItemIds
        );
        return items == null ? List.of() : items;
    }

    public List<OrderLineItem> getOrderLineItemsByRampGroupId(UUID rampGroupId) {
        return orderDAO.getOrderLineItemsByRampGroupId(rampGroupId);
    }

    public List<OrderLineItem> getExecutedLineItemsBySubscriptionIdChargeId(String subscriptionId, String chargeId) {
        return getExecutedOrdersBySubscriptionId(subscriptionId)
            .stream()
            .filter(order -> order.getStatus() == OrderStatus.EXECUTED)
            .map(Order::getLineItems)
            .flatMap(List::stream)
            .filter(orderLineItem -> orderLineItem.getChargeId().equals(chargeId))
            .collect(Collectors.toList());
    }

    public boolean contactUsedByOrder(String contactId) {
        return orderDAO.contactUsedByOrder(getDSLContext(), contactId);
    }

    public boolean accountUsedByOrder(String accountId) {
        DSLContext context = getDSLContext();
        return orderDAO.accountUsedByOrder(context, accountId) || orderDAO.accountUsedByOrderAsReseller(context, accountId);
    }

    public boolean accountUsedByOrderWithOpportunity(String accountId) {
        return orderDAO.accountUsedByOrderWithOpportunity(accountId);
    }

    public boolean planUsedByOrder(String planId) {
        return orderDAO.planUsedByOrderLineItem(getDSLContext(), planId);
    }

    public Set<String> entitiesUsedByOrdersUsingPlan(String planId) {
        return orderDAO.entitiesUsedByOrdersUsingPlan(planId);
    }

    public Set<String> entitiesUsedByOrdersUsingTemplate(UUID templateId) {
        return orderDAO.entitiesUsedByOrdersUsingTemplate(templateId);
    }

    public Set<String> entitiesUsedByAccount(String accountId) {
        return orderDAO.entitiesUsedByAccount(accountId);
    }

    public List<Order> getExecutedOrdersBySubscriptionId(String subscriptionId) {
        if (subscriptionId == null) {
            LOGGER.info("subscriptionId is null");
            throw new IllegalArgumentException("subscriptionId is null");
        }

        var orders = orderDAO.getOrdersByExternalSubscriptionId(getDSLContext(), subscriptionId, Optional.of(OrderStatus.EXECUTED));
        addOpportunitiesToOrders(orders);
        return orders;
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Order> getOrderHeadersBySubscriptionUuid(UUID subscriptionUuid, Optional<OrderStatus> status) {
        Validator.validateNonNullArgument(subscriptionUuid, "subscriptionId");

        var orders = orderDAO.getOrderHeadersBySubscriptionUuid(subscriptionUuid, status);
        addOpportunitiesToOrders(orders);
        return orders;
    }

    // get orders by subscription based on status. If status param is empty, return orders of any status
    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Order> getOrdersBySubscriptionIdOrRenewalSubscriptionId(String subscriptionId, Optional<OrderStatus> status) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is null");

        var orders = orderDAO.getOrdersBySubscriptionIdOrRenewalForSubscriptionId(getDSLContext(), subscriptionId, status);
        addOpportunitiesToOrders(orders);
        return orders;
    }

    public List<Order> getOrdersByExternalSubscriptionId(String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is required");
        List<String> orderIds = orderDAO.getOrderIdsByExternalSubscriptionId(getDSLContext(), subscriptionId);
        return loadOrdersByOrderId(orderIds);
    }

    private List<Order> loadOrdersByOrderId(List<String> orderIds) {
        return orderIds.stream().map(this::getOrderByOrderId).collect(Collectors.toList());
    }

    private void addOpportunitiesToOrders(List<Order> orders) {
        opportunityGetService.addOpportunitiesToOrders(orders);
        OrderServiceHelper.fillNetEffectOrderLines(orders);
        orders.forEach(order -> OrderDiscountService.validateOrderDiscounts(order, discountService, false));
    }

    private void addCustomFieldsToOrders(List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        Map<String, CustomField> orderCustomFields = customFieldService.getCustomFields(
            CustomFieldParentType.ORDER,
            orders.stream().map(Order::getOrderId).toList()
        );

        orders.forEach(o -> addCustomFieldsToOrder(o, orderCustomFields.get(o.getOrderId())));
    }

    private void addCustomFieldsToOrder(Order order, CustomField orderCustomFields) {
        order.setCustomFields(orderCustomFields);
        if (CollectionUtils.isEmpty(order.getLineItems())) {
            return;
        }

        Map<String, CustomField> orderLineItemCustomFields = customFieldService.getCustomFields(
            CustomFieldParentType.ORDER_ITEM,
            order.getLineItems().stream().map(OrderLineItem::getOrderLineId).toList()
        );

        order.getLineItems().forEach(li -> li.setCustomFieldEntriesFromCustomFieldObject(orderLineItemCustomFields.get(li.getOrderLineId())));
    }

    public List<Order> getDeletedOrdersByCrmOpportunityIds(List<String> crmOpportunityIds) {
        if (crmOpportunityIds.isEmpty()) {
            return List.of();
        }
        return orderDAO.getDeletedOrdersByCrmOpportunityIds(getDSLContext(), crmOpportunityIds);
    }

    public List<Order> getOrdersByCrmOpportunityId(String crmOpportunityId) {
        Validator.validateStringNotBlank(crmOpportunityId, "opportunityId is null");

        var orders = orderDAO.getOrdersByCrmOpportunityId(getDSLContext(), crmOpportunityId);
        addOpportunitiesToOrders(orders);
        return orders;
    }

    public List<Order> getOrdersByAccountId(String accountId) {
        if (accountId == null) {
            LOGGER.info("accountId is null");
            throw new IllegalArgumentException("accountId is null");
        }

        var orders = orderDAO.getOrdersByAccountId(getDSLContext(), accountId);
        addOpportunitiesToOrders(orders);
        return orders;
    }

    public List<String> getOrderlineIdsForMetricsUpdateLocked(DSLContext dslContext, String tenantId, int limit) {
        return orderDAO.getOrderlineIdsForMetricsUpdateLocked(dslContext, tenantId, limit);
    }

    public List<String> getExecutedOrderIdsForMetricsUpdateLocked(DSLContext txnDslContext, String tenantId, int limit) {
        return orderDAO.getExecutedOrderIdsForMetricsUpdateLocked(txnDslContext, tenantId, limit);
    }

    public List<String> getNonExecutedOrderIdsForMetricsUpdate(String tenantId, int limit) {
        return orderDAO.getNonExecutedOrderIdsForMetricsUpdate(tenantId, limit);
    }

    private DSLContext getDSLContext() {
        return TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
    }

    public boolean isMasterTemplateInUse(UUID masterTemplateId) {
        return orderDAO.isMasterTemplateInUse(masterTemplateId);
    }

    public List<String> getOrderIdsInCompositeOrder(String compositeOrderId) {
        Validator.validateStringNotBlank(compositeOrderId, "compositeOrderId must be provided");

        return orderDAO.getOrderIdsInCompositeOrder(compositeOrderId);
    }

    public List<Order> getOrdersByBillingContacts(List<String> contactIds) {
        Validator.validateCollectionNotEmpty(contactIds, "contactIds");
        List<Order> orders = orderDAO.getOrdersByBillingContacts(contactIds);
        addOpportunitiesToOrders(orders);
        if (CollectionUtils.isEmpty(orders)) {
            return List.of();
        }
        return orders;
    }

    public List<Order> getExpirableOrdersWithValidStatus(Instant currentDateTime) {
        return orderDAO.getExpirableOrders(currentDateTime);
    }

    public List<OrderLineItem> getExecutedOrderLinesWithSameBaseSubscriptionChargeId(String baseSubscriptionChargeId) {
        Validator.validateStringNotBlank(baseSubscriptionChargeId, "baseSubscriptionChargeId is blank");
        return orderDAO.getExecutedOrderLinesWithSameBaseSubscriptionChargeId(baseSubscriptionChargeId);
    }

    public List<OrderLineItem> getRenewalOrderLinesWithMissingBaseSubscriptionChargeId() {
        return orderDAO.getRenewalOrderLinesWithMissingBaseSubscriptionCharge();
    }

    public int getCountOfCrmPrimaryOrdersUpdatedAfterSpecificDate(Instant instant) {
        return orderDAO.getCountOfCrmPrimaryOrdersUpdatedAfterSpecificDate(instant);
    }

    public List<Order> getOrdersToSyncToCrmUpdatedAfterSpecificDate(String accountId, Instant updatedOn) {
        return orderDAO.getOrdersToSyncToCrmUpdatedAfterSpecificDate(accountId, updatedOn);
    }

    public CustomBillingSchedule getCustomBillingScheduleForOrder(String orderId, boolean isInternal) {
        Optional<CustomBillingSchedule> schedule = orderDAO.getCustomBillingSchedules(List.of(orderId)).stream().findFirst();
        return isInternal
            ? schedule.orElseThrow(() -> new ConflictingStateException("Custom billing schedule not found for order id: " + orderId))
            : schedule.orElse(null);
    }

    public Optional<Instant> getLatestOrderLineEndDate() {
        return orderDAO.getLatestOrderLineEndDate();
    }
}
