package com.subskribe.billy.graphql;

import static com.subskribe.billy.resources.shared.PaginationQueryParams.DEFAULT_PAGINATION_QUERY_PARAMS;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.crm.contact.CrmContact;
import com.subskribe.billy.account.crm.contact.mapper.CrmContactMapper;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.model.AccountStub;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.AccountingPeriodStatus;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.accounting.services.AccountingPeriodCalculationService;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.accounting.services.AccountingPeriodStatusUpdateService;
import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.accounting.services.JournalEntryService;
import com.subskribe.billy.admin.AdminOperationsService;
import com.subskribe.billy.admin.model.InvoiceDeletableResponse;
import com.subskribe.billy.admin.model.SubscriptionModifiableResponse;
import com.subskribe.billy.alias.model.SubscriptionChargeAlias;
import com.subskribe.billy.alias.service.AliasService;
import com.subskribe.billy.anrok.model.AnrokIntegrationInput;
import com.subskribe.billy.anrok.service.AnrokIntegrationGetService;
import com.subskribe.billy.anrok.service.AnrokIntegrationService;
import com.subskribe.billy.approvalflow.model.ApprovalFlow;
import com.subskribe.billy.approvalflow.model.ApprovalWithCodeResponse;
import com.subskribe.billy.approvalflow.service.ApprovalFlowService;
import com.subskribe.billy.approvalflowhierarchy.model.ApprovalRole;
import com.subskribe.billy.approvalflowhierarchy.model.ApprovalSegment;
import com.subskribe.billy.approvalflowhierarchy.service.ApprovalFlowHierarchyService;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceGroup;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceStateUpdates;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowSubmitterNote;
import com.subskribe.billy.approvalflowinstance.model.PendingOrderApprovalFlow;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowPendingUserActionService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowStateUpdaterService;
import com.subskribe.billy.attachments.model.Attachment;
import com.subskribe.billy.attachments.service.AttachmentsService;
import com.subskribe.billy.auth.apikey.ApiKeyService;
import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.auth.mapper.ApiKeyDetailMapper;
import com.subskribe.billy.auth.model.ApiKeyDetail;
import com.subskribe.billy.auth.model.ApiKeyMetaData;
import com.subskribe.billy.auth.model.ApiKeyTokenAndSecret;
import com.subskribe.billy.auth.model.UserAuthInfo;
import com.subskribe.billy.auth.model.UserPrincipal;
import com.subskribe.billy.auth.services.EmailLinkLoginService;
import com.subskribe.billy.avalara.model.AvalaraIntegration;
import com.subskribe.billy.avalara.model.AvalaraIntegrationInput;
import com.subskribe.billy.avalara.service.AvalaraIntegrationGetService;
import com.subskribe.billy.avalara.service.AvalaraIntegrationService;
import com.subskribe.billy.aws.cognito.CognitoService;
import com.subskribe.billy.aws.es.ElasticSearchService;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationship;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipGetService;
import com.subskribe.billy.clm.service.CLMService;
import com.subskribe.billy.compositeorder.model.CancelAndRestructureOrderDataAggregator;
import com.subskribe.billy.compositeorder.model.CancelAndRestructureOrderDetail;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.model.CompositeOrderDetail;
import com.subskribe.billy.compositeorder.model.ImmutableCancelAndRestructureOrderDetail;
import com.subskribe.billy.compositeorder.service.CompositeOrderDocumentService;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.compositeorder.service.CompositeOrderService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.CrmIntegrationService;
import com.subskribe.billy.crm.CrmType;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.currency.model.CurrencyJson;
import com.subskribe.billy.customfield.CustomFieldProxy;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldDefinitionUpdate;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.customization.SelectionCustomizationInput;
import com.subskribe.billy.customization.model.Action;
import com.subskribe.billy.customization.model.PlanAdditionCustomizationInput;
import com.subskribe.billy.customization.model.ui.UIConfiguration;
import com.subskribe.billy.customization.service.CustomizationService;
import com.subskribe.billy.customization.service.ui.UIConfigurationService;
import com.subskribe.billy.dataimport.model.DataImport;
import com.subskribe.billy.dataimport.model.ImportDomain;
import com.subskribe.billy.dataimport.model.flatfile.FlatfileSpace;
import com.subskribe.billy.dataimport.model.flatfile.FlatfileSpaceResponse;
import com.subskribe.billy.dataimport.service.DataImportService;
import com.subskribe.billy.dataimport.service.FlatfileService;
import com.subskribe.billy.discount.model.Discount;
import com.subskribe.billy.discount.model.DiscountStatus;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.document.model.DocumentLink;
import com.subskribe.billy.document.service.DocumentLinkService;
import com.subskribe.billy.docusign.mapper.DocuSignMapper;
import com.subskribe.billy.docusign.model.DocuSignIntegration;
import com.subskribe.billy.docusign.model.DocuSignIntegrationRequestJson;
import com.subskribe.billy.docusign.service.DocuSignAuthService;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.email.model.EmailLog;
import com.subskribe.billy.email.model.EmailSetting;
import com.subskribe.billy.email.services.CreditMemoEmailService;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.email.services.EmailService;
import com.subskribe.billy.email.services.EmailSettingService;
import com.subskribe.billy.email.services.InvoiceEmailService;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.graphql.EntityResolver;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.model.EntityPatchRequest;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.entity.service.EntityService;
import com.subskribe.billy.erp.model.ErpInvoiceJson;
import com.subskribe.billy.erp.service.ErpIntegrationService;
import com.subskribe.billy.erp.service.ErpSyncQueueService;
import com.subskribe.billy.erp.service.ErpSyncService;
import com.subskribe.billy.escalationpolicy.model.EscalationPolicy;
import com.subskribe.billy.escalationpolicy.model.ImmutableEscalationPolicy;
import com.subskribe.billy.escalationpolicy.service.EscalationPolicyService;
import com.subskribe.billy.esign.model.ElectronicSignatureProvider;
import com.subskribe.billy.esign.model.EsignRequest;
import com.subskribe.billy.esign.model.EsignTenantSignatory;
import com.subskribe.billy.esign.model.EsignTenantSignatoryDetail;
import com.subskribe.billy.esign.model.ImmutableElectronicSignatureAuditLog;
import com.subskribe.billy.esign.services.EsignIntegrationService;
import com.subskribe.billy.esign.services.EsignService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.model.CurrencyTypeSetting;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateService;
import com.subskribe.billy.foreignexchange.service.CurrencyTypeSettingGetService;
import com.subskribe.billy.foreignexchange.service.CurrencyTypeSettingService;
import com.subskribe.billy.graphql.account.AccountContactDataAggregator;
import com.subskribe.billy.graphql.account.AccountContactDetail;
import com.subskribe.billy.graphql.account.AccountDataAggregator;
import com.subskribe.billy.graphql.account.AccountDetail;
import com.subskribe.billy.graphql.approvalflow.ApprovalFlowDataAggregator;
import com.subskribe.billy.graphql.clm.CLMChatMessageDetail;
import com.subskribe.billy.graphql.clm.CLMCreateThreadRequest;
import com.subskribe.billy.graphql.clm.CLMThreadCreationDetail;
import com.subskribe.billy.graphql.clm.CLMThreadDetail;
import com.subskribe.billy.graphql.compositeorder.AmendAndRenewCreateRequest;
import com.subskribe.billy.graphql.compositeorder.CompositeOrderFieldsUpdateRequest;
import com.subskribe.billy.graphql.creditmemo.CreditMemoDataAggregator;
import com.subskribe.billy.graphql.creditmemo.CreditMemoDetail;
import com.subskribe.billy.graphql.creditmemo.CreditMemoEmailResponse;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.customfield.CustomFieldInput;
import com.subskribe.billy.graphql.customfield.CustomFieldWithParentReference;
import com.subskribe.billy.graphql.esign.ElectronicSignatureDetail;
import com.subskribe.billy.graphql.esign.EsignDataAggregator;
import com.subskribe.billy.graphql.invoice.BillingEventEntry;
import com.subskribe.billy.graphql.invoice.BillingEventInput;
import com.subskribe.billy.graphql.invoice.BillingEventMapper;
import com.subskribe.billy.graphql.invoice.BillingEvents;
import com.subskribe.billy.graphql.invoice.InvoiceDataAggregator;
import com.subskribe.billy.graphql.invoice.InvoiceDetail;
import com.subskribe.billy.graphql.invoice.InvoiceEmailResponse;
import com.subskribe.billy.graphql.invoice.InvoiceItemDetail;
import com.subskribe.billy.graphql.invoice.SettlementApplicationDataAggregator;
import com.subskribe.billy.graphql.invoicedunning.InvoiceDunningDetailMapper;
import com.subskribe.billy.graphql.order.AmendmentOrderMapper;
import com.subskribe.billy.graphql.order.CancelAndRestructureRequest;
import com.subskribe.billy.graphql.order.OrderDataAggregator;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.payment.FailedPaymentDetail;
import com.subskribe.billy.graphql.payment.PaymentDataAggregator;
import com.subskribe.billy.graphql.payment.PaymentDetail;
import com.subskribe.billy.graphql.payment.PaymentInAccountDetail;
import com.subskribe.billy.graphql.payment.PaymentMethodDetail;
import com.subskribe.billy.graphql.payment.RefundPreviewDetail;
import com.subskribe.billy.graphql.payment.SettlementApplicationDetail;
import com.subskribe.billy.graphql.payment.SettlementApplicationDetailMapper;
import com.subskribe.billy.graphql.plangroup.PlanGroupInterfaceMapper;
import com.subskribe.billy.graphql.productcatalog.ChargeDataAggregator;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.graphql.productcatalog.ChargeResolverEntityCache;
import com.subskribe.billy.graphql.productcatalog.PlanDataAggregator;
import com.subskribe.billy.graphql.productcatalog.PlanDetail;
import com.subskribe.billy.graphql.productcatalog.PlanMinimal;
import com.subskribe.billy.graphql.productcatalog.ProductDataAggregator;
import com.subskribe.billy.graphql.productcatalog.ProductDetail;
import com.subskribe.billy.graphql.productcatalog.ProductDetailMapper;
import com.subskribe.billy.graphql.refund.RefundDataAggregator;
import com.subskribe.billy.graphql.subscription.SubscriptionChargeRecognition;
import com.subskribe.billy.graphql.subscription.SubscriptionDataAggregator;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.graphql.taxrate.UpsertTaxRateMapper;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrder;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrderMapper;
import com.subskribe.billy.graphql.template.DocumentTemplateMapper;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.graphql.tenant.TenantDetailsMapper;
import com.subskribe.billy.graphql.user.UserDataAggregator;
import com.subskribe.billy.hubspot.model.HubSpotIntegration;
import com.subskribe.billy.hubspot.model.HubSpotSetupMessage;
import com.subskribe.billy.hubspot.service.HubSpotContactService;
import com.subskribe.billy.hubspot.service.HubSpotGetService;
import com.subskribe.billy.hubspot.service.HubSpotInstallService;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.hubspot.service.HubSpotJobQueueService;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.integration.IntegrationTargetService;
import com.subskribe.billy.integration.model.Integration;
import com.subskribe.billy.integration.model.IntegrationDetail;
import com.subskribe.billy.integration.model.IntegrationRequest;
import com.subskribe.billy.integration.service.IntegrationAuthFacadeService;
import com.subskribe.billy.integration.service.IntegrationFacadeService;
import com.subskribe.billy.integration.service.MergeIntegrationService;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRun;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunInput;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunItem;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunSelector;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceSelectorService;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceService;
import com.subskribe.billy.invoice.model.BillingSchedule;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceChargeInclusionOption;
import com.subskribe.billy.invoice.model.InvoiceGenerationMethod;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.UpdateInvoiceRequest;
import com.subskribe.billy.invoice.service.AccountInvoiceService;
import com.subskribe.billy.invoice.service.BillingScheduleService;
import com.subskribe.billy.invoice.service.InvoiceConfigurationService;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.service.VoidInvoiceService;
import com.subskribe.billy.invoicedunning.model.DunningReminderType;
import com.subskribe.billy.invoicedunning.model.InvoiceDunning;
import com.subskribe.billy.invoicedunning.services.InvoiceDunningService;
import com.subskribe.billy.invoicesettlement.document.CreditMemoDocumentDataAggregator;
import com.subskribe.billy.invoicesettlement.document.CreditMemoDocumentJson;
import com.subskribe.billy.invoicesettlement.document.render.CreditMemoTemplateData;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.model.CreditMemoStatus;
import com.subskribe.billy.invoicesettlement.model.Refund;
import com.subskribe.billy.invoicesettlement.model.RefundPreview;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.services.CreditMemoDocumentService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoRetrievalService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoService;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.invoicesettlement.services.RefundService;
import com.subskribe.billy.looker.LookerService;
import com.subskribe.billy.looker.model.LookerUserMapping;
import com.subskribe.billy.notification.gql.NotificationInstanceWithAttempts;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationProcessorTypeSupport;
import com.subskribe.billy.notification.model.NotificationTargetAndSubscriptions;
import com.subskribe.billy.notification.service.NotificationRetryService;
import com.subskribe.billy.notification.service.NotificationService;
import com.subskribe.billy.notification.service.NotificationTargetService;
import com.subskribe.billy.notification.service.WebhookSigningKeyService;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.EvergreenUtils;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderSource;
import com.subskribe.billy.order.quotebuilder.model.Answer;
import com.subskribe.billy.order.quotebuilder.model.ImmutableBuildOrderFromAnswersRequest;
import com.subskribe.billy.order.quotebuilder.service.QuoteBuilderService;
import com.subskribe.billy.order.services.ChangeOrderService;
import com.subskribe.billy.order.services.OrderDocumentService;
import com.subskribe.billy.order.services.OrderDocumentTemplateService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderPdfGenerationTrackerService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.order.services.OrderServiceHelper;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationService;
import com.subskribe.billy.payment.link.service.PaymentLinkService;
import com.subskribe.billy.payment.model.AccountPaymentLinkGqlResponse;
import com.subskribe.billy.payment.model.AccountPaymentLinkResponse;
import com.subskribe.billy.payment.model.AccountPaymentManagementLink;
import com.subskribe.billy.payment.model.AddPaymentMethodActionType;
import com.subskribe.billy.payment.model.InvoicePaymentManagementLink;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.model.PaymentConfiguration;
import com.subskribe.billy.payment.model.PaymentIntegration;
import com.subskribe.billy.payment.model.PaymentLinkGqlResponse;
import com.subskribe.billy.payment.model.PaymentLinkResponse;
import com.subskribe.billy.payment.model.PaymentLinkSourceType;
import com.subskribe.billy.payment.model.PaymentManagementLink;
import com.subskribe.billy.payment.model.PaymentManagementLinkType;
import com.subskribe.billy.payment.services.AccountPaymentManagementLinkService;
import com.subskribe.billy.payment.services.InvoicePaymentManagementLinkService;
import com.subskribe.billy.payment.services.LinkBasedPaymentService;
import com.subskribe.billy.payment.services.PaymentBankAccountService;
import com.subskribe.billy.payment.services.PaymentConfigurationService;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegrationSetupResponse;
import com.subskribe.billy.payment.stripe.model.StripeIntentResponse;
import com.subskribe.billy.payment.stripe.model.StripeSetupIntentResponse;
import com.subskribe.billy.payment.stripe.service.StripeService;
import com.subskribe.billy.paymentterms.model.PaymentTermSettings;
import com.subskribe.billy.paymentterms.services.PaymentTermSettingsService;
import com.subskribe.billy.platformfeature.model.EnabledPlatformFeature;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.productcatalog.model.TaxRate;
import com.subskribe.billy.productcatalog.model.UnitOfMeasure;
import com.subskribe.billy.productcatalog.plangroup.model.ImmutablePlanGroup;
import com.subskribe.billy.productcatalog.plangroup.model.PlanGroup;
import com.subskribe.billy.productcatalog.plangroup.model.gql.PlanGroupInterface;
import com.subskribe.billy.productcatalog.plangroup.service.PlanGroupService;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.ChargeDefaultAttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ChargeService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.productcatalog.services.TaxRateService;
import com.subskribe.billy.productcatalog.services.UnitOfMeasureService;
import com.subskribe.billy.proposal.model.CreateProposalRequest;
import com.subskribe.billy.proposal.model.Proposal;
import com.subskribe.billy.proposal.service.ProposalService;
import com.subskribe.billy.report.service.ReportService;
import com.subskribe.billy.resources.json.account.AccountMapper;
import com.subskribe.billy.resources.json.accountreceivablecontact.AccountReceivableContactJsonMapper;
import com.subskribe.billy.resources.json.approvalflow.ApprovalFlowMapper;
import com.subskribe.billy.resources.json.approvalflowinstance.ApprovalFlowSubmitterNoteJson;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalFlowHierarchyMapper;
import com.subskribe.billy.resources.json.creditmemo.StandaloneCreditMemoRequest;
import com.subskribe.billy.resources.json.customfield.CustomFieldDefinitionJsonMapper;
import com.subskribe.billy.resources.json.discount.DiscountJsonMapper;
import com.subskribe.billy.resources.json.dunningsetting.DunningSettingJsonMapper;
import com.subskribe.billy.resources.json.entity.EntityJsonMapper;
import com.subskribe.billy.resources.json.escalationpolicy.AddEscalationPolicyRequest;
import com.subskribe.billy.resources.json.escalationpolicy.EscalationPolicyJsonMapper;
import com.subskribe.billy.resources.json.escalationpolicy.UpdateEscalationPolicyRequest;
import com.subskribe.billy.resources.json.invoice.InvoicePreviewJson;
import com.subskribe.billy.resources.json.invoice.InvoicePreviewJsonMapper;
import com.subskribe.billy.resources.json.invoicesettlement.RefundDetail;
import com.subskribe.billy.resources.json.invoicesettlement.RefundDetailMapper;
import com.subskribe.billy.resources.json.opportunity.OpportunityInput;
import com.subskribe.billy.resources.json.opportunity.OpportunityMapper;
import com.subskribe.billy.resources.json.order.CompositeOrderMapper;
import com.subskribe.billy.resources.json.order.OrderAttributesUpdateRequest;
import com.subskribe.billy.resources.json.order.OrderInvoicePreviewResolver;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.resources.json.payment.PaymentBankAccountJson;
import com.subskribe.billy.resources.json.payment.PaymentBankAccountJsonMapper;
import com.subskribe.billy.resources.json.paymenttermsettings.PaymentTermSettingsJson;
import com.subskribe.billy.resources.json.paymenttermsettings.PaymentTermSettingsJsonMapper;
import com.subskribe.billy.resources.json.plan.ChargePartialJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.resources.json.report.PredefinedReportDefsJson;
import com.subskribe.billy.resources.json.report.PredefinedReportJson;
import com.subskribe.billy.resources.json.report.ReportJobResponse;
import com.subskribe.billy.resources.json.salesforce.SalesforceClientIntegrationRequestJson;
import com.subskribe.billy.resources.json.subscription.SubscriptionUpdateJson;
import com.subskribe.billy.resources.json.taxrate.TaxRateJsonMapper;
import com.subskribe.billy.resources.json.tenant.TenantCreationResponseJson;
import com.subskribe.billy.resources.json.tenant.TenantMapper;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.resources.json.tenant.UserMapper;
import com.subskribe.billy.resources.json.tenantsetting.TenantSettingJsonMapper;
import com.subskribe.billy.resources.json.uom.UnitOfMeasureJsonMapper;
import com.subskribe.billy.resources.json.usergroup.UserGroupMapper;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.revrec.services.RevenueRecognitionService;
import com.subskribe.billy.salesforce.model.SalesforceIntegrationResponse;
import com.subskribe.billy.salesforce.model.account.SalesforceAccount;
import com.subskribe.billy.salesforce.model.contact.SubskribeSalesforceContact;
import com.subskribe.billy.salesforce.model.contact.UpsertCRMContactResponse;
import com.subskribe.billy.salesforce.service.SalesforceContactService;
import com.subskribe.billy.salesforce.service.SalesforceGetService;
import com.subskribe.billy.salesforce.service.SalesforceIntegrationService;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.salesforce.service.SalesforceLoginService;
import com.subskribe.billy.salesroom.model.SalesRoom;
import com.subskribe.billy.salesroom.model.SalesRoomLink;
import com.subskribe.billy.salesroom.service.SalesRoomLinkService;
import com.subskribe.billy.salesroom.service.SalesRoomService;
import com.subskribe.billy.settings.billingcycle.json.BillingCycleDefinitionJson;
import com.subskribe.billy.settings.billingcycle.json.BillingCycleDefinitionJsonMapper;
import com.subskribe.billy.settings.billingcycle.model.BillingCycleDefinition;
import com.subskribe.billy.settings.billingcycle.service.BillingCycleDefinitionService;
import com.subskribe.billy.shared.enums.AccountType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.enums.SigningOrder;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.shared.utility.UUIDConverter;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.taxjar.model.TaxJarIntegration;
import com.subskribe.billy.taxjar.model.TaxJarIntegrationInput;
import com.subskribe.billy.taxjar.service.TaxJarIntegrationGetService;
import com.subskribe.billy.taxjar.service.TaxJarIntegrationService;
import com.subskribe.billy.template.model.CustomTemplateUpdateOnOrder;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.model.DocumentMasterTemplateStatus;
import com.subskribe.billy.template.model.DocumentSection;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.model.DocumentTemplateStatus;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderGetService;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.DocumentTemplateService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.Tenant;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenant.settings.accountreceivablecontact.services.AccountReceivableContactService;
import com.subskribe.billy.tenant.settings.dunningsettings.model.DunningSetting;
import com.subskribe.billy.tenant.settings.dunningsettings.services.DunningSettingService;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.tenantjob.service.TenantJobGetService;
import com.subskribe.billy.usage.model.RawUsage;
import com.subskribe.billy.usage.model.UsageAggregateOutput;
import com.subskribe.billy.usage.model.UsageBatchInsertResult;
import com.subskribe.billy.usage.service.UsageService;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.usergroup.model.UserGroup;
import com.subskribe.billy.usergroup.service.UserGroupService;
import com.subskribe.billy.validation.Validator;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.glassfish.hk2.api.Immediate;
import org.mapstruct.factory.Mappers;

@SuppressWarnings({ "deprecation,SameParameterValue", "OptionalUsedAsFieldOrParameterType" })
@Immediate
public class GqlAuthContext {

    private static final Logger LOGGER = LoggerFactory.getLogger(GqlAuthContext.class);

    private final TenantService tenantService;

    private final ProductCatalogService productCatalogService;
    private final ProductCatalogGetService productCatalogGetService;

    private final AccountService accountService;

    private final AccountGetService accountGetService;

    private final AccountDataAggregator accountDataAggregator;

    private final AmendmentOrderMapper amendmentOrderMapper;

    private final OrderService orderService;

    private final OrderGetService orderGetService;

    private final ChangeOrderService changeOrderService;

    private final CurrentUserProvider currentUserProvider;

    private final OpportunityMapper opportunityMapper;

    private final OpportunityGetService opportunityGetService;

    private final PlanMapper planMapper;

    private final TenantMapper tenantMapper;

    private final AccountMapper accountMapper;

    private final OrderMapper orderMapper;

    private final ElasticSearchService elasticSearchService;

    private final TenantSettingService tenantSettingService;

    private final TenantSettingJsonMapper tenantSettingJsonMapper;

    private final InvoiceService invoiceService;

    private final OrderDataAggregator orderDataAggregator;

    private final SubscriptionDataAggregator subscriptionDataAggregator;

    private final SubscriptionService subscriptionService;

    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;

    private final InvoiceDataAggregator invoiceDataAggregator;

    private final CreditMemoDataAggregator creditMemoDataAggregator;

    private final UserService userService;

    private final PlanDataAggregator planDataAggregator;

    private final ChargeDataAggregator chargeDataAggregator;

    private final TaxRateService taxRateService;

    private final TaxRateGetService taxRateGetService;

    private final TaxRateJsonMapper taxRateJsonMapper;

    private final UnitOfMeasureService unitOfMeasureService;

    private final UnitOfMeasureJsonMapper unitOfMeasureJsonMapper;

    private final UserMapper userMapper;

    private final UserGroupService userGroupService;

    private final UserGroupMapper userGroupMapper;

    private final AccountContactDataAggregator accountContactDataAggregator;

    private final SalesforceGetService salesforceGetService;

    private final SalesforceIntegrationService salesforceIntegrationService;

    private final PaymentDataAggregator paymentDataAggregator;

    private final InvoiceSettlementService invoiceSettlementService;

    private final SettlementApplicationDetailMapper settlementApplicationDetailMapper;

    private final NotificationTargetService notificationTargetService;

    private final NotificationService notificationService;

    private final NotificationRetryService notificationRetryService;

    private final WebhookSigningKeyService webhookSigningKeyService;

    private final UpsertTaxRateMapper upsertTaxRateMapper;

    private final AvalaraIntegrationService avalaraIntegrationService;

    private final AvalaraIntegrationGetService avalaraIntegrationGetService;

    private final SalesforceLoginService salesforceLoginService;

    private final SalesforceJobQueueService salesforceJobQueueService;

    private final SalesforceContactService salesforceContactService;

    private final TenantJobGetService tenantJobGetService;

    private final TenantJobDispatcherService tenantJobDispatcherService;

    private final TenantIdProvider tenantIdProvider;

    private final ApiKeyService apiKeyService;

    private final TenantDetailsMapper tenantDetailsMapper;

    private final DocumentTemplateMapper documentTemplateMapper;

    private final DocumentTemplateService documentTemplateService;

    private final DocumentTemplateGetService documentTemplateGetService;

    private final InvoiceEmailService invoiceEmailService;

    private final EmailContactListService emailContactListService;

    private final DocuSignAuthService docuSignAuthenticationService;

    private final IntegrationAuthFacadeService integrationAuthFacadeService;
    private final IntegrationFacadeService integrationFacadeService;

    private final MergeIntegrationService mergeIntegrationService;

    private final ErpSyncQueueService erpSyncQueueService;
    private final ErpSyncService erpSyncService;

    private final ErpIntegrationService erpIntegrationService;

    private final DocuSignMapper docuSignMapper;

    private final AccountReceivableContactService accountReceivableContactService;

    private final AccountReceivableContactJsonMapper accountReceivableContactJsonMapper;

    private final InvoiceDunningService invoiceDunningService;

    private final InvoiceDunningDetailMapper invoiceDunningDetailMapper;

    private final DunningSettingService dunningSettingService;

    private final DunningSettingJsonMapper dunningSettingJsonMapper;

    private final ReportService reportService;

    private final DiscountService discountService;

    private final DiscountJsonMapper discountJsonMapper;

    private final PaymentIntegrationService paymentIntegrationService;

    private final StripeService stripeService;

    private final PaymentLinkService paymentLinkService;

    private final ApprovalFlowDataAggregator approvalFlowDataAggregator;

    private final ApprovalFlowService approvalFlowService;

    private final ApprovalFlowMapper approvalFlowMapper;

    private final ApprovalFlowInstanceService approvalFlowInstanceService;

    private final ApprovalFlowStateUpdaterService approvalFlowStateUpdaterService;

    private final BulkInvoiceService bulkInvoiceService;

    private final ApprovalFlowPendingUserActionService approvalFlowPendingUserActionService;

    private final RevenueRecognitionService revenueRecognitionService;

    private final RevenueRecognitionGetService revenueRecognitionGetService;

    private final CreditMemoService creditMemoService;

    private final InvoiceRetrievalService invoiceRetrievalService;
    private final CreditMemoRetrievalService creditMemoRetrievalService;

    private final BillyConfiguration billyConfiguration;

    private final FeatureService featureService;

    private final AccountingService accountingService;
    private final AccountingGetService accountingGetService;

    private final AccountingPeriodService accountingPeriodService;

    private final AccountingPeriodCalculationService accountingPeriodCalculationService;

    private final AccountingPeriodStatusUpdateService accountingPeriodStatusUpdateService;

    private final CognitoService cognitoService;

    private final CreditMemoEmailService creditMemoEmailService;

    private final CreditMemoDocumentDataAggregator creditMemoDocumentDataAggregator;

    private final CreditMemoDocumentService creditMemoDocumentService;

    private final AliasService aliasService;

    private final BulkInvoiceSelectorService bulkInvoiceSelectorService;

    private final ApprovalFlowHierarchyService approvalFlowHierarchyService;

    private final ApprovalFlowHierarchyMapper approvalFlowHierarchyMapper;

    private final PaymentConfigurationService paymentConfigurationService;

    private final RefundService refundService;

    private final RefundDetailMapper refundDetailMapper;

    private final HubSpotIntegrationService hubSpotIntegrationService;

    private final HubSpotService hubSpotService;

    private final HubSpotGetService hubSpotGetService;

    private final HubSpotInstallService hubSpotInstallService;

    private final PaymentIntegrationGetService paymentIntegrationGetService;

    private final RefundDataAggregator refundDataAggregator;

    private final CrmIntegrationService crmIntegrationService;

    private final UserDataAggregator userDataAggregator;

    private final UsageService usageService;

    private final AttachmentsService attachmentsService;

    private final PlatformFeatureService platformFeatureService;

    private final JournalEntryService journalEntryService;

    private final OrderDocumentService orderDocumentService;

    private final AccountInvoiceService accountInvoiceService;

    private final CustomFieldService customFieldService;

    private final CustomFieldDefinitionJsonMapper customFieldDefinitionJsonMapper;

    private final CustomFieldAPIMapper customFieldAPIMapper;

    private final EmailService emailService;

    private final PaymentGetService paymentGetService;

    private final VoidInvoiceService voidInvoiceService;

    private final CustomFieldProxy customFieldProxy;

    private final ApiKeyDetailMapper apiKeyDetailMapper;

    private final ProductDataAggregator productDataAggregator;

    private final ProductDetailMapper productDetailMapper;

    private final PaymentTermSettingsService paymentTermSettingsService;

    private final PaymentTermSettingsJsonMapper paymentTermSettingsJsonMapper;

    private final CompositeOrderService compositeOrderService;
    private final CompositeOrderGetService compositeOrderGetService;
    private final CompositeOrderDocumentService compositeOrderDocumentService;
    private final CompositeOrderMapper compositeOrderMapper;

    private final EsignService esignService;

    private final CrmContactMapper crmContactMapper;

    private final HubSpotContactService hubSpotContactService;

    private final CrmService crmService;

    private final PlanGroupService planGroupService;

    private final PlanGroupInterfaceMapper planGroupInterfaceMapper;

    private final ProposalService proposalService;

    private final DocumentLinkService documentLinkService;

    private final LinkBasedPaymentService linkBasedPaymentService;

    private final InvoicePaymentManagementLinkService invoicePaymentManagementLinkService;

    private final AccountPaymentManagementLinkService accountPaymentManagementLinkService;

    private final EscalationPolicyService escalationPolicyService;

    private final EscalationPolicyJsonMapper escalationPolicyJsonMapper;

    private final BillingScheduleService billingScheduleService;

    private final BillingEventMapper billingEventMapper;

    private final CLMService clmService;

    private final EmailLinkLoginService emailLinkLoginService;

    private final RateCardService rateCardService;

    private final EntityGetService entityGetService;

    private final InvoiceConfigurationService invoiceConfigurationService;

    private final EntityService entityService;

    private final SettlementApplicationDataAggregator settlementApplicationDataAggregator;

    private final TaxJarIntegrationService taxJarIntegrationService;

    private final TaxJarIntegrationGetService taxJarIntegrationGetService;

    private final DataImportService dataImportService;

    private final FlatfileService flatfileService;

    private final EsignDataAggregator esignDataAggregator;

    private final CustomizationService customizationService;

    private final LookerService lookerService;

    private final EsignIntegrationService esignIntegrationService;

    private final CustomTemplateUpdatedOnOrderService customTemplateUpdatedOnOrderService;

    private final CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService;

    private final CustomPredefinedTemplateOnOrderMapper customPredefinedTemplateOnOrderMapper;

    private final SalesRoomLinkService salesRoomLinkService;

    private final SalesRoomService salesRoomService;

    private final CancelAndRestructureOrderDataAggregator cancelAndRestructureOrderDataAggregator;

    private final UIConfigurationService uiConfigurationService;

    private final OrderPdfGenerationTrackerService orderPdfGenerationTrackerService;

    private final EntityContextProvider entityContextProvider;

    private final EntityJsonMapper entityJsonMapper;

    private final HubSpotJobQueueService hubSpotJobQueueService;

    private final AdminOperationsService adminOperationsService;

    private final OpportunityService opportunityService;

    private final AnrokIntegrationService anrokIntegrationService;

    private final AnrokIntegrationGetService anrokIntegrationGetService;

    private final CurrencyTypeSettingService currencyTypeSettingService;

    private final CurrencyTypeSettingGetService currencyTypeSettingGetService;

    private final CurrencyConversionRateService currencyConversionRateService;

    private final CurrencyConversionRateGetService currencyConversionRateGetService;

    private final CatalogRelationshipGetService catalogRelationshipGetService;

    private final InvoicePreviewJsonMapper invoicePreviewJsonMapper;

    private final PaymentBankAccountService paymentBankAccountService;

    private final PaymentBankAccountJsonMapper paymentBankAccountJsonMapper;

    private final EmailSettingService emailSettingService;

    private final QuoteBuilderService quoteBuilderService;

    private final ChargeService chargeService;

    private final OrderDocumentTemplateService orderDocumentTemplateService;

    private final BillingCycleDefinitionService billingCycleDefinitionService;

    private final BillingCycleDefinitionJsonMapper billingCycleDefinitionJsonMapper;

    @Inject
    public GqlAuthContext(
        TenantService tenantService,
        ProductCatalogService productCatalogService,
        ProductCatalogGetService productCatalogGetService,
        AccountService accountService,
        AccountGetService accountGetService,
        AccountDataAggregator accountDataAggregator,
        OrderService orderService,
        OrderGetService orderGetService,
        ChangeOrderService changeOrderService,
        CurrentUserProvider currentUserProvider,
        OpportunityGetService opportunityGetService,
        ElasticSearchService elasticSearchService,
        TenantSettingService tenantSettingService,
        InvoiceService invoiceService,
        OrderDataAggregator orderDataAggregator,
        SubscriptionDataAggregator subscriptionDataAggregator,
        SubscriptionService subscriptionService,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        InvoiceDataAggregator invoiceDataAggregator,
        CreditMemoDataAggregator creditMemoDataAggregator,
        UserService userService,
        PlanDataAggregator planDataAggregator,
        ChargeDataAggregator chargeDataAggregator,
        TaxRateService taxRateService,
        TaxRateGetService taxRateGetService,
        UnitOfMeasureService unitOfMeasureService,
        AccountContactDataAggregator accountContactDataAggregator,
        SalesforceGetService salesforceGetService,
        SalesforceIntegrationService salesforceIntegrationService,
        PaymentDataAggregator paymentDataAggregator,
        InvoiceSettlementService invoiceSettlementService,
        NotificationTargetService notificationTargetService,
        NotificationService notificationService,
        NotificationRetryService notificationRetryService,
        WebhookSigningKeyService webhookSigningKeyService,
        AvalaraIntegrationService avalaraIntegrationService,
        AvalaraIntegrationGetService avalaraIntegrationGetService,
        SalesforceLoginService salesforceLoginService,
        SalesforceContactService salesforceContactService,
        SalesforceJobQueueService salesforceJobQueueService,
        TenantJobGetService tenantJobGetService,
        TenantJobDispatcherService tenantJobDispatcherService,
        TenantIdProvider tenantIdProvider,
        ApiKeyService apiKeyService,
        DocumentTemplateService documentTemplateService,
        DocumentTemplateGetService documentTemplateGetService,
        InvoiceEmailService invoiceEmailService,
        EmailContactListService emailContactListService,
        DocuSignAuthService docuSignAuthenticationService,
        IntegrationAuthFacadeService integrationAuthFacadeService,
        MergeIntegrationService mergeIntegrationService,
        ErpSyncQueueService erpSyncQueueService,
        AccountReceivableContactService accountReceivableContactService,
        InvoiceDunningService invoiceDunningService,
        DunningSettingService dunningSettingService,
        ReportService reportService,
        DiscountService discountService,
        PaymentIntegrationService paymentIntegrationService,
        StripeService stripeService,
        PaymentLinkService paymentLinkService,
        UserGroupService userGroupService,
        IntegrationFacadeService integrationFacadeService,
        ErpSyncService erpSyncService,
        ErpIntegrationService erpIntegrationService,
        ApprovalFlowService approvalFlowService,
        ApprovalFlowDataAggregator approvalFlowDataAggregator,
        ApprovalFlowInstanceService approvalFlowInstanceService,
        ApprovalFlowStateUpdaterService approvalFlowStateUpdaterService,
        BulkInvoiceService bulkInvoiceService,
        RevenueRecognitionService revenueRecognitionService,
        RevenueRecognitionGetService revenueRecognitionGetService,
        ApprovalFlowPendingUserActionService approvalFlowPendingUserActionService,
        CreditMemoService creditMemoService,
        InvoiceRetrievalService invoiceRetrievalService,
        CreditMemoRetrievalService creditMemoRetrievalService,
        BillyConfiguration billyConfiguration,
        FeatureService featureService,
        AccountingService accountingService,
        AccountingGetService accountingGetService,
        AccountingPeriodService accountingPeriodService,
        AccountingPeriodCalculationService accountingPeriodCalculationService,
        AccountingPeriodStatusUpdateService accountingPeriodStatusUpdateService,
        CognitoService cognitoService,
        CreditMemoEmailService creditMemoEmailService,
        CreditMemoDocumentDataAggregator creditMemoDocumentDataAggregator,
        CreditMemoDocumentService creditMemoDocumentService,
        AliasService aliasService,
        BulkInvoiceSelectorService bulkInvoiceSelectorService,
        ApprovalFlowHierarchyService approvalFlowHierarchyService,
        PaymentConfigurationService paymentConfigurationService,
        RefundService refundService,
        HubSpotIntegrationService hubSpotIntegrationService,
        HubSpotService hubSpotService,
        HubSpotGetService hubSpotGetService,
        HubSpotInstallService hubSpotInstallService,
        PaymentIntegrationGetService paymentIntegrationGetService,
        RefundDataAggregator refundDataAggregator,
        CrmIntegrationService crmIntegrationService,
        UserDataAggregator userDataAggregator,
        UsageService usageService,
        AttachmentsService attachmentsService,
        PlatformFeatureService platformFeatureService,
        JournalEntryService journalEntryService,
        OrderDocumentService orderDocumentService,
        AccountInvoiceService accountInvoiceService,
        CustomFieldService customFieldService,
        EmailService emailService,
        PaymentGetService paymentGetService,
        VoidInvoiceService voidInvoiceService,
        CustomFieldProxy customFieldProxy,
        ProductDataAggregator productDataAggregator,
        PaymentTermSettingsService paymentTermSettingsService,
        CompositeOrderService compositeOrderService,
        CompositeOrderGetService compositeOrderGetService,
        CompositeOrderDocumentService compositeOrderDocumentService,
        EsignService esignService,
        HubSpotContactService hubSpotContactService,
        CrmService crmService,
        PlanGroupService planGroupService,
        ProposalService proposalService,
        DocumentLinkService documentLinkService,
        LinkBasedPaymentService linkBasedPaymentService,
        InvoicePaymentManagementLinkService invoicePaymentManagementLinkService,
        AccountPaymentManagementLinkService accountPaymentManagementLinkService,
        BillingScheduleService billingScheduleService,
        EscalationPolicyService escalationPolicyService,
        CLMService clmService,
        EmailLinkLoginService emailLinkLoginService,
        RateCardService rateCardService,
        EntityGetService entityGetService,
        InvoiceConfigurationService invoiceConfigurationService,
        EntityService entityService,
        SettlementApplicationDataAggregator settlementApplicationDataAggregator,
        TaxJarIntegrationService taxJarIntegrationService,
        TaxJarIntegrationGetService taxJarIntegrationGetService,
        DataImportService dataImportService,
        FlatfileService flatfileService,
        EsignDataAggregator esignDataAggregator,
        CustomizationService customizationService,
        LookerService lookerService,
        EsignIntegrationService esignIntegrationService,
        CustomTemplateUpdatedOnOrderService customTemplateUpdatedOnOrderService,
        CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService,
        SalesRoomLinkService salesRoomLinkService,
        SalesRoomService salesRoomService,
        CancelAndRestructureOrderDataAggregator cancelAndRestructureOrderDataAggregator,
        UIConfigurationService uiConfigurationService,
        OrderPdfGenerationTrackerService orderPdfGenerationTrackerService,
        EntityContextProvider entityContextProvider,
        HubSpotJobQueueService hubSpotJobQueueService,
        AdminOperationsService adminOperationsService,
        OpportunityService opportunityService,
        AnrokIntegrationService anrokIntegrationService,
        AnrokIntegrationGetService anrokIntegrationGetService,
        CurrencyTypeSettingService currencyTypeSettingService,
        CurrencyTypeSettingGetService currencyTypeSettingGetService,
        CurrencyConversionRateService currencyConversionRateService,
        CurrencyConversionRateGetService currencyConversionRateGetService,
        CatalogRelationshipGetService catalogRelationshipGetService,
        PaymentBankAccountService paymentBankAccountService,
        EmailSettingService emailSettingService,
        QuoteBuilderService quoteBuilderService,
        ChargeService chargeService,
        OrderDocumentTemplateService orderDocumentTemplateService,
        BillingCycleDefinitionService billingCycleDefinitionService
    ) {
        this.tenantService = tenantService;
        this.productCatalogService = productCatalogService;
        this.productCatalogGetService = productCatalogGetService;
        this.accountService = accountService;
        this.accountGetService = accountGetService;
        this.accountDataAggregator = accountDataAggregator;
        this.orderService = orderService;
        this.orderGetService = orderGetService;
        this.changeOrderService = changeOrderService;
        this.currentUserProvider = currentUserProvider;
        this.opportunityGetService = opportunityGetService;
        this.elasticSearchService = elasticSearchService;
        this.tenantSettingService = tenantSettingService;
        this.orderDataAggregator = orderDataAggregator;
        this.subscriptionDataAggregator = subscriptionDataAggregator;
        this.subscriptionService = subscriptionService;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.invoiceDataAggregator = invoiceDataAggregator;
        this.creditMemoDataAggregator = creditMemoDataAggregator;
        this.userService = userService;
        this.planDataAggregator = planDataAggregator;
        this.chargeDataAggregator = chargeDataAggregator;
        this.taxRateService = taxRateService;
        this.taxRateGetService = taxRateGetService;
        this.unitOfMeasureService = unitOfMeasureService;
        this.accountContactDataAggregator = accountContactDataAggregator;
        this.invoiceService = invoiceService;
        this.salesforceGetService = salesforceGetService;
        this.salesforceIntegrationService = salesforceIntegrationService;
        this.paymentDataAggregator = paymentDataAggregator;
        this.invoiceSettlementService = invoiceSettlementService;
        this.notificationTargetService = notificationTargetService;
        this.notificationService = notificationService;
        this.notificationRetryService = notificationRetryService;
        this.webhookSigningKeyService = webhookSigningKeyService;
        this.avalaraIntegrationService = avalaraIntegrationService;
        this.avalaraIntegrationGetService = avalaraIntegrationGetService;
        this.salesforceLoginService = salesforceLoginService;
        this.salesforceContactService = salesforceContactService;
        this.salesforceJobQueueService = salesforceJobQueueService;
        this.tenantJobGetService = tenantJobGetService;
        this.tenantJobDispatcherService = tenantJobDispatcherService;
        this.tenantIdProvider = tenantIdProvider;
        this.apiKeyService = apiKeyService;
        this.documentTemplateService = documentTemplateService;
        this.documentTemplateGetService = documentTemplateGetService;
        this.invoiceEmailService = invoiceEmailService;
        this.emailContactListService = emailContactListService;
        this.docuSignAuthenticationService = docuSignAuthenticationService;
        this.integrationAuthFacadeService = integrationAuthFacadeService;
        this.mergeIntegrationService = mergeIntegrationService;
        this.erpSyncQueueService = erpSyncQueueService;
        this.accountReceivableContactService = accountReceivableContactService;
        this.invoiceDunningService = invoiceDunningService;
        this.dunningSettingService = dunningSettingService;
        this.reportService = reportService;
        this.discountService = discountService;
        this.paymentIntegrationService = paymentIntegrationService;
        this.stripeService = stripeService;
        this.paymentLinkService = paymentLinkService;
        this.userGroupService = userGroupService;
        this.integrationFacadeService = integrationFacadeService;
        this.erpSyncService = erpSyncService;
        this.erpIntegrationService = erpIntegrationService;
        this.approvalFlowService = approvalFlowService;
        this.approvalFlowDataAggregator = approvalFlowDataAggregator;
        this.approvalFlowInstanceService = approvalFlowInstanceService;
        this.approvalFlowStateUpdaterService = approvalFlowStateUpdaterService;
        this.bulkInvoiceService = bulkInvoiceService;
        this.revenueRecognitionService = revenueRecognitionService;
        this.revenueRecognitionGetService = revenueRecognitionGetService;
        this.approvalFlowPendingUserActionService = approvalFlowPendingUserActionService;
        this.creditMemoService = creditMemoService;
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.creditMemoRetrievalService = creditMemoRetrievalService;
        this.billyConfiguration = billyConfiguration;
        this.featureService = featureService;
        this.accountingService = accountingService;
        this.accountingGetService = accountingGetService;
        this.accountingPeriodService = accountingPeriodService;
        this.accountingPeriodCalculationService = accountingPeriodCalculationService;
        this.accountingPeriodStatusUpdateService = accountingPeriodStatusUpdateService;
        this.cognitoService = cognitoService;
        this.creditMemoEmailService = creditMemoEmailService;
        this.creditMemoDocumentDataAggregator = creditMemoDocumentDataAggregator;
        this.creditMemoDocumentService = creditMemoDocumentService;
        this.aliasService = aliasService;
        this.bulkInvoiceSelectorService = bulkInvoiceSelectorService;
        this.approvalFlowHierarchyService = approvalFlowHierarchyService;
        this.paymentConfigurationService = paymentConfigurationService;
        this.refundService = refundService;
        this.hubSpotIntegrationService = hubSpotIntegrationService;
        this.hubSpotService = hubSpotService;
        this.hubSpotGetService = hubSpotGetService;
        this.hubSpotInstallService = hubSpotInstallService;
        this.paymentIntegrationGetService = paymentIntegrationGetService;
        this.refundDataAggregator = refundDataAggregator;
        this.crmIntegrationService = crmIntegrationService;
        this.userDataAggregator = userDataAggregator;
        this.usageService = usageService;
        this.attachmentsService = attachmentsService;
        this.platformFeatureService = platformFeatureService;
        this.journalEntryService = journalEntryService;
        this.orderDocumentService = orderDocumentService;
        this.accountInvoiceService = accountInvoiceService;
        this.customFieldService = customFieldService;
        this.emailService = emailService;
        this.paymentGetService = paymentGetService;
        this.voidInvoiceService = voidInvoiceService;
        this.customFieldProxy = customFieldProxy;
        this.productDataAggregator = productDataAggregator;
        this.paymentTermSettingsService = paymentTermSettingsService;
        this.compositeOrderService = compositeOrderService;
        this.compositeOrderGetService = compositeOrderGetService;
        this.compositeOrderDocumentService = compositeOrderDocumentService;
        this.esignService = esignService;
        this.hubSpotContactService = hubSpotContactService;
        this.crmService = crmService;
        this.planGroupService = planGroupService;
        this.proposalService = proposalService;
        this.documentLinkService = documentLinkService;
        this.linkBasedPaymentService = linkBasedPaymentService;
        this.invoicePaymentManagementLinkService = invoicePaymentManagementLinkService;
        this.accountPaymentManagementLinkService = accountPaymentManagementLinkService;
        this.billingScheduleService = billingScheduleService;
        this.escalationPolicyService = escalationPolicyService;
        this.clmService = clmService;
        this.emailLinkLoginService = emailLinkLoginService;
        this.rateCardService = rateCardService;
        this.entityGetService = entityGetService;
        this.invoiceConfigurationService = invoiceConfigurationService;
        this.entityService = entityService;
        this.settlementApplicationDataAggregator = settlementApplicationDataAggregator;
        this.taxJarIntegrationService = taxJarIntegrationService;
        this.taxJarIntegrationGetService = taxJarIntegrationGetService;
        this.dataImportService = dataImportService;
        this.flatfileService = flatfileService;
        this.esignDataAggregator = esignDataAggregator;
        this.customizationService = customizationService;
        this.lookerService = lookerService;
        this.esignIntegrationService = esignIntegrationService;
        this.customTemplateUpdatedOnOrderService = customTemplateUpdatedOnOrderService;
        this.customTemplateUpdatedOnOrderGetService = customTemplateUpdatedOnOrderGetService;
        this.salesRoomLinkService = salesRoomLinkService;
        this.salesRoomService = salesRoomService;
        this.cancelAndRestructureOrderDataAggregator = cancelAndRestructureOrderDataAggregator;
        this.uiConfigurationService = uiConfigurationService;
        this.orderPdfGenerationTrackerService = orderPdfGenerationTrackerService;
        this.entityContextProvider = entityContextProvider;
        this.hubSpotJobQueueService = hubSpotJobQueueService;
        this.adminOperationsService = adminOperationsService;
        this.opportunityService = opportunityService;
        this.anrokIntegrationService = anrokIntegrationService;
        this.anrokIntegrationGetService = anrokIntegrationGetService;
        this.currencyTypeSettingService = currencyTypeSettingService;
        this.currencyTypeSettingGetService = currencyTypeSettingGetService;
        this.currencyConversionRateService = currencyConversionRateService;
        this.currencyConversionRateGetService = currencyConversionRateGetService;
        this.catalogRelationshipGetService = catalogRelationshipGetService;
        this.paymentBankAccountService = paymentBankAccountService;
        this.emailSettingService = emailSettingService;
        this.quoteBuilderService = quoteBuilderService;
        this.chargeService = chargeService;
        this.orderDocumentTemplateService = orderDocumentTemplateService;
        this.billingCycleDefinitionService = billingCycleDefinitionService;
        opportunityMapper = Mappers.getMapper(OpportunityMapper.class);
        planMapper = Mappers.getMapper(PlanMapper.class);
        tenantMapper = Mappers.getMapper(TenantMapper.class);
        accountMapper = Mappers.getMapper(AccountMapper.class);
        orderMapper = Mappers.getMapper(OrderMapper.class);
        tenantSettingJsonMapper = Mappers.getMapper(TenantSettingJsonMapper.class);
        taxRateJsonMapper = Mappers.getMapper(TaxRateJsonMapper.class);
        unitOfMeasureJsonMapper = Mappers.getMapper(UnitOfMeasureJsonMapper.class);
        userMapper = Mappers.getMapper(UserMapper.class);
        amendmentOrderMapper = Mappers.getMapper(AmendmentOrderMapper.class);
        settlementApplicationDetailMapper = Mappers.getMapper(SettlementApplicationDetailMapper.class);
        upsertTaxRateMapper = Mappers.getMapper(UpsertTaxRateMapper.class);
        tenantDetailsMapper = Mappers.getMapper(TenantDetailsMapper.class);
        documentTemplateMapper = Mappers.getMapper(DocumentTemplateMapper.class);
        docuSignMapper = Mappers.getMapper(DocuSignMapper.class);
        accountReceivableContactJsonMapper = Mappers.getMapper(AccountReceivableContactJsonMapper.class);
        dunningSettingJsonMapper = Mappers.getMapper(DunningSettingJsonMapper.class);
        discountJsonMapper = Mappers.getMapper(DiscountJsonMapper.class);
        userGroupMapper = Mappers.getMapper(UserGroupMapper.class);
        approvalFlowMapper = Mappers.getMapper(ApprovalFlowMapper.class);
        invoiceDunningDetailMapper = Mappers.getMapper(InvoiceDunningDetailMapper.class);
        approvalFlowHierarchyMapper = Mappers.getMapper(ApprovalFlowHierarchyMapper.class);
        refundDetailMapper = Mappers.getMapper(RefundDetailMapper.class);
        customFieldDefinitionJsonMapper = Mappers.getMapper(CustomFieldDefinitionJsonMapper.class);
        customFieldAPIMapper = Mappers.getMapper(CustomFieldAPIMapper.class);
        apiKeyDetailMapper = Mappers.getMapper(ApiKeyDetailMapper.class);
        productDetailMapper = Mappers.getMapper(ProductDetailMapper.class);
        paymentTermSettingsJsonMapper = Mappers.getMapper(PaymentTermSettingsJsonMapper.class);
        compositeOrderMapper = Mappers.getMapper(CompositeOrderMapper.class);
        crmContactMapper = Mappers.getMapper(CrmContactMapper.class);
        planGroupInterfaceMapper = Mappers.getMapper(PlanGroupInterfaceMapper.class);
        billingEventMapper = Mappers.getMapper(BillingEventMapper.class);
        escalationPolicyJsonMapper = Mappers.getMapper(EscalationPolicyJsonMapper.class);
        customPredefinedTemplateOnOrderMapper = Mappers.getMapper(CustomPredefinedTemplateOnOrderMapper.class);
        entityJsonMapper = Mappers.getMapper(EntityJsonMapper.class);
        invoicePreviewJsonMapper = Mappers.getMapper(InvoicePreviewJsonMapper.class);
        paymentBankAccountJsonMapper = Mappers.getMapper(PaymentBankAccountJsonMapper.class);
        billingCycleDefinitionJsonMapper = Mappers.getMapper(BillingCycleDefinitionJsonMapper.class);
    }

    @AllowAllRoles
    AmendmentOrderMapper getAmendmentOrderMapper() {
        return amendmentOrderMapper;
    }

    @AllowAllRoles
    OpportunityMapper getOpportunityMapper() {
        return opportunityMapper;
    }

    @AllowAllRoles
    PlanMapper getPlanMapper() {
        return planMapper;
    }

    @AllowAllRoles
    TenantMapper getTenantMapper() {
        return tenantMapper;
    }

    @AllowAllRoles
    AccountMapper getAccountMapper() {
        return accountMapper;
    }

    @AllowAllRoles
    OrderMapper getOrderMapper() {
        return orderMapper;
    }

    @AllowAllRoles
    TenantSettingJsonMapper getTenantSettingJsonMapper() {
        return tenantSettingJsonMapper;
    }

    @AllowAllRoles
    TaxRateJsonMapper getTaxRateJsonMapper() {
        return taxRateJsonMapper;
    }

    @AllowAllRoles
    UnitOfMeasureJsonMapper getUnitOfMeasureJsonMapper() {
        return unitOfMeasureJsonMapper;
    }

    @AllowAllRoles
    UserMapper getUserMapper() {
        return userMapper;
    }

    @AllowAllRoles
    SettlementApplicationDetailMapper getSettlementApplicationDetailMapper() {
        return settlementApplicationDetailMapper;
    }

    @AllowAllRoles
    UpsertTaxRateMapper getUpsertTaxRateMapper() {
        return upsertTaxRateMapper;
    }

    @AllowAllRoles
    TenantDetailsMapper getTenantDetailsMapper() {
        return tenantDetailsMapper;
    }

    @AllowAllRoles
    DocumentTemplateMapper getDocumentTemplateMapper() {
        return documentTemplateMapper;
    }

    @AllowAllRoles
    DocuSignMapper getDocuSignMapper() {
        return docuSignMapper;
    }

    @AllowAllRoles
    AccountReceivableContactJsonMapper getAccountReceivableContactJsonMapper() {
        return accountReceivableContactJsonMapper;
    }

    @AllowAllRoles
    InvoiceDunningDetailMapper getInvoiceDunningDetailMapper() {
        return invoiceDunningDetailMapper;
    }

    @AllowAllRoles
    DunningSettingJsonMapper getDunningSettingJsonMapper() {
        return dunningSettingJsonMapper;
    }

    @AllowAllRoles
    DiscountJsonMapper getDiscountJsonMapper() {
        return discountJsonMapper;
    }

    @AllowAllRoles
    UserGroupMapper getUserGroupMapper() {
        return userGroupMapper;
    }

    @AllowAllRoles
    ApprovalFlowDataAggregator getApprovalFlowDataAggregator() {
        return approvalFlowDataAggregator;
    }

    @AllowAllRoles
    ApprovalFlowMapper getApprovalFlowMapper() {
        return approvalFlowMapper;
    }

    @AllowAllRoles
    BillyConfiguration getBillyConfiguration() {
        return billyConfiguration;
    }

    @AllowAllRoles
    ApprovalFlowHierarchyMapper getApprovalFlowHierarchyMapper() {
        return approvalFlowHierarchyMapper;
    }

    @AllowAllRoles
    RefundDetailMapper getRefundDetailMapper() {
        return refundDetailMapper;
    }

    @AllowAllRoles
    UserDataAggregator getUserDataAggregator() {
        return userDataAggregator;
    }

    @AllowAllRoles
    CustomFieldDefinitionJsonMapper getCustomFieldDefinitionJsonMapper() {
        return customFieldDefinitionJsonMapper;
    }

    @AllowAllRoles
    EntityJsonMapper getEntityJsonMapper() {
        return entityJsonMapper;
    }

    @AllowAllRoles
    List<Order> getOrders(PaginationQueryParams paginationQueryParams) {
        return orderGetService.getOrders(paginationQueryParams, Optional.empty());
    }

    @AllowAllRoles
    Order getOrderByOrderId(String orderId) {
        return orderGetService.getOrderByOrderId(orderId);
    }

    @AllowAllRoles
    List<Order> getOrdersByAccountId(String accountId) {
        return orderGetService.getOrdersByAccountId(accountId);
    }

    @AllowAllRoles
    List<Order> getOrdersBySubscriptionId(String subscriptionId) {
        return orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);
    }

    @AllowAllRoles
    List<Order> getOrdersByCrmOpportunityId(String crmOpportunityId) {
        return orderGetService.getOrdersByCrmOpportunityId(crmOpportunityId);
    }

    @AllowAllRoles
    Map<String, List<Order>> getOrdersOverviewInPeriod(Period period, ZoneId timeZoneId) {
        return orderGetService.getOrdersOverviewInPeriod(period, timeZoneId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Order upsertOrder(Order order, Boolean isDryRun, Boolean sanitizeLineItemDates) {
        return orderService.upsertOrder(order, isDryRun, sanitizeLineItemDates);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void updateOrderStatus(String orderId, OrderStatus orderStatus, Long statusUpdatedOn, boolean adminApprovalByPass) {
        var orderStub = orderGetService.getOrderStubOrThrow(orderId);
        if (orderStub.getCompositeOrderId().isPresent()) {
            throw new IllegalArgumentException("Use composite order Gql mutation to change status of composite orders");
        }

        Optional<Instant> statusUpdatedOnInstant = Optional.ofNullable(statusUpdatedOn).map(Instant::ofEpochSecond);
        orderService.updateOrderStatus(orderId, orderStatus, statusUpdatedOnInstant, adminApprovalByPass);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void deleteOrder(String orderId) {
        orderService.deleteOrder(orderId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Order duplicateOrder(String orderId, String accountId) {
        return orderService.duplicateOrder(orderId, accountId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Order buildOrderFromAnswers(String accountId, String usecase, List<Answer> answers) {
        ImmutableBuildOrderFromAnswersRequest request = ImmutableBuildOrderFromAnswersRequest.builder()
            .accountId(accountId)
            .usecaseIdentifier(usecase)
            .answers(answers)
            .build();
        return quoteBuilderService.buildOrderFromAnswers(request);
    }

    @AllowRoles({ Role.ADMIN })
    OrderDetail rebaseAmendment(String orderId) {
        Order order = changeOrderService.rebaseAmendment(orderId);
        return getOrderDetail(order, false, false);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Order upsertChangeOrder(Order changeOrder, Boolean isDryRun, Boolean populateMissingLineItems) {
        return changeOrderService.upsertChangeOrder(changeOrder, isDryRun, populateMissingLineItems);
    }

    @AllowAllRoles
    Boolean isAmendmentCurrent(String orderId) {
        return changeOrderService.isAmendmentCurrent(orderId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Order generateDraftAmendment(String subscriptionId) {
        return changeOrderService.generateDraftAmendment(subscriptionId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Order generateCancelOrder(String subscriptionId, Optional<Instant> effectiveDate) {
        return changeOrderService.generateCancelOrder(subscriptionId, effectiveDate);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Order renewSubscription(Order order, Boolean isDryRun) {
        order.setSource(OrderSource.USER);
        order.setShouldUseCustomBillingSchedule(true);
        return orderService.renewSubscription(order, isDryRun);
    }

    @AllowAllRoles
    OrderDetail getOrderDetail(String orderId, boolean skipApprovals, boolean skipMetrics) {
        Order order = orderGetService.getOrderByOrderId(orderId);
        return getOrderDetail(order, skipApprovals, skipMetrics);
    }

    @AllowAllRoles
    OrderDetail getOrderDetail(Order order, boolean skipApprovals, boolean skipMetrics) {
        OrderDetail orderDetail = orderDataAggregator.getOrderDetail(order, skipApprovals, skipMetrics);
        if (!skipApprovals) {
            orderDetail.setApprovalFlows(approvalFlowDataAggregator.getFilteredApprovalFlowInstanceGroupDetail(order));
        }
        EvergreenUtils.sanitizeEvergreenOrderDetail(orderDetail);
        return orderDetail;
    }

    @AllowRoles({ Role.ADMIN, Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    String previewMasterTemplate(String orderId, String masterTemplateId) {
        return orderDocumentService.previewMasterTemplate(orderId, masterTemplateId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.SALES_MANAGER, Role.SALES })
    void updateSubscriptionAttributes(String subscriptionId, SubscriptionUpdateJson subscriptionUpdate) {
        subscriptionService.updateSubscriptionAttributes(subscriptionId, subscriptionUpdate);
    }

    @AllowAllRoles
    List<ProductDetail> getProductDetailsList() {
        return productDataAggregator.getProductDetailsList();
    }

    @AllowAllRoles
    ProductDetail getProductDetail(String productId) {
        return productDataAggregator.getProductDetail(productId);
    }

    @AllowAllRoles
    ProductDetailMapper getProductDetailMapper() {
        return productDetailMapper;
    }

    @AllowAllRoles
    private List<Product> getProducts(PaginationQueryParams paginationQueryParams) {
        return productCatalogGetService.getProducts(paginationQueryParams);
    }

    @AllowAllRoles
    ProductCategory getProductCategory(String productCategoryId) {
        return productCatalogGetService.getProductCategory(productCategoryId);
    }

    @AllowAllRoles
    List<ProductCategory> getProductCategories(PaginationQueryParams paginationQueryParams) {
        return productCatalogService.getProductCategories(paginationQueryParams);
    }

    @AllowAllRoles
    Charge getCharge(String planId, String chargeId) {
        return productCatalogGetService.getCharge(planId, chargeId);
    }

    @AllowAllRoles
    ChargeDetail getChargeDetail(Charge charge) {
        return chargeDataAggregator.getChargeDetail(charge);
    }

    @AllowAllRoles
    List<Plan> getPlans(
        String productId,
        Optional<PlanStatus> planStatus,
        Optional<String> accountId,
        Optional<String> orderCurrencyCode,
        PaginationQueryParams paginationQueryParams,
        boolean includeCharges
    ) {
        String accountCurrency = null;
        if (accountId.isPresent() && StringUtils.isNotBlank(accountId.get())) {
            Optional<AccountStub> accountStub = accountGetService.getAccountStub(accountId.get());
            accountCurrency = accountStub.map(stub -> stub.getCurrency().getCurrencyCode()).orElse(null);
        }
        return productCatalogGetService.getPlans(
            productId,
            planStatus,
            accountCurrency,
            orderCurrencyCode.orElse(null),
            paginationQueryParams,
            includeCharges
        );
    }

    @AllowAllRoles
    Plan getPlan(String planId) {
        return productCatalogGetService.getPlan(planId);
    }

    @AllowAllRoles
    PlanDetail getPlanDetail(String planId) {
        return planDataAggregator.getPlanDetail(planId);
    }

    @AllowAllRoles
    PlanGroupInterface getPlanGroup(String planGroupId) {
        PlanGroup planGroup = planGroupService
            .getPlanGroup(planGroupId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PLAN_GROUP, planGroupId));
        return planGroupInterfaceMapper.toPlanGroupOutput(planGroup);
    }

    @AllowAllRoles
    Proposal getProposal(String proposalId) {
        return proposalService.getProposal(proposalId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PROPOSAL, proposalId));
    }

    @AllowAllRoles
    private PlanMinimal getPlanMinimalFromPlan(Plan plan, Map<String, Product> productMap) {
        return planDataAggregator.getPlanMinimalFromPlan(plan, productMap);
    }

    @AllowAllRoles
    PlanDetail getPlanDetailFromPlan(Plan plan) {
        return planDataAggregator.getPlanDetailFromPlan(plan);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Product addProduct(Product product) {
        return productCatalogService.addProduct(product);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    void updateProduct(Product product) {
        productCatalogService.updateProduct(product);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Product deleteProduct(String productId) {
        return productCatalogService.deleteProduct(productId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    ProductCategory addProductCategory(ProductCategory category) {
        return productCatalogService.addProductCategory(category);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    void updateProductCategory(ProductCategory category) {
        productCatalogService.updateProductCategory(category);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    ProductCategory deleteProductCategory(String productCategoryId) {
        return productCatalogService.deleteProductCategory(productCategoryId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    String addCharge(String planId, Charge charge) {
        return productCatalogService.addCharge(planId, charge);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Charge updateCharge(String planId, Charge charge) {
        return productCatalogService.updateCharge(planId, charge);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Charge updateChargePartial(String planId, ChargePartialJson chargePartial) {
        return chargeService.updateChargePartial(planId, chargePartial);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Charge deleteCharge(String planId, String chargeId) {
        return productCatalogService.deleteCharge(planId, chargeId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Plan addPlanWithoutCharges(Plan plan, List<CatalogRelationship> planRelationships) {
        return productCatalogService.addPlanWithoutCharges(plan, planRelationships);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    void updatePlanWithoutCharges(Plan plan, List<CatalogRelationship> planRuleRelationships) {
        productCatalogService.updatePlanWithoutCharges(plan, planRuleRelationships);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Entity addEntity(Entity entity) {
        return entityService.addEntity(entity);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Entity updateEntity(EntityPatchRequest entityPatchRequest) {
        return entityService.updateEntity(entityPatchRequest);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    void deleteEntity(String entityId) {
        entityService.deleteEntity(entityId);
    }

    @AllowAllRoles
    Entity getEntity(String entityId) {
        return entityGetService.getEntityById(entityId);
    }

    @AllowAllRoles
    EntityResolver getEntityResolver(String entityId) {
        return new EntityResolver(entityGetService, invoiceConfigurationService, entityId);
    }

    @AllowAllRoles
    UserJson getCurrentUserJson(UserPrincipal userPrincipal, User user) {
        return userDataAggregator.getCurrentUserJson(userPrincipal, user);
    }

    @AllowAllRoles
    List<Entity> getEntities() {
        return entityGetService.getEntities();
    }

    @AllowRoles({ Role.ADMIN, Role.BILLY_ENGINEER })
    PlanGroupInterface upsertPlanGroup(PlanGroupInterface planGroupInput) {
        ImmutablePlanGroup.Builder planGroupBuilder = planGroupInterfaceMapper.fromInput(planGroupInput);
        PlanGroup upsertedPlanGroup = planGroupService.upsertPlanGroup(
            planGroupBuilder.planGroupItems(planGroupInterfaceMapper.fromInputs(planGroupInput.getPlanGroupItemInputList())).build()
        );
        return planGroupInterfaceMapper.toPlanGroupOutput(upsertedPlanGroup);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN, Role.SALES, Role.SALES_MANAGER })
    Proposal createProposal(CreateProposalRequest createProposalRequest) {
        return proposalService.createProposal(createProposalRequest);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    PlanGroupInterface deletePlanGroup(String planGroupId) {
        PlanGroup planGroup = planGroupService.deletePlanGroup(planGroupId);
        return planGroupInterfaceMapper.toPlanGroupOutput(planGroup);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Plan duplicatePlan(String planId) {
        return productCatalogService.duplicatePlan(planId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    void deletePlan(String planId) {
        productCatalogService.deletePlan(planId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Plan deprecatePlan(String planId) {
        return productCatalogService.deprecatePlan(planId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    void reactivatePlan(String planId) {
        productCatalogService.reactivatePlan(planId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Plan activatePlan(String planId) {
        return productCatalogService.activatePlan(planId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Plan revertToDraft(String planId) {
        return productCatalogService.revertToDraft(planId);
    }

    @AllowAllRoles
    AccountDetail getAccountDetail(String accountId) {
        return accountDataAggregator.getAccountDetail(accountId);
    }

    @AllowAllRoles
    AccountDetail getAccountDetail(Account account) {
        return accountDataAggregator.decorateAccount(account);
    }

    @AllowAllRoles
    List<AccountDetail> getAccountDetails(AccountType accountType, PaginationQueryParams paginationQueryParams) {
        return accountDataAggregator.getAccountDetails(accountType, paginationQueryParams);
    }

    @AllowAllRoles
    Account getAccount(String accountId) {
        return accountGetService.getAccount(accountId);
    }

    @AllowAllRoles
    List<Account> getAccounts(AccountType accountType, PaginationQueryParams paginationQueryParams) {
        return accountGetService.getAccounts(accountType, paginationQueryParams);
    }

    @AllowAllRoles
    AccountContact getAccountContact(String contactId) {
        return accountGetService.getContact(contactId);
    }

    @AllowAllRoles
    List<AccountContact> getAccountContacts(String accountId, boolean expand) {
        return accountGetService.getAccountContacts(accountId, expand);
    }

    @AllowAllRoles
    Optional<AccountContact> getAccountReceivableContact() {
        return accountReceivableContactService.getContact();
    }

    @AllowAllRoles
    AccountContactDetail getAccountContactDetail(String contactId) {
        return accountContactDataAggregator.getAccountContactDetail(contactId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER, Role.BILLING_CLERK, Role.ACCOUNTANT })
    Account upsertAccount(Account account, AccountAddress address) {
        return accountService.upsertAccount(account, address);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER, Role.BILLING_CLERK })
    Account addAccount(Account account, AccountAddress address) {
        return accountService.addAccount(account, address);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void updateAccount(Account account, AccountAddress address) {
        accountService.updateAccount(account, address);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Account deleteAccount(String accountId) {
        return accountService.deleteAccount(accountId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    AccountContact upsertAccountContact(AccountContact accountContact, boolean skipAddressValidation, boolean strictValidation) {
        return accountService.upsertContact(accountContact, skipAddressValidation, strictValidation);
    }

    @AllowRoles({ Role.ADMIN })
    AccountContact upsertAccountReceivableContact(AccountContact accountContact) {
        return accountReceivableContactService.upsertContact(accountContact);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    AccountContact deleteContact(String contactId) {
        return accountService.deleteContact(contactId);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    @AllowAllRoles
    List<EmailLog> getEmailLogs(Optional<String> entityId, Optional<String> parentEntityId) {
        return emailService.getEmailLogs(entityId, parentEntityId);
    }

    @AllowAllRoles
    SubscriptionModifiableResponse subscriptionModifiable(String subscriptionId) {
        return adminOperationsService.canDeleteSubscription(subscriptionId);
    }

    @AllowAllRoles
    SubscriptionModifiableResponse subscriptionReversible(String subscriptionId) {
        return adminOperationsService.canRevertSubscription(subscriptionId);
    }

    @AllowAllRoles
    InvoiceDeletableResponse canDeleteInvoice(String invoiceNumber) {
        return adminOperationsService.canDeleteInvoice(new Invoice.Number(invoiceNumber));
    }

    @AllowRoles({ Role.ADMIN })
    void deleteSubscription(String subscriptionId, int version) {
        adminOperationsService.deleteSubscription(subscriptionId, version);
    }

    @AllowRoles({ Role.ADMIN })
    void revertSubscription(String subscriptionId, int version) {
        adminOperationsService.revertSubscription(subscriptionId, version);
    }

    @AllowAllRoles
    SubscriptionDetail getSubscriptionDetail(String subscriptionId) {
        return subscriptionDataAggregator.getSubscriptionDetail(subscriptionId);
    }

    @AllowAllRoles
    Invoice getInvoice(Invoice.Number invoiceNumber) {
        return invoiceRetrievalService.getInvoice(invoiceNumber);
    }

    @AllowRoles({ Role.ADMIN })
    void deleteVoidedInvoice(String invoiceNumber) {
        adminOperationsService.deleteVoidedInvoice(new Invoice.Number(invoiceNumber));
    }

    @AllowAllRoles
    Map<String, List<BigDecimal>> getOpenInvoicesReportOverview() {
        return invoiceRetrievalService.getOpenInvoicesReportOverview();
    }

    @AllowAllRoles
    List<InvoiceItem> getUnbilledUsageInvoiceItems(String subscriptionId) {
        return invoiceService.getUnbilledUsageInvoiceItems(subscriptionId);
    }

    @AllowAllRoles
    List<InvoiceItem> getUsageInvoiceItemsForSubscriptionCharge(String subscriptionId, String chargeId) {
        return invoiceService.getUsageInvoiceItemsForSubscriptionCharge(subscriptionId, chargeId);
    }

    @AllowAllRoles
    Optional<Instant> getRawUsageArrivalTimeCheckpoint() {
        return usageService.getRawUsageArrivalTimeCheckpoint();
    }

    @AllowAllRoles
    List<UsageAggregateOutput> getAggregatedUsageForSubscription(String subscriptionId, Period period) {
        return usageService.getAggregatedUsageForSubscription(subscriptionId, period);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    UsageBatchInsertResult addRawUsages(List<RawUsage> rawUsages) {
        return usageService.insertRawUsageBatch(rawUsages);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    Invoice updateInvoice(Invoice.Number invoiceNumber, UpdateInvoiceRequest updateInvoiceRequest) {
        return invoiceService.updateInvoice(invoiceNumber, updateInvoiceRequest);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    Invoice postInvoice(Invoice.Number draftInvoiceNumber) {
        return invoiceService.postInvoice(draftInvoiceNumber);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    Invoice deleteInvoice(Invoice.Number draftInvoiceNumber) {
        return invoiceService.deleteInvoice(draftInvoiceNumber);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    Optional<Invoice> generateInvoice(
        String subscriptionId,
        Instant invoiceTargetDate,
        Instant invoiceDate,
        InvoiceChargeInclusionOption invoiceChargeInclusionOption
    ) {
        return invoiceService.generateInvoice(
            subscriptionId,
            invoiceTargetDate,
            Optional.ofNullable(invoiceDate),
            invoiceChargeInclusionOption,
            Optional.of(InvoiceGenerationMethod.USER_INITIATED),
            Optional.empty()
        );
    }

    @AllowAllRoles
    InvoiceDetail getInvoiceDetail(Invoice invoice) {
        return invoiceDataAggregator.getInvoiceDetail(invoice);
    }

    @AllowAllRoles
    InvoiceDetail getInvoiceDetail(Invoice.Number invoiceNumber) {
        return invoiceDataAggregator.getInvoiceDetail(invoiceNumber);
    }

    @AllowAllRoles
    List<InvoiceItemDetail> getInvoiceItemDetails(List<InvoiceItem> invoiceItems, String subscriptionId) {
        return invoiceDataAggregator.getInvoiceItemDetails(invoiceItems, subscriptionId);
    }

    @AllowAllRoles
    List<SettlementApplication> getAppliedSettlementApplications(Invoice.Number invoiceNumber) {
        return invoiceSettlementService.getAppliedSettlementApplicationsForInvoice(invoiceNumber);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    void addAndApplyManualPayment(
        Invoice.Number invoiceNumber,
        BigDecimal invoiceAmount,
        UUID paymentMethodId,
        PaymentType paymentType,
        Long paymentDate,
        BigDecimal amount,
        String note,
        BigDecimal bankFee,
        String paymentBankAccountId
    ) {
        invoiceSettlementService.addAndApplyManualPayment(
            invoiceNumber,
            invoiceAmount,
            paymentMethodId,
            paymentType,
            paymentDate,
            amount,
            note,
            bankFee,
            paymentBankAccountId
        );
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    void voidPayment(String paymentId, Invoice.Number invoiceNumber, Instant voidDate, BigDecimal invoiceBalanceAmount, String note) {
        invoiceSettlementService.voidPayment(paymentId, invoiceNumber, voidDate, invoiceBalanceAmount, note);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    void applyCredit(Invoice.Number invoiceNumber, BigDecimal invoiceAmount, String creditMemoNumber, BigDecimal amount, String note) {
        invoiceSettlementService.applyCredit(invoiceNumber, invoiceAmount, creditMemoNumber, amount, note);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    void unapplyCredit(
        Invoice.Number invoiceNumber,
        BigDecimal invoiceBalanceAmount,
        String creditMemoNumber,
        BigDecimal creditMemoBalanceAmount,
        UUID settlementToUnapplyId,
        String note
    ) {
        invoiceSettlementService.unapplyCredit(
            invoiceNumber,
            invoiceBalanceAmount,
            creditMemoNumber,
            creditMemoBalanceAmount,
            settlementToUnapplyId,
            note
        );
    }

    @AllowAllRoles
    int getDraftInvoicesCountForAccount(String accountId) {
        return accountInvoiceService.getDraftInvoicesCountForAccount(accountId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    CreditMemo postCreditMemo(String draftCreditMemoNumber) {
        return creditMemoService.postCreditMemo(draftCreditMemoNumber);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    CreditMemo createStandaloneCreditMemo(StandaloneCreditMemoRequest creditMemoRequest) {
        return creditMemoService.createStandaloneCreditMemo(creditMemoRequest);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    CreditMemo updateDraftCreditMemo(StandaloneCreditMemoRequest request, String creditMemoNumber) {
        return creditMemoService.updateDraftCreditMemo(request, creditMemoNumber);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    CreditMemo convertNegativeInvoiceToCreditMemo(Invoice negativeInvoice) {
        return creditMemoService.convertNegativeInvoiceToCreditMemo(negativeInvoice);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    void deleteCreditMemo(String creditMemoNumber) {
        creditMemoService.deleteCreditMemo(creditMemoNumber);
    }

    @AllowAllRoles
    CreditMemoDetail getCreditMemoDetail(String creditMemoNumber) {
        return creditMemoDataAggregator.getCreditMemoDetail(creditMemoNumber);
    }

    @AllowAllRoles
    Optional<InputStream> getCreditMemoDocumentByCreditMemoNumber(String creditMemoNumber) {
        return creditMemoDocumentService.getCreditMemoDocumentByCreditMemoNumber(creditMemoNumber);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    CreditMemoDocumentJson aggregateDataForCreditMemoDocument(String creditMemoNumber) {
        return creditMemoDocumentDataAggregator.aggregateDataForCreditMemoDocument(creditMemoNumber);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    CreditMemoEmailResponse sendCreditMemoEmail(
        CreditMemoTemplateData creditMemoTemplateData,
        CreditMemoDetail creditMemoDetail,
        InputStream creditMemoDocument
    ) {
        return creditMemoEmailService.sendCreditMemoEmail(creditMemoTemplateData, creditMemoDetail, creditMemoDocument);
    }

    @AllowAllRoles
    List<Payment> getPaymentsByAccountId(String accountId) {
        return paymentGetService.getPaymentsByAccountId(accountId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    List<LedgerAccount> getLedgerAccountsForPaymentBankAccount(String paymentBankAccountId) {
        return paymentBankAccountService.getLedgerAccountsForPaymentBankAccount(paymentBankAccountId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    PaymentBankAccountJson upsertPaymentBankAccount(PaymentBankAccountJson paymentBankAccountRequest) {
        var paymentBankAccountCreated = paymentBankAccountService.upsert(paymentBankAccountJsonMapper.fromJson(paymentBankAccountRequest));
        return paymentBankAccountJsonMapper.toJson(paymentBankAccountCreated);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    PaymentBankAccountJson deletePaymentBankAccount(String paymentBankAccountId) {
        var paymentBankAccountDeleted = paymentBankAccountService.delete(paymentBankAccountId);
        return paymentBankAccountJsonMapper.toJson(paymentBankAccountDeleted);
    }

    @AllowAllRoles
    PaymentBankAccountJson getPaymentBankAccount(String paymentBankAccountId) {
        var paymentBankAccount = paymentBankAccountService.getByPaymentBankAccountId(paymentBankAccountId);
        return paymentBankAccountJsonMapper.toJson(paymentBankAccount);
    }

    @AllowAllRoles
    List<PaymentBankAccountJson> getApplicablePaymentBankAccountsForInvoicePayment(String invoiceNumber) {
        var paymentBankAccounts = invoiceSettlementService.getApplicablePaymentBankAccountsForInvoicePayment(invoiceNumber);
        return paymentBankAccountJsonMapper.paymentBankAccountsToJson(paymentBankAccounts);
    }

    @AllowAllRoles
    Payment getPayment(String paymentId) {
        return paymentGetService.getPaymentByPaymentId(paymentId);
    }

    @AllowAllRoles
    PaymentDetail getPaymentDetail(String paymentId) {
        return paymentDataAggregator.getPaymentDetail(paymentId);
    }

    @AllowAllRoles
    PaymentInAccountDetail getPaymentInAccountDetail(Payment payment) {
        return paymentDataAggregator.getPaymentInAccountDetail(payment);
    }

    @AllowAllRoles
    List<PaymentMethodDetail> getPaymentMethodDetails(String accountId) {
        return paymentDataAggregator.getPaymentMethodDetails(accountId);
    }

    @AllowAllRoles
    List<FailedPaymentDetail> getFailedPaymentDetailsForInvoice(Invoice.Number invoiceNumber) {
        return paymentDataAggregator.getFailedPaymentDetailsForInvoice(invoiceNumber);
    }

    @AllowRoles({ Role.ADMIN })
    Optional<PaymentIntegration> getPaymentIntegration() {
        return paymentIntegrationGetService.getPaymentIntegration();
    }

    @AllowAllRoles
    Optional<PaymentStripeConnectIntegration> getCompletedStripeConnectIntegration() {
        return paymentIntegrationGetService.getCompletedStripeConnectIntegration();
    }

    @AllowAllRoles
    boolean isPaymentIntegrationEnabled() {
        return paymentIntegrationGetService.isPaymentIntegrationEnabled();
    }

    @AllowAllRoles
    PaymentStripeConnectIntegration getStripeConnectIntegrationThrowIfNotExists(String tenantId) {
        return paymentIntegrationGetService.getStripeConnectIntegrationThrowIfNotExists(tenantId);
    }

    @AllowAllRoles
    boolean hasPaymentIntegration() {
        return paymentIntegrationGetService.hasIntegration();
    }

    @AllowRoles({ Role.ADMIN })
    PaymentStripeConnectIntegrationSetupResponse initiateStripeConnectIntegration() {
        return paymentIntegrationService.initiateStripeConnectIntegration();
    }

    @AllowUnauthenticated(cascade = true)
    void completeStripeConnectIntegration(String authorizationCode, String scope, String integrationId) {
        stripeService.handleAuthorizationCodeCallback(authorizationCode, scope, integrationId);
    }

    @AllowRoles({ Role.ADMIN })
    PaymentStripeConnectIntegration deleteCurrentStripePaymentIntegration() {
        return paymentIntegrationService.deleteCurrentStripePaymentIntegration();
    }

    @AllowUnauthenticated(cascade = true)
    void unEnrollFromAutomaticPayments(String paymentLinkId, PaymentManagementLinkType paymentLinkType) {
        PaymentManagementLink paymentManagementLink = linkBasedPaymentService.getPaymentManagementLink(paymentLinkId, paymentLinkType);
        TenantContextInjector.runInTenantContext(paymentManagementLink.getTenantId(), tenantIdProvider, () ->
            linkBasedPaymentService.unEnrollFromAutomaticPayments(paymentLinkId, paymentLinkType)
        );
    }

    @AllowUnauthenticated(cascade = true)
    void reEnableAutomaticPayments(String paymentLinkId, PaymentManagementLinkType paymentLinkType) {
        PaymentManagementLink paymentManagementLink = linkBasedPaymentService.getPaymentManagementLink(paymentLinkId, paymentLinkType);
        TenantContextInjector.runInTenantContext(paymentManagementLink.getTenantId(), tenantIdProvider, () ->
            linkBasedPaymentService.reEnableAutomaticPayments(paymentLinkId, paymentLinkType)
        );
    }

    @AllowUnauthenticated(cascade = true)
    PaymentLinkGqlResponse buildInvoicePaymentLinkResponse(String paymentLinkId) {
        PaymentLinkResponse paymentLinkResponse = linkBasedPaymentService.createInvoicePaymentLinkResponse(paymentLinkId);
        PaymentManagementLink paymentManagementLink = linkBasedPaymentService.getPaymentManagementLink(
            paymentLinkId,
            PaymentManagementLinkType.INVOICE
        );
        return TenantContextInjector.callInTenantContext(paymentManagementLink.getTenantId(), tenantIdProvider, () ->
            paymentDataAggregator.getPaymentLinkGqlResponse(paymentLinkResponse)
        );
    }

    @AllowUnauthenticated(cascade = true)
    AccountPaymentLinkGqlResponse buildAccountPaymentLinkResponse(String paymentLinkId) {
        AccountPaymentLinkResponse accountPaymentLinkResponse = linkBasedPaymentService.createAccountPaymentLinkResponse(paymentLinkId);
        PaymentManagementLink paymentManagementLink = linkBasedPaymentService.getPaymentManagementLink(
            paymentLinkId,
            PaymentManagementLinkType.ACCOUNT
        );
        return TenantContextInjector.callInTenantContext(paymentManagementLink.getTenantId(), tenantIdProvider, () ->
            paymentDataAggregator.getAccountPaymentLinkGqlResponse(accountPaymentLinkResponse)
        );
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    String generateLink(
        String accountId,
        String tenantId,
        AddPaymentMethodActionType actionType,
        String sourceId,
        PaymentLinkSourceType sourceType,
        UUID paymentIntegrationId
    ) {
        return paymentLinkService.generateLink(accountId, tenantId, actionType, sourceId, sourceType, paymentIntegrationId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES })
    InvoicePaymentManagementLink generateInvoicePaymentLink(String accountId, String invoiceNumber) {
        return invoicePaymentManagementLinkService.getOrCreateInvoicePaymentManagementLink(accountId, invoiceNumber).orElse(null);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.BILLING_CLERK })
    AccountPaymentManagementLink generateAccountPaymentLink(String accountId) {
        return accountPaymentManagementLinkService.getOrCreateAccountPaymentManagementLink(accountId).orElse(null);
    }

    @AllowAllRoles
    List<Refund> getRefunds(String accountId) {
        return refundService.getRefunds(accountId);
    }

    @AllowAllRoles
    List<Refund> getRefundsByPaymentId(String paymentId) {
        return refundService.getRefundsByPaymentId(paymentId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    RefundPreview generateRefund(String creditMemoNumber, String paymentId) {
        return refundService.generateRefund(creditMemoNumber, paymentId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    Refund createAndApplyRefund(Refund refund) {
        return refundService.createAndApplyRefund(refund);
    }

    @AllowAllRoles
    RefundDetail getRefundDetail(Refund refund) {
        return refundDataAggregator.getRefundDetail(refund);
    }

    @AllowAllRoles
    RefundPreviewDetail getRefundPreviewDetail(RefundPreview refundPreview) {
        return paymentDataAggregator.getRefundPreviewDetail(refundPreview);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK, Role.READ_ONLY, Role.EXECUTIVE })
    BulkInvoiceRun getBulkInvoiceRun(String bulkInvoiceRunId) {
        Optional<UUID> internalId = UUIDConverter.toUUID(bulkInvoiceRunId);
        if (internalId.isPresent()) {
            return bulkInvoiceService.getBulkInvoiceRun(internalId.get());
        }

        return bulkInvoiceService.getBulkInvoiceRunByBulkInvoiceRunId(bulkInvoiceRunId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK, Role.READ_ONLY, Role.EXECUTIVE })
    List<BulkInvoiceRunItem> getBulkInvoiceRunItems(String bulkInvoiceRunId) {
        Optional<UUID> internalId = UUIDConverter.toUUID(bulkInvoiceRunId);
        if (internalId.isPresent()) {
            return bulkInvoiceService.getBulkInvoiceRunItems(internalId.get());
        }

        return bulkInvoiceService.getBulkInvoiceRunItemsByBulkInvoiceRunItemId(bulkInvoiceRunId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK, Role.READ_ONLY, Role.EXECUTIVE })
    Optional<UUID> getBulkInvoiceRunInProgress() {
        return bulkInvoiceService.getBulkInvoiceRunInProgress();
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    BulkInvoiceRun createBulkInvoiceRun(BulkInvoiceRunInput bulkInvoiceRunInput) {
        return bulkInvoiceService.createManualBulkInvoiceRun(bulkInvoiceRunInput);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    BulkInvoiceRun postInvoicesForBulkInvoiceRun(String bulkInvoiceRunId, List<String> subscriptionIdExclusionList) {
        Optional<UUID> internalId = UUIDConverter.toUUID(bulkInvoiceRunId);
        if (internalId.isPresent()) {
            return bulkInvoiceService.postInvoicesForBulkInvoiceRun(internalId.get(), subscriptionIdExclusionList);
        }

        return bulkInvoiceService.postInvoicesForBulkInvoiceRunByBulkInvoiceRunId(bulkInvoiceRunId, subscriptionIdExclusionList);
    }

    @AllowAllRoles
    BulkInvoiceRunSelector getDefaultBulkInvoiceRunSelector() {
        return bulkInvoiceSelectorService.getDefaultBulkInvoiceRunSelector();
    }

    @AllowAllRoles
    Tenant getCurrentTenant() {
        return tenantService.getCurrentTenant();
    }

    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    List<Tenant> getTenants() {
        return tenantService.getTenants();
    }

    @AllowRoles({ Role.BILLY_ADMIN })
    TenantCreationResponseJson createTenant(Tenant tenant) {
        return tenantService.createTenant(tenant);
    }

    @AllowRoles({ Role.ADMIN })
    void updateTenant(Tenant tenant) {
        tenantService.updateTenant(tenant);
    }

    @AllowRoles({ Role.SALES, Role.SALES_MANAGER, Role.FINANCE, Role.ADMIN })
    Opportunity getOpportunityByOpportunityId(String opportunityId) {
        return opportunityGetService.getOpportunityByOpportunityId(opportunityId);
    }

    @AllowRoles({ Role.SALES, Role.SALES_MANAGER, Role.FINANCE, Role.ADMIN })
    Opportunity getOpportunityByCrmOpportunityId(String crmOpportunityId) {
        return opportunityGetService.getOpportunityByCrmOpportunityId(crmOpportunityId);
    }

    @AllowAllRoles
    TenantSetting getTenantSetting() {
        return tenantSettingService.getTenantSetting();
    }

    @AllowAllRoles
    List<CurrencyJson> getSupportedCurrencies() {
        return tenantSettingService.getSupportedCurrencies();
    }

    @AllowRoles({ Role.ADMIN })
    void upsertTenantSetting(TenantSetting tenantSetting) {
        tenantSettingService.upsertTenantSetting(tenantSetting);
    }

    @AllowRoles({ Role.ADMIN })
    void updateSupportedCurrencies(List<String> supportedCurrencies) {
        tenantSettingService.updateSupportedCurrencies(supportedCurrencies);
    }

    @AllowAllRoles
    String search(String jsonQuery) throws IOException {
        return elasticSearchService.search(jsonQuery);
    }

    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    String searchInTenant(String jsonQuery, String tenantId) throws IOException {
        return elasticSearchService.search(jsonQuery, tenantId);
    }

    @AllowUnauthenticated(cascade = true)
    Optional<UserPrincipal> provideCurrentUserOptional() {
        return currentUserProvider.provideOptional();
    }

    @AllowUnauthenticated(cascade = true)
    Optional<User> provideApiKeyUser() {
        return currentUserProvider.provideApiKeyUser();
    }

    @AllowUnauthenticated(cascade = true)
    Optional<UserPrincipal> provideAuthUser() {
        return currentUserProvider.provideOptional();
    }

    @AllowAllRoles
    User getUser(String userId) {
        return userService.getUser(userId);
    }

    @AllowAllRoles
    User getUserIncludeCognitoStatus(String userId) {
        Validator.validateStringNotBlank(userId, "Argument userId cannot be empty.");
        return userService.getUserIncludeCognitoStatus(userId);
    }

    @AllowAllRoles
    Optional<User> getUserByEmail(String email) {
        return userService.getUserByEmail(email);
    }

    @AllowUnauthenticated(cascade = true)
    Optional<UserAuthInfo> getAuthInfoByEmail(String emailAddress) {
        return userService.getAuthInfoByEmail(emailAddress);
    }

    @AllowRoles({ Role.ADMIN })
    String createUser(User user) {
        return userService.createUser(user);
    }

    @AllowRoles({ Role.ADMIN })
    void updateUser(User user) {
        userService.updateUser(user);
    }

    @AllowRoles({ Role.ADMIN })
    User disableUser(String userId) {
        return userService.disableUser(userId);
    }

    @AllowRoles({ Role.ADMIN })
    User enableUser(String userId) {
        return userService.enableUser(userId);
    }

    @AllowRoles({ Role.BILLY_ADMIN, Role.ADMIN })
    void resendCognitoInvitationEmailForExistingUser(String email) {
        userService.resendCognitoInvitationEmailForExistingUser(email);
    }

    @AllowAllRoles
    List<TaxRate> getTaxRates(PaginationQueryParams paginationQueryParams) {
        return taxRateGetService.getTaxRates(paginationQueryParams);
    }

    @AllowAllRoles
    TaxRate getTaxRate(UUID id) {
        return taxRateGetService.getTaxRate(id);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    TaxRate addTaxRate(TaxRate taxRate) {
        return taxRateService.addTaxRate(taxRate);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    void updateTaxRate(TaxRate taxRate) {
        taxRateService.updateTaxRate(taxRate);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    TaxRate deleteTaxRate(UUID taxRateId) {
        return taxRateService.deleteTaxRate(taxRateId);
    }

    @AllowAllRoles
    List<UnitOfMeasure> getUnitsOfMeasure(PaginationQueryParams paginationQueryParams) {
        return unitOfMeasureService.getUnitsOfMeasure(paginationQueryParams);
    }

    @AllowAllRoles
    UnitOfMeasure getUnitOfMeasure(UUID id) {
        return unitOfMeasureService.getUnitOfMeasure(id);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    UnitOfMeasure addUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        return unitOfMeasureService.addUnitOfMeasure(unitOfMeasure);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    void updateUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        unitOfMeasureService.updateUnitOfMeasure(unitOfMeasure);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    UnitOfMeasure deleteUnitOfMeasure(UUID id) {
        return unitOfMeasureService.deleteUnitOfMeasure(id);
    }

    @AllowAllRoles
    List<Discount> getDiscounts() {
        return discountService.getDiscounts();
    }

    @AllowAllRoles
    Discount getDiscount(String discountId, boolean includeDeleted) {
        return discountService.getDiscount(discountId, includeDeleted);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    Discount upsertDiscount(Discount discount) {
        return discountService.upsertDiscount(discount);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    void updateDiscountStatus(Discount discount, DiscountStatus discountStatus) {
        discountService.updateDiscountStatus(discount, discountStatus);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    Discount deleteDiscount(String discountId) {
        return discountService.deleteDiscount(discountId);
    }

    @AllowRoles({ Role.ADMIN })
    List<UserGroup> getUserGroups() {
        return userGroupService.getUserGroups();
    }

    @AllowRoles({ Role.ADMIN, Role.READ_ONLY, Role.EXECUTIVE })
    UserGroup getUserGroup(String userGroupId, boolean updateUserList) {
        return userGroupService.getUserGroup(userGroupId, updateUserList);
    }

    @AllowRoles({ Role.ADMIN })
    UserGroup upsertUserGroup(UserGroup userGroup, boolean updateUserList) {
        return userGroupService.upsertUserGroup(userGroup, updateUserList);
    }

    @AllowRoles({ Role.ADMIN })
    UserGroup deleteUserGroup(String userGroupId, boolean updateUserList) {
        return userGroupService.deleteUserGroup(userGroupId, updateUserList);
    }

    @AllowAllRoles
    DunningSetting getDunningSetting() {
        return dunningSettingService.getDunningSetting();
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    DunningSetting upsertDunningSetting(DunningSetting dunningSetting) {
        return dunningSettingService.upsertDunningSetting(dunningSetting);
    }

    @AllowRoles({ Role.ADMIN })
    void sendSampleDunningEmail(DunningReminderType reminderType) {
        invoiceDunningService.sendSampleEmail(reminderType);
    }

    @AllowAllRoles
    List<InvoiceDunning> getDunningDetails(String invoiceNumber) {
        return invoiceDunningService.getEmailedDunningDetails(invoiceNumber);
    }

    @AllowAllRoles
    @Deprecated
    List<Opportunity> getOpportunitiesBySalesforceAccountId(String accountId, String tenantId) {
        List<Opportunity> standaloneOpportunities = crmService.getOpportunitiesByAccountCrmId(accountId);
        if (CollectionUtils.isNotEmpty(standaloneOpportunities)) {
            return standaloneOpportunities;
        }
        return salesforceGetService.getOpportunitiesByAccountCrmId(accountId, tenantId);
    }

    @AllowAllRoles
    List<Opportunity> getOpportunitiesFromCrm(String accountCrmId, String tenantId) {
        return crmService.getOpportunitiesFromCrm(accountCrmId, tenantId);
    }

    @AllowAllRoles
    SalesforceAccount getAccountById(String accountId, boolean throwIfExists) {
        return salesforceGetService
            .getAccountById(accountId, throwIfExists)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.SFDC_ACCOUNT, accountId));
    }

    @AllowAllRoles
    Optional<SalesforceIntegrationResponse> getSalesforceIntegrationForTenant(String tenantId) {
        return salesforceIntegrationService.getIntegrationForTenant(tenantId);
    }

    @AllowRoles({ Role.ADMIN })
    void deleteSalesforceIntegration(String tenantId) {
        salesforceIntegrationService.deleteSalesforceIntegration(tenantId);
    }

    @AllowRoles({ Role.ADMIN })
    String initiateSalesforceIntegration(SalesforceClientIntegrationRequestJson clientInformationJson) {
        return salesforceLoginService.initiateIntegration(clientInformationJson);
    }

    @AllowUnauthenticated(cascade = true)
    void completeSalesforceIntegration(String authorizationCode, String integrationId, String redirectUri) {
        salesforceLoginService.handleAuthorizationCodeCallback(authorizationCode, integrationId, redirectUri);
    }

    @AllowAllRoles
    List<SubskribeSalesforceContact> getSalesforceContactsForAccount(String accountId) {
        return salesforceContactService.getContactsForAccount(accountId);
    }

    @AllowAllRoles
    List<CrmContact> getCrmContactsForAccount(String accountId) {
        if (tenantHasCompletedSalesforceIntegration()) {
            List<SubskribeSalesforceContact> contacts = salesforceContactService.getContactsForAccount(accountId);
            return crmContactMapper.toCrmContacts(contacts);
        }

        if (tenantHasCompletedHubSpotIntegration()) {
            return hubSpotContactService.getContactsForAccount(accountId);
        }

        return List.of();
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    List<UpsertCRMContactResponse> upsertCrmContacts(String accountId, List<String> crmIds) {
        if (tenantHasCompletedSalesforceIntegration()) {
            return salesforceContactService.upsertCrmContacts(accountId, crmIds);
        }

        if (tenantHasCompletedHubSpotIntegration()) {
            return hubSpotContactService.upsertCrmContacts(accountId, crmIds);
        }

        return List.of();
    }

    private boolean tenantHasCompletedSalesforceIntegration() {
        Optional<SalesforceIntegrationResponse> integrationResponse = getSalesforceIntegrationForTenant(provideTenantIdString());
        return integrationResponse.isPresent() && integrationResponse.get().getIsCompleted();
    }

    private boolean tenantHasCompletedHubSpotIntegration() {
        try {
            HubSpotIntegration integration = getHubSpotIntegrationForTenant();
            return integration != null && BooleanUtils.isTrue(integration.getIsCompleted());
        } catch (ObjectNotFoundException e) {
            return false;
        }
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    @Deprecated
    void updatePrimaryOrderIdForSalesforceOpportunity(Order order) {
        salesforceJobQueueService.updatePrimaryOrderIdForOpportunity(order);
    }

    @AllowAllRoles
    void addSalesforceJobUnit(Order order) {
        salesforceJobQueueService.dispatchOrderSync(order);
    }

    @AllowAllRoles
    String provideTenantIdString() {
        return tenantIdProvider.provideTenantIdString();
    }

    @AllowUnauthenticated(cascade = true)
    Pair<DocumentLink, String> getHtmlForWebOrderForm(String linkId) {
        DocumentLink documentLink = documentLinkService.getDocumentLinkByLinkId(linkId);
        return Pair.of(
            documentLink,
            TenantContextInjector.callInTenantContext(documentLink.tenantId(), tenantIdProvider, () ->
                orderDocumentService.renderOrderHTML(documentLink.orderId())
            )
        );
    }

    @AllowRoles({ Role.ADMIN })
    NotificationTargetAndSubscriptions getNotificationTargetAndSubscriptionsById(UUID notificationId, boolean returnIfDeleted) {
        return notificationTargetService.getNotificationTargetAndSubscriptionsById(notificationId, returnIfDeleted);
    }

    @AllowRoles({ Role.ADMIN })
    List<NotificationTargetAndSubscriptions> getAllNotificationSubscriptionsForTenant() {
        return notificationTargetService.getAllNotificationSubscriptionsForTenant();
    }

    @AllowRoles({ Role.ADMIN })
    UUID addNotificationTargetAndSubscriptions(NotificationTargetAndSubscriptions notificationTargetAndSubscriptions) {
        return notificationTargetService.addNotificationTargetAndSubscriptions(notificationTargetAndSubscriptions);
    }

    @AllowRoles({ Role.ADMIN })
    UUID updateNotificationTargetAndSubscriptions(UUID notificationId, NotificationTargetAndSubscriptions notificationTargetAndSubscriptions) {
        return notificationTargetService.updateNotificationTargetAndSubscriptions(notificationId, notificationTargetAndSubscriptions);
    }

    @AllowRoles({ Role.ADMIN })
    void subscribeExistingNotificationTargetToEvent(UUID notificationId, NotificationEventType notificationEventType) {
        notificationTargetService.subscribeExistingNotificationTargetToEvent(notificationId, notificationEventType);
    }

    @AllowRoles({ Role.ADMIN })
    void unsubscribeNotificationTarget(UUID notificationId) {
        notificationTargetService.unsubscribeNotificationTarget(notificationId);
    }

    @AllowRoles({ Role.ADMIN })
    void unsubscribeNotificationTargetFromEvent(UUID notificationId, NotificationEventType notificationEventType) {
        notificationTargetService.unsubscribeNotificationTargetFromEvent(notificationId, notificationEventType);
    }

    @AllowRoles({ Role.ADMIN })
    String getSigningKeyForWebhook(String notificationId) {
        return webhookSigningKeyService.getOrCreateSigningKeyForTargetId(notificationId);
    }

    @AllowRoles({ Role.ADMIN })
    AvalaraIntegration getAvalaraIntegration() {
        return avalaraIntegrationGetService.getIntegrationByTenant();
    }

    @AllowAllRoles
    boolean hasAvalaraIntegration() {
        return avalaraIntegrationGetService.hasAvalaraIntegration();
    }

    @AllowRoles({ Role.ADMIN })
    String integrateAvalara(AvalaraIntegrationInput integrationInput) {
        return avalaraIntegrationService.integrate(integrationInput);
    }

    @AllowRoles({ Role.ADMIN })
    AvalaraIntegration getAvalaraIntegrationById(String integrationId) {
        return avalaraIntegrationGetService.getIntegrationById(integrationId);
    }

    @AllowRoles({ Role.ADMIN })
    void pingAvalara(AvalaraIntegrationInput integrationInput) {
        avalaraIntegrationService.pingAvalara(integrationInput);
    }

    @AllowRoles({ Role.ADMIN })
    DocuSignIntegration getDocuSignIntegrationByTenantId(String tenantId) {
        return docuSignAuthenticationService.getIntegrationByTenantId(tenantId);
    }

    @AllowAllRoles
    Boolean hasDocusignIntegration(String tenantId) {
        return docuSignAuthenticationService.hasDocusignIntegration(tenantId);
    }

    @AllowRoles({ Role.ADMIN })
    URI initiateDocuSignIntegration(DocuSignIntegrationRequestJson integrationRequestJson) {
        return docuSignAuthenticationService.initiateIntegration(integrationRequestJson);
    }

    @AllowUnauthenticated(cascade = true)
    void completeDocuSignIntegration(String authorizationCode, String integrationId) {
        docuSignAuthenticationService.handleAuthorizationCodeCallback(authorizationCode, integrationId);
    }

    @AllowRoles({ Role.ADMIN })
    DocuSignIntegration deleteDocuSignIntegration(String tenantId) {
        return docuSignAuthenticationService.deleteDocuSignIntegration(tenantId);
    }

    @AllowAllRoles
    Optional<Integration> getIntegrationByTargetService(IntegrationTargetService integrationTargetService) {
        return integrationAuthFacadeService.getIntegrationByTargetService(integrationTargetService);
    }

    @AllowAllRoles
    boolean testIntegration(IntegrationTargetService targetService) {
        return integrationAuthFacadeService.testIntegration(targetService);
    }

    @AllowRoles({ Role.ADMIN })
    IntegrationDetail addIntegration(IntegrationRequest integrationRequest) {
        return integrationAuthFacadeService.addIntegration(integrationRequest);
    }

    @AllowRoles({ Role.ADMIN })
    Integration deleteIntegration(UUID integrationId) {
        return integrationFacadeService.deleteIntegration(integrationId);
    }

    @AllowUnauthenticated(cascade = true)
    Integration receiveAuthCallback(String authCode, UUID integrationId, String realmId) {
        return integrationAuthFacadeService.receiveAuthCallback(authCode, integrationId, realmId);
    }

    @AllowAllRoles
    DocumentTemplate getDocumentTemplateByTemplateId(String templateId) {
        return documentTemplateGetService.getDocumentTemplateByTemplateId(templateId);
    }

    @AllowAllRoles
    DocumentTemplate getDocumentTemplateByTemplateId(String templateId, int version) {
        return documentTemplateGetService.getDocumentTemplateByTemplateId(templateId, version);
    }

    @AllowAllRoles
    List<DocumentTemplate> getDocumentTemplates(DocumentTemplateType type) {
        return documentTemplateGetService.getDocumentTemplates(type);
    }

    @AllowAllRoles
    List<DocumentTemplate> getDocumentTemplateVersions(String templateId) {
        return documentTemplateGetService.getDocumentTemplateVersions(templateId);
    }

    @AllowAllRoles
    List<DocumentMasterTemplate> getDocumentMasterTemplates(DocumentTemplateType templateType) {
        return documentTemplateGetService.getDocumentMasterTemplates(templateType);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    @AllowAllRoles
    Optional<DocumentMasterTemplate> getDocumentMasterTemplateById(String documentMasterTemplateId, Optional<String> optionalTenantId) {
        return documentTemplateGetService.getDocumentMasterTemplateById(documentMasterTemplateId, optionalTenantId);
    }

    @AllowAllRoles
    void populateInUseFlagForMasterTemplate(DocumentMasterTemplate masterTemplate) {
        documentTemplateService.populateInUseFlagForMasterTemplate(masterTemplate);
    }

    @AllowAllRoles
    List<DocumentSection> getDocumentSections() {
        return documentTemplateGetService.getDocumentSections();
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    DocumentTemplate addDocumentTemplate(DocumentTemplate documentTemplate) {
        return documentTemplateService.addDocumentTemplate(documentTemplate);
    }

    @AllowRoles({ Role.ADMIN })
    void updateDocumentTemplate(DocumentTemplate documentTemplate) {
        documentTemplateService.updateDocumentTemplate(documentTemplate);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    void updateDocumentTemplateStatus(DocumentTemplate documentTemplate, DocumentTemplateStatus documentTemplateStatus) {
        documentTemplateService.updateDocumentTemplateStatus(documentTemplate, documentTemplateStatus);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    List<DocumentTemplate> deleteDocumentTemplates(String templateId, Optional<String> optionalTenantId) {
        return documentTemplateService.deleteDocumentTemplates(templateId, optionalTenantId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    DocumentSection upsertDocumentSection(DocumentSection documentSection) {
        return documentTemplateService.upsertDocumentSection(documentSection);
    }

    @AllowRoles({ Role.ADMIN, Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    DocumentMasterTemplate upsertDocumentMasterTemplate(DocumentMasterTemplate documentMasterTemplate, Optional<String> optionalTenantId) {
        return documentTemplateService.upsertDocumentMasterTemplate(documentMasterTemplate, optionalTenantId);
    }

    @AllowRoles({ Role.ADMIN, Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    DocumentMasterTemplate upsertDocumentMasterTemplateV2(DocumentMasterTemplate documentMasterTemplate, Optional<String> optionalTenantId) {
        return documentTemplateService.upsertDocumentMasterTemplateV2(documentMasterTemplate, optionalTenantId);
    }

    @AllowRoles({ Role.ADMIN, Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    void updateDocumentMasterTemplateStatus(
        DocumentMasterTemplate documentMasterTemplate,
        DocumentMasterTemplateStatus documentMasterTemplateStatus,
        Optional<String> optionalTenantId
    ) {
        documentTemplateService.updateDocumentMasterTemplateStatus(documentMasterTemplate, documentMasterTemplateStatus, optionalTenantId);
    }

    @AllowRoles({ Role.ADMIN, Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    DocumentMasterTemplate deleteDocumentMasterTemplate(String documentMasterTemplateId, Optional<String> optionalTenantId) {
        return documentTemplateService.deleteDocumentMasterTemplate(documentMasterTemplateId, optionalTenantId);
    }

    @AllowRoles(
        { Role.ADMIN, Role.FINANCE, Role.SALES_MANAGER, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.READ_ONLY, Role.EXECUTIVE }
    )
    PredefinedReportDefsJson getPredefinedReportDefs() {
        return reportService.getPredefinedReportDefs();
    }

    @AllowRoles(
        { Role.ADMIN, Role.FINANCE, Role.SALES_MANAGER, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.READ_ONLY, Role.EXECUTIVE }
    )
    ReportJobResponse generatePredefinedReport(PredefinedReportJson reportRequest) {
        return reportService.generatePredefinedReport(reportRequest);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.READ_ONLY, Role.EXECUTIVE })
    Optional<Integration> getErpIntegration() {
        return erpIntegrationService.getErpIntegration();
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.READ_ONLY, Role.EXECUTIVE })
    List<TenantJob> getActiveErpSyncTasks() {
        return erpSyncQueueService.getActiveSyncTasks();
    }

    @AllowRoles({ Role.ADMIN })
    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    ApiKeyTokenAndSecret generateNewApiKey(
        Optional<Role> role,
        Optional<Instant> expiry,
        Optional<String> note,
        Optional<String> userId,
        Optional<String> entityId
    ) {
        return apiKeyService.generateNewApiKey(role, expiry, note, userId, entityId);
    }

    @AllowRoles({ Role.ADMIN })
    void revokeApiKey(String keyId) {
        apiKeyService.revokeApiKey(keyId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    InvoiceEmailResponse sendInvoiceEmail(Invoice.Number invoiceNumber) {
        return invoiceEmailService.sendInvoiceEmail(invoiceNumber);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    AccountContact addEmailContact(String subscriptionId, String emailContactId) {
        return emailContactListService.addEmailContact(subscriptionId, emailContactId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    AccountContact deleteEmailContact(String subscriptionId, String emailContactId) {
        return emailContactListService.deleteEmailContact(subscriptionId, emailContactId);
    }

    @AllowAllRoles
    @Deprecated
    List<Opportunity> getOpportunitiesByHubSpotCompanyId(String companyId) {
        List<Opportunity> standaloneOpportunities = crmService.getOpportunitiesByAccountCrmId(companyId);
        if (CollectionUtils.isNotEmpty(standaloneOpportunities)) {
            return standaloneOpportunities;
        }
        return hubSpotGetService.getOpportunitiesByHubSpotCompanyId(companyId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Account getCompanyFromHubSpotById(String companyId) {
        return hubSpotService.getCompanyFromHubSpotById(companyId);
    }

    @AllowAllRoles
    Optional<Account> getAccountByCrmId(String accountCrmId) {
        return accountGetService.getAccountByCrmId(accountCrmId);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Account getOrCreateAccountByCrmId(String accountCrmId, Optional<String> optionalOpportunityCrmId) {
        return crmService.getOrCreateAccount(accountCrmId, optionalOpportunityCrmId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    Account importAccountByCrmId(String accountCrmId) {
        return crmService.importAccount(accountCrmId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    @Deprecated
    void updatePrimaryOrderIdForHubSpotOpportunity(Order order) {
        hubSpotService.updatePrimaryOrderIdForOpportunity(order);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void updatePrimaryOrderIdForOpportunity(Order order) {
        crmService.updatePrimaryOrderIdForOpportunity(order);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void syncOrderToHubSpot(Order order) {
        crmService.pushOrderSync(order);
    }

    @AllowRoles({ Role.ADMIN })
    List<HubSpotSetupMessage> setupHubSpotCustomObjects(String privateKey) {
        return hubSpotInstallService.setupHubSpotCustomObjects(privateKey);
    }

    @AllowRoles({ Role.ADMIN })
    List<HubSpotSetupMessage> setupHubSpotCustomProperties() {
        return hubSpotInstallService.setupHubSpotCustomProperties();
    }

    @AllowAllRoles
    List<HubSpotSetupMessage> verifyHubSpotSetup() {
        return hubSpotInstallService.verifyHubSpotSetup();
    }

    @AllowAllRoles
    HubSpotIntegration getHubSpotIntegrationForTenant() {
        return hubSpotIntegrationService.getIntegrationForTenant();
    }

    @AllowRoles({ Role.ADMIN })
    String initiateHubSpotIntegration(String email) {
        return hubSpotIntegrationService.initiateIntegration(email);
    }

    @AllowUnauthenticated(cascade = true)
    HubSpotIntegration completeHubSpotIntegration(String authorizationCode, String integrationId, String redirectUri) throws IOException {
        return hubSpotIntegrationService.handleAuthorizationCodeCallback(authorizationCode, integrationId, redirectUri);
    }

    @AllowRoles({ Role.ADMIN })
    void deleteHubSpotIntegration() {
        hubSpotIntegrationService.deleteIntegration();
    }

    @AllowAllRoles
    void throwIfTenantAlreadyHasACrmIntegration() {
        crmIntegrationService.throwIfTenantAlreadyHasACrmIntegration();
    }

    @AllowAllRoles
    List<ApprovalFlow> getApprovalFlows() {
        return approvalFlowService.getApprovalFlows();
    }

    @AllowAllRoles
    ApprovalFlow getApprovalFlow(String approvalFlowId) {
        return approvalFlowService.getApprovalFlow(approvalFlowId);
    }

    @AllowRoles({ Role.ADMIN })
    ApprovalFlow upsertApprovalFlow(ApprovalFlow approvalFlow) {
        return approvalFlowService.upsertApprovalFlow(approvalFlow);
    }

    @AllowRoles({ Role.ADMIN })
    ApprovalFlow deleteApprovalFlow(String approvalFlowId) {
        return approvalFlowService.deleteApprovalFlow(approvalFlowId);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    @AllowAllRoles
    Optional<ApprovalFlowInstanceGroup> updateApprovalFlowStates(ApprovalFlowInstanceStateUpdates approvalFlowStateUpdates, Optional<User> approver) {
        return approvalFlowStateUpdaterService.updateApprovalFlowStates(approvalFlowStateUpdates, approver);
    }

    @AllowUnauthenticated(cascade = true)
    ApprovalWithCodeResponse updateApprovalFlowStateWithCode(String code, boolean approve) {
        return approvalFlowStateUpdaterService.updateApprovalFlowStateWithCode(code, approve);
    }

    @AllowAllRoles
    List<ApprovalFlowInstanceGroup> getApprovalFlowInstances(String orderId) {
        Order order = orderGetService.getOrderByOrderId(orderId);
        return approvalFlowInstanceService.getApprovalFlowInstances(orderId, order.getStatus() == OrderStatus.DRAFT);
    }

    @AllowAllRoles
    List<PendingOrderApprovalFlow> getPendingOrderApprovalFlowsForUser(User user) {
        return approvalFlowPendingUserActionService.getPendingOrderApprovalFlowsForUser(user);
    }

    @AllowAllRoles
    List<ApprovalRole> getApprovalRoles() {
        return approvalFlowHierarchyService.getApprovalRoles();
    }

    @AllowAllRoles
    ApprovalRole getApprovalRoleByApprovalRoleId(String approvalRoleId) {
        return approvalFlowHierarchyService.getApprovalRoleByApprovalRoleId(approvalRoleId);
    }

    @AllowAllRoles
    ApprovalSegment getApprovalSegmentByApprovalSegmentId(String approvalSegmentId, boolean includeDeleted) {
        return approvalFlowHierarchyService.getApprovalSegmentByApprovalSegmentId(approvalSegmentId, includeDeleted);
    }

    @AllowAllRoles
    List<ApprovalSegment> getSegmentsForUser(String userId) {
        return approvalFlowHierarchyService.getSegmentsForUser(userId);
    }

    @AllowAllRoles
    List<ApprovalSegment> getApprovalSegments() {
        return approvalFlowHierarchyService.getApprovalSegments();
    }

    @AllowRoles({ Role.ADMIN })
    ApprovalRole upsertApprovalRole(ApprovalRole approvalRole) {
        return approvalFlowHierarchyService.upsertApprovalRole(approvalRole);
    }

    @AllowRoles({ Role.ADMIN })
    ApprovalRole deleteApprovalRole(String approvalRoleId) {
        return approvalFlowHierarchyService.deleteApprovalRole(approvalRoleId);
    }

    @AllowRoles({ Role.ADMIN })
    ApprovalSegment upsertApprovalSegment(ApprovalSegment approvalSegment) {
        return approvalFlowHierarchyService.upsertApprovalSegment(approvalSegment);
    }

    @AllowRoles({ Role.ADMIN })
    ApprovalSegment deleteApprovalSegment(String approvalSegmentId) {
        return approvalFlowHierarchyService.deleteApprovalSegment(approvalSegmentId);
    }

    @AllowAllRoles
    List<RecognitionRule> getRecognitionRules() {
        return revenueRecognitionGetService.getRecognitionRules();
    }

    @AllowAllRoles
    Optional<RecognitionRule> getRecognitionRuleByRuleId(String ruleId) {
        return revenueRecognitionGetService.getRecognitionRuleByRuleId(ruleId);
    }

    @AllowAllRoles
    Optional<SubscriptionChargeRecognition> getSubscriptionChargeRecognition(String subscriptionId, String chargeId) {
        return revenueRecognitionService.getSubscriptionChargeRecognition(subscriptionId, chargeId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.REVENUE_CLERK })
    RecognitionRule upsertRecognitionRule(RecognitionRule recognitionRule) {
        return revenueRecognitionService.upsertRecognitionRule(recognitionRule);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    RecognitionRule deleteRecognitionRule(String recognitionRuleId) {
        return revenueRecognitionService.deleteRecognitionRule(recognitionRuleId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.REVENUE_CLERK })
    void recognizeRevenue() {
        AccountingPeriod currentAccountingPeriod = accountingPeriodService
            .getCurrentAccountingPeriod()
            .orElseThrow(() -> new InvalidInputException("Current accounting period not defined cannot recognize revenue"));
        revenueRecognitionService.recognizeRevenueAsync(currentAccountingPeriod.getAccountingPeriodId());
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.READ_ONLY, Role.EXECUTIVE })
    List<AccountingPeriod> getRecentAccountingPeriods(Long recentNumberOfMonths) {
        return accountingPeriodService.getRecentAccountingPeriods(recentNumberOfMonths);
    }

    @AllowAllRoles
    List<AccountingPeriod> getAccountingPeriodsBetween(Period period) {
        return accountingPeriodService.getAccountingPeriodsBetween(period);
    }

    @AllowAllRoles
    AccountingPeriod getAccountingPeriod(String accountingPeriodId) {
        return accountingPeriodService.getAccountingPeriod(accountingPeriodId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.READ_ONLY, Role.EXECUTIVE })
    void fetchOpenedByUserForAccountingPeriod(AccountingPeriod accountingPeriod) {
        accountingPeriodService.fetchOpenedByUser(accountingPeriod);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK, Role.READ_ONLY, Role.EXECUTIVE })
    void fetchClosedByUserForAccountingPeriod(AccountingPeriod accountingPeriod) {
        accountingPeriodService.fetchClosedByUser(accountingPeriod);
    }

    @AllowAllRoles
    Optional<AccountingPeriod> getCurrentAccountingPeriod() {
        return accountingPeriodService.getCurrentAccountingPeriod();
    }

    @AllowAllRoles
    Optional<AccountingPeriod> getCurrentAccountingPeriodByEntityId(String entityId) {
        return accountingPeriodService.getCurrentAccountingPeriod(entityId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT })
    AccountingPeriod specifyCurrentAccountingPeriod(Instant periodStart) {
        return accountingPeriodService.specifyCurrentAccountingPeriod(periodStart);
    }

    @AllowAllRoles
    void populateAccountingPeriodCalculation(AccountingPeriod accountingPeriod) {
        accountingPeriodCalculationService.populateAccountingPeriodCalculation(accountingPeriod);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT })
    void performAccountingPeriodCloseWorkflowChecks(AccountingPeriod accountingPeriod) {
        accountingPeriodStatusUpdateService.performAccountingPeriodCloseWorkflowChecks(accountingPeriod);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN, Role.ACCOUNTANT })
    void moveAccountingPeriodToCloseInProgress(String accountingPeriodId) {
        accountingPeriodStatusUpdateService.moveAccountingPeriodToCloseInProgress(accountingPeriodId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN, Role.ACCOUNTANT })
    AccountingPeriod reopenAccountingPeriod(String accountingPeriodId) {
        return accountingPeriodStatusUpdateService.reopenAccountingPeriod(accountingPeriodId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN, Role.ACCOUNTANT })
    AccountingPeriod closeAccountingPeriod(String accountingPeriodId) {
        return accountingPeriodStatusUpdateService.closeAccountingPeriod(accountingPeriodId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN, Role.ACCOUNTANT })
    AccountingPeriod abandonAccountingPeriodClose(String accountingPeriodId) {
        return accountingPeriodStatusUpdateService.abandonAccountingPeriodClose(accountingPeriodId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT })
    void writeJournalEntriesForAccountingPeriod(String accountingPeriodId) {
        journalEntryService.writeJournalEntriesForAccountingPeriodAsync(accountingPeriodId);
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN, Role.ACCOUNTANT })
    String syncJournalEntriesToErp(String accountingPeriodId) {
        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(accountingPeriodId);
        if (accountingPeriod.getStatus() != AccountingPeriodStatus.CLOSED) {
            throw new ConflictingStateException(
                String.format("accounting period must be closed to sync journal entries to erp, but was %s", accountingPeriod.getStatus())
            );
        }
        Optional<String> jobId = erpSyncQueueService.dispatchCreationJob(accountingPeriodId);
        if (jobId.isEmpty()) {
            throw new ServiceFailureException("unable to create erp journal entry sync task, possibly due to erp integration missing");
        }
        return jobId.get();
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN, Role.ACCOUNTANT })
    String deleteJournalEntriesFromErp(String accountingPeriodId) {
        AccountingPeriod accountingPeriod = accountingPeriodService.getAccountingPeriod(accountingPeriodId);
        if (accountingPeriod.getStatus() != AccountingPeriodStatus.OPEN) {
            throw new ConflictingStateException(
                String.format("accounting period must be open to delete journal entries from erp, but was %s", accountingPeriod.getStatus())
            );
        }
        Optional<String> jobId = erpSyncQueueService.dispatchDeletionJob(accountingPeriodId);
        if (jobId.isEmpty()) {
            throw new ServiceFailureException("unable to create erp journal entry delete task, possibly due to erp integration missing");
        }
        return jobId.get();
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN, Role.ACCOUNTANT })
    String syncInvoiceToErp(String invoiceNumber) {
        Invoice invoice = invoiceRetrievalService.getInvoice(new Invoice.Number(invoiceNumber));
        if (invoice.getStatus() != InvoiceStatus.POSTED) {
            throw new InvalidInputException(String.format("invoice %s is %s instead of POSTED", invoiceNumber, invoice.getStatus()));
        }
        Optional<String> jobId = erpSyncQueueService.dispatchInvoiceSyncJob(invoiceNumber);
        if (jobId.isEmpty()) {
            throw new ServiceFailureException("unable to create erp invoice sync task, possibly due to erp integration missing");
        }
        return jobId.get();
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN, Role.ACCOUNTANT })
    String syncCreditMemoToErp(String creditMemoNumber) {
        CreditMemo creditMemo = creditMemoRetrievalService.getCreditMemoByNumber(creditMemoNumber);
        if (creditMemo.getStatus() != CreditMemoStatus.POSTED) {
            throw new InvalidInputException(String.format("credit memo %s is %s instead of POSTED", creditMemoNumber, creditMemo.getStatus()));
        }
        Optional<String> jobId = erpSyncQueueService.dispatchCreditMemoSyncJob(creditMemoNumber);
        if (jobId.isEmpty()) {
            throw new ServiceFailureException("unable to create erp credit memo sync task, possibly due to erp integration missing");
        }
        return jobId.get();
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN, Role.ACCOUNTANT })
    String voidInvoiceOnErp(String invoiceNumber) {
        Invoice invoice = invoiceRetrievalService.getInvoice(new Invoice.Number(invoiceNumber));
        if (invoice.getStatus() != InvoiceStatus.VOIDED) {
            throw new InvalidInputException(String.format("invoice %s is %s instead of VOIDED", invoiceNumber, invoice.getStatus()));
        }
        Optional<String> jobId = erpSyncQueueService.dispatchVoidInvoiceSyncJob(invoiceNumber);
        if (jobId.isEmpty()) {
            throw new ServiceFailureException("unable to create erp void invoice sync task, possibly due to erp integration missing");
        }
        return jobId.get();
    }

    @AllowAllRoles
    List<LedgerAccount> getLedgerAccounts() {
        return accountingGetService.getLedgerAccounts();
    }

    @AllowAllRoles
    List<LedgerAccount> getDefaultLedgerAccountTemplates() {
        return accountingGetService.getDefaultLedgerAccountTemplates();
    }

    @AllowAllRoles
    List<LedgerAccount> getAllLedgerAccountTemplates() {
        return accountingGetService.getAllLedgerAccountTemplates();
    }

    @AllowAllRoles
    List<LedgerAccount> getDefaultLedgerAccounts() {
        return accountingGetService.getDefaultLedgerAccounts();
    }

    @AllowRoles({ Role.FINANCE, Role.ADMIN })
    void mapLedgerAccountsToCharge(String chargeId, List<String> ledgerAccountIdsList) {
        accountingService.mapLedgerAccountsToCharge(chargeId, ledgerAccountIdsList);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT })
    LedgerAccount upsertLedgerAccount(LedgerAccount ledgerAccount) {
        return accountingService.upsertLedgerAccount(ledgerAccount);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT })
    LedgerAccount deleteLedgerAccount(String ledgerAccountId) {
        return accountingService.deleteLedgerAccount(ledgerAccountId);
    }

    @AllowUnauthenticated
    boolean isEnabled(Feature feature) {
        return featureService.isEnabled(feature);
    }

    @AllowAllRoles
    Map<String, Boolean> getAllFeatureFlags() {
        return featureService.getAll();
    }

    @AllowAllRoles
    Map<Feature, Boolean> getAllFeatureFlagsWithProperEnumKeys() {
        return featureService.getAllFeatureFlagsWithProperEnumKeys();
    }

    @AllowAllRoles
    SubscriptionChargeAlias getSubscriptionChargeAlias(String aliasId) {
        return aliasService.getSubscriptionChargeAlias(aliasId);
    }

    @AllowAllRoles
    List<SubscriptionChargeAlias> getAliasesBySubscriptionIdAndChargeId(String subscriptionId, String chargeId) {
        return aliasService.getAliasesBySubscriptionIdAndChargeId(subscriptionId, chargeId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK })
    SubscriptionChargeAlias addSubscriptionChargeAlias(String aliasId, String subscriptionId, String chargeId) {
        return aliasService.addSubscriptionChargeAlias(aliasId, subscriptionId, chargeId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.ACCOUNTANT, Role.BILLING_CLERK, Role.REVENUE_CLERK })
    SubscriptionChargeAlias deleteSubscriptionChargeAlias(String aliasId) {
        return aliasService.deleteSubscriptionChargeAlias(aliasId);
    }

    @AllowAllRoles
    PaymentConfiguration getPaymentConfiguration() {
        return paymentConfigurationService.getTenantPaymentConfiguration();
    }

    @AllowRoles({ Role.ADMIN })
    PaymentConfiguration updateAutomaticPaymentConfiguration(PaymentConfiguration newPaymentConfiguration) {
        return paymentConfigurationService.updateAutomaticPaymentConfiguration(newPaymentConfiguration);
    }

    @AllowRoles({ Role.ADMIN })
    PaymentConfiguration updatePaymentConfiguration(PaymentConfiguration newPaymentConfiguration) {
        return paymentConfigurationService.updatePaymentConfiguration(newPaymentConfiguration);
    }

    @AllowAllRoles
    List<Attachment> getAccountAttachments(String accountId) {
        return attachmentsService.getAccountAttachments(accountId);
    }

    @AllowAllRoles
    Attachment getAttachmentByAttachmentId(UUID attachmentId) {
        return attachmentsService.getAttachmentById(attachmentId);
    }

    @AllowAllRoles
    Optional<EnabledPlatformFeature> getFeatureEnablement(PlatformFeature platformFeature) {
        return platformFeatureService.getFeatureEnablement(platformFeature);
    }

    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    Optional<EnabledPlatformFeature> getFeatureEnablementInTenant(PlatformFeature platformFeature, String tenantId) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        return platformFeatureService.getFeatureEnablement(platformFeature, tenantId);
    }

    @AllowRoles({ Role.ADMIN })
    void enableAccountingPlatformFeature() {
        platformFeatureService.enablePlatformFeature(PlatformFeature.ACCOUNTING);
    }

    @AllowAllRoles
    List<CustomFieldDefinition> getCustomFieldDefinitions(CustomFieldParentType parentObjectType) {
        return customFieldService.getCustomFieldDefinitions(parentObjectType);
    }

    @AllowRoles({ Role.ADMIN })
    CustomFieldDefinition createCustomFieldDefinition(CustomFieldDefinition customFieldDefinition) {
        return customFieldService.createCustomFieldDefinition(customFieldDefinition);
    }

    @AllowRoles({ Role.ADMIN })
    CustomFieldDefinition updateCustomFieldDefinition(CustomFieldDefinitionUpdate customFieldDefinitionUpdate) {
        return customFieldService.updateCustomFieldDefinition(customFieldDefinitionUpdate);
    }

    @AllowRoles({ Role.ADMIN })
    CustomFieldDefinition deleteCustomFieldDefinition(String customFieldDefinitionId) {
        return customFieldService.deleteCustomFieldDefinition(customFieldDefinitionId);
    }

    @AllowAllRoles
    List<CustomFieldEntry> getCustomFields(CustomFieldParentType type, String parentId) {
        CustomField customField = customFieldProxy.getCustomFields(type, parentId);
        List<CustomFieldEntry> entries = customFieldAPIMapper.toCustomFieldEntries(customField);
        return customizationService.runOrderLineItemCustomFieldCustomization(type, parentId, entries);
    }

    @AllowAllRoles
    Optional<UIConfiguration> getUIConfiguration() {
        return uiConfigurationService.getUIConfiguration();
    }

    @AllowAllRoles
    List<CustomFieldWithParentReference> getCustomFields(CustomFieldParentType type, List<String> parentObjectIds) {
        Map<String, CustomField> customFields = customFieldProxy.getCustomFields(type, parentObjectIds);
        return customFields
            .entrySet()
            .stream()
            .map(entry -> new CustomFieldWithParentReference(entry.getKey(), customFieldAPIMapper.toCustomFieldEntries(entry.getValue())))
            .toList();
    }

    @Deprecated // use runSelectionCustomization instead
    @AllowAllRoles
    List<@GraphQLNonNull Action> runPlanAdditionCustomization(PlanAdditionCustomizationInput customizationInput) {
        return customizationService.runPlanAdditionCustomization(customizationInput);
    }

    @AllowAllRoles
    List<@GraphQLNonNull Action> runSelectionCustomization(SelectionCustomizationInput customizationInput) {
        return customizationService.runSelectionCustomization(customizationInput);
    }

    @AllowAllRoles
    List<CustomFieldEntry> updateCustomFields(CustomFieldParentType type, String parentId, List<CustomFieldInput> customFieldInputs) {
        CustomField customField = customFieldAPIMapper.inputsToCustomField(customFieldInputs);
        return customFieldAPIMapper.toCustomFieldEntries(customFieldProxy.updateCustomFields(type, parentId, customField, false));
    }

    @AllowRoles({ Role.ADMIN })
    void addSamlIntegration(String tenantId, String metadataUrl, String providerName, Map<String, String> attributeMapping) {
        cognitoService.addSamlIntegration(tenantId, metadataUrl, providerName, attributeMapping);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    InvoiceDetail voidInvoice(Invoice.Number invoiceNumber, BigDecimal invoiceBalance, Instant voidInvoiceDate) {
        voidInvoiceService.voidInvoice(invoiceNumber, invoiceBalance, voidInvoiceDate);
        return invoiceDataAggregator.getInvoiceDetail(invoiceNumber);
    }

    @AllowRoles({ Role.ADMIN })
    List<ApiKeyDetail> getApiKeys(boolean activeOnly) {
        List<ApiKeyMetaData> apiKeys;

        if (activeOnly) {
            apiKeys = apiKeyService.getAllActiveApiKeys();
        } else {
            apiKeys = apiKeyService.getAllApiKeys();
        }

        return apiKeyDetailMapper.convertToResponseObjects(apiKeys);
    }

    @AllowRoles({ Role.ADMIN })
    ApiKeyDetail getApiKey(String apiKeyId) {
        ApiKeyMetaData apiKeyMetaData = apiKeyService.getApiKeyById(apiKeyId);
        return apiKeyDetailMapper.convertToResponseObject(apiKeyMetaData);
    }

    @AllowAllRoles
    void createOrderDocument(String orderId) {
        Order order = orderGetService.getOrderByOrderId(orderId);
        if (StringUtils.isNotBlank(order.getCompositeOrderId())) {
            throw new IllegalArgumentException("Use createCompositeOrderDocument mutation to generate pdf for composite orders");
        }
        if (!order.getShouldRegeneratePdf()) {
            return;
        }

        orderDocumentService.createOrderDocumentWithoutChecks(orderId);
    }

    @AllowAllRoles
    PaymentTermSettingsJson getPaymentTermSettings() {
        PaymentTermSettings paymentTermSettings = paymentTermSettingsService.getPaymentTermSettings();
        return paymentTermSettingsJsonMapper.paymentTermSettingsToJson(paymentTermSettings);
    }

    @AllowRoles({ Role.ADMIN })
    PaymentTermSettingsJson updatePaymentTermSettings(PaymentTermSettingsJson paymentTermSettingsJson) {
        PaymentTermSettings paymentTermSettings = paymentTermSettingsJsonMapper.jsonToPaymentTermSettings(paymentTermSettingsJson);
        paymentTermSettingsService.updatePaymentTermSettings(paymentTermSettings);
        return getPaymentTermSettings();
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    CompositeOrderDetail createUpsellWithEarlyRenewal(
        String subscriptionId,
        String crmOpportunityId,
        OpportunityInput opportunityInput,
        Optional<AmendAndRenewCreateRequest> amendAndRenewCreateRequest
    ) {
        String compositeOrderId = compositeOrderService.createUpsellEarlyRenewal(
            subscriptionId,
            crmOpportunityId,
            opportunityInput,
            amendAndRenewCreateRequest
        );

        return getCompositeOrderDetail(compositeOrderId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    CompositeOrderDetail updateCompositeOrderFields(String compositeOrderId, CompositeOrderFieldsUpdateRequest compositeOrderFieldsUpdateRequest) {
        compositeOrderService.updateCompositeOrderFields(compositeOrderId, compositeOrderFieldsUpdateRequest);
        return getCompositeOrderDetail(compositeOrderId);
    }

    @AllowAllRoles
    CompositeOrderDetail getCompositeOrderDetail(String compositeOrderId) {
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        List<String> orderIdsInCompositeOrder = compositeOrderGetService.getOrderIdsInCompositeOrder(compositeOrderId);

        List<OrderDetail> orderDetails = new ArrayList<>();
        orderIdsInCompositeOrder.forEach(orderId -> orderDetails.add(orderDataAggregator.getOrderDetail(orderId, false, false)));
        return compositeOrderMapper.compositeOrderToDetail(compositeOrder, orderIdsInCompositeOrder, orderDetails);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void updateCompositeOrderStatus(String compositeOrderId, OrderStatus status, Long statusUpdatedOn, boolean adminApprovalByPass) {
        Optional<Instant> statusUpdatedOnInstant = Optional.ofNullable(statusUpdatedOn).map(Instant::ofEpochSecond);
        compositeOrderService.updateCompositeOrderStatus(compositeOrderId, status, statusUpdatedOnInstant, adminApprovalByPass);
    }

    @AllowAllRoles
    void createCompositeOrderPdf(String compositeOrderId) {
        compositeOrderDocumentService.createCompositeOrderDocumentWithoutChecks(compositeOrderId);
    }

    @AllowAllRoles
    List<EsignTenantSignatoryDetail> getTenantSignatories() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        List<EsignTenantSignatory> tenantSignatories = esignService.getTenantSignatories(tenantId);
        // TODO: refactor mapping logic
        return tenantSignatories
            .stream()
            .map(s -> {
                var detail = new EsignTenantSignatoryDetail();
                User user = userService.getUser(s.getUserId());
                detail.setUser(userMapper.userToJson(user));
                return detail;
            })
            .toList();
    }

    @AllowRoles({ Role.ADMIN })
    void insertTenantSignatory(EsignTenantSignatory signatory) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        esignService.insertTenantSignatory(signatory, tenantId);
    }

    @AllowRoles({ Role.ADMIN })
    void deleteTenantSignatory(String userId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        esignService.deleteTenantSignatory(userId, tenantId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void sendEmailForEsign(EsignRequest esignRequest) {
        esignService.requestEsignatureViaPandaDoc(esignRequest, false);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void sendEmailForDocusign(EsignRequest esignRequest) {
        esignService.requestEsignatureViaDocusign(esignRequest);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void voidEsignDocumentV2(String id) {
        esignService.voidEsignatureDocument(UUID.fromString(id));
    }

    @AllowAllRoles
    ElectronicSignatureDetail getElectronicSignatureDetailByOrderId(String orderId) {
        return esignDataAggregator.getEsignDetailByOrderId(orderId);
    }

    @AllowAllRoles
    List<ImmutableElectronicSignatureAuditLog> getElectronicSignatureAuditLogs(String electronicSignatureId) {
        return esignService.getElectronicSignatureAuditLogs(electronicSignatureId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void resendEsignatureDocumentV2(String id) {
        esignService.resendEsignatureDocument(UUID.fromString(id));
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES })
    String getOrCreateDocumentLink(String orderId) {
        return documentLinkService.getOrCreateDocumentLink(orderId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES })
    String getOrCreateDocumentLinkIdForCLM(String orderId) {
        return documentLinkService.getOrCreateDocumentLinkIdForCLM(orderId);
    }

    @AllowAllRoles
    List<PaymentDetail> getRefundablePayments(String accountId) {
        return paymentDataAggregator.getRefundablePayments(accountId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER, Role.BILLING_CLERK })
    void updateAccountSupportedPaymentTypes(String accountId, Set<PaymentType> supportedPaymentTypes) {
        accountService.updateAccountSupportedPaymentTypes(accountId, supportedPaymentTypes);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    BillingEvents addBillingEvent(BillingEventInput billingEventInput) {
        billingScheduleService.addBillingScheduleEntry(
            billingEventInput.subscriptionId(),
            billingEventInput.subscriptionChargeId(),
            Instant.ofEpochSecond(billingEventInput.triggerOn()),
            billingEventInput.amount()
        );

        BillingSchedule updatedBillingSchedule = billingScheduleService.getBillingSchedules(
            billingEventInput.subscriptionId(),
            billingEventInput.subscriptionChargeId()
        );

        return getBillingEvents(updatedBillingSchedule);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    BillingEvents getBillingEvents(String subscriptionId, String subscriptionChargeId) {
        BillingSchedule schedule = billingScheduleService.getBillingSchedules(subscriptionId, subscriptionChargeId);
        return getBillingEvents(schedule);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    BillingEvents deleteBillingEvent(String id) {
        BillingSchedule schedule = billingScheduleService.deleteBillingSchedule(UUID.fromString(id));
        return getBillingEvents(schedule);
    }

    private BillingEvents getBillingEvents(BillingSchedule schedule) {
        List<BillingEventEntry> entries = billingEventMapper.fromBillingScheduleEntries(schedule.entries());
        return new BillingEvents(schedule.remainingAmount(), entries);
    }

    @AllowUnauthenticated(cascade = true)
    StripeIntentResponse createPaymentIntentWithPaymentLink(String paymentLinkId) {
        InvoicePaymentManagementLink paymentLink = invoicePaymentManagementLinkService.getLinkById(paymentLinkId);
        String tenantId = paymentLink.getTenantId();
        return TenantContextInjector.callInTenantContext(tenantId, tenantIdProvider, () ->
            stripeService.createPaymentIntentWithPaymentLink(paymentLinkId)
        );
    }

    @AllowUnauthenticated(cascade = true)
    StripeSetupIntentResponse createSetupIntentWithPaymentLink(String paymentLinkId) {
        AccountPaymentManagementLink paymentLink = accountPaymentManagementLinkService.getLinkById(paymentLinkId);
        String tenantId = paymentLink.getTenantId();
        return TenantContextInjector.callInTenantContext(tenantId, tenantIdProvider, () ->
            stripeService.createSetupIntentWithPaymentLink(paymentLinkId)
        );
    }

    @AllowUnauthenticated(cascade = true)
    void updatePaymentIntentWithPaymentLink(String paymentLinkId, String stripePaymentIntentId, boolean optInForAutomaticPayments) {
        InvoicePaymentManagementLink paymentLink = invoicePaymentManagementLinkService.getLinkById(paymentLinkId);
        String tenantId = paymentLink.getTenantId();
        TenantContextInjector.runInTenantContext(tenantId, tenantIdProvider, () ->
            stripeService.updatePaymentIntentWithPaymentLink(paymentLink, stripePaymentIntentId, optInForAutomaticPayments)
        );
    }

    @AllowRoles({ Role.ADMIN })
    EscalationPolicy addEscalationPolicy(AddEscalationPolicyRequest addEscalationPolicyRequest) {
        var escalationPolicy = escalationPolicyJsonMapper.addEscalationPolicyRequestToBuilder(addEscalationPolicyRequest);
        String escalationPolicyId = escalationPolicyService.addEscalationPolicy(ImmutableEscalationPolicy.builder().from(escalationPolicy));
        return escalationPolicyService.getEscalationPolicy(escalationPolicyId);
    }

    @AllowRoles({ Role.ADMIN })
    EscalationPolicy updateEscalationPolicy(UpdateEscalationPolicyRequest updateEscalationPolicyRequest) {
        var escalationPolicy = escalationPolicyJsonMapper.updateEscalationPolicyRequestToBuilder(updateEscalationPolicyRequest);
        escalationPolicyService.updateEscalationPolicy(ImmutableEscalationPolicy.builder().from(escalationPolicy));
        return escalationPolicyService.getEscalationPolicy(updateEscalationPolicyRequest.getEscalationPolicyId());
    }

    @AllowRoles({ Role.ADMIN })
    EscalationPolicy getEscalationPolicy(String escalationPolicyId) {
        return escalationPolicyService.getEscalationPolicy(escalationPolicyId);
    }

    @AllowUnauthenticated
    UserService getUserService() {
        return userService;
    }

    @AllowUnauthenticated
    TenantService getTenantService() {
        return tenantService;
    }

    @AllowUnauthenticated(cascade = true)
    CLMThreadCreationDetail createCLMThread(CLMCreateThreadRequest clmThread) {
        return clmService.insertCLMThread(clmThread);
    }

    @AllowUnauthenticated(cascade = true)
    CLMChatMessageDetail addMessageToThread(CLMChatMessageDetail clmAddMessageRequest) {
        return clmService.addMessageToThread(clmAddMessageRequest);
    }

    @AllowUnauthenticated(cascade = true)
    List<CLMThreadDetail> getThreadsForDocument(String documentId) {
        return clmService.getThreadsForDocument(documentId);
    }

    @AllowUnauthenticated(cascade = true)
    void resolveThread(String threadId) {
        clmService.resolveThread(threadId);
    }

    @AllowUnauthenticated(cascade = true)
    void emailLinkForLogin(String email) {
        Validator.validateStringNotBlank(email, "Email cannot be blank.");
        emailLinkLoginService.sendLinkEmailForLogin(email.trim().toLowerCase());
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    RateCard upsertRateCard(RateCard rateCard) {
        return rateCardService.upsertRateCard(rateCard);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    PriceAttribute upsertPriceAttribute(PriceAttribute priceAttribute) {
        return rateCardService.upsertPriceAttribute(priceAttribute);
    }

    @AllowAllRoles
    RateCard getRateCard(String rateCardId) {
        return rateCardService.getRateCard(rateCardId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.RATE_CARD, rateCardId));
    }

    @AllowAllRoles
    RateCard getConvertedRateCardAsOfDate(String rateCardId, String targetCurrencyCode, Instant currencyConversionEffectiveDate) {
        return rateCardService
            .getConvertedRateCardAsOfDate(rateCardId, targetCurrencyCode, currencyConversionEffectiveDate)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.RATE_CARD, rateCardId));
    }

    @AllowAllRoles
    List<RateCard> getRateCards() {
        return rateCardService.getRateCards();
    }

    @AllowAllRoles
    Integer getOrderExpiryDurationInDays() {
        return tenantSettingService.getOrderExpiryDurationInDays();
    }

    @AllowRoles(Role.ADMIN)
    void updateOrderExpiryDurationInDays(String orderExpiryDurationInDays) {
        tenantSettingService.updateOrderExpiryDurationInDays(orderExpiryDurationInDays);
    }

    @AllowAllRoles
    SigningOrder getSigningOrder() {
        return tenantSettingService.getSigningOrder();
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void updateSigningOrder(SigningOrder signingOrder) {
        tenantSettingService.updateSigningOrder(signingOrder);
    }

    @AllowAllRoles
    TenantJob getTenantJob(String jobId) {
        return tenantJobGetService.getTenantJobById(jobId, Optional.empty());
    }

    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    TenantJob getTenantJobAsAdmin(String tenantId, String jobId) {
        return tenantJobGetService.getTenantJobById(jobId, Optional.of(tenantId));
    }

    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    TenantJob retryTenantJobAsAdmin(String tenantId, String jobId) {
        return tenantJobDispatcherService.retry(jobId, Optional.of(tenantId));
    }

    @AllowAllRoles
    List<PriceAttribute> getPriceAttributes() {
        return rateCardService.getPriceAttributes();
    }

    @AllowAllRoles
    List<PaymentType> getSupportedPaymentTypes() {
        return paymentConfigurationService.getSupportedPaymentTypes();
    }

    @AllowAllRoles
    List<Instant> getBillingPeriodStartDates(String subscriptionId) {
        return subscriptionBillingPeriodService.getFutureBillingPeriodStartDates(subscriptionId);
    }

    @AllowAllRoles
    List<SettlementApplicationDetail> getSettlementApplicationDetails(Invoice.Number invoiceNumber) {
        return settlementApplicationDataAggregator.getSettlementApplicationDetails(invoiceNumber);
    }

    @AllowRoles({ Role.ADMIN, Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    String createMergeLinkToken(IntegrationTargetService integrationTargetService) {
        return mergeIntegrationService.createLinkToken(integrationTargetService);
    }

    @AllowRoles({ Role.ADMIN, Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    void processMergePublicToken(String publicToken, IntegrationTargetService integrationTargetService) {
        mergeIntegrationService.processPublicToken(publicToken, integrationTargetService);
    }

    @AllowRoles({ Role.ADMIN })
    PriceAttribute deletePriceAttribute(String priceAttributeId) {
        return rateCardService.deletePriceAttribute(priceAttributeId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE })
    ChargeDefaultAttributeReferences upsertChargeDefaultAttributeReferences(ChargeDefaultAttributeReferences chargeDefaultAttributeReferences) {
        return rateCardService.upsertChargeDefaultAttributeReferences(chargeDefaultAttributeReferences);
    }

    @AllowAllRoles
    List<PaymentDetail> getPaymentDetailsForInvoice(Invoice.Number invoiceNumber) {
        return paymentDataAggregator.getPaymentDetailsForInvoice(invoiceNumber);
    }

    @AllowAllRoles
    ChargeDefaultAttributeReferences getChargeDefaultAttributeReferences(String chargeId) {
        return rateCardService
            .getChargeDefaultAttributeReferencesByChargeId(chargeId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.CHARGE_DEFAULT_ATTRIBUTE_REFERENCES, chargeId));
    }

    @AllowRoles({ Role.ADMIN })
    String integrateTaxJar(TaxJarIntegrationInput input) {
        return taxJarIntegrationService.addIntegration(input);
    }

    @AllowRoles({ Role.ADMIN })
    void testTaxJarIntegration(TaxJarIntegrationInput input) {
        taxJarIntegrationService.testIntegration(input);
    }

    @AllowRoles({ Role.ADMIN })
    TaxJarIntegration getTaxJarIntegrationById(String integrationId) {
        return taxJarIntegrationGetService.getIntegrationById(integrationId);
    }

    @AllowAllRoles
    Optional<TaxJarIntegration> getTaxJarIntegration() {
        return taxJarIntegrationGetService.getTaxJarIntegration();
    }

    @AllowRoles({ Role.ADMIN })
    List<DataImport> getDataImports() {
        return dataImportService.getDataImports();
    }

    @AllowRoles({ Role.ADMIN })
    DataImport getDataImport(String dataImportId) {
        return dataImportService.getDataImportById(dataImportId);
    }

    @AllowRoles({ Role.ADMIN })
    List<FlatfileSpace> getFlatfileSpaces() {
        return flatfileService.getSpaces();
    }

    @AllowRoles({ Role.ADMIN })
    FlatfileSpaceResponse getFlatfileSpace(String spaceId) throws IOException {
        return flatfileService.getSpace(spaceId);
    }

    @AllowRoles({ Role.ADMIN })
    DataImport processImport(String dataImportId) {
        return dataImportService.startProcessing(dataImportId);
    }

    @AllowRoles({ Role.ADMIN })
    FlatfileSpaceResponse createFlatfileWorkbook(ImportDomain importDomain) {
        return flatfileService.createWorkbook(importDomain);
    }

    @AllowRoles({ Role.ADMIN })
    void renewApiKeyForFlatfileSpace(String spaceId) throws IOException {
        flatfileService.renewApiKeyForSpace(spaceId);
    }

    @AllowRoles({ Role.ADMIN })
    void deleteFlatfileSpace(String spaceId) throws IOException {
        flatfileService.deleteSpace(spaceId);
    }

    @AllowAllRoles
    List<EmailContact> getEmailContactsForAccount(String accountId) {
        return emailContactListService.getEmailContactsForAccount(accountId);
    }

    @AllowRoles({ Role.ADMIN })
    NotificationInstanceWithAttempts getNotificationInstance(String notificationInstanceId) {
        return notificationService.getNotificationInstanceWithAttempts(notificationInstanceId);
    }

    @AllowAllRoles
    List<NotificationProcessorTypeSupport> getNotificationProcessorTypeSupport() {
        return notificationService.getSupportedNotificationTypesForTargetTypes();
    }

    @AllowRoles({ Role.ADMIN })
    void resendNotificationInstance(String notificationInstanceId) {
        notificationRetryService.manualRetry(notificationInstanceId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    CompositeOrderDetail deleteUpsellWithEarlyRenewal(String compositeOrderId) {
        CompositeOrderDetail compositeOrderDetail = getCompositeOrderDetail(compositeOrderId);
        compositeOrderService.deleteCompositeOrder(compositeOrderId);
        return compositeOrderDetail;
    }

    @AllowAllRoles
    Optional<LookerUserMapping> getCurrentLookerUser() {
        return lookerService.findLookerUserMappingForCurrentUser();
    }

    @AllowAllRoles
    List<Opportunity> getOpportunitiesByAccountCrmId(String accountId) {
        return crmService.getOpportunitiesByAccountCrmId(accountId);
    }

    @AllowAllRoles
    ElectronicSignatureProvider getElectronicSignatureProvider() {
        return esignIntegrationService.getEsignProvider().orElse(null);
    }

    @AllowAllRoles
    CustomPredefinedTemplateOnOrder upsertCustomPredefinedTemplateOnOrder(CustomPredefinedTemplateOnOrder documentTemplate) {
        CustomTemplateUpdateOnOrder inputTemplate = customPredefinedTemplateOnOrderMapper.toCustomTemplateUpdateOnOrder(documentTemplate);
        CustomTemplateUpdateOnOrder savedTemplate = customTemplateUpdatedOnOrderService.upsert(inputTemplate);
        return customPredefinedTemplateOnOrderMapper.toCustomPredefinedTemplateOnOrder(savedTemplate);
    }

    @AllowAllRoles
    CustomPredefinedTemplateOnOrder deleteCustomPredefinedTemplateOnOrder(String templateId, String orderId) {
        CustomTemplateUpdateOnOrder deletedTemplate = customTemplateUpdatedOnOrderService.deleteCustomUpdatedTemplate(templateId, orderId);
        return customPredefinedTemplateOnOrderMapper.toCustomPredefinedTemplateOnOrder(deletedTemplate);
    }

    @AllowAllRoles
    CustomPredefinedTemplateOnOrder getCustomPredefinedTemplateOnOrder(String templateId, String orderId) {
        CustomTemplateUpdateOnOrder customTemplate = customTemplateUpdatedOnOrderGetService.getCustomUpdatedTemplate(templateId, orderId);
        return customPredefinedTemplateOnOrderMapper.toCustomPredefinedTemplateOnOrder(customTemplate);
    }

    @AllowAllRoles
    void addCustomFieldsToOrderLineItems(OrderDetail orderDetail) {
        List<String> orderLineItemIds = orderDetail.getLineItems().stream().map(OrderLineItemDetail::getId).filter(Objects::nonNull).toList();
        Map<String, List<CustomFieldEntry>> customFieldsByLineItemIdMap;
        if (CollectionUtils.isNotEmpty(orderLineItemIds)) {
            customFieldsByLineItemIdMap = getCustomFields(CustomFieldParentType.ORDER_ITEM, orderLineItemIds)
                .stream()
                .collect(Collectors.toMap(CustomFieldWithParentReference::parentObjectId, CustomFieldWithParentReference::customFieldEntries));
        } else {
            customFieldsByLineItemIdMap = Map.of();
        }

        List<CustomFieldEntry> defaultCustomField = getCustomFields(CustomFieldParentType.ORDER_ITEM, (String) null);
        orderDetail
            .getLineItems()
            .forEach(orderLineItemDetail -> {
                List<CustomFieldEntry> customFields = orderLineItemDetail.getId() == null
                    ? defaultCustomField
                    : customFieldsByLineItemIdMap.getOrDefault(orderLineItemDetail.getId(), defaultCustomField);
                List<CustomFieldEntry> filteredCustomFields = customizationService.runOrderLineItemCustomFieldCustomization(
                    orderLineItemDetail.getPlan().getId(),
                    orderLineItemDetail.getCharge().getId(),
                    customFields
                );
                orderLineItemDetail.setCustomFields(filteredCustomFields);
            });
    }

    @AllowAllRoles
    void hydrateCustomFieldsForOrderLineItems(OrderDetail response) {
        CustomField emptyHydratedCustomField = customFieldService.getEmptyHydratedCustomFieldForParentObjectType(CustomFieldParentType.ORDER_ITEM);
        List<CustomFieldEntry> emptyHydratedCustomFieldEntries = customFieldAPIMapper.toCustomFieldEntries(emptyHydratedCustomField);
        response
            .getLineItems()
            .forEach(orderLineItemDetail -> {
                if (CollectionUtils.isEmpty(orderLineItemDetail.getCustomFields())) {
                    orderLineItemDetail.setCustomFields(emptyHydratedCustomFieldEntries);
                }

                List<CustomFieldEntry> filteredEntries = customizationService.runOrderLineItemCustomFieldCustomization(
                    orderLineItemDetail.getPlan().getId(),
                    orderLineItemDetail.getCharge().getId(),
                    orderLineItemDetail.getCustomFields()
                );
                orderLineItemDetail.setCustomFields(filteredEntries);
            });
    }

    @AllowAllRoles
    void hydrateOrderAndOrderLineCustomFieldsForNewChangeOrder(OrderDetail orderDetail) {
        CustomField emptyHydratedOrderCustomField = customFieldService.getEmptyHydratedCustomFieldForParentObjectType(CustomFieldParentType.ORDER);
        List<CustomFieldEntry> orderCustomFieldEntries = getSourceMappedCustomFieldEntries(
            emptyHydratedOrderCustomField,
            CustomFieldParentType.SUBSCRIPTION,
            orderDetail.getSubscriptionId()
        );
        orderDetail.setCustomFields(orderCustomFieldEntries);

        CustomField emptyHydratedOrderLineCustomField = customFieldService.getEmptyHydratedCustomFieldForParentObjectType(
            CustomFieldParentType.ORDER_ITEM
        );
        orderDetail
            .getLineItems()
            .forEach(lineItem -> {
                List<CustomFieldEntry> customFieldEntries = getSourceMappedCustomFieldEntries(
                    emptyHydratedOrderLineCustomField,
                    CustomFieldParentType.SUBSCRIPTION_ITEM,
                    lineItem.getSubscriptionChargeId()
                );
                lineItem.setCustomFields(customFieldEntries);
            });
    }

    private List<CustomFieldEntry> getSourceMappedCustomFieldEntries(
        CustomField emptyHydratedCustomField,
        CustomFieldParentType sourceParentType,
        String sourceParentId
    ) {
        CustomField sourceCustomField = customFieldProxy.getCustomFields(sourceParentType, sourceParentId);

        Map<String, CustomFieldValue> sourceCustomFieldEntries = sourceCustomField
            .getEntries()
            .values()
            .stream()
            .collect(Collectors.toMap(CustomFieldValue::getName, Function.identity()));

        var fieldsToMap = emptyHydratedCustomField
            .getEntries()
            .entrySet()
            .stream()
            .map(entry -> {
                if (!sourceCustomFieldEntries.containsKey(entry.getValue().getName())) {
                    return entry;
                }
                // todo: validate that values are valid for destination custom field
                return Map.entry(entry.getKey(), sourceCustomFieldEntries.get(entry.getValue().getName()));
            })
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        CustomField customField = customFieldAPIMapper.toCustomField(fieldsToMap);
        return customFieldAPIMapper.toCustomFieldEntries(customField);
    }

    @AllowAllRoles
    SalesRoomLink createSalesRoomLink(String orderId) {
        return salesRoomLinkService.createLink(orderId);
    }

    @AllowUnauthenticated(cascade = true)
    SalesRoom getSalesRoom(String linkId) {
        return salesRoomService.getSalesRoomWithLinkId(linkId);
    }

    @AllowUnauthenticated(cascade = true)
    void disableSalesRoomLink(String linkId) {
        salesRoomLinkService.disableLink(linkId);
    }

    @AllowUnauthenticated(cascade = true)
    SalesRoomLink getSalesRoomLink(String linkId) {
        return salesRoomLinkService.validateAndGetLink(linkId);
    }

    @AllowUnauthenticated
    TenantIdProvider getTenantIdProvider() {
        return tenantIdProvider;
    }

    @AllowUnauthenticated(cascade = true)
    void updateOrderContactsForSalesRoom(String linkId, String billingContactId, String shippingContactId) {
        salesRoomService.updateOrderContacts(linkId, billingContactId, shippingContactId);
    }

    @AllowUnauthenticated(cascade = true)
    Optional<AccountDetail> getResellerAccountForSalesRoom(String linkId) {
        SalesRoomLink salesRoomLink = getSalesRoomLink(linkId);

        Optional<String> resellerAccountId = getResellerAccountIdForSalesRoom(linkId);
        if (resellerAccountId.isEmpty()) {
            return Optional.empty();
        }

        AccountDetail resellerAccount = TenantContextInjector.callInTenantContext(salesRoomLink.getTenantId(), tenantIdProvider, () ->
            getAccountDetail(resellerAccountId.get())
        );

        return Optional.of(resellerAccount);
    }

    @AllowUnauthenticated(cascade = true)
    Optional<String> getResellerAccountIdForSalesRoom(String linkId) {
        SalesRoom salesRoom = getSalesRoom(linkId);
        OrderDetail orderDetail = salesRoom.getOrderDetail();
        if ((orderDetail.getResoldBy() == null) || (orderDetail.getResoldBy().getId() == null)) {
            return Optional.empty();
        }

        return Optional.of(orderDetail.getResoldBy().getId());
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    EsignRequest upsertEsignDetails(EsignRequest esignRequest) {
        return esignService.upsertEsignDetails(esignRequest);
    }

    @AllowAllRoles
    EsignRequest getEsignDetailsForOrder(String orderId) {
        return esignService.getEsignDetailsForOrder(orderId);
    }

    @AllowAllRoles
    Optional<SalesRoomLink> getSalesRoomLinkByOrderId(String orderId) {
        return salesRoomLinkService.getLinkByOrderId(orderId);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void sendEmailForEsignSalesRoom(SalesRoomLink salesRoomLink, EmailContact accountSignatory) {
        var esign = getEsignDetailsForOrder(salesRoomLink.getOrderId());
        if (accountSignatory != null) {
            esign.setAccountSignatory(accountSignatory);
            esign = esignService.upsertEsignDetails(esign);
        }

        if (getFeatureEnablement(PlatformFeature.ESIGN).isPresent()) {
            esignService.requestEsignatureViaPandaDoc(esign, false);
            return;
        }

        if (hasDocusignIntegration(salesRoomLink.getTenantId())) {
            esignService.requestEsignatureViaDocusign(esign);
            return;
        }

        throw new ObjectNotFoundException(BillyObjectType.ESIGNATURE, salesRoomLink.getTenantId());
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    CancelAndRestructureOrderDetail generateCancelAndRestructure(String subscriptionId) {
        List<Order> orders = compositeOrderService.generateCancelAndRestructure(subscriptionId);
        var cancelRestructureOrderDetail = getCancelAndRestructureOrderDetail(orders, null, null, null, null);
        // add custom field defaults directly for generate
        var customFieldsWithDefaults = OrderServiceHelper.populateCustomFieldDefaults(
            cancelRestructureOrderDetail.getCustomFields(),
            CustomFieldParentType.ORDER,
            customFieldService
        );
        return ImmutableCancelAndRestructureOrderDetail.builder().from(cancelRestructureOrderDetail).customFields(customFieldsWithDefaults).build();
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    CancelAndRestructureOrderDetail upsertCancelAndRestructure(CancelAndRestructureRequest cancelAndRestructureRequest, Boolean isDryRun) {
        String crmOpportunityId = cancelAndRestructureRequest.getCrmOpportunityId();
        String crmOpportunityType = cancelAndRestructureRequest.getCrmOpportunityType();
        String crmOpportunityName = cancelAndRestructureRequest.getCrmOpportunityName();
        String crmOpportunityStage = cancelAndRestructureRequest.getCrmOpportunityStage();
        List<Order> resultOrders = compositeOrderService.upsertCancelAndRestructure(cancelAndRestructureRequest, isDryRun);
        return getCancelAndRestructureOrderDetail(resultOrders, crmOpportunityId, crmOpportunityType, crmOpportunityName, crmOpportunityStage);
    }

    private CancelAndRestructureOrderDetail getCancelAndRestructureOrderDetail(
        List<Order> restructureOrders,
        String crmOpportunityId,
        String crmOpportunityType,
        String crmOpportunityName,
        String crmOpportunityStage
    ) {
        CancelAndRestructureOrderDetail cancelAndRestructureOrderDetail = cancelAndRestructureOrderDataAggregator.getCancelAndRestructureOrderDetail(
            restructureOrders
        );
        if (StringUtils.isBlank(crmOpportunityId)) {
            return cancelAndRestructureOrderDetail;
        }
        Optional<Opportunity> opportunityOptional = opportunityGetService.getOptionalOpportunityByCrmOpportunityId(crmOpportunityId);
        if (opportunityOptional.isPresent()) {
            Opportunity opportunity = opportunityOptional.get();
            crmOpportunityId = opportunity.getCrmId();
            crmOpportunityType = opportunity.getType();
            crmOpportunityName = opportunity.getName();
            crmOpportunityStage = opportunity.getStage();
        }
        return ImmutableCancelAndRestructureOrderDetail.builder()
            .from(cancelAndRestructureOrderDetail)
            .crmOpportunityId(crmOpportunityId)
            .crmOpportunityName(crmOpportunityName)
            .crmOpportunityStage(crmOpportunityStage)
            .crmOpportunityType(crmOpportunityType)
            .build();
    }

    @AllowAllRoles
    CancelAndRestructureOrderDetail getCancelAndRestructureOrderDetail(String compositeOrderId) {
        List<Order> ordersInCompositeOrder = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrderId);
        String message = String.format(
            "cancel and restructure composite order id: %s, orders included: %s ",
            compositeOrderId,
            ordersInCompositeOrder.stream().map(Order::getOrderId).toList()
        );
        LOGGER.info(message);
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        return getCancelAndRestructureOrderDetail(
            ordersInCompositeOrder,
            compositeOrder.getCrmOpportunityId(),
            compositeOrder.getCrmOpportunityType(),
            compositeOrder.getCrmOpportunityName(),
            compositeOrder.getCrmOpportunityStage()
        );
    }

    @AllowAllRoles
    void setShouldRegeneratePdfForOrder(String orderId) {
        orderPdfGenerationTrackerService.setShouldRegeneratePdf(orderId, true);
    }

    @AllowAllRoles
    CancelAndRestructureOrderDetail deleteCancelAndRestructure(String compositeOrderId) {
        CancelAndRestructureOrderDetail cancelAndRestructureOrderDetail = getCancelAndRestructureOrderDetail(compositeOrderId);
        compositeOrderService.deleteCompositeOrder(compositeOrderId);
        return cancelAndRestructureOrderDetail;
    }

    @AllowAllRoles
    String getWireInstruction() {
        String entityId = entityContextProvider.provideSelected();
        return entityGetService.getWireInstruction(entityId);
    }

    @AllowRoles({ Role.ADMIN })
    void updateWireInstruction(String wireInstruction) {
        String entityId = entityContextProvider.provideSelected();
        entityService.updateWireInstruction(entityId, wireInstruction);
    }

    @AllowAllRoles
    List<CompositeOrder> getCompositeOrdersByCrmOpportunityId(String crmOpportunityId) {
        return compositeOrderGetService.getCompositeOrdersByCrmOpportunityId(crmOpportunityId);
    }

    @AllowAllRoles
    List<EnabledPlatformFeature> getEnabledFeatures() {
        return new ArrayList<>(platformFeatureService.getEnabledFeatures());
    }

    @AllowRoles({ Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    List<EnabledPlatformFeature> getEnabledFeaturesInTenant(String tenantId) {
        tenantService.throwIfTenantIdIsNotValid(tenantId);
        return new ArrayList<>(platformFeatureService.getEnabledFeatures(tenantId));
    }

    @AllowAllRoles
    void updatePrimaryCompositeOrderIdForCrmOpportunity(String compositeOrderId) {
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(compositeOrderId);
        List<Order> orders = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrder.getCompositeOrderId());
        Optional<Order> orderOptional = orders
            .stream()
            .filter(o -> o.getOrderType() == OrderType.RESTRUCTURE || o.getOrderType() == OrderType.RENEWAL)
            .findFirst();

        if (orderOptional.isEmpty()) {
            return;
        }
        Order order = orderOptional.get();
        Optional<CrmType> optionalCrmType = crmService.getCrmType();
        if (optionalCrmType.isEmpty()) {
            opportunityService.updatePrimaryCompositeOrderIdForCrmOpportunity(compositeOrder, order);
            return;
        }
        CrmType crmType = optionalCrmType.get();

        switch (crmType) {
            case SALESFORCE -> salesforceJobQueueService.updatePrimaryCompositeOrderIdForCrmOpportunity(compositeOrder, order);
            case HUBSPOT -> hubSpotJobQueueService.updatePrimaryCompositeOrderIdForCrmOpportunity(compositeOrder, order);
        }
    }

    @AllowAllRoles
    void syncCompositeOrderToCrm(String compositeOrderId) {
        Optional<CrmType> optionalCrmType = crmService.getCrmType();
        if (optionalCrmType.isEmpty()) {
            return;
        }
        CrmType crmType = optionalCrmType.get();
        List<Order> orders = compositeOrderGetService.getOrdersInCompositeOrder(compositeOrderId);
        Optional<Order> orderOptional = orders
            .stream()
            .filter(o -> o.getOrderType() == OrderType.RESTRUCTURE || o.getOrderType() == OrderType.RENEWAL)
            .findFirst();

        if (orderOptional.isEmpty()) {
            return;
        }
        Order order = orderOptional.get();

        switch (crmType) {
            case SALESFORCE -> salesforceJobQueueService.dispatchCompositeOrderSync(compositeOrderId, order.getAccountId(), order.getTenantId());
            case HUBSPOT -> hubSpotJobQueueService.dispatchHubSpotCompositeOrderSyncTenantJob(
                compositeOrderId,
                order.getAccountId(),
                order.getTenantId()
            );
        }
    }

    @AllowAllRoles
    Set<String> getUpdatableSubscriptionsByAccountCrmId(String accountCrmId) {
        return crmService.getUpdatableSubscriptionsByAccountCrmId(accountCrmId);
    }

    @AllowRoles({ Role.ADMIN })
    Opportunity resetOpportunityClosedState(String opportunityId) {
        return opportunityService.resetOpportunityClosedState(opportunityId);
    }

    @AllowRoles({ Role.ADMIN })
    String integrateAnrok(AnrokIntegrationInput input) {
        return anrokIntegrationService.addIntegration(input);
    }

    @AllowRoles({ Role.ADMIN })
    void testAnrokIntegration(AnrokIntegrationInput input) {
        anrokIntegrationService.testIntegration(input);
    }

    @AllowRoles({ Role.ADMIN })
    Integration getAnrokIntegrationById(String integrationId) {
        return anrokIntegrationGetService.getIntegrationById(integrationId);
    }

    @AllowAllRoles
    Optional<Integration> getAnrokIntegration() {
        return anrokIntegrationGetService.getAnrokIntegration();
    }

    @AllowAllRoles
    ErpInvoiceJson getErpInvoice(String invoiceErpId) {
        return erpSyncService.getErpInvoice(invoiceErpId);
    }

    @AllowAllRoles
    List<ErpInvoiceJson> getErpInvoicesBySubscriptionId(String subscriptionId) {
        return erpSyncService.getErpInvoiceBySubscriptionId(subscriptionId);
    }

    @AllowAllRoles
    CurrencyTypeSetting upsertCurrencyTypeSetting(CurrencyTypeSetting currencyTypeSetting) {
        return currencyTypeSettingService
            .upsertCurrencyTypeSetting(currencyTypeSetting)
            .orElseThrow(() ->
                new ServiceFailureException(
                    String.format("The currency type setting for tenant: %s could not be created/updated", tenantIdProvider.provideTenantIdString())
                )
            );
    }

    @AllowAllRoles
    CurrencyTypeSetting getCurrencyTypeSetting() {
        return currencyTypeSettingGetService
            .getCurrencyTypeSetting()
            .orElseThrow(() ->
                new ObjectNotFoundException(
                    BillyObjectType.CURRENCY_TYPE_SETTING,
                    String.format("for tenant: %s", tenantIdProvider.provideTenantIdString())
                )
            );
    }

    @AllowAllRoles
    CurrencyConversionRate upsertCurrencyConversionRate(CurrencyConversionRate currencyConversionRate) {
        return currencyConversionRateService
            .upsertCurrencyConversionRate(currencyConversionRate)
            .orElseThrow(() -> new ServiceFailureException("The currency conversion rate could not be created/updated"));
    }

    @AllowAllRoles
    CurrencyConversionRate currencyConversionRateById(String id) {
        return currencyConversionRateGetService
            .getCurrencyConversionRateById(id)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.CURRENCY_CONVERSION_RATE, id));
    }

    @AllowAllRoles
    List<CurrencyConversionRate> currencyConversionRates() {
        return currencyConversionRateGetService.getCurrencyConversionRates();
    }

    @AllowAllRoles
    List<CurrencyConversionRate> getCurrencyConversionRatesForGivenCurrencyPair(String fromCurrency, String toCurrency) {
        List<String> supportedCurrencyCodesForTenant = tenantSettingService.getTenantSetting().getSupportedCurrencies();
        if (!supportedCurrencyCodesForTenant.contains(fromCurrency)) {
            throw new InvalidInputException("Currency is not supported: " + fromCurrency);
        }
        if (!supportedCurrencyCodesForTenant.contains(toCurrency)) {
            throw new InvalidInputException("Currency is not supported: " + toCurrency);
        }
        return currencyConversionRateGetService.getCurrencyConversionRatesForGivenCurrencyPair(fromCurrency, toCurrency);
    }

    @AllowAllRoles
    ApprovalFlowSubmitterNoteJson upsertApprovalFlowSubmitterNote(ApprovalFlowSubmitterNote note) {
        ApprovalFlowSubmitterNote approvalFlowSubmitterNote = approvalFlowInstanceService.upsertApprovalFlowSubmitterNote(note);
        return approvalFlowMapper.toSubmitterNoteJson(approvalFlowSubmitterNote);
    }

    @AllowRoles({ Role.ADMIN, Role.BILLY_ADMIN, Role.BILLY_ENGINEER })
    String previewDocumentTemplate(String orderId, String templateId) {
        return orderDocumentService.previewDocumentTemplate(orderId, templateId);
    }

    @AllowAllRoles
    OrderInvoicePreviewResolver getOrderInvoicePreview(String orderId) {
        if (platformFeatureService.getFeatureEnablement(PlatformFeature.BILLING).isEmpty()) {
            throw new ConflictingStateException("Billing feature is not enabled");
        }
        List<InvoicePreview> invoicePreviews = invoiceService.previewAllInvoicesForOrder(orderId);
        List<InvoicePreviewJson> invoicePreviewJsons = invoicePreviews
            .stream()
            .map(invoicePreviewJsonMapper::invoicePreviewToJson)
            .collect(Collectors.toList());
        ChargeResolverEntityCache chargeResolverEntityCache = new ChargeResolverEntityCache(
            productCatalogGetService,
            taxRateGetService,
            unitOfMeasureService,
            revenueRecognitionGetService,
            accountingGetService,
            catalogRelationshipGetService
        );
        return new OrderInvoicePreviewResolver(invoicePreviewJsons, chargeResolverEntityCache);
    }

    @AllowAllRoles
    List<PlanMinimal> getPlanMinimals(Optional<PlanStatus> status, Optional<String> accountId, Optional<String> orderCurrencyCode) {
        // todo: get all plans, even beyond pagination limit
        // todo: cache tenant plans or plan details
        List<Plan> plans = getPlans(null, status, accountId, orderCurrencyCode, DEFAULT_PAGINATION_QUERY_PARAMS, false);

        List<Product> products = getProducts(DEFAULT_PAGINATION_QUERY_PARAMS);
        Map<String, Product> productMap = products.stream().collect(Collectors.toMap(Product::getProductId, Function.identity()));

        return plans.stream().map(plan -> getPlanMinimalFromPlan(plan, productMap)).collect(Collectors.toList());
    }

    @AllowAllRoles
    boolean wasEmailSentForInvoice(String invoiceNumber) {
        return emailService.wasEmailSentForInvoice(new Invoice.Number(invoiceNumber));
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.BILLING_CLERK })
    void sendBulkInvoiceEmail(List<String> invoiceNumbers) {
        bulkInvoiceService.sendBulkInvoiceEmail(invoiceNumbers);
    }

    @AllowAllRoles
    List<PlanJson> getReplacedPlans(String planId) {
        List<Plan> plans = productCatalogGetService.getReplacedPlans(planId);
        return planMapper.plansToJson(plans);
    }

    @AllowRoles({ Role.ADMIN })
    List<EmailSetting> getEmailSettings() {
        return emailSettingService.getEmailSettings();
    }

    @AllowRoles({ Role.ADMIN })
    EmailSetting addEmailSetting(EmailSetting emailSetting) {
        return emailSettingService.addEmailSetting(emailSetting);
    }

    @AllowRoles({ Role.ADMIN })
    EmailSetting updateEmailSetting(EmailSetting emailSetting) {
        return emailSettingService.updateEmailSetting(emailSetting);
    }

    @AllowRoles({ Role.ADMIN })
    void deleteEmailSetting(EmailSetting emailSetting) {
        emailSettingService.deleteEmailSetting(emailSetting);
    }

    @AllowRoles({ Role.ADMIN, Role.FINANCE, Role.SALES, Role.SALES_MANAGER })
    void updateOrderAttributes(String orderId, OrderAttributesUpdateRequest updateRequest) {
        orderService.updateOrderAttributes(orderId, updateRequest);
    }

    @AllowAllRoles
    void syncAccountToCrm(String accountId) {
        crmService.dispatchAccountSync(accountId);
    }

    @AllowAllRoles
    void syncSubscriptionToCrm(String subscriptionId) {
        crmService.pushSubscriptionSync(subscriptionId);
    }

    @AllowAllRoles
    void updatePredefinedTermsToLatestForOrder(String templateId, String orderId) {
        orderDocumentTemplateService.updatePredefinedTermsToLatestForOrder(orderId, templateId);
    }

    @AllowAllRoles
    List<BillingCycleDefinitionJson> getBillingCycleDefinitions() {
        List<BillingCycleDefinition> billingCycleDefinitions = billingCycleDefinitionService.getBillingCycleDefinitions();
        return billingCycleDefinitionJsonMapper.modelsToJson(billingCycleDefinitions);
    }
}
