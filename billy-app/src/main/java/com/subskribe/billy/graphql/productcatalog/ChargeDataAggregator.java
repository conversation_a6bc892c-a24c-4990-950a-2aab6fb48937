package com.subskribe.billy.graphql.productcatalog;

import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.LedgerAccountMapping;
import com.subskribe.billy.productcatalog.model.TaxRate;
import com.subskribe.billy.productcatalog.model.UnitOfMeasure;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.productcatalog.services.UnitOfMeasureService;
import com.subskribe.billy.resources.json.taxrate.TaxRateJsonMapper;
import com.subskribe.billy.resources.json.uom.UnitOfMeasureJsonMapper;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.shared.entitycache.EntityCache;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class ChargeDataAggregator {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChargeDataAggregator.class);

    private final TaxRateGetService taxRateGetService;
    private final UnitOfMeasureService unitOfMeasureService;
    private final RevenueRecognitionGetService revenueRecognitionGetService;
    private final PlanDetailMapper planDetailMapper;
    private final TaxRateJsonMapper taxRateJsonMapper;
    private final UnitOfMeasureJsonMapper unitOfMeasureJsonMapper;
    private final AccountingGetService accountingGetService;

    @Inject
    public ChargeDataAggregator(
        TaxRateGetService taxRateGetService,
        UnitOfMeasureService unitOfMeasureService,
        RevenueRecognitionGetService revenueRecognitionGetService,
        AccountingGetService accountingGetService
    ) {
        this.taxRateGetService = taxRateGetService;
        this.unitOfMeasureService = unitOfMeasureService;
        this.revenueRecognitionGetService = revenueRecognitionGetService;
        this.accountingGetService = accountingGetService;
        planDetailMapper = Mappers.getMapper(PlanDetailMapper.class);
        taxRateJsonMapper = Mappers.getMapper(TaxRateJsonMapper.class);
        unitOfMeasureJsonMapper = Mappers.getMapper(UnitOfMeasureJsonMapper.class);
    }

    public List<ChargeDetail> getChargeDetails(List<Charge> charges) {
        List<ChargeDetail> chargeDetails = new ArrayList<>();

        if (CollectionUtils.isEmpty(charges)) {
            return chargeDetails;
        }
        ChargeDetailEntityCache entityCache = makeChargeDetailEntityCache(charges);

        charges.forEach(charge -> chargeDetails.add(getChargeDetail(charge, entityCache)));

        return chargeDetails;
    }

    public ChargeDetail getChargeDetail(Charge charge) {
        return getChargeDetail(charge, makeChargeDetailEntityCache());
    }

    private ChargeDetail getChargeDetail(Charge charge, ChargeDetailEntityCache chargeDataEntityCache) {
        ChargeDetail chargeDetail = planDetailMapper.chargeToChargeDetail(charge);

        populateTaxRate(chargeDetail, charge.getTaxRateId(), chargeDataEntityCache);
        populateUnitOfMeasure(chargeDetail, charge.getUnitOfMeasureId(), chargeDataEntityCache);
        populateRecognitionRule(chargeDetail, charge.getRecognitionRuleId(), chargeDataEntityCache);
        populateLedgerAccountMapping(chargeDetail, chargeDataEntityCache);

        return chargeDetail;
    }

    private ChargeDetailEntityCache makeChargeDetailEntityCache() {
        return new ChargeDetailEntityCache(taxRateGetService, unitOfMeasureService, revenueRecognitionGetService, accountingGetService);
    }

    private ChargeDetailEntityCache makeChargeDetailEntityCache(List<Charge> charges) {
        return new ChargeDetailEntityCache(taxRateGetService, unitOfMeasureService, revenueRecognitionGetService, accountingGetService, charges);
    }

    private void populateLedgerAccountMapping(ChargeDetail chargeDetail, ChargeDetailEntityCache chargeDataEntityCache) {
        List<LedgerAccount> ledgerAccounts = chargeDataEntityCache.ledgerAccountEntityCache.get(chargeDetail.getId());
        LedgerAccountMapping ledgerAccountMapping = new LedgerAccountMapping(ledgerAccounts);
        chargeDetail.setLedgerAccountMapping(ledgerAccountMapping);
    }

    private void populateTaxRate(ChargeDetail chargeDetail, UUID taxRateId, ChargeDetailEntityCache chargeDataEntityCache) {
        if (taxRateId == null) {
            return;
        }

        var taxRate = chargeDataEntityCache.taxRateEntityCache.get(taxRateId);
        var taxRateJson = taxRateJsonMapper.toTaxRateJson(taxRate);
        chargeDetail.setTaxRate(taxRateJson);
    }

    private void populateUnitOfMeasure(ChargeDetail chargeDetail, UUID unitOfMeasureId, ChargeDetailEntityCache chargeDataEntityCache) {
        if (unitOfMeasureId != null) {
            try {
                var unitOfMeasure = chargeDataEntityCache.unitOfMeasureEntityCache.get(unitOfMeasureId);
                var unitOfMeasureJson = unitOfMeasureJsonMapper.toJson(unitOfMeasure);
                chargeDetail.setUnitOfMeasure(unitOfMeasureJson);
            } catch (ObjectNotFoundException ex) {
                LOGGER.warn("Unit of measure {} should be available but isn't found", unitOfMeasureId);
            }
        }
    }

    private void populateRecognitionRule(ChargeDetail chargeDetail, String recognitionRuleId, ChargeDetailEntityCache chargeDataEntityCache) {
        if (StringUtils.isBlank(recognitionRuleId)) {
            return;
        }

        Optional<RecognitionRule> recognitionRuleOptional = chargeDataEntityCache.recognitionRuleEntityCache.get(recognitionRuleId);
        if (recognitionRuleOptional.isEmpty()) {
            return;
        }
        chargeDetail.setRecognitionRule(recognitionRuleOptional.get());
    }

    public static final class ChargeDetailEntityCache {

        private final EntityCache<UUID, TaxRate> taxRateEntityCache;
        private final EntityCache<UUID, UnitOfMeasure> unitOfMeasureEntityCache;
        private final EntityCache<String, Optional<RecognitionRule>> recognitionRuleEntityCache;
        private final EntityCache<String, List<LedgerAccount>> ledgerAccountEntityCache;

        private ChargeDetailEntityCache(
            TaxRateGetService taxRateGetService,
            UnitOfMeasureService unitOfMeasureService,
            RevenueRecognitionGetService revenueRecognitionGetService,
            AccountingGetService accountingGetService
        ) {
            taxRateEntityCache = EntityCache.of(taxRateGetService::getTaxRate);
            unitOfMeasureEntityCache = EntityCache.of(unitOfMeasureService::getUnitOfMeasure);
            recognitionRuleEntityCache = EntityCache.of(revenueRecognitionGetService::getRecognitionRuleByRuleId);
            ledgerAccountEntityCache = EntityCache.of(accountingGetService::getLedgerAccountsForCharge);
        }

        private ChargeDetailEntityCache(
            TaxRateGetService taxRateGetService,
            UnitOfMeasureService unitOfMeasureService,
            RevenueRecognitionGetService revenueRecognitionGetService,
            AccountingGetService accountingGetService,
            List<Charge> charges
        ) {
            Set<UUID> taxRateIds = charges.stream().map(Charge::getTaxRateId).collect(Collectors.toSet());
            Map<UUID, TaxRate> taxRatesMap = taxRateGetService.getTaxRatesMap(taxRateIds);

            List<UUID> unitOfMeasureIds = charges.stream().map(Charge::getUnitOfMeasureId).distinct().toList();
            Map<UUID, UnitOfMeasure> unitsOfMeasureMap = unitOfMeasureService.getUnitsOfMeasureByIds(unitOfMeasureIds);

            Set<String> recognitionRuleIds = charges.stream().map(Charge::getRecognitionRuleId).collect(Collectors.toSet());
            List<RecognitionRule> recognitionRules = revenueRecognitionGetService.getRecognitionRules();
            Map<String, Optional<RecognitionRule>> recognitionRulesMap = recognitionRules
                .stream()
                .filter(recognitionRule -> recognitionRuleIds.contains(recognitionRule.getRuleId()))
                .collect(Collectors.toMap(RecognitionRule::getRuleId, Optional::of));

            taxRateEntityCache = EntityCache.of(taxRateGetService::getTaxRate, taxRatesMap);
            unitOfMeasureEntityCache = EntityCache.of(unitOfMeasureService::getUnitOfMeasure, unitsOfMeasureMap);
            recognitionRuleEntityCache = EntityCache.of(revenueRecognitionGetService::getRecognitionRuleByRuleId, recognitionRulesMap);
            ledgerAccountEntityCache = EntityCache.of(accountingGetService::getLedgerAccountsForCharge);
        }
    }
}
