package com.subskribe.billy.graphql.taxrate;

import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;

@GraphQLName("UpsertTaxRateRequest")
public class UpsertTaxRateRequest {

    @GraphQLField
    @GraphQLName("id")
    private String id;

    @GraphQLField
    @GraphQLName("name")
    @GraphQLNonNull
    private String name;

    @GraphQLField
    @GraphQLName("description")
    private String description;

    @GraphQLField
    @GraphQLName("taxCode")
    private String taxCode;

    @GraphQLField
    @GraphQLName("taxInclusive")
    private boolean taxInclusive;

    @GraphQLField
    @GraphQLName("taxPercentage")
    private BigDecimal taxPercentage;

    public UpsertTaxRateRequest() {}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public boolean isTaxInclusive() {
        return taxInclusive;
    }

    public void setTaxInclusive(boolean taxInclusive) {
        this.taxInclusive = taxInclusive;
    }

    public BigDecimal getTaxPercentage() {
        return taxPercentage;
    }

    public void setTaxPercentage(BigDecimal taxPercentage) {
        this.taxPercentage = taxPercentage;
    }
}
