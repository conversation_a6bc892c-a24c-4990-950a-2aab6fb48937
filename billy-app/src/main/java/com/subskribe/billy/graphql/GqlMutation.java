package com.subskribe.billy.graphql;

import com.google.gson.Gson;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.AccountingPeriodStatus;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.alias.model.SubscriptionChargeAlias;
import com.subskribe.billy.anrok.model.AnrokIntegrationInput;
import com.subskribe.billy.anrok.model.ImmutableAnrokIntegrationInput;
import com.subskribe.billy.approvalflow.model.ApprovalWithCodeResponse;
import com.subskribe.billy.approvalflowhierarchy.model.ApprovalRole;
import com.subskribe.billy.approvalflowhierarchy.model.ApprovalSegment;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceGroup;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceStateUpdates;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowSubmitterNote;
import com.subskribe.billy.auth.model.ApiKeyGenerationRequest;
import com.subskribe.billy.auth.model.ApiKeyTokenAndSecret;
import com.subskribe.billy.avalara.model.AvalaraIntegration;
import com.subskribe.billy.avalara.model.AvalaraIntegrationInput;
import com.subskribe.billy.compositeorder.model.CancelAndRestructureOrderDetail;
import com.subskribe.billy.compositeorder.model.CompositeOrderDetail;
import com.subskribe.billy.currency.model.CurrencyJson;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldDefinitionUpdate;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.dataimport.model.DataImport;
import com.subskribe.billy.dataimport.model.ImportDomain;
import com.subskribe.billy.dataimport.model.flatfile.FlatfileSpaceResponse;
import com.subskribe.billy.discount.model.Discount;
import com.subskribe.billy.discount.model.DiscountStatus;
import com.subskribe.billy.docusign.model.DocuSignIntegration;
import com.subskribe.billy.docusign.model.DocuSignIntegrationRequestJson;
import com.subskribe.billy.docusign.model.DocuSignIntegrationResponseJson;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.email.model.EmailSetting;
import com.subskribe.billy.email.model.ImmutableEmailSetting;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.model.EntityJson;
import com.subskribe.billy.entity.model.EntityPatchRequest;
import com.subskribe.billy.entity.model.ImmutableEntityJson;
import com.subskribe.billy.escalationpolicy.model.EscalationPolicy;
import com.subskribe.billy.esign.model.EsignRequest;
import com.subskribe.billy.esign.model.EsignTenantSignatory;
import com.subskribe.billy.exception.AddressValidationException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.model.CurrencyTypeSetting;
import com.subskribe.billy.foreignexchange.model.ImmutableCurrencyConversionRate;
import com.subskribe.billy.foreignexchange.model.ImmutableCurrencyTypeSetting;
import com.subskribe.billy.graphql.account.AccountDetail;
import com.subskribe.billy.graphql.account.CreateAccountInput;
import com.subskribe.billy.graphql.account.UpdateAccountInput;
import com.subskribe.billy.graphql.approvalflow.ApprovalFlowDetail;
import com.subskribe.billy.graphql.clm.CLMChatMessageDetail;
import com.subskribe.billy.graphql.clm.CLMCreateThreadRequest;
import com.subskribe.billy.graphql.clm.CLMThreadCreationDetail;
import com.subskribe.billy.graphql.compositeorder.AmendAndRenewCreateRequest;
import com.subskribe.billy.graphql.compositeorder.ImmutableCompositeOrderFieldsUpdateRequest;
import com.subskribe.billy.graphql.creditmemo.ApplyCreditMemoRequest;
import com.subskribe.billy.graphql.creditmemo.CreditMemoDetail;
import com.subskribe.billy.graphql.creditmemo.CreditMemoEmailResponse;
import com.subskribe.billy.graphql.creditmemo.UnapplyCreditMemoRequest;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.customfield.CustomFieldInput;
import com.subskribe.billy.graphql.invoice.BillingEventInput;
import com.subskribe.billy.graphql.invoice.BillingEvents;
import com.subskribe.billy.graphql.invoice.InvoiceDetail;
import com.subskribe.billy.graphql.invoice.InvoiceEmailResponse;
import com.subskribe.billy.graphql.invoice.UpdateInvoiceMutation;
import com.subskribe.billy.graphql.invoice.VoidInvoiceMutation;
import com.subskribe.billy.graphql.order.AmendmentOrderRequest;
import com.subskribe.billy.graphql.order.CancelAndRestructureRequest;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.order.OrderStatusUpdateResponse;
import com.subskribe.billy.graphql.payment.ApplyPaymentRequest;
import com.subskribe.billy.graphql.payment.SettlementApplicationDetail;
import com.subskribe.billy.graphql.payment.VoidPaymentRequest;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.graphql.productcatalog.PlanDetail;
import com.subskribe.billy.graphql.productcatalog.ProductDetail;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.graphql.taxrate.UpsertTaxRateRequest;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrder;
import com.subskribe.billy.graphql.template.DocumentTemplateResponse;
import com.subskribe.billy.graphql.template.UpsertDocumentTemplateRequest;
import com.subskribe.billy.graphql.tenant.TenantDetails;
import com.subskribe.billy.graphql.tenant.UpdateTenantRequest;
import com.subskribe.billy.graphql.tenant.settings.dunningsetting.DunningSettingDetails;
import com.subskribe.billy.graphql.tenant.settings.dunningsetting.DunningSettingDetailsMapper;
import com.subskribe.billy.graphql.uom.ImmutableUnitOfMeasureRequest;
import com.subskribe.billy.graphql.uom.UnitOfMeasureRequest;
import com.subskribe.billy.hubspot.model.HubSpotIntegration;
import com.subskribe.billy.hubspot.model.HubSpotSetupMessage;
import com.subskribe.billy.integration.IntegrationTargetService;
import com.subskribe.billy.integration.model.Integration;
import com.subskribe.billy.integration.model.IntegrationDetail;
import com.subskribe.billy.integration.model.IntegrationRequest;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRun;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunInput;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceChargeInclusionOption;
import com.subskribe.billy.invoice.model.UpdateInvoiceRequest;
import com.subskribe.billy.invoicesettlement.document.CreditMemoDocumentJson;
import com.subskribe.billy.invoicesettlement.document.render.CreditMemoTemplateData;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.notification.gql.NotificationInstanceWithAttempts;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationTargetAndSubscriptions;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.payment.model.AccountPaymentManagementLink;
import com.subskribe.billy.payment.model.AccountPaymentManagementLinkGqlResponse;
import com.subskribe.billy.payment.model.AddPaymentMethodActionType;
import com.subskribe.billy.payment.model.InvoicePaymentManagementLink;
import com.subskribe.billy.payment.model.InvoicePaymentManagementLinkGqlResponse;
import com.subskribe.billy.payment.model.PaymentConfiguration;
import com.subskribe.billy.payment.model.PaymentLinkSourceType;
import com.subskribe.billy.payment.model.PaymentManagementLinkType;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegrationSetupResponse;
import com.subskribe.billy.payment.stripe.model.StripeIntentResponse;
import com.subskribe.billy.payment.stripe.model.StripeSetupIntentResponse;
import com.subskribe.billy.platformfeature.model.EnabledPlatformFeature;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.productcatalog.model.AddPlanRequest;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.LedgerAccountMapping;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.productcatalog.model.TaxRate;
import com.subskribe.billy.productcatalog.model.UnitOfMeasure;
import com.subskribe.billy.productcatalog.model.UnitOfMeasureStatus;
import com.subskribe.billy.productcatalog.model.UpdatePlanRequest;
import com.subskribe.billy.productcatalog.plangroup.model.gql.PlanGroupInterface;
import com.subskribe.billy.productcatalog.priceattribute.model.ImmutablePriceAttribute;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.ChargeDefaultAttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import com.subskribe.billy.proposal.model.CreateProposalRequest;
import com.subskribe.billy.proposal.model.Proposal;
import com.subskribe.billy.resources.ReportResource;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.accountreceivablecontact.AccountReceivableContactJson;
import com.subskribe.billy.resources.json.approvalflow.ApprovalFlowJson;
import com.subskribe.billy.resources.json.approvalflowinstance.ApprovalFlowSubmitterNoteJson;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalRoleJson;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalSegmentJson;
import com.subskribe.billy.resources.json.auth.AuthSamlIntegrationMapper;
import com.subskribe.billy.resources.json.auth.AuthSamlIntegrationRequestJson;
import com.subskribe.billy.resources.json.creditmemo.StandaloneCreditMemoRequest;
import com.subskribe.billy.resources.json.customfield.CustomFieldDefinitionCreateInput;
import com.subskribe.billy.resources.json.customfield.CustomFieldDefinitionJson;
import com.subskribe.billy.resources.json.customfield.CustomFieldDefinitionUpdateInput;
import com.subskribe.billy.resources.json.discount.DiscountJson;
import com.subskribe.billy.resources.json.entity.EntityJsonMapper;
import com.subskribe.billy.resources.json.escalationpolicy.ImmutableAddEscalationPolicyRequest;
import com.subskribe.billy.resources.json.escalationpolicy.ImmutableUpdateEscalationPolicyRequest;
import com.subskribe.billy.resources.json.invoicesettlement.RefundDetail;
import com.subskribe.billy.resources.json.invoicesettlement.RefundRequestJson;
import com.subskribe.billy.resources.json.opportunity.OpportunityInput;
import com.subskribe.billy.resources.json.opportunity.OpportunityJson;
import com.subskribe.billy.resources.json.order.OrderAttributesUpdateRequest;
import com.subskribe.billy.resources.json.order.OrderRequestJson;
import com.subskribe.billy.resources.json.payment.PaymentBankAccountJson;
import com.subskribe.billy.resources.json.paymenttermsettings.PaymentTermSettingsJson;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.ChargePartialJson;
import com.subskribe.billy.resources.json.report.PredefinedReportJson;
import com.subskribe.billy.resources.json.report.ReportJobResponse;
import com.subskribe.billy.resources.json.salesforce.SalesforceClientIntegrationRequestJson;
import com.subskribe.billy.resources.json.subscription.SubscriptionUpdateJson;
import com.subskribe.billy.resources.json.taxrate.TaxRateJson;
import com.subskribe.billy.resources.json.tenant.TenantCreationResponseJson;
import com.subskribe.billy.resources.json.tenant.TenantJson;
import com.subskribe.billy.resources.json.tenant.UserInput;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.resources.json.tenantsetting.TenantSettingJson;
import com.subskribe.billy.resources.json.uom.UnitOfMeasureJson;
import com.subskribe.billy.resources.json.usage.RawUsagesData;
import com.subskribe.billy.resources.json.usergroup.UserGroupDetail;
import com.subskribe.billy.resources.json.usergroup.UserGroupRequestJson;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.salesforce.model.contact.UpsertCRMContactResponse;
import com.subskribe.billy.salesroom.model.SalesRoomLink;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.enums.SigningOrder;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.taxjar.model.ImmutableTaxJarIntegrationInput;
import com.subskribe.billy.taxjar.model.TaxJarIntegration;
import com.subskribe.billy.taxjar.model.TaxJarIntegrationInput;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.model.DocumentMasterTemplateStatus;
import com.subskribe.billy.template.model.DocumentSection;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.model.DocumentTemplateStatus;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.usage.model.UsageBatchInsertResult;
import com.subskribe.billy.user.model.User;
import graphql.annotations.annotationTypes.GraphQLDeprecate;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLMutation;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import graphql.schema.DataFetchingEnvironment;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import javax.ws.rs.core.UriBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

@SuppressWarnings({ "unused", "deprecation" })
@GraphQLMutation
public class GqlMutation {

    private static final Logger LOGGER = LoggerFactory.getLogger(GqlMutation.class);

    private static final String ACCOUNT_KEY = "account";

    private static final String ACCOUNT_CONTACT_KEY = "accountContact";

    private static final String ORDER_KEY = "order";

    private static final String DISCOUNT_KEY = "discount";

    private static final String PLAN_KEY = "plan";

    private static final String RATE_CARD = "rateCard";

    private static final String CHARGE_KEY = "charge";

    private static final String PRICE_ATTRIBUTE_KEY = "priceAttribute";

    private static final String CHARGE_DEFAULT_PRICE_ATTRIBUTE_KEY = "chargeDefaultAttributeReferences";

    private static final String CHARGE_PARTIAL_KEY = "chargePartial";

    private static final String SETTING_KEY = "setting";

    private static final String DUNNING_SETTING_KEY = "dunningSetting";

    private static final String USER_KEY = "user";

    private static final String USER_GROUP_KEY = "userGroup";

    private static final String PAYMENT_KEY = "addPaymentRequest";

    private static final String TENANT_KEY = "tenant";

    private static final String NOTIFICATION_KEY = "notification";

    private static final String TAX_RATE_KEY = "taxRate";

    private static final String UNIT_OF_MEASURE_KEY = "unitOfMeasure";

    private static final String PONG = "PONG";

    private static final String SALESFORCE_INTEGRATION_REQUEST_KEY = "salesforceIntegration";

    private static final String IS_DRY_RUN = "isDryRun";

    private static final String POPULATE_MISSING_LINE_ITEMS = "populateMissingLineItems";

    private static final String SANITIZE_LINE_ITEM_DATES = "sanitizeLineItemDates";

    private static final String DOCUSIGN_INTEGRATION_REQUEST_KEY = "docuSignIntegrationRequest";

    private static final String DOCUSIGN_EMAIL_SIGNATURE_REQUEST_KEY = "docuSignEmailSignatureRequest";

    private static final String ACCOUNT_RECEIVABLE_CONTACT_KEY = "accountReceivableContact";

    private static final String API_KEY_REQUEST = "apiKey";

    private static final String APPROVAL_FLOW_KEY = "approvalFlow";

    private static final String ORDER_APPROVAL_FLOW_STATE_UPDATES = "orderApprovalStateUpdates";

    private static final String APPROVAL_ROLE_KEY = "approvalRole";

    private static final String APPROVAL_SEGMENT_KEY = "approvalSegment";

    private static final String CURRENCY_TYPE_SETTING = "currencyTypeSetting";

    private static final String CURRENCY_CONVERSION_RATE = "currencyConversionRate";

    @GraphQLField
    @GraphQLNonNull
    public static ProductDetail addProduct(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("name") String name,
        @GraphQLName("displayName") String displayName,
        @GraphQLName("description") String description,
        @GraphQLName("sku") String sku,
        @GraphQLName("productCategoryId") String productCategoryId,
        @GraphQLName("externalId") String externalId,
        @GraphQLName("entityIds") List<@GraphQLNonNull String> entityIds
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();

        ProductCategory productCategory = null;
        if (productCategoryId != null) {
            productCategory = new ProductCategory();
            productCategory.setProductCategoryId(productCategoryId);
        }
        Set<String> resolvedEntityIds = CollectionUtils.isEmpty(entityIds) ? null : new HashSet<>(entityIds);
        Product product = new Product(null, resolvedEntityIds, null, false, name, displayName, description, sku, productCategory, null, externalId);
        Product result = gqlAuthContext.addProduct(product);

        return gqlAuthContext.getProductDetailMapper().toProductDetail(result);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ProductDetail updateProduct(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String productId,
        @GraphQLNonNull @GraphQLName("name") String name,
        @GraphQLName("displayName") String displayName,
        @GraphQLName("description") String description,
        @GraphQLName("sku") String sku,
        @GraphQLName("productCategoryId") String productCategoryId,
        @GraphQLName("externalId") String externalId,
        @GraphQLName("entityIds") List<@GraphQLNonNull String> entityIds
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();

        ProductCategory productCategory = null;
        if (productCategoryId != null) {
            productCategory = new ProductCategory();
            productCategory.setProductCategoryId(productCategoryId);
        }
        Set<String> resolvedEntityIds = CollectionUtils.isEmpty(entityIds) ? null : new HashSet<>(entityIds);
        Product product = new Product(
            null,
            resolvedEntityIds,
            productId,
            false,
            name,
            displayName,
            description,
            sku,
            productCategory,
            null,
            externalId
        );
        gqlAuthContext.updateProduct(product);

        return gqlAuthContext.getProductDetailMapper().toProductDetail(product);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ProductDetail deleteProduct(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String productId) {
        GqlAuthContext context = env.getContext();
        var product = context.deleteProduct(productId);
        return context.getProductDetailMapper().toProductDetail(product);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ProductCategory addProductCategory(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("name") String name,
        @GraphQLNonNull @GraphQLName("description") String description,
        @GraphQLName("entityIds") List<String> entityIds
    ) {
        ProductCategory category = new ProductCategory();
        category.setName(name);
        category.setInUse(false);
        category.setDescription(description);
        if (CollectionUtils.isNotEmpty(entityIds)) {
            category.setEntityIds(new HashSet<>(entityIds));
        }
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.addProductCategory(category);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ProductCategory updateProductCategory(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String productCategoryId,
        @GraphQLNonNull @GraphQLName("name") String name,
        @GraphQLNonNull @GraphQLName("description") String description,
        @GraphQLName("entityIds") List<String> entityIds
    ) {
        ProductCategory category = new ProductCategory();
        category.setProductCategoryId(productCategoryId);
        category.setInUse(false);
        category.setName(name);
        category.setDescription(description);
        if (CollectionUtils.isNotEmpty(entityIds)) {
            category.setEntityIds(new HashSet<>(entityIds));
        }
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.updateProductCategory(category);
        return category;
    }

    @GraphQLField
    @GraphQLNonNull
    public static ProductCategory upsertProductCategory(
        DataFetchingEnvironment env,
        @GraphQLName("id") String productCategoryId,
        @GraphQLNonNull @GraphQLName("name") String name,
        @GraphQLNonNull @GraphQLName("description") String description,
        @GraphQLName("entityIds") List<String> entityIds
    ) {
        if (StringUtils.isBlank(productCategoryId)) {
            return addProductCategory(env, name, description, entityIds);
        }

        return updateProductCategory(env, productCategoryId, name, description, entityIds);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ProductCategory deleteProductCategory(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String productCategoryId) {
        GqlAuthContext context = env.getContext();
        return context.deleteProductCategory(productCategoryId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ChargeDetail addCharge(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("planId") String planId,
        @GraphQLName("charge") @GraphQLNonNull ChargeJson ignored
    ) {
        // todo split out GQL object from REST JSON and use @GraphQlConstructor
        var chargeJson = getObjectFromMap(env.getArguments().get(CHARGE_KEY), ChargeJson.class);
        GqlAuthContext gqlAuthContext = env.getContext();
        Charge charge = gqlAuthContext.getPlanMapper().jsonToCharge(chargeJson);
        String chargeId = gqlAuthContext.addCharge(planId, charge);
        Charge savedCharge = gqlAuthContext.getCharge(planId, chargeId);
        LedgerAccountMapping ledgerAccountMapping = chargeJson.getLedgerAccountMapping();

        if (ledgerAccountMapping != null) {
            // todo: this logic should move out of this class, probably into ProductCatalogService.
            //  However ProductCatalogService <> AccountingService currently has a circular dependency that need to be first resolved
            gqlAuthContext.mapLedgerAccountsToCharge(chargeId, ledgerAccountMapping.getLedgerAccountIds());
        }

        return gqlAuthContext.getChargeDetail(savedCharge);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ChargeDetail updateCharge(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("planId") String planId,
        @GraphQLName(CHARGE_KEY) @GraphQLNonNull ChargeJson ignored
    ) {
        // todo split out GQL object from REST JSON and use @GraphQlConstructor
        var chargeJson = getObjectFromMap(env.getArguments().get(CHARGE_KEY), ChargeJson.class);
        GqlAuthContext gqlAuthContext = env.getContext();
        Charge charge = gqlAuthContext.getPlanMapper().jsonToCharge(chargeJson);
        Charge savedCharge = gqlAuthContext.updateCharge(planId, charge);

        LedgerAccountMapping ledgerAccountMapping = chargeJson.getLedgerAccountMapping();

        if (ledgerAccountMapping != null) {
            // todo: this logic should move out of this class, probably into ProductCatalogService.
            //  However ProductCatalogService <> AccountingService currently has a circular dependency that need to be first resolved
            gqlAuthContext.mapLedgerAccountsToCharge(charge.getChargeId(), ledgerAccountMapping.getLedgerAccountIds());
        }

        return gqlAuthContext.getChargeDetail(savedCharge);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ChargeDetail updateChargePartial(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("planId") String planId,
        @GraphQLName(CHARGE_PARTIAL_KEY) @GraphQLNonNull ChargePartialJson ignored
    ) {
        // todo split out GQL object from REST JSON and use @GraphQlConstructor
        var chargePartialJson = getObjectFromMap(env.getArguments().get(CHARGE_PARTIAL_KEY), ChargeJson.class);
        GqlAuthContext gqlAuthContext = env.getContext();
        Charge savedCharge = gqlAuthContext.updateChargePartial(planId, chargePartialJson);
        return gqlAuthContext.getChargeDetail(savedCharge);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ChargeDetail deleteCharge(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("planId") String planId,
        @GraphQLName("chargeId") @GraphQLNonNull String chargeId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Charge deletedCharge = gqlAuthContext.deleteCharge(planId, chargeId);
        return gqlAuthContext.getChargeDetail(deletedCharge);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanDetail addPlan(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("addPlanRequest") AddPlanRequest addPlanRequest) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Plan plan = gqlAuthContext.getPlanMapper().addPlanRequestToPlan(addPlanRequest);
        Plan savedPlan = gqlAuthContext.addPlanWithoutCharges(plan, addPlanRequest.getPlanRelationships());
        return gqlAuthContext.getPlanDetailFromPlan(savedPlan);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanDetail updatePlan(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("updatePlanRequest") UpdatePlanRequest updatePlanRequest
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Plan plan = gqlAuthContext.getPlanMapper().updatePlanRequestToPlan(updatePlanRequest);
        gqlAuthContext.updatePlanWithoutCharges(plan, updatePlanRequest.getPlanRelationships());
        return gqlAuthContext.getPlanDetail(plan.getPlanId());
    }

    @GraphQLField
    @GraphQLNonNull
    public static RateCard upsertRateCard(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName(RATE_CARD) RateCard rateCard) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertRateCard(rateCard);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanGroupInterface upsertPlanGroup(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("planGroup") PlanGroupInterface planGroupInput
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertPlanGroup(planGroupInput);
    }

    @GraphQLField
    @GraphQLNonNull
    public static Proposal createProposal(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("createProposalRequest") CreateProposalRequest createProposalRequest
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.createProposal(createProposalRequest);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanGroupInterface deletePlanGroup(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String planGroupId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.deletePlanGroup(planGroupId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanDetail duplicatePlan(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String planId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var duplicatePlan = gqlAuthContext.duplicatePlan(planId);
        return gqlAuthContext.getPlanDetailFromPlan(duplicatePlan);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanDetail deletePlan(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String planId) {
        GqlAuthContext context = env.getContext();
        PlanDetail planToDelete = context.getPlanDetail(planId);
        context.deletePlan(planId);
        return planToDelete;
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanDetail deprecatePlan(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String planId) {
        GqlAuthContext context = env.getContext();
        Plan plan = context.deprecatePlan(planId);
        return context.getPlanDetailFromPlan(plan);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanDetail reactivatePlan(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String planId) {
        GqlAuthContext context = env.getContext();
        context.reactivatePlan(planId);
        return context.getPlanDetail(planId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanDetail activatePlan(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String planId) {
        GqlAuthContext context = env.getContext();
        Plan plan = context.activatePlan(planId);
        return context.getPlanDetailFromPlan(plan);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanDetail revertToDraft(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String planId) {
        GqlAuthContext context = env.getContext();
        Plan plan = context.revertToDraft(planId);
        return context.getPlanDetailFromPlan(plan);
    }

    // TODO: we have different authz for add & update account, so need to deprecate this endpoint
    @GraphQLField
    @GraphQLNonNull
    public static AccountDetail upsertAccount(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName(ACCOUNT_KEY) AccountJson ignored) {
        AccountJson accountJson = getObjectFromMap(env.getArguments().get(ACCOUNT_KEY), AccountJson.class);
        GqlAuthContext gqlAuthContext = env.getContext();
        Account account = gqlAuthContext.getAccountMapper().jsonToAccount(accountJson);
        AccountAddress address = gqlAuthContext.getAccountMapper().jsonToAccountAddress(accountJson.getAddress());
        Account newAccount = gqlAuthContext.upsertAccount(account, address);
        return gqlAuthContext.getAccountDetail(newAccount.getAccountId());
    }

    @GraphQLField
    public static AccountDetail createAccount(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountInput") CreateAccountInput createAccountInput
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Account account = gqlAuthContext.getAccountMapper().fromCreateAccountInput(createAccountInput);
        AccountAddress address = gqlAuthContext.getAccountMapper().jsonToAccountAddress(createAccountInput.getAddress());
        var addedAccount = gqlAuthContext.addAccount(account, address);
        return gqlAuthContext.getAccountDetail(addedAccount);
    }

    // todo: this is V2 of updateAccount API. Need a multi-step migration to move UI over to use this version.
    @GraphQLField
    public static AccountDetail updateAccountV2(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountInput") UpdateAccountInput updateAccountInput
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Account account = gqlAuthContext.getAccountMapper().fromUpdateAccountInput(updateAccountInput);
        AccountAddress address = gqlAuthContext.getAccountMapper().jsonToAccountAddress(updateAccountInput.getAddress());
        gqlAuthContext.updateAccount(account, address);
        return gqlAuthContext.getAccountDetail(updateAccountInput.getId());
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountJson addAccount(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName(ACCOUNT_KEY) AccountJson ignored) {
        AccountJson accountJson = getObjectFromMap(env.getArguments().get(ACCOUNT_KEY), AccountJson.class);
        GqlAuthContext gqlAuthContext = env.getContext();
        var account = gqlAuthContext.getAccountMapper().jsonToAccount(accountJson);
        AccountAddress address = gqlAuthContext.getAccountMapper().jsonToAccountAddress(accountJson.getAddress());
        var addedAccount = gqlAuthContext.addAccount(account, address);
        return gqlAuthContext.getAccountMapper().accountToJson(addedAccount);
    }

    @GraphQLField
    @GraphQLNonNull
    @Deprecated
    // todo: remove this. this is deprecated in favor of upsertAccount, not in use by UI
    public static AccountJson updateAccount(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName(ACCOUNT_KEY) AccountJson ignored) {
        AccountJson accountJson = getObjectFromMap(env.getArguments().get(ACCOUNT_KEY), AccountJson.class);
        GqlAuthContext gqlAuthContext = env.getContext();
        var account = gqlAuthContext.getAccountMapper().jsonToAccount(accountJson);
        AccountAddress address = gqlAuthContext.getAccountMapper().jsonToAccountAddress(accountJson.getAddress());
        gqlAuthContext.updateAccount(account, address);
        return accountJson;
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountJson deleteAccount(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String id) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var account = gqlAuthContext.deleteAccount(id);
        return gqlAuthContext.getAccountMapper().accountToJson(account);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountContactJson upsertAccountContact(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId,
        @GraphQLName("skipAddressValidation") boolean skipAddressValidation,
        @GraphQLName("strictValidation") boolean strictValidation,
        @GraphQLNonNull @GraphQLName(ACCOUNT_CONTACT_KEY) AccountContactJson accountContactJson
    ) {
        GqlAuthContext context = env.getContext();
        var accountContact = context.getAccountMapper().jsonToAccountContact(accountContactJson);
        accountContact.setAccountId(accountId);
        try {
            var newAccountContact = context.upsertAccountContact(accountContact, skipAddressValidation, strictValidation);
            return context.getAccountMapper().accountContactToJson(newAccountContact);
        } catch (AddressValidationException ex) {
            LOGGER.info("address validation error", ex);
            throw ex;
        }
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull UpsertCRMContactResponse> upsertCrmContacts(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId,
        @GraphQLNonNull @GraphQLName("crmIds") List<@GraphQLNonNull String> crmIds
    ) {
        GqlAuthContext context = env.getContext();
        return context.upsertCrmContacts(accountId, crmIds);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountContactJson deleteAccountContact(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String contactId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var deletedContact = gqlAuthContext.deleteContact(contactId);
        return gqlAuthContext.getAccountMapper().accountContactToJson(deletedContact);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail deleteOrder(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String orderId) {
        GqlAuthContext context = env.getContext();
        OrderDetail orderDetail = context.getOrderDetail(orderId, true, true);
        context.deleteOrder(orderId);
        return orderDetail;
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail upsertOrder(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(ORDER_KEY) OrderRequestJson ignored,
        @GraphQLName(IS_DRY_RUN) Boolean isDryRun,
        @GraphQLName(SANITIZE_LINE_ITEM_DATES) Boolean sanitizeLineItemDates
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        OrderRequestJson orderRequestJson = getObjectFromMap(env.getArguments().get(ORDER_KEY), OrderRequestJson.class);
        // todo: validate json
        isDryRun = BooleanUtils.isTrue(isDryRun);
        sanitizeLineItemDates = BooleanUtils.isTrue(sanitizeLineItemDates);

        Order order = gqlAuthContext.getOrderMapper().jsonToOrder(orderRequestJson);

        if (order.getOrderType() != OrderType.NEW) {
            var message = "Only NEW orderType is supported using upsertOrder. Given OrderType: " + order.getOrderType();
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        Order resultOrder = gqlAuthContext.upsertOrder(order, isDryRun, sanitizeLineItemDates);
        OrderDetail response = gqlAuthContext.getOrderDetail(resultOrder, isDryRun, false);
        if (isDryRun) {
            gqlAuthContext.hydrateCustomFieldsForOrderLineItems(response);
        }
        return response;
    }

    @GraphQLField
    @GraphQLNonNull
    public static DiscountJson upsertDiscount(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName(DISCOUNT_KEY) DiscountJson ignored) {
        GqlAuthContext gqlAuthContext = env.getContext();
        DiscountJson discountJson = getObjectFromMap(env.getArguments().get(DISCOUNT_KEY), DiscountJson.class);
        Discount discount = gqlAuthContext.getDiscountJsonMapper().toDiscount(discountJson);

        Discount savedDiscount = gqlAuthContext.upsertDiscount(discount);
        return gqlAuthContext.getDiscountJsonMapper().toJson(savedDiscount);
    }

    @GraphQLField
    @GraphQLNonNull
    public static DiscountJson updateDiscountStatus(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("discountId") String discountId,
        @GraphQLNonNull @GraphQLName("status") DiscountStatus status
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var discount = gqlAuthContext.getDiscount(discountId, false);

        gqlAuthContext.updateDiscountStatus(discount, status);

        return gqlAuthContext.getDiscountJsonMapper().toJson(discount);
    }

    @GraphQLField
    @GraphQLNonNull
    public static DiscountJson deleteDiscount(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String discountId) {
        GqlAuthContext context = env.getContext();

        Discount deletedDiscount = context.deleteDiscount(discountId);

        return context.getDiscountJsonMapper().toJson(deletedDiscount);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail upsertRenewalOrder(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(ORDER_KEY) OrderRequestJson ignored,
        @GraphQLName(IS_DRY_RUN) Boolean isDryRun
    ) {
        var orderRequestJson = getObjectFromMap(env.getArguments().get(ORDER_KEY), OrderRequestJson.class);
        // todo: validate json
        isDryRun = BooleanUtils.isTrue(isDryRun);

        GqlAuthContext gqlAuthContext = env.getContext();
        var order = gqlAuthContext.getOrderMapper().jsonToOrder(orderRequestJson);

        if (order.getOrderType() != OrderType.RENEWAL) {
            var message = "Only RENEW orderType is supported using updateRenewOrder. Given OrderType: " + order.getOrderType();
            LOGGER.info(message);
            throw new IllegalArgumentException(message);
        }

        Order resultOrder = gqlAuthContext.upsertOrder(order, isDryRun, false);
        OrderDetail response = gqlAuthContext.getOrderDetail(resultOrder, isDryRun, false);
        if (isDryRun) {
            gqlAuthContext.hydrateCustomFieldsForOrderLineItems(response);
        }
        return response;
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail rebaseAmendment(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.rebaseAmendment(orderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail upsertAmendment(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(ORDER_KEY) AmendmentOrderRequest ignored,
        @GraphQLName(IS_DRY_RUN) Boolean isDryRun,
        @GraphQLName(POPULATE_MISSING_LINE_ITEMS) Boolean populateMissingLineItems
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var amendmentOrderRequestJson = getObjectFromMap(env.getArguments().get(ORDER_KEY), AmendmentOrderRequest.class);
        isDryRun = BooleanUtils.isTrue(isDryRun);
        populateMissingLineItems = BooleanUtils.isTrue(populateMissingLineItems);

        Order order = gqlAuthContext.getAmendmentOrderMapper().jsonToOrder(amendmentOrderRequestJson);
        validateUpsertAmendmentOrder(order);

        var resultOrder = gqlAuthContext.upsertChangeOrder(order, isDryRun, populateMissingLineItems);
        OrderDetail response = gqlAuthContext.getOrderDetail(resultOrder, isDryRun, false);
        if (isDryRun) {
            gqlAuthContext.hydrateCustomFieldsForOrderLineItems(response);
        }
        return response;
    }

    private static void validateUpsertAmendmentOrder(Order order) {
        if (order.getOrderType() != OrderType.AMENDMENT && order.getOrderType() != OrderType.CANCEL) {
            var message = "Only AMENDMENT and CANCEL order types are supported using upsertAmendment.  Given OrderType: " + order.getOrderType();
            LOGGER.info(message);
            throw new InvalidInputException(message);
        }
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderStatusUpdateResponse updateOrderStatus(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("orderId") String orderId,
        @GraphQLNonNull @GraphQLName("status") String status,
        @GraphQLName("statusUpdatedOn") Long statusUpdatedOn,
        @GraphQLName("adminApprovalFlowByPass") Boolean adminApprovalByPass
    ) {
        boolean adminApprovalFlowByPass = BooleanUtils.isTrue(adminApprovalByPass);
        GqlAuthContext gqlAuthContext = env.getContext();
        var orderStatus = OrderStatus.valueOf(status.toUpperCase());

        gqlAuthContext.updateOrderStatus(orderId, orderStatus, statusUpdatedOn, adminApprovalFlowByPass);
        Order updatedOrder = gqlAuthContext.getOrderByOrderId(orderId);

        return new OrderStatusUpdateResponse(updatedOrder.getOrderId(), updatedOrder.getStatus(), updatedOrder.getOrderType());
    }

    @GraphQLField
    @GraphQLNonNull
    public static InvoiceDetail deleteInvoice(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber) {
        GqlAuthContext context = env.getContext();
        Invoice deletedInvoice = context.deleteInvoice(new Invoice.Number(invoiceNumber));
        return context.getInvoiceDetail(deletedInvoice);
    }

    @GraphQLField
    @GraphQLNonNull
    public static InvoiceDetail updateInvoice(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber,
        @GraphQLNonNull @GraphQLName("updateInvoice") UpdateInvoiceMutation updateInvoiceMutation
    ) {
        GqlAuthContext context = env.getContext();
        UpdateInvoiceRequest updateInvoiceRequest = new UpdateInvoiceRequest(
            updateInvoiceMutation.getInvoiceDate(),
            updateInvoiceMutation.getDueDate(),
            updateInvoiceMutation.getNote(),
            updateInvoiceMutation.getPurchaseOrderNumber(),
            updateInvoiceMutation.getBillingContactId(),
            updateInvoiceMutation.getEmailNotifiersList()
        );
        Invoice invoice = context.updateInvoice(new Invoice.Number(invoiceNumber), updateInvoiceRequest);
        return context.getInvoiceDetail(invoice);
    }

    @GraphQLField
    @GraphQLNonNull
    public static InvoiceDetail voidInvoice(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("voidInvoice") VoidInvoiceMutation voidInvoiceMutation
    ) {
        GqlAuthContext context = env.getContext();
        return context.voidInvoice(
            new Invoice.Number(voidInvoiceMutation.getInvoiceNumber()),
            voidInvoiceMutation.getInvoiceBalance(),
            Instant.ofEpochSecond(voidInvoiceMutation.getVoidDate())
        );
    }

    @GraphQLField
    @GraphQLNonNull
    public static InvoiceDetail postInvoice(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber) {
        GqlAuthContext context = env.getContext();
        Invoice invoice = context.postInvoice(new Invoice.Number(invoiceNumber));
        return context.getInvoiceDetail(invoice.getInvoiceNumber());
    }

    @GraphQLField
    @GraphQLNonNull
    public static SubscriptionDetail updateSubscriptionAttributes(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("subscriptionUpdate") SubscriptionUpdateJson subscriptionUpdate
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.updateSubscriptionAttributes(subscriptionId, subscriptionUpdate);
        return gqlAuthContext.getSubscriptionDetail(subscriptionId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static Boolean deleteSubscription(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("version") int version
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.deleteSubscription(subscriptionId, version);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static Boolean revertSubscription(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("version") int version
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.revertSubscription(subscriptionId, version);
        return true;
    }

    @GraphQLField
    public static InvoiceDetail generateInvoice(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("targetDate") Long targetDate,
        @GraphQLName("invoiceDate") Long invoiceDate,
        @GraphQLNonNull @GraphQLName("invoiceChargeInclusionOption") InvoiceChargeInclusionOption invoiceChargeInclusionOption
    ) {
        GqlAuthContext context = env.getContext();
        var invoice = context.generateInvoice(
            subscriptionId,
            Instant.ofEpochSecond(targetDate),
            invoiceDate == null ? null : Instant.ofEpochSecond(invoiceDate),
            invoiceChargeInclusionOption
        );
        return invoice.map(context::getInvoiceDetail).orElse(null);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean deleteVoidedInvoice(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.deleteVoidedInvoice(invoiceNumber);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull SettlementApplicationDetail> applyManualPayment(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(ApplyPaymentRequest.NAME) ApplyPaymentRequest ignored
    ) {
        GqlAuthContext context = env.getContext();
        var applyPaymentRequest = getObjectFromMap(env.getArguments().get(ApplyPaymentRequest.NAME), ApplyPaymentRequest.class);

        var invoiceNumber = new Invoice.Number(applyPaymentRequest.getInvoiceNumber());
        context.addAndApplyManualPayment(
            invoiceNumber,
            new BigDecimal(applyPaymentRequest.getInvoiceAmount()), // todo: why is invoice amount a string?
            null, // PaymentMethodId is not expected from UI
            applyPaymentRequest.getPaymentType(),
            applyPaymentRequest.getPaymentDate(),
            applyPaymentRequest.getAmount(),
            applyPaymentRequest.getNote(),
            applyPaymentRequest.getBankFee(),
            applyPaymentRequest.getPaymentBankAccountId()
        );

        var settlementApplications = context.getAppliedSettlementApplications(invoiceNumber);

        return context.getSettlementApplicationDetailMapper().toSettlementApplicationDetailsList(settlementApplications);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentBankAccountJson upsertPaymentBankAccount(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("paymentBankAccount") PaymentBankAccountJson paymentBankAccountRequest
    ) {
        GqlAuthContext context = env.getContext();
        return context.upsertPaymentBankAccount(paymentBankAccountRequest);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentBankAccountJson deletePaymentBankAccount(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String id) {
        GqlAuthContext context = env.getContext();
        return context.deletePaymentBankAccount(id);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull SettlementApplicationDetail> voidPayment(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(VoidPaymentRequest.NAME) VoidPaymentRequest ignored
    ) {
        GqlAuthContext context = env.getContext();
        VoidPaymentRequest voidPaymentRequest = getObjectFromMap(env.getArguments().get(VoidPaymentRequest.NAME), VoidPaymentRequest.class);
        Invoice.Number invoiceNumber = new Invoice.Number(voidPaymentRequest.getInvoiceNumber());
        context.voidPayment(
            voidPaymentRequest.getPaymentId(),
            invoiceNumber,
            DateTimeConverter.epochSecondsToInstant(voidPaymentRequest.getVoidDate()),
            voidPaymentRequest.getInvoiceBalance(),
            voidPaymentRequest.getNote()
        );

        List<SettlementApplication> settlementApplications = context.getAppliedSettlementApplications(invoiceNumber);

        return context.getSettlementApplicationDetailMapper().toSettlementApplicationDetailsList(settlementApplications);
    }

    @GraphQLField
    @GraphQLNonNull
    @Deprecated
    public static UserJson upsertUser(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("user") UserJson userJson) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var user = gqlAuthContext.getUserMapper().jsonToUser(userJson);

        String userId = userJson.getId();

        if (StringUtils.isBlank(userId)) {
            userId = gqlAuthContext.createUser(user);
        } else {
            gqlAuthContext.updateUser(user);
        }

        var storedUser = gqlAuthContext.getUser(userId);
        return gqlAuthContext.getUserDataAggregator().getJson(storedUser);
    }

    @GraphQLField
    @GraphQLNonNull
    public static UserJson upsertUserV2(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("user") UserInput userInput) {
        GqlAuthContext gqlAuthContext = env.getContext();
        User user = gqlAuthContext.getUserMapper().userInputToUser(userInput);

        String userId = userInput.getId();
        if (StringUtils.isBlank(userId)) {
            userId = gqlAuthContext.createUser(user);
        } else {
            gqlAuthContext.updateUser(user);
        }

        User storedUser = gqlAuthContext.getUser(userId);
        return gqlAuthContext.getUserDataAggregator().getJson(storedUser);
    }

    @GraphQLField
    @GraphQLNonNull
    public static EntityJson addEntity(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("entity") ImmutableEntityJson entityJson) {
        GqlAuthContext gqlAuthContext = env.getContext();
        EntityJsonMapper entityJsonMapper = gqlAuthContext.getEntityJsonMapper();
        Entity entity = gqlAuthContext.addEntity(entityJsonMapper.fromJson(entityJson));
        return entityJsonMapper.toJson(entity);
    }

    @GraphQLField
    @GraphQLNonNull
    public static EntityJson updateEntity(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("entity") EntityPatchRequest entityPatchRequest) {
        GqlAuthContext gqlAuthContext = env.getContext();
        EntityJsonMapper entityJsonMapper = gqlAuthContext.getEntityJsonMapper();
        Entity entity = gqlAuthContext.updateEntity(entityPatchRequest);
        return entityJsonMapper.toJson(entity);
    }

    @GraphQLField
    @GraphQLNonNull
    public static Boolean deleteEntity(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("entityId") String entityId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.deleteEntity(entityId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static UserGroupDetail upsertUserGroup(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(USER_GROUP_KEY) UserGroupRequestJson userGroupRequestJson
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var userGroup = gqlAuthContext.getUserGroupMapper().toUserGroup(userGroupRequestJson);

        var savedUserGroup = gqlAuthContext.upsertUserGroup(userGroup, true);
        return gqlAuthContext.getUserGroupMapper().toJson(savedUserGroup);
    }

    @GraphQLField
    @GraphQLNonNull
    public static UserGroupDetail deleteUserGroup(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String userGroupId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var userGroup = gqlAuthContext.deleteUserGroup(userGroupId, true);
        return gqlAuthContext.getUserGroupMapper().toJson(userGroup);
    }

    @GraphQLField
    @GraphQLNonNull
    public static TenantCreationResponseJson addTenant(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName(TENANT_KEY) TenantJson ignored) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var tenantJson = getObjectFromMap(env.getArguments().get(TENANT_KEY), TenantJson.class);
        var tenant = gqlAuthContext.getTenantMapper().jsonToTenant(tenantJson);
        return gqlAuthContext.createTenant(tenant);
    }

    @GraphQLField
    @GraphQLNonNull
    public static TenantDetails updateTenantDetails(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(TENANT_KEY) UpdateTenantRequest ignored
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var tenantDetailsMapper = gqlAuthContext.getTenantDetailsMapper();
        var updateTenantRequest = getObjectFromMap(env.getArguments().get(TENANT_KEY), UpdateTenantRequest.class);
        var tenant = tenantDetailsMapper.updateRequestToTenant(updateTenantRequest);
        gqlAuthContext.updateTenant(tenant);
        tenant = gqlAuthContext.getCurrentTenant();
        return tenantDetailsMapper.tenantToTenantInfo(tenant);
    }

    // TODO: Remove. We are not doing a hard delete. Keeping it for now in case of requirement for backwards compatibility.
    @GraphQLField
    @GraphQLNonNull
    public static UserJson deleteUser(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String userId) {
        return disableUser(env, userId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static UserJson disableUser(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String userId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        User user = gqlAuthContext.disableUser(userId);
        return gqlAuthContext.getUserDataAggregator().getJson(user);
    }

    @GraphQLField
    @GraphQLNonNull
    public static UserJson enableUser(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String userId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var user = gqlAuthContext.enableUser(userId);
        return gqlAuthContext.getUserDataAggregator().getJson(user);
    }

    @GraphQLField
    @GraphQLNonNull
    public static UnitOfMeasureJson upsertUnitOfMeasure(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(UNIT_OF_MEASURE_KEY) ImmutableUnitOfMeasureRequest unitOfMeasureRequest
    ) {
        GqlAuthContext context = env.getContext();

        var id = StringUtils.isBlank(unitOfMeasureRequest.getId()) ? null : UUID.fromString(unitOfMeasureRequest.getId());

        var unitOfMeasure = new UnitOfMeasure(id, unitOfMeasureRequest.getName(), unitOfMeasureRequest.getDescription(), UnitOfMeasureStatus.ACTIVE);

        if (unitOfMeasure.getId() != null) {
            context.updateUnitOfMeasure(unitOfMeasure);
            return context.getUnitOfMeasureJsonMapper().toJson(unitOfMeasure);
        } else {
            UnitOfMeasure savedUnitOfMeasure = context.addUnitOfMeasure(unitOfMeasure);
            return context.getUnitOfMeasureJsonMapper().toJson(savedUnitOfMeasure);
        }
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean resendCognitoInvitationEmailForExistingUser(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("email") String email
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.resendCognitoInvitationEmailForExistingUser(email);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean emailLinkForLogin(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("email") String email) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.emailLinkForLogin(email);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static UnitOfMeasureJson addUnitOfMeasure(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(UNIT_OF_MEASURE_KEY) UnitOfMeasureRequest ignored
    ) {
        GqlAuthContext context = env.getContext();
        var unitOfMeasureRequest = getObjectFromMap(env.getArguments().get(UNIT_OF_MEASURE_KEY), UnitOfMeasureRequest.class);

        // todo: status management for UOM. Requires some UX design work
        var unitOfMeasure = new UnitOfMeasure(
            null,
            unitOfMeasureRequest.getName(),
            unitOfMeasureRequest.getDescription(),
            UnitOfMeasureStatus.ACTIVE
        );

        UnitOfMeasure savedUnitOfMeasure = context.addUnitOfMeasure(unitOfMeasure);

        return context.getUnitOfMeasureJsonMapper().toJson(savedUnitOfMeasure);
    }

    @GraphQLField
    @GraphQLNonNull
    public static UnitOfMeasureJson deleteUnitOfMeasure(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String unitOfMeasureId) {
        if (StringUtils.isBlank(unitOfMeasureId)) {
            throw new IllegalArgumentException("id is required");
        }

        GqlAuthContext context = env.getContext();

        var deletedUnitOfMeasure = context.deleteUnitOfMeasure(UUID.fromString(unitOfMeasureId));
        return context.getUnitOfMeasureJsonMapper().toJson(deletedUnitOfMeasure);
    }

    @GraphQLField
    @GraphQLNonNull
    public static TaxRateJson upsertTaxRate(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName(TAX_RATE_KEY) UpsertTaxRateRequest ignored) {
        GqlAuthContext context = env.getContext();
        var upsertTaxRateRequest = getObjectFromMap(env.getArguments().get(TAX_RATE_KEY), UpsertTaxRateRequest.class);
        TaxRate taxRate = context.getUpsertTaxRateMapper().toTaxRate(upsertTaxRateRequest);
        TaxRate savedTaxRate;

        if (taxRate.getId() == null) {
            savedTaxRate = context.addTaxRate(taxRate);
        } else {
            context.updateTaxRate(taxRate);
            savedTaxRate = context.getTaxRate(taxRate.getId());
        }

        return context.getTaxRateJsonMapper().toTaxRateJson(savedTaxRate);
    }

    @GraphQLField
    @GraphQLNonNull
    public static TaxRateJson deleteTaxRate(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String taxRateId) {
        GqlAuthContext context = env.getContext();

        if (StringUtils.isBlank(taxRateId)) {
            throw new IllegalArgumentException("id is required");
        }

        TaxRate deletedTaxRate = context.deleteTaxRate(UUID.fromString(taxRateId));

        return context.getTaxRateJsonMapper().toTaxRateJson(deletedTaxRate);
    }

    @GraphQLField
    @GraphQLNonNull
    public static TenantSettingJson updateSetting(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName(SETTING_KEY) TenantSettingJson ignored) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var tenantSettingJson = getObjectFromMap(env.getArguments().get(SETTING_KEY), TenantSettingJson.class);
        var tenantSetting = gqlAuthContext.getTenantSettingJsonMapper().jsonToTenantSetting(tenantSettingJson);
        gqlAuthContext.upsertTenantSetting(tenantSetting);
        return tenantSettingJson;
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull CurrencyJson> updateSupportedCurrencies(
        DataFetchingEnvironment env,
        @GraphQLName("currencies") @GraphQLNonNull List<@GraphQLNonNull String> currencies
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.updateSupportedCurrencies(currencies);
        return gqlAuthContext.getSupportedCurrencies();
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountReceivableContactJson updateAccountReceivableContact(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(ACCOUNT_RECEIVABLE_CONTACT_KEY) AccountReceivableContactJson ignored
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var contactJson = getObjectFromMap(env.getArguments().get(ACCOUNT_RECEIVABLE_CONTACT_KEY), AccountReceivableContactJson.class);
        var contact = gqlAuthContext.getAccountReceivableContactJsonMapper().toAccountContact(contactJson);

        var updatedContact = gqlAuthContext.upsertAccountReceivableContact(contact);
        return gqlAuthContext.getAccountReceivableContactJsonMapper().toJson(updatedContact);
    }

    @GraphQLField
    @GraphQLNonNull
    public static DunningSettingDetails updateDunningSetting(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(DUNNING_SETTING_KEY) DunningSettingDetails ignored
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var dunningSettingDetails = getObjectFromMap(env.getArguments().get(DUNNING_SETTING_KEY), DunningSettingDetails.class);
        var dunningSettingJson = DunningSettingDetailsMapper.toDunningSetting(dunningSettingDetails);
        var dunningSetting = gqlAuthContext.getDunningSettingJsonMapper().jsonToDunningSetting(dunningSettingJson);

        var updatedSetting = gqlAuthContext.upsertDunningSetting(dunningSetting);
        var updatedSettingJson = gqlAuthContext.getDunningSettingJsonMapper().dunningSettingToJson(updatedSetting);
        return DunningSettingDetailsMapper.toDetails(updatedSettingJson);
    }

    @GraphQLField
    @GraphQLNonNull
    public static NotificationTargetAndSubscriptions subscribeExistingNotificationTargetToEvent(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("notificationId") String notificationIdString,
        @GraphQLNonNull @GraphQLName("notificationEventType") NotificationEventType notificationEventType
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        UUID notificationId = UUID.fromString(notificationIdString);
        gqlAuthContext.subscribeExistingNotificationTargetToEvent(notificationId, notificationEventType);
        return gqlAuthContext.getNotificationTargetAndSubscriptionsById(notificationId, false);
    }

    @GraphQLField
    @GraphQLNonNull
    public static NotificationTargetAndSubscriptions unsubscribeNotificationEventForTarget(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("notificationId") String notificationIdString,
        @GraphQLNonNull @GraphQLName("notificationEventType") NotificationEventType notificationEventType
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        UUID notificationId = UUID.fromString(notificationIdString);
        gqlAuthContext.unsubscribeNotificationTargetFromEvent(notificationId, notificationEventType);
        return gqlAuthContext.getNotificationTargetAndSubscriptionsById(notificationId, false);
    }

    @GraphQLField
    @GraphQLNonNull
    public static NotificationTargetAndSubscriptions upsertNotificationTargetAndSubscriptions(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(NOTIFICATION_KEY) NotificationTargetAndSubscriptions ignored
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var notificationTargetAndSubscriptions = getObjectFromMap(env.getArguments().get(NOTIFICATION_KEY), NotificationTargetAndSubscriptions.class);
        String notificationId = notificationTargetAndSubscriptions.getNotificationId();
        UUID savedNotificationId;
        if (StringUtils.isBlank(notificationId)) {
            savedNotificationId = gqlAuthContext.addNotificationTargetAndSubscriptions(notificationTargetAndSubscriptions);
        } else {
            savedNotificationId = gqlAuthContext.updateNotificationTargetAndSubscriptions(
                UUID.fromString(notificationId),
                notificationTargetAndSubscriptions
            );
        }
        return gqlAuthContext.getNotificationTargetAndSubscriptionsById(savedNotificationId, false);
    }

    @GraphQLField
    @GraphQLNonNull
    public static NotificationTargetAndSubscriptions unsubscribeNotificationTarget(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("notificationId") String notificationIdString
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        UUID notificationId = UUID.fromString(notificationIdString);
        gqlAuthContext.unsubscribeNotificationTarget(notificationId);
        return gqlAuthContext.getNotificationTargetAndSubscriptionsById(notificationId, true);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AvalaraIntegration upsertAvalaraIntegration(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId,
        @GraphQLName("companyCode") String companyCode,
        @GraphQLNonNull @GraphQLName("accountLicenseKey") String accountLicenseKey,
        @GraphQLNonNull @GraphQLName("sandboxEnvironment") Boolean sandboxEnvironment,
        // TODO: make this non-null after UI is developed
        @GraphQLName("shouldCommitDocuments") Boolean shouldCommitDocuments
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        AvalaraIntegrationInput integrationInput = toIntegrationInput(accountId, companyCode, accountLicenseKey, sandboxEnvironment);
        if (shouldCommitDocuments != null) {
            integrationInput.setShouldCommitDocuments(shouldCommitDocuments);
        }
        String integrationId = gqlAuthContext.integrateAvalara(integrationInput);
        return gqlAuthContext.getAvalaraIntegrationById(integrationId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static String pingAvalara(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId,
        @GraphQLName("companyCode") String companyCode,
        @GraphQLNonNull @GraphQLName("accountLicenseKey") String accountLicenseKey,
        @GraphQLNonNull @GraphQLName("sandboxEnvironment") Boolean sandboxEnvironment
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.pingAvalara(toIntegrationInput(accountId, companyCode, accountLicenseKey, sandboxEnvironment));
        return PONG;
    }

    private static AvalaraIntegrationInput toIntegrationInput(
        String accountId,
        String companyCode,
        String accountLicenseKey,
        Boolean sandboxEnvironment
    ) {
        AvalaraIntegrationInput integrationInput = new AvalaraIntegrationInput();
        integrationInput.setAccountId(accountId);
        integrationInput.setCompanyCode(companyCode);
        integrationInput.setAccountLicenseKey(accountLicenseKey);
        integrationInput.setSandboxEnvironment(sandboxEnvironment);
        return integrationInput;
    }

    @GraphQLField
    @GraphQLNonNull
    public static String initiateSalesforceIntegration(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(SALESFORCE_INTEGRATION_REQUEST_KEY) SalesforceClientIntegrationRequestJson ignored
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var initiationRequest = getObjectFromMap(
            env.getArguments().get(SALESFORCE_INTEGRATION_REQUEST_KEY),
            SalesforceClientIntegrationRequestJson.class
        );
        gqlAuthContext.throwIfTenantAlreadyHasACrmIntegration();
        return gqlAuthContext.initiateSalesforceIntegration(initiationRequest);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean completeSalesforceIntegration(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("code") String authorizationCode,
        @GraphQLNonNull @GraphQLName("state") String integrationId,
        @GraphQLNonNull @GraphQLName("redirect_uri") String redirectUri
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.completeSalesforceIntegration(authorizationCode, integrationId, redirectUri);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean deleteSalesforceIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        String tenantId = gqlAuthContext.provideTenantIdString();
        gqlAuthContext.deleteSalesforceIntegration(tenantId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail updatePrimaryOrderIdForSalesforceOpportunity(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("orderId") String orderId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Order order = gqlAuthContext.getOrderByOrderId(orderId);
        gqlAuthContext.updatePrimaryOrderIdForSalesforceOpportunity(order);
        return gqlAuthContext.getOrderDetail(order, true, true);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail updatePrimaryOrderIdForHubSpotOpportunity(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("orderId") String orderId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Order order = gqlAuthContext.getOrderByOrderId(orderId);
        gqlAuthContext.updatePrimaryOrderIdForHubSpotOpportunity(order);
        return gqlAuthContext.getOrderDetail(order, true, true);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail updatePrimaryOrderIdForOpportunity(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("orderId") String orderId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Order order = gqlAuthContext.getOrderByOrderId(orderId);
        gqlAuthContext.updatePrimaryOrderIdForOpportunity(order);
        return gqlAuthContext.getOrderDetail(order, true, true);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail syncOrderToSalesforce(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Order order = gqlAuthContext.getOrderByOrderId(orderId);
        gqlAuthContext.addSalesforceJobUnit(order);
        return gqlAuthContext.getOrderDetail(order, true, true);
    }

    @GraphQLField
    @GraphQLNonNull
    public static String generateApiKeyV3(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(API_KEY_REQUEST) ApiKeyGenerationRequest ignored
    ) {
        ApiKeyGenerationRequest apiKeyGenerationRequest = getObjectFromMap(env.getArguments().get(API_KEY_REQUEST), ApiKeyGenerationRequest.class);
        GqlAuthContext gqlAuthContext = env.getContext();
        ApiKeyTokenAndSecret apiKeyTokenAndSecret = gqlAuthContext.generateNewApiKey(
            Optional.ofNullable(apiKeyGenerationRequest.getRole()),
            Optional.ofNullable(apiKeyGenerationRequest.getExpiry()).map(Instant::ofEpochSecond),
            Optional.ofNullable(apiKeyGenerationRequest.getNote()),
            Optional.ofNullable(apiKeyGenerationRequest.getUserId()),
            Optional.ofNullable(apiKeyGenerationRequest.getEntityId())
        );

        return apiKeyTokenAndSecret.getJwtToken().getSecretValue();
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean revokeApiKeyV3(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String keyId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.revokeApiKey(keyId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentTemplateResponse upsertDocumentTemplate(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("documentTemplate") UpsertDocumentTemplateRequest upsertDocumentTemplateRequest
    ) {
        GqlAuthContext context = env.getContext();
        var mapper = context.getDocumentTemplateMapper();

        var documentTemplate = mapper.requestToDocumentTemplate(upsertDocumentTemplateRequest);
        if (StringUtils.isBlank(documentTemplate.getTemplateId())) {
            var template = context.addDocumentTemplate(documentTemplate);
            return mapper.documentTemplateToResponse(template);
        }
        context.updateDocumentTemplate(documentTemplate);
        DocumentTemplate template = context.getDocumentTemplateByTemplateId(documentTemplate.getTemplateId());
        return mapper.documentTemplateToResponse(template);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CustomPredefinedTemplateOnOrder upsertCustomPredefinedTemplateOnOrder(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("documentTemplate") CustomPredefinedTemplateOnOrder documentTemplate
    ) {
        GqlAuthContext context = env.getContext();
        return context.upsertCustomPredefinedTemplateOnOrder(documentTemplate);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CustomPredefinedTemplateOnOrder deleteCustomPredefinedTemplateOnOrder(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("templateId") String templateId,
        @GraphQLNonNull @GraphQLName("orderId") String orderId
    ) {
        GqlAuthContext context = env.getContext();
        return context.deleteCustomPredefinedTemplateOnOrder(templateId, orderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentTemplateResponse updateDocumentTemplateStatus(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("templateId") String templateId,
        @GraphQLNonNull @GraphQLName("status") DocumentTemplateStatus status
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        DocumentTemplate documentTemplate = gqlAuthContext.getDocumentTemplateByTemplateId(templateId);

        gqlAuthContext.updateDocumentTemplateStatus(documentTemplate, status);

        return gqlAuthContext.getDocumentTemplateMapper().documentTemplateToResponse(documentTemplate);
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentTemplateResponse deleteDocumentTemplate(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String id) {
        GqlAuthContext context = env.getContext();
        List<DocumentTemplate> deletedTemplates = context.deleteDocumentTemplates(id, Optional.empty());
        // Not sure why, but the UI does not like it when we return anything but a single template
        return context.getDocumentTemplateMapper().documentTemplateToResponse(deletedTemplates.get(0));
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentSection upsertDocumentSection(
        DataFetchingEnvironment env,
        @GraphQLName("documentSection") @GraphQLNonNull DocumentSection documentSection
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertDocumentSection(documentSection);
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentMasterTemplate upsertDocumentMasterTemplate(
        DataFetchingEnvironment env,
        @GraphQLName("documentMasterTemplate") @GraphQLNonNull DocumentMasterTemplate documentMasterTemplate
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertDocumentMasterTemplate(documentMasterTemplate, Optional.empty());
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentMasterTemplate upsertDocumentMasterTemplateV2(
        DataFetchingEnvironment env,
        @GraphQLName("documentMasterTemplate") @GraphQLNonNull DocumentMasterTemplate documentMasterTemplate
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertDocumentMasterTemplateV2(documentMasterTemplate, Optional.empty());
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentMasterTemplate updateDocumentMasterTemplateStatus(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String masterTemplateId,
        @GraphQLNonNull @GraphQLName("status") DocumentMasterTemplateStatus status
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        DocumentMasterTemplate masterTemplate = gqlAuthContext
            .getDocumentMasterTemplateById(masterTemplateId, Optional.empty())
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_MASTER_TEMPLATE, masterTemplateId));

        gqlAuthContext.updateDocumentMasterTemplateStatus(masterTemplate, status, Optional.empty());

        return masterTemplate;
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentMasterTemplate deleteDocumentMasterTemplate(
        DataFetchingEnvironment env,
        @GraphQLName("id") @GraphQLNonNull String masterTemplateId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.deleteDocumentMasterTemplate(masterTemplateId, Optional.empty());
    }

    @GraphQLField
    @GraphQLNonNull
    public static InvoiceEmailResponse sendInvoiceEmail(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumberString
    ) {
        GqlAuthContext context = env.getContext();
        Invoice.Number invoiceNumber = new Invoice.Number(invoiceNumberString);
        return context.sendInvoiceEmail(invoiceNumber);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CreditMemoEmailResponse sendCreditMemoEmail(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("creditMemoNumber") String creditMemoNumber
    ) {
        GqlAuthContext context = env.getContext();
        CreditMemoDocumentJson creditMemoDocumentJson = context.aggregateDataForCreditMemoDocument(creditMemoNumber);

        CreditMemoTemplateData creditMemoTemplateData = new CreditMemoTemplateData(creditMemoDocumentJson);
        Optional<InputStream> creditMemoDocument = context.getCreditMemoDocumentByCreditMemoNumber(creditMemoNumber);
        if (creditMemoDocument.isEmpty()) {
            throw new ServiceFailureException("Failed to get the document for credit memo with number: " + creditMemoNumber);
        }

        return context.sendCreditMemoEmail(creditMemoTemplateData, creditMemoDocumentJson.getCreditMemoDetail(), creditMemoDocument.get());
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountContactJson addMailingContact(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("contactId") String contactId
    ) {
        GqlAuthContext context = env.getContext();
        AccountContact emailContact = context.addEmailContact(subscriptionId, contactId);
        return context.getAccountMapper().accountContactToJson(emailContact);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountContactJson deleteMailingContact(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("contactId") String contactId
    ) {
        GqlAuthContext context = env.getContext();
        AccountContact emailContact = context.deleteEmailContact(subscriptionId, contactId);
        return context.getAccountMapper().accountContactToJson(emailContact);
    }

    @GraphQLField
    @GraphQLNonNull
    public static String initiateDocuSignIntegration(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(DOCUSIGN_INTEGRATION_REQUEST_KEY) DocuSignIntegrationRequestJson ignored
    ) {
        GqlAuthContext context = env.getContext();
        var docuSignIntegrationRequest = getObjectFromMap(
            env.getArguments().get(DOCUSIGN_INTEGRATION_REQUEST_KEY),
            DocuSignIntegrationRequestJson.class
        );
        return context.initiateDocuSignIntegration(docuSignIntegrationRequest).toString();
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean completeDocuSignIntegration(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("code") String authorizationCode,
        @GraphQLNonNull @GraphQLName("state") String integrationId
    ) {
        GqlAuthContext context = env.getContext();
        context.completeDocuSignIntegration(authorizationCode, integrationId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocuSignIntegrationResponseJson deleteDocuSignIntegration(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        String tenantId = context.provideTenantIdString();
        DocuSignIntegration deletedDocuSignIntegration = context.deleteDocuSignIntegration(tenantId);
        return context.getDocuSignMapper().convertIntegrationToResponseJson(deletedDocuSignIntegration);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ReportJobResponse generateReport(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("reportRequest") PredefinedReportJson reportRequest
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        ReportJobResponse result = gqlAuthContext.generatePredefinedReport(reportRequest);
        result.setUri(UriBuilder.fromPath(ReportResource.REPORT_RESULTS_PATH).build(result.getUri()).toString());
        return result;
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentStripeConnectIntegrationSetupResponse initiateStripeConnectIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.initiateStripeConnectIntegration();
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean completeStripeConnectIntegration(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("code") String authorizationCode,
        @GraphQLNonNull @GraphQLName("scope") String scope,
        @GraphQLNonNull @GraphQLName("state") String integrationId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.completeStripeConnectIntegration(authorizationCode, scope, integrationId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentStripeConnectIntegration deleteStripeConnectIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.deleteCurrentStripePaymentIntegration();
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean unEnrollFromAutomaticPayments(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("paymentLinkId") String paymentLinkId,
        @GraphQLNonNull @GraphQLName("paymentLinkType") PaymentManagementLinkType paymentLinkType
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.unEnrollFromAutomaticPayments(paymentLinkId, paymentLinkType);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean reEnableAutomaticPayments(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("paymentLinkId") String paymentLinkId,
        @GraphQLNonNull @GraphQLName("paymentLinkType") PaymentManagementLinkType paymentLinkType
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.reEnableAutomaticPayments(paymentLinkId, paymentLinkType);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static String generatePaymentLink(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId,
        @GraphQLNonNull @GraphQLName("actionType") AddPaymentMethodActionType actionType
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        String tenantId = gqlAuthContext.provideTenantIdString();
        PaymentStripeConnectIntegration paymentIntegration = gqlAuthContext.getStripeConnectIntegrationThrowIfNotExists(tenantId);
        return gqlAuthContext.generateLink(accountId, tenantId, actionType, null, PaymentLinkSourceType.EXTERNAL, paymentIntegration.getId());
    }

    @GraphQLField
    public static InvoicePaymentManagementLinkGqlResponse generateInvoicePaymentLink(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId,
        @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        InvoicePaymentManagementLink link = gqlAuthContext.generateInvoicePaymentLink(accountId, invoiceNumber);
        if (link == null) {
            return null;
        }
        return new InvoicePaymentManagementLinkGqlResponse(link);
    }

    @GraphQLField
    public static AccountPaymentManagementLinkGqlResponse generateAccountPaymentLink(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        AccountPaymentManagementLink link = gqlAuthContext.generateAccountPaymentLink(accountId);
        if (link == null) {
            return null;
        }
        return new AccountPaymentManagementLinkGqlResponse(link);
    }

    // TODO: this is a workaround to building the input object.
    //  Using GraphQL annotations library, the object doesn't get populated.
    //  should revisit this when time permits to see if there is a configuration somewhere that isn't being set properly (or may be a bug in the library)
    private static <T> T getObjectFromMap(Object input, Class<T> classOfT) {
        var gson = new Gson();
        var jsonElement = gson.toJsonTree(input);
        return gson.fromJson(jsonElement, classOfT);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ApprovalFlowDetail upsertApprovalFlow(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(APPROVAL_FLOW_KEY) ApprovalFlowJson approvalFlowJson
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var approvalFlow = gqlAuthContext.getApprovalFlowMapper().toApprovalFlow(approvalFlowJson);
        var savedApprovalFlow = gqlAuthContext.upsertApprovalFlow(approvalFlow);

        return gqlAuthContext.getApprovalFlowDataAggregator().toApprovalFlowDetail(savedApprovalFlow);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ApprovalFlowDetail deleteApprovalFlow(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String approvalFlowId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var approvalFlow = gqlAuthContext.deleteApprovalFlow(approvalFlowId);
        return gqlAuthContext.getApprovalFlowDataAggregator().toApprovalFlowDetail(approvalFlow);
    }

    @GraphQLField
    public static OrderDetail updateOrderApprovalState(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(ORDER_APPROVAL_FLOW_STATE_UPDATES) ApprovalFlowInstanceStateUpdates approvalFlowInstanceStateUpdates
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Optional<ApprovalFlowInstanceGroup> updatedApprovalFlowGroup = gqlAuthContext.updateApprovalFlowStates(
            approvalFlowInstanceStateUpdates,
            Optional.empty()
        );

        if (updatedApprovalFlowGroup.isEmpty()) {
            throw new IllegalStateException("Approval flow isn't enabled in this environment");
        }

        return gqlAuthContext.getOrderDetail(updatedApprovalFlowGroup.get().getOrderId(), false, true);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ApprovalRoleJson upsertApprovalRole(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(APPROVAL_ROLE_KEY) ApprovalRoleJson approvalRoleJson
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        ApprovalRole approvalRole = gqlAuthContext.getApprovalFlowHierarchyMapper().jsonToApprovalRole(approvalRoleJson);
        ApprovalRole savedApprovalRole = gqlAuthContext.upsertApprovalRole(approvalRole);
        return gqlAuthContext.getApprovalFlowHierarchyMapper().approvalRoleToJson(savedApprovalRole);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ApprovalRoleJson deleteApprovalRole(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String approvalRoleId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        ApprovalRole approvalRole = gqlAuthContext.deleteApprovalRole(approvalRoleId);
        return gqlAuthContext.getApprovalFlowHierarchyMapper().approvalRoleToJson(approvalRole);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ApprovalSegmentJson upsertApprovalSegment(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(APPROVAL_SEGMENT_KEY) ApprovalSegmentJson approvalSegmentJson
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        ApprovalSegment approvalSegment = gqlAuthContext.getApprovalFlowHierarchyMapper().jsonToApprovalSegment(approvalSegmentJson);
        ApprovalSegment savedApprovalSegment = gqlAuthContext.upsertApprovalSegment(approvalSegment);
        return gqlAuthContext.getApprovalFlowHierarchyMapper().approvalSegmentToJson(savedApprovalSegment);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ApprovalSegmentJson deleteApprovalSegment(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String approvalSegmentId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        ApprovalSegment approvalSegment = gqlAuthContext.deleteApprovalSegment(approvalSegmentId);
        return gqlAuthContext.getApprovalFlowHierarchyMapper().approvalSegmentToJson(approvalSegment);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ApprovalFlowSubmitterNoteJson upsertApprovalFlowSubmitterNote(
        DataFetchingEnvironment env,
        @GraphQLName("approvalFlowSubmitterNote") @GraphQLNonNull ApprovalFlowSubmitterNoteJson approvalFlowSubmitterNoteJson
    ) {
        GqlAuthContext context = env.getContext();
        ApprovalFlowSubmitterNote note = context.getApprovalFlowMapper().toSubmitterNote(approvalFlowSubmitterNoteJson);
        return context.upsertApprovalFlowSubmitterNote(note);
    }

    @GraphQLField
    @GraphQLNonNull
    public static BulkInvoiceRun addBulkInvoiceRun(
        DataFetchingEnvironment env,
        @GraphQLName("bulkInvoiceRunInput") @GraphQLNonNull BulkInvoiceRunInput bulkInvoiceRunInput
    ) {
        GqlAuthContext context = env.getContext();
        return context.createBulkInvoiceRun(bulkInvoiceRunInput);
    }

    @GraphQLField
    @GraphQLNonNull
    public static BulkInvoiceRun postInvoicesForBulkInvoiceRun(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("bulkInvoiceRunId") String bulkInvoiceRunId,
        @GraphQLName("subscriptionIdExclusionList") List<String> subscriptionIdExclusionList
    ) {
        GqlAuthContext context = env.getContext();
        return context.postInvoicesForBulkInvoiceRun(bulkInvoiceRunId, subscriptionIdExclusionList);
    }

    @GraphQLField
    @GraphQLNonNull
    public static LedgerAccount upsertLedgerAccount(
        DataFetchingEnvironment env,
        @GraphQLName("ledgerAccount") @GraphQLNonNull LedgerAccount ledgerAccount
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertLedgerAccount(ledgerAccount);
    }

    @GraphQLField
    @GraphQLNonNull
    public static LedgerAccount deleteLedgerAccount(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String ledgerAccountId) {
        GqlAuthContext context = env.getContext();
        return context.deleteLedgerAccount(ledgerAccountId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountingPeriod initiateAccountingPeriodClose(
        DataFetchingEnvironment env,
        @GraphQLName("accountingPeriodId") @GraphQLNonNull String accountingPeriodId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        if (gqlAuthContext.getFeatureEnablement(PlatformFeature.ACCOUNTING).isEmpty()) {
            throw new IllegalStateException(
                String.format("Cannot initiate close of accounting period: %s Accounting is not enabled", accountingPeriodId)
            );
        }

        // since journal entry creation is an expensive process we check accounting period status first even here in GQL
        AccountingPeriod accountingPeriod = gqlAuthContext.getAccountingPeriod(accountingPeriodId);

        if (AccountingPeriodStatus.OPEN != accountingPeriod.getStatus()) {
            throw new IllegalStateException(
                String.format("Accounting period: %s not in required %s state", accountingPeriodId, AccountingPeriodStatus.OPEN)
            );
        }

        // since JE creation is expensive do a cursory check to see if accounting period can be put through
        // close workflow
        gqlAuthContext.performAccountingPeriodCloseWorkflowChecks(accountingPeriod);

        // generate journal entries
        gqlAuthContext.writeJournalEntriesForAccountingPeriod(accountingPeriodId);

        // mark accounting period close in progress
        gqlAuthContext.moveAccountingPeriodToCloseInProgress(accountingPeriodId);
        return gqlAuthContext.getAccountingPeriod(accountingPeriodId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountingPeriod abandonAccountingPeriodClose(
        DataFetchingEnvironment env,
        @GraphQLName("accountingPeriodId") @GraphQLNonNull String accountingPeriodId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.abandonAccountingPeriodClose(accountingPeriodId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountingPeriod updateAccountingPeriodStatus(
        DataFetchingEnvironment env,
        @GraphQLName("id") @GraphQLNonNull String accountingPeriodId,
        @GraphQLName("status") @GraphQLNonNull AccountingPeriodStatus status
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return switch (status) {
            case OPEN -> gqlAuthContext.reopenAccountingPeriod(accountingPeriodId);
            case CLOSED -> gqlAuthContext.closeAccountingPeriod(accountingPeriodId);
            default -> throw new IllegalArgumentException(
                String.format("Unsupported update state %s for accounting period id %s", status, accountingPeriodId)
            );
        };
    }

    @GraphQLField
    @GraphQLNonNull
    public static String syncJournalEntriesInAccountingPeriodToErp(
        DataFetchingEnvironment env,
        @GraphQLName("accountingPeriodId") @GraphQLNonNull String accountingPeriodId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.syncJournalEntriesToErp(accountingPeriodId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static String deleteJournalEntriesInAccountingPeriodFromErp(
        DataFetchingEnvironment env,
        @GraphQLName("accountingPeriodId") @GraphQLNonNull String accountingPeriodId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.deleteJournalEntriesFromErp(accountingPeriodId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static String syncInvoiceToErp(DataFetchingEnvironment env, @GraphQLName("invoiceNumber") @GraphQLNonNull String invoiceNumber) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.syncInvoiceToErp(invoiceNumber);
    }

    @GraphQLField
    @GraphQLNonNull
    public static String syncCreditMemoToErp(DataFetchingEnvironment env, @GraphQLName("creditMemoNumber") @GraphQLNonNull String creditMemoNumber) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.syncCreditMemoToErp(creditMemoNumber);
    }

    @GraphQLField
    @GraphQLNonNull
    public static String voidInvoiceOnErp(DataFetchingEnvironment env, @GraphQLName("invoiceNumber") @GraphQLNonNull String invoiceNumber) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.voidInvoiceOnErp(invoiceNumber);
    }

    @GraphQLField
    @GraphQLNonNull
    @GraphQLDeprecate("Use enableAccountingPlatformFeature")
    public static EnabledPlatformFeature enablePlatformFeature(
        DataFetchingEnvironment env,
        @GraphQLName("platformFeature") @GraphQLNonNull PlatformFeature platformFeature
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        if (platformFeature != PlatformFeature.ACCOUNTING) {
            throw new InvalidInputException("This deprecated API should only be used to enable ACCOUNTING platform feature.");
        }

        gqlAuthContext.enableAccountingPlatformFeature();
        return gqlAuthContext.getFeatureEnablement(PlatformFeature.ACCOUNTING).orElseThrow();
    }

    @GraphQLField
    @GraphQLNonNull
    public static EnabledPlatformFeature enableAccountingPlatformFeature(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.enableAccountingPlatformFeature();
        return gqlAuthContext.getFeatureEnablement(PlatformFeature.ACCOUNTING).orElseThrow();
    }

    @GraphQLField
    @GraphQLNonNull
    public static String generateJournalEntries(
        DataFetchingEnvironment env,
        @GraphQLName("accountingPeriodId") @GraphQLNonNull String accountingPeriodId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.writeJournalEntriesForAccountingPeriod(accountingPeriodId);
        return accountingPeriodId;
    }

    @GraphQLField
    @GraphQLNonNull
    public static RecognitionRule upsertRecognitionRule(
        DataFetchingEnvironment env,
        @GraphQLName("recognitionRule") @GraphQLNonNull RecognitionRule recognitionRule
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertRecognitionRule(recognitionRule);
    }

    @GraphQLField
    @GraphQLNonNull
    public static RecognitionRule deleteRecognitionRule(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String recognitionRuleId) {
        GqlAuthContext context = env.getContext();
        return context.deleteRecognitionRule(recognitionRuleId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean recognizeRevenue(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.recognizeRevenue();
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountingPeriod specifyCurrentAccountingPeriod(DataFetchingEnvironment env, @GraphQLName("startDate") @GraphQLNonNull Long start) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.specifyCurrentAccountingPeriod(Instant.ofEpochSecond(start));
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull SettlementApplicationDetail> applyCreditMemo(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(ApplyCreditMemoRequest.NAME) ApplyCreditMemoRequest applyCreditMemoRequest
    ) {
        GqlAuthContext context = env.getContext();
        var invoiceNumber = new Invoice.Number(applyCreditMemoRequest.getInvoiceNumber());

        context.applyCredit(
            invoiceNumber,
            applyCreditMemoRequest.getInvoiceAmount(),
            applyCreditMemoRequest.getCreditMemoNumber(),
            applyCreditMemoRequest.getAmount(),
            applyCreditMemoRequest.getNote()
        );

        var settlementApplications = context.getAppliedSettlementApplications(invoiceNumber);

        return context.getSettlementApplicationDetailMapper().toSettlementApplicationDetailsList(settlementApplications);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull SettlementApplicationDetail> unapplyCreditMemo(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(UnapplyCreditMemoRequest.NAME) UnapplyCreditMemoRequest unapplyCreditMemoRequest
    ) {
        GqlAuthContext context = env.getContext();
        var invoiceNumber = new Invoice.Number(unapplyCreditMemoRequest.getInvoiceNumber());

        context.unapplyCredit(
            invoiceNumber,
            unapplyCreditMemoRequest.getInvoiceBalanceAmount(),
            unapplyCreditMemoRequest.getCreditMemoNumber(),
            unapplyCreditMemoRequest.getCreditMemoBalanceAmount(),
            UUID.fromString(unapplyCreditMemoRequest.getSettlementToUnapplyId()),
            unapplyCreditMemoRequest.getNote()
        );

        var settlementApplications = context.getAppliedSettlementApplications(invoiceNumber);

        return context.getSettlementApplicationDetailMapper().toSettlementApplicationDetailsList(settlementApplications);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CreditMemoDetail postCreditMemo(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("creditMemoNumber") String creditMemoNumber
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        CreditMemo creditMemo = gqlAuthContext.postCreditMemo(creditMemoNumber);
        return gqlAuthContext.getCreditMemoDetail(creditMemo.getCreditMemoNumber());
    }

    @GraphQLField
    @GraphQLNonNull
    public static CreditMemoDetail createStandaloneCreditMemo(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("creditMemo") StandaloneCreditMemoRequest standaloneCreditMemoRequest
    ) {
        GqlAuthContext context = env.getContext();
        CreditMemo creditMemo = context.createStandaloneCreditMemo(standaloneCreditMemoRequest);
        return context.getCreditMemoDetail(creditMemo.getCreditMemoNumber());
    }

    @GraphQLField
    @GraphQLNonNull
    public static CreditMemoDetail updateDraftCreditMemo(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("creditMemoNumber") String creditMemoNumber,
        @GraphQLNonNull @GraphQLName("creditMemo") StandaloneCreditMemoRequest updateCreditMemoRequest
    ) {
        GqlAuthContext context = env.getContext();
        CreditMemo returned = context.updateDraftCreditMemo(updateCreditMemoRequest, creditMemoNumber);
        return context.getCreditMemoDetail(returned.getCreditMemoNumber());
    }

    @GraphQLField
    @GraphQLNonNull
    public static CreditMemoDetail convertNegativeDraftInvoice(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Invoice invoice = gqlAuthContext.getInvoice(new Invoice.Number(invoiceNumber));
        CreditMemo creditMemo = gqlAuthContext.convertNegativeInvoiceToCreditMemo(invoice);
        return gqlAuthContext.getCreditMemoDetail(creditMemo.getCreditMemoNumber());
    }

    @GraphQLField
    @GraphQLNonNull
    public static CreditMemoDetail deleteCreditMemo(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("creditMemoNumber") String creditMemoNumber
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var detail = gqlAuthContext.getCreditMemoDetail(creditMemoNumber);
        detail.setIsDeleted(true);
        gqlAuthContext.deleteCreditMemo(creditMemoNumber);
        return detail;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean addSamlIntegration(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("authSamlIntegration") AuthSamlIntegrationRequestJson authSamlIntegrationRequestJson
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        String tenantId = gqlAuthContext.provideTenantIdString();
        var authSamlIntegrationJson = AuthSamlIntegrationMapper.requestToAuthSamlIntegration(authSamlIntegrationRequestJson);
        gqlAuthContext.addSamlIntegration(
            tenantId,
            authSamlIntegrationJson.getMetadataUrl(),
            authSamlIntegrationJson.getProviderName(),
            authSamlIntegrationJson.getAttributeMapping()
        );
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static SubscriptionChargeAlias addSubscriptionChargeAlias(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("aliasId") String aliasId,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("chargeId") String chargeId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.addSubscriptionChargeAlias(aliasId, subscriptionId, chargeId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static SubscriptionChargeAlias deleteSubscriptionChargeAlias(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("aliasId") String aliasId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.deleteSubscriptionChargeAlias(aliasId);
    }

    @GraphQLField
    @GraphQLNonNull
    @GraphQLDeprecate("Use updatePaymentConfiguration instead")
    public static PaymentConfiguration updateAutomaticPaymentConfiguration(
        DataFetchingEnvironment env,
        @GraphQLName("paymentTypes") @GraphQLNonNull List<@GraphQLNonNull PaymentType> supportedPaymentTypes
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        PaymentConfiguration paymentConfiguration = new PaymentConfiguration(new HashSet<>(supportedPaymentTypes));
        return gqlAuthContext.updateAutomaticPaymentConfiguration(paymentConfiguration);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentConfiguration updatePaymentConfiguration(
        DataFetchingEnvironment env,
        @GraphQLName("paymentTypes") @GraphQLNonNull List<@GraphQLNonNull PaymentType> supportedPaymentTypes
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        PaymentConfiguration paymentConfiguration = new PaymentConfiguration(new HashSet<>(supportedPaymentTypes));
        return gqlAuthContext.updatePaymentConfiguration(paymentConfiguration);
    }

    @GraphQLField
    @GraphQLNonNull
    public static RefundDetail createRefund(
        DataFetchingEnvironment env,
        @GraphQLName("refundRequest") @GraphQLNonNull RefundRequestJson requestJson
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var refundToCreate = gqlAuthContext.getRefundDetailMapper().toRefund(requestJson);
        var appliedRefund = gqlAuthContext.createAndApplyRefund(refundToCreate);
        return gqlAuthContext.getRefundDetail(appliedRefund);
    }

    @GraphQLField
    @GraphQLNonNull
    public static String initiateHubSpotIntegration(DataFetchingEnvironment env, @GraphQLName("email") String email) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.throwIfTenantAlreadyHasACrmIntegration();
        return gqlAuthContext.initiateHubSpotIntegration(email);
    }

    @GraphQLField
    public static HubSpotIntegration completeHubSpotIntegration(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("code") String authorizationCode,
        @GraphQLNonNull @GraphQLName("state") String integrationId,
        @GraphQLNonNull @GraphQLName("redirect_uri") String redirectUri
    ) throws IOException {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.completeHubSpotIntegration(authorizationCode, integrationId, redirectUri);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean deleteHubSpotIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.deleteHubSpotIntegration();
        return true;
    }

    /**
     * Mutation that is always successful.  Used for E2E testing.
     */
    @GraphQLField
    @GraphQLNonNull
    public static String mutateSuccessfully(DataFetchingEnvironment env, @GraphQLName("result") @GraphQLNonNull String result) {
        return result;
    }

    /**
     * Mutation that always fails.  Used for E2E testing.
     */
    @GraphQLField
    @GraphQLNonNull
    public static String mutateWithError(DataFetchingEnvironment env, @GraphQLName("message") @GraphQLNonNull String message) throws Exception {
        throw new Exception(message);
    }

    @GraphQLField
    public static AccountJson getOrCreateAccountByCrmId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountCrmId") String accountCrmId,
        @GraphQLName("opportunityCrmId") String opportunityCrmId
    ) {
        if (StringUtils.isBlank(accountCrmId)) {
            throw new IllegalArgumentException("account crm id is required.");
        }
        GqlAuthContext context = env.getContext();
        Account account = context.getOrCreateAccountByCrmId(accountCrmId, Optional.ofNullable(opportunityCrmId));
        return context.getAccountMapper().accountToJson(account);
    }

    @GraphQLField
    public static AccountJson importAccountByCrmId(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("accountCrmId") String accountCrmId) {
        if (StringUtils.isBlank(accountCrmId)) {
            throw new IllegalArgumentException("account crm id is required.");
        }
        GqlAuthContext context = env.getContext();
        Account account = context.importAccountByCrmId(accountCrmId);
        return context.getAccountMapper().accountToJson(account);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean syncDealInformationToHubSpot(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Order order = gqlAuthContext.getOrderByOrderId(orderId);
        gqlAuthContext.syncOrderToHubSpot(order);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull HubSpotSetupMessage> setupHubSpotCustomObjects(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("privateKey") String privateKey
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.setupHubSpotCustomObjects(privateKey);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull HubSpotSetupMessage> setupHubSpotCustomProperties(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.setupHubSpotCustomProperties();
    }

    @GraphQLField
    @GraphQLNonNull
    public static IntegrationDetail addIntegration(
        DataFetchingEnvironment env,
        @GraphQLName("integrationRequest") @GraphQLNonNull IntegrationRequest integrationRequest
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.addIntegration(integrationRequest);
    }

    @GraphQLField
    @GraphQLNonNull
    public static Integration completeIntegration(
        DataFetchingEnvironment env,
        @GraphQLName("authCode") @GraphQLNonNull String authCode,
        @GraphQLName("state") @GraphQLNonNull String integrationId,
        @GraphQLName("realmId") String realmId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.receiveAuthCallback(authCode, UUID.fromString(integrationId), realmId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static Integration deleteIntegration(DataFetchingEnvironment env, @GraphQLName("integrationId") @GraphQLNonNull String integrationId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.deleteIntegration(UUID.fromString(integrationId));
    }

    @GraphQLField
    @GraphQLNonNull
    public static ApprovalWithCodeResponse approvalFlowStatusChangeWithCode(
        DataFetchingEnvironment env,
        @GraphQLName("code") @GraphQLNonNull String code,
        @GraphQLName("state") @GraphQLNonNull boolean approve
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.updateApprovalFlowStateWithCode(code, approve);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CustomFieldDefinitionJson createCustomFieldDefinition(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("customFieldDefinition") CustomFieldDefinitionCreateInput customFieldDefinitionCreateInput
    ) {
        GqlAuthContext context = env.getContext();
        CustomFieldDefinition customFieldDefinition = context
            .getCustomFieldDefinitionJsonMapper()
            .toCustomFieldDefinition(customFieldDefinitionCreateInput);
        CustomFieldDefinition createdDefinition = context.createCustomFieldDefinition(customFieldDefinition);
        return context.getCustomFieldDefinitionJsonMapper().toCustomFieldDefinitionJson(createdDefinition);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CustomFieldDefinitionJson updateCustomFieldDefinition(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String id,
        @GraphQLNonNull @GraphQLName("customFieldDefinitionUpdate") CustomFieldDefinitionUpdateInput customFieldDefinitionUpdateInput
    ) {
        GqlAuthContext context = env.getContext();
        var customFieldDefinitionUpdate = new CustomFieldDefinitionUpdate(
            id,
            customFieldDefinitionUpdateInput.getFieldName(),
            customFieldDefinitionUpdateInput.getFieldLabel(),
            customFieldDefinitionUpdateInput.getFieldType(),
            customFieldDefinitionUpdateInput.getOptions(),
            customFieldDefinitionUpdateInput.getDefaultValue()
        );
        CustomFieldDefinition updatedDefinition = context.updateCustomFieldDefinition(customFieldDefinitionUpdate);
        return context.getCustomFieldDefinitionJsonMapper().toCustomFieldDefinitionJson(updatedDefinition);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CustomFieldDefinitionJson deleteCustomFieldDefinition(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String id) {
        GqlAuthContext context = env.getContext();
        CustomFieldDefinition deletedCustomFieldDefinition = context.deleteCustomFieldDefinition(id);
        return context.getCustomFieldDefinitionJsonMapper().toCustomFieldDefinitionJson(deletedCustomFieldDefinition);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull CustomFieldEntry> updateCustomFields(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("parentObjectType") CustomFieldParentType parentType,
        @GraphQLNonNull @GraphQLName("parentObjectId") String parentObjectId,
        @GraphQLNonNull @GraphQLName("customFields") List<@GraphQLNonNull CustomFieldInput> customFields
    ) {
        GqlAuthContext context = env.getContext();
        return context.updateCustomFields(parentType, parentObjectId, customFields);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean createOrderDocument(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext context = env.getContext();
        context.createOrderDocument(orderId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentTermSettingsJson updatePaymentTermSettings(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("paymentTermSettings") PaymentTermSettingsJson paymentTermSettings
    ) {
        GqlAuthContext context = env.getContext();
        return context.updatePaymentTermSettings(paymentTermSettings);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean addTenantSignatory(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("tenantSignatory") EsignTenantSignatory signatory
    ) {
        GqlAuthContext context = env.getContext();
        context.insertTenantSignatory(signatory);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean deleteTenantSignatory(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("userId") String userId) {
        GqlAuthContext context = env.getContext();
        context.deleteTenantSignatory(userId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static CompositeOrderDetail createUpsellWithEarlyRenewal(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLName("crmOpportunityId") String crmOpportunityId,
        @GraphQLName("opportunityInput") OpportunityInput opportunityInput,
        @GraphQLName("amendAndRenewRequest") AmendAndRenewCreateRequest amendAndRenewCreateRequest
    ) {
        GqlAuthContext context = env.getContext();
        return context.createUpsellWithEarlyRenewal(
            subscriptionId,
            crmOpportunityId,
            opportunityInput,
            Optional.ofNullable(amendAndRenewCreateRequest)
        );
    }

    @GraphQLField
    @GraphQLNonNull
    public static CompositeOrderDetail updateCompositeOrderFields(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("compositeOrderId") String compositeOrderId,
        @GraphQLNonNull @GraphQLName("compositeOrderFieldsUpdateRequest") ImmutableCompositeOrderFieldsUpdateRequest compositeOrderFieldsUpdateRequest
    ) {
        GqlAuthContext context = env.getContext();
        return context.updateCompositeOrderFields(compositeOrderId, compositeOrderFieldsUpdateRequest);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CompositeOrderDetail updateCompositeOrderStatus(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String compositeOrderId,
        @GraphQLNonNull @GraphQLName("status") OrderStatus status,
        @GraphQLName("statusUpdatedOn") Long statusUpdatedOn,
        @GraphQLName("adminApprovalFlowByPass") Boolean adminApprovalByPass
    ) {
        boolean adminApprovalFlowByPass = BooleanUtils.isTrue(adminApprovalByPass);
        GqlAuthContext context = env.getContext();
        context.updateCompositeOrderStatus(compositeOrderId, status, statusUpdatedOn, adminApprovalFlowByPass);
        return context.getCompositeOrderDetail(compositeOrderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean createCompositeOrderDocument(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String compositeOrderId) {
        GqlAuthContext context = env.getContext();
        context.createCompositeOrderPdf(compositeOrderId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static CompositeOrderDetail deleteUpsellWithEarlyRenewal(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String compositeOrderId
    ) {
        GqlAuthContext context = env.getContext();
        return context.deleteUpsellWithEarlyRenewal(compositeOrderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean sendEmailForEsign(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("esignRequest") EsignRequest esignRequest) {
        GqlAuthContext context = env.getContext();
        context.sendEmailForEsign(esignRequest);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean sendEmailForDocusign(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("esignRequest") EsignRequest esignRequest) {
        GqlAuthContext context = env.getContext();
        context.sendEmailForDocusign(esignRequest);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static String voidEsignatureDocumentV2(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String id) {
        GqlAuthContext context = env.getContext();
        context.voidEsignDocumentV2(id);
        return id;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean resendEsignatureDocumentV2(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String id) {
        GqlAuthContext context = env.getContext();
        context.resendEsignatureDocumentV2(id);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static String generateDocumentLink(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getOrCreateDocumentLink(orderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean updateAccountSupportedPaymentTypes(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId,
        @GraphQLNonNull @GraphQLName("supportedPaymentTypes") List<PaymentType> supportedPaymentTypes
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.updateAccountSupportedPaymentTypes(accountId, new HashSet<>(supportedPaymentTypes));
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static StripeIntentResponse createPaymentIntent(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("paymentLinkId") String paymentLinkId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.createPaymentIntentWithPaymentLink(paymentLinkId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static StripeSetupIntentResponse createSetupIntent(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("paymentLinkId") String paymentLinkId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.createSetupIntentWithPaymentLink(paymentLinkId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean updatePaymentIntent(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("paymentLinkId") String paymentLinkId,
        @GraphQLNonNull @GraphQLName("paymentIntentId") String stripePaymentIntentId,
        @GraphQLNonNull @GraphQLName("optInForAutomaticPayments") boolean optInForAutomaticPayments
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.updatePaymentIntentWithPaymentLink(paymentLinkId, stripePaymentIntentId, optInForAutomaticPayments);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static BillingEvents addBillingEvent(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("billingEventInput") BillingEventInput billingEventInput
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.addBillingEvent(billingEventInput);
    }

    @GraphQLField
    @GraphQLNonNull
    public static BillingEvents deleteBillingEvent(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String id) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.deleteBillingEvent(id);
    }

    @GraphQLField
    @GraphQLNonNull
    public static EscalationPolicy updateEscalationPolicy(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("escalationPolicy") ImmutableUpdateEscalationPolicyRequest updateEscalationPolicyRequest
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.updateEscalationPolicy(updateEscalationPolicyRequest);
    }

    @GraphQLField
    @GraphQLNonNull
    public static EscalationPolicy addEscalationPolicy(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("escalationPolicy") ImmutableAddEscalationPolicyRequest addEscalationPolicyRequest
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.addEscalationPolicy(addEscalationPolicyRequest);
    }

    @GraphQLField
    @GraphQLNonNull
    public static String generateDocumentLinkIdForCLM(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getOrCreateDocumentLinkIdForCLM(orderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CLMThreadCreationDetail createCLMThread(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("clmThread") CLMCreateThreadRequest clmThread
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.createCLMThread(clmThread);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CLMChatMessageDetail addMessageToCLMThread(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("clmThreadMessageRequest") CLMChatMessageDetail clmAddMessageRequest
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.addMessageToThread(clmAddMessageRequest);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean resolveThread(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("threadId") String threadId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.resolveThread(threadId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static PriceAttribute upsertPriceAttribute(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(PRICE_ATTRIBUTE_KEY) ImmutablePriceAttribute priceAttribute
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertPriceAttribute(priceAttribute);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ChargeDefaultAttributeReferences upsertChargeDefaultAttributeReferences(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(CHARGE_DEFAULT_PRICE_ATTRIBUTE_KEY) ChargeDefaultAttributeReferences chargeDefaultAttributeReferences
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertChargeDefaultAttributeReferences(chargeDefaultAttributeReferences);
    }

    @GraphQLField
    public static Integer updateOrderExpiryDurationInDays(
        DataFetchingEnvironment env,
        @GraphQLName("orderExpiryDurationInDays") String orderExpiryDurationInDays
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.updateOrderExpiryDurationInDays(orderExpiryDurationInDays);
        return gqlAuthContext.getOrderExpiryDurationInDays();
    }

    @GraphQLField
    public static SigningOrder updateSigningOrder(DataFetchingEnvironment env, @GraphQLName("signingOrder") SigningOrder signingOrder) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.updateSigningOrder(signingOrder);
        return gqlAuthContext.getSigningOrder();
    }

    @GraphQLField
    @GraphQLNonNull
    public static UsageBatchInsertResult addRawUsages(
        DataFetchingEnvironment env,
        @GraphQLName("rawUsagesData") @GraphQLNonNull RawUsagesData rawUsagesData
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.addRawUsages(rawUsagesData.getData());
    }

    @GraphQLField
    @GraphQLNonNull
    public static PriceAttribute deletePriceAttribute(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String priceAttributeId) {
        GqlAuthContext context = env.getContext();
        return context.deletePriceAttribute(priceAttributeId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static TenantJob retryTenantJobAsAdmin(
        DataFetchingEnvironment env,
        @GraphQLName("tenantId") String tenantId,
        @GraphQLName("id") String id
    ) {
        GqlAuthContext context = env.getContext();
        return context.retryTenantJobAsAdmin(tenantId, id);
    }

    @GraphQLField
    @GraphQLNonNull
    public static TaxJarIntegration upsertTaxJarIntegration(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("apiKey") String apiKey,
        @GraphQLName("isSandbox") Boolean isSandbox
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        TaxJarIntegrationInput input = ImmutableTaxJarIntegrationInput.builder().apiKey(apiKey).isSandbox(isSandbox).build();
        String integrationId = gqlAuthContext.integrateTaxJar(input);
        return gqlAuthContext.getTaxJarIntegrationById(integrationId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean testTaxJarIntegration(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("apiKey") String apiKey,
        @GraphQLName("isSandbox") Boolean isSandbox
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        TaxJarIntegrationInput input = ImmutableTaxJarIntegrationInput.builder().apiKey(apiKey).isSandbox(isSandbox).build();
        gqlAuthContext.testTaxJarIntegration(input);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static DataImport processImport(DataFetchingEnvironment env, @GraphQLName("dataImportId") String dataImportId) {
        GqlAuthContext context = env.getContext();
        return context.processImport(dataImportId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static FlatfileSpaceResponse createFlatfileWorkbook(DataFetchingEnvironment env, @GraphQLName("domain") ImportDomain domain) {
        GqlAuthContext context = env.getContext();
        return context.createFlatfileWorkbook(domain);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean renewApiKeyForFlatfileSpace(DataFetchingEnvironment env, @GraphQLName("spaceId") String spaceId) throws IOException {
        GqlAuthContext context = env.getContext();
        context.renewApiKeyForFlatfileSpace(spaceId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean deleteFlatfileSpace(DataFetchingEnvironment env, @GraphQLName("spaceId") String spaceId) throws IOException {
        GqlAuthContext context = env.getContext();
        context.deleteFlatfileSpace(spaceId);
        return true;
    }

    @GraphQLField
    public static NotificationInstanceWithAttempts resendNotification(
        DataFetchingEnvironment env,
        @GraphQLName("notificationInstanceId") String notificationInstanceId
    ) {
        GqlAuthContext context = env.getContext();
        context.resendNotificationInstance(notificationInstanceId);
        return context.getNotificationInstance(notificationInstanceId);
    }

    @GraphQLField
    public static String createMergeLinkToken(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("integrationType") IntegrationTargetService integrationTargetService
    ) {
        GqlAuthContext context = env.getContext();
        return context.createMergeLinkToken(integrationTargetService);
    }

    @GraphQLField
    public static boolean processMergePublicToken(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("publicToken") String publicToken,
        @GraphQLNonNull @GraphQLName("integrationType") IntegrationTargetService integrationTargetService
    ) {
        GqlAuthContext context = env.getContext();
        context.processMergePublicToken(publicToken, integrationTargetService);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static SalesRoomLink createSalesRoomLink(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.createSalesRoomLink(orderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean disableSalesRoomLink(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("linkId") String linkId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.disableSalesRoomLink(linkId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountContactJson upsertAccountContactForSalesRoom(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("linkId") String linkId,
        @GraphQLName("skipAddressValidation") boolean skipAddressValidation,
        @GraphQLName("strictValidation") boolean strictValidation,
        @GraphQLName("updateReseller") boolean updateReseller,
        @GraphQLNonNull @GraphQLName(ACCOUNT_CONTACT_KEY) AccountContactJson accountContactJson
    ) {
        GqlAuthContext context = env.getContext();
        SalesRoomLink salesRoom = context.getSalesRoomLink(linkId);
        String resellerAccountId = null;
        if (updateReseller) {
            resellerAccountId = context
                .getResellerAccountIdForSalesRoom(linkId)
                .orElseThrow(() -> new InvalidInputException("No reseller account found for sales room."));
        }
        String accountId = resellerAccountId != null ? resellerAccountId : salesRoom.getAccountId();

        AccountContactJson response = TenantContextInjector.callInTenantContext(salesRoom.getTenantId(), context.getTenantIdProvider(), () ->
            upsertAccountContact(env, accountId, skipAddressValidation, strictValidation, accountContactJson)
        );

        TenantContextInjector.runInTenantContext(salesRoom.getTenantId(), context.getTenantIdProvider(), () ->
            context.setShouldRegeneratePdfForOrder(salesRoom.getOrderId())
        );

        return response;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean updateOrderContacts(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("linkId") String linkId,
        @GraphQLName("billingContactId") String billingContactId,
        @GraphQLName("shippingContactId") String shippingContactId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.updateOrderContactsForSalesRoom(linkId, billingContactId, shippingContactId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static CancelAndRestructureOrderDetail generateCancelAndRestructure(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        LOGGER.info("Generating cancel and restructure for subscription {}", subscriptionId);
        return gqlAuthContext.generateCancelAndRestructure(subscriptionId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CancelAndRestructureOrderDetail upsertCancelAndRestructure(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(ORDER_KEY) CancelAndRestructureRequest ignored,
        @GraphQLName(IS_DRY_RUN) Boolean isDryRun
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        CancelAndRestructureRequest cancelAndRestructureRequest = getObjectFromMap(
            env.getArguments().get(ORDER_KEY),
            CancelAndRestructureRequest.class
        );
        isDryRun = BooleanUtils.isTrue(isDryRun);

        return gqlAuthContext.upsertCancelAndRestructure(cancelAndRestructureRequest, isDryRun);
    }

    @GraphQLField
    @GraphQLNonNull
    public static EsignRequest upsertEsignDetails(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("esignRequest") EsignRequest esignRequest
    ) {
        GqlAuthContext context = env.getContext();
        return context.upsertEsignDetails(esignRequest);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean sendEmailForEsignSalesRoom(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("linkId") String linkId,
        @GraphQLName("accountSignatory") EmailContact accountSignatory
    ) {
        GqlAuthContext context = env.getContext();
        SalesRoomLink salesRoomLink = context.getSalesRoomLink(linkId);
        TenantContextInjector.runInTenantContext(salesRoomLink.getTenantId(), context.getTenantIdProvider(), () ->
            context.sendEmailForEsignSalesRoom(salesRoomLink, accountSignatory)
        );

        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull CustomFieldEntry> updateCustomFieldsForSalesRoom(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("linkId") String linkId,
        @GraphQLNonNull @GraphQLName("customFields") List<@GraphQLNonNull CustomFieldInput> customFields
    ) {
        GqlAuthContext context = env.getContext();
        SalesRoomLink salesRoomLink = context.getSalesRoomLink(linkId);
        return TenantContextInjector.callInTenantContext(salesRoomLink.getTenantId(), context.getTenantIdProvider(), () ->
            updateCustomFields(env, CustomFieldParentType.SALES_ROOM, linkId, customFields)
        );
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean createOrderDocumentForSalesRoom(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("linkId") String linkId) {
        GqlAuthContext context = env.getContext();
        SalesRoomLink salesRoomLink = context.getSalesRoomLink(linkId);
        return TenantContextInjector.callInTenantContext(salesRoomLink.getTenantId(), context.getTenantIdProvider(), () ->
            createOrderDocument(env, salesRoomLink.getOrderId())
        );
    }

    @GraphQLField
    @GraphQLNonNull
    public static CancelAndRestructureOrderDetail updateCancelAndRestructureOrderStatus(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String compositeOrderId,
        @GraphQLNonNull @GraphQLName("status") OrderStatus status,
        @GraphQLName("statusUpdatedOn") Long statusUpdatedOn,
        @GraphQLName("adminApprovalFlowByPass") Boolean adminApprovalByPass
    ) {
        GqlAuthContext context = env.getContext();
        boolean adminApprovalFlowByPass = BooleanUtils.isTrue(adminApprovalByPass);
        context.updateCompositeOrderStatus(compositeOrderId, status, statusUpdatedOn, adminApprovalFlowByPass);
        return context.getCancelAndRestructureOrderDetail(compositeOrderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CancelAndRestructureOrderDetail deleteCancelAndRestructure(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String compositeOrderId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        String message = String.format("delete cancel and restructure order for composite order id: %s", compositeOrderId);
        LOGGER.info(message);
        return gqlAuthContext.deleteCancelAndRestructure(compositeOrderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean updateWireInstruction(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("wireInstruction") String wireInstruction) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.updateWireInstruction(wireInstruction);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static CompositeOrderDetail updatePrimaryCompositeOrderIdForCrmOpportunity(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("compositeOrderId") String compositeOrderId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.updatePrimaryCompositeOrderIdForCrmOpportunity(compositeOrderId);
        return gqlAuthContext.getCompositeOrderDetail(compositeOrderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CompositeOrderDetail syncCompositeOrderToCrm(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("compositeOrderId") String compositeOrderId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.syncCompositeOrderToCrm(compositeOrderId);
        return gqlAuthContext.getCompositeOrderDetail(compositeOrderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OpportunityJson resetOpportunityClosedState(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("opportunityId") String opportunityId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Opportunity opportunity = gqlAuthContext.resetOpportunityClosedState(opportunityId);
        return gqlAuthContext.getOpportunityMapper().opportunityToJson(opportunity);
    }

    @GraphQLField
    @GraphQLNonNull
    public static Integration upsertAnrokIntegration(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("apiKey") String apiKey) {
        GqlAuthContext gqlAuthContext = env.getContext();
        AnrokIntegrationInput input = ImmutableAnrokIntegrationInput.builder().apiKey(apiKey).build();
        String integrationId = gqlAuthContext.integrateAnrok(input);
        return gqlAuthContext.getAnrokIntegrationById(integrationId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean testAnrokIntegration(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("apiKey") String apiKey) {
        GqlAuthContext gqlAuthContext = env.getContext();
        AnrokIntegrationInput input = ImmutableAnrokIntegrationInput.builder().apiKey(apiKey).build();
        gqlAuthContext.testAnrokIntegration(input);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static CurrencyTypeSetting upsertCurrencyTypeSetting(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(CURRENCY_TYPE_SETTING) ImmutableCurrencyTypeSetting currencyTypeSetting
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertCurrencyTypeSetting(currencyTypeSetting);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CurrencyConversionRate upsertCurrencyConversionRate(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName(CURRENCY_CONVERSION_RATE) ImmutableCurrencyConversionRate currencyConversionRate
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.upsertCurrencyConversionRate(currencyConversionRate);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean sendBulkInvoiceEmail(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("invoiceNumbers") List<@GraphQLNonNull String> invoiceNumbers
    ) {
        GqlAuthContext context = env.getContext();
        context.sendBulkInvoiceEmail(invoiceNumbers);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static EmailSetting addEmailSetting(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("emailSetting") ImmutableEmailSetting emailSetting
    ) {
        GqlAuthContext context = env.getContext();
        return context.addEmailSetting(emailSetting);
    }

    @GraphQLField
    @GraphQLNonNull
    public static EmailSetting updateEmailSetting(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("emailSetting") ImmutableEmailSetting emailSetting
    ) {
        GqlAuthContext context = env.getContext();
        return context.updateEmailSetting(emailSetting);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean deleteEmailSetting(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("emailSetting") ImmutableEmailSetting emailSetting
    ) {
        GqlAuthContext context = env.getContext();
        context.deleteEmailSetting(emailSetting);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static Boolean updateOrderAttributes(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("orderId") String orderId,
        @GraphQLNonNull @GraphQLName("orderAttributes") OrderAttributesUpdateRequest updateRequest
    ) {
        GqlAuthContext context = env.getContext();
        context.updateOrderAttributes(orderId, updateRequest);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean syncAccountToCrm(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("accountId") String accountId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.syncAccountToCrm(accountId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean syncSubscriptionToCrm(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        gqlAuthContext.syncSubscriptionToCrm(subscriptionId);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean updatePredefinedTermsToLatestForOrder(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("templateId") String templateId,
        @GraphQLNonNull @GraphQLName("orderId") String orderId
    ) {
        GqlAuthContext context = env.getContext();
        context.updatePredefinedTermsToLatestForOrder(templateId, orderId);
        return true;
    }
}
