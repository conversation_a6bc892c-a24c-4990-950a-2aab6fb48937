package com.subskribe.billy.graphql.subscription;

import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.graphql.productcatalog.PlanDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountLineItemDetail;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.resources.json.order.PricingOverrideJson;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.shared.DiscountJson;
import com.subskribe.billy.shared.enums.ChargeType;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@GraphQLName("SubscriptionCharge")
public class SubscriptionChargeDetail {

    @GraphQLField
    @GraphQLName("id")
    @GraphQLNonNull
    private String id;

    @GraphQLField
    @GraphQLName("groupId")
    @GraphQLNonNull
    private String groupId;

    @GraphQLField
    @GraphQLName("charge")
    @GraphQLNonNull
    private ChargeJson charge;

    @GraphQLField
    @GraphQLName("chargeDetail")
    @GraphQLNonNull
    private ChargeDetail chargeDetail;

    @GraphQLField
    @GraphQLName("plan")
    @GraphQLNonNull
    private PlanDetail plan;

    @GraphQLField
    @GraphQLName("quantity")
    @GraphQLNonNull
    private long quantity;

    @GraphQLField
    @GraphQLName("isRamp")
    @GraphQLNonNull
    private boolean isRamp;

    @GraphQLField
    @GraphQLName("currencyConversionRateId")
    private String currencyConversionRateId;

    @GraphQLField
    @GraphQLName("discounts")
    private List<DiscountJson> discounts;

    @GraphQLField
    @GraphQLName("predefinedDiscounts")
    private List<PredefinedDiscountLineItemDetail> predefinedDiscounts;

    @GraphQLField
    @GraphQLName("attributeReferences")
    private List<AttributeReference> attributeReferences;

    @GraphQLField
    @GraphQLName("pricingOverride")
    private PricingOverrideJson pricingOverride;

    @GraphQLField
    @GraphQLName("listUnitPrice")
    private BigDecimal listUnitPrice;

    @GraphQLField
    @GraphQLName("sellUnitPrice")
    private BigDecimal sellUnitPrice;

    @GraphQLField
    @GraphQLName("discountAmount")
    private BigDecimal discountAmount;

    @GraphQLField
    @GraphQLName("startDate")
    @GraphQLNonNull
    private Long startDate;

    @GraphQLField
    @GraphQLName("endDate")
    private Long endDate;

    @GraphQLField
    @GraphQLName("orderLines")
    @GraphQLNonNull
    private List<UUID> orderLines;

    private String formattedStartDate;
    private String formattedEndDate;
    private String formattedDiscountPercent;
    private String formattedListUnitPrice;
    private String formattedSellUnitPrice;

    public SubscriptionChargeDetail() {}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public ChargeJson getCharge() {
        return charge;
    }

    public void setCharge(ChargeJson charge) {
        this.charge = charge;
    }

    public ChargeDetail getChargeDetail() {
        return chargeDetail;
    }

    public void setChargeDetail(ChargeDetail chargeDetail) {
        this.chargeDetail = chargeDetail;
    }

    public PlanDetail getPlan() {
        return plan;
    }

    public void setPlan(PlanDetail plan) {
        this.plan = plan;
    }

    public long getQuantity() {
        return quantity;
    }

    public void setQuantity(long quantity) {
        this.quantity = quantity;
    }

    public boolean getIsRamp() {
        return isRamp;
    }

    public void setIsRamp(boolean ramp) {
        isRamp = ramp;
    }

    public String getCurrencyConversionRateId() {
        return currencyConversionRateId;
    }

    public void setCurrencyConversionRateId(String currencyConversionRateId) {
        this.currencyConversionRateId = currencyConversionRateId;
    }

    public List<DiscountJson> getDiscounts() {
        return discounts;
    }

    public void setDiscounts(List<DiscountJson> discounts) {
        this.discounts = discounts;
    }

    public BigDecimal getListUnitPrice() {
        return listUnitPrice;
    }

    public void setListUnitPrice(BigDecimal listUnitPrice) {
        this.listUnitPrice = listUnitPrice;
    }

    public BigDecimal getSellUnitPrice() {
        return sellUnitPrice;
    }

    public void setSellUnitPrice(BigDecimal sellUnitPrice) {
        this.sellUnitPrice = sellUnitPrice;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public List<UUID> getOrderLines() {
        return orderLines;
    }

    public void setOrderLines(List<UUID> orderLines) {
        this.orderLines = orderLines;
    }

    public List<PredefinedDiscountLineItemDetail> getPredefinedDiscounts() {
        return predefinedDiscounts;
    }

    public void setPredefinedDiscounts(List<PredefinedDiscountLineItemDetail> predefinedDiscounts) {
        this.predefinedDiscounts = predefinedDiscounts;
    }

    public List<AttributeReference> getAttributeReferences() {
        return attributeReferences;
    }

    public void setAttributeReferences(List<AttributeReference> attributeReferences) {
        this.attributeReferences = attributeReferences;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public boolean getIsRecurring() {
        return charge.getType() == ChargeType.RECURRING;
    }

    public String getFormattedStartDate() {
        return formattedStartDate;
    }

    public void setFormattedStartDate(String formattedStartDate) {
        this.formattedStartDate = formattedStartDate;
    }

    public String getFormattedEndDate() {
        return formattedEndDate;
    }

    public void setFormattedEndDate(String formattedEndDate) {
        this.formattedEndDate = formattedEndDate;
    }

    public String getFormattedDiscountPercent() {
        return formattedDiscountPercent;
    }

    public void setFormattedDiscountPercent(String formattedDiscountPercent) {
        this.formattedDiscountPercent = formattedDiscountPercent;
    }

    public String getFormattedListUnitPrice() {
        return formattedListUnitPrice;
    }

    public void setFormattedListUnitPrice(String formattedListUnitPrice) {
        this.formattedListUnitPrice = formattedListUnitPrice;
    }

    public String getFormattedSellUnitPrice() {
        return formattedSellUnitPrice;
    }

    public void setFormattedSellUnitPrice(String formattedSellUnitPrice) {
        this.formattedSellUnitPrice = formattedSellUnitPrice;
    }

    public PricingOverrideJson getPricingOverride() {
        return pricingOverride;
    }

    public void setPricingOverride(PricingOverrideJson pricingOverride) {
        this.pricingOverride = pricingOverride;
    }
}
