package com.subskribe.billy.graphql.order;

import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountLineItemDetail;
import com.subskribe.billy.metrics.model.LineItemMetrics;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.resources.json.order.PricingOverrideJson;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.shared.DiscountDetailJson;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.shared.enums.ActionType;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@GraphQLName("OrderLineItemDetail")
public class OrderLineItemDetail {

    @GraphQLField
    @GraphQLName("id")
    private String id;

    @GraphQLField
    @GraphQLName("itemGroupId")
    private String itemGroupId;

    @GraphQLField
    @GraphQLName("isDryRunItem")
    private boolean isDryRunItem;

    @GraphQLField
    private int rank;

    @GraphQLField
    @GraphQLName("action")
    @GraphQLNonNull
    private ActionType action;

    @GraphQLField
    @GraphQLName("plan")
    private PlanJson plan;

    @GraphQLField
    @GraphQLName("subscriptionChargeId")
    private String subscriptionChargeId;

    @GraphQLField
    @GraphQLName("currencyConversionRateId")
    private String currencyConversionRateId;

    @GraphQLField
    @GraphQLName("subscriptionChargeGroupId")
    private String subscriptionChargeGroupId;

    @GraphQLField
    @GraphQLName("chargeDetail")
    @GraphQLNonNull
    private ChargeDetail chargeDetail;

    @GraphQLField
    @GraphQLName("charge")
    @GraphQLNonNull
    private ChargeJson charge;

    @GraphQLField
    @GraphQLName("quantity")
    private long quantity;

    @GraphQLField
    @GraphQLName("isRamp")
    @GraphQLNonNull
    private boolean isRamp;

    @GraphQLField
    @GraphQLName("isCreditable")
    private boolean isCreditable;

    @GraphQLField
    @GraphQLName("listUnitPriceBeforeOverride")
    private BigDecimal listUnitPriceBeforeOverride;

    @GraphQLField
    @GraphQLName("listUnitPrice")
    private BigDecimal listUnitPrice;

    @GraphQLField
    @GraphQLName("sellUnitPrice")
    private BigDecimal sellUnitPrice;

    @GraphQLField
    @GraphQLName("discountAmount")
    private BigDecimal discountAmount;

    @GraphQLField
    @GraphQLName("discounts")
    private List<DiscountDetailJson> discounts;

    @GraphQLField
    @GraphQLName("predefinedDiscounts")
    private List<PredefinedDiscountLineItemDetail> predefinedDiscounts;

    @GraphQLField
    @GraphQLName("amount")
    private BigDecimal amount;

    @GraphQLField
    @GraphQLName("listAmountBeforeOverride")
    private BigDecimal listAmountBeforeOverride;

    @GraphQLField
    @GraphQLName("listAmount")
    private BigDecimal listAmount;

    @GraphQLField
    @GraphQLName("annualizedAmount")
    private BigDecimal annualizedAmount;

    @GraphQLField
    @GraphQLName("taxEstimate")
    private BigDecimal taxEstimate;

    @GraphQLField
    @GraphQLName("effectiveDate")
    private Long effectiveDate;

    @GraphQLField
    @GraphQLName("endDate")
    private Long endDate;

    @GraphQLField
    @GraphQLName("listPriceOverrideRatio")
    private BigDecimal listPriceOverrideRatio;

    @GraphQLField
    @GraphQLName("pricingOverride")
    private PricingOverrideJson pricingOverride;

    @GraphQLField
    @GraphQLName("attributeReferences")
    private List<AttributeReference> attributeReferences;

    @GraphQLField
    @GraphQLName("customFields")
    private List<CustomFieldEntry> customFields;

    @GraphQLField
    @GraphQLName("arrOverride")
    private BigDecimal arrOverride;

    @GraphQLField
    @GraphQLName("replacedPlan")
    private PlanJson replacedPlan;

    @GraphQLField
    @GraphQLName("availableReplacementPlan")
    private PlanJson availableReplacementPlan;

    @GraphQLField
    @GraphQLName("metrics")
    private LineItemMetrics metrics;

    @GraphQLField
    @GraphQLName("restructureAmount")
    private BigDecimal restructureAmount;

    @GraphQLField
    @GraphQLName("recognitionRule")
    private RecognitionRule recognitionRule;

    public OrderLineItemDetail() {
        discounts = new ArrayList<>();
        predefinedDiscounts = new ArrayList<>();
        customFields = List.of();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getItemGroupId() {
        return itemGroupId;
    }

    public void setItemGroupId(String itemGroupId) {
        this.itemGroupId = itemGroupId;
    }

    public boolean getDryRunItem() {
        return isDryRunItem;
    }

    public void setDryRunItem(boolean dryRunItem) {
        isDryRunItem = dryRunItem;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public ActionType getAction() {
        return action;
    }

    public void setAction(ActionType action) {
        this.action = action;
    }

    public PlanJson getPlan() {
        return plan;
    }

    public void setPlan(PlanJson plan) {
        this.plan = plan;
    }

    public String getSubscriptionChargeId() {
        return subscriptionChargeId;
    }

    public void setSubscriptionChargeId(String subscriptionChargeId) {
        this.subscriptionChargeId = subscriptionChargeId;
    }

    public String getCurrencyConversionRateId() {
        return currencyConversionRateId;
    }

    public void setCurrencyConversionRateId(String currencyConversionRateId) {
        this.currencyConversionRateId = currencyConversionRateId;
    }

    public String getSubscriptionChargeGroupId() {
        return subscriptionChargeGroupId;
    }

    public void setSubscriptionChargeGroupId(String subscriptionChargeGroupId) {
        this.subscriptionChargeGroupId = subscriptionChargeGroupId;
    }

    public ChargeDetail getChargeDetail() {
        return chargeDetail;
    }

    public void setChargeDetail(ChargeDetail chargeDetail) {
        this.chargeDetail = chargeDetail;
    }

    public ChargeJson getCharge() {
        return charge;
    }

    public void setCharge(ChargeJson charge) {
        this.charge = charge;
    }

    public long getQuantity() {
        return quantity;
    }

    public void setQuantity(long quantity) {
        this.quantity = quantity;
    }

    public boolean getIsRamp() {
        return isRamp;
    }

    public void setIsRamp(boolean ramp) {
        isRamp = ramp;
    }

    public boolean isCreditable() {
        return isCreditable;
    }

    public void setCreditable(boolean creditable) {
        isCreditable = creditable;
    }

    public BigDecimal getListUnitPriceBeforeOverride() {
        return listUnitPriceBeforeOverride;
    }

    public void setListUnitPriceBeforeOverride(BigDecimal listUnitPriceBeforeOverride) {
        this.listUnitPriceBeforeOverride = listUnitPriceBeforeOverride;
    }

    public BigDecimal getListUnitPrice() {
        return listUnitPrice;
    }

    public void setListUnitPrice(BigDecimal listUnitPrice) {
        this.listUnitPrice = listUnitPrice;
    }

    public BigDecimal getSellUnitPrice() {
        return sellUnitPrice;
    }

    public void setSellUnitPrice(BigDecimal sellUnitPrice) {
        this.sellUnitPrice = sellUnitPrice;
    }

    public List<DiscountDetailJson> getDiscounts() {
        return discounts;
    }

    public void setDiscounts(List<DiscountDetailJson> discounts) {
        this.discounts = discounts == null ? new ArrayList<>() : discounts;
    }

    public List<PredefinedDiscountLineItemDetail> getPredefinedDiscounts() {
        return predefinedDiscounts == null ? new ArrayList<>() : predefinedDiscounts;
    }

    public void setPredefinedDiscounts(List<PredefinedDiscountLineItemDetail> predefinedDiscounts) {
        this.predefinedDiscounts = predefinedDiscounts;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getListAmountBeforeOverride() {
        return listAmountBeforeOverride;
    }

    public void setListAmountBeforeOverride(BigDecimal listAmountBeforeOverride) {
        this.listAmountBeforeOverride = listAmountBeforeOverride;
    }

    public BigDecimal getListAmount() {
        return listAmount;
    }

    public void setListAmount(BigDecimal listAmount) {
        this.listAmount = listAmount;
    }

    public BigDecimal getAnnualizedAmount() {
        return annualizedAmount;
    }

    public void setAnnualizedAmount(BigDecimal annualizedAmount) {
        this.annualizedAmount = annualizedAmount;
    }

    public Long getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Long effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getListPriceOverrideRatio() {
        return listPriceOverrideRatio;
    }

    public void setListPriceOverrideRatio(BigDecimal listPriceOverrideRatio) {
        this.listPriceOverrideRatio = listPriceOverrideRatio;
    }

    public List<AttributeReference> getAttributeReferences() {
        return attributeReferences;
    }

    public void setAttributeReferences(List<AttributeReference> attributeReferences) {
        this.attributeReferences = attributeReferences;
    }

    public List<CustomFieldEntry> getCustomFields() {
        return customFields == null ? List.of() : customFields;
    }

    public void setCustomFields(List<CustomFieldEntry> customFields) {
        this.customFields = customFields == null ? List.of() : customFields;
    }

    public BigDecimal getTaxEstimate() {
        return taxEstimate;
    }

    public void setTaxEstimate(BigDecimal taxEstimate) {
        this.taxEstimate = taxEstimate;
    }

    public BigDecimal getArrOverride() {
        return arrOverride;
    }

    public void setArrOverride(BigDecimal arrOverride) {
        this.arrOverride = arrOverride;
    }

    public LineItemMetrics getMetrics() {
        return metrics;
    }

    public void setMetrics(LineItemMetrics metrics) {
        this.metrics = metrics;
    }

    public PlanJson getReplacedPlan() {
        return replacedPlan;
    }

    public void setReplacedPlan(PlanJson replacedPlan) {
        this.replacedPlan = replacedPlan;
    }

    public PlanJson getAvailableReplacementPlan() {
        return availableReplacementPlan;
    }

    public void setAvailableReplacementPlan(PlanJson availableReplacementPlan) {
        this.availableReplacementPlan = availableReplacementPlan;
    }

    public BigDecimal getRestructureAmount() {
        return restructureAmount;
    }

    public void setRestructureAmount(BigDecimal restructureAmount) {
        this.restructureAmount = restructureAmount;
    }

    public PricingOverrideJson getPricingOverride() {
        return pricingOverride;
    }

    public void setPricingOverride(PricingOverrideJson pricingOverride) {
        this.pricingOverride = pricingOverride;
    }

    public RecognitionRule getRecognitionRule() {
        return recognitionRule;
    }

    public void setRecognitionRule(RecognitionRule recognitionRule) {
        this.recognitionRule = recognitionRule;
    }
}
