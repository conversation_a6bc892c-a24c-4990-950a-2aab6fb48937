package com.subskribe.billy.graphql.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.opportunity.model.OpportunityCrmType;
import com.subskribe.billy.resources.json.order.CustomBillingScheduleInput;
import com.subskribe.billy.resources.json.order.OrderLineItemRequestJson;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.shared.TenantDiscountJson;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.template.model.DocumentCustomContent;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.List;

@GraphQLName("CancelAndRestructureRequest")
public class CancelAndRestructureRequest {

    @JsonProperty
    @GraphQLField
    @GraphQLName("id")
    private String id;

    @JsonProperty
    @GraphQLField
    @GraphQLNonNull
    @GraphQLName("subscriptionId")
    private String subscriptionId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("name")
    private String name;

    @JsonProperty
    @GraphQLField
    @GraphQLName("accountId")
    String accountId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("orderType")
    @GraphQLNonNull
    private OrderType orderType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("paymentTerm")
    private String paymentTerm;

    @JsonProperty
    @GraphQLField
    @GraphQLName("shippingContactId")
    private String shippingContactId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingContactId")
    private String billingContactId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("predefinedDiscounts")
    private List<TenantDiscountJson> predefinedDiscounts;

    @JsonProperty
    @GraphQLField
    @GraphQLName("lineItems")
    private List<OrderLineItemRequestJson> lineItems;

    @JsonProperty
    @GraphQLField
    @GraphQLName("startDate")
    @GraphQLNonNull
    private Long startDate;

    @JsonProperty
    @GraphQLField
    @GraphQLName("endDate")
    private Long endDate;

    @JsonProperty
    @GraphQLField
    @GraphQLName("termLength")
    private RecurrenceJson termLength;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingCycle")
    private RecurrenceJson billingCycle;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingTerm")
    private BillingTerm billingTerm;

    @JsonProperty
    @GraphQLField
    @GraphQLName("billingAnchorDate")
    private Long billingAnchorDate;

    @JsonProperty
    @GraphQLField
    @GraphQLName("rampInterval")
    private List<Long> rampInterval;

    @JsonProperty
    @GraphQLField
    @GraphQLName("orderFormTemplateIds")
    private List<String> orderFormTemplateIds;

    @JsonProperty
    @GraphQLField
    @GraphQLName("crmOpportunityId")
    private String crmOpportunityId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("isPrimaryOrderForSfdcOpportunity")
    private Boolean isPrimaryOrderForSfdcOpportunity;

    @JsonProperty
    @GraphQLField
    @GraphQLName("crmOpportunityName")
    private String crmOpportunityName;

    @JsonProperty
    @GraphQLField
    @GraphQLName("crmOpportunityType")
    private String crmOpportunityType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("crmOpportunityStage")
    private String crmOpportunityStage;

    @JsonProperty
    @GraphQLField
    @GraphQLName("opportunityCrmType")
    private OpportunityCrmType opportunityCrmType;

    @JsonProperty
    @GraphQLField
    @GraphQLName("ownerId")
    private String ownerId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("documentMasterTemplateId")
    private String documentMasterTemplateId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("documentCustomContent")
    private DocumentCustomContent documentCustomContent;

    @JsonProperty
    @GraphQLField
    @GraphQLName("purchaseOrderNumber")
    private String purchaseOrderNumber;

    @JsonProperty
    @GraphQLField
    @GraphQLName("purchaseOrderRequiredForInvoicing")
    private Boolean purchaseOrderRequiredForInvoicing;

    @JsonProperty
    @GraphQLField
    @GraphQLName("autoRenew")
    private boolean autoRenew;

    @JsonProperty
    @GraphQLField
    @GraphQLName("approvalSegmentId")
    private String approvalSegmentId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("expiresOn")
    private Long expiresOn;

    @JsonProperty
    @GraphQLField
    @GraphQLName("executedOn")
    private Long executedOn;

    @JsonProperty
    @GraphQLField
    @GraphQLName("restructureForSubscriptionId")
    private String restructureForSubscriptionId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("compositeOrderId")
    private String compositeOrderId;

    @JsonProperty
    @GraphQLField
    @GraphQLName("customFields")
    private List<CustomFieldEntry> customFields;

    @JsonProperty
    @GraphQLField
    @GraphQLName("currency")
    private String currency;

    @JsonProperty
    @GraphQLField
    @GraphQLName("customBillingSchedule")
    private CustomBillingScheduleInput customBillingSchedule;

    @JsonProperty
    @GraphQLField
    @GraphQLName("subscriptionDurationModel")
    private SubscriptionDurationModel subscriptionDurationModel;

    public CancelAndRestructureRequest() {
        customFields = List.of();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public String getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(String paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public String getShippingContactId() {
        return shippingContactId;
    }

    public void setShippingContactId(String shippingContactId) {
        this.shippingContactId = shippingContactId;
    }

    public String getBillingContactId() {
        return billingContactId;
    }

    public void setBillingContactId(String billingContactId) {
        this.billingContactId = billingContactId;
    }

    public List<TenantDiscountJson> getPredefinedDiscounts() {
        return predefinedDiscounts;
    }

    public void setPredefinedDiscounts(List<TenantDiscountJson> predefinedDiscounts) {
        this.predefinedDiscounts = predefinedDiscounts;
    }

    public List<OrderLineItemRequestJson> getLineItems() {
        return lineItems;
    }

    public void setLineItems(List<OrderLineItemRequestJson> lineItems) {
        this.lineItems = lineItems;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public RecurrenceJson getTermLength() {
        return termLength;
    }

    public void setTermLength(RecurrenceJson termLength) {
        this.termLength = termLength;
    }

    public RecurrenceJson getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(RecurrenceJson billingCycle) {
        this.billingCycle = billingCycle;
    }

    public BillingTerm getBillingTerm() {
        return billingTerm;
    }

    public void setBillingTerm(BillingTerm billingTerm) {
        this.billingTerm = billingTerm;
    }

    public Long getBillingAnchorDate() {
        return billingAnchorDate;
    }

    public void setBillingAnchorDate(Long billingAnchorDate) {
        this.billingAnchorDate = billingAnchorDate;
    }

    public List<Long> getRampInterval() {
        return rampInterval;
    }

    public void setRampInterval(List<Long> rampInterval) {
        this.rampInterval = rampInterval;
    }

    public List<String> getOrderFormTemplateIds() {
        return orderFormTemplateIds;
    }

    public void setOrderFormTemplateIds(List<String> orderFormTemplateIds) {
        this.orderFormTemplateIds = orderFormTemplateIds;
    }

    public String getCrmOpportunityName() {
        return crmOpportunityName;
    }

    public void setCrmOpportunityName(String crmOpportunityName) {
        this.crmOpportunityName = crmOpportunityName;
    }

    public String getCrmOpportunityType() {
        return crmOpportunityType;
    }

    public void setCrmOpportunityType(String crmOpportunityType) {
        this.crmOpportunityType = crmOpportunityType;
    }

    public String getCrmOpportunityStage() {
        return crmOpportunityStage;
    }

    public void setCrmOpportunityStage(String crmOpportunityStage) {
        this.crmOpportunityStage = crmOpportunityStage;
    }

    public Boolean getPrimaryOrderForSfdcOpportunity() {
        return isPrimaryOrderForSfdcOpportunity;
    }

    public void setPrimaryOrderForSfdcOpportunity(Boolean primaryOrderForSfdcOpportunity) {
        isPrimaryOrderForSfdcOpportunity = primaryOrderForSfdcOpportunity;
    }

    public OpportunityCrmType getOpportunityCrmType() {
        return opportunityCrmType;
    }

    public void setOpportunityCrmType(OpportunityCrmType opportunityCrmType) {
        this.opportunityCrmType = opportunityCrmType;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getDocumentMasterTemplateId() {
        return documentMasterTemplateId;
    }

    public void setDocumentMasterTemplateId(String documentMasterTemplateId) {
        this.documentMasterTemplateId = documentMasterTemplateId;
    }

    public DocumentCustomContent getDocumentCustomContent() {
        return documentCustomContent;
    }

    public void setDocumentCustomContent(DocumentCustomContent documentCustomContent) {
        this.documentCustomContent = documentCustomContent;
    }

    public String getPurchaseOrderNumber() {
        return purchaseOrderNumber;
    }

    public void setPurchaseOrderNumber(String purchaseOrderNumber) {
        this.purchaseOrderNumber = purchaseOrderNumber;
    }

    public Boolean getPurchaseOrderRequiredForInvoicing() {
        return purchaseOrderRequiredForInvoicing;
    }

    public void setPurchaseOrderRequiredForInvoicing(Boolean purchaseOrderRequiredForInvoicing) {
        this.purchaseOrderRequiredForInvoicing = purchaseOrderRequiredForInvoicing;
    }

    public boolean isAutoRenew() {
        return autoRenew;
    }

    public void setAutoRenew(boolean autoRenew) {
        this.autoRenew = autoRenew;
    }

    public String getApprovalSegmentId() {
        return approvalSegmentId;
    }

    public void setApprovalSegmentId(String approvalSegmentId) {
        this.approvalSegmentId = approvalSegmentId;
    }

    public Long getExpiresOn() {
        return expiresOn;
    }

    public void setExpiresOn(Long expiresOn) {
        this.expiresOn = expiresOn;
    }

    public Long getExecutedOn() {
        return executedOn;
    }

    public void setExecutedOn(Long executedOn) {
        this.executedOn = executedOn;
    }

    public String getRestructureForSubscriptionId() {
        return restructureForSubscriptionId;
    }

    public void setRestructureForSubscriptionId(String restructureForSubscriptionId) {
        this.restructureForSubscriptionId = restructureForSubscriptionId;
    }

    public String getCompositeOrderId() {
        return compositeOrderId;
    }

    public void setCompositeOrderId(String compositeOrderId) {
        this.compositeOrderId = compositeOrderId;
    }

    public String getCrmOpportunityId() {
        return crmOpportunityId;
    }

    public void setCrmOpportunityId(String crmOpportunityId) {
        this.crmOpportunityId = crmOpportunityId;
    }

    public List<CustomFieldEntry> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<CustomFieldEntry> customFields) {
        this.customFields = customFields;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public CustomBillingScheduleInput getCustomBillingSchedule() {
        return customBillingSchedule;
    }

    public void setCustomBillingSchedule(CustomBillingScheduleInput customBillingSchedule) {
        this.customBillingSchedule = customBillingSchedule;
    }

    public SubscriptionDurationModel getSubscriptionDurationModel() {
        return subscriptionDurationModel;
    }

    public void setSubscriptionDurationModel(SubscriptionDurationModel subscriptionDurationModel) {
        this.subscriptionDurationModel = subscriptionDurationModel;
    }
}
