package com.subskribe.billy.graphql.order;

import com.subskribe.billy.customization.model.OrderCreationCustomizationOutputJson;
import com.subskribe.billy.graphql.account.AccountDetail;
import com.subskribe.billy.graphql.approvalflowinstance.ApprovalFlowInstanceGroupDetail;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.shared.PredefinedDiscountDetail;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrder;
import com.subskribe.billy.graphql.template.DocumentTemplateResponse;
import com.subskribe.billy.opportunity.model.OpportunityCrmType;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalSegmentJson;
import com.subskribe.billy.resources.json.metrics.MetricsJson;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.template.model.DocumentCustomContent;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.model.OrderTerms;
import graphql.annotations.annotationTypes.GraphQLDeprecate;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.BooleanUtils;

@GraphQLName("OrderDetail")
public class OrderDetail {

    @GraphQLField
    @GraphQLName("id")
    private String id;

    @GraphQLField
    @GraphQLName("externalId")
    private String externalId;

    @GraphQLField
    @GraphQLName("entityId")
    private String entityId;

    @GraphQLField
    @GraphQLName("name")
    private String name;

    @GraphQLField
    @GraphQLName("account")
    @GraphQLNonNull
    @GraphQLDeprecate
    private AccountJson account;

    @GraphQLField
    @GraphQLName("accountDetail")
    @GraphQLNonNull
    @GraphQLDeprecate
    private AccountDetail accountDetail;

    @GraphQLField
    @GraphQLName("createdBy")
    private UserJson createdBy;

    @GraphQLField
    @GraphQLName("owner")
    private UserJson owner;

    @GraphQLField
    @GraphQLName("resoldBy")
    private AccountJson resoldBy;

    @GraphQLField
    @GraphQLName("orderType")
    @GraphQLNonNull
    private OrderType orderType;

    @GraphQLField
    @GraphQLName("currency")
    private String currency;

    @GraphQLField
    @GraphQLNonNull
    @GraphQLName("paymentTerm")
    private String paymentTerm;

    // this field is deprecated and shall be removed in the future
    @GraphQLField
    @GraphQLName("subscriptionId")
    private String subscriptionId;

    @GraphQLField
    @GraphQLName("currentSubscription")
    private SubscriptionDetail currentSubscription;

    @GraphQLField
    @GraphQLName("updatedSubscription")
    private SubscriptionDetail updatedSubscription;

    @GraphQLField
    @GraphQLName("subscriptionTargetVersion")
    private int subscriptionTargetVersion;

    @GraphQLField
    @GraphQLName("renewalForSubscription")
    private SubscriptionDetail renewalForSubscription;

    @GraphQLField
    @GraphQLName("shippingContact")
    private AccountContactJson shippingContact;

    @GraphQLField
    @GraphQLName("billingContact")
    private AccountContactJson billingContact;

    @GraphQLField
    @GraphQLName("predefinedDiscounts")
    private List<PredefinedDiscountDetail> predefinedDiscounts;

    @GraphQLField
    @GraphQLName("creditableAmounts")
    private List<CreditableAmount> creditableAmounts;

    @GraphQLField
    @GraphQLName("lineItems")
    @GraphQLNonNull
    private List<@GraphQLNonNull OrderLineItemDetail> lineItems;

    @GraphQLField
    @GraphQLName("lineItemsNetEffect")
    private List<@GraphQLNonNull OrderLineItemDetail> lineItemsNetEffect;

    @GraphQLField
    @GraphQLName("startDate")
    @GraphQLNonNull
    private Long startDate;

    @GraphQLField
    @GraphQLName("endDate")
    private Long endDate;

    @GraphQLField
    @GraphQLName("termLength")
    private RecurrenceJson termLength;

    @GraphQLField
    @GraphQLName("billingCycle")
    @GraphQLNonNull
    private RecurrenceJson billingCycle;

    @GraphQLField
    @GraphQLName("billingTerm")
    @GraphQLNonNull
    private BillingTerm billingTerm;

    @GraphQLField
    @GraphQLName("totalListAmount")
    private BigDecimal totalListAmount;

    @GraphQLField
    @GraphQLName("totalListAmountBeforeOverride")
    private BigDecimal totalListAmountBeforeOverride;

    @GraphQLField
    @GraphQLName("taxEstimate")
    private BigDecimal taxEstimate;

    @GraphQLField
    @GraphQLName("totalAmount")
    private BigDecimal totalAmount;

    @GraphQLField
    @GraphQLName("totalDiscount")
    private BigDecimal totalDiscount;

    @GraphQLField
    @GraphQLName("totalDiscountPercent")
    private BigDecimal totalDiscountPercent;

    @GraphQLField
    @GraphQLName("totalRecurringDiscount")
    private BigDecimal totalRecurringDiscount;

    @GraphQLField
    @GraphQLName("totalRecurringDiscountPercent")
    private BigDecimal totalRecurringDiscountPercent;

    @GraphQLField
    @GraphQLName("totalNonRecurringDiscount")
    private BigDecimal totalNonRecurringDiscount;

    @GraphQLField
    @GraphQLName("totalNonRecurringDiscountPercent")
    private BigDecimal totalNonRecurringDiscountPercent;

    @GraphQLField
    @GraphQLName("deltaArrPercent")
    private BigDecimal deltaArrPercent;

    @GraphQLField
    @GraphQLName("status")
    @GraphQLNonNull
    private OrderStatus status;

    @GraphQLField
    @GraphQLName("executedOn")
    private Long executedOn;

    @GraphQLField
    @GraphQLName("updatedOn")
    private Long updatedOn;

    @GraphQLField
    @GraphQLName("rampInterval")
    private List<Long> rampInterval;

    @GraphQLField
    @GraphQLName("orderFormTemplates")
    @GraphQLNonNull
    private List<@GraphQLNonNull DocumentTemplateResponse> orderFormTemplates;

    @GraphQLField
    @GraphQLName("orderDocumentTemplates")
    private List<@GraphQLNonNull OrderDocumentTemplate> orderDocumentTemplates;

    @GraphQLField
    @GraphQLName("orderTerms")
    private List<OrderTerms> orderTerms;

    // TODO: Rename and remove _sfdc_ from all following fields.

    @GraphQLField
    @GraphQLName("sfdcOpportunityId")
    private String sfdcOpportunityId;

    @GraphQLField
    @GraphQLName("isPrimaryOrderForSfdcOpportunity")
    private Boolean isPrimaryOrderForSfdcOpportunity;

    @GraphQLField
    @GraphQLName("sfdcOpportunityName")
    private String sfdcOpportunityName;

    @GraphQLField
    @GraphQLName("sfdcOpportunityType")
    private String sfdcOpportunityType;

    @GraphQLField
    @GraphQLName("sfdcOpportunityStage")
    private String sfdcOpportunityStage;

    @GraphQLField
    @GraphQLName("sfdcOrderCanBeExecuted")
    private Boolean sfdcOrderCanBeExecuted;

    @GraphQLField
    @GraphQLName("opportunityCrmType")
    private OpportunityCrmType opportunityCrmType;

    @GraphQLField
    @GraphQLName("approvalFlows")
    @GraphQLNonNull
    private List<@GraphQLNonNull ApprovalFlowInstanceGroupDetail> approvalFlows;

    @GraphQLField
    @GraphQLName("orderMetrics")
    private MetricsJson metrics;

    @GraphQLField
    @GraphQLName("documentMasterTemplate")
    private DocumentMasterTemplate documentMasterTemplate;

    @GraphQLField
    private DocumentCustomContent documentCustomContent;

    @GraphQLField
    @GraphQLName("purchaseOrderNumber")
    private String purchaseOrderNumber;

    @GraphQLField
    @GraphQLName("purchaseOrderRequiredForInvoicing")
    private Boolean purchaseOrderRequiredForInvoicing;

    @GraphQLField
    @GraphQLName("autoRenew")
    private boolean autoRenew;

    @GraphQLField
    @GraphQLName("approvalSegment")
    private ApprovalSegmentJson approvalSegment;

    @GraphQLDeprecate
    @GraphQLField
    @GraphQLName("validApprovalSegments")
    private List<ApprovalSegmentJson> validApprovalSegments;

    @GraphQLField
    @GraphQLName("billingAnchorDate")
    private Long billingAnchorDate;

    @GraphQLField
    private String attachmentId;

    @GraphQLField
    @GraphQLName("compositeOrderId")
    private String compositeOrderId;

    @GraphQLField
    @GraphQLName("expiresOn")
    private Long expiresOn;

    @GraphQLField
    @GraphQLName("submittedBy")
    private UserJson submittedBy;

    @Deprecated
    @GraphQLDeprecate
    @GraphQLField
    @GraphQLName("recurringDiscountPercent")
    private BigDecimal recurringDiscountPercent;

    @Deprecated
    @GraphQLDeprecate
    @GraphQLField
    @GraphQLName("nonRecurringDiscountPercent")
    private BigDecimal nonRecurringDiscountPercent;

    @GraphQLField
    @GraphQLName("customFields")
    private List<CustomFieldEntry> customFields;

    @GraphQLField
    @GraphQLName("startDateType")
    private OrderStartDateType startDateType;

    @GraphQLField
    @GraphQLName("customBillingEligibleOrderLineIds")
    private List<@GraphQLNonNull String> customBillingEligibleOrderLineIds;

    @GraphQLField
    @GraphQLName("customPredefinedTemplatesOnOrder")
    private List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder;

    @GraphQLField
    @GraphQLName("subscriptionDurationModel")
    private SubscriptionDurationModel subscriptionDurationModel;

    @GraphQLField
    @GraphQLName("zeppaOutput")
    private OrderCreationCustomizationOutputJson zeppaOutput;

    public OrderDetail() {
        approvalFlows = new ArrayList<>();
        customFields = new ArrayList<>();
        lineItems = new ArrayList<>();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public AccountJson getAccount() {
        return account;
    }

    public void setAccount(AccountJson account) {
        this.account = account;
    }

    public UserJson getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(UserJson createdBy) {
        this.createdBy = createdBy;
    }

    public UserJson getOwner() {
        return owner;
    }

    public void setOwner(UserJson owner) {
        this.owner = owner;
    }

    public AccountJson getResoldBy() {
        return resoldBy;
    }

    public void setResoldBy(AccountJson resoldBy) {
        this.resoldBy = resoldBy;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(String paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public SubscriptionDetail getCurrentSubscription() {
        return currentSubscription;
    }

    public void setCurrentSubscription(SubscriptionDetail currentSubscription) {
        this.currentSubscription = currentSubscription;
    }

    public SubscriptionDetail getUpdatedSubscription() {
        return updatedSubscription;
    }

    public void setUpdatedSubscription(SubscriptionDetail updatedSubscription) {
        this.updatedSubscription = updatedSubscription;
    }

    public int getSubscriptionTargetVersion() {
        return subscriptionTargetVersion;
    }

    public void setSubscriptionTargetVersion(int subscriptionTargetVersion) {
        this.subscriptionTargetVersion = subscriptionTargetVersion;
    }

    public AccountContactJson getShippingContact() {
        return shippingContact;
    }

    public void setShippingContact(AccountContactJson shippingContact) {
        this.shippingContact = shippingContact;
    }

    public AccountContactJson getBillingContact() {
        return billingContact;
    }

    public void setBillingContact(AccountContactJson billingContact) {
        this.billingContact = billingContact;
    }

    public List<PredefinedDiscountDetail> getPredefinedDiscounts() {
        return predefinedDiscounts;
    }

    public void setPredefinedDiscounts(List<PredefinedDiscountDetail> predefinedDiscounts) {
        this.predefinedDiscounts = predefinedDiscounts;
    }

    public List<CreditableAmount> getCreditableAmounts() {
        return creditableAmounts;
    }

    public void setCreditableAmounts(List<CreditableAmount> creditableAmounts) {
        this.creditableAmounts = creditableAmounts;
    }

    public List<OrderLineItemDetail> getLineItems() {
        return lineItems;
    }

    public void setLineItems(List<OrderLineItemDetail> lineItems) {
        this.lineItems = lineItems;
    }

    public List<OrderLineItemDetail> getLineItemsNetEffect() {
        return lineItemsNetEffect;
    }

    public void setLineItemsNetEffect(List<OrderLineItemDetail> lineItemsNetEffect) {
        this.lineItemsNetEffect = lineItemsNetEffect;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public RecurrenceJson getTermLength() {
        return termLength;
    }

    public void setTermLength(RecurrenceJson termLength) {
        this.termLength = termLength;
    }

    public RecurrenceJson getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(RecurrenceJson billingCycle) {
        this.billingCycle = billingCycle;
    }

    public BillingTerm getBillingTerm() {
        return billingTerm;
    }

    public void setBillingTerm(BillingTerm billingTerm) {
        this.billingTerm = billingTerm;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalListAmount() {
        return totalListAmount;
    }

    public void setTotalListAmount(BigDecimal totalListAmount) {
        this.totalListAmount = totalListAmount;
    }

    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public BigDecimal getTotalDiscountPercent() {
        return totalDiscountPercent;
    }

    public void setTotalDiscountPercent(BigDecimal totalDiscountPercent) {
        this.totalDiscountPercent = totalDiscountPercent;
    }

    public BigDecimal getTotalRecurringDiscount() {
        return totalRecurringDiscount;
    }

    public void setTotalRecurringDiscount(BigDecimal totalRecurringDiscount) {
        this.totalRecurringDiscount = totalRecurringDiscount;
    }

    public BigDecimal getTotalRecurringDiscountPercent() {
        return totalRecurringDiscountPercent;
    }

    public void setTotalRecurringDiscountPercent(BigDecimal totalRecurringDiscountPercent) {
        this.totalRecurringDiscountPercent = totalRecurringDiscountPercent;
    }

    public BigDecimal getTotalNonRecurringDiscount() {
        return totalNonRecurringDiscount;
    }

    public void setTotalNonRecurringDiscount(BigDecimal totalNonRecurringDiscount) {
        this.totalNonRecurringDiscount = totalNonRecurringDiscount;
    }

    public BigDecimal getTotalNonRecurringDiscountPercent() {
        return totalNonRecurringDiscountPercent;
    }

    public void setTotalNonRecurringDiscountPercent(BigDecimal totalNonRecurringDiscountPercent) {
        this.totalNonRecurringDiscountPercent = totalNonRecurringDiscountPercent;
    }

    public BigDecimal getDeltaArrPercent() {
        return deltaArrPercent;
    }

    public void setDeltaArrPercent(BigDecimal deltaArrPercent) {
        this.deltaArrPercent = deltaArrPercent;
    }

    public BigDecimal getTotalListAmountBeforeOverride() {
        return totalListAmountBeforeOverride;
    }

    public void setTotalListAmountBeforeOverride(BigDecimal totalListAmountBeforeOverride) {
        this.totalListAmountBeforeOverride = totalListAmountBeforeOverride;
    }

    public OrderStatus getStatus() {
        return status;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public Long getExecutedOn() {
        return executedOn;
    }

    public void setExecutedOn(Long executedOn) {
        this.executedOn = executedOn;
    }

    public Long getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Long updatedOn) {
        this.updatedOn = updatedOn;
    }

    public List<Long> getRampInterval() {
        return rampInterval;
    }

    public void setRampInterval(List<Long> rampInterval) {
        this.rampInterval = rampInterval;
    }

    public List<DocumentTemplateResponse> getOrderFormTemplates() {
        return Objects.requireNonNullElseGet(orderFormTemplates, List::of);
    }

    public void setOrderDocumentTemplates(List<OrderDocumentTemplate> orderDocumentTemplates) {
        this.orderDocumentTemplates = orderDocumentTemplates;
    }

    public List<OrderDocumentTemplate> getOrderDocumentTemplates() {
        return Objects.requireNonNullElseGet(orderDocumentTemplates, List::of);
    }

    public void setOrderFormTemplates(List<DocumentTemplateResponse> orderFormTemplates) {
        this.orderFormTemplates = orderFormTemplates;
    }

    public List<OrderTerms> getOrderTerms() {
        return Objects.requireNonNullElseGet(orderTerms, List::of);
    }

    public void setOrderTerms(List<OrderTerms> orderTerms) {
        this.orderTerms = orderTerms;
    }

    public String getSfdcOpportunityId() {
        return sfdcOpportunityId;
    }

    public void setSfdcOpportunityId(String sfdcOpportunityId) {
        this.sfdcOpportunityId = sfdcOpportunityId;
    }

    public String getSfdcOpportunityName() {
        return sfdcOpportunityName;
    }

    public void setSfdcOpportunityName(String sfdcOpportunityName) {
        this.sfdcOpportunityName = sfdcOpportunityName;
    }

    public String getSfdcOpportunityType() {
        return sfdcOpportunityType;
    }

    public void setSfdcOpportunityType(String sfdcOpportunityType) {
        this.sfdcOpportunityType = sfdcOpportunityType;
    }

    public String getSfdcOpportunityStage() {
        return sfdcOpportunityStage;
    }

    public void setSfdcOpportunityStage(String sfdcOpportunityStage) {
        this.sfdcOpportunityStage = sfdcOpportunityStage;
    }

    public boolean getIsPrimaryOrderForSfdcOpportunity() {
        return BooleanUtils.isTrue(isPrimaryOrderForSfdcOpportunity);
    }

    public void setIsPrimaryOrderForSfdcOpportunity(Boolean primaryOrderForSfdcOpportunity) {
        isPrimaryOrderForSfdcOpportunity = primaryOrderForSfdcOpportunity;
    }

    public boolean getSfdcOrderCanBeExecuted() {
        return BooleanUtils.isNotFalse(sfdcOrderCanBeExecuted);
    }

    public void setSfdcOrderCanBeExecuted(Boolean sfdcOrderCanBeExecuted) {
        this.sfdcOrderCanBeExecuted = sfdcOrderCanBeExecuted;
    }

    public boolean isOrderRamped() {
        return lineItems.stream().anyMatch(OrderLineItemDetail::getIsRamp);
    }

    public List<ApprovalFlowInstanceGroupDetail> getApprovalFlows() {
        return approvalFlows;
    }

    public void setApprovalFlows(List<ApprovalFlowInstanceGroupDetail> approvalFlows) {
        this.approvalFlows = approvalFlows;
    }

    public MetricsJson getMetrics() {
        return metrics;
    }

    public void setMetrics(MetricsJson metrics) {
        this.metrics = metrics;
    }

    public SubscriptionDetail getRenewalForSubscription() {
        return renewalForSubscription;
    }

    public void setRenewalForSubscription(SubscriptionDetail renewalForSubscription) {
        this.renewalForSubscription = renewalForSubscription;
    }

    public DocumentMasterTemplate getDocumentMasterTemplate() {
        return documentMasterTemplate;
    }

    public void setDocumentMasterTemplate(DocumentMasterTemplate documentMasterTemplate) {
        this.documentMasterTemplate = documentMasterTemplate;
    }

    public DocumentCustomContent getDocumentCustomContent() {
        return documentCustomContent;
    }

    public void setDocumentCustomContent(DocumentCustomContent documentCustomContent) {
        this.documentCustomContent = documentCustomContent;
    }

    public String getPurchaseOrderNumber() {
        return purchaseOrderNumber;
    }

    public void setPurchaseOrderNumber(String purchaseOrderNumber) {
        this.purchaseOrderNumber = purchaseOrderNumber;
    }

    public Boolean getPurchaseOrderRequiredForInvoicing() {
        return purchaseOrderRequiredForInvoicing;
    }

    public void setPurchaseOrderRequiredForInvoicing(Boolean purchaseOrderRequiredForInvoicing) {
        this.purchaseOrderRequiredForInvoicing = purchaseOrderRequiredForInvoicing;
    }

    public boolean isAutoRenew() {
        return autoRenew;
    }

    public void setAutoRenew(boolean autoRenew) {
        this.autoRenew = autoRenew;
    }

    public OpportunityCrmType getOpportunityCrmType() {
        return opportunityCrmType;
    }

    public void setOpportunityCrmType(OpportunityCrmType opportunityCrmType) {
        this.opportunityCrmType = opportunityCrmType;
    }

    public ApprovalSegmentJson getApprovalSegment() {
        return approvalSegment;
    }

    public void setApprovalSegment(ApprovalSegmentJson approvalSegment) {
        this.approvalSegment = approvalSegment;
    }

    public List<ApprovalSegmentJson> getValidApprovalSegments() {
        return validApprovalSegments;
    }

    public void setValidApprovalSegments(List<ApprovalSegmentJson> validApprovalSegments) {
        this.validApprovalSegments = validApprovalSegments;
    }

    public Long getBillingAnchorDate() {
        return billingAnchorDate;
    }

    public void setBillingAnchorDate(Long billingAnchorDate) {
        this.billingAnchorDate = billingAnchorDate;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    public AccountDetail getAccountDetail() {
        return accountDetail;
    }

    public void setAccountDetail(AccountDetail accountDetail) {
        this.accountDetail = accountDetail;
    }

    public String getCompositeOrderId() {
        return compositeOrderId;
    }

    public void setCompositeOrderId(String compositeOrderId) {
        this.compositeOrderId = compositeOrderId;
    }

    public Long getExpiresOn() {
        return expiresOn;
    }

    public void setExpiresOn(Long expiresOn) {
        this.expiresOn = expiresOn;
    }

    public UserJson getSubmittedBy() {
        return submittedBy;
    }

    public void setSubmittedBy(UserJson submittedBy) {
        this.submittedBy = submittedBy;
    }

    public BigDecimal getTaxEstimate() {
        return taxEstimate;
    }

    public void setTaxEstimate(BigDecimal taxEstimate) {
        this.taxEstimate = taxEstimate;
    }

    public BigDecimal getRecurringDiscountPercent() {
        return recurringDiscountPercent;
    }

    public void setRecurringDiscountPercent(BigDecimal recurringDiscountPercent) {
        this.recurringDiscountPercent = recurringDiscountPercent;
    }

    public BigDecimal getNonRecurringDiscountPercent() {
        return nonRecurringDiscountPercent;
    }

    public void setNonRecurringDiscountPercent(BigDecimal nonRecurringDiscountPercent) {
        this.nonRecurringDiscountPercent = nonRecurringDiscountPercent;
    }

    public List<CustomFieldEntry> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<CustomFieldEntry> customFields) {
        this.customFields = customFields;
    }

    public OrderStartDateType getStartDateType() {
        return startDateType;
    }

    public void setStartDateType(OrderStartDateType startDateType) {
        this.startDateType = startDateType;
    }

    public List<String> getCustomBillingEligibleOrderLineIds() {
        return customBillingEligibleOrderLineIds;
    }

    public void setCustomBillingEligibleOrderLineIds(List<String> customBillingEligibleOrderLineIds) {
        this.customBillingEligibleOrderLineIds = customBillingEligibleOrderLineIds;
    }

    public List<CustomPredefinedTemplateOnOrder> getCustomPredefinedTemplatesOnOrder() {
        return customPredefinedTemplatesOnOrder;
    }

    public void setCustomPredefinedTemplatesOnOrder(List<CustomPredefinedTemplateOnOrder> customPredefinedTemplatesOnOrder) {
        this.customPredefinedTemplatesOnOrder = customPredefinedTemplatesOnOrder;
    }

    public SubscriptionDurationModel getSubscriptionDurationModel() {
        return subscriptionDurationModel;
    }

    public void setSubscriptionDurationModel(SubscriptionDurationModel subscriptionDurationModel) {
        this.subscriptionDurationModel = subscriptionDurationModel;
    }

    public OrderCreationCustomizationOutputJson getZeppaOutput() {
        return zeppaOutput;
    }

    public void setZeppaOutput(OrderCreationCustomizationOutputJson zeppaOutput) {
        this.zeppaOutput = zeppaOutput;
    }
}
