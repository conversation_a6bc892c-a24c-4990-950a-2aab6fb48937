package com.subskribe.billy.graphql.approvalflow;

import com.subskribe.billy.approvalflow.model.ApprovalFlow;
import com.subskribe.billy.approvalflow.model.ApprovalState;
import com.subskribe.billy.approvalflow.model.ApprovalTransitionRule;
import com.subskribe.billy.approvalflowhierarchy.model.ApprovalRole;
import com.subskribe.billy.approvalflowhierarchy.service.ApprovalFlowHierarchyService;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstance;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceData;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceGroup;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceState;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceStatus;
import com.subskribe.billy.approvalflowinstance.model.UserActionTime;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceService;
import com.subskribe.billy.escalationpolicy.service.EscalationPolicyService;
import com.subskribe.billy.graphql.approvalflowinstance.ApprovalFlowInstanceDataDetail;
import com.subskribe.billy.graphql.approvalflowinstance.ApprovalFlowInstanceDetail;
import com.subskribe.billy.graphql.approvalflowinstance.ApprovalFlowInstanceGroupDetail;
import com.subskribe.billy.graphql.approvalflowinstance.ApprovalFlowInstanceStateDetail;
import com.subskribe.billy.graphql.approvalflowinstance.ApprovalGroupDetail;
import com.subskribe.billy.graphql.approvalflowinstance.UserActionTimeJson;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderComment;
import com.subskribe.billy.order.services.OrderCommentService;
import com.subskribe.billy.resources.json.approvalflow.ApprovalFlowMapper;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalFlowHierarchyMapper;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.resources.json.usergroup.UserGroupMapper;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.Status;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.usergroup.model.UserGroup;
import com.subskribe.billy.usergroup.service.UserGroupService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class ApprovalFlowDataAggregator {

    private final UserGroupService userGroupService;
    private final UserService userService;
    private final ApprovalFlowMapper approvalFlowMapper;
    private final ApprovalFlowHierarchyMapper approvalFlowHierarchyMapper;
    private final UserGroupMapper userGroupMapper;
    private final OrderCommentService orderCommentService;
    private final ApprovalFlowHierarchyService approvalFlowHierarchyService;
    private final ApprovalFlowInstanceService approvalFlowInstanceService;
    private final EscalationPolicyService escalationPolicyService;

    @Inject
    public ApprovalFlowDataAggregator(
        UserGroupService userGroupService,
        UserService userService,
        OrderCommentService orderCommentService,
        ApprovalFlowHierarchyService approvalFlowHierarchyService,
        ApprovalFlowInstanceService approvalFlowInstanceService,
        EscalationPolicyService escalationPolicyService
    ) {
        this.userGroupService = userGroupService;
        this.userService = userService;
        this.orderCommentService = orderCommentService;
        this.approvalFlowHierarchyService = approvalFlowHierarchyService;
        this.approvalFlowInstanceService = approvalFlowInstanceService;
        this.escalationPolicyService = escalationPolicyService;
        approvalFlowMapper = Mappers.getMapper(ApprovalFlowMapper.class);
        userGroupMapper = Mappers.getMapper(UserGroupMapper.class);
        approvalFlowHierarchyMapper = Mappers.getMapper(ApprovalFlowHierarchyMapper.class);
    }

    public ApprovalFlowDetail toApprovalFlowDetail(ApprovalFlow approvalFlow) {
        List<ApprovalFlowDetail> approvalFlowDetails = toApprovalFlowDetails(List.of(approvalFlow));
        return approvalFlowDetails.get(0);
    }

    public List<ApprovalFlowDetail> toApprovalFlowDetails(List<ApprovalFlow> approvalFlows) {
        if (CollectionUtils.isEmpty(approvalFlows)) {
            return List.of();
        }

        Map<String, ApprovalGroupDetail> userGroupByIdMap = getUserGroupDetails(approvalFlows);

        List<ApprovalFlowDetail> approvalFlowDetails = new ArrayList<>();
        for (var approvalFlow : approvalFlows) {
            ApprovalFlowDetail approvalFlowDetail = getApprovalFlowDetail(userGroupByIdMap, approvalFlow);

            approvalFlowDetails.add(approvalFlowDetail);
        }

        return approvalFlowDetails;
    }

    private ApprovalFlowDetail getApprovalFlowDetail(Map<String, ApprovalGroupDetail> userGroupByIdMap, ApprovalFlow approvalFlow) {
        var approvalFlowDetail = new ApprovalFlowDetail();
        approvalFlowDetail.setId(approvalFlow.getApprovalFlowId());
        approvalFlowDetail.setEntityIds(approvalFlow.getEntityIds());
        approvalFlowDetail.setName(approvalFlow.getName());
        approvalFlowDetail.setDescription(approvalFlow.getDescription());
        approvalFlowDetail.setStatus(approvalFlow.getStatus().name());
        approvalFlowDetail.setIsSmartApproval(BooleanUtils.isTrue(approvalFlow.getIsSmartApproval()));

        List<ApprovalStateDetail> approvalStateDetails = getApprovalStateDetails(approvalFlow.getStates(), userGroupByIdMap);
        approvalFlowDetail.setStates(approvalStateDetails);

        Map<String, ApprovalStateDetail> approvalStateDetailMapById = approvalStateDetails
            .stream()
            .collect(Collectors.toMap(ApprovalStateDetail::getId, Function.identity()));
        approvalFlowDetail.setTransitionRules(getTransitionRuleDetails(approvalFlow.getTransitionRules(), approvalStateDetailMapById));
        return approvalFlowDetail;
    }

    private Map<String, ApprovalGroupDetail> getUserGroupDetails(List<ApprovalFlow> approvalFlows) {
        List<ApprovalState> states = approvalFlows.stream().flatMap(approvalFlow -> approvalFlow.getStates().stream()).toList();

        // find user or groups ids from ApprovalGroupId
        List<String> groupUserOrRoleIds = states.stream().map(ApprovalState::getApproverId).distinct().collect(Collectors.toList());
        return getUserGroupDetailMapById(groupUserOrRoleIds);
    }

    private Map<String, ApprovalGroupDetail> getUserGroupDetailMapById(List<String> groupUserOrRoleIds) {
        // get the user group details of individual groups first
        List<UserGroup> userGroups = userGroupService.getUserGroups(groupUserOrRoleIds, true);
        Set<String> foundGroupIds = userGroups.stream().map(UserGroup::getUserGroupId).collect(Collectors.toSet());
        List<ApprovalGroupDetail> approvalGroupDetails = userGroupMapper.toApprovalGroupDetails(userGroups);
        groupUserOrRoleIds.removeAll(foundGroupIds);

        if (CollectionUtils.isNotEmpty(groupUserOrRoleIds)) {
            List<ApprovalRole> approvalRoles = approvalFlowHierarchyService.getApprovalRoles(groupUserOrRoleIds, true);
            Set<String> approvalRolesIds = approvalRoles.stream().map(ApprovalRole::getApprovalRoleId).collect(Collectors.toSet());
            approvalRolesIds.retainAll(groupUserOrRoleIds);
            if (CollectionUtils.isNotEmpty(approvalRolesIds)) {
                approvalGroupDetails.addAll(getRolesAsUserGroupDetails(approvalRoles, approvalRolesIds));
                groupUserOrRoleIds.removeAll(approvalRolesIds);
            }
        }

        // create individual user groups for the users
        if (CollectionUtils.isNotEmpty(groupUserOrRoleIds)) {
            approvalGroupDetails.addAll(getUsersAsIndividualGroups(groupUserOrRoleIds));
        }
        return approvalGroupDetails.stream().collect(Collectors.toMap(ApprovalGroupDetail::getId, Function.identity()));
    }

    private List<ApprovalGroupDetail> getRolesAsUserGroupDetails(List<ApprovalRole> approvalRoles, Set<String> approvalRolesIdsToConvert) {
        Map<String, ApprovalRole> approvalRoleMap = approvalRoles
            .stream()
            .collect(Collectors.toMap(ApprovalRole::getApprovalRoleId, Function.identity()));
        List<ApprovalGroupDetail> approvalGroupDetails = new ArrayList<>();

        for (var approvalRoleId : approvalRolesIdsToConvert) {
            var approvalRole = approvalRoleMap.get(approvalRoleId);
            var approvalGroupDetail = new ApprovalGroupDetail();
            approvalGroupDetail.setUsers(List.of());
            approvalGroupDetail.setId(approvalRole.getApprovalRoleId());
            approvalGroupDetail.setUser(false);
            approvalGroupDetail.setRole(true);
            approvalGroupDetail.setUserGroup(false);
            approvalGroupDetail.setName(approvalRole.getName());
            approvalGroupDetail.setDescription(approvalRole.getDescription());
            approvalGroupDetail.setDeleted(approvalRole.isDeleted());

            approvalGroupDetails.add(approvalGroupDetail);
        }
        return approvalGroupDetails;
    }

    private List<ApprovalGroupDetail> getUsersAsIndividualGroups(List<String> userIds) {
        List<User> users = userService.getUsersByUserIds(userIds);
        List<ApprovalGroupDetail> approvalGroupDetails = new ArrayList<>();
        for (var user : users) {
            var approvalGroupDetail = new ApprovalGroupDetail();
            approvalGroupDetail.setUsers(userGroupMapper.usersToJson(List.of(user)));
            approvalGroupDetail.setId(user.getUserId());
            approvalGroupDetail.setName(user.getDisplayName());
            approvalGroupDetail.setUser(true);
            approvalGroupDetail.setRole(false);
            approvalGroupDetail.setUserGroup(false);
            approvalGroupDetail.setDeleted(user.getState() != Status.ACTIVE);

            approvalGroupDetails.add(approvalGroupDetail);
        }

        return approvalGroupDetails;
    }

    private List<ApprovalStateDetail> getApprovalStateDetails(
        List<ApprovalState> approvalStates,
        Map<String, ApprovalGroupDetail> approvalGroupByIdMap
    ) {
        if (CollectionUtils.isEmpty(approvalStates)) {
            return List.of();
        }

        List<ApprovalStateDetail> approvalStateDetails = new ArrayList<>();
        for (var approvalState : approvalStates) {
            var approvalStateDetail = new ApprovalStateDetail();
            approvalStateDetail.setId(approvalState.getApprovalStateId());
            approvalStateDetail.setName(approvalState.getName());
            approvalStateDetail.setAction(approvalFlowMapper.toActionJson(approvalState.getAction()));
            approvalStateDetail.setApprovalGroupDetail(approvalGroupByIdMap.get(approvalState.getApproverId()));
            approvalStateDetail.setApprovalGroup(userGroupMapper.toUserGroupDetail(approvalGroupByIdMap.get(approvalState.getApproverId())));

            var escalationPolicyOptional = escalationPolicyService.getEscalationPolicyOptional(approvalState.getEscalationPolicyId());
            escalationPolicyOptional.ifPresent(approvalStateDetail::setEscalationPolicy);

            approvalStateDetails.add(approvalStateDetail);
        }

        return approvalStateDetails;
    }

    private List<ApprovalTransitionRuleDetail> getTransitionRuleDetails(
        List<ApprovalTransitionRule> transitionRules,
        Map<String, ApprovalStateDetail> approvalStateDetailMapById
    ) {
        if (CollectionUtils.isEmpty(transitionRules)) {
            return List.of();
        }

        List<ApprovalTransitionRuleDetail> transitionRuleDetails = new ArrayList<>();
        for (var transitionRule : transitionRules) {
            var transitionRuleDetail = new ApprovalTransitionRuleDetail();
            transitionRuleDetail.setId(transitionRule.getTransitionRuleId());
            transitionRuleDetail.setName(transitionRule.getName());
            transitionRuleDetail.setCondition(transitionRule.getCondition());
            transitionRuleDetail.setFromState(approvalStateDetailMapById.get(transitionRule.getFromState()));
            transitionRuleDetail.setToState(approvalStateDetailMapById.get(transitionRule.getToState()));
            transitionRuleDetail.setRuleConditions(transitionRule.getRuleConditions());

            transitionRuleDetails.add(transitionRuleDetail);
        }

        return transitionRuleDetails;
    }

    public List<ApprovalFlowInstanceGroupDetail> getFilteredApprovalFlowInstanceGroupDetail(Order order) {
        boolean includePreview = order.getStatus() == OrderStatus.DRAFT;
        List<ApprovalFlowInstanceGroup> approvalFlowInstanceGroups = approvalFlowInstanceService.getApprovalFlowInstances(
            order.getOrderId(),
            includePreview
        );
        return toFilteredApprovalFlowInstanceGroupDetails(approvalFlowInstanceGroups);
    }

    // this filters out the "not applicable" approval flow instances
    public List<ApprovalFlowInstanceGroupDetail> toFilteredApprovalFlowInstanceGroupDetails(
        List<ApprovalFlowInstanceGroup> approvalFlowInstanceGroups
    ) {
        if (CollectionUtils.isEmpty(approvalFlowInstanceGroups)) {
            return List.of();
        }

        Map<String, ApprovalGroupDetail> userGroupByIdMap = getUserGroupDetailsFromInstanceGroups(approvalFlowInstanceGroups);
        Map<String, ApprovalRole> approvalRoleByIdMap = getApprovalRoleFromInstanceGroups(approvalFlowInstanceGroups);
        Set<String> orderIds = approvalFlowInstanceGroups.stream().map(ApprovalFlowInstanceGroup::getOrderId).collect(Collectors.toSet());
        if (orderIds.size() != 1) {
            throw new UnsupportedOperationException("Fetching of multiple approval flow groups is restricted only for a single order id");
        }
        Map<String, String> commentMessageById = getOrderCommentsForOrder(orderIds.stream().findAny().get());

        List<ApprovalFlowInstanceGroupDetail> approvalFlowInstanceGroupDetails = new ArrayList<>();
        for (var approvalFlowInstanceGroup : approvalFlowInstanceGroups) {
            ApprovalFlowInstanceGroupDetail approvalFlowInstanceGroupDetail = new ApprovalFlowInstanceGroupDetail();
            approvalFlowInstanceGroupDetail.setId(approvalFlowInstanceGroup.getGroupId());
            approvalFlowInstanceGroupDetail.setOrderId(approvalFlowInstanceGroup.getOrderId());
            approvalFlowInstanceGroupDetail.setSubmitterNote(
                approvalFlowMapper.toSubmitterNoteJson(approvalFlowInstanceGroup.getApprovalFlowSubmitterNote())
            );
            approvalFlowInstanceGroupDetail.setNote(approvalFlowInstanceGroup.getNote());
            approvalFlowInstanceGroupDetail.setVersion(approvalFlowInstanceGroup.getVersion());
            approvalFlowInstanceGroupDetail.setApprovalStatus(approvalFlowInstanceGroup.getApprovalStatus());
            approvalFlowInstanceGroupDetail.setWorkflowStatus(approvalFlowInstanceGroup.getWorkflowStatus());
            approvalFlowInstanceGroupDetail.setCreatedOn(approvalFlowInstanceGroup.getCreatedOn().getEpochSecond());
            approvalFlowInstanceGroupDetail.setUpdatedOn(approvalFlowInstanceGroup.getUpdatedOn().getEpochSecond());

            if (CollectionUtils.isEmpty(approvalFlowInstanceGroup.getApprovalFlowInstances())) {
                approvalFlowInstanceGroupDetail.setApprovalFlowInstances(List.of());
            } else {
                List<ApprovalFlowInstanceDetail> approvalFlowInstanceDetails = approvalFlowInstanceGroup
                    .getApprovalFlowInstances()
                    .stream()
                    .filter(approvalFlowInstance -> approvalFlowInstance.getStatus() != ApprovalFlowInstanceStatus.NOT_APPLICABLE)
                    .map(approvalFlowInstance ->
                        getApprovalFlowInstanceDetail(approvalFlowInstance, userGroupByIdMap, commentMessageById, approvalRoleByIdMap)
                    )
                    .collect(Collectors.toList());
                approvalFlowInstanceGroupDetail.setApprovalFlowInstances(approvalFlowInstanceDetails);
            }

            approvalFlowInstanceGroupDetails.add(approvalFlowInstanceGroupDetail);
        }

        return approvalFlowInstanceGroupDetails;
    }

    private Map<String, ApprovalRole> getApprovalRoleFromInstanceGroups(List<ApprovalFlowInstanceGroup> approvalFlowInstanceGroups) {
        List<ApprovalFlowInstanceState> states = getApprovalFlowInstanceStatesFromGroups(approvalFlowInstanceGroups);
        if (CollectionUtils.isEmpty(states)) {
            return Map.of();
        }

        List<String> approvalRoleIdsResolvedFrom = states
            .stream()
            .map(ApprovalFlowInstanceState::getApprovalRoleIdResolvedFrom)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(approvalRoleIdsResolvedFrom)) {
            return Map.of();
        }

        List<ApprovalRole> approvalRoles = approvalFlowHierarchyService.getApprovalRoles(approvalRoleIdsResolvedFrom, true);
        return approvalRoles.stream().collect(Collectors.toMap(ApprovalRole::getApprovalRoleId, Function.identity()));
    }

    private List<ApprovalFlowInstanceState> getApprovalFlowInstanceStatesFromGroups(List<ApprovalFlowInstanceGroup> approvalFlowInstanceGroups) {
        if (CollectionUtils.isEmpty(approvalFlowInstanceGroups)) {
            return List.of();
        }

        // get all the approval group ids
        List<ApprovalFlowInstance> approvalFlowInstances = approvalFlowInstanceGroups
            .stream()
            .flatMap(approvalFlowInstanceGroup -> approvalFlowInstanceGroup.getApprovalFlowInstances().stream())
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(approvalFlowInstances)) {
            return List.of();
        }

        return approvalFlowInstances
            .stream()
            .flatMap(approvalFlowInstance -> approvalFlowInstance.getApprovalFlowData().getStates().stream())
            .collect(Collectors.toList());
    }

    private Map<String, String> getOrderCommentsForOrder(String orderId) {
        List<OrderComment> orderComments = orderCommentService.getOrderComments(orderId);
        if (CollectionUtils.isEmpty(orderComments)) {
            return Map.of();
        }

        return orderComments.stream().collect(Collectors.toMap(OrderComment::getCommentId, OrderComment::getMessage));
    }

    private ApprovalFlowInstanceDetail getApprovalFlowInstanceDetail(
        ApprovalFlowInstance approvalFlowInstance,
        Map<String, ApprovalGroupDetail> userGroupByIdMap,
        Map<String, String> commentMessageByMessageId,
        Map<String, ApprovalRole> approvalRoleByIdMap
    ) {
        var approvalFlowInstanceDetail = new ApprovalFlowInstanceDetail();
        approvalFlowInstanceDetail.setId(approvalFlowInstance.getId().toString());
        approvalFlowInstanceDetail.setApprovalFlowId(approvalFlowInstance.getApprovalFlowId());
        approvalFlowInstanceDetail.setActiveStateId(approvalFlowInstance.getActiveStateId());
        approvalFlowInstanceDetail.setStatus(approvalFlowInstance.getStatus());

        approvalFlowInstanceDetail.setData(
            getApprovalFlowInstanceDataDetail(
                approvalFlowInstance.getApprovalFlowData(),
                userGroupByIdMap,
                commentMessageByMessageId,
                approvalRoleByIdMap
            )
        );
        return approvalFlowInstanceDetail;
    }

    private ApprovalFlowInstanceDataDetail getApprovalFlowInstanceDataDetail(
        ApprovalFlowInstanceData approvalFlowInstanceData,
        Map<String, ApprovalGroupDetail> userGroupByIdMap,
        Map<String, String> commentMessageByMessageId,
        Map<String, ApprovalRole> approvalRoleByIdMap
    ) {
        var approvalFlowInstanceDataDetail = new ApprovalFlowInstanceDataDetail();
        approvalFlowInstanceDataDetail.setId(approvalFlowInstanceData.getId().toString());
        approvalFlowInstanceDataDetail.setApprovalFlowId(approvalFlowInstanceDataDetail.getApprovalFlowId());
        approvalFlowInstanceDataDetail.setName(approvalFlowInstanceData.getName());
        approvalFlowInstanceDataDetail.setDescription(approvalFlowInstanceData.getDescription());
        approvalFlowInstanceDataDetail.setIsSmartApproval(approvalFlowInstanceData.getIsSmartApproval());
        approvalFlowInstanceDataDetail.setStatus(approvalFlowInstanceData.getStatus());

        List<ApprovalFlowInstanceStateDetail> approvalFlowInstanceStateDetails = getApprovalFlowInstanceStateDetails(
            approvalFlowInstanceData.getStates(),
            userGroupByIdMap,
            commentMessageByMessageId,
            approvalRoleByIdMap
        );
        approvalFlowInstanceDataDetail.setStates(approvalFlowInstanceStateDetails);
        Map<String, ApprovalStateDetail> approvalStateDetailByIdMap = approvalFlowInstanceStateDetails
            .stream()
            .collect(Collectors.toMap(ApprovalFlowInstanceStateDetail::getId, Function.identity()));

        approvalFlowInstanceDataDetail.setTransitionRules(
            getTransitionRuleDetails(approvalFlowInstanceData.getTransitionRules(), approvalStateDetailByIdMap)
        );
        return approvalFlowInstanceDataDetail;
    }

    private List<ApprovalFlowInstanceStateDetail> getApprovalFlowInstanceStateDetails(
        List<ApprovalFlowInstanceState> approvalFlowInstanceStates,
        Map<String, ApprovalGroupDetail> approvalGroupByIdMap,
        Map<String, String> commentMessageByMessageId,
        Map<String, ApprovalRole> approvalRoleByIdMap
    ) {
        if (CollectionUtils.isEmpty(approvalFlowInstanceStates)) {
            return List.of();
        }

        List<UserJson> users = approvalGroupByIdMap
            .values()
            .stream()
            .flatMap(approvalGroupDetail -> approvalGroupDetail.getUsers().stream())
            .toList();
        Map<String, UserJson> userJsonByIdMap = users
            .stream()
            .collect(Collectors.toMap(UserJson::getId, Function.identity(), (user, userDup) -> user));

        List<ApprovalFlowInstanceStateDetail> approvalFlowInstanceStateDetails = new ArrayList<>();
        for (var approvalFlowInstanceState : approvalFlowInstanceStates) {
            var approvalFlowInstanceStateDetail = new ApprovalFlowInstanceStateDetail();
            approvalFlowInstanceStateDetail.setId(approvalFlowInstanceState.getApprovalStateId());
            approvalFlowInstanceStateDetail.setName(approvalFlowInstanceState.getName());
            approvalFlowInstanceStateDetail.setAction(approvalFlowMapper.toActionJson(approvalFlowInstanceState.getAction()));

            var escalationPolicyOptional = escalationPolicyService.getEscalationPolicyOptional(approvalFlowInstanceState.getEscalationPolicyId());
            escalationPolicyOptional.ifPresent(approvalFlowInstanceStateDetail::setEscalationPolicy);

            String approvalGroupId = StringUtils.isBlank(approvalFlowInstanceState.getApproverId())
                ? approvalFlowInstanceState.getApprovalGroupId()
                : approvalFlowInstanceState.getApproverId();
            approvalFlowInstanceStateDetail.setApprovalGroupDetail(approvalGroupByIdMap.get(approvalGroupId));
            approvalFlowInstanceStateDetail.setApprovalGroup(userGroupMapper.toUserGroupDetail(approvalGroupByIdMap.get(approvalGroupId)));
            approvalFlowInstanceStateDetail.setStatus(approvalFlowInstanceState.getStatus());

            if (StringUtils.isNotBlank(approvalFlowInstanceState.getApprovalRoleIdResolvedFrom())) {
                approvalFlowInstanceStateDetail
                    .getApprovalGroupDetail()
                    .setApprovalRoleResolvedFrom(
                        approvalFlowHierarchyMapper.approvalRoleToJson(
                            approvalRoleByIdMap.get(approvalFlowInstanceState.getApprovalRoleIdResolvedFrom())
                        )
                    );
            }

            // todo: add a proper migration file for this fix. This is a temporary workaround on old orders for an old bug which was fixed on 4/27
            // For future testing: https://dev2.subskribe.net/orders/ORD-GKCCCN4 shows gql error as state returns null
            if (approvalFlowInstanceStateDetail.getStatus() == null) {
                approvalFlowInstanceStateDetail.setStatus(ApprovalFlowInstanceStatus.INACTIVE);
            }

            approvalFlowInstanceStateDetail.setApprovedBy(
                getUserActionTimeList(approvalFlowInstanceState.getApprovedBy(), userJsonByIdMap, commentMessageByMessageId)
            );
            approvalFlowInstanceStateDetail.setRejectedBy(
                getUserActionTimeList(approvalFlowInstanceState.getRejectedBy(), userJsonByIdMap, commentMessageByMessageId)
            );

            approvalFlowInstanceStateDetail.setSmartlyApprovedBySystem(BooleanUtils.isTrue(approvalFlowInstanceState.isSmartlyApprovedBySystem()));

            approvalFlowInstanceStateDetails.add(approvalFlowInstanceStateDetail);
        }

        return approvalFlowInstanceStateDetails;
    }

    private List<UserActionTimeJson> getUserActionTimeList(
        List<UserActionTime> userActionTimeList,
        Map<String, UserJson> userJsonByIdMap,
        Map<String, String> commentMessageByMessageId
    ) {
        if (CollectionUtils.isEmpty(userActionTimeList)) {
            return List.of();
        }

        List<UserActionTimeJson> userActionTimeJsonList = new ArrayList<>();
        for (var userActionTime : userActionTimeList) {
            var userActionTimeJson = new UserActionTimeJson();
            userActionTimeJson.setTime(userActionTime.getTime().getEpochSecond());
            userActionTimeJson.setUser(userJsonByIdMap.get(userActionTime.getUserId()));
            if (StringUtils.isNotBlank(userActionTime.getCommentId())) {
                userActionTimeJson.setNote(commentMessageByMessageId.get(userActionTime.getCommentId()));
            }

            userActionTimeJsonList.add(userActionTimeJson);
        }

        return userActionTimeJsonList;
    }

    private Map<String, ApprovalGroupDetail> getUserGroupDetailsFromInstanceGroups(List<ApprovalFlowInstanceGroup> approvalFlowInstanceGroups) {
        List<ApprovalFlowInstanceState> states = getApprovalFlowInstanceStatesFromGroups(approvalFlowInstanceGroups);
        if (CollectionUtils.isEmpty(states)) {
            return Map.of();
        }

        Set<String> groupsOrUserIdsSet = states
            .stream()
            .map(ApprovalFlowInstanceState::getApproverId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        Set<String> groupsOrUserIdsFromApprovalGroupIds = states
            .stream()
            .map(ApprovalFlowInstanceState::getApprovalGroupId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(groupsOrUserIdsFromApprovalGroupIds)) {
            groupsOrUserIdsSet.addAll(groupsOrUserIdsFromApprovalGroupIds);
        }

        // get approved and rejected users from the states
        for (var approvalFlowInstanceState : states) {
            if (CollectionUtils.isNotEmpty(approvalFlowInstanceState.getApprovedBy())) {
                groupsOrUserIdsSet.addAll(approvalFlowInstanceState.getApprovedBy().stream().map(UserActionTime::getUserId).toList());
            }

            if (CollectionUtils.isNotEmpty(approvalFlowInstanceState.getRejectedBy())) {
                groupsOrUserIdsSet.addAll(approvalFlowInstanceState.getRejectedBy().stream().map(UserActionTime::getUserId).toList());
            }
        }

        List<String> groupOrUserIds = new ArrayList<>(groupsOrUserIdsSet);
        return getUserGroupDetailMapById(groupOrUserIds);
    }
}
