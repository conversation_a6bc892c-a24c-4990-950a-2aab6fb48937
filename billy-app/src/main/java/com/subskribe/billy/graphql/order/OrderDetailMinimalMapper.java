package com.subskribe.billy.graphql.order;

import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.mapper.BaseMapper;
import java.time.Instant;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
public interface OrderDetailMinimalMapper extends BaseMapper {
    @Mapping(source = "orderId", target = "id")
    @Mapping(source = "startDate", target = "effectiveDate", qualifiedByName = "startDateToEffectiveDateConversion")
    @Mapping(target = "name", defaultValue = "")
    @Mapping(source = "customBillingEligibleOrderLineIds", target = "customBillingEligibleOrderLineIds")
    OrderDetailMinimal orderToOrderDetailMinimal(Order order);

    List<OrderDetailMinimal> ordersToOrderDetailMinimals(List<Order> orders);

    @Named("startDateToEffectiveDateConversion")
    default Long startDateToEffectiveDateConversion(Instant startDate) {
        if (startDate == null) {
            return Instant.now().toEpochMilli();
        }
        return startDate.getEpochSecond();
    }
}
