package com.subskribe.billy.graphql.order;

import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.math.BigDecimal;
import java.util.List;
import javax.annotation.Nullable;
import org.immutables.value.Value;

@GraphQLName("OrderDetailMinimal")
@BillyModelStyle
@Value.Immutable
public interface OrderDetailMinimal {
    @Nullable
    @GraphQLField
    @GraphQLName("id")
    String getId();

    @Nullable
    @GraphQLField
    @GraphQLName("name")
    String getName();

    @GraphQLNonNull
    @GraphQLField
    @GraphQLName("orderType")
    OrderType getOrderType();

    @Nullable
    @GraphQLField
    @GraphQLName("ownerId")
    String getOwnerId();

    @GraphQLField
    @GraphQLName("status")
    @GraphQLNonNull
    OrderStatus getStatus();

    @GraphQLField
    @GraphQLName("effectiveDate")
    Long getEffectiveDate();

    @GraphQLField
    @GraphQLName("totalAmount")
    BigDecimal getTotalAmount();

    @Nullable
    @GraphQLField
    @GraphQLName("customBillingEligibleOrderLineIds")
    List<String> getCustomBillingEligibleOrderLineIds();

    @Nullable
    @GraphQLField
    @GraphQLName("subscriptionDurationModel")
    SubscriptionDurationModel getSubscriptionDurationModel();
}
