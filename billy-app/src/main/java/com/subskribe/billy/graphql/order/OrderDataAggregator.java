package com.subskribe.billy.graphql.order;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.approvalflowhierarchy.model.ApprovalSegment;
import com.subskribe.billy.approvalflowhierarchy.service.ApprovalFlowHierarchyService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.graphql.account.AccountDetailMapper;
import com.subskribe.billy.graphql.productcatalog.ChargeDataAggregator;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.graphql.productcatalog.PlanDataAggregator;
import com.subskribe.billy.graphql.shared.PredefinedDiscountDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountLineItemDetail;
import com.subskribe.billy.graphql.subscription.SubscriptionDataAggregator;
import com.subskribe.billy.graphql.template.DocumentTemplateMapper;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.LineItemMetrics;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderCustomBillingService;
import com.subskribe.billy.order.services.OrderDiscountService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderServiceHelper;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.account.AccountMapper;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalFlowHierarchyMapper;
import com.subskribe.billy.resources.json.metrics.MetricsJson;
import com.subskribe.billy.resources.json.metrics.MetricsJsonMapper;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.order.OrderLineItemJson;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.resources.json.shared.TenantDiscountLineItemJson;
import com.subskribe.billy.resources.json.tenant.UserMapper;
import com.subskribe.billy.shared.DataValidation;
import com.subskribe.billy.shared.StringConverter;
import com.subskribe.billy.shared.entitycache.EntityCache;
import com.subskribe.billy.shared.entitycache.EntityCacheBuilder;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.subscription.services.SubscriptionOrderService;
import com.subskribe.billy.template.model.CustomTemplateUpdateOnOrder;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.template.model.OrderTerms;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderGetService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.OrderTermsService;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.Validator;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.mapstruct.factory.Mappers;

public class OrderDataAggregator {

    private final OrderGetService orderGetService;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final ProductCatalogGetService productCatalogGetService;
    private final AccountGetService accountGetService;
    private final UserService userService;
    private final OrderMapper orderMapper;
    private final OrderDetailsMapper orderDetailsMapper;
    private final AccountMapper accountMapper;
    private final PlanMapper planMapper;
    private final UserMapper userMapper;
    private final SubscriptionDataAggregator subscriptionDataAggregator;
    private final DocumentTemplateMapper documentTemplateMapper;
    private final ChargeDataAggregator chargeDataAggregator;
    private final AccountDetailMapper accountDetailMapper;
    private final MetricsService metricsService;
    private final MetricsJsonMapper metricsJsonMapper;
    private final ApprovalFlowHierarchyService approvalFlowHierarchyService;
    private final ApprovalFlowHierarchyMapper approvalFlowHierarchyMapper;
    private final CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService;
    private final FeatureService featureService;
    private final OrderTermsService orderTermsService;
    private final SubscriptionOrderService subscriptionOrderService;
    private final OrderCustomBillingService orderCustomBillingService;
    private final PlanDataAggregator planDataAggregator;
    private final EntityCacheBuilder entityCacheBuilder;

    @Inject
    public OrderDataAggregator(
        OrderGetService orderGetService,
        DocumentTemplateGetService documentTemplateGetService,
        ProductCatalogGetService productCatalogGetService,
        AccountGetService accountGetService,
        UserService userService,
        SubscriptionDataAggregator subscriptionDataAggregator,
        ChargeDataAggregator chargeDataAggregator,
        MetricsService metricsService,
        ApprovalFlowHierarchyService approvalFlowHierarchyService,
        CustomTemplateUpdatedOnOrderGetService customTemplateUpdatedOnOrderGetService,
        FeatureService featureService,
        OrderTermsService orderTermsService,
        SubscriptionOrderService subscriptionOrderService,
        OrderCustomBillingService orderCustomBillingService,
        PlanDataAggregator planDataAggregator,
        EntityCacheBuilder entityCacheBuilder
    ) {
        this.orderGetService = orderGetService;
        this.documentTemplateGetService = documentTemplateGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.accountGetService = accountGetService;
        this.userService = userService;
        this.subscriptionDataAggregator = subscriptionDataAggregator;
        this.chargeDataAggregator = chargeDataAggregator;
        this.metricsService = metricsService;
        this.approvalFlowHierarchyService = approvalFlowHierarchyService;
        this.customTemplateUpdatedOnOrderGetService = customTemplateUpdatedOnOrderGetService;
        this.featureService = featureService;
        this.orderTermsService = orderTermsService;
        this.subscriptionOrderService = subscriptionOrderService;
        this.orderCustomBillingService = orderCustomBillingService;
        this.planDataAggregator = planDataAggregator;
        this.entityCacheBuilder = entityCacheBuilder;
        orderDetailsMapper = Mappers.getMapper(OrderDetailsMapper.class);
        accountMapper = Mappers.getMapper(AccountMapper.class);
        planMapper = Mappers.getMapper(PlanMapper.class);
        userMapper = Mappers.getMapper(UserMapper.class);
        orderMapper = Mappers.getMapper(OrderMapper.class);
        documentTemplateMapper = Mappers.getMapper(DocumentTemplateMapper.class);
        metricsJsonMapper = Mappers.getMapper(MetricsJsonMapper.class);
        approvalFlowHierarchyMapper = Mappers.getMapper(ApprovalFlowHierarchyMapper.class);
        accountDetailMapper = Mappers.getMapper(AccountDetailMapper.class);
    }

    public OrderDetail getOrderDetail(String orderId, boolean skipApprovals, boolean skipMetrics) {
        Order order = orderGetService.getOrderByOrderId(orderId);
        return getOrderDetail(order, skipApprovals, skipMetrics);
    }

    @SuppressFBWarnings("NP_NULL_ON_SOME_PATH")
    public OrderDetail getOrderDetail(Order order, boolean skipApprovals, boolean skipMetrics) {
        Map<String, Charge> chargeMap = OrderServiceHelper.getChargeMap(productCatalogGetService, order.getLineItems());
        Map<String, BigDecimal> orderLineItemAnnualizedAmounts = metricsService.getOrderLineItemAnnualizedAmounts(
            order,
            new ArrayList<>(chargeMap.values())
        );
        OrderServiceHelper.updateOrderLineItemAnnualizedAmounts(order, orderLineItemAnnualizedAmounts);
        OrderJson orderJson = orderMapper.orderToJson(order);
        OrderDetail orderDetail = orderDetailsMapper.orderJsonToOrderDetails(orderJson);

        setOrderDetailAccountAndContactFields(orderDetail, order);
        setOrderDetailUserFields(orderDetail, order);
        setOrderLineItemDetailsAndPredefinedDiscounts(order, orderJson, orderDetail);
        setOrderDiscountDetails(order, orderDetail, chargeMap);

        setOrderDetailDocumentTemplateFields(orderDetail, order);
        orderDetail.setDocumentCustomContent(order.getDocumentCustomContent());
        setOrderDetailRecurringAndNonRecurringDiscounts(orderDetail, order, chargeMap);
        addSubscriptionDetailsToOrderDetail(order, orderDetail);

        // calculating metrics and approval rules are heavy operations and not necessary in most cases.
        // e.g. calculating order line amounts during dry run, deleting an order, generating an amendment from subscription etc
        // todo: this is probably better split out into needed and optional operations
        if (!skipApprovals) {
            addUserApprovalFlowSegmentDetails(order, orderDetail);
        }

        if (!skipMetrics) {
            Metrics orderMetrics = metricsService.getOrderMetrics(order, Instant.now());
            MetricsJson metricsJson = metricsJsonMapper.metricsToJson(orderMetrics);
            orderDetail.setMetrics(metricsJson);
            setOrderLineMetrics(orderDetail, order);
            orderDetail.setDeltaArrPercent(OrderServiceHelper.getDeltaArrPercent(order, orderMetrics, metricsService));
        }

        setCreditableChargeDetails(orderDetail);
        setCreditableAmounts(orderDetail);

        orderDetail.setCustomBillingEligibleOrderLineIds(getCustomBillingEligibleOrderLines(order, chargeMap));

        return orderDetail;
    }

    public List<String> getCustomBillingEligibleOrderLines(Order order, Map<String, Charge> chargeMap) {
        if (CollectionUtils.isNotEmpty(order.getCustomBillingEligibleOrderLineIds())) {
            return order.getCustomBillingEligibleOrderLineIds();
        }

        // As this is get path we can always set the eligible lines if FF is enabled.
        if (featureService.isEnabled(Feature.CUSTOM_BILLING)) {
            // If the order is saved with a custom billing schedule fetch it from the db
            if (order.getOrderId() != null) {
                CustomBillingSchedule customBillingSchedule = orderGetService.getCustomBillingScheduleForOrder(order.getOrderId(), false);
                if (customBillingSchedule != null) {
                    return customBillingSchedule.orderLines();
                }
            }

            // Else set it by checking the order line items
            return orderCustomBillingService
                .getOrderLineItemsEligibleForCustomBilling(order, chargeMap)
                .stream()
                .map(OrderLineItem::getOrderLineId)
                .collect(Collectors.toList());
        }

        // Explicitly leave the field as null instead of an empty array just for readability
        return null;
    }

    private void setCreditableChargeDetails(OrderDetail orderDetail) {
        orderDetail.getLineItems().forEach(lineItem -> lineItem.setCreditable(lineItem.getCharge().isCreditable()));
        orderDetail.getLineItemsNetEffect().forEach(lineItem -> lineItem.setCreditable(lineItem.getCharge().isCreditable()));
    }

    private void setCreditableAmounts(OrderDetail orderDetail) {
        List<CreditableAmount> creditableAmounts = orderDetail
            .getLineItems()
            .stream()
            .filter(
                lineItem ->
                    lineItem.getCharge().getType() == ChargeType.ONE_TIME &&
                    lineItem.getCharge().isCreditable() &&
                    Objects.nonNull(lineItem.getSubscriptionChargeId())
            )
            .map(lineItem -> {
                BigDecimal maxCreditableAmount = subscriptionOrderService.getCreditableAmountFromSubscriptionChargeId(
                    lineItem.getSubscriptionChargeId()
                );
                CreditableAmount creditableAmount = new CreditableAmount();
                creditableAmount.setSubscriptionChargeId(lineItem.getSubscriptionChargeId());
                creditableAmount.setCreditableAmount(lineItem.getAmount());
                creditableAmount.setMaxCreditableAmount(maxCreditableAmount.negate());
                return creditableAmount;
            })
            .collect(Collectors.toList());
        orderDetail.setCreditableAmounts(creditableAmounts);
    }

    private void setOrderDiscountDetails(Order order, OrderDetail orderDetail, Map<String, Charge> chargeMap) {
        orderDetail.setTotalDiscount(OrderDiscountService.getNetDiscountAmountCurrencyScaled(order.getLineItemsNetEffect(), chargeMap));
        orderDetail.setTotalDiscountPercent(OrderDiscountService.calculateOrderLineNetDiscountPercent(order.getLineItemsNetEffect(), chargeMap));

        List<OrderLineItem> recurringOrderLines = OrderServiceHelper.getRecurringOrderLineItems(order.getLineItemsNetEffect(), chargeMap);
        orderDetail.setTotalRecurringDiscount(OrderDiscountService.getNetDiscountAmountCurrencyScaled(recurringOrderLines, chargeMap));
        orderDetail.setTotalRecurringDiscountPercent(OrderDiscountService.calculateOrderLineNetDiscountPercent(recurringOrderLines, chargeMap));

        List<OrderLineItem> nonRecurringOrderLines = OrderServiceHelper.getNonRecurringOrderLineItems(order.getLineItemsNetEffect(), chargeMap);
        orderDetail.setTotalNonRecurringDiscount(OrderDiscountService.getNetDiscountAmountCurrencyScaled(nonRecurringOrderLines, chargeMap));
        orderDetail.setTotalNonRecurringDiscountPercent(OrderDiscountService.calculateOrderLineNetDiscountPercent(nonRecurringOrderLines, chargeMap));
    }

    private void setOrderDetailAccountAndContactFields(OrderDetail orderDetail, Order order) {
        String accountId = order.getAccountId();
        if (StringUtils.isBlank(accountId)) {
            return;
        }

        Account account = accountGetService.getAccount(accountId);

        AccountContact billingContact = order.getBillingContactId() == null ? null : accountGetService.getContact(order.getBillingContactId());
        AccountContact shippingContact = order.getShippingContactId() == null ? null : accountGetService.getContact(order.getShippingContactId());

        AccountJson accountJson = accountMapper.accountToJson(account);

        if (account.getAddressId() != null) {
            Optional<AccountAddress> accountAddress = accountGetService.getAccountAddress(account.getAddressId());
            accountAddress.ifPresent(address -> accountJson.setAddress(accountMapper.accountAddressToJson(address)));
        }

        AccountContactJson billingContactJson = accountMapper.accountContactToJson(billingContact);
        AccountContactJson shippingContactJson = accountMapper.accountContactToJson(shippingContact);

        orderDetail.setAccount(accountJson);
        // fetches partial details of account instead of the whole account details object to speed up orderDetail.
        // anyone depending on the account detail here should take a dependency directly on AccountDataAggregator
        orderDetail.setAccountDetail(accountDetailMapper.accountToAccountDetail(account));
        orderDetail.setBillingContact(billingContactJson);
        orderDetail.setShippingContact(shippingContactJson);
        if (billingContact != null && !accountId.equalsIgnoreCase(billingContact.getAccountId())) {
            Account resellerAccount = accountGetService.getAccount(billingContact.getAccountId());
            AccountJson resellerAccountJson = accountMapper.accountToJson(resellerAccount);

            if (resellerAccount.getAddressId() != null) {
                Optional<AccountAddress> resellerAddress = accountGetService.getAccountAddress(resellerAccount.getAddressId());
                resellerAddress.ifPresent(address -> resellerAccountJson.setAddress(accountMapper.accountAddressToJson(address)));
            }

            orderDetail.setResoldBy(resellerAccountJson);
        }
    }

    private void setOrderDetailUserFields(OrderDetail orderDetail, Order order) {
        Map<String, User> usersByNormalizedEmail = Map.of();

        List<String> rawEmails = new ArrayList<>();
        if (StringUtils.isNotBlank(order.getCreatedBy()) && DataValidation.isEmailValid(order.getCreatedBy())) {
            rawEmails.add(order.getCreatedBy());
        }
        if (StringUtils.isNotBlank(order.getSubmittedBy())) {
            rawEmails.add(order.getSubmittedBy());
        }

        if (!rawEmails.isEmpty()) {
            List<String> normalizedEmails = rawEmails
                .stream()
                .map(email -> StringConverter.toNormalizedEmail(email.trim()))
                .collect(Collectors.toList());
            List<User> users = userService.getUsersOfAnyStatusByEmails(normalizedEmails);

            // Map users by normalized email to avoid lookup mismatches
            usersByNormalizedEmail = users.stream().collect(Collectors.toMap(User::getNormalizedEmail, Function.identity(), (u1, u2) -> u1));
        }

        // Resolve and set CreatedBy
        if (StringUtils.isNotBlank(order.getCreatedBy()) && DataValidation.isEmailValid(order.getCreatedBy())) {
            String normalizedCreatedBy = StringConverter.toNormalizedEmail(order.getCreatedBy().trim());
            User createdByUser = usersByNormalizedEmail.get(normalizedCreatedBy);
            orderDetail.setCreatedBy(userMapper.userToJson(createdByUser));
        }

        if (StringUtils.isNotBlank(order.getOwnerId())) {
            User owner = userService.getUser(order.getOwnerId());
            orderDetail.setOwner(userMapper.userToJson(owner));
        }

        if (StringUtils.isNotBlank(order.getSubmittedBy())) {
            String normalizedSubmittedBy = StringConverter.toNormalizedEmail(order.getSubmittedBy().trim());
            User submittedByUser = usersByNormalizedEmail.get(normalizedSubmittedBy);
            orderDetail.setSubmittedBy(userMapper.userToJson(submittedByUser));
        }
    }

    private void setOrderDetailDocumentTemplateFields(OrderDetail orderDetail, Order order) {
        if (order.getDocumentMasterTemplateId() != null) {
            Optional<DocumentMasterTemplate> optionalDocumentMasterTemplate = documentTemplateGetService.getDocumentMasterTemplateById(
                order.getDocumentMasterTemplateId().toString(),
                Optional.empty()
            );
            optionalDocumentMasterTemplate.ifPresent(orderDetail::setDocumentMasterTemplate);
        }

        List<DocumentTemplate> documentTemplates = getDocumentTemplates(order);
        orderDetail.setOrderFormTemplates(documentTemplateMapper.documentTemplatesToResponse(documentTemplates));
        // todo - conditionally fetch document custom content only when requested (eg: based on gql query fields)

        setOrderDocumentTemplates(orderDetail, documentTemplates);
    }

    private void setOrderDocumentTemplates(OrderDetail orderDetail, List<DocumentTemplate> documentTemplates) {
        List<OrderDocumentTemplate> orderDocumentTemplates = getOrderDocumentTemplates(documentTemplates);
        List<CustomTemplateUpdateOnOrder> modifiedOrderFormsList = StringUtils.isBlank(orderDetail.getId())
            ? List.of()
            : customTemplateUpdatedOnOrderGetService.getUpdatedTemplatesOnOrder(orderDetail.getId());
        Map<String, CustomTemplateUpdateOnOrder> modifiedOrderFormsMap = modifiedOrderFormsList
            .stream()
            .collect(Collectors.toMap(CustomTemplateUpdateOnOrder::getTemplateId, Function.identity()));

        for (OrderDocumentTemplate orderDocumentTemplate : orderDocumentTemplates) {
            if (modifiedOrderFormsMap.containsKey(orderDocumentTemplate.getId())) {
                CustomTemplateUpdateOnOrder modifiedTemplate = modifiedOrderFormsMap.get(orderDocumentTemplate.getId());
                orderDocumentTemplate.setModified(true);
                orderDocumentTemplate.setName(modifiedTemplate.getName());
                orderDocumentTemplate.setDescription(modifiedTemplate.getDescription());
                orderDocumentTemplate.setContent(modifiedTemplate.getContent());
            } else {
                orderDocumentTemplate.setModified(false);
            }
        }

        orderDetail.setOrderDocumentTemplates(orderDocumentTemplates);
    }

    public List<OrderDocumentTemplate> getOrderDocumentTemplates(List<DocumentTemplate> documentTemplates) {
        return documentTemplateMapper.documentTemplatesToOrderDocumentTemplates(documentTemplates);
    }

    private void setOrderDetailRecurringAndNonRecurringDiscounts(OrderDetail orderDetail, Order order, Map<String, Charge> chargeMap) {
        List<OrderLineItem> recurringOrderLineItems = OrderServiceHelper.getRecurringOrderLineItems(order.getLineItems(), chargeMap);
        BigDecimal recurringDiscountPercent = OrderDiscountService.calculateOrderLineNetDiscountPercent(recurringOrderLineItems, chargeMap);
        orderDetail.setRecurringDiscountPercent(recurringDiscountPercent);

        List<OrderLineItem> nonRecurringOrderLineItems = OrderServiceHelper.getNonRecurringOrderLineItems(order.getLineItems(), chargeMap);
        BigDecimal nonRecurringDiscountPercent = OrderDiscountService.calculateOrderLineNetDiscountPercent(nonRecurringOrderLineItems, chargeMap);
        orderDetail.setNonRecurringDiscountPercent(nonRecurringDiscountPercent);
    }

    private void addUserApprovalFlowSegmentDetails(Order order, OrderDetail orderDetail) {
        if (StringUtils.isNotBlank(order.getApprovalSegmentId())) {
            ApprovalSegment approvalSegment = approvalFlowHierarchyService.getApprovalSegmentByApprovalSegmentId(order.getApprovalSegmentId(), true);
            orderDetail.setApprovalSegment(approvalFlowHierarchyMapper.approvalSegmentToJson(approvalSegment));
        }

        if (StringUtils.isNotBlank(order.getOwnerId())) {
            List<ApprovalSegment> userSegments = approvalFlowHierarchyService.getSegmentsForUser(order.getOwnerId());
            orderDetail.setValidApprovalSegments(approvalFlowHierarchyMapper.approvalSegmentsToJson(userSegments));
        }

        if (StringUtils.isBlank(orderDetail.getId())) {
            orderDetail.setApprovalFlows(List.of());
        }
    }

    private void addSubscriptionDetailsToOrderDetail(Order order, OrderDetail orderDetail) {
        if (order.getOrderType() == OrderType.AMENDMENT || order.getOrderType() == OrderType.CANCEL) {
            orderDetail.setCurrentSubscription(
                subscriptionDataAggregator.getSubscriptionDetail(order.getExternalSubscriptionId(), order.getSubscriptionTargetVersion() - 1)
            );
        }

        if (order.getStatus() == OrderStatus.EXECUTED) {
            orderDetail.setUpdatedSubscription(
                subscriptionDataAggregator.getSubscriptionDetail(order.getExternalSubscriptionId(), order.getSubscriptionTargetVersion())
            );
        }

        if (order.getOrderType() == OrderType.RENEWAL) {
            orderDetail.setRenewalForSubscription(
                subscriptionDataAggregator.getSubscriptionDetail(order.getRenewalForSubscriptionId(), order.getRenewalForSubscriptionVersion())
            );
        }
    }

    private void setOrderLineItemDetailsAndPredefinedDiscounts(Order order, OrderJson orderJson, OrderDetail orderDetail) {
        orderDetail.setPredefinedDiscounts(orderDetailsMapper.toOrderDiscountDetailList(order.getPredefinedDiscounts()));

        Map<String, PredefinedDiscountDetail> orderDiscountDetailMap = orderDetail.getPredefinedDiscounts() == null
            ? Map.of()
            : orderDetail.getPredefinedDiscounts().stream().collect(Collectors.toMap(PredefinedDiscountDetail::getId, Function.identity()));

        List<String> chargeIds = orderJson.getLineItems().stream().map(OrderLineItemJson::getChargeId).toList();
        Set<String> planIds = orderJson.getLineItems().stream().map(OrderLineItemJson::getPlanId).collect(Collectors.toSet());

        EntityCache<String, Charge> chargeEntityCache = entityCacheBuilder.getChargeEntityCache(chargeIds);
        EntityCache<String, Plan> planEntityCache = entityCacheBuilder.getPlanEntityCache(planIds);

        orderDetail.setLineItems(getLineItemDetails(orderJson.getLineItems(), orderDiscountDetailMap, chargeEntityCache, planEntityCache));
        orderDetail.setLineItemsNetEffect(
            getLineItemDetails(orderJson.getLineItemsNetEffect(), orderDiscountDetailMap, chargeEntityCache, planEntityCache)
        );
    }

    public List<DocumentTemplate> getDocumentTemplates(Order order) {
        Validator.checkNonNullInternal(order.getOrderFormTemplateIds(), "orderFormTemplateIds cannot be null");
        List<OrderTerms> orderTerms = orderTermsService.getOrderTermsByOrderId(order.getOrderId());
        List<Pair<String, Integer>> templateIdVersionPairs = orderTerms
            .stream()
            .filter(orderTerm -> order.getOrderFormTemplateIds().contains(orderTerm.getTemplateGroupId()))
            .map(orderTerm -> Pair.of(orderTerm.getTemplateGroupId(), orderTerm.getTemplateGroupVersion()))
            .collect(Collectors.toList());
        List<DocumentTemplate> templates = new ArrayList<>(
            documentTemplateGetService.getDocumentTemplatesByTemplateIdVersionPairs(templateIdVersionPairs)
        );
        Set<String> existingTemplateIds = templates.stream().map(DocumentTemplate::getTemplateId).collect(Collectors.toUnmodifiableSet());

        // `Order#getOrderFormTemplateIds` can contain template ids that are not present in the `templates` list. e.g. during dry run
        // fetch the missing templates and add them to the list
        List<String> missingTemplateIds = order
            .getOrderFormTemplateIds()
            .stream()
            .filter(templateId -> !existingTemplateIds.contains(templateId))
            .collect(Collectors.toList());
        if (!missingTemplateIds.isEmpty()) {
            List<DocumentTemplate> missingTemplates = documentTemplateGetService.getDocumentTemplatesByTemplateIds(missingTemplateIds);
            templates.addAll(missingTemplates);
        }

        return templates.stream().sorted(Comparator.comparing(DocumentTemplate::getName, String.CASE_INSENSITIVE_ORDER)).collect(Collectors.toList());
    }

    public Map<String, PredefinedDiscountDetail> getDiscountsMapForLineItems(List<OrderLineItem> lineItems) {
        return lineItems
            .stream()
            .map(OrderLineItem::getPredefinedDiscounts)
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(List::stream)
            .map(orderDetailsMapper::lineItemDiscountToPredefinedDiscount)
            .collect(Collectors.toMap(PredefinedDiscountDetail::getId, Function.identity(), (d1, d2) -> d1));
    }

    public List<OrderLineItemDetail> getLineItemDetails(
        List<OrderLineItemJson> orderLineItems,
        Map<String, PredefinedDiscountDetail> orderDiscountDetailMap
    ) {
        if (CollectionUtils.isEmpty(orderLineItems)) {
            return List.of();
        }

        List<String> chargeIds = orderLineItems.stream().map(OrderLineItemJson::getChargeId).toList();
        Set<String> planIds = orderLineItems.stream().map(OrderLineItemJson::getPlanId).collect(Collectors.toSet());

        EntityCache<String, Charge> chargeEntityCache = entityCacheBuilder.getChargeEntityCache(chargeIds);
        EntityCache<String, Plan> planEntityCache = entityCacheBuilder.getPlanEntityCache(planIds);

        return getLineItemDetails(orderLineItems, orderDiscountDetailMap, chargeEntityCache, planEntityCache);
    }

    private List<OrderLineItemDetail> getLineItemDetails(
        List<OrderLineItemJson> orderLineItems,
        Map<String, PredefinedDiscountDetail> orderDiscountDetailMap,
        EntityCache<String, Charge> chargeEntityCache,
        EntityCache<String, Plan> planEntityCache
    ) {
        if (CollectionUtils.isEmpty(orderLineItems)) {
            return List.of();
        }
        List<OrderLineItemDetail> orderLineItemDetails = new ArrayList<>();

        List<String> chargeIds = orderLineItems.stream().map(OrderLineItemJson::getChargeId).distinct().toList();

        // note: chargeEntityCache should be pre-populated with these charges already, so no DB query is expected
        List<Charge> charges = chargeIds.stream().map(chargeEntityCache::get).toList();
        List<ChargeDetail> chargeDetails = chargeDataAggregator.getChargeDetails(charges);
        Map<String, ChargeDetail> chargeDetailMap = chargeDetails.stream().collect(Collectors.toMap(ChargeDetail::getId, Function.identity()));

        for (OrderLineItemJson orderLineItem : orderLineItems) {
            String chargeId = orderLineItem.getChargeId();
            OrderLineItemDetail orderLineItemDetail = orderDetailsMapper.orderLineItemJsonToDetail(orderLineItem);
            Charge charge = chargeEntityCache.get(chargeId);
            ChargeJson chargeJson = planMapper.chargeToJson(charge);
            ChargeDetail chargeDetail = chargeDetailMap.get(chargeId);
            orderLineItemDetail.setCharge(chargeJson);
            orderLineItemDetail.setChargeDetail(chargeDetail);
            orderLineItemDetail.setRecognitionRule(chargeDetail.getRecognitionRule());
            PlanJson planJson = planMapper.planToJson(planEntityCache.get(orderLineItem.getPlanId()));
            orderLineItemDetail.setPlan(planJson);
            populateOrderDiscountLineItems(orderLineItem, orderLineItemDetail, orderDiscountDetailMap);
            populatePlanReplacements(planEntityCache, orderLineItem, orderLineItemDetail, planJson);
            orderLineItemDetails.add(orderLineItemDetail);
        }

        return orderLineItemDetails;
    }

    private void populatePlanReplacements(
        EntityCache<String, Plan> planEntityCache,
        OrderLineItemJson orderLineItem,
        OrderLineItemDetail orderLineItemDetail,
        PlanJson planJson
    ) {
        if (StringUtils.isNotBlank(orderLineItem.getReplacedPlanId())) {
            Plan replacedPlan = planEntityCache.get(orderLineItem.getReplacedPlanId());
            PlanJson replacedPlanJson = planMapper.planToJson(replacedPlan);
            orderLineItemDetail.setReplacedPlan(replacedPlanJson);
        }
        if (CollectionUtils.isNotEmpty(planJson.getReplacementPlanIds())) {
            List<String> replacementPlanIds = planDataAggregator.getReplacementPlanIds(planJson.getId());
            Validator.checkStateInternal(CollectionUtils.isNotEmpty(replacementPlanIds), "replacement plan ids cannot be empty here");
            PlanJson availableReplacementPlanJson = planMapper.planToJson(planEntityCache.get(replacementPlanIds.get(replacementPlanIds.size() - 1)));
            orderLineItemDetail.setAvailableReplacementPlan(availableReplacementPlanJson);
        }
    }

    private void populateOrderDiscountLineItems(
        OrderLineItemJson orderLineItem,
        OrderLineItemDetail orderLineItemDetail,
        Map<String, PredefinedDiscountDetail> orderDiscountDetailMap
    ) {
        if (CollectionUtils.isEmpty(orderLineItem.getPredefinedDiscounts())) {
            return;
        }

        List<PredefinedDiscountLineItemDetail> discountLineItemDetails = new ArrayList<>();
        orderLineItem
            .getPredefinedDiscounts()
            .forEach(discount -> populateOrderDiscountLineItem(orderDiscountDetailMap, discountLineItemDetails, discount));

        orderLineItemDetail.setPredefinedDiscounts(discountLineItemDetails);
    }

    private void populateOrderDiscountLineItem(
        Map<String, PredefinedDiscountDetail> orderDiscountDetailMap,
        List<PredefinedDiscountLineItemDetail> discountLineItemDetails,
        TenantDiscountLineItemJson discount
    ) {
        var orderDiscountLineItemDetail = orderDetailsMapper.toOrderDiscountLineItemDetail(discount);
        PredefinedDiscountDetail predefinedDiscountDetail = orderDiscountDetailMap.get(discount.getId());
        if (Objects.nonNull(predefinedDiscountDetail)) {
            orderDiscountLineItemDetail.setName(predefinedDiscountDetail.getName());
            orderDiscountLineItemDetail.setType(predefinedDiscountDetail.getType());
            orderDiscountLineItemDetail.setDescription(predefinedDiscountDetail.getDescription());
            orderDiscountLineItemDetail.setStatus(predefinedDiscountDetail.getStatus());
        }
        discountLineItemDetails.add(orderDiscountLineItemDetail);
    }

    public List<OrderLineItem> getExecutedLineItemsBySubscriptionId(String subscriptionId) {
        if (StringUtils.isBlank(subscriptionId)) {
            return List.of();
        }
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);
        return orders.stream().map(Order::getLineItemsNetEffect).flatMap(List::stream).collect(Collectors.toList());
    }

    public void setOrderLineMetrics(OrderDetail orderDetail, Order order) {
        if (!featureService.isEnabled(Feature.ORDER_LINE_ARR_OVERRIDE)) {
            return;
        }

        Map<String, LineItemMetrics> orderLineMetrics = metricsService.getLineItemMetricsForAllOrderLines(order);

        for (var li : orderDetail.getLineItems()) {
            if (!orderLineMetrics.containsKey(li.getId())) {
                throw new InvariantCheckFailedException(String.format("line item metrics for line item with id %s are missing", li.getId()));
            }
            LineItemMetrics metrics = orderLineMetrics.get(li.getId());
            li.setMetrics(metrics);
        }

        for (var li : orderDetail.getLineItemsNetEffect()) {
            if (!orderLineMetrics.containsKey(li.getId())) {
                throw new InvariantCheckFailedException(String.format("line item metrics for line item with id %s are missing", li.getId()));
            }
            LineItemMetrics metrics = orderLineMetrics.get(li.getId());
            li.setMetrics(metrics);
        }
    }
}
