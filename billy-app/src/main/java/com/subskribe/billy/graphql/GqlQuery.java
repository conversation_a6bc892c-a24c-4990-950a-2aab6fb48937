package com.subskribe.billy.graphql;

import static com.subskribe.billy.resources.shared.PaginationQueryParams.DEFAULT_PAGINATION_QUERY_PARAMS;
import static com.subskribe.billy.resources.shared.PaginationQueryParams.MAX_PAGINATION_LIMIT;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.crm.contact.CrmContact;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.admin.model.InvoiceDeletableResponse;
import com.subskribe.billy.admin.model.SubscriptionModifiableResponse;
import com.subskribe.billy.alias.model.SubscriptionChargeAlias;
import com.subskribe.billy.approvalflow.model.ApprovalFlow;
import com.subskribe.billy.approvalflowhierarchy.model.ApprovalRole;
import com.subskribe.billy.approvalflowhierarchy.model.ApprovalSegment;
import com.subskribe.billy.approvalflowinstance.model.PendingOrderApprovalFlow;
import com.subskribe.billy.attachments.model.Attachment;
import com.subskribe.billy.auth.model.ApiKeyDetail;
import com.subskribe.billy.auth.model.UserAuthInfo;
import com.subskribe.billy.auth.model.UserPrincipal;
import com.subskribe.billy.avalara.model.AvalaraIntegration;
import com.subskribe.billy.compositeorder.model.CancelAndRestructureOrderDetail;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.model.CompositeOrderDetail;
import com.subskribe.billy.configuration.SystemSettings;
import com.subskribe.billy.configuration.SystemSettingsConstructor;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureSetting;
import com.subskribe.billy.currency.model.CurrencyJson;
import com.subskribe.billy.customfield.model.CustomFieldDefinition;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.customization.SelectionCustomizationInput;
import com.subskribe.billy.customization.model.Action;
import com.subskribe.billy.customization.model.PlanAdditionCustomizationInput;
import com.subskribe.billy.customization.model.ui.UIConfiguration;
import com.subskribe.billy.dataimport.model.DataImport;
import com.subskribe.billy.dataimport.model.flatfile.FlatfileSpace;
import com.subskribe.billy.dataimport.model.flatfile.FlatfileSpaceResponse;
import com.subskribe.billy.discount.model.Discount;
import com.subskribe.billy.document.model.DocumentLink;
import com.subskribe.billy.docusign.model.DocuSignIntegration;
import com.subskribe.billy.docusign.model.DocuSignIntegrationResponseJson;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.email.model.EmailLog;
import com.subskribe.billy.email.model.EmailLogsResponse;
import com.subskribe.billy.email.model.EmailSetting;
import com.subskribe.billy.entity.graphql.EntityResolver;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.model.EntityJson;
import com.subskribe.billy.erp.model.ErpInvoiceJson;
import com.subskribe.billy.escalationpolicy.model.EscalationPolicy;
import com.subskribe.billy.esign.model.ElectronicSignatureProvider;
import com.subskribe.billy.esign.model.EsignRequest;
import com.subskribe.billy.esign.model.EsignTenantSignatoryDetail;
import com.subskribe.billy.esign.model.ImmutableElectronicSignatureAuditLog;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.TenantNotFoundException;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.model.CurrencyTypeSetting;
import com.subskribe.billy.graphql.account.AccountContactDetail;
import com.subskribe.billy.graphql.account.AccountDetail;
import com.subskribe.billy.graphql.approvalflow.ApprovalFlowDetail;
import com.subskribe.billy.graphql.approvalflowinstance.ApprovalFlowInstanceGroupDetail;
import com.subskribe.billy.graphql.clm.WebOrderFormDetail;
import com.subskribe.billy.graphql.creditmemo.CreditMemoDetail;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.customfield.CustomFieldWithParentReference;
import com.subskribe.billy.graphql.esign.ElectronicSignatureDetail;
import com.subskribe.billy.graphql.invoice.BillingEvents;
import com.subskribe.billy.graphql.invoice.InvoiceDetail;
import com.subskribe.billy.graphql.invoice.InvoiceItemDetail;
import com.subskribe.billy.graphql.invoicedunning.InvoiceDunningEmailDetail;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.order.quotebuilder.AnswerInput;
import com.subskribe.billy.graphql.payment.FailedPaymentDetail;
import com.subskribe.billy.graphql.payment.PaymentDetail;
import com.subskribe.billy.graphql.payment.PaymentInAccountDetail;
import com.subskribe.billy.graphql.payment.PaymentMethodDetail;
import com.subskribe.billy.graphql.payment.RefundPreviewDetail;
import com.subskribe.billy.graphql.payment.SettlementApplicationDetail;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.graphql.productcatalog.PlanDetail;
import com.subskribe.billy.graphql.productcatalog.PlanMinimal;
import com.subskribe.billy.graphql.productcatalog.ProductDetail;
import com.subskribe.billy.graphql.subscription.SubscriptionChargeDetail;
import com.subskribe.billy.graphql.subscription.SubscriptionChargeRecognition;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.graphql.template.CustomPredefinedTemplateOnOrder;
import com.subskribe.billy.graphql.template.DocumentTemplateResponse;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.graphql.tenant.TenantDetails;
import com.subskribe.billy.graphql.tenant.settings.dunningsetting.DunningSettingDetails;
import com.subskribe.billy.graphql.tenant.settings.dunningsetting.DunningSettingDetailsMapper;
import com.subskribe.billy.graphql.usage.AggregatedUsage;
import com.subskribe.billy.hubspot.model.HubSpotIntegration;
import com.subskribe.billy.hubspot.model.HubSpotSetupMessage;
import com.subskribe.billy.integration.IntegrationTargetService;
import com.subskribe.billy.integration.model.Integration;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRun;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunItem;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunSelector;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoicedunning.model.DunningReminderType;
import com.subskribe.billy.invoicedunning.model.InvoiceDunning;
import com.subskribe.billy.invoicesettlement.model.Refund;
import com.subskribe.billy.invoicesettlement.model.RefundPreview;
import com.subskribe.billy.looker.model.LookerUserMapping;
import com.subskribe.billy.notification.gql.NotificationInstanceWithAttempts;
import com.subskribe.billy.notification.model.NotificationProcessorTypeSupport;
import com.subskribe.billy.notification.model.NotificationTargetAndSubscriptions;
import com.subskribe.billy.opportunity.model.Opportunity;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.quotebuilder.model.Answer;
import com.subskribe.billy.payment.model.AccountPaymentLinkGqlResponse;
import com.subskribe.billy.payment.model.PaymentConfiguration;
import com.subskribe.billy.payment.model.PaymentLinkGqlResponse;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.platformfeature.model.EnabledPlatformFeature;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.productcatalog.model.TaxRate;
import com.subskribe.billy.productcatalog.model.UnitOfMeasure;
import com.subskribe.billy.productcatalog.plangroup.model.gql.PlanGroupInterface;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.ChargeDefaultAttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import com.subskribe.billy.proposal.model.Proposal;
import com.subskribe.billy.report.model.ChartDataValue;
import com.subskribe.billy.report.model.ChartDataset;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.accountreceivablecontact.AccountReceivableContactJson;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalRoleJson;
import com.subskribe.billy.resources.json.approvalhierarchy.ApprovalSegmentJson;
import com.subskribe.billy.resources.json.customfield.CustomFieldDefinitionJson;
import com.subskribe.billy.resources.json.discount.DiscountJson;
import com.subskribe.billy.resources.json.entity.EntityJsonMapper;
import com.subskribe.billy.resources.json.invoicesettlement.RefundDetail;
import com.subskribe.billy.resources.json.opportunity.OpportunityJson;
import com.subskribe.billy.resources.json.order.OrderInvoicePreviewResolver;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.payment.PaymentBankAccountJson;
import com.subskribe.billy.resources.json.paymenttermsettings.PaymentTermSettingsJson;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.report.PredefinedReportDefsJson;
import com.subskribe.billy.resources.json.taxrate.TaxRateJson;
import com.subskribe.billy.resources.json.tenant.TenantJson;
import com.subskribe.billy.resources.json.tenant.UserJson;
import com.subskribe.billy.resources.json.tenantsetting.TenantSettingJson;
import com.subskribe.billy.resources.json.uom.UnitOfMeasureJson;
import com.subskribe.billy.resources.json.usergroup.UserGroupDetail;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.salesforce.model.SalesforceIntegrationResponse;
import com.subskribe.billy.salesforce.model.account.SalesforceAccount;
import com.subskribe.billy.salesforce.model.contact.SubskribeSalesforceContact;
import com.subskribe.billy.salesroom.model.SalesRoom;
import com.subskribe.billy.salesroom.model.SalesRoomLink;
import com.subskribe.billy.settings.billingcycle.json.BillingCycleDefinitionJson;
import com.subskribe.billy.shared.enums.AccountType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.enums.SigningOrder;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.utility.BillyStringUtils;
import com.subskribe.billy.taxjar.model.TaxJarIntegration;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.model.DocumentSection;
import com.subskribe.billy.template.model.DocumentTemplate;
import com.subskribe.billy.tenant.model.Tenant;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.time.TimeZoneData;
import com.subskribe.billy.time.TimeZoneService;
import com.subskribe.billy.usage.model.UsageAggregateOutput;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.usergroup.model.UserGroup;
import com.subskribe.billy.validation.Validator;
import graphql.annotations.annotationTypes.GraphQLDeprecate;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import graphql.schema.DataFetchingEnvironment;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

@SuppressWarnings({ "unused", "deprecation" })
public class GqlQuery {

    private static final Logger LOGGER = LoggerFactory.getLogger(GqlQuery.class);

    private static final String ADDRESS_KEY = "address";

    @GraphQLField
    @GraphQLNonNull
    public static TenantDetails tenantDetails(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Tenant tenant = gqlAuthContext.getCurrentTenant();
        return gqlAuthContext.getTenantDetailsMapper().tenantToTenantInfo(tenant);
    }

    @GraphQLField
    @GraphQLNonNull
    // todo: can this be removed after UI switches to use tenantDetail?
    public static TenantJson currentTenant(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Tenant tenant = gqlAuthContext.getCurrentTenant();
        return gqlAuthContext.getTenantMapper().tenantToJson(tenant);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull TenantJson> tenants(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var tenants = gqlAuthContext.getTenants();
        return gqlAuthContext.getTenantMapper().tenantsToJson(tenants);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull ProductDetail> products(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getProductDetailsList();
    }

    @GraphQLField
    @GraphQLNonNull
    public static ProductDetail product(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String productId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getProductDetail(productId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull ProductCategory> productCategories(DataFetchingEnvironment env, @GraphQLName("id") String productCategoryId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        if (productCategoryId != null) {
            var category = gqlAuthContext.getProductCategory(productCategoryId);
            return optionalToList(Optional.of(category));
        }

        return gqlAuthContext.getProductCategories(DEFAULT_PAGINATION_QUERY_PARAMS);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ChargeDetail chargeDetail(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("planId") String planId,
        @GraphQLNonNull @GraphQLName("chargeId") String chargeId
    ) {
        Validator.validateStringNotBlank(planId, "planId cannot be blank");
        Validator.validateStringNotBlank(chargeId, "chargeId cannot be blank");

        GqlAuthContext gqlAuthContext = env.getContext();

        Charge charge = gqlAuthContext.getCharge(planId, chargeId);

        return gqlAuthContext.getChargeDetail(charge);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanDetail planDetail(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String planId) {
        Validator.validateStringNotBlank(planId, "id cannot be blank");
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getPlanDetail(planId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static RateCard rateCard(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String rateCardId,
        @GraphQLName("targetCurrencyCode") String targetCurrencyCode,
        @GraphQLName("effectiveDate") Long effectiveDate
    ) {
        if (StringUtils.isBlank(rateCardId)) {
            throw new InvalidInputException("id cannot be blank when fetching rate card");
        }
        GqlAuthContext gqlAuthContext = env.getContext();
        if (StringUtils.isBlank(targetCurrencyCode)) {
            return gqlAuthContext.getRateCard(rateCardId);
        }
        Instant currencyConversionEffectiveDate = Objects.nonNull(effectiveDate) ? Instant.ofEpochSecond(effectiveDate) : Instant.now();
        return gqlAuthContext.getConvertedRateCardAsOfDate(rateCardId, targetCurrencyCode, currencyConversionEffectiveDate);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull RateCard> rateCards(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getRateCards();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull PriceAttribute> priceAttributes(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getPriceAttributes();
    }

    @GraphQLField
    @GraphQLNonNull
    public static PlanGroupInterface planGroup(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String planGroupId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getPlanGroup(planGroupId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static Proposal proposal(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String proposalId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getProposal(proposalId);
    }

    // this call is just for demo purposes, do not use otherwise
    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull PlanMinimal> planMinimals(
        DataFetchingEnvironment env,
        @GraphQLName("status") PlanStatus status,
        @GraphQLName("accountId") String accountId,
        @GraphQLName("orderCurrencyCode") String orderCurrencyCode
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getPlanMinimals(Optional.ofNullable(status), Optional.ofNullable(accountId), Optional.ofNullable(orderCurrencyCode));
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull PlanJson> plans(
        DataFetchingEnvironment env,
        @GraphQLName("productId") String productId,
        @GraphQLName("status") PlanStatus planStatus,
        @GraphQLName("id") String planId,
        @GraphQLName("currency") String currencyCode
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var planMapper = gqlAuthContext.getPlanMapper();

        if (planId != null) {
            var plan = gqlAuthContext.getPlan(planId);
            return planMapper.plansToJson(List.of(plan));
        }

        return planMapper.plansToJson(
            gqlAuthContext.getPlans(
                productId,
                Optional.ofNullable(planStatus),
                Optional.empty(),
                BillyStringUtils.optionalIfNotBlank(currencyCode),
                DEFAULT_PAGINATION_QUERY_PARAMS,
                true
            )
        );
    }

    @GraphQLField
    @GraphQLNonNull
    public static EntityJson entity(DataFetchingEnvironment env, @GraphQLName("id") String entityId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        EntityJsonMapper entityJsonMapper = gqlAuthContext.getEntityJsonMapper();
        Entity entity = gqlAuthContext.getEntity(entityId);
        return entityJsonMapper.toJson(entity);
    }

    @GraphQLField
    @GraphQLNonNull
    public static EntityResolver entityResolver(DataFetchingEnvironment env, @GraphQLName("id") String entityId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getEntityResolver(entityId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull EntityJson> entities(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        EntityJsonMapper entityJsonMapper = gqlAuthContext.getEntityJsonMapper();
        List<Entity> entities = gqlAuthContext.getEntities();
        return entityJsonMapper.toJsonList(entities);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountDetail accountDetail(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String accountId) {
        GqlAuthContext context = env.getContext();
        return context.getAccountDetail(accountId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull AccountJson> accounts(
        DataFetchingEnvironment env,
        @GraphQLName("id") String id,
        @GraphQLName("type") String type
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var accountMapper = gqlAuthContext.getAccountMapper();

        if (id != null) {
            var account = gqlAuthContext.getAccount(id);
            return accountMapper.accountsToJson(optionalToList(Optional.of(account)));
        } else {
            AccountType accountType = StringUtils.isBlank(type)
                ? AccountType.ALL
                : Validator.enumFromString(type.toUpperCase(), AccountType.class, "type");
            var accounts = gqlAuthContext.getAccounts(accountType, DEFAULT_PAGINATION_QUERY_PARAMS);
            return accountMapper.accountsToJson(accounts);
        }
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull AccountDetail> accountDetails(
        DataFetchingEnvironment env,
        @GraphQLName("id") String id,
        @GraphQLName("type") AccountType accountType
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        if (StringUtils.isNotBlank(id)) {
            AccountDetail account = gqlAuthContext.getAccountDetail(id);
            return List.of(account);
        }
        if (accountType == null) {
            accountType = AccountType.ALL;
        }
        return gqlAuthContext.getAccountDetails(accountType, DEFAULT_PAGINATION_QUERY_PARAMS);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountContactDetail accountContactDetail(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId,
        @GraphQLNonNull @GraphQLName("id") String id
    ) {
        if (StringUtils.isBlank(accountId) || StringUtils.isBlank(id)) {
            throw new IllegalArgumentException("accountId and id cannot be blank");
        }
        GqlAuthContext context = env.getContext();
        return context.getAccountContactDetail(id);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull AccountContactJson> accountContacts(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId,
        @GraphQLName("id") String id
    ) {
        GqlAuthContext context = env.getContext();
        var accountMapper = context.getAccountMapper();

        boolean expand = (env.getSelectionSet().getFields(ADDRESS_KEY) != null);
        if (id != null) {
            var accountContact = context.getAccountContact(id);
            return accountMapper.accountContactsToJson(optionalToList(Optional.of(accountContact)));
        } else {
            var accountContacts = context.getAccountContacts(accountId, expand);
            return accountMapper.accountContactsToJson(accountContacts);
        }
    }

    @GraphQLField
    @GraphQLNonNull
    public static SubscriptionModifiableResponse subscriptionModifiable(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId
    ) {
        if (StringUtils.isBlank(subscriptionId)) {
            throw new InvalidInputException("id cannot be blank");
        }
        GqlAuthContext context = env.getContext();
        return context.subscriptionModifiable(subscriptionId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static SubscriptionModifiableResponse subscriptionReversible(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId
    ) {
        if (StringUtils.isBlank(subscriptionId)) {
            throw new InvalidInputException("id cannot be blank");
        }
        GqlAuthContext context = env.getContext();
        return context.subscriptionReversible(subscriptionId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static InvoiceDeletableResponse canDeleteInvoice(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber
    ) {
        if (StringUtils.isBlank(invoiceNumber)) {
            throw new InvalidInputException("invoiceNumber cannot be blank");
        }
        GqlAuthContext context = env.getContext();
        return context.canDeleteInvoice(invoiceNumber);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull SubscriptionDetail> subscriptions(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String subscriptionId
    ) {
        if (StringUtils.isBlank(subscriptionId)) {
            throw new IllegalArgumentException("id cannot be blank");
        }
        GqlAuthContext context = env.getContext();
        var subscription = context.getSubscriptionDetail(subscriptionId);
        // TODO: why are we returning a list? is subscription not unique by subscriptionId?
        //   resource returns a single subscription json
        return List.of(subscription);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail orderDetail(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String orderId) {
        if (StringUtils.isBlank(orderId)) {
            throw new IllegalArgumentException("id cannot be blank");
        }
        GqlAuthContext context = env.getContext();
        OrderDetail orderDetail = context.getOrderDetail(orderId, false, false);
        context.addCustomFieldsToOrderLineItems(orderDetail);
        return orderDetail;
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull OrderJson> orders(
        DataFetchingEnvironment env,
        @GraphQLName("accountId") String accountId,
        @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLName("id") String orderId,
        @GraphQLName("limit") Integer limit
    ) {
        GqlAuthContext context = env.getContext();
        List<Order> orders = getOrders(accountId, subscriptionId, orderId, limit, context);
        return context.getOrderMapper().ordersToJson(orders);
    }

    private static List<Order> getOrders(String accountId, String subscriptionId, String orderId, Integer limit, GqlAuthContext context) {
        if (StringUtils.isNotBlank(accountId)) {
            return context.getOrdersByAccountId(accountId);
        }

        if (StringUtils.isNotBlank(subscriptionId)) {
            return context.getOrdersBySubscriptionId(subscriptionId);
        }

        if (StringUtils.isNotBlank(orderId)) {
            Order order = context.getOrderByOrderId(orderId);
            return List.of(order);
        }

        Integer paginationLimit = Objects.requireNonNullElse(limit, MAX_PAGINATION_LIMIT);
        PaginationQueryParams paginationQueryParams = new PaginationQueryParams(null, paginationLimit);
        // TODO: Envelope response object?
        return context.getOrders(paginationQueryParams);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull OrderJson> ordersByCrmOpportunityId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("crmOpportunityId") String crmOpportunityId
    ) {
        GqlAuthContext context = env.getContext();
        List<Order> orders = context.getOrdersByCrmOpportunityId(crmOpportunityId);
        return context.getOrderMapper().ordersToJson(orders);
    }

    private static List<ChartDataset> mapOrdersToChartDataset(GqlAuthContext context, Map<String, List<Order>> ordersByCurrency) {
        List<ChartDataset> datasets = new ArrayList<>();
        ordersByCurrency.forEach((currency, ordersList) -> {
            List<OrderJson> orderJsons = context.getOrderMapper().ordersToJson(ordersList);
            List<ChartDataValue> values = orderJsons
                .stream()
                .map(orderJson ->
                    new ChartDataValue(
                        new BigDecimal(orderJson.getExecutedOn()),
                        orderJson.getTotalAmount(),
                        orderJson.getExecutedOnFormatted(),
                        null
                    )
                )
                .collect(Collectors.toList());
            datasets.add(new ChartDataset(currency, values));
        });
        return datasets;
    }

    // Orders returned are aggregated by date
    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull ChartDataset> ordersOverviewByDate(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("from") Long from,
        @GraphQLNonNull @GraphQLName("to") Long to
    ) {
        GqlAuthContext context = env.getContext();
        TenantSetting tenantSetting = context.getTenantSetting();
        ZoneId zoneId = tenantSetting.getDefaultTimeZone().toZoneId();

        Period period = Period.between(from, to);
        Map<String, List<Order>> orders = context.getOrdersOverviewInPeriod(period, zoneId);

        return mapOrdersToChartDataset(context, orders);
    }

    private static List<ChartDataset> mapOpenInvoiceBalancesToChartDataset(GqlAuthContext context, Map<String, List<BigDecimal>> invoiceBalances) {
        List<ChartDataset> datasets = new ArrayList<>();
        invoiceBalances.forEach((currency, invoiceBalancesList) -> {
            BigDecimal invoiceBalanceTotal = BigDecimal.ZERO;
            for (BigDecimal invoiceBalance : invoiceBalancesList) {
                invoiceBalanceTotal = invoiceBalanceTotal.add(invoiceBalance);
            }
            ChartDataValue invoiceBalanceTotalValue = new ChartDataValue(null, invoiceBalanceTotal, "", null);

            String datasetLabel = String.format("%s (%d)", currency, invoiceBalancesList.size());
            datasets.add(new ChartDataset(datasetLabel, List.of(invoiceBalanceTotalValue)));
        });
        return datasets;
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull ChartDataset> openInvoicesReportOverview(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();

        Map<String, List<BigDecimal>> invoiceBalances = context.getOpenInvoicesReportOverview();
        return mapOpenInvoiceBalancesToChartDataset(context, invoiceBalances);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull DiscountJson> discounts(DataFetchingEnvironment env, @GraphQLName("discountId") String discountId) {
        GqlAuthContext gqlAuthContext = env.getContext();

        if (StringUtils.isBlank(discountId)) {
            List<Discount> discounts = gqlAuthContext.getDiscounts();

            return gqlAuthContext.getDiscountJsonMapper().toJson(discounts);
        }

        Discount discount = gqlAuthContext.getDiscount(discountId, false);

        return gqlAuthContext.getDiscountJsonMapper().toJson(List.of(discount));
    }

    @GraphQLField
    @GraphQLNonNull
    public static Boolean isAmendmentCurrent(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext context = env.getContext();
        return context.isAmendmentCurrent(orderId);
    }

    @GraphQLField
    @GraphQLNonNull
    // TODO: this should be a mutation
    public static OrderDetail generateAmendment(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var order = gqlAuthContext.generateDraftAmendment(subscriptionId);
        OrderDetail orderDetail = gqlAuthContext.getOrderDetail(order, true, false);
        gqlAuthContext.hydrateOrderAndOrderLineCustomFieldsForNewChangeOrder(orderDetail);
        return orderDetail;
    }

    @GraphQLField
    @GraphQLNonNull
    // TODO: this should be a mutation
    public static OrderDetail generateCancelOrder(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLName("effectiveDate") Long effectiveDate
    ) {
        var cancelDate = Optional.ofNullable(effectiveDate).map(Instant::ofEpochSecond);
        GqlAuthContext gqlAuthContext = env.getContext();
        var order = gqlAuthContext.generateCancelOrder(subscriptionId, cancelDate);
        return gqlAuthContext.getOrderDetail(order, true, true);
    }

    @GraphQLField
    @GraphQLNonNull
    // TODO: this should be a mutation
    public static OrderDetail generateRenewalOrder(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var order = gqlAuthContext.generateRenewalOrder(subscriptionId);
        OrderDetail orderDetail = gqlAuthContext.getOrderDetail(order, true, false);
        gqlAuthContext.addCustomFieldsToOrderLineItems(orderDetail);
        return orderDetail;
    }

    @GraphQLField
    @GraphQLNonNull
    // TODO: this should be a mutation
    public static OrderDetail duplicateOrder(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("orderId") String orderId,
        // account id is optional
        // the order account and shipping and billing contact will be reset
        // when account id is provided
        @GraphQLName("accountId") String accountId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var order = gqlAuthContext.duplicateOrder(orderId, accountId);
        OrderDetail orderDetail = gqlAuthContext.getOrderDetail(order, true, false);
        gqlAuthContext.addCustomFieldsToOrderLineItems(orderDetail);
        return orderDetail;
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderDetail buildDryRunOrderFromAnswers(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId,
        @GraphQLNonNull @GraphQLName("usecaseId") String usecaseId,
        @GraphQLNonNull @GraphQLName("answers") List<@GraphQLNonNull AnswerInput> answersInput
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        List<Answer> answers = CollectionUtils.isEmpty(answersInput) ? List.of() : answersInput.stream().map(AnswerInput::toModelAnswer).toList();
        var order = gqlAuthContext.buildOrderFromAnswers(accountId, usecaseId, answers);
        OrderDetail orderDetail = gqlAuthContext.getOrderDetail(order, true, false);
        gqlAuthContext.hydrateCustomFieldsForOrderLineItems(orderDetail);
        return orderDetail;
    }

    @GraphQLField
    @GraphQLNonNull
    public static InvoiceDetail invoice(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber) {
        GqlAuthContext context = env.getContext();
        return context.getInvoiceDetail(new Invoice.Number(invoiceNumber));
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull TaxRateJson> taxRates(DataFetchingEnvironment env, @GraphQLName("id") String taxRateId) {
        GqlAuthContext context = env.getContext();
        if (StringUtils.isBlank(taxRateId)) {
            List<TaxRate> taxRates = context.getTaxRates(DEFAULT_PAGINATION_QUERY_PARAMS);
            return context.getTaxRateJsonMapper().toTaxRatesJson(taxRates);
        } else {
            TaxRate taxRate = context.getTaxRate(UUID.fromString(taxRateId));
            TaxRateJson json = context.getTaxRateJsonMapper().toTaxRateJson(taxRate);
            return List.of(json);
        }
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull SettlementApplicationDetail> getInvoiceSettlementApplications(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumberString
    ) {
        if (StringUtils.isBlank(invoiceNumberString)) {
            throw new IllegalArgumentException("invoiceNumber is required");
        }

        GqlAuthContext context = env.getContext();
        var invoiceNumber = new Invoice.Number(invoiceNumberString);
        return context.getSettlementApplicationDetails(invoiceNumber);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentDetail payment(DataFetchingEnvironment env, @GraphQLName("id") @GraphQLNonNull String paymentId) {
        GqlAuthContext context = env.getContext();
        return context.getPaymentDetail(paymentId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentBankAccountJson paymentBankAccount(
        DataFetchingEnvironment env,
        @GraphQLName("id") @GraphQLNonNull String paymentBankAccountId
    ) {
        GqlAuthContext context = env.getContext();
        return context.getPaymentBankAccount(paymentBankAccountId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull LedgerAccount> ledgerAccountsForPaymentBankAccount(
        DataFetchingEnvironment env,
        @GraphQLName("id") String paymentBankAccountId
    ) {
        GqlAuthContext context = env.getContext();
        return context.getLedgerAccountsForPaymentBankAccount(paymentBankAccountId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull PaymentBankAccountJson> applicablePaymentBankAccountsForInvoicePayment(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber
    ) {
        GqlAuthContext context = env.getContext();
        return context.getApplicablePaymentBankAccountsForInvoicePayment(invoiceNumber);
    }

    @GraphQLField
    @GraphQLNonNull
    @GraphQLDeprecate
    public static List<@GraphQLNonNull PaymentDetail> getPayment(
        DataFetchingEnvironment env,
        @GraphQLName("id") String paymentId,
        @GraphQLName("accountId") String accountId
    ) {
        GqlAuthContext context = env.getContext();
        if (null != paymentId) {
            return List.of(context.getPaymentDetail(paymentId));
        }

        return context
            .getPaymentsByAccountId(accountId)
            .stream()
            .map(payment -> context.getPaymentDetail(payment.getPaymentId()))
            .collect(Collectors.toList());
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull PaymentInAccountDetail> paymentsInAccountDetail(
        DataFetchingEnvironment env,
        @GraphQLName("id") String paymentId,
        @GraphQLName("accountId") String accountId
    ) {
        GqlAuthContext context = env.getContext();
        if (null != paymentId) {
            return List.of(context.getPaymentInAccountDetail(context.getPayment(paymentId)));
        }

        return context.getPaymentsByAccountId(accountId).stream().map(context::getPaymentInAccountDetail).collect(Collectors.toList());
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull PaymentMethodDetail> paymentMethods(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId
    ) {
        GqlAuthContext context = env.getContext();
        return context.getPaymentMethodDetails(accountId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull UnitOfMeasureJson> unitsOfMeasure(DataFetchingEnvironment env, @GraphQLName("id") String unitOfMeasureId) {
        GqlAuthContext context = env.getContext();

        if (StringUtils.isBlank(unitOfMeasureId)) {
            List<UnitOfMeasure> unitsOfMeasure = context.getUnitsOfMeasure(DEFAULT_PAGINATION_QUERY_PARAMS);
            return context.getUnitOfMeasureJsonMapper().toJsonList(unitsOfMeasure);
        } else {
            UnitOfMeasure unitOfMeasure = context.getUnitOfMeasure(UUID.fromString(unitOfMeasureId));
            var unitOfMeasureJson = context.getUnitOfMeasureJsonMapper().toJson(unitOfMeasure);
            return List.of(unitOfMeasureJson);
        }
    }

    @GraphQLField
    @GraphQLNonNull
    public static UserJson user(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String userId) {
        GqlAuthContext context = env.getContext();
        User user = context.getUserIncludeCognitoStatus(userId);
        return context.getUserDataAggregator().getJson(user);
    }

    @GraphQLField
    @GraphQLNonNull
    public static UserJson currentUser(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        Optional<UserPrincipal> currentUser = context.provideCurrentUserOptional();
        if (currentUser.isPresent() && currentUser.get().getEmailAddress().isPresent()) {
            boolean isSsoLogin = currentUser.get().isSsoLogin();
            User user = getUserByEmail(context, currentUser.get().getEmailAddress().get(), isSsoLogin);
            return context.getCurrentUserJson(currentUser.get(), user);
        }

        return getOptionalUserFromApiKey(context);
    }

    private static UserJson getOptionalUserFromApiKey(GqlAuthContext context) {
        Optional<User> userFromApiKey = context.provideApiKeyUser();
        if (userFromApiKey.isEmpty()) {
            throw new TenantNotFoundException("No current user available");
        }

        UserJson result = new UserJson();
        result.setId(userFromApiKey.get().getUserId());
        result.setRole(userFromApiKey.get().getRole());
        result.setDisplayName(userFromApiKey.get().getDisplayName());
        result.setEmail(userFromApiKey.get().getEmail());
        result.setState(userFromApiKey.get().getState());
        return result;
    }

    private static User getUserByEmail(GqlAuthContext context, String emailAddress, boolean isSsoLogin) {
        Optional<User> optionalUser = context.getUserByEmail(emailAddress);
        if (optionalUser.isPresent()) {
            return optionalUser.get();
        }

        String message = String.format("User with given email not found: %s, is SSO: %s", emailAddress, isSsoLogin);
        LOGGER.info(message);
        throw new TenantNotFoundException("No current user available.");
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull UserGroupDetail> userGroups(DataFetchingEnvironment env, @GraphQLName("id") String userGroupId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        List<UserGroup> userGroups;
        if (StringUtils.isBlank(userGroupId)) {
            userGroups = gqlAuthContext.getUserGroups();
        } else {
            userGroups = new ArrayList<>();
            userGroups.add(gqlAuthContext.getUserGroup(userGroupId, true));
        }

        return gqlAuthContext.getUserGroupMapper().toJsonList(userGroups);
    }

    @GraphQLField
    @GraphQLNonNull
    public static TenantSettingJson setting(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        var tenantSetting = context.getTenantSetting();
        return context.getTenantSettingJsonMapper().tenantSettingToJson(tenantSetting);
    }

    @GraphQLField
    public static AccountReceivableContactJson accountReceivableContactSetting(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        var optionalContact = context.getAccountReceivableContact();
        return optionalContact.map(accountContact -> context.getAccountReceivableContactJsonMapper().toJson(accountContact)).orElse(null);
    }

    @GraphQLField
    @GraphQLNonNull
    public static DunningSettingDetails dunningSetting(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        var dunningSetting = context.getDunningSetting();
        var dunningSettingJson = context.getDunningSettingJsonMapper().dunningSettingToJson(dunningSetting);
        return DunningSettingDetailsMapper.toDetails(dunningSettingJson);
    }

    @GraphQLField
    @GraphQLNonNull
    // TODO: should be a GQL mutation
    public static boolean sendSampleDunningEmail(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("dunningReminderType") DunningReminderType dunningReminderType
    ) {
        GqlAuthContext context = env.getContext();
        context.sendSampleDunningEmail(dunningReminderType);
        return true;
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull InvoiceDunningEmailDetail> invoiceDunningDetails(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber
    ) {
        GqlAuthContext context = env.getContext();
        List<InvoiceDunning> invoiceDunningList = context.getDunningDetails(invoiceNumber);
        return context.getInvoiceDunningDetailMapper().toInvoiceDunningEmailDetails(invoiceDunningList);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull TimeZoneData> timezones() {
        return TimeZoneService.getAvailableTimeZones();
    }

    @GraphQLField
    @GraphQLNonNull
    public static String search(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("jsonInBase64") String jsonInBase64) throws IOException {
        GqlAuthContext context = env.getContext();
        String query = new String(Base64.getDecoder().decode(jsonInBase64), StandardCharsets.UTF_8);
        String searchResult = context.search(query);
        return Base64.getEncoder().encodeToString(searchResult.getBytes(StandardCharsets.UTF_8));
    }

    @GraphQLField
    @GraphQLNonNull
    public static String searchInTenant(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("jsonInBase64") String jsonInBase64,
        @GraphQLNonNull @GraphQLName("tenantId") String tenantId
    ) throws IOException {
        GqlAuthContext context = env.getContext();
        String query = new String(Base64.getDecoder().decode(jsonInBase64), StandardCharsets.UTF_8);
        String searchResult = context.searchInTenant(query, tenantId);
        return Base64.getEncoder().encodeToString(searchResult.getBytes(StandardCharsets.UTF_8));
    }

    @GraphQLField
    public static UserAuthInfo userAuthInfo(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("email") String email) {
        GqlAuthContext context = env.getContext();
        return context.getAuthInfoByEmail(email).orElse(null);
    }

    @GraphQLField
    public static SalesforceAccount getSalesforceAccountById(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId
    ) {
        if (StringUtils.isBlank(accountId)) {
            throw new IllegalArgumentException("Salesforce account id is required.");
        }
        GqlAuthContext context = env.getContext();
        return context.getAccountById(accountId, true);
    }

    @GraphQLField
    public static List<@GraphQLNonNull SubskribeSalesforceContact> salesforceContactsForAccount(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId
    ) {
        if (StringUtils.isBlank(accountId)) {
            throw new IllegalArgumentException("Subskribe account id is required.");
        }
        GqlAuthContext context = env.getContext();
        return context.getSalesforceContactsForAccount(accountId);
    }

    @GraphQLField
    public static List<@GraphQLNonNull CrmContact> crmContactsForAccount(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId
    ) {
        if (StringUtils.isBlank(accountId)) {
            throw new IllegalArgumentException("Subskribe account id is required.");
        }
        GqlAuthContext context = env.getContext();
        return context.getCrmContactsForAccount(accountId);
    }

    @GraphQLField
    @Deprecated
    public static List<@GraphQLNonNull OpportunityJson> opportunitiesBySalesforceAccountId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("sfdcAccountId") String sfdcAccountId
    ) {
        if (StringUtils.isBlank(sfdcAccountId)) {
            throw new IllegalArgumentException("Salesforce Account id is required.");
        }
        GqlAuthContext gqlAuthContext = env.getContext();
        String tenantId = gqlAuthContext.provideTenantIdString();
        List<Opportunity> opportunities = gqlAuthContext.getOpportunitiesBySalesforceAccountId(sfdcAccountId, tenantId);
        return gqlAuthContext.getOpportunityMapper().opportunitiesToJson(opportunities);
    }

    @GraphQLField
    @Deprecated
    public static List<@GraphQLNonNull OpportunityJson> opportunitiesByHubSpotCompanyId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("companyId") String companyId
    ) {
        if (StringUtils.isBlank(companyId)) {
            throw new IllegalArgumentException("HubSpot company id is required.");
        }
        GqlAuthContext gqlAuthContext = env.getContext();
        List<Opportunity> opportunities = gqlAuthContext.getOpportunitiesByHubSpotCompanyId(companyId);
        return gqlAuthContext.getOpportunityMapper().opportunitiesToJson(opportunities);
    }

    // TODO: Use this for all opportunity fetches on the UI because it handles the CRM integration checks
    @GraphQLField
    public static List<@GraphQLNonNull OpportunityJson> opportunitiesFromCrm(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountCrmId") String accountCrmId
    ) {
        if (StringUtils.isBlank(accountCrmId)) {
            throw new IllegalArgumentException("Account CRM ID is required.");
        }
        GqlAuthContext gqlAuthContext = env.getContext();
        String tenantId = gqlAuthContext.provideTenantIdString();
        List<Opportunity> opportunities = gqlAuthContext.getOpportunitiesFromCrm(accountCrmId, tenantId);
        return gqlAuthContext.getOpportunityMapper().opportunitiesToJson(opportunities);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OpportunityJson opportunityByOpportunityId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("opportunityId") String opportunityId
    ) {
        Validator.validateStringNotBlank(opportunityId, "id cannot be blank");
        GqlAuthContext gqlAuthContext = env.getContext();
        Opportunity opportunity = gqlAuthContext.getOpportunityByOpportunityId(opportunityId);
        return gqlAuthContext.getOpportunityMapper().opportunityToJson(opportunity);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OpportunityJson opportunityByCrmOpportunityId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("crmOpportunityId") String crmOpportunityId
    ) {
        Validator.validateStringNotBlank(crmOpportunityId, "id cannot be blank");
        GqlAuthContext gqlAuthContext = env.getContext();
        Opportunity opportunity = gqlAuthContext.getOpportunityByCrmOpportunityId(crmOpportunityId);
        return gqlAuthContext.getOpportunityMapper().opportunityToJson(opportunity);
    }

    @GraphQLField
    @GraphQLNonNull
    public static NotificationTargetAndSubscriptions notification(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("notificationId") String notificationId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        // TODO: UUID.fromString() throws NumberFormatException which shows blank error on UI
        return gqlAuthContext.getNotificationTargetAndSubscriptionsById(UUID.fromString(notificationId), false);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull NotificationTargetAndSubscriptions> notifications(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getAllNotificationSubscriptionsForTenant();
    }

    @GraphQLField
    @GraphQLNonNull
    public static String webhookSigningKey(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("notificationId") String notificationId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getSigningKeyForWebhook(notificationId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AvalaraIntegration avalaraIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getAvalaraIntegration();
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean hasAvalaraIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.hasAvalaraIntegration();
    }

    @GraphQLField
    @GraphQLNonNull
    public static SalesforceIntegrationResponse salesforceIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Tenant tenant = gqlAuthContext.getCurrentTenant();
        return getSalesforceIntegrationForTenant(tenant, gqlAuthContext).orElseThrow(() ->
            new ObjectNotFoundException(BillyObjectType.SFDC_INTEGRATION, tenant.getTenantId())
        );
    }

    private static Optional<SalesforceIntegrationResponse> getSalesforceIntegrationForTenant(Tenant tenant, GqlAuthContext gqlAuthContext) {
        if (!tenant.getHasSalesforceIntegration()) {
            return Optional.empty();
        }

        return gqlAuthContext.getSalesforceIntegrationForTenant(tenant.getTenantId());
    }

    @GraphQLField
    @GraphQLNonNull
    public static SalesforceIntegrationResponse salesforceIntegrationDetails(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Tenant tenant = gqlAuthContext.getCurrentTenant();
        return gqlAuthContext
            .getSalesforceIntegrationForTenant(tenant.getTenantId())
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.SFDC_INTEGRATION, tenant.getTenantId()));
    }

    @GraphQLField
    @GraphQLNonNull
    public static TenantJob tenantJob(DataFetchingEnvironment env, @GraphQLName("id") String id) {
        GqlAuthContext context = env.getContext();
        return context.getTenantJob(id);
    }

    @GraphQLField
    @GraphQLNonNull
    public static TenantJob tenantJobAsAdmin(DataFetchingEnvironment env, @GraphQLName("tenantId") String tenantId, @GraphQLName("id") String id) {
        GqlAuthContext context = env.getContext();
        return context.getTenantJobAsAdmin(tenantId, id);
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentTemplateResponse documentTemplate(DataFetchingEnvironment env, @GraphQLName("id") String id) {
        GqlAuthContext context = env.getContext();
        DocumentTemplate documentTemplate = context.getDocumentTemplateByTemplateId(id);
        return context.getDocumentTemplateMapper().documentTemplateToResponse(documentTemplate);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull DocumentTemplateResponse> documentTemplates(DataFetchingEnvironment env, @GraphQLName("type") String type) {
        GqlAuthContext context = env.getContext();
        var documentTemplates = context.getDocumentTemplates(Validator.enumFromStringNullForError(type, DocumentTemplateType.class));
        return documentTemplates == null ? List.of() : context.getDocumentTemplateMapper().documentTemplatesToResponse(documentTemplates);
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentTemplateResponse documentTemplateVersion(
        DataFetchingEnvironment env,
        @GraphQLName("id") String id,
        @GraphQLName("version") int version
    ) {
        GqlAuthContext context = env.getContext();
        DocumentTemplate documentTemplate = context.getDocumentTemplateByTemplateId(id, version);
        return context.getDocumentTemplateMapper().documentTemplateToResponse(documentTemplate);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull DocumentTemplateResponse> documentTemplateVersions(DataFetchingEnvironment env, @GraphQLName("id") String id) {
        GqlAuthContext context = env.getContext();
        var documentTemplates = context.getDocumentTemplateVersions(id);
        return documentTemplates == null ? List.of() : context.getDocumentTemplateMapper().documentTemplatesToResponse(documentTemplates);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull DocumentMasterTemplate> documentMasterTemplates(
        DataFetchingEnvironment env,
        @GraphQLName("templateType") DocumentTemplateType templateType
    ) {
        GqlAuthContext context = env.getContext();
        return context.getDocumentMasterTemplates(templateType);
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocumentMasterTemplate documentMasterTemplate(DataFetchingEnvironment env, @GraphQLName("id") String documentMasterTemplateId) {
        GqlAuthContext context = env.getContext();
        DocumentMasterTemplate masterTemplate = context
            .getDocumentMasterTemplateById(documentMasterTemplateId, Optional.empty())
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.DOCUMENT_MASTER_TEMPLATE, documentMasterTemplateId));
        context.populateInUseFlagForMasterTemplate(masterTemplate);
        if (Objects.nonNull(masterTemplate.getConfiguration()) && masterTemplate.getConfiguration().getVersion() == 0) {
            masterTemplate.setConfiguration(null);
        }
        return masterTemplate;
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull DocumentSection> documentSections(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context.getDocumentSections();
    }

    @GraphQLField
    @GraphQLNonNull
    public static CustomPredefinedTemplateOnOrder customPredefinedTemplateOnOrder(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("templateId") String templateId,
        @GraphQLNonNull @GraphQLName("orderId") String orderId
    ) {
        GqlAuthContext context = env.getContext();
        return context.getCustomPredefinedTemplateOnOrder(templateId, orderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static EmailLogsResponse emailLogs(
        DataFetchingEnvironment env,
        @GraphQLName("entityId") String entityId,
        @GraphQLName("parentEntityId") String parentEntityId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        List<EmailLog> emailLogs = gqlAuthContext.getEmailLogs(Optional.ofNullable(entityId), Optional.ofNullable(parentEntityId));
        return new EmailLogsResponse(emailLogs);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull CurrencyJson> supportedCurrencies(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getSupportedCurrencies();
    }

    @GraphQLField
    @GraphQLNonNull
    public static DocuSignIntegrationResponseJson docuSignIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        String tenantId = gqlAuthContext.provideTenantIdString();

        DocuSignIntegration docuSignIntegration;
        try {
            docuSignIntegration = gqlAuthContext.getDocuSignIntegrationByTenantId(tenantId);
        } catch (ObjectNotFoundException e) {
            docuSignIntegration = new DocuSignIntegration(false);
        }
        return gqlAuthContext.getDocuSignMapper().convertIntegrationToResponseJson(docuSignIntegration);
    }

    @GraphQLField
    @GraphQLNonNull
    @Deprecated
    public static Boolean hasDocusignIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        String tenantId = gqlAuthContext.provideTenantIdString();
        return gqlAuthContext.hasDocusignIntegration(tenantId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PredefinedReportDefsJson predefinedReports(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getPredefinedReportDefs();
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean paymentIntegrationEnabled(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.isPaymentIntegrationEnabled();
    }

    @GraphQLField
    public static PaymentStripeConnectIntegration stripeConnectIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Optional<PaymentStripeConnectIntegration> result = gqlAuthContext.getCompletedStripeConnectIntegration();
        return result.orElse(null);
    }

    @GraphQLField
    public static List<PaymentStripeConnectIntegration> stripeConnectIntegrations(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getCompletedStripeConnectIntegrations();
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean hasPaymentIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.hasPaymentIntegration();
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private static <T> List<T> optionalToList(Optional<T> optional) {
        return optional.map(List::of).orElseGet(List::of);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull ApprovalFlowDetail> approvalFlows(DataFetchingEnvironment env, @GraphQLName("id") String approvalFlowId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        List<ApprovalFlow> approvalFlows;
        if (StringUtils.isBlank(approvalFlowId)) {
            approvalFlows = gqlAuthContext.getApprovalFlows();
        } else {
            approvalFlows = new ArrayList<>();
            approvalFlows.add(gqlAuthContext.getApprovalFlow(approvalFlowId));
        }

        return gqlAuthContext.getApprovalFlowDataAggregator().toApprovalFlowDetails(approvalFlows);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull ApprovalFlowInstanceGroupDetail> orderApprovals(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("orderId") String orderId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        var approvalFlowInstanceGroups = gqlAuthContext.getApprovalFlowInstances(orderId);
        return gqlAuthContext.getApprovalFlowDataAggregator().toFilteredApprovalFlowInstanceGroupDetails(approvalFlowInstanceGroups);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull FailedPaymentDetail> failedPaymentsForInvoice(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumberString
    ) {
        Validator.validateNonNullArgument(invoiceNumberString);
        Invoice.Number invoiceNumber = new Invoice.Number(invoiceNumberString);
        GqlAuthContext context = env.getContext();
        return context.getFailedPaymentDetailsForInvoice(invoiceNumber);
    }

    @GraphQLField
    @GraphQLNonNull
    public static BulkInvoiceRunSelector defaultBulkInvoiceRunSelector(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context.getDefaultBulkInvoiceRunSelector();
    }

    @GraphQLField
    @GraphQLNonNull
    public static BulkInvoiceRun bulkInvoiceRun(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("bulkInvoiceRunId") String bulkInvoiceRunId
    ) {
        GqlAuthContext context = env.getContext();
        return context.getBulkInvoiceRun(bulkInvoiceRunId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull BulkInvoiceRunItem> bulkInvoiceRunItems(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("bulkInvoiceRunId") String bulkInvoiceRunId
    ) {
        GqlAuthContext context = env.getContext();
        return context.getBulkInvoiceRunItems(bulkInvoiceRunId);
    }

    @GraphQLField
    public static UUID bulkInvoiceRunInProgress(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        Optional<UUID> inProgressRunId = context.getBulkInvoiceRunInProgress();
        return inProgressRunId.orElse(null);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull InvoiceItemDetail> previewUnbilledUsage(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId
    ) {
        GqlAuthContext context = env.getContext();
        List<InvoiceItem> previewInvoiceItems = context.getUnbilledUsageInvoiceItems(subscriptionId);
        return context.getInvoiceItemDetails(previewInvoiceItems, subscriptionId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull InvoiceItemDetail> usageForSubscriptionCharge(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("chargeId") String chargeId
    ) {
        GqlAuthContext context = env.getContext();
        List<InvoiceItem> invoiceItems = context.getUsageInvoiceItemsForSubscriptionCharge(subscriptionId, chargeId);
        return context.getInvoiceItemDetails(invoiceItems, subscriptionId);
    }

    @GraphQLField
    public static Long lastUsageArrivalCheckpoint(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        Optional<Instant> usageArrivalTimeCheckpointOptional = context.getRawUsageArrivalTimeCheckpoint();
        return usageArrivalTimeCheckpointOptional.map(Instant::getEpochSecond).orElse(null);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull PendingOrderApprovalFlow> pendingApprovalFlowsForUser(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        UserJson userJson = currentUser(env);
        User user = context.getUserMapper().jsonToUser(userJson);
        return context.getPendingOrderApprovalFlowsForUser(user);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull RecognitionRule> recognitionRules(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context.getRecognitionRules();
    }

    @GraphQLField
    @GraphQLNonNull
    public static RecognitionRule recognitionRuleById(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String ruleId) {
        GqlAuthContext context = env.getContext();
        return context.getRecognitionRuleByRuleId(ruleId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.RECOGNITION_RULE, ruleId));
    }

    @GraphQLField
    @GraphQLNonNull
    public static SubscriptionChargeRecognition subscriptionChargeRecognition(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("chargeId") String chargeId
    ) {
        GqlAuthContext context = env.getContext();
        return context
            .getSubscriptionChargeRecognition(subscriptionId, chargeId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.SUBSCRIPTION_CHARGE, String.format("%s %s", subscriptionId, chargeId)));
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull AccountingPeriod> recentAccountingPeriods(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("recentNumberOfMonths") Long recentNumberOfMonths
    ) {
        GqlAuthContext context = env.getContext();
        return context.getRecentAccountingPeriods(recentNumberOfMonths);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull AccountingPeriod> accountingPeriodsBetween(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("startDate") Long startDate,
        @GraphQLNonNull @GraphQLName("endDate") Long endDate
    ) {
        GqlAuthContext context = env.getContext();
        return context.getAccountingPeriodsBetween(Period.between(startDate, endDate));
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountingPeriod accountingPeriod(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountingPeriodId") String accountingPeriodId
    ) {
        GqlAuthContext context = env.getContext();
        AccountingPeriod accountingPeriod = context.getAccountingPeriod(accountingPeriodId);
        if (env.getSelectionSet().contains("calculation")) {
            context.populateAccountingPeriodCalculation(accountingPeriod);
        }
        if (env.getSelectionSet().contains("openedByUser")) {
            context.fetchOpenedByUserForAccountingPeriod(accountingPeriod);
        }
        if (env.getSelectionSet().contains("closedByUser")) {
            context.fetchClosedByUserForAccountingPeriod(accountingPeriod);
        }
        return accountingPeriod;
    }

    @GraphQLField
    public static AccountingPeriod currentAccountingPeriod(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context
            .getCurrentAccountingPeriod()
            .map(accountingPeriod -> {
                if (env.getSelectionSet().contains("calculation")) {
                    context.populateAccountingPeriodCalculation(accountingPeriod);
                }
                return accountingPeriod;
            })
            .orElse(null);
    }

    @GraphQLField
    public static AccountingPeriod currentAccountingPeriodByEntityId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("entityId") String entityId
    ) {
        GqlAuthContext context = env.getContext();
        return context.getCurrentAccountingPeriodByEntityId(entityId).orElse(null);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull LedgerAccount> ledgerAccounts(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context.getLedgerAccounts();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull LedgerAccount> defaultLedgerAccountTemplates(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context.getDefaultLedgerAccountTemplates();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull LedgerAccount> allLedgerAccountTemplates(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context.getAllLedgerAccountTemplates();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull LedgerAccount> defaultLedgerAccounts(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context.getDefaultLedgerAccounts();
    }

    @GraphQLField
    @GraphQLNonNull
    public static CreditMemoDetail creditMemo(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("creditMemoNumber") String creditMemoNumber) {
        GqlAuthContext context = env.getContext();
        return context.getCreditMemoDetail(creditMemoNumber);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean dynamicFeatureEnabled(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("feature") Feature dynamicFeature) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.isEnabled(dynamicFeature);
    }

    @Deprecated
    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull FeatureSetting> dynamicFeatures(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();

        return gqlAuthContext
            .getAllFeatureFlags()
            .entrySet()
            .stream()
            .map(e -> new FeatureSetting(e.getKey(), e.getValue()))
            .collect(Collectors.toList());
    }

    @GraphQLField
    @GraphQLNonNull
    public static SystemSettings systemSettings(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        BillyConfiguration billyConfiguration = gqlAuthContext.getBillyConfiguration();
        return SystemSettingsConstructor.constructSystemSettings(billyConfiguration);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull FeatureSetting> dynamicFeatureListV2(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();

        return gqlAuthContext
            .getAllFeatureFlagsWithProperEnumKeys()
            .entrySet()
            .stream()
            .map(e -> new FeatureSetting(e.getKey().name(), e.getValue()))
            .collect(Collectors.toList());
    }

    @GraphQLField
    @GraphQLNonNull
    public static SubscriptionChargeAlias subscriptionChargeAlias(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("aliasId") String aliasId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getSubscriptionChargeAlias(aliasId);
    }

    @GraphQLField
    public static List<@GraphQLNonNull SubscriptionChargeAlias> aliasesBySubscriptionIdAndChargeId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("chargeId") String chargeId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getAliasesBySubscriptionIdAndChargeId(subscriptionId, chargeId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull ApprovalRoleJson> approvalRoles(DataFetchingEnvironment env, @GraphQLName("id") String approvalRoleId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        List<ApprovalRole> approvalRoles;
        if (StringUtils.isBlank(approvalRoleId)) {
            approvalRoles = gqlAuthContext.getApprovalRoles();
        } else {
            approvalRoles = List.of(gqlAuthContext.getApprovalRoleByApprovalRoleId(approvalRoleId));
        }

        return gqlAuthContext.getApprovalFlowHierarchyMapper().approvalRolesToJson(approvalRoles);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull ApprovalSegmentJson> approvalSegments(
        DataFetchingEnvironment env,
        @GraphQLName("id") String approvalSegmentId,
        @GraphQLName("userId") String userId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        List<ApprovalSegment> approvalSegments;
        if (StringUtils.isNotBlank(approvalSegmentId)) {
            approvalSegments = List.of(gqlAuthContext.getApprovalSegmentByApprovalSegmentId(approvalSegmentId, false));
        } else if (StringUtils.isNotBlank(userId)) {
            approvalSegments = gqlAuthContext.getSegmentsForUser(userId);
        } else {
            approvalSegments = gqlAuthContext.getApprovalSegments();
        }

        return gqlAuthContext.getApprovalFlowHierarchyMapper().approvalSegmentsToJson(approvalSegments);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull RefundDetail> refunds(
        DataFetchingEnvironment env,
        @GraphQLName("accountId") String accountId,
        @GraphQLName("paymentId") String paymentId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();

        var refunds = getRefunds(gqlAuthContext, accountId, paymentId);
        return refunds.stream().map(gqlAuthContext::getRefundDetail).collect(Collectors.toList());
    }

    private static List<Refund> getRefunds(GqlAuthContext gqlAuthContext, String accountId, String paymentId) {
        if (StringUtils.isNotBlank(accountId)) {
            return gqlAuthContext.getRefunds(accountId);
        }

        if (StringUtils.isNotBlank(paymentId)) {
            return gqlAuthContext.getRefundsByPaymentId(paymentId);
        }

        throw new IllegalArgumentException("Either accountId or paymentId must be non-blank");
    }

    @GraphQLField
    @GraphQLNonNull
    public static RefundPreviewDetail generateRefund(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("creditMemoNumber") String creditMemoNumber,
        @GraphQLNonNull @GraphQLName("paymentId") String paymentId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        RefundPreview refundPreview = gqlAuthContext.generateRefund(creditMemoNumber, paymentId);
        return gqlAuthContext.getRefundPreviewDetail(refundPreview);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentConfiguration paymentConfiguration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getPaymentConfiguration();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<PaymentType> supportedPaymentTypes(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getSupportedPaymentTypes();
    }

    @GraphQLField
    @GraphQLNonNull
    public static HubSpotIntegration hubSpotIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getHubSpotIntegrationForTenant();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull HubSpotSetupMessage> verifyHubSpotSetup(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.verifyHubSpotSetup();
    }

    @GraphQLField
    public static AccountJson accountByHubSpotCompanyId(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("companyId") String companyId) {
        if (StringUtils.isBlank(companyId)) {
            throw new IllegalArgumentException("HubSpot company id is required.");
        }
        GqlAuthContext context = env.getContext();
        Account account = context.getCompanyFromHubSpotById(companyId);
        return context.getAccountMapper().accountToJson(account);
    }

    @GraphQLField
    public static AccountJson accountByCrmId(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("accountCrmId") String accountCrmId) {
        if (StringUtils.isBlank(accountCrmId)) {
            throw new IllegalArgumentException("crm id is required.");
        }
        GqlAuthContext context = env.getContext();
        Optional<Account> account = context.getAccountByCrmId(accountCrmId);
        return context.getAccountMapper().accountToJson(account.orElse(null));
    }

    @GraphQLField
    public static Long latestAggregationTime(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        Optional<Instant> usageArrivalTimeCheckpointOptional = context.getRawUsageArrivalTimeCheckpoint();
        return usageArrivalTimeCheckpointOptional.map(Instant::getEpochSecond).orElse(null);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull AggregatedUsage> latestAggregatedUsage(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId
    ) {
        GqlAuthContext context = env.getContext();

        // todo: move this mapping logic out of this class
        SubscriptionDetail subscriptionDetail = context.getSubscriptionDetail(subscriptionId);
        Map<String, ChargeJson> chargeGroupMap = subscriptionDetail
            .getCharges()
            .stream()
            .filter(subscriptionChargeDetail -> subscriptionChargeDetail.getCharge().getType() == ChargeType.USAGE)
            .collect(Collectors.toMap(SubscriptionChargeDetail::getGroupId, SubscriptionChargeDetail::getCharge));

        Instant now = Instant.now();
        Period period = Period.between(now.minus(15, ChronoUnit.DAYS), now);
        List<UsageAggregateOutput> usageAggregateOutput = context.getAggregatedUsageForSubscription(subscriptionId, period);

        return usageAggregateOutput
            .stream()
            .map(usageAggregate -> {
                ChargeJson chargeJson = chargeGroupMap.get(usageAggregate.getSubscriptionChargeGroupId());
                String chargeId = chargeJson == null ? null : chargeJson.getId();
                return new AggregatedUsage(
                    subscriptionId,
                    chargeId,
                    usageAggregate.getStartAt(),
                    usageAggregate.getEndAt(),
                    usageAggregate.getUsageSum()
                );
            })
            .collect(Collectors.toList());
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentTermSettingsJson paymentTermSettings(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context.getPaymentTermSettings();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull Attachment> attachments(
        DataFetchingEnvironment env,
        @GraphQLName("accountId") String accountId,
        @GraphQLName("attachmentId") String attachmentId
    ) {
        GqlAuthContext context = env.getContext();
        return getAttachments(accountId, attachmentId, context);
    }

    private static List<Attachment> getAttachments(String accountId, String attachmentId, GqlAuthContext context) {
        if (StringUtils.isNotBlank(accountId)) {
            return context.getAccountAttachments(accountId);
        }

        if (StringUtils.isNotBlank(attachmentId)) {
            Attachment attachment = context.getAttachmentByAttachmentId(UUID.fromString(attachmentId));
            return List.of(attachment);
        }

        throw new IllegalArgumentException("Either accountId or attachmentId must be provided");
    }

    @GraphQLField
    @GraphQLNonNull
    public static EnabledPlatformFeature enabledPlatformFeature(
        DataFetchingEnvironment env,
        @GraphQLName("platformFeature") @GraphQLNonNull PlatformFeature platformFeature
    ) {
        GqlAuthContext context = env.getContext();
        return context
            .getFeatureEnablement(platformFeature)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PLATFORM_FEATURE, platformFeature.name()));
    }

    @GraphQLField
    @GraphQLNonNull
    public static EnabledPlatformFeature enabledPlatformFeatureInTenant(
        DataFetchingEnvironment env,
        @GraphQLName("platformFeature") @GraphQLNonNull PlatformFeature platformFeature,
        @GraphQLNonNull @GraphQLName("tenantId") String tenantId
    ) {
        GqlAuthContext context = env.getContext();
        return context
            .getFeatureEnablementInTenant(platformFeature, tenantId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.PLATFORM_FEATURE, platformFeature.name()));
    }

    @GraphQLField
    public static Long validMinimumDateForTransactions(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context.getCurrentAccountingPeriod().map(acctPeriod -> acctPeriod.getStartDate().getEpochSecond()).orElse(null);
    }

    @GraphQLField
    public static Integration integration(
        DataFetchingEnvironment env,
        @GraphQLName("targetService") @GraphQLNonNull IntegrationTargetService integrationTargetService
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getIntegrationByTargetService(integrationTargetService).orElse(null);
    }

    @GraphQLField
    public static boolean testIntegration(
        DataFetchingEnvironment env,
        @GraphQLName("targetService") @GraphQLNonNull IntegrationTargetService targetService
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.testIntegration(targetService);
    }

    @GraphQLField
    public static Optional<Integration> erpIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getErpIntegration();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull TenantJob> activeErpSyncTasks(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getActiveErpSyncTasks();
    }

    @GraphQLField
    @GraphQLNonNull
    public static int draftInvoicesCountForCustomerAccount(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("accountId") String accountId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        // fetch account for 404 barrier
        gqlAuthContext.getAccount(accountId);
        return gqlAuthContext.getDraftInvoicesCountForAccount(accountId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull CustomFieldDefinitionJson> customFieldDefinitions(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("parentObjectType") CustomFieldParentType parentType
    ) {
        GqlAuthContext context = env.getContext();
        List<CustomFieldDefinition> definitions = context.getCustomFieldDefinitions(parentType);
        return context.getCustomFieldDefinitionJsonMapper().toCustomFieldDefinitionJsons(definitions);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull CustomFieldEntry> customFields(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("parentObjectType") CustomFieldParentType parentType,
        @GraphQLName("parentObjectId") String parentObjectId
    ) {
        GqlAuthContext context = env.getContext();
        return context.getCustomFields(parentType, parentObjectId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull CustomFieldWithParentReference> customFieldsBatched(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("parentObjectType") CustomFieldParentType parentType,
        @GraphQLNonNull @GraphQLName("parentObjectIds") List<String> parentObjectIds
    ) {
        GqlAuthContext context = env.getContext();
        return context.getCustomFields(parentType, parentObjectIds);
    }

    @Deprecated // This is kept for UI compatibility. Use runSelectionCustomization instead
    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull Action> runPlanAdditionCustomization(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("planCustomizationInput") PlanAdditionCustomizationInput customizationInput
    ) {
        GqlAuthContext context = env.getContext();
        return context.runPlanAdditionCustomization(customizationInput);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull Action> runSelectionCustomization(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("selectionCustomizationInput") SelectionCustomizationInput customizationInput
    ) {
        GqlAuthContext context = env.getContext();
        return context.runSelectionCustomization(customizationInput);
    }

    @GraphQLField
    public static Optional<UIConfiguration> uiConfiguration(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        return context.getUIConfiguration();
    }

    @GraphQLField
    @GraphQLNonNull
    public static String previewMasterTemplate(
        DataFetchingEnvironment env,
        @GraphQLName("orderId") @GraphQLNonNull String orderId,
        @GraphQLName("masterTemplateId") @GraphQLNonNull String masterTemplateId
    ) {
        GqlAuthContext gqlContext = env.getContext();
        return gqlContext.previewMasterTemplate(orderId, masterTemplateId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull ApiKeyDetail> apiKeys(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("activeOnly") boolean activeOnly
    ) {
        GqlAuthContext gqlContext = env.getContext();
        return gqlContext.getApiKeys(activeOnly);
    }

    @GraphQLField
    @GraphQLNonNull
    public static ApiKeyDetail apiKey(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String apiKeyId) {
        GqlAuthContext gqlContext = env.getContext();
        return gqlContext.getApiKey(apiKeyId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<EsignTenantSignatoryDetail> tenantSignatories(DataFetchingEnvironment env) {
        GqlAuthContext gqlContext = env.getContext();
        return gqlContext.getTenantSignatories();
    }

    @GraphQLField
    @GraphQLNonNull
    public static ElectronicSignatureDetail electronicSignature(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext gqlContext = env.getContext();
        return gqlContext.getElectronicSignatureDetailByOrderId(orderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<ImmutableElectronicSignatureAuditLog> electronicSignatureAuditLogs(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("electronicSignatureId") String electronicSignatureId
    ) {
        GqlAuthContext gqlContext = env.getContext();
        return gqlContext.getElectronicSignatureAuditLogs(electronicSignatureId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull UserJson> associatedUsers(DataFetchingEnvironment env) {
        GqlAuthContext context = env.getContext();
        Optional<String> email = CurrentUserProvider.getGoogleEmail();
        if (email.isEmpty()) {
            return List.of();
        }
        List<User> users = context.getUserService().getAssociatedUsers(email.get());
        List<Tenant> tenants = context.getTenantService().getAllCachedTenantsForGoogleLogin();
        List<User> usersInActiveTenants = UserService.addTenantNamesToUsers(tenants, users);
        return context.getUserMapper().usersToJson(usersInActiveTenants);
    }

    /**
     * Given a message, we return the message to the caller.  This is used for
     * E2E testing.
     * @param message The message to return
     */
    @GraphQLField
    @GraphQLNonNull
    public static String echo(@GraphQLNonNull @GraphQLName("message") String message) {
        return message;
    }

    /**
     * Query which throws an exception on every call for E2E testing.
     *
     * @param message Use this message as the Exception message
     * @throws Exception Always throw this exception on every API call.
     */
    @GraphQLField
    @GraphQLNonNull
    public static String throwException(@GraphQLNonNull @GraphQLName("message") String message) throws Exception {
        throw new Exception(message);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CompositeOrderDetail compositeOrder(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String compositeOrderId) {
        GqlAuthContext context = env.getContext();

        return context.getCompositeOrderDetail(compositeOrderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull PaymentDetail> refundablePayments(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountId") String accountId
    ) {
        GqlAuthContext context = env.getContext();
        return context.getRefundablePayments(accountId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static PaymentLinkGqlResponse invoicePaymentLinkDetails(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("linkId") String linkId
    ) {
        GqlAuthContext context = env.getContext();
        return context.buildInvoicePaymentLinkResponse(linkId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static AccountPaymentLinkGqlResponse accountPaymentLinkDetails(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("linkId") String linkId
    ) {
        GqlAuthContext context = env.getContext();
        return context.buildAccountPaymentLinkResponse(linkId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static BillingEvents billingEvents(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId,
        @GraphQLNonNull @GraphQLName("subscriptionChargeId") String subscriptionChargeId
    ) {
        GqlAuthContext context = env.getContext();
        return context.getBillingEvents(subscriptionId, subscriptionChargeId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static EscalationPolicy escalationPolicy(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String escalationPolicyId) {
        GqlAuthContext context = env.getContext();
        return context.getEscalationPolicy(escalationPolicyId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static WebOrderFormDetail webOrderFormDetail(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("documentId") String documentId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        Pair<DocumentLink, String> htmlTemplatePair = gqlAuthContext.getHtmlForWebOrderForm(documentId);
        var threads = gqlAuthContext.getThreadsForDocument(htmlTemplatePair.getLeft().linkId());
        return new WebOrderFormDetail(threads, htmlTemplatePair.getRight());
    }

    @GraphQLField
    public static Integer orderExpiryDurationInDays(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getOrderExpiryDurationInDays();
    }

    @GraphQLField
    public static SigningOrder signingOrder(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getSigningOrder();
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean userLoggedIn(DataFetchingEnvironment env) {
        try {
            GqlAuthContext gqlAuthContext = env.getContext();
            return gqlAuthContext.provideAuthUser().isPresent();
        } catch (Exception e) {
            LOGGER.info("Failed to determine if user is logged in.", e);
            return false;
        }
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull Long> billingPeriodStartDates(
        DataFetchingEnvironment env,
        @GraphQLName("subscriptionId") @GraphQLNonNull String subscriptionId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        List<Instant> billingPeriodStartDates = gqlAuthContext.getBillingPeriodStartDates(subscriptionId);
        if (CollectionUtils.isEmpty(billingPeriodStartDates)) {
            return List.of();
        }
        return billingPeriodStartDates.stream().map(Instant::getEpochSecond).toList();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull PaymentDetail> paymentsForInvoice(
        DataFetchingEnvironment env,
        @GraphQLName("invoiceNumber") String invoiceNumber
    ) {
        GqlAuthContext context = env.getContext();
        return context.getPaymentDetailsForInvoice(new Invoice.Number(invoiceNumber));
    }

    @GraphQLField
    @GraphQLNonNull
    public static ChargeDefaultAttributeReferences chargeDefaultAttributeReferences(
        DataFetchingEnvironment env,
        @GraphQLName("chargeId") String chargeId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getChargeDefaultAttributeReferences(chargeId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static TaxJarIntegration taxJarIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        String tenantId = gqlAuthContext.provideTenantIdString();
        return getTaxJarIntegration(gqlAuthContext).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.TAX_JAR_INTEGRATION, tenantId));
    }

    private static Optional<TaxJarIntegration> getTaxJarIntegration(GqlAuthContext gqlAuthContext) {
        return gqlAuthContext.getTaxJarIntegration();
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean hasTaxJarIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return getTaxJarIntegration(gqlAuthContext).isPresent();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull DataImport> dataImports(DataFetchingEnvironment env, @GraphQLName("dataImportId") String dataImportId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        if (StringUtils.isNotBlank(dataImportId)) {
            return List.of(gqlAuthContext.getDataImport(dataImportId));
        }
        return gqlAuthContext.getDataImports();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull FlatfileSpace> flatfileSpaces(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getFlatfileSpaces();
    }

    @GraphQLField
    @GraphQLNonNull
    public static FlatfileSpaceResponse flatfileSpace(DataFetchingEnvironment env, @GraphQLName("spaceId") String spaceId) throws IOException {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getFlatfileSpace(spaceId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull EmailContact> emailContactsForAccount(
        DataFetchingEnvironment env,
        @GraphQLName("accountId") String accountId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getEmailContactsForAccount(accountId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static NotificationInstanceWithAttempts notificationInstance(
        DataFetchingEnvironment env,
        @GraphQLName("notificationInstanceId") String notificationInstanceId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getNotificationInstance(notificationInstanceId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<NotificationProcessorTypeSupport> notificationProcessorTypeSupport(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getNotificationProcessorTypeSupport();
    }

    @GraphQLField
    public static Optional<LookerUserMapping> currentLookerUser(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getCurrentLookerUser();
    }

    @GraphQLField
    public static List<@GraphQLNonNull OpportunityJson> opportunitiesByAccountCrmId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountCrmId") String accountCrmId
    ) {
        if (StringUtils.isBlank(accountCrmId)) {
            throw new IllegalArgumentException("Account CRM id is required.");
        }
        GqlAuthContext gqlAuthContext = env.getContext();
        List<Opportunity> opportunities = gqlAuthContext.getOpportunitiesByAccountCrmId(accountCrmId);
        return gqlAuthContext.getOpportunityMapper().opportunitiesToJson(opportunities);
    }

    @GraphQLField
    public static ElectronicSignatureProvider electronicSignatureProvider(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getElectronicSignatureProvider();
    }

    @GraphQLField
    public static SalesRoom salesRoom(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("linkId") String linkId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getSalesRoom(linkId);
    }

    @GraphQLField
    public static EsignRequest esignDetails(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getEsignDetailsForOrder(orderId);
    }

    @GraphQLField
    public static SalesRoomLink salesRoomLink(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("orderId") String orderId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getSalesRoomLinkByOrderId(orderId).orElse(null);
    }

    @GraphQLField
    public static AccountDetail resellerForSalesRoom(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("linkId") String linkId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext
            .getResellerAccountForSalesRoom(linkId)
            .orElseThrow(() -> new InvalidInputException("No reseller account found for sales room."));
    }

    @GraphQLField
    @GraphQLNonNull
    public static CancelAndRestructureOrderDetail cancelAndRestructure(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("id") String compositeOrderId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        String message = String.format("get cancel and restructure detail for composite order id: %s", compositeOrderId);
        LOGGER.info(message);
        return gqlAuthContext.getCancelAndRestructureOrderDetail(compositeOrderId);
    }

    @GraphQLField
    public static String wireInstruction(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getWireInstruction();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull CompositeOrder> compositeOrdersByCrmOpportunityId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("crmOpportunityId") String crmOpportunityId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getCompositeOrdersByCrmOpportunityId(crmOpportunityId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull EnabledPlatformFeature> enabledPlatformFeatures(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getEnabledFeatures();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull EnabledPlatformFeature> enabledPlatformFeaturesInTenant(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("tenantId") String tenantId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getEnabledFeaturesInTenant(tenantId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static Set<String> activeSubscriptionsByAccountCrmId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("accountCrmId") String accountCrmId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getUpdatableSubscriptionsByAccountCrmId(accountCrmId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static Integration anrokIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        String tenantId = gqlAuthContext.provideTenantIdString();
        return getAnrokIntegration(gqlAuthContext).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.INTEGRATION, tenantId));
    }

    private static Optional<Integration> getAnrokIntegration(GqlAuthContext gqlAuthContext) {
        return gqlAuthContext.getAnrokIntegration();
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean hasAnrokIntegration(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return getAnrokIntegration(gqlAuthContext).isPresent();
    }

    @GraphQLField
    @GraphQLNonNull
    public static ErpInvoiceJson erpInvoice(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("invoiceErpId") String invoiceErpId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getErpInvoice(invoiceErpId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull ErpInvoiceJson> erpInvoicesBySubscriptionId(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("subscriptionId") String subscriptionId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getErpInvoicesBySubscriptionId(subscriptionId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static CurrencyTypeSetting currencyTypeSetting(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getCurrencyTypeSetting();
    }

    @GraphQLField
    @GraphQLNonNull
    public static CurrencyConversionRate currencyConversionRateById(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("id") String id) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.currencyConversionRateById(id);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull CurrencyConversionRate> currencyConversionRates(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.currencyConversionRates();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<@GraphQLNonNull CurrencyConversionRate> currencyConversionRatesForGivenCurrencyPair(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("fromCurrency") String fromCurrency,
        @GraphQLNonNull @GraphQLName("toCurrency") String toCurrency
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getCurrencyConversionRatesForGivenCurrencyPair(fromCurrency, toCurrency);
    }

    @GraphQLField
    @GraphQLNonNull
    public static String previewDocumentTemplate(
        DataFetchingEnvironment env,
        @GraphQLName("orderId") @GraphQLNonNull String orderId,
        @GraphQLName("templateId") @GraphQLNonNull String templateId
    ) {
        GqlAuthContext gqlContext = env.getContext();
        return gqlContext.previewDocumentTemplate(orderId, templateId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static OrderInvoicePreviewResolver orderInvoicePreview(
        DataFetchingEnvironment env,
        @GraphQLNonNull @GraphQLName("orderId") String orderId
    ) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getOrderInvoicePreview(orderId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static boolean emailSentForInvoice(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("invoiceNumber") String invoiceNumber) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.wasEmailSentForInvoice(invoiceNumber);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<PlanJson> replacedPlans(DataFetchingEnvironment env, @GraphQLNonNull @GraphQLName("planId") String planId) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getReplacedPlans(planId);
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<EmailSetting> emailSettings(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getEmailSettings();
    }

    @GraphQLField
    @GraphQLNonNull
    public static List<BillingCycleDefinitionJson> billingCycleDefinitions(DataFetchingEnvironment env) {
        GqlAuthContext gqlAuthContext = env.getContext();
        return gqlAuthContext.getBillingCycleDefinitions();
    }
}
