package com.subskribe.billy.graphql.subscription;

import com.subskribe.billy.graphql.invoice.EmailNotifiersDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountDetail;
import com.subskribe.billy.invoice.model.PurchaseOrder;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.metrics.MetricsJson;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.subscription.model.SubscriptionState;
import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import java.util.List;

@GraphQLName("Subscription")
public class SubscriptionDetail {

    @GraphQLField
    @GraphQLName("id")
    @GraphQLNonNull
    private String id;

    @GraphQLField
    @GraphQLName("externalId")
    private String externalId;

    @GraphQLField
    @GraphQLName("entityId")
    private String entityId;

    @GraphQLField
    @GraphQLName("account")
    @GraphQLNonNull
    private AccountJson account;

    @GraphQLField
    @GraphQLName("resoldBy")
    private AccountJson resoldBy;

    @GraphQLField
    @GraphQLName("shippingContact")
    @GraphQLNonNull
    private AccountContactJson shippingContact;

    @GraphQLField
    @GraphQLName("billingContact")
    @GraphQLNonNull
    private AccountContactJson billingContact;

    @GraphQLField
    @GraphQLName("mailingContacts")
    private List<@GraphQLNonNull AccountContactJson> mailingContacts;

    @GraphQLField
    @GraphQLName("state")
    @GraphQLNonNull
    private SubscriptionState state;

    @GraphQLField
    @GraphQLName("startDate")
    @GraphQLNonNull
    private Long startDate;

    @GraphQLField
    @GraphQLName("endDate")
    private Long endDate;

    @GraphQLField
    @GraphQLName("canceledDate")
    private Long canceledDate;

    @GraphQLField
    @GraphQLName("termLength")
    private RecurrenceJson termLength;

    @GraphQLField
    @GraphQLName("billingCycle")
    @GraphQLNonNull
    private RecurrenceJson billingCycle;

    @GraphQLField
    @GraphQLName("billingAnchorDate")
    private Long billingAnchorDate;

    @GraphQLField
    @GraphQLName("paymentTerm")
    private String paymentTerm;

    @GraphQLField
    @GraphQLName("billingTerm")
    private BillingTerm billingTerm;

    @GraphQLField
    @GraphQLName("predefinedDiscounts")
    private List<PredefinedDiscountDetail> predefinedDiscounts;

    @GraphQLField
    @GraphQLName("charges")
    @GraphQLNonNull
    private List<SubscriptionChargeDetail> charges;

    @GraphQLField
    @GraphQLName("orders")
    @GraphQLNonNull
    private List<String> orders;

    @GraphQLField
    @GraphQLName("purchaseOrders")
    private List<PurchaseOrder> purchaseOrders;

    @GraphQLField
    @GraphQLName("purchaseOrderRequiredForInvoicing")
    private boolean purchaseOrderRequiredForInvoicing;

    @GraphQLField
    @GraphQLName("autoRenew")
    private boolean autoRenew;

    @GraphQLField
    @GraphQLName("subscriptionMetrics")
    @GraphQLNonNull
    private MetricsJson metrics;

    @GraphQLField
    @GraphQLName("version")
    @GraphQLNonNull
    private int version;

    @GraphQLField
    @GraphQLName("creationTime")
    @GraphQLNonNull
    private Long creationTime;

    @GraphQLField
    @GraphQLName("renewedFromSubscription")
    private SubscriptionDetail renewedFromSubscription;

    @GraphQLField
    @GraphQLName("renewedFromDate")
    private Long renewedFromDate;

    @GraphQLField
    @GraphQLName("renewedToSubscription")
    private SubscriptionDetail renewedToSubscription;

    @GraphQLField
    @GraphQLName("renewedToDate")
    private Long renewedToDate;

    @GraphQLField
    @GraphQLName("restructuredFromSubscription")
    private SubscriptionDetail restructuredFromSubscription;

    @GraphQLField
    @GraphQLName("restructuredFromDate")
    private Long restructuredFromDate;

    @GraphQLField
    @GraphQLName("restructuredToSubscription")
    private SubscriptionDetail restructuredToSubscription;

    @GraphQLField
    @GraphQLName("restructuredToDate")
    private Long restructuredToDate;

    @GraphQLField
    @GraphQLName("activationDate")
    private Long activationDate;

    @GraphQLField
    @GraphQLName("currency")
    private String currency;

    @GraphQLField
    @GraphQLName("emailNotifiersList")
    @GraphQLNonNull
    private EmailNotifiersDetail emailNotifiersList;

    @GraphQLField
    @GraphQLName("durationModel")
    private SubscriptionDurationModel durationModel;

    public SubscriptionDetail() {}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public AccountJson getAccount() {
        return account;
    }

    public void setAccount(AccountJson account) {
        this.account = account;
    }

    public AccountJson getResoldBy() {
        return resoldBy;
    }

    public void setResoldBy(AccountJson resoldBy) {
        this.resoldBy = resoldBy;
    }

    public AccountContactJson getShippingContact() {
        return shippingContact;
    }

    public void setShippingContact(AccountContactJson shippingContact) {
        this.shippingContact = shippingContact;
    }

    public AccountContactJson getBillingContact() {
        return billingContact;
    }

    public void setBillingContact(AccountContactJson billingContact) {
        this.billingContact = billingContact;
    }

    public List<AccountContactJson> getMailingContacts() {
        return mailingContacts;
    }

    public void setMailingContacts(List<AccountContactJson> mailingContacts) {
        this.mailingContacts = mailingContacts;
    }

    public SubscriptionState getState() {
        return state;
    }

    public void setState(SubscriptionState state) {
        this.state = state;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public Long getCanceledDate() {
        return canceledDate;
    }

    public void setCanceledDate(Long canceledDate) {
        this.canceledDate = canceledDate;
    }

    public RecurrenceJson getTermLength() {
        return termLength;
    }

    public void setTermLength(RecurrenceJson termLength) {
        this.termLength = termLength;
    }

    public RecurrenceJson getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(RecurrenceJson billingCycle) {
        this.billingCycle = billingCycle;
    }

    public Long getBillingAnchorDate() {
        return billingAnchorDate;
    }

    public void setBillingAnchorDate(Long billingAnchorDate) {
        this.billingAnchorDate = billingAnchorDate;
    }

    public String getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(String paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public BillingTerm getBillingTerm() {
        return billingTerm;
    }

    public void setBillingTerm(BillingTerm billingTerm) {
        this.billingTerm = billingTerm;
    }

    public List<SubscriptionChargeDetail> getCharges() {
        return charges;
    }

    public void setCharges(List<SubscriptionChargeDetail> charges) {
        this.charges = charges;
    }

    public List<String> getOrders() {
        return orders;
    }

    public void setOrders(List<String> orders) {
        this.orders = orders;
    }

    public List<PurchaseOrder> getPurchaseOrders() {
        return purchaseOrders;
    }

    public void setPurchaseOrders(List<PurchaseOrder> purchaseOrders) {
        this.purchaseOrders = purchaseOrders;
    }

    public boolean isPurchaseOrderRequiredForInvoicing() {
        return purchaseOrderRequiredForInvoicing;
    }

    public void setPurchaseOrderRequiredForInvoicing(boolean purchaseOrderRequiredForInvoicing) {
        this.purchaseOrderRequiredForInvoicing = purchaseOrderRequiredForInvoicing;
    }

    public MetricsJson getMetrics() {
        return metrics;
    }

    public void setMetrics(MetricsJson metrics) {
        this.metrics = metrics;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public Long getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Long creationTime) {
        this.creationTime = creationTime;
    }

    public List<PredefinedDiscountDetail> getPredefinedDiscounts() {
        return predefinedDiscounts;
    }

    public void setPredefinedDiscounts(List<PredefinedDiscountDetail> predefinedDiscounts) {
        this.predefinedDiscounts = predefinedDiscounts;
    }

    public SubscriptionDetail getRenewedFromSubscription() {
        return renewedFromSubscription;
    }

    public void setRenewedFromSubscription(SubscriptionDetail renewedFromSubscription) {
        this.renewedFromSubscription = renewedFromSubscription;
    }

    public Long getRenewedFromDate() {
        return renewedFromDate;
    }

    public void setRenewedFromDate(Long renewedFromDate) {
        this.renewedFromDate = renewedFromDate;
    }

    public SubscriptionDetail getRenewedToSubscription() {
        return renewedToSubscription;
    }

    public void setRenewedToSubscription(SubscriptionDetail renewedToSubscription) {
        this.renewedToSubscription = renewedToSubscription;
    }

    public Long getRenewedToDate() {
        return renewedToDate;
    }

    public void setRenewedToDate(Long renewedToDate) {
        this.renewedToDate = renewedToDate;
    }

    public SubscriptionDetail getRestructuredFromSubscription() {
        return restructuredFromSubscription;
    }

    public void setRestructuredFromSubscription(SubscriptionDetail restructuredFromSubscription) {
        this.restructuredFromSubscription = restructuredFromSubscription;
    }

    public Long getRestructuredFromDate() {
        return restructuredFromDate;
    }

    public void setRestructuredFromDate(Long restructuredFromDate) {
        this.restructuredFromDate = restructuredFromDate;
    }

    public SubscriptionDetail getRestructuredToSubscription() {
        return restructuredToSubscription;
    }

    public void setRestructuredToSubscription(SubscriptionDetail restructuredToSubscription) {
        this.restructuredToSubscription = restructuredToSubscription;
    }

    public Long getRestructuredToDate() {
        return restructuredToDate;
    }

    public void setRestructuredToDate(Long restructuredToDate) {
        this.restructuredToDate = restructuredToDate;
    }

    public Long getActivationDate() {
        return activationDate;
    }

    public void setActivationDate(Long activationDate) {
        this.activationDate = activationDate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public boolean isAutoRenew() {
        return autoRenew;
    }

    public void setAutoRenew(boolean autoRenew) {
        this.autoRenew = autoRenew;
    }

    public EmailNotifiersDetail getEmailNotifiersList() {
        return emailNotifiersList;
    }

    public void setEmailNotifiersList(EmailNotifiersDetail emailNotifiersList) {
        this.emailNotifiersList = emailNotifiersList;
    }

    public SubscriptionDurationModel getDurationModel() {
        return durationModel;
    }

    public void setDurationModel(SubscriptionDurationModel durationModel) {
        this.durationModel = durationModel;
    }
}
