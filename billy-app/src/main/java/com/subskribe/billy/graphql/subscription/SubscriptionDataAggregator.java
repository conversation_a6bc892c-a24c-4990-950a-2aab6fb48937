package com.subskribe.billy.graphql.subscription;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.graphql.productcatalog.PlanDataAggregator;
import com.subskribe.billy.graphql.productcatalog.PlanDetail;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.account.AccountMapper;
import com.subskribe.billy.resources.json.metrics.MetricsJson;
import com.subskribe.billy.resources.json.metrics.MetricsJsonMapper;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.shared.pecuniary.ForeignExchangeUtils;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class SubscriptionDataAggregator {

    private final SubscriptionGetService subscriptionGetService;
    private final ProductCatalogGetService productCatalogGetService;
    private final AccountGetService accountGetService;
    private final MetricsService metricsService;
    private final EmailContactListService emailContactListService;
    private final SubscriptionDetailsMapper subscriptionDetailsMapper;
    private final AccountMapper accountMapper;
    private final PlanMapper planMapper;
    private final MetricsJsonMapper metricsJsonMapper;
    private final PlanDataAggregator planDataAggregator;
    private final CurrencyConversionRateGetService currencyConversionRateGetService;
    private final FeatureService featureService;

    @Inject
    public SubscriptionDataAggregator(
        SubscriptionGetService subscriptionGetService,
        ProductCatalogGetService productCatalogGetService,
        AccountGetService accountGetService,
        MetricsService metricsService,
        EmailContactListService emailContactListService,
        PlanDataAggregator planDataAggregator,
        CurrencyConversionRateGetService currencyConversionRateGetService,
        FeatureService featureService
    ) {
        this.subscriptionGetService = subscriptionGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.accountGetService = accountGetService;
        this.metricsService = metricsService;
        this.emailContactListService = emailContactListService;
        this.planDataAggregator = planDataAggregator;
        this.currencyConversionRateGetService = currencyConversionRateGetService;
        this.featureService = featureService;
        subscriptionDetailsMapper = Mappers.getMapper(SubscriptionDetailsMapper.class);
        accountMapper = Mappers.getMapper(AccountMapper.class);
        planMapper = Mappers.getMapper(PlanMapper.class);
        metricsJsonMapper = Mappers.getMapper(MetricsJsonMapper.class);
    }

    public SubscriptionDetail getSubscriptionDetail(String subscriptionId) {
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        return getSubscriptionDetail(subscription, true, true);
    }

    public SubscriptionDetail getSubscriptionDetail(String subscriptionId, int version) {
        if (version < 1) {
            throw new IllegalArgumentException("subscription version should be greater than 0");
        }

        Subscription subscription = subscriptionGetService.find(subscriptionId, version);
        return getSubscriptionDetail(subscription, true, true);
    }

    private SubscriptionDetail getSubscriptionDetail(Subscription subscription, boolean includeRenewals, boolean includeRestructure) {
        String accountId = subscription.getAccountId();
        String subscriptionId = subscription.getSubscriptionId();

        Account account = accountGetService.getAccount(accountId);
        AccountContact shippingContact = accountGetService.getContact(subscription.getShippingContactId());
        AccountContact billingContact = accountGetService.getContact(subscription.getBillingContactId());
        List<AccountContact> mailingContacts = emailContactListService.getEmailAccountContactsForSubscription(subscriptionId);

        SubscriptionDetail subscriptionDetail = subscriptionDetailsMapper.subscriptionToSubscriptionDetail(subscription);
        AccountJson accountJson = accountMapper.accountToJson(account);
        AccountContactJson shippingContactJson = accountMapper.accountContactToJson(shippingContact);
        AccountContactJson billingContactJson = accountMapper.accountContactToJson(billingContact);
        List<AccountContactJson> mailingContactsJson = accountMapper.accountContactsToJson(mailingContacts);

        Metrics subscriptionMetrics = metricsService.getSubscriptionMetrics(subscription, Instant.now());
        MetricsJson metricsJson = metricsJsonMapper.metricsToJson(subscriptionMetrics);

        subscriptionDetail.setAccount(accountJson);
        subscriptionDetail.setShippingContact(shippingContactJson);
        subscriptionDetail.setBillingContact(billingContactJson);
        if (!account.getAccountId().equalsIgnoreCase(billingContact.getAccountId())) {
            Account resellerAccount = accountGetService.getAccount(billingContact.getAccountId());
            subscriptionDetail.setResoldBy(accountMapper.accountToJson(resellerAccount));
        }

        subscriptionDetail.setMailingContacts(mailingContactsJson);
        subscriptionDetail.setMetrics(metricsJson);
        subscriptionDetail.setPredefinedDiscounts(subscriptionDetailsMapper.toOrderDiscountDetailList(subscription.getPredefinedDiscounts()));

        List<SubscriptionChargeDetail> subscriptionChargeDetails = getSubscriptionChargeDetails(subscription.getCharges());
        subscriptionDetail.setCharges(subscriptionChargeDetails);

        if (includeRenewals) {
            populateRenewalSubscriptions(subscription, subscriptionDetail);
        }

        if (includeRestructure) {
            populateRestructuringSubscriptions(subscription, subscriptionDetail);
        }

        //populate invoice emailing - To, Cc, Bcc lists
        subscriptionDetail.setEmailNotifiersList(emailContactListService.getInvoiceEmailNotifiersListForSubscription(subscriptionId));

        return subscriptionDetail;
    }

    private void populateRenewalSubscriptions(Subscription subscription, SubscriptionDetail subscriptionDetail) {
        if (StringUtils.isNotBlank(subscription.getRenewedFromSubscriptionId())) {
            Subscription renewedFromSubscription = subscriptionGetService.getSubscription(subscription.getRenewedFromSubscriptionId());
            SubscriptionDetail renewedFromSubscriptionDetail = getSubscriptionDetail(renewedFromSubscription, false, false);
            subscriptionDetail.setRenewedFromSubscription(renewedFromSubscriptionDetail);
        }

        if (StringUtils.isNotBlank(subscription.getRenewedToSubscriptionId())) {
            Subscription renewedToSubscription = subscriptionGetService.getSubscription(subscription.getRenewedToSubscriptionId());
            SubscriptionDetail renewedToSubscriptionDetail = getSubscriptionDetail(renewedToSubscription, false, false);
            subscriptionDetail.setRenewedToSubscription(renewedToSubscriptionDetail);
        }
    }

    private void populateRestructuringSubscriptions(Subscription subscription, SubscriptionDetail subscriptionDetail) {
        if (StringUtils.isNotBlank(subscription.getRestructuredFromSubscriptionId())) {
            Subscription restructuredFromSubscription = subscriptionGetService.getSubscription(subscription.getRestructuredFromSubscriptionId());
            SubscriptionDetail restructuredFromSubscriptionDetail = getSubscriptionDetail(restructuredFromSubscription, false, false);
            subscriptionDetail.setRestructuredFromSubscription(restructuredFromSubscriptionDetail);
        }

        if (StringUtils.isNotBlank(subscription.getRestructuredToSubscriptionId())) {
            Subscription restructuredToSubscription = subscriptionGetService.getSubscription(subscription.getRestructuredToSubscriptionId());
            SubscriptionDetail restructuredToSubscriptionDetail = getSubscriptionDetail(restructuredToSubscription, false, false);
            subscriptionDetail.setRestructuredToSubscription(restructuredToSubscriptionDetail);
        }
    }

    private List<SubscriptionChargeDetail> getSubscriptionChargeDetails(List<SubscriptionCharge> subscriptionCharges) {
        List<String> chargeIds = subscriptionCharges.stream().map(SubscriptionCharge::getChargeId).collect(Collectors.toList());
        List<Charge> charges = CollectionUtils.isEmpty(chargeIds) ? List.of() : productCatalogGetService.getChargesByChargeId(chargeIds);

        Map<String, Charge> chargeMap = new HashMap<>();
        charges.forEach(charge -> chargeMap.put(charge.getChargeId(), charge));

        Set<UUID> planIds = charges.stream().map(Charge::getPlanUuid).collect(Collectors.toSet());
        List<Plan> plans = CollectionUtils.isEmpty(planIds) ? List.of() : productCatalogGetService.getPlansByIds(new ArrayList<>(planIds));

        Map<UUID, PlanDetail> planDetailMap = new HashMap<>();
        plans.forEach(p -> planDetailMap.put(p.getId(), planDataAggregator.getPlanDetailFromPlan(p)));

        Map<String, ChargeDetail> chargeDetailMap = ProductCatalogGetService.getChargeDetailMapFromPlanDetailMap(planDetailMap);

        subscriptionCharges.sort(Comparator.comparing(SubscriptionCharge::getRank));

        List<SubscriptionChargeDetail> subscriptionChargeDetails = new ArrayList<>();

        for (SubscriptionCharge subscriptionCharge : subscriptionCharges) {
            SubscriptionChargeDetail subscriptionChargeDetail = subscriptionDetailsMapper.subscriptionChargeToDetail(subscriptionCharge);
            Charge charge = chargeMap.get(subscriptionCharge.getChargeId());
            subscriptionChargeDetail.setCharge(planMapper.chargeToJson(charge));
            subscriptionChargeDetail.setChargeDetail(chargeDetailMap.get(charge.getChargeId()));
            subscriptionChargeDetail.setPlan(planDetailMap.get(charge.getPlanUuid()));
            applyCurrencyConversion(subscriptionCharge, subscriptionChargeDetail);
            subscriptionChargeDetails.add(subscriptionChargeDetail);
            subscriptionChargeDetail.setPredefinedDiscounts(
                subscriptionDetailsMapper.toOrderDiscountLineItemDetailList(subscriptionCharge.getPredefinedDiscounts())
            );
        }
        return subscriptionChargeDetails;
    }

    private void applyCurrencyConversion(SubscriptionCharge subscriptionCharge, SubscriptionChargeDetail subscriptionChargeDetail) {
        if (subscriptionCharge.getCurrencyConversionRateId() == null) {
            return;
        }
        CurrencyConversionRate currencyConversionRate = currencyConversionRateGetService
            .getCurrencyConversionRateById(subscriptionCharge.getCurrencyConversionRateId().toString())
            .orElse(null);
        if (currencyConversionRate == null) {
            return;
        }
        subscriptionChargeDetail
            .getChargeDetail()
            .getPriceTiers()
            .forEach(priceTier ->
                priceTier.setAmount(
                    Numbers.makeCurrencyScale(ForeignExchangeUtils.applyExchangeRate(priceTier.getAmount(), currencyConversionRate, featureService))
                )
            );
    }
}
