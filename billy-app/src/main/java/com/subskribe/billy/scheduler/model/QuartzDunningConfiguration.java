package com.subskribe.billy.scheduler.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class QuartzDunningConfiguration implements QuartzPeriodicJobConfiguration {

    private Boolean enabled;

    // todo: Change the scheduler to run at 3am EST every day.
    private Integer intervalInMinutes;

    private Boolean jobProcessingEnabled;

    private String fromEmailAddress;

    @Override
    @JsonProperty
    public Boolean getEnabled() {
        return enabled;
    }

    @JsonProperty
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    @JsonProperty
    public Integer getIntervalInMinutes() {
        return intervalInMinutes;
    }

    @JsonProperty
    public void setIntervalInMinutes(Integer intervalInMinutes) {
        this.intervalInMinutes = intervalInMinutes;
    }

    @JsonProperty
    public Boolean getJobProcessingEnabled() {
        return jobProcessingEnabled;
    }

    @JsonProperty
    public void setJobProcessingEnabled(Boolean jobProcessingEnabled) {
        this.jobProcessingEnabled = jobProcessingEnabled;
    }

    @JsonProperty
    public String getFromEmailAddress() {
        return fromEmailAddress;
    }

    @JsonProperty
    public void setFromEmailAddress(String fromEmailAddress) {
        this.fromEmailAddress = fromEmailAddress;
    }
}
