package com.subskribe.billy.scheduler.job;

import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.service.InvoicePreviewReportService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.MetricsUpdater;
import com.subskribe.billy.metrics.model.StoredInvoicePreviews;
import com.subskribe.billy.metrics.model.StoredMetrics;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.resources.json.invoice.InvoicePreviewJsonMapper;
import com.subskribe.billy.scheduler.model.QuartzPeriodicJobConfiguration;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.mapper.BaseMapper;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.tenant.model.Tenant;
import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record3;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;
import org.quartz.DisallowConcurrentExecution;

/**
 * Job that computes and persists Financial metrics.
 * CAVEAT:
 * Persisted metrics can become stale and may result in incorrect values being
 * presented. The following conditions must be met for them to remain valid: <ol>
 * <li> Metrics can be persisted only for objects having immutable financial attributes
 * or their derivatives. (e.g. order, order_line, specific version of subscription, and account</li>
 * <li>Immutability includes all transitively-referenced objects that may be used
 * in metrics calculations (e.g. Rounding rules)</li>
 * <li> When metrics are likely to become stale because of <ul>
 *   <li>Addition of a new metric field</li>
 *   <li>a bug-fix</li>
 *   <li>change in algorithm</li>
 *   <li>Or a customer-action like change in Rounding rules and other settings etc, </li></ul>
 * all affected rows must be updated with (metrics->>'recompute')::boolean = true. It will result in
 * metrics being calculated live, until this job had a chance to recompute and store
 * the updated values </li></ol>
 */
// the metrics updater is twiddling with locks for Subscription/Order/OrderLineItem/Account and doing select for update
// it is fully not clear what concurrent execution of MetricsUpdaterJob will cause in terms of cascading/interleaving deadlocks
@DisallowConcurrentExecution
public class MetricsUpdaterJob extends AllTenantsJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetricsUpdaterJob.class);

    private DSLContextProvider dslContextProvider;
    private MetricsService metricsService;
    private SubscriptionGetService subscriptionGetService;
    private SubscriptionService subscriptionService;
    private OrderGetService orderGetService;
    private OrderService orderService;
    private AccountGetService accountGetService;
    private AccountService accountService;
    private InvoiceService invoiceService;
    private InvoicePreviewReportService invoicePreviewReportService;
    private MetricsUpdater metricsUpdater;
    private FeatureService featureService;
    private final InvoicePreviewJsonMapper invoicePreviewJsonMapper = Mappers.getMapper(InvoicePreviewJsonMapper.class);

    public void setDslContextProvider(DSLContextProvider dslContextProvider) {
        this.dslContextProvider = dslContextProvider;
    }

    public void setMetricsService(MetricsService metricsService) {
        this.metricsService = metricsService;
    }

    public void setSubscriptionGetService(SubscriptionGetService subscriptionGetService) {
        this.subscriptionGetService = subscriptionGetService;
    }

    public void setSubscriptionService(SubscriptionService subscriptionService) {
        this.subscriptionService = subscriptionService;
    }

    public void setOrderGetService(OrderGetService orderGetService) {
        this.orderGetService = orderGetService;
    }

    public void setOrderService(OrderService orderService) {
        this.orderService = orderService;
    }

    public void setAccountGetService(AccountGetService accountGetService) {
        this.accountGetService = accountGetService;
    }

    public void setAccountService(AccountService accountService) {
        this.accountService = accountService;
    }

    public void setInvoiceService(InvoiceService invoiceService) {
        this.invoiceService = invoiceService;
    }

    public void setInvoicePreviewReportService(InvoicePreviewReportService invoicePreviewReportService) {
        this.invoicePreviewReportService = invoicePreviewReportService;
    }

    public void setMetricsUpdater(MetricsUpdater metricsUpdater) {
        this.metricsUpdater = metricsUpdater;
    }

    public void setFeatureService(FeatureService featureService) {
        this.featureService = featureService;
    }

    @Override
    public void processTenant(Tenant tenant) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenant.getTenantId(), dslContextProvider);
        dslContext.transaction(configuration -> updateSubscriptionsTransactional(configuration, tenant.getTenantId()));
        dslContext.transaction(configuration -> updateOrderLinesTransactional(configuration, tenant.getTenantId()));
        dslContext.transaction(configuration -> updateExecutedOrdersTransactional(configuration, tenant.getTenantId()));
        dslContext.transaction(configuration -> updateAccountsTransactional(configuration, tenant.getTenantId()));
        dslContext.transaction(configuration -> updateInvoicePreviewsTransactional(configuration, tenant.getTenantId()));
        updateNonExecutedOrders(tenant.getTenantId());
    }

    private void updateSubscriptionsTransactional(Configuration configuration, String tenantId) {
        // Use Select for update with skip locked as this job will be running on multiple pods.
        var dslContext = DSL.using(configuration);
        List<Record3<UUID, String, UUID>> idsToUpdate = subscriptionGetService.getSubscriptionIdsForMetricsUpdateLocked(
            dslContext,
            tenantId,
            getMaxUpdatesPerTypeAndTenant()
        );
        Set<String> accountIds = new HashSet<>();
        idsToUpdate.forEach(id -> updateSubscription(dslContext, tenantId, id, accountIds));
        // Account metrics change when-and-only-when subscription metrics change.
        accountService.markAccountsForMetricsRecalculation(dslContext, tenantId, accountIds);
    }

    private void updateSubscription(DSLContext dslContext, String tenantId, Record3<UUID, String, UUID> ids, Set<String> accountIds) {
        try {
            UUID internalId = ids.get(ids.field1());
            String externalId = ids.get(ids.field2());
            UUID metricsId = ids.get(ids.field3());
            Subscription subscription = subscriptionGetService.getSubscription(externalId);
            boolean isLatestVersion = internalId.equals(subscription.getId());
            StoredMetrics sm = isLatestVersion
                ? new StoredMetrics(metricsService.recalculateSubscriptionMetrics(subscription, Instant.now(), true)) // Outside transaction
                : new StoredMetrics(false); // Old version of subscription
            subscriptionService.updateSubscriptionMetrics(
                dslContext,
                tenantId,
                internalId,
                externalId,
                metricsId,
                JSONB.jsonb(BaseMapper.JSON_MAPPER.writeValueAsString(sm))
            );
            if (isLatestVersion) {
                accountIds.add(subscription.getAccountId());
            }
        } catch (Exception e) {
            LOGGER.error("Subscription metrics update failed for tenant: {}, subscription {}", tenantId, ids.get(ids.field1()), e);
        }
    }

    private Map<String, StoredMetrics> updateOrderLinesTransactional(Configuration configuration, String tenantId) {
        var dslContext = DSL.using(configuration);
        List<String> idsToUpdate = orderGetService.getOrderlineIdsForMetricsUpdateLocked(dslContext, tenantId, getMaxUpdatesPerTypeAndTenant());
        return idsToUpdate.stream().collect(Collectors.toMap(Function.identity(), id -> updateOrderLine(dslContext, tenantId, id)));
    }

    private StoredMetrics updateOrderLine(DSLContext dslContext, String tenantId, String orderLineId) {
        StoredMetrics sm = new StoredMetrics(metricsService.getMetricsForOrderLine(orderLineId));
        try {
            orderService.updateOrderLineMetrics(dslContext, tenantId, orderLineId, JSONB.jsonb(BaseMapper.JSON_MAPPER.writeValueAsString(sm)));
        } catch (Exception e) {
            LOGGER.error("Order line metrics update failed for tenant: {}, orderLine {}", tenantId, orderLineId, e);
        }
        return sm;
    }

    private void updateExecutedOrdersTransactional(Configuration configuration, String tenantId) {
        var txnDslContext = DSL.using(configuration);
        List<String> idsToUpdate = orderGetService.getExecutedOrderIdsForMetricsUpdateLocked(
            txnDslContext,
            tenantId,
            getMaxUpdatesPerTypeAndTenant()
        );
        idsToUpdate.forEach(id -> metricsUpdater.updateExecutedOrder(txnDslContext, tenantId, id));
    }

    private void updateNonExecutedOrders(String tenantId) {
        List<String> idsToUpdate = orderGetService.getNonExecutedOrderIdsForMetricsUpdate(tenantId, getMaxUpdatesPerTypeAndTenant());
        idsToUpdate.forEach(orderId -> updateOrder(tenantId, orderId));
    }

    private void updateOrder(String tenantId, String orderId) {
        try {
            Order order = orderGetService.getOrderByOrderId(orderId);
            StoredMetrics sm = new StoredMetrics(metricsService.getOrderMetrics(order, Instant.now()));
            orderService.updateOrderMetricsIfCurrent(order, JSONB.jsonb(BaseMapper.JSON_MAPPER.writeValueAsString(sm)));
        } catch (Exception e) {
            LOGGER.error("Non-executed order metrics update failed for tenant: {}, order_id {}", tenantId, orderId, e);
        }
    }

    private void updateAccountsTransactional(Configuration configuration, String tenantId) {
        var txnDslContext = DSL.using(configuration);
        List<String> idsToUpdate = accountGetService.getAccountIdsForMetricsUpdateLocked(txnDslContext, tenantId, getMaxUpdatesPerTypeAndTenant());
        idsToUpdate.forEach(id -> updateAccount(txnDslContext, tenantId, id));
    }

    private void updateAccount(DSLContext txnDslContext, String tenantId, String accountId) {
        try {
            StoredMetrics sm = new StoredMetrics(metricsService.getAccountMetrics(accountId, Instant.now(), false));
            accountService.updateAccountMetrics(txnDslContext, tenantId, accountId, JSONB.jsonb(BaseMapper.JSON_MAPPER.writeValueAsString(sm)));
        } catch (Exception e) {
            LOGGER.error("Account metrics update failed for tenant: {}, account_id {}", tenantId, accountId, e);
        }
    }

    private void updateInvoicePreviewsTransactional(Configuration configuration, String tenantId) {
        /*
         * Since a subscription version is immutable today, we expect previews to be calculated once
         * for new subscriptions, an amendment, or when migration scripts change recompute to true,
         * because of a code/algorithm or settings change that may alter values of stored invoice previews.
         */
        var dslContext = DSL.using(configuration);
        List<Record3<UUID, String, UUID>> idsToUpdate = subscriptionGetService.getSubscriptionIdsForInvoicePreviewsLocked(
            dslContext,
            tenantId,
            getMaxUpdatesPerTypeAndTenant()
        );
        idsToUpdate.forEach(ids -> updateInvoicePreviews(dslContext, tenantId, ids));
    }

    private void updateInvoicePreviews(DSLContext dslContext, String tenantId, Record3<UUID, String, UUID> ids) {
        try {
            UUID internalId = ids.get(ids.field1());
            String externalId = ids.get(ids.field2());
            UUID metricsId = ids.get(ids.field3());
            Subscription subscription = subscriptionGetService.getSubscription(externalId);
            boolean isLatestVersion = internalId.equals(subscription.getId());

            StoredInvoicePreviews sip;
            if (isLatestVersion) {
                List<InvoicePreview> invoicePreviews;
                if (featureService.isEnabled(Feature.INVOICE_PREVIEW_REPORT_V2)) {
                    invoicePreviews = invoicePreviewReportService.previewAllInvoicesBySubscriptionId(externalId);
                } else {
                    invoicePreviews = invoiceService.previewAllInvoicesBySubscriptionId(externalId);
                }

                sip = new StoredInvoicePreviews(invoicePreviewJsonMapper.invoicePreviewListToStoredPreviews(invoicePreviews));
            } else {
                sip = null; // Old version of subscription
            }

            subscriptionService.setSubscriptionInvoicePreviews(
                dslContext,
                tenantId,
                internalId,
                externalId,
                metricsId,
                sip != null ? JSONB.jsonb(BaseMapper.JSON_MAPPER.writeValueAsString(sip)) : null
            );
        } catch (Exception e) {
            LOGGER.error("Stored invoice previews update failed for tenant: {}, subscription {}", tenantId, ids.get(ids.field1()), e);
        }
    }

    @Override
    public QuartzPeriodicJobConfiguration getJobConfiguration() {
        return getBillyConfiguration().getQuartzMetricsUpdaterConfiguration();
    }

    public Integer getMaxUpdatesPerTypeAndTenant() {
        return getBillyConfiguration().getQuartzMetricsUpdaterConfiguration().getUpdateBatchSize();
    }
}
