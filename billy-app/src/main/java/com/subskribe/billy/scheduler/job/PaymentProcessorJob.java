package com.subskribe.billy.scheduler.job;

import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;
import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.model.PaymentProcessorJobStatus;
import com.subskribe.billy.payment.model.PaymentProcessorJobUnit;
import com.subskribe.billy.payment.model.PaymentProviderCustomer;
import com.subskribe.billy.payment.model.PaymentProviderPaymentMethod;
import com.subskribe.billy.payment.processor.service.PaymentProcessorJobQueueService;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodGetService;
import com.subskribe.billy.payment.services.PaymentOrchestrator;
import com.subskribe.billy.payment.services.PaymentProviderService;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.scheduler.model.QuartzPeriodicJobConfiguration;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.tenant.model.Tenant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.collections4.CollectionUtils;

public class PaymentProcessorJob extends AllTenantsJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentProcessorJob.class);

    private static final int MAX_NUMBER_OF_INVOICES_TO_PROCESS_PER_TENANT = 1000;

    private PaymentProcessorJobQueueService paymentProcessorJobQueueService;

    private InvoiceRetrievalService invoiceRetrievalService;

    private PaymentIntegrationGetService paymentIntegrationGetService;

    private PaymentProviderService paymentProviderService;

    private FeatureService featureService;

    private AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService;

    private PaymentOrchestrator paymentOrchestrator;

    @Override
    public void processTenant(Tenant tenant) {
        if (!featureService.isEnabled(Feature.ENABLE_PAYMENT_JOB)) {
            LOGGER.info("Payment processing is disabled for tenant: {}", tenant.getTenantId());
            return;
        }

        if (getBillyConfiguration().isProd()) {
            LOGGER.info("Running payment processor job for tenant: {}", tenant.getTenantId());
        }

        List<PaymentProcessorJobUnit> units = paymentProcessorJobQueueService.getPaymentProcessorJobUnitsForTenant(
            tenant.getTenantId(),
            MAX_NUMBER_OF_INVOICES_TO_PROCESS_PER_TENANT
        );

        if (CollectionUtils.isEmpty(units)) {
            return;
        }

        if (getBillyConfiguration().isProd()) {
            LOGGER.info("Number of units to process for tenant: {} is: {}", tenant.getTenantId(), units.size());
        }

        Optional<PaymentStripeConnectIntegration> paymentStripeConnectIntegration =
            paymentIntegrationGetService.getCompletedStripeConnectIntegration();
        if (!paymentIntegrationGetService.isPaymentIntegrationEnabled() || paymentStripeConnectIntegration.isEmpty()) {
            // Marked as processed since there is no stripe integration or no automatic payment configured for this tenant
            processUnitsWithStatus(PaymentProcessorJobStatus.SUCCEEDED, units, tenant.getTenantId());
            return;
        }

        Multimap<PaymentProcessorJobStatus, PaymentProcessorJobUnit> results = Multimaps.index(units, this::processUnit);
        processResults(results, tenant.getTenantId());
    }

    /*
     * TODO: This method is clearly getting long and it needs refactoring.
     * Problem: This method does a few things: It fetches data and does validation at the same time
     */
    private PaymentProcessorJobStatus processUnit(PaymentProcessorJobUnit unit) {
        try {
            Invoice.Number invoiceNumber = new Invoice.Number(unit.getInvoiceNumber());
            Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);
            if (!shouldInvoiceBeProcessed(invoice)) {
                // One of the invoice pre-checks failed
                return PaymentProcessorJobStatus.SUCCEEDED;
            }

            if (!InvoiceRetrievalService.isInvoiceDueForPayment(invoice)) {
                // Invoice is not due yet, delay
                return PaymentProcessorJobStatus.DELAYED;
            }

            Optional<PaymentProviderCustomer> paymentProviderCustomer = paymentProviderService.getPaymentProviderCustomerByAccountId(
                unit.getTenantId(),
                invoice.getCustomerAccountId()
            );
            if (paymentProviderCustomer.isEmpty()) {
                // No matching stripe customer for this account.
                return PaymentProcessorJobStatus.SUCCEEDED;
            }

            return processAutomaticPayment(invoice, paymentProviderCustomer.get());
        } catch (Exception e) {
            LOGGER.info("Failed to process unit: {}", unit, e);
            return PaymentProcessorJobStatus.FAILED;
        }
    }

    private PaymentProcessorJobStatus processAutomaticPayment(Invoice invoice, PaymentProviderCustomer paymentProviderCustomer) {
        Optional<AccountPaymentMethod> accountPaymentMethod = accountAutomaticPaymentMethodGetService.getActiveAccountPaymentMethodForAccount(
            invoice.getCustomerAccountId()
        );
        if (accountPaymentMethod.isEmpty()) {
            // No active automatic payment method for this account
            return PaymentProcessorJobStatus.SUCCEEDED;
        }

        Optional<PaymentProviderPaymentMethod> paymentProviderPaymentMethod = paymentProviderService.getPaymentProviderPaymentMethodById(
            UUID.fromString(accountPaymentMethod.get().getPaymentMethodId()),
            false
        );
        if (paymentProviderPaymentMethod.isEmpty()) {
            // The payment provider payment method no longer exists. This should never happen.
            LOGGER.warn(
                "Payment provider payment method {} no longer exists for account {}",
                accountPaymentMethod.get().getPaymentMethodId(),
                invoice.getCustomerAccountId()
            );
            return PaymentProcessorJobStatus.SUCCEEDED;
        }

        // TODO : if there's an ongoing payment for this invoice, we should not process it again. This would be via the retry job.
        paymentOrchestrator.processPayment(
            invoice,
            accountPaymentMethod.get(),
            paymentProviderCustomer,
            paymentProviderPaymentMethod.get(),
            PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_FIRST_ATTEMPT,
            null
        );
        return PaymentProcessorJobStatus.SUCCEEDED;
    }

    private boolean shouldInvoiceBeProcessed(Invoice invoice) {
        return invoice.getStatus() == InvoiceStatus.POSTED;
    }

    private void processResults(Multimap<PaymentProcessorJobStatus, PaymentProcessorJobUnit> results, String tenantId) {
        for (PaymentProcessorJobStatus status : PaymentProcessorJobStatus.values()) {
            Collection<PaymentProcessorJobUnit> unitsWithStatus = results.get(status);
            if (CollectionUtils.isNotEmpty(unitsWithStatus)) {
                processUnitsWithStatus(status, unitsWithStatus, tenantId);
            }
        }
    }

    private void processUnitsWithStatus(PaymentProcessorJobStatus status, Collection<PaymentProcessorJobUnit> unitsWithStatus, String tenantId) {
        switch (status) {
            case SUCCEEDED -> paymentProcessorJobQueueService.deleteSuccessfullyProcessedJobUnits(unitsWithStatus, tenantId);
            case FAILED -> paymentProcessorJobQueueService.abandonFailedJobUnits(unitsWithStatus, tenantId);
            case DELAYED -> paymentProcessorJobQueueService.updateDelayedJobUnits(unitsWithStatus, tenantId);
            default -> LOGGER.error("Got invalid result after processing job unit: {}", status);
        }
    }

    @Override
    public QuartzPeriodicJobConfiguration getJobConfiguration() {
        return getBillyConfiguration().getQuartzPaymentJobConfiguration();
    }

    public void setPaymentProcessorJobQueueService(PaymentProcessorJobQueueService paymentProcessorJobQueueService) {
        this.paymentProcessorJobQueueService = paymentProcessorJobQueueService;
    }

    public void setInvoiceRetrievalService(InvoiceRetrievalService invoiceRetrievalService) {
        this.invoiceRetrievalService = invoiceRetrievalService;
    }

    public void setPaymentIntegrationGetService(PaymentIntegrationGetService paymentIntegrationGetService) {
        this.paymentIntegrationGetService = paymentIntegrationGetService;
    }

    public void setPaymentProviderService(PaymentProviderService paymentProviderService) {
        this.paymentProviderService = paymentProviderService;
    }

    public void setFeatureService(FeatureService featureService) {
        this.featureService = featureService;
    }

    public void setAccountAutomaticPaymentMethodGetService(AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService) {
        this.accountAutomaticPaymentMethodGetService = accountAutomaticPaymentMethodGetService;
    }

    public void setPaymentOrchestrator(PaymentOrchestrator paymentOrchestrator) {
        this.paymentOrchestrator = paymentOrchestrator;
    }
}
