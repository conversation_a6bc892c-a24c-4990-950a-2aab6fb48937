package com.subskribe.billy.scheduler.job;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowStateUpdaterService;
import com.subskribe.billy.auth.apikey.ApiKeyService;
import com.subskribe.billy.aws.cognito.CognitoService;
import com.subskribe.billy.aws.ses.SesClientProvider;
import com.subskribe.billy.aws.sqs.SqsClientProvider;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.dataimport.jobs.DataImportJob;
import com.subskribe.billy.dataimport.processors.AccountDomainImportProcessor;
import com.subskribe.billy.dataimport.processors.CatalogDomainImportProcessor;
import com.subskribe.billy.dataimport.processors.OrderDomainImportProcessor;
import com.subskribe.billy.dataimport.service.DataImportService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.dlq.service.DLQMessageHandler;
import com.subskribe.billy.dlq.service.DLQService;
import com.subskribe.billy.email.services.EmailNotificationQueueMessageHandler;
import com.subskribe.billy.email.services.EmailService;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.entity.service.EntityInvariantsService;
import com.subskribe.billy.entity.service.EntityService;
import com.subskribe.billy.erp.quickbooks.service.QuickbooksDataService;
import com.subskribe.billy.escalationpolicy.service.EscalationPolicyService;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeBackfillService;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateService;
import com.subskribe.billy.hubspot.service.HubSpotInstallService;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.infra.maintenance.MaintenanceService;
import com.subskribe.billy.invoice.automated.service.AutomatedInvoiceRuleGetService;
import com.subskribe.billy.invoice.automated.service.AutomatedInvoiceRuleService;
import com.subskribe.billy.invoice.bulk.job.BulkInvoiceRunJob;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceService;
import com.subskribe.billy.invoice.service.InvoiceConfigurationService;
import com.subskribe.billy.invoice.service.InvoiceDocumentService;
import com.subskribe.billy.invoice.service.InvoicePreviewReportService;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoicedunning.services.InvoiceDunningService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoDocumentService;
import com.subskribe.billy.looker.LookerConfigurationExportService;
import com.subskribe.billy.looker.LookerTenantService;
import com.subskribe.billy.looker.client.LookerClient;
import com.subskribe.billy.looker.db.LookerTenantDAO;
import com.subskribe.billy.looker.db.LookerUserDAO;
import com.subskribe.billy.looker.job.LookerTenantBackfillJob;
import com.subskribe.billy.looker.job.LookerTenantConfigExportJob;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metricsreporting.service.MetricsReportingService;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.notification.service.NotificationRetryService;
import com.subskribe.billy.notification.service.NotificationTargetService;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.services.OrderDocumentService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.processor.service.PaymentProcessorJobQueueService;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodGetService;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.payment.services.PaymentOrchestrator;
import com.subskribe.billy.payment.services.PaymentProviderService;
import com.subskribe.billy.payment.services.PaymentReconciliationService;
import com.subskribe.billy.payment.stripe.service.StripeService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogService;
import com.subskribe.billy.resources.json.account.AccountMapper;
import com.subskribe.billy.revrec.bulk.job.BulkRevenueRecognitionJob;
import com.subskribe.billy.revrec.bulk.service.BulkRevenueService;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.revrec.services.RevenueRecognitionService;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.scheduler.auth.QuartzApiKeyGarbageCollectorJob;
import com.subskribe.billy.scheduler.queue.QuartzQueueMessageHandler;
import com.subskribe.billy.search.reindex.db.SearchReindexDAO;
import com.subskribe.billy.search.reindex.service.SearchReindexService;
import com.subskribe.billy.search.reindex.task.GenerateReindexQueueJob;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionLifecycleNotificationsService;
import com.subskribe.billy.subscription.services.SubscriptionLifecycleScheduleService;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.usage.service.UsageAggregationService;
import com.subskribe.billy.user.service.UserService;
import java.time.Clock;
import javax.inject.Inject;
import org.glassfish.hk2.api.Immediate;
import org.mapstruct.factory.Mappers;
import org.quartz.Job;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.simpl.PropertySettingJobFactory;
import org.quartz.spi.TriggerFiredBundle;

@Immediate
public class QuartzDependencyInjectingJobFactory extends PropertySettingJobFactory {

    private final QuartzQueueMessageHandler queueMessageHandler;

    private final DLQMessageHandler dlqMessageHandler;

    private final SqsClientProvider sqsClientProvider;

    private final BillyConfiguration billyConfiguration;

    private final Clock clock;

    private final TenantService tenantService;

    private final TenantIdProvider tenantIdProvider;

    private final SubscriptionService subscriptionService;

    private final AccountGetService accountGetService;

    private final InvoiceService invoiceService;

    private final InvoiceDunningService invoiceDunningService;

    private final DLQService dlqService;

    private final DSLContextProvider dslContextProvider;

    private final DataImportService dataImportService;

    private final AccountDomainImportProcessor accountDomainImportProcessor;

    private final OrderDomainImportProcessor orderDomainImportProcessor;

    private final PaymentProcessorJobQueueService paymentProcessorJobQueueService;

    private final UsageAggregationService usageAggregationService;

    private final InvoiceRetrievalService invoiceRetrievalService;

    private final StripeService stripeService;

    private final BulkInvoiceService bulkInvoiceService;

    private final AccountingPeriodService accountingPeriodService;

    private final RevenueRecognitionService revenueRecognitionService;

    private final RevenueRecognitionGetService revenueRecognitionGetService;

    private final QuickbooksDataService quickbooksDataService;

    private final SalesforceJobQueueService salesforceJobQueueService;

    private final MetricsService metricsService;

    private final SubscriptionGetService subscriptionGetService;

    private final SubscriptionLifecycleNotificationsService subscriptionLifecycleNotificationsService;

    private final OrderGetService orderGetService;

    private final OrderService orderService;

    private final AccountService accountService;

    private final OpportunityGetService opportunityGetService;

    private final OpportunityService opportunityService;

    private final CognitoService cognitoService;

    private final FeatureService featureService;

    private final PaymentIntegrationGetService paymentIntegrationGetService;

    private final OrderDocumentService orderDocumentService;

    private final InvoiceDocumentService invoiceDocumentService;

    private final EmailNotificationQueueMessageHandler emailNotificationQueueMessageHandler;

    private final UserService userService;

    private final SesClientProvider sesClientProvider;

    private final CatalogDomainImportProcessor catalogDomainImportProcessor;

    private final MetricsReportingService metricsReportingService;

    private final ReportingJobQueueService reportingJobQueueService;

    private final PaymentGetService paymentGetService;

    private final PaymentProviderService paymentProviderService;

    private final AutomatedInvoiceRuleService automatedInvoiceRuleService;

    private final AutomatedInvoiceRuleGetService automatedInvoiceRuleGetService;

    private final ApprovalFlowInstanceService approvalFlowInstanceService;

    private final EscalationPolicyService escalationPolicyService;

    private final ApprovalFlowStateUpdaterService approvalFlowStateUpdaterService;

    private final ApiKeyService apiKeyService;

    private final TenantSettingService tenantSettingService;

    private final PaymentReconciliationService paymentReconciliationService;

    private final SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService;

    private final AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService;

    private final NotificationRetryService notificationRetryService;

    private final NotificationTargetService notificationTargetService;

    private final InvoicePreviewReportService invoicePreviewReportService;

    private final MaintenanceService maintenanceService;

    private final SearchReindexDAO searchReindexDAO;

    private final SearchReindexService searchReindexService;

    private final EntityService entityService;

    private final TransactionalExchangeBackfillService transactionalExchangeBackfillService;

    private final EntityInvariantsService entityInvariantsService;

    private final TransactionalExchangeRateService transactionalExchangeRateService;

    private final InvoiceConfigurationService invoiceConfigurationService;

    private final ProductCatalogGetService productCatalogGetService;

    private final ProductCatalogService productCatalogService;

    private final HubSpotInstallService hubSpotInstallService;

    private final HubSpotIntegrationService hubSpotIntegrationService;

    private final DocumentTemplateGetService documentTemplateGetService;

    private final CustomFieldService customFieldService;

    private final HubSpotService hubSpotService;

    private final EntityContextProvider entityContextProvider;

    private final EntityGetService entityGetService;

    private final LookerClient lookerClient;

    private final LookerTenantDAO lookerTenantDAO;

    private final LookerUserDAO lookerUserDAO;

    private final LookerTenantService lookerTenantService;

    private final LookerConfigurationExportService lookerConfigurationExportService;

    private final CreditMemoDocumentService creditMemoDocumentService;

    private final TenantJobDispatcherService tenantJobDispatcherService;

    private final EmailService emailService;

    private final CrmService crmService;

    private final BulkRevenueService bulkRevenueService;

    private final PaymentOrchestrator paymentOrchestrator;

    @Inject
    public QuartzDependencyInjectingJobFactory(
        QuartzQueueMessageHandler queueMessageHandler,
        DLQMessageHandler dlqMessageHandler,
        SqsClientProvider sqsClientProvider,
        BillyConfiguration billyConfiguration,
        Clock clock,
        TenantService tenantService,
        TenantIdProvider tenantIdProvider,
        SubscriptionService subscriptionService,
        AccountGetService accountGetService,
        InvoiceService invoiceService,
        InvoiceDunningService invoiceDunningService,
        DLQService dlqService,
        DSLContextProvider dslContextProvider,
        DataImportService dataImportService,
        AccountDomainImportProcessor accountDomainImportProcessor,
        OrderDomainImportProcessor orderDomainImportProcessor,
        PaymentProcessorJobQueueService paymentProcessorJobQueueService,
        UsageAggregationService usageAggregationService,
        InvoiceRetrievalService invoiceRetrievalService,
        StripeService stripeService,
        BulkInvoiceService bulkInvoiceService,
        AccountingPeriodService accountingPeriodService,
        RevenueRecognitionService revenueRecognitionService,
        RevenueRecognitionGetService revenueRecognitionGetService,
        QuickbooksDataService quickbooksDataService,
        SalesforceJobQueueService salesforceJobQueueService,
        MetricsService metricsService,
        SubscriptionGetService subscriptionGetService,
        SubscriptionLifecycleNotificationsService subscriptionLifecycleNotificationsService,
        OrderGetService orderGetService,
        OrderService orderService,
        AccountService accountService,
        OpportunityGetService opportunityGetService,
        OpportunityService opportunityService,
        CognitoService cognitoService,
        FeatureService featureService,
        PaymentIntegrationGetService paymentIntegrationGetService,
        OrderDocumentService orderDocumentService,
        InvoiceDocumentService invoiceDocumentService,
        EmailNotificationQueueMessageHandler emailNotificationQueueMessageHandler,
        UserService userService,
        SesClientProvider sesClientProvider,
        CatalogDomainImportProcessor catalogDomainImportProcessor,
        PaymentGetService paymentGetService,
        PaymentProviderService paymentProviderService,
        MetricsReportingService metricsReportingService,
        ReportingJobQueueService reportingJobQueueService,
        AutomatedInvoiceRuleService automatedInvoiceRuleService,
        AutomatedInvoiceRuleGetService automatedInvoiceRuleGetService,
        ApprovalFlowInstanceService approvalFlowInstanceService,
        EscalationPolicyService escalationPolicyService,
        ApprovalFlowStateUpdaterService approvalFlowStateUpdaterService,
        ApiKeyService apiKeyService,
        TenantSettingService tenantSettingService,
        PaymentReconciliationService paymentReconciliationService,
        SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService,
        AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService,
        NotificationRetryService notificationRetryService,
        NotificationTargetService notificationTargetService,
        InvoicePreviewReportService invoicePreviewReportService,
        MaintenanceService maintenanceService,
        SearchReindexDAO searchReindexDAO,
        SearchReindexService searchReindexService,
        EntityService entityService,
        TransactionalExchangeBackfillService transactionalExchangeBackfillService,
        EntityInvariantsService entityInvariantsService,
        TransactionalExchangeRateService transactionalExchangeRateService,
        InvoiceConfigurationService invoiceConfigurationService,
        ProductCatalogGetService productCatalogGetService,
        ProductCatalogService productCatalogService,
        HubSpotInstallService hubSpotInstallService,
        HubSpotIntegrationService hubSpotIntegrationService,
        DocumentTemplateGetService documentTemplateGetService,
        CustomFieldService customFieldService,
        HubSpotService hubSpotService,
        EntityContextProvider entityContextProvider,
        EntityGetService entityGetService,
        LookerClient lookerClient,
        LookerTenantDAO lookerTenantDAO,
        LookerUserDAO lookerUserDAO,
        LookerTenantService lookerTenantService,
        LookerConfigurationExportService lookerConfigurationExportService,
        CreditMemoDocumentService creditMemoDocumentService,
        TenantJobDispatcherService tenantJobDispatcherService,
        EmailService emailService,
        CrmService crmService,
        BulkRevenueService bulkRevenueService,
        PaymentOrchestrator paymentOrchestrator
    ) {
        this.queueMessageHandler = queueMessageHandler;
        this.dlqMessageHandler = dlqMessageHandler;
        this.accountingPeriodService = accountingPeriodService;
        this.sqsClientProvider = sqsClientProvider;
        this.billyConfiguration = billyConfiguration;
        this.clock = clock;
        this.tenantService = tenantService;
        this.tenantIdProvider = tenantIdProvider;
        this.subscriptionService = subscriptionService;
        this.accountGetService = accountGetService;
        this.invoiceService = invoiceService;
        this.invoiceDunningService = invoiceDunningService;
        this.dlqService = dlqService;
        this.dslContextProvider = dslContextProvider;
        this.dataImportService = dataImportService;
        this.accountDomainImportProcessor = accountDomainImportProcessor;
        this.orderDomainImportProcessor = orderDomainImportProcessor;
        this.paymentProcessorJobQueueService = paymentProcessorJobQueueService;
        this.usageAggregationService = usageAggregationService;
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.stripeService = stripeService;
        this.bulkInvoiceService = bulkInvoiceService;
        this.revenueRecognitionService = revenueRecognitionService;
        this.revenueRecognitionGetService = revenueRecognitionGetService;
        this.quickbooksDataService = quickbooksDataService;
        this.salesforceJobQueueService = salesforceJobQueueService;
        this.metricsService = metricsService;
        this.subscriptionGetService = subscriptionGetService;
        this.subscriptionLifecycleNotificationsService = subscriptionLifecycleNotificationsService;
        this.orderGetService = orderGetService;
        this.orderService = orderService;
        this.accountService = accountService;
        this.opportunityGetService = opportunityGetService;
        this.opportunityService = opportunityService;
        this.cognitoService = cognitoService;
        this.featureService = featureService;
        this.paymentIntegrationGetService = paymentIntegrationGetService;
        this.orderDocumentService = orderDocumentService;
        this.invoiceDocumentService = invoiceDocumentService;
        this.emailNotificationQueueMessageHandler = emailNotificationQueueMessageHandler;
        this.userService = userService;
        this.sesClientProvider = sesClientProvider;
        this.catalogDomainImportProcessor = catalogDomainImportProcessor;
        this.paymentGetService = paymentGetService;
        this.paymentProviderService = paymentProviderService;
        this.metricsReportingService = metricsReportingService;
        this.reportingJobQueueService = reportingJobQueueService;
        this.automatedInvoiceRuleService = automatedInvoiceRuleService;
        this.automatedInvoiceRuleGetService = automatedInvoiceRuleGetService;
        this.accountAutomaticPaymentMethodGetService = accountAutomaticPaymentMethodGetService;
        this.approvalFlowInstanceService = approvalFlowInstanceService;
        this.escalationPolicyService = escalationPolicyService;
        this.approvalFlowStateUpdaterService = approvalFlowStateUpdaterService;
        this.apiKeyService = apiKeyService;
        this.tenantSettingService = tenantSettingService;
        this.paymentReconciliationService = paymentReconciliationService;
        this.subscriptionLifecycleScheduleService = subscriptionLifecycleScheduleService;
        this.notificationRetryService = notificationRetryService;
        this.notificationTargetService = notificationTargetService;
        this.invoicePreviewReportService = invoicePreviewReportService;
        this.maintenanceService = maintenanceService;
        this.searchReindexDAO = searchReindexDAO;
        this.searchReindexService = searchReindexService;
        this.entityService = entityService;
        this.transactionalExchangeBackfillService = transactionalExchangeBackfillService;
        this.entityInvariantsService = entityInvariantsService;
        this.transactionalExchangeRateService = transactionalExchangeRateService;
        this.invoiceConfigurationService = invoiceConfigurationService;
        this.productCatalogGetService = productCatalogGetService;
        this.productCatalogService = productCatalogService;
        this.hubSpotInstallService = hubSpotInstallService;
        this.hubSpotIntegrationService = hubSpotIntegrationService;
        this.documentTemplateGetService = documentTemplateGetService;
        this.customFieldService = customFieldService;
        this.hubSpotService = hubSpotService;
        this.entityContextProvider = entityContextProvider;
        this.entityGetService = entityGetService;
        this.lookerClient = lookerClient;
        this.lookerTenantDAO = lookerTenantDAO;
        this.lookerUserDAO = lookerUserDAO;
        this.lookerTenantService = lookerTenantService;
        this.lookerConfigurationExportService = lookerConfigurationExportService;
        this.creditMemoDocumentService = creditMemoDocumentService;
        this.tenantJobDispatcherService = tenantJobDispatcherService;
        this.emailService = emailService;
        this.crmService = crmService;
        this.bulkRevenueService = bulkRevenueService;
        this.paymentOrchestrator = paymentOrchestrator;
    }

    @Override
    public Job newJob(TriggerFiredBundle bundle, Scheduler scheduler) throws SchedulerException {
        Job job = super.newJob(bundle, scheduler);

        if (job instanceof AllTenantsJob allTenantsJob) {
            allTenantsJob.setBillyConfiguration(billyConfiguration);
            allTenantsJob.setTenantIdProvider(tenantIdProvider);
            allTenantsJob.setTenantService(tenantService);
            allTenantsJob.setMaintenanceService(maintenanceService);
        }

        if (job instanceof AllInclusiveTenantsJob allInclusiveTenantsJob) {
            allInclusiveTenantsJob.setBillyConfiguration(billyConfiguration);
            allInclusiveTenantsJob.setTenantIdProvider(tenantIdProvider);
            allInclusiveTenantsJob.setTenantService(tenantService);
            allInclusiveTenantsJob.setMaintenanceService(maintenanceService);
        }

        if (job instanceof AllTenantEntitiesJob allTenantEntitiesJob) {
            allTenantEntitiesJob.setBillyConfiguration(billyConfiguration);
            allTenantEntitiesJob.setTenantIdProvider(tenantIdProvider);
            allTenantEntitiesJob.setMaintenanceService(maintenanceService);
            allTenantEntitiesJob.setEntityContextProvider(entityContextProvider);
            allTenantEntitiesJob.setEntityGetService(entityGetService);
        }

        if (job instanceof QueuePollingJob queuePollingJob) {
            queuePollingJob.setSqsClientProvider(sqsClientProvider);
            queuePollingJob.setBillyConfiguration(billyConfiguration);
        }

        if (job instanceof BillyJob billyJob) {
            billyJob.setMessageHandler(queueMessageHandler);
        }

        if (job instanceof DLQJob dlqJob) {
            dlqJob.setMessageHandler(dlqMessageHandler);
        }

        if (job instanceof EmailNotificationQueueJob emailNotificationQueueJob) {
            emailNotificationQueueJob.setMessageHandler(emailNotificationQueueMessageHandler);
        }

        if (job instanceof QuartzEnqueueJob quartzEnqueueJob) {
            quartzEnqueueJob.setSqsClientProvider(sqsClientProvider);
        }

        if (job instanceof InvoiceGeneratorJob invoiceGeneratorJob) {
            invoiceGeneratorJob.setBillyConfiguration(billyConfiguration);
            invoiceGeneratorJob.setTenantIdProvider(tenantIdProvider);
            invoiceGeneratorJob.setAutomatedInvoiceRuleService(automatedInvoiceRuleService);
            invoiceGeneratorJob.setAutomatedInvoiceRuleGetService(automatedInvoiceRuleGetService);
            invoiceGeneratorJob.setTenantJobDispatcherService(tenantJobDispatcherService);
        }

        if (job instanceof DataImportJob dataImportJob) {
            dataImportJob.setTenantIdProvider(tenantIdProvider);
            dataImportJob.setEntityContextProvider(entityContextProvider);
            dataImportJob.setDataImportService(dataImportService);
            dataImportJob.setAccountDomainProcessor(accountDomainImportProcessor);
            dataImportJob.setOrderDomainProcessor(orderDomainImportProcessor);
            dataImportJob.setCatalogDomainImportProcessor(catalogDomainImportProcessor);
        }

        if (job instanceof SubscriptionStatusUpdaterJob subscriptionStatusUpdaterJob) {
            subscriptionStatusUpdaterJob.setBillyConfiguration(billyConfiguration);
            subscriptionStatusUpdaterJob.setTenantIdProvider(tenantIdProvider);
            subscriptionStatusUpdaterJob.setTenantService(tenantService);
            subscriptionStatusUpdaterJob.setSubscriptionService(subscriptionService);
            subscriptionStatusUpdaterJob.setSubscriptionGetService(subscriptionGetService);
            subscriptionStatusUpdaterJob.setSalesforceJobQueueService(salesforceJobQueueService);
            subscriptionStatusUpdaterJob.setHubSpotService(hubSpotService);
        }

        if (job instanceof SubscriptionChargeChangeProcessorJob subscriptionChargeChangeProcessorJob) {
            subscriptionChargeChangeProcessorJob.setBillyConfiguration(billyConfiguration);
            subscriptionChargeChangeProcessorJob.setTenantIdProvider(tenantIdProvider);
            subscriptionChargeChangeProcessorJob.setTenantService(tenantService);
            subscriptionChargeChangeProcessorJob.setSubscriptionChargeChangeScheduleService(subscriptionLifecycleScheduleService);
            subscriptionChargeChangeProcessorJob.setFeatureService(featureService);
        }

        if (job instanceof DunningJob dunningJob) {
            dunningJob.setBillyConfiguration(billyConfiguration);
            dunningJob.setTenantIdProvider(tenantIdProvider);
            dunningJob.setTenantService(tenantService);
            dunningJob.setInvoiceDunningService(invoiceDunningService);
        }

        if (job instanceof UsageAggregationJob usageAggregationJob) {
            usageAggregationJob.setBillyConfiguration(billyConfiguration);
            usageAggregationJob.setTenantIdProvider(tenantIdProvider);
            usageAggregationJob.setTenantService(tenantService);
            usageAggregationJob.setUsageAggregationService(usageAggregationService);
        }

        if (job instanceof RevenueRecognitionJob revenueRecognitionJob) {
            revenueRecognitionJob.setRevenueRecognitionService(revenueRecognitionService);
        }

        if (job instanceof PaymentProcessorJob paymentProcessorJob) {
            paymentProcessorJob.setBillyConfiguration(billyConfiguration);
            paymentProcessorJob.setTenantIdProvider(tenantIdProvider);
            paymentProcessorJob.setTenantService(tenantService);
            paymentProcessorJob.setPaymentProcessorJobQueueService(paymentProcessorJobQueueService);
            paymentProcessorJob.setInvoiceRetrievalService(invoiceRetrievalService);
            //            paymentProcessorJob.setStripeService(stripeService);
            paymentProcessorJob.setPaymentOrchestrator(paymentOrchestrator);
            paymentProcessorJob.setPaymentIntegrationGetService(paymentIntegrationGetService);
            paymentProcessorJob.setPaymentProviderService(paymentProviderService);
            paymentProcessorJob.setFeatureService(featureService);
            paymentProcessorJob.setAccountAutomaticPaymentMethodGetService(accountAutomaticPaymentMethodGetService);
        }

        if (job instanceof PaymentReconciliationJob paymentReconciliationJob) {
            paymentReconciliationJob.setBillyConfiguration(billyConfiguration);
            paymentReconciliationJob.setTenantIdProvider(tenantIdProvider);
            paymentReconciliationJob.setTenantService(tenantService);
            paymentReconciliationJob.setPaymentIntegrationGetService(paymentIntegrationGetService);
            paymentReconciliationJob.setPaymentGetService(paymentGetService);
            paymentReconciliationJob.setPaymentReconciliationService(paymentReconciliationService);
        }

        if (job instanceof BulkInvoiceRunJob bulkInvoiceRunJob) {
            bulkInvoiceRunJob.setBulkInvoiceService(bulkInvoiceService);
        }

        if (job instanceof MetricsUpdaterJob metricsUpdaterJob) {
            metricsUpdaterJob.setBillyConfiguration(billyConfiguration);
            metricsUpdaterJob.setTenantIdProvider(tenantIdProvider);
            metricsUpdaterJob.setTenantService(tenantService);
            metricsUpdaterJob.setDslContextProvider(dslContextProvider);
            metricsUpdaterJob.setMetricsService(metricsService);
            metricsUpdaterJob.setSubscriptionGetService(subscriptionGetService);
            metricsUpdaterJob.setSubscriptionService(subscriptionService);
            metricsUpdaterJob.setOrderGetService(orderGetService);
            metricsUpdaterJob.setOrderService(orderService);
            metricsUpdaterJob.setAccountGetService(accountGetService);
            metricsUpdaterJob.setAccountService(accountService);
            metricsUpdaterJob.setInvoiceService(invoiceService);
            metricsUpdaterJob.setInvoicePreviewReportService(invoicePreviewReportService);
            metricsUpdaterJob.setFeatureService(featureService);
        }

        if (job instanceof OpportunityDataMigrationJob opportunityDataMigrationJob) {
            opportunityDataMigrationJob.setOrderGetService(orderGetService);
            opportunityDataMigrationJob.setOpportunityGetService(opportunityGetService);
            opportunityDataMigrationJob.setOpportunityService(opportunityService);
        }

        if (job instanceof AccountCrmTypeBackfillJob accountCrmTypeBackfillJob) {
            accountCrmTypeBackfillJob.setAccountGetService(accountGetService);
            accountCrmTypeBackfillJob.setAccountService(accountService);
        }

        if (job instanceof CognitoEmailTemplateUpdaterJob cognitoEmailTemplateUpdaterJob) {
            cognitoEmailTemplateUpdaterJob.setCognitoService(cognitoService);
        }

        if (job instanceof InvoiceDocumentGeneratorJob invoiceDocumentGeneratorJob) {
            invoiceDocumentGeneratorJob.setTenantIdProvider(tenantIdProvider);
            invoiceDocumentGeneratorJob.setInvoiceDocumentService(invoiceDocumentService);
            invoiceDocumentGeneratorJob.setDlqService(dlqService);
        }

        if (job instanceof OrderDocumentGeneratorJob orderDocumentGeneratorJob) {
            orderDocumentGeneratorJob.setTenantIdProvider(tenantIdProvider);
            orderDocumentGeneratorJob.setOrderDocumentService(orderDocumentService);
            orderDocumentGeneratorJob.setDlqService(dlqService);
        }

        if (job instanceof BackfillMemoizedInvoiceLinesJob backfillMemoizedInvoiceLinesJob) {
            backfillMemoizedInvoiceLinesJob.setTenantIdProvider(tenantIdProvider);
            backfillMemoizedInvoiceLinesJob.setInvoiceService(invoiceService);
        }

        if (job instanceof CompareOrderLineToPreviewAmountsJob compareOrderLineToPreviewAmountsJob) {
            compareOrderLineToPreviewAmountsJob.setTenantIdProvider(tenantIdProvider);
            compareOrderLineToPreviewAmountsJob.setInvoiceService(invoiceService);
        }

        if (job instanceof AddressCountryDataMigrationJob addressCountryDataMigrationJob) {
            addressCountryDataMigrationJob.setAccountGetService(accountGetService);
            addressCountryDataMigrationJob.setAccountService(accountService);
        }

        if (job instanceof EntityTaggingBackfillJob entityTaggingBackfillJob) {
            entityTaggingBackfillJob.setEntityService(entityService);
        }

        if (job instanceof TransactionalExchangeBackfillJob transactionalExchangeBackfillJob) {
            transactionalExchangeBackfillJob.setTransactionalExchangeBackfillService(transactionalExchangeBackfillService);
        }

        if (job instanceof EntityInvariantsJob entityInvariantsJob) {
            entityInvariantsJob.setEntityInvariantsService(entityInvariantsService);
        }

        if (job instanceof ExchangeRateRefreshJob exchangeRateRefreshJob) {
            exchangeRateRefreshJob.setClock(clock);
            exchangeRateRefreshJob.setTenantJobDispatcherService(tenantJobDispatcherService);
            exchangeRateRefreshJob.setTenantSettingService(tenantSettingService);
            exchangeRateRefreshJob.setFeatureService(featureService);
            exchangeRateRefreshJob.setTransactionalExchangeRateService(transactionalExchangeRateService);
        }

        if (job instanceof RampGroupIdBackfillJob rampGroupIdBackfillJob) {
            rampGroupIdBackfillJob.setFeatureService(featureService);
            rampGroupIdBackfillJob.setOrderService(orderService);
        }

        if (job instanceof InvoiceConfigIdBackfillJob invoiceConfigIdBackfillJob) {
            invoiceConfigIdBackfillJob.setInvoiceConfigurationService(invoiceConfigurationService);
        }

        if (job instanceof BillyAdminReporterJob billyAdminReporterJob) {
            billyAdminReporterJob.setBillyConfiguration(billyConfiguration);
            billyAdminReporterJob.setUserService(userService);
            billyAdminReporterJob.setSesClientProvider(sesClientProvider);
        }

        if (job instanceof ReportingJob reportingJob) {
            reportingJob.setBillyConfiguration(billyConfiguration);
            reportingJob.setMetricsReportingService(metricsReportingService);
        }

        if (job instanceof ApprovalFlowEscalationJob approvalFlowEscalationJob) {
            approvalFlowEscalationJob.setBillyConfiguration(billyConfiguration);
            approvalFlowEscalationJob.setTenantIdProvider(tenantIdProvider);
            approvalFlowEscalationJob.setApprovalFlowInstanceService(approvalFlowInstanceService);
            approvalFlowEscalationJob.setEscalationPolicyService(escalationPolicyService);
            approvalFlowEscalationJob.setApprovalFlowStateUpdaterService(approvalFlowStateUpdaterService);
            approvalFlowEscalationJob.setFeatureService(featureService);
        }

        if (job instanceof QuartzApiKeyGarbageCollectorJob quartzApiKeyGarbageCollectorJob) {
            quartzApiKeyGarbageCollectorJob.setBillyConfiguration(billyConfiguration);
            quartzApiKeyGarbageCollectorJob.setTenantIdProvider(tenantIdProvider);
            quartzApiKeyGarbageCollectorJob.setTenantService(tenantService);
            quartzApiKeyGarbageCollectorJob.setApiKeyService(apiKeyService);
            quartzApiKeyGarbageCollectorJob.setEntityContextProvider(entityContextProvider);
            quartzApiKeyGarbageCollectorJob.setEntityGetService(entityGetService);
        }

        if (job instanceof OrderExpiryJob orderExpiryJob) {
            orderExpiryJob.setBillyConfiguration(billyConfiguration);
            orderExpiryJob.setTenantIdProvider(tenantIdProvider);
            orderExpiryJob.setTenantService(tenantService);
            orderExpiryJob.setOrderGetService(orderGetService);
            orderExpiryJob.setTenantSettingService(tenantSettingService);
            orderExpiryJob.setOrderService(orderService);
        }

        if (job instanceof NotificationRetryJob notificationRetryJob) {
            notificationRetryJob.setNotificationRetryService(notificationRetryService);
        }

        if (job instanceof RenewalOrderLinesSubscriptionChargeBackfillJob renewalOrderLinesSubscriptionChargeBackfillJob) {
            renewalOrderLinesSubscriptionChargeBackfillJob.setOrderService(orderService);
            renewalOrderLinesSubscriptionChargeBackfillJob.setOrderGetService(orderGetService);
            renewalOrderLinesSubscriptionChargeBackfillJob.setSubscriptionGetService(subscriptionGetService);
        }

        if (job instanceof BackfillArrCategoryForOrdersJob backfillArrCategoryForOrdersJob) {
            backfillArrCategoryForOrdersJob.setReportingJobQueueService(reportingJobQueueService);
            backfillArrCategoryForOrdersJob.setAccountMapper(Mappers.getMapper(AccountMapper.class));
        }

        if (job instanceof WebhookSigningKeyBackfillJob webhookSigningKeyBackfillJob) {
            webhookSigningKeyBackfillJob.setNotificationTargetService(notificationTargetService);
        }

        if (job instanceof TenantSettingBackfillJob tenantSettingBackfillJob) {
            tenantSettingBackfillJob.setTenantSettingService(tenantSettingService);
        }

        if (job instanceof LookerTenantBackfillJob lookerTenantBackfillJob) {
            lookerTenantBackfillJob.setLookerClient(lookerClient);
            lookerTenantBackfillJob.setLookerTenantDAO(lookerTenantDAO);
            lookerTenantBackfillJob.setLookerUserDAO(lookerUserDAO);
            lookerTenantBackfillJob.setBillyConfiguration(billyConfiguration);
            lookerTenantBackfillJob.setTenantService(tenantService);
            lookerTenantBackfillJob.setLookerTenantService(lookerTenantService);
        }

        if (job instanceof LookerTenantConfigExportJob lookerTenantConfigExportJob) {
            lookerTenantConfigExportJob.setLookerConfigurationExportService(lookerConfigurationExportService);
        }

        if (job instanceof GenerateReindexQueueJob generateReindexQueueJob) {
            generateReindexQueueJob.setSearchReindexService(searchReindexService);
            generateReindexQueueJob.setSearchReindexDAO(searchReindexDAO);
        }

        if (job instanceof AccountingInvariantsJob accountingInvariantsJob) {
            accountingInvariantsJob.setAccountingPeriodService(accountingPeriodService);
            accountingInvariantsJob.setRevenueRecognitionGetService(revenueRecognitionGetService);
        }

        if (job instanceof QuickbooksPingJob quickbooksPingJob) {
            quickbooksPingJob.setQuickbooksDataService(quickbooksDataService);
        }

        if (job instanceof TrackArrFlagBackfillJob trackArrFlagBackfillJob) {
            trackArrFlagBackfillJob.setProductCatalogGetService(productCatalogGetService);
            trackArrFlagBackfillJob.setProductCatalogService(productCatalogService);
        }

        if (job instanceof HubSpotEsignatureMigrationJob hubSpotEsignatureMigrationJob) {
            hubSpotEsignatureMigrationJob.setHubSpotInstallService(hubSpotInstallService);
            hubSpotEsignatureMigrationJob.setHubSpotIntegrationService(hubSpotIntegrationService);
        }

        if (job instanceof MultiEntityWireInstructionBackfillJob multiEntityWireInstructionBackfillJob) {
            multiEntityWireInstructionBackfillJob.setDocumentTemplateGetService(documentTemplateGetService);
            multiEntityWireInstructionBackfillJob.setEntityService(entityService);
        }

        if (job instanceof SubscriptionCustomFieldsBackfillJob subscriptionCustomFieldsBackfillJob) {
            subscriptionCustomFieldsBackfillJob.setCustomFieldService(customFieldService);
        }

        if (job instanceof MultiEntityApiKeyBackfillJob multiEntityApiKeyBackfillJob) {
            multiEntityApiKeyBackfillJob.setApiKeyService(apiKeyService);
        }

        if (job instanceof MultiEntityUserBackfillJob multiEntityUserBackfillJob) {
            multiEntityUserBackfillJob.setUserService(userService);
        }

        if (job instanceof CreditMemoDocumentGeneratorJob creditMemoDocumentGeneratorJob) {
            creditMemoDocumentGeneratorJob.setTenantIdProvider(tenantIdProvider);
            creditMemoDocumentGeneratorJob.setCreditMemoDocumentService(creditMemoDocumentService);
            creditMemoDocumentGeneratorJob.setDlqService(dlqService);
        }

        if (job instanceof BulkInvoiceRunIdBackfillJob bulkInvoiceRunIdBackfillJob) {
            bulkInvoiceRunIdBackfillJob.setBulkInvoiceService(bulkInvoiceService);
        }

        if (job instanceof AutomatedInvoiceRuleIdBackfillJob automatedInvoiceRuleIdBackfillJob) {
            automatedInvoiceRuleIdBackfillJob.setAutomatedInvoiceRuleGetService(automatedInvoiceRuleGetService);
            automatedInvoiceRuleIdBackfillJob.setAutomatedInvoiceRuleService(automatedInvoiceRuleService);
        }

        if (job instanceof EmailLastSentOnBackfillJob emailLastSentOnBackfillJob) {
            emailLastSentOnBackfillJob.setInvoiceRetrievalService(invoiceRetrievalService);
            emailLastSentOnBackfillJob.setInvoiceService(invoiceService);
            emailLastSentOnBackfillJob.setEmailService(emailService);
        }

        if (job instanceof CrmResyncAfterSpecificDateJob crmResyncAfterSpecificDateJob) {
            crmResyncAfterSpecificDateJob.setCrmService(crmService);
            crmResyncAfterSpecificDateJob.setOrderGetService(orderGetService);
            crmResyncAfterSpecificDateJob.setAccountGetService(accountGetService);
            crmResyncAfterSpecificDateJob.setAccountMapper(Mappers.getMapper(AccountMapper.class));
        }

        if (job instanceof BulkRevenueRecognitionJob bulkRevenueRecognitionJob) {
            bulkRevenueRecognitionJob.setBulkRevenueService(bulkRevenueService);
        }

        return job;
    }
}
