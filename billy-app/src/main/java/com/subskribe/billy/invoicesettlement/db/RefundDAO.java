package com.subskribe.billy.invoicesettlement.db;

import static com.subskribe.billy.jooq.default_schema.tables.CreditMemo.CREDIT_MEMO;
import static com.subskribe.billy.jooq.default_schema.tables.Refund.REFUND;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.invoicesettlement.mapper.RefundRecordMapper;
import com.subskribe.billy.invoicesettlement.model.CreditMemoBalance;
import com.subskribe.billy.invoicesettlement.model.Refund;
import com.subskribe.billy.jooq.default_schema.tables.records.RefundRecord;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.SelectOnConditionStep;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class RefundDAO {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefundDAO.class);

    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final RefundRecordMapper refundRecordMapper;
    private final InvoiceSettlementDAO invoiceSettlementDAO;

    @Inject
    public RefundDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider, InvoiceSettlementDAO invoiceSettlementDAO) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        this.invoiceSettlementDAO = invoiceSettlementDAO;
        refundRecordMapper = Mappers.getMapper(RefundRecordMapper.class);
    }

    public Refund createAndApplyRefund(CreditMemoBalance balance, Refund refund, Configuration configuration) {
        var record = refundRecordMapper.toRefundRecord(refund);
        record.reset(REFUND.ID);
        record.setTenantId(tenantIdProvider.provideTenantIdString());
        return applyRefund(record, balance, configuration);
    }

    private Refund applyRefund(RefundRecord record, CreditMemoBalance balance, Configuration configuration) {
        DSLContext transactionContext = DSL.using(configuration);

        var insertedRefund = transactionContext.insertInto(REFUND).set(record).returning().fetchOneInto(RefundRecord.class);

        invoiceSettlementDAO.updateCreditMemoBalance(
            tenantIdProvider.provideTenantIdString(),
            balance,
            record.getAmount(),
            transactionContext,
            LocalDateTime.now()
        );

        return refundRecordMapper.toRefund(insertedRefund);
    }

    public List<Refund> getRefunds(String accountId, String creditMemoNumber) {
        var returning = getJoinedRefundAndCMTables()
            .where(CREDIT_MEMO.ACCOUNT_ID.eq(accountId))
            .and(REFUND.CREDIT_MEMO_NUMBER.eq(creditMemoNumber))
            .fetchInto(RefundRecord.class);
        return refundRecordMapper.toRefunds(returning);
    }

    public List<Refund> getRefunds(String accountId) {
        var returning = getJoinedRefundAndCMTables().where(CREDIT_MEMO.ACCOUNT_ID.eq(accountId)).fetchInto(RefundRecord.class);
        return refundRecordMapper.toRefunds(returning);
    }

    public Optional<Refund> getRefundById(String refundId, String accountId) {
        var returning = getJoinedRefundAndCMTables()
            .where(CREDIT_MEMO.ACCOUNT_ID.eq(accountId))
            .and(REFUND.REFUND_ID.eq(refundId))
            .fetchOneInto(RefundRecord.class);
        return Optional.ofNullable(refundRecordMapper.toRefund(returning));
    }

    public List<Refund> getRefundsByPaymentId(String paymentId) {
        DSLContext context = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var returning = context
            .select()
            .from(REFUND)
            .where(REFUND.PAYMENT_ID.eq(paymentId).and(REFUND.TENANT_ID.eq(tenantIdProvider.provideTenantIdString())))
            .fetchInto(RefundRecord.class);
        return refundRecordMapper.toRefunds(returning);
    }

    public void ensureUniqueRefundId(DSLContext dslContext, String refundId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(REFUND)
            .where(REFUND.REFUND_ID.eq(refundId))
            .and(REFUND.TENANT_ID.eq(tenantId))
            .fetchOneInto(RefundRecord.class);

        if (record == null) {
            return;
        }

        throwDuplicateRefundIdException(refundId);
    }

    private void throwDuplicateRefundIdException(String refundId) {
        var message = "Duplicate refundId generated. refundId = " + refundId;
        LOGGER.info(message);
        throw new DuplicateIdException(message);
    }

    private SelectOnConditionStep<Record> getJoinedRefundAndCMTables() {
        DSLContext context = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return context
            .select()
            .from(REFUND)
            .leftJoin(CREDIT_MEMO)
            .on(REFUND.CREDIT_MEMO_NUMBER.eq(CREDIT_MEMO.CREDIT_MEMO_NUMBER).and(REFUND.TENANT_ID.eq(CREDIT_MEMO.TENANT_ID)));
    }
}
