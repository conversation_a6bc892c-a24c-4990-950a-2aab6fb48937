package com.subskribe.billy.invoicesettlement.db;

import static com.subskribe.billy.jooq.default_schema.tables.CreditMemoBalance.CREDIT_MEMO_BALANCE;
import static com.subskribe.billy.jooq.default_schema.tables.Invoice.INVOICE;
import static com.subskribe.billy.jooq.default_schema.tables.InvoiceBalance.INVOICE_BALANCE;
import static com.subskribe.billy.jooq.default_schema.tables.InvoiceLineItem.INVOICE_LINE_ITEM;
import static com.subskribe.billy.jooq.default_schema.tables.PaymentBalance.PAYMENT_BALANCE;
import static com.subskribe.billy.jooq.default_schema.tables.SettlementApplication.SETTLEMENT_APPLICATION;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoicesettlement.mapper.CreditMemoBalanceRecordMapper;
import com.subskribe.billy.invoicesettlement.mapper.InvoiceBalanceRecordMapper;
import com.subskribe.billy.invoicesettlement.mapper.PaymentBalanceRecordMapper;
import com.subskribe.billy.invoicesettlement.mapper.SettlementApplicationRecordMapper;
import com.subskribe.billy.invoicesettlement.model.CreditMemoBalance;
import com.subskribe.billy.invoicesettlement.model.InvoiceBalance;
import com.subskribe.billy.invoicesettlement.model.PaymentBalance;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationStatus;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationType;
import com.subskribe.billy.jooq.default_schema.tables.records.CreditMemoBalanceRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.InvoiceBalanceRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.InvoiceRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.SettlementApplicationRecord;
import com.subskribe.billy.postgres.PostgresAdvisoryLock;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ConcurrentModificationException;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class InvoiceSettlementDAO {

    private static final String BALANCE_UNIQUE_CONSTRAINT_NAME = "index_credit_memo_balance_tenant_id_credit_memo_number";
    private static final Logger LOGGER = LoggerFactory.getLogger(InvoiceSettlementDAO.class);
    private static final String SETTLEMENT_APPLICATION_LOCK_FORMAT = "%s/%s/settlement_application_lock";

    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final InvoiceBalanceRecordMapper invoiceBalanceRecordMapper;
    private final SettlementApplicationRecordMapper settlementApplicationRecordMapper;
    private final PaymentBalanceRecordMapper paymentBalanceRecordMapper;
    private final CreditMemoBalanceRecordMapper creditMemoBalanceRecordMapper;

    @Inject
    public InvoiceSettlementDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        paymentBalanceRecordMapper = Mappers.getMapper(PaymentBalanceRecordMapper.class);
        invoiceBalanceRecordMapper = Mappers.getMapper(InvoiceBalanceRecordMapper.class);
        settlementApplicationRecordMapper = Mappers.getMapper(SettlementApplicationRecordMapper.class);
        creditMemoBalanceRecordMapper = Mappers.getMapper(CreditMemoBalanceRecordMapper.class);
    }

    public SettlementApplication getSettlementApplication(UUID settlementApplicationId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext context = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var record = context.selectFrom(SETTLEMENT_APPLICATION).where(SETTLEMENT_APPLICATION.ID.eq(settlementApplicationId)).fetchOne();

        if (record == null) {
            throw new ObjectNotFoundException(BillyObjectType.INVOICE_SETTLEMENT_APPLICATION, settlementApplicationId.toString());
        }

        return settlementApplicationRecordMapper.recordToSettlementApplication(record);
    }

    // TODO: Are we assuming NULL status is an applied one?
    public List<SettlementApplication> getAppliedSettlementApplications(Invoice.Number invoiceNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var settlementApplications = dslContext
            .select()
            .from(SETTLEMENT_APPLICATION)
            .where(SETTLEMENT_APPLICATION.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .and(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
            .and(SETTLEMENT_APPLICATION.STATUS.isNull().or(SETTLEMENT_APPLICATION.STATUS.eq(SettlementApplicationStatus.APPLIED_PAYMENT.name())))
            .fetchInto(SettlementApplicationRecord.class);
        return settlementApplicationRecordMapper.recordsToSettlementApplication(settlementApplications);
    }

    public List<SettlementApplication> getSettlementApplications(Invoice.Number invoiceNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var settlementApplications = dslContext
            .select()
            .from(SETTLEMENT_APPLICATION)
            .where(SETTLEMENT_APPLICATION.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .and(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
            .fetchInto(SettlementApplicationRecord.class);
        return settlementApplicationRecordMapper.recordsToSettlementApplication(settlementApplications);
    }

    public List<SettlementApplication> getSettlementApplicationsForCreditMemo(String creditMemoNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var settlementApplications = dslContext
            .select()
            .from(SETTLEMENT_APPLICATION)
            .where(SETTLEMENT_APPLICATION.CREDIT_MEMO_NUMBER.eq(creditMemoNumber))
            .and(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
            .and(
                SETTLEMENT_APPLICATION.APPLICATION_TYPE.eq(SettlementApplicationType.CREDIT.name()).or(
                    SETTLEMENT_APPLICATION.APPLICATION_TYPE.eq(SettlementApplicationType.UNAPPLY_CREDIT.name())
                )
            )
            .and(SETTLEMENT_APPLICATION.STATUS.isNull().or(SETTLEMENT_APPLICATION.STATUS.eq(SettlementApplicationStatus.APPLIED_PAYMENT.name())))
            .fetchInto(SettlementApplicationRecord.class);
        return settlementApplicationRecordMapper.recordsToSettlementApplication(settlementApplications);
    }

    public Optional<SettlementApplication> getPendingPaymentCollectionAttemptForInvoice(DSLContext dslContext, String invoiceNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        List<SettlementApplicationRecord> records = dslContext
            .select()
            .from(SETTLEMENT_APPLICATION)
            .where(SETTLEMENT_APPLICATION.INVOICE_NUMBER.eq(invoiceNumber))
            .and(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
            .and(
                SETTLEMENT_APPLICATION.STATUS.isNotNull()
                    .and(SETTLEMENT_APPLICATION.STATUS.eq(SettlementApplicationStatus.ATTEMPTING_PAYMENT_COLLECTION.name()))
            )
            .fetchInto(SettlementApplicationRecord.class);

        List<SettlementApplication> settlementApplications = settlementApplicationRecordMapper.recordsToSettlementApplication(records);
        return settlementApplications.stream().findAny(); // TODO : Why is this findAny and not findOne ?
    }

    public SettlementApplication addPaymentApplication(
        Configuration configuration,
        SettlementApplication settlementApplication,
        InvoiceBalance currentInvoiceBalance,
        PaymentBalance currentPaymentBalance
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var amount = settlementApplication.getAmount();
        var currentTimestamp = DateTimeConverter.instantToLocalDateTime(Instant.now());

        try {
            SettlementApplication savedApplication = upsertSettlementApplication(
                settlementApplication,
                tenantId,
                Optional.of(configuration),
                Optional.of(currentTimestamp)
            );
            updateInvoiceBalance(currentInvoiceBalance, amount, configuration, currentTimestamp);
            updatePaymentBalance(currentPaymentBalance, amount, configuration, currentTimestamp);
            return savedApplication;
        } catch (DataAccessException ex) {
            if (PostgresErrorHandler.causeIsUniqueConstraintViolation(ex)) {
                LOGGER.warn("Duplicate key applying new payment application", ex);
                throw new ConcurrentModificationException("Record being inserted already exists");
            }

            throw ex;
        }
    }

    public SettlementApplication addCreditApplication(
        SettlementApplication settlementApplication,
        InvoiceBalance currentInvoiceBalance,
        CreditMemoBalance currentCreditMemoBalance
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var amount = settlementApplication.getAmount();
        var currentTimestamp = DateTimeConverter.instantToLocalDateTime(Instant.now());

        return dslContext.transactionResult(configuration -> {
            try {
                SettlementApplication savedSettlement = upsertSettlementApplication(
                    settlementApplication,
                    tenantId,
                    Optional.of(configuration),
                    Optional.of(currentTimestamp)
                );
                updateInvoiceBalance(currentInvoiceBalance, amount, configuration, currentTimestamp);
                updateCreditMemoBalance(
                    tenantIdProvider.provideTenantIdString(),
                    creditMemoBalanceRecordMapper,
                    currentCreditMemoBalance,
                    amount,
                    DSL.using(configuration),
                    currentTimestamp
                );
                return savedSettlement;
            } catch (DataAccessException ex) {
                if (PostgresErrorHandler.causeIsUniqueConstraintViolation(ex)) {
                    LOGGER.warn("Duplicate key inserting payment balance", ex);
                    throw new ConcurrentModificationException("Record being inserted already exists");
                }
                throw new ServiceFailureException("unable to save credit application", ex);
            }
        });
    }

    public List<String> getInvoicesAppliedFromCreditMemo(String creditMemoNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return dslContext
            .select(SETTLEMENT_APPLICATION.INVOICE_NUMBER)
            .from(SETTLEMENT_APPLICATION)
            .where(SETTLEMENT_APPLICATION.CREDIT_MEMO_NUMBER.eq(creditMemoNumber).and(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId)))
            .fetchInto(String.class);
    }

    // TODO: Duplicate code found in InvoiceStatusUpdater
    private void setInvoiceStatus(
        String invoiceNumber,
        InvoiceStatus currentStatus,
        InvoiceStatus newStatus,
        String tenantId,
        Configuration configuration,
        LocalDateTime updatedOn
    ) {
        DSLContext dslContext = DSL.using(configuration);
        InvoiceRecord invoiceRecord = dslContext
            .update(INVOICE)
            .set(INVOICE.STATUS, newStatus.name())
            .set(INVOICE.SHOULD_REGENERATE_PDF, true)
            .set(INVOICE.UPDATED_ON, updatedOn)
            .where(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.INVOICE_NUMBER.eq(invoiceNumber))
            .and(INVOICE.STATUS.eq(currentStatus.name()))
            .returning()
            .fetchOne();

        if (invoiceRecord == null) {
            throw new ConcurrentModificationException("Optimistic lock failure updating invoice");
        }

        dslContext
            .update(INVOICE_LINE_ITEM)
            .set(INVOICE_LINE_ITEM.STATUS, newStatus.name())
            .where(INVOICE_LINE_ITEM.INVOICE_ID.eq(invoiceRecord.getId()))
            .and(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.STATUS.eq(currentStatus.name()))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .execute();
    }

    public InvoiceBalance updateInvoiceBalance(
        InvoiceBalance currentInvoiceBalance,
        BigDecimal amount,
        Configuration configuration,
        LocalDateTime updatedOn
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        BigDecimal updatedInvoiceBalance = currentInvoiceBalance.getBalance().subtract(amount);
        if (Numbers.isNegative(updatedInvoiceBalance)) {
            throw new IllegalArgumentException("Cannot apply a settlement application that exceeds the invoice balance");
        }

        InvoiceBalanceRecord result;

        if (currentInvoiceBalance.getId() == null) {
            var invoiceBalanceRecord = invoiceBalanceRecordMapper.invoiceBalanceToRecord(currentInvoiceBalance);
            invoiceBalanceRecord.reset(INVOICE_BALANCE.ID);
            invoiceBalanceRecord.setBalance(updatedInvoiceBalance);
            invoiceBalanceRecord.setTenantId(tenantId);
            invoiceBalanceRecord.setUpdatedOn(DateTimeConverter.instantToLocalDateTime(currentInvoiceBalance.getUpdatedOn()));

            result = DSL.using(configuration)
                .insertInto(INVOICE_BALANCE)
                .set(invoiceBalanceRecord)
                .onConflictDoNothing()
                .returning()
                .fetchOneInto(InvoiceBalanceRecord.class);
        } else {
            result = DSL.using(configuration)
                .update(INVOICE_BALANCE)
                .set(INVOICE_BALANCE.BALANCE, updatedInvoiceBalance)
                .set(INVOICE_BALANCE.UPDATED_ON, updatedOn)
                .where(INVOICE_BALANCE.TENANT_ID.eq(tenantId))
                .and(INVOICE_BALANCE.ID.eq(currentInvoiceBalance.getId()))
                .and(INVOICE_BALANCE.BALANCE.eq(currentInvoiceBalance.getBalance()))
                .returning()
                .fetchOneInto(InvoiceBalanceRecord.class);
        }

        if (result == null) {
            String message = String.format(
                "Payment or Credit memo applied on Invoice: %s while updating its balance.",
                currentInvoiceBalance.getInvoiceNumber()
            );

            throw new ConcurrentModificationException(message);
        }

        if (currentInvoiceBalance.getBalance().signum() > 0 && updatedInvoiceBalance.signum() <= 0) {
            setInvoiceStatus(currentInvoiceBalance.getInvoiceNumber(), InvoiceStatus.POSTED, InvoiceStatus.PAID, tenantId, configuration, updatedOn);
        } else if (currentInvoiceBalance.getBalance().signum() <= 0 && updatedInvoiceBalance.signum() > 0) {
            setInvoiceStatus(currentInvoiceBalance.getInvoiceNumber(), InvoiceStatus.PAID, InvoiceStatus.POSTED, tenantId, configuration, updatedOn);
        }

        return invoiceBalanceRecordMapper.recordToInvoiceBalance(result);
    }

    private void updatePaymentBalance(PaymentBalance currentPaymentBalance, BigDecimal amount, Configuration configuration, LocalDateTime updatedOn) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        BigDecimal updatedPaymentBalance = currentPaymentBalance.getBalance().subtract(amount);
        if (Numbers.isNegative(updatedPaymentBalance)) {
            throw new IllegalArgumentException("Cannot apply a payment that exceeds the payment balance");
        }

        if (currentPaymentBalance.getId() == null) {
            var paymentBalanceRecord = paymentBalanceRecordMapper.paymentBalanceToRecord(currentPaymentBalance);
            paymentBalanceRecord.reset(PAYMENT_BALANCE.ID);
            paymentBalanceRecord.setTenantId(tenantId);
            paymentBalanceRecord.setUpdatedOn(updatedOn);
            paymentBalanceRecord.setBalance(updatedPaymentBalance);

            DSL.using(configuration).insertInto(PAYMENT_BALANCE).set(paymentBalanceRecord).execute();
        } else {
            var result = DSL.using(configuration)
                .update(PAYMENT_BALANCE)
                .set(PAYMENT_BALANCE.BALANCE, updatedPaymentBalance)
                .set(PAYMENT_BALANCE.UPDATED_ON, updatedOn)
                .where(PAYMENT_BALANCE.ID.eq(currentPaymentBalance.getId()))
                .and(PAYMENT_BALANCE.BALANCE.eq(currentPaymentBalance.getBalance()))
                .returning()
                .fetchOne();

            if (result == null) {
                throw new ConcurrentModificationException("Optimistic lock failure updating payment balance");
            }
        }
    }

    public static void updateCreditMemoBalance(
        String tenantId,
        CreditMemoBalanceRecordMapper creditMemoBalanceRecordMapper,
        CreditMemoBalance currentCreditMemoBalance,
        BigDecimal amount,
        DSLContext context,
        LocalDateTime updatedOn
    ) {
        BigDecimal updatedCreditMemoBalance = currentCreditMemoBalance.getBalance().subtract(amount);
        if (Numbers.isNegative(updatedCreditMemoBalance)) {
            throw new IllegalArgumentException("Cannot apply a credit memo that exceeds the credit memo balance");
        }

        if (currentCreditMemoBalance.getId() == null) {
            CreditMemoBalanceRecord creditMemoBalanceRecord = creditMemoBalanceRecordMapper.creditMemoBalanceToRecord(currentCreditMemoBalance);
            creditMemoBalanceRecord.reset(CREDIT_MEMO_BALANCE.ID);
            creditMemoBalanceRecord.setTenantId(tenantId);
            creditMemoBalanceRecord.setUpdatedOn(updatedOn);
            creditMemoBalanceRecord.setBalance(updatedCreditMemoBalance);

            PostgresErrorHandler.withConstraintAsConflict(
                () -> context.insertInto(CREDIT_MEMO_BALANCE).set(creditMemoBalanceRecord).execute(),
                BALANCE_UNIQUE_CONSTRAINT_NAME,
                String.format("credit memo balance exists with credit memo number: %s", currentCreditMemoBalance.getCreditMemoNumber())
            );
        } else {
            var result = context
                .update(CREDIT_MEMO_BALANCE)
                .set(CREDIT_MEMO_BALANCE.BALANCE, updatedCreditMemoBalance)
                .set(CREDIT_MEMO_BALANCE.UPDATED_ON, updatedOn)
                .where(CREDIT_MEMO_BALANCE.ID.eq(currentCreditMemoBalance.getId()))
                .and(CREDIT_MEMO_BALANCE.BALANCE.eq(currentCreditMemoBalance.getBalance()))
                .returning()
                .fetchOne();

            if (result == null) {
                throw new ConcurrentModificationException("Optimistic lock failure updating credit memo balance");
            }
        }
    }

    public SettlementApplication addPendingAutomaticPaymentApplication(SettlementApplication settlementApplication, Configuration configuration) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        // Acquire a lock to prevent concurrent settlement application creation for the same invoice as we don't have a db index, this method is invoked in both one time and automatic flows
        tryAndAcquireSettlementApplicationLock(DSL.using(configuration), settlementApplication.getInvoiceNumber());

        SettlementApplication savedSettlementApplication = upsertSettlementApplication(
            settlementApplication,
            tenantId,
            Optional.ofNullable(configuration),
            Optional.empty()
        );
        validateInvoiceBalanceForPendingAutomaticPaymentApplication(
            savedSettlementApplication,
            Optional.ofNullable(configuration).map(DSL::using).orElseGet(() -> dslContextProvider.get(tenantId))
        );
        return savedSettlementApplication;
    }

    private void validateInvoiceBalanceForPendingAutomaticPaymentApplication(
        SettlementApplication settlementApplication,
        DSLContext transactionContext
    ) {
        // Run an update query with returning to check if the invoice balance is still valid
        Optional<InvoiceBalance> invoiceBalanceOptional = getInvoiceBalance(new Invoice.Number(settlementApplication.getInvoiceNumber()));
        if (invoiceBalanceOptional.isEmpty()) {
            // There is no settlement application for this invoice yet, so this is a valid state.
            return;
        }
        InvoiceBalance invoiceBalance = invoiceBalanceOptional.get();
        int count = transactionContext
            .update(INVOICE_BALANCE)
            .set(INVOICE_BALANCE.BALANCE, invoiceBalance.getBalance())
            .where(INVOICE_BALANCE.ID.eq(invoiceBalance.getId()))
            .and(INVOICE_BALANCE.BALANCE.eq(invoiceBalance.getBalance()))
            .execute();
        if (count == 0) {
            throw new ConcurrentModificationException("Invoice balance has changed since the settlement application was created");
        }
    }

    private SettlementApplication upsertSettlementApplication(
        SettlementApplication settlementApplication,
        String tenantId,
        Optional<Configuration> configuration,
        Optional<LocalDateTime> createdOn
    ) {
        DSLContext dslContext = configuration
            .map(DSL::using)
            .orElseGet(() -> TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider));

        validateNoOtherExistingPaymentCollectionAttemptExistsForInvoice(settlementApplication, dslContext);

        // TODO: should this be part of service layer?
        if (settlementApplication.getId() != null && settlementApplication.getStatus() == SettlementApplicationStatus.ATTEMPTING_PAYMENT_COLLECTION) {
            return updatePendingSettlementApplication(
                settlementApplication.getId(),
                SettlementApplicationStatus.APPLIED_PAYMENT,
                settlementApplication.getAppliedOn(),
                Optional.of(dslContext)
            );
        }

        SettlementApplicationRecord record = generateSettlementApplicationRecord(settlementApplication, tenantId, createdOn);
        SettlementApplicationRecord result = dslContext.insertInto(SETTLEMENT_APPLICATION).set(record).returning().fetchOne();
        if (result == null) {
            throw new ServiceFailureException("Failed to insert settlement application: " + settlementApplication);
        }
        return settlementApplicationRecordMapper.recordToSettlementApplication(result);
    }

    private void tryAndAcquireSettlementApplicationLock(DSLContext transactionContext, String invoiceNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        String key = String.format(SETTLEMENT_APPLICATION_LOCK_FORMAT, tenantId, invoiceNumber);
        Optional<Long> lock = PostgresAdvisoryLock.tryAcquireLock(transactionContext, key);
        if (lock.isEmpty()) {
            LOGGER.info("Settlement application lock acquisition failed for invoice {}", invoiceNumber);
            throw new ConflictingStateException("A settlement application creation is already in progress for this invoice. Please try again later.");
        }
    }

    private SettlementApplicationRecord generateSettlementApplicationRecord(
        SettlementApplication settlementApplication,
        String tenantId,
        Optional<LocalDateTime> createdOn
    ) {
        SettlementApplicationRecord record = settlementApplicationRecordMapper.settlementApplicationToRecord(settlementApplication);
        record.reset(SETTLEMENT_APPLICATION.ID);
        record.setTenantId(tenantId);
        createdOn.ifPresent(record::setCreatedOn);
        return record;
    }

    private void validateNoOtherExistingPaymentCollectionAttemptExistsForInvoice(SettlementApplication settlementApplication, DSLContext dslContext) {
        String invoiceNumber = settlementApplication.getInvoiceNumber();
        Optional<SettlementApplication> existingAttempt = getPendingPaymentCollectionAttemptForInvoice(dslContext, invoiceNumber);

        if (existingAttempt.isEmpty()) {
            return;
        }

        // If we are applying payment to a pending settlement application, it can go through.
        if (settlementApplication.getId() != null && existingAttempt.get().getId().equals(settlementApplication.getId())) {
            return;
        }

        throw new IllegalStateException("Found a pending payment for this invoice number: " + existingAttempt.get());
    }

    public Optional<InvoiceBalance> getInvoiceBalance(Invoice.Number invoiceNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var invoiceBalanceRecord = dslContext
            .select()
            .from(INVOICE_BALANCE)
            .where(INVOICE_BALANCE.TENANT_ID.eq(tenantId))
            .and(INVOICE_BALANCE.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .fetchOneInto(InvoiceBalanceRecord.class);

        return Optional.ofNullable(invoiceBalanceRecordMapper.recordToInvoiceBalance(invoiceBalanceRecord));
    }

    public List<InvoiceBalance> getInvoiceBalances(List<Invoice.Number> invoiceNumbers) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        List<InvoiceBalanceRecord> invoiceBalanceRecords = dslContext
            .select()
            .from(INVOICE_BALANCE)
            .where(INVOICE_BALANCE.TENANT_ID.eq(tenantId))
            .and(INVOICE_BALANCE.INVOICE_NUMBER.in(invoiceNumbers))
            .fetchInto(InvoiceBalanceRecord.class);

        return invoiceBalanceRecordMapper.recordsToInvoiceBalanceList(invoiceBalanceRecords);
    }

    public Optional<PaymentBalance> getPaymentBalance(String paymentId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var paymentBalanceRecord = dslContext
            .selectFrom(PAYMENT_BALANCE)
            .where(PAYMENT_BALANCE.TENANT_ID.eq(tenantId))
            .and(PAYMENT_BALANCE.PAYMENT_ID.eq(paymentId))
            .fetchOne();

        return Optional.ofNullable(paymentBalanceRecordMapper.recordToPaymentBalance(paymentBalanceRecord));
    }

    public Optional<CreditMemoBalance> getCreditMemoBalance(String creditMemoNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var record = dslContext
            .selectFrom(CREDIT_MEMO_BALANCE)
            .where(CREDIT_MEMO_BALANCE.TENANT_ID.eq(tenantId))
            .and(CREDIT_MEMO_BALANCE.CREDIT_MEMO_NUMBER.eq(creditMemoNumber))
            .fetchOne();

        return Optional.ofNullable(creditMemoBalanceRecordMapper.recordToCreditMemoBalance(record));
    }

    public List<SettlementApplication> getSettlementApplicationsForPaymentId(String paymentId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        List<SettlementApplicationRecord> settlementApplications = dslContext
            .select()
            .from(SETTLEMENT_APPLICATION)
            .where(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
            .and(SETTLEMENT_APPLICATION.PAYMENT_ID.eq(paymentId))
            .fetchInto(SettlementApplicationRecord.class);
        return settlementApplicationRecordMapper.recordsToSettlementApplication(settlementApplications);
    }

    public SettlementApplication updatePendingSettlementApplication(
        UUID settlementApplicationId,
        SettlementApplicationStatus status,
        Instant appliedOn,
        Optional<DSLContext> dslContext
    ) {
        validateAppliedOnForPendingSettlementApplication(appliedOn);
        String tenantId = tenantIdProvider.provideTenantIdString();

        SettlementApplicationRecord result = dslContext
            .orElse(TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider))
            .update(SETTLEMENT_APPLICATION)
            .set(SETTLEMENT_APPLICATION.STATUS, status.name())
            .set(SETTLEMENT_APPLICATION.APPLIED_ON, DateTimeConverter.instantToLocalDateTime(appliedOn))
            .where(SETTLEMENT_APPLICATION.ID.eq(settlementApplicationId))
            .and(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
            .returning()
            .fetchOne();

        if (result == null) {
            throw new ServiceFailureException("Unable to update settlement application's status: " + settlementApplicationId);
        }

        return settlementApplicationRecordMapper.recordToSettlementApplication(result);
    }

    private void validateAppliedOnForPendingSettlementApplication(Instant appliedOn) {
        if (appliedOn == null) {
            throw new InvariantCheckFailedException("Applied on must be set for a pending settlement application");
        }
    }

    public List<SettlementApplication> getFailedSettlementApplications(Invoice.Number invoiceNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<SettlementApplicationRecord> failedSettlementApplications = dslContext
            .select()
            .from(SETTLEMENT_APPLICATION)
            .where(SETTLEMENT_APPLICATION.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .and(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
            .and(SETTLEMENT_APPLICATION.STATUS.isNotNull().and(SETTLEMENT_APPLICATION.STATUS.eq(SettlementApplicationStatus.FAILED.name())))
            .fetchInto(SettlementApplicationRecord.class);
        return settlementApplicationRecordMapper.recordsToSettlementApplication(failedSettlementApplications);
    }

    public SettlementApplication addVoidPaymentApplication(
        Configuration configuration,
        SettlementApplication settlementApplication,
        InvoiceBalance currentInvoiceBalance,
        PaymentBalance currentPaymentBalance
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        BigDecimal amount = settlementApplication.getAmount();
        LocalDateTime currentTimestamp = DateTimeConverter.instantToLocalDateTime(Instant.now());
        try {
            SettlementApplication savedApplication = upsertSettlementApplication(
                settlementApplication,
                tenantId,
                Optional.of(configuration),
                Optional.of(currentTimestamp)
            );
            updateInvoiceBalance(currentInvoiceBalance, amount, configuration, currentTimestamp);
            if (currentPaymentBalance.getBalance().compareTo(BigDecimal.ZERO) != 0) {
                updatePaymentBalance(currentPaymentBalance, currentPaymentBalance.getBalance(), configuration, currentTimestamp);
            }
            return savedApplication;
        } catch (DataAccessException ex) {
            if (PostgresErrorHandler.causeIsUniqueConstraintViolation(ex)) {
                LOGGER.warn("Duplicate key applying new payment application", ex);
                throw new ConcurrentModificationException("Record being inserted already exists");
            }
            throw ex;
        }
    }

    public List<SettlementApplication> getCreditSettlementsWithoutUnapplication(Invoice.Number invoiceNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        List<SettlementApplicationRecord> settlementApplications = dslContext
            .select()
            .from(SETTLEMENT_APPLICATION)
            .where(SETTLEMENT_APPLICATION.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .and(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
            .and(SETTLEMENT_APPLICATION.APPLICATION_TYPE.eq(SettlementApplicationType.CREDIT.toString()))
            .and(
                SETTLEMENT_APPLICATION.ID.notIn(
                    dslContext
                        .select(SETTLEMENT_APPLICATION.NEGATED_SETTLEMENT_ID)
                        .from(SETTLEMENT_APPLICATION)
                        .where(SETTLEMENT_APPLICATION.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
                        .and(SETTLEMENT_APPLICATION.TENANT_ID.eq(tenantId))
                        .and(SETTLEMENT_APPLICATION.APPLICATION_TYPE.eq(SettlementApplicationType.CREDIT.toString()))
                        .and(SETTLEMENT_APPLICATION.NEGATED_SETTLEMENT_ID.isNotNull())
                )
            )
            .fetchInto(SettlementApplicationRecord.class);
        return settlementApplicationRecordMapper.recordsToSettlementApplication(settlementApplications);
    }
}
