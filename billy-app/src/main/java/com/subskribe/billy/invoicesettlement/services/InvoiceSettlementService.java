package com.subskribe.billy.invoicesettlement.services;

import static com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadCsvSchema.AMOUNT_HEADER;
import static com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadCsvSchema.BANK_FEE_HEADER;
import static com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadCsvSchema.BULK_PAYMENT_UPLOAD_DATETIME_FORMAT;
import static com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadCsvSchema.BULK_PAYMENT_UPLOAD_HEADERS;
import static com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadCsvSchema.BULK_PAYMENT_WITH_BANKFEE_UPLOAD_HEADERS;
import static com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadCsvSchema.INVOICE_NUMBER_HEADER;
import static com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadCsvSchema.NOTE_HEADER;
import static com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadCsvSchema.PAYMENT_BANK_ACCOUNT_ID_HEADER;
import static com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadCsvSchema.PAYMENT_DATE_HEADER;
import static com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadCsvSchema.PAYMENT_METHOD_HEADER;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.account.model.AccountPaymentMethodStatus;
import com.subskribe.billy.account.services.AccountPaymentMethodGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodService;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.dlq.model.DLQMessageType;
import com.subskribe.billy.dlq.service.DLQService;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.payload.PaymentProcessedEventPayload;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.foreignexchange.model.ImmutableRealizedGainLossInput;
import com.subskribe.billy.foreignexchange.model.RealizedGainLossInput;
import com.subskribe.billy.foreignexchange.service.RealizedGainLossService;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceEventService;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoicesettlement.db.InvoiceSettlementDAO;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.model.CreditMemoBalance;
import com.subskribe.billy.invoicesettlement.model.CreditMemoStatus;
import com.subskribe.billy.invoicesettlement.model.InvoiceBalance;
import com.subskribe.billy.invoicesettlement.model.PaymentBalance;
import com.subskribe.billy.invoicesettlement.model.Refund;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationStatus;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationType;
import com.subskribe.billy.invoicesettlement.model.SettlementBalance;
import com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadData;
import com.subskribe.billy.invoicesettlement.model.bulkpayment.BulkPaymentUploadResult;
import com.subskribe.billy.invoicesettlement.model.bulkpayment.ImmutableBulkPaymentUploadData;
import com.subskribe.billy.invoicesettlement.model.bulkpayment.ImmutableBulkPaymentUploadResult;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.model.PaymentAttempt;
import com.subskribe.billy.payment.model.PaymentBankAccount;
import com.subskribe.billy.payment.model.PaymentLifecycleType;
import com.subskribe.billy.payment.services.PaymentBankAccountService;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.payment.services.PaymentService;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.scheduler.job.InvoiceDocumentGeneratorJob;
import com.subskribe.billy.scheduler.model.QuartzJobType;
import com.subskribe.billy.scheduler.service.QuartzSchedulerService;
import com.subskribe.billy.shared.Constants;
import com.subskribe.billy.shared.enums.PaymentState;
import com.subskribe.billy.shared.enums.PaymentStatus;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import dev.failsafe.Failsafe;
import dev.failsafe.RetryPolicy;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.ConcurrentModificationException;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.quartz.SchedulerException;
import org.quartz.SimpleScheduleBuilder;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class InvoiceSettlementService {

    private static final com.subskribe.billy.shared.logger.Logger LOGGER = LoggerFactory.getLogger(InvoiceSettlementService.class);

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    private static final CSVFormat BULK_PAYMENT_UPLOAD_CSV_FORMAT = CSVFormat.DEFAULT.builder()
        .setHeader(BULK_PAYMENT_UPLOAD_HEADERS.toArray(String[]::new))
        .setAllowMissingColumnNames(false)
        .setDuplicateHeaderMode(DuplicateHeaderMode.DISALLOW)
        .setTrim(true)
        .build();

    private static final CSVFormat BULK_PAYMENT_WITH_BANKFEE_UPLOAD_CSV_FORMAT = CSVFormat.DEFAULT.builder()
        .setHeader(BULK_PAYMENT_WITH_BANKFEE_UPLOAD_HEADERS.toArray(String[]::new))
        .setAllowMissingColumnNames(false)
        .setDuplicateHeaderMode(DuplicateHeaderMode.DISALLOW)
        .setTrim(true)
        .build();

    private static final int MAX_BULK_PAYMENT_RECORDS_PER_SUBMISSION = 100;
    private final InvoiceSettlementDAO invoiceSettlementDAO;
    private final InvoiceRetrievalService invoiceRetrievalService;
    private final PaymentService paymentService;
    private final CreditMemoRetrievalService creditMemoRetrievalService;
    private final CreditMemoService creditMemoService;
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final EventPublishingService eventPublishingService;
    private final AccountPaymentMethodGetService accountPaymentMethodGetService;
    private final AccountPaymentMethodService accountPaymentMethodService;
    private final TenantSettingService tenantSettingService;
    private final QuartzSchedulerService quartzSchedulerService;
    private final DLQService dlqService;
    private final AccountingPeriodService accountingPeriodService;
    private final RefundService refundService;
    private final PaymentGetService paymentGetService;
    private final RealizedGainLossService realizedGainLossService;
    private final FeatureService featureService;
    private final EntityContextProvider entityContextProvider;
    private final PaymentBankAccountService paymentBankAccountService;
    private final EntityGetService entityGetService;
    private final PlatformFeatureService platformFeatureService;
    private final InvoiceEventService invoiceEventService;

    @Inject
    public InvoiceSettlementService(
        InvoiceSettlementDAO invoiceSettlementDAO,
        InvoiceRetrievalService invoiceRetrievalService,
        PaymentService paymentService,
        CreditMemoRetrievalService creditMemoRetrievalService,
        CreditMemoService creditMemoService,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        EventPublishingService eventPublishingService,
        AccountPaymentMethodGetService accountPaymentMethodGetService,
        AccountPaymentMethodService accountPaymentMethodService,
        TenantSettingService tenantSettingService,
        QuartzSchedulerService quartzSchedulerService,
        DLQService dlqService,
        AccountingPeriodService accountingPeriodService,
        RefundService refundService,
        PaymentGetService paymentGetService,
        RealizedGainLossService realizedGainLossService,
        FeatureService featureService,
        EntityContextProvider entityContextProvider,
        PaymentBankAccountService paymentBankAccountService,
        EntityGetService entityGetService,
        PlatformFeatureService platformFeatureService,
        InvoiceEventService invoiceEventService
    ) {
        this.invoiceSettlementDAO = invoiceSettlementDAO;
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.paymentService = paymentService;
        this.creditMemoRetrievalService = creditMemoRetrievalService;
        this.creditMemoService = creditMemoService;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.eventPublishingService = eventPublishingService;
        this.accountPaymentMethodGetService = accountPaymentMethodGetService;
        this.accountPaymentMethodService = accountPaymentMethodService;
        this.tenantSettingService = tenantSettingService;
        this.quartzSchedulerService = quartzSchedulerService;
        this.dlqService = dlqService;
        this.accountingPeriodService = accountingPeriodService;
        this.refundService = refundService;
        this.paymentGetService = paymentGetService;
        this.realizedGainLossService = realizedGainLossService;
        this.featureService = featureService;
        this.entityContextProvider = entityContextProvider;
        this.paymentBankAccountService = paymentBankAccountService;
        this.entityGetService = entityGetService;
        this.platformFeatureService = platformFeatureService;
        this.invoiceEventService = invoiceEventService;
    }

    // This method is designed for application of manual payment from UI or API.
    // In the case of manual payment (CHECK, WIRE), the payment method attached to the account contains no unique information
    // todo: do we need a payment method per account in the case of WIRE and CHECK?
    // TODO: Remove payment method id from parameter, as it is null in all cases
    public SettlementApplication addAndApplyManualPayment(
        Invoice.Number invoiceNumber,
        BigDecimal invoiceAmount,
        UUID paymentMethodId,
        PaymentType paymentType,
        Long paymentDate,
        BigDecimal amount,
        String note,
        BigDecimal bankFee,
        String paymentBankAccountId
    ) {
        var invoice = invoiceRetrievalService.getInvoice(invoiceNumber);
        EntityContext invoiceEntityContext = entityContextProvider.provideScopedEntityContext(Set.of(invoice.getEntityId()));
        return TenantContextInjector.spawnThreadAndCallInEntityContext(
            tenantIdProvider.provideTenantIdString(),
            invoiceEntityContext,
            entityContextProvider,
            () -> {
                validatePaymentMethodId(paymentMethodId, invoice.getCustomerAccountId());
                UUID accountPaymentMethodId = Objects.nonNull(paymentMethodId)
                    ? paymentMethodId
                    : getAccountPaymentMethodId(paymentType, invoice.getCustomerAccountId());

                validatePaymentBankAccountId(paymentBankAccountId, invoice);
                return addAndApplyPayment(
                    invoice,
                    invoiceAmount,
                    accountPaymentMethodId,
                    PaymentState.SUCCEED,
                    amount,
                    DateTimeConverter.epochSecondsToInstant(paymentDate),
                    note,
                    PaymentLifecycleType.OFFLINE,
                    bankFee,
                    paymentBankAccountId
                );
            }
        );
    }

    public List<PaymentBankAccount> getApplicablePaymentBankAccountsForInvoicePayment(String invoiceNumber) {
        if (!featureService.isEnabled(Feature.PAYMENT_WITH_BANK_ACCOUNT)) {
            LOGGER.info("Returning empty list for getApplicablePaymentBankAccountsForInvoicePayment as feature is not enabled.");
            return List.of();
        }
        Validator.validateStringNotBlank(invoiceNumber, "Invoice number cannot be blank.");
        Invoice invoice = invoiceRetrievalService.getInvoice(new Invoice.Number(invoiceNumber));
        Entity entity = entityGetService.getEntityById(invoice.getEntityId());
        String entityFunctionalCurrency = entity.getFunctionalCurrency();
        String invoiceCurrency = invoice.getCurrencyCode();
        return paymentBankAccountService.getActivePaymentBankAccountsForCurrencies(
            List.of(entityFunctionalCurrency, invoiceCurrency),
            new String[] { invoice.getEntityId() }
        );
    }

    private void validatePaymentBankAccountId(String paymentBankAccountId, Invoice invoice) {
        if (!featureService.isEnabled(Feature.PAYMENT_WITH_BANK_ACCOUNT)) {
            if (paymentBankAccountId != null) {
                throw new InvalidInputException("Payment bank account id cannot be provided when feature is not enabled.");
            }
            LOGGER.info("Bypassing validatePaymentBankAccountId as feature is not enabled.");
            return;
        }

        if (StringUtils.isBlank(paymentBankAccountId)) {
            throw new InvalidInputException("Payment bank account id cannot be blank.");
        }

        var paymentBankAccount = paymentBankAccountService.getByPaymentBankAccountId(paymentBankAccountId);
        if (!invoice.getEntityId().equals(paymentBankAccount.getEntityIds().stream().findFirst().orElseThrow())) {
            throw new InvalidInputException("Payment bank account entity does not match invoice entity.");
        }

        var entityId = invoice.getEntityId();
        var entity = entityGetService.getEntityById(entityId);
        if (
            !invoice.getCurrencyCode().equals(paymentBankAccount.getCurrencyCode().getCurrencyCode()) &&
            !entity.getFunctionalCurrency().equals(paymentBankAccount.getCurrencyCode().getCurrencyCode())
        ) {
            throw new InvalidInputException("Payment bank account currency does not match invoice currency or entity functional currency.");
        }

        if (!paymentBankAccount.canBeUsedForPayment()) {
            throw new ConflictingStateException("Payment bank account is not allowed to be used for payment.");
        }

        if (
            platformFeatureService.getFeatureEnablement(PlatformFeature.ACCOUNTING).isPresent() &&
            (paymentBankAccount.getCashLedgerAccountId() == null || paymentBankAccount.getExpenseLedgerAccountId() == null)
        ) {
            throw new InvalidInputException(
                String.format(
                    "Payment bank account %s does not have cash or expense ledger account set, please update the payment bank account with ledger accounts before using it for payments!",
                    paymentBankAccount.getPaymentBankAccountId()
                )
            );
        }
    }

    private void validatePaymentMethodId(UUID paymentMethodId, String customerAccountId) {
        if (Objects.isNull(paymentMethodId)) {
            return;
        }
        Optional<AccountPaymentMethod> accountPaymentMethodOptional = accountPaymentMethodGetService.getPaymentMethod(
            customerAccountId,
            paymentMethodId
        );
        if (accountPaymentMethodOptional.isEmpty() || accountPaymentMethodOptional.get().getStatus() != AccountPaymentMethodStatus.ACTIVE) {
            throw new IllegalStateException(
                String.format("No payment method with id: %s exists for account id: %s", paymentMethodId, customerAccountId)
            );
        }
    }

    private Optional<AccountPaymentMethod> getManualPaymentMethodOfType(PaymentType paymentType, String accountId) {
        List<AccountPaymentMethod> paymentMethods = accountPaymentMethodGetService.getPaymentMethodsInAccount(accountId);

        // Filter out valid automatic payment methods
        return paymentMethods
            .stream()
            .filter(
                accountPaymentMethod ->
                    StringUtils.isBlank(accountPaymentMethod.getPaymentMethodId()) && accountPaymentMethod.getPaymentType() == paymentType
            )
            .findAny();
    }

    public BulkPaymentUploadResult uploadBulkPaymentsCsv(InputStream inputStream) throws IOException {
        if (inputStream == null) {
            throw new InvalidInputException("InputStream is null");
        }

        List<BulkPaymentUploadData> bulkPaymentRequests;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            bulkPaymentRequests = extractAndValidateBulkPaymentRequests(reader);
        }

        return processBulkPaymentRequests(bulkPaymentRequests);
    }

    private BulkPaymentUploadResult processBulkPaymentRequests(List<BulkPaymentUploadData> bulkPaymentUploadList) {
        int failureCount = 0;
        int totalCount = 0;
        List<BulkPaymentUploadData> uploadDataResultList = new ArrayList<>();

        for (BulkPaymentUploadData paymentRequest : bulkPaymentUploadList) {
            ImmutableBulkPaymentUploadData.Builder paymentResultBuilder = ImmutableBulkPaymentUploadData.builder().from(paymentRequest);
            try {
                totalCount++;
                Invoice.Number invoiceNumber = new Invoice.Number(paymentRequest.getInvoiceNumber());
                Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);

                validatePaymentBankAccountId(paymentRequest.getPaymentBankAccountId(), invoice);

                // TODO: Need to consider how to handle automatic payments
                addAndApplyPayment(
                    invoice,
                    paymentRequest.getOriginalInvoiceBalance(),
                    paymentRequest.getPaymentMethodId(),
                    PaymentState.SUCCEED,
                    paymentRequest.getAmount(),
                    DateTimeConverter.epochSecondsToInstant(paymentRequest.getPaymentDate()),
                    paymentRequest.getNote(),
                    PaymentLifecycleType.OFFLINE,
                    paymentRequest.getBankFee(),
                    paymentRequest.getPaymentBankAccountId()
                );

                InvoiceBalance newInvoiceBalance = getInvoiceBalance(invoiceNumber);
                paymentResultBuilder.newInvoiceBalance(newInvoiceBalance.getBalance());
            } catch (Exception e) {
                failureCount++;
                paymentResultBuilder.failed(true);
                paymentResultBuilder.newInvoiceBalance(paymentRequest.getOriginalInvoiceBalance());
                paymentResultBuilder.failureReason(e.getMessage());
                paymentResultBuilder.bankFee(paymentRequest.getBankFee());
                paymentResultBuilder.paymentBankAccountId(paymentRequest.getPaymentBankAccountId());
            }

            uploadDataResultList.add(paymentResultBuilder.build());
        }

        return ImmutableBulkPaymentUploadResult.builder()
            .paymentsRequestCount(totalCount)
            .failedPaymentsCount(failureCount)
            .bulkPaymentUploadData(uploadDataResultList)
            .build();
    }

    private List<BulkPaymentUploadData> extractAndValidateBulkPaymentRequests(BufferedReader csvReader) throws IOException {
        Iterator<CSVRecord> records;

        // When enabling this FF we also need to ensure FE updates the file format on its end
        if (featureService.isEnabled(Feature.PAYMENT_WITH_BANK_ACCOUNT)) {
            records = BULK_PAYMENT_WITH_BANKFEE_UPLOAD_CSV_FORMAT.parse(csvReader).iterator();
            // no data simply return with empty
            if (!records.hasNext()) {
                return List.of();
            }
            // should match header
            List<String> headersFromFile = records.next().toList();
            validateHeaders(headersFromFile, BULK_PAYMENT_WITH_BANKFEE_UPLOAD_HEADERS);
        } else {
            records = BULK_PAYMENT_UPLOAD_CSV_FORMAT.parse(csvReader).iterator();
            // no data simply return with empty
            if (!records.hasNext()) {
                return List.of();
            }
            // should match header
            List<String> headersFromFile = records.next().toList();
            validateHeaders(headersFromFile, BULK_PAYMENT_UPLOAD_HEADERS);
        }

        List<BulkPaymentUploadData> bulkPaymentUploadDataList = new ArrayList<>();
        DateTimeFormatter formatter = tenantUsageTimeFormatter();
        while (records.hasNext()) {
            bulkPaymentUploadDataList.add(extractAndValidateBulkPaymentRecord(records.next(), formatter));
        }

        return bulkPaymentUploadDataList;
    }

    private void validateHeaders(List<String> headersFromFile, List<String> definedHeaders) {
        if (!headersFromFile.equals(definedHeaders)) {
            String message = String.format("Column list in file %s does not match expected column list %s", headersFromFile, definedHeaders);
            throw new IllegalArgumentException(message);
        }
    }

    // Helper function to validate required fields
    private void validateRequiredFields(CSVRecord record, long rowNum, boolean isPaymentWithBankAccountEnabled) {
        String[] requiredHeaders = { INVOICE_NUMBER_HEADER, AMOUNT_HEADER, PAYMENT_METHOD_HEADER, PAYMENT_DATE_HEADER, NOTE_HEADER };

        // List to store all missing or empty fields
        List<String> validationErrors = new ArrayList<>();

        // Check each required header for missing or empty values
        for (String header : requiredHeaders) {
            try {
                String value = record.get(header);
                if (StringUtils.isBlank(value) && !NOTE_HEADER.equals(header)) {
                    validationErrors.add(header);
                }
            } catch (IllegalArgumentException e) {
                // Handle cases where the header exists but there are fewer values in the row
                validationErrors.add(header);
            }
        }

        // Check if the amount is valid and non-negative
        String amountValue = record.get(AMOUNT_HEADER);
        if (StringUtils.isNotBlank(amountValue)) {
            try {
                BigDecimal amount = new BigDecimal(amountValue);
                if (amount.compareTo(BigDecimal.ZERO) < 0) {
                    validationErrors.add("Amount must be non-negative in '" + AMOUNT_HEADER + "'");
                }
            } catch (NumberFormatException e) {
                validationErrors.add("Invalid numeric value in '" + AMOUNT_HEADER + "'");
            }
        }

        // Conditionally check BANK_FEE_HEADER and PAYMENT_BANK_ACCOUNT_ID_HEADER if feature is enabled
        if (isPaymentWithBankAccountEnabled) {
            String bankFeeValue = record.get(BANK_FEE_HEADER);
            String paymentBankAccountIdValue = record.get(PAYMENT_BANK_ACCOUNT_ID_HEADER);

            // Check for blank or missing bank fee
            if (StringUtils.isBlank(bankFeeValue)) {
                validationErrors.add("Missing value for '" + BANK_FEE_HEADER + "'");
            } else {
                // Validate bank fee as a non-negative decimal
                try {
                    BigDecimal bankFee = new BigDecimal(bankFeeValue);
                    if (bankFee.compareTo(BigDecimal.ZERO) < 0) {
                        validationErrors.add("Bank fee must be non-negative in '" + BANK_FEE_HEADER + "'");
                    }
                } catch (NumberFormatException e) {
                    validationErrors.add("Invalid numeric value in '" + BANK_FEE_HEADER + "'");
                }
            }

            // Check for missing or empty payment bank account ID
            if (StringUtils.isBlank(paymentBankAccountIdValue)) {
                validationErrors.add("Missing value for '" + PAYMENT_BANK_ACCOUNT_ID_HEADER + "'");
            }
        }

        // If there are any missing or empty fields, throw an exception with details
        if (!validationErrors.isEmpty()) {
            String message = String.format(
                "Missing or invalid values in row %d for the following fields: %s",
                rowNum,
                String.join(", ", validationErrors)
            );
            throw new InvalidInputException(message);
        }
    }

    private BulkPaymentUploadData extractAndValidateBulkPaymentRecord(CSVRecord record, DateTimeFormatter formatter) {
        // since we include the CSV header to inspect
        long rowNum = record.getRecordNumber() - 1;
        if (rowNum > MAX_BULK_PAYMENT_RECORDS_PER_SUBMISSION) {
            String message = String.format("Only %d entries are allowed per Bulk Payment csv upload", MAX_BULK_PAYMENT_RECORDS_PER_SUBMISSION);
            throw new InvalidInputException(message);
        }

        try {
            // Validate that none of the required fields are missing or empty
            validateRequiredFields(record, rowNum, featureService.isEnabled(Feature.PAYMENT_WITH_BANK_ACCOUNT));

            Invoice.Number invoiceNumber = new Invoice.Number(record.get(INVOICE_NUMBER_HEADER));
            BigDecimal amount = new BigDecimal(record.get(AMOUNT_HEADER));
            String note = record.get(NOTE_HEADER);
            InvoiceBalance invoiceBalance = getInvoiceBalance(invoiceNumber);

            String paymentMethodString = record.get(PAYMENT_METHOD_HEADER);
            UUID paymentMethodId = getAccountPaymentMethodId(
                PaymentType.valueOf(StringUtils.upperCase(paymentMethodString)),
                invoiceBalance.getCustomerAccountId()
            );

            Instant paymentDateTimeInstant = Instant.now();
            String paymentDateTimeString = record.get(PAYMENT_DATE_HEADER);
            if (StringUtils.isNotBlank(paymentDateTimeString)) {
                ZonedDateTime zonedInvoiceDateTime = ZonedDateTime.parse(paymentDateTimeString, formatter);
                paymentDateTimeInstant = zonedInvoiceDateTime.toInstant();
            }

            var bulkPaymentUploadDataBuilder = ImmutableBulkPaymentUploadData.builder()
                .invoiceNumber(invoiceNumber.getNumber())
                .paymentMethod(paymentMethodString)
                .paymentMethodId(paymentMethodId)
                .amount(amount)
                .currencyCode(invoiceBalance.getCurrencyCode())
                .paymentDate(paymentDateTimeInstant.getEpochSecond())
                .originalInvoiceBalance(invoiceBalance.getBalance())
                .newInvoiceBalance(invoiceBalance.getBalance().subtract(amount))
                .failed(false)
                .note(note);

            if (featureService.isEnabled(Feature.PAYMENT_WITH_BANK_ACCOUNT)) {
                return bulkPaymentUploadDataBuilder
                    .bankFee(new BigDecimal(record.get(BANK_FEE_HEADER)))
                    .paymentBankAccountId(record.get(PAYMENT_BANK_ACCOUNT_ID_HEADER))
                    .build();
            } else {
                return bulkPaymentUploadDataBuilder.build();
            }
        } catch (DateTimeParseException exception) {
            String message = String.format(
                "Invalid date time format found in row %d. Expected format: %s",
                rowNum,
                BULK_PAYMENT_UPLOAD_DATETIME_FORMAT
            );
            throw new InvalidInputException(message);
        } catch (InvalidInputException | ObjectNotFoundException | IllegalStateException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceFailureException(ex.getMessage(), ex);
        }
    }

    private DateTimeFormatter tenantUsageTimeFormatter() {
        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        return DateTimeFormatter.ofPattern(BULK_PAYMENT_UPLOAD_DATETIME_FORMAT).withZone(tenantTimeZone.toZoneId());
    }

    SettlementApplication addAndApplyPayment(
        Invoice invoice,
        BigDecimal invoiceAmount,
        UUID paymentMethodId,
        PaymentState state,
        BigDecimal amount,
        Instant paymentDate,
        String note,
        PaymentLifecycleType type,
        BigDecimal bankFee,
        String paymentBankAccountId
    ) {
        validateNoteLength(note);
        verifyCurrency(amount, invoice);
        validateBankFee(bankFee, amount);

        if (Objects.isNull(paymentDate)) {
            paymentDate = Instant.now();
        }
        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        validatePaymentDate(invoice.getEntityId(), paymentDate, invoice.getInvoiceDate(), tenantTimeZone);

        var payment = new Payment();
        payment.setEntityId(invoice.getEntityId());
        payment.setCustomerAccountId(invoice.getCustomerAccountId());
        payment.setPaymentMethodId(paymentMethodId);
        payment.setState(state);
        payment.setAmount(amount);
        payment.setPaymentDate(paymentDate);
        payment.setLifecycleType(type);
        payment.setPaymentBankAccountId(paymentBankAccountId);
        payment.setBankFee(bankFee);
        payment.setCurrencyCode(invoice.getCurrencyCode());

        Invoice.Number invoiceNumber = invoice.getInvoiceNumber();
        Instant appliedOn = validateAndGetAppliedOn(paymentDate);
        InvoiceBalance invoiceBalance = getAndCompareInvoiceBalance(invoiceNumber, invoiceAmount);
        PaymentBalance paymentBalance = getPaymentBalanceFromPayment(payment);

        validateBalancesForApplication(amount, invoiceBalance, paymentBalance);

        RetryPolicy<Object> retryPolicy = getRetryPolicy();
        return Failsafe.with(retryPolicy).get(() -> addAndApplyPaymentAttempt(invoice, amount, note, appliedOn, invoiceBalance, payment));
    }

    private void validateBankFee(BigDecimal bankFee, BigDecimal amount) {
        if (!featureService.isEnabled(Feature.PAYMENT_WITH_BANK_ACCOUNT)) {
            if (bankFee != null) {
                throw new InvalidInputException("Bank fee cannot be provided when feature is not enabled.");
            }
            LOGGER.info("Bypassing validateBankFee as feature is not enabled.");
            return;
        }
        if (bankFee == null) {
            throw new InvalidInputException("Bank fee cannot be null.");
        }
        if (bankFee.compareTo(amount) > 0) {
            throw new InvalidInputException("Bank fee cannot be greater than payment amount.");
        }
        if (bankFee.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidInputException("Bank fee cannot be negative.");
        }
    }

    private void validatePaymentDate(String entityId, Instant paymentDate, Instant invoiceDate, TimeZone tenantTimeZone) {
        if (
            DateTimeCalculator.getStartOfDay(paymentDate, tenantTimeZone.toZoneId()).isBefore(
                DateTimeCalculator.getStartOfDay(invoiceDate, tenantTimeZone.toZoneId())
            )
        ) {
            throw new IllegalArgumentException("Payment date cannot be before invoice date.");
        }

        if (accountingPeriodService.inClosedAccountingPeriod(entityId, paymentDate)) {
            throw new IllegalStateException("Payment date cannot be earlier than current open accounting period.");
        }
    }

    private static void verifyCurrency(BigDecimal amount, Invoice invoice) {
        var currencyCode = invoice.getCurrencyCode();
        var precision = SupportedCurrency.getCurrencyPrecision(currencyCode);
        if (amount.scale() > precision) { // we should not process more than the maximum precision for any given currency
            throw new IllegalArgumentException(
                String.format("Payment amount to apply for currency %s cannot have more than %d decimal points", currencyCode, precision)
            );
        }
    }

    private SettlementApplication addAndApplyPaymentAttempt(
        Invoice invoice,
        BigDecimal amount,
        String note,
        Instant appliedOn,
        InvoiceBalance invoiceBalance,
        Payment payment
    ) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String invoiceNumber = invoice.getInvoiceNumber().getNumber();
        SettlementApplication savedSettlementApplication = dslContext.transactionResult(configuration -> {
            // Invalidate any future retry attempts for this invoice
            handleScheduledPaymentRetriesForInvoice(configuration.dsl(), invoice.getInvoiceNumber());

            Payment persistedPayment = paymentService.addPayment(payment, configuration.dsl());
            String paymentId = persistedPayment.getPaymentId();
            SettlementApplication.Builder settlementApplicationBuilder = SettlementApplication.builder()
                .entityId(invoice.getEntityId())
                .customerAccountId(invoiceBalance.getCustomerAccountId())
                .invoiceNumber(invoiceNumber)
                .paymentId(paymentId)
                .applicationType(SettlementApplicationType.PAYMENT)
                .amount(amount)
                .note(note)
                .appliedOn(appliedOn);

            populateFunctionalAmounts(settlementApplicationBuilder, persistedPayment, amount);

            SettlementApplication settlementApplication = settlementApplicationBuilder.build();

            PaymentBalance paymentBalance = getPaymentBalanceFromPayment(persistedPayment);
            SettlementApplication savedApplication = addPaymentApplication(configuration, settlementApplication, invoiceBalance, paymentBalance);
            String tenantId = tenantIdProvider.provideTenantIdString();
            PaymentProcessedEventPayload eventPayload = PaymentProcessedEventPayload.builder()
                .payment(persistedPayment)
                .settlementApplication(savedApplication)
                .build();
            byte[] payloadBytes = OBJECT_MAPPER.writeValueAsBytes(eventPayload);
            eventPublishingService.publishEventInTransaction(
                configuration,
                EventType.PAYMENT_PROCESSED,
                tenantId,
                settlementApplication.getEntityId(),
                settlementApplication.getCustomerAccountId(),
                payloadBytes
            );
            if (featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
                addRealizedGainLossEntryInTransaction(configuration, invoice, savedApplication, Optional.of(persistedPayment), Optional.empty());
            }
            return savedApplication;
        });

        generateInvoiceDocumentAsync(invoiceNumber);
        return savedSettlementApplication;
    }

    private void addRealizedGainLossEntryInTransaction(
        Configuration configuration,
        Invoice invoice,
        SettlementApplication settlementApplication,
        Optional<Payment> payment,
        Optional<CreditMemo> creditMemo
    ) {
        RealizedGainLossInput realizedGainLossInput = ImmutableRealizedGainLossInput.builder()
            .invoice(invoice)
            .settlementApplication(settlementApplication)
            .payment(payment)
            .creditMemo(creditMemo)
            .build();
        realizedGainLossService.addRealizedGainLossInTransaction(configuration, realizedGainLossInput);
    }

    private SettlementApplication populateFunctionalAmounts(
        SettlementApplication settlementApplication,
        CreditMemo creditMemo,
        String functionalCurrency
    ) {
        if (!featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            return settlementApplication;
        }
        SettlementApplication.Builder settlementApplicationBuilder = SettlementApplication.builder(settlementApplication);
        if (StringUtils.isBlank(creditMemo.getExchangeRateId())) {
            settlementApplicationBuilder.functionalAmount(settlementApplication.getAmount());
        } else {
            settlementApplicationBuilder.exchangeRateId(creditMemo.getExchangeRateId());
            settlementApplicationBuilder.exchangeRate(creditMemo.getExchangeRate());
            settlementApplicationBuilder.exchangeRateDate(creditMemo.getExchangeRateDate());
            settlementApplicationBuilder.functionalAmount(
                Numbers.applyExchangeRate(settlementApplication.getAmount(), creditMemo.getExchangeRate(), functionalCurrency)
            );
        }
        return settlementApplicationBuilder.build();
    }

    private SettlementApplication populateFunctionalAmounts(SettlementApplication settlementApplication, Payment payment) {
        SettlementApplication.Builder settlementApplicationBuilder = SettlementApplication.builder(settlementApplication);
        populateFunctionalAmounts(settlementApplicationBuilder, payment, settlementApplication.getAmount());
        return settlementApplicationBuilder.build();
    }

    private void populateFunctionalAmounts(SettlementApplication.Builder settlementApplicationBuilder, Payment payment, BigDecimal amount) {
        if (!featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            return;
        }
        if (StringUtils.isBlank(payment.getExchangeRateId())) {
            settlementApplicationBuilder.functionalAmount(amount);
        } else {
            settlementApplicationBuilder.exchangeRateId(payment.getExchangeRateId());
            settlementApplicationBuilder.exchangeRate(payment.getExchangeRate());
            settlementApplicationBuilder.exchangeRateDate(payment.getExchangeRateDate());
            settlementApplicationBuilder.functionalAmount(Numbers.applyExchangeRate(amount, payment.getExchangeRate(), payment.getCurrencyCode()));
        }
    }

    private PaymentBalance getPaymentBalanceFromPayment(Payment payment) {
        return new PaymentBalance(null, payment.getCustomerAccountId(), payment.getPaymentId(), payment.getAmount(), null);
    }

    private Instant validateAndGetAppliedOn(Instant appliedOn) {
        Instant now = Instant.now();
        if (appliedOn == null) {
            return now;
        }

        if (appliedOn.isAfter(now)) {
            throw new IllegalArgumentException("appliedOn must be in the past");
        }

        return appliedOn;
    }

    public void applyPaymentForPendingSettlementApplication(
        SettlementApplication settlementApplication,
        Payment payment,
        DSLContext transactionContext
    ) {
        Invoice.Number invoiceNumber = new Invoice.Number(settlementApplication.getInvoiceNumber());
        InvoiceBalance invoiceBalance = getAndCompareInvoiceBalance(invoiceNumber, settlementApplication.getAmount());
        PaymentBalance paymentBalance = getPaymentBalance(settlementApplication.getPaymentId());
        validateBalancesForApplication(settlementApplication.getAmount(), invoiceBalance, paymentBalance);
        SettlementApplication settlementApplicationWithFx = populateFunctionalAmounts(settlementApplication, payment);

        RetryPolicy<Object> retryPolicy = getRetryPolicy();
        Failsafe.with(retryPolicy).get(() ->
            applyPaymentAttempt(settlementApplicationWithFx, payment, invoiceBalance, paymentBalance, transactionContext)
        );
    }

    private SettlementApplication applyPaymentAttempt(
        SettlementApplication settlementApplication,
        Payment payment,
        InvoiceBalance invoiceBalance,
        PaymentBalance paymentBalance,
        DSLContext transactionContext
    ) {
        DSLContext dslContext = Optional.ofNullable(transactionContext).orElseGet(() ->
            TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider)
        );

        SettlementApplication savedSettlementApplication = dslContext.transactionResult(configuration -> {
            Payment updatedPayment = paymentService.updatePayment(payment, configuration.dsl());
            SettlementApplication settlementApplicationWithFx = populateFunctionalAmounts(settlementApplication, updatedPayment);
            SettlementApplication savedApplication = addPaymentApplication(
                configuration,
                settlementApplicationWithFx,
                invoiceBalance,
                paymentBalance
            );
            String tenantId = tenantIdProvider.provideTenantIdString();
            PaymentProcessedEventPayload eventPayload = PaymentProcessedEventPayload.builder()
                .payment(updatedPayment)
                .settlementApplication(savedApplication)
                .build();
            byte[] payloadBytes = OBJECT_MAPPER.writeValueAsBytes(eventPayload);
            eventPublishingService.publishEventInTransaction(
                configuration,
                EventType.PAYMENT_PROCESSED,
                tenantId,
                settlementApplication.getEntityId(),
                settlementApplication.getCustomerAccountId(),
                payloadBytes
            );
            return savedApplication;
        });

        generateInvoiceDocumentAsync(settlementApplication.getInvoiceNumber());
        return savedSettlementApplication;
    }

    private void generateInvoiceDocumentAsync(String invoiceNumber) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        try {
            quartzSchedulerService.scheduleSimpleQuartzJob(
                Optional.of(tenantId),
                UUID.randomUUID().toString(),
                Optional.of(invoiceNumber),
                QuartzJobType.INVOICE_DOCUMENT_GENERATION,
                InvoiceDocumentGeneratorJob.class,
                Date.from(Instant.now()),
                SimpleScheduleBuilder.simpleSchedule()
            );
        } catch (SchedulerException e) {
            LOGGER.error("Error generating invoice document after payment application for {}", invoiceNumber, e);
            dlqService.send(invoiceNumber, DLQMessageType.INVOICE_GENERATE_DOCUMENT);
        }
    }

    public List<SettlementApplication> getSettlementApplicationsByPaymentId(String paymentId) {
        Validator.validateNonNullArgument(paymentId);
        return invoiceSettlementDAO.getSettlementApplicationsForPaymentId(paymentId);
    }

    public Optional<SettlementApplication> getPendingSettlementApplicationByPaymentId(String paymentId) {
        Validator.validateNonNullArgument(paymentId);
        List<SettlementApplication> settlementApplications = invoiceSettlementDAO.getSettlementApplicationsForPaymentId(paymentId);
        if (CollectionUtils.isEmpty(settlementApplications)) {
            return Optional.empty();
        }

        return settlementApplications.stream().filter(s -> s.getStatus() == SettlementApplicationStatus.ATTEMPTING_PAYMENT_COLLECTION).findAny();
    }

    public void applyCredit(Invoice.Number invoiceNumber, BigDecimal invoiceAmount, String creditMemoNumber, BigDecimal amount, String note) {
        validateNoteLength(note);

        InvoiceBalance invoiceBalance = getAndCompareInvoiceBalance(invoiceNumber, invoiceAmount);
        CreditMemo creditMemo = creditMemoRetrievalService.getCreditMemoByNumber(creditMemoNumber);
        Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);
        validateEntityReferences(invoice, creditMemo);
        CreditMemoBalance creditMemoBalance = creditMemoRetrievalService.getCreditMemoBalance(creditMemoNumber);
        validateCreditMemoToApply(creditMemo, creditMemoBalance);

        validateBalancesForApplication(amount, invoiceBalance, creditMemoBalance);
        // validate credit memo and invoice both have same currency
        validateCurrencies(invoice, creditMemo);

        var settlementApplication = SettlementApplication.builder()
            .entityId(creditMemo.getEntityId())
            .customerAccountId(invoiceBalance.getCustomerAccountId())
            .invoiceNumber(invoiceNumber.getNumber())
            .creditMemoNumber(creditMemo.getCreditMemoNumber())
            .applicationType(SettlementApplicationType.CREDIT)
            .amount(amount)
            .note(note)
            .appliedOn(Instant.now())
            .build();

        Entity entity = entityGetService.getEntityById(creditMemo.getEntityId());
        SettlementApplication settlementApplicationWithFx = populateFunctionalAmounts(
            settlementApplication,
            creditMemo,
            entity.getFunctionalCurrency()
        );

        RetryPolicy<Object> retryPolicy = getRetryPolicy();
        SettlementApplication savedSettlement = Failsafe.with(retryPolicy).get(() ->
            applyCreditAttempt(settlementApplicationWithFx, invoiceBalance, creditMemoBalance)
        );

        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        if (featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            addRealizedGainLossEntryInTransaction(dslContext.configuration(), invoice, savedSettlement, Optional.empty(), Optional.of(creditMemo));
        }

        CreditMemoBalance newCreditMemoBalance = creditMemoRetrievalService.getCreditMemoBalance(creditMemoNumber);
        if (Numbers.isZero(newCreditMemoBalance.getBalance())) {
            creditMemoService.closeCreditMemo(creditMemoNumber);
        }
    }

    private void validateCurrencies(Invoice invoice, CreditMemo creditMemo) {
        if (!invoice.getCurrencyCode().equals(creditMemo.getCurrencyCode())) {
            throw new InvalidInputException(
                String.format(
                    "Cannot apply credit memo with currency: %s to invoice with currency: %s",
                    creditMemo.getCurrencyCode(),
                    invoice.getCurrencyCode()
                )
            );
        }
    }

    private void validateEntityReferences(Invoice invoice, CreditMemo creditMemo) {
        if (!invoice.getEntityId().equals(creditMemo.getEntityId())) {
            throw new IllegalArgumentException("Cannot apply credit memo to invoice from different entity");
        }
    }

    private void validateCreditMemoToApply(CreditMemo creditMemo, CreditMemoBalance balance) {
        if (creditMemo.getStatus() != CreditMemoStatus.POSTED || Numbers.isZero(balance.getBalance())) {
            throw new IllegalArgumentException(String.format("cannot apply credit memo %s to an invoice", creditMemo.getCreditMemoNumber()));
        }
    }

    public void unapplyCredit(
        Invoice.Number invoiceNumber,
        BigDecimal invoiceBalanceAmount,
        String creditMemoNumber,
        BigDecimal creditMemoBalanceAmount,
        UUID settlementToUnapplyId,
        String note
    ) {
        validateNoteLength(note);

        InvoiceBalance invoiceBalance = getAndCompareInvoiceBalance(invoiceNumber, invoiceBalanceAmount);
        CreditMemo creditMemo = creditMemoRetrievalService.getCreditMemoByNumber(creditMemoNumber);
        Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);
        validateEntityReferences(invoice, creditMemo);

        CreditMemoBalance creditMemoBalance = getAndCompareCreditMemoBalance(creditMemoNumber, creditMemoBalanceAmount);
        SettlementApplication settlementToUnapply = invoiceSettlementDAO.getSettlementApplication(settlementToUnapplyId);
        List<SettlementApplication> creditSettlementsWithoutUnapplication = invoiceSettlementDAO.getCreditSettlementsWithoutUnapplication(
            invoiceNumber
        );
        validateCreditMemoAndSettlementToUnapply(
            creditMemo,
            settlementToUnapply,
            creditSettlementsWithoutUnapplication,
            invoiceBalance,
            creditMemoBalance
        );
        validateBalancesForUnapplication(invoice, invoiceBalance, creditMemo, creditMemoBalance, settlementToUnapply);

        var settlementApplication = SettlementApplication.builder()
            .entityId(creditMemo.getEntityId())
            .customerAccountId(invoiceBalance.getCustomerAccountId())
            .invoiceNumber(invoiceNumber.getNumber())
            .creditMemoNumber(creditMemo.getCreditMemoNumber())
            .applicationType(SettlementApplicationType.UNAPPLY_CREDIT)
            .amount(settlementToUnapply.getAmount().negate())
            .note(note)
            .appliedOn(Instant.now())
            .negatedSettlementId(settlementToUnapply.getId())
            .build();

        RetryPolicy<Object> retryPolicy = getRetryPolicy();
        Failsafe.with(retryPolicy).run(() -> applyCreditAttempt(settlementApplication, invoiceBalance, creditMemoBalance));

        CreditMemoBalance newCreditMemoBalance = creditMemoRetrievalService.getCreditMemoBalance(creditMemoNumber);
        if (creditMemo.getStatus() == CreditMemoStatus.CLOSED && Numbers.isPositive(newCreditMemoBalance.getBalance())) {
            creditMemoService.reopenCreditMemo(creditMemoNumber);
        }
    }

    private void validateCreditMemoAndSettlementToUnapply(
        CreditMemo creditMemo,
        SettlementApplication settlementToUnapply,
        List<SettlementApplication> creditSettlementsWithoutUnapplication,
        InvoiceBalance invoiceBalance,
        CreditMemoBalance creditMemoBalance
    ) {
        if (!invoiceBalance.getCustomerAccountId().equals(creditMemoBalance.getCustomerAccountId())) {
            throw new IllegalArgumentException("Credit memo and invoice belong to different accounts");
        }

        boolean isStatusValid = creditMemo.getStatus() == CreditMemoStatus.POSTED || creditMemo.getStatus() == CreditMemoStatus.CLOSED;
        if (!isStatusValid) {
            String message = String.format("Cannot unapply credit memo %s that is not posted or closed", creditMemo.getCreditMemoNumber());
            throw new InvalidInputException(message);
        }

        if (settlementToUnapply.getApplicationType() != SettlementApplicationType.CREDIT) {
            throw new InvalidInputException("Cannot unapply a non-credit settlement application");
        }

        if (settlementToUnapply.getStatus() != null) {
            throw new InvalidInputException("Cannot unapply a settlement application that is not applied");
        }

        Set<UUID> creditSettlementIds = creditSettlementsWithoutUnapplication.stream().map(SettlementApplication::getId).collect(Collectors.toSet());
        if (!creditSettlementIds.contains(settlementToUnapply.getId())) {
            throw new ConflictingStateException("Cannot unapply a settlement application that has been already unapplied");
        }
    }

    public List<Invoice> getInvoicesCreditMemoIsAppliedTo(String creditMemoNumber) {
        var numbers = invoiceSettlementDAO.getInvoicesAppliedFromCreditMemo(creditMemoNumber);
        return numbers.stream().map(number -> invoiceRetrievalService.getInvoice(new Invoice.Number(number))).collect(Collectors.toList());
    }

    public SettlementApplication addSettlementApplicationForAttemptedPayment(
        String accountId,
        String invoiceNumber,
        Payment payment,
        BigDecimal amount,
        String note,
        Configuration configuration
    ) {
        Instant now = Instant.now();
        SettlementApplication settlementApplication = SettlementApplication.builder()
            .entityId(payment.getEntityId())
            .customerAccountId(accountId)
            .invoiceNumber(invoiceNumber)
            .paymentId(payment.getPaymentId())
            .applicationType(SettlementApplicationType.PAYMENT)
            .amount(amount)
            .note(note)
            .appliedOn(now)
            .status(SettlementApplicationStatus.ATTEMPTING_PAYMENT_COLLECTION)
            .build();
        SettlementApplication settlementApplicationWithFx = populateFunctionalAmounts(settlementApplication, payment);
        return invoiceSettlementDAO.addPendingAutomaticPaymentApplication(settlementApplicationWithFx, configuration);
    }

    public Optional<SettlementApplication> findPendingPaymentCollectionAttemptForInvoice(Invoice.Number invoiceNumber) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return invoiceSettlementDAO.getPendingPaymentCollectionAttemptForInvoice(dslContext, invoiceNumber.getNumber());
    }

    public void markSettlementApplicationAsFailed(SettlementApplication settlementApplication, Instant failedOn, DSLContext transactionContext) {
        invoiceSettlementDAO.updatePendingSettlementApplication(
            settlementApplication.getId(),
            SettlementApplicationStatus.FAILED,
            Instant.now(),
            Optional.ofNullable(transactionContext)
        );
    }

    private SettlementApplication applyCreditAttempt(
        SettlementApplication settlementApplication,
        InvoiceBalance invoiceBalance,
        CreditMemoBalance creditMemoBalance
    ) {
        SettlementApplication savedSettlement = addCreditApplication(settlementApplication, invoiceBalance, creditMemoBalance);
        generateInvoiceDocumentAsync(settlementApplication.getInvoiceNumber());
        return savedSettlement;
    }

    private RetryPolicy<Object> getRetryPolicy() {
        return RetryPolicy.builder().handle(ConcurrentModificationException.class).withDelay(100, 500, ChronoUnit.MILLIS).build();
    }

    public InvoiceBalance getAndCompareInvoiceBalance(Invoice.Number invoiceNumber, BigDecimal invoiceBalanceAmount) {
        var invoiceBalance = getInvoiceBalance(invoiceNumber);

        if (!Numbers.equals(invoiceBalance.getBalance(), invoiceBalanceAmount)) {
            throw new ConflictingStateException("Invoice balance amount has been updated, please refresh first.");
        }
        return invoiceBalance;
    }

    public CreditMemoBalance getAndCompareCreditMemoBalance(String creditMemoNumber, BigDecimal creditMemoBalanceAmount) {
        CreditMemoBalance creditMemoBalance = creditMemoRetrievalService.getCreditMemoBalance(creditMemoNumber);

        if (!Numbers.equals(creditMemoBalance.getBalance(), creditMemoBalanceAmount)) {
            throw new ConflictingStateException("Credit memo balance amount has been updated, please refresh first.");
        }
        return creditMemoBalance;
    }

    private void validateInvoiceForGettingBalance(Invoice invoice) {
        if (invoice.getStatus() == null) {
            LOGGER.warn("Invoice number {} has null status", invoice.getInvoiceNumber());
            throw new IllegalStateException("Invoice has unknown status");
        }

        if (!invoice.getStatus().hasFinalizedTotal()) {
            throw new IllegalArgumentException("Cannot get invoice balance for invoices that have not been posted.");
        }
    }

    public PaymentBalance getPaymentBalance(String paymentId) {
        Optional<PaymentBalance> paymentBalance = invoiceSettlementDAO.getPaymentBalance(paymentId);

        if (paymentBalance.isPresent()) {
            return paymentBalance.get();
        } else {
            // todo: validate payment state
            Payment payment = paymentGetService.getPaymentByPaymentId(paymentId);
            return new PaymentBalance(null, payment.getCustomerAccountId(), paymentId, payment.getAmount(), null);
        }
    }

    public InvoiceBalance getInvoiceBalance(Invoice.Number invoiceNumber) {
        Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);
        validateInvoiceForGettingBalance(invoice);

        Optional<InvoiceBalance> invoiceBalance = invoiceSettlementDAO.getInvoiceBalance(invoice.getInvoiceNumber());

        if (
            invoice.getStatus() == InvoiceStatus.PAID &&
            invoiceBalance.isPresent() &&
            invoiceBalance.get().getBalance().compareTo(BigDecimal.ZERO) != 0
        ) {
            LOGGER.warn("Invoice number {} is marked as paid, but invoice balance is not 0", invoice.getInvoiceNumber());
        }

        return invoiceBalance.orElseGet(() ->
            new InvoiceBalance(
                null,
                invoice.getCustomerAccountId(),
                invoice.getInvoiceNumber().getNumber(),
                invoice.getTotal(),
                invoice.getCurrencyCode(),
                null
            )
        );
    }

    public InvoiceBalance updateInvoiceBalance(
        InvoiceBalance currentInvoiceBalance,
        BigDecimal adjustmentAmount,
        Configuration configuration,
        Instant updatedOn
    ) {
        return updateInvoiceBalance(currentInvoiceBalance, adjustmentAmount, configuration, DateTimeConverter.instantToLocalDateTime(updatedOn));
    }

    public List<InvoiceBalance> getInvoiceBalances(List<Invoice> invoiceList) {
        if (CollectionUtils.isEmpty(invoiceList)) {
            return List.of();
        }

        var invoiceNumbers = invoiceList.stream().map(Invoice::getInvoiceNumber).collect(Collectors.toList());
        var invoiceBalanceList = invoiceSettlementDAO.getInvoiceBalances(invoiceNumbers);
        var existingInvoiceNumberSet = invoiceBalanceList.stream().map(InvoiceBalance::getInvoiceNumber).collect(Collectors.toSet());

        invoiceList.forEach(invoice -> populateInvoiceBalanceIfMissing(invoice, existingInvoiceNumberSet, invoiceBalanceList));
        return invoiceBalanceList;
    }

    private void populateInvoiceBalanceIfMissing(Invoice invoice, Set<String> existingInvoiceNumberSet, List<InvoiceBalance> invoiceBalanceList) {
        if (!existingInvoiceNumberSet.contains(invoice.getInvoiceNumber().getNumber())) {
            invoiceBalanceList.add(
                new InvoiceBalance(
                    null,
                    invoice.getCustomerAccountId(),
                    invoice.getInvoiceNumber().getNumber(),
                    invoice.getTotal(),
                    invoice.getCurrencyCode(),
                    null
                )
            );
        }
    }

    public BigDecimal getAmountPaid(Invoice.Number invoiceNumber) {
        List<SettlementApplication> settlementApplications = getAppliedSettlementApplicationsForInvoice(invoiceNumber);
        return settlementApplications.stream().map(SettlementApplication::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public List<SettlementApplication> getAppliedSettlementApplicationsForInvoice(Invoice.Number invoiceNumber) {
        try {
            invoiceRetrievalService.getInvoice(invoiceNumber);
        } catch (ObjectNotFoundException ex) {
            LOGGER.warn("Invoice number {} doesn't exist", invoiceNumber.getNumber(), ex);
            throw new IllegalArgumentException("Invalid invoice number");
        }
        return invoiceSettlementDAO.getAppliedSettlementApplications(invoiceNumber);
    }

    public List<SettlementApplication> getAppliedSettlementApplicationsForPayment(String paymentId) {
        Validator.validateNonNullArgument(paymentId);
        // validate payment exists
        paymentGetService.getPaymentByPaymentId(paymentId);
        List<SettlementApplication> applications = invoiceSettlementDAO.getSettlementApplicationsForPaymentId(paymentId);

        // manual payments applied have status == null
        return applications.stream().filter(s -> s.getStatus() == null || s.getStatus() == SettlementApplicationStatus.APPLIED_PAYMENT).toList();
    }

    public List<SettlementApplication> getSettlementApplications(Invoice.Number invoiceNumber) {
        try {
            invoiceRetrievalService.getInvoice(invoiceNumber);
        } catch (ObjectNotFoundException ex) {
            LOGGER.warn("Invoice number {} doesn't exist", invoiceNumber.getNumber(), ex);
            throw new IllegalArgumentException("Invalid invoice number");
        }
        return invoiceSettlementDAO.getSettlementApplications(invoiceNumber);
    }

    public List<SettlementApplication> getSettlementApplicationsForCreditMemo(String creditMemoNumber) {
        try {
            creditMemoRetrievalService.getCreditMemoByNumber(creditMemoNumber);
        } catch (ObjectNotFoundException ex) {
            LOGGER.warn("Credit memo number {} doesn't exist", creditMemoNumber, ex);
            throw new IllegalArgumentException("Invalid credit memo number");
        }
        return invoiceSettlementDAO.getSettlementApplicationsForCreditMemo(creditMemoNumber);
    }

    private void validateBalancesForApplication(BigDecimal amount, InvoiceBalance invoiceBalance, SettlementBalance settlementBalance) {
        if (!invoiceBalance.getCustomerAccountId().equals(settlementBalance.getCustomerAccountId())) {
            throw new IllegalArgumentException("Payment / Credit Memo and invoice belong to different accounts");
        }

        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("amount must be positive");
        }

        if (invoiceBalance.getBalance().compareTo(amount) < 0) {
            throw new IllegalArgumentException("amount exceeds remaining balance");
        }

        if (settlementBalance.getBalance().compareTo(amount) < 0) {
            throw new IllegalArgumentException("insufficient funds from Payment or Credit Memo");
        }
    }

    private void validateBalancesForUnapplication(
        Invoice invoice,
        InvoiceBalance invoiceBalance,
        CreditMemo creditMemo,
        CreditMemoBalance creditMemoBalance,
        SettlementApplication settlementToUnapply
    ) {
        // The amount to unapply + the current invoice balance should not exceed the original invoice amount
        BigDecimal newInvoiceBalance = invoiceBalance.getBalance().add(settlementToUnapply.getAmount());
        if (newInvoiceBalance.compareTo(invoice.getTotal()) > 0) {
            throw new ConflictingStateException(
                String.format(
                    "The new invoice balance is %s after unapplying %s, which exceeds the original invoice total %s. Cannot unapply settlement application %s for invoice %s",
                    newInvoiceBalance,
                    settlementToUnapply.getAmount(),
                    invoice.getTotal(),
                    settlementToUnapply.getId(),
                    invoice.getInvoiceNumber()
                )
            );
        }

        // The amount to unapply + the current credit memo balance should not exceed the original credit memo amount
        BigDecimal newCreditMemoBalance = creditMemoBalance.getBalance().add(settlementToUnapply.getAmount());
        if (newCreditMemoBalance.compareTo(creditMemo.getAmount()) > 0) {
            throw new ConflictingStateException(
                String.format(
                    "The new credit memo balance is %s after unapplying %s, which exceeds the original credit memo amount %s. Cannot unapply settlement application %s for invoice %s",
                    newCreditMemoBalance,
                    settlementToUnapply.getAmount(),
                    creditMemo.getAmount(),
                    settlementToUnapply.getId(),
                    invoice.getInvoiceNumber()
                )
            );
        }
    }

    public List<SettlementApplication> getFailedSettlementApplications(Invoice.Number invoiceNumber) {
        Validator.validateNonNullArgument(invoiceNumber);
        return invoiceSettlementDAO.getFailedSettlementApplications(invoiceNumber);
    }

    private void validateNoteLength(String note) {
        Validator.validateOptionalStringLength(note, 0, Constants.DEFAULT_MAX_STRING_LENGTH, "note");
    }

    public UUID getAccountPaymentMethodId(PaymentType paymentType, String customerAccountId) {
        Optional<AccountPaymentMethod> matchingAccountPaymentMethod = getManualPaymentMethodOfType(paymentType, customerAccountId);
        UUID accountPaymentMethodId;
        if (matchingAccountPaymentMethod.isEmpty()) {
            var paymentMethod = new AccountPaymentMethod();
            paymentMethod.setAccountId(customerAccountId);
            paymentMethod.setName(paymentType.getDisplayName());
            paymentMethod.setPaymentType(paymentType);

            try {
                accountPaymentMethodId = accountPaymentMethodService.addAccountPaymentMethod(paymentMethod).getId();
            } catch (IllegalStateException ex) {
                // payment method with same name already exists, fetch payment method list again
                matchingAccountPaymentMethod = getManualPaymentMethodOfType(paymentType, customerAccountId);
                if (matchingAccountPaymentMethod.isPresent()) {
                    accountPaymentMethodId = matchingAccountPaymentMethod.get().getId();
                } else {
                    // if still can't find a matching account payment method, something is wrong
                    throw ex;
                }
            }
        } else {
            accountPaymentMethodId = matchingAccountPaymentMethod.get().getId();
        }
        return accountPaymentMethodId;
    }

    public SettlementApplication getSettlementApplicationById(UUID settlementApplicationId) {
        Validator.validateNonNullArgument(settlementApplicationId);
        return invoiceSettlementDAO.getSettlementApplication(settlementApplicationId);
    }

    public void voidPayment(String paymentId, Invoice.Number invoiceNumber, Instant voidDate, BigDecimal invoiceBalanceAmount, String note) {
        Payment payment = paymentGetService.getPaymentByPaymentId(paymentId);
        validateVoidPaymentStatus(payment);
        validateVoidPaymentDate(voidDate, payment);
        validateVoidPaymentForPaymentType(payment);
        List<SettlementApplication> settlementApplications = getSettlementApplicationsByPaymentId(paymentId);
        List<String> distinctInvoiceNumbers = settlementApplications
            .stream()
            .map(SettlementApplication::getInvoiceNumber)
            .distinct()
            .collect(Collectors.toList());
        validatePaymentAppliedToSingleInvoice(paymentId, distinctInvoiceNumbers, invoiceNumber);
        InvoiceBalance invoiceBalance = getAndCompareInvoiceBalance(invoiceNumber, invoiceBalanceAmount);
        validateNoteLength(note);
        voidPaymentAttempt(payment, invoiceBalance, note, voidDate);
    }

    private void validateVoidPaymentStatus(Payment payment) {
        if (payment.getState() == PaymentState.VOIDED) {
            throw new IllegalArgumentException(String.format("Payment with id: %s is already voided", payment.getPaymentId()));
        }
        BigDecimal totalRefund = refundService
            .getRefundsByPaymentId(payment.getPaymentId())
            .stream()
            .map(Refund::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actualPaymentAmount = payment.getAmount().subtract(totalRefund);
        if (!(payment.getState() == PaymentState.SUCCEED && Numbers.equals(actualPaymentAmount, payment.getAmount()))) {
            throw new IllegalArgumentException(String.format("Payment with id: %s is not in PAID status, cannot be voided", payment.getPaymentId()));
        }
    }

    private void validateVoidPaymentDate(Instant voidDate, Payment payment) {
        if (voidDate.isBefore(payment.getPaymentDate())) {
            throw new IllegalArgumentException("Void payment date cannot be before the payment date.");
        }
        if (accountingPeriodService.inClosedAccountingPeriod(payment.getEntityId(), voidDate)) {
            throw new IllegalStateException("Void payment date cannot be before the start date of the current open accounting period.");
        }
    }

    private void validateVoidPaymentForPaymentType(Payment payment) {
        if (paymentGetService.paymentCannotBeVoided(payment)) {
            throw new IllegalArgumentException("Only manual payment can be voided");
        }
    }

    private void validatePaymentAppliedToSingleInvoice(String paymentId, List<String> distinctInvoiceNumbers, Invoice.Number invoiceNumber) {
        if (distinctInvoiceNumbers.size() > 1) {
            throw new IllegalArgumentException(String.format("Payment with id: %s applied to multiple invoices, cannot be voided", paymentId));
        }
        if (distinctInvoiceNumbers.isEmpty() || !distinctInvoiceNumbers.get(0).equals(invoiceNumber.getNumber())) {
            throw new IllegalArgumentException(
                String.format("Payment with id: %s is not applied to invoice with invoice number: %s", paymentId, invoiceNumber)
            );
        }
    }

    private void voidPaymentAttempt(Payment payment, InvoiceBalance invoiceBalance, String note, Instant voidDate) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        dslContext.transaction(configuration -> voidPaymentAttemptInTransaction(payment, invoiceBalance, note, voidDate, configuration));
        generateInvoiceDocumentAsync(invoiceBalance.getInvoiceNumber());
    }

    private void voidPaymentAttemptInTransaction(
        Payment payment,
        InvoiceBalance invoiceBalance,
        String note,
        Instant voidDate,
        Configuration configuration
    ) {
        String paymentId = payment.getPaymentId();
        PaymentBalance paymentBalance = getPaymentBalance(paymentId);

        // TODO: we should indicate which settlement this is voiding
        SettlementApplication settlementApplication = SettlementApplication.builder()
            .entityId(payment.getEntityId())
            .customerAccountId(invoiceBalance.getCustomerAccountId())
            .invoiceNumber(invoiceBalance.getInvoiceNumber())
            .paymentId(paymentId)
            .applicationType(SettlementApplicationType.VOID_PAYMENT)
            .amount(payment.getAmount().negate())
            .note(note)
            .appliedOn(voidDate)
            .build();

        SettlementApplication settlementApplicationWithFx = populateFunctionalAmounts(settlementApplication, payment);
        SettlementApplication savedApplication = addVoidPaymentApplication(
            configuration,
            settlementApplicationWithFx,
            invoiceBalance,
            paymentBalance
        );
        paymentService.updateVoidedPaymentAttributesInTransaction(
            paymentId,
            PaymentState.VOIDED,
            PaymentStatus.VOIDED,
            configuration.dsl(),
            voidDate
        );
        String tenantId = tenantIdProvider.provideTenantIdString();
        byte[] payloadBytes = savedApplication.getId().toString().getBytes(StandardCharsets.UTF_8);
        eventPublishingService.publishEventInTransaction(
            configuration,
            EventType.PAYMENT_VOIDED,
            tenantId,
            settlementApplication.getEntityId(),
            settlementApplication.getCustomerAccountId(),
            payloadBytes
        );
    }

    public boolean checkIfOngoingPaymentExistsForInvoice(Invoice.Number invoiceNumber) {
        return getSettlementApplications(invoiceNumber)
            .stream()
            .anyMatch(sa -> sa.getStatus() == SettlementApplicationStatus.ATTEMPTING_PAYMENT_COLLECTION);
    }

    public boolean hasAppliedCredit(Invoice.Number invoiceNumber) {
        var settlementApplications = getSettlementApplications(invoiceNumber);
        Set<UUID> negatedCreditSettlementIds = settlementApplications
            .stream()
            .filter(sa -> sa.getApplicationType() == SettlementApplicationType.UNAPPLY_CREDIT)
            .map(SettlementApplication::getNegatedSettlementId)
            .collect(Collectors.toSet());
        Set<UUID> appliedCreditSettlementIds = settlementApplications
            .stream()
            .filter(sa -> sa.getApplicationType() == SettlementApplicationType.CREDIT)
            .map(SettlementApplication::getId)
            .collect(Collectors.toSet());
        return CollectionUtils.isNotEmpty(SetUtils.difference(appliedCreditSettlementIds, negatedCreditSettlementIds));
    }

    public void deleteSettlementApplicationsForPayment(String paymentId, Set<UUID> settlementApplicationIds) {
        Validator.validateNonNullArgument(settlementApplicationIds);
        if (CollectionUtils.isEmpty(settlementApplicationIds)) {
            return;
        }
        invoiceSettlementDAO.deleteSettlementApplicationsForPayment(paymentId, settlementApplicationIds);
    }

    public void deletePaymentBalance(String paymentId, BigDecimal currentBalance) {
        Validator.validateStringNotBlank(paymentId, "paymentId");
        Validator.validateNonNullArgument(currentBalance, "currentBalance");
        if (currentBalance.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidInputException("Current payment balance cannot be negative.");
        }

        invoiceSettlementDAO.deletePaymentBalance(paymentId, currentBalance);
    }

    public void handleScheduledPaymentRetriesForInvoice(DSLContext txnContext, Invoice.Number invoiceNumber) {
        if (featureService.isEnabled(Feature.PAYMENT_RETRY)) {
            LOGGER.info("Handling scheduled payment retries for invoice {}", invoiceNumber.getNumber());
            List<SettlementApplication> inProgressSettlements = getSettlementApplications(invoiceNumber)
                .stream()
                .filter(sa -> sa.getStatus() == SettlementApplicationStatus.ATTEMPTING_PAYMENT_COLLECTION)
                .toList();

            if (inProgressSettlements.isEmpty()) {
                LOGGER.info("No in-progress payment settlement applications found for invoice {}", invoiceNumber.getNumber());
                return;
            }

            if (inProgressSettlements.size() > 1) {
                throw new ServiceFailureException(
                    String.format(
                        "There are multiple settlement applications in progress for invoice %s, please wait for them to complete before applying a new payment.",
                        invoiceNumber.getNumber()
                    )
                );
            }

            SettlementApplication attemptingPaymentSettlement = inProgressSettlements.get(0);
            Payment payment = paymentGetService.getPaymentByPaymentId(attemptingPaymentSettlement.getPaymentId());

            List<PaymentAttempt> paymentAttempts = paymentGetService.getPaymentAttemptsByPaymentId(payment.getPaymentId());

            if (!paymentAttempts.isEmpty() && paymentAttempts.stream().anyMatch(pa -> !pa.getState().isTerminal())) {
                String message = String.format(
                    "There is an in-progress payment %s for invoice %s, please wait for it to complete before applying a new payment.",
                    payment.getPaymentId(),
                    invoiceNumber.getNumber()
                );
                throw new ConflictingStateException(message);
            }

            markSettlementApplicationAsFailed(attemptingPaymentSettlement, Instant.now(), txnContext);
            payment.setState(PaymentState.FAILED);
            paymentService.updatePayment(payment, txnContext);
            LOGGER.info(
                "Marked settlement application {} as failed and updated payment {} to FAILED state for invoice {}",
                attemptingPaymentSettlement.getId(),
                payment.getPaymentId(),
                invoiceNumber.getNumber()
            );
        }
    }

    public SettlementApplication addVoidPaymentApplication(
        Configuration configuration,
        SettlementApplication settlementApplication,
        InvoiceBalance currentInvoiceBalance,
        PaymentBalance currentPaymentBalance
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        BigDecimal amount = settlementApplication.getAmount();
        LocalDateTime currentTimestamp = DateTimeConverter.instantToLocalDateTime(Instant.now());
        try {
            SettlementApplication savedApplication = invoiceSettlementDAO.upsertSettlementApplication(
                settlementApplication,
                tenantId,
                Optional.of(configuration),
                Optional.of(currentTimestamp)
            );
            updateInvoiceBalance(currentInvoiceBalance, amount, configuration, currentTimestamp);
            if (currentPaymentBalance.getBalance().compareTo(BigDecimal.ZERO) != 0) {
                invoiceSettlementDAO.updatePaymentBalance(currentPaymentBalance, currentPaymentBalance.getBalance(), configuration, currentTimestamp);
            }
            return savedApplication;
        } catch (DataAccessException ex) {
            if (PostgresErrorHandler.causeIsUniqueConstraintViolation(ex)) {
                LOGGER.warn("Duplicate key applying new payment application", ex);
                throw new ConcurrentModificationException("Record being inserted already exists");
            }
            throw ex;
        }
    }

    public SettlementApplication addPaymentApplication(
        Configuration configuration,
        SettlementApplication settlementApplication,
        InvoiceBalance currentInvoiceBalance,
        PaymentBalance currentPaymentBalance
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var amount = settlementApplication.getAmount();
        var currentTimestamp = DateTimeConverter.instantToLocalDateTime(Instant.now());

        try {
            SettlementApplication savedApplication = invoiceSettlementDAO.upsertSettlementApplication(
                settlementApplication,
                tenantId,
                Optional.of(configuration),
                Optional.of(currentTimestamp)
            );
            updateInvoiceBalance(currentInvoiceBalance, amount, configuration, currentTimestamp);
            invoiceSettlementDAO.updatePaymentBalance(currentPaymentBalance, amount, configuration, currentTimestamp);
            return savedApplication;
        } catch (DataAccessException ex) {
            if (PostgresErrorHandler.causeIsUniqueConstraintViolation(ex)) {
                LOGGER.warn("Duplicate key applying new payment application", ex);
                throw new ConcurrentModificationException("Record being inserted already exists");
            }

            throw ex;
        }
    }

    public SettlementApplication addCreditApplication(
        SettlementApplication settlementApplication,
        InvoiceBalance currentInvoiceBalance,
        CreditMemoBalance currentCreditMemoBalance
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var amount = settlementApplication.getAmount();
        var currentTimestamp = DateTimeConverter.instantToLocalDateTime(Instant.now());

        return dslContext.transactionResult(configuration -> {
            try {
                SettlementApplication savedSettlement = invoiceSettlementDAO.upsertSettlementApplication(
                    settlementApplication,
                    tenantId,
                    Optional.of(configuration),
                    Optional.of(currentTimestamp)
                );
                updateInvoiceBalance(currentInvoiceBalance, amount, configuration, currentTimestamp);
                invoiceSettlementDAO.updateCreditMemoBalance(
                    tenantIdProvider.provideTenantIdString(),
                    currentCreditMemoBalance,
                    amount,
                    DSL.using(configuration),
                    currentTimestamp
                );
                return savedSettlement;
            } catch (DataAccessException ex) {
                if (PostgresErrorHandler.causeIsUniqueConstraintViolation(ex)) {
                    LOGGER.warn("Duplicate key inserting payment balance", ex);
                    throw new ConcurrentModificationException("Record being inserted already exists");
                }
                throw new ServiceFailureException("unable to save credit application", ex);
            }
        });
    }

    public InvoiceBalance updateInvoiceBalance(
        InvoiceBalance currentInvoiceBalance,
        BigDecimal amount,
        Configuration configuration,
        LocalDateTime updatedOn
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        BigDecimal updatedInvoiceBalance = currentInvoiceBalance.getBalance().subtract(amount);
        if (Numbers.isNegative(updatedInvoiceBalance)) {
            throw new IllegalArgumentException("Cannot apply a settlement application that exceeds the invoice balance");
        }

        InvoiceBalance updateResult = invoiceSettlementDAO.updateInvoiceBalance(
            currentInvoiceBalance,
            updatedInvoiceBalance,
            updatedOn,
            configuration
        );

        if (currentInvoiceBalance.getBalance().signum() > 0 && updatedInvoiceBalance.signum() <= 0) {
            invoiceSettlementDAO.setInvoiceStatus(
                currentInvoiceBalance.getInvoiceNumber(),
                InvoiceStatus.POSTED,
                InvoiceStatus.PAID,
                tenantId,
                configuration,
                updatedOn
            );
            Invoice invoice = invoiceRetrievalService.getInvoiceByInvoiceNumberInTransaction(
                new Invoice.Number(currentInvoiceBalance.getInvoiceNumber()),
                configuration
            );
            invoiceEventService.publishPaidEventInTransaction(configuration, invoice);
        } else if (currentInvoiceBalance.getBalance().signum() <= 0 && updatedInvoiceBalance.signum() > 0) {
            invoiceSettlementDAO.setInvoiceStatus(
                currentInvoiceBalance.getInvoiceNumber(),
                InvoiceStatus.PAID,
                InvoiceStatus.POSTED,
                tenantId,
                configuration,
                updatedOn
            );
        }

        return updateResult;
    }
}
