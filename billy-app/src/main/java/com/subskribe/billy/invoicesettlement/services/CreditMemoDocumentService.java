package com.subskribe.billy.invoicesettlement.services;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.document.model.DocumentType;
import com.subskribe.billy.document.model.GeneratedDocument;
import com.subskribe.billy.document.service.DocumentService;
import com.subskribe.billy.invoicesettlement.document.CreditMemoDocumentDataAggregator;
import com.subskribe.billy.invoicesettlement.document.CreditMemoDocumentJson;
import java.io.InputStream;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;

public class CreditMemoDocumentService {

    private final CreditMemoDocumentDataAggregator creditMemoDocumentDataAggregator;
    private final BillyConfiguration billyConfiguration;
    private final String s3Bucket;
    private final DocumentService documentService;
    private final CreditMemoDocumentRenderService documentRenderService;

    @Inject
    public CreditMemoDocumentService(
        CreditMemoDocumentDataAggregator creditMemoDocumentDataAggregator,
        BillyConfiguration billyConfiguration,
        DocumentService documentService,
        CreditMemoDocumentRenderService documentRenderService
    ) {
        this.creditMemoDocumentDataAggregator = creditMemoDocumentDataAggregator;
        s3Bucket = billyConfiguration.getDocumentConfiguration().getCreditMemoS3Bucket();
        this.billyConfiguration = billyConfiguration;
        this.documentService = documentService;
        this.documentRenderService = documentRenderService;
    }

    public void createCreditMemoDocument(String creditMemoNumber) {
        if (!billyConfiguration.isLocalOrCi() && !billyConfiguration.getDocumentConfiguration().isEnabled()) {
            return;
        }
        CreditMemoDocumentJson documentJsonObject = creditMemoDocumentDataAggregator.generateDocumentDataForCreditMemo(creditMemoNumber);
        documentRenderService.generatePdfOnS3(documentJsonObject);
    }

    public Optional<InputStream> getCreditMemoDocumentByCreditMemoNumber(String creditMemoNumber) {
        GeneratedDocument creditMemoDocument = documentService.getLatestDocument(creditMemoNumber, DocumentType.CREDIT_MEMO);
        if (StringUtils.isBlank(creditMemoDocument.getDocumentLocationIdentifier())) {
            return Optional.empty();
        }

        return documentService.getDocumentFromS3(s3Bucket, creditMemoDocument.getDocumentLocationIdentifier());
    }
}
