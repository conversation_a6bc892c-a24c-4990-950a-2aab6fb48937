package com.subskribe.billy.invoicesettlement.services;

import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.TenantPartitionKey;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.invoicesettlement.db.RefundDAO;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.model.CreditMemoBalance;
import com.subskribe.billy.invoicesettlement.model.CreditMemoStatus;
import com.subskribe.billy.invoicesettlement.model.Refund;
import com.subskribe.billy.invoicesettlement.model.RefundPreview;
import com.subskribe.billy.metrics.datadog.AllMetrics;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.resources.json.invoicesettlement.RefundDetail;
import com.subskribe.billy.resources.json.invoicesettlement.RefundDetailMapper;
import com.subskribe.billy.shared.Constants;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicReference;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.mapstruct.factory.Mappers;

@AllMetrics
public class RefundService {

    private final RefundDAO refundDAO;
    private final CreditMemoRetrievalService creditMemoRetrievalService;
    private final PaymentGetService paymentGetService;
    private final RefundIdGenerator refundIdGenerator;
    private final TenantSettingService tenantSettingService;
    private final AccountingPeriodService accountingPeriodService;
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;
    private final UncheckedObjectMapper objectMapper;
    private final EventPublishingService eventPublishingService;
    private final RefundDetailMapper refundDetailMapper;

    @Inject
    public RefundService(
        RefundDAO refundDAO,
        CreditMemoRetrievalService creditMemoRetrievalService,
        PaymentGetService paymentGetService,
        RefundIdGenerator refundIdGenerator,
        TenantSettingService tenantSettingService,
        AccountingPeriodService accountingPeriodService,
        TenantIdProvider tenantIdProvider,
        DSLContextProvider dslContextProvider,
        EventPublishingService eventPublishingService
    ) {
        this.refundDAO = refundDAO;
        this.creditMemoRetrievalService = creditMemoRetrievalService;
        this.paymentGetService = paymentGetService;
        this.refundIdGenerator = refundIdGenerator;
        this.tenantSettingService = tenantSettingService;
        this.accountingPeriodService = accountingPeriodService;
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        this.eventPublishingService = eventPublishingService;
        this.objectMapper = UncheckedObjectMapper.defaultMapper();
        refundDetailMapper = Mappers.getMapper(RefundDetailMapper.class);
    }

    public Refund createAndApplyRefund(Refund refund) {
        var currBalance = creditMemoRetrievalService.getCreditMemoBalance(refund.getCreditMemoNumber());
        var currCreditMemo = creditMemoRetrievalService.getCreditMemoByNumber(refund.getCreditMemoNumber());
        validateRefundToApply(refund, currCreditMemo, currBalance);

        refund.setRefundId(refundIdGenerator.generate());
        refund.setEntityId(currCreditMemo.getEntityId());
        refund.setCurrencyCode(currCreditMemo.getCurrencyCode());
        return TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider).transactionResult(configuration -> {
            Refund appliedRefund = refundDAO.createAndApplyRefund(currBalance, refund, configuration);
            sendRefundEvent(refund, EventType.REFUND_GENERATED, configuration);
            return appliedRefund;
        });
    }

    public List<Refund> getRefunds(String accountId, String creditMemoNumber) {
        return refundDAO.getRefunds(accountId, creditMemoNumber);
    }

    public List<Refund> getRefunds(String accountId) {
        return refundDAO.getRefunds(accountId);
    }

    public List<Refund> getRefundsByPaymentId(String paymentId) { //TODO: validate
        return refundDAO.getRefundsByPaymentId(paymentId);
    }

    public Refund getRefundById(String refundId, String accountId) {
        return refundDAO.getRefundById(refundId, accountId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.REFUND, refundId));
    }

    private void validateRefundToApply(Refund refund, CreditMemo creditMemo, CreditMemoBalance creditMemoBalance) {
        Validator.validateNonNullArgument(refund, "refund");
        Validator.validateNonNullArgument(refund.getAmount(), "amount");
        Validator.validateOptionalStringLength(refund.getNotes(), 0, Constants.DEFAULT_MAX_STRING_LENGTH, "notes");

        // validate payment exist
        Payment payment = paymentGetService.getPaymentByPaymentId(refund.getPaymentId());

        validateRefundAmount(refund, creditMemoBalance, payment);
        validateRefundDate(refund, payment);
        validateCurrenciesMatch(creditMemo, payment);

        if (creditMemo.getStatus() != CreditMemoStatus.POSTED) {
            throw new IllegalArgumentException("Credit memo must be in POSTED status");
        }
    }

    private void validateCurrenciesMatch(CreditMemo creditMemo, Payment payment) {
        if (!creditMemo.getCurrencyCode().equals(payment.getCurrencyCode())) {
            throw new InvalidInputException(
                String.format(
                    "Credit memo currency: %s does not match payment currency: %s, refund cannot be applied.",
                    creditMemo.getCurrencyCode(),
                    payment.getCurrencyCode()
                )
            );
        }
    }

    private void validateRefundAmount(Refund refund, CreditMemoBalance creditMemoBalance, Payment payment) {
        if (refund.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException(String.format("Refund must be greater than 0, received %s", refund.getAmount()));
        }

        if (refund.getAmount().compareTo(creditMemoBalance.getBalance()) > 0) {
            throw new IllegalArgumentException(
                String.format(
                    "Refund amount cannot be more than credit memo balance amount, received refund amount %s for credit memo balance amount %s",
                    refund.getAmount(),
                    creditMemoBalance.getBalance()
                )
            );
        }

        BigDecimal effectivePaymentReceived = getEffectivePaymentReceived(payment);
        if (refund.getAmount().compareTo(effectivePaymentReceived) > 0) {
            throw new IllegalArgumentException(
                String.format(
                    "Refund amount cannot be more than effective payment received, received refund amount %s for effective payment received amount %s",
                    refund.getAmount(),
                    effectivePaymentReceived
                )
            );
        }
    }

    private void validateRefundDate(Refund refund, Payment payment) {
        if (accountingPeriodService.inClosedAccountingPeriod(payment.getEntityId(), refund.getRefundDate())) {
            throw new IllegalStateException("Cannot create refund with date earlier than current open accounting period.");
        }

        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        Instant paymentDateStartOfDay = DateTimeCalculator.getStartOfDay(payment.getPaymentDate(), tenantTimeZone.toZoneId());
        Instant refundDateStartOfDay = DateTimeCalculator.getStartOfDay(refund.getRefundDate(), tenantTimeZone.toZoneId());
        Instant currentInstant = DateTimeCalculator.getStartOfDay(Instant.now(), tenantTimeZone.toZoneId());

        if (paymentDateStartOfDay.isAfter(refundDateStartOfDay) || refundDateStartOfDay.isAfter(currentInstant)) {
            String message = String.format(
                "Refund date should be in between payment date and current date. Payment date: %s, Current date: %s, refundDate: %s",
                payment.getPaymentDate(),
                currentInstant,
                refund.getRefundDate()
            );
            throw new IllegalArgumentException(message);
        }
    }

    public RefundPreview generateRefund(String creditMemoNumber, String paymentId) {
        Validator.validateStringNotBlank(creditMemoNumber, "CreditMemoNumber is blank");
        Validator.validateNonNullArgument(paymentId);

        // validate creditMemo and payment exist
        creditMemoRetrievalService.getCreditMemoByNumber(creditMemoNumber);
        Payment payment = paymentGetService.getPaymentByPaymentId(paymentId);

        CreditMemoBalance creditMemoBalance = creditMemoRetrievalService.getCreditMemoBalance(creditMemoNumber);
        BigDecimal effectivePaymentMade = getEffectivePaymentReceived(payment);

        RefundPreview refundPreview = new RefundPreview();
        refundPreview.setCreditMemoNumber(creditMemoNumber);
        refundPreview.setAppliedDate(Instant.now());
        refundPreview.setPaymentId(paymentId);

        BigDecimal refundAvailable = creditMemoBalance.getBalance().compareTo(effectivePaymentMade) >= 0
            ? effectivePaymentMade
            : creditMemoBalance.getBalance();
        refundPreview.setRefundAvailable(refundAvailable);
        refundPreview.setRefundAmount(refundAvailable);

        return refundPreview;
    }

    private BigDecimal getEffectivePaymentReceived(Payment payment) {
        List<Refund> refundsAppliedForPayments = getRefundsByPaymentId(payment.getPaymentId());

        AtomicReference<BigDecimal> effectivePaymentReceived = new AtomicReference<>(payment.getAmount());
        refundsAppliedForPayments.forEach(refund -> effectivePaymentReceived.set(effectivePaymentReceived.get().subtract(refund.getAmount())));

        return effectivePaymentReceived.get();
    }

    private void sendRefundEvent(Refund refund, EventType eventType, Configuration configuration) throws Exception {
        RefundDetail refundDetail = refundDetailMapper.toDetail(refund);
        eventPublishingService.publishEventInTransaction(
            configuration,
            eventType,
            tenantIdProvider.provideTenantIdString(),
            Set.of(refund.getEntityId()),
            new TenantPartitionKey(tenantIdProvider.provideTenantIdString()),
            objectMapper.writeValueAsBytes(refundDetail)
        );
    }
}
