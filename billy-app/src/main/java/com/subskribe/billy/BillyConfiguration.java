package com.subskribe.billy;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableMap;
import com.subskribe.ai.service.openai.OpenAIConfiguration;
import com.subskribe.billy.approvalflow.model.ApprovalFlowConfiguration;
import com.subskribe.billy.attachments.model.AttachmentConfiguration;
import com.subskribe.billy.auth.model.CognitoConfiguration;
import com.subskribe.billy.auth.model.EmailLoginLinkConfiguration;
import com.subskribe.billy.auth.model.InternalLoginConfiguration;
import com.subskribe.billy.auth.model.SecretsManagerConfiguration;
import com.subskribe.billy.aws.model.AppConfigConfiguration;
import com.subskribe.billy.aws.model.DLQConfiguration;
import com.subskribe.billy.aws.model.ElasticSearchConfiguration;
import com.subskribe.billy.aws.model.LambdaConfiguration;
import com.subskribe.billy.aws.model.SesConfiguration;
import com.subskribe.billy.aws.model.SnsConfiguration;
import com.subskribe.billy.cache.MemcachedConfiguration;
import com.subskribe.billy.crm.model.CrmConfiguration;
import com.subskribe.billy.customfield.CustomFieldConfiguration;
import com.subskribe.billy.customization.service.ui.UIConfigurationConfig;
import com.subskribe.billy.di.hk2.providers.auditlog.AuditLogConfiguration;
import com.subskribe.billy.discount.FixedAmountDiscountConfiguration;
import com.subskribe.billy.document.model.DocumentConfiguration;
import com.subskribe.billy.docusign.DocuSignConfiguration;
import com.subskribe.billy.email.CreditMemoEmailConfiguration;
import com.subskribe.billy.email.HubSpotEmailConfiguration;
import com.subskribe.billy.email.InvoiceEmailConfiguration;
import com.subskribe.billy.email.model.EmailNotificationQueueConfiguration;
import com.subskribe.billy.entity.EntityConfiguration;
import com.subskribe.billy.erp.netsuite.config.NetsuiteConfiguration;
import com.subskribe.billy.erp.quickbooks.QuickbooksConfiguration;
import com.subskribe.billy.esign.model.ElectronicSignatureConfiguration;
import com.subskribe.billy.event.config.KinesisConfiguration;
import com.subskribe.billy.filter.http.DosFilterConfiguration;
import com.subskribe.billy.filter.logger.RequestHeaderLoggerConfiguration;
import com.subskribe.billy.foreignexchange.FxConfiguration;
import com.subskribe.billy.hubspot.config.HubSpotConfiguration;
import com.subskribe.billy.idempotency.model.DynamoDbConfiguration;
import com.subskribe.billy.idempotency.model.IdempotencyConfiguration;
import com.subskribe.billy.infra.TenantNameCacheConfiguration;
import com.subskribe.billy.invoice.model.AvalaraConfiguration;
import com.subskribe.billy.jooq.QueryTimeoutConfiguration;
import com.subskribe.billy.log.RequestLoggingConfiguration;
import com.subskribe.billy.looker.LookerConfiguration;
import com.subskribe.billy.metricsreporting.config.QuartzReportingJobConfiguration;
import com.subskribe.billy.metricsreporting.model.ArrMetricsReportingConfiguration;
import com.subskribe.billy.notification.model.NotificationConfiguration;
import com.subskribe.billy.order.model.TenantSealProtectionConfiguration;
import com.subskribe.billy.payment.model.CustomPaymentTypeConfiguration;
import com.subskribe.billy.payment.stripe.StripeConfiguration;
import com.subskribe.billy.ratelimiter.config.IpBasedRequestRateLimiterConfiguration;
import com.subskribe.billy.ratelimiter.config.RateLimiterConfiguration;
import com.subskribe.billy.revrec.RevenueRecognitionConfiguration;
import com.subskribe.billy.salesforce.config.SalesforceConfiguration;
import com.subskribe.billy.scheduler.log.LogHeartbeatConfiguration;
import com.subskribe.billy.scheduler.model.EventPumpConfiguration;
import com.subskribe.billy.scheduler.model.QuartzApiKeyGarbageCollectorJobConfiguration;
import com.subskribe.billy.scheduler.model.QuartzBillyAdminReporterJobConfiguration;
import com.subskribe.billy.scheduler.model.QuartzBulkInvoiceRunConfiguration;
import com.subskribe.billy.scheduler.model.QuartzBulkRevenueRecognitionConfiguration;
import com.subskribe.billy.scheduler.model.QuartzConfiguration;
import com.subskribe.billy.scheduler.model.QuartzCronJobConfiguration;
import com.subskribe.billy.scheduler.model.QuartzDunningConfiguration;
import com.subskribe.billy.scheduler.model.QuartzInvoiceGeneratorConfiguration;
import com.subskribe.billy.scheduler.model.QuartzMetricsUpdaterConfiguration;
import com.subskribe.billy.scheduler.model.QuartzNotificationRetryJobConfiguration;
import com.subskribe.billy.scheduler.model.QuartzOrderExpiryConfiguration;
import com.subskribe.billy.scheduler.model.QuartzPaymentReconciliationJobConfiguration;
import com.subskribe.billy.scheduler.model.QuartzQueueConfiguration;
import com.subskribe.billy.scheduler.model.QuartzRevenueRecognitionJobConfiguration;
import com.subskribe.billy.scheduler.model.QuartzUsageAggregationConfiguration;
import com.subskribe.billy.search.model.SearchUpdaterThreadConfiguration;
import com.subskribe.billy.shared.db.DBCPConfiguration;
import com.subskribe.billy.shared.db.DBCPFactory;
import com.subskribe.billy.shared.task.queue.config.TaskQueueConfiguration;
import com.subskribe.billy.tax.tros.client.TaxConfiguration;
import com.subskribe.billy.usage.model.UsageAggregationConfiguration;
import io.dropwizard.Configuration;
import io.dropwizard.flyway.FlywayFactory;
import io.federecio.dropwizard.swagger.SwaggerBundleConfiguration;
import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.glassfish.hk2.api.Immediate;

@SuppressWarnings("unused")
@Immediate
public class BillyConfiguration extends Configuration {

    private static final String LOCALHOST_PREFIX = "http://localdev";

    private static final String DEV_ENVIRONMENT_NAME_PREFIX = "dev";

    private static final String PROD_ENVIRONMENT_NAME = "prod";

    private static final String QA_ENVIRONMENT_NAME_PREFIX = "qa";

    private static final String SANDBOX_ENVIRONMENT_NAME_PREFIX = "sandbox";

    @Valid
    @NotNull
    @JsonIgnore
    private DBCPFactory database = new DBCPFactory();

    @Valid
    @NotNull
    @JsonIgnore
    private final DBCPFactory readOnlyDatabase = new DBCPFactory();

    @Valid
    @NotNull
    private FlywayFactory flywayFactory = new FlywayFactory();

    @Valid
    @NotNull
    private DBCPConfiguration dbcpConfiguration = new DBCPConfiguration();

    @Valid
    @NotNull
    private DBCPConfiguration dbcpReadOnlyConfiguration = new DBCPConfiguration();

    @NotNull
    private final CognitoConfiguration cognitoConfiguration = new CognitoConfiguration();

    @NotNull
    private final DynamoDbConfiguration dynamoDbConfiguration = new DynamoDbConfiguration();

    @NotNull
    private final SecretsManagerConfiguration secretsManagerConfiguration = new SecretsManagerConfiguration();

    @NotNull
    private final QuartzQueueConfiguration quartzQueueConfiguration = new QuartzQueueConfiguration();

    @NotNull
    private final SnsConfiguration snsConfiguration = new SnsConfiguration();

    @NotNull
    private final SesConfiguration sesConfiguration = new SesConfiguration();

    @NotNull
    private final LambdaConfiguration lambdaConfiguration = new LambdaConfiguration();

    @NotNull
    private final NotificationConfiguration notificationConfiguration = new NotificationConfiguration();

    @NotNull
    private final DLQConfiguration dlqConfiguration = new DLQConfiguration();

    @NotNull
    private final SearchUpdaterThreadConfiguration searchUpdaterThreadConfiguration = new SearchUpdaterThreadConfiguration();

    @NotNull
    private final QuartzInvoiceGeneratorConfiguration quartzInvoiceGeneratorConfiguration = new QuartzInvoiceGeneratorConfiguration();

    @NotNull
    private final QuartzDunningConfiguration quartzDunningConfiguration = new QuartzDunningConfiguration();

    @NotNull
    private final QuartzConfiguration quartzConfiguration = new QuartzConfiguration();

    @NotNull
    private final DocumentConfiguration documentConfiguration = new DocumentConfiguration();

    @NotNull
    private final ElasticSearchConfiguration elasticSearchConfiguration = new ElasticSearchConfiguration();

    @NotNull
    private final SalesforceConfiguration salesforceConfiguration = new SalesforceConfiguration();

    @NotNull
    private final IdempotencyConfiguration idempotencyConfiguration = new IdempotencyConfiguration();

    @NotNull
    private final RateLimiterConfiguration rateLimiterConfiguration = new RateLimiterConfiguration();

    @NotNull
    private final AvalaraConfiguration avalaraConfiguration = new AvalaraConfiguration();

    @NotNull
    private final MemcachedConfiguration memcachedConfiguration = new MemcachedConfiguration();

    @NotNull
    private final DocuSignConfiguration docuSignConfiguration = new DocuSignConfiguration();

    @NotNull
    private final RequestHeaderLoggerConfiguration requestHeaderLoggerConfiguration = new RequestHeaderLoggerConfiguration();

    @NotNull
    private final IpBasedRequestRateLimiterConfiguration ipBasedRequestRateLimiterConfiguration = new IpBasedRequestRateLimiterConfiguration();

    @NotNull
    private final StripeConfiguration stripeConfiguration = new StripeConfiguration();

    @NotNull
    private final FixedAmountDiscountConfiguration fixedAmountDiscountConfiguration = new FixedAmountDiscountConfiguration();

    @NotNull
    @JsonProperty("quartzUsageAggregation")
    private final QuartzUsageAggregationConfiguration quartzAggregationConfiguration = new QuartzUsageAggregationConfiguration();

    @NotNull
    @JsonProperty("quartzRevenueRecognition")
    private final QuartzRevenueRecognitionJobConfiguration quartzRevenueRecognitionJobConfiguration = new QuartzRevenueRecognitionJobConfiguration();

    @NotNull
    @JsonProperty("quartzBulkInvoiceRun")
    private final QuartzBulkInvoiceRunConfiguration quartzBulkInvoiceRunConfiguration = new QuartzBulkInvoiceRunConfiguration();

    @NotNull
    @JsonProperty("usageAggregationConfiguration")
    private final UsageAggregationConfiguration usageAggregationConfiguration = new UsageAggregationConfiguration();

    @NotNull
    @JsonProperty("quartzPaymentProcessor")
    private final QuartzCronJobConfiguration quartzPaymentJobConfiguration = new QuartzCronJobConfiguration();

    @NotNull
    @JsonProperty("quartzPaymentReconciler")
    private final QuartzPaymentReconciliationJobConfiguration quartzPaymentReconciliationJobConfiguration =
        new QuartzPaymentReconciliationJobConfiguration();

    @NotNull
    @JsonProperty("eventPump")
    protected final EventPumpConfiguration eventPumpConfiguration = new EventPumpConfiguration();

    @NotNull
    @JsonProperty("quartzBillyAdminReporterJob")
    protected final QuartzBillyAdminReporterJobConfiguration quartzBillyAdminReporterJobConfiguration =
        new QuartzBillyAdminReporterJobConfiguration();

    @NotNull
    @JsonProperty("quartzApiKeyGarbageCollectorJob")
    protected final QuartzApiKeyGarbageCollectorJobConfiguration quartzApiKeyGarbageCollectorJobConfiguration =
        new QuartzApiKeyGarbageCollectorJobConfiguration();

    @NotNull
    @JsonProperty("emailLinkLogin")
    protected final EmailLoginLinkConfiguration emailLoginLinkConfiguration = new EmailLoginLinkConfiguration();

    @NotNull
    private final AttachmentConfiguration attachmentConfiguration = new AttachmentConfiguration();

    @NotNull
    @JsonProperty("approvalFlow")
    private final ApprovalFlowConfiguration approvalFlowConfiguration = new ApprovalFlowConfiguration();

    @NotNull
    @JsonProperty("invoiceEmail")
    private final InvoiceEmailConfiguration invoiceEmailConfiguration = new InvoiceEmailConfiguration();

    @NotNull
    @JsonProperty("creditMemoEmail")
    private final CreditMemoEmailConfiguration creditMemoEmailConfiguration = new CreditMemoEmailConfiguration();

    @NotNull
    @JsonProperty("customField")
    private final CustomFieldConfiguration customFieldConfiguration = new CustomFieldConfiguration();

    @NotNull
    @JsonProperty("hubSpotEmail")
    private final HubSpotEmailConfiguration hubSpotEmailConfiguration = new HubSpotEmailConfiguration();

    @NotNull
    @JsonProperty("internalLogin")
    private final InternalLoginConfiguration internalLoginConfiguration = new InternalLoginConfiguration();

    private final AppConfigConfiguration appConfigConfiguration = new AppConfigConfiguration();

    @NotNull
    @JsonProperty
    private final ArrMetricsReportingConfiguration arrMetricsReportingConfiguration = new ArrMetricsReportingConfiguration();

    @NotNull
    @JsonProperty
    private final QuartzNotificationRetryJobConfiguration quartzNotificationRetryJobConfiguration = new QuartzNotificationRetryJobConfiguration();

    @NotNull
    @JsonProperty
    private final TenantNameCacheConfiguration tenantNameCacheConfiguration = new TenantNameCacheConfiguration();

    @NotNull
    @JsonProperty
    private final TenantSealProtectionConfiguration tenantSealProtectionConfiguration = new TenantSealProtectionConfiguration();

    @NotNull
    @JsonProperty
    private final CrmConfiguration crmConfiguration = new CrmConfiguration();

    @NotNull
    @JsonProperty
    private final EntityConfiguration entityConfiguration = new EntityConfiguration();

    @NotNull
    @JsonProperty
    private final FxConfiguration fxConfiguration = new FxConfiguration();

    @NotNull
    @JsonProperty("logHeartbeat")
    private final LogHeartbeatConfiguration logHeartbeatConfiguration = new LogHeartbeatConfiguration();

    @NotNull
    @JsonProperty("quartzEntityInvariants")
    private final QuartzCronJobConfiguration quartzEntityInvariantsJobConfiguration = new QuartzCronJobConfiguration();

    @NotNull
    @JsonProperty("quartzAccountingInvariants")
    private final QuartzCronJobConfiguration quartzAccountingInvariantsJobConfiguration = new QuartzCronJobConfiguration();

    @NotNull
    @JsonProperty("quartzExchangeRateRefresh")
    private final QuartzCronJobConfiguration quartzExchangeRateRefreshJobConfiguration = new QuartzCronJobConfiguration();

    @NotNull
    private final QuartzCronJobConfiguration quartzQuickbooksPingJobConfiguration = new QuartzCronJobConfiguration();

    @NotNull
    @JsonProperty("taskQueue")
    private final TaskQueueConfiguration taskQueueConfiguration = new TaskQueueConfiguration();

    @NotNull
    @JsonProperty("kinesis")
    private final KinesisConfiguration kinesisConfiguration = new KinesisConfiguration();

    @NotNull
    @JsonProperty("netsuite")
    private final NetsuiteConfiguration netsuiteConfiguration = new NetsuiteConfiguration();

    @NotNull
    @JsonProperty("electronicSignature")
    private final ElectronicSignatureConfiguration electronicSignatureConfiguration = new ElectronicSignatureConfiguration();

    private Map<String, Boolean> dynamicFeatures;

    private String siteUrl;

    private String apiUrl;

    private String envName;

    @JsonProperty("swagger")
    public SwaggerBundleConfiguration swaggerBundleConfiguration;

    private final AuditLogConfiguration auditLogConfiguration = new AuditLogConfiguration();

    @NotNull
    private final QuartzMetricsUpdaterConfiguration quartzMetricsUpdaterConfiguration = new QuartzMetricsUpdaterConfiguration();

    @NotNull
    @JsonProperty("requestLogging")
    private final RequestLoggingConfiguration requestLoggingConfiguration = new RequestLoggingConfiguration();

    @NotNull
    @JsonProperty("hubspot")
    private final HubSpotConfiguration hubSpotConfiguration = new HubSpotConfiguration();

    @NotNull
    @JsonProperty("looker")
    private final LookerConfiguration lookerConfiguration = new LookerConfiguration();

    @JsonProperty("quickbooks")
    private final QuickbooksConfiguration quickbooksConfiguration = new QuickbooksConfiguration();

    @NotNull
    private final EmailNotificationQueueConfiguration emailNotificationQueueConfiguration = new EmailNotificationQueueConfiguration();

    @JsonProperty("customPaymentType")
    private final CustomPaymentTypeConfiguration customPaymentTypeConfiguration = new CustomPaymentTypeConfiguration();

    @JsonProperty("revenueRecognition")
    private final RevenueRecognitionConfiguration revenueRecognitionConfiguration = new RevenueRecognitionConfiguration();

    @JsonProperty("uiConfiguration")
    private final UIConfigurationConfig uiConfiguration = new UIConfigurationConfig();

    @NotNull
    @JsonProperty("quartzReportingJob")
    private final QuartzReportingJobConfiguration quartzReportingJobConfiguration = new QuartzReportingJobConfiguration();

    @NotNull
    private final QuartzCronJobConfiguration quartzApprovalFlowEscalationJobConfiguration = new QuartzCronJobConfiguration();

    @JsonProperty("quartzOrderExpiryJob")
    private final QuartzOrderExpiryConfiguration quartzOrderExpiryConfiguration = new QuartzOrderExpiryConfiguration();

    @JsonProperty("dosFilter")
    private final DosFilterConfiguration dosFilterConfiguration = new DosFilterConfiguration();

    @JsonProperty("databaseQueryTimeout")
    private final QueryTimeoutConfiguration queryTimeoutConfiguration = new QueryTimeoutConfiguration();

    @JsonProperty("openAi")
    private final OpenAIConfiguration openAIConfiguration = new OpenAIConfiguration();

    @JsonProperty("tax")
    private final TaxConfiguration taxConfiguration = new TaxConfiguration();

    @NotNull
    @JsonProperty("quartzBulkRevenueRecognition")
    private final QuartzBulkRevenueRecognitionConfiguration quartzBulkRevenueRecognitionConfiguration =
        new QuartzBulkRevenueRecognitionConfiguration();

    public void setDataSourceFactory(DBCPFactory factory) {
        database = factory;
    }

    @JsonIgnore
    public DBCPFactory getDataSourceFactory() {
        return database;
    }

    @JsonIgnore
    public DBCPFactory getReadOnlyDataSourceFactory() {
        return readOnlyDatabase;
    }

    @JsonProperty("dbcp")
    public void setDBCPConfiguration(DBCPConfiguration dbcpConfiguration) {
        this.dbcpConfiguration = dbcpConfiguration;
    }

    @JsonProperty("dbcpReadOnly")
    public void setDBCPReadOnlyConfiguration(DBCPConfiguration dbcpReadOnlyConfiguration) {
        this.dbcpReadOnlyConfiguration = dbcpReadOnlyConfiguration;
    }

    @JsonProperty("dbcp")
    public DBCPConfiguration getDbcpConfiguration() {
        return dbcpConfiguration;
    }

    @JsonProperty("dbcpReadOnly")
    public DBCPConfiguration getDbcpReadOnlyConfiguration() {
        return dbcpReadOnlyConfiguration;
    }

    @JsonProperty("flyway")
    public FlywayFactory getFlywayFactory() {
        return flywayFactory;
    }

    @JsonProperty("flyway")
    public void setFlywayFactory(FlywayFactory flywayFactory) {
        this.flywayFactory = flywayFactory;
    }

    @JsonProperty("cognito")
    public CognitoConfiguration getCognitoConfiguration() {
        return cognitoConfiguration;
    }

    @JsonProperty("secretsManager")
    public SecretsManagerConfiguration getSecretsManagerConfiguration() {
        return secretsManagerConfiguration;
    }

    @JsonProperty("quartzQueue")
    public QuartzQueueConfiguration getQuartzQueueConfiguration() {
        return quartzQueueConfiguration;
    }

    @JsonProperty("sns")
    public SnsConfiguration getSnsConfiguration() {
        return snsConfiguration;
    }

    @JsonProperty("email")
    public SesConfiguration getSesConfiguration() {
        return sesConfiguration;
    }

    @JsonProperty("lambda")
    public LambdaConfiguration getLambdaConfiguration() {
        return lambdaConfiguration;
    }

    @JsonProperty("notification")
    public NotificationConfiguration getNotificationConfiguration() {
        return notificationConfiguration;
    }

    @JsonProperty("dlq")
    public DLQConfiguration getDlqConfiguration() {
        return dlqConfiguration;
    }

    @JsonProperty("searchUpdaterThread")
    public SearchUpdaterThreadConfiguration getSearchUpdaterThreadConfiguration() {
        return searchUpdaterThreadConfiguration;
    }

    @JsonProperty("quartzInvoiceGenerator")
    public QuartzInvoiceGeneratorConfiguration getQuartzInvoiceGeneratorConfiguration() {
        return quartzInvoiceGeneratorConfiguration;
    }

    @JsonProperty("quartzDunning")
    public QuartzDunningConfiguration getQuartzDunningConfiguration() {
        return quartzDunningConfiguration;
    }

    @JsonProperty("quartz")
    public QuartzConfiguration getQuartzConfiguration() {
        return quartzConfiguration;
    }

    @JsonProperty("document")
    public DocumentConfiguration getDocumentConfiguration() {
        return documentConfiguration;
    }

    @JsonProperty("elasticSearch")
    public ElasticSearchConfiguration getElasticSearchConfiguration() {
        return elasticSearchConfiguration;
    }

    @JsonProperty("salesforce")
    public SalesforceConfiguration getSalesforceConfiguration() {
        return salesforceConfiguration;
    }

    @JsonProperty("idempotency")
    public IdempotencyConfiguration getIdempotencyConfiguration() {
        return idempotencyConfiguration;
    }

    @JsonProperty("dynamodb")
    public DynamoDbConfiguration getDynamoDbConfiguration() {
        return dynamoDbConfiguration;
    }

    @JsonProperty("rateLimiter")
    public RateLimiterConfiguration getRateLimiterConfiguration() {
        return rateLimiterConfiguration;
    }

    @JsonProperty("avalara")
    public AvalaraConfiguration getAvalaraConfiguration() {
        return avalaraConfiguration;
    }

    @JsonProperty("memcached")
    public MemcachedConfiguration getMemcachedConfiguration() {
        return memcachedConfiguration;
    }

    @JsonProperty("docusign")
    public DocuSignConfiguration getDocuSignConfiguration() {
        return docuSignConfiguration;
    }

    @JsonProperty("requestHeaderLogger")
    public RequestHeaderLoggerConfiguration getRequestHeaderLoggerConfiguration() {
        return requestHeaderLoggerConfiguration;
    }

    @JsonProperty("ipBasedRateLimiter")
    public IpBasedRequestRateLimiterConfiguration getIpBasedRequestRateLimiterConfiguration() {
        return ipBasedRequestRateLimiterConfiguration;
    }

    @JsonProperty("siteUrl")
    public String getSiteUrl() {
        return siteUrl;
    }

    @JsonProperty("stripe")
    public StripeConfiguration getStripeConfiguration() {
        return stripeConfiguration;
    }

    @JsonProperty("fixedAmountDiscount")
    public FixedAmountDiscountConfiguration getFixedAmountDiscountConfiguration() {
        return fixedAmountDiscountConfiguration;
    }

    @JsonProperty("electronicSignature")
    public ElectronicSignatureConfiguration getElectronicSignatureConfiguration() {
        return electronicSignatureConfiguration;
    }

    public QuartzUsageAggregationConfiguration getQuartzUsageAggregationConfiguration() {
        return quartzAggregationConfiguration;
    }

    public QuartzRevenueRecognitionJobConfiguration getQuartzRevenueRecognitionJobConfiguration() {
        return quartzRevenueRecognitionJobConfiguration;
    }

    public QuartzBulkInvoiceRunConfiguration getQuartzBulkInvoiceRunConfiguration() {
        return quartzBulkInvoiceRunConfiguration;
    }

    public UsageAggregationConfiguration getUsageAggregationConfiguration() {
        return usageAggregationConfiguration;
    }

    public EventPumpConfiguration getEventPumpConfiguration() {
        return eventPumpConfiguration;
    }

    public QuartzBillyAdminReporterJobConfiguration getQuartzBillyAdminJobConfiguration() {
        return quartzBillyAdminReporterJobConfiguration;
    }

    public QuartzApiKeyGarbageCollectorJobConfiguration getQuartzApiKeyGarbageCollectorJobConfiguration() {
        return quartzApiKeyGarbageCollectorJobConfiguration;
    }

    public EmailLoginLinkConfiguration getEmailLoginLinkConfiguration() {
        return emailLoginLinkConfiguration;
    }

    public QuartzCronJobConfiguration getQuartzPaymentJobConfiguration() {
        return quartzPaymentJobConfiguration;
    }

    public QuartzPaymentReconciliationJobConfiguration getQuartzPaymentReconciliationJobConfiguration() {
        return quartzPaymentReconciliationJobConfiguration;
    }

    @JsonProperty("attachments")
    public AttachmentConfiguration getAttachmentConfiguration() {
        return attachmentConfiguration;
    }

    @JsonProperty("approvalflow")
    public ApprovalFlowConfiguration getApprovalFlowConfiguration() {
        return approvalFlowConfiguration;
    }

    public InvoiceEmailConfiguration getInvoiceEmailConfiguration() {
        return invoiceEmailConfiguration;
    }

    public CreditMemoEmailConfiguration getCreditMemoEmailConfiguration() {
        return creditMemoEmailConfiguration;
    }

    public CustomFieldConfiguration getCustomFieldConfiguration() {
        return customFieldConfiguration;
    }

    public HubSpotEmailConfiguration getHubSpotEmailConfiguration() {
        return hubSpotEmailConfiguration;
    }

    public InternalLoginConfiguration getInternalLoginConfiguration() {
        return internalLoginConfiguration;
    }

    @JsonProperty("appConfig")
    public AppConfigConfiguration getAppConfigConfiguration() {
        return appConfigConfiguration;
    }

    @JsonProperty("apiUrl")
    public String getApiUrl() {
        return apiUrl;
    }

    public boolean isLocalOrCi() {
        return siteUrl != null && siteUrl.startsWith(LOCALHOST_PREFIX);
    }

    public boolean isDev() {
        return envName != null && envName.startsWith(DEV_ENVIRONMENT_NAME_PREFIX);
    }

    public boolean isQA() {
        return envName != null && envName.startsWith(QA_ENVIRONMENT_NAME_PREFIX);
    }

    public boolean isSandbox() {
        return envName != null && envName.startsWith(SANDBOX_ENVIRONMENT_NAME_PREFIX);
    }

    public boolean isProd() {
        return PROD_ENVIRONMENT_NAME.equals(envName);
    }

    @JsonProperty("envName")
    public String getEnvName() {
        return envName;
    }

    @JsonProperty("auditlog")
    public AuditLogConfiguration getAuditLogConfiguration() {
        return auditLogConfiguration;
    }

    @JsonProperty("quartzMetricsUpdater")
    public QuartzMetricsUpdaterConfiguration getQuartzMetricsUpdaterConfiguration() {
        return quartzMetricsUpdaterConfiguration;
    }

    @JsonProperty("dynamicFeatures")
    public Map<String, Boolean> getDynamicFeatures() {
        return dynamicFeatures == null ? Map.of() : dynamicFeatures;
    }

    @JsonProperty("dynamicFeatures")
    public void setDynamicFeatures(Map<String, Boolean> dynamicFeatures) {
        this.dynamicFeatures = ImmutableMap.copyOf(dynamicFeatures);
    }

    @JsonProperty("requestLogging")
    public RequestLoggingConfiguration getRequestLoggingConfiguration() {
        return requestLoggingConfiguration;
    }

    @JsonProperty("hubspot")
    public HubSpotConfiguration getHubSpotConfiguration() {
        return hubSpotConfiguration;
    }

    @JsonProperty("looker")
    public LookerConfiguration getLookerConfiguration() {
        return lookerConfiguration;
    }

    public QuickbooksConfiguration getQuickbooksConfiguration() {
        return quickbooksConfiguration;
    }

    @JsonProperty("emailNotificationQueue")
    public EmailNotificationQueueConfiguration getEmailNotificationQueueConfiguration() {
        return emailNotificationQueueConfiguration;
    }

    public CustomPaymentTypeConfiguration getCustomPaymentTypeConfiguration() {
        return customPaymentTypeConfiguration;
    }

    public UIConfigurationConfig getUiConfiguration() {
        return uiConfiguration;
    }

    public RevenueRecognitionConfiguration getRevenueRecognitionConfiguration() {
        return revenueRecognitionConfiguration;
    }

    @JsonProperty("quartzReportingJob")
    public QuartzReportingJobConfiguration getQuartzReportingJobConfiguration() {
        return quartzReportingJobConfiguration;
    }

    @JsonProperty("quartzApprovalFlowEscalationJob")
    public QuartzCronJobConfiguration getQuartzApprovalFlowEscalationJobConfiguration() {
        return quartzApprovalFlowEscalationJobConfiguration;
    }

    @JsonProperty("quartzOrderExpiryJob")
    public QuartzOrderExpiryConfiguration getQuartzOrderExpiryConfiguration() {
        return quartzOrderExpiryConfiguration;
    }

    @JsonProperty("dosFilter")
    public DosFilterConfiguration getDosFilterConfiguration() {
        return dosFilterConfiguration;
    }

    @JsonProperty("databaseQueryTimeout")
    public QueryTimeoutConfiguration getQueryTimeoutConfiguration() {
        return queryTimeoutConfiguration;
    }

    @JsonProperty("arrMetricsReporting")
    public ArrMetricsReportingConfiguration getArrMetricsReportingConfiguration() {
        return arrMetricsReportingConfiguration;
    }

    @JsonProperty("notificationRetryJob")
    public QuartzNotificationRetryJobConfiguration getQuartzNotificationRetryJobConfiguration() {
        return quartzNotificationRetryJobConfiguration;
    }

    @JsonProperty("tenantNameCache")
    public TenantNameCacheConfiguration getTenantNameCacheConfiguration() {
        return tenantNameCacheConfiguration;
    }

    @JsonProperty("tenantSealProtection")
    public TenantSealProtectionConfiguration getTenantSealProtectionConfiguration() {
        return tenantSealProtectionConfiguration;
    }

    @JsonProperty("crm")
    public CrmConfiguration getCrmConfiguration() {
        return crmConfiguration;
    }

    @JsonProperty("entity")
    public EntityConfiguration getEntityConfiguration() {
        return entityConfiguration;
    }

    @JsonProperty("fx")
    public FxConfiguration getFxConfiguration() {
        return fxConfiguration;
    }

    @JsonProperty("logHeartbeat")
    public LogHeartbeatConfiguration getLogHeartbeatConfiguration() {
        return logHeartbeatConfiguration;
    }

    public QuartzCronJobConfiguration getQuartzEntityInvariantsJobConfiguration() {
        return quartzEntityInvariantsJobConfiguration;
    }

    public QuartzCronJobConfiguration getQuartzAccountingInvariantsJobConfiguration() {
        return quartzAccountingInvariantsJobConfiguration;
    }

    public QuartzCronJobConfiguration getQuartzExchangeRateRefreshJobConfiguration() {
        return quartzExchangeRateRefreshJobConfiguration;
    }

    @JsonProperty("quartzQuickbooksPing")
    public QuartzCronJobConfiguration getQuartzQuickbooksPingJobConfiguration() {
        return quartzQuickbooksPingJobConfiguration;
    }

    @JsonProperty("taskQueue")
    public TaskQueueConfiguration getTaskQueueConfiguration() {
        return taskQueueConfiguration;
    }

    @JsonProperty("kinesis")
    public KinesisConfiguration getKinesisConfiguration() {
        return kinesisConfiguration;
    }

    @JsonProperty("netsuite")
    public NetsuiteConfiguration getNetsuiteConfiguration() {
        return netsuiteConfiguration;
    }

    public OpenAIConfiguration getOpenAIConfiguration() {
        return openAIConfiguration;
    }

    @JsonProperty("tax")
    public TaxConfiguration getTaxConfiguration() {
        return taxConfiguration;
    }

    public QuartzBulkRevenueRecognitionConfiguration getQuartzBulkRevenueRecognitionConfiguration() {
        return quartzBulkRevenueRecognitionConfiguration;
    }

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this);
    }
}
