package com.subskribe.billy.email.services;

import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.MustacheFactory;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.email.model.EmailContacts;
import com.subskribe.billy.email.model.EmailData;
import com.subskribe.billy.email.model.EmailType;
import com.subskribe.billy.email.model.ImmutableEmailAttachmentData;
import com.subskribe.billy.email.model.ImmutableEmailData;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.invoice.EmailNotifierContact;
import com.subskribe.billy.graphql.invoice.EmailNotifiersDetail;
import com.subskribe.billy.graphql.invoice.InvoiceDetail;
import com.subskribe.billy.graphql.invoice.InvoiceEmailResponse;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceService;
import com.subskribe.billy.invoice.document.InvoiceDocumentDataAggregator;
import com.subskribe.billy.invoice.document.InvoiceDocumentJson;
import com.subskribe.billy.invoice.document.InvoiceTemplateData;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.service.InvoiceDocumentService;
import com.subskribe.billy.invoice.service.InvoiceServiceInternal;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.shared.StreamUtils;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.sanitizers.HtmlSanitizer;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.mail.MessagingException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

public class InvoiceEmailService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InvoiceEmailService.class);

    private static final String INVOICE_EMAIL_TEMPLATE = "html/invoice-email.html";
    private static final String FILE_NAME_FORMAT = "%s.pdf";
    private static final String INVOICE_EMAIL_SUBJECT_TEMPLATE = "Invoice %s from %s is attached";
    private static final String CURRENCY_CODE_KEY = "{{currency_code}}";
    private static final String INVOICE_BALANCE_PLACEHOLDER = "{{invoice_balance}}";
    private static final String INVOICE_DUE_DATE_PLACEHOLDER = "{{invoice_due_date}}";
    private static final String INVOICE_SUBSCRIPTION_ID_PLACEHOLDER = "{{subscription_id}}";
    private static final String INVOICE_ACCOUNT_NAME_PLACEHOLDER = "{{account_name}}";
    private static final String INVOICE_NUMBER_PLACEHOLDER = "{{invoice_number}}";
    private static final String BILLING_CONTACT_NAME_PLACEHOLDER = "{{billing_contact_name}}";
    private static final String TENANT_NAME_PLACEHOLDER = "{{tenant_name}}";
    private static final String ACCOUNT_RECEIVABLE_CONTACT_NAME_PLACEHOLDER = "{{account_receivable_contact_name}}";
    private static final String ACCOUNT_RECEIVABLE_CONTACT_EMAIL_PLACEHOLDER = "{{account_receivable_contact_email}}";
    private static final String FROM_EMAIL_ADDRESS_PLACEHOLDER = "{{from_email_address}}";
    private static final String FROM_EMAIL_ADDRESS_FORMAT = "\"{{account_receivable_contact_name}}\" <{{from_email_address}}>";

    private final BillyConfiguration billyConfiguration;
    private final EmailService emailService;
    private final BulkInvoiceService bulkInvoiceService;
    private final InvoiceServiceInternal invoiceServiceInternal;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final EmailValidationService emailValidationService;
    private final InvoiceDocumentDataAggregator invoiceDocumentDataAggregator;
    private final InvoiceDocumentService invoiceDocumentService;
    private final MustacheFactory factory;
    private final FeatureService featureService;

    @Inject
    public InvoiceEmailService(
        BillyConfiguration billyConfiguration,
        EmailService emailService,
        BulkInvoiceService bulkInvoiceService,
        InvoiceServiceInternal invoiceServiceInternal,
        DocumentTemplateGetService documentTemplateGetService,
        EmailValidationService emailValidationService,
        InvoiceDocumentDataAggregator invoiceDocumentDataAggregator,
        InvoiceDocumentService invoiceDocumentService,
        FeatureService featureService
    ) {
        this.billyConfiguration = billyConfiguration;
        this.emailService = emailService;
        this.bulkInvoiceService = bulkInvoiceService;
        this.invoiceServiceInternal = invoiceServiceInternal;
        this.documentTemplateGetService = documentTemplateGetService;
        this.emailValidationService = emailValidationService;
        this.invoiceDocumentDataAggregator = invoiceDocumentDataAggregator;
        this.invoiceDocumentService = invoiceDocumentService;
        this.featureService = featureService;
        this.factory = new DefaultMustacheFactory();
    }

    public InvoiceEmailResponse sendInvoiceEmail(Invoice.Number invoiceNumber) {
        InvoiceDocumentJson invoiceDocumentJson = invoiceDocumentDataAggregator.generateDocumentDataForInvoiceEmail(invoiceNumber);

        if (!invoiceDocumentJson.getInvoiceDetail().getStatus().hasFinalizedTotal()) {
            throw new IllegalStateException("Invoice is not finalized.");
        }

        String subscriptionId = invoiceDocumentJson.getInvoiceDetail().getSubscriptionId();
        InvoiceTemplateData invoiceTemplateData = new InvoiceTemplateData(invoiceDocumentJson);
        Optional<InputStream> invoiceDocument = invoiceDocumentService.getInvoiceDocumentCreateIfNeeded(invoiceNumber);
        if (invoiceDocument.isEmpty()) {
            throw new ServiceFailureException("Failed to get the document for invoice with number: " + invoiceNumber.getNumber());
        }

        try (BufferedInputStream bin = new BufferedInputStream(invoiceDocument.get())) {
            return sendInvoiceEmail(subscriptionId, invoiceTemplateData, invoiceDocumentJson.getInvoiceDetail(), bin);
        } catch (UnsupportedOperationException e) {
            LOGGER.info("Unable to email invoice for invoice number: {}", invoiceNumber, e);
            throw e;
        } catch (IOException e) {
            String message = "failed to create and send invoice email";
            LOGGER.error(message);
            throw new ServiceFailureException(message, e);
        }
    }

    private InvoiceEmailResponse sendInvoiceEmail(
        String subscriptionId,
        InvoiceTemplateData invoiceTemplateData,
        InvoiceDetail invoiceDetail,
        InputStream invoiceDocument
    ) {
        if (!isSendingInvoiceEmailsEnabled(billyConfiguration, invoiceTemplateData.getTenantId())) {
            throw new UnsupportedOperationException("Sending invoice emails is not enabled.");
        }

        if (invoiceDetail.getPostedDate() == null) {
            throw new UnsupportedOperationException("Cannot send invoice email for an invoice that has not been posted.");
        }

        Map<String, String> replacementValuesMap = getReplacementValuesMap(
            subscriptionId,
            invoiceTemplateData.getAccount().getName(),
            invoiceTemplateData.getInvoiceNumber(),
            invoiceTemplateData.getCurrency(),
            invoiceTemplateData.getAmountDue(),
            invoiceTemplateData.getDueDate(),
            invoiceTemplateData.getBillingContact().getFullName(),
            invoiceTemplateData.getTenantName(),
            invoiceTemplateData.getAccountReceivableEmail(),
            invoiceTemplateData.getAccountReceivableName()
        );
        EmailContacts emailContacts = getEmailContacts(invoiceDetail);

        try {
            Optional<DocumentMasterTemplate> invoiceEmailTemplate = documentTemplateGetService.getDefaultMasterTemplate(
                DocumentTemplateType.INVOICE_EMAIL
            );

            String emailTemplate = invoiceEmailTemplate
                .map(DocumentMasterTemplate::getContent)
                .orElse(EmailService.getTemplateFromResourceFile(INVOICE_EMAIL_TEMPLATE));

            if (featureService.isEnabled(Feature.MUSTACHE_INVOICE_EMAIL_TEMPLATE)) {
                var stringWriter = new StringWriter();
                Reader reader = new StringReader(emailTemplate);
                factory.compile(reader, "invoice email").execute(stringWriter, invoiceTemplateData);
                String emailHtml = stringWriter.toString();
                emailTemplate = HtmlSanitizer.sanitize(emailHtml);
            }

            EmailData emailData = ImmutableEmailData.builder()
                .emailTemplate(emailTemplate)
                .emailContacts(emailContacts)
                .fromEmail(getFromEmailAddress(invoiceTemplateData.getAccountReceivableName()))
                .subject(String.format(INVOICE_EMAIL_SUBJECT_TEMPLATE, invoiceTemplateData.getInvoiceNumber(), invoiceTemplateData.getTenantName()))
                .emailType(EmailType.INVOICE_POSTED)
                .relatedObjectId(invoiceTemplateData.getInvoiceNumber())
                .relatedObjectParentId(Optional.of(subscriptionId))
                .replyToEmail(invoiceTemplateData.getAccountReceivableEmail())
                .putAllReplacementValuesMap(replacementValuesMap)
                .attachmentData(
                    ImmutableEmailAttachmentData.builder()
                        .attachmentDocument(invoiceDocument)
                        .attachmentDocumentName(String.format(FILE_NAME_FORMAT, invoiceTemplateData.getInvoiceNumber()))
                        .build()
                )
                .entityId(invoiceDetail.getEntityId())
                .build();

            String emailId = emailService.sendEmail(emailData);
            invoiceServiceInternal.updateEmailLastSentOn(invoiceTemplateData.getInvoiceNumber(), Instant.now());
            bulkInvoiceService.setSesMessageIdForRunItemByInvoiceNumber(new Invoice.Number(invoiceTemplateData.getInvoiceNumber()), emailId);

            InvoiceEmailResponse invoiceEmailResponse = new InvoiceEmailResponse();
            invoiceEmailResponse.setEmailId(emailId);
            return invoiceEmailResponse;
        } catch (MessagingException | IOException e) {
            LOGGER.error("Sending invoice email threw an exception: ", e);
            throw new ServiceFailureException(e.getMessage(), e);
        }
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private static boolean isSendingInvoiceEmailsEnabled(BillyConfiguration billyConfiguration, Optional<String> tenantId) {
        if (tenantId.isEmpty()) {
            return BooleanUtils.isTrue(billyConfiguration.getInvoiceEmailConfiguration().isEnabled());
        }

        BillyConfiguration tenantScopedConfig = TenantScopedConfigProvider.provideCombined(billyConfiguration, tenantId.get());
        return BooleanUtils.isTrue(tenantScopedConfig.getInvoiceEmailConfiguration().isEnabled());
    }

    private EmailContacts getEmailContacts(InvoiceDetail invoiceDetail) {
        EmailContacts emailContacts = new EmailContacts();

        List<AccountContactJson> contacts = new ArrayList<>();
        contacts.add(invoiceDetail.getBillingContact());
        contacts = sanitizeAccountContactsList(contacts);
        if (CollectionUtils.isNotEmpty(contacts)) {
            emailContacts.getToContacts().addAll(contacts.stream().map(EmailContact::new).toList());
        }

        populateEmailContactsFromEmailNotifiersList(invoiceDetail, emailContacts);

        if (
            CollectionUtils.isEmpty(emailContacts.getToContacts()) &&
            CollectionUtils.isEmpty(emailContacts.getCcContacts()) &&
            CollectionUtils.isEmpty(emailContacts.getBccContacts())
        ) {
            throw new UnsupportedOperationException("Cannot send external emails.");
        }

        return emailContacts;
    }

    private List<AccountContactJson> sanitizeAccountContactsList(List<AccountContactJson> contacts) {
        if (BooleanUtils.isFalse(billyConfiguration.getInvoiceEmailConfiguration().getInternalOnly())) {
            return contacts;
        }

        return contacts
            .stream()
            .filter(StreamUtils.distinctByKey(AccountContactJson::getEmail))
            .filter(contact -> emailValidationService.isInternalEmailAddress(contact.getEmail()))
            .collect(Collectors.toList());
    }

    private void populateEmailContactsFromEmailNotifiersList(InvoiceDetail invoiceDetail, EmailContacts emailContacts) {
        EmailNotifiersDetail emailNotifiersList = invoiceDetail.getEmailNotifiersList();
        if (emailNotifiersList == null) {
            return;
        }

        List<EmailNotifierContact> toContacts = sanitizeEmailNotifierList(emailNotifiersList.getToContacts());
        if (CollectionUtils.isNotEmpty(toContacts)) {
            emailContacts.getToContacts().addAll(toContacts.stream().map(EmailContact::new).toList());
        }

        List<EmailNotifierContact> ccContacts = sanitizeEmailNotifierList(emailNotifiersList.getCcContacts());
        if (CollectionUtils.isNotEmpty(ccContacts)) {
            emailContacts.getCcContacts().addAll(ccContacts.stream().map(EmailContact::new).toList());
        }

        List<EmailNotifierContact> bccContacts = sanitizeEmailNotifierList(emailNotifiersList.getBccContacts());
        if (CollectionUtils.isNotEmpty(bccContacts)) {
            emailContacts.getBccContacts().addAll(bccContacts.stream().map(EmailContact::new).toList());
        }
    }

    private List<EmailNotifierContact> sanitizeEmailNotifierList(List<EmailNotifierContact> emailNotifierContacts) {
        if (BooleanUtils.isFalse(billyConfiguration.getInvoiceEmailConfiguration().getInternalOnly())) {
            return emailNotifierContacts;
        }

        return emailNotifierContacts
            .stream()
            .filter(contact -> emailValidationService.isInternalEmailAddress(contact.getEmail()))
            .collect(Collectors.toList());
    }

    private Map<String, String> getReplacementValuesMap(
        String subscriptionId,
        String accountName,
        String invoiceNumber,
        String currencyCode,
        String invoiceBalance,
        String invoiceDueDate,
        String billingContactName,
        String tenantName,
        String accountReceivableEmail,
        String accountReceivableName
    ) {
        Map<String, String> replacementValuesMap = new HashMap<>();

        replacementValuesMap.put(CURRENCY_CODE_KEY, currencyCode);
        replacementValuesMap.put(INVOICE_BALANCE_PLACEHOLDER, invoiceBalance);
        replacementValuesMap.put(INVOICE_DUE_DATE_PLACEHOLDER, invoiceDueDate);
        replacementValuesMap.put(INVOICE_SUBSCRIPTION_ID_PLACEHOLDER, subscriptionId);
        replacementValuesMap.put(INVOICE_ACCOUNT_NAME_PLACEHOLDER, accountName);
        replacementValuesMap.put(INVOICE_NUMBER_PLACEHOLDER, invoiceNumber);
        replacementValuesMap.put(BILLING_CONTACT_NAME_PLACEHOLDER, billingContactName);
        replacementValuesMap.put(TENANT_NAME_PLACEHOLDER, tenantName);
        replacementValuesMap.put(ACCOUNT_RECEIVABLE_CONTACT_EMAIL_PLACEHOLDER, accountReceivableEmail);
        replacementValuesMap.put(ACCOUNT_RECEIVABLE_CONTACT_NAME_PLACEHOLDER, accountReceivableName);

        return replacementValuesMap;
    }

    private String getFromEmailAddress(String accountReceivableContactName) {
        String emailAddress = billyConfiguration.getInvoiceEmailConfiguration().getFromEmailAddress();
        return FROM_EMAIL_ADDRESS_FORMAT.replace(ACCOUNT_RECEIVABLE_CONTACT_NAME_PLACEHOLDER, accountReceivableContactName).replace(
            FROM_EMAIL_ADDRESS_PLACEHOLDER,
            emailAddress
        );
    }
}
