package com.subskribe.billy.metrics;

import com.subskribe.billy.metrics.model.StoredMetrics;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.mapper.BaseMapper;
import com.subskribe.billy.subscription.model.SubscriptionMetrics;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionService;
import java.time.Instant;
import java.util.UUID;
import javax.inject.Inject;
import org.jooq.DSLContext;
import org.jooq.JSONB;

public class MetricsUpdater {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetricsUpdater.class);

    private final MetricsService metricsService;
    private final OrderService orderService;
    private final SubscriptionService subscriptionService;
    private final SubscriptionGetService subscriptionGetService;

    @Inject
    public MetricsUpdater(
        MetricsService metricsService,
        OrderService orderService,
        SubscriptionService subscriptionService,
        SubscriptionGetService subscriptionGetService
    ) {
        this.metricsService = metricsService;
        this.orderService = orderService;
        this.subscriptionService = subscriptionService;
        this.subscriptionGetService = subscriptionGetService;
    }

    public void updateExecutedOrder(DSLContext txnDslContext, String tenantId, String orderId) {
        try {
            StoredMetrics sm = new StoredMetrics(metricsService.getOrderMetrics(orderId, Instant.now()));
            orderService.updateOrderMetrics(txnDslContext, tenantId, orderId, JSONB.jsonb(BaseMapper.JSON_MAPPER.writeValueAsString(sm)));
        } catch (Exception e) {
            LOGGER.error("Order metrics update failed for tenant: {}, order_id {}", tenantId, orderId, e);
        }
    }

    public void setSubscriptionMetricsForRecompute(DSLContext txnDslContext, String tenantId, UUID internalId, String subscriptionId) {
        try {
            StoredMetrics sm = new StoredMetrics(true);
            SubscriptionMetrics metrics = subscriptionGetService.getSubscriptionMetrics(tenantId, internalId);
            subscriptionService.updateSubscriptionMetrics(
                txnDslContext,
                tenantId,
                internalId,
                subscriptionId,
                metrics == null ? null : metrics.getId(),
                JSONB.jsonb(BaseMapper.JSON_MAPPER.writeValueAsString(sm))
            );
        } catch (Exception e) {
            LOGGER.error("Subscription metrics update for recompute failed for tenant: {}, subscription_id {}", tenantId, subscriptionId, e);
        }
    }
}
