package com.subskribe.billy.metrics;

import static com.subskribe.billy.invoice.service.InvoiceServiceInternal.INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION;
import static com.subskribe.billy.shared.enums.CompositeOrderType.UPSELL_AND_EARLY_RENEWAL;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.compositeorder.model.CompositeOrder;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.invoice.ProrationCalculator;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.model.ListAmount;
import com.subskribe.billy.invoice.model.ListUnitPrice;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.InvoiceAmountCalculator;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.metrics.model.ArrOverrideOptions;
import com.subskribe.billy.metrics.model.LineItemMetrics;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.EvergreenUtils;
import com.subskribe.billy.order.OrderLineUtils;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderServiceHelper;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.resources.shared.PaginationResponseJson;
import com.subskribe.billy.shared.TimeSeriesElement;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.subscription.model.SubscriptionMetrics;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionOrderService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.mapstruct.factory.Mappers;

public class MetricsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetricsService.class);

    private final AccountGetService accountGetService;
    private final OrderGetService orderGetService;
    private final ProductCatalogGetService productCatalogGetService;
    private final SubscriptionGetService subscriptionGetService;
    private final TenantSettingService tenantSettingService;
    private final ProrationConfigurationGetService prorationConfigurationGetService;
    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;
    private final FeatureService featureService;
    private final InvoiceAmountCalculator invoiceAmountCalculator;
    private final CompositeOrderGetService compositeOrderGetService;
    private final InvoiceService invoiceService;
    private final OrderMapper orderMapper;
    private final SubscriptionOrderService subscriptionOrderService;

    private static final int MAX_GET_ORDERS_PAGINATION_LIMIT = 50;

    @Inject
    public MetricsService(
        AccountGetService accountGetService,
        OrderGetService orderGetService,
        ProductCatalogGetService productCatalogGetService,
        SubscriptionGetService subscriptionGetService,
        TenantSettingService tenantSettingService,
        ProrationConfigurationGetService prorationConfigurationGetService,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        FeatureService featureService,
        InvoiceAmountCalculator invoiceAmountCalculator,
        CompositeOrderGetService compositeOrderGetService,
        InvoiceService invoiceService,
        SubscriptionOrderService subscriptionOrderService
    ) {
        this.accountGetService = accountGetService;
        this.orderGetService = orderGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.subscriptionGetService = subscriptionGetService;
        this.tenantSettingService = tenantSettingService;
        this.prorationConfigurationGetService = prorationConfigurationGetService;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.featureService = featureService;
        this.invoiceAmountCalculator = invoiceAmountCalculator;
        this.compositeOrderGetService = compositeOrderGetService;
        this.invoiceService = invoiceService;
        this.subscriptionOrderService = subscriptionOrderService;
        orderMapper = Mappers.getMapper(OrderMapper.class);
    }

    // Provides the annual amount for the subscription taking into account billing cycle start date (potential first stub period)
    // by previewing invoice amounts with annual billing cycle
    public Map<Period, BigDecimal> getOrderAnnualTotal(String orderId) {
        Order order = validateAndGetOrder(orderId);

        order.setBillingCycle(new Recurrence(Cycle.YEAR, 1));

        // get annual billing periods to partition by years
        List<BillingPeriod> billingPeriods = invoiceService.getBillingPeriods(order);
        InvoicePreview preview = invoiceService.previewInvoiceByOrderPeriod(order, false, !INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION);
        List<InvoiceItem> invoiceItems = preview.getInvoiceItems();

        Map<Period, BigDecimal> annualAmounts = new LinkedHashMap<>(); // preserve order

        billingPeriods.forEach(billingPeriod -> annualAmounts.put(Period.between(billingPeriod.getStart(), billingPeriod.getEnd()), BigDecimal.ZERO));

        // invoice items may have separate billing cycles despite annual order billing cycle. e.g. charge level billing cycle, usage charges, OTC
        invoiceItems.forEach(item -> {
            // find the billing cycle the item fits in and add to the total
            Optional<Period> matchingPeriod = findPeriodForInvoiceItem(billingPeriods, item);
            matchingPeriod.ifPresent(period -> annualAmounts.put(period, annualAmounts.get(period).add(item.getAmount())));
        });

        return annualAmounts;
    }

    private Optional<Period> findPeriodForInvoiceItem(List<BillingPeriod> billingPeriods, InvoiceItem item) {
        return billingPeriods
            .stream()
            .filter(
                billingPeriod ->
                    item.getPeriodStartDate().compareTo(billingPeriod.getStart()) >= 0 &&
                    item.getPeriodStartDate().compareTo(billingPeriod.getEnd()) < 0
            )
            .findFirst()
            .map(billingPeriod -> Period.between(billingPeriod.getStart(), billingPeriod.getEnd()));
    }

    public Metrics getAccountMetrics(String accountId, Instant target, boolean forceRecalculate) {
        Account account = validateAndGetAccount(accountId);
        if (!forceRecalculate && (account.getMetrics() != null) && !account.getMetrics().isRecompute() && !account.getIsReseller()) {
            return account.getMetrics().getMetrics().toMetrics();
        }

        List<Order> orders = getAccountExecutedOrders(accountId);
        Map<String, MetricsCalculationContext> orderMetricsCalculationContextMap = getMetricsCalculationContextMap(orders);
        List<OrderLineItem> orderLineItems = getExecutedOrderLineItems(orders);
        var chargeMap = getChargeMap(orderLineItems);
        List<OrderLineItem> recurringOrderLineItems = OrderServiceHelper.getRecurringOrderLineItems(orderLineItems, chargeMap);
        List<OrderLineItem> nonRecurringOrderLineItems = OrderServiceHelper.getNonRecurringOrderLineItems(orderLineItems, chargeMap);

        BigDecimal accountTCV = getOrderLineTotalAmount(orderLineItems);
        BigDecimal recurringTotal = getOrderLineTotalAmount(recurringOrderLineItems);
        BigDecimal nonRecurringTotal = getOrderLineTotalAmount(nonRecurringOrderLineItems);

        BigDecimal accountARR = getOrderLineTotalARR(target, orders, orderLineItems);
        List<TimeSeriesElement<BigDecimal>> arrTrend = getOrderLineItemsARRTrend(
            orderMetricsCalculationContextMap,
            orders,
            orderLineItems,
            chargeMap,
            Optional.empty()
        );

        checkTotalAmounts(nonRecurringTotal, recurringTotal, accountTCV, "AccountID: " + accountId);

        return new Metrics(accountTCV, recurringTotal, nonRecurringTotal, accountARR, null, null, null, null, null, arrTrend);
    }

    public Metrics getSubscriptionMetrics(String subscriptionId, Instant target, boolean forceRecalculate) {
        if (!forceRecalculate) {
            // if request isn't forcing recompute, and we have stored metrics, and it's not marked as needing recompute, return stored metrics
            Optional<Metrics> storedMetrics = subscriptionGetService.getStoredMetrics(subscriptionId);
            if (storedMetrics.isPresent()) {
                Metrics metrics = storedMetrics.get();
                List<TimeSeriesElement<BigDecimal>> arrTrend = metrics.getArrTrend();

                // stored metrics doesn't have the current ARR as of the target instant, so we need to find it from the ARR trend
                BigDecimal arrAtTarget = CollectionUtils.isEmpty(arrTrend) ? BigDecimal.ZERO : findArrAtTargetInstant(arrTrend, target);
                return new Metrics(
                    metrics.getTcv(),
                    metrics.getRecurringTotal(),
                    metrics.getNonRecurringTotal(),
                    arrAtTarget,
                    metrics.getEntryArr(),
                    metrics.getExitArr(),
                    metrics.getAverageArr(),
                    metrics.getDeltaTcv(),
                    metrics.getDeltaArr(),
                    arrTrend
                );
            }
        }

        return getSubscriptionMetrics(subscriptionId, target);
    }

    // finds the ARR as of the target instant based on ARR trend time series data
    // the last entry of ARR trend is 0, representing the end of the time series values
    // if the target date is after the last entry, the last entry's value (0) is returned
    // if the target date is before the first entry, 0 is returned
    private BigDecimal findArrAtTargetInstant(List<TimeSeriesElement<BigDecimal>> arrTrend, Instant target) {
        for (int i = arrTrend.size() - 1; i >= 0; i--) {
            TimeSeriesElement<BigDecimal> entry = arrTrend.get(i);
            if (!entry.getInstant().isAfter(target)) {
                return entry.getData();
            }
        }

        return BigDecimal.ZERO;
    }

    public Metrics getSubscriptionMetrics(String subscriptionId, Instant target) {
        Subscription subscription = validateAndGetSubscription(subscriptionId);
        return getSubscriptionMetrics(subscription, target);
    }

    public Metrics getSubscriptionMetrics(Subscription subscription, Instant target) {
        return calculateSubscriptionMetrics(subscription, target, true);
    }

    private Metrics calculateSubscriptionMetrics(Subscription subscription, Instant target, boolean calculateDeltaFromPreviousSubscription) {
        SubscriptionMetrics subscriptionMetrics = subscriptionGetService.getSubscriptionMetrics(subscription.getTenantId(), subscription.getId());
        if (
            (subscriptionMetrics != null) &&
            (subscriptionMetrics.getMetrics() != null) &&
            !subscriptionMetrics.getMetrics().isRecompute() &&
            subscriptionMetrics.getMetrics().getMetrics() != null
        ) {
            // if subscription metrics has already been computed and subscription is unchanged, return stored value
            return subscriptionMetrics.getMetrics().getMetrics().toMetrics();
        }
        return recalculateSubscriptionMetrics(subscription, target, calculateDeltaFromPreviousSubscription);
    }

    // calculateDeltaFromPreviousSubscription indicates if delta metrics based on renewed from subscription should be calculated.
    // This is to prevent going all the way back to the start of a subscription chain
    public Metrics recalculateSubscriptionMetrics(Subscription subscription, Instant target, boolean calculateDeltaFromPreviousSubscription) {
        // for subscription metrics only consider executed order lines
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscription.getSubscriptionId());
        List<OrderLineItem> executedOrderLineItems = getExecutedOrderLineItems(orders);
        Map<String, Charge> chargeMap = getChargeMap(executedOrderLineItems);
        List<OrderLineItem> recurringOrderLineItems = OrderServiceHelper.getRecurringOrderLineItems(executedOrderLineItems, chargeMap);
        List<OrderLineItem> nonRecurringOrderLineItems = OrderServiceHelper.getNonRecurringOrderLineItems(executedOrderLineItems, chargeMap);
        List<OrderLineItem> arrTrackingOrderLineItems = getArrTrackingOrderLineItems(executedOrderLineItems, chargeMap);

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Map<String, Order> orderMap = getOrderMapFromOrderLineItems(orders, arrTrackingOrderLineItems);
        Map<String, MetricsCalculationContext> metricsCalculationContextMap = getMetricsCalculationContextMap(orders);

        BigDecimal subscriptionTCV = getOrderLineTotalAmount(executedOrderLineItems);
        BigDecimal subscriptionRecurringTotal = getOrderLineTotalAmount(recurringOrderLineItems);
        BigDecimal subscriptionNonRecurringTotal = getOrderLineTotalAmount(nonRecurringOrderLineItems);

        BigDecimal subscriptionARR = getOrderLineTotalARR(metricsCalculationContextMap, target, arrTrackingOrderLineItems, chargeMap, orderMap);
        BigDecimal subscriptionEntryArr = getOrderLineTotalARR(
            metricsCalculationContextMap,
            subscription.getStartDate(),
            arrTrackingOrderLineItems,
            chargeMap,
            orderMap
        );
        BigDecimal subscriptionExitARR = getOrderLineTotalARR(
            metricsCalculationContextMap,
            subscription.getEndDate().minusSeconds(1),
            arrTrackingOrderLineItems,
            chargeMap,
            orderMap
        );

        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(subscription);
        Map<String, BigDecimal> lineItemArrAmounts = getOrderLineToArrMap(
            metricsCalculationContextMap,
            chargeMap,
            arrTrackingOrderLineItems,
            orderMap
        );
        BigDecimal averageArr = getAverageArr(
            subscription.getStartDate(),
            Period.between(subscription.getStartDate(), subscription.getEndDate()),
            subscription.getBillingAnchorDate(),
            timeZone,
            prorationConfig,
            arrTrackingOrderLineItems,
            lineItemArrAmounts
        );

        List<TimeSeriesElement<BigDecimal>> arrTrend = getOrderLineItemsARRTrend(
            metricsCalculationContextMap,
            orders,
            arrTrackingOrderLineItems,
            chargeMap,
            Optional.of(lineItemArrAmounts)
        );

        BigDecimal deltaTcv;
        BigDecimal deltaArr;
        if (calculateDeltaFromPreviousSubscription && StringUtils.isNotBlank(subscription.getRenewedFromSubscriptionId())) {
            // add delta tcv and delta arr for subscriptions renewed from previous subscriptions
            Subscription renewedFromSubscription = validateAndGetSubscription(subscription.getRenewedFromSubscriptionId());
            Metrics previousSubscriptionMetrics = calculateSubscriptionMetrics(renewedFromSubscription, subscription.getRenewedFromDate(), false); // go back only 1 subscription renewal
            deltaTcv = subscriptionTCV.subtract(previousSubscriptionMetrics.getTcv());
            deltaArr = subscriptionEntryArr.subtract(previousSubscriptionMetrics.getExitArr());
        } else {
            deltaTcv = subscriptionTCV;
            deltaArr = subscriptionEntryArr;
        }

        checkTotalAmounts(
            subscriptionNonRecurringTotal,
            subscriptionRecurringTotal,
            subscriptionTCV,
            "SubscriptionID: " + subscription.getSubscriptionId()
        );

        return new Metrics(
            subscriptionTCV,
            subscriptionRecurringTotal,
            subscriptionNonRecurringTotal,
            subscriptionARR,
            subscriptionEntryArr,
            subscriptionExitARR,
            averageArr,
            deltaTcv,
            deltaArr,
            arrTrend
        );
    }

    public Metrics getOrderMetrics(String orderId, Instant target) {
        Order order = validateAndGetOrder(orderId);
        return getOrderMetrics(order, target);
    }

    public Metrics getStoredMetrics(String orderId) {
        Order order = validateAndGetOrder(orderId);
        if (order.getMetrics() == null) {
            throw new ObjectNotFoundException(BillyObjectType.ORDER_METRICS, orderId);
        }
        return order.getMetrics().getMetrics().toMetrics();
    }

    public Metrics getOrderMetrics(Order originalOrder, Instant target) {
        Order order = orderMapper.duplicateOrder(originalOrder);
        if (order.getStatus() == OrderStatus.EXECUTED && (order.getMetrics() != null) && !order.getMetrics().isRecompute()) {
            // if order metrics has already been computed and order is unchanged, return stored value
            return order.getMetrics().getMetrics().toMetrics();
        }

        MetricsCalculationContext metricsCalculationContext = getMetricsCalculationContext(order);

        Map<String, MetricsCalculationContext> metricsCalculationContextMap = getMetricsCalculationContextMap(
            order.getOrderId(),
            metricsCalculationContext
        );

        Map<String, Charge> chargeMap = getChargeMap(order.getLineItemsNetEffect());
        List<OrderLineItem> recurringOrderLineItems = OrderServiceHelper.getRecurringOrderLineItems(order.getLineItemsNetEffect(), chargeMap);
        List<OrderLineItem> nonRecurringOrderLineItems = OrderServiceHelper.getNonRecurringOrderLineItems(order.getLineItemsNetEffect(), chargeMap);
        List<OrderLineItem> arrTrackingOrderLineItems = getArrTrackingOrderLineItems(order.getLineItemsNetEffect(), chargeMap);

        List<OrderLineItem> overLappingItems = arrTrackingOrderLineItems
            .stream()
            .filter(orderLineItem -> orderAndLineHasOverLap(order, orderLineItem))
            .collect(Collectors.toList());

        Map<String, Order> orderMap = getOrderMapFromOrderLineItems(List.of(order), overLappingItems);

        BigDecimal orderTCV = getOrderLineTotalAmount(order.getLineItemsNetEffect());
        BigDecimal recurringTotal = getOrderLineTotalAmount(recurringOrderLineItems);
        BigDecimal nonRecurringTotal = getOrderLineTotalAmount(nonRecurringOrderLineItems);

        BigDecimal orderARR;
        BigDecimal orderEntryARR;
        BigDecimal orderExitARR;

        if (
            featureService.isEnabled(Feature.EXPIRE_CANCEL_DELTA_ARR) &&
            OrderType.CANCEL == order.getOrderType() &&
            order.getStartDate().equals(order.getEndDate())
        ) {
            // todo: temporary condition to calculate order ARR values for explicit cancellation of an expired subscription.
            // if the subscription is cancelled at the end date, ARR values should be the inverse of subscription exit ARR instead of 0
            Metrics subscriptionMetrics = getSubscriptionMetrics(order.getExternalSubscriptionId(), target);
            // show arr (now) if target is on the same instance of end date, otherwise arr (now) is 0
            orderARR = order.getEndDate().compareTo(target) == 0 ? subscriptionMetrics.getExitArr().negate() : BigDecimal.ZERO;
            orderEntryARR = subscriptionMetrics.getExitArr().negate();
            orderExitARR = orderEntryARR;
        } else {
            orderARR = getOrderLineTotalARR(metricsCalculationContextMap, target, overLappingItems, chargeMap, orderMap);
            orderEntryARR = getOrderLineTotalARR(metricsCalculationContextMap, order.getStartDate(), overLappingItems, chargeMap, orderMap);
            orderExitARR = getOrderLineTotalARR(
                metricsCalculationContextMap,
                order.getEndDate().minusSeconds(1),
                overLappingItems,
                chargeMap,
                orderMap
            );
        }

        Map<String, BigDecimal> orderLineArrAmounts = getOrderLineToArrMap(metricsCalculationContextMap, chargeMap, overLappingItems, orderMap);
        BigDecimal averageArr;
        if (order.getStartDate().getEpochSecond() == order.getEndDate().getEpochSecond()) {
            // if the order is 0 duration, then average is the same as entry
            averageArr = orderEntryARR;
        } else {
            TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
            averageArr = getAverageArr(
                metricsCalculationContext.subscriptionStart(),
                Period.between(order.getStartDate(), order.getEndDate()),
                metricsCalculationContext.billingAnchorDate(),
                timeZone,
                metricsCalculationContext.prorationConfig(),
                overLappingItems,
                orderLineArrAmounts
            );
        }

        List<TimeSeriesElement<BigDecimal>> arrTrend = getOrderLineItemsARRTrend(
            getMetricsCalculationContextMap(order.getOrderId(), metricsCalculationContext),
            List.of(order),
            overLappingItems,
            chargeMap,
            Optional.of(orderLineArrAmounts)
        );

        BigDecimal deltaTcv;
        BigDecimal deltaArr;
        if (order.getOrderType() == OrderType.RENEWAL) {
            // if order is a renewal order, calculate delta metrics based on renewed from subscription
            Subscription renewedFromSubscription = validateAndGetSubscriptionAtVersion(
                order.getRenewalForSubscriptionId(),
                order.getRenewalForSubscriptionVersion()
            );
            Metrics previousSubscriptionMetrics = calculateSubscriptionMetrics(renewedFromSubscription, order.getStartDate(), false);
            deltaTcv = orderTCV.subtract(previousSubscriptionMetrics.getTcv());
            deltaArr = orderEntryARR.subtract(previousSubscriptionMetrics.getExitArr());
            deltaArr = getRenewalOrderDeltaArrForUpsellAndEarlyRenewal(order, deltaArr);
        } else {
            deltaTcv = orderTCV;
            deltaArr = orderEntryARR;
        }

        checkTotalAmounts(nonRecurringTotal, recurringTotal, orderTCV, "OrderID: " + order.getOrderId());

        if (order.getSubscriptionDurationModel() == SubscriptionDurationModel.EVERGREEN) {
            orderTCV = null;
            recurringTotal = null;
            averageArr = null;
            deltaTcv = null;
        }

        return new Metrics(
            orderTCV,
            recurringTotal,
            nonRecurringTotal,
            orderARR,
            orderEntryARR,
            orderExitARR,
            averageArr,
            deltaTcv,
            deltaArr,
            arrTrend
        );
    }

    private BigDecimal getRenewalOrderDeltaArrForUpsellAndEarlyRenewal(Order order, BigDecimal deltaArr) {
        if (StringUtils.isBlank(order.getCompositeOrderId())) {
            return deltaArr;
        }
        //Get composite order for renewal order
        CompositeOrder compositeOrder = compositeOrderGetService.getCompositeOrder(order.getCompositeOrderId());
        if (compositeOrder == null || UPSELL_AND_EARLY_RENEWAL != compositeOrder.getType()) {
            return deltaArr;
        }
        //get amendment order from composite order
        Order amendmentOrder = compositeOrderGetService
            .getOrdersInCompositeOrder(compositeOrder.getCompositeOrderId())
            .stream()
            .filter(orderInComposite -> orderInComposite.getOrderType() == OrderType.AMENDMENT)
            .findFirst()
            .orElseThrow();
        Metrics previousAmendmentOrderMetrics = getOrderMetrics(amendmentOrder.getOrderId(), Instant.now());
        deltaArr = deltaArr.subtract(previousAmendmentOrderMetrics.getExitArr());
        return deltaArr;
    }

    @Deprecated
    public Metrics getOrderLineMetrics(String orderLineItemId) {
        OrderLineItem lineItem = validateAndGetOrderLine(orderLineItemId);
        Order order = validateAndGetOrder(lineItem.getOrderId());
        Charge charge = productCatalogGetService.getChargeByChargeId(lineItem.getChargeId());
        MetricsCalculationContext metricsCalculationContext = getMetricsCalculationContext(order);
        LineItemMetrics lineItemMetrics = getOrderLineMetrics(metricsCalculationContext, order, lineItem, charge);
        return lineItemMetrics.toMetrics();
    }

    public LineItemMetrics getMetricsForOrderLine(String orderLineItemId) {
        OrderLineItem lineItem = validateAndGetOrderLine(orderLineItemId);
        Order order = validateAndGetOrder(lineItem.getOrderId());
        Charge charge = productCatalogGetService.getChargeByChargeId(lineItem.getChargeId());
        MetricsCalculationContext metricsCalculationContext = getMetricsCalculationContext(order);
        return getOrderLineMetrics(metricsCalculationContext, order, lineItem, charge);
    }

    public Map<String, Metrics> getMetricsForAllOrderLines(String orderId) {
        Order order = validateAndGetOrder(orderId);
        return getMetricsForAllOrderLines(order);
    }

    @Deprecated
    public Map<String, Metrics> getMetricsForAllOrderLines(Order originalOrder) {
        return getLineItemMetricsForAllOrderLines(originalOrder)
            .entrySet()
            .stream()
            .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().toMetrics()));
    }

    public Map<String, LineItemMetrics> getLineItemMetricsForAllOrderLines(Order originalOrder) {
        Order order = orderMapper.duplicateOrder(originalOrder);
        Map<String, LineItemMetrics> orderLineMetrics = new HashMap<>();
        Map<String, Charge> chargeMap = getChargeMap(order.getLineItems());
        MetricsCalculationContext metricsCalculationContext = getMetricsCalculationContext(order);

        order
            .getLineItems()
            .forEach(orderLineItem -> {
                Charge charge = chargeMap.get(orderLineItem.getChargeId());
                orderLineMetrics.put(orderLineItem.getOrderLineId(), getOrderLineMetrics(metricsCalculationContext, order, orderLineItem, charge));
            });
        return orderLineMetrics;
    }

    private LineItemMetrics getOrderLineMetrics(
        MetricsCalculationContext metricsCalculationContext,
        Order order,
        OrderLineItem lineItem,
        Charge charge
    ) {
        var metricsCalculationContextWithoutOverride = metricsCalculationContext.withOverrideOption(ArrOverrideOptions.WITHOUT_OVERRIDE);
        BigDecimal arrWithoutOverride = calculateOrderLineARR(metricsCalculationContextWithoutOverride, order, lineItem, charge);
        if ((lineItem.getMetrics() != null) && !lineItem.getMetrics().isRecompute()) {
            // if order line metrics has already been computed and order line is unchanged, return stored value
            return lineItem.getMetrics().getMetrics().toLineItemMetrics();
        }
        BigDecimal arr = calculateOrderLineARR(metricsCalculationContext, order, lineItem, charge);
        BigDecimal deltaArr;

        BigDecimal recurringTotal = charge.isRecurring() ? lineItem.getAmount() : BigDecimal.ZERO;
        BigDecimal nonRecurringTotal = charge.isRecurring() ? BigDecimal.ZERO : lineItem.getAmount();

        if ((order.getOrderType() == OrderType.NEW || order.getOrderType() == OrderType.RENEWAL) && lineItem.getIsRamp()) {
            // If line is ramped and part of new or renewal order, delta ARR = ARR (line) - ARR (previous line in ramp)
            Optional<OrderLineItem> optionalPreviousRampItem = OrderLineUtils.getPreviousOrderLineItemInRamp(order, lineItem);
            if (optionalPreviousRampItem.isEmpty()) {
                deltaArr = arr;
            } else {
                // if there is a previous ramp item, delta ARR = ARR (current item) - ARR (previous item)
                OrderLineItem previousRampItem = optionalPreviousRampItem.get();
                BigDecimal previousItemArr = calculateOrderLineARR(order, previousRampItem);
                deltaArr = arr.subtract(previousItemArr);
            }
        } else {
            // for everything else, delta ARR = ARR
            deltaArr = arr;
        }

        if (
            featureService.isEnabled(Feature.CHANGE_DELTA_ARR_FOR_RENEWALS) &&
            (lineItem.getAction() == ActionType.RENEWAL || lineItem.getAction() == ActionType.MISSING_RENEWAL) &&
            StringUtils.isNotBlank(lineItem.getBaseExternalSubscriptionChargeId())
        ) {
            Optional<OrderLineItem> parentOrderLineIdOptional = subscriptionOrderService.getAmendedOrRenewedFromLineItem(lineItem);
            if (parentOrderLineIdOptional.isPresent()) {
                LineItemMetrics previousOrderLineMetrics = getMetricsForOrderLine(parentOrderLineIdOptional.get().getOrderLineId());
                deltaArr = arr.subtract(previousOrderLineMetrics.arr());
            }
        }
        deltaArr = getRenewalLineDeltaArrForUpsellAndEarlyRenewal(metricsCalculationContext, lineItem, order, deltaArr);
        BigDecimal tcv = lineItem.getAmount();
        return new LineItemMetrics(tcv, recurringTotal, nonRecurringTotal, arr, arrWithoutOverride, tcv, deltaArr);
    }

    private BigDecimal getRenewalLineDeltaArrForUpsellAndEarlyRenewal(
        MetricsCalculationContext metricsCalculationContext,
        OrderLineItem lineItem,
        Order order,
        BigDecimal deltaArr
    ) {
        if (order.getOrderType() != OrderType.RENEWAL || StringUtils.isBlank(order.getCompositeOrderId())) {
            return deltaArr;
        }

        Optional<CompositeOrder> optionalCompositeOrder = metricsCalculationContext.compositeOrder();

        if (optionalCompositeOrder.isEmpty() || UPSELL_AND_EARLY_RENEWAL != optionalCompositeOrder.get().getType()) {
            return deltaArr;
        }

        // get amendment order from composite order
        Order amendmentOrder = metricsCalculationContext
            .ordersInCompositeOrder()
            .stream()
            .filter(orderInComposite -> orderInComposite.getOrderType() == OrderType.AMENDMENT)
            .findFirst()
            .orElseThrow();
        AtomicReference<BigDecimal> effectiveDeltaArr = new AtomicReference<>(deltaArr);

        List<OrderLineItem> orderLineItems = amendmentOrder
            .getLineItems()
            .stream()
            .filter(orderLineItem ->
                (orderLineItem.getAction() != ActionType.NONE &&
                    StringUtils.isNotBlank(lineItem.getBaseExternalSubscriptionChargeId()) &&
                    lineItem.getBaseExternalSubscriptionChargeId().equalsIgnoreCase(orderLineItem.getBaseExternalSubscriptionChargeId()))
            )
            .toList();
        orderLineItems.forEach(orderLineItem -> {
            LineItemMetrics previousOrderLineMetrics = getMetricsForOrderLine(orderLineItem.getOrderLineId());
            effectiveDeltaArr.set(effectiveDeltaArr.get().subtract(previousOrderLineMetrics.arr()));
        });
        return effectiveDeltaArr.get();
    }

    public BigDecimal getSubscriptionTCV(String subscriptionId) {
        validateAndGetSubscription(subscriptionId);
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);
        List<OrderLineItem> executedOrderLineItems = getExecutedOrderLineItems(orders);
        return getOrderLineTotalAmount(executedOrderLineItems);
    }

    public BigDecimal getSubscriptionExitARR(String subscriptionId) {
        Subscription subscription = validateAndGetSubscription(subscriptionId);
        return getSubscriptionARR(subscription, subscription.getEndDate().minusSeconds(1));
    }

    public BigDecimal getSubscriptionARR(String subscriptionId, Instant targetDate) {
        Subscription subscription = validateAndGetSubscription(subscriptionId);
        return getSubscriptionARR(subscription, targetDate);
    }

    private BigDecimal getSubscriptionARR(Subscription subscription, Instant targetDate) {
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscription.getSubscriptionId());
        List<OrderLineItem> executedOrderLineItems = getExecutedOrderLineItems(orders);
        return getOrderLineTotalARR(targetDate, orders, executedOrderLineItems);
    }

    private Subscription validateAndGetSubscription(String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is required");
        return subscriptionGetService.getSubscription(subscriptionId);
    }

    private Subscription validateAndGetSubscriptionAtVersion(String subscriptionId, int versionNumber) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is required");
        return subscriptionGetService.find(subscriptionId, versionNumber);
    }

    public BigDecimal getOrderLineTCV(String orderLineItemId) {
        Optional<OrderLineItem> lineItemOptional = orderGetService.getOrderLineItemByOrderLineItemId(orderLineItemId);
        return lineItemOptional.map(OrderLineItem::getAmount).orElse(null);
    }

    public BigDecimal getOrderAverageACV(Order order) {
        MetricsCalculationContext metricsCalculationContext = getMetricsCalculationContext(order);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);

        return getAverageAcv(
            metricsCalculationContext.subscriptionStart(),
            order.getEndDate(),
            Period.between(order.getStartDate(), order.getEndDate()),
            metricsCalculationContext.billingAnchorDate(),
            timeZone,
            prorationConfig,
            order.getTotalAmount()
        );
    }

    public BigDecimal getOrderLineACV(String orderId, String orderLineItemId) {
        Optional<OrderLineItem> lineItemOptional = orderGetService.getOrderLineItemByOrderLineItemId(orderLineItemId);
        if (lineItemOptional.isEmpty()) {
            return null;
        }
        Order order = orderGetService.getOrderByOrderId(orderId);
        return calculateOrderLineACV(order, lineItemOptional.get());
    }

    public BigDecimal getOrderLineARR(String orderLineItemId) {
        Optional<OrderLineItem> lineItemOptional = orderGetService.getOrderLineItemByOrderLineItemId(orderLineItemId);
        if (lineItemOptional.isEmpty()) {
            return null;
        }
        Order order = validateAndGetOrder(lineItemOptional.get().getOrderId());
        return calculateOrderLineARR(order, lineItemOptional.get());
    }

    public BigDecimal calculateOrderLineACV(Order order, OrderLineItem lineItem) {
        if (!orderAndLineHasOverLap(order, lineItem)) {
            return BigDecimal.ZERO;
        }

        if (!orderAndLineHasOverLap(order, lineItem)) {
            return BigDecimal.ZERO;
        }

        MetricsCalculationContext metricsCalculationContext = getMetricsCalculationContext(order);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        return getAverageAcv(
            metricsCalculationContext.subscriptionStart(),
            order.getEndDate(),
            Period.between(lineItem.getEffectiveDate(), lineItem.getEndDate()),
            metricsCalculationContext.billingAnchorDate(),
            timeZone,
            metricsCalculationContext.prorationConfig(),
            lineItem.getAmount()
        );
    }

    public BigDecimal calculateOrderLineARR(Order order, OrderLineItem lineItem) {
        Charge charge = productCatalogGetService.getChargeByChargeId(lineItem.getChargeId());

        MetricsCalculationContext metricsCalculationContext = getMetricsCalculationContext(order);

        return calculateOrderLineARR(metricsCalculationContext, order, lineItem, charge);
    }

    private Map<String, MetricsCalculationContext> getMetricsCalculationContextMap(
        String orderId,
        MetricsCalculationContext metricsCalculationContext
    ) {
        // todo: constructing map from a single value with hashmap instead of Map.of because in some cases (dryrun) orderId can be null. Map.of requires non-null keys.
        //  This is dangerous since it's implicitly assuming callers with null order keys is always a single order.
        Map<String, MetricsCalculationContext> metricsCalculationContextMap = new HashMap<>();
        metricsCalculationContextMap.put(orderId, metricsCalculationContext);
        return metricsCalculationContextMap;
    }

    private Map<String, MetricsCalculationContext> getMetricsCalculationContextMap(List<Order> orders) {
        Map<String, MetricsCalculationContext> metricsCalculationContextMap = new HashMap<>();
        orders.forEach(order -> metricsCalculationContextMap.put(order.getOrderId(), getMetricsCalculationContext(order)));
        return metricsCalculationContextMap;
    }

    private MetricsCalculationContext getMetricsCalculationContext(Order order) {
        return getMetricsCalculationContext(order, ArrOverrideOptions.WITH_OVERRIDE);
    }

    private MetricsCalculationContext getMetricsCalculationContext(Order order, ArrOverrideOptions arrOverrideOptions) {
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);

        Instant subscriptionStartDate;
        Instant billingAnchorDate;

        if (order.getOrderType() == OrderType.NEW || order.getOrderType() == OrderType.RENEWAL || order.getOrderType() == OrderType.RESTRUCTURE) {
            subscriptionStartDate = order.getStartDate();
            billingAnchorDate = order.getBillingAnchorDate();
        } else {
            Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
            subscriptionStartDate = subscription.getStartDate();
            billingAnchorDate = subscription.getBillingAnchorDate();
        }

        Optional<CompositeOrder> compositeOrder;

        if (order.getOrderType() == OrderType.RENEWAL && StringUtils.isNotBlank(order.getCompositeOrderId())) {
            String compositeOrderId = order.getCompositeOrderId();
            try {
                compositeOrder = Optional.ofNullable(compositeOrderGetService.getCompositeOrder(compositeOrderId));
            } catch (ObjectNotFoundException ex) {
                // Note: composite order may not exist here during initial upsell + early renewal order creation.
                // This is because the following sequence is taken:
                //  1) composite order ID is generated
                //  2) amendment order is persisted to DB with composite order ID
                //  3) renewal order is persisted to DB with composite order ID
                //  4) composite order persisted to DB
                // During the write operation of the renewal order, metrics is computed but the composite order does not yet exist in the DB.
                // todo: either persist composite order first followed by amendment and renewal orders (in a DB transaction), or don't compute metrics during save operation
                compositeOrder = Optional.empty();
            }
        } else {
            compositeOrder = Optional.empty();
        }

        List<Order> ordersInCompositeOrder = compositeOrder
            .map(CompositeOrder::getCompositeOrderId)
            .map(compositeOrderGetService::getOrdersInCompositeOrder)
            .orElse(List.of());

        return new MetricsCalculationContext(
            subscriptionStartDate,
            billingAnchorDate,
            prorationConfig,
            compositeOrder,
            ordersInCompositeOrder,
            arrOverrideOptions
        );
    }

    private BigDecimal calculateOrderLineARR(
        MetricsCalculationContext metricsCalculationContext,
        Order order,
        OrderLineItem lineItem,
        Charge charge
    ) {
        if (!orderAndLineHasOverLap(order, lineItem)) {
            return BigDecimal.ZERO;
        }

        if (lineItem.getAction() == ActionType.NONE || lineItem.getAction() == ActionType.MISSING_RENEWAL) {
            return BigDecimal.ZERO;
        }

        if (charge.getType() == ChargeType.USAGE || BooleanUtils.isFalse(charge.getShouldTrackArr())) {
            return BigDecimal.ZERO;
        }

        if (
            featureService.isEnabled(Feature.ORDER_LINE_ARR_OVERRIDE) &&
            lineItem.getArrOverride() != null &&
            metricsCalculationContext.arrOverrideOptions() == ArrOverrideOptions.WITH_OVERRIDE
        ) {
            return Numbers.makeCurrencyScale(lineItem.getArrOverride());
        }

        if (charge.getType() == ChargeType.PERCENTAGE_OF || charge.getType() == ChargeType.PREPAID || charge.getType() == ChargeType.ONE_TIME) {
            TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
            // Calculate ARR by prorating in reverse. This is subject to potential rounding issues
            return getAverageAcv(
                metricsCalculationContext.subscriptionStart(),
                order.getEndDate(),
                Period.between(lineItem.getEffectiveDate(), lineItem.getEndDate()),
                metricsCalculationContext.billingAnchorDate(),
                timeZone,
                metricsCalculationContext.prorationConfig(),
                lineItem.getAmount()
            );
        }

        return calculateOrderLineARRForRecurring(lineItem, charge);
    }

    // Calculate ARR value per line based on the charge list price normalized to annual values and any discounts
    private BigDecimal calculateOrderLineARRForRecurring(OrderLineItem orderLineItem, Charge charge) {
        ListAmount listAmount = invoiceAmountCalculator.calculateRecurringItemPrice(orderLineItem, charge);
        ListUnitPrice listUnitPrice = invoiceAmountCalculator.getListUnitPrice(charge, orderLineItem);

        BigDecimal multiplier = getRecurrenceToYearMultiplier(charge.getRecurrence());
        listAmount = listAmount.map(amount -> amount.multiply(multiplier));

        var invoiceItemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(
            orderLineItem.getDiscounts(),
            orderLineItem.getPredefinedDiscounts(),
            listAmount,
            listUnitPrice,
            orderLineItem.getQuantity(),
            orderLineItem.getListPriceOverrideRatio()
        );

        return invoiceItemAmounts.getSellAmount();
    }

    static BigDecimal getRecurrenceToYearMultiplier(Recurrence recurrence) {
        return switch (recurrence.getCycle()) {
            case YEAR -> Numbers.scaledDivide(BigDecimal.ONE, BigDecimal.valueOf(recurrence.getStep()));
            case MONTH -> Numbers.scaledDivide(BigDecimal.valueOf(Recurrence.NUMBER_OF_MONTHS_IN_YEAR), BigDecimal.valueOf(recurrence.getStep()));
            case QUARTER -> Numbers.scaledDivide(BigDecimal.valueOf(Recurrence.NUMBER_OF_QUARTERS_IN_YEAR), BigDecimal.valueOf(recurrence.getStep()));
            case SEMI_ANNUAL -> Numbers.scaledDivide(
                BigDecimal.valueOf(Recurrence.NUMBER_OF_SEMI_ANNUAL_CYCLES_IN_YEAR),
                BigDecimal.valueOf(recurrence.getStep())
            );
            case DAY -> Numbers.scaledDivide(BigDecimal.valueOf(Recurrence.NUMBER_OF_DAYS_IN_YEAR), BigDecimal.valueOf(recurrence.getStep()));
            case PAID_IN_FULL, CUSTOM -> throw new UnsupportedOperationException(
                String.format("Recurrence Cycle %s is not supported", recurrence.getCycle())
            );
        };
    }

    public List<TimeSeriesElement<BigDecimal>> getOrderARRTrend(Order order) {
        Map<String, MetricsCalculationContext> orderMetricsCalculationContextMap = getMetricsCalculationContextMap(List.of(order));
        List<OrderLineItem> orderLineItems = order.getLineItemsNetEffect();
        var chargeMap = getChargeMap(orderLineItems);
        return getOrderLineItemsARRTrend(orderMetricsCalculationContextMap, List.of(order), orderLineItems, chargeMap, Optional.empty());
    }

    public List<TimeSeriesElement<BigDecimal>> getAccountARRTrend(Account account) {
        List<Order> orders = getAccountExecutedOrders(account.getAccountId());
        Map<String, MetricsCalculationContext> orderMetricsCalculationContextMap = getMetricsCalculationContextMap(orders);
        List<OrderLineItem> orderLineItems = getExecutedOrderLineItems(orders);
        var chargeMap = getChargeMap(orderLineItems);
        return getOrderLineItemsARRTrend(orderMetricsCalculationContextMap, orders, orderLineItems, chargeMap, Optional.empty());
    }

    public List<TimeSeriesElement<BigDecimal>> getPendingAccountARRTrend(Account account) {
        List<Order> orders = orderGetService.getOrdersByAccountId(account.getAccountId());
        List<OrderLineItem> orderLineItems = orders.stream().flatMap(order -> order.getLineItemsNetEffect().stream()).collect(Collectors.toList());
        var chargeMap = getChargeMap(orderLineItems);
        Map<String, MetricsCalculationContext> metricsCalculationContextMap = getMetricsCalculationContextMap(orders);
        return getOrderLineItemsARRTrend(metricsCalculationContextMap, orders, orderLineItems, chargeMap, Optional.empty());
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private List<TimeSeriesElement<BigDecimal>> getOrderLineItemsARRTrend(
        Map<String, MetricsCalculationContext> orderMetricsCalculationContextMap,
        List<Order> orders,
        List<OrderLineItem> orderLineItems,
        Map<String, Charge> chargeMap,
        Optional<Map<String, BigDecimal>> lineItemArrAmountsOptional
    ) {
        List<OrderLineItem> arrTrackingLineItems = getArrTrackingOrderLineItems(orderLineItems, chargeMap);

        List<Instant> sortedChangeInstants = getSortedChangeInstants(arrTrackingLineItems);

        Map<String, Order> orderMap = getOrderMapFromOrderLineItems(orders, arrTrackingLineItems);

        Map<String, BigDecimal> orderLineARRAmounts = lineItemArrAmountsOptional.orElseGet(() ->
            getOrderLineToArrMap(orderMetricsCalculationContextMap, chargeMap, arrTrackingLineItems, orderMap)
        );

        List<TimeSeriesElement<BigDecimal>> arrTrend = new ArrayList<>();

        for (Instant instant : sortedChangeInstants) {
            List<OrderLineItem> currentOrderLineItems = getIntersectingOrderLineItems(instant, arrTrackingLineItems);
            BigDecimal arr = currentOrderLineItems
                .stream()
                .map(orderLineItem -> orderLineARRAmounts.get(orderLineItem.getOrderLineId()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            arrTrend.add(new TimeSeriesElement<>(instant, Numbers.makeCurrencyScale(arr)));
        }

        return mergeContinuousEquivalentSegments(arrTrend);
    }

    private @NotNull Map<String, BigDecimal> getOrderLineToArrMap(
        Map<String, MetricsCalculationContext> orderMetricsCalculationContextMap,
        Map<String, Charge> chargeMap,
        List<OrderLineItem> arrTrackingLineItems,
        Map<String, Order> orderMap
    ) {
        Map<String, BigDecimal> orderLineARRAmounts = new HashMap<>();
        for (OrderLineItem orderLineItem : arrTrackingLineItems) {
            Order order = orderMap.get(orderLineItem.getOrderId());
            BigDecimal arr = calculateOrderLineARR(
                orderMetricsCalculationContextMap.get(order.getOrderId()),
                order,
                orderLineItem,
                chargeMap.get(orderLineItem.getChargeId())
            );
            orderLineARRAmounts.put(orderLineItem.getOrderLineId(), arr);
        }
        return orderLineARRAmounts;
    }

    List<TimeSeriesElement<BigDecimal>> mergeContinuousEquivalentSegments(List<TimeSeriesElement<BigDecimal>> arrTrend) {
        List<TimeSeriesElement<BigDecimal>> mergedArrTrend = new ArrayList<>();

        for (int i = 0; i < arrTrend.size(); i++) {
            var current = arrTrend.get(i);
            mergedArrTrend.add(current);

            while (i < arrTrend.size() - 1) {
                // loop until second last index so there is 1 more next element
                var next = arrTrend.get(i + 1);
                if (Numbers.equals(current.getData(), next.getData())) {
                    // if the current and next elements have equal value, skip next
                    i++;
                } else {
                    break;
                }
            }
        }

        return mergedArrTrend;
    }

    private BigDecimal getOrderLineTotalARR(Instant targetDate, List<Order> orders, List<OrderLineItem> orderLineItems) {
        Map<String, Charge> chargeMap = getChargeMap(orderLineItems);
        List<OrderLineItem> lineItemsForArrCalculation = getArrTrackingOrderLineItems(orderLineItems, chargeMap);
        Map<String, Order> orderMap = getOrderMapFromOrderLineItems(orders, lineItemsForArrCalculation);
        Map<String, MetricsCalculationContext> metricsCalculationContextMap = getMetricsCalculationContextMap(orders);
        return getOrderLineTotalARR(metricsCalculationContextMap, targetDate, lineItemsForArrCalculation, chargeMap, orderMap);
    }

    // calculates the aggregate ARR value for a list of order line items at a given point in time
    // any order line items that do not intersect the point in time will be ignored
    private BigDecimal getOrderLineTotalARR(
        Map<String, MetricsCalculationContext> orderMetricsCalculationContextMap,
        Instant targetDate,
        List<OrderLineItem> orderLineItems,
        Map<String, Charge> chargeMap,
        Map<String, Order> orderMap
    ) {
        List<OrderLineItem> currentOrderLineItems = getIntersectingOrderLineItems(targetDate, orderLineItems);

        BigDecimal totalARR = BigDecimal.ZERO;
        for (OrderLineItem orderLineItem : currentOrderLineItems) {
            Order order = orderMap.get(orderLineItem.getOrderId());
            BigDecimal orderLineARR = calculateOrderLineARR(
                orderMetricsCalculationContextMap.get(order.getOrderId()),
                order,
                orderLineItem,
                chargeMap.get(orderLineItem.getChargeId())
            );
            totalARR = totalARR.add(orderLineARR);
        }

        return totalARR;
    }

    private Map<String, Order> getOrderMapFromOrderLineItems(List<Order> orders, List<OrderLineItem> orderLineItems) {
        if (orderLineItems.isEmpty()) {
            return new HashMap<>();
        }

        return orders.stream().collect(Collectors.toMap(Order::getOrderId, Function.identity()));
    }

    private BigDecimal getOrderLineTotalAmount(List<OrderLineItem> orderLineItems) {
        return orderLineItems.stream().map(OrderLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal getOrderRecurringTotalListAmount(OrderDetail orderDetail) {
        return getRecurringLineItems(orderDetail.getLineItems())
            .stream()
            .map(OrderLineItemDetail::getListAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal getOrderRecurringTotal(OrderDetail orderDetail) {
        return getRecurringLineItems(orderDetail.getLineItems())
            .stream()
            .map(OrderLineItemDetail::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal getOrderNonRecurringTotalListAmount(OrderDetail orderDetail) {
        return getNonRecurringLineItems(orderDetail.getLineItems())
            .stream()
            .map(OrderLineItemDetail::getListAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal getOrderNonRecurringTotal(OrderDetail orderDetail) {
        return getNonRecurringLineItems(orderDetail.getLineItems())
            .stream()
            .map(OrderLineItemDetail::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static List<OrderLineItemDetail> getRecurringLineItems(List<OrderLineItemDetail> orderLineItems) {
        return orderLineItems.stream().filter(lineItem -> lineItem.getCharge().isRecurring()).collect(Collectors.toList());
    }

    private static List<OrderLineItemDetail> getNonRecurringLineItems(List<OrderLineItemDetail> orderLineItems) {
        return orderLineItems.stream().filter(lineItem -> !(lineItem.getCharge().isRecurring())).collect(Collectors.toList());
    }

    private List<Instant> getSortedChangeInstants(List<OrderLineItem> recurringOrderLineItems) {
        List<Instant> uniqueOrderLineStarts = recurringOrderLineItems.stream().map(OrderLineItem::getEffectiveDate).distinct().toList();

        List<Instant> uniqueOrderLineEnds = recurringOrderLineItems.stream().map(OrderLineItem::getEndDate).distinct().toList();

        return Stream.concat(uniqueOrderLineStarts.stream(), uniqueOrderLineEnds.stream()).distinct().sorted().collect(Collectors.toList());
    }

    private static List<OrderLineItem> getIntersectingOrderLineItems(Instant targetDate, List<OrderLineItem> orderLineItems) {
        return orderLineItems
            .stream()
            .filter(orderLineItem -> targetDate.isBefore(orderLineItem.getEndDate()) && !targetDate.isBefore(orderLineItem.getEffectiveDate()))
            .collect(Collectors.toList());
    }

    private static List<OrderLineItem> getArrTrackingOrderLineItems(List<OrderLineItem> orderLineItems, Map<String, Charge> chargeMap) {
        return orderLineItems
            .stream()
            .filter(orderLineItem -> BooleanUtils.isTrue(chargeMap.get(orderLineItem.getChargeId()).getShouldTrackArr()))
            .collect(Collectors.toList());
    }

    // Given a total amount over a given period, calculate the equivalent ARR value
    // Example 1: $1000 over 6 months => $2000 ARR
    // Example 2: $3000 over 3 years => $1000 ARR
    private BigDecimal getAverageAcv(
        Instant subscriptionStartDate,
        Instant subscriptionEndDate,
        Period period,
        Instant billingAnchorDate,
        TimeZone timeZone,
        ProrationConfig prorationConfig,
        BigDecimal totalAmount
    ) {
        // Fetch a list of yearly periods from the start of the subscription based on given timezone
        List<BillingPeriod> years = subscriptionBillingPeriodService.getBillingPeriods(
            subscriptionStartDate,
            subscriptionEndDate,
            billingAnchorDate,
            period.getEnd(),
            timeZone.toZoneId(),
            new Recurrence(Cycle.YEAR, 1),
            BillingTerm.UP_FRONT
        );

        BigDecimal numYears = BigDecimal.ZERO;
        for (var year : years) {
            // for each yearly period from start of subscription, determine total fraction of the year the item period intersects with and sum for all years
            // this fraction is based on tenant's proration configuration
            Period overlapPeriod = SubscriptionBillingPeriodService.getItemOverlapPeriod(period.getStart(), period.getEnd(), year);
            numYears = numYears.add(ProrationCalculator.getProrationRatio(year, overlapPeriod, prorationConfig, timeZone));
        }

        return numYears.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : Numbers.scaledDivide(totalAmount, numYears);
    }

    private BigDecimal getAverageArr(
        Instant subscriptionStartDate,
        Period period,
        Instant billingAnchorDate,
        TimeZone timeZone,
        ProrationConfig prorationConfig,
        List<OrderLineItem> arrLineItems,
        Map<String, BigDecimal> orderLineARRAmounts
    ) {
        // Fetch a list of yearly periods from the start of the subscription based on given timezone
        List<BillingPeriod> years = subscriptionBillingPeriodService.getBillingPeriods(
            subscriptionStartDate,
            period.getEnd(),
            billingAnchorDate,
            period.getEnd(),
            timeZone.toZoneId(),
            new Recurrence(Cycle.YEAR, 1),
            BillingTerm.UP_FRONT
        );
        BigDecimal numYears = BigDecimal.ZERO;
        BigDecimal totalArr = BigDecimal.ZERO;
        for (var year : years) {
            // for each yearly period from start of subscription, determine total fraction of the year the item period intersects with and sum for all years
            // this fraction is based on tenant's proration configuration
            Period overlapPeriod = SubscriptionBillingPeriodService.getItemOverlapPeriod(period.getStart(), period.getEnd(), year);
            numYears = numYears.add(ProrationCalculator.getProrationRatio(year, overlapPeriod, prorationConfig, timeZone));

            for (var lineItem : arrLineItems) {
                Period lineItemOverlap = SubscriptionBillingPeriodService.getItemOverlapPeriod(
                    lineItem.getEffectiveDate(),
                    lineItem.getEndDate(),
                    year
                );
                BigDecimal lineItemProration = ProrationCalculator.getProrationRatio(year, lineItemOverlap, prorationConfig, timeZone);
                BigDecimal lineItemArr = orderLineARRAmounts.get(lineItem.getOrderLineId()).multiply(lineItemProration);
                totalArr = totalArr.add(lineItemArr);
            }
        }

        return numYears.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : Numbers.scaledDivide(totalArr, numYears);
    }

    private Map<String, Charge> getChargeMap(List<OrderLineItem> orderLineItems) {
        return OrderServiceHelper.getChargeMap(productCatalogGetService, orderLineItems);
    }

    private static boolean orderAndLineHasOverLap(Order order, OrderLineItem lineItem) {
        return Period.overlapOf(
            Period.between(order.getStartDate(), order.getEndDate()),
            Period.between(lineItem.getEffectiveDate(), lineItem.getEndDate())
        ).isPresent();
    }

    private List<Order> getAccountExecutedOrders(String accountId) {
        UUID cursor = null;
        List<Order> executedOrdersInAccount = new ArrayList<>();
        do {
            PaginationQueryParams orderQueryParams = new PaginationQueryParams(cursor, MAX_GET_ORDERS_PAGINATION_LIMIT);
            List<Order> executedOrders = orderGetService.getOrdersInAccount(accountId, orderQueryParams, Optional.of(OrderStatus.EXECUTED));

            var paginationResponse = new PaginationResponseJson<>(
                executedOrders,
                MAX_GET_ORDERS_PAGINATION_LIMIT,
                orderMapper::getUnmodifiableList,
                Order::getId
            );

            executedOrdersInAccount.addAll(paginationResponse.getData());
            cursor = paginationResponse.getNextCursor();
        } while (cursor != null);

        return executedOrdersInAccount;
    }

    private static List<OrderLineItem> getExecutedOrderLineItems(List<Order> orders) {
        List<Order> executedOrders = orders.stream().filter(order -> order.getStatus() == OrderStatus.EXECUTED).toList();
        return executedOrders.stream().map(Order::getLineItemsNetEffect).flatMap(List::stream).toList();
    }

    private Account validateAndGetAccount(String accountId) {
        if (StringUtils.isBlank(accountId)) {
            throw new IllegalArgumentException("accountId is required");
        }

        Account account = accountGetService.getAccount(accountId);

        if (account == null) {
            throw new ObjectNotFoundException(BillyObjectType.ACCOUNT, accountId);
        }

        return account;
    }

    private Order validateAndGetOrder(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            throw new IllegalArgumentException("orderId is required");
        }

        Order order = orderGetService.getOrderByOrderId(orderId);

        if (order == null) {
            throw new ObjectNotFoundException(BillyObjectType.ORDER, orderId);
        }

        return order;
    }

    private OrderLineItem validateAndGetOrderLine(String orderLineItemId) {
        if (StringUtils.isBlank(orderLineItemId)) {
            throw new IllegalArgumentException("orderLineItemId is required");
        }

        return orderGetService
            .getOrderLineItemByOrderLineItemId(orderLineItemId)
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ORDER_LINE_ITEM, orderLineItemId));
    }

    private void checkTotalAmounts(BigDecimal nonRecurringTotal, BigDecimal recurringTotal, BigDecimal tcv, String identifier) {
        if (nonRecurringTotal.add(recurringTotal).compareTo(tcv) != 0) {
            LOGGER.warn(
                "total amounts do not match for {}. Non-recurring total: {}, recurring total: {}, TCV: {}",
                identifier,
                nonRecurringTotal,
                recurringTotal,
                tcv
            );
        }
    }

    public Map<String, BigDecimal> getOrderLineItemAnnualizedAmounts(Order order, List<Charge> charges) {
        if (CollectionUtils.isEmpty(order.getLineItems())) {
            return Map.of();
        }

        // duplicate order to avoid modifying the original order
        // todo: DiscountCalculator mutates line item discount objects and needs to be updated
        //  This is pretty dangerous side affect as calls to MetricsService to calculate line item ARR can update the order line discount amounts
        Order duplicatedOrder = orderMapper.duplicateOrder(order);

        Map<String, Charge> chargeMap = charges.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        MetricsCalculationContext metricsCalculationContext = getMetricsCalculationContext(duplicatedOrder, ArrOverrideOptions.WITHOUT_OVERRIDE);
        Map<String, BigDecimal> lineItemAnnualizedAmounts = new HashMap<>();

        duplicatedOrder
            .getLineItems()
            .forEach(item -> {
                Charge charge = chargeMap.get(item.getChargeId());
                if (charge == null) {
                    throw new InvariantCheckFailedException("Charge not found for item: " + item.getId());
                }
                Optional<BigDecimal> annualizedAmount = getItemAnnualizedAmount(metricsCalculationContext, duplicatedOrder, charge, item);
                annualizedAmount.ifPresent(bigDecimal -> lineItemAnnualizedAmounts.put(item.getOrderLineId(), bigDecimal));
            });

        return lineItemAnnualizedAmounts;
    }

    // calculate order line item amount on an annualized basis
    private Optional<BigDecimal> getItemAnnualizedAmount(
        MetricsCalculationContext metricsCalculationContext,
        Order order,
        Charge charge,
        OrderLineItem item
    ) {
        if (
            StringUtils.isNotBlank(order.getCompositeOrderId()) &&
            (order.getOrderType() == OrderType.RENEWAL || order.getOrderType() == OrderType.AMENDMENT)
        ) {
            // skip for Amend and Renew
            // todo: update metrics to handle composite order ARR calculation for unsaved amend and renew orders
            return Optional.empty();
        }
        return switch (charge.getType()) {
            case RECURRING -> {
                if (item.getEffectiveDate().compareTo(item.getEndDate()) == 0) {
                    // ARR amount for 0 duration recurring items is 0, calculate based on charge amounts
                    ListAmount listAmount = invoiceAmountCalculator.calculateRecurringItemPrice(item, charge);
                    BigDecimal sellAmount = getAnnualSellAmount(item, listAmount, charge);
                    yield Optional.of(sellAmount);
                } else {
                    LineItemMetrics metrics = getOrderLineMetrics(metricsCalculationContext, order, item, charge);
                    yield Optional.of(metrics.arr());
                }
            }
            case PERCENTAGE_OF -> {
                LineItemMetrics metrics = getOrderLineMetrics(metricsCalculationContext, order, item, charge);
                yield Optional.of(metrics.arr());
            }
            case PREPAID -> {
                if (charge.getRecurrence() == null) {
                    // if prepaid charge recurrence is null, then treat it as a one time charge
                    yield Optional.empty();
                }

                // todo: should this be make list amount with override?
                ListAmount listAmount = invoiceAmountCalculator.calculatePrepaidListAmount(item, charge);
                BigDecimal sellAmount = getAnnualSellAmount(item, listAmount, charge);
                yield Optional.of(sellAmount);
            }
            case USAGE, ONE_TIME -> Optional.empty(); // usage and one time charges are not annualized
        };
    }

    private BigDecimal getAnnualSellAmount(OrderLineItem item, ListAmount listAmount, Charge charge) {
        return getPeriodizedAmounts(item, listAmount, charge, new Recurrence(Cycle.YEAR, 1)).getSellAmount();
    }

    private InvoiceItemAmounts getPeriodizedAmounts(OrderLineItem item, ListAmount listAmount, Charge charge, Recurrence recurrence) {
        // normalize amount to annual amount based on charge cycle
        listAmount = listAmount.map(amount -> invoiceAmountCalculator.getScaledCost(amount, charge.getRecurrence(), recurrence));

        // calculate amounts based on list amount, discounts and price override
        return invoiceAmountCalculator.getInvoiceItemAmounts(
            item.getDiscounts(),
            item.getPredefinedDiscounts(),
            listAmount,
            invoiceAmountCalculator.getListUnitPrice(charge, item),
            item.getQuantity(),
            item.getListPriceOverrideRatio()
        );
    }

    public Map<String, InvoiceItemAmounts> getOrderLineItemBillingCycleAmounts(Order order, List<Charge> charges) {
        if (!EvergreenUtils.isEvergreenOrder(order, featureService) || CollectionUtils.isEmpty(order.getLineItems())) {
            return Map.of();
        }

        Order duplicatedOrder = orderMapper.duplicateOrder(order);

        Map<String, Charge> chargeMap = charges.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        Map<String, InvoiceItemAmounts> lineItemBillingCycleAmounts = new HashMap<>();
        duplicatedOrder
            .getLineItems()
            .forEach(item -> {
                Charge charge = chargeMap.get(item.getChargeId());
                if (charge == null) {
                    throw new InvariantCheckFailedException("Charge not found for item: " + item.getId());
                }
                Optional<InvoiceItemAmounts> billingCycleAmounts = getItemBillingCycleAmounts(duplicatedOrder, charge, item);
                billingCycleAmounts.ifPresent(bca -> lineItemBillingCycleAmounts.put(item.getOrderLineId(), bca));
            });

        return lineItemBillingCycleAmounts;
    }

    private Optional<InvoiceItemAmounts> getItemBillingCycleAmounts(Order order, Charge charge, OrderLineItem item) {
        Recurrence chargeBillingCycleRecurrence = getChargeBillingCycleRecurrence(order, charge);
        return switch (charge.getType()) {
            case RECURRING -> {
                ListAmount listAmount = invoiceAmountCalculator.calculateRecurringItemPrice(item, charge);
                InvoiceItemAmounts amounts = getPeriodizedAmounts(item, listAmount, charge, chargeBillingCycleRecurrence);
                yield Optional.of(amounts);
            }
            // TODO: how to calculate billing cycle amount for percentage of charge?
            case PERCENTAGE_OF -> Optional.empty();
            case PREPAID -> {
                if (charge.getRecurrence() == null) {
                    // if prepaid charge recurrence is null, then treat it as a one time charge
                    yield Optional.empty();
                }
                ListAmount listAmount = invoiceAmountCalculator.calculatePrepaidListAmount(item, charge);
                InvoiceItemAmounts amounts = getPeriodizedAmounts(item, listAmount, charge, chargeBillingCycleRecurrence);
                yield Optional.of(amounts);
            }
            case USAGE, ONE_TIME -> Optional.empty();
        };
    }

    private Recurrence getChargeBillingCycleRecurrence(Order order, Charge charge) {
        if (charge.getBillingCycle() == BillingCycle.CHARGE_RECURRENCE) {
            // if charge billing cycle is set to charge recurrence, use the charge recurrence
            return charge.getRecurrence();
        } else if (charge.getBillingCycle() == BillingCycle.DEFAULT) {
            // if charge billing cycle is set to default, use the order billing cycle
            return order.getBillingCycle();
        } else if (charge.getBillingCycle().getCycle().isPresent()) {
            return new Recurrence(charge.getBillingCycle().getCycle().get(), 1);
        }
        throw new InvariantCheckFailedException(
            String.format("Cannot infer billing cycle for charge: id=%s, billingCycle=%s", charge.getChargeId(), charge.getBillingCycle())
        );
    }
}
