package com.subskribe.billy.invoice.service;

import com.subskribe.billy.cache.CacheLoader;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.entity.model.NumberConfig;
import com.subskribe.billy.exception.DuplicateInvoicePrefixException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.invoice.db.InvoiceConfigDAO;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.InvoiceNumberPrefix;
import com.subskribe.billy.invoice.model.InvoiceNumberScheme;
import com.subskribe.billy.invoice.model.TenantInvoiceConfig;
import com.subskribe.billy.jooq.default_schema.tables.Invoice;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.validation.Validator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.inject.Inject;

public class InvoiceConfigurationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InvoiceConfigurationService.class);

    private final InvoiceDAO invoiceDAO;

    private final InvoiceConfigDAO invoiceConfigDAO;

    private final CacheService cacheService;

    private final CacheLoader<String, TenantInvoiceConfig> invoiceConfigByIdCacheLoader;

    @Inject
    public InvoiceConfigurationService(InvoiceDAO invoiceDAO, InvoiceConfigDAO invoiceConfigDAO, CacheService cacheService) {
        this.invoiceDAO = invoiceDAO;
        this.invoiceConfigDAO = invoiceConfigDAO;
        this.cacheService = cacheService;
        invoiceConfigByIdCacheLoader = this::getInvoiceConfigById;
    }

    public TenantInvoiceConfig getCachedInvoiceConfigById(String invoiceConfigId) {
        return cacheService.get(
            invoiceConfigId,
            CacheType.INVOICE_CONFIG_BY_ID,
            invoiceConfigId,
            invoiceConfigByIdCacheLoader,
            TenantInvoiceConfig.class
        );
    }

    public TenantInvoiceConfig getInvoiceConfigById(String invoiceConfigId) {
        try {
            var config = invoiceDAO.getInvoiceConfigById(invoiceConfigId);
            populateNextInvoiceNumberForReadPathByConfigId(config);
            return config;
        } catch (Exception ex) {
            LOGGER.error("Error getting invoice configuration", ex);
            throw ex;
        }
    }

    public NumberConfig getInvoiceNumberConfigById(String invoiceConfigId) {
        return invoiceConfigDAO.getInvoiceConfigById(invoiceConfigId);
    }

    private void populateNextInvoiceNumberForReadPathByConfigId(TenantInvoiceConfig tenantInvoiceConfig) {
        if (tenantInvoiceConfig != null && tenantInvoiceConfig.getInvoiceNumberScheme() == InvoiceNumberScheme.SEQUENCE) {
            Optional<Long> nextNumber = invoiceDAO.getPointInTimeInvoiceSequenceNumberByConfigId(tenantInvoiceConfig.getInvoiceConfigId());
            nextNumber.ifPresent(tenantInvoiceConfig::setInvoiceNextNumber);
        }
    }

    // todo: multientity: temporary method for data migration. remove it once backfill completed in all environments
    public void backfillInvoiceConfigIdOnSequence() {
        invoiceDAO.backfillInvoiceConfigIdOnSequence();
    }

    public void invariantCheckSequenceExists(TenantInvoiceConfig invoiceConfig) {
        if (
            invoiceConfig.getInvoiceNumberScheme() == InvoiceNumberScheme.SEQUENCE &&
            !invoiceDAO.checkInvoiceSequenceExistsByConfigId(invoiceConfig.getInvoiceConfigId())
        ) {
            throw new InvariantCheckFailedException(
                String.format("invoice sequence record missing for invoice config: %s", invoiceConfig.getInvoiceConfigId())
            );
        }
    }

    public NumberConfig createInvoiceConfig(NumberConfig numberConfig) {
        validateInvoiceNumberLength(numberConfig);
        validateDuplicateInvoicePrefix(numberConfig);
        return invoiceConfigDAO.createInvoiceConfig(numberConfig);
    }

    private void validateInvoiceNumberLength(NumberConfig numberConfig) {
        if (numberConfig.length() < 1) {
            throw new InvalidInputException("invoice number length must be greater than 0");
        }
        if (numberConfig.prefix().isEmpty()) {
            throw new InvalidInputException("invoice prefix is required");
        }
        int prefixLength = Math.max(InvoiceNumberPrefix.getDraftPrefix().length(), numberConfig.prefix().length());
        if (prefixLength + numberConfig.length() > Invoice.INVOICE.INVOICE_NUMBER.getDataType().length()) {
            throw new InvalidInputException(
                String.format("invoice number cannot be longer than %s characters", Invoice.INVOICE.INVOICE_NUMBER.getDataType().length())
            );
        }
    }

    private void validateDuplicateInvoicePrefix(NumberConfig numberConfig) {
        Validator.validateStringNotBlank(numberConfig.prefix(), "invoice prefix is required");
        List<NumberConfig> invoiceConfigs = invoiceConfigDAO.getInvoiceConfigs();
        boolean isDuplicatePrefix = invoiceConfigs
            .stream()
            .anyMatch(config -> !Objects.equals(config.configId(), numberConfig.configId()) && config.prefix().equals(numberConfig.prefix()));
        if (isDuplicatePrefix) {
            throw new DuplicateInvoicePrefixException("invoice prefix already exists");
        }
    }

    public NumberConfig updateInvoiceConfig(NumberConfig numberConfig) {
        validateInvoiceNumberLength(numberConfig);
        validateDuplicateInvoicePrefix(numberConfig);
        return invoiceConfigDAO.updateInvoiceConfig(numberConfig);
    }

    public void updateInvoiceNumberSequence(String invoiceConfigId, long nextInvoiceNumber) {
        invoiceConfigDAO.updateInvoiceNumberSequence(invoiceConfigId, nextInvoiceNumber);
    }
}
