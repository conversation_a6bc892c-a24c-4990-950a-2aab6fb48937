package com.subskribe.billy.invoice.document;

import static com.subskribe.billy.shared.enums.Cycle.MONTH;
import static com.subskribe.billy.shared.enums.Cycle.YEAR;
import static com.subskribe.billy.shared.render.DocumentRenderFormatter.INTERNATIONAL_DATE_FORMAT;
import static com.subskribe.billy.shared.render.DocumentRenderFormatter.US_DATE_FORMAT;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.graphql.invoice.InvoiceDetailsMapper;
import com.subskribe.billy.graphql.invoice.InvoiceItemDetail;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoice.model.RatedQuantity;
import com.subskribe.billy.invoice.model.RatingOutput;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationStatus;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationType;
import com.subskribe.billy.io.LogoEncoder;
import com.subskribe.billy.order.document.TemplateCustomField;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.resources.json.account.AccountAddressJson;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.shared.document.BaseTemplateData;
import com.subskribe.billy.shared.document.TemplateAccount;
import com.subskribe.billy.shared.document.TemplateCharge;
import com.subskribe.billy.shared.document.TemplateError;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.formatter.NumberFormatter;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.tenant.model.TenantInfo;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class InvoiceTemplateData extends BaseTemplateData {

    private static final String CHARGE_ID_PERIOD_STRING = "%s_%s_%s";

    private final InvoiceDocumentJson invoiceDocumentJson;

    private List<InvoiceTemplateLineItem> lineItems;

    private final List<SettlementApplicationTemplateData> settleApplicationsData;

    private String customContent;

    private Long sharedStartDate;

    private Long sharedEndDate;

    private String lineItemOrderByCustomFieldName;

    private InvoiceTemplateBundles itemBundles;

    private List<InvoiceTemplateBundles> bundlesByDate;

    private final InvoiceDetailsMapper invoiceDetailsMapper;

    public InvoiceTemplateData(InvoiceDocumentJson invoiceDocumentJson) {
        this.invoiceDocumentJson = invoiceDocumentJson;
        String currency = StringUtils.isNotBlank(invoiceDocumentJson.getInvoiceDetail().getCurrency())
            ? invoiceDocumentJson.getInvoiceDetail().getCurrency()
            : invoiceDocumentJson.getInvoiceDetail().getCustomerAccount().getCurrency();

        initBaseDocumentJson(invoiceDocumentJson, currency);

        populateSharedDates();
        settleApplicationsData = getSettlementApplicationData(invoiceDocumentJson);
        invoiceDetailsMapper = Mappers.getMapper(InvoiceDetailsMapper.class);
    }

    private List<SettlementApplicationTemplateData> getSettlementApplicationData(InvoiceDocumentJson invoiceDocumentJson) {
        if (CollectionUtils.isEmpty(invoiceDocumentJson.getSettlementApplications())) {
            return List.of();
        }

        return invoiceDocumentJson
            .getSettlementApplications()
            .stream()
            .filter(
                settlementApplication ->
                    settlementApplication.getStatus() == null || settlementApplication.getStatus() == SettlementApplicationStatus.APPLIED_PAYMENT
            )
            .map(settlementApplication -> new SettlementApplicationTemplateData(formatter, currencyCode, settlementApplication))
            .collect(Collectors.toList());
    }

    /**
     * CAUTION: this method is used to change the formatter state for the entire template document and should be invoked first in the template header
     * @deprecated Use {@link #useDateFormat()} instead
     */
    @Deprecated
    public void useUSDateFormat() {
        formatter.setDateFormatPattern(US_DATE_FORMAT);
    }

    /**
     * CAUTION: this method is used to change the formatter state for the entire template document and should be invoked first in the template header
     * @deprecated Use {@link #useDateFormat()} instead
     */
    @Deprecated
    public void useInternationalDateFormat() {
        formatter.setDateFormatPattern(INTERNATIONAL_DATE_FORMAT);
    }

    /**
     * CAUTION: this method is used to change the formatter state for the entire template document and should be invoked first in the template header
     * From template, this can be invoked with the following syntax: {{#useDateFormat}}dd/MM/yyyy{{/useDateFormat}}
     */
    public Function<String, Void> useDateFormat() {
        return input -> {
            Validator.validateStringNotBlank(input, "date format required");
            formatter.setDateFormatPattern(input);
            return null;
        };
    }

    // sort line items by charge custom field of given name
    // Example: {{#sortByChargeCustomField}}rank{{/sortByChargeCustomField}} -> sort line items by charge custom field "rank"'s value
    public Function<String, String> sortByChargeCustomField() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            lineItemOrderByCustomFieldName = input;
            return null;
        };
    }

    public Function<String, String> bundleByPlanCustomField() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            itemBundles = InvoiceTemplateBundles.buildItemBundles(
                invoiceDocumentJson,
                InvoiceTemplateBundleBy.PLAN_CUSTOM_FIELD,
                input.trim(),
                getLineItems(),
                formatter,
                getCurrency()
            );

            return null;
        };
    }

    public Function<String, String> bundleByPlanCustomFieldAndDate() {
        return input -> {
            if (StringUtils.isBlank(input)) {
                return TemplateError.displayError("Custom field name is required");
            }

            Map<String, List<InvoiceTemplateLineItem>> invoiceTemplateLineItemsByRampDatesMap = getLineItems()
                .stream()
                .collect(Collectors.groupingBy(lineItem -> lineItem.getStartDate() + lineItem.getEndDate(), LinkedHashMap::new, Collectors.toList()));

            bundlesByDate = new ArrayList<>();
            for (var pair : invoiceTemplateLineItemsByRampDatesMap.entrySet()) {
                bundlesByDate.add(
                    InvoiceTemplateBundles.buildItemBundles(
                        invoiceDocumentJson,
                        InvoiceTemplateBundleBy.PLAN_CUSTOM_FIELD,
                        input.trim(),
                        pair.getValue(),
                        formatter,
                        getCurrency()
                    )
                );
            }

            return null;
        };
    }

    InvoiceTemplateBundles getItemBundles() {
        return itemBundles;
    }

    List<InvoiceTemplateBundles> getBundlesByDate() {
        return bundlesByDate;
    }

    public Map<String, Boolean> invoiceCurrency() {
        return Map.of(currencyCode, true);
    }

    public Map<String, Boolean> shippingContactCountry() {
        if (invoiceDocumentJson.getInvoiceDetail().getShippingContact().getAddress().getCountry() != null) {
            return Map.of(invoiceDocumentJson.getInvoiceDetail().getShippingContact().getAddress().getCountry(), true);
        } else {
            return Map.of();
        }
    }

    public void hideCurrencyDecimals() {
        formatter.setCurrencyDisplayMaxDecimals(0);
        formatter.setCurrencyDisplayMinDecimals(0);
    }

    // Returns a list of line items aggregated by charge and date range.
    // Example use case this supports is when a line is removed and duplicated to give a different discount and / or quantity,
    // but the invoice should show a single line with the aggregated quantity and amount
    public List<InvoiceTemplateLineItem> getChargeAggregatedLineItems() {
        List<InvoiceItemDetail> lineItems = invoiceDocumentJson.getInvoiceDetail().getInvoiceItems();
        if (CollectionUtils.isEmpty(lineItems)) {
            return List.of();
        }

        Map<String, List<InvoiceItemDetail>> groupedLineItems = lineItems
            .stream()
            .collect(Collectors.groupingBy(InvoiceTemplateData::chargeIdAndPeriodString, Collectors.toList()));

        List<InvoiceTemplateLineItem> aggregatedLineItems = aggregatedGroupedLineItems(groupedLineItems);
        return getSortedLineItems(aggregatedLineItems);
    }

    private List<InvoiceTemplateLineItem> initializeLineItems() {
        List<InvoiceItemDetail> lineItems = invoiceDocumentJson.getInvoiceDetail().getInvoiceItems();
        if (CollectionUtils.isEmpty(lineItems)) {
            return List.of();
        }

        Map<String, List<InvoiceItemDetail>> groupedLineItems = lineItems
            .stream()
            .collect(Collectors.groupingBy(lineItem -> subscriptionChargeIdAndPeriodString(getInvoiceNumber(), lineItem), Collectors.toList()));

        return aggregatedGroupedLineItems(groupedLineItems);
    }

    private List<InvoiceTemplateLineItem> aggregatedGroupedLineItems(Map<String, List<InvoiceItemDetail>> groupedLineItems) {
        Map<String, ProductJson> productsMap = invoiceDocumentJson.getProductsMap();

        List<InvoiceTemplateLineItem> templateLineItems = new ArrayList<>();

        groupedLineItems
            .values()
            .stream()
            .filter(CollectionUtils::isNotEmpty)
            .forEach(items -> {
                List<InvoiceItem> rawLineItems = items.stream().map(invoiceDetailsMapper::detailtoInvoiceItem).toList();
                Optional<InvoiceItem> reducedLineItem = rawLineItems.stream().reduce(InvoiceTemplateData::addAmountAndQuantityForLineItem);

                if (reducedLineItem.isPresent()) {
                    InvoiceItemDetail itemDetails = items.get(0);
                    ChargeDetail charge = itemDetails.getCharge();
                    PlanJson plan = itemDetails.getPlan();
                    CustomField customField = invoiceDocumentJson.getChargeCustomFields().get(charge.getId());
                    TemplateCharge templateCharge = new TemplateCharge(
                        customField,
                        formatter,
                        getCurrency(),
                        charge.getRecurrence() == null ? null : charge.getRecurrence().getCycle(),
                        charge
                    );

                    InvoiceTemplateLineItem templateLineItem = new InvoiceTemplateLineItem(
                        reducedLineItem.get(),
                        rawLineItems,
                        formatter,
                        getCurrency(),
                        productsMap.get(plan.getProductId()),
                        getInvoiceTemplateMapUsage(itemDetails, currencyCode),
                        invoiceDocumentJson.getOrderLineMetrics(),
                        invoiceDocumentJson.getOrderLineItems(),
                        templateCharge,
                        plan,
                        charge
                    );

                    templateLineItems.add(templateLineItem);
                }
            });

        return templateLineItems.stream().sorted(Comparator.comparing(InvoiceTemplateLineItem::getInvoiceLineNumber)).collect(Collectors.toList());
    }

    private List<InvoiceTemplateLineItem> getSortedLineItems(List<InvoiceTemplateLineItem> lineItems) {
        return InvoiceTemplateItemSorter.sortByChargeCustomField(lineItemOrderByCustomFieldName, lineItems);
    }

    public List<InvoiceTemplateLineItem> getOrderLineSortedItems() {
        Map<String, Integer> orderLineRank = invoiceDocumentJson
            .getOrderLineItems()
            .values()
            .stream()
            .collect(Collectors.toMap(OrderLineItem::getOrderLineId, OrderLineItem::getRank));
        return InvoiceTemplateItemSorter.sortLineItems(orderLineRank, getLineItems());
    }

    public List<InvoiceTemplatePlan> getPlans() {
        Map<String, InvoiceTemplatePlan> plans = new HashMap<>();

        getLineItems()
            .forEach(item -> {
                PlanJson planJson = item.getPlan();
                String planId = planJson.getId();
                InvoiceTemplatePlan plan = plans.getOrDefault(
                    planId,
                    new InvoiceTemplatePlan(
                        planJson.getId(),
                        invoiceDocumentJson.getProductsMap().get(planJson.getProductId()).getName(),
                        planJson.getName(),
                        planJson.getDescription(),
                        formatter,
                        getCurrency()
                    )
                );

                plan.addLineItem(item);
                plans.put(planId, plan);
            });

        return new ArrayList<>(plans.values());
    }

    private InvoiceTemplateMapUsage getInvoiceTemplateMapUsage(InvoiceItemDetail li, String currencyCode) {
        RatingOutput ratingOutput = li.getRatingOutput();
        // if this is not a usage line item or a standard usage without map then we can simply ignore the table
        if (
            ratingOutput == null ||
            MapUtils.isEmpty(ratingOutput.ratedQuantityByAttributeReferences()) ||
            ratingOutput.ratedQuantityByAttributeReferences().containsKey(AttributeReferences.NULL_ATTRIBUTE_REFERENCES_KEY)
        ) {
            return null;
        }

        List<InvoiceTemplateMapUsage.AttributeIdNamePair> headers = ratingOutput
            .ratedQuantityByAttributeReferences()
            .keySet()
            .stream()
            .map(AttributeReferences::getReferencesInOrder)
            .flatMap(Collection::stream)
            .map(AttributeReference::getAttributeDefinitionId)
            .distinct()
            .map(this::fromAttributeReference)
            .sorted(Comparator.comparing(InvoiceTemplateMapUsage.AttributeIdNamePair::attributeName))
            .toList();

        InvoiceTemplateMapUsage.TableHeaderDefinition headerDefinition = new InvoiceTemplateMapUsage.TableHeaderDefinition(headers);

        List<InvoiceTemplateMapUsage.TableRowData> tableRowDataList = ratingOutput
            .ratedQuantityByAttributeReferences()
            .entrySet()
            .stream()
            .map(entry -> toTableRowData(currencyCode, headers, entry))
            .toList();

        Long totalQuantity = ratingOutput
            .ratedQuantityByAttributeReferences()
            .values()
            .stream()
            .map(RatedQuantity::getQuantity)
            .reduce(0L, Long::sum);
        BigDecimal totalAmount = Numbers.makeCurrencyScale(
            ratingOutput
                .ratedQuantityByAttributeReferences()
                .values()
                .stream()
                .map(RatedQuantity::getRatedValue)
                .reduce(BigDecimal.ZERO, BigDecimal::add),
            currencyCode
        );

        // now we create the rendering object
        return new InvoiceTemplateMapUsage(
            headerDefinition,
            tableRowDataList,
            NumberFormatter.formatQuantityValue(BigDecimal.valueOf(totalQuantity)),
            formatter.currencyFormat(totalAmount, currencyCode)
        );
    }

    private InvoiceTemplateMapUsage.TableRowData toTableRowData(
        String currencyCode,
        List<InvoiceTemplateMapUsage.AttributeIdNamePair> headers,
        Map.Entry<AttributeReferences, RatedQuantity> entry
    ) {
        Map<String, String> references = entry
            .getKey()
            .getReferencesInOrder()
            .stream()
            .collect(Collectors.toMap(AttributeReference::getAttributeDefinitionId, AttributeReference::getAttributeValue));
        RatedQuantity ratedQuantity = entry.getValue();

        List<String> attributeValues = headers
            .stream()
            .map(idNamePair -> references.getOrDefault(idNamePair.attributeId(), StringUtils.EMPTY))
            .toList();

        return new InvoiceTemplateMapUsage.TableRowData(
            attributeValues,
            NumberFormatter.formatQuantityValue(BigDecimal.valueOf(ratedQuantity.getQuantity())),
            formatter.unitPriceCurrencyFormat(ratedQuantity.getRatedValue(), currencyCode)
        );
    }

    private InvoiceTemplateMapUsage.AttributeIdNamePair fromAttributeReference(String attributeDefinitionId) {
        String attributeName = Optional.ofNullable(invoiceDocumentJson.getPriceAttributeMap().getOrDefault(attributeDefinitionId, null))
            .map(PriceAttribute::getName)
            .orElse(StringUtils.EMPTY);
        return new InvoiceTemplateMapUsage.AttributeIdNamePair(attributeDefinitionId, attributeName);
    }

    private static String subscriptionChargeIdAndPeriodString(String invoiceNumber, InvoiceItemDetail lineItem) {
        String chargeGroupId = Optional.ofNullable(lineItem.getSubscriptionChargeGroupId()).orElse(lineItem.getSubscriptionChargeId());
        if (StringUtils.isEmpty(chargeGroupId)) {
            throw new InvariantCheckFailedException(
                String.format(
                    "No subscription charge and group id. Invoice number: %s, Line Number %s",
                    invoiceNumber,
                    lineItem.getInvoiceLineNumber()
                )
            );
        }
        return String.format(CHARGE_ID_PERIOD_STRING, chargeGroupId, lineItem.getPeriodStartDate(), lineItem.getPeriodEndDate());
    }

    private static String chargeIdAndPeriodString(InvoiceItemDetail lineItem) {
        return String.format(CHARGE_ID_PERIOD_STRING, lineItem.getCharge().getId(), lineItem.getPeriodStartDate(), lineItem.getPeriodEndDate());
    }

    private static InvoiceItem addAmountAndQuantityForLineItem(InvoiceItem firstLineItem, InvoiceItem secondLineItem) {
        InvoiceItem.InvoiceItemBuilder builder = new InvoiceItem.InvoiceItemBuilder().from(firstLineItem);

        builder.amount(
            Objects.requireNonNullElse(firstLineItem.getAmount(), BigDecimal.ZERO).add(
                Objects.requireNonNullElse(secondLineItem.getAmount(), BigDecimal.ZERO)
            )
        );
        builder.listAmount(
            Objects.requireNonNullElse(firstLineItem.getListAmount(), BigDecimal.ZERO).add(
                Objects.requireNonNullElse(secondLineItem.getListAmount(), BigDecimal.ZERO)
            )
        );
        builder.taxAmount(
            Objects.requireNonNullElse(firstLineItem.getTaxAmount(), BigDecimal.ZERO).add(
                Objects.requireNonNullElse(secondLineItem.getTaxAmount(), BigDecimal.ZERO)
            )
        );
        builder.discountAmount(
            Objects.requireNonNullElse(firstLineItem.getDiscountAmount(), BigDecimal.ZERO).add(
                Objects.requireNonNullElse(secondLineItem.getDiscountAmount(), BigDecimal.ZERO)
            )
        );
        builder.quantity(firstLineItem.getQuantity() + secondLineItem.getQuantity());

        builder.drawdownQuantityUsed(
            Objects.requireNonNullElse(firstLineItem.getDrawdownQuantityUsed(), 0L) +
            Objects.requireNonNullElse(secondLineItem.getDrawdownQuantityUsed(), 0L)
        );
        return builder.createInvoiceItem();
    }

    private void populateSharedDates() {
        List<InvoiceItemDetail> lineItems = invoiceDocumentJson.getInvoiceDetail().getInvoiceItems();
        if (CollectionUtils.isEmpty(lineItems)) {
            return;
        }
        var startDateStream = lineItems.stream().map(InvoiceItemDetail::getPeriodStartDate);
        var uniqueStartDatesCount = startDateStream.distinct().count();
        var endDateStream = lineItems.stream().map(InvoiceItemDetail::getPeriodEndDate);
        var uniqueEndDatesCount = endDateStream.distinct().count();
        if (uniqueStartDatesCount == 1 && uniqueEndDatesCount == 1) {
            sharedStartDate = lineItems.get(0).getPeriodStartDate();
            sharedEndDate = lineItems.get(0).getPeriodEndDate();
        }
    }

    public AccountAddressJson getTenantAddress() {
        return invoiceDocumentJson.getTenantInfo().getAddress();
    }

    public String getTenantName() {
        return invoiceDocumentJson.getTenantInfo().getName();
    }

    public Optional<String> getTenantId() {
        return Optional.ofNullable(invoiceDocumentJson).map(InvoiceDocumentJson::getTenantInfo).map(TenantInfo::getTenantId);
    }

    public String getTenantEmail() {
        return invoiceDocumentJson.getTenantInfo().getEmail();
    }

    public String getTenantPhoneNumber() {
        return formatter.phoneNumberFormat(invoiceDocumentJson.getTenantInfo().getPhoneNumber());
    }

    public boolean getIsDraft() {
        if (invoiceDocumentJson.getInvoiceDetail().getStatus() == null) {
            return false;
        }

        return invoiceDocumentJson.getInvoiceDetail().getStatus() == InvoiceStatus.DRAFT;
    }

    public boolean getIsVoided() {
        return invoiceDocumentJson.getInvoiceDetail().getStatus() == InvoiceStatus.VOIDED;
    }

    public boolean getIsPaid() {
        return invoiceDocumentJson.getInvoiceDetail().getStatus() == InvoiceStatus.PAID;
    }

    public String getInvoiceNumber() {
        return invoiceDocumentJson.getInvoiceDetail().getInvoiceNumber();
    }

    public TemplateAccount getAccount() {
        return invoiceDocumentJson.getTemplateAccount();
    }

    public Optional<TemplateAccount> getResellerAccount() {
        return invoiceDocumentJson.getResellerTemplateAccount();
    }

    public AccountContactJson getShippingContact() {
        return invoiceDocumentJson.getInvoiceDetail().getShippingContact();
    }

    public AccountContactJson getBillingContact() {
        return invoiceDocumentJson.getInvoiceDetail().getBillingContact();
    }

    public AccountAddressJson getShippingAddress() {
        return getShippingContact() == null ? null : getShippingContact().getAddress();
    }

    public AccountAddressJson getBillingAddress() {
        return getBillingContact() == null ? null : getBillingContact().getAddress();
    }

    public String getBillingContactPhoneNumber() {
        return formatter.phoneNumberFormat(getBillingContact().getPhoneNumber());
    }

    public String getShippingContactPhoneNumber() {
        return formatter.phoneNumberFormat(getShippingContact().getPhoneNumber());
    }

    public List<InvoiceTemplateLineItem> getLineItems() {
        if (lineItems == null) {
            lineItems = initializeLineItems();
        }
        return getSortedLineItems(lineItems);
    }

    public String getAmountDue() {
        return formatter.currencyFormat(invoiceDocumentJson.getAmountDue(), getCurrency());
    }

    public String getDueDate() {
        return formatter.dateFormat(invoiceDocumentJson.getInvoiceDetail().getDueDate());
    }

    public String getPostedDate() {
        return formatter.dateFormat(invoiceDocumentJson.getInvoiceDetail().getPostedDate());
    }

    public String getInvoiceDate() {
        return formatter.dateFormat(invoiceDocumentJson.getInvoiceDetail().getInvoiceDate());
    }

    public String getSubtotal() {
        return formatter.currencyFormat(invoiceDocumentJson.getInvoiceDetail().getSubTotal(), getCurrency());
    }

    public String getTaxAmount() {
        return formatter.currencyFormat(invoiceDocumentJson.getInvoiceDetail().getTaxTotal(), getCurrency());
    }

    public String getTotal() {
        return formatter.currencyFormat(invoiceDocumentJson.getInvoiceDetail().getTotal(), getCurrency());
    }

    public String getTotalDiscount() {
        return formatter.currencyFormat(invoiceDocumentJson.getInvoiceDetail().getTotalDiscount(), getCurrency());
    }

    public String getSharedStartDate() {
        return formatter.dateFormat(sharedStartDate);
    }

    public String getSharedEndDate() {
        return formatter.endDateFormat(sharedEndDate);
    }

    public String getSubscriptionStartDate() {
        return formatter.dateFormat(invoiceDocumentJson.getSubscriptionStartDate());
    }

    public String getSubscriptionEndDate() {
        return formatter.endDateFormat(invoiceDocumentJson.getSubscriptionEndDate());
    }

    public Map<String, TemplateCustomField> getSubscriptionCustomFields() {
        return TemplateCustomField.getCustomFieldsMap(invoiceDocumentJson.getSubscriptionCustomField());
    }

    public String getPaymentTerm() {
        return new PaymentTerm(invoiceDocumentJson.getInvoiceDetail().getPaymentTerm()).getDisplayName();
    }

    public void setCustomContent(String customContent) {
        this.customContent = customContent;
    }

    public String getCustomContent() {
        return customContent;
    }

    public String getBase64EncodedLogo() {
        return LogoEncoder.getBase64EncodedLogo(invoiceDocumentJson.getLogoContent().orElse(null));
    }

    public String getPaymentLink() {
        return invoiceDocumentJson.getPaymentLink();
    }

    public String getPurchaseOrderNumber() {
        return invoiceDocumentJson.getInvoiceDetail().getPurchaseOrderNumber();
    }

    public String getStatus() {
        return formatter.formatEnum(invoiceDocumentJson.getInvoiceDetail().getStatus());
    }

    public String getCurrency() {
        return Objects.requireNonNullElse(currencyCode, StringUtils.EMPTY);
    }

    public boolean getHasTax() {
        return invoiceDocumentJson.getInvoiceDetail().getTaxTotal().compareTo(BigDecimal.ZERO) > 0;
    }

    public String getAccountReceivableName() {
        return invoiceDocumentJson.getAccountReceivableContact().getFullName();
    }

    public String getAccountReceivableEmail() {
        return invoiceDocumentJson.getAccountReceivableContact().getEmail();
    }

    public String getNote() {
        return invoiceDocumentJson.getInvoiceDetail().getNote();
    }

    public String getPreferredPaymentType() {
        Set<PaymentType> supportedPaymentTypes = invoiceDocumentJson.getInvoiceDetail().getCustomerAccount().getSupportedPaymentTypes();
        if (CollectionUtils.isEmpty(supportedPaymentTypes)) {
            return StringUtils.EMPTY;
        }
        return supportedPaymentTypes.stream().sorted().map(PaymentType::getDisplayName).collect(Collectors.joining(", "));
    }

    public boolean isAutoRenew() {
        return invoiceDocumentJson.isAutoRenew();
    }

    public String getBillingCycle() {
        RecurrenceJson billingCycle = invoiceDocumentJson.getBillingCycle();
        return formatter.formatBillingCycle(billingCycle);
    }

    public String getTermLength() {
        RecurrenceJson termLength = invoiceDocumentJson.getTermLength();
        if (Objects.isNull(termLength)) {
            return StringUtils.EMPTY;
        }
        String pluralName = termLength.getCycle().getPluralName();
        String singularName = termLength.getCycle().getSingularName();
        return String.format("%s %s", termLength.getStep(), termLength.getStep() > 1 ? pluralName : singularName);
    }

    public List<SettlementApplicationTemplateData> getSettlementApplications() {
        return settleApplicationsData;
    }

    public String getTotalPaymentAmount() {
        BigDecimal totalPaymentAmount = getNetPaymentAmount();
        return formatter.currencyFormat(totalPaymentAmount, getCurrency());
    }

    public String getTotalCreditAmount() {
        BigDecimal totalCreditMemoAmount = getTotalAmountByType(SettlementApplicationType.CREDIT);
        BigDecimal totalUnappliedCreditMemoAmount = getTotalAmountByType(SettlementApplicationType.UNAPPLY_CREDIT);

        BigDecimal netCreditMemoAmount = totalCreditMemoAmount.add(totalUnappliedCreditMemoAmount);
        return formatter.currencyFormat(netCreditMemoAmount, getCurrency());
    }

    public boolean hasPayments() {
        return getNetPaymentAmount().compareTo(BigDecimal.ZERO) != 0;
    }

    public boolean hasCreditMemos() {
        return getTotalAmountByType(SettlementApplicationType.CREDIT).compareTo(BigDecimal.ZERO) != 0;
    }

    public List<SettlementApplicationTemplateData> getPayments() {
        return settleApplicationsData
            .stream()
            .filter(
                settlementApplication ->
                    settlementApplication.getRawApplicationType() == SettlementApplicationType.PAYMENT ||
                    settlementApplication.getRawApplicationType() == SettlementApplicationType.VOID_PAYMENT
            )
            .sorted(Comparator.comparing(SettlementApplicationTemplateData::getRawAppliedOn))
            .toList();
    }

    public List<SettlementApplicationTemplateData> getCreditMemos() {
        return settleApplicationsData
            .stream()
            .filter(settlementApplication -> settlementApplication.getRawApplicationType() == SettlementApplicationType.CREDIT)
            .sorted(Comparator.comparing(SettlementApplicationTemplateData::getRawAppliedOn))
            .toList();
    }

    // return a list of order Ids the invoice items are invoicing for. Parameter passed in is used as delimiter between the Ids. If nothing is passed in a default is used
    // Example usage: {{#orderIds}},{{/orderIds}} returns: ORD-123,ORD-456
    public Function<String, String> getOrderIds() {
        return input -> {
            String delimiter = StringUtils.isBlank(input) ? "," : input;

            List<String> orderIds = invoiceDocumentJson.getOrderIds();
            if (CollectionUtils.isEmpty(orderIds)) {
                return StringUtils.EMPTY;
            }

            return String.join(delimiter, orderIds);
        };
    }

    public Map<String, TemplateCustomField> getCustomFields() {
        return TemplateCustomField.getCustomFieldsMap(invoiceDocumentJson.getCustomField());
    }

    private BigDecimal getNetPaymentAmount() {
        BigDecimal totalPaymentAmount = getTotalAmountByType(SettlementApplicationType.PAYMENT);
        BigDecimal totalVoidPaymentAmount = getTotalAmountByType(SettlementApplicationType.VOID_PAYMENT);

        // note: voided payment amounts are negative, so net amount is the sum of payment and voided payments
        return totalPaymentAmount.add(totalVoidPaymentAmount);
    }

    private BigDecimal getTotalAmountByType(SettlementApplicationType type) {
        return settleApplicationsData
            .stream()
            .filter(settlementApplication -> settlementApplication.getRawApplicationType() == type)
            .map(SettlementApplicationTemplateData::getRawAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public InvoiceDocumentJson getInvoiceDocumentJson() {
        return invoiceDocumentJson;
    }

    public String getTermLengthInYears() {
        BigDecimal periodInYears = getRawTermLengthInYears();
        if (periodInYears == null) {
            return StringUtils.EMPTY;
        }

        return String.format("%s %s", periodInYears.toPlainString(), periodInYears.doubleValue() > 1 ? YEAR.getPluralName() : YEAR.getSingularName());
    }

    private BigDecimal getRawTermLengthInYears() {
        if (sharedStartDate == null || sharedEndDate == null) {
            return null;
        }

        BigDecimal years = Period.toDurationInYears(Instant.ofEpochSecond(sharedStartDate), Instant.ofEpochSecond(sharedEndDate), timeZone);

        return years.stripTrailingZeros();
    }

    public String getTermLengthInMonths() {
        if (sharedStartDate == null || sharedEndDate == null) {
            return null;
        }

        long periodInMonths = Period.toDurationInMonthsFloor(Instant.ofEpochSecond(sharedStartDate), Instant.ofEpochSecond(sharedEndDate), timeZone);
        return formatMonths(periodInMonths);
    }

    private static String formatMonths(long months) {
        return String.format("%d %s", months, months > 1 ? MONTH.getPluralName() : MONTH.getSingularName());
    }
}
