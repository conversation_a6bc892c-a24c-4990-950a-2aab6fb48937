package com.subskribe.billy.invoice.document;

import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.invoice.InvoiceDetail;
import com.subskribe.billy.graphql.invoice.InvoiceDetailsMapper;
import com.subskribe.billy.graphql.invoice.InvoiceItemDetail;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.invoice.InvoiceItemJson;
import com.subskribe.billy.resources.json.invoice.InvoiceJson;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.shared.ImmutableInvoiceTemplateDataJson;
import com.subskribe.billy.shared.InvoiceTemplateDataJson;
import com.subskribe.billy.shared.document.TemplateAccount;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.factory.Mappers;

class InvoiceTemplateDataToJsonMapper {

    private static final InvoiceDetailsMapper INVOICE_DETAIL_MAPPER = Mappers.getMapper(InvoiceDetailsMapper.class);

    static InvoiceTemplateDataJson mapInvoiceTemplateDataToJson(InvoiceTemplateData invoiceTemplateData) {
        if (
            invoiceTemplateData == null ||
            invoiceTemplateData.getInvoiceDocumentJson() == null ||
            invoiceTemplateData.getInvoiceDocumentJson().getInvoiceDetail() == null
        ) {
            throw new ServiceFailureException("Invoice detail is not provided.");
        }

        if (invoiceTemplateData.getAccount() == null || invoiceTemplateData.getAccount().getAccountJson() == null) {
            throw new ServiceFailureException("Account is not provided.");
        }

        InvoiceDetail invoiceDetail = invoiceTemplateData.getInvoiceDocumentJson().getInvoiceDetail();

        InvoiceJson invoice = INVOICE_DETAIL_MAPPER.invoiceDetailToInvoiceJson(invoiceDetail);
        AccountJson account = invoiceTemplateData.getAccount().getAccountJson();
        Optional<AccountJson> resellerAccount = invoiceTemplateData.getResellerAccount().map(TemplateAccount::getAccountJson);

        List<InvoiceItemDetail> invoiceItemDetails = invoiceDetail.getInvoiceItems();
        List<InvoiceItemJson> invoiceItemsJson = getInvoiceItems(invoiceItemDetails);

        List<InvoiceTemplateLineItem> invoiceTemplateLineItems = invoiceTemplateData.getLineItems();
        Map<String, PlanJson> planJsonMap = getPlansMap(invoiceTemplateLineItems);
        Map<String, ChargeJson> chargeJsonMap = getChargesMap(invoiceTemplateLineItems);

        return ImmutableInvoiceTemplateDataJson.builder()
            .invoice(invoice)
            .invoiceItems(invoiceItemsJson)
            .account(account)
            .resellerAccount(resellerAccount)
            .planByPlanId(planJsonMap)
            .chargeByChargeId(chargeJsonMap)
            .build();
    }

    private static List<InvoiceItemJson> getInvoiceItems(List<InvoiceItemDetail> invoiceItemDetails) {
        if (CollectionUtils.isEmpty(invoiceItemDetails)) {
            return List.of();
        }

        return INVOICE_DETAIL_MAPPER.invoiceItemDetailsToJson(invoiceItemDetails);
    }

    private static Map<String, PlanJson> getPlansMap(List<InvoiceTemplateLineItem> invoiceTemplateLineItems) {
        if (CollectionUtils.isEmpty(invoiceTemplateLineItems)) {
            return Map.of();
        }

        return invoiceTemplateLineItems
            .stream()
            .map(InvoiceTemplateLineItem::getPlan)
            .collect(Collectors.toMap(PlanJson::getId, Function.identity(), (plan1, plan2) -> plan1));
    }

    private static Map<String, ChargeJson> getChargesMap(List<InvoiceTemplateLineItem> invoiceTemplateLineItems) {
        if (CollectionUtils.isEmpty(invoiceTemplateLineItems)) {
            return Map.of();
        }

        return invoiceTemplateLineItems
            .stream()
            .map(InvoiceTemplateLineItem::getChargeJson)
            .collect(Collectors.toMap(ChargeJson::getId, Function.identity(), (charge1, charge2) -> charge1));
    }
}
