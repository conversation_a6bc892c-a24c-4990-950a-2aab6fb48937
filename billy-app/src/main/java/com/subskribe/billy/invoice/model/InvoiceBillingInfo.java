package com.subskribe.billy.invoice.model;

import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import com.subskribe.billy.shared.temporal.Recurrence;
import java.time.Instant;
import java.util.Currency;
import java.util.TimeZone;
import javax.annotation.Nullable;
import org.immutables.value.Value;

@Value.Immutable
@BillyModelStyle
@Value.Style(stagedBuilder = true)
public interface InvoiceBillingInfo {
    Instant getInvoiceTargetDate();

    Instant getSubscriptionStart();

    Instant getSubscriptionEnd();

    Instant getBillingAnchorDate();

    Currency getCurrency();

    default String getCurrencyCode() {
        return getCurrency().getCurrencyCode();
    }

    TimeZone getTimeZone();

    ProrationConfig getProrationConfig();

    Recurrence getBillingCycle();

    @Nullable
    BillingTerm getBillingTerm();
}
