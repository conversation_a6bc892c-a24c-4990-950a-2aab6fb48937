package com.subskribe.billy.invoice.service;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.LockAcquisitionFailureException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.invoice.ProrationCalculator;
import com.subskribe.billy.invoice.RoundedAmountCalculator;
import com.subskribe.billy.invoice.model.BillingSchedule;
import com.subskribe.billy.invoice.model.BillingScheduleEntry;
import com.subskribe.billy.invoice.model.ImmutableMemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.ItemAmounts;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.order.model.CustomBillingPeriod;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.postgres.PostgresAdvisoryLock;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;

@SuppressWarnings("PMD.LooseCoupling")
public class BillingScheduleService {

    private static final String ADVISORY_LOCK_KEY = "%s/billingSchedule/%s";

    // order line actions that adds a line to an order
    private static final Set<ActionType> ORDER_ADD_ACTIONS = Set.of(ActionType.ADD, ActionType.RENEWAL, ActionType.RESTRUCTURE);
    private static final Logger LOGGER = LoggerFactory.getLogger(BillingScheduleService.class);

    private final OrderGetService orderGetService;

    private final SubscriptionGetService subscriptionGetService;

    private final ProductCatalogGetService productCatalogGetService;

    private final TenantSettingService tenantSettingService;

    private final InvoiceService invoiceService;

    private final InvoiceRetrievalService invoiceRetrievalService;

    private final ProrationConfigurationGetService prorationConfigurationGetService;

    private final DSLContextProvider dslContextProvider;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public BillingScheduleService(
        OrderGetService orderGetService,
        SubscriptionGetService subscriptionGetService,
        ProductCatalogGetService productCatalogGetService,
        TenantSettingService tenantSettingService,
        InvoiceService invoiceService,
        InvoiceRetrievalService invoiceRetrievalService,
        ProrationConfigurationGetService prorationConfigurationGetService,
        DSLContextProvider dslContextProvider,
        TenantIdProvider tenantIdProvider
    ) {
        this.orderGetService = orderGetService;
        this.subscriptionGetService = subscriptionGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.tenantSettingService = tenantSettingService;
        this.invoiceService = invoiceService;
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.prorationConfigurationGetService = prorationConfigurationGetService;
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
    }

    public BillingSchedule getBillingSchedules(String subscriptionId, String subscriptionChargeId) {
        OrderLineItem orderLineItem = getOrderLineItem(subscriptionId, subscriptionChargeId);
        return getBillingSchedule(orderLineItem);
    }

    private BillingSchedule getBillingSchedule(OrderLineItem orderLineItem) {
        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems = invoiceRetrievalService.getMemoizedInvoiceItems(
            orderLineItem.getOrderId(),
            orderLineItem.getOrderLineId()
        );

        if (memoizedInvoiceLineItems.isEmpty()) {
            return new BillingSchedule(orderLineItem.getOrderId(), orderLineItem.getOrderLineId(), orderLineItem.getAmount(), new ArrayList<>());
        } else {
            List<BillingScheduleEntry> entries = getBillingScheduleEntriesFromMemoizedLineItems(memoizedInvoiceLineItems);
            return new BillingSchedule(orderLineItem.getOrderId(), orderLineItem.getOrderLineId(), orderLineItem.getAmount(), entries);
        }
    }

    private OrderLineItem getOrderLineItem(String subscriptionId, String subscriptionChargeId) {
        SubscriptionCharge subscriptionCharge = getSubscriptionCharge(subscriptionId, subscriptionChargeId);
        return getGetOrderLineFromSubscriptionCharge(subscriptionCharge);
    }

    public BillingScheduleEntry addBillingScheduleEntry(String subscriptionId, String subscriptionChargeId, Instant triggerOn, BigDecimal amount) {
        SubscriptionCharge subscriptionCharge = getSubscriptionCharge(subscriptionId, subscriptionChargeId);
        BillingSchedule billingSchedule = getBillingSchedules(subscriptionId, subscriptionChargeId);
        OrderLineItem orderLineItem = getGetOrderLineFromSubscriptionCharge(subscriptionCharge);
        Order order = orderGetService.getOrderByOrderId(orderLineItem.getOrderId());
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        validateEventInput(triggerOn, amount, orderLineItem, billingSchedule);

        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var savedItem = dslContext.transactionResult(configuration -> {
            // lock modifications to billing schedule
            // note: transaction is only used here to acquire and release advisory lock
            tryAcquireLock(configuration, orderLineItem);

            List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems = invoiceRetrievalService.getMemoizedInvoiceItems(
                orderLineItem.getOrderId(),
                orderLineItem.getOrderLineId()
            );

            Optional<MemoizedInvoiceLineItem> lastEntry = getAndValidateLastEntry(memoizedInvoiceLineItems, triggerOn);
            BigDecimal updatedTotalAmount = getAndValidateAmount(triggerOn, amount, memoizedInvoiceLineItems, orderLineItem);
            Period period = getAndValidateServicePeriod(triggerOn, amount, orderLineItem, lastEntry, prorationConfig, timeZone, updatedTotalAmount);
            ItemAmounts roundedAmounts = getRoundedAmounts(amount, orderLineItem);

            var newLineItem = ImmutableMemoizedInvoiceLineItem.builder()
                .entityId(orderLineItem.getEntityId())
                .subscriptionId(subscriptionId)
                .chargeId(orderLineItem.getChargeId())
                .subscriptionChargeId(subscriptionChargeId)
                .subscriptionChargeGroupId(subscriptionCharge.getSubscriptionChargeGroupId())
                .orderId(orderLineItem.getOrderId())
                .orderLineItemId(orderLineItem.getOrderLineId())
                .listAmount(roundedAmounts.listAmount())
                .discountAmount(roundedAmounts.discountAmount())
                .amount(roundedAmounts.sellAmount())
                .listUnitPrice(orderLineItem.getListUnitPrice())
                .sellUnitPrice(orderLineItem.getSellUnitPrice())
                .quantity(orderLineItem.getQuantity())
                .periodStartDate(period.getStart())
                .periodEndDate(period.getEnd())
                .triggerOn(triggerOn)
                .build();

            return invoiceService.memoizeInvoiceLineItem(orderLineItem.getOrderId(), orderLineItem.getOrderLineId(), newLineItem);
        });

        return new BillingScheduleEntry(
            savedItem.getId(),
            savedItem.getPeriodStartDate(),
            savedItem.getPeriodEndDate(),
            savedItem.getTriggerOn(),
            savedItem.getAmount(),
            savedItem.getCreatedOn()
        );
    }

    private static ItemAmounts getRoundedAmounts(BigDecimal amount, OrderLineItem orderLineItem) {
        // todo: should these be consolidated in DiscountCalculator / InvoiceService?
        BigDecimal totalDiscountPercent = DiscountCalculator.calculateDiscountRatio(orderLineItem.getAmount(), orderLineItem.getListAmount());
        BigDecimal listAmount = Numbers.scaledDivide(amount, BigDecimal.ONE.subtract(totalDiscountPercent));
        return RoundedAmountCalculator.getRoundedAmounts(listAmount, amount);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private static Period getAndValidateServicePeriod(
        Instant triggerOn,
        BigDecimal amount,
        OrderLineItem orderLineItem,
        Optional<MemoizedInvoiceLineItem> lastEntry,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        BigDecimal updatedTotalAmount
    ) {
        BigDecimal orderLineItemAmount = orderLineItem.getAmount();

        // if there is a previous entry, period start is equal to last entry period end.
        // If not and this is the first entry, period start is equal to order line item effective date
        Instant periodStart = lastEntry.map(MemoizedInvoiceLineItem::getPeriodEndDate).orElse(orderLineItem.getEffectiveDate());

        Period period = ProrationCalculator.calculateServicePeriod(
            Period.between(orderLineItem.getEffectiveDate(), orderLineItem.getEndDate()),
            periodStart,
            amount,
            orderLineItemAmount,
            prorationConfig,
            timeZone
        );

        if (
            period.getEnd().getEpochSecond() == orderLineItem.getEndDate().getEpochSecond() && updatedTotalAmount.compareTo(orderLineItemAmount) != 0
        ) {
            // if computed period end is at end of order line, all amounts should be accounted for
            String message = String.format(
                "Computed service end is equal to order line end date but total amount remaining to invoice is not 0. Order Id: %s, Order Line Item Id: %s, triggerOn: %s, amount: %s",
                orderLineItem.getOrderId(),
                orderLineItem.getOrderLineId(),
                triggerOn,
                amount
            );

            LOGGER.warn(message);
        }

        // if the entire order amount has been accounted for, period end is equal to order line end. Otherwise, use calculated period end
        Instant periodEnd = (updatedTotalAmount.compareTo(orderLineItemAmount) == 0) ? orderLineItem.getEndDate() : period.getEnd();

        return Period.between(period.getStart(), periodEnd);
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private static Period getAndValidateServicePeriodForCustomBilling(
        Instant triggerOn,
        BigDecimal amount,
        OrderLineItem orderLineItem,
        Optional<Instant> lastEntryPeriodEndDate,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        BigDecimal updatedTotalAmount
    ) {
        BigDecimal orderLineItemAmount = orderLineItem.getAmount();

        // if there is a previous entry, period start is equal to last entry period end.
        // If not and this is the first entry, period start is equal to order line item effective date
        Instant periodStart = lastEntryPeriodEndDate.orElse(orderLineItem.getEffectiveDate());

        Period period = ProrationCalculator.calculateServicePeriod(
            Period.between(orderLineItem.getEffectiveDate(), orderLineItem.getEndDate()),
            periodStart,
            amount,
            orderLineItemAmount,
            prorationConfig,
            timeZone
        );

        if (
            period.getEnd().getEpochSecond() == orderLineItem.getEndDate().getEpochSecond() && updatedTotalAmount.compareTo(orderLineItemAmount) != 0
        ) {
            // if computed period end is at end of order line, all amounts should be accounted for
            String message = String.format(
                "Custom Billing - computed service end is equal to order line end date but total amount remaining to invoice is not 0. Order Id: %s, Order Line Item Id: %s, triggerOn: %s, amount: %s",
                orderLineItem.getOrderId(),
                orderLineItem.getOrderLineId(),
                triggerOn,
                amount
            );

            LOGGER.warn(message);
        }

        // if the entire order amount has been accounted for, period end is equal to order line end. Otherwise, use calculated period end
        Instant periodEnd = (updatedTotalAmount.compareTo(orderLineItemAmount) == 0) ? orderLineItem.getEndDate() : period.getEnd();

        return Period.between(period.getStart(), periodEnd);
    }

    private static BigDecimal getAndValidateAmount(
        Instant triggerOn,
        BigDecimal amount,
        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems,
        OrderLineItem orderLineItem
    ) {
        BigDecimal orderLineAmount = orderLineItem.getAmount();
        BigDecimal totalSavedAmount = memoizedInvoiceLineItems
            .stream()
            .map(MemoizedInvoiceLineItem::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal updatedTotalAmount = totalSavedAmount.add(amount);

        if (updatedTotalAmount.abs().compareTo(orderLineAmount.abs()) > 0) {
            throw new InvalidInputException("Total billing event amounts cannot exceed order line item amount");
        }

        if (triggerOn.getEpochSecond() == orderLineItem.getEndDate().getEpochSecond() && updatedTotalAmount.compareTo(orderLineAmount) < 0) {
            // if the event is last possible one (end of order line), then the remaining amount must be accounted for
            throw new InvalidInputException("When scheduled date is equal to charge end date, entire remaining amount must be scheduled");
        }
        return updatedTotalAmount;
    }

    private Optional<MemoizedInvoiceLineItem> getAndValidateLastEntry(List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems, Instant triggerOn) {
        if (memoizedInvoiceLineItems.isEmpty()) {
            return Optional.empty();
        }

        var sortedLineItems = memoizedInvoiceLineItems.stream().sorted(Comparator.comparing(MemoizedInvoiceLineItem::getTriggerOn)).toList();

        Optional<MemoizedInvoiceLineItem> lastEntry = Optional.of(sortedLineItems.get(sortedLineItems.size() - 1));

        if (!triggerOn.isAfter(lastEntry.get().getTriggerOn())) {
            throw new InvalidInputException(
                String.format("new billing event date %s must be after the last billing event date %s", triggerOn, lastEntry.get().getTriggerOn())
            );
        }
        return lastEntry;
    }

    public BillingSchedule deleteBillingSchedule(UUID id) {
        Optional<MemoizedInvoiceLineItem> result = invoiceRetrievalService.getMemoizedInvoiceItem(id);

        MemoizedInvoiceLineItem item = result.orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.BILLING_EVENT_ENTRY, id.toString()));

        // todo: should create a direct link between invoice items and memoized invoice items. This check based on date is fragile and has no FK constraint
        List<InvoiceItem> invoiceItems = invoiceRetrievalService.getAllInvoiceItemsForOrderLineIdsPastThreshold(
            List.of(item.getOrderLineItemId()),
            item.getPeriodStartDate()
        );

        if (!invoiceItems.isEmpty()) {
            throw new InvalidInputException("Cannot delete billing event entry that has already been invoiced");
        }

        OrderLineItem orderLineItem = getOrderLineItem(item.getSubscriptionId(), item.getSubscriptionChargeId());

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        dslContext.transaction(configuration -> {
            // lock modifications to billing schedule
            // note: transaction is only used here to acquire and release advisory lock
            tryAcquireLock(configuration, orderLineItem);

            BillingSchedule schedule = getBillingSchedule(orderLineItem);

            long laterEntriesCount = schedule.entries().stream().filter(entry -> entry.triggerOn().isAfter(item.getTriggerOn())).count();

            if (laterEntriesCount > 0) {
                throw new InvalidInputException(
                    "Billing events must be be deleted in reverse chronological order. Please delete later events first."
                );
            }

            Optional<MemoizedInvoiceLineItem> deleteResult = invoiceService.deleteMemoizedInvoiceItem(id);

            if (deleteResult.isEmpty()) {
                throw new ObjectNotFoundException(BillyObjectType.BILLING_EVENT_ENTRY, id.toString());
            }
        });

        return getBillingSchedules(item.getSubscriptionId(), item.getSubscriptionChargeId());
    }

    /**
     * This method generates invoice items for ad hoc custom billing for a given order.
     * The input to this method is an ad-hoc billing period and the total amount to be billed for that period.
     * The logic is as follows -
     * 1. Find the ratio of the billing period amount to the total amount
     * 2. For each order line item in the order, calculate the amount to be billed for that line item based on the ratio, ratio * order line amount (billingPeriodAmount)
     * ------------------------------------------------
     * 3. If it's a ramp group then consider the entire group amount as a single line first and multiply the ratio * with total amount to get rampGroupAmountToUse
     * 3.1 - then distribute the amount across the ramped lines sorted by effective date using a greedy approach, i.e. sum up till total reaches the rampGroupAmountToUse
     * 3.2 - this is termed as rampOrderLineBillingPeriodAmount which is synonymous to (billingPeriodAmount) in 2.
     */
    public static List<InvoiceItem> generateInvoiceItemsForAdhocCustomBillingPeriod(
        BigDecimal adhocBillingTotalAmount,
        CustomBillingPeriod period,
        boolean isLastSegment,
        String orderId,
        String currencyCode,
        CustomBillingOrderLineAmountGrouping customBillingOrderLineAmountGrouping,
        Map<String, Pair<Instant, BigDecimal>> orderLineUsedServicePeriodAndAmounts,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        boolean forOrderLineCalculation
    ) {
        List<InvoiceItem> invoiceItems = new ArrayList<>();
        BigDecimal billingPeriodAmountRatio = Numbers.scaledDivide(period.getAmount(), adhocBillingTotalAmount);

        LinkedHashMap<String, BigDecimal> weightedDistributionAmounts = isLastSegment
            ? getRemainingOrderLineAmounts(customBillingOrderLineAmountGrouping, orderLineUsedServicePeriodAndAmounts)
            : WeightedDistributionCalculator.getWeightedDistributionAmounts(
                customBillingOrderLineAmountGrouping.mergedAmounts(),
                period.getAmount(),
                currencyCode,
                billingPeriodAmountRatio
            );

        LOGGER.info("generateInvoiceItemsForAdhocCustomBillingPeriod : Weighted distribution amounts: {}", weightedDistributionAmounts);

        generateInvoiceItemsForNonRampedLines(
            period,
            isLastSegment,
            orderLineUsedServicePeriodAndAmounts,
            prorationConfig,
            timeZone,
            customBillingOrderLineAmountGrouping.nonRampLineItems(),
            weightedDistributionAmounts,
            invoiceItems
        );

        generateInvoiceItemsForRampedLines(
            period,
            isLastSegment,
            orderLineUsedServicePeriodAndAmounts,
            prorationConfig,
            timeZone,
            customBillingOrderLineAmountGrouping.rampLineItemsGroup(),
            weightedDistributionAmounts,
            invoiceItems
        );

        BigDecimal invoiceItemsTotalAmount = invoiceItems.stream().map(InvoiceItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (BooleanUtils.isFalse(forOrderLineCalculation) && period.getAmount().compareTo(invoiceItemsTotalAmount) != 0) {
            throw new InvariantCheckFailedException(
                String.format(
                    "Order %s, period %s, billingPeriodAmountRatio %s, the total amount of invoice items should be equal to the billing period amount. Billing Period Amount: %s, Invoice Items Total Amount: %s",
                    orderId,
                    period,
                    billingPeriodAmountRatio,
                    period.getAmount(),
                    invoiceItemsTotalAmount
                )
            );
        }
        return invoiceItems;
    }

    public static CustomBillingOrderLineAmountGrouping getOrderLineAmountGrouping(Map<String, OrderLineItem> orderLineItemMap) {
        // Find out order lines which are not ramped
        List<OrderLineItem> nonRampLineItems = orderLineItemMap.values().stream().filter(orderLineItem -> !orderLineItem.getIsRamp()).toList();

        LinkedHashMap<String, BigDecimal> nonRampLineItemAmounts = nonRampLineItems
            .stream()
            .map(orderLineItem -> Map.entry(orderLineItem.getOrderLineId(), orderLineItem.getAmount()))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> a, LinkedHashMap::new));

        LinkedHashMap<String, List<OrderLineItem>> rampLineItemsGroup = orderLineItemMap
            .values()
            .stream()
            .filter(OrderLineItem::getIsRamp)
            .collect(Collectors.groupingBy(lineItem -> lineItem.getRampGroupId().toString(), LinkedHashMap::new, Collectors.toList()));

        LinkedHashMap<String, BigDecimal> rampGroupAmounts = rampLineItemsGroup
            .entrySet()
            .stream()
            .map(entry -> Map.entry(entry.getKey(), entry.getValue().stream().map(OrderLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> a, LinkedHashMap::new));

        LinkedHashMap<String, BigDecimal> mergedAmounts = new LinkedHashMap<>(nonRampLineItemAmounts);
        mergedAmounts.putAll(rampGroupAmounts);
        return new CustomBillingOrderLineAmountGrouping(nonRampLineItems, rampLineItemsGroup, mergedAmounts);
    }

    /**
     * For the last custom billing period segment we find the amount to be billed by subtract the amount already billed for the order line from the order line amount,
     * this is to ensure that the invoice line amounts is equal to the order line amount
     * @param customBillingOrderLineAmountGrouping
     * @param orderLineUsedServicePeriodAndAmounts
     * @return a map of order line ids and the remaining amount to be billed for that order line
     */
    private static LinkedHashMap<String, BigDecimal> getRemainingOrderLineAmounts(
        CustomBillingOrderLineAmountGrouping customBillingOrderLineAmountGrouping,
        Map<String, Pair<Instant, BigDecimal>> orderLineUsedServicePeriodAndAmounts
    ) {
        LinkedHashMap<String, BigDecimal> remainingOrderLineAmounts = new LinkedHashMap<>();
        customBillingOrderLineAmountGrouping
            .mergedAmounts()
            .forEach((orderLineId, amount) -> {
                // For ramp groups we need to find the remaining amount for the entire group to be billed in the last segment, we do this by checking used amounts of each ramped line in the group
                var rampLineItemsGroup = customBillingOrderLineAmountGrouping.rampLineItemsGroup();
                if (rampLineItemsGroup.containsKey(orderLineId)) {
                    List<OrderLineItem> rampLineItems = rampLineItemsGroup.get(orderLineId);
                    BigDecimal rampGroupUsedAmount = BigDecimal.ZERO;
                    for (OrderLineItem rampLineItem : rampLineItems) {
                        Pair<Instant, BigDecimal> usedServicePeriodAndAmount = orderLineUsedServicePeriodAndAmounts.getOrDefault(
                            rampLineItem.getOrderLineId(),
                            Pair.of(rampLineItem.getEffectiveDate(), BigDecimal.ZERO)
                        );
                        rampGroupUsedAmount = rampGroupUsedAmount.add(usedServicePeriodAndAmount.getRight());
                    }
                    remainingOrderLineAmounts.put(orderLineId, amount.subtract(rampGroupUsedAmount));
                }
                // For non ramped lines we just need to check individually
                else {
                    OrderLineItem orderLineItem = customBillingOrderLineAmountGrouping
                        .nonRampLineItems()
                        .stream()
                        .filter(item -> item.getOrderLineId().equals(orderLineId))
                        .findFirst()
                        .orElseThrow();
                    Pair<Instant, BigDecimal> usedServicePeriodAndAmount = orderLineUsedServicePeriodAndAmounts.getOrDefault(
                        orderLineId,
                        Pair.of(orderLineItem.getEffectiveDate(), BigDecimal.ZERO)
                    ); // In case there is only 1 segment, nothing is used from the order line
                    BigDecimal remainingAmount = amount.subtract(usedServicePeriodAndAmount.getRight());
                    remainingOrderLineAmounts.put(orderLineId, remainingAmount);
                }
            });
        return remainingOrderLineAmounts;
    }

    private static void generateInvoiceItemsForNonRampedLines(
        CustomBillingPeriod period,
        boolean isLastSegment,
        Map<String, Pair<Instant, BigDecimal>> orderLineUsedServicePeriodAndAmounts,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        List<OrderLineItem> nonRampLineItems,
        LinkedHashMap<String, BigDecimal> weightedDistributionAmounts,
        List<InvoiceItem> invoiceItems
    ) {
        // Spread out the custom billing period amounts across each line item based on ratio of period amount to total amount
        nonRampLineItems.forEach(orderLineItem -> {
            BigDecimal billingPeriodAmount = weightedDistributionAmounts.get(orderLineItem.getOrderLineId());

            // This is tracking the amount used up for the given order line item across different ad hoc billing periods, because we need to spread out the overall amount across them
            Pair<Instant, BigDecimal> usedServicePeriodAndAmount = orderLineUsedServicePeriodAndAmounts.getOrDefault(
                orderLineItem.getOrderLineId(),
                Pair.of(orderLineItem.getEffectiveDate(), BigDecimal.ZERO)
            );

            BigDecimal orderLineLeftAmount = orderLineItem.getAmount().subtract(usedServicePeriodAndAmount.getRight());
            if (isLastSegment && billingPeriodAmount.compareTo(orderLineLeftAmount) != 0) {
                throw new InvariantCheckFailedException(
                    String.format(
                        "For last segment, the non ramped line billing period amount should be equal to the remaining amount to be billed for the line. Order Line Id: %s, Order Line Amount: %s, Order Line Left Amount: %s, Billing Period Amount: %s",
                        orderLineItem.getOrderLineId(),
                        orderLineItem.getAmount(),
                        orderLineLeftAmount,
                        billingPeriodAmount
                    )
                );
            }

            CustomBillingOrderLineUsageCalculation customBillingOrderLineUsageCalculation = new CustomBillingOrderLineUsageCalculation(
                orderLineUsedServicePeriodAndAmounts,
                orderLineItem,
                usedServicePeriodAndAmount,
                billingPeriodAmount
            );

            invoiceItems.add(getServicePeriodAndInvoiceItem(period, customBillingOrderLineUsageCalculation, prorationConfig, timeZone));
        });
    }

    private static void generateInvoiceItemsForRampedLines(
        CustomBillingPeriod period,
        boolean isLastSegment,
        Map<String, Pair<Instant, BigDecimal>> orderLineUsedServicePeriodAndAmounts,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        Map<String, List<OrderLineItem>> rampLineItemsGroup,
        LinkedHashMap<String, BigDecimal> weightedDistributionAmounts,
        List<InvoiceItem> invoiceItems
    ) {
        // Send all the non ramped lines and ramp groups together to the function so that we can validate that the distribution doesn't exceed the total segment amount

        // Basically treat each ramp group as a single line item initially to get the ratio, similar to nonRampedLines but then distribute the amount across the ramped lines
        rampLineItemsGroup.forEach((rampGroupId, rampLineItems) -> {
            //IMPORTANT TO SORT THE LINES BY EFFECTIVE DATE AS WE ARE USING A GREEDY APPROACH TO SPREAD THE AMOUNT
            rampLineItems.sort(Comparator.comparing(OrderLineItem::getEffectiveDate));

            BigDecimal rampGroupBillingPeriodAmount = weightedDistributionAmounts.get(rampGroupId);
            BigDecimal rampGroupUsedUpAmount = BigDecimal.ZERO;

            LinkedHashMap<String, BigDecimal> rampOrderLineAmounts = rampLineItems
                .stream()
                .collect(Collectors.toMap(OrderLineItem::getOrderLineId, OrderLineItem::getAmount, (a, b) -> a, LinkedHashMap::new));

            // Spread the rampGroupBillingPeriodAmount across each ramped line in a greedy manner by picking ramped lines in order of effective dates
            for (OrderLineItem rampOrderLine : rampLineItems) {
                // This is tracking the amount used up for the given order line item across different ad hoc billing periods, because we need to spread out the overall amount across them
                Pair<Instant, BigDecimal> usedServicePeriodAndAmount = orderLineUsedServicePeriodAndAmounts.getOrDefault(
                    rampOrderLine.getOrderLineId(),
                    Pair.of(rampOrderLine.getEffectiveDate(), BigDecimal.ZERO)
                );

                Optional<BigDecimal> rampOrderLineBillingPeriodAmount = getRampOrderLineBillingPeriodAmount(
                    isLastSegment,
                    rampGroupId,
                    rampOrderLine,
                    rampOrderLineAmounts,
                    usedServicePeriodAndAmount,
                    rampGroupBillingPeriodAmount,
                    rampGroupUsedUpAmount
                );
                // This ramped line is fully consumed
                if (rampOrderLineBillingPeriodAmount.isEmpty()) continue;

                CustomBillingOrderLineUsageCalculation customBillingOrderLineUsageCalculation = new CustomBillingOrderLineUsageCalculation(
                    orderLineUsedServicePeriodAndAmounts,
                    rampOrderLine,
                    usedServicePeriodAndAmount,
                    rampOrderLineBillingPeriodAmount.get()
                );

                invoiceItems.add(getServicePeriodAndInvoiceItem(period, customBillingOrderLineUsageCalculation, prorationConfig, timeZone));
                rampGroupUsedUpAmount = rampGroupUsedUpAmount.add(rampOrderLineBillingPeriodAmount.get());

                // The ramp group is fully consumed for the given ad hoc billing period
                if (Numbers.makeCurrencyScale(rampGroupUsedUpAmount).compareTo(Numbers.makeCurrencyScale(rampGroupBillingPeriodAmount)) == 0) {
                    break;
                }
            }
        });
    }

    private static Optional<BigDecimal> getRampOrderLineBillingPeriodAmount(
        boolean isLastSegment,
        String rampGroupId,
        OrderLineItem rampOrderLine,
        LinkedHashMap<String, BigDecimal> rampOrderLineAmounts,
        Pair<Instant, BigDecimal> usedServicePeriodAndAmount,
        BigDecimal rampGroupBillingPeriodAmount,
        BigDecimal rampGroupUsedUpAmount
    ) {
        BigDecimal rampOrderLineLeftAmount = rampOrderLineAmounts.get(rampOrderLine.getOrderLineId()).subtract(usedServicePeriodAndAmount.getRight());

        // This ramped line is fully consumed
        if (Numbers.isZero(rampOrderLineLeftAmount)) {
            return Optional.empty();
        }

        BigDecimal rampGroupAmountToUse = rampGroupBillingPeriodAmount.subtract(rampGroupUsedUpAmount);
        BigDecimal rampOrderLineBillingPeriodAmount = rampOrderLineLeftAmount.compareTo(rampGroupAmountToUse) <= 0
            ? rampOrderLineLeftAmount
            : rampGroupAmountToUse;

        if (isLastSegment && rampOrderLineBillingPeriodAmount.compareTo(rampOrderLineLeftAmount) != 0) {
            throw new InvariantCheckFailedException(
                String.format(
                    "For last segment, the ramped line billing period amount should be equal to the remaining amount to be billed for the line. Order Line Id: %s, Ramp Group Id: %s, Ramp Line Id: %s, Ramp Line Amount: %s, Ramp Line Left Amount: %s, Ramp Line Billing Period Amount: %s, Ramp Group Amount To Use: %s",
                    rampOrderLine.getOrderLineId(),
                    rampGroupId,
                    rampOrderLine.getOrderLineId(),
                    rampOrderLine.getAmount(),
                    rampOrderLineLeftAmount,
                    rampOrderLineBillingPeriodAmount,
                    rampGroupAmountToUse
                )
            );
        }
        return Optional.of(rampOrderLineBillingPeriodAmount);
    }

    private static InvoiceItem getServicePeriodAndInvoiceItem(
        CustomBillingPeriod period,
        CustomBillingOrderLineUsageCalculation customBillingOrderLineUsageCalculation,
        ProrationConfig prorationConfig,
        TimeZone timeZone
    ) {
        OrderLineItem rampOrderLine = customBillingOrderLineUsageCalculation.orderLineItem();
        Pair<Instant, BigDecimal> usedServicePeriodAndAmount = customBillingOrderLineUsageCalculation.usedServicePeriodAndAmount();
        BigDecimal orderLineBillingPeriodAmount = customBillingOrderLineUsageCalculation.orderLineBillingPeriodAmount();
        Map<String, Pair<Instant, BigDecimal>> orderLineUsedServicePeriodAndAmounts =
            customBillingOrderLineUsageCalculation.orderLineUsedServicePeriodAndAmounts();

        BigDecimal usedRampOrderLineAmount = usedServicePeriodAndAmount.getRight().add(orderLineBillingPeriodAmount);

        Period servicePeriod = getAndValidateServicePeriodForCustomBilling(
            period.getTriggerInstant(),
            orderLineBillingPeriodAmount,
            rampOrderLine,
            Optional.of(usedServicePeriodAndAmount.getLeft()),
            prorationConfig,
            timeZone,
            usedRampOrderLineAmount
        );

        InvoiceItem invoiceItem = getInvoiceItemForAdhocCustomBilling(
            rampOrderLine,
            period.getTriggerInstant(),
            orderLineBillingPeriodAmount,
            servicePeriod
        );

        orderLineUsedServicePeriodAndAmounts.put(rampOrderLine.getOrderLineId(), Pair.of(servicePeriod.getEnd(), usedRampOrderLineAmount));
        return invoiceItem;
    }

    private static InvoiceItem getInvoiceItemForAdhocCustomBilling(
        OrderLineItem orderLineItem,
        Instant triggerOn,
        BigDecimal amount,
        Period servicePeriod
    ) {
        ItemAmounts roundedAmounts = getRoundedAmounts(amount, orderLineItem);

        return new InvoiceItem.InvoiceItemBuilder()
            .entityId(orderLineItem.getEntityId())
            .orderId(orderLineItem.getOrderId())
            .orderLineItemId(orderLineItem.getOrderLineId())
            .planId(orderLineItem.getPlanId())
            .chargeId(orderLineItem.getChargeId())
            .subscriptionChargeId(orderLineItem.getExternalSubscriptionChargeId())
            .subscriptionChargeGroupId(orderLineItem.getSubscriptionChargeGroupId())
            .status(InvoiceStatus.DRAFT)
            .listAmount(roundedAmounts.listAmount())
            .discountAmount(roundedAmounts.discountAmount())
            .amount(roundedAmounts.sellAmount())
            .listUnitPrice(orderLineItem.getListUnitPrice())
            .sellUnitPrice(orderLineItem.getSellUnitPrice())
            .taxAmount(BigDecimal.ZERO)
            .quantity(orderLineItem.getQuantity())
            .inlineDiscounts(orderLineItem.getDiscounts())
            .predefinedDiscounts(orderLineItem.getPredefinedDiscounts())
            .periodStartDate(servicePeriod.getStart())
            .periodEndDate(servicePeriod.getEnd())
            .triggerOn(triggerOn)
            .createInvoiceItem();
    }

    public static void validateCustomBillingInvoiceGenerationInvariants(
        String orderId,
        List<List<InvoiceItem>> allInvoiceItems,
        CustomBillingOrderLineAmountGrouping customBillingOrderLineAmountGrouping,
        BigDecimal adhocBillingTotalAmount
    ) {
        // Check custom billing total amount with generated invoice items total amount
        List<InvoiceItem> invoiceItems = allInvoiceItems.stream().flatMap(List::stream).toList();
        BigDecimal allInvoiceItemsTotalAmount = invoiceItems.stream().map(InvoiceItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (adhocBillingTotalAmount.compareTo(allInvoiceItemsTotalAmount) != 0) {
            throw new InvariantCheckFailedException(
                String.format(
                    "adhocBillingTotalAmount %s does not match allInvoiceItemsTotalAmount %s, order id: %s",
                    adhocBillingTotalAmount,
                    allInvoiceItemsTotalAmount,
                    orderId
                )
            );
        }

        // ------------------------------------------------
        // Check order line amount with generated invoice lines total, also for ramps
        Map<String, BigDecimal> orderLineIdToInvoiceItemAmount = invoiceItems
            .stream()
            .collect(
                Collectors.groupingBy(InvoiceItem::getOrderLineItemId, Collectors.reducing(BigDecimal.ZERO, InvoiceItem::getAmount, BigDecimal::add))
            );

        // Each ramped order line total amount should be equal to the sum of generated invoice line amounts
        LinkedHashMap<String, List<OrderLineItem>> rampLineItemsGroup = customBillingOrderLineAmountGrouping.rampLineItemsGroup();
        rampLineItemsGroup.forEach((rampGroupId, rampLineItems) -> {
            BigDecimal rampGroupTotalAmount = rampLineItems.stream().map(OrderLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal rampGroupInvoiceItemsTotalAmount = BigDecimal.ZERO;

            for (OrderLineItem rampLineItem : rampLineItems) {
                BigDecimal rampLineItemAmount = rampLineItem.getAmount();
                BigDecimal invoiceItemsAmount = orderLineIdToInvoiceItemAmount.get(rampLineItem.getOrderLineId());
                rampGroupInvoiceItemsTotalAmount = rampGroupInvoiceItemsTotalAmount.add(invoiceItemsAmount);
                if (invoiceItemsAmount.compareTo(rampLineItemAmount) != 0) {
                    throw new InvariantCheckFailedException(
                        String.format(
                            "Order %s, the total amount of invoice items should be equal to the order line amount. Order Line Id: %s, Order Line Amount: %s, Invoice Items Total Amount: %s",
                            orderId,
                            rampLineItem.getOrderLineId(),
                            rampLineItemAmount,
                            invoiceItemsAmount
                        )
                    );
                }
            }

            // The total amount of generated invoice items for group's order lines should be equal to the ramp group amount
            if (rampGroupTotalAmount.compareTo(rampGroupInvoiceItemsTotalAmount) != 0) {
                throw new InvariantCheckFailedException(
                    String.format(
                        "Order %s, the total amount of invoice items should be equal to the ramp group amount. Ramp Group Id: %s, Ramp Group Amount: %s, Invoice Items Total Amount: %s",
                        orderId,
                        rampGroupId,
                        rampGroupTotalAmount,
                        rampGroupInvoiceItemsTotalAmount
                    )
                );
            }
        });

        // Each non ramped order line total amount should be equal to the sum of generated invoice line amounts
        customBillingOrderLineAmountGrouping.mergedAmounts.forEach((orderLineId, orderLineAmount) -> {
            // Skip ramped lines as they are already validated
            if (rampLineItemsGroup.containsKey(orderLineId)) {
                return;
            }
            BigDecimal invoiceItemsAmount = orderLineIdToInvoiceItemAmount.get(orderLineId);
            if (invoiceItemsAmount.compareTo(orderLineAmount) != 0) {
                throw new InvariantCheckFailedException(
                    String.format(
                        "Order %s, the total amount of invoice items should be equal to the order line amount. Order Line Id: %s, Order Line Amount: %s, Invoice Items Total Amount: %s",
                        orderId,
                        orderLineId,
                        orderLineAmount,
                        invoiceItemsAmount
                    )
                );
            }
        });
    }

    // Wrap billing schedule modification operations with advisory lock to prevent concurrent modifications
    // Billing events are sequential and dependent on previous event entries.
    // If events are added / removed while another event for the same order line item is being processed, amounts and service periods can become inconsistent
    private void tryAcquireLock(Configuration configuration, OrderLineItem orderLineItem) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        String lockIdentifier = String.format(ADVISORY_LOCK_KEY, tenantId, orderLineItem.getOrderLineId());

        Optional<Long> lock = PostgresAdvisoryLock.tryAcquireLock(DSL.using(configuration), lockIdentifier);

        if (lock.isPresent()) {
            LOGGER.info("Acquired advisory lock for billing schedule modification {}", lock.get());
        } else {
            LOGGER.warn("Could not acquire advisory lock for billing schedule modification {}", lockIdentifier);
            throw new LockAcquisitionFailureException("Billing schedule being modified, please refresh and try again.");
        }
    }

    private List<BillingScheduleEntry> getBillingScheduleEntriesFromMemoizedLineItems(List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems) {
        return memoizedInvoiceLineItems
            .stream()
            .map(memoizedInvoiceLineItem ->
                new BillingScheduleEntry(
                    memoizedInvoiceLineItem.getId(),
                    memoizedInvoiceLineItem.getPeriodStartDate(),
                    memoizedInvoiceLineItem.getPeriodEndDate(),
                    memoizedInvoiceLineItem.getTriggerOn(),
                    memoizedInvoiceLineItem.getAmount(),
                    memoizedInvoiceLineItem.getCreatedOn()
                )
            )
            .toList();
    }

    void validateEventInput(Instant triggerOn, BigDecimal amount, OrderLineItem orderLineItem, BillingSchedule billingSchedule) {
        if (triggerOn.isBefore(orderLineItem.getEffectiveDate()) || triggerOn.isAfter(orderLineItem.getEndDate())) {
            throw new InvalidInputException("billing event must be between effective date and end date");
        }

        if (Numbers.isZero(amount)) {
            throw new InvalidInputException("billing event amount cannot be zero");
        }

        if (Numbers.isPositive(billingSchedule.remainingAmount()) && Numbers.isNegative(amount)) {
            throw new InvalidInputException("billing event amount must be greater than zero");
        }

        if (Numbers.isNegative(billingSchedule.remainingAmount()) && Numbers.isPositive(amount)) {
            throw new InvalidInputException("billing event amount must be less than zero");
        }

        if (Numbers.getNumberOfDecimals(amount) > 2) {
            throw new InvalidInputException("billing event amount must have at most 2 decimal places");
        }

        Charge charge = productCatalogGetService.getChargeByChargeId(orderLineItem.getChargeId());

        if (!charge.isEventBased()) {
            throw new InvalidInputException("charge must be event based");
        }
    }

    private SubscriptionCharge getSubscriptionCharge(String subscriptionId, String subscriptionChargeId) {
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);

        return subscription
            .getCharges()
            .stream()
            .filter(charge -> charge.getSubscriptionChargeId().equals(subscriptionChargeId))
            .findFirst()
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.SUBSCRIPTION_CHARGE, subscriptionChargeId));
    }

    OrderLineItem getGetOrderLineFromSubscriptionCharge(SubscriptionCharge subscriptionCharge) {
        List<OrderLineItem> orderLineItems = orderGetService.getOrderLineItemsByOrderLineItemIds(subscriptionCharge.getOrderLines());

        String subscriptionChargeIdentifierString = String.format(
            "subscription %s, subscription charge %s",
            subscriptionCharge.getSubscriptionId(),
            subscriptionCharge.getSubscriptionChargeId()
        );

        if (orderLineItems.isEmpty()) {
            // unexpected state. Order line associated with subscription charge not found
            throw new InvariantCheckFailedException(String.format("Order line items not found for %s", subscriptionChargeIdentifierString));
        }

        if (orderLineItems.size() > 2) {
            throw new InvariantCheckFailedException(
                String.format("More than 2 order lines found for event based charge %s", subscriptionChargeIdentifierString)
            );
        }

        List<OrderLineItem> addItems = orderLineItems.stream().filter(item -> ORDER_ADD_ACTIONS.contains(item.getAction())).toList();
        List<OrderLineItem> updateItems = orderLineItems.stream().filter(item -> item.getAction() == ActionType.UPDATE).toList();

        if (addItems.size() != 1 || !updateItems.isEmpty()) {
            throw new InvariantCheckFailedException(
                String.format("Unexpected order line items found for event based charge %s", subscriptionChargeIdentifierString)
            );
        }

        return addItems.get(0);
    }

    @SuppressWarnings("PMD.LooseCoupling")
    public record CustomBillingOrderLineUsageCalculation(
        Map<String, Pair<Instant, BigDecimal>> orderLineUsedServicePeriodAndAmounts,
        OrderLineItem orderLineItem,
        Pair<Instant, BigDecimal> usedServicePeriodAndAmount,
        BigDecimal orderLineBillingPeriodAmount
    ) {}

    @SuppressWarnings("PMD.LooseCoupling")
    public record CustomBillingOrderLineAmountGrouping(
        List<OrderLineItem> nonRampLineItems,
        LinkedHashMap<String, List<OrderLineItem>> rampLineItemsGroup,
        LinkedHashMap<String, BigDecimal> mergedAmounts // here ramp order lines are grouped and treated like a single line with the total amount
    ) {}
}
