package com.subskribe.billy.invoice.service;

import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.invoice.model.ImmutableInvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceItemPreview;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.ListAmount;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.processor.OneTimeInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.RecurringInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.model.ImmutableInvoicePreviewInput;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

public class InvoicePreviewReportService {

    private final OrderGetService orderGetService;

    private final SubscriptionGetService subscriptionGetService;

    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;

    private final ProrationConfigurationGetService prorationConfigurationGetService;

    private final ProductCatalogGetService productCatalogGetService;

    private final RecurringInvoiceProcessor recurringInvoiceProcessor;

    private final OneTimeInvoiceProcessor oneTimeInvoiceProcessor;

    private final TenantSettingService tenantSettingService;

    private final InvoiceAmountCalculator invoiceAmountCalculator;

    private final PercentOfChargeInvoiceHelper percentOfChargeHelper;

    @Inject
    public InvoicePreviewReportService(
        OrderGetService orderGetService,
        SubscriptionGetService subscriptionGetService,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        ProrationConfigurationGetService prorationConfigurationGetService,
        ProductCatalogGetService productCatalogGetService,
        RecurringInvoiceProcessor recurringInvoiceProcessor,
        OneTimeInvoiceProcessor oneTimeInvoiceProcessor,
        TenantSettingService tenantSettingService,
        InvoiceAmountCalculator invoiceAmountCalculator,
        PercentOfChargeInvoiceHelper percentOfChargeHelper
    ) {
        this.orderGetService = orderGetService;
        this.subscriptionGetService = subscriptionGetService;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.prorationConfigurationGetService = prorationConfigurationGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.recurringInvoiceProcessor = recurringInvoiceProcessor;
        this.oneTimeInvoiceProcessor = oneTimeInvoiceProcessor;
        this.tenantSettingService = tenantSettingService;
        this.invoiceAmountCalculator = invoiceAmountCalculator;
        this.percentOfChargeHelper = percentOfChargeHelper;
    }

    // NOTE: this is a temporary fork of invoice preview to allow the combination of "regular" invoice preview and
    // preview for event based charges which are stored in memoized_invoice_line_item table.
    // This mirrored code path filters out event based charges so they are not double counted in the invoice preview report
    // todo: Instead of calculating and caching invoice previews as aggregate values by billing period,
    //  we should complete invoice line memoization and query / aggregate from there instead
    public List<InvoicePreview> previewAllInvoicesBySubscriptionId(String subscriptionId) {
        if (StringUtils.isBlank(subscriptionId)) {
            throw new IllegalArgumentException("subscriptionId is required");
        }

        // 1. Get all executed orders
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);
        List<Order> executedOrders = orders.stream().filter(order -> order.getStatus() == OrderStatus.EXECUTED).toList();

        // 2. Get all billing periods for subscription.
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        List<BillingPeriod> allBillingPeriods = subscriptionBillingPeriodService.getBillingPeriods(subscription, timeZone, subscription.getEndDate());

        // 3. Generate invoice previews by subscription's billing period for all orders.
        List<List<InvoicePreview>> allOrderInvoicePreviews = executedOrders.stream().map(this::previewInvoicesByBillingPeriods).toList();

        /*
         * 4. For each subscription billing period, roll up invoice previews from orders for matching period.
         * For One time and Prepaid charge types, the InvoicePreview period is same as orderline's effective
         * start date and end dates. For Recurring and Usage charge types it is same as order's billing cycle,
         * which is set to same value as subscription's billing cycle.
         * The roll-up logic below needs to be adjusted if that behavior changes.
         */
        return allBillingPeriods
            .stream()
            .map(billingPeriod -> // For each subscription billing period
                rollupPreviews(
                    billingPeriod,
                    allOrderInvoicePreviews
                        .stream()
                        .flatMap(oneOrderInvoicePreviews -> // For each order's list of invoice previews
                            oneOrderInvoicePreviews
                                .stream()
                                .filter(aPreview -> // Pick those that are within the subscriptions billing period
                                    SubscriptionBillingPeriodService.isInstantInBillingPeriod(aPreview.getBillingPeriod().getStart(), billingPeriod)
                                )
                        )
                        .collect(Collectors.toList())
                )
            )
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private List<InvoicePreview> previewInvoicesByBillingPeriods(Order order) {
        Map<String, Charge> chargeMap = getChargeMap(List.of(order));
        InvoicePreview invoicePreviewByOrderPeriod = previewInvoiceByOrderPeriod(order, chargeMap);
        var allInvoiceItems = invoicePreviewByOrderPeriod.getInvoiceItems();

        List<BillingPeriod> billingPeriods = getBillingPeriods(order);

        List<InvoicePreview> invoicePreviews = new ArrayList<>();

        for (BillingPeriod billingPeriod : billingPeriods) {
            Instant periodEnd = billingPeriod.getEnd().isAfter(order.getEndDate()) ? order.getEndDate() : billingPeriod.getEnd();
            BillingPeriod adjustedBillingPeriod = new BillingPeriod(
                billingPeriod.getStart(),
                periodEnd,
                billingPeriod.getFullPeriodEnd(),
                billingPeriod.getRecurrence()
            );

            // find line items that falls within the billing period
            List<InvoiceItem> itemsInPeriod = allInvoiceItems
                .stream()
                .filter(
                    item ->
                        item.getPeriodStartDate().compareTo(adjustedBillingPeriod.getStart()) >= 0 &&
                        item.getPeriodStartDate().compareTo(adjustedBillingPeriod.getEnd()) < 0
                )
                .toList();

            invoicePreviews.add(InvoicePreviewBuilder.getInvoicePreview(order.getOrderId(), itemsInPeriod, adjustedBillingPeriod, chargeMap));
        }

        return invoicePreviews;
    }

    private InvoicePreview previewInvoiceByOrderPeriod(Order order, Map<String, Charge> chargeMap) {
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        List<InvoiceItem> mergedInvoiceItems = previewInvoiceItemsForOrder(order, prorationConfig, timeZone, chargeMap);

        BillingPeriod orderPeriod = new BillingPeriod(order.getStartDate(), order.getEndDate(), order.getEndDate(), order.getBillingCycle());
        return InvoicePreviewBuilder.getInvoicePreview(order.getOrderId(), mergedInvoiceItems, orderPeriod, chargeMap);
    }

    private List<InvoiceItem> previewInvoiceItemsForOrder(
        Order order,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        Map<String, Charge> chargeMap
    ) {
        validateInvoiceItemsGenerationEligibility(order);

        Instant subscriptionStart;

        if (StringUtils.isNotBlank(order.getExternalSubscriptionId())) {
            Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
            subscriptionStart = subscription.getStartDate();
        } else {
            subscriptionStart = order.getStartDate();
        }

        InvoiceBillingInfo invoiceBillingInfo = ImmutableInvoiceBillingInfo.builder()
            .invoiceTargetDate(order.getEndDate())
            .subscriptionStart(subscriptionStart)
            .subscriptionEnd(order.getEndDate())
            .billingAnchorDate(getBillingAnchorDate(order))
            .currency(order.getCurrency())
            .timeZone(timeZone)
            .prorationConfig(prorationConfig)
            .billingCycle(order.getBillingCycle())
            .billingTerm(order.getBillingTerm())
            .build();

        var orderBillingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            invoiceBillingInfo.getSubscriptionStart(),
            invoiceBillingInfo.getSubscriptionEnd(),
            invoiceBillingInfo.getBillingAnchorDate(),
            invoiceBillingInfo.getInvoiceTargetDate(),
            invoiceBillingInfo.getTimeZone().toZoneId(),
            invoiceBillingInfo.getBillingCycle(),
            invoiceBillingInfo.getBillingTerm()
        );

        var prepaidOrderLines = getOrderLinesByType(order.getLineItemsNetEffect(), chargeMap, ChargeType.PREPAID);

        // Remove event based charges, since for the purposes of invoice preview reports they are not aggregated properly.
        // Event based invoice preview instead comes directly from memoized_invoice_line_item table in predefined_report query
        List<OrderLineItem> filteredLineItems = order
            .getLineItemsNetEffect()
            .stream()
            .filter(item -> {
                Charge charge = chargeMap.get(item.getChargeId());

                if (charge == null) {
                    throw new InvariantCheckFailedException(
                        String.format(
                            "Invoice item preview. Charge %s not found for item %s in order %s",
                            item.getChargeId(),
                            item.getOrderLineId(),
                            item.getOrderId()
                        )
                    );
                }

                return !charge.isEventBased();
            })
            .collect(Collectors.toList());

        ImmutableInvoicePreviewInput previewInput = ImmutableInvoicePreviewInput.builder()
            .invoiceBillingInfo(invoiceBillingInfo)
            .putAllChargeMap(chargeMap)
            .addAllOrderLineItems(order.getLineItems())
            .addAllOrderLineItemsNetEffect(filteredLineItems)
            .addAllTopLevelBillingPeriods(orderBillingPeriods)
            .build();
        List<InvoiceItem> recurringInvoiceItems = recurringInvoiceProcessor.previewInvoiceItems(previewInput);
        List<InvoiceItem> oneTimeInvoiceItems = oneTimeInvoiceProcessor.previewInvoiceItems(previewInput);
        List<InvoiceItem> prepaidInvoiceItems = previewPrepaidInvoiceItems(invoiceBillingInfo, chargeMap, prepaidOrderLines);

        List<InvoiceItem> allRecurringInvoiceItems = recurringInvoiceProcessor.previewInvoiceItems(
            // NOTE: we need to get all recurring lines for percent of charge because we need to assess all charges involved
            // NOTE: the lineItems and lineItemsNetEffect are the same for Order Creation/Order Cancellation/Order Renewal
            // NOTE: the only place where they differ is for amendment and for amendment of percent of charge we need the full context
            // NOTE: and since we de-book and re-book all lines involved during amendment here we pass line items and not net effect line items
            previewInput.withOrderLineItemsNetEffect(order.getLineItems())
        );
        List<InvoiceItem> percentOfInvoiceItems = percentOfChargeHelper.previewPercentOfInvoiceItems(
            order,
            previewInput,
            allRecurringInvoiceItems,
            false
        );

        var percentOfOrderLines = getOrderLinesByType(order.getLineItemsNetEffect(), chargeMap, ChargeType.PERCENTAGE_OF);

        // todo: this is a temporary solution to preview percent of charge by billing period. Should revisit the preview path
        // Percent of charge preview lines do not consider billing period.
        // Instead, it takes the total target amounts that fall within the percent of charge order line start to end interval and derives the total percent of amount
        // This causes 2 issues:
        // 1) Due to the later splitting by billing period during invoice generation, there can be rounding artifacts
        // 2) Order form invoice preview puts the entirety of the percent of amount in the first billing period
        // This block takes the generated percent of amount and splits it by billing period invoking the same code as invoice generation
        percentOfInvoiceItems = splitPercentOfAmountsByBillingPeriod(
            percentOfOrderLines,
            percentOfInvoiceItems,
            invoiceBillingInfo,
            orderBillingPeriods,
            chargeMap
        );

        return mergeInvoiceItemsAndGenerateLineNumbers(recurringInvoiceItems, oneTimeInvoiceItems, prepaidInvoiceItems, percentOfInvoiceItems);
    }

    private List<InvoiceItem> mergeInvoiceItemsAndGenerateLineNumbers(
        List<InvoiceItem> recurringInvoiceItems,
        List<InvoiceItem> onetimeInvoiceItems,
        List<InvoiceItem> prepaidInvoiceItems,
        List<InvoiceItem> percentOfInvoiceItems
    ) {
        List<InvoiceItem> preLineNoItems = Stream.of(recurringInvoiceItems, onetimeInvoiceItems, prepaidInvoiceItems, percentOfInvoiceItems)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());

        return generateLineNumbers(preLineNoItems);
    }

    private List<InvoiceItem> generateLineNumbers(List<InvoiceItem> preLineNumberItems) {
        return IntStream.range(0, preLineNumberItems.size())
            .mapToObj(index -> {
                InvoiceItem invoiceItem = preLineNumberItems.get(index);
                return new InvoiceItem.InvoiceItemBuilder()
                    .from(invoiceItem)
                    .invoiceLineNumber(getInvoiceLineNumberFromIndex(index))
                    .createInvoiceItem();
            })
            .collect(Collectors.toList());
    }

    private static String getInvoiceLineNumberFromIndex(int index) {
        // we want the invoice line numbers not to be 0 based but 1 based
        return String.format("%d", index + 1);
    }

    private List<InvoiceItem> previewPrepaidInvoiceItems(
        InvoiceBillingInfo invoiceBillingInfo,
        Map<String, Charge> chargeMap,
        List<OrderLineItem> prepaidOrderLines
    ) {
        return prepaidOrderLines
            .stream()
            .map(item -> {
                Charge charge = chargeMap.get(item.getChargeId());
                return getPrepaidChargeInvoiceItems(invoiceBillingInfo, item, charge);
            })
            .flatMap(Collection::stream)
            .filter(invoiceItem -> !invoiceItem.getPeriodStartDate().isAfter(invoiceBillingInfo.getInvoiceTargetDate()))
            .toList();
    }

    private List<InvoiceItem> splitPercentOfAmountsByBillingPeriod(
        List<OrderLineItem> percentOfOrderLineItems,
        List<InvoiceItem> percentOfInvoiceItems,
        InvoiceBillingInfo invoiceBillingInfo,
        List<BillingPeriod> billingPeriods,
        Map<String, Charge> chargeMap
    ) {
        Map<String, OrderLineItem> orderLineItemMap = percentOfOrderLineItems
            .stream()
            .collect(Collectors.toMap(OrderLineItem::getOrderLineId, Function.identity()));

        List<InvoiceItem> invoiceItemsByBillingPeriod = new ArrayList<>();
        for (InvoiceItem invoiceItem : percentOfInvoiceItems) {
            OrderLineItem orderLineItem = orderLineItemMap.get(invoiceItem.getOrderLineItemId());
            orderLineItem.setAmount(invoiceItem.getAmount());
            orderLineItem.setListAmount(invoiceItem.getListAmount());
            orderLineItem.setListAmountBeforeOverride(invoiceItem.getListAmountBeforeOverride());
            orderLineItem.setListUnitPrice(invoiceItem.getListUnitPrice());
            orderLineItem.setListUnitPriceBeforeOverride(invoiceItem.getListUnitPriceBeforeOverride());
            orderLineItem.setSellUnitPrice(invoiceItem.getSellUnitPrice());

            Charge charge = chargeMap.get(orderLineItem.getChargeId());

            List<BillingPeriod> finalBillingPeriods = subscriptionBillingPeriodService.getOrderLineBillingPeriods(
                orderLineItem,
                chargeMap,
                invoiceBillingInfo,
                billingPeriods
            );

            for (BillingPeriod billingPeriod : finalBillingPeriods) {
                percentOfChargeHelper
                    .getPercentOfInvoiceItemForBillingPeriod(
                        invoiceBillingInfo,
                        billingPeriod,
                        orderLineItem,
                        charge,
                        false,
                        invoiceItem.getInlineDiscounts(),
                        invoiceItem.getPredefinedDiscounts()
                    )
                    .map(invoiceItemsByBillingPeriod::add);
            }
        }

        return invoiceItemsByBillingPeriod;
    }

    private List<InvoiceItem> getPrepaidChargeInvoiceItems(InvoiceBillingInfo invoiceBillingInfo, OrderLineItem lineItem, Charge charge) {
        if (charge.getRecurrence() == null) {
            var billingPeriod = new BillingPeriod(
                lineItem.getEffectiveDate(),
                lineItem.getEndDate(),
                lineItem.getEndDate(),
                new Recurrence(Cycle.PAID_IN_FULL, 1)
            );
            InvoiceItem invoiceItem = getPrepaidInvoiceItem(lineItem, charge, billingPeriod);
            return List.of(invoiceItem);
        }

        // todo: use order line start and end dates (fix issues with billing anchor date)
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            invoiceBillingInfo.getSubscriptionStart(),
            invoiceBillingInfo.getSubscriptionEnd(),
            invoiceBillingInfo.getBillingAnchorDate(),
            invoiceBillingInfo.getInvoiceTargetDate(),
            invoiceBillingInfo.getTimeZone().toZoneId(),
            charge.getRecurrence() != null ? charge.getRecurrence() : new Recurrence(Cycle.PAID_IN_FULL, 1),
            charge.getBillingTerm()
        );
        List<InvoiceItem> invoiceItems = new ArrayList<>();
        for (var billingPeriod : billingPeriods) {
            if (
                !orderLineItemInPeriod(
                    lineItem,
                    billingPeriod,
                    charge,
                    invoiceBillingInfo.getInvoiceTargetDate(),
                    invoiceBillingInfo.getSubscriptionEnd()
                )
            ) {
                continue;
            }

            InvoiceItem invoiceItem = getPrepaidInvoiceItem(lineItem, charge, billingPeriod);
            invoiceItems.add(invoiceItem);
        }

        return invoiceItems;
    }

    private InvoiceItem getPrepaidInvoiceItem(OrderLineItem lineItem, Charge charge, BillingPeriod billingPeriod) {
        ListAmount listAmount = invoiceAmountCalculator.calculatePrepaidListAmount(lineItem, charge).map(Numbers::makeCurrencyScale);

        // do not prorate prepaid amounts
        InvoiceItemAmounts invoiceItemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(
            lineItem.getDiscounts(),
            lineItem.getPredefinedDiscounts(),
            listAmount,
            invoiceAmountCalculator.getListUnitPrice(charge, lineItem),
            lineItem.getQuantity(),
            lineItem.getListPriceOverrideRatio()
        );

        var invoiceItemBuilder = new InvoiceItem.InvoiceItemBuilder()
            .entityId(lineItem.getEntityId())
            .orderId(lineItem.getOrderId())
            .orderLineItemId(lineItem.getOrderLineId())
            .subscriptionChargeId(lineItem.getExternalSubscriptionChargeId())
            .subscriptionChargeGroupId(lineItem.getSubscriptionChargeGroupId())
            .status(InvoiceStatus.DRAFT)
            .planId(lineItem.getPlanId())
            .chargeId(charge.getChargeId())
            .listAmount(invoiceItemAmounts.getListAmount())
            .listAmountBeforeOverride(listAmount.getListAmountBeforeOverride())
            .discountAmount(invoiceItemAmounts.getDiscountAmount())
            .amount(invoiceItemAmounts.getSellAmount())
            .listUnitPrice(invoiceItemAmounts.getListUnitPrice())
            .listUnitPriceBeforeOverride(invoiceItemAmounts.getListUnitPriceBeforeOverride())
            .sellUnitPrice(invoiceItemAmounts.getSellUnitPrice())
            .taxAmount(BigDecimal.ZERO)
            .quantity(lineItem.getQuantity())
            .inlineDiscounts(invoiceItemAmounts.getLineItemDiscounts())
            .predefinedDiscounts(invoiceItemAmounts.getTenantDiscounts())
            .periodStartDate(billingPeriod.getStart())
            .periodEndDate(billingPeriod.getEnd())
            .triggerOn(charge.getBillingTerm(), billingPeriod.getStart(), billingPeriod.getEnd());

        return invoiceItemBuilder.createInvoiceItem();
    }

    private static boolean orderLineItemInPeriod(
        OrderLineItem orderLineItem,
        BillingPeriod billingPeriod,
        Charge charge,
        Instant invoiceTargetDate,
        Instant subscriptionEndDate
    ) {
        // there may be a stub that extends past the end of the subscription. If the charge is billed in Arrears, adjustment is needed to cover the entire remaining duration
        // example: order line from Jan 1, 2023 - Dec 31, 2023. Yearly billing with first full billing cycle Feb 1, 2023.
        // in this case the second billing period is Feb 1, 2023 - Jan 31, 2024. When generating invoice / preview with target date = subscription end date
        // the target date is before the billing period end date, which means the invoice for that period doesn't get generated
        // adjusting the end date to the earlier of the subscription end date or billing period end date ensures the last billing period is always covered
        Instant periodEnd = billingPeriod.getEnd().isBefore(subscriptionEndDate) ? billingPeriod.getEnd() : subscriptionEndDate;

        if (charge.getBillingTerm() == BillingTerm.IN_ARREARS && periodEnd.isAfter(invoiceTargetDate)) {
            return false;
        }
        return orderLineItem.getEffectiveDate().isBefore(billingPeriod.getEnd()) && orderLineItem.getEndDate().isAfter(billingPeriod.getStart());
    }

    private List<OrderLineItem> getOrderLinesByType(List<OrderLineItem> orderLines, Map<String, Charge> chargeMap, ChargeType type) {
        return orderLines.stream().filter(orderLineItem -> chargeMap.get(orderLineItem.getChargeId()).getType() == type).collect(Collectors.toList());
    }

    private List<BillingPeriod> getBillingPeriods(Order order) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        Instant subscriptionStart;

        if (StringUtils.isNotEmpty(order.getExternalSubscriptionId())) {
            Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
            subscriptionStart = subscription.getStartDate();
        } else {
            subscriptionStart = order.getStartDate();
        }

        return subscriptionBillingPeriodService.getBillingPeriods(
            subscriptionStart,
            order.getEndDate(),
            getBillingAnchorDate(order),
            order.getEndDate(),
            timeZone.toZoneId(),
            order.getBillingCycle(),
            order.getBillingTerm()
        );
    }

    private Map<String, Charge> getChargeMap(List<Order> orders) {
        var uniqueChargeIds = orders
            .stream()
            .map(Order::getLineItems)
            .flatMap(Collection::stream)
            .map(OrderLineItem::getChargeId)
            .distinct()
            .collect(Collectors.toList());
        return productCatalogGetService.getChargeMapByChargeIds(uniqueChargeIds);
    }

    private Instant getBillingAnchorDate(Order order) {
        String externalSubscriptionId = order.getExternalSubscriptionId();

        if (StringUtils.isBlank(externalSubscriptionId)) {
            return order.getBillingAnchorDate();
        }

        try {
            var subscription = subscriptionGetService.getSubscription(externalSubscriptionId);
            return subscription.getBillingAnchorDate();
        } catch (ObjectNotFoundException ex) {
            return order.getBillingAnchorDate();
        }
    }

    private InvoicePreview rollupPreviews(BillingPeriod billingPeriod, List<InvoicePreview> previews) {
        if (previews.isEmpty()) {
            return null;
        }
        var subtotal = BigDecimal.ZERO;
        var total = BigDecimal.ZERO;
        var totalDiscount = BigDecimal.ZERO;
        var totalTaxEstimate = BigDecimal.ZERO;
        var totalListAmountBeforeOverrides = BigDecimal.ZERO;
        List<InvoiceItem> invoiceItems = new ArrayList<>(10);
        List<InvoiceItemPreview> invoiceItemPreviews = new ArrayList<>(10);
        String orderId = previews.get(0).getOrderId(); // TODO: Use subscription Id instead?

        for (var preview : previews) {
            subtotal = subtotal.add(preview.getSubtotal());
            total = total.add(preview.getTotal());
            totalDiscount = totalDiscount.add(preview.getTotalDiscount());
            totalTaxEstimate = totalTaxEstimate.add(preview.getTaxEstimate());
            totalListAmountBeforeOverrides = totalListAmountBeforeOverrides.add(preview.getTotalListAmountBeforeOverrides());
            invoiceItems.addAll(preview.getInvoiceItems());
            invoiceItemPreviews.addAll(preview.getLineItems());
        }
        return new InvoicePreview(
            orderId,
            billingPeriod,
            invoiceItemPreviews,
            subtotal,
            total,
            totalDiscount,
            totalTaxEstimate,
            totalListAmountBeforeOverrides,
            invoiceItems
        );
    }

    private void validateInvoiceItemsGenerationEligibility(Order order) {
        if (ObjectUtils.anyNull(order.getStartDate(), order.getEndDate(), order.getBillingAnchorDate(), order.getBillingCycle())) {
            throw new IllegalArgumentException("Order startDate, endDate, billingAnchorDate and billingCycle are required");
        }
    }
}
