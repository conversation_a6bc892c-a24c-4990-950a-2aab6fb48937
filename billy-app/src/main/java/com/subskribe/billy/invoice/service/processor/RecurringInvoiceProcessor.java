package com.subskribe.billy.invoice.service.processor;

import static com.subskribe.billy.invoice.service.processor.InvoiceProcessorUtility.getOrderLinesByType;
import static com.subskribe.billy.invoice.service.processor.InvoiceProcessorUtility.orderLineItemInPeriod;
import static com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService.getItemOverlapPeriod;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.InvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.ListAmount;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.InvoiceAmountCalculator;
import com.subskribe.billy.invoice.service.processor.model.InvoiceGenerationInput;
import com.subskribe.billy.invoice.service.processor.model.InvoicePreviewInput;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.collections4.ListUtils;

public class RecurringInvoiceProcessor implements InvoiceProcessor, EventBasedAndAdhocCustomBillingInvoiceProcessor {

    private final InvoiceAmountCalculator invoiceAmountCalculator;

    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;

    private final FeatureService featureService;

    private final InvoiceDAO invoiceDAO;

    @Inject
    public RecurringInvoiceProcessor(
        InvoiceAmountCalculator invoiceAmountCalculator,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        FeatureService featureService,
        InvoiceDAO invoiceDAO
    ) {
        this.invoiceAmountCalculator = invoiceAmountCalculator;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.featureService = featureService;
        this.invoiceDAO = invoiceDAO;
    }

    @Override
    public List<InvoiceItem> previewInvoiceItems(InvoicePreviewInput invoiceGenerationInput) {
        List<OrderLineItem> relevantOrderLines = getOrderLinesByType(
            invoiceGenerationInput.getOrderLineItemsNetEffect(),
            invoiceGenerationInput.getChargeMap(),
            ChargeType.RECURRING
        );
        return relevantOrderLines
            .stream()
            .map(orderItem -> previewRecurringInvoiceItemsForOrderItem(invoiceGenerationInput, orderItem))
            .flatMap(List::stream)
            .toList();
    }

    @Override
    public List<InvoiceItem> generateInvoiceItems(InvoiceGenerationInput invoiceGenerationInput) {
        List<OrderLineItem> recurringInvoiceItems = getOrderLinesByType(
            invoiceGenerationInput.getOrderLineItemsNetEffect(),
            invoiceGenerationInput.getChargeMap(),
            ChargeType.RECURRING
        );

        List<OrderLineItem> standardRecurringOrderLines = getNonEventBasedAndNonAdhocCustomBillingOrderLines(
            recurringInvoiceItems,
            invoiceGenerationInput.getChargeMap(),
            invoiceGenerationInput
        );
        List<OrderLineItem> eventBasedRecurringOrderLines = getEventBasedOrderLines(recurringInvoiceItems, invoiceGenerationInput.getChargeMap());
        List<OrderLineItem> adhocCustomBillingOrderLines = getAdhocCustomBillingOrderLines(
            recurringInvoiceItems,
            ListUtils.sum(standardRecurringOrderLines, eventBasedRecurringOrderLines)
        );

        List<InvoiceItem> standardRecurringInvoiceItems = standardRecurringOrderLines
            .stream()
            .map(orderItem -> generateInvoiceItemsForRecurringOrderItem(invoiceGenerationInput, orderItem))
            .flatMap(List::stream)
            .toList();

        List<InvoiceItem> eventBasedRecurringInvoiceItems = generateEventBasedInvoiceItems(invoiceGenerationInput, eventBasedRecurringOrderLines);
        List<InvoiceItem> adhocCustomBillingInvoiceItems = generateAdhocCustomBillingInvoiceItems(
            invoiceGenerationInput,
            adhocCustomBillingOrderLines
        );

        return Stream.of(standardRecurringInvoiceItems, eventBasedRecurringInvoiceItems, adhocCustomBillingInvoiceItems)
            .flatMap(List::stream)
            .toList();
    }

    // ALL PRIVATE METHODS
    private List<InvoiceItem> previewRecurringInvoiceItemsForOrderItem(InvoicePreviewInput invoicePreviewInput, OrderLineItem orderItem) {
        // If order line is part of custom billing then use custom billing periods
        Optional<List<BillingPeriod>> customBillingPeriods = getCustomBillingPeriodsIfApplicable(invoicePreviewInput, orderItem);
        List<BillingPeriod> topLevelBillingPeriods = customBillingPeriods.orElseGet(invoicePreviewInput::getTopLevelBillingPeriods);

        Map<String, Charge> chargeMap = invoicePreviewInput.getChargeMap();
        InvoiceBillingInfo invoiceBillingInfo = invoicePreviewInput.getInvoiceBillingInfo();

        List<BillingPeriod> finalBillingPeriods = subscriptionBillingPeriodService.getOrderLineBillingPeriods(
            orderItem,
            chargeMap,
            invoiceBillingInfo,
            topLevelBillingPeriods
        );

        if (
            featureService.isEnabled(Feature.ALLOW_ZERO_DURATION_ORDER_ITEMS) && orderItem.getEffectiveDate().compareTo(orderItem.getEndDate()) == 0
        ) {
            // Special handling if zero duration line items is allowed: ignore order billing cycle and calculate amounts as paid in full.
            // Normally, zero duration line items are not invoiced, but this is a special case where we want to preview the invoice items to return unit prices.
            BillingPeriod fullOrderItemPeriod = new BillingPeriod(
                orderItem.getEffectiveDate(),
                orderItem.getEndDate(),
                orderItem.getEndDate(),
                new Recurrence(Cycle.PAID_IN_FULL, 1)
            );
            InvoiceItem invoiceItem = getRecurringInvoiceItem(fullOrderItemPeriod, orderItem, invoicePreviewInput);
            return List.of(invoiceItem);
        }

        return finalBillingPeriods
            .stream()
            .filter(billingPeriod ->
                orderLineItemInPeriod(
                    orderItem,
                    billingPeriod,
                    chargeMap.get(orderItem.getChargeId()),
                    invoiceBillingInfo.getInvoiceTargetDate(),
                    invoiceBillingInfo.getSubscriptionEnd()
                )
            )
            .map(billingPeriod -> getRecurringInvoiceItem(billingPeriod, orderItem, invoicePreviewInput))
            .toList();
    }

    private List<InvoiceItem> generateInvoiceItemsForRecurringOrderItem(InvoiceGenerationInput invoiceGenerationInput, OrderLineItem orderItem) {
        List<BillingPeriod> topLevelBillingPeriods = invoiceGenerationInput.getTopLevelBillingPeriods();
        List<BillingPeriod> finalBillingPeriods = subscriptionBillingPeriodService.getOrderLineBillingPeriods(
            orderItem,
            invoiceGenerationInput.getChargeMap(),
            invoiceGenerationInput.getInvoiceBillingInfo(),
            topLevelBillingPeriods
        );

        return finalBillingPeriods
            .stream()
            .filter(billingPeriod -> isOrderLineEligibleForInvoicing(invoiceGenerationInput, orderItem, billingPeriod))
            .map(billingPeriod -> generateRecurringInvoiceItemsForBillingPeriod(invoiceGenerationInput, orderItem, billingPeriod))
            .flatMap(List::stream)
            .toList();
    }

    private boolean isOrderLineEligibleForInvoicing(
        InvoiceGenerationInput invoiceGenerationInput,
        OrderLineItem orderItem,
        BillingPeriod billingPeriod
    ) {
        InvoiceBillingInfo invoiceBillingInfo = invoiceGenerationInput.getInvoiceBillingInfo();
        if (
            !orderLineItemInPeriod(
                orderItem,
                billingPeriod,
                invoiceGenerationInput.getChargeMap().get(orderItem.getChargeId()),
                invoiceBillingInfo.getInvoiceTargetDate(),
                invoiceBillingInfo.getSubscriptionEnd()
            )
        ) {
            return false;
        }
        Set<String> orderLineItemIdsAlreadyInvoiced = new HashSet<>(
            invoiceDAO.getAllOrderLineIdsWithInvoiceItemsPastThreshold(List.of(orderItem.getOrderLineId()), billingPeriod.getStart())
        );
        return !orderLineItemIdsAlreadyInvoiced.contains(orderItem.getOrderLineId());
    }

    private List<InvoiceItem> generateRecurringInvoiceItemsForBillingPeriod(
        InvoiceGenerationInput invoiceGenerationInput,
        OrderLineItem orderItem,
        BillingPeriod billingPeriod
    ) {
        Map<String, List<MemoizedInvoiceLineItem>> memoizedByOrderLineMap = invoiceGenerationInput.getMemoizedItemsByOrderLine();
        if (memoizedByOrderLineMap.containsKey(orderItem.getOrderLineId())) {
            return getMemoizedInvoiceItemsForBillingPeriod(memoizedByOrderLineMap, orderItem, billingPeriod);
        }
        return List.of(getRecurringInvoiceItem(billingPeriod, orderItem, invoiceGenerationInput));
    }

    private List<InvoiceItem> getMemoizedInvoiceItemsForBillingPeriod(
        Map<String, List<MemoizedInvoiceLineItem>> memoizedByOrderLineMap,
        OrderLineItem orderItem,
        BillingPeriod billingPeriod
    ) {
        Period orderItemBillingPeriodOverlap = getItemOverlapPeriod(orderItem.getEffectiveDate(), orderItem.getEndDate(), billingPeriod);
        return memoizedByOrderLineMap
            .get(orderItem.getOrderLineId())
            .stream()
            .filter(
                memoizedInvoiceItem ->
                    memoizedInvoiceItem.getPeriodStartDate().compareTo(orderItemBillingPeriodOverlap.getStart()) >= 0 &&
                    memoizedInvoiceItem.getPeriodEndDate().compareTo(orderItemBillingPeriodOverlap.getEnd()) <= 0
            )
            .map(memoizedInvoiceLineItem -> memoizedInvoiceLineItem.to(orderItem))
            .toList();
    }

    private InvoiceItem getRecurringInvoiceItem(BillingPeriod billingPeriod, OrderLineItem item, InvoicePreviewInput invoicePreviewInput) {
        Charge charge = invoicePreviewInput.getChargeMap().get(item.getChargeId());
        TimeZone timeZone = invoicePreviewInput.getInvoiceBillingInfo().getTimeZone();
        ProrationConfig prorationConfig = invoicePreviewInput.getInvoiceBillingInfo().getProrationConfig();
        Period orderItemBillingPeriodOverlap = getItemOverlapPeriod(item.getEffectiveDate(), item.getEndDate(), billingPeriod);

        ListAmount listAmount;

        if (billingPeriod.getRecurrence().getCycle() == Cycle.PAID_IN_FULL || charge.getRecurrence().getCycle() == Cycle.DAY) {
            // if the order billing cycle is paid in full or charge has DAY recurrence, use the charge cycle instead to construct the periods used to calculate invoice amounts
            listAmount = getTotalAmountByChargePeriod(billingPeriod, item, charge, invoicePreviewInput.getInvoiceBillingInfo());
        } else {
            // todo: currency scale may not be correct if we use the default
            listAmount = invoiceAmountCalculator
                .calculateRecurringItemListAmount(item, charge, billingPeriod, orderItemBillingPeriodOverlap, timeZone, prorationConfig)
                .map(amount -> Numbers.makeCurrencyScale(amount, invoicePreviewInput.getInvoiceBillingInfo().getCurrencyCode()));
        }

        InvoiceItemAmounts invoiceItemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(
            item.getDiscounts(),
            item.getPredefinedDiscounts(),
            listAmount,
            invoiceAmountCalculator.getListUnitPrice(charge, item),
            item.getQuantity(),
            item.getListPriceOverrideRatio()
        );

        var invoiceItemBuilder = InvoiceItem.InvoiceItemBuilder.from(item, invoiceItemAmounts)
            .status(InvoiceStatus.DRAFT)
            .taxAmount(BigDecimal.ZERO)
            .periodStartDate(orderItemBillingPeriodOverlap.getStart())
            .periodEndDate(orderItemBillingPeriodOverlap.getEnd())
            .triggerOn(charge.getBillingTerm(), billingPeriod.getStart(), billingPeriod.getEnd(), invoicePreviewInput.getInvoiceBillingInfo());

        return invoiceItemBuilder.createInvoiceItem();
    }

    private ListAmount getTotalAmountByChargePeriod(BillingPeriod billingPeriod, OrderLineItem item, Charge charge, InvoiceBillingInfo billingInfo) {
        // get periods based on charge cycle
        List<BillingPeriod> chargePeriods = subscriptionBillingPeriodService.getBillingPeriods(
            billingPeriod.getStart(),
            billingPeriod.getEnd(),
            billingPeriod.getStart(),
            billingPeriod.getEnd(),
            billingInfo.getTimeZone().toZoneId(),
            charge.getRecurrence(),
            BillingTerm.UP_FRONT
        );

        List<ListAmount> listAmounts = chargePeriods
            .stream()
            .map(chargePeriod -> {
                Period itemChargePeriodOverlap = getItemOverlapPeriod(item.getEffectiveDate(), item.getEndDate(), chargePeriod);
                return invoiceAmountCalculator.calculateRecurringItemListAmount(
                    item,
                    charge,
                    chargePeriod,
                    itemChargePeriodOverlap,
                    billingInfo.getTimeZone(),
                    billingInfo.getProrationConfig()
                );
            })
            .map(periodListAmount -> periodListAmount.map(periodAmount -> Numbers.makeCurrencyScale(periodAmount, billingInfo.getCurrencyCode())))
            .toList();
        return ListAmount.sum(listAmounts);
    }

    @Override
    public List<InvoiceItem> getInvoiceItemsForOrderLineItem(String orderLineId) {
        return invoiceDAO.getInvoiceItemsForOrderLineItem(orderLineId);
    }
}
