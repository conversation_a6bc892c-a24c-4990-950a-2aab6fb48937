package com.subskribe.billy.invoice;

import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.upload.ProrationVersion;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.BillyDuration;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.TimeZone;

public class ProrationCalculator {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProrationCalculator.class);
    private static final BigDecimal SECONDS_IN_DAY = BigDecimal.valueOf(24L * 60L * 60L);

    // given the total item period, total amount, service period amount and start instant,
    // reverse calculate based on proration configuration and timezone to derive the service period
    public static Period calculateServicePeriod(
        Period periodDenominator,
        Instant servicePeriodStart,
        BigDecimal servicePeriodAmount,
        BigDecimal itemTotalAmount,
        ProrationConfig prorationConfig,
        TimeZone timeZone
    ) {
        Validator.validateNonNullArguments(periodDenominator, servicePeriodStart, itemTotalAmount, servicePeriodAmount);

        long startEpochSeconds = servicePeriodStart.getEpochSecond();

        // using epoch seconds because instant in some cases may have millis due to an old bug that was subsequently fixed but may still be in the DB
        if (periodDenominator.getStart().getEpochSecond() > startEpochSeconds || periodDenominator.getEnd().getEpochSecond() <= startEpochSeconds) {
            throw new InvalidInputException("start must be within prorate using period");
        }

        if (servicePeriodAmount.signum() * itemTotalAmount.signum() < 0) {
            throw new InvalidInputException("both service period and total amounts must have the same sign, either all positive or all negative");
        }

        if (servicePeriodAmount.abs().compareTo(itemTotalAmount.abs()) > 0) {
            throw new InvalidInputException("service period amount cannot be greater than total amount");
        }

        if (servicePeriodAmount.compareTo(itemTotalAmount) == 0) {
            if (startEpochSeconds == periodDenominator.getStart().getEpochSecond()) {
                return periodDenominator;
            } else {
                throw new InvalidInputException(
                    "start must be at the beginning of the prorate using period when service period amount equals total amount"
                );
            }
        }

        BigDecimal serviceAmountRatio = Numbers.scaledDivide(servicePeriodAmount, itemTotalAmount);

        Instant serviceEnd =
            switch (prorationConfig.getMode()) {
                case EXACT -> calculateServiceEndForExact(periodDenominator, servicePeriodStart, serviceAmountRatio);
                case EXACT_DAYS -> calculateServiceEndForExactDays(periodDenominator, servicePeriodStart, serviceAmountRatio, timeZone);
                case NORMALIZED -> calculateServicePeriodForNormalized(
                    periodDenominator,
                    servicePeriodStart,
                    serviceAmountRatio,
                    timeZone,
                    prorationConfig
                );
            };

        if (serviceEnd.isAfter(periodDenominator.getEnd())) {
            serviceEnd = periodDenominator.getEnd();
        } else {
            // for end of period (last segment), the proration numbers may not match since there is adjustment made. Skip invariant check in this case
            Period period = Period.between(servicePeriodStart, serviceEnd);
            BigDecimal prorationRatio = getPeriodProrationRatio(periodDenominator, period, prorationConfig, timeZone, ProrationVersion.V2);
            BigDecimal proratedAmount = Numbers.makeCurrencyScale(prorationRatio.multiply(itemTotalAmount));

            BigDecimal allowedVariance = getAllowedVariance(periodDenominator, itemTotalAmount);

            // here we do an invariant check based on both forward and reverse proration calculations.
            // 1. Take the user input amount and calculate the service period (reverse proration)
            // 2. Take the calculated service period and total order line amount and calculate expected prorated amount (forward proration)
            // 3. Compare the 2 values (taking into account tolerance amount) to see if they match
            if (proratedAmount.subtract(Numbers.makeCurrencyScale(servicePeriodAmount)).abs().compareTo(allowedVariance.abs()) > 0) {
                String message = String.format(
                    "InvariantCheckFailed: Prorated amount %s does not match expected service amount %s for service period %s, allowed variance %s and total amount %s",
                    proratedAmount,
                    servicePeriodAmount,
                    period,
                    allowedVariance,
                    itemTotalAmount
                );

                // todo: during baking time (in sandbox), log error but allow the request to go through so we don't block UAT testing by customers
                LOGGER.error(message);
            }
        }

        return Period.between(servicePeriodStart, serviceEnd);
    }

    // calculate the rounding tolerance allowed based on the total number of seconds in the line item and total amount
    // this determines the $ / second value. Since time granularity is at the second level, this should be the maximum delta for rounding
    // example: given 1 month subscription interval and total order line amount of $200,000, there are 2,678,400 seconds,
    // assuming 31 day month with no leap year or DST. Therefore, each second has a value of ~$0.0746
    // Given the tolerance, it's not possible to get to the exact cent, since adding 1 second to the service interval will result in $0.0746
    // So we allow up to $0.0746 variance in the proration calculation invariant check
    private static BigDecimal getAllowedVariance(Period periodDenominator, BigDecimal itemTotalAmount) {
        long totalDenominatorSeconds = periodDenominator.toDuration().getSeconds();
        return Numbers.scaledDivide(itemTotalAmount, BigDecimal.valueOf(totalDenominatorSeconds));
    }

    // calculate based on exact number of seconds
    // service period end = service period start + (service amount / total amount) * denominator seconds
    private static Instant calculateServiceEndForExact(Period prorationDenominator, Instant start, BigDecimal serviceAmountRatio) {
        BigDecimal serviceSeconds = serviceAmountRatio.multiply(
            BigDecimal.valueOf(prorationDenominator.getEnd().getEpochSecond() - prorationDenominator.getStart().getEpochSecond())
        );

        // round to the nearest second
        return Instant.ofEpochSecond(Numbers.round(serviceSeconds) + start.getEpochSecond());
    }

    private static Instant calculateServiceEndForExactDays(
        Period prorationDenominator,
        Instant servicePeriodStart,
        BigDecimal servicePeriodAmountRatio,
        TimeZone timeZone
    ) {
        BigDecimal denominatorDays = getDaysInPeriod(prorationDenominator, timeZone);

        // based on the total days in the denominator and service period amount ratio, calculate the number of days in the service period, including fractional days
        BigDecimal computedDaysInNumerator = servicePeriodAmountRatio.multiply(denominatorDays);
        long completeDaysInNumerator = computedDaysInNumerator.longValue();
        BigDecimal remainingSecondsFractionOfDay = computedDaysInNumerator.subtract(BigDecimal.valueOf(completeDaysInNumerator));

        // get start of day containing fractional day
        Instant fractionalDayStart = DateTimeCalculator.plusDays(timeZone.toZoneId(), servicePeriodStart, completeDaysInNumerator);
        long secondsInFractionalDay = getSecondsInFractionalDay(remainingSecondsFractionOfDay, fractionalDayStart, timeZone);

        return DateTimeCalculator.plusSeconds(timeZone.toZoneId(), fractionalDayStart, secondsInFractionalDay);
    }

    private static Instant calculateServicePeriodForNormalized(
        Period prorationDenominator,
        Instant servicePeriodStart,
        BigDecimal serviceAmountRatio,
        TimeZone timeZone,
        ProrationConfig prorationConfig
    ) {
        // first calculate the total months in the numerator
        BigDecimal denominatorMonths = getNormalizedMonths(prorationDenominator, prorationConfig, timeZone);
        BigDecimal computedNumeratorMonths = serviceAmountRatio.multiply(denominatorMonths);

        // get number of complete months in numerator
        int completeNumeratorMonths = computedNumeratorMonths.intValue();
        Instant fractionalMonthStart = DateTimeCalculator.plusMonths(timeZone.toZoneId(), servicePeriodStart, completeNumeratorMonths);

        // then calculate the number of complete days remaining after taking into account complete months if any
        BigDecimal fractionalMonthInNumerator = computedNumeratorMonths.subtract(BigDecimal.valueOf(completeNumeratorMonths));
        long totalDaysInFractionalMonth = getTotalDaysInMonth(fractionalMonthStart, timeZone, prorationConfig);
        BigDecimal daysInFractionalMonth = fractionalMonthInNumerator.multiply(BigDecimal.valueOf(totalDaysInFractionalMonth));
        int completeDaysInFractionalMonth = daysInFractionalMonth.intValue();
        Instant fractionalDayStart = DateTimeCalculator.plusDays(timeZone.toZoneId(), fractionalMonthStart, completeDaysInFractionalMonth);

        // then calculate the number of seconds remaining after taking into account complete months and days if any
        BigDecimal partialDayInFractionalMonth = daysInFractionalMonth.subtract(BigDecimal.valueOf(completeDaysInFractionalMonth));
        long secondsInFractionalDay = getSecondsInFractionalDay(partialDayInFractionalMonth, fractionalDayStart, timeZone);

        return DateTimeCalculator.plusSeconds(timeZone.toZoneId(), fractionalDayStart, secondsInFractionalDay);
    }

    // for existing callers use original proration logic
    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public static BigDecimal getPeriodProrationRatio(
        Period prorateOnPeriod,
        Period prorateUsingPeriod,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        Optional<Recurrence> billingCycle
    ) {
        // TODO : track usages of this method and get rid of V1 version and use V2, it's safe to use V2 directly for custom billing as we are starting fresh
        var prorationVersion = billingCycle
            .filter(recurrence -> recurrence.getCycle() == Cycle.CUSTOM)
            .map(recurrence -> ProrationVersion.V2)
            .orElse(ProrationVersion.V1);
        return getPeriodProrationRatio(prorateOnPeriod, prorateUsingPeriod, prorationConfig, timeZone, prorationVersion);
    }

    protected static BigDecimal getPeriodProrationRatio(
        Period prorateOnPeriod,
        Period prorateUsingPeriod,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        ProrationVersion prorationVersion
    ) {
        Validator.validateNonNullArguments(prorateOnPeriod, prorateUsingPeriod, prorationConfig, timeZone);

        Optional<Period> prorationOverlapOptional = Period.overlapOf(prorateOnPeriod, prorateUsingPeriod);
        if (prorationOverlapOptional.isEmpty()) {
            return BigDecimal.ZERO;
        }
        Period prorationOverlapPeriod = prorationOverlapOptional.get();
        Duration prorationOverlapDuration = prorationOverlapPeriod.toDuration();
        Duration prorateOnPeriodDuration = prorateOnPeriod.toDuration();
        if (prorateOnPeriodDuration.isZero()) {
            return BigDecimal.ONE;
        }

        return switch (prorationConfig.getMode()) {
            case EXACT -> getPreciseProrationRatio(prorateOnPeriodDuration, prorationOverlapDuration);
            case EXACT_DAYS -> getPreciseDaysProrationRatio(prorateOnPeriod, prorationOverlapPeriod, timeZone, prorationVersion);
            case NORMALIZED -> getNormalizedProrationRatioForPeriod(
                prorateOnPeriod,
                prorationOverlapPeriod,
                prorationConfig,
                timeZone,
                prorationVersion
            );
        };
    }

    // for existing callers use original proration logic
    public static BigDecimal getProrationRatio(
        BillingPeriod billingPeriod,
        Period orderLineItemPeriod,
        ProrationConfig prorationConfig,
        TimeZone timeZone
    ) {
        return getProrationRatio(billingPeriod, orderLineItemPeriod, prorationConfig, timeZone, ProrationVersion.V1);
    }

    // TODO: We need to reconcile this method with getPeriodProrationRatio
    protected static BigDecimal getProrationRatio(
        BillingPeriod billingPeriod,
        Period orderLineItemPeriod,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        ProrationVersion prorationVersion
    ) {
        Validator.validateNonNullArguments(billingPeriod, orderLineItemPeriod, prorationConfig);

        Duration billingPeriodDuration = billingPeriod.getFullPeriodDuration();
        Duration orderLineItemDuration = orderLineItemPeriod.toDuration();

        Duration prorationDuration;
        long orderLineItemDurationInSeconds = orderLineItemDuration.getSeconds();

        if (prorationConfig.getScheme() == ProrationConfig.Scheme.FIXED_DAYS) {
            prorationDuration = getProrationDurationForFixedDaysScheme(billingPeriod.getRecurrence());
        } else {
            prorationDuration = billingPeriodDuration;
        }

        if (
            orderLineItemDurationInSeconds >= billingPeriodDuration.getSeconds() || orderLineItemDurationInSeconds >= prorationDuration.getSeconds()
        ) {
            // full overlap between billing period and order item period
            return BigDecimal.ONE;
        }

        return switch (prorationConfig.getMode()) {
            case EXACT -> getPreciseProrationRatio(prorationDuration, orderLineItemDuration);
            case EXACT_DAYS -> getPreciseDaysProrationRatio(
                Period.between(billingPeriod.getStart(), billingPeriod.getFullPeriodEnd()),
                orderLineItemPeriod,
                timeZone,
                prorationVersion
            );
            case NORMALIZED -> getNormalizedProrationRatio(billingPeriod, orderLineItemPeriod, timeZone, prorationConfig, prorationVersion);
        };
    }

    private static BigDecimal getNormalizedProrationRatioForPeriod(
        Period denominator,
        Period numerator,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        ProrationVersion prorationVersion
    ) {
        BigDecimal v1Ratio = getNormalizedProrationRatioForPeriodV1(denominator, numerator, prorationConfig, timeZone);
        BigDecimal v2Ratio = getNormalizedProrationRatioForPeriodV2(denominator, numerator, prorationConfig, timeZone);

        if (v1Ratio.compareTo(v2Ratio) != 0) {
            logWarningForProrationRatio(numerator, denominator, v1Ratio, v2Ratio, "NORMALIZED");
        }

        return switch (prorationVersion) {
            case V1 -> v1Ratio;
            case V2 -> v2Ratio;
        };
    }

    private static BigDecimal getNormalizedProrationRatioForPeriodV1(
        Period denominator,
        Period numerator,
        ProrationConfig prorationConfig,
        TimeZone timeZone
    ) {
        BillyDuration denominatorDuration = DateTimeCalculator.getDurationBetween(denominator.getStart(), denominator.getEnd(), timeZone);
        BillyDuration numeratorDuration = DateTimeCalculator.getDurationBetween(numerator.getStart(), numerator.getEnd(), timeZone);

        BigDecimal fractionalMonthOfDaysInDenominator = BigDecimal.ZERO;
        if (denominatorDuration.hasRemainderDays()) {
            fractionalMonthOfDaysInDenominator = getRatioOfDaysInFullMonth(
                denominator,
                timeZone,
                prorationConfig,
                denominatorDuration.getMonths(),
                BigDecimal.valueOf(denominatorDuration.getDays())
            );
        }

        BigDecimal fractionalMonthOfDaysInNumerator = BigDecimal.ZERO;
        if (numeratorDuration.hasRemainderDays()) {
            fractionalMonthOfDaysInNumerator = getRatioOfDaysInFullMonth(
                numerator,
                timeZone,
                prorationConfig,
                numeratorDuration.getMonths(),
                BigDecimal.valueOf(numeratorDuration.getDays())
            );
        }

        BigDecimal fractionalMonthOfSecondsInDenominator = BigDecimal.ZERO;
        if (denominatorDuration.getSeconds() != 0) {
            fractionalMonthOfSecondsInDenominator = getMonthFractionForSeconds(denominator, timeZone, prorationConfig, denominatorDuration);
        }

        BigDecimal fractionalMonthOfSecondsInNumerator = BigDecimal.ZERO;
        if (numeratorDuration.getSeconds() != 0) {
            fractionalMonthOfSecondsInNumerator = getMonthFractionForSeconds(numerator, timeZone, prorationConfig, numeratorDuration);
        }

        BigDecimal monthsRatioNumerator = BigDecimal.valueOf(numeratorDuration.getMonths())
            .add(fractionalMonthOfDaysInNumerator)
            .add(fractionalMonthOfSecondsInNumerator);

        BigDecimal monthsRatioDenominator = BigDecimal.valueOf(denominatorDuration.getMonths())
            .add(fractionalMonthOfDaysInDenominator)
            .add(fractionalMonthOfSecondsInDenominator);

        return Numbers.scaledDivide(monthsRatioNumerator, monthsRatioDenominator);
    }

    private static BigDecimal getNormalizedProrationRatioForPeriodV2(
        Period denominator,
        Period numerator,
        ProrationConfig prorationConfig,
        TimeZone timeZone
    ) {
        BigDecimal monthsRatioNumerator = getNormalizedMonths(numerator, prorationConfig, timeZone);
        BigDecimal monthsRatioDenominator = getNormalizedMonths(denominator, prorationConfig, timeZone);
        return Numbers.scaledDivide(monthsRatioNumerator, monthsRatioDenominator);
    }

    // compute total number of months in the period, taking into account any fractional days and seconds
    private static BigDecimal getNormalizedMonths(Period period, ProrationConfig prorationConfig, TimeZone timeZone) {
        BillyDuration duration = DateTimeCalculator.getDurationBetween(period.getStart(), period.getEnd(), timeZone);

        BigDecimal fractionalDayOfSeconds = BigDecimal.ZERO;
        if (duration.getSeconds() != 0) {
            // what part of the specific full days does the remaining seconds represent?
            fractionalDayOfSeconds = getFractionOfPartialDayToCompleteDay(duration.getSeconds(), period.getEnd(), timeZone);
        }

        // what part of the specific full month does the remaining days and seconds together represent?
        BigDecimal fractionalMonthOfDays = getRatioOfDaysInFullMonth(
            period,
            timeZone,
            prorationConfig,
            duration.getMonths(),
            BigDecimal.valueOf(duration.getDays()).add(fractionalDayOfSeconds)
        );

        return BigDecimal.valueOf(duration.getMonths()).add(fractionalMonthOfDays);
    }

    private static BigDecimal getNormalizedProrationRatio(
        BillingPeriod billingPeriod,
        Period orderLineItemPeriod,
        TimeZone timeZone,
        ProrationConfig prorationConfig,
        ProrationVersion prorationVersion
    ) {
        BigDecimal v1Ratio = getNormalizedProrationRatioV1(billingPeriod, orderLineItemPeriod, timeZone, prorationConfig);
        BigDecimal v2Ratio = getNormalizedProrationRatioV2(billingPeriod, orderLineItemPeriod, timeZone, prorationConfig);

        if (v1Ratio.compareTo(v2Ratio) != 0) {
            logWarningForProrationRatio(
                orderLineItemPeriod,
                Period.between(billingPeriod.getStart(), billingPeriod.getStart()),
                v1Ratio,
                v2Ratio,
                "NORMALIZED"
            );
        }

        return switch (prorationVersion) {
            case V1 -> v1Ratio;
            case V2 -> v2Ratio;
        };
    }

    /*
    Calculates the proration ratio as a normalized value of the billing period
    Example: For a 1 year billing period, a 6 months term will return a ratio of 0.5 regardless of the actual length of the 6 months
     */
    private static BigDecimal getNormalizedProrationRatioV1(
        BillingPeriod billingPeriod,
        Period orderLineItemPeriod,
        TimeZone timeZone,
        ProrationConfig prorationConfig
    ) {
        BillyDuration durationBetween = DateTimeCalculator.getDurationBetween(orderLineItemPeriod.getStart(), orderLineItemPeriod.getEnd(), timeZone);

        BigDecimal fractionalMonth = BigDecimal.ZERO;

        if (durationBetween.hasRemainderDays()) {
            fractionalMonth = getRatioOfDaysInFullMonth(
                orderLineItemPeriod,
                timeZone,
                prorationConfig,
                durationBetween.getMonths(),
                BigDecimal.valueOf(durationBetween.getDays())
            );
        }

        // use the actual number of months + any fractional months from days left over
        BigDecimal monthsRatio = getNormalizedMonthsProrationRatio(
            BigDecimal.valueOf(durationBetween.getMonths()).add(fractionalMonth),
            billingPeriod.getRecurrence()
        );

        BigDecimal secondsRatio = getNormalizedSecondsProrationRatio(durationBetween.getSeconds(), billingPeriod.getFullPeriodDuration());

        return monthsRatio.add(secondsRatio);
    }

    private static BigDecimal getNormalizedProrationRatioV2(
        BillingPeriod billingPeriod,
        Period numerator,
        TimeZone timeZone,
        ProrationConfig prorationConfig
    ) {
        BillyDuration durationBetween = DateTimeCalculator.getDurationBetween(numerator.getStart(), numerator.getEnd(), timeZone);

        BigDecimal fractionalDayOfSecondsInNumerator = BigDecimal.ZERO;
        if (durationBetween.getSeconds() != 0) {
            fractionalDayOfSecondsInNumerator = getFractionOfPartialDayToCompleteDay(durationBetween.getSeconds(), numerator.getEnd(), timeZone);
        }

        BigDecimal fractionalMonth = getRatioOfDaysInFullMonth(
            numerator,
            timeZone,
            prorationConfig,
            durationBetween.getMonths(),
            BigDecimal.valueOf(durationBetween.getDays()).add(fractionalDayOfSecondsInNumerator)
        );

        // use the actual number of months + any fractional months from days left over
        return getNormalizedMonthsProrationRatio(BigDecimal.valueOf(durationBetween.getMonths()).add(fractionalMonth), billingPeriod.getRecurrence());
    }

    private static BigDecimal getRatioOfDaysInFullMonth(
        Period targetPeriod,
        TimeZone timeZone,
        ProrationConfig prorationConfig,
        int monthsInNumerator,
        BigDecimal remainingDays
    ) {
        // if there are left over days, calculate how much of a month the days represent. This looks at the last minute in which there are remaining days
        // Example: item from Jan 1 - July 15. Jan 1 - July 1 (exclusive) represents the first 6 whole months. July 1 - July 15 is the number of remaining days
        // Depending on the configuration the actual number of days in the month is required.
        // So here, the number of days in the last month of the term (July) is determined. Then the fraction (15/31) is calculated

        // Using the above example, zonedItemStart == Jan 1 at 00:00:00
        var zonedItemStart = ZonedDateTime.ofInstant(targetPeriod.getStart(), timeZone.toZoneId());

        // moves it by number of whole months. Jan 1 + 6 months => July 1 at 00:00:00
        var itemLastMonthStart = zonedItemStart.plusMonths(monthsInNumerator);

        // what would be the end of the month if it was a full month? For july => Aug 1 at 00:00:00 (right exclusive)
        var itemLastMonthEnd = itemLastMonthStart.plusMonths(1);

        // how many days between start and end? July 1 - Aug 1 (right exclusive) == 31 days
        long daysInLastMonth = ChronoUnit.DAYS.between(itemLastMonthStart, itemLastMonthEnd);

        // taking into account Proration.Scheme (FIXED vs CALENDAR days), what fraction of a month does the remaining days of July represent?
        // => 15 / 30 (FIXED days) or 15 / 31 (CALENDAR days)
        return Numbers.scaledDivide(remainingDays, BigDecimal.valueOf(getDaysInMonth(prorationConfig, daysInLastMonth)));
    }

    private static BigDecimal getMonthFractionForSeconds(
        Period targetPeriod,
        TimeZone timeZone,
        ProrationConfig prorationConfig,
        BillyDuration durationBetween
    ) {
        // if there are left over seconds, calculate how much of a month the days represent. This looks at the last minute in which there are remaining seconds
        // Example: item from Jan 1 - July 15 + 2400 seconds. Jan 1 - July 1 (exclusive) represents the first 6 whole months. July 1 - July 15 is the number of seconds days
        // Depending on the configuration the actual number of days in the month is required.
        // So here, the number of days in the last month of the term (July) is determined. Then faction of those seconds as month is calculated (2400 / (31 * SECONDS_IN_DAY))

        // Using the above example, zonedItemStart == Jan 1 at 00:00:00
        var zonedItemStart = ZonedDateTime.ofInstant(targetPeriod.getStart(), timeZone.toZoneId());

        // moves it by number of whole months. Jan 1 + 6 months + remainder days => July 1 at 00:00:00
        var itemLastMonthStart = zonedItemStart.plusMonths(durationBetween.getMonths()).plusDays(durationBetween.getDays());

        // what would be the end of the month if it was a full month? For july => Aug 1 at 00:00:00 (right exclusive)
        var itemLastMonthEnd = itemLastMonthStart.plusMonths(1);

        // how many days between start and end? July 1 - Aug 1 (right exclusive) == 31 days
        long daysInLastMonth = ChronoUnit.DAYS.between(itemLastMonthStart, itemLastMonthEnd);

        // taking into account Proration.Scheme (FIXED vs CALENDAR days), what fraction of a month does the remaining days of July represent?
        // => 15 / 30 (FIXED days) or 15 / 31 (CALENDAR days)
        return getNormalizedFractionalMonthForSeconds(durationBetween.getSeconds(), getDaysInMonth(prorationConfig, daysInLastMonth));
    }

    /*
    How many days of month to consider for proration purposes depending on proration configuration.
     */
    private static long getDaysInMonth(ProrationConfig prorationConfig, long daysInLastMonth) {
        return prorationConfig.getScheme() == ProrationConfig.Scheme.CALENDAR_DAYS ? daysInLastMonth : (long) Cycle.MONTH.getFixedDays();
    }

    private static BigDecimal getNormalizedMonthsProrationRatio(BigDecimal months, Recurrence billingCycle) {
        return switch (billingCycle.getCycle()) {
            case YEAR, SEMI_ANNUAL, QUARTER -> {
                BigDecimal monthsByBillingCycleMonths = Numbers.scaledDivide(months, BigDecimal.valueOf(billingCycle.getCycle().getNumberOfMonths()));
                yield Numbers.scaledDivide(monthsByBillingCycleMonths, BigDecimal.valueOf(billingCycle.getStep()));
            }
            case MONTH -> Numbers.scaledDivide(months, BigDecimal.valueOf(billingCycle.getStep()));
            case DAY, PAID_IN_FULL, CUSTOM -> throw new InvariantCheckFailedException(
                "DAY, PAID_IN_FULL and CUSTOM cycles are incompatible with normalized proration"
            );
        };
    }

    private static BigDecimal getNormalizedFractionalMonthForSeconds(int seconds, long daysInMonth) {
        return Numbers.scaledDivide(BigDecimal.valueOf(seconds), BigDecimal.valueOf(daysInMonth).multiply(SECONDS_IN_DAY));
    }

    private static BigDecimal getNormalizedSecondsProrationRatio(int seconds, Duration prorateOverDuration) {
        long billingPeriodInSeconds = prorateOverDuration.getSeconds();
        return Numbers.scaledDivide(BigDecimal.valueOf(seconds), BigDecimal.valueOf(billingPeriodInSeconds));
    }

    private static BigDecimal getPreciseProrationRatio(Duration prorateOnDuration, Duration prorationUsingDuration) {
        return Numbers.scaledDivide(BigDecimal.valueOf(prorationUsingDuration.getSeconds()), BigDecimal.valueOf(prorateOnDuration.getSeconds()));
    }

    private static BigDecimal getPreciseDaysProrationRatio(
        Period denominator,
        Period numerator,
        TimeZone timeZone,
        ProrationVersion prorationVersion
    ) {
        // the 2 version below should only differ if there are fractional days in either the numerator or denominator. i.e. not starting or ending at midnight
        BigDecimal v1Ratio = getPreciseDaysProrationRatioV1(denominator, numerator, timeZone);
        BigDecimal v2Ratio = getPreciseDaysProrationRatioV2(denominator, numerator, timeZone);

        if (v1Ratio.compareTo(v2Ratio) != 0) {
            logWarningForProrationRatio(numerator, denominator, v1Ratio, v2Ratio, "EXACT_DAYS");
        }

        return switch (prorationVersion) {
            case V1 -> v1Ratio;
            case V2 -> v2Ratio;
        };
    }

    // get ratio of service period days to total days using full days only
    private static BigDecimal getPreciseDaysProrationRatioV1(Period prorateOnDuration, Period prorationOverlapPeriod, TimeZone timeZone) {
        if (prorationOverlapPeriod.toDuration().isZero() || prorationOverlapPeriod.toDuration().isZero()) {
            // if numerator is 0, proration ratio is 0.
            // If denominator is also 0, proration ratio isn't valid mathematically, but from business logic perspect can consider no impact to proration (0)
            return BigDecimal.ZERO;
        }

        long prorateOnDays = DateTimeCalculator.getDaysBetween(prorateOnDuration.getStart(), prorateOnDuration.getEnd(), timeZone);
        long prorationUsingDays = DateTimeCalculator.getDaysBetween(prorationOverlapPeriod.getStart(), prorationOverlapPeriod.getEnd(), timeZone);

        return Numbers.scaledDivide(BigDecimal.valueOf(prorationUsingDays), BigDecimal.valueOf(prorateOnDays));
    }

    // get ratio of service period days to total days taking into account partial days for both numerator and denominator
    private static BigDecimal getPreciseDaysProrationRatioV2(Period denominator, Period numerator, TimeZone timeZone) {
        if (numerator.toDuration().isZero() || numerator.toDuration().isZero()) {
            // if numerator is 0, proration ratio is 0.
            // If denominator is also 0, proration ratio isn't valid mathematically, but from business logic perspect can consider no impact to proration (0)
            return BigDecimal.ZERO;
        }

        BigDecimal numeratorDays = getDaysInPeriod(numerator, timeZone);
        BigDecimal denominatorDays = getDaysInPeriod(denominator, timeZone);

        return Numbers.scaledDivide(numeratorDays, denominatorDays);
    }

    // Calculate the total number of seconds from fraction of day and total number of seconds in the particular day
    private static long getSecondsInFractionalDay(BigDecimal fractionOfDay, Instant fractionalDayStart, TimeZone timeZone) {
        Instant fractionalDayEnd = DateTimeCalculator.plusDays(timeZone.toZoneId(), fractionalDayStart, 1);
        long secondsInFractionalDay = Duration.between(fractionalDayStart, fractionalDayEnd).getSeconds();
        return Numbers.round(fractionOfDay.multiply(BigDecimal.valueOf(secondsInFractionalDay)));
    }

    private static long getTotalDaysInMonth(Instant fractionalMonthStart, TimeZone timeZone, ProrationConfig prorationConfig) {
        Instant fractionalMonthEnd = DateTimeCalculator.plusMonths(timeZone.toZoneId(), fractionalMonthStart, 1);
        long calendarDays = DateTimeCalculator.getDaysBetween(fractionalMonthStart, fractionalMonthEnd, timeZone);
        return getDaysInMonth(prorationConfig, calendarDays);
    }

    // compute the number of days taking into account partial days using the timezone and end date of the period
    private static BigDecimal getDaysInPeriod(Period period, TimeZone timeZone) {
        BillyDuration duration = DateTimeCalculator.getDurationBetween(period.getStart(), period.getEnd(), timeZone);
        BigDecimal completeDays = BigDecimal.valueOf(DateTimeCalculator.getDaysBetween(period.getStart(), period.getEnd(), timeZone));
        BigDecimal partialDayFromSeconds = getFractionOfPartialDayToCompleteDay(duration.getSeconds(), period.getEnd(), timeZone);
        return completeDays.add(partialDayFromSeconds);
    }

    // If there are any remaining seconds in a day, get the ratio of the number of seconds remaining over total number of seconds of the day
    // e.g if there is 1 hour remaining, the ratio is ~ 1 / 24 (differs slightly based on DST, leap seconds etc)
    private static BigDecimal getFractionOfPartialDayToCompleteDay(int remainingSeconds, Instant instanceInDay, TimeZone timeZone) {
        Instant startOfDayOfPeriodEnd = DateTimeCalculator.getStartOfDay(instanceInDay, timeZone.toZoneId());
        Instant startOfNextDay = DateTimeCalculator.plusDays(timeZone.toZoneId(), startOfDayOfPeriodEnd, 1);
        return Numbers.scaledDivide(
            BigDecimal.valueOf(remainingSeconds),
            BigDecimal.valueOf(startOfNextDay.getEpochSecond() - startOfDayOfPeriodEnd.getEpochSecond())
        );
    }

    private static Duration getProrationDurationForFixedDaysScheme(Recurrence recurrence) {
        int daysInPeriod = getDaysInPeriod(recurrence);
        return Duration.ofDays(daysInPeriod);
    }

    private static int getDaysInPeriod(Recurrence recurrence) {
        Integer step = recurrence.getStep();

        return switch (recurrence.getCycle()) {
            case DAY, MONTH, QUARTER, SEMI_ANNUAL, YEAR -> recurrence.getCycle().getFixedDays() * step;
            case PAID_IN_FULL, CUSTOM -> throw new UnsupportedOperationException("Billing cycle must be in multiples of months or years");
        };
    }

    private static void logWarningForProrationRatio(
        Period numerator,
        Period denominator,
        BigDecimal v1Ratio,
        BigDecimal v2Ratio,
        String prorationMode
    ) {
        LOGGER.warn(
            "V1 and V2 proration ratios are different for numerator: {}, denominator: {}. Proration mode: {}. V1 ratio: {}, V2 ratio: {}",
            numerator,
            denominator,
            prorationMode,
            v1Ratio,
            v2Ratio
        );
    }
}
