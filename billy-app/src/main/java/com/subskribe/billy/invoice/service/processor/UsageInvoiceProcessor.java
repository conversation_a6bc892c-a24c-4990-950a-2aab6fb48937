package com.subskribe.billy.invoice.service.processor;

import static com.subskribe.billy.invoice.service.processor.InvoiceProcessorUtility.getOrderLinesByType;
import static com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences.NULL_ATTRIBUTE_REFERENCES_KEY;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.ImmutableInvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.ListAmount;
import com.subskribe.billy.invoice.model.RatedQuantity;
import com.subskribe.billy.invoice.model.RatingInput;
import com.subskribe.billy.invoice.model.RatingOutput;
import com.subskribe.billy.invoice.service.InvoiceAmountCalculator;
import com.subskribe.billy.invoice.service.RatingService;
import com.subskribe.billy.invoice.service.processor.model.ImmutableInvoicePreviewInput;
import com.subskribe.billy.invoice.service.processor.model.InvoiceGenerationInput;
import com.subskribe.billy.invoice.service.processor.model.InvoicePreviewInput;
import com.subskribe.billy.invoice.service.processor.model.ProcessorMode;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.PricingOverride;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.ForeignExchangeUtils;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.model.SubscriptionMapper;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.usage.model.CreditBucket;
import com.subskribe.billy.usage.service.UsageStatisticsService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.ArithmeticUtils;
import org.mapstruct.factory.Mappers;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class UsageInvoiceProcessor implements InvoiceProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(UsageInvoiceProcessor.class);

    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;

    private final InvoiceAmountCalculator invoiceAmountCalculator;

    private final FeatureService featureService;

    private final RecurringInvoiceProcessor recurringInvoiceProcessor;

    private final PrepaidInvoiceProcessor prepaidInvoiceProcessor;

    private final InvoiceDAO invoiceDAO;

    private final UsageStatisticsService usageStatisticsService;

    private final RatingService ratingService;

    private final CurrencyConversionRateGetService currencyConversionRateGetService;

    private final SubscriptionMapper subscriptionMapper;

    @Inject
    public UsageInvoiceProcessor(
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        InvoiceAmountCalculator invoiceAmountCalculator,
        FeatureService featureService,
        RecurringInvoiceProcessor recurringInvoiceProcessor,
        PrepaidInvoiceProcessor prepaidInvoiceProcessor,
        InvoiceDAO invoiceDAO,
        UsageStatisticsService usageStatisticsService,
        RatingService ratingService,
        CurrencyConversionRateGetService currencyConversionRateGetService
    ) {
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.invoiceAmountCalculator = invoiceAmountCalculator;
        this.featureService = featureService;
        this.recurringInvoiceProcessor = recurringInvoiceProcessor;
        this.prepaidInvoiceProcessor = prepaidInvoiceProcessor;
        this.invoiceDAO = invoiceDAO;
        this.usageStatisticsService = usageStatisticsService;
        this.ratingService = ratingService;
        this.currencyConversionRateGetService = currencyConversionRateGetService;
        subscriptionMapper = Mappers.getMapper(SubscriptionMapper.class);
    }

    @Override
    public List<InvoiceItem> previewInvoiceItems(InvoicePreviewInput invoiceGenerationInput) {
        List<OrderLineItem> relevantOrderLines = getOrderLinesByType(
            invoiceGenerationInput.getOrderLineItemsNetEffect(),
            invoiceGenerationInput.getChargeMap(),
            ChargeType.USAGE
        );
        return previewUsageItems(invoiceGenerationInput, relevantOrderLines);
    }

    @Override
    public List<InvoiceItem> generateInvoiceItems(InvoiceGenerationInput invoiceGenerationInput) {
        List<SubscriptionCharge> usageSubscriptionCharges = getUsageSubscriptionCharges(invoiceGenerationInput);
        List<OrderLineItem> usageOrderLines = getUsageOrderLines(invoiceGenerationInput);
        return getUsageInvoiceItems(invoiceGenerationInput, usageSubscriptionCharges, usageOrderLines);
    }

    private List<InvoiceItem> getUsageInvoiceItems(
        InvoiceGenerationInput invoiceGenerationInput,
        List<SubscriptionCharge> usageSubscriptionCharges,
        List<OrderLineItem> usageOrderLineItems
    ) {
        List<InvoiceItem> invoiceItems = new ArrayList<>();
        List<InvoiceItem> prepaidInvoiceItems = previewPrepaidInvoiceItems(invoiceGenerationInput);
        Map<String, Charge> chargeMap = invoiceGenerationInput.getChargeMap();
        InvoiceBillingInfo invoiceBillingInfo = invoiceGenerationInput.getInvoiceBillingInfo();
        Subscription subscription = invoiceGenerationInput.getSubscription();

        var now = Instant.now();

        for (SubscriptionCharge subscriptionCharge : usageSubscriptionCharges) {
            Charge charge = chargeMap.get(subscriptionCharge.getChargeId());
            var latestInvoiceItem = invoiceDAO.getLatestInvoiceItemBySubscriptionChargeGroupId(subscriptionCharge.getSubscriptionChargeGroupId());
            var usageChargePeriods = subscriptionBillingPeriodService.getBillingPeriods(
                invoiceBillingInfo.getSubscriptionStart(),
                invoiceBillingInfo.getSubscriptionEnd(),
                invoiceBillingInfo.getBillingAnchorDate(),
                invoiceBillingInfo.getInvoiceTargetDate(),
                invoiceBillingInfo.getTimeZone().toZoneId(),
                charge.getRecurrence(),
                BillingTerm.IN_ARREARS
            );

            List<InvoiceItem> minCommitInvoiceItems = previewMinimumCommitInvoiceItems(invoiceGenerationInput, charge, usageChargePeriods);

            invoiceItems.addAll(
                getUsageInvoiceItemsForSubscriptionCharge(
                    subscription,
                    subscriptionCharge,
                    chargeMap,
                    usageOrderLineItems,
                    latestInvoiceItem,
                    usageChargePeriods,
                    minCommitInvoiceItems,
                    prepaidInvoiceItems,
                    now,
                    invoiceGenerationInput.getInvoiceProcessorModes().isModeSet(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS)
                )
            );
        }

        return invoiceItems;
    }

    private List<InvoiceItem> getUsageInvoiceItemsForSubscriptionCharge(
        Subscription subscription,
        SubscriptionCharge subscriptionCharge,
        Map<String, Charge> chargeMap,
        List<OrderLineItem> usageOrderLineItems,
        Optional<InvoiceItem> latestInvoiceItem,
        List<BillingPeriod> usageChargePeriods,
        List<InvoiceItem> minCommitInvoiceItems,
        List<InvoiceItem> prepaidInvoiceItems,
        Instant now,
        boolean includePeriodInProgress
    ) {
        Charge charge = chargeMap.get(subscriptionCharge.getChargeId());
        List<InvoiceItem> items = new ArrayList<>();

        List<CreditBucket> creditBuckets = getCreditBucketsForDrawdown(subscription, subscriptionCharge, prepaidInvoiceItems, chargeMap);

        for (BillingPeriod usageChargePeriod : usageChargePeriods) {
            // calculate invoice item for usage charge period if the period has finished already and hasn't already been invoiced.
            boolean shouldGenerateUsagePeriod = (includePeriodInProgress ? usageChargePeriod.getStart() : usageChargePeriod.getEnd()).isBefore(now);
            boolean usagePeriodNotYetGenerated =
                latestInvoiceItem.isEmpty() || !latestInvoiceItem.get().getPeriodEndDate().isAfter(usageChargePeriod.getStart());
            if (shouldGenerateUsagePeriod && usagePeriodNotYetGenerated) {
                Period overlapPeriod = SubscriptionBillingPeriodService.getItemOverlapPeriod(
                    subscriptionCharge.getStartDate(),
                    subscriptionCharge.getEndDate(),
                    usageChargePeriod
                );

                if (overlapPeriod.toDuration().getSeconds() == 0) {
                    // no overlap between billing period and subscription charge period. Nothing to generate
                    continue;
                }

                Map<AttributeReferences, Long> usageQuantityByAttribute = getUsageQuantityByAttribute(
                    subscriptionCharge.getSubscriptionId(),
                    subscriptionCharge.getSubscriptionChargeGroupId(),
                    overlapPeriod.getStart(),
                    overlapPeriod.getEnd()
                );

                RatingInput ratingInput;
                if (charge.getChargeModel() == ChargeModel.RATE_CARD_LOOKUP) {
                    ratingInput = RatingInput.fromChargeAttributeReferencesMap(charge, usageQuantityByAttribute);
                } else {
                    ratingInput = buildUsageRatingInput(
                        charge,
                        usageQuantityByAttribute.getOrDefault(NULL_ATTRIBUTE_REFERENCES_KEY, 0L),
                        subscription,
                        overlapPeriod,
                        creditBuckets, // credit buckets are updated in method
                        subscriptionCharge
                    );
                }

                InvoiceItem usageItem = getUsageInvoiceItem(subscriptionCharge, usageOrderLineItems, overlapPeriod, ratingInput);
                usageItem.setIsBilled(false);

                usageItem = adjustInvoiceItemForMinimumCommitUsage(usageItem, charge, overlapPeriod, minCommitInvoiceItems);

                items.add(usageItem);
            }
        }
        return items;
    }

    private List<InvoiceItem> previewMinimumCommitInvoiceItems(
        InvoiceGenerationInput invoiceGenerationInput,
        Charge usageCharge,
        List<BillingPeriod> usageChargePeriods
    ) {
        if (StringUtils.isEmpty(usageCharge.getMinimumCommitBaseChargeId())) {
            return List.of();
        }

        List<OrderLineItem> minCommitOrderLines = invoiceGenerationInput
            .getOrderLineItemsNetEffect()
            .stream()
            .filter(li -> usageCharge.getMinimumCommitBaseChargeId().equals(li.getChargeId()))
            .toList();

        InvoiceBillingInfo minCommitBillingInfo = ImmutableInvoiceBillingInfo.copyOf(
            invoiceGenerationInput.getInvoiceBillingInfo()
        ).withInvoiceTargetDate(invoiceGenerationInput.getSubscription().getEndDate());

        return recurringInvoiceProcessor.previewInvoiceItems(
            ImmutableInvoicePreviewInput.builder()
                .invoiceBillingInfo(minCommitBillingInfo)
                .putAllChargeMap(invoiceGenerationInput.getChargeMap())
                .addAllOrderLineItems(minCommitOrderLines)
                .addAllOrderLineItemsNetEffect(minCommitOrderLines)
                .addAllTopLevelBillingPeriods(usageChargePeriods)
                .build()
        );
    }

    private List<InvoiceItem> previewPrepaidInvoiceItems(InvoiceGenerationInput invoiceGenerationInput) {
        if (invoiceGenerationInput.getChargeMap().values().stream().noneMatch(Charge::isDrawdown)) {
            return List.of();
        }
        return prepaidInvoiceProcessor.previewInvoiceItems(invoiceGenerationInput);
    }

    private List<SubscriptionCharge> getUsageSubscriptionCharges(InvoiceGenerationInput invoiceGenerationInput) {
        Map<String, Charge> chargeMap = invoiceGenerationInput.getChargeMap();
        Instant invoiceTargetDate = invoiceGenerationInput.getInvoiceBillingInfo().getInvoiceTargetDate();
        return invoiceGenerationInput
            .getSubscription()
            .getCharges()
            .stream()
            .filter(sc -> (Objects.nonNull(chargeMap.get(sc.getChargeId())) && chargeMap.get(sc.getChargeId()).getType() == ChargeType.USAGE))
            .filter(sc -> !sc.getStartDate().isAfter(invoiceTargetDate))
            .collect(Collectors.toList());
    }

    private List<OrderLineItem> getUsageOrderLines(InvoiceGenerationInput invoiceGenerationInput) {
        // filter out any order lines that are of NONE or REMOVE ActionType for usage.
        // NONE shouldn't part of the list and REMOVE is not applicable for usage invoice generation
        // since the order line is only used to link the invoice line item to as a reference.
        List<OrderLineItem> usageOrderLines = getOrderLinesByType(
            invoiceGenerationInput.getOrderLineItemsNetEffect(),
            invoiceGenerationInput.getChargeMap(),
            ChargeType.USAGE
        )
            .stream()
            .filter(item -> item.getAction() != ActionType.NONE && item.getAction() != ActionType.REMOVE)
            .collect(Collectors.toList());

        if (usageOrderLines.stream().anyMatch(item -> item.getAction() == ActionType.UPDATE)) {
            throw new InvariantCheckFailedException(
                String.format("Usage charge in subscription %s has been updated", invoiceGenerationInput.getSubscription().getSubscriptionId())
            );
        }
        return usageOrderLines;
    }

    private List<InvoiceItem> previewUsageItems(InvoicePreviewInput invoiceGenerationInput, List<OrderLineItem> usageOrderLines) {
        Map<String, Charge> chargeMap = invoiceGenerationInput.getChargeMap();
        InvoiceBillingInfo invoiceBillingInfo = invoiceGenerationInput.getInvoiceBillingInfo();

        List<InvoiceItem> invoiceItems = new ArrayList<>();

        for (OrderLineItem orderLineItem : usageOrderLines) {
            Charge charge = chargeMap.get(orderLineItem.getChargeId());
            var usageChargePeriods = subscriptionBillingPeriodService.getBillingPeriods(
                invoiceBillingInfo.getSubscriptionStart(),
                invoiceBillingInfo.getSubscriptionEnd(),
                invoiceBillingInfo.getBillingAnchorDate(),
                invoiceBillingInfo.getInvoiceTargetDate(),
                invoiceBillingInfo.getTimeZone().toZoneId(),
                charge.getRecurrence(),
                BillingTerm.IN_ARREARS
            );

            for (BillingPeriod usageChargePeriod : usageChargePeriods) {
                InvoiceItemAmounts invoiceItemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(
                    orderLineItem.getDiscounts(),
                    orderLineItem.getPredefinedDiscounts(),
                    ListAmount.of(BigDecimal.ZERO),
                    invoiceAmountCalculator.getListUnitPrice(charge, orderLineItem),
                    orderLineItem.getQuantity(),
                    orderLineItem.getListPriceOverrideRatio()
                );

                Period overlapPeriod = SubscriptionBillingPeriodService.getItemOverlapPeriod(
                    invoiceBillingInfo.getSubscriptionStart(),
                    invoiceBillingInfo.getSubscriptionEnd(),
                    usageChargePeriod
                );

                var invoiceItemBuilder = new InvoiceItem.InvoiceItemBuilder()
                    .entityId(orderLineItem.getEntityId())
                    .orderLineItemId(orderLineItem.getOrderLineId())
                    .orderId(orderLineItem.getOrderId())
                    .chargeId(charge.getChargeId())
                    .listAmount(BigDecimal.ZERO)
                    .listAmountBeforeOverride(invoiceItemAmounts.getListAmountBeforeOverride())
                    .status(InvoiceStatus.DRAFT)
                    .discountAmount(invoiceItemAmounts.getDiscountAmount())
                    .amount(invoiceItemAmounts.getSellAmount())
                    .listUnitPrice(invoiceItemAmounts.getListUnitPrice())
                    .listUnitPriceBeforeOverride(invoiceItemAmounts.getListUnitPriceBeforeOverride())
                    .sellUnitPrice(invoiceItemAmounts.getSellUnitPrice())
                    .taxAmount(BigDecimal.ZERO)
                    .quantity(0L)
                    .inlineDiscounts(invoiceItemAmounts.getLineItemDiscounts())
                    .predefinedDiscounts(invoiceItemAmounts.getTenantDiscounts())
                    .periodStartDate(overlapPeriod.getStart())
                    .periodEndDate(overlapPeriod.getEnd())
                    .triggerOn(charge.getBillingTerm(), usageChargePeriod.getStart(), usageChargePeriod.getEnd());

                InvoiceItem invoiceItem = invoiceItemBuilder.createInvoiceItem();
                invoiceItems.add(invoiceItem);
            }
        }

        return invoiceItems;
    }

    // todo: temporary implementation to get drawdown overage cost. Need to rewrite to use stored prepaid credit values
    /*
    The logic for this calculation is as follows:
    If no previous invoices exist, then it's a simple calculation of consumed quantity vs provisioned quantity.
    Otherwise, we first determine the total provisioned (all prepaid charges quantity total) quantity, then subtract any usage quantities loaded.
    However, in the previous invoices, some overage quantities may have been charged already.
    We need to deduct these from the consumed amount so they don't get double counted
    the final formula is: provisioned quantity - (consumed quantity - charged overage quantity) = remaining provisioned quantity
     */
    private List<CreditBucket> getStartingPrepaidBalances(
        Subscription subscription,
        SubscriptionCharge usageSubscriptionCharge,
        List<InvoiceItem> prepaidInvoiceItems,
        Map<String, Charge> chargeMap
    ) {
        Charge usageCharge = chargeMap.get(usageSubscriptionCharge.getChargeId());
        List<CreditBucket> creditBuckets = getCreditBucketsFromPrepaidCharges(
            subscription,
            usageSubscriptionCharge,
            prepaidInvoiceItems,
            chargeMap,
            usageCharge
        );
        List<InvoiceItem> usageHistory = invoiceDAO.getAllInvoiceItemsBySubscriptionCharge(usageSubscriptionCharge.getSubscriptionChargeId());
        if (CollectionUtils.isEmpty(usageHistory)) {
            return creditBuckets;
        }
        usageHistory = usageHistory
            .stream()
            .sorted((a, b) -> DateTimeCalculator.compareInstants(a.getPeriodEndDate(), b.getPeriodEndDate()))
            .toList();

        for (var usageItem : usageHistory) {
            Period usagePeriod = Period.between(usageItem.getPeriodStartDate(), usageItem.getPeriodEndDate());
            boolean drawdownInfoIsNull = usageItem.getDrawdownQuantityUsed() == null || usageItem.getDrawdownQuantityRemaining() == null;
            long drawdownQuantityUsed = !drawdownInfoIsNull
                ? usageItem.getDrawdownQuantityUsed()
                : getUsageQuantity(
                    subscription.getSubscriptionId(),
                    usageSubscriptionCharge.getSubscriptionChargeGroupId(),
                    usagePeriod.getStart(),
                    usagePeriod.getEnd()
                ) -
                usageItem.getQuantity();

            RatingInput ratingInput = buildDrawdownRatingInput(
                usageCharge,
                drawdownQuantityUsed,
                usagePeriod,
                creditBuckets,
                Optional.ofNullable(usageSubscriptionCharge.getPricingOverride())
            );

            if (drawdownInfoIsNull) {
                invoiceDAO.backfillUsageDrawdown(
                    usageItem,
                    ratingInput.getDrawdownQuantityUsed().orElseThrow(),
                    ratingInput.getDrawdownQuantityRemaining().orElseThrow()
                );
            }
        }

        return creditBuckets;
    }

    private static List<CreditBucket> getCreditBucketsFromPrepaidCharges(
        Subscription subscription,
        SubscriptionCharge usageSubscriptionCharge,
        List<InvoiceItem> prepaidInvoiceItems,
        Map<String, Charge> chargeMap,
        Charge usageCharge
    ) {
        return prepaidInvoiceItems
            .stream()
            .filter(invoiceItem -> {
                Charge prepaidCharge = chargeMap.get(invoiceItem.getChargeId());
                return prepaidCharge.getPlanUuid().equals(usageCharge.getPlanUuid());
            })
            .map(invoiceItem -> {
                String creditBucketId = CreditBucket.createCreditBucketId(subscription.getSubscriptionId(), usageSubscriptionCharge.getChargeId());
                return new CreditBucket(
                    null,
                    creditBucketId,
                    subscription.getSubscriptionId(),
                    invoiceItem.getSubscriptionChargeGroupId(),
                    invoiceItem.getQuantity(),
                    invoiceItem.getQuantity(),
                    invoiceItem.getPeriodStartDate(),
                    invoiceItem.getPeriodEndDate()
                );
            })
            .toList();
    }

    private List<CreditBucket> getCreditBucketsFromDb(String subscriptionId, String drawdownChargeId) {
        String creditBucketId = CreditBucket.createCreditBucketId(subscriptionId, drawdownChargeId);
        return invoiceDAO.getCreditBuckets(creditBucketId);
    }

    private List<CreditBucket> getCreditBucketsForDrawdown(
        Subscription subscription,
        SubscriptionCharge usageSubscriptionCharge,
        List<InvoiceItem> prepaidInvoiceItems,
        Map<String, Charge> chargeMap
    ) {
        if (!chargeMap.get(usageSubscriptionCharge.getChargeId()).isDrawdown()) {
            return List.of();
        }
        Validator.checkNonNullInternal(prepaidInvoiceItems, "prepaidInvoiceItems");
        if (!featureService.isEnabled(Feature.CREDIT_BUCKETS)) {
            return getStartingPrepaidBalances(subscription, usageSubscriptionCharge, prepaidInvoiceItems, chargeMap);
        }
        // todo: create credit buckets on demand if none exist
        return getCreditBucketsFromDb(subscription.getSubscriptionId(), usageSubscriptionCharge.getChargeId());
    }

    private Map<AttributeReferences, Long> getUsageQuantityByAttribute(
        String subscriptionId,
        String usageChargeGroupId,
        Instant startTime,
        Instant endTime
    ) {
        // todo: use BigDecimal directly instead of converting to Long first
        Map<AttributeReferences, BigDecimal> usageByAttribute = usageStatisticsService.getUsageSumByAttributeForChargeGroupId(
            subscriptionId,
            usageChargeGroupId,
            Period.between(startTime, endTime)
        );
        return usageByAttribute.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().longValue()));
    }

    // updates credit bucket balances
    private RatingInput buildUsageRatingInput(
        Charge charge,
        long usageQuantity,
        Subscription subscription,
        Period overlapPeriod,
        List<CreditBucket> creditBuckets,
        SubscriptionCharge subscriptionCharge
    ) {
        long ratingQuantity;
        if (featureService.isEnabled(Feature.PRECOMMIT_OVERAGE) && StringUtils.isNotBlank(charge.getOverageBaseChargeId())) {
            ratingQuantity = calculateUsageQuantityWithOverage(charge, subscription, overlapPeriod, usageQuantity);
        } else if (charge.isDrawdown()) {
            return buildDrawdownRatingInput(
                charge,
                usageQuantity,
                overlapPeriod,
                creditBuckets,
                Optional.ofNullable(subscriptionCharge.getPricingOverride())
            );
        } else {
            ratingQuantity = usageQuantity;
        }

        return RatingInput.fromChargeAndQuantity(
            charge,
            ratingQuantity,
            Optional.empty(),
            Optional.empty(),
            Optional.ofNullable(subscriptionCharge.getPricingOverride())
        );
    }

    // updates credit bucket balances
    static RatingInput buildDrawdownRatingInput(
        Charge charge,
        long usageQuantity,
        Period overlapPeriod,
        List<CreditBucket> creditBuckets,
        Optional<PricingOverride> pricingOverride
    ) {
        List<CreditBucket> filteredCreditBuckets = getFilteredCreditBuckets(creditBuckets, overlapPeriod);

        long ratingQuantity = usageQuantity;
        for (var creditBucket : filteredCreditBuckets) {
            if (ratingQuantity <= 0) {
                break;
            }
            long drawdown = Math.min(creditBucket.getRemainingQuantity(), ratingQuantity);
            ratingQuantity -= drawdown;
            creditBucket.setRemainingQuantity(creditBucket.getRemainingQuantity() - drawdown);
        }
        long drawdownQuantityUsed = Math.max(usageQuantity - ratingQuantity, 0L);
        long drawdownQuantityRemaining = filteredCreditBuckets.stream().map(CreditBucket::getRemainingQuantity).reduce(0L, Long::sum);
        return RatingInput.fromChargeAndQuantity(
            charge,
            ratingQuantity,
            Optional.of(drawdownQuantityUsed),
            Optional.of(drawdownQuantityRemaining),
            pricingOverride
        );
    }

    private InvoiceItem getUsageInvoiceItem(
        SubscriptionCharge subscriptionCharge,
        List<OrderLineItem> usageOrderLineItems,
        Period overlapPeriod,
        RatingInput ratingInput
    ) {
        Charge charge = ratingInput.getCharge();
        Map<AttributeReferences, Long> usageQuantityByAttribute = ratingInput.getQuantityByAttributeReferences();
        RatingOutput ratingOutput = ratingService.performRating(
            ratingInput,
            getCurrencyConversionRate(subscriptionCharge.getCurrencyConversionRateId())
        );
        BigDecimal listAmount = ratingOutput
            .ratedQuantityByAttributeReferences()
            .values()
            .stream()
            .map(RatedQuantity::getRatedValue)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        Long quantity = reduceAttributeReferencesQuantityValues(usageQuantityByAttribute);

        // Apply currency conversion if necessary
        BigDecimal listUnitPrice = charge.getListUnitPriceByQuantity(quantity, Optional.ofNullable(subscriptionCharge.getPricingOverride()));
        listUnitPrice = subscriptionCharge.getCurrencyConversionRateId() != null
            ? ForeignExchangeUtils.applyExchangeRate(
                listUnitPrice,
                getCurrencyConversionRate(subscriptionCharge.getCurrencyConversionRateId()),
                featureService
            )
            : listUnitPrice;

        InvoiceItemAmounts invoiceItemAmounts = invoiceAmountCalculator.getInvoiceItemAmountsOld(
            subscriptionMapper.discountToDiscountDetail(subscriptionCharge.getDiscounts()),
            subscriptionCharge.getPredefinedDiscounts(),
            listAmount,
            listUnitPrice,
            quantity,
            subscriptionCharge.getListPriceOverrideRatio()
        );

        List<String> orderLineItemIds = subscriptionCharge.getOrderLines();

        List<OrderLineItem> subscriptionChargeAddItems = usageOrderLineItems
            .stream()
            .filter(item -> orderLineItemIds.contains(item.getOrderLineId()))
            .filter(item -> item.getAction() != ActionType.NONE && item.getAction() != ActionType.REMOVE)
            .toList();

        String orderLineItemId;
        String orderId;

        // todo: only set order line item ID if there is a single one (i.e. no amendments) for now. Need to ensure usage line items cannot be updated
        if (subscriptionChargeAddItems.size() == 1) {
            OrderLineItem item = subscriptionChargeAddItems.get(0);
            orderLineItemId = item.getOrderLineId();
            orderId = item.getOrderId();
        } else {
            throw new InvariantCheckFailedException(
                String.format(
                    "Expected 1 order line item for subscription charge %s, found %s",
                    subscriptionCharge.getSubscriptionChargeId(),
                    subscriptionChargeAddItems.size()
                )
            );
        }

        var invoiceItemBuilder = new InvoiceItem.InvoiceItemBuilder()
            // TODO: multi-entity - set entity ID from subscription charge
            .chargeId(subscriptionCharge.getChargeId())
            .orderId(orderId)
            .orderLineItemId(orderLineItemId)
            .subscriptionChargeId(subscriptionCharge.getSubscriptionChargeId())
            .subscriptionChargeGroupId(subscriptionCharge.getSubscriptionChargeGroupId())
            .status(InvoiceStatus.DRAFT)
            .listAmountBeforeOverride(invoiceItemAmounts.getListAmountBeforeOverride())
            .listAmount(listAmount)
            .discountAmount(invoiceItemAmounts.getDiscountAmount())
            .predefinedDiscounts(invoiceItemAmounts.getTenantDiscounts())
            .inlineDiscounts(invoiceItemAmounts.getLineItemDiscounts())
            .amount(invoiceItemAmounts.getSellAmount())
            .listUnitPriceBeforeOverride(invoiceItemAmounts.getListUnitPriceBeforeOverride())
            .listUnitPrice(invoiceItemAmounts.getListUnitPrice())
            .sellUnitPrice(invoiceItemAmounts.getSellUnitPrice())
            .taxAmount(BigDecimal.ZERO)
            .quantity(quantity)
            .drawdownQuantityUsed(ratingInput.getDrawdownQuantityUsed().orElse(null))
            .drawdownQuantityRemaining(ratingInput.getDrawdownQuantityRemaining().orElse(null))
            .periodStartDate(overlapPeriod.getStart())
            .periodEndDate(overlapPeriod.getEnd());

        return invoiceItemBuilder.createInvoiceItem();
    }

    private InvoiceItem adjustInvoiceItemForMinimumCommitUsage(
        InvoiceItem usageItem,
        Charge charge,
        Period overlapPeriod,
        List<InvoiceItem> minCommitInvoiceItems
    ) {
        if (StringUtils.isBlank(charge.getMinimumCommitBaseChargeId())) {
            return usageItem;
        }

        String minimumCommitBaseChargeId = charge.getMinimumCommitBaseChargeId();
        if (CollectionUtils.isEmpty(minCommitInvoiceItems)) {
            String message = String.format("no min commit invoice items found for min commit charge id %s", minimumCommitBaseChargeId);
            LOGGER.error(message);
            throw new InvariantCheckFailedException(message);
        }

        List<InvoiceItem> minCommitWithOverlap = minCommitInvoiceItems
            .stream()
            .filter(invoiceItem -> minimumCommitBaseChargeId.equals(invoiceItem.getChargeId()))
            .filter(
                invoiceItem ->
                    invoiceItem.getPeriodStartDate().isBefore(overlapPeriod.getEnd()) &&
                    invoiceItem.getPeriodEndDate().isAfter(overlapPeriod.getStart())
            )
            .toList();
        if (CollectionUtils.isEmpty(minCommitWithOverlap)) {
            String message = String.format(
                "no base minimum commit charge %s found for usage charge id %s between %s and %s",
                minimumCommitBaseChargeId,
                usageItem.getChargeId(),
                DateTimeConverter.instantToLocalDateTime(overlapPeriod.getStart()),
                DateTimeConverter.instantToLocalDateTime(overlapPeriod.getEnd())
            );
            LOGGER.error(message);
            throw new InvariantCheckFailedException(message);
        }

        BigDecimal minimumCommitTotal = minCommitWithOverlap.stream().map(InvoiceItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // todo: work through negative cases
        // we can use the usage charge recurrence because we validate that the usage charge and minimum commit have the same recurrence
        BigDecimal usageAmount = usageItem.getAmount().compareTo(minimumCommitTotal) >= 0
            ? usageItem.getAmount().subtract(minimumCommitTotal)
            : BigDecimal.ZERO;

        // todo: discount calculation, currently unnecessary
        return InvoiceItem.InvoiceItemBuilder.builder().from(usageItem).amount(usageAmount).createInvoiceItem();
    }

    private Long getUsageQuantity(String subscriptionId, String usageChargeGroupId, Instant startTime, Instant endTime) {
        Map<AttributeReferences, Long> quantityByAttribute = getUsageQuantityByAttribute(subscriptionId, usageChargeGroupId, startTime, endTime);
        return reduceAttributeReferencesQuantityValues(quantityByAttribute);
    }

    private long calculateUsageQuantityWithOverage(Charge charge, Subscription subscription, Period overlapPeriod, long usageQuantity) {
        String overageBaseChargeId = charge.getOverageBaseChargeId();
        List<SubscriptionCharge> overageBaseSubscriptionCharges = subscription
            .getCharges()
            .stream()
            .filter(sc -> sc.getChargeId().equals(overageBaseChargeId))
            .toList();
        if (overageBaseSubscriptionCharges.isEmpty()) {
            String message = String.format("No base overage charge %s found for usage charge %s", overageBaseChargeId, charge.getChargeId());
            LOGGER.warn(message);
            throw new InvariantCheckFailedException(message);
        }

        long totalCommitQuantity = overageBaseSubscriptionCharges
            .stream()
            .filter(sc -> !sc.getStartDate().isAfter(overlapPeriod.getStart()) && !sc.getEndDate().isBefore(overlapPeriod.getEnd()))
            .map(SubscriptionCharge::getQuantity)
            .reduce(0L, Long::sum);

        // if the usage quantity is greater than total quantity committed, use the difference.
        // Otherwise, usage quantity is covered by committed quantity, use 0
        return usageQuantity > totalCommitQuantity ? usageQuantity - totalCommitQuantity : 0;
    }

    private static List<CreditBucket> getFilteredCreditBuckets(List<CreditBucket> creditBuckets, Period overlapPeriod) {
        Validator.checkNonNullInternal(creditBuckets, "creditBuckets cannot be null");
        Validator.checkNonNullInternal(overlapPeriod, "overlapPeriod cannot be null");
        // if there are no amendments, there should be only one item per period
        // sort by end date to drawdown from the periods which will expire the soonest
        return creditBuckets
            .stream()
            .filter(
                creditBucket ->
                    !overlapPeriod.getStart().isBefore(creditBucket.getPeriodStart()) && !overlapPeriod.getEnd().isAfter(creditBucket.getPeriodEnd())
            )
            .sorted(Comparator.comparing(CreditBucket::getPeriodEnd))
            .toList();
    }

    private static Long reduceAttributeReferencesQuantityValues(Map<AttributeReferences, Long> quantityByAttribute) {
        // in this function we do not care about attributes just the total quantity in that period
        return quantityByAttribute.values().stream().reduce(0L, ArithmeticUtils::addAndCheck);
    }

    private CurrencyConversionRate getCurrencyConversionRate(UUID currencyConversionRateId) {
        if (currencyConversionRateId == null) {
            return null;
        }
        return currencyConversionRateGetService.getCurrencyConversionRateById(currencyConversionRateId.toString()).orElse(null);
    }
}
