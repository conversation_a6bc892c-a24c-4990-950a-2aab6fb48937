package com.subskribe.billy.invoice.automated.service;

import com.subskribe.billy.auth.authorizers.JobOnly;
import com.subskribe.billy.cron.CronExpressionValidator;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.automated.db.AutomatedInvoiceRuleDAO;
import com.subskribe.billy.invoice.automated.model.AutomatedInvoiceRule;
import com.subskribe.billy.invoice.automated.model.ImmutableAutomatedInvoiceRule;
import com.subskribe.billy.resources.json.invoice.AutomatedInvoiceRuleRequestJson;
import com.subskribe.billy.validation.Validator;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import javax.inject.Inject;
import org.quartz.CronExpression;

public class AutomatedInvoiceRuleService {

    private final AutomatedInvoiceRuleDAO automatedInvoiceRuleDAO;

    private final AutomatedInvoiceRuleIdGenerator automatedInvoiceRuleIdGenerator;

    private final EntityContextResolver entityContextResolver;

    @Inject
    public AutomatedInvoiceRuleService(
        AutomatedInvoiceRuleDAO automatedInvoiceRuleDAO,
        AutomatedInvoiceRuleIdGenerator automatedInvoiceRuleIdGenerator,
        EntityContextResolver entityContextResolver
    ) {
        this.automatedInvoiceRuleDAO = automatedInvoiceRuleDAO;
        this.automatedInvoiceRuleIdGenerator = automatedInvoiceRuleIdGenerator;
        this.entityContextResolver = entityContextResolver;
    }

    public AutomatedInvoiceRule addAutomatedInvoiceRule(AutomatedInvoiceRuleRequestJson automatedInvoiceRuleRequestJson) {
        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(automatedInvoiceRuleRequestJson.getEntityIds());
        AutomatedInvoiceRule automatedInvoiceRule = getAutomatedInvoiceRuleFromRequest(automatedInvoiceRuleRequestJson, entityIds);
        String automatedInvoiceRuleId = automatedInvoiceRuleIdGenerator.generate();
        return automatedInvoiceRuleDAO.addAutomatedInvoiceRule(automatedInvoiceRule, automatedInvoiceRuleId);
    }

    private AutomatedInvoiceRule getAutomatedInvoiceRuleFromRequest(
        AutomatedInvoiceRuleRequestJson automatedInvoiceRuleRequestJson,
        Set<String> entityIds
    ) {
        if (CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(automatedInvoiceRuleRequestJson.getCronExpression())) {
            throw new InvalidInputException(
                String.format(
                    "Cron expression is too frequent. It needs to be configured with a frequency of at most %s hours.",
                    CronExpressionValidator.MAX_HOURS
                )
            );
        }

        if (!CronExpression.isValidExpression(automatedInvoiceRuleRequestJson.getCronExpression())) {
            throw new InvalidInputException(
                String.format("Cron expression is not valid for automated invoice rule with name: %s", automatedInvoiceRuleRequestJson.getName())
            );
        }
        Integer targetDuration = automatedInvoiceRuleRequestJson.getTargetDuration() == null
            ? Integer.valueOf(0)
            : automatedInvoiceRuleRequestJson.getTargetDuration();
        Integer invoiceDuration = automatedInvoiceRuleRequestJson.getInvoiceDuration() == null
            ? Integer.valueOf(0)
            : automatedInvoiceRuleRequestJson.getInvoiceDuration();
        Boolean includeUsageCharge = automatedInvoiceRuleRequestJson.getIncludeUsageCharge() == null
            ? Boolean.TRUE
            : automatedInvoiceRuleRequestJson.getIncludeUsageCharge();
        Boolean includeNonUsageCharge = automatedInvoiceRuleRequestJson.getIncludeNonUsageCharge() == null
            ? Boolean.TRUE
            : automatedInvoiceRuleRequestJson.getIncludeNonUsageCharge();
        Boolean autoPostInvoice = automatedInvoiceRuleRequestJson.getAutoPostInvoice() == null
            ? Boolean.FALSE
            : automatedInvoiceRuleRequestJson.getAutoPostInvoice();
        Boolean autoEmailInvoice = automatedInvoiceRuleRequestJson.getAutoEmailInvoice() == null
            ? Boolean.FALSE
            : automatedInvoiceRuleRequestJson.getAutoEmailInvoice();

        Boolean enabled = automatedInvoiceRuleRequestJson.getEnabled() == null ? Boolean.FALSE : automatedInvoiceRuleRequestJson.getEnabled();
        return ImmutableAutomatedInvoiceRule.builder()
            .name(automatedInvoiceRuleRequestJson.getName())
            .entityIds(entityIds)
            .description(automatedInvoiceRuleRequestJson.getDescription())
            .cronExpression(automatedInvoiceRuleRequestJson.getCronExpression())
            .firstExecutionDate(Instant.ofEpochSecond(automatedInvoiceRuleRequestJson.getFirstExecutionDate()))
            .lastExecutionDate(Instant.ofEpochSecond(automatedInvoiceRuleRequestJson.getFirstExecutionDate()))
            .targetDuration(targetDuration)
            .invoiceDuration(invoiceDuration)
            .includeUsageCharge(includeUsageCharge)
            .includeNonUsageCharge(includeNonUsageCharge)
            .autoPostInvoice(autoPostInvoice)
            .autoEmailInvoice(autoEmailInvoice)
            .enabled(enabled)
            .createdOn(Instant.now())
            .updatedOn(Instant.now())
            .build();
    }

    public void updateLastAndNextExecutionDate(
        AutomatedInvoiceRule automatedInvoiceRule,
        String entityId,
        Instant lastExecutionInstant,
        Instant nextExecutionInstant
    ) {
        automatedInvoiceRuleDAO.updateLastAndNextExecutionDate(automatedInvoiceRule, lastExecutionInstant, nextExecutionInstant);
        automatedInvoiceRuleDAO.updateLastAndNextExecutionDateForEntity(automatedInvoiceRule, entityId, lastExecutionInstant, nextExecutionInstant);
    }

    @Deprecated
    public AutomatedInvoiceRule updateAutomatedInvoiceRule(
        String automatedInvoiceRuleInternalId,
        AutomatedInvoiceRuleRequestJson automatedInvoiceRuleUpdateJson
    ) {
        AutomatedInvoiceRule automatedInvoiceRule = automatedInvoiceRuleDAO.getAutomatedInvoiceRule(automatedInvoiceRuleInternalId);
        if (automatedInvoiceRule == null) {
            throw new InvalidInputException(String.format("Automated invoice rule with id: %s does not exist", automatedInvoiceRuleInternalId));
        }

        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            automatedInvoiceRuleUpdateJson.getEntityIds(),
            automatedInvoiceRule.getEntityIds()
        );

        AutomatedInvoiceRule updatedAutomatedInvoiceRule = getAutomatedInvoiceRuleFromRequest(automatedInvoiceRuleUpdateJson, entityIds);
        return automatedInvoiceRuleDAO.updateAutomatedInvoiceRule(automatedInvoiceRuleInternalId, updatedAutomatedInvoiceRule);
    }

    public void updateAutomatedInvoiceRuleUsingAutomatedInvoiceRuleId(
        String automatedInvoiceRuleId,
        AutomatedInvoiceRuleRequestJson automatedInvoiceRuleUpdateJson
    ) {
        AutomatedInvoiceRule automatedInvoiceRule = automatedInvoiceRuleDAO.getAutomatedInvoiceRuleById(automatedInvoiceRuleId);
        if (automatedInvoiceRule == null) {
            throw new InvalidInputException(String.format("Automated invoice rule with id: %s does not exist", automatedInvoiceRuleId));
        }

        Set<String> entityIds = entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(
            automatedInvoiceRuleUpdateJson.getEntityIds(),
            automatedInvoiceRule.getEntityIds()
        );

        AutomatedInvoiceRule updatedAutomatedInvoiceRule = getAutomatedInvoiceRuleFromRequest(automatedInvoiceRuleUpdateJson, entityIds);
        automatedInvoiceRuleDAO.updateAutomatedInvoiceRuleUsingAutomatedInvoiceRuleId(
            automatedInvoiceRuleId,
            automatedInvoiceRule.getId(),
            updatedAutomatedInvoiceRule
        );
    }

    @JobOnly
    public void addHumanFriendlyIdToAutomatedInvoiceRule(UUID internalId) {
        Validator.validateNonNullArguments(internalId);
        String automatedInvoiceRuleId = automatedInvoiceRuleIdGenerator.generate();
        automatedInvoiceRuleDAO.addHumanFriendlyIdToAutomatedInvoiceRule(internalId, automatedInvoiceRuleId);
    }
}
