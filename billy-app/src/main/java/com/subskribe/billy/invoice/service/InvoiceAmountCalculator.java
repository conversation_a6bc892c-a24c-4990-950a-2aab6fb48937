package com.subskribe.billy.invoice.service;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.invoice.PriceCalculationRequestBuilder;
import com.subskribe.billy.invoice.ProrationCalculator;
import com.subskribe.billy.invoice.RoundedAmountCalculator;
import com.subskribe.billy.invoice.model.DiscountResult;
import com.subskribe.billy.invoice.model.ImmutableInvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.ItemAmounts;
import com.subskribe.billy.invoice.model.ListAmount;
import com.subskribe.billy.invoice.model.ListUnitPrice;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.PricingOverride;
import com.subskribe.billy.pricing.model.CalculatePriceRequest;
import com.subskribe.billy.pricing.services.PricingService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.shared.architecture.InvoiceModuleLimitedUse;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.ForeignExchangeUtils;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import java.util.UUID;
import javax.inject.Inject;

// TODO: move Invoice amount calculator into processor
@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class InvoiceAmountCalculator {

    private static final Logger LOGGER = LoggerFactory.getLogger(InvoiceAmountCalculator.class);

    private final RateCardService rateCardService;

    private final CurrencyConversionRateGetService currencyConversionRateGetService;

    private final DiscountCalculator discountCalculator;
    private final FeatureService featureService;

    @Inject
    public InvoiceAmountCalculator(
        RateCardService rateCardService,
        CurrencyConversionRateGetService currencyConversionRateGetService,
        DiscountCalculator discountCalculator,
        FeatureService featureService
    ) {
        this.rateCardService = rateCardService;
        this.currencyConversionRateGetService = currencyConversionRateGetService;
        this.discountCalculator = discountCalculator;
        this.featureService = featureService;
    }

    // PUBLIC METHODS
    @InvoiceModuleLimitedUse(reason = "one time amount calculation should be done via previewing a line item rather than calling it directly")
    public ListAmount calculateOneTimeListAmount(OrderLineItem item, Charge charge) {
        BigDecimal listAmountWithOverride = calculateOneTimeListAmount(item, charge, Optional.ofNullable(item.getPricingOverride()));
        BigDecimal listAmountBeforeOverride = calculateOneTimeListAmount(item, charge, Optional.empty());
        return ListAmount.of(listAmountWithOverride, listAmountBeforeOverride);
    }

    public BigDecimal calculateOneTimeListAmount(OrderLineItem item, Charge charge, Optional<PricingOverride> pricingOverride) {
        CalculatePriceRequest req = getCalculatePriceRequest(item, charge, ChargeType.ONE_TIME, pricingOverride);
        return PricingService.calculateCostWithQuantity(req.getPriceModel(), req.getQuantity(), featureService);
    }

    @InvoiceModuleLimitedUse(reason = "proration should not be called directly, invoice line items should be previewed to get scaled cost")
    public BigDecimal getScaledCost(BigDecimal chargeCost, Recurrence chargeRecurrence, Recurrence billingCycle) {
        if (chargeRecurrence.equals(billingCycle)) {
            return chargeCost;
        }

        // e.g: monthly => 1
        var chargeCycleInMonths = BigDecimal.valueOf(chargeRecurrence.getRecurrenceDurationInMonths());

        // e.g. yearly => 12
        var billingCycleInMonths = BigDecimal.valueOf(billingCycle.getRecurrenceDurationInMonths());

        // scaled price = 12 / 1 = 12 * chargeCost
        return Numbers.scaledDivide(billingCycleInMonths, chargeCycleInMonths).multiply(chargeCost);
    }

    @InvoiceModuleLimitedUse(reason = "calculate recurring amount price should also happen via preview line item and not called directly")
    public ListAmount calculateRecurringItemPrice(OrderLineItem item, Charge charge) {
        BigDecimal listAmountWithOverride = calculateRecurringItemPrice(item, charge, Optional.ofNullable(item.getPricingOverride()));
        BigDecimal listAmountBeforeOverride = calculateRecurringItemPrice(item, charge, Optional.empty());
        return ListAmount.of(listAmountWithOverride, listAmountBeforeOverride);
    }

    private BigDecimal calculateRecurringItemPrice(OrderLineItem item, Charge charge, Optional<PricingOverride> pricingOverride) {
        CalculatePriceRequest req = getCalculatePriceRequest(item, charge, ChargeType.RECURRING, pricingOverride);
        return PricingService.calculateCostWithQuantity(req.getPriceModel(), req.getQuantity(), featureService);
    }

    @InvoiceModuleLimitedUse(reason = "getting list price should also happen via preview line item and not called directly")
    public ListUnitPrice getListUnitPrice(Charge charge, OrderLineItem item) {
        Optional<BigDecimal> listUnitPriceBeforeOverride = getListUnitPrice(charge, item, Optional.empty());
        Optional<BigDecimal> listUnitPriceWithOverride = getListUnitPrice(charge, item, Optional.ofNullable(item.getPricingOverride()));
        return ListUnitPrice.of(listUnitPriceWithOverride, listUnitPriceBeforeOverride);
    }

    private Optional<BigDecimal> getListUnitPrice(Charge charge, OrderLineItem item, Optional<PricingOverride> pricingOverride) {
        if (charge.isCustom()) {
            return Optional.of(item.getCustomListUnitPrice());
        }
        CurrencyConversionRate currencyConversionRate = getCurrencyConversionRate(item.getCurrencyConversionRateId());

        // if the charge model is rate card we resolve price from rate card
        // TODO: in the future we will pass in quantity as well
        if (ChargeModel.RATE_CARD_LOOKUP == charge.getChargeModel()) {
            RateCard rateCard = loadRateCard(charge);
            BigDecimal listUnitPrice = ForeignExchangeUtils.applyExchangeRate(
                rateCard.resolvePrice(AttributeReferences.wrap(item.getAttributeReferences())).orElse(BigDecimal.ZERO),
                currencyConversionRate,
                featureService
            );
            return Optional.of(listUnitPrice);
        }

        Optional<BigDecimal> listUnitPrice = Optional.ofNullable(charge.getListUnitPriceByQuantity(item.getQuantity(), pricingOverride));

        return listUnitPrice.map(unitPrice -> ForeignExchangeUtils.applyExchangeRate(unitPrice, currencyConversionRate, featureService));
    }

    @InvoiceModuleLimitedUse(reason = "will be moved into processor package")
    void validateChargeType(ChargeType expectedType, Charge charge) {
        if (charge.getType() != expectedType) {
            String message = String.format("%s cannot be used to calculate %s amount", charge.getType(), expectedType);
            LOGGER.warn(message);
            throw new IllegalArgumentException(message);
        }
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    @InvoiceModuleLimitedUse(reason = "will be moved into processor package")
    // todo: in the future, leverage other aggregates in addition to sum of quantity when additional charges support it
    BigDecimal calculateUsageAmount(
        Charge charge,
        Long quantity,
        Optional<PricingOverride> pricingOverride,
        CurrencyConversionRate currencyConversionRate
    ) {
        validateChargeType(ChargeType.USAGE, charge);

        var req = PriceCalculationRequestBuilder.buildCalculateCostRequest(charge, quantity, pricingOverride, currencyConversionRate);

        return PricingService.calculateCostWithQuantity(req.getPriceModel(), req.getQuantity(), featureService);
    }

    @InvoiceModuleLimitedUse(reason = "will be moved into processor package")
    public ListAmount calculatePrepaidListAmount(OrderLineItem item, Charge charge) {
        BigDecimal listAmountWithOverride = calculatePrepaidListAmount(item, charge, Optional.ofNullable(item.getPricingOverride()));
        BigDecimal listAmountBeforeOverride = calculatePrepaidListAmount(item, charge, Optional.empty());
        return ListAmount.of(listAmountWithOverride, listAmountBeforeOverride);
    }

    private BigDecimal calculatePrepaidListAmount(OrderLineItem item, Charge charge, Optional<PricingOverride> pricingOverride) {
        validateChargeType(ChargeType.PREPAID, charge);

        var req = PriceCalculationRequestBuilder.buildCalculateCostRequest(
            charge,
            item.getQuantity(),
            pricingOverride,
            getCurrencyConversionRate(item.getCurrencyConversionRateId())
        );

        return PricingService.calculateCostWithQuantity(req.getPriceModel(), req.getQuantity(), featureService);
    }

    @InvoiceModuleLimitedUse(reason = "getting list price should also happen via preview line item and not called directly")
    public ListAmount calculateRecurringItemListAmount(
        OrderLineItem item,
        Charge charge,
        BillingPeriod billingPeriod,
        Period overlapPeriod,
        TimeZone timeZone,
        ProrationConfig prorationConfig
    ) {
        BigDecimal listAmountWithoutOverride = calculateRecurringItemAmount(
            item,
            charge,
            billingPeriod,
            overlapPeriod,
            timeZone,
            prorationConfig,
            Optional.empty()
        );
        if (item.getPricingOverride() != null) {
            BigDecimal listAmountWithOverride = calculateRecurringItemAmount(
                item,
                charge,
                billingPeriod,
                overlapPeriod,
                timeZone,
                prorationConfig,
                Optional.of(item.getPricingOverride())
            );
            return ListAmount.of(listAmountWithOverride, listAmountWithoutOverride);
        }
        return ListAmount.of(listAmountWithoutOverride);
    }

    private BigDecimal calculateRecurringItemAmount(
        OrderLineItem item,
        Charge charge,
        BillingPeriod billingPeriod,
        Period overlapPeriod,
        TimeZone timeZone,
        ProrationConfig prorationConfig,
        Optional<PricingOverride> pricingOverride
    ) {
        BigDecimal chargeCost = calculateRecurringItemPrice(item, charge, pricingOverride);
        BigDecimal scaledCost = getScaledCost(chargeCost, charge.getRecurrence(), billingPeriod.getRecurrence());
        return calculateProratedAmount(billingPeriod, overlapPeriod, scaledCost, timeZone, prorationConfig);
    }

    public InvoiceItemAmounts getInvoiceItemAmounts(
        List<DiscountDetail> discounts,
        List<TenantDiscountLineItem> tenantDiscounts,
        ListAmount listAmount,
        ListUnitPrice listUnitPrice,
        long quantity,
        BigDecimal listPriceOverrideRatio
    ) {
        if (!featureService.isEnabled(Feature.NEGOTIATED_PRICE_TIERS)) {
            return getInvoiceItemAmountsOld(
                discounts,
                tenantDiscounts,
                listAmount.getListAmountBeforeOverride(),
                listUnitPrice.getListUnitPrice().orElse(null),
                quantity,
                listPriceOverrideRatio
            );
        }

        BigDecimal listAmountWithOverride = listAmount.getListAmount();
        Optional<BigDecimal> listUnitPriceWithOverride = listUnitPrice.getListUnitPrice();
        BigDecimal listAmountBeforeOverride = listAmount.getListAmountBeforeOverride();
        Optional<BigDecimal> listUnitPriceBeforeOverride = listUnitPrice.getListUnitPriceBeforeOverride();
        if (listPriceOverrideRatio != null) {
            listAmountWithOverride = listAmountBeforeOverride.multiply(listPriceOverrideRatio);
            listUnitPriceWithOverride = listUnitPriceBeforeOverride.map(unitPrice -> unitPrice.multiply(listPriceOverrideRatio));
        }

        BigDecimal sellingUnitPrice = listUnitPriceWithOverride
            .map(unitPrice ->
                unitPrice.subtract(discountCalculator.calculateDiscountAmounts(unitPrice, discounts, tenantDiscounts).getTotalDiscountAmount())
            )
            .orElse(null);

        DiscountResult discountResult = discountCalculator.calculateDiscountAmounts(listAmountWithOverride, discounts, tenantDiscounts, quantity);
        BigDecimal sellAmount = listAmountWithOverride.subtract(discountResult.getTotalDiscountAmount());

        ItemAmounts roundedAmounts = RoundedAmountCalculator.getRoundedAmounts(listAmountWithOverride, sellAmount);

        boolean hasNonRatioOverride =
            !Numbers.equals(listAmountBeforeOverride, listAmountWithOverride) ||
            !Numbers.equals(listUnitPriceBeforeOverride.orElse(null), listUnitPriceWithOverride.orElse(null));
        boolean noPriceOverride = !hasNonRatioOverride && listPriceOverrideRatio == null;
        return ImmutableInvoiceItemAmounts.builder()
            .listAmount(roundedAmounts.listAmount())
            .listAmountBeforeOverride(noPriceOverride ? null : Numbers.makeCurrencyScale(listAmountBeforeOverride))
            .sellAmount(roundedAmounts.sellAmount())
            .discountAmount(roundedAmounts.discountAmount())
            .discountPercent(discountResult.getTotalDiscountPercent())
            .listUnitPrice(listUnitPriceWithOverride.map(Numbers::makePriceDisplayScale).orElse(null))
            .listUnitPriceBeforeOverride(noPriceOverride ? null : listUnitPriceBeforeOverride.map(Numbers::makePriceDisplayScale).orElse(null))
            .lineItemDiscounts(discountResult.getLineItemDiscounts())
            .tenantDiscounts(discountResult.getTenantDiscounts())
            .sellUnitPrice(Optional.ofNullable(sellingUnitPrice).map(Numbers::makePriceDisplayScale).orElse(null))
            .build();
    }

    @Deprecated
    public InvoiceItemAmounts getInvoiceItemAmountsOld(
        List<DiscountDetail> discounts,
        List<TenantDiscountLineItem> tenantDiscounts,
        BigDecimal listAmount,
        BigDecimal listUnitPrice,
        long quantity,
        BigDecimal listPriceOverrideRatio
    ) {
        BigDecimal listAmountBeforeOverride = listAmount;
        if (listPriceOverrideRatio != null) {
            listAmount = listAmountBeforeOverride.multiply(listPriceOverrideRatio);
        }

        BigDecimal listUnitPriceBeforeOverride = listUnitPrice;
        if (listUnitPrice != null && listPriceOverrideRatio != null) {
            listUnitPrice = listUnitPrice.multiply(listPriceOverrideRatio);
        }

        BigDecimal sellingUnitPrice = listUnitPrice == null
            ? null
            : listUnitPrice.subtract(discountCalculator.calculateDiscountAmounts(listUnitPrice, discounts, tenantDiscounts).getTotalDiscountAmount());

        DiscountResult discountResult = discountCalculator.calculateDiscountAmounts(listAmount, discounts, tenantDiscounts, quantity);
        BigDecimal sellAmount = listAmount.subtract(discountResult.getTotalDiscountAmount());

        ItemAmounts roundedAmounts = RoundedAmountCalculator.getRoundedAmounts(listAmount, sellAmount);

        return ImmutableInvoiceItemAmounts.builder()
            .listAmount(roundedAmounts.listAmount())
            .listAmountBeforeOverride(listPriceOverrideRatio == null ? null : Numbers.makeCurrencyScale(listAmountBeforeOverride))
            .sellAmount(roundedAmounts.sellAmount())
            .discountAmount(roundedAmounts.discountAmount())
            .discountPercent(discountResult.getTotalDiscountPercent())
            .listUnitPrice(listUnitPrice == null ? null : Numbers.makePriceDisplayScale(listUnitPrice))
            .listUnitPriceBeforeOverride(listPriceOverrideRatio == null ? null : Numbers.makePriceDisplayScale(listUnitPriceBeforeOverride))
            .lineItemDiscounts(discountResult.getLineItemDiscounts())
            .tenantDiscounts(discountResult.getTenantDiscounts())
            .sellUnitPrice(sellingUnitPrice == null ? null : Numbers.makePriceDisplayScale(sellingUnitPrice))
            .build();
    }

    // PRIVATE METHODS
    private BigDecimal calculateProratedAmount(
        BillingPeriod billingPeriod,
        Period orderLineItemPeriod,
        BigDecimal fullAmount,
        TimeZone timeZone,
        ProrationConfig prorationConfig
    ) {
        BigDecimal prorationRatio = ProrationCalculator.getProrationRatio(billingPeriod, orderLineItemPeriod, prorationConfig, timeZone);
        return fullAmount.multiply(prorationRatio);
    }

    private RateCard loadRateCard(Charge charge) {
        return rateCardService
            .getRateCard(charge.getRateCardId())
            .orElseThrow(() ->
                new InvariantCheckFailedException(
                    String.format(
                        "Recurring charge %s has %s model but could not find rate card with id %s",
                        charge.getChargeId(),
                        ChargeModel.RATE_CARD_LOOKUP,
                        charge.getRateCardId()
                    )
                )
            );
    }

    private CalculatePriceRequest getCalculatePriceRequest(
        OrderLineItem item,
        Charge charge,
        ChargeType chargeType,
        Optional<PricingOverride> pricingOverride
    ) {
        validateChargeType(chargeType, charge);

        if (charge.isCustom()) {
            BigDecimal unitPrice = chargeType == ChargeType.ONE_TIME ? item.getCustomListUnitPrice() : item.getListUnitPrice();
            return PriceCalculationRequestBuilder.buildCalculateCostRequestWithCustomAmount(
                charge,
                item.getQuantity(),
                unitPrice,
                getCurrencyConversionRate(item.getCurrencyConversionRateId())
            );
        }

        // if rate card is involved load it
        if (charge.getChargeModel() == ChargeModel.RATE_CARD_LOOKUP) {
            RateCard rateCard = loadRateCard(charge);
            return PriceCalculationRequestBuilder.buildCalculateCostRequestWithRateCard(
                charge,
                item.getQuantity(),
                rateCard,
                AttributeReferences.wrap(item.getAttributeReferences()),
                getCurrencyConversionRate(item.getCurrencyConversionRateId())
            );
        }

        return PriceCalculationRequestBuilder.buildCalculateCostRequest(
            charge,
            item.getQuantity(),
            pricingOverride,
            getCurrencyConversionRate(item.getCurrencyConversionRateId())
        );
    }

    private CurrencyConversionRate getCurrencyConversionRate(UUID currencyConversionRateId) {
        if (currencyConversionRateId == null) {
            return null;
        }
        return currencyConversionRateGetService.getCurrencyConversionRateById(currencyConversionRateId.toString()).orElse(null);
    }
}
