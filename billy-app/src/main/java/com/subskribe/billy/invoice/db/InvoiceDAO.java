package com.subskribe.billy.invoice.db;

import static com.subskribe.billy.jooq.default_schema.Tables.CREDIT_BUCKET;
import static com.subskribe.billy.jooq.default_schema.tables.Account.ACCOUNT;
import static com.subskribe.billy.jooq.default_schema.tables.CreditMemo.CREDIT_MEMO;
import static com.subskribe.billy.jooq.default_schema.tables.Invoice.INVOICE;
import static com.subskribe.billy.jooq.default_schema.tables.InvoiceBalance.INVOICE_BALANCE;
import static com.subskribe.billy.jooq.default_schema.tables.InvoiceLineItem.INVOICE_LINE_ITEM;
import static com.subskribe.billy.jooq.default_schema.tables.MemoizedInvoiceLineItem.MEMOIZED_INVOICE_LINE_ITEM;
import static com.subskribe.billy.jooq.default_schema.tables.TenantInvoiceConfiguration.TENANT_INVOICE_CONFIGURATION;
import static com.subskribe.billy.jooq.default_schema.tables.TenantInvoiceNumberSequence.TENANT_INVOICE_NUMBER_SEQUENCE;

import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.DuplicateInvoiceNumberException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.exception.InvoiceAlreadyExistsException;
import com.subskribe.billy.invoice.mapper.InvoiceMapper;
import com.subskribe.billy.invoice.mapper.TenantInvoiceConfigMapper;
import com.subskribe.billy.invoice.model.EmailNotifiersList;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.model.TenantInvoiceConfig;
import com.subskribe.billy.invoicesettlement.model.CreditMemoStatus;
import com.subskribe.billy.invoicesettlement.model.InvoiceBalance;
import com.subskribe.billy.jooq.default_schema.tables.records.CreditBucketRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.InvoiceBalanceRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.InvoiceLineItemRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.InvoiceRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.MemoizedInvoiceLineItemRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.TenantInvoiceConfigurationRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.TenantInvoiceNumberSequenceRecord;
import com.subskribe.billy.postgres.PostgresAdvisoryLock;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.usage.model.CreditBucket;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jooq.Condition;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.SelectSeekStep1;
import org.jooq.TableField;
import org.jooq.UpdateSetMoreStep;
import org.jooq.UpdateSetStep;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class InvoiceDAO {

    private static final Logger LOGGER = LoggerFactory.getLogger(InvoiceDAO.class);
    private static final String SUBSCRIPTION_INVOICE_KEY_FORMAT = "invoice/%s/%s";

    private final DSLContextProvider dslContextProvider;
    private final InvoiceMapper invoiceMapper;
    private final TenantIdProvider tenantIdProvider;
    private final FeatureService featureService;

    @Inject
    public InvoiceDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider, FeatureService featureService) {
        this.dslContextProvider = dslContextProvider;
        this.featureService = featureService;
        invoiceMapper = Mappers.getMapper(InvoiceMapper.class);
        this.tenantIdProvider = tenantIdProvider;
    }

    public Invoice addInvoice(Invoice invoice) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return dslContext.transactionResult(configuration -> addInvoiceTransaction(invoice, configuration));
    }

    private Invoice addInvoiceTransaction(Invoice invoice, Configuration configuration) {
        LocalDateTime currentTimestamp = LocalDateTime.now(ZoneOffset.UTC);
        DSLContext dslContext = DSL.using(configuration);

        tryAndAcquireLockInTransaction(invoice.getSubscriptionId(), dslContext);

        // there is an edge case where two distributed transactions can bypass checkForDraftInvoice
        // calls made previously, this checkForDraftInvoice acts as a canonical fencing barrier for all edge cases
        checkForDraftInvoice(invoice.getSubscriptionId());

        InvoiceRecord invoiceRecord = createInvoiceRecord(invoice, currentTimestamp);
        InvoiceRecord savedInvoice = dslContext.insertInto(INVOICE).set(invoiceRecord).returning().fetchOne();
        savedInvoice = getSafeRecord(savedInvoice, "Failed to save the invoice.");
        var invoiceInternalId = savedInvoice.get(INVOICE.ID);

        List<InvoiceLineItemRecord> lineItemRecords = createLineItemRecords(
            invoice.getInvoiceItems(),
            currentTimestamp,
            invoice.getEntityId(),
            invoiceInternalId
        );

        dslContext.batchInsert(lineItemRecords).execute();

        return getInvoiceById(dslContext, invoiceInternalId);
    }

    private void tryAndAcquireLockInTransaction(String subscriptionId, DSLContext dslContext) {
        // we acquire a lock on key of the form "invoice/{tenantId}/{subscriptionId}"
        // this protects invoice generation for a single subscription
        // by serializing DRAFT invoice generation for that subscription
        // any type (usage/non-usage/both) of invoice generated for a subscription will be serialized on this lock
        String tenantId = tenantIdProvider.provideTenantIdString();
        String lockKey = subscriptionLockKey(tenantId, subscriptionId);
        Optional<Long> lockHashOptional = PostgresAdvisoryLock.tryAcquireLock(dslContext, lockKey);
        // if lock cannot be acquired just raise a conflict
        if (lockHashOptional.isEmpty()) {
            LOGGER.warn("Could not acquire subscription invoice lock on {}", lockKey);
            String message = String.format("Invoice generation already in progress for subscription with id: %s", subscriptionId);
            throw new ConflictingStateException(message);
        }
        // lock acquisition was successful
    }

    private static String subscriptionLockKey(String tenantId, String subscriptionId) {
        return String.format(SUBSCRIPTION_INVOICE_KEY_FORMAT, tenantId, subscriptionId);
    }

    private InvoiceRecord createInvoiceRecord(Invoice invoice, LocalDateTime currentTimestamp) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        InvoiceRecord invoiceRecord = invoiceMapper.convertInvoiceToDBRecord(invoice);
        invoiceRecord.setCreatedOn(currentTimestamp);
        invoiceRecord.setUpdatedOn(currentTimestamp);
        invoiceRecord.setShouldRegeneratePdf(true);
        invoiceRecord.setTenantId(tenantId);
        invoiceRecord.reset(INVOICE.ID);
        return invoiceRecord;
    }

    private List<InvoiceLineItemRecord> createLineItemRecords(
        List<InvoiceItem> invoiceItems,
        LocalDateTime currentTimestamp,
        String entityId,
        UUID invoiceInternalId
    ) {
        var itemsWithInvoiceId = invoiceItems
            .stream()
            .map(item -> new InvoiceItem.InvoiceItemBuilder().from(item).invoiceId(new Invoice.InternalId(invoiceInternalId)).createInvoiceItem())
            .toList();

        return itemsWithInvoiceId
            .stream()
            .map(invoiceMapper::convertInvoiceLineItemToDBRecord)
            .map(record -> setItemRecordFieldsAndReturn(record, entityId, currentTimestamp))
            .collect(Collectors.toList());
    }

    private InvoiceLineItemRecord setItemRecordFieldsAndReturn(InvoiceLineItemRecord record, String entityId, LocalDateTime currentTimestamp) {
        record.setCreatedOn(currentTimestamp);
        record.setUpdatedOn(currentTimestamp);
        record.reset(INVOICE_LINE_ITEM.ID);
        record.setTenantId(tenantIdProvider.provideTenantIdString());
        record.setEntityId(entityId);
        return record;
    }

    private Invoice getInvoiceById(DSLContext dslContext, UUID invoiceInternalId) {
        InvoiceRecord invoiceRecord = getInvoiceRecord(invoiceInternalId, dslContext);
        return populateInvoiceItems(invoiceRecord, dslContext);
    }

    public Invoice getInvoiceById(UUID invoiceId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        InvoiceRecord invoiceRecord = getInvoiceRecord(invoiceId, dslContext);
        return invoiceMapper.convertDBRecordToInvoice(invoiceRecord);
    }

    public void checkForDraftInvoice(String subscriptionId) {
        Optional<Invoice> draftInvoiceOptional = getDraftInvoiceForSubscription(subscriptionId);
        if (draftInvoiceOptional.isEmpty()) {
            return;
        }

        if (draftInvoiceOptional.get().getStatus() == InvoiceStatus.CONVERTED) {
            throw new InvoiceAlreadyExistsException(
                "There is an invoice which is converted to a draft credit memo. Delete or post the credit memo first."
            );
        }

        throw new InvoiceAlreadyExistsException("There is an open draft invoice for this subscription. Delete or post first.");
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    public List<Invoice> getInvoicesBySubscription(
        String subscriptionId,
        Optional<InvoiceStatus> status,
        PaginationQueryParams paginationQueryParams
    ) {
        if (subscriptionId.isBlank()) {
            throw new IllegalArgumentException("subscriptionId cannot be blank");
        }

        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext
            .select()
            .from(INVOICE)
            .where(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(INVOICE.IS_DELETED.isFalse());

        if (status.isPresent()) {
            query = query.and(INVOICE.STATUS.eq(status.get().toString()));
        }

        var invoiceRecords = paginationQueryParams.executePaginationQuery(
            query,
            dslContext,
            tenantId,
            INVOICE,
            INVOICE.ID,
            INVOICE.TENANT_ID,
            INVOICE.CREATED_ON,
            InvoiceRecord.class
        );

        return invoiceMapper.convertDBRecordsToInvoices(invoiceRecords);
    }

    // StartingInstant could be either invoice createdOn or invoice date whichever is earliest.
    public List<String> getInvoiceCountForSubscriptionAfterSpecificDate(
        String subscriptionId,
        Instant startingCreatedOn,
        Instant startingInvoiceDate
    ) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        LocalDateTime startingCreatedOnLocalDate = DateTimeConverter.instantToLocalDateTime(startingCreatedOn);
        LocalDateTime startingInvoiceDateLocalDate = DateTimeConverter.instantToLocalDateTime(startingInvoiceDate);
        List<InvoiceRecord> invoicesCreatedAfterTheStartingInstant = dslContext
            .select(INVOICE.INVOICE_NUMBER, INVOICE.DRAFT_INVOICE_NUMBER)
            .from(INVOICE)
            .where(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(INVOICE.CREATED_ON.greaterThan(startingCreatedOnLocalDate).or(INVOICE.INVOICE_DATE.greaterThan(startingInvoiceDateLocalDate)))
            .and(INVOICE.STATUS.ne(InvoiceStatus.VOIDED.name()))
            .and(INVOICE.IS_DELETED.isFalse())
            .fetchInto(InvoiceRecord.class);

        return invoicesCreatedAfterTheStartingInstant.stream().map(InvoiceRecord::getInvoiceNumber).collect(Collectors.toList());
    }

    public List<Invoice> getInvoicesForDunningByDueDateInterval(Instant startDate, Instant endDate) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var invoiceRecords = dslContext
            .select()
            .from(INVOICE)
            .innerJoin(ACCOUNT)
            .on(ACCOUNT.ACCOUNT_ID.eq(INVOICE.CUSTOMER_ACCOUNT_ID))
            .where(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.DUE_DATE.between(DateTimeConverter.instantToLocalDateTime(startDate), DateTimeConverter.instantToLocalDateTime(endDate)))
            .and(INVOICE.STATUS.eq(InvoiceStatus.POSTED.name()))
            .and(INVOICE.IS_DELETED.isFalse())
            .and(ACCOUNT.EXCLUDE_FROM_DUNNING.isFalse())
            .fetchInto(InvoiceRecord.class);

        return invoiceMapper.convertDBRecordsToInvoices(invoiceRecords);
    }

    public List<Invoice> getInvoicesForAccount(String accountId, InvoiceStatus status) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var query = dslContext
            .select()
            .from(INVOICE)
            .where(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.CUSTOMER_ACCOUNT_ID.eq(accountId))
            .and(INVOICE.IS_DELETED.isFalse());

        if (status != null) {
            query = query.and(INVOICE.STATUS.eq(status.name()));
        }

        var invoiceRecords = query.fetchInto(InvoiceRecord.class);

        return invoiceMapper.convertDBRecordsToInvoices(invoiceRecords);
    }

    public Optional<Invoice> getInvoice(Invoice.Number invoiceNumber, boolean includeDeleted) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        String tenantId = tenantIdProvider.provideTenantIdString();

        var query = dslContext.select().from(INVOICE).where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber.getNumber())).and(INVOICE.TENANT_ID.eq(tenantId));

        if (!includeDeleted) {
            query = query.and(INVOICE.IS_DELETED.isFalse());
        }

        InvoiceRecord invoiceRecord = query.fetchOneInto(InvoiceRecord.class);

        if (invoiceRecord == null) {
            return Optional.empty();
        }

        Invoice invoice = populateInvoiceItems(invoiceRecord, dslContext);
        return Optional.of(invoice);
    }

    public Invoice getInvoiceByInvoiceNumber(Invoice.Number invoiceNumber) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        InvoiceRecord invoiceRecord = getInvoiceRecord(invoiceNumber.getNumber(), dslContext);
        if (invoiceRecord == null) {
            throw new ObjectNotFoundException(BillyObjectType.INVOICE, invoiceNumber.getNumber());
        }
        return populateInvoiceItems(invoiceRecord, dslContext);
    }

    // todo: remove this once getInvoiceByInvoiceNumber returns only active invoice items
    public Invoice getInvoiceWithActiveItems(Invoice.Number invoiceNumber) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        InvoiceRecord invoiceRecord = getInvoiceRecord(invoiceNumber.getNumber(), dslContext);
        if (invoiceRecord == null) {
            throw new ObjectNotFoundException(BillyObjectType.INVOICE, invoiceNumber.getNumber());
        }
        var invoiceItems = getActiveInvoiceItemsByInvoiceId(invoiceRecord.getId());
        Invoice invoice = invoiceMapper.convertDBRecordToInvoice(invoiceRecord);
        invoice.addInvoiceItems(invoiceItems);
        return invoice;
    }

    public Optional<Invoice> getLastPostedInvoiceForSubscription(String subscriptionId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        Optional<Record> optionalRecord = dslContext
            .select()
            .from(INVOICE)
            .where(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(INVOICE.STATUS.eq(InvoiceStatus.POSTED.name()).or(INVOICE.STATUS.eq(InvoiceStatus.PAID.name())))
            .and(INVOICE.IS_DELETED.isFalse())
            .orderBy(INVOICE.CREATED_ON.desc())
            .limit(1)
            .fetchOptional();

        return optionalRecord.map(r -> populateInvoiceItems((InvoiceRecord) r, dslContext));
    }

    public boolean invoiceExists(Invoice.Number invoiceNumber) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return dslContext.fetchExists(
            dslContext
                .selectFrom(INVOICE)
                .where(INVOICE.TENANT_ID.eq(tenantId))
                .and(INVOICE.INVOICE_NUMBER.eq(invoiceNumber.getNumber()).and(INVOICE.IS_DELETED.isFalse()))
        );
    }

    public Invoice getInvoiceByInvoiceNumberInTransaction(Configuration configuration, Invoice.Number invoiceNumber) {
        InvoiceRecord invoiceRecord = getInvoiceRecord(invoiceNumber.getNumber(), configuration);
        if (invoiceRecord == null) {
            throw new ObjectNotFoundException(BillyObjectType.INVOICE, invoiceNumber.getNumber());
        }
        return populateInvoiceItems(invoiceRecord, configuration);
    }

    private Invoice populateInvoiceItems(InvoiceRecord invoiceRecord, Configuration configuration) {
        var dslContext = DSL.using(configuration);
        return populateInvoiceItems(invoiceRecord, dslContext);
    }

    // todo: migrate to use `configuration`
    // todo: update below to fetch only active invoice items (non-deleted)
    private Invoice populateInvoiceItems(InvoiceRecord invoiceRecord, DSLContext dslContext) {
        var result = dslContext
            .select()
            .from(INVOICE_LINE_ITEM)
            .where(INVOICE_LINE_ITEM.INVOICE_ID.eq(invoiceRecord.getId()))
            .and(INVOICE_LINE_ITEM.TENANT_ID.eq(invoiceRecord.getTenantId()))
            .fetch();

        var invoiceItems = result
            .stream()
            .map(record -> record.into(InvoiceLineItemRecord.class))
            .map(invoiceMapper::convertDBRecordToInvoiceLineItem)
            .sorted(Comparator.comparingDouble(a -> Numbers.bigDecimalFromString(a.getInvoiceLineNumber()).doubleValue()))
            .collect(Collectors.toList());

        Invoice invoice = invoiceMapper.convertDBRecordToInvoice(invoiceRecord);
        invoice.addInvoiceItems(invoiceItems);

        return invoice;
    }

    public Invoice getInvoiceWithNoLineItems(Invoice.Number invoiceNumber) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        InvoiceRecord invoiceRecord = getInvoiceRecord(invoiceNumber.getNumber(), dslContext);
        return invoiceMapper.convertDBRecordToInvoice(invoiceRecord);
    }

    private InvoiceRecord getInvoiceRecord(String invoiceNumber, Configuration configuration) {
        var dslContext = DSL.using(configuration);
        return getInvoiceRecord(invoiceNumber, dslContext);
    }

    private InvoiceRecord getInvoiceRecord(String invoiceNumber, DSLContext dslContext) {
        String tenantId = tenantIdProvider.provideTenantIdString();

        InvoiceRecord recordByInvoiceNumber = dslContext
            .select()
            .from(INVOICE)
            .where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber))
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.IS_DELETED.isFalse())
            .fetchOneInto(InvoiceRecord.class);

        // try to fetch using the draft invoice number
        if (recordByInvoiceNumber != null) {
            return recordByInvoiceNumber;
        }

        // check to see if there is an invoice by the draft invoice number
        return dslContext
            .select()
            .from(INVOICE)
            .where(INVOICE.DRAFT_INVOICE_NUMBER.eq(invoiceNumber))
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.IS_DELETED.isFalse())
            .fetchOneInto(InvoiceRecord.class);
    }

    private InvoiceRecord getInvoiceRecord(UUID internalInvoiceId, DSLContext dslContext) {
        return dslContext.select().from(INVOICE).where(INVOICE.ID.eq(internalInvoiceId)).fetchOneInto(InvoiceRecord.class);
    }

    public Optional<Invoice> updateInvoiceTransaction(
        Configuration configuration,
        Invoice.Number invoiceNumber,
        Instant invoiceDate,
        Instant invoiceDueDate,
        String note,
        String purchaseOrderNumber,
        AccountContact billingContact,
        EmailNotifiersList emailNotifiersList,
        Optional<Invoice> invoiceFunctionalAmountUpdates
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = DSL.using(configuration);

        var update = dslContext
            .update(INVOICE)
            .set(INVOICE.INVOICE_DATE, DateTimeConverter.instantToLocalDateTime(invoiceDate))
            .set(INVOICE.NOTE, note)
            .set(INVOICE.PO_NUMBER, purchaseOrderNumber)
            .set(INVOICE.BILLING_CONTACT, invoiceMapper.serializeAccountContact(billingContact))
            .set(INVOICE.EMAIL_NOTIFIERS_LIST, invoiceMapper.serializeEmailNotifiersList(emailNotifiersList))
            .set(INVOICE.SHOULD_REGENERATE_PDF, true);

        if (invoiceFunctionalAmountUpdates.isPresent()) {
            update = prepareInvoiceUpdateStepForFunctionalAmounts(update, invoiceFunctionalAmountUpdates.get());
        }

        if (invoiceDueDate != null) {
            update = update.set(INVOICE.DUE_DATE, DateTimeConverter.instantToLocalDateTime(invoiceDueDate));
        }

        var record = update.where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber.getNumber()).and(INVOICE.TENANT_ID.eq(tenantId))).returning().fetchOne();

        if (record == null) {
            return Optional.empty();
        }

        return Optional.of(populateInvoiceItems(record, dslContext));
    }

    private UpdateSetMoreStep<InvoiceRecord> prepareInvoiceUpdateStepForFunctionalAmounts(
        UpdateSetStep<InvoiceRecord> update,
        Invoice invoiceFunctionAmounts
    ) {
        return update
            .set(INVOICE.EXCHANGE_RATE_ID, invoiceFunctionAmounts.getExchangeRateId())
            .set(INVOICE.EXCHANGE_RATE, invoiceFunctionAmounts.getExchangeRate())
            .set(INVOICE.EXCHANGE_RATE_DATE, DateTimeConverter.instantToLocalDateTime(invoiceFunctionAmounts.getExchangeRateDate()))
            .set(INVOICE.FUNCTIONAL_TOTAL, invoiceFunctionAmounts.getFunctionalTotal())
            .set(INVOICE.FUNCTIONAL_SUB_TOTAL, invoiceFunctionAmounts.getFunctionalSubTotal())
            .set(INVOICE.FUNCTIONAL_TOTAL_DISCOUNT, invoiceFunctionAmounts.getFunctionalTotalDiscount())
            .set(INVOICE.FUNCTIONAL_TAX_TOTAL, invoiceFunctionAmounts.getFunctionalTaxTotal());
    }

    public void updateInvoiceErpId(Invoice.Number invoiceNumber, String erpId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var record = dslContext
            .update(INVOICE)
            .set(INVOICE.ERP_ID, erpId)
            .where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .returning()
            .fetchOne();

        if (record == null) {
            throw new ServiceFailureException(String.format("unable to update invoice %s with erp id %s", invoiceNumber, erpId));
        }
    }

    public Invoice postInvoice(
        Configuration configuration,
        Invoice.Number invoiceNumber,
        Invoice.Number draftInvoiceNumber,
        Instant invoiceDate,
        Instant invoicePostDate,
        Instant invoiceDueDate,
        Optional<Invoice> invoiceFunctionalAmountUpdates
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = DSL.using(configuration);

        final InvoiceStatus invoiceStatus = InvoiceStatus.POSTED;

        var updateQuery = dslContext
            .update(INVOICE)
            .set(INVOICE.STATUS, InvoiceStatus.POSTED.name())
            .set(INVOICE.INVOICE_NUMBER, invoiceNumber.getNumber())
            // when invoice is POSTED the tax transaction code is the invoice number, so we can set the tax transaction code to NULL
            .setNull(INVOICE.TAX_TRANSACTION_CODE)
            .set(INVOICE.POSTED_DATE, DateTimeConverter.instantToLocalDateTime(invoicePostDate))
            .set(INVOICE.INVOICE_DATE, DateTimeConverter.instantToLocalDateTime(invoiceDate))
            .set(INVOICE.DUE_DATE, DateTimeConverter.instantToLocalDateTime(invoiceDueDate))
            .set(INVOICE.SHOULD_REGENERATE_PDF, true);

        if (invoiceFunctionalAmountUpdates.isPresent()) {
            Invoice invoiceFunctionAmounts = invoiceFunctionalAmountUpdates.get();
            updateQuery = updateQuery
                .set(INVOICE.EXCHANGE_RATE_ID, invoiceFunctionAmounts.getExchangeRateId())
                .set(INVOICE.EXCHANGE_RATE, invoiceFunctionAmounts.getExchangeRate())
                .set(INVOICE.EXCHANGE_RATE_DATE, DateTimeConverter.instantToLocalDateTime(invoiceFunctionAmounts.getExchangeRateDate()))
                .set(INVOICE.FUNCTIONAL_TOTAL, invoiceFunctionAmounts.getFunctionalTotal())
                .set(INVOICE.FUNCTIONAL_SUB_TOTAL, invoiceFunctionAmounts.getFunctionalSubTotal())
                .set(INVOICE.FUNCTIONAL_TOTAL_DISCOUNT, invoiceFunctionAmounts.getFunctionalTotalDiscount())
                .set(INVOICE.FUNCTIONAL_TAX_TOTAL, invoiceFunctionAmounts.getFunctionalTaxTotal());
        }

        InvoiceRecord updatedRecord = updateQuery
            .where(INVOICE.INVOICE_NUMBER.eq(draftInvoiceNumber.getNumber()))
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .returning()
            .fetchOne();
        if (updatedRecord == null) {
            throw new ObjectNotFoundException(BillyObjectType.INVOICE, invoiceNumber.getNumber());
        }

        if (invoiceFunctionalAmountUpdates.isPresent()) {
            List<InvoiceItem> invoiceItems = invoiceFunctionalAmountUpdates.get().getInvoiceItems();
            for (InvoiceItem invoiceItem : invoiceItems) {
                dslContext
                    .update(INVOICE_LINE_ITEM)
                    .set(INVOICE_LINE_ITEM.STATUS, invoiceStatus.name())
                    .set(INVOICE_LINE_ITEM.FUNCTIONAL_AMOUNT, invoiceItem.getFunctionalAmount())
                    .set(INVOICE_LINE_ITEM.FUNCTIONAL_LIST_AMOUNT, invoiceItem.getFunctionalListAmount())
                    .set(INVOICE_LINE_ITEM.FUNCTIONAL_DISCOUNT_AMOUNT, invoiceItem.getFunctionalDiscountAmount())
                    .set(INVOICE_LINE_ITEM.FUNCTIONAL_TAX_AMOUNT, invoiceItem.getFunctionalTaxAmount())
                    .where(INVOICE_LINE_ITEM.ID.eq(UUID.fromString(invoiceItem.getId().id())))
                    .and(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
                    .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
                    .execute();
            }
        } else {
            dslContext
                .update(INVOICE_LINE_ITEM)
                .set(INVOICE_LINE_ITEM.STATUS, invoiceStatus.name())
                .where(INVOICE_LINE_ITEM.INVOICE_ID.eq(updatedRecord.getId()))
                .and(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
                .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
                .execute();
        }

        InvoiceBalanceRecord invoiceBalanceRecord = getInvoiceBalanceRecord(updatedRecord);
        invoiceBalanceRecord.reset(INVOICE_BALANCE.ID);
        dslContext.insertInto(INVOICE_BALANCE).set(invoiceBalanceRecord).execute();

        return populateInvoiceItems(updatedRecord, dslContext);
    }

    private static InvoiceBalanceRecord getInvoiceBalanceRecord(InvoiceRecord invoiceRecord) {
        LocalDateTime localDateTime = DateTimeConverter.instantToLocalDateTime(Instant.now());
        InvoiceBalanceRecord invoiceBalanceRecord = new InvoiceBalanceRecord();
        invoiceBalanceRecord.setBalance(invoiceRecord.getTotal());
        invoiceBalanceRecord.setCreatedOn(localDateTime);
        invoiceBalanceRecord.setUpdatedOn(localDateTime);
        invoiceBalanceRecord.setInvoiceNumber(invoiceRecord.getInvoiceNumber());
        invoiceBalanceRecord.setTenantId(invoiceRecord.getTenantId());
        invoiceBalanceRecord.setCurrencyCode(invoiceRecord.getCurrencyCode());
        invoiceBalanceRecord.setCustomerAccountId(invoiceRecord.getCustomerAccountId());

        return invoiceBalanceRecord;
    }

    public Invoice deleteInvoice(Invoice.Number invoiceNumber, InvoiceStatus expectedStatus) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        return dslContext.transactionResult(configuration ->
            softDeleteInvoiceAndLineItems(DSL.using(configuration), invoiceNumber, expectedStatus, tenantId)
        );
    }

    public Invoice deleteInvoice(Configuration configuration, Invoice.Number invoiceNumber, InvoiceStatus expectedStatus) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return softDeleteInvoiceAndLineItems(DSL.using(configuration), invoiceNumber, expectedStatus, tenantId);
    }

    private Invoice softDeleteInvoiceAndLineItems(
        DSLContext dslContext,
        Invoice.Number invoiceNumber,
        InvoiceStatus expectedStatus,
        String tenantId
    ) {
        var deletedInvoiceRecord = dslContext
            .update(INVOICE)
            .set(INVOICE.IS_DELETED, true)
            .where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber.getNumber()).and(INVOICE.TENANT_ID.eq(tenantId)))
            .and(INVOICE.STATUS.eq(expectedStatus.name()))
            .returning()
            .fetchOne();

        if (deletedInvoiceRecord == null) {
            return null;
        }

        deleteInvoiceLineItems(dslContext, deletedInvoiceRecord.getId(), tenantId);

        return populateInvoiceItems(deletedInvoiceRecord, dslContext);
    }

    private void deleteInvoiceLineItems(DSLContext dslContext, UUID invoiceId, String tenantId) {
        dslContext
            .update(INVOICE_LINE_ITEM)
            .set(INVOICE_LINE_ITEM.IS_DELETED, true)
            .where(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.INVOICE_ID.eq(invoiceId))
            .execute();
    }

    public List<InvoiceItem> getAllInvoiceItemsForSubscription(String subscriptionId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        // TODO: Possibly use a join statement to get all invoice items.
        Result<Record> result = dslContext
            .select()
            .from(INVOICE)
            .where(INVOICE.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(INVOICE.STATUS.eq(InvoiceStatus.POSTED.name()))
            .fetch();

        var invoiceIds = result.stream().map(record -> record.into(InvoiceRecord.class)).map(InvoiceRecord::getId).collect(Collectors.toList());

        return getInvoiceItemsForInvoiceIds(dslContext, invoiceIds);
    }

    public Optional<InvoiceItem> getLatestInvoiceItemBySubscriptionChargeGroupId(String subscriptionChargeGroupId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = dslContextProvider.get(tenantId);
        var result = dslContext
            .selectFrom(INVOICE_LINE_ITEM)
            .where(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.SUBSCRIPTION_CHARGE_GROUP_ID.eq(subscriptionChargeGroupId))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .and(INVOICE_LINE_ITEM.STATUS.ne(InvoiceStatus.VOIDED.name()))
            .orderBy(INVOICE_LINE_ITEM.PERIOD_END_DATE.desc())
            .limit(1)
            .fetchOne();

        return Optional.ofNullable(invoiceMapper.convertDBRecordToInvoiceLineItem(result));
    }

    public List<InvoiceItem> getAllInvoiceItemsBySubscriptionCharge(String subscriptionChargeId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = dslContextProvider.get(tenantId);
        var result = dslContext
            .select()
            .from(INVOICE_LINE_ITEM)
            .where(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.SUBSCRIPTION_CHARGE_ID.eq(subscriptionChargeId))
            .and(INVOICE_LINE_ITEM.STATUS.ne(InvoiceStatus.VOIDED.name()))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .orderBy(INVOICE_LINE_ITEM.PERIOD_END_DATE.asc())
            .fetchInto(InvoiceLineItemRecord.class);
        return invoiceMapper.convertDBRecordsToInvoiceLineItems(result);
    }

    public List<InvoiceItem> getInvoiceItemsBySubscriptionChargeAndPeriod(String subscriptionChargeId, Period prepaidPeriod) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = dslContextProvider.get(tenantId);
        var result = dslContext
            .select()
            .from(INVOICE_LINE_ITEM)
            .where(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.SUBSCRIPTION_CHARGE_ID.eq(subscriptionChargeId))
            .and(INVOICE_LINE_ITEM.PERIOD_START_DATE.ge(DateTimeConverter.instantToLocalDateTime(prepaidPeriod.getStart())))
            .and(INVOICE_LINE_ITEM.PERIOD_END_DATE.le(DateTimeConverter.instantToLocalDateTime(prepaidPeriod.getEnd())))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .and(INVOICE_LINE_ITEM.STATUS.ne(InvoiceStatus.VOIDED.name()))
            .orderBy(INVOICE_LINE_ITEM.PERIOD_END_DATE.desc())
            .fetchInto(InvoiceLineItemRecord.class);
        return invoiceMapper.convertDBRecordsToInvoiceLineItems(result);
    }

    public List<InvoiceItem> getInvoiceItemsForOrderLineItem(String orderLineItemId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);
        Result<Record> result = dslContext
            .select()
            .from(INVOICE_LINE_ITEM)
            .where(
                INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId)
                    .and(INVOICE_LINE_ITEM.ORDER_LINE_ITEM_ID.eq(orderLineItemId))
                    .and(INVOICE_LINE_ITEM.STATUS.ne(InvoiceStatus.VOIDED.name()))
                    .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            )
            .fetch();

        return result.stream().map(record -> record.into(InvoiceLineItemRecord.class)).map(invoiceMapper::convertDBRecordToInvoiceLineItem).toList();
    }

    public List<InvoiceItem> getAllInvoiceItemsForOrderLineIdsPastThreshold(List<String> orderLineItemIds, Instant endDateThreshold) {
        LocalDateTime endDate = DateTimeConverter.instantToLocalDateTime(endDateThreshold);

        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);
        List<InvoiceLineItemRecord> invoiceLineItemRecords = dslContext
            .select()
            .from(INVOICE_LINE_ITEM)
            .where(
                INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId)
                    .and(INVOICE_LINE_ITEM.ORDER_LINE_ITEM_ID.in(orderLineItemIds))
                    .and(INVOICE_LINE_ITEM.PERIOD_END_DATE.greaterThan(endDate))
                    .and(INVOICE_LINE_ITEM.STATUS.ne(InvoiceStatus.VOIDED.name()))
                    .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            )
            .fetchInto(InvoiceLineItemRecord.class);

        return invoiceMapper.convertDBRecordsToInvoiceLineItems(invoiceLineItemRecords);
    }

    public Optional<Instant> getLatestInvoiceItemEndDateForOrderLineItem(String orderLineItemId, Charge charge, Instant subscriptionStart) {
        List<InvoiceItem> invoiceItems = getAllInvoiceItemsForOrderLineIdsPastThreshold(List.of(orderLineItemId), subscriptionStart);
        return invoiceItems
            .stream()
            .map(invoiceItem -> {
                // todo: replace with just trigger on when trigger on backfill is done
                if (invoiceItem.getTriggerOn() != null) {
                    return invoiceItem.getTriggerOn();
                }
                if (charge.getBillingTerm() == BillingTerm.UP_FRONT) {
                    return invoiceItem.getPeriodStartDate();
                }
                return invoiceItem.getPeriodEndDate();
            })
            .max(Comparator.naturalOrder());
    }

    public Set<String> getAllOrderLineIdsWithInvoiceItemsPastThreshold(List<String> orderLineItemIds, Instant endDateThreshold) {
        List<InvoiceItem> invoiceItems = getAllInvoiceItemsForOrderLineIdsPastThreshold(orderLineItemIds, endDateThreshold);

        return invoiceItems.stream().map(InvoiceItem::getOrderLineItemId).collect(Collectors.toSet());
    }

    public TenantInvoiceConfig createOrUpdateTenantInvoiceConfiguration(TenantInvoiceConfig tenantInvoiceConfig, String tenantId) {
        LocalDateTime currentTimestamp = LocalDateTime.now(ZoneOffset.UTC);
        DSLContext dslContext = dslContextProvider.get(tenantId);

        TenantInvoiceConfigurationRecord record = TenantInvoiceConfigMapper.toRecord(tenantInvoiceConfig);
        record.setUpdatedOn(currentTimestamp);
        record.setTenantId(tenantId);

        var existingRecord = dslContext.select().from(TENANT_INVOICE_CONFIGURATION).fetchOne();

        if (existingRecord == null) {
            record.setCreatedOn(currentTimestamp);
            record.setInvoiceConfigId(UUID.randomUUID().toString());
            record.reset(TENANT_INVOICE_CONFIGURATION.ID);
            var savedRecord = dslContext.insertInto(TENANT_INVOICE_CONFIGURATION).set(record).returning().fetchOne();
            savedRecord = getSafeRecord(savedRecord, "Failed to create tenant invoice config.");
            return TenantInvoiceConfigMapper.toTenantConfig(savedRecord);
        } else {
            TenantInvoiceConfigurationRecord invoiceConfigRecord = existingRecord.into(TenantInvoiceConfigurationRecord.class);
            record.setId(invoiceConfigRecord.getId());
            record.setInvoiceConfigId(invoiceConfigRecord.getInvoiceConfigId());
            var savedRecord = dslContext
                .update(TENANT_INVOICE_CONFIGURATION)
                .set(record)
                .where(TENANT_INVOICE_CONFIGURATION.TENANT_ID.eq(tenantId))
                .returning()
                .fetchOne();
            savedRecord = getSafeRecord(savedRecord, "Failed to update tenant invoice config.");
            return TenantInvoiceConfigMapper.toTenantConfig(savedRecord);
        }
    }

    public Optional<TenantInvoiceConfig> getTenantInvoiceConfiguration() {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext.select().from(TENANT_INVOICE_CONFIGURATION).where(TENANT_INVOICE_CONFIGURATION.TENANT_ID.eq(tenantId)).fetchOne();
        if (record == null) {
            return Optional.empty();
        }

        TenantInvoiceConfigurationRecord tenantInvoicePreferencesRecord = record.into(TenantInvoiceConfigurationRecord.class);
        return Optional.of(TenantInvoiceConfigMapper.toTenantConfig(tenantInvoicePreferencesRecord));
    }

    public TenantInvoiceConfig getInvoiceConfigById(String invoiceConfigId) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(TENANT_INVOICE_CONFIGURATION)
            .where(TENANT_INVOICE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .and(TENANT_INVOICE_CONFIGURATION.INVOICE_CONFIG_ID.eq(invoiceConfigId))
            .fetchOne();
        if (record == null) {
            throw new ObjectNotFoundException(BillyObjectType.INVOICE_CONFIG, invoiceConfigId);
        }

        TenantInvoiceConfigurationRecord invoiceConfigurationRecord = record.into(TenantInvoiceConfigurationRecord.class);
        return TenantInvoiceConfigMapper.toTenantConfig(invoiceConfigurationRecord);
    }

    /**
     * this method returns the next invoice sequence number, as seen during the time of read
     * there are no guarantees around strictly increasing numbers by the time this read is done
     * the invoice number could have changed
     * @return next invoice number if present
     */
    public Optional<Long> getTimeInPointInvoiceSequenceNumberForTenant() {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(TENANT_INVOICE_NUMBER_SEQUENCE)
            .where(TENANT_INVOICE_NUMBER_SEQUENCE.TENANT_ID.eq(tenantId))
            .fetchOneInto(TenantInvoiceNumberSequenceRecord.class);

        if (record == null) {
            return Optional.empty();
        }

        long nextInvoiceNumber = getNextInvoiceNumber(record);
        return Optional.of(nextInvoiceNumber);
    }

    public Optional<Long> getPointInTimeInvoiceSequenceNumberByConfigId(String invoiceConfigId) {
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(TENANT_INVOICE_NUMBER_SEQUENCE)
            .where(TENANT_INVOICE_NUMBER_SEQUENCE.TENANT_ID.eq(tenantId))
            .and(TENANT_INVOICE_NUMBER_SEQUENCE.INVOICE_CONFIG_ID.eq(invoiceConfigId))
            .fetchOneInto(TenantInvoiceNumberSequenceRecord.class);

        if (record == null) {
            return Optional.empty();
        }

        long nextInvoiceNumber = getNextInvoiceNumber(record);
        return Optional.of(nextInvoiceNumber);
    }

    public long getAndIncrementNextInvoiceNumberInSequenceForTenant(Configuration jooqConfiguration) {
        DSLContext dslContext = DSL.using(jooqConfiguration);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(TENANT_INVOICE_NUMBER_SEQUENCE)
            .where(TENANT_INVOICE_NUMBER_SEQUENCE.TENANT_ID.eq(tenantId))
            .forUpdate()
            .fetchOneInto(TenantInvoiceNumberSequenceRecord.class);

        if (record == null) {
            throw new IllegalStateException("No next invoice number for tenant: " + tenantIdProvider.provideTenantIdString());
        }

        long nextInvoiceNumber = getNextInvoiceNumber(record);

        record.setUpdatedOn(LocalDateTime.now(ZoneOffset.UTC));
        record.setNextInvoiceNumber(nextInvoiceNumber + 1);
        dslContext.update(TENANT_INVOICE_NUMBER_SEQUENCE).set(record).where(TENANT_INVOICE_NUMBER_SEQUENCE.TENANT_ID.eq(tenantId)).execute();

        return nextInvoiceNumber;
    }

    public long getAndIncrementInvoiceSequenceByConfigId(Configuration jooqConfiguration, String invoiceConfigId) {
        DSLContext dslContext = DSL.using(jooqConfiguration);
        var tenantId = tenantIdProvider.provideTenantIdString();
        var record = dslContext
            .select()
            .from(TENANT_INVOICE_NUMBER_SEQUENCE)
            .where(TENANT_INVOICE_NUMBER_SEQUENCE.TENANT_ID.eq(tenantId))
            .and(TENANT_INVOICE_NUMBER_SEQUENCE.INVOICE_CONFIG_ID.eq(invoiceConfigId))
            .forUpdate()
            .fetchOneInto(TenantInvoiceNumberSequenceRecord.class);

        if (record == null) {
            throw new IllegalStateException("No next invoice number for config id: " + invoiceConfigId);
        }

        long nextInvoiceNumber = getNextInvoiceNumber(record);

        record.setUpdatedOn(LocalDateTime.now(ZoneOffset.UTC));
        record.setNextInvoiceNumber(nextInvoiceNumber + 1);
        dslContext
            .update(TENANT_INVOICE_NUMBER_SEQUENCE)
            .set(record)
            .where(TENANT_INVOICE_NUMBER_SEQUENCE.TENANT_ID.eq(tenantId))
            .and(TENANT_INVOICE_NUMBER_SEQUENCE.INVOICE_CONFIG_ID.eq(invoiceConfigId))
            .execute();

        return nextInvoiceNumber;
    }

    public void createOrUpdateInvoiceNumberRecordForTenant(long nextInvoiceNumber, String tenantId, String invoiceConfigId) {
        DSLContext dslContext = dslContextProvider.get(tenantId);
        var existingRecord = dslContext
            .select()
            .from(TENANT_INVOICE_NUMBER_SEQUENCE)
            .where(TENANT_INVOICE_NUMBER_SEQUENCE.TENANT_ID.eq(tenantId))
            .fetchOneInto(TenantInvoiceNumberSequenceRecord.class);

        LocalDateTime currentTimestamp = LocalDateTime.now(ZoneOffset.UTC);
        TenantInvoiceNumberSequenceRecord record = new TenantInvoiceNumberSequenceRecord();
        record.setInvoiceConfigId(invoiceConfigId);
        record.setCreatedOn(currentTimestamp);
        record.setUpdatedOn(currentTimestamp);
        record.setTenantId(tenantId);
        record.setNextInvoiceNumber(nextInvoiceNumber);

        if (existingRecord == null) {
            dslContext.insertInto(TENANT_INVOICE_NUMBER_SEQUENCE).set(record).execute();
            return;
        }

        validateNewNextInvoiceNumber(nextInvoiceNumber, existingRecord.getNextInvoiceNumber());

        dslContext.update(TENANT_INVOICE_NUMBER_SEQUENCE).set(record).where(TENANT_INVOICE_NUMBER_SEQUENCE.TENANT_ID.eq(tenantId)).execute();
    }

    private void validateNewNextInvoiceNumber(long nextInvoiceNumber, Long existingNextInvoiceNumber) {
        if (existingNextInvoiceNumber == null || existingNextInvoiceNumber <= nextInvoiceNumber) {
            return;
        }

        String errorMessage = String.format(
            "Existing next invoice number: %s cannot be greater than the new next invoice number: %s",
            existingNextInvoiceNumber,
            nextInvoiceNumber
        );
        throw new IllegalArgumentException(errorMessage);
    }

    private long getNextInvoiceNumber(TenantInvoiceNumberSequenceRecord tenantInvoiceIdSequenceRecord) {
        if (tenantInvoiceIdSequenceRecord != null && tenantInvoiceIdSequenceRecord.getNextInvoiceNumber() != null) {
            return tenantInvoiceIdSequenceRecord.getNextInvoiceNumber();
        }

        return getTenantInvoiceConfiguration().map(TenantInvoiceConfig::getInvoiceNextNumber).orElse(1L);
    }

    private List<InvoiceItem> getInvoiceItemsForInvoiceIds(DSLContext dslContext, List<UUID> invoiceIds) {
        var result = dslContext.select().from(INVOICE_LINE_ITEM).where(INVOICE_LINE_ITEM.INVOICE_ID.in(invoiceIds)).fetch();

        return result
            .stream()
            .map(record -> record.into(InvoiceLineItemRecord.class))
            .map(invoiceMapper::convertDBRecordToInvoiceLineItem)
            .collect(Collectors.toList());
    }

    // todo: remove this once populateInvoiceItems is updated to fetch only active invoice items
    public List<InvoiceItem> getActiveInvoiceItemsByInvoiceId(UUID invoiceId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var records = dslContext
            .select()
            .from(INVOICE_LINE_ITEM)
            .where(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.INVOICE_ID.eq(invoiceId))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .fetchInto(InvoiceLineItemRecord.class);
        return invoiceMapper.convertDBRecordsToInvoiceLineItems(records);
    }

    public List<InvoiceItem> getInvoiceItemsForOrderId(String orderId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var result = dslContext
            .select()
            .from(INVOICE_LINE_ITEM)
            .where(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.ORDER_ID.eq(orderId))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .fetchInto(InvoiceLineItemRecord.class);
        return invoiceMapper.convertDBRecordsToInvoiceLineItems(result);
    }

    public void ensureInvoiceNumberIsUnique(Invoice.Number invoiceNumber) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var record = dslContext
            .select()
            .from(INVOICE)
            .where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .fetchOneInto(InvoiceRecord.class);

        if (record == null) {
            return;
        }

        throw new DuplicateInvoiceNumberException("InvoiceNumber is a duplicate: " + invoiceNumber.getNumber());
    }

    public void ensureDraftInvoiceNumberIsUnique(Invoice.Number invoiceNumber) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var record = dslContext
            .select(INVOICE.INVOICE_NUMBER)
            .from(INVOICE)
            .where(INVOICE.DRAFT_INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .fetchOneInto(String.class);

        if (record == null) {
            return;
        }

        throw new DuplicateInvoiceNumberException("InvoiceNumber is a duplicate: " + invoiceNumber.getNumber());
    }

    private <T> T getSafeRecord(T record, String errorMessage) {
        return Optional.ofNullable(record).orElseThrow(() -> new ServiceFailureException(errorMessage));
    }

    public Optional<Invoice> getDraftInvoiceForSubscription(String subscriptionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        InvoiceRecord record = dslContext
            .select()
            .from(INVOICE)
            .leftOuterJoin(CREDIT_MEMO)
            .on(INVOICE.INVOICE_NUMBER.eq(CREDIT_MEMO.CREATED_FROM))
            .where(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(INVOICE.IS_DELETED.isFalse())
            .and(
                INVOICE.STATUS.eq(InvoiceStatus.DRAFT.name()).or(
                    INVOICE.STATUS.eq(InvoiceStatus.CONVERTED.name())
                        .and(CREDIT_MEMO.STATUS.eq(CreditMemoStatus.DRAFT.name()))
                        .and(CREDIT_MEMO.IS_DELETED.isFalse())
                        .and(CREDIT_MEMO.TENANT_ID.eq(tenantId))
                )
            )
            .fetchOneInto(InvoiceRecord.class);

        Invoice invoice = invoiceMapper.convertDBRecordToInvoice(record);
        return Optional.ofNullable(invoice);
    }

    public Optional<InvoiceItem> getInvoiceItemById(UUID invoiceItemId) {
        Validator.validateNonNullArgument(invoiceItemId, "invoiceItemId");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var record = dslContext
            .select()
            .from(INVOICE_LINE_ITEM)
            .where(INVOICE_LINE_ITEM.ID.eq(invoiceItemId))
            .and(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .fetchOneInto(InvoiceLineItemRecord.class);
        return Optional.ofNullable(invoiceMapper.convertDBRecordToInvoiceLineItem(record));
    }

    public Optional<InvoiceItem> getPostedInvoiceItemByOrderLineIdAndDebookPeriod(String orderLineItemId, Period debookPeriod) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);
        var record = dslContext
            .select()
            .from(INVOICE_LINE_ITEM)
            .leftOuterJoin(INVOICE)
            .on(INVOICE.ID.eq(INVOICE_LINE_ITEM.INVOICE_ID))
            .leftOuterJoin(CREDIT_MEMO)
            .on(INVOICE.INVOICE_NUMBER.eq(CREDIT_MEMO.CREATED_FROM))
            .where(INVOICE_LINE_ITEM.ORDER_LINE_ITEM_ID.eq(orderLineItemId))
            .and(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(
                INVOICE.STATUS.eq(InvoiceStatus.POSTED.name()).or(
                    INVOICE.STATUS.eq(InvoiceStatus.CONVERTED.name())
                        .and(CREDIT_MEMO.STATUS.eq(CreditMemoStatus.POSTED.name()))
                        .and(CREDIT_MEMO.IS_DELETED.isFalse())
                        .and(CREDIT_MEMO.TENANT_ID.eq(tenantId))
                )
            )
            .and(INVOICE_LINE_ITEM.PERIOD_START_DATE.le(DateTimeConverter.instantToLocalDateTime(debookPeriod.getStart())))
            .and(INVOICE_LINE_ITEM.PERIOD_END_DATE.ge(DateTimeConverter.instantToLocalDateTime(debookPeriod.getEnd())))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .fetchOneInto(InvoiceLineItemRecord.class);
        return Optional.ofNullable(invoiceMapper.convertDBRecordToInvoiceLineItem(record));
    }

    public Map<String, List<BigDecimal>> getOpenInvoicesReportOverview() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        return dslContext
            .select()
            .from(INVOICE)
            .leftOuterJoin(INVOICE_BALANCE)
            .on(INVOICE.IS_DELETED.isFalse())
            .and(INVOICE.TOTAL.gt(BigDecimal.ZERO))
            .and(INVOICE.INVOICE_NUMBER.eq(INVOICE_BALANCE.INVOICE_NUMBER))
            .and(INVOICE.TENANT_ID.eq(INVOICE_BALANCE.TENANT_ID))
            .where(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.STATUS.ne(InvoiceStatus.DRAFT.name()))
            .and(INVOICE_BALANCE.BALANCE.gt(BigDecimal.ZERO).or(INVOICE_BALANCE.BALANCE.isNull()))
            .fetchGroups(INVOICE.CURRENCY_CODE, record -> Objects.requireNonNullElse(record.get(INVOICE_BALANCE.BALANCE), record.get(INVOICE.TOTAL)));
    }

    public void updateInvoiceStatusInTransaction(
        Invoice.Number invoiceNumber,
        InvoiceStatus currentStatus,
        InvoiceStatus newStatus,
        String tenantId,
        Configuration configuration
    ) {
        Validator.validateNonNullArguments(invoiceNumber, currentStatus, newStatus);
        DSLContext dslContext = DSL.using(configuration);

        InvoiceRecord invoiceRecord = dslContext
            .update(INVOICE)
            .set(INVOICE.STATUS, newStatus.name())
            .set(INVOICE.SHOULD_REGENERATE_PDF, true)
            .where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.IS_DELETED.isFalse())
            .and(INVOICE.STATUS.eq(currentStatus.name()))
            .returning()
            .fetchOne();

        if (invoiceRecord == null) {
            throw new ServiceFailureException(String.format("Invoice with number %s does not exist", invoiceNumber));
        }

        dslContext
            .update(INVOICE_LINE_ITEM)
            .set(INVOICE_LINE_ITEM.STATUS, newStatus.name())
            .where(INVOICE_LINE_ITEM.INVOICE_ID.eq(invoiceRecord.getId()))
            .and(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .and(INVOICE_LINE_ITEM.STATUS.eq(currentStatus.name()))
            .execute();
    }

    public void modifyInvoiceDueDateForTesting(String invoiceNumber, Long dueDate) {
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Instant dueDateInstant = Instant.ofEpochSecond(dueDate);
        LocalDateTime dueDateLocalDateTime = LocalDateTime.ofInstant(dueDateInstant, ZoneOffset.UTC);
        dslContext
            .update(INVOICE)
            .set(INVOICE.DUE_DATE, dueDateLocalDateTime)
            .where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber))
            .and(INVOICE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(INVOICE.IS_DELETED.isFalse())
            .execute();
    }

    public int getDraftInvoicesCountForAccount(String accountId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        return dslContext.fetchCount(
            INVOICE,
            INVOICE.TENANT_ID.eq(tenantId)
                .and(INVOICE.CUSTOMER_ACCOUNT_ID.eq(accountId))
                .and(INVOICE.STATUS.eq(InvoiceStatus.DRAFT.name()))
                .and(INVOICE.IS_DELETED.isFalse())
        );
    }

    public Invoice voidInvoice(
        Invoice.Number invoiceNumber,
        Instant invoiceVoidDate,
        InvoiceBalance currentInvoiceBalance,
        Configuration configuration,
        String tenantId
    ) {
        // Note: For concurrency checks, we are performing two types of checks here
        // 1. invoice status check to handle two concurrent requests for voiding the same invoice
        // 2. invoice balance check to ensure that no new payments / credit memos got applied to
        // the invoice while performing the void operation

        DSLContext dslContext = DSL.using(configuration);
        InvoiceRecord invoiceRecord = dslContext
            .update(INVOICE)
            .set(INVOICE.STATUS, InvoiceStatus.VOIDED.name())
            .set(INVOICE.VOIDED_DATE, DateTimeConverter.instantToLocalDateTime(invoiceVoidDate))
            .set(INVOICE.VOID_REQUESTED_ON, DateTimeConverter.instantToLocalDateTime(Instant.now()))
            .from(INVOICE_BALANCE)
            .where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .and(
                INVOICE.STATUS.eq(InvoiceStatus.POSTED.name()).or(
                    INVOICE.STATUS.eq(InvoiceStatus.PAID.name()).and(INVOICE_BALANCE.BALANCE.eq(BigDecimal.ZERO))
                )
            )
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE_BALANCE.INVOICE_NUMBER.eq(INVOICE.INVOICE_NUMBER))
            .and(INVOICE_BALANCE.TENANT_ID.eq(tenantId))
            .and(INVOICE_BALANCE.BALANCE.eq(currentInvoiceBalance.getBalance()))
            .and(INVOICE_BALANCE.ID.eq(currentInvoiceBalance.getId()))
            .returning()
            .fetchOne();

        if (invoiceRecord == null) {
            String message =
                "Invoice status changed while performing void invoice operation. This could be either due to a payment or credit memo got applied to the invoice or another void invoice operation in progress. ";
            message = message + "Invoice Number: " + invoiceNumber.getNumber();

            throw new ConflictingStateException(message);
        }

        dslContext
            .update(INVOICE_LINE_ITEM)
            .set(INVOICE_LINE_ITEM.STATUS, InvoiceStatus.VOIDED.name())
            .where(INVOICE_LINE_ITEM.INVOICE_ID.eq(invoiceRecord.getId()))
            .and(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .execute();

        invoiceRecord = getInvoiceRecord(invoiceNumber.getNumber(), dslContext);
        return populateInvoiceItems(invoiceRecord, dslContext);
    }

    public List<MemoizedInvoiceLineItem> getMemoizedInvoiceItemsForSubscription(String subscriptionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var memoizedRecords = dslContext
            .selectFrom(MEMOIZED_INVOICE_LINE_ITEM)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.SUBSCRIPTION_ID.eq(subscriptionId))
            // IMPORTANT - Don't use backfilled items for invoice generation unless FF is enabled
            .and(selectBackfilledLinesCondition())
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .fetchInto(MemoizedInvoiceLineItemRecord.class);

        return Collections.unmodifiableList(invoiceMapper.fromRecords(memoizedRecords));
    }

    private Condition selectBackfilledLinesCondition() {
        if (featureService.isEnabled(Feature.USE_BACKFILLED_MEMOIZATION)) {
            // fetch both backfilled and non-backfilled items
            return DSL.noCondition();
        }
        return MEMOIZED_INVOICE_LINE_ITEM.IS_BACKFILLED.isFalse();
    }

    // NOTE: Only for admin resource test invocations
    public Integer deleteMemoizedItemsForSubscriptionAndOrder(String subscriptionId, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return dslContext
            .update(MEMOIZED_INVOICE_LINE_ITEM)
            .set(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED, true)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ORDER_ID.eq(orderId))
            .execute();
    }

    public List<MemoizedInvoiceLineItem> getMemoizedInvoiceItemsForOrder(String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var memoizedRecords = dslContext
            .selectFrom(MEMOIZED_INVOICE_LINE_ITEM)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ORDER_ID.eq(orderId))
            .and(selectBackfilledLinesCondition())
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .orderBy(MEMOIZED_INVOICE_LINE_ITEM.TRIGGER_ON)
            .fetchInto(MemoizedInvoiceLineItemRecord.class);

        return Collections.unmodifiableList(invoiceMapper.fromRecords(memoizedRecords));
    }

    // NOTE: This method is used only for backfilling transactions, it will also return memoized lines with is_backfilled = true
    public List<MemoizedInvoiceLineItem> getMemoizedInvoiceItemsForOrderInBackfillingTransaction(
        DSLContext transactionContext,
        String tenantId,
        String orderId
    ) {
        var memoizedRecords = transactionContext
            .selectFrom(MEMOIZED_INVOICE_LINE_ITEM)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ORDER_ID.eq(orderId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .orderBy(MEMOIZED_INVOICE_LINE_ITEM.TRIGGER_ON)
            .fetchInto(MemoizedInvoiceLineItemRecord.class);

        return Collections.unmodifiableList(invoiceMapper.fromRecords(memoizedRecords));
    }

    public List<MemoizedInvoiceLineItem> getMemoizedInvoiceItems(String orderId, String orderItemId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var memoizedRecords = dslContext
            .selectFrom(MEMOIZED_INVOICE_LINE_ITEM)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ORDER_ID.eq(orderId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ORDER_LINE_ITEM_ID.eq(orderItemId))
            .and(selectBackfilledLinesCondition())
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .orderBy(MEMOIZED_INVOICE_LINE_ITEM.TRIGGER_ON)
            .fetchInto(MemoizedInvoiceLineItemRecord.class);

        return Collections.unmodifiableList(invoiceMapper.fromRecords(memoizedRecords));
    }

    public Optional<MemoizedInvoiceLineItem> getMemoizedInvoiceItem(UUID id) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        Optional<MemoizedInvoiceLineItemRecord> result = dslContext
            .selectFrom(MEMOIZED_INVOICE_LINE_ITEM)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ID.eq(id))
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .fetchOptional();

        return result.map(invoiceMapper::fromRecord);
    }

    public Set<String> getOrderIdsWithMemoizedInvoiceItems(List<String> orderIds) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return dslContext
            .selectDistinct(MEMOIZED_INVOICE_LINE_ITEM.ORDER_ID)
            .from(MEMOIZED_INVOICE_LINE_ITEM)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ORDER_ID.in(orderIds))
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .fetchSet(MEMOIZED_INVOICE_LINE_ITEM.ORDER_ID);
    }

    public void memoizeInvoiceItems(String entityId, String subscriptionId, String orderId, Collection<MemoizedInvoiceLineItem> toMemoize) {
        if (CollectionUtils.isEmpty(toMemoize)) {
            return;
        }
        Validator.checkArgumentNotBlankInternal(subscriptionId, "subscription id cannot be blank while memoization");
        Validator.checkArgumentNotBlankInternal(orderId, "order id cannot be blank while memoization");
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        List<MemoizedInvoiceLineItemRecord> itemRecords = toMemoize
            .stream()
            .map(item -> {
                if (StringUtils.isBlank(item.getSubscriptionId())) {
                    throw new ServiceFailureException(
                        String.format("memoized invoice line item for order %s missing subscription", item.getOrderId())
                    );
                }
                var itemRecord = invoiceMapper.toRecord(item);
                itemRecord.setTenantId(tenantId);
                itemRecord.setEntityId(entityId);
                //NOTE : IMPORTANT - only non-backfilled lines to be used in business logic for now
                itemRecord.setIsBackfilled(false);
                itemRecord.reset(MEMOIZED_INVOICE_LINE_ITEM.ID);
                return itemRecord;
            })
            .toList();

        dslContext.transaction(configuration -> {
            DSLContext txnContext = DSL.using(configuration);
            // first blindly purge any existing memoized items
            // this is being done so that we can execute the order again if required
            purgeMemoizedItemsInTransaction(subscriptionId, orderId, tenantId, txnContext);
            batchInsertMemoizedItemsInTransaction(itemRecords, txnContext);
        });

        LOGGER.info("successfully memoized {} items", toMemoize.size());
    }

    public void memoizeInvoiceItemsForBackfill(
        DSLContext transactionDSLContext,
        String tenantId,
        String entityId,
        String subscriptionId,
        String orderId,
        Collection<MemoizedInvoiceLineItem> toMemoize,
        List<String> existingMemoizedOrderLines
    ) {
        if (CollectionUtils.isEmpty(toMemoize)) {
            return;
        }
        Validator.checkArgumentNotBlankInternal(subscriptionId, "subscription id cannot be blank while memoization");
        Validator.checkArgumentNotBlankInternal(orderId, "order id cannot be blank while memoization");
        List<MemoizedInvoiceLineItemRecord> itemRecords = toMemoize
            .stream()
            .filter(item -> {
                var orderLineAlreadyMemoized = existingMemoizedOrderLines.contains(item.getOrderLineItemId());
                if (orderLineAlreadyMemoized) {
                    LOGGER.info(
                        String.format(
                            "Skipping preview item with period - %s : %s for backfill memoization as orderLine %s is already memoized",
                            item.getPeriodStartDate(),
                            item.getPeriodEndDate(),
                            item.getOrderLineItemId()
                        )
                    );
                    return false;
                }
                return true;
            })
            .map(item -> {
                if (StringUtils.isBlank(item.getSubscriptionId())) {
                    throw new ServiceFailureException(
                        String.format("memoized invoice line item for order %s missing subscription", item.getOrderId())
                    );
                }
                var itemRecord = invoiceMapper.toRecord(item);
                itemRecord.setTenantId(tenantId);
                itemRecord.setEntityId(entityId);
                // IMPORTANT - Set is_backfilled as true
                itemRecord.setIsBackfilled(true);
                itemRecord.reset(MEMOIZED_INVOICE_LINE_ITEM.ID);
                return itemRecord;
            })
            .toList();

        batchInsertMemoizedItemsInTransaction(itemRecords, transactionDSLContext);

        LOGGER.info("Memoization backfill - successfully memoized {} items", toMemoize.size());
    }

    public MemoizedInvoiceLineItem memoizeInvoiceItem(String orderId, String orderLineItemId, MemoizedInvoiceLineItem toMemoize) {
        Validator.checkArgumentNotBlankInternal(orderId, "order id cannot be blank while memoization");
        Validator.checkArgumentNotBlankInternal(orderLineItemId, "order line item id cannot be blank while memoization");
        Validator.validateNonNullArgument(toMemoize, "memoized invoice line item cannot be null");

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        MemoizedInvoiceLineItemRecord record = invoiceMapper.toRecord(toMemoize);
        record.setTenantId(tenantId);
        //NOTE : IMPORTANT - only non-backfilled lines to be used in business logic for now
        record.setIsBackfilled(false);
        record.reset(MEMOIZED_INVOICE_LINE_ITEM.ID);

        // todo: add optimistic locking to prevent concurrent inserts / deletes
        var savedRecord = dslContext.insertInto(MEMOIZED_INVOICE_LINE_ITEM).set(record).returning().fetchOne();
        return invoiceMapper.fromRecord(savedRecord);
    }

    public Optional<MemoizedInvoiceLineItem> deleteMemoizedInvoiceItem(UUID id) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        Optional<MemoizedInvoiceLineItemRecord> result = dslContext
            .update(MEMOIZED_INVOICE_LINE_ITEM)
            .set(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED, true)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ID.eq(id))
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .returning()
            .fetchOptional();

        return result.map(invoiceMapper::fromRecord);
    }

    public void deleteSubscriptionMemoizedItemsInTransaction(String subscriptionId, String tenantId, DSLContext txnContext) {
        int deletedCount = txnContext
            .update(MEMOIZED_INVOICE_LINE_ITEM)
            .set(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED, true)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .execute();
        LOGGER.info("deleted {} memoized invoice for subscription:{}", deletedCount, subscriptionId);
    }

    public void deleteOrderMemoizedItemsInTransaction(String orderId, String tenantId, DSLContext txnContext) {
        int deletedCount = txnContext
            .update(MEMOIZED_INVOICE_LINE_ITEM)
            .set(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED, true)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ORDER_ID.eq(orderId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .execute();
        LOGGER.info("deleted {} memoized invoice for order:{}", deletedCount, orderId);
    }

    // ****DO NOT USE****
    // NOTE: This method is used only for memoization backfilling transactions, it will also delete memoized lines with is_backfilled = true
    public void batchDeleteBackfilledOrderMemoizedItemsInTransaction(
        String orderId,
        List<UUID> memoizedInvoiceLineIds,
        String tenantId,
        DSLContext txnContext
    ) {
        int deletedCount = txnContext
            .deleteFrom(MEMOIZED_INVOICE_LINE_ITEM)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ORDER_ID.eq(orderId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ID.in(memoizedInvoiceLineIds))
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_BACKFILLED.isTrue())
            .and(MEMOIZED_INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .execute();
        if (deletedCount != memoizedInvoiceLineIds.size()) {
            String message = String.format("expected to delete %d rows but deleted %d rows", memoizedInvoiceLineIds.size(), deletedCount);
            throw new InvariantCheckFailedException(message);
        }
        LOGGER.info("deleted {} backfilled memoized invoice for order:{}", deletedCount, orderId);
    }

    private void purgeMemoizedItemsInTransaction(String subscriptionId, String orderId, String tenantId, DSLContext txnContext) {
        int purgedCount = txnContext
            .deleteFrom(MEMOIZED_INVOICE_LINE_ITEM)
            .where(MEMOIZED_INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.SUBSCRIPTION_ID.eq(subscriptionId))
            .and(MEMOIZED_INVOICE_LINE_ITEM.ORDER_ID.eq(orderId))
            .execute();
        LOGGER.info("purged {} memoized invoice for subscription:{} and order:{}", purgedCount, subscriptionId, orderId);
    }

    private void batchInsertMemoizedItemsInTransaction(List<MemoizedInvoiceLineItemRecord> itemRecords, DSLContext txnContext) {
        // now batch insert the incoming memoized items
        int[] countArray = txnContext.batchInsert(itemRecords).execute();
        int insertCount = Arrays.stream(countArray).sum();

        if (insertCount != itemRecords.size()) {
            String message = String.format("expected to insert %d rows but inserted %d rows", itemRecords.size(), insertCount);
            throw new ServiceFailureException(message);
        }
    }

    public void createCreditBuckets(List<CreditBucket> creditBuckets) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var records = invoiceMapper.creditBucketsToRecords(creditBuckets);
        records.forEach(record -> {
            record.setTenantId(tenantId);
            record.reset(CREDIT_BUCKET.ID);
        });

        int[] countArray = dslContext.batchInsert(records).execute();
        int insertCount = Arrays.stream(countArray).sum();

        if (insertCount != records.size()) {
            String message = String.format("expected to insert %d rows but inserted %d rows", records.size(), insertCount);
            LOGGER.error(message);
            throw new ServiceFailureException(message);
        }
    }

    public List<CreditBucket> getCreditBuckets(String creditBucketId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var creditBucketRecords = dslContext
            .selectFrom(CREDIT_BUCKET)
            .where(CREDIT_BUCKET.TENANT_ID.eq(tenantId))
            .and(CREDIT_BUCKET.CREDIT_BUCKET_ID.eq(creditBucketId))
            .and(CREDIT_BUCKET.IS_DELETED.isFalse())
            .fetchInto(CreditBucketRecord.class);

        return Collections.unmodifiableList(invoiceMapper.recordsToCreditBuckets(creditBucketRecords));
    }

    // only applies for legacy prepaid drawdown subscriptions. Newly generated invoice items will have these values included
    public void backfillUsageDrawdown(InvoiceItem invoiceItem, long drawdownQuantityUsed, long drawdownQuantityRemaining) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        UUID invoiceItemId = UUID.fromString(invoiceItem.getId().id());
        dslContext
            .update(INVOICE_LINE_ITEM)
            .set(INVOICE_LINE_ITEM.DRAWDOWN_QUANTITY_USED, drawdownQuantityUsed)
            .set(INVOICE_LINE_ITEM.DRAWDOWN_QUANTITY_REMAINING, drawdownQuantityRemaining)
            .where(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.ID.eq(invoiceItemId))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .execute();
    }

    public Integer getPostedInvoicesCountForSubscription(String externalSubscriptionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        return dslContext
            .selectCount()
            .from(INVOICE)
            .where(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.SUBSCRIPTION_ID.eq(externalSubscriptionId))
            .and(
                INVOICE.STATUS.eq(InvoiceStatus.POSTED.name())
                    .or(INVOICE.STATUS.eq(InvoiceStatus.PAID.name()))
                    .or(INVOICE.STATUS.eq(InvoiceStatus.CONVERTED.name()))
            )
            .and(INVOICE.IS_DELETED.isFalse())
            .fetchOne(0, Integer.class);
    }

    // todo: multientity: temporary method for data migration. remove it once backfill completed in all environments
    public void backfillInvoiceConfigIdOnSequence() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Optional<TenantInvoiceConfig> optionalInvoiceConfig = getTenantInvoiceConfiguration();
        if (optionalInvoiceConfig.isEmpty()) {
            return;
        }
        TenantInvoiceConfig invoiceConfig = optionalInvoiceConfig.get();
        dslContext
            .update(TENANT_INVOICE_NUMBER_SEQUENCE)
            .set(TENANT_INVOICE_NUMBER_SEQUENCE.INVOICE_CONFIG_ID, invoiceConfig.getInvoiceConfigId())
            .where(TENANT_INVOICE_NUMBER_SEQUENCE.TENANT_ID.eq(tenantId))
            .execute();
    }

    public boolean checkInvoiceSequenceExistsByConfigId(String invoiceConfigId) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        var record = dslContext
            .select()
            .from(TENANT_INVOICE_NUMBER_SEQUENCE)
            .where(TENANT_INVOICE_NUMBER_SEQUENCE.TENANT_ID.eq(tenantId))
            .and(TENANT_INVOICE_NUMBER_SEQUENCE.INVOICE_CONFIG_ID.eq(invoiceConfigId))
            .fetchOneInto(TenantInvoiceNumberSequenceRecord.class);
        return record != null;
    }

    public void setShouldRegeneratePdf(Invoice.Number invoiceNumber, boolean shouldRegeneratePdf) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        dslContext
            .update(INVOICE)
            .set(INVOICE.SHOULD_REGENERATE_PDF, shouldRegeneratePdf)
            .where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber.getNumber()))
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .execute();
    }

    public void updateInvoiceItemFunctionalAmounts(Configuration configuration, InvoiceItem invoiceItem) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = DSL.using(configuration);
        int count = dslContext
            .update(INVOICE_LINE_ITEM)
            .set(INVOICE_LINE_ITEM.FUNCTIONAL_AMOUNT, invoiceItem.getFunctionalAmount())
            .set(INVOICE_LINE_ITEM.FUNCTIONAL_LIST_AMOUNT, invoiceItem.getFunctionalListAmount())
            .set(INVOICE_LINE_ITEM.FUNCTIONAL_DISCOUNT_AMOUNT, invoiceItem.getFunctionalDiscountAmount())
            .set(INVOICE_LINE_ITEM.FUNCTIONAL_TAX_AMOUNT, invoiceItem.getFunctionalTaxAmount())
            .where(INVOICE_LINE_ITEM.ID.eq(UUID.fromString(invoiceItem.getId().id())))
            .and(INVOICE_LINE_ITEM.TENANT_ID.eq(tenantId))
            .and(INVOICE_LINE_ITEM.IS_DELETED.isFalse())
            .execute();
        if (count != 1) {
            throw new ServiceFailureException("Failed to update invoice item functional amounts");
        }
    }

    public boolean updateInvoiceGeneratedBy(String invoiceNumber, String generatedBy) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        InvoiceRecord invoiceRecord = getInvoiceRecord(invoiceNumber, dslContext);
        if (invoiceRecord == null) {
            LOGGER.warn("Did not find an invoice with number: {}", invoiceNumber);
            return false;
        }

        int count = dslContext
            .update(INVOICE)
            .set(INVOICE.GENERATED_BY, generatedBy)
            .where(INVOICE.ID.eq(invoiceRecord.getId()))
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.IS_DELETED.isFalse())
            .execute();

        if (count == 0) {
            LOGGER.warn("Was not able to update generatedBy field for invoice with number: {} id: {}", invoiceNumber, invoiceRecord.getId());
            return false;
        }

        return true;
    }

    public boolean updateEmailLastSentOn(String invoiceNumber, Instant emailSentOn) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        InvoiceRecord invoiceRecord = getInvoiceRecord(invoiceNumber, dslContext);
        if (invoiceRecord == null) {
            LOGGER.warn("Did not find an invoice with number: {}", invoiceNumber);
            return false;
        }

        int count = dslContext
            .update(INVOICE)
            .set(INVOICE.EMAIL_LAST_SENT_ON, DateTimeConverter.instantToLocalDateTime(emailSentOn))
            .where(INVOICE.ID.eq(invoiceRecord.getId()))
            .and(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.IS_DELETED.isFalse())
            .execute();

        if (count == 0) {
            LOGGER.warn("Was not able to update emailLastSentOn field for invoice with number: {} id: {}", invoiceNumber, invoiceRecord.getId());
            return false;
        }

        return true;
    }

    public PageResult<List<Invoice>, Instant> getInvoices(PageRequest<Instant> pageRequest) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        var query = dslContext
            .selectFrom(INVOICE)
            .where(INVOICE.TENANT_ID.eq(tenantId))
            .and(INVOICE.IS_DELETED.isFalse())
            .orderBy(INVOICE.CREATED_ON);

        List<InvoiceRecord> records;
        if (pageRequest.getPageToken() != null) {
            records = query.seek(DateTimeConverter.instantToLocalDateTime(pageRequest.getPageToken())).limit(pageRequest.getLimit()).fetch();
        } else {
            records = query.limit(pageRequest.getLimit()).fetch();
        }
        return PageResult.fromCollectionAndRequest(invoiceMapper.convertDBRecordsToInvoices(records), pageRequest, Invoice::getCreatedOn);
    }

    public void updateInvoiceFunctionalAmounts(Configuration configuration, Invoice invoice) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = DSL.using(configuration);
        UpdateSetStep<InvoiceRecord> updateFirstStep = dslContext.update(INVOICE);
        var update = prepareInvoiceUpdateStepForFunctionalAmounts(updateFirstStep, invoice);
        var count = update.where(INVOICE.ID.eq(invoice.getUUID())).and(INVOICE.TENANT_ID.eq(tenantId)).execute();
        if (count != 1) {
            throw new ServiceFailureException("Failed to update invoice functional amounts");
        }
    }

    public PageResult<List<Invoice>, Instant> getInvoicesByStatus(Set<InvoiceStatus> invoiceStatuses, PageRequest<Instant> pageRequest) {
        Validator.checkNonNullInternal(pageRequest, "pageRequest cannot be null");
        Validator.validateCollectionElementsNotNull(invoiceStatuses, "invoiceStatuses cannot be null");

        return fetchPage(getQueryForInvoices(INVOICE.STATUS, invoiceStatuses), pageRequest);
    }

    public PageResult<List<Invoice>, Instant> getInvoicesForInvoiceNumbers(Collection<String> invoiceNumbers, PageRequest<Instant> pageRequest) {
        Validator.checkNonNullInternal(pageRequest, "pageRequest cannot be null");
        Validator.validateCollectionElementsNotNull(invoiceNumbers, "invoiceNumbers cannot be null");

        return fetchPage(getQueryForInvoices(INVOICE.INVOICE_NUMBER, invoiceNumbers), pageRequest);
    }

    private <T> @NotNull SelectSeekStep1<InvoiceRecord, LocalDateTime> getQueryForInvoices(
        TableField<InvoiceRecord, String> invoiceField,
        Collection<T> invoiceNumbers
    ) {
        return TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider)
            .selectFrom(INVOICE)
            .where(INVOICE.TENANT_ID.eq(tenantIdProvider.provideTenantIdString()))
            .and(INVOICE.IS_DELETED.isFalse())
            .and(invoiceField.in(invoiceNumbers))
            .orderBy(INVOICE.CREATED_ON);
    }

    private PageResult<List<Invoice>, Instant> fetchPage(SelectSeekStep1<InvoiceRecord, LocalDateTime> query, PageRequest<Instant> pageRequest) {
        Supplier<List<InvoiceRecord>> fetcher = () -> {
            if (pageRequest.getPageToken() != null) {
                return query.seek(DateTimeConverter.instantToLocalDateTime(pageRequest.getPageToken())).limit(pageRequest.getLimit()).fetch();
            }
            return query.limit(pageRequest.getLimit()).fetch();
        };

        return PageResult.fromCollectionAndRequest(invoiceMapper.convertDBRecordsToInvoices(fetcher.get()), pageRequest, Invoice::getCreatedOn);
    }
}
