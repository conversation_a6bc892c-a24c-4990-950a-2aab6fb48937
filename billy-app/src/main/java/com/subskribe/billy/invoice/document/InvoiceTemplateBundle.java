package com.subskribe.billy.invoice.document;

import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

public class InvoiceTemplateBundle {

    private final String bundleName;

    private final List<InvoiceTemplateLineItem> lineItems;

    private final String currencyCode;

    private final DocumentRenderFormatter formatter;

    public InvoiceTemplateBundle(String bundleName, List<InvoiceTemplateLineItem> lineItems, String currencyCode, DocumentRenderFormatter formatter) {
        this.bundleName = bundleName;
        this.lineItems = lineItems;
        this.currencyCode = currencyCode;
        this.formatter = formatter;
    }

    public String getBundleName() {
        return bundleName;
    }

    public List<InvoiceTemplateLineItem> getLineItems() {
        return lineItems;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public DocumentRenderFormatter getFormatter() {
        return formatter;
    }

    public String getAmount() {
        return formatter.currencyFormat(getAmountUnformatted(), currencyCode);
    }

    public BigDecimal getAmountUnformatted() {
        return lineItems.stream().map(item -> item.getLineItem().getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public InvoiceTemplateLineItem getMaxQuantityItem() {
        return lineItems.stream().max(Comparator.comparing(item -> item.getLineItem().getQuantity())).orElse(null);
    }

    public InvoiceTemplateLineItem getMaxQuantityOfNonBlockChargeItem() {
        return lineItems
            .stream()
            .filter(item -> item.getChargeJson().getChargeModel() != ChargeModel.BLOCK)
            .max(Comparator.comparing(item -> item.getLineItem().getQuantity()))
            .orElse(null);
    }
}
