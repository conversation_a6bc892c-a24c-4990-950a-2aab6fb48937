package com.subskribe.billy.invoice.service;

import com.google.common.annotations.VisibleForTesting;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;

@SuppressWarnings("PMD.LooseCoupling")
public class WeightedDistributionCalculator {

    private static final Logger LOGGER = LoggerFactory.getLogger(WeightedDistributionCalculator.class);

    public static LinkedHashMap<String, BigDecimal> getWeightedDistributionAmounts(
        LinkedHashMap<String, BigDecimal> orderLineAmounts, // For lack of better naming, this can actually be a ramp group as well, but we are using the same function for both ramp groups and individual lines
        BigDecimal totalAmount,
        String currencyCode,
        BigDecimal weightRatio
    ) {
        BigDecimal totalRoundedAmount = BigDecimal.ZERO;
        LinkedHashMap<String, BigDecimal> roundedAmounts = new LinkedHashMap<>();

        for (Map.Entry<String, BigDecimal> entry : orderLineAmounts.entrySet()) {
            BigDecimal lineAmount = entry.getValue();
            BigDecimal weightedAmount = weightRatio.multiply(lineAmount);

            BigDecimal roundedAmount = Numbers.makeCurrencyScale(weightedAmount, currencyCode);
            roundedAmounts.put(entry.getKey(), roundedAmount);
            totalRoundedAmount = totalRoundedAmount.add(roundedAmount);
        }

        if (totalRoundedAmount.compareTo(totalAmount) != 0) {
            LOGGER.info(
                "getWeightedDistributionAmounts : Rounding artifacts detected. Total rounded amount: {}, Total amount: {}",
                totalRoundedAmount,
                totalAmount
            );
            return handleRoundingArtifacts(roundedAmounts, totalRoundedAmount, totalAmount, currencyCode, false);
        }
        BigDecimal finalRoundedAmount = roundedAmounts.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        if (finalRoundedAmount.compareTo(totalAmount) != 0) {
            throw new InvariantCheckFailedException(
                String.format(
                    "Final rounded amounts %s do not match total amount %s, roundedAmounts %s, orderLineAmounts %s, currencyCode %s, weightRatio %s",
                    finalRoundedAmount,
                    totalAmount,
                    roundedAmounts,
                    orderLineAmounts,
                    currencyCode,
                    weightRatio
                )
            );
        }

        return roundedAmounts;
    }

    /**
     * If the total rounded amount doesn't match the total target amount, we need to adjust the amounts to match the total amount
     * Currently it uses a smoothening approach where it adjusts the amounts by 1 cent (lowest currency denomination) per entry, to match the total target amount
     * TODO : This can be extended later by also accepting an adjustment strategy which is not fifo but probably a different logic.
     * @param roundedAmounts
     * @param totalRoundedAmount
     * @param totalAmount
     * @param currencyCode
     * @return LinkedHashMap<String, BigDecimal> with adjusted amounts
     */
    public static LinkedHashMap<String, BigDecimal> handleRoundingArtifacts(
        Map<String, BigDecimal> roundedAmounts,
        BigDecimal totalRoundedAmount,
        BigDecimal totalAmount,
        String currencyCode,
        boolean forMemoizationBackfill
    ) {
        LinkedHashMap<String, BigDecimal> roundingAdjustedAmounts = new LinkedHashMap<>();
        BigDecimal roundingDifference = Numbers.makeCurrencyScale(totalRoundedAmount.subtract(totalAmount), currencyCode);
        BigDecimal absoluteRoundingDifference = roundingDifference.abs();

        // If the rounding difference is positive, we need to subtract the amounts by 1, else -1 (addition)
        BigDecimal lowestDenomination = SupportedCurrency.convertFromSmallestUnitToAmount(BigDecimal.ONE.longValue(), currencyCode);
        BigDecimal amountToAdjust = roundingDifference.compareTo(BigDecimal.ZERO) > 0 ? lowestDenomination : lowestDenomination.negate();

        LOGGER.info("handleRoundingArtifacts : Rounding difference: {}, Amount to adjust: {}", roundingDifference, amountToAdjust);

        if (forMemoizationBackfill) {
            roundingAdjustedAmounts = new LinkedHashMap<>(roundedAmounts);
            // We need a while loop here to adjust the entire difference between the total rounded amount and the total amount
            // In case of backfill the difference can be significant, i.e. more than 1 cent * number of order lines, so we need to keep on looping over the entries till the rounding difference is 0
            // As we are running it in a loop we need to pass the updated roundingAdjustedAmounts in the next iteration as the initial amounts, hence the same param is being passed
            while (absoluteRoundingDifference.compareTo(BigDecimal.ZERO) > 0) {
                absoluteRoundingDifference = adjustRoundedAmounts(
                    roundingAdjustedAmounts,
                    roundingAdjustedAmounts,
                    absoluteRoundingDifference,
                    lowestDenomination,
                    amountToAdjust
                );
            }
        } else {
            // For custom billing use case - in this case we will never have rounding difference more than 1 cent * number of order lines
            absoluteRoundingDifference = adjustRoundedAmounts(
                roundedAmounts,
                roundingAdjustedAmounts,
                absoluteRoundingDifference,
                lowestDenomination,
                amountToAdjust
            );
        }

        if (Boolean.FALSE.equals(Numbers.isZero(absoluteRoundingDifference))) {
            throw new InvariantCheckFailedException(
                String.format(
                    "handleRoundingArtifacts : Rounding difference %s is not zero after adjustment, totalRoundedAmount %s, totalAmount %s, currencyCode %s",
                    absoluteRoundingDifference,
                    totalRoundedAmount,
                    totalAmount,
                    currencyCode
                )
            );
        }

        // Also add original values from roundedAmounts which didn't need to be adjusted
        for (Map.Entry<String, BigDecimal> entry : roundedAmounts.entrySet()) {
            if (!roundingAdjustedAmounts.containsKey(entry.getKey())) {
                roundingAdjustedAmounts.put(entry.getKey(), entry.getValue());
            }
        }

        BigDecimal finalRoundedAmount = roundingAdjustedAmounts.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        if (finalRoundedAmount.compareTo(totalAmount) != 0) {
            throw new InvariantCheckFailedException(
                String.format(
                    "Final rounded amounts %s do not match total amount %s, roundedAmounts %s, totalRoundedAmount %s, currencyCode %s",
                    finalRoundedAmount,
                    totalAmount,
                    roundingAdjustedAmounts,
                    totalRoundedAmount,
                    currencyCode
                )
            );
        }

        return roundingAdjustedAmounts;
    }

    @VisibleForTesting
    static BigDecimal adjustRoundedAmounts(
        Map<String, BigDecimal> roundedAmounts,
        LinkedHashMap<String, BigDecimal> roundingAdjustedAmounts,
        BigDecimal absoluteRoundingDifference,
        BigDecimal lowestDenomination,
        BigDecimal amountToAdjust
    ) {
        if (lowestDenomination.abs().compareTo(amountToAdjust.abs()) != 0) {
            throw new InvariantCheckFailedException(
                String.format(
                    "Lowest denomination absolute value %s does not match amount to adjust %s, absoluteRoundingDifference %s",
                    lowestDenomination,
                    amountToAdjust,
                    absoluteRoundingDifference
                )
            );
        }
        for (Map.Entry<String, BigDecimal> entry : roundedAmounts.entrySet()) {
            if (absoluteRoundingDifference.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            BigDecimal roundedAmount = entry.getValue();
            BigDecimal adjustedAmount = roundedAmount.subtract(amountToAdjust);
            roundingAdjustedAmounts.put(entry.getKey(), adjustedAmount);
            absoluteRoundingDifference = absoluteRoundingDifference.subtract(lowestDenomination);
        }

        return absoluteRoundingDifference;
    }
}
