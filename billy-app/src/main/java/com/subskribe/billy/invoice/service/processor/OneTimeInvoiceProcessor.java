package com.subskribe.billy.invoice.service.processor;

import static com.subskribe.billy.invoice.service.processor.InvoiceProcessorUtility.getOrderLinesByType;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.ListAmount;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.service.InvoiceAmountCalculator;
import com.subskribe.billy.invoice.service.processor.model.InvoiceGenerationInput;
import com.subskribe.billy.invoice.service.processor.model.InvoicePreviewInput;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;

public class OneTimeInvoiceProcessor implements InvoiceProcessor, EventBasedAndAdhocCustomBillingInvoiceProcessor {

    private final SubscriptionGetService subscriptionGetService;

    private final InvoiceDAO invoiceDAO;

    private final InvoiceAmountCalculator invoiceAmountCalculator;

    private final FeatureService featureService;

    @Inject
    public OneTimeInvoiceProcessor(
        SubscriptionGetService subscriptionGetService,
        InvoiceDAO invoiceDAO,
        InvoiceAmountCalculator invoiceAmountCalculator,
        FeatureService featureService
    ) {
        this.subscriptionGetService = subscriptionGetService;
        this.invoiceDAO = invoiceDAO;
        this.invoiceAmountCalculator = invoiceAmountCalculator;
        this.featureService = featureService;
    }

    @Override
    public List<InvoiceItem> previewInvoiceItems(InvoicePreviewInput invoicePreviewInput) {
        List<OrderLineItem> relevantOrderLines = getOrderLinesByType(
            invoicePreviewInput.getOrderLineItemsNetEffect(),
            invoicePreviewInput.getChargeMap(),
            ChargeType.ONE_TIME
        );

        List<OrderLineItem> orderLineItemsToProcess = relevantOrderLines
            .stream()
            .filter(item -> ALLOWED_ACTION_TYPES_EVENT_BASED.contains(item.getAction()))
            .filter(
                item ->
                    DateTimeCalculator.compareInstants(item.getEffectiveDate(), invoicePreviewInput.getInvoiceBillingInfo().getInvoiceTargetDate()) <=
                    0
            )
            .collect(Collectors.toList());
        orderLineItemsToProcess.addAll(getRemovedItemsToBeIncluded(relevantOrderLines, invoicePreviewInput.getChargeMap()));

        return orderLineItemsToProcess
            .stream()
            .map(lineItem -> {
                Charge charge = invoicePreviewInput.getChargeMap().get(lineItem.getChargeId());
                return getOneTimeInvoiceItem(invoicePreviewInput, lineItem, charge);
            })
            .filter(Objects::nonNull)
            .toList();
    }

    @Override
    public List<InvoiceItem> generateInvoiceItems(InvoiceGenerationInput invoiceGenerationInput) {
        Map<String, Charge> chargeMap = invoiceGenerationInput.getChargeMap();

        List<OrderLineItem> oneTimeOrderLines = getOrderLinesByType(
            invoiceGenerationInput.getOrderLineItemsNetEffect(),
            invoiceGenerationInput.getChargeMap(),
            ChargeType.ONE_TIME
        );

        List<OrderLineItem> standardOneTimeOrderLines = getNonEventBasedAndNonAdhocCustomBillingOrderLines(
            oneTimeOrderLines,
            chargeMap,
            invoiceGenerationInput
        );
        List<OrderLineItem> eventBasedOneTimeOrderLines = getEventBasedOrderLines(oneTimeOrderLines, chargeMap);
        List<OrderLineItem> adhocCustomBillingOrderLines = getAdhocCustomBillingOrderLines(
            oneTimeOrderLines,
            ListUtils.sum(standardOneTimeOrderLines, eventBasedOneTimeOrderLines)
        );

        List<InvoiceItem> standardOneTimeInvoiceItems = generateOneTimeInvoiceItemsMemoized(invoiceGenerationInput, standardOneTimeOrderLines);
        List<InvoiceItem> eventBasedOneTimeInvoiceItems = generateEventBasedInvoiceItems(invoiceGenerationInput, eventBasedOneTimeOrderLines);
        List<InvoiceItem> adhocCustomBillingInvoiceItems = generateAdhocCustomBillingInvoiceItems(
            invoiceGenerationInput,
            adhocCustomBillingOrderLines
        );

        return Stream.of(standardOneTimeInvoiceItems, eventBasedOneTimeInvoiceItems, adhocCustomBillingInvoiceItems).flatMap(List::stream).toList();
    }

    private List<InvoiceItem> generateOneTimeInvoiceItemsMemoized(
        InvoiceGenerationInput invoiceGenerationInput,
        List<OrderLineItem> oneTimeOrderLines
    ) {
        List<InvoiceItem> invoiceItems = new ArrayList<>();
        Instant subscriptionStartDate = invoiceGenerationInput.getInvoiceBillingInfo().getSubscriptionStart();
        Map<String, List<MemoizedInvoiceLineItem>> memoizedByOrderLineMap = invoiceGenerationInput.getMemoizedItemsByOrderLine();

        List<OrderLineItem> orderLineItemsToProcess = oneTimeOrderLines
            .stream()
            .filter(item -> ALLOWED_ACTION_TYPES_EVENT_BASED.contains(item.getAction()))
            .filter(
                item ->
                    DateTimeCalculator.compareInstants(
                        item.getEffectiveDate(),
                        invoiceGenerationInput.getInvoiceBillingInfo().getInvoiceTargetDate()
                    ) <=
                    0
            )
            .collect(Collectors.toList());

        orderLineItemsToProcess.addAll(getRemovedItemsToBeIncluded(oneTimeOrderLines, invoiceGenerationInput.getChargeMap()));

        List<String> orderLineItemIds = orderLineItemsToProcess.stream().map(OrderLineItem::getOrderLineId).collect(Collectors.toList());
        Set<String> orderLineItemIdsAlreadyInvoiced = invoiceDAO.getAllOrderLineIdsWithInvoiceItemsPastThreshold(
            orderLineItemIds,
            subscriptionStartDate
        );

        orderLineItemsToProcess
            .stream()
            .filter(item -> !orderLineItemIdsAlreadyInvoiced.contains(item.getOrderLineId()))
            .forEach(item -> {
                Charge charge = invoiceGenerationInput.getChargeMap().get(item.getChargeId());

                if (memoizedByOrderLineMap.containsKey(item.getOrderLineId())) {
                    List<InvoiceItem> memoized = memoizedByOrderLineMap
                        .get(item.getOrderLineId())
                        .stream()
                        .map(memoizedInvoiceLineItem -> memoizedInvoiceLineItem.to(item))
                        .toList();
                    invoiceItems.addAll(memoized);
                } else {
                    invoiceItems.add(getOneTimeInvoiceItem(invoiceGenerationInput, item, charge));
                }
            });
        return invoiceItems;
    }

    private List<OrderLineItem> getRemovedItemsToBeIncluded(List<OrderLineItem> oneTimeOrderLines, Map<String, Charge> chargeMap) {
        // only remove if removing the entire item from the beginning
        return oneTimeOrderLines.stream().filter(shouldOrderLineBeIncluded(chargeMap)).toList();
    }

    private Predicate<OrderLineItem> shouldOrderLineBeIncluded(Map<String, Charge> chargeMap) {
        return item -> {
            if (item.getAction() != ActionType.REMOVE) {
                return false;
            }

            if (isDebookingFromBeginning(item)) {
                return true;
            }

            return isOneTimeChargeRefundEligible(item, chargeMap);
        };
    }

    // Check if the order line item is being debooked from the beginning of the subscription
    private boolean isDebookingFromBeginning(OrderLineItem item) {
        UUID subscriptionChargeId = item.getSubscriptionChargeId();
        if (subscriptionChargeId != null) {
            Instant subscriptionChargeStart = subscriptionGetService.getSubscriptionChargeById(subscriptionChargeId).getStartDate();
            return subscriptionChargeStart.equals(item.getEffectiveDate());
        }

        if (StringUtils.isBlank(item.getBaseExternalSubscriptionChargeId())) {
            throw new ServiceFailureException("Couldn't find the reference to the removed charge");
        }

        SubscriptionCharge subscriptionCharge = subscriptionGetService.getSubscriptionChargeBySubscriptionChargeId(
            item.getBaseExternalSubscriptionChargeId()
        );
        return subscriptionCharge.getStartDate().equals(item.getEffectiveDate());
    }

    private boolean isOneTimeChargeRefundEligible(OrderLineItem item, Map<String, Charge> chargeMap) {
        // By now it is known that the order line item action type is REMOVE, charge type is ONE_TIME

        if (!featureService.isEnabled(Feature.ONE_TIME_CHARGE_DEBOOK)) {
            return false;
        }

        // check if the charge is creditable and the refund amount is valid (not null)
        return chargeMap.get(item.getChargeId()).isCreditable() && item.getAmount() != null;
    }

    private boolean isOneTimeChargeRefundEligible(OrderLineItem item, Charge charge) {
        if (item.getAction() != ActionType.REMOVE || !featureService.isEnabled(Feature.ONE_TIME_CHARGE_DEBOOK)) {
            return false;
        }
        // check if the charge is creditable and the refund amount is valid (not null)
        return charge.isCreditable() && item.getAmount() != null;
    }

    private InvoiceItem getOneTimeInvoiceItem(InvoicePreviewInput invoicePreviewInput, OrderLineItem item, Charge charge) {
        Optional<List<BillingPeriod>> customBillingPeriods = getCustomBillingPeriodsIfApplicable(invoicePreviewInput, item);
        List<BillingPeriod> topLevelBillingPeriods = customBillingPeriods.orElseGet(invoicePreviewInput::getTopLevelBillingPeriods);
        if (topLevelBillingPeriods.isEmpty()) {
            // TODO : Handle invoice item amount calculation for recurrence based custom billing, amounts need to be divided similar to adhoc billing segments mostly
            // NOTE : This is currently only for adhoc custom billing, because the invoice generation logic sits outside the processor, we don't need to return anything here.
            return null;
        }

        // The OTC line item is included in the invoice under the following scenarios:
        // 1: When the line item is debooked from the start of the subscription.
        // 2: When the line item is debooked on any date (including the subscription start date), provided the OTC debook
        //    criteria are satisfied.
        // If both scenarios apply, the one-time charge refund eligibility takes precedence.
        // This ensures that even if the debook occurs on the subscription start date, the user-specified refund amount is honored.
        // Consequently, the invoice item reflects the refund amount specified in the order line item.
        if (isOneTimeChargeRefundEligible(item, charge)) {
            return getOneTimeInvoiceItemForRefund(item, charge);
        }

        ListAmount listAmount = invoiceAmountCalculator.calculateOneTimeListAmount(item, charge);

        InvoiceItemAmounts invoiceItemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(
            item.getDiscounts(),
            item.getPredefinedDiscounts(),
            listAmount,
            invoiceAmountCalculator.getListUnitPrice(charge, item),
            item.getQuantity(),
            item.getListPriceOverrideRatio()
        );

        var invoiceItemBuilder = new InvoiceItem.InvoiceItemBuilder()
            .entityId(item.getEntityId())
            .orderId(item.getOrderId())
            .orderLineItemId(item.getOrderLineId())
            .subscriptionChargeId(item.getExternalSubscriptionChargeId())
            .subscriptionChargeGroupId(item.getSubscriptionChargeGroupId())
            .status(InvoiceStatus.DRAFT)
            .chargeId(item.getChargeId())
            .listAmountBeforeOverride(invoiceItemAmounts.getListAmountBeforeOverride())
            .listAmount(invoiceItemAmounts.getListAmount())
            .discountAmount(invoiceItemAmounts.getDiscountAmount())
            .amount(invoiceItemAmounts.getSellAmount())
            .listUnitPriceBeforeOverride(invoiceItemAmounts.getListUnitPriceBeforeOverride())
            .listUnitPrice(invoiceItemAmounts.getListUnitPrice())
            .sellUnitPrice(invoiceItemAmounts.getSellUnitPrice())
            .taxAmount(BigDecimal.ZERO)
            .quantity(item.getQuantity())
            .inlineDiscounts(invoiceItemAmounts.getLineItemDiscounts())
            .predefinedDiscounts(invoiceItemAmounts.getTenantDiscounts())
            .periodStartDate(item.getEffectiveDate())
            .periodEndDate(item.getEndDate())
            // todo: this may not be correct for the billing periods
            .triggerOn(charge.getBillingTerm(), item.getEffectiveDate(), item.getEndDate());

        return invoiceItemBuilder.createInvoiceItem();
    }

    @Override
    public List<InvoiceItem> getInvoiceItemsForOrderLineItem(String orderLineId) {
        return invoiceDAO.getInvoiceItemsForOrderLineItem(orderLineId);
    }

    private InvoiceItem getOneTimeInvoiceItemForRefund(OrderLineItem item, Charge charge) {
        InvoiceItem.InvoiceItemBuilder invoiceItemBuilder = new InvoiceItem.InvoiceItemBuilder()
            .entityId(item.getEntityId())
            .orderId(item.getOrderId())
            .orderLineItemId(item.getOrderLineId())
            .subscriptionChargeId(item.getExternalSubscriptionChargeId())
            .subscriptionChargeGroupId(item.getSubscriptionChargeGroupId())
            .status(InvoiceStatus.DRAFT)
            .chargeId(item.getChargeId())
            .listAmountBeforeOverride(item.getListAmountBeforeOverride())
            .listAmount(item.getListAmount())
            .discountAmount(item.getDiscountAmount())
            .amount(item.getAmount())
            .listUnitPriceBeforeOverride(item.getListUnitPriceBeforeOverride())
            .listUnitPrice(item.getListUnitPrice())
            .sellUnitPrice(item.getSellUnitPrice())
            .taxAmount(BigDecimal.ZERO)
            .quantity(item.getQuantity())
            .inlineDiscounts(item.getDiscounts())
            .predefinedDiscounts(item.getPredefinedDiscounts())
            .periodStartDate(item.getEffectiveDate())
            .periodEndDate(item.getEndDate())
            // todo: this may not be correct for the billing periods
            .triggerOn(charge.getBillingTerm(), item.getEffectiveDate(), item.getEndDate());

        return invoiceItemBuilder.createInvoiceItem();
    }
}
