package com.subskribe.billy.invoice.service.processor;

import static com.subskribe.billy.invoice.service.processor.InvoiceProcessorUtility.getOrderLinesByType;
import static com.subskribe.billy.invoice.service.processor.InvoiceProcessorUtility.orderLineItemInPeriod;
import static com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService.getItemOverlapPeriod;

import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.InvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.ListAmount;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.service.InvoiceAmountCalculator;
import com.subskribe.billy.invoice.service.processor.model.InvoiceGenerationInput;
import com.subskribe.billy.invoice.service.processor.model.InvoicePreviewInput;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;

public class PrepaidInvoiceProcessor implements InvoiceProcessor, EventBasedAndAdhocCustomBillingInvoiceProcessor {

    private final InvoiceDAO invoiceDAO;
    private final InvoiceAmountCalculator invoiceAmountCalculator;
    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;

    @Inject
    public PrepaidInvoiceProcessor(
        InvoiceDAO invoiceDAO,
        InvoiceAmountCalculator invoiceAmountCalculator,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService
    ) {
        this.invoiceDAO = invoiceDAO;
        this.invoiceAmountCalculator = invoiceAmountCalculator;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
    }

    @Override
    public List<InvoiceItem> previewInvoiceItems(InvoicePreviewInput invoiceGenerationInput) {
        List<OrderLineItem> relevantOrderLines = getOrderLinesByType(
            invoiceGenerationInput.getOrderLineItemsNetEffect(),
            invoiceGenerationInput.getChargeMap(),
            ChargeType.PREPAID
        );
        return previewPrepaidInvoiceItems(invoiceGenerationInput, invoiceGenerationInput.getChargeMap(), relevantOrderLines);
    }

    @Override
    public List<InvoiceItem> generateInvoiceItems(InvoiceGenerationInput invoiceGenerationInput) {
        Map<String, Charge> chargeMap = invoiceGenerationInput.getChargeMap();
        List<OrderLineItem> prepaidOrderLines = getOrderLinesByType(
            invoiceGenerationInput.getOrderLineItemsNetEffect(),
            chargeMap,
            ChargeType.PREPAID
        );
        return generatePrepaidInvoiceItemsMemoized(
            invoiceGenerationInput.getMemoizedItemsByOrderLine(),
            chargeMap,
            prepaidOrderLines,
            invoiceGenerationInput
        );
    }

    private List<InvoiceItem> previewPrepaidInvoiceItems(
        InvoicePreviewInput invoiceGenerationInput,
        Map<String, Charge> chargeMap,
        List<OrderLineItem> prepaidOrderLines
    ) {
        return prepaidOrderLines
            .stream()
            .map(item -> {
                Charge charge = chargeMap.get(item.getChargeId());
                return getPrepaidChargeInvoiceItems(invoiceGenerationInput, item, charge);
            })
            .flatMap(Collection::stream)
            .filter(invoiceItem -> !invoiceItem.getPeriodStartDate().isAfter(invoiceGenerationInput.getInvoiceBillingInfo().getInvoiceTargetDate()))
            .toList();
    }

    private List<InvoiceItem> getPrepaidChargeInvoiceItems(InvoicePreviewInput invoiceGenerationInput, OrderLineItem lineItem, Charge charge) {
        Optional<List<BillingPeriod>> customBillingPeriods = getCustomBillingPeriodsIfApplicable(invoiceGenerationInput, lineItem);

        // Update - don't do this if custom billing periods are present, hence the empty check
        if (charge.getRecurrence() == null && customBillingPeriods.isEmpty()) {
            var billingPeriod = new BillingPeriod(
                lineItem.getEffectiveDate(),
                lineItem.getEndDate(),
                lineItem.getEndDate(),
                new Recurrence(Cycle.PAID_IN_FULL, 1)
            );
            InvoiceItem invoiceItem = getPrepaidInvoiceItem(lineItem, charge, billingPeriod);
            return List.of(invoiceItem);
        }

        InvoiceBillingInfo invoiceBillingInfo = invoiceGenerationInput.getInvoiceBillingInfo();

        List<BillingPeriod> billingPeriods = customBillingPeriods.orElseGet(() ->
            subscriptionBillingPeriodService.getBillingPeriods(
                invoiceBillingInfo.getSubscriptionStart(),
                invoiceBillingInfo.getSubscriptionEnd(),
                invoiceBillingInfo.getBillingAnchorDate(),
                invoiceBillingInfo.getInvoiceTargetDate(),
                invoiceBillingInfo.getTimeZone().toZoneId(),
                charge.getRecurrence() != null ? charge.getRecurrence() : new Recurrence(Cycle.PAID_IN_FULL, 1),
                charge.getBillingTerm()
            )
        );

        List<InvoiceItem> invoiceItems = new ArrayList<>();
        for (var billingPeriod : billingPeriods) {
            if (
                !orderLineItemInPeriod(
                    lineItem,
                    billingPeriod,
                    charge,
                    invoiceBillingInfo.getInvoiceTargetDate(),
                    invoiceBillingInfo.getSubscriptionEnd()
                )
            ) {
                continue;
            }

            InvoiceItem invoiceItem = getPrepaidInvoiceItem(lineItem, charge, billingPeriod);
            invoiceItems.add(invoiceItem);
        }

        return invoiceItems;
    }

    private InvoiceItem getPrepaidInvoiceItem(OrderLineItem lineItem, Charge charge, BillingPeriod billingPeriod) {
        ListAmount listAmount = invoiceAmountCalculator.calculatePrepaidListAmount(lineItem, charge).map(Numbers::makeCurrencyScale);

        // do not prorate prepaid amounts
        InvoiceItemAmounts invoiceItemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(
            lineItem.getDiscounts(),
            lineItem.getPredefinedDiscounts(),
            listAmount,
            invoiceAmountCalculator.getListUnitPrice(charge, lineItem),
            lineItem.getQuantity(),
            lineItem.getListPriceOverrideRatio()
        );
        Period orderItemBillingPeriodOverlap = getItemOverlapPeriod(lineItem.getEffectiveDate(), lineItem.getEndDate(), billingPeriod);

        var invoiceItemBuilder = InvoiceItem.InvoiceItemBuilder.from(lineItem, invoiceItemAmounts)
            .status(InvoiceStatus.DRAFT)
            .taxAmount(BigDecimal.ZERO)
            .periodStartDate(orderItemBillingPeriodOverlap.getStart())
            .periodEndDate(orderItemBillingPeriodOverlap.getEnd())
            .triggerOn(charge.getBillingTerm(), billingPeriod.getStart(), billingPeriod.getEnd());

        return invoiceItemBuilder.createInvoiceItem();
    }

    private List<InvoiceItem> generatePrepaidInvoiceItemsMemoized(
        Map<String, List<MemoizedInvoiceLineItem>> memoizedByOrderLineMap,
        Map<String, Charge> chargeMap,
        List<OrderLineItem> prepaidOrderLines,
        InvoiceGenerationInput invoiceGenerationInput
    ) {
        List<OrderLineItem> standardPrepaidOrderLines = getNonEventBasedAndNonAdhocCustomBillingOrderLines(
            prepaidOrderLines,
            chargeMap,
            invoiceGenerationInput
        );
        List<OrderLineItem> adhocCustomBillingOrderLines = getAdhocCustomBillingOrderLines(prepaidOrderLines, standardPrepaidOrderLines);
        List<InvoiceItem> standardPrepaidInvoiceItems = generatePrepaidInvoiceItemsMemoizedExcludingBilled(
            memoizedByOrderLineMap,
            chargeMap,
            standardPrepaidOrderLines,
            invoiceGenerationInput
        );
        List<InvoiceItem> adhocCustomBillingInvoiceItems = generateAdhocCustomBillingInvoiceItems(
            invoiceGenerationInput,
            adhocCustomBillingOrderLines
        );
        return Stream.of(standardPrepaidInvoiceItems, adhocCustomBillingInvoiceItems).flatMap(List::stream).toList();
    }

    private List<InvoiceItem> generatePrepaidInvoiceItemsMemoizedExcludingBilled(
        Map<String, List<MemoizedInvoiceLineItem>> memoizedByOrderLineMap,
        Map<String, Charge> chargeMap,
        List<OrderLineItem> standardPrepaidOrderLines,
        InvoicePreviewInput invoiceGenerationInput
    ) {
        List<InvoiceItem> invoiceItems = new ArrayList<>();
        List<String> orderLineItemIds = standardPrepaidOrderLines.stream().map(OrderLineItem::getOrderLineId).toList();
        Map<String, Set<Instant>> alreadyInvoicedItems = invoiceDAO
            .getAllInvoiceItemsForOrderLineIdsPastThreshold(orderLineItemIds, invoiceGenerationInput.getInvoiceBillingInfo().getSubscriptionStart())
            .stream()
            .collect(Collectors.groupingBy(InvoiceItem::getOrderLineItemId, Collectors.mapping(InvoiceItem::getPeriodStartDate, Collectors.toSet())));

        standardPrepaidOrderLines.forEach(item -> {
            var charge = chargeMap.get(item.getChargeId());
            if (memoizedByOrderLineMap.containsKey(item.getOrderLineId())) {
                List<InvoiceItem> memoized = memoizedByOrderLineMap
                    .get(item.getOrderLineId())
                    .stream()
                    .map(memoizedInvoiceLineItem -> memoizedInvoiceLineItem.to(item))
                    .toList();
                invoiceItems.addAll(memoized);
            } else {
                invoiceItems.addAll(getPrepaidChargeInvoiceItems(invoiceGenerationInput, item, charge));
            }
        });
        return invoiceItems
            .stream()
            .filter(invoiceItem -> !invoiceItem.getPeriodStartDate().isAfter(invoiceGenerationInput.getInvoiceBillingInfo().getInvoiceTargetDate()))
            .filter(invoiceItem ->
                !alreadyInvoicedItems.getOrDefault(invoiceItem.getOrderLineItemId(), Set.of()).contains(invoiceItem.getPeriodStartDate())
            )
            .toList();
    }

    @Override
    public List<InvoiceItem> getInvoiceItemsForOrderLineItem(String orderLineId) {
        return invoiceDAO.getInvoiceItemsForOrderLineItem(orderLineId);
    }
}
