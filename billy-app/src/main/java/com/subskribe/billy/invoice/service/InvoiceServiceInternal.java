package com.subskribe.billy.invoice.service;

import static com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService.getItemOverlapPeriod;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Functions;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.ExternalDependencyException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.NegativeInvoicePostException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.foreignexchange.model.TransactionalExchangeRate;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateService;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.EmailNotifiersList;
import com.subskribe.billy.invoice.model.ImmutableInvoiceBillingInfo;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceChargeInclusionOption;
import com.subskribe.billy.invoice.model.InvoiceGenerationCheckResult;
import com.subskribe.billy.invoice.model.InvoiceGenerationMethod;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceItemPreview;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.model.UpdateInvoiceRequest;
import com.subskribe.billy.invoice.number.InvoiceNumberGenerator;
import com.subskribe.billy.invoice.service.processor.InvoiceProcessorUtility;
import com.subskribe.billy.invoice.service.processor.OneTimeInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PercentOfInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PrepaidInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.RecurringInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.UsageInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.model.ImmutableInvoiceGenerationInput;
import com.subskribe.billy.invoice.service.processor.model.ImmutableInvoicePreviewInput;
import com.subskribe.billy.invoice.service.processor.model.InvoiceGenerationInput;
import com.subskribe.billy.invoice.service.processor.model.InvoicePreviewInput;
import com.subskribe.billy.invoice.service.processor.model.ProcessorMode;
import com.subskribe.billy.invoice.tax.model.ImmutableCommitTaxTransactionInput;
import com.subskribe.billy.invoice.tax.model.ImmutableTaxCalculationInput;
import com.subskribe.billy.invoice.tax.model.ImmutableTaxPreviewInput;
import com.subskribe.billy.invoice.tax.model.TaxCalculationInput;
import com.subskribe.billy.invoice.tax.model.TaxLineItem;
import com.subskribe.billy.invoice.tax.model.TaxPreviewInput;
import com.subskribe.billy.invoice.tax.model.TaxTransaction;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.metrics.datadog.AllMetrics;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.order.EvergreenUtils;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderCustomBillingService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.payment.processor.service.PaymentProcessorJobQueueService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.model.TaxRate;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.resources.shared.PaginationResponseJson;
import com.subskribe.billy.revrec.model.TransactionType;
import com.subskribe.billy.revrec.services.RevenueRecognitionJobService;
import com.subskribe.billy.shared.contactfetcher.ContactFetcherResult;
import com.subskribe.billy.shared.contactfetcher.ContactGroupOrUserFetcher;
import com.subskribe.billy.shared.contactfetcher.ContactType;
import com.subskribe.billy.shared.entitycache.EntityCache;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.Status;
import com.subskribe.billy.shared.enums.TaxExemptionUseCode;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.usage.model.CreditBucket;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Currency;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

@AllMetrics
@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class InvoiceServiceInternal implements InvoiceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InvoiceServiceInternal.class);

    private static final Set<ContactType> CONTACT_TYPES = Set.of(ContactType.ACCOUNT_CONTACT, ContactType.USER);

    private enum BackfillOrderType {
        NO_EXISTING_MEMOIZED_LINES,
        PERCENT_OF_CHARGE_ORDER_WITH_NON_MEMOIZED_LINES,
        EXISTING_MEMOIZED_LINES,
    }

    public static final ZoneId DEFAULT_TIME_ZONE = ZoneOffset.UTC;
    private static final boolean DO_NOT_SKIP_PERCENT_OF = false;
    public static final boolean INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION = true;

    private static final Comparator<Pair<Integer, InvoiceItem>> RANKED_INVOICE_ITEM_COMPARATOR = Comparator.comparing(Pair::getLeft);

    private final OrderGetService orderGetService;
    private final SubscriptionGetService subscriptionGetService;
    private final AccountGetService accountGetService;
    private final ProductCatalogGetService productCatalogGetService;
    private final InvoiceDAO invoiceDAO;
    private final TenantIdProvider tenantIdProvider;
    private final InvoiceNumberGenerator invoiceNumberGenerator;
    private final DSLContextProvider dslContextProvider;
    private final InvoiceDocumentGenerationTrackerService invoiceDocumentGenerationTrackerService;
    private final TaxService taxService;
    private final TaxRateGetService taxRateGetService;
    private final TenantSettingService tenantSettingService;
    private final PercentOfChargeInvoiceHelper percentOfChargeHelper;
    private final PercentOfInvoiceProcessor percentOfInvoiceProcessor;
    private final PaymentProcessorJobQueueService paymentProcessorJobQueueService;
    private final InvoiceEventService invoiceEventService;
    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;
    private final ProrationConfigurationGetService prorationConfigurationGetService;
    private final RevenueRecognitionJobService revenueRecognitionJobService;
    private final AccountingPeriodService accountingPeriodService;
    private final EmailContactListService emailContactListService;
    private final ContactGroupOrUserFetcher contactGroupOrUserFetcher;
    private final FeatureService featureService;
    private final RecurringInvoiceProcessor recurringInvoiceProcessor;
    private final OneTimeInvoiceProcessor oneTimeInvoiceProcessor;
    private final PrepaidInvoiceProcessor prepaidInvoiceProcessor;
    private final UsageInvoiceProcessor usageInvoiceProcessor;
    private final ReportingJobQueueService reportingJobQueueService;
    private final Clock clock;
    private final TransactionalExchangeRateService transactionalExchangeRateService;
    private final OrderCustomBillingService orderCustomBillingService;
    private final EntityGetService entityGetService;
    private final CustomFieldService customFieldService;
    private final OrderMapper orderMapper;

    @Inject
    public InvoiceServiceInternal(
        OrderGetService orderGetService,
        SubscriptionGetService subscriptionGetService,
        AccountGetService accountGetService,
        ProductCatalogGetService productCatalogGetService,
        TenantIdProvider tenantIdProvider,
        InvoiceDAO invoiceDAO,
        InvoiceNumberGenerator invoiceNumberGenerator,
        DSLContextProvider dslContextProvider,
        TaxService taxService,
        TaxRateGetService taxRateGetService,
        InvoiceDocumentGenerationTrackerService invoiceDocumentGenerationTrackerService,
        TenantSettingService tenantSettingService,
        PercentOfChargeInvoiceHelper percentOfChargeHelper,
        PercentOfInvoiceProcessor percentOfInvoiceProcessor,
        PaymentProcessorJobQueueService paymentProcessorJobQueueService,
        InvoiceEventService invoiceEventService,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        ProrationConfigurationGetService prorationConfigurationGetService,
        RevenueRecognitionJobService revenueRecognitionJobService,
        AccountingPeriodService accountingPeriodService,
        EmailContactListService emailContactListService,
        ContactGroupOrUserFetcher contactGroupOrUserFetcher,
        FeatureService featureService,
        RecurringInvoiceProcessor recurringInvoiceProcessor,
        OneTimeInvoiceProcessor oneTimeInvoiceProcessor,
        PrepaidInvoiceProcessor prepaidInvoiceProcessor,
        UsageInvoiceProcessor usageInvoiceProcessor,
        ReportingJobQueueService reportingJobQueueService,
        Clock clock,
        TransactionalExchangeRateService transactionalExchangeRateService,
        OrderCustomBillingService orderCustomBillingService,
        EntityGetService entityGetService,
        CustomFieldService customFieldService
    ) {
        this.orderGetService = orderGetService;
        this.subscriptionGetService = subscriptionGetService;
        this.accountGetService = accountGetService;
        this.productCatalogGetService = productCatalogGetService;
        this.tenantIdProvider = tenantIdProvider;
        this.invoiceDAO = invoiceDAO;
        this.invoiceNumberGenerator = invoiceNumberGenerator;
        this.dslContextProvider = dslContextProvider;
        this.invoiceDocumentGenerationTrackerService = invoiceDocumentGenerationTrackerService;
        this.taxService = taxService;
        this.taxRateGetService = taxRateGetService;
        this.tenantSettingService = tenantSettingService;
        this.percentOfChargeHelper = percentOfChargeHelper;
        this.percentOfInvoiceProcessor = percentOfInvoiceProcessor;
        this.paymentProcessorJobQueueService = paymentProcessorJobQueueService;
        this.invoiceEventService = invoiceEventService;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.prorationConfigurationGetService = prorationConfigurationGetService;
        this.revenueRecognitionJobService = revenueRecognitionJobService;
        this.accountingPeriodService = accountingPeriodService;
        this.emailContactListService = emailContactListService;
        this.contactGroupOrUserFetcher = contactGroupOrUserFetcher;
        this.featureService = featureService;
        this.recurringInvoiceProcessor = recurringInvoiceProcessor;
        this.oneTimeInvoiceProcessor = oneTimeInvoiceProcessor;
        this.prepaidInvoiceProcessor = prepaidInvoiceProcessor;
        this.usageInvoiceProcessor = usageInvoiceProcessor;
        this.reportingJobQueueService = reportingJobQueueService;
        this.clock = clock;
        this.transactionalExchangeRateService = transactionalExchangeRateService;
        this.orderCustomBillingService = orderCustomBillingService;
        this.entityGetService = entityGetService;
        this.customFieldService = customFieldService;
        orderMapper = Mappers.getMapper(OrderMapper.class);
    }

    // todo: we can refactor this method and copy over the updated fields from the request to the invoice
    //  that will make it easier to pass around the invoice object
    public Invoice updateInvoice(Invoice.Number invoiceNumber, UpdateInvoiceRequest updateInvoiceRequest) {
        updateInvoiceRequest.validate();
        Validator.validateNonNullArgument(invoiceNumber, "invoiceNumber");
        Validator.validateStringNotBlank(invoiceNumber.getNumber(), "Invoice Number is required");

        Invoice originalInvoice = invoiceDAO.getInvoiceByInvoiceNumber(invoiceNumber);
        validateInvoiceDate(updateInvoiceRequest, originalInvoice);
        validateInvoiceDueDate(updateInvoiceRequest, originalInvoice);

        Subscription subscription = subscriptionGetService.getSubscription(originalInvoice.getSubscriptionId());
        AccountContact subscriptionBillingContact = accountGetService.getContact(subscription.getBillingContactId());
        AccountContact billingContact = originalInvoice.getBillingContact();
        if (StringUtils.isNotBlank(updateInvoiceRequest.getBillingContactId())) {
            billingContact = accountGetService.getContact(updateInvoiceRequest.getBillingContactId());
            validateAccountContact(billingContact, originalInvoice, subscriptionBillingContact);
        }
        AccountContact finalBillingContact = billingContact;
        EmailNotifiersList emailNotifiersList = validateEmailNotifiersList(updateInvoiceRequest, originalInvoice, subscriptionBillingContact);

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        // prepare updates for functional amounts
        Optional<Invoice> invoiceFunctionalAmountUpdates = prepareFunctionalAmountUpdate(originalInvoice, updateInvoiceRequest.getInvoiceDate());

        Invoice updatedInvoice = dslContext.transactionResult(configuration ->
            updateInvoiceTransaction(
                configuration,
                invoiceNumber,
                updateInvoiceRequest,
                finalBillingContact,
                emailNotifiersList,
                invoiceFunctionalAmountUpdates
            )
        );

        generateInvoiceDocument(updatedInvoice.getInvoiceNumber());
        return updatedInvoice;
    }

    public void updateFunctionalAmounts(Configuration configuration, Invoice invoice) {
        Optional<Invoice> invoiceFunctionalAmountUpdates = prepareFunctionalAmountUpdate(invoice, invoice.getInvoiceDate());
        if (invoiceFunctionalAmountUpdates.isEmpty()) {
            LOGGER.info("skipping backfill, invoice = {}", invoice.getInvoiceNumber());
            return;
        }
        LOGGER.info("backfilling invoice = {}", invoice.getInvoiceNumber());
        invoiceDAO.updateInvoiceFunctionalAmounts(configuration, invoiceFunctionalAmountUpdates.get());
        for (InvoiceItem invoiceItem : invoiceFunctionalAmountUpdates.get().getInvoiceItems()) {
            invoiceDAO.updateInvoiceItemFunctionalAmounts(configuration, invoiceItem);
        }
    }

    // prepare invoice and invoice items with potentially updated functional amounts
    // if the functional amounts are not updated, this will return empty
    private Optional<Invoice> prepareFunctionalAmountUpdate(Invoice originalInvoice, Instant invoiceDate) {
        if (!featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            return Optional.empty();
        }
        Validator.validateCollectionNotEmpty(originalInvoice.getInvoiceItems(), "invoiceItems");
        // todo: refactor the calling method so that we use an invoice object with invoice date.
        //  with that we can extract below into a method with invoice as input
        String transactionalCurrencyCode = originalInvoice.getCurrency().getCurrencyCode();
        Entity entity = entityGetService.getEntityById(originalInvoice.getEntityId());
        String functionalCurrencyCode = entity.getFunctionalCurrency();
        Optional<TransactionalExchangeRate> optionalExchangeRate = getExchangeRateByInvoiceDate(
            Optional.ofNullable(invoiceDate),
            transactionalCurrencyCode,
            functionalCurrencyCode
        );
        Invoice invoiceFunctionalAmounts = null;
        Long originalExchangeRateDate = Optional.ofNullable(originalInvoice.getExchangeRateDate()).map(Instant::getEpochSecond).orElse(null);
        Long updatedExchangeRateDate = optionalExchangeRate.map(TransactionalExchangeRate::getEffectiveDate).orElse(null);
        BigDecimal originalExchangeRate = originalInvoice.getExchangeRate();
        BigDecimal updatedExchangeRate = optionalExchangeRate.map(TransactionalExchangeRate::getExchangeRate).orElse(null);
        if (
            !Numbers.equals(originalExchangeRate, updatedExchangeRate) ||
            !Objects.equals(originalExchangeRateDate, updatedExchangeRateDate) ||
            Objects.isNull(originalInvoice.getFunctionalTotal())
        ) {
            // this means either of below:
            // 1. invoice date changed, or
            // 2. exchange rate is overridden for the same invoice date
            List<InvoiceItem> invoiceItems = populateFunctionalAmountsOnInvoiceItems(
                originalInvoice.getInvoiceItems(),
                transactionalCurrencyCode,
                functionalCurrencyCode,
                optionalExchangeRate
            );
            Invoice.InvoiceBuilder invoiceBuilder = new Invoice.InvoiceBuilder().from(originalInvoice).invoiceItems(invoiceItems);
            populateFunctionalAmountsToInvoice(invoiceBuilder, optionalExchangeRate, invoiceItems);
            invoiceFunctionalAmounts = invoiceBuilder.createInvoice();
        }
        return Optional.ofNullable(invoiceFunctionalAmounts);
    }

    private Invoice updateInvoiceTransaction(
        Configuration configuration,
        Invoice.Number invoiceNumber,
        UpdateInvoiceRequest updateInvoiceRequest,
        AccountContact billingContact,
        EmailNotifiersList emailNotifiersList,
        Optional<Invoice> invoiceFunctionalAmountUpdates
    ) {
        Invoice updatedInvoice = invoiceDAO
            .updateInvoiceTransaction(
                configuration,
                invoiceNumber,
                updateInvoiceRequest.getInvoiceDate(),
                updateInvoiceRequest.getDueDate(),
                updateInvoiceRequest.getNote(),
                updateInvoiceRequest.getPurchaseOrderNumber(),
                billingContact,
                emailNotifiersList,
                invoiceFunctionalAmountUpdates
            )
            .orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.INVOICE, invoiceNumber.getNumber()));

        if (invoiceFunctionalAmountUpdates.isPresent()) {
            List<InvoiceItem> invoiceItems = invoiceFunctionalAmountUpdates.get().getInvoiceItems();
            for (InvoiceItem invoiceItem : invoiceItems) {
                invoiceDAO.updateInvoiceItemFunctionalAmounts(configuration, invoiceItem);
            }
        }
        return updatedInvoice;
    }

    public void updateInvoiceErpId(Invoice.Number invoiceNumber, String erpId) {
        Validator.checkNonNullInternal(invoiceNumber, "invoiceNumber cannot be null");
        Validator.checkArgumentNotBlankInternal(erpId, "erpId cannot be blank");

        invoiceDAO.updateInvoiceErpId(invoiceNumber, erpId);
    }

    private void validateInvoiceDueDate(UpdateInvoiceRequest updateInvoiceRequest, Invoice originalInvoice) {
        if (updateInvoiceRequest.getDueDate() == null) {
            return;
        }

        if (
            originalInvoice.getDueDate() != null &&
            updateInvoiceRequest.getDueDate().getEpochSecond() == originalInvoice.getDueDate().getEpochSecond()
        ) {
            // due date is the same as stored, clear from request and continue. Here we're clearing instead of re-setting the same value to avoid potential concurrency issues
            updateInvoiceRequest.setDueDate(null);
            return;
        }

        if (InvoiceStatus.DRAFT != originalInvoice.getStatus()) {
            throw new InvalidInputException("Invoice due date can only be updated for draft invoices.");
        }

        if (updateInvoiceRequest.getDueDate().isBefore(updateInvoiceRequest.getInvoiceDate())) {
            throw new InvalidInputException("Invoice due date cannot be before invoice date");
        }
    }

    private EmailNotifiersList validateEmailNotifiersList(
        UpdateInvoiceRequest updateInvoiceRequest,
        Invoice originalInvoice,
        AccountContact subscriptionBillingContact
    ) {
        if (updateInvoiceRequest.getEmailNotifiersList() == null) {
            return originalInvoice.getEmailNotifiersList();
        }
        Validator.validateNonNullArgument(updateInvoiceRequest.getEmailNotifiersList().getToIds(), "toIds");
        Validator.validateNonNullArgument(updateInvoiceRequest.getEmailNotifiersList().getCcIds(), "ccIds");
        Validator.validateNonNullArgument(updateInvoiceRequest.getEmailNotifiersList().getBccIds(), "bccIds");

        List<String> contactIds = new ArrayList<>(updateInvoiceRequest.getEmailNotifiersList().getToIds());
        contactIds.addAll(updateInvoiceRequest.getEmailNotifiersList().getCcIds());
        contactIds.addAll(updateInvoiceRequest.getEmailNotifiersList().getBccIds());
        if (CollectionUtils.isEmpty(contactIds)) {
            return updateInvoiceRequest.getEmailNotifiersList();
        }

        ContactFetcherResult contactFetcherResult = contactGroupOrUserFetcher.fetchContactsByType(contactIds, CONTACT_TYPES, false);
        Map<String, AccountContact> accountContactMap = contactFetcherResult
            .getContacts()
            .stream()
            .collect(Collectors.toMap(AccountContact::getContactId, Function.identity()));
        Map<String, User> userMap = contactFetcherResult.getUsers().stream().collect(Collectors.toMap(User::getUserId, Function.identity()));
        for (var contactId : contactIds) {
            if (accountContactMap.containsKey(contactId)) {
                validateAccountContact(accountContactMap.get(contactId), originalInvoice, subscriptionBillingContact);
            } else if (userMap.containsKey(contactId)) {
                User user = userMap.get(contactId);
                if (user.getState() != Status.ACTIVE) {
                    throw new IllegalArgumentException(String.format("User: %s is not active", user.getUserId()));
                }
            } else {
                throw new IllegalArgumentException(String.format("ContactId: %s is invalid", contactId));
            }
        }

        return updateInvoiceRequest.getEmailNotifiersList();
    }

    private void validateAccountContact(AccountContact accountContact, Invoice originalInvoice, AccountContact subscriptionBillingContact) {
        // making sure that accountContact is never null
        if (accountContact == null) {
            throw new IllegalArgumentException("accountContact is null");
        }

        // check if the billingContact is from the same account as the
        // originalInvoiceBillingContact(including reseller) or invoice account
        if (
            accountContact.getAccountId().equals(originalInvoice.getBillingContact().getAccountId()) || // same account from current invoice account
            accountContact.getAccountId().equals(subscriptionBillingContact.getAccountId()) || // same account as subscription billing contact (includes reseller)
            accountContact.getAccountId().equals(originalInvoice.getCustomerAccountId()) // or same account as the invoice account
        ) {
            return;
        }

        throw new IllegalArgumentException(
            String.format("contactId: %s is not from the same account as the subscription account", accountContact.getContactId())
        );
    }

    private void validateInvoiceDate(UpdateInvoiceRequest updateInvoiceRequest, Invoice originalInvoice) {
        long invoiceDateEpochSeconds = originalInvoice.getInvoiceDate() == null ? 0 : originalInvoice.getInvoiceDate().getEpochSecond();

        if (updateInvoiceRequest.getInvoiceDate().getEpochSecond() == invoiceDateEpochSeconds) {
            // request invoice date is the same as existing invoice date. No changes made.
            return;
        }

        if (InvoiceStatus.DRAFT != originalInvoice.getStatus()) {
            throw new IllegalStateException(
                String.format(
                    "Invoice Date can be changed only on draft invoices. Given date: %d, Invoice date: %d",
                    updateInvoiceRequest.getInvoiceDate().getEpochSecond(),
                    invoiceDateEpochSeconds
                )
            );
        }
        validateInvoiceDateIsNotBeforeLastPostedInvoiceDate(originalInvoice.getSubscriptionId(), updateInvoiceRequest.getInvoiceDate());

        if (accountingPeriodService.inClosedAccountingPeriod(originalInvoice.getEntityId(), updateInvoiceRequest.getInvoiceDate())) {
            throw new IllegalArgumentException(
                "Invoice date belongs to a closed accounting period, Invoice date only in open and upcoming accounting periods are allowed"
            );
        }
    }

    private void validateInvoiceDateIsNotBeforeLastPostedInvoiceDate(String subscriptionId, Instant invoiceDate) {
        Optional<Invoice> previousPostedInvoiceOptional = invoiceDAO.getLastPostedInvoiceForSubscription(subscriptionId);
        if (previousPostedInvoiceOptional.isEmpty()) {
            return;
        }

        Invoice previousInvoice = previousPostedInvoiceOptional.get();
        if (previousInvoice.getInvoiceDate().isAfter(invoiceDate)) {
            String message = String.format(
                "Invoice date cannot be before last posted invoice's date for the subscription. SubscriptionId: %s, previous invoice date: %d, current invoice date: %d",
                subscriptionId,
                previousInvoice.getInvoiceDate().getEpochSecond(),
                invoiceDate.getEpochSecond()
            );
            throw new InvalidInputException(message);
        }
    }

    // TODO: Add unit tests for post and delete
    public Invoice postInvoice(Invoice.Number draftInvoiceNumber) {
        return postInvoice(draftInvoiceNumber, false);
    }

    public Invoice postInvoice(Invoice.Number draftInvoiceNumber, boolean isHistorical) {
        Invoice stored = invoiceDAO.getInvoiceByInvoiceNumber(draftInvoiceNumber);

        if (stored == null) {
            throw new ObjectNotFoundException(BillyObjectType.INVOICE, draftInvoiceNumber.getNumber());
        }

        // if invoice is already posted nothing to do just return the invoice
        // this check makes post invoice idempotent
        if (InvoiceStatus.POSTED == stored.getStatus()) {
            return stored;
        }

        ensureInvoiceIsInDraftStatus(stored);

        if (stored.getTotal().compareTo(BigDecimal.ZERO) < 0) {
            throw new NegativeInvoicePostException("Invoice has negative balance and cannot be posted.");
        }

        if (stored.getPurchaseOrderRequired() && StringUtils.isBlank(stored.getPurchaseOrderNumber())) {
            throw new ConflictingStateException(
                String.format(
                    "Unable to post invoice: %s due to missing required PO number. Please update PO number or mark PO as not required.",
                    stored.getInvoiceNumber()
                )
            );
        }

        Subscription subscription = subscriptionGetService.getSubscription(stored.getSubscriptionId());
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        AccountContact shippingContact = stored.getShippingContact();
        AccountContact billingContact = stored.getBillingContact();
        Account account = accountGetService.getAccount(billingContact.getAccountId());

        Instant invoicePostDate = Instant.now();
        Instant invoiceDate = stored.getInvoiceDate() == null
            ? DateTimeConverter.getStartOfCurrentDay(invoicePostDate, timeZone)
            : stored.getInvoiceDate();
        validateInvoiceDateIsNotBeforeLastPostedInvoiceDate(stored.getSubscriptionId(), invoiceDate);
        validateAccountingPeriod(stored.getEntityId(), invoiceDate);

        Instant invoiceDueDate = getInvoiceDueDate(stored, invoiceDate, subscription, timeZone);

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        // prepare updates for functional amounts
        Optional<Invoice> invoiceFunctionalAmountUpdates = prepareFunctionalAmountUpdate(stored, invoiceDate);

        Invoice postedInvoice = dslContext.transactionResult(configuration -> {
            Invoice.Number invoiceNumber = getInvoiceNumberForPostedInvoice(configuration, subscription.getEntityId(), isHistorical);
            Invoice result = invoiceDAO.postInvoice(
                configuration,
                invoiceNumber,
                draftInvoiceNumber,
                invoiceDate,
                invoicePostDate,
                invoiceDueDate,
                invoiceFunctionalAmountUpdates
            );
            invoiceEventService.publishEventInTransaction(configuration, result);

            // TODO: taxTransactionCode will not be optional with tax integration
            if (stored.getTaxTransactionCode() != null) {
                taxService.commitTaxTransaction(
                    ImmutableCommitTaxTransactionInput.builder()
                        // note we are passing in the stored tax transaction code
                        // when invoice is posted tax transaction code is made to be null
                        .taxTransactionCode(stored.getTaxTransactionCode())
                        .invoice(result)
                        .timeZone(timeZone)
                        .taxExceptionUseCode(Optional.ofNullable(account.getTaxExemptionUseCode()))
                        .account(account)
                        .shippingAddress(shippingContact.getAddress())
                        .billingAddress(Optional.ofNullable(billingContact.getAddress()))
                        .build()
                );
            }

            paymentProcessorJobQueueService.addPaymentProcessorJobUnit(invoiceNumber.getNumber(), tenantIdProvider.provideTenantIdString());

            // todo: Does this need to be part of the invoice posting transaction
            reportingJobQueueService.addUsageArrForInvoiceInTransaction(configuration, result, tenantIdProvider.provideTenantIdString());
            if (Numbers.isZero(result.getTotal())) {
                invoiceDAO.updateInvoiceStatusInTransaction(
                    result.getInvoiceNumber(),
                    InvoiceStatus.POSTED,
                    InvoiceStatus.PAID,
                    tenantIdProvider.provideTenantIdString(),
                    configuration
                );
                result = invoiceDAO.getInvoiceByInvoiceNumberInTransaction(configuration, result.getInvoiceNumber());
            }

            // todo: this (and also some post invoice transaction actions) should be done in a transaction to prevent partial failures
            copyCustomFields(draftInvoiceNumber, invoiceNumber);

            return result;
        });

        performPostInvoiceTransactionActions(postedInvoice);
        return postedInvoice;
    }

    private void copyCustomFields(Invoice.Number fromInvoiceNumber, Invoice.Number toInvoiceNumber) {
        // todo: introduce an explicit move custom field parent id operation?
        CustomField customField = customFieldService.getCustomFields(CustomFieldParentType.INVOICE, fromInvoiceNumber.getNumber());

        if (customField != null && !customField.isEmpty()) {
            customFieldService.setCustomFields(CustomFieldParentType.INVOICE, toInvoiceNumber.getNumber(), customField);
        }
    }

    private Instant getInvoiceDueDate(Invoice invoice, Instant invoiceDate, Subscription subscription, TimeZone timeZone) {
        Instant storedInvoiceDueDate = invoice.getDueDate();

        if (storedInvoiceDueDate == null) {
            // nothing updated by the user, use invoice date + payment term to determine due date
            return DateTimeCalculator.plusDays(timeZone.toZoneId(), invoiceDate, subscription.getPaymentTerm().getPaymentDueInDays());
        } else {
            if (storedInvoiceDueDate.isBefore(invoiceDate)) {
                throw new InvalidInputException("Invoice due date cannot be before invoice date");
            }

            return storedInvoiceDueDate;
        }
    }

    // make sure invoice is not posted with invoice date (either explicitly set or posting date) before start of current accounting period
    // todo: This is not guaranteed to be 100% reliable as accounting period state can change while this check is being performed
    private void validateAccountingPeriod(String entityId, Instant invoiceDate) {
        if (accountingPeriodService.inClosedAccountingPeriod(entityId, invoiceDate)) {
            throw new IllegalStateException("Cannot post invoice with invoice date earlier than current open accounting period.");
        }
    }

    // TODO: return published event if required

    private void performPostInvoiceTransactionActions(Invoice postedInvoice) {
        // NOTE: rev rec job kickoff needs to be performed first and needs to succeed before rest of the actions
        // are performed
        if (featureService.isEnabled(Feature.REV_REC_INVOICE)) {
            createJobToGenerateRecognitionSchedule(postedInvoice);
        }
        generateInvoiceDocument(postedInvoice.getInvoiceNumber());
    }

    private void createJobToGenerateRecognitionSchedule(Invoice invoice) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        revenueRecognitionJobService.createRecognitionScheduleJob(
            tenantId,
            TransactionType.INVOICE,
            invoice.getInvoiceNumber().getNumber(),
            invoice.getEntityId()
        );
    }

    public Invoice deleteInvoice(Invoice.Number draftInvoiceNumber) {
        Invoice invoice = invoiceDAO.getInvoiceWithNoLineItems(draftInvoiceNumber);
        if (invoice == null) {
            throw new ObjectNotFoundException(BillyObjectType.INVOICE, draftInvoiceNumber.getNumber());
        }

        ensureInvoiceIsInDraftStatus(invoice);
        return invoiceDAO.deleteInvoice(draftInvoiceNumber, InvoiceStatus.DRAFT);
    }

    public Invoice deleteVoidedInvoice(Configuration configuration, Invoice.Number draftInvoiceNumber) {
        Invoice invoice = invoiceDAO.getInvoiceWithNoLineItems(draftInvoiceNumber);
        if (invoice == null) {
            throw new ObjectNotFoundException(BillyObjectType.INVOICE, draftInvoiceNumber.getNumber());
        }

        checkInvoiceStatus(invoice, InvoiceStatus.VOIDED);
        return invoiceDAO.deleteInvoice(configuration, draftInvoiceNumber, InvoiceStatus.VOIDED);
    }

    private void ensureInvoiceIsInDraftStatus(Invoice invoice) {
        checkInvoiceStatus(invoice, InvoiceStatus.DRAFT);
    }

    private void checkInvoiceStatus(Invoice invoice, InvoiceStatus status) {
        if (invoice.getStatus() != status) {
            throw new IllegalStateException(
                String.format("Invoice %s is not in %s status. Status is %s", invoice.getInvoiceNumber().getNumber(), status, invoice.getStatus())
            );
        }
    }

    public List<InvoicePreview> previewAllInvoicesBySubscriptionId(String subscriptionId) {
        if (StringUtils.isBlank(subscriptionId)) {
            throw new IllegalArgumentException("subscriptionId is required");
        }

        // 1. Get all executed orders
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);
        List<Order> executedOrders = orders.stream().filter(order -> order.getStatus() == OrderStatus.EXECUTED).toList();

        // 2. Get all billing periods for subscription.
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        List<BillingPeriod> allBillingPeriods = subscriptionBillingPeriodService.getBillingPeriods(subscription, timeZone, subscription.getEndDate());

        // 3. Generate invoice previews by subscription's billing period for all orders.
        List<List<InvoicePreview>> allOrderInvoicePreviews = executedOrders.stream().map(this::previewInvoicesByBillingPeriods).toList();

        /*
         * 4. For each subscription billing period, roll up invoice previews from orders for matching period.
         * For One time and Prepaid charge types, the InvoicePreview period is same as orderline's effective
         * start date and end dates. For Recurring and Usage charge types it is same as order's billing cycle,
         * which is set to same value as subscription's billing cycle.
         * The roll-up logic below needs to be adjusted if that behavior changes.
         */
        return allBillingPeriods
            .stream()
            .map(billingPeriod -> // For each subscription billing period
                rollupPreviews(
                    billingPeriod,
                    allOrderInvoicePreviews
                        .stream()
                        .flatMap(oneOrderInvoicePreviews -> // For each order's list of invoice previews
                            oneOrderInvoicePreviews
                                .stream()
                                .filter(aPreview -> // Pick those that are within the subscriptions billing period
                                    SubscriptionBillingPeriodService.isInstantInBillingPeriod(aPreview.getBillingPeriod().getStart(), billingPeriod)
                                )
                        )
                        .collect(Collectors.toList())
                )
            )
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private InvoicePreview rollupPreviews(BillingPeriod billingPeriod, List<InvoicePreview> previews) {
        if (previews.isEmpty()) {
            return null;
        }
        var subtotal = BigDecimal.ZERO;
        var total = BigDecimal.ZERO;
        var totalDiscount = BigDecimal.ZERO;
        var totalTaxEstimate = BigDecimal.ZERO;
        var totalListAmountBeforeOverrides = BigDecimal.ZERO;
        List<InvoiceItem> invoiceItems = new ArrayList<>(10);
        List<InvoiceItemPreview> invoiceItemPreviews = new ArrayList<>(10);
        String orderId = previews.get(0).getOrderId(); // TODO: Use subscription Id instead?

        for (var preview : previews) {
            subtotal = subtotal.add(preview.getSubtotal());
            total = total.add(preview.getTotal());
            totalDiscount = totalDiscount.add(preview.getTotalDiscount());
            totalTaxEstimate = totalTaxEstimate.add(preview.getTaxEstimate());
            totalListAmountBeforeOverrides = totalListAmountBeforeOverrides.add(preview.getTotalListAmountBeforeOverrides());
            invoiceItems.addAll(preview.getInvoiceItems());
            invoiceItemPreviews.addAll(preview.getLineItems());
        }
        return new InvoicePreview(
            orderId,
            billingPeriod,
            invoiceItemPreviews,
            subtotal,
            total,
            totalDiscount,
            totalTaxEstimate,
            totalListAmountBeforeOverrides,
            invoiceItems
        );
    }

    public List<InvoicePreview> previewAllInvoicesForOrder(String orderId) {
        Optional<Order> orderOptional = orderGetService.getOrderByOrderIdOptional(orderId);
        if (orderOptional.isEmpty()) {
            return List.of();
        }
        Order order = orderOptional.get();
        return previewInvoicesForOrder(order);
    }

    private List<InvoicePreview> previewInvoicesForOrder(Order order) {
        EvergreenUtils.extendInvoicePreviewWindowForEvergreenOrder(order);

        String orderId = order.getOrderId();
        List<String> chargeIds = order.getLineItems().stream().map(OrderLineItem::getChargeId).toList();
        List<Charge> charges = productCatalogGetService.getChargesByChargeId(chargeIds);
        Map<String, Charge> chargeMap = charges.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));

        InvoicePreview preview = previewInvoiceByOrderPeriod(order);
        List<InvoiceItem> invoiceItems = preview.getInvoiceItems();
        List<Instant> triggerOnDates = invoiceItems.stream().map(InvoiceItem::getTriggerOn).filter(Objects::nonNull).distinct().toList();

        List<BillingPeriod> billingPeriods = getBillingPeriodsFromTriggerOnDates(triggerOnDates, order.getStartDate(), order.getEndDate());
        Map<BillingPeriod, List<InvoiceItem>> billingPeriodMap = groupLineItemsInBillingPeriods(billingPeriods, invoiceItems, order);

        // create invoice previews for each billing period
        List<InvoicePreview> invoicePreviews = new ArrayList<>();
        for (var entry : billingPeriodMap.entrySet()) {
            BillingPeriod billingPeriod = entry.getKey();
            List<InvoiceItem> itemsForPeriod = entry.getValue();

            if (!itemsForPeriod.isEmpty()) {
                InvoicePreview invoicePreview = InvoicePreviewBuilder.getInvoicePreview(orderId, itemsForPeriod, billingPeriod, chargeMap);
                invoicePreviews.add(invoicePreview);
            }
        }

        return invoicePreviews;
    }

    private Map<BillingPeriod, List<InvoiceItem>> groupLineItemsInBillingPeriods(
        List<BillingPeriod> billingPeriods,
        List<InvoiceItem> invoiceItems,
        Order order
    ) {
        Map<BillingPeriod, List<InvoiceItem>> billingPeriodMap = new LinkedHashMap<>(billingPeriods.size());
        for (BillingPeriod billingPeriod : billingPeriods) {
            billingPeriodMap.put(billingPeriod, new ArrayList<>());
        }
        for (InvoiceItem invoiceItem : invoiceItems) {
            boolean added = false;
            for (var entry : billingPeriodMap.entrySet()) {
                var billingPeriod = entry.getKey();
                var invoiceItemsForPeriod = entry.getValue();
                Instant triggerOn = invoiceItem.getTriggerOn();
                if (triggerOn.compareTo(billingPeriod.getStart()) >= 0 && triggerOn.compareTo(billingPeriod.getEnd()) < 0) {
                    invoiceItemsForPeriod.add(invoiceItem);
                    added = true;
                }
            }
            if (!added && invoiceItem.getTriggerOn().compareTo(order.getEndDate()) == 0) {
                // invoice item is triggered on the order end date
                // add it to the last billing period
                BillingPeriod lastBillingPeriod = billingPeriods.get(billingPeriods.size() - 1);
                billingPeriodMap.get(lastBillingPeriod).add(invoiceItem);
                added = true;
            }
            if (!added) {
                throw new InvariantCheckFailedException("no billing period found");
            }
        }
        return billingPeriodMap;
    }

    private static List<BillingPeriod> getBillingPeriodsFromTriggerOnDates(List<Instant> triggerOnDates, Instant startDate, Instant endDate) {
        List<Instant> allDates = new ArrayList<>(triggerOnDates);
        allDates.add(startDate);
        allDates.add(endDate);
        allDates = allDates.stream().distinct().sorted().toList();
        if (CollectionUtils.isEmpty(allDates)) {
            throw new InvariantCheckFailedException("no trigger on dates exist");
        }
        List<BillingPeriod> billingPeriods = new ArrayList<>();
        if (allDates.size() == 1) {
            Instant onlyDate = allDates.get(0);
            billingPeriods.add(new BillingPeriod(onlyDate, onlyDate, onlyDate, new Recurrence(Cycle.PAID_IN_FULL, 1)));
        }

        for (int i = 1; i < allDates.size(); i++) {
            billingPeriods.add(new BillingPeriod(allDates.get(i - 1), allDates.get(i), allDates.get(i), new Recurrence(Cycle.PAID_IN_FULL, 1)));
        }
        billingPeriods.add(new BillingPeriod(endDate, endDate, endDate, new Recurrence(Cycle.PAID_IN_FULL, 1)));
        return billingPeriods;
    }

    public InvoicePreview previewInvoiceByOrderPeriod(String orderId) {
        var order = orderGetService.getOrderByOrderId(orderId);
        return previewInvoiceByOrderPeriod(order);
    }

    public InvoicePreview previewInvoiceByOrderPeriod(Order order) {
        // todo: this is here temporary to facilitate data import, should move to tenant config instead
        return previewInvoiceByOrderPeriod(order, false, !INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION);
    }

    public InvoicePreview previewInvoiceByOrderPeriodForOrderLinesCalculation(Order order) {
        LOGGER.info("Previewing invoice for order lines calculation, order {}", order.getOrderId());
        return previewInvoiceByOrderPeriod(order, false, INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION);
    }

    // todo: generate multiple invoices, one for each period?
    public InvoicePreview previewInvoiceByOrderPeriod(Order order, boolean skipAmendPercentOf, boolean forOrderLineCalculation) {
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        return generateInvoicePreview(order, skipAmendPercentOf, forOrderLineCalculation, prorationConfig, timeZone);
    }

    public InvoicePreview previewInvoiceByOrderBillingCycle(Order order) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Instant billingStartDate = Optional.ofNullable(order.getBillingAnchorDate())
            .map(anchorDate ->
                // If anchor date is before order start date, use the next billing cycle start date for a full billing cycle
                anchorDate.isBefore(order.getStartDate())
                    ? SubscriptionBillingPeriodService.getNextBillingCycleStart(
                        ZonedDateTime.ofInstant(anchorDate, timeZone.toZoneId()),
                        order.getBillingCycle(),
                        1
                    ).toInstant()
                    : anchorDate
            )
            .orElse(order.getStartDate());
        ZonedDateTime zonedBillingStartDate = ZonedDateTime.ofInstant(billingStartDate, timeZone.toZoneId());
        Instant billingEndDate = SubscriptionBillingPeriodService.getNextBillingCycleStart(
            zonedBillingStartDate,
            order.getBillingCycle(),
            1
        ).toInstant();
        BillingPeriod stubPeriod = subscriptionBillingPeriodService
            .getBillingPeriods(
                billingStartDate,
                billingEndDate,
                billingStartDate,
                billingStartDate,
                timeZone.toZoneId(),
                order.getBillingCycle(),
                order.getBillingTerm()
            )
            .stream()
            .findFirst()
            .orElseThrow(() -> new ServiceFailureException("Failed to create a stub billing period for order: " + order.getOrderId()));

        return generateInvoicePreviewByBillingPeriod(order, timeZone, stubPeriod);
    }

    private InvoicePreview generateInvoicePreviewByBillingPeriod(Order order, TimeZone timeZone, BillingPeriod billingPeriod) {
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);
        Map<String, Charge> chargeMap = getChargeMap(List.of(order));
        InvoiceBillingInfo invoiceBillingInfo = ImmutableInvoiceBillingInfo.builder()
            .invoiceTargetDate(billingPeriod.getEnd())
            .subscriptionStart(billingPeriod.getStart())
            .subscriptionEnd(billingPeriod.getEnd())
            .billingAnchorDate(billingPeriod.getStart())
            .currency(order.getCurrency())
            .timeZone(timeZone)
            .prorationConfig(prorationConfig)
            .billingCycle(order.getBillingCycle())
            .billingTerm(order.getBillingTerm())
            .build();
        List<InvoiceItem> mergedInvoiceItems = previewInvoiceItemsForOrder(
            order,
            DO_NOT_SKIP_PERCENT_OF,
            INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION,
            timeZone,
            chargeMap,
            invoiceBillingInfo
        );
        return InvoicePreviewBuilder.getInvoicePreview(order.getOrderId(), mergedInvoiceItems, billingPeriod, chargeMap);
    }

    public InvoicePreview generateInvoicePreview(
        Order order,
        boolean skipAmendPercentOf,
        boolean forOrderLineCalculation,
        ProrationConfig prorationConfig,
        TimeZone timeZone
    ) {
        Map<String, Charge> chargeMap = getChargeMap(List.of(order));
        List<InvoiceItem> mergedInvoiceItems = previewInvoiceItemsForOrder(
            order,
            skipAmendPercentOf,
            forOrderLineCalculation,
            prorationConfig,
            timeZone,
            chargeMap
        );
        BillingPeriod orderPeriod = new BillingPeriod(order.getStartDate(), order.getEndDate(), order.getEndDate(), order.getBillingCycle());
        return InvoicePreviewBuilder.getInvoicePreview(order.getOrderId(), mergedInvoiceItems, orderPeriod, chargeMap);
    }

    public void triggerOrderExecutionInvoiceDependencies(String orderId) {
        Order order = orderGetService.getOrderByOrderId(orderId);
        if (StringUtils.isBlank(order.getExternalSubscriptionId())) {
            throw new ServiceFailureException(String.format("subscription id missing in order while memoizing invoice data for order: %s", orderId));
        }

        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Map<String, Charge> chargeMap = getChargeMap(List.of(order));
        List<InvoiceItem> previewInvoiceItems = previewInvoiceItemsForOrder(
            order,
            DO_NOT_SKIP_PERCENT_OF,
            !INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION,
            prorationConfig,
            timeZone,
            chargeMap
        );

        //TODO : while backfilling check if generated memoization lines matches already generated invoice lines for the order.
        memoizeInvoiceDataForOrder(order, previewInvoiceItems, chargeMap);
        createCreditBucketsForOrder(order, previewInvoiceItems, chargeMap);
        logMemoizationDifferences(order, previewInvoiceItems, chargeMap);
    }

    private void memoizeInvoiceDataForOrder(Order order, List<InvoiceItem> memoizationCandidates, Map<String, Charge> chargeMap) {
        LOGGER.info("memoizing invoice items for order {}", order.getOrderId());
        Collection<MemoizedInvoiceLineItem> toMemoize = memoizationCandidates
            .stream()
            // usage and event based invoice items are not memoized as of now
            .filter(invoiceItem -> InvoiceProcessorUtility.filterChargeTypesForMemoization(invoiceItem.getChargeId(), chargeMap))
            .map(item -> {
                Charge charge = chargeMap.get(item.getChargeId());
                if (charge == null) {
                    throw new InvariantCheckFailedException(
                        String.format("Charge %s for corresponding invoice line item not found in charge map", item.getChargeId())
                    );
                }
                if (charge.getType() == ChargeType.PERCENTAGE_OF) {
                    LOGGER.info(
                        "Memoizing percent of charge for order line {} and charge id {}, with period {} - {}, and amounts listUnit : {}, sellUnit : {}",
                        item.getOrderLineItemId(),
                        item.getChargeId(),
                        item.getPeriodStartDate(),
                        item.getPeriodEndDate(),
                        item.getListUnitPrice(),
                        item.getSellUnitPrice()
                    );
                }
                return MemoizedInvoiceLineItem.from(order.getExternalSubscriptionId(), item, charge.getBillingTerm());
            })
            .toList();

        invoiceDAO.memoizeInvoiceItems(order.getEntityId(), order.getExternalSubscriptionId(), order.getOrderId(), toMemoize);
        LOGGER.info("successfully memoized {} items", toMemoize.size());
    }

    private void createCreditBucketsForOrder(Order order, List<InvoiceItem> invoiceItems, Map<String, Charge> chargeMap) {
        List<Charge> drawdownCharges = chargeMap.values().stream().filter(Charge::isDrawdown).distinct().toList();
        if (CollectionUtils.isEmpty(drawdownCharges) || !featureService.isEnabled(Feature.CREDIT_BUCKETS)) {
            return;
        }
        LOGGER.info("creating credit buckets for order {}", order.getOrderId());

        List<InvoiceItem> prepaidInvoiceItems = invoiceItems
            .stream()
            .filter(item -> chargeMap.get(item.getChargeId()).getType() == ChargeType.PREPAID)
            .toList();
        List<CreditBucket> creditBuckets = new ArrayList<>(prepaidInvoiceItems.size());
        for (InvoiceItem invoiceItem : prepaidInvoiceItems) {
            Charge prepaidCharge = chargeMap.get(invoiceItem.getChargeId());
            Optional<String> drawdownChargeId = drawdownCharges
                .stream()
                .filter(charge -> charge.getPlanUuid().equals(prepaidCharge.getPlanUuid()))
                .map(Charge::getChargeId)
                .findFirst();
            if (drawdownChargeId.isEmpty()) {
                // prepaid charge is not associated with a drawdown charge
                continue;
            }
            String creditBucketId = CreditBucket.createCreditBucketId(order.getExternalSubscriptionId(), drawdownChargeId.get());
            CreditBucket creditBucket = new CreditBucket(
                null,
                creditBucketId,
                order.getExternalSubscriptionId(),
                invoiceItem.getSubscriptionChargeGroupId(),
                invoiceItem.getQuantity(),
                invoiceItem.getQuantity(),
                invoiceItem.getPeriodStartDate(),
                invoiceItem.getPeriodEndDate()
            );
            creditBuckets.add(creditBucket);
        }
        invoiceDAO.createCreditBuckets(creditBuckets);
        LOGGER.info("successfully created {} credit buckets for order {}", creditBuckets.size(), order.getOrderId());
    }

    private void logMemoizationDifferences(Order order, List<InvoiceItem> previewInvoiceItems, Map<String, Charge> chargeMap) {
        List<OrderLineItem> orderLineItems = orderGetService.getOrderLineItemsByOrderLineItemIds(
            order.getLineItemsNetEffect().stream().map(OrderLineItem::getOrderLineId).toList()
        );
        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems = invoiceDAO.getMemoizedInvoiceItemsForOrder(order.getOrderId());
        InvoiceProcessorUtility.logOrderAmountDifferencesWithMemoization(
            order,
            orderLineItems,
            memoizedInvoiceLineItems,
            previewInvoiceItems,
            chargeMap,
            featureService
        );
    }

    public MemoizedInvoiceLineItem memoizeInvoiceLineItem(String orderId, String orderLineItemId, MemoizedInvoiceLineItem memoizedInvoiceLineItem) {
        return invoiceDAO.memoizeInvoiceItem(orderId, orderLineItemId, memoizedInvoiceLineItem);
    }

    public Optional<MemoizedInvoiceLineItem> deleteMemoizedInvoiceItem(UUID id) {
        return invoiceDAO.deleteMemoizedInvoiceItem(id);
    }

    private List<InvoiceItem> previewInvoiceItemsForOrder(
        Order order,
        boolean skipAmendPercentOf,
        boolean forOrderLineCalculation,
        ProrationConfig prorationConfig,
        TimeZone timeZone,
        Map<String, Charge> chargeMap
    ) {
        Instant subscriptionStart;
        Instant billingAnchorDate;
        if (StringUtils.isNotBlank(order.getExternalSubscriptionId())) {
            Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
            subscriptionStart = subscription.getStartDate();
            billingAnchorDate = subscription.getBillingAnchorDate();
        } else {
            subscriptionStart = order.getStartDate();
            billingAnchorDate = order.getBillingAnchorDate();
        }

        InvoiceBillingInfo invoiceBillingInfo = ImmutableInvoiceBillingInfo.builder()
            .invoiceTargetDate(order.getEndDate())
            .subscriptionStart(subscriptionStart)
            .subscriptionEnd(order.getEndDate())
            .billingAnchorDate(billingAnchorDate)
            .currency(order.getCurrency())
            .timeZone(timeZone)
            .prorationConfig(prorationConfig)
            .billingCycle(order.getBillingCycle())
            .billingTerm(order.getBillingTerm())
            .build();
        return previewInvoiceItemsForOrder(order, skipAmendPercentOf, forOrderLineCalculation, timeZone, chargeMap, invoiceBillingInfo);
    }

    private List<InvoiceItem> previewInvoiceItemsForOrder(
        Order order,
        boolean skipAmendPercentOf,
        boolean forOrderLineCalculation,
        TimeZone timeZone,
        Map<String, Charge> chargeMap,
        InvoiceBillingInfo invoiceBillingInfo
    ) {
        validateInvoiceItemsGenerationEligibility(order);

        List<BillingPeriod> orderBillingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            invoiceBillingInfo.getSubscriptionStart(),
            invoiceBillingInfo.getSubscriptionEnd(),
            invoiceBillingInfo.getBillingAnchorDate(),
            invoiceBillingInfo.getInvoiceTargetDate(),
            invoiceBillingInfo.getTimeZone().toZoneId(),
            invoiceBillingInfo.getBillingCycle(),
            invoiceBillingInfo.getBillingTerm()
        );

        Map<String, InvoicePreviewInput.CustomBillingInvoicingInput> customBillingInvoicingInput = null;
        if (
            featureService.isEnabled(Feature.CUSTOM_BILLING) &&
            order.getBillingCycle().getCycle() == Cycle.CUSTOM &&
            orderCustomBillingService.shouldUseCustomBillingInvoicingInput(order)
        ) {
            customBillingInvoicingInput = Map.of(
                order.getOrderId(),
                getCustomBillingInvoicingInputForOrder(order, forOrderLineCalculation, timeZone, chargeMap, invoiceBillingInfo)
            );
        }

        // TODO : If we want to support order lines not eligible for custom billing mixed with order lines eligible for an order,
        //  we need to pass separate billing periods for order lines outside custom billing to the processors
        ImmutableInvoicePreviewInput previewInput = ImmutableInvoicePreviewInput.builder()
            .invoiceBillingInfo(invoiceBillingInfo)
            .putAllChargeMap(chargeMap)
            .addAllOrderLineItems(order.getLineItems())
            .addAllOrderLineItemsNetEffect(order.getLineItemsNetEffect())
            .addAllTopLevelBillingPeriods(orderBillingPeriods)
            .customBillingInvoicingInput(customBillingInvoicingInput)
            .build();

        List<InvoiceItem> recurringInvoiceItems = recurringInvoiceProcessor.previewInvoiceItems(previewInput);

        List<InvoiceItem> oneTimeInvoiceItems = oneTimeInvoiceProcessor.previewInvoiceItems(previewInput);

        List<InvoiceItem> prepaidInvoiceItems = prepaidInvoiceProcessor.previewInvoiceItems(previewInput);

        List<InvoiceItem> usageItems = usageInvoiceProcessor.previewInvoiceItems(previewInput);

        List<InvoiceItem> percentOfInvoiceItems = previewPercentOfInvoiceItems(order, skipAmendPercentOf, previewInput);

        List<InvoiceItem> adhocCustomBillingInvoiceItems = getAdhocCustomBillingInvoiceItems(
            order,
            customBillingInvoicingInput,
            timeZone,
            forOrderLineCalculation,
            Stream.of(recurringInvoiceItems, oneTimeInvoiceItems, usageItems, prepaidInvoiceItems, percentOfInvoiceItems)
                .flatMap(List::stream)
                .collect(Collectors.toList())
        );

        return mergeInvoiceItemsAndGenerateLineNumbers(
            recurringInvoiceItems,
            oneTimeInvoiceItems,
            usageItems,
            prepaidInvoiceItems,
            percentOfInvoiceItems,
            adhocCustomBillingInvoiceItems
        );
    }

    private InvoicePreviewInput.CustomBillingInvoicingInput getCustomBillingInvoicingInputForOrder(
        Order order,
        boolean forOrderLineCalculation,
        TimeZone timeZone,
        Map<String, Charge> chargeMap,
        InvoiceBillingInfo invoiceBillingInfo
    ) {
        InvoicePreviewInput.CustomBillingInvoicingInput customBillingInvoicingInput;
        CustomBillingSchedule customBillingSchedule = forOrderLineCalculation
            ? order.getCustomBillingSchedule()
            : orderGetService.getCustomBillingScheduleForOrder(order.getOrderId(), true);
        CustomBillingSchedule.CustomBillingType customBillingType = customBillingSchedule.getCustomBillingType();

        List<BillingPeriod> customBillingPeriods;
        if (!forOrderLineCalculation && customBillingType == CustomBillingSchedule.CustomBillingType.ADHOC) {
            // For adhoc custom billing invoice preview, we don't rely on billing periods or processors to generate invoice items.
            customBillingPeriods = List.of();
        } else {
            customBillingPeriods = subscriptionBillingPeriodService
                .getBillingPeriodsForCustomBilling(
                    timeZone,
                    order,
                    customBillingSchedule,
                    Optional.empty(),
                    invoiceBillingInfo,
                    forOrderLineCalculation
                )
                .stream()
                .flatMap(List::stream)
                .toList();
        }
        List<OrderLineItem> customBillingEligibleOrderLines = orderCustomBillingService.getOrderLineItemsEligibleForCustomBilling(order, chargeMap);
        customBillingInvoicingInput = new InvoicePreviewInput.CustomBillingInvoicingInput(
            customBillingSchedule,
            customBillingPeriods,
            customBillingEligibleOrderLines,
            forOrderLineCalculation
        );
        return customBillingInvoicingInput;
    }

    private Map<String, InvoicePreviewInput.CustomBillingInvoicingInput> getCustomBillingInvoicingInputForSubscription(
        TimeZone timeZone,
        List<Order> orders,
        Map<String, Charge> chargeMap,
        InvoiceBillingInfo invoiceBillingInfo
    ) {
        Map<String, InvoicePreviewInput.CustomBillingInvoicingInput> customBillingInvoicingInput;
        Map<String, InvoicePreviewInput.CustomBillingInvoicingInput> customBillingInvoicingInputForSubscriptionOrders = new LinkedHashMap<>();
        for (Order order : orders) {
            // For orders like cancellation, we should not use custom billing invoicing input
            if (BooleanUtils.isFalse(orderCustomBillingService.shouldUseCustomBillingInvoicingInput(order))) {
                continue;
            }
            var customBillingInvoicingInputForOrder = getCustomBillingInvoicingInputForOrder(
                order,
                !INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION,
                timeZone,
                chargeMap,
                invoiceBillingInfo
            );
            customBillingInvoicingInputForSubscriptionOrders.put(order.getOrderId(), customBillingInvoicingInputForOrder);
        }
        customBillingInvoicingInput = MapUtils.isNotEmpty(customBillingInvoicingInputForSubscriptionOrders)
            ? customBillingInvoicingInputForSubscriptionOrders
            : null;
        return customBillingInvoicingInput;
    }

    // isolate percent of processing to a single function
    // todo: remove this once percent of invoice processor is complete
    private List<InvoiceItem> previewPercentOfInvoiceItems(Order order, boolean skipAmendPercentOf, ImmutableInvoicePreviewInput previewInput) {
        // NOTE : For adhoc custom billing this list would be empty, as the invoice items are generated outside of processors
        List<InvoiceItem> allRecurringInvoiceItems = recurringInvoiceProcessor.previewInvoiceItems(
            // NOTE: we need to get all recurring lines for percent of charge because we need to assess all charges involved
            // NOTE: the lineItems and lineItemsNetEffect are the same for Order Creation/Order Cancellation/Order Renewal
            // NOTE: the only place where they differ is for amendment and for amendment of percent of charge we need the full context
            // NOTE: and since we de-book and re-book all lines involved during amendment here we pass line items and not net effect line items
            previewInput.withOrderLineItemsNetEffect(order.getLineItems())
        );
        List<InvoiceItem> percentOfInvoiceItems = percentOfChargeHelper.previewPercentOfInvoiceItems(
            order,
            previewInput,
            allRecurringInvoiceItems,
            skipAmendPercentOf
        );

        var percentOfOrderLines = getOrderLinesByType(order.getLineItemsNetEffect(), previewInput.getChargeMap(), ChargeType.PERCENTAGE_OF);

        // todo: this is a temporary solution to preview percent of charge by billing period. Should revisit the preview path
        // Percent of charge preview lines do not consider billing period.
        // Instead, it takes the total target amounts that fall within the percent of charge order line start to end interval and derives the total percent of amount
        // This causes 2 issues:
        // 1) Due to the later splitting by billing period during invoice generation, there can be rounding artifacts
        // 2) Order form invoice preview puts the entirety of the percent of amount in the first billing period
        // This block takes the generated percent of amount and splits it by billing period invoking the same code as invoice generation
        percentOfInvoiceItems = splitPercentOfAmountsByBillingPeriod(percentOfOrderLines, percentOfInvoiceItems, previewInput);
        return percentOfInvoiceItems;
    }

    private List<InvoiceItem> splitPercentOfAmountsByBillingPeriod(
        List<OrderLineItem> percentOfOrderLineItems,
        List<InvoiceItem> percentOfInvoiceItems,
        InvoicePreviewInput previewInput
    ) {
        InvoiceBillingInfo invoiceBillingInfo = previewInput.getInvoiceBillingInfo();
        List<BillingPeriod> billingPeriods = previewInput.getTopLevelBillingPeriods();
        Map<String, Charge> chargeMap = previewInput.getChargeMap();

        Map<String, OrderLineItem> orderLineItemMap = percentOfOrderLineItems
            .stream()
            .collect(Collectors.toMap(OrderLineItem::getOrderLineId, Function.identity()));

        List<InvoiceItem> invoiceItemsByBillingPeriod = new ArrayList<>();
        for (InvoiceItem invoiceItem : percentOfInvoiceItems) {
            OrderLineItem orderLineItem = orderLineItemMap.get(invoiceItem.getOrderLineItemId());

            // For recurrence custom billing, we would use the billing periods from the custom billing invoicing input,
            // For adhoc custom billing it will not be present as we generate invoice items outside of processors
            Optional<List<BillingPeriod>> customBillingPeriods = percentOfInvoiceProcessor.getCustomBillingPeriodsIfApplicable(
                previewInput,
                orderLineItem
            );
            List<BillingPeriod> topLevelBillingPeriods = customBillingPeriods.orElse(billingPeriods);

            orderLineItem.setAmount(invoiceItem.getAmount());
            orderLineItem.setListAmount(invoiceItem.getListAmount());
            orderLineItem.setListAmountBeforeOverride(invoiceItem.getListAmountBeforeOverride());
            orderLineItem.setListUnitPrice(invoiceItem.getListUnitPrice());
            orderLineItem.setListUnitPriceBeforeOverride(invoiceItem.getListUnitPriceBeforeOverride());
            orderLineItem.setSellUnitPrice(invoiceItem.getSellUnitPrice());

            Charge charge = chargeMap.get(orderLineItem.getChargeId());

            List<BillingPeriod> finalBillingPeriods = subscriptionBillingPeriodService.getOrderLineBillingPeriods(
                orderLineItem,
                chargeMap,
                invoiceBillingInfo,
                topLevelBillingPeriods
            );

            for (BillingPeriod billingPeriod : finalBillingPeriods) {
                percentOfChargeHelper
                    .getPercentOfInvoiceItemForBillingPeriod(
                        invoiceBillingInfo,
                        billingPeriod,
                        orderLineItem,
                        charge,
                        false,
                        invoiceItem.getInlineDiscounts(),
                        invoiceItem.getPredefinedDiscounts()
                    )
                    // add only if an invoice item is found
                    .map(invoiceItemsByBillingPeriod::add);
            }
        }

        return invoiceItemsByBillingPeriod;
    }

    private List<InvoiceItem> getAdhocCustomBillingInvoiceItems(
        Order order,
        Map<String, InvoicePreviewInput.CustomBillingInvoicingInput> customBillingInvoicingInputForOrder,
        TimeZone timeZone,
        boolean forOrderLineCalculation,
        List<InvoiceItem> nonCustomBilledInvoiceItems
    ) {
        if (customBillingInvoicingInputForOrder == null) {
            return List.of();
        }

        InvoicePreviewInput.CustomBillingInvoicingInput customBillingInvoicingInput = customBillingInvoicingInputForOrder.get(order.getOrderId());
        if (customBillingInvoicingInput == null) {
            throw new ServiceFailureException(
                "getAdhocCustomBillingInvoiceItems : Custom billing invoicing input not found for order id : " + order.getOrderId()
            );
        }

        if (
            customBillingInvoicingInput.customBillingSchedule().getCustomBillingType() == CustomBillingSchedule.CustomBillingType.ADHOC &&
            !forOrderLineCalculation // Only for actual invoice preview, for order lines calculation we treat the billing cycle as paid in full for amount calculation and defer to invoice processors
        ) {
            Set<String> customBillingEligibleOrderLines = customBillingInvoicingInput
                .customBillingEligibleOrderLineItems()
                .stream()
                .map(OrderLineItem::getOrderLineId)
                .collect(Collectors.toSet());
            nonCustomBilledInvoiceItems.forEach(invoiceItem -> {
                if (customBillingEligibleOrderLines.contains(invoiceItem.getOrderLineItemId())) {
                    throw new InvariantCheckFailedException(
                        String.format(
                            "Found invoice item with period %s - %s, for order line eligible for adhoc custom billing %s, this should not have been processed by invoice processors!!",
                            invoiceItem.getPeriodStartDate(),
                            invoiceItem.getPeriodEndDate(),
                            invoiceItem.getOrderLineItemId()
                        )
                    );
                }
            });

            LOGGER.info(
                "Generating invoice items for adhoc custom billing order {}, forOrderLineCalculation : {}",
                order.getOrderId(),
                forOrderLineCalculation
            );
            // For adhoc billing we generate invoice items directly which is a separate algorithm
            return subscriptionBillingPeriodService
                .generateInvoiceItemsForAdhocCustomBilling(customBillingInvoicingInput.customBillingSchedule(), order, timeZone, false)
                .stream()
                .flatMap(List::stream)
                .toList();
        }
        return List.of();
    }

    private TaxResult applyTaxPreview(Map<String, Charge> chargeMap, List<InvoiceItem> invoiceItems, Order order) {
        Map<UUID, TaxRate> taxRateMap = getTaxRateMap(invoiceItems, chargeMap);

        Optional<TaxTransaction> taxTxnOptional = previewTaxesForItems(invoiceItems, taxRateMap, chargeMap, order);

        // nothing to do set tax to 0 and return
        if (taxTxnOptional.isEmpty()) {
            List<InvoiceItem> zeroTaxItems = invoiceItems.stream().map(InvoiceServiceInternal::taxZeroedOutItem).collect(Collectors.toList());
            return TaxResult.builder().taxTransaction(taxTxnOptional).taxedInvoiceItems(zeroTaxItems).build();
        }

        TaxTransaction taxTransaction = taxTxnOptional.get();
        List<InvoiceItem> taxedInvoiceItems = new ArrayList<>();
        for (InvoiceItem preTaxItem : invoiceItems) {
            taxedInvoiceItems.add(applyTaxForInvoiceLineItem(chargeMap, taxRateMap, taxTransaction, preTaxItem));
        }
        return TaxResult.builder().taxTransaction(taxTxnOptional).taxedInvoiceItems(taxedInvoiceItems).build();
    }

    private Optional<TaxTransaction> previewTaxesForItems(
        List<InvoiceItem> preTaxInvoiceItems,
        Map<UUID, TaxRate> taxRateMap,
        Map<String, Charge> chargeMap,
        Order order
    ) {
        if (
            !featureService.isEnabled(Feature.TAX_PERCENT) ||
            taxService.tenantHasTaxServiceIntegration() ||
            CollectionUtils.isEmpty(preTaxInvoiceItems)
        ) {
            return Optional.empty();
        }

        EntityCache<String, Account> accountEntityCache = EntityCache.of(accountGetService::getAccount);
        EntityCache<String, AccountContact> contactEntityCache = EntityCache.of(accountGetService::getContact);
        Optional<Account> account = Optional.ofNullable(order.getAccountId()).map(accountEntityCache::get);
        if (account.isEmpty()) {
            return Optional.empty();
        }
        Optional<AccountAddress> shippingAddress = Optional.ofNullable(order.getShippingContactId())
            .map(contactEntityCache::get)
            .map(AccountContact::getAddress);
        Optional<TaxExemptionUseCode> taxExemptionUseCode = Optional.ofNullable(order.getBillingContactId())
            .map(contactEntityCache::get)
            .map(AccountContact::getAccountId)
            .map(accountEntityCache::get)
            .map(Account::getTaxExemptionUseCode);

        List<TaxLineItem> taxLines = generateTaxLineItems(preTaxInvoiceItems, chargeMap, taxRateMap);

        TaxPreviewInput taxPreviewInput = ImmutableTaxPreviewInput.builder()
            .taxTransactionCode(Optional.empty())
            .account(account.get())
            .accountAddress(shippingAddress)
            .taxLineItems(taxLines)
            .taxExceptionUseCode(taxExemptionUseCode)
            .currency(order.getCurrency())
            .build();
        return taxService.previewTaxTransaction(taxPreviewInput);
    }

    public List<InvoicePreview> previewInvoicesByBillingPeriods(String orderId) {
        var order = orderGetService.getOrderByOrderId(orderId);
        return previewInvoicesByBillingPeriods(order);
    }

    // todo: invoice previews need to be consolidated.
    private List<InvoicePreview> previewInvoicesByBillingPeriods(Order order) {
        InvoicePreview invoicePreviewByOrderPeriod = previewInvoiceByOrderPeriod(order);
        Map<String, Charge> chargeMap = getChargeMap(List.of(order));
        var allInvoiceItems = applyTaxPreview(chargeMap, invoicePreviewByOrderPeriod.getInvoiceItems(), order).getTaxedInvoiceItems();

        List<BillingPeriod> billingPeriods = getBillingPeriods(order);

        List<InvoicePreview> invoicePreviews = new ArrayList<>();

        for (BillingPeriod billingPeriod : billingPeriods) {
            Instant periodEnd = billingPeriod.getEnd().isAfter(order.getEndDate()) ? order.getEndDate() : billingPeriod.getEnd();
            BillingPeriod adjustedBillingPeriod = new BillingPeriod(
                billingPeriod.getStart(),
                periodEnd,
                billingPeriod.getFullPeriodEnd(),
                billingPeriod.getRecurrence()
            );

            // find line items that falls within the billing period
            List<InvoiceItem> itemsInPeriod = allInvoiceItems
                .stream()
                .filter(
                    item ->
                        item.getPeriodStartDate().compareTo(adjustedBillingPeriod.getStart()) >= 0 &&
                        item.getPeriodStartDate().compareTo(adjustedBillingPeriod.getEnd()) < 0
                )
                .toList();

            invoicePreviews.add(InvoicePreviewBuilder.getInvoicePreview(order.getOrderId(), itemsInPeriod, adjustedBillingPeriod, chargeMap));
        }

        return invoicePreviews;
    }

    public List<BillingPeriod> getBillingPeriods(Order order) {
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

        Instant subscriptionStart;
        Instant billingAnchorDate;

        if (StringUtils.isNotEmpty(order.getExternalSubscriptionId())) {
            Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
            subscriptionStart = subscription.getStartDate();
            billingAnchorDate = subscription.getBillingAnchorDate();
        } else {
            subscriptionStart = order.getStartDate();
            billingAnchorDate = order.getBillingAnchorDate();
        }

        return subscriptionBillingPeriodService.getBillingPeriods(
            subscriptionStart,
            order.getEndDate(),
            billingAnchorDate,
            order.getEndDate(),
            timeZone.toZoneId(),
            order.getBillingCycle(),
            order.getBillingTerm()
        );
    }

    private void validateInvoiceItemsGenerationEligibility(Order order) {
        if (ObjectUtils.anyNull(order.getStartDate(), order.getEndDate(), order.getBillingAnchorDate(), order.getBillingCycle())) {
            throw new IllegalArgumentException("Order startDate, endDate, billingAnchorDate and billingCycle are required");
        }
    }

    public Optional<Invoice> generateInvoice(
        String subscriptionId,
        Instant invoiceTargetDate,
        Optional<Instant> invoiceDate,
        InvoiceChargeInclusionOption invoiceChargeInclusionOption,
        Optional<InvoiceGenerationMethod> generationMethod,
        Optional<String> generatedBy
    ) {
        if (StringUtils.isBlank(subscriptionId) || invoiceTargetDate == null) {
            throw new IllegalArgumentException("subscriptionId and invoiceTargetDate are required");
        }

        // fail fast check, quickly fail invoice generation if DRAFT invoice exists
        invoiceDAO.checkForDraftInvoice(subscriptionId);

        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        String accountId = subscription.getAccountId();
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        AccountContact shippingContact = accountGetService.getContact(subscription.getShippingContactId());
        AccountContact billingContact = accountGetService.getContact(subscription.getBillingContactId());
        Account account = accountGetService.getAccount(billingContact.getAccountId());
        var orders = orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);

        Map<String, Charge> chargeMap = getChargeMap(orders);

        if (invoiceTargetDate.isBefore(subscription.getStartDate())) {
            // if target date is before subscription start date, skip and return empty
            return Optional.empty();
        }

        Instant subscriptionCanceledDate = subscription.getCanceledDate();

        if (subscriptionCanceledDate != null && subscriptionCanceledDate.compareTo(invoiceTargetDate) <= 0) {
            // if subscription is cancelled or pending cancellation, and target date is on or after cancellation date, generate all pending invoices
            invoiceTargetDate = subscription.getEndDate();
        }

        // if the target date is past subscription end date, generate for whole subscription
        if (invoiceTargetDate.isAfter(subscription.getEndDate())) {
            invoiceTargetDate = subscription.getEndDate();
        }

        Invoice.Number draftInvoiceNumber = getInvoiceNumberForDraftInvoice(subscription.getEntityId());

        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems = invoiceDAO.getMemoizedInvoiceItemsForSubscription(subscription.getSubscriptionId());
        List<InvoiceItem> invoiceItems = getInvoiceItems(
            invoiceTargetDate,
            subscription,
            timeZone,
            orders,
            chargeMap,
            invoiceChargeInclusionOption,
            memoizedInvoiceLineItems,
            false
        );

        TaxResult taxResult = applyTaxAndGetResult(
            account,
            shippingContact,
            billingContact,
            chargeMap,
            invoiceItems,
            draftInvoiceNumber,
            subscription.getCurrency(),
            invoiceDate
        );

        if (taxResult.getTaxedInvoiceItems().isEmpty()) {
            LOGGER.debug("All items are already invoiced. account id: {}", accountId);
            return Optional.empty();
        }

        Optional<String> invoicePurchaseOrderNumber = getPurchaseOrderForInvoiceFromSubscription(subscription);

        EmailNotifiersList emailNotifiersList = new EmailNotifiersList();

        // Update Invoice Email List - T0, Cc, and Bcc from Subscription
        emailContactListService.populateInvoiceEmailListFromSubscription(subscriptionId, emailNotifiersList);

        Optional<TransactionalExchangeRate> exchangeRateOptional = getTransactionalExchangeRate(invoiceDate, subscription);

        // invoice items with tax amounts and fx amounts (if feature enabled)
        List<InvoiceItem> decoratedItems = populateFunctionalAmountsOnInvoiceItems(
            taxResult.getTaxedInvoiceItems(),
            subscription,
            exchangeRateOptional
        );

        Invoice invoice = createInvoiceObject(
            draftInvoiceNumber,
            subscription.getEntityId(),
            invoiceDate,
            accountId,
            subscription.getResellerAccountId(),
            subscriptionId,
            subscription.getCurrency(),
            decoratedItems,
            shippingContact,
            billingContact,
            subscription.getPaymentTerm(),
            taxResult.getTaxTransaction(),
            invoicePurchaseOrderNumber,
            subscription.getPurchaseOrderRequiredForInvoicing(),
            emailNotifiersList,
            exchangeRateOptional,
            generationMethod,
            generatedBy
        );

        Invoice stored = invoiceDAO.addInvoice(invoice);

        Invoice hydratedInvoice = InvoiceLineItemFieldsHydrator.hydrateInvoiceItemsWithPlanId(productCatalogGetService, stored);
        hydratedInvoice = InvoiceLineItemFieldsHydrator.hydrateInvoiceItemsWithTriggerOn(invoice, hydratedInvoice);
        generateInvoiceDocument(hydratedInvoice.getInvoiceNumber());

        return Optional.of(hydratedInvoice);
    }

    public Optional<Invoice> generateInvoiceForTaxVerification(
        String subscriptionId,
        Optional<Instant> invoiceDate,
        List<InvoiceItem> invoiceItems,
        Optional<InvoiceGenerationMethod> generationMethod,
        Optional<String> generatedBy,
        AccountContact shippingContact,
        AccountContact billingContact
    ) {
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        String accountId = subscription.getAccountId();
        Account account = accountGetService.getAccount(billingContact.getAccountId());

        var orders = orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);
        Map<String, Charge> chargeMap = getChargeMap(orders);

        Invoice.Number draftInvoiceNumber = getInvoiceNumberForDraftInvoice(subscription.getEntityId());

        TaxResult taxResult = applyTaxAndGetResult(
            account,
            shippingContact,
            billingContact,
            chargeMap,
            invoiceItems,
            draftInvoiceNumber,
            subscription.getCurrency(),
            invoiceDate
        );

        if (taxResult.getTaxedInvoiceItems().isEmpty()) {
            LOGGER.debug("All items are already invoiced. account id: {}", accountId);
            return Optional.empty();
        }

        Optional<String> invoicePurchaseOrderNumber = getPurchaseOrderForInvoiceFromSubscription(subscription);

        EmailNotifiersList emailNotifiersList = new EmailNotifiersList();

        // Update Invoice Email List - T0, Cc, and Bcc from Subscription
        emailContactListService.populateInvoiceEmailListFromSubscription(subscriptionId, emailNotifiersList);

        Optional<TransactionalExchangeRate> exchangeRateOptional = getTransactionalExchangeRate(invoiceDate, subscription);

        // invoice items with tax amounts and fx amounts (if feature enabled)
        List<InvoiceItem> decoratedItems = populateFunctionalAmountsOnInvoiceItems(
            taxResult.getTaxedInvoiceItems(),
            subscription,
            exchangeRateOptional
        );

        Invoice invoice = createInvoiceObject(
            draftInvoiceNumber,
            subscription.getEntityId(),
            invoiceDate,
            accountId,
            subscription.getResellerAccountId(),
            subscriptionId,
            subscription.getCurrency(),
            decoratedItems,
            shippingContact,
            billingContact,
            subscription.getPaymentTerm(),
            taxResult.getTaxTransaction(),
            invoicePurchaseOrderNumber,
            subscription.getPurchaseOrderRequiredForInvoicing(),
            emailNotifiersList,
            exchangeRateOptional,
            generationMethod,
            generatedBy
        );

        return Optional.of(invoice);
    }

    // todo: keeping as separate method for now, will be merged with generateInvoice after fully vetted
    public Optional<Invoice> generateInvoiceForOrder(
        String orderId,
        List<String> orderLineItemIds,
        Instant invoiceTargetDate,
        Optional<Instant> invoiceDate,
        InvoiceChargeInclusionOption invoiceChargeInclusionOption,
        Optional<InvoiceGenerationMethod> generationMethod,
        Optional<String> generatedBy
    ) {
        if (!featureService.isEnabled(Feature.INVOICE_PER_ORDER)) {
            throw new UnsupportedOperationException("Feature is not enabled");
        }

        if (StringUtils.isBlank(orderId) || invoiceTargetDate == null) {
            throw new InvalidInputException("orderId and invoiceTargetDate are required");
        }

        Order order = orderGetService.getOrderByOrderId(orderId);
        if (order.getStatus() != OrderStatus.EXECUTED || StringUtils.isBlank(order.getExternalSubscriptionId())) {
            throw new InvalidInputException("Order must be executed to generate invoice");
        }

        String subscriptionId = order.getExternalSubscriptionId();

        // fail fast check, quickly fail invoice generation if DRAFT invoice exists
        invoiceDAO.checkForDraftInvoice(subscriptionId);

        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        String accountId = subscription.getAccountId();
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        AccountContact shippingContact = accountGetService.getContact(subscription.getShippingContactId());
        AccountContact billingContact = accountGetService.getContact(subscription.getBillingContactId());
        Account account = accountGetService.getAccount(billingContact.getAccountId());

        var orders = List.of(order);

        Map<String, Charge> chargeMap = getChargeMap(orders);

        if (invoiceTargetDate.isBefore(subscription.getStartDate())) {
            // if target date is before subscription start date, skip and return empty
            return Optional.empty();
        }

        // if the target date is past subscription end date, generate for whole subscription
        if (invoiceTargetDate.isAfter(subscription.getEndDate())) {
            invoiceTargetDate = subscription.getEndDate();
        }

        Invoice.Number draftInvoiceNumber = getInvoiceNumberForDraftInvoice(subscription.getEntityId());

        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems = invoiceDAO.getMemoizedInvoiceItemsForSubscription(subscription.getSubscriptionId());
        List<InvoiceItem> invoiceItems = getInvoiceItems(
            invoiceTargetDate,
            subscription,
            timeZone,
            orders,
            chargeMap,
            invoiceChargeInclusionOption,
            memoizedInvoiceLineItems,
            false
        );

        // todo: filtering after generating invoice items to minimize impact on existing invoice code for now
        List<InvoiceItem> filteredInvoiceItems = filterInvoiceItemsByOrderLineItemIds(invoiceItems, orderLineItemIds);

        TaxResult taxResult = applyTaxAndGetResult(
            account,
            shippingContact,
            billingContact,
            chargeMap,
            filteredInvoiceItems,
            draftInvoiceNumber,
            subscription.getCurrency(),
            invoiceDate
        );

        if (taxResult.getTaxedInvoiceItems().isEmpty()) {
            LOGGER.debug("All items are already invoiced. account id: {}", accountId);
            return Optional.empty();
        }

        Optional<String> invoicePurchaseOrderNumber = getPurchaseOrderForInvoiceFromSubscription(subscription);

        EmailNotifiersList emailNotifiersList = new EmailNotifiersList();

        // Update Invoice Email List - T0, Cc, and Bcc from Subscription
        emailContactListService.populateInvoiceEmailListFromSubscription(subscriptionId, emailNotifiersList);

        Optional<TransactionalExchangeRate> exchangeRateOptional = getTransactionalExchangeRate(invoiceDate, subscription);

        // invoice items with tax amounts and fx amounts (if feature enabled)
        List<InvoiceItem> decoratedItems = populateFunctionalAmountsOnInvoiceItems(
            taxResult.getTaxedInvoiceItems(),
            subscription,
            exchangeRateOptional
        );

        Invoice invoice = createInvoiceObject(
            draftInvoiceNumber,
            subscription.getEntityId(),
            invoiceDate,
            accountId,
            subscription.getResellerAccountId(),
            subscriptionId,
            subscription.getCurrency(),
            decoratedItems,
            shippingContact,
            billingContact,
            subscription.getPaymentTerm(),
            taxResult.getTaxTransaction(),
            invoicePurchaseOrderNumber,
            subscription.getPurchaseOrderRequiredForInvoicing(),
            emailNotifiersList,
            exchangeRateOptional,
            generationMethod,
            generatedBy
        );

        Invoice stored = invoiceDAO.addInvoice(invoice);

        Invoice hydratedInvoice = InvoiceLineItemFieldsHydrator.hydrateInvoiceItemsWithPlanId(productCatalogGetService, stored);
        generateInvoiceDocument(hydratedInvoice.getInvoiceNumber());

        return Optional.of(hydratedInvoice);
    }

    private List<InvoiceItem> filterInvoiceItemsByOrderLineItemIds(List<InvoiceItem> invoiceItems, List<String> orderLineItemIds) {
        if (orderLineItemIds == null) {
            return invoiceItems;
        }

        List<String> nonEmptyLineItemIds = orderLineItemIds.stream().filter(StringUtils::isNotBlank).toList();

        if (CollectionUtils.isEmpty(nonEmptyLineItemIds)) {
            return invoiceItems;
        }

        return invoiceItems
            .stream()
            .filter(invoiceItem -> nonEmptyLineItemIds.contains(invoiceItem.getOrderLineItemId()))
            .collect(Collectors.toList());
    }

    private Optional<TransactionalExchangeRate> getTransactionalExchangeRate(Optional<Instant> invoiceDate, Subscription subscription) {
        if (!featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            return Optional.empty();
        }
        // fetch exchange rate using functional and transactional currencies
        String transactionalCurrency = subscription.getCurrency().getCurrencyCode();
        Entity entity = entityGetService.getEntityById(subscription.getEntityId());
        String functionalCurrency = entity.getFunctionalCurrency();
        return getExchangeRateByInvoiceDate(invoiceDate, transactionalCurrency, functionalCurrency);
    }

    private Optional<TransactionalExchangeRate> getExchangeRateByInvoiceDate(
        Optional<Instant> invoiceDate,
        String transactionalCurrency,
        String functionalCurrency
    ) {
        if (transactionalCurrency.equals(functionalCurrency)) {
            return Optional.empty();
        }
        Optional<TransactionalExchangeRate> exchangeRateOptional = invoiceDate.isPresent()
            ? transactionalExchangeRateService.getRefreshExchangeRateAsOf(transactionalCurrency, functionalCurrency, invoiceDate.get())
            : transactionalExchangeRateService.getRefreshExchangeRateAsOf(transactionalCurrency, functionalCurrency, clock.instant());
        if (exchangeRateOptional.isEmpty()) {
            throw new ConflictingStateException("Exchange rate not found for invoice date");
        }
        return exchangeRateOptional;
    }

    private List<InvoiceItem> populateFunctionalAmountsOnInvoiceItems(
        List<InvoiceItem> invoiceItems,
        Subscription subscription,
        Optional<TransactionalExchangeRate> exchangeRateOptional
    ) {
        if (!featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            return invoiceItems;
        }
        String transactionalCurrency = subscription.getCurrency().getCurrencyCode();
        Entity entity = entityGetService.getEntityById(subscription.getEntityId());
        String functionalCurrency = entity.getFunctionalCurrency();
        return populateFunctionalAmountsOnInvoiceItems(invoiceItems, transactionalCurrency, functionalCurrency, exchangeRateOptional);
    }

    private List<InvoiceItem> populateFunctionalAmountsOnInvoiceItems(
        List<InvoiceItem> invoiceItems,
        String transactionalCurrency,
        String functionalCurrency,
        Optional<TransactionalExchangeRate> exchangeRateOptional
    ) {
        return invoiceItems
            .stream()
            .map(invoiceItem -> populateFunctionalAmountsOnInvoiceItem(invoiceItem, transactionalCurrency, functionalCurrency, exchangeRateOptional))
            .collect(Collectors.toList());
    }

    private InvoiceItem populateFunctionalAmountsOnInvoiceItem(
        InvoiceItem invoiceItem,
        String transactionalCurrency,
        String functionalCurrency,
        Optional<TransactionalExchangeRate> exchangeRateOptional
    ) {
        BigDecimal functionalListAmount;
        BigDecimal functionalDiscountAmount;
        BigDecimal functionalAmount;
        BigDecimal functionalTaxAmount;
        if (transactionalCurrency.equals(functionalCurrency)) {
            functionalAmount = invoiceItem.getAmount();
            functionalListAmount = invoiceItem.getListAmount();
            functionalDiscountAmount = invoiceItem.getDiscountAmount();
            functionalTaxAmount = invoiceItem.getTaxAmount();
        } else {
            TransactionalExchangeRate exchangeRate = exchangeRateOptional.orElseThrow();
            functionalListAmount = Numbers.applyExchangeRate(invoiceItem.getListAmount(), exchangeRate.getExchangeRate(), functionalCurrency);
            functionalAmount = Numbers.applyExchangeRate(invoiceItem.getAmount(), exchangeRate.getExchangeRate(), functionalCurrency);
            functionalDiscountAmount = Numbers.applyExchangeRate(invoiceItem.getDiscountAmount(), exchangeRate.getExchangeRate(), functionalCurrency);
            functionalTaxAmount = Numbers.applyExchangeRate(invoiceItem.getTaxAmount(), exchangeRate.getExchangeRate(), functionalCurrency);
        }
        return new InvoiceItem.InvoiceItemBuilder()
            .from(invoiceItem)
            .functionalListAmount(functionalListAmount)
            .functionalDiscountAmount(functionalDiscountAmount)
            .functionalAmount(functionalAmount)
            .functionalTaxAmount(functionalTaxAmount)
            .createInvoiceItem();
    }

    private Optional<String> getPurchaseOrderForInvoiceFromSubscription(Subscription subscription) {
        if (CollectionUtils.isEmpty(subscription.getPurchaseOrders())) {
            return Optional.empty();
        }
        return Optional.of(subscription.getPurchaseOrders().get(0).getPurchaseOrderNumber());
    }

    public InvoiceGenerationCheckResult performInvoiceGenerationCheck(
        String subscriptionId,
        Instant invoiceTargetDate,
        InvoiceChargeInclusionOption invoiceChargeInclusionOption
    ) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is required");
        Validator.validateNonNullArgument(invoiceTargetDate, "invoiceTargetDate");

        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        String accountId = subscription.getAccountId();
        Account account = accountGetService.getAccount(accountId);

        Optional<Invoice> existingDraftInvoice = invoiceDAO.getDraftInvoiceForSubscription(subscriptionId);

        if (existingDraftInvoice.isPresent()) {
            if (existingDraftInvoice.get().getGenerationMethod() == InvoiceGenerationMethod.BULK_INVOICE_RUN) {
                // subscription already has an open draft invoice from bulk invoice run, no further invoices can be generated
                return InvoiceGenerationCheckResult.builder()
                    .invoiceToBeGenerated(false)
                    .existingDraftInvoice(existingDraftInvoice)
                    .subscription(subscription)
                    .account(account)
                    .build();
            } else {
                // subscription already has an open draft generated from a different path, do not include in bulk operation
                return InvoiceGenerationCheckResult.builder()
                    .invoiceToBeGenerated(false)
                    .existingDraftInvoice(Optional.empty())
                    .subscription(subscription)
                    .account(account)
                    .build();
            }
        }

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);
        if (CollectionUtils.isEmpty(orders)) {
            LOGGER.warn("orders list is empty for subscription: {}", subscriptionId);
        }

        if (invoiceTargetDate.isBefore(subscription.getStartDate())) {
            // if the target date is before subscription start date no invoices to be generated
            return InvoiceGenerationCheckResult.builder()
                .invoiceToBeGenerated(false)
                .existingDraftInvoice(Optional.empty())
                .subscription(subscription)
                .account(account)
                .build();
        }

        // if the target date is past subscription end date, generate for whole subscription
        if (invoiceTargetDate.isAfter(subscription.getEndDate())) {
            invoiceTargetDate = subscription.getEndDate();
        }

        Map<String, Charge> chargeMap = getChargeMap(orders);

        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems = invoiceDAO.getMemoizedInvoiceItemsForSubscription(subscription.getSubscriptionId());
        List<InvoiceItem> invoiceItems = getInvoiceItems(
            invoiceTargetDate,
            subscription,
            timeZone,
            orders,
            chargeMap,
            invoiceChargeInclusionOption,
            memoizedInvoiceLineItems,
            false
        );

        return InvoiceGenerationCheckResult.builder()
            .invoiceToBeGenerated(!invoiceItems.isEmpty())
            .existingDraftInvoice(Optional.empty())
            .subscription(subscription)
            .account(account)
            .build();
    }

    public Optional<Invoice> getDraftInvoiceForSubscription(String subscriptionId) {
        return invoiceDAO.getDraftInvoiceForSubscription(subscriptionId);
    }

    private TaxResult applyTaxAndGetResult(
        Account account,
        AccountContact shippingContact,
        AccountContact billingContact,
        Map<String, Charge> chargeMap,
        List<InvoiceItem> invoiceItems,
        Invoice.Number invoiceNumber,
        Currency currency,
        Optional<Instant> invoiceDate
    ) {
        Map<UUID, TaxRate> taxRateMap = getTaxRateMap(invoiceItems, chargeMap);

        Account billingAccount = accountGetService.getAccount(billingContact.getAccountId());

        Optional<TaxTransaction> taxTxnOptional = processTaxesForItems(
            invoiceItems,
            taxRateMap,
            chargeMap,
            account,
            shippingContact.getAddress(),
            billingContact.getAddress(),
            Optional.ofNullable(billingAccount.getTaxExemptionUseCode()),
            invoiceNumber,
            currency,
            invoiceDate
        );

        // nothing to do set tax to 0 and return
        if (taxTxnOptional.isEmpty()) {
            List<InvoiceItem> zeroTaxItems = invoiceItems.stream().map(InvoiceServiceInternal::taxZeroedOutItem).collect(Collectors.toList());
            return TaxResult.builder().taxTransaction(taxTxnOptional).taxedInvoiceItems(zeroTaxItems).build();
        }

        TaxTransaction taxTransaction = taxTxnOptional.get();
        List<InvoiceItem> taxedInvoiceItems = new ArrayList<>();
        for (InvoiceItem preTaxItem : invoiceItems) {
            taxedInvoiceItems.add(applyTaxForInvoiceLineItem(chargeMap, taxRateMap, taxTransaction, preTaxItem));
        }
        return TaxResult.builder().taxTransaction(taxTxnOptional).taxedInvoiceItems(taxedInvoiceItems).build();
    }

    private InvoiceItem applyTaxForInvoiceLineItem(
        Map<String, Charge> chargeMap,
        Map<UUID, TaxRate> taxRateMap,
        TaxTransaction taxTransaction,
        InvoiceItem preTaxItem
    ) {
        if (chargeMap.get(preTaxItem.getChargeId()).getTaxRateId() == null) {
            // if tax rate is missing then 0 tax
            return taxZeroedOutItem(preTaxItem);
        }

        BigDecimal taxAmount = taxTransaction
            .getComputedTaxByLineNo()
            .computeIfAbsent(preTaxItem.getInvoiceLineNumber(), key -> {
                String message = String.format(
                    "tax line item missing for expected invoice item (order line item): %s",
                    preTaxItem.getOrderLineItemId()
                );
                throw new ExternalDependencyException(message);
            });

        return new InvoiceItem.InvoiceItemBuilder()
            .from(preTaxItem)
            .taxAmount(taxAmount)
            .taxRate(taxRateMap.get(chargeMap.get(preTaxItem.getChargeId()).getTaxRateId()))
            .createInvoiceItem();
    }

    private static InvoiceItem taxZeroedOutItem(InvoiceItem invoiceItem) {
        return new InvoiceItem.InvoiceItemBuilder().from(invoiceItem).taxAmount(BigDecimal.ZERO).createInvoiceItem();
    }

    private Map<UUID, TaxRate> getTaxRateMap(List<InvoiceItem> preTaxInvoiceItems, Map<String, Charge> chargeMap) {
        // optimization: get tax rates only once
        // makes things complex but probably worth it
        return preTaxInvoiceItems
            .stream()
            .map(invoiceItem -> chargeMap.get(invoiceItem.getChargeId()))
            .map(Charge::getTaxRateId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toMap(Functions.identity(), taxRateGetService::getTaxRate));
    }

    private Optional<TaxTransaction> processTaxesForItems(
        List<InvoiceItem> preTaxInvoiceItems,
        Map<UUID, TaxRate> taxRateMap,
        Map<String, Charge> chargeMap,
        Account account,
        AccountAddress shippingAddress,
        AccountAddress billingAddress,
        Optional<TaxExemptionUseCode> exemptionUseCodeOptional,
        Invoice.Number invoiceNumber,
        Currency currency,
        Optional<Instant> invoiceDate
    ) {
        if (!featureService.isEnabled(Feature.TAX_PERCENT) && !taxService.tenantHasTaxServiceIntegration()) {
            return Optional.empty();
        }

        if (preTaxInvoiceItems.isEmpty()) {
            return Optional.empty();
        }

        // if address is not available then return error
        if (shippingAddress == null) {
            throw new ConflictingStateException("Address is required for tax calculation");
        }

        List<TaxLineItem> taxLines = generateTaxLineItems(preTaxInvoiceItems, chargeMap, taxRateMap);

        TaxCalculationInput taxCalculationInput = ImmutableTaxCalculationInput.builder()
            .taxTransactionCode(invoiceNumber.getNumber())
            .taxLineItems(taxLines)
            .account(account)
            .shippingAddress(shippingAddress)
            .billingAddress(Optional.ofNullable(billingAddress))
            .taxExceptionUseCode(exemptionUseCodeOptional)
            .currency(currency)
            .invoiceDate(invoiceDate)
            .build();
        // NOTE: draft invoice number becomes tax transaction code
        return taxService.createTaxTransaction(taxCalculationInput);
    }

    private List<TaxLineItem> generateTaxLineItems(
        List<InvoiceItem> preTaxInvoiceItems,
        Map<String, Charge> chargeMap,
        Map<UUID, TaxRate> taxRateMap
    ) {
        Map<String, Product> productByCharge = productMapByChargeId(chargeMap.keySet());
        if (taxService.tenantHasTaxServiceIntegration()) {
            return getTaxLineItemsWithIntegration(preTaxInvoiceItems, chargeMap, taxRateMap, productByCharge);
        }
        return getTaxLineItemsWithoutIntegration(preTaxInvoiceItems, chargeMap, taxRateMap, productByCharge);
    }

    private static List<TaxLineItem> getTaxLineItemsWithIntegration(
        List<InvoiceItem> preTaxInvoiceItems,
        Map<String, Charge> chargeMap,
        Map<UUID, TaxRate> taxRateMap,
        Map<String, Product> productByCharge
    ) {
        List<InvoiceItem> linesMissingTaxRates = preTaxInvoiceItems
            .stream()
            .filter(invoiceItem -> chargeMap.get(invoiceItem.getChargeId()).getTaxRateId() == null)
            .toList();
        if (!linesMissingTaxRates.isEmpty()) {
            Set<String> chargesMissingTaxRate = linesMissingTaxRates
                .stream()
                .map(invoiceItem -> chargeMap.get(invoiceItem.getChargeId()).getChargeId())
                .collect(Collectors.toSet());
            throw new ConflictingStateException(
                String.format(
                    "Tax rate is required for tax calculation, the following charges are missing tax rates: %s",
                    String.join(", ", chargesMissingTaxRate)
                )
            );
        }

        return preTaxInvoiceItems
            .stream()
            // safety check before generating tax lines
            .peek(InvoiceServiceInternal::validateInvoiceItemHasLineNumber)
            .map(invoiceItem -> generateTaxLineItem(invoiceItem, chargeMap, taxRateMap, productByCharge))
            .toList();
    }

    private static List<TaxLineItem> getTaxLineItemsWithoutIntegration(
        List<InvoiceItem> preTaxInvoiceItems,
        Map<String, Charge> chargeMap,
        Map<UUID, TaxRate> taxRateMap,
        Map<String, Product> productByCharge
    ) {
        return preTaxInvoiceItems
            .stream()
            // safety check before generating tax lines
            .peek(InvoiceServiceInternal::validateInvoiceItemHasLineNumber)
            .filter(invoiceItem -> chargeMap.get(invoiceItem.getChargeId()).getTaxRateId() != null)
            .map(invoiceItem -> generateTaxLineItem(invoiceItem, chargeMap, taxRateMap, productByCharge))
            .toList();
    }

    private static void validateInvoiceItemHasLineNumber(InvoiceItem invoiceItem) {
        if (StringUtils.isBlank(invoiceItem.getInvoiceLineNumber())) {
            String message = String.format("invoice line no null/empty for invoice with orderLineItemId: %s", invoiceItem.getOrderLineItemId());
            throw new ServiceFailureException(message);
        }
    }

    private static TaxLineItem generateTaxLineItem(
        InvoiceItem invoiceItem,
        Map<String, Charge> chargeMap,
        Map<UUID, TaxRate> taxRateMap,
        Map<String, Product> productByCharge
    ) {
        var charge = chargeMap.get(invoiceItem.getChargeId());
        var taxRateId = charge.getTaxRateId();
        TaxRate taxRate = taxRateMap.get(taxRateId);
        Product product = productByCharge.get(invoiceItem.getChargeId());
        return TaxLineItem.builder()
            .lineNo(invoiceItem.getInvoiceLineNumber())
            .taxCode(taxRate.getTaxCode())
            .taxPercentage(Optional.ofNullable(taxRate.getTaxPercentage()))
            .amount(invoiceItem.getAmount())
            .quantity(new BigDecimal(invoiceItem.getQuantity()))
            .itemCode(product.getSku())
            .itemDescription(product.getName())
            .build();
    }

    private Map<String, Product> productMapByChargeId(Set<String> chargeIds) {
        return chargeIds.stream().collect(Collectors.toMap(Functions.identity(), this::getProductByChargeId));
    }

    private Product getProductByChargeId(String chargeId) {
        List<Plan> plans = productCatalogGetService.getPlansFromChargeIds(List.of(chargeId));
        if (plans.size() != 1) {
            String message = String.format("plans for chargeId:%s not expected size of 1 plans:%s", chargeId, plans);
            throw new ServiceFailureException(message);
        }
        return productCatalogGetService.getProduct(plans.get(0).getProductId());
    }

    private List<InvoiceItem> getInvoiceItems(
        Instant invoiceTargetDate,
        Subscription subscription,
        TimeZone timeZone,
        List<Order> orders,
        Map<String, Charge> chargeMap,
        InvoiceChargeInclusionOption invoiceChargeInclusionOption,
        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems,
        boolean forMemoizationBackfill
    ) {
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(subscription);

        InvoiceBillingInfo invoiceBillingInfo = ImmutableInvoiceBillingInfo.builder()
            .invoiceTargetDate(invoiceTargetDate)
            .subscriptionStart(subscription.getStartDate())
            .subscriptionEnd(subscription.getEndDate())
            .billingAnchorDate(subscription.getBillingAnchorDate())
            .currency(subscription.getCurrency())
            .timeZone(timeZone)
            .prorationConfig(prorationConfig)
            .billingCycle(subscription.getBillingCycle())
            .billingTerm(subscription.getBillingTerm())
            .build();

        // todo: should periods be stored? Is it reliable to recalculate the period each time this is invoked?
        // TODO : The above todo makes sense for custom billing as billing periods can be generated upfront during order execution
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(subscription, timeZone, invoiceTargetDate);

        Map<String, InvoicePreviewInput.CustomBillingInvoicingInput> customBillingInvoicingInput = null;
        if (featureService.isEnabled(Feature.CUSTOM_BILLING) && subscription.getBillingCycle().getCycle() == Cycle.CUSTOM) {
            customBillingInvoicingInput = getCustomBillingInvoicingInputForSubscription(timeZone, orders, chargeMap, invoiceBillingInfo);
        }

        List<OrderLineItem> orderLines = orders
            .stream()
            .filter(order -> !order.getStartDate().isAfter(invoiceTargetDate))
            .map(Order::getLineItems)
            .flatMap(Collection::stream)
            .toList();

        // skip orders that start after the invoice target date
        List<OrderLineItem> orderLinesNetEffect = orders
            .stream()
            .filter(order -> !order.getStartDate().isAfter(invoiceTargetDate))
            .flatMap(order -> order.getLineItemsNetEffect().stream())
            .collect(Collectors.toList());

        Predicate<MemoizedInvoiceLineItem> filterBackfilledMemoizedLinePredicate = filterBackfilledMemoizedLinesPredicate(forMemoizationBackfill);

        Map<String, List<MemoizedInvoiceLineItem>> memoizedByOrderLineMap = memoizedInvoiceLineItems
            .stream()
            // Explicit safety check at the lowest level to remove backfilled memoized invoice lines for regular flows, if FF is not enabled for the tenant
            .filter(filterBackfilledMemoizedLinePredicate)
            .collect(Collectors.groupingBy(MemoizedInvoiceLineItem::getOrderLineItemId));

        ImmutableInvoiceGenerationInput invoiceGenerationInput = ImmutableInvoiceGenerationInput.builder()
            .invoiceBillingInfo(invoiceBillingInfo)
            .subscription(subscription)
            .customBillingInvoicingInput(customBillingInvoicingInput)
            .putAllChargeMap(chargeMap)
            .addAllOrderLineItems(orderLines)
            .addAllOrderLineItemsNetEffect(orderLinesNetEffect)
            .addAllTopLevelBillingPeriods(billingPeriods)
            .putAllMemoizedItemsByOrderLine(memoizedByOrderLineMap)
            .build();

        List<InvoiceItem> invoiceLineItems = new ArrayList<>();
        if (invoiceChargeInclusionOption.isUsageIncluded()) {
            logChargesNotInChargeMap(subscription, chargeMap);
            // do not include the current period when actually generating invoice
            if (featureService.isEnabled(Feature.INCLUDE_USAGE_IN_PROGRESS)) {
                // if feature is enabled, include in progress periods
                invoiceGenerationInput.getInvoiceProcessorModes().setMode(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS);
            } else {
                invoiceGenerationInput.getInvoiceProcessorModes().unsetMode(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS);
            }
            List<InvoiceItem> usageInvoiceItems = usageInvoiceProcessor.generateInvoiceItems(invoiceGenerationInput);
            invoiceLineItems.addAll(usageInvoiceItems);
        }

        if (invoiceChargeInclusionOption != InvoiceChargeInclusionOption.ONLY_USAGE) {
            List<OrderLineItem> percentOfOrderLines = getOrderLinesByType(orderLinesNetEffect, chargeMap, ChargeType.PERCENTAGE_OF);

            invoiceLineItems.addAll(recurringInvoiceProcessor.generateInvoiceItems(invoiceGenerationInput));
            invoiceLineItems.addAll(oneTimeInvoiceProcessor.generateInvoiceItems(invoiceGenerationInput));
            invoiceLineItems.addAll(prepaidInvoiceProcessor.generateInvoiceItems(invoiceGenerationInput));

            List<InvoiceItem> invoiceItemsFromMemoization = new ArrayList<>();
            // percent of invoice items is not memoized because there is no computation per se during invoice generation
            // simple proration ratio of billing period to order line item period is used to generate invoice amount
            // TODO: we can come up with the memoization strategy to memoize the above mentioned computation but will involve
            // TODO: more work and can be done as a part of future memoization enhancements
            List<InvoiceItem> percentOfInvoiceItems = generatePercentOfInvoiceItems(
                invoiceGenerationInput,
                percentOfOrderLines,
                invoiceItemsFromMemoization
            );

            List<InvoiceItem> percentOfInvoiceItemsFromProcessor = percentOfInvoiceProcessor.generateInvoiceItems(invoiceGenerationInput);
            InvoiceProcessorUtility.logInvoiceItemDifferences(percentOfInvoiceItems, percentOfInvoiceItemsFromProcessor, "percentOf", false);
            InvoiceProcessorUtility.logInvoiceItemDifferences(
                percentOfInvoiceItems,
                invoiceItemsFromMemoization,
                "percentOfAgainstMemoization",
                false
            );
            invoiceLineItems.addAll(percentOfInvoiceItems);
        }

        invoiceLineItems = sortInvoiceItemsBySubscriptionChargeRank(invoiceLineItems);

        return generateLineNumbers(invoiceLineItems);
    }

    @VisibleForTesting
    Predicate<MemoizedInvoiceLineItem> filterBackfilledMemoizedLinesPredicate(boolean forMemoizationBackfill) {
        return memoizedInvoiceLineItem -> {
            if (forMemoizationBackfill) {
                return true;
            }
            // Backfilled lines should have been fetched from DAO layer only if FF is enabled
            if (Boolean.TRUE.equals(memoizedInvoiceLineItem.getIsBackfilled())) {
                if (featureService.isEnabled(Feature.USE_BACKFILLED_MEMOIZATION)) {
                    LOGGER.debug(
                        "Using backfilled memoized line {} in order {} for invoicing",
                        memoizedInvoiceLineItem.getId(),
                        memoizedInvoiceLineItem.getOrderId()
                    );
                    return true;
                }
                throw new IllegalStateException(
                    "Encountered backfilled memoized invoice line item: " + memoizedInvoiceLineItem.getId() + ", this should have been never fetched"
                );
            }
            return true;
        };
    }

    private void logChargesNotInChargeMap(Subscription subscription, Map<String, Charge> chargeMap) {
        subscription
            .getCharges()
            .stream()
            .filter(sc -> !chargeMap.containsKey(sc.getChargeId()))
            .forEach(sc ->
                LOGGER.warn(
                    "Charge {} not found in charge map for subscription {}, chargeMap: {}",
                    sc.getChargeId(),
                    subscription.getSubscriptionId(),
                    chargeMap
                )
            );
    }

    private List<InvoiceItem> getPostedInvoiceItemsForSubscription(String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscriptionId is required");
        return invoiceDAO.getAllInvoiceItemsForSubscription(subscriptionId);
    }

    public Optional<InvoiceItem> getPostedInvoiceItemByOrderLineIdAndDebookPeriod(String orderLineId, Period debookPeriod) {
        Validator.validateNonNullArguments(orderLineId, debookPeriod);
        return invoiceDAO.getPostedInvoiceItemByOrderLineIdAndDebookPeriod(orderLineId, debookPeriod);
    }

    public List<InvoiceItem> getPostedInvoiceItemsBySubscriptionIdChargeId(String subscriptionId, String chargeId) {
        return getPostedInvoiceItemsForSubscription(subscriptionId)
            .stream()
            .filter(invoiceItem -> invoiceItem.getChargeId().equals(chargeId))
            .collect(Collectors.toList());
    }

    // gets usage invoice items that haven't yet been billed, including current (potentially incomplete period)
    // this can be used to preview usage invoices that have yet to be posted
    public List<InvoiceItem> getUnbilledUsageInvoiceItems(String subscriptionId) {
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);

        InvoiceBillingInfo invoiceBillingInfo = ImmutableInvoiceBillingInfo.builder()
            .invoiceTargetDate(subscription.getEndDate())
            .subscriptionStart(subscription.getStartDate())
            .subscriptionEnd(subscription.getEndDate())
            .billingAnchorDate(subscription.getBillingAnchorDate())
            .currency(subscription.getCurrency())
            .timeZone(timeZone)
            .prorationConfig(prorationConfigurationGetService.resolveProrationConfig(subscription))
            .billingCycle(subscription.getBillingCycle())
            .billingTerm(subscription.getBillingTerm())
            .build();

        Map<String, Charge> chargeMap = getChargeMap(orders);
        List<OrderLineItem> allOrderLineItems = orders.stream().map(Order::getLineItems).flatMap(List::stream).toList();

        ImmutableInvoiceGenerationInput usageInvoiceGenerationInput = ImmutableInvoiceGenerationInput.builder()
            .invoiceBillingInfo(invoiceBillingInfo)
            .subscription(subscription)
            .putAllChargeMap(chargeMap)
            .addAllOrderLineItems(allOrderLineItems)
            .addAllOrderLineItemsNetEffect(allOrderLineItems)
            // usage invoice generation does not require top level billing periods
            .addAllTopLevelBillingPeriods(List.of())
            // usage does not use memoization
            .putAllMemoizedItemsByOrderLine(Map.of())
            .build();

        usageInvoiceGenerationInput.getInvoiceProcessorModes().setMode(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS);
        return usageInvoiceProcessor.generateInvoiceItems(usageInvoiceGenerationInput);
    }

    public List<InvoiceItem> getUsageInvoiceItemsForSubscriptionCharge(String subscriptionId, String chargeId) {
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        List<Order> orders = orderGetService.getExecutedOrdersBySubscriptionId(subscriptionId);

        InvoiceBillingInfo invoiceBillingInfo = ImmutableInvoiceBillingInfo.builder()
            .invoiceTargetDate(subscription.getEndDate())
            .subscriptionStart(subscription.getStartDate())
            .subscriptionEnd(subscription.getEndDate())
            .billingAnchorDate(subscription.getBillingAnchorDate())
            .currency(subscription.getCurrency())
            .timeZone(timeZone)
            .prorationConfig(prorationConfigurationGetService.resolveProrationConfig(subscription))
            .billingCycle(subscription.getBillingCycle())
            .billingTerm(subscription.getBillingTerm())
            .build();

        Map<String, Charge> chargeMap = getChargeMap(orders);
        List<OrderLineItem> allOrderLineItems = orders.stream().map(Order::getLineItems).flatMap(List::stream).toList();

        SubscriptionCharge usageSubscriptionCharge = subscription
            .getCharges()
            .stream()
            .filter(sc -> chargeId.equals(sc.getChargeId()))
            .findFirst()
            .orElseThrow(() -> new InvalidInputException(String.format("charge id %s was not found in subscription %s", chargeId, subscriptionId)));

        List<InvoiceItem> billedInvoiceItems = invoiceDAO.getInvoiceItemsBySubscriptionChargeAndPeriod(
            usageSubscriptionCharge.getSubscriptionChargeId(),
            Period.between(invoiceBillingInfo.getSubscriptionStart(), invoiceBillingInfo.getSubscriptionEnd())
        );
        billedInvoiceItems.forEach(invoiceItem -> invoiceItem.setIsBilled(true));

        ImmutableInvoiceGenerationInput usageInvoiceGenerationInput = ImmutableInvoiceGenerationInput.builder()
            .invoiceBillingInfo(invoiceBillingInfo)
            .subscription(subscription)
            .putAllChargeMap(chargeMap)
            .addAllOrderLineItems(allOrderLineItems)
            .addAllOrderLineItemsNetEffect(allOrderLineItems)
            // usage invoice generation does not require top level billing periods
            .addAllTopLevelBillingPeriods(List.of())
            // usage does not use memoization
            .putAllMemoizedItemsByOrderLine(Map.of())
            .build();

        usageInvoiceGenerationInput.getInvoiceProcessorModes().setMode(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS);
        List<InvoiceItem> unbilledInvoiceItems = usageInvoiceProcessor
            .generateInvoiceItems(usageInvoiceGenerationInput)
            .stream()
            .filter(invoiceItem -> invoiceItem.getChargeId().equals(chargeId))
            .toList();
        unbilledInvoiceItems.forEach(item -> item.setIsBilled(false));

        return Stream.of(billedInvoiceItems, unbilledInvoiceItems)
            .flatMap(Collection::stream)
            .sorted((a, b) -> DateTimeCalculator.compareInstants(a.getPeriodEndDate(), b.getPeriodEndDate()))
            .toList();
    }

    public Optional<InvoiceItem> getLatestInvoiceItemBySubscriptionChargeGroupId(String subscriptionChargeGroupId) {
        return invoiceDAO.getLatestInvoiceItemBySubscriptionChargeGroupId(subscriptionChargeGroupId);
    }

    // TODO: move this method into PercentOfChargeInvoiceProcessor as a part of refactor of this class
    private List<InvoiceItem> generatePercentOfInvoiceItems(
        InvoiceGenerationInput invoiceGenerationInput,
        List<OrderLineItem> percentOfOrderLines,
        List<InvoiceItem> invoiceItemsFromMemoization
    ) {
        List<OrderLineItem> standardPercentOfOrderLines = percentOfInvoiceProcessor.getNonEventBasedAndNonAdhocCustomBillingOrderLines(
            percentOfOrderLines,
            invoiceGenerationInput.getChargeMap(),
            invoiceGenerationInput
        );
        // We don't support event based order lines for percent of charge so no check for event based order lines
        List<OrderLineItem> adhocCustomBillingOrderLines = percentOfInvoiceProcessor.getAdhocCustomBillingOrderLines(
            percentOfOrderLines,
            standardPercentOfOrderLines
        );

        List<InvoiceItem> standardPercentOfInvoiceItems = standardPercentOfOrderLines
            .stream()
            .map(orderItem -> generateInvoiceItemsForPercentOrderLine(invoiceGenerationInput, orderItem, invoiceItemsFromMemoization))
            .flatMap(List::stream)
            .toList();

        List<InvoiceItem> adhocCustomBillingInvoiceItems = percentOfInvoiceProcessor.generateAdhocCustomBillingInvoiceItems(
            invoiceGenerationInput,
            adhocCustomBillingOrderLines
        );

        return Stream.of(standardPercentOfInvoiceItems, adhocCustomBillingInvoiceItems).flatMap(List::stream).toList();
    }

    private List<InvoiceItem> generateInvoiceItemsForPercentOrderLine(
        InvoiceGenerationInput invoiceGenerationInput,
        OrderLineItem orderItem,
        List<InvoiceItem> invoiceItemsFromMemoization
    ) {
        List<BillingPeriod> topLevelBillingPeriods = invoiceGenerationInput.getTopLevelBillingPeriods();
        List<BillingPeriod> finalBillingPeriods = subscriptionBillingPeriodService.getOrderLineBillingPeriods(
            orderItem,
            invoiceGenerationInput.getChargeMap(),
            invoiceGenerationInput.getInvoiceBillingInfo(),
            topLevelBillingPeriods
        );

        return finalBillingPeriods
            .stream()
            .filter(billingPeriod -> isOrderLineEligibleForInvoicing(invoiceGenerationInput, orderItem, billingPeriod))
            .flatMap(billingPeriod ->
                generatePercentOfInvoiceItemForBillingPeriod(invoiceGenerationInput, billingPeriod, orderItem, invoiceItemsFromMemoization).stream()
            )
            .toList();
    }

    private boolean isOrderLineEligibleForInvoicing(
        InvoiceGenerationInput invoiceGenerationInput,
        OrderLineItem orderItem,
        BillingPeriod billingPeriod
    ) {
        InvoiceBillingInfo invoiceBillingInfo = invoiceGenerationInput.getInvoiceBillingInfo();
        if (
            !orderLineItemInPeriod(
                orderItem,
                billingPeriod,
                invoiceGenerationInput.getChargeMap().get(orderItem.getChargeId()),
                invoiceBillingInfo.getInvoiceTargetDate(),
                invoiceBillingInfo.getSubscriptionEnd()
            )
        ) {
            return false;
        }
        Set<String> orderLineItemIdsAlreadyInvoiced = new HashSet<>(
            invoiceDAO.getAllOrderLineIdsWithInvoiceItemsPastThreshold(List.of(orderItem.getOrderLineId()), billingPeriod.getStart())
        );
        return !orderLineItemIdsAlreadyInvoiced.contains(orderItem.getOrderLineId());
    }

    private List<InvoiceItem> generatePercentOfInvoiceItemForBillingPeriod(
        InvoiceGenerationInput invoiceGenerationInput,
        BillingPeriod billingPeriod,
        OrderLineItem orderItem,
        List<InvoiceItem> invoiceItemsFromMemoization
    ) {
        Map<String, List<MemoizedInvoiceLineItem>> memoizedByOrderLineMap = invoiceGenerationInput.getMemoizedItemsByOrderLine();
        if (memoizedByOrderLineMap.containsKey(orderItem.getOrderLineId())) {
            // TODO : Once we are confident that memoization is working fine we should be using these lines instead of regenerating via helper
            // NOTE : for now we are just storing these lines to compare them with the lines generated by helper
            invoiceItemsFromMemoization.addAll(getMemoizedInvoiceItemsForBillingPeriod(memoizedByOrderLineMap, orderItem, billingPeriod));
        }
        InvoiceBillingInfo invoiceBillingInfo = invoiceGenerationInput.getInvoiceBillingInfo();
        return List.of(
            percentOfChargeHelper
                .getPercentOfInvoiceItemForBillingPeriod(
                    invoiceBillingInfo,
                    billingPeriod,
                    orderItem,
                    invoiceGenerationInput.getChargeMap().get(orderItem.getChargeId()),
                    true,
                    List.of(), // we don't use the individual discounts from memoization
                    List.of() // we don't use the individual discounts from memoization
                )
                .orElseThrow(() ->
                    new ServiceFailureException(
                        String.format(
                            "Could not generate invoice item for percent of order line: %s within period %s to %s",
                            orderItem.getOrderLineId(),
                            billingPeriod.getStart(),
                            billingPeriod.getEnd()
                        )
                    )
                )
        );
    }

    private List<InvoiceItem> getMemoizedInvoiceItemsForBillingPeriod(
        Map<String, List<MemoizedInvoiceLineItem>> memoizedByOrderLineMap,
        OrderLineItem orderItem,
        BillingPeriod billingPeriod
    ) {
        Period orderItemBillingPeriodOverlap = getItemOverlapPeriod(orderItem.getEffectiveDate(), orderItem.getEndDate(), billingPeriod);
        return memoizedByOrderLineMap
            .get(orderItem.getOrderLineId())
            .stream()
            .filter(
                memoizedInvoiceItem ->
                    memoizedInvoiceItem.getPeriodStartDate().compareTo(orderItemBillingPeriodOverlap.getStart()) >= 0 &&
                    memoizedInvoiceItem.getPeriodEndDate().compareTo(orderItemBillingPeriodOverlap.getEnd()) <= 0
            )
            .map(memoizedInvoiceLineItem -> memoizedInvoiceLineItem.to(orderItem))
            .toList();
    }

    private List<InvoiceItem> mergeInvoiceItemsAndGenerateLineNumbers(
        List<InvoiceItem> recurringInvoiceItems,
        List<InvoiceItem> onetimeInvoiceItems,
        List<InvoiceItem> usageInvoiceItems,
        List<InvoiceItem> prepaidInvoiceItems,
        List<InvoiceItem> percentOfInvoiceItems,
        List<InvoiceItem> adhocCustomBillingInvoiceItems
    ) {
        List<InvoiceItem> preLineNoItems = Stream.of(
            recurringInvoiceItems,
            onetimeInvoiceItems,
            usageInvoiceItems,
            prepaidInvoiceItems,
            percentOfInvoiceItems,
            adhocCustomBillingInvoiceItems
        )
            .flatMap(Collection::stream)
            .collect(Collectors.toList());

        preLineNoItems = sortInvoiceItemsBySubscriptionChargeRank(preLineNoItems);

        return generateLineNumbers(preLineNoItems);
    }

    @VisibleForTesting
    List<InvoiceItem> sortInvoiceItemsBySubscriptionChargeRank(List<InvoiceItem> invoiceItems) {
        return invoiceItems
            .stream()
            .map(this::getInvoiceItemWithRank)
            .sorted(RANKED_INVOICE_ITEM_COMPARATOR)
            .map(Pair::getRight)
            .collect(Collectors.toList());
    }

    private Pair<Integer, InvoiceItem> getInvoiceItemWithRank(InvoiceItem invoiceItem) {
        return Pair.of(getRankForInvoiceLineItem(invoiceItem), invoiceItem);
    }

    private int getRankForInvoiceLineItem(InvoiceItem invoiceItem) {
        // Subscription charge id is coming back as null for invoice preview on adding an order.
        // In such cases, let us fallback to the default rank.
        if (StringUtils.isBlank(invoiceItem.getSubscriptionChargeId())) {
            return 0;
        }

        SubscriptionCharge subscriptionCharge = subscriptionGetService.getSubscriptionChargeBySubscriptionChargeId(
            invoiceItem.getSubscriptionChargeId()
        );
        return subscriptionCharge.getRank();
    }

    // line numbers are used for tax line item correlation
    private List<InvoiceItem> generateLineNumbers(List<InvoiceItem> preLineNumberItems) {
        return IntStream.range(0, preLineNumberItems.size())
            .mapToObj(index -> {
                InvoiceItem invoiceItem = preLineNumberItems.get(index);
                return new InvoiceItem.InvoiceItemBuilder()
                    .from(invoiceItem)
                    .invoiceLineNumber(getInvoiceLineNumberFromIndex(index))
                    .createInvoiceItem();
            })
            .collect(Collectors.toList());
    }

    private static String getInvoiceLineNumberFromIndex(int index) {
        // we want the invoice line numbers not to be 0 based but 1 based
        return String.format("%d", index + 1);
    }

    private void generateInvoiceDocument(Invoice.Number invoiceNumber) {
        invoiceDocumentGenerationTrackerService.setShouldRegeneratePdf(invoiceNumber, true);
    }

    private Map<String, Charge> getChargeMap(List<Order> orders) {
        var uniqueChargeIds = orders
            .stream()
            .map(Order::getLineItems)
            .flatMap(Collection::stream)
            .map(OrderLineItem::getChargeId)
            .distinct()
            .collect(Collectors.toList());
        return productCatalogGetService.getChargeMapByChargeIds(uniqueChargeIds);
    }

    private Invoice createInvoiceObject(
        Invoice.Number invoiceNumber,
        String entityId,
        Optional<Instant> invoiceDate,
        String accountId,
        Optional<String> resellerAccountId,
        String subscriptionId,
        Currency currency,
        List<InvoiceItem> invoiceItems,
        AccountContact shippingContact,
        AccountContact billingContact,
        PaymentTerm paymentTerm,
        Optional<TaxTransaction> taxTransaction,
        Optional<String> purchaseOrderNumber,
        boolean purchaseOrderRequired,
        EmailNotifiersList emailNotifiersList,
        Optional<TransactionalExchangeRate> exchangeRateOptional,
        Optional<InvoiceGenerationMethod> generationMethod,
        Optional<String> optionalGeneratedBy
    ) {
        BigDecimal totalDiscount = invoiceItems.stream().map(InvoiceItem::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal subTotal = invoiceItems.stream().map(InvoiceItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal taxTotal = invoiceItems.stream().map(InvoiceItem::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal total = subTotal.add(taxTotal);

        String generatedBy = optionalGeneratedBy.orElseGet(() -> getInvoiceGeneratedBy(generationMethod));

        Invoice.InvoiceBuilder builder = new Invoice.InvoiceBuilder()
            .invoiceNumber(invoiceNumber)
            .entityId(entityId)
            .totalDiscount(totalDiscount)
            .subTotal(subTotal)
            .taxTotal(taxTotal)
            .total(total)
            .subscriptionId(subscriptionId)
            .customerAccountId(accountId)
            .resellerAccountId(resellerAccountId.orElse(null))
            .invoiceItems(invoiceItems)
            .billingContact(billingContact)
            .shippingContact(shippingContact)
            .status(InvoiceStatus.DRAFT)
            .currency(currency.getCurrencyCode())
            .paymentTerm(paymentTerm)
            .taxTransactionCode(taxTransaction.map(TaxTransaction::getTransactionCode).orElse(null))
            .purchaseOrderNumber(purchaseOrderNumber.orElse(null))
            .purchaseOrderRequired(purchaseOrderRequired)
            .emailNotifiersList(emailNotifiersList)
            .shouldRegeneratePdf(true)
            .generatedBy(generatedBy);

        invoiceDate.ifPresent(builder::invoiceDate);
        generationMethod.ifPresent(builder::generationMethod);

        if (featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            populateFunctionalAmountsToInvoice(builder, exchangeRateOptional, invoiceItems);
        }

        return builder.createInvoice();
    }

    private String getInvoiceGeneratedBy(Optional<InvoiceGenerationMethod> invoiceGenerationMethod) {
        if (invoiceGenerationMethod.isEmpty()) {
            return StringUtils.EMPTY;
        }

        switch (invoiceGenerationMethod.get()) {
            case USER_INITIATED, API_INITIATED -> {
                return CurrentUserProvider.provideAuthPrincipalName();
            }
            case BULK_INVOICE_RUN -> {
                LOGGER.warn("Bulk invoice run is missing invoice generated by.");
                return StringUtils.EMPTY;
            }
            case AUTOMATED_INVOICE_JOB, RULE_DRIVEN_INVOICE_JOB -> {
                return StringUtils.EMPTY;
            }
        }
        return StringUtils.EMPTY;
    }

    private void populateFunctionalAmountsToInvoice(
        Invoice.InvoiceBuilder builder,
        Optional<TransactionalExchangeRate> exchangeRateOptional,
        List<InvoiceItem> invoiceItems
    ) {
        if (exchangeRateOptional.isPresent()) {
            TransactionalExchangeRate exchangeRate = exchangeRateOptional.get();
            builder.exchangeRateId(exchangeRate.getId().orElse(null));
            builder.exchangeRate(exchangeRate.getExchangeRate());
            builder.exchangeRateDate(Instant.ofEpochSecond(exchangeRate.getEffectiveDate()));
        } else {
            builder.exchangeRateId(null);
            builder.exchangeRate(null);
            builder.exchangeRateDate(null);
        }

        BigDecimal functionalTotalDiscountAmount = invoiceItems
            .stream()
            .map(InvoiceItem::getFunctionalDiscountAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal functionalSubTotal = invoiceItems.stream().map(InvoiceItem::getFunctionalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal functionalTaxAmount = invoiceItems.stream().map(InvoiceItem::getFunctionalTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal functionalTotal = functionalSubTotal.add(functionalTaxAmount);

        builder
            .functionalTotalDiscount(functionalTotalDiscountAmount)
            .functionalSubTotal(functionalSubTotal)
            .functionalTaxTotal(functionalTaxAmount)
            .functionalTotal(functionalTotal);
    }

    private List<OrderLineItem> getOrderLinesByType(List<OrderLineItem> orderLines, Map<String, Charge> chargeMap, ChargeType type) {
        return orderLines.stream().filter(orderLineItem -> chargeMap.get(orderLineItem.getChargeId()).getType() == type).collect(Collectors.toList());
    }

    private static boolean orderLineItemInPeriod(
        OrderLineItem orderLineItem,
        BillingPeriod billingPeriod,
        Charge charge,
        Instant invoiceTargetDate,
        Instant subscriptionEndDate
    ) {
        // there may be a stub that extends past the end of the subscription. If the charge is billed in Arrears, adjustment is needed to cover the entire remaining duration
        // example: order line from Jan 1, 2023 - Dec 31, 2023. Yearly billing with first full billing cycle Feb 1, 2023.
        // in this case the second billing period is Feb 1, 2023 - Jan 31, 2024. When generating invoice / preview with target date = subscription end date
        // the target date is before the billing period end date, which means the invoice for that period doesn't get generated
        // adjusting the end date to the earlier of the subscription end date or billing period end date ensures the last billing period is always covered
        Instant periodEnd = billingPeriod.getEnd().isBefore(subscriptionEndDate) ? billingPeriod.getEnd() : subscriptionEndDate;

        if (charge.getBillingTerm() == BillingTerm.IN_ARREARS && periodEnd.isAfter(invoiceTargetDate)) {
            return false;
        }
        return orderLineItem.getEffectiveDate().isBefore(billingPeriod.getEnd()) && orderLineItem.getEndDate().isAfter(billingPeriod.getStart());
    }

    private Invoice.Number getInvoiceNumberForPostedInvoice(Configuration jooqConfiguration, String entityId, boolean isHistorical) {
        return invoiceNumberGenerator.generate(jooqConfiguration, entityId, InvoiceStatus.POSTED, isHistorical);
    }

    private Invoice.Number getInvoiceNumberForDraftInvoice(String entityId) {
        return invoiceNumberGenerator.generateDraft(entityId);
    }

    public Optional<InvoiceItem> getInvoiceItemById(UUID invoiceItemId) {
        return invoiceDAO.getInvoiceItemById(invoiceItemId);
    }

    private static class TaxResult {

        private final Optional<TaxTransaction> taxTransaction;
        private final List<InvoiceItem> taxedInvoiceItems;

        private TaxResult(Builder builder) {
            taxTransaction = builder.taxTransaction;
            taxedInvoiceItems = builder.taxedInvoiceItems;
        }

        Optional<TaxTransaction> getTaxTransaction() {
            return taxTransaction;
        }

        List<InvoiceItem> getTaxedInvoiceItems() {
            return taxedInvoiceItems;
        }

        static Builder builder() {
            return new Builder();
        }

        public static class Builder {

            private Optional<TaxTransaction> taxTransaction = Optional.empty();
            private List<InvoiceItem> taxedInvoiceItems = Collections.emptyList();

            private Builder() {}

            Builder taxTransaction(Optional<TaxTransaction> taxTransaction) {
                this.taxTransaction = taxTransaction;
                return this;
            }

            Builder taxedInvoiceItems(List<InvoiceItem> taxedInvoiceItems) {
                this.taxedInvoiceItems = taxedInvoiceItems;
                return this;
            }

            public TaxResult build() {
                return new TaxResult(this);
            }
        }
    }

    public void modifyInvoiceDueDateForTesting(String invoiceNumber, Long dueDate) {
        Validator.validateNonNullArguments(invoiceNumber, dueDate);
        invoiceDAO.modifyInvoiceDueDateForTesting(invoiceNumber, dueDate);
    }

    public void deleteInvoiceDataForSubscription(Configuration configuration, String subscriptionId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = DSL.using(configuration);
        invoiceDAO.deleteSubscriptionMemoizedItemsInTransaction(subscriptionId, tenantId, dslContext);
    }

    public void deleteInvoiceDataForOrder(Configuration configuration, String orderId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = DSL.using(configuration);
        invoiceDAO.deleteOrderMemoizedItemsInTransaction(orderId, tenantId, dslContext);
    }

    @Override
    public boolean updateInvoiceGeneratedBy(String invoiceNumber, String generatedBy) {
        Validator.validateNonNullArgument(invoiceNumber, generatedBy);
        return invoiceDAO.updateInvoiceGeneratedBy(invoiceNumber, generatedBy);
    }

    @Override
    public boolean updateEmailLastSentOn(String invoiceNumber, Instant emailSentOn) {
        Validator.validateNonNullArguments(invoiceNumber, emailSentOn);
        return invoiceDAO.updateEmailLastSentOn(invoiceNumber, emailSentOn);
    }

    public void backfillMemoizedItemsBulk(String tenantId, Integer batchSize, boolean isDryRun) {
        LOGGER.info("* * * [STARTING] Backfilling memoized lines in bulk. tenantId: {} * * *", tenantId);
        UUID cursor = null;
        AtomicInteger batch = new AtomicInteger();
        do {
            PaginationQueryParams paginationQueryParams = new PaginationQueryParams(cursor, batchSize);

            // We only need executed orders, as memoization happens post order execution so any order that is not executed will get memoized by itself later
            List<Order> executedOrders = orderGetService.getOrders(paginationQueryParams, Optional.of(OrderStatus.EXECUTED));

            List<Order> ordersToBackfill = getOrdersToBackfill(executedOrders);

            LOGGER.info("Backfilling batch - tenantId: {}, batch: {}, batch size: {}", tenantId, batch.incrementAndGet(), ordersToBackfill.size());

            for (Order order : ordersToBackfill) {
                try {
                    backfillMemoizedItems(tenantId, order.getOrderId(), Optional.of(order), isDryRun);
                } catch (Exception e) {
                    LOGGER.warn("!!! Memoization backfill - error backfilling memoized items for order, skipping: {}", order.getOrderId(), e);
                }
            }

            var paginationResponse = new PaginationResponseJson<>(executedOrders, batchSize, orderMapper::ordersToJson, Order::getId);
            cursor = paginationResponse.getNextCursor();
        } while (cursor != null && batch.get() < 20); // Safety net for long running job, batch size of 20 will process 400 orders max, job itself is stateless so no issues as such to retrigger for next batch
        LOGGER.info("* * * [COMPLETED] Backfilling memoized lines in bulk. tenantId: {} * * *", tenantId);
    }

    // This is an optimization layer to filter out orders that already have memoized lines and don't need to be backfilled
    // Orders with percent of charge lines need special handling as they would be partially memoized and need to be backfilled for only the non-memoized lines
    private List<Order> getOrdersToBackfill(List<Order> executedOrders) {
        List<String> executedOrderIds = executedOrders.stream().map(Order::getOrderId).toList();

        Set<String> orderIdsWithMemoizedItems = invoiceDAO.getOrderIdsWithMemoizedInvoiceItems(executedOrderIds);
        Set<Order> ordersWithMemoizedItems = executedOrders
            .stream()
            .filter(order -> orderIdsWithMemoizedItems.contains(order.getOrderId()))
            .collect(Collectors.toSet());
        Set<Order> ordersWithNoMemoizedItems = SetUtils.difference(new HashSet<>(executedOrders), ordersWithMemoizedItems);

        // Orders with percent of charge lines will most likely have partial memoization so we need to also backfill them
        Map<String, Charge> chargeMap = getChargeMap(ordersWithMemoizedItems.stream().toList());
        List<Order> percentOfChargeOrders = ordersWithMemoizedItems
            .stream()
            .filter(order ->
                order
                    .getLineItems()
                    .stream()
                    .anyMatch(orderLineItem -> chargeMap.get(orderLineItem.getChargeId()).getType() == ChargeType.PERCENTAGE_OF)
            )
            .toList();

        Set<Order> ordersToBackfill = new LinkedHashSet<>();
        ordersToBackfill.addAll(ordersWithNoMemoizedItems);
        ordersToBackfill.addAll(percentOfChargeOrders);

        return ordersToBackfill.stream().toList();
    }

    // This method will only backfill orders with either no existing memoized lines or percent of charge orders with existing memoized lines (excluding percent of charge order lines)
    public List<UUID> backfillMemoizedItems(String tenantId, String orderId, Optional<Order> orderParam, boolean isDryRun) {
        LOGGER.info("Memoization backfill - starting backfill for tenant : {}, order: {}", tenantId, orderId);
        Order order = orderParam.orElseGet(() -> orderGetService.getOrderByOrderId(orderId));
        if (Objects.isNull(order)) {
            throw new InvalidInputException("Order not found for order id: " + orderId);
        }
        Map<String, Charge> chargeMap = getChargeMap(List.of(order));

        Pair<BackfillOrderType, List<String>> backfillOrderTypeToMemoizedOrderLines = getBackfillOrderType(tenantId, order);
        BackfillOrderType backfillOrderType = backfillOrderTypeToMemoizedOrderLines.getLeft();

        if (backfillOrderType == BackfillOrderType.EXISTING_MEMOIZED_LINES) {
            LOGGER.info("Memoization backfill - skipping backfill for tenant : {}, order: {} as it already has memoized lines", tenantId, orderId);
            return Collections.emptyList();
        }

        Pair<List<MemoizedInvoiceLineItem>, InvoiceProcessorUtility.InvoiceItemComparisonResult> toMemoizeResult = getToMemoizeBackfill(
            order,
            chargeMap,
            backfillOrderType,
            isDryRun
        );
        List<MemoizedInvoiceLineItem> toMemoize = toMemoizeResult.getLeft();
        var orderLineToMemoizeLineComparisonResult = toMemoizeResult.getRight();

        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);

        List<UUID> memoizedLinesResult = new ArrayList<>();

        // Rollback the txn if any of the matches fail
        dslContext.transaction(configuration -> {
            DSLContext txnContext = DSL.using(configuration);

            // Memoize the preview lines in txn and also generate pre + post memoization
            // invoice items
            MemoizationResult memoizationResult = getMemoizationResult(
                txnContext,
                tenantId,
                order,
                chargeMap,
                toMemoize,
                backfillOrderTypeToMemoizedOrderLines.getRight(),
                orderLineToMemoizeLineComparisonResult
            );

            // This is just to be able to compare using InvoiceItem class
            List<InvoiceItem> memoizedInvoiceItemsForComparisonSorted = InvoiceProcessorUtility.convertMemoizedInvoiceLineItemsToInvoiceItems(
                order,
                memoizationResult.memoizedInvoiceLineItems()
            )
                .stream()
                .sorted(InvoiceProcessorUtility.INVOICE_ITEM_COMPARATOR)
                .toList();

            List<InvoiceItem> existingInvoiceItems = invoiceDAO.getInvoiceItemsForOrderId(orderId);
            List<InvoiceItem> existingInvoiceItemsSorted = InvoiceProcessorUtility.filterExistingInvoiceItemsForComparison(
                existingInvoiceItems,
                chargeMap
            );

            // Check if order has existing generated invoices
            if (CollectionUtils.isNotEmpty(existingInvoiceItemsSorted)) {
                InvoiceProcessorUtility.matchExistingInvoiceItemsWithMemoizedLines(
                    existingInvoiceItemsSorted,
                    memoizedInvoiceItemsForComparisonSorted,
                    order,
                    orderLineToMemoizeLineComparisonResult
                );
            } else {
                matchPostMemoizationGeneratedInvoicesWithMemoizedLines(memoizationResult, chargeMap, memoizedInvoiceItemsForComparisonSorted, order);
            }
            memoizedLinesResult.addAll(
                memoizationResult
                    .memoizedInvoiceLineItems()
                    .stream()
                    .filter(MemoizedInvoiceLineItem::getIsBackfilled)
                    .map(MemoizedInvoiceLineItem::getId)
                    .toList()
            );

            if (isDryRun) {
                LOGGER.info("Memoization backfill - skipping memoization for tenant : {}, order: {} as it is a dry run", tenantId, orderId);
                invoiceDAO.batchDeleteBackfilledOrderMemoizedItemsInTransaction(orderId, memoizedLinesResult, tenantId, txnContext);
            }

            LOGGER.info(
                "Memoization backfill - completed backfill for tenant : {}, order: {}, memoized lines ids {}",
                tenantId,
                orderId,
                memoizedLinesResult
            );
        });

        return memoizedLinesResult;
    }

    public void compareOrderLineToPreviewAmounts(String tenantId, String orderStatus) {
        LOGGER.info("* * * [STARTING] compareOrderLineToPreviewAmounts tenantId: {} * * *", tenantId);
        UUID cursor = null;
        int batchNumber = 0;
        int batchSize = 1000; // max pagination limit is 2000
        do {
            PaginationQueryParams paginationQueryParams = new PaginationQueryParams(cursor, batchSize);

            Optional<OrderStatus> orderStatusOptional = orderStatus != null ? Optional.of(OrderStatus.valueOf(orderStatus)) : Optional.empty();
            List<Order> orders = orderGetService.getOrders(paginationQueryParams, orderStatusOptional);

            LOGGER.info("compareOrderLineToPreviewAmounts batch - tenantId: {}, batch: {}, batch size: {}", tenantId, ++batchNumber, orders.size());

            for (Order order : orders) {
                try {
                    compareOrderLineToPreviewAmountsForOrder(order);
                } catch (Exception e) {
                    LOGGER.warn("!!! compareOrderLineToPreviewAmounts - error for order, skipping: {}", order.getOrderId(), e);
                }
            }

            var paginationResponse = new PaginationResponseJson<>(orders, batchSize, orderMapper::ordersToJson, Order::getId);
            cursor = paginationResponse.getNextCursor();
        } while (cursor != null && batchNumber < 50); // Safety net for long running jobs, in prod the max number of orders is ~ 39k for EP, this processes around 50k orders at max
        LOGGER.info("* * * [COMPLETED] compareOrderLineToPreviewAmounts tenantId: {}, batches: {} * * *", tenantId, batchNumber);
    }

    private static void matchPostMemoizationGeneratedInvoicesWithMemoizedLines(
        MemoizationResult memoizationResult,
        Map<String, Charge> chargeMap,
        List<InvoiceItem> memoizedInvoiceItemsForComparisonSorted,
        Order order
    ) {
        List<InvoiceItem> generatedInvoiceItemsPostMemoizationExcludingNonMemoizedCharges = memoizationResult
            .generatedInvoiceItemsPostMemoization()
            .stream()
            // filter out the charge types that are not memoized
            .filter(invoiceItem -> InvoiceProcessorUtility.filterChargeTypesForMemoization(invoiceItem.getChargeId(), chargeMap))
            .toList();

        // Match generated invoice items using memoization with memoized items
        InvoiceProcessorUtility.InvoiceItemComparisonResult generatedInvoiceItemsAgainstToMemoize =
            InvoiceProcessorUtility.logMemoizedInvoiceItemDifferences(
                memoizedInvoiceItemsForComparisonSorted,
                generatedInvoiceItemsPostMemoizationExcludingNonMemoizedCharges,
                "ToMemoizeBackfillAgainstGeneratedInvoiceItems",
                true,
                false
            );

        if (generatedInvoiceItemsAgainstToMemoize == InvoiceProcessorUtility.InvoiceItemComparisonResult.NO_MATCH) {
            throw new InvariantCheckFailedException(
                "Memoization backfill - generated invoice items do not match with memoized invoice items for order : " + order.getOrderId()
            );
        }
    }

    /**
     * This returns the in-memory memoized invoice lines and also the invoices generated with the corresponding memoized lines
     * @param txnContext
     * @param tenantId
     * @param order
     * @param chargeMap
     * @param toMemoize
     * @param existingMemoizedOrderLines
     * @param orderLineToMemoizeLineComparisonResult
     * @return MemoizationResult
     */
    private MemoizationResult getMemoizationResult(
        DSLContext txnContext,
        String tenantId,
        Order order,
        Map<String, Charge> chargeMap,
        List<MemoizedInvoiceLineItem> toMemoize,
        List<String> existingMemoizedOrderLines,
        InvoiceProcessorUtility.InvoiceItemComparisonResult orderLineToMemoizeLineComparisonResult
    ) {
        // Generate invoice items for the order without memoization with target date is order end date and including usage
        List<InvoiceItem> generatedInvoiceItemsPreMemoization = generateInvoiceLineItemsForMemoizationBackfill(
            order,
            order.getEndDate(),
            InvoiceChargeInclusionOption.INCLUDE_USAGE,
            chargeMap,
            null,
            Collections.emptyList()
        );

        invoiceDAO.memoizeInvoiceItemsForBackfill(
            txnContext,
            tenantId,
            order.getEntityId(),
            order.getExternalSubscriptionId(),
            order.getOrderId(),
            toMemoize,
            existingMemoizedOrderLines
        );

        //Fetch the memoizedInvoiceLines in the same txn
        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems = invoiceDAO.getMemoizedInvoiceItemsForOrderInBackfillingTransaction(
            txnContext,
            tenantId,
            order.getOrderId()
        );

        // Generate invoice items for the order with memoization with target date is order end date and including usage
        List<InvoiceItem> generatedInvoiceItemsPostMemoization = generateInvoiceLineItemsForMemoizationBackfill(
            order,
            order.getEndDate(),
            InvoiceChargeInclusionOption.INCLUDE_USAGE,
            chargeMap,
            generatedInvoiceItemsPreMemoization,
            memoizedInvoiceLineItems
        );

        // Now match pre vs post memoization generation for invoice items
        InvoiceProcessorUtility.InvoiceItemComparisonResult preMemoizationAgainstPostMemoization = InvoiceProcessorUtility.logInvoiceItemDifferences(
            generatedInvoiceItemsPreMemoization,
            generatedInvoiceItemsPostMemoization,
            "preMemoizationAgainstPostMemoization",
            // This is because we have adjusted the memoized lines amount artificially to match the order line amounts.
            // The invoices generated via runtime code will have the issue as preview, where the amount won't add up to the order line amounts, hence we skip the check
            orderLineToMemoizeLineComparisonResult == InvoiceProcessorUtility.InvoiceItemComparisonResult.NO_MATCH_ORDER_AND_PREVIEW_AMOUNTS
        );
        if (preMemoizationAgainstPostMemoization == InvoiceProcessorUtility.InvoiceItemComparisonResult.NO_MATCH) {
            throw new InvariantCheckFailedException(
                "Memoization backfill - generated invoice items do not match before and after memoization for order : " + order.getOrderId()
            );
        }
        return new MemoizationResult(memoizedInvoiceLineItems, generatedInvoiceItemsPostMemoization);
    }

    private record MemoizationResult(
        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems,
        List<InvoiceItem> generatedInvoiceItemsPostMemoization
    ) {}

    // Wrapper over preview invoice items + converting them to memoized lines, along with comparing them
    private Pair<List<MemoizedInvoiceLineItem>, InvoiceProcessorUtility.InvoiceItemComparisonResult> getToMemoizeBackfill(
        Order order,
        Map<String, Charge> chargeMap,
        BackfillOrderType backfillOrderType,
        boolean isDryRun
    ) {
        // NOTE - Important to have an immutable deep copy for order line amounts comparison, for some reason the order object is being mutated in preview function and thus
        // the order line amounts as well, which leads to differences in cents due to rounding/proration etc.
        List<OrderLineItem> lineItemsNetEffect = order
            .getLineItemsNetEffect()
            .stream()
            .map(OrderLineItem::new)
            .collect(Collectors.collectingAndThen(Collectors.toList(), Collections::unmodifiableList));

        // Generate toMemoize invoice items for the order, this is in memory and they are not persisted yet
        List<InvoiceItem> previewInvoiceItems = previewInvoiceItemsForOrderMemoizationBackfill(order, chargeMap);
        List<MemoizedInvoiceLineItem> toMemoize = getToMemoizeInvoiceLineItemsForMemoizationBackfill(previewInvoiceItems, order, chargeMap);

        InvoiceProcessorUtility.InvoiceItemComparisonResult comparisonResult = InvoiceProcessorUtility.logOrderAmountDifferencesWithMemoization(
            order,
            lineItemsNetEffect,
            toMemoize,
            previewInvoiceItems,
            chargeMap,
            featureService
        );
        if (comparisonResult == InvoiceProcessorUtility.InvoiceItemComparisonResult.NO_MATCH) {
            throw new InvariantCheckFailedException(
                "Memoization backfill - order line items do not match with to memoize invoice items for order : " + order.getOrderId()
            );
        }

        // If the order line items do not match with the preview invoice items, we need to adjust the memoized lines to match the order line items
        if (comparisonResult == InvoiceProcessorUtility.InvoiceItemComparisonResult.NO_MATCH_ORDER_AND_PREVIEW_AMOUNTS) {
            String orderId = order.getOrderId();
            List<InvoiceItem> existingInvoiceItems = invoiceDAO.getInvoiceItemsForOrderId(orderId);
            //This is just to be able to compare using InvoiceItem class
            List<InvoiceItem> memoizedInvoiceItemsForComparisonSorted = InvoiceProcessorUtility.convertMemoizedInvoiceLineItemsToInvoiceItems(
                order,
                toMemoize
            )
                .stream()
                .sorted(InvoiceProcessorUtility.INVOICE_ITEM_COMPARATOR)
                .toList();
            List<InvoiceItem> existingInvoiceItemsSorted = InvoiceProcessorUtility.filterExistingInvoiceItemsForComparison(
                existingInvoiceItems,
                chargeMap
            );

            Map<String, List<MemoizedInvoiceLineItem>> adjustedMemoizedLinesByOrderLine =
                InvoiceProcessorUtility.handleOrderLineAndMemoizedLineAmountDifference(
                    order,
                    lineItemsNetEffect,
                    toMemoize,
                    memoizedInvoiceItemsForComparisonSorted,
                    existingInvoiceItemsSorted
                );

            List<MemoizedInvoiceLineItem> unadjustedMemoizedLines = toMemoize
                .stream()
                .sorted(Comparator.comparing(MemoizedInvoiceLineItem::getOrderLineItemId))
                .filter(memoizedInvoiceLineItem -> !adjustedMemoizedLinesByOrderLine.containsKey(memoizedInvoiceLineItem.getOrderLineItemId()))
                .toList();

            List<MemoizedInvoiceLineItem> toMemoizeAdjusted = adjustedMemoizedLinesByOrderLine
                .values()
                .stream()
                .flatMap(List::stream)
                .sorted(Comparator.comparing(MemoizedInvoiceLineItem::getOrderLineItemId))
                .collect(Collectors.toList());
            toMemoizeAdjusted.addAll(unadjustedMemoizedLines);

            InvoiceProcessorUtility.validateAdjustedMemoizedLineAmountsMatchWithOrderLines(
                order,
                chargeMap,
                isDryRun,
                toMemoizeAdjusted,
                toMemoize,
                lineItemsNetEffect,
                previewInvoiceItems,
                featureService
            );

            toMemoize = toMemoizeAdjusted;

            LOGGER.info(
                "Memoization backfill - [getToMemoizeBackfill] Successfully adjusted toMemoize lines for order {}, isDryRun {}",
                order.getOrderId(),
                isDryRun
            );
        }

        // If the order is a percent of charge order with existing memoization, we only want to memoize the percent of order lines
        if (backfillOrderType == BackfillOrderType.PERCENT_OF_CHARGE_ORDER_WITH_NON_MEMOIZED_LINES) {
            LOGGER.info(
                "Memoization backfill - order {} is a percent of charge order with non memoized lines, returning only percent of order lines",
                order.getOrderId()
            );
            return Pair.of(
                toMemoize
                    .stream()
                    .filter(memoizedInvoiceLineItem -> chargeMap.get(memoizedInvoiceLineItem.getChargeId()).getType() == ChargeType.PERCENTAGE_OF)
                    .toList(),
                comparisonResult
            );
        }
        return Pair.of(toMemoize, comparisonResult);
    }

    // Returns the backfill order type based on existing memoized lines and the order lines memoized if any
    private Pair<BackfillOrderType, List<String>> getBackfillOrderType(String tenantId, Order order) {
        String orderId = order.getOrderId();

        List<String> chargeIds = order.getLineItemsNetEffect().stream().map(OrderLineItem::getChargeId).toList();
        Map<String, Charge> chargeMap = productCatalogGetService.getChargeMapByChargeIds(chargeIds);

        boolean isPercentOfChargeOrder = chargeMap.values().stream().anyMatch(charge -> charge.getType() == ChargeType.PERCENTAGE_OF);

        // Check if order has already generated memoized invoice items
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantIdProvider, dslContextProvider);
        Map<String, List<MemoizedInvoiceLineItem>> orderLineToExistingMemoizedLines = dslContext.transactionResult(configuration -> {
            DSLContext txnContext = DSL.using(configuration);
            return invoiceDAO
                .getMemoizedInvoiceItemsForOrderInBackfillingTransaction(txnContext, tenantId, orderId)
                .stream()
                .collect(Collectors.groupingBy(MemoizedInvoiceLineItem::getOrderLineItemId));
        });

        if (MapUtils.isEmpty(orderLineToExistingMemoizedLines)) {
            return Pair.of(BackfillOrderType.NO_EXISTING_MEMOIZED_LINES, Collections.emptyList());
        }

        List<String> existingMemoizedOrderLines = orderLineToExistingMemoizedLines.keySet().stream().toList();
        if (isPercentOfChargeOrder) {
            List<OrderLineItem> percentOfChargeOrderLines = order
                .getLineItemsNetEffect()
                .stream()
                .filter(orderLineItem -> chargeMap.get(orderLineItem.getChargeId()).getType() == ChargeType.PERCENTAGE_OF)
                .toList();
            boolean percentOfChargeFullyMemoized = percentOfChargeOrderLines
                .stream()
                .allMatch(percentOfOrderLine -> orderLineToExistingMemoizedLines.containsKey(percentOfOrderLine.getOrderLineId()));
            // If percent of charge lines are memoized then no backfill is needed, if percent of charge lines are not memoized then only backfill those lines
            return percentOfChargeFullyMemoized
                ? Pair.of(BackfillOrderType.EXISTING_MEMOIZED_LINES, existingMemoizedOrderLines)
                : Pair.of(BackfillOrderType.PERCENT_OF_CHARGE_ORDER_WITH_NON_MEMOIZED_LINES, existingMemoizedOrderLines);
        }

        return Pair.of(BackfillOrderType.EXISTING_MEMOIZED_LINES, existingMemoizedOrderLines);
    }

    // Wrapper over preview invoice items
    private List<InvoiceItem> previewInvoiceItemsForOrderMemoizationBackfill(Order order, Map<String, Charge> chargeMap) {
        LOGGER.info("Memoization backfill - previewing invoice items for order: {}", order.getOrderId());
        if (StringUtils.isBlank(order.getExternalSubscriptionId())) {
            throw new ServiceFailureException(
                String.format("subscription id missing in order while memoizing invoice data for order: %s", order.getOrderId())
            );
        }

        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);

        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        return previewInvoiceItemsForOrder(
            order,
            DO_NOT_SKIP_PERCENT_OF,
            !INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION,
            prorationConfig,
            timeZone,
            chargeMap
        );
    }

    // Wrapper over memoizeInvoiceDataForOrder
    private List<MemoizedInvoiceLineItem> getToMemoizeInvoiceLineItemsForMemoizationBackfill(
        List<InvoiceItem> memoizationCandidates,
        Order order,
        Map<String, Charge> chargeMap
    ) {
        LOGGER.info("Memoization backfill - getting toMemoize invoice items for order: {}", order.getOrderId());
        List<MemoizedInvoiceLineItem> toMemoize = memoizationCandidates
            .stream()
            // usage and event based invoice items are not memoized as of now
            .filter(invoiceItem -> InvoiceProcessorUtility.filterChargeTypesForMemoization(invoiceItem.getChargeId(), chargeMap))
            .map(item -> {
                Charge charge = chargeMap.get(item.getChargeId());
                if (charge == null) {
                    throw new InvariantCheckFailedException(
                        String.format("Charge %s for corresponding invoice line item not found in charge map", item.getChargeId())
                    );
                }
                if (charge.getType() == ChargeType.PERCENTAGE_OF) {
                    LOGGER.info(
                        "Memoization backfill - memoizing percent of charge for order line {} and charge id {}, with period {} - {}, and amounts listUnit : {}, sellUnit : {}",
                        item.getOrderLineItemId(),
                        item.getChargeId(),
                        item.getPeriodStartDate(),
                        item.getPeriodEndDate(),
                        item.getListUnitPrice(),
                        item.getSellUnitPrice()
                    );
                }
                return MemoizedInvoiceLineItem.from(order.getExternalSubscriptionId(), item, charge.getBillingTerm());
            })
            .toList();
        LOGGER.info(
            "Memoization backfill - toMemoize order line items for order: {} are {}",
            order.getOrderId(),
            toMemoize.stream().map(MemoizedInvoiceLineItem::getOrderLineItemId).toList()
        );
        return toMemoize;
    }

    // Wrapper over getInvoiceItems, this is a subset of the method generateInvoice
    private List<InvoiceItem> generateInvoiceLineItemsForMemoizationBackfill(
        Order order,
        Instant invoiceTargetDate,
        InvoiceChargeInclusionOption invoiceChargeInclusionOption,
        Map<String, Charge> chargeMap,
        List<InvoiceItem> preMemoizationInvoiceItems,
        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems
    ) {
        if (StringUtils.isBlank(order.getExternalSubscriptionId()) || invoiceTargetDate == null) {
            throw new IllegalArgumentException("subscriptionId and invoiceTargetDate are required");
        }

        Subscription subscription = subscriptionGetService.getSubscription(order.getExternalSubscriptionId());
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        var orders = List.of(order);

        if (invoiceTargetDate.isBefore(subscription.getStartDate())) {
            // if target date is before subscription start date, skip and return empty
            return Collections.emptyList();
        }

        // if the target date is past subscription end date, generate for whole subscription
        if (invoiceTargetDate.isAfter(subscription.getEndDate())) {
            invoiceTargetDate = subscription.getEndDate();
        }

        List<InvoiceItem> invoiceItems = getInvoiceItems(
            invoiceTargetDate,
            subscription,
            timeZone,
            orders,
            chargeMap,
            invoiceChargeInclusionOption,
            memoizedInvoiceLineItems,
            true
        )
            .stream()
            .sorted(InvoiceProcessorUtility.INVOICE_ITEM_COMPARATOR)
            .toList();

        if (preMemoizationInvoiceItems == null) {
            return invoiceItems;
        }

        // Memoized lines don't store discount percent, this leads to the value being set as null in the generated invoice items.
        // However, generated invoices via code seem to set the discount percent from calculated invoice amounts.
        // Thus, when doing a validation match this gives a false negative by comparing the runtime value with null.
        // To avoid this, we set the discount percent for memoized lines as the same as runtime value.
        return IntStream.range(0, invoiceItems.size())
            .mapToObj(index -> {
                InvoiceItem preMemoizationInvoiceItem = preMemoizationInvoiceItems.get(index);
                InvoiceItem generatedInvoiceItem = invoiceItems.get(index);
                return new InvoiceItem.InvoiceItemBuilder()
                    .from(generatedInvoiceItem)
                    .discountPercent(preMemoizationInvoiceItem.getDiscountPercent())
                    .createInvoiceItem();
            })
            .toList();
    }

    private void compareOrderLineToPreviewAmountsForOrder(Order order) {
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);
        TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();
        Map<String, Charge> chargeMap = getChargeMap(List.of(order));
        List<InvoiceItem> invoiceItems = previewInvoiceItemsForOrder(
            order,
            DO_NOT_SKIP_PERCENT_OF,
            !INVOICE_PREVIEW_FOR_ORDER_LINE_CALCULATION,
            prorationConfig,
            timeZone,
            chargeMap
        );

        BigDecimal orderLineTotal = order.getLineItemsNetEffect().stream().map(OrderLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal invoiceItemsTotal = invoiceItems.stream().map(InvoiceItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (!Numbers.equals(orderLineTotal, invoiceItemsTotal)) {
            LOGGER.warn(
                String.format(
                    "Order line total %s does not match with preview invoice item total %s for order %s",
                    orderLineTotal,
                    invoiceItemsTotal,
                    order.getOrderId()
                )
            );
        }

        Map<String, BigDecimal> invoiceItemAmountsByOrderLineId = invoiceItems
            .stream()
            .collect(
                Collectors.groupingBy(InvoiceItem::getOrderLineItemId, Collectors.reducing(BigDecimal.ZERO, InvoiceItem::getAmount, BigDecimal::add))
            );

        for (OrderLineItem orderLineItem : order.getLineItemsNetEffect()) {
            BigDecimal invoiceItemAmount = invoiceItemAmountsByOrderLineId.getOrDefault(orderLineItem.getOrderLineId(), BigDecimal.ZERO);
            if (orderLineItem.getAmount().compareTo(invoiceItemAmount) != 0) {
                LOGGER.warn(
                    String.format(
                        "Order line amount %s does not match with preview invoice item amount %s for order line id %s, list amount %s, discount amount %s",
                        orderLineItem.getAmount(),
                        invoiceItemAmount,
                        orderLineItem.getOrderLineId(),
                        orderLineItem.getListAmount(),
                        orderLineItem.getDiscountAmount()
                    )
                );
            }
        }
    }
}
