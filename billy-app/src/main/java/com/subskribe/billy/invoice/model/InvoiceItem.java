package com.subskribe.billy.invoice.model;

import com.subskribe.billy.invoice.RoundedAmountCalculator;
import com.subskribe.billy.invoice.service.processor.InvoiceProcessorUtility.EqualsResultWithField;
import com.subskribe.billy.productcatalog.model.TaxRate;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;

public class InvoiceItem {

    private final Id id;
    private final String entityId;

    // todo: backfill so this value is no longer nullable
    @Nullable
    private final String planId;

    private final String chargeId;
    private final String subscriptionChargeId;
    private final String subscriptionChargeGroupId;
    private final String orderId;
    private final String orderLineItemId;
    private final String invoiceLineNumber;
    private final InvoiceStatus status;
    private final BigDecimal listAmountBeforeOverride;
    private final BigDecimal listAmount;
    private final BigDecimal discountAmount;
    private final BigDecimal discountPercent;
    private final BigDecimal amount;
    private final BigDecimal listUnitPriceBeforeOverride;
    private final BigDecimal listUnitPrice;
    private final BigDecimal sellUnitPrice;
    private final Long quantity;
    private final Long drawdownQuantityUsed;
    private final Long drawdownQuantityRemaining;
    private final BigDecimal taxAmount;
    private final TaxRate taxRate;
    private final Invoice.InternalId invoiceId;
    private final Instant periodStartDate;
    private final Instant periodEndDate;
    private final List<DiscountDetail> inlineDiscounts;
    private final List<TenantDiscountLineItem> predefinedDiscounts;
    private final BigDecimal functionalListAmount;
    private final BigDecimal functionalDiscountAmount;
    private final BigDecimal functionalAmount;
    private final BigDecimal functionalTaxAmount;
    private Boolean isBilled;
    private Instant triggerOn;

    public InvoiceItem(
        Id id,
        String entityId,
        String planId,
        String chargeId,
        String orderId,
        String orderLineItemId,
        String invoiceLineNumber,
        InvoiceStatus status,
        String subscriptionChargeId,
        String subscriptionChargeGroupId,
        BigDecimal listAmountBeforeOverride,
        BigDecimal listAmount,
        BigDecimal discountAmount,
        BigDecimal discountPercent,
        BigDecimal amount,
        BigDecimal listUnitPriceBeforeOverride,
        BigDecimal listUnitPrice,
        BigDecimal sellUnitPrice,
        Long quantity,
        Long drawdownQuantityUsed,
        Long drawdownQuantityRemaining,
        List<DiscountDetail> inlineDiscounts,
        List<TenantDiscountLineItem> predefinedDiscounts,
        BigDecimal taxAmount,
        TaxRate taxRate,
        Invoice.InternalId invoiceId,
        Instant periodStartDate,
        Instant periodEndDate,
        BigDecimal functionalListAmount,
        BigDecimal functionalDiscountAmount,
        BigDecimal functionalAmount,
        BigDecimal functionalTaxAmount,
        Boolean isBilled,
        Instant triggerOn
    ) {
        this.id = id;
        this.entityId = entityId;
        this.planId = planId;
        this.chargeId = chargeId;
        this.orderId = orderId;
        this.orderLineItemId = orderLineItemId;
        this.subscriptionChargeId = subscriptionChargeId;
        this.subscriptionChargeGroupId = subscriptionChargeGroupId;
        this.listAmountBeforeOverride = listAmountBeforeOverride;
        this.listAmount = listAmount;
        this.discountAmount = discountAmount;
        this.discountPercent = discountPercent;
        this.amount = Numbers.makeCurrencyScale(amount);
        this.listUnitPriceBeforeOverride = listUnitPriceBeforeOverride;
        this.listUnitPrice = listUnitPrice;
        this.sellUnitPrice = sellUnitPrice;
        this.quantity = quantity;
        this.drawdownQuantityUsed = drawdownQuantityUsed;
        this.drawdownQuantityRemaining = drawdownQuantityRemaining;
        this.inlineDiscounts = inlineDiscounts;
        this.predefinedDiscounts = predefinedDiscounts;
        this.taxAmount = Numbers.makeCurrencyScale(taxAmount);
        this.taxRate = taxRate;
        this.invoiceId = invoiceId;
        this.periodStartDate = periodStartDate;
        this.periodEndDate = periodEndDate;
        this.invoiceLineNumber = invoiceLineNumber;
        this.status = status;
        this.functionalListAmount = functionalListAmount;
        this.functionalDiscountAmount = functionalDiscountAmount;
        this.functionalAmount = functionalAmount;
        this.functionalTaxAmount = functionalTaxAmount;
        this.isBilled = isBilled;
        this.triggerOn = triggerOn;
    }

    public Id getId() {
        return id;
    }

    public String getEntityId() {
        return entityId;
    }

    public String getPlanId() {
        return planId;
    }

    public String getChargeId() {
        return chargeId;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getOrderLineItemId() {
        return orderLineItemId;
    }

    public String getInvoiceLineNumber() {
        return invoiceLineNumber;
    }

    public InvoiceStatus getStatus() {
        return status;
    }

    public String getSubscriptionChargeId() {
        return subscriptionChargeId;
    }

    public String getSubscriptionChargeGroupId() {
        return subscriptionChargeGroupId;
    }

    public BigDecimal getListAmountBeforeOverride() {
        return listAmountBeforeOverride;
    }

    public BigDecimal getListAmount() {
        return listAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public BigDecimal getDiscountPercent() {
        return discountPercent;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public BigDecimal getListUnitPriceBeforeOverride() {
        return listUnitPriceBeforeOverride;
    }

    public BigDecimal getListUnitPrice() {
        return listUnitPrice;
    }

    public BigDecimal getSellUnitPrice() {
        return sellUnitPrice;
    }

    public Long getQuantity() {
        return quantity;
    }

    public Long getDrawdownQuantityUsed() {
        return drawdownQuantityUsed;
    }

    public Long getDrawdownQuantityRemaining() {
        return drawdownQuantityRemaining;
    }

    public List<DiscountDetail> getInlineDiscounts() {
        return inlineDiscounts;
    }

    public List<TenantDiscountLineItem> getPredefinedDiscounts() {
        return predefinedDiscounts;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public TaxRate getTaxRate() {
        return taxRate;
    }

    public Invoice.InternalId getInvoiceId() {
        return invoiceId;
    }

    public Instant getPeriodStartDate() {
        return periodStartDate;
    }

    public Instant getPeriodEndDate() {
        return periodEndDate;
    }

    public BigDecimal getFunctionalListAmount() {
        return functionalListAmount;
    }

    public BigDecimal getFunctionalDiscountAmount() {
        return functionalDiscountAmount;
    }

    public BigDecimal getFunctionalAmount() {
        return functionalAmount;
    }

    public BigDecimal getFunctionalTaxAmount() {
        return functionalTaxAmount;
    }

    public Boolean getIsBilled() {
        return isBilled;
    }

    public void setIsBilled(Boolean isBilled) {
        this.isBilled = isBilled;
    }

    public Instant getTriggerOn() {
        return triggerOn;
    }

    public void setTriggerOn(Instant triggerOn) {
        this.triggerOn = triggerOn;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InvoiceItem that = (InvoiceItem) o;
        return (
            Objects.equals(id, that.id) &&
            Objects.equals(entityId, that.entityId) &&
            Objects.equals(chargeId, that.chargeId) &&
            Objects.equals(subscriptionChargeId, that.subscriptionChargeId) &&
            Objects.equals(subscriptionChargeGroupId, that.subscriptionChargeGroupId) &&
            Objects.equals(orderId, that.orderId) &&
            Objects.equals(orderLineItemId, that.orderLineItemId) &&
            Objects.equals(invoiceLineNumber, that.invoiceLineNumber) &&
            status == that.status &&
            Numbers.equals(listAmountBeforeOverride, that.listAmountBeforeOverride) &&
            Numbers.equals(listAmount, that.listAmount) &&
            Numbers.equals(discountAmount, that.discountAmount) &&
            Numbers.equals(discountPercent, that.discountPercent) &&
            Numbers.equals(amount, that.amount) &&
            Numbers.equals(listUnitPriceBeforeOverride, that.listUnitPriceBeforeOverride) &&
            Numbers.equals(listUnitPrice, that.listUnitPrice) &&
            Numbers.equals(sellUnitPrice, that.sellUnitPrice) &&
            Objects.equals(quantity, that.quantity) &&
            Numbers.equals(taxAmount, that.taxAmount) &&
            Objects.equals(taxRate, that.taxRate) &&
            Objects.equals(invoiceId, that.invoiceId) &&
            Objects.equals(periodStartDate, that.periodStartDate) &&
            Objects.equals(periodEndDate, that.periodEndDate) &&
            Objects.equals(inlineDiscounts, that.inlineDiscounts) &&
            Objects.equals(predefinedDiscounts, that.predefinedDiscounts) &&
            Numbers.equals(functionalListAmount, that.functionalListAmount) &&
            Numbers.equals(functionalDiscountAmount, that.functionalDiscountAmount) &&
            Numbers.equals(functionalAmount, that.functionalAmount) &&
            Numbers.equals(functionalTaxAmount, that.functionalTaxAmount) &&
            Objects.equals(isBilled, that.isBilled)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            id,
            entityId,
            chargeId,
            subscriptionChargeId,
            subscriptionChargeGroupId,
            orderId,
            orderLineItemId,
            invoiceLineNumber,
            status,
            listAmountBeforeOverride,
            listAmount,
            discountAmount,
            discountPercent,
            amount,
            listUnitPriceBeforeOverride,
            listUnitPrice,
            sellUnitPrice,
            quantity,
            taxAmount,
            taxRate,
            invoiceId,
            periodStartDate,
            periodEndDate,
            inlineDiscounts,
            predefinedDiscounts,
            functionalListAmount,
            functionalDiscountAmount,
            functionalAmount,
            functionalTaxAmount,
            isBilled
        );
    }

    public static class InvoiceItemBuilder {

        private InvoiceItem.Id id;
        private String entityId;
        private String planId;
        private String chargeId;
        private String orderId;
        private String orderLineItemId;
        private String invoiceLineNumber;

        private InvoiceStatus status;
        private String subscriptionChargeId;
        private String subscriptionChargeGroupId;
        private BigDecimal listAmountBeforeOverride;
        private BigDecimal listAmount;
        private BigDecimal discountAmount;
        private BigDecimal discountPercent;
        private BigDecimal amount;
        private BigDecimal listUnitPriceBeforeOverride;
        private BigDecimal listUnitPrice;
        private BigDecimal sellUnitPrice;
        private Long quantity;
        private Long drawdownQuantityUsed;
        private Long drawdownQuantityRemaining;
        private List<DiscountDetail> inlineDiscounts;
        private List<TenantDiscountLineItem> predefinedDiscounts;
        private BigDecimal taxAmount;
        private TaxRate taxRate;
        private Invoice.InternalId invoiceId;
        private Instant periodStartDate;
        private Instant periodEndDate;
        private BigDecimal functionalListAmount;
        private BigDecimal functionalDiscountAmount;
        private BigDecimal functionalAmount;
        private BigDecimal functionalTaxAmount;
        private Boolean isBilled;
        private Instant triggerOn;

        public static InvoiceItemBuilder builder() {
            return new InvoiceItemBuilder();
        }

        public InvoiceItemBuilder from(InvoiceItem invoiceItem) {
            id = invoiceItem.getId();
            entityId = invoiceItem.getEntityId();
            planId = invoiceItem.getPlanId();
            chargeId = invoiceItem.getChargeId();
            subscriptionChargeId = invoiceItem.getSubscriptionChargeId();
            subscriptionChargeGroupId = invoiceItem.getSubscriptionChargeGroupId();
            orderId = invoiceItem.getOrderId();
            orderLineItemId = invoiceItem.getOrderLineItemId();
            invoiceLineNumber = invoiceItem.getInvoiceLineNumber();
            status = invoiceItem.getStatus();
            listAmountBeforeOverride = invoiceItem.getListAmountBeforeOverride();
            listAmount = invoiceItem.getListAmount();
            discountAmount = invoiceItem.getDiscountAmount();
            discountPercent = invoiceItem.getDiscountPercent();
            amount = invoiceItem.getAmount();
            listUnitPriceBeforeOverride = invoiceItem.getListUnitPriceBeforeOverride();
            listUnitPrice = invoiceItem.getListUnitPrice();
            sellUnitPrice = invoiceItem.getSellUnitPrice();
            quantity = invoiceItem.getQuantity();
            drawdownQuantityUsed = invoiceItem.getDrawdownQuantityUsed();
            drawdownQuantityRemaining = invoiceItem.getDrawdownQuantityRemaining();
            inlineDiscounts = invoiceItem.getInlineDiscounts();
            predefinedDiscounts = invoiceItem.getPredefinedDiscounts();
            taxAmount = invoiceItem.getTaxAmount();
            taxRate = invoiceItem.getTaxRate();
            invoiceId = invoiceItem.getInvoiceId();
            periodStartDate = invoiceItem.getPeriodStartDate();
            periodEndDate = invoiceItem.getPeriodEndDate();
            functionalListAmount = invoiceItem.getFunctionalListAmount();
            functionalDiscountAmount = invoiceItem.getFunctionalDiscountAmount();
            functionalAmount = invoiceItem.getFunctionalAmount();
            functionalTaxAmount = invoiceItem.getFunctionalTaxAmount();
            isBilled = invoiceItem.getIsBilled();
            triggerOn = invoiceItem.getTriggerOn();
            return this;
        }

        public InvoiceItemBuilder id(InvoiceItem.Id id) {
            this.id = id;
            return this;
        }

        public InvoiceItemBuilder entityId(String entityId) {
            this.entityId = entityId;
            return this;
        }

        public InvoiceItemBuilder planId(String planId) {
            this.planId = planId;
            return this;
        }

        public InvoiceItemBuilder chargeId(String chargeId) {
            this.chargeId = chargeId;
            return this;
        }

        public InvoiceItemBuilder subscriptionChargeId(String subscriptionChargeId) {
            this.subscriptionChargeId = subscriptionChargeId;
            return this;
        }

        public InvoiceItemBuilder subscriptionChargeGroupId(String subscriptionChargeGroupId) {
            this.subscriptionChargeGroupId = subscriptionChargeGroupId;
            return this;
        }

        public InvoiceItemBuilder orderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public InvoiceItemBuilder orderLineItemId(String orderLineItemId) {
            this.orderLineItemId = orderLineItemId;
            return this;
        }

        public InvoiceItemBuilder invoiceLineNumber(String invoiceLineNumber) {
            this.invoiceLineNumber = invoiceLineNumber;
            return this;
        }

        public InvoiceItemBuilder status(InvoiceStatus status) {
            this.status = status;
            return this;
        }

        public InvoiceItemBuilder listAmountBeforeOverride(BigDecimal listAmountBeforeOverride) {
            this.listAmountBeforeOverride = listAmountBeforeOverride;
            return this;
        }

        public InvoiceItemBuilder listAmount(BigDecimal listAmount) {
            this.listAmount = listAmount;
            return this;
        }

        public InvoiceItemBuilder discountAmount(BigDecimal discountAmount) {
            this.discountAmount = discountAmount;
            return this;
        }

        public InvoiceItemBuilder discountPercent(BigDecimal discountPercent) {
            this.discountPercent = discountPercent;
            return this;
        }

        public InvoiceItemBuilder amount(BigDecimal amount) {
            this.amount = amount;
            return this;
        }

        public InvoiceItemBuilder listUnitPriceBeforeOverride(BigDecimal listUnitPriceBeforeOverride) {
            this.listUnitPriceBeforeOverride = listUnitPriceBeforeOverride;
            return this;
        }

        public InvoiceItemBuilder listUnitPrice(BigDecimal listUnitPrice) {
            this.listUnitPrice = listUnitPrice;
            return this;
        }

        public InvoiceItemBuilder sellUnitPrice(BigDecimal sellUnitPrice) {
            this.sellUnitPrice = sellUnitPrice;
            return this;
        }

        public InvoiceItemBuilder quantity(Long quantity) {
            this.quantity = quantity;
            return this;
        }

        public InvoiceItemBuilder drawdownQuantityUsed(Long drawdownQuantityUsed) {
            this.drawdownQuantityUsed = drawdownQuantityUsed;
            return this;
        }

        public InvoiceItemBuilder drawdownQuantityRemaining(Long drawdownQuantityRemaining) {
            this.drawdownQuantityRemaining = drawdownQuantityRemaining;
            return this;
        }

        public InvoiceItemBuilder inlineDiscounts(List<DiscountDetail> inlineDiscounts) {
            this.inlineDiscounts = inlineDiscounts;
            return this;
        }

        public InvoiceItemBuilder predefinedDiscounts(List<TenantDiscountLineItem> predefinedDiscounts) {
            this.predefinedDiscounts = predefinedDiscounts;
            return this;
        }

        public InvoiceItemBuilder taxAmount(BigDecimal taxAmount) {
            this.taxAmount = taxAmount;
            return this;
        }

        public InvoiceItemBuilder taxRate(TaxRate taxRate) {
            this.taxRate = taxRate;
            return this;
        }

        public InvoiceItemBuilder invoiceId(Invoice.InternalId invoiceId) {
            this.invoiceId = invoiceId;
            return this;
        }

        public InvoiceItemBuilder periodStartDate(Instant periodStartDate) {
            this.periodStartDate = periodStartDate;
            return this;
        }

        public InvoiceItemBuilder periodEndDate(Instant periodEndDate) {
            this.periodEndDate = periodEndDate;
            return this;
        }

        public InvoiceItemBuilder functionalListAmount(BigDecimal functionalListAmount) {
            this.functionalListAmount = functionalListAmount;
            return this;
        }

        public InvoiceItemBuilder functionalDiscountAmount(BigDecimal functionalDiscountAmount) {
            this.functionalDiscountAmount = functionalDiscountAmount;
            return this;
        }

        public InvoiceItemBuilder functionalAmount(BigDecimal functionalAmount) {
            this.functionalAmount = functionalAmount;
            return this;
        }

        public InvoiceItemBuilder functionalTaxAmount(BigDecimal functionalTaxAmount) {
            this.functionalTaxAmount = functionalTaxAmount;
            return this;
        }

        public InvoiceItemBuilder isBilled(Boolean isBilled) {
            this.isBilled = isBilled;
            return this;
        }

        public InvoiceItemBuilder triggerOn(Instant triggerOn) {
            this.triggerOn = triggerOn;
            return this;
        }

        @Deprecated
        public InvoiceItemBuilder triggerOn(BillingTerm billingTerm, Instant startDate, Instant endDate) {
            triggerOn = billingTerm == BillingTerm.UP_FRONT ? startDate : endDate;
            return this;
        }

        public InvoiceItemBuilder triggerOn(BillingTerm billingTerm, Instant startDate, Instant endDate, InvoiceBillingInfo invoiceBillingInfo) {
            startDate = getTriggerOnStart(startDate, invoiceBillingInfo);
            triggerOn = billingTerm == BillingTerm.UP_FRONT ? startDate : endDate;
            return this;
        }

        private static Instant getTriggerOnStart(Instant billingPeriodStart, InvoiceBillingInfo invoiceBillingInfo) {
            if (invoiceBillingInfo.getOrderStart().isEmpty()) {
                return billingPeriodStart;
            }
            Instant orderStart = invoiceBillingInfo.getOrderStart().get();
            return billingPeriodStart.compareTo(orderStart) <= 0 ? orderStart : billingPeriodStart;
        }

        public InvoiceItem createInvoiceItem() {
            return new InvoiceItem(
                id,
                entityId,
                planId,
                chargeId,
                orderId,
                orderLineItemId,
                invoiceLineNumber,
                status,
                subscriptionChargeId,
                subscriptionChargeGroupId,
                listAmountBeforeOverride,
                listAmount,
                discountAmount,
                discountPercent,
                amount,
                listUnitPriceBeforeOverride,
                listUnitPrice,
                sellUnitPrice,
                quantity,
                drawdownQuantityUsed,
                drawdownQuantityRemaining,
                inlineDiscounts,
                predefinedDiscounts,
                taxAmount,
                taxRate,
                invoiceId,
                periodStartDate,
                periodEndDate,
                functionalListAmount,
                functionalDiscountAmount,
                functionalAmount,
                functionalTaxAmount,
                isBilled,
                triggerOn
            );
        }
    }

    public record Id(String id) {}

    /**
     * Only for logging purposes, consider carefully for functional checks. This method uses Numbers.equals instead of Objects.equals for BigDecimals
     * And also doesn't check for certain fields like lineNumber, taxRate, discountPercent, listAmountBeforeOverride, listUnitPriceBeforeOverride which don't get persisted for memoized line items
     * @param o InvoiceItem to compare with
     * @return boolean
     */
    @SuppressFBWarnings({ "NP_NULL_ON_SOME_PATH" })
    public EqualsResultWithField equalsMemoizedItems(InvoiceItem o) {
        if (this == o) return new EqualsResultWithField(true, null, null, null);
        if (o == null || getClass() != o.getClass()) return new EqualsResultWithField(false, "class", this.getClass(), o.getClass());
        InvoiceItem that = o;

        if (!Objects.equals(id, that.id)) return new EqualsResultWithField(false, "id", id, that.id);
        if (!Objects.equals(entityId, that.entityId)) return new EqualsResultWithField(false, "entityId", entityId, that.entityId);
        if (!Objects.equals(chargeId, that.chargeId)) return new EqualsResultWithField(false, "chargeId", chargeId, that.chargeId);
        if (!Objects.equals(subscriptionChargeId, that.subscriptionChargeId)) return new EqualsResultWithField(
            false,
            "subscriptionChargeId",
            subscriptionChargeId,
            that.subscriptionChargeId
        );
        if (!Objects.equals(subscriptionChargeGroupId, that.subscriptionChargeGroupId)) return new EqualsResultWithField(
            false,
            "subscriptionChargeGroupId",
            subscriptionChargeGroupId,
            that.subscriptionChargeGroupId
        );
        if (!Objects.equals(orderId, that.orderId)) return new EqualsResultWithField(false, "orderId", orderId, that.orderId);
        if (!Objects.equals(orderLineItemId, that.orderLineItemId)) return new EqualsResultWithField(
            false,
            "orderLineItemId",
            orderLineItemId,
            that.orderLineItemId
        );
        if (status != that.status) return new EqualsResultWithField(false, "status", status, that.status);
        if (!Numbers.equals(listAmount, that.listAmount)) return new EqualsResultWithField(false, "listAmount", listAmount, that.listAmount);
        if (!Numbers.equals(discountAmount, that.discountAmount)) return new EqualsResultWithField(
            false,
            "discountAmount",
            discountAmount,
            that.discountAmount
        );
        if (!Numbers.equals(amount, that.amount)) return new EqualsResultWithField(false, "amount", amount, that.amount);
        if (!Numbers.equals(listUnitPrice, that.listUnitPrice)) return new EqualsResultWithField(
            false,
            "listUnitPrice",
            listUnitPrice,
            that.listUnitPrice
        );
        if (!Numbers.equals(sellUnitPrice, that.sellUnitPrice)) return new EqualsResultWithField(
            false,
            "sellUnitPrice",
            sellUnitPrice,
            that.sellUnitPrice
        );
        if (!Objects.equals(quantity, that.quantity)) return new EqualsResultWithField(false, "quantity", quantity, that.quantity);
        if (!Numbers.equals(taxAmount, that.taxAmount)) return new EqualsResultWithField(false, "taxAmount", taxAmount, that.taxAmount);
        if (!Objects.equals(invoiceId, that.invoiceId)) return new EqualsResultWithField(false, "invoiceId", invoiceId, that.invoiceId);
        if (!Objects.equals(periodStartDate, that.periodStartDate)) return new EqualsResultWithField(
            false,
            "periodStartDate",
            periodStartDate,
            that.periodStartDate
        );
        if (!Objects.equals(periodEndDate, that.periodEndDate)) return new EqualsResultWithField(
            false,
            "periodEndDate",
            periodEndDate,
            that.periodEndDate
        );
        // TODO : temporarily disabling this check to unblock backfill as there is a bug in the read path of memoization invoice <> invoice item conversion
        //        if (!Objects.equals(inlineDiscounts, that.inlineDiscounts)) return new EqualsResultWithField(
        //            false,
        //            "inlineDiscounts",
        //            inlineDiscounts,
        //            that.inlineDiscounts
        //        );
        //        if (!Objects.equals(predefinedDiscounts, that.predefinedDiscounts)) return new EqualsResultWithField(
        //            false,
        //            "predefinedDiscounts",
        //            predefinedDiscounts,
        //            that.predefinedDiscounts
        //        );
        if (!Numbers.equals(functionalListAmount, that.functionalListAmount)) return new EqualsResultWithField(
            false,
            "functionalListAmount",
            functionalListAmount,
            that.functionalListAmount
        );
        if (!Numbers.equals(functionalDiscountAmount, that.functionalDiscountAmount)) return new EqualsResultWithField(
            false,
            "functionalDiscountAmount",
            functionalDiscountAmount,
            that.functionalDiscountAmount
        );
        if (!Numbers.equals(functionalAmount, that.functionalAmount)) return new EqualsResultWithField(
            false,
            "functionalAmount",
            functionalAmount,
            that.functionalAmount
        );
        if (!Numbers.equals(functionalTaxAmount, that.functionalTaxAmount)) return new EqualsResultWithField(
            false,
            "functionalTaxAmount",
            functionalTaxAmount,
            that.functionalTaxAmount
        );
        if (!Objects.equals(isBilled, that.isBilled)) return new EqualsResultWithField(false, "isBilled", isBilled, that.isBilled);

        return new EqualsResultWithField(true, null, null, null);
    }

    /**
     * Only for logging purposes, consider carefully for functional checks. This method uses Numbers.equals instead of Objects.equals for BigDecimals
     * And also doesn't check for certain fields like lineNumber, taxRate, discountPercent, listAmountBeforeOverride which don't get persisted for memoized line items
     * ALSO - for already generated invoices we have to exclude id,invoiceId (won't exist in runtime), status (will be PAID vs DRAFT in runtime)
     * @param o Generated InvoiceItem to compare with
     * @return boolean
     */
    @SuppressFBWarnings({ "NP_NULL_ON_SOME_PATH" })
    public EqualsResultWithField equalsGeneratedInvoice(InvoiceItem o, boolean skipAmountCheck) {
        if (this == o) return new EqualsResultWithField(true, null, null, null);
        if (o == null || getClass() != o.getClass()) return new EqualsResultWithField(false, "class", this.getClass(), o.getClass());
        InvoiceItem that = o;

        if (!Objects.equals(entityId, that.entityId)) return new EqualsResultWithField(false, "entityId", entityId, that.entityId);
        if (!Objects.equals(chargeId, that.chargeId)) return new EqualsResultWithField(false, "chargeId", chargeId, that.chargeId);
        if (!Objects.equals(subscriptionChargeId, that.subscriptionChargeId)) return new EqualsResultWithField(
            false,
            "subscriptionChargeId",
            subscriptionChargeId,
            that.subscriptionChargeId
        );
        if (!Objects.equals(subscriptionChargeGroupId, that.subscriptionChargeGroupId)) return new EqualsResultWithField(
            false,
            "subscriptionChargeGroupId",
            subscriptionChargeGroupId,
            that.subscriptionChargeGroupId
        );
        if (!Objects.equals(orderId, that.orderId)) return new EqualsResultWithField(false, "orderId", orderId, that.orderId);
        if (!Objects.equals(orderLineItemId, that.orderLineItemId)) return new EqualsResultWithField(
            false,
            "orderLineItemId",
            orderLineItemId,
            that.orderLineItemId
        );
        // Invoice items in runtime have rounded amounts calculated for them which is not the case for generated invoices, leading to false negative matches
        ItemAmounts generatedInvoiceRoundedAmounts = RoundedAmountCalculator.getRoundedAmounts(that.listAmount, that.amount);
        if (!Numbers.equals(listAmount, generatedInvoiceRoundedAmounts.listAmount())) return new EqualsResultWithField(
            false,
            "listAmount",
            listAmount,
            generatedInvoiceRoundedAmounts.listAmount()
        );
        if (!Numbers.equals(discountAmount, generatedInvoiceRoundedAmounts.discountAmount()) && !skipAmountCheck) return new EqualsResultWithField(
            false,
            "discountAmount",
            discountAmount,
            generatedInvoiceRoundedAmounts.discountAmount()
        );
        if (!Numbers.equals(amount, generatedInvoiceRoundedAmounts.sellAmount()) && !skipAmountCheck) return new EqualsResultWithField(
            false,
            "amount",
            amount,
            generatedInvoiceRoundedAmounts.sellAmount()
        );
        if (!Numbers.equals(listUnitPriceBeforeOverride, that.listUnitPriceBeforeOverride)) return new EqualsResultWithField(
            false,
            "listUnitPriceBeforeOverride",
            listUnitPriceBeforeOverride,
            that.listUnitPriceBeforeOverride
        );
        // TODO: Check why unit prices are varying during runtime only for percent of charge, although total amounts are equal so ignoring for now, also generated invoice unit prices seem fine
        // if (!Numbers.equals(listUnitPrice, that.listUnitPrice)) return new EqualsResultWithField(false, "listUnitPrice", listUnitPrice, that.listUnitPrice);
        // if (!Numbers.equals(sellUnitPrice, that.sellUnitPrice)) return new EqualsResultWithField(false, "sellUnitPrice", sellUnitPrice, that.sellUnitPrice);
        if (!Objects.equals(quantity, that.quantity)) return new EqualsResultWithField(false, "quantity", quantity, that.quantity);
        if (!Objects.equals(periodStartDate, that.periodStartDate)) return new EqualsResultWithField(
            false,
            "periodStartDate",
            periodStartDate,
            that.periodStartDate
        );
        if (!Objects.equals(periodEndDate, that.periodEndDate)) return new EqualsResultWithField(
            false,
            "periodEndDate",
            periodEndDate,
            that.periodEndDate
        );
        // FUNCTIONAL AMOUNTS ARE NOT SET IN MEMOIZED INVOICE LINE ITEMS SO IT GIVES A FALSE NEGATIVE MATCH
        if (
            (functionalListAmount != null && that.functionalListAmount != null) && !Numbers.equals(functionalListAmount, that.functionalListAmount)
        ) return new EqualsResultWithField(false, "functionalListAmount", functionalListAmount, that.functionalListAmount);
        if (
            (functionalDiscountAmount != null && that.functionalDiscountAmount != null) &&
            !Numbers.equals(functionalDiscountAmount, that.functionalDiscountAmount)
        ) return new EqualsResultWithField(false, "functionalDiscountAmount", functionalDiscountAmount, that.functionalDiscountAmount);
        if (
            (functionalAmount != null && that.functionalAmount != null) && !Numbers.equals(functionalAmount, that.functionalAmount)
        ) return new EqualsResultWithField(false, "functionalAmount", functionalAmount, that.functionalAmount);
        if (!Objects.equals(isBilled, that.isBilled)) return new EqualsResultWithField(false, "isBilled", isBilled, that.isBilled);

        return new EqualsResultWithField(true, null, null, null);
    }

    public boolean equalsWithFieldContext(Object o, List<EqualsResultWithField> results, boolean skipAmountCheck) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) {
            results.add(new EqualsResultWithField(false, "class", this.getClass(), o != null ? o.getClass() : null));
            return false;
        }
        InvoiceItem that = (InvoiceItem) o;
        boolean isEqual = true;

        if (!Objects.equals(id, that.id)) {
            results.add(new EqualsResultWithField(false, "id", id, that.id));
            isEqual = false;
        }
        if (!Objects.equals(entityId, that.entityId)) {
            results.add(new EqualsResultWithField(false, "entityId", entityId, that.entityId));
            isEqual = false;
        }
        if (!Objects.equals(chargeId, that.chargeId)) {
            results.add(new EqualsResultWithField(false, "chargeId", chargeId, that.chargeId));
            isEqual = false;
        }
        if (!Objects.equals(subscriptionChargeId, that.subscriptionChargeId)) {
            results.add(new EqualsResultWithField(false, "subscriptionChargeId", subscriptionChargeId, that.subscriptionChargeId));
            isEqual = false;
        }
        if (!Objects.equals(subscriptionChargeGroupId, that.subscriptionChargeGroupId)) {
            results.add(new EqualsResultWithField(false, "subscriptionChargeGroupId", subscriptionChargeGroupId, that.subscriptionChargeGroupId));
            isEqual = false;
        }
        if (!Objects.equals(orderId, that.orderId)) {
            results.add(new EqualsResultWithField(false, "orderId", orderId, that.orderId));
            isEqual = false;
        }
        if (!Objects.equals(orderLineItemId, that.orderLineItemId)) {
            results.add(new EqualsResultWithField(false, "orderLineItemId", orderLineItemId, that.orderLineItemId));
            isEqual = false;
        }
        if (!Objects.equals(invoiceLineNumber, that.invoiceLineNumber)) {
            results.add(new EqualsResultWithField(false, "invoiceLineNumber", invoiceLineNumber, that.invoiceLineNumber));
            isEqual = false;
        }
        if (status != that.status) {
            results.add(new EqualsResultWithField(false, "status", status, that.status));
            isEqual = false;
        }
        if (
            (listAmountBeforeOverride != null && that.listAmountBeforeOverride != null) &&
            !Numbers.equals(listAmountBeforeOverride, that.listAmountBeforeOverride)
        ) {
            results.add(new EqualsResultWithField(false, "listAmountBeforeOverride", listAmountBeforeOverride, that.listAmountBeforeOverride));
            isEqual = false;
        }
        if (!Numbers.equals(listAmount, that.listAmount)) {
            results.add(new EqualsResultWithField(false, "listAmount", listAmount, that.listAmount));
            isEqual = false;
        }
        if (!Numbers.equals(discountAmount, that.discountAmount) && !skipAmountCheck) {
            results.add(new EqualsResultWithField(false, "discountAmount", discountAmount, that.discountAmount));
            isEqual = false;
        }
        if (!Numbers.equals(discountPercent, that.discountPercent)) {
            results.add(new EqualsResultWithField(false, "discountPercent", discountPercent, that.discountPercent));
            isEqual = false;
        }
        if (!Numbers.equals(amount, that.amount) && !skipAmountCheck) {
            results.add(new EqualsResultWithField(false, "amount", amount, that.amount));
            isEqual = false;
        }
        if (
            (listUnitPriceBeforeOverride != null && that.listUnitPriceBeforeOverride != null) &&
            !Numbers.equals(listUnitPriceBeforeOverride, that.listUnitPriceBeforeOverride)
        ) {
            results.add(
                new EqualsResultWithField(false, "listUnitPriceBeforeOverride", listUnitPriceBeforeOverride, that.listUnitPriceBeforeOverride)
            );
            isEqual = false;
        }
        if (!Numbers.equals(listUnitPrice, that.listUnitPrice)) {
            results.add(new EqualsResultWithField(false, "listUnitPrice", listUnitPrice, that.listUnitPrice));
            isEqual = false;
        }
        if (!Numbers.equals(sellUnitPrice, that.sellUnitPrice)) {
            results.add(new EqualsResultWithField(false, "sellUnitPrice", sellUnitPrice, that.sellUnitPrice));
            isEqual = false;
        }
        if (!Objects.equals(quantity, that.quantity)) {
            results.add(new EqualsResultWithField(false, "quantity", quantity, that.quantity));
            isEqual = false;
        }
        if (!Numbers.equals(taxAmount, that.taxAmount)) {
            results.add(new EqualsResultWithField(false, "taxAmount", taxAmount, that.taxAmount));
            isEqual = false;
        }
        if (!Objects.equals(taxRate, that.taxRate)) {
            results.add(new EqualsResultWithField(false, "taxRate", taxRate, that.taxRate));
            isEqual = false;
        }
        if (!Objects.equals(invoiceId, that.invoiceId)) {
            results.add(new EqualsResultWithField(false, "invoiceId", invoiceId, that.invoiceId));
            isEqual = false;
        }
        if (!Objects.equals(periodStartDate, that.periodStartDate)) {
            results.add(new EqualsResultWithField(false, "periodStartDate", periodStartDate, that.periodStartDate));
            isEqual = false;
        }
        if (!Objects.equals(periodEndDate, that.periodEndDate)) {
            results.add(new EqualsResultWithField(false, "periodEndDate", periodEndDate, that.periodEndDate));
            isEqual = false;
        }
        if (!Objects.equals(inlineDiscounts, that.inlineDiscounts)) {
            results.add(new EqualsResultWithField(false, "inlineDiscounts", inlineDiscounts, that.inlineDiscounts));
            isEqual = false;
        }
        if (!Objects.equals(predefinedDiscounts, that.predefinedDiscounts)) {
            results.add(new EqualsResultWithField(false, "predefinedDiscounts", predefinedDiscounts, that.predefinedDiscounts));
            isEqual = false;
        }
        if (!Numbers.equals(functionalListAmount, that.functionalListAmount)) {
            results.add(new EqualsResultWithField(false, "functionalListAmount", functionalListAmount, that.functionalListAmount));
            isEqual = false;
        }
        if (!Numbers.equals(functionalDiscountAmount, that.functionalDiscountAmount)) {
            results.add(new EqualsResultWithField(false, "functionalDiscountAmount", functionalDiscountAmount, that.functionalDiscountAmount));
            isEqual = false;
        }
        if (!Numbers.equals(functionalAmount, that.functionalAmount)) {
            results.add(new EqualsResultWithField(false, "functionalAmount", functionalAmount, that.functionalAmount));
            isEqual = false;
        }
        if (!Numbers.equals(functionalTaxAmount, that.functionalTaxAmount)) {
            results.add(new EqualsResultWithField(false, "functionalTaxAmount", functionalTaxAmount, that.functionalTaxAmount));
            isEqual = false;
        }
        if (!Objects.equals(isBilled, that.isBilled)) {
            results.add(new EqualsResultWithField(false, "isBilled", isBilled, that.isBilled));
            isEqual = false;
        }
        return isEqual;
    }
}
