package com.subskribe.billy.invoice.document;

import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.shared.document.TemplateCalculations;
import com.subskribe.billy.shared.document.TemplateCharge;
import com.subskribe.billy.shared.document.TemplateMathFunctions;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import com.subskribe.billy.shared.temporal.Period;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import org.apache.commons.lang3.StringUtils;

public class InvoiceTemplateLineItem {

    private final InvoiceItem lineItem;

    private final List<InvoiceItem> rawLineItems;

    private final DocumentRenderFormatter formatter;

    private final String currencyCode;

    private final ProductJson productJson;

    private final InvoiceTemplateMapUsage invoiceTemplateMapUsage;

    private final Map<String, Metrics> orderLineMetrics;

    private final Map<String, OrderLineItem> orderLineItems;

    private final TemplateCharge charge;

    private final PlanJson plan;

    private final ChargeJson chargeJson;

    public InvoiceTemplateLineItem(
        InvoiceItem lineItem,
        List<InvoiceItem> rawLineItems,
        DocumentRenderFormatter formatter,
        String currencyCode,
        ProductJson productJson,
        InvoiceTemplateMapUsage invoiceTemplateMapUsage,
        Map<String, Metrics> orderLineMetrics,
        Map<String, OrderLineItem> orderLineItems,
        TemplateCharge charge,
        PlanJson plan,
        ChargeJson chargeJson
    ) {
        this.lineItem = lineItem;
        this.rawLineItems = rawLineItems;
        this.formatter = formatter;
        this.currencyCode = currencyCode;
        this.productJson = productJson;
        this.invoiceTemplateMapUsage = invoiceTemplateMapUsage;
        this.orderLineMetrics = orderLineMetrics;
        this.orderLineItems = orderLineItems;
        this.charge = charge;
        this.chargeJson = chargeJson;
        this.plan = plan;
    }

    public String getProductName() {
        return productJson.getName();
    }

    public String getPlanName() {
        return plan.getName();
    }

    public String getPlanDisplayName() {
        return StringUtils.isBlank(plan.getDisplayName()) ? plan.getName() : plan.getDisplayName();
    }

    public String getChargeName() {
        return charge.getName();
    }

    public String getChargeDisplayName() {
        return StringUtils.isBlank(chargeJson.getDisplayName()) ? chargeJson.getName() : chargeJson.getDisplayName();
    }

    public String getQuantity() {
        return formatter.numberFormat(lineItem.getQuantity());
    }

    public boolean hasMultipleQuantity() {
        return lineItem.getQuantity() > 1;
    }

    // return the average unit price of the line item to the given precision
    // this is useful when the charge model isn't linear (e.g. block / tiered pricing)
    // Example: {{#averageUnitPrice}}2{{/averageUnitPrice}}
    public Function<String, String> getAverageUnitPrice() {
        return input -> {
            int fractionalDigits = TemplateMathFunctions.parseInt(input, 2);
            BigDecimal amount = lineItem.getAmount();
            Long quantity = lineItem.getQuantity();

            if (amount == null || quantity == null || quantity == 0) {
                return StringUtils.EMPTY;
            }

            return formatter.currencyFormat(Numbers.scaledDivide(amount, BigDecimal.valueOf(quantity)), currencyCode, fractionalDigits);
        };
    }

    public Function<String, String> getRoundedUnitPrice() {
        return input -> formatter.currencyFormat(lineItem.getSellUnitPrice(), currencyCode, input);
    }

    public String getUnitPrice() {
        return formatter.currencyFormat(lineItem.getSellUnitPrice(), currencyCode, 5);
    }

    public String getListAmount() {
        return formatter.currencyFormat(lineItem.getListAmount(), currencyCode);
    }

    public String getDiscountAmount() {
        return formatter.currencyFormat(lineItem.getDiscountAmount(), currencyCode);
    }

    public String getTaxAmount() {
        return formatter.currencyFormat(lineItem.getTaxAmount(), currencyCode);
    }

    public String getAmount() {
        BigDecimal taxAmount = Optional.ofNullable(lineItem.getTaxAmount()).orElse(BigDecimal.ZERO);
        return formatter.currencyFormat(lineItem.getAmount().add(taxAmount), currencyCode);
    }

    public String getSubtotal() {
        return getPreTaxAmount();
    }

    public String getPreTaxAmount() {
        return formatter.currencyFormat(lineItem.getAmount(), currencyCode);
    }

    public BigDecimal getRawAmount() {
        return lineItem.getAmount();
    }

    public String getStartDate() {
        return formatter.dateFormat(lineItem.getPeriodStartDate());
    }

    public Long getRawStartDate() {
        return lineItem.getPeriodStartDate().getEpochSecond();
    }

    public String getEndDate() {
        return formatter.endDateFormat(lineItem.getPeriodEndDate());
    }

    public Long getRawEndDate() {
        return lineItem.getPeriodEndDate().getEpochSecond();
    }

    public String getTermLengthInYears() {
        return Period.getPeriodInYearsString(lineItem.getPeriodStartDate(), lineItem.getPeriodEndDate(), formatter.getTimeZone());
    }

    public String getTermLengthInMonths() {
        return Period.getPeriodInMonthsString(lineItem.getPeriodStartDate(), lineItem.getPeriodEndDate(), formatter.getTimeZone());
    }

    public String getUnitOfMeasure() {
        return formatter.formatUnitOfMeasure(getCharge().getUnitOfMeasure());
    }

    public Map<String, Boolean> getChargeType() {
        return Map.of(chargeJson.getType().toString(), true);
    }

    public boolean getIsRecurring() {
        return chargeJson.getType().isRecurring();
    }

    public boolean getIsUsage() {
        return chargeJson.getType() == ChargeType.USAGE;
    }

    public boolean getIsRenewable() {
        return chargeJson.getIsRenewable();
    }

    public boolean getIsRecurringAndRenewable() {
        return getIsRecurring() && getIsRenewable();
    }

    public Map<String, Boolean> getChargeRecurrence() {
        if (chargeJson.getRecurrence() != null) {
            return Map.of(chargeJson.getRecurrence().getCycle().getPeriodicityName(), true);
        }

        return Map.of();
    }

    public String getPlanDescription() {
        return Objects.requireNonNullElse(plan.getDescription(), StringUtils.EMPTY);
    }

    public String getChargeDescription() {
        return Objects.requireNonNullElse(chargeJson.getDescription(), StringUtils.EMPTY);
    }

    public InvoiceTemplateMapUsage getInvoiceTemplateMapUsage() {
        return invoiceTemplateMapUsage;
    }

    InvoiceItem getLineItem() {
        return lineItem;
    }

    PlanJson getPlan() {
        return plan;
    }

    public boolean hasQuantity() {
        return lineItem.getQuantity() != null && lineItem.getQuantity() != 0;
    }

    public boolean hasAmount() {
        return lineItem.getAmount().compareTo(BigDecimal.ZERO) != 0;
    }

    public boolean isDiscountLine() {
        return chargeJson.getIsDiscount();
    }

    public String getMonthlyAmount() {
        return getYearlyAmountValue()
            .map(yearlyAmount -> formatter.currencyFormat(TemplateCalculations.yearlyToMonthlyAmount(yearlyAmount), currencyCode))
            .orElse(StringUtils.EMPTY);
    }

    public String getYearlyAmount() {
        return getYearlyAmountValue().map(yearlyAmount -> formatter.currencyFormat(yearlyAmount, currencyCode)).orElse(StringUtils.EMPTY);
    }

    private Optional<BigDecimal> getYearlyAmountValue() {
        BigDecimal yearlyAmount = BigDecimal.ZERO;

        for (var item : rawLineItems) {
            String orderLineItemId = item.getOrderLineItemId();
            Metrics metrics = orderLineMetrics.get(orderLineItemId);
            if (metrics != null) {
                yearlyAmount = yearlyAmount.add(metrics.getEntryArr());
            }
        }

        return Optional.of(yearlyAmount);
    }

    public String getTotalLineValue() {
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (var item : rawLineItems) {
            String orderLineItemId = item.getOrderLineItemId();
            Metrics metrics = orderLineMetrics.get(orderLineItemId);
            if (metrics != null) {
                totalAmount = totalAmount.add(metrics.getTcv());
            }
        }
        return formatter.currencyFormat(totalAmount, currencyCode);
    }

    public TemplateCharge getCharge() {
        return charge;
    }

    public Map<String, Boolean> getChargeModel() {
        return Map.of(chargeJson.getChargeModel().name(), true);
    }

    public Map<String, Boolean> getAction() {
        OrderLineItem orderLineItem = orderLineItems.get(lineItem.getOrderLineItemId());

        if (orderLineItem == null) {
            return Map.of();
        }

        return Map.of(orderLineItem.getAction().name(), true);
    }

    public boolean isAggregatedLineItem() {
        return rawLineItems.size() > 1;
    }

    // check if all raw line items have the same unit price
    public boolean hasConsistentUnitPrice() {
        return rawLineItems.stream().map(InvoiceItem::getSellUnitPrice).distinct().count() == 1;
    }

    public Integer getInvoiceLineNumber() {
        String lineNumber = lineItem.getInvoiceLineNumber();
        // if line number is not set or is not an int, return max int to sort at the bottom
        return Numbers.tryParseInt(lineNumber).orElse(Integer.MAX_VALUE);
    }

    public ChargeJson getChargeJson() {
        return chargeJson;
    }
}
