package com.subskribe.billy.invoice.model;

import com.subskribe.billy.account.model.AccountStub;
import java.util.Optional;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class InvoiceGenerationCheckResult {

    private final boolean invoiceToBeGenerated;

    private final Optional<Invoice> existingDraftInvoice;

    private final String subscriptionId;

    private final AccountStub account;

    private InvoiceGenerationCheckResult(Builder builder) {
        invoiceToBeGenerated = builder.invoiceToBeGenerated;
        existingDraftInvoice = builder.existingDraftInvoice;
        subscriptionId = builder.subscriptionId;
        account = builder.account;
    }

    public static Builder builder() {
        return new Builder();
    }

    public boolean isInvoiceToBeGenerated() {
        return invoiceToBeGenerated;
    }

    public Optional<Invoice> getExistingDraftInvoice() {
        return existingDraftInvoice;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public AccountStub getAccount() {
        return account;
    }

    public static class Builder {

        private boolean invoiceToBeGenerated;
        private Optional<Invoice> existingDraftInvoice = Optional.empty();
        private String subscriptionId;
        private AccountStub account;

        private Builder() {}

        public Builder invoiceToBeGenerated(boolean invoiceToBeGenerated) {
            this.invoiceToBeGenerated = invoiceToBeGenerated;
            return this;
        }

        public Builder existingDraftInvoice(Optional<Invoice> existingDraftInvoice) {
            this.existingDraftInvoice = existingDraftInvoice;
            return this;
        }

        public Builder subscriptionId(String subscriptionId) {
            this.subscriptionId = subscriptionId;
            return this;
        }

        public Builder account(AccountStub account) {
            this.account = account;
            return this;
        }

        public InvoiceGenerationCheckResult build() {
            return new InvoiceGenerationCheckResult(this);
        }
    }
}
