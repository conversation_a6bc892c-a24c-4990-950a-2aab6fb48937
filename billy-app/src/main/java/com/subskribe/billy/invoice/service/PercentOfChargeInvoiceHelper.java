package com.subskribe.billy.invoice.service;

import com.google.common.collect.Sets;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.ProrationCalculator;
import com.subskribe.billy.invoice.model.DiscountResult;
import com.subskribe.billy.invoice.model.ImmutableInvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.processor.model.ImmutableInvoicePreviewInput;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderCustomBillingService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.enums.PercentDerivedFrom;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Currency;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.BiPredicate;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;

public class PercentOfChargeInvoiceHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(PercentOfChargeInvoiceHelper.class);

    private static final BigDecimal HUNDRED = BigDecimal.valueOf(100L);

    private static final BiPredicate<OrderLineItem, String> DE_BOOK_PREDICATE = (lineItem, targetChargeId) ->
        lineItem.getChargeId().equals(targetChargeId) && lineItem.getQuantity() < 0;
    private static final BiPredicate<OrderLineItem, String> RE_BOOK_PREDICATE = (lineItem, targetChargeId) ->
        lineItem.getChargeId().equals(targetChargeId) && lineItem.getQuantity() >= 0;

    private static final Predicate<List<Plan>> PERCENT_OF_PERCENT_PREDICATE = plans -> plans.stream().anyMatch(Plan::hasPercentageOfCharge);
    private static final Predicate<List<Plan>> PERCENT_OF_PREDICATE = plans -> !PERCENT_OF_PERCENT_PREDICATE.test(plans);

    private final ProductCatalogGetService productCatalogGetService;
    private final TenantSettingService tenantSettingService;
    private final FeatureService featureService;
    private final ProrationConfigurationGetService prorationConfigurationGetService;
    private final DiscountCalculator discountCalculator;

    @Inject
    public PercentOfChargeInvoiceHelper(
        ProductCatalogGetService productCatalogGetService,
        TenantSettingService tenantSettingService,
        FeatureService featureService,
        ProrationConfigurationGetService prorationConfigurationGetService,
        DiscountCalculator discountCalculator
    ) {
        this.productCatalogGetService = productCatalogGetService;
        this.tenantSettingService = tenantSettingService;
        this.featureService = featureService;
        this.prorationConfigurationGetService = prorationConfigurationGetService;
        this.discountCalculator = discountCalculator;
    }

    public List<InvoiceItem> previewPercentOfInvoiceItems(
        Order order,
        ImmutableInvoicePreviewInput previewInput,
        List<InvoiceItem> percentOfTargetInvoiceItems,
        boolean skipAmendPercentOf
    ) {
        Validator.checkNonNullInternal(order, "passed in order cannot be null here");

        if (CollectionUtils.isEmpty(order.getLineItemsNetEffect())) {
            return List.of();
        }

        switch (order.getOrderType()) {
            case AMENDMENT -> {
                if (skipAmendPercentOf) {
                    return List.of();
                } else {
                    return processAmendmentOrder(order, previewInput, percentOfTargetInvoiceItems);
                }
            }
            case NEW, CANCEL, RENEWAL, RESTRUCTURE -> {
                return processNonAmendmentOrder(order, previewInput, percentOfTargetInvoiceItems);
            }
            default -> {
                String message = String.format("unknown order type %s while processing percent of invoice items preview", order.getOrderType());
                throw new ServiceFailureException(message);
            }
        }
    }

    Optional<InvoiceItem> getPercentOfInvoiceItemForBillingPeriod(
        InvoiceBillingInfo billingInfo,
        BillingPeriod billingPeriod,
        OrderLineItem orderLineItem,
        Charge charge,
        Boolean isInvoiceGenerationFlow,
        List<DiscountDetail> inlineDiscounts,
        List<TenantDiscountLineItem> predefinedDiscounts
    ) {
        Validator.checkStateInternal(
            charge.getType() == ChargeType.PERCENTAGE_OF,
            "Charge argument for percent of invoice generation not percent of type"
        );
        Validator.checkStateInternal(
            orderLineItem.getChargeId().equals(charge.getChargeId()),
            "Order line item charge id does not match required charge id"
        );

        Period lineItemPeriod = Period.between(orderLineItem.getEffectiveDate(), orderLineItem.getEndDate());
        Period effectiveBillingPeriod = Period.between(billingPeriod.getStart(), billingPeriod.getEnd());

        if (Period.overlapOf(lineItemPeriod, effectiveBillingPeriod).isEmpty()) {
            return Optional.empty();
        }

        BigDecimal prorationRatio = ProrationCalculator.getPeriodProrationRatio(
            lineItemPeriod,
            effectiveBillingPeriod,
            billingInfo.getProrationConfig(),
            billingInfo.getTimeZone(),
            Optional.ofNullable(billingInfo.getBillingCycle())
        );

        BigDecimal sellAmountForPeriod = Numbers.makeCurrencyScale(
            orderLineItem.getAmount().multiply(prorationRatio),
            billingInfo.getCurrency().getCurrencyCode()
        );
        BigDecimal listAmountForPeriod = Numbers.makeCurrencyScale(
            orderLineItem.getListAmount().multiply(prorationRatio),
            billingInfo.getCurrencyCode()
        );

        // NOTE: For percentOfCharge unit prices, we are prorating during invoice preview which gets stored in order lines
        // so we should not re-prorate them during invoice generation, this was leading to a redivided amount for unit prices
        BigDecimal listUnitPrice = isInvoiceGenerationFlow
            ? orderLineItem.getListUnitPrice()
            : orderLineItem.getListUnitPrice().multiply(prorationRatio);
        BigDecimal sellUnitPrice = isInvoiceGenerationFlow
            ? orderLineItem.getSellUnitPrice()
            : orderLineItem.getSellUnitPrice().multiply(prorationRatio);
        BigDecimal listAmountBeforeOverride = Optional.ofNullable(orderLineItem.getListAmountBeforeOverride())
            .map(amount -> amount.multiply(prorationRatio))
            .map(Numbers::makeCurrencyScale)
            .orElse(null);
        BigDecimal listUnitPriceBeforeOverride = Optional.ofNullable(orderLineItem.getListUnitPriceBeforeOverride())
            .map(amount -> amount.multiply(prorationRatio))
            .map(Numbers::makeCurrencyScale)
            .orElse(null);

        List<DiscountDetail> periodDiscountDetails = new ArrayList<>();
        for (DiscountDetail discountDetail : inlineDiscounts) {
            BigDecimal discountAmount = Numbers.makeCurrencyScale(
                discountDetail.getAmount().multiply(prorationRatio),
                billingInfo.getCurrency().getCurrencyCode()
            );
            periodDiscountDetails.add(DiscountCalculator.getLineItemDiscountDetailWithAmount(discountDetail, discountAmount));
        }

        List<TenantDiscountLineItem> periodPredefinedDiscounts = new ArrayList<>();
        for (TenantDiscountLineItem discountLineItem : predefinedDiscounts) {
            BigDecimal discountAmount = Numbers.makeCurrencyScale(
                discountLineItem.getAmount().multiply(prorationRatio),
                billingInfo.getCurrency().getCurrencyCode()
            );
            periodPredefinedDiscounts.add(DiscountCalculator.getTenantDiscountWithDiscountAmount(discountLineItem, discountAmount));
        }

        InvoiceItemAmounts periodInvoiceAmounts = ImmutableInvoiceItemAmounts.builder()
            .sellAmount(sellAmountForPeriod)
            .listAmount(listAmountForPeriod)
            .listAmountBeforeOverride(listAmountBeforeOverride)
            .discountAmount(listAmountForPeriod.subtract(sellAmountForPeriod))
            .listUnitPrice(Numbers.makeCurrencyScale(listUnitPrice, billingInfo.getCurrencyCode()))
            .listUnitPriceBeforeOverride(listUnitPriceBeforeOverride)
            .sellUnitPrice(Numbers.makeCurrencyScale(sellUnitPrice, billingInfo.getCurrencyCode()))
            .build();

        InvoiceItem.InvoiceItemBuilder invoiceItemBuilder = new InvoiceItem.InvoiceItemBuilder()
            .entityId(orderLineItem.getEntityId())
            .orderId(orderLineItem.getOrderId())
            .orderLineItemId(orderLineItem.getOrderLineId())
            .chargeId(orderLineItem.getChargeId())
            .subscriptionChargeId(orderLineItem.getExternalSubscriptionChargeId())
            .subscriptionChargeGroupId(orderLineItem.getSubscriptionChargeGroupId())
            .status(InvoiceStatus.DRAFT)
            .listAmount(periodInvoiceAmounts.getListAmount())
            .listAmountBeforeOverride(periodInvoiceAmounts.getListAmountBeforeOverride())
            .discountAmount(periodInvoiceAmounts.getDiscountAmount())
            .amount(periodInvoiceAmounts.getSellAmount())
            .listUnitPrice(periodInvoiceAmounts.getListUnitPrice())
            .listUnitPriceBeforeOverride(periodInvoiceAmounts.getListUnitPriceBeforeOverride())
            .sellUnitPrice(periodInvoiceAmounts.getSellUnitPrice())
            .taxAmount(BigDecimal.ZERO)
            .quantity(orderLineItem.getQuantity())
            .inlineDiscounts(periodDiscountDetails)
            .predefinedDiscounts(periodPredefinedDiscounts)
            .periodStartDate(effectiveBillingPeriod.getStart())
            .periodEndDate(effectiveBillingPeriod.getEnd())
            .triggerOn(charge.getBillingTerm(), billingPeriod.getStart(), billingPeriod.getEnd());
        return Optional.of(invoiceItemBuilder.createInvoiceItem());
    }

    private List<InvoiceItem> processAmendmentOrder(
        Order order,
        ImmutableInvoicePreviewInput previewInput,
        List<InvoiceItem> percentOfTargetInvoiceItems
    ) {
        List<InvoiceItem> percentOfInvoiceItems = getAmendInvoiceItems(order, previewInput, percentOfTargetInvoiceItems, PERCENT_OF_PREDICATE);
        List<InvoiceItem> targetInvoiceItemsForPercentOfPercent = Stream.of(percentOfTargetInvoiceItems, percentOfInvoiceItems)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
        List<InvoiceItem> percentOfPercentInvoiceItems = getAmendInvoiceItems(
            order,
            previewInput,
            targetInvoiceItemsForPercentOfPercent,
            PERCENT_OF_PERCENT_PREDICATE
        );
        // combine percent of invoice items and percent of percent invoice items and return
        return Stream.of(percentOfInvoiceItems, percentOfPercentInvoiceItems).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<InvoiceItem> getAmendInvoiceItems(
        Order order,
        ImmutableInvoicePreviewInput previewInput,
        List<InvoiceItem> targetInvoiceItems,
        Predicate<List<Plan>> targetPlansPredicate
    ) {
        Map<String, Charge> chargeMap = previewInput.getChargeMap();
        Map<String, Set<String>> percentChargeIdToTargetPlanIds = percentChargeIdToRelatedPlanIdsMap(chargeMap, targetPlansPredicate);

        // if there are no percent of charges we need not bother doing anything
        if (MapUtils.isEmpty(percentChargeIdToTargetPlanIds)) {
            return List.of();
        }

        // collect all plan ids of the amended line items excluding the percent of line items themselves
        Set<String> affectedPercentChargeIds = getAffectedPercentChargeIds(order, percentChargeIdToTargetPlanIds);

        List<InvoiceItem> allPercentOfInvoiceItems = new ArrayList<>();

        ProcessingHelper helper = ProcessingHelper.builder()
            .targetInvoiceItems(targetInvoiceItems)
            .affectedPercentChargeIds(affectedPercentChargeIds)
            .chargeMap(chargeMap)
            .percentChargeIdToTargetPlanIds(percentChargeIdToTargetPlanIds)
            .tenantSettingService(tenantSettingService)
            .featureService(featureService)
            .prorationConfigurationGetService(prorationConfigurationGetService)
            .discountCalculator(discountCalculator)
            .build();

        allPercentOfInvoiceItems.addAll(helper.getPercentOfInvoiceItemsDeBooked(order, previewInput));
        allPercentOfInvoiceItems.addAll(helper.getPercentOfInvoiceItemsReBooked(order, previewInput));

        // process percent of amendments for whom no target line items were changed
        allPercentOfInvoiceItems.addAll(processIsolatedPercentOfLineItemAmendments(order, helper, previewInput));
        return allPercentOfInvoiceItems;
    }

    private List<InvoiceItem> processIsolatedPercentOfLineItemAmendments(
        Order order,
        ProcessingHelper helper,
        ImmutableInvoicePreviewInput previewInput
    ) {
        List<InvoiceItem> percentOfLineInvoiceItems = new ArrayList<>();
        List<OrderLineItem> percentOfChargeLineItems = order
            .getLineItemsNetEffect()
            .stream()
            .filter(item -> helper.getPercentChargeIdToTargetPlanIds().containsKey(item.getChargeId()))
            .filter(item -> !helper.getAffectedPercentChargeIds().contains(item.getChargeId()))
            .filter(orderLineItem -> OrderCustomBillingService.notInvoicedForCustomBilling(order, previewInput, order.getOrderType(), orderLineItem))
            .toList();

        percentOfChargeLineItems.forEach(percentOfChargeLineItem -> {
            String percentOfChargeId = percentOfChargeLineItem.getChargeId();
            Charge charge = helper.chargeMap.get(percentOfChargeId);
            List<InvoiceItem> targetInvoiceItemsReBooked = helper.getTargetInvoiceItemsReBooked();
            switch (percentOfChargeLineItem.getAction()) {
                case ADD -> {
                    InvoiceItemAmounts addAmounts = helper.getInvoiceItemAmounts(
                        order,
                        targetInvoiceItemsReBooked,
                        percentOfChargeId,
                        percentOfChargeLineItem
                    );
                    percentOfLineInvoiceItems.add(buildInvoiceItem(percentOfChargeLineItem, addAmounts, charge.getBillingTerm()));
                }
                case REMOVE, UPDATE -> {
                    Currency currency = getCurrencyFromOrder(order);
                    InvoiceItemAmounts updateAmounts = getSimpleInvoiceAmount(percentOfChargeLineItem, currency);
                    percentOfLineInvoiceItems.add(buildInvoiceItem(percentOfChargeLineItem, updateAmounts, charge.getBillingTerm()));
                }
                default -> throw new ServiceFailureException(
                    String.format("Unexpect line item action %s while processing percent of charges", percentOfChargeLineItem.getAction())
                );
            }
        });
        return percentOfLineInvoiceItems;
    }

    private Set<String> getAffectedPercentChargeIds(Order order, Map<String, Set<String>> percentChargeIdToTargetPlanIds) {
        Set<String> planIdsChanged = order
            .getLineItemsNetEffect()
            .stream()
            .filter(orderLineItem -> !percentChargeIdToTargetPlanIds.containsKey(orderLineItem.getChargeId()))
            .map(OrderLineItem::getPlanId)
            .collect(Collectors.toSet());
        return percentChargeIdToTargetPlanIds
            .keySet()
            .stream()
            .filter(percentChargeId -> !Sets.intersection(percentChargeIdToTargetPlanIds.get(percentChargeId), planIdsChanged).isEmpty())
            .collect(Collectors.toSet());
    }

    private Map<String, Set<String>> percentChargeIdToRelatedPlanIdsMap(Map<String, Charge> chargeMap, Predicate<List<Plan>> targetPlansPredicate) {
        List<Charge> allPercentOfCharges = chargeMap.values().stream().filter(charge -> ChargeType.PERCENTAGE_OF == charge.getType()).toList();
        Map<String, Plan> planMap = new HashMap<>();
        Map<String, Set<String>> targetPlansByChargeId = new HashMap<>();
        for (Charge percentOfCharge : allPercentOfCharges) {
            Set<String> targetPlanIds = productCatalogGetService.getTargetPlanIdsForPercentOfPlanWithoutChecks(percentOfCharge.getPlanId());
            updatePlanMap(planMap, targetPlanIds);
            List<Plan> targetPlans = targetPlanIds.stream().map(planMap::get).toList();
            if (targetPlansPredicate.test(targetPlans)) {
                targetPlansByChargeId.put(percentOfCharge.getChargeId(), targetPlanIds);
            }
        }
        return targetPlansByChargeId;
    }

    private void updatePlanMap(Map<String, Plan> planMap, Set<String> targetPlanIds) {
        Set<String> missingPlanIds = SetUtils.difference(targetPlanIds, planMap.keySet());
        if (CollectionUtils.isEmpty(missingPlanIds)) {
            return;
        }
        List<Plan> missingPlans = productCatalogGetService.getPlansByPlanIds(missingPlanIds);
        missingPlans.forEach(plan -> planMap.put(plan.getPlanId(), plan));
    }

    private List<InvoiceItem> processNonAmendmentOrder(
        Order order,
        ImmutableInvoicePreviewInput previewInput,
        List<InvoiceItem> percentOfTargetInvoiceItems
    ) {
        List<InvoiceItem> percentOfInvoiceItems = getAddInvoiceItems(order, previewInput, percentOfTargetInvoiceItems, PERCENT_OF_PREDICATE);
        List<InvoiceItem> targetInvoiceItemsForPercentOfPercent = Stream.of(percentOfTargetInvoiceItems, percentOfInvoiceItems)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
        List<InvoiceItem> percentOfPercentInvoiceItems = getAddInvoiceItems(
            order,
            previewInput,
            targetInvoiceItemsForPercentOfPercent,
            PERCENT_OF_PERCENT_PREDICATE
        );
        // combine percent of invoice items and percent of percent invoice items and return
        return Stream.of(percentOfInvoiceItems, percentOfPercentInvoiceItems).flatMap(Collection::stream).collect(Collectors.toList());
    }

    @NotNull
    private List<InvoiceItem> getAddInvoiceItems(
        Order order,
        ImmutableInvoicePreviewInput previewInput,
        List<InvoiceItem> percentOfTargetInvoiceItems,
        Predicate<List<Plan>> targetPlansPredicate
    ) {
        Map<String, Charge> chargeMap = previewInput.getChargeMap();
        Recurrence billingCycle = previewInput.getInvoiceBillingInfo().getBillingCycle();
        Map<String, Set<String>> percentChargeIdToTargetPlanIds = percentChargeIdToRelatedPlanIdsMap(chargeMap, targetPlansPredicate);
        List<OrderLineItem> percentOfOrderLines = getPercentOfOrderLines(
            order,
            order.getLineItemsNetEffect(),
            previewInput,
            order.getOrderType(),
            percentChargeIdToTargetPlanIds
        );
        if (CollectionUtils.isEmpty(percentOfOrderLines)) {
            return List.of();
        }

        Currency currency = getCurrencyFromOrder(order);
        ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);
        TenantSetting tenantSetting = tenantSettingService.getTenantSettingInternal();
        TimeZone timeZone = tenantSetting.getDefaultTimeZone();
        boolean isMinMaxPercentOfAmountEnabled = featureService.isEnabled(Feature.MIN_MAX_PERCENT_OF_AMOUNT);

        List<InvoiceItem> invoiceItems = new ArrayList<>();
        for (OrderLineItem percentOfLine : percentOfOrderLines) {
            List<InvoiceItem> targetInvoiceItems = percentOfTargetInvoiceItems
                .stream()
                .filter(targetItem -> isInvoiceItemPercentOfRelation(percentChargeIdToTargetPlanIds, percentOfLine, targetItem, chargeMap))
                //TODO: implement precise pro-ration for overlapping invoice items
                .filter(targetItem -> isInvoiceItemInOrderRange(targetItem, percentOfLine))
                .collect(Collectors.toList());

            Validator.checkStateInternal(
                chargeMap.containsKey(percentOfLine.getChargeId()),
                String.format("cannot find percent of charge id %s in charge map", percentOfLine.getChargeId())
            );
            Charge charge = chargeMap.get(percentOfLine.getChargeId());
            // PMD complains about charge.getPercentDerivedFrom() even though we have Validator check above
            if (charge == null) {
                throw new InvariantCheckFailedException(
                    String.format("cannot find percent of charge id %s in charge map", percentOfLine.getChargeId())
                );
            }
            PercentDerivedFrom percentDerivedFrom = Optional.ofNullable(charge.getPercentDerivedFrom()).orElse(tenantSetting.getPercentDerivedFrom());

            InvoiceItemAmounts itemAmounts = getPercentOfInvoiceItemAmount(
                percentOfLine,
                targetInvoiceItems,
                charge,
                currency,
                percentDerivedFrom,
                isMinMaxPercentOfAmountEnabled,
                prorationConfig,
                discountCalculator,
                timeZone,
                billingCycle
            );
            invoiceItems.add(buildInvoiceItem(percentOfLine, itemAmounts, charge.getBillingTerm()));
        }
        return Collections.unmodifiableList(invoiceItems);
    }

    private boolean isInvoiceItemPercentOfRelation(
        Map<String, Set<String>> percentChargeIdToTargetPlanIds,
        OrderLineItem percentOrderItem,
        InvoiceItem targetItem,
        Map<String, Charge> chargeMap
    ) {
        Set<String> targetPlanIds = percentChargeIdToTargetPlanIds.get(percentOrderItem.getChargeId());
        Charge itemCharge = chargeMap.get(targetItem.getChargeId());
        return targetPlanIds.contains(itemCharge.getPlanId());
    }

    private InvoiceItemAmounts getSimpleInvoiceAmount(OrderLineItem percentOrderItem, Currency currency) {
        long quantity = percentOrderItem.getQuantity();
        BigDecimal listUnitPrice = percentOrderItem.getListUnitPrice();
        List<DiscountDetail> discounts = percentOrderItem.getDiscounts();
        List<TenantDiscountLineItem> predefinedDiscounts = percentOrderItem.getPredefinedDiscounts();
        DiscountResult listDiscountResult = discountCalculator.calculateDiscountAmounts(listUnitPrice, discounts, predefinedDiscounts);
        BigDecimal sellUnitPrice = listUnitPrice.subtract(
            Numbers.makeCurrencyScale(listDiscountResult.getTotalDiscountAmount(), currency.getCurrencyCode())
        );

        BigDecimal listAmount = listUnitPrice.multiply(BigDecimal.valueOf(quantity));
        DiscountResult discountResult = discountCalculator.calculateDiscountAmounts(listAmount, discounts, predefinedDiscounts);
        BigDecimal sellAmount = sellUnitPrice.multiply(BigDecimal.valueOf(quantity));

        return ImmutableInvoiceItemAmounts.builder()
            .sellAmount(sellAmount)
            .listAmount(listAmount)
            .discountAmount(Numbers.makeCurrencyScale(discountResult.getTotalDiscountAmount(), currency.getCurrencyCode()))
            .listUnitPrice(listUnitPrice)
            .sellUnitPrice(sellUnitPrice)
            .lineItemDiscounts(discountResult.getLineItemDiscounts())
            .tenantDiscounts(discountResult.getTenantDiscounts())
            .build();
    }

    private static InvoiceItemAmounts getPercentOfInvoiceItemAmount(
        OrderLineItem percentOrderItem,
        List<InvoiceItem> matchingInvoiceItems,
        Charge percentOfCharge,
        Currency currency,
        PercentDerivedFrom percentDerivedFrom,
        boolean minMaxAmountEnabled,
        ProrationConfig prorationConfig,
        DiscountCalculator discountCalculator,
        TimeZone timeZone,
        Recurrence billingCycle
    ) {
        Validator.checkNonNullInternal(percentOrderItem, "order line must be present");
        Validator.checkNonNullInternal(percentOfCharge, "charge must be present");

        long quantity = percentOrderItem.getQuantity();
        // NOTE: we are taking the absolute value so that we can account for cancellation and amendments
        BigDecimal targetAmountSum = matchingInvoiceItems
            .stream()
            .map(item -> {
                BigDecimal itemAmount =
                    switch (percentDerivedFrom) {
                        case LIST_AMOUNT -> item.getListAmount();
                        case SELL_AMOUNT -> item.getAmount();
                    };
                // prorate the percent of order line wrt to overlap with invoice items duration
                Period invoiceItemPeriod = Period.between(item.getPeriodStartDate(), item.getPeriodEndDate());
                Period lineItemPeriod = Period.between(percentOrderItem.getEffectiveDate(), percentOrderItem.getEndDate());
                BigDecimal prorationRatio = ProrationCalculator.getPeriodProrationRatio(
                    invoiceItemPeriod,
                    lineItemPeriod,
                    prorationConfig,
                    timeZone,
                    Optional.ofNullable(billingCycle)
                );
                LOGGER.debug(
                    "Proration ratio for order line {} and lineItemPeriod {}, invoiceItemPeriod {} - is {}",
                    percentOrderItem.getOrderLineId(),
                    lineItemPeriod,
                    invoiceItemPeriod,
                    prorationRatio
                );
                return itemAmount.multiply(prorationRatio);
            })
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .abs();

        List<DiscountDetail> discounts = percentOrderItem.getDiscounts();
        List<TenantDiscountLineItem> predefinedDiscounts = percentOrderItem.getPredefinedDiscounts();

        // list and sell unit price calculation
        BigDecimal percentOfListUnitPrice = calculatePercentOf(percentOfCharge.getPercent(), targetAmountSum);
        BigDecimal percentOfListUnitPriceBeforeOverride = null;
        if (percentOrderItem.getListPriceOverrideRatio() != null) {
            percentOfListUnitPriceBeforeOverride = Numbers.makeCurrencyScale(percentOfListUnitPrice, currency.getCurrencyCode());
            percentOfListUnitPrice = percentOfListUnitPrice.multiply(percentOrderItem.getListPriceOverrideRatio());
        }
        BigDecimal percentOfListUnitPriceScaled = Numbers.makeCurrencyScale(percentOfListUnitPrice, currency.getCurrencyCode());
        DiscountResult listDiscountResult = discountCalculator.calculateDiscountAmounts(percentOfListUnitPrice, discounts, predefinedDiscounts);
        BigDecimal percentOfSellUnitPrice = percentOfListUnitPrice.subtract(
            Numbers.makeCurrencyScale(listDiscountResult.getTotalDiscountAmount(), currency.getCurrencyCode())
        );
        BigDecimal percentOfSellUnitPriceScaled = Numbers.makeCurrencyScale(percentOfSellUnitPrice, currency.getCurrencyCode());

        // list amount and sell amount calculation
        BigDecimal percentOfListAmount = percentOfListUnitPriceScaled.multiply(BigDecimal.valueOf(quantity));
        BigDecimal percentOfListAmountBeforeOverride = Optional.ofNullable(percentOfListUnitPriceBeforeOverride)
            .map(unitPrice -> unitPrice.multiply(BigDecimal.valueOf(quantity)))
            .orElse(null);

        DiscountResult discountResult = discountCalculator.calculateDiscountAmounts(
            percentOfListAmount,
            percentOrderItem.getDiscounts(),
            percentOrderItem.getPredefinedDiscounts()
        );
        // sell amount is currency scaled sell unit price * quantity
        BigDecimal sellAmount = percentOfSellUnitPriceScaled.multiply(BigDecimal.valueOf(quantity));

        if (minMaxAmountEnabled && percentOrderItem.getQuantity() > 0) {
            // if percent of min max feature is enabled and order line is adding quantity (add or rebook part of amendment) then adjust sell amount according to min max configurations
            sellAmount = adjustSellAmount(percentOfCharge, sellAmount);
        }

        return ImmutableInvoiceItemAmounts.builder()
            .sellAmount(sellAmount)
            .listAmount(percentOfListAmount)
            .listAmountBeforeOverride(
                percentOrderItem.getListPriceOverrideRatio() == null
                    ? null
                    : Numbers.makeCurrencyScale(percentOfListAmountBeforeOverride, currency.getCurrencyCode())
            )
            .discountAmount(Numbers.makeCurrencyScale(discountResult.getTotalDiscountAmount(), currency.getCurrencyCode()))
            .listUnitPrice(percentOfListUnitPriceScaled)
            // TODO: make unit prices price display scale instead of currency scale
            .listUnitPriceBeforeOverride(
                percentOrderItem.getListPriceOverrideRatio() == null
                    ? null
                    : Numbers.makeCurrencyScale(percentOfListUnitPriceBeforeOverride, currency.getCurrencyCode())
            )
            .tenantDiscounts(discountResult.getTenantDiscounts())
            .lineItemDiscounts(discountResult.getLineItemDiscounts())
            .sellUnitPrice(percentOfSellUnitPriceScaled)
            .build();
    }

    private static BigDecimal adjustSellAmount(Charge percentOfCharge, BigDecimal sellAmount) {
        BigDecimal minAmount = percentOfCharge.getMinAmount();
        BigDecimal maxAmount = percentOfCharge.getMaxAmount();
        if (minAmount != null && sellAmount.compareTo(minAmount) < 0) {
            // if min amount is configured and sell amount is less than min amount, set to min amount
            return minAmount;
        }

        if (maxAmount != null && sellAmount.compareTo(maxAmount) > 0) {
            // if max amount is configured and sell amount is greater than max amount, set to max amount
            return maxAmount;
        }

        return sellAmount;
    }

    private static boolean isInvoiceItemInOrderRange(InvoiceItem invoiceItem, OrderLineItem lineItem) {
        return (
            lineItem.getEndDate().isAfter(invoiceItem.getPeriodStartDate()) && lineItem.getEffectiveDate().isBefore(invoiceItem.getPeriodEndDate())
        );
    }

    private static BigDecimal calculatePercentOf(BigDecimal percent, BigDecimal percentOf) {
        return Numbers.scaledDivide(percent, HUNDRED).multiply(percentOf);
    }

    private static List<OrderLineItem> getPercentOfOrderLines(
        Order order,
        List<OrderLineItem> orderLines,
        ImmutableInvoicePreviewInput previewInput,
        OrderType orderType,
        Map<String, Set<String>> percentChargeIdToTargetPlanIds
    ) {
        return orderLines
            .stream()
            .filter(orderLineItem -> percentChargeIdToTargetPlanIds.containsKey(orderLineItem.getChargeId()))
            .filter(orderLineItem -> OrderCustomBillingService.notInvoicedForCustomBilling(order, previewInput, orderType, orderLineItem))
            .collect(Collectors.toList());
    }

    private static InvoiceItem buildInvoiceItem(OrderLineItem percentOfLine, InvoiceItemAmounts itemAmounts, BillingTerm billingTerm) {
        InvoiceItem.InvoiceItemBuilder invoiceItemBuilder = new InvoiceItem.InvoiceItemBuilder()
            .entityId(percentOfLine.getEntityId())
            .orderId(percentOfLine.getOrderId())
            .orderLineItemId(percentOfLine.getOrderLineId())
            .chargeId(percentOfLine.getChargeId())
            .subscriptionChargeId(percentOfLine.getExternalSubscriptionChargeId())
            .subscriptionChargeGroupId(percentOfLine.getSubscriptionChargeGroupId())
            .status(InvoiceStatus.DRAFT)
            .listAmount(itemAmounts.getListAmount())
            .listAmountBeforeOverride(itemAmounts.getListAmountBeforeOverride())
            .discountAmount(itemAmounts.getDiscountAmount())
            .amount(itemAmounts.getSellAmount())
            .listUnitPrice(itemAmounts.getListUnitPrice())
            .listUnitPriceBeforeOverride(itemAmounts.getListUnitPriceBeforeOverride())
            .sellUnitPrice(itemAmounts.getSellUnitPrice())
            .taxAmount(BigDecimal.ZERO)
            .quantity(percentOfLine.getQuantity())
            .inlineDiscounts(itemAmounts.getLineItemDiscounts())
            .predefinedDiscounts(itemAmounts.getTenantDiscounts())
            .periodStartDate(percentOfLine.getEffectiveDate())
            .periodEndDate(percentOfLine.getEndDate())
            // todo: this may not be correct for the billing periods
            .triggerOn(billingTerm, percentOfLine.getEffectiveDate(), percentOfLine.getEndDate());
        return invoiceItemBuilder.createInvoiceItem();
    }

    /*
    There is a super edge case where the implementation of this method breaks down
    the use case is as follows
    1. There are two duplicate percent of line items in the order
    2. a target charge line item is modified
    3. one of the duplicate percent of line items is also modified
    4. then only one of the duplicate percent of line item will be picked up for processing
    NOTE: one way to not get into this nasty state is to allow only one percent of chargeId in a Order
    which is what we may land up doing
     */
    private static List<OrderLineItem> lineItemsByChargeIdAndBookingPredicate(
        Order order,
        ImmutableInvoicePreviewInput previewInput,
        String targetChargeId,
        BiPredicate<OrderLineItem, String> bookingPredicate
    ) {
        List<OrderLineItem> itemsFromNetEffect = order
            .getLineItemsNetEffect()
            .stream()
            .filter(lineItem -> lineItem.getEndDate().isAfter(order.getStartDate()) && bookingPredicate.test(lineItem, targetChargeId))
            .filter(lineItem -> OrderCustomBillingService.notInvoicedForCustomBilling(order, previewInput, order.getOrderType(), lineItem))
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(itemsFromNetEffect)) {
            return itemsFromNetEffect;
        }

        // else get it from line items but make sure to mark is as UPDATE
        List<OrderLineItem> orderLineItems = order
            .getLineItems()
            .stream()
            .filter(lineItem -> lineItem.getEndDate().isAfter(order.getStartDate()) && bookingPredicate.test(lineItem, targetChargeId))
            .filter(lineItem -> OrderCustomBillingService.notInvoicedForCustomBilling(order, previewInput, order.getOrderType(), lineItem))
            .collect(Collectors.toList());
        orderLineItems.forEach(item -> {
            // mark it as update and add it to
            item.setAction(ActionType.UPDATE);
            // add it to net effect
            order.getLineItemsNetEffect().add(item);
        });

        return orderLineItems;
    }

    private static Currency getCurrencyFromOrder(Order order) {
        Validator.checkStateInternal(order.getCurrency() != null, "order currency cannot be null");
        return order.getCurrency();
    }

    private static class ProcessingHelper {

        private final List<InvoiceItem> targetInvoiceItemsDeBooked;
        private final List<InvoiceItem> targetInvoiceItemsReBooked;
        private final Set<String> affectedPercentChargeIds;
        private final Map<String, Charge> chargeMap;
        private final Map<String, Set<String>> percentChargeIdToTargetPlanIds;
        private final PercentDerivedFrom tenantPercentDerivedFrom;
        private final FeatureService featureService;
        private final ProrationConfigurationGetService prorationConfigurationGetService;
        private final TenantSettingService tenantSettingService;
        private final DiscountCalculator discountCalculator;

        private ProcessingHelper(Builder builder) {
            targetInvoiceItemsDeBooked = builder.targetInvoiceItems
                .stream()
                .filter(item -> item.getAmount().compareTo(BigDecimal.ZERO) < 0)
                .collect(Collectors.toList());
            targetInvoiceItemsReBooked = builder.targetInvoiceItems
                .stream()
                .filter(item -> item.getAmount().compareTo(BigDecimal.ZERO) >= 0)
                .collect(Collectors.toList());
            affectedPercentChargeIds = builder.affectedPercentChargeIds;
            chargeMap = builder.chargeMap;
            percentChargeIdToTargetPlanIds = builder.percentChargeIdToTargetPlanIds;
            tenantPercentDerivedFrom = builder.tenantSettingService.getTenantSettingInternal().getPercentDerivedFrom();
            featureService = builder.featureService;
            prorationConfigurationGetService = builder.prorationConfigurationGetService;
            tenantSettingService = builder.tenantSettingService;
            discountCalculator = builder.discountCalculator;
        }

        public Map<String, Set<String>> getPercentChargeIdToTargetPlanIds() {
            return percentChargeIdToTargetPlanIds;
        }

        private List<InvoiceItem> getTargetInvoiceItemsReBooked() {
            return targetInvoiceItemsReBooked;
        }

        public Set<String> getAffectedPercentChargeIds() {
            return affectedPercentChargeIds;
        }

        private static Builder builder() {
            return new Builder();
        }

        private List<InvoiceItem> getPercentOfInvoiceItemsDeBooked(Order order, ImmutableInvoicePreviewInput previewInput) {
            return percentOfInvoiceItemsByBooking(order, DE_BOOK_PREDICATE, targetInvoiceItemsDeBooked, previewInput);
        }

        private List<InvoiceItem> getPercentOfInvoiceItemsReBooked(Order order, ImmutableInvoicePreviewInput previewInput) {
            return percentOfInvoiceItemsByBooking(order, RE_BOOK_PREDICATE, targetInvoiceItemsReBooked, previewInput);
        }

        private List<InvoiceItem> percentOfInvoiceItemsByBooking(
            Order order,
            BiPredicate<OrderLineItem, String> bookingPredicate,
            List<InvoiceItem> bookedInvoiceItems,
            ImmutableInvoicePreviewInput previewInput
        ) {
            List<InvoiceItem> percentOfInvoiceItems = new ArrayList<>();
            for (String affectedPercentChargeId : affectedPercentChargeIds) {
                List<OrderLineItem> bookedLineItems = lineItemsByChargeIdAndBookingPredicate(
                    order,
                    previewInput,
                    affectedPercentChargeId,
                    bookingPredicate
                );
                for (OrderLineItem bookedLineItem : bookedLineItems) {
                    Charge charge = chargeMap.get(bookedLineItem.getChargeId());
                    InvoiceItemAmounts itemAmounts = getInvoiceItemAmounts(order, bookedInvoiceItems, affectedPercentChargeId, bookedLineItem);
                    percentOfInvoiceItems.add(buildInvoiceItem(bookedLineItem, itemAmounts, charge.getBillingTerm()));
                }
            }
            return percentOfInvoiceItems;
        }

        private InvoiceItemAmounts getInvoiceItemAmounts(
            Order order,
            List<InvoiceItem> targetInvoiceItems,
            String percentOfChargeId,
            OrderLineItem percentOfLineItem
        ) {
            Set<String> percentOfPlanIds = percentChargeIdToTargetPlanIds.get(percentOfChargeId);
            List<InvoiceItem> matchingReBookLineItems = targetInvoiceItems
                .stream()
                .filter(invoiceItem -> {
                    Charge charge = chargeMap.get(invoiceItem.getChargeId());
                    return percentOfPlanIds.contains(charge.getPlanId());
                })
                .filter(invoiceItem -> isInvoiceItemInOrderRange(invoiceItem, percentOfLineItem))
                .collect(Collectors.toList());

            Validator.checkStateInternal(
                chargeMap.containsKey(percentOfChargeId),
                String.format("cannot find percent of charge id %s in charge map", percentOfChargeId)
            );
            Charge charge = chargeMap.get(percentOfLineItem.getChargeId());
            PercentDerivedFrom percentDerivedFrom = Optional.ofNullable(charge.getPercentDerivedFrom()).orElse(tenantPercentDerivedFrom);

            ProrationConfig prorationConfig = prorationConfigurationGetService.resolveProrationConfig(order);
            TimeZone timeZone = tenantSettingService.getTenantSetting().getDefaultTimeZone();

            return getPercentOfInvoiceItemAmount(
                percentOfLineItem,
                matchingReBookLineItems,
                charge,
                getCurrencyFromOrder(order),
                percentDerivedFrom,
                featureService.isEnabled(Feature.MIN_MAX_PERCENT_OF_AMOUNT),
                prorationConfig,
                discountCalculator,
                timeZone,
                order.getBillingCycle()
            );
        }

        public static class Builder {

            private List<InvoiceItem> targetInvoiceItems;
            private Set<String> affectedPercentChargeIds;
            private Map<String, Charge> chargeMap;
            private Map<String, Set<String>> percentChargeIdToTargetPlanIds;
            private TenantSettingService tenantSettingService;
            private FeatureService featureService;
            private ProrationConfigurationGetService prorationConfigurationGetService;
            private DiscountCalculator discountCalculator;

            private Builder() {}

            public Builder targetInvoiceItems(List<InvoiceItem> targetInvoiceItems) {
                this.targetInvoiceItems = targetInvoiceItems;
                return this;
            }

            public Builder affectedPercentChargeIds(Set<String> affectedPercentChargeIds) {
                this.affectedPercentChargeIds = affectedPercentChargeIds;
                return this;
            }

            public Builder chargeMap(Map<String, Charge> chargeMap) {
                this.chargeMap = chargeMap;
                return this;
            }

            public Builder percentChargeIdToTargetPlanIds(Map<String, Set<String>> percentChargeIdToTargetPlanIds) {
                this.percentChargeIdToTargetPlanIds = percentChargeIdToTargetPlanIds;
                return this;
            }

            public Builder tenantSettingService(TenantSettingService tenantSettingService) {
                this.tenantSettingService = tenantSettingService;
                return this;
            }

            public Builder featureService(FeatureService featureService) {
                this.featureService = featureService;
                return this;
            }

            public Builder prorationConfigurationGetService(ProrationConfigurationGetService prorationConfigurationGetService) {
                this.prorationConfigurationGetService = prorationConfigurationGetService;
                return this;
            }

            public Builder discountCalculator(DiscountCalculator discountCalculator) {
                this.discountCalculator = discountCalculator;
                return this;
            }

            public ProcessingHelper build() {
                return new ProcessingHelper(this);
            }
        }
    }
}
