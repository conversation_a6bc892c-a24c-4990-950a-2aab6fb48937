package com.subskribe.billy.invoice.document;

import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class InvoiceTemplatePlan {

    private final String id;
    private final String productName;
    private final String name;
    private final String description;
    private final DocumentRenderFormatter formatter;
    private final String currencyCode;
    private final List<InvoiceTemplateLineItem> lineItems;

    public InvoiceTemplatePlan(
        String id,
        String productName,
        String name,
        String description,
        DocumentRenderFormatter formatter,
        String currencyCode
    ) {
        this.id = id;
        this.productName = productName;
        this.name = name;
        this.description = description;
        this.formatter = formatter;
        this.currencyCode = currencyCode;
        lineItems = new ArrayList<>();
    }

    public String getId() {
        return id;
    }

    public void addLineItem(InvoiceTemplateLineItem lineItem) {
        lineItems.add(lineItem);
    }

    public String getProductName() {
        return productName;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public List<InvoiceTemplateLineItem> getLineItems() {
        return lineItems;
    }

    public List<InvoiceTemplateLineItem> getBillableItems() {
        return lineItems.stream().filter(item -> item.getRawAmount().compareTo(BigDecimal.ZERO) != 0).toList();
    }

    public String getAmount() {
        return formatter.currencyFormat(getPlanAmount(), currencyCode);
    }

    public Boolean hasAmount() {
        return getPlanAmount().compareTo(BigDecimal.ZERO) != 0;
    }

    public String sharedStartDate() {
        List<Long> startDates = lineItems.stream().map(InvoiceTemplateLineItem::getRawStartDate).distinct().toList();
        if (startDates.size() == 1) {
            return formatter.dateFormat(startDates.get(0));
        } else {
            return null;
        }
    }

    public String sharedEndDate() {
        List<Long> endDates = lineItems.stream().map(InvoiceTemplateLineItem::getRawEndDate).distinct().toList();
        if (endDates.size() == 1) {
            return formatter.endDateFormat(endDates.get(0));
        } else {
            return null;
        }
    }

    public String sharedBillableStartDate() {
        List<Long> startDates = getBillableItems().stream().map(InvoiceTemplateLineItem::getRawStartDate).distinct().toList();
        if (startDates.size() == 1) {
            return formatter.dateFormat(startDates.get(0));
        } else {
            return null;
        }
    }

    public String sharedBillableEndDate() {
        List<Long> endDates = getBillableItems().stream().map(InvoiceTemplateLineItem::getRawEndDate).distinct().toList();
        if (endDates.size() == 1) {
            return formatter.endDateFormat(endDates.get(0));
        } else {
            return null;
        }
    }

    private BigDecimal getPlanAmount() {
        return lineItems.stream().map(InvoiceTemplateLineItem::getRawAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
