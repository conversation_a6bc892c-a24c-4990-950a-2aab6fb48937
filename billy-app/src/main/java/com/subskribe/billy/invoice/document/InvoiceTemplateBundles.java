package com.subskribe.billy.invoice.document;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class InvoiceTemplateBundles {

    private final List<InvoiceTemplateBundle> bundles;

    private final List<InvoiceTemplateLineItem> unbundledItems;

    private final DocumentRenderFormatter formatter;

    private final String currencyCode;

    public InvoiceTemplateBundles(
        List<InvoiceTemplateBundle> bundles,
        List<InvoiceTemplateLineItem> unbundledItems,
        DocumentRenderFormatter formatter,
        String currencyCode
    ) {
        this.bundles = bundles;
        this.unbundledItems = unbundledItems;
        this.formatter = formatter;
        this.currencyCode = currencyCode;
    }

    public List<InvoiceTemplateBundle> getBundles() {
        return bundles;
    }

    public List<InvoiceTemplateLineItem> getUnbundledItems() {
        return unbundledItems;
    }

    public DocumentRenderFormatter getFormatter() {
        return formatter;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public InvoiceTemplateLineItem getFirstItem() {
        if (CollectionUtils.isNotEmpty(bundles)) {
            return bundles.get(0).getLineItems().get(0);
        }

        if (CollectionUtils.isNotEmpty(unbundledItems)) {
            return unbundledItems.get(0);
        }

        return null;
    }

    public String getAmount() {
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (InvoiceTemplateBundle bundle : bundles) {
            totalAmount = totalAmount.add(Numbers.makeCurrencyScale(bundle.getAmountUnformatted(), bundle.getCurrencyCode()));
        }

        for (InvoiceTemplateLineItem item : unbundledItems) {
            totalAmount = totalAmount.add(item.getRawAmount());
        }

        return formatter.currencyFormat(totalAmount, currencyCode);
    }

    public static InvoiceTemplateBundles buildItemBundles(
        InvoiceDocumentJson invoiceDocumentJson,
        InvoiceTemplateBundleBy type,
        String bundleKey,
        List<InvoiceTemplateLineItem> lineItems,
        DocumentRenderFormatter formatter,
        String currencyCode
    ) {
        if (type == null || StringUtils.isEmpty(bundleKey) || CollectionUtils.isEmpty(lineItems)) {
            return new InvoiceTemplateBundles(List.of(), lineItems, formatter, currencyCode);
        }

        Map<String, List<InvoiceTemplateLineItem>> invoiceItemsBundleMap = new LinkedHashMap<>();
        List<InvoiceTemplateLineItem> unbundledItems = new ArrayList<>();

        Optional<CustomFieldValue> bundleByCustomField = lineItems
            .stream()
            .map(item -> findCustomFieldByName(bundleKey, getCustomFieldsByType(item, type, invoiceDocumentJson)))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .findFirst();

        if (bundleByCustomField.isEmpty()) {
            return new InvoiceTemplateBundles(List.of(), lineItems, formatter, currencyCode);
        }

        CustomFieldValue bundleCustomField = bundleByCustomField.get();
        List<String> bundlePickListOptions = bundleCustomField.getType() == CustomFieldType.PICKLIST
            ? bundleCustomField.getOptions()
            : getCustomFieldOptionsByInvoiceLineItems(invoiceDocumentJson, lineItems, bundleCustomField, type);

        bundlePickListOptions.forEach(option -> invoiceItemsBundleMap.put(option, new ArrayList<>()));
        lineItems.forEach(lineItem -> findBundleAndAddLineItem(invoiceDocumentJson, type, bundleKey, lineItem, unbundledItems, invoiceItemsBundleMap)
        );

        List<InvoiceTemplateBundle> bundles = invoiceItemsBundleMap
            .entrySet()
            .stream()
            .filter(entry -> !entry.getValue().isEmpty())
            .map(entry -> new InvoiceTemplateBundle(entry.getKey(), entry.getValue(), currencyCode, formatter))
            .toList();

        return new InvoiceTemplateBundles(bundles, unbundledItems, formatter, currencyCode);
    }

    private static void findBundleAndAddLineItem(
        InvoiceDocumentJson invoiceDocumentJson,
        InvoiceTemplateBundleBy type,
        String bundleKey,
        InvoiceTemplateLineItem lineItem,
        List<InvoiceTemplateLineItem> unbundledItems,
        Map<String, List<InvoiceTemplateLineItem>> invoiceItemsBundleMap
    ) {
        Optional<CustomFieldValue> customFieldOptional = findCustomFieldByName(bundleKey, getCustomFieldsByType(lineItem, type, invoiceDocumentJson));
        String customFieldValue = customFieldOptional.map(CustomFieldValue::getValue).orElse(null);
        if (StringUtils.isBlank(customFieldValue) || invoiceItemsBundleMap.get(customFieldValue) == null) {
            unbundledItems.add(lineItem);
        } else {
            invoiceItemsBundleMap.get(customFieldValue).add(lineItem);
        }
    }

    private static List<String> getCustomFieldOptionsByInvoiceLineItems(
        InvoiceDocumentJson invoiceDocumentJson,
        List<InvoiceTemplateLineItem> lineItems,
        CustomFieldValue bundleCustomField,
        InvoiceTemplateBundleBy type
    ) {
        List<String> customFieldOptions = new ArrayList<>();

        for (InvoiceTemplateLineItem item : lineItems) {
            var customFieldOptional = findCustomFieldByName(bundleCustomField.getName(), getCustomFieldsByType(item, type, invoiceDocumentJson));

            if (customFieldOptional.isPresent() && StringUtils.isNotBlank(customFieldOptional.get().getValue())) {
                customFieldOptions.add(customFieldOptional.get().getValue());
            }
        }

        return customFieldOptions;
    }

    private static Optional<CustomFieldValue> findCustomFieldByName(String name, Map<String, CustomFieldValue> customFieldsMap) {
        if (customFieldsMap == null) {
            return Optional.empty();
        }

        return customFieldsMap.values().stream().filter(customFieldValue -> customFieldValue.getName().equals(name)).findFirst();
    }

    private static Map<String, CustomFieldValue> getCustomFieldsByType(
        InvoiceTemplateLineItem invoiceTemplateLineItem,
        InvoiceTemplateBundleBy customFieldType,
        InvoiceDocumentJson invoiceDocumentJson
    ) {
        Map<String, CustomField> customFieldsByTypeMap;
        String key = StringUtils.EMPTY;
        switch (customFieldType) {
            case PLAN_CUSTOM_FIELD -> {
                customFieldsByTypeMap = invoiceDocumentJson.getPlanCustomFields();
                key = invoiceTemplateLineItem.getPlan().getId();
            }
            case CHARGE_CUSTOM_FIELD -> {
                customFieldsByTypeMap = invoiceDocumentJson.getChargeCustomFields();
                key = invoiceTemplateLineItem.getCharge().getId();
            }
            default -> {
                return Map.of();
            }
        }

        if (customFieldsByTypeMap == null || customFieldsByTypeMap.isEmpty() || customFieldsByTypeMap.get(key) == null) {
            return Map.of();
        }

        return customFieldsByTypeMap.get(key).getEntries();
    }
}
