package com.subskribe.billy.invoice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.foreignexchange.service.RealizedGainLossService;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.resources.json.invoice.InvoiceJson;
import com.subskribe.billy.resources.json.invoice.InvoiceJsonMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.mapstruct.factory.Mappers;

public class InvoiceEventService {

    private final EventPublishingService eventPublishingService;
    private final TenantIdProvider tenantIdProvider;
    private final RealizedGainLossService realizedGainLossService;
    private final EntityGetService entityGetService;
    private final FeatureService featureService;
    private final InvoiceJsonMapper invoiceJsonMapper;
    private final ObjectMapper objectMapper;

    @Inject
    public InvoiceEventService(
        EventPublishingService eventPublishingService,
        TenantIdProvider tenantIdProvider,
        RealizedGainLossService realizedGainLossService,
        EntityGetService entityGetService,
        FeatureService featureService
    ) {
        this.eventPublishingService = eventPublishingService;
        this.tenantIdProvider = tenantIdProvider;
        this.realizedGainLossService = realizedGainLossService;
        this.entityGetService = entityGetService;
        this.featureService = featureService;
        invoiceJsonMapper = Mappers.getMapper(InvoiceJsonMapper.class);
        objectMapper = JacksonProvider.defaultMapper();
    }

    @VisibleForTesting
    InvoiceEventService(
        EventPublishingService eventPublishingService,
        TenantIdProvider tenantIdProvider,
        RealizedGainLossService realizedGainLossService,
        EntityGetService entityGetService,
        FeatureService featureService,
        InvoiceJsonMapper invoiceJsonMapper,
        ObjectMapper objectMapper
    ) {
        this.eventPublishingService = eventPublishingService;
        this.tenantIdProvider = tenantIdProvider;
        this.realizedGainLossService = realizedGainLossService;
        this.entityGetService = entityGetService;
        this.featureService = featureService;
        this.invoiceJsonMapper = invoiceJsonMapper;
        this.objectMapper = objectMapper;
    }

    public void publishEventInTransaction(Configuration configuration, Invoice invoice) {
        publishV1(configuration, invoice);
        publishV2(configuration, invoice);
        addAndPublishContractAssetGainLossEntries(configuration, invoice);
    }

    private void publishV1(Configuration configuration, Invoice invoice) {
        publishV1Event(invoice, configuration, EventType.INVOICE_POSTED);
    }

    private void publishV2(Configuration configuration, Invoice invoice) {
        publishV2Event(invoice, configuration, EventType.INVOICE_POSTED_V2);
    }

    public void publishVoidEventInTransaction(Configuration configuration, Invoice invoice) {
        publishVoidV1(configuration, invoice);
        publishVoidV2(configuration, invoice);
    }

    private void publishVoidV1(Configuration configuration, Invoice invoice) {
        publishV1Event(invoice, configuration, EventType.INVOICE_VOIDED);
    }

    private void publishVoidV2(Configuration configuration, Invoice invoice) {
        publishV2Event(invoice, configuration, EventType.INVOICE_VOIDED_V2);
    }

    private void publishV2Event(Invoice invoice, Configuration configuration, EventType eventType) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        InvoiceJson invoiceJson = invoiceJsonMapper.invoiceToJson(invoice);
        byte[] v2PayloadBytes = serializeInvoiceJson(invoiceJson);
        eventPublishingService.publishEventInTransaction(
            configuration,
            eventType,
            tenantId,
            invoice.getEntityId(),
            invoice.getCustomerAccountId(),
            v2PayloadBytes
        );
    }

    private void publishV1Event(Invoice invoice, Configuration configuration, EventType eventType) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        byte[] v1PayloadBytes = invoice.getInvoiceNumber().getNumber().getBytes(StandardCharsets.UTF_8);

        eventPublishingService.publishEventInTransaction(
            configuration,
            eventType,
            tenantId,
            invoice.getEntityId(),
            invoice.getCustomerAccountId(),
            v1PayloadBytes
        );
    }

    private void addAndPublishContractAssetGainLossEntries(Configuration configuration, Invoice invoice) {
        if (!featureService.isEnabled(Feature.TRANSACTIONAL_FOREIGN_EXCHANGE)) {
            return;
        }
        Entity entity = entityGetService.getEntityById(invoice.getEntityId());
        if (Objects.equals(entity.getFunctionalCurrency(), invoice.getCurrencyCode())) {
            return;
        }
        realizedGainLossService.addAndPublishContractAssetGainLossEntries(configuration, invoice);
    }

    private byte[] serializeInvoiceJson(InvoiceJson invoiceJson) {
        try {
            return objectMapper.writeValueAsBytes(invoiceJson);
        } catch (JsonProcessingException e) {
            throw new ServiceFailureException("Failed to serialize invoice json for event publishing", e);
        }
    }
}
