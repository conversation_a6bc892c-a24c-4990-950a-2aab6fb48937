package com.subskribe.billy.invoice.model;

import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import javax.annotation.Nullable;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
public interface MemoizedInvoiceLineItem {
    @Nullable
    UUID getId();

    @Nullable
    String getEntityId();

    String getSubscriptionId();

    String getChargeId();

    String getSubscriptionChargeId();

    String getSubscriptionChargeGroupId();

    String getOrderId();

    String getOrderLineItemId();

    BigDecimal getListAmount();

    BigDecimal getDiscountAmount();

    BigDecimal getAmount();

    @Nullable
    BigDecimal getListUnitPrice();

    @Nullable
    BigDecimal getSellUnitPrice();

    Long getQuantity();

    Instant getPeriodStartDate();

    Instant getPeriodEndDate();

    Instant getTriggerOn();

    @Nullable
    Boolean getIsBackfilled();

    @Nullable
    Instant getCreatedOn();

    default InvoiceItem to(OrderLineItem item) {
        return new InvoiceItem.InvoiceItemBuilder()
            .entityId(item.getEntityId())
            .orderId(item.getOrderId())
            .orderLineItemId(item.getOrderLineId())
            .planId(item.getPlanId())
            .chargeId(getChargeId())
            .subscriptionChargeId(item.getExternalSubscriptionChargeId())
            .subscriptionChargeGroupId(item.getSubscriptionChargeGroupId())
            .status(InvoiceStatus.DRAFT)
            .listAmount(getListAmount())
            .discountAmount(getDiscountAmount())
            .amount(getAmount())
            .listUnitPrice(getListUnitPrice())
            .sellUnitPrice(getSellUnitPrice())
            .taxAmount(BigDecimal.ZERO)
            .quantity(getQuantity())
            .inlineDiscounts(item.getDiscounts())
            .predefinedDiscounts(item.getPredefinedDiscounts())
            .periodStartDate(getPeriodStartDate())
            .periodEndDate(getPeriodEndDate())
            .triggerOn(getTriggerOn())
            .createInvoiceItem();
    }

    static MemoizedInvoiceLineItem from(String subscriptionId, InvoiceItem invoiceItem, BillingTerm billingTerm) {
        Instant triggerOn = invoiceItem.getTriggerOn() != null
            ? invoiceItem.getTriggerOn()
            : (billingTerm == BillingTerm.IN_ARREARS ? invoiceItem.getPeriodEndDate() : invoiceItem.getPeriodStartDate());
        return from(subscriptionId, invoiceItem, triggerOn);
    }

    private static MemoizedInvoiceLineItem from(String subscriptionId, InvoiceItem invoiceItem, Instant triggerOn) {
        return ImmutableMemoizedInvoiceLineItem.builder()
            .entityId(invoiceItem.getEntityId())
            .subscriptionId(subscriptionId)
            .chargeId(invoiceItem.getChargeId())
            .subscriptionChargeId(invoiceItem.getSubscriptionChargeId())
            .subscriptionChargeGroupId(invoiceItem.getSubscriptionChargeGroupId())
            .orderId(invoiceItem.getOrderId())
            .orderLineItemId(invoiceItem.getOrderLineItemId())
            .listAmount(invoiceItem.getListAmount())
            .discountAmount(invoiceItem.getDiscountAmount())
            .amount(invoiceItem.getAmount())
            .listUnitPrice(invoiceItem.getListUnitPrice())
            .sellUnitPrice(invoiceItem.getSellUnitPrice())
            .quantity(invoiceItem.getQuantity())
            .periodStartDate(invoiceItem.getPeriodStartDate())
            .periodEndDate(invoiceItem.getPeriodEndDate())
            .triggerOn(triggerOn)
            .build();
    }
}
