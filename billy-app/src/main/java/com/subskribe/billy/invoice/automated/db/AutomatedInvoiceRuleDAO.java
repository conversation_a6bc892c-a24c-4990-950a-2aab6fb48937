package com.subskribe.billy.invoice.automated.db;

import static com.subskribe.billy.jooq.default_schema.Indexes.INDEX_AUTOMATED_INVOICE_RULE_TENANT_ID_NAME_CONDITIONAL;
import static com.subskribe.billy.jooq.default_schema.Tables.AUTOMATED_INVOICE_RULE_CONFIGURATION;
import static com.subskribe.billy.jooq.default_schema.Tables.AUTOMATED_INVOICE_RULE_EXECUTION;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.automated.mapper.AutomatedInvoiceRuleMapper;
import com.subskribe.billy.invoice.automated.model.AutomatedInvoiceRule;
import com.subskribe.billy.invoice.automated.model.AutomatedInvoiceRuleExecution;
import com.subskribe.billy.jooq.default_schema.tables.records.AutomatedInvoiceRuleConfigurationRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.AutomatedInvoiceRuleExecutionRecord;
import com.subskribe.billy.postgres.PostgresErrorHandler;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.mapstruct.factory.Mappers;

public class AutomatedInvoiceRuleDAO {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutomatedInvoiceRuleDAO.class);

    private final AutomatedInvoiceRuleMapper automatedInvoiceRuleMapper;
    private final TenantIdProvider tenantIdProvider;
    private final DSLContextProvider dslContextProvider;

    @Inject
    public AutomatedInvoiceRuleDAO(TenantIdProvider tenantIdProvider, DSLContextProvider dslContextProvider) {
        this.tenantIdProvider = tenantIdProvider;
        this.dslContextProvider = dslContextProvider;
        automatedInvoiceRuleMapper = Mappers.getMapper(AutomatedInvoiceRuleMapper.class);
    }

    @Deprecated
    public AutomatedInvoiceRule getAutomatedInvoiceRule(String automatedInvoiceRuleInternalId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);

        AutomatedInvoiceRuleConfigurationRecord record = dslContext
            .select()
            .from(AUTOMATED_INVOICE_RULE_CONFIGURATION)
            .where(AUTOMATED_INVOICE_RULE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .and(AUTOMATED_INVOICE_RULE_CONFIGURATION.ID.eq(UUID.fromString(automatedInvoiceRuleInternalId)))
            .fetchOneInto(AutomatedInvoiceRuleConfigurationRecord.class);

        if (record == null) {
            throw new ObjectNotFoundException(BillyObjectType.AUTOMATED_INVOICE_RULE_CONFIGURATION, automatedInvoiceRuleInternalId);
        }

        return automatedInvoiceRuleMapper.recordToAutomatedInvoiceRule(record);
    }

    public AutomatedInvoiceRule addAutomatedInvoiceRule(AutomatedInvoiceRule automatedInvoiceRule, String automatedInvoiceRuleId) {
        AutomatedInvoiceRuleConfigurationRecord recordToInsert = automatedInvoiceRuleMapper.automatedInvoiceRuleToRecord(automatedInvoiceRule);
        recordToInsert.setNormalizedName(StringUtils.normalizeSpace(automatedInvoiceRule.getName()).toLowerCase());
        recordToInsert.reset(AUTOMATED_INVOICE_RULE_CONFIGURATION.ID);

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);

        recordToInsert.setTenantId(tenantId);
        recordToInsert.setAutomatedInvoiceRuleId(automatedInvoiceRuleId);

        AutomatedInvoiceRuleConfigurationRecord storedRecord = PostgresErrorHandler.withConstraintAsConflict(
            () -> dslContext.insertInto(AUTOMATED_INVOICE_RULE_CONFIGURATION).set(recordToInsert).returning().fetchOne(),
            INDEX_AUTOMATED_INVOICE_RULE_TENANT_ID_NAME_CONDITIONAL.getName(),
            String.format("Automated Invoice rule with name: %s already exists for tenant with id: %s", automatedInvoiceRule.getName(), tenantId)
        );
        return automatedInvoiceRuleMapper.recordToAutomatedInvoiceRule(storedRecord);
    }

    public List<AutomatedInvoiceRule> getAutomatedInvoiceRulesByTenantId() {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);
        List<AutomatedInvoiceRuleConfigurationRecord> records = dslContext
            .select()
            .from(AUTOMATED_INVOICE_RULE_CONFIGURATION)
            .where(AUTOMATED_INVOICE_RULE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .and(AUTOMATED_INVOICE_RULE_CONFIGURATION.IS_DELETED.isFalse())
            .fetchInto(AutomatedInvoiceRuleConfigurationRecord.class);

        return automatedInvoiceRuleMapper.recordsToAutomatedInvoiceRules(records);
    }

    /**
     * Remove this method when FF {@link com.subskribe.billy.configuration.dynamic.Feature.INVOICE_GENERATION_MULTI_ENTITY} is removed
     * **/
    public void updateLastAndNextExecutionDate(
        AutomatedInvoiceRule automatedInvoiceRule,
        Instant lastExecutionInstant,
        Instant nextExecutionInstant
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);

        AutomatedInvoiceRuleConfigurationRecord record = automatedInvoiceRuleMapper.automatedInvoiceRuleToRecord(automatedInvoiceRule);
        record.setLastExecutionDate(DateTimeConverter.instantToLocalDateTime(lastExecutionInstant));
        record.setNextExecutionDate(DateTimeConverter.instantToLocalDateTime(nextExecutionInstant));

        int updatedRows = dslContext
            .update(AUTOMATED_INVOICE_RULE_CONFIGURATION)
            .set(record)
            .where(AUTOMATED_INVOICE_RULE_CONFIGURATION.ID.eq(automatedInvoiceRule.getId()))
            .and(AUTOMATED_INVOICE_RULE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .execute();
        if (updatedRows != 1) {
            throw new ServiceFailureException(
                String.format(
                    "Automated Invoice rule with name: %s could not be updated for tenant with id: %s",
                    automatedInvoiceRule.getName(),
                    tenantId
                )
            );
        }
    }

    @Deprecated
    public AutomatedInvoiceRule updateAutomatedInvoiceRule(String automatedInvoiceRuleInternalId, AutomatedInvoiceRule automatedInvoiceRule) {
        AutomatedInvoiceRuleConfigurationRecord recordToUpdate = automatedInvoiceRuleMapper.automatedInvoiceRuleToRecord(automatedInvoiceRule);
        recordToUpdate.setNormalizedName(StringUtils.normalizeSpace(automatedInvoiceRule.getName()).toLowerCase());
        recordToUpdate.setId(UUID.fromString(automatedInvoiceRuleInternalId));

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);

        int updatedRows = dslContext
            .update(AUTOMATED_INVOICE_RULE_CONFIGURATION)
            .set(recordToUpdate)
            .where(AUTOMATED_INVOICE_RULE_CONFIGURATION.ID.eq(UUID.fromString(automatedInvoiceRuleInternalId)))
            .and(AUTOMATED_INVOICE_RULE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .execute();
        if (updatedRows != 1) {
            throw new ServiceFailureException(
                String.format(
                    "Automated Invoice rule with name: %s and id: %s could not be updated for tenant with id: %s",
                    recordToUpdate.getName(),
                    recordToUpdate.getId(),
                    tenantId
                )
            );
        }

        return getAutomatedInvoiceRule(automatedInvoiceRuleInternalId);
    }

    public void ensureUniqueAutomatedInvoiceRuleId(String automatedInvoiceRuleId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        AutomatedInvoiceRuleConfigurationRecord record = dslContext
            .select()
            .from(AUTOMATED_INVOICE_RULE_CONFIGURATION)
            .where(AUTOMATED_INVOICE_RULE_CONFIGURATION.AUTOMATED_INVOICE_RULE_ID.eq(automatedInvoiceRuleId))
            .and(AUTOMATED_INVOICE_RULE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .fetchOneInto(AutomatedInvoiceRuleConfigurationRecord.class);

        if (record != null) {
            throw new DuplicateIdException("Duplicated Automated Invoice Rule Id generated = " + automatedInvoiceRuleId);
        }
    }

    public AutomatedInvoiceRule getAutomatedInvoiceRuleById(String automatedInvoiceRuleId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return getAutomatedInvoiceRuleById(automatedInvoiceRuleId, tenantId);
    }

    public AutomatedInvoiceRule getAutomatedInvoiceRuleById(String automatedInvoiceRuleId, String tenantId) {
        DSLContext dslContext = dslContextProvider.get(tenantId);

        AutomatedInvoiceRuleConfigurationRecord record = dslContext
            .select()
            .from(AUTOMATED_INVOICE_RULE_CONFIGURATION)
            .where(AUTOMATED_INVOICE_RULE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .and(AUTOMATED_INVOICE_RULE_CONFIGURATION.AUTOMATED_INVOICE_RULE_ID.eq(automatedInvoiceRuleId))
            .fetchOneInto(AutomatedInvoiceRuleConfigurationRecord.class);

        if (record == null) {
            throw new ObjectNotFoundException(BillyObjectType.AUTOMATED_INVOICE_RULE_CONFIGURATION, automatedInvoiceRuleId);
        }

        return automatedInvoiceRuleMapper.recordToAutomatedInvoiceRule(record);
    }

    public void updateAutomatedInvoiceRuleUsingAutomatedInvoiceRuleId(
        String automatedInvoiceRuleId,
        UUID automatedInvoiceRuleInternalId,
        AutomatedInvoiceRule automatedInvoiceRule
    ) {
        AutomatedInvoiceRuleConfigurationRecord recordToUpdate = automatedInvoiceRuleMapper.automatedInvoiceRuleToRecord(automatedInvoiceRule);
        recordToUpdate.setNormalizedName(StringUtils.normalizeSpace(automatedInvoiceRule.getName()).toLowerCase());
        recordToUpdate.setAutomatedInvoiceRuleId(automatedInvoiceRuleId);
        recordToUpdate.setId(automatedInvoiceRuleInternalId);

        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);

        int updatedRows = dslContext
            .update(AUTOMATED_INVOICE_RULE_CONFIGURATION)
            .set(recordToUpdate)
            .where(AUTOMATED_INVOICE_RULE_CONFIGURATION.AUTOMATED_INVOICE_RULE_ID.eq(automatedInvoiceRuleId))
            .and(AUTOMATED_INVOICE_RULE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .execute();
        if (updatedRows != 1) {
            throw new ServiceFailureException(
                String.format(
                    "Automated Invoice rule with name: %s and id: %s could not be updated for tenant with id: %s",
                    recordToUpdate.getName(),
                    recordToUpdate.getAutomatedInvoiceRuleId(),
                    tenantId
                )
            );
        }
    }

    public List<AutomatedInvoiceRule> getPageOfAutomatedInvoiceRulesWithNoIdsForTenant(int limit) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);

        List<AutomatedInvoiceRuleConfigurationRecord> records = dslContext
            .select()
            .from(AUTOMATED_INVOICE_RULE_CONFIGURATION)
            .where(AUTOMATED_INVOICE_RULE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .and(
                AUTOMATED_INVOICE_RULE_CONFIGURATION.AUTOMATED_INVOICE_RULE_ID.isNull()
                    .or(AUTOMATED_INVOICE_RULE_CONFIGURATION.AUTOMATED_INVOICE_RULE_ID.eq(StringUtils.EMPTY))
            )
            .limit(limit)
            .fetchInto(AutomatedInvoiceRuleConfigurationRecord.class);

        return automatedInvoiceRuleMapper.recordsToAutomatedInvoiceRules(records);
    }

    public void addHumanFriendlyIdToAutomatedInvoiceRule(UUID internalId, String automatedInvoiceRuleId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);

        ensureAutomatedInvoiceRuleExistsAndDoesNotHaveHumanFriendlyId(internalId, dslContext, tenantId);

        int recordsUpdated = dslContext
            .update(AUTOMATED_INVOICE_RULE_CONFIGURATION)
            .set(AUTOMATED_INVOICE_RULE_CONFIGURATION.AUTOMATED_INVOICE_RULE_ID, automatedInvoiceRuleId)
            .where(AUTOMATED_INVOICE_RULE_CONFIGURATION.ID.eq(internalId))
            .and(AUTOMATED_INVOICE_RULE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .execute();

        if (recordsUpdated != 1) {
            throw new ServiceFailureException("Failed to update automated invoice rule id for row: " + internalId);
        }

        LOGGER.info("Updated automated invoice rule id for row. Internal id: {} Human-friendly ID: {}", internalId, automatedInvoiceRuleId);
    }

    private static void ensureAutomatedInvoiceRuleExistsAndDoesNotHaveHumanFriendlyId(UUID internalId, DSLContext dslContext, String tenantId) {
        AutomatedInvoiceRuleConfigurationRecord record = dslContext
            .selectFrom(AUTOMATED_INVOICE_RULE_CONFIGURATION)
            .where(AUTOMATED_INVOICE_RULE_CONFIGURATION.ID.eq(internalId))
            .and(AUTOMATED_INVOICE_RULE_CONFIGURATION.TENANT_ID.eq(tenantId))
            .fetchOne();

        if (record == null) {
            throw new InvariantCheckFailedException("An automated invoice rule record with ID not found: " + internalId);
        }

        if (StringUtils.isNotBlank(record.getAutomatedInvoiceRuleId())) {
            throw new InvariantCheckFailedException("Automated invoice rule with this id already has a human friendly id: " + internalId);
        }
    }

    public void updateLastAndNextExecutionDateForEntity(
        AutomatedInvoiceRule automatedInvoiceRule,
        String entityId,
        Instant lastExecutionInstant,
        Instant nextExecutionInstant
    ) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        DSLContext dslContext = dslContextProvider.get(tenantId);

        LocalDateTime lastExecutionTime = DateTimeConverter.instantToLocalDateTime(lastExecutionInstant);
        LocalDateTime nextExecutionTime = DateTimeConverter.instantToLocalDateTime(nextExecutionInstant);
        AutomatedInvoiceRuleExecutionRecord record = createAutomatedInvoiceRuleExecutionRecord(
            automatedInvoiceRule,
            entityId,
            tenantId,
            lastExecutionTime,
            nextExecutionTime
        );

        int rowCount = dslContext
            .insertInto(AUTOMATED_INVOICE_RULE_EXECUTION)
            .set(record)
            .onConflict(
                AUTOMATED_INVOICE_RULE_EXECUTION.TENANT_ID,
                AUTOMATED_INVOICE_RULE_EXECUTION.ENTITY_ID,
                AUTOMATED_INVOICE_RULE_EXECUTION.AUTOMATED_INVOICE_RULE_ID
            )
            .doUpdate()
            .set(AUTOMATED_INVOICE_RULE_EXECUTION.LAST_EXECUTION_DATE, lastExecutionTime)
            .set(AUTOMATED_INVOICE_RULE_EXECUTION.NEXT_EXECUTION_DATE, nextExecutionTime)
            .where(AUTOMATED_INVOICE_RULE_EXECUTION.AUTOMATED_INVOICE_RULE_ID.eq(automatedInvoiceRule.getAutomatedInvoiceRuleId()))
            .and(AUTOMATED_INVOICE_RULE_EXECUTION.TENANT_ID.eq(tenantId))
            .and(AUTOMATED_INVOICE_RULE_EXECUTION.ENTITY_ID.eq(entityId))
            .execute();

        if (rowCount != 1) {
            throw new ServiceFailureException(
                String.format(
                    "Failed to update last and next execution dates for entity. Invoice rule ID: %s, Tenant id: %s, Entity id: %s",
                    automatedInvoiceRule.getName(),
                    tenantId,
                    entityId
                )
            );
        }
    }

    private static AutomatedInvoiceRuleExecutionRecord createAutomatedInvoiceRuleExecutionRecord(
        AutomatedInvoiceRule automatedInvoiceRule,
        String entityId,
        String tenantId,
        LocalDateTime lastExecutionTime,
        LocalDateTime nextExecutionTime
    ) {
        AutomatedInvoiceRuleExecutionRecord record = new AutomatedInvoiceRuleExecutionRecord();
        record.setTenantId(tenantId);
        record.setEntityId(entityId);
        record.setAutomatedInvoiceRuleId(automatedInvoiceRule.getAutomatedInvoiceRuleId());
        record.setLastExecutionDate(lastExecutionTime);
        record.setNextExecutionDate(nextExecutionTime);
        return record;
    }

    public Optional<AutomatedInvoiceRuleExecution> getAutomatedInvoiceRuleExecutionForEntityId(
        String automatedInvoiceRuleId,
        String tenantId,
        String entityId
    ) {
        DSLContext dslContext = dslContextProvider.get(tenantId);

        AutomatedInvoiceRuleExecutionRecord record = dslContext
            .select()
            .from(AUTOMATED_INVOICE_RULE_EXECUTION)
            .where(AUTOMATED_INVOICE_RULE_EXECUTION.TENANT_ID.eq(tenantId))
            .and(AUTOMATED_INVOICE_RULE_EXECUTION.ENTITY_ID.eq(entityId))
            .and(AUTOMATED_INVOICE_RULE_EXECUTION.AUTOMATED_INVOICE_RULE_ID.eq(automatedInvoiceRuleId))
            .fetchOneInto(AutomatedInvoiceRuleExecutionRecord.class);

        return Optional.ofNullable(automatedInvoiceRuleMapper.recordToAutomatedInvoiceRuleExecution(record));
    }
}
