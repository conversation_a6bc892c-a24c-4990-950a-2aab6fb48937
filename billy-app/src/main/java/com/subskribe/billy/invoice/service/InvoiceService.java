package com.subskribe.billy.invoice.service;

import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceChargeInclusionOption;
import com.subskribe.billy.invoice.model.InvoiceGenerationCheckResult;
import com.subskribe.billy.invoice.model.InvoiceGenerationMethod;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.model.UpdateInvoiceRequest;
import com.subskribe.billy.metrics.datadog.AllMetrics;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import java.util.UUID;
import org.jooq.Configuration;

@AllMetrics
@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public interface InvoiceService {
    Invoice updateInvoice(Invoice.Number invoiceNumber, UpdateInvoiceRequest updateInvoiceRequest);

    void updateInvoiceErpId(Invoice.Number invoiceNumber, String erpId);

    // TODO: Add unit tests for post and delete
    Invoice postInvoice(Invoice.Number draftInvoiceNumber);

    Invoice postInvoice(Invoice.Number draftInvoiceNumber, boolean isHistorical);

    Invoice deleteInvoice(Invoice.Number draftInvoiceNumber);

    Invoice deleteVoidedInvoice(Configuration configuration, Invoice.Number draftInvoiceNumber);

    List<InvoicePreview> previewAllInvoicesBySubscriptionId(String subscriptionId);

    InvoicePreview previewInvoiceByOrderPeriod(String orderId);

    InvoicePreview previewInvoiceByOrderPeriod(Order order);

    List<InvoicePreview> previewAllInvoicesForOrder(String orderId);

    InvoicePreview previewInvoiceByOrderPeriodForOrderLinesCalculation(Order order);

    // todo: generate multiple invoices, one for each period?
    InvoicePreview previewInvoiceByOrderPeriod(Order order, boolean skipAmendPercentOf, boolean forOrderLineCalculation);

    InvoicePreview previewInvoiceByOrderBillingCycle(Order order);

    InvoicePreview generateInvoicePreview(
        Order order,
        boolean skipAmendPercentOf,
        boolean forOrderLineCalculation,
        ProrationConfig prorationConfig,
        TimeZone timeZone
    );

    void triggerOrderExecutionInvoiceDependencies(String orderId);

    MemoizedInvoiceLineItem memoizeInvoiceLineItem(String orderId, String orderLineItemId, MemoizedInvoiceLineItem memoizedInvoiceLineItem);

    Optional<MemoizedInvoiceLineItem> deleteMemoizedInvoiceItem(UUID id);

    List<InvoicePreview> previewInvoicesByBillingPeriods(String orderId);

    List<BillingPeriod> getBillingPeriods(Order order);

    Optional<Invoice> generateInvoice(
        String subscriptionId,
        Instant invoiceTargetDate,
        Optional<Instant> invoiceDate,
        InvoiceChargeInclusionOption invoiceChargeInclusionOption,
        Optional<InvoiceGenerationMethod> generationMethod,
        Optional<String> generatedBy
    );

    Optional<Invoice> generateInvoiceForOrder(
        String orderId,
        List<String> orderLineItemIds,
        Instant invoiceTargetDate,
        Optional<Instant> invoiceDate,
        InvoiceChargeInclusionOption invoiceChargeInclusionOption,
        Optional<InvoiceGenerationMethod> generationMethod,
        Optional<String> generatedBy
    );

    InvoiceGenerationCheckResult performInvoiceGenerationCheck(
        String subscriptionId,
        Instant invoiceTargetDate,
        InvoiceChargeInclusionOption invoiceChargeInclusionOption
    );

    Optional<Invoice> getDraftInvoiceForSubscription(String subscriptionId);

    Optional<InvoiceItem> getPostedInvoiceItemByOrderLineIdAndDebookPeriod(String orderLineId, Period debookPeriod);

    List<InvoiceItem> getPostedInvoiceItemsBySubscriptionIdChargeId(String subscriptionId, String chargeId);

    // gets usage invoice items that haven't yet been billed, including current (potentially incomplete period)
    // this can be used to preview usage invoices that have yet to be posted
    List<InvoiceItem> getUnbilledUsageInvoiceItems(String subscriptionId);

    List<InvoiceItem> getUsageInvoiceItemsForSubscriptionCharge(String subscriptionId, String chargeId);

    Optional<InvoiceItem> getLatestInvoiceItemBySubscriptionChargeGroupId(String subscriptionChargeGroupId);

    Optional<InvoiceItem> getInvoiceItemById(UUID invoiceItemId);

    void modifyInvoiceDueDateForTesting(String invoiceNumber, Long dueDate);

    void deleteInvoiceDataForSubscription(Configuration configuration, String subscriptionId);

    void deleteInvoiceDataForOrder(Configuration configuration, String orderId);

    boolean updateInvoiceGeneratedBy(String invoiceNumber, String generatedBy);

    boolean updateEmailLastSentOn(String invoiceNumber, Instant emailSentOn);

    Optional<Invoice> generateInvoiceForTaxVerification(
        String subscriptionId,
        Optional<Instant> invoiceDate,
        List<InvoiceItem> invoiceItems,
        Optional<InvoiceGenerationMethod> generationMethod,
        Optional<String> generatedBy,
        AccountContact shippingContact,
        AccountContact billingContact
    );
}
