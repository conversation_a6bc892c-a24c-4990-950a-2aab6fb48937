package com.subskribe.billy.invoice.service.processor;

import static com.subskribe.billy.invoice.service.processor.InvoiceProcessor.getOrderLinesByType;
import static com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService.getItemOverlapPeriod;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.ImmutableInvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.service.PercentOfChargeInvoiceHelper;
import com.subskribe.billy.invoice.service.processor.model.ImmutableInvoicePreviewInput;
import com.subskribe.billy.invoice.service.processor.model.InvoiceGenerationInput;
import com.subskribe.billy.invoice.service.processor.model.InvoicePreviewInput;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.architecture.InvoiceModuleLimitedUse;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;

public class PercentOfInvoiceProcessor implements InvoiceProcessor, EventBasedAndAdhocCustomBillingInvoiceProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(PercentOfInvoiceProcessor.class);

    private final InvoiceDAO invoiceDAO;
    private final RecurringInvoiceProcessor recurringInvoiceProcessor;
    private final PercentOfChargeInvoiceHelper percentOfChargeHelper;
    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;
    private final FeatureService featureService;

    @Inject
    public PercentOfInvoiceProcessor(
        InvoiceDAO invoiceDAO,
        RecurringInvoiceProcessor recurringInvoiceProcessor,
        PercentOfChargeInvoiceHelper percentOfChargeHelper,
        SubscriptionBillingPeriodService subscriptionBillingPeriodService,
        FeatureService featureService
    ) {
        this.invoiceDAO = invoiceDAO;
        this.recurringInvoiceProcessor = recurringInvoiceProcessor;
        this.percentOfChargeHelper = percentOfChargeHelper;
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.featureService = featureService;
    }

    @Override
    public List<InvoiceItem> previewInvoiceItems(InvoicePreviewInput invoicePreviewInput) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public List<InvoiceItem> generateInvoiceItems(InvoiceGenerationInput invoiceGenerationInput) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public List<InvoiceItem> getInvoiceItemsForOrderLineItem(String orderLineId) {
        return invoiceDAO.getInvoiceItemsForOrderLineItem(orderLineId);
    }

    @InvoiceModuleLimitedUse
    public List<InvoiceItem> previewPercentOfInvoiceItems(Order order, boolean skipAmendPercentOf, ImmutableInvoicePreviewInput previewInput) {
        // NOTE : For adhoc custom billing this list would be empty, as the invoice items are generated outside of processors
        List<InvoiceItem> allRecurringInvoiceItems = recurringInvoiceProcessor.previewInvoiceItems(
            // NOTE: we need to get all recurring lines for percent of charge because we need to assess all charges involved
            // NOTE: the lineItems and lineItemsNetEffect are the same for Order Creation/Order Cancellation/Order Renewal
            // NOTE: the only place where they differ is for amendment and for amendment of percent of charge we need the full context
            // NOTE: and since we de-book and re-book all lines involved during amendment here we pass line items and not net effect line items
            previewInput.withOrderLineItemsNetEffect(order.getLineItems())
        );
        List<InvoiceItem> percentOfInvoiceItems = percentOfChargeHelper.previewPercentOfInvoiceItems(
            order,
            previewInput,
            allRecurringInvoiceItems,
            skipAmendPercentOf
        );

        var percentOfOrderLines = getOrderLinesByType(order.getLineItemsNetEffect(), previewInput.getChargeMap(), ChargeType.PERCENTAGE_OF);

        // todo: this is a temporary solution to preview percent of charge by billing period. Should revisit the preview path
        // Percent of charge preview lines do not consider billing period.
        // Instead, it takes the total target amounts that fall within the percent of charge order line start to end interval and derives the total percent of amount
        // This causes 2 issues:
        // 1) Due to the later splitting by billing period during invoice generation, there can be rounding artifacts
        // 2) Order form invoice preview puts the entirety of the percent of amount in the first billing period
        // This block takes the generated percent of amount and splits it by billing period invoking the same code as invoice generation
        percentOfInvoiceItems = splitPercentOfAmountsByBillingPeriod(percentOfOrderLines, percentOfInvoiceItems, previewInput);
        return percentOfInvoiceItems;
    }

    private List<InvoiceItem> splitPercentOfAmountsByBillingPeriod(
        List<OrderLineItem> percentOfOrderLineItems,
        List<InvoiceItem> percentOfInvoiceItems,
        InvoicePreviewInput previewInput
    ) {
        InvoiceBillingInfo invoiceBillingInfo = previewInput.getInvoiceBillingInfo();
        List<BillingPeriod> billingPeriods = previewInput.getTopLevelBillingPeriods();
        Map<String, Charge> chargeMap = previewInput.getChargeMap();

        Map<String, OrderLineItem> orderLineItemMap = percentOfOrderLineItems
            .stream()
            .collect(Collectors.toMap(OrderLineItem::getOrderLineId, Function.identity()));

        List<InvoiceItem> invoiceItemsByBillingPeriod = new ArrayList<>();
        for (InvoiceItem invoiceItem : percentOfInvoiceItems) {
            OrderLineItem orderLineItem = orderLineItemMap.get(invoiceItem.getOrderLineItemId());

            // For recurrence custom billing, we would use the billing periods from the custom billing invoicing input,
            // For adhoc custom billing it will not be present as we generate invoice items outside of processors
            Optional<List<BillingPeriod>> customBillingPeriods = getCustomBillingPeriodsIfApplicable(previewInput, orderLineItem);
            List<BillingPeriod> topLevelBillingPeriods = customBillingPeriods.orElse(billingPeriods);

            orderLineItem.setAmount(invoiceItem.getAmount());
            orderLineItem.setListAmount(invoiceItem.getListAmount());
            orderLineItem.setListAmountBeforeOverride(invoiceItem.getListAmountBeforeOverride());
            orderLineItem.setListUnitPrice(invoiceItem.getListUnitPrice());
            orderLineItem.setListUnitPriceBeforeOverride(invoiceItem.getListUnitPriceBeforeOverride());
            orderLineItem.setSellUnitPrice(invoiceItem.getSellUnitPrice());

            Charge charge = chargeMap.get(orderLineItem.getChargeId());

            List<BillingPeriod> finalBillingPeriods = subscriptionBillingPeriodService.getOrderLineBillingPeriods(
                orderLineItem,
                chargeMap,
                invoiceBillingInfo,
                topLevelBillingPeriods
            );

            List<InvoiceItem> invoiceItemsForOrderLine = new ArrayList<>();
            for (BillingPeriod billingPeriod : finalBillingPeriods) {
                percentOfChargeHelper
                    .getPercentOfInvoiceItemForBillingPeriod(
                        invoiceBillingInfo,
                        billingPeriod,
                        orderLineItem,
                        charge,
                        false,
                        invoiceItem.getInlineDiscounts(),
                        invoiceItem.getPredefinedDiscounts()
                    )
                    .map(invoiceItemsForOrderLine::add);
            }
            invoiceItemsForOrderLine = distributeRemainderAmountToInvoiceItems(
                invoiceItemsForOrderLine,
                invoiceItem.getAmount(),
                invoiceItem.getListAmount(),
                RemainderDistributionMethod.DISTRIBUTE_TO_GREATEST_DIFFERENCE
            );
            invoiceItemsByBillingPeriod.addAll(invoiceItemsForOrderLine);
        }
        return invoiceItemsByBillingPeriod;
    }

    enum RemainderDistributionMethod {
        DISTRIBUTE_TO_LAST_ITEM,
        DISTRIBUTE_TO_FIRST_ITEM,
        DISTRIBUTE_TO_GREATEST_DIFFERENCE,
    }

    private List<InvoiceItem> distributeRemainderAmountToInvoiceItems(
        List<InvoiceItem> inputInvoiceItemsForOrderLine,
        BigDecimal expectedAmount,
        BigDecimal expectedListAmount,
        RemainderDistributionMethod distributionMethod
    ) {
        List<InvoiceItem> invoiceItemsForOrderLine = new ArrayList<>(inputInvoiceItemsForOrderLine);

        // split into two because the rounding on list amount and sell amount can actually be different
        distributeListAmount(invoiceItemsForOrderLine, expectedListAmount, distributionMethod);
        distributeSellAmount(invoiceItemsForOrderLine, expectedAmount, distributionMethod);

        validateInvoiceItemInvariants(invoiceItemsForOrderLine, expectedAmount, expectedListAmount);

        if (!featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)) {
            return inputInvoiceItemsForOrderLine;
        }
        return invoiceItemsForOrderLine;
    }

    private void distributeListAmount(
        List<InvoiceItem> invoiceItemsForOrderLine,
        BigDecimal expectedListAmount,
        RemainderDistributionMethod distributionMethod
    ) {
        BigDecimal actualListAmount = invoiceItemsForOrderLine.stream().map(InvoiceItem::getListAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal remainderListAmount = expectedListAmount.subtract(actualListAmount);
        if (Numbers.isZero(remainderListAmount)) {
            return;
        }
        int selectedIndex =
            switch (distributionMethod) {
                case DISTRIBUTE_TO_LAST_ITEM -> invoiceItemsForOrderLine.size() - 1;
                case DISTRIBUTE_TO_FIRST_ITEM -> 0;
                case DISTRIBUTE_TO_GREATEST_DIFFERENCE -> getIndexOfGreatestOffset(
                    invoiceItemsForOrderLine,
                    remainderListAmount,
                    InvoiceItem::getListAmount
                );
            };

        InvoiceItem selectedItem = invoiceItemsForOrderLine.remove(selectedIndex);
        BigDecimal newListAmount = selectedItem.getListAmount().add(remainderListAmount);
        BigDecimal discountAmount = newListAmount.subtract(selectedItem.getAmount());
        InvoiceItem replacementItem = InvoiceItem.InvoiceItemBuilder.builder()
            .from(selectedItem)
            .listAmount(selectedItem.getListAmount().add(remainderListAmount))
            .discountAmount(discountAmount)
            .createInvoiceItem();
        invoiceItemsForOrderLine.add(selectedIndex, replacementItem);
    }

    private void distributeSellAmount(
        List<InvoiceItem> invoiceItemsForOrderLine,
        BigDecimal expectedAmount,
        RemainderDistributionMethod distributionMethod
    ) {
        BigDecimal actualAmount = invoiceItemsForOrderLine.stream().map(InvoiceItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal remainderAmount = expectedAmount.subtract(actualAmount);
        if (Numbers.isZero(remainderAmount)) {
            return;
        }
        int selectedIndex =
            switch (distributionMethod) {
                case DISTRIBUTE_TO_LAST_ITEM -> invoiceItemsForOrderLine.size() - 1;
                case DISTRIBUTE_TO_FIRST_ITEM -> 0;
                case DISTRIBUTE_TO_GREATEST_DIFFERENCE -> getIndexOfGreatestOffset(invoiceItemsForOrderLine, remainderAmount, InvoiceItem::getAmount);
            };

        InvoiceItem selectedItem = invoiceItemsForOrderLine.remove(selectedIndex);
        BigDecimal newSellAmount = selectedItem.getAmount().add(remainderAmount);
        BigDecimal discountAmount = selectedItem.getListAmount().subtract(newSellAmount);
        InvoiceItem replacementItem = InvoiceItem.InvoiceItemBuilder.builder()
            .from(selectedItem)
            .amount(newSellAmount)
            .discountAmount(discountAmount)
            .createInvoiceItem();
        invoiceItemsForOrderLine.add(selectedIndex, replacementItem);
    }

    private void validateInvoiceItemInvariants(List<InvoiceItem> invoiceItemsForOrderLine, BigDecimal expectedAmount, BigDecimal expectedListAmount) {
        String splitId = UUID.randomUUID().toString();
        LOGGER.info("split {}: validating invoice items for order line: {} {}", splitId, expectedListAmount, expectedAmount);
        for (int i = 0; i < invoiceItemsForOrderLine.size(); i++) {
            InvoiceItem invoiceItem = invoiceItemsForOrderLine.get(i);
            LOGGER.info("split {}: {} - {} {} ", splitId, i, invoiceItem.getListAmount(), invoiceItem.getAmount());

            boolean sumCorrect = Numbers.equals(invoiceItem.getListAmount(), invoiceItem.getAmount().add(invoiceItem.getDiscountAmount()));
            if (!sumCorrect && featureService.isEnabled(Feature.ROUNDED_DISCOUNTS)) {
                String message = String.format(
                    "sell amount (%s) + discount amount (%s) does not match list amount (%s)",
                    invoiceItem.getAmount(),
                    invoiceItem.getDiscountAmount(),
                    invoiceItem.getListAmount()
                );
                triggerInvariant(message);
            }

            if (invoiceItem.getListAmount().compareTo(invoiceItem.getAmount()) < 0) {
                String message = String.format(
                    "sell amount (%s) is greater than list amount (%s)",
                    invoiceItem.getAmount(),
                    invoiceItem.getListAmount()
                );
                triggerInvariant(message);
            }
        }

        // validate list amount matches order line list amount
        BigDecimal actualListAmount = invoiceItemsForOrderLine.stream().map(InvoiceItem::getListAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (!Numbers.equals(actualListAmount, expectedListAmount)) {
            String message = String.format("expected list amount %s does not match actual list amount %s", expectedListAmount, actualListAmount);
            triggerInvariant(message);
        }

        // validate sell amount matches order line sell amount
        BigDecimal actualAmount = invoiceItemsForOrderLine.stream().map(InvoiceItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (!Numbers.equals(actualAmount, expectedAmount)) {
            String message = String.format("expected amount %s does not match actual amount %s", expectedAmount, actualAmount);
            triggerInvariant(message);
        }
    }

    private void triggerInvariant(String message) {
        if (featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)) {
            throw new InvariantCheckFailedException(message);
        } else {
            LOGGER.warn(String.format("PERCENT_OF_INVARIANT: %s", message));
        }
    }

    private int getIndexOfGreatestOffset(
        List<InvoiceItem> invoiceItemsForOrderLine,
        BigDecimal remainderAmount,
        Function<InvoiceItem, BigDecimal> amountExtractor
    ) {
        BigDecimal sign = BigDecimal.ONE;
        if (Numbers.isNegative(remainderAmount)) {
            sign = sign.negate();
        }
        int greatestOffsetIndex = 0;
        BigDecimal greatestOffset = BigDecimal.ZERO;
        for (int i = 0; i < invoiceItemsForOrderLine.size(); i++) {
            InvoiceItem item = invoiceItemsForOrderLine.get(i);
            BigDecimal amount = amountExtractor.apply(item).multiply(sign);
            if (amount.compareTo(greatestOffset) > 0) {
                greatestOffset = amount;
                greatestOffsetIndex = i;
            }
        }
        return greatestOffsetIndex;
    }

    @InvoiceModuleLimitedUse
    public List<InvoiceItem> generatePercentOfInvoiceItems(
        InvoiceGenerationInput invoiceGenerationInput,
        List<OrderLineItem> percentOfOrderLines,
        List<InvoiceItem> invoiceItemsFromMemoization
    ) {
        List<OrderLineItem> standardPercentOfOrderLines = getNonEventBasedAndNonAdhocCustomBillingOrderLines(
            percentOfOrderLines,
            invoiceGenerationInput.getChargeMap(),
            invoiceGenerationInput
        );
        // We don't support event based order lines for percent of charge so no check for event based order lines
        List<OrderLineItem> adhocCustomBillingOrderLines = getAdhocCustomBillingOrderLines(percentOfOrderLines, standardPercentOfOrderLines);

        List<InvoiceItem> standardPercentOfInvoiceItems = standardPercentOfOrderLines
            .stream()
            .map(orderItem -> generateInvoiceItemsForPercentOrderLine(invoiceGenerationInput, orderItem, invoiceItemsFromMemoization))
            .flatMap(List::stream)
            .toList();

        List<InvoiceItem> adhocCustomBillingInvoiceItems = generateAdhocCustomBillingInvoiceItems(
            invoiceGenerationInput,
            adhocCustomBillingOrderLines
        );

        return Stream.of(standardPercentOfInvoiceItems, adhocCustomBillingInvoiceItems).flatMap(List::stream).toList();
    }

    private List<InvoiceItem> generateInvoiceItemsForPercentOrderLine(
        InvoiceGenerationInput invoiceGenerationInput,
        OrderLineItem orderItem,
        List<InvoiceItem> invoiceItemsFromMemoization
    ) {
        List<BillingPeriod> allBillingPeriods = getAllSubscriptionBillingPeriods(invoiceGenerationInput, orderItem);

        List<InvoiceItem> invoiceItemsForOrderLine = allBillingPeriods
            .stream()
            .flatMap(billingPeriod ->
                generatePercentOfInvoiceItemForBillingPeriod(invoiceGenerationInput, billingPeriod, orderItem, invoiceItemsFromMemoization).stream()
            )
            .toList();
        invoiceItemsForOrderLine = distributeRemainderAmountToInvoiceItems(
            invoiceItemsForOrderLine,
            orderItem.getAmount(),
            orderItem.getListAmount(),
            RemainderDistributionMethod.DISTRIBUTE_TO_GREATEST_DIFFERENCE
        );

        Optional<Instant> latestInvoiceItemEndDate = invoiceDAO.getLatestInvoiceItemEndDateForOrderLineItem(
            orderItem.getOrderLineId(),
            invoiceGenerationInput.getChargeMap().get(orderItem.getChargeId()),
            invoiceGenerationInput.getInvoiceBillingInfo().getSubscriptionStart()
        );
        return invoiceItemsForOrderLine
            .stream()
            .filter(invoiceItem -> !isInvoiceItemAlreadyInvoiced(invoiceItem, latestInvoiceItemEndDate))
            .filter(invoiceItem -> invoiceItemIncludedInInvoicePeriod(invoiceItem, invoiceGenerationInput.getInvoiceBillingInfo()))
            .toList();
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private boolean isInvoiceItemAlreadyInvoiced(InvoiceItem invoiceItem, Optional<Instant> latestInvoiceItemEndDate) {
        return latestInvoiceItemEndDate.isPresent() && invoiceItem.getTriggerOn().compareTo(latestInvoiceItemEndDate.get()) <= 0;
    }

    private boolean invoiceItemIncludedInInvoicePeriod(InvoiceItem invoiceItem, InvoiceBillingInfo invoiceBillingInfo) {
        return invoiceItem.getTriggerOn().compareTo(invoiceBillingInfo.getInvoiceTargetDate()) <= 0;
    }

    // Fetch all subscription billing periods instead of just the ones for the invoice target date.
    // Without using full memoization we cannot distribute the remainder amounts properly.
    private List<BillingPeriod> getAllSubscriptionBillingPeriods(InvoiceGenerationInput invoiceGenerationInput, OrderLineItem orderItem) {
        List<BillingPeriod> subscriptionBillingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            invoiceGenerationInput.getSubscription(),
            invoiceGenerationInput.getInvoiceBillingInfo().getTimeZone(),
            invoiceGenerationInput.getSubscription().getEndDate()
        );
        InvoiceBillingInfo percentOfBillingInfo = ImmutableInvoiceBillingInfo.copyOf(
            invoiceGenerationInput.getInvoiceBillingInfo()
        ).withInvoiceTargetDate(invoiceGenerationInput.getSubscription().getEndDate());
        return subscriptionBillingPeriodService.getOrderLineBillingPeriods(
            orderItem,
            invoiceGenerationInput.getChargeMap(),
            percentOfBillingInfo,
            subscriptionBillingPeriods
        );
    }

    private Optional<InvoiceItem> generatePercentOfInvoiceItemForBillingPeriod(
        InvoiceGenerationInput invoiceGenerationInput,
        BillingPeriod billingPeriod,
        OrderLineItem orderItem,
        List<InvoiceItem> invoiceItemsFromMemoization
    ) {
        Map<String, List<MemoizedInvoiceLineItem>> memoizedByOrderLineMap = invoiceGenerationInput.getMemoizedItemsByOrderLine();
        if (memoizedByOrderLineMap.containsKey(orderItem.getOrderLineId())) {
            // TODO : Once we are confident that memoization is working fine we should be using these lines instead of regenerating via helper
            // NOTE : for now we are just storing these lines to compare them with the lines generated by helper
            invoiceItemsFromMemoization.addAll(getMemoizedInvoiceItemsForBillingPeriod(memoizedByOrderLineMap, orderItem, billingPeriod));
        }
        InvoiceBillingInfo invoiceBillingInfo = invoiceGenerationInput.getInvoiceBillingInfo();
        return percentOfChargeHelper.getPercentOfInvoiceItemForBillingPeriod(
            invoiceBillingInfo,
            billingPeriod,
            orderItem,
            invoiceGenerationInput.getChargeMap().get(orderItem.getChargeId()),
            true,
            List.of(), // we don't use the individual discounts from memoization
            List.of() // we don't use the individual discounts from memoization
        );
    }

    private List<InvoiceItem> getMemoizedInvoiceItemsForBillingPeriod(
        Map<String, List<MemoizedInvoiceLineItem>> memoizedByOrderLineMap,
        OrderLineItem orderItem,
        BillingPeriod billingPeriod
    ) {
        Period orderItemBillingPeriodOverlap = getItemOverlapPeriod(orderItem.getEffectiveDate(), orderItem.getEndDate(), billingPeriod);
        return memoizedByOrderLineMap
            .get(orderItem.getOrderLineId())
            .stream()
            .filter(
                memoizedInvoiceItem ->
                    memoizedInvoiceItem.getPeriodStartDate().compareTo(orderItemBillingPeriodOverlap.getStart()) >= 0 &&
                    memoizedInvoiceItem.getPeriodEndDate().compareTo(orderItemBillingPeriodOverlap.getEnd()) <= 0
            )
            .map(memoizedInvoiceLineItem -> memoizedInvoiceLineItem.to(orderItem))
            .toList();
    }
}
