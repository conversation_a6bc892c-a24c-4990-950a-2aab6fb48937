package com.subskribe.billy.invoice.service.processor;

import static com.subskribe.billy.invoice.service.processor.InvoiceProcessorUtility.getOrderLinesByType;

import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.ProrationCalculator;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.ImmutableInvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceBillingInfo;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.processor.model.InvoiceGenerationInput;
import com.subskribe.billy.invoice.service.processor.model.InvoicePreviewInput;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import javax.inject.Inject;

public class PercentOfInvoiceProcessor implements InvoiceProcessor, EventBasedAndAdhocCustomBillingInvoiceProcessor {

    private final SubscriptionBillingPeriodService subscriptionBillingPeriodService;
    private final InvoiceDAO invoiceDAO;

    @Inject
    public PercentOfInvoiceProcessor(SubscriptionBillingPeriodService subscriptionBillingPeriodService, InvoiceDAO invoiceDAO) {
        this.subscriptionBillingPeriodService = subscriptionBillingPeriodService;
        this.invoiceDAO = invoiceDAO;
    }

    @Override
    public List<InvoiceItem> previewInvoiceItems(InvoicePreviewInput invoicePreviewInput) {
        return List.of();
    }

    @Override
    public List<InvoiceItem> generateInvoiceItems(InvoiceGenerationInput invoiceGenerationInput) {
        List<OrderLineItem> percentOfOrderLines = getOrderLinesByType(
            invoiceGenerationInput.getOrderLineItemsNetEffect(),
            invoiceGenerationInput.getChargeMap(),
            ChargeType.PERCENTAGE_OF
        );

        List<OrderLineItem> standardPercentOfOrderLines = getNonEventBasedAndNonAdhocCustomBillingOrderLines(
            percentOfOrderLines,
            invoiceGenerationInput.getChargeMap(),
            invoiceGenerationInput
        );
        // We don't support event based order lines for percent of charge so no check for event based order lines
        List<OrderLineItem> adhocCustomBillingOrderLines = getAdhocCustomBillingOrderLines(percentOfOrderLines, standardPercentOfOrderLines);

        List<InvoiceItem> standardPercentOfInvoiceItems = generatePercentOfInvoiceItems(
            invoiceGenerationInput.getChargeMap(),
            invoiceGenerationInput.getInvoiceBillingInfo(),
            invoiceGenerationInput.getTopLevelBillingPeriods(),
            standardPercentOfOrderLines
        );

        List<InvoiceItem> adhocCustomBillingInvoiceItems = generateAdhocCustomBillingInvoiceItems(
            invoiceGenerationInput,
            adhocCustomBillingOrderLines
        );

        return Stream.of(standardPercentOfInvoiceItems, adhocCustomBillingInvoiceItems).flatMap(List::stream).toList();
    }

    private List<InvoiceItem> generatePercentOfInvoiceItems(
        Map<String, Charge> chargeMap,
        InvoiceBillingInfo invoiceBillingInfo,
        List<BillingPeriod> billingPeriods,
        List<OrderLineItem> percentOfOrderLines
    ) {
        return percentOfOrderLines
            .stream()
            .map(orderItem -> generateInvoiceItemsForPercentOrderLine(chargeMap, invoiceBillingInfo, billingPeriods, orderItem))
            .flatMap(List::stream)
            .toList();
    }

    private List<InvoiceItem> generateInvoiceItemsForPercentOrderLine(
        Map<String, Charge> chargeMap,
        InvoiceBillingInfo invoiceBillingInfo,
        List<BillingPeriod> billingPeriods,
        OrderLineItem orderItem
    ) {
        List<BillingPeriod> finalBillingPeriods = subscriptionBillingPeriodService.getOrderLineBillingPeriods(
            orderItem,
            chargeMap,
            invoiceBillingInfo,
            billingPeriods
        );

        return finalBillingPeriods
            .stream()
            .filter(billingPeriod ->
                isOrderLineEligibleForInvoicing(
                    chargeMap,
                    invoiceBillingInfo.getInvoiceTargetDate(),
                    invoiceBillingInfo.getSubscriptionEnd(),
                    orderItem,
                    billingPeriod
                )
            )
            .map(billingPeriod ->
                getPercentOfInvoiceItemForBillingPeriod(
                    billingPeriod,
                    orderItem,
                    chargeMap.get(orderItem.getChargeId()),
                    invoiceBillingInfo
                ).orElseThrow(() ->
                    new ServiceFailureException(
                        String.format(
                            "Could not generate invoice item for percent of order line: %s within period %s to %s",
                            orderItem.getOrderLineId(),
                            billingPeriod.getStart(),
                            billingPeriod.getEnd()
                        )
                    )
                )
            )
            .toList();
    }

    private boolean isOrderLineEligibleForInvoicing(
        Map<String, Charge> chargeMap,
        Instant invoiceTargetDate,
        Instant subscriptionEndDate,
        OrderLineItem orderItem,
        BillingPeriod billingPeriod
    ) {
        if (!orderLineItemInPeriod(orderItem, billingPeriod, chargeMap.get(orderItem.getChargeId()), invoiceTargetDate, subscriptionEndDate)) {
            return false;
        }
        Set<String> orderLineItemIdsAlreadyInvoiced = new HashSet<>(
            invoiceDAO.getAllOrderLineIdsWithInvoiceItemsPastThreshold(List.of(orderItem.getOrderLineId()), billingPeriod.getStart())
        );
        return !orderLineItemIdsAlreadyInvoiced.contains(orderItem.getOrderLineId());
    }

    private static boolean orderLineItemInPeriod(
        OrderLineItem orderLineItem,
        BillingPeriod billingPeriod,
        Charge charge,
        Instant invoiceTargetDate,
        Instant subscriptionEndDate
    ) {
        // there may be a stub that extends past the end of the subscription. If the charge is billed in Arrears, adjustment is needed to cover the entire remaining duration
        // example: order line from Jan 1, 2023 - Dec 31, 2023. Yearly billing with first full billing cycle Feb 1, 2023.
        // in this case the second billing period is Feb 1, 2023 - Jan 31, 2024. When generating invoice / preview with target date = subscription end date
        // the target date is before the billing period end date, which means the invoice for that period doesn't get generated
        // adjusting the end date to the earlier of the subscription end date or billing period end date ensures the last billing period is always covered
        Instant periodEnd = billingPeriod.getEnd().isBefore(subscriptionEndDate) ? billingPeriod.getEnd() : subscriptionEndDate;

        if (charge.getBillingTerm() == BillingTerm.IN_ARREARS && periodEnd.isAfter(invoiceTargetDate)) {
            return false;
        }
        return orderLineItem.getEffectiveDate().isBefore(billingPeriod.getEnd()) && orderLineItem.getEndDate().isAfter(billingPeriod.getStart());
    }

    Optional<InvoiceItem> getPercentOfInvoiceItemForBillingPeriod(
        BillingPeriod billingPeriod,
        OrderLineItem orderLineItem,
        Charge charge,
        InvoiceBillingInfo billingInfo
    ) {
        Validator.checkStateInternal(
            charge.getType() == ChargeType.PERCENTAGE_OF,
            "Charge argument for percent of invoice generation not percent of type"
        );
        Validator.checkStateInternal(
            orderLineItem.getChargeId().equals(charge.getChargeId()),
            "Order line item charge id does not match required charge id"
        );

        Period lineItemPeriod = Period.between(orderLineItem.getEffectiveDate(), orderLineItem.getEndDate());
        Period effectiveBillingPeriod = Period.between(billingPeriod.getStart(), billingPeriod.getEnd());

        if (Period.overlapOf(lineItemPeriod, effectiveBillingPeriod).isEmpty()) {
            return Optional.empty();
        }

        BigDecimal prorationRatio = ProrationCalculator.getPeriodProrationRatio(
            lineItemPeriod,
            effectiveBillingPeriod,
            billingInfo.getProrationConfig(),
            billingInfo.getTimeZone(),
            Optional.ofNullable(billingInfo.getBillingCycle())
        );

        BigDecimal sellAmountForPeriod = Numbers.makeCurrencyScale(orderLineItem.getAmount().multiply(prorationRatio), billingInfo.getCurrencyCode());
        BigDecimal listAmountForPeriod = Numbers.makeCurrencyScale(
            orderLineItem.getListAmount().multiply(prorationRatio),
            billingInfo.getCurrencyCode()
        );

        // NOTE: For percentOfCharge unit prices, we are prorating during invoice preview which gets stored in order lines
        // so we should not re-prorate them during invoice generation, this was leading to a redivided amount for unit prices
        BigDecimal listUnitPrice = orderLineItem.getListUnitPrice();
        BigDecimal sellUnitPrice = orderLineItem.getSellUnitPrice();

        // TODO: scale with currency from order in the next iteration
        InvoiceItemAmounts periodInvoiceAmounts = ImmutableInvoiceItemAmounts.builder()
            .sellAmount(sellAmountForPeriod)
            .listAmount(listAmountForPeriod)
            .discountAmount(listAmountForPeriod.subtract(sellAmountForPeriod))
            .listUnitPrice(Numbers.makeCurrencyScale(listUnitPrice, billingInfo.getCurrencyCode()))
            .sellUnitPrice(Numbers.makeCurrencyScale(sellUnitPrice, billingInfo.getCurrencyCode()))
            .build();

        InvoiceItem.InvoiceItemBuilder invoiceItemBuilder = new InvoiceItem.InvoiceItemBuilder()
            .entityId(orderLineItem.getEntityId())
            .orderId(orderLineItem.getOrderId())
            .orderLineItemId(orderLineItem.getOrderLineId())
            .chargeId(orderLineItem.getChargeId())
            .subscriptionChargeId(orderLineItem.getExternalSubscriptionChargeId())
            .subscriptionChargeGroupId(orderLineItem.getSubscriptionChargeGroupId())
            .status(InvoiceStatus.DRAFT)
            .listAmount(periodInvoiceAmounts.getListAmount())
            .discountAmount(periodInvoiceAmounts.getDiscountAmount())
            .amount(periodInvoiceAmounts.getSellAmount())
            .listUnitPrice(periodInvoiceAmounts.getListUnitPrice())
            .sellUnitPrice(periodInvoiceAmounts.getSellUnitPrice())
            .taxAmount(BigDecimal.ZERO)
            .quantity(orderLineItem.getQuantity())
            .inlineDiscounts(periodInvoiceAmounts.getLineItemDiscounts())
            .predefinedDiscounts(periodInvoiceAmounts.getTenantDiscounts())
            .periodStartDate(effectiveBillingPeriod.getStart())
            .periodEndDate(effectiveBillingPeriod.getEnd());
        return Optional.of(invoiceItemBuilder.createInvoiceItem());
    }

    @Override
    public List<InvoiceItem> getInvoiceItemsForOrderLineItem(String orderLineId) {
        return invoiceDAO.getInvoiceItemsForOrderLineItem(orderLineId);
    }
}
