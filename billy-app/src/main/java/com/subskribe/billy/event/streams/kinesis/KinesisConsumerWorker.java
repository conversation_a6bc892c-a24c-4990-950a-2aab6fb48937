package com.subskribe.billy.event.streams.kinesis;

import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.apache.commons.lang3.StringUtils;
import software.amazon.kinesis.common.ConfigsBuilder;
import software.amazon.kinesis.common.StreamIdentifier;
import software.amazon.kinesis.coordinator.Scheduler;
import software.amazon.kinesis.processor.SingleStreamTracker;
import software.amazon.kinesis.processor.StreamTracker;
import software.amazon.kinesis.retrieval.RetrievalConfig;
import software.amazon.kinesis.retrieval.polling.PollingConfig;

public class KinesisConsumerWorker {

    private static final Logger LOGGER = LoggerFactory.getLogger(KinesisConsumerWorker.class);

    private static final int CLEAN_SHUTDOWN_TIMEOUT_SECONDS = 20;

    KinesisConsumerWorkerConfig workerConfig;

    public KinesisConsumerWorker(KinesisConsumerWorkerConfig workerConfig) {
        if (StringUtils.isBlank(workerConfig.getStreamName())) {
            throw new ServiceFailureException("stream name cannot be blank here");
        }
        if (StringUtils.isBlank(workerConfig.getConsumerName())) {
            throw new ServiceFailureException("consumer name cannot be blank here");
        }
        if (StringUtils.isBlank(workerConfig.getWorkerIdentifier())) {
            throw new ServiceFailureException("worker identifier cannot be blank here");
        }
        this.workerConfig = workerConfig;
    }

    public void startWorker() {
        StreamTracker streamTracker = new SingleStreamTracker(
            StreamIdentifier.singleStreamInstance(workerConfig.getStreamName()),
            workerConfig.getInitialPositionInStream()
        );

        ConfigsBuilder configsBuilder = new ConfigsBuilder(
            streamTracker,
            workerConfig.getConsumerName(),
            workerConfig.getKinesisAsyncClient(),
            workerConfig.getDynamoDbAsyncClient(),
            workerConfig.getCloudWatchAsyncClient(),
            workerConfig.getWorkerIdentifier(),
            new EventConsumerShardProcessor.EventConsumerShardProcessorFactory(workerConfig.getEventConsumer(), workerConfig.getStreamName())
        );

        Scheduler scheduler = new Scheduler(
            configsBuilder.checkpointConfig(),
            configsBuilder.coordinatorConfig(),
            configsBuilder.leaseManagementConfig(),
            configsBuilder.lifecycleConfig(),
            configsBuilder.metricsConfig(),
            configsBuilder.processorConfig(),
            getRetrievalConfig(workerConfig.getConsumerConsumptionMode(), configsBuilder)
        );

        // add JVM shutdown hook so that the scheduler can make a graceful exit
        Runtime.getRuntime().addShutdownHook(new Thread(() -> _schedulerGracefulShutDownHook(scheduler)));
        LOGGER.info("successfully added shutdown hook for the kinesis consumer scheduler");

        // start a new daemon thread that will perform the worker logic
        Thread schedulerThread = new Thread(scheduler);
        schedulerThread.setDaemon(true);
        schedulerThread.start();
        LOGGER.info("successfully started scheduler daemon thread");
    }

    private RetrievalConfig getRetrievalConfig(ConsumerConsumptionMode consumerConsumptionMode, ConfigsBuilder configsBuilder) {
        return switch (consumerConsumptionMode) {
            case SHARED_SHARD_CAPACITY -> configsBuilder
                .retrievalConfig()
                .retrievalSpecificConfig(new PollingConfig(workerConfig.getStreamName(), workerConfig.getKinesisAsyncClient()));
            case DEDICATED_FAN_OUT -> configsBuilder.retrievalConfig();
        };
    }

    private static void _schedulerGracefulShutDownHook(Scheduler scheduler) {
        Future<Boolean> gracefulShutdownFuture = scheduler.startGracefulShutdown();
        LOGGER.debug("Waiting up to {} seconds for shutdown to complete.", CLEAN_SHUTDOWN_TIMEOUT_SECONDS);
        try {
            gracefulShutdownFuture.get(CLEAN_SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LOGGER.info("Interrupted while waiting for graceful shutdown. Continuing.");
        } catch (ExecutionException e) {
            LOGGER.error("Exception while executing graceful shutdown.", e);
        } catch (TimeoutException e) {
            LOGGER.error("Timeout while waiting for shutdown. Scheduler may not have exited.");
        }
        LOGGER.debug("Completed, shutting down now.");
    }
}
