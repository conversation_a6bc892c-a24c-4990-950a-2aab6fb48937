package com.subskribe.billy.event.model.payload;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.payment.model.PaymentFailureReason;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import java.util.Optional;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
@JsonSerialize(as = ImmutablePaymentRetriesExhaustedEventPayload.class)
@JsonDeserialize(as = ImmutablePaymentRetriesExhaustedEventPayload.class)
public interface PaymentRetriesExhaustedEventPayload extends NotifyingEvent {
    String getPaymentGatewayIntegrationId();

    String getAccountPaymentMethodId();

    String getInvoiceNumber();

    String getPaymentId();

    String getPaymentAttemptId();

    Optional<PaymentFailureReason> getFailureReason();

    Optional<String> getInvoicePaymentManagementUrl();

    @JsonIgnore
    @Override
    default String getEventObjectId() {
        return getPaymentAttemptId();
    }
}
