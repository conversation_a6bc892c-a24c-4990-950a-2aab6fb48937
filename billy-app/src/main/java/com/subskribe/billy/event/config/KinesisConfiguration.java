package com.subskribe.billy.event.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import software.amazon.awssdk.regions.Region;

public class KinesisConfiguration {

    @JsonProperty
    private Boolean useEndpointOverride;

    @JsonProperty
    private String endpointOverride;

    @JsonProperty
    private Region region;

    @JsonProperty
    private Boolean forceSharedCapacityMode;

    @JsonProperty
    public Boolean getUseEndpointOverride() {
        return useEndpointOverride;
    }

    @JsonProperty
    public void setUseEndpointOverride(Boolean useEndpointOverride) {
        this.useEndpointOverride = useEndpointOverride;
    }

    @JsonProperty
    public String getEndpointOverride() {
        return endpointOverride;
    }

    @JsonProperty
    public void setEndpointOverride(String endpointOverride) {
        this.endpointOverride = endpointOverride;
    }

    public Region getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = Region.of(region);
    }

    public void setRegion(Region region) {
        this.region = region;
    }

    public Boolean getForceSharedCapacityMode() {
        return forceSharedCapacityMode;
    }

    public void setForceSharedCapacityMode(Boolean forceSharedCapacityMode) {
        this.forceSharedCapacityMode = forceSharedCapacityMode;
    }
}
