package com.subskribe.billy.event.streams;

import org.apache.commons.lang3.StringUtils;

public enum Stream {
    CATALOG_DOMAIN_STREAM("catalog-domain-stream"),
    ORDER_DOMAIN_STREAM("order-domain-stream"),
    BILLING_DOMAIN_STREAM("billing-domain-stream"),
    REVENUE_DOMAIN_STREAM("revenue-domain-stream"),
    SUBSCRIPTION_DOMAIN_STREAM("subscription-domain-stream"),
    TASK_DOMAIN_STREAM("task-domain-stream");

    private final String canonicalStreamName;

    Stream(String canonicalStreamName) {
        this.canonicalStreamName = canonicalStreamName;
    }

    public String getCanonicalStreamName(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            return canonicalStreamName;
        }

        return String.format("%s-%s", prefix, canonicalStreamName);
    }

    public String getLogicalStreamName() {
        return canonicalStreamName;
    }
}
