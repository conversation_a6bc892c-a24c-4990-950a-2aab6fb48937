package com.subskribe.billy.event.model;

import static com.subskribe.billy.event.streams.Stream.BILLING_DOMAIN_STREAM;
import static com.subskribe.billy.event.streams.Stream.ORDER_DOMAIN_STREAM;
import static com.subskribe.billy.event.streams.Stream.REVENUE_DOMAIN_STREAM;
import static com.subskribe.billy.event.streams.Stream.SUBSCRIPTION_DOMAIN_STREAM;
import static com.subskribe.billy.event.streams.Stream.TASK_DOMAIN_STREAM;

import com.subskribe.billy.event.streams.Stream;

public enum EventType {
    INVOICE_POSTED(BILLING_DOMAIN_STREAM),
    INVOICE_POSTED_V2(BILLING_DOMAIN_STREAM),
    PAYMENT_PROCESSED(BILLING_DOMAIN_STREAM),
    PAYMENT_ATTEMPT_FAILED(BILLING_DOMAIN_STREAM),
    PAYMENT_RETRIES_EXHAUSTED(BILLING_DOMAIN_STREAM),
    CREDIT_MEMO_POSTED(BILLING_DOMAIN_STREAM),
    REVENUE_RECOGNIZED(REVENUE_DOMAIN_STREAM),
    PAYMENT_VOIDED(BILLING_DOMAIN_STREAM),
    INVOICE_VOIDED(BILLING_DOMAIN_STREAM),
    INVOICE_VOIDED_V2(BILLING_DOMAIN_STREAM),
    REALIZED_GAIN_LOSS_POSTED(BILLING_DOMAIN_STREAM),
    ORDER_SUBMITTED(ORDER_DOMAIN_STREAM),
    ORDER_EXECUTED(ORDER_DOMAIN_STREAM),
    ORDER_APPROVED(ORDER_DOMAIN_STREAM),
    ORDER_REVERTED_TO_DRAFT(ORDER_DOMAIN_STREAM),
    ORDER_APPROVAL_FLOWS_EVALUATED(ORDER_DOMAIN_STREAM),
    SUBSCRIPTION_CREATED(SUBSCRIPTION_DOMAIN_STREAM),
    SUBSCRIPTION_UPDATED(SUBSCRIPTION_DOMAIN_STREAM),
    SUBSCRIPTION_ACTIVATING(SUBSCRIPTION_DOMAIN_STREAM),
    SUBSCRIPTION_ACTIVATED(SUBSCRIPTION_DOMAIN_STREAM),
    SUBSCRIPTION_CANCELLING(SUBSCRIPTION_DOMAIN_STREAM),
    SUBSCRIPTION_CANCELLED(SUBSCRIPTION_DOMAIN_STREAM),
    SUBSCRIPTION_EXPIRING(SUBSCRIPTION_DOMAIN_STREAM),
    SUBSCRIPTION_EXPIRED(SUBSCRIPTION_DOMAIN_STREAM),
    SUBSCRIPTION_CHARGE_CHANGE(SUBSCRIPTION_DOMAIN_STREAM),
    SUBSCRIPTION_DELETED(SUBSCRIPTION_DOMAIN_STREAM),
    ESIGNATURE_COMPLETED(ORDER_DOMAIN_STREAM),
    ESIGNATURE_VOIDED(ORDER_DOMAIN_STREAM),
    TASK_SCHEDULED(TASK_DOMAIN_STREAM),
    TASK_COMPLETED(TASK_DOMAIN_STREAM),
    TASK_EXECUTING(TASK_DOMAIN_STREAM),
    HUBSPOT_SYNC_FAILED(TASK_DOMAIN_STREAM),
    SALESFORCE_SYNC_FAILED(TASK_DOMAIN_STREAM);

    private final Stream stream;

    EventType(Stream stream) {
        this.stream = stream;
    }

    public Stream getStream() {
        return stream;
    }
}
