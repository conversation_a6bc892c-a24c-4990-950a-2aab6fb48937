package com.subskribe.billy.event.streams.kinesis;

import com.subskribe.billy.event.consumer.EventConsumer;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import org.immutables.value.Value;
import software.amazon.awssdk.services.cloudwatch.CloudWatchAsyncClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient;
import software.amazon.awssdk.services.kinesis.KinesisAsyncClient;
import software.amazon.kinesis.common.InitialPositionInStreamExtended;

@BillyModelStyle
@Value.Immutable
public interface KinesisConsumerWorkerConfig {
    String getStreamName();

    String getConsumerName();

    String getWorkerIdentifier();

    EventConsumer getEventConsumer();

    ConsumerConsumptionMode getConsumerConsumptionMode();

    KinesisAsyncClient getKinesisAsyncClient();

    DynamoDbAsyncClient getDynamoDbAsyncClient();

    CloudWatchAsyncClient getCloudWatchAsyncClient();

    InitialPositionInStreamExtended getInitialPositionInStream();
}
