package com.subskribe.billy.event.model.payload;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.payment.model.PaymentFailureReason;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import java.util.Optional;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
@JsonSerialize(as = ImmutableAccountPaymentMethodSuspendedEventPayload.class)
@JsonDeserialize(as = ImmutableAccountPaymentMethodSuspendedEventPayload.class)
public interface AccountPaymentMethodSuspendedEventPayload extends NotifyingEvent {
    String getAccountId();

    String getAccountPaymentMethodId();

    String getSetupUrl();

    String getInvoiceNumber();

    String getFailedPaymentId();

    Optional<PaymentFailureReason> getFailureReason();

    @Override
    @JsonIgnore
    default String getEventObjectId() {
        return getAccountPaymentMethodId();
    }
}
