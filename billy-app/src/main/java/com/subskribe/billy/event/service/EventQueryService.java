package com.subskribe.billy.event.service;

import com.subskribe.billy.event.db.EventDAO;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Duration;
import java.util.List;
import java.util.Set;
import javax.inject.Inject;

// TODO: wire this in
public class EventQueryService {

    private final EventDAO eventDAO;

    private final TenantIdProvider tenantIdProvider;

    @Inject
    public EventQueryService(EventDAO eventDAO, TenantIdProvider tenantIdProvider) {
        this.eventDAO = eventDAO;
        this.tenantIdProvider = tenantIdProvider;
    }

    public List<Event> getEventsOfTypeInDurationPast(Set<EventType> eventTypes, Duration durationInPast) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        return eventDAO.getEventsOfTypeInDurationPast(tenantId, eventTypes, durationInPast);
    }
}
