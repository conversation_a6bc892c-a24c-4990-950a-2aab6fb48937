package com.subskribe.billy.entity.service;

import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.EntityProductMetadata;
import com.subskribe.billy.entity.db.EntityDAO;
import com.subskribe.billy.entity.mapper.EntityMapper;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.model.EntityPatchRequest;
import com.subskribe.billy.entity.model.ImmutableEntity;
import com.subskribe.billy.entity.model.NumberConfig;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.DuplicateObjectException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.InvoiceConfigurationService;
import com.subskribe.billy.order.model.OrderStub;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.revrec.services.RevenueEnablementService;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.image.ImageUtils;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.sanitizers.HtmlSanitizer;
import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.validation.Validator;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import javax.inject.Inject;
import javax.ws.rs.ForbiddenException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.factory.Mappers;

public class EntityService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EntityService.class);

    public static final String DEFAULT_FUNCTIONAL_CURRENCY = "USD";
    public static final String DEFAULT_ENTITY_DISPLAY_ID = "ENT-1";
    private static final int MAX_LOGO_SIZE = 1024 * 1024; // 1MB
    private static final String LOGO_TYPE = "PNG";
    private static final int ERP_ID_MAX_LENGTH = 36;

    private final EntityDAO entityDAO;
    private final EntityGetService entityGetService;
    private final EntityIdGenerator entityIdGenerator;
    private final TenantIdProvider tenantIdProvider;
    private final EntityContextProvider entityContextProvider;
    private final InvoiceConfigurationService invoiceConfigurationService;
    private final OrderGetService orderGetService;
    private final CacheService cacheService;
    private final FeatureService featureService;
    private final EntityMapper entityMapper;
    private final RevenueEnablementService revenueEnablementService;

    @Inject
    public EntityService(
        EntityDAO entityDAO,
        EntityGetService entityGetService,
        EntityIdGenerator entityIdGenerator,
        TenantIdProvider tenantIdProvider,
        EntityContextProvider entityContextProvider,
        InvoiceConfigurationService invoiceConfigurationService,
        OrderGetService orderGetService,
        CacheService cacheService,
        FeatureService featureService,
        RevenueEnablementService revenueEnablementService
    ) {
        this.entityDAO = entityDAO;
        this.entityGetService = entityGetService;
        this.entityIdGenerator = entityIdGenerator;
        this.tenantIdProvider = tenantIdProvider;
        this.entityContextProvider = entityContextProvider;
        this.invoiceConfigurationService = invoiceConfigurationService;
        this.orderGetService = orderGetService;
        this.cacheService = cacheService;
        this.featureService = featureService;
        this.revenueEnablementService = revenueEnablementService;
        entityMapper = Mappers.getMapper(EntityMapper.class);
    }

    // todo: add the created/existing invoice config on the returned entity
    public Entity addEntity(Entity entity) {
        entity = sanitizeEntity(entity);
        String entityId = entityIdGenerator.generate();
        validateDuplicateDefaultEntity(entity);
        validateIfMultipleEntitiesAllowed(entity);
        validateProrationConfig(entity.getProrationScheme(), entity.getProrationMode());
        entity = createInvoiceConfig(entity);
        String tenantId = tenantIdProvider.provideTenantIdString();
        cacheService.invalidateKey(tenantId, CacheType.TENANT_ENTITY_IDS, tenantId);
        Entity createdEntity = entityDAO.addEntity(entity, entityId);
        revenueEnablementService.ensureRevenueEnablementProgressExistsForEntity(createdEntity.getEntityId());
        return createdEntity;
    }

    // todo: validation needed on provided invoice config
    private Entity createInvoiceConfig(Entity entity) {
        if (StringUtils.isNotBlank(entity.getInvoiceConfigId())) {
            // validate that invoice config exists
            invoiceConfigurationService.getInvoiceConfigById(entity.getInvoiceConfigId());
            return entity;
        }
        NumberConfig invoiceConfig = entity.getInvoiceConfig().orElse(NumberConfig.getDefaultInvoiceConfig());
        NumberConfig createdInvoiceConfig = invoiceConfigurationService.createInvoiceConfig(invoiceConfig);
        entity = ImmutableEntity.copyOf(entity).withInvoiceConfigId(createdInvoiceConfig.configId());
        return entity;
    }

    // do not allow to create a second "default" entity
    private void validateDuplicateDefaultEntity(Entity entity) {
        if (BooleanUtils.isTrue(entity.getIsDefault()) && entityDAO.getDefaultEntity().isPresent()) {
            throw new DuplicateObjectException("default entity exists for the tenant");
        }
    }

    // do not allow to create a non-default entity if feature flag is off
    private void validateIfMultipleEntitiesAllowed(Entity entity) {
        if (!featureService.isEnabled(Feature.MULTI_ENTITY_AUTH) && !BooleanUtils.isTrue(entity.getIsDefault())) {
            throw new ForbiddenException("multi entity feature is not enabled");
        }
    }

    private Entity sanitizeEntity(Entity entity) {
        Validator.validateNonNullArgument(entity, "entity");
        Validator.validateStringNotBlank(entity.getName(), "Entity name cannot be blank");
        Validator.validateStringNotBlank(entity.getDisplayId(), "Entity display id cannot be blank");
        if (StringUtils.isNotBlank(entity.getEntityId())) {
            throw new InvalidInputException("entity id present during add operation");
        }

        String trimmedName = StringUtils.trim(entity.getName());
        String erpId = validateAndSanitizeErpId(entity.getErpId());
        Entity sanitizedEntity = ImmutableEntity.copyOf(entity).withName(trimmedName).withErpId(erpId);
        validateDuplicateByName(null, sanitizedEntity.getName());
        validateDuplicateByDisplayId(null, sanitizedEntity.getDisplayId());

        if (StringUtils.isNotBlank(entity.getWireInstruction())) {
            String trimmedWireInstruction = StringUtils.trim(entity.getWireInstruction());
            sanitizedEntity = ImmutableEntity.copyOf(sanitizedEntity).withWireInstruction(trimmedWireInstruction);
        }
        return sanitizedEntity;
    }

    public Entity createDefaultEntityForNewTenant(String name) {
        Entity entity = ImmutableEntity.builder()
            .displayId(DEFAULT_ENTITY_DISPLAY_ID)
            .name(name)
            .prorationScheme(ProrationConfig.Scheme.getDefault())
            .prorationMode(ProrationConfig.Mode.getDefault())
            .functionalCurrency(DEFAULT_FUNCTIONAL_CURRENCY)
            .isDefault(true)
            .build();
        return addEntity(entity);
    }

    // todo: multientity: add rules of update around proration/timezone/number config
    public Entity updateEntity(EntityPatchRequest entityPatchRequest) {
        entityPatchRequest = validateUpdate(entityPatchRequest);
        Entity updatedEntity = entityDAO.patchEntity(entityPatchRequest);
        updateInvoiceConfig(updatedEntity.getInvoiceConfigId(), entityPatchRequest.invoiceConfig());
        cacheService.invalidateKey(tenantIdProvider.provideTenantIdString(), CacheType.ENTITY_BY_ID, entityPatchRequest.entityId());
        cacheService.invalidateKey(tenantIdProvider.provideTenantIdString(), CacheType.ENTITY_BY_DISPLAY_ID, updatedEntity.getDisplayId());
        return updatedEntity;
    }

    public Entity updateEntity(Entity entity) {
        EntityPatchRequest entityPatchRequest = entityMapper.toPatchRequest(entity);
        return updateEntity(entityPatchRequest);
    }

    // todo: ensure that current user has access to all entities with this invoice config
    private void updateInvoiceConfig(String invoiceConfigId, NumberConfig invoiceConfig) {
        if (Objects.nonNull(invoiceConfig)) {
            NumberConfig preparedConfig = new NumberConfig(
                invoiceConfigId,
                invoiceConfig.scheme(),
                invoiceConfig.prefix(),
                invoiceConfig.length(),
                invoiceConfig.nextSequenceNumber()
            );
            invoiceConfigurationService.updateInvoiceConfig(preparedConfig);
        }
    }

    private EntityPatchRequest validateUpdate(EntityPatchRequest entityPatchRequest) {
        Validator.validateNonNullArgument(entityPatchRequest, "entity");
        Validator.validateStringNotBlank(entityPatchRequest.entityId(), "entity id required");
        EntityContext entityContext = entityContextProvider.provideEntityContext();
        if (!entityContext.getSelectedIds().contains(entityPatchRequest.entityId())) {
            throw new InvalidInputException("entity must be selected for update");
        }
        Entity existingEntity = entityDAO
            .getEntityById(entityPatchRequest.entityId())
            .orElseThrow(() -> new InvalidInputException("entity not found"));
        var targetProrationScheme = Optional.ofNullable(entityPatchRequest.prorationScheme()).orElse(existingEntity.getProrationScheme());
        var targetProrationMode = Optional.ofNullable(entityPatchRequest.prorationMode()).orElse(existingEntity.getProrationMode());
        validateProrationConfig(targetProrationScheme, targetProrationMode);
        if (isChangeInProration(existingEntity, entityPatchRequest)) {
            preventEntityUpdateIfOrdersPresent(existingEntity.getEntityId());
        }
        entityPatchRequest = sanitizePatchRequest(entityPatchRequest);
        validateDuplicateByName(entityPatchRequest.entityId(), entityPatchRequest.name());
        validateDuplicateByDisplayId(entityPatchRequest.entityId(), entityPatchRequest.displayId());
        validateAndSanitizeErpId(entityPatchRequest.erpId());
        return entityPatchRequest;
    }

    private void validateDuplicateByName(String entityId, String name) {
        Optional<Entity> duplicateEntity = entityDAO.getEntityByName(name).filter(entity -> !Objects.equals(entity.getEntityId(), entityId));
        if (duplicateEntity.isPresent()) {
            throw new InvalidInputException("entity present with name: " + name);
        }
    }

    private void validateDuplicateByDisplayId(String entityId, String displayId) {
        if (Objects.isNull(displayId)) {
            return;
        }
        Optional<Entity> duplicateEntity = entityDAO
            .getEntityByDisplayId(displayId)
            .filter(entity -> !Objects.equals(entity.getEntityId(), entityId));
        if (duplicateEntity.isPresent()) {
            throw new InvalidInputException("entity present with display id: " + displayId);
        }
    }

    private String validateAndSanitizeErpId(String erpId) {
        if (StringUtils.isBlank(erpId)) {
            return null;
        }
        if (erpId.length() > ERP_ID_MAX_LENGTH) {
            throw new InvalidInputException("erpId must be less than " + ERP_ID_MAX_LENGTH + " characters");
        }
        return erpId;
    }

    private EntityPatchRequest sanitizePatchRequest(EntityPatchRequest entityPatchRequest) {
        EntityPatchRequest sanitizedPatchRequest = entityMapper.sanitizePatchRequest(entityPatchRequest);
        if (StringUtils.isNotBlank(entityPatchRequest.wireInstruction())) {
            String sanitizedWireInstruction = HtmlSanitizer.sanitize(entityPatchRequest.wireInstruction());
            sanitizedPatchRequest = EntityPatchRequest.builder(sanitizedPatchRequest).wireInstruction(sanitizedWireInstruction).build();
        }
        String erpId = validateAndSanitizeErpId(entityPatchRequest.erpId());
        sanitizedPatchRequest = EntityPatchRequest.builder(sanitizedPatchRequest).erpId(erpId).build();
        return sanitizedPatchRequest;
    }

    // return true if proration scheme or mode is changed
    // if proration fields are not present in patch request, then it is not considered as change
    private boolean isChangeInProration(Entity existingEntity, EntityPatchRequest entityPatchRequest) {
        if (Objects.nonNull(entityPatchRequest.prorationScheme()) && entityPatchRequest.prorationScheme() != existingEntity.getProrationScheme()) {
            return true;
        }
        return Objects.nonNull(entityPatchRequest.prorationMode()) && entityPatchRequest.prorationMode() != existingEntity.getProrationMode();
    }

    // prevent updating these fields if there is any order present within that entity: prorationScheme, prorationMode
    // this is an optimistic check, there could be a concurrent transaction that creates an order during update
    // todo: implement one-way "order creation seal" that prevents order creation until turned on
    private void preventEntityUpdateIfOrdersPresent(String entityId) {
        EntityContext entityContext = entityContextProvider.provideScopedEntityContext(Set.of(entityId));
        List<OrderStub> orderStubs = TenantContextInjector.spawnThreadAndCallInEntityContext(
            tenantIdProvider.provideTenantIdString(),
            entityContext,
            entityContextProvider,
            () -> orderGetService.getOrderStubs(PageRequest.from(null, 1)).getResult()
        );
        if (CollectionUtils.isNotEmpty(orderStubs)) {
            throw new ConflictingStateException("proration cannot be updated, order(s) present in the entity");
        }
    }

    private void validateProrationConfig(ProrationConfig.Scheme prorationScheme, ProrationConfig.Mode prorationMode) {
        // this constructor validates the valid combination of proration scheme and mode
        new ProrationConfig(prorationScheme, prorationMode);
    }

    public void updateWireInstructionForDefaultEntity(String wireInstruction) {
        Optional<Entity> defaultEntity = entityDAO.getDefaultEntity();
        if (defaultEntity.isEmpty()) {
            return;
        }
        updateWireInstruction(defaultEntity.get().getEntityId(), wireInstruction);
    }

    public void updateLogo(String entityId, InputStream inputStream) {
        Validator.validateStringNotBlank(entityId, "entityId");
        if (Objects.isNull(inputStream)) {
            LOGGER.warn(EntityProductMetadata.ENTITY_CORE, "missing input stream for logo upload");
            throw new InvalidInputException("Logo image is required");
        }
        entityDAO.getEntityById(entityId).orElseThrow(() -> new ObjectNotFoundException(BillyObjectType.ENTITY, entityId));
        byte[] logoBytes = getByteArray(inputStream);
        if (logoBytes.length > MAX_LOGO_SIZE) {
            String humanReadableMaxSize = FileUtils.byteCountToDisplaySize(MAX_LOGO_SIZE);
            String errorMessage = String.format("Logo image must be less than %s in size", humanReadableMaxSize);
            throw new InvalidInputException(errorMessage);
        }
        // makes sure the input is a valid image type
        String imageType = ImageUtils.getImageType(logoBytes);
        if (!imageType.equalsIgnoreCase(LOGO_TYPE)) {
            throw new InvalidInputException("Invalid logo image type: " + imageType);
        }
        entityDAO.updateLogoContent(entityId, logoBytes);
    }

    private byte[] getByteArray(InputStream logoInputStream) {
        try {
            return logoInputStream.readAllBytes();
        } catch (IOException e) {
            String errorMessage = "Error reading input stream";
            LOGGER.error(errorMessage, e);
            throw new ServiceFailureException(errorMessage);
        }
    }

    public void updateWireInstruction(String entityId, String wireInstruction) {
        Validator.validateStringNotBlank(entityId, "entityId");
        Validator.validateStringNotBlank(wireInstruction, "wireInstruction");
        String sanitizedWireInstruction = HtmlSanitizer.sanitize(wireInstruction);
        EntityPatchRequest entityPatchRequest = new EntityPatchRequest(
            entityId,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            sanitizedWireInstruction,
            null,
            null,
            null
        );
        String tenantId = tenantIdProvider.provideTenantIdString();
        cacheService.invalidateKey(tenantId, CacheType.ENTITY_BY_ID, entityId);
        entityDAO.patchEntity(entityPatchRequest);
    }

    public void deleteEntity(String entityId) {
        String tenantId = tenantIdProvider.provideTenantIdString();
        Validator.validateStringNotBlank(entityId, "entityId");
        Entity entity = entityGetService.getEntityFromDb(entityId);
        if (BooleanUtils.isTrue(entity.getIsDefault())) {
            throw new InvalidInputException("default entity cannot be deleted");
        }
        if (entityGetService.checkIfEntityContainsData(entityId)) {
            throw new InvalidInputException("entity cannot be deleted as it contains data");
        }
        entityDAO.deleteEntity(entityId);
        cacheService.invalidateKey(tenantId, CacheType.ENTITY_BY_ID, entityId);
        cacheService.invalidateKey(tenantId, CacheType.ENTITY_BY_DISPLAY_ID, entity.getDisplayId());
        cacheService.invalidateKey(tenantId, CacheType.TENANT_ENTITY_IDS, tenantId);
    }

    // todo: multientity: temporary method for data migration. remove it once backfill completed in all environments
    public void entityTaggingBackfill() {
        entityDAO.entityTaggingBackfill();
    }

    // todo: temporary method for data migration. remove it once backfill completed in all environments
    public void displayIdBackfill() {
        entityDAO.displayIdBackfill();
    }
}
