package com.subskribe.billy.entity;

import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.entity.db.EntityDAO;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.exception.EntityNotFoundException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.SecurityContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;

public class EntityContextProvider {

    private final EntityDAO entityDAO;

    @Context
    private SecurityContext securityContext;

    private final CurrentUserProvider currentUserProvider;

    private final FeatureService featureService;

    @Inject
    public EntityContextProvider(EntityDAO entityDAO, CurrentUserProvider currentUserProvider, FeatureService featureService) {
        this.currentUserProvider = currentUserProvider;
        this.entityDAO = entityDAO;
        this.featureService = featureService;
    }

    // ensure that current session is under single entity context
    // returns the entity id if so, else throw exception
    public String provideSingleEntityOrElseThrow() {
        if (!featureService.isEnabled(Feature.MULTI_ENTITY_AUTH)) {
            return getDefaultEntityId();
        }
        EntityContext entityContext = provideEntityContext();
        if (CollectionUtils.isEmpty(entityContext.getSelectedIds())) {
            throw new EntityNotFoundException("No entity selected");
        }
        if (entityContext.getSelectedIds().size() > 1) {
            throw new EntityNotFoundException("Multiple entities selected");
        }
        return entityContext.getSelectedIds().stream().findFirst().orElseThrow();
    }

    public EntityContext provideEntityContext() {
        try {
            Optional<BillyAuthPrincipal> optionalBillyAuthPrincipal = currentUserProvider.getBillyAuthPrincipal();
            return optionalBillyAuthPrincipal.map(BillyAuthPrincipal::getEntityContext).orElseThrow();
        } catch (Exception e) {
            throw new EntityNotFoundException("Entity context not found", e);
        }
    }

    public EntityContext provideScopedEntityContext(Set<String> entityIds) {
        EntityContext entityContext = provideEntityContext();
        if (containsAllAccessToken(entityIds)) {
            return entityContext;
        }
        Set<String> scopedEntityIds = SetUtils.intersection(entityIds, entityContext.getSelectedIds());
        return EntityContext.builder(entityContext).selectedIds(scopedEntityIds).hasFullSelection(false).build();
    }

    public EntityContext provideAllEntitiesContext() {
        List<Entity> entities = entityDAO.getEntities();
        Set<String> entityIds = entities.stream().map(Entity::getEntityId).collect(Collectors.toSet());
        return EntityContext.builder().selectedIds(entityIds).authorizedIds(entityIds).hasFullSelection(true).hasFullAuthorization(true).build();
    }

    public Set<String> getEntityIdsFromEntityContext() {
        EntityContext entityContext = provideEntityContext();
        if (entityContext.hasFullSelection() && entityContext.hasFullAuthorization()) {
            return Set.of(EntityContext.ALL_ACCESS_ID);
        }
        return entityContext.getSelectedIds();
    }

    private String getDefaultEntityId() {
        return entityDAO.getDefaultEntity().map(Entity::getEntityId).orElseThrow(() -> new InvariantCheckFailedException("default entity not found"));
    }

    public static boolean containsAllAccessToken(Set<String> entityIds) {
        return entityIds.contains(EntityContext.ALL_ACCESS_ID);
    }
}
