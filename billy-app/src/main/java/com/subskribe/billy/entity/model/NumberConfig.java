package com.subskribe.billy.entity.model;

import graphql.annotations.annotationTypes.GraphQLField;
import graphql.annotations.annotationTypes.GraphQLName;
import graphql.annotations.annotationTypes.GraphQLNonNull;

@Graph<PERSON>Name("NumberConfig")
public record NumberConfig(
    @GraphQLField String configId,
    @GraphQLField @GraphQLNonNull NumberScheme scheme,
    @GraphQLField @GraphQLNonNull String prefix,
    @GraphQLField @GraphQLNonNull int length,
    @GraphQLField Long nextSequenceNumber
) {
    private static final NumberScheme DEFAULT_SCHEME = NumberScheme.SEQUENCE;
    private static final String DEFAULT_INV_PREFIX = "INV-";
    private static final int DEFAULT_LENGTH = 6;
    private static final long DEFAULT_SEQUENCE_NUMBER = 1;

    public static NumberConfig getDefaultInvoiceConfig() {
        return new NumberConfig(null, DEFAULT_SCHEME, DEFAULT_INV_PREFIX, DEFAULT_LENGTH, DEFAULT_SEQUENCE_NUMBER);
    }
}
