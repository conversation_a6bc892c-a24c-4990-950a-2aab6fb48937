package com.subskribe.billy.invoicedunning.model;

import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import com.subskribe.billy.tenant.model.Tenant;
import java.io.BufferedInputStream;
import java.util.Optional;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
public interface SendEmailRequest {
    BufferedInputStream getInvoiceDocumentBinary();

    Optional<AccountContact> getAccountReceivableContact();

    Tenant getTenant();

    String getAutomaticPaymentMethod();
}
