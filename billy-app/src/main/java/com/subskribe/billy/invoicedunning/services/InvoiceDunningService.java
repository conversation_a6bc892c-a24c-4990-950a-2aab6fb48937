package com.subskribe.billy.invoicedunning.services;

import static com.subskribe.billy.invoicedunning.DunningProductMetadata.DUNNING_CORE;
import static com.subskribe.billy.invoicedunning.DunningProductMetadata.DUNNING_CUSTOMIZATION;
import static com.subskribe.billy.invoicedunning.DunningProductMetadata.DUNNING_DOCUMENT_NOT_FOUND;
import static com.subskribe.billy.invoicedunning.DunningProductMetadata.DUNNING_EMAIL_FAILURE;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.auth.authorizers.JobOnly;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.email.model.EmailContacts;
import com.subskribe.billy.email.model.EmailData;
import com.subskribe.billy.email.model.EmailType;
import com.subskribe.billy.email.model.ImmutableEmailAttachmentData;
import com.subskribe.billy.email.model.ImmutableEmailData;
import com.subskribe.billy.email.services.EmailService;
import com.subskribe.billy.email.services.EmailValidationService;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.template.DocumentTemplateType;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoice.service.InvoiceDocumentService;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoicedunning.db.InvoiceDunningDAO;
import com.subskribe.billy.invoicedunning.model.DunningReminderType;
import com.subskribe.billy.invoicedunning.model.ImmutableInvoiceDunning;
import com.subskribe.billy.invoicedunning.model.ImmutableInvoiceDunningRequest;
import com.subskribe.billy.invoicedunning.model.ImmutableSendDunningReminderRequest;
import com.subskribe.billy.invoicedunning.model.ImmutableSendEmailRequest;
import com.subskribe.billy.invoicedunning.model.InvoiceDunning;
import com.subskribe.billy.invoicedunning.model.InvoiceDunningAction;
import com.subskribe.billy.invoicedunning.model.InvoiceDunningRequest;
import com.subskribe.billy.invoicedunning.model.SendDunningReminderRequest;
import com.subskribe.billy.invoicedunning.model.SendEmailRequest;
import com.subskribe.billy.invoicesettlement.model.InvoiceBalance;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.payment.model.AccountAutomaticPaymentMethodDetails;
import com.subskribe.billy.payment.model.PaymentProviderPaymentMethod;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodGetService;
import com.subskribe.billy.shared.DataValidation;
import com.subskribe.billy.shared.logger.ErrorContext;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.Tenant;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenant.settings.accountreceivablecontact.services.AccountReceivableContactService;
import com.subskribe.billy.tenant.settings.dunningsettings.model.DunningSetting;
import com.subskribe.billy.tenant.settings.dunningsettings.services.DunningSettingService;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.validation.Validator;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.FormatStyle;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.mail.MessagingException;
import org.apache.commons.lang3.StringUtils;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class InvoiceDunningService {

    private static final com.subskribe.billy.shared.logger.Logger LOGGER = LoggerFactory.getLogger(InvoiceDunningService.class);

    private static final String DUNNING_TEMPLATE_MANUAL_REMINDER = "html/dunning/dunning-email-manual.html";
    private static final String DUNNING_TEMPLATE_AUTO_PAYMENT_FAILED = "html/dunning/dunning-email-auto-payment-failed.html";
    private static final String DUNNING_TEMPLATE_AUTO_PAYMENT_SUCCEEDED = "html/dunning/dunning-email-auto-payment-succeeded.html";
    private static final String DUNNING_TEMPLATE_DUE_SOON = "html/dunning/dunning-email-due-soon.html";
    private static final String DUNNING_TEMPLATE_DUE_SOON_MISSING_ADDRESS = "html/dunning/dunning-email-due-soon-missing-address.html";
    private static final String DUNNING_TEMPLATE_AUTO_PAYMENT_SOON = "html/dunning/dunning-email-auto-payment-soon.html";
    private static final String DUNNING_TEMPLATE_DUE_TODAY = "html/dunning/dunning-email-due-today.html";
    private static final String DUNNING_TEMPLATE_DUE_TODAY_MISSING_ADDRESS = "html/dunning/dunning-email-due-today-missing-address.html";
    private static final String DUNNING_TEMPLATE_AFTER_DUE_DATE = "html/dunning/dunning-email-after-due-date.html";
    private static final String DUNNING_TEMPLATE_AFTER_DUE_DATE_MISSING_ADDRESS = "html/dunning/dunning-email-after-due-date-missing-address.html";

    private static final String INVOICE_NUMBER_KEY = "{{invoice_number}}";
    private static final String INVOICE_DUE_DATE_KEY = "{{invoice_due_date}}";
    private static final String INVOICE_COLLECTION_DATE_KEY = "{{invoice_collection_date}}";
    private static final String ACCOUNT_PAYMENT_METHOD_KEY = "{{account_payment_method}}";
    private static final String INVOICE_BALANCE_KEY = "{{invoice_balance}}";
    private static final String INVOICE_PAID_AMOUNT_KEY = "{{invoice_paid_amount}}";
    private static final String CURRENCY_CODE_KEY = "{{currency_code}}";
    private static final String INVOICE_DAYS_PAST_DUE_KEY = "{{days_past_due}}";
    private static final String INVOICE_BILLING_CONTACT_NAME_KEY = "{{billing_contact_name}}";

    private static final String TENANT_NAME_KEY = "{{tenant_name}}";
    private static final String ACCOUNT_RECEIVABLE_CONTACT_NAME_KEY = "{{account_receivable_contact_name}}";
    private static final String ACCOUNT_RECEIVABLE_STREET_ADDRESS_KEY = "{{account_receivable_street_address}}";
    private static final String ACCOUNT_RECEIVABLE_CITY_KEY = "{{account_receivable_city}}";
    private static final String ACCOUNT_RECEIVABLE_STATE_KEY = "{{account_receivable_state}}";
    private static final String ACCOUNT_RECEIVABLE_COUNTRY_KEY = "{{account_receivable_country}}";
    private static final String ACCOUNT_RECEIVABLE_ZIPCODE_KEY = "{{account_receivable_zipcode}}";
    private static final String ACCOUNT_RECEIVABLE_CONTACT_EMAIL_KEY = "{{account_receivable_contact_email}}";

    private static final String EMAIL_SUBJECT_DEFAULT = "Invoice due reminder for Invoice: %s";
    private static final String EMAIL_SUBJECT_AUTO_PAYMENT_FAILED = "Automatic payment failed for Invoice: %s";
    private static final String EMAIL_SUBJECT_AUTO_PAYMENT_SUCCEEDED = "Automatic payment succeeded for Invoice: %s";
    private static final String EMAIL_SUBJECT_UPCOMING_AUTOMATIC_PAYMENT = "Upcoming automatic payment for Invoice: %s";

    private static final String FILE_NAME_FORMAT = "%s.pdf";

    private static final int NUM_MONTHS_BACK_TO_CHECK = 3;
    private static final int NUM_DAYS_FORWARD_TO_CHECK = 7;
    private static final int NUM_DAYS_AUTOPAY_BACKOFF = 3;
    private static final int NUM_DAYS_NET0_BACKOFF = 3;

    private static final String FROM_EMAIL_ADDRESS_KEY = "{{from_email_address}}";
    private static final String FROM_EMAIL_ADDRESS_FORMAT = "\"{{account_receivable_contact_name}}\" <{{from_email_address}}>";

    private static final String SAMPLE_INVOICE_NUMBER = "INV-000000";

    private final InvoiceRetrievalService invoiceRetrievalService;
    private final InvoiceSettlementService invoiceSettlementService;
    private final InvoiceDunningDAO invoiceDunningDAO;
    private final InvoiceDocumentService invoiceDocumentService;
    private final TenantIdProvider tenantIdProvider;
    private final TenantSettingService tenantSettingService;
    private final DunningSettingService dunningSettingService;
    private final TenantService tenantService;
    private final AccountReceivableContactService accountReceivableContactService;
    private final BillyConfiguration billyConfiguration;
    private final EmailService emailService;
    private final AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService;
    private final DocumentTemplateGetService documentTemplateGetService;
    private final EmailValidationService emailValidationService;

    @Inject
    public InvoiceDunningService(
        InvoiceRetrievalService invoiceRetrievalService,
        InvoiceSettlementService invoiceSettlementService,
        InvoiceDunningDAO invoiceDunningDAO,
        InvoiceDocumentService invoiceDocumentService,
        TenantIdProvider tenantIdProvider,
        TenantSettingService tenantSettingService,
        DunningSettingService dunningSettingService,
        TenantService tenantService,
        AccountReceivableContactService accountReceivableContactService,
        BillyConfiguration billyConfiguration,
        EmailService emailService,
        AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService,
        DocumentTemplateGetService documentTemplateGetService,
        EmailValidationService emailValidationService
    ) {
        this.invoiceRetrievalService = invoiceRetrievalService;
        this.invoiceSettlementService = invoiceSettlementService;
        this.invoiceDunningDAO = invoiceDunningDAO;
        this.invoiceDocumentService = invoiceDocumentService;
        this.tenantIdProvider = tenantIdProvider;
        this.tenantSettingService = tenantSettingService;
        this.dunningSettingService = dunningSettingService;
        this.tenantService = tenantService;
        this.accountReceivableContactService = accountReceivableContactService;
        this.billyConfiguration = billyConfiguration;
        this.emailService = emailService;
        this.accountAutomaticPaymentMethodGetService = accountAutomaticPaymentMethodGetService;
        this.documentTemplateGetService = documentTemplateGetService;
        this.emailValidationService = emailValidationService;
    }

    public void sendSampleEmail(DunningReminderType reminderType) {
        Optional<String> currentUserEmailOptional = CurrentUserProvider.provideEmailAddress();
        if (currentUserEmailOptional.isEmpty() || !DataValidation.isEmailValid(currentUserEmailOptional.get())) {
            throw new IllegalStateException("User email address is invalid. This method can be called only on user context");
        }

        try {
            Optional<AccountContact> accountReceivableContactOptional = accountReceivableContactService.getContact();
            String dunningTemplate = getDunningTemplate(reminderType, accountReceivableContactOptional, false);

            EmailContacts emailContacts = getEmailContacts(currentUserEmailOptional.get());

            Tenant tenant = tenantService.getCurrentTenant();
            String subject = getSubject(reminderType, false, SAMPLE_INVOICE_NUMBER);
            EmailData emailData = ImmutableEmailData.builder()
                .emailTemplate(dunningTemplate)
                .emailContacts(emailContacts)
                .fromEmail(getFromEmailAddress(accountReceivableContactOptional))
                .subject(subject)
                .emailType(EmailType.DUNNING)
                .relatedObjectId(SAMPLE_INVOICE_NUMBER)
                .putAllReplacementValuesMap(getReplacementKeysMapForSampleEmail(tenant, accountReceivableContactOptional))
                .build();

            emailService.sendEmail(emailData);
        } catch (MessagingException | IOException e) {
            LOGGER.error(new ErrorContext(DUNNING_CORE, DUNNING_EMAIL_FAILURE), "Sample email threw an exception: ", e);
            throw new ServiceFailureException("Sample email couldn't be sent due to an internal failure. We are looking into it.");
        }
    }

    private EmailContacts getEmailContacts(String currentUserEmail) {
        EmailContact currentUserEmailContact = new EmailContact();
        currentUserEmailContact.setEmail(currentUserEmail);

        EmailContacts emailContacts = new EmailContacts();
        emailContacts.setToContacts(List.of(currentUserEmailContact));
        return emailContacts;
    }

    public void sendInvoiceReminderEmail(
        Invoice.Number invoiceNumber,
        Optional<DunningReminderType> reminderType,
        Optional<PaymentProviderPaymentMethod> paymentMethod
    ) {
        DunningSetting dunningSetting = dunningSettingService.getDunningSetting();
        if (!dunningSetting.getIsEnabled()) {
            LOGGER.info("Dunning is disabled. Skipping dunning for invoice: {}, with reminderType", invoiceNumber, reminderType);
            return;
        }
        LOGGER.info("Performing dunning for invoice: {} with reminder type {}", invoiceNumber, reminderType.orElse(null));
        Invoice invoice = invoiceRetrievalService.getInvoice(invoiceNumber);
        InvoiceBalance invoiceBalance = invoiceSettlementService.getInvoiceBalance(invoiceNumber);
        boolean hasAutomaticPayment = accountAutomaticPaymentMethodGetService
            .getActiveAccountPaymentMethodForAccount(invoice.getCustomerAccountId())
            .isPresent();

        InvoiceDunningRequest invoiceDunningRequest = ImmutableInvoiceDunningRequest.builder()
            .invoiceBalance(invoiceBalance)
            .invoice(invoice)
            .automaticPaymentEnabled(hasAutomaticPayment)
            .reminderTypeToUse(reminderType)
            .paymentMethodUsed(paymentMethod)
            .dunningTypeEnabledMap(dunningSetting.getDunningTypeMap())
            .build();
        performDunningOnInvoice(invoiceDunningRequest);
    }

    private String getFromEmailAddress(Optional<AccountContact> accountReceivableContactOptional) {
        String emailAddress = billyConfiguration.getQuartzDunningConfiguration().getFromEmailAddress();

        if (accountReceivableContactOptional.isEmpty()) {
            return emailAddress;
        }

        AccountContact accountContact = accountReceivableContactOptional.get();
        String contactName = getFullName(accountContact);
        return FROM_EMAIL_ADDRESS_FORMAT.replace(ACCOUNT_RECEIVABLE_CONTACT_NAME_KEY, contactName).replace(FROM_EMAIL_ADDRESS_KEY, emailAddress);
    }

    private String getFullName(AccountContact accountContact) {
        Validator.validateStringNotBlank(accountContact.getFirstName(), "Account contact first name");
        if (StringUtils.isBlank(accountContact.getLastName())) {
            return accountContact.getFirstName();
        }
        return String.format("%s %s", accountContact.getFirstName(), accountContact.getLastName());
    }

    private Map<String, String> getReplacementKeysMapForSampleEmail(Tenant tenant, Optional<AccountContact> accountReceivableContactOptional) {
        Map<String, String> replacementValuesMap = new HashMap<>();
        replacementValuesMap.put(TENANT_NAME_KEY, tenant.getName());
        addContactDetailKeys(accountReceivableContactOptional, replacementValuesMap);
        return replacementValuesMap;
    }

    private String getDunningTemplate(
        DunningReminderType reminderType,
        Optional<AccountContact> accountReceivableContact,
        boolean hasAutomaticPaymentMethodEnabled
    ) {
        // Check if tenant has a custom template for this reminder type
        List<DocumentMasterTemplate> dunningTemplates = documentTemplateGetService.getDocumentMasterTemplates(DocumentTemplateType.DUNNING);
        Optional<DocumentMasterTemplate> customTemplateOptional = dunningTemplates
            .stream()
            .filter(template -> reminderType == template.getConfiguration().getDunningReminderType())
            .findFirst();
        if (customTemplateOptional.isPresent()) {
            return customTemplateOptional.get().getContent();
        }

        String templateFilepath =
            switch (reminderType) {
                case WEEK_BEFORE_DUE_DATE -> findTemplateForDueSoonEmail(accountReceivableContact, hasAutomaticPaymentMethodEnabled);
                case DUE_DATE -> accountReceivableContact.isPresent() ? DUNNING_TEMPLATE_DUE_TODAY : DUNNING_TEMPLATE_DUE_TODAY_MISSING_ADDRESS;
                case MANUAL -> DUNNING_TEMPLATE_MANUAL_REMINDER;
                case AUTO_PAYMENT_FAILED -> DUNNING_TEMPLATE_AUTO_PAYMENT_FAILED;
                case AUTO_PAYMENT_SUCCEEDED -> DUNNING_TEMPLATE_AUTO_PAYMENT_SUCCEEDED;
                case WEEK_AFTER_DUE_DATE,
                    TWO_WEEKS_AFTER_DUE_DATE,
                    MONTH_AFTER_DUE_DATE,
                    TWO_MONTHS_AFTER_DUE_DATE -> accountReceivableContact.isPresent()
                    ? DUNNING_TEMPLATE_AFTER_DUE_DATE
                    : DUNNING_TEMPLATE_AFTER_DUE_DATE_MISSING_ADDRESS;
            };

        try {
            return EmailService.getTemplateFromResourceFile(templateFilepath);
        } catch (IOException e) {
            String errorMessage = String.format("Couldn't find the template file for dunning reminder type: %s", reminderType);
            LOGGER.warn(DUNNING_CUSTOMIZATION, errorMessage);
            throw new ServiceFailureException(errorMessage);
        }
    }

    private static String findTemplateForDueSoonEmail(Optional<AccountContact> accountReceivableContact, boolean hasAutomaticPaymentMethodEnabled) {
        if (hasAutomaticPaymentMethodEnabled) {
            return DUNNING_TEMPLATE_AUTO_PAYMENT_SOON;
        }
        return accountReceivableContact.isPresent() ? DUNNING_TEMPLATE_DUE_SOON : DUNNING_TEMPLATE_DUE_SOON_MISSING_ADDRESS;
    }

    @JobOnly
    public void performDunningOperation() {
        var dunningSetting = dunningSettingService.getDunningSetting();
        if (!dunningSetting.getIsEnabled()) {
            return;
        }

        // Find start and end dates in tenant zone from current date to perform dunning
        ZoneId tenantZoneId = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        LocalDateTime localStartDate = LocalDateTime.now(tenantZoneId).minusMonths(NUM_MONTHS_BACK_TO_CHECK);
        Instant dunningStartDate = localStartDate.atZone(tenantZoneId).toInstant();
        LocalDateTime localEndDate = LocalDateTime.now(tenantZoneId).plusDays(NUM_DAYS_FORWARD_TO_CHECK);
        Instant dunningEndDate = localEndDate.atZone(tenantZoneId).toInstant();

        // fetch invoices and balances
        List<Invoice> invoicesList = invoiceRetrievalService.getInvoicesForDunningWithInDueDateInterval(dunningStartDate, dunningEndDate);
        List<InvoiceBalance> invoiceBalances = invoiceSettlementService.getInvoiceBalances(invoicesList);
        List<InvoiceBalance> invoicesWithPositiveBalance = invoiceBalances
            .stream()
            .filter(this::checkForPositiveBalance)
            .collect(Collectors.toList());

        if (invoicesWithPositiveBalance.isEmpty()) {
            LOGGER.info("No invoices with balances left for dunning!");
            return;
        }

        // fetch already performed dunning on the invoice numbers
        var dunningInvoiceList = invoicesWithPositiveBalance.stream().map(InvoiceBalance::getInvoiceNumber).collect(Collectors.toList());
        var previousDunnList = invoiceDunningDAO.getDunningDetails(dunningInvoiceList);

        performDunningOnInvoicesWithBalances(invoicesWithPositiveBalance, invoicesList, previousDunnList, dunningSetting.getDunningTypeMap());

        LOGGER.debug("Dunning job done!");
    }

    private void performDunningOnInvoicesWithBalances(
        List<InvoiceBalance> invoiceBalanceList,
        List<Invoice> invoicesList,
        List<InvoiceDunning> previousDunnList,
        Map<DunningReminderType, Boolean> dunningTypeEnabledMap
    ) {
        Map<String, Invoice> invoiceMap = invoicesList
            .stream()
            .collect(Collectors.toMap(invoice -> invoice.getInvoiceNumber().getNumber(), Function.identity()));
        Map<String, List<InvoiceDunning>> previousDunnMap = previousDunnList
            .stream()
            .collect(Collectors.groupingBy(InvoiceDunning::getInvoiceNumber));

        Set<String> accountsWithAutomaticPayments;

        List<String> accountIdsForInvoices = invoicesList.stream().map(Invoice::getCustomerAccountId).toList();
        accountsWithAutomaticPayments = accountAutomaticPaymentMethodGetService.getAccountsWithAutomaticPaymentMethodsFromIdList(
            accountIdsForInvoices
        );

        invoiceBalanceList.forEach(invoiceBalance -> {
            Invoice invoice = invoiceMap.get(invoiceBalance.getInvoiceNumber());
            Map<DunningReminderType, List<InvoiceDunning>> previousDunningsByType = previousDunnMap
                .getOrDefault(invoiceBalance.getInvoiceNumber(), List.of())
                .stream()
                .collect(Collectors.groupingBy(InvoiceDunning::getReminderType));
            InvoiceDunningRequest invoiceDunningRequest = ImmutableInvoiceDunningRequest.builder()
                .invoiceBalance(invoiceBalance)
                .invoice(invoice)
                .previousInvoiceDunning(previousDunningsByType)
                .dunningTypeEnabledMap(dunningTypeEnabledMap)
                .automaticPaymentEnabled(accountsWithAutomaticPayments.contains(invoice.getCustomerAccountId()))
                .build();
            performDunningOnInvoice(invoiceDunningRequest);
        });
    }

    // Dunning will be performed on the following times and paymentTerms based on this logic
    // PaymentTerm                 NumDaysToDueDate             ReminderType
    //   Any                        < 0                      Due date reminder
    //   Any                        0                        Due Date is today reminder
    //   Any                        7 or 14                  Late by 7 or 14 reminder
    //   Any                        1 month                  Late by 1 month reminder
    //   Any                        2 months                 Late by 2 months reminder
    private void performDunningOnInvoice(InvoiceDunningRequest request) {
        // skip performing dunning on external emails in non-prod envs
        Optional<String> toEmail = Optional.ofNullable(request.getInvoice().getBillingContact().getEmail()).filter(
            e -> billyConfiguration.isProd() || emailValidationService.isInternalEmailAddress(e)
        );
        if (toEmail.isEmpty()) {
            LOGGER.info("Cannot send external emails: {}", request.getInvoice().getBillingContact().getEmail());
            return;
        }

        TimeZone timeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        ZoneId tenantZoneId = timeZone.toZoneId();
        DocumentRenderFormatter formatter = new DocumentRenderFormatter(timeZone);
        Instant dueDate = request.getInvoice().getDueDate();
        LocalDateTime currentLocalDate = LocalDateTime.now(tenantZoneId);
        LocalDateTime localDueDate = dueDate.atZone(tenantZoneId).toLocalDateTime();
        LocalDateTime localPaymentDate = localDueDate.minusHours(InvoiceRetrievalService.PROCESS_BEFORE_DUE_DATE_HOURS);
        long diffDays = DateTimeCalculator.getNormalizedDaysBetween(dueDate, Instant.now(), timeZone, ChronoUnit.DAYS);
        long diffMonths = localDueDate.until(currentLocalDate, ChronoUnit.MONTHS);
        DunningReminderType dunningReminderType = request.reminderTypeToUse().orElse(getReminderType(diffMonths, diffDays));
        String subject = getSubject(dunningReminderType, request.isAutomaticPaymentEnabled(), request.getInvoice().getInvoiceNumber().getNumber());
        SendDunningReminderRequest dunningReminderRequest = ImmutableSendDunningReminderRequest.builder()
            .dunningReminderType(dunningReminderType)
            .diffDays(diffDays)
            .diffMonths(diffMonths)
            .dueDate(localDueDate)
            .automaticPaymentDate(localPaymentDate)
            .formatter(formatter)
            .subject(subject)
            .build();
        checkAndSendDunningReminder(dunningReminderRequest, request);
    }

    private DunningReminderType getReminderType(long diffMonths, long diffDays) {
        if (diffMonths >= 2) {
            return DunningReminderType.TWO_MONTHS_AFTER_DUE_DATE;
        }

        if (diffMonths >= 1) {
            return DunningReminderType.MONTH_AFTER_DUE_DATE;
        }

        if (diffDays >= 14) {
            return DunningReminderType.TWO_WEEKS_AFTER_DUE_DATE;
        }

        if (diffDays >= 7) {
            return DunningReminderType.WEEK_AFTER_DUE_DATE;
        }

        if (diffDays >= 0) {
            return DunningReminderType.DUE_DATE;
        }

        return DunningReminderType.WEEK_BEFORE_DUE_DATE;
    }

    private String getSubject(DunningReminderType reminderType, boolean hasAutomaticPaymentEnabled, String invoiceNumber) {
        String subjectFormat =
            switch (reminderType) {
                case AUTO_PAYMENT_FAILED -> EMAIL_SUBJECT_AUTO_PAYMENT_FAILED;
                case AUTO_PAYMENT_SUCCEEDED -> EMAIL_SUBJECT_AUTO_PAYMENT_SUCCEEDED;
                case WEEK_BEFORE_DUE_DATE -> hasAutomaticPaymentEnabled ? EMAIL_SUBJECT_UPCOMING_AUTOMATIC_PAYMENT : EMAIL_SUBJECT_DEFAULT;
                default -> EMAIL_SUBJECT_DEFAULT;
            };
        return String.format(subjectFormat, invoiceNumber);
    }

    private void checkAndSendDunningReminder(SendDunningReminderRequest reminderRequest, InvoiceDunningRequest invoiceDunningRequest) {
        if (checkIfDunningShouldBeSkipped(reminderRequest, invoiceDunningRequest)) {
            return;
        }

        String automaticPaymentMethod = getAutomaticPaymentMethod(invoiceDunningRequest);

        // perform dunning ony if the specific dunning wasn't performed before
        Optional<AccountContact> accountReceivableContactOptional = accountReceivableContactService.getContact();
        Tenant tenantInfo = tenantService.getCurrentTenant();
        Invoice.Number invoiceNumber = invoiceDunningRequest.getInvoice().getInvoiceNumber();
        Optional<InputStream> invoiceDocument = invoiceDocumentService.getInvoiceDocumentCreateIfNeeded(invoiceNumber);
        if (invoiceDocument.isEmpty()) {
            var message = "couldn't find the invoice document when dunning for invoice: " + invoiceNumber;
            LOGGER.error(new ErrorContext(DUNNING_CORE, DUNNING_DOCUMENT_NOT_FOUND), message);
            throw new ServiceFailureException(message);
        }
        try (BufferedInputStream bin = new BufferedInputStream(invoiceDocument.get())) {
            SendEmailRequest sendEmailRequest = ImmutableSendEmailRequest.builder()
                .invoiceDocumentBinary(bin)
                .accountReceivableContact(accountReceivableContactOptional)
                .tenant(tenantInfo)
                .automaticPaymentMethod(automaticPaymentMethod)
                .build();
            createAndSendEmail(sendEmailRequest, reminderRequest, invoiceDunningRequest);
        } catch (IOException e) {
            String message = "failed to create and send dunning email";
            LOGGER.error(new ErrorContext(DUNNING_CORE, DUNNING_EMAIL_FAILURE), message);
            throw new ServiceFailureException(message, e);
        }
    }

    private boolean checkIfDunningShouldBeSkipped(SendDunningReminderRequest reminderRequest, InvoiceDunningRequest invoiceDunningRequest) {
        // First check if we evaluated this dunning type before. If we did, we can skip it, and we do not need to record anything
        // Otherwise we would record the same thing over and over again
        // Do not perform this check if the dunning type is repeatable, for example MANUAL
        if (
            !reminderRequest.getDunningReminderType().isRepeatable() &&
            invoiceDunningRequest.getPreviousInvoiceDunning().containsKey(reminderRequest.getDunningReminderType())
        ) {
            return true;
        }

        // do not send dunning reminder for NET0 if a reminder has already been sent within 3 days
        if (
            reminderRequest.getDunningReminderType().isConfigurable() &&
            PaymentTerm.NET0.equals(invoiceDunningRequest.getInvoice().getPaymentTerm()) &&
            isTimeSinceLastNet0ReminderTooShort(invoiceDunningRequest)
        ) {
            saveDunningRecordForSkipped(
                invoiceDunningRequest.getInvoice(),
                invoiceDunningRequest.getInvoiceBalance(),
                reminderRequest.getDunningReminderType(),
                InvoiceDunningAction.SKIPPED_NET0
            );
            return true;
        }

        if (
            reminderRequest.getDunningReminderType().isConfigurable() &&
            !isDunningTypeEnabled(invoiceDunningRequest.getDunningTypeEnabledMap(), reminderRequest.getDunningReminderType())
        ) {
            saveDunningRecordForSkipped(
                invoiceDunningRequest.getInvoice(),
                invoiceDunningRequest.getInvoiceBalance(),
                reminderRequest.getDunningReminderType(),
                InvoiceDunningAction.SKIPPED_TYPE_DISABLED
            );
            return true;
        }

        // If this account has automatic payments turned on, we do not need to send any dunning emails
        // If automatic payments fail, we will send a different email in any case
        if (!reminderRequest.getDunningReminderType().isSendIfAutoPay() && invoiceDunningRequest.isAutomaticPaymentEnabled()) {
            saveDunningRecordForSkipped(
                invoiceDunningRequest.getInvoice(),
                invoiceDunningRequest.getInvoiceBalance(),
                reminderRequest.getDunningReminderType(),
                InvoiceDunningAction.SKIPPED_AUTO_PAY_ENABLED
            );
            return true;
        }

        // We want to make sure enough time has passed since we sent a dunning email for automatic payment failure
        // However, this does not apply to repeatable dunning types, if another automatic payment failure happens
        // or a manual one is requested, they should be sent
        if (!reminderRequest.getDunningReminderType().isRepeatable() && isTimeSinceLatestAutopayDunningTooShort(invoiceDunningRequest)) {
            saveDunningRecordForSkipped(
                invoiceDunningRequest.getInvoice(),
                invoiceDunningRequest.getInvoiceBalance(),
                reminderRequest.getDunningReminderType(),
                InvoiceDunningAction.SKIPPED_TOO_FREQUENT
            );
            return true;
        }

        return false;
    }

    private static boolean isTimeSinceLastNet0ReminderTooShort(InvoiceDunningRequest request) {
        // todo: add check against invoice posting date to avoid duplicate emails

        Optional<InvoiceDunning> latestDunning = request
            .getPreviousInvoiceDunning()
            .entrySet()
            .stream()
            .filter(entry -> isDunningTypeSpecialForNet0(entry.getKey()))
            .map(Map.Entry::getValue)
            .flatMap(List::stream)
            .filter(dunning -> dunning.getAction() != InvoiceDunningAction.SKIPPED_NET0)
            // sort by completedOn, nulls first
            .sorted(Comparator.comparing(InvoiceDunning::getCompletedOn, Comparator.nullsFirst(Comparator.naturalOrder())))
            // get last item in stream
            .reduce((a, b) -> b);

        if (latestDunning.isEmpty() || latestDunning.get().getCompletedOn() == null) {
            return false;
        }

        return latestDunning.get().getCompletedOn().isAfter(Instant.now().minus(NUM_DAYS_NET0_BACKOFF, ChronoUnit.DAYS));
    }

    // Surprising how much work it takes to find the latest item in a Map of Lists when you have so many optionals
    // and nullable fields
    private static boolean isTimeSinceLatestAutopayDunningTooShort(InvoiceDunningRequest request) {
        Map<DunningReminderType, List<InvoiceDunning>> previousInvoiceDunning = request.getPreviousInvoiceDunning();

        if (!previousInvoiceDunning.containsKey(DunningReminderType.AUTO_PAYMENT_FAILED)) {
            return false;
        }

        List<InvoiceDunning> autoPayDunnings = previousInvoiceDunning.get(DunningReminderType.AUTO_PAYMENT_FAILED);

        Optional<InvoiceDunning> latestDunning = autoPayDunnings
            .stream()
            // sort by completedOn, nulls first
            .sorted(Comparator.comparing(InvoiceDunning::getCompletedOn, Comparator.nullsFirst(Comparator.naturalOrder())))
            // get last item in stream
            .reduce((a, b) -> b);

        if (latestDunning.isEmpty() || latestDunning.get().getCompletedOn() == null) {
            return false;
        }

        return latestDunning.get().getCompletedOn().isAfter(Instant.now().minus(NUM_DAYS_AUTOPAY_BACKOFF, ChronoUnit.DAYS));
    }

    private static boolean isDunningTypeEnabled(Map<DunningReminderType, Boolean> lookupMap, DunningReminderType dunningReminderType) {
        if (!dunningReminderType.isConfigurable()) {
            return true;
        }

        return lookupMap.getOrDefault(dunningReminderType, false);
    }

    private String getAutomaticPaymentMethod(InvoiceDunningRequest invoiceDunningRequest) {
        // If the request has a payment method attached, use it
        if (invoiceDunningRequest.getPaymentMethodUsed().isPresent()) {
            return invoiceDunningRequest.getPaymentMethodUsed().get().toUserFriendlyString();
        }

        // Otherwise, we only need to fetch payment method details is automatic payment is enabled
        if (invoiceDunningRequest.isAutomaticPaymentEnabled()) {
            AccountAutomaticPaymentMethodDetails activeMethodDetailsForAccount =
                accountAutomaticPaymentMethodGetService.getActiveMethodDetailsForAccount(invoiceDunningRequest.getInvoice().getCustomerAccountId());
            if (activeMethodDetailsForAccount.getPaymentProviderPaymentMethod().isPresent()) {
                return activeMethodDetailsForAccount.getPaymentProviderPaymentMethod().get().toUserFriendlyString();
            }
        }
        return "";
    }

    private static boolean isDunningTypeSpecialForNet0(DunningReminderType reminderType) {
        return (reminderType == DunningReminderType.DUE_DATE || reminderType == DunningReminderType.WEEK_BEFORE_DUE_DATE);
    }

    private void saveDunningRecordForEmail(Invoice invoice, InvoiceBalance invoiceBalance, DunningReminderType reminderType, String emailId) {
        InvoiceDunning invoiceDunning = ImmutableInvoiceDunning.builder()
            .tenantId(tenantIdProvider.provideTenantIdString())
            .invoiceNumber(invoice.getInvoiceNumber().getNumber())
            .amount(invoiceBalance.getBalance())
            .reminderType(reminderType)
            .completedOn(Instant.now())
            .emailSentTo(String.join(",", List.of(invoice.getBillingContact().getEmail())))
            .emailId(emailId)
            .action(InvoiceDunningAction.EMAIL_SENT)
            .build();
        invoiceDunningDAO.addDunningDetail(invoiceDunning);
    }

    private void saveDunningRecordForSkipped(
        Invoice invoice,
        InvoiceBalance invoiceBalance,
        DunningReminderType reminderType,
        InvoiceDunningAction action
    ) {
        InvoiceDunning invoiceDunning = ImmutableInvoiceDunning.builder()
            .tenantId(tenantIdProvider.provideTenantIdString())
            .invoiceNumber(invoice.getInvoiceNumber().getNumber())
            .amount(invoiceBalance.getBalance())
            .reminderType(reminderType)
            .completedOn(Instant.now())
            .action(action)
            .build();
        invoiceDunningDAO.addDunningDetail(invoiceDunning);
    }

    private void createAndSendEmail(
        SendEmailRequest emailRequest,
        SendDunningReminderRequest reminderRequest,
        InvoiceDunningRequest invoiceDunningRequest
    ) {
        Invoice invoice = invoiceDunningRequest.getInvoice();
        String invoiceNumber = invoice.getInvoiceNumber().getNumber();
        InvoiceBalance invoiceBalance = invoiceDunningRequest.getInvoiceBalance();

        Map<String, String> replacementValuesMap = new HashMap<>();
        replacementValuesMap.put(TENANT_NAME_KEY, emailRequest.getTenant().getName());
        addContactDetailKeys(emailRequest.getAccountReceivableContact(), replacementValuesMap);
        addInvoiceDetailKeys(
            invoice,
            invoiceBalance,
            reminderRequest.getDueDate(),
            reminderRequest.getAutomaticPaymentDate(),
            reminderRequest.getFormatter(),
            emailRequest.getAutomaticPaymentMethod(),
            reminderRequest.getDiffDays(),
            replacementValuesMap
        );
        String dunningTemplate = getDunningTemplate(
            reminderRequest.getDunningReminderType(),
            emailRequest.getAccountReceivableContact(),
            invoiceDunningRequest.isAutomaticPaymentEnabled()
        );
        EmailContacts emailContacts = getEmailContacts(invoice, emailRequest.getAccountReceivableContact(), reminderRequest.getDunningReminderType());

        try {
            EmailData emailData = ImmutableEmailData.builder()
                .emailTemplate(dunningTemplate)
                .emailContacts(emailContacts)
                .fromEmail(getFromEmailAddress(emailRequest.getAccountReceivableContact()))
                .subject(reminderRequest.getSubject())
                .emailType(EmailType.DUNNING)
                .relatedObjectId(invoice.getInvoiceNumber().getNumber())
                .relatedObjectParentId(Optional.of(invoice.getSubscriptionId()))
                .putAllReplacementValuesMap(replacementValuesMap)
                .entityId(invoice.getEntityId())
                .attachmentData(
                    ImmutableEmailAttachmentData.builder()
                        .attachmentDocument(emailRequest.getInvoiceDocumentBinary())
                        .attachmentDocumentName(String.format(FILE_NAME_FORMAT, invoiceNumber))
                        .build()
                )
                .build();

            String emailId = emailService.sendEmail(emailData);
            saveDunningRecordForEmail(invoice, invoiceDunningRequest.getInvoiceBalance(), reminderRequest.getDunningReminderType(), emailId);
        } catch (MessagingException | IOException e) {
            LOGGER.error(new ErrorContext(DUNNING_CORE, DUNNING_EMAIL_FAILURE), "Dunning email threw an exception: ", e);
        }
    }

    private EmailContacts getEmailContacts(Invoice invoice, Optional<AccountContact> accountReceivableContact, DunningReminderType reminderType) {
        EmailContacts emailContacts = new EmailContacts();
        emailContacts.setToContacts(List.of(new EmailContact(invoice.getBillingContact())));

        boolean isAutoPayDunning =
            reminderType == DunningReminderType.AUTO_PAYMENT_FAILED || reminderType == DunningReminderType.AUTO_PAYMENT_SUCCEEDED;
        if (isAutoPayDunning && accountReceivableContact.isPresent()) {
            emailContacts.setCcContacts(List.of(new EmailContact(accountReceivableContact.get().getEmail())));
        }

        return emailContacts;
    }

    private void addInvoiceDetailKeys(
        Invoice invoice,
        InvoiceBalance invoiceBalance,
        LocalDateTime dueDate,
        LocalDateTime automaticPaymentDate,
        DocumentRenderFormatter formatter,
        String automaticPaymentMethod,
        long daysPastDue,
        Map<String, String> replacementValuesMap
    ) {
        String invoiceNumber = invoice.getInvoiceNumber().getNumber();
        String invoiceDueDate = dueDate.format(DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT));
        String invoiceAutomaticPaymentDate = automaticPaymentDate.format(DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT));
        String currencyCode = invoice.getCurrencyCode();
        BigDecimal balanceAmount = invoiceBalance.getBalance();
        String formattedBalance = formatter.currencyFormat(balanceAmount, currencyCode);
        BigDecimal paidAmount = invoice.getTotal().subtract(balanceAmount);
        String formattedPaidAmount = formatter.currencyFormat(paidAmount, currencyCode);

        replacementValuesMap.put(INVOICE_NUMBER_KEY, invoiceNumber);
        replacementValuesMap.put(INVOICE_DUE_DATE_KEY, invoiceDueDate);
        replacementValuesMap.put(INVOICE_COLLECTION_DATE_KEY, invoiceAutomaticPaymentDate);
        replacementValuesMap.put(ACCOUNT_PAYMENT_METHOD_KEY, automaticPaymentMethod);
        replacementValuesMap.put(INVOICE_BALANCE_KEY, formattedBalance);
        replacementValuesMap.put(INVOICE_PAID_AMOUNT_KEY, formattedPaidAmount);
        replacementValuesMap.put(CURRENCY_CODE_KEY, invoice.getCurrencyCode());
        replacementValuesMap.put(INVOICE_DAYS_PAST_DUE_KEY, String.valueOf(daysPastDue));
        replacementValuesMap.put(INVOICE_BILLING_CONTACT_NAME_KEY, invoice.getBillingContact().getFullName());
    }

    private void addContactDetailKeys(Optional<AccountContact> accountContactOptional, Map<String, String> replacementValuesMap) {
        if (accountContactOptional.isEmpty()) {
            return;
        }

        AccountContact accountContact = accountContactOptional.get();
        var address = accountContact.getAddress();

        if (address != null) {
            String contactName = getFullName(accountContact);
            replacementValuesMap.put(ACCOUNT_RECEIVABLE_CONTACT_NAME_KEY, contactName);

            var streetAddress = address.getStreetAddressLine1();
            streetAddress += StringUtils.isBlank(address.getStreetAddressLine2()) ? StringUtils.EMPTY : " " + address.getStreetAddressLine2();
            replacementValuesMap.put(ACCOUNT_RECEIVABLE_STREET_ADDRESS_KEY, streetAddress);

            replacementValuesMap.put(ACCOUNT_RECEIVABLE_CITY_KEY, address.getCity());
            replacementValuesMap.put(ACCOUNT_RECEIVABLE_STATE_KEY, address.getState());
            replacementValuesMap.put(ACCOUNT_RECEIVABLE_COUNTRY_KEY, address.getCountry());
            replacementValuesMap.put(ACCOUNT_RECEIVABLE_ZIPCODE_KEY, address.getZipcode());
            replacementValuesMap.put(ACCOUNT_RECEIVABLE_CONTACT_EMAIL_KEY, accountContact.getEmail());
        }
    }

    private boolean checkForPositiveBalance(InvoiceBalance invoiceBalance) {
        return (invoiceBalance.getBalance().floatValue() > 0);
    }

    public List<InvoiceDunning> getEmailedDunningDetails(String invoiceNumber) {
        Validator.validateStringNotBlank(invoiceNumber, "invoiceNumber cannot be blank");
        return invoiceDunningDAO.getEmailedDunningDetails(List.of(invoiceNumber));
    }
}
