package com.subskribe.billy.invoicedunning.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.subskribe.billy.email.model.EmailContacts;
import com.subskribe.billy.event.model.NotifyingEvent;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import org.immutables.value.Value;

@BillyModelStyle
@Value.Immutable
@JsonSerialize(as = ImmutableDunningEmailSentEvent.class)
@JsonDeserialize(as = ImmutableDunningEmailSentEvent.class)
public interface DunningEmailSentEvent extends NotifyingEvent {
    String getInvoiceNumber();
    String getSubscriptionId();
    String getEntityId();
    String getAccountId();
    DunningReminderType getReminderType();
    EmailContacts getEmailContacts();

    @Override
    default String getEventObjectId() {
        return getInvoiceNumber();
    }
}
