package com.subskribe.billy.invoicedunning.db;

import static com.subskribe.billy.jooq.default_schema.tables.Dunning.DUNNING;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.invoicedunning.mapper.InvoiceDunningMapper;
import com.subskribe.billy.invoicedunning.model.InvoiceDunning;
import com.subskribe.billy.invoicedunning.model.InvoiceDunningAction;
import com.subskribe.billy.jooq.default_schema.tables.records.DunningRecord;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.List;
import javax.inject.Inject;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.mapstruct.factory.Mappers;

public class InvoiceDunningDAO {

    private final DSLContextProvider dslContextProvider;
    private final TenantIdProvider tenantIdProvider;
    private final InvoiceDunningMapper invoiceDunningMapper;

    @Inject
    public InvoiceDunningDAO(DSLContextProvider dslContextProvider, TenantIdProvider tenantIdProvider) {
        this.dslContextProvider = dslContextProvider;
        this.tenantIdProvider = tenantIdProvider;
        invoiceDunningMapper = Mappers.getMapper(InvoiceDunningMapper.class);
    }

    public List<InvoiceDunning> getDunningDetails(List<String> invoiceNumbers) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var dunningRecords = dslContext.select().from(DUNNING).where(DUNNING.INVOICE_NUMBER.in(invoiceNumbers)).fetchInto(DunningRecord.class);
        return invoiceDunningMapper.recordToInvoiceDunning(dunningRecords);
    }

    public List<InvoiceDunning> getEmailedDunningDetails(List<String> invoiceNumbers) {
        var tenantId = tenantIdProvider.provideTenantIdString();
        var dslContext = TenantDSLContextProvider.getTenantDslContext(tenantId, dslContextProvider);

        var dunningRecords = dslContext
            .select()
            .from(DUNNING)
            .where(DUNNING.INVOICE_NUMBER.in(invoiceNumbers))
            .and(DUNNING.ACTION.eq(InvoiceDunningAction.EMAIL_SENT.name()))
            .fetchInto(DunningRecord.class);
        return invoiceDunningMapper.recordToInvoiceDunning(dunningRecords);
    }

    public void addDunningDetail(InvoiceDunning invoiceDunning, Configuration configuration) {
        DSLContext dslContext = DSL.using(configuration);
        var dunningRecord = invoiceDunningMapper.invoiceDunningToRecord(invoiceDunning);
        dunningRecord.reset(DUNNING.ID);
        dslContext.insertInto(DUNNING).set(dunningRecord).execute();
    }
}
