package com.subskribe.billy.usage.service;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.usage.db.UsageDAO;
import com.subskribe.billy.usage.model.PrepaidStats;
import com.subskribe.billy.validation.Validator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;

public class UsageStatisticsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UsageStatisticsService.class);

    private final SubscriptionGetService subscriptionGetService;

    private final EntityGetService entityGetService;

    private final AccountGetService accountGetService;

    private final UsageDAO usageDAO;

    private final TenantSettingService tenantSettingService;

    private final ProductCatalogGetService productCatalogGetService;

    @Inject
    public UsageStatisticsService(
        SubscriptionGetService subscriptionGetService,
        EntityGetService entityGetService,
        AccountGetService accountGetService,
        UsageDAO usageDAO,
        TenantSettingService tenantSettingService,
        ProductCatalogGetService productCatalogGetService
    ) {
        this.subscriptionGetService = subscriptionGetService;
        this.entityGetService = entityGetService;
        this.accountGetService = accountGetService;
        this.usageDAO = usageDAO;
        this.tenantSettingService = tenantSettingService;
        this.productCatalogGetService = productCatalogGetService;
    }

    private BigDecimal getUsageSumForChargeGroupId(String subscriptionId, String subscriptionChargeGroupId, Period period) {
        Period normalized = UsageAggregationService.normalizePeriodForAggregateHour(period);
        return usageDAO.getUsageSumForPeriod(subscriptionId, subscriptionChargeGroupId, normalized);
    }

    public Map<AttributeReferences, BigDecimal> getUsageSumByAttributeForChargeGroupId(
        String subscriptionId,
        String subscriptionChargeGroupId,
        Period period
    ) {
        Period normalized = UsageAggregationService.normalizePeriodForAggregateHour(period);
        return usageDAO.getUsageSumByAttributeForPeriod(subscriptionId, subscriptionChargeGroupId, normalized);
    }

    //TODO: this is a very non optimal method we need to cache charges and plans
    //TODO: one solution cold be to pass in LoadingCache for catalog calls
    //TODO: also this call can lead to unbounded output as there can be for e.g. 1000 prepaid subscriptions (call my timeout)
    public List<PrepaidStats> getPrepaidDrawStatsForAllSubscriptions(Period targetPeriod) {
        Set<String> subscriptionIdsInPeriod = subscriptionGetService.getAllSubscriptionIdsInPeriodForTenant(targetPeriod);
        List<Subscription> allSubscriptionsInRange = subscriptionIdsInPeriod.stream().map(subscriptionGetService::getSubscription).toList();
        Map<String, Account> subscriptionAccounts = allSubscriptionsInRange
            .stream()
            .map(Subscription::getAccountId)
            .distinct()
            .map(accountGetService::getAccount)
            .collect(Collectors.toMap(Account::getAccountId, Function.identity()));
        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();

        List<PrepaidStats> finalList = new ArrayList<>();
        for (Subscription subscription : allSubscriptionsInRange) {
            Optional<Period> overlapWithSubscription = getOverlapPeriodWithSubscription(subscription, targetPeriod);
            if (overlapWithSubscription.isPresent()) {
                Account account = subscriptionAccounts.get(subscription.getAccountId());
                Entity entity = entityGetService.getEntityById(subscription.getEntityId());
                finalList.addAll(getDrawStatsForSubscription(subscription, account, entity, tenantTimeZone, overlapWithSubscription.get()));
            }
        }
        return finalList;
    }

    public List<PrepaidStats> getPrepaidDrawStatsForSubscription(String subscriptionId) {
        Validator.validateStringNotBlank(subscriptionId, "subscription id cannot be null");
        Subscription subscription = subscriptionGetService.getSubscription(subscriptionId);
        String accountId = subscription.getAccountId();
        Account account = accountGetService.getAccount(accountId);
        Entity entity = entityGetService.getEntityById(subscription.getEntityId());
        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        Period subscriptionStartToNow = Period.between(subscription.getStartDate(), Instant.now());
        return getDrawStatsForSubscription(subscription, account, entity, tenantTimeZone, subscriptionStartToNow);
    }

    // todo: update to allow multi-period prepaid charges and prepaid charges which are not part of a drawdown plan
    private List<PrepaidStats> getDrawStatsForSubscription(
        Subscription subscription,
        Account account,
        Entity entity,
        TimeZone tenantTimeZone,
        Period forPeriod
    ) {
        List<Charge> charges = getSubscriptionCharges(subscription);

        List<Charge> prePaidCharges = charges.stream().filter(charge -> ChargeType.PREPAID == charge.getType()).collect(Collectors.toList());
        // if prepaid charges are empty return
        if (CollectionUtils.isEmpty(prePaidCharges)) {
            return List.of();
        }

        List<Period> months = DateTimeCalculator.getMonthPeriods(forPeriod.getStart(), forPeriod.getEnd(), tenantTimeZone.toZoneId(), false);
        if (CollectionUtils.isEmpty(months)) {
            return List.of();
        }

        Optional<Charge> drawdownChargeOptional = getDrawdownCharge(subscription, charges, prePaidCharges);
        // this should really not happen, but we have junk data in test stages
        // hence we cannot hard fail we simply return empty list
        if (drawdownChargeOptional.isEmpty()) {
            return List.of();
        }

        Charge drawdownCharge = drawdownChargeOptional.get();
        Plan plan = getPlan(drawdownCharge);
        String drawdownChargeGroupId = getDrawDownChargeGroupId(subscription, drawdownCharge);
        // todo: update because there is no longer just 1 provisioned quantity
        BigDecimal provisionedQuantity = getPrepaidProvisionedQuantity(subscription, prePaidCharges);

        BigDecimal remainingQuantity = provisionedQuantity.add(BigDecimal.ZERO);
        List<PrepaidStats> prepaidStats = new ArrayList<>(months.size());
        Instant firstMonthStart = months.get(0).getStart();
        Instant subscriptionStart = subscription.getStartDate();

        BigDecimal startingConsumedQuantity = getUsageSumForChargeGroupId(
            subscription.getSubscriptionId(),
            drawdownChargeGroupId,
            Period.between(subscriptionStart, firstMonthStart)
        );
        remainingQuantity = remainingQuantity.subtract(startingConsumedQuantity);
        BigDecimal startingQuantity = provisionedQuantity.subtract(startingConsumedQuantity);

        for (Period month : months) {
            BigDecimal usageSumThisMonth = getUsageSumForChargeGroupId(subscription.getSubscriptionId(), drawdownChargeGroupId, month);
            remainingQuantity = remainingQuantity.subtract(usageSumThisMonth);
            PrepaidStats monthlyStat = PrepaidStats.builder()
                .accountId(account.getAccountId())
                .accountName(account.getName())
                .accountCrmId(account.getCrmId())
                .subscriptionId(subscription.getSubscriptionId())
                .entityDisplayId(entity.getDisplayId())
                .subscriptionStartDate(subscription.getStartDate())
                .subscriptionEndDate(subscription.getEndDate())
                .planName(plan.getName())
                .drawdownChargeName(drawdownCharge.getName())
                .provisionedQuantity(provisionedQuantity)
                .startingQuantity(startingQuantity)
                .remainingQuantity(remainingQuantity)
                .consumedQuantity(usageSumThisMonth)
                .periodStart(month.getStart())
                .periodEnd(month.getEnd())
                .build();

            // for all iterations henceforth remainingQuantity becomes startingQuantity
            startingQuantity = remainingQuantity;
            prepaidStats.add(monthlyStat);
        }
        return prepaidStats;
    }

    private Plan getPlan(Charge drawdownCharge) {
        return productCatalogGetService
            .getPlansByIds(List.of(drawdownCharge.getPlanUuid()))
            .stream()
            .findFirst()
            .orElseThrow(() -> {
                String message = String.format("could not find plan for charge %s", drawdownCharge.getChargeId());
                return new ServiceFailureException(message);
            });
    }

    private List<Charge> getSubscriptionCharges(Subscription subscription) {
        // first get all charges for subscription
        List<String> chargeIdsInSubscription = subscription
            .getCharges()
            .stream()
            .map(SubscriptionCharge::getChargeId)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(chargeIdsInSubscription)) {
            return List.of();
        }

        return productCatalogGetService.getChargesByChargeId(chargeIdsInSubscription);
    }

    private BigDecimal getPrepaidProvisionedQuantity(Subscription subscription, List<Charge> prepaidCharges) {
        Set<String> prepaidChargeIds = prepaidCharges.stream().map(Charge::getChargeId).collect(Collectors.toSet());
        List<SubscriptionCharge> prepaidSubscriptionCharges = subscription
            .getCharges()
            .stream()
            .filter(subscriptionCharge -> prepaidChargeIds.contains(subscriptionCharge.getChargeId()))
            .toList();
        Long allPrepaidQuantity = prepaidSubscriptionCharges.stream().map(SubscriptionCharge::getQuantity).reduce(0L, Long::sum);

        return new BigDecimal(allPrepaidQuantity);
    }

    private String getDrawDownChargeGroupId(Subscription subscription, Charge drawdownCharge) {
        List<String> drawdownChargeGroupIds = subscription
            .getCharges()
            .stream()
            .filter(subscriptionCharge -> subscriptionCharge.getChargeId().equals(drawdownCharge.getChargeId()))
            .map(SubscriptionCharge::getSubscriptionChargeGroupId)
            .toList();
        if (drawdownChargeGroupIds.size() != 1) {
            String message = String.format(
                "could not find draw down charge in subscription %s with charge id %s",
                subscription.getSubscriptionId(),
                drawdownCharge.getChargeId()
            );
            LOGGER.error(message);
            throw new ServiceFailureException(message);
        }

        return drawdownChargeGroupIds.get(0);
    }

    private Optional<Charge> getDrawdownCharge(Subscription subscription, List<Charge> charges, List<Charge> prePaidCharges) {
        Set<UUID> prepaidPlanIds = prePaidCharges.stream().map(Charge::getPlanUuid).collect(Collectors.toSet());

        // TODO: filter by drawdown
        List<Charge> drawdownCharges = charges
            .stream()
            .filter(charge -> ChargeType.USAGE == charge.getType() && prepaidPlanIds.contains(charge.getPlanUuid()))
            .toList();

        if (CollectionUtils.isEmpty(drawdownCharges)) {
            String message = String.format(
                "could not find draw down charge for pre paid charge in subscription %s",
                subscription.getSubscriptionId()
            );
            LOGGER.info(message);
            return Optional.empty();
        }

        if (drawdownCharges.size() > 1) {
            String message = String.format(
                "more than one draw down charge found for pre paid charge in subscription %s",
                subscription.getSubscriptionId()
            );
            LOGGER.warn(message);
            return Optional.empty();
        }
        return drawdownCharges.stream().findFirst();
    }

    private Optional<Period> getOverlapPeriodWithSubscription(Subscription subscription, Period givenPeriod) {
        Period subscriptionPeriod = Period.between(subscription.getStartDate(), subscription.getEndDate());
        return Period.overlapOf(givenPeriod, subscriptionPeriod);
    }
}
