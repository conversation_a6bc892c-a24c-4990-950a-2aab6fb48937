package com.subskribe.billy.usage.service;

import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.document.model.DocumentConfiguration;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.graphql.subscription.SubscriptionDataAggregator;
import com.subskribe.billy.graphql.subscription.SubscriptionDetail;
import com.subskribe.billy.shared.csv.SecureCSVPrinter;
import com.subskribe.billy.shared.formatter.NumberFormatter;
import com.subskribe.billy.shared.logger.Logger;
import com.subskribe.billy.shared.logger.LoggerFactory;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import com.subskribe.billy.shared.render.HtmlToPdfConverter;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.usage.model.PrepaidReportCsvSchema;
import com.subskribe.billy.usage.model.PrepaidStats;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import javax.inject.Inject;
import javax.ws.rs.core.StreamingOutput;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

public class UsageReportRenderingService {

    private static final String DRAWDOWN_PER_SUBSCRIPTION_PDF_NAME_FORMAT = "drawdown_for_%s_until_%s.pdf";

    private static final MustacheFactory MUSTACHE_FACTORY = new DefaultMustacheFactory();

    private static final String DEFAULT_MUSTACHE_TEMPLATE_FILENAME = "templates/defaultUsage.mustache";

    private static final Mustache MUSTACHE = MUSTACHE_FACTORY.compile(DEFAULT_MUSTACHE_TEMPLATE_FILENAME);

    private static final CSVFormat PREPAID_DRAWDOWN_REPORT_FORMAT = CSVFormat.Builder.create()
        .setHeader(PrepaidReportCsvSchema.PREPAID_REPORT_HEADERS.toArray(String[]::new))
        .setAllowMissingColumnNames(true)
        .build();

    private static final DateTimeFormatter REPORT_FILE_NAME_DATE_FORMAT = DateTimeFormatter.ofPattern("MM_dd_yyyy");

    private static final DateTimeFormatter PREPAID_DRAWDOWN_REPORT_DATE_FORMAT = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    private static final DecimalFormat USAGE_QUANTITY_FORMAT = new DecimalFormat("##0.00");

    private static final String DRAWDOWN_ALL_SUBSCRIPTIONS_FILE_NAME_FORMAT = "drawdown_all_subscriptions_from_%s_to_%s.csv";
    private static final String DRAWDOWN_PER_SUBSCRIPTION_FILE_NAME_FORMAT = "drawdown_for_%s_until_%s.csv";

    private static final Logger LOGGER = LoggerFactory.getLogger(UsageReportRenderingService.class);

    private final DocumentConfiguration documentConfiguration;

    private final UsageStatisticsService usageStatisticsService;

    private final SubscriptionDataAggregator subscriptionDataAggregator;

    private final TenantSettingService tenantSettingService;

    private final TenantService tenantService;

    private final HtmlToPdfConverter htmlToPdfConverter;

    @Inject
    public UsageReportRenderingService(
        BillyConfiguration billyConfiguration,
        UsageStatisticsService usageStatisticsService,
        SubscriptionDataAggregator subscriptionDataAggregator,
        TenantSettingService tenantSettingService,
        TenantService tenantService,
        HtmlToPdfConverter htmlToPdfConverter
    ) {
        documentConfiguration = billyConfiguration.getDocumentConfiguration();
        this.usageStatisticsService = usageStatisticsService;
        this.subscriptionDataAggregator = subscriptionDataAggregator;
        this.tenantSettingService = tenantSettingService;
        this.tenantService = tenantService;
        this.htmlToPdfConverter = htmlToPdfConverter;
    }

    public Pair<Optional<InputStream>, String> getPrepaidDrawStatsForSubscriptionPdf(String subscriptionId) {
        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        Optional<InputStream> pdfStreamOptional = getPrepaidDrawReportPdfStream(subscriptionId);
        String pdfFileName = String.format(DRAWDOWN_PER_SUBSCRIPTION_PDF_NAME_FORMAT, subscriptionId, toFileNameTime(Instant.now(), tenantTimeZone));
        return Pair.of(pdfStreamOptional, pdfFileName);
    }

    public Pair<StreamingOutput, String> getPrepaidDrawStatsForAllSubscriptionsCsv(Period targetPeriod) {
        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        StreamingOutput reportStream = prepaidStatsToCsvOutStream(
            usageStatisticsService.getPrepaidDrawStatsForAllSubscriptions(targetPeriod),
            tenantTimeZone
        );
        // file name will contain from and to in tenant timezone e.g. drawdown_all_subscriptions_form_03_22_2022_to_07_22_2022.csv
        String fileName = String.format(
            DRAWDOWN_ALL_SUBSCRIPTIONS_FILE_NAME_FORMAT,
            toFileNameTime(targetPeriod.getStart(), tenantTimeZone),
            toFileNameTime(targetPeriod.getEnd(), tenantTimeZone)
        );
        return Pair.of(reportStream, fileName);
    }

    public Pair<StreamingOutput, String> getPrepaidDrawStatsForSubscriptionCsv(String subscriptionId) {
        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        StreamingOutput reportStream = prepaidStatsToCsvOutStream(
            usageStatisticsService.getPrepaidDrawStatsForSubscription(subscriptionId),
            tenantTimeZone
        );
        // since the report will be until now the file name will be of the form e.g. drawdown_for_SUB-JC3E7TC_03_22_2022.csv
        String fileName = String.format(DRAWDOWN_PER_SUBSCRIPTION_FILE_NAME_FORMAT, subscriptionId, toFileNameTime(Instant.now(), tenantTimeZone));
        return Pair.of(reportStream, fileName);
    }

    private Optional<InputStream> getPrepaidDrawReportPdfStream(String subscriptionId) {
        if (!documentConfiguration.isEnabled()) {
            return Optional.empty();
        }

        SubscriptionDetail subscriptionDetail = subscriptionDataAggregator.getSubscriptionDetail(subscriptionId);

        // if subscription hasn't started yet, nothing to generate
        if (Instant.ofEpochSecond(subscriptionDetail.getStartDate()).isAfter(Instant.now())) {
            return Optional.empty();
        }

        StringWriter writer = new StringWriter();
        Map<String, Object> renderBindings = getPdfRenderBindings(subscriptionDetail);
        MUSTACHE.execute(writer, renderBindings);
        byte[] pdfBytes = htmlToPdfConverter.generatePdf(writer.toString(), Optional.empty());
        return Optional.of(new ByteArrayInputStream(pdfBytes));
    }

    private Map<String, Object> getPdfRenderBindings(SubscriptionDetail subscriptionDetail) {
        Map<String, Object> context = new HashMap<>();
        TenantSetting tenantSetting = tenantSettingService.getTenantSettingInternal();
        List<PrepaidStats> prepaidStats = usageStatisticsService.getPrepaidDrawStatsForSubscription(subscriptionDetail.getId());
        DocumentRenderFormatter formatter = new DocumentRenderFormatter(tenantSetting.getDefaultTimeZone());

        context.put(BindingKeys.ACCOUNT_NAME, subscriptionDetail.getAccount().getName());
        context.put(BindingKeys.PLAN_NAME, prepaidStats.stream().findFirst().map(PrepaidStats::getPlanName).orElse(StringUtils.EMPTY));
        context.put(BindingKeys.SUBSCRIPTION_START_DATE, formatter.dateFormat(subscriptionDetail.getStartDate()));
        context.put(BindingKeys.SUBSCRIPTION_END_DATE, formatter.dateFormat(subscriptionDetail.getEndDate()));
        context.put(BindingKeys.CHARGE_NAME, prepaidStats.stream().findFirst().map(PrepaidStats::getDrawdownChargeName).orElse(StringUtils.EMPTY));
        context.put(
            BindingKeys.PROVISIONED_QUANTITY,
            prepaidStats
                .stream()
                .findFirst()
                .map(stat -> NumberFormatter.formatQuantityValue(stat.getProvisionedQuantity()))
                .orElse(NumberFormatter.formatQuantityValue(BigDecimal.ZERO))
        );
        context.put(BindingKeys.USAGE_STATISTICS, generateStatsBindings(prepaidStats, formatter));
        context.put(BindingKeys.TENANT_INFO, tenantService.getCurrentTenantInfo());

        return context;
    }

    private List<Map<String, Object>> generateStatsBindings(List<PrepaidStats> prepaidStats, DocumentRenderFormatter formatter) {
        List<Map<String, Object>> usageStats = new ArrayList<>(prepaidStats.size());
        prepaidStats.forEach(stat -> {
            Map<String, Object> statMap = new HashMap<>();
            statMap.put(BindingKeys.USAGE_CHARGE_NAME, stat.getDrawdownChargeName());
            statMap.put(BindingKeys.USAGE_START, formatter.dateFormat(stat.getPeriodStart()));
            statMap.put(BindingKeys.USAGE_END, formatter.dateFormat(stat.getPeriodEnd()));
            statMap.put(BindingKeys.USAGE_PROVISIONED_QUANTITY, NumberFormatter.formatQuantityValue(stat.getProvisionedQuantity()));
            statMap.put(BindingKeys.USAGE_REMAINING_QUANTITY, NumberFormatter.formatQuantityValue(stat.getRemainingQuantity()));
            statMap.put(BindingKeys.USAGE_CONSUMED_QUANTITY, NumberFormatter.formatQuantityValue(stat.getConsumedQuantity()));
            usageStats.add(statMap);
        });
        return usageStats;
    }

    private StreamingOutput prepaidStatsToCsvOutStream(List<PrepaidStats> prepaidStats, TimeZone timeZone) {
        return out -> {
            Writer writer = new BufferedWriter(new OutputStreamWriter(out, StandardCharsets.UTF_8));

            try (SecureCSVPrinter csvPrinter = SecureCSVPrinter.wrap(new CSVPrinter(writer, PREPAID_DRAWDOWN_REPORT_FORMAT))) {
                for (PrepaidStats prepaidStat : prepaidStats) {
                    csvPrinter.printRecord(
                        prepaidStat.getAccountId(),
                        prepaidStat.getAccountName(),
                        prepaidStat.getAccountCrmId(),
                        prepaidStat.getSubscriptionId(),
                        prepaidStat.getEntityDisplayId(),
                        toReportDateTime(prepaidStat.getSubscriptionStartDate(), timeZone),
                        toReportDateTime(prepaidStat.getSubscriptionEndDate(), timeZone),
                        prepaidStat.getPlanName(),
                        prepaidStat.getDrawdownChargeName(),
                        toReportDateTime(prepaidStat.getPeriodStart(), timeZone),
                        toReportDateTime(prepaidStat.getPeriodEnd(), timeZone),
                        USAGE_QUANTITY_FORMAT.format(prepaidStat.getProvisionedQuantity()),
                        USAGE_QUANTITY_FORMAT.format(prepaidStat.getStartingQuantity()),
                        USAGE_QUANTITY_FORMAT.format(prepaidStat.getConsumedQuantity()),
                        USAGE_QUANTITY_FORMAT.format(prepaidStat.getRemainingQuantity())
                    );
                }
                csvPrinter.flush();
                out.flush();
            } catch (IOException e) {
                String message = "Error writing prepaid CSV report";
                LOGGER.error(message, e);
                throw new ServiceFailureException(message, e);
            }
        };
    }

    private static String toReportDateTime(Instant instant, TimeZone timeZone) {
        return ZonedDateTime.ofInstant(instant, timeZone.toZoneId()).format(PREPAID_DRAWDOWN_REPORT_DATE_FORMAT);
    }

    private static String toFileNameTime(Instant instant, TimeZone timeZone) {
        return ZonedDateTime.ofInstant(instant, timeZone.toZoneId()).format(REPORT_FILE_NAME_DATE_FORMAT);
    }

    private static final class BindingKeys {

        private static final String ACCOUNT_NAME = "accountName";
        private static final String PLAN_NAME = "planName";
        private static final String SUBSCRIPTION_START_DATE = "subscriptionStartDate";
        private static final String SUBSCRIPTION_END_DATE = "subscriptionEndDate";
        private static final String CHARGE_NAME = "chargeName";
        private static final String PROVISIONED_QUANTITY = "provisionedQuantity";
        private static final String USAGE_STATISTICS = "usageStatistics";
        private static final String TENANT_INFO = "tenantInfo";

        private static final String USAGE_CHARGE_NAME = "chargeName";
        private static final String USAGE_START = "start";
        private static final String USAGE_END = "end";
        private static final String USAGE_PROVISIONED_QUANTITY = "provisionedQuantity";
        private static final String USAGE_REMAINING_QUANTITY = "remainingQuantity";
        private static final String USAGE_CONSUMED_QUANTITY = "consumedQuantity";
    }
}
