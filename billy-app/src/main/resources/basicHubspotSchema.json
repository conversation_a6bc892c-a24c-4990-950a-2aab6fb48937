[{"name": "deal", "labels": {"singular": "Deal", "plural": "Deals"}, "properties": [{"name": "average_arr", "label": "Subskribe Average ARR", "type": "number", "fieldType": "number", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "delta_arr", "label": "Subskribe Delta ARR", "type": "number", "fieldType": "number", "description": "", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "entry_arr", "label": "Subskribe Entry ARR", "type": "number", "fieldType": "number", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "exit_arr", "label": "Subskribe Exit ARR", "type": "number", "fieldType": "number", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "order_total", "label": "Subskribe Order Total", "type": "number", "fieldType": "number", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "subscription_id", "label": "Subskribe Subscription ID", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_opportunity_link", "label": "Subskribe Opportunity URL", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_id", "label": "Subskribe Order ID", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_status", "label": "Subskribe Order Status", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_primary_order_link", "label": "Subskribe Order URL", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_term_end_date", "label": "Subskribe Term End Date", "type": "datetime", "fieldType": "date", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_term_start_date", "label": "Subskribe Term Start Date", "type": "datetime", "fieldType": "date", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_start_date_type", "label": "Subskribe Order Start Date Type", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_payment_terms", "label": "Subskribe Payment Terms", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_billing_cycle", "label": "Subskribe Billing Cycle", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_type", "label": "Subskribe Order Type", "type": "enumeration", "fieldType": "select", "description": "", "groupName": "subskribe", "options": [{"label": "New", "value": "new", "displayOrder": 0, "hidden": false}, {"label": "Amendment", "value": "amendment", "displayOrder": 1, "hidden": false}, {"label": "Renewal", "value": "renewal", "displayOrder": 2, "hidden": false}, {"label": "Cancel and Restructure", "value": "cancel_and_restructure", "displayOrder": 3, "hidden": false}], "hasUniqueValue": false, "hidden": false, "formField": true}, {"name": "subskribe_average_acv", "label": "Subskribe Average ACV", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_expiration_date", "label": "Subskribe Order Expiration Date", "type": "datetime", "fieldType": "date", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_esign_status", "label": "Subskribe eSign Status", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_esign_link", "label": "Subskribe eSign Link", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_auto_renew", "label": "Subskribe Order Auto Renew", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}]}, {"name": "company", "labels": {"singular": "Company", "plural": "Companies"}, "properties": [{"name": "subskribe_account", "label": "Subskribe Account URL", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}]}]