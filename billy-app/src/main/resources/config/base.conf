#######################################################################################################################
#                             BASE CONFIG THAT IS INCLUDED IN ALL CONFIGURATION                                       #
#######################################################################################################################

envName = "undefined"

attachments {
    maxLengthPerFileInBytes = ******** #10MB is the max size per file
    maxFilesPerAccount = 10
    acceptedFileExtensions = [ "docx", "pdf", "png", "jpeg", "svg", "doc" ]
}

approvalFlow {
  enabled = true
  emailEnabled = true
  emailInternalOnly = true
  sendEmailToApprovers = true
  sendEmailToOwners = true
  sendApprovalSlackNotifications = true
  sendApprovalSlackCommonChannelNotifications = true
  sendApprovalSlackCommonChannelApprovalFlowRequestNotification = true
  sendApprovalSlackCommonChannelApprovedByNotification = true
  sendApprovalSlackCommonChannelRejectedByNotification = true
  sendApprovalSlackCommonChannelOrderApprovedNotification = true
  sendApprovalSlackCommonChannelOrderRejectedNotification = true
  sendApprovalSlackPrivateNotifications = true
  fromEmailAddress = "<EMAIL>"
  slackCallbackUrl = ${apiUrl}"/approvalFlowNotifications"
  slackTokenSecretPrefix = "SLACK_APPROVAL_NOTIFICATION_TOKEN_"
  slackPublicChannelId = "slack-approvals-notifications-public"
  allowOwnerApproval = true
}

cognito {
  region = unconfigured
  callbackUrl = "https://unconfigured"
  signOutUrl = "https://unconfigured"
  emailIdentityArn = "arn:aws:ses:us-east-1:085981900979:identity/subskribe.net"
  shouldInviteNewUsers = false
  uiLoginUrl = ${siteUrl}"/login"
  userPoolNameForTenantPrefix = "Tenant-unconfigured-"
  fromEmailAddress = "<EMAIL>"
}

creditMemoEmail {
  enabled = true
  internalOnly = true
  fromEmailAddress = "<EMAIL>"
}

customField {
  customFieldsPerObject = 20
}

dynamodb {
  region = "us-east-2"
  lockTable = ${envName}"-billy-locks"
  # note the dbUrl is required only for non-AWS dynamodb
  # note the SDK figures the endpoint automatically
  dbUrl = null
}

dbcp {
  url = "*****************************************"
  driverClassName = "org.postgresql.Driver"
  testWhileIdle = true
  testOnBorrow = true
  timeBetweenEvictionRunsMillis = 10000
  maxTotalActive = 50
  maxWaitMillis = 1000
  minEvictableIdleTimeMillis = 30000
  connectionProperties {
    charSet = UTF-8
  }
  validationQuery = "/* Billy Health Check */ SELECT 1"
  validationQueryTimeout = 3
  maxPerUser = 15
  maxPerUserIdle = 15
  minPerUserIdle = 0
  readOnly = false
}

dbcpReadOnly = ${dbcp} {
  readOnlyUrl = "*****************************************"
  maxPerUser = 3
  maxPerUserIdle = 1
  maxTotalActive = 10
  readOnly = true
}

databaseQueryTimeout {
  enabled = true
  queryTimeoutInSeconds = 60
}

dlq {
  enabled = true
  queueUrl = "http://localhost:4566/queue/us-east-1/************/dlq"
  region = "us-east-2"
  pollingIntervalInSeconds = 20
}

# TODO = factor out the S3 client configuration and split out
# buckets by functionality this config is currently messy
document {
  local = true
  approvalMatrixS3Bucket = "approval-matrix-imports"
  gotenbergUrl = "http://localhost:3001"
  invoiceS3Bucket = "invoice-documents"
  creditMemoS3Bucket = "credit-memo-documents"
  orderS3Bucket = "order-documents"
  importS3Bucket = "import-documents"
  attachmentS3Bucket = "attachments"
  reportsS3Bucket = "reports"
  s3Region = "us-east-2"
  s3Url = "http://localhost:4566"
  crmFieldMappingS3Bucket = "crm-field-mapping"
  intelligentSalesRoomS3Bucket = "intelligent-sales-room"
  webhookPayloadS3Bucket = "webhook-payload"
  enabled = true
  orderFormTemplateFileName = "defaultQuote.mustache"
  orderFormTemplateCssFileName = "default.css"
  orderFormTemplateContainerFileName = "defaultQuoteContainer.mustache"
  invoiceTemplateFileName = "defaultInvoice.mustache"
  upsellEarlyRenewalTemplateFileName = "defaultUpsellEarlyRenewal.mustache"
  maxImportRows = 10000
  cancelAndRestructureTemplateFileName = "defaultCancelAndRestructure.mustache"
}

elasticSearch {
  endpoint = "localhost:9200"
  localES = true
  region = "us-east-2"
  slowQueryThreshold = 150
}

entity {
  rlsEnabled = false
}

fx {
  fxProviderType = "SYSTEM"
}

auditlog {
  billyAuditOn = true
  chronoUnit = DAYS
  retention = 15
  createAhead = 2
  partitionDuration = 1
  alwaysSetSessionParams = false
}

flyway {
  baselineDescription = "<< Flyway Baseline >>"
  cleanDisabled = true
  ignoreMissingMigrations = true
  locations = [
    "db/migration"
  ]
  metaDataTableName = "flyway_schema_history"
  sqlMigrationSuffixes = [
    ".sql"
  ]
  placeholders {
      billyAuditOn = true
      billyAuditOn = ${?auditlog.billyAuditOn}
      entityRls = false
  }
  outOfOrder = true
}

logging {
  appenders: [
    {
      type = "console"
      threshold = "INFO"
    },
    {
      archivedFileCount = 5
      archivedLogFilenamePattern = "./logs/billy-%d{yyyy-MMM-dd}.log.gz"
      currentLogFilename = "./logs/billy.log"
      threshold = "WARN"
      type = "file"
    },
    {
      archivedFileCount = 5
      archivedLogFilenamePattern = "./logs/billy-debug-%d{yyyy-MMM-dd-HH}.log.gz"
      currentLogFilename = "./logs/billy-debug.log"
      threshold = "DEBUG"
      type = "file"
    }
  ]
  level = "WARN"
  loggers {
    "notprivacysafe.graphql" = "ERROR"
    # we are turning off logging for ava tax client library since
    # it logs retry ERROR(s) we specifically catch avalara exceptions
    # log and throw (which should be enough to debug issues)
    "net.avalara.avatax.rest.client" = "OFF"
    # turning off info, warning from quickbook sdk
    "com.intuit.logger" = "ERROR"
    "com.intuit.oauth2" = "ERROR"

    # this is leading to suprios errors even when actual API call is fine
    # io.netty.channel.unix.Errors$NativeIoException: recvAddress(..) failed: Connection reset by peer
    # R:api.openai.com/************:443] The connection observed an error, the request cannot be retried as the headers/body were se
    # we can enable logging if and when these issues lead to acutal problems
    "reactor.netty.http.client" = "OFF"
    "com.azure.core.http.netty" = "OFF"
  }
}

memcached {
  hostname = "unconfigured"
  port = 11211
}

metrics {
  frequency = "1 minute"
  reporters: [
    {
      type = "csv"
      file = "/tmp/billymetrics"
    }
  ]
}

notification {
  siteUrl = "http://localhost:8080/"
  snsTopicArn = "unconfigured"
  slackEnabled = true
  webhooksEnabled = true
  httpCallTimeoutInSeconds = 10
  notificationRetryIntervals = [1, 5, 15, 60, 360]
  allowLocalUrls = false
  emailFromAddress = "<EMAIL>"
}

notificationRetryJob {
  enabled = true
  intervalInMinutes = 1
}

quartz {
  enabled = true
  clustered = false
  propertiesFile = "quartz-in-memory.properties"
  clearOnStartup = false
}

quartzQueue {
  enabled = false
  pollingIntervalInSeconds = 10
  queueUrl = "http://localhost:4566/queue/us-east-1/************/Local-Quartz-Queue.fifo"
  region = "us-east-2"
}

searchUpdaterThread {
  enabled = false
  reindexerEnabled = true
}

quartzInvoiceGenerator {
  enabled = true
  intervalInHours = 0
  intervalInMinutes = 15
  intervalInSeconds = 0
}

quartzPaymentProcessor {
  enabled = true
  isCron = true
  dailyAt = "18:00"
}

quartzPaymentReconciler {
  enabled = true
  intervalInMinutes = 60
  intervalInSeconds = 0
}

quartzBulkInvoiceRun {
  enabled = true
  intervalInSeconds = 600
}

quartzSubscriptionStatusUpdater {
  enabled = true
  intervalInMinutes = 60
}

quartzSubscriptionLifecycleNotifications {
    enabled = true
    intervalInMinutes = 30
    hoursBeforeEvent = 1
}

quartzSubscriptionChargeChangeProcessor {
  enabled = true
  isCron = true
  cronExpression = "0 0 * ? * * *"
}

quartzDunning {
  enabled = true
  intervalInMinutes = 1
  jobProcessingEnabled = false
  fromEmailAddress = "<EMAIL>"
  attachInvoiceDocuments = true
}

quartzUsageAggregation {
  # usage aggregation is enabled in all stages
  enabled = true
  # default 5 mins TODO: make it tighter if required in respective stages
  intervalInSeconds = 300
}

quartzRevenueRecognition {
  enabled = true
  # default run every half hour to be tweaked later
  intervalInSeconds = 1800 // 60 * 30
}

eventPump {
  enabled = true
  delayBetweenRunsMs = 250
}

quartzMetricsUpdater {
  enabled = true
  intervalInMinutes = 10
  updateBatchSize = 100
}


rateLimiter {
  maxTenantCallsPerMinute = 1000
  enabled = true
}

salesforce {
  subskribeRedirectUrl = "http://localhost:8080/sfdc"
  apiVersion = "v55.0"
  shouldSyncProductCategory = false
}

idempotency {
  defaultBackOffTimeMs = 500
  # idempotency records are retained for 4 hours
  # but they will be logged for reference
  recordTTLInHours = 4
  idempotencyTableName = "unconfigured"
}

avalara {
  commitTaxTransactions = true
}

secretsManager {
  url = "http://localhost:4566"
  region = "us-west-2"
  databaseSecretName = "LOCAL_CI_DATABASE_SECRET"
  subskribeStripeApiKeySecretName = "SUBSKRIBE_STRIPE_API_KEY_SECRET"
  subskribeStripeEndpointSecretName = "SUBSKRIBE_STRIPE_ENDPOINT_SECRET"
  subskribeStripeClientIdSecretName = "SUBSKRIBE_STRIPE_CLIENT_ID_SECRET"
  datadogApiKeySecretName = "dataDogApiKey"
  rlsEncryptionKeyName = "RLS_ENCRYPTION_KEY_LOCAL_CI"
  hubSpotAppIdSecretName = "HUBSPOT_PUBLIC_APP_ID"
  hubSpotClientIdSecretName = "HUBSPOT_PUBLIC_APP_CLIENT_ID"
  hubSpotClientSecretSecretName = "HUBSPOT_PUBLIC_APP_CLIENT_SECRET"
  hubSpotAppScopesSecretName = "HUBSPOT_PUBLIC_APP_SCOPES"
  lookerClientIdSecretName = "LOOKER_API3_KEY_CLIENT_ID"
  lookerClientSecretSecretName = "LOOKER_API3_KEY_CLIENT_SECRET"
  openAiClientApiKeySecretName = "OPEN_AI_CLIENT_API_KEY"
  pandaDocApiKeySecretName = "PANDA_DOC_API_KEY"
  quoddApiKeySecretName = "QUODD_API_KEY"
  taxJarApiKeySecretName = "TAX_JAR_API_KEY"
  flatfileApiKeySecretName = "FLATFILE_API_KEY"
  mergeApiKeySecretName = "SUBSKRIBE_MERGE_API_KEY_SECRET"
  adobePdfClientIdSecretName = "ADOBE_PDF_CLIENT_ID"
  adobePdfClientSecretSecretName = "ADOBE_PDF_CLIENT_SECRET"
  docxPasswordSecretName = "DOCX_PASSWORD_SECRET"
}

appConfig {
  enabled = true
  ttlSeconds = 30
  initialDownloadAttempts = 10
  initialSleepTimeMs = 10
  application = "billy-"${envName}"-appconfig-app"
  configurationProfile = "billy-"${envName}"-feature-flags"
  environment = "billy-"${envName}"-appconfig-env"
}

server {
  adminConnectors: [
    {
      port = 8081
      type = "http"
    }
  ]

  applicationConnectors: [
    {
      port = 8080
      type = "http"
    }
  ]

  requestLog {
    appenders: [
      {
        type = "console"
        layout {
          type = "access-json"
        }
      }
    ]
  }

  gzip : {
    excludedMimeTypes : ["text/event-stream"]
  }
}

sns {
  local = true
  region = "unconfigured"
}

swagger {
  resourcePackage = "com.subskribe.billy.resources"
  title = "Subskribe API"
  version = 1.0.0
  host = "api.app.subskribe.com"
  basePath = "/"
  schemes = ["https"]
}

email {
  enabled = true
  prependEnvironmentName = true
  region = "us-east-1"
  sesConfigurationSetName = ${envName}"-email-notify"
  internalOnly = true
  local = false
}

lambda {
  local = false
  region = "us-east-2"
  functionNamePrefix = "undefined"
}

docusign {
  subskribeRedirectUri = ${siteUrl}"/callback/docusign"
}

requestHeaderLogger {
  enabled = false
  logTestId = false
}

ipBasedRateLimiter {
  enabled = true
  maxNumberOfRequestsPerMinute = 5
}

siteUrl = "http://localdev.subskribe.net:3000"
apiUrl = ${siteUrl}"/api/backend"

stripe {
  redirectUri = ${siteUrl}"/callback/stripe"
  enabled = true
}

usageAggregationConfiguration {
  # the number of usage rows to be read per tenant in a single run
  # defaults to 1000
  rawUsageAggregationBatchSize = 1000

  # 1 minute by default allowing for the worst drift :)
  # duration is expressed in https://en.wikipedia.org/wiki/ISO_8601#Durations
  # NOTE:duration cannot be negative and cannot be more than 5 mins
  ntpDriftFactor = "PT1M"
}

invoiceEmail {
  enabled = true
  internalOnly = true
  fromEmailAddress = "<EMAIL>"
}

revrec {
  enabled = false
}

salesforceJob {
  enabled = true
  intervalInSeconds = 60
}

fixedAmountDiscount {
  enabled = false
}

requestLogging {
  requestLoggingEnabled = true
  requestLogS3BucketName = "access-audit-log"
}

hubspot {
  redirectUrl = ${siteUrl}"/settings/hubspot"
  basic = false
}

hubSpotEmail {
  enabled = true
  internalOnly = true
  fromEmailAddress = "<EMAIL>"
}

looker {
  baseUrl = "subskribe.cloud.looker.com"
  userRolePrefix = ${envName}
  userGroupPrefix = ${envName}
  tenantModelSuffix = "_"${envName}
  sharedFolderRoot = ${envName}"-shared"
  tenantFolderRoot = ${envName}"-private"
  allowDevelopers = true
  configExportBucketName = "subskribe-reporting-config-"${envName}
  configExportJob {
    enabled = true
    cronExpression = "0 30 * ? * * *"
  }
}

quickbooks {
  environment = SANDBOX
  baseUrl = "https://sandbox-quickbooks.api.intuit.com/v3/company/"
  redirectUrl = ${siteUrl}"/settings/integration"
}

emailNotificationQueue {
  enabled = false
  region = "us-east-2"
  queueUrl = "http://localhost:4566/queue/us-east-1/************/email-notification-queue"
  pollingIntervalInSeconds = 60
}

quartzBillyAdminReporterJob {
  enabled = false
  intervalInMinutes = 5
  fromEmailAddress = "<EMAIL>"
  toEmailAddress = "<EMAIL>"
}

quartzReportingJob {
  enabled = true
  intervalInSeconds = 120
}

revenueRecognition {
  # whether revrec schedule generation job is disabled for test tenants
  scheduleGenerationJobEnabled = true
}

preferredAccountPaymentType {
  shouldSetPreferredPaymentTypeOnAccount = false
  preferredPaymentType = null
}

quartzApprovalFlowEscalationJob {
  enabled = false
  isCron = false
  intervalInMinutes = 10
  intervalInSeconds = 0
}

quartzEntityInvariants {
  enabled = true
  isCron = true
  dailyAt = "12:00"
}

quartzAccountingInvariants {
  enabled = true
  isCron = true
  dailyAt = "12:05"
}

# run hourly at 05 minutes
quartzExchangeRateRefresh {
  enabled = true
  cronExpression = "0 5 * ? * *"
}

# run quickbooks ping on every tuesday at 4pm UTC (9am PST)
quartzQuickbooksPing {
  enabled = true
  cronExpression = "0 0 16 ? * TUE"
}

internalLogin {
  enabled = true
  googleClientId = "************-c2qs0u5raghjqsa9vdq1fms58ddgso9c.apps.googleusercontent.com"
}

quartzApiKeyGarbageCollectorJob {
  enabled = true
  intervalInMinutes = 15
}

emailLinkLogin {
  enabled = true
  fromEmailAddress = "<EMAIL>"
}

customPaymentType {
  includeInvoicePaymentType = false
}

quartzOrderExpiryJob {
  enabled = false
  intervalInSeconds = 300
}

dosFilter {
  enabled = true
  requestTimeoutInSeconds = 600
  maxRequestsPerSecond = 10000
}

arrMetricsReporting {
    pendingRenewalMonths = 12
    cancelNeededToDropArr = false
    includePendingForUsageArr = false
}

tenantNameCache {
  enabled = true
}

crm {
  syncToCrm = true
}

logHeartbeat {
  enabled = true
  frequencyInSeconds = 30
}

tenantSealProtection.enabled = false

taskQueue {
  enabled = true
  useQueue = true
  scheduledAndNotExecutedTaskTimeoutSeconds = 60
  rescheduleTimedOutTaskAfterSeconds = 15
  schedulerDelayBetweenRunsMs = 250
  archiveTaskAfterDays = 14
  queueConfiguration = {
    queueUrl = "http://localhost:4566/queue/us-east-2/************/billy-task-queue"
    region = "us-east-2"
  }
}

kinesis {
    region = "us-east-2"
    useEndpointOverride = false
    forceSharedCapacityMode = false
    # We shouldn't useFastUnsafeStartup this outside of local development and CI. It disables protections against
    # conflicts and contention in consumers in distributed environments.
    useFastUnsafeStartup = false
}

netsuite {
    payloadMapper = "DEFAULT"
    invoiceSyncEnabled = false
    journalEntrySyncEnabled = true
}

openAi {
  assistantId = "asst_T0yH3e6JJv7N2taMwcq01gdM"
}

quartzBulkRevenueRecognition {
  enabled = true
  intervalInMinutes = 1
}

electronicSignature {
  ignoreSignatureVerificationForResellers = false
}
