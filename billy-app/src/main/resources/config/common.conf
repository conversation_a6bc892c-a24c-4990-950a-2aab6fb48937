#######################################################################################################################
#                             COMMON NON-LOCAL ENV CONFIG THAT IS INCLUDED EVERYWHERE                                 #
#######################################################################################################################

include "base.conf"

# env based cognito config

# NOTE: override this env name in all env config extensions
envName = "none"
siteUrl = "https://"${envName}".subskribe.net"

cognito {
  # all non local stages invite users
  shouldInviteNewUsers = true
  callbackUrl = ${siteUrl}"/api/auth/signin"
  signOutUrl = ${siteUrl}"/api/auth/signout"
  userPoolNameForTenantPrefix = "Tenant-"${envName}"-"
}

# point to correct region on all env config files
documentRegion = "none"

document {
  # no local documents for non-local stages
  local = false
  gotenbergUrl = "http://gotenberg7:3000"
  approvalMatrixS3Bucket = "billy-"${envName}"-approval-matrix-imports"
  invoiceS3Bucket = "billy-"${envName}"-invoices-bucket"
  creditMemoS3Bucket = "billy-"${envName}"-credit-memos-bucket"
  orderS3Bucket = "billy-"${envName}"-orders-bucket"
  importS3Bucket = "billy-"${envName}"-import-documents"
  attachmentS3Bucket = "billy-"${envName}"-attachments"
  reportsS3Bucket = "billy-"${envName}"-reports"
  s3Region = ${documentRegion}
  s3Url = "s3."${documentRegion}".amazonaws.com"
  crmFieldMappingS3Bucket = "billy-"${envName}"-crm-field-mapping"
  intelligentSalesRoomS3Bucket = "billy-"${envName}"-intelligent-sales-room"
  webhookPayloadS3Bucket = "billy-"${envName}"-webhook-payload"
  enabled = true
}

logging {
  appenders = [
    {
      type = "console"
      target = "stderr"
      threshold = "INFO"
      layout {
        type = "json"
        timestampFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        prettyPrint = false
        appendLineSeparator = true
        includes = [
          "timestamp",
          "threadName",
          "level",
          "loggerName",
          "message",
          "exception",
          "callerData",
          "mdc"
        ]
        customFieldNames {
          timestamp = "@timestamp"
        }
        flattenMdc = true
        exception {
          rootFirst = true
          depth = "full"
        }
      }
    },
    {
      archivedFileCount = 5
      archivedLogFilenamePattern = "./logs/billy-%d{yyyy-MMM-dd}.log.gz"
      currentLogFilename = "./logs/billy.log"
      threshold = "WARN"
      type = "file"
    },
    {
      archivedFileCount = 5
      archivedLogFilenamePattern = "./logs/billy-debug-%d{yyyy-MMM-dd-HH}.log.gz"
      currentLogFilename = "./logs/billy-debug.log"
      threshold = "DEBUG"
      type = "file"
    }
  ],
  level = "INFO"
}

# see overrides for env specific values
envMetricTag = "env:none"
metricsSecretRegion = "none"

podName = "unknown"
podName = ${?POD_NAME}
podNameMetricTag = "pod_name:"${podName}

metrics {
  frequency = "5 seconds"
  reporters = [
    {
      type = "datadog"
      excludes = ["request.filtering", "response.filtering"]
      expansions = ["COUNT", "RATE_1_MINUTE", "MAX", "P99", "MEDIAN"]
      useSubstringMatching = true
      tags = [${envMetricTag}, ${podNameMetricTag}]
      transport {
        type = "billy_http"
        apiKeySecretName = ${secretsManager.datadogApiKeySecretName}
        secretManagerRegion = ${metricsSecretRegion}
      }
    }
  ]
}

# all non local envs share this quartz configuration
quartz {
  enabled = true
  clustered = true
  propertiesFile = "quartz.properties"
  clearOnStartup = false
}

quartzRevenueRecognition {
  enabled = true
  intervalInSeconds = 300
}

# none of the non-local envs should ever run sns locally
sns.local = false

# none of the non-local envs should every run ES locally
elasticSearch.localES = false

notification.siteUrl = ${siteUrl}"/"

salesforce.subskribeRedirectUrl = ${siteUrl}"/api/backend/sfdc"

# by default ratelimiter is enabled in all stages
rateLimiter.enabled = true

requestLogging {
  requestLogS3BucketName = "billy-"${envName}"-access-audit-log"
}
