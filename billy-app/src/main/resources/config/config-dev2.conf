include "common.conf"

envName = "dev2"
# dev2 is in us-west-2 any exceptions will be otherwise noted
dev2Region = "us-west-2"

swagger.host = "api.dev2.subskribe.net"

# NOTE:dev2 secrets reside in us-east-1 for legacy reasons
dev2SecretsRegion = "us-east-1"

cognito.region = ${dev2Region}

dynamodb.region = ${dev2Region}

dbcp.url = "********************************************************************************************"
dbcpReadOnly.url = "***********************************************************************************************"

dlq {
  queueUrl = "https://sqs.us-west-2.amazonaws.com/085981900979/dev-sqs-billy_DLQ"
  region = ${dev2Region}
}

documentRegion = ${dev2Region}
document {
  approvalMatrixS3Bucket = "billy-dev-approval-matrix-imports"
  invoiceS3Bucket = "billy-dev-invoices-bucket"
  creditMemoS3Bucket = "billy-dev-credit-memos-bucket"
  orderS3Bucket = "billy-dev-orders-bucket"
  importS3Bucket = "billy-dev-import-documents"
  attachmentS3Bucket = "billy-dev-attachments"
  reportsS3Bucket = "billy-dev-reports"
  crmFieldMappingS3Bucket = "billy-dev-crm-field-mapping"
  intelligentSalesRoomS3Bucket = "billy-dev-intelligent-sales-room"
  webhookPayloadS3Bucket = "billy-dev-webhook-payload"
}

elasticSearch {
  endpoint = "https://vpc-dev-billy-search-domain-3l3v6repi2dobus4ofsvuwcqwe.us-west-2.es.amazonaws.com"
  region = ${dev2Region}
}

flyway.locations = [
  "db/migration",
  "db/tenant_bootstrap_dev2"
]

envMetricTag = "env:dev2"
metricsSecretRegion = ${dev2SecretsRegion}

notification.snsTopicArn = "arn:aws:sns:us-west-2:085981900979:dev-sns-billy_TenantNotifications"

quartzInvoiceGenerator {
  intervalInHours = 0
  intervalInMinutes = 5
  intervalInSeconds = 0
}

quartzPaymentProcessor {
  enabled = true
  isCron = false
  intervalInMinutes = 1
  intervalInSeconds = 0
}

quartzPaymentReconciler {
  enabled = true
  intervalInMinutes = 1
  intervalInSeconds = 0
}

# the interval schedule has been reduced for testing
quartzBulkInvoiceRun.intervalInSeconds = 120

quartzQueue {
  queueUrl = "https://sqs.us-west-2.amazonaws.com/085981900979/dev-sqs-billy_Quartz.fifo"
  region = ${dev2Region}
}

sns.region = ${dev2Region}

idempotency.idempotencyTableName = "DEV2_IDEMPOTENCY_TABLE"

secretsManager {
  region = ${dev2SecretsRegion}
  databaseSecretName = "devdbclusterSecretE9ED1FF3-uCXckael3Eei"
  rlsEncryptionKeyName = "RLS_ENCRYPTION_KEY_DEV2"
}

quartz.clearOnStartup = true

quartzDunning {
  enabled = true
  intervalInMinutes = 30
  jobProcessingEnabled = false
  fromEmailAddress = "<EMAIL>"
}

# enable aggregation for DEV2 testing
quartzUsageAggregation.enabled = true

# in dev2 for quick testing the usage aggregation job runs every 60 seconds
quartzUsageAggregation.intervalInSeconds = 60

# the NTP drift factor for DEV2 is 60 seconds
usageAggregationConfiguration.ntpDriftFactor = "PT60S"

auditlog {
  retention = 2
  chronoUnit = WEEKS
}
flyway.placeholders {
      billyAuditOn = ${auditlog.billyAuditOn}
}

memcached.hostname = "dev-billy-memcached.us8bta.cfg.usw2.cache.amazonaws.com"

tenantRlsEncryption.enabled = true

fixedAmountDiscount {
  enabled = true
}

appConfig {
  region = ${dev2Region}
  application = "billy-dev-appconfig-app"
  configurationProfile = "billy-dev-feature-flags"
  environment = "billy-dev-appconfig-env"
}

salesforceJob {
  enabled = true
  # default 90 seconds
  intervalInSeconds = 5
}

requestLogging {
  requestLogS3BucketName = "billy-dev-access-audit-log"
}

email.sesConfigurationSetName = "dev-email-notify"

emailNotificationQueue {
  enabled = true
  region = ${dev2Region}
  queueUrl = "https://sqs.us-west-2.amazonaws.com/085981900979/dev-email-notification-queue"
}

quartzOrderExpiryJob.enabled = true

searchUpdaterThread.enabled = true

rateLimiter {
  maxTenantCallsPerMinute = 2000
}

entity.rlsEnabled = true

taskQueue.queueConfiguration {
  region = ${dev2Region}
  queueUrl = "https://sqs.us-west-2.amazonaws.com/085981900979/dev-billy-queued-task"
}

kinesis {
    region = ${dev2Region}
}

looker.configExportBucketName = "subskribe-reporting-config-dev"

lambda {
  region = ${dev2Region}
  functionNamePrefix = "dev2"
}
