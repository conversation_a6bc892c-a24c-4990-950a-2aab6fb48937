include "config-ci.conf"

memcached.hostname = "localhost"

dbcp {
  url = "*****************************************"
  readOnlyUrl = "*****************************************"
  maxTotalActive = 40
  maxPerUser = 40
  maxPerUserIdle = 40
}`

dynamodb {
    region = "us-east-2"
    dbUrl = "http://localhost:4566"
    lockTable = "local-billy-locks"
}

secretsManager.url = "http://localhost:4566"

dlq.queueUrl = "https://sqs.us-east-2.amazonaws.com/085981900979/CI-DLQ"

document {
  local = true
  enabled = false
  gotenbergUrl = "http://localhost:3000"
  approvalMatrixS3Bucket = "subskribe-test-us-east-2-approval-matrix-imports"
  invoiceS3Bucket = "subskribe-test-us-east-2-invoice-documents"
  creditMemoS3Bucket = "subskribe-test-us-east-2-credit-memo-documents"
  orderS3Bucket = "subskribe-test-us-east-2-order-documents"
  importS3Bucket = "subskribe-test-us-east-2-import-documents"
  attachmentS3Bucket = "subskribe-test-us-east-2-attachments"
  reportsS3Bucket = "subskribe-test-us-east-2-reports"
  s3Url = "http://localhost:4566"
}

quartzQueue.queueUrl = "https://sqs.us-east-2.amazonaws.com/085981900979/CI-Quartz-Queue.fifo"

quartzDunning {
  enabled = false
  intervalInMinutes = 1
  jobProcessingEnabled = false
  fromEmailAddress = "<EMAIL>"
}

sns {
  local = false
  region = "us-east-2"
}

envName = "local"

fixedAmountDiscount {
  enabled = true
}


# event pump is not enabled in CI since testing is done via
# test resource
eventPump.enabled = false

quartzInvoiceGenerator.enabled = false

quartzReportingJob.enabled = false

arrMetricsReporting.cancelNeededToDropArr = true
arrMetricsReporting.includePendingForUsageArr = true

dosFilter.enabled = false

elasticSearch {
  endpoint = "localhost:9200"
  localES = true
}

kinesis {
    endpointOverride = "http://localhost:4566"
}

taskQueue {
  useQueue = true
  schedulerDelayBetweenRunsMs = 500
  queueConfiguration = {
    queueUrl = "http://localhost:4566/queue/us-east-2/000000000000/billy-task-queue"
    region = "us-east-2"
  }
}

requestHeaderLogger.logTestId = true

logging {
  appenders: [
    {
      type = "console"
      threshold = "INFO"
      logFormat = "%-5level [%X{testRunId}] [%thread] %d{HH:mm:ss.SSS} %logger{36} - %msg%n"
    },
    {
      archivedFileCount = 5
      archivedLogFilenamePattern = "./logs/billy-debug-%d{yyyy-MMM-dd-HH}.log.gz"
      currentLogFilename = "./logs/billy-debug.log"
      threshold = "DEBUG"
      type = "file"
      logFormat = "%-5level [%X{testRunId}] [%thread] %d{HH:mm:ss.SSS} %logger{36} - %msg%n"
    }
  ]
}
