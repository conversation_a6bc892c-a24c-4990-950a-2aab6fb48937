#######################################################################################################################
#                                COMMON LOCAL ENV CONFIG INCLUDED IN ENV: LOCAL AND CI                                #
#######################################################################################################################

include "base.conf"

swagger.host = "localhost:8000"
swagger.schemes = ["http"]

approvalFlow.emailEnabled = false

cognito {
  userPoolNameForTenantPrefix = "Tenant-local-"
  region = "us-east-2"
}

# dlq is not enabled in LOCAL or CI
dlq.enabled = false

flyway.locations = [
  "db/migration",
  "db/demo_tenant_migration",
  "db/e2e_tenant_migration"
]

logging.level = "INFO"

# only ERRORS are logged from the kinesis dependency
# if you wish to you debug in case of a kinesis issue
# please change the logging level accordingly
logging.loggers = {
  "com.subskribe.billy.metrics.db" = "ERROR"
  "com.subskribe.billy.shared.db" = "ERROR"
  "com.subskribe.billy.event.streams.kinesis" = "ERROR"
  "com.subskribe.billy.aws.kinesis" = "ERROR"
  "com.subskribe.billy.shared.tenant.TenantContextInjector" = "ERROR"
  "com.subskribe.billy.aws.es.ElasticSearchService" = "ERROR"
  "com.subskribe.billy.search.service" = "ERROR"
  "software.amazon.kinesis" = "ERROR"
  "com.amazonaws.services.kinesis.producer" = "ERROR"
  "com.subskribe" = "DEBUG"
}

logging = {
  level = "INFO"
  appenders = [
    {
      type = "console"
      threshold = "INFO"
      target = "stdout"
      logFormat = "[%boldMagenta(%d{'yyyy-MM-dd HH:mm:ss,SSS'})] [%cyan(%-5level)] [%yellow(%thread)] [%green(%logger{15})] - %msg%n"
      logFormatEscapes = true
      color = true
    }
    ]
  }

# event pump is dsiabled by default locally because of wait for something to be complete pattern
# leads to non-deterministic tests
# TODO: make sure KCL is working locally before enabling this for eventing e2e testing
eventPump.enabled = false

notification {
  slackEnabled = false
  allowLocalUrls = true
}

notificationRetryJob.enabled = false

quartzInvoiceGenerator {
  intervalInHours = 0
  intervalInMinutes = 0
  intervalInSeconds = 5
}

rateLimiter.enabled = false

idempotency.idempotencyTableName = "LOCAL_IDEMPOTENCY_TABLE"

# we do not commit transaction and spam avalara in LOCAL and CI stage
avalara.commitTaxTransactions = false

# for local testing we simply call /v2/usage/aggregate to perform aggregation
quartzUsageAggregation.enabled = false

# for local testing we do not care about NTP drifts
# we set the duration to 0 seconds
usageAggregationConfiguration.ntpDriftFactor = "PT0S"

# Run locally every 1 second to avoid waiting for a long time during e2e tests.
quartzPaymentProcessor {
  enabled = true
  isCron = false
  intervalInMinutes = 0
  intervalInSeconds = 30
}

quartzPaymentReconciler.enabled = false

ipBasedRateLimiter.enabled = false

# NOTE: for local and CI the bulk invoice run is disabled
# for kicking off the bulk invoice run simply call "invoices/test/processBulkInvoiceRun"
# it will simply process the bulk invoice run for that specific tenancy
quartzBulkInvoiceRun.enabled = false

memcached.hostname = "localhost"

salesforceJob.intervalInSeconds = 5

appConfig {
  environment = "billy-dev-appconfig-env"
  region = "us-west-2"
  application = "billy-dev-appconfig-app"
  configurationProfile = "billy-dev-feature-flags"
  enabled = false
}

dynamicFeatures {
  productRulesDemo: false
  revRecInvoice: false
  creditBuckets: false
  separateRemovedLinedFromRestructuredLines: false
  orderPageTimelineRefactor: false
  enableJpyCurrency: false
  useTasksForSubscriptionChanges: false
}

looker {
  baseUrl = "subskribe.cloud.looker.com"
  userRolePrefix = dev2
  userGroupPrefix = dev2
  sharedFolderRoot = "dev2-shared"
  tenantFolderRoot = "dev2-private"
}

emailNotificationQueue.enabled = false

revenueRecognition.scheduleGenerationJobEnabled = false

quartzApprovalFlowEscalationJob {
  enabled = true
  intervalInMinutes = 0
  intervalInSeconds = 30
}

quartzApiKeyGarbageCollectorJob.enabled = false
quartzApiKeyGarbageCollectorJob.intervalInMinutes = 1

tenantNameCache.enabled = false

logHeartbeat.enabled = false

tenantSealProtection.enabled = true

entity.rlsEnabled = true

fx.fxProviderType = "TEST_RANDOM"

quartzEntityInvariants {
  enabled = true
  isCron = true
  dailyAt = "15:00"
}

kinesis {
    useEndpointOverride = true
    endpointOverride = "http://localhost:4566"
    forceSharedCapacityMode = true
}

dbcp {
  maxTotalActive = 25
  maxPerUser = 25
  maxPerUserIdle = 25
}

dbcpReadOnly {
  maxTotalActive = 5
}

hubspot.syncOrderAnnualAmounts = true

email {
   local = true
   endpointOverride = "http://localhost:4566"
}

tax.taxableCountryCodes = ["US", "EU", "NZ", "CA"]

lambda {
  local = true
  endpointOverride = "http://localhost:4566"
  functionNamePrefix = "local"
}
