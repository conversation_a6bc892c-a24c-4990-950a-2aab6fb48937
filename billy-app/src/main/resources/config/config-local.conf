include "common-local.conf"

dynamodb.dbUrl = "http://localhost:4566"

# append db/local_dev_ui_users to any migration we already run from local common
flyway.locations += "db/local_dev_ui_users"

# add local state clean up script which allows for correction of local state if
# required
flyway.locations += "db/local_dev_state_cleanup"

quartz {
  enabled = true
  propertiesFile = "quartz-local.properties"
}

quartzQueue.enabled = false

searchUpdaterThread.enabled = true

# if LOCAL_DOCUMENT_GEN_ENABLED env variable is set use it otherwise revert to what is set already
document.enabled = ${?LOCAL_DOCUMENT_GEN_ENABLED}

quartzInvoiceGenerator.intervalInSeconds = 30

envName = "local"

tenantRlsEncryption.enabled = true

fixedAmountDiscount {
  enabled = true
}

quartzBulkInvoiceRun {
  enabled = ${?LOCAL_BULK_INVOICE_RUN_ENABLED}
  intervalInSeconds = 60
}

appConfig {
  enabled = ${?AWS_APP_CONFIG_ENABLED}
}

quartzDunning {
  enabled = false
  intervalInMinutes = 1
  jobProcessingEnabled = false
  fromEmailAddress = "<EMAIL>"
}

dynamicFeatures = {
  productRulesDemo: true
}

quartzPaymentReconciler {
  enabled: true
  intervalInSeconds: 10
}

eventPump.enabled = true

quartzRevenueRecognition.intervalInSeconds = 60

revenueRecognition.scheduleGenerationJobEnabled = ${?LOCAL_SCHEDULE_GENERATION_JOB_ENABLED}

email.enabled = false

quartzInvoiceGenerator.enabled = false

quartzReportingJob.enabled = false

arrMetricsReporting.cancelNeededToDropArr = true
arrMetricsReporting.includePendingForUsageArr = true

quartzMetricsUpdater {
  intervalInMinutes = 1
  updateBatchSize = 500
}

taskQueue {
  useQueue = false
  schedulerDelayBetweenRunsMs = 500
}

netsuite {
    journalEntrySyncEnabled = true
}