include "common-local.conf"

memcached.hostname = "memcached"

dbcp.url = "*******************************************"
dbcpReadOnly.url = ${dbcp.url}

dynamodb.dbUrl = "http://localstack:4566"
secretsManager.url = "http://localstack:4566"

dlq.queueUrl = "https://sqs.us-east-2.amazonaws.com/085981900979/CI-DLQ"

document {
  local = true
  enabled = true
  gotenbergUrl = "http://gotenberg:3000"
  approvalMatrixS3Bucket = "subskribe-test-us-east-2-approval-matrix-imports"
  invoiceS3Bucket = "subskribe-test-us-east-2-invoice-documents"
  creditMemoS3Bucket = "subskribe-test-us-east-2-credit-memo-documents"
  orderS3Bucket = "subskribe-test-us-east-2-order-documents"
  importS3Bucket = "subskribe-test-us-east-2-import-documents"
  attachmentS3Bucket = "subskribe-test-us-east-2-attachments"
  reportsS3Bucket = "subskribe-test-us-east-2-reports"
  s3Url = "http://localstack:4566"
  crmFieldMappingS3Bucket = "subskribe-test-us-east-2-crm-field-mapping"
  intelligentSalesRoomS3Bucket = "subskribe-test-us-east-2-intelligent-sales-room"
  webhookPayloadS3Bucket = "subskribe-test-us-east-2-webhook-payload"
}

quartzQueue.queueUrl = "https://sqs.us-east-2.amazonaws.com/085981900979/CI-Quartz-Queue.fifo"

quartzDunning {
  enabled = false
  intervalInMinutes = 1
  jobProcessingEnabled = false
  fromEmailAddress = "<EMAIL>"
}

sns {
  local = false
  region = "us-east-2"
}


envName = "ci"

fixedAmountDiscount {
  enabled = true
}


# event pump is not enabled in CI since testing is done via
# test resource
eventPump.enabled = false

quartzInvoiceGenerator.enabled = false

quartzReportingJob.enabled = false

arrMetricsReporting.cancelNeededToDropArr = true
arrMetricsReporting.includePendingForUsageArr = true

dosFilter.enabled = false

elasticSearch {
  endpoint = "elasticsearch:9200"
  localES = true
}

kinesis {
    endpointOverride = "http://localstack:4566"
}

taskQueue {
  useQueue = true
  queueConfiguration = {
    queueUrl = "http://localstack:4566/queue/us-east-2/000000000000/billy-task-queue"
    region = "us-east-2"
  }
}

requestHeaderLogger.logTestId = true

logging {
  appenders: [
    {
      type = "console"
      threshold = "INFO"
      logFormat = "%-5level [%X{testRunId}] [%thread] %d{HH:mm:ss.SSS} %logger{36} - %msg%n"
    },
    {
      archivedFileCount = 5
      archivedLogFilenamePattern = "./logs/billy-debug-%d{yyyy-MMM-dd-HH}.log.gz"
      currentLogFilename = "./logs/billy-debug.log"
      threshold = "DEBUG"
      type = "file"
      logFormat = "%-5level [%X{testRunId}] [%thread] %d{HH:mm:ss.SSS} %logger{36} - %msg%n"
    }
  ]
}

email.endpointOverride = "http://localstack:4566"

quartzPaymentReconciler.enabled = true

lambda.endpointOverride = "http://localstack:4566"

quartzDunning.attachInvoiceDocuments = false

logging.loggers = {
  "com.subskribe.billy.metrics.db" = "INFO"
  "com.subskribe.billy.shared.db" = "INFO"
  "com.subskribe.billy.event.streams.kinesis" = "INFO"
  "com.subskribe.billy.aws.kinesis" = "INFO"
  "com.subskribe.billy.shared.tenant.TenantContextInjector" = "INFO"
  "com.subskribe.billy.aws.es.ElasticSearchService" = "INFO"
  "com.subskribe.billy.search.service" = "INFO"
  "software.amazon.kinesis" = "INFO"
  "com.amazonaws.services.kinesis.producer" = "INFO"
  "com.subskribe" = "DEBUG"
}
