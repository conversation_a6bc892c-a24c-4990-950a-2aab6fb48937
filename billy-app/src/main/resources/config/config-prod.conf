include "common.conf"

envName = "prod"
prodRegion = "us-east-2"
siteUrl = "https://app.subskribe.com"

swagger.host = "api.app.subskribe.com"

cognito {
  region = ${prodRegion}
  # PROD domain is .com hence non standard url override from common.conf
  emailIdentityArn = "arn:aws:ses:us-east-1:137933513575:identity/subskribe.com"
  fromEmailAddress = "<EMAIL>"
}

dynamodb.region = ${prodRegion}

dbcp.url = "*********************************************************************************************"
dbcpReadOnly.url = "************************************************************************************************"

approvalFlow {
  emailInternalOnly = false
  fromEmailAddress = "<EMAIL>"
}

dlq {
  queueUrl = "https://sqs.us-east-2.amazonaws.com/137933513575/prod-sqs-billy_DLQ"
  region = ${prodRegion}
}

documentRegion = ${prodRegion}

quartzDunning {
  enabled = true
  intervalInMinutes = 30
  jobProcessingEnabled = true
  fromEmailAddress = "<EMAIL>"
}

elasticSearch {
  endpoint = "https://vpc-prod-billy-search-domain-23l2lqmi7n6xlzywcgnwgukfwa.us-east-2.es.amazonaws.com"
  region = ${prodRegion}
}

envMetricTag = "env:prod"
metricsSecretRegion = ${prodRegion}

notification {
  snsTopicArn = "arn:aws:sns:us-east-2:137933513575:prod-sns-billy_TenantNotifications"
  emailFromAddress = "<EMAIL>"
}

quartzQueue {
  queueUrl = "https://sqs.us-east-2.amazonaws.com/137933513575/prod-sqs-billy_Quartz.fifo"
  region = ${prodRegion}
}

quartzRevenueRecognition.intervalInSeconds = 1800

quartz.clearOnStartup = true

sns.region = ${prodRegion}

idempotency.idempotencyTableName = "PROD_IDEMPOTENCY_TABLE"

flyway.locations = [
  "db/migration",
  "db/tenant_bootstrap_prod"
]

secretsManager {
  region = ${prodRegion}
  databaseSecretName = "proddbclusterSecret623536FF-hKfY4svPiNZ0"
  rlsEncryptionKeyName = "RLS_ENCRYPTION_KEY_PROD"
}

appConfig {
  region = ${prodRegion}
}

stripe.enabled = true

invoiceEmail {
  internalOnly = false
  fromEmailAddress = "<EMAIL>"
}

creditMemoEmail.fromEmailAddress = "<EMAIL>"
creditMemoEmail.internalOnly = false

memcached.hostname = "prod-billy-memcached.izxnsr.cfg.use2.cache.amazonaws.com"

quickbooks.environment = PRODUCTION
quickbooks.baseUrl = "https://quickbooks.api.intuit.com/v3/company/"

auditlog {
  retention = 260
  chronoUnit = WEEKS
}

hubSpotEmail {
  enabled = false
  internalOnly = true
  fromEmailAddress = "<EMAIL>"
}

looker {
  sharedFolderRoot = "Standard Reports"
  tenantFolderRoot = "Custom Reports"
  tenantModelSuffix = ""
  allowDevelopers = false
}

quartzBillyAdminReporterJob.enabled = true

emailNotificationQueue.enabled = true

emailNotificationQueue {
  region = ${prodRegion}
  queueUrl = "https://sqs.us-east-2.amazonaws.com/137933513575/prod-email-notification-queue"
}

emailLinkLogin.fromEmailAddress = "<EMAIL>"

quartzOrderExpiryJob.enabled = true

searchUpdaterThread.enabled = true

taskQueue.queueConfiguration {
  region = ${prodRegion}
  queueUrl = "https://sqs.us-east-2.amazonaws.com/137933513575/prod-billy-queued-task"
}

kinesis {
    region = ${prodRegion}
}

entity.rlsEnabled = true

email {
  prependEnvironmentName = false
  internalOnly = false
}

quartzMetricsUpdater {
  intervalInMinutes = 1
}

openAi.assistantId = "asst_kOcZkYTLR6VnCfIGP9jEui7w"

lambda {
  region = ${prodRegion}
  functionNamePrefix = "prod"
}

quartzPaymentProcessor {
  enabled = true
  isCron = false
  intervalInMinutes = 60
  intervalInSeconds = 0
}
