include "config-ci.conf"

memcached.hostname = "localhost"

envName = "local"

dbcp.url = "*****************************************"
dbcpReadOnly.url = ${dbcp.url}

dynamodb {
    region = "us-east-2"
    dbUrl = "http://localhost:4566"
    lockTable = "local-billy-locks"
}

secretsManager.url = "http://localhost:4566"

document {
  gotenbergUrl = "http://localhost:3001"
  s3Url = "http://localhost:4566"
  approvalMatrixS3Bucket = "approval-matrix-imports"
  invoiceS3Bucket = "invoice-documents"
  creditMemoS3Bucket = "credit-memo-documents"
  orderS3Bucket = "order-documents"
  importS3Bucket = "import-documents"
  attachmentS3Bucket = "attachments"
  reportsS3Bucket = "reports"
  crmFieldMappingS3Bucket = "crm-field-mapping"
  intelligentSalesRoomS3Bucket = "intelligent-sales-room"
  webhookPayloadS3Bucket = "webhook-payload"
}

elasticSearch {
  localES = true
  endpoint = "localhost:9200"
}

kinesis {
    region = "us-east-2"
    endpointOverride = "http://localhost:4566"
}

taskQueue {
  queueConfiguration = {
    queueUrl = "http://localhost:4566/queue/us-east-2/000000000000/billy-task-queue"
    region = "us-east-2"
  }
}
