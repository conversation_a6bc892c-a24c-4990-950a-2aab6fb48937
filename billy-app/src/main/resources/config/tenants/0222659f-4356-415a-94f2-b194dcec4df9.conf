# dev2 Subskribe Test Tenant
arrMetricsReporting.cancelNeededToDropArr = true
arrMetricsReporting.includePendingForUsageArr = true
approvalFlow.sendEmailToApprovers = false
approvalFlow.sendApprovalSlackNotifications = true
approvalFlow.sendApprovalSlackCommonChannelNotifications = true
approvalFlow.sendApprovalSlackCommonChannelApprovalFlowRequestNotification = false
approvalFlow.sendApprovalSlackCommonChannelApprovedByNotification = false
approvalFlow.sendApprovalSlackCommonChannelRejectedByNotification = false
approvalFlow.sendApprovalSlackPrivateNotifications = true
approvalFlow.slackPublicChannelId = "slack-approvals-public-temp-test"
hubspot.syncCustomFields = true

uiConfiguration {
  orderItemCustomFields: [
    "years",
    "country",
    "Order_Line_Value"
  ]
}