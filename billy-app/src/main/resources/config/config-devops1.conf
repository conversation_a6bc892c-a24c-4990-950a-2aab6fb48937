include "common.conf"

envName = "devops1"
devops1Region = "ca-central-1"

swagger.host = "api.devops1.subskribe.net"

cognito.region = ${devops1Region}

dynamodb.region = ${devops1Region}

dbcp.url = "**********************************************************************************************"
dbcpReadOnly.url = "*************************************************************************************************"

dlq {
  queueUrl = "https://sqs.ca-central-1.amazonaws.com/085981900979/devops1-sqs-billy_DLQ"
  region = ${devops1Region}
}

documentRegion = ${devops1Region}

elasticSearch {
  endpoint = "https://vpc-devops1-billy-search-domain-c4erdcltx5g5m4vgnk7zhzh4di.ca-central-1.es.amazonaws.com"
  region = ${devops1Region}
}

flyway.locations = [
  "db/migration",
  "db/tenant_bootstrap_devops1"
]

# metrics is expensive we do not report metrics in DEVOPS1
# but we send metrics to temp directory
metrics {
  frequency = "1 minute"
  reporters: [
    {
      type = "csv"
      file = "/tmp/billymetrics"
    }
  ]
}

notification.snsTopicArn = "arn:aws:sns:ca-central-1:085981900979:devops1-sns-billy_TenantNotifications"

quartzInvoiceGenerator {
  intervalInHours = 0
  intervalInMinutes = 5
  intervalInSeconds = 0
}

quartzQueue {
  queueUrl = "https://sqs.ca-central-1.amazonaws.com/085981900979/devops1-sqs-billy_Quartz.fifo"
  region = ${devops1Region}
  enabled = false
}

sns.region = ${devops1Region}

idempotency.idempotencyTableName = "DEVOPS1_IDEMPOTENCY_TABLE"

secretsManager {
  region = ${devops1Region}
  databaseSecretName = "devops1dbclusterSecret61CCA-8RWxFibXJXIw"
  rlsEncryptionKeyName = "RLS_ENCRYPTION_KEY_DEVOPS1"
}

appConfig {
  region = ${devops1Region}
}

stripe.enabled = false

memcached.hostname = "devops1-billy-memcached.ezegpa.cfg.cac1.cache.amazonaws.com"

quartzPaymentProcessor.enabled = false

quartzPaymentReconciler.enabled = false


emailNotificationQueue {
  region = ${devops1Region}
  queueUrl = "https://sqs.ca-central-1.amazonaws.com/085981900979/devops1-email-notification-queue"
}

searchUpdaterThread.enabled = true

rateLimiter {
  maxTenantCallsPerMinute = 2000
}

taskQueue.queueConfiguration {
  region = ${devops1Region}
  queueUrl = "https://sqs.ca-central-1.amazonaws.com/085981900979/devops1-billy-queued-task"
}

kinesis {
    region = ${devops1Region}
}

lambda {
  region = ${devops1Region}
  functionNamePrefix = "devops1"
}