include "common.conf"

envName   = "sandbox"
envRegion = "us-east-2"

swagger.host = "api.sandbox.subskribe.net"

cognito.region = "us-east-2"

dynamodb.region = "us-east-2"

approvalFlow.emailInternalOnly = false

dbcp.url = "************************************************************************************************"
dbcpReadOnly.url = "***************************************************************************************************"

dlq {
  queueUrl = "https://sqs.us-east-2.amazonaws.com/085981900979/sandbox-sqs-billy_DLQ"
  region = "us-east-2"
}

documentRegion = "us-east-2"

# reduce interval for responsiveness during UAT / testing
quartzBulkInvoiceRun.intervalInSeconds = 180

quartzDunning {
  enabled = true
  intervalInMinutes = 30
  jobProcessingEnabled = true
  fromEmailAddress = "<EMAIL>"
}

elasticSearch {
  endpoint = "https://vpc-sandbox-billy-search-domain-2j4ms6mm65mqgznj776azotd3u.us-east-2.es.amazonaws.com"
  region = "us-east-2"
}

flyway.locations = [
  "db/migration"
  "db/tenant_bootstrap_sandbox"
]

envMetricTag = "env:sandbox"
metricsSecretRegion = "us-east-2"

notification.snsTopicArn = "arn:aws:sns:us-east-2:085981900979:sandbox-sns-billy_TenantNotifications"

quartz.clearOnStartup = true

quartzQueue {
  queueUrl = "https://sqs.us-east-2.amazonaws.com/085981900979/sandbox-sqs-billy_Quartz.fifo"
  region = "us-east-2"
}

quartzPaymentProcessor {
  enabled = true
  isCron = false
  intervalInMinutes = 1
  intervalInSeconds = 0
}

quartzPaymentReconciler {
  enabled = true
  intervalInMinutes = 1
  intervalInSeconds = 0
}

quartzRevenueRecognition.intervalInSeconds = 1800

sns.region = "us-east-2"

idempotency.idempotencyTableName = "SANDBOX_IDEMPOTENCY_TABLE"

secretsManager {
  region = "us-east-2"
  databaseSecretName = "sandboxdbclusterSecret59C67-bRUELd4Z5pv6"
  datadogApiKeySecretName = ${envName}"/dataDogApiKey"
  rlsEncryptionKeyName = "RLS_ENCRYPTION_KEY_SANDBOX"
}

memcached.hostname = "sandbox-billy-memcached.vj6gb9.cfg.use2.cache.amazonaws.com"

appConfig {
  region = ${envRegion}
}

auditlog {
  retention = 52
  chronoUnit = WEEKS
}

emailNotificationQueue {
  region = ${envRegion}
  queueUrl = "https://sqs.us-east-2.amazonaws.com/085981900979/sandbox-email-notification-queue"
}

looker {
  sharedFolderRoot = "Standard Reports (Sandbox)"
  tenantFolderRoot = "Custom Reports (Sandbox)"
}

quartzBillyAdminReporterJob.enabled = true

emailNotificationQueue.enabled = true

quartzOrderExpiryJob.enabled = true

searchUpdaterThread.enabled = true

taskQueue.queueConfiguration {
  region = ${envRegion}
  queueUrl = "https://sqs.us-east-2.amazonaws.com/085981900979/sandbox-billy-queued-task"
}

kinesis {
    region = ${envRegion}
}

entity.rlsEnabled = true

openAi.assistantId = "asst_PXcxyPiCvEzrVGGguHYIfUgu"

lambda {
  region = ${envRegion}
  functionNamePrefix = "sandbox"
}