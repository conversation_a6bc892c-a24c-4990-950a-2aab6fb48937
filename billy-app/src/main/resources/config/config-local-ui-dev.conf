#######################################################################################################################
#                                LOCAL ENV CONFIG: ENABLES FEATURES NEEDED TO RUN LOCAL UI                            #
#######################################################################################################################

include "config-local.conf"

notification.slackEnabled = true

quartzBulkInvoiceRun.enabled = true

quartzInvoiceGenerator {
  enabled = true
  intervalInHours = 0
  intervalInMinutes = 1
  intervalInSeconds = 0
}

dynamicFeatures {
  orderPageTimelineRefactor: true
}

document.enabled = true
eventPump.enabled = true
revenueRecognition.scheduleGenerationJobEnabled = true

memcached {
  enabled = true
  hostname = "localhost"
  port = 11211
}

