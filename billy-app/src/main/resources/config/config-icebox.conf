include "config-ci.conf"

elasticSearch {
  endpoint = "elasticsearch:9200"
  localES = true
}

# local users are available in ICEBOX as well
flyway.locations += "db/local_dev_ui_users"

# add local state clean up script which allows for correction of local state if
# required
flyway.locations += "db/local_dev_state_cleanup"

# document generation is enabled in icebox
document.enabled = true

# search is enabled for icebox
searchUpdaterThread.enabled = true

quartzReportingJob.enabled = true
arrMetricsReporting.cancelNeededToDropArr = true
arrMetricsReporting.includePendingForUsageArr = true