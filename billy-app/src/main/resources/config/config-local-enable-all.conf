#######################################################################################################################
#                                LOCAL ENV CONFIG ENABLES ALL QUEUES and SERVICES                                     #
#######################################################################################################################

include "config-local.conf"

notification.slackEnabled = true

approvalFlow.emailEnabled = true

quartzInvoiceGenerator {
  intervalInHours = 0
  intervalInMinutes = 5
  intervalInSeconds = 0
}

document.enabled = true
eventPump.enabled = true
revenueRecognition.scheduleGenerationJobEnabled = true
quartzBulkInvoiceRun.enabled = true

memcached {
  enabled = true
  hostname = "localhost"
  port = 11211
}

