include "common.conf"

envName   = "qa01"
envRegion = "us-east-2"

swagger.host = "api.qa01.subskribe.net"

cognito {
  region = "us-east-2"
  emailIdentityArn = "arn:aws:ses:us-east-1:542672998005:identity/subskribe.net"
}

dynamodb.region = "us-east-2"

dbcp.url = "*********************************************************************************************"
dbcpReadOnly.url = "************************************************************************************************"

dlq {
  queueUrl = "https://sqs.us-east-2.amazonaws.com/542672998005/qa01-sqs-billy_DLQ"
  region = "us-east-2"
}

quartzDunning {
  enabled = true
  intervalInMinutes = 30
  jobProcessingEnabled = true
  fromEmailAddress = "<EMAIL>"
}

quartzInvoiceGenerator {
  intervalInHours = 0
  intervalInMinutes = 5
  intervalInSeconds = 0
}

documentRegion = "us-east-2"

elasticSearch {
  endpoint = "https://vpc-qa01-billy-search-domain-2wyighzi2vdpshralvvocsyfxm.us-east-2.es.amazonaws.com"
  region = "us-east-2"
}

flyway.locations = [
  "db/migration"
  "db/tenant_bootstrap_qa01"
]

envMetricTag = "env:qa01"
metricsSecretRegion = "us-east-2"

notification.snsTopicArn = "arn:aws:sns:us-east-2:542672998005:qa01-sns-billy_TenantNotifications"

quartz.clearOnStartup = true

quartzQueue {
  queueUrl = "https://sqs.us-east-2.amazonaws.com/542672998005/qa01-sqs-billy_Quartz.fifo"
  region = "us-east-2"
}

sns.region = "us-east-2"

idempotency.idempotencyTableName = "QA01_IDEMPOTENCY_TABLE"

secretsManager {
  region = "us-east-2"
  databaseSecretName = "qa01dbclusterSecret9EF5BE03-1pxSuA1p31SX"
  datadogApiKeySecretName = qa01"/dataDogApiKey"
  rlsEncryptionKeyName = "RLS_ENCRYPTION_KEY_QA"
}

memcached.hostname = "qa01-billy-memcached.gppnrg.cfg.use2.cache.amazonaws.com"

appConfig {
  region = ${envRegion}
}

email.enabled = true

stripe.enabled = true

quartzPaymentProcessor {
  enabled = true
  isCron = false
  intervalInMinutes = 1
  intervalInSeconds = 0
}

quartzPaymentReconciler.enabled = true

salesforceJob {
  enabled = true
  # default 90 seconds
  intervalInSeconds = 5
}

auditlog.billyAuditOn = true

emailNotificationQueue {
  region = ${envRegion}
  queueUrl = "https://sqs.us-east-2.amazonaws.com/542672998005/qa01-email-notification-queue"
}

emailNotificationQueue.enabled = true

quartzOrderExpiryJob.enabled = true

searchUpdaterThread.enabled = true

entity.rlsEnabled = true

taskQueue.queueConfiguration {
  region = ${envRegion}
  queueUrl = "https://sqs.us-east-2.amazonaws.com/542672998005/qa01-billy-queued-task"
}

kinesis {
    region = ${envRegion}
}

quartzBulkInvoiceRun.intervalInSeconds = 120

openAi.assistantId = "asst_ycuZId2z6a9p2DL9cPsiRLBj"

lambda {
  region = ${envRegion}
  functionNamePrefix = "qa01"
}