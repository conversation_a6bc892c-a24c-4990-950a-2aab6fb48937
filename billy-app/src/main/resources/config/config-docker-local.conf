include "common-local.conf"

dbcp.url = "*******************************************"
dbcpReadOnly.url = ${dbcp.url}

dlq.queueUrl = "https://sqs.us-east-2.amazonaws.com/085981900979/CI-DLQ"

document {
  local = false
  invoiceS3Bucket = "subskribe-test-us-east-2-invoice-documents"
  creditMemoS3Bucket = "subskribe-test-us-east-2-credit-memo-documents"
  orderS3Bucket = "subskribe-test-us-east-2-order-documents"
  importS3Bucket = "subskribe-test-us-east-2-import-documents"
  reportsS3Bucket = "subskribe-test-us-east-2-reports"
  s3Url = "s3.us-east-2.amazonaws.com"
}

quartzQueue.queueUrl = "https://sqs.us-east-2.amazonaws.com/085981900979/CI-Quartz-Queue.fifo"

sns {
  local = false
  region = "us-east-2"
}

