{"name": "account-import-{{import_date}}", "labels": ["account-import-{{import_date}}"], "sheets": [{"name": "Accounts", "slug": "accounts", "allowAdditionalFields": true, "fields": [{"key": "Account.ExternalId", "type": "string", "label": "Account External ID"}, {"key": "Account.Name", "type": "string", "label": "Account Name"}, {"key": "Account.LegalName", "type": "string", "label": "Account Legal Name"}, {"key": "Account.Description", "type": "string", "label": "Account Description"}, {"key": "Account.EntityDisplayIds", "type": "string", "label": "Account Entity Display IDs"}, {"key": "Account.CrmId", "type": "string", "label": "Account CRM ID"}, {"key": "Account.ErpId", "type": "string", "label": "Account ERP ID"}, {"key": "Account.Phone", "type": "string", "label": "Account Phone"}, {"key": "Account.AddressLine1", "type": "string", "label": "Account Address Line 1"}, {"key": "Account.AddressLine2", "type": "string", "label": "Account Address Line 2"}, {"key": "Account.City", "type": "string", "label": "Account City"}, {"key": "Account.State", "type": "string", "label": "Account State"}, {"key": "Account.Zip", "type": "string", "label": "Account ZIP"}, {"key": "Account.Country", "type": "string", "label": "Account Country"}, {"key": "Account.<PERSON><PERSON><PERSON>cy", "type": "string", "label": "Account <PERSON><PERSON><PERSON><PERSON>"}, {"key": "Account.<PERSON><PERSON><PERSON><PERSON>", "type": "enum", "label": "Account Is Reseller", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "Contact.ExternalId", "type": "string", "label": "Contact External ID"}, {"key": "Contact.AccountId", "type": "string", "label": "Contact Account ID"}, {"key": "Contact.ErpId", "type": "string", "label": "Contact ERP ID"}, {"key": "Contact.FirstName", "type": "string", "label": "Contact First Name"}, {"key": "Contact.LastName", "type": "string", "label": "Contact Last Name"}, {"key": "Contact.Email", "type": "string", "label": "Contact Email"}, {"key": "Contact.Phone", "type": "string", "label": "Contact Phone"}, {"key": "Contact.AddressLine1", "type": "string", "label": "Contact Address Line 1"}, {"key": "Contact.AddressLine2", "type": "string", "label": "Contact Address Line 2"}, {"key": "Contact.City", "type": "string", "label": "Contact City"}, {"key": "Contact.State", "type": "string", "label": "Contact State"}, {"key": "Contact.Zip", "type": "string", "label": "Contact ZIP"}, {"key": "Contact.Country", "type": "string", "label": "Contact Country"}, {"key": "Contact.StrictAddressValidation", "type": "enum", "label": "Contact Strict Address Validation", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}]}], "actions": [{"operation": "submitAction", "mode": "foreground", "label": "Submit", "description": "Submit data to Subskribe", "primary": true}, {"operation": "accountAddAccountCustomFieldAction", "mode": "foreground", "label": "Add Account Custom Field", "description": "Add custom field to account", "inputForm": {"type": "simple", "fields": [{"key": "customFieldName", "label": "Custom Field Name", "description": "Name of the custom field", "type": "string", "constraints": [{"type": "required"}]}]}}]}