{"name": "order-import-{{import_date}}", "labels": ["order-import-{{import_date}}"], "sheets": [{"name": "Orders", "slug": "orders", "allowAdditionalFields": true, "fields": [{"key": "Order.ExternalId", "type": "string", "label": "Order External ID"}, {"key": "Order.EntityDisplayId", "type": "string", "label": "Entity Display ID"}, {"key": "Order.OpportunityId", "type": "string", "label": "Order Opportunity ID"}, {"key": "Order.OpportunityName", "type": "string", "label": "Order Opportunity Name"}, {"key": "Order.Name", "type": "string", "label": "Order Name"}, {"key": "Order.Type", "type": "enum", "label": "Order Type", "config": {"options": [{"label": "NEW", "value": "NEW"}, {"label": "AMENDMENT", "value": "AMENDMENT"}, {"label": "CANCEL", "value": "CANCEL"}, {"label": "RENEWAL", "value": "RENEWAL"}, {"label": "AUTO", "value": "AUTO"}, {"label": "", "value": ""}]}}, {"key": "Subscription.Id", "type": "string", "label": "Subscription ID (or Executed Order External ID)"}, {"key": "Account.Id", "type": "string", "label": "Account ID"}, {"key": "BillingContact.Id", "type": "string", "label": "Billing Contact ID"}, {"key": "ShippingContact.Id", "type": "string", "label": "Shipping Contact ID"}, {"key": "Order.StartDate", "type": "string", "label": "Order Start Date"}, {"key": "Order.EndDate", "type": "string", "label": "Order End Date"}, {"key": "Order.BillingCycle", "type": "enum", "label": "Order Billing Cycle", "config": {"options": [{"label": "MONTH", "value": "MONTH"}, {"label": "QUARTER", "value": "QUARTER"}, {"label": "SEMI_ANNUAL", "value": "SEMI_ANNUAL"}, {"label": "YEAR", "value": "YEAR"}, {"label": "PAID_IN_FULL", "value": "PAID_IN_FULL"}, {"label": "", "value": ""}]}}, {"key": "Order.BillingCycleStartDate", "type": "string", "label": "Order Billing Cycle Start Date"}, {"key": "Order.BillingTerm", "type": "enum", "label": "Order Billing Term", "config": {"options": [{"label": "UP_FRONT", "value": "UP_FRONT"}, {"label": "IN_ARREARS", "value": "IN_ARREARS"}, {"label": "", "value": ""}]}}, {"key": "Order.PaymentTerm", "type": "string", "label": "Order Payment Term"}, {"key": "Order.Currency", "type": "string", "label": "Order Currency"}, {"key": "Order.PurchaseOrderNumber", "type": "string", "label": "Order Purchase Order Number"}, {"key": "Order.PredefinedDiscounts", "type": "string", "label": "Order Predefined Discounts"}, {"key": "Order.AutoExecute", "type": "enum", "label": "Automatically Execute Successfully Imported Order", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "Order.ExpectedEntryArr", "type": "string", "label": "Order Expected Entry ARR"}, {"key": "Order.ExpectedExitArr", "type": "string", "label": "Order Expected Exit ARR"}, {"key": "Order.ExpectedTcv", "type": "string", "label": "Order Expected TCV"}, {"key": "Order.AutoRenew", "type": "enum", "label": "Order Auto Renew", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "Order.SkipOrderCreationCustomization", "type": "enum", "label": "Order Skip Order Creation Customization", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "OrderItem.PlanId", "type": "string", "label": "Order Item Plan ID"}, {"key": "OrderItem.ChargeId", "type": "string", "label": "Order Item Charge ID"}, {"key": "OrderItem.Action", "type": "enum", "label": "Order Item Action", "config": {"options": [{"label": "ADD", "value": "ADD"}, {"label": "UPDATE", "value": "UPDATE"}, {"label": "REMOVE", "value": "REMOVE"}, {"label": "RENEWAL", "value": "RENEWAL"}, {"label": "NONE", "value": "NONE"}, {"label": "MISSING_RENEWAL", "value": "MISSING_RENEWAL"}, {"label": "", "value": ""}]}}, {"key": "OrderItem.StartDate", "type": "string", "label": "Order Item Start Date"}, {"key": "OrderItem.EndDate", "type": "string", "label": "Order Item End Date"}, {"key": "OrderItem.Quantity", "type": "string", "label": "Order Item Quantity"}, {"key": "OrderItem.PredefinedDiscounts", "type": "string", "label": "Order Item Predefined Discounts"}, {"key": "OrderItem.DiscountPercent", "type": "string", "label": "Order Item Discount Percent"}, {"key": "OrderItem.Amount", "type": "string", "label": "Order Item Amount"}, {"key": "OrderItem.ListPriceOverride", "type": "string", "label": "Order Item List Price Override"}, {"key": "OrderItem.CustomChargeListUnitPrice", "type": "string", "label": "Order Item Custom Charge List Unit Price"}, {"key": "OrderItem.PriceAttributes", "type": "string", "label": "Order Item Price Attribute Values"}]}], "actions": [{"operation": "submitAction", "mode": "foreground", "label": "Submit", "description": "Submit data to Subskribe", "primary": true}, {"operation": "orderAddOrderCustomFieldAction", "mode": "foreground", "label": "Add Order Custom Field", "description": "Add custom field to order", "inputForm": {"type": "simple", "fields": [{"key": "customFieldName", "label": "Custom Field Name", "description": "Name of the custom field", "type": "string", "constraints": [{"type": "required"}]}]}}, {"operation": "orderAddOrderItemCustomFieldAction", "mode": "foreground", "label": "Add Order Item Custom Field", "description": "Add custom field to order item", "inputForm": {"type": "simple", "fields": [{"key": "customFieldName", "label": "Custom Field Name", "description": "Name of the custom field", "type": "string", "constraints": [{"type": "required"}]}]}}]}