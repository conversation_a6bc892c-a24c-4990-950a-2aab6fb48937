{"name": "catalog-import-{{import_date}}", "labels": ["catalog-import-{{import_date}}"], "sheets": [{"name": "Catalog", "slug": "catalog", "allowAdditionalFields": true, "fields": [{"key": "Product.ExternalId", "type": "string", "label": "Product External ID"}, {"key": "Product.Sku", "type": "string", "label": "Product SKU"}, {"key": "Product.Name", "type": "string", "label": "Product Name"}, {"key": "Product.DisplayName", "type": "string", "label": "Product Display Name"}, {"key": "Product.Description", "type": "string", "label": "Product Description"}, {"key": "Product.CategoryName", "type": "string", "label": "Product Category Name"}, {"key": "Product.EntityDisplayIds", "type": "string", "label": "Product Entity Display IDs"}, {"key": "Plan.ExternalId", "type": "string", "label": "Plan External ID"}, {"key": "Plan.Name", "type": "string", "label": "Plan Name"}, {"key": "Plan.DisplayName", "type": "string", "label": "Plan Display Name"}, {"key": "Plan.Description", "type": "string", "label": "Plan Description"}, {"key": "Plan.Currency", "type": "string", "label": "Plan Currency"}, {"key": "Plan.EntityDisplayIds", "type": "string", "label": "Plan Entity Display IDs"}, {"key": "Charge.ExternalId", "type": "string", "label": "Charge External ID"}, {"key": "Charge.Name", "type": "string", "label": "Charge Name"}, {"key": "Charge.DisplayName", "type": "string", "label": "Charge Display Name"}, {"key": "Charge.Description", "type": "string", "label": "Charge Description"}, {"key": "Charge.Type", "type": "enum", "label": "Charge Type", "config": {"options": [{"label": "ONE_TIME", "value": "ONE_TIME"}, {"label": "RECURRING", "value": "RECURRING"}, {"label": "USAGE", "value": "USAGE"}, {"label": "PREPAID", "value": "PREPAID"}, {"label": "PERCENTAGE_OF", "value": "PERCENTAGE_OF"}, {"label": "", "value": ""}]}}, {"key": "Charge.Model", "type": "enum", "label": "Charge Model", "config": {"options": [{"label": "PER_UNIT", "value": "PER_UNIT"}, {"label": "VOLUME", "value": "VOLUME"}, {"label": "TIERED", "value": "TIERED"}, {"label": "FLAT_FEE", "value": "FLAT_FEE"}, {"label": "BLOCK", "value": "BLOCK"}, {"label": "RATE_CARD_LOOKUP", "value": "RATE_CARD_LOOKUP"}, {"label": "", "value": ""}]}}, {"key": "Charge.Recurrence", "type": "enum", "label": "Charge Recurrence Cycle", "config": {"options": [{"label": "DAY", "value": "DAY"}, {"label": "MONTH", "value": "MONTH"}, {"label": "YEAR", "value": "YEAR"}, {"label": "", "value": ""}]}}, {"key": "Charge.Step", "type": "string", "label": "Charge Recurrence Step"}, {"key": "Charge.Amount", "type": "string", "label": "Charge Amount"}, {"key": "Charge.PriceTier", "type": "string", "label": "Charge Price Tier"}, {"key": "Charge.Percent", "type": "string", "label": "Charge Percent"}, {"key": "Charge.PercentTargetPlans", "type": "string", "label": "Charge Percent Target Plans"}, {"key": "Charge.IsCustom", "type": "enum", "label": "Charge Is Custom", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "Charge.IsRenewable", "type": "enum", "label": "Charge Is Renewable", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "Charge.TaxRateName", "type": "string", "label": "Charge Tax Rate Name"}, {"key": "Charge.TaxRateCode", "type": "string", "label": "Charge Tax Rate Code"}, {"key": "Charge.UnitOfMeasureName", "type": "string", "label": "Charge Unit Of Measure Name"}, {"key": "Charge.ErpId", "type": "string", "label": "Charge ERP ID"}, {"key": "Charge.ItemCode", "type": "string", "label": "Charge Item Code"}, {"key": "Charge.RecognitionRuleName", "type": "string", "label": "Charge Recognition Rule Name"}, {"key": "Charge.LedgerAccount.DeferredRevenue", "type": "string", "label": "Charge Deferred Revenue Ledger Account Name or Code"}, {"key": "Charge.LedgerAccount.RecognizedRevenue", "type": "string", "label": "Charge Recognized Revenue Ledger Account Name or Code"}, {"key": "Charge.LedgerAccount.ContractAsset", "type": "string", "label": "Charge Contract Asset Ledger Account Name or Code"}, {"key": "Charge.LedgerAccount.TaxLiability", "type": "string", "label": "Charge Tax Liability Ledger Account Name or Code"}, {"key": "Charge.IsDrawdown", "type": "enum", "label": "Charge Is Drawdown", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "Charge.IsEventBased", "type": "enum", "label": "Charge Is Event Based", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "Charge.RateCardName", "type": "string", "label": "Charge Rate Card Name"}, {"key": "Charge.DefaultPriceAttributes", "type": "string", "label": "Charge Rate Card Default Price Attributes"}, {"key": "Charge.BillingCycle", "type": "enum", "label": "Charge Billing Cycle", "config": {"options": [{"label": "DEFAULT", "value": "DEFAULT"}, {"label": "PAID_IN_FULL", "value": "PAID_IN_FULL"}, {"label": "MONTH", "value": "MONTH"}, {"label": "QUARTER", "value": "QUARTER"}, {"label": "SEMI_ANNUAL", "value": "SEMI_ANNUAL"}, {"label": "YEAR", "value": "YEAR"}, {"label": "", "value": ""}]}}, {"key": "Charge.MinQuantity", "type": "string", "label": "Charge Min Quantity"}, {"key": "Charge.MaxQuantity", "type": "string", "label": "Charge Max Quantity"}, {"key": "Charge.DefaultQuantity", "type": "string", "label": "Charge Default Quantity"}, {"key": "Charge.DurationInMonths", "type": "string", "label": "Charge Duration In Months"}, {"key": "Charge.IsListPriceEditable", "type": "enum", "label": "Charge Is List Price Editable", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "Charge.ShouldTrackArr", "type": "enum", "label": "Charge Should Track ARR", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "Charge.IsCreditable", "type": "enum", "label": "Charge Is Creditable", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}, {"key": "<PERSON>.IsDiscount", "type": "enum", "label": "Charge Is Discount", "config": {"options": [{"label": "Y", "value": "Y"}, {"label": "N", "value": "N"}, {"label": "", "value": ""}]}}]}], "actions": [{"operation": "submitAction", "mode": "foreground", "label": "Submit", "description": "Submit data to Subskribe", "primary": true}]}