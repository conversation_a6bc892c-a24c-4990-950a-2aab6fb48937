-- [jooq ignore start]
-- ${flyway:timestamp}
-- fly way timestamp makes sure this runs all the time
-- clean up the job queue locally so that previous stateful run failures do not affect other
-- this is mean to be run only on the local box

DO $$
    BEGIN
        IF EXISTS
            ( SELECT 1
              FROM   information_schema.tables
              WHERE  table_schema = 'public'
                AND  table_name = 'invoice_run_item'
            )
        THEN
            -- first delete run items
            DELETE FROM invoice_run_item WHERE run_id IN
                                               (SELECT id FROM bulk_invoice_run WHERE status NOT IN ('COMPLETED', 'FAILED'));

            -- then delete runs
            DELETE FROM bulk_invoice_run WHERE status NOT IN ('COMPLETED', 'FAILED');
        END IF ;
    END
$$ ;

-- [jooq ignore stop]