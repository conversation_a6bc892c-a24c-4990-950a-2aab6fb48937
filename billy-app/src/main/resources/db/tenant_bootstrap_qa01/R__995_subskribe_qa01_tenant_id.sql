-- [jooq ignore start]
-- ${flyway:timestamp}
INSERT INTO tenant (tenant_id, name, email, phone_number, encrypted_password, is_test, created_on, updated_on)
VALUES ('3408f061-4bcd-470c-9c9d-3efaf2a67277', 'Internal', '<EMAIL>', '************', 'zlC0Tes0o4m3PMPyUG3IxeeBv2D2xexU57UPGFZzuU4=', true, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO payment_term_settings(tenant_id)
VALUES ('3408f061-4bcd-470c-9c9d-3efaf2a67277')
ON CONFLICT DO NOTHING;

DO
$do$
    BEGIN
        IF NOT EXISTS (
                SELECT FROM pg_catalog.pg_roles
                WHERE  rolname = '3408f061-4bcd-470c-9c9d-3efaf2a67277') THEN

            create user "3408f061-4bcd-470c-9c9d-3efaf2a67277" WITH PASSWORD 'subskribe-tenant';
        END IF;
    END
$do$;

INSERT INTO auth_tenant_cognito (tenant_id, client_id, user_pool_id, domain_name, created_on, updated_on)
VALUES ('3408f061-4bcd-470c-9c9d-3efaf2a67277', '3105aumheo8ec9jd8jddjhispg', 'us-east-2_WQkJwR5tv', 'billy-qa01-admin-pool-domain', NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user (user_id, tenant_id, display_name, title, email, normalized_email, phone_number, role, state, created_on, updated_on)
VALUES ('USER-BILLY-ADMIN', '3408f061-4bcd-470c-9c9d-3efaf2a67277', 'Prakash - Admin', '', '<EMAIL>', '<EMAIL>', '', 'BILLY_ADMIN', 'ACTIVE', NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user (user_id, tenant_id, display_name, title, email, normalized_email, phone_number, role, state, created_on, updated_on)
VALUES ('USER-BILLY-ADMIN-2', '3408f061-4bcd-470c-9c9d-3efaf2a67277', 'Ugur - Admin', '', '<EMAIL>', '<EMAIL>', '', 'BILLY_ADMIN', 'ACTIVE', NOW(), NOW())
ON CONFLICT DO NOTHING;
-- [jooq ignore stop]
