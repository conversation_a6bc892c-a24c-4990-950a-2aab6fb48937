-- [jooq ignore start]
-- ${flyway:timestamp}
-- Adding a demo tenant
INSERT INTO tenant (tenant_id, name, email, phone_number, encrypted_password, created_on, updated_on)
VALUES ('f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'Demo Tenant', '<EMAIL>', '7781234567', 'lGY0PfwqiN8XXJGyDKZe4Q==', NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO payment_term_settings(tenant_id)
VALUES ('f35504c0-1b8b-4739-a466-caab3f6f6c7e')
ON CONFLICT DO NOTHING;

DO
$do$
    BEGIN
        IF NOT EXISTS (
                SELECT FROM pg_catalog.pg_roles
                WHERE  rolname = 'f35504c0-1b8b-4739-a466-caab3f6f6c7e') THEN

            create user "f35504c0-1b8b-4739-a466-caab3f6f6c7e" WITH PASSWORD 'demo-tenant';
        END IF;
    END
$do$;

INSERT INTO auth_tenant_cognito (tenant_id, client_id, user_pool_id, domain_name, created_on, updated_on)
VALUES ('f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'jtpmcto1sffrbef6ru5ok4ijg', 'us-east-2_c4LuqbbHC', 'subskribe-test', NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO notification (tenant_id, topic_arn, notification_target, notification_target_type, created_on, updated_on, name)
VALUES ('f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'arn:aws:sns:us-east-2:085981900979:Topic-f35504c0-1b8b-4739-a466-caab3f6f6c7e',
        '*********************************************************************************', 'SLACK', NOW(), NOW(), 'Demo Slack Notification')
ON CONFLICT DO NOTHING;

INSERT INTO notification (tenant_id, topic_arn, notification_target, notification_target_type, created_on, updated_on, name)
VALUES ('f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'arn:aws:sns:us-east-2:085981900979:Topic-f35504c0-1b8b-4739-a466-caab3f6f6c7e',
        'http://localhost:8080/demo/log', 'WEBHOOK', NOW(), NOW(), 'Demo Webhook Notification')
ON CONFLICT DO NOTHING;

INSERT INTO notification_event (tenant_id, notification_id, event, created_on, updated_on)
SELECT tenant_id, id, 'INVOICE_POSTED', NOW(), NOW()
FROM notification where (tenant_id = 'f35504c0-1b8b-4739-a466-caab3f6f6c7e' AND notification.notification_target_type = 'SLACK')
ON CONFLICT DO NOTHING;

INSERT INTO notification_event (tenant_id, notification_id, event, created_on, updated_on)
SELECT tenant_id, id, 'SUBSCRIPTION_CREATED', NOW(), NOW()
FROM notification where (tenant_id = 'f35504c0-1b8b-4739-a466-caab3f6f6c7e' AND notification.notification_target_type = 'SLACK')
ON CONFLICT DO NOTHING;

INSERT INTO notification_event (tenant_id, notification_id, event, created_on, updated_on)
SELECT tenant_id, id, 'ORDER_SUBMITTED', NOW(), NOW()
FROM notification where (tenant_id = 'f35504c0-1b8b-4739-a466-caab3f6f6c7e' AND notification.notification_target_type = 'SLACK')
ON CONFLICT DO NOTHING;

INSERT INTO notification_event (tenant_id, notification_id, event, created_on, updated_on)
SELECT tenant_id, id, 'ORDER_EXECUTED', NOW(), NOW()
FROM notification where (tenant_id = 'f35504c0-1b8b-4739-a466-caab3f6f6c7e' AND notification.notification_target_type = 'WEBHOOK')
ON CONFLICT DO NOTHING;

INSERT INTO tenant_invoice_configuration (invoice_config_id, tenant_id,  invoice_number_scheme, invoice_number_prefix, invoice_number_length, proration_scheme)
VALUES ('77435b92-230b-44ef-9835-aad1b98868c3', 'f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'SEQUENCE', 'INV-', 6, 'CALENDAR_DAYS')
ON CONFLICT DO NOTHING;

INSERT INTO tenant_invoice_number_sequence (invoice_config_id, tenant_id, next_invoice_number)
VALUES ('77435b92-230b-44ef-9835-aad1b98868c3', 'f35504c0-1b8b-4739-a466-caab3f6f6c7e', 1)
ON CONFLICT DO NOTHING;

-- create default entity
DO
$do$
BEGIN
        IF NOT EXISTS (
            SELECT FROM entity
                WHERE tenant_id   = 'f35504c0-1b8b-4739-a466-caab3f6f6c7e'
                  AND is_default  = true
                  AND is_deleted  = false
           )
        THEN
            INSERT INTO entity(entity_id, tenant_id, display_id, name, proration_scheme, proration_mode, invoice_config_id, timezone, functional_currency, is_default)
            VALUES ('ENT-DEMOTEN', 'f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'ENT-1', 'default', 'CALENDAR_DAYS', 'NORMALIZED', '77435b92-230b-44ef-9835-aad1b98868c3', 'US/Pacific', 'USD', true)
                ON CONFLICT DO NOTHING;
END IF;
END
$do$;

INSERT INTO tenant_payment_configuration (tenant_id, supported_payment_types)
VALUES ('f35504c0-1b8b-4739-a466-caab3f6f6c7e', '["EXTERNAL", "CHECK", "WIRE", "CARD", "ACH"]'::JSON)
ON CONFLICT (tenant_id) DO UPDATE SET supported_payment_types = '["EXTERNAL", "CHECK", "WIRE", "CARD", "ACH"]'::JSON;

INSERT INTO tenant_setting (tenant_id, default_time_zone, seal)
VALUES ('f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'US/Pacific', 'ON')
ON CONFLICT (tenant_id) DO UPDATE SET seal = 'ON';

INSERT INTO platform_feature (tenant_id, name, enabled, enabled_by_user_id)
VALUES ('f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'REVENUE_RECOGNITION', true, 'SYSTEM')
ON CONFLICT DO NOTHING;

INSERT INTO platform_feature (tenant_id, name, enabled, enabled_by_user_id)
VALUES ('f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'BILLING', true, 'SYSTEM')
ON CONFLICT DO NOTHING;

-- [jooq ignore stop]
