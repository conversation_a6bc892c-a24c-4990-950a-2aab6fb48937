-- [jooq ignore start]
-- ${flyway:timestamp}
-- Adding a test tenant which is used for e2e tests
INSERT INTO tenant (tenant_id, name, email, phone_number, encrypted_password, is_test, created_on, updated_on)
VALUES ('7911efdc-331d-4c7f-95d4-152fadd06f5a', 'e2e tests', '<EMAIL>', '6041234567', '62NWaMMwvZEUwK6LVn+2IQ==', true, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO payment_term_settings(tenant_id)
VALUES ('7911efdc-331d-4c7f-95d4-152fadd06f5a')
ON CONFLICT DO NOTHING;

DO
$do$
    BEGIN
        IF NOT EXISTS (
                SELECT FROM pg_catalog.pg_roles
                WHERE  rolname = '7911efdc-331d-4c7f-95d4-152fadd06f5a') THEN

            create user "7911efdc-331d-4c7f-95d4-152fadd06f5a" WITH PASSWORD 'e2e-tests';
        END IF;
    END
$do$;

INSERT INTO auth_tenant_cognito (tenant_id, client_id, user_pool_id, domain_name)
VALUES ('7911efdc-331d-4c7f-95d4-152fadd06f5a', 'TEST_CLIENT_ID', 'TEST_USER_ID', 'TEST_DOMAIN')
ON CONFLICT DO NOTHING;

INSERT INTO tenant_invoice_configuration (invoice_config_id, tenant_id,  invoice_number_scheme, invoice_number_prefix, invoice_number_length, proration_scheme)
VALUES ('bf39489a-cbe0-47b4-b9e4-de1ebd8c520c', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'SEQUENCE', 'INV-', 6, 'CALENDAR_DAYS')
    ON CONFLICT DO NOTHING;

INSERT INTO tenant_invoice_number_sequence (invoice_config_id, tenant_id, next_invoice_number)
VALUES ('bf39489a-cbe0-47b4-b9e4-de1ebd8c520c', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 1)
    ON CONFLICT DO NOTHING;

-- create default entity
DO
$do$
BEGIN
        IF NOT EXISTS (
            SELECT FROM entity
                WHERE tenant_id   = '7911efdc-331d-4c7f-95d4-152fadd06f5a'
                  AND is_default  = true
                  AND is_deleted  = false
           )
        THEN
            INSERT INTO entity(entity_id, tenant_id, display_id, name, proration_scheme, proration_mode, invoice_config_id, timezone, functional_currency, is_default)
            VALUES ('ENT-E2ETEST', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'ENT-1', 'default', 'CALENDAR_DAYS', 'NORMALIZED', 'bf39489a-cbe0-47b4-b9e4-de1ebd8c520c', 'US/Pacific', 'USD', true)
                ON CONFLICT DO NOTHING;
        END IF;
END
$do$;

-- add e2e test users with specific roles

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids, terms_accepted_on)
VALUES ('Admin Test User', 'ADMIN', 'ACTIVE', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'USER-ADMIN', '<EMAIL>', '<EMAIL>', ARRAY['*'], '2020-01-01'::TIMESTAMP)
    ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids, terms_accepted_on)
VALUES ('Finance Test User', 'FINANCE', 'ACTIVE', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'USER-FINANCE', '<EMAIL>', '<EMAIL>', ARRAY['*'], '2020-01-01'::TIMESTAMP)
    ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids, terms_accepted_on)
VALUES ('Sales Test User', 'SALES', 'ACTIVE', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'USER-SALES', '<EMAIL>', '<EMAIL>', ARRAY['*'], '2020-01-01'::TIMESTAMP)
    ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids, terms_accepted_on)
VALUES ('Sales Manager Test User', 'SALES_MANAGER', 'ACTIVE', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'USER-SALES-MANAGER', '<EMAIL>', '<EMAIL>', ARRAY['*'], '2020-01-01'::TIMESTAMP)
    ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids, terms_accepted_on)
VALUES ('Accountant Test User', 'ACCOUNTANT', 'ACTIVE', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'USER-ACCOUNTANT', '<EMAIL>', '<EMAIL>', ARRAY['*'], '2020-01-01'::TIMESTAMP)
    ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids, terms_accepted_on)
VALUES ('Billing Clerk Test User', 'BILLING_CLERK', 'ACTIVE', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'USER-BILLING-CLERK', '<EMAIL>', '<EMAIL>', ARRAY['*'], '2020-01-01'::TIMESTAMP)
    ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids, terms_accepted_on)
VALUES ('Revenue Clerk Test User', 'REVENUE_CLERK', 'ACTIVE', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'USER-REVENUE-CLERK', '<EMAIL>', '<EMAIL>', ARRAY['*'], '2020-01-01'::TIMESTAMP)
    ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids, terms_accepted_on)
VALUES ('Read Only Test User', 'READ_ONLY', 'ACTIVE', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'USER-READ-ONLY', '<EMAIL>', '<EMAIL>', ARRAY['*'], '2020-01-01'::TIMESTAMP)
    ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids, terms_accepted_on)
VALUES ('Executive Test User', 'EXECUTIVE', 'ACTIVE', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'USER-EXECUTIVE', '<EMAIL>', '<EMAIL>', ARRAY['*'], '2020-01-01'::TIMESTAMP)
    ON CONFLICT DO NOTHING;

INSERT INTO api_key (id, tenant_id, role, generated_by, vault_key_id, is_active, expires_on, user_id, entity_id)
VALUES ('6493bee2-b0ba-4f58-b2fe-423d5aaada86', '7911efdc-331d-4c7f-95d4-152fadd06f5a', 'ADMIN', 'R__998', 'E2E-TESTS-SECRET-KEY-ID', TRUE, NULL, 'USER-ADMIN', '*')
    ON CONFLICT DO NOTHING;


INSERT INTO account_address(id, address_id, tenant_id, street_address_line1, street_address_line2, city, state, country, zipcode)
VALUES ('2edd6f70-1af6-4d86-9088-1c4fce9c35db', '2587d6d6-697f-4d77-8a9d-53c3bac30004', '7911efdc-331d-4c7f-95d4-152fadd06f5a', '607 N Elm Dr', null, 'Beverly Hills', 'CA', 'US', '90210-3420')
    ON CONFLICT DO NOTHING;

UPDATE account SET name = 'Bose Corp (OLD)' WHERE crm_id = '0010300000QBcTzAAL';

INSERT INTO tenant_credit_memo_number_sequence (tenant_id, next_number)
SELECT tenant_id, 1 FROM tenant where is_deleted = false
ON CONFLICT DO NOTHING;

INSERT INTO tenant_payment_configuration (tenant_id, supported_payment_types)
VALUES ('7911efdc-331d-4c7f-95d4-152fadd06f5a', '["EXTERNAL", "CHECK", "WIRE", "CARD", "ACH"]'::JSON)
ON CONFLICT (tenant_id) DO UPDATE SET supported_payment_types = '["EXTERNAL", "CHECK", "WIRE", "CARD", "ACH"]'::JSON;

-- [jooq ignore stop]
