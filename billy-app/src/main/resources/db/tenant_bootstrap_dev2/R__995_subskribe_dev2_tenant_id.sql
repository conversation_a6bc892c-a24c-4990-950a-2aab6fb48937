-- [jooq ignore start]
-- ${flyway:timestamp}
INSERT INTO tenant (tenant_id, name, email, phone_number, encrypted_password, is_test, created_on, updated_on)
VALUES ('24dbe72c-1e3a-47d2-bff0-56703984267a', 'Internal', '<EMAIL>', '************', 'BiejzoTs5vtSVWqbO04Hunj6LdoIHlKiRIIYXw1hoL4=', true, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO payment_term_settings(tenant_id)
VALUES ('24dbe72c-1e3a-47d2-bff0-56703984267a')
ON CONFLICT DO NOTHING;

DO
$do$
    BEGIN
        IF NOT EXISTS (
                SELECT FROM pg_catalog.pg_roles
                WHERE  rolname = '24dbe72c-1e3a-47d2-bff0-56703984267a') THEN

            create user "24dbe72c-1e3a-47d2-bff0-56703984267a" WITH PASSWORD 'subskribe-tenant';
        END IF;
    END
$do$;

INSERT INTO auth_tenant_cognito (tenant_id, client_id, user_pool_id, domain_name, created_on, updated_on)
VALUES ('24dbe72c-1e3a-47d2-bff0-56703984267a', '1qfm6i18la87lfbml9is74deeh', 'us-west-2_LVN2s0nhU', 'billy-dev-admin-pool-domain', NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user (user_id, tenant_id, display_name, title, email, normalized_email, phone_number, role, state, created_on, updated_on)
VALUES ('USER-BILLY-ADMIN', '24dbe72c-1e3a-47d2-bff0-56703984267a', 'Billy Admin', 'Billy Admin', '<EMAIL>', '<EMAIL>', '************', 'BILLY_ADMIN', 'ACTIVE', NOW(), NOW())
ON CONFLICT DO NOTHING;
-- [jooq ignore stop]
