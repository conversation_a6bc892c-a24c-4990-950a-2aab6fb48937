-- [jooq ignore start]
-- ${flyway:timestamp}
-- NOTE: the any user added here needs to be in Cognito for login to work fully
INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids)
VALUES ('David', 'BILLY_ADMIN', 'ACTIVE', 'f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'USER-DAVID', 'seung<PERSON><EMAIL>', '<EMAIL>', ARRAY['*'])
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids)
VALUES ('Subbu', 'BILLY_ADMIN', 'ACTIVE', 'f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'USER-SUBBU', '<EMAIL>', '<EMAIL>', ARRAY['*'])
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids)
VALUES ('Seyed', 'BILLY_ADMIN', 'ACTIVE', 'f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'USER-SEYED', '<EMAIL>', '<EMAIL>', ARRAY['*'])
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids)
VALUES ('Test User', 'ADMIN', 'ACTIVE', 'f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'USER-TEST', '<EMAIL>', '<EMAIL>', ARRAY['*'])
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids)
VALUES ('Test Sales User', 'SALES', 'ACTIVE', 'f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'USER-TEST-SALES', '<EMAIL>', '<EMAIL>', ARRAY['*'])
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids)
VALUES ('Test Finance User', 'FINANCE', 'ACTIVE', 'f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'USER-TEST-FINANCE', '<EMAIL>', '<EMAIL>', ARRAY['*'])
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user(display_name, role, state, tenant_id, user_id, email, normalized_email, entity_ids)
VALUES ('Test Admin User', 'ADMIN', 'ACTIVE', 'f35504c0-1b8b-4739-a466-caab3f6f6c7e', 'USER-TEST-ADMIN', '<EMAIL>', '<EMAIL>', ARRAY['*'])
ON CONFLICT DO NOTHING;

-- [jooq ignore stop]
