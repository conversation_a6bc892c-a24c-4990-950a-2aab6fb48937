-- [jooq ignore start]
-- ${flyway:timestamp}
INSERT INTO tenant (tenant_id, name, email, phone_number, encrypted_password, is_test, created_on, updated_on)
VALUES ('0f6d961d-3832-4f76-ad07-a5286dcbbc9a', 'Internal', '<EMAIL>', '************', 'GhfLOVYM6RtOi+mE2pjxppZrQczUheklUibrMa9OvE0=', true, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO payment_term_settings(tenant_id)
VALUES ('0f6d961d-3832-4f76-ad07-a5286dcbbc9a')
ON CONFLICT DO NOTHING;

DO
$do$
    BEGIN
        IF NOT EXISTS (
                SELECT FROM pg_catalog.pg_roles
                WHERE  rolname = '0f6d961d-3832-4f76-ad07-a5286dcbbc9a') THEN

            create user "0f6d961d-3832-4f76-ad07-a5286dcbbc9a" WITH PASSWORD 'subskribe-tenant';
        END IF;
    END
$do$;

INSERT INTO auth_tenant_cognito (tenant_id, client_id, user_pool_id, domain_name, created_on, updated_on)
VALUES ('0f6d961d-3832-4f76-ad07-a5286dcbbc9a', '4mksvlb9kcq7chptlaqtkft96f', 'ca-central-1_mb3AHZV8O', 'billy-devops1-admin-pool-domain', NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user (user_id, tenant_id, display_name, title, email, normalized_email, phone_number, role, state, created_on, updated_on)
VALUES ('USER-BILLY-ADMIN', '0f6d961d-3832-4f76-ad07-a5286dcbbc9a', 'Billy Admin', 'Billy Admin', '<EMAIL>', '<EMAIL>', '************', 'BILLY_ADMIN', 'ACTIVE', NOW(), NOW())
ON CONFLICT DO NOTHING;
-- [jooq ignore stop]
