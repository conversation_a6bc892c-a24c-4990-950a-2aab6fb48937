CREATE TABLE IF NOT EXISTS intelligent_sales_room
(
    id               UUID         NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    share_link       UUID         NOT NULL UNIQUE DEFAULT uuid_generate_v4(),
    tenant_id        VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    account_id       VARCHAR(36)  NOT NULL REFERENCES account (account_id),
    order_id         VARCHAR(36)  NOT NULL REFERENCES account_order (order_id),
    status           VARCHAR(20)  NOT NULL DEFAULT 'DRAFT',
    is_first_access  BOOLEAN      NOT NULL DEFAULT TRUE,
    shared_on        TIMESTAMP,
    accepted_on      TIMESTAMP,
    expires_on       TIMESTAMP    NOT NULL DEFAULT (NOW() + INTERVAL '60 days'),
    is_deleted       BOOLEAN      NOT NULL DEFAULT FALSE,
    created_on       TIMESTAMP,
    updated_on       TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_intelligent_sales_rooms_tenant_order ON intelligent_sales_room(tenant_id, order_id);
CREATE INDEX IF NOT EXISTS index_intelligent_sales_rooms_tenant_status ON intelligent_sales_room(tenant_id, status);
CREATE INDEX IF NOT EXISTS index_intelligent_sales_rooms_share_link ON intelligent_sales_room(share_link);

COMMENT ON TABLE intelligent_sales_room IS '{"audited": true, "search": { "enabled" : false } }';

CREATE TABLE IF NOT EXISTS intelligent_sales_room_widget
(
    id                UUID         NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id         VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    sales_room_id     UUID         NOT NULL REFERENCES intelligent_sales_room (id),
    widget_type       VARCHAR(50)  NOT NULL,
    widget_name       VARCHAR(255) NOT NULL,
    sort_order        INTEGER      NOT NULL,
    is_user_generated BOOLEAN      NOT NULL DEFAULT FALSE,
    is_deletable      BOOLEAN      NOT NULL DEFAULT FALSE,
    content           JSONB,
    is_deleted        BOOLEAN      NOT NULL DEFAULT FALSE,
    created_on        TIMESTAMP,
    updated_on        TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_intelligent_sales_room_widgets_tenant_sales_room ON intelligent_sales_room_widget(tenant_id, sales_room_id);

COMMENT ON TABLE intelligent_sales_room_widget IS '{"audited": true, "search": { "enabled" : false } }';

CREATE TABLE IF NOT EXISTS intelligent_sales_room_file
(
    id                 UUID         NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id          VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    sales_room_id      UUID         NOT NULL REFERENCES intelligent_sales_room (id),
    file_name          VARCHAR(512) NOT NULL,
    original_file_name VARCHAR(512) NOT NULL,
    file_size          BIGINT       NOT NULL,
    mime_type          VARCHAR(100) NOT NULL,
    file_type          VARCHAR(50)  NOT NULL,
    file_category      VARCHAR(50)  NOT NULL,
    is_deleted         BOOLEAN      NOT NULL DEFAULT FALSE,
    created_on         TIMESTAMP,
    updated_on         TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_intelligent_sales_room_files_tenant_sales_room ON intelligent_sales_room_file(tenant_id, sales_room_id);

COMMENT ON TABLE intelligent_sales_room_file IS '{"audited": true, "search": { "enabled" : false } }';

CREATE TABLE IF NOT EXISTS intelligent_sales_room_theme
(
    id                      UUID           NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id               VARCHAR(36)    NOT NULL REFERENCES tenant (tenant_id),
    sales_room_id           UUID           NOT NULL REFERENCES intelligent_sales_room (id),
    website_url             VARCHAR(1024),
    logo_url                TEXT,
    primary_color           VARCHAR(100),
    secondary_color         VARCHAR(100),
    template                VARCHAR(20)    NOT NULL,
    is_logo_auto_extracted  BOOLEAN        NOT NULL DEFAULT TRUE,
    is_color_auto_extracted BOOLEAN        NOT NULL DEFAULT TRUE,
    is_deleted              BOOLEAN        NOT NULL DEFAULT FALSE,
    created_on              TIMESTAMP,
    updated_on              TIMESTAMP,
    UNIQUE(tenant_id, sales_room_id)
);

CREATE INDEX IF NOT EXISTS index_intelligent_sales_room_themes_tenant_sales_room ON intelligent_sales_room_theme(tenant_id, sales_room_id);

COMMENT ON TABLE intelligent_sales_room_theme IS '{"audited": true, "search": { "enabled" : false } }';

CREATE TABLE IF NOT EXISTS intelligent_sales_room_engagement_session
(
    id                  UUID          NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id           VARCHAR(36)   NOT NULL REFERENCES tenant (tenant_id),
    sales_room_id       UUID          NOT NULL REFERENCES intelligent_sales_room (id),
    user_name           VARCHAR(1024) NOT NULL,
    user_email          VARCHAR(1024) NOT NULL,
    session_id          UUID          NOT NULL DEFAULT uuid_generate_v4(),
    session_start       TIMESTAMP     NOT NULL DEFAULT NOW(),
    session_end         TIMESTAMP,
    last_activity_time  TIMESTAMP,
    duration_seconds    BIGINT        NOT NULL DEFAULT 0,
    ip_address          VARCHAR(100),
    user_agent          TEXT,
    tabs_visited        TEXT[]        DEFAULT '{}',
    activity_data       JSONB,
    is_active           BOOLEAN       NOT NULL DEFAULT TRUE,
    is_deleted          BOOLEAN       NOT NULL DEFAULT FALSE,
    created_on          TIMESTAMP,
    updated_on          TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_isr_engagement_sessions_tenant_sales_room ON intelligent_sales_room_engagement_session(tenant_id, sales_room_id);


COMMENT ON TABLE intelligent_sales_room_engagement_session IS '{"audited": true, "search": { "enabled" : true } }';

CREATE TABLE IF NOT EXISTS intelligent_sales_room_activity_log
(
    id                 UUID          NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id          VARCHAR(36)   NOT NULL REFERENCES tenant (tenant_id),
    sales_room_id      UUID          NOT NULL REFERENCES intelligent_sales_room (id),
    session_id         UUID          NOT NULL,
    user_name          VARCHAR(1024) NOT NULL,
    user_email         VARCHAR(1024) NOT NULL,
    activity           VARCHAR(100)  NOT NULL,
    tab_name           TEXT,
    widget_id          UUID          REFERENCES intelligent_sales_room_widget (id),
    time_spent_seconds BIGINT        NOT NULL DEFAULT 0,
    timestamp          TIMESTAMP     NOT NULL DEFAULT NOW(),
    metadata           JSONB,
    is_deleted         BOOLEAN       NOT NULL DEFAULT false,
    created_on         TIMESTAMP,
    updated_on         TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_intelligent_sales_room_activity_logs_tenant_session ON intelligent_sales_room_activity_log(tenant_id, session_id);
CREATE INDEX IF NOT EXISTS index_intelligent_sales_room_activity_logs_tenant_sales_room ON intelligent_sales_room_activity_log(tenant_id, sales_room_id);

COMMENT ON TABLE intelligent_sales_room_activity_log IS '{"audited": true, "search": { "enabled" : false } }';

CREATE TABLE IF NOT EXISTS intelligent_sales_room_share_link_access
(
    id             UUID          NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id      VARCHAR(36)   NOT NULL REFERENCES tenant (tenant_id),
    sales_room_id  UUID          NOT NULL REFERENCES intelligent_sales_room (id),
    user_name      VARCHAR(1024) NOT NULL,
    user_email     VARCHAR(1024) NOT NULL,
    first_access   TIMESTAMP     NOT NULL DEFAULT NOW(),
    last_access    TIMESTAMP     NOT NULL DEFAULT NOW(),
    ip_address     VARCHAR(100),
    user_agent     TEXT,
    is_active      BOOLEAN       NOT NULL DEFAULT TRUE,
    is_deleted     BOOLEAN       NOT NULL DEFAULT FALSE,
    created_on     TIMESTAMP,
    updated_on     TIMESTAMP,
    UNIQUE(tenant_id, sales_room_id, user_name, user_email)
);

CREATE INDEX IF NOT EXISTS index_isr_share_link_access_tenant_sales_room ON intelligent_sales_room_share_link_access(tenant_id, sales_room_id);

COMMENT ON TABLE intelligent_sales_room_share_link_access IS '{"audited": true, "search": { "enabled" : false } }';

CREATE TABLE IF NOT EXISTS intelligent_sales_room_ai_generated_content
(
    id                  UUID         NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id           VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    sales_room_id       UUID         NOT NULL REFERENCES intelligent_sales_room (id),
    widget_id           UUID         NOT NULL,
    generated_content   TEXT         NOT NULL,
    order_form_context  TEXT,
    is_edited           BOOLEAN      NOT NULL DEFAULT FALSE,
    generated_on        TIMESTAMP    NOT NULL DEFAULT NOW(),
    last_edited_on      TIMESTAMP,
    is_deleted          BOOLEAN      NOT NULL DEFAULT FALSE,
    created_on          TIMESTAMP,
    updated_on          TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_intelligent_sales_room_ai_generated_content_tenant_widget ON intelligent_sales_room_ai_generated_content(tenant_id, sales_room_id, widget_id);

COMMENT ON TABLE intelligent_sales_room_ai_generated_content IS '{"audited": true, "search": { "enabled" : false } }';
