DO
$$
    BEGIN
        IF EXISTS (SELECT 1
                     FROM pg_tables
                    WHERE tablename = 'electronic_signature')
        THEN
            DROP INDEX IF EXISTS index_electronic_signature_tenant_id_order_id;
            CREATE UNIQUE INDEX IF NOT EXISTS index_electronic_signature_tenant_id_order_id ON electronic_signature (tenant_id, order_id) WHERE status NOT IN ('VOIDED', 'FAILED', 'DECLINED', 'COMPLETED');
        END IF;
    END;
$$;