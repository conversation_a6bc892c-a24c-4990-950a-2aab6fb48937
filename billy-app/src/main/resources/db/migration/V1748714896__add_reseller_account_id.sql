DO $$
    BEGIN
        -- backfill will be done separately
        ALTER TABLE IF EXISTS account_order ADD COLUMN IF NOT EXISTS reseller_account_id VARCHAR(36) REFERENCES account (account_id);
        ALTER TABLE IF EXISTS subscription ADD COLUMN IF NOT EXISTS reseller_account_id VARCHAR(36) REFERENCES account (account_id);
        ALTER TABLE IF EXISTS invoice ADD COLUMN IF NOT EXISTS reseller_account_id VARCHAR(36) REFERENCES account (account_id);
    END
$$ ;
