CREATE TABLE IF NOT EXISTS document_link
(
    id                    UUID            NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    link_id               VARCHAR(50)     NOT NULL UNIQUE,
    tenant_id             VARCHAR(36)     NOT NULL REFERENCES tenant (tenant_id),
    order_id              VARCHAR(36)     NOT NULL REFERENCES account_order (order_id),
    is_deleted            BOOLEAN         NOT NULL DEFAULT FALSE,
    created_on            TIMESTAMP,
    updated_on            TIMESTAMP
);

CREATE INDEX IF NOT EXISTS document_link_link_id_index ON document_link(link_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS document_link_tenant_id_order_id_index ON document_link(tenant_id, order_id);

COMMENT ON TABLE document_link IS '{"audited":true}';
