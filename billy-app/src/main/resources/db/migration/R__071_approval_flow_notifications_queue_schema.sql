CREATE TABLE IF NOT EXISTS approval_flow_notifications_queue
(
    id                              UUID            NOT NULL      PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id                       VARCHAR(36)     NOT NULL      REFERENCES tenant (tenant_id),
    order_id                        VARCHAR(36)     NOT NULL      REFERENCES account_order (order_id),
    approval_flow_instance_group_id VARCHAR(36)     NOT NULL,
    approval_flow_instance_id       VARCHAR(36)     NOT NULL,
    approval_flow_id                VARCHAR(36)     NOT NULL,
    approval_state_id               VARCHAR(36)     NOT NULL,
    notification_type               VARCHAR(20)     NOT NULL,
    created_on                      TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_approval_flow_notifications_queue_notification_type ON approval_flow_notifications_queue (notification_type);

COMMENT ON TABLE approval_flow_notifications_queue IS '{"audited":true}';