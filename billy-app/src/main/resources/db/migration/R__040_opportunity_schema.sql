CREATE TABLE IF NOT EXISTS opportunity
(
    id               UUID         NOT NULL PRIMARY KEY
                                           DEFAULT uuid_generate_v4(),
    tenant_id        VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    entity_id        VARCHAR(36)  NOT NULL,
    crm_id           VARCHAR(255) NULL,
    opportunity_id   VARCHAR(255) NULL,
    name             VARCHAR(255) NULL,
    type             VARCHAR(255) NULL,
    stage            VARCHAR(255) NULL,
    primary_order_id VARCHAR(100),
    is_closed        BOOLEAN      NOT NULL DEFAULT FALSE,
    is_deleted       BOOLEAN      NOT NULL DEFAULT FALSE,
    crm_type         VARCHAR(100) NOT NULL DEFAULT 'SALESFORCE',
    account_id       VARCHAR(36) REFERENCES account (account_id),
    created_on       TIMESTAMP,
    updated_on       TIMESTAMP,
    CONSTRAINT opportunity_entity_id_fkey FOREIGN KEY(tenant_id, entity_id) REFERENCES entity(tenant_id, entity_id)
);

CREATE UNIQUE INDEX IF NOT EXISTS index_opportunity_id ON opportunity (tenant_id, crm_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS index_opportunity_name ON opportunity (tenant_id, name) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS index_opportunity_type ON opportunity (tenant_id, type) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS index_opportunity_stage ON opportunity (tenant_id, stage) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS index_opportunity_opportunity_id ON opportunity (tenant_id, opportunity_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS index_opportunity_account_id ON opportunity (tenant_id, account_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS index_opportunity_tenant_id ON opportunity (tenant_id);

COMMENT ON TABLE opportunity IS '{"audited": true, "search": { "enabled" : true }, "rls" : { "tenant": true, "entity": "entity_id" }}';
