CREATE TABLE IF NOT EXISTS queued_task
(
    id              UUID         NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id         VARCHAR(36)  NOT NULL,
    task_module     VARCHAR(100) NOT NULL,
    task_type       VARCHAR(100) NOT NULL,
    task_data       TEXT         NOT NULL,
    task_metadata   JSONB        NOT NULL,
    tenant_id       VARCHAR(36)  NOT NULL,
    entity_id       VARCHAR(36),
    entity_ids      TEXT         ARRAY,
    partition_key   VARCHAR(256) NOT NULL,
    sequence_number BIGINT       NOT NULL,
    parent_type     VARCHAR(64),
    parent_id       VARCHAR(36),
    task_status     VARCHAR(36)  NOT NULL,
    failure_reason  TEXT,
    created_on      TIMESTAMP,
    last_started_on TIMESTAMP,
    completed_on    TIMESTAMP,
    delayed_until   TIMESTAMP,
    updated_on      TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS index_queued_task_task_id on queued_task (tenant_id, task_id);
CREATE INDEX IF NOT EXISTS index_queued_task_partition_key_status on queued_task (tenant_id, partition_key, task_status);
CREATE INDEX IF NOT EXISTS index_queued_task_id on queued_task (task_id);
CREATE INDEX IF NOT EXISTS index_queued_task_sequence_number on queued_task (sequence_number);
CREATE INDEX IF NOT EXISTS index_queued_task_task_status on queued_task (task_status, last_started_on);
CREATE INDEX IF NOT EXISTS index_queued_task_delayed_until on queued_task (delayed_until);

CREATE TABLE IF NOT EXISTS queued_task_archive (LIKE queued_task INCLUDING INDEXES INCLUDING CONSTRAINTS);

CREATE TABLE IF NOT EXISTS queued_task_execution
(
    id                 UUID        NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id          VARCHAR(36) NOT NULL,
    task_execution_id  VARCHAR(36) NOT NULL,
    task_id            VARCHAR(36) NOT NULL,
    timeout_in_seconds SMALLINT    NOT NULL,
    status             VARCHAR(36) NOT NULL,
    worker_name        VARCHAR(36) NOT NULL,
    failure_reason     TEXT,
    created_on         TIMESTAMP,
    updated_on         TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS index_queued_task_execution_task_id on queued_task_execution (tenant_id, task_execution_id);
CREATE INDEX IF NOT EXISTS index_queued_task_execution_queued_task_id on queued_task_execution (task_id);
CREATE INDEX IF NOT EXISTS index_queued_task_execution_status on queued_task_execution (status);

CREATE TABLE IF NOT EXISTS queued_task_execution_archive (LIKE queued_task_execution INCLUDING INDEXES INCLUDING CONSTRAINTS);

CREATE TABLE IF NOT EXISTS queued_task_worker_capacity
(
    worker_name        VARCHAR(36) NOT NULL PRIMARY KEY,
    capacity           INT    NOT NULL,
    last_heartbeat     TIMESTAMP   NOT NULL
);

CREATE OR REPLACE VIEW queued_tasks_ready_to_run AS
WITH running_tasks AS (SELECT qt.task_id, qt.tenant_id, qt.partition_key
                       FROM queued_task qt
                       WHERE qt.task_status = 'IN_PROGRESS')
SELECT
    queued_task.id,
    queued_task.task_id,
    queued_task.tenant_id,
    queued_task.partition_key,
    queued_task.sequence_number,
    queued_task.task_status,
    queued_task.delayed_until
FROM queued_task
         LEFT OUTER JOIN running_tasks ON (queued_task.tenant_id = running_tasks.tenant_id AND
                                           queued_task.partition_key = running_tasks.partition_key)
WHERE running_tasks.task_id IS NULL AND
       (queued_task.delayed_until < timezone('utc', now()) OR queued_task.delayed_until IS NULL) AND
       (queued_task.task_status = 'WAITING' OR queued_task.task_status = 'DELAYED');

CREATE OR REPLACE VIEW queued_tasks_first_within_partition AS
WITH ordered_ready_tasks AS (
    SELECT
        ready_tasks.id,
        ready_tasks.task_id,
        ready_tasks.tenant_id,
        ready_tasks.partition_key,
        ready_tasks.sequence_number,
        ready_tasks.task_status,
        ready_tasks.delayed_until,
        row_number() OVER (PARTITION BY ready_tasks.tenant_id, ready_tasks.partition_key ORDER BY ready_tasks.sequence_number)
            AS partition_key_row_number
    FROM queued_tasks_ready_to_run ready_tasks
)
SELECT * FROM ordered_ready_tasks
WHERE partition_key_row_number <= 1;

CREATE OR REPLACE VIEW queued_tasks_limited_by_tenant AS
WITH tenant_ordered_tasks AS (
    SELECT
        tasks.id,
        tasks.task_id,
        tasks.tenant_id,
        tasks.partition_key,
        tasks.sequence_number,
        tasks.task_status,
        tasks.delayed_until,
        row_number() OVER (PARTITION BY tasks.tenant_id ORDER BY tasks.sequence_number) AS by_tenant_row_number
    FROM queued_tasks_first_within_partition tasks
)
SELECT * FROM tenant_ordered_tasks
WHERE by_tenant_row_number <= 10;
