CREATE TABLE IF NOT EXISTS tenant
(
    id                 UUID         NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id          VARCHAR(36)  NOT NULL UNIQUE,
    name               VA<PERSON>HA<PERSON>(255) NOT NULL,
    email              VARCHAR(255),
    phone_number       VA<PERSON>HAR(255),
    address            JSO<PERSON>,
    encrypted_password VARCHAR(255) NOT NULL,
    logo_content       BYTEA,
    is_test            BOOLEAN      NOT NULL             DEFAULT FALSE,
    is_deleted         BOOLEAN      NOT NULL             DEFAULT FALSE,
    created_on         TIMESTAMP,
    updated_on         TIMESTAMP
);

COMMENT ON TABLE tenant IS '{"audited": true}';

CREATE TABLE IF NOT EXISTS entity
(
    id                          UUID            NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id                   VARCHAR(36)     NOT NULL REFERENCES tenant (tenant_id),
    entity_id                   VARCHAR(36)     NOT NULL,
    name                        <PERSON><PERSON><PERSON><PERSON>(100)    NOT NULL,
    display_id                  VARCHAR(100)    NOT NULL,
    description                 VARCHAR(1000),
    proration_scheme            VARCHAR(100)    NOT NULL,
    proration_mode              VARCHAR(100)    NOT NULL,
    invoice_config_id           VARCHAR(36)     NOT NULL,
    timezone                    VARCHAR(100),
    functional_currency         VARCHAR(10),
    erp_id                      VARCHAR(36),
    wire_instruction            TEXT,
    company_contact             JSON,
    account_receivable_contact  JSON,
    logo_content                BYTEA,
    is_default                  BOOLEAN         NOT NULL DEFAULT FALSE,
    encrypted_password          VARCHAR(255),
    is_deleted                  BOOLEAN         NOT NULL DEFAULT FALSE,
    created_on                  TIMESTAMP,
    updated_on                  TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS index_entity_id ON entity (tenant_id, entity_id);
CREATE UNIQUE INDEX IF NOT EXISTS index_entity_name ON entity (tenant_id, name) WHERE is_deleted = FALSE;

COMMENT ON TABLE entity IS '{"audited":true}';

CREATE TABLE IF NOT EXISTS tenant_user
(
    id                UUID         NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id           VARCHAR(255) NOT NULL,
    tenant_id         VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    display_name      VARCHAR(255) NOT NULL,
    title             VARCHAR(255),
    email             VARCHAR(255) NOT NULL,
    normalized_email  VARCHAR(255) NOT NULL,
    phone_number      VARCHAR(255),
    role              VARCHAR(255) NOT NULL,
    state             VARCHAR(255),
    sso_only          BOOLEAN      NOT NULL             DEFAULT FALSE,
    entity_ids        VARCHAR(36)  ARRAY,
    external_id       VARCHAR(255),
    terms_accepted_on TIMESTAMP,
    created_on        TIMESTAMP,
    updated_on        TIMESTAMP,
    UNIQUE (tenant_id, user_id)
);

CREATE INDEX IF NOT EXISTS index_tenant_user_tenant_id ON tenant_user (tenant_id);
CREATE INDEX IF NOT EXISTS index_tenant_user_id ON tenant_user (user_id);
CREATE INDEX IF NOT EXISTS index_tenant_user_created_on_id ON tenant_user (tenant_id, created_on, id);
CREATE INDEX IF NOT EXISTS index_tenant_user_email ON tenant_user (tenant_id, email);
CREATE INDEX IF NOT EXISTS index_tenant_user_normalized_email ON tenant_user (tenant_id, normalized_email);
CREATE INDEX IF NOT EXISTS index_tenant_user_email_only ON tenant_user (email);

CREATE UNIQUE INDEX IF NOT EXISTS index_tenant_user_tenant_id_user_id ON tenant_user (tenant_id, user_id);
CREATE UNIQUE INDEX IF NOT EXISTS index_tenant_user_external_id ON tenant_user (tenant_id, external_id);

COMMENT ON TABLE tenant_user IS '{"audited": true, "search": { "enabled" : true }, "rls" : { "tenant": true, "entity": "entity_ids" } }';

CREATE TABLE IF NOT EXISTS tax_rate
(
    id             UUID                  DEFAULT uuid_generate_v4(),
    tenant_id      VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    name           VARCHAR(100) NOT NULL,
    description    TEXT,
    tax_percentage NUMERIC (22, 10),
    tax_code       VARCHAR(20),
    tax_inclusive  BOOLEAN,
    status         VARCHAR(20),
    is_deleted     BOOLEAN      NOT NULL DEFAULT FALSE,
    created_on     TIMESTAMP,
    updated_on     TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS tax_status_id ON tax_rate (tenant_id, status);
CREATE INDEX IF NOT EXISTS index_tax_rate_created_on_id ON tax_rate (tenant_id, created_on, id);

COMMENT ON TABLE tax_rate IS '{"audited": true, "search": { "enabled" : true } }';

CREATE TABLE IF NOT EXISTS tax_rate_strategy
(
    id                   UUID                  DEFAULT uuid_generate_v4(),
    tenant_id            VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    name                 VARCHAR(100) NOT NULL,
    tax_rate_strategy_id VARCHAR(36)  NOT NULL,
    country_code         VARCHAR(2)   NOT NULL,
    tax_rate_id          UUID         NOT NULL REFERENCES tax_rate (id),
    is_deleted           BOOLEAN      NOT NULL DEFAULT FALSE,
    created_on           TIMESTAMP,
    updated_on           TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE UNIQUE INDEX IF NOT EXISTS index_tax_rate_strategy_tenant_id_tax_rate_strategy_id ON tax_rate_strategy (tenant_id, tax_rate_strategy_id) WHERE is_deleted = FALSE;
CREATE UNIQUE INDEX IF NOT EXISTS index_tax_rate_strategy_tenant_id_country_code_tax_rate_id ON tax_rate_strategy (tenant_id, country_code, tax_rate_id) WHERE is_deleted = FALSE;

COMMENT ON TABLE tax_rate_strategy IS '{"audited": true, "search": { "enabled" : false } }';

CREATE TABLE IF NOT EXISTS unit_of_measure
(
    id          UUID                 DEFAULT uuid_generate_v4(),
    tenant_id   VARCHAR(36) NOT NULL REFERENCES tenant (tenant_id),
    name        VARCHAR(30) NOT NULL,
    description TEXT,
    status      VARCHAR(20) NOT NULL,
    is_deleted  BOOLEAN     NOT NULL DEFAULT FALSE,
    created_on  TIMESTAMP,
    updated_on  TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE (tenant_id, name)
);

CREATE INDEX IF NOT EXISTS name_index ON unit_of_measure (tenant_id, name, is_deleted);
CREATE INDEX IF NOT EXISTS status_index ON unit_of_measure (tenant_id, status);
CREATE INDEX IF NOT EXISTS index_unit_of_measure_created_on_id ON unit_of_measure (tenant_id, created_on, id);

COMMENT ON TABLE unit_of_measure IS '{"audited": true, "search": { "enabled" : true } }';

CREATE TABLE IF NOT EXISTS product_category
(
    id                  UUID                  DEFAULT uuid_generate_v4(),
    product_category_id VARCHAR(36)  NOT NULL,
    tenant_id           VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    entity_ids          TEXT         ARRAY,
    name                VARCHAR(100) NOT NULL,
    description         TEXT,
    is_deleted          BOOLEAN      NOT NULL DEFAULT FALSE,
    created_on          TIMESTAMP,
    updated_on          TIMESTAMP,
    PRIMARY KEY (id)
);
CREATE UNIQUE INDEX IF NOT EXISTS index_product_category_tenant_id_product_category_id ON product_category (tenant_id, product_category_id);
CREATE INDEX IF NOT EXISTS index_product_category_is_deleted ON product_category (tenant_id, is_deleted);
CREATE INDEX IF NOT EXISTS index_product_category_created_on_id ON product_category (tenant_id, created_on, id);
CREATE UNIQUE INDEX IF NOT EXISTS index_product_category_tenant_id_name ON product_category (tenant_id, name) WHERE is_deleted = FALSE;

COMMENT ON TABLE product_category IS '{"audited": true, "search": { "enabled" : true, "use_insert_specific_trigger" : true, "update_with_values" : true }, "rls" : { "tenant": true, "entity": "entity_ids" }}';

CREATE TABLE IF NOT EXISTS product
(
    id                  UUID                  DEFAULT uuid_generate_v4(),
    product_id          VARCHAR(36)  NOT NULL,
    tenant_id           VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    entity_ids          TEXT         ARRAY,
    name                VARCHAR(100) NOT NULL,
    display_name        VARCHAR(100),
    description         TEXT,
    sku                 VARCHAR(100),
    external_id         VARCHAR(100),
    product_category_id VARCHAR(36),
    is_deleted          BOOLEAN      NOT NULL DEFAULT FALSE,
    created_on          TIMESTAMP,
    updated_on          TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE (tenant_id, product_id)
);

CREATE INDEX IF NOT EXISTS index_product_is_deleted ON product (tenant_id, is_deleted);
CREATE INDEX IF NOT EXISTS index_product_created_on_id ON product (tenant_id, created_on, id);
CREATE INDEX IF NOT EXISTS index_product_product_category_id ON product (tenant_id, product_category_id, is_deleted);
CREATE UNIQUE INDEX IF NOT EXISTS index_product_external_id ON product (tenant_id, external_id) WHERE is_deleted = FALSE;

COMMENT ON TABLE product IS '{"audited": true, "search": { "enabled" : true }, "rls" : { "tenant": true, "entity": "entity_ids" } }';

CREATE TABLE IF NOT EXISTS plan
(
    id                   UUID                  DEFAULT uuid_generate_v4(),
    plan_id              VARCHAR(36)  NOT NULL,
    external_id          VARCHAR(100),
    tenant_id            VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    entity_ids           TEXT         ARRAY,
    status               VARCHAR(20)  NOT NULL,
    name                 VARCHAR(100) NOT NULL,
    display_name         VARCHAR(100),
    description          TEXT,
    product_id           VARCHAR(36)  NOT NULL,
    is_deleted           BOOLEAN      NOT NULL DEFAULT FALSE,
    supported_currencies JSON         NOT NULL DEFAULT '["USD"]'::JSON,
    entity_id            VARCHAR(36),
    created_on           TIMESTAMP,
    updated_on           TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (tenant_id, product_id) REFERENCES product (tenant_id, product_id),
    UNIQUE (tenant_id, plan_id)
);

CREATE INDEX IF NOT EXISTS index_plan_product_id ON plan (tenant_id, product_id);
CREATE INDEX IF NOT EXISTS index_plan_created_on_id ON plan (tenant_id, created_on, id);
CREATE INDEX IF NOT EXISTS index_plan_is_deleted ON plan (tenant_id, is_deleted);
CREATE INDEX IF NOT EXISTS index_plan_status ON plan (tenant_id, status);
CREATE UNIQUE INDEX IF NOT EXISTS index_plan_external_id ON plan (tenant_id, external_id) WHERE is_deleted = FALSE;

COMMENT ON TABLE plan IS '{"audited": true, "search": { "enabled" : true, "use_insert_specific_trigger" : true, "update_with_values" : true }, "rls" : { "tenant": true, "entity": "entity_ids" }}';

CREATE TABLE IF NOT EXISTS charge
(
    id                              UUID                  DEFAULT uuid_generate_v4(),
    charge_id                       VARCHAR(36),
    external_id                     VARCHAR(100),
    tenant_id                       VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    entity_ids                      TEXT         ARRAY,
    subskribe_plan_id               VARCHAR(36)  NOT NULL,
    name                            VARCHAR(100) NOT NULL,
    display_name                    VARCHAR(100),
    description                     TEXT,
    amount                          NUMERIC(22, 10),
    percent                         NUMERIC(14, 10),
    percent_derived_from            VARCHAR(20),
    charge_type                     VARCHAR(20)  NOT NULL,
    charge_model                    VARCHAR(20)  NOT NULL,
    cycle                           VARCHAR(20),
    step                            INTEGER,
    duration_in_months              BIGINT,
    plan_id                         UUID         NOT NULL REFERENCES plan (id) ON DELETE CASCADE,
    tax_rate_id                     UUID REFERENCES tax_rate (id),
    unit_of_measure_id              UUID REFERENCES unit_of_measure (id),
    recognition_rule_id             VARCHAR(36), -- TODO: add reference to recognition_rule + trigger to set null on delete
    price_tiers                     JSON,
    min_quantity                    BIGINT,
    max_quantity                    BIGINT,
    min_amount                      NUMERIC(22, 10),
    max_amount                      NUMERIC(22, 10),
    default_quantity                BIGINT,
    is_drawdown                     BOOLEAN      NOT NULL DEFAULT FALSE,
    is_custom                       BOOLEAN      NOT NULL DEFAULT FALSE,
    is_list_price_editable          BOOLEAN      NOT NULL DEFAULT FALSE,
    is_renewable                    BOOLEAN      NOT NULL DEFAULT TRUE,
    -- setting is_creditable to true means charge is eligible for manually input refund amount
    -- our customers want to credit their customers in case line item is debooked
    -- During cancellation/amendment, debooking of line item means ActionType is REMOVE
    is_creditable                   BOOLEAN      NOT NULL DEFAULT FALSE,
    is_event_based                  BOOLEAN      NOT NULL DEFAULT FALSE,
    is_discount                     BOOLEAN      NOT NULL DEFAULT FALSE,
    overage_base_charge_id          VARCHAR(36),
    minimum_commit_base_charge_id   VARCHAR(36),
    -- if the charge model is RATE_CARD_LOOKUP, there has to be a rate_card_id
    rate_card_id                    VARCHAR(36),
    erp_id                          VARCHAR(100),
    item_code                       VARCHAR(36),
    billing_term                    VARCHAR(20),
    billing_cycle                   VARCHAR(100) NOT NULL DEFAULT 'DEFAULT',
    should_track_arr                BOOLEAN,
    is_deleted                      BOOLEAN      NOT NULL DEFAULT FALSE,
    created_on                      TIMESTAMP,
    updated_on                      TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS index_charge_plan_id ON charge (tenant_id, plan_id);
CREATE INDEX IF NOT EXISTS index_charge_tax_rate_id ON charge (tenant_id, tax_rate_id);
CREATE INDEX IF NOT EXISTS index_charge_created_on_id ON charge (tenant_id, created_on, id);
CREATE UNIQUE INDEX IF NOT EXISTS index_charge_tenant_id ON charge (tenant_id, charge_id);
CREATE UNIQUE INDEX IF NOT EXISTS index_charge_external_id ON charge (tenant_id, external_id) WHERE is_deleted = FALSE;

COMMENT ON TABLE charge IS '{"audited": true, "search": { "enabled" : true }, "rls": { "tenant": true, "entity": "entity_ids" }}';

CREATE TABLE IF NOT EXISTS account_address
(
    id                   UUID               NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    address_id           VARCHAR(36) UNIQUE NOT NULL,
    tenant_id            VARCHAR(36)        NOT NULL REFERENCES tenant (tenant_id),
    street_address_line1 VARCHAR(255),
    street_address_line2 VARCHAR(255),
    city                 VARCHAR(255),
    state                VARCHAR(255),
    country              VARCHAR(255),
    zipcode              VARCHAR(10),
    is_deleted           BOOLEAN            NOT NULL             DEFAULT FALSE,
    created_on           TIMESTAMP,
    updated_on           TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_account_address_is_deleted ON account_address (tenant_id, is_deleted);

COMMENT ON TABLE account_address IS '{"audited": true, "search": { "enabled" : true } }';

CREATE TABLE IF NOT EXISTS account
(
    id                            UUID               NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id                    VARCHAR(36) UNIQUE NOT NULL,
    external_id                   VARCHAR(36),
    tenant_id                     VARCHAR(36)        NOT NULL REFERENCES tenant (tenant_id),
    entity_ids                    VARCHAR(36) ARRAY  NOT NULL,
    name                          VARCHAR(255)       NOT NULL,
    legal_name                    VARCHAR(255),
    description                   TEXT,
    primary_contact_id            VARCHAR(36),
    phone_number                  VARCHAR(255),
    address_id                    VARCHAR(36) REFERENCES account_address (address_id),
    payment_method_id             UUID,
    preferred_payment_type        VARCHAR(50),
    timezone                      VARCHAR(32),
    crm_id                        VARCHAR(100)       NULL,
    crm_type                      VARCHAR(100),
    erp_id                        VARCHAR(100),
    currency                      VARCHAR(3)         NOT NULL             DEFAULT 'USD',
    tax_exemption_use_code        VARCHAR(12)        NULL,
    is_reseller                   BOOLEAN            NOT NULL             DEFAULT FALSE,
    exclude_from_batch_operations BOOLEAN            NOT NULL             DEFAULT FALSE,
    exclude_from_dunning          BOOLEAN            NOT NULL             DEFAULT FALSE,
    supported_payment_types       JSON,
    is_deleted                    BOOLEAN            NOT NULL             DEFAULT FALSE,
    created_on                    TIMESTAMP,
    updated_on                    TIMESTAMP,
    metrics                       JSONB              NOT NULL             DEFAULT '{"recompute": true}'::jsonb
);

CREATE UNIQUE INDEX IF NOT EXISTS index_account_external_id ON account (tenant_id, external_id) WHERE is_deleted = FALSE;
CREATE UNIQUE INDEX IF NOT EXISTS index_account_name ON account (tenant_id, name) WHERE is_deleted = FALSE;
CREATE UNIQUE INDEX IF NOT EXISTS index_tenant_account_crm_id on account (tenant_id, crm_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS index_account_crm_id ON account (tenant_id, crm_id);
CREATE INDEX IF NOT EXISTS index_account_created_on_id ON account (tenant_id, created_on, id);
CREATE INDEX IF NOT EXISTS index_account_is_reseller ON account(tenant_id, is_deleted, is_reseller);
CREATE INDEX IF NOT EXISTS index_account_metrics_recompute ON account (tenant_id) INCLUDE (account_id) WHERE (metrics->>'recompute')::boolean = true AND is_deleted = false;
CREATE INDEX IF NOT EXISTS index_account_exclude_from_batch_operations ON account(tenant_id, is_deleted, exclude_from_batch_operations);
CREATE INDEX IF NOT EXISTS index_account_exclude_from_dunning ON account(tenant_id, is_deleted, exclude_from_dunning) WHERE is_deleted = FALSE;

COMMENT ON TABLE account IS '{"audited": true, "search": { "enabled" : true, "use_insert_specific_trigger" : true, "update_with_values": true, "update_with_condition": true }, "rls" : { "tenant": true, "entity": "entity_ids" }}';

CREATE TABLE IF NOT EXISTS account_contact
(
    id             UUID               NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    contact_id     VARCHAR(36) UNIQUE NOT NULL,
    account_id     VARCHAR(36) REFERENCES account (account_id),
    tenant_id      VARCHAR(36)        NOT NULL REFERENCES tenant (tenant_id),
    external_id    VARCHAR(36),
    erp_id         VARCHAR(100),
    first_name     VARCHAR(255),
    last_name      VARCHAR(255),
    email          VARCHAR(255)       NOT NULL,
    email_verified BOOLEAN,
    phone_number   VARCHAR(255),
    title          VARCHAR(255),
    state          VARCHAR(255),
    address_id     VARCHAR(36) REFERENCES account_address (address_id),
    is_deleted     BOOLEAN            NOT NULL             DEFAULT FALSE,
    created_on     TIMESTAMP,
    updated_on     TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS index_account_contact_external_id ON account_contact (tenant_id, account_id, external_id) WHERE is_deleted = FALSE;
CREATE UNIQUE INDEX IF NOT EXISTS index_account_contact_email ON account_contact (tenant_id, account_id, email) WHERE is_deleted = FALSE;
CREATE UNIQUE INDEX IF NOT EXISTS index_account_contact_contact_id ON account_contact (tenant_id, contact_id);
CREATE INDEX IF NOT EXISTS index_account_contact_account_id ON account_contact (tenant_id, account_id);
CREATE INDEX IF NOT EXISTS index_account_contact_is_deleted ON account_contact (tenant_id, is_deleted);

COMMENT ON TABLE account_contact IS '{"audited": true, "search": { "enabled" : true, "use_insert_specific_trigger" : true, "update_with_values" : true }}';

CREATE TABLE IF NOT EXISTS account_payment_method
(
    id                          UUID            NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    name                        VARCHAR(255)    NOT NULL,
    account_id                  VARCHAR(36)     REFERENCES account (account_id),
    external_payment_account_id VARCHAR(255),
    tenant_id                   VARCHAR(36)     NOT NULL REFERENCES tenant (tenant_id),
    payment_type                VARCHAR(36),
    payment_method_id           VARCHAR(255),   -- this is the id of payment_provider_payment_method table
    status                      VARCHAR(50)     NOT NULL DEFAULT 'ACTIVE',
    created_on                  TIMESTAMP,
    updated_on                  TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_account_payment_method_account_id ON account_payment_method (tenant_id, account_id);
CREATE INDEX IF NOT EXISTS index_account_payment_method_external_payment_account_id ON account_payment_method (tenant_id, external_payment_account_id);
CREATE UNIQUE INDEX IF NOT EXISTS index_account_payment_method_account_id_name ON account_payment_method (tenant_id, account_id, name) WHERE status != 'DISABLED';
CREATE INDEX IF NOT EXISTS index_account_payment_method_account_id_payment_type ON account_payment_method (tenant_id, account_id, payment_type);

COMMENT ON TABLE account_payment_method IS '{"audited": true, "search": { "enabled" : true } }';

CREATE TABLE IF NOT EXISTS document_master_template
(
    id              UUID            NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id       VARCHAR(36)     NOT NULL REFERENCES tenant (tenant_id),
    entity_ids      TEXT            ARRAY,
    type            VARCHAR(30)     NOT NULL,
    name            VARCHAR(250)    NOT NULL,
    content         TEXT,
    is_full_html    BOOLEAN         NOT NULL DEFAULT FALSE,
    is_default      BOOLEAN         NOT NULL DEFAULT FALSE,
    configuration   JSON            NOT NULL DEFAULT '{}'::JSON,
    status          VARCHAR(20)     NOT NULL DEFAULT 'DRAFT',
    is_deleted      BOOLEAN         NOT NULL DEFAULT FALSE,
    created_on      TIMESTAMP,
    updated_on      TIMESTAMP
);

COMMENT ON TABLE document_master_template IS '{"audited":true, "rls" : { "tenant": true, "entity": "entity_ids" }}';

-- currency exchange rates for transactional currency to functional currency
CREATE TABLE IF NOT EXISTS transactional_exchange_rate
(
    id                                 UUID            NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id                          VARCHAR(36)     NOT NULL REFERENCES tenant (tenant_id),
    transactional_exchange_rate_id     VARCHAR(36)     NOT NULL,
    from_currency                      VARCHAR(3)      NOT NULL,
    to_currency                        VARCHAR(3)      NOT NULL,
    effective_date                     TIMESTAMP       NOT NULL,
    exchange_rate                      NUMERIC(16,10)  NOT NULL,
    exchange_source                    VARCHAR(255),
    updated_by                         VARCHAR(255),
    is_overridden                      BOOLEAN         NOT NULL DEFAULT FALSE,
    created_on                         TIMESTAMP,
    updated_on                         TIMESTAMP
);

COMMENT ON TABLE transactional_exchange_rate IS '{"audited": true, "search": { "enabled" : true } }';

CREATE UNIQUE INDEX IF NOT EXISTS index_transactional_exchange_rate_id ON transactional_exchange_rate (tenant_id, transactional_exchange_rate_id);
-- the unique index on (tenant_id, from_currency, to_currency, effective_date) is to make sure that
-- there is only one fx rate for a given currency pair at a given effective date which is not overridden
-- there can be many overridden conversion rates for a given currency pair with same effective date
CREATE UNIQUE INDEX IF NOT EXISTS index_tx_exchange_rate_unique_from_to_date on transactional_exchange_rate (tenant_id, from_currency, to_currency, effective_date) where is_overridden = FALSE;
-- the index on (tenant_id, from_currency, to_currency) is to support below two frequent queries:
-- to get fx rate for a given currency pair and "as of date" -> get the max(effective_date) <= "as of date" and is_overridden = false
-- to list all the fx rates for a given currency pair (both overridden and not overridden)
CREATE INDEX IF NOT EXISTS index_tx_exchange_rate_from_to_date_sorting on transactional_exchange_rate (tenant_id, from_currency, to_currency, effective_date DESC);

CREATE TABLE IF NOT EXISTS account_order
(
    id                                      UUID               NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    name                                    TEXT,
    order_id                                VARCHAR(36) UNIQUE NOT NULL,
    external_id                             VARCHAR(36),
    tenant_id                               VARCHAR(36)        NOT NULL REFERENCES tenant (tenant_id),
    entity_id                               VARCHAR(36)        NOT NULL,
    account_id                              VARCHAR(36) REFERENCES account (account_id),
    reseller_account_id                     VARCHAR(36) REFERENCES account (account_id),
    order_type                              VARCHAR(12)        NOT NULL,
    currency                                VARCHAR(3)         NOT NULL,
    payment_term                            VARCHAR(6)         NOT NULL,
    subscription_id                         UUID,
    external_subscription_id                VARCHAR(36),
    subscription_target_version             INTEGER,
    shipping_contact_id                     VARCHAR(36) REFERENCES account_contact (contact_id),
    billing_contact_id                      VARCHAR(36) REFERENCES account_contact (contact_id),
    start_date                              TIMESTAMP          NOT NULL,
    end_date                                TIMESTAMP          NOT NULL,
    billing_anchor_date                     TIMESTAMP          NOT NULL,
    term_length_cycle                       VARCHAR(20),
    term_length_step                        INTEGER,
    billing_cycle                           VARCHAR(20)        NOT NULL,
    billing_step                            INTEGER            NOT NULL,
    billing_term                            VARCHAR(20)        NOT NULL,
    total_list_amount                       NUMERIC(22, 10)    NOT NULL             DEFAULT 0.00,
    total_list_amount_before_override       NUMERIC(22, 10)    NOT NULL             DEFAULT 0.00,
    tax_estimate                            NUMERIC(22, 10),
    total_amount                            NUMERIC(22, 10)    NOT NULL,
    status                                  VARCHAR(12)        NOT NULL,
    executed_on                             TIMESTAMP,
    ramp_interval                           TEXT,
    predefined_discounts                    TEXT,
    renewal_for_subscription_id             VARCHAR(36),
    renewal_for_subscription_version        INTEGER,
    restructure_for_subscription_id         VARCHAR(36),
    restructure_for_subscription_version    INTEGER,
    sfdc_opportunity_id                     VARCHAR(255)       NULL,
    document_master_template_id             UUID,
    purchase_order_number                   VARCHAR(255),
    po_required_for_invoicing               BOOLEAN            NOT NULL             DEFAULT FALSE,
    is_deleted                              BOOLEAN            NOT NULL             DEFAULT FALSE,
    created_on                              TIMESTAMP          NOT NULL,
    updated_on                              TIMESTAMP          NOT NULL,
    created_by                              VARCHAR(1024)      NOT NULL,
    submitted_by                            VARCHAR(1024),
    owner_id                                VARCHAR(36),
    auto_renew                              BOOLEAN            NOT NULL             DEFAULT FALSE,
    metrics                                 JSONB              NOT NULL             DEFAULT '{"recompute": true}'::jsonb,
    approval_segment_id                     VARCHAR(36),
    attachment_id                           UUID,
    composite_order_id                      VARCHAR(36),
    should_regenerate_pdf                   BOOLEAN            NOT NULL             DEFAULT TRUE,
    expires_on                              TIMESTAMP,
    source                                  VARCHAR(50),
    start_date_type                         VARCHAR(20)        NOT NULL             DEFAULT 'FIXED',
    subscription_duration_model             VARCHAR(50),
    CONSTRAINT account_order_entity_id_fkey FOREIGN KEY(tenant_id, entity_id) REFERENCES entity(tenant_id, entity_id)
);


CREATE INDEX IF NOT EXISTS index_account_order_composite_order_id ON account_order (tenant_id, composite_order_id);
CREATE INDEX IF NOT EXISTS index_account_order_account_id ON account_order (tenant_id, account_id);
CREATE INDEX IF NOT EXISTS index_account_order_subscription_id ON account_order (tenant_id, subscription_id);
CREATE INDEX IF NOT EXISTS index_account_order_external_subscription_id ON account_order (tenant_id, external_subscription_id);
CREATE INDEX IF NOT EXISTS index_account_order_shipping_contact_id ON account_order (tenant_id, shipping_contact_id);
CREATE INDEX IF NOT EXISTS index_account_order_billing_contact_id ON account_order (tenant_id, billing_contact_id);
CREATE INDEX IF NOT EXISTS index_account_order_created_on_id ON account_order (tenant_id, created_on, id);
CREATE INDEX IF NOT EXISTS index_account_order_executed_on ON account_order (tenant_id, executed_on) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS index_account_order_master_template_id ON account_order (tenant_id, document_master_template_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS index_account_order_sfdc_opportunity_id ON account_order (tenant_id, sfdc_opportunity_id);
CREATE UNIQUE INDEX IF NOT EXISTS index_account_order_external_id ON account_order (tenant_id, external_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS index_account_order_metrics_recompute ON account_order (tenant_id) INCLUDE (order_id) WHERE (metrics->>'recompute')::boolean = true AND is_deleted = false AND status = 'EXECUTED';

COMMENT ON TABLE account_order IS '{"audited": true, "search": { "enabled" : true, "use_insert_specific_trigger" : true, "update_with_values": true, "update_with_condition": true }, "rls" : { "tenant": true, "entity": "entity_id" }}';

CREATE TABLE IF NOT EXISTS account_order_line_item
(
    id                                   UUID               NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_line_id                        VARCHAR(36) UNIQUE NOT NULL,
    tenant_id                            VARCHAR(36)        NOT NULL REFERENCES tenant (tenant_id),
    entity_id                            VARCHAR(36)        NOT NULL,
    order_id                             VARCHAR(36)        NOT NULL REFERENCES account_order (order_id),
    rank                                 INTEGER                                 DEFAULT 0,
    is_ramp                              BOOLEAN            NOT NULL             DEFAULT FALSE,
    action                               VARCHAR(50)        NOT NULL,
    plan_id                              VARCHAR(36)        NOT NULL,
    external_subscription_charge_id      VARCHAR(36),
    subscription_charge_group_id         VARCHAR(36),
    base_external_subscription_charge_id VARCHAR(36),
    subscription_charge_id               UUID,
    currency_conversion_rate_id          UUID,
    charge_id                            VARCHAR(36)        NOT NULL,
    ramp_group_id                        UUID,
    discounts                            TEXT,
    predefined_discounts                 TEXT,
    pricing_override                     JSONB,
    list_price_override_ratio            NUMERIC(13, 10),
    list_unit_price_before_override      NUMERIC(22, 10),
    list_amount_before_override          NUMERIC(22, 10),
    list_unit_price                      NUMERIC(22, 10),
    sell_unit_price                      NUMERIC(22, 10),
    discount_amount                      NUMERIC(22, 10),
    tax_estimate                         NUMERIC(22, 10),
    list_amount                          NUMERIC(22, 10)    NOT NULL,
    amount                               NUMERIC(22, 10)    NOT NULL,
    quantity                             BIGINT             NOT NULL,
    effective_date                       TIMESTAMP          NOT NULL,
    end_date                             TIMESTAMP          NOT NULL,
    created_on                           TIMESTAMP          NOT NULL,
    updated_on                           TIMESTAMP          NOT NULL,
    is_deleted                           BOOLEAN            NOT NULL             DEFAULT FALSE,
    metrics                              JSONB              NOT NULL             DEFAULT '{"recompute": true}'::jsonb,
    -- references to price attributes for MAP RECURRING use case
    attribute_references                 JSONB,
    arr_override                         NUMERIC(22, 10),
    replaces_plan_id                     VARCHAR(36),
    CONSTRAINT account_order_line_item_entity_id_fkey FOREIGN KEY(tenant_id, entity_id) REFERENCES entity(tenant_id, entity_id),
    FOREIGN KEY (tenant_id, charge_id) REFERENCES charge (tenant_id, charge_id)
);

CREATE INDEX IF NOT EXISTS index_account_order_line_item_order_id on account_order_line_item (tenant_id, order_id);
CREATE INDEX IF NOT EXISTS index_account_order_line_item_plan_id on account_order_line_item (tenant_id, plan_id);
CREATE INDEX IF NOT EXISTS index_account_order_line_item_ramp_group_id on account_order_line_item (tenant_id, ramp_group_id);
CREATE INDEX IF NOT EXISTS index_account_order_line_item_metrics_recompute ON account_order_line_item (tenant_id) INCLUDE (order_line_id) WHERE (metrics->>'recompute')::boolean = true AND is_deleted = false;
CREATE INDEX IF NOT EXISTS index_oli_base_external_subscription_charge_id ON account_order_line_item (tenant_id, base_external_subscription_charge_id) WHERE is_deleted = false;

COMMENT ON TABLE account_order_line_item IS '{"audited": true, "search": { "enabled" : true, "use_insert_specific_trigger" : true, "update_with_condition": true }, "rls" : { "tenant": true, "entity": "entity_id" }}';

CREATE TABLE IF NOT EXISTS auth_tenant_cognito
(
    id           UUID         NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id    VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    client_id    VARCHAR(255) NOT NULL,
    user_pool_id VARCHAR(255) NOT NULL,
    domain_name  VARCHAR(63)  NOT NULL UNIQUE,
    has_sso      BOOLEAN      NOT NULL             DEFAULT FALSE,
    is_deleted   BOOLEAN      NOT NULL             DEFAULT FALSE,
    created_on   TIMESTAMP,
    updated_on   TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS index_auth_tenant_cognito_client_id ON auth_tenant_cognito (tenant_id, client_id);

COMMENT ON TABLE auth_tenant_cognito IS '{"audited": true}';

CREATE TABLE IF NOT EXISTS subscription
(
    id                                  UUID        NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_id                     VARCHAR(36) NOT NULL,
    external_id                         VARCHAR(36),
    tenant_id                           VARCHAR(36) NOT NULL REFERENCES tenant (tenant_id),
    entity_id                           VARCHAR(36) NOT NULL,
    account_id                          VARCHAR(36) REFERENCES account (account_id),
    reseller_account_id                 VARCHAR(36) REFERENCES account (account_id),
    shipping_contact_id                 VARCHAR(36) REFERENCES account_contact (contact_id),
    billing_contact_id                  VARCHAR(36) REFERENCES account_contact (contact_id),
    currency                            VARCHAR(3)  NOT NULL,
    payment_term                        VARCHAR(6)  NOT NULL,
    state                               VARCHAR(32) NOT NULL,
    term_length_cycle                   VARCHAR(20),
    term_length_step                    INTEGER,
    billing_cycle                       VARCHAR(20) NOT NULL,
    billing_step                        INTEGER     NOT NULL,
    creation_time                       TIMESTAMP   NOT NULL,
    start_date                          TIMESTAMP,
    end_date                            TIMESTAMP   NOT NULL,
    billing_anchor_date                 TIMESTAMP   NOT NULL,
    billing_term                        VARCHAR(20) NOT NULL,
    predefined_discounts                TEXT,
    orders                              TEXT,
    purchase_orders                     JSON,
    po_required_for_invoicing           BOOLEAN     NOT NULL             DEFAULT FALSE,
    canceled_date                       TIMESTAMP,
    activation_date                     TIMESTAMP,
    renewed_from_subscription_id        VARCHAR(36),
    renewed_from_date                   TIMESTAMP,
    renewed_to_subscription_id          VARCHAR(36),
    renewed_to_date                     TIMESTAMP,
    restructured_from_subscription_id   VARCHAR(36),
    restructured_from_date              TIMESTAMP,
    restructured_to_subscription_id     VARCHAR(36),
    restructured_to_date                TIMESTAMP,
    version                             INTEGER,
    version_start                       TIMESTAMP,
    version_end                         TIMESTAMP,
    is_deleted                          BOOLEAN     NOT NULL             DEFAULT FALSE,
    created_on                          TIMESTAMP,
    updated_on                          TIMESTAMP,
    ramp_interval                       TEXT,
    auto_renew                          BOOLEAN     NOT NULL             DEFAULT FALSE,
    metrics                             JSONB       NOT NULL             DEFAULT '{"recompute": true}'::jsonb,
    proration_override_option           VARCHAR(100),
    renewal_opportunity_crm_id          VARCHAR(255),
    name                                VARCHAR(255),
    duration_model                      VARCHAR(50),
    CONSTRAINT subscription_entity_id_fkey FOREIGN KEY(tenant_id, entity_id) REFERENCES entity(tenant_id, entity_id),
    UNIQUE (tenant_id, subscription_id, version)
);

CREATE INDEX IF NOT EXISTS index_subscription_account_id ON subscription (tenant_id, account_id);
CREATE INDEX IF NOT EXISTS index_subscription_subscription_id ON subscription (tenant_id, subscription_id);
CREATE INDEX IF NOT EXISTS index_subscription_shipping_contact_id ON subscription (tenant_id, shipping_contact_id);
CREATE INDEX IF NOT EXISTS index_subscription_billing_contact_id ON subscription (tenant_id, billing_contact_id);
CREATE INDEX IF NOT EXISTS index_subscription_updated_on ON subscription (tenant_id, updated_on);
CREATE INDEX IF NOT EXISTS index_subscription_end_date ON subscription (tenant_id, end_date);
CREATE UNIQUE INDEX IF NOT EXISTS index_subscription_external_id ON subscription (tenant_id, external_id, version) WHERE external_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS index_subscription_auto_renew ON subscription (tenant_id, auto_renew);

COMMENT ON TABLE subscription IS '{"audited": true, "search": { "enabled" : true, "use_insert_specific_trigger" : true, "update_with_condition": true }, "rls" : { "tenant": true, "entity": "entity_id" }}';

CREATE TABLE IF NOT EXISTS subscription_charge
(
    id                           UUID        NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),

    charge_id                    VARCHAR(36) NOT NULL,
    subscription_charge_id       VARCHAR(36) NOT NULL UNIQUE,
    subscription_charge_group_id VARCHAR(36) NOT NULL,
    subscription_id              VARCHAR(36) NOT NULL, -- references external subscription id
    tenant_id                    VARCHAR(36) NOT NULL REFERENCES tenant (tenant_id),
    entity_id                    VARCHAR(36),
    account_id                   VARCHAR(36) REFERENCES account (account_id),
    currency_conversion_rate_id  UUID,
    quantity                     BIGINT,
    discounts                    TEXT,
    predefined_discounts         TEXT,
    is_ramp                      BOOLEAN     NOT NULL             DEFAULT FALSE,
    list_unit_price              NUMERIC(22, 10),
    sell_unit_price              NUMERIC(22, 10),
    discount_amount              NUMERIC(22, 10),
    pricing_override             JSONB,
    list_price_override_ratio    NUMERIC(13, 10),
    arr_override                 NUMERIC(22, 10),
    order_lines                  TEXT,
    start_date                   TIMESTAMP   NOT NULL,
    end_date                     TIMESTAMP   NOT NULL,
    version                      INTEGER,
    version_start                TIMESTAMP,
    version_end                  TIMESTAMP,
    rank                         INTEGER                          DEFAULT 0,
    is_deleted                   BOOLEAN     NOT NULL             DEFAULT FALSE,
    created_on                   TIMESTAMP,
    updated_on                   TIMESTAMP,
    -- references to price attributes for MAP RECURRING use case
    attribute_references         JSONB,
    CONSTRAINT subscription_charge_entity_id_fkey FOREIGN KEY(tenant_id, entity_id) REFERENCES entity(tenant_id, entity_id),
    FOREIGN KEY (tenant_id, charge_id) REFERENCES charge (tenant_id, charge_id)
);

CREATE INDEX IF NOT EXISTS index_subscription_charge_tenant_id_charge_id  ON subscription_charge (tenant_id, charge_id);
CREATE INDEX IF NOT EXISTS index_subscription_charge_account_id ON subscription_charge (tenant_id, account_id);
CREATE INDEX IF NOT EXISTS index_subscription_charge_subscription_id ON subscription_charge (tenant_id, subscription_id);
CREATE INDEX IF NOT EXISTS index_subscription_charge_subscription_charge_group_id ON subscription_charge(tenant_id, subscription_charge_group_id);

COMMENT ON TABLE subscription_charge IS '{"audited": true, "search": { "enabled" : true } }';

CREATE TABLE IF NOT EXISTS subscription_charge_map
(
    id                           UUID        NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id                    VARCHAR(36) NOT NULL REFERENCES tenant (tenant_id),
    subscription_id              UUID        NOT NULL REFERENCES subscription (id),
    subscription_charge_group_id VARCHAR(36) NOT NULL,
    version                      INTEGER,
    is_deleted                   BOOLEAN     NOT NULL             DEFAULT FALSE,
    created_on                   TIMESTAMP,
    updated_on                   TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_subscription_charge_map_subscription_id ON subscription_charge_map (tenant_id, subscription_id);
CREATE UNIQUE INDEX IF NOT EXISTS index_subscription_charge_map_subscription_charge_group_id ON subscription_charge_map (tenant_id, subscription_id, subscription_charge_group_id, version);

COMMENT ON TABLE subscription_charge_map IS '{"audited": true, "search": { "enabled" : true } }';

CREATE TABLE IF NOT EXISTS invoice
(
    id                              UUID        NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_number                  VARCHAR(36) NOT NULL,
    draft_invoice_number            VARCHAR(36),
    tenant_id                       VARCHAR(36) NOT NULL REFERENCES tenant (tenant_id),
    entity_id                       VARCHAR(36) NOT NULL,
    subscription_id                 VARCHAR(36) NOT NULL,
    customer_account_id             VARCHAR(36) REFERENCES account (account_id),
    reseller_account_id             VARCHAR(36) REFERENCES account (account_id),
    currency_code                   VARCHAR(10),
    payment_term                    VARCHAR(6)  NOT NULL,
    total_discount                  NUMERIC(22, 10),
    sub_total                       NUMERIC(22, 10),
    tax_total                       NUMERIC(22, 10),
    total                           NUMERIC(22, 10),
    exchange_rate_id                VARCHAR(36),
    exchange_rate                   NUMERIC(16,10),
    exchange_rate_date              TIMESTAMP,
    functional_total_discount       NUMERIC(22, 10),
    functional_sub_total            NUMERIC(22, 10),
    functional_tax_total            NUMERIC(22, 10),
    functional_total                NUMERIC(22, 10),
    status                          VARCHAR(36) NOT NULL,
    posted_date                     TIMESTAMP,
    invoice_date                    TIMESTAMP,
    voided_date                     TIMESTAMP,
    void_requested_on               TIMESTAMP,
    due_date                        TIMESTAMP,
    billing_contact                 JSON        NOT NULL,
    shipping_contact                JSON        NOT NULL,
    po_number                       VARCHAR(255),
    po_required                     BOOLEAN     NOT NULL             DEFAULT FALSE,
    is_deleted                      BOOLEAN     NOT NULL             DEFAULT FALSE,
    tax_transaction_code            VARCHAR(36),
    note                            VARCHAR(1000),
    email_notifiers_list            JSON        NOT NULL             DEFAULT '{ "toIds":[], "ccIds":[], "bccIds":[] }'::jsonb,
    generation_method               VARCHAR(100),
    generated_by                    VARCHAR(100),
    erp_id                          VARCHAR(100),
    should_regenerate_pdf           BOOLEAN     NOT NULL             DEFAULT TRUE,
    email_last_sent_on              TIMESTAMP,
    created_on                      TIMESTAMP,
    updated_on                      TIMESTAMP,
    CONSTRAINT invoice_exchange_rate_id_fkey FOREIGN KEY(tenant_id, exchange_rate_id) REFERENCES transactional_exchange_rate(tenant_id, transactional_exchange_rate_id),
    CONSTRAINT invoice_entity_id_fkey FOREIGN KEY(tenant_id, entity_id) REFERENCES entity(tenant_id, entity_id)
);

CREATE INDEX IF NOT EXISTS index_invoice_subscription_id ON invoice (tenant_id, subscription_id);
CREATE INDEX IF NOT EXISTS index_invoice_customer_account_id ON invoice (tenant_id, customer_account_id);
CREATE INDEX IF NOT EXISTS index_invoice_status ON invoice (tenant_id, status);
CREATE INDEX IF NOT EXISTS index_invoice_created_on_id ON invoice (tenant_id, created_on, id);
CREATE UNIQUE INDEX IF NOT EXISTS index_invoice_tenant_id_invoice_number ON invoice (tenant_id, invoice_number);
CREATE INDEX IF NOT EXISTS index_invoice_draft_invoice_number ON invoice (tenant_id, draft_invoice_number);

COMMENT ON TABLE invoice IS '{"audited": true, "search": { "enabled" : true }, "rls" : { "tenant": true, "entity": "entity_id" } }';

CREATE TABLE IF NOT EXISTS invoice_line_item
(
    id                           UUID        NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id                    VARCHAR(36) NOT NULL REFERENCES tenant (tenant_id),
    entity_id                    VARCHAR(36) NOT NULL,
    invoice_id                   UUID REFERENCES invoice (id),
    plan_id                      VARCHAR(36) NOT NULL,
    charge_id                    VARCHAR(36) NOT NULL,
    subscription_charge_id       VARCHAR(36) REFERENCES subscription_charge (subscription_charge_id),
    subscription_charge_group_id VARCHAR(36), -- todo: make NON NULL
    order_id                     VARCHAR(36) REFERENCES account_order (order_id),
    order_line_item_id           VARCHAR(36) REFERENCES account_order_line_item (order_line_id),
    invoice_line_number          VARCHAR(50),
    status                       VARCHAR(36) NOT NULL,
    list_amount                  NUMERIC(22, 10),
    discount_amount              NUMERIC(22, 10),
    amount                       NUMERIC(22, 10),
    list_unit_price              NUMERIC(22, 10),
    sell_unit_price              NUMERIC(22, 10),
    quantity                     BIGINT,
    drawdown_quantity_used       BIGINT,
    drawdown_quantity_remaining  BIGINT,
    tax_amount                   NUMERIC(22, 10),
    tax_rate                     JSON,
    period_start_date            TIMESTAMP,
    period_end_date              TIMESTAMP,
    functional_list_amount       NUMERIC(22, 10),
    functional_discount_amount   NUMERIC(22, 10),
    functional_amount            NUMERIC(22, 10),
    functional_tax_amount        NUMERIC(22, 10),
    is_deleted                   BOOLEAN     NOT NULL             DEFAULT FALSE,
    created_on                   TIMESTAMP,
    updated_on                   TIMESTAMP,
    CONSTRAINT invoice_line_item_entity_id_fkey FOREIGN KEY(tenant_id, entity_id) REFERENCES entity(tenant_id, entity_id),
    FOREIGN KEY (tenant_id, plan_id) REFERENCES plan (tenant_id, plan_id),
    FOREIGN KEY (tenant_id, charge_id) REFERENCES charge (tenant_id, charge_id)
);

CREATE INDEX IF NOT EXISTS index_invoice_line_item_invoice_id ON invoice_line_item (tenant_id, invoice_id);
-- also including charge_id to support the ability to query by both plan_id and charge_id
CREATE INDEX IF NOT EXISTS index_invoice_line_item_plan_id_charge_id ON invoice_line_item (tenant_id, plan_id, charge_id);
CREATE INDEX IF NOT EXISTS index_invoice_line_item_charge_id ON invoice_line_item (tenant_id, charge_id);
CREATE INDEX IF NOT EXISTS index_invoice_line_item_subscription_charge_id ON invoice_line_item (tenant_id, subscription_charge_id);
CREATE INDEX IF NOT EXISTS index_invoice_line_item_period_start_date ON invoice_line_item (tenant_id, period_start_date);
CREATE INDEX IF NOT EXISTS index_invoice_line_item_period_end_date ON invoice_line_item (tenant_id, period_end_date);
CREATE INDEX IF NOT EXISTS index_invoice_line_item_bulk_run ON invoice_line_item (tenant_id, period_end_date, order_line_item_id, status, is_deleted, entity_id);

CREATE UNIQUE INDEX IF NOT EXISTS index_order_line_item_period_start_date
    ON invoice_line_item (tenant_id, order_line_item_id, period_start_date)
    WHERE is_deleted = false and status <> 'VOIDED';

COMMENT ON TABLE invoice_line_item IS '{"audited": true, "rls" : { "tenant": true, "entity": "entity_id" }}';

CREATE TABLE IF NOT EXISTS tenant_invoice_configuration
(
    id                    UUID        NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id             VARCHAR(36) NOT NULL REFERENCES tenant (tenant_id),
    invoice_config_id     VARCHAR(36) NOT NULL DEFAULT uuid_generate_v4(),
    invoice_number_scheme VARCHAR(20),
    invoice_number_prefix VARCHAR(20),
    invoice_number_length INTEGER,
    proration_scheme      VARCHAR(50),
    proration_mode        VARCHAR(50),
    created_on            TIMESTAMP,
    updated_on            TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS index_tenant_invoice_config_id ON tenant_invoice_configuration (tenant_id, invoice_config_id);

COMMENT ON TABLE tenant_invoice_configuration IS '{"audited": true, "search": { "enabled" : true } }';

CREATE TABLE IF NOT EXISTS tenant_invoice_number_sequence
(
    id                  UUID        NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id           VARCHAR(36) NOT NULL REFERENCES tenant (tenant_id),
    invoice_config_id   VARCHAR(36),
    next_invoice_number BIGINT,
    created_on          TIMESTAMP,
    updated_on          TIMESTAMP,
    CONSTRAINT tenant_invoice_number_sequence_config_id_fkey FOREIGN KEY(tenant_id, invoice_config_id) REFERENCES tenant_invoice_configuration(tenant_id, invoice_config_id)
);

CREATE UNIQUE INDEX IF NOT EXISTS index_tenant_invoice_number_sequence_invoice_config_id ON tenant_invoice_number_sequence (tenant_id, invoice_config_id);

COMMENT ON TABLE tenant_invoice_number_sequence IS '{"audited": false}';

CREATE TABLE IF NOT EXISTS payment
(
    id                          UUID        NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id                  VARCHAR(36) NOT NULL,
    tenant_id                   VARCHAR(36) NOT NULL REFERENCES tenant (tenant_id),
    entity_id                   VARCHAR(36) NOT NULL,
    customer_account_id         VARCHAR(36) NOT NULL REFERENCES account (account_id),
    payment_method_id           UUID        NOT NULL REFERENCES account_payment_method (id),
    payment_bank_account_id     VARCHAR(36),
    lifecycle_type              VARCHAR(50),
    currency_code               VARCHAR(10),
    state                       VARCHAR(32) NOT NULL,
    status                      VARCHAR(255),
    amount                      NUMERIC(22, 10),
    amount_captured             NUMERIC(22, 10),
    bank_fee                    NUMERIC(22, 10),
    transaction_id              VARCHAR(255),
    payment_date                TIMESTAMP   NOT NULL,
    exchange_rate_id            VARCHAR(36),
    exchange_rate               NUMERIC(16,10),
    exchange_rate_date          TIMESTAMP,
    functional_amount           NUMERIC(22, 10),
    functional_amount_captured  NUMERIC(22, 10),
    functional_bank_fee         NUMERIC(22, 10),
    created_on                  TIMESTAMP,
    updated_on                  TIMESTAMP,
    void_date                   TIMESTAMP,
    void_requested_on           TIMESTAMP,
    metadata                    JSONB,
    CONSTRAINT payment_exchange_rate_id_fkey FOREIGN KEY(tenant_id, exchange_rate_id) REFERENCES transactional_exchange_rate(tenant_id, transactional_exchange_rate_id),
    CONSTRAINT payment_entity_id_fkey FOREIGN KEY(tenant_id, entity_id) REFERENCES entity(tenant_id, entity_id)
);

CREATE UNIQUE INDEX IF NOT EXISTS index_payment_payment_id ON payment (tenant_id, payment_id);
CREATE INDEX IF NOT EXISTS index_payment_customer_account_id ON payment (tenant_id, customer_account_id);
CREATE INDEX IF NOT EXISTS index_payment_created_on_id ON payment (tenant_id, created_on, id);
CREATE INDEX IF NOT EXISTS index_payment_created_on_payment_id ON payment (tenant_id, created_on, payment_id);
CREATE INDEX IF NOT EXISTS index_payment_transaction_id ON payment (tenant_id, transaction_id);

COMMENT ON TABLE payment IS '{"audited": true, "search": { "enabled" : true }, "rls" : { "tenant": true, "entity": "entity_id" } }';
