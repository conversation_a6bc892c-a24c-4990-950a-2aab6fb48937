CREATE TABLE IF NOT EXISTS automated_invoice_rule_configuration
(
    id                         UUID         NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    automated_invoice_rule_id  VARCHAR(36)  NOT NULL DEFAULT '',
    tenant_id                  VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    entity_ids                 TEXT ARRAY   DEFAULT ARRAY ['*'],
    name                       VARCHAR(255) NOT NULL,
    -- system will normalize the name by changing the name to lowercase, removing leading and trailing whitespaces
    -- and replacing all the whitespace characters in between with a single space. This normalized name will ensure
    -- that there are no duplicate names in the system.
    normalized_name            VARCHAR(255) NOT NULL,
    description                VARCHAR(255),
    -- cron expression for the invoice creation schedule, e.g. 0 0 12 1/1 * ? * for "AT 12:00 EVERY DAY"
    cron_expression            VARCHAR(255) NOT NULL,
    -- first_execution_date to be provided by the user, this will be the date when the job will run for the first time
    first_execution_date       TIMESTAMP    NOT NULL,
    -- last_execution_date will be the date when the job ran the last time
    -- the initial value of last_execution_date will be same first_execution_date
    last_execution_date        TIMESTAMP    NOT NULL,
    next_execution_date        TIMESTAMP,
    -- target_duration and invoice_duration will be used to calculate the target_date and invoice_date respectively
    -- these duration represent the number of days (+/-) referencing job current execution time
    target_duration            INTEGER      NOT NULL             DEFAULT 0,
    invoice_duration           INTEGER      NOT NULL             DEFAULT 0,
    include_usage_charge       BOOLEAN      NOT NULL             DEFAULT TRUE,
    include_non_usage_charge   BOOLEAN      NOT NULL             DEFAULT TRUE,
    -- if auto_post_invoice is true then the generated invoice will be posted automatically
    auto_post_invoice          BOOLEAN      NOT NULL             DEFAULT FALSE,
    -- if auto_email_invoice is true then the posted invoice will be posted automatically
    -- auto_email_invoice will be ignored if auto_post_invoice is false
    auto_email_invoice         BOOLEAN      NOT NULL             DEFAULT FALSE,
    enabled                    BOOLEAN      NOT NULL             DEFAULT FALSE,
    is_deleted                 BOOLEAN      NOT NULL             DEFAULT FALSE,
    created_on                 TIMESTAMP,
    updated_on                 TIMESTAMP
);

-- for a tenant it does not make any sense to allow duplicate names for automated invoice rules, hence the unique index on tenant_id and normalized_name
CREATE UNIQUE INDEX IF NOT EXISTS index_automated_invoice_rule_tenant_id_name_conditional ON automated_invoice_rule_configuration (tenant_id, normalized_name) WHERE is_deleted = FALSE;

CREATE INDEX IF NOT EXISTS index_automated_invoice_rule_by_id ON automated_invoice_rule_configuration(tenant_id, automated_invoice_rule_id) WHERE is_deleted = FALSE;

COMMENT ON TABLE automated_invoice_rule_configuration IS '{"audited": true, "search": { "enabled" : false }, "rls" : { "tenant": true, "entity": "entity_ids" } }';

CREATE TABLE IF NOT EXISTS automated_invoice_rule_execution
(
    id                         UUID         NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id                  VARCHAR(36)  NOT NULL REFERENCES tenant (tenant_id),
    automated_invoice_rule_id  VARCHAR(36)  NOT NULL,
    entity_id                  VARCHAR(36)  NOT NULL,
    last_execution_date        TIMESTAMP    NOT NULL,
    next_execution_date        TIMESTAMP,
    created_on                 TIMESTAMP,
    updated_on                 TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS index_automated_invoice_rule_execution_rule_id_entity_id ON automated_invoice_rule_execution(tenant_id, entity_id, automated_invoice_rule_id);

COMMENT ON TABLE automated_invoice_rule_execution IS '{"audited": false, "search": { "enabled" : false } }';
