CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION date_with_tz(input_time TIMESTAMP, tenant_timezone VARCHAR(50)) RETURNS DATE AS $$
BEGIN
    -- interprets the input timestamp as UTC first, then convert to tenant timezone
    RETURN (input_time AT TIME ZONE 'UTC' AT TIME ZONE tenant_timezone)::date;
END; $$
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION convert_tz(input_time TIMESTAMP, from_timezone VARCHAR(50), to_timezone VARCHAR(50)) RETURNS TIMESTAMP AS $$
BEGIN
    RETURN input_time AT TIME ZONE 'UTC' AT TIME ZONE from_timezone AT TIME ZONE to_timezone;
END; $$
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION epoch_to_timestamp(input_time INT) RETURNS TIMESTAMP AS $$
BEGIN
    RETURN TIMESTAMP WITH TIME ZONE 'EPOCH' + input_time * interval '1 second';
END; $$
LANGUAGE plpgsql;

CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION timestamp_to_epoch(input_time TIMESTAMP) RETURNS BIGINT AS $$
BEGIN
    RETURN extract(epoch from input_time);
END; $$
LANGUAGE plpgsql;
