-- Say price tiers looks like this:[{"untilQuantity":600,"amount":999},{"untilQuantity":800,"amount":99},{"amount":9}]
-- And conversion_rate is 1.58
-- The function should return:[{"amount": "1578.42", "untilQuantity": "600"}, {"amount": "156.42", "untilQuantity": "800"}, {"amount": "14.22"}]

CREATE OR REPLACE FUNCTION apply_foreign_exchange(price_tiers jsonb, conversion_rate NUMERIC(14,10))
  RETURNS jsonb AS $$
BEGIN
    RETURN (
      WITH price_tier_unpacked AS (
        SELECT i, key,
           CASE
             WHEN key = 'amount' THEN format_currency_amount(value::decimal(22,10) * conversion_rate)
             ELSE value
           END AS value
        FROM jsonb_array_elements(price_tiers) WITH ORDINALITY AS element(obj, i)
               CROSS JOIN LATERAL jsonb_each_text(element.obj) AS entry(key, value)
      ),
      group_objs AS (
      SELECT i, jsonb_object_agg(key, value) AS obj
        FROM price_tier_unpacked
       GROUP BY i
      )
      SELECT jsonb_agg(obj ORDER BY i) FROM group_objs);
END; $$
LANGUAGE plpgsql;

