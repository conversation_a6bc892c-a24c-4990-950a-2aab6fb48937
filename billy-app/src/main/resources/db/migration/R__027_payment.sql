CREATE TABLE IF NOT EXISTS payment_stripe_connect_integration
(
    id                 UUID           NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id          VARCHAR(36)    NOT NULL REFERENCES tenant (tenant_id),
    entity_id          VARCHAR(36),
    ledger_account_id  VARCHAR(36),
    connect_account_id VARCHAR(1024),
    integration_id     VARCHAR(36)    NOT NULL,
    is_completed       BOOLEAN        NOT NULL DEFAULT FALSE,
    is_deleted         BOOLEAN        NOT NULL DEFAULT FALSE,
    created_on         TIMESTAMP,
    updated_on         TIMESTAMP,
    CONSTRAINT payment_stripe_connect_integration_entity_id_fkey FOREIGN KEY(tenant_id, entity_id) REFERENCES entity(tenant_id, entity_id)
);

CREATE UNIQUE INDEX IF NOT EXISTS payment_stripe_connect_integration_tenant_id_entity_id_index ON payment_stripe_connect_integration(tenant_id, entity_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_stripe_connect_integration_connect_account_id_index ON payment_stripe_connect_integration(connect_account_id) WHERE is_deleted = FALSE;

COMMENT ON TABLE payment_stripe_connect_integration IS '{"audited":true}';

CREATE TABLE IF NOT EXISTS payment_provider_customer
(
    id               UUID          NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id        VARCHAR(36)   NOT NULL REFERENCES tenant (tenant_id),
    integration_id   UUID          REFERENCES payment_stripe_connect_integration (id), -- This would eventually be set to NOT NULL. For migration, we can leave it as nullable.
    payment_provider VARCHAR(255)  NOT NULL,
    customer_id      VARCHAR(1024) NOT NULL UNIQUE,
    account_id       VARCHAR(36)   REFERENCES account (account_id),
    is_deleted       BOOLEAN       NOT NULL DEFAULT FALSE,
    created_on       TIMESTAMP,
    updated_on       TIMESTAMP
);

CREATE INDEX IF NOT EXISTS payment_provider_customer_account_id_index ON payment_provider_customer(account_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_provider_customer_tenant_id_account_id_index ON payment_provider_customer(tenant_id, account_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_provider_customer_customer_id_index ON payment_provider_customer(customer_id) WHERE is_deleted = FALSE;

CREATE INDEX IF NOT EXISTS payment_provider_customer_account_id_integration_id_index ON payment_provider_customer(integration_id, account_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_provider_customer_tenant_account_integration_id_index ON payment_provider_customer(integration_id, tenant_id, account_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_provider_customer_customer_id_integration_id_index ON payment_provider_customer(integration_id, customer_id) WHERE is_deleted = FALSE;

COMMENT ON TABLE payment_provider_customer IS '{"audited": false}';

CREATE TABLE IF NOT EXISTS payment_provider_payment_method
(
    id                       UUID          NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id                VARCHAR(36)   NOT NULL REFERENCES tenant (tenant_id),
    integration_id           UUID          REFERENCES payment_stripe_connect_integration (id),
    customer_id              VARCHAR(1024) NOT NULL REFERENCES payment_provider_customer (customer_id),
    payment_method_id        VARCHAR(1024) NOT NULL, -- This is the external id returned by the payment provider
    payment_method_type      VARCHAR(100)  NOT NULL DEFAULT 'CARD',
    last_four                VARCHAR(4),
    bank_name                VARCHAR(1024),
    account_type             VARCHAR(100),
    account_number_last_four VARCHAR(4),
    expiry_date              DATE,
    is_deleted               BOOLEAN       NOT NULL DEFAULT FALSE,
    created_on               TIMESTAMP,
    updated_on               TIMESTAMP
);

CREATE INDEX IF NOT EXISTS payment_provider_payment_method_customer_id_index ON payment_provider_payment_method(tenant_id, customer_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_provider_payment_method_customer_integration_id_index ON payment_provider_payment_method(integration_id, tenant_id, customer_id) WHERE is_deleted = FALSE;
CREATE UNIQUE INDEX IF NOT EXISTS payment_provider_payment_method_payment_method_id_index ON payment_provider_payment_method(tenant_id, integration_id, customer_id, payment_method_id) WHERE is_deleted = FALSE;

COMMENT ON TABLE payment_provider_payment_method IS '{"audited": false}';

CREATE TABLE IF NOT EXISTS payment_link
(
    id                   UUID          NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    link_id              VARCHAR(50)   NOT NULL UNIQUE,
    tenant_id            VARCHAR(36)   NOT NULL REFERENCES tenant (tenant_id),
    integration_id       UUID          REFERENCES payment_stripe_connect_integration (id),
    customer_id          VARCHAR(1024) REFERENCES payment_provider_customer (customer_id),
    account_id           VARCHAR(36)   NOT NULL REFERENCES account (account_id),
    source_id            VARCHAR(36),
    source_type          VARCHAR(50),
    action_type          VARCHAR(20)   NOT NULL,
    max_attempts         INTEGER,
    number_of_attempts   INTEGER       NOT NULL,
    expires_on           TIMESTAMP,
    is_successfully_used BOOLEAN       NOT NULL DEFAULT FALSE,
    successfully_used_on TIMESTAMP,
    is_deleted           BOOLEAN       NOT NULL DEFAULT FALSE,
    created_on           TIMESTAMP,
    updated_on           TIMESTAMP
);

CREATE INDEX IF NOT EXISTS payment_link_tenant_id_link_id_index ON payment_link(tenant_id, link_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_link_tenant_id_customer_id_index ON payment_link(tenant_id, customer_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_link_tenant_id_account_id_index ON payment_link(tenant_id, account_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_link_tenant_id_index ON payment_link(tenant_id);

CREATE INDEX IF NOT EXISTS payment_link_tenant_id_link_id_integration_id_index ON payment_link(integration_id, tenant_id, link_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_link_tenant_id_customer_id_integration_id_index ON payment_link(integration_id, tenant_id, customer_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_link_tenant_id_account_id_integration_id_index ON payment_link(integration_id, tenant_id, account_id) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS payment_link_tenant_id_integration_id_index ON payment_link(integration_id, tenant_id);

COMMENT ON TABLE payment_link IS '{"audited": false}';

CREATE TABLE IF NOT EXISTS payment_processor_job_queue
(
    id             UUID          NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id      VARCHAR(36)   NOT NULL REFERENCES tenant (tenant_id),
    invoice_number VARCHAR(36),
    status         VARCHAR(100),
    created_on     TIMESTAMP,
    updated_on     TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS payment_processor_job_queue_tenant_id_invoice_number_index ON payment_processor_job_queue(tenant_id, invoice_number);
CREATE INDEX IF NOT EXISTS payment_processor_job_queue_tenant_id_index ON payment_processor_job_queue(tenant_id);
CREATE INDEX IF NOT EXISTS payment_processor_job_queue_tenant_id_status_index ON payment_processor_job_queue(tenant_id, status);
CREATE INDEX IF NOT EXISTS payment_processor_job_queue_tenant_id_status_updates_on_index ON payment_processor_job_queue(tenant_id, status, updated_on);

COMMENT ON TABLE payment_processor_job_queue IS '{"audited": false}';

CREATE TABLE IF NOT EXISTS tenant_payment_configuration
(
    id                      UUID        NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id               VARCHAR(36) NOT NULL UNIQUE REFERENCES tenant (tenant_id),
    supported_payment_types JSON        NOT NULL             DEFAULT '[
      "EXTERNAL",
      "WIRE",
      "CHECK"
    ]':: JSON,
    max_retries             INTEGER     NOT NULL DEFAULT 3,
    created_on              TIMESTAMP,
    updated_on              TIMESTAMP
);

COMMENT ON TABLE tenant_payment_configuration IS '{"audited": true, "search": { "enabled" : true } }';

CREATE TABLE IF NOT EXISTS account_payment_configuration
    (
        id                           UUID            NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id                    VARCHAR(36)     NOT NULL REFERENCES tenant (tenant_id),
        account_id                   VARCHAR(36)     NOT NULL REFERENCES account (account_id),
        exclude_from_payment_retries BOOLEAN,
        is_deleted                   BOOLEAN         NOT NULL DEFAULT FALSE,
        created_on                   TIMESTAMP,
        updated_on                   TIMESTAMP
    );

CREATE UNIQUE INDEX IF NOT EXISTS index_account_payment_configuration_account_id ON account_payment_configuration (tenant_id, account_id) WHERE is_deleted = FALSE;

COMMENT ON TABLE account_payment_configuration IS '{"audited": true }';