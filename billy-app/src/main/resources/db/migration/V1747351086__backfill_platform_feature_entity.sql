DO
$$
    DECLARE
        platform_feature_row RECORD;
        entity_row RECORD;

    BEGIN
        IF EXISTS (SELECT 1
                   FROM pg_tables
                   WHERE tablename = 'platform_feature') AND
           EXISTS (SELECT 1 FROM pg_tables where tablename = 'entity') AND
           EXISTS (SELECT 1 FROM pg_tables where tablename = 'platform_feature_entity')
        THEN
            -- Duplicate the platform_feature rows for each entity
            FOR platform_feature_row IN (SELECT * FROM platform_feature)
                LOOP
                    FOR entity_row IN (SELECT entity_id FROM entity WHERE tenant_id = platform_feature_row.tenant_id AND is_deleted = false)
                        LOOP
                            IF NOT EXISTS (SELECT 1 from platform_feature_entity where tenant_id = platform_feature_row.tenant_id and platform_feature_id = platform_feature_row.id and entity_id = entity_row.entity_id) THEN
                                INSERT INTO platform_feature_entity (tenant_id, platform_feature_id, entity_id, status)
                                VALUES (platform_feature_row.tenant_id, platform_feature_row.id, entity_row.entity_id, CASE WHEN platform_feature_row.enabled THEN 'READY' ELSE 'DISABLED' END)
                                ON CONFLICT DO NOTHING;
                            END IF;
                        END LOOP;
                END LOOP;
        END IF;
    END
$$;