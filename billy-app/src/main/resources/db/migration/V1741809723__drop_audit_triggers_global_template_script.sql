DO $$
    BEGIN
        IF EXISTS (SELECT 1
                   FROM pg_tables
                   WHERE tablename = 'global_template_script')
        THEN
            COMMENT ON TABLE global_template_script IS '{"audited": false, "search": { "enabled" : false } }';
            DROP TRIGGER IF EXISTS audit_trigger ON global_template_script;
            DROP TRIGGER IF EXISTS audit_update_trigger ON global_template_script;
        END IF;
    END
$$ ;
