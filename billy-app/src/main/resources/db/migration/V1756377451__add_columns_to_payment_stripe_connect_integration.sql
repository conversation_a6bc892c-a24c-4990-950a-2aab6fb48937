DO
$$
    BEGIN
        IF EXISTS (
           SELECT 1
           FROM pg_tables
           WHERE tablename = 'payment_stripe_connect_integration'
        ) THEN
           -- Add a column
           EXECUTE 'ALTER TABLE payment_stripe_connect_integration ADD COLUMN IF NOT EXISTS entity_id VARCHAR(36);';
           EXECUTE 'ALTER TABLE payment_stripe_connect_integration ADD COLUMN IF NOT EXISTS ledger_account_id VARCHAR(36);';

           -- Drop an index (if exists)
           EXECUTE 'DROP INDEX IF EXISTS payment_stripe_connect_integration_tenant_id_index;';

           -- Create a new index
           EXECUTE 'CREATE UNIQUE INDEX IF NOT EXISTS payment_stripe_connect_integration_tenant_id_entity_id_index ON payment_stripe_connect_integration(tenant_id, entity_id) WHERE is_deleted = FALSE;';

        END IF;
    END
$$;
