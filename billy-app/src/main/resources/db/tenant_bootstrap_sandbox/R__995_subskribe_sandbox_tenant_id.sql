-- [jooq ignore start]
-- ${flyway:timestamp}
INSERT INTO tenant (tenant_id, name, email, phone_number, encrypted_password, is_test, created_on, updated_on)
VALUES ('ab6eee66-6367-4746-9faa-d6dcc73df68b', 'Internal', '<EMAIL>', '************', 'QaV3oNvq5HFs9umrr99bGuLd5+ss13NmujSVF7K8lKk=', true, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO payment_term_settings(tenant_id)
VALUES ('ab6eee66-6367-4746-9faa-d6dcc73df68b')
ON CONFLICT DO NOTHING;

DO
$do$
    BEGIN
        IF NOT EXISTS (
                SELECT FROM pg_catalog.pg_roles
                WHERE  rolname = 'ab6eee66-6367-4746-9faa-d6dcc73df68b') THEN

            create user "ab6eee66-6367-4746-9faa-d6dcc73df68b" WITH PASSWORD 'subskribe-tenant';
        END IF;
    END
$do$;

INSERT INTO auth_tenant_cognito (tenant_id, client_id, user_pool_id, domain_name, created_on, updated_on)
VALUES ('ab6eee66-6367-4746-9faa-d6dcc73df68b', '53kpfcu8eshl32gdjoqcjr4i95', 'us-east-2_oV8TMQClp', 'billy-sandbox-admin-pool-domain', NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO tenant_user (user_id, tenant_id, display_name, title, email, normalized_email, phone_number, role, state, created_on, updated_on)
VALUES ('USER-BILLY-ADMIN', 'ab6eee66-6367-4746-9faa-d6dcc73df68b', 'Billy Admin', 'Billy Admin', '<EMAIL>', '<EMAIL>', '************', 'BILLY_ADMIN', 'ACTIVE', NOW(), NOW())
ON CONFLICT DO NOTHING;

-- Add slack and webhook notifications for sandbox demo tenant

INSERT INTO notification (tenant_id, topic_arn, notification_target, notification_target_type, created_on, updated_on, name)
VALUES ('1e082eb0-53b9-44fa-9a48-07fecc2897a9', 'arn:aws:sns:us-east-2:085981900979:sandbox-sns-billy_TenantNotifications',
        '*********************************************************************************', 'SLACK', NOW(), NOW(), 'Demo Slack Notification')
    ON CONFLICT DO NOTHING;

INSERT INTO notification (tenant_id, topic_arn, notification_target, notification_target_type, created_on, updated_on, name)
VALUES ('1e082eb0-53b9-44fa-9a48-07fecc2897a9', 'arn:aws:sns:us-east-2:085981900979:sandbox-sns-billy_TenantNotifications',
        'https://sandbox.subskribe.net/api/backend/demo', 'WEBHOOK', NOW(), NOW(), 'Demo Webhook Notification')
    ON CONFLICT DO NOTHING;

INSERT INTO notification_event (tenant_id, notification_id, event, created_on, updated_on)
SELECT tenant_id, id, 'INVOICE_POSTED', NOW(), NOW()
FROM notification where (tenant_id = '1e082eb0-53b9-44fa-9a48-07fecc2897a9' AND notification.notification_target_type = 'SLACK')
    ON CONFLICT DO NOTHING;

INSERT INTO notification_event (tenant_id, notification_id, event, created_on, updated_on)
SELECT tenant_id, id, 'SUBSCRIPTION_CREATED', NOW(), NOW()
FROM notification where (tenant_id = '1e082eb0-53b9-44fa-9a48-07fecc2897a9' AND notification.notification_target_type = 'SLACK')
    ON CONFLICT DO NOTHING;

INSERT INTO notification_event (tenant_id, notification_id, event, created_on, updated_on)
SELECT tenant_id, id, 'ORDER_SUBMITTED', NOW(), NOW()
FROM notification where (tenant_id = '1e082eb0-53b9-44fa-9a48-07fecc2897a9' AND notification.notification_target_type = 'SLACK')
    ON CONFLICT DO NOTHING;

INSERT INTO notification_event (tenant_id, notification_id, event, created_on, updated_on)
SELECT tenant_id, id, 'ORDER_EXECUTED', NOW(), NOW()
FROM notification where (tenant_id = '1e082eb0-53b9-44fa-9a48-07fecc2897a9' AND notification.notification_target_type = 'WEBHOOK')
    ON CONFLICT DO NOTHING;

-- [jooq ignore stop]
