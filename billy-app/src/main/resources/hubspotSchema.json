[{"name": "subskribe_subscription", "labels": {"singular": "Subskribe Subscription", "plural": "Subskribe Subscriptions"}, "requiredProperties": ["name"], "searchableProperties": ["name"], "primaryDisplayProperty": "name", "secondaryDisplayProperties": ["tcv", "entry_arr"], "properties": [{"name": "name", "label": "Subscription Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_id", "label": "Subskribe Subscription ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "account_url", "label": "Account URL", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_url", "label": "Subskribe Subscription URL", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "entry_arr", "label": "Entry ARR", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "exit_arr", "label": "Exit ARR", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "tcv", "label": "TCV", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "subscription_end_date", "label": "Subscription End Date", "type": "date", "fieldType": "date", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_start_date", "label": "Subscription Start Date", "type": "date", "fieldType": "date", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "currency", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_status", "label": "Subscription Status", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "delta_arr", "label": "Delta ARR", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "renewed_to_subscription", "label": "Renewed to Subscription", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "renewed_from_subscription", "label": "Renewed from Subscription", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_cancellation_date", "label": "Subscription Cancellation Date", "type": "date", "fieldType": "date", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "billing_cycle_start_date", "label": "Billing Cycle Start Date", "type": "date", "fieldType": "date", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "recurring_total", "label": "Recurring Total", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "non_recurring_total", "label": "Non Recurring Total", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}], "associatedObjects": ["COMPANY"]}, {"name": "subskribe_line_item", "labels": {"singular": "Subskribe Line Item", "plural": "Subskribe Line Items"}, "requiredProperties": ["name"], "searchableProperties": ["name"], "primaryDisplayProperty": "name", "secondaryDisplayProperties": ["term", "quantity"], "properties": [{"name": "name", "label": "Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "charge_id", "label": "Charge ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "charge_name", "label": "Charge Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "currency", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "line_start_date", "label": "Line Start Date", "type": "date", "fieldType": "date", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "line_end_date", "label": "Line End Date", "type": "date", "fieldType": "date", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "plan_id", "label": "Plan ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "plan_name", "label": "Plan Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "product_id", "label": "Product ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "product_name", "label": "Product Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "quantity", "label": "Quantity", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "list_unit_price", "label": "List Unit Price", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "sell_unit_price", "label": "Sell Unit Price", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "discount", "label": "Discount", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "subscription_id", "label": "Subscription ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_start_date", "label": "Subscription Start Date", "type": "date", "fieldType": "date", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_end_date", "label": "Subscription End Date", "type": "date", "fieldType": "date", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "term_length_in_years", "label": "Term Length In Years", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "term", "label": "Term", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_charge_id", "label": "Subscription Charge ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}]}, {"name": "subskribe_orders", "labels": {"singular": "Subskribe Order", "plural": "Subskribe Orders"}, "requiredProperties": ["name"], "searchableProperties": ["name"], "primaryDisplayProperty": "name", "secondaryDisplayProperties": ["order_status", "order_execution_date"], "properties": [{"name": "name", "label": "Order Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "order_status", "label": "Order Status", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "order_execution_date", "label": "Order Execution Date", "type": "date", "fieldType": "date", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "order_total", "label": "Order Total", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "primary_order_id", "label": "Order ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "primary_order_link", "label": "Order Link", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_id", "label": "Subscription ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_link", "label": "Subscription Link", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "opportunity_link", "label": "Opportunity Link", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "entry_arr", "label": "Entry ARR", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "exit_arr", "label": "Exit ARR", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "average_arr", "label": "Average ARR", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "recurring_total", "label": "Recurring Total", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "non_recurring_total", "label": "Non Recurring Total", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "tcv", "label": "TCV", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "average_acv", "label": "Average ACV", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "currency", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_auto_renew", "label": "Subskribe Order Auto Renew", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_discount_total", "label": "Subskribe Order Discount Total", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_discount_percent", "label": "Subskribe Order Discount Percent", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}], "associatedObjects": ["DEAL"]}, {"name": "subskribe_order_lines", "labels": {"singular": "Subskribe Order Line", "plural": "Subskribe Order Lines"}, "metaType": "PORTAL_SPECIFIC", "primaryDisplayProperty": "name", "secondaryDisplayProperties": ["charge_name", "quantity"], "requiredProperties": ["name"], "properties": [{"name": "name", "label": "Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "charge_name", "label": "Charge Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "quantity", "label": "Quantity", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "tcv", "label": "TCV", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "product_category", "label": "Product Category", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "product_name", "label": "Product Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "plan_name", "label": "Plan Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "charge_id", "label": "Charge ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "currency", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_id", "label": "Subscription ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "list_unit_price", "label": "List Unit Price", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "sell_unit_price", "label": "Sell Unit Price", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "discount", "label": "Discount %", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "arr", "label": "ARR", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "acv", "label": "ACV", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "line_start_date", "label": "Line Start Date", "type": "date", "fieldType": "date", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "line_end_date", "label": "Line End Date", "type": "date", "fieldType": "date", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "uuid", "label": "UUID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "product_external_id", "label": "Product External ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "plan_external_id", "label": "Plan External ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "charge_external_id", "label": "Charge External ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "order_id", "label": "Order ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "item_code", "label": "Item Code", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "deal_id", "label": "Deal ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}]}, {"name": "deal", "labels": {"singular": "Deal", "plural": "Deals"}, "properties": [{"name": "average_arr", "label": "Subskribe Average ARR", "type": "number", "fieldType": "number", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "delta_arr", "label": "Subskribe Delta ARR", "type": "number", "fieldType": "number", "description": "", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "entry_arr", "label": "Subskribe Entry ARR", "type": "number", "fieldType": "number", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "exit_arr", "label": "Subskribe Exit ARR", "type": "number", "fieldType": "number", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "order_total", "label": "Subskribe Order Total", "type": "number", "fieldType": "number", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "showCurrencySymbol": false, "formField": false}, {"name": "subscription_id", "label": "Subskribe Subscription ID", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_opportunity_link", "label": "Subskribe Opportunity URL", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_id", "label": "Subskribe Order ID", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_status", "label": "Subskribe Order Status", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_primary_order_link", "label": "Subskribe Order URL", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_term_end_date", "label": "Subskribe Term End Date", "type": "date", "fieldType": "date", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_term_start_date", "label": "Subskribe Term Start Date", "type": "date", "fieldType": "date", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_start_date_type", "label": "Subskribe Order Start Date Type", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_payment_terms", "label": "Subskribe Payment Terms", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_billing_cycle", "label": "Subskribe Billing Cycle", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_type", "label": "Subskribe Order Type", "type": "enumeration", "fieldType": "select", "description": "", "groupName": "subskribe", "options": [{"label": "New", "value": "new", "displayOrder": 0, "hidden": false}, {"label": "Amendment", "value": "amendment", "displayOrder": 1, "hidden": false}, {"label": "Renewal", "value": "renewal", "displayOrder": 2, "hidden": false}, {"label": "Cancel", "value": "cancel", "displayOrder": 3, "hidden": false}, {"label": "Amend and Renew", "value": "amend_and_renew", "displayOrder": 4, "hidden": false}, {"label": "Cancel and Restructure", "value": "cancel_and_restructure", "displayOrder": 5, "hidden": false}], "hasUniqueValue": false, "hidden": false, "formField": true}, {"name": "subskribe_average_acv", "label": "Subskribe Average ACV", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_expiration_date", "label": "Subskribe Order Expiration Date", "type": "date", "fieldType": "date", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_esign_status", "label": "Subskribe eSign Status", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_esign_link", "label": "Subskribe eSign Link", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_auto_renew", "label": "Subskribe Order Auto Renew", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_discount_total", "label": "Subskribe Order Discount Total", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subskribe_order_discount_percent", "label": "Subskribe Order Discount Percent", "type": "number", "fieldType": "number", "groupName": "subskribe", "options": [], "showCurrencySymbol": false, "hasUniqueValue": false, "hidden": false, "formField": false}]}, {"name": "company", "labels": {"singular": "Company", "plural": "Companies"}, "properties": [{"name": "subskribe_account", "label": "Subskribe Account URL", "type": "string", "fieldType": "text", "description": "", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}]}, {"name": "subskribe_rate_cards", "labels": {"singular": "Subskribe Rate Card", "plural": "Subskribe Rate Cards"}, "metaType": "PORTAL_SPECIFIC", "primaryDisplayProperty": "price_attribute_id", "secondaryDisplayProperties": ["price_attribute_name", "price_attribute_value"], "requiredProperties": ["price_attribute_id"], "properties": [{"name": "price_attribute_name", "label": "Price Attribute Name", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "price_attribute_value", "label": "Price Attribute Value", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "price_attribute_id", "label": "Price Attribute ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "rate_card_id", "label": "Rate Card ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "deal_id", "label": "Deal ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "order_id", "label": "Order ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "order_line_id", "label": "Order Line ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_id", "label": "Subscription ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}, {"name": "subscription_charge_id", "label": "Subscription Charge ID", "type": "string", "fieldType": "text", "groupName": "subskribe", "options": [], "hasUniqueValue": false, "hidden": false, "formField": false}]}]