```mermaid
---
title: Payment State Diagram
---
stateDiagram-v2
    s1 : Success
    s2 : Success
    
    [*] --> Initiated
    Initiated --> PaymentAttempt

    state PaymentAttempt {
        [*] --> Created
        Created --> Confirmed: 2PC
        Confirmed --> Processing
        Processing --> s2
        Processing --> Failed
        Confirmed --> s2
        Confirmed --> Failed
        s2 --> [*]
        Failed --> [*]
    }

    PaymentAttempt --> s1
    PaymentAttempt --> Abandoned
    s1 --> Voided
    s1 --> [*]
    Voided --> [*]
    Abandoned --> [*]
```