{"order": {"id": "ORD-17QDXBQ", "entityId": "ENT-XJREF38", "name": "Mother-of-all-orders", "orderType": "NEW", "currency": "USD", "paymentTerm": "NET30", "subscriptionId": "SUB-42JT4DC", "subscriptionTargetVersion": 1, "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1}], "creditableAmounts": [], "lineItems": [{"id": "0d2ce05b-0372-4970-96c8-566d3702e87e", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "1777f1d4-8dc0-44db-9d0d-7cf439aae2c0", "chargeId": "CHRG-WD06ZP6", "quantity": 1500, "isRamp": false, "discountAmount": 41.33, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 21.75}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 19.575}], "amount": 176.175, "listAmount": 217.5, "annualizedAmount": 58.73, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "7e796c04-dda4-4f99-af08-fa26a2c755b0", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "c82aa862-8c64-4301-8436-dae16b2e5f09", "chargeId": "CHRG-5FWC94V", "quantity": 1, "isRamp": false, "listUnitPrice": 50000.0, "sellUnitPrice": 40499.1, "discountAmount": 9500.9, "discounts": [{"name": "default", "percent": 0.10002, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 5001.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 4499.9}], "amount": 40499.1, "listAmount": 50000.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "70140f79-ccb6-4003-92d8-ab2654c6dbcd", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "be95b091-e6a5-41de-a0b9-8bd641e7b10a", "chargeId": "CHRG-KTBXQ1K", "quantity": 1, "isRamp": false, "listUnitPrice": 5000.0, "sellUnitPrice": 4050.0, "discountAmount": 950.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 500.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 450.0}], "amount": 4050.0, "listAmount": 5000.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "3af9c999-7185-428e-8b7f-dc59cf513b59", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "04a52d13-5560-4951-8f60-8c2a9bdadca3", "chargeId": "CHRG-X16H072", "quantity": 3, "isRamp": false, "listUnitPrice": 3000.0, "sellUnitPrice": 2430.0, "discountAmount": 1710.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 900.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 810.0}], "amount": 7290.0, "listAmount": 9000.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "67fd00c4-07d4-488c-bb67-27205fbcb294", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "edebac16-320e-40be-87af-c0b89f950f6d", "chargeId": "CHRG-4BQM09Q", "quantity": 50, "isRamp": true, "listUnitPrice": 100.0, "sellUnitPrice": 81.0, "discountAmount": 950.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 500.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 450.0}], "amount": 4050.0, "listAmount": 5000.0, "annualizedAmount": 4050.0, "effectiveDate": 1659337200, "endDate": 1690873200, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "96f74c33-1d0a-40e7-b612-f17842262ccb", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "edebac16-320e-40be-87af-c0b89f950f6d", "chargeId": "CHRG-4BQM09Q", "quantity": 100, "isRamp": true, "listUnitPrice": 100.0, "sellUnitPrice": 81.0, "discountAmount": 1900.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 1000.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 900.0}], "amount": 8100.0, "listAmount": 10000.0, "annualizedAmount": 8100.0, "effectiveDate": 1690873200, "endDate": 1722495600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "26b4184b-8d18-43e0-a173-677219310ce8", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "edebac16-320e-40be-87af-c0b89f950f6d", "chargeId": "CHRG-4BQM09Q", "quantity": 150, "isRamp": true, "listUnitPrice": 100.0, "sellUnitPrice": 81.0, "discountAmount": 2850.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 1500.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 1350.0}], "amount": 12150.0, "listAmount": 15000.0, "annualizedAmount": 12150.0, "effectiveDate": 1722495600, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "87121f51-9970-461f-8577-cacbdb38abcd", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "acda2477-b861-4fbb-a2b7-7dc2b151543b", "chargeId": "CHRG-H6ZQ42B", "quantity": 350, "isRamp": false, "listUnitPrice": 8.0, "sellUnitPrice": 6.48, "discountAmount": 1596.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 840.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 756.0}], "amount": 6804.0, "listAmount": 8400.0, "annualizedAmount": 2268.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "2498999b-b3ca-48dd-be0c-ed305ebd2351", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "bc588cc5-7d16-4f21-a122-31d5e2a52380", "chargeId": "CHRG-NK5Q5QJ", "quantity": 400, "isRamp": false, "listUnitPrice": 1.0, "sellUnitPrice": 0.81, "discountAmount": 228.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 120.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 108.0}], "amount": 972.0, "listAmount": 1200.0, "annualizedAmount": 324.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "ca737aa0-d1fc-4431-bb86-c0ced947a7b1", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "8a3f9ba4-f8af-49ed-b5fd-ba8862fa0799", "chargeId": "CHRG-1K6JBET", "quantity": 0, "isRamp": false, "discountAmount": 0.0, "discounts": [], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 0.0}], "amount": 0.0, "listAmount": 0.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "519b0d52-2f06-4e63-b527-2d4486c0d5b3", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-Z2BBRDE", "subscriptionChargeGroupId": "510b1047-b670-459d-b0d1-c544e96d676a", "chargeId": "CHRG-VQQVEZR", "quantity": 5, "isRamp": false, "listUnitPrice": 1990.88, "sellUnitPrice": 1612.61, "discountAmount": 1891.34, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 995.44}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 895.896}], "amount": 8063.05, "listAmount": 9954.4, "annualizedAmount": 2687.68, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "b140f3de-ffbb-4540-a842-482d5869a5f5", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-TD9PHP5", "subscriptionChargeGroupId": "f8fcf844-6d25-4903-8c08-b668a5c921fb", "chargeId": "CHRG-NMH2KWN", "quantity": 1, "isRamp": false, "listUnitPrice": 4977.19, "sellUnitPrice": 4031.52, "discountAmount": 945.67, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 497.719}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 447.9471}], "amount": 4031.52, "listAmount": 4977.19, "annualizedAmount": 1343.84, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}], "lineItemsNetEffect": [{"id": "ca737aa0-d1fc-4431-bb86-c0ced947a7b1", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "8a3f9ba4-f8af-49ed-b5fd-ba8862fa0799", "chargeId": "CHRG-1K6JBET", "quantity": 0, "isRamp": false, "discountAmount": 0.0, "discounts": [], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 0.0}], "amount": 0.0, "listAmount": 0.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "67fd00c4-07d4-488c-bb67-27205fbcb294", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "edebac16-320e-40be-87af-c0b89f950f6d", "chargeId": "CHRG-4BQM09Q", "quantity": 50, "isRamp": true, "listUnitPrice": 100.0, "sellUnitPrice": 81.0, "discountAmount": 950.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 500.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 450.0}], "amount": 4050.0, "listAmount": 5000.0, "annualizedAmount": 4050.0, "effectiveDate": 1659337200, "endDate": 1690873200, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "96f74c33-1d0a-40e7-b612-f17842262ccb", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "edebac16-320e-40be-87af-c0b89f950f6d", "chargeId": "CHRG-4BQM09Q", "quantity": 100, "isRamp": true, "listUnitPrice": 100.0, "sellUnitPrice": 81.0, "discountAmount": 1900.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 1000.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 900.0}], "amount": 8100.0, "listAmount": 10000.0, "annualizedAmount": 8100.0, "effectiveDate": 1690873200, "endDate": 1722495600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "26b4184b-8d18-43e0-a173-677219310ce8", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "edebac16-320e-40be-87af-c0b89f950f6d", "chargeId": "CHRG-4BQM09Q", "quantity": 150, "isRamp": true, "listUnitPrice": 100.0, "sellUnitPrice": 81.0, "discountAmount": 2850.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 1500.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 1350.0}], "amount": 12150.0, "listAmount": 15000.0, "annualizedAmount": 12150.0, "effectiveDate": 1722495600, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "7e796c04-dda4-4f99-af08-fa26a2c755b0", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "c82aa862-8c64-4301-8436-dae16b2e5f09", "chargeId": "CHRG-5FWC94V", "quantity": 1, "isRamp": false, "listUnitPrice": 50000.0, "sellUnitPrice": 40499.1, "discountAmount": 9500.9, "discounts": [{"name": "default", "percent": 0.10002, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 5001.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 4499.9}], "amount": 40499.1, "listAmount": 50000.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "87121f51-9970-461f-8577-cacbdb38abcd", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "acda2477-b861-4fbb-a2b7-7dc2b151543b", "chargeId": "CHRG-H6ZQ42B", "quantity": 350, "isRamp": false, "listUnitPrice": 8.0, "sellUnitPrice": 6.48, "discountAmount": 1596.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 840.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 756.0}], "amount": 6804.0, "listAmount": 8400.0, "annualizedAmount": 2268.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "70140f79-ccb6-4003-92d8-ab2654c6dbcd", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "be95b091-e6a5-41de-a0b9-8bd641e7b10a", "chargeId": "CHRG-KTBXQ1K", "quantity": 1, "isRamp": false, "listUnitPrice": 5000.0, "sellUnitPrice": 4050.0, "discountAmount": 950.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 500.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 450.0}], "amount": 4050.0, "listAmount": 5000.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "2498999b-b3ca-48dd-be0c-ed305ebd2351", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "bc588cc5-7d16-4f21-a122-31d5e2a52380", "chargeId": "CHRG-NK5Q5QJ", "quantity": 400, "isRamp": false, "listUnitPrice": 1.0, "sellUnitPrice": 0.81, "discountAmount": 228.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 120.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 108.0}], "amount": 972.0, "listAmount": 1200.0, "annualizedAmount": 324.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "b140f3de-ffbb-4540-a842-482d5869a5f5", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-TD9PHP5", "subscriptionChargeGroupId": "f8fcf844-6d25-4903-8c08-b668a5c921fb", "chargeId": "CHRG-NMH2KWN", "quantity": 1, "isRamp": false, "listUnitPrice": 4977.19, "sellUnitPrice": 4031.52, "discountAmount": 945.67, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 497.719}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 447.9471}], "amount": 4031.52, "listAmount": 4977.19, "annualizedAmount": 1343.84, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "519b0d52-2f06-4e63-b527-2d4486c0d5b3", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-Z2BBRDE", "subscriptionChargeGroupId": "510b1047-b670-459d-b0d1-c544e96d676a", "chargeId": "CHRG-VQQVEZR", "quantity": 5, "isRamp": false, "listUnitPrice": 1990.88, "sellUnitPrice": 1612.61, "discountAmount": 1891.34, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 995.44}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 895.896}], "amount": 8063.05, "listAmount": 9954.4, "annualizedAmount": 2687.68, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "0d2ce05b-0372-4970-96c8-566d3702e87e", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "1777f1d4-8dc0-44db-9d0d-7cf439aae2c0", "chargeId": "CHRG-WD06ZP6", "quantity": 1500, "isRamp": false, "discountAmount": 41.33, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 21.75}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 19.575}], "amount": 176.175, "listAmount": 217.5, "annualizedAmount": 58.73, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}, {"id": "3af9c999-7185-428e-8b7f-dc59cf513b59", "isDryRunItem": false, "action": "ADD", "planId": "PLAN-ZHYZMTM", "subscriptionChargeGroupId": "04a52d13-5560-4951-8f60-8c2a9bdadca3", "chargeId": "CHRG-X16H072", "quantity": 3, "isRamp": false, "listUnitPrice": 3000.0, "sellUnitPrice": 2430.0, "discountAmount": 1710.0, "discounts": [{"name": "default", "percent": 0.1, "discountAmount": null, "status": null, "discountedPrice": null, "amount": 900.0}], "predefinedDiscounts": [{"id": "DISC-Z3TW23V", "percent": 0.1, "amount": 810.0}], "amount": 7290.0, "listAmount": 9000.0, "effectiveDate": 1659337200, "endDate": 1754031600, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "value": null, "selections": [], "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "value": null, "selections": [], "options": ["CA", "US", "AU"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "value": null, "selections": [], "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "value": null, "selections": [], "options": ["included", "itemized", "future"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "> than 100K", "selections": null}}], "dryRunItem": false}], "startDate": 1659337200, "endDate": 1754031600, "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "billingTerm": "UP_FRONT", "billingAnchorDate": 1659337200, "totalAmount": 96185.845, "totalListAmount": 118749.08, "totalListAmountBeforeOverride": 0.0, "status": "EXECUTED", "executedOn": 1660757634, "updatedOn": 1740157420, "rampInterval": [1659337200, 1690873200, 1722495600], "orderFormTemplateIds": [], "orderTerms": [{"id": "c101a582-b1c6-445a-821f-9cd88a98f9c8", "templateGroupId": "59280a99-ebab-4f4f-9157-1476e22c2f42", "templateGroupVersion": 1, "orderId": "ORD-17QDXBQ", "levelType": "ORDER", "planIds": [], "deleted": null, "templateId": "59280a99-ebab-4f4f-9157-1476e22c2f42"}, {"id": "431b77cb-9829-4bd5-b9ab-094538d0935d", "templateGroupId": "9ad8ec90-e56e-4d67-bea1-40d124420c2b", "templateGroupVersion": 1, "orderId": "ORD-17QDXBQ", "levelType": "ORDER", "planIds": [], "deleted": null, "templateId": "9ad8ec90-e56e-4d67-bea1-40d124420c2b"}], "isPrimaryOrderForSfdcOpportunity": false, "sfdcOrderCanBeExecuted": true, "renewalForSubscriptionVersion": 0, "purchaseOrderRequiredForInvoicing": false, "autoRenew": false, "customFields": [{"id": "CF-13W8FNJZ", "type": "STRING", "name": "0__test", "label": "2. test", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-81E91Y1R", "type": "STRING", "name": "1__test", "label": "1. test", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-32M873B2", "type": "STRING", "name": "Order_Category", "label": "Default Order Category", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "Enterprise", "selections": null}}, {"id": "CF-2P979N66", "type": "PICKLIST", "name": "Order_Segment", "label": "default Order Segment", "value": null, "selections": [], "options": ["Small", "Mid", "Custom"], "required": false, "source": "USER", "defaultValue": {"value": null, "selections": ["Mid"]}}, {"id": "CF-7DZJQ5JK", "type": "STRING", "name": "Order_TCV", "label": "Order TCV", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-HZZNY2D6", "type": "STRING", "name": "Order_type", "label": "Default Order Type", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "New", "selections": null}}, {"id": "CF-DCP6HGXK", "type": "PICKLIST", "name": "deposit_amount", "label": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "value": null, "selections": [], "options": ["10000", "20000", "27500", "50000", "100000"], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-1VJDXZZB", "type": "STRING", "name": "legal_name", "label": "Legal Name", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-Q1H13Y8J", "type": "STRING", "name": "order_item_10", "label": "order_item_10", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, {"id": "CF-3V882R2H", "type": "PICKLIST", "name": "show_contact", "label": "show_contact", "value": null, "selections": [], "options": ["yes", "no"], "required": false, "source": "USER", "defaultValue": null}], "startDateType": "FIXED"}, "account": {"id": "ACCT-RF8BD0Z", "name": "ServiceNow", "legalName": null, "description": null, "phoneNumber": "******-501-8550", "timezone": null, "crmId": "0018B00000FWkZeQAL", "crmType": "SALESFORCE", "erpId": null, "externalId": null, "currency": "USD", "taxExemptionUseCode": null, "isReseller": false, "hasAutomaticPayment": false, "excludeFromBatchOperations": false, "excludeFromDunning": false, "supportedPaymentTypes": ["CHECK", "CARD", "ACH"], "address": {"streetAddressLine1": "900 Front St", "streetAddressLine2": "", "city": "San Francisco", "state": "CA", "country": "US", "zipcode": "94111-1427"}, "updatedOn": **********, "customFields": {"CF-BG22D20E": {"type": "STRING", "name": "Account_hometown", "label": "Account_hometown", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-BCPB93Z2": {"type": "STRING", "name": "Payment_Methods", "label": "Payment_Methods", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-9FEPNP17": {"type": "STRING", "name": "Customer_Segment", "label": "Customer_Segment", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-G203ZY52": {"type": "PICKLIST", "name": "testfield", "label": "testfield", "value": null, "selections": [], "options": ["v1", "v2", "v3"], "required": false, "source": "USER", "defaultValue": null}, "CF-BYCZREDV": {"type": "STRING", "name": "Company_ARR", "label": "Company ARR", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-M1VZX69E": {"type": "STRING", "name": "Account_Category", "label": "Default Account Category", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "Mid Size", "selections": null}}, "CF-93Q6QDQT": {"type": "PICKLIST", "name": "Legal_Approval_Required", "label": "Legal Approval Required", "value": null, "selections": [], "options": ["Yes", "No"], "required": false, "source": "USER", "defaultValue": null}, "CF-VCE6MC59": {"type": "STRING", "name": "common_name", "label": "Common Name", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-EJCBD9FN": {"type": "PICKLIST", "name": "test_field", "label": "Testing", "value": null, "selections": [], "options": ["pick1", "pick2"], "required": false, "source": "USER", "defaultValue": null}, "CF-4TKV3NMX": {"type": "MULTISELECT_PICKLIST", "name": "MultiSelectPickListForAccount", "label": "MultiSelectPickListForAccount", "value": null, "selections": [], "options": ["MS1", "MS2"], "required": false, "source": "USER", "defaultValue": null}}, "entityIds": ["*"]}, "productByProductId": {"PROD-E7X2ZT0": {"id": "PROD-E7X2ZT0", "entityIds": ["*"], "name": "Uber - all inclusive", "displayName": null, "inUse": true, "description": "All inclusive plan", "sku": "UBER-001", "productCategoryId": "PCAT-ME60K3C", "productCategory": {"productCategoryId": "PCAT-ME60K3C", "entityIds": ["*"], "name": "<PERSON>’s Categories", "description": "🤔", "inUse": false, "updatedOn": **********, "pkId": "5b12fd8e-c27d-48a6-a3e3-a01a08da5935"}, "updatedOn": **********, "externalId": null}}, "planByPlanId": {"PLAN-Z2BBRDE": {"id": "PLAN-Z2BBRDE", "entityIds": ["*"], "name": "Uber - Custom app", "displayName": null, "description": "Custom app instances as a % of  All inclusive platform", "status": "ACTIVE", "productId": "PROD-E7X2ZT0", "charges": [{"id": "CHRG-VQQVEZR", "name": "Custom app instance (% of per unit)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-Z2BBRDE", "amount": null, "type": "PERCENTAGE_OF", "chargeModel": "PER_UNIT", "recurrence": null, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": 5.0, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}], "currency": "USD", "externalId": null, "templateIds": [], "replacementPlanIds": [], "customFields": {"CF-YJPWP7RN": {"type": "STRING", "name": "segment", "label": "Segment", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-DCJG1DFP": {"type": "STRING", "name": "plan_custom_field_text", "label": "plan_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-M1JHZ06Z": {"type": "PICKLIST", "name": "plan_custom_field_picklist", "label": "plan_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-237WKBPZ": {"type": "PICKLIST", "name": "deposit_amount", "label": "deposit_amount", "value": null, "selections": [], "options": ["10000", "20000", "27500", "50000"], "required": false, "source": "USER", "defaultValue": null}, "CF-1H0B09NB": {"type": "PICKLIST", "name": "Tier", "label": "Tier", "value": null, "selections": [], "options": ["Same"], "required": false, "source": "USER", "defaultValue": null}}, "updatedOn": 1663953305}, "PLAN-TD9PHP5": {"id": "PLAN-TD9PHP5", "entityIds": ["*"], "name": "Uber - All inclusive support", "displayName": null, "description": "Uber all inclusive support plan", "status": "ACTIVE", "productId": "PROD-E7X2ZT0", "charges": [{"id": "CHRG-NMH2KWN", "name": "Support fee (% of flat fee)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": "RCRL-FJ7WP1J", "erpId": null, "itemCode": null, "planId": "PLAN-TD9PHP5", "amount": null, "type": "PERCENTAGE_OF", "chargeModel": "FLAT_FEE", "recurrence": null, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": 10.0, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}], "currency": "USD", "externalId": null, "templateIds": [], "replacementPlanIds": [], "customFields": {"CF-YJPWP7RN": {"type": "STRING", "name": "segment", "label": "Segment", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-DCJG1DFP": {"type": "STRING", "name": "plan_custom_field_text", "label": "plan_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-M1JHZ06Z": {"type": "PICKLIST", "name": "plan_custom_field_picklist", "label": "plan_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-237WKBPZ": {"type": "PICKLIST", "name": "deposit_amount", "label": "deposit_amount", "value": null, "selections": [], "options": ["10000", "20000", "27500", "50000"], "required": false, "source": "USER", "defaultValue": null}, "CF-1H0B09NB": {"type": "PICKLIST", "name": "Tier", "label": "Tier", "value": null, "selections": [], "options": ["Same"], "required": false, "source": "USER", "defaultValue": null}}, "updatedOn": **********}, "PLAN-ZHYZMTM": {"id": "PLAN-ZHYZMTM", "entityIds": ["*"], "name": "Uber - All inclusive platform", "displayName": null, "description": "Includes everything", "status": "ACTIVE", "productId": "PROD-E7X2ZT0", "charges": [{"id": "CHRG-X16H072", "name": "Sandbox accounts (one time per unit)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": 3000.0, "type": "ONE_TIME", "chargeModel": "PER_UNIT", "recurrence": null, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "customFields": {}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, {"id": "CHRG-WD06ZP6", "name": "Graduated data storage (recurring, tiered)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": "7d5010e0-9afe-4277-af43-678f590dc03b", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": null, "type": "RECURRING", "chargeModel": "TIERED", "recurrence": {"cycle": "YEAR", "step": 1}, "priceTiers": [{"untilQuantity": "1000", "amount": 0.05, "overage": null}, {"untilQuantity": "2000", "amount": 0.045, "overage": null}, {"untilQuantity": "5000", "amount": 0.04, "overage": null}, {"untilQuantity": "INF", "amount": 0.038, "overage": null}], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, {"id": "CHRG-4BQM09Q", "name": "Per user fee (recurring, per unit)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": "34ee4ff7-baca-4f9e-92ae-8ea1a8360d0c", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": 100.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "YEAR", "step": 1}, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, {"id": "CHRG-H6ZQ42B", "name": "Precommited volume (recurring, volume discount)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": "7d5010e0-9afe-4277-af43-678f590dc03b", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": null, "type": "RECURRING", "chargeModel": "VOLUME", "recurrence": {"cycle": "YEAR", "step": 1}, "priceTiers": [{"untilQuantity": "100", "amount": 10.0, "overage": null}, {"untilQuantity": "300", "amount": 9, "overage": null}, {"untilQuantity": "INF", "amount": 8, "overage": null}], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, {"id": "CHRG-NK5Q5QJ", "name": "Block of nodes (recurring, block)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": null, "type": "RECURRING", "chargeModel": "BLOCK", "recurrence": {"cycle": "YEAR", "step": 1}, "priceTiers": [{"untilQuantity": "100", "amount": 120.0, "overage": null}, {"untilQuantity": "200", "amount": 200.0, "overage": null}, {"untilQuantity": "500", "amount": 400.0, "overage": null}, {"untilQuantity": "INF", "amount": 600.0, "overage": null}], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, {"id": "CHRG-5FWC94V", "name": "Implementation fee (one time, flat custom)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": null, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "recurrence": null, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": true, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "customFields": {}, "drawdown": false, "custom": true, "eventBased": false, "creditable": false}, {"id": "CHRG-KTBXQ1K", "name": "Setup fee (one time, flat fee)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": 5000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "recurrence": null, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "customFields": {}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, {"id": "CHRG-1K6JBET", "name": "API requests (usage, block)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": "decff709-3a7b-4800-a5b1-3e3cd69074c6", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": null, "type": "USAGE", "chargeModel": "BLOCK", "recurrence": {"cycle": "MONTH", "step": 1}, "priceTiers": [{"untilQuantity": "100000", "amount": 1500.0, "overage": null}, {"untilQuantity": "500000", "amount": 4000.0, "overage": null}, {"untilQuantity": "INF", "amount": 10000.0, "overage": null}], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "IN_ARREARS", "billingCycle": "DEFAULT", "shouldTrackArr": false, "customFields": {}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}], "currency": "USD", "externalId": null, "templateIds": [], "replacementPlanIds": [], "customFields": {"CF-YJPWP7RN": {"type": "STRING", "name": "segment", "label": "Segment", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-DCJG1DFP": {"type": "STRING", "name": "plan_custom_field_text", "label": "plan_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-M1JHZ06Z": {"type": "PICKLIST", "name": "plan_custom_field_picklist", "label": "plan_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-237WKBPZ": {"type": "PICKLIST", "name": "deposit_amount", "label": "deposit_amount", "value": null, "selections": [], "options": ["10000", "20000", "27500", "50000"], "required": false, "source": "USER", "defaultValue": null}, "CF-1H0B09NB": {"type": "PICKLIST", "name": "Tier", "label": "Tier", "value": null, "selections": [], "options": ["Same"], "required": false, "source": "USER", "defaultValue": null}}, "updatedOn": 1663953740}}, "chargeByChargeId": {"CHRG-KTBXQ1K": {"id": "CHRG-KTBXQ1K", "name": "Setup fee (one time, flat fee)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": 5000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "recurrence": null, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "123", "selections": null}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "value": null, "selections": [], "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER", "defaultValue": null}}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, "CHRG-NMH2KWN": {"id": "CHRG-NMH2KWN", "name": "Support fee (% of flat fee)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": "RCRL-FJ7WP1J", "erpId": null, "itemCode": null, "planId": "PLAN-TD9PHP5", "amount": null, "type": "PERCENTAGE_OF", "chargeModel": "FLAT_FEE", "recurrence": null, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": 10.0, "percentDerivedFrom": null, "targetPlanIds": ["PLAN-Z2BBRDE", "PLAN-ZHYZMTM"], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "123", "selections": null}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "value": null, "selections": [], "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER", "defaultValue": null}}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, "CHRG-5FWC94V": {"id": "CHRG-5FWC94V", "name": "Implementation fee (one time, flat custom)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": null, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "recurrence": null, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": true, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "123", "selections": null}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "value": null, "selections": [], "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER", "defaultValue": null}}, "drawdown": false, "custom": true, "eventBased": false, "creditable": false}, "CHRG-WD06ZP6": {"id": "CHRG-WD06ZP6", "name": "Graduated data storage (recurring, tiered)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": "7d5010e0-9afe-4277-af43-678f590dc03b", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": null, "type": "RECURRING", "chargeModel": "TIERED", "recurrence": {"cycle": "YEAR", "step": 1}, "priceTiers": [{"untilQuantity": "1000", "amount": 0.05, "overage": null}, {"untilQuantity": "2000", "amount": 0.045, "overage": null}, {"untilQuantity": "5000", "amount": 0.04, "overage": null}, {"untilQuantity": "INF", "amount": 0.038, "overage": null}], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "123", "selections": null}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "value": null, "selections": [], "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER", "defaultValue": null}}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, "CHRG-4BQM09Q": {"id": "CHRG-4BQM09Q", "name": "Per user fee (recurring, per unit)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": "34ee4ff7-baca-4f9e-92ae-8ea1a8360d0c", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": 100.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "YEAR", "step": 1}, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "123", "selections": null}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "value": null, "selections": [], "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER", "defaultValue": null}}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, "CHRG-NK5Q5QJ": {"id": "CHRG-NK5Q5QJ", "name": "Block of nodes (recurring, block)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": null, "type": "RECURRING", "chargeModel": "BLOCK", "recurrence": {"cycle": "YEAR", "step": 1}, "priceTiers": [{"untilQuantity": "100", "amount": 120.0, "overage": null}, {"untilQuantity": "200", "amount": 200.0, "overage": null}, {"untilQuantity": "500", "amount": 400.0, "overage": null}, {"untilQuantity": "INF", "amount": 600.0, "overage": null}], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "123", "selections": null}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "value": null, "selections": [], "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER", "defaultValue": null}}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, "CHRG-H6ZQ42B": {"id": "CHRG-H6ZQ42B", "name": "Precommited volume (recurring, volume discount)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": "7d5010e0-9afe-4277-af43-678f590dc03b", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": null, "type": "RECURRING", "chargeModel": "VOLUME", "recurrence": {"cycle": "YEAR", "step": 1}, "priceTiers": [{"untilQuantity": "100", "amount": 10.0, "overage": null}, {"untilQuantity": "300", "amount": 9, "overage": null}, {"untilQuantity": "INF", "amount": 8, "overage": null}], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "123", "selections": null}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "value": null, "selections": [], "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER", "defaultValue": null}}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, "CHRG-VQQVEZR": {"id": "CHRG-VQQVEZR", "name": "Custom app instance (% of per unit)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-Z2BBRDE", "amount": null, "type": "PERCENTAGE_OF", "chargeModel": "PER_UNIT", "recurrence": null, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": 5.0, "percentDerivedFrom": null, "targetPlanIds": ["PLAN-ZHYZMTM"], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "123", "selections": null}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "value": null, "selections": [], "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER", "defaultValue": null}}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, "CHRG-1K6JBET": {"id": "CHRG-1K6JBET", "name": "API requests (usage, block)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": "decff709-3a7b-4800-a5b1-3e3cd69074c6", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": null, "type": "USAGE", "chargeModel": "BLOCK", "recurrence": {"cycle": "MONTH", "step": 1}, "priceTiers": [{"untilQuantity": "100000", "amount": 1500.0, "overage": null}, {"untilQuantity": "500000", "amount": 4000.0, "overage": null}, {"untilQuantity": "INF", "amount": 10000.0, "overage": null}], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "IN_ARREARS", "billingCycle": "DEFAULT", "shouldTrackArr": false, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "123", "selections": null}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "value": null, "selections": [], "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER", "defaultValue": null}}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}, "CHRG-X16H072": {"id": "CHRG-X16H072", "name": "Sandbox accounts (one time per unit)", "displayName": null, "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZHYZMTM", "amount": 3000.0, "type": "ONE_TIME", "chargeModel": "PER_UNIT", "recurrence": null, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "value": null, "selections": [], "options": ["A", "B", "C"], "required": false, "source": "USER", "defaultValue": null}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "123", "selections": null}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "value": null, "selections": [], "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER", "defaultValue": null}}, "drawdown": false, "custom": false, "eventBased": false, "creditable": false}}, "contactByContactId": {"CONT-EJXQ3B6": {"id": "CONT-EJXQ3B6", "accountId": "ACCT-RF8BD0Z", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "prakash+ab<PERSON><PERSON><PERSON>@subskribe.com", "phoneNumber": "************", "title": null, "address": {"streetAddressLine1": "900 Front St", "streetAddressLine2": "", "city": "San Francisco", "state": "CA", "country": "US", "zipcode": "94111-1427"}, "externalId": null, "erpId": null, "fullName": "<PERSON>"}}, "productCategoryById": {"PCAT-ME60K3C": {"productCategoryId": "PCAT-ME60K3C", "entityIds": ["*"], "name": "<PERSON>’s Categories", "description": "🤔", "inUse": false, "updatedOn": **********, "pkId": "5b12fd8e-c27d-48a6-a3e3-a01a08da5935"}}, "priceAttributesById": {}, "predefinedTermByName": {"Credit Expiry": "<p>The unused credit will expire on the contract end date. The customer will not get any refund, or the credit will not be transferred to any new contract.</p>", "Educational ": "<h1>Terms &amp; Conditions</h1><ol><li>Partial refunds</li><li>Order starts on Aug 1, 2022 and ends on Jul 31, 2025</li><li>No exchanges</li><li>Quote is valid for 15 business days</li><li>Additional integration costs will be charged at $150 / hr</li><li>You will be sent an invoice Yearly.</li></ol><h3>Service Level Agreements</h3><ol><li>We provide 99.9% uptime guarantee</li><li>SLA details can be found <a href=\"https://google.com\" rel=\"nofollow\">here</a>.</li><li>Certain planned maintenance windows will be communicated at least 24 hours in advance</li></ol>"}}