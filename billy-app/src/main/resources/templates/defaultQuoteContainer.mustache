<html
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:w="urn:schemas-microsoft-com:office:word"
  xmlns="http://www.w3.org/TR/REC-html40"
>
  <head>
    <title>Quote</title>
    <meta charset="UTF-8">
    <script src="paged.polyfill.js"></script>
    <script>
      const LOCALE = "en-US";
      const CURRENCY = "USD";
      const CURRENCY_FORMATTER = new Intl.NumberFormat(LOCALE, {
        style: "currency",
        currency: CURRENCY,
      });

      function roundToTwoDecimal(number) {
        return Math.round((number + Number.EPSILON) * 100) / 100;
      }

      function localizeNumber(number) {
        return number.toLocaleString(LOCALE, { minimumFractionDigits: 2 });
      }

      function formatNumber(number) {
        return localizeNumber(roundToTwoDecimal(number));
      }

      function formatPercent(number) {
        return formatNumber(number * 100) + "%";
      }

      function formatAmount(number) {
        return CURRENCY_FORMATTER.format(number);
      }

      function formatPercentValues() {
        const elements = document.querySelectorAll(".format-percent");
        elements.forEach((element) => {
          const originalValue = parseFloat(element.textContent);
          element.textContent = formatPercent(originalValue);
        });
      }

      function formatAmounts() {
        const elements = document.querySelectorAll(".format-amount");
        elements.forEach((element) => {
          const originalValue = parseFloat(element.textContent);
          element.textContent = formatAmount(originalValue);
        });
      }

      formatPercentValues();
      formatAmounts();
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400&display=swap"
      rel="stylesheet"
    />
    <style>
      body {
        margin: 0;
        font-family: {{{configuration.fontFamily}}};
      }

      body,
      table {
        font-weight: 300;
        font-size: {{{configuration.fontSize}}};
      }

      .header {
        margin-top: 1em;
      }

      .brand-name {
        color: {{{configuration.primaryColor}}};
      }

      .bold {
        font-weight: bold;
      }

      table {
        flex: 1;
        width: 100%;
      }

      caption,
      th,
      td {
        text-align: left;
      }
      .align-left {
        text-align: left;
      }
      .align-right {
        text-align: right;
      }
      .align-center {
        text-align: center;
      }

      .padding-left-2 {
        padding-left: 2em;
      }

      tr.align-center td,
      tr.align-center th {
        text-align: center;
      }

      .line-items-table {
        text-align: left;
        border-collapse: collapse;
      }

      .contact-table {
        text-align: left;
        vertical-align: top;
        border-collapse: collapse;
      }
      .line-items-table-caption-date-interval {
        font-weight: bold;
        margin: 0.25em 0 0.5em;
        text-align: left;
      }

      .line-items-table-head {
        padding-bottom: 4pt;
      }

      .line-items-table-body {
        line-height: 1.25rem;
      }

      .avoid-breaks {
        page-break-inside: avoid;
        page-break-after: avoid;
      }

      .contact-table-head {
        padding-bottom: 1px solid;
      }

      .contact-item {
        padding: 0px;
        margin: 0px;
        vertical-align: top;
      }

      .table-number {
        padding-left: 1em;
        text-align: right;
      }

      .total {
        text-align: right;
      }

      .amount-due-label {
        text-align: right;
        padding-right: 1rem;
        font-weight: bold;
      }

      .subTotal-label {
        text-align: right;
        padding-right: 1rem;
      }

      .total-label {
        text-align: right;
        padding-right: 1rem;
        font-weight: bold;
      }

      .taxTotal-label {
        text-align: right;
        padding-right: 1rem;
      }

      .section-header {
        font-weight: bold;
        border-bottom: solid 2px {{{configuration.primaryColor}}};
        padding-bottom: 4pt;
      }

      label {
        padding-right: 1em;
      }

      .draft-watermark {
        margin-top: 0;
        margin-bottom: 0;
        color: #d6dde4;
      }

      .indented {
        text-indent: 3em;
      }

      .logo {
        max-height: 5em;
        width: auto;
      }

      .signature-section {
        break-inside: avoid;
        width: 100%;
        font-size: 8pt;
      }

      .provider-gap-block {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: start;

        border-bottom: 1px solid #000;
        height: 60px;
        font-size: 16px;
        padding-top: 10px;
      }

      .provider-gap-block--signature {
        height: 60px;
      }

      .signature-anchor-text {
        color: white;
        text-align: center;
        display: flex;
      }

      .alpha-table {
        border-spacing: 0;
        border-collapse: collapse;
        margin-right: auto;
      }

      .alpha-tr {
        height: 12pt;
      }

      .alpha-td {
        border-right-style: solid;
        padding: 5pt 5pt 5pt 5pt;
        border-bottom-color: #000000;
        border-top-width: 1pt;
        border-right-width: 1pt;
        border-left-color: #000000;
        vertical-align: center;
        text-align: center;
        border-right-color: #000000;
        border-left-width: 1pt;
        border-top-style: solid;
        border-left-style: solid;
        border-bottom-width: 1pt;
        border-top-color: #000000;
        border-bottom-style: solid;
      }

      .alpha-table-text {
        color: #000000;
        text-decoration: none;
        vertical-align: baseline;
        font-style: normal;

        padding-top: 0pt;
        padding-bottom: 0pt;
        line-height: 1;
        text-align: center;
      }

      .alpha-table-body-text {
        font-weight: 400;
      }

      .alpha-table-head-text {
        font-weight: 700;
      }

      .alpha-table-head {
        background-color: {{{configuration.secondaryColor}}};
      }

      /* PDF page - styles and settings */
      @media print {
        @page {
          margin: 0.39in;
          width: 8.5in;
          height: 11in;
          @top-right {
            content: ' ';
            background-color: {{{configuration.primaryColor}}};
          }
          @top-left-corner {
            content: ' ';
            background-color: {{{configuration.primaryColor}}};
          }
          @top-right-corner {
            content: ' ';
            background-color: {{{configuration.primaryColor}}};
          }
          @bottom-right {
            content: 'Page ' counter(page) ' of ' counter(pages);
            font-size: 12px;
          }
        }
      }

      /* WYSIWYG (quill) editor specific styles */

      .terms-section-container {
        line-height: 1.42;
        outline: none;
        padding-top: 12px;
        tab-size: 4;
        -moz-tab-size: 4;
        text-align: left;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
      .terms-section-container p,
      .terms-section-container ol,
      .terms-section-container ul,
      .terms-section-container pre,
      .terms-section-container blockquote,
      .terms-section-container h1,
      .terms-section-container h2,
      .terms-section-container h3,
      .terms-section-container h4,
      .terms-section-container h5,
      .terms-section-container h6 {
        margin: 0;
        padding: 0;
        counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
      }
      .terms-section-container ol,
      .terms-section-container ul {
        padding-left: 1.5em;
      }
      .terms-section-container ol > li,
      .terms-section-container ul > li {
        list-style-type: none;
      }
      .terms-section-container ul > li::before {
        content: '\2022';
      }
      .terms-section-container ul[data-checked='true'],
      .terms-section-container ul[data-checked='false'] {
        pointer-events: none;
      }
      .terms-section-container ul[data-checked='true'] > li *,
      .terms-section-container ul[data-checked='false'] > li * {
        pointer-events: all;
      }
      .terms-section-container ul[data-checked='true'] > li::before,
      .terms-section-container ul[data-checked='false'] > li::before {
        color: #777;
        cursor: pointer;
        pointer-events: all;
      }
      .terms-section-container ul[data-checked='true'] > li::before {
        content: '\2611';
      }
      .terms-section-container ul[data-checked='false'] > li::before {
        content: '\2610';
      }
      .terms-section-container li::before {
        display: inline-block;
        white-space: nowrap;
        width: 1.2em;
      }
      .terms-section-container li:not(.ql-direction-rtl)::before {
        margin-left: -1.5em;
        margin-right: 0.3em;
        text-align: right;
      }
      .terms-section-container li.ql-direction-rtl::before {
        margin-left: 0.3em;
        margin-right: -1.5em;
      }
      .terms-section-container ol li:not(.ql-direction-rtl),
      .terms-section-container ul li:not(.ql-direction-rtl) {
        padding-left: 1.5em;
      }
      .terms-section-container ol li.ql-direction-rtl,
      .terms-section-container ul li.ql-direction-rtl {
        padding-right: 1.5em;
      }
      .terms-section-container ol li {
        counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
        counter-increment: list-0;
      }
      .terms-section-container ol li:before {
        content: counter(list-0, decimal) '. ';
      }
      .terms-section-container ol li.ql-indent-1 {
        counter-increment: list-1;
      }
      .terms-section-container ol li.ql-indent-1:before {
        content: counter(list-1, lower-alpha) '. ';
      }
      .terms-section-container ol li.ql-indent-1 {
        counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
      }
      .terms-section-container ol li.ql-indent-2 {
        counter-increment: list-2;
      }
      .terms-section-container ol li.ql-indent-2:before {
        content: counter(list-2, lower-roman) '. ';
      }
      .terms-section-container ol li.ql-indent-2 {
        counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;
      }
      .terms-section-container ol li.ql-indent-3 {
        counter-increment: list-3;
      }
      .terms-section-container ol li.ql-indent-3:before {
        content: counter(list-3, decimal) '. ';
      }
      .terms-section-container ol li.ql-indent-3 {
        counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;
      }
      .terms-section-container ol li.ql-indent-4 {
        counter-increment: list-4;
      }
      .terms-section-container ol li.ql-indent-4:before {
        content: counter(list-4, lower-alpha) '. ';
      }
      .terms-section-container ol li.ql-indent-4 {
        counter-reset: list-5 list-6 list-7 list-8 list-9;
      }
      .terms-section-container ol li.ql-indent-5 {
        counter-increment: list-5;
      }
      .terms-section-container ol li.ql-indent-5:before {
        content: counter(list-5, lower-roman) '. ';
      }
      .terms-section-container ol li.ql-indent-5 {
        counter-reset: list-6 list-7 list-8 list-9;
      }
      .terms-section-container ol li.ql-indent-6 {
        counter-increment: list-6;
      }
      .terms-section-container ol li.ql-indent-6:before {
        content: counter(list-6, decimal) '. ';
      }
      .terms-section-container ol li.ql-indent-6 {
        counter-reset: list-7 list-8 list-9;
      }
      .terms-section-container ol li.ql-indent-7 {
        counter-increment: list-7;
      }
      .terms-section-container ol li.ql-indent-7:before {
        content: counter(list-7, lower-alpha) '. ';
      }
      .terms-section-container ol li.ql-indent-7 {
        counter-reset: list-8 list-9;
      }
      .terms-section-container ol li.ql-indent-8 {
        counter-increment: list-8;
      }
      .terms-section-container ol li.ql-indent-8:before {
        content: counter(list-8, lower-roman) '. ';
      }
      .terms-section-container ol li.ql-indent-8 {
        counter-reset: list-9;
      }
      .terms-section-container ol li.ql-indent-9 {
        counter-increment: list-9;
      }
      .terms-section-container ol li.ql-indent-9:before {
        content: counter(list-9, decimal) '. ';
      }
      .terms-section-container .ql-indent-1:not(.ql-direction-rtl) {
        padding-left: 3em;
      }
      .terms-section-container li.ql-indent-1:not(.ql-direction-rtl) {
        padding-left: 4.5em;
      }
      .terms-section-container .ql-indent-1.ql-direction-rtl.ql-align-right {
        padding-right: 3em;
      }
      .terms-section-container li.ql-indent-1.ql-direction-rtl.ql-align-right {
        padding-right: 4.5em;
      }
      .terms-section-container .ql-indent-2:not(.ql-direction-rtl) {
        padding-left: 6em;
      }
      .terms-section-container li.ql-indent-2:not(.ql-direction-rtl) {
        padding-left: 7.5em;
      }
      .terms-section-container .ql-indent-2.ql-direction-rtl.ql-align-right {
        padding-right: 6em;
      }
      .terms-section-container li.ql-indent-2.ql-direction-rtl.ql-align-right {
        padding-right: 7.5em;
      }
      .terms-section-container .ql-indent-3:not(.ql-direction-rtl) {
        padding-left: 9em;
      }
      .terms-section-container li.ql-indent-3:not(.ql-direction-rtl) {
        padding-left: 10.5em;
      }
      .terms-section-container .ql-indent-3.ql-direction-rtl.ql-align-right {
        padding-right: 9em;
      }
      .terms-section-container li.ql-indent-3.ql-direction-rtl.ql-align-right {
        padding-right: 10.5em;
      }
      .terms-section-container .ql-indent-4:not(.ql-direction-rtl) {
        padding-left: 12em;
      }
      .terms-section-container li.ql-indent-4:not(.ql-direction-rtl) {
        padding-left: 13.5em;
      }
      .terms-section-container .ql-indent-4.ql-direction-rtl.ql-align-right {
        padding-right: 12em;
      }
      .terms-section-container li.ql-indent-4.ql-direction-rtl.ql-align-right {
        padding-right: 13.5em;
      }
      .terms-section-container .ql-indent-5:not(.ql-direction-rtl) {
        padding-left: 15em;
      }
      .terms-section-container li.ql-indent-5:not(.ql-direction-rtl) {
        padding-left: 16.5em;
      }
      .terms-section-container .ql-indent-5.ql-direction-rtl.ql-align-right {
        padding-right: 15em;
      }
      .terms-section-container li.ql-indent-5.ql-direction-rtl.ql-align-right {
        padding-right: 16.5em;
      }
      .terms-section-container .ql-indent-6:not(.ql-direction-rtl) {
        padding-left: 18em;
      }
      .terms-section-container li.ql-indent-6:not(.ql-direction-rtl) {
        padding-left: 19.5em;
      }
      .terms-section-container .ql-indent-6.ql-direction-rtl.ql-align-right {
        padding-right: 18em;
      }
      .terms-section-container li.ql-indent-6.ql-direction-rtl.ql-align-right {
        padding-right: 19.5em;
      }
      .terms-section-container .ql-indent-7:not(.ql-direction-rtl) {
        padding-left: 21em;
      }
      .terms-section-container li.ql-indent-7:not(.ql-direction-rtl) {
        padding-left: 22.5em;
      }
      .terms-section-container .ql-indent-7.ql-direction-rtl.ql-align-right {
        padding-right: 21em;
      }
      .terms-section-container li.ql-indent-7.ql-direction-rtl.ql-align-right {
        padding-right: 22.5em;
      }
      .terms-section-container .ql-indent-8:not(.ql-direction-rtl) {
        padding-left: 24em;
      }
      .terms-section-container li.ql-indent-8:not(.ql-direction-rtl) {
        padding-left: 25.5em;
      }
      .terms-section-container .ql-indent-8.ql-direction-rtl.ql-align-right {
        padding-right: 24em;
      }
      .terms-section-container li.ql-indent-8.ql-direction-rtl.ql-align-right {
        padding-right: 25.5em;
      }
      .terms-section-container .ql-indent-9:not(.ql-direction-rtl) {
        padding-left: 27em;
      }
      .terms-section-container li.ql-indent-9:not(.ql-direction-rtl) {
        padding-left: 28.5em;
      }
      .terms-section-container .ql-indent-9.ql-direction-rtl.ql-align-right {
        padding-right: 27em;
      }
      .terms-section-container li.ql-indent-9.ql-direction-rtl.ql-align-right {
        padding-right: 28.5em;
      }
      .terms-section-container .ql-video {
        display: block;
        max-width: 100%;
      }
      .terms-section-container .ql-video.ql-align-center {
        margin: 0 auto;
      }
      .terms-section-container .ql-video.ql-align-right {
        margin: 0 0 0 auto;
      }
      .terms-section-container .ql-bg-black {
        background-color: #000;
      }
      .terms-section-container .ql-bg-red {
        background-color: #e60000;
      }
      .terms-section-container .ql-bg-orange {
        background-color: #f90;
      }
      .terms-section-container .ql-bg-yellow {
        background-color: #ff0;
      }
      .terms-section-container .ql-bg-green {
        background-color: #008a00;
      }
      .terms-section-container .ql-bg-blue {
        background-color: #06c;
      }
      .terms-section-container .ql-bg-purple {
        background-color: #93f;
      }
      .terms-section-container .ql-color-white {
        color: #fff;
      }
      .terms-section-container .ql-color-red {
        color: #e60000;
      }
      .terms-section-container .ql-color-orange {
        color: #f90;
      }
      .terms-section-container .ql-color-yellow {
        color: #ff0;
      }
      .terms-section-container .ql-color-green {
        color: #008a00;
      }
      .terms-section-container .ql-color-blue {
        color: #06c;
      }
      .terms-section-container .ql-color-purple {
        color: #93f;
      }
      .terms-section-container .ql-font-serif {
        font-family: Georgia, Times New Roman, serif;
      }
      .terms-section-container .ql-font-monospace {
        font-family: Monaco, Courier New, monospace;
      }
      .terms-section-container .ql-size-small {
        font-size: 0.75em;
      }
      .terms-section-container .ql-size-large {
        font-size: 1.5em;
      }
      .terms-section-container .ql-size-huge {
        font-size: 2.5em;
      }
      .terms-section-container .ql-direction-rtl {
        direction: rtl;
        text-align: inherit;
      }
      .terms-section-container .ql-align-center {
        text-align: center;
      }
      .terms-section-container .ql-align-justify {
        text-align: justify;
      }
      .terms-section-container .ql-align-right {
        text-align: right;
      }
      .terms-section-container.ql-blank::before {
        color: rgba(0, 0, 0, 0.6);
        content: attr(data-placeholder);
        font-style: italic;
        left: 15px;
        pointer-events: none;
        position: absolute;
        right: 15px;
      }
      .mce-table td,
      .mce-table th {
        padding: 5pt;
        border: 1px solid #000000;
      }
    </style>
  </head>

  <body>
    {{{contentBody}}}
  </body>
</html>
