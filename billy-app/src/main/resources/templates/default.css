body {
  font-family: "Helvetica Neue", sans-serif;
  font-size: 12px;
}

table {
  flex: 1;
  width: 100%;
  font-size: 12px;
}

caption, th, td {
  text-align: left;
}

.line-items-table {
  text-align: left;
  border-collapse: collapse;
}

.line-items-table-caption-date-interval {
  font-size: 1.25em;
  font-weight: bold;
  margin: 0.25em 0 0.5em;
  text-align: left;
}

.line-items-table-head {
  border-bottom: 1px solid;
}

.line-items-table-body {
  line-height: 1.25rem;
}

.table-number {
  text-align: right;
}

.total {
  text-align: right;
}

.amount-due-label {
  text-align: right;
  padding-right: 1rem;
  font-weight: bold;
}

.subTotal-label {
  text-align: right;
  padding-right: 1rem;
}

.total-label {
  text-align: right;
  padding-right: 1rem;
  font-weight: bold;
}

.taxTotal-label {
  text-align: right;
  padding-right: 1rem;
}

.section-header {
  background-color: #595959;
  color: white;
  text-align: center;
  font-weight: bold;
  margin: 1em 0;
}

label {
  padding-right: 1em;
}

.draft-watermark {
  color: #D6DDE4;
}

.nowrap {
  white-space: nowrap;
}

.indented {
  text-indent: 3em;
}

.logo {
  max-height: 5em;
  width: auto;
}

.signature-section {
  border-collapse: collapse;
  break-inside: avoid;
  width: 100%;
}

.signature-block {
  display: flex;
  padding: 8px 0;
}

.signature-anchor-text {
  display: inline;
  width: 100%;
  margin: 0 4px;
  color: white;
  text-align: center;
  border-bottom: 1px solid #000;
}

/* WYSIWYG (quill) editor specific styles */

.ql-align-center {
  text-align: center;
}

.ql-align-right {
  text-align: right;
}

.ql-align-justify {
  text-align: justify;
}

.ql-indent-1 {
  padding-left: 3em;
}

.ql-indent-2 {
  padding-left: 6em;
}

.ql-indent-3 {
  padding-left: 9em;
}

.ql-indent-4 {
  padding-left: 12em;
}

.ql-indent-5 {
  padding-left: 15em;
}

.ql-indent-6 {
  padding-left: 18em;
}

.ql-indent-7 {
  padding-left: 21em;
}

.ql-indent-8 {
  padding-left: 24em;
}
