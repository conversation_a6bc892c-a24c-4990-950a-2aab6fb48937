<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word"
  xmlns="http://www.w3.org/TR/REC-html40">

<head>
  <title>Quote</title>
  <meta charset="UTF-8">
  <script>
    window.addEventListener(
      'error',
      function (e) {
        const ele = e.target
        const url = ele.tagName === 'LINK' ? ele.href : ele.src
        console.error(url + ' failed loading.')
        window.stop()
      },
      true
    )
  </script>
  <script src="paged.polyfill.js"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400&display=swap" rel="stylesheet" />

  <style>
    body {
      margin: 0;
      font-family: 'Roboto Slab', serif;
    }

    body,
    table {
      font-weight: 300;
      font-size: 8pt;
    }

    .header {
      margin-top: 1em;
    }

    .brand-name {
      color: #5350ed;
    }

    .bold {
      font-weight: bold;
    }

    table {
      flex: 1;
      width: 100%;
    }

    caption,
    th,
    td {
      text-align: left;
    }

    .align-left {
      text-align: left;
    }

    .align-right {
      text-align: right;
    }

    .align-center {
      text-align: center;
    }

    .padding-left-2 {
      padding-left: 2em;
    }

    tr.align-center td,
    tr.align-center th {
      text-align: center;
    }

    .line-items-table {
      text-align: left;
      border-collapse: collapse;
    }

    .contact-table {
      text-align: left;
      vertical-align: top;
      border-collapse: collapse;
    }

    .line-items-table-caption-date-interval {
      font-weight: bold;
      margin: 0.25em 0 0.5em;
      text-align: left;
    }

    .line-items-table-head {
      padding-bottom: 4pt;
    }

    .line-items-table-body {
      line-height: 1.25rem;
    }

    .contact-table-head {
      padding-bottom: 1px solid;
    }

    .contact-item {
      padding: 0px;
      margin: 0px;
      vertical-align: top;
    }

    .table-number {
      padding-left: 1em;
      text-align: right;
    }

    .total {
      text-align: right;
    }

    .amount-due-label {
      text-align: right;
      padding-right: 1rem;
      font-weight: bold;
    }

    .subTotal-label {
      text-align: right;
      padding-right: 1rem;
    }

    .total-label {
      text-align: right;
      padding-right: 1rem;
      font-weight: bold;
    }

    .taxTotal-label {
      text-align: right;
      padding-right: 1rem;
    }

    .section-header {
      font-weight: bold;
      text-transform: uppercase;
      border-bottom: solid 2px #5350ed;
      padding-bottom: 4pt;
    }

    label {
      padding-right: 1em;
    }

    .draft-watermark {
      margin-top: 0;
      margin-bottom: 0;
      color: #d6dde4;
    }

    .indented {
      text-indent: 3em;
    }

    .logo {
      max-height: 5em;
      width: auto;
    }

    .signature-section {
      break-inside: avoid;
      width: 100%;
      font-size: 8pt;
    }

    .provider-gap-block {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: start;

      border-bottom: 1px solid #000;
      height: 60px;
      font-size: 16px;
      padding-top: 10px;
    }

    .provider-gap-block--signature {
      height: 60px;
    }

    .signature-block {
      display: flex;
      padding: 8px 0;
    }

    .signature-anchor-text {
      color: white;
      text-align: center;
      display: flex;
    }

    /* WYSIWYG (quill) editor specific styles */

    .ql-align-center {
      text-align: center;
    }

    .ql-align-right {
      text-align: right;
    }

    .ql-align-justify {
      text-align: justify;
    }

    .ql-indent-1 {
      padding-left: 3em;
    }

    .ql-indent-2 {
      padding-left: 6em;
    }

    .ql-indent-3 {
      padding-left: 9em;
    }

    .ql-indent-4 {
      padding-left: 12em;
    }

    .ql-indent-5 {
      padding-left: 15em;
    }

    .ql-indent-6 {
      padding-left: 18em;
    }

    .ql-indent-7 {
      padding-left: 21em;
    }

    .ql-indent-8 {
      padding-left: 24em;
    }

    .alpha-table {
      border-spacing: 0;
      border-collapse: collapse;
      margin-right: auto;
    }

    .alpha-tr {
      height: 12pt;
    }

    .alpha-td {
      border-right-style: solid;
      padding: 5pt 5pt 5pt 5pt;
      border-bottom-color: #000000;
      border-top-width: 1pt;
      border-right-width: 1pt;
      border-left-color: #000000;
      vertical-align: center;
      text-align: center;
      border-right-color: #000000;
      border-left-width: 1pt;
      border-top-style: solid;
      border-left-style: solid;
      border-bottom-width: 1pt;
      border-top-color: #000000;
      border-bottom-style: solid;
    }

    .alpha-table-text {
      color: #000000;
      text-decoration: none;
      vertical-align: baseline;
      font-style: normal;

      padding-top: 0pt;
      padding-bottom: 0pt;
      line-height: 1;
      text-align: center;
    }

    .alpha-table-body-text {
      font-weight: 400;
    }

    .alpha-table-head-text {
      font-weight: 700;
    }

    .alpha-table-head {
      background-color: #daebfd;
    }

    /* PDF page - styles and settings */
    @media print {
      @page {
        margin: 6mm 10mm 10mm 10mm;
        width: 210mm;
        height: 297mm;

        @top-right {
          content: ' ';
          background-color: #5350ed;
        }

        @top-left-corner {
          content: ' ';
          background-color: #5350ed;
        }

        @top-right-corner {
          content: ' ';
          background-color: #5350ed;
        }

        @bottom-right {
          content: 'Page ' counter(page) ' of ' counter(pages);
          font-size: 12px;
        }
      }
    }
  </style>
</head>

<body>
  <table width="100%">
    <tr>
      <td>
        {{#restructure.base64EncodedLogo}}
        <img src="{{restructure.base64EncodedLogo}}" alt="logo" class="logo" width="100" height="50" />
        <h3 style="margin-top: 0" class="brand-name">{{restructure.tenantName}}</h3>
        {{/restructure.base64EncodedLogo}} {{^restructure.base64EncodedLogo}}
        <h1 style="margin-top: 0">{{restructure.tenantName}}</h1>
        {{/restructure.base64EncodedLogo}}
        <div>{{restructure.tenantPhoneNumber}}</div>
        <div>{{restructure.tenantEmail}}</div>
      </td>
      <td style="text-align: right">
        {{#restructure.isDraft}}
        <h2 class="draft-watermark">DRAFT</h2>
        {{/restructure.isDraft}}
        <div><label>Order ID</label> {{compositeOrderId}}</div>
        {{#restructure.purchaseOrderNumber}}
        <div><label>Purchase Order</label> #{{restructure.purchaseOrderNumber}}</div>
        {{/restructure.purchaseOrderNumber}} {{#restructure.autoRenew}}
        <div><label>Auto Renew</label> &#10003;</div>
        {{/restructure.autoRenew}}
      </td>
    </tr>
  </table>

  <div>
    <h3 class="section-header">Customer Information (Restructure)</h3>
    <div>
      <table class="contact-table">
        <thead class="contact-table-head">
          <tr>
            <th>Bill to</th>
            <th>Ship to</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="contact-item">
              {{restructure.account.name}}<br />
              {{restructure.billingContact.firstName}} {{restructure.billingContact.lastName}}<br />
              {{restructure.billingAddress.streetAddressLine1}}<br />
              {{#restructure.billingAddress.streetAddressLine2}}
              {{restructure.billingAddress.streetAddressLine2}}<br />
              {{/restructure.billingAddress.streetAddressLine2}} {{restructure.billingAddress.city}},
              {{restructure.billingAddress.state}} {{restructure.billingAddress.zipcode}}<br />
              {{restructure.billingAddress.countryName}}<br />
              {{restructure.billingContact.email}}<br />
              {{restructure.billingContactPhoneNumber}}<br />
            </td>
            <td class="contact-item">
              {{restructure.account.name}}<br />
              {{restructure.shippingContact.firstName}} {{restructure.shippingContact.lastName}}<br />
              {{restructure.shippingAddress.streetAddressLine1}}<br />
              {{#restructure.shippingAddress.streetAddressLine2}}
              {{restructure.shippingAddress.streetAddressLine2}}<br />
              {{/restructure.shippingAddress.streetAddressLine2}} {{restructure.shippingAddress.city}},
              {{restructure.shippingAddress.state}} {{restructure.shippingAddress.zipcode}}<br />
              {{restructure.shippingAddress.countryName}}<br />
              {{restructure.shippingContact.email}}<br />
              {{restructure.shippingContactPhoneNumber}}<br />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <br />

  <div>
    <h3 class="section-header">Contract Terms</h3>
    <table class="line-items-table">
      <thead class="line-items-table-head">
        <tr>
          <th>Start Date</th>
          <th>End Date</th>
          <th>Billing Cycle</th>
          <th>Payment Term</th>
          {{#restructure.preferredPaymentType}}
          <th>Preferred Payment Type</th>
          {{/restructure.preferredPaymentType}}
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{restructure.orderStartDate}}</td>
          <td>{{restructure.orderEndDate}}</td>
          <td>{{restructure.billingCycle}}</td>
          <td>{{restructure.paymentTerm}}</td>
          {{#restructure.preferredPaymentType}}
          <td>{{restructure.preferredPaymentType}}</td>
          {{/restructure.preferredPaymentType}}
        </tr>
      </tbody>
    </table>
  </div>

  <br />

  <div>
    <h3 class="section-header">Contract Items (Restructure)</h3>
    {{#restructure.formattedDateToLineItems}} {{#restructure.formattedDateToLineItems.entrySet}}
    <div class="bold">
      {{key.formattedStartDate}} - {{key.formattedEndDate}}
    </div>
    <table class="alpha-table">
      <thead>
        <tr class="alpha-tr">
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-head-text">Plan</span></p>
          </td>
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-head-text">Charge</span></p>
          </td>
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-head-text">Quantity</span></p>
          </td>
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-head-text">Total</span></p>
          </td>
        </tr>
      </thead>
      <tbody>
        {{#value}}
        <tr class="alpha-tr">
          <td class="alpha-td" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-body-text">{{planName}}</span></p>
          </td>
          <td class="alpha-td" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-body-text">{{chargeName}}</span></p>
          </td>
          <td class="alpha-td" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-body-text">{{quantity}}</span></p>
          </td>
          <td class="alpha-td" colspan="1" rowspan="1">
            <p><span class="alpha-table-text table-number">{{amount}}</span></p>
          </td>
        </tr>
        {{/value}}
      </tbody>
    </table>
    {{/restructure.formattedDateToLineItems.entrySet}}
    <div class="total">
      <label class="total-label">Grand Total</label>
      <span class="table-number">{{restructure.total}}</span>
    </div>
    {{/restructure.formattedDateToLineItems}}
  </div>

  <br />

  <div>
    <h3 class="section-header">Contract Items (Cancel)</h3>
    {{#cancel.formattedDateToLineItems}} {{#cancel.formattedDateToLineItems.entrySet}}
    <div class="bold">
      {{key.formattedStartDate}} - {{key.formattedEndDate}}
    </div>
    <table class="alpha-table">
      <thead>
        <tr class="alpha-tr">
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-head-text">Plan</span></p>
          </td>
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-head-text">Charge</span></p>
          </td>
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-head-text">Quantity</span></p>
          </td>
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-head-text">Total</span></p>
          </td>
        </tr>
      </thead>
      <tbody>
        {{#value}}
        <tr class="alpha-tr">
          <td class="alpha-td" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-body-text">{{planName}}</span></p>
          </td>
          <td class="alpha-td" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-body-text">{{chargeName}}</span></p>
          </td>
          <td class="alpha-td" colspan="1" rowspan="1">
            <p><span class="alpha-table-text alpha-table-body-text">{{quantity}}</span></p>
          </td>
          <td class="alpha-td" colspan="1" rowspan="1">
            <p><span class="alpha-table-text table-number">{{amount}}</span></p>
          </td>
        </tr>
        {{/value}}
      </tbody>
    </table>
    {{/cancel.formattedDateToLineItems.entrySet}}
    <div class="total">
      <label class="total-label">Grand Total</label>
      <span class="table-number">{{cancel.total}}</span>
    </div>
    {{/cancel.formattedDateToLineItems}}
  </div>

  <br />

  <div>
    {{#restructure.formattedCustomContent}} {{#restructure.customContentTitle}}
    <div>
      <h3 class="section-header">{{ restructure.customContentTitle }}</h3>
    </div>
    {{/restructure.customContentTitle}}
    <div>{{{ restructure.formattedCustomContent }}}</div>
    {{/restructure.formattedCustomContent}}
  </div>

  <div>
    {{#cancel.formattedCustomContent}} {{#cancel.customContentTitle}}
    <div>
      <h3 class="section-header">{{ cancel.customContentTitle }}</h3>
    </div>
    {{/cancel.customContentTitle}}
    <div>{{{ cancel.formattedCustomContent }}}</div>
    {{/cancel.formattedCustomContent}}
  </div>

  <br />

  {{#restructure.formattedDocumentTemplatesWithoutSection}} {{{restructure.formattedDocumentTemplatesWithoutSection}}}
  {{/restructure.formattedDocumentTemplatesWithoutSection}} {{#cancel.formattedDocumentTemplatesWithoutSection}}
  {{{cancel.formattedDocumentTemplatesWithoutSection}}} {{/cancel.formattedDocumentTemplatesWithoutSection}}

  <br />

  {{#restructure.paymentLink}}
  <br />
  <div>
    <h3 class="section-header">Automatic Payments</h3>
    <p>
      To make automatic payments, add a payment instrument as your primary payment method.<br />
      <a href="{{restructure.paymentLink}}">Submit Payment Details</a>
    </p>
  </div>
  {{/restructure.paymentLink}} {{#restructure.formattedBeforeSignatureSection}}
  {{{restructure.formattedBeforeSignatureSection}}} {{/restructure.formattedBeforeSignatureSection}}
  {{#cancel.formattedBeforeSignatureSection}} {{{cancel.formattedBeforeSignatureSection}}}
  {{/cancel.formattedBeforeSignatureSection}}

  <div class="signature-section">
    <table width="100%">
      <tr>
        <th colspan="2" class="section-header">{{ restructure.account.name }}</th>
        <th width="10%" class="section-header"></th>
        <th colspan="2" class="section-header">{{ restructure.tenantName }}</th>
      </tr>

      <tr>
        <td>&nbsp</td>
      </tr>

      <tr>
        <th>
          <div>Signature:</div>
        </th>
        <th>
          <div class="provider-gap-block provider-gap-block--signature">
            <div class="signature-anchor-text">
              <div>_{{{restructure.customerEsignDocumentTags.signature}}}_</div>
            </div>
          </div>
        </th>
        <th width="10%"></th>
        <th>
          <div>Signature:</div>
        </th>
        <th>
          <div class="provider-gap-block provider-gap-block--signature">
            <div class="signature-anchor-text">_{{{restructure.signatoryEsignDocumentTags.signature}}}_</div>
          </div>
        </th>
      </tr>

      <tr>
        <th>
          <div>Name:</div>
        </th>
        <th>
          <div class="provider-gap-block">
            <div class="signature-anchor-text">_{{{restructure.customerEsignDocumentTags.name}}}_</div>
          </div>
        </th>
        <th width="10%"></th>
        <th>
          <div>Name:</div>
        </th>
        <th>
          <div class="provider-gap-block">
            <div class="signature-anchor-text">_{{{restructure.signatoryEsignDocumentTags.name}}}_</div>
          </div>
        </th>
      </tr>

      <tr>
        <th>
          <div>Title:</div>
        </th>
        <th>
          <div class="provider-gap-block">
            <div class="signature-anchor-text">_{{{restructure.customerEsignDocumentTags.title}}}_</div>
          </div>
        </th>
        <th width="10%"></th>
        <th>
          <div>Title:</div>
        </th>
        <th>
          <div class="provider-gap-block">
            <div class="signature-anchor-text">_{{{restructure.signatoryEsignDocumentTags.title}}}_</div>
          </div>
        </th>
      </tr>

      <tr>
        <th>
          <div>Date:</div>
        </th>
        <th>
          <div class="provider-gap-block">
            <div class="signature-anchor-text">_{{{restructure.customerEsignDocumentTags.date}}}_</div>
          </div>
        </th>
        <th width="10%"></th>
        <th>
          <div>Date:</div>
        </th>
        <th>
          <div class="provider-gap-block">
            <div class="signature-anchor-text">_{{{restructure.signatoryEsignDocumentTags.date}}}_</div>
          </div>
        </th>
      </tr>
    </table>
  </div>

  {{#restructure.formattedAfterSignatureSection}}
    <br/>
    {{{restructure.formattedAfterSignatureSection}}}
  {{/restructure.formattedAfterSignatureSection}}
  {{#cancel.formattedAfterSignatureSection}}
    <br/>
    {{{cancel.formattedAfterSignatureSection}}}
  {{/cancel.formattedAfterSignatureSection}}
</body>

</html>