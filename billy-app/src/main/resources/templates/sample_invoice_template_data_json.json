{"invoice": {"invoiceNumber": "IN-00000000000000004536", "postedDate": **********, "invoiceDate": **********, "voidedDate": null, "dueDate": **********, "currency": "USD", "paymentTerm": "NET30", "totalDiscount": 0.0, "subTotal": 200.0, "taxTotal": 0.0, "taxTransactionCode": null, "total": 200.0, "subscriptionId": "SUB-81567JD", "accountId": null, "invoiceItems": [{"id": null, "planId": null, "chargeId": null, "orderId": "ORD-P8RPG5J", "orderLineItemId": "810f7f22-e3f1-43a2-af6a-94272337505e", "subscriptionChargeId": "********-b02a-4daf-bf6a-b3c5b8436eb6", "subscriptionChargeGroupId": "cc5f5311-d015-426c-9636-fff626aa66f4", "listAmount": 100.0, "discountAmount": 0.0, "amount": 100.0, "taxAmount": 0.0, "taxRate": null, "listUnitPrice": 100.0, "sellUnitPrice": 100.0, "quantity": 1, "drawdownQuantityUsed": null, "drawdownQuantityRemaining": null, "periodStartDate": **********, "periodEndDate": **********, "isBilled": null, "functionalListAmount": null, "functionalDiscountAmount": null, "functionalAmount": null, "functionalTaxAmount": null, "triggerOn": null}, {"id": null, "planId": null, "chargeId": null, "orderId": "ORD-P8RPG5J", "orderLineItemId": "810f7f22-e3f1-43a2-af6a-94272337505e", "subscriptionChargeId": "********-b02a-4daf-bf6a-b3c5b8436eb6", "subscriptionChargeGroupId": "cc5f5311-d015-426c-9636-fff626aa66f4", "listAmount": 100.0, "discountAmount": 0.0, "amount": 100.0, "taxAmount": 0.0, "taxRate": null, "listUnitPrice": 100.0, "sellUnitPrice": 100.0, "quantity": 1, "drawdownQuantityUsed": null, "drawdownQuantityRemaining": null, "periodStartDate": **********, "periodEndDate": **********, "isBilled": null, "functionalListAmount": null, "functionalDiscountAmount": null, "functionalAmount": null, "functionalTaxAmount": null, "triggerOn": null}], "billingContact": {"id": "CONT-FDWFNGH", "accountId": "ACCT-NG97NPP", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phoneNumber": "**********", "title": null, "address": {"streetAddressLine1": "112 Ave", "streetAddressLine2": "", "city": "Madrid", "state": "ESP", "country": "ES", "zipcode": "123"}, "externalId": null, "erpId": null, "fullName": "<PERSON>"}, "shippingContact": {"id": "CONT-FDWFNGH", "accountId": "ACCT-NG97NPP", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phoneNumber": "**********", "title": null, "address": {"streetAddressLine1": "112 Ave", "streetAddressLine2": "", "city": "Madrid", "state": "ESP", "country": "ES", "zipcode": "123"}, "externalId": null, "erpId": null, "fullName": "<PERSON>"}, "status": "POSTED", "purchaseOrderNumber": null, "purchaseOrderRequired": false, "note": null, "emailNotifiersList": {"toIds": [], "ccIds": [], "bccIds": []}, "erpId": null, "generationMethod": "USER_INITIATED", "generatedBy": "<EMAIL>", "exchangeRateId": null, "exchangeRate": null, "exchangeRateDate": null, "functionalTotalDiscount": null, "functionalSubTotal": null, "functionalTaxTotal": null, "functionalTotal": null, "customFields": {}}, "invoiceItems": [{"id": null, "planId": null, "chargeId": null, "orderId": "ORD-P8RPG5J", "orderLineItemId": "810f7f22-e3f1-43a2-af6a-94272337505e", "subscriptionChargeId": "********-b02a-4daf-bf6a-b3c5b8436eb6", "subscriptionChargeGroupId": "cc5f5311-d015-426c-9636-fff626aa66f4", "listAmount": 100.0, "discountAmount": 0.0, "amount": 100.0, "taxAmount": 0.0, "taxRate": null, "listUnitPrice": 100.0, "sellUnitPrice": 100.0, "quantity": 1, "drawdownQuantityUsed": null, "drawdownQuantityRemaining": null, "periodStartDate": **********, "periodEndDate": **********, "isBilled": null, "functionalListAmount": null, "functionalDiscountAmount": null, "functionalAmount": null, "functionalTaxAmount": null, "triggerOn": null}, {"id": null, "planId": null, "chargeId": null, "orderId": "ORD-P8RPG5J", "orderLineItemId": "810f7f22-e3f1-43a2-af6a-94272337505e", "subscriptionChargeId": "********-b02a-4daf-bf6a-b3c5b8436eb6", "subscriptionChargeGroupId": "cc5f5311-d015-426c-9636-fff626aa66f4", "listAmount": 100.0, "discountAmount": 0.0, "amount": 100.0, "taxAmount": 0.0, "taxRate": null, "listUnitPrice": 100.0, "sellUnitPrice": 100.0, "quantity": 1, "drawdownQuantityUsed": null, "drawdownQuantityRemaining": null, "periodStartDate": **********, "periodEndDate": **********, "isBilled": null, "functionalListAmount": null, "functionalDiscountAmount": null, "functionalAmount": null, "functionalTaxAmount": null, "triggerOn": null}], "account": {"id": "ACCT-NG97NPP", "name": "QA Test", "legalName": null, "description": "EURO $$$", "phoneNumber": "**********", "timezone": null, "crmId": null, "crmType": null, "erpId": null, "externalId": null, "currency": "EUR", "taxExemptionUseCode": null, "isReseller": true, "hasAutomaticPayment": false, "excludeFromBatchOperations": false, "excludeFromDunning": false, "supportedPaymentTypes": ["CARD", "ACH", "CHECK"], "address": null, "updatedOn": **********, "customFields": {"CF-BG22D20E": {"type": "STRING", "name": "Account_hometown", "label": "Account_hometown", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-BCPB93Z2": {"type": "STRING", "name": "Payment_Methods", "label": "Payment_Methods", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-9FEPNP17": {"type": "STRING", "name": "Customer_Segment", "label": "Customer_Segment", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-G203ZY52": {"type": "PICKLIST", "name": "testfield", "label": "testfield", "value": null, "selections": [], "options": ["v1", "v2", "v3"], "required": false, "source": "USER", "defaultValue": null}, "CF-BYCZREDV": {"type": "STRING", "name": "Company_ARR", "label": "Company ARR", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-M1VZX69E": {"type": "STRING", "name": "Account_Category", "label": "Default Account Category", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": {"value": "Mid Size", "selections": null}}, "CF-93Q6QDQT": {"type": "PICKLIST", "name": "Legal_Approval_Required", "label": "Legal Approval Required", "value": null, "selections": [], "options": ["Yes", "No"], "required": false, "source": "USER", "defaultValue": null}, "CF-VCE6MC59": {"type": "STRING", "name": "common_name", "label": "Common Name", "value": null, "selections": [], "options": [], "required": false, "source": "USER", "defaultValue": null}, "CF-EJCBD9FN": {"type": "PICKLIST", "name": "test_field", "label": "Testing", "value": null, "selections": [], "options": ["pick1", "pick2"], "required": false, "source": "USER", "defaultValue": null}, "CF-4TKV3NMX": {"type": "MULTISELECT_PICKLIST", "name": "MultiSelectPickListForAccount", "label": "MultiSelectPickListForAccount", "value": null, "selections": [], "options": ["MS1", "MS2"], "required": false, "source": "USER", "defaultValue": null}}, "entityIds": ["*"]}, "resellerAccount": null, "planByPlanId": {"PLAN-ZD7G3WX": {"id": "PLAN-ZD7G3WX", "entityIds": ["*"], "name": "100 / month", "displayName": "", "description": "", "status": "ACTIVE", "productId": "PROD-TM12XMV", "charges": [{"id": "CHRG-KRX05PF", "name": "monthly", "displayName": "", "description": "", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "unitOfMeasureId": null, "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": "RCRL-Z73F271", "erpId": null, "itemCode": null, "planId": "PLAN-ZD7G3WX", "amount": 100.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": null, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {}, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}], "currency": "USD", "externalId": null, "templateIds": [], "replacementPlanIds": [], "customFields": {}, "updatedOn": **********}}, "chargeByChargeId": {"CHRG-KRX05PF": {"id": "CHRG-KRX05PF", "name": "monthly", "displayName": "", "description": "", "taxRateId": null, "unitOfMeasureId": null, "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "minQuantity": null, "defaultQuantity": null, "maxQuantity": null, "externalId": null, "minAmount": null, "maxAmount": null, "recognitionRuleId": null, "erpId": null, "itemCode": null, "planId": "PLAN-ZD7G3WX", "amount": 100.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "priceTiers": [], "isDrawdown": false, "minimumCommitBaseChargeId": null, "overageBaseChargeId": null, "isCustom": false, "percent": null, "percentDerivedFrom": null, "targetPlanIds": [], "ledgerAccountMapping": {"taxLiabilityAccountId": null, "deferredRevenueAccountId": null, "recognizedRevenueAccountId": null, "contractAssetAccountId": null, "ledgerAccountIds": []}, "durationInMonths": null, "isEventBased": false, "isDiscount": false, "rateCardId": null, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {}, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}}}