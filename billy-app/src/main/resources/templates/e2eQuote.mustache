<html lang="us-en">
<head>
  <title>Quote</title>
  <script>
    window.addEventListener("error", function (e) {
      const ele = e.target;
      const url = ele.tagName === "LINK" ? ele.href : ele.src;
      console.error(url + " failed loading.");
      window.stop();
    }, true);
  </script>
  <script src="paged.polyfill.js"></script>
  <style>
    html {
      font-family: "Helvetica Neue", sans-serif;
      line-height: 1.5rem;
    }

    @media print {
      @page {
        size: A4;
        margin: 2.5cm;
        @bottom-right {
          content: 'Page ' counter(page) ' of ' counter(pages);
          vertical-align: top;
          padding-top: 1rem;
          font-size: 12px;
        }
      }

      div.pagedjs_margin-bottom {
        border: 0 solid #999999;
        border-top-width: 2px;
      }
    }
  </style>
</head>
<body>
<link rel="stylesheet" href="default.css"/>
<div class="header">
  <div>
    {{#base64EncodedLogo}}
      <img src="{{base64EncodedLogo}}" alt="logo" class="logo">
    {{/base64EncodedLogo}}
  </div>
  <div style="display: flex; justify-content: space-between">
    <h1 style="flex: 1; margin-top:0">{{tenantName}}</h1>
    {{#isDraft}}
      <h1 class="draft-watermark">DRAFT</h1>
    {{/isDraft}}
  </div>
  <div style="display: flex; justify-content: space-between">
    <div>
      <div>{{tenantPhoneNumber}}</div>
      <div>{{tenantEmail}}</div>
    </div>
    <div>
      <div>
        <label>Order ID</label> {{orderId}}
      </div>
    </div>
  </div>
</div>

<br>

<div>
  <p class="section-header">Customer Information</p>
  <div>
    <table>
      <tr>
        <th>Bill to</th>
        <th>Ship to</th>
      </tr>
      <tr>
        <td>{{account.name}}</td>
        <td>{{account.name}}</td>
      </tr>
      <tr>
        <td>{{billingContact.firstName}} {{billingContact.lastName}}</td>
        <td>{{shippingContact.firstName}} {{shippingContact.lastName}}</td>
      </tr>
      <tr>
        <td>{{billingAddress.streetAddressLine1}}, {{billingAddress.city}}
          , {{billingAddress.state}} {{billingAddress.zipcode}}</td>
        <td>{{shippingAddress.streetAddressLine1}}, {{shippingAddress.city}}
          , {{shippingAddress.state}} {{shippingAddress.zipcode}}</td>
      </tr>
      <tr>
        <td>{{billingContact.email}}</td>
        <td>{{shippingContact.email}}</td>
      </tr>
      <tr>
        <td>{{billingContactPhoneNumber}}</td>
        <td>{{shippingContactPhoneNumber}}</td>
      </tr>
    </table>
  </div>
</div>

<br>

<div>
  <p class="section-header">Contract Terms</p>
  <table>
    <tr>
      <th>Start Date</th>
      <th>End Date</th>
      <th>Billing Cycle</th>
      <th>Payment Term</th>
    </tr>
    <tr>
      <td>{{orderStartDate}}</td>
      <td>{{orderEndDate}}</td>
      <td>{{billingCycle}}</td>
      <td>{{paymentTerm}}</td>
    </tr>
  </table>
</div>

<br>

<div>
  <p class="section-header">Contract Items</p>
  {{#formattedDateToLineItems}}
    {{#formattedDateToLineItems.entrySet}}
      <table class="line-items-table">
        <caption class="line-items-table-caption-date-interval">{{key.formattedStartDate}} - {{key.formattedEndDate}}</caption>
        <thead class="line-items-table-head">
          <tr>
            <th>Plan</th>
            <th>Charge</th>
            <th class="table-number">Quantity</th>
            <th class="table-number">Unit Price</th>
            <th class="table-number">Total</th>
          </tr>
        </thead>
        <tbody class="line-items-table-body">
          <tr>
            <td></td>
          </tr>
          {{#value}}
            <tr>
              <td>{{planName}}</td>
              <td>{{chargeName}}</td>
              <td class="table-number">{{quantity}}</td>
              <td class="table-number">{{unitPrice}}</td>
              <td class="table-number">{{amount}}</td>
            </tr>
          {{/value}}
          <tr>
            <td>&nbsp;</td>
          </tr>
        </tbody>
      </table>
    {{/formattedDateToLineItems.entrySet}}
    <div class="total">
      <label class="total-label">Grand Total</label>
      <span class="table-number">{{total}}</span>
    </div>
  {{/formattedDateToLineItems}}
</div>

{{#formattedDocumentTemplatesWithoutSection}}
  <br/>
  {{{formattedDocumentTemplatesWithoutSection}}}
{{/formattedDocumentTemplatesWithoutSection}}


{{#formattedBeforeSignatureSection}}
  <br/>
  {{{formattedBeforeSignatureSection}}}
{{/formattedBeforeSignatureSection}}

{{#paymentLink}}
<div>
  <p class="section-header">Automatic Payments</p>
  <p>To make automatic payments, add a card as your primary payment method. <br/>
    <a href="{{paymentLink}}">Submit Payment Details</a></p>
</div>

<br/>
{{/paymentLink}}

<div>
  <table class="signature-section">
    <div>
      <th class="section-header">{{tenantName}}</th>
      <th class="section-header">Customer</th>
    </div>
    <tr>
      <th>
        <div class="signature-block">
          <span>Signature: </span>
          <div class="signature-anchor-text">**sign_here_1**</div>
        </div>
        <div class="signature-block">
          <span>Name: </span>
          <div class="signature-anchor-text">**full_name_1**</div>
        </div>
        <div class="signature-block" style="text-align: left">
          <span>Title: </span>
          <div class="signature-anchor-text">**signer_title_1**</div>
        </div>
        <div class="signature-block">
          <span>Date: </span>
          <div class="signature-anchor-text">**date_signed_1**</div>
        </div>
      </th>
      <th>
        <div class="signature-block">
          <span>Signature: </span>
          <div class="signature-anchor-text">**sign_here_2**</div>
        </div>
        <div class="signature-block">
          <span>Name: </span>
          <div class="signature-anchor-text">**full_name_2**</div>
        </div>
        <div class="signature-block">
          <span>Title: </span>
          <div class="signature-anchor-text">**signer_title_2**</div>
        </div>
        <div class="signature-block">
          <span>Date: </span>
          <div class="signature-anchor-text">**date_signed_2**</div>
        </div>
      </th>
    </tr>
  </table>
</div>

{{#formattedAfterSignatureSection}}
  <br/>
  {{{formattedAfterSignatureSection}}}
{{/formattedAfterSignatureSection}}

</body>
</html>
