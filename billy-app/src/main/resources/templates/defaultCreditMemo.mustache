<html lang="us-en">
<head>
  <title>Credit Memo</title>
  <meta charset="UTF-8">
  <script>
    window.addEventListener("error", function (e) {
      const ele = e.target;
      const url = ele.tagName === "LINK" ? ele.href : ele.src;
      console.error(url + " failed loading.");
      window.stop();
    }, true);
  </script>
  <script src="paged.polyfill.js"></script>
  <style>
    html {
      font-family: "Helvetica Neue", sans-serif;
    }

    p {
      margin: 0;
    }

    @media print {
      @page {
        size: A4;
        margin: 2.5cm;
        @bottom-right {
          content: 'Page ' counter(page) ' of ' counter(pages);
          vertical-align: top;
          padding-top: 1rem;
          font-size: 12px;
        }
      }

      div.pagedjs_margin-bottom {
        border: 0 solid #999999;
        border-top-width: 2px;
      }
    }
  </style>
</head>
<body>
<link rel="stylesheet" href="default.css"/>

<div>
  <div>
    {{#base64EncodedLogo}}
      <img src="{{base64EncodedLogo}}" alt="logo" class="logo">
    {{/base64EncodedLogo}}
  </div>
  <div style="display: flex; justify-content: space-between">
    <h1 style="flex: 1;margin-top:0">{{tenantName}}</h1>
    {{#isDraft}}
      <h1 class="draft">DRAFT</h1>
    {{/isDraft}}
  </div>
</div>

<div style="display: flex; justify-content: space-between">
  <div>
    <table>
      <tr>
        <td>{{tenantPhoneNumber}}</td>
      </tr>
      <tr>
        <td>{{tenantEmail}}</td>
      </tr>
    </table>
  </div>
  <div>
    <table>
      <tr>
        <td class="table-cell-left">Credit Memo Number</td>
        <td class="table-cell-right">{{number}}</td>
      </tr>
      <tr>
        <td class="table-cell-left">Credit memo date</td>
        <td class="table-cell-right">{{datePosted}}</td>
      </tr>
    </table>
  </div>
</div>

<br>

<p class="section-header">Customer Information</p>

<div>
  <table style="flex: 1; width: 100%">
    <tr>
      <th>Credit to</th>
    </tr>
    <tr>
      <td>{{account.name}}</td>
    </tr>
  </table>
</div>

<br>

<p class="section-header">Item Details</p>

<div>
  <table id="line-items" style="border-collapse: collapse">
    <thead style="border-bottom: 1px solid">
    <tr>
      <th>Name</th>
      {{^sharedStartDate}}
        <th>Start Date</th>
        <th>End Date</th>
      {{/sharedStartDate}}
      <th>Amount</th>
    </tr>
    </thead>
    <tbody style="line-height: 1.25rem">
    <tr>
      <td></td>
    </tr>
    {{#sharedStartDate}}
      <tr>
        <td><span style="font-size: 12px">{{sharedStartDate}} - {{sharedEndDate}}</span></td>
      </tr>
    {{/sharedStartDate}}
    {{#lineItems}}
      <tr>
        <td>{{name}}</td>
        {{^sharedStartDate}}
          <td>{{startDate}}</td>
          <td>{{endDate}}</td>
        {{/sharedStartDate}}
        <td>{{amount}}</td>
      </tr>
    {{/lineItems}}
    <tr>
      <td>&nbsp;</td>
    </tr>
    <tr class="subtotal">
      <td></td>
      {{^sharedStartDate}}
        <td></td>
        <td></td>
      {{/sharedStartDate}}
      <td></td>
      <td class="amount-due-label">Amount Credited</td>
      <td>{{total}}</td>
    </tr>
    </tbody>
  </table>
</div>

<br>
{{{customContent}}}
<br>
</body>
</html>
