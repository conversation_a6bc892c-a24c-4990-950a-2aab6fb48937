<html lang="us-en">
<head>
  <title>Usage</title>
  <meta charset="UTF-8">
  <script>
    window.addEventListener("error", function (e) {
      const ele = e.target;
      const url = ele.tagName === "LINK" ? ele.href : ele.src;
      console.error(url + " failed loading.");
      window.stop();
    }, true);
  </script>
  <script src="paged.polyfill.js"></script>
  <style>
    html {
      font-family: "Helvetica Neue", sans-serif;
      line-height: 1.5rem;
    }

    @media print {
      @page {
        size: A4;
        margin: 2.5cm;
        @bottom-right {
          content: 'Page ' counter(page) ' of ' counter(pages);
          vertical-align: top;
          padding-top: 1rem;
          font-size: 12px;
        }
      }

      div.pagedjs_margin-bottom {
        border: 0 solid #999999;
        border-top-width: 2px;
      }
    }
  </style>
</head>
<body>
<link rel="stylesheet" href="default.css"/>
<div class="header">
  <div>
    {{#tenantInfo.base64EncodedLogo}}
      <img src="{{tenantInfo.base64EncodedLogo}}" alt="logo-usage" class="logo">
    {{/tenantInfo.base64EncodedLogo}}
    {{^tenantInfo.base64EncodedLogo}}
      <h1 style="flex: 1; margin-top:0">{{tenantInfo.name}}</h1>
    {{/tenantInfo.base64EncodedLogo}}
  </div>
</div>

<br>

<div>
  <p class="section-header" style="background-color: #ee604a;">Customer Information</p>
  <div>
    <table>
      <tr>
        <th>Account Name</th>
        <th>Subscription Start Date</th>
        <th>Subscription End Date</th>
      </tr>
      <tr>
        <td>{{accountName}}</td>
        <td>{{subscriptionStartDate}}</td>
        <td>{{subscriptionEndDate}}</td>
      </tr>
    </table>
  </div>
</div>

<br>

<div>
  <p class="section-header" style="background-color: #ee604a;">Usage Statistics</p>
  <table class="line-items-table">
    <caption>
      <h2>{{planName}}</h2>
      <h4>Purchased Quantity: {{provisionedQuantity}}</h4>
    </caption>
    <thead class="line-items-table-head">
      <tr>
        <th>Period Start</th>
        <th>Period End</th>
        <th class="table-number">Consumed Quantity</th>
        <th class="table-number">Remaining Quantity</th>
      </tr>
    </thead>
    <tbody class="line-items-table-body">
      {{#usageStatistics}}
        <tr>
          <td>{{start}}</td>
          <td>{{end}}</td>
          <td class="table-number">{{consumedQuantity}}</td>
          <td class="table-number">{{remainingQuantity}}</td>
        </tr>
      {{/usageStatistics}}
    </tbody>
  </table>
</div>

<br>

<p>For any question related to this report, please reach out to {{tenantInfo.email}}.</p>
</body>
</html>
