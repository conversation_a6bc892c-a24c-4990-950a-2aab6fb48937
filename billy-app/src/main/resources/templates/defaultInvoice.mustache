<html lang="us-en">
  <head>
    <title>Invoice</title>
    <meta charset="UTF-8">
    <script>
      window.addEventListener(
        'error',
        function (e) {
          const ele = e.target
          const url = ele.tagName === 'LINK' ? ele.href : ele.src
          console.error(url + ' failed loading.')
          window.stop()
        },
        true
      )
    </script>
    <script src="paged.polyfill.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400&display=swap" rel="stylesheet" />
    <style>
      body,
      table {
        font-weight: 300;
        font-size: 10pt;
      }

      body {
        font-family: 'Roboto Slab', serif;
      }

      table {
        flex: 1;
        width: 100%;
      }

      caption,
      th,
      td {
        text-align: left;
      }

      .line-items-table {
        text-align: left;
        border-collapse: collapse;
      }

      .line-items-table-caption {
        font-weight: bold;
        margin: 0.25em 0 0.5em;
        text-align: left;
      }

      .line-items-table-head {
        background-color: #daebfd;
        border-bottom: 1px solid;
      }

      .line-items-table-body {
        line-height: 1.25rem;
      }

      .contact-item {
        padding: 0;
        margin: 0;
        vertical-align: top;
      }

      .amount-due {
        font-weight: bold;
      }

      .section-header {
        font-weight: bold;
        text-transform: uppercase;
        margin: 1em 0;
        padding-bottom: 4px;
        border-bottom: solid 4px #5350ed;
      }

      .logo {
        max-height: 5em;
        width: auto;
      }

      .total {
        text-align: right;
      }

      .total-label {
        text-align: right;
        padding-right: 1rem;
        font-weight: bold;
      }

      .subtotal {
        display: flex;
        justify-content: end;
        align-items: center;
      }

      .subtotal-number {
        text-align: right;
        min-width: 150px;
        font-weight: bold;
      }

      .table-number {
        padding-left: 1em;
        text-align: right;
      }

      .alpha-table {
        border-spacing: 0;
        border-collapse: collapse;
        margin-right: auto;
      }

      .alpha-tr {
        height: 12pt;
      }

      .alpha-td {
        border-right-style: solid;
        padding: 5pt 5pt 5pt 5pt;
        border-bottom-color: #000000;
        border-top-width: 1pt;
        border-right-width: 1pt;
        border-left-color: #000000;
        vertical-align: center;
        text-align: center;
        border-right-color: #000000;
        border-left-width: 1pt;
        border-top-style: solid;
        border-left-style: solid;
        border-bottom-width: 1pt;
        border-top-color: #000000;
        border-bottom-style: solid;
      }

      .alpha-table-text {
        color: #000000;
        text-decoration: none;
        vertical-align: baseline;
        font-style: normal;

        padding-top: 0pt;
        padding-bottom: 0pt;
        line-height: 1;
        text-align: center;
      }

      .alpha-table-body-text {
        font-weight: 400;
      }

      .alpha-table-head-text {
        font-weight: 700;
      }

      .alpha-table-head {
        background-color: #daebfd;
      }

      .superscript-text {
        vertical-align: top;
        font-size: 8px;
      }

      .draft-watermark {
        margin-top: 0;
        margin-bottom: 0;
        color: #d6dde4;
      } 

      /* PDF page - styles and settings */
      @media print {
        @page {
          margin: 10mm 10mm 10mm 10mm;
          width: 210mm;
          height: 297mm;
          @top-right {
            content: ' ';
            background-color: #5350ed;
          }
          @top-left-corner {
            content: ' ';
            background-color: #5350ed;
          }
          @top-right-corner {
            content: ' ';
            background-color: #5350ed;
          }
          @bottom-right {
            content: 'Page ' counter(page) ' of ' counter(pages);
            font-size: 10px;
          }
        }
      }
    </style>
  </head>
  <body>
    {{#isVoided}}
    <div
      style="position: fixed; font-size: 6cm; color: #eee; left: 5%; top: 30%; transform: rotate(-45deg); z-index: -1"
    >
      VOIDED
    </div>
    {{/isVoided}}
    <div>
      <div>
        {{#base64EncodedLogo}}
        <img src="{{base64EncodedLogo}}" alt="logo" class="logo" />
        {{/base64EncodedLogo}}
      </div>
      <div style="display: flex; justify-content: space-between">
        <h3 style="flex: 1; margin-top: 0">{{tenantName}}</h3>
        {{#isDraft}}
        <h3 class="draft-watermark">DRAFT</h3>
        {{/isDraft}}
      </div>
    </div>
    <div style="display: flex; justify-content: space-between">
      <div>
        <table>
          <tr>
            <td>{{tenantPhoneNumber}}</td>
          </tr>
          <tr>
            <td>{{tenantEmail}}</td>
          </tr>
        </table>
      </div>
      <div>
        <table>
          <tr>
            <td class="table-cell-left">Invoice Number:</td>
            <td class="table-cell-right">{{invoiceNumber}}</td>
          </tr>
          <tr>
            <td class="table-cell-left">Invoice Date:</td>
            <td class="table-cell-right">{{invoiceDate}}</td>
          </tr>
          <tr>
            <td class="table-cell-left">Invoice Due Date:</td>
            <td class="table-cell-right">{{dueDate}}</td>
          </tr>
          <tr>
            <td class="table-cell-left">Purchase Order:</td>
            <td class="table-cell-right">#{{purchaseOrderNumber}}</td>
          </tr>
        </table>
      </div>
    </div>
    <p class="section-header">Customer Info</p>
    <div>
      <table class="line-items-table">
        <thead class="line-items-table-head">
          <tr>
            <th>Bill to</th>
            <th>Ship to</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="contact-item">
              {{#resellerAccount}}{{resellerAccount.name}}{{/resellerAccount}}{{^resellerAccount}}{{account.name}}{{/resellerAccount}}<br />
              {{billingContact.firstName}} {{billingContact.lastName}}<br />
              {{billingAddress.streetAddressLine1}}<br />
              {{#billingAddress.streetAddressLine2}} {{billingAddress.streetAddressLine2}}<br />
              {{/billingAddress.streetAddressLine2}} {{billingAddress.city}}, {{billingAddress.state}}
              {{billingAddress.zipcode}}<br />
              {{billingAddress.countryName}}<br />
              {{billingContact.email}}<br />
              {{billingContactPhoneNumber}}<br />
            </td>
            <td class="contact-item">
              {{account.name}}<br />
              {{shippingContact.firstName}} {{shippingContact.lastName}}<br />
              {{shippingAddress.streetAddressLine1}}<br />
              {{#shippingAddress.streetAddressLine2}} {{shippingAddress.streetAddressLine2}}<br />
              {{/shippingAddress.streetAddressLine2}} {{shippingAddress.city}}, {{shippingAddress.state}}
              {{shippingAddress.zipcode}}<br />
              {{shippingAddress.countryName}}<br />
              {{shippingContact.email}}<br />
              {{shippingContactPhoneNumber}}<br />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <p class="section-header">Contract Details</p>
    <div>
      <table class="line-items-table">
        <thead class="line-items-table-head">
          <tr>
            <th>Start Date</th>
            <th>End Date</th>
            <th>Payment Terms</th>
          </tr>
        </thead>
        <tbody class="line-items-table-body">
          <tr>
            <td></td>
          </tr>
          <tr>
            <td>{{subscriptionStartDate}}</td>
            <td>{{subscriptionEndDate}}</td>
            <td>{{paymentTerm}}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <p class="section-header">Item Details</p>

    <table class="alpha-table">
      {{#sharedStartDate}}
          <caption class="line-items-table-caption">
            {{sharedStartDate}} - {{sharedEndDate}}
          </caption>
      {{/sharedStartDate}}
      <thead>
        <tr class="alpha-tr">
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text alpha-table-head-text"> Name </span>
            </p>
          </td>
          {{^sharedStartDate}}
              <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                <p>
                  <span class="alpha-table-text alpha-table-head-text"> Start Date </span>
                </p>
              </td>
          {{/sharedStartDate}}
          {{^sharedEndDate}}
              <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                <p>
                  <span class="alpha-table-text alpha-table-head-text"> End Date </span>
                </p>
              </td>
          {{/sharedEndDate}}
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text alpha-table-head-text"> Quantity </span>
            </p>
          </td>
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text alpha-table-head-text"> Amount </span>
            </p>
          </td>
        </tr>
      </thead>
      <tbody>
        {{#lineItems}}
        <tr class="alpha-tr">
          <td class="alpha-td" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text table-number"> {{planDisplayName}}: {{chargeDisplayName}} </span>
            </p>
          </td>
          {{^sharedStartDate}}
              <td class="alpha-td" colspan="1" rowspan="1">
                <p>
                  <span class="alpha-table-text table-number"> {{startDate}} </span>
                </p>
              </td>
          {{/sharedStartDate}}
          {{^sharedEndDate}}
              <td class="alpha-td" colspan="1" rowspan="1">
                <p>
                  <span class="alpha-table-text table-number"> {{endDate}} </span>
                </p>
              </td>
          {{/sharedEndDate}}
          <td class="alpha-td" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text table-number"> {{quantity}} {{unitOfMeasure}} </span>
            </p>
          </td>
          <td class="alpha-td" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text table-number"> {{amount}} </span>
            </p>
          </td>
        </tr>
        {{/lineItems}}
      </tbody>
    </table>
    {{#invoiceTemplateMapUsage}}
    <br />
    <table class="alpha-table">
      <caption class="line-items-table-caption">
        Rating With Attributes
      </caption>
      <thead>
        <tr class="alpha-tr">
          {{#tableHeaderDefinition.attributeIdNamePairs}}
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text alpha-table-head-text"> {{attributeName}} </span>
            </p>
          </td>
          {{/tableHeaderDefinition.attributeIdNamePairs}}
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text alpha-table-head-text"> Quantity </span>
            </p>
          </td>
          <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text alpha-table-head-text"> Rated Amount </span>
            </p>
          </td>
        </tr>
      </thead>
      <tbody>
        {{#tableRowData}}
        <tr>
          {{#attributeValues}}
          <td class="alpha-td" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text table-number"> {{.}} </span>
            </p>
          </td>
          {{/attributeValues}}
          <td class="alpha-td" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text table-number"> {{formattedQuantity}} </span>
            </p>
          </td>
          <td class="alpha-td" colspan="1" rowspan="1">
            <p>
              <span class="alpha-table-text table-number"> {{formattedRatedAmount}} </span>
            </p>
          </td>
        </tr>
        {{/tableRowData}}
      </tbody>
      <tr class="alpha-tr">
        {{#tableHeaderDefinition.attributeIdNamePairs}}
        <td class="alpha-td"></td>
        {{/tableHeaderDefinition.attributeIdNamePairs}}
        <td class="alpha-td" colspan="1" rowspan="1">
          <p>
            <span class="alpha-table-text alpha-table-head-text table-number"> {{formattedTotalQuantity}} </span>
          </p>
        </td>
        <td class="alpha-td" colspan="1" rowspan="1">
          <p>
            <span class="alpha-table-text alpha-table-head-text table-number"> {{formattedTotalAmount}} </span>
            <span class="superscript-text"> *Rounded </span>
          </p>
        </td>
      </tr>
    </table>
    {{/invoiceTemplateMapUsage}}
    <br />
    <div class="subtotal">
      <div class="total-label">Subtotal</div>
      <div class="subtotal-number"><b>{{subtotal}}</b></div>
    </div>
    <div class="subtotal">
      <div class="total-label">Tax</div>
      <div class="subtotal-number"><b>{{taxAmount}}</b></div>
    </div>
    <div class="subtotal">
      <div class="total-label">Total</div>
      <div class="subtotal-number"><b>{{total}}</b></div>
    </div>
    <div class="subtotal">
      <div class="total-label">Amount Due</div>
      <div class="subtotal-number"><b>{{amountDue}}</b></div>
    </div>

    {{#paymentLink}}
    <br />
    <div>
      <p class="section-header">Payments</p>
      <p>
        <a href="{{paymentLink}}">Click here</a> to manage payments for this invoice or to set up automatic payments
      </p>
    </div>
    {{/paymentLink}}

    <br />
    {{{customContent}}}
    <br />
  </body>
</html>
