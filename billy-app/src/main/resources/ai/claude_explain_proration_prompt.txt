<order>
__ORDER_DATA__
</order>

<order_line_item>
__ORDER_LINE_DATA__
</order_line_item>

Explain the above order_line_item's prorated amount if any. The explanation should be simple and succinct. Ideally step by step and easy to follow.
Intermediate calculation values are rounded to 10 decimal places.
unitPrice and charge prices are rounded to 5 decimal places.
The final total and per billing cycle amounts are rounded to 2 decimal places.
Skip any preamble and go straight to the explanation.

## order_line_item's proration logic is as follows
- if the order_line_item's chargeType is not RECURRING, no proration takes place
- if the order_line_item's chargeType is RECURRING, proration is determined by:
    - for each billing cycle, the proration ratio multiplied by the order_line_item's full billingCycle amount.
    - sum the totals of all billingCycle amounts for the order_line_item. This is the order_line_item's total value.
- Each billing cycle is determined by the order's startDate, endDate, billingAnchorDate and billingCycle:
    - billingCycle can be: MONTH, QUARTER, SEMI-ANNUAL, YEAR and UPFRONT
    - if the billing cycle is UPFRONT, use the charge's recurrence instead
    - each billing cycle is anchored on the billingAnchorDate and extends in a recurring manner based on the billing cycle until the order's endDate
    - if the billingAnchorDate is after order's startDate, the period from startDate and billingAnchor date is a partial period
    - Some examples:
        - startDate = Jan 1, 2025, billingAnchorDate = Jan 1, 2025, billingCycle = MONTH
            - billing cycles are: Jan 1, 2025 - Jan 31, 2025, Feb 1, 2025 - Feb 27, 2025 and so on
        - startDate = Jan 15, 2025, billingAnchorDate = Feb 1, 2025, billingCycle = QUARTER
            - billing cycles are: Jan 15, 2025 - Jan 31, 2025, Feb 1, 2025 - April 30, 2025 and so on
- The order_line_item's charge price is then scaled according to the billingCycle to get the full billingCycle price. Some examples:
    - charge price = 200, chargeCycle = MONTH, billingCycle = MONTH. full billingCycle price = 200
    - charge price = 200, chargeCycle = MONTH, billingCycle = QUARTER. full billingCycle price = 200 x 3 (3 months in a quarter) = 600
    - charge price = 1200, chargeCycle = YEAR, billingCycle = MONTH. full billingCycle price = 1200 / 12 = 100
- The order_line_item's overlap period with billing cycles are the time period where the order_line_item intersects billing cycles

## determining a billing cycle's proration ratio
- the proration ratio calculation is dependent on the order_line_item's startDate and endDate and the full billing cycle start and end dates
- if prorationMode is EXACT_DAYS, the ratio is the number of days the order_line_item overlaps a billing period divided by the total number of days in the billing period
    then the
- if prorationMode mode is NORMALIZED, the ratio is the number of months the order_line_item overlaps a billing period divided by the total number of months in the billing period. Partial months are calculated the same way as EXACT_DAYS above.
- if the order_line_item start date is Jan 1, 2025 and end date is June 30, 2025 and billing period is from Jan 1, 2025 until Dec 31, 2025
    - EXACT_DAYS: proration ratio is 181 (number of days between Jan 1 2025 and June 30 2025) / 365 (number of days between Jan 1 2025 and Dec 31 2025) = 0.4958904110
    - NORMALIZED: proration ratio is 6 (number of months between Jan 1 2025 and June 30 2025) / 12 (number of months between Jan 1 2025 and Dec 31 2025) = 0.5000000000
- if the order_line_item start date is Jan 1, 2025 and end date is June 14, 2025 and billing period is from Jan 1, 2025 until Dec 31, 2025
    - EXACT_DAYS: proration ratio is 165 (number of days between Jan 1 2025 and June 14 2025) / 365 (number of days between Jan 1 2025 and Dec 31 2025) = 0.4520547945
    - NORMALIZED: proration ratio is (5 (number of months between Jan 1 2025 and May 31 2025) + 14 (number of days between June 1 2025 and June 14 2025) / 30 (number of days between June 1 2025 and June 30 2025)) / 12 (number of months between Jan 1 2025 and Dec 31 2025) = (5 + 0.4666666667) / 12 = 0.4555555556

 ## determining order_line_item charge price
 - The charge price is calculated by multiplying order_line_item's unitPrice and quantity
 - This is the only place to multiply by quantity
 - Example: unitPrice = $100, quantity = 2. charge price = 100 x 2 = 200