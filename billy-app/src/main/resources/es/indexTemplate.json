{"index_patterns": ["billy*"], "order": 99, "settings": {"number_of_shards": 3}, "mappings": {"dynamic_templates": [{"disabled": {"match_pattern": "regex", "match": "^(password|.+_private_key|.+public_key)$", "mapping": {"type": "object", "enabled": false}}}, {"amounts": {"match_pattern": "regex", "match": "^(.*amount|.*price)$", "mapping": {"type": "float"}}}, {"date_fields": {"match_pattern": "regex", "match": "^(created_on|updated_on|.+_date)$", "mapping": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss.SSSSSS||yyyy-MM-dd HH:mm:ss.S||yyyy-MM-dd HH:mm:ss.SSSSS||yyyy-MM-dd HH:mm:ss.SSSS||yyyy-MM-dd HH:mm:ss.SSS||yyyy-MM-dd HH:mm:ss.SS"}}}, {"id_strings": {"match_mapping_type": "string", "match_pattern": "regex", "match": "^(id|.+_id|.+_key)$", "mapping": {"type": "keyword", "copy_to": ["_all_", "_suggest_"]}}}, {"sortable_strings": {"match_mapping_type": "string", "match": "*", "mapping": {"type": "text", "fields": {"keyword": {"type": "keyword"}}, "copy_to": ["_all_", "_suggest_"]}}}, {"composite_field": {"match": "*", "mapping": {"copy_to": "_all_"}}}], "properties": {"_all_": {"type": "text"}, "_suggest_": {"type": "completion"}, "object_id": {"type": "text", "fields": {"keyword": {"type": "keyword"}}, "copy_to": ["_all_", "_suggest_"]}, "_relations_": {"type": "join", "relations": {"product": "plan", "plan": "charge", "account": "subscription", "subscription": ["account_order", "subscription_charge"], "account_order": "account_order_line_item", "invoice": ["invoice_line_item", "invoice_document"]}}}}}