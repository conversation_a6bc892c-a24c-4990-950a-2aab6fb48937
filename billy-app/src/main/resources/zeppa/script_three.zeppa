orderCreationCustomization {
    rule {
        scope ORDER_LINE_ITEM
        name "line actions for specific line actions"
        condition {
            when lineItem.plan.customField("foo") in ["bar"]
        }

        lineItemActions {
            forceStartDateToEndDate()
            applyListPriceOverride 1.2
            applyPriceAttributeSelection {
                put "x", "y"
            }
            applyLineItemQuantity 10
        }
    }

    rule {
        scope ORDER
        name "apply discount on cancel order does not work"
        condition {
            when order.anyLineItemsMatch {
                on lineItem.plan.customField("foo") in ["bar"]
            }
        }

        orderActions {
            addPredefinedDiscount "Custom Discount"
        }
    }
}