orderCreationCustomization {
    rule {
        scope ORDER
        name "Test Account Order Creation Not Allowed"
        condition {
            when order.account.name in ["TestAccount", "Test Account", "Test Any"]
            and order.account.isReseller
        }
        orderActions {
            failOrderCreation "Test account name ${order.account.name} found while creating order", ON_SAVE_AND_DRYRUN
        }
    }

    rule {
        scope ORDER
        name "Orders Should Start on 2nd Day of Month"
        condition {
            when order.type == OrderType.NEW
            and not(order.startDate.dayOfMonth == 2)
        }
        orderActions {
            failOrderCreation "New Orders should start at 2 day of every month not ${order.startDate.dayOfMonth}", ON_SAVE
        }
    }

    rule {
        scope ORDER
        name "Order Expires Before Starting"
        condition {
            when order.type == OrderType.NEW
            and order.expiresOn > order.startDate
        }
        orderActions {
            failOrderCreation "Order cannot expire after the start date", ON_SAVE
        }
    }

    rule {
        scope ORDER
        name "Orders Should Start on 2nd Day of Month Or Expression"
        condition {
            when order.type in OrderType.NET_NEW
            and not(order.startDate.dayOfMonth == 2)
        }
        orderActions {
            failOrderCreation "New Orders should start at 2 day of every month not ${order.startDate.dayOfMonth}", ON_SAVE
        }
    }

    rule {
        scope ORDER
        name "Orders Should Start on 1st Day of Month"
        condition {
            when order.type == OrderType.NEW
            and not(order.startDate.dayOfMonth == 1)
        }
        orderActions {
            failOrderCreation "New Orders should start at 1st of every month not ${order.startDate.dayOfMonth}"
        }
    }

    rule {
        scope ORDER_LINE_ITEM
        name "Several Line Item Mutations Based On Conditions"
        condition {
            when order.type == OrderType.NEW
            and not(order.anyAddedPlansMatch {
                on plan.currency == "INR"
            })
            and lineItem.chargeId == "CHRG-12345"
        }

        lineItemActions {
            applyLineItemDiscount .1075
            applyLineItemQuantity 2
            applyListPriceOverride 1.2
            applyCustomListPrice 20
            applyCustomListPrice 1.2345678901234567890
            applyCustomField "foo", lineItem.plan.currency
            applyCustomField "bar", ["baz", 1, order.type]
        }
    }

    rule {
        scope ORDER_LINE_ITEM
        name "Start Date To End Date And Custom Field On Specific Charge"
        condition {
            when order.type == OrderType.NEW
            and lineItem.planId == "PLAN-12345"
        }

        lineItemActions {
            forceStartDateToEndDate()
            forceCustomField "foo", lineItem.plan.currency
            forceCustomField "bar", ["baz", 1, "donkey"]
        }
    }

    rule {
        scope ORDER_LINE_ITEM
        name "Line Item Custom Field Missing"
        condition {
            when order.type == OrderType.NEW
            and Strings.isBlank(lineItem.customField("foo"))
        }

        lineItemActions {
            failOrderCreation "Plan -> ${lineItem.plan.name} and Charge -> ${lineItem.charge.name} line items need custom field foo"
        }
    }

    rule {
        scope ORDER_LINE_ITEM
        name "Apply Quantity On Select Plans"
        condition {
            when order.type == OrderType.AMENDMENT
            and lineItem.planId == "PLAN-12345"
        }

        lineItemActions {
            applyLineItemQuantity 20
        }
    }

    rule {
        scope ORDER_LINE_ITEM
        name "Product Based Discount On Line Item"
        condition {
            when order.type == OrderType.NEW
            and lineItem.plan.product.sku == "SK-001"
        }

        lineItemActions {
            applyLineItemDiscount .2
            forceListPriceOverride 1.32
            forceCustomListPrice 10.25
        }
    }

    rule {
        scope ORDER
        name "Fail order creation on Plan Foo"
        condition {
            when order.addedPlansMatchCount {
                on plan.currency == "USD"
                and plan.customFieldMatches("foo", "baz")
            } >= 1
        }
        orderActions {
            failOrderCreation "Cannot create orders in USD with foo baz"
        }
    }

    rule {
        scope ORDER
        name "Discount, Order Template and Terms Rule"
        condition {
            when order.anyAddedPlansMatch {
                on plan.currency == "USD"
                and plan.customFieldMatches("foo", "foo-baz")
            }
        }

        orderActions {
            addPredefinedDiscount "Foo Baz Discount"
            forceDocumentTemplate "Foo Baz Template"
            addPredefinedTerm "Foo Baz Terms"
        }

        orderInverseActions {
            removePredefinedTerm "Foo Baz Terms"
            removePredefinedDiscount "Foo Baz Discount"
            removeDocumentTemplate "Foo Baz Template"
        }
    }

    rule {
        scope ORDER_LINE_ITEM
        name "Foo Bar Baz Recurring per unit rule"
        condition {
            when order.type == OrderType.NEW
            and lineItem.plan.name in ["foo", "bar", "baz"]
            and lineItem.charge.type == "RECURRING"
            and lineItem.charge.model == "PER_UNIT"
        }

        lineItemActions {
            applyLineItemDiscount .1075
            removePredefinedDiscount "Standard Discount"
        }
    }

    rule {
        scope ORDER_LINE_ITEM
        name "Foo Bar Baz Recurring per unit rule boolean expression"
        condition {
            when order.type == OrderType.NEW
            and(lineItem.plan.name == "foo" || lineItem.plan.name == "bar" || lineItem.plan.name == "baz")
        }

        lineItemActions {
            applyLineItemDiscount .1075
            removePredefinedDiscount "Standard Discount"
        }
    }

    rule {
        scope ORDER_LINE_ITEM
        name "Foo Bar Baz Recurring per unit rule Bool functions"
        condition {
            when order.type == OrderType.NEW
            and(Bool.andGroup lineItem.plan.name == "foo", lineItem.plan.name == "bar", lineItem.plan.name == "baz")
        }

        lineItemActions {
            applyLineItemDiscount .1075
            removePredefinedDiscount "Standard Discount"
        }
    }

    rule {
        scope ORDER
        name "Billing Cycle should be yearly"
        condition {
            when order.type == OrderType.NEW
            and order.billingCycle != "YEAR"
        }
        orderActions {
            failOrderCreation "New orders should have yearly billing", ON_SAVE
        }
    }

    rule {
        scope ORDER
        name "Payment term should be less than NET30"
        condition {
            when order.type == OrderType.NEW
            and order.paymentDueInDays > 30
        }
        orderActions {
            failOrderCreation "New orders payment term should be <= NET 30", ON_SAVE
        }
    }

    rule {
        scope ORDER
        name "Quantity multiple of 3"
        condition {
            when order.anyLineItemsMatch {
                on lineItem.quantity % 3 != 0
            }
        }
        orderActions {
            failOrderCreation "Item quantity should be multiple of 3"
        }
    }

    rule {
        scope ORDER
        name "Quantity multiple of 5"
        condition {
            when order.anyLineItemsMatch {
                on lineItem.quantity % 5 != 0
            }
        }
        orderActions {
            failOrderCreation "Item quantity should be multiple of 5"
        }
    }

    rule {
        scope ORDER
        name "Price Override On Non 3 Multiples"
        condition {
            when order.anyLineItemsMatch {
                on lineItem.quantity % 3 != 0
            }
        }
        orderActions {
            forcePriceUpliftOnRamps 10.25
        }
    }

    rule {
        scope ORDER
        name "Reseller Orders Are not Allowed"
        condition {
            when order.isResold
        }
        orderActions {
            failOrderCreation "Reseller Orders are not allowed"
        }
    }

    rule {
        scope ORDER_LINE_ITEM
        name "Force renewal uplift"
        condition {
            when lineItem.plan.product.sku == "SK-001"
        }

        lineItemActions {
            forceRenewalUplift 1.1
        }
    }

    rule {
        scope ORDER
        name "Order duration"
        condition {
            when order.termLengthDays >= 300
        }
        orderActions {
            failOrderCreation "Order must be less than 300 days in duration"
        }
    }

    rule {
        scope ORDER
        name "Charge description example"
        condition {
            when order.lineItemsMatching { on lineItem.charge.description == "A super charge for testing" }.lineCount > 0
        }
        orderActions {
            addPredefinedDiscount "Awesome Discount"
        }
    }
}