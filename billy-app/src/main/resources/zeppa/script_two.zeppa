orderCreationCustomization {

    rule {
        scope ORDER
        name "Sum of lines quantity based on order start date"
        condition {
            when order.anyLineItemsMatch {
                on lineItem.action in [LineAction.ADD]
                and lineItem.startDate == order.startDate
            }
        }
        orderActions {
            applyCustomField "foo", Numbers.toString(order.lineItemsMatching {
                on lineItem.action in [LineAction.ADD]
                and lineItem.startDate == order.startDate
            }.sumOfQuantity)
        }
    }

    rule {
        scope ORDER
        name "Sum of lines quantity based on order NOT date"
        condition {
            when order.anyLineItemsMatch {
                on lineItem.action in [LineAction.ADD]
                and lineItem.startDate != order.startDate
            }
        }
        orderActions {
            applyCustomField "bar", Numbers.toString(order.lineItemsMatching {
                on lineItem.action in [LineAction.ADD]
                and lineItem.startDate != order.startDate
            }.sumOfQuantity)
        }
    }

    rule {
        scope ORDER
        name "Update or Remove line action present"
        condition {
            when order.anyLineItemsMatch {
                on lineItem.action in [LineAction.REMOVE]
            }
        }
        orderActions {
            failOrderCreation "Removal of line items not allowed in the order"
        }
    }

    rule {
        scope ORDER
        name "Several Order Actions Based On Line Items"
        condition {
            when order.lineItemsMatchingCount {
                on lineItem.action in [LineAction.ADD]
            } >= 2
        }
        orderActions {
            addPredefinedTerm "Add Terms"
            addPredefinedDiscount "Add Discount"
            forcePaymentTermNet 60
            forceBillingCycle Cycle.MONTH
            applyCustomField "foo", "bar"
            applyCustomField "bar", ["baz", 1, "donkey"]

            forceCustomField "foo", "bar"
            forceCustomField "bar", ["baz", 1, order.type]
        }
    }

    rule {
        scope ORDER
        name "Bar and Baz cannot be same"
        condition {
            when order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "bar")
            }.sumOfQuantity == order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "baz")
            }.sumOfQuantity
        }
        orderActions {
            failOrderCreation "Cannot Have Same Quantity for bar and baz"
        }
    }

    rule {
        scope ORDER
        name "Bar and Baz both have 1 line"
        condition {
            when order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "bar")
            }.lineCount == 1 and order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "baz")
            }.lineCount == 1
        }
        orderActions {
            failOrderCreation "Bar and Baz both have 1 line"
        }
    }

    rule {
        scope ORDER
        name "Bar has 2 lines"
        condition {
            when order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "bar")
            }.lineCount == 2 and order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "baz")
            }.lineCount == 1
        }
        orderActions {
            failOrderCreation "Bar has 2 lines"
        }
    }

    rule {
        scope ORDER
        name "Bar and Baz cannot have same custom list unit price"
        condition {
            when order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "bar")
            }.first.listUnitPrice == order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "baz")
            }.first.listUnitPrice
        }
        orderActions {
            failOrderCreation "Bar and Baz cannot have same custom list unit price"
        }
    }

    rule {
        scope ORDER
        name "Bar unit price 5 less than Baz"
        condition {
            when order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "bar")
            }.first.listUnitPrice != order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "baz")
            }.first.listUnitPrice - 5
        }
        orderActions {
            failOrderCreation "Bar unit price 5 less than Baz"
        }
    }

    rule {
        scope ORDER
        name "Bar and Baz must have same custom list unit price"
        condition {
            when order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "bar")
            }.first.listUnitPrice != order.lineItemsMatching {
                on lineItem.plan.customFieldMatches("foo", "baz")
            }.first.listUnitPrice
        }
        orderActions {
            failOrderCreation "Bar and Baz must have same custom list unit price"
        }
    }

    rule {
        scope ORDER
        name "Bar and Baz quantity should be > 50"
        condition {
            when order.lineItemsMatching {
                on lineItem.plan.customField("foo") in ["bar", "baz"]
            }.sumOfQuantity < 50
            and user.isInSales
        }
        orderActions {
            failOrderCreation "Bar Baz total quantity should be > 50"
        }
    }

    rule {
        scope ORDER
        name "Bar and Baz quantity should be > 100 for Admin"
        condition {
            when order.lineItemsMatching {
                on lineItem.plan.customField("foo") in ["bar", "baz"]
            }.sumOfQuantity < 100
            and user.role == UserRole.ADMIN
        }
        orderActions {
            failOrderCreation "Bar Baz total quantity should be > 50"
        }
    }

    rule {
        scope ORDER
        name "Any line has discount"
        condition {
            when order.anyLineItemsMatch {
                on lineItem.hasAnyDiscount
            }
        }
        orderActions {
            failOrderCreation "Order lines cannot have discounts"
        }
    }

    rule {
        scope ORDER_LINE_ITEM
        name "String expression with mapping"
        condition {
            when lineItem.plan.customField("foo") in ["baz"]
            and order.lineItemsMatching {
                on thisLineItem().plan.id == lineItem.plan.id
            }.lineCount == 1
        }
        lineItemActions {
            forcePriceAttributeSelection {
                put("Hello", Numbers.toString(10, {
                    switch (it) {
                        case 1..10:
                            return "World"
                        default:
                            return "Other"
                    }
                }))
            }

            forceLineItemQuantity 20
        }
    }

    rule {
        scope ORDER
        name "All line items should have different attributes"
        condition {
            when order.lineItemsMatching {
                on lineItem.action in [LineAction.ADD]
            }.haveSameAttributeReferences
        }
        orderActions {
            failOrderCreation "Add line should have different MAP Attributes"
        }
    }

    rule {
        scope ORDER
        name "Opportunity Based Order Failure Rule"
        condition {
            when order.anyLineItemsMatch {
                on lineItem.action in LineAction.NET_NEW_ACTIONS
            }
            and order.opportunity.name in ["Test"]
            and order.opportunity.type == "Sample"
            and order.opportunity.crmType in ["HUBSPOT"]
            and order.opportunity.customField("foo") in ["bar"]
            and order.opportunity.customFieldMatches("crooked", "creek")
        }
        orderActions {
            failOrderCreation "Bad Opportunity rule test"
        }
    }
}