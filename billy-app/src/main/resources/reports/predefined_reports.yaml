open_invoices:
  report_id: open_invoices
  name: Open Invoices Report
  description: List of posted invoices having non-zero balance
  chart:
    chartType: bar
    showLegend: true
    mainAxisKey: mainText
    crossAxisKey: crossNumber
    crossAxisLabel: Invoice Balance Total
    mainAxisScale: category
    crossAxisScale: logarithmic
  query: >
    SELECT inv.customer_account_id AS "Account ID",
        entity.display_id AS "Entity Display ID",
        acc.name AS "Account Name", acc.crm_id AS "CRM ID",
        inv.subscription_id AS "Subscription ID",
        inv.invoice_number AS "Invoice Number",
        format_date(inv.invoice_date, {1}) AS "Invoice Date",
        format_date(inv.posted_date, {1}) AS "Posted Date",
        format_date(inv.due_date, {1}) AS "Invoice Due Date",
        inv.billing_contact::json->'address'->>'streetAddressLine1' AS "Billing Contact Address Line 1",
        inv.billing_contact::json->'address'->>'streetAddressLine2' AS "Billing Contact Address Line 2",
        inv.billing_contact::json->'address'->>'city' AS "Billing Contact City",
        inv.billing_contact::json->'address'->>'state' AS "Billing Contact State",
        inv.billing_contact::json->'address'->>'country' AS "Billing Contact Country",
        inv.billing_contact::json->'address'->>'zipcode' AS "Billing Contact ZIP",
        inv.shipping_contact::json->'address'->>'streetAddressLine1' AS "Shipping Contact Address Line 1",
        inv.shipping_contact::json->'address'->>'streetAddressLine1' AS "Shipping Contact Address Line 2",
        inv.shipping_contact::json->'address'->>'city' AS "Shipping Contact City",
        inv.shipping_contact::json->'address'->>'state' AS "Shipping Contact State",
        inv.shipping_contact::json->'address'->>'country' AS "Shipping Contact Country",
        inv.shipping_contact::json->'address'->>'zipcode' AS "Shipping Contact ZIP",
        inv.payment_term AS "Payment Term",
        inv.currency_code AS "Currency", 
        format_currency_amount(inv.total) AS "Invoice Amount",
        format_currency_amount((CASE WHEN (ib.balance >= 0) THEN ib.balance ELSE inv.total END)) AS "Balance Amount",
        entity.functional_currency AS "Functional Currency",
        txr.exchange_rate AS "Exchange Rate",
        format_date(txr.effective_date, {1}) AS "Exchange Rate Date",
        CASE WHEN inv.functional_total IS NULL THEN NULL ELSE format_currency_amount(inv.functional_total) END AS "Functional Invoice Amount"
    FROM invoice inv LEFT OUTER JOIN invoice_balance ib
             on inv.is_deleted = false AND inv.total > 0 AND inv.invoice_number = ib.invoice_number
                 AND inv.tenant_id = ib.tenant_id
         INNER JOIN account acc 
             on inv.customer_account_id = acc.account_id AND inv.tenant_id = acc.tenant_id
         INNER JOIN entity 
             on inv.entity_id = entity.entity_id AND inv.tenant_id = entity.tenant_id
        LEFT JOIN transactional_exchange_rate txr
                 ON inv.exchange_rate_id = txr.transactional_exchange_rate_id
                AND inv.tenant_id = txr.tenant_id
    WHERE inv.tenant_id = {0} AND inv.status IN ('POSTED', 'PAID') AND (ib.balance > 0 OR ib.balance is null)
       AND inv.entity_id IN {entity_ids_expression}
    ORDER BY "Balance Amount" desc
  bindings:
    - tenant_id
    - timezone

invoice_details:
  report_id: invoice_details
  name: Invoice Details Report
  description: Invoice details based on the invoice date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Posted between
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    SELECT inv.customer_account_id AS "Account ID",
      entity.display_id AS "Entity Display ID",
      acc.name AS "Account Name", acc.crm_id AS "CRM ID",
      inv.subscription_id AS "Subscription ID",
      inv.invoice_number AS "Invoice Number",
      inv.status AS "Invoice Status",
      il.invoice_line_number AS "Invoice Line Number",
      format_date(inv.invoice_date, {0}) AS "Invoice Date",
      format_date(inv.posted_date, {0}) AS "Posted Date",
      format_date(inv.due_date, {0}) AS "Invoice Due Date",
      format_date(inv.voided_date, {0}) AS "Invoice Voided Date",
      inv.payment_term AS "Payment Term",
      inv.currency_code AS "Currency", 
      format_currency_amount(inv.total) AS "Invoice Amount",
      il.order_id as "Order ID", il.order_line_item_id as "Order Line ID",
      il.charge_id as "Charge ID", 
      format_currency_amount(il.list_amount) as "Listed Amount",
      format_currency_amount(il.discount_amount) as "Discount", 
      format_currency_amount(il.amount) as "Charged Amount",
      TRUNC(il.list_unit_price, 5) as "Listed Unit Price",
      TRUNC(il.sell_unit_price, 5) as "Sell Unit Price",
      il.quantity as "Quantity", 
      format_currency_amount(il.tax_amount) as "Tax",
      format_date(il.period_start_date, {0}) as "Period Start Date",
      format_end_date(il.period_end_date, {0}) as "Period End Date",
      format_currency_amount((CASE WHEN (ib.balance >= 0) THEN ib.balance ELSE inv.total END)) AS "Balance",
      entity.functional_currency AS "Functional Currency",
      txr.exchange_rate AS "Exchange Rate",
      format_date(txr.effective_date, {0}) AS "Exchange Rate Date",
      CASE WHEN inv.functional_total IS NULL THEN NULL ELSE format_currency_amount(inv.functional_total) END AS "Functional Invoice Amount",
      CASE WHEN il.functional_list_amount IS NULL THEN NULL ELSE format_currency_amount(il.functional_list_amount) END AS "Functional Listed Amount",
      CASE WHEN il.functional_discount_amount IS NULL THEN NULL ELSE format_currency_amount(il.functional_discount_amount) END AS "Functional Discount Amount",
      CASE WHEN il.functional_amount IS NULL THEN NULL ELSE format_currency_amount(il.functional_amount) END AS "Functional Charged Amount",
      CASE WHEN il.functional_tax_amount IS NULL THEN NULL ELSE format_currency_amount(il.functional_tax_amount) END AS "Functional Tax Amount"
    FROM invoice_line_item il INNER JOIN invoice inv
            ON il.invoice_id = inv.id AND inv.is_deleted = false
        INNER JOIN account acc
            ON inv.customer_account_id = acc.account_id AND inv.tenant_id = acc.tenant_id
        INNER JOIN entity 
            ON inv.entity_id = entity.entity_id AND inv.tenant_id = entity.tenant_id
        LEFT OUTER JOIN invoice_balance ib
            ON ib.invoice_number = inv.invoice_number AND ib.tenant_id = il.tenant_id
        LEFT JOIN transactional_exchange_rate txr
            ON inv.exchange_rate_id = txr.transactional_exchange_rate_id
            AND inv.tenant_id = txr.tenant_id
    WHERE il.tenant_id = {1} AND il.is_deleted = false
        AND (inv.invoice_date IS NULL OR trunc(extract(epoch from inv.invoice_date)) BETWEEN {2} AND {3})
        AND inv.entity_id IN {entity_ids_expression}
    ORDER BY inv.invoice_date ASC

invoices_missing_po:
  report_id: invoices_missing_po
  name: Invoices Missing Required Purchase Order Number
  description: Invoices which are missing purchase order number when required
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  bindings:
    - timezone
    - tenant_id
  query: >
    SELECT inv.customer_account_id AS "Account ID",
      entity.display_id AS "Entity Display ID",
      acc.name AS "Account Name",
      acc.crm_id AS "CRM ID",
      inv.subscription_id AS "Subscription ID",
      inv.invoice_number AS "Invoice Number",
      inv.status AS "Invoice Status",
      format_date(inv.invoice_date, {0}) AS "Invoice Date",
      inv.payment_term AS "Payment Term",
      inv.currency_code AS "Currency", 
      format_currency_amount(inv.total) AS "Invoice Amount",
      inv.po_number AS "PO Number",
      inv.po_required AS "PO Required"
    FROM invoice inv
        INNER JOIN account acc
            ON inv.customer_account_id = acc.account_id AND inv.tenant_id = acc.tenant_id
        INNER JOIN entity 
            ON inv.entity_id = entity.entity_id AND inv.tenant_id = entity.tenant_id
    WHERE inv.status = 'DRAFT'
    AND inv.is_deleted = false 
    AND inv.po_number is null 
    AND inv.po_required = true
    AND inv.tenant_id = {1}
    AND inv.entity_id IN {entity_ids_expression}
    ORDER BY inv.created_on ASC

open_credit_memos:
  report_id: open_credit_memos
  name: Open Credit Memos Report
  description: List of posted credit memos having non-zero balance
  query: >
    SELECT cm.account_id AS "Account ID", 
        entity.display_id AS "Entity Display ID",
        acc.name AS "Account Name", 
        acc.crm_id AS "CRM ID",
        cm.credit_memo_number AS "Credit Memo Number",
        cm.created_from AS "Created From",
        format_date(cm.credit_memo_date, {1}) AS "Credit Memo Date",
        format_date(cm.posted_date, {1}) AS "Posted Date",
        cm.currency_code AS "Currency", 
        entity.functional_currency AS "Functional Currency",
        cm.exchange_rate AS "Exchange Rate",
        format_date(cm.exchange_rate_date, {1}) AS "Exchange Rate Date",
        format_currency_amount(cm.amount) AS "Credit Memo Amount",
        CASE WHEN cm.functional_amount IS NULL THEN NULL ELSE format_currency_amount(cm.functional_amount) END AS "Funct. Credit Memo Amount",
        format_currency_amount((CASE WHEN (cb.balance >= 0) THEN cb.balance ELSE cm.amount END)) AS "Balance Amount"
    FROM credit_memo cm LEFT OUTER JOIN credit_memo_balance cb
             on cm.is_deleted = false AND cm.amount > 0 AND cm.credit_memo_number = cb.credit_memo_number
                 AND cm.tenant_id = cb.tenant_id
         INNER JOIN account acc 
             on cm.account_id = acc.account_id AND cm.tenant_id = acc.tenant_id
         INNER JOIN entity
             ON cm.entity_id = entity.entity_id AND cm.tenant_id = entity.tenant_id
    WHERE cm.tenant_id = {0} AND cm.status <> 'DRAFT' AND (cb.balance > 0 OR cb.balance is null)
      AND cm.entity_id IN {entity_ids_expression}
    ORDER BY "Balance Amount" desc
  bindings:
    - tenant_id
    - timezone

credit_memo_details:
  report_id: credit_memo_details
  name: Credit Memo Details Report
  description: Credit Memo details based on credit memo date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Posted between
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    SELECT cm.account_id AS "Account ID", 
      entity.display_id AS "Entity Display ID",
      acc.name AS "Account Name", 
      acc.crm_id AS "CRM ID",
      cm.credit_memo_number AS "Credit Memo Number", 
      cl.charge_id AS "Charge ID",
      format_date(cm.credit_memo_date, {0}) AS "Credit Memo Date",
      format_date(cm.posted_date, {0}) AS "Posted Date",
      cm.currency_code AS "Currency", 
      entity.functional_currency AS "Functional Currency",
      cm.exchange_rate AS "Exchange Rate",
      format_date(cm.exchange_rate_date, {0}) AS "Exchange Rate Date",
      format_currency_amount(cm.amount) AS "Credit Memo Amount",
      CASE WHEN cm.functional_amount IS NULL THEN NULL ELSE format_currency_amount(cm.functional_amount) END AS "Funct. Credit Memo Amount",
      format_currency_amount(cl.amount) as "Credit Memo Line Amount",
      CASE WHEN cl.functional_amount IS NULL THEN NULL ELSE format_currency_amount(cl.functional_amount) END AS "Funct. Credit Memo Line Amount",
      format_currency_amount((CASE WHEN (cb.balance >= 0) THEN cb.balance ELSE cm.amount END)) AS "Balance"
    FROM credit_memo_line_item cl INNER JOIN credit_memo cm
            ON cl.credit_memo_id = cm.id AND cm.is_deleted = false
        INNER JOIN account acc
            ON cm.account_id = acc.account_id AND cm.tenant_id = acc.tenant_id
        INNER JOIN entity
            ON cm.entity_id = entity.entity_id AND cm.tenant_id = entity.tenant_id
        LEFT OUTER JOIN credit_memo_balance cb
            ON cb.credit_memo_number = cm.credit_memo_number AND cb.tenant_id = cl.tenant_id
    WHERE cl.tenant_id = {1} AND cl.is_deleted = false
        AND trunc(extract(epoch from cm.credit_memo_date)) BETWEEN {2} AND {3}
        AND cm.entity_id IN {entity_ids_expression}
    ORDER BY cm.credit_memo_date ASC

refunds:
  report_id: refunds
  name: Refunds Report
  description: Refunds based on refund date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Created between
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    SELECT re.refund_id as "Refund Id", 
      entity.display_id AS "Entity Display ID",
      re.payment_id as "Payment Id", re.payment_method_type as "Payment Method Type", re.notes as "Notes", re.created_by as "Created By", 
      re.credit_memo_number AS "Credit Memo Number",
      format_date(cm.credit_memo_date, {0}) AS "Credit Memo Date",
      cm.currency_code AS "Currency", 
      format_currency_amount(cm.amount) AS "Credit Memo Amount",
      format_currency_amount((CASE WHEN (cb.balance >= 0) THEN cb.balance ELSE cm.amount END)) AS "Balance"
    FROM refund re INNER JOIN credit_memo cm
            ON re.credit_memo_number = cm.credit_memo_number AND cm.is_deleted = false AND re.tenant_id = cm.tenant_id
        INNER JOIN entity
            ON re.entity_id = entity.entity_id AND re.tenant_id = entity.tenant_id
        LEFT OUTER JOIN credit_memo_balance cb
            ON cb.credit_memo_number = re.credit_memo_number AND cb.tenant_id = re.tenant_id
    WHERE re.tenant_id = {1} AND trunc(extract(epoch from re.created_on)) BETWEEN {2} AND {3}
      AND re.entity_id IN {entity_ids_expression}
    ORDER BY re.created_on ASC

bookings:
  report_id: bookings
  name: Bookings Report
  description: Bookings information based on order execution date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Posted between
      type: range
      datatype: date
  chart:
    chartType: bar
    showLegend: true
    mainAxisKey: mainText
    crossAxisKey: crossNumber
    mainAxisLabel: Close Date
    crossAxisLabel: Order Total
    mainAxisScale: time
    crossAxisScale: logarithmic
    sortBy: mainNumber
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  variableLengthField:
    outputName: custom_fields
    variableLenHeaderQuery: >
      SELECT array_agg(field_name order by custom_field_id ASC) AS custom_fields
        FROM custom_field_definition where tenant_id = {1} AND is_deleted = false AND parent_object_type = 'ORDER'
  query: >
    WITH subs as (SELECT DISTINCT ON (sub.subscription_id) sub.subscription_id
          FROM subscription sub 
          WHERE sub.tenant_id = {1} AND sub.is_deleted = false
          ORDER BY sub.subscription_id desc, sub.version desc),
       orders as (SELECT * from account_order ordr WHERE
          ordr.tenant_id = {1} AND
          ordr.entity_id IN {entity_ids_expression} AND
          ordr.is_deleted = false AND
          (ordr.metrics->>'recompute')::bool = false AND
          trunc(extract(epoch from ordr.executed_on)) BETWEEN {2} AND {3}),
       all_cfids as (SELECT custom_field_id FROM custom_field_definition where tenant_id = {1} AND is_deleted = false AND parent_object_type = 'ORDER' ),
       all_order_cfids as (SELECT orders.order_id, all_cfids.custom_field_id as cf_id FROM orders CROSS JOIN all_cfids),
       assigned_cfs as (SELECT parent_object_id, e.key as cf_id, e.value->>'value' as cf_value
          FROM  custom_field cf, jsonb_each(cf.custom_fields -> 'entries') AS e, custom_field_definition cfd
          WHERE cf.tenant_id = {1} and cf.parent_object_type = 'ORDER'
              AND cfd.tenant_id = cf.tenant_id and cfd.parent_object_type = 'ORDER'
              AND cfd.custom_field_id = e.key AND cfd.is_deleted = false),
       all_cfs as (SELECT aocf.order_id, aocf.cf_id, acf.cf_value
          FROM all_order_cfids aocf LEFT OUTER JOIN assigned_cfs acf
              ON aocf.order_id = acf.parent_object_id AND aocf.cf_id = acf.cf_id),
       cfs_by_order as (SELECT order_id, array_agg(all_cfs.cf_value ORDER BY all_cfs.cf_id  ASC) as cfs FROM all_cfs GROUP BY order_id)
    SELECT acc.name AS "Account", acc.crm_id AS "CRM ID", ordr.account_id AS "Account ID",
      entity.display_id AS "Entity Display ID",
      CASE WHEN reseller.is_reseller
        THEN reseller.account_id
        ELSE null
      END AS "Reseller ID",
      CASE WHEN reseller.is_reseller
        THEN reseller.name
        ELSE null
      END AS "Reseller Name",
      ordr.order_id AS "Order ID",
      ordr.name AS "Order Name",
      ordr.sfdc_opportunity_id AS "Opportunity ID",
      format_date(ordr.executed_on, {0}) AS "Close Date",
      format_date(ordr.start_date, {0}) AS "Start Date",
      format_end_date(ordr.end_date, {0}) AS "End Date",
      ordr.order_type AS "Type",
      ordr.billing_cycle AS "Billing Cycle", ordr.currency AS "Currency",
      billing_address.street_address_line1 AS "Billing Contact Address Line 1",
      billing_address.street_address_line2 AS "Billing Contact Address Line 2",
      billing_address.city AS "Billing Contact City",
      billing_address.state AS "Billing Contact State",
      billing_address.country AS "Billing Contact Country",
      billing_address.zipcode AS "Billing Contact ZIP",
      shipping_address.street_address_line1 AS "Shipping Contact Address Line 1",
      shipping_address.street_address_line2 AS "Shipping Contact Address Line 2",
      shipping_address.city AS "Shipping Contact City",
      shipping_address.state AS "Shipping Contact State",
      shipping_address.country AS "Shipping Contact Country",
      shipping_address.zipcode AS "Shipping Contact ZIP",
      format_currency_amount(ordr.total_amount) AS "Order TCV",
      ordr.external_subscription_id AS "Subscription ID",
      format_currency_amount(((ordr.metrics->'metrics')->>'deltaTcv')::DECIMAL) AS "Delta TCV",
      format_currency_amount(((ordr.metrics->'metrics')->>'deltaArr')::DECIMAL) AS "Delta ARR",
      COALESCE(cfbo.cfs, ARRAY[]::text[]) as custom_fields
    FROM orders ordr 
        INNER JOIN account acc
          ON ordr.account_id = acc.account_id AND acc.is_deleted = false
        INNER JOIN entity
            ON ordr.entity_id = entity.entity_id AND ordr.tenant_id = entity.tenant_id
        INNER JOIN account_contact billing_contact
          ON ordr.billing_contact_id = billing_contact.contact_id
        INNER JOIN account_address billing_address
          ON billing_contact.address_id = billing_address.address_id
        INNER JOIN account_contact shipping_contact
          ON ordr.shipping_contact_id = shipping_contact.contact_id
        INNER JOIN account_address shipping_address
          ON shipping_contact.address_id = shipping_address.address_id
        INNER JOIN subs
          ON ordr.external_subscription_id = subs.subscription_id
        INNER JOIN account reseller
          ON ordr.billing_contact_id = billing_contact.contact_id AND billing_contact.account_id = reseller.account_id AND ordr.tenant_id = reseller.tenant_id
        LEFT OUTER JOIN cfs_by_order cfbo
          ON ordr.order_id = cfbo.order_id
    ORDER BY ordr.executed_on desc

booking_details:
  report_id: booking_details
  name: Booking Details Report
  description: Bookings details based on order execution date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Posted between
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    SELECT 
        ordr.account_id AS "Account ID",
        entity.display_id AS "Entity Display ID",
        acc.name AS "Account Name", 
        acc.crm_id AS "CRM ID",
        CASE WHEN reseller.is_reseller
          THEN reseller.account_id
          ELSE null
        END AS "Reseller ID",
        CASE WHEN reseller.is_reseller
          THEN reseller.name
          ELSE null
        END AS "Reseller Name",
        ordr.order_id AS "Order ID", 
        ordr.name AS "Order Name",
        ordr.sfdc_opportunity_id AS "Opportunity ID",
        oppor.name AS "Opportunity Name",
        ordr.external_subscription_id AS "Subscription ID",
        format_date(ordr.executed_on, {0}) AS "Close Date",
        format_date(ordr.start_date, {0}) AS "Order Start Date",
        format_end_date(ordr.end_date, {0}) AS "Order End Date",
        format_date(ol.effective_date, {0}) AS "Effective Date",
        format_end_date(ol.end_date, {0}) AS "End Date",
        CASE WHEN ordr.term_length_cycle = 'MONTH'
          THEN ordr.term_length_step / 12::float
          ELSE ordr.term_length_step
        END as "Term Length in Year", 
        CASE WHEN ol.is_ramp THEN 'Yes' ELSE 'No' END AS "Ramp",
        ordr.order_type AS "Order Type",
        ol.action AS "Line Item Type",
        ordr.billing_cycle AS "Billing Cycle",
        ordr.currency AS "Currency",
        pcat.name as "Product Category",
        prdct.product_id as "Product ID",
        prdct.name as "Product Name",
        ol.plan_id as "Plan ID",
        pl.name as "Plan Name",
        ol.replaces_plan_id as "Replaced Plan ID",
        ol.charge_id as "Charge ID",
        chg.name as "Charge Name",
        CASE WHEN ol.list_unit_price_before_override IS NULL THEN NULL ELSE TRUNC(ol.list_unit_price_before_override, 5) END AS "Listed Unit Price (Before Override)",
        TRUNC(ol.list_unit_price,5) as "Listed Unit Price",
        TRUNC(ol.sell_unit_price,5) as "Sell Unit Price",
        CASE WHEN ol.list_amount_before_override IS NULL THEN NULL ELSE format_currency_amount(ol.list_amount_before_override) END AS "Listed Amount (Before Override)",
        format_currency_amount(ol.list_amount) AS "Listed Amount",
        format_currency_amount(ol.list_amount - ol.amount) AS "Discount Amount",
        format_currency_amount(ol.amount) AS "Sell Amount",
        ol.quantity AS "Quantity",
        format_currency_amount(((ol.metrics->'metrics')->>'tcv')::DECIMAL) AS "Order Line TCV",
        format_currency_amount(((ol.metrics->'metrics')->>'arr')::DECIMAL) AS "Order Line ARR",
        format_currency_amount(((ol.metrics->'metrics')->>'averageArr')::DECIMAL) AS "Order Line Average ARR",
        format_currency_amount(((ol.metrics->'metrics')->>'deltaArr')::DECIMAL) AS "Order Line Delta ARR"
    FROM account_order_line_item ol
         LEFT OUTER JOIN account_order ordr
                         ON ordr.order_id = ol.order_id AND ordr.tenant_id = ol.tenant_id
         LEFT OUTER JOIN account acc
                         ON ordr.account_id = acc.account_id AND ordr.tenant_id = acc.tenant_id
         LEFT OUTER JOIN entity
                         ON ordr.entity_id = entity.entity_id AND ordr.tenant_id = entity.tenant_id
         LEFT OUTER JOIN account_contact billing_contact
                         ON ordr.billing_contact_id = billing_contact.contact_id AND ordr.tenant_id = billing_contact.tenant_id
         LEFT OUTER JOIN account reseller
                         ON billing_contact.account_id = reseller.account_id
                             AND billing_contact.tenant_id = reseller.tenant_id
         LEFT OUTER JOIN charge chg
                         ON ol.charge_id = chg.charge_id
                             AND ol.tenant_id = chg.tenant_id
         LEFT OUTER JOIN plan pl
                         ON chg.plan_id = pl.id
                             AND chg.tenant_id = pl.tenant_id
         LEFT OUTER JOIN product prdct
                         ON pl.product_id = prdct.product_id
                             AND pl.tenant_id = prdct.tenant_id
         LEFT OUTER JOIN product_category pcat
                         ON prdct.product_category_id = pcat.product_category_id
                             AND prdct.tenant_id = pcat.tenant_id
         LEFT OUTER JOIN opportunity oppor
                         ON ordr.sfdc_opportunity_id = oppor.crm_id
                             AND ordr.tenant_id = oppor.tenant_id
                             AND oppor.is_deleted = false 
    WHERE ordr.tenant_id = {1}
          AND ordr.entity_id IN {entity_ids_expression}
          AND ordr.is_deleted = false
          AND ol.is_deleted = false
          AND ol.action <> 'NONE'
          AND ordr.status = 'EXECUTED'
          AND (ol.metrics->>'recompute')::bool = false
          AND trunc(extract(epoch from ordr.executed_on)) BETWEEN {2} AND {3}
    ORDER BY ordr.executed_on desc

order_line_details:
  report_id: order_line_details
  name: Order Line Details Report
  description: Order Line details based on order start date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Posted between
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    SELECT
        ordr.account_id AS "Account ID",
        entity.display_id AS "Entity Display ID",
        acc.name AS "Account Name",
        acc.crm_id AS "CRM ID",
        ordr.order_id AS "Order ID",
        ordr.name AS "Order Name",
        ordr.status AS "Order Status",
        ordr.sfdc_opportunity_id AS "Opportunity ID",
        oppor.name AS "Opportunity Name",
        CASE WHEN oppor.primary_order_id = ordr.order_id THEN 'YES' ELSE 'NO' END AS "Is Primary Order",
        ordr.external_subscription_id AS "Subscription ID",    
        format_date(ordr.executed_on, {0}) AS "Order Executed On",
        format_date(ordr.start_date, {0}) AS "Order Start Date",
        format_end_date(ordr.end_date, {0}) AS "Order End Date",
        format_date(ol.effective_date, {0}) AS "Order Line Start Date",
        format_end_date(ol.end_date, {0}) AS "Order Line End Date",
        CASE WHEN ordr.term_length_cycle = 'MONTH'
             THEN ordr.term_length_step / 12::float
             ELSE ordr.term_length_step
        END as "Term Length in Year",    
        CASE WHEN ol.is_ramp THEN 'Yes' ELSE 'No' END AS "Ramp",
        ordr.order_type AS "Order Type",
        ordr.billing_cycle AS "Billing Cycle",
        ordr.currency AS "Currency",
        pcat.name as "Product Category",
        prdct.product_id as "Product ID",
        prdct.name as "Product Name",
        ol.plan_id as "Plan ID",
        pl.name as "Plan Name",
        ol.replaces_plan_id as "Replaced Plan ID",
        ol.charge_id as "Charge ID",
        chg.name as "Charge Name",
        CASE WHEN ol.list_unit_price_before_override IS NULL THEN NULL ELSE TRUNC(ol.list_unit_price_before_override, 5) END AS "Listed Unit Price (Before Override)",
        TRUNC(ol.list_unit_price,5) as "Listed Unit Price",
        TRUNC(ol.sell_unit_price,5) as "Sell Unit Price",
        CASE WHEN ol.list_amount_before_override IS NULL THEN NULL ELSE format_currency_amount(ol.list_amount_before_override) END AS "Listed Amount (Before Override)",
        format_currency_amount(ol.list_amount) AS "Listed Amount",
        format_currency_amount(ol.list_amount - ol.amount) AS "Discount Amount",
        format_currency_amount(ol.amount) AS "Sell Amount",
        ol.quantity AS "Quantity",
        format_currency_amount(((ol.metrics->'metrics')->>'tcv')::DECIMAL) AS "Order Line TCV",
        format_currency_amount(((ol.metrics->'metrics')->>'arr')::DECIMAL) AS "Order Line ARR",
        format_currency_amount(((ol.metrics->'metrics')->>'averageArr')::DECIMAL) AS "Order Line Average ARR",
        format_currency_amount(((ol.metrics->'metrics')->>'deltaArr')::DECIMAL) AS "Order Line Delta ARR"
    FROM account_order_line_item ol
         LEFT OUTER JOIN account_order ordr
            ON ordr.order_id = ol.order_id AND ordr.tenant_id = ol.tenant_id
        LEFT OUTER JOIN account acc
            ON ordr.account_id = acc.account_id AND ordr.tenant_id = acc.tenant_id
        LEFT OUTER JOIN entity
            ON ordr.entity_id = entity.entity_id AND ordr.tenant_id = entity.tenant_id
         LEFT OUTER JOIN charge chg
                         ON ol.charge_id = chg.charge_id
                             AND ol.tenant_id = chg.tenant_id
         LEFT OUTER JOIN plan pl
                         ON chg.plan_id = pl.id
                             AND chg.tenant_id = pl.tenant_id
         LEFT OUTER JOIN product prdct
                         ON pl.product_id = prdct.product_id
                             AND pl.tenant_id = prdct.tenant_id
         LEFT OUTER JOIN product_category pcat
                         ON prdct.product_category_id = pcat.product_category_id
                             AND prdct.tenant_id = pcat.tenant_id
         LEFT OUTER JOIN opportunity oppor
                         ON ordr.sfdc_opportunity_id = oppor.crm_id
                             AND ordr.tenant_id = oppor.tenant_id 
                             AND oppor.is_deleted = false
    WHERE ordr.tenant_id = {1}
        AND ordr.entity_id IN {entity_ids_expression}
        AND ordr.is_deleted = false
        AND ol.is_deleted = false
        AND ol.action <> 'NONE'
        AND (ol.metrics->>'recompute')::bool = false
        AND trunc(extract(epoch from ordr.start_date)) BETWEEN {2} AND {3}
    ORDER BY ordr.start_date desc

active_subscriptions:
  report_id: active_subscriptions
  name: Active Subscriptions Report
  description: Active subscriptions based on subscription start date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  bindings:
    - timezone
    - tenant_id
  query: >
    WITH subs as (SELECT DISTINCT ON (sub.subscription_id) sub.subscription_id AS "Subscription ID",
            entity.display_id AS "Entity Display ID",
            sub.account_id AS "Account ID", acc.name AS "Account Name", acc.crm_id AS "CRM ID",
            sub.currency AS "Currency",
            sub.state AS "State",
            format_date(sub.start_date, {0}) AS "Subscription Start Date",
            format_end_date(sub.end_date, {0}) AS "Subscription End Date",
            format_currency_amount(((subm.metrics->'metrics')->>'tcv')::DECIMAL) AS "Subscription TCV",
            format_currency_amount(((subm.metrics->'metrics')->>'exitArr')::DECIMAL) AS "Exit ARR",
            format_currency_amount(((acc.metrics->'metrics')->>'tcv')::DECIMAL) AS "Account TCV",
            subm.metrics as "Subscription Metrics"
        FROM subscription sub
            INNER JOIN subscription_metrics subm
                on sub.id = subm.id AND (subm.metrics->>'recompute')::bool = false
            INNER JOIN account acc
                on sub.account_id = acc.account_id AND sub.tenant_id = acc.tenant_id AND (acc.metrics->>'recompute')::bool = false
            INNER JOIN entity
                ON sub.entity_id = entity.entity_id AND sub.tenant_id = entity.tenant_id
        WHERE sub.tenant_id = {1} AND CURRENT_DATE BETWEEN sub.start_date AND sub.end_date AND sub.is_deleted = false
          AND sub.entity_id IN {entity_ids_expression}
        ORDER BY sub.subscription_id desc, sub.version desc)
    SELECT "Account ID", "Entity Display ID", "Account Name", "CRM ID", "Subscription ID", "Currency", "Subscription Start Date", "Subscription End Date",
      "Subscription TCV", "Exit ARR", "Account TCV", "Subscription Metrics" from subs
    WHERE "State" = 'ACTIVE'
    ORDER BY subs."Subscription Start Date" asc
  compute_only_cols:
    - Subscription Metrics

all_subscriptions:
  report_id: all_subscriptions
  name: Subscriptions Report
  description: List of subscriptions that start within the specified date range ordered by start date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Subscription Start Date Between
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    WITH subs as (SELECT DISTINCT ON (sub.subscription_id) sub.subscription_id AS "Subscription ID",
            acc.exclude_from_batch_operations AS "Bulk Inv Excluded",
            acc.exclude_from_dunning AS "Dunning Excluded",
            sub.account_id AS "Account ID", 
            entity.display_id AS "Entity Display ID",
            acc.name AS "Account Name",
            acc.crm_id AS "CRM ID",
            sub.currency AS "Currency",
            sub.state AS "Status",
            format_date(sub.start_date, {0}) AS "Subscription Start Date",
            format_end_date(sub.end_date, {0}) AS "Subscription End Date",
            format_date(sub.canceled_date, {0}) AS "Canceled Date",
            format_date(sub.created_on, {0}) AS "Created On",
            format_date(renewed_from_date, {0}) AS "Renewed From Date",
            format_date(renewed_to_date, {0}) AS "Renewed To Date",
            po_required_for_invoicing AS "PO Required",
            auto_renew AS "Auto Renew",
            payment_term AS "Payment Term",
            billing_term AS "Billing Term",
            billing_cycle AS "Billing Cycle",
            billing_step AS "Billing Step",
            format_currency_amount(((subm.metrics->'metrics')->>'tcv')::DECIMAL) AS "Subscription TCV",
            format_currency_amount(((subm.metrics->'metrics')->>'entryArr')::DECIMAL) AS "Entry ARR",
            format_currency_amount(((subm.metrics->'metrics')->>'exitArr')::DECIMAL) AS "Exit ARR",
            format_currency_amount(((subm.metrics->'metrics')->>'deltaArr')::DECIMAL) AS "Delta ARR",
            format_currency_amount(((acc.metrics->'metrics')->>'tcv')::DECIMAL) AS "Account TCV",
            subm.metrics as "Subscription Metrics"
        FROM subscription sub
            INNER JOIN subscription_metrics subm
                on sub.id = subm.id AND (subm.metrics->>'recompute')::bool = false
            INNER JOIN account acc
                on sub.account_id = acc.account_id AND sub.tenant_id = acc.tenant_id AND (acc.metrics->>'recompute')::bool = false
            INNER JOIN entity
                ON sub.entity_id = entity.entity_id AND sub.tenant_id = entity.tenant_id
        WHERE sub.tenant_id = {1} AND trunc(extract(epoch from sub.start_date)) BETWEEN {2} AND {3} AND sub.is_deleted = false
          AND sub.entity_id IN {entity_ids_expression}
        ORDER BY sub.subscription_id desc, sub.version desc)
    SELECT "Bulk Inv Excluded",
           "Dunning Excluded",
           "Account ID",
           "Entity Display ID",
           "Account Name",
           "CRM ID",
           "Subscription ID",
           "Currency",
           "Status",
           "Subscription Start Date",
           "Subscription End Date",
           "Canceled Date",
           "Created On",
           "Renewed From Date",
           "Renewed To Date",
           "PO Required",
           "Auto Renew",
           "Payment Term",
           "Billing Term",
           "Subscription TCV",
           "Entry ARR",
           "Exit ARR",
           "Delta ARR",
           "Account TCV",
           "Subscription Metrics",
           "Billing Cycle",
           "Billing Step" from subs
    ORDER BY subs."Subscription Start Date" asc
  compute_only_cols:
    - Subscription Metrics
    - Billing Step
    - Billing Cycle
  fields:
    - outputName: ARR (now)
      transform: subscriptionArr
    - outputName: Billing Cycle
      transform: billingCycle

subscription_details:
  report_id: subscription_details
  name: Subscription Details Report
  description: Subscription details based on subscription start date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Starts between
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    WITH sub as (SELECT DISTINCT ON (subscr.subscription_id) subscr.subscription_id,
          subscr.id,
          subscr.entity_id,
          subscr.tenant_id,
          subscr.version,
          subscr.account_id,
          subscr.start_date,
          subscr.end_date,
          subscr.currency,
          subscr.state,
          subscr.term_length_cycle,
          subscr.term_length_step
        FROM subscription subscr
        WHERE subscr.tenant_id = {1} AND subscr.is_deleted = false
          AND subscr.entity_id IN {entity_ids_expression}
        ORDER BY subscr.subscription_id, subscr.version DESC)
    SELECT
        sub.subscription_id AS "Subscription ID",
        entity.display_id AS "Entity Display ID",
        sub.account_id AS "Account ID",
        acc.name AS "Account Name",
        acc.crm_id AS "CRM ID",
        format_date(sub.start_date, {0}) AS "Subscription Start Date",
        format_end_date(sub.end_date, {0}) AS "Subscription End Date",
        sub.state AS "State",
        CASE WHEN sub.term_length_cycle = 'MONTH'
             THEN sub.term_length_step / 12::float
             ELSE sub.term_length_step
        END as "Term Length in Year", 
        pcat.name as "Product Category",
        prdct.product_id as "Product ID",
        prdct.name as "Product Name",
        pl.plan_id as "Plan ID",
        pl.name as "Plan Name",
        chg.charge_id as "Charge ID",
        chg.name as "Charge Name",
        format_date(subscr_chg.start_date, {0}) AS "Line Start Date",
        format_end_date(subscr_chg.end_date, {0}) AS "Line End Date",
        subscr_chg.quantity AS "Quantity",
        sub.currency AS "Currency",
        TRUNC(subscr_chg.sell_unit_price,5) as "Sell Unit Price"
    FROM sub
        LEFT OUTER JOIN subscription_charge subscr_chg
            ON sub.subscription_id = subscr_chg.subscription_id AND sub.tenant_id = subscr_chg.tenant_id AND subscr_chg.is_deleted = false
        JOIN subscription_charge_map sub_chg_map
            ON sub.id = sub_chg_map.subscription_id AND subscr_chg.tenant_id = sub_chg_map.tenant_id AND subscr_chg.version = sub_chg_map.version
              AND sub_chg_map.subscription_charge_group_id = subscr_chg.subscription_charge_group_id AND sub_chg_map.is_deleted = false
        LEFT OUTER JOIN account acc
            ON sub.account_id = acc.account_id AND sub.tenant_id = acc.tenant_id
        LEFT OUTER JOIN entity
            ON sub.entity_id = entity.entity_id AND sub.tenant_id = entity.tenant_id
        LEFT OUTER JOIN charge chg
            ON subscr_chg.charge_id = chg.charge_id AND subscr_chg.tenant_id = chg.tenant_id
        LEFT OUTER JOIN plan pl
            ON chg.plan_id = pl.id AND chg.tenant_id = pl.tenant_id
        LEFT OUTER JOIN product prdct
            ON pl.product_id = prdct.product_id AND pl.tenant_id = prdct.tenant_id
        LEFT OUTER JOIN product_category pcat
            ON prdct.product_category_id = pcat.product_category_id AND prdct.tenant_id = pcat.tenant_id
    WHERE sub.tenant_id = {1}
        AND trunc(extract(epoch from sub.start_date)) BETWEEN {2} AND {3}
    ORDER BY sub.start_date DESC

upcoming_renewals:
  report_id: upcoming_renewals
  name: Upcoming Renewals Report
  description: Active subscriptions without existing renewal orders based on subscription end date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Ending between
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    WITH subs as (SELECT DISTINCT ON (sub.subscription_id) 
            sub.subscription_id AS "Subscription ID",
            sub.account_id AS "Account ID", 
            entity.display_id AS "Entity Display ID",
            acc.name AS "Account Name",
            acc.crm_id AS "CRM ID",
            sub.currency AS "Currency",
            sub.renewed_to_subscription_id,
            sub.canceled_date,
            format_date(sub.start_date, {0}) AS "Subscription Start Date",
            format_end_date(sub.end_date, {0}) AS "Subscription End Date",
            format_currency_amount(((subm.metrics->'metrics')->>'tcv')::DECIMAL) AS "Subscription TCV",
            format_currency_amount(((subm.metrics->'metrics')->>'exitArr')::DECIMAL) AS "Exit ARR",
            sub.id as "subscription_id", subm.metrics as "Subscription Metrics"
        FROM subscription sub
            INNER JOIN subscription_metrics subm
                on sub.id = subm.id AND (subm.metrics->>'recompute')::bool = false
            INNER JOIN account acc
                on sub.account_id = acc.account_id
            INNER JOIN entity
                ON sub.entity_id = entity.entity_id AND sub.tenant_id = entity.tenant_id
        WHERE sub.tenant_id = {1} AND sub.is_deleted = false
            AND sub.entity_id IN {entity_ids_expression}
            AND trunc(extract(epoch from sub.end_date)) BETWEEN {2} AND {3}
        ORDER BY sub.subscription_id desc, sub.version desc)
    SELECT 
      "Account ID", "Entity Display ID", "Account Name", "CRM ID",
      "Subscription ID", "Currency", 
      "Subscription Start Date", 
      "Subscription End Date", 
      "Subscription TCV", 
      "Exit ARR" from subs
    WHERE subs.renewed_to_subscription_id IS NULL AND subs.canceled_date IS NULL
    ORDER BY "Subscription End Date" asc

payments_v3:
  report_id: payments_v3
  name: Successful Payments Report
  description: List of successful payment transactions based on payment execution date
  filters:
    - name: duration
      description: Posted between
      type: range
      datatype: date
  query: >
    SELECT
        sa.customer_account_id AS "Account ID", 
        entity.display_id AS "Entity Display ID",
        acc.name AS "Account Name",
        acc.crm_id AS "CRM ID",
        inv.currency_code AS "Currency",
        entity.functional_currency AS "Functional Currency",
        inv.invoice_number AS "Invoice Number", 
        format_currency_amount(inv.total) AS "Invoice Amount",
        CASE WHEN inv.functional_total IS NULL THEN NULL ELSE format_currency_amount(inv.functional_total) END AS "Functional Invoice Amount",
        format_date(inv.invoice_date, {1}) AS "Invoice Date",
        format_date(inv.due_date, {1}) AS "Invoice Due Date", 
        p.payment_id as "Payment ID",
        format_currency_amount(sa.amount) AS "Payment Amount",
        CASE WHEN sa.functional_amount IS NULL THEN NULL ELSE format_currency_amount(sa.functional_amount) END AS "Functional Payment Amount",
        format_date(sa.applied_on, {1}) AS "Payment Date",
        txr.exchange_rate AS "Payment Exchange Rate",
        format_date(txr.effective_date, {1}) AS "Payment Exchange Rate Date",
        p.lifecycle_type AS "Payment Type",
        pppm.payment_method_id AS "PG Payment Method ID",
        apm.payment_type AS "Payment Method",
        pa.id AS "Payment Attempt ID",
        pa.transaction_id AS "PG Transaction ID"
    FROM settlement_application sa 
         INNER JOIN invoice inv
                    ON sa.invoice_number = inv.invoice_number
                        AND sa.tenant_id = inv.tenant_id
         INNER JOIN payment p
                    ON sa.payment_id = p.payment_id
                        AND sa.tenant_id = p.tenant_id
         LEFT JOIN payment_attempt pa
                   ON pa.payment_id = p.payment_id
                       AND pa.tenant_id = p.tenant_id
         INNER JOIN account_payment_method apm
                    ON p.payment_method_id = apm.id
                        AND p.tenant_id = apm.tenant_id
         LEFT JOIN payment_provider_payment_method pppm
                   ON apm.payment_method_id::uuid = pppm.id
                       AND apm.tenant_id = pppm.tenant_id
         INNER JOIN account acc
                    ON sa.customer_account_id = acc.account_id
                        AND sa.tenant_id = acc.tenant_id
         INNER JOIN entity
                    ON sa.entity_id = entity.entity_id AND sa.tenant_id = entity.tenant_id
        LEFT JOIN transactional_exchange_rate txr
                 ON sa.exchange_rate_id = txr.transactional_exchange_rate_id
                AND sa.tenant_id = txr.tenant_id
        
    WHERE sa.tenant_id = {0}
          AND sa.entity_id IN {entity_ids_expression}
          AND (sa.status IS NULL OR sa.status = 'APPLIED_PAYMENT')
          AND trunc(extract(epoch from sa.updated_on)) BETWEEN {2} AND {3}
    ORDER BY sa.updated_on asc
  bindings:
    - tenant_id
    - timezone
    - duration.start
    - duration.end

payments_bank_account_v3:
  report_id: payments_bank_account_v3
  name: Successful Payments Report
  description: List of successful payment transactions based on payment execution date
  filters:
    - name: duration
      description: Posted between
      type: range
      datatype: date
  query: >
    SELECT
        sa.customer_account_id AS "Account ID", 
        entity.display_id AS "Entity Display ID",
        acc.name AS "Account Name",
        acc.crm_id AS "CRM ID",
        inv.currency_code AS "Currency",
        inv.invoice_number AS "Invoice Number", 
        format_currency_amount(inv.total) AS "Invoice Amount",
        format_date(inv.invoice_date, {1}) AS "Invoice Date",
        format_date(inv.due_date, {1}) AS "Invoice Due Date", 
        p.payment_id as "Payment ID",
        format_currency_amount(sa.amount) AS "Payment Amount",
        format_currency_amount(p.bank_fee) AS "Bank Fees",
        p.payment_bank_account_id AS "Bank Account ID",
        format_date(sa.applied_on, {1}) AS "Payment Date",
        p.lifecycle_type AS "Payment Type",
        pppm.payment_method_id AS "PG Payment Method ID",
        apm.payment_type AS "Payment Method",
        pa.id AS "Payment Attempt ID",
        pa.transaction_id AS "PG Transaction ID",
        entity.functional_currency AS "Functional Currency",
        txr.exchange_rate AS "Exchange Rate",
        format_date(txr.effective_date, {1}) AS "Exchange Rate Date",
        CASE WHEN inv.functional_total IS NULL THEN NULL ELSE format_currency_amount(inv.functional_total) END AS "Functional Invoice Amount",
        CASE WHEN sa.functional_amount IS NULL THEN NULL ELSE format_currency_amount(sa.functional_amount) END AS "Functional Payment Amount"
    FROM settlement_application sa 
         INNER JOIN invoice inv
                    ON sa.invoice_number = inv.invoice_number
                        AND sa.tenant_id = inv.tenant_id
         INNER JOIN payment p
                    ON sa.payment_id = p.payment_id
                        AND sa.tenant_id = p.tenant_id
         LEFT JOIN payment_attempt pa
                   ON pa.payment_id = p.payment_id
                       AND pa.tenant_id = p.tenant_id
         INNER JOIN account_payment_method apm
                    ON p.payment_method_id = apm.id
                        AND p.tenant_id = apm.tenant_id
         LEFT JOIN payment_provider_payment_method pppm
                   ON apm.payment_method_id::uuid = pppm.id
                       AND apm.tenant_id = pppm.tenant_id
         INNER JOIN account acc
                    ON sa.customer_account_id = acc.account_id
                        AND sa.tenant_id = acc.tenant_id
         INNER JOIN entity
                    ON sa.entity_id = entity.entity_id AND sa.tenant_id = entity.tenant_id
        LEFT JOIN transactional_exchange_rate txr
                 ON sa.exchange_rate_id = txr.transactional_exchange_rate_id
                AND sa.tenant_id = txr.tenant_id

    WHERE sa.tenant_id = {0}
          AND sa.entity_id IN {entity_ids_expression}
          AND (sa.status IS NULL OR sa.status = 'APPLIED_PAYMENT')
          AND trunc(extract(epoch from sa.updated_on)) BETWEEN {2} AND {3}
    ORDER BY sa.updated_on asc
  bindings:
    - tenant_id
    - timezone
    - duration.start
    - duration.end

failed_payments_v3:
  report_id: failed_payments_v3
  name: Failed Payments Report
  description: List of failed payment transactions based on payment attempt date
  filters:
    - name: duration
      description: Posted between
      type: range
      datatype: date
  query: >
    SELECT 
        sa.customer_account_id AS "Account ID", 
        entity.display_id AS "Entity Display ID",
        acc.name AS "Account Name",
        acc.crm_id AS "CRM ID",
        inv.currency_code AS "Currency",
        entity.functional_currency AS "Functional Currency",
        sa.invoice_number AS "Invoice Number", 
        format_currency_amount(inv.total) AS "Invoice Amount",
        CASE WHEN inv.functional_total IS NULL THEN NULL ELSE format_currency_amount(inv.functional_total) END AS "Functional Invoice Amount",
        format_date(inv.invoice_date, {1}) AS "Invoice Date",
        format_date(inv.due_date, {1}) AS "Invoice Due Date", 
        p.payment_id as "Payment ID",
        format_currency_amount(sa.amount) AS "Payment Amount",
        CASE WHEN sa.functional_amount IS NULL THEN NULL ELSE format_currency_amount(sa.functional_amount) END AS "Functional Payment Amount",
        format_date(sa.applied_on, {1}) AS "Payment Date",
        txr.exchange_rate AS "Payment Exchange Rate",
        format_date(txr.effective_date, {1}) AS "Payment Exchange Rate Date",
        p.lifecycle_type AS "Payment Type",
        pppm.payment_method_id AS "PG Payment Method ID",
        apm.payment_type AS "Payment Method",
        pa.id AS "Payment Attempt ID",
        pa.transaction_id AS "PG Transaction ID",
        pa.failure_reason AS "Failure Reason"
    FROM settlement_application sa
         INNER JOIN invoice inv
                    ON sa.invoice_number = inv.invoice_number
                        AND sa.tenant_id = inv.tenant_id
         INNER JOIN payment p
                    ON sa.payment_id = p.payment_id
                        AND sa.tenant_id = p.tenant_id
         LEFT JOIN payment_attempt pa
                   ON pa.payment_id = p.payment_id
                       AND pa.tenant_id = p.tenant_id
         INNER JOIN account_payment_method apm
                    ON p.payment_method_id = apm.id
                        AND p.tenant_id = apm.tenant_id
         LEFT JOIN payment_provider_payment_method pppm
                   ON apm.payment_method_id::uuid = pppm.id
                       AND apm.tenant_id = pppm.tenant_id
         INNER JOIN account acc
                    ON sa.customer_account_id = acc.account_id
                        AND sa.tenant_id = acc.tenant_id
         INNER JOIN entity
                    ON sa.entity_id = entity.entity_id AND sa.tenant_id = entity.tenant_id
         LEFT JOIN transactional_exchange_rate txr
                    ON sa.exchange_rate_id = txr.transactional_exchange_rate_id
                      AND sa.tenant_id = txr.tenant_id
    WHERE sa.tenant_id = {0}
          AND sa.entity_id IN {entity_ids_expression}
          AND sa.status = 'FAILED'
          AND p.state = 'FAILED'
          AND trunc(extract(epoch from sa.updated_on)) BETWEEN {2} AND {3}
    ORDER BY sa.updated_on asc
  bindings:
    - tenant_id
    - timezone
    - duration.start
    - duration.end

ar_aging_with_report_date_by_account:
  report_id: ar_aging_with_report_date_by_account
  name: AR Aging Report - By Account
  description: Report of account balances bucketed by overdue amounts
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: report_date
      description: AR aging by account as of date
      type: value
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - report_date
  query: >
    WITH inv_payment AS
             (SELECT sa.tenant_id, sa.customer_account_id, sa.invoice_number, SUM(sa.amount) AS amount
              FROM settlement_application sa
              WHERE sa.tenant_id = {1}
                AND sa.entity_id IN {entity_ids_expression}
                AND date_with_tz(sa.applied_on, {0}) - date_with_tz(epoch_to_timestamp({2}::INT), {0}) <= 0
                AND (sa.status IS NULL OR sa.status = 'APPLIED_PAYMENT')
              GROUP BY sa.tenant_id, sa.customer_account_id, sa.invoice_number),
         inv_balance AS
             (SELECT inv.tenant_id,
                     inv.entity_id,
                     inv.customer_account_id AS account_id,
                     inv.currency_code AS currency,
                     inv.total AS invoice_total,
                     inv.total - COALESCE(inv_payment.amount, 0) AS invoice_balance,
                     date_with_tz(inv.due_date, {0}) AS due_date
              FROM invoice inv
              LEFT OUTER JOIN inv_payment
                ON inv.invoice_number = inv_payment.invoice_number
                  AND inv.tenant_id = inv_payment.tenant_id
              WHERE inv.tenant_id = {1}
                AND inv.entity_id IN {entity_ids_expression}
                AND inv.status in ('POSTED', 'PAID', 'VOIDED')
                AND date_with_tz(inv.invoice_date, {0}) -
                    date_with_tz(epoch_to_timestamp({2}::INT), {0}) <= 0
                AND (inv.voided_date IS NULL OR (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - date_with_tz(inv.voided_date, {0}) < 0))
                AND inv.is_deleted = 'FALSE'),
         acc_inv_balance AS
             (SELECT inv_balance.tenant_id AS tenant_id,
                     inv_balance.entity_id AS entity_id,
                     inv_balance.account_id AS account_id,
                     inv_balance.currency AS currency,
                     SUM(inv_balance.invoice_total) AS total_amount,
                     SUM(inv_balance.invoice_balance) AS total_balance,
                     SUM(inv_balance.invoice_balance)
                       FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date <= 0)) AS balance_current,
                     SUM(inv_balance.invoice_balance)
                       FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date > 0)
                               AND (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date) <= 30) AS balance_1_to_30,
                     SUM(inv_balance.invoice_balance)
                       FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date > 30)
                               AND (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date) <= 60) AS balance_31_to_60,
                     SUM(inv_balance.invoice_balance)
                       FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date > 60)
                               AND (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date) <= 90) AS balance_61_to_90,
                     SUM(inv_balance.invoice_balance)
                       FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date > 90)) AS balance_over_90
              FROM inv_balance
              GROUP BY inv_balance.tenant_id, inv_balance.entity_id, inv_balance.account_id, inv_balance.currency),
         cm_payment AS
             (SELECT sa.tenant_id,
                     sa.customer_account_id,
                     sa.credit_memo_number,
                     SUM(sa.amount) AS credit_application_amount
              FROM settlement_application sa
              WHERE sa.tenant_id = {1}
                AND sa.entity_id IN {entity_ids_expression}
                AND (sa.application_type = 'CREDIT' OR sa.application_type = 'UNAPPLY_CREDIT')
                AND date_with_tz(sa.applied_on, {0}) -
                  date_with_tz(epoch_to_timestamp({2}::INT), {0}) <= 0
                AND (sa.status IS NULL OR sa.status = 'APPLIED_PAYMENT')
              GROUP BY sa.tenant_id, sa.customer_account_id, sa.credit_memo_number),
         cm_refund AS
             (SELECT rf.tenant_id, rf.credit_memo_number, SUM(rf.amount) AS refund_total
              FROM refund rf
              WHERE tenant_id = {1}
                AND rf.entity_id IN {entity_ids_expression}
                AND date_with_tz(rf.refund_date, {0}) -
                    date_with_tz(epoch_to_timestamp({2}::INT), {0}) <= 0
              GROUP BY rf.tenant_id, rf.credit_memo_number),
         cm_balances AS
             (SELECT cm.tenant_id AS tenant_id,
                     cm.entity_id AS entity_id,
                     cm.account_id AS account_id,
                     cm.currency_code AS currency,
                     cm.amount AS credit_memo_total,
                     cm.amount - COALESCE(cm_payment.credit_application_amount, 0) - COALESCE(cm_refund.refund_total, 0) AS credit_memo_balance,
                     date_with_tz(COALESCE(cm.credit_memo_date, cm.posted_date), {0}) AS credit_memo_date
              FROM credit_memo cm
              LEFT OUTER JOIN cm_payment
                ON cm.credit_memo_number = cm_payment.credit_memo_number
                  AND cm.tenant_id = cm_payment.tenant_id
              LEFT OUTER JOIN cm_refund
                ON cm.credit_memo_number = cm_refund.credit_memo_number
                  AND cm.tenant_id = cm_refund.tenant_id
              WHERE cm.tenant_id = {1}
                AND cm.entity_id IN {entity_ids_expression}
                AND cm.status in ('POSTED', 'CLOSED')
                AND date_with_tz(COALESCE(cm.credit_memo_date, cm.posted_date), {0}) - date_with_tz(epoch_to_timestamp({2}::INT), {0}) <= 0
                AND cm.is_deleted = 'FALSE'),
         acc_credit_balance AS
             (SELECT cm_balances.tenant_id AS tenant_id,
                     cm_balances.entity_id AS entity_id,
                     cm_balances.account_id AS account_id,
                     cm_balances.currency AS currency,
                     SUM(cm_balances.credit_memo_total) AS total_credit_amount,
                     SUM(cm_balances.credit_memo_balance) AS total_credit_remaining,
                     SUM(cm_balances.credit_memo_balance)
                       FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date <= 0)) AS credit_current,
                     SUM(cm_balances.credit_memo_balance)
                       FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date > 0)
                         AND (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date) <= 30) AS credit_1_to_30,
                     SUM(cm_balances.credit_memo_balance)
                       FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date > 30)
                         AND (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date) <= 60) AS credit_31_to_60,
                     SUM(cm_balances.credit_memo_balance)
                       FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date > 60)
                         AND (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date) <= 90) AS credit_61_to_90,
                     SUM(cm_balances.credit_memo_balance)
                       FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date > 90)) AS credit_over_90
              FROM cm_balances
              GROUP BY cm_balances.tenant_id, cm_balances.entity_id, cm_balances.account_id, cm_balances.currency),
          selected_entities as 
              (select * from (values {entity_id_values}) as selected_entities(entity_id)),


        acc_currency_combos AS (
            SELECT DISTINCT account_id, currency, tenant_id, entity_id FROM (
              SELECT account_id, currency, tenant_id, entity_id FROM acc_credit_balance
              UNION ALL
              SELECT account_id, currency, tenant_id, entity_id FROM acc_inv_balance
            ) as acc_curr_tenant_entity
        ),

       acc_currency_balance AS (
           SELECT    acc_inv_balance.account_id                                              AS "Invoice Account ID",
                     acc_credit_balance.account_id                                           AS "Credit Account ID",
                     acc_inv_balance.tenant_id                                               AS "Invoice Tenant ID",
                     acc_credit_balance.tenant_id                                            AS "Credit Tenant ID",
                     acc_inv_balance.entity_id                                               AS "Invoice Entity ID",
                     acc_credit_balance.entity_id                                            AS "Credit Entity ID",
                     acc_inv_balance.currency                                                AS "Invoice Currency",
                     acc_credit_balance.currency                                             AS "Credit Currency",
                     format_currency_amount(acc_inv_balance.total_amount)                    AS "Total Invoice Amount",
                     format_currency_amount(acc_inv_balance.total_balance)                   AS "Total Outstanding Balance",
                     format_currency_amount(acc_credit_balance.total_credit_amount)          AS "Total Credit Amount",
                     format_currency_amount(acc_credit_balance.total_credit_remaining)       AS "Total Credit Balance",
                     format_currency_amount(COALESCE(acc_inv_balance.balance_current, 0) -
                                            COALESCE(acc_credit_balance.credit_current, 0))  AS "Current",
                     format_currency_amount(COALESCE(acc_inv_balance.balance_1_to_30, 0) -
                                            COALESCE(acc_credit_balance.credit_1_to_30, 0))  AS "1 - 30 Days",
                     format_currency_amount(COALESCE(acc_inv_balance.balance_31_to_60, 0) -
                                            COALESCE(acc_credit_balance.credit_31_to_60, 0)) AS "31 - 60 Days",
                     format_currency_amount(COALESCE(acc_inv_balance.balance_61_to_90, 0) -
                                            COALESCE(acc_credit_balance.credit_61_to_90, 0)) AS "61 - 90 Days",
                     format_currency_amount(COALESCE(acc_inv_balance.balance_over_90, 0) -
                                            COALESCE(acc_credit_balance.credit_over_90, 0))  AS "Over 90 Days"
           FROM acc_currency_combos
              LEFT JOIN acc_credit_balance
                        ON acc_currency_combos.account_id = acc_credit_balance.account_id
                            AND acc_currency_combos.currency = acc_credit_balance.currency
                            AND acc_currency_combos.tenant_id = acc_credit_balance.tenant_id
                            AND acc_currency_combos.entity_id = acc_credit_balance.entity_id
              LEFT JOIN acc_inv_balance
                        ON acc_currency_combos.account_id = acc_inv_balance.account_id
                            AND acc_currency_combos.currency = acc_inv_balance.currency
                            AND acc_currency_combos.tenant_id = acc_inv_balance.tenant_id
                            AND acc_currency_combos.entity_id = acc_inv_balance.entity_id
           WHERE (acc_inv_balance.total_balance != 0 OR acc_credit_balance.total_credit_remaining != 0)
       )

         (SELECT acc.account_id                                                    AS "Account ID",
           entity.display_id                                                       AS "Entity Display ID",
           acc.name                                                                AS "Account Name",
           acc.crm_id                                                              AS "CRM ID",
           CASE WHEN ( acc_currency_balance."Invoice Currency" IS NULL )
                THEN acc_currency_balance."Credit Currency"
           ELSE acc_currency_balance."Invoice Currency" END                        AS "Currency",
           acc_currency_balance."Total Invoice Amount",
           acc_currency_balance."Total Outstanding Balance",
           acc_currency_balance."Total Credit Amount",
           acc_currency_balance."Total Credit Balance",
           acc_currency_balance."Current",
           acc_currency_balance."1 - 30 Days",
           acc_currency_balance."31 - 60 Days",
           acc_currency_balance."61 - 90 Days",
           acc_currency_balance."Over 90 Days"

         FROM account acc
         INNER JOIN selected_entities
           ON ARRAY[selected_entities.entity_id, '*'] && acc.entity_ids::TEXT[]
         INNER JOIN entity
           ON selected_entities.entity_id = entity.entity_id AND acc.tenant_id = entity.tenant_id
         LEFT OUTER JOIN acc_currency_balance
           ON ( acc.tenant_id = acc_currency_balance."Invoice Tenant ID" OR acc.tenant_id = acc_currency_balance."Credit Tenant ID" )
           AND ( selected_entities.entity_id = acc_currency_balance."Invoice Entity ID" OR selected_entities.entity_id = acc_currency_balance."Credit Entity ID" )
           AND ( acc.account_id = acc_currency_balance."Invoice Account ID" OR acc.account_id = acc_currency_balance."Credit Account ID" )
         WHERE acc.tenant_id = {1}
           AND acc.is_deleted = 'FALSE'
           AND (acc_currency_balance."Total Outstanding Balance" is not null OR acc_currency_balance."Total Credit Balance" is not null )
         ORDER BY entity.display_id ASC, acc.name ASC
       );

ar_aging_with_report_date_by_document:
  report_id: ar_aging_with_report_date_by_document
  name: AR Aging Report - By Document
  description: Report of account balances against individual documents, bucketed by overdue amounts
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: report_date
      description: AR aging by document as of date
      type: value
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - report_date
  query: >
    with cm_payment AS
             (SELECT sa.tenant_id,
                     sa.customer_account_id,
                     sa.credit_memo_number,
                     SUM(sa.amount) AS credit_application_amount
              FROM settlement_application sa
              WHERE sa.tenant_id = {1}
                AND sa.entity_id IN {entity_ids_expression}
                AND (sa.application_type = 'CREDIT' 
                 OR sa.application_type = 'UNAPPLY_CREDIT')
                AND date_with_tz(sa.applied_on
                  , {0}) -
                  date_with_tz(epoch_to_timestamp({2}::INT)
                  , {0}) <= 0
                AND (sa.status IS NULL
                 OR sa.status = 'APPLIED_PAYMENT')
              GROUP BY sa.tenant_id, sa.customer_account_id, sa.credit_memo_number),
         cm_refund AS
             (SELECT rf.tenant_id, rf.credit_memo_number, SUM(rf.amount) AS refund_total
              FROM refund rf
              WHERE tenant_id = {1}
                AND rf.entity_id IN {entity_ids_expression}
                AND date_with_tz(rf.refund_date
                  , {0}) -
                  date_with_tz(epoch_to_timestamp({2}::INT)
                  , {0}) <= 0
              GROUP BY rf.tenant_id, rf.credit_memo_number),
         cm_balances AS
             (SELECT cm.tenant_id                        AS tenant_id,
                     cm.entity_id                        AS entity_id,
                     cm.account_id                       AS account_id,
                     cm.credit_memo_number               AS credit_memo_number,
                     cm.currency_code                    AS currency_code,
                     cm.amount                           AS credit_memo_total,
                     cm.amount - COALESCE(cm_payment.credit_application_amount, 0) -
                     COALESCE(cm_refund.refund_total, 0) AS credit_memo_balance,
                     date_with_tz(COALESCE(cm.credit_memo_date, cm.posted_date),
                         {0})                            AS credit_memo_date
              FROM credit_memo cm
                       LEFT OUTER JOIN cm_payment
                                       ON cm.credit_memo_number = cm_payment.credit_memo_number
                                           AND cm.tenant_id = cm_payment.tenant_id
                       LEFT OUTER JOIN cm_refund
                                       ON cm.credit_memo_number = cm_refund.credit_memo_number
                                           AND cm.tenant_id = cm_refund.tenant_id
              WHERE cm.tenant_id = {1}
                AND cm.entity_id IN {entity_ids_expression}
                AND cm.status in ('POSTED'
                  , 'CLOSED')
                AND date_with_tz(COALESCE (cm.credit_memo_date
                  , cm.posted_date)
                  , {0}) -
                  date_with_tz(epoch_to_timestamp({2}::INT)
                  , {0}) <= 0
                AND cm.is_deleted = 'FALSE'),
         acc_credit_balance AS
             (SELECT cm_balances.tenant_id                AS tenant_id,
                     cm_balances.entity_id                AS entity_id,
                     cm_balances.account_id               AS account_id,
                     cm_balances.credit_memo_number       AS credit_memo_number,
                     cm_balances.currency_code            AS currency_code,
                     SUM(cm_balances.credit_memo_total)   AS total_credit_amount,
                     SUM(cm_balances.credit_memo_balance) AS total_credit_remaining,
                     SUM(cm_balances.credit_memo_balance)
                     FILTER (WHERE (
                         date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date <=
                         0))                              AS credit_current,
                     SUM(cm_balances.credit_memo_balance)
                     FILTER (WHERE
                         (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date >
                          0)
                             AND
                         (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date) <=
                         30)                              AS credit_1_to_30,
                     SUM(cm_balances.credit_memo_balance)
                     FILTER (WHERE
                         (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date >
                          30)
                             AND
                         (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date) <=
                         60)                              AS credit_31_to_60,
                     SUM(cm_balances.credit_memo_balance)
                     FILTER (WHERE
                         (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date >
                          60)
                             AND
                         (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date) <=
                         90)                              AS credit_61_to_90,
                     SUM(cm_balances.credit_memo_balance)
                     FILTER (WHERE (
                         date_with_tz(epoch_to_timestamp({2}::INT), {0}) - cm_balances.credit_memo_date >
                         90))                             AS credit_over_90
              FROM cm_balances
              GROUP BY cm_balances.tenant_id, cm_balances.entity_id, cm_balances.account_id,
                       cm_balances.credit_memo_number, cm_balances.currency_code),
         inv_payment AS
             (SELECT sa.tenant_id, sa.customer_account_id, sa.invoice_number, SUM(sa.amount) AS amount
              FROM settlement_application sa
              WHERE sa.tenant_id = {1}
                AND sa.entity_id IN {entity_ids_expression}
                AND date_with_tz(sa.applied_on
                  , {0}) -
                  date_with_tz(epoch_to_timestamp({2}::INT)
                  , {0}) <= 0
                AND (sa.status IS NULL
                 OR sa.status = 'APPLIED_PAYMENT')
              GROUP BY sa.tenant_id, sa.customer_account_id, sa.invoice_number),
         inv_balance AS
             (SELECT inv.tenant_id,
                     inv.entity_id,
                     inv.customer_account_id                     AS account_id,
                     inv.invoice_number                          AS invoice_number,
                     inv.currency_code                           AS currency_code,
                     inv.total                                   AS invoice_total,
                     inv.total - COALESCE(inv_payment.amount, 0) AS invoice_balance,
                     date_with_tz(inv.due_date, {0})             AS due_date
              FROM invoice inv
                       LEFT OUTER JOIN inv_payment
                                       ON inv.invoice_number = inv_payment.invoice_number
                                           AND inv.tenant_id = inv_payment.tenant_id
              WHERE inv.tenant_id = {1}
                AND inv.entity_id IN {entity_ids_expression}
                AND inv.status in ('POSTED'
                  , 'PAID'
                  , 'VOIDED')
                AND date_with_tz(inv.invoice_date
                  , {0}) -
                  date_with_tz(epoch_to_timestamp({2}::INT)
                  , {0}) <= 0
                AND (inv.voided_date IS NULL
                 OR (date_with_tz(epoch_to_timestamp({2}::INT)
                  , {0}) -
                  date_with_tz(inv.voided_date
                  , {0})
                  < 0))
                AND inv.is_deleted = 'FALSE'),
         acc_inv_balance AS
             (SELECT inv_balance.tenant_id            AS tenant_id,
                     inv_balance.entity_id            AS entity_id,
                     inv_balance.account_id           AS account_id,
                     inv_balance.invoice_number       as invoice_number,
                     inv_balance.currency_code        as currency_code,
                     SUM(inv_balance.invoice_total)   AS total_amount,
                     SUM(inv_balance.invoice_balance) AS total_balance,
                     SUM(inv_balance.invoice_balance)
                     FILTER (WHERE (
                         date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date <=
                         0))                          AS balance_current,
                     SUM(inv_balance.invoice_balance)
                     FILTER (WHERE
                         (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date > 0)
                             AND (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date) <=
                                 30)                  AS balance_1_to_30,
                     SUM(inv_balance.invoice_balance)
                     FILTER (WHERE
                         (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date > 30)
                             AND (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date) <=
                                 60)                  AS balance_31_to_60,
                     SUM(inv_balance.invoice_balance)
                     FILTER (WHERE
                         (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date > 60)
                             AND (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date) <=
                                 90)                  AS balance_61_to_90,
                     SUM(inv_balance.invoice_balance)
                     FILTER (WHERE (date_with_tz(epoch_to_timestamp({2}::INT), {0}) - inv_balance.due_date >
                                    90))              AS balance_over_90
              FROM inv_balance
              GROUP BY inv_balance.tenant_id, inv_balance.entity_id, inv_balance.account_id,
                       inv_balance.invoice_number, inv_balance.currency_code),
         selected_entities as
                 (select * from (values {entity_id_values}) as selected_entities(entity_id))

    select entity.display_id                                                    as "Entity Display ID",
           acc.account_id                                                       as "Account ID",
           acc.name                                                             as "Account Name",
           acc.crm_id                                                           as "CRM ID",
           combined_balance."Document Currency",
           combined_balance."Document Number",
           format_currency_amount(combined_balance."Total Amount")              as "Total Amount",
           format_currency_amount(combined_balance."Total Outstanding Balance") as "Total Outstanding Balance",
           format_currency_amount(COALESCE(combined_balance."Current", 0))      as "Current",
           format_currency_amount(COALESCE(combined_balance."1 - 30 Days", 0))  as "1 - 30 Days",
           format_currency_amount(COALESCE(combined_balance."31 - 60 Days", 0)) as "31 - 60 Days",
           format_currency_amount(COALESCE(combined_balance."61-90 Days", 0))   as "61-90 Days",
           format_currency_amount(COALESCE(combined_balance."Over 90 Days", 0)) as "Over 90 Days"
    from (select tenant_id,
                 entity_id,
                 account_id,
                 invoice_number   as "Document Number",
                 currency_code    as "Document Currency",
                 total_amount     as "Total Amount",
                 total_balance    as "Total Outstanding Balance",
                 balance_current  as "Current",
                 balance_1_to_30  as "1 - 30 Days",
                 balance_31_to_60 as "31 - 60 Days",
                 balance_61_to_90 as "61-90 Days",
                 balance_over_90  as "Over 90 Days"
          from acc_inv_balance
          union
          select tenant_id,
                 entity_id,
                 account_id,
                 credit_memo_number      as "Document Number",
                 currency_code           as "Currency",
                 -total_credit_amount    as "Total Amount",
                 -total_credit_remaining as "Total Outstanding Balance",
                 -credit_current         as "Current",
                 -credit_1_to_30         as "1 - 30 Days",
                 -credit_31_to_60        as "31 - 60 Days",
                 -credit_61_to_90        as "61-90 Days",
                 -credit_over_90         as "Over 90 Days"
          from acc_credit_balance) AS combined_balance
             RIGHT OUTER JOIN account acc on combined_balance.account_id = acc.account_id
             INNER JOIN selected_entities
                        ON selected_entities.entity_id = combined_balance.entity_id
             INNER JOIN entity
                        ON selected_entities.entity_id = entity.entity_id AND combined_balance.tenant_id = entity.tenant_id
    WHERE acc.tenant_id = {1}
      AND acc.is_deleted = 'FALSE'
      AND "Document Number" is not null
      AND ("Total Amount"!=0 OR "Total Outstanding Balance"!=0)
    ORDER BY "Entity Display ID", "Account Name", "Total Amount"::decimal ASC NULLS LAST;

invoice_preview:
  report_id: invoice_preview
  name: Invoice Preview Report
  description: Breakdown of current and future invoices based on billing period start date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Invoice period start date between this time range
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    WITH subs as (SELECT DISTINCT ON (sub.subscription_id) sub.subscription_id, sub.id, sub.account_id, sub.entity_id, sub.tenant_id, sub.currency, sub.payment_term, sub.billing_cycle
                                    FROM subscription sub
                                   WHERE sub.tenant_id = {1} AND sub.is_deleted = false
                                     AND sub.entity_id IN {entity_ids_expression}
                                   ORDER BY sub.subscription_id desc, sub.version desc),
                         ip as (SELECT metrics.id, 
                                      (e->'periodStart')::bigint AS period_start, 
                                      (e->'periodEnd')::bigint AS period_end, 
                                      (case when e->'subtotal' is null then e->'total' else e->'subtotal' end)::DECIMAL as sub_total
                                  FROM subscription_metrics metrics, jsonb_array_elements(metrics.invoice_previews->'invoicePreviews') as e
                                 WHERE metrics.tenant_id = {1} AND metrics.recompute_previews = false
                                   AND metrics.invoice_previews is not null AND (e->'periodStart')::bigint >= {2} AND (e->'periodStart')::bigint < {3}),
                         invoice_line_items_combined_status AS (SELECT inv.subscription_id, inv.currency_code,
                                                                       timestamp_to_epoch(ili.period_start_date) AS start_date,
                                                                       SUM(CASE WHEN inv.status IN ('POSTED', 'PAID') THEN ili.amount ELSE 0 END) AS billed_total,
                                                                       SUM(CASE WHEN inv.status IN ('DRAFT') THEN ili.amount ELSE 0 END) AS draft_total,
                                                                       SUM(CASE WHEN inv.status IN ('CONVERTED') THEN ili.amount ELSE 0 END) AS converted_total
                                                                  FROM invoice inv
                                                                           INNER JOIN invoice_line_item ili
                                                                           ON inv.id = ili.invoice_id
                                                                 WHERE inv.tenant_id = {1}
                                                                   AND inv.status IN ('POSTED', 'PAID', 'DRAFT', 'CONVERTED')
                                                                   AND inv.is_deleted = FALSE
                                                                   AND inv.entity_id IN {entity_ids_expression}
                                                                 GROUP BY inv.subscription_id, inv.currency_code,
                                                                          ili.period_start_date)
                  SELECT subs.subscription_id AS "Subscription ID",
                         subs.account_id as "Account ID",
                         entity.display_id AS "Entity Display ID",
                         acc.name AS "Account Name",
                         acc.crm_id AS "CRM ID",
                         format_epoch_date(ip.period_start, {0}) AS "Billing Period Start Date",
                         format_epoch_end_date(ip.period_end, {0}) AS "Billing Period End Date",
                         subs.currency AS "Currency",
                         format_currency_amount(ip.sub_total) AS "Total Invoice Amount",
                         format_currency_amount(SUM(invoice_line_items_combined_status.billed_total)) AS "Posted Amount",
                         format_currency_amount(SUM(invoice_line_items_combined_status.draft_total)) AS "Draft Amount",
                         format_currency_amount(SUM(invoice_line_items_combined_status.converted_total)) AS "Converted Amount",
                         format_currency_amount(ip.sub_total
                             - (CASE WHEN SUM(invoice_line_items_combined_status.draft_total) IS NULL THEN 0 ELSE SUM(invoice_line_items_combined_status.draft_total) END)
                             - (CASE WHEN SUM(invoice_line_items_combined_status.billed_total) IS NULL THEN 0 ELSE SUM(invoice_line_items_combined_status.billed_total) END)
                             - (CASE WHEN SUM(invoice_line_items_combined_status.converted_total) IS NULL THEN 0 ELSE SUM(invoice_line_items_combined_status.converted_total) END)) AS "Remaining Amount",
                         subs.billing_cycle AS "Billing Cycle",
                         subs.payment_term as "Payment Terms"
                    FROM subs
                             INNER JOIN ip ON subs.id = ip.id
                             LEFT OUTER JOIN invoice_line_items_combined_status
                             ON subs.subscription_id = invoice_line_items_combined_status.subscription_id AND
                                subs.currency = invoice_line_items_combined_status.currency_code AND
                                ip.period_start <= invoice_line_items_combined_status.start_date AND
                                ip.period_end > invoice_line_items_combined_status.start_date
                             INNER JOIN account acc ON subs.account_id = acc.account_id
                             INNER JOIN entity
                             ON subs.entity_id = entity.entity_id AND subs.tenant_id = entity.tenant_id
                   GROUP BY subs.subscription_id, entity.display_id, subs.account_id, acc.name, acc.crm_id, ip.period_start, ip.period_end, subs.currency, ip.sub_total, subs.billing_cycle, subs.payment_term
                   ORDER by subs.subscription_id asc, ip.period_start asc;

invoice_preview_v2:
  report_id: invoice_preview_v2
  name: Invoice Preview Report
  description: Breakdown of current and future invoices based on invoice scheduled date
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Invoice period start date between this time range
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    WITH subs as (SELECT DISTINCT ON (sub.subscription_id) sub.subscription_id, sub.id, sub.account_id, sub.entity_id, sub.tenant_id, sub.currency, sub.payment_term, sub.billing_cycle
                  FROM subscription sub
                  WHERE sub.tenant_id = {1} AND sub.is_deleted = false
                    AND sub.entity_id IN {entity_ids_expression}
                  ORDER BY sub.subscription_id desc, sub.version desc),
         ip as (SELECT metrics.id as id,
                      (e->'periodStart')::bigint AS period_start,
                       (e->'periodEnd')::bigint AS period_end,
                       (e->'periodStart')::bigint AS trigger_on,
                       (case when e->'subtotal' is null then e->'total' else e->'subtotal' end)::DECIMAL as sub_total
                FROM subscription_metrics metrics, jsonb_array_elements(metrics.invoice_previews->'invoicePreviews') as e
                WHERE metrics.tenant_id = {1} AND metrics.recompute_previews = false
                  AND metrics.invoice_previews is not null AND (e->'periodStart')::bigint >= {2} AND (e->'periodStart')::bigint < {3}),
         billed as (SELECT inv.subscription_id, inv.currency_code, timestamp_to_epoch(ili.period_start_date) AS start_date, timestamp_to_epoch(ili.period_end_date) AS end_date, SUM(ili.amount) AS total
                    FROM invoice inv INNER JOIN invoice_line_item ili ON inv.id = ili.invoice_id AND inv.tenant_id = ili.tenant_id LEFT JOIN charge ON ili.charge_id = charge.charge_id AND ili.tenant_id = charge.tenant_id
                    WHERE inv.tenant_id = {1} AND inv.status IN ('POSTED', 'PAID') AND inv.is_deleted = FALSE AND charge.is_deleted = FALSE AND charge.is_event_based = FALSE
                      AND inv.entity_id IN {entity_ids_expression}
                    GROUP BY inv.subscription_id, inv.currency_code, ili.period_start_date, ili.period_end_date),
         draft as (SELECT inv.subscription_id, inv.currency_code, timestamp_to_epoch(ili.period_start_date) AS start_date, timestamp_to_epoch(ili.period_end_date) AS end_date, SUM(ili.amount) AS total
                   FROM invoice inv INNER JOIN invoice_line_item ili ON inv.id = ili.invoice_id AND inv.tenant_id = ili.tenant_id LEFT JOIN charge ON ili.charge_id = charge.charge_id AND ili.tenant_id = charge.tenant_id
                   WHERE inv.tenant_id = {1} AND inv.status = 'DRAFT' AND inv.is_deleted = FALSE AND charge.is_deleted = FALSE AND charge.is_event_based = FALSE
                    AND inv.entity_id IN {entity_ids_expression}
                   GROUP BY inv.subscription_id, inv.currency_code, ili.period_start_date, ili.period_end_date),
         converted as (SELECT inv.subscription_id, inv.currency_code, timestamp_to_epoch(ili.period_start_date) AS start_date, timestamp_to_epoch(ili.period_end_date) AS end_date, SUM(ili.amount) AS total
                       FROM invoice inv INNER JOIN invoice_line_item ili ON inv.id = ili.invoice_id AND inv.tenant_id = ili.tenant_id LEFT JOIN charge ON ili.charge_id = charge.charge_id AND ili.tenant_id = charge.tenant_id
                       WHERE inv.tenant_id = {1} AND inv.status = 'CONVERTED' AND inv.is_deleted = FALSE AND charge.is_deleted = FALSE AND charge.is_event_based = FALSE
                        AND inv.entity_id IN {entity_ids_expression}
                       GROUP BY inv.subscription_id, inv.currency_code, ili.period_start_date, ili.period_end_date),
         event_ip as (SELECT 
                       (SELECT id FROM subscription
                         WHERE subscription_id = memoized_invoice_line_item.subscription_id
                           AND subscription.tenant_id = {1}
                         ORDER BY version DESC LIMIT 1) AS subscription_id,
                    extract(epoch FROM memoized_invoice_line_item.period_start_date)::bigint AS period_start,
                    extract(epoch FROM memoized_invoice_line_item.period_end_date)::bigint AS period_end,
                    extract(epoch FROM memoized_invoice_line_item.trigger_on)::bigint AS trigger_on,
                    SUM(memoized_invoice_line_item.amount) AS total
                FROM memoized_invoice_line_item LEFT JOIN charge ON memoized_invoice_line_item.charge_id = charge.charge_id
                WHERE memoized_invoice_line_item.tenant_id = {1} 
                  AND memoized_invoice_line_item.entity_id IN {entity_ids_expression}
                  AND trunc(extract(epoch from memoized_invoice_line_item.trigger_on)) >= {2}
                  AND memoized_invoice_line_item.is_deleted = false
                  AND trunc(extract(epoch from memoized_invoice_line_item.trigger_on)) < {3} AND memoized_invoice_line_item.is_deleted = FALSE
                  AND charge.tenant_id = {1} AND charge.is_deleted = FALSE AND charge.is_event_based = TRUE
                  GROUP BY subscription_id, period_start,period_end, trigger_on),
         event_billed as (SELECT inv.subscription_id, inv.currency_code, timestamp_to_epoch(ili.period_start_date) AS start_date, timestamp_to_epoch(ili.period_end_date) AS end_date, SUM(ili.amount) AS total
                    FROM invoice inv INNER JOIN invoice_line_item ili ON inv.id = ili.invoice_id AND inv.tenant_id = ili.tenant_id LEFT JOIN charge ON ili.charge_id = charge.charge_id AND ili.tenant_id = charge.tenant_id
                    WHERE inv.tenant_id = {1} AND inv.status IN ('POSTED', 'PAID') AND inv.is_deleted = FALSE AND charge.is_deleted = FALSE AND charge.is_event_based = TRUE
                      AND inv.entity_id IN {entity_ids_expression}
                    GROUP BY inv.subscription_id, inv.currency_code, ili.period_start_date, ili.period_end_date),
         event_draft as (SELECT inv.subscription_id, inv.currency_code, timestamp_to_epoch(ili.period_start_date) AS start_date, timestamp_to_epoch(ili.period_end_date) AS end_date, SUM(ili.amount) AS total
                   FROM invoice inv INNER JOIN invoice_line_item ili ON inv.id = ili.invoice_id AND inv.tenant_id = ili.tenant_id LEFT JOIN charge ON ili.charge_id = charge.charge_id AND ili.tenant_id = charge.tenant_id
                   WHERE inv.tenant_id = {1} AND inv.status = 'DRAFT' AND inv.is_deleted = FALSE AND charge.is_deleted = FALSE AND charge.is_event_based = TRUE
                    AND inv.entity_id IN {entity_ids_expression}
                   GROUP BY inv.subscription_id, inv.currency_code, ili.period_start_date, ili.period_end_date),
         event_converted as (SELECT inv.subscription_id, inv.currency_code, timestamp_to_epoch(ili.period_start_date) AS start_date, timestamp_to_epoch(ili.period_end_date) AS end_date, SUM(ili.amount) AS total
                       FROM invoice inv INNER JOIN invoice_line_item ili ON inv.id = ili.invoice_id AND inv.tenant_id = ili.tenant_id LEFT JOIN charge ON ili.charge_id = charge.charge_id AND ili.tenant_id = charge.tenant_id
                       WHERE inv.tenant_id = {1} AND inv.status = 'CONVERTED' AND inv.is_deleted = FALSE AND charge.is_deleted = FALSE AND charge.is_event_based = TRUE
                        AND inv.entity_id IN {entity_ids_expression}
                       GROUP BY inv.subscription_id, inv.currency_code, ili.period_start_date, ili.period_end_date)
    SELECT subscription_id AS "Subscription ID",
        account_id AS "Account ID",
        entity_display_id AS "Entity Display ID",
        account_name AS "Account Name",
        crm_id AS "CRM ID",
        scheduled_date AS "Scheduled Date",
        currency AS "Currency",
        format_currency_amount(SUM(total_amount)) AS "Total Invoice Amount",
        format_currency_amount(SUM(posted_amount)) AS "Posted Amount",
        format_currency_amount(SUM(draft_amount)) AS "Draft Amount",
        format_currency_amount(SUM(converted_amount)) AS "Converted Amount",
        format_currency_amount(sum(remaining_amount)) AS "Remaining Amount",
        billing_cycle AS "Billing Cycle",
        payment_term AS "Payment Terms"
    FROM 
      ((SELECT subs.subscription_id AS subscription_id,
            subs.account_id AS account_id,
            entity.display_id AS entity_display_id,
            acc.name AS account_name,
            acc.crm_id AS crm_id,
            format_epoch_date(ip.trigger_on, {0}) AS scheduled_date,
            subs.currency AS currency,
            ip.sub_total AS total_amount,
            SUM(billed.total) AS posted_amount,
            SUM(draft.total) AS draft_amount,
            SUM(converted.total) AS converted_amount,
            (ip.sub_total
              - (CASE WHEN SUM(draft.total) IS NULL THEN 0 ELSE SUM(draft.total) END)
              - (CASE WHEN SUM(billed.total) IS NULL THEN 0 ELSE SUM(billed.total) END)
              - (CASE WHEN SUM(converted.total) IS NULL THEN 0 ELSE SUM(converted.total) END)) AS remaining_amount,
            subs.billing_cycle AS billing_cycle,
            subs.payment_term AS payment_term
      FROM subs INNER JOIN ip ON subs.id = ip.id
               LEFT OUTER JOIN billed ON subs.subscription_id = billed.subscription_id 
                AND subs.currency = billed.currency_code 
                AND ip.period_start <= billed.start_date
                AND ip.period_end > billed.start_date
               LEFT OUTER JOIN draft ON subs.subscription_id = draft.subscription_id 
                AND subs.currency = draft.currency_code 
                AND ip.period_start <= draft.start_date
                AND ip.period_end > draft.start_date
               LEFT OUTER JOIN converted ON subs.subscription_id = converted.subscription_id 
                AND subs.currency = converted.currency_code 
                AND ip.period_start <= converted.start_date
                AND ip.period_end > converted.start_date
               INNER JOIN account acc ON subs.account_id = acc.account_id
               INNER JOIN entity
                  ON subs.entity_id = entity.entity_id AND subs.tenant_id = entity.tenant_id
      GROUP BY subs.subscription_id, entity.display_id, subs.account_id, acc.name, acc.crm_id, ip.trigger_on, subs.currency, ip.sub_total, subs.billing_cycle, subs.payment_term)
      UNION
      (SELECT subs.subscription_id AS subscription_id,
              subs.account_id AS account_id,
              entity.display_id AS entity_display_id,
              acc.name AS account_name,
              acc.crm_id AS crm_id,
              format_epoch_date(event_ip.trigger_on, {0}) AS scheduled_date,
              subs.currency AS currency,
              event_ip.total AS total_amount,
              SUM(event_billed.total) AS posted_amount,
              SUM(event_draft.total) AS draft_amount,
              SUM(event_converted.total) AS converted_amount,
              (event_ip.total
                - (CASE WHEN SUM(event_draft.total) IS NULL THEN 0 ELSE SUM(event_draft.total) END)
                - (CASE WHEN SUM(event_billed.total) IS NULL THEN 0 ELSE SUM(event_billed.total) END)
                - (CASE WHEN SUM(event_converted.total) IS NULL THEN 0 ELSE SUM(event_converted.total) END)) AS remaining_amount,
            subs.billing_cycle AS billing_cycle,
            subs.payment_term as payment_term
      FROM subs INNER JOIN event_ip ON subs.id = event_ip.subscription_id
                LEFT OUTER JOIN event_billed ON subs.subscription_id = event_billed.subscription_id
                                    AND subs.currency = event_billed.currency_code
                                    AND event_ip.period_start <= event_billed.start_date
                                    AND event_ip.period_end > event_billed.start_date
                LEFT OUTER JOIN event_draft ON subs.subscription_id = event_draft.subscription_id
                                    AND subs.currency = event_draft.currency_code
                                    AND event_ip.period_start <= event_draft.start_date
                                    AND event_ip.period_end > event_draft.start_date
                LEFT OUTER JOIN event_converted ON subs.subscription_id = event_converted.subscription_id
                                    AND subs.currency = event_converted.currency_code
                                    AND event_ip.period_start <= event_converted.start_date
                                    AND event_ip.period_end > event_converted.start_date
                INNER JOIN account acc ON subs.account_id = acc.account_id
                INNER JOIN entity
                    ON subs.entity_id = entity.entity_id AND subs.tenant_id = entity.tenant_id
      GROUP BY subs.subscription_id, entity.display_id, subs.account_id, acc.name, acc.crm_id, event_ip.trigger_on, subs.currency, event_ip.total, subs.billing_cycle, subs.payment_term)) as previews
    GROUP BY subscription_id, entity_display_id, account_id, account_name, crm_id, scheduled_date, currency, billing_cycle, payment_term
    ORDER BY subscription_id, scheduled_date;

invoice_preview_by_accounting_period:
  report_id: invoice_preview_by_accounting_period
  name: Invoice Preview by Accounting Period Report
  description: Pivoted breakdown of current and future invoice amounts by quarter, based on accounting period
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Invoice period end date between this time range
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  variableLengthField:
    outputName: amounts
    variableLenHeaderQuery: >
      WITH quarters as (SELECT date(i - interval '1 day') as qend FROM
          generate_series(date_trunc('quarter', to_timestamp({2}) + interval '3 months'),
              to_timestamp({3}) + interval '3 months', '3 months') as t(i))
      SELECT array_agg('Quarter Ending ' || to_char(qend, 'yyyy-MM-dd')) as amounts from quarters
  query: >
    WITH subs as (SELECT DISTINCT ON (sub.subscription_id) sub.subscription_id, sub.id, sub.account_id, sub.currency,
                      sub.entity_id, sub.tenant_id,
                      sub.payment_term, sub.billing_cycle
                  FROM subscription sub
                  WHERE sub.tenant_id = {1} AND sub.is_deleted = false
                    AND sub.entity_id IN {entity_ids_expression}
                  ORDER BY sub.subscription_id desc, sub.version desc),
         quarters as (SELECT date(i - interval '1 day') as qend FROM
                   generate_series(date_trunc('quarter', to_timestamp({2}) + interval '3 months'),
                   to_timestamp({3}) + interval '3 months', '3 months') as t(i)),
         ip as (SELECT metrics.subscription_id,
                   date(date_trunc('quarter', to_timestamp((e->'periodStart')::bigint)  + interval '3 months') - interval '1 day') as ap_end,
                   sum((e->'total')::DECIMAL) as amount
                FROM subscription_metrics metrics INNER JOIN subs ON metrics.id = subs.id AND metrics.tenant_id = subs.tenant_id,
                     jsonb_array_elements(metrics.invoice_previews->'invoicePreviews') as e
                WHERE metrics.tenant_id = {1} AND metrics.recompute_previews = false
                    AND metrics.invoice_previews is not null
                    AND (e->'periodStart')::bigint >= {2}  AND (e->'periodStart')::bigint < {3}
                GROUP BY metrics.subscription_id, ap_end),
         invoices_by_period as (SELECT inv.subscription_id,
                date(date_trunc('quarter', ili.period_start_date + interval '3 months') - interval '1 day') as ap_end, sum(ili.amount) as amount
                FROM invoice_line_item ili INNER JOIN invoice inv 
                    ON ili.invoice_id = inv.id AND inv.is_deleted = false AND ili.is_deleted = false AND inv.status IN ('POSTED', 'PAID')
                WHERE ili.tenant_id = {1} AND inv.tenant_id = {1} 
                    AND inv.entity_id IN {entity_ids_expression}
                    AND trunc(extract(epoch from ili.period_start_date)) BETWEEN {2} AND {3}
                GROUP BY inv.subscription_id, ap_end
                ORDER BY subscription_id ASC, ap_end ASC),
         all_ids as (SELECT subscription_id FROM ip UNION SELECT subscription_id FROM invoices_by_period),
         all_quarters as (SELECT all_ids.subscription_id, quarters.qend FROM all_ids CROSS JOIN quarters),
         all_preview_amounts as (SELECT all_quarters.subscription_id, all_quarters.qend, COALESCE(ip.amount, 0::DECIMAL) as amount
                FROM all_quarters LEFT OUTER JOIN ip ON all_quarters.subscription_id = ip.subscription_id AND all_quarters.qend = ip.ap_end),
         all_invoiced_amounts as (SELECT aiq.subscription_id, aiq.qend, COALESCE(ibp.amount, 0::DECIMAL) as amount
            FROM all_quarters aiq LEFT OUTER JOIN invoices_by_period ibp ON 
                 aiq.subscription_id = ibp.subscription_id AND aiq.qend = ibp.ap_end),
         all_unbilled_amounts as (SELECT pa.subscription_id, pa.qend, (pa.amount - COALESCE(ia.amount, 0::DECIMAL)) as amount
             FROM all_preview_amounts pa LEFT OUTER JOIN all_invoiced_amounts ia
                 ON pa.subscription_id = ia.subscription_id AND pa.qend = ia.qend),
         flattened_unbilled_amounts as (SELECT subscription_id, array_agg(format_currency_amount(amount) ORDER BY qend ASC) as amounts,
                format_currency_amount(sum(amount)) as total
                FROM all_unbilled_amounts
                GROUP BY subscription_id),
         flattened_invoiced_amounts as (SELECT subscription_id, array_agg(format_currency_amount(amount) ORDER BY qend ASC) as amounts,
                format_currency_amount(sum(amount)) as total
                FROM all_invoiced_amounts
                GROUP BY subscription_id),
         unbilled_invoices as (SELECT subs.subscription_id AS "Subscription ID", subs.account_id as "Account ID",
                    entity.display_id AS "Entity Display ID",
                    acc.name AS "Account Name", acc.crm_id AS "CRM ID",
                    subs.currency AS "Currency", subs.billing_cycle AS "Billing Cycle", subs.payment_term AS "Payment Terms",
                    'Unbilled' AS "State", fuv.amounts, fuv.total AS "Total Invoice Amounts"
                FROM subs INNER JOIN flattened_unbilled_amounts fuv ON subs.subscription_id = fuv.subscription_id
                    LEFT JOIN entity ON subs.entity_id = entity.entity_id AND subs.tenant_id = entity.tenant_id
                    LEFT JOIN account acc ON subs.account_id = acc.account_id AND subs.tenant_id = acc.tenant_id),
         billed_invoices as (SELECT subs.subscription_id AS "Subscription ID", subs.account_id as "Account ID",
                    entity.display_id AS "Entity Display ID",
                    acc.name AS "Account Name", acc.crm_id AS "CRM ID",
                    subs.currency AS "Currency", subs.billing_cycle AS "Billing Cycle", subs.payment_term AS "Payment Terms",
                    'Billed' AS "State", fia.amounts, fia.total AS "Total Invoice Amounts"
                FROM subs INNER JOIN flattened_invoiced_amounts fia ON subs.subscription_id = fia.subscription_id
                      LEFT JOIN entity ON subs.entity_id = entity.entity_id AND subs.tenant_id = entity.tenant_id
                      LEFT JOIN account acc ON subs.account_id = acc.account_id AND subs.tenant_id = acc.tenant_id)
    SELECT * from unbilled_invoices UNION ALL SELECT * from billed_invoices
    ORDER by "Subscription ID" ASC, "State" ASC

invoice_and_dunning_emails_report_v2:
  report_id: invoice_and_dunning_emails_report_v2
  name: Invoice and Dunning Emails Report
  description: Emails sent within the specified date range
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Ending between
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    WITH eml_contacts AS (
        SELECT id,
                 array_agg(emailSentTo)  AS emailSentTo,
                 array_agg(emailSentCc)  AS emailSentCc,
                 array_agg(emailSentBcc) AS emailSentBcc
          FROM (
            SELECT id,
                   jsonb_array_elements((contacts :: jsonb ->> 'toContacts') :: jsonb) ->> 'email'  AS emailSentTo,
                   jsonb_array_elements((contacts :: jsonb ->> 'ccContacts') :: jsonb) ->> 'email'  AS emailSentCc,
                   jsonb_array_elements((contacts :: jsonb ->> 'bccContacts') :: jsonb) ->> 'email' AS emailSentBcc
              FROM email) AS subquery
         GROUP BY id)
    SELECT eml.id,
           acc.account_id                                                                                         AS "ACCOUNT ID",
           entity.display_id                                                                                      AS "ENTITY DISPLAY ID",
           acc.name                                                                                               AS "ACCOUNT NAME",
           inv.invoice_number                                                                                     AS "INVOICE NUMBER",
           format_date(inv.invoice_date, {0})                                                                     AS "INVOICE DATE",
           format_date(inv.posted_date, {0})                                                                      AS "POSTED DATE",
           format_date(inv.due_date, {0})                                                                         AS "DUE DATE",
           inv.payment_term                                                                                       AS "PAYMENT TERM",
           inv.currency_code                                                                                      AS "CURRENCY",
           format_currency_amount(inv.total)                                                                      AS "INVOICE TOTAL",
           format_currency_amount(coalesce(dun.amount, inv.total))                                                AS "INVOICE BALANCE",
           coalesce(dun.reminder_type, eml.email_type)                                                            AS "EMAIL TYPE",
           eml.status                                                                                             AS "DELIVERY STATUS (BILL TO)",
           to_char(to_timestamp(extract(EPOCH FROM eml.created_on)) AT TIME ZONE {0}, 'YYYY-MM-DD / HH24:MI:SS')  AS "SENT ON",
           (SELECT concat('TO: ', array_to_string(emailSentTo, ',')) || E'\r\n' ||
                   concat('CC: ', array_to_string(emailSentCc, ',')) || E'\r\n' ||
                   concat('BCC: ', array_to_string(emailSentBcc, ','))
              FROM eml_contacts
             WHERE eml.id = eml_contacts.id)                                        AS "SENT TO",
          inv.note AS "NOTES"
      FROM email AS eml
      JOIN invoice AS inv ON inv.invoice_number = eml.entity_id AND inv.tenant_id = eml.tenant_id
      JOIN account AS acc ON inv.customer_account_id = acc.account_id AND inv.tenant_id = acc.tenant_id
      LEFT JOIN entity ON inv.entity_id = entity.entity_id AND inv.tenant_id = entity.tenant_id
      LEFT JOIN dunning AS dun ON dun.email_id = eml.email_id AND dun.tenant_id = eml.tenant_id
     WHERE eml.tenant_id = {1} AND trunc(EXTRACT(epoch FROM eml.created_on)) BETWEEN {2} AND {3} AND coalesce(dun.reminder_type, eml.email_type) != 'DUNNING'
       AND inv.entity_id IN {entity_ids_expression}
     ORDER BY "SENT ON" DESC;

journal_entries:
  report_id: journal_entries
  name: Journal Entry Report
  description: Download journal entries for accounting date range
  query: >
    SELECT 
      acc.account_id AS "Account ID",
      entity.display_id AS "Entity Display ID",
      acc.name AS "Account Name",
      format_date(je.accounting_date, {1}) AS "Accounting Date",
      je.journal_entry_id AS "Journal Entry ID",
      je.source_transaction_type AS "Source Transaction Type",
      je.source_transaction_id AS "Source Transaction Id",
      jl.journal_line_id AS "Journal Line ID",
      jl.journal_line_number AS "Journal Line Number",
      jl.ledger_account_type AS "Ledger Account Type",
      jl.ledger_account_code AS "Ledger Account Code",
      je.currency_code AS "Transaction Currency Code",
      CASE WHEN jl.entry_type = 'DEBIT' THEN format_currency_amount(jl.amount) ELSE null END AS "Txn Debit Amount",
      CASE WHEN jl.entry_type = 'CREDIT' THEN format_currency_amount(jl.amount) ELSE null END AS "Txn Credit Amount",
      format_date(jl.exchange_rate_date, {1}) AS "Exchange Rate Date",
      jl.exchange_rate AS "Exchange Rate",
      entity.functional_currency AS "Functional Currency Code",
      CASE WHEN jl.entry_type = 'DEBIT' THEN
          CASE WHEN jl.functional_amount IS NOT NULL THEN 
              format_currency_amount(jl.functional_amount) 
              ELSE null 
          END
          ELSE null
      END AS "Funct. Debit Amount",
      CASE WHEN jl.entry_type = 'CREDIT' THEN
          CASE WHEN jl.functional_amount IS NOT NULL THEN 
              format_currency_amount(jl.functional_amount) 
              ELSE null 
          END
          ELSE null
      END AS "Funct. Credit Amount",
      jl.plan_id AS "Plan ID",
      pl.name AS "Plan Name",
      jl.charge_id AS "Charge ID",
      ch.name AS "Charge Name",
      ael.subscription_id AS "Subscription ID",
      je.entity_id AS "Entity ID"
    FROM accounting_journal_entry_line jl
        LEFT OUTER JOIN accounting_journal_entry je
                  ON jl.journal_entry_id = je.journal_entry_id AND jl.tenant_id = je.tenant_id
        LEFT OUTER JOIN account acc
                  ON je.account_id = acc.account_id AND je.tenant_id = acc.tenant_id
        LEFT OUTER JOIN accounting_event_log ael
                  ON je.source_event_id::uuid = ael.id AND je.tenant_id = ael.tenant_id
        LEFT OUTER JOIN entity
                  ON je.entity_id = entity.entity_id AND je.tenant_id = entity.tenant_id
        LEFT OUTER JOIN plan pl
                        ON jl.plan_id = pl.plan_id AND jl.tenant_id = pl.tenant_id
        LEFT OUTER JOIN charge ch
                        ON jl.charge_id = ch.charge_id AND jl.tenant_id = ch.tenant_id
    WHERE jl.tenant_id = {0}
    AND jl.entity_id IN {entity_ids_expression}
    AND trunc(extract(epoch from je.accounting_date)) >= {2} 
    AND trunc(extract(epoch from je.accounting_date)) < {3}
    ORDER BY je.accounting_date, ael.source_event_sequence_number, je.journal_entry_id, jl.journal_line_number
  bindings:
    - tenant_id
    - timezone
    - accounting_date.start
    - accounting_date.end
  filters:
    - name: accounting_date
      description: Accounting Date Range
      type: range
      datatype: date
  hide_from_definition: true

journal_entries_summary:
  report_id: journal_entries_summary
  name: Journal Entry Summary by Ledger Account
  description: Summary of journal entries grouped by ledger account type
  query: >
    SELECT 
      jl.ledger_account_type AS "Ledger Account Type",
      jl.ledger_account_code AS "Ledger Account Code",
      format_currency_amount(SUM(CASE
          WHEN jl.entry_type = 'CREDIT' THEN jl.amount * -1
          ELSE jl.amount
      END)) AS "Normalized Amount"
    FROM accounting_journal_entry_line jl
        LEFT OUTER JOIN accounting_journal_entry je
                  ON jl.journal_entry_id = je.journal_entry_id AND jl.tenant_id = je.tenant_id
    WHERE jl.tenant_id = {0}
    AND jl.entity_id IN {entity_ids_expression}
    AND trunc(extract(epoch from je.accounting_date)) >= {1} 
    AND trunc(extract(epoch from je.accounting_date)) < {2}
    GROUP BY jl.ledger_account_type, jl.ledger_account_code
    ORDER BY jl.ledger_account_type, jl.ledger_account_code
  bindings:
    - tenant_id
    - accounting_date.start
    - accounting_date.end
  filters:
    - name: accounting_date
      description: Accounting Date Range
      type: range
      datatype: date
  hide_from_definition: true

catalog_export:
  report_id: catalog_export
  name: Product Catalog
  description: Report on Product Catalog Data
  query: >
    WITH entities AS (
        SELECT * FROM entity WHERE tenant_id = {0}
    ),
    product_entity AS (
        SELECT
            product.product_id AS product_id,
            CASE WHEN product.entity_ids = ARRAY['*'] THEN '*' ELSE string_agg(entities.display_id, '|') END AS entity_display_ids
        FROM product
                 LEFT JOIN entities ON entities.entity_id = ANY(product.entity_ids)
        WHERE product.tenant_id = {0} AND product.is_deleted = FALSE
        GROUP BY product.product_id, product.entity_ids
    ),
    plan_entity AS (
        SELECT
          plan.plan_id AS plan_id,
          CASE WHEN plan.entity_ids = ARRAY['*'] THEN '*' ELSE string_agg(entities.display_id, '|') END AS entity_display_ids
        FROM plan
        LEFT JOIN entities ON entities.entity_id = ANY(plan.entity_ids)
        WHERE plan.tenant_id = {0} AND plan.is_deleted = FALSE
        GROUP BY plan.plan_id, plan.entity_ids
    ),
    charges AS (
        SELECT * FROM charge WHERE tenant_id = {0} AND is_deleted = FALSE
    )
    SELECT 
      prd.product_id AS "Product ID",
      prd.external_id AS "Product External ID",
      prd.name AS "Product Name",
      product_entity.entity_display_ids AS "Product Entities",
      prd.description AS "Product Description",
      prd.sku as "Product SKU",
      category.name AS "Product Category",
      plan.plan_id AS "Plan ID",
      plan.external_id as "Plan External ID",
      plan.name AS "Plan Name",
      plan_entity.entity_display_ids AS "Plan Entities",
      plan.description AS "Plan Description",
      plan.status AS "Plan Status",
      plan.supported_currencies->>0 AS "Currency",
      replaced_by.to_plan_id AS "Plan Replaced By",
      chrg.charge_id AS "Charge ID",
      chrg.external_id AS "Charge External ID",
      chrg.name AS "Charge Name",
      chrg.description AS "Charge Description",
      chrg.charge_type AS "Charge Type",
      chrg.charge_model AS "Charge Model",
      chrg.cycle AS "Charge Cycle",
      chrg.percent AS "Charge Percent",
      chrg.amount AS "Charge Amount",
      chrg.is_custom AS "Is Custom Charge",
      chrg.is_renewable AS "Is Renewable",
      chrg.min_quantity AS "Charge Minimum Quantity",
      chrg.max_quantity AS "Charge Maximum Quantity",
      chrg.default_quantity AS "Charge Default Quantity",
      chrg.duration_in_months AS "Duration In Months",
      chrg.price_tiers AS "Charge Price Tiers",
      chrg.rate_card_id as "Rate Card Id",
      rtc.name as "Rate Card Name",
      chrg.erp_id as "ERP Id",
      chrg.item_code as "Item Code"
    FROM product prd
      LEFT JOIN product_entity
        ON prd.product_id = product_entity.product_id
      LEFT OUTER JOIN product_category category
        ON prd.product_category_id = category.product_category_id AND prd.tenant_id = category.tenant_id AND category.is_deleted = FALSE
      LEFT OUTER JOIN plan
        ON prd.product_id = plan.product_id AND prd.tenant_id = plan.tenant_id AND plan.is_deleted = FALSE
      LEFT OUTER JOIN charges chrg
        ON plan.id = chrg.plan_id AND plan.tenant_id = chrg.tenant_id AND chrg.is_deleted = FALSE
      LEFT OUTER JOIN rate_card rtc
        ON rtc.card_id = chrg.rate_card_id AND chrg.tenant_id = rtc.tenant_id AND rtc.is_deleted = FALSE
      LEFT JOIN plan_entity
        ON plan.plan_id = plan_entity.plan_id
      LEFT OUTER JOIN catalog_relationship replaced_by
        ON plan.tenant_id = replaced_by.tenant_id AND plan.plan_id = replaced_by.from_plan_id AND replaced_by.type = 'IS_REPLACED_BY' AND replaced_by.is_deleted IS FALSE
    WHERE prd.tenant_id = {0} AND prd.is_deleted = FALSE  
    ORDER BY prd.name
  bindings:
    - tenant_id
    - timezone

catalog_export_with_conversion:
  report_id: catalog_export_with_conversion
  name: Product Catalog Conversions for Foreign Exchange
  description: Report on Conversion Rates for Product Catalog
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: report_date
      description: Product Catalog Export as of date
      type: value
      datatype: date
  query: >
    WITH entities AS (
        SELECT * FROM entity WHERE tenant_id = {0}
    ),
    product_entity AS (
        SELECT
            product.product_id AS product_id,
            CASE WHEN product.entity_ids = ARRAY['*'] THEN '*' ELSE string_agg(entities.display_id, '|') END AS entity_display_ids
        FROM product
                 LEFT JOIN entities ON entities.entity_id = ANY(product.entity_ids)
        WHERE product.tenant_id = {0} AND product.is_deleted = FALSE
        GROUP BY product.product_id, product.entity_ids
    ),
    plan_entity AS (
        SELECT
          plan.plan_id AS plan_id,
          CASE WHEN plan.entity_ids = ARRAY['*'] THEN '*' ELSE string_agg(entities.display_id, '|') END AS entity_display_ids
        FROM plan
        LEFT JOIN entities ON entities.entity_id = ANY(plan.entity_ids)
        WHERE plan.tenant_id = {0} AND plan.is_deleted = FALSE
        GROUP BY plan.plan_id, plan.entity_ids
    ),
    currency_conversion AS (
      SELECT
        DISTINCT ON (from_currency, to_currency) *
      FROM currency_conversion_rate
      WHERE tenant_id = {0} AND date_with_tz(effective_date, {1}) - date_with_tz(epoch_to_timestamp({2}::INT), {1}) <= 0 AND is_overridden = false
      ORDER BY from_currency, to_currency, effective_date DESC
    ),
    currency_setting AS (
      SELECT
        currency_type_setting.catalog_currency AS catalog_currency,
        currency_type_setting.plan_currency_setting_type AS plan_currency_setting_type
      FROM currency_type_setting
      WHERE currency_type_setting.tenant_id = {0}
    ),
    charges AS (
        SELECT * FROM charge WHERE tenant_id = {0} AND is_deleted = FALSE
    )

    (
    SELECT
      prd.product_id AS "Product ID",
      prd.name AS "Product Name",
      plan.plan_id AS "Plan ID",
      plan.name AS "Plan Name",
      plan.status AS "Plan Status",
      plan.supported_currencies->>0 AS "Currency",
      currency_setting.catalog_currency AS "Catalog Currency",
      CASE WHEN ( chrg.amount IS NULL OR chrg.amount <= 0 ) THEN format_currency_amount(1.00) ELSE format_currency_amount(chrg.amount / chrg.amount) END AS "Rate Multiplier",
      chrg.charge_id AS "Charge ID",
      chrg.name AS "Charge Name",
      CASE WHEN chrg.amount IS NULL THEN NULL ELSE format_currency_amount(chrg.amount) END AS "Charge Amount",
      chrg.price_tiers::text::jsonb AS "Charge Price Tiers",
      currency_setting.catalog_currency AS "Plan Type"
    FROM product prd
      LEFT JOIN product_entity
        ON prd.product_id = product_entity.product_id
      LEFT OUTER JOIN product_category category
        ON prd.product_category_id = category.product_category_id AND prd.tenant_id = category.tenant_id AND category.is_deleted = FALSE
      LEFT OUTER JOIN plan
        ON prd.product_id = plan.product_id AND prd.tenant_id = plan.tenant_id AND plan.is_deleted = FALSE
      LEFT OUTER JOIN charges chrg
        ON plan.id = chrg.plan_id AND plan.tenant_id = chrg.tenant_id AND chrg.is_deleted = FALSE
      LEFT OUTER JOIN rate_card rtc
        ON rtc.card_id = chrg.rate_card_id AND chrg.tenant_id = rtc.tenant_id AND rtc.is_deleted = FALSE
      LEFT JOIN plan_entity
        ON plan.plan_id = plan_entity.plan_id
      LEFT JOIN currency_setting
        ON currency_setting.plan_currency_setting_type = 'UNIQUE_PLAN_PER_CURRENCY'
    WHERE prd.tenant_id = {0} AND prd.is_deleted = FALSE
    ORDER BY prd.name
    )
    UNION ALL
    (
    SELECT
      prd.product_id AS "Product ID",
      prd.name AS "Product Name",
      concat(plan.plan_id, '_CONVERTED' ) AS "Plan ID",
      plan.name AS "Plan Name",
      plan.status AS "Plan Status",
      currency_conversion.to_currency AS "Currency",
      currency_conversion.from_currency AS "Catalog Currency",
      format_currency_amount(currency_conversion.conversion_rate) AS "Rate Multiplier",
      chrg.charge_id AS "Charge ID",
      chrg.name AS "Charge Name",
      CASE WHEN chrg.amount IS NULL THEN NULL ELSE format_currency_amount(chrg.amount * currency_conversion.conversion_rate) END AS "Charge Amount",
      apply_foreign_exchange(chrg.price_tiers::text::jsonb, currency_conversion.conversion_rate)  AS "Charge Price Tiers",
      CASE WHEN (currency_conversion.to_currency != currency_conversion.from_currency) THEN concat('', 'CONVERTED' ) ELSE NULL END AS "Plan Type"
    FROM product prd
      LEFT JOIN product_entity
        ON prd.product_id = product_entity.product_id
      LEFT OUTER JOIN product_category category
        ON prd.product_category_id = category.product_category_id AND prd.tenant_id = category.tenant_id AND category.is_deleted = FALSE
      LEFT OUTER JOIN plan
        ON prd.product_id = plan.product_id AND prd.tenant_id = plan.tenant_id AND plan.is_deleted = FALSE
      LEFT OUTER JOIN charges chrg
        ON plan.id = chrg.plan_id AND plan.tenant_id = chrg.tenant_id AND chrg.is_deleted = FALSE
      LEFT OUTER JOIN rate_card rtc
        ON rtc.card_id = chrg.rate_card_id AND chrg.tenant_id = rtc.tenant_id AND rtc.is_deleted = FALSE
      LEFT JOIN plan_entity
        ON plan.plan_id = plan_entity.plan_id
      LEFT JOIN currency_conversion
        ON plan.supported_currencies->>0 = currency_conversion.from_currency
    WHERE prd.tenant_id = {0} AND prd.is_deleted = FALSE AND currency_conversion.from_currency is not null
    ORDER BY prd.name
    )
  compute_only_cols:
    - Plan Type
  fields:
    - outputName: Conversion Notes
      transform: planType
  bindings:
    - tenant_id
    - timezone
    - report_date

esignature_status:
  report_id: esignature_status
  name: eSignature Status Report
  description: eSignature requests initiated within the specified date range
  generator: com.subskribe.billy.report.ReportMetricsGenerator
  filters:
    - name: duration
      description: Initiated between
      type: range
      datatype: date
  bindings:
    - timezone
    - tenant_id
    - duration.start
    - duration.end
  query: >
    SELECT 
      ord.order_id AS "Order ID",
      ord.account_id AS "Account ID",
      ord.order_type AS "Order Type",
      ord.status AS "Order Status",
      format_date(ord.start_date, {0}) AS "Order Start Date",
      format_date(ord.end_date, {0}) AS "Order End Date",
      format_date(ord.expires_on, {0}) AS "Order Expiry Date",
      es.status AS "eSignature Status",
      format_date(es.initiated_on, {0}) AS "eSignature Initiated Date",
      es.initiated_by AS "eSignature Initiated By",
      format_date(es.completed_on, {0}) AS "eSignature Completed Date"
    FROM electronic_signature es
      JOIN account_order ord ON es.order_id = ord.order_id
    WHERE es.tenant_id = {1}
      AND trunc(extract(epoch from es.initiated_on)) BETWEEN {2} AND {3}
    ORDER BY es.initiated_on DESC

users:
  report_id: users
  name: Users Report
  description: Report of all users in the system
  bindings:
    - tenant_id
  query: >
    SELECT user_id as "User ID",
           display_name as "Display Name",
           title as "Title",
           normalized_email as "Email",
           phone_number as "Phone Number",
           role as "Role",
           state as "State",
           sso_only as "SSO Only",
           array_to_string(entity_ids, ',') as "Entity IDs"
    FROM tenant_user u
    WHERE u.tenant_id = {0}

plan_replacement:
  report_id: plan_replacement
  name: Plan Replacement Report
  description: Report on plan replacements
  bindings:
    - tenant_id
    - timezone
  query: >
    SELECT
      p.plan_id as "Plan ID",
      p.name as "Plan Name",
      p.product_id as "Product ID",
      p.description as "Plan Description",
      cr.to_plan_id as "Replaced By",
      format_date(cr.created_on, {1}) as "Replacement Date"
    FROM plan p
    LEFT JOIN catalog_relationship cr
      ON p.tenant_id = cr.tenant_id AND p.plan_id = cr.from_plan_id
      AND cr.type = 'IS_REPLACED_BY' and cr.is_deleted IS FALSE
    WHERE p.tenant_id = {0}
    AND cr.tenant_id = {0}
    AND cr.from_plan_id IS NOT NULL -- filter out plans that are not replaced
    AND cr.is_deleted IS FALSE
    AND p.is_deleted IS FALSE
