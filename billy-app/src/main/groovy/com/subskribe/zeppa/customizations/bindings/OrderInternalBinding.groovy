package com.subskribe.zeppa.customizations.bindings

import com.subskribe.billy.currency.SupportedCurrency
import com.subskribe.billy.invoice.model.PaymentTerm
import com.subskribe.billy.order.model.Order
import com.subskribe.billy.shared.enums.BillingTerm
import com.subskribe.billy.shared.enums.Cycle
import com.subskribe.billy.shared.enums.OrderType
import com.subskribe.billy.shared.temporal.DateTimeConverter
import com.subskribe.billy.shared.temporal.Recurrence
import java.time.Instant
import org.apache.commons.collections.CollectionUtils

class OrderInternalBinding {

    private final Order orderSource

    private final AccountBinding accountBinding

    private final Map<String, Object> orderCustomFields

    private final Set<String> predefinedTermNames

    private OrderInternalBinding(AccountBinding accountBinding) {
        this.orderSource = new Order()
        this.orderSource.setAccountId(accountBinding.getId())
        this.accountBinding = accountBinding
        this.orderCustomFields = new HashMap<>()
        this.predefinedTermNames = new HashSet<>()
    }

    static OrderInternalBinding newInternalBinding(AccountBinding accountBinding) {
        return new OrderInternalBinding(accountBinding)
    }

    void setCustomFieldValue(String fieldName, Object value) {
        orderCustomFields.put(fieldName, value)
    }

    void setDefaults(TimeZone timeZone) {
        if (timeZone) {
            orderSource.setStartDate(DateTimeConverter.startOfDayToInstant(timeZone))
        } else {
            orderSource.setStartDate(Instant.now())
        }
        orderSource.setTermLength(new Recurrence(Cycle.YEAR, 1))
        orderSource.setBillingCycle(new Recurrence(Cycle.YEAR, 1))
        orderSource.setCurrency(SupportedCurrency.getDefaultCurrency())
        orderSource.setOrderType(OrderType.NEW)
        orderSource.setBillingTerm(BillingTerm.UP_FRONT)
        orderSource.setPaymentTerm(PaymentTerm.NET30)
        orderSource.setAutoRenew(true)
    }

    void setDuration(Cycle cycle, int step) {
        orderSource.setTermLength(new Recurrence(cycle, step))
    }

    void setUpfrontBilling() {
        orderSource.setBillingCycle(new Recurrence(Cycle.PAID_IN_FULL, 1))
    }

    void setYearlyBilling() {
        orderSource.setBillingCycle(new Recurrence(Cycle.YEAR, 1))
    }

    void setSemiAnnualBilling() {
        orderSource.setBillingCycle(new Recurrence(Cycle.SEMI_ANNUAL, 1))
    }

    void setQuarterlyBilling() {
        orderSource.setBillingCycle(new Recurrence(Cycle.QUARTER, 1))
    }

    void setMonthlyBilling() {
        orderSource.setBillingCycle(new Recurrence(Cycle.MONTH, 1))
    }

    void setPaymentDueIn(int dueInDays) {
        // set payment due in only if it is valid
        PaymentTerm.fromDueInDays(dueInDays).map {orderSource.paymentTerm = it}
    }

    void addPredefinedTerms(List<String> termNames) {
        if (CollectionUtils.isNotEmpty(termNames)) {
            predefinedTermNames.addAll(termNames)
        }
    }

    Map<String, Object> getOrderCustomFields() {
        Collections.unmodifiableMap(orderCustomFields)
    }

    Set<String> getPredefinedTermNames() {
        Collections.unmodifiableSet(predefinedTermNames)
    }

    Order sourceOrder() {
        return orderSource
    }
}
