package com.subskribe.zeppa.customizations.bindings

import static BindingNullSafety.nullSafe
import static com.subskribe.zeppa.customizations.bindings.BindingNullSafety.nullSafeCustomFieldValue
import static com.subskribe.zeppa.customizations.bindings.BindingNullSafety.nullSafeCustomFieldValueMatch
import static java.util.stream.Collectors.toSet
import static org.apache.commons.lang.ArrayUtils.isEmpty

import com.subskribe.billy.order.model.Order
import com.subskribe.billy.order.model.OrderLineItem
import com.subskribe.billy.shared.enums.ActionType
import com.subskribe.zeppa.customizations.language.BooleanSpec
import java.time.Duration
import java.time.Instant
import java.time.ZoneId
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.collections4.MapUtils
import org.apache.commons.lang3.StringUtils

class OrderBinding {

    private static final int APPROX_NUM_DAYS_IN_YEAR = 365

    private static final int INVALID_DURATION = -1

    private final Order order

    private final AccountBinding accountBinding

    private final List<PlanBinding> allPlanBindings

    private final ZoneId timeZoneId

    private final DateBinding startDate

    private final DateBinding endDate

    private final ContactBinding shippingContact

    private final ContactBinding billingContact

    OrderBinding(Order order,
    AccountBinding accountBinding,
    List<PlanBinding> allPlanBindings,
    ZoneId timeZoneId,
    ContactBinding shippingContact,
    ContactBinding billingContact) {
        this.order = order
        this.accountBinding = accountBinding
        this.allPlanBindings = allPlanBindings
        this.timeZoneId = timeZoneId
        this.startDate = DateBinding.from(this.order?.startDate, timeZoneId)
        this.endDate = DateBinding.from(this.order?.endDate, timeZoneId)
        this.shippingContact = shippingContact
        this.billingContact = billingContact
    }

    DateBinding getStartDate() {
        startDate
    }

    DateBinding getEndDate() {
        endDate
    }

    DateBinding getExpiresOn() {
        DateBinding.from(order?.getExpiresOn(), timeZoneId)
    }

    String getName() {
        nullSafe(order?.getName())
    }

    String getType() {
        nullSafe(order?.getOrderType()?.name())
    }

    String getStatus() {
        nullSafe(order?.getStatus()?.name())
    }

    String getCurrency() {
        nullSafe(order?.getCurrency()?.getCurrencyCode())
    }

    String getBillingCycle() {
        nullSafe(order?.getBillingCycle()?.getCycle()?.name())
    }

    boolean getIsAutoRenew() {
        nullSafe(order?.getAutoRenew(), false)
    }

    int getPaymentDueInDays() {
        nullSafe(order?.getPaymentTerm()?.paymentDueInDays, -1)
    }

    int getTermLengthYears() {
        Instant startDate = order?.getStartDate()
        Instant endDate = order?.getEndDate()

        if (startDate && endDate) {
            long days = Duration.between(startDate, endDate).toDays()
            return days < 0 ? INVALID_DURATION : days / APPROX_NUM_DAYS_IN_YEAR
        }

        return INVALID_DURATION
    }

    long getTermLengthDays() {
        Instant startDate = order?.getStartDate()
        Instant endDate = order?.getEndDate()

        if (startDate && endDate) {
            return Duration.between(startDate, endDate).toDays()
        }

        return INVALID_DURATION
    }

    String getCrmOpportunityId() {
        nullSafe(order?.getSfdcOpportunityId())
    }

    String getCompositeOrderId() {
        nullSafe(order?.getCompositeOrderId())
    }

    AccountBinding getAccount() {
        return accountBinding
    }

    OpportunityBinding getOpportunity() {
        if (order && order.opportunity.isPresent()) {
            return OpportunityBinding.from(order.opportunity.get())
        }
        return OpportunityBinding.empty()
    }

    ContactBinding getShippingContact() {
        return shippingContact
    }

    ContactBinding getBillingContact() {
        return billingContact
    }

    boolean getIsResold() {
        boolean billingContactPresent = StringUtils.isNotBlank(billingContact.getAccountId())
        return billingContactPresent && !accountBinding.id.equalsIgnoreCase(billingContact.getAccountId()) && billingContact.account.isReseller
    }

    OrderLineGroupBinding lineItemsMatching(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = OrderLinesMatchSpec) Closure closure) {
        List<OrderLineItem> lineItemsNetEffect = CollectionUtils.isEmpty(order?.getLineItemsNetEffect()) ? [] : order?.getLineItemsNetEffect()
        return runClosure(lineItemsNetEffect, closure)
    }

    OrderLineGroupBinding unmodifiedLineItemsMatching(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = OrderLinesMatchSpec) Closure closure) {
        List<OrderLineItem> lineItemsNotModified =
                CollectionUtils.isEmpty(order?.getLineItems()) ?
                [] :
                order?.getLineItems()?.findAll { lineItem -> lineItem.action == ActionType.NONE}
        return runClosure(lineItemsNotModified, closure)
    }

    boolean anyUnmodifiedLineItemsMatch(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = OrderLinesMatchSpec) Closure closure) {
        unmodifiedLineItemsMatching(closure).lineCount > 0
    }

    int unmodifiedLineItemsMatchingCount(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = OrderLinesMatchSpec) Closure closure) {
        unmodifiedLineItemsMatching(closure).lineCount
    }

    boolean anyLineItemsMatch(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = OrderLinesMatchSpec) Closure closure) {
        runLineMatchClosureAndGetCount(closure) > 0
    }

    int lineItemsMatchingCount(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = OrderLinesMatchSpec) Closure closure) {
        runLineMatchClosureAndGetCount(closure)
    }

    boolean anyAddedPlansMatch(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = PlanMatchSpec) Closure closure) {
        addedPlansMatchCount(closure) > 0
    }

    int addedPlansMatchCount(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = PlanMatchSpec) Closure closure) {
        runClosureAndGetCount(distinctPlanIds(ActionType.ADD), closure)
    }

    boolean anyRenewedPlansMatch(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = PlanMatchSpec) Closure closure) {
        renewedPlansMatchCount(closure) > 0
    }

    int renewedPlansMatchCount(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = PlanMatchSpec) Closure closure) {
        runClosureAndGetCount(distinctPlanIds(ActionType.RENEWAL), closure)
    }

    boolean anyRestructuredPlansMatch(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = PlanMatchSpec) Closure closure) {
        restructuredPlansMatchCount(closure) > 0
    }

    int restructuredPlansMatchCount(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = PlanMatchSpec) Closure closure) {
        runClosureAndGetCount(distinctPlanIds(ActionType.RESTRUCTURE), closure)
    }

    boolean anyModifiedPlansMatch(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = PlanMatchSpec) Closure closure) {
        modifiedPlansMatchCount(closure) > 0
    }

    int modifiedPlansMatchCount(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = PlanMatchSpec) Closure closure) {
        runClosureAndGetCount(distinctPlanIds(ActionType.UPDATE), closure)
    }

    boolean anyRemovedPlansMatch(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = PlanMatchSpec) Closure closure) {
        removedPlansMatchCount(closure) > 0
    }

    int removedPlansMatchCount(@DelegatesTo(strategy = Closure.DELEGATE_ONLY, value = PlanMatchSpec) Closure closure) {
        runClosureAndGetCount(distinctPlanIds(ActionType.REMOVE), closure)
    }

    String customField(String customFieldName) {
        nullSafeCustomFieldValue(order?.customFields, customFieldName)
    }

    boolean customFieldMatches(String customFieldName, String value) {
        nullSafeCustomFieldValueMatch(order?.customFields, customFieldName, value)
    }


    class PlanMatchSpec extends BooleanSpec {
        private PlanBinding planBinding

        PlanBinding getPlan() {
            planBinding
        }
    }

    class OrderLinesMatchSpec extends BooleanSpec {
        private OrderLineItemBinding lineItemBinding
        private OrderBinding orderBinding

        OrderLineItemBinding getLineItem() {
            lineItemBinding
        }

        OrderBinding getOrder() {
            orderBinding
        }
    }

    // methods accessed by runtime and not user in script runtime
    Order sourceOrder() {
        order
    }

    ZoneId orderTimeZoneId() {
        timeZoneId
    }

    Map<String, PlanBinding> planBindingMap() {
        if (CollectionUtils.isEmpty(allPlanBindings)) {
            return Map.of()
        }

        return allPlanBindings.collectEntries {
            [it.id, it]
        }
    }

    Map<String, ChargeBinding> chargeBindingMap() {
        if (CollectionUtils.isEmpty(allPlanBindings)) {
            return Map.of()
        }

        Map<String, ChargeBinding> finalMap = new HashMap<>()
        allPlanBindings.forEach {
            Map<String, ChargeBinding> chargeBindingMap = it.chargeBindingMap()
            if (MapUtils.isNotEmpty(chargeBindingMap)) {
                finalMap.putAll(chargeBindingMap)
            }
        }

        return Collections.unmodifiableMap(finalMap)
    }

    static OrderBinding from(Order order,
            AccountBinding accountBinding,
            List<PlanBinding> planBindings,
            ZoneId zoneId,
            ContactBinding shippingContact = null,
            ContactBinding billingContact = null) {
        new OrderBinding(order,
                accountBinding,
                planBindings,
                zoneId,
                shippingContact ? shippingContact : ContactBinding.empty(),
                billingContact ? billingContact : ContactBinding.empty())
    }

    private List<OrderLineItem> orderLineItems() {
        CollectionUtils.isEmpty(order?.getLineItemsNetEffect()) ? List.of() : order?.getLineItemsNetEffect()
    }

    private Set<String> distinctPlanIds(ActionType... actionTypes) {
        if (isEmpty(actionTypes)) {
            return Set.of()
        }

        Set<ActionType> types = Arrays.stream(actionTypes).distinct().collect(toSet())
        orderLineItems().findAll {
            types.contains(it.action)
        }.collect {
            it.planId
        }.findAll {
            StringUtils.isNotBlank(it)
        }.toSet()
    }

    private int runClosureAndGetCount(Set<String> relevantPlanIds, Closure closure) {
        List<PlanBinding> planBindings = CollectionUtils.isEmpty(allPlanBindings) ? [] : allPlanBindings
        planBindings.findAll {
            relevantPlanIds.contains(it.id)
        }.findAll {
            def planMatchSpec = new PlanMatchSpec()
            // se the plan binding
            planMatchSpec.planBinding = it
            closure.delegate = planMatchSpec
            closure.resolveStrategy = Closure.DELEGATE_ONLY
            closure.call()
            return planMatchSpec.value
        }.size()
    }

    private int runLineMatchClosureAndGetCount(Closure closure) {
        List<OrderLineItem> lineItemsNetEffect = CollectionUtils.isEmpty(order?.getLineItemsNetEffect()) ? [] : order?.getLineItemsNetEffect()

        lineItemsNetEffect.findAll {
            def lineItemMatchSpec = new OrderLinesMatchSpec()
            PlanBinding planBinding = planBindingMap().getOrDefault(it.planId, PlanBinding.empty())
            ChargeBinding chargeBinding = chargeBindingMap().getOrDefault(it.chargeId, ChargeBinding.empty())
            lineItemMatchSpec.lineItemBinding = OrderLineItemBinding.from(it, planBinding, chargeBinding, orderTimeZoneId())
            lineItemMatchSpec.orderBinding = this

            closure.delegate = lineItemMatchSpec
            closure.resolveStrategy = Closure.DELEGATE_ONLY
            closure.call()

            return lineItemMatchSpec.value
        }.size()
    }

    private runClosure(List<OrderLineItem> relevantLineItems, closure) {
        OrderLineGroupBinding groupBinding = OrderLineGroupBinding.from(relevantLineItems.collect {
            def lineItemMatchSpec = new OrderLinesMatchSpec()
            PlanBinding planBinding = planBindingMap().getOrDefault(it.planId, PlanBinding.empty())
            ChargeBinding chargeBinding = chargeBindingMap().getOrDefault(it.chargeId, ChargeBinding.empty())
            lineItemMatchSpec.lineItemBinding = OrderLineItemBinding.from(it, planBinding, chargeBinding, orderTimeZoneId())
            lineItemMatchSpec.orderBinding = this

            closure.delegate = lineItemMatchSpec
            closure.resolveStrategy = Closure.DELEGATE_FIRST
            closure.call()

            return lineItemMatchSpec
        }.findAll {
            it.value
        }.collect {
            it.lineItemBinding
        })
        return groupBinding
    }
}
