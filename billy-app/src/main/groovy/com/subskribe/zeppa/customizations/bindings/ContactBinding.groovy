package com.subskribe.zeppa.customizations.bindings

import static com.subskribe.zeppa.customizations.bindings.BindingNullSafety.nullSafe

import com.subskribe.billy.account.model.AccountContact

class ContactBinding {

    private final AccountContact accountContact

    private final AccountBinding accountBinding

    private ContactBinding(AccountContact accountContact, AccountBinding accountBinding) {
        this.accountContact = accountContact
        this.accountBinding = accountBinding
    }

    String getId() {
        nullSafe(accountContact?.getContactId())
    }

    String getAccountId() {
        nullSafe(accountContact?.getAccountId())
    }

    AccountBinding getAccount() {
        if (accountBinding) {
            return accountBinding
        }
        return AccountBinding.empty()
    }

    static ContactBinding from(AccountContact accountContact, AccountBinding accountBinding) {
        return new ContactBinding(accountContact, accountBinding)
    }

    static ContactBinding empty() {
        return new ContactBinding(null, null)
    }
}
