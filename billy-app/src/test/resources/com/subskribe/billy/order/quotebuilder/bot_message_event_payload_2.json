{"token": "VEGPfp6XuRVdefYiM6BBjS0r", "team_id": "T017910MK7V", "context_team_id": "T017910MK7V", "context_enterprise_id": null, "api_app_id": "A04BTA1J9T2", "event": {"user": "U04AX1NGZ38", "type": "message", "ts": "1742904930.390549", "bot_id": "B04AP3ACJTZ", "app_id": "A04BTA1J9T2", "text": "This is message by app thru test program", "team": "T017910MK7V", "bot_profile": {"id": "B04AP3ACJTZ", "deleted": false, "name": "Subskribe Inc Local", "updated": 1698349667, "app_id": "A04BTA1J9T2", "user_id": "U04AX1NGZ38", "icons": {"image_36": "https://avatars.slack-edge.com/2023-06-28/5500700472930_6dc4d2984e0c51d658b0_36.png", "image_48": "https://avatars.slack-edge.com/2023-06-28/5500700472930_6dc4d2984e0c51d658b0_48.png", "image_72": "https://avatars.slack-edge.com/2023-06-28/5500700472930_6dc4d2984e0c51d658b0_72.png"}, "team_id": "T017910MK7V"}, "thread_ts": "1742904615.924259", "parent_user_id": "U04AX1NGZ38", "blocks": [{"type": "rich_text", "block_id": "iGkq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is message by app thru test program"}]}]}], "channel": "C08G20S9RS8", "event_ts": "1742904930.390549", "channel_type": "channel"}, "type": "event_callback", "event_id": "Ev08JT6JQGGP", "event_time": 1742904930, "authorizations": [{"enterprise_id": null, "team_id": "T017910MK7V", "user_id": "U04AX1NGZ38", "is_bot": true, "is_enterprise_install": false}], "is_ext_shared_channel": false, "event_context": "4-eyJldCI6Im1lc3NhZ2UiLCJ0aWQiOiJUMDE3OTEwTUs3ViIsImFpZCI6IkEwNEJUQTFKOVQyIiwiY2lkIjoiQzA4RzIwUzlSUzgifQ"}