{"token": "VEGPfp6XuRVdefYiM6BBjS0r", "team_id": "T017910MK7V", "context_team_id": "T017910MK7V", "context_enterprise_id": null, "api_app_id": "A04BTA1J9T2", "event": {"user": "U04AX1NGZ38", "type": "message", "ts": "**********.924259", "bot_id": "B04AP3ACJTZ", "app_id": "A04BTA1J9T2", "text": "Hi rakesh:wave: Thanks for the message. I will start reviewing the order request you made below and get back to you in this thread :thread: *I need a quote for an enterprise account that wants our platform and analytics. They need advanced support. they need 1000000 users* Give me a few minutes to take a look. I'll @ you in thread when I've got an update. :subskribe-spinning: :subskribe-spinning: :subskribe-spinning:", "team": "T017910MK7V", "bot_profile": {"id": "B04AP3ACJTZ", "deleted": false, "name": "Subskribe Inc Local", "updated": **********, "app_id": "A04BTA1J9T2", "user_id": "U04AX1NGZ38", "icons": {"image_36": "https://avatars.slack-edge.com/2023-06-28/5500700472930_6dc4d2984e0c51d658b0_36.png", "image_48": "https://avatars.slack-edge.com/2023-06-28/5500700472930_6dc4d2984e0c51d658b0_48.png", "image_72": "https://avatars.slack-edge.com/2023-06-28/5500700472930_6dc4d2984e0c51d658b0_72.png"}, "team_id": "T017910MK7V"}, "blocks": [{"type": "section", "block_id": "MhazH", "text": {"type": "mrkdwn", "text": "Hi rakesh:wave:", "verbatim": false}}, {"type": "section", "block_id": "lfW9k", "text": {"type": "mrkdwn", "text": "Thanks for the message. I will start reviewing the order request you made below and get back to you in this thread :thread:", "verbatim": false}}, {"type": "section", "block_id": "7CUS6", "text": {"type": "mrkdwn", "text": "*I need a quote for an enterprise account that wants our platform and analytics. They need advanced support. they need 1000000 users*", "verbatim": false}}, {"type": "section", "block_id": "hhIZv", "text": {"type": "mrkdwn", "text": "Give me a few minutes to take a look. I'll @ you in thread when I've got an update.", "verbatim": false}}, {"type": "section", "block_id": "79HOK", "text": {"type": "mrkdwn", "text": ":subskribe-spinning: :subskribe-spinning: :subskribe-spinning:", "verbatim": false}}], "channel": "C08G20S9RS8", "event_ts": "**********.924259", "channel_type": "channel"}, "type": "event_callback", "event_id": "Ev08KA2GAQ3V", "event_time": **********, "authorizations": [{"enterprise_id": null, "team_id": "T017910MK7V", "user_id": "U04AX1NGZ38", "is_bot": true, "is_enterprise_install": false}], "is_ext_shared_channel": false, "event_context": "4-eyJldCI6Im1lc3NhZ2UiLCJ0aWQiOiJUMDE3OTEwTUs3ViIsImFpZCI6IkEwNEJUQTFKOVQyIiwiY2lkIjoiQzA4RzIwUzlSUzgifQ"}