{"draftOrderV1": {"id": "1f67b973-3332-458a-9b78-78a89d04184b", "orderId": "1f67b973-3332-458a-9b78-78a89d04184b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "entityId": "test entity id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "subscriptionTargetVersion": 1, "orderType": "NEW", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "startDate": "2024-01-01T00:00:00Z", "paymentTerm": "NET30", "currency": "USD", "billingTerm": "UP_FRONT", "billingAnchorDate": "2024-01-01T00:00:00Z", "status": "DRAFT", "totalAmount": 2000.0, "totalListAmount": 2000.0, "totalListAmountBeforeOverride": 2000.0, "lineItems": [{"planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 10, "discounts": [{"name": "test discount", "percent": 0.1}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"planId": "Tenant 1 Plan 1", "chargeId": "ff313d39-0ac7-4e6c-b711-776f7833f10d", "tenantId": "test tenant 1 id", "quantity": 20, "discounts": [{"name": "test discount", "percent": 0.2}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}], "shouldRegeneratePdf": true, "expiresOn": null}, "draftOrderV2": {"id": "1f67b973-3332-458a-9b78-78a89d04184b", "orderId": "1f67b973-3332-458a-9b78-78a89d04184b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "entityId": "test entity id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "subscriptionTargetVersion": 1, "orderType": "NEW", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2024-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "DRAFT", "billingAnchorDate": "2024-01-01T00:00:00Z", "totalAmount": 3000.0, "totalListAmount": 3000.0, "totalListAmountBeforeOverride": 3000.0, "lineItems": [{"planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 10, "discounts": [{"name": "test discount", "percent": 0.1}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"planId": "Tenant 1 Plan 1", "chargeId": "ff313d39-0ac7-4e6c-b711-776f7833f10d", "tenantId": "test tenant 1 id", "quantity": 10, "discounts": [{"name": "test discount", "percent": 0.1}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"planId": "Tenant 1 Plan 2", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "tenantId": "test tenant 1 id", "quantity": 10, "discounts": [{"name": "test discount", "percent": 0.1}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}], "shouldRegeneratePdf": true, "expiresOn": null}, "submitOrder": {"id": "1f67b973-3332-458a-9b78-78a89d04184b", "orderId": "1f67b973-3332-458a-9b78-78a89d04184b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "entityId": "test entity id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "subscriptionTargetVersion": 1, "orderType": "NEW", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "EXECUTED", "totalAmount": 4000.0, "totalListAmount": 4000.0, "totalListAmountBeforeOverride": 4000.0, "billingAnchorDate": "2024-01-01T00:00:00Z", "lineItems": [{"action": "ADD", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.3}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"action": "ADD", "planId": "Tenant 1 Plan 1", "chargeId": "ff313d39-0ac7-4e6c-b711-776f7833f10d", "tenantId": "test tenant 1 id", "quantity": 20, "discounts": [{"name": "test discount", "percent": 0.4}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"action": "ADD", "planId": "Tenant 1 Plan 2", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.1}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"action": "ADD", "planId": "Tenant 1 Plan 3", "chargeId": "e9f98431-f248-4fbc-be5e-eff37473392a", "tenantId": "test tenant 1 id", "quantity": 50, "discounts": [{"name": "test discount", "percent": 0.2}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}], "shouldRegeneratePdf": true, "expiresOn": null}, "expectedSubscriptionV1": {"subscriptionEntity": {"id": "7a6a0a2d-6248-4804-9125-cffeecbdb537", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "entityId": "test entity id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "state": "ACTIVE", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "paymentTerm": "NET30", "currency": "USD", "billingTerm": "UP_FRONT", "status": "SUBMITTED", "billingAnchorDate": "2024-01-01T00:00:00Z", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "orders": ["2d3f04b0-2523-4ed6-b36a-f5ebc2439447"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, "charges": [{"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.3}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "ff313d39-0ac7-4e6c-b711-776f7833f10d", "tenantId": "test tenant 1 id", "quantity": 20, "discounts": [{"name": "test discount", "percent": 0.4}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.1}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "e9f98431-f248-4fbc-be5e-eff37473392a", "tenantId": "test tenant 1 id", "quantity": 50, "discounts": [{"name": "test discount", "percent": 0.2}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}]}}