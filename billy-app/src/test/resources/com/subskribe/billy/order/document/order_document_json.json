{"entity": {"id": "575676e5-e341-4328-8739-37e79745a39f", "tenantId": "f35504c0-1b8b-4739-a466-caab3f6f6c7e", "entityId": "ENT-XJREF38", "displayId": "ENT-1", "name": "Subskribe Test", "prorationScheme": "CALENDAR_DAYS", "prorationMode": "NORMALIZED", "invoiceConfigId": "6fa86fbc-ff54-480b-b5d4-e7dd5df6bb76", "timezone": "US/Pacific", "functionalCurrency": "USD", "companyContact": {"firstName": "Subskribe Test", "email": "<EMAIL>", "emailVerified": false, "address": {"streetAddressLine1": "128 Any St", "city": "San Fransisco", "state": "CA", "country": "US", "zipcode": "90210"}, "fullName": "Subskribe Test"}, "accountReceivableContact": {"firstName": "Graphite Growth, Inc. (“Graphite”)", "email": "<EMAIL>", "emailVerified": false, "phoneNumber": "**********", "address": {"streetAddressLine1": "1954 Mountain Blvd", "city": "Oakland", "state": "CA", "country": "USA", "zipcode": "94611"}, "fullName": "Graphite Growth, Inc. (“Graphite”)"}}, "timeZone": "US/Pacific", "documentKey": "1681a9e6-0717-47b4-bd8f-ace216db586d", "fileName": "f35504c0-1b8b-4739-a466-caab3f6f6c7e/1681a9e6-0717-47b4-bd8f-ace216db586d.pdf", "templateFileName": "defaultQuote.mustache", "templateCssFileName": "default.css", "templateContainerFileName": "defaultQuoteContainer.mustache", "s3Bucket": "order-documents", "orderDetail": {"id": "ORD-N6J9Z2M", "entityId": "ENT-XJREF38", "name": "<PERSON><PERSON>", "account": {"id": "ACCT-PY7JZEC", "name": "Bose Corp (OLD)", "phoneNumber": "******-879-7330", "crmId": "0010300000QBcTzAAL", "crmType": "SALESFORCE", "currency": "USD", "isReseller": false, "hasAutomaticPayment": false, "excludeFromBatchOperations": false, "excludeFromDunning": false, "supportedPaymentTypes": ["CARD", "ACH", "CHECK"], "updatedOn": **********, "customFields": {"CF-BG22D20E": {"type": "STRING", "name": "Account_hometown", "label": "Account_hometown", "required": false, "source": "USER"}, "CF-BCPB93Z2": {"type": "STRING", "name": "Payment_Methods", "label": "Payment_Methods", "required": false, "source": "USER"}, "CF-9FEPNP17": {"type": "STRING", "name": "Customer_Segment", "label": "Customer_Segment", "required": false, "source": "USER"}, "CF-G203ZY52": {"type": "PICKLIST", "name": "testfield", "label": "testfield", "options": ["v1", "v2", "v3"], "required": false, "source": "USER"}, "CF-BYCZREDV": {"type": "STRING", "name": "Company_ARR", "label": "Company ARR", "required": false, "source": "USER"}, "CF-M1VZX69E": {"type": "STRING", "name": "Account_Category", "label": "Default Account Category", "required": false, "source": "USER", "defaultValue": {"value": "Mid Size"}}, "CF-93Q6QDQT": {"type": "PICKLIST", "name": "Legal_Approval_Required", "label": "Legal Approval Required", "options": ["Yes", "No"], "required": false, "source": "USER"}, "CF-VCE6MC59": {"type": "STRING", "name": "common_name", "label": "Common Name", "required": false, "source": "USER"}, "CF-EJCBD9FN": {"type": "PICKLIST", "name": "test_field", "label": "Testing", "options": ["pick1", "pick2"], "required": false, "source": "USER"}, "CF-4TKV3NMX": {"type": "MULTISELECT_PICKLIST", "name": "MultiSelectPickListForAccount", "label": "MultiSelectPickListForAccount", "options": ["MS1", "MS2"], "required": false, "source": "USER"}}, "entityIds": ["*"]}, "accountDetail": {"id": "ACCT-PY7JZEC", "name": "Bose Corp (OLD)", "inUse": false, "phoneNumber": "******-879-7330", "crmId": "0010300000QBcTzAAL", "crmType": "SALESFORCE", "currency": "USD", "isReseller": false, "metrics": {}, "excludeFromBatchOperations": false, "excludeFromDunning": false, "hasAutomaticPayment": false, "supportedPaymentTypes": ["CHECK", "ACH", "CARD"], "customFields": [{"id": "CF-M1VZX69E", "type": "STRING", "name": "Account_Category", "label": "Default Account Category", "required": false, "source": "USER", "defaultValue": {"value": "Mid Size"}}, {"id": "CF-BG22D20E", "type": "STRING", "name": "Account_hometown", "label": "Account_hometown", "required": false, "source": "USER"}, {"id": "CF-BYCZREDV", "type": "STRING", "name": "Company_ARR", "label": "Company ARR", "required": false, "source": "USER"}, {"id": "CF-9FEPNP17", "type": "STRING", "name": "Customer_Segment", "label": "Customer_Segment", "required": false, "source": "USER"}, {"id": "CF-93Q6QDQT", "type": "PICKLIST", "name": "Legal_Approval_Required", "label": "Legal Approval Required", "options": ["Yes", "No"], "required": false, "source": "USER"}, {"id": "CF-4TKV3NMX", "type": "MULTISELECT_PICKLIST", "name": "MultiSelectPickListForAccount", "label": "MultiSelectPickListForAccount", "options": ["MS1", "MS2"], "required": false, "source": "USER"}, {"id": "CF-BCPB93Z2", "type": "STRING", "name": "Payment_Methods", "label": "Payment_Methods", "required": false, "source": "USER"}, {"id": "CF-VCE6MC59", "type": "STRING", "name": "common_name", "label": "Common Name", "required": false, "source": "USER"}, {"id": "CF-EJCBD9FN", "type": "PICKLIST", "name": "test_field", "label": "Testing", "options": ["pick1", "pick2"], "required": false, "source": "USER"}, {"id": "CF-G203ZY52", "type": "PICKLIST", "name": "testfield", "label": "testfield", "options": ["v1", "v2", "v3"], "required": false, "source": "USER"}], "entityIds": ["*"]}, "createdBy": {"id": "USER-SEYED", "displayName": "<PERSON><PERSON>", "email": "<EMAIL>", "state": "ACTIVE", "role": "BILLY_ADMIN", "ssoOnly": false, "entityIds": ["*"]}, "owner": {"id": "USER-SEYED", "displayName": "<PERSON><PERSON>", "email": "<EMAIL>", "state": "ACTIVE", "role": "BILLY_ADMIN", "ssoOnly": false, "entityIds": ["*"]}, "orderType": "NEW", "currency": "USD", "paymentTerm": "NET30", "subscriptionTargetVersion": 1, "shippingContact": {"id": "CONT-8H9TN2M", "accountId": "ACCT-PY7JZEC", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "address": {"streetAddressLine1": "250 Bellevue Way Ne", "city": "Bellevue", "state": "WA", "country": "US", "zipcode": "98005-2327"}, "fullName": "<PERSON><PERSON>"}, "billingContact": {"id": "CONT-8H9TN2M", "accountId": "ACCT-PY7JZEC", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "address": {"streetAddressLine1": "250 Bellevue Way Ne", "city": "Bellevue", "state": "WA", "country": "US", "zipcode": "98005-2327"}, "fullName": "<PERSON><PERSON>"}, "lineItems": [{"id": "6bdfa83e-a41d-4189-bd4e-3eb0954ec462", "rank": 1, "action": "ADD", "plan": {"id": "PLAN-8ZXEZC7", "entityIds": ["*"], "name": "Yearly Basic Plan", "status": "ACTIVE", "productId": "PROD-N2EQ0B0", "charges": [{"id": "CHRG-XKJ2KZW", "name": "Yearly Recurring", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, {"id": "CHRG-D7X9EXC", "name": "Setup fee", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 1000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}], "currency": "USD", "customFields": {"CF-YJPWP7RN": {"type": "STRING", "name": "segment", "label": "Segment", "required": false, "source": "USER"}, "CF-DCJG1DFP": {"type": "STRING", "name": "plan_custom_field_text", "label": "plan_custom_field_text", "required": false, "source": "USER"}, "CF-M1JHZ06Z": {"type": "PICKLIST", "name": "plan_custom_field_picklist", "label": "plan_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-237WKBPZ": {"type": "PICKLIST", "name": "deposit_amount", "label": "deposit_amount", "options": ["10000", "20000", "27500", "50000"], "required": false, "source": "USER"}, "CF-1H0B09NB": {"type": "PICKLIST", "name": "Tier", "label": "Tier", "options": ["Same"], "required": false, "source": "USER"}}, "updatedOn": 1644393346}, "chargeDetail": {"id": "CHRG-D7X9EXC", "planId": "PLAN-8ZXEZC7", "name": "Setup fee", "amount": 1000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "taxRate": {"id": "ab7550cf-c233-4429-ad52-de84b2778d08", "name": "SaaS", "taxCode": "SW052000", "taxInclusive": false, "status": "ACTIVE", "inUse": true}, "isRenewable": false, "isListPriceEditable": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "eventBased": false, "drawdown": false, "recurring": false, "creditable": false, "custom": false}, "charge": {"id": "CHRG-D7X9EXC", "name": "Setup fee", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 1000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "required": false, "source": "USER"}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "required": false, "source": "USER", "defaultValue": {"value": "123"}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER"}}, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, "quantity": 1, "isRamp": false, "listUnitPrice": 1000.0, "sellUnitPrice": 1000.0, "discountAmount": 0.0, "amount": 1000.0, "listAmount": 1000.0, "effectiveDate": 1656658800, "endDate": 1719817200, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER"}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "options": ["CA", "US", "AU"], "required": false, "source": "USER"}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER"}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "options": ["included", "itemized", "future"], "required": false, "source": "USER"}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": "> than 100K", "required": false, "source": "USER"}], "metrics": {"tcv": 1000.0, "recurringTotal": 0.0, "nonRecurringTotal": 1000.0, "arr": 0.0, "arrWithoutOverride": 0.0, "deltaTcv": 1000.0, "deltaArr": 0.0}, "creditable": false, "dryRunItem": false}, {"id": "62ec25ff-9858-4cfc-aff6-e2ad53326128", "rank": 2, "action": "ADD", "plan": {"id": "PLAN-8ZXEZC7", "entityIds": ["*"], "name": "Yearly Basic Plan", "status": "ACTIVE", "productId": "PROD-N2EQ0B0", "charges": [{"id": "CHRG-XKJ2KZW", "name": "Yearly Recurring", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, {"id": "CHRG-D7X9EXC", "name": "Setup fee", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 1000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}], "currency": "USD", "customFields": {"CF-YJPWP7RN": {"type": "STRING", "name": "segment", "label": "Segment", "required": false, "source": "USER"}, "CF-DCJG1DFP": {"type": "STRING", "name": "plan_custom_field_text", "label": "plan_custom_field_text", "required": false, "source": "USER"}, "CF-M1JHZ06Z": {"type": "PICKLIST", "name": "plan_custom_field_picklist", "label": "plan_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-237WKBPZ": {"type": "PICKLIST", "name": "deposit_amount", "label": "deposit_amount", "options": ["10000", "20000", "27500", "50000"], "required": false, "source": "USER"}, "CF-1H0B09NB": {"type": "PICKLIST", "name": "Tier", "label": "Tier", "options": ["Same"], "required": false, "source": "USER"}}, "updatedOn": 1644393346}, "chargeDetail": {"id": "CHRG-XKJ2KZW", "planId": "PLAN-8ZXEZC7", "name": "Yearly Recurring", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "taxRate": {"id": "ab7550cf-c233-4429-ad52-de84b2778d08", "name": "SaaS", "taxCode": "SW052000", "taxInclusive": false, "status": "ACTIVE", "inUse": true}, "isRenewable": true, "isListPriceEditable": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "eventBased": false, "drawdown": false, "recurring": true, "creditable": false, "custom": false}, "charge": {"id": "CHRG-XKJ2KZW", "name": "Yearly Recurring", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "required": false, "source": "USER"}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "required": false, "source": "USER", "defaultValue": {"value": "123"}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER"}}, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, "quantity": 1, "isRamp": true, "listUnitPrice": 10.0, "sellUnitPrice": 10.0, "discountAmount": 0.0, "amount": 120.0, "listAmount": 120.0, "annualizedAmount": 120.0, "effectiveDate": 1656658800, "endDate": 1688194800, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER"}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "options": ["CA", "US", "AU"], "required": false, "source": "USER"}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER"}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "options": ["included", "itemized", "future"], "required": false, "source": "USER"}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": "> than 100K", "required": false, "source": "USER"}], "metrics": {"tcv": 120.0, "recurringTotal": 120.0, "nonRecurringTotal": 0.0, "arr": 120.0, "arrWithoutOverride": 120.0, "deltaTcv": 120.0, "deltaArr": 120.0}, "creditable": false, "dryRunItem": false}, {"id": "6a68f77a-7252-4782-94dc-b93af2a6354c", "rank": 3, "action": "ADD", "plan": {"id": "PLAN-8ZXEZC7", "entityIds": ["*"], "name": "Yearly Basic Plan", "status": "ACTIVE", "productId": "PROD-N2EQ0B0", "charges": [{"id": "CHRG-XKJ2KZW", "name": "Yearly Recurring", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, {"id": "CHRG-D7X9EXC", "name": "Setup fee", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 1000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}], "currency": "USD", "customFields": {"CF-YJPWP7RN": {"type": "STRING", "name": "segment", "label": "Segment", "required": false, "source": "USER"}, "CF-DCJG1DFP": {"type": "STRING", "name": "plan_custom_field_text", "label": "plan_custom_field_text", "required": false, "source": "USER"}, "CF-M1JHZ06Z": {"type": "PICKLIST", "name": "plan_custom_field_picklist", "label": "plan_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-237WKBPZ": {"type": "PICKLIST", "name": "deposit_amount", "label": "deposit_amount", "options": ["10000", "20000", "27500", "50000"], "required": false, "source": "USER"}, "CF-1H0B09NB": {"type": "PICKLIST", "name": "Tier", "label": "Tier", "options": ["Same"], "required": false, "source": "USER"}}, "updatedOn": 1644393346}, "chargeDetail": {"id": "CHRG-XKJ2KZW", "planId": "PLAN-8ZXEZC7", "name": "Yearly Recurring", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "taxRate": {"id": "ab7550cf-c233-4429-ad52-de84b2778d08", "name": "SaaS", "taxCode": "SW052000", "taxInclusive": false, "status": "ACTIVE", "inUse": true}, "isRenewable": true, "isListPriceEditable": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "eventBased": false, "drawdown": false, "recurring": true, "creditable": false, "custom": false}, "charge": {"id": "CHRG-XKJ2KZW", "name": "Yearly Recurring", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "required": false, "source": "USER"}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "required": false, "source": "USER", "defaultValue": {"value": "123"}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER"}}, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, "quantity": 1, "isRamp": true, "listUnitPrice": 10.0, "sellUnitPrice": 10.0, "discountAmount": 0.0, "amount": 120.0, "listAmount": 120.0, "annualizedAmount": 120.0, "effectiveDate": 1688194800, "endDate": 1719817200, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER"}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "options": ["CA", "US", "AU"], "required": false, "source": "USER"}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER"}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "options": ["included", "itemized", "future"], "required": false, "source": "USER"}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": "> than 100K", "required": false, "source": "USER"}], "metrics": {"tcv": 120.0, "recurringTotal": 120.0, "nonRecurringTotal": 0.0, "arr": 120.0, "arrWithoutOverride": 120.0, "deltaTcv": 120.0, "deltaArr": 0.0}, "creditable": false, "dryRunItem": false}], "lineItemsNetEffect": [{"id": "6bdfa83e-a41d-4189-bd4e-3eb0954ec462", "rank": 1, "action": "ADD", "plan": {"id": "PLAN-8ZXEZC7", "entityIds": ["*"], "name": "Yearly Basic Plan", "status": "ACTIVE", "productId": "PROD-N2EQ0B0", "charges": [{"id": "CHRG-XKJ2KZW", "name": "Yearly Recurring", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, {"id": "CHRG-D7X9EXC", "name": "Setup fee", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 1000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}], "currency": "USD", "customFields": {"CF-YJPWP7RN": {"type": "STRING", "name": "segment", "label": "Segment", "required": false, "source": "USER"}, "CF-DCJG1DFP": {"type": "STRING", "name": "plan_custom_field_text", "label": "plan_custom_field_text", "required": false, "source": "USER"}, "CF-M1JHZ06Z": {"type": "PICKLIST", "name": "plan_custom_field_picklist", "label": "plan_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-237WKBPZ": {"type": "PICKLIST", "name": "deposit_amount", "label": "deposit_amount", "options": ["10000", "20000", "27500", "50000"], "required": false, "source": "USER"}, "CF-1H0B09NB": {"type": "PICKLIST", "name": "Tier", "label": "Tier", "options": ["Same"], "required": false, "source": "USER"}}, "updatedOn": 1644393346}, "chargeDetail": {"id": "CHRG-D7X9EXC", "planId": "PLAN-8ZXEZC7", "name": "Setup fee", "amount": 1000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "taxRate": {"id": "ab7550cf-c233-4429-ad52-de84b2778d08", "name": "SaaS", "taxCode": "SW052000", "taxInclusive": false, "status": "ACTIVE", "inUse": true}, "isRenewable": false, "isListPriceEditable": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "eventBased": false, "drawdown": false, "recurring": false, "creditable": false, "custom": false}, "charge": {"id": "CHRG-D7X9EXC", "name": "Setup fee", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 1000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "required": false, "source": "USER"}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "required": false, "source": "USER", "defaultValue": {"value": "123"}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER"}}, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, "quantity": 1, "isRamp": false, "listUnitPrice": 1000.0, "sellUnitPrice": 1000.0, "discountAmount": 0.0, "amount": 1000.0, "listAmount": 1000.0, "effectiveDate": 1656658800, "endDate": 1719817200, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER"}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "options": ["CA", "US", "AU"], "required": false, "source": "USER"}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER"}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "options": ["included", "itemized", "future"], "required": false, "source": "USER"}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": "> than 100K", "required": false, "source": "USER"}], "metrics": {"tcv": 1000.0, "recurringTotal": 0.0, "nonRecurringTotal": 1000.0, "arr": 0.0, "arrWithoutOverride": 0.0, "deltaTcv": 1000.0, "deltaArr": 0.0}, "creditable": false, "dryRunItem": false}, {"id": "62ec25ff-9858-4cfc-aff6-e2ad53326128", "rank": 2, "action": "ADD", "plan": {"id": "PLAN-8ZXEZC7", "entityIds": ["*"], "name": "Yearly Basic Plan", "status": "ACTIVE", "productId": "PROD-N2EQ0B0", "charges": [{"id": "CHRG-XKJ2KZW", "name": "Yearly Recurring", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, {"id": "CHRG-D7X9EXC", "name": "Setup fee", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 1000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}], "currency": "USD", "customFields": {"CF-YJPWP7RN": {"type": "STRING", "name": "segment", "label": "Segment", "required": false, "source": "USER"}, "CF-DCJG1DFP": {"type": "STRING", "name": "plan_custom_field_text", "label": "plan_custom_field_text", "required": false, "source": "USER"}, "CF-M1JHZ06Z": {"type": "PICKLIST", "name": "plan_custom_field_picklist", "label": "plan_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-237WKBPZ": {"type": "PICKLIST", "name": "deposit_amount", "label": "deposit_amount", "options": ["10000", "20000", "27500", "50000"], "required": false, "source": "USER"}, "CF-1H0B09NB": {"type": "PICKLIST", "name": "Tier", "label": "Tier", "options": ["Same"], "required": false, "source": "USER"}}, "updatedOn": 1644393346}, "chargeDetail": {"id": "CHRG-XKJ2KZW", "planId": "PLAN-8ZXEZC7", "name": "Yearly Recurring", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "taxRate": {"id": "ab7550cf-c233-4429-ad52-de84b2778d08", "name": "SaaS", "taxCode": "SW052000", "taxInclusive": false, "status": "ACTIVE", "inUse": true}, "isRenewable": true, "isListPriceEditable": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "eventBased": false, "drawdown": false, "recurring": true, "creditable": false, "custom": false}, "charge": {"id": "CHRG-XKJ2KZW", "name": "Yearly Recurring", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "required": false, "source": "USER"}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "required": false, "source": "USER", "defaultValue": {"value": "123"}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER"}}, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, "quantity": 1, "isRamp": true, "listUnitPrice": 10.0, "sellUnitPrice": 10.0, "discountAmount": 0.0, "amount": 120.0, "listAmount": 120.0, "annualizedAmount": 120.0, "effectiveDate": 1656658800, "endDate": 1688194800, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER"}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "options": ["CA", "US", "AU"], "required": false, "source": "USER"}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER"}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "options": ["included", "itemized", "future"], "required": false, "source": "USER"}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": "> than 100K", "required": false, "source": "USER"}], "metrics": {"tcv": 120.0, "recurringTotal": 120.0, "nonRecurringTotal": 0.0, "arr": 120.0, "arrWithoutOverride": 120.0, "deltaTcv": 120.0, "deltaArr": 120.0}, "creditable": false, "dryRunItem": false}, {"id": "6a68f77a-7252-4782-94dc-b93af2a6354c", "rank": 3, "action": "ADD", "plan": {"id": "PLAN-8ZXEZC7", "entityIds": ["*"], "name": "Yearly Basic Plan", "status": "ACTIVE", "productId": "PROD-N2EQ0B0", "charges": [{"id": "CHRG-XKJ2KZW", "name": "Yearly Recurring", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, {"id": "CHRG-D7X9EXC", "name": "Setup fee", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": false, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 1000.0, "type": "ONE_TIME", "chargeModel": "FLAT_FEE", "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": false, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}], "currency": "USD", "customFields": {"CF-YJPWP7RN": {"type": "STRING", "name": "segment", "label": "Segment", "required": false, "source": "USER"}, "CF-DCJG1DFP": {"type": "STRING", "name": "plan_custom_field_text", "label": "plan_custom_field_text", "required": false, "source": "USER"}, "CF-M1JHZ06Z": {"type": "PICKLIST", "name": "plan_custom_field_picklist", "label": "plan_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-237WKBPZ": {"type": "PICKLIST", "name": "deposit_amount", "label": "deposit_amount", "options": ["10000", "20000", "27500", "50000"], "required": false, "source": "USER"}, "CF-1H0B09NB": {"type": "PICKLIST", "name": "Tier", "label": "Tier", "options": ["Same"], "required": false, "source": "USER"}}, "updatedOn": 1644393346}, "chargeDetail": {"id": "CHRG-XKJ2KZW", "planId": "PLAN-8ZXEZC7", "name": "Yearly Recurring", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "taxRate": {"id": "ab7550cf-c233-4429-ad52-de84b2778d08", "name": "SaaS", "taxCode": "SW052000", "taxInclusive": false, "status": "ACTIVE", "inUse": true}, "isRenewable": true, "isListPriceEditable": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "eventBased": false, "drawdown": false, "recurring": true, "creditable": false, "custom": false}, "charge": {"id": "CHRG-XKJ2KZW", "name": "Yearly Recurring", "taxRateId": "ab7550cf-c233-4429-ad52-de84b2778d08", "isRenewable": true, "isCreditable": false, "isListPriceEditable": false, "planId": "PLAN-8ZXEZC7", "amount": 10.0, "type": "RECURRING", "chargeModel": "PER_UNIT", "recurrence": {"cycle": "MONTH", "step": 1}, "isDrawdown": false, "isCustom": false, "isEventBased": false, "isDiscount": false, "billingTerm": "UP_FRONT", "billingCycle": "DEFAULT", "shouldTrackArr": true, "customFields": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "required": false, "source": "USER"}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "required": false, "source": "USER", "defaultValue": {"value": "123"}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER"}}, "eventBased": false, "drawdown": false, "custom": false, "creditable": false}, "quantity": 1, "isRamp": true, "listUnitPrice": 10.0, "sellUnitPrice": 10.0, "discountAmount": 0.0, "amount": 120.0, "listAmount": 120.0, "annualizedAmount": 120.0, "effectiveDate": 1688194800, "endDate": 1719817200, "customFields": [{"id": "CF-KN0F27K4", "type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER"}, {"id": "CF-2BK5D0F9", "type": "PICKLIST", "name": "country", "label": "country", "options": ["CA", "US", "AU"], "required": false, "source": "USER"}, {"id": "CF-W6J22WC8", "type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER"}, {"id": "CF-MPE17DFY", "type": "PICKLIST", "name": "product_type", "label": "Product Type", "options": ["included", "itemized", "future"], "required": false, "source": "USER"}, {"id": "CF-HEQ8C5WN", "type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": "> than 100K", "required": false, "source": "USER"}], "metrics": {"tcv": 120.0, "recurringTotal": 120.0, "nonRecurringTotal": 0.0, "arr": 120.0, "arrWithoutOverride": 120.0, "deltaTcv": 120.0, "deltaArr": 0.0}, "creditable": false, "dryRunItem": false}], "startDate": 1656658800, "endDate": 1719817200, "termLength": {"cycle": "YEAR", "step": 2}, "billingCycle": {"cycle": "YEAR", "step": 1}, "billingTerm": "UP_FRONT", "totalListAmount": 1240.0, "totalListAmountBeforeOverride": 1240.0, "totalAmount": 1240.0, "totalDiscount": 0.0, "totalDiscountPercent": 0.0, "totalRecurringDiscount": 0.0, "totalRecurringDiscountPercent": 0.0, "totalNonRecurringDiscount": 0.0, "totalNonRecurringDiscountPercent": 0.0, "deltaArrPercent": 100.0, "status": "DRAFT", "updatedOn": 1738972581, "rampInterval": [1656658800, 1688194800], "isPrimaryOrderForSfdcOpportunity": false, "sfdcOrderCanBeExecuted": true, "metrics": {"tcv": 1240.0, "recurringTotal": 240.0, "nonRecurringTotal": 1000.0, "arr": 0.0, "entryArr": 120.0, "exitArr": 120.0, "averageArr": 120.0, "arrTrend": [{"instant": 1656658800, "amount": 120.0}, {"instant": 1719817200, "amount": 0.0}], "deltaTcv": 1240.0, "deltaArr": 120.0}, "purchaseOrderRequiredForInvoicing": false, "autoRenew": false, "billingAnchorDate": 1656658800, "recurringDiscountPercent": 0.0, "nonRecurringDiscountPercent": 0.0, "customFields": [{"id": "CF-13W8FNJZ", "type": "STRING", "name": "0__test", "label": "2. test", "required": false, "source": "USER"}, {"id": "CF-81E91Y1R", "type": "STRING", "name": "1__test", "label": "1. test", "required": false, "source": "USER"}, {"id": "CF-32M873B2", "type": "STRING", "name": "Order_Category", "label": "Default Order Category", "required": false, "source": "USER"}, {"id": "CF-2P979N66", "type": "PICKLIST", "name": "Order_Segment", "label": "default Order Segment", "options": ["Small", "Mid", "Custom"], "required": false, "source": "USER"}, {"id": "CF-7DZJQ5JK", "type": "STRING", "name": "Order_TCV", "label": "Order TCV", "required": false, "source": "USER"}, {"id": "CF-HZZNY2D6", "type": "STRING", "name": "Order_type", "label": "Default Order Type", "required": false, "source": "USER"}, {"id": "CF-DCP6HGXK", "type": "PICKLIST", "name": "deposit_amount", "label": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "options": ["10000", "20000", "27500", "50000", "100000"], "required": false, "source": "USER"}, {"id": "CF-1VJDXZZB", "type": "STRING", "name": "legal_name", "label": "Legal Name", "required": false, "source": "USER"}, {"id": "CF-Q1H13Y8J", "type": "STRING", "name": "order_item_10", "label": "order_item_10", "required": false, "source": "USER"}, {"id": "CF-3V882R2H", "type": "PICKLIST", "name": "show_contact", "label": "show_contact", "options": ["yes", "no"], "required": false, "source": "USER"}], "startDateType": "FIXED", "orderRamped": true}, "customField": {"entries": {"CF-Q1H13Y8J": {"type": "STRING", "name": "order_item_10", "label": "order_item_10", "required": false, "source": "USER"}, "CF-7DZJQ5JK": {"type": "STRING", "name": "Order_TCV", "label": "Order TCV", "required": false, "source": "USER"}, "CF-HZZNY2D6": {"type": "STRING", "name": "Order_type", "label": "Default Order Type", "required": false, "source": "USER"}, "CF-32M873B2": {"type": "STRING", "name": "Order_Category", "label": "Default Order Category", "required": false, "source": "USER"}, "CF-2P979N66": {"type": "PICKLIST", "name": "Order_Segment", "label": "default Order Segment", "options": ["Small", "Mid", "Custom"], "required": false, "source": "USER"}, "CF-3V882R2H": {"type": "PICKLIST", "name": "show_contact", "label": "show_contact", "options": ["yes", "no"], "required": false, "source": "USER"}, "CF-DCP6HGXK": {"type": "PICKLIST", "name": "deposit_amount", "label": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "options": ["10000", "20000", "27500", "50000", "100000"], "required": false, "source": "USER"}, "CF-1VJDXZZB": {"type": "STRING", "name": "legal_name", "label": "Legal Name", "required": false, "source": "USER"}, "CF-81E91Y1R": {"type": "STRING", "name": "1__test", "label": "1. test", "required": false, "source": "USER"}, "CF-13W8FNJZ": {"type": "STRING", "name": "0__test", "label": "2. test", "required": false, "source": "USER"}}}, "lineItemCustomFields": {"6a68f77a-7252-4782-94dc-b93af2a6354c": {"entries": {"CF-KN0F27K4": {"type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER"}, "CF-2BK5D0F9": {"type": "PICKLIST", "name": "country", "label": "country", "options": ["CA", "US", "AU"], "required": false, "source": "USER"}, "CF-W6J22WC8": {"type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER"}, "CF-MPE17DFY": {"type": "PICKLIST", "name": "product_type", "label": "Product Type", "options": ["included", "itemized", "future"], "required": false, "source": "USER"}, "CF-HEQ8C5WN": {"type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": "> than 100K", "required": false, "source": "USER"}}}, "6bdfa83e-a41d-4189-bd4e-3eb0954ec462": {"entries": {"CF-KN0F27K4": {"type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER"}, "CF-2BK5D0F9": {"type": "PICKLIST", "name": "country", "label": "country", "options": ["CA", "US", "AU"], "required": false, "source": "USER"}, "CF-W6J22WC8": {"type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER"}, "CF-MPE17DFY": {"type": "PICKLIST", "name": "product_type", "label": "Product Type", "options": ["included", "itemized", "future"], "required": false, "source": "USER"}, "CF-HEQ8C5WN": {"type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": "> than 100K", "required": false, "source": "USER"}}}, "62ec25ff-9858-4cfc-aff6-e2ad53326128": {"entries": {"CF-KN0F27K4": {"type": "MULTISELECT_PICKLIST", "name": "years", "label": "years", "options": ["7", "8", "9", "10", "11"], "required": false, "source": "USER"}, "CF-2BK5D0F9": {"type": "PICKLIST", "name": "country", "label": "country", "options": ["CA", "US", "AU"], "required": false, "source": "USER"}, "CF-W6J22WC8": {"type": "MULTISELECT_PICKLIST", "name": "integration_type", "label": "Integration Type", "options": ["ERP", "CRM", "SRM", "VM", "HCM"], "required": false, "source": "USER"}, "CF-MPE17DFY": {"type": "PICKLIST", "name": "product_type", "label": "Product Type", "options": ["included", "itemized", "future"], "required": false, "source": "USER"}, "CF-HEQ8C5WN": {"type": "STRING", "name": "Order_Line_Value", "label": "Default Order Line Value", "value": "> than 100K", "required": false, "source": "USER"}}}}, "chargeCustomFields": {"CHRG-D7X9EXC": {"entries": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "required": false, "source": "USER"}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "required": false, "source": "USER", "defaultValue": {"value": "123"}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER"}}}, "CHRG-XKJ2KZW": {"entries": {"CF-4Z99EJ06": {"type": "STRING", "name": "charge_custom_field_text", "label": "charge_custom_field_text", "required": false, "source": "USER"}, "CF-RV03VNZH": {"type": "PICKLIST", "name": "charge_custom_field_picklist", "label": "charge_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-P7X095TM": {"type": "STRING", "name": "Measurement", "label": "Measurement", "required": false, "source": "USER", "defaultValue": {"value": "123"}}, "CF-1KW7NJXX": {"type": "PICKLIST", "name": "product_type", "label": "product_type", "options": ["Standard Success", "Premier Success"], "required": false, "source": "USER"}}}}, "planCustomFields": {"PLAN-8ZXEZC7": {"entries": {"CF-YJPWP7RN": {"type": "STRING", "name": "segment", "label": "Segment", "required": false, "source": "USER"}, "CF-DCJG1DFP": {"type": "STRING", "name": "plan_custom_field_text", "label": "plan_custom_field_text", "required": false, "source": "USER"}, "CF-M1JHZ06Z": {"type": "PICKLIST", "name": "plan_custom_field_picklist", "label": "plan_custom_field_picklist", "options": ["A", "B", "C"], "required": false, "source": "USER"}, "CF-237WKBPZ": {"type": "PICKLIST", "name": "deposit_amount", "label": "deposit_amount", "options": ["10000", "20000", "27500", "50000"], "required": false, "source": "USER"}, "CF-1H0B09NB": {"type": "PICKLIST", "name": "Tier", "label": "Tier", "options": ["Same"], "required": false, "source": "USER"}}}}, "tenantInfo": {"tenantId": "f35504c0-1b8b-4739-a466-caab3f6f6c7e", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "phoneNumber": "**********"}, "documentRenderingOptions": {"docOutput": true}, "products": {"PROD-N2EQ0B0": {"id": "PROD-N2EQ0B0", "entityIds": ["*"], "name": "Vijay Test Product", "inUse": true, "description": "Test Product", "sku": "ABCD", "updatedOn": **********}}, "rawJson": "{\"id\":null,\"externalId\":null,\"entityId\":null,\"name\":null,\"account\":null,\"accountDetail\":null,\"createdBy\":null,\"owner\":null,\"resoldBy\":null,\"orderType\":null,\"currency\":null,\"paymentTerm\":null,\"subscriptionId\":null,\"currentSubscription\":null,\"updatedSubscription\":null,\"subscriptionTargetVersion\":0,\"renewalForSubscription\":null,\"shippingContact\":null,\"billingContact\":null,\"predefinedDiscounts\":null,\"creditableAmounts\":null,\"lineItems\":[{\"id\":\"6bdfa83e-a41d-4189-bd4e-3eb0954ec462\",\"itemGroupId\":null,\"rank\":1,\"action\":\"ADD\",\"plan\":{\"id\":\"PLAN-8ZXEZC7\",\"entityIds\":[\"*\"],\"name\":\"Yearly Basic Plan\",\"displayName\":null,\"description\":\"\",\"status\":\"ACTIVE\",\"productId\":\"PROD-N2EQ0B0\",\"charges\":[{\"id\":\"CHRG-XKJ2KZW\",\"name\":\"Yearly Recurring\",\"displayName\":null,\"description\":\"\",\"taxRateId\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"unitOfMeasureId\":null,\"isRenewable\":true,\"isCreditable\":false,\"isListPriceEditable\":false,\"minQuantity\":null,\"defaultQuantity\":null,\"maxQuantity\":null,\"externalId\":null,\"minAmount\":null,\"maxAmount\":null,\"recognitionRuleId\":null,\"erpId\":null,\"itemCode\":null,\"planId\":\"PLAN-8ZXEZC7\",\"amount\":1E+1,\"type\":\"RECURRING\",\"chargeModel\":\"PER_UNIT\",\"recurrence\":{\"cycle\":\"MONTH\",\"step\":1},\"priceTiers\":[],\"isDrawdown\":false,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isCustom\":false,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":null,\"durationInMonths\":null,\"isEventBased\":false,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":true,\"customFields\":{},\"eventBased\":false,\"drawdown\":false,\"custom\":false,\"creditable\":false},{\"id\":\"CHRG-D7X9EXC\",\"name\":\"Setup fee\",\"displayName\":null,\"description\":\"\",\"taxRateId\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"unitOfMeasureId\":null,\"isRenewable\":false,\"isCreditable\":false,\"isListPriceEditable\":false,\"minQuantity\":null,\"defaultQuantity\":null,\"maxQuantity\":null,\"externalId\":null,\"minAmount\":null,\"maxAmount\":null,\"recognitionRuleId\":null,\"erpId\":null,\"itemCode\":null,\"planId\":\"PLAN-8ZXEZC7\",\"amount\":1E+3,\"type\":\"ONE_TIME\",\"chargeModel\":\"FLAT_FEE\",\"recurrence\":null,\"priceTiers\":[],\"isDrawdown\":false,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isCustom\":false,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":null,\"durationInMonths\":null,\"isEventBased\":false,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":false,\"customFields\":{},\"eventBased\":false,\"drawdown\":false,\"custom\":false,\"creditable\":false}],\"currency\":\"USD\",\"externalId\":null,\"templateIds\":[],\"replacementPlanIds\":[],\"customFields\":{\"CF-YJPWP7RN\":{\"type\":\"STRING\",\"name\":\"segment\",\"label\":\"Segment\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-DCJG1DFP\":{\"type\":\"STRING\",\"name\":\"plan_custom_field_text\",\"label\":\"plan_custom_field_text\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-M1JHZ06Z\":{\"type\":\"PICKLIST\",\"name\":\"plan_custom_field_picklist\",\"label\":\"plan_custom_field_picklist\",\"value\":null,\"selections\":[],\"options\":[\"A\",\"B\",\"C\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-237WKBPZ\":{\"type\":\"PICKLIST\",\"name\":\"deposit_amount\",\"label\":\"deposit_amount\",\"value\":null,\"selections\":[],\"options\":[\"10000\",\"20000\",\"27500\",\"50000\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-1H0B09NB\":{\"type\":\"PICKLIST\",\"name\":\"Tier\",\"label\":\"Tier\",\"value\":null,\"selections\":[],\"options\":[\"Same\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null}},\"updatedOn\":1644393346},\"subscriptionChargeId\":null,\"currencyConversionRateId\":null,\"subscriptionChargeGroupId\":null,\"chargeDetail\":{\"id\":\"CHRG-D7X9EXC\",\"planId\":\"PLAN-8ZXEZC7\",\"name\":\"Setup fee\",\"displayName\":null,\"description\":\"\",\"amount\":1E+3,\"type\":\"ONE_TIME\",\"chargeModel\":\"FLAT_FEE\",\"recurrence\":null,\"durationInMonths\":null,\"priceTiers\":[],\"taxRate\":{\"id\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"name\":\"SaaS\",\"description\":\"\",\"taxPercentage\":null,\"taxCode\":\"SW052000\",\"taxInclusive\":false,\"status\":\"ACTIVE\",\"inUse\":true},\"unitOfMeasure\":null,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isRenewable\":false,\"isListPriceEditable\":false,\"recognitionRule\":null,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":{\"taxLiabilityAccountId\":null,\"deferredRevenueAccountId\":null,\"recognizedRevenueAccountId\":null,\"contractAssetAccountId\":null,\"ledgerAccountIds\":[]},\"minQuantity\":null,\"maxQuantity\":null,\"defaultQuantity\":null,\"externalId\":null,\"erpId\":null,\"itemCode\":null,\"minAmount\":null,\"maxAmount\":null,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":false,\"creditable\":false,\"recurring\":false,\"eventBased\":false,\"drawdown\":false,\"custom\":false},\"charge\":{\"id\":\"CHRG-D7X9EXC\",\"name\":\"Setup fee\",\"displayName\":null,\"description\":\"\",\"taxRateId\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"unitOfMeasureId\":null,\"isRenewable\":false,\"isCreditable\":false,\"isListPriceEditable\":false,\"minQuantity\":null,\"defaultQuantity\":null,\"maxQuantity\":null,\"externalId\":null,\"minAmount\":null,\"maxAmount\":null,\"recognitionRuleId\":null,\"erpId\":null,\"itemCode\":null,\"planId\":\"PLAN-8ZXEZC7\",\"amount\":1E+3,\"type\":\"ONE_TIME\",\"chargeModel\":\"FLAT_FEE\",\"recurrence\":null,\"priceTiers\":[],\"isDrawdown\":false,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isCustom\":false,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":null,\"durationInMonths\":null,\"isEventBased\":false,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":false,\"customFields\":{\"CF-4Z99EJ06\":{\"type\":\"STRING\",\"name\":\"charge_custom_field_text\",\"label\":\"charge_custom_field_text\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-RV03VNZH\":{\"type\":\"PICKLIST\",\"name\":\"charge_custom_field_picklist\",\"label\":\"charge_custom_field_picklist\",\"value\":null,\"selections\":[],\"options\":[\"A\",\"B\",\"C\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-P7X095TM\":{\"type\":\"STRING\",\"name\":\"Measurement\",\"label\":\"Measurement\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":{\"value\":\"123\",\"selections\":null}},\"CF-1KW7NJXX\":{\"type\":\"PICKLIST\",\"name\":\"product_type\",\"label\":\"product_type\",\"value\":null,\"selections\":[],\"options\":[\"Standard Success\",\"Premier Success\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null}},\"eventBased\":false,\"drawdown\":false,\"custom\":false,\"creditable\":false},\"quantity\":1,\"isRamp\":false,\"listUnitPriceBeforeOverride\":null,\"listUnitPrice\":1000.00000,\"sellUnitPrice\":1000.0000000000,\"discountAmount\":0.00,\"discounts\":[],\"predefinedDiscounts\":[],\"amount\":1000.0000000000,\"listAmountBeforeOverride\":null,\"listAmount\":1000.0000000000,\"annualizedAmount\":null,\"taxEstimate\":null,\"effectiveDate\":1656658800,\"endDate\":1719817200,\"listPriceOverrideRatio\":null,\"attributeReferences\":null,\"customFields\":[{\"id\":\"CF-KN0F27K4\",\"type\":\"MULTISELECT_PICKLIST\",\"name\":\"years\",\"label\":\"years\",\"value\":\"\",\"selections\":[],\"options\":[\"7\",\"8\",\"9\",\"10\",\"11\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-2BK5D0F9\",\"type\":\"PICKLIST\",\"name\":\"country\",\"label\":\"country\",\"value\":\"\",\"selections\":[],\"options\":[\"CA\",\"US\",\"AU\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-W6J22WC8\",\"type\":\"MULTISELECT_PICKLIST\",\"name\":\"integration_type\",\"label\":\"Integration Type\",\"value\":\"\",\"selections\":[],\"options\":[\"ERP\",\"CRM\",\"SRM\",\"VM\",\"HCM\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-MPE17DFY\",\"type\":\"PICKLIST\",\"name\":\"product_type\",\"label\":\"Product Type\",\"value\":\"\",\"selections\":[],\"options\":[\"included\",\"itemized\",\"future\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-HEQ8C5WN\",\"type\":\"STRING\",\"name\":\"Order_Line_Value\",\"label\":\"Default Order Line Value\",\"value\":\"> than 100K\",\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null}],\"arrOverride\":null,\"replacedPlan\":null,\"availableReplacementPlan\":null,\"metrics\":{\"tcv\":1000.00,\"recurringTotal\":0.00,\"nonRecurringTotal\":1000.00,\"arr\":0.00,\"arrWithoutOverride\":0.00,\"deltaTcv\":1000.00,\"deltaArr\":0.00},\"dryRunItem\":false,\"creditable\":false},{\"id\":\"62ec25ff-9858-4cfc-aff6-e2ad53326128\",\"itemGroupId\":null,\"rank\":2,\"action\":\"ADD\",\"plan\":{\"id\":\"PLAN-8ZXEZC7\",\"entityIds\":[\"*\"],\"name\":\"Yearly Basic Plan\",\"displayName\":null,\"description\":\"\",\"status\":\"ACTIVE\",\"productId\":\"PROD-N2EQ0B0\",\"charges\":[{\"id\":\"CHRG-XKJ2KZW\",\"name\":\"Yearly Recurring\",\"displayName\":null,\"description\":\"\",\"taxRateId\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"unitOfMeasureId\":null,\"isRenewable\":true,\"isCreditable\":false,\"isListPriceEditable\":false,\"minQuantity\":null,\"defaultQuantity\":null,\"maxQuantity\":null,\"externalId\":null,\"minAmount\":null,\"maxAmount\":null,\"recognitionRuleId\":null,\"erpId\":null,\"itemCode\":null,\"planId\":\"PLAN-8ZXEZC7\",\"amount\":1E+1,\"type\":\"RECURRING\",\"chargeModel\":\"PER_UNIT\",\"recurrence\":{\"cycle\":\"MONTH\",\"step\":1},\"priceTiers\":[],\"isDrawdown\":false,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isCustom\":false,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":null,\"durationInMonths\":null,\"isEventBased\":false,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":true,\"customFields\":{},\"eventBased\":false,\"drawdown\":false,\"custom\":false,\"creditable\":false},{\"id\":\"CHRG-D7X9EXC\",\"name\":\"Setup fee\",\"displayName\":null,\"description\":\"\",\"taxRateId\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"unitOfMeasureId\":null,\"isRenewable\":false,\"isCreditable\":false,\"isListPriceEditable\":false,\"minQuantity\":null,\"defaultQuantity\":null,\"maxQuantity\":null,\"externalId\":null,\"minAmount\":null,\"maxAmount\":null,\"recognitionRuleId\":null,\"erpId\":null,\"itemCode\":null,\"planId\":\"PLAN-8ZXEZC7\",\"amount\":1E+3,\"type\":\"ONE_TIME\",\"chargeModel\":\"FLAT_FEE\",\"recurrence\":null,\"priceTiers\":[],\"isDrawdown\":false,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isCustom\":false,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":null,\"durationInMonths\":null,\"isEventBased\":false,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":false,\"customFields\":{},\"eventBased\":false,\"drawdown\":false,\"custom\":false,\"creditable\":false}],\"currency\":\"USD\",\"externalId\":null,\"templateIds\":[],\"replacementPlanIds\":[],\"customFields\":{\"CF-YJPWP7RN\":{\"type\":\"STRING\",\"name\":\"segment\",\"label\":\"Segment\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-DCJG1DFP\":{\"type\":\"STRING\",\"name\":\"plan_custom_field_text\",\"label\":\"plan_custom_field_text\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-M1JHZ06Z\":{\"type\":\"PICKLIST\",\"name\":\"plan_custom_field_picklist\",\"label\":\"plan_custom_field_picklist\",\"value\":null,\"selections\":[],\"options\":[\"A\",\"B\",\"C\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-237WKBPZ\":{\"type\":\"PICKLIST\",\"name\":\"deposit_amount\",\"label\":\"deposit_amount\",\"value\":null,\"selections\":[],\"options\":[\"10000\",\"20000\",\"27500\",\"50000\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-1H0B09NB\":{\"type\":\"PICKLIST\",\"name\":\"Tier\",\"label\":\"Tier\",\"value\":null,\"selections\":[],\"options\":[\"Same\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null}},\"updatedOn\":1644393346},\"subscriptionChargeId\":null,\"currencyConversionRateId\":null,\"subscriptionChargeGroupId\":null,\"chargeDetail\":{\"id\":\"CHRG-XKJ2KZW\",\"planId\":\"PLAN-8ZXEZC7\",\"name\":\"Yearly Recurring\",\"displayName\":null,\"description\":\"\",\"amount\":1E+1,\"type\":\"RECURRING\",\"chargeModel\":\"PER_UNIT\",\"recurrence\":{\"cycle\":\"MONTH\",\"step\":1},\"durationInMonths\":null,\"priceTiers\":[],\"taxRate\":{\"id\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"name\":\"SaaS\",\"description\":\"\",\"taxPercentage\":null,\"taxCode\":\"SW052000\",\"taxInclusive\":false,\"status\":\"ACTIVE\",\"inUse\":true},\"unitOfMeasure\":null,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isRenewable\":true,\"isListPriceEditable\":false,\"recognitionRule\":null,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":{\"taxLiabilityAccountId\":null,\"deferredRevenueAccountId\":null,\"recognizedRevenueAccountId\":null,\"contractAssetAccountId\":null,\"ledgerAccountIds\":[]},\"minQuantity\":null,\"maxQuantity\":null,\"defaultQuantity\":null,\"externalId\":null,\"erpId\":null,\"itemCode\":null,\"minAmount\":null,\"maxAmount\":null,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":true,\"creditable\":false,\"recurring\":true,\"eventBased\":false,\"drawdown\":false,\"custom\":false},\"charge\":{\"id\":\"CHRG-XKJ2KZW\",\"name\":\"Yearly Recurring\",\"displayName\":null,\"description\":\"\",\"taxRateId\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"unitOfMeasureId\":null,\"isRenewable\":true,\"isCreditable\":false,\"isListPriceEditable\":false,\"minQuantity\":null,\"defaultQuantity\":null,\"maxQuantity\":null,\"externalId\":null,\"minAmount\":null,\"maxAmount\":null,\"recognitionRuleId\":null,\"erpId\":null,\"itemCode\":null,\"planId\":\"PLAN-8ZXEZC7\",\"amount\":1E+1,\"type\":\"RECURRING\",\"chargeModel\":\"PER_UNIT\",\"recurrence\":{\"cycle\":\"MONTH\",\"step\":1},\"priceTiers\":[],\"isDrawdown\":false,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isCustom\":false,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":null,\"durationInMonths\":null,\"isEventBased\":false,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":true,\"customFields\":{\"CF-4Z99EJ06\":{\"type\":\"STRING\",\"name\":\"charge_custom_field_text\",\"label\":\"charge_custom_field_text\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-RV03VNZH\":{\"type\":\"PICKLIST\",\"name\":\"charge_custom_field_picklist\",\"label\":\"charge_custom_field_picklist\",\"value\":null,\"selections\":[],\"options\":[\"A\",\"B\",\"C\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-P7X095TM\":{\"type\":\"STRING\",\"name\":\"Measurement\",\"label\":\"Measurement\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":{\"value\":\"123\",\"selections\":null}},\"CF-1KW7NJXX\":{\"type\":\"PICKLIST\",\"name\":\"product_type\",\"label\":\"product_type\",\"value\":null,\"selections\":[],\"options\":[\"Standard Success\",\"Premier Success\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null}},\"eventBased\":false,\"drawdown\":false,\"custom\":false,\"creditable\":false},\"quantity\":1,\"isRamp\":true,\"listUnitPriceBeforeOverride\":null,\"listUnitPrice\":10.00000,\"sellUnitPrice\":10.0000000000,\"discountAmount\":0.00,\"discounts\":[],\"predefinedDiscounts\":[],\"amount\":120.0000000000,\"listAmountBeforeOverride\":null,\"listAmount\":120.0000000000,\"annualizedAmount\":120.00,\"taxEstimate\":null,\"effectiveDate\":1656658800,\"endDate\":1688194800,\"listPriceOverrideRatio\":null,\"attributeReferences\":null,\"customFields\":[{\"id\":\"CF-KN0F27K4\",\"type\":\"MULTISELECT_PICKLIST\",\"name\":\"years\",\"label\":\"years\",\"value\":\"\",\"selections\":[],\"options\":[\"7\",\"8\",\"9\",\"10\",\"11\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-2BK5D0F9\",\"type\":\"PICKLIST\",\"name\":\"country\",\"label\":\"country\",\"value\":\"\",\"selections\":[],\"options\":[\"CA\",\"US\",\"AU\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-W6J22WC8\",\"type\":\"MULTISELECT_PICKLIST\",\"name\":\"integration_type\",\"label\":\"Integration Type\",\"value\":\"\",\"selections\":[],\"options\":[\"ERP\",\"CRM\",\"SRM\",\"VM\",\"HCM\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-MPE17DFY\",\"type\":\"PICKLIST\",\"name\":\"product_type\",\"label\":\"Product Type\",\"value\":\"\",\"selections\":[],\"options\":[\"included\",\"itemized\",\"future\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-HEQ8C5WN\",\"type\":\"STRING\",\"name\":\"Order_Line_Value\",\"label\":\"Default Order Line Value\",\"value\":\"> than 100K\",\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null}],\"arrOverride\":null,\"replacedPlan\":null,\"availableReplacementPlan\":null,\"metrics\":{\"tcv\":120.00,\"recurringTotal\":120.00,\"nonRecurringTotal\":0.00,\"arr\":120.00,\"arrWithoutOverride\":120.00,\"deltaTcv\":120.00,\"deltaArr\":120.00},\"dryRunItem\":false,\"creditable\":false},{\"id\":\"6a68f77a-7252-4782-94dc-b93af2a6354c\",\"itemGroupId\":null,\"rank\":3,\"action\":\"ADD\",\"plan\":{\"id\":\"PLAN-8ZXEZC7\",\"entityIds\":[\"*\"],\"name\":\"Yearly Basic Plan\",\"displayName\":null,\"description\":\"\",\"status\":\"ACTIVE\",\"productId\":\"PROD-N2EQ0B0\",\"charges\":[{\"id\":\"CHRG-XKJ2KZW\",\"name\":\"Yearly Recurring\",\"displayName\":null,\"description\":\"\",\"taxRateId\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"unitOfMeasureId\":null,\"isRenewable\":true,\"isCreditable\":false,\"isListPriceEditable\":false,\"minQuantity\":null,\"defaultQuantity\":null,\"maxQuantity\":null,\"externalId\":null,\"minAmount\":null,\"maxAmount\":null,\"recognitionRuleId\":null,\"erpId\":null,\"itemCode\":null,\"planId\":\"PLAN-8ZXEZC7\",\"amount\":1E+1,\"type\":\"RECURRING\",\"chargeModel\":\"PER_UNIT\",\"recurrence\":{\"cycle\":\"MONTH\",\"step\":1},\"priceTiers\":[],\"isDrawdown\":false,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isCustom\":false,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":null,\"durationInMonths\":null,\"isEventBased\":false,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":true,\"customFields\":{},\"eventBased\":false,\"drawdown\":false,\"custom\":false,\"creditable\":false},{\"id\":\"CHRG-D7X9EXC\",\"name\":\"Setup fee\",\"displayName\":null,\"description\":\"\",\"taxRateId\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"unitOfMeasureId\":null,\"isRenewable\":false,\"isCreditable\":false,\"isListPriceEditable\":false,\"minQuantity\":null,\"defaultQuantity\":null,\"maxQuantity\":null,\"externalId\":null,\"minAmount\":null,\"maxAmount\":null,\"recognitionRuleId\":null,\"erpId\":null,\"itemCode\":null,\"planId\":\"PLAN-8ZXEZC7\",\"amount\":1E+3,\"type\":\"ONE_TIME\",\"chargeModel\":\"FLAT_FEE\",\"recurrence\":null,\"priceTiers\":[],\"isDrawdown\":false,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isCustom\":false,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":null,\"durationInMonths\":null,\"isEventBased\":false,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":false,\"customFields\":{},\"eventBased\":false,\"drawdown\":false,\"custom\":false,\"creditable\":false}],\"currency\":\"USD\",\"externalId\":null,\"templateIds\":[],\"replacementPlanIds\":[],\"customFields\":{\"CF-YJPWP7RN\":{\"type\":\"STRING\",\"name\":\"segment\",\"label\":\"Segment\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-DCJG1DFP\":{\"type\":\"STRING\",\"name\":\"plan_custom_field_text\",\"label\":\"plan_custom_field_text\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-M1JHZ06Z\":{\"type\":\"PICKLIST\",\"name\":\"plan_custom_field_picklist\",\"label\":\"plan_custom_field_picklist\",\"value\":null,\"selections\":[],\"options\":[\"A\",\"B\",\"C\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-237WKBPZ\":{\"type\":\"PICKLIST\",\"name\":\"deposit_amount\",\"label\":\"deposit_amount\",\"value\":null,\"selections\":[],\"options\":[\"10000\",\"20000\",\"27500\",\"50000\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-1H0B09NB\":{\"type\":\"PICKLIST\",\"name\":\"Tier\",\"label\":\"Tier\",\"value\":null,\"selections\":[],\"options\":[\"Same\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null}},\"updatedOn\":1644393346},\"subscriptionChargeId\":null,\"currencyConversionRateId\":null,\"subscriptionChargeGroupId\":null,\"chargeDetail\":{\"id\":\"CHRG-XKJ2KZW\",\"planId\":\"PLAN-8ZXEZC7\",\"name\":\"Yearly Recurring\",\"displayName\":null,\"description\":\"\",\"amount\":1E+1,\"type\":\"RECURRING\",\"chargeModel\":\"PER_UNIT\",\"recurrence\":{\"cycle\":\"MONTH\",\"step\":1},\"durationInMonths\":null,\"priceTiers\":[],\"taxRate\":{\"id\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"name\":\"SaaS\",\"description\":\"\",\"taxPercentage\":null,\"taxCode\":\"SW052000\",\"taxInclusive\":false,\"status\":\"ACTIVE\",\"inUse\":true},\"unitOfMeasure\":null,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isRenewable\":true,\"isListPriceEditable\":false,\"recognitionRule\":null,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":{\"taxLiabilityAccountId\":null,\"deferredRevenueAccountId\":null,\"recognizedRevenueAccountId\":null,\"contractAssetAccountId\":null,\"ledgerAccountIds\":[]},\"minQuantity\":null,\"maxQuantity\":null,\"defaultQuantity\":null,\"externalId\":null,\"erpId\":null,\"itemCode\":null,\"minAmount\":null,\"maxAmount\":null,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":true,\"creditable\":false,\"recurring\":true,\"eventBased\":false,\"drawdown\":false,\"custom\":false},\"charge\":{\"id\":\"CHRG-XKJ2KZW\",\"name\":\"Yearly Recurring\",\"displayName\":null,\"description\":\"\",\"taxRateId\":\"ab7550cf-c233-4429-ad52-de84b2778d08\",\"unitOfMeasureId\":null,\"isRenewable\":true,\"isCreditable\":false,\"isListPriceEditable\":false,\"minQuantity\":null,\"defaultQuantity\":null,\"maxQuantity\":null,\"externalId\":null,\"minAmount\":null,\"maxAmount\":null,\"recognitionRuleId\":null,\"erpId\":null,\"itemCode\":null,\"planId\":\"PLAN-8ZXEZC7\",\"amount\":1E+1,\"type\":\"RECURRING\",\"chargeModel\":\"PER_UNIT\",\"recurrence\":{\"cycle\":\"MONTH\",\"step\":1},\"priceTiers\":[],\"isDrawdown\":false,\"minimumCommitBaseChargeId\":null,\"overageBaseChargeId\":null,\"isCustom\":false,\"percent\":null,\"percentDerivedFrom\":null,\"targetPlanIds\":[],\"ledgerAccountMapping\":null,\"durationInMonths\":null,\"isEventBased\":false,\"isDiscount\":false,\"rateCardId\":null,\"billingTerm\":\"UP_FRONT\",\"billingCycle\":\"DEFAULT\",\"shouldTrackArr\":true,\"customFields\":{\"CF-4Z99EJ06\":{\"type\":\"STRING\",\"name\":\"charge_custom_field_text\",\"label\":\"charge_custom_field_text\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-RV03VNZH\":{\"type\":\"PICKLIST\",\"name\":\"charge_custom_field_picklist\",\"label\":\"charge_custom_field_picklist\",\"value\":null,\"selections\":[],\"options\":[\"A\",\"B\",\"C\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},\"CF-P7X095TM\":{\"type\":\"STRING\",\"name\":\"Measurement\",\"label\":\"Measurement\",\"value\":null,\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":{\"value\":\"123\",\"selections\":null}},\"CF-1KW7NJXX\":{\"type\":\"PICKLIST\",\"name\":\"product_type\",\"label\":\"product_type\",\"value\":null,\"selections\":[],\"options\":[\"Standard Success\",\"Premier Success\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null}},\"eventBased\":false,\"drawdown\":false,\"custom\":false,\"creditable\":false},\"quantity\":1,\"isRamp\":true,\"listUnitPriceBeforeOverride\":null,\"listUnitPrice\":10.00000,\"sellUnitPrice\":10.0000000000,\"discountAmount\":0.00,\"discounts\":[],\"predefinedDiscounts\":[],\"amount\":120.0000000000,\"listAmountBeforeOverride\":null,\"listAmount\":120.0000000000,\"annualizedAmount\":120.00,\"taxEstimate\":null,\"effectiveDate\":1688194800,\"endDate\":1719817200,\"listPriceOverrideRatio\":null,\"attributeReferences\":null,\"customFields\":[{\"id\":\"CF-KN0F27K4\",\"type\":\"MULTISELECT_PICKLIST\",\"name\":\"years\",\"label\":\"years\",\"value\":\"\",\"selections\":[],\"options\":[\"7\",\"8\",\"9\",\"10\",\"11\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-2BK5D0F9\",\"type\":\"PICKLIST\",\"name\":\"country\",\"label\":\"country\",\"value\":\"\",\"selections\":[],\"options\":[\"CA\",\"US\",\"AU\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-W6J22WC8\",\"type\":\"MULTISELECT_PICKLIST\",\"name\":\"integration_type\",\"label\":\"Integration Type\",\"value\":\"\",\"selections\":[],\"options\":[\"ERP\",\"CRM\",\"SRM\",\"VM\",\"HCM\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-MPE17DFY\",\"type\":\"PICKLIST\",\"name\":\"product_type\",\"label\":\"Product Type\",\"value\":\"\",\"selections\":[],\"options\":[\"included\",\"itemized\",\"future\"],\"required\":false,\"source\":\"USER\",\"defaultValue\":null},{\"id\":\"CF-HEQ8C5WN\",\"type\":\"STRING\",\"name\":\"Order_Line_Value\",\"label\":\"Default Order Line Value\",\"value\":\"> than 100K\",\"selections\":[],\"options\":[],\"required\":false,\"source\":\"USER\",\"defaultValue\":null}],\"arrOverride\":null,\"replacedPlan\":null,\"availableReplacementPlan\":null,\"metrics\":{\"tcv\":120.00,\"recurringTotal\":120.00,\"nonRecurringTotal\":0.00,\"arr\":120.00,\"arrWithoutOverride\":120.00,\"deltaTcv\":120.00,\"deltaArr\":0.00},\"dryRunItem\":false,\"creditable\":false}],\"lineItemsNetEffect\":null,\"startDate\":0,\"endDate\":0,\"termLength\":null,\"billingCycle\":null,\"billingTerm\":null,\"totalListAmount\":null,\"totalListAmountBeforeOverride\":null,\"taxEstimate\":null,\"totalAmount\":null,\"totalDiscount\":null,\"totalDiscountPercent\":null,\"totalRecurringDiscount\":null,\"totalRecurringDiscountPercent\":null,\"totalNonRecurringDiscount\":null,\"totalNonRecurringDiscountPercent\":null,\"deltaArrPercent\":null,\"status\":null,\"executedOn\":null,\"updatedOn\":null,\"rampInterval\":null,\"orderFormTemplates\":[],\"orderDocumentTemplates\":[],\"orderTerms\":[],\"sfdcOpportunityId\":null,\"isPrimaryOrderForSfdcOpportunity\":false,\"sfdcOpportunityName\":null,\"sfdcOpportunityType\":null,\"sfdcOpportunityStage\":null,\"sfdcOrderCanBeExecuted\":true,\"opportunityCrmType\":null,\"approvalFlows\":[],\"metrics\":null,\"documentMasterTemplate\":null,\"documentCustomContent\":null,\"purchaseOrderNumber\":null,\"purchaseOrderRequiredForInvoicing\":null,\"autoRenew\":false,\"approvalSegment\":null,\"validApprovalSegments\":null,\"billingAnchorDate\":null,\"attachmentId\":null,\"compositeOrderId\":null,\"expiresOn\":null,\"submittedBy\":null,\"recurringDiscountPercent\":null,\"nonRecurringDiscountPercent\":null,\"customFields\":[],\"startDateType\":null,\"customBillingEligibleOrderLineIds\":null,\"orderRamped\":true}"}