{"orderV1": {"id": "1f67b973-3332-458a-9b78-78a89d04184b", "orderId": "1f67b973-3332-458a-9b78-78a89d04184b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "entityId": "test entity id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "externalSubscriptionId": "test-sub-1", "subscriptionTargetVersion": 1, "orderType": "NEW", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2024-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "SUBMITTED", "billingAnchorDate": "2024-01-01T00:00:00Z", "totalAmount": 3000.0, "totalListAmount": 3000.0, "lineItems": [{"planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 1000, "discounts": [{"name": "test discount", "percent": 0.1}], "isRamp": "true", "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2025-01-01T00:00:00Z"}, {"planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 2000, "discounts": [{"name": "test discount", "percent": 0.2}], "isRamp": "true", "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2025-01-01T00:00:00Z", "endDate": "2026-01-01T00:00:00Z"}, {"planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 3000, "discounts": [{"name": "test discount", "percent": 0.3}], "isRamp": "true", "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2026-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}], "shouldRegeneratePdf": true, "expiresOn": null}, "expectedSubscriptionV1": {"subscriptionEntity": {"id": "7a6a0a2d-6248-4804-9125-cffeecbdb537", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "state": "ACTIVE", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "billingTerm": "UP_FRONT", "billingAnchorDate": "2024-01-01T00:00:00Z", "orders": ["2d3f04b0-2523-4ed6-b36a-f5ebc2439447"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, "charges": [{"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 1000, "discounts": [{"name": "test discount", "percent": 0.1}], "isRamp": "true", "startDate": "2024-01-01T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 2000, "discounts": [{"name": "test discount", "percent": 0.2}], "isRamp": "true", "startDate": "2025-01-01T00:00:00Z", "endDate": "2026-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 3000, "discounts": [{"name": "test discount", "percent": 0.3}], "isRamp": "true", "startDate": "2026-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}], "plans": [{"id": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "planId": "Tenant 1 Plan 1"}]}, "changeOrder1": {"externalSubscriptionId": "test-sub-1", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "orderType": "AMENDMENT", "status": "DRAFT", "startDate": "2025-01-01T00:00:00Z", "lineItems": [{"action": "UPDATE", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "externalSubscriptionChargeId": "6de58bd5-8ef6-4408-8f57-5db36b34f618", "quantity": 5000}]}, "expectedOrder2": {"id": "1f67b973-3332-458a-9b78-78a89d04184b", "orderId": "1f67b973-3332-458a-9b78-78a89d04184b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "externalSubscriptionId": "test-sub-1", "subscriptionTargetVersion": 2, "orderType": "AMENDMENT", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2025-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "DRAFT", "billingAnchorDate": "2024-01-01T00:00:00Z", "totalAmount": 2000.0, "totalListAmount": 2000.0, "lineItems": [{"action": "UPDATE", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": -2000, "discounts": [{"name": "test discount", "percent": 0.2}], "isRamp": 1, "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2025-01-01T00:00:00Z", "endDate": "2026-01-01T00:00:00Z"}, {"action": "UPDATE", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 5000, "discounts": [{"name": "test discount", "percent": 0.2}], "isRamp": 1, "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2025-01-01T00:00:00Z", "endDate": "2026-01-01T00:00:00Z"}], "shouldRegeneratePdf": true, "expiresOn": null}, "changeOrder2": {"externalSubscriptionId": "test-sub-1", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "orderType": "AMENDMENT", "status": "SUBMITTED", "startDate": "2025-01-01T00:00:00Z", "lineItems": [{"action": "UPDATE", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "externalSubscriptionChargeId": "6de58bd5-8ef6-4408-8f57-5db36b34f618", "quantity": 10000}]}, "expectedOrder3": {"id": "1f67b973-3332-458a-9b78-78a89d04184b", "orderId": "1f67b973-3332-458a-9b78-78a89d04184b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "externalSubscriptionId": "test-sub-1", "subscriptionTargetVersion": 2, "orderType": "AMENDMENT", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2025-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "SUBMITTED", "billingAnchorDate": "2024-01-01T00:00:00Z", "totalAmount": 2000.0, "totalListAmount": 2000.0, "lineItems": [{"action": "UPDATE", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": -2000, "discounts": [{"name": "test discount", "percent": 0.2}], "isRamp": true, "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2025-01-01T00:00:00Z", "endDate": "2026-01-01T00:00:00Z"}, {"action": "UPDATE", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 10000, "discounts": [{"name": "test discount", "percent": 0.2}], "isRamp": true, "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2025-01-01T00:00:00Z", "endDate": "2026-01-01T00:00:00Z"}], "shouldRegeneratePdf": true, "expiresOn": null}, "expectedSubscriptionV2": {"subscriptionEntity": {"id": "7a6a0a2d-6248-4804-9125-cffeecbdb537", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "state": "ACTIVE", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "billingTerm": "UP_FRONT", "billingAnchorDate": "2024-01-01T00:00:00Z", "orders": ["2d3f04b0-2523-4ed6-b36a-f5ebc2439447"], "version": 2, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, "charges": [{"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 1000, "discounts": [{"name": "test discount", "percent": 0.1}], "isRamp": true, "startDate": "2024-01-01T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 2, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 10000, "discounts": [{"name": "test discount", "percent": 0.2}], "isRamp": true, "startDate": "2025-01-01T00:00:00Z", "endDate": "2026-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 2, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 3000, "discounts": [{"name": "test discount", "percent": 0.3}], "isRamp": true, "startDate": "2026-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 2, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}]}}