{"orderV1": {"id": "1f67b973-3332-458a-9b78-78a89d04184b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "entityId": "test entity id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "externalSubscriptionId": "test-sub-1", "subscriptionTargetVersion": 1, "orderType": "NEW", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2024-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "EXECUTED", "billingStartDate": "2024-01-01T00:00:00Z", "billingAnchorDate": "2024-01-01T00:00:00Z", "totalAmount": 4000.0, "totalListAmount": 4000.0, "lineItems": [{"planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.3}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"planId": "Tenant 1 Plan 1", "chargeId": "ff313d39-0ac7-4e6c-b711-776f7833f10d", "tenantId": "test tenant 1 id", "quantity": 20, "discounts": [{"name": "test discount", "percent": 0.2}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"planId": "Tenant 1 Plan 2", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.1}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"planId": "Tenant 1 Plan 3", "chargeId": "e9f98431-f248-4fbc-be5e-eff37473392a", "tenantId": "test tenant 1 id", "quantity": 50, "discounts": [{"name": "test discount", "percent": 0.2}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}], "orderTemplateIds": [], "shouldRegeneratePdf": true, "expiresOn": null}, "expectedSubscriptionV1": {"subscriptionEntity": {"id": "7a6a0a2d-6248-4804-9125-cffeecbdb537", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "state": "ACTIVE", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "billingTerm": "UP_FRONT", "billingAnchorDate": "2024-01-01T00:00:00Z", "orders": ["2d3f04b0-2523-4ed6-b36a-f5ebc2439447"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, "charges": [{"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.3}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "ff313d39-0ac7-4e6c-b711-776f7833f10d", "tenantId": "test tenant 1 id", "quantity": 20, "discounts": [{"name": "test discount", "percent": 0.2}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.1}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "e9f98431-f248-4fbc-be5e-eff37473392a", "tenantId": "test tenant 1 id", "quantity": 50, "discounts": [{"name": "test discount", "percent": 0.2}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}]}, "changeOrder1": {"externalSubscriptionId": "test-sub-1", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "orderType": "AMENDMENT", "status": "EXECUTED", "startDate": "2024-09-01T00:00:00Z", "lineItems": [{"action": "UPDATE", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "externalSubscriptionChargeId": "6de58bd5-8ef6-4408-8f57-5db36b34f618", "quantity": 100}], "orderTemplateIds": []}, "expectedOrder2": {"id": "1f67b973-3332-458a-9b78-78a89d04184b", "orderId": "1f67b973-3332-458a-9b78-78a89d04184b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "externalSubscriptionId": "test-sub-1", "subscriptionTargetVersion": 2, "orderType": "AMENDMENT", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2024-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "EXECUTED", "billingAnchorDate": "2024-01-01T00:00:00Z", "totalAmount": 2000.0, "totalListAmount": 2000.0, "lineItems": [{"action": "UPDATE", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": -30, "discounts": [{"name": "test discount", "percent": 0.3}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"action": "UPDATE", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 100, "discounts": [{"name": "test discount", "percent": 0.3}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2024-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}], "shouldRegeneratePdf": true, "expiresOn": null}, "expectedSubscriptionV2": {"subscriptionEntity": {"id": "7a6a0a2d-6248-4804-9125-cffeecbdb537", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "state": "ACTIVE", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "billingTerm": "UP_FRONT", "billingAnchorDate": "2024-01-01T00:00:00Z", "orders": ["2d3f04b0-2523-4ed6-b36a-f5ebc2439447"], "version": 2, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, "charges": [{"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.3}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2024-09-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 2, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2025-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 100, "discounts": [{"name": "test discount", "percent": 0.3}], "startDate": "2024-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 2, "versionStart": "2025-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.1}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "e9f98431-f248-4fbc-be5e-eff37473392a", "tenantId": "test tenant 1 id", "quantity": 50, "discounts": [{"name": "test discount", "percent": 0.2}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}]}, "changeOrder2": {"externalSubscriptionId": "test-sub-1", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "orderType": "AMENDMENT", "status": "DRAFT", "startDate": "2025-09-01T00:00:00Z", "lineItems": [{"action": "REMOVE", "planId": "Tenant 1 Plan 2", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "externalSubscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "quantity": 0}]}, "expectedOrder3": {"id": "1f67b973-3332-458a-9b78-78a89d04184b", "orderId": "1f67b973-3332-458a-9b78-78a89d04184b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "externalSubscriptionId": "test-sub-1", "subscriptionTargetVersion": 3, "orderType": "AMENDMENT", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2025-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "DRAFT", "billingAnchorDate": "2024-01-01T00:00:00Z", "totalAmount": 1000.0, "totalListAmount": 1000.0, "lineItems": [{"action": "REMOVE", "planId": "Tenant 1 Plan 2", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "tenantId": "test tenant 1 id", "quantity": -30, "discounts": [{"name": "test discount", "percent": 0.1}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2025-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}], "shouldRegeneratePdf": true, "expiresOn": null}, "changeOrder3": {"externalSubscriptionId": "test-sub-1", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "orderType": "AMENDMENT", "status": "EXECUTED", "subscriptionTargetVersion": 3, "startDate": "2025-09-01T00:00:00Z", "lineItems": [{"action": "REMOVE", "planId": "Tenant 1 Plan 2", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "externalSubscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "quantity": 0}, {"action": "UPDATE", "planId": "Tenant 1 Plan 3", "chargeId": "e9f98431-f248-4fbc-be5e-eff37473392a", "externalSubscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "quantity": 150}]}, "expectedOrder4": {"id": "1f67b973-3332-458a-9b78-78a89d04184b", "orderId": "1f67b973-3332-458a-9b78-78a89d04184b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "externalSubscriptionId": "test-sub-1", "subscriptionTargetVersion": 3, "orderType": "AMENDMENT", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2025-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "EXECUTED", "billingAnchorDate": "2024-01-01T00:00:00Z", "totalAmount": 3000.0, "totalListAmount": 3000.0, "lineItems": [{"action": "REMOVE", "planId": "Tenant 1 Plan 2", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "tenantId": "test tenant 1 id", "quantity": -30, "discounts": [{"name": "test discount", "percent": 0.1}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2025-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"action": "UPDATE", "planId": "Tenant 1 Plan 3", "chargeId": "e9f98431-f248-4fbc-be5e-eff37473392a", "tenantId": "test tenant 1 id", "quantity": -50, "discounts": [{"name": "test discount", "percent": 0.2}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2025-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"action": "UPDATE", "planId": "Tenant 1 Plan 3", "chargeId": "e9f98431-f248-4fbc-be5e-eff37473392a", "tenantId": "test tenant 1 id", "quantity": 150, "discounts": [{"name": "test discount", "percent": 0.2}], "amount": 1000.0, "listAmount": 0.0, "sellUnitPrice": 0.0, "listUnitPrice": 0.0, "effectiveDate": "2025-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}], "shouldRegeneratePdf": true, "expiresOn": null}, "expectedSubscriptionV3": {"subscriptionEntity": {"id": "7a6a0a2d-6248-4804-9125-cffeecbdb537", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "state": "ACTIVE", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "billingTerm": "UP_FRONT", "billingAnchorDate": "2024-01-01T00:00:00Z", "orders": ["2d3f04b0-2523-4ed6-b36a-f5ebc2439447"], "version": 3, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, "charges": [{"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.3}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2024-09-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 2, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2025-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 100, "discounts": [{"name": "test discount", "percent": 0.3}], "startDate": "2024-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 2, "versionStart": "2024-09-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "69fe4219-8f79-41b9-ae0e-fb51917e21d8", "tenantId": "test tenant 1 id", "quantity": 30, "discounts": [{"name": "test discount", "percent": 0.1}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2025-09-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 3, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "e9f98431-f248-4fbc-be5e-eff37473392a", "tenantId": "test tenant 1 id", "quantity": 50, "discounts": [{"name": "test discount", "percent": 0.2}], "startDate": "2024-01-01T00:00:00Z", "endDate": "2025-09-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 3, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, {"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "chargeId": "e9f98431-f248-4fbc-be5e-eff37473392a", "tenantId": "test tenant 1 id", "quantity": 150, "discounts": [{"name": "test discount", "percent": 0.2}], "startDate": "2025-09-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 3, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}]}}