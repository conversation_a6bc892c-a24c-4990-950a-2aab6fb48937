INSERT INTO tenant (id, tenant_id, name, email, phone_number, password, encrypted_password, created_on, updated_on)
VALUES ('84ae8d32-8690-4540-85c9-b4804fb9484f', 'test-tenant-id', 'Test tenant',
        '<EMAIL>', '778', 'some-password', 'JT62EjqkhQedW0owtaWpUg==', NOW(), NOW()),
       ('25de0de4-81e6-458f-8207-8b53f6b20953', 'normal-tenant-id', 'Normal tenant',
        '<EMAIL>', '604', 'another-password', 'KeIprppB+ZoGFe8tyVc5WAHl3OdYLZu6Pp+OMZSgXg0=', NOW(), NOW());

INSERT INTO payment_term_settings(tenant_id)
VALUES ('test-tenant-id'),
       ('normal-tenant-id')
ON CONFLICT DO NOTHING;

INSERT INTO account (account_id, tenant_id, entity_ids, name, phone_number, timezone)
VALUES ('ACCT-1', 'test-tenant-id', ARRAY['*'], 'Test account 1', '778', 'US/Pacific'),
       ('ACCT-2', 'normal-tenant-id', ARRAY['*'], 'Normal account 2', '604', 'US/Pacific'),
       ('ACCT-3', 'normal-tenant-id', ARRAY['*'], 'Normal account 3', '604', 'US/Pacific');

INSERT INTO subscription (subscription_id, tenant_id, account_id, currency, payment_term, state, term_length_cycle, term_length_step, billing_cycle, billing_step, creation_time, start_date, end_date, billing_anchor_date, billing_term, orders, version, version_start, version_end)
VALUES ('SUB-TEST-TENANT-NO-INVOICE-1', 'test-tenant-id', 'ACCT-1', 'CAD', 'NET30', 'ACTIVE', 'YEAR', 1, 'MONTH', 1, NOW(), NOW(), DATEADD('YEAR',1, NOW()), NOW(), 'UP_FRONT', 'ORD-1', 1, NOW(), NOW()),
       ('SUB-NO-INVOICE-1', 'normal-tenant-id', 'ACCT-2', 'USD', 'NET30', 'ACTIVE', 'YEAR', 1, 'MONTH', 1, NOW(), NOW(), DATEADD('YEAR',1, NOW()), NOW(), 'UP_FRONT', 'ORD-2', 1, NOW(), NOW()),
       ('SUB-IN-THE-PAST', 'normal-tenant-id', 'ACCT-3', 'USD', 'NET30', 'ACTIVE', 'YEAR', 1, 'MONTH', 1, NOW(), DATEADD('YEAR',-2, NOW()), DATEADD('YEAR',-1, NOW()), DATEADD('YEAR',-2, NOW()), 'UP_FRONT', 'ORD-2', 1, NOW(), NOW()),
       ('SUB-SHOULD-BE-RUN-1', 'normal-tenant-id', 'ACCT-3', 'USD', 'NET30', 'ACTIVE', 'YEAR', 1, 'MONTH', 1, NOW(), DATEADD('DAY',1, NOW()), DATEADD('DAY',366, NOW()), DATEADD('DAY',1, NOW()), 'UP_FRONT', 'ORD-2', 1, NOW(), NOW()),
       ('SUB-SHOULD-BE-RUN-2', 'normal-tenant-id', 'ACCT-3', 'USD', 'NET30', 'ACTIVE', 'YEAR', 1, 'MONTH', 1, NOW(), DATEADD('DAY',-10, NOW()), DATEADD('DAY',366, NOW()), DATEADD('DAY',1, NOW()), 'UP_FRONT', 'ORD-2', 1, NOW(), NOW()),
       ('SUB-TEST-THROWS-ERROR', 'normal-tenant-id', 'ACCT-3', 'USD', 'NET30', 'ACTIVE', 'YEAR', 1, 'MONTH', 1, NOW(), DATEADD('DAY',-10, NOW()), DATEADD('DAY',366, NOW()), DATEADD('DAY',1, NOW()), 'UP_FRONT', 'ORD-2', 1, NOW(), NOW()),
       ('SUB-SHOULD-BE-RUN-3', 'normal-tenant-id', 'ACCT-2', 'USD', 'NET30', 'ACTIVE', 'YEAR', 1, 'MONTH', 1, NOW(), DATEADD('HOUR', 3, DATEADD('MONTH',-1, NOW())), DATEADD('YEAR',1, NOW()), DATEADD('DAY', 1, DATEADD('MONTH',-1, NOW())), 'UP_FRONT', 'ORD-2', 1, NOW(), NOW());
