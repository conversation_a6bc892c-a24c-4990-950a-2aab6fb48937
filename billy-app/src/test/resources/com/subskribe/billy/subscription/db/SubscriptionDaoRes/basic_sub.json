[{"subscription": {"id": "7a6a0a2d-6248-4804-9125-cffeecbdb537", "subscriptionId": "basic-sub-1", "tenantId": "test tenant 1 id", "entityId": "ENT-1234567", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "state": "ACTIVE", "currency": "USD", "paymentTerm": "NET30", "billingTerm": "UP_FRONT", "startDate": "2020-02-22T16:00:00Z", "endDate": "2022-02-22T16:00:00Z", "billingAnchorDate": "2020-02-22T16:00:00Z", "termLength": {"cycle": "MONTH", "step": 24}, "billingCycle": {"cycle": "MONTH", "step": 3}, "orders": ["f1976298-807c-4472-9ff2-2abebda5c450"], "creationTime": "2020-02-15T20:37:16Z", "version": 1, "versionStart": "2020-02-15T20:37:16Z", "versionEnd": "2022-02-22T16:00:00Z"}, "charges": [{"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionChargeGroupId": "083751b5-7eec-4825-ad4a-2e79feefc43b", "subscriptionId": "basic-sub-1", "tenantId": "test tenant 1 id", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "startDate": "2020-02-22T16:00:00Z", "endDate": "2022-02-22T16:00:00Z", "quantity": 20, "discounts": [{"name": "test discount", "percent": 20.0}], "orderLines": ["27919b80-937a-437a-a95e-4eefa5122904"], "version": 1, "versionStart": "2020-02-22T16:00:00Z", "versionEnd": "2022-02-22T16:00:00Z"}, {"id": "ff313d39-0ac7-4e6c-b711-776f7833f10d", "chargeId": "ff313d39-0ac7-4e6c-b711-776f7833f10d", "subscriptionChargeId": "8c2d5447-b362-49e6-9717-099080ee8f90", "subscriptionChargeGroupId": "fb62235e-e6f5-4ba7-a654-751baac15f20", "subscriptionId": "basic-sub-1", "tenantId": "test tenant 1 id", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "startDate": "2020-02-22T16:00:00Z", "endDate": "2022-02-22T16:00:00Z", "quantity": 50, "discounts": [{"name": "test discount", "percent": 20.0}], "orderLines": ["476d1e0e-928b-4118-a847-c1aed155a85c"], "version": 1, "versionStart": "2020-02-22T16:00:00Z", "versionEnd": "2022-02-22T16:00:00Z"}], "chargesMap": [{"id": "370ebc55-aee7-4a10-aae9-45f3d9eb3d37", "tenantId": "test tenant 1 id", "subscriptionId": "7a6a0a2d-6248-4804-9125-cffeecbdb537", "subscriptionChargeGroupId": "083751b5-7eec-4825-ad4a-2e79feefc43b", "version": 1}, {"id": "1e82b318-6462-4a84-9ed8-d7c2a713a7e9", "tenantId": "test tenant 1 id", "subscriptionId": "7a6a0a2d-6248-4804-9125-cffeecbdb537", "subscriptionChargeGroupId": "fb62235e-e6f5-4ba7-a654-751baac15f20", "version": 1}]}]