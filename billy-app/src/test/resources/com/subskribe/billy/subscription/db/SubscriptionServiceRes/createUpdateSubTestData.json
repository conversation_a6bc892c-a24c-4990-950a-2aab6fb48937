{"orderV1": {"id": "2d3f04b0-2523-4ed6-b36a-f5ebc2439447", "orderId": "2d3f04b0-2523-4ed6-b36a-f5ebc2439447", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "entityId": "ENT-1234567", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "externalSubscriptionId": "test-sub-1", "subscriptionTargetVersion": 1, "orderType": "NEW", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "SUBMITTED", "billingAnchorDate": "2024-01-01T00:00:00Z", "lineItems": [{"id": "1fc28585-20c7-4a8e-a475-0ad8fd9f3771", "orderLineId": "1fc28585-20c7-4a8e-a475-0ad8fd9f3771", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 10, "discounts": [{"name": "test discount", "percent": 10.0}], "effectiveDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}]}, "expectedSubscriptionV1": {"subscriptionEntity": {"id": "7a6a0a2d-6248-4804-9125-cffeecbdb537", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "state": "ACTIVE", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingAnchorDate": "2024-01-01T00:00:00Z", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "billingTerm": "UP_FRONT", "orders": ["2d3f04b0-2523-4ed6-b36a-f5ebc2439447"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}, "charges": [{"id": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionChargeGroupId": "083751b5-7eec-4825-ad4a-2e79feefc43b", "subscriptionId": "test-sub-1", "tenantId": "test tenant 1 id", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "quantity": 10, "discounts": [{"name": "test discount", "percent": 10.0}], "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771"], "version": 1, "versionStart": "2024-01-01T00:00:00Z", "versionEnd": "2024-01-01T00:00:00Z"}], "plans": [{"id": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "planId": "Tenant 1 Plan 1"}]}, "orderV2": {"id": "090c15f7-843f-412f-a0b0-75d86930132b", "orderId": "090c15f7-843f-412f-a0b0-75d86930132b", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "entityId": "ENT-1234567", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "externalSubscriptionId": "test-sub-1", "subscriptionTargetVersion": 2, "orderType": "AMENDMENT", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "SUBMITTED", "billingAnchorDate": "2024-01-01T00:00:00Z", "lineItems": [{"id": "13a7a6bd-eb61-4289-ae1b-a8e489b834c2", "orderLineId": "13a7a6bd-eb61-4289-ae1b-a8e489b834c2", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": -10, "discounts": [{"name": "test discount", "percent": 10.0}], "effectiveDate": "2025-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}, {"id": "e80f9fa6-47f2-4068-ac8a-f972a6f59f5c", "orderLineId": "e80f9fa6-47f2-4068-ac8a-f972a6f59f5c", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": 20, "discounts": [{"name": "test discount", "percent": 20.0}], "effectiveDate": "2025-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}]}, "expectedSubscriptionV2": {"subscriptionEntity": {"id": "0abb4ad2-e242-47b8-875a-b20705d0f5f1", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "state": "ACTIVE", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingAnchorDate": "2024-01-01T00:00:00Z", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "billingTerm": "UP_FRONT", "orders": ["2d3f04b0-2523-4ed6-b36a-f5ebc2439447", "090c15f7-843f-412f-a0b0-75d86930132b"], "version": 2, "versionStart": "2025-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}, "charges": [{"id": "bc422a90-1ca3-47e0-918e-97b26200e3b5", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionChargeGroupId": "083751b5-7eec-4825-ad4a-2e79feefc43b", "subscriptionId": "test-sub-1", "tenantId": "test tenant 1 id", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "startDate": "2024-01-01T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "quantity": 10, "discounts": [{"name": "test discount", "percent": 10.0}], "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771", "13a7a6bd-eb61-4289-ae1b-a8e489b834c2"], "version": 2, "versionStart": "2025-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}, {"id": "a27f6bc0-725e-4a67-b479-4c80c16def19", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionChargeGroupId": "083751b5-7eec-4825-ad4a-2e79feefc43b", "subscriptionId": "test-sub-1", "tenantId": "test tenant 1 id", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "startDate": "2025-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "quantity": 20, "discounts": [{"name": "test discount", "percent": 20.0}], "orderLines": ["13a7a6bd-eb61-4289-ae1b-a8e489b834c2"], "version": 2, "versionStart": "2025-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}], "plans": [{"id": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "planId": "Tenant 1 Plan 1"}]}, "orderV3": {"id": "fa0dadda-e466-46c1-8b26-a5199ad15734", "orderId": "fa0dadda-e466-46c1-8b26-a5199ad15734", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "entityId": "ENT-1234567", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "externalSubscriptionId": "test-sub-1", "subscriptionTargetVersion": 3, "orderType": "AMENDMENT", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "billingTerm": "UP_FRONT", "status": "SUBMITTED", "billingAnchorDate": "2024-01-01T00:00:00Z", "lineItems": [{"id": "de571915-2a16-418d-a4f7-0ba35955752b", "orderLineId": "de571915-2a16-418d-a4f7-0ba35955752b", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "planId": "Tenant 1 Plan 1", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "tenantId": "test tenant 1 id", "quantity": -20, "discounts": [{"name": "test discount", "percent": 20.0}], "effectiveDate": "2026-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z"}]}, "expectedSubscriptionV3": {"subscriptionEntity": {"id": "7b108310-378d-42eb-b317-5ff0cedba423", "subscriptionId": "test-sub-1", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "tenantId": "test tenant 1 id", "shippingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "billingContactId": "8625d494-8837-4649-9686-fe7487aae7da", "state": "ACTIVE", "startDate": "2024-01-01T00:00:00Z", "endDate": "2027-01-01T00:00:00Z", "termLength": {"cycle": "YEAR", "step": 3}, "billingCycle": {"cycle": "YEAR", "step": 1}, "paymentTerm": "NET30", "currency": "USD", "billingTerm": "UP_FRONT", "billingAnchorDate": "2024-01-01T00:00:00Z", "orders": ["2d3f04b0-2523-4ed6-b36a-f5ebc2439447", "090c15f7-843f-412f-a0b0-75d86930132b", "fa0dadda-e466-46c1-8b26-a5199ad15734"], "version": 3, "versionStart": "2025-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}, "charges": [{"id": "00b28898-e85b-4428-af10-bcb34ebbb54d", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionChargeGroupId": "083751b5-7eec-4825-ad4a-2e79feefc43b", "subscriptionId": "test-sub-1", "tenantId": "test tenant 1 id", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "startDate": "2024-01-01T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "quantity": 10, "discounts": [{"name": "test discount", "percent": 10.0}], "orderLines": ["1fc28585-20c7-4a8e-a475-0ad8fd9f3771", "13a7a6bd-eb61-4289-ae1b-a8e489b834c2", "7b108310-378d-42eb-b317-5ff0cedba423"], "version": 3, "versionStart": "2025-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}, {"id": "d4ac6e3b-0b72-4d9d-b2a4-2834eb43629c", "chargeId": "c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a", "subscriptionChargeId": "363db8f3-0c2d-46bd-a52a-0a204291d5bc", "subscriptionChargeGroupId": "083751b5-7eec-4825-ad4a-2e79feefc43b", "subscriptionId": "test-sub-1", "tenantId": "test tenant 1 id", "accountId": "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68", "startDate": "2025-01-01T00:00:00Z", "endDate": "2026-01-01T00:00:00Z", "quantity": 20, "discounts": [{"name": "test discount", "percent": 20.0}], "orderLines": ["13a7a6bd-eb61-4289-ae1b-a8e489b834c2", "7b108310-378d-42eb-b317-5ff0cedba423"], "version": 3, "versionStart": "2025-01-01T00:00:00Z", "versionEnd": "2027-01-01T00:00:00Z"}]}}