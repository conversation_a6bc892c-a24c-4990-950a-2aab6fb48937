
INSERT INTO product (id, product_id, tenant_id, name, display_name, description)
VALUES
    ('63be3a3c-1e18-4c3d-a9a6-f6c9136cd82c', 'Tenant 1 Product 1', 'test tenant 1 id',
     'Fantastic product', 'Fantastic product', 'T1P1 description');


INSERT INTO plan (id, plan_id, tenant_id, status, name, display_name, description, product_id)
   VALUES
    ('363db8f3-0c2d-46bd-a52a-0a204291d5bc', 'Tenant 1 Plan 1', 'test tenant 1 id',
     'ACTIVE', 'Fantastic plan', 'Fantastic plan', 'T1P1 description', 'Tenant 1 Product 1'),
    ('8c2d5447-b362-49e6-9717-099080ee8f90', 'Tenant 1 Plan 2', 'test tenant 1 id',
     'ACTIVE', 'Another fantastic plan', 'Another fantastic plan', 'T1P2 description', 'Tenant 1 Product 1');

INSERT INTO charge(id, charge_id, tenant_id, subskribe_plan_id, name, display_name, description, amount, charge_type, charge_model, cycle, step, plan_id, is_drawdown, billing_term)
VALUES
(
'c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a', 'c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a', 'test tenant 1 id', 'Tenant 1 Plan 1', 'Plan 1 Charge 1', 'Plan 1 Charge 1', 'Plan 1 Charge 1 description', 10,
'RECURRING', 'PER_UNIT', 'MONTH', 1, '363db8f3-0c2d-46bd-a52a-0a204291d5bc', false, 'UP_FRONT'
),
(
'ff313d39-0ac7-4e6c-b711-776f7833f10d', 'ff313d39-0ac7-4e6c-b711-776f7833f10d', 'test tenant 1 id', 'Tenant 1 Plan 2', 'Plan 1 Charge 1', 'Plan 1 Charge 1', 'Plan 1 Charge 1 description', 20,
'RECURRING', 'PER_UNIT', 'MONTH', 1, '8c2d5447-b362-49e6-9717-099080ee8f90', false, 'UP_FRONT'
);

INSERT INTO account_address(address_id, tenant_id, street_address_line1, street_address_line2, city, state, country, zipcode, created_on, updated_on)
VALUES
(
'ffb3c876-4f4f-419d-a99a-d0881acbeea8', 'test tenant 1 id', 'street address1',
'street_address2', 'city', 'state', 'usa', '49295', TO_TIMESTAMP('2020-12-01', 'YYYY-MM-DD'), TO_TIMESTAMP('2020-12-01', 'YYYY-MM-DD')
);

INSERT INTO account(account_id, tenant_id, entity_ids, name, primary_contact_id, phone_number, address_id, payment_method_id, timezone,
created_on, updated_on)
VALUES
(
'c00cf41d-2b30-49d6-ae8f-a6913e3a4b68', 'test tenant 1 id', ARRAY['*'], 'test account name', '8625d494-8837-4649-9686-fe7487aae7da',
'**********', 'ffb3c876-4f4f-419d-a99a-d0881acbeea8', null, 'US/Pacific', TO_TIMESTAMP('2020-12-01', 'YYYY-MM-DD'), TO_TIMESTAMP('2020-12-01', 'YYYY-MM-DD')
);

INSERT INTO account_contact(contact_id, account_id, tenant_id, address_id, first_name, last_name, email, email_verified, phone_number, state, created_on, updated_on)
VALUES
(
    '8625d494-8837-4649-9686-fe7487aae7da', 'c00cf41d-2b30-49d6-ae8f-a6913e3a4b68', 'test tenant 1 id', 'ffb3c876-4f4f-419d-a99a-d0881acbeea8', 'First Name', 'Last Name',
    '<EMAIL>', true, '**********', 'ACTIVE', TO_TIMESTAMP('2020-12-01', 'YYYY-MM-DD'), TO_TIMESTAMP('2020-12-01', 'YYYY-MM-DD')
);
