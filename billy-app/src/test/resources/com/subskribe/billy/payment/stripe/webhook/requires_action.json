{"id": "evt_3RyXurLKK4sQm01U0WDAopG1", "object": "event", "api_version": "2025-05-28.basil", "context": "acct_1RgAN8LKK4sQm01U", "created": 1755780509, "data": {"object": {"id": "pi_3RyXurLKK4sQm01U0Kh3drJq", "object": "payment_intent", "amount": 1325000, "amount_capturable": 0, "amount_details": {"tip": {}}, "amount_received": 0, "application": "ca_LPwVTmhuHQ7fCesDjrUWorsyIqVVbk63", "application_fee_amount": null, "automatic_payment_methods": null, "canceled_at": null, "cancellation_reason": null, "capture_method": "automatic", "client_secret": "pi_3RyXurLKK4sQm01U0Kh3drJq_secret_5Cach1OPPSMKpfbQtE8XgHWg3", "confirmation_method": "automatic", "created": 1755780509, "currency": "usd", "customer": "cus_SbNFrabMUwoDIR", "description": null, "excluded_payment_method_types": null, "last_payment_error": null, "latest_charge": null, "livemode": false, "metadata": {"bal": "13250.0000000000", "inv": "INV-000092", "pm": "pm_1RyWwMLKK4sQm01UFSEviEoy", "sbsk_env": "local", "tid": "f35504c0-1b8b-4739-a466-caab3f6f6c7e"}, "next_action": {"redirect_to_url": {"return_url": "http://localdev.subskribe.net:3000/invoices/INV-000092", "url": "https://hooks.stripe.com/3d_secure_2/hosted?merchant=acct_1RgAN8LKK4sQm01U&payment_intent=pi_3RyXurLKK4sQm01U0Kh3drJq&payment_intent_client_secret=pi_3RyXurLKK4sQm01U0Kh3drJq_secret_5Cach1OPPSMKpfbQtE8XgHWg3&publishable_key=pk_test_51RgAN8LKK4sQm01UvvSvWKBm2k5KaHe6IMXcVfkwUCfa0XFLShS8PU1VVJBi0M7iS5zBQcwwO4uLrlam7petgJnE00Id3vDW5E&source=payatt_3RyXurLKK4sQm01U0L6T77iW"}, "type": "redirect_to_url"}, "on_behalf_of": null, "payment_method": "pm_1RyWwMLKK4sQm01UFSEviEoy", "payment_method_configuration_details": null, "payment_method_options": {"card": {"installments": null, "mandate_options": null, "network": null, "request_three_d_secure": "automatic"}}, "payment_method_types": ["card"], "processing": null, "receipt_email": null, "review": null, "setup_future_usage": null, "shipping": null, "source": null, "statement_descriptor": null, "statement_descriptor_suffix": null, "status": "requires_action", "transfer_data": null, "transfer_group": null}}, "livemode": false, "pending_webhooks": 0, "request": {"id": "req_KKlmqBabUTZiNs", "idempotency_key": "681d46b9-bb4a-4e54-9dfd-cbaa3c8ec975"}, "type": "payment_intent.requires_action"}