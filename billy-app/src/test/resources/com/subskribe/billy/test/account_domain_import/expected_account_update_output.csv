Import.<PERSON><PERSON><PERSON>,Import.Success,Failure.Reason,Entity.Delete,Account.Id,Account.Name,Account.LegalName,Account.Description,Account.EntityDisplayIds,Account.CrmId,Account.ErpId,Account.Phone,Account.AddressLine1,Account.AddressLine2,Account.City,Account.State,Account.Zip,Account.Country,Account.Currency,Account.<PERSON><PERSON>,Contact.Id,Contact.ErpId,Contact.FirstName,Contact.LastName,Contact.Email,Contact.Phone,Contact.AddressLine1,Contact.AddressLine2,Contact.City,Contact.State,Contact.Zip,Contact.Country,Contact.StrictAddressValidation
2,N,expected at least 12 data columns but found 3,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3,Y,,N,ACCT-LIVE,Awesome Account,,what is this,,,,**********,,,,,,,USD,N,,,,,,,,,,,,,
4,Y,,Y,ACCT-123,Disco,,Disco Never Died,,,,************,,,,,,,USD,N,,,,,,,,,,,,,
5,N,contact data present but Account.Id and Contact.Id missing,N,,Awesome Account,,what is this,,,,**********,,,,,,,USD,N,,Test,,,,,,,,,,,N
6,N,"only Account.Id or Contact.Id are present, account/contact data missing or delete flag not set",N,ACCT-LIVE,,,,,,,,,,,,,,USD,N,,,,,,,,,,,,,
7,N,empty row please provide data,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
8,Y,,N,ACCT-LIVE,,,,,,,,,,,,,,,,CONTACT-ABC,Test,,,,,,,,,,,N
9,N,"only Account.Id or Contact.Id are present, account/contact data missing or delete flag not set",N,ACCT-LIVE,,,,,,,,,,,,,,USD,N,CONTACT-ABC,,,,,,,,,,,,N
10,N,"only Account.Id or Contact.Id are present, account/contact data missing or delete flag not set",N,ACCT-LIVE,,,,,,,,,,,,,,USD,N,CONTACT-ABC,,,,,,,,,,,,N
11,N,contact data present but Account.Id and Contact.Id missing,N,,,,,,,,,,,,,,,USD,N,,,First,Last,<EMAIL>,,,,,,,,N
12,N,contact data present but Account.Id and Contact.Id missing,N,,,,,,,,,,,,,,,,,CONTACT-123,,First,Last,<EMAIL>,,,,,,,,N
13,N,contact data present but Account.Id and Contact.Id missing,N,,,,,,,,,,,,,,,,,,,First,Last,<EMAIL>,,,,,,,,N
14,Y,,N,ACCT-LIVE,,,,,,,,,,,,,,,,CONTACT-ABC,,First,Last,<EMAIL>,,,,,,,,N
15,Y,,Y,ACCT-LIVE,,,,,,,,,,,,,,,,CONTACT-DELETE,,First,Last,<EMAIL>,,,,,,,,N
16,N,Entity.Delete present but Account.Id and/or Contact.Id missing,Y,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
