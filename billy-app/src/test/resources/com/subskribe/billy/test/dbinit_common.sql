INSERT INTO tenant (id, tenant_id, name, email, phone_number, encrypted_password, created_on, updated_on)
VALUES ('941b6f7a-7b60-4a5e-81a5-aee0e5523240', 'test tenant 1 id', 'Test tenant 1',
        '<EMAIL>', '+23-1254375182', 'RPpdWWYrEzpL2t8Bnw/fIw==',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
       ('731dfdc3-6558-4005-b8ab-4db70947d115', '27b59431-0cc7-4c87-883b-be345e9bd525', 'Test tenant 2',
        '<EMAIL>', '01234', 'k1q6PWnJ303ignW+U6X19A==',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
       ('e31c2c76-5fae-4158-9772-e108fdddfc7e', '31f7e098-14db-4497-8310-aa8a1602afd0', 'Test tenant 3',
        '<EMAIL>', '604', 'kAqw8BKJoZsb3cWWvxoBQe8t4mMIqnD5tnmSmzqhiX0=',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO payment_term_settings(tenant_id)
VALUES ('test tenant 1 id'),
       ('27b59431-0cc7-4c87-883b-be345e9bd525'),
       ('31f7e098-14db-4497-8310-aa8a1602afd0')
ON CONFLICT DO NOTHING;

INSERT INTO account_address (address_id, tenant_id, street_address_line1,
                             street_address_line2, city, state, country, zipcode, created_on, updated_on)
VALUES ('a552d3c0-e19b-453b-a17f-ed0de82ee8ec',
        'test tenant 1 id', '#1234', '5679 Main St.',
        'Disney world', 'CA', 'USA', '98765', CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP);

INSERT INTO account (account_id, tenant_id, entity_ids, name, primary_contact_id, phone_number, address_id, timezone, created_on,
                     updated_on)
VALUES ('4611754a-ddee-4c25-8086-c6fdc3494be4', 'test tenant 1 id', ARRAY['*'], 'Test account 1 name',
        'edd70581-6c77-46f7-a3b2-e9156b91f0d6', '+23-**********', 'a552d3c0-e19b-453b-a17f-ed0de82ee8ec',
        'America/Los_Angeles', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO account_contact (contact_id, account_id, tenant_id, first_name, last_name, email,
                          email_verified, phone_number, state, address_id, created_on, updated_on)
VALUES ('edd70581-6c77-46f7-a3b2-e9156b91f0d6', '4611754a-ddee-4c25-8086-c6fdc3494be4',
        'test tenant 1 id', 'Mickey', 'Mouse',
        '<EMAIL>', true, '+23-*********', 'ACTIVE', 'a552d3c0-e19b-453b-a17f-ed0de82ee8ec', CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP);
