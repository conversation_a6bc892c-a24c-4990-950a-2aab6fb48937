Import.<PERSON><PERSON><PERSON>,Import.Success,Failure.Reason,Account.ExternalId,Account.Id,Account.Name,Account.LegalName,Account.Description,Account.EntityDisplayIds,Account.CrmId,Account.ErpId,Account.Phone,Account.AddressLine1,Account.AddressLine2,Account.City,Account.State,Account.Zip,Account.Country,Account.Currency,Account.<PERSON><PERSON><PERSON><PERSON>,Contact.ExternalId,Contact.Id,Contact.ErpId,Contact.FirstName,Contact.LastName,Contact.Email,Contact.Phone,Contact.AddressLine1,Contact.AddressLine2,Contact.City,Contact.State,Contact.Zip,Contact.Country,Contact.StrictAddressValidation
2,N,The tenant does not have a completed salesforce integration,123,,Disco,,Disco Never Died,,0011U00000TFV7MQAX,,************,,,,,,,USD,N,,,,,,,,,,,,,,
3,N,Salesforce account: 0011U00000TFV7MQAX does not exist,123,,Disco,,<PERSON> Never Died,,0011U00000TFV7MQAX,,************,,,,,,,USD,N,,,,,,,,,,,,,,
4,Y,,EXTR-123,ACCT-123,Disco,,Disco Never Died,,0011U00000TFV7MQAX,,************,,,,,,,USD,N,,,,,,,,,,,,,,
