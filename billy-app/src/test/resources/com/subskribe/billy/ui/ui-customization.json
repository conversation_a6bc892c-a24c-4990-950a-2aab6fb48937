{"version": "V1_TENANT_LEVEL", "customizations": [{"id": "order::new::newOrderPage", "configType": "state", "defaultValueSetters": [{"path": "orderDetail.autoRenew", "value": false}]}, {"id": "order::new::billingCycleStartDate", "configType": "form", "hidden": true, "backend": {"id": "newOrder", "conditions": [{"key": "billingCycleStartDate", "checks": ["empty"]}]}}, {"id": "order::new::sfdcOpportunityId", "configType": "form", "required": false, "backend": {"id": "newOrder", "conditions": [{"key": "sfdcOpportunityId", "checks": ["empty"]}]}}, {"id": "order::new::addPlan", "configType": "dgpTable", "columnOrdering": ["product_name", "name", "product_category", "description", "id", "measurement"], "columnDefaultInvisible": ["id", "description"]}, {"id": "order::amendment::addPlan", "configType": "dgpTable", "columnOrdering": ["product_name", "name", "product_category", "description", "id", "measurement"], "columnDefaultInvisible": ["id", "description"]}]}