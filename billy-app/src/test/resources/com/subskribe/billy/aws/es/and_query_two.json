{"_source": ["subscription_id", "orders", "account_name"], "from": 0, "query": {"bool": {"filter": [{"match": {"table_name": {"query": "subscription"}}}], "minimum_should_match": 1, "must": [], "must_not": [], "should": [{"bool": {"must": [{"wildcard": {"account_name": {"case_insensitive": true, "value": "*Acme*"}}}, {"wildcard": {"account_name": {"case_insensitive": true, "value": "*Inc*"}}}, {"wildcard": {"state": {"case_insensitive": true, "value": "*ACTIVE*"}}}]}}]}}, "size": 10, "sort": [{"updated_on": "desc"}]}