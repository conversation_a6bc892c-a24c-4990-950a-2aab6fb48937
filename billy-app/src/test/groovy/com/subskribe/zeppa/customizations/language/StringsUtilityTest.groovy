package com.subskribe.zeppa.customizations.language

import groovy.test.GroovyTestCase
import org.assertj.core.api.Assertions

class StringsUtilityTest extends GroovyTestCase {

    void testStringBlankiness() {
        Assertions.assertThat(Strings.isBlank(null)).isTrue()
        Assertions.assertThat(Strings.isBlank("")).isTrue()
        Assertions.assertThat(Strings.isBlank("     ")).isTrue()
        Assertions.assertThat(Strings.isBlank("   hello  ")).isFalse()
    }
}
