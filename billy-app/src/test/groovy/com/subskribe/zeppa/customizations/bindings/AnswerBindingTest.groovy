package com.subskribe.zeppa.customizations.bindings

import com.subskribe.billy.order.quotebuilder.model.ImmutableAnswer
import groovy.test.GroovyTestCase
import java.time.ZonedDateTime
import org.apache.commons.lang3.StringUtils
import org.assertj.core.api.Assertions

class AnswerBindingTest extends GroovyTestCase {

    private static final String TEST_QUESTION_ID = "simpleQuestion"

    private static final List<String> TEST_ANSWERS = ["A", "B", "C"]

    private static final TimeZone TEST_TIME_ZONE = TimeZone.getTimeZone("America/Los_Angeles")

    void testEmptyAnswerBindingWorksAsExpected() {
        AnswerBinding emptyAnswerBinding = AnswerBinding.empty()
        Assertions.assertThat(emptyAnswerBinding.matches("A", "B")).isFalse()
        Assertions.assertThat(emptyAnswerBinding.matches("A")).isFalse()
        Assertions.assertThat(emptyAnswerBinding.matchesOneOf("A", "B", "C")).isFalse()

        Assertions.assertThat(emptyAnswerBinding.matchesExactly("A")).isFalse()
        Assertions.assertThat(emptyAnswerBinding.matchesExactly("A", "B")).isFalse()

        Assertions.assertThat(emptyAnswerBinding.answerAsNumber()).isEqualTo(0)
        Assertions.assertThat(emptyAnswerBinding.answerAsNumber(10)).isEqualTo(10)

        Assertions.assertThat(emptyAnswerBinding.whenMatchesOrElse("A", {
            10
        }, {
            5
        })).isEqualTo(5)
        Assertions.assertThat(emptyAnswerBinding.whenMatchesOrElse("A", {
            10
        }, {
            "foo"
        })).isEqualTo("foo")
        Assertions.assertThat(emptyAnswerBinding.whenMatchesOrElse("A", {
            10
        })).isExactlyInstanceOf(Object.class)

        Assertions.assertThat(emptyAnswerBinding.transform(answer -> {
            return answer.size()
        })).isEqualTo(0)

        Assertions.assertThat(emptyAnswerBinding.transform(answer -> {
            return "foo"
        })).isEqualTo("foo")

        Assertions.assertThat(emptyAnswerBinding.transform(answer -> {
            return null
        })).isExactlyInstanceOf(Object.class)

        Assertions.assertThat(emptyAnswerBinding.answerAsString()).isEmpty()
        Assertions.assertThat(emptyAnswerBinding.answerAsString("foo")).isEqualTo("foo")

        Assertions.assertThat(emptyAnswerBinding.answerAsStringList()).isEmpty()
    }

    void testNonEmptyAnswerMatchesWorksAsExpected() {
        AnswerBinding testAnswerBinding = simpleTestBinding()
        Assertions.assertThat(testAnswerBinding.matches("A")).isTrue()
        Assertions.assertThat(testAnswerBinding.matches("A", "B")).isTrue()
        Assertions.assertThat(testAnswerBinding.matches("A", "B", "C")).isTrue()
        Assertions.assertThat(testAnswerBinding.matches("B", "A", "C")).isTrue()
        Assertions.assertThat(testAnswerBinding.matches("A", "A", "C")).isTrue()
        Assertions.assertThat(testAnswerBinding.matches("C", "D")).isFalse()
        Assertions.assertThat(testAnswerBinding.matches()).isFalse()
        Assertions.assertThat(testAnswerBinding.matches(null)).isFalse()

        // matches exactly
        Assertions.assertThat(testAnswerBinding.matchesExactly("A")).isFalse()
        Assertions.assertThat(testAnswerBinding.matchesExactly("A", "B")).isFalse()
        Assertions.assertThat(testAnswerBinding.matchesExactly("A", "B", "C")).isTrue()
        Assertions.assertThat(testAnswerBinding.matchesExactly("A", "C", "B")).isTrue()
        Assertions.assertThat(testAnswerBinding.matchesExactly("B", "A", "C")).isTrue()
        Assertions.assertThat(testAnswerBinding.matchesExactly("B", "A", "X")).isFalse()

        Assertions.assertThat(testAnswerBinding.whenMatchesOrElse("A", {
            20
        }, {
            10
        })).isEqualTo(20)
        Assertions.assertThat(testAnswerBinding.whenMatchesOrElse("A", {
            "This A"
        }, {
            10
        })).isEqualTo("This A")
        Assertions.assertThat(testAnswerBinding.whenMatchesOrElse("A", null, {
            10
        })).isExactlyInstanceOf(Object.class)
        Assertions.assertThat(testAnswerBinding.whenMatchesOrElse("M", null, {
            10
        })).isEqualTo(10)
        Assertions.assertThat(testAnswerBinding.whenMatchesOrElse("", null, {
            10
        })).isEqualTo(10)

        Assertions.assertThat(testAnswerBinding.matchesOneOf("A", "P", "p", "Q")).isTrue()
        Assertions.assertThat(testAnswerBinding.matchesOneOf("X")).isFalse()
        Assertions.assertThat(testAnswerBinding.matchesOneOf("X", "Y", "Z")).isFalse()
        Assertions.assertThat(testAnswerBinding.matchesOneOf("A", "B")).isTrue()
        Assertions.assertThat(testAnswerBinding.matchesOneOf()).isFalse()
        Assertions.assertThat(testAnswerBinding.matchesOneOf(null)).isFalse()

        Assertions.assertThat(testAnswerBinding.answerAsNumber()).isEqualTo(0)
        Assertions.assertThat(testAnswerBinding.answerAsNumber(10)).isEqualTo(10)
        Assertions.assertThat(testAnswerBinding.answerAsNumber(1.5)).isEqualTo(1.5)

        Assertions.assertThat(testAnswerBinding.transform(answer -> {
            if (answer.contains("A")) {
                return "has A"
            }
            return "no A"
        })).isEqualTo("has A")

        Assertions.assertThat(testAnswerBinding.transform(answer -> {
            if (answer == ["A", "B", "C"]) {
                return "has all"
            }
            return "no some or none"
        })).isEqualTo("has all")

        Assertions.assertThat(testAnswerBinding.transform(answer -> {
            if (answer.contains("A")) {
                return null
            }
            return "no A"
        })).isExactlyInstanceOf(Object.class)

        Assertions.assertThat(testAnswerBinding.transform(answer -> {
            if (answer.contains("X")) {
                return "has X"
            }
            return "no X"
        })).isEqualTo("no X")

        Assertions.assertThat(testAnswerBinding.answerAsString("foo")).isEqualTo("foo")
    }

    void testNumericAnswerWorksAsExpected() {
        AnswerBinding validNumericAnswer = simpleTestBinding(["10"])
        Assertions.assertThat(validNumericAnswer.answerAsNumber()).isInstanceOf(Integer.class)
        Assertions.assertThat(validNumericAnswer.answerAsNumber()).isEqualTo(10)
        Assertions.assertThat(validNumericAnswer.answerAsNumber(50)).isEqualTo(10)

        validNumericAnswer = simpleTestBinding(["10.22"])
        Assertions.assertThat(validNumericAnswer.answerAsNumber()).isInstanceOf(BigDecimal.class)
        Assertions.assertThat(validNumericAnswer.answerAsNumber()).isEqualTo(10.22)
        Assertions.assertThat(validNumericAnswer.answerAsNumber(50)).isEqualTo(10.22)

        validNumericAnswer = simpleTestBinding(["-1.5"])
        Assertions.assertThat(validNumericAnswer.answerAsNumber()).isInstanceOf(BigDecimal.class)
        Assertions.assertThat(validNumericAnswer.answerAsNumber()).isEqualTo(-1.5)
        Assertions.assertThat(validNumericAnswer.answerAsNumber(50)).isEqualTo(-1.5)

        validNumericAnswer = simpleTestBinding(["9223372036854775807"])
        Assertions.assertThat(validNumericAnswer.answerAsNumber()).isInstanceOf(Long.class)
        Assertions.assertThat(validNumericAnswer.answerAsNumber()).isEqualTo(9223372036854775807)
        Assertions.assertThat(validNumericAnswer.answerAsNumber(50)).isEqualTo(9223372036854775807)

        AnswerBinding invalidNumericAnswer = simpleTestBinding([])
        Assertions.assertThat(invalidNumericAnswer.answerAsNumber()).isEqualTo(0)
        Assertions.assertThat(invalidNumericAnswer.answerAsString()).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(invalidNumericAnswer.answerAsStringList()).isEqualTo([])

        invalidNumericAnswer = simpleTestBinding([""])
        Assertions.assertThat(invalidNumericAnswer.answerAsNumber()).isEqualTo(0)

        invalidNumericAnswer = simpleTestBinding(["10", "20"])
        Assertions.assertThat(invalidNumericAnswer.answerAsNumber()).isEqualTo(0)

        Assertions.assertThat(validNumericAnswer.answerAsString("foo")).isEqualTo("9223372036854775807")
        Assertions.assertThat(validNumericAnswer.answerAsStringList()).isEqualTo(["9223372036854775807"])

        Assertions.assertThat(invalidNumericAnswer.answerAsString("foo")).isEqualTo("foo")
        Assertions.assertThat(invalidNumericAnswer.answerAsStringList()).isEqualTo(["10", "20"])
    }

    void testDateAnswerWorksAsExpected() {
        ZonedDateTime testDate = ZonedDateTime.now(TEST_TIME_ZONE.toZoneId())
        AnswerBinding validDate = simpleTestBinding([
            String.valueOf(testDate.toEpochSecond())
        ])
        Assertions.assertThat(validDate.asDate().isPresent).isTrue()
        Assertions.assertThat(validDate.asDate().isAbsent).isFalse()
        Assertions.assertThat(validDate.asDate().dayOfMonth).isEqualTo(testDate.dayOfMonth)
        Assertions.assertThat(validDate.asDate().monthOfYear).isEqualTo(testDate.monthValue)
        Assertions.assertThat(validDate.asDate().year).isEqualTo(testDate.year)

        // valid dates should work as expected as well
        AnswerBinding dateFormatBinding = simpleTestBinding(["1/1/2025"])
        Assertions.assertThat(dateFormatBinding.asDate().isPresent).isTrue()
        Assertions.assertThat(dateFormatBinding.asDate().isAbsent).isFalse()
        Assertions.assertThat(dateFormatBinding.asDate().dayOfMonth).isEqualTo(1)
        Assertions.assertThat(dateFormatBinding.asDate().monthOfYear).isEqualTo(1)
        Assertions.assertThat(dateFormatBinding.asDate().year).isEqualTo(2025)

        dateFormatBinding = simpleTestBinding(["1/1/25"])
        Assertions.assertThat(dateFormatBinding.asDate().isPresent).isTrue()
        Assertions.assertThat(dateFormatBinding.asDate().isAbsent).isFalse()
        Assertions.assertThat(dateFormatBinding.asDate().dayOfMonth).isEqualTo(1)
        Assertions.assertThat(dateFormatBinding.asDate().monthOfYear).isEqualTo(1)
        Assertions.assertThat(dateFormatBinding.asDate().year).isEqualTo(2025)

        AnswerBinding invalidDate = simpleTestBinding([
            String.valueOf(testDate.toEpochSecond()),
            String.valueOf(testDate.toEpochSecond())
        ])
        Assertions.assertThat(invalidDate.asDate().isPresent).isFalse()

        invalidDate = simpleTestBinding(['a'])
        Assertions.assertThat(invalidDate.asDate().isPresent).isFalse()

        invalidDate = simpleTestBinding([""])
        Assertions.assertThat(invalidDate.asDate().isPresent).isFalse()

        invalidDate = simpleTestBinding(["     "])
        Assertions.assertThat(invalidDate.asDate().isPresent).isFalse()

        invalidDate = simpleTestBinding(["-1000"])
        Assertions.assertThat(invalidDate.asDate().isPresent).isFalse()

        invalidDate = AnswerBinding.empty()
        Assertions.assertThat(invalidDate.asDate().isPresent).isFalse()

        invalidDate = simpleTestBinding(["1//2025"])
        Assertions.assertThat(invalidDate.asDate().isPresent).isFalse()

        invalidDate = simpleTestBinding(["//2025"])
        Assertions.assertThat(invalidDate.asDate().isPresent).isFalse()

        invalidDate = simpleTestBinding(["30/1/2025"])
        Assertions.assertThat(invalidDate.asDate().isPresent).isFalse()
    }

    static AnswerBinding simpleTestBinding(List<String> answers = TEST_ANSWERS) {
        AnswerBinding.fromAnswer(ImmutableAnswer.builder()
        .questionId(TEST_QUESTION_ID).answer(answers).skipped(false).build(), TEST_TIME_ZONE)
    }
}
