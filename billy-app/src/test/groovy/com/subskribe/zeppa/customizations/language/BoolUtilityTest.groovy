package com.subskribe.zeppa.customizations.language

import groovy.test.GroovyTestCase
import org.assertj.core.api.Assertions

class BoolUtilityTest extends GroovyTestCase {

    void testNullAndEmptyConditionsWorkAsExpected() {
        Assertions.assertThat(Bool.orGroup()).isFalse()
        Assertions.assertThat(Bool.andGroup()).isFalse()

        Assertions.assertThat(Bool.orGroup(null)).isFalse()
        Assertions.assertThat(Bool.andGroup(null)).isFalse()

        Assertions.assertThat(Bool.orGroup(2 > 1, null)).isTrue()
        Assertions.assertThat(Bool.andGroup(2 > 1, null)).isFalse()
    }

    void testMultiConditionWorksAsExpected() {
        Assertions.assertThat(Bool.orGroup(1 > 2, 2 > 3, 4 > 5, 1==1)).isTrue()
        Assertions.assertThat(Bool.andGroup(1 > 2, 2 > 3, 4 > 5, 1==1)).isFalse()
    }
}
