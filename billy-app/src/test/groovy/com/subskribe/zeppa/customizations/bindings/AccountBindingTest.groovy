package com.subskribe.zeppa.customizations.bindings

import com.subskribe.billy.account.model.Account
import com.subskribe.billy.account.model.AccountAddress
import com.subskribe.billy.customfield.model.CustomField
import com.subskribe.billy.customfield.model.CustomFieldSource
import com.subskribe.billy.customfield.model.CustomFieldType
import com.subskribe.billy.customfield.model.CustomFieldValue
import groovy.test.GroovyTestCase
import org.apache.commons.lang3.RandomStringUtils
import org.apache.commons.lang3.StringUtils
import org.assertj.core.api.Assertions


class AccountBindingTest extends GroovyTestCase {

    private static final String ACCOUNT_ID = "ACCT-${RandomStringUtils.randomAlphanumeric(7).toUpperCase()}"

    void testAccountBindingIdIsWorkingAsExpected() {
        Account account = new Account()
        account.setAccountId(ACCOUNT_ID)

        AccountBinding accountBinding = AccountBinding.from(account, null, null)
        Assertions.assertThat(accountBinding.id).isEqualTo(ACCOUNT_ID)
        Assertions.assertThat(accountBinding.isReseller).isFalse()

        // now se the value to true and it should reflect in the binding
        account.setIsReseller(true)
        Assertions.assertThat(accountBinding.isReseller).isTrue()


        accountBinding = AccountBinding.empty()
        Assertions.assertThat(accountBinding.id).isEqualTo(StringUtils.EMPTY)

        // when there is nothing int he binding the reseller should be false
        Assertions.assertThat(accountBinding.isReseller).isFalse()
    }

    void testAccountAddressBindingIsWorkingAsExpected() {
        AccountAddress accountAddress = new AccountAddress()
        AccountBinding accountBinding = AccountBinding.from(null, accountAddress, null)

        accountAddress.setCountry("UK")
        Assertions.assertThat(accountBinding.country).isEqualTo("UK")

        accountAddress.setCountry(null)
        Assertions.assertThat(accountBinding.country).isEqualTo(StringUtils.EMPTY)

        accountBinding = AccountBinding.empty()
        Assertions.assertThat(accountBinding.country).isEqualTo(StringUtils.EMPTY)
    }

    void testAccountCustomFieldsBindingIsWorkingAsExpected() {
        CustomField emptyValuesCustomField = new CustomField(Map.of())

        AccountBinding accountBinding = AccountBinding.from(null, null, emptyValuesCustomField)
        Assertions.assertThat(accountBinding.customField("foo")).isEqualTo(StringUtils.EMPTY)

        CustomField nonEmptyMissingValueCustomField = new CustomField(Map.of(
                "foo-bar",
                new CustomFieldValue(CustomFieldType.STRING, "foo-bar", "Foo Bar", "foo-baz", List.of(), List.of(), false, CustomFieldSource.USER, null),
                "donkey-do",
                new CustomFieldValue(CustomFieldType.STRING, "donkey-do", "Donkey Doo", null, List.of("donkey", "do"), List.of(), false, CustomFieldSource.USER, null)))


        accountBinding = AccountBinding.from(null, null, nonEmptyMissingValueCustomField)
        Assertions.assertThat(accountBinding.customField("foo")).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(accountBinding.customField("foo-bar")).isEqualTo("foo-baz")
        Assertions.assertThat(accountBinding.customFieldMatches("foo-bar", "foo-baz")).isTrue()
        Assertions.assertThat(accountBinding.customFieldMatches("foo-bar", "foo")).isFalse()
        Assertions.assertThat(accountBinding.customField("donkey-do")).isEqualTo("donkey")
        Assertions.assertThat(accountBinding.customFieldMatches("donkey-do", "do")).isTrue()
        Assertions.assertThat(accountBinding.customFieldMatches("donkey-do", "donkey")).isTrue()
        Assertions.assertThat(accountBinding.customFieldMatches("donkey-do", "foo")).isFalse()

        accountBinding = AccountBinding.empty()
        Assertions.assertThat(accountBinding.customField("foo")).isEqualTo(StringUtils.EMPTY)
    }
}
