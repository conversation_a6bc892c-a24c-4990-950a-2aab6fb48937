package com.subskribe.zeppa.customizations.bindings

import com.subskribe.billy.user.model.Role
import com.subskribe.billy.user.model.User
import groovy.test.GroovyTestCase
import org.apache.commons.lang3.StringUtils
import org.assertj.core.api.Assertions
import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils

class UserBindingTest extends GroovyTestCase {

    void testIfEmptyUserBindingWorksAsExpected() {
        UserBinding userBinding = UserBinding.fromUser(null)

        Assertions.assertThat(userBinding.id).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(userBinding.email).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(userBinding.role).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(userBinding.isAdmin).isFalse()
        Assertions.assertThat(userBinding.isNotAdmin).isTrue()
    }

    void testIfNormalUserBindingWorksAsExpected() {
        User testUser = getTestUser(Role.ADMIN)
        UserBinding userBinding = UserBinding.fromUser(testUser)
        Assertions.assertThat(userBinding.id).isEqualTo(testUser.userId)
        Assertions.assertThat(userBinding.email).isEqualTo(testUser.email)
        Assertions.assertThat(userBinding.role).isEqualTo(testUser.role.name())
        Assertions.assertThat(userBinding.isAdmin).isTrue()
        Assertions.assertThat(userBinding.isNotAdmin).isFalse()
    }

    static User getTestUser(Role role) {
        User user = new User()
        user.userId = "USER-${RandomStringUtils.randomAlphanumeric(7).toUpperCase()}"
        user.email = "test-${RandomStringUtils.randomAlphanumeric(4)}@awesome.com"
        user.role = role
        user
    }
}
