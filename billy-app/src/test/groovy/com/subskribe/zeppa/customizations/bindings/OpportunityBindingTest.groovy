package com.subskribe.zeppa.customizations.bindings

import com.subskribe.billy.customfield.model.CustomField
import com.subskribe.billy.customfield.model.CustomFieldSource
import com.subskribe.billy.customfield.model.CustomFieldType
import com.subskribe.billy.customfield.model.CustomFieldValue
import com.subskribe.billy.opportunity.model.Opportunity
import com.subskribe.billy.opportunity.model.OpportunityCrmType
import groovy.test.GroovyTestCase
import org.apache.commons.lang3.StringUtils
import org.assertj.core.api.Assertions

class OpportunityBindingTest extends GroovyTestCase {

    void testNullSafetyOfOpportunityBinding() {
        OpportunityBinding nullSourceBinding = OpportunityBinding.from(null)
        verifyBlankOppBinding(nullSourceBinding)

        OpportunityBinding emptyBinding = OpportunityBinding.empty()
        verifyBlankOppBinding(emptyBinding)
    }

    void testValidOpportunityBindingWithData() {
        Opportunity opportunity = makeOpportunity()
        OpportunityBinding opportunityBinding = OpportunityBinding.from(opportunity)

        Assertions.assertThat(opportunityBinding.name).isEqualTo(opportunity.name)
        Assertions.assertThat(opportunityBinding.type).isEqualTo(opportunity.type)
        Assertions.assertThat(opportunityBinding.stage).isEqualTo(opportunity.stage)
        Assertions.assertThat(opportunityBinding.crmType).isEqualTo(opportunity.opportunityCrmType.name())

        Assertions.assertThat(opportunityBinding.customField("foo-bar")).isEqualTo("foo-baz")
        Assertions.assertThat(opportunityBinding.customField("donkey-do")).isEqualTo("donkey")

        Assertions.assertThat(opportunityBinding.customFieldMatches("foo-bar", "foo-baz")).isTrue()
        Assertions.assertThat(opportunityBinding.customFieldMatches("donkey-do", "do")).isTrue()
    }

    private static verifyBlankOppBinding(OpportunityBinding opportunityBinding) {
        Assertions.assertThat(opportunityBinding.name).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(opportunityBinding.type).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(opportunityBinding.stage).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(opportunityBinding.crmType).isEqualTo(StringUtils.EMPTY)

        Assertions.assertThat(opportunityBinding.customField("Foo")).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(opportunityBinding.customFieldMatches("Foo", "Bar")).isFalse()
    }

    static final Opportunity makeOpportunity() {
        Opportunity testOpp = new Opportunity()
        testOpp.setName("Test")
        testOpp.setType("TestType")
        testOpp.setStage("TestStage")
        testOpp.setOpportunityCrmType(OpportunityCrmType.HUBSPOT)


        CustomField testCustomFields = new CustomField(Map.of(
                "foo-bar",
                new CustomFieldValue(CustomFieldType.STRING,
                "foo-bar",
                "Foo Bar",
                "foo-baz",
                List.of(),
                List.of(),
                false,
                CustomFieldSource.USER,
                null),
                "donkey-do",
                new CustomFieldValue(CustomFieldType.STRING,
                "donkey-do",
                "Donkey Doo",
                null,
                List.of("donkey", "do"),
                List.of(),
                false,
                CustomFieldSource.USER,
                null)))

        testOpp.setCustomFields(testCustomFields)
        return testOpp
    }
}
