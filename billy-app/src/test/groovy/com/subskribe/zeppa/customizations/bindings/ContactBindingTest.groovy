package com.subskribe.zeppa.customizations.bindings

import com.subskribe.billy.account.model.Account
import com.subskribe.billy.account.model.AccountContact
import groovy.test.GroovyTestCase
import org.assertj.core.api.Assertions
import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils

class ContactBindingTest extends GroovyTestCase {

    public static final String TEST_CONTACT_ID = RandomStringUtils.randomAlphanumeric(10)
    public static final String TEST_ACCOUNT_ID = RandomStringUtils.randomAlphanumeric(10)
    public static final String TEST_PHONE_NUMBER = "+**********"

    void testEmptyContactBindingWorkingAsExpected() {
        ContactBinding binding = ContactBinding.empty()
        Assertions.assertThat(binding.id).isEmpty()
        Assertions.assertThat(binding.accountId).isEmpty()
        Assertions.assertThat(binding.phoneNumber).isEmpty()
        Assertions.assertThat(binding.account).isNotNull()

        Assertions.assertThat(binding.account.id).isEmpty()
        Assertions.assertThat(binding.account.name).isEmpty()
    }

    void testContactBindingWorksAsExpected() {
        AccountContact testContact = getTestContact()
        ContactBinding contactBinding = ContactBinding.from(testContact, getTestAccountBinding())

        Assertions.assertThat(contactBinding.id).isEqualTo(TEST_CONTACT_ID)
        Assertions.assertThat(contactBinding.accountId).isEqualTo(TEST_ACCOUNT_ID)
        Assertions.assertThat(contactBinding.phoneNumber).isEqualTo(TEST_PHONE_NUMBER)
        Assertions.assertThat(contactBinding.account).isNotNull()

        Assertions.assertThat(contactBinding.account.id).isEqualTo(TEST_ACCOUNT_ID)
        Assertions.assertThat(contactBinding.account.name).isEqualTo("test")
    }

    static AccountContact getTestContact() {
        AccountContact accountContact = new AccountContact()
        accountContact.setContactId(TEST_CONTACT_ID)
        accountContact.setAccountId(TEST_ACCOUNT_ID)
        accountContact.setPhoneNumber(TEST_PHONE_NUMBER)
        accountContact
    }

    static AccountBinding getTestAccountBinding() {
        Account account = new Account()
        account.setAccountId(TEST_ACCOUNT_ID)
        account.setName("test")
        AccountBinding.from(account, null, null)
    }
}
