package com.subskribe.zeppa.customizations.bindings

import com.subskribe.billy.productcatalog.model.Charge
import com.subskribe.billy.productcatalog.model.Plan
import com.subskribe.billy.productcatalog.model.PlanStatus
import com.subskribe.billy.productcatalog.model.Product
import com.subskribe.billy.productcatalog.model.ProductCategory
import com.subskribe.billy.shared.enums.ChargeModel
import com.subskribe.billy.shared.enums.ChargeType
import com.subskribe.billy.shared.enums.Cycle
import com.subskribe.billy.shared.temporal.Recurrence
import groovy.test.GroovyTestCase
import java.time.Instant
import org.apache.commons.lang3.RandomStringUtils
import org.apache.commons.lang3.StringUtils
import org.assertj.core.api.Assertions

class CatalogBindingTest extends GroovyTestCase {

    static final String PRODUCT_SAMPLE_SKU = "SK-001"
    static final String PRODUCT_SAMPLE_NAME = "Sample Product"
    static final String PRODUCT_SAMPLE_CATEGORY = "Test Category"

    void testEmptyProductBindingWorksAsExpected() {
        ProductBinding emptyBinding = ProductBinding.from(null)
        Assertions.assertThat(emptyBinding.sku).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(emptyBinding.id).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(emptyBinding.category).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(emptyBinding.name).isEqualTo(StringUtils.EMPTY)
    }

    void testProductBindingWorksAsExpected() {
        String productId = RandomStringUtils.randomAlphanumeric(7)
        Product product = getSampleProduct(productId)
        ProductBinding productBinding = ProductBinding.from(product)
        Assertions.assertThat(productBinding.sku).isEqualTo(PRODUCT_SAMPLE_SKU)
        Assertions.assertThat(productBinding.id).isEqualTo(productId)
        Assertions.assertThat(productBinding.category).isEqualTo(PRODUCT_SAMPLE_CATEGORY)
        Assertions.assertThat(productBinding.name).isEqualTo(PRODUCT_SAMPLE_NAME)
    }

    static Plan getSamplePlan(String planId = RandomStringUtils.randomAlphanumeric(7)) {
        Plan plan = new Plan()
        plan.planId = planId
        plan.name = "foo"
        plan.currency = Currency.getInstance("USD")
        plan.status = PlanStatus.ACTIVE
        return plan
    }

    static Charge getSampleCharge(String chargeId = RandomStringUtils.randomAlphanumeric(7)) {
        Charge charge = new Charge()
        charge.name = "Super Charge"
        charge.description = "A super charge for testing"
        charge.chargeId = chargeId
        charge.type = ChargeType.RECURRING
        charge.recurrence = new Recurrence(Cycle.YEAR, 1)
        charge.chargeModel = ChargeModel.PER_UNIT
        return charge
    }

    static Product getSampleProduct(String productId = RandomStringUtils.randomAlphanumeric(7)) {
        ProductCategory productCategory = new ProductCategory()
        productCategory.name = PRODUCT_SAMPLE_CATEGORY
        Product product = new Product(UUID.randomUUID(),
                Set.of(),
                productId,
                true,
                PRODUCT_SAMPLE_NAME,
                "Sample Name",
                "Sample Name Desc",
                PRODUCT_SAMPLE_SKU,
                productCategory,
                Instant.now(),
                "Test")
        return product
    }
}
