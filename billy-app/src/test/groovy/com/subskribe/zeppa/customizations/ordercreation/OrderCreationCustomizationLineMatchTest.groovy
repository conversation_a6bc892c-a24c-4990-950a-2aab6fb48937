package com.subskribe.zeppa.customizations.ordercreation

import static com.subskribe.zeppa.customizations.bindings.CatalogBindingTest.getSampleCharge
import static com.subskribe.zeppa.customizations.bindings.CatalogBindingTest.getSamplePlan
import static com.subskribe.zeppa.customizations.bindings.CatalogBindingTest.getSampleProduct
import static com.subskribe.zeppa.customizations.bindings.OrderBindingTest.getChargeBinding
import static com.subskribe.zeppa.customizations.bindings.OrderBindingTest.getCustomField
import static com.subskribe.zeppa.customizations.bindings.OrderBindingTest.getOrderWithLineItem
import static com.subskribe.zeppa.customizations.bindings.OrderBindingTest.makeOpportunity

import com.google.common.io.Resources
import com.subskribe.billy.exception.InvalidInputException
import com.subskribe.billy.order.model.Order
import com.subskribe.billy.productcatalog.model.Charge
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference
import com.subskribe.billy.shared.enums.ActionType
import com.subskribe.billy.shared.enums.ChargeModel
import com.subskribe.billy.shared.enums.ChargeType
import com.subskribe.billy.shared.enums.OrderType
import com.subskribe.billy.shared.pecuniary.DiscountDetail
import com.subskribe.billy.user.model.Role
import com.subskribe.billy.user.model.User
import com.subskribe.zeppa.customizations.bindings.AccountBinding
import com.subskribe.zeppa.customizations.bindings.OrderBinding
import com.subskribe.zeppa.customizations.bindings.PlanBinding
import com.subskribe.zeppa.customizations.bindings.ProductBinding
import com.subskribe.zeppa.customizations.bindings.UserBinding
import com.subskribe.zeppa.customizations.bindings.UserBindingTest
import com.subskribe.zeppa.customizations.core.CustomizationAction
import com.subskribe.zeppa.customizations.core.RuleAction
import com.subskribe.zeppa.customizations.core.RuleProcessed
import com.subskribe.zeppa.customizations.core.ZeppaDriver
import groovy.test.GroovyTestCase
import java.nio.charset.StandardCharsets
import java.time.Instant
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import org.assertj.core.api.Assertions

class OrderCreationCustomizationLineMatchTest extends GroovyTestCase {

    private static final String SCRIPT_TWO_PATH = "zeppa/script_two.zeppa"
    private static final String SCRIPT_TWO = Resources.toString(Resources.getResource(SCRIPT_TWO_PATH), StandardCharsets.UTF_8)

    private static final String SCRIPT_THREE_PATH = "zeppa/script_three.zeppa"
    private static final String SCRIPT_THREE = Resources.toString(Resources.getResource(SCRIPT_THREE_PATH), StandardCharsets.UTF_8)

    public static final ZoneId PACIFIC_ZONE_ID = ZoneId.of("America/Los_Angeles")

    private static String SCRIPT_BLOCKED_OPERATIONS =
    """
    orderCreationCustomization {
        rule {
            scope ORDER_LINE_ITEM 
            name "Bar unit price is half of Baz"
            condition {
                when order.lineItemsMatching {
                        on lineItem.plan.customFieldMatches("foo", "bar")
                    }.first.listUnitPrice != order.lineItemsMatching {
                        on lineItem.plan.customFieldMatches("foo", "baz")
                    }.first.listUnitPrice / 2
            }
            
            lineItemActions {
                failOrderCreation "Bar unit price is half of Baz"
            }
        }
    }
    """

    void testOrderCreationCustomizationOnOrderNegativeCondition() {
        PlanBinding planBindingOne = PlanBinding.from(
                getSamplePlan(),
                getCustomField(["foo": "bar"]),
                List.of(getChargeBinding(getSampleCharge(), getCustomField(["foo": "bar"]))),
                ProductBinding.from(getSampleProduct())
                )

        Charge oneTimeCharge = getSampleCharge()
        oneTimeCharge.type = ChargeType.ONE_TIME
        oneTimeCharge.chargeModel = ChargeModel.FLAT_FEE

        PlanBinding planBindingTwo = PlanBinding.from(
                getSamplePlan(),
                getCustomField(["foo": "baz"]),
                List.of(getChargeBinding(oneTimeCharge, getCustomField(["foo": "baz"]))),
                ProductBinding.from(getSampleProduct())
                )

        Order order = getOrderWithLineItem(ActionType.ADD, planBindingOne, planBindingTwo)
        order.orderType = OrderType.CANCEL

        order.lineItemsNetEffect[0].action = ActionType.UPDATE

        order.lineItemsNetEffect[1].action = ActionType.REMOVE
        order.lineItemsNetEffect[1].quantity *= -1

        // user
        User testUser = UserBindingTest.getTestUser(Role.SALES)

        OrderBinding orderBinding = OrderBinding.from(
                order,
                AccountBinding.empty(),
                List.of(planBindingOne, planBindingTwo),
                null)

        List<RuleProcessed> rulesProcessed = ZeppaDriver.fireOrderCreationCustomization(orderBinding,
                SCRIPT_THREE, UserBinding.fromUser(testUser))

        Assertions.assertThat(rulesProcessed).hasSize(2)

        List<RuleProcessed> rulesFired = rulesProcessed.findAll {
            it.fired
        }
        Assertions.assertThat(rulesFired).hasSize(2)

        List<RuleProcessed> lineActionsRuleProcessed = rulesFired.findAll {
            it.ruleName == "line actions for specific line actions"
        }

        Assertions.assertThat(lineActionsRuleProcessed).hasSize(1)

        Assertions.assertThat(lineActionsRuleProcessed[0].fired).isTrue()

        // only one action because of UPDATE line item
        Assertions.assertThat(lineActionsRuleProcessed[0].actions).hasSize(1)
        Assertions.assertThat(lineActionsRuleProcessed[0].actions[0].metadata).isInstanceOf(LineItemQuantity.class)
        LineItemQuantity metadata = lineActionsRuleProcessed[0].actions[0].metadata as LineItemQuantity
        Assertions.assertThat(metadata.lineItemId).isEqualTo(order.lineItemsNetEffect[0].id.toString())
        Assertions.assertThat(metadata.quantity).isEqualTo(10)
    }

    void testOrderCreationCustomizationOnOrderLineItemMatch() {
        PlanBinding planBindingOne = PlanBinding.from(
                getSamplePlan(),
                getCustomField(["foo": "bar"]),
                List.of(getChargeBinding(getSampleCharge(), getCustomField(["foo": "bar"]))),
                ProductBinding.from(getSampleProduct())
                )

        Charge oneTimeCharge = getSampleCharge()
        oneTimeCharge.type = ChargeType.ONE_TIME
        oneTimeCharge.chargeModel = ChargeModel.FLAT_FEE

        PlanBinding planBindingTwo = PlanBinding.from(
                getSamplePlan(),
                getCustomField(["foo": "baz"]),
                List.of(getChargeBinding(oneTimeCharge, getCustomField(["foo": "baz"]))),
                ProductBinding.from(getSampleProduct())
                )

        Order order = getOrderWithLineItem(ActionType.ADD, planBindingOne, planBindingTwo)
        order.orderType = OrderType.NEW

        // set opportunity
        order.setOpportunity(makeOpportunity())

        Instant startDate = Instant.now()
        Instant endDate = Instant.now().plus(365, ChronoUnit.DAYS)

        order.startDate = startDate
        order.endDate = endDate

        // now set quantity
        order.lineItemsNetEffect[0].quantity = 10
        order.lineItemsNetEffect[1].quantity = 15

        Instant lineOneEnd = startDate.plus(180, ChronoUnit.DAYS)
        order.lineItemsNetEffect[0].effectiveDate = startDate
        order.lineItemsNetEffect[0].endDate = lineOneEnd

        order.lineItemsNetEffect[1].effectiveDate = lineOneEnd
        order.lineItemsNetEffect[1].endDate = endDate

        // now set attribute references
        order.lineItemsNetEffect[0].attributeReferences =
                List.of(new AttributeReference("x", "1"),
                new AttributeReference("y", "2"))
        order.lineItemsNetEffect[1].attributeReferences =
                List.of(new AttributeReference("y", "2"),
                new AttributeReference("x", "1"))

        // set discount
        DiscountDetail discount = new DiscountDetail()
        discount.setPercent(new BigDecimal("0.1"))
        order.lineItemsNetEffect[0].discounts = [discount]

        // set custom list unit price
        order.lineItemsNetEffect[0].listUnitPrice = new BigDecimal("10.00")
        order.lineItemsNetEffect[1].listUnitPrice = new BigDecimal("15.00")

        // user
        User testUser = UserBindingTest.getTestUser(Role.SALES)

        OrderBinding orderBinding = OrderBinding.from(
                order,
                AccountBinding.from(null, null, null),
                List.of(planBindingOne, planBindingTwo),
                PACIFIC_ZONE_ID)

        List<RuleProcessed> rulesProcessed = ZeppaDriver.fireOrderCreationCustomization(orderBinding, SCRIPT_TWO, UserBinding.fromUser(testUser))

        Assertions.assertThat(rulesProcessed).hasSize(16)

        List<RuleProcessed> rulesFired = rulesProcessed.findAll {
            it.fired
        }
        Assertions.assertThat(rulesFired).hasSize(10)

        verifySumOfQuantitySettingCustomField(rulesFired)

        verifySumOfQuantityDateNotMatchSettingCustomField(rulesFired)

        verifySeveralOrderActionsBasedOnLines(rulesFired)

        verifyQuantitySumLessThanDefined(rulesFired)

        verifyLineItemDiscount(rulesFired)

        verifyStringExpressionMapping(rulesFired)

        verifyBarAndBazHave1Line(rulesFired)

        verifyBarAndBazHaveSameCustomListUnitPrice(rulesFired)

        verifyAttributeReferencesRule(rulesFired)

        verifyOrderOpportunityRule(rulesFired)
    }

    void testUsingBlockedOperationsNotAllowed() {
        PlanBinding planBinding = PlanBinding.from(
                getSamplePlan(),
                getCustomField(["foo": "bar"]),
                List.of(getChargeBinding(getSampleCharge(), getCustomField(["foo": "bar"]))),
                ProductBinding.from(getSampleProduct())
                )

        Order order = getOrderWithLineItem(ActionType.ADD, planBinding)
        order.orderType = OrderType.NEW

        // user
        User testUser = UserBindingTest.getTestUser(Role.SALES)

        OrderBinding orderBinding = OrderBinding.from(
                order,
                AccountBinding.from(null, null, null),
                List.of(planBinding),
                null)

        Assertions.assertThatThrownBy {
            ZeppaDriver.fireOrderCreationCustomization(orderBinding, SCRIPT_BLOCKED_OPERATIONS, UserBinding.fromUser(testUser))
        }.isInstanceOf(InvalidInputException.class)
        .hasMessageContaining("is not allowed")
    }

    private static void verifySeveralOrderActionsBasedOnLines(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> addLineRuleProcessed = rulesFired.findAll {
            it.ruleName == "Several Order Actions Based On Line Items"
        }

        Assertions.assertThat(addLineRuleProcessed).hasSize(1)
        Assertions.assertThat(addLineRuleProcessed.get(0).actions).hasSize(8)

        RuleAction addTerm = addLineRuleProcessed.get(0).actions.find { action ->
            action.action == CustomizationAction.ADD_PREDEFINED_TERM
        }
        RuleAction addDiscount = addLineRuleProcessed.get(0).actions.find { action ->
            action.action == CustomizationAction.ADD_PREDEFINED_DISCOUNT
        }

        RuleAction forcePaymentDue = addLineRuleProcessed.get(0).actions.find { action ->
            action.action == CustomizationAction.FORCE_PAYMENT_TERM
        }

        RuleAction forceBillingCycle = addLineRuleProcessed.get(0).actions.find { action ->
            action.action == CustomizationAction.FORCE_BILLING_CYCLE
        }

        Assertions.assertThat(addTerm).isNotNull()
        Assertions.assertThat(addDiscount).isNotNull()
        Assertions.assertThat(forcePaymentDue).isNotNull()
        Assertions.assertThat(forceBillingCycle).isNotNull()

        Assertions.assertThat(addTerm.metadata).isInstanceOf(String.class)
        Assertions.assertThat(addTerm.metadata as String).isEqualTo("Add Terms")

        Assertions.assertThat(addDiscount.metadata).isInstanceOf(String.class)
        Assertions.assertThat(addDiscount.metadata as String).isEqualTo("Add Discount")

        Assertions.assertThat(forcePaymentDue.metadata).isInstanceOf(Integer.class)
        Assertions.assertThat(forcePaymentDue.metadata as Integer).isEqualTo(60)

        List<RuleAction> forceCustomFieldActions = addLineRuleProcessed.get(0).actions.findAll { action ->
            action.action == CustomizationAction.FORCE_ORDER_CUSTOM_FIELD
        }

        Assertions.assertThat(forceCustomFieldActions.size()).isEqualTo(2)
        RuleAction actionOne = forceCustomFieldActions[0]
        Assertions.assertThat(actionOne.action).isEqualTo(CustomizationAction.FORCE_ORDER_CUSTOM_FIELD)
        Assertions.assertThat(actionOne.metadata).isInstanceOf(CustomFieldValue.class)
        Assertions.assertThat((actionOne.metadata as CustomFieldValue).identifier).isEqualTo("foo")
        Assertions.assertThat((actionOne.metadata as CustomFieldValue).values).isEqualTo(List.of("bar"))

        RuleAction actionTwo = forceCustomFieldActions[1]
        Assertions.assertThat(actionTwo.action).isEqualTo(CustomizationAction.FORCE_ORDER_CUSTOM_FIELD)
        Assertions.assertThat(actionTwo.metadata).isInstanceOf(CustomFieldValue.class)
        Assertions.assertThat((actionTwo.metadata as CustomFieldValue).identifier).isEqualTo("bar")
        Assertions.assertThat((actionTwo.metadata as CustomFieldValue).values).isEqualTo(List.of("baz", "NEW"))

        List<RuleAction> applyCustomFieldActions = addLineRuleProcessed.get(0).actions.findAll { action ->
            action.action == CustomizationAction.APPLY_ORDER_CUSTOM_FIELD
        }
        Assertions.assertThat(applyCustomFieldActions.size()).isEqualTo(2)
        actionOne = applyCustomFieldActions[0]
        Assertions.assertThat(actionOne.action).isEqualTo(CustomizationAction.APPLY_ORDER_CUSTOM_FIELD)
        Assertions.assertThat(actionOne.metadata).isInstanceOf(CustomFieldValue.class)
        Assertions.assertThat((actionOne.metadata as CustomFieldValue).identifier).isEqualTo("foo")
        Assertions.assertThat((actionOne.metadata as CustomFieldValue).values).isEqualTo(List.of("bar"))

        actionTwo = applyCustomFieldActions[1]
        Assertions.assertThat(actionTwo.action).isEqualTo(CustomizationAction.APPLY_ORDER_CUSTOM_FIELD)
        Assertions.assertThat(actionTwo.metadata).isInstanceOf(CustomFieldValue.class)
        Assertions.assertThat((actionTwo.metadata as CustomFieldValue).identifier).isEqualTo("bar")
        Assertions.assertThat((actionTwo.metadata as CustomFieldValue).values).isEqualTo(List.of("baz", "donkey"))
    }

    private static void verifyQuantitySumLessThanDefined(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> addLineRuleProcessed = rulesFired.findAll {
            it.ruleName == "Bar and Baz quantity should be > 50"
        }

        Assertions.assertThat(addLineRuleProcessed).hasSize(1)
        Assertions.assertThat(addLineRuleProcessed.get(0).actions).hasSize(1)

        Assertions.assertThat(addLineRuleProcessed[0].actions[0].action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata).isInstanceOf(String.class)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata as String).contains("quantity should be > 50")
    }

    private static void verifyLineItemDiscount(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> addLineRuleProcessed = rulesFired.findAll {
            it.ruleName == "Any line has discount"
        }

        Assertions.assertThat(addLineRuleProcessed).hasSize(1)
        Assertions.assertThat(addLineRuleProcessed.get(0).actions).hasSize(1)

        Assertions.assertThat(addLineRuleProcessed[0].actions[0].action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata).isInstanceOf(String.class)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata as String).contains("Order lines cannot have discounts")
    }

    private static void verifyStringExpressionMapping(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> stringExpressionMappingRule = rulesFired.findAll {
            it.ruleName == "String expression with mapping"
        }

        Assertions.assertThat(stringExpressionMappingRule).hasSize(1)
        Assertions.assertThat(stringExpressionMappingRule.get(0).actions).hasSize(2)

        Assertions.assertThat(stringExpressionMappingRule[0].actions[0].action).isEqualTo(CustomizationAction.FORCE_LINE_ITEM_PRICE_ATTRIBUTE)
        Assertions.assertThat(stringExpressionMappingRule[0].actions[0].metadata).isInstanceOf(LineItemPriceAttribute.class)
        LineItemPriceAttribute attributeData = stringExpressionMappingRule[0].actions[0].metadata as LineItemPriceAttribute
        Assertions.assertThat(attributeData.attributeMap).hasSize(1)
        Assertions.assertThat(attributeData.attributeMap).containsKey("Hello")
        Assertions.assertThat(attributeData.attributeMap["Hello"]).isEqualTo("World")


        Assertions.assertThat(stringExpressionMappingRule[0].actions[1].action).isEqualTo(CustomizationAction.FORCE_LINE_ITEM_QUANTITY)
        Assertions.assertThat(stringExpressionMappingRule[0].actions[1].metadata).isInstanceOf(LineItemQuantity.class)
        LineItemQuantity lineItemQuantity = stringExpressionMappingRule[0].actions[1].metadata as LineItemQuantity
        Assertions.assertThat(lineItemQuantity.getQuantity()).isEqualTo(20)
    }

    private static void verifyBarAndBazHave1Line(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> addLineRuleProcessed = rulesFired.findAll {
            it.ruleName == "Bar and Baz both have 1 line"
        }

        Assertions.assertThat(addLineRuleProcessed).hasSize(1)
        Assertions.assertThat(addLineRuleProcessed.get(0).actions).hasSize(1)

        Assertions.assertThat(addLineRuleProcessed[0].actions[0].action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata).isInstanceOf(String.class)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata as String).contains("Bar and Baz both have 1 line")
    }

    private static void verifyBarAndBazHaveSameCustomListUnitPrice(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> addLineRuleProcessed = rulesFired.findAll {
            it.ruleName == "Bar and Baz must have same custom list unit price"
        }

        Assertions.assertThat(addLineRuleProcessed).hasSize(1)
        Assertions.assertThat(addLineRuleProcessed.get(0).actions).hasSize(1)

        Assertions.assertThat(addLineRuleProcessed[0].actions[0].action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata).isInstanceOf(String.class)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata as String).contains("Bar and Baz must have same custom list unit price")
    }

    private static void verifyAttributeReferencesRule(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> addLineRuleProcessed = rulesFired.findAll {
            it.ruleName == "All line items should have different attributes"
        }

        Assertions.assertThat(addLineRuleProcessed).hasSize(1)
        Assertions.assertThat(addLineRuleProcessed.get(0).actions).hasSize(1)

        Assertions.assertThat(addLineRuleProcessed[0].actions[0].action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata).isInstanceOf(String.class)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata as String).contains("Add line should have different MAP Attributes")
    }

    private static void verifyOrderOpportunityRule(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> addLineRuleProcessed = rulesFired.findAll {
            it.ruleName == "Opportunity Based Order Failure Rule"
        }

        Assertions.assertThat(addLineRuleProcessed).hasSize(1)
        Assertions.assertThat(addLineRuleProcessed.get(0).actions).hasSize(1)

        Assertions.assertThat(addLineRuleProcessed[0].actions[0].action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata).isInstanceOf(String.class)
        Assertions.assertThat(addLineRuleProcessed[0].actions[0].metadata as String).contains("Bad Opportunity rule test")
    }

    static void verifySumOfQuantitySettingCustomField(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> customFieldBasedSettingRule = rulesFired.findAll {
            it.ruleName == "Sum of lines quantity based on order start date"
        }

        Assertions.assertThat(customFieldBasedSettingRule).hasSize(1)
        Assertions.assertThat(customFieldBasedSettingRule.get(0).actions).hasSize(1)

        Assertions.assertThat(customFieldBasedSettingRule[0].actions[0].action).isEqualTo(CustomizationAction.APPLY_ORDER_CUSTOM_FIELD)
        Assertions.assertThat(customFieldBasedSettingRule[0].actions[0].metadata).isInstanceOf(CustomFieldValue.class)
        CustomFieldValue setValue = customFieldBasedSettingRule[0].actions[0].metadata as CustomFieldValue
        Assertions.assertThat(setValue.identifier).isEqualTo("foo")
        Assertions.assertThat(setValue.values).hasSize(1)
        Assertions.assertThat(setValue.values).contains("10")
    }

    static void verifySumOfQuantityDateNotMatchSettingCustomField(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> customFieldBasedSettingRule = rulesFired.findAll {
            it.ruleName == "Sum of lines quantity based on order NOT date"
        }

        Assertions.assertThat(customFieldBasedSettingRule).hasSize(1)
        Assertions.assertThat(customFieldBasedSettingRule.get(0).actions).hasSize(1)

        Assertions.assertThat(customFieldBasedSettingRule[0].actions[0].action).isEqualTo(CustomizationAction.APPLY_ORDER_CUSTOM_FIELD)
        Assertions.assertThat(customFieldBasedSettingRule[0].actions[0].metadata).isInstanceOf(CustomFieldValue.class)
        CustomFieldValue setValue = customFieldBasedSettingRule[0].actions[0].metadata as CustomFieldValue
        Assertions.assertThat(setValue.identifier).isEqualTo("bar")
        Assertions.assertThat(setValue.values).hasSize(1)
        Assertions.assertThat(setValue.values).contains("15")
    }
}
