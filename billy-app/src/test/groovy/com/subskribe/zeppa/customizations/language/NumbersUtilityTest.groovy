package com.subskribe.zeppa.customizations.language


import groovy.test.GroovyTestCase
import org.apache.commons.lang3.StringUtils
import org.assertj.core.api.Assertions

class NumbersUtilityTest extends GroovyTestCase {

    private static final BigDecimal MINUS_ONE = new BigDecimal(-1)

    void testIsNumberAndToBigDecimal() {
        Assertions.assertThat(Numbers.isNumber(null)).isFalse()
        Assertions.assertThat(Numbers.isNumber(StringUtils.EMPTY)).isFalse()
        Assertions.assertThat(Numbers.isNumber("      ")).isFalse()

        Assertions.assertThat(Numbers.isNumber("  200    ")).isTrue()
        Assertions.assertThat(Numbers.isNumber("200    ")).isTrue()
        Assertions.assertThat(Numbers.isNumber("  200")).isTrue()
        Assertions.assertThat(Numbers.isNumber("  20 0    ")).isFalse()
        Assertions.assertThat(Numbers.isNumber("  20,000    ")).isTrue()

        Assertions.assertThat(Numbers.isNumber("  Test 20,000    ")).isFalse()
        Assertions.assertThat(Numbers.isNumber("  Test    ")).isFalse()
        Assertions.assertThat(Numbers.isNumber("\$20,000")).isFalse()

        Assertions.assertThat(Numbers.isNumber("  -200    ")).isTrue()
        Assertions.assertThat(Numbers.isNumber("  -20,000,000    ")).isTrue()
        Assertions.assertThat(Numbers.isNumber("-20,000,000")).isTrue()
        Assertions.assertThat(Numbers.isNumber("  - 200    ")).isFalse()


        // now test the number
        Assertions.assertThat(Numbers.toBigDecimal("  200    ")).isEqualTo(new BigDecimal("200"))
        Assertions.assertThat(Numbers.toBigDecimal("  20,000,000    ")).isEqualTo(new BigDecimal("20000000"))
        Assertions.assertThat(Numbers.toBigDecimal("20,000,000")).isEqualTo(new BigDecimal("20000000"))
        // now test for negative numbers
        Assertions.assertThat(Numbers.toBigDecimal("  -200    ")).isEqualTo(new BigDecimal("-200"))
        Assertions.assertThat(Numbers.toBigDecimal("  -20,000,000    ")).isEqualTo(new BigDecimal("-20000000"))
        Assertions.assertThat(Numbers.toBigDecimal("-20,000,000")).isEqualTo(new BigDecimal("-20000000"))
        Assertions.assertThat(Numbers.toBigDecimal("  - 200    ")).isEqualTo(BigDecimal.ZERO)


        // invalid numbers
        Assertions.assertThat(Numbers.toBigDecimal("20,000, 000")).isEqualTo(BigDecimal.ZERO)
        Assertions.assertThat(Numbers.toBigDecimal("  Test 20,000    ")).isEqualTo(BigDecimal.ZERO)
        Assertions.assertThat(Numbers.toBigDecimal("  Test    ")).isEqualTo(BigDecimal.ZERO)
        Assertions.assertThat(Numbers.toBigDecimal("\$20,000")).isEqualTo(BigDecimal.ZERO)

        Assertions.assertThat(Numbers.toBigDecimal("20,000, 000", -1)).isEqualTo(MINUS_ONE)
        Assertions.assertThat(Numbers.toBigDecimal("  Test 20,000    ", -1)).isEqualTo(MINUS_ONE)
        Assertions.assertThat(Numbers.toBigDecimal("  Test    ", -1)).isEqualTo(MINUS_ONE)
        Assertions.assertThat(Numbers.toBigDecimal("\$20,000", -1)).isEqualTo(MINUS_ONE)
    }

    void testDivisionQuotientWorksAsExpected() {
        Number value = Numbers.quotient(10, 0)
        Assertions.assertThat(value).isEqualTo(-1)

        value = Numbers.quotient(10, 0, 0)
        Assertions.assertThat(value).isEqualTo(0)

        value = Numbers.quotient(10, 5)
        Assertions.assertThat(value == 2).isTrue()

        value = Numbers.quotient(10L, 5)
        Assertions.assertThat(value == 2).isTrue()

        value = Numbers.quotient(9, 2)
        Assertions.assertThat(value == 4).isTrue()

        // cannot use decimal values for quotient division the input has to be
        // long or int
        value = Numbers.quotient(10.4, 2)
        Assertions.assertThat(value == -1).isTrue()
    }

    void testSafeDivideWorksAsExpected() {
        Number value = Numbers.safeDivide(10.0, 0)
        Assertions.assertThat(value == 0).isTrue()
        value = Numbers.safeDivide(10.0, 0.0)
        Assertions.assertThat(value == 0).isTrue()
        value = Numbers.safeDivide(11, 0.000000000000)
        Assertions.assertThat(value == 0).isTrue()
        value = Numbers.safeDivide(12.25, -0.0000)
        Assertions.assertThat(value == 0).isTrue()
        value = Numbers.safeDivide(null, -0)
        Assertions.assertThat(value == 0).isTrue()


        value = Numbers.safeDivide(10.0, 0, -1)
        Assertions.assertThat(value == -1).isTrue()
        value = Numbers.safeDivide(10.0, 0.0, -1)
        Assertions.assertThat(value == -1).isTrue()
        value = Numbers.safeDivide(11, 0.000000000000, -1)
        Assertions.assertThat(value == -1).isTrue()
        value = Numbers.safeDivide(12.25, -0.0000, -1)
        Assertions.assertThat(value == -1).isTrue()
        value = Numbers.safeDivide(null, -0, -1)
        Assertions.assertThat(value == -1).isTrue()

        value = Numbers.safeDivide(1, 3)
        Assertions.assertThat(value).isEqualTo(0.3333333333)

        value = Numbers.safeDivide(1, 3.0)
        Assertions.assertThat(value).isEqualTo(0.3333333333)

        value = Numbers.safeDivide(1, 2, -1)
        Assertions.assertThat(value).isEqualTo(0.5)

        value = Numbers.safeDivide(1, 2.0, -1)
        Assertions.assertThat(value).isEqualTo(0.5)

        // if the value is non zero then the division needs to happen
        value = Numbers.safeDivide(11, 0.000000000000000000001)
        Assertions.assertThat(value).isEqualTo(new BigDecimal("1.1E+22"))

        // if the dividend is null then default value should be returned
        value = Numbers.safeDivide(null, 10, -1)
        Assertions.assertThat(value).isEqualTo(-1)
    }

    void testNumbersToIntWorksAsExpected() {
        int value = Numbers.toInt(null, -1)
        Assertions.assertThat(value).isEqualTo(-1)

        value = Numbers.toInt(StringUtils.EMPTY, -1)
        Assertions.assertThat(value).isEqualTo(-1)

        value = Numbers.toInt("FOOO!!!!!!", -1)
        Assertions.assertThat(value).isEqualTo(-1)

        value = Numbers.toInt("100 ", -1)
        Assertions.assertThat(value).isEqualTo(100)

        value = Numbers.toInt(" 100 ", -1)
        Assertions.assertThat(value).isEqualTo(100)

        value = Numbers.toInt(" -100 ", -1)
        Assertions.assertThat(value).isEqualTo(-100)
    }

    void testNumbersInRangeIsWorkingAsExpected() {
        Assertions.assertThat(Numbers.numberInRange(-1, -1, 10)).isTrue()
        Assertions.assertThat(Numbers.numberInRange(-1, -1, -10)).isFalse()
        Assertions.assertThat(Numbers.numberInRange(5, -1, -10)).isFalse()
        Assertions.assertThat(Numbers.numberInRange(5, -1, 10)).isTrue()

        Assertions.assertThat(Numbers.numberInRange(1.5, 1.567, 10.1119)).isFalse()
        Assertions.assertThat(Numbers.numberInRange(1.5672, 1.567, 10.1119)).isTrue()
        Assertions.assertThat(Numbers.numberInRange(10.11191, 1.567, 10.1119)).isFalse()
        Assertions.assertThat(Numbers.numberInRange(10.1119, 1.567, 10.1119)).isTrue()
        Assertions.assertThat(Numbers.numberInRange(10.1118, 1.567, 10.1119)).isTrue()
    }

    void testNumbersToStringTransformWorksAsExpected() {
        Assertions.assertThat(Numbers.toString(null)).isBlank()
        Assertions.assertThat(Numbers.toString(10)).isEqualTo("10")
        Assertions.assertThat(Numbers.toString(1.1)).isEqualTo("1.1")

        Assertions.assertThat(Numbers.toString(10, {
            switch (it) {
                case 1..5:
                    return "1-5"
                case 6..10:
                    return "6-10"
                case it >= 11:
                    return "11+"
            }
        })).isEqualTo("6-10")

        Assertions.assertThat(Numbers.toString(11, {
            switch (it) {
                case 1..5:
                    return "1-5"
                case 6..10:
                    return "6-10"
                case { it >= 11 }:
                    return "11+"
            }
        })).isEqualTo("11+")
    }

    void testNumbersToNumbersTransformWorksAsExpected() {
        Assertions.assertThat(Numbers.toNumber(null)).isEqualTo(-1)
        Assertions.assertThat(Numbers.toNumber(10)).isEqualTo(10)
        Assertions.assertThat(Numbers.toNumber(-10)).isEqualTo(-10)
        Assertions.assertThat(Numbers.toNumber(0)).isEqualTo(-1)
        Assertions.assertThat(Numbers.toNumber(10.25)).isEqualTo(10.25)
        Assertions.assertThat(Numbers.toNumber(-1)).isEqualTo(-1)

        Closure testTransformOne = { it ->
            switch (it) {
                case 1..5:
                    return 2 * it
                case 6..10:
                    return 3 * it
                case { it >= 11 }:
                    return "11+"
            }
        }

        // if 11 string is returned so return value should be sentinel
        Assertions.assertThat(Numbers.toNumber(11, testTransformOne)).isEqualTo(-1)

        // if 10 then triple the value should be returned
        Assertions.assertThat(Numbers.toNumber(10, testTransformOne)).isEqualTo(30)

        // if 4 then double the value should be returned
        Assertions.assertThat(Numbers.toNumber(4, testTransformOne)).isEqualTo(8)

        // 0 is no match so sentinel should be returned
        Assertions.assertThat(Numbers.toNumber(0, testTransformOne)).isEqualTo(-1)

        Closure longReturnLambda = { it ->
            if (it) {
                return 2L * it
            }
        }

        Assertions.assertThat(Numbers.toNumber(5, longReturnLambda)).isEqualTo(10L)
        Assertions.assertThat(Numbers.toNumber(0, longReturnLambda)).isEqualTo(-1)
        Assertions.assertThat(Numbers.toNumber(-5, longReturnLambda)).isEqualTo(-10L)

        Closure reallyBadLambda = { it ->
            return "I always return string"
        }
        Assertions.assertThat(Numbers.toNumber(0, reallyBadLambda)).isEqualTo(-1)
    }
}
