package com.subskribe.zeppa.customizations.bindings

import static com.subskribe.zeppa.customizations.bindings.CatalogBindingTest.getSampleCharge
import static com.subskribe.zeppa.customizations.bindings.CatalogBindingTest.getSamplePlan
import static com.subskribe.zeppa.customizations.bindings.CatalogBindingTest.getSampleProduct
import static com.subskribe.zeppa.customizations.selection.SelectionCustomizationTest.getTestAccount

import com.subskribe.billy.account.model.Account
import com.subskribe.billy.account.model.AccountContact
import com.subskribe.billy.customfield.model.CustomField
import com.subskribe.billy.customfield.model.CustomFieldSource
import com.subskribe.billy.customfield.model.CustomFieldType
import com.subskribe.billy.customfield.model.CustomFieldValue
import com.subskribe.billy.graphql.customfield.CustomFieldEntry
import com.subskribe.billy.invoice.model.PaymentTerm
import com.subskribe.billy.opportunity.model.Opportunity
import com.subskribe.billy.opportunity.model.OpportunityCrmType
import com.subskribe.billy.order.model.Order
import com.subskribe.billy.order.model.OrderLineItem
import com.subskribe.billy.productcatalog.model.Charge
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference
import com.subskribe.billy.shared.enums.ActionType
import com.subskribe.billy.shared.enums.ChargeModel
import com.subskribe.billy.shared.enums.ChargeType
import com.subskribe.billy.shared.enums.Cycle
import com.subskribe.billy.shared.pecuniary.DiscountDetail
import com.subskribe.billy.shared.temporal.Recurrence
import groovy.test.GroovyTestCase
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import nl.jqno.equalsverifier.EqualsVerifier
import org.apache.commons.lang3.RandomStringUtils
import org.apache.commons.lang3.StringUtils
import org.assertj.core.api.Assertions

class OrderBindingTest extends GroovyTestCase {

    public static final ZoneId PACIFIC_ZONE_ID = ZoneId.of("America/Los_Angeles")
    public static final ZonedDateTime TEST_START_DATE = ZonedDateTime.of(LocalDate.of(2024, 01, 01), LocalTime.of(0, 0), PACIFIC_ZONE_ID)

    void testNullSafetyOfOrderBinding() {
        OrderBinding orderBinding = OrderBinding.from(null,
                AccountBinding.from(null, null, null),
                null,
                null)

        Assertions.assertThat(orderBinding.type).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.status).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.currency).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.billingCycle).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.paymentDueInDays).isEqualTo(-1)
        Assertions.assertThat(orderBinding.isAutoRenew).isEqualTo(false)
        Assertions.assertThat(orderBinding.addedPlansMatchCount {
            on plan.name == "Test"
        }).isEqualTo(0)
        Assertions.assertThat(orderBinding.anyAddedPlansMatch {
            on plan.name == "Test"
        }).isFalse()

        Assertions.assertThat(orderBinding.modifiedPlansMatchCount {
            on plan.name == "Test"
        }).isEqualTo(0)
        Assertions.assertThat(orderBinding.anyModifiedPlansMatch {
            on plan.name == "Test"
        }).isFalse()

        Assertions.assertThat(orderBinding.removedPlansMatchCount {
            on plan.name == "Test"
        }).isEqualTo(0)
        Assertions.assertThat(orderBinding.anyRemovedPlansMatch {
            on plan.name == "Test"
        }).isFalse()

        Assertions.assertThat(orderBinding.account).isNotNull()
        Assertions.assertThat(orderBinding.name).isBlank()
        Assertions.assertThat(orderBinding.crmOpportunityId).isBlank()
        Assertions.assertThat(orderBinding.compositeOrderId).isBlank()

        Assertions.assertThat(orderBinding.startDate.dayOfMonth).isEqualTo(0)
        Assertions.assertThat(orderBinding.startDate.monthOfYear).isEqualTo(0)
        Assertions.assertThat(orderBinding.startDate.year).isEqualTo(0)

        Assertions.assertThat(orderBinding.endDate.dayOfMonth).isEqualTo(0)
        Assertions.assertThat(orderBinding.endDate.monthOfYear).isEqualTo(0)
        Assertions.assertThat(orderBinding.endDate.year).isEqualTo(0)

        Assertions.assertThat(orderBinding.expiresOn.dayOfMonth).isEqualTo(0)
        Assertions.assertThat(orderBinding.expiresOn.monthOfYear).isEqualTo(0)
        Assertions.assertThat(orderBinding.expiresOn.year).isEqualTo(0)

        // test line item matching
        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.planId == "X"
        }.sumOfQuantity).isZero()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.planId == "X"
        }.maxQuantity).isZero()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.planId == "X"
        }.lineCount).isZero()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.planId == "X"
        }.first.quantity).isZero()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.planId == "X"
        }.sumOfQuantity - 1).isEqualTo(-1)

        // test contact emptiness
        Assertions.assertThat(orderBinding.shippingContact).isNotNull()
        Assertions.assertThat(orderBinding.shippingContact.id).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.shippingContact.accountId).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.shippingContact.account).isNotNull()
        Assertions.assertThat(orderBinding.shippingContact.account.id).isEqualTo(StringUtils.EMPTY)

        Assertions.assertThat(orderBinding.billingContact).isNotNull()
        Assertions.assertThat(orderBinding.billingContact.id).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.billingContact.accountId).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.billingContact.account).isNotNull()
        Assertions.assertThat(orderBinding.billingContact.account.id).isEqualTo(StringUtils.EMPTY)

        Assertions.assertThat(orderBinding.isResold).isFalse()

        // opportunity check
        Assertions.assertThat(orderBinding.opportunity.name).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.opportunity.crmType).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.opportunity.stage).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.opportunity.type).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.opportunity.customField("X")).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(orderBinding.opportunity.customFieldMatches("X", "Y")).isFalse()
    }

    void testShippingAndBillingContactBinding() {
        String accountIdOne = RandomStringUtils.randomAlphanumeric(10)
        Account accountOne = getTestAccount()
        accountOne.setAccountId(accountIdOne)
        AccountContact accountContactOne = new AccountContact()
        accountContactOne.contactId = "CONT-${RandomStringUtils.randomAlphanumeric(5).toUpperCase()}"
        accountContactOne.accountId = accountIdOne
        AccountBinding accountBindingOne = AccountBinding.from(accountOne, null, null)
        ContactBinding shippingContact = ContactBinding.from(accountContactOne, accountBindingOne)

        String accountIdTwo = RandomStringUtils.randomAlphanumeric(10)
        Account accountTwo = getTestAccount()
        accountTwo.setAccountId(accountIdTwo)
        accountTwo.setIsReseller(true)
        AccountContact accountContactTwo = new AccountContact()
        accountContactTwo.contactId = "CONT-${RandomStringUtils.randomAlphanumeric(5).toUpperCase()}"
        accountContactTwo.accountId = accountIdTwo
        AccountBinding accountBindingTwo = AccountBinding.from(accountTwo, null, null)
        ContactBinding billingContact = ContactBinding.from(accountContactTwo, accountBindingTwo)

        OrderBinding testOrder =
                OrderBinding.from(null,
                accountBindingOne,
                null,
                null,
                shippingContact,
                billingContact)

        Assertions.assertThat(testOrder.billingContact).isNotNull()
        Assertions.assertThat(testOrder.billingContact.accountId).isEqualTo(accountIdTwo)
        Assertions.assertThat(testOrder.billingContact.id).isEqualTo(accountContactTwo.contactId)
        Assertions.assertThat(testOrder.billingContact.account).isNotNull()
        Assertions.assertThat(testOrder.billingContact.account.id).isEqualTo(accountIdTwo)

        Assertions.assertThat(testOrder.shippingContact).isNotNull()
        Assertions.assertThat(testOrder.shippingContact.accountId).isEqualTo(accountIdOne)
        Assertions.assertThat(testOrder.shippingContact.id).isEqualTo(accountContactOne.contactId)
        Assertions.assertThat(testOrder.shippingContact.account).isNotNull()
        Assertions.assertThat(testOrder.shippingContact.account.id).isEqualTo(accountIdOne)

        Assertions.assertThat(testOrder.isResold).isTrue()
    }

    void testNullSafetyOfOrderLineItemBinding() {
        OrderLineItemBinding lineItemBinding = OrderLineItemBinding.from(null, null, null, null)
        Assertions.assertThat(lineItemBinding.planId).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.chargeId).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.charge.id).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.charge.name).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.charge.type).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.charge.model).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.charge.description).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.charge.customField("foo")).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.charge.recurrenceStep).isEqualTo(1)

        Assertions.assertThat(lineItemBinding.plan.id).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.plan.name).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.plan.status).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.plan.currency).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.plan.customField("bar")).isEqualTo(StringUtils.EMPTY)

        // line item custom fields should also be null safe
        Assertions.assertThat(lineItemBinding.customField("foo")).isEqualTo(StringUtils.EMPTY)
        Assertions.assertThat(lineItemBinding.customFieldMatches("foo", "bar")).isFalse()
        Assertions.assertThat(lineItemBinding.customFieldEmpty("foo")).isTrue()

        // debook should be false
        Assertions.assertThat(lineItemBinding.isDebook).isFalse()

        // quantity should be 0
        Assertions.assertThat(lineItemBinding.quantity).isZero()

        // default discount amount is 0
        Assertions.assertThat(lineItemBinding.hasAnyDiscount).isFalse()
        Assertions.assertThat(lineItemBinding.lineDiscountPercent).isZero()

        Assertions.assertThat(lineItemBinding.startDate).isNotNull()
        Assertions.assertThat(lineItemBinding.startDate.dayOfMonth).isEqualTo(0)
        Assertions.assertThat(lineItemBinding.startDate.monthOfYear).isEqualTo(0)
        Assertions.assertThat(lineItemBinding.startDate.year).isEqualTo(0)

        Assertions.assertThat(lineItemBinding.endDate).isNotNull()
        Assertions.assertThat(lineItemBinding.endDate.dayOfMonth).isEqualTo(0)
        Assertions.assertThat(lineItemBinding.endDate.monthOfYear).isEqualTo(0)
        Assertions.assertThat(lineItemBinding.endDate.year).isEqualTo(0)
    }

    void testOrderLineItemCustomFieldBinding() {
        OrderLineItem testOrderLineItem = getOrderLineItem(getCustomField(["foo": "bar"]))

        OrderLineItemBinding lineItemBinding = OrderLineItemBinding.from(testOrderLineItem, null, null, PACIFIC_ZONE_ID)
        Assertions.assertThat(lineItemBinding.customField("foo")).isEqualTo("bar")
        Assertions.assertThat(lineItemBinding.customFieldEmpty("foo")).isFalse()

        lineItemBinding = OrderLineItemBinding.from(getOrderLineItem(getCustomFieldList(["foo": ["bar", "baz"]])), null, null, PACIFIC_ZONE_ID)
        Assertions.assertThat(lineItemBinding.customFieldMatches("foo", "bar")).isTrue()
        Assertions.assertThat(lineItemBinding.customFieldEmpty("foo")).isFalse()
        Assertions.assertThat(lineItemBinding.customFieldMatches("foo", "baz")).isTrue()

        // foobar should be empty
        Assertions.assertThat(lineItemBinding.customFieldEmpty("foobar")).isTrue()

        // test debook and action attributes
        lineItemBinding = OrderLineItemBinding.from(testOrderLineItem, null, null, PACIFIC_ZONE_ID)
        testOrderLineItem.action = ActionType.UPDATE

        testOrderLineItem.quantity = 2
        Assertions.assertThat(lineItemBinding.action).isEqualTo(ActionType.UPDATE.name())
        Assertions.assertThat(lineItemBinding.isDebook).isFalse()
        Assertions.assertThat(lineItemBinding.quantity).isEqualTo(2)

        testOrderLineItem.quantity = -3
        Assertions.assertThat(lineItemBinding.isDebook).isTrue()
        Assertions.assertThat(lineItemBinding.quantity).isEqualTo(-3)

        testOrderLineItem.action = ActionType.RENEWAL
        Assertions.assertThat(lineItemBinding.isDebook).isFalse()
    }

    void testOrderLineItemDiscountBinding() {
        OrderLineItem orderLineItem = new OrderLineItem()
        orderLineItem.id = UUID.randomUUID()
        orderLineItem.discounts = []

        OrderLineItemBinding lineItemBinding = OrderLineItemBinding.from(orderLineItem, null, null, PACIFIC_ZONE_ID)
        Assertions.assertThat(lineItemBinding.lineDiscountPercent).isZero()
        Assertions.assertThat(lineItemBinding.hasAnyDiscount).isFalse()

        orderLineItem.discounts = [
            new DiscountDetail(percent: new BigDecimal("0.1"))
        ]
        lineItemBinding = OrderLineItemBinding.from(orderLineItem, null, null, PACIFIC_ZONE_ID)

        Assertions.assertThat(lineItemBinding.lineDiscountPercent).isEqualTo(10.00)
        Assertions.assertThat(lineItemBinding.hasAnyDiscount).isTrue()
    }

    void testOrderHeaderBindings() {
        AccountBinding emptyAccountBinding = AccountBinding.from(null, null, null)
        Order order = new Order()
        order.setBillingCycle(new Recurrence(Cycle.YEAR, 1))
        order.setPaymentTerm(PaymentTerm.NET45)
        order.setName("This is Awesome Name")
        order.setAutoRenew(true)
        String opportunityId = UUID.randomUUID().toString()
        order.setSfdcOpportunityId(opportunityId)
        String compositeOrderId = UUID.randomUUID().toString()
        order.setCompositeOrderId(compositeOrderId)
        Opportunity opportunity = makeTestOpportunity()
        order.setOpportunity(opportunity)
        OrderBinding orderBinding = OrderBinding.from(order, emptyAccountBinding, null, PACIFIC_ZONE_ID)

        Assertions.assertThat(orderBinding.billingCycle).isEqualTo("YEAR")
        Assertions.assertThat(orderBinding.paymentDueInDays).isEqualTo(45)
        Assertions.assertThat(orderBinding.name).isEqualTo("This is Awesome Name")
        Assertions.assertThat(orderBinding.isAutoRenew).isTrue()
        Assertions.assertThat(orderBinding.crmOpportunityId.isBlank()).isFalse()
        Assertions.assertThat(orderBinding.crmOpportunityId).isEqualTo(opportunityId)
        Assertions.assertThat(orderBinding.compositeOrderId).isEqualTo(compositeOrderId)

        Assertions.assertThat(orderBinding.opportunity.name).isEqualTo(opportunity.name)
        Assertions.assertThat(orderBinding.opportunity.type).isEqualTo(opportunity.type)
        Assertions.assertThat(orderBinding.opportunity.crmType).isEqualTo(opportunity.opportunityCrmType.name())
    }

    void testOrderDateRelatedBindings() {
        AccountBinding emptyAccountBinding = AccountBinding.from(null, null, null)
        int durationDays = 360
        OrderBinding orderBinding = OrderBinding.from(getOrderWithDuration(durationDays),
                emptyAccountBinding,
                null,
                PACIFIC_ZONE_ID)
        Assertions.assertThat(orderBinding.getTermLengthYears()).isEqualTo(0)
        Assertions.assertThat(orderBinding.getTermLengthDays()).isEqualTo(durationDays)
        Assertions.assertThat(orderBinding.startDate.dayOfMonth).isEqualTo(TEST_START_DATE.getDayOfMonth())
        Assertions.assertThat(orderBinding.startDate.monthOfYear).isEqualTo(TEST_START_DATE.getMonthValue())
        Assertions.assertThat(orderBinding.startDate.year).isEqualTo(TEST_START_DATE.getYear())

        ZonedDateTime endDate = TEST_START_DATE.plusDays(durationDays)
        Assertions.assertThat(orderBinding.endDate.dayOfMonth).isEqualTo(endDate.getDayOfMonth())
        Assertions.assertThat(orderBinding.endDate.monthOfYear).isEqualTo(endDate.getMonthValue())
        Assertions.assertThat(orderBinding.endDate.year).isEqualTo(endDate.getYear())


        durationDays = 365
        orderBinding = OrderBinding.from(getOrderWithDuration(durationDays),
                emptyAccountBinding,
                null,
                PACIFIC_ZONE_ID)
        Assertions.assertThat(orderBinding.getTermLengthYears()).isEqualTo(1)
        Assertions.assertThat(orderBinding.getTermLengthDays()).isEqualTo(durationDays)
        Assertions.assertThat(orderBinding.startDate.dayOfMonth).isEqualTo(TEST_START_DATE.getDayOfMonth())
        Assertions.assertThat(orderBinding.startDate.monthOfYear).isEqualTo(TEST_START_DATE.getMonthValue())
        Assertions.assertThat(orderBinding.startDate.year).isEqualTo(TEST_START_DATE.getYear())

        endDate = TEST_START_DATE.plusDays(durationDays)
        Assertions.assertThat(orderBinding.endDate.dayOfMonth).isEqualTo(endDate.getDayOfMonth())
        Assertions.assertThat(orderBinding.endDate.monthOfYear).isEqualTo(endDate.getMonthValue())
        Assertions.assertThat(orderBinding.endDate.year).isEqualTo(endDate.getYear())


        durationDays = 365 * 2 + 10
        orderBinding = OrderBinding.from(getOrderWithDuration(durationDays),
                emptyAccountBinding,
                null,
                PACIFIC_ZONE_ID)
        Assertions.assertThat(orderBinding.getTermLengthYears()).isEqualTo(2)
        Assertions.assertThat(orderBinding.getTermLengthDays()).isEqualTo(durationDays)
        Assertions.assertThat(orderBinding.startDate.dayOfMonth).isEqualTo(TEST_START_DATE.getDayOfMonth())
        Assertions.assertThat(orderBinding.startDate.monthOfYear).isEqualTo(TEST_START_DATE.getMonthValue())
        Assertions.assertThat(orderBinding.startDate.year).isEqualTo(TEST_START_DATE.getYear())

        // test null first
        Assertions.assertThat(orderBinding.startDate > null).isTrue()
        Assertions.assertThat(orderBinding.startDate == null).isFalse()
        Assertions.assertThat(orderBinding.startDate < null).isFalse()
        Assertions.assertThat(DateBinding.from(null, null) < orderBinding.startDate).isTrue()
        Assertions.assertThat(orderBinding.startDate > DateBinding.from(null, null)).isTrue()
        Assertions.assertThat(orderBinding.startDate >= DateBinding.from(null, null)).isTrue()
        Assertions.assertThat(orderBinding.startDate < DateBinding.from(null, null)).isFalse()

        DateBinding plusOneDay = DateBinding.from(TEST_START_DATE.plusDays(1).toInstant(), PACIFIC_ZONE_ID)
        Assertions.assertThat(orderBinding.startDate > plusOneDay).isFalse()
        Assertions.assertThat(orderBinding.startDate <= plusOneDay).isTrue()
        Assertions.assertThat(orderBinding.startDate < plusOneDay).isTrue()
        Assertions.assertThat(orderBinding.startDate == plusOneDay).isFalse()


        endDate = TEST_START_DATE.plusDays(durationDays)
        Assertions.assertThat(orderBinding.endDate.dayOfMonth).isEqualTo(endDate.getDayOfMonth())
        Assertions.assertThat(orderBinding.endDate.monthOfYear).isEqualTo(endDate.getMonthValue())
        Assertions.assertThat(orderBinding.endDate.year).isEqualTo(endDate.getYear())

        orderBinding = OrderBinding.from(null, emptyAccountBinding, null, null)
        Assertions.assertThat(orderBinding.getTermLengthYears()).isEqualTo(-1)
        Assertions.assertThat(orderBinding.getTermLengthDays()).isEqualTo(-1)
        Assertions.assertThat(orderBinding.startDate.dayOfMonth).isEqualTo(0)
        Assertions.assertThat(orderBinding.startDate.monthOfYear).isEqualTo(0)
        Assertions.assertThat(orderBinding.startDate.year).isEqualTo(0)

        Assertions.assertThat(orderBinding.endDate.dayOfMonth).isEqualTo(0)
        Assertions.assertThat(orderBinding.endDate.monthOfYear).isEqualTo(0)
        Assertions.assertThat(orderBinding.endDate.year).isEqualTo(0)
    }

    void testOrderLineItemMatching() {
        Charge recurringCharge = getSampleCharge()
        recurringCharge.setRecurrence(new Recurrence(Cycle.YEAR, 2))
        PlanBinding planBindingOne = PlanBinding.from(getSamplePlan(),
                getCustomField(["foo": "bar"]),
                List.of(getChargeBinding(recurringCharge, getCustomField(["foo": "bar"]))),
                ProductBinding.from(getSampleProduct()))

        Charge oneTimeCharge = getSampleCharge()
        oneTimeCharge.type = ChargeType.ONE_TIME
        oneTimeCharge.chargeModel = ChargeModel.FLAT_FEE

        PlanBinding planBindingTwo = PlanBinding.from(getSamplePlan(),
                getCustomField(["foo": "baz"]),
                List.of(getChargeBinding(oneTimeCharge, getCustomField(["foo": "baz"]))),
                ProductBinding.from(getSampleProduct()))

        Order order = getOrderWithLineItem(ActionType.ADD, planBindingOne, planBindingTwo)

        // add quantity to each item
        order.lineItemsNetEffect[0].quantity = 5
        order.lineItemsNetEffect[1].quantity = 10

        // current instant
        Instant startDateOne = Instant.now()
        Instant endDateOne = Instant.now().plus(100, ChronoUnit.DAYS)
        order.lineItemsNetEffect[0].effectiveDate = startDateOne
        order.lineItemsNetEffect[0].endDate = endDateOne
        DateBinding startDateOneBinding = DateBinding.from(startDateOne, PACIFIC_ZONE_ID)
        DateBinding endDateOneBinding = DateBinding.from(endDateOne, PACIFIC_ZONE_ID)

        Instant startDateTwo = Instant.now()
        Instant endDateTwo = Instant.now().plus(100, ChronoUnit.DAYS)
        order.lineItemsNetEffect[1].effectiveDate = startDateTwo
        order.lineItemsNetEffect[1].endDate = endDateTwo
        DateBinding startDateTwoBinding = DateBinding.from(startDateTwo, PACIFIC_ZONE_ID)
        DateBinding endDateTwoBinding = DateBinding.from(endDateTwo, PACIFIC_ZONE_ID)

        OrderBinding orderBinding = OrderBinding.from(order,
                AccountBinding.from(null, null, null),
                List.of(planBindingOne, planBindingTwo),
                PACIFIC_ZONE_ID)

        // data setup complete now we can test the predicates
        Assertions.assertThat(orderBinding.anyLineItemsMatch {
            on lineItem.action in ["UPDATE", "REMOVE"]
        }).isFalse()

        Assertions.assertThat(orderBinding.lineItemsMatchingCount {
            when lineItem.action in ["UPDATE", "REMOVE"]
        }).isZero()

        Assertions.assertThat(orderBinding.anyLineItemsMatch {
            on lineItem.action in ["ADD"]
        }).isTrue()
        Assertions.assertThat(orderBinding.lineItemsMatchingCount {
            on lineItem.action in ["ADD"]
        }).isEqualTo(2)

        // now let us inspect plan and charge attributes
        Assertions.assertThat(orderBinding.anyLineItemsMatch {
            on lineItem.plan.customFieldMatches("foo", "bar")
            and lineItem.charge.type in ["RECURRING"]
        }).isTrue()

        Assertions.assertThat(orderBinding.lineItemsMatchingCount {
            on lineItem.plan.customFieldMatches("foo", "bar")
            and lineItem.charge.type in ["RECURRING"]
        }).isEqualTo(1)

        Assertions.assertThat(orderBinding.lineItemsMatchingCount {
            on lineItem.plan.customFieldMatches("foo", "bar")
            and lineItem.charge.recurrence == "YEAR"
            and lineItem.charge.recurrenceStep == 2
        }).isEqualTo(1)

        Assertions.assertThat(orderBinding.lineItemsMatchingCount {
            on lineItem.plan.customFieldMatches("foo", "bar")
            and lineItem.charge.type in ["ONE_TIME"]
        }).isEqualTo(0)

        Assertions.assertThat(orderBinding.anyLineItemsMatch {
            on lineItem.plan.customFieldMatches("foo", "baz")
            and lineItem.charge.type in ["ONE_TIME"]
        }).isTrue()

        Assertions.assertThat(orderBinding.lineItemsMatchingCount {
            on lineItem.plan.customFieldMatches("foo", "baz")
            and lineItem.charge.type in ["ONE_TIME"]
        }).isEqualTo(1)

        Assertions.assertThat(orderBinding.lineItemsMatchingCount {
            on lineItem.plan.customFieldMatches("foo", "baz")
            and lineItem.charge.type in ["RECURRING"]
        }).isEqualTo(0)

        // now verify multi line matching with or condition
        Assertions.assertThat(orderBinding.anyLineItemsMatch {
            when lineItem.planId in [
                planBindingOne.id,
                planBindingTwo.id
            ]
            // there are no custom fields in the any line item so this is always true
            and not(lineItem.customFieldMatches("foo", "bar"))
        }).isTrue()

        Assertions.assertThat(orderBinding.lineItemsMatchingCount {
            when lineItem.planId in [
                planBindingOne.id,
                planBindingTwo.id
            ]
            // there are no custom fields in the any line item so this is always true
            and not(lineItem.customFieldMatches("foo", "bar"))
        }).isEqualTo(2)

        // now verify quantity lines matching
        EqualsVerifier.simple().forClass(DateBinding.class).verify()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.startDate == startDateOneBinding
        }).isNotNull()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.startDate == startDateOneBinding
        }.lineCount).isEqualTo(1)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.endDate == endDateOneBinding
        }).isNotNull()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.endDate == endDateOneBinding
        }.lineCount).isEqualTo(1)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.startDate == startDateTwoBinding
        }).isNotNull()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.startDate == startDateTwoBinding
        }.lineCount).isEqualTo(1)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.endDate == endDateTwoBinding
        }).isNotNull()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.endDate == endDateTwoBinding
        }.lineCount).isEqualTo(1)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["ADD"]
        }).isNotNull()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["ADD"]
        }.sumOfQuantity).isEqualTo(15)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["ADD"]
        }.maxQuantity).isEqualTo(10)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["ADD"]
        }.minQuantity).isEqualTo(5)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["ADD"]
        }.lineCount).isEqualTo(2)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["UPDATE"]
        }.sumOfQuantity).isZero()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["UPDATE"]
        }.maxQuantity).isZero()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["UPDATE"]
        }.minQuantity).isZero()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["UPDATE"]
        }.lineCount).isZero()

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.plan.customFieldMatches("foo", "bar")
            and lineItem.charge.type in ["RECURRING"]
        }.sumOfQuantity).isEqualTo(5)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.plan.customFieldMatches("foo", "bar")
            and lineItem.charge.type in ["RECURRING"]
        }.maxQuantity).isEqualTo(5)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.plan.customFieldMatches("foo", "bar")
            and lineItem.charge.type in ["RECURRING"]
        }.minQuantity).isEqualTo(5)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.plan.customFieldMatches("foo", "bar")
            and lineItem.charge.type in ["RECURRING"]
        }.lineCount).isEqualTo(1)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.plan.customFieldMatches("foo", "bar")
            and lineItem.charge.type in ["RECURRING"]
        }.first.quantity).isEqualTo(5)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.plan.customFieldMatches("foo", "baz")
            and lineItem.charge.type in ["ONE_TIME"]
        }.sumOfQuantity).isEqualTo(10)

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.plan.customFieldMatches("foo", "baz")
            and lineItem.charge.type in ["ONE_TIME"]
        }.lineCount).isEqualTo(1)

        // setup the line items with attribute references for testing
        order.lineItemsNetEffect[0].attributeReferences = List.of(new AttributeReference("x", "1"))

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["ADD"]
        }.haveSameAttributeReferences).isFalse()

        // the one attribute should match
        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.plan.customFieldMatches("foo", "bar")
        }.haveSameAttributeReferences).isTrue()

        order.lineItemsNetEffect[0].attributeReferences = List.of(new AttributeReference("x", "1"))
        order.lineItemsNetEffect[1].attributeReferences = List.of(new AttributeReference("x", "1"))

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["ADD"]
        }.haveSameAttributeReferences).isTrue()

        order.lineItemsNetEffect[0].attributeReferences = List.of(new AttributeReference("x", "1"),
                new AttributeReference("y", "2"))
        order.lineItemsNetEffect[1].attributeReferences = List.of(new AttributeReference("y", "2"),
                new AttributeReference("x", "1"))

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["ADD"]
        }.haveSameAttributeReferences).isTrue()

        // only one line but has attributes so it should be a match
        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.plan.customFieldMatches("foo", "baz")
            and lineItem.charge.type in ["ONE_TIME"]
        }.haveSameAttributeReferences).isTrue()

        order.lineItemsNetEffect[0].attributeReferences = List.of(new AttributeReference("x", "1"),
                new AttributeReference("y", "2"))
        order.lineItemsNetEffect[1].attributeReferences = List.of(new AttributeReference("y", "2"))

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["ADD"]
        }.haveSameAttributeReferences).isFalse()

        order.lineItemsNetEffect[0].attributeReferences = List.of(new AttributeReference("x", "1"),
                new AttributeReference("y", "2"))
        order.lineItemsNetEffect[1].attributeReferences = List.of(new AttributeReference("y", "2"),
                new AttributeReference("X", "1"))

        Assertions.assertThat(orderBinding.lineItemsMatching {
            on lineItem.action in ["ADD"]
        }.haveSameAttributeReferences).isFalse()
    }

    void testPlanMatchingOnBinding() {
        OrderBinding orderBinding = OrderBinding.from(null,
                AccountBinding.from(null, null, null),
                null,
                null)

        boolean anyAddedMatch = orderBinding.anyAddedPlansMatch {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        int addedMatchCount = orderBinding.addedPlansMatchCount {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        Assertions.assertThat(addedMatchCount).isZero()
        Assertions.assertThat(anyAddedMatch).isFalse()

        boolean anyModifiedMatch = orderBinding.anyModifiedPlansMatch {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        int modifiedMatchCount = orderBinding.modifiedPlansMatchCount {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        Assertions.assertThat(anyModifiedMatch).isFalse()
        Assertions.assertThat(modifiedMatchCount).isZero()

        boolean anyRemovedMatch = orderBinding.anyRemovedPlansMatch {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        int removedMatchCount = orderBinding.modifiedPlansMatchCount {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        Assertions.assertThat(anyRemovedMatch).isFalse()
        Assertions.assertThat(removedMatchCount).isZero()


        PlanBinding planBinding = PlanBinding.from(getSamplePlan(),
                getCustomField(["foo": "bar"]),
                List.of(getChargeBinding(getSampleCharge(), getCustomField(["foo": "bar"]))),
                ProductBinding.from(getSampleProduct()))

        Order order = getOrderWithLineItem(ActionType.ADD, planBinding)
        order.customFields = getCustomField(["foo": "bar", "royal": "bash"])
        orderBinding = OrderBinding.from(order,
                AccountBinding.from(null, null, null),
                List.of(planBinding),
                null)

        anyAddedMatch = orderBinding.anyAddedPlansMatch {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        Assertions.assertThat(anyAddedMatch).isTrue()
        addedMatchCount = orderBinding.addedPlansMatchCount {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        Assertions.assertThat(addedMatchCount).isEqualTo(1)

        anyModifiedMatch = orderBinding.anyModifiedPlansMatch {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        modifiedMatchCount = orderBinding.modifiedPlansMatchCount {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        Assertions.assertThat(anyModifiedMatch).isFalse()
        Assertions.assertThat(modifiedMatchCount).isEqualTo(0)

        anyRemovedMatch = orderBinding.anyRemovedPlansMatch {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }
        removedMatchCount = orderBinding.removedPlansMatchCount {
            when plan.currency == "USD"
            and plan.customField("foo") == "bar"
        }

        Assertions.assertThat(orderBinding.customField("foo")).isEqualTo("bar")
        Assertions.assertThat(orderBinding.customFieldMatches("royal", "bash")).isTrue()
        Assertions.assertThat(orderBinding.customFieldMatches("royal", "name")).isFalse()
        Assertions.assertThat(anyRemovedMatch).isFalse()
        Assertions.assertThat(removedMatchCount).isEqualTo(0)
    }

    void testActionBasedPlanMatching() {
        PlanBinding planBinding = PlanBinding.from(getSamplePlan(),
                getCustomField(["foo": "bar"]),
                List.of(getChargeBinding(getSampleCharge(), getCustomField(["foo": "bar"]))),
                ProductBinding.from(getSampleProduct()))
        OrderBinding orderBinding = OrderBinding.from(getOrderWithLineItem(ActionType.RENEWAL, planBinding),
                AccountBinding.from(null, null, null),
                List.of(planBinding),
                null)

        boolean renewalMatch = orderBinding.anyRenewedPlansMatch {
            when plan.customFieldMatches("foo", "bar")
        }

        int renewalMatchCount = orderBinding.renewedPlansMatchCount {
            on plan.customFieldMatches("foo", "bar")
        }

        Assertions.assertThat(renewalMatch).isTrue()
        Assertions.assertThat(renewalMatchCount).isEqualTo(1)

        boolean addedMatch = orderBinding.anyAddedPlansMatch {
            when plan.customFieldMatches("foo", "bar")
        }

        int addedMatchCount = orderBinding.addedPlansMatchCount {
            on plan.customFieldMatches("foo", "bar")
        }

        Assertions.assertThat(addedMatch).isFalse()
        Assertions.assertThat(addedMatchCount).isZero()

        orderBinding = OrderBinding.from(getOrderWithLineItem(ActionType.RESTRUCTURE, planBinding),
                AccountBinding.from(null, null, null),
                List.of(planBinding),
                null)

        boolean restructureMatch = orderBinding.anyRestructuredPlansMatch {
            when plan.customFieldMatches("foo", "bar")
        }

        int restructureMatchCount = orderBinding.restructuredPlansMatchCount {
            on plan.customFieldMatches("foo", "bar")
        }

        Assertions.assertThat(restructureMatch).isTrue()
        Assertions.assertThat(restructureMatchCount).isEqualTo(1)
    }

    static Order getOrderWithDuration(int days) {
        Order order = new Order()
        order.setStartDate(TEST_START_DATE.toInstant())
        order.setEndDate(TEST_START_DATE.plusDays(days).toInstant())
        return order
    }

    static Order getOrderWithLineItem(ActionType actionType, PlanBinding... bindings) {
        Order order = new Order()
        List<OrderLineItem> testLineItems = []
        for (PlanBinding planBinding : bindings) {
            planBinding.chargeBindingMap().keySet().forEach { chargeId ->
                OrderLineItem orderLineItem = new OrderLineItem()
                orderLineItem.id = UUID.randomUUID()
                orderLineItem.action = actionType
                orderLineItem.planId = planBinding.id
                orderLineItem.chargeId = chargeId
                testLineItems.add(orderLineItem)
            }
        }
        order.lineItemsNetEffect = testLineItems
        return order
    }

    static Opportunity makeOpportunity() {
        Opportunity opportunity = new Opportunity()
        opportunity.setName("Test")
        opportunity.setType("Sample")
        opportunity.setOpportunityCrmType(OpportunityCrmType.HUBSPOT)
        opportunity.setCustomFields(getCustomField(["foo": "bar", "crooked": "creek"]))
        opportunity
    }

    static OrderLineItem getOrderLineItem(CustomField customField) {
        OrderLineItem orderLineItem = new OrderLineItem()
        orderLineItem.id = UUID.randomUUID()
        orderLineItem.customFields = customField.entries.values().collect {
            CustomFieldEntry.fromValue(RandomStringUtils.randomAlphanumeric(7), it)
        }
        return orderLineItem
    }

    static ChargeBinding getChargeBinding(Charge charge, CustomField chargeCustomFields) {
        ChargeBinding.from(charge, chargeCustomFields)
    }

    static CustomField getCustomField(Map<String, String> customFields) {
        new CustomField(customFields.entrySet().collectEntries {
            [
                it.key,
                new CustomFieldValue(CustomFieldType.STRING, it.key, it.key, it.value, null, null, false, CustomFieldSource.USER, null)
            ]
        })
    }

    static CustomField getCustomFieldList(Map<String, List<String>> customFields) {
        new CustomField(customFields.entrySet().collectEntries {
            [
                it.key,
                new CustomFieldValue(CustomFieldType.STRING, it.key, it.key, null, it.value, null, false, CustomFieldSource.USER, null)
            ]
        })
    }

    static Opportunity makeTestOpportunity() {
        Opportunity opportunity = new Opportunity()
        opportunity.setName("Test")
        opportunity.setOpportunityCrmType(OpportunityCrmType.HUBSPOT)
        opportunity.setType("some_type")
        return opportunity
    }
}
