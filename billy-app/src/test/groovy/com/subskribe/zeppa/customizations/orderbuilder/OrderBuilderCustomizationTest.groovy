package com.subskribe.zeppa.customizations.orderbuilder

import static com.subskribe.zeppa.customizations.selection.SelectionCustomizationTest.getTestAccount

import com.subskribe.billy.account.model.Account
import com.subskribe.billy.currency.SupportedCurrency
import com.subskribe.billy.customfield.model.CustomField
import com.subskribe.billy.customfield.model.CustomFieldSource
import com.subskribe.billy.customfield.model.CustomFieldType
import com.subskribe.billy.customfield.model.CustomFieldValue
import com.subskribe.billy.exception.InvalidInputException
import com.subskribe.billy.invoice.model.PaymentTerm
import com.subskribe.billy.order.model.Order
import com.subskribe.billy.order.quotebuilder.model.Answer
import com.subskribe.billy.order.quotebuilder.model.ImmutableAnswer
import com.subskribe.billy.shared.enums.BillingTerm
import com.subskribe.billy.shared.enums.Cycle
import com.subskribe.billy.shared.enums.OrderType
import com.subskribe.billy.shared.temporal.DateTimeConverter
import com.subskribe.zeppa.customizations.bindings.AccountBinding
import com.subskribe.zeppa.customizations.core.ChargeShape
import com.subskribe.zeppa.customizations.core.PlanShape
import com.subskribe.zeppa.customizations.core.ZeppaDriver
import com.subskribe.zeppa.customizations.output.OrderBuilderOutput
import groovy.test.GroovyTestCase
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.stream.Collectors
import org.assertj.core.api.Assertions
import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils

class OrderBuilderCustomizationTest extends GroovyTestCase {

    private static final TimeZone TEST_TIME_ZONE = TimeZone.getTimeZone("America/Los_Angeles")
    private static final String TEST_START_DATE_EPOCH = "**********"
    private static final ZonedDateTime TEST_START_DATE = ZonedDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(TEST_START_DATE_EPOCH)), TEST_TIME_ZONE.toZoneId())

    private static List<Answer> TEST_ANSWERS = List.of(
    ImmutableAnswer.builder()
    .questionId("question_one")
    .answer(List.of("A", "B", "C"))
    .skipped(false)
    .build(),
    ImmutableAnswer.builder()
    .questionId("question_two")
    .answer(List.of("X"))
    .skipped(false)
    .build()
    )

    private static List<Answer> TEST_ANSWERS_DATE_ONE = List.of(
    ImmutableAnswer.builder()
    .questionId("question_date")
    .answer(List.of("A", "B"))
    .skipped(false)
    .build()
    )

    private static List<Answer> TEST_ANSWERS_DATE_TWO = List.of(
    ImmutableAnswer.builder()
    .questionId("question_date_two")
    .answer(List.of("X"))
    .skipped(false)
    .build()
    )

    private static List<Answer> TEST_ANSWER_START_DATE_GOOD = List.of(
    ImmutableAnswer.builder()
    .questionId("start_date")
    // good date
    .answer(List.of(TEST_START_DATE_EPOCH))
    .skipped(false)
    .build()
    )

    private static List<Answer> TEST_ANSWER_START_DATE_BAD = List.of(
    ImmutableAnswer.builder()
    .questionId("start_date")
    // bad date
    .answer(List.of("foo"))
    .skipped(false)
    .build()
    )

    private static String BUILDER_SCRIPT_TWIDDLE_DEFAULTS = """
orderBuilder {
    withDefaults
    withDurationMonths 18
    withMonthlyBilling
    withStartDate Q("start_date").asDate()
   
    when(Q("question_one").matches("A")) {
        withDurationYears 5
    }
    
    when(Q("question_two").matches("X")) {
        withQuarterlyBilling
        withPaymentDueIn 60
    }
}
"""

    private static String BUILDER_SCRIPT_TWIDDLE_DEFAULT_DATE = """
orderBuilder {
    withDefaults
    withDurationMonths 18
    withMonthlyBilling
   
    when(Q("question_date").matches("A")) {
        withTomorrowStartDate
    }
    
    when(Q("question_date_two").matches("X")) {
        withFirstDayOfNextMonthStartDate
    }
}
"""
    private static String BUILDER_SCRIPT_WITH_START_DATE = """
orderBuilder {
    withDefaults
    withDurationMonths 18
    withMonthlyBilling
    withStartDate Q("start_date").asDate()
}
"""

    private static String BUILDER_SCRIPT_QUESTIONS = """
orderBuilder {
    // this is defaults for the order 
    withDefaults
    
    customField("Foo", "Foo-Bar")
    customField "Bar", ["Bar-Baz", "Bar-Boo"]
    
    // this question does not exist so should not match
    when(Q("does_not_exist").matches("A", "B", "C")) {
        addPlan "Plan-foo"
        addPlan "Plan-bar"
        addPlan "Plan-baz"
    }
    
    // this should also match
    when(Q("question_one").matches("A", "B") && Bool.not(Q("question_two").matchesOneOf("Y", "Z"))) {
        addPlan "Plan-A"
        addPlan "Plan-B", {
            charge "SimpleCharge", {
                quantity Q("question_one").whenMatchesOrElse("A", { 10 }, { 20 })
            }
        } 
    }
    
    // this should match since two has answer "X" 
    when(Q("question_two").matchesOneOf("X", "Y", "Z")) {
        addPlan "Plan-X", {
            charge("Recurring", 3) {
                // the quantity should be 20 since question_one does not match M 
                quantity Q("question_one").whenMatchesOrElse("M", { 10 }, { 20 })
                discountPercent 5
                customField("Foo", "Foo-Bar")
                customField "Bar", ["Bar-Baz", "Bar-Boo", 2]
                customField "Donkey", Q("question_two").answerAsString()
                customField "Doo", Q("question_two").answerAsStringList()
            }
        }
    }
}
"""

    private static String BUILDER_SCRIPT =
    """
orderBuilder {
    // assume reasonable defaults for the order
    withDefaults
    when(account.name == "TestAccount") {
        
        // all charges for planId-x will be added
        // the recurring charge quantity will beset to 10 and discount to be 5 percent
        addPlan("planId-x") {
           charge "Recurring", {
              quantity 10
              discountPercent 5
           }
        }
        
        // planId-y with all its charges and default quantity will be added
        addPlan "planId-y"
        
        // this plan will be added only when account custom field matches "foo-no"
        when(account.customFieldMatches("foo-bar", "foo-no")) {
           addPlan "planId-z", {
                charge "Recurring", {
                    quantity 10
                    discountPercent 5
                }
                charge "FlatFee", {
                    quantity 50
                } 
            }
        }
    }
    
    when(account.name == "Foo") {
       addPlan "planId-foo", {
           charge "Recurring", {
               quantity 10   
           }   
       }
    }
}
    """

    void test_whenOrderBuilderCustomizationIsNotCalled_DriverErrors() {
        AccountBinding accountBinding = AccountBinding.from(getTestAccount(), null, null)
        Assertions.assertThatThrownBy {
            ZeppaDriver.compileOrderBuilderCustomization(accountBinding, null, null, "1 == 2")
        }.isInstanceOf(InvalidInputException.class)
        .hasMessageContaining("did not call entrypoint: orderBuilder")
    }

    void test_whenOrderBuilderIsCalled_thenOrderBuilderWorksAsExpected() {
        Account testAccount = getTestAccount()
        testAccount.setAccountId(RandomStringUtils.randomAlphanumeric(10))
        AccountBinding accountBinding = AccountBinding.from(testAccount, null, getAccountCustomField())
        OrderBuilderOutput output = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, null, TEST_TIME_ZONE, BUILDER_SCRIPT)

        def internalBinding = output.internalBinding
        Order sourceOrder = internalBinding.sourceOrder()

        verifyOrderDefaults(sourceOrder, testAccount, TEST_TIME_ZONE)

        // verify plan additions
        List<PlanShape> plansInvolved = output.planShapes
        Assertions.assertThat(plansInvolved).isNotEmpty()

        // when(account.name == "TestAccount") is true
        // when(account.customFieldMatches("foo-bar", "foo-no")) is false
        // when(account.name == "Foo") is false
        // plans shapes should contain planId-x and planId-y only
        Assertions.assertThat(plansInvolved.size()).isEqualTo(2)

        Assertions.assertThat(plansInvolved.stream().map { it.planIdentifier }.collect(Collectors.toSet())).contains("planId-x", "planId-y")
        Assertions.assertThat(plansInvolved[0].chargeShapes).isNotEmpty()
        Assertions.assertThat(plansInvolved[0].chargeShapes["Recurring"]).isNotNull()
        ChargeShape recurringChargeShape = plansInvolved[0].chargeShapes["Recurring"]
        Assertions.assertThat(recurringChargeShape.quantity).isEqualTo(10)
        Assertions.assertThat(recurringChargeShape.discountPercent).isEqualTo(new BigDecimal("5"))

        Assertions.assertThat(plansInvolved[1].chargeShapes).isEmpty()
    }

    void test_whenQuestionsAreUsedInScript_thenOrderBuilderWorksAsExpected() {
        Account testAccount = getTestAccount()
        testAccount.setAccountId(RandomStringUtils.randomAlphanumeric(10))
        AccountBinding accountBinding = AccountBinding.from(testAccount, null, getAccountCustomField())
        OrderBuilderOutput output = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, TEST_ANSWERS, TEST_TIME_ZONE, BUILDER_SCRIPT_QUESTIONS)

        def internalBinding = output.internalBinding
        def sourceOrder = output.internalBinding.sourceOrder()
        verifyOrderDefaults(sourceOrder, testAccount, TEST_TIME_ZONE)

        Assertions.assertThat(internalBinding.getOrderCustomFields()).isNotEmpty()
        Assertions.assertThat(internalBinding.getOrderCustomFields().keySet()).contains("Foo", "Bar")
        Assertions.assertThat(internalBinding.getOrderCustomFields().get("Foo")).isInstanceOf(String.class)
        Assertions.assertThat(internalBinding.getOrderCustomFields().get("Foo") as String).isEqualTo("Foo-Bar")

        Assertions.assertThat(internalBinding.getOrderCustomFields().get("Bar")).isInstanceOf(List.class)
        Assertions.assertThat(internalBinding.getOrderCustomFields().get("Bar") as List).hasSize(2)
        Assertions.assertThat(internalBinding.getOrderCustomFields().get("Bar") as List).contains("Bar-Baz", "Bar-Boo")

        List<PlanShape> plansInvolved = output.planShapes
        Assertions.assertThat(plansInvolved).isNotEmpty()

        Set<String> planIdentifierSet = plansInvolved.stream().map { it.planIdentifier }.collect(Collectors.toSet())

        // when(Q("does_not_exist").matches("A", "B", "C")) is false
        Assertions.assertThat(planIdentifierSet).doesNotContain("Plan-foo", "Plan-bar", "Plan-baz")

        // when(Q("question_one").matches("A", "B") && Bool.not(Q("question_two").matchesOneOf("Y", "Z")))
        // when(Q("question_two").matchesOneOf("X", "Y", "Z"))
        // the above two conditions is true
        Assertions.assertThat(planIdentifierSet).contains("Plan-A", "Plan-B", "Plan-X")

        PlanShape planShape = plansInvolved.findAll {
            it.planIdentifier == "Plan-B"
        }.first()
        Assertions.assertThat(planShape).isNotNull()
        Assertions.assertThat(planShape.chargeShapes).isNotEmpty()
        Assertions.assertThat(planShape.chargeShapes).containsKey("SimpleCharge")
        Assertions.assertThat(planShape.chargeShapes.get("SimpleCharge").quantity).isEqualTo(10)

        planShape = plansInvolved.findAll {
            it.planIdentifier == "Plan-X"
        }.first()
        Assertions.assertThat(planShape).isNotNull()
        Assertions.assertThat(planShape.chargeShapes).isNotEmpty()
        Assertions.assertThat(planShape.chargeShapes).containsKey("Recurring")
        Assertions.assertThat(planShape.chargeShapes.get("Recurring").quantity).isEqualTo(20)
        Assertions.assertThat(planShape.chargeShapes.get("Recurring").duplicates).isEqualTo(3)

        ChargeShape recurringChargeShape = planShape.chargeShapes.get("Recurring")
        Assertions.assertThat(recurringChargeShape.getLineCustomFields()).isNotEmpty()
        Assertions.assertThat(recurringChargeShape.getLineCustomFields().keySet()).contains("Foo", "Bar")
        Assertions.assertThat(recurringChargeShape.getLineCustomFields().get("Foo")).isInstanceOf(String.class)
        Assertions.assertThat(recurringChargeShape.getLineCustomFields().get("Foo") as String).isEqualTo("Foo-Bar")

        Assertions.assertThat(recurringChargeShape.getLineCustomFields().get("Bar")).isInstanceOf(List.class)
        Assertions.assertThat(recurringChargeShape.getLineCustomFields().get("Bar") as List).hasSize(3)
        Assertions.assertThat(recurringChargeShape.getLineCustomFields().get("Bar") as List).contains("Bar-Baz", "Bar-Boo", 2)

        Assertions.assertThat(recurringChargeShape.getLineCustomFields().get("Donkey")).isInstanceOf(String.class)
        Assertions.assertThat(recurringChargeShape.getLineCustomFields().get("Donkey") as String).isEqualTo("X")

        Assertions.assertThat(recurringChargeShape.getLineCustomFields().get("Doo")).isInstanceOf(List.class)
        Assertions.assertThat(recurringChargeShape.getLineCustomFields().get("Doo") as List).hasSize(1)
        Assertions.assertThat(recurringChargeShape.getLineCustomFields().get("Doo") as List).isEqualTo(["X"])
    }

    void test_OrderDefaultsOverrideWorksAsExpected() {
        Account testAccount = getTestAccount()
        testAccount.setCurrency(Currency.getInstance("USD"))
        testAccount.setAccountId(RandomStringUtils.randomAlphanumeric(10))
        AccountBinding accountBinding = AccountBinding.from(testAccount, null, getAccountCustomField())
        OrderBuilderOutput outputWithoutAnswers = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, null, TEST_TIME_ZONE, BUILDER_SCRIPT_TWIDDLE_DEFAULTS)

        def sourceOrder = outputWithoutAnswers.internalBinding.sourceOrder()
        Assertions.assertThat(sourceOrder.getStartDate()).isNotNull()
        Assertions.assertThat(sourceOrder.getTermLength().cycle).isEqualByComparingTo(Cycle.MONTH)
        Assertions.assertThat(sourceOrder.getTermLength().step).isEqualTo(18)

        OrderBuilderOutput outputWithAnswers = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, TEST_ANSWERS, TEST_TIME_ZONE, BUILDER_SCRIPT_TWIDDLE_DEFAULTS)
        sourceOrder = outputWithAnswers.internalBinding.sourceOrder()
        Assertions.assertThat(sourceOrder.getStartDate()).isNotNull()
        Assertions.assertThat(sourceOrder.getTermLength().cycle).isEqualByComparingTo(Cycle.YEAR)
        Assertions.assertThat(sourceOrder.getTermLength().step).isEqualTo(5)

        Assertions.assertThat(sourceOrder.getBillingCycle()).isNotNull()
        Assertions.assertThat(sourceOrder.getBillingCycle().cycle).isEqualTo(Cycle.QUARTER)
        Assertions.assertThat(sourceOrder.getBillingCycle().step).isEqualTo(1)

        Assertions.assertThat(sourceOrder.getPaymentTerm()).isEqualTo(PaymentTerm.NET60)

        outputWithAnswers = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, TEST_ANSWERS_DATE_ONE, TEST_TIME_ZONE, BUILDER_SCRIPT_TWIDDLE_DEFAULT_DATE)
        sourceOrder = outputWithAnswers.internalBinding.sourceOrder()
        def now = ZonedDateTime.ofInstant(DateTimeConverter.startOfDayToInstant(TEST_TIME_ZONE), TEST_TIME_ZONE.toZoneId())
        Assertions.assertThat(sourceOrder.getStartDate()).isNotNull()
        def tomStart = sourceOrder.getStartDate().atZone(TEST_TIME_ZONE.toZoneId())
        Assertions.assertThat(ChronoUnit.DAYS.between(now, tomStart)).isGreaterThanOrEqualTo(1L)

        outputWithAnswers = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, TEST_ANSWERS_DATE_TWO, TEST_TIME_ZONE, BUILDER_SCRIPT_TWIDDLE_DEFAULT_DATE)
        sourceOrder = outputWithAnswers.internalBinding.sourceOrder()
        now = ZonedDateTime.ofInstant(DateTimeConverter.startOfDayToInstant(TEST_TIME_ZONE), TEST_TIME_ZONE.toZoneId())
        Assertions.assertThat(sourceOrder.getStartDate()).isNotNull()
        def nextMonthStart = sourceOrder.getStartDate().atZone(TEST_TIME_ZONE.toZoneId())
        Assertions.assertThat(nextMonthStart.dayOfMonth).isEqualTo(1)
        Assertions.assertThat(now.plusMonths(1).monthValue).isEqualTo(nextMonthStart.monthValue)

        outputWithAnswers = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, TEST_ANSWER_START_DATE_GOOD, TEST_TIME_ZONE, BUILDER_SCRIPT_WITH_START_DATE)
        sourceOrder = outputWithAnswers.internalBinding.sourceOrder()
        Assertions.assertThat(sourceOrder.startDate).isEqualTo(TEST_START_DATE.toInstant())

        outputWithAnswers = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, TEST_ANSWER_START_DATE_BAD, TEST_TIME_ZONE, BUILDER_SCRIPT_WITH_START_DATE)
        sourceOrder = outputWithAnswers.internalBinding.sourceOrder()
        Assertions.assertThat(sourceOrder.startDate).isNotEqualTo(TEST_START_DATE.toInstant())
    }

    private static String BUILDER_SCRIPT_PRICE_ATTRIBUTE_SELECTION = """
    orderBuilder {
        withDefaults
        withDurationMonths 12
        withMonthlyBilling
        
        addPlan "PlanWithAttributes", {
            charge "ChargeWithAttributes", {
                quantity 5
                priceAttributeSelection {
                    put "color", "blue"
                    put "size", "medium"
                    put "material", Q("question_two").answerAsString()
                }
            }
        }
    }
    """

    void test_PriceAttributeSelection_AddsAttributesToChargeShape() {
        Account testAccount = getTestAccount()
        testAccount.setAccountId(RandomStringUtils.randomAlphanumeric(10))
        AccountBinding accountBinding = AccountBinding.from(testAccount, null, getAccountCustomField())
        OrderBuilderOutput output = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, TEST_ANSWERS,
                TEST_TIME_ZONE, BUILDER_SCRIPT_PRICE_ATTRIBUTE_SELECTION)

        // Verify plan and charge were added
        List<PlanShape> plansInvolved = output.planShapes
        Assertions.assertThat(plansInvolved).isNotEmpty()
        Assertions.assertThat(plansInvolved.size()).isEqualTo(1)
        Assertions.assertThat(plansInvolved[0].planIdentifier).isEqualTo("PlanWithAttributes")

        // Verify charge shape and quantity
        PlanShape planShape = plansInvolved[0]
        Assertions.assertThat(planShape.chargeShapes).isNotEmpty()
        Assertions.assertThat(planShape.chargeShapes).containsKey("ChargeWithAttributes")
        ChargeShape chargeShape = planShape.chargeShapes.get("ChargeWithAttributes")
        Assertions.assertThat(chargeShape.quantity).isEqualTo(5)

        // Verify price attributes were correctly added
        Assertions.assertThat(chargeShape.priceAttributes).isNotEmpty()
        Assertions.assertThat(chargeShape.priceAttributes).hasSize(3)
        Assertions.assertThat(chargeShape.priceAttributes).containsEntry("color", "blue")
        Assertions.assertThat(chargeShape.priceAttributes).containsEntry("size", "medium")
        Assertions.assertThat(chargeShape.priceAttributes).containsEntry("material", "X")
    }

    private static String BUILDER_SCRIPT_WITH_PREDEFINED_TERMS = """
    orderBuilder {
        withDefaults
        withDurationMonths 12
        withMonthlyBilling
        withPredefinedTerms "term1", "term2", ""
    }
    """

    void test_WithPredefinedTerms_AddsValidTermsToOrder() {
        Account testAccount = getTestAccount()
        testAccount.setAccountId(RandomStringUtils.randomAlphanumeric(10))
        AccountBinding accountBinding = AccountBinding.from(testAccount, null, getAccountCustomField())
        OrderBuilderOutput output = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, null, TEST_TIME_ZONE, BUILDER_SCRIPT_WITH_PREDEFINED_TERMS)

        // Verify predefined terms were added correctly
        Set<String> predefinedTerms = output.internalBinding.getPredefinedTermNames()
        Assertions.assertThat(predefinedTerms).isNotNull()
        Assertions.assertThat(predefinedTerms).hasSize(2)

        // Verify term names match expected values
        Assertions.assertThat(predefinedTerms).contains("term1", "term2")

        // Verify empty term was filtered out
        Assertions.assertThat(predefinedTerms).doesNotContain("")
    }

    private static String BUILDER_SCRIPT_WITH_NULL_PREDEFINED_TERM = """
    orderBuilder {
        withDefaults
        withDurationMonths 12
        withMonthlyBilling
        withPredefinedTerms null
    }
    """

    void test_WithPredefinedTerms_HandlesNullTerms() {
        Account testAccount = getTestAccount()
        testAccount.setAccountId(RandomStringUtils.randomAlphanumeric(10))
        AccountBinding accountBinding = AccountBinding.from(testAccount, null, getAccountCustomField())
        OrderBuilderOutput output = ZeppaDriver.fireOrderBuilderCustomization(accountBinding, null, TEST_TIME_ZONE, BUILDER_SCRIPT_WITH_NULL_PREDEFINED_TERM)

        // Verify no predefined terms were added with null input
        Set<String> predefinedTerms = output.internalBinding.getPredefinedTermNames()
        Assertions.assertThat(predefinedTerms).isEmpty()
    }

    private static getAccountCustomField() {
        new CustomField(Map.of(
                "foo-bar",
                new CustomFieldValue(CustomFieldType.STRING,
                "foo-bar",
                "Foo Bar",
                "foo-baz", List.of(), List.of(), false, CustomFieldSource.USER, null),
                "donkey-do",
                new CustomFieldValue(CustomFieldType.STRING,
                "donkey-do",
                "Donkey Doo",
                null,
                List.of("donkey", "do"), List.of(), false, CustomFieldSource.USER, null)))
    }

    private static verifyOrderDefaults(Order sourceOrder, Account testAccount, TimeZone timeZone) {
        Instant startInstant = DateTimeConverter.startOfDayToInstant(timeZone)
        LocalDateTime zoned = LocalDateTime.ofInstant(startInstant, timeZone.toZoneId())
        Assertions.assertThat(sourceOrder.getStartDate()).isNotNull()
        Assertions.assertThat(sourceOrder.getStartDate().atZone(TEST_TIME_ZONE.toZoneId()).getDayOfMonth()).isEqualTo(zoned.getDayOfMonth())
        Assertions.assertThat(sourceOrder.getStartDate().atZone(TEST_TIME_ZONE.toZoneId()).getMonthValue()).isEqualTo(zoned.getMonthValue())
        Assertions.assertThat(sourceOrder.getStartDate().atZone(TEST_TIME_ZONE.toZoneId()).getYear()).isEqualTo(zoned.getYear())
        Assertions.assertThat(sourceOrder.getStartDate().atZone(TEST_TIME_ZONE.toZoneId()).getHour()).isZero()
        Assertions.assertThat(sourceOrder.getStartDate().atZone(TEST_TIME_ZONE.toZoneId()).getMinute()).isZero()
        Assertions.assertThat(sourceOrder.getStartDate().atZone(TEST_TIME_ZONE.toZoneId()).getSecond()).isZero()
        Assertions.assertThat(sourceOrder.getStartDate().atZone(TEST_TIME_ZONE.toZoneId()).getNano()).isZero()
        Assertions.assertThat(sourceOrder.getTermLength().cycle).isEqualByComparingTo(Cycle.YEAR)
        Assertions.assertThat(sourceOrder.getTermLength().step).isEqualTo(1)

        Assertions.assertThat(sourceOrder.getBillingCycle().cycle).isEqualByComparingTo(Cycle.YEAR)
        Assertions.assertThat(sourceOrder.getBillingCycle().step).isEqualTo(1)

        Assertions.assertThat(sourceOrder.getOrderType()).isEqualTo(OrderType.NEW)

        Assertions.assertThat(sourceOrder.getBillingTerm()).isEqualByComparingTo(BillingTerm.UP_FRONT)

        Assertions.assertThat(sourceOrder.getPaymentTerm()).isEqualTo(PaymentTerm.NET30)
        Assertions.assertThat(sourceOrder.getAccountId()).isEqualTo(testAccount.getAccountId())
        Assertions.assertThat(sourceOrder.getAutoRenew()).isTrue()
    }
}
