package com.subskribe.zeppa.customizations.ordercreation

import static com.subskribe.zeppa.customizations.bindings.CatalogBindingTest.getSampleCharge
import static com.subskribe.zeppa.customizations.bindings.CatalogBindingTest.getSamplePlan
import static com.subskribe.zeppa.customizations.bindings.CatalogBindingTest.getSampleProduct
import static com.subskribe.zeppa.customizations.bindings.OrderBindingTest.getChargeBinding
import static com.subskribe.zeppa.customizations.bindings.OrderBindingTest.getCustomFieldList
import static com.subskribe.zeppa.customizations.selection.SelectionCustomizationTest.getTestAccount

import com.google.common.io.Resources
import com.subskribe.billy.account.model.Account
import com.subskribe.billy.account.model.AccountContact
import com.subskribe.billy.exception.InvalidInputException
import com.subskribe.billy.invoice.model.PaymentTerm
import com.subskribe.billy.order.model.Order
import com.subskribe.billy.order.model.OrderLineItem
import com.subskribe.billy.shared.enums.ActionType
import com.subskribe.billy.shared.enums.Cycle
import com.subskribe.billy.shared.enums.OrderStatus
import com.subskribe.billy.shared.enums.OrderType
import com.subskribe.billy.shared.temporal.Recurrence
import com.subskribe.billy.user.model.Role
import com.subskribe.billy.user.model.User
import com.subskribe.zeppa.customizations.bindings.AccountBinding
import com.subskribe.zeppa.customizations.bindings.ChargeBinding
import com.subskribe.zeppa.customizations.bindings.ContactBinding
import com.subskribe.zeppa.customizations.bindings.OrderBinding
import com.subskribe.zeppa.customizations.bindings.OrderBindingTest
import com.subskribe.zeppa.customizations.bindings.PlanBinding
import com.subskribe.zeppa.customizations.bindings.ProductBinding
import com.subskribe.zeppa.customizations.bindings.UserBinding
import com.subskribe.zeppa.customizations.bindings.UserBindingTest
import com.subskribe.zeppa.customizations.core.CustomizationAction
import com.subskribe.zeppa.customizations.core.RuleAction
import com.subskribe.zeppa.customizations.core.RuleProcessed
import com.subskribe.zeppa.customizations.core.ZeppaDriver
import groovy.test.GroovyTestCase
import java.nio.charset.StandardCharsets
import org.assertj.core.api.Assertions
import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils

class OrderCreationCustomizationTest extends GroovyTestCase {

    private static final String TEST_PLAN_ID = "PLAN-12345"

    private static final String TEST_CHARGE_ID = "CHRG-12345"

    private static final String SCRIPT_ONE_PATH = "zeppa/script_one.zeppa"

    private static final String SCRIPT_ONE = Resources.toString(Resources.getResource(SCRIPT_ONE_PATH), StandardCharsets.UTF_8)

    void test_whenOrderCreationCustomizationIsNotCalled_DriverErrors() {
        OrderBinding orderBinding = OrderBinding.from(null,
        AccountBinding.from(getTestAccount(), null, null),
        null,
        null)
        Assertions.assertThatThrownBy {
            // call a valid script but compilation should failed since order creation customization is not called
            ZeppaDriver.compileOrderCreationCustomization(orderBinding, """
            1 == 2
            """, UserBinding.empty())
        }.isInstanceOf(InvalidInputException.class)
        .hasMessageContaining("did not call entrypoint: orderCreationCustomization")
    }

    void testRunningOrderCreationCustomizationWorksAsExpected() {
        Order testOrder = getTestOrder()
        User testUser = UserBindingTest.getTestUser(Role.SALES)
        ChargeBinding chargeBinding = getChargeBinding(getSampleCharge(TEST_CHARGE_ID), getCustomFieldList(["foo": ["bar", "baz", "bee"]]))
        PlanBinding planBinding = PlanBinding.from(
        getSamplePlan(TEST_PLAN_ID),
        getCustomFieldList(["foo": ["bar", "baz", "boo"]]),
        List.of(chargeBinding),
        ProductBinding.from(getSampleProduct()))

        Account accountOne = getTestAccount()
        accountOne.accountId = RandomStringUtils.randomAlphanumeric(10).toUpperCase()
        accountOne.setIsReseller(true)
        AccountContact shippingContact = getAccountContact(accountOne)
        // Set phone number to blank to trigger the phone number validation rule
        shippingContact.setPhoneNumber("")
        AccountBinding accountBindingOne = AccountBinding.from(accountOne, null, null)
        ContactBinding shippingContactBinding = ContactBinding.from(shippingContact, accountBindingOne)

        Account accountTwo = getTestAccount()
        accountTwo.accountId = RandomStringUtils.randomAlphanumeric(10).toUpperCase()
        accountTwo.setIsReseller(true)
        AccountContact billingContact = getAccountContact(accountTwo)
        AccountBinding accountBindingTwo = AccountBinding.from(accountTwo, null, null)
        ContactBinding billingContactBinding = ContactBinding.from(billingContact, accountBindingTwo)

        OrderBinding orderBinding = OrderBinding.from(testOrder,
        accountBindingOne,
        List.of(planBinding),
        OrderBindingTest.PACIFIC_ZONE_ID,
        shippingContactBinding,
        billingContactBinding)

        List<RuleProcessed> rulesProcessed = ZeppaDriver.fireOrderCreationCustomization(orderBinding, SCRIPT_ONE, UserBinding.fromUser(testUser))

        Assertions.assertThat(rulesProcessed).hasSize(25)

        List<RuleProcessed> rulesFired = rulesProcessed.findAll {
            it.fired
        }
        Assertions.assertThat(rulesFired).hasSize(20)

        verifyOrderShouldStartOnSecond(rulesFired, "Orders Should Start on 2nd Day of Month")
        verifyOrderShouldStartOnSecond(rulesFired, "Orders Should Start on 2nd Day of Month Or Expression")

        verifyStartDateToEndDateRule(rulesFired, testOrder)

        verifyOrderCreationFailureOnLineCustomFields(rulesFired)

        verifyOrderExpiresRule(rulesFired)

        verifyTestAccountOrderCreationRule(rulesFired)

        verifyProductBasedDiscountOnLineItem(rulesFired, testOrder)

        verifyCurrencyAttributeOrderCreationFailure(rulesFired)

        verifyRecurringPerUnitRule(rulesFired, testOrder, "Foo Bar Baz Recurring per unit rule")
        verifyRecurringPerUnitRule(rulesFired, testOrder, "Foo Bar Baz Recurring per unit rule boolean expression")

        verifySeveralLineItemMutationsBasedOnConditions(rulesFired, testOrder)

        verifyResellerOrdersNotAllowedRule(rulesFired)

        List<RuleProcessed> rulesNotFired = rulesProcessed.findAll {
            !it.fired
        }
        Assertions.assertThat(rulesNotFired).hasSize(5)
        Assertions.assertThat(rulesNotFired.get(0).actions).isEmpty()

        verifyInverseActionRule(rulesNotFired)

        verifyBillingCycleOrderCreationFailure(rulesFired)
        verifyPaymentTermOrderCreationFailure(rulesFired)

        verifyOrderCreationFailureOnLineQuantity(rulesFired)

        verifyRampOverrideRuleOnOrder(rulesFired)

        verifyRenewalUpliftRule(rulesFired)

        verifyOrderDurationInDaysRule(rulesFired)

        verifyChargeDescriptionExampleRule(rulesFired, testOrder)

        verifyShippingContactPhoneNumberRequiredRule(rulesFired)
    }

    private static void verifyRampOverrideRuleOnOrder(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> orderCreationNotAllowed = rulesFired.findAll {
            it.ruleName == "Price Override On Non 3 Multiples"
        }
        Assertions.assertThat(orderCreationNotAllowed).hasSize(1)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions).hasSize(1)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions.get(0).action).isEqualTo(CustomizationAction.FORCE_UPLIFT_ON_RAMPS)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions.get(0).metadata).isInstanceOf(BigDecimal)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions.get(0).metadata as BigDecimal).isEqualTo("10.25")
    }

    private static void verifyInverseActionRule(List<RuleProcessed> rulesNotFired) {
        List<RuleProcessed> discountTemplateTermsInversionRule = rulesNotFired.findAll {
            it.ruleName == "Discount, Order Template and Terms Rule"
        }

        Assertions.assertThat(discountTemplateTermsInversionRule).hasSize(1)
        Assertions.assertThat(discountTemplateTermsInversionRule.get(0).actions).hasSize(3)

        RuleAction removeTerms = discountTemplateTermsInversionRule.get(0).actions.findAll {
            it.action == CustomizationAction.REMOVE_PREDEFINED_TERM
        }.first()
        Assertions.assertThat(removeTerms.metadata).isInstanceOf(String.class)
        Assertions.assertThat(removeTerms.metadata).isEqualTo("Foo Baz Terms")

        RuleAction removeDiscount = discountTemplateTermsInversionRule.get(0).actions.findAll {
            it.action == CustomizationAction.REMOVE_PREDEFINED_DISCOUNT
        }.first()
        Assertions.assertThat(removeDiscount.metadata).isInstanceOf(String.class)
        Assertions.assertThat(removeDiscount.metadata).isEqualTo("Foo Baz Discount")

        RuleAction removeTemplate = discountTemplateTermsInversionRule.get(0).actions.findAll {
            it.action == CustomizationAction.REMOVE_DOCUMENT_TEMPLATE
        }.first()
        Assertions.assertThat(removeTemplate.metadata).isInstanceOf(String.class)
        Assertions.assertThat(removeTemplate.metadata).isEqualTo("Foo Baz Template")
    }

    private static void verifyOrderShouldStartOnSecond(List<RuleProcessed> rulesFired, String ruleName) {
        List<RuleProcessed> shouldStartOnSecond = rulesFired.findAll {
            it.ruleName == ruleName
        }

        Assertions.assertThat(shouldStartOnSecond).hasSize(1)
        Assertions.assertThat(shouldStartOnSecond.get(0).actions).hasSize(1)

        Assertions.assertThat(shouldStartOnSecond.get(0).actions.get(0).action).isEqualByComparingTo(CustomizationAction.FAIL_ORDER_CREATION_ON_SAVE)
        Assertions.assertThat(shouldStartOnSecond.get(0).actions.get(0).metadata).isInstanceOf(String.class)
        Assertions.assertThat(shouldStartOnSecond.get(0).actions.get(0).metadata as String).contains("start at 2 day of every month not 1")
    }

    private static void verifySeveralLineItemMutationsBasedOnConditions(List<RuleProcessed> rulesFired, Order testOrder) {
        List<RuleProcessed> applyDiscountAndQuantity = rulesFired.findAll {
            it.ruleName == "Several Line Item Mutations Based On Conditions"
        }
        Assertions.assertThat(applyDiscountAndQuantity).hasSize(1)
        Assertions.assertThat(applyDiscountAndQuantity.get(0).actions).hasSize(6)

        RuleAction applyDiscount = applyDiscountAndQuantity.get(0).actions.find {
            action -> action.action == CustomizationAction.APPLY_LINE_ITEM_DISCOUNT
        }
        RuleAction applyQuantity = applyDiscountAndQuantity.get(0).actions.find {
            action -> action.action == CustomizationAction.APPLY_LINE_ITEM_QUANTITY
        }
        RuleAction applyOverride = applyDiscountAndQuantity.get(0).actions.find {
            action -> action.action == CustomizationAction.APPLY_LINE_ITEM_PRICE_OVERRIDE
        }
        RuleAction applyCustomListPrice = applyDiscountAndQuantity.get(0).actions.find {
            action -> action.action == CustomizationAction.APPLY_LINE_ITEM_LIST_PRICE
        }

        List<RuleAction> applyCustomFieldActions = applyDiscountAndQuantity.get(0).actions.findAll {
            action -> action.action == CustomizationAction.APPLY_ORDER_LINE_CUSTOM_FIELD
        }

        Assertions.assertThat(applyDiscount).isNotNull()
        Assertions.assertThat(applyQuantity).isNotNull()
        Assertions.assertThat(applyOverride).isNotNull()
        Assertions.assertThat(applyCustomListPrice).isNotNull()

        Assertions.assertThat(applyDiscount.metadata).isInstanceOf(LineItemDiscount.class)
        LineItemDiscount lineItemDiscount = applyDiscount.metadata as LineItemDiscount
        String orderLineItemId = testOrder.getLineItemsNetEffect().get(0).id.toString()
        Assertions.assertThat(lineItemDiscount.getLineItemId()).isEqualTo(orderLineItemId)
        Assertions.assertThat(lineItemDiscount.getDiscount()).isEqualTo(BigDecimal.valueOf(.1075))

        Assertions.assertThat(applyQuantity.metadata).isInstanceOf(LineItemQuantity.class)
        LineItemQuantity lineItemQuantity = applyQuantity.metadata as LineItemQuantity
        Assertions.assertThat(lineItemQuantity.getLineItemId()).isEqualTo(orderLineItemId)
        Assertions.assertThat(lineItemQuantity.getQuantity()).isEqualTo(2)

        Assertions.assertThat(applyOverride.metadata).isInstanceOf(LineItemPriceOverride.class)
        LineItemPriceOverride priceOverride = applyOverride.metadata as LineItemPriceOverride
        Assertions.assertThat(priceOverride.getLineItemId()).isEqualTo(orderLineItemId)
        Assertions.assertThat(priceOverride.getPriceOverride()).isEqualTo(1.2)

        Assertions.assertThat(applyCustomListPrice.metadata).isInstanceOf(LineItemPriceOverride.class)
        LineItemPriceOverride customListPrice = applyCustomListPrice.metadata as LineItemPriceOverride
        Assertions.assertThat(customListPrice.getLineItemId()).isEqualTo(orderLineItemId)
        Assertions.assertThat(customListPrice.getPriceOverride()).isEqualTo(new BigDecimal(20))

        Assertions.assertThat(applyCustomFieldActions.size()).isEqualTo(2)
        RuleAction actionOne = applyCustomFieldActions[0]
        Assertions.assertThat(actionOne.action).isEqualTo(CustomizationAction.APPLY_ORDER_LINE_CUSTOM_FIELD)
        Assertions.assertThat(actionOne.metadata).isInstanceOf(OrderLineCustomField.class)
        Assertions.assertThat((actionOne.metadata as OrderLineCustomField).identifier).isEqualTo("foo")
        Assertions.assertThat((actionOne.metadata as OrderLineCustomField).values).isEqualTo(List.of("USD"))

        RuleAction actionTwo = applyCustomFieldActions[1]
        Assertions.assertThat(actionTwo.action).isEqualTo(CustomizationAction.APPLY_ORDER_LINE_CUSTOM_FIELD)
        Assertions.assertThat(actionTwo.metadata).isInstanceOf(OrderLineCustomField.class)
        Assertions.assertThat((actionTwo.metadata as OrderLineCustomField).identifier).isEqualTo("bar")
        Assertions.assertThat((actionTwo.metadata as OrderLineCustomField).values).isEqualTo(List.of("baz", "NEW"))
    }

    private static void verifyRecurringPerUnitRule(List<RuleProcessed> rulesFired, Order testOrder, String ruleName) {
        List<RuleProcessed> fooBarBazRule = rulesFired.findAll {
            it.ruleName == ruleName
        }
        Assertions.assertThat(fooBarBazRule).hasSize(1)
        Assertions.assertThat(fooBarBazRule.get(0).actions).hasSize(2)

        RuleAction applyLineItemAction = fooBarBazRule.get(0).actions.findAll {
            it.action == CustomizationAction.APPLY_LINE_ITEM_DISCOUNT
        }.first()

        Assertions.assertThat(applyLineItemAction.action).isEqualTo(CustomizationAction.APPLY_LINE_ITEM_DISCOUNT)
        Assertions.assertThat(applyLineItemAction.metadata).isInstanceOf(LineItemDiscount.class)

        LineItemDiscount lineItemDiscount = applyLineItemAction.metadata as LineItemDiscount
        String orderLineItemId = testOrder.getLineItemsNetEffect().get(0).id.toString()
        Assertions.assertThat(lineItemDiscount.getLineItemId()).isEqualTo(orderLineItemId)
        Assertions.assertThat(lineItemDiscount.getDiscount()).isEqualTo(BigDecimal.valueOf(.1075))

        RuleAction removePredefinedDiscount = fooBarBazRule.get(0).actions.findAll {
            it.action == CustomizationAction.REMOVE_LINE_ITEM_PREDEFINED_DISCOUNT
        }.first()

        Assertions.assertThat(removePredefinedDiscount.action).isEqualTo(CustomizationAction.REMOVE_LINE_ITEM_PREDEFINED_DISCOUNT)
        Assertions.assertThat(removePredefinedDiscount.metadata).isInstanceOf(LineItemPredefinedDiscount.class)

        LineItemPredefinedDiscount lineItemPredefinedDiscount = removePredefinedDiscount.metadata as LineItemPredefinedDiscount
        orderLineItemId = testOrder.getLineItemsNetEffect().get(0).id.toString()
        Assertions.assertThat(lineItemPredefinedDiscount.getLineItemId()).isEqualTo(orderLineItemId)
        Assertions.assertThat(lineItemPredefinedDiscount.getDiscountName()).isEqualTo("Standard Discount")
    }

    private static void verifyCurrencyAttributeOrderCreationFailure(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> orderCreationNotAllowed = rulesFired.findAll {
            it.ruleName == "Fail order creation on Plan Foo"
        }
        Assertions.assertThat(orderCreationNotAllowed).hasSize(1)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions).hasSize(1)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions.get(0).action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions.get(0).metadata as String).contains("Cannot create orders in USD with foo baz")
    }

    private static void verifyResellerOrdersNotAllowedRule(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> orderCreationNotAllowed = rulesFired.findAll {
            it.ruleName == "Reseller Orders Are not Allowed"
        }
        Assertions.assertThat(orderCreationNotAllowed).hasSize(1)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions).hasSize(1)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions.get(0).action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions.get(0).metadata as String).contains("Reseller Orders are not allowed")
    }

    private static void verifyTestAccountOrderCreationRule(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> orderCreationNotAllowed = rulesFired.findAll {
            it.ruleName == "Test Account Order Creation Not Allowed"
        }
        Assertions.assertThat(orderCreationNotAllowed).hasSize(1)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions).hasSize(1)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions.get(0).action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions.get(0).metadata as String).contains("TestAccount found while creating order")
    }

    private static void verifyStartDateToEndDateRule(List<RuleProcessed> rulesFired ,Order testOrder) {
        List<RuleProcessed> orderCreationNotAllowed = rulesFired.findAll {
            it.ruleName == "Start Date To End Date And Custom Field On Specific Charge"
        }
        Assertions.assertThat(orderCreationNotAllowed).hasSize(1)
        Assertions.assertThat(orderCreationNotAllowed.get(0).actions).hasSize(3)
        RuleAction forceDateAction = orderCreationNotAllowed.get(0).actions.find {
            it.action == CustomizationAction.FORCE_START_DATE_TO_END_DATE
        }
        Assertions.assertThat(forceDateAction.action).isEqualTo(CustomizationAction.FORCE_START_DATE_TO_END_DATE)

        Assertions.assertThat(forceDateAction.metadata).isInstanceOf(String.class)
        String lineItemId = forceDateAction.metadata as String

        String orderLineItemId = testOrder.getLineItemsNetEffect().get(0).id.toString()
        Assertions.assertThat(lineItemId).isEqualTo(orderLineItemId)

        List<RuleAction> forceCustomFieldActions = orderCreationNotAllowed.get(0).actions.findAll { action ->
            action.action == CustomizationAction.FORCE_ORDER_LINE_CUSTOM_FIELD
        }

        Assertions.assertThat(forceCustomFieldActions.size()).isEqualTo(2)
        RuleAction actionOne = forceCustomFieldActions[0]
        Assertions.assertThat(actionOne.action).isEqualTo(CustomizationAction.FORCE_ORDER_LINE_CUSTOM_FIELD)
        Assertions.assertThat(actionOne.metadata).isInstanceOf(OrderLineCustomField.class)
        Assertions.assertThat((actionOne.metadata as OrderLineCustomField).identifier).isEqualTo("foo")
        Assertions.assertThat((actionOne.metadata as OrderLineCustomField).values).isEqualTo(List.of("USD"))

        RuleAction actionTwo = forceCustomFieldActions[1]
        Assertions.assertThat(actionTwo.action).isEqualTo(CustomizationAction.FORCE_ORDER_LINE_CUSTOM_FIELD)
        Assertions.assertThat(actionTwo.metadata).isInstanceOf(OrderLineCustomField.class)
        Assertions.assertThat((actionTwo.metadata as OrderLineCustomField).identifier).isEqualTo("bar")
        Assertions.assertThat((actionTwo.metadata as OrderLineCustomField).values).isEqualTo(List.of("baz", "donkey"))
    }

    private static void verifyOrderCreationFailureOnLineCustomFields(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> customFieldRequired = rulesFired.findAll {
            it.ruleName == "Line Item Custom Field Missing"
        }
        Assertions.assertThat(customFieldRequired).hasSize(1)
        Assertions.assertThat(customFieldRequired.get(0).actions).hasSize(1)
        Assertions.assertThat(customFieldRequired.get(0).actions.get(0).action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)

        Assertions.assertThat(customFieldRequired.get(0).actions.get(0).metadata).isInstanceOf(String.class)
        Assertions.assertThat(customFieldRequired.get(0).actions.get(0).metadata as String).contains("Plan -> foo and Charge -> Super Charge line items need custom field foo")
    }

    private static void verifyOrderExpiresRule(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> customFieldRequired = rulesFired.findAll {
            it.ruleName == "Order Expires Before Starting"
        }
        Assertions.assertThat(customFieldRequired).hasSize(1)
        Assertions.assertThat(customFieldRequired.get(0).actions).hasSize(1)
        Assertions.assertThat(customFieldRequired.get(0).actions.get(0).action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION_ON_SAVE)

        Assertions.assertThat(customFieldRequired.get(0).actions.get(0).metadata).isInstanceOf(String.class)
        Assertions.assertThat(customFieldRequired.get(0).actions.get(0).metadata as String).contains("Order cannot expire after the start date")
    }

    protected static void verifyProductBasedDiscountOnLineItem(List<RuleProcessed> rulesFired, Order testOrder) {
        List<RuleProcessed> discountRule = rulesFired.findAll {
            it.ruleName == "Product Based Discount On Line Item"
        }
        Assertions.assertThat(discountRule).hasSize(1)
        Assertions.assertThat(discountRule.get(0).actions).hasSize(3)

        RuleAction applyDiscount = discountRule.get(0).actions.find {
            action -> action.action == CustomizationAction.APPLY_LINE_ITEM_DISCOUNT
        }
        Assertions.assertThat(applyDiscount).isNotNull()

        Assertions.assertThat(applyDiscount.metadata).isInstanceOf(LineItemDiscount.class)
        LineItemDiscount lineItemDiscount = applyDiscount.metadata as LineItemDiscount
        String orderLineItemId = testOrder.getLineItemsNetEffect().get(0).id.toString()
        Assertions.assertThat(lineItemDiscount.getLineItemId()).isEqualTo(orderLineItemId)
        Assertions.assertThat(lineItemDiscount.getDiscount()).isEqualTo(BigDecimal.valueOf(.20))

        RuleAction forcePriceOverride = discountRule.get(0).actions.find {
            action -> action.action == CustomizationAction.FORCE_LINE_ITEM_PRICE_OVERRIDE
        }
        Assertions.assertThat(forcePriceOverride).isNotNull()

        Assertions.assertThat(forcePriceOverride.metadata).isInstanceOf(LineItemPriceOverride.class)
        LineItemPriceOverride lineItemPriceOverride = forcePriceOverride.metadata as LineItemPriceOverride
        Assertions.assertThat(lineItemPriceOverride.getLineItemId()).isEqualTo(orderLineItemId)
        Assertions.assertThat(lineItemPriceOverride.getPriceOverride()).isEqualTo(BigDecimal.valueOf(1.32))

        RuleAction forceListPriceAction = discountRule.get(0).actions.find {
            action -> action.action == CustomizationAction.FORCE_LINE_ITEM_LIST_PRICE
        }
        Assertions.assertThat(forcePriceOverride).isNotNull()

        Assertions.assertThat(forceListPriceAction.metadata).isInstanceOf(LineItemPriceOverride.class)
        LineItemPriceOverride priceOverrideMeta = forceListPriceAction.metadata as LineItemPriceOverride
        Assertions.assertThat(priceOverrideMeta.getLineItemId()).isEqualTo(orderLineItemId)
        Assertions.assertThat(priceOverrideMeta.getPriceOverride()).isEqualTo(BigDecimal.valueOf(10.25))
    }

    private static void verifyOrderCreationFailureOnLineQuantity(List<RuleProcessed> rulesFired) {
        List<RuleProcessed> orderLineQuantityMultipleOf5 = rulesFired.findAll {
            it.ruleName == "Quantity multiple of 5"
        }

        Assertions.assertThat(orderLineQuantityMultipleOf5).hasSize(0)

        List<RuleProcessed> orderLineQuantityMultipleOf3 = rulesFired.findAll {
            it.ruleName == "Quantity multiple of 3"
        }

        Assertions.assertThat(orderLineQuantityMultipleOf3).hasSize(1)
        Assertions.assertThat(orderLineQuantityMultipleOf3.get(0).actions).hasSize(1)
        Assertions.assertThat(orderLineQuantityMultipleOf3.get(0).actions.get(0).action).isEqualTo(CustomizationAction.FAIL_ORDER_CREATION)

        Assertions.assertThat(orderLineQuantityMultipleOf3.get(0).actions.get(0).metadata).isInstanceOf(String.class)
        Assertions.assertThat(orderLineQuantityMultipleOf3.get(0).actions.get(0).metadata as String).contains("Item quantity should be multiple of 3")
    }

    private static void verifyBillingCycleOrderCreationFailure(List<RuleProcessed> rulesFired) {
        RuleProcessed orderCreationNotAllowed = getSingleRuleProcessed(rulesFired, "Billing Cycle should be yearly")
        RuleAction action = getSingleRuleAction(orderCreationNotAllowed, CustomizationAction.FAIL_ORDER_CREATION_ON_SAVE)
        Assertions.assertThat(action.metadata as String).contains("New orders should have yearly billing")
    }

    private static void verifyPaymentTermOrderCreationFailure(List<RuleProcessed> rulesFired) {
        RuleProcessed orderCreationNotAllowed = getSingleRuleProcessed(rulesFired, "Payment term should be less than NET30")
        RuleAction action = getSingleRuleAction(orderCreationNotAllowed, CustomizationAction.FAIL_ORDER_CREATION_ON_SAVE)
        Assertions.assertThat(action.metadata as String).contains("<= NET 30")
    }

    static void verifyRenewalUpliftRule(List<RuleProcessed> rulesFired) {
        RuleProcessed renewalUpliftRule = getSingleRuleProcessed(rulesFired, "Force renewal uplift")
        RuleAction forceRenewalUpliftAction = getSingleRuleAction(renewalUpliftRule, CustomizationAction.FORCE_RENEWAL_UPLIFT)

        Assertions.assertThat(forceRenewalUpliftAction.metadata).isInstanceOf(LineItemRenewalUplift.class)
        LineItemRenewalUplift lineItemRenewalUplift = forceRenewalUpliftAction.metadata as LineItemRenewalUplift
        Assertions.assertThat(lineItemRenewalUplift.upliftRatio).isEqualTo(1.1)
    }

    static void verifyOrderDurationInDaysRule(List<RuleProcessed> rulesFired) {
        RuleProcessed rule = getSingleRuleProcessed(rulesFired, "Order duration")
        RuleAction action = getSingleRuleAction(rule, CustomizationAction.FAIL_ORDER_CREATION)
        Assertions.assertThat(action.metadata as String).contains("Order must be less than 300 days in duration")
    }

    static Order getTestOrder() {
        Order order = new Order()
        order.id = UUID.randomUUID()
        order.currency = Currency.getInstance("USD")
        order.orderType = OrderType.NEW
        order.billingCycle = new Recurrence(Cycle.QUARTER, 1)
        order.paymentTerm = PaymentTerm.NET60
        order.status = OrderStatus.DRAFT
        order.startDate = OrderBindingTest.TEST_START_DATE.toInstant()
        order.endDate = OrderBindingTest.TEST_START_DATE.plusMonths(12).toInstant()

        order.expiresOn = OrderBindingTest.TEST_START_DATE.plusMonths(1).toInstant()

        OrderLineItem orderLineItem = new OrderLineItem()
        orderLineItem.id = UUID.randomUUID()
        orderLineItem.planId = TEST_PLAN_ID
        orderLineItem.chargeId = TEST_CHARGE_ID
        orderLineItem.action = ActionType.ADD
        orderLineItem.quantity = 10
        order.lineItemsNetEffect = List.of(orderLineItem)
        return order
    }

    static AccountContact getAccountContact(Account account) {
        AccountContact accountContact = new AccountContact()
        accountContact.setContactId(RandomStringUtils.randomAlphanumeric(7).toUpperCase())
        accountContact.setAccountId(account.getAccountId())
        return accountContact
    }

    private static RuleProcessed getSingleRuleProcessed(List<RuleProcessed> rulesFired, String ruleName) {
        List<RuleProcessed> rules = rulesFired.findAll {
            it.ruleName == ruleName
        }

        Assertions.assertThat(rules).hasSize(1)
        return rules.get(0)
    }

    private static RuleAction getSingleRuleAction(RuleProcessed rule, CustomizationAction action) {
        Assertions.assertThat(rule.actions).hasSize(1)
        Assertions.assertThat(rule.actions.get(0).action).isEqualTo(action)
        return rule.actions.get(0)
    }

    static void verifyChargeDescriptionExampleRule(List<RuleProcessed> rulesFired, Order testOrder) {
        RuleProcessed rule = getSingleRuleProcessed(rulesFired, "Charge description example")
        RuleAction action = getSingleRuleAction(rule, CustomizationAction.ADD_PREDEFINED_DISCOUNT)

        Assertions.assertThat(action.metadata).isInstanceOf(String.class)
        String discountName = action.metadata as String
        Assertions.assertThat(discountName).isEqualTo("Awesome Discount")
    }

    static void verifyShippingContactPhoneNumberRequiredRule(List<RuleProcessed> rulesFired) {
        RuleProcessed rule = getSingleRuleProcessed(rulesFired, "Shipping Contact Phone Number Required")
        RuleAction action = getSingleRuleAction(rule, CustomizationAction.FAIL_ORDER_CREATION_ON_SAVE)

        Assertions.assertThat(action.metadata).isInstanceOf(String.class)
        Assertions.assertThat(action.metadata as String).contains("Shipping contact phone number is required for order creation")
    }
}
