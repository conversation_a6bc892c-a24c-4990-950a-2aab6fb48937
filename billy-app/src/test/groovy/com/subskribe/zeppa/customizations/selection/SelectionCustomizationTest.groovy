package com.subskribe.zeppa.customizations.selection

import com.subskribe.billy.account.model.Account
import com.subskribe.billy.exception.InvalidInputException
import com.subskribe.billy.opportunity.model.Opportunity
import com.subskribe.billy.user.model.Role
import com.subskribe.billy.user.model.User
import com.subskribe.zeppa.customizations.bindings.AccountBinding
import com.subskribe.zeppa.customizations.bindings.OpportunityBinding
import com.subskribe.zeppa.customizations.bindings.OpportunityBindingTest
import com.subskribe.zeppa.customizations.bindings.UserBinding
import com.subskribe.zeppa.customizations.core.CustomizationAction
import com.subskribe.zeppa.customizations.core.RuleProcessed
import com.subskribe.zeppa.customizations.core.ZeppaDriver
import groovy.test.GroovyTestCase
import org.assertj.core.api.Assertions
import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils

class SelectionCustomizationTest extends GroovyTestCase {

    private static String SCRIPT = """
    selectionCustomization {
        rule {
            name "Blank Country Number"
            condition {
                when account.country == "" 
                and account.customField("sector") == ""
                also Numbers.toInt("10", -1) > 5
            }  
            userInterfaceActions {
                applyAddPlanSelectionFilter {
                    attrib "product_category", "BlankCountry" 
                }
            }
        }
        
        rule {
            name "UK based filter"
            condition {
               when account.country == "UK"
            } 
            userInterfaceActions {
                applyAddPlanSelectionFilter {
                    attrib "product_category", "UK Based"
                    attrib "product", "UK SKU" 
                }
            }
        }
        
        rule {
            name "Name Based Filter"
            condition {
               when account.name == "FooBar"
               or account.name in ["TestAccount", "Some Account", "Other Account"]
            } 
            userInterfaceActions {
                applyAddPlanSelectionFilter {
                    attrib("product_category", "Select Plans")
                }
            }
        }
        
        rule {
            name "Attribute Filter"
            condition {
               when account.name == "TestAccount"
            } 
            userInterfaceActions {
                applyAttributeSelectionFilter {
                    attrib("attribute", "attribute value")
                }
            }
        }
        
        rule {
            name "User Role Based Filter"
            condition {
               when user.role == "ADMIN"
            } 
            userInterfaceActions {
                applyAddPlanSelectionFilter {
                    attrib("product_category", "Admin Products")
                }
            }
        }
        
        rule {
            name "Opportunity Based Rule"
            condition {
               when opportunity.crmType in ["HUBSPOT"]
               and opportunity.customField("donkey-do") in ["donkey"]
            } 
            userInterfaceActions {
                applyAddPlanSelectionFilter {
                    attrib "product_category", opportunity.customField("donkey-do")
                }
            }
        }
    }
"""

    void test_whenSelectionCustomizationIsNotCalled_DriverErrors() {
        AccountBinding accountBinding = AccountBinding.from(getTestAccount(), null, null)
        Assertions.assertThatThrownBy {
            // call a valid script but compilation should failed since plan selection customization is not called
            ZeppaDriver.compileSelectionCustomization(accountBinding, UserBinding.empty(), OpportunityBinding.empty(), """
            1 == 2
            """)
        }.isInstanceOf(InvalidInputException.class)
        .hasMessageContaining("did not call entrypoint: selectionCustomization")
    }

    void test_whenVariablesAreDeclared_DriverErrors() {
        AccountBinding accountBinding = AccountBinding.from(getTestAccount(), null, null)
        Assertions.assertThatThrownBy {
            // declaration of variables is not allowed
            ZeppaDriver.compileSelectionCustomization(accountBinding, UserBinding.empty(), OpportunityBinding.empty(), """
            def x = 10
            selectionCustomization {
            }
            """)
        }.isInstanceOf(InvalidInputException.class)
        .hasMessageContaining("DeclarationExpressions are not allowed")
    }

    void test_importIsUsed_DriverErrors() {
        AccountBinding accountBinding = AccountBinding.from(getTestAccount(), null, null)
        Assertions.assertThatThrownBy {
            // declaration of variables is not allowed
            ZeppaDriver.compileSelectionCustomization(accountBinding, UserBinding.empty(), OpportunityBinding.empty(), """
            import java.lang.System
            selectionCustomization {
            }
            """)
        }.isInstanceOf(InvalidInputException.class)
        .hasMessageContaining("Importing [java.lang.System] is not allowed")
    }

    void test_methodDefinitionIsUsed_DriverErrors() {
        AccountBinding accountBinding = AccountBinding.from(getTestAccount(), null, null)
        Assertions.assertThatThrownBy {
            // declaration of variables is not allowed
            ZeppaDriver.compileSelectionCustomization(accountBinding, UserBinding.empty(), OpportunityBinding.empty(), """
            def someCoolMethod() {
                println("Wow! not allowed")
            }
            selectionCustomization {
            }
            """)
        }.isInstanceOf(InvalidInputException.class)
        .hasMessageContaining("Method definitions are not allowed")
    }

    void testRunningSampleScriptWorksAsExpected() {
        AccountBinding accountBinding = AccountBinding.from(getTestAccount(), null, null)
        UserBinding adminUserBinding = UserBinding.fromUser(getTestUser(Role.ADMIN))
        Opportunity opportunity = OpportunityBindingTest.makeOpportunity()
        OpportunityBinding opportunityBinding = OpportunityBinding.from(opportunity)
        List<RuleProcessed> rulesProcessed = ZeppaDriver.fireSelectionCustomization(accountBinding, adminUserBinding, opportunityBinding, SCRIPT)

        Assertions.assertThat(rulesProcessed).hasSize(6)

        RuleProcessed blankCountryNumber = rulesProcessed.find {
            it.ruleName == "Blank Country Number"
        }
        Assertions.assertThat(blankCountryNumber).isNotNull()

        Assertions.assertThat(blankCountryNumber.fired).isTrue()
        Assertions.assertThat(blankCountryNumber.actions).hasSize(1)

        Assertions.assertThat(blankCountryNumber.actions[0].action).isEqualTo(CustomizationAction.ADD_PLAN_SELECTION_FILTER)
        Assertions.assertThat(blankCountryNumber.actions[0].metadata).isInstanceOf(SelectedAttributes)
        SelectedAttributes selectedAttributes = (SelectedAttributes) blankCountryNumber.actions[0].metadata
        Assertions.assertThat(selectedAttributes.attributeMap).containsKey("product_category")
        Assertions.assertThat(selectedAttributes.attributeMap.get("product_category")).isEqualTo("BlankCountry")

        RuleProcessed ukBasedFilter = rulesProcessed.find {
            it.ruleName == "UK based filter"
        }

        Assertions.assertThat(ukBasedFilter).isNotNull()
        Assertions.assertThat(ukBasedFilter.fired).isFalse()
        Assertions.assertThat(ukBasedFilter.actions).hasSize(1)

        Assertions.assertThat(ukBasedFilter.actions[0].action).isEqualTo(CustomizationAction.ADD_PLAN_SELECTION_FILTER)
        Assertions.assertThat(ukBasedFilter.actions[0].metadata).isInstanceOf(SelectedAttributes)
        selectedAttributes = (SelectedAttributes) ukBasedFilter.actions[0].metadata
        Assertions.assertThat(selectedAttributes.attributeMap).containsKey("product_category")
        Assertions.assertThat(selectedAttributes.attributeMap.get("product_category")).isEqualTo("UK Based")

        Assertions.assertThat(selectedAttributes.attributeMap).containsKey("product")
        Assertions.assertThat(selectedAttributes.attributeMap.get("product")).isEqualTo("UK SKU")

        RuleProcessed nameBasedFilter = rulesProcessed.find {
            it.ruleName == "Name Based Filter"
        }

        Assertions.assertThat(nameBasedFilter).isNotNull()
        Assertions.assertThat(nameBasedFilter.fired).isTrue()
        Assertions.assertThat(nameBasedFilter.actions).hasSize(1)

        Assertions.assertThat(nameBasedFilter.actions[0].action).isEqualTo(CustomizationAction.ADD_PLAN_SELECTION_FILTER)
        Assertions.assertThat(nameBasedFilter.actions[0].metadata).isInstanceOf(SelectedAttributes)
        selectedAttributes = (SelectedAttributes) nameBasedFilter.actions[0].metadata
        Assertions.assertThat(selectedAttributes.attributeMap).containsKey("product_category")
        Assertions.assertThat(selectedAttributes.attributeMap.get("product_category")).isEqualTo("Select Plans")

        RuleProcessed attributeFilter = rulesProcessed.find {
            it.ruleName == "Attribute Filter"
        }

        Assertions.assertThat(attributeFilter).isNotNull()
        Assertions.assertThat(attributeFilter.fired).isTrue()
        Assertions.assertThat(attributeFilter.actions).hasSize(1)

        Assertions.assertThat(attributeFilter.actions[0].action).isEqualTo(CustomizationAction.ATTRIBUTE_SELECTION_FILTER)
        Assertions.assertThat(attributeFilter.actions[0].metadata).isInstanceOf(SelectedAttributes)
        selectedAttributes = (SelectedAttributes) attributeFilter.actions[0].metadata
        Assertions.assertThat(selectedAttributes.attributeMap).containsKey("attribute")
        Assertions.assertThat(selectedAttributes.attributeMap.get("attribute")).isEqualTo("attribute value")


        RuleProcessed roleBasedFilter = rulesProcessed.find {
            it.ruleName == "User Role Based Filter"
        }

        Assertions.assertThat(roleBasedFilter).isNotNull()
        Assertions.assertThat(roleBasedFilter.fired).isTrue()
        Assertions.assertThat(roleBasedFilter.actions).hasSize(1)

        Assertions.assertThat(roleBasedFilter.actions[0].action).isEqualTo(CustomizationAction.ADD_PLAN_SELECTION_FILTER)
        Assertions.assertThat(roleBasedFilter.actions[0].metadata).isInstanceOf(SelectedAttributes)
        selectedAttributes = (SelectedAttributes) roleBasedFilter.actions[0].metadata
        Assertions.assertThat(selectedAttributes.attributeMap).containsKey("product_category")
        Assertions.assertThat(selectedAttributes.attributeMap.get("product_category")).isEqualTo("Admin Products")

        RuleProcessed opportunityBasedRule = rulesProcessed.find {
            it.ruleName == "Opportunity Based Rule"
        }

        Assertions.assertThat(opportunityBasedRule).isNotNull()
        Assertions.assertThat(opportunityBasedRule.fired).isTrue()
        Assertions.assertThat(opportunityBasedRule.actions).hasSize(1)

        Assertions.assertThat(opportunityBasedRule.actions[0].action).isEqualTo(CustomizationAction.ADD_PLAN_SELECTION_FILTER)
        Assertions.assertThat(opportunityBasedRule.actions[0].metadata).isInstanceOf(SelectedAttributes)
        selectedAttributes = (SelectedAttributes) opportunityBasedRule.actions[0].metadata
        Assertions.assertThat(selectedAttributes.attributeMap).containsKey("product_category")
        Assertions.assertThat(selectedAttributes.attributeMap.get("product_category")).isEqualTo("donkey")
    }

    static Account getTestAccount() {
        Account account = new Account()
        account.setName("TestAccount")
        return account
    }

    static User getTestUser(Role role) {
        User user = new User()
        user.userId = "USER-${RandomStringUtils.randomAlphanumeric(6).toUpperCase()}"
        user.email = "${RandomStringUtils.randomAlphanumeric(10)}@test.com"
        user.role = role
        user
    }
}
