package com.subskribe.billy.invoice.number


import spock.lang.Specification
import spock.lang.Unroll

class RandomNumberGeneratorSpec extends Specification {

    private static final RandomNumberGenerator RANDOM_INVOICE_NUMBER_GENERATOR = new RandomNumberGenerator()

    @Unroll
    def "Random invoice number generation of different lengths"(int length) {
        when:
        long number = RANDOM_INVOICE_NUMBER_GENERATOR.generate(length)

        then:
        number.toString().length() <= length

        where:
        length << [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    }
}
