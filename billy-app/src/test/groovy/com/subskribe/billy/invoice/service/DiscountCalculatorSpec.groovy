package com.subskribe.billy.invoice.service

import com.subskribe.billy.configuration.dynamic.FeatureService
import com.subskribe.billy.shared.pecuniary.DiscountDetail
import spock.lang.Specification

class DiscountCalculatorSpec extends Specification {

    private static final int MAX_LIST_AMOUNT = 1000000

    FeatureService featureService = Mock(FeatureService)

    private DiscountCalculator discountCalculator = new DiscountCalculator(featureService)

    def "calculate line discounts with positive amount"() {
        when:
        List<DiscountDetail> discounts = getRandomizedPercentDiscounts(new Random().nextInt(10) + 1)
        BigDecimal listAmount = BigDecimal.valueOf(new Random().nextInt(MAX_LIST_AMOUNT) + 1) // set 1 million as upper bound
        def discountResult = discountCalculator.calculateDiscountAmounts(listAmount, discounts, List.of())

        then:
        discountResult.getTotalDiscountAmount() < listAmount
        discountResult.getTotalDiscountAmount() >= BigDecimal.ZERO
        discountResult.getLineItemDiscountAmounts().every { it >= BigDecimal.ZERO }
    }

    private static List<DiscountDetail> getRandomizedPercentDiscounts(int count) {
        (1..count).collect { it ->
            DiscountDetail discount = new DiscountDetail()
            discount.setPercent(BigDecimal.valueOf(new Random().nextDouble()))
            discount
        }
    }
}
