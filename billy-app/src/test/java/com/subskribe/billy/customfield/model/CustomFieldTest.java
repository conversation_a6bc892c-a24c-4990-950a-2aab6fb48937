package com.subskribe.billy.customfield.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.util.HashMap;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class CustomFieldTest {

    private final List<String> options = List.of("option1", "option2", "option3");

    @Test
    public void picklistValue() {
        CustomFieldValue customFieldValue = new CustomFieldValue(
            CustomFieldType.PICKLIST,
            "string_field",
            "String Field",
            "value",
            null,
            null,
            false,
            null,
            null
        );
        assertNull(customFieldValue.getValue());

        customFieldValue = new CustomFieldValue(
            CustomFieldType.PICKLIST,
            "string_field",
            "String Field",
            "value",
            List.of("selection"),
            List.of("selection"),
            false,
            null,
            null
        );
        assertEquals("selection", customFieldValue.getValue());
    }

    @Test
    public void validCustomFieldValues() throws Exception {
        var customFieldValues = new HashMap<String, CustomFieldValue>();
        customFieldValues.put(
            "id1",
            new CustomFieldValue(CustomFieldType.STRING, "string_field", "String Field", "value", null, null, false, null, null)
        );
        customFieldValues.put(
            "id2",
            new CustomFieldValue(
                CustomFieldType.MULTISELECT_PICKLIST,
                "multiselect_field",
                "Multiselect Field",
                null,
                List.of("option1", "option2"),
                options,
                false,
                null,
                null
            )
        );
        customFieldValues.put(
            "id3",
            new CustomFieldValue(CustomFieldType.PICKLIST, "picklist_field", "Picklist Field", null, List.of("option1"), options, false, null, null)
        );
        var customField = new CustomField(customFieldValues);
        customField.validate();

        // validate custom field should be serializable and back
        byte[] data = JacksonProvider.emptyFieldExcludingMapper().writeValueAsBytes(customField);
        CustomField readBack = JacksonProvider.emptyFieldExcludingMapper().readValue(data, CustomField.class);
        Assertions.assertThat(customField.getEntries().keySet()).isEqualTo(readBack.getEntries().keySet());

        customField
            .getEntries()
            .forEach((key, val) -> {
                CustomFieldValue readBackVal = readBack.getEntries().get(key);
                EqualsBuilder.reflectionEquals(val, readBackVal);
            });
    }

    @Test
    public void missingRequiredStringValue() {
        var customFieldValues = new HashMap<String, CustomFieldValue>();
        customFieldValues.put("id", new CustomFieldValue(CustomFieldType.STRING, "string_field", "String Field", "", null, null, true, null, null));
        var customField = new CustomField(customFieldValues);
        assertThrows(InvalidInputException.class, customField::validate);
    }

    @Test
    public void stringValueTooLong() {
        var customFieldValues = new HashMap<String, CustomFieldValue>();
        customFieldValues.put(
            "id",
            new CustomFieldValue(
                CustomFieldType.STRING,
                "string_field",
                null,
                RandomStringUtils.randomAlphanumeric(CustomFieldDefinition.MAX_VALUE_LENGTH + 1),
                null,
                null,
                false,
                null,
                null
            )
        );
        var customField = new CustomField(customFieldValues);
        assertThrows(InvalidInputException.class, customField::validate);
    }

    @Test
    public void missingRequiredSelections() {
        var customFieldValues = new HashMap<String, CustomFieldValue>();
        customFieldValues.put(
            "id",
            new CustomFieldValue(
                CustomFieldType.MULTISELECT_PICKLIST,
                "multiselect_field",
                "Multiselect Field",
                null,
                List.of(),
                options,
                true,
                null,
                null
            )
        );
        var customField = new CustomField(customFieldValues);
        assertThrows(InvalidInputException.class, customField::validate);
    }

    @Test
    public void invalidSelections() {
        var customFieldValues = new HashMap<String, CustomFieldValue>();
        customFieldValues.put(
            "id",
            new CustomFieldValue(
                CustomFieldType.MULTISELECT_PICKLIST,
                "multiselect_field",
                "Multiselect Field",
                null,
                List.of("option1", "option4"),
                options,
                false,
                null,
                null
            )
        );
        var customField = new CustomField(customFieldValues);
        assertThrows(InvalidInputException.class, customField::validate);
    }

    @Test
    public void tooManySelectionsForPicklist() {
        var customFieldValues = new HashMap<String, CustomFieldValue>();
        customFieldValues.put(
            "id",
            new CustomFieldValue(
                CustomFieldType.PICKLIST,
                "picklist_field",
                "Picklist Field",
                null,
                List.of("option1", "option2"),
                options,
                false,
                null,
                null
            )
        );
        var customField = new CustomField(customFieldValues);
        assertThrows(InvalidInputException.class, customField::validate);
    }

    @Test
    void fromEntries() throws Exception {
        var entries = List.of(
            new CustomFieldEntry("id1", CustomFieldType.STRING, "string_field", "String Field", "value", null, null, false, null, null),
            new CustomFieldEntry(
                "id2",
                CustomFieldType.MULTISELECT_PICKLIST,
                "multiselect_field",
                "Multiselect Field",
                null,
                List.of("option1"),
                options,
                false,
                null,
                null
            ),
            new CustomFieldEntry(
                "id3",
                CustomFieldType.PICKLIST,
                "picklist_field",
                "Picklist Field",
                null,
                List.of("option1"),
                options,
                false,
                null,
                null
            )
        );

        var customField = CustomField.fromEntries(entries);
        customField.validate();

        // validate custom field should be serializable and back
        byte[] data = JacksonProvider.emptyFieldExcludingMapper().writeValueAsBytes(customField);
        CustomField readBack = JacksonProvider.emptyFieldExcludingMapper().readValue(data, CustomField.class);
        Assertions.assertThat(customField.getEntries().keySet()).isEqualTo(readBack.getEntries().keySet());

        customField
            .getEntries()
            .forEach((key, val) -> {
                CustomFieldValue readBackVal = readBack.getEntries().get(key);
                EqualsBuilder.reflectionEquals(val, readBackVal);
            });
    }

    @Test
    void fromEntriesWithEmptyList() {
        var customField = CustomField.fromEntries(List.of());
        Assertions.assertThat(customField.isEmpty()).isTrue();
    }

    @Test
    void fromEntriesWithBlankId() {
        var entries = List.of(
            new CustomFieldEntry("", CustomFieldType.STRING, "string_field", "String Field", "value", null, null, false, null, null)
        );
        InvalidInputException exception = assertThrows(InvalidInputException.class, () -> CustomField.fromEntries(entries));
        Assertions.assertThat(exception.getMessage()).isEqualTo("Custom field id is required");
    }

    @Test
    void fromEntriesWithDuplicateId() {
        var entries = List.of(
            new CustomFieldEntry("id1", CustomFieldType.STRING, "string_field", "String Field", "value", null, null, false, null, null),
            new CustomFieldEntry("id1", CustomFieldType.STRING, "string_field2", "String Field 2", "value2", null, null, false, null, null)
        );
        InvalidInputException exception = assertThrows(InvalidInputException.class, () -> CustomField.fromEntries(entries));
        Assertions.assertThat(exception.getMessage()).isEqualTo("Duplicate custom field id: id1");
    }
}
