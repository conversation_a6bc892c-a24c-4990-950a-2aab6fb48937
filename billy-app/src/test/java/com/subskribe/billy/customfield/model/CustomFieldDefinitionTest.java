package com.subskribe.billy.customfield.model;

import com.subskribe.billy.exception.ModelValidationException;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.time.Instant;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class CustomFieldDefinitionTest {

    @Test
    public void fieldNameCannotContainSpace() {
        var definition = new CustomFieldDefinition(
            null,
            null,
            CustomFieldParentType.ACCOUNT,
            CustomFieldType.STRING,
            "invalid name",
            "Some Label",
            List.of(),
            true,
            null,
            CustomFieldSource.USER,
            Instant.now(),
            Instant.now()
        );
        Assertions.assertThatThrownBy(definition::validate).isInstanceOf(ModelValidationException.class);
    }

    @Test
    public void fieldNameCannotContainDot() {
        var definition = new CustomFieldDefinition(
            null,
            null,
            CustomFieldParentType.ACCOUNT,
            CustomFieldType.STRING,
            "invalid.name",
            "Some label",
            List.of(),
            true,
            null,
            CustomFieldSource.USER,
            Instant.now(),
            Instant.now()
        );
        Assertions.assertThatThrownBy(definition::validate).isInstanceOf(ModelValidationException.class);
    }

    @Test
    public void fieldNameCannotContainBlank() {
        var definition = new CustomFieldDefinition(
            null,
            null,
            CustomFieldParentType.ACCOUNT,
            CustomFieldType.STRING,
            "",
            "",
            List.of(),
            true,
            null,
            CustomFieldSource.USER,
            Instant.now(),
            Instant.now()
        );
        Assertions.assertThatThrownBy(definition::validate).isInstanceOf(ModelValidationException.class);
    }

    @Test
    public void validFieldName() {
        var definition = new CustomFieldDefinition(
            null,
            null,
            CustomFieldParentType.ACCOUNT,
            CustomFieldType.STRING,
            "valid_name",
            "Some Label",
            List.of(),
            true,
            null,
            CustomFieldSource.USER,
            Instant.now(),
            Instant.now()
        );
        definition.validate();
    }

    @Test
    public void fieldTypeCannotBeNull() {
        var definition = new CustomFieldDefinition(
            null,
            null,
            CustomFieldParentType.ACCOUNT,
            null,
            "valid_name",
            "Some Label",
            List.of(),
            true,
            null,
            CustomFieldSource.USER,
            Instant.now(),
            Instant.now()
        );
        Assertions.assertThatThrownBy(definition::validate).isInstanceOf(ModelValidationException.class);
    }

    @Test
    public void parentTypeCannotBeNull() {
        var definition = new CustomFieldDefinition(
            null,
            null,
            null,
            CustomFieldType.STRING,
            "valid_name",
            "Some Label",
            List.of(),
            true,
            null,
            CustomFieldSource.USER,
            Instant.now(),
            Instant.now()
        );
        Assertions.assertThatThrownBy(definition::validate).isInstanceOf(ModelValidationException.class);
    }

    @Test
    public void validPickListDefinition() {
        var definition = new CustomFieldDefinition(
            null,
            null,
            CustomFieldParentType.ACCOUNT,
            CustomFieldType.PICKLIST,
            "valid_name",
            "Some Label",
            List.of("option1"),
            true,
            null,
            CustomFieldSource.USER,
            Instant.now(),
            Instant.now()
        );

        definition.validate();
    }

    @Test
    public void optionsRequiredForPickListTypes() {
        var definition = new CustomFieldDefinition(
            null,
            null,
            CustomFieldParentType.ACCOUNT,
            CustomFieldType.PICKLIST,
            "valid_name",
            "Some Label",
            List.of(),
            true,
            null,
            CustomFieldSource.USER,
            Instant.now(),
            Instant.now()
        );
        Assertions.assertThatThrownBy(definition::validate).isInstanceOf(ModelValidationException.class);

        definition = new CustomFieldDefinition(
            null,
            null,
            CustomFieldParentType.ACCOUNT,
            CustomFieldType.MULTISELECT_PICKLIST,
            "valid_name",
            "Some Label",
            List.of(),
            true,
            null,
            CustomFieldSource.USER,
            Instant.now(),
            Instant.now()
        );
        Assertions.assertThatThrownBy(definition::validate).isInstanceOf(ModelValidationException.class);
    }

    @Test
    public void picklistOptionsMustBeUnique() {
        String option = RandomStringUtils.randomAlphanumeric(10);
        var definition = new CustomFieldDefinition(
            null,
            null,
            CustomFieldParentType.ACCOUNT,
            CustomFieldType.PICKLIST,
            "valid_name",
            "Some Label",
            List.of(option, option),
            true,
            null,
            CustomFieldSource.USER,
            Instant.now(),
            Instant.now()
        );
        Assertions.assertThatThrownBy(definition::validate).isInstanceOf(ModelValidationException.class);
    }

    @Test
    public void customFieldDefinitionSerdeTest() throws Exception {
        List<String> options = List.of(
            RandomStringUtils.randomAlphanumeric(10).toUpperCase(),
            RandomStringUtils.randomAlphanumeric(10).toUpperCase()
        );
        var definition = new CustomFieldDefinition(
            null,
            RandomStringUtils.randomAlphanumeric(10).toUpperCase(),
            CustomFieldParentType.ACCOUNT,
            CustomFieldType.STRING,
            "valid_name",
            "Some Label",
            List.of(),
            true,
            new CustomFieldDefault("default Value", null),
            CustomFieldSource.USER,
            Instant.ofEpochSecond(Instant.now().getEpochSecond()),
            Instant.ofEpochSecond(Instant.now().getEpochSecond())
        );

        byte[] bytes = JacksonProvider.emptyFieldExcludingMapper().writeValueAsBytes(definition);
        CustomFieldDefinition readBack = JacksonProvider.emptyFieldExcludingMapper().readValue(bytes, CustomFieldDefinition.class);
        Assertions.assertThat(EqualsBuilder.reflectionEquals(definition, readBack)).isTrue();

        definition = new CustomFieldDefinition(
            null,
            RandomStringUtils.randomAlphanumeric(10).toUpperCase(),
            CustomFieldParentType.ACCOUNT,
            CustomFieldType.STRING,
            "valid_name",
            "Some Label",
            options,
            true,
            new CustomFieldDefault("default Value", null),
            CustomFieldSource.SYSTEM,
            Instant.ofEpochSecond(Instant.now().getEpochSecond()),
            Instant.ofEpochSecond(Instant.now().getEpochSecond())
        );
        bytes = JacksonProvider.emptyFieldExcludingMapper().writeValueAsBytes(definition);
        readBack = JacksonProvider.emptyFieldExcludingMapper().readValue(bytes, CustomFieldDefinition.class);
        Assertions.assertThat(EqualsBuilder.reflectionEquals(definition, readBack)).isTrue();
    }
}
