package com.subskribe.billy.accounting.journalentry;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.fixture.BalanceContexts;
import com.subskribe.billy.accounting.model.EntryType;
import com.subskribe.billy.accounting.model.FxBalanceContext;
import com.subskribe.billy.accounting.model.JournalEntry;
import com.subskribe.billy.accounting.model.JournalEntryAndBalances;
import com.subskribe.billy.accounting.model.JournalLine;
import com.subskribe.billy.accounting.model.LedgerAccountType;
import com.subskribe.billy.accounting.model.RunningBalanceContext;
import com.subskribe.billy.accounting.model.RunningBalanceType;
import com.subskribe.billy.accounting.services.JournalEntryIdGenerator;
import com.subskribe.billy.accounting.services.JournalLineIdGenerator;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.foreignexchange.service.RealizedGainLossService;
import com.subskribe.billy.invoice.fixture.InvoiceFixture;
import com.subskribe.billy.invoice.fixture.InvoiceItemFixture;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.shared.pecuniary.Numbers;
import java.math.BigDecimal;
import java.time.Clock;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class InvoicePostedFxProcessorTest {

    private static final BigDecimal INVOICE_AMOUNT = new BigDecimal("1000.00");
    private static final BigDecimal INVOICE_EXCHANGE_RATE = new BigDecimal("1.1");
    private static final BigDecimal INVOICE_FX_AMOUNT = Numbers.makeCurrencyScale(INVOICE_AMOUNT.multiply(INVOICE_EXCHANGE_RATE));

    private final JournalEntryIdGenerator journalEntryIdGenerator = mock(JournalEntryIdGenerator.class);
    private final JournalLineIdGenerator journalLineIdGenerator = mock(JournalLineIdGenerator.class);
    private final RealizedGainLossService realizedGainLossService = mock(RealizedGainLossService.class);
    private InvoicePostedFxJournalProcessor invoicePostedFxJournalProcessor;
    private final Clock clock = Clock.systemUTC();

    @BeforeEach
    public void setUp() {
        when(journalEntryIdGenerator.generate()).thenReturn(String.format("JE-%s", RandomStringUtils.randomAlphanumeric(10).toUpperCase()));
        when(journalLineIdGenerator.generate()).thenReturn(String.format("JL-%s", RandomStringUtils.randomAlphanumeric(10).toUpperCase()));
        invoicePostedFxJournalProcessor = new InvoicePostedFxJournalProcessor(
            journalEntryIdGenerator,
            journalLineIdGenerator,
            realizedGainLossService
        );
    }

    @Test
    public void testInvoicePostedWithoutBalances() {
        // prepare invoice and balances
        Entity entity = EntityFixture.getEntityFixtureEur();
        InvoiceItem invoiceItem = InvoiceItemFixture.getDefault().amount(INVOICE_AMOUNT).functionalAmount(INVOICE_FX_AMOUNT).createInvoiceItem();
        Invoice invoice = InvoiceFixture.getDefault()
            .invoiceItems(List.of(invoiceItem))
            .exchangeRate(INVOICE_EXCHANGE_RATE)
            .exchangeRateDate(clock.instant())
            .exchangeRateId(UUID.randomUUID().toString())
            .createInvoice();
        RunningBalanceContext runningBalanceContext = BalanceContexts.initRunningBalanceContext(invoice);
        RunningBalanceContext prevRunningBalanceContext = RunningBalanceContext.copy(runningBalanceContext);
        FxBalanceContext fxBalanceContext = BalanceContexts.initFxBalanceContext(runningBalanceContext);

        // prepare journal entry and lines
        String journalEntryId = journalEntryIdGenerator.generate();
        JournalLine debitLine = createJournalLine(
            EntryType.DEBIT,
            LedgerAccountType.ACCOUNTS_RECEIVABLE,
            INVOICE_AMOUNT,
            journalEntryId,
            invoiceItem
        );
        JournalLine creditLine = createJournalLine(EntryType.CREDIT, LedgerAccountType.DEFERRED_REVENUE, INVOICE_AMOUNT, journalEntryId, invoiceItem);
        JournalEntry journalEntry = createJournalEntry(
            journalEntryId,
            JournalEntry.TransactionType.INVOICE_POSTED,
            List.of(debitLine, creditLine),
            invoiceItem,
            invoice
        );
        var journalEntryAndBalances = JournalEntryAndBalances.builder()
            .journalEntry(journalEntry)
            .balances(runningBalanceContext.getAllBalances())
            .build();

        // execute the fx processor
        JournalEntryAndBalances decoratedJournalEntryAndBalances = invoicePostedFxJournalProcessor.decorateJournalEntry(
            journalEntryAndBalances,
            invoice,
            entity,
            fxBalanceContext,
            prevRunningBalanceContext,
            runningBalanceContext
        );

        // validate the results
        JournalEntry decoratedJournalEntry = decoratedJournalEntryAndBalances.getJournalEntry();
        List<JournalLine> decoratedJournalLines = decoratedJournalEntry.getJournalLines();
        Assertions.assertThat(decoratedJournalLines).hasSize(2);
        JournalLine debitLineDecorated = decoratedJournalLines.get(0);
        JournalLine creditLineDecorated = decoratedJournalLines.get(1);
        Assertions.assertThat(debitLine.getFunctionalAmount()).isNull();
        Assertions.assertThat(debitLineDecorated.getFunctionalAmount()).isEqualTo(INVOICE_FX_AMOUNT);
        Assertions.assertThat(creditLine.getFunctionalAmount()).isNull();
        Assertions.assertThat(creditLineDecorated.getFunctionalAmount()).isEqualTo(INVOICE_FX_AMOUNT);
        assertUnchangedFields(debitLine, debitLineDecorated);
        assertUnchangedFields(creditLine, creditLineDecorated);
    }

    @Test
    public void testInvoicePostedWithBalances() {
        // prepare invoice and balances
        Entity entity = EntityFixture.getEntityFixtureEur();
        InvoiceItem invoiceItem = InvoiceItemFixture.getDefault()
            .amount(INVOICE_AMOUNT)
            .functionalAmount(INVOICE_AMOUNT.multiply(INVOICE_EXCHANGE_RATE))
            .createInvoiceItem();
        Invoice invoice = InvoiceFixture.getDefault()
            .invoiceItems(List.of(invoiceItem))
            .exchangeRate(INVOICE_EXCHANGE_RATE)
            .exchangeRateDate(clock.instant())
            .exchangeRateId(UUID.randomUUID().toString())
            .createInvoice();
        RunningBalanceContext runningBalanceContext = BalanceContexts.initRunningBalanceContext(invoice);
        RunningBalanceContext prevRunningBalanceContext = RunningBalanceContext.copy(runningBalanceContext);
        FxBalanceContext fxBalanceContext = BalanceContexts.initFxBalanceContext(runningBalanceContext);

        BalanceContexts.settleBalance(
            runningBalanceContext,
            RunningBalanceType.DEFERRED_REVENUE,
            fxBalanceContext,
            BalanceContexts.getDeferredRevenueBalance(runningBalanceContext).getId(),
            new BigDecimal("200.00"),
            new BigDecimal("1.2")
        );

        BalanceContexts.settleBalance(
            runningBalanceContext,
            RunningBalanceType.CONTRACT_ASSET,
            fxBalanceContext,
            BalanceContexts.getDeferredRevenueBalance(runningBalanceContext).getId(),
            new BigDecimal("300.00"),
            new BigDecimal("1.3")
        );

        // prepare journal entry and lines
        BigDecimal drAmount = new BigDecimal("400.00");
        BigDecimal caAmount = new BigDecimal("600.00");
        String journalEntryId = journalEntryIdGenerator.generate();
        JournalLine debitLine = createJournalLine(
            EntryType.DEBIT,
            LedgerAccountType.ACCOUNTS_RECEIVABLE,
            INVOICE_AMOUNT,
            journalEntryId,
            invoiceItem
        );
        JournalLine drCreditLine = createJournalLine(EntryType.CREDIT, LedgerAccountType.DEFERRED_REVENUE, drAmount, journalEntryId, invoiceItem);
        JournalLine caCreditLine = createJournalLine(EntryType.CREDIT, LedgerAccountType.DEFERRED_REVENUE, caAmount, journalEntryId, invoiceItem);
        JournalEntry journalEntry = createJournalEntry(
            journalEntryId,
            JournalEntry.TransactionType.INVOICE_POSTED,
            List.of(debitLine, drCreditLine, caCreditLine),
            invoiceItem,
            invoice
        );
        var journalEntryAndBalances = JournalEntryAndBalances.builder()
            .journalEntry(journalEntry)
            .balances(runningBalanceContext.getAllBalances())
            .build();

        // execute the fx processor
        JournalEntryAndBalances decoratedJournalEntryAndBalances = invoicePostedFxJournalProcessor.decorateJournalEntry(
            journalEntryAndBalances,
            invoice,
            entity,
            fxBalanceContext,
            prevRunningBalanceContext,
            runningBalanceContext
        );

        // validate the results
        JournalEntry decoratedJournalEntry = decoratedJournalEntryAndBalances.getJournalEntry();
        List<JournalLine> decoratedJournalLines = decoratedJournalEntry.getJournalLines();
        Assertions.assertThat(decoratedJournalLines).hasSize(3);
        JournalLine debitLineDecorated = decoratedJournalLines.get(0);
        JournalLine drLineDecorated = decoratedJournalLines.get(1);
        JournalLine caLineDecorated = decoratedJournalLines.get(2);
        Assertions.assertThat(debitLine.getFunctionalAmount()).isNull();
        Assertions.assertThat(debitLineDecorated.getFunctionalAmount()).isEqualTo(INVOICE_AMOUNT.multiply(INVOICE_EXCHANGE_RATE));
        Assertions.assertThat(drCreditLine.getFunctionalAmount()).isNull();
        BigDecimal expectedDrFxAmount = Numbers.setScale(drAmount.multiply(INVOICE_EXCHANGE_RATE), 2);
        Assertions.assertThat(drLineDecorated.getFunctionalAmount()).isEqualTo(expectedDrFxAmount);
        BigDecimal expectedCaFxAmount = Numbers.setScale(caAmount.multiply(INVOICE_EXCHANGE_RATE), 2);
        Assertions.assertThat(caLineDecorated.getFunctionalAmount()).isEqualTo(expectedCaFxAmount);
        assertUnchangedFields(debitLine, debitLineDecorated);
        assertUnchangedFields(drCreditLine, drLineDecorated);
        assertUnchangedFields(caCreditLine, caLineDecorated);
    }

    // this is a key test that guards against any accidental changes to journal lines other than the functional amount
    private void assertUnchangedFields(JournalLine debitLine, JournalLine debitLineDecorated) {
        Assertions.assertThat(debitLine.getJournalLineId()).isEqualTo(debitLineDecorated.getJournalLineId());
        Assertions.assertThat(debitLine.getJournalEntryId()).isEqualTo(debitLineDecorated.getJournalEntryId());
        Assertions.assertThat(debitLine.getJournalLineNumber()).isEqualTo(debitLineDecorated.getJournalLineNumber());
        Assertions.assertThat(debitLine.getAmount()).isEqualTo(debitLineDecorated.getAmount());
        Assertions.assertThat(debitLine.getEntryType()).isEqualTo(debitLineDecorated.getEntryType());
        Assertions.assertThat(debitLine.getSourceTransactionLineId()).isEqualTo(debitLineDecorated.getSourceTransactionLineId());
        Assertions.assertThat(debitLine.getPlanId()).isEqualTo(debitLineDecorated.getPlanId());
        Assertions.assertThat(debitLine.getChargeId()).isEqualTo(debitLineDecorated.getChargeId());
        Assertions.assertThat(debitLine.getLedgerAccountType()).isEqualTo(debitLineDecorated.getLedgerAccountType());
        Assertions.assertThat(debitLine.getLedgerAccountId()).isEqualTo(debitLineDecorated.getLedgerAccountId());
        Assertions.assertThat(debitLine.getLedgerAccountCode()).isEqualTo(debitLineDecorated.getLedgerAccountCode());
    }

    private JournalLine createJournalLine(
        EntryType entryType,
        LedgerAccountType ledgerAccountType,
        BigDecimal amount,
        String journalEntryId,
        InvoiceItem invoiceItem
    ) {
        return new JournalLine.Builder()
            .setEntityId(invoiceItem.getEntityId())
            .setJournalLineId(journalLineIdGenerator.generate())
            .setJournalEntryId(journalEntryId)
            .setJournalLineNumber(1)
            .setAmount(amount)
            .setEntryType(entryType)
            .setSourceTransactionLineId(invoiceItem.getInvoiceLineNumber())
            .setPlanId(invoiceItem.getPlanId())
            .setChargeId(invoiceItem.getChargeId())
            .setLedgerAccountType(ledgerAccountType)
            .setLedgerAccountId(UUID.randomUUID().toString())
            .setLedgerAccountCode(ledgerAccountType.getDefaultAccountCode())
            .setEntityId(EntityFixture.ENTITY_1_ID)
            .build();
    }

    public JournalEntry createJournalEntry(
        String journalEntryId,
        JournalEntry.TransactionType transactionType,
        List<JournalLine> journalLines,
        InvoiceItem invoiceItem,
        Invoice invoice
    ) {
        return new JournalEntry.Builder()
            .setJournalEntryId(journalEntryId)
            .setId(UUID.randomUUID())
            .setEntityId(invoiceItem.getEntityId())
            .setAccountId(UUID.randomUUID().toString())
            .setSourceEventId(UUID.randomUUID().toString())
            .setSourceEventTimestamp(clock.instant())
            .setJournalLines(journalLines)
            .setSourceTransactionId(invoice.getInvoiceNumber().getNumber())
            .setSourceTransactionType(transactionType)
            .setCurrency(invoice.getCurrency())
            .setStatus(JournalEntry.Status.DRAFT)
            .setAccountingDate(invoice.getInvoiceDate())
            .build();
    }
}
