package com.subskribe.billy.accounting.fixture;

import com.subskribe.billy.accounting.model.AccountingFxBalance;
import com.subskribe.billy.accounting.model.FxBalanceContext;
import com.subskribe.billy.accounting.model.FxOperationResult;
import com.subskribe.billy.accounting.model.ImmutableInvoiceLineItemBalances;
import com.subskribe.billy.accounting.model.InvoiceLineItemBalances;
import com.subskribe.billy.accounting.model.RunningBalance;
import com.subskribe.billy.accounting.model.RunningBalanceContext;
import com.subskribe.billy.accounting.model.RunningBalanceType;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class BalanceContexts {

    private static final String SCHEDULE_ID = UUID.randomUUID().toString();
    private static final String FUNCTIONAL_CURRENCY = "AUD";

    public static RunningBalanceContext initRunningBalanceContext(Invoice invoice) {
        InvoiceItem invoiceItem = invoice.getInvoiceItems().stream().findFirst().orElseThrow();
        String invoiceItemId = invoiceItem.getId().id();
        RunningBalance contractAssetBalance = initRunningBalance(RunningBalanceType.CONTRACT_ASSET);
        RunningBalance deferredRevenueBalance = initRunningBalance(RunningBalanceType.DEFERRED_REVENUE);
        Map<String, String> invoiceItemIdToRevenueScheduleMap = Map.of(invoiceItemId, SCHEDULE_ID);
        InvoiceLineItemBalances invoiceLineItemBalances = ImmutableInvoiceLineItemBalances.builder()
            .recognitionScheduleId(SCHEDULE_ID)
            .contractAssetBalance(contractAssetBalance)
            .deferredRevenueBalance(deferredRevenueBalance)
            .build();
        Map<String, InvoiceLineItemBalances> scheduleIdToBalancesMap = Map.of(SCHEDULE_ID, invoiceLineItemBalances);
        return RunningBalanceContext.from(invoiceItemIdToRevenueScheduleMap, scheduleIdToBalancesMap);
    }

    public static RunningBalance initRunningBalance(RunningBalanceType runningBalanceType) {
        return RunningBalance.builder().id(UUID.randomUUID().toString()).type(runningBalanceType).balance(BigDecimal.ZERO).build();
    }

    public static FxBalanceContext initFxBalanceContext(RunningBalanceContext runningBalanceContext) {
        Map<String, List<AccountingFxBalance>> fxBalancesMap = new HashMap<>();
        for (var runningBalance : runningBalanceContext.getAllBalances()) {
            List<AccountingFxBalance> fxBalances = List.of();
            fxBalancesMap.put(runningBalance.getId(), fxBalances);
        }
        return new FxBalanceContext(fxBalancesMap);
    }

    public static FxOperationResult settleBalance(
        RunningBalanceContext runningBalanceContext,
        RunningBalanceType runningBalanceType,
        FxBalanceContext fxBalanceContext,
        String prevRunningBalanceId,
        BigDecimal transactionAmount,
        BigDecimal exchangeRate
    ) {
        // increment running balance
        RunningBalance updatedRunningBalance = updateRunningBalance(runningBalanceContext, runningBalanceType, transactionAmount);

        // increment fx balance
        return fxBalanceContext.settleBalance(
            prevRunningBalanceId,
            updatedRunningBalance,
            transactionAmount,
            FUNCTIONAL_CURRENCY,
            UUID.randomUUID().toString(),
            exchangeRate,
            Instant.now()
        );
    }

    public static RunningBalance updateRunningBalance(
        RunningBalanceContext runningBalanceContext,
        RunningBalanceType runningBalanceType,
        BigDecimal updatedBalance
    ) {
        RunningBalance contractAssetBalance = runningBalanceContext
            .getAllBalances()
            .stream()
            .filter(balance -> balance.getType() == RunningBalanceType.CONTRACT_ASSET)
            .findFirst()
            .orElseThrow();
        RunningBalance deferredRevenueBalance = runningBalanceContext
            .getAllBalances()
            .stream()
            .filter(balance -> balance.getType() == RunningBalanceType.DEFERRED_REVENUE)
            .findFirst()
            .orElseThrow();
        if (runningBalanceType == RunningBalanceType.CONTRACT_ASSET) {
            contractAssetBalance = RunningBalance.builder(contractAssetBalance).balance(updatedBalance).build();
        } else {
            deferredRevenueBalance = RunningBalance.builder(deferredRevenueBalance).balance(updatedBalance).build();
        }
        runningBalanceContext.updateBalanceInPlace(
            ImmutableInvoiceLineItemBalances.builder()
                .recognitionScheduleId(SCHEDULE_ID)
                .deferredRevenueBalance(deferredRevenueBalance)
                .contractAssetBalance(contractAssetBalance)
                .build()
        );
        return runningBalanceType == RunningBalanceType.CONTRACT_ASSET ? contractAssetBalance : deferredRevenueBalance;
    }

    public static RunningBalance getContractAssetBalance(RunningBalanceContext runningBalanceContext) {
        return getRunningBalance(runningBalanceContext, RunningBalanceType.CONTRACT_ASSET);
    }

    public static RunningBalance getDeferredRevenueBalance(RunningBalanceContext runningBalanceContext) {
        return getRunningBalance(runningBalanceContext, RunningBalanceType.DEFERRED_REVENUE);
    }

    public static RunningBalance getRunningBalance(RunningBalanceContext runningBalanceContext, RunningBalanceType runningBalanceType) {
        return runningBalanceContext.getAllBalances().stream().filter(balance -> balance.getType() == runningBalanceType).findFirst().orElseThrow();
    }
}
