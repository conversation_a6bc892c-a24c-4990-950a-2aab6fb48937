package com.subskribe.billy.accounting.journalentry;

import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.event.streams.Stream;
import com.subskribe.billy.event.streams.kinesis.ConsumerConsumptionMode;
import com.subskribe.billy.exception.handling.TryForeverExceptionHandlingStrategy;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class AccountingLogWritingEventConsumerConfigurationTest {

    @Mock
    private AccountingEventLogWriter accountingEventLogWriter;

    @Mock
    private FeatureService featureService;

    @Mock
    private PlatformFeatureService platformFeatureService;

    private AccountingLogWritingEventConsumer accountingLogWritingEventConsumer;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        accountingLogWritingEventConsumer = new AccountingLogWritingEventConsumer(accountingEventLogWriter, featureService, platformFeatureService);
    }

    @Test
    public void assertThatAccountingEventConsumerConfigurationIsAsExpected() {
        Assertions.assertThat(accountingLogWritingEventConsumer.getConfiguration().getCanonicalConsumerName()).isEqualTo(
            AccountingLogWritingEventConsumer.ACCOUNTING_CONSUMER_CANONICAL_NAME
        );
        Assertions.assertThat(AccountingLogWritingEventConsumer.ACCOUNTING_CONSUMER_CANONICAL_NAME).isEqualTo("accounting-consumer");

        Assertions.assertThat(accountingLogWritingEventConsumer.getConfiguration().getExceptionHandlingStrategy()).isInstanceOf(
            TryForeverExceptionHandlingStrategy.class
        );

        Assertions.assertThat(accountingLogWritingEventConsumer.getConfiguration().getHandlesEventsFromStream()).containsOnly(
            Stream.BILLING_DOMAIN_STREAM,
            Stream.REVENUE_DOMAIN_STREAM
        );
        Assertions.assertThat(accountingLogWritingEventConsumer.getConfiguration().getConsumerConsumptionMode()).isEqualTo(
            ConsumerConsumptionMode.DEDICATED_FAN_OUT
        );
    }
}
