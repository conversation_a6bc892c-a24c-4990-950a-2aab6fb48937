package com.subskribe.billy.accounting.model;

import com.subskribe.billy.accounting.fixture.BalanceContexts;
import com.subskribe.billy.invoice.fixture.InvoiceFixture;
import com.subskribe.billy.invoice.model.Invoice;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class FxBalanceContextTest {

    private static final BigDecimal ADDITION_1 = new BigDecimal("1000.00");
    private static final BigDecimal EXCHANGE_RATE_1 = new BigDecimal("1.1"); // 1100.00 AUD
    private static final BigDecimal ADDITION_2 = new BigDecimal("2000.0");
    private static final BigDecimal EXCHANGE_RATE_2 = new BigDecimal("1.2"); // 2400.00 AUD
    private static final BigDecimal ADDITION_3 = new BigDecimal("3000.0");
    private static final BigDecimal EXCHANGE_RATE_3 = new BigDecimal("1.3"); // 3900.00 AUD
    private static final BigDecimal DEDUCTION_1 = new BigDecimal("400.00");
    private static final BigDecimal DEDUCTION_1_FX = new BigDecimal("440.00"); // 400 * 1.1 = 440.00 AUD
    private static final List<BigDecimal> RUNNING_AFTER_DEDUCTION_1 = List.of(
        new BigDecimal("600.00"),
        new BigDecimal("2000.00"),
        new BigDecimal("3000.00")
    );
    private static final List<BigDecimal> FX_AFTER_DEDUCTION_1 = List.of(
        new BigDecimal("660.00"),
        new BigDecimal("2400.00"),
        new BigDecimal("3900.00")
    );
    private static final BigDecimal DEDUCTION_2 = new BigDecimal("900.00");
    private static final BigDecimal DEDUCTION_2_FX = new BigDecimal("1020.00"); // 600*1.1 + 300*1.2 = 660 + 360 = 1020.00 AUD
    private static final List<BigDecimal> RUNNING_AFTER_DEDUCTION_2 = List.of(BigDecimal.ZERO, new BigDecimal("1700.00"), new BigDecimal("3000.00"));
    private static final List<BigDecimal> FX_AFTER_DEDUCTION_2 = List.of(BigDecimal.ZERO, new BigDecimal("2040.00"), new BigDecimal("3900.00"));

    @BeforeEach
    public void setUp() {}

    @Test
    public void testAddBalances() {
        // init balance contexts
        Invoice invoice = InvoiceFixture.getDefault().createInvoice();
        RunningBalanceContext runningBalanceContext = BalanceContexts.initRunningBalanceContext(invoice);
        String prevRunningBalanceId = BalanceContexts.getContractAssetBalance(runningBalanceContext).getId();
        FxBalanceContext fxBalanceContext = BalanceContexts.initFxBalanceContext(runningBalanceContext);

        // add contract asset balance 1
        BalanceContexts.settleBalance(
            runningBalanceContext,
            RunningBalanceType.CONTRACT_ASSET,
            fxBalanceContext,
            prevRunningBalanceId,
            ADDITION_1,
            EXCHANGE_RATE_1
        );

        // verify the update
        Collection<AccountingFxBalance> updatedFxBalances = fxBalanceContext.getBalances();
        Assertions.assertThat(updatedFxBalances).hasSize(1);
        AccountingFxBalance updatedFxBalance = updatedFxBalances.stream().findFirst().orElseThrow();
        Assertions.assertThat(updatedFxBalance.getBalance()).isEqualByComparingTo(ADDITION_1);
        Assertions.assertThat(updatedFxBalance.getFunctionalBalance()).isEqualByComparingTo(ADDITION_1.multiply(EXCHANGE_RATE_1));

        // add contract asset balance 2
        prevRunningBalanceId = BalanceContexts.getContractAssetBalance(runningBalanceContext).getId();
        BalanceContexts.settleBalance(
            runningBalanceContext,
            RunningBalanceType.CONTRACT_ASSET,
            fxBalanceContext,
            prevRunningBalanceId,
            ADDITION_2,
            EXCHANGE_RATE_2
        );

        // verify the update
        updatedFxBalances = fxBalanceContext.getBalances();
        Assertions.assertThat(updatedFxBalances).hasSize(2);
        updatedFxBalance = updatedFxBalances.stream().skip(1).findFirst().orElseThrow();
        Assertions.assertThat(updatedFxBalance.getBalance()).isEqualByComparingTo(ADDITION_2);
        Assertions.assertThat(updatedFxBalance.getFunctionalBalance()).isEqualByComparingTo(ADDITION_2.multiply(EXCHANGE_RATE_2));
    }

    @Test
    public void testDeductBalances() {
        // init balance contexts
        Invoice invoice = InvoiceFixture.getDefault().createInvoice();
        RunningBalanceContext runningBalanceContext = BalanceContexts.initRunningBalanceContext(invoice);
        String prevRunningBalanceId = BalanceContexts.getContractAssetBalance(runningBalanceContext).getId();
        FxBalanceContext fxBalanceContext = BalanceContexts.initFxBalanceContext(runningBalanceContext);

        // add contract asset balances - $1000, $2000, $3000 at fx rates 1.1, 1.2 and 1.3
        BalanceContexts.settleBalance(
            runningBalanceContext,
            RunningBalanceType.CONTRACT_ASSET,
            fxBalanceContext,
            prevRunningBalanceId,
            ADDITION_1,
            EXCHANGE_RATE_1
        );
        prevRunningBalanceId = BalanceContexts.getContractAssetBalance(runningBalanceContext).getId();
        BalanceContexts.settleBalance(
            runningBalanceContext,
            RunningBalanceType.CONTRACT_ASSET,
            fxBalanceContext,
            prevRunningBalanceId,
            ADDITION_2,
            EXCHANGE_RATE_2
        );
        prevRunningBalanceId = BalanceContexts.getContractAssetBalance(runningBalanceContext).getId();
        BalanceContexts.settleBalance(
            runningBalanceContext,
            RunningBalanceType.CONTRACT_ASSET,
            fxBalanceContext,
            prevRunningBalanceId,
            ADDITION_3,
            EXCHANGE_RATE_3
        );

        // reduce contract asset balance by $500
        BigDecimal deductedFxAmount = BalanceContexts.settleBalance(
            runningBalanceContext,
            RunningBalanceType.CONTRACT_ASSET,
            fxBalanceContext,
            prevRunningBalanceId,
            DEDUCTION_1.negate(),
            EXCHANGE_RATE_1
        ).getAffectedFunctionalAmount();
        Assertions.assertThat(deductedFxAmount).isEqualByComparingTo(DEDUCTION_1_FX);

        // verify the update
        Collection<AccountingFxBalance> fxBalances = fxBalanceContext.getBalances();
        Assertions.assertThat(fxBalances).hasSize(3);
        for (int i = 0; i < fxBalances.size(); i++) {
            AccountingFxBalance fxBalance = fxBalances.stream().skip(i).findFirst().orElseThrow();
            Assertions.assertThat(fxBalance.getBalance()).isEqualByComparingTo(RUNNING_AFTER_DEDUCTION_1.get(i));
            Assertions.assertThat(fxBalance.getFunctionalBalance()).isEqualByComparingTo(FX_AFTER_DEDUCTION_1.get(i));
        }

        // reduce contract asset balance by another $900
        prevRunningBalanceId = BalanceContexts.getContractAssetBalance(runningBalanceContext).getId();
        deductedFxAmount = BalanceContexts.settleBalance(
            runningBalanceContext,
            RunningBalanceType.CONTRACT_ASSET,
            fxBalanceContext,
            prevRunningBalanceId,
            DEDUCTION_2.negate(),
            EXCHANGE_RATE_2
        ).getAffectedFunctionalAmount();
        Assertions.assertThat(deductedFxAmount).isEqualByComparingTo(DEDUCTION_2_FX);

        // verify the update
        fxBalances = fxBalanceContext.getBalances();
        Assertions.assertThat(fxBalances).hasSize(3);
        for (int i = 0; i < fxBalances.size(); i++) {
            AccountingFxBalance fxBalance = fxBalances.stream().skip(i).findFirst().orElseThrow();
            Assertions.assertThat(fxBalance.getBalance()).isEqualByComparingTo(RUNNING_AFTER_DEDUCTION_2.get(i));
            Assertions.assertThat(fxBalance.getFunctionalBalance()).isEqualByComparingTo(FX_AFTER_DEDUCTION_2.get(i));
        }
    }
}
