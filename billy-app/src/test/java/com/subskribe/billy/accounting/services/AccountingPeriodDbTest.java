package com.subskribe.billy.accounting.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.accounting.db.AccountingPeriodDAO;
import com.subskribe.billy.accounting.db.JournalEntryDAO;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.AccountingPeriodStatus;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityContextProviderFixture;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.erp.service.ErpSyncQueueService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.order.model.TenantSealProtectionConfiguration;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.shared.enums.PercentDerivedFrom;
import com.subskribe.billy.shared.enums.SigningOrder;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantIdProviderFixture;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.model.TenantSettingSeal;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.service.TenantJobGetService;
import com.subskribe.billy.test.WithDb;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.UUID;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.api.Condition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

@TestMethodOrder(OrderAnnotation.class)
public class AccountingPeriodDbTest extends WithDb {

    private static final TenantSetting TENANT_SETTING = getTenantSetting("America/Los_Angeles");
    private static final Instant CURRENT_PERIOD_START = Instant.parse("2022-01-01T08:00:00.00Z");

    private static final Period ALL_INCLUSIVE_PERIOD = Period.between(
        CURRENT_PERIOD_START.minus(10000, ChronoUnit.DAYS),
        CURRENT_PERIOD_START.plus(10000, ChronoUnit.DAYS)
    );

    @Mock
    private RevenueRecognitionGetService mockRevenueRecognitionService;

    @Mock
    private TenantSettingService mockTenantSettingService;

    private final TenantIdProvider tenantIdProvider = TenantIdProviderFixture.getDefault();

    private final EntityContextProvider entityContextProvider = EntityContextProviderFixture.buildSingleEntityContext();

    private final FeatureService featureService = FeatureServiceFixture.allEnabled();

    @Mock
    private CurrentUserProvider mockCurrentUserProvider;

    @Mock
    private UserService mockUserService;

    @Mock
    private AccountingService accountingService;

    @Mock
    private PlatformFeatureService platformFeatureService;

    @Mock
    private ErpSyncQueueService erpSyncQueueService;

    @Mock
    private JournalEntryDAO journalEntryDAO;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private TenantJobGetService tenantJobGetService;

    private AccountingPeriodDAO accountingPeriodDAO;

    private AccountingPeriodService accountingPeriodService;

    private AccountingPeriodStatusUpdateService accountingPeriodStatusUpdateService;

    @BeforeAll
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockTenantSettingService.getTenantSettingInternal()).thenReturn(TENANT_SETTING);
        when(mockTenantSettingService.isSealed()).thenReturn(true);
        when(billyConfiguration.getTenantSealProtectionConfiguration()).thenReturn(new TenantSealProtectionConfiguration());

        accountingPeriodDAO = new AccountingPeriodDAO(tenantIdProvider, entityContextProvider, featureService, dslContextProvider);
        AccountingPeriodIdGenerator accountingPeriodIdGenerator = new AccountingPeriodIdGenerator(accountingPeriodDAO);
        accountingPeriodService = spy(
            new AccountingPeriodService(
                accountingPeriodDAO,
                accountingPeriodIdGenerator,
                mockCurrentUserProvider,
                mockUserService,
                mockTenantSettingService,
                tenantIdProvider,
                entityContextProvider,
                dslContextProvider,
                accountingService,
                billyConfiguration
            )
        );
        AccountingPeriodCalculationService accountingPeriodCalculationService = new AccountingPeriodCalculationService(
            accountingPeriodService,
            mockRevenueRecognitionService
        );
        accountingPeriodStatusUpdateService = new AccountingPeriodStatusUpdateService(
            accountingPeriodService,
            accountingPeriodCalculationService,
            mockRevenueRecognitionService,
            mockTenantSettingService,
            dslContextProvider,
            tenantIdProvider,
            platformFeatureService,
            erpSyncQueueService,
            journalEntryDAO,
            tenantJobGetService
        );

        doNothing().when(accountingPeriodService).tryAndAcquireAccountingPeriodLock(any());
        EntityFixture.initDb(dslContextProvider);
    }

    @Test
    @Order(1)
    // before generation - ensure no accounting periods
    public void emptyAccountingPeriods() {
        Optional<AccountingPeriod> lastPeriod = accountingPeriodDAO.getLast(entityContextProvider.provideSelected());
        assertThat(lastPeriod.isEmpty()).isTrue();
    }

    @Test
    @Order(2)
    // after specify accounting period - ensure open period
    // Jan 2022 (Open)
    public void specifyCurrentOpenPeriod() {
        AccountingPeriod savedAccountingPeriod = accountingPeriodService.specifyCurrentAccountingPeriod(CURRENT_PERIOD_START);
        AccountingPeriod currentAccountingPeriod = accountingPeriodDAO.getFirstOpen(entityContextProvider.provideSelected()).orElseThrow();

        assertThat(savedAccountingPeriod).isEqualTo(currentAccountingPeriod);
        assertThat(currentAccountingPeriod.getStatus()).isEqualTo(AccountingPeriodStatus.OPEN);
        assertThat(CURRENT_PERIOD_START).isEqualTo(currentAccountingPeriod.getStartDate());
    }

    @Test
    @Order(3)
    // Jan 2022 (Closed), Feb 2022 (Open)
    public void closeAccountingPeriod() {
        AccountingPeriod currentAccountingPeriod = accountingPeriodDAO.getFirstOpen(entityContextProvider.provideSelected()).orElseThrow();
        AccountingPeriod closedAccountingPeriod = accountingPeriodStatusUpdateService.closeAccountingPeriod(
            currentAccountingPeriod.getAccountingPeriodId()
        );
        AccountingPeriod closedAccountingPeriodFromDb = accountingPeriodDAO
            .getAccountingPeriodByAccountingPeriodId(closedAccountingPeriod.getAccountingPeriodId())
            .orElseThrow();
        AccountingPeriod nextAccountingPeriod = accountingPeriodDAO.getFirstOpen(entityContextProvider.provideSelected()).orElseThrow();

        assertThat(closedAccountingPeriod).isEqualTo(closedAccountingPeriodFromDb);
        assertThat(closedAccountingPeriod.getStatus()).isEqualTo(AccountingPeriodStatus.CLOSED);
        assertThat(closedAccountingPeriodFromDb.getStatus()).isEqualTo(AccountingPeriodStatus.CLOSED);
        assertThat(nextAccountingPeriod.getStatus()).isEqualTo(AccountingPeriodStatus.OPEN);
    }

    @Test
    @Order(4)
    // Jan 2022 (Open), Feb 2022 (Upcoming)
    public void reopenAccountingPeriod() {
        AccountingPeriod currentAccountingPeriod = accountingPeriodDAO.getFirstOpen(entityContextProvider.provideSelected()).orElseThrow();
        AccountingPeriod previousAccountingPeriod = accountingPeriodDAO.getPreviousAccountingPeriod(currentAccountingPeriod).orElseThrow();
        AccountingPeriod reopenedAccountingPeriod = accountingPeriodStatusUpdateService.reopenAccountingPeriod(
            previousAccountingPeriod.getAccountingPeriodId()
        );
        AccountingPeriod reopenedAccountingPeriodFromDb = accountingPeriodDAO.getFirstOpen(entityContextProvider.provideSelected()).orElseThrow();
        AccountingPeriod nextAccountingPeriod = accountingPeriodDAO
            .getAccountingPeriodByAccountingPeriodId(currentAccountingPeriod.getAccountingPeriodId())
            .orElseThrow();

        assertThat(reopenedAccountingPeriod).isEqualTo(reopenedAccountingPeriodFromDb);
        assertThat(reopenedAccountingPeriod.getStatus()).isEqualTo(AccountingPeriodStatus.OPEN);
        assertThat(reopenedAccountingPeriodFromDb.getStatus()).isEqualTo(AccountingPeriodStatus.OPEN);
        assertThat(nextAccountingPeriod.getStatus()).isEqualTo(AccountingPeriodStatus.UPCOMING);
    }

    @Test
    @Order(5)
    // generate accounting periods overlapping with current period
    // Generate: Sep 2020 through May 2021 (should be already generated earlier)
    public void generateOverlappingAccountingPeriods() {
        AccountingPeriod currentAccountingPeriod = accountingPeriodDAO.getFirstOpen(entityContextProvider.provideSelected()).orElseThrow();
        var startDate = DateTimeCalculator.minusMonths(
            mockTenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId(),
            currentAccountingPeriod.getStartDate(),
            4
        );
        var endDate = DateTimeCalculator.plusMonths(
            mockTenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId(),
            currentAccountingPeriod.getEndDate(),
            4
        );
        var generatedPeriods = accountingPeriodService.generateAccountingPeriods(Period.between(startDate, endDate));

        assertThat(generatedPeriods.size()).isEqualTo(9);
        assertThat(generatedPeriods).areExactly(
            1,
            new Condition<>(
                period ->
                    period.getStatus() == AccountingPeriodStatus.OPEN &&
                    Objects.equals(period.getAccountingPeriodId(), currentAccountingPeriod.getAccountingPeriodId()),
                "open accounting period"
            )
        );
        assertThat(generatedPeriods).areExactly(
            4,
            new Condition<>(
                period ->
                    period.getStatus() == AccountingPeriodStatus.CLOSED && period.getStartDate().isBefore(currentAccountingPeriod.getStartDate()),
                "closed accounting period"
            )
        );
        assertThat(generatedPeriods).areExactly(
            4,
            new Condition<>(
                period ->
                    period.getStatus() == AccountingPeriodStatus.UPCOMING && period.getStartDate().isAfter(currentAccountingPeriod.getStartDate()),
                "up next accounting period"
            )
        );
    }

    @Test
    @Order(6)
    // generate accounting periods after last period with a gap of 2 months in between
    // make sure correct number of accounting periods are generated
    // last period: Dec 2031
    // generate: Mar 2032 through May 2032
    public void generateAccountingPeriodsWithTrailingGap() {
        var lastPeriod = accountingPeriodDAO.getLast(entityContextProvider.provideSelected()).orElse(null);
        assertThat(lastPeriod).isNotNull();
        ZoneId zoneId = mockTenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        Instant startDate = DateTimeCalculator.plusMonths(zoneId, lastPeriod.getStartDate(), 3);
        Instant endDate = DateTimeCalculator.plusMonths(zoneId, startDate, 3);
        List<AccountingPeriod> generatedPeriods = accountingPeriodService.generateAccountingPeriods(Period.between(startDate, endDate));
        assertThat(generatedPeriods.size()).isEqualTo(3);
        List<AccountingPeriod> rangePeriods = accountingPeriodDAO.getAccountingPeriodsByPeriod(Period.between(lastPeriod.getStartDate(), endDate));
        assertThat(rangePeriods.size()).isEqualTo(6);
    }

    @Test
    @Order(7)
    // generate accounting periods before first period with a gap of 2 months in between
    // make sure correct number of accounting periods are generated
    // first period: Sep 2021
    // generate: Apr 2021 through July 2021
    public void generateAccountingPeriodsWithLeadingGap() {
        var firstPeriod = accountingPeriodDAO.getFirst(entityContextProvider.provideSelected()).orElse(null);
        assertThat(firstPeriod).isNotNull();
        ZoneId zoneId = mockTenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        Instant startDate = DateTimeCalculator.minusMonths(zoneId, firstPeriod.getStartDate(), 5);
        Instant endDate = DateTimeCalculator.plusMonths(zoneId, startDate, 3);
        List<AccountingPeriod> generatedPeriods = accountingPeriodService.generateAccountingPeriods(Period.between(startDate, endDate));
        assertThat(generatedPeriods.size()).isEqualTo(3);
        List<AccountingPeriod> rangePeriods = accountingPeriodDAO.getAccountingPeriodsByPeriod(Period.between(startDate, firstPeriod.getEndDate()));
        assertThat(rangePeriods.size()).isEqualTo(6);
    }

    @Test
    @Order(8)
    // make sure that all periods are closed before current open period
    // and all periods are open after current period
    // Apr 2021 (Closed) .. Jan 2022 (Open) .. May 2032 (Upcoming)
    public void checkAccountingPeriodStatus() {
        AccountingPeriod currentAccountingPeriod = accountingPeriodDAO.getFirstOpen(entityContextProvider.provideSelected()).orElseThrow();
        List<AccountingPeriod> allAccountingPeriods = accountingPeriodDAO.getAccountingPeriodsByPeriod(ALL_INCLUSIVE_PERIOD);
        allAccountingPeriods.forEach(accountingPeriod -> {
            if (accountingPeriod.getStartDate().isBefore(currentAccountingPeriod.getStartDate())) {
                assertThat(accountingPeriod.getStatus()).isEqualTo(AccountingPeriodStatus.CLOSED);
            } else if (accountingPeriod.getStartDate().isAfter(currentAccountingPeriod.getStartDate())) {
                assertThat(accountingPeriod.getStatus()).isEqualTo(AccountingPeriodStatus.UPCOMING);
            } else {
                assertThat(accountingPeriod.getStatus()).isEqualTo(AccountingPeriodStatus.OPEN);
            }
        });
    }

    @Test
    @Order(9)
    // check various cases of conversion from instant to accounting period
    // Apr 2021 (Closed) .. Jan 2022 (Open) .. May 2032 (Upcoming)
    public void checkAccountingPeriodByInstant() {
        AccountingPeriod firstPeriod = accountingPeriodDAO.getFirst(entityContextProvider.provideSelected()).orElseThrow();
        AccountingPeriod currentAccountingPeriod = accountingPeriodDAO.getFirstOpen(entityContextProvider.provideSelected()).orElseThrow();
        AccountingPeriod previousAccountingPeriod = accountingPeriodDAO.getPreviousAccountingPeriod(currentAccountingPeriod).orElseThrow();
        AccountingPeriod lastPeriod = accountingPeriodDAO.getLast(entityContextProvider.provideSelected()).orElseThrow();

        List<Pair<Instant, AccountingPeriod>> validations = List.of(
            Pair.of(currentAccountingPeriod.getStartDate(), currentAccountingPeriod),
            Pair.of(currentAccountingPeriod.getStartDate().minusSeconds(1), previousAccountingPeriod),
            Pair.of(previousAccountingPeriod.getStartDate(), previousAccountingPeriod),
            Pair.of(previousAccountingPeriod.getEndDate(), currentAccountingPeriod),
            Pair.of(previousAccountingPeriod.getEndDate().minusSeconds(1), previousAccountingPeriod),
            Pair.of(firstPeriod.getStartDate(), firstPeriod),
            Pair.of(firstPeriod.getStartDate().minusSeconds(1), null),
            Pair.of(lastPeriod.getEndDate().minusSeconds(1), lastPeriod),
            Pair.of(lastPeriod.getEndDate(), null)
        );

        validations.forEach(pair -> {
            Instant instant = pair.getLeft();
            AccountingPeriod expectedAccountingPeriod = pair.getRight();
            var obtainedAccountingPeriod = accountingPeriodDAO.getAccountingPeriodByInstant(instant);
            if (expectedAccountingPeriod == null) {
                assertThat(obtainedAccountingPeriod).isEmpty();
            } else {
                assertThat(obtainedAccountingPeriod).isPresent();
                assertThat(obtainedAccountingPeriod.get()).isEqualTo(expectedAccountingPeriod);
            }
        });

        // accounting period service -> generates when missing
        AccountingPeriod beforeFirst = accountingPeriodService.getAccountingPeriodByInstant(firstPeriod.getStartDate().minusSeconds(1));
        assertThat(beforeFirst).isNotNull();
        ZoneId zoneId = mockTenantSettingService.getTenantSettingInternal().getDefaultTimeZone().toZoneId();
        Instant expectedStartDate = DateTimeCalculator.minusMonths(zoneId, firstPeriod.getStartDate(), 1);
        assertThat(beforeFirst.getStartDate()).isEqualTo(expectedStartDate);

        AccountingPeriod afterLast = accountingPeriodService.getAccountingPeriodByInstant(lastPeriod.getEndDate());
        assertThat(afterLast).isNotNull();
        expectedStartDate = DateTimeCalculator.plusMonths(zoneId, lastPeriod.getStartDate(), 1);
        assertThat(afterLast.getStartDate()).isEqualTo(expectedStartDate);
    }

    @Test
    @Order(10)
    public void tryClosingAll() {
        AccountingPeriod currentAccountingPeriod = accountingPeriodDAO.getFirstOpen(entityContextProvider.provideSelected()).orElseThrow();
        accountingPeriodDAO
            .getAccountingPeriodsByPeriod(ALL_INCLUSIVE_PERIOD)
            .stream()
            .filter(accountingPeriod -> !currentAccountingPeriod.getAccountingPeriodId().equals(accountingPeriod.getAccountingPeriodId()))
            .forEach(accountingPeriod ->
                Assertions.assertThrows(ConflictingStateException.class, () ->
                    accountingPeriodStatusUpdateService.closeAccountingPeriod(accountingPeriod.getAccountingPeriodId())
                )
            );
    }

    @Test
    @Order(11)
    public void tryOpeningAll() {
        AccountingPeriod lastClosedPeriod = accountingPeriodDAO.getLastClosed().orElseThrow();
        // when trying to reopen
        accountingPeriodDAO
            .getAccountingPeriodsByPeriod(ALL_INCLUSIVE_PERIOD)
            .stream()
            .filter(accountingPeriod -> !lastClosedPeriod.getAccountingPeriodId().equals(accountingPeriod.getAccountingPeriodId()))
            // re-opening already opened accounting periods should be idempotent
            .filter(accountingPeriod -> accountingPeriod.getStatus() != AccountingPeriodStatus.OPEN)
            .forEach(accountingPeriod ->
                Assertions.assertThrows(ConflictingStateException.class, () ->
                    accountingPeriodStatusUpdateService.reopenAccountingPeriod(accountingPeriod.getAccountingPeriodId())
                )
            );
    }

    public static TenantSetting getTenantSetting(String timezone) {
        return new TenantSetting(
            UUID.randomUUID(),
            TimeZone.getTimeZone(timezone),
            null,
            List.of("USD"),
            PercentDerivedFrom.LIST_AMOUNT,
            TenantSettingSeal.ON,
            null,
            SigningOrder.ACCOUNT_FIRST,
            null,
            false,
            false,
            false,
            false,
            null
        );
    }
}
