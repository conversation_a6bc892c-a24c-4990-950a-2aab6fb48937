package com.subskribe.billy.accounting.services;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.accounting.db.AccountingPeriodDAO;
import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.AccountingPeriodStatus;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityContextProviderFixture;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import com.subskribe.billy.user.service.UserService;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class AccountingPeriodServiceTest {

    private static final ZoneOffset ZONE_OFFSET = ZoneOffset.UTC;

    @Mock
    private AccountingPeriodDAO mockAccountingPeriodDao;

    @Mock
    private AccountingPeriodIdGenerator mockAccountingPeriodIdGenerator;

    @Mock
    private CurrentUserProvider currentUserProvider;

    @Mock
    private UserService userService;

    @Mock
    private TenantSettingService tenantSettingService;

    @Mock
    private TenantIdProvider tenantIdProvider;

    private final EntityContextProvider entityContextProvider = EntityContextProviderFixture.buildSingleEntityContext();

    @Mock
    private DSLContextProvider dslContextProvider;

    @Mock
    private AccountingService accountingService;

    private AccountingPeriodService accountingPeriodService;

    @Mock
    private BillyConfiguration billyConfiguration;

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.openMocks(this);
        accountingPeriodService = new AccountingPeriodService(
            mockAccountingPeriodDao,
            mockAccountingPeriodIdGenerator,
            currentUserProvider,
            userService,
            tenantSettingService,
            tenantIdProvider,
            entityContextProvider,
            dslContextProvider,
            accountingService,
            billyConfiguration
        );
    }

    @Test
    public void checkInstantInClosedAccountingPeriod() {
        // no open accounting periods
        when(mockAccountingPeriodDao.getFirstOpen(entityContextProvider.provideSelected())).thenReturn(Optional.empty());
        boolean inClosedAccountingPeriod = accountingPeriodService.inClosedAccountingPeriod(entityContextProvider.provideSelected(), Instant.now());
        assertFalse(inClosedAccountingPeriod);

        // instant before open accounting period start
        LocalDateTime now = LocalDateTime.now();
        when(mockAccountingPeriodDao.getFirstOpen(entityContextProvider.provideSelected())).thenReturn(Optional.of(getAccountingPeriod(now)));
        inClosedAccountingPeriod = accountingPeriodService.inClosedAccountingPeriod(
            entityContextProvider.provideSelected(),
            now.minusSeconds(1).toInstant(ZONE_OFFSET)
        );
        assertTrue(inClosedAccountingPeriod);

        // instant after open accounting period start
        now = LocalDateTime.now();
        when(mockAccountingPeriodDao.getFirstOpen(entityContextProvider.provideSelected())).thenReturn(Optional.of(getAccountingPeriod(now)));
        inClosedAccountingPeriod = accountingPeriodService.inClosedAccountingPeriod(
            entityContextProvider.provideSelected(),
            now.plusSeconds(1).toInstant(ZONE_OFFSET)
        );
        assertFalse(inClosedAccountingPeriod);

        // instant same as open accounting period start
        now = LocalDateTime.now();
        when(mockAccountingPeriodDao.getFirstOpen(entityContextProvider.provideSelected())).thenReturn(Optional.of(getAccountingPeriod(now)));
        inClosedAccountingPeriod = accountingPeriodService.inClosedAccountingPeriod(
            entityContextProvider.provideSelected(),
            now.toInstant(ZONE_OFFSET)
        );
        assertFalse(inClosedAccountingPeriod);
    }

    private static AccountingPeriod getAccountingPeriod(LocalDateTime start) {
        LocalDateTime end = start.plusMonths(1);
        return new AccountingPeriod(
            UUID.randomUUID().toString(),
            EntityFixture.ENTITY_1_ID,
            start.toInstant(ZONE_OFFSET).getEpochSecond(),
            end.toInstant(ZONE_OFFSET).getEpochSecond(),
            AccountingPeriodStatus.OPEN
        );
    }
}
