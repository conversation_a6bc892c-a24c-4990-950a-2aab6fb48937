package com.subskribe.billy.accounting.journalentry;

import static com.subskribe.billy.accounting.model.LedgerAccountType.ACCOUNTS_RECEIVABLE;
import static com.subskribe.billy.accounting.model.LedgerAccountType.CASH;
import static com.subskribe.billy.accounting.model.LedgerAccountType.DEFERRED_REVENUE;
import static com.subskribe.billy.accounting.model.LedgerAccountType.RECOGNIZED_REVENUE;
import static com.subskribe.billy.accounting.model.LedgerAccountType.TAX_LIABILITY;
import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.model.AccountingEvent;
import com.subskribe.billy.accounting.model.EntryType;
import com.subskribe.billy.accounting.model.JournalEntry;
import com.subskribe.billy.accounting.model.JournalLine;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.model.LedgerAccountType;
import com.subskribe.billy.accounting.model.RunningBalanceContext;
import com.subskribe.billy.accounting.services.JournalEntryIdGenerator;
import com.subskribe.billy.accounting.services.JournalLineIdGenerator;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.shared.enums.RecognitionSource;
import com.subskribe.billy.test.utilities.MockRecognitionRuleBuilder;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Currency;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class InvoicePostedJournalProcessorTest {

    private static final String CURRENCY_CODE = "USD";
    private static final String INVOICE_NUMBER = "INV-0004";
    private static final String ENTITY_ID = "ENT-1234567";
    private static final String CHARGE_ID = "CHRG-1234567";
    private static final UUID PLAN_UUID = UUID.randomUUID();
    private static final String PLAN_ID = "PLAN-1234567";
    private static final String EVENT_ID = UUID.randomUUID().toString();
    private static final Instant EVENT_TIME = Instant.now();
    private static final BigDecimal AMOUNT = new BigDecimal("12.25");
    private static final BigDecimal TAX_AMOUNT = new BigDecimal("1.05");

    @Mock
    private JournalEntryIdGenerator journalEntryIdGenerator;

    @Mock
    private JournalLineIdGenerator journalLineIdGenerator;

    @Mock
    private RunningBalanceContext runningBalanceContext;

    private InvoicePostedJournalCreator invoicePostedJournalProcessor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(journalEntryIdGenerator.generate()).thenReturn(String.format("JE-%s", RandomStringUtils.randomAlphanumeric(10).toUpperCase()));
        when(journalLineIdGenerator.generate()).thenReturn(String.format("JL-%s", RandomStringUtils.randomAlphanumeric(10).toUpperCase()));
        invoicePostedJournalProcessor = new InvoicePostedJournalCreator(journalEntryIdGenerator, journalLineIdGenerator);
    }

    @Test
    public void invoiceItemWithTaxRevRecDisabled() {
        AccountingEvent event = getEvent();
        Charge charge = getCharge(CHARGE_ID, PLAN_ID, PLAN_UUID, null);
        var chargeMap = getChargeMap(List.of(charge));
        Plan plan = getPlan(PLAN_UUID, PLAN_ID);
        var planMap = getPlanMap(List.of(plan));
        var chargeLedgerAccountsMap = getChargeLedgerAccountsMap(CHARGE_ID);
        InvoiceItem invoiceItem = getInvoiceItem(CHARGE_ID, AMOUNT, TAX_AMOUNT);
        Invoice invoice = getInvoice(List.of(invoiceItem));

        var journalEntryAndBalances = invoicePostedJournalProcessor.createJournalEntry(
            event,
            invoice,
            chargeMap,
            chargeLedgerAccountsMap,
            planMap,
            Map.of(),
            runningBalanceContext
        );
        var journalEntry = journalEntryAndBalances.getJournalEntry();
        Assertions.assertThat(journalEntryAndBalances.getBalances()).isEmpty();
        Assertions.assertThat(journalEntry.getJournalLines().size()).isEqualTo(3);
        Assertions.assertThat(journalEntry.getSourceEventId()).isEqualTo(EVENT_ID);
        Assertions.assertThat(journalEntry.getCurrency()).isEqualTo(Currency.getInstance(CURRENCY_CODE));
        Assertions.assertThat(journalEntry.getSourceEventTimestamp()).isEqualTo(EVENT_TIME);
        Assertions.assertThat(journalEntry.getEntityId()).isEqualTo(ENTITY_ID);

        JournalLine accountsReceivable = getJournalLineByType(journalEntry, ACCOUNTS_RECEIVABLE);
        checkJournalLine(accountsReceivable, EntryType.DEBIT, AMOUNT.add(TAX_AMOUNT));

        JournalLine recognizedRevenue = getJournalLineByType(journalEntry, RECOGNIZED_REVENUE);
        checkJournalLine(recognizedRevenue, EntryType.CREDIT, AMOUNT);

        JournalLine taxLiability = getJournalLineByType(journalEntry, TAX_LIABILITY);
        checkJournalLine(taxLiability, EntryType.CREDIT, TAX_AMOUNT);
    }

    @Test
    public void invoiceItemWithTaxRevRecEnabled() {
        AccountingEvent event = getEvent();
        RecognitionRule recognitionRule = new MockRecognitionRuleBuilder().withSource(RecognitionSource.INVOICE).build();
        Charge charge = getCharge(CHARGE_ID, PLAN_ID, PLAN_UUID, recognitionRule.getRuleId());
        var chargeMap = getChargeMap(List.of(charge));
        Plan plan = getPlan(PLAN_UUID, PLAN_ID);
        var planMap = getPlanMap(List.of(plan));
        var chargeLedgerAccountsMap = getChargeLedgerAccountsMap(CHARGE_ID);
        InvoiceItem invoiceItem = getInvoiceItem(CHARGE_ID, AMOUNT, TAX_AMOUNT);
        Invoice invoice = getInvoice(List.of(invoiceItem));

        var journalEntryAndBalances = invoicePostedJournalProcessor.createJournalEntry(
            event,
            invoice,
            chargeMap,
            chargeLedgerAccountsMap,
            planMap,
            Map.of(recognitionRule.getRuleId(), recognitionRule),
            runningBalanceContext
        );

        var journalEntry = journalEntryAndBalances.getJournalEntry();
        Assertions.assertThat(journalEntryAndBalances.getBalances()).isEmpty();
        Assertions.assertThat(journalEntry.getJournalLines().size()).isEqualTo(3);
        Assertions.assertThat(journalEntry.getSourceEventId()).isEqualTo(EVENT_ID);
        Assertions.assertThat(journalEntry.getEntityId()).isEqualTo(ENTITY_ID);

        JournalLine accountsReceivable = getJournalLineByType(journalEntry, ACCOUNTS_RECEIVABLE);
        checkJournalLine(accountsReceivable, EntryType.DEBIT, AMOUNT.add(TAX_AMOUNT));

        JournalLine recognizedRevenue = getJournalLineByType(journalEntry, DEFERRED_REVENUE);
        checkJournalLine(recognizedRevenue, EntryType.CREDIT, AMOUNT);

        JournalLine taxLiability = getJournalLineByType(journalEntry, TAX_LIABILITY);
        checkJournalLine(taxLiability, EntryType.CREDIT, TAX_AMOUNT);
    }

    @Test
    public void invoiceItemWithoutTaxRevRecEnabled() {
        AccountingEvent event = getEvent();
        RecognitionRule recognitionRule = new MockRecognitionRuleBuilder().withSource(RecognitionSource.INVOICE).build();
        Charge charge = getCharge(CHARGE_ID, PLAN_ID, PLAN_UUID, recognitionRule.getRuleId());
        var chargeMap = getChargeMap(List.of(charge));
        Plan plan = getPlan(PLAN_UUID, PLAN_ID);
        var planMap = getPlanMap(List.of(plan));
        var chargeLedgerAccountsMap = getChargeLedgerAccountsMap(CHARGE_ID);
        InvoiceItem invoiceItem = getInvoiceItem(CHARGE_ID, AMOUNT, BigDecimal.ZERO);
        Invoice invoice = getInvoice(List.of(invoiceItem));

        var journalEntryAndBalances = invoicePostedJournalProcessor.createJournalEntry(
            event,
            invoice,
            chargeMap,
            chargeLedgerAccountsMap,
            planMap,
            Map.of(recognitionRule.getRuleId(), recognitionRule),
            runningBalanceContext
        );

        JournalEntry journalEntry = journalEntryAndBalances.getJournalEntry();
        Assertions.assertThat(journalEntryAndBalances.getBalances()).isEmpty();
        Assertions.assertThat(journalEntry.getJournalLines().size()).isEqualTo(2);
        Assertions.assertThat(journalEntry.getSourceEventId()).isEqualTo(EVENT_ID);
        Assertions.assertThat(journalEntry.getEntityId()).isEqualTo(ENTITY_ID);

        JournalLine accountsReceivable = getJournalLineByType(journalEntry, ACCOUNTS_RECEIVABLE);
        checkJournalLine(accountsReceivable, EntryType.DEBIT, AMOUNT);

        JournalLine recognizedRevenue = getJournalLineByType(journalEntry, DEFERRED_REVENUE);
        checkJournalLine(recognizedRevenue, EntryType.CREDIT, AMOUNT);

        List<JournalLine> taxLiabilityLines = getJournalLineListByType(journalEntry, TAX_LIABILITY);
        Assertions.assertThat(taxLiabilityLines.size()).isEqualTo(0);
    }

    @Test
    // 2 invoice items, 1 with tax 1 without. 1 charge contains recognition rule 1 does not
    public void generateForInvoiceWithMultipleLines() {
        AccountingEvent event = getEvent();
        RecognitionRule recognitionRule = new MockRecognitionRuleBuilder().withSource(RecognitionSource.INVOICE).build();
        String charge2Id = "charge2";
        BigDecimal item2Amount = new BigDecimal("13.50");
        UUID plan2Id = UUID.randomUUID();
        Charge charge1 = getCharge(CHARGE_ID, PLAN_ID, PLAN_UUID, recognitionRule.getRuleId());
        Charge charge2 = getCharge(charge2Id, "PLAN-2222222", plan2Id, null);
        var chargeMap = getChargeMap(List.of(charge1, charge2));
        Plan plan1 = getPlan(PLAN_UUID, PLAN_ID);
        Plan plan2 = getPlan(plan2Id, "plan2");
        var planMap = getPlanMap(List.of(plan1, plan2));
        var chargeLedgerAccountsMap = getChargeLedgerAccountsMap(CHARGE_ID);
        chargeLedgerAccountsMap.putAll(getChargeLedgerAccountsMap(charge2Id));

        InvoiceItem invoiceItem1 = getInvoiceItem(CHARGE_ID, AMOUNT, TAX_AMOUNT);
        InvoiceItem invoiceItem2 = getInvoiceItem(charge2Id, item2Amount, null);
        Invoice invoice = getInvoice(List.of(invoiceItem1, invoiceItem2));

        var journalEntryAndBalances = invoicePostedJournalProcessor.createJournalEntry(
            event,
            invoice,
            chargeMap,
            chargeLedgerAccountsMap,
            planMap,
            Map.of(recognitionRule.getRuleId(), recognitionRule),
            runningBalanceContext
        );

        var journalEntry = journalEntryAndBalances.getJournalEntry();
        Assertions.assertThat(journalEntryAndBalances.getBalances()).isEmpty();
        Assertions.assertThat(journalEntry.getJournalLines().size()).isEqualTo(5);
        Assertions.assertThat(journalEntry.getSourceEventId()).isEqualTo(EVENT_ID);
        Assertions.assertThat(journalEntry.getEntityId()).isEqualTo(ENTITY_ID);

        var accountsReceivableLines = getJournalLineListByType(journalEntry, ACCOUNTS_RECEIVABLE);
        Assertions.assertThat(accountsReceivableLines.size()).isEqualTo(2);
        Assertions.assertThat(accountsReceivableLines.stream().map(JournalLine::getEntityId).findFirst().orElseThrow()).isEqualTo(ENTITY_ID);

        var recognizedRevenueLines = getJournalLineListByType(journalEntry, RECOGNIZED_REVENUE);
        Assertions.assertThat(recognizedRevenueLines.size()).isEqualTo(1);
        Assertions.assertThat(recognizedRevenueLines.stream().map(JournalLine::getEntityId).findFirst().orElseThrow()).isEqualTo(ENTITY_ID);

        var deferredRevenueLines = getJournalLineListByType(journalEntry, DEFERRED_REVENUE);
        Assertions.assertThat(deferredRevenueLines.size()).isEqualTo(1);
        Assertions.assertThat(deferredRevenueLines.stream().map(JournalLine::getEntityId).findFirst().orElseThrow()).isEqualTo(ENTITY_ID);

        var taxLiabilityLines = getJournalLineListByType(journalEntry, TAX_LIABILITY);
        Assertions.assertThat(taxLiabilityLines.size()).isEqualTo(1);
        Assertions.assertThat(taxLiabilityLines.stream().map(JournalLine::getEntityId).findFirst().orElseThrow()).isEqualTo(ENTITY_ID);
    }

    private static JournalLine getJournalLineByType(JournalEntry journalEntry, LedgerAccountType ledgerAccountType) {
        var journalLines = getJournalLineListByType(journalEntry, ledgerAccountType);
        Assertions.assertThat(journalLines.size()).isEqualTo(1);
        return journalLines.get(0);
    }

    private static List<JournalLine> getJournalLineListByType(JournalEntry journalEntry, LedgerAccountType ledgerAccountType) {
        return journalEntry
            .getJournalLines()
            .stream()
            .filter(journalLine -> journalLine.getLedgerAccountType() == ledgerAccountType)
            .collect(Collectors.toList());
    }

    private static void checkJournalLine(JournalLine journalLine, EntryType type, BigDecimal amount) {
        Assertions.assertThat(journalLine.getEntryType()).isEqualTo(type);
        Assertions.assertThat(journalLine.getAmount()).isEqualTo(amount);
        Assertions.assertThat(journalLine.getEntityId()).isEqualTo(ENTITY_ID);
    }

    private static Invoice getInvoice(List<InvoiceItem> invoiceItems) {
        return new Invoice.InvoiceBuilder()
            .entityId(ENTITY_ID)
            .currency(CURRENCY_CODE)
            .customerAccountId(UUID.randomUUID().toString())
            .invoiceNumber(new Invoice.Number(INVOICE_NUMBER))
            .invoiceItems(invoiceItems)
            .createInvoice();
    }

    private static InvoiceItem getInvoiceItem(String chargeId, BigDecimal amount, BigDecimal taxAmount) {
        var invoiceItemBuilder = new InvoiceItem.InvoiceItemBuilder()
            .entityId(ENTITY_ID)
            .invoiceLineNumber(UUID.randomUUID().toString())
            .planId(PLAN_ID)
            .chargeId(chargeId)
            .amount(amount);

        if (taxAmount != null) {
            invoiceItemBuilder.taxAmount(taxAmount);
        }

        return invoiceItemBuilder.createInvoiceItem();
    }

    private static Charge getCharge(String chargeId, String planId, UUID planUuid, String recognitionRuleId) {
        Charge charge = new Charge();

        charge.setChargeId(chargeId);
        charge.setPlanUuid(planUuid);
        charge.setPlanId(planId);
        charge.setRecognitionRuleId(recognitionRuleId);

        return charge;
    }

    private static Plan getPlan(UUID id, String planId) {
        Plan plan = new Plan();
        plan.setId(id);
        plan.setPlanId(planId);
        return plan;
    }

    private static Map<UUID, Plan> getPlanMap(List<Plan> plans) {
        return plans.stream().collect(Collectors.toMap(Plan::getId, Function.identity()));
    }

    private static Map<String, Charge> getChargeMap(List<Charge> charges) {
        return charges.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
    }

    private static Map<String, List<LedgerAccount>> getChargeLedgerAccountsMap(String chargeId) {
        Map<String, List<LedgerAccount>> chargeLedgerAccountsMap = new HashMap<>();

        List<LedgerAccount> ledgerAccounts = new ArrayList<>();
        ledgerAccounts.add(
            new LedgerAccount(UUID.randomUUID().toString(), ACCOUNTS_RECEIVABLE.getUserFriendlyName(), "10004", "", ACCOUNTS_RECEIVABLE, false)
        );
        ledgerAccounts.add(
            new LedgerAccount(UUID.randomUUID().toString(), DEFERRED_REVENUE.getUserFriendlyName(), "30004", "", DEFERRED_REVENUE, false)
        );
        ledgerAccounts.add(
            new LedgerAccount(UUID.randomUUID().toString(), RECOGNIZED_REVENUE.getUserFriendlyName(), "20004", "", RECOGNIZED_REVENUE, false)
        );
        ledgerAccounts.add(new LedgerAccount(UUID.randomUUID().toString(), TAX_LIABILITY.getUserFriendlyName(), "30010", "", TAX_LIABILITY, false));
        ledgerAccounts.add(new LedgerAccount(UUID.randomUUID().toString(), CASH.getUserFriendlyName(), "10005", "", CASH, false));

        chargeLedgerAccountsMap.put(chargeId, ledgerAccounts);

        return chargeLedgerAccountsMap;
    }

    private AccountingEvent getEvent() {
        return AccountingEvent.builder()
            .id(EVENT_ID)
            .sourceEventTimestamp(EVENT_TIME)
            .accountingDate(EVENT_TIME)
            .sourceTransactionId(UUID.randomUUID().toString())
            .sourceTransactionType(JournalEntry.TransactionType.INVOICE_POSTED)
            .build();
    }
}
