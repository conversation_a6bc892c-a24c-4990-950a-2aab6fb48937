package com.subskribe.billy.accounting.services;

import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.db.JournalEntryDAO;
import com.subskribe.billy.accounting.db.LedgerAccountDAO;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.model.LedgerAccountType;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.payment.services.PaymentBankAccountGetService;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class AccountingGetServiceTest {

    @Mock
    private LedgerAccountDAO ledgerAccountDAO;

    @Mock
    private JournalEntryDAO journalEntryDAO;

    @Mock
    private FeatureService featureService;

    @Mock
    private PaymentBankAccountGetService paymentBankAccountGetService;

    private AccountingGetService accountingGetService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        accountingGetService = new AccountingGetService(ledgerAccountDAO, paymentBankAccountGetService, journalEntryDAO, featureService);
    }

    @Test
    public void whenGetDefaultLedgerAccountTemplatesIsCalled_thenItReturnsTemplatesCorrectly() {
        List<LedgerAccount> defaultLedgerAccounts = accountingGetService.getDefaultLedgerAccountTemplates();

        Assertions.assertThat(defaultLedgerAccounts).isNotEmpty();

        Set<LedgerAccountType> availableDefaultTypes = Arrays.stream(LedgerAccountType.values())
            .filter(ledgerAccountType -> ledgerAccountType != LedgerAccountType.EXPENSE)
            .collect(Collectors.toSet());
        Assertions.assertThat(availableDefaultTypes).isEqualTo(
            defaultLedgerAccounts.stream().map(LedgerAccount::getAccountType).collect(Collectors.toSet())
        );

        Assertions.assertThat(defaultLedgerAccounts.stream().allMatch(LedgerAccount::isDefault)).isTrue();

        Assertions.assertThat(
            defaultLedgerAccounts.stream().allMatch(lg -> lg.getAccountCode().equals(lg.getAccountType().getDefaultAccountCode()))
        ).isTrue();

        Assertions.assertThat(defaultLedgerAccounts.stream().allMatch(lg -> lg.getLedgerAccountId() == null)).isTrue();
    }

    @Test
    public void whenLedgerAccountsForChargeExist_thenReturnMappedLedgerAccounts() {
        String chargeId = "charge123";
        LedgerAccount ledgerAccount1 = new LedgerAccount("1", "Account1", "Code1", "Description1", LedgerAccountType.DEFERRED_REVENUE, true);
        LedgerAccount ledgerAccount2 = new LedgerAccount("2", "Account2", "Code2", "Description2", LedgerAccountType.RECOGNIZED_REVENUE, true);
        LedgerAccount chargeLedgerAccount1 = new LedgerAccount(
            "3",
            "ChargeAccount",
            "Code3",
            "Description3",
            LedgerAccountType.DEFERRED_REVENUE,
            false
        );
        LedgerAccount chargeLedgerAccount2 = new LedgerAccount("4", "ChargeAccount", "Code4", "Description4", LedgerAccountType.TAX_LIABILITY, true);

        when(ledgerAccountDAO.getDefaultLedgerAccounts()).thenReturn(Arrays.asList(ledgerAccount1, ledgerAccount2, chargeLedgerAccount2));
        when(ledgerAccountDAO.getLedgerAccountsForCharge(chargeId)).thenReturn(Arrays.asList(chargeLedgerAccount1, chargeLedgerAccount2));

        List<LedgerAccount> result = accountingGetService.getLedgerAccountsForCharge(chargeId);

        Assertions.assertThat(result).containsExactlyInAnyOrder(chargeLedgerAccount1, chargeLedgerAccount2, ledgerAccount2);
    }

    @Test
    public void whenLedgerAccountsForChargeExistInRandomOrder_thenReturnMappedLedgerAccountsInAnyOrder() {
        String chargeId = "charge123";
        LedgerAccount ledgerAccount1 = new LedgerAccount("1", "Account1", "Code1", "Description1", LedgerAccountType.DEFERRED_REVENUE, true);
        LedgerAccount ledgerAccount2 = new LedgerAccount("2", "Account2", "Code2", "Description2", LedgerAccountType.RECOGNIZED_REVENUE, true);
        LedgerAccount ledgerAccount3 = new LedgerAccount("3", "Account3", "Code2", "Description2", LedgerAccountType.CONTRACT_ASSET, true);
        LedgerAccount chargeLedgerAccount = new LedgerAccount("3", "ChargeAccount", "Code3", "Description3", LedgerAccountType.CONTRACT_ASSET, false);

        when(ledgerAccountDAO.getDefaultLedgerAccounts()).thenReturn(Arrays.asList(ledgerAccount3, ledgerAccount2, ledgerAccount1));
        when(ledgerAccountDAO.getLedgerAccountsForCharge(chargeId)).thenReturn(List.of(chargeLedgerAccount));

        List<LedgerAccount> result = accountingGetService.getLedgerAccountsForCharge(chargeId);

        Assertions.assertThat(result).containsExactlyInAnyOrder(chargeLedgerAccount, ledgerAccount2, ledgerAccount1);
    }

    @Test
    public void whenNoLedgerAccountsExist_thenReturnEmptyList() {
        String chargeId = "charge123";

        when(ledgerAccountDAO.getDefaultLedgerAccounts()).thenReturn(List.of());

        List<LedgerAccount> result = accountingGetService.getLedgerAccountsForCharge(chargeId);

        Assertions.assertThat(result).isEmpty();
    }

    @Test
    public void whenNoChargeLedgerAccountsExist_thenReturnDefaultLedgerAccounts() {
        String chargeId = "charge123";
        LedgerAccount ledgerAccount1 = new LedgerAccount("1", "Account1", "Code1", "Description1", LedgerAccountType.DEFERRED_REVENUE, true);
        LedgerAccount ledgerAccount2 = new LedgerAccount("2", "Account2", "Code2", "Description2", LedgerAccountType.RECOGNIZED_REVENUE, true);

        when(ledgerAccountDAO.getDefaultLedgerAccounts()).thenReturn(Arrays.asList(ledgerAccount1, ledgerAccount2));
        when(ledgerAccountDAO.getLedgerAccountsForCharge(chargeId)).thenReturn(List.of());

        List<LedgerAccount> result = accountingGetService.getLedgerAccountsForCharge(chargeId);

        Assertions.assertThat(result).containsExactlyInAnyOrder(ledgerAccount1, ledgerAccount2);
    }
}
