package com.subskribe.billy.accounting.services;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.db.JournalEntryDAO;
import com.subskribe.billy.accounting.db.LedgerAccountDAO;
import com.subskribe.billy.accounting.model.LedgerAccount;
import com.subskribe.billy.accounting.model.LedgerAccountType;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.payment.services.PaymentBankAccountGetService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Arrays;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class AccountingServiceTest {

    @Mock
    private AccountingGetService accountingGetService;

    @Mock
    private LedgerAccountDAO ledgerAccountDAO;

    @Mock
    private JournalEntryDAO journalEntryDAO;

    @Mock
    private LedgerAccountIdGenerator ledgerAccountIdGenerator;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private DSLContextProvider dslContextProvider;

    @Mock
    private ProductCatalogGetService productCatalogGetService;

    @Mock
    private PaymentBankAccountGetService paymentBankAccountGetService;

    @Mock
    private FeatureService featureService;

    @Mock
    private EntityContextProvider entityContextProvider;

    private AccountingService accountingService;

    private static final List<LedgerAccountType> SUPPORTED_CHARGE_LEDGER_ACCOUNT_MAPPINGS = List.of(
        LedgerAccountType.TAX_LIABILITY,
        LedgerAccountType.DEFERRED_REVENUE,
        LedgerAccountType.RECOGNIZED_REVENUE,
        LedgerAccountType.CONTRACT_ASSET
    );

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        accountingService = new AccountingService(
            accountingGetService,
            ledgerAccountDAO,
            journalEntryDAO,
            ledgerAccountIdGenerator,
            tenantIdProvider,
            dslContextProvider,
            productCatalogGetService,
            paymentBankAccountGetService,
            featureService,
            entityContextProvider
        );
    }

    @Test
    public void whenDuplicateLedgerAccountIdsProvided_thenThrowInvalidInputException() {
        String chargeId = "charge123";
        List<String> duplicateLedgerAccountIds = Arrays.asList("account1", "account2", "account1");

        Assertions.assertThatThrownBy(() -> accountingService.mapLedgerAccountsToCharge(chargeId, duplicateLedgerAccountIds))
            .isInstanceOf(InvalidInputException.class)
            .hasMessage("Duplicate ledger account ids are not allowed during mapping");
    }

    @Test
    public void whenUnsupportedLedgerAccountTypeProvided_thenThrowInvalidInputException() {
        String chargeId = "charge123";
        List<String> ledgerAccountIds = Arrays.asList("account1", "account2");

        LedgerAccount unsupportedAccount = new LedgerAccount(
            "account1",
            "Account1",
            "Code1",
            "Description1",
            LedgerAccountType.CASH, // Unsupported type
            false
        );
        LedgerAccount supportedAccount = new LedgerAccount("account2", "Account2", "Code2", "Description2", LedgerAccountType.TAX_LIABILITY, false);

        when(accountingGetService.getLedgerAccountsByIds(anyList())).thenReturn(Arrays.asList(unsupportedAccount, supportedAccount));
        when(productCatalogGetService.getChargeByChargeId(any())).thenReturn(new Charge());

        Assertions.assertThatThrownBy(() -> accountingService.mapLedgerAccountsToCharge(chargeId, ledgerAccountIds))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("Unsupported ledger account type")
            .hasMessageContaining("CASH")
            .hasMessageContaining("Supported types are " + SUPPORTED_CHARGE_LEDGER_ACCOUNT_MAPPINGS);
    }
}
