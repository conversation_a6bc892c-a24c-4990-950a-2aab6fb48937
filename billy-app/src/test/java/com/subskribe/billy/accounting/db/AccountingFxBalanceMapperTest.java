package com.subskribe.billy.accounting.db;

import com.subskribe.billy.accounting.model.AccountingFxBalance;
import com.subskribe.billy.accounting.model.ImmutableAccountingFxBalance;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountingFxBalanceRecord;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

public class AccountingFxBalanceMapperTest {

    @Test
    public void testAccountingFxBalanceMapper() {
        AccountingFxBalance accountingFxBalance = mockAccountingFxBalance();
        JournalEntryMapper journalEntryMapper = Mappers.getMapper(JournalEntryMapper.class);
        AccountingFxBalanceRecord record = journalEntryMapper.toFxBalanceRecord(accountingFxBalance);
        AccountingFxBalance mappedAccountingFxBalance = journalEntryMapper.fromFxBalanceRecord(record);
        Assertions.assertThat(mappedAccountingFxBalance).isEqualTo(accountingFxBalance);
    }

    private AccountingFxBalance mockAccountingFxBalance() {
        return ImmutableAccountingFxBalance.builder()
            .id(UUID.randomUUID().toString())
            .runningBalanceId(UUID.randomUUID().toString())
            .balance(new BigDecimal("123.45"))
            .exchangeRate(new BigDecimal("1.23"))
            .exchangeRateId(UUID.randomUUID().toString())
            .exchangeRateDate(Instant.now())
            .functionalBalance(new BigDecimal("123.45"))
            .balanceSequenceNumber(1L)
            .build();
    }
}
