package com.subskribe.billy.usage.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.usage.db.UsageDAO;
import com.subskribe.billy.usage.model.ImmutableUsageAggregationKey;
import com.subskribe.billy.usage.model.RawUsage;
import com.subskribe.billy.usage.model.UsageAggregate;
import com.subskribe.billy.usage.model.UsageAggregationConfiguration;
import com.subskribe.billy.usage.model.UsageAggregationKey;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class UsageAggregationServiceTest {

    private static final String SUBSCRIPTION1 = "SUB1";
    private static final String SUBSCRIPTION2 = "SUB2";

    private static final String SUBSCRIPTION1_GROUP1 = "1GRP1";
    private static final String SUBSCRIPTION1_GROUP2 = "1GRP2";

    private static final String SUBSCRIPTION2_GROUP1 = "2GRP1";

    private static final UsageAggregationConfiguration TEST_CONFIGURATION = testConfiguration();

    private static final UsageAggregationKey SUB1_GRP1_KEY = ImmutableUsageAggregationKey.builder()
        .subscriptionId(SUBSCRIPTION1)
        .subscriptionChargeGroupId(SUBSCRIPTION1_GROUP1)
        .build();

    private static final UsageAggregationKey SUB1_GRP2_KEY = ImmutableUsageAggregationKey.builder()
        .subscriptionId(SUBSCRIPTION1)
        .subscriptionChargeGroupId(SUBSCRIPTION1_GROUP2)
        .build();

    private static final UsageAggregationKey SUB2_GRP1_KEY = ImmutableUsageAggregationKey.builder()
        .subscriptionId(SUBSCRIPTION2)
        .subscriptionChargeGroupId(SUBSCRIPTION2_GROUP1)
        .build();

    private static final Instant METERING_HOUR_23_10 = zonedTimeInstant("2022-02-23T10:00-08:00");
    private static final Instant METERING_HOUR_23_11 = zonedTimeInstant("2022-02-23T11:00-08:00");
    private static final Instant METERING_HOUR_21_05 = zonedTimeInstant("2022-02-21T05:00-08:00");
    private static final Instant METERING_HOUR_21_11 = zonedTimeInstant("2022-02-21T11:00-08:00");

    private static final Instant ARRIVAL_TIME_LATEST = Instant.now();

    private static final List<RawUsage> TEST_RAW_USAGE = List.of(
        makeSampleRecord(SUBSCRIPTION1, SUBSCRIPTION1_GROUP1, usageTime("2022-02-23T10:29:49-08:00"), ARRIVAL_TIME_LATEST, new BigDecimal(10)),
        // all records below arrived before the first one for test purposes
        makeSampleRecord(
            SUBSCRIPTION1,
            SUBSCRIPTION1_GROUP1,
            usageTime("2022-02-23T10:50:55-08:00"),
            ARRIVAL_TIME_LATEST.minusSeconds(10),
            new BigDecimal(20)
        ),
        makeSampleRecord(
            SUBSCRIPTION1,
            SUBSCRIPTION1_GROUP2,
            usageTime("2022-02-23T11:10:55-08:00"),
            ARRIVAL_TIME_LATEST.minusSeconds(10),
            new BigDecimal(5)
        ),
        makeSampleRecord(
            SUBSCRIPTION2,
            SUBSCRIPTION2_GROUP1,
            usageTime("2022-02-21T05:10:55-08:00"),
            ARRIVAL_TIME_LATEST.minusSeconds(20),
            new BigDecimal(25)
        ),
        makeSampleRecord(
            SUBSCRIPTION2,
            SUBSCRIPTION2_GROUP1,
            usageTime("2022-02-21T05:20:55-08:00"),
            ARRIVAL_TIME_LATEST.minusSeconds(20),
            new BigDecimal(75)
        ),
        makeSampleRecord(
            SUBSCRIPTION2,
            SUBSCRIPTION2_GROUP1,
            usageTime("2022-02-21T11:13:26-08:00"),
            ARRIVAL_TIME_LATEST.minusSeconds(20),
            new BigDecimal(75)
        )
    );

    @Mock
    private UsageDAO usageDAO;

    private final TenantSettingService tenantSettingService = TenantSettingServiceFixture.getDefault();

    @Mock
    private BillyConfiguration mockConfig;

    @Captor
    ArgumentCaptor<List<UsageAggregate>> aggregatesCaptor;

    @Captor
    ArgumentCaptor<List<RawUsage>> rawUsagesCaptor;

    @Captor
    ArgumentCaptor<Instant> expectedCheckpointCaptor;

    @Captor
    ArgumentCaptor<Instant> newCheckpointCaptor;

    private UsageAggregationService usageAggregationService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        when(mockConfig.getUsageAggregationConfiguration()).thenReturn(TEST_CONFIGURATION);

        usageAggregationService = new UsageAggregationService(usageDAO, mockConfig);
    }

    @Test
    public void whenAggregatedUsageIsPassed_thenAggregationFails() {
        RawUsage badRawUsage = makeSampleRecord(
            SUBSCRIPTION1,
            SUBSCRIPTION1_GROUP1,
            usageTime("2022-02-23T10:50:55-08:00"),
            ARRIVAL_TIME_LATEST.minusSeconds(10),
            new BigDecimal(20)
        );
        badRawUsage.setIsAggregated(true);

        when(usageDAO.getRawUsageForAggregation(anyInt(), any(Instant.class), any(Instant.class))).thenReturn(List.of(badRawUsage));
        Assertions.assertThatThrownBy(() -> usageAggregationService.aggregateRawUsageAndPersist())
            .isInstanceOf(ServiceFailureException.class)
            .hasMessage("aggregated usage found when querying for un-aggregated RAW usage");
    }

    @Test
    public void whenNoUsageRecordsArePresent_thenMethodDoesNothing() {
        when(usageDAO.getRawUsageArrivalTimeCheckpoint()).thenReturn(Optional.empty());
        when(usageDAO.getRawUsageForAggregation(anyInt(), any(Instant.class), any(Instant.class))).thenReturn(Collections.emptyList());

        usageAggregationService.aggregateRawUsageAndPersist();

        verify(tenantSettingService, never()).getTenantSettingInternal();
        verify(usageDAO, never()).writeAggregateUsage(any(), any(), any(), any());
    }

    @Test
    public void whenRawUsageIsPresent_thenItIsAggregatedProperly() {
        when(usageDAO.getRawUsageArrivalTimeCheckpoint()).thenReturn(Optional.empty());
        when(usageDAO.getRawUsageForAggregation(anyInt(), any(Instant.class), any(Instant.class))).thenReturn(TEST_RAW_USAGE);
        usageAggregationService.aggregateRawUsageAndPersist();

        verify(usageDAO).writeAggregateUsage(
            aggregatesCaptor.capture(),
            rawUsagesCaptor.capture(),
            expectedCheckpointCaptor.capture(),
            newCheckpointCaptor.capture()
        );

        var usageAggregates = aggregatesCaptor.getValue();
        var rawUsages = rawUsagesCaptor.getValue();
        Instant expectedCheckpoint = expectedCheckpointCaptor.getValue();
        Instant newCheckpoint = newCheckpointCaptor.getValue();

        // we are expecting 4 aggregates for
        // SUB1, 1GRP1 - single metering hour
        // SUB1, 1GRP2 - single metering hour
        // SUB2, 2GRP1 - 2 metering hours
        Assertions.assertThat(usageAggregates).hasSize(4);

        // the raw usage list should be passed in un-molested
        Assertions.assertThat(rawUsages).isEqualTo(TEST_RAW_USAGE);

        Assertions.assertThat(expectedCheckpoint).isNull();

        // ne checkpoint should be latest of the raw usage batch
        Assertions.assertThat(newCheckpoint).isEqualTo(ARRIVAL_TIME_LATEST);

        var aggregatesByKey = usageAggregates.stream().collect(Collectors.groupingBy(UsageAggregate::getUsageAggregationKey));

        // 3 key entries for each subscription and groupId combo
        // SUB1, 1GRP1 - single metering hour
        // SUB1, 1GRP2 - single metering hour
        // SUB2, 2GRP1 - 2 metering hours
        Assertions.assertThat(aggregatesByKey).hasSize(3);

        verifyAggregateSUB1GRP1(aggregatesByKey);
        verifyAggregateSUB1GRP2(aggregatesByKey);
        verifyAggregateSUB2GRP1(aggregatesByKey);
    }

    private void verifyAggregateSUB1GRP1(Map<UsageAggregationKey, List<UsageAggregate>> aggregatesByKey) {
        var aggregateOneList = aggregatesByKey.get(SUB1_GRP1_KEY);
        Assertions.assertThat(aggregateOneList).hasSize(1);
        var aggregateOne = aggregateOneList.get(0);
        Assertions.assertThat(aggregateOne.getStartAt()).isEqualTo(METERING_HOUR_23_10);
        Assertions.assertThat(aggregateOne.getEndAt()).isEqualTo(METERING_HOUR_23_10.plus(1, ChronoUnit.HOURS));
        Assertions.assertThat(aggregateOne.getObservedDataPoints()).isEqualTo(2);
        Assertions.assertThat(aggregateOne.getUsageSum()).isEqualTo(new BigDecimal(10 + 20));
        Assertions.assertThat(aggregateOne.getUsageMax()).isEqualTo(new BigDecimal(20));
        Assertions.assertThat(aggregateOne.getUsageMin()).isEqualTo(new BigDecimal(10));
        Assertions.assertThat(aggregateOne.getUsageAggregationKey()).isEqualTo(SUB1_GRP1_KEY);
    }

    private void verifyAggregateSUB1GRP2(Map<UsageAggregationKey, List<UsageAggregate>> aggregatesByKey) {
        var aggregateOneList = aggregatesByKey.get(SUB1_GRP2_KEY);
        Assertions.assertThat(aggregateOneList).hasSize(1);
        var aggregateOne = aggregateOneList.get(0);
        Assertions.assertThat(aggregateOne.getStartAt()).isEqualTo(METERING_HOUR_23_11);
        Assertions.assertThat(aggregateOne.getEndAt()).isEqualTo(METERING_HOUR_23_11.plus(1, ChronoUnit.HOURS));
        Assertions.assertThat(aggregateOne.getObservedDataPoints()).isEqualTo(1);
        Assertions.assertThat(aggregateOne.getUsageSum()).isEqualTo(new BigDecimal(5));
        Assertions.assertThat(aggregateOne.getUsageMax()).isEqualTo(new BigDecimal(5));
        Assertions.assertThat(aggregateOne.getUsageMin()).isEqualTo(new BigDecimal(5));
        Assertions.assertThat(aggregateOne.getUsageAggregationKey()).isEqualTo(SUB1_GRP2_KEY);
    }

    private void verifyAggregateSUB2GRP1(Map<UsageAggregationKey, List<UsageAggregate>> aggregatesByKey) {
        var aggregateOneList = aggregatesByKey.get(SUB2_GRP1_KEY);
        Assertions.assertThat(aggregateOneList).hasSize(2);
        var aggregateOne = aggregateOneList.get(0);
        Assertions.assertThat(aggregateOne.getStartAt()).isEqualTo(METERING_HOUR_21_05);
        Assertions.assertThat(aggregateOne.getEndAt()).isEqualTo(METERING_HOUR_21_05.plus(1, ChronoUnit.HOURS));
        Assertions.assertThat(aggregateOne.getObservedDataPoints()).isEqualTo(2);
        Assertions.assertThat(aggregateOne.getUsageSum()).isEqualTo(new BigDecimal(75 + 25));
        Assertions.assertThat(aggregateOne.getUsageMax()).isEqualTo(new BigDecimal(75));
        Assertions.assertThat(aggregateOne.getUsageMin()).isEqualTo(new BigDecimal(25));
        Assertions.assertThat(aggregateOne.getUsageAggregationKey()).isEqualTo(SUB2_GRP1_KEY);

        var aggregateTwo = aggregateOneList.get(1);
        Assertions.assertThat(aggregateTwo.getStartAt()).isEqualTo(METERING_HOUR_21_11);
        Assertions.assertThat(aggregateTwo.getEndAt()).isEqualTo(METERING_HOUR_21_11.plus(1, ChronoUnit.HOURS));
        Assertions.assertThat(aggregateTwo.getObservedDataPoints()).isEqualTo(1);
        Assertions.assertThat(aggregateTwo.getUsageSum()).isEqualTo(new BigDecimal(75));
        Assertions.assertThat(aggregateTwo.getUsageMax()).isEqualTo(new BigDecimal(75));
        Assertions.assertThat(aggregateTwo.getUsageMin()).isEqualTo(new BigDecimal(75));
        Assertions.assertThat(aggregateTwo.getUsageAggregationKey()).isEqualTo(SUB2_GRP1_KEY);
    }

    public static Instant usageTime(String time) {
        return ZonedDateTime.parse(time).toInstant();
    }

    private static Instant zonedTimeInstant(String time) {
        return ZonedDateTime.parse(time).toInstant();
    }

    public static RawUsage makeSampleRecord(
        String subscriptionId,
        String chargeGroupId,
        Instant usageTime,
        Instant arrivalTime,
        BigDecimal quantity
    ) {
        RawUsage usageRecord = new RawUsage();
        usageRecord.setSubscriptionId(subscriptionId);
        usageRecord.setSubscriptionChargeGroupId(chargeGroupId);
        usageRecord.setUsageTime(usageTime);
        usageRecord.setArrivalTime(arrivalTime);
        usageRecord.setUsageQuantity(quantity.longValue());
        usageRecord.setIsAggregated(false);
        return usageRecord;
    }

    private static UsageAggregationConfiguration testConfiguration() {
        UsageAggregationConfiguration configuration = new UsageAggregationConfiguration();
        configuration.setRawUsageAggregationBatchSize(1000);
        configuration.setNtpDriftFactor("PT1M");
        return configuration;
    }
}
