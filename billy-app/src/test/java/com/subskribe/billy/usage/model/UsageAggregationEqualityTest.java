package com.subskribe.billy.usage.model;

import static com.subskribe.billy.usage.service.UsageAggregationServiceTest.makeSampleRecord;
import static com.subskribe.billy.usage.service.UsageAggregationServiceTest.usageTime;

import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class UsageAggregationEqualityTest {

    private static final String PRICE_ATTRIBUTE_DEF_ID_ONE = "PATTRB-123ABC5";

    private static final String PRICE_ATTRIBUTE_VAL_ONE = "Office";

    private static final String PRICE_ATTRIBUTE_VAL_TWO = "MAX";

    private static final String PRICE_ATTRIBUTE_DEF_ID_TWO = "PATTRB-ABC123A";

    private static final String PRICE_ATTRIBUTE_VAL_THREE = "Tier1";
    public static final List<AttributeReference> ORDER_ONE_ATTRIBUTE_REFERENCE = makeAttributeReferences(
        PRICE_ATTRIBUTE_DEF_ID_ONE,
        PRICE_ATTRIBUTE_VAL_ONE,
        PRICE_ATTRIBUTE_DEF_ID_TWO,
        PRICE_ATTRIBUTE_VAL_THREE
    );
    public static final List<AttributeReference> ORDER_TWO_ATTRIBUTE_REFERENCE = makeAttributeReferences(
        PRICE_ATTRIBUTE_DEF_ID_TWO,
        PRICE_ATTRIBUTE_VAL_THREE,
        PRICE_ATTRIBUTE_DEF_ID_ONE,
        PRICE_ATTRIBUTE_VAL_ONE
    );

    public static final List<AttributeReference> ORDER_THREE_ATTRIBUTE_REFERENCE = makeAttributeReferences(
        PRICE_ATTRIBUTE_DEF_ID_ONE,
        PRICE_ATTRIBUTE_VAL_TWO,
        PRICE_ATTRIBUTE_DEF_ID_TWO,
        PRICE_ATTRIBUTE_VAL_THREE
    );

    @Test
    public void whenRawAttributeReferencesAreNotPresent_thenTheyGetOrderedByUsageAggregationKeyCorrectly() {
        String sub1 = "sub1";
        String subChargeGroupId1 = "sub-group-id1";

        RawUsage rawUsageOne = makeSampleRecord(
            sub1,
            subChargeGroupId1,
            usageTime("2022-02-23T10:50:55-08:00"),
            Instant.now().minusSeconds(10),
            new BigDecimal(20)
        );

        RawUsage rawUsageTwo = makeSampleRecord(
            sub1,
            subChargeGroupId1,
            usageTime("2022-02-23T10:50:57-08:00"),
            Instant.now().minusSeconds(10),
            new BigDecimal(20)
        );

        Map<UsageAggregationKey, List<RawUsage>> aggregatedByKey = Stream.of(rawUsageOne, rawUsageTwo).collect(
            Collectors.groupingBy(rawUsage ->
                ImmutableUsageAggregationKey.builder()
                    .subscriptionId(rawUsage.getSubscriptionId())
                    .subscriptionChargeGroupId(rawUsage.getSubscriptionChargeGroupId())
                    .attributeReferences(AttributeReferences.wrap(rawUsage.getAttributeReferences()))
                    .build()
            )
        );

        Assertions.assertThat(aggregatedByKey).hasSize(1);
        Assertions.assertThat(aggregatedByKey.keySet()).hasSize(1);
        UsageAggregationKey key = aggregatedByKey.keySet().stream().findFirst().orElseThrow();
        Assertions.assertThat(key).isNotNull();
        Assertions.assertThat(key.getAttributeReferences()).isNull();

        // values should have
        Assertions.assertThat(aggregatedByKey.get(key)).hasSize(2);
    }

    @Test
    public void whenOneRawAttributeReferencesArePresent_thenTheyGetOrderedByUsageAggregationKeyThatIsDifferent() {
        String sub1 = "sub1";
        String subChargeGroupId1 = "sub-group-id1";

        RawUsage rawUsageOne = makeSampleRecord(
            sub1,
            subChargeGroupId1,
            usageTime("2022-02-23T10:50:55-08:00"),
            Instant.now().minusSeconds(10),
            new BigDecimal(20)
        );
        rawUsageOne.setAttributeReferences(ORDER_ONE_ATTRIBUTE_REFERENCE);

        RawUsage rawUsageTwo = makeSampleRecord(
            sub1,
            subChargeGroupId1,
            usageTime("2022-02-23T10:50:57-08:00"),
            Instant.now().minusSeconds(10),
            new BigDecimal(20)
        );

        Map<UsageAggregationKey, List<RawUsage>> aggregatedByKey = Stream.of(rawUsageOne, rawUsageTwo).collect(
            Collectors.groupingBy(rawUsage ->
                ImmutableUsageAggregationKey.builder()
                    .subscriptionId(rawUsage.getSubscriptionId())
                    .subscriptionChargeGroupId(rawUsage.getSubscriptionChargeGroupId())
                    .attributeReferences(AttributeReferences.wrap(rawUsage.getAttributeReferences()))
                    .build()
            )
        );

        // there should be only one KEY
        Assertions.assertThat(aggregatedByKey).hasSize(2);
        Assertions.assertThat(aggregatedByKey.keySet()).hasSize(2);

        Iterator<UsageAggregationKey> aggregationKeyIterator = aggregatedByKey.keySet().iterator();
        UsageAggregationKey keyOne = aggregationKeyIterator.next();
        UsageAggregationKey keyTwo = aggregationKeyIterator.next();

        Assertions.assertThat(aggregatedByKey.get(keyOne)).hasSize(1);
        Assertions.assertThat(aggregatedByKey.get(keyTwo)).hasSize(1);

        Assertions.assertThat(
            keyOne.getAttributeReferences() == null || keyOne.getAttributeReferences().equals(AttributeReferences.wrap(ORDER_ONE_ATTRIBUTE_REFERENCE))
        ).isTrue();

        Assertions.assertThat(
            keyTwo.getAttributeReferences() == null || keyTwo.getAttributeReferences().equals(AttributeReferences.wrap(ORDER_ONE_ATTRIBUTE_REFERENCE))
        ).isTrue();
    }

    @Test
    public void whenRawUsageAttributeReferencesAreUnOrdered_theyGetOrderedByUsageAggregationKey() {
        String sub1 = "sub1";
        String subChargeGroupId1 = "sub-group-id1";

        RawUsage rawUsageOne = makeSampleRecord(
            sub1,
            subChargeGroupId1,
            usageTime("2022-02-23T10:50:55-08:00"),
            Instant.now().minusSeconds(10),
            new BigDecimal(20)
        );
        rawUsageOne.setAttributeReferences(ORDER_ONE_ATTRIBUTE_REFERENCE);

        RawUsage rawUsageTwo = makeSampleRecord(
            sub1,
            subChargeGroupId1,
            usageTime("2022-02-23T10:50:57-08:00"),
            Instant.now().minusSeconds(10),
            new BigDecimal(20)
        );
        rawUsageTwo.setAttributeReferences(ORDER_TWO_ATTRIBUTE_REFERENCE);

        Map<UsageAggregationKey, List<RawUsage>> aggregatedByKey = Stream.of(rawUsageOne, rawUsageTwo).collect(
            Collectors.groupingBy(rawUsage ->
                ImmutableUsageAggregationKey.builder()
                    .subscriptionId(rawUsage.getSubscriptionId())
                    .subscriptionChargeGroupId(rawUsage.getSubscriptionChargeGroupId())
                    .attributeReferences(AttributeReferences.wrap(rawUsage.getAttributeReferences()))
                    .build()
            )
        );

        // there should be only one KEY
        Assertions.assertThat(aggregatedByKey).hasSize(1);
        Assertions.assertThat(aggregatedByKey.keySet()).hasSize(1);
        UsageAggregationKey key = aggregatedByKey.keySet().stream().findFirst().orElseThrow();
        Assertions.assertThat(key).isNotNull();
        Assertions.assertThat(key.getAttributeReferences()).isEqualTo(AttributeReferences.wrap(ORDER_ONE_ATTRIBUTE_REFERENCE));
        // values should have
        Assertions.assertThat(aggregatedByKey.get(key)).hasSize(2);
    }

    @Test
    public void whenRawUsageAttributeReferencesAreUnOrderedAndDifferent_theyGetOrderedByUsageAggregationKeyThatAreDifferent() {
        String sub1 = "sub1";
        String subChargeGroupId1 = "sub-group-id1";

        RawUsage rawUsageOne = makeSampleRecord(
            sub1,
            subChargeGroupId1,
            usageTime("2022-02-23T10:50:55-08:00"),
            Instant.now().minusSeconds(10),
            new BigDecimal(20)
        );
        rawUsageOne.setAttributeReferences(ORDER_ONE_ATTRIBUTE_REFERENCE);

        RawUsage rawUsageTwo = makeSampleRecord(
            sub1,
            subChargeGroupId1,
            usageTime("2022-02-23T10:50:57-08:00"),
            Instant.now().minusSeconds(10),
            new BigDecimal(20)
        );
        rawUsageTwo.setAttributeReferences(ORDER_THREE_ATTRIBUTE_REFERENCE);

        Map<UsageAggregationKey, List<RawUsage>> aggregatedByKey = Stream.of(rawUsageOne, rawUsageTwo).collect(
            Collectors.groupingBy(rawUsage ->
                ImmutableUsageAggregationKey.builder()
                    .subscriptionId(rawUsage.getSubscriptionId())
                    .subscriptionChargeGroupId(rawUsage.getSubscriptionChargeGroupId())
                    .attributeReferences(AttributeReferences.wrap(rawUsage.getAttributeReferences()))
                    .build()
            )
        );

        // there should be only one KEY
        Assertions.assertThat(aggregatedByKey).hasSize(2);
        Assertions.assertThat(aggregatedByKey.keySet()).hasSize(2);

        Iterator<UsageAggregationKey> aggregationKeyIterator = aggregatedByKey.keySet().iterator();
        UsageAggregationKey keyOne = aggregationKeyIterator.next();
        UsageAggregationKey keyTwo = aggregationKeyIterator.next();

        Assertions.assertThat(aggregatedByKey.get(keyOne)).hasSize(1);
        Assertions.assertThat(aggregatedByKey.get(keyTwo)).hasSize(1);

        Assertions.assertThat(
            keyOne.getAttributeReferences().equals(AttributeReferences.wrap(ORDER_ONE_ATTRIBUTE_REFERENCE)) ||
            keyOne.getAttributeReferences().equals(AttributeReferences.wrap(ORDER_THREE_ATTRIBUTE_REFERENCE))
        ).isTrue();

        Assertions.assertThat(
            keyTwo.getAttributeReferences().equals(AttributeReferences.wrap(ORDER_ONE_ATTRIBUTE_REFERENCE)) ||
            keyTwo.getAttributeReferences().equals(AttributeReferences.wrap(ORDER_THREE_ATTRIBUTE_REFERENCE))
        ).isTrue();
    }

    private static List<AttributeReference> makeAttributeReferences(String... references) {
        if (references == null || references.length == 0 || references.length % 2 != 0) {
            throw new IllegalArgumentException("References should be of the form (id, value, id2, value2) and should have even arguments");
        }

        List<AttributeReference> finalList = new ArrayList<>(references.length / 2);
        int i = 0;
        while (i < references.length) {
            String attributeDefinitionId = references[i];
            String attributeValue = references[i + 1];
            finalList.add(AttributeReference.builder().attributeDefinitionId(attributeDefinitionId).attributeValue(attributeValue).build());
            i += 2;
        }
        return Collections.unmodifiableList(finalList);
    }
}
