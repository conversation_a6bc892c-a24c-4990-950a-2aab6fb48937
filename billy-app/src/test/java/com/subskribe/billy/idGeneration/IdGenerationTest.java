package com.subskribe.billy.idGeneration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.shared.Base30StringGen;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Test;

public class IdGenerationTest {

    @Test
    public void successfullyGenerateRequestedLengthId() {
        var idGenImpl = new IdGenerationImpl(7, 12, "TEST-");
        String id = idGenImpl.generate();
        assertEquals(12, id.length());
        assertTrue(id.startsWith("TEST-"));
    }

    @Test
    public void successfullyGenerateRequestedPlusOneLengthId() {
        var idGenImpl = new IdGenerationImpl(7, 13, "TEST-");
        String id = idGenImpl.generate();
        assertEquals(13, id.length());
        assertTrue(id.startsWith("TEST-"));
    }

    @Test
    public void successfullyGenerateRequestedPlusTwoLengthId() {
        var idGenImpl = new IdGenerationImpl(7, 14, "TEST-");
        String id = idGenImpl.generate();
        assertEquals(14, id.length());
        assertTrue(id.startsWith("TEST-"));
    }

    @Test
    public void successfullyGenerateRequestedPlusThreeLengthId() {
        var idGenImpl = new IdGenerationImpl(7, 15, "TEST-");
        String id = idGenImpl.generate();
        assertEquals(15, id.length());
        assertTrue(id.startsWith("TEST-"));
    }

    @Test
    public void failGeneratingId() {
        var idGenImpl = new IdGenerationImpl(7, 20, "TEST-");
        assertThrows(DuplicateIdException.class, idGenImpl::generate);
    }

    @Test
    public void TestBase30StringGenAndChecksum() {
        String generatedId = Base30StringGen.generate(36);
        assertEquals(36, generatedId.length());
        assertTrue(Base30StringGen.isValid(generatedId));
    }

    @Test
    public void TestBase30StringGenAndChecksumKnown() {
        assertTrue(Base30StringGen.isValid("1R"));
        assertTrue(Base30StringGen.isValid("21Q"));
        assertTrue(Base30StringGen.isValid("31V"));
        assertTrue(Base30StringGen.isValid("12G"));
        assertTrue(Base30StringGen.isValid("13J"));
        assertTrue(Base30StringGen.isValid("20D"));
    }

    @Test
    public void badWordTest() throws IOException {
        URL resource = Thread.currentThread().getContextClassLoader().getResource("com/subskribe/billy/shared/bad-words.txt");
        InputStream inputStream = Objects.requireNonNull(resource).openStream();
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        List<String> badWords = reader.lines().map(this::processLine).filter(Objects::nonNull).toList();
        assertFalse(badWords.isEmpty());
        assertTrue(badWords.size() < 40);
    }

    private String processLine(String line) {
        for (char lineChar : line.toUpperCase().toCharArray()) {
            if (Base30StringGen.characterTableDoesNotIncludeLineChar(lineChar)) {
                return null;
            }
        }
        return line;
    }
}
