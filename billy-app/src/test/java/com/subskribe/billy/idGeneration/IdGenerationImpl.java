package com.subskribe.billy.idGeneration;

import com.subskribe.billy.exception.DuplicateIdException;
import com.subskribe.billy.shared.IdGeneratorBase;
import org.apache.commons.lang3.StringUtils;

public class IdGenerationImpl extends IdGeneratorBase {

    private final int acceptLength;

    public IdGenerationImpl(int initialLength, int acceptLength, String prefix) {
        super(prefix, initialLength);
        this.acceptLength = acceptLength;
    }

    @Override
    public void verifyUniqueId(String id) {
        if (StringUtils.isBlank(id) || (id.length() != acceptLength) || !id.startsWith(prefix)) {
            throw new DuplicateIdException("invalid id generated. Id: " + id);
        }
    }
}
