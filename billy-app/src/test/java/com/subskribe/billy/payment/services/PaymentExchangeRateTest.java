package com.subskribe.billy.payment.services;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.foreignexchange.fixture.ExchangeRateServicesFixture;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateService;
import com.subskribe.billy.payment.db.PaymentDAO;
import com.subskribe.billy.payment.fixture.PaymentFixture;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Clock;
import org.assertj.core.api.Assertions;
import org.jooq.DSLContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

public class PaymentExchangeRateTest {

    private static final BigDecimal EUR_USD_EXCHANGE_RATE = BigDecimal.valueOf(1.234);

    @Mock
    private PaymentDAO paymentDAO;

    @Mock
    private PaymentIdGenerator paymentIdGenerator;

    @Mock
    private Clock clock;

    private final EntityGetService entityGetService = EntityGetServiceFixture.entityGetServiceFixture();

    @Mock
    private TransactionalExchangeRateService transactionalExchangeRateService;

    @Mock
    private PaymentBankAccountService paymentBankAccountService;

    private final FeatureService featureService = FeatureServiceFixture.allEnabled();

    @Mock
    private DSLContextProvider dslContextProvider;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private DSLContext transactionContext;

    private PaymentService paymentService;

    @BeforeEach
    public void setUp() {
        openMocks(this);
    }

    @BeforeEach
    void beforeEach() {
        openMocks(this);
        ExchangeRateServicesFixture.mockExchangeRate(transactionalExchangeRateService, "EUR", "USD", EUR_USD_EXCHANGE_RATE);
        when(paymentDAO.addPayment(any(), any())).then(i -> i.getArgument(0));
        paymentService = new PaymentService(
            paymentDAO,
            paymentIdGenerator,
            clock,
            entityGetService,
            transactionalExchangeRateService,
            paymentBankAccountService,
            featureService,
            dslContextProvider,
            tenantIdProvider
        );
    }

    @Test
    public void sameFunctionalAndTransactionCurrencies() {
        Payment payment = PaymentFixture.getDefault().build();
        Payment result = paymentService.addPayment(payment, transactionContext);
        Assertions.assertThat(result).isEqualTo(payment);
        Assertions.assertThat(result.getFunctionalAmount()).isEqualTo(payment.getAmount());
    }

    @Test
    public void noExchangeRateAvailable() {
        Payment payment = PaymentFixture.getDefault().currencyCode("INR").build();
        Assertions.assertThatThrownBy(() -> paymentService.addPayment(payment, transactionContext))
            .isInstanceOf(ConflictingStateException.class)
            .hasMessage("Exchange rate not found for payment date");
    }

    @Test
    public void differentExchangeRate() {
        Payment payment = PaymentFixture.getDefault().currencyCode("EUR").build();
        Payment result = paymentService.addPayment(payment, transactionContext);
        Assertions.assertThat(result).isEqualTo(payment);
        BigDecimal expectedFunctionalAmount = roundToTwoPlaces(payment.getAmount().multiply(EUR_USD_EXCHANGE_RATE));
        Assertions.assertThat(result.getFunctionalAmount()).isEqualTo(expectedFunctionalAmount);
    }

    private static BigDecimal roundToTwoPlaces(BigDecimal value) {
        return value.setScale(2, RoundingMode.HALF_EVEN);
    }
}
