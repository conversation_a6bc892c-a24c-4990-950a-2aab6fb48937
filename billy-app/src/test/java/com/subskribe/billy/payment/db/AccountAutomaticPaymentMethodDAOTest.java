package com.subskribe.billy.payment.db;

import static com.subskribe.billy.jooq.default_schema.Tables.ACCOUNT_AUTOMATIC_PAYMENT_METHOD;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountAutomaticPaymentMethodRecord;
import com.subskribe.billy.payment.model.AccountAutomaticPaymentMethod;
import com.subskribe.billy.payment.model.ImmutableAccountAutomaticPaymentMethod;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.jooq.MockDSLBuilder;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AccountAutomaticPaymentMethodDAOTest {

    private static final String TENANT_ID = "test tenant 1 id";

    private static final String ACCOUNT_ID = UUID.randomUUID().toString();
    private static final UUID PAYMENT_METHOD_ID = UUID.randomUUID();

    private AccountAutomaticPaymentMethodDAO accountAutomaticPaymentMethodDAO;

    private DSLContextProvider dslContextProvider;

    @BeforeEach
    void setUp() {
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        dslContextProvider = mock(DSLContextProvider.class);

        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        accountAutomaticPaymentMethodDAO = new AccountAutomaticPaymentMethodDAO(dslContextProvider, tenantIdProvider);
    }

    @Test
    void getActiveMethodReturnsFoundMethodIfFound() {
        AccountAutomaticPaymentMethod expectedMethod = getMethod();
        AccountAutomaticPaymentMethodRecord savedRecord = getSavedRecord();

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(ACCOUNT_AUTOMATIC_PAYMENT_METHOD)
                    .where(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.ACCOUNT_ID.eq(ACCOUNT_ID))
                    .and(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.TENANT_ID.eq(TENANT_ID))
                    .and(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.IS_DELETED.isFalse())
            )
            .thenReturn(savedRecord)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        Optional<AccountAutomaticPaymentMethod> foundMethod = accountAutomaticPaymentMethodDAO.getEnrolledMethodForAccount(ACCOUNT_ID);

        assertThat(foundMethod).isPresent();
        assertThat(foundMethod.get()).usingRecursiveComparison().ignoringFields("id", "createdOn", "updatedOn").isEqualTo(expectedMethod);
    }

    @Test
    void setActiveMethodDeactivatesAllPrevious() {
        AccountAutomaticPaymentMethod expectedMethod = getMethod();
        AccountAutomaticPaymentMethodRecord savedRecord = getSavedRecord();
        AccountAutomaticPaymentMethodRecord expectedRecord = new AccountAutomaticPaymentMethodRecord(
            null,
            TENANT_ID,
            PAYMENT_METHOD_ID,
            ACCOUNT_ID,
            false,
            null,
            null
        );

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .update(ACCOUNT_AUTOMATIC_PAYMENT_METHOD)
                    .set(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.IS_DELETED, true)
                    .where(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.ACCOUNT_ID.eq(ACCOUNT_ID))
                    .and(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.TENANT_ID.eq(TENANT_ID))
                    .and(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.IS_DELETED.isFalse())
            )
            .thenReturn(1)
            .when(DSL.using(SQLDialect.POSTGRES).insertInto(ACCOUNT_AUTOMATIC_PAYMENT_METHOD).set(expectedRecord).returning())
            .thenReturn(savedRecord)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        AccountAutomaticPaymentMethod createdMethod = accountAutomaticPaymentMethodDAO.setActiveMethod(expectedMethod);

        assertThat(createdMethod).usingRecursiveComparison().ignoringFields("id", "createdOn", "updatedOn").isEqualTo(expectedMethod);
    }

    @Test
    void deleteActiveMethodDeactivatesAllPrevious() {
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .update(ACCOUNT_AUTOMATIC_PAYMENT_METHOD)
                    .set(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.IS_DELETED, true)
                    .where(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.ACCOUNT_ID.eq(ACCOUNT_ID))
                    .and(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.TENANT_ID.eq(TENANT_ID))
                    .and(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.IS_DELETED.isFalse())
            )
            .thenReturn(1)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        accountAutomaticPaymentMethodDAO.removeActiveMethods(ACCOUNT_ID);
    }

    @Test
    void getActiveMethodReturnsEmptyIfNoAutoPaymentMethodFound() {
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(ACCOUNT_AUTOMATIC_PAYMENT_METHOD)
                    .where(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.ACCOUNT_ID.eq(ACCOUNT_ID))
                    .and(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.TENANT_ID.eq(TENANT_ID))
                    .and(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.IS_DELETED.isFalse())
            )
            .thenReturnNothingFor(ACCOUNT_AUTOMATIC_PAYMENT_METHOD)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        Optional<AccountAutomaticPaymentMethod> foundMethod = accountAutomaticPaymentMethodDAO.getEnrolledMethodForAccount(ACCOUNT_ID);

        assertThat(foundMethod).isEmpty();
    }

    @Test
    void getActiveAutomaticPaymentMethodsForAccountsReturnsFoundRecords() {
        AccountAutomaticPaymentMethod expectedMethod = getMethod();
        AccountAutomaticPaymentMethodRecord savedRecord = getSavedRecord();

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(ACCOUNT_AUTOMATIC_PAYMENT_METHOD)
                    .where(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.ACCOUNT_ID.in(ACCOUNT_ID))
                    .and(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.TENANT_ID.eq(TENANT_ID))
                    .and(ACCOUNT_AUTOMATIC_PAYMENT_METHOD.IS_DELETED.isFalse())
            )
            .thenReturn(savedRecord)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        List<AccountAutomaticPaymentMethod> foundResults = accountAutomaticPaymentMethodDAO.getActiveAutomaticPaymentMethodsForAccounts(
            List.of(ACCOUNT_ID)
        );

        assertThat(foundResults).usingRecursiveComparison().ignoringFields("id", "createdOn", "updatedOn").isEqualTo(List.of(expectedMethod));
    }

    private AccountAutomaticPaymentMethod getMethod() {
        return ImmutableAccountAutomaticPaymentMethod.builder()
            .accountId(ACCOUNT_ID)
            .accountPaymentMethodId(PAYMENT_METHOD_ID)
            .tenantId(TENANT_ID)
            .build();
    }

    private AccountAutomaticPaymentMethodRecord getSavedRecord() {
        return new AccountAutomaticPaymentMethodRecord(
            UUID.randomUUID(),
            TENANT_ID,
            PAYMENT_METHOD_ID,
            ACCOUNT_ID,
            false,
            DateTimeConverter.instantToLocalDateTime(Instant.now()),
            DateTimeConverter.instantToLocalDateTime(Instant.now().plusSeconds(10))
        );
    }
}
