package com.subskribe.billy.payment.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.jooq.default_schema.tables.records.InvoicePaymentManagementLinkRecord;
import com.subskribe.billy.payment.model.ImmutableInvoicePaymentManagementLink;
import com.subskribe.billy.payment.model.InvoicePaymentManagementLink;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import java.time.Instant;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class InvoicePaymentManagementLinkMapperTest {

    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final String LINK_ID = UUID.randomUUID().toString();
    private static final String INVOICE_NUMBER = "INV-123456";
    private static final UUID ID = UUID.randomUUID();
    private static final Instant NOW = Instant.now();
    private static final Instant LATER = Instant.now().plusSeconds(10);

    @Test
    void testToRecord() {
        InvoicePaymentManagementLinkMapper mapper = new InvoicePaymentManagementLinkMapperImpl();
        assertThat(mapper.toRecord(createInvoicePaymentManagementLink())).isEqualTo(createInvoicePaymentManagementLinkRecord());
    }

    @Test
    void testFromRecord() {
        InvoicePaymentManagementLinkMapper mapper = new InvoicePaymentManagementLinkMapperImpl();
        assertThat(mapper.fromRecord(createInvoicePaymentManagementLinkRecord())).isEqualTo(createInvoicePaymentManagementLink());
    }

    private InvoicePaymentManagementLink createInvoicePaymentManagementLink() {
        return ImmutableInvoicePaymentManagementLink.builder()
            .id(ID)
            .linkId(LINK_ID)
            .invoiceNumber(INVOICE_NUMBER)
            .tenantId(TENANT_ID)
            .entityId(EntityFixture.ENTITY_1_ID)
            .createdOn(NOW)
            .updatedOn(LATER)
            .isDeleted(false)
            .build();
    }

    private InvoicePaymentManagementLinkRecord createInvoicePaymentManagementLinkRecord() {
        InvoicePaymentManagementLinkRecord record = new InvoicePaymentManagementLinkRecord();
        record.setId(ID);
        record.setTenantId(TENANT_ID);
        record.setEntityId(EntityFixture.ENTITY_1_ID);
        record.setLinkId(LINK_ID);
        record.setInvoiceNumber(INVOICE_NUMBER);
        record.setIsDeleted(false);
        record.setCreatedOn(DateTimeConverter.instantToLocalDateTime(NOW));
        record.setUpdatedOn(DateTimeConverter.instantToLocalDateTime(LATER));
        return record;
    }
}
