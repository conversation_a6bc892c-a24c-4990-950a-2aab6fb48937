package com.subskribe.billy.payment.stripe.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.graphql.payment.PaymentDataAggregator;
import com.subskribe.billy.graphql.payment.PaymentDetail;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.payment.model.PaymentProviderCustomer;
import com.subskribe.billy.payment.model.PaymentProviderPaymentMethod;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodGetService;
import com.subskribe.billy.payment.services.PaymentOrchestrator;
import com.subskribe.billy.payment.services.PaymentProviderService;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class StripeSelfServeServiceTest {

    private static final String INVOICE_NUMBER = "INV-12345";
    private static final String ACCOUNT_ID = "ACC-12345";
    private static final String TENANT_ID = "TENANT-12345";
    private static final String PAYMENT_METHOD_ID = "a1b2c3d4-e5f6-7890-abcd-ef1234567890";
    private static final String PAYMENT_ID = "pay_123456789";

    @Mock
    private InvoiceRetrievalService invoiceRetrievalService;

    @Mock
    private PaymentProviderService paymentProviderService;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService;

    @Mock
    private PaymentDataAggregator paymentDataAggregator;

    private StripeSelfServeService stripeSelfServeService;

    private Invoice invoice;
    private PaymentProviderCustomer paymentProviderCustomer;
    private AccountPaymentMethod accountPaymentMethod;
    private PaymentProviderPaymentMethod paymentProviderPaymentMethod;
    private PaymentDetail paymentDetail;
    private PaymentOrchestrator paymentOrchestrator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        invoice = mock(Invoice.class);
        paymentProviderCustomer = mock(PaymentProviderCustomer.class);
        accountPaymentMethod = mock(AccountPaymentMethod.class);
        paymentProviderPaymentMethod = mock(PaymentProviderPaymentMethod.class);
        paymentDetail = mock(PaymentDetail.class);
        paymentOrchestrator = mock(PaymentOrchestrator.class);

        stripeSelfServeService = new StripeSelfServeService(
            invoiceRetrievalService,
            paymentProviderService,
            tenantIdProvider,
            accountAutomaticPaymentMethodGetService,
            paymentDataAggregator,
            paymentOrchestrator
        );

        when(invoice.getCustomerAccountId()).thenReturn(ACCOUNT_ID);
        when(invoice.getStatus()).thenReturn(InvoiceStatus.POSTED);

        when(invoiceRetrievalService.getInvoice(any(Invoice.Number.class))).thenReturn(invoice);

        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        when(paymentProviderService.getPaymentProviderCustomerByAccountId(TENANT_ID, ACCOUNT_ID)).thenReturn(Optional.of(paymentProviderCustomer));

        when(accountAutomaticPaymentMethodGetService.getActiveAccountPaymentMethodForAccount(ACCOUNT_ID)).thenReturn(
            Optional.of(accountPaymentMethod)
        );

        when(accountPaymentMethod.getPaymentMethodId()).thenReturn(PAYMENT_METHOD_ID);

        when(paymentProviderService.getPaymentProviderPaymentMethodById(UUID.fromString(PAYMENT_METHOD_ID), false)).thenReturn(
            Optional.of(paymentProviderPaymentMethod)
        );

        when(
            paymentOrchestrator.processPayment(
                any(Invoice.class),
                any(AccountPaymentMethod.class),
                any(PaymentProviderCustomer.class),
                any(PaymentProviderPaymentMethod.class),
                eq(PaymentOrchestrator.PaymentAttemptType.SELF_SERVE),
                eq(null)
            )
        ).thenReturn(Optional.of(PAYMENT_ID));

        when(paymentDataAggregator.getPaymentDetail(PAYMENT_ID)).thenReturn(paymentDetail);
    }

    @Test
    void processPaymentForInvoiceSuccess() {
        Optional<PaymentDetail> result = stripeSelfServeService.processPaymentForInvoice(INVOICE_NUMBER);

        assertTrue(result.isPresent());
        assertEquals(paymentDetail, result.get());

        verify(paymentOrchestrator).processPayment(
            eq(invoice),
            eq(accountPaymentMethod),
            eq(paymentProviderCustomer),
            eq(paymentProviderPaymentMethod),
            eq(PaymentOrchestrator.PaymentAttemptType.SELF_SERVE),
            eq(null)
        );
        verify(paymentDataAggregator).getPaymentDetail(PAYMENT_ID);
    }

    @Test
    void processPaymentForInvoicePaymentNotProcessed() {
        when(
            paymentOrchestrator.processPayment(
                any(Invoice.class),
                any(AccountPaymentMethod.class),
                any(PaymentProviderCustomer.class),
                any(PaymentProviderPaymentMethod.class),
                eq(PaymentOrchestrator.PaymentAttemptType.SELF_SERVE),
                eq(null)
            )
        ).thenReturn(Optional.empty());

        Optional<PaymentDetail> result = stripeSelfServeService.processPaymentForInvoice(INVOICE_NUMBER);

        assertTrue(result.isEmpty());
        verify(paymentDataAggregator, never()).getPaymentDetail(anyString());
    }

    @Test
    void processPaymentForInvoiceNullInvoiceNumberThrowsIllegalArgumentException() {
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> stripeSelfServeService.processPaymentForInvoice(null)
        );

        assertEquals("invoiceNumber is required", exception.getMessage());

        verify(paymentOrchestrator, never()).processPayment(
            any(),
            any(),
            any(),
            any(),
            eq(PaymentOrchestrator.PaymentAttemptType.SELF_SERVE),
            eq(null)
        );
    }

    @Test
    void processPaymentForInvoiceEmptyInvoiceNumberThrowsIllegalArgumentException() {
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> stripeSelfServeService.processPaymentForInvoice(""));

        assertEquals("invoiceNumber is required", exception.getMessage());

        verify(paymentOrchestrator, never()).processPayment(
            any(),
            any(),
            any(),
            any(),
            eq(PaymentOrchestrator.PaymentAttemptType.SELF_SERVE),
            eq(null)
        );
    }

    @Test
    void processPaymentForInvoiceInvoiceNotInPostedStateThrowsInvalidInputException() {
        when(invoice.getStatus()).thenReturn(InvoiceStatus.DRAFT);

        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            stripeSelfServeService.processPaymentForInvoice(INVOICE_NUMBER)
        );

        assertEquals("Invoice is not in POSTED state. Invoice number: " + INVOICE_NUMBER + ", status: DRAFT", exception.getMessage());

        verify(paymentOrchestrator, never()).processPayment(
            any(),
            any(),
            any(),
            any(),
            eq(PaymentOrchestrator.PaymentAttemptType.SELF_SERVE),
            eq(null)
        );
    }

    @Test
    void processPaymentForInvoiceNoPaymentProviderCustomerThrowsInvalidInputException() {
        when(paymentProviderService.getPaymentProviderCustomerByAccountId(TENANT_ID, ACCOUNT_ID)).thenReturn(Optional.empty());

        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            stripeSelfServeService.processPaymentForInvoice(INVOICE_NUMBER)
        );

        assertEquals("No payment provider found for accountId: " + ACCOUNT_ID, exception.getMessage());

        verify(paymentOrchestrator, never()).processPayment(
            any(),
            any(),
            any(),
            any(),
            eq(PaymentOrchestrator.PaymentAttemptType.SELF_SERVE),
            eq(null)
        );
    }

    @Test
    void processPaymentForInvoiceNoAccountPaymentMethodThrowsInvalidInputException() {
        when(accountAutomaticPaymentMethodGetService.getActiveAccountPaymentMethodForAccount(ACCOUNT_ID)).thenReturn(Optional.empty());

        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            stripeSelfServeService.processPaymentForInvoice(INVOICE_NUMBER)
        );

        assertEquals("No account payment method found for accountId: " + ACCOUNT_ID, exception.getMessage());

        verify(paymentOrchestrator, never()).processPayment(
            any(),
            any(),
            any(),
            any(),
            eq(PaymentOrchestrator.PaymentAttemptType.SELF_SERVE),
            eq(null)
        );
    }

    @Test
    void processPaymentForInvoiceNoPaymentProviderPaymentMethodThrowsConflictingStateException() {
        when(paymentProviderService.getPaymentProviderPaymentMethodById(UUID.fromString(PAYMENT_METHOD_ID), false)).thenReturn(Optional.empty());

        ConflictingStateException exception = assertThrows(ConflictingStateException.class, () ->
            stripeSelfServeService.processPaymentForInvoice(INVOICE_NUMBER)
        );

        assertEquals("No payment provider payment method found for accountPaymentMethodId: " + PAYMENT_METHOD_ID, exception.getMessage());

        verify(paymentOrchestrator, never()).processPayment(
            any(),
            any(),
            any(),
            any(),
            eq(PaymentOrchestrator.PaymentAttemptType.SELF_SERVE),
            eq(null)
        );
    }

    @Test
    void processPaymentForInvoiceStripeServiceThrowsExceptionPropagatesException() {
        RuntimeException expectedExceptionFromStripe = new RuntimeException("Error processing payment");
        doThrow(expectedExceptionFromStripe)
            .when(paymentOrchestrator)
            .processPayment(
                any(Invoice.class),
                any(AccountPaymentMethod.class),
                any(PaymentProviderCustomer.class),
                any(PaymentProviderPaymentMethod.class),
                eq(PaymentOrchestrator.PaymentAttemptType.SELF_SERVE),
                eq(null)
            );

        RuntimeException actualException = assertThrows(RuntimeException.class, () -> stripeSelfServeService.processPaymentForInvoice(INVOICE_NUMBER)
        );

        assertEquals(expectedExceptionFromStripe, actualException);
    }
}
