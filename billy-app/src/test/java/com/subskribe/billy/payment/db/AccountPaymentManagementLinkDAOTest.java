package com.subskribe.billy.payment.db;

import static com.subskribe.billy.jooq.default_schema.tables.AccountPaymentManagementLink.ACCOUNT_PAYMENT_MANAGEMENT_LINK;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.jooq.default_schema.tables.records.AccountPaymentManagementLinkRecord;
import com.subskribe.billy.payment.model.AccountPaymentManagementLink;
import com.subskribe.billy.payment.model.ImmutableAccountPaymentManagementLink;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.jooq.MockDSLBuilder;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AccountPaymentManagementLinkDAOTest {

    private static final String TENANT_ID = "test tenant 1 id";
    private static final String LINK_ID = UUID.randomUUID().toString();
    private static final String ACCOUNT_ID = "ACC-123456";

    private AccountPaymentManagementLinkDAO accountPaymentManagementLinkDAO;

    private DSLContextProvider dslContextProvider;

    @BeforeEach
    void setUp() {
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        dslContextProvider = mock(DSLContextProvider.class);

        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        accountPaymentManagementLinkDAO = new AccountPaymentManagementLinkDAO(dslContextProvider, tenantIdProvider);
    }

    @Test
    void createAccountPaymentManagementLinkReturnsCreatedLink() {
        AccountPaymentManagementLink linkToCreate = getLink();
        AccountPaymentManagementLinkRecord expectedRecord = getExpectedRecord();
        AccountPaymentManagementLinkRecord savedRecord = getSavedRecord();

        DSLContext dslContext = new MockDSLBuilder()
            .when(DSL.using(SQLDialect.POSTGRES).insertInto(ACCOUNT_PAYMENT_MANAGEMENT_LINK).set(expectedRecord).returning())
            .thenReturn(savedRecord)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        AccountPaymentManagementLink createdLink = accountPaymentManagementLinkDAO.createAccountPaymentManagementLink(linkToCreate);

        assertThat(createdLink).usingRecursiveComparison().ignoringFields("id", "createdOn", "updatedOn").isEqualTo(linkToCreate);
    }

    @Test
    void findPaymentManagementLinkForAccountNumberReturnsFoundLinkIfFound() {
        AccountPaymentManagementLink expectedLink = getLink();
        AccountPaymentManagementLinkRecord savedRecord = getSavedRecord();

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(ACCOUNT_PAYMENT_MANAGEMENT_LINK)
                    .where(ACCOUNT_PAYMENT_MANAGEMENT_LINK.TENANT_ID.eq(TENANT_ID))
                    .and(ACCOUNT_PAYMENT_MANAGEMENT_LINK.IS_DELETED.isFalse())
                    .and(ACCOUNT_PAYMENT_MANAGEMENT_LINK.ACCOUNT_ID.eq(ACCOUNT_ID))
            )
            .thenReturn(savedRecord)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        Optional<AccountPaymentManagementLink> foundLink = accountPaymentManagementLinkDAO.findPaymentManagementLinkForAccountId(ACCOUNT_ID);

        assertThat(foundLink).isPresent();
        assertThat(foundLink.get()).usingRecursiveComparison().ignoringFields("id", "createdOn", "updatedOn").isEqualTo(expectedLink);
    }

    @Test
    void findPaymentManagementLinkForAccountNumberReturnsEmptyOptionalIfNoLinkFound() {
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(ACCOUNT_PAYMENT_MANAGEMENT_LINK)
                    .where(ACCOUNT_PAYMENT_MANAGEMENT_LINK.TENANT_ID.eq(TENANT_ID))
                    .and(ACCOUNT_PAYMENT_MANAGEMENT_LINK.IS_DELETED.isFalse())
                    .and(ACCOUNT_PAYMENT_MANAGEMENT_LINK.ACCOUNT_ID.eq(ACCOUNT_ID))
            )
            .thenReturnNothingFor(ACCOUNT_PAYMENT_MANAGEMENT_LINK)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        Optional<AccountPaymentManagementLink> foundLink = accountPaymentManagementLinkDAO.findPaymentManagementLinkForAccountId(ACCOUNT_ID);

        assertThat(foundLink).isEmpty();
    }

    @Test
    @AllowNonRlsDataAccess
    void findPaymentManagementLinkForLinkIdReturnsFoundLinkIfFound() {
        AccountPaymentManagementLink expectedLink = getLink();

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(ACCOUNT_PAYMENT_MANAGEMENT_LINK)
                    .where(ACCOUNT_PAYMENT_MANAGEMENT_LINK.IS_DELETED.isFalse())
                    .and(ACCOUNT_PAYMENT_MANAGEMENT_LINK.LINK_ID.eq(LINK_ID))
            )
            .thenReturn(getSavedRecord())
            .context();
        when(dslContextProvider.get()).thenReturn(dslContext);

        Optional<AccountPaymentManagementLink> foundLink = accountPaymentManagementLinkDAO.findAccountPaymentManagementLinkForLinkId(LINK_ID);

        assertThat(foundLink).isPresent();
        assertThat(foundLink.get()).usingRecursiveComparison().ignoringFields("id", "createdOn", "updatedOn").isEqualTo(expectedLink);
    }

    @Test
    @AllowNonRlsDataAccess
    void findPaymentManagementLinkForLinkIdReturnsEmptyOptionalIfNoLinkFound() {
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(ACCOUNT_PAYMENT_MANAGEMENT_LINK)
                    .where(ACCOUNT_PAYMENT_MANAGEMENT_LINK.IS_DELETED.isFalse())
                    .and(ACCOUNT_PAYMENT_MANAGEMENT_LINK.LINK_ID.eq(LINK_ID))
            )
            .thenReturnNothingFor(ACCOUNT_PAYMENT_MANAGEMENT_LINK)
            .context();
        when(dslContextProvider.get()).thenReturn(dslContext);

        Optional<AccountPaymentManagementLink> foundLink = accountPaymentManagementLinkDAO.findAccountPaymentManagementLinkForLinkId(LINK_ID);

        assertThat(foundLink).isEmpty();
    }

    @Test
    void testDeletePaymentLinkSuccess() {
        String linkId = UUID.randomUUID().toString();
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .update(ACCOUNT_PAYMENT_MANAGEMENT_LINK)
                    .set(ACCOUNT_PAYMENT_MANAGEMENT_LINK.IS_DELETED, true)
                    .where(ACCOUNT_PAYMENT_MANAGEMENT_LINK.TENANT_ID.eq(TENANT_ID))
                    .and(ACCOUNT_PAYMENT_MANAGEMENT_LINK.LINK_ID.eq(linkId))
            )
            .thenReturn(1)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        accountPaymentManagementLinkDAO.deletePaymentLink(linkId);
    }

    @Test
    void testDeletePaymentLinkFailure() {
        String linkId = UUID.randomUUID().toString();
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .update(ACCOUNT_PAYMENT_MANAGEMENT_LINK)
                    .set(ACCOUNT_PAYMENT_MANAGEMENT_LINK.IS_DELETED, true)
                    .where(ACCOUNT_PAYMENT_MANAGEMENT_LINK.TENANT_ID.eq(TENANT_ID))
                    .and(ACCOUNT_PAYMENT_MANAGEMENT_LINK.LINK_ID.eq(linkId))
            )
            .thenReturn(0)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        assertThatThrownBy(() -> accountPaymentManagementLinkDAO.deletePaymentLink(linkId)).isInstanceOf(IllegalStateException.class);
    }

    private static AccountPaymentManagementLinkRecord getSavedRecord() {
        return new AccountPaymentManagementLinkRecord(
            UUID.randomUUID(),
            TENANT_ID,
            LINK_ID,
            ACCOUNT_ID,
            false,
            DateTimeConverter.instantToLocalDateTime(Instant.now()),
            DateTimeConverter.instantToLocalDateTime(Instant.now())
        );
    }

    private static AccountPaymentManagementLinkRecord getExpectedRecord() {
        return new AccountPaymentManagementLinkRecord(null, TENANT_ID, LINK_ID, ACCOUNT_ID, false, null, null);
    }

    private static AccountPaymentManagementLink getLink() {
        return ImmutableAccountPaymentManagementLink.builder().linkId(LINK_ID).accountId(ACCOUNT_ID).tenantId(TENANT_ID).build();
    }
}
