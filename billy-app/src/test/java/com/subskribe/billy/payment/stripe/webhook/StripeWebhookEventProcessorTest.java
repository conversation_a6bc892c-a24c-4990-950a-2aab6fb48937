package com.subskribe.billy.payment.stripe.webhook;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.stripe.model.MetadataStore;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.payment.services.PaymentOrchestrator;
import com.subskribe.billy.payment.stripe.service.StripeService;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class StripeWebhookEventProcessorTest {

    private BillyConfiguration billyConfiguration;

    private MetadataStore<?> stripeObject;

    private StripeWebhookEventProcessor stripeWebhookEventProcessor;

    @BeforeEach
    void setUp() {
        stripeObject = mock(MetadataStore.class);
        StripeService stripeService = mock(StripeService.class);
        billyConfiguration = mock(BillyConfiguration.class);
        PaymentOrchestrator paymentOrchestrator = mock(PaymentOrchestrator.class);
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);

        stripeWebhookEventProcessor = new StripeWebhookEventProcessor(stripeService, billyConfiguration, paymentOrchestrator, tenantIdProvider);
    }

    @Test
    void envCheckShouldReturnFalseWhenMetadataDoesNotContainEnvNameKey() {
        when(stripeObject.getMetadata()).thenReturn(Map.of());
        when(billyConfiguration.getEnvName()).thenReturn("test");

        boolean result = stripeWebhookEventProcessor.isObjectForDifferentEnvironment(stripeObject);

        assertThat(result).isFalse();
    }

    @Test
    void envCheckShouldReturnTrueWhenEnvNameDoesNotMatch() {
        when(stripeObject.getMetadata()).thenReturn(Map.of(StripeService.STRIPE_METADATA_ENV_NAME, "different-env"));
        when(billyConfiguration.getEnvName()).thenReturn("test-env");

        boolean result = stripeWebhookEventProcessor.isObjectForDifferentEnvironment(stripeObject);

        assertThat(result).isTrue();
    }

    @Test
    void envCheckShouldReturnFalseWhenEnvNameMatches() {
        when(stripeObject.getMetadata()).thenReturn(Map.of(StripeService.STRIPE_METADATA_ENV_NAME, "test-env"));
        when(billyConfiguration.getEnvName()).thenReturn("test-env");

        boolean result = stripeWebhookEventProcessor.isObjectForDifferentEnvironment(stripeObject);

        assertThat(result).isFalse();
    }
}
