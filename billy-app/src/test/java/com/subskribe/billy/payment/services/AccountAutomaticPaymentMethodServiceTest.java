package com.subskribe.billy.payment.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.account.model.AccountPaymentMethodStatus;
import com.subskribe.billy.account.services.AccountCacheInvalidator;
import com.subskribe.billy.account.services.AccountPaymentMethodGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodService;
import com.subskribe.billy.payment.db.AccountAutomaticPaymentMethodDAO;
import com.subskribe.billy.payment.db.PaymentProviderDAO;
import com.subskribe.billy.payment.model.AccountAutomaticPaymentMethod;
import com.subskribe.billy.payment.model.ImmutableAccountAutomaticPaymentMethod;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AccountAutomaticPaymentMethodServiceTest {

    private static final String ACCOUNT_ID = UUID.randomUUID().toString();
    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final UUID PAYMENT_METHOD_ID = UUID.randomUUID();
    private static final UUID PAYMENT_METHOD_ID_2 = UUID.randomUUID();
    private AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService;
    private AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService;
    private AccountPaymentMethodService accountPaymentMethodService;
    private AccountAutomaticPaymentMethodDAO accountAutomaticPaymentMethodDAO;
    private AccountPaymentMethodGetService accountPaymentMethodGetService;
    private AccountCacheInvalidator accountCacheInvalidator;

    @BeforeEach
    void setUp() {
        accountAutomaticPaymentMethodDAO = mock(AccountAutomaticPaymentMethodDAO.class);
        accountPaymentMethodService = mock(AccountPaymentMethodService.class);
        accountPaymentMethodGetService = mock(AccountPaymentMethodGetService.class);
        accountCacheInvalidator = mock(AccountCacheInvalidator.class);

        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        PaymentProviderDAO paymentProviderDAO = mock(PaymentProviderDAO.class);
        accountAutomaticPaymentMethodGetService = new AccountAutomaticPaymentMethodGetService(
            accountAutomaticPaymentMethodDAO,
            accountPaymentMethodGetService,
            tenantIdProvider,
            paymentProviderDAO
        );

        PaymentProcessorService paymentProcessorService = mock(PaymentProcessorService.class);
        accountAutomaticPaymentMethodService = new AccountAutomaticPaymentMethodService(
            accountAutomaticPaymentMethodDAO,
            accountPaymentMethodService,
            tenantIdProvider,
            accountAutomaticPaymentMethodGetService,
            paymentProcessorService,
            accountCacheInvalidator
        );
    }

    @Test
    void testGetEnrolledMethodForAccountReturnsResultFromDAO() {
        AccountAutomaticPaymentMethod accountAutomaticPaymentMethod = buildSavedMethod();

        when(accountAutomaticPaymentMethodDAO.getEnrolledMethodForAccount(ACCOUNT_ID)).thenReturn(Optional.of(accountAutomaticPaymentMethod));

        Optional<AccountAutomaticPaymentMethod> result = accountAutomaticPaymentMethodGetService.getEnrolledMethodForAccount(ACCOUNT_ID);

        assertThat(result).isPresent().contains(accountAutomaticPaymentMethod);
    }

    @Test
    void testSetPaymentMethodAsActiveReturnsSavedRecord() {
        AccountAutomaticPaymentMethod savedMethod = buildSavedMethod();
        AccountAutomaticPaymentMethod expectedSaveRequest = buildMethodToSave();

        when(accountAutomaticPaymentMethodDAO.setActiveMethod(expectedSaveRequest)).thenReturn(savedMethod);

        AccountAutomaticPaymentMethod result = accountAutomaticPaymentMethodService.setPaymentMethodAsActive(stubPaymentMethod());
        verify(accountCacheInvalidator).invalidateAccountCaches(ACCOUNT_ID);
        assertThat(result).isEqualTo(savedMethod);
    }

    @Test
    void testRemoveActiveMethodsForAccount() {
        accountAutomaticPaymentMethodService.removeActiveMethodsForAccount(ACCOUNT_ID);
        verify(accountAutomaticPaymentMethodDAO, times(1)).removeActiveMethods(ACCOUNT_ID);
        verify(accountCacheInvalidator, times(1)).invalidateAccountCaches(ACCOUNT_ID);
    }

    @Test
    void testRemoveActiveMethodsForAccountIfNoLongerSupportedDoesNothingIfNoActiveMethod() {
        Set<PaymentType> supported = Set.of(PaymentType.CARD);
        AccountAutomaticPaymentMethod savedMethod = buildSavedMethod();
        AccountPaymentMethod accountPaymentMethod = new AccountPaymentMethod();
        accountPaymentMethod.setPaymentType(PaymentType.CARD);

        when(accountAutomaticPaymentMethodDAO.getEnrolledMethodForAccount(ACCOUNT_ID)).thenReturn(Optional.of(savedMethod));
        when(accountPaymentMethodGetService.getPaymentMethod(ACCOUNT_ID, savedMethod.getAccountPaymentMethodId())).thenReturn(
            Optional.of(accountPaymentMethod)
        );

        accountAutomaticPaymentMethodService.removeActiveMethodsForAccountIfNoLongerSupported(ACCOUNT_ID, supported);

        verify(accountCacheInvalidator, never()).invalidateAccountCaches(ACCOUNT_ID);
        verify(accountAutomaticPaymentMethodDAO, never()).removeActiveMethods(anyString());
    }

    @Test
    void testRemoveActiveMethodsForAccountIfNoLongerSupportedRemovesMethodIfNoLongerSupported() {
        Set<PaymentType> supported = Set.of(PaymentType.ACH);
        AccountAutomaticPaymentMethod savedMethod = buildSavedMethod();
        AccountPaymentMethod accountPaymentMethod = new AccountPaymentMethod();
        accountPaymentMethod.setPaymentType(PaymentType.CARD);
        accountPaymentMethod.setStatus(AccountPaymentMethodStatus.ACTIVE);
        accountPaymentMethod.setPaymentMethodId(savedMethod.getAccountPaymentMethodId().toString());

        when(accountAutomaticPaymentMethodDAO.getEnrolledMethodForAccount(ACCOUNT_ID)).thenReturn(Optional.of(savedMethod));
        when(accountPaymentMethodGetService.getPaymentMethod(ACCOUNT_ID, savedMethod.getAccountPaymentMethodId())).thenReturn(
            Optional.of(accountPaymentMethod)
        );

        accountAutomaticPaymentMethodService.removeActiveMethodsForAccountIfNoLongerSupported(ACCOUNT_ID, supported);

        verify(accountAutomaticPaymentMethodDAO, times(1)).removeActiveMethods(ACCOUNT_ID);
        verify(accountPaymentMethodService, times(1)).deleteAccountPaymentMethod(savedMethod.getAccountPaymentMethodId().toString());
        verify(accountCacheInvalidator, times(2)).invalidateAccountCaches(ACCOUNT_ID);
    }

    @Test
    void testRemoveActiveMethodsForAccountIfNoLongerSupportedDoesNothingIfMethodStillSupported() {
        Set<PaymentType> supported = Set.of(PaymentType.CARD);

        when(accountAutomaticPaymentMethodDAO.getEnrolledMethodForAccount(ACCOUNT_ID)).thenReturn(Optional.empty());

        accountAutomaticPaymentMethodService.removeActiveMethodsForAccountIfNoLongerSupported(ACCOUNT_ID, supported);

        verify(accountPaymentMethodGetService, never()).getPaymentMethod(anyString(), any());
        verify(accountCacheInvalidator, never()).invalidateAccountCaches(ACCOUNT_ID);
    }

    @Test
    void testGetAccountsWithAutomaticPaymentMethodsReturnsEmptyIfInputEmpty() {
        assertThat(accountAutomaticPaymentMethodGetService.getAccountsWithAutomaticPaymentMethods(List.of())).isEqualTo(Set.of());
    }

    @Test
    void testGetAccountsWithAutomaticPaymentMethodsReturnsAccountIdsOfFoundMethods() {
        String accountId2 = UUID.randomUUID().toString();
        AccountAutomaticPaymentMethod savedMethod1 = buildSavedMethod();
        AccountAutomaticPaymentMethod savedMethod2 = ImmutableAccountAutomaticPaymentMethod.builder()
            .from(savedMethod1)
            .accountId(accountId2)
            .accountPaymentMethodId(PAYMENT_METHOD_ID_2)
            .build();
        Account account1 = new Account();
        account1.setAccountId(ACCOUNT_ID);
        Account account2 = new Account();
        account2.setAccountId(accountId2);

        when(accountAutomaticPaymentMethodDAO.getActiveAutomaticPaymentMethodsForAccounts(List.of(ACCOUNT_ID, accountId2))).thenReturn(
            List.of(savedMethod1, savedMethod2)
        );

        AccountPaymentMethod paymentMethod1 = stubPaymentMethod();
        AccountPaymentMethod paymentMethod2 = stubPaymentMethod();
        paymentMethod2.setId(PAYMENT_METHOD_ID_2);

        when(accountPaymentMethodGetService.getPaymentMethodsFromIdList(List.of(PAYMENT_METHOD_ID, PAYMENT_METHOD_ID_2))).thenReturn(
            List.of(paymentMethod1, paymentMethod2)
        );

        when(accountAutomaticPaymentMethodDAO.getEnrolledMethodForAccount(ACCOUNT_ID)).thenReturn(Optional.of(savedMethod1));
        when(accountAutomaticPaymentMethodDAO.getEnrolledMethodForAccount(accountId2)).thenReturn(Optional.of(savedMethod2));

        Set<String> results = accountAutomaticPaymentMethodGetService.getAccountsWithAutomaticPaymentMethods(List.of(account1, account2));

        assertThat(results).isEqualTo(Set.of(ACCOUNT_ID, accountId2));
    }

    @Test
    void testGetAccountsWithAutomaticPaymentMethodsReturnsOnlyActiveMethods() {
        String accountId2 = UUID.randomUUID().toString();
        AccountAutomaticPaymentMethod savedMethod1 = buildSavedMethod();
        AccountAutomaticPaymentMethod savedMethod2 = ImmutableAccountAutomaticPaymentMethod.builder()
            .from(savedMethod1)
            .accountId(accountId2)
            .accountPaymentMethodId(PAYMENT_METHOD_ID_2)
            .build();
        Account account1 = new Account();
        account1.setAccountId(ACCOUNT_ID);
        Account account2 = new Account();
        account2.setAccountId(accountId2);

        when(accountAutomaticPaymentMethodDAO.getActiveAutomaticPaymentMethodsForAccounts(List.of(ACCOUNT_ID, accountId2))).thenReturn(
            List.of(savedMethod1, savedMethod2)
        );

        AccountPaymentMethod paymentMethod1 = stubPaymentMethod();
        paymentMethod1.setStatus(AccountPaymentMethodStatus.SUSPENDED);
        AccountPaymentMethod paymentMethod2 = stubPaymentMethod();
        paymentMethod2.setId(PAYMENT_METHOD_ID_2);

        when(accountPaymentMethodGetService.getPaymentMethodsFromIdList(List.of(PAYMENT_METHOD_ID, PAYMENT_METHOD_ID_2))).thenReturn(
            List.of(paymentMethod1, paymentMethod2)
        );

        when(accountAutomaticPaymentMethodDAO.getEnrolledMethodForAccount(ACCOUNT_ID)).thenReturn(Optional.of(savedMethod1));
        when(accountAutomaticPaymentMethodDAO.getEnrolledMethodForAccount(accountId2)).thenReturn(Optional.of(savedMethod2));

        Set<String> results = accountAutomaticPaymentMethodGetService.getAccountsWithAutomaticPaymentMethods(List.of(account1, account2));

        assertThat(results).isEqualTo(Set.of(accountId2));
    }

    private static AccountAutomaticPaymentMethod buildSavedMethod() {
        return ImmutableAccountAutomaticPaymentMethod.builder()
            .accountId(ACCOUNT_ID)
            .isDeleted(false)
            .accountPaymentMethodId(PAYMENT_METHOD_ID)
            .id(UUID.randomUUID())
            .tenantId(TENANT_ID)
            .createdOn(Instant.now())
            .updatedOn(Instant.now().plusSeconds(10))
            .build();
    }

    private static AccountAutomaticPaymentMethod buildMethodToSave() {
        return ImmutableAccountAutomaticPaymentMethod.builder()
            .accountId(ACCOUNT_ID)
            .isDeleted(false)
            .accountPaymentMethodId(PAYMENT_METHOD_ID)
            .tenantId(TENANT_ID)
            .build();
    }

    private static AccountPaymentMethod stubPaymentMethod() {
        return new AccountPaymentMethod(
            PAYMENT_METHOD_ID,
            ACCOUNT_ID,
            "Test payment method",
            "externalId",
            PaymentType.CARD,
            UUID.randomUUID().toString(),
            Instant.now(),
            Instant.now().plusSeconds(10),
            AccountPaymentMethodStatus.ACTIVE
        );
    }
}
