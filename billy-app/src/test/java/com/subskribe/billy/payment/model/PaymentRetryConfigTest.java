package com.subskribe.billy.payment.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.event.model.payload.ImmutableAccountPaymentMethodSuspendedEventPayload;
import com.subskribe.billy.event.model.payload.ImmutablePaymentAttemptFailedEventPayload;
import com.subskribe.billy.event.model.payload.ImmutablePaymentRetriesExhaustedEventPayload;
import com.subskribe.billy.event.model.payload.PaymentAttemptFailedEventPayload;
import com.subskribe.billy.jooq.default_schema.tables.records.PaymentAttemptRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.PaymentRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.PaymentRetryConfigRecord;
import com.subskribe.billy.payment.mapper.PaymentRecordMapper;
import com.subskribe.billy.payment.mapper.PaymentRetryConfigMapper;
import com.subskribe.billy.payment.services.PaymentOrchestrator;
import com.subskribe.billy.shared.enums.PaymentState;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.jooq.JSONB;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

public class PaymentRetryConfigTest {

    private static PaymentRetryConfigMapper mapper;
    private static PaymentRecordMapper paymentRecordMapper;
    private static ObjectMapper objectMapper;

    @BeforeAll
    static void setup() {
        mapper = Mappers.getMapper(PaymentRetryConfigMapper.class);
        paymentRecordMapper = Mappers.getMapper(PaymentRecordMapper.class);
        objectMapper = JacksonProvider.defaultMapper();
    }

    @Test
    void testDbSerialization() throws JsonProcessingException {
        // Create a PaymentRetryConfig object
        var policy = new IntervalBasedPaymentRetryPolicy();
        policy.setVersion(1);
        policy.setType(PaymentRetryPolicy.PaymentRetryPolicyType.INTERVAL_BASED);
        policy.setSchedule(
            List.of(
                new IntervalBasedPaymentRetryPolicy.ScheduleEntry("1st Attempt", 60),
                new IntervalBasedPaymentRetryPolicy.ScheduleEntry("2nd Attempt", 120)
            )
        );
        PaymentRetryConfig config = ImmutablePaymentRetryConfig.builder()
            .id(UUID.randomUUID())
            .groupingId("PAY-RTRY-GRP-1234567")
            .policy(policy)
            .status(PaymentRetryConfig.PaymentRetryConfigStatus.LATEST)
            .isDeleted(false)
            .createdOn(Instant.now())
            .updatedOn(Instant.now())
            .build();

        // Serialize the object
        PaymentRetryConfigRecord record = mapper.modelToRecord(config);

        // Assertions
        assertEquals(record.getId(), config.getId().get());
        assertEquals(record.getGroupingId(), config.getGroupingId().get());
        assertNotNull(record.getId());
        var policyJson = record.getPolicy();
        assertNotNull(policyJson);
        var policyFromJson = objectMapper.readValue(policyJson.data(), PaymentRetryPolicy.class);
        assertEquals(IntervalBasedPaymentRetryPolicy.class, policyFromJson.getClass());
        assertEquals(policy.getVersion(), policyFromJson.getVersion());
        assertEquals(policy.getType(), policyFromJson.getType());
        assertNotNull(record.getPolicy());
    }

    @Test
    void testDbDeserialization() {
        // Create a PaymentRetryConfigRecord object
        PaymentRetryConfigRecord record = new PaymentRetryConfigRecord();
        record.setId(UUID.randomUUID());
        record.setGroupingId("PAY-RTRY-GRP-1234567");
        record.setTenantId("TENANT-123");
        record.setPolicy(
            JSONB.valueOf(
                """
                    {
                    "type": "INTERVAL_BASED",
                    "version": 1,
                        "schedule": [
                            {"attemptName": "1st Attempt", "afterMinutes": 60},
                            {"attemptName": "2nd Attempt", "afterMinutes": 120}
                        ]
                    }
                """
            )
        );
        record.setStatus("LATEST");
        record.setIsDeleted(false);
        record.setCreatedOn(LocalDateTime.now());
        record.setUpdatedOn(LocalDateTime.now());

        // Deserialize the object
        PaymentRetryConfig config = mapper.recordToModel(record);

        // Assertions
        assertNotNull(config);
        assertEquals(record.getId(), config.getId().get());
        assertEquals(record.getGroupingId(), config.getGroupingId().get());
        assertEquals(PaymentRetryPolicy.PaymentRetryPolicyType.INTERVAL_BASED, config.getPolicy().getType());
        assertEquals(1, config.getPolicy().getVersion());
        assertNotNull(config.getPolicy());
        assertEquals(IntervalBasedPaymentRetryPolicy.class, config.getPolicy().getClass());

        // Validate the policy
        IntervalBasedPaymentRetryPolicy policy = (IntervalBasedPaymentRetryPolicy) config.getPolicy();
        assertEquals(2, policy.getSchedule().size());
        assertEquals("1st Attempt", policy.getSchedule().get(0).attemptName());
        assertEquals(60, policy.getSchedule().get(0).afterMinutes());
    }

    @Test
    void testDeserializePaymentRetryConfigInput() throws JsonProcessingException {
        String inputJson =
            """
            {
                "policy": {
                "type": "INTERVAL_BASED",
                    "version" : 1,
                    "schedule": [
                        {"attemptName": "1st Attempt", "afterMinutes": 60},
                        {"attemptName": "2nd Attempt", "afterMinutes": 120}
                    ]
                }
            }
            """;

        // Deserialize the object
        PaymentRetryConfigInput deserializedInput = JacksonProvider.defaultMapper().readValue(inputJson, PaymentRetryConfigInput.class);

        // Assertions
        assertNotNull(deserializedInput);
        PaymentRetryPolicy deserializedInputPolicy = deserializedInput.getPolicy();
        assertEquals(1, deserializedInputPolicy.getVersion());
        assertEquals(PaymentRetryPolicy.PaymentRetryPolicyType.INTERVAL_BASED, deserializedInputPolicy.getType());
        assertNotNull(deserializedInput.getPolicy());

        var paymentRetryConfig = mapper.inputToModel(deserializedInput);
        assertNotNull(paymentRetryConfig);
        assertTrue(paymentRetryConfig.getId().isEmpty());
        assertTrue(paymentRetryConfig.getGroupingId().isEmpty());
        assertEquals(deserializedInputPolicy.getType(), paymentRetryConfig.getPolicy().getType());
        assertEquals(deserializedInputPolicy.getVersion(), paymentRetryConfig.getPolicy().getVersion());
        assertEquals(IntervalBasedPaymentRetryPolicy.class, paymentRetryConfig.getPolicy().getClass());
        IntervalBasedPaymentRetryPolicy policy = (IntervalBasedPaymentRetryPolicy) paymentRetryConfig.getPolicy();
        assertEquals(2, policy.getSchedule().size());
        assertEquals("1st Attempt", policy.getSchedule().get(0).attemptName());
        assertEquals(60, policy.getSchedule().get(0).afterMinutes());
        assertEquals("2nd Attempt", policy.getSchedule().get(1).attemptName());
        assertEquals(120, policy.getSchedule().get(1).afterMinutes());
    }

    @Test
    void testSerDeForModel() throws JsonProcessingException {
        // Create a PaymentRetryConfig object
        var policy = new IntervalBasedPaymentRetryPolicy();
        policy.setVersion(1);
        policy.setType(PaymentRetryPolicy.PaymentRetryPolicyType.INTERVAL_BASED);
        policy.setSchedule(
            List.of(
                new IntervalBasedPaymentRetryPolicy.ScheduleEntry("1st Attempt", 60),
                new IntervalBasedPaymentRetryPolicy.ScheduleEntry("2nd Attempt", 120)
            )
        );
        PaymentRetryConfig config = ImmutablePaymentRetryConfig.builder()
            .id(UUID.randomUUID())
            .groupingId("PAY-RTRY-GRP-1234567")
            .policy(policy)
            .status(PaymentRetryConfig.PaymentRetryConfigStatus.LATEST)
            .isDeleted(false)
            .createdOn(Instant.now())
            .updatedOn(Instant.now())
            .build();

        // Serialize the object
        String serialized = JacksonProvider.defaultMapper().writeValueAsString(config);

        // Deserialize the object
        PaymentRetryConfig deserialized = JacksonProvider.defaultMapper().readValue(serialized, PaymentRetryConfig.class);

        // Assertions
        assertEquals(config.getId(), deserialized.getId());
        assertEquals(config.getGroupingId(), deserialized.getGroupingId());
        assertEquals(config.getPolicy().getType(), deserialized.getPolicy().getType());
        assertEquals(config.getPolicy().getVersion(), deserialized.getPolicy().getVersion());
        assertEquals(IntervalBasedPaymentRetryPolicy.class, deserialized.getPolicy().getClass());
        IntervalBasedPaymentRetryPolicy deserializedPolicy = (IntervalBasedPaymentRetryPolicy) deserialized.getPolicy();
        assertEquals(2, deserializedPolicy.getSchedule().size());
        assertEquals("1st Attempt", deserializedPolicy.getSchedule().get(0).attemptName());
        assertEquals(60, deserializedPolicy.getSchedule().get(0).afterMinutes());
        assertEquals("2nd Attempt", deserializedPolicy.getSchedule().get(1).attemptName());
        assertEquals(120, deserializedPolicy.getSchedule().get(1).afterMinutes());
        assertEquals(config.getStatus(), deserialized.getStatus());
        assertEquals(config.getIsDeleted(), deserialized.getIsDeleted());
        assertNotNull(deserialized.getPolicy());
    }

    @Test
    public void testPaymentSerDeWithAttemptAndMetadata() {
        // Create PaymentAttemptMetadata
        PaymentAttemptMetadata attemptMetadata = ImmutablePaymentAttemptMetadata.builder()
            .retryMetadata(
                new PaymentAttemptMetadata.AttemptRetryMetadata(
                    PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
                    "Initial automated attempt",
                    Optional.empty()
                )
            )
            .build();

        // Create PaymentAttempt
        PaymentAttempt paymentAttempt = PaymentAttempt.builder()
            .id(UUID.randomUUID())
            .tenantId("tenant-1")
            .entityId("entity-1")
            .paymentId("payment-1")
            .state(PaymentAttemptState.CREATED)
            .transactionId("txn-1")
            .attemptNumber(1)
            .attemptedOn(Instant.now())
            .metadata(attemptMetadata)
            .build();

        PaymentAttemptRecord paymentAttemptRecord = paymentRecordMapper.paymentAttemptToRecord(paymentAttempt);
        // Deserialize back to PaymentAttempt
        PaymentAttempt deserializedAttempt = paymentRecordMapper.recordToPaymentAttempt(paymentAttemptRecord);
        // Assert equality
        assertEquals(paymentAttempt.getId(), deserializedAttempt.getId());
        assertEquals(paymentAttempt.getMetadata(), deserializedAttempt.getMetadata());

        // Create PaymentMetadata
        PaymentMetadata paymentMetadata = ImmutablePaymentMetadata.builder()
            .retryMetadata(
                new PaymentMetadata.PaymentRetryMetadata(
                    UUID.randomUUID(),
                    UUID.randomUUID(),
                    Optional.of(
                        new PaymentMetadata.NextAutomatedRetryDetail(
                            Instant.now().truncatedTo(java.time.temporal.ChronoUnit.SECONDS),
                            UUID.randomUUID().toString()
                        )
                    )
                )
            )
            .build();

        // Create Payment
        UUID paymentId = UUID.randomUUID();
        UUID paymentMethodId = UUID.randomUUID();

        Payment payment = Payment.builder()
            .id(UUID.randomUUID())
            .paymentId(paymentId.toString())
            .entityId("entity-1")
            .customerAccountId("account-1")
            .paymentMethodId(paymentMethodId)
            .state(PaymentState.INITIATED)
            .amount(BigDecimal.valueOf(100.0))
            .currencyCode("USD")
            .metadata(paymentMetadata)
            .build();

        // Serialize to PaymentRecord
        PaymentRecord record = paymentRecordMapper.paymentToRecord(payment);

        // Deserialize back to Payment
        Payment deserialized = paymentRecordMapper.recordToPayment(record);

        // Assert equality
        assertEquals(payment.getPaymentId(), deserialized.getPaymentId());
        PaymentMetadata.PaymentRetryMetadata paymentRetryMetadata = payment.getMetadata().getRetryMetadata().orElse(null);
        assertNotNull(paymentRetryMetadata);
        assertEquals(paymentRetryMetadata.paymentRetryConfigId(), deserialized.getMetadata().getRetryMetadata().get().paymentRetryConfigId());
        assertEquals(paymentRetryMetadata.accountPaymentMethodId(), deserialized.getMetadata().getRetryMetadata().get().accountPaymentMethodId());
        assertEquals(paymentRetryMetadata.nextAutomatedRetryDetail(), deserialized.getMetadata().getRetryMetadata().get().nextAutomatedRetryDetail());
    }

    @Test
    public void testPaymentAttemptFailedNotificationPayloadSerDe() throws IOException {
        String paymentAttemptId = UUID.randomUUID().toString();
        String invoiceNumber = "INV-123456";
        String paymentGatewayIntegrationId = "PG-INT-123456";
        String accountPaymentMethodId = UUID.randomUUID().toString();
        String paymentId = "PAY-123456";
        int attemptNumber = 1;
        String attemptName = "1st Attempt";
        String failureReason = "Insufficient funds";

        // Create a PaymentAttemptFailedEventPayload object
        PaymentAttemptFailedEventPayload payload = ImmutablePaymentAttemptFailedEventPayload.builder()
            .paymentAttemptId(paymentAttemptId)
            .invoiceNumber(invoiceNumber)
            .paymentGatewayIntegrationId(paymentGatewayIntegrationId)
            .accountPaymentMethodId(accountPaymentMethodId)
            .paymentId(paymentId)
            .attemptName(attemptName)
            .attemptNumber(attemptNumber)
            .failureReason(failureReason)
            .build();

        // Serialize the object to bytes
        byte[] serializedPayload = JacksonProvider.defaultMapper().writeValueAsBytes(payload);
        // Deserialize the bytes back to PaymentAttemptFailedEventPayload
        PaymentAttemptFailedEventPayload deserializedPayload = JacksonProvider.defaultMapper()
            .readValue(serializedPayload, PaymentAttemptFailedEventPayload.class);
        // Serialize back to JSON string
        String serializedJson = JacksonProvider.defaultMapper().writeValueAsString(deserializedPayload);
        // Deserialize the JSON string back to PaymentAttemptFailedEventPayload
        PaymentAttemptFailedEventPayload deserializedFromJson = JacksonProvider.defaultMapper()
            .readValue(serializedJson, PaymentAttemptFailedEventPayload.class);

        // Assertions
        assertNotNull(deserializedFromJson);
        assertEquals(paymentAttemptId, deserializedFromJson.getPaymentAttemptId());
        assertEquals(invoiceNumber, deserializedFromJson.getInvoiceNumber());
        assertEquals(paymentGatewayIntegrationId, deserializedFromJson.getPaymentGatewayIntegrationId());
        assertEquals(attemptName, deserializedFromJson.getAttemptName());
        assertEquals(attemptNumber, deserializedFromJson.getAttemptNumber());
        assertEquals(paymentAttemptId, deserializedFromJson.getEventObjectId());
    }

    @Test
    public void testPaymentRetriesExhaustedNotificationPayloadSerDe() throws IOException {
        String paymentAttemptId = UUID.randomUUID().toString();
        String invoiceNumber = "INV-123456";
        String paymentGatewayIntegrationId = "PG-INT-123456";
        String accountPaymentMethodId = UUID.randomUUID().toString();
        String paymentId = "PAY-123456";
        String failureReason = "All retry attempts exhausted";

        // Create a PaymentRetriesExhaustedEventPayload object
        var payload = ImmutablePaymentRetriesExhaustedEventPayload.builder()
            .paymentAttemptId(paymentAttemptId)
            .invoiceNumber(invoiceNumber)
            .paymentGatewayIntegrationId(paymentGatewayIntegrationId)
            .accountPaymentMethodId(accountPaymentMethodId)
            .paymentId(paymentId)
            .failureReason(failureReason)
            .build();

        // Serialize the object to bytes
        byte[] serializedPayload = JacksonProvider.defaultMapper().writeValueAsBytes(payload);
        // Deserialize the bytes back to PaymentRetriesExhaustedEventPayload
        var deserializedPayload = JacksonProvider.defaultMapper().readValue(serializedPayload, ImmutablePaymentRetriesExhaustedEventPayload.class);
        // Serialize back to JSON string
        String serializedJson = JacksonProvider.defaultMapper().writeValueAsString(deserializedPayload);
        // Deserialize the JSON string back to PaymentRetriesExhaustedEventPayload
        var deserializedFromJson = JacksonProvider.defaultMapper().readValue(serializedJson, ImmutablePaymentRetriesExhaustedEventPayload.class);

        // Assertions
        assertNotNull(deserializedFromJson);
        assertEquals(paymentAttemptId, deserializedFromJson.getPaymentAttemptId());
        assertEquals(invoiceNumber, deserializedFromJson.getInvoiceNumber());
        assertEquals(paymentGatewayIntegrationId, deserializedFromJson.getPaymentGatewayIntegrationId());
        assertEquals(paymentAttemptId, deserializedFromJson.getEventObjectId());
    }

    @Test
    public void testAccountPaymentMethodSuspendedEventPayloadSerDe() throws IOException {
        String accountId = "account-123";
        String accountPaymentMethodId = "payment-method-123";
        String setupUrl = "https://dev2.subskribe.net/automatic-payments/abcd";

        // Create an AccountPaymentMethodSuspendedEventPayload object
        var payload = ImmutableAccountPaymentMethodSuspendedEventPayload.builder()
            .accountId(accountId)
            .accountPaymentMethodId(accountPaymentMethodId)
            .setupUrl(setupUrl)
            .build();

        // Serialize the object to bytes
        byte[] serializedPayload = JacksonProvider.defaultMapper().writeValueAsBytes(payload);
        // Deserialize the bytes back to AccountPaymentMethodSuspendedEventPayload
        var deserializedPayload = JacksonProvider.defaultMapper()
            .readValue(serializedPayload, ImmutableAccountPaymentMethodSuspendedEventPayload.class);
        // Serialize back to JSON string
        String serializedJson = JacksonProvider.defaultMapper().writeValueAsString(deserializedPayload);
        // Deserialize the JSON string back to AccountPaymentMethodSuspendedEventPayload
        var deserializedFromJson = JacksonProvider.defaultMapper()
            .readValue(serializedJson, ImmutableAccountPaymentMethodSuspendedEventPayload.class);

        // Assertions
        assertNotNull(deserializedFromJson);
        assertEquals(accountId, deserializedFromJson.getAccountId());
        assertEquals(accountPaymentMethodId, deserializedFromJson.getAccountPaymentMethodId());
        assertEquals(setupUrl, deserializedFromJson.getSetupUrl());
    }
}
