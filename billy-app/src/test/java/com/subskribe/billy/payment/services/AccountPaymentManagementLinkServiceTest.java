package com.subskribe.billy.payment.services;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.payment.db.AccountPaymentManagementLinkDAO;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.model.AccountPaymentManagementLink;
import com.subskribe.billy.payment.model.ImmutableAccountPaymentManagementLink;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class AccountPaymentManagementLinkServiceTest {

    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final String ACCOUNT_ID = "ACC-123456";

    private AccountPaymentManagementLinkDAO accountPaymentManagementLinkDAO;

    private AccountPaymentManagementLinkService accountPaymentManagementLinkService;

    @BeforeEach
    void setUp() {
        accountPaymentManagementLinkDAO = mock(AccountPaymentManagementLinkDAO.class);
        AccountGetService accountGetService = mock(AccountGetService.class);

        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        PaymentIntegrationGetService paymentIntegrationGetService = mock(PaymentIntegrationGetService.class);
        when(paymentIntegrationGetService.hasIntegration()).thenReturn(true);

        when(accountGetService.accountExists(ACCOUNT_ID)).thenReturn(true);

        accountPaymentManagementLinkService = new AccountPaymentManagementLinkService(
            accountPaymentManagementLinkDAO,
            accountGetService,
            tenantIdProvider,
            paymentIntegrationGetService
        );
    }

    @Test
    void testGetLinkByIdThrowsObjectNotFoundExceptionIfLinkNotFound() {
        String linkId = UUID.randomUUID().toString();

        when(accountPaymentManagementLinkDAO.findAccountPaymentManagementLinkForLinkId(linkId)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> accountPaymentManagementLinkService.getLinkById(linkId)).isInstanceOf(ObjectNotFoundException.class);
    }

    @Test
    void testGetLinkByIdReturnsFoundLink() {
        UUID linkId = UUID.randomUUID();
        AccountPaymentManagementLink link = buildLink();

        when(accountPaymentManagementLinkDAO.findAccountPaymentManagementLinkForLinkId(linkId.toString())).thenReturn(Optional.of(link));

        AccountPaymentManagementLink foundLink = accountPaymentManagementLinkService.getLinkById(linkId.toString());

        assertThat(foundLink).isEqualTo(link);
    }

    @Test
    void testInvalidatePaymentLinkMakesCorrectDAOCall() {
        String linkId = UUID.randomUUID().toString();
        AccountPaymentManagementLink link = buildLink();

        when(accountPaymentManagementLinkDAO.findAccountPaymentManagementLinkForLinkId(linkId)).thenReturn(Optional.of(link));
        accountPaymentManagementLinkService.invalidatePaymentLink(linkId);

        verify(accountPaymentManagementLinkDAO, times(1)).deletePaymentLink(linkId);
    }

    @Test
    void testGetOrCreateAccountPaymentManagementLinkReturnsExistingLinkIfFound() {
        AccountPaymentManagementLink link = buildLink();

        when(accountPaymentManagementLinkDAO.findPaymentManagementLinkForAccountId(ACCOUNT_ID)).thenReturn(Optional.of(link));

        Optional<AccountPaymentManagementLink> result = accountPaymentManagementLinkService.getOrCreateAccountPaymentManagementLink(ACCOUNT_ID);

        Assertions.assertTrue(result.isPresent());
        assertThat(result.get()).isEqualTo(link);
    }

    @Test
    void testGetOrCreateAccountPaymentManagementLinkReturnsNewlyCreatedLinkIfExistingLinkNotFound() {
        AccountPaymentManagementLink link = buildLink();
        AccountPaymentManagementLink expectedRequest = expectedRequest();

        when(accountPaymentManagementLinkDAO.findPaymentManagementLinkForAccountId(ACCOUNT_ID)).thenReturn(Optional.empty());
        when(accountPaymentManagementLinkDAO.createAccountPaymentManagementLink(any(AccountPaymentManagementLink.class))).thenReturn(link);

        Optional<AccountPaymentManagementLink> result = accountPaymentManagementLinkService.getOrCreateAccountPaymentManagementLink(ACCOUNT_ID);

        Assertions.assertTrue(result.isPresent());
        assertThat(result.get()).isEqualTo(link);

        ArgumentCaptor<AccountPaymentManagementLink> captor = ArgumentCaptor.forClass(AccountPaymentManagementLink.class);
        verify(accountPaymentManagementLinkDAO, times(1)).createAccountPaymentManagementLink(captor.capture());
        assertThat(captor.getValue()).usingRecursiveComparison().ignoringFields("linkId").isEqualTo(expectedRequest);
    }

    private static AccountPaymentManagementLink buildLink() {
        return ImmutableAccountPaymentManagementLink.builder()
            .id(UUID.randomUUID())
            .tenantId(TENANT_ID)
            .accountId(ACCOUNT_ID)
            .linkId(UUID.randomUUID().toString())
            .createdOn(Instant.now())
            .updatedOn(Instant.now())
            .isDeleted(false)
            .build();
    }

    private static AccountPaymentManagementLink expectedRequest() {
        return ImmutableAccountPaymentManagementLink.builder().tenantId(TENANT_ID).accountId(ACCOUNT_ID).linkId(UUID.randomUUID().toString()).build();
    }
}
