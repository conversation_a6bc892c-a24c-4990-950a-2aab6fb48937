package com.subskribe.billy.payment.stripe.webhook;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.stripe.exception.SignatureVerificationException;
import com.stripe.model.Event;
import com.stripe.net.Webhook;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.aws.secretsmanager.model.SecretType;
import com.subskribe.billy.payment.stripe.model.StripeEventType;
import com.subskribe.billy.shared.webhook.ImmutableIncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhookTenantLookupResult;
import com.subskribe.billy.shared.webhook.IncomingWebhookType;
import com.subskribe.billy.test.BillyTestBase;
import java.util.Optional;
import java.util.UUID;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class StripeIncomingWebhookProcessorTest extends BillyTestBase {

    private static final String TEST_TENANT_ID = "test-tenant-id";
    private static final String TEST_ENDPOINT_SECRET = "test_endpointSecret";
    private static final String TEST_SIGNATURE_HEADER = "testSignature_header";

    @Mock
    private StripeWebhookEventProcessor stripeWebhookEventProcessor;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private SecretsService secretsService;

    @Mock
    private Event mockEvent;

    private StripeIncomingWebhookProcessor processor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        processor = new StripeIncomingWebhookProcessor(stripeWebhookEventProcessor, billyConfiguration, secretsService);
    }

    @Test
    void getWebhookTypeShouldReturnCorrectType() {
        IncomingWebhookType webhookType = processor.getWebhookType();

        assertThat(webhookType).isEqualTo(StripeIncomingWebhookProcessor.WEBHOOK_TYPE);
        assertThat(webhookType.name()).isEqualTo("stripe");
    }

    @Test
    void findTenantIdWithUnsupportedEventTypeShouldReturnEmptyTenantId() throws Exception {
        String payload = createEventJson("unsupported_event_type", UUID.randomUUID().toString());
        IncomingWebhook webhook = createIncomingWebhook(payload);

        IncomingWebhookTenantLookupResult result = processor.findTenantId(webhook);

        IncomingWebhookTenantLookupResult expectedResult = new IncomingWebhookTenantLookupResult(Optional.empty(), true);
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void findTenantIdWithSupportedEventTypeAndMissingTenantIdShouldReturnEmptyTenantId() throws Exception {
        String payload = createEventJson(StripeEventType.PAYMENT_INTENT_SUCCEEDED, "");
        IncomingWebhook webhook = createIncomingWebhook(payload);

        IncomingWebhookTenantLookupResult result = processor.findTenantId(webhook);

        IncomingWebhookTenantLookupResult expectedResult = new IncomingWebhookTenantLookupResult(Optional.empty(), true);
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void findTenantIdWithSupportedEventTypeAndValidTenantIdShouldReturnTenantId() throws Exception {
        String payload = createEventJson(StripeEventType.PAYMENT_INTENT_SUCCEEDED, TEST_TENANT_ID);
        IncomingWebhook webhook = createIncomingWebhook(payload);

        IncomingWebhookTenantLookupResult result = processor.findTenantId(webhook);

        IncomingWebhookTenantLookupResult expectedResult = new IncomingWebhookTenantLookupResult(Optional.of(TEST_TENANT_ID), true);
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void validateInLocalOrCiEnvironmentShouldReturnTrue() {
        when(billyConfiguration.isLocalOrCi()).thenReturn(true);
        IncomingWebhook webhook = createIncomingWebhook("{}");

        boolean result = processor.validate(webhook);

        assertThat(result).isTrue();
        verify(secretsService, never()).getStripeSecret(any(), anyBoolean());
    }

    @Test
    void validateWithMissingSignatureHeaderShouldThrowException() {
        when(billyConfiguration.isLocalOrCi()).thenReturn(false);
        MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();
        IncomingWebhook webhook = ImmutableIncomingWebhook.builder()
            .webhookType(StripeIncomingWebhookProcessor.WEBHOOK_TYPE)
            .payload("{}")
            .receivedOn(System.currentTimeMillis())
            .headers(headers)
            .build();

        assertThatExceptionOfType(IllegalArgumentException.class)
            .isThrownBy(() -> processor.validate(webhook))
            .withMessage("Webhook is missing proper signature header.");
    }

    @Test
    void validateWithMissingEndpointSecretShouldReturnFalse() {
        when(billyConfiguration.isLocalOrCi()).thenReturn(false);
        when(secretsService.getStripeSecret(SecretType.STRIPE_ENDPOINT_SECRET, false)).thenReturn("");

        MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();
        headers.add("Stripe-Signature", TEST_SIGNATURE_HEADER);

        IncomingWebhook webhook = ImmutableIncomingWebhook.builder()
            .webhookType(StripeIncomingWebhookProcessor.WEBHOOK_TYPE)
            .payload("{}")
            .receivedOn(System.currentTimeMillis())
            .headers(headers)
            .build();

        boolean result = processor.validate(webhook);

        assertThat(result).isFalse();
    }

    @Test
    void validateWithValidSignatureShouldReturnTrue() {
        when(billyConfiguration.isLocalOrCi()).thenReturn(false);
        when(secretsService.getStripeSecret(SecretType.STRIPE_ENDPOINT_SECRET, false)).thenReturn(TEST_ENDPOINT_SECRET);

        MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();
        headers.add("Stripe-Signature", TEST_SIGNATURE_HEADER);

        IncomingWebhook webhook = ImmutableIncomingWebhook.builder()
            .webhookType(StripeIncomingWebhookProcessor.WEBHOOK_TYPE)
            .payload("{}")
            .receivedOn(System.currentTimeMillis())
            .headers(headers)
            .build();

        try (MockedStatic<Webhook> webhookMock = Mockito.mockStatic(Webhook.class)) {
            webhookMock.when(() -> Webhook.constructEvent(eq("{}"), eq(TEST_SIGNATURE_HEADER), eq(TEST_ENDPOINT_SECRET))).thenReturn(mockEvent);

            boolean result = processor.validate(webhook);

            assertThat(result).isTrue();
        }
    }

    @Test
    void validateWithInvalidSignatureShouldReturnFalse() {
        when(billyConfiguration.isLocalOrCi()).thenReturn(false);
        when(secretsService.getStripeSecret(SecretType.STRIPE_ENDPOINT_SECRET, false)).thenReturn(TEST_ENDPOINT_SECRET);

        MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();
        headers.add("Stripe-Signature", TEST_SIGNATURE_HEADER);

        IncomingWebhook webhook = ImmutableIncomingWebhook.builder()
            .webhookType(StripeIncomingWebhookProcessor.WEBHOOK_TYPE)
            .payload("{}")
            .receivedOn(System.currentTimeMillis())
            .headers(headers)
            .build();

        try (MockedStatic<Webhook> webhookMock = Mockito.mockStatic(Webhook.class)) {
            webhookMock
                .when(() -> Webhook.constructEvent(eq("{}"), eq(TEST_SIGNATURE_HEADER), eq(TEST_ENDPOINT_SECRET)))
                .thenThrow(new SignatureVerificationException("Invalid signature", null));

            boolean result = processor.validate(webhook);

            assertThat(result).isFalse();
        }
    }

    @Test
    void processShouldDelegateToEventProcessor() throws Exception {
        String payload = createEventJson(StripeEventType.PAYMENT_INTENT_SUCCEEDED, TEST_TENANT_ID);
        IncomingWebhook webhook = createIncomingWebhook(payload);

        processor.process(webhook);

        verify(stripeWebhookEventProcessor, times(1)).processWebhook(any(Event.class));
    }

    @Test
    void processWhenProcessorThrowsExceptionShouldHandleGracefully() throws Exception {
        String payload = createEventJson(StripeEventType.PAYMENT_INTENT_SUCCEEDED, TEST_TENANT_ID);
        IncomingWebhook webhook = createIncomingWebhook(payload);

        doThrow(new RuntimeException("Test exception")).when(stripeWebhookEventProcessor).processWebhook(any(Event.class));

        processor.process(webhook);

        verify(stripeWebhookEventProcessor, times(1)).processWebhook(any(Event.class));
    }

    private IncomingWebhook createIncomingWebhook(String payload) {
        return ImmutableIncomingWebhook.builder()
            .webhookType(StripeIncomingWebhookProcessor.WEBHOOK_TYPE)
            .payload(payload)
            .receivedOn(System.currentTimeMillis())
            .headers(new MultivaluedHashMap<>())
            .build();
    }

    private String createEventJson(String eventType, String tenantId) throws Exception {
        String jsonTemplate = asString(getClass(), "succeed.json");
        return String.format(jsonTemplate, tenantId, eventType);
    }
}
