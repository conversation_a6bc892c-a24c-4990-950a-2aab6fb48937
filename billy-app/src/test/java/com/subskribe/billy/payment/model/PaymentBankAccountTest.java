package com.subskribe.billy.payment.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.shared.enums.PaymentBankAccountStatus;
import java.time.Instant;
import java.util.Currency;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class PaymentBankAccountTest {

    private static PaymentBankAccount bankAccount1;
    private static PaymentBankAccount bankAccount2;

    @BeforeAll
    static void setup() {
        var timestamp = Instant.now();
        var paymentBankAccountId = "BANK-1234567";
        Set<String> entityIds = Set.of("ENT-TEST1");
        Currency currencyCode = Currency.getInstance("USD");
        String cashLedgerAccountId = "LGAC-1234567";
        String expenseLedgerAccountId = "LGAC-7654321";
        PaymentBankAccountStatus status = PaymentBankAccountStatus.DRAFT;
        String externalId = "EXT-1234";
        String name = "Test bank acc";

        bankAccount1 = new PaymentBankAccount();
        bankAccount1.setCreatedOn(timestamp);
        bankAccount1.setUpdatedOn(timestamp);
        bankAccount1.setPaymentBankAccountId(paymentBankAccountId);
        bankAccount1.setEntityIds(entityIds);
        bankAccount1.setCurrencyCode(currencyCode);
        bankAccount1.setCashLedgerAccountId(cashLedgerAccountId);
        bankAccount1.setExpenseLedgerAccountId(expenseLedgerAccountId);
        bankAccount1.setStatus(status);
        bankAccount1.setExternalId(externalId);
        bankAccount1.setDescription("Random desc 1");
        bankAccount1.setName(name);
        bankAccount1.setDeleted(false);

        bankAccount2 = new PaymentBankAccount();
        bankAccount2.setCreatedOn(timestamp);
        bankAccount2.setUpdatedOn(timestamp);
        bankAccount2.setPaymentBankAccountId(paymentBankAccountId);
        bankAccount2.setEntityIds(entityIds);
        bankAccount2.setCurrencyCode(currencyCode);
        bankAccount2.setCashLedgerAccountId(cashLedgerAccountId);
        bankAccount2.setExpenseLedgerAccountId(expenseLedgerAccountId);
        bankAccount2.setStatus(status);
        bankAccount2.setExternalId(externalId);
        bankAccount2.setDescription("Random desc 2");
        bankAccount2.setName(name);
        bankAccount2.setDeleted(false);
    }

    @Test
    void testEquals() {
        assertEquals(bankAccount1, bankAccount2);

        Set<PaymentBankAccount> bankAccounts = new HashSet<>();
        bankAccounts.add(bankAccount1);
        assertTrue(bankAccounts.contains(bankAccount2));

        Map<PaymentBankAccount, Integer> map = new HashMap<>();
        map.put(bankAccount1, 1);

        assertTrue(map.containsKey(bankAccount2));
    }

    @Test
    void testHashCode() {
        assertEquals(bankAccount1.hashCode(), bankAccount2.hashCode());
    }
}
