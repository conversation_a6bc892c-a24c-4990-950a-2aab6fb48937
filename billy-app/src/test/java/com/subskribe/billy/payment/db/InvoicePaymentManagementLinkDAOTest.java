package com.subskribe.billy.payment.db;

import static com.subskribe.billy.jooq.default_schema.tables.InvoicePaymentManagementLink.INVOICE_PAYMENT_MANAGEMENT_LINK;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.jooq.default_schema.tables.records.InvoicePaymentManagementLinkRecord;
import com.subskribe.billy.payment.model.ImmutableInvoicePaymentManagementLink;
import com.subskribe.billy.payment.model.InvoicePaymentManagementLink;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.jooq.MockDSLBuilder;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class InvoicePaymentManagementLinkDAOTest {

    private static final String TENANT_ID = "test tenant 1 id";
    private static final String LINK_ID = UUID.randomUUID().toString();
    private static final String INVOICE_NUMBER = "INV-123456";

    private InvoicePaymentManagementLinkDAO invoicePaymentManagementLinkDAO;

    private DSLContextProvider dslContextProvider;

    @BeforeEach
    void setUp() {
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        dslContextProvider = mock(DSLContextProvider.class);

        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        invoicePaymentManagementLinkDAO = new InvoicePaymentManagementLinkDAO(dslContextProvider, tenantIdProvider);
    }

    @Test
    void createInvoicePaymentManagementLinkReturnsCreatedLink() {
        InvoicePaymentManagementLink linkToCreate = getLink();
        InvoicePaymentManagementLinkRecord expectedRecord = getExpectedRecord();
        InvoicePaymentManagementLinkRecord savedRecord = getSavedRecord();

        DSLContext dslContext = new MockDSLBuilder()
            .when(DSL.using(SQLDialect.POSTGRES).insertInto(INVOICE_PAYMENT_MANAGEMENT_LINK).set(expectedRecord).returning())
            .thenReturn(savedRecord)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        InvoicePaymentManagementLink createdLink = invoicePaymentManagementLinkDAO.createInvoicePaymentManagementLink(linkToCreate);

        assertThat(createdLink).usingRecursiveComparison().ignoringFields("id", "createdOn", "updatedOn").isEqualTo(linkToCreate);
    }

    @Test
    void findPaymentManagementLinkForInvoiceNumberReturnsFoundLinkIfFound() {
        InvoicePaymentManagementLink expectedLink = getLink();
        InvoicePaymentManagementLinkRecord savedRecord = getSavedRecord();

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(INVOICE_PAYMENT_MANAGEMENT_LINK)
                    .where(INVOICE_PAYMENT_MANAGEMENT_LINK.TENANT_ID.eq(TENANT_ID))
                    .and(INVOICE_PAYMENT_MANAGEMENT_LINK.IS_DELETED.isFalse())
                    .and(INVOICE_PAYMENT_MANAGEMENT_LINK.INVOICE_NUMBER.eq(INVOICE_NUMBER))
            )
            .thenReturn(savedRecord)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        Optional<InvoicePaymentManagementLink> foundLink = invoicePaymentManagementLinkDAO.findPaymentManagementLinkForInvoiceNumber(INVOICE_NUMBER);

        assertThat(foundLink).isPresent();
        assertThat(foundLink.get()).usingRecursiveComparison().ignoringFields("id", "createdOn", "updatedOn").isEqualTo(expectedLink);
    }

    @Test
    void findPaymentManagementLinkForInvoiceNumberReturnsEmptyOptionalIfNoLinkFound() {
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(INVOICE_PAYMENT_MANAGEMENT_LINK)
                    .where(INVOICE_PAYMENT_MANAGEMENT_LINK.TENANT_ID.eq(TENANT_ID))
                    .and(INVOICE_PAYMENT_MANAGEMENT_LINK.IS_DELETED.isFalse())
                    .and(INVOICE_PAYMENT_MANAGEMENT_LINK.INVOICE_NUMBER.eq(INVOICE_NUMBER))
            )
            .thenReturnNothingFor(INVOICE_PAYMENT_MANAGEMENT_LINK)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        Optional<InvoicePaymentManagementLink> foundLink = invoicePaymentManagementLinkDAO.findPaymentManagementLinkForInvoiceNumber(INVOICE_NUMBER);

        assertThat(foundLink).isEmpty();
    }

    @Test
    @AllowNonRlsDataAccess
    void findPaymentManagementLinkForLinkIdReturnsFoundLinkIfFound() {
        InvoicePaymentManagementLink expectedLink = getLink();

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(INVOICE_PAYMENT_MANAGEMENT_LINK)
                    .where(INVOICE_PAYMENT_MANAGEMENT_LINK.IS_DELETED.isFalse())
                    .and(INVOICE_PAYMENT_MANAGEMENT_LINK.LINK_ID.eq(LINK_ID))
            )
            .thenReturn(getSavedRecord())
            .context();
        when(dslContextProvider.get()).thenReturn(dslContext);

        Optional<InvoicePaymentManagementLink> foundLink = invoicePaymentManagementLinkDAO.findPaymentManagementLinkForLinkId(LINK_ID);

        assertThat(foundLink).isPresent();
        assertThat(foundLink.get()).usingRecursiveComparison().ignoringFields("id", "createdOn", "updatedOn").isEqualTo(expectedLink);
    }

    @Test
    @AllowNonRlsDataAccess
    void findPaymentManagementLinkForLinkIdReturnsEmptyOptionalIfNoLinkFound() {
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(INVOICE_PAYMENT_MANAGEMENT_LINK)
                    .where(INVOICE_PAYMENT_MANAGEMENT_LINK.IS_DELETED.isFalse())
                    .and(INVOICE_PAYMENT_MANAGEMENT_LINK.LINK_ID.eq(LINK_ID))
            )
            .thenReturnNothingFor(INVOICE_PAYMENT_MANAGEMENT_LINK)
            .context();
        when(dslContextProvider.get()).thenReturn(dslContext);

        Optional<InvoicePaymentManagementLink> foundLink = invoicePaymentManagementLinkDAO.findPaymentManagementLinkForLinkId(LINK_ID);

        assertThat(foundLink).isEmpty();
    }

    @Test
    void testDeletePaymentLinkSuccess() {
        String linkId = UUID.randomUUID().toString();
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .update(INVOICE_PAYMENT_MANAGEMENT_LINK)
                    .set(INVOICE_PAYMENT_MANAGEMENT_LINK.IS_DELETED, true)
                    .where(INVOICE_PAYMENT_MANAGEMENT_LINK.TENANT_ID.eq(TENANT_ID))
                    .and(INVOICE_PAYMENT_MANAGEMENT_LINK.LINK_ID.eq(linkId))
            )
            .thenReturn(1)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        invoicePaymentManagementLinkDAO.deletePaymentLink(linkId);
    }

    @Test
    void testDeletePaymentLinkFailure() {
        String linkId = UUID.randomUUID().toString();
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .update(INVOICE_PAYMENT_MANAGEMENT_LINK)
                    .set(INVOICE_PAYMENT_MANAGEMENT_LINK.IS_DELETED, true)
                    .where(INVOICE_PAYMENT_MANAGEMENT_LINK.TENANT_ID.eq(TENANT_ID))
                    .and(INVOICE_PAYMENT_MANAGEMENT_LINK.LINK_ID.eq(linkId))
            )
            .thenReturn(0)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        assertThatThrownBy(() -> invoicePaymentManagementLinkDAO.deletePaymentLink(linkId)).isInstanceOf(IllegalStateException.class);
    }

    private static InvoicePaymentManagementLinkRecord getSavedRecord() {
        InvoicePaymentManagementLinkRecord record = new InvoicePaymentManagementLinkRecord();
        record.setId(UUID.randomUUID());
        record.setTenantId(TENANT_ID);
        record.setLinkId(LINK_ID);
        record.setInvoiceNumber(INVOICE_NUMBER);
        record.setEntityId(EntityFixture.ENTITY_1_ID);
        record.setIsDeleted(false);
        record.setCreatedOn(DateTimeConverter.instantToLocalDateTime(Instant.now()));
        record.setUpdatedOn(DateTimeConverter.instantToLocalDateTime(Instant.now()));
        return record;
    }

    private static InvoicePaymentManagementLinkRecord getExpectedRecord() {
        InvoicePaymentManagementLinkRecord record = new InvoicePaymentManagementLinkRecord();
        record.setTenantId(TENANT_ID);
        record.setEntityId(EntityFixture.ENTITY_1_ID);
        record.setLinkId(LINK_ID);
        record.setInvoiceNumber(INVOICE_NUMBER);
        record.setIsDeleted(false);
        record.setCreatedOn(null);
        record.setUpdatedOn(null);
        return record;
    }

    private static InvoicePaymentManagementLink getLink() {
        return ImmutableInvoicePaymentManagementLink.builder()
            .linkId(LINK_ID)
            .invoiceNumber(INVOICE_NUMBER)
            .tenantId(TENANT_ID)
            .entityId(EntityFixture.ENTITY_1_ID)
            .build();
    }
}
