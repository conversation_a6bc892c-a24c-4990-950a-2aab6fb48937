package com.subskribe.billy.payment.services;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.payment.db.InvoicePaymentManagementLinkDAO;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.model.ImmutableInvoicePaymentManagementLink;
import com.subskribe.billy.payment.model.InvoicePaymentManagementLink;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class InvoicePaymentManagementLinkServiceTest {

    private static final String TENANT_ID = UUID.randomUUID().toString();

    private static final String ACCOUNT_ID = UUID.randomUUID().toString();

    private static final Invoice.Number INVOICE_NUMBER = new Invoice.Number("INV-123456");

    private InvoicePaymentManagementLinkDAO invoicePaymentManagementLinkDAO;

    private InvoicePaymentManagementLinkService invoicePaymentManagementLinkService;

    @BeforeEach
    void setUp() {
        invoicePaymentManagementLinkDAO = mock(InvoicePaymentManagementLinkDAO.class);
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        PaymentIntegrationGetService paymentIntegrationGetService = mock(PaymentIntegrationGetService.class);
        when(paymentIntegrationGetService.hasIntegration()).thenReturn(true);

        AccountGetService accountGetService = mock(AccountGetService.class);
        when(accountGetService.getAccount(ACCOUNT_ID)).thenReturn(buildAccount());

        InvoiceRetrievalService invoiceRetrievalService = mock(InvoiceRetrievalService.class);
        when(invoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(buildInvoice());

        invoicePaymentManagementLinkService = new InvoicePaymentManagementLinkService(
            invoicePaymentManagementLinkDAO,
            tenantIdProvider,
            paymentIntegrationGetService,
            accountGetService,
            invoiceRetrievalService
        );
    }

    @Test
    void testGetLinkByIdThrowsObjectNotFoundExceptionIfLinkNotFound() {
        String linkId = UUID.randomUUID().toString();

        when(invoicePaymentManagementLinkDAO.findPaymentManagementLinkForLinkId(linkId)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> invoicePaymentManagementLinkService.getLinkById(linkId)).isInstanceOf(ObjectNotFoundException.class);
    }

    @Test
    void testGetLinkByIdReturnsFoundLink() {
        UUID linkId = UUID.randomUUID();
        InvoicePaymentManagementLink link = buildLink();

        when(invoicePaymentManagementLinkDAO.findPaymentManagementLinkForLinkId(linkId.toString())).thenReturn(Optional.of(link));

        InvoicePaymentManagementLink foundLink = invoicePaymentManagementLinkService.getLinkById(linkId.toString());

        assertThat(foundLink).isEqualTo(link);
    }

    @Test
    void testInvalidatePaymentLinkMakesCorrectDAOCall() {
        String linkId = UUID.randomUUID().toString();

        invoicePaymentManagementLinkService.invalidatePaymentLink(linkId);

        verify(invoicePaymentManagementLinkDAO, times(1)).deletePaymentLink(linkId);
    }

    @Test
    void testGetOrCreateInvoicePaymentManagementLinkReturnsExistingLinkIfFound() {
        String invoiceNumber = "INV-123456";
        InvoicePaymentManagementLink link = buildLink();

        when(invoicePaymentManagementLinkDAO.findPaymentManagementLinkForInvoiceNumber(invoiceNumber)).thenReturn(Optional.of(link));

        Optional<InvoicePaymentManagementLink> result = invoicePaymentManagementLinkService.getOrCreateInvoicePaymentManagementLink(
            ACCOUNT_ID,
            invoiceNumber
        );

        Assertions.assertTrue(result.isPresent());
        assertThat(result.get()).isEqualTo(link);
    }

    @Test
    void testGetOrCreateInvoicePaymentManagementLinkReturnsNewlyCreatedLinkIfExistingLinkNotFound() {
        String invoiceNumber = "INV-123456";
        InvoicePaymentManagementLink link = buildLink();
        InvoicePaymentManagementLink expectedRequest = expectedRequest();

        when(invoicePaymentManagementLinkDAO.findPaymentManagementLinkForInvoiceNumber(invoiceNumber)).thenReturn(Optional.empty());
        when(invoicePaymentManagementLinkDAO.createInvoicePaymentManagementLink(any(InvoicePaymentManagementLink.class))).thenReturn(link);

        Optional<InvoicePaymentManagementLink> result = invoicePaymentManagementLinkService.getOrCreateInvoicePaymentManagementLink(
            ACCOUNT_ID,
            invoiceNumber
        );

        Assertions.assertTrue(result.isPresent());
        assertThat(result.get()).isEqualTo(link);

        ArgumentCaptor<InvoicePaymentManagementLink> captor = ArgumentCaptor.forClass(InvoicePaymentManagementLink.class);
        verify(invoicePaymentManagementLinkDAO, times(1)).createInvoicePaymentManagementLink(captor.capture());
        assertThat(captor.getValue()).usingRecursiveComparison().ignoringFields("linkId").isEqualTo(expectedRequest);
    }

    private static InvoicePaymentManagementLink buildLink() {
        return ImmutableInvoicePaymentManagementLink.builder()
            .id(UUID.randomUUID())
            .tenantId(TENANT_ID)
            .entityId(EntityFixture.ENTITY_1_ID)
            .invoiceNumber("INV-123456")
            .linkId(UUID.randomUUID().toString())
            .createdOn(Instant.now())
            .updatedOn(Instant.now())
            .isDeleted(false)
            .build();
    }

    private static InvoicePaymentManagementLink expectedRequest() {
        return ImmutableInvoicePaymentManagementLink.builder()
            .tenantId(TENANT_ID)
            .entityId(EntityFixture.ENTITY_1_ID)
            .invoiceNumber("INV-123456")
            .linkId(UUID.randomUUID().toString())
            .build();
    }

    private static Account buildAccount() {
        Account account = new Account();
        account.setAccountId(ACCOUNT_ID);
        account.setSupportedPaymentTypes(Set.of(PaymentType.CARD, PaymentType.ACH));
        return account;
    }

    private static Invoice buildInvoice() {
        return new Invoice.InvoiceBuilder()
            .invoiceNumber(INVOICE_NUMBER)
            .entityId(EntityFixture.ENTITY_1_ID)
            .currency("USD")
            .status(InvoiceStatus.POSTED)
            .createInvoice();
    }
}
