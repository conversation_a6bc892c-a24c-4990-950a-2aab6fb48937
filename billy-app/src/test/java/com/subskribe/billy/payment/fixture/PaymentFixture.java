package com.subskribe.billy.payment.fixture;

import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.shared.enums.PaymentState;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

public class PaymentFixture {

    private static final BigDecimal PAYMENT_AMOUNT = new BigDecimal("123.45");

    public static Payment.Builder getDefault() {
        return Payment.builder()
            .paymentId(UUID.randomUUID().toString())
            .state(PaymentState.CREATED)
            .entityId(EntityFixture.ENTITY_1_ID)
            .amount(PAYMENT_AMOUNT)
            .paymentDate(Instant.now())
            .currencyCode("USD");
    }
}
