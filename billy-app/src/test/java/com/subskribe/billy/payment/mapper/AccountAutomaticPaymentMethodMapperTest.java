package com.subskribe.billy.payment.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.jooq.default_schema.tables.records.AccountAutomaticPaymentMethodRecord;
import com.subskribe.billy.payment.model.AccountAutomaticPaymentMethod;
import com.subskribe.billy.payment.model.ImmutableAccountAutomaticPaymentMethod;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import java.time.Instant;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class AccountAutomaticPaymentMethodMapperTest {

    private static final String ACCOUNT_ID = UUID.randomUUID().toString();
    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final UUID PAYMENT_METHOD_ID = UUID.randomUUID();
    private static final UUID ID = UUID.randomUUID();
    private static final Instant NOW = Instant.now();
    private static final Instant LATER = Instant.now().plusSeconds(10);

    @Test
    void testToRecord() {
        AccountAutomaticPaymentMethodMapper mapper = new AccountAutomaticPaymentMethodMapperImpl();
        assertThat(mapper.toRecord(createAccountAutomaticPaymentMethod())).isEqualTo(createAccountAutomaticPaymentMethodRecord());
    }

    @Test
    void testFromRecord() {
        AccountAutomaticPaymentMethodMapper mapper = new AccountAutomaticPaymentMethodMapperImpl();
        assertThat(mapper.fromRecord(createAccountAutomaticPaymentMethodRecord())).isEqualTo(createAccountAutomaticPaymentMethod());
    }

    private AccountAutomaticPaymentMethod createAccountAutomaticPaymentMethod() {
        return ImmutableAccountAutomaticPaymentMethod.builder()
            .id(ID)
            .accountId(ACCOUNT_ID)
            .accountPaymentMethodId(PAYMENT_METHOD_ID)
            .tenantId(TENANT_ID)
            .createdOn(NOW)
            .updatedOn(LATER)
            .isDeleted(false)
            .build();
    }

    private AccountAutomaticPaymentMethodRecord createAccountAutomaticPaymentMethodRecord() {
        return new AccountAutomaticPaymentMethodRecord(
            ID,
            TENANT_ID,
            PAYMENT_METHOD_ID,
            ACCOUNT_ID,
            false,
            DateTimeConverter.instantToLocalDateTime(NOW),
            DateTimeConverter.instantToLocalDateTime(LATER)
        );
    }
}
