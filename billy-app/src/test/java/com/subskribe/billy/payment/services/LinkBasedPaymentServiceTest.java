package com.subskribe.billy.payment.services;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.account.model.AccountPaymentMethodStatus;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodService;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.TenantNotFoundException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoicesettlement.model.InvoiceBalance;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.model.AccountAutomaticPaymentMethod;
import com.subskribe.billy.payment.model.AccountAutomaticPaymentMethodDetails;
import com.subskribe.billy.payment.model.AccountPaymentLinkResponse;
import com.subskribe.billy.payment.model.AccountPaymentManagementLink;
import com.subskribe.billy.payment.model.ImmutableAccountAutomaticPaymentMethod;
import com.subskribe.billy.payment.model.ImmutableAccountAutomaticPaymentMethodDetails;
import com.subskribe.billy.payment.model.ImmutableAccountPaymentLinkResponse;
import com.subskribe.billy.payment.model.ImmutableAccountPaymentManagementLink;
import com.subskribe.billy.payment.model.ImmutableInvoicePaymentManagementLink;
import com.subskribe.billy.payment.model.ImmutablePaymentLinkResponse;
import com.subskribe.billy.payment.model.InvoicePaymentManagementLink;
import com.subskribe.billy.payment.model.PaymentLinkResponse;
import com.subskribe.billy.payment.model.PaymentManagementLink;
import com.subskribe.billy.payment.model.PaymentManagementLinkType;
import com.subskribe.billy.payment.model.PaymentProviderPaymentMethod;
import com.subskribe.billy.payment.stripe.model.PaymentStripeConnectIntegration;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.TenantInfo;
import com.subskribe.billy.tenant.services.TenantService;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InOrder;

public class LinkBasedPaymentServiceTest {

    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final String TENANT_NAME = "Test tenant";
    private static final String ACCOUNT_NAME = "Account name";
    private static final long NOW = Instant.now().getEpochSecond();
    private static final String LINK_ID = UUID.randomUUID().toString();
    private static final String ACCOUNT_LINK_ID = UUID.randomUUID().toString();
    private static final String INTEGRATION_ID = UUID.randomUUID().toString();
    private static final String ACCOUNT_ID = UUID.randomUUID().toString();
    private static final String SUBSCRIPTION_ID = UUID.randomUUID().toString();
    private static final UUID ACCOUNT_PAYMENT_METHOD_ID = UUID.randomUUID();
    private static final UUID PAYMENT_METHOD_ID = UUID.randomUUID();
    private static final Invoice.Number INVOICE_NUMBER = new Invoice.Number("INV-123456");
    private static final BigDecimal INVOICE_BALANCE = new BigDecimal("9.99");
    private static final String CURRENCY_CODE = "USD";
    private static final Set<PaymentType> SUPPORTED_PAYMENT_TYPES = Set.of(PaymentType.CARD, PaymentType.ACH);

    private LinkBasedPaymentService linkBasedPaymentService;

    private InvoicePaymentManagementLinkService invoicePaymentManagementLinkService;
    private AccountPaymentManagementLinkService accountPaymentManagementLinkService;
    private AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService;
    private InvoiceSettlementService invoiceSettlementService;
    private InvoiceRetrievalService invoiceRetrievalService;
    private PaymentIntegrationGetService paymentIntegrationGetService;
    private TenantIdProvider tenantIdProvider;
    private AccountGetService accountGetService;
    private TenantService tenantService;
    private AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService;

    @BeforeEach
    void setUp() {
        invoicePaymentManagementLinkService = mock(InvoicePaymentManagementLinkService.class);
        accountPaymentManagementLinkService = mock(AccountPaymentManagementLinkService.class);
        accountAutomaticPaymentMethodService = mock(AccountAutomaticPaymentMethodService.class);
        invoiceSettlementService = mock(InvoiceSettlementService.class);
        invoiceRetrievalService = mock(InvoiceRetrievalService.class);
        paymentIntegrationGetService = mock(PaymentIntegrationGetService.class);
        tenantIdProvider = mock(TenantIdProvider.class);
        accountGetService = mock(AccountGetService.class);
        tenantService = mock(TenantService.class);
        accountAutomaticPaymentMethodGetService = mock(AccountAutomaticPaymentMethodGetService.class);
        PaymentGetService paymentGetService = mock(PaymentGetService.class);
        AccountPaymentMethodService accountPaymentMethodService = mock(AccountPaymentMethodService.class);
        PaymentProcessorService paymentProcessorService = mock(PaymentProcessorService.class);

        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        linkBasedPaymentService = new LinkBasedPaymentService(
            invoicePaymentManagementLinkService,
            accountPaymentManagementLinkService,
            accountAutomaticPaymentMethodService,
            invoiceSettlementService,
            invoiceRetrievalService,
            paymentIntegrationGetService,
            tenantIdProvider,
            accountGetService,
            tenantService,
            paymentGetService,
            accountPaymentMethodService,
            accountAutomaticPaymentMethodGetService,
            paymentProcessorService
        );
    }

    @Test
    void testCreatePaymentLinkResponseRethrowsExceptionIfNotFound() {
        ObjectNotFoundException ex = new ObjectNotFoundException(BillyObjectType.INVOICE_PAYMENT_MANAGEMENT_LINK, LINK_ID);
        when(invoicePaymentManagementLinkService.getLinkById(LINK_ID)).thenThrow(ex);

        assertThatThrownBy(() -> linkBasedPaymentService.createInvoicePaymentLinkResponse(LINK_ID)).isEqualTo(ex);
    }

    @Test
    void testCreatePaymentLinkSetsTenantIdAndClearsLocalThreadOnFail() {
        InvoicePaymentManagementLink link = buildLink();
        when(invoicePaymentManagementLinkService.getLinkById(LINK_ID)).thenReturn(link);
        when(paymentIntegrationGetService.getPaymentIntegration()).thenThrow(new RuntimeException());
        when(tenantIdProvider.provideTenantIdString()).thenThrow(TenantNotFoundException.class);

        assertThatThrownBy(() -> linkBasedPaymentService.createInvoicePaymentLinkResponse(LINK_ID)).isInstanceOf(RuntimeException.class);

        InOrder inOrder = inOrder(tenantIdProvider);
        inOrder.verify(tenantIdProvider, times(1)).setTenantId(new TenantId(TENANT_ID));
        inOrder.verify(tenantIdProvider, times(1)).clearThreadLocal();
    }

    @Test
    void testCreatePaymentLinkThrowsObjectNotFoundExceptionIfNoIntegration() {
        InvoicePaymentManagementLink link = buildLink();
        when(invoicePaymentManagementLinkService.getLinkById(LINK_ID)).thenReturn(link);
        when(paymentIntegrationGetService.getCompletedStripeConnectIntegration()).thenReturn(Optional.empty());

        assertThatThrownBy(() -> linkBasedPaymentService.createInvoicePaymentLinkResponse(LINK_ID)).isInstanceOf(ObjectNotFoundException.class);
    }

    @Test
    void testCreatePaymentLinkWithoutActiveAutoPay() {
        setupMocksForSuccess();

        when(accountAutomaticPaymentMethodGetService.getActiveMethodDetailsForAccount(ACCOUNT_ID)).thenReturn(
            ImmutableAccountAutomaticPaymentMethodDetails.builder().build()
        );

        PaymentLinkResponse response = linkBasedPaymentService.createInvoicePaymentLinkResponse(LINK_ID);

        PaymentLinkResponse expected = ImmutablePaymentLinkResponse.builder().from(baseResponse()).isCustomerEnrolledInAutoPay(false).build();

        assertThat(response).isEqualTo(expected);
    }

    @Test
    void testCreatePaymentLinkWithZeroBalance() {
        setupMocksForSuccess();

        when(accountAutomaticPaymentMethodGetService.getActiveMethodDetailsForAccount(ACCOUNT_ID)).thenReturn(
            ImmutableAccountAutomaticPaymentMethodDetails.builder().build()
        );

        when(invoiceSettlementService.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(getInvoiceBalance(new BigDecimal("0.00")));

        PaymentLinkResponse response = linkBasedPaymentService.createInvoicePaymentLinkResponse(LINK_ID);

        PaymentLinkResponse expected = ImmutablePaymentLinkResponse.builder()
            .from(baseResponse())
            .isCustomerEnrolledInAutoPay(false)
            .invoiceBalance(new BigDecimal("0.00"))
            .build();

        assertThat(response).isEqualTo(expected);
    }

    @Test
    void testCreatePaymentLinkWithExistingPayment() {
        setupMocksForSuccess();

        SettlementApplication settlementApplication = mock(SettlementApplication.class);

        when(accountAutomaticPaymentMethodGetService.getActiveMethodDetailsForAccount(ACCOUNT_ID)).thenReturn(
            ImmutableAccountAutomaticPaymentMethodDetails.builder().build()
        );
        when(invoiceSettlementService.findPendingPaymentCollectionAttemptForInvoice(INVOICE_NUMBER)).thenReturn(Optional.of(settlementApplication));

        PaymentLinkResponse response = linkBasedPaymentService.createInvoicePaymentLinkResponse(LINK_ID);

        PaymentLinkResponse expected = ImmutablePaymentLinkResponse.builder()
            .from(baseResponse())
            .isCustomerEnrolledInAutoPay(false)
            .hasOngoingPayment(true)
            .build();

        assertThat(response).isEqualTo(expected);
    }

    @Test
    void testCreatePaymentLinkWithActiveAutoPay() {
        setupMocksForSuccess();

        PaymentProviderPaymentMethod paymentProviderPaymentMethod = buildPaymentMethod();
        AccountPaymentMethod accountPaymentMethod = buildAccountPaymentMethod();
        AccountAutomaticPaymentMethodDetails accountAutomaticPaymentMethodDetails = ImmutableAccountAutomaticPaymentMethodDetails.builder()
            .accountAutomaticPaymentMethod(Optional.of(buildAccountAutomaticPaymentMethod()))
            .accountPaymentMethod(Optional.of(accountPaymentMethod))
            .paymentProviderPaymentMethod(Optional.of(paymentProviderPaymentMethod))
            .build();
        when(accountAutomaticPaymentMethodGetService.getActiveMethodDetailsForAccount(ACCOUNT_ID)).thenReturn(accountAutomaticPaymentMethodDetails);

        PaymentLinkResponse response = linkBasedPaymentService.createInvoicePaymentLinkResponse(LINK_ID);

        PaymentLinkResponse expected = ImmutablePaymentLinkResponse.builder()
            .from(baseResponse())
            .isCustomerEnrolledInAutoPay(true)
            .enrolledPaymentMethod(paymentProviderPaymentMethod)
            .enrolledAccountPaymentMethod(accountPaymentMethod)
            .build();

        assertThat(response).isEqualTo(expected);
    }

    @Test
    void testCreatePaymentLinkWithActiveAutoPayReturnsEmptyOptionalIfAccountMethodNotFound() {
        setupMocksForSuccess();

        AccountAutomaticPaymentMethodDetails accountAutomaticPaymentMethodDetails = ImmutableAccountAutomaticPaymentMethodDetails.builder()
            .accountAutomaticPaymentMethod(Optional.of(buildAccountAutomaticPaymentMethod()))
            .build();
        when(accountAutomaticPaymentMethodGetService.getActiveMethodDetailsForAccount(ACCOUNT_ID)).thenReturn(accountAutomaticPaymentMethodDetails);

        PaymentLinkResponse response = linkBasedPaymentService.createInvoicePaymentLinkResponse(LINK_ID);

        PaymentLinkResponse expected = ImmutablePaymentLinkResponse.builder()
            .from(baseResponse())
            .isCustomerEnrolledInAutoPay(true)
            .enrolledPaymentMethod(Optional.empty())
            .build();

        assertThat(response).isEqualTo(expected);
    }

    @Test
    void testCreatePaymentLinkWithActiveAutoPayReturnsEmptyOptionalIfProviderMethodNotFound() {
        setupMocksForSuccess();

        AccountPaymentMethod accountPaymentMethod = buildAccountPaymentMethod();
        AccountAutomaticPaymentMethodDetails accountAutomaticPaymentMethodDetails = ImmutableAccountAutomaticPaymentMethodDetails.builder()
            .accountAutomaticPaymentMethod(Optional.of(buildAccountAutomaticPaymentMethod()))
            .accountPaymentMethod(Optional.of(accountPaymentMethod))
            .build();
        when(accountAutomaticPaymentMethodGetService.getActiveMethodDetailsForAccount(ACCOUNT_ID)).thenReturn(accountAutomaticPaymentMethodDetails);

        PaymentLinkResponse response = linkBasedPaymentService.createInvoicePaymentLinkResponse(LINK_ID);

        PaymentLinkResponse expected = ImmutablePaymentLinkResponse.builder()
            .from(baseResponse())
            .isCustomerEnrolledInAutoPay(true)
            .enrolledPaymentMethod(Optional.empty())
            .enrolledAccountPaymentMethod(accountPaymentMethod)
            .build();

        assertThat(response).isEqualTo(expected);
    }

    private PaymentLinkResponse baseResponse() {
        return ImmutablePaymentLinkResponse.builder()
            .connectAccountId(INTEGRATION_ID)
            .accountId(ACCOUNT_ID)
            .subscriptionId(SUBSCRIPTION_ID)
            .invoiceNumber(INVOICE_NUMBER.getNumber())
            .invoiceBalance(INVOICE_BALANCE)
            .invoiceCurrencyCode(CURRENCY_CODE)
            .invoiceFrom(TENANT_NAME)
            .invoiceTo(ACCOUNT_NAME)
            .invoiceDueDate(NOW)
            .isCustomerEnrolledInAutoPay(false)
            .supportedPaymentTypes(SUPPORTED_PAYMENT_TYPES)
            .enrolledPaymentMethod(Optional.empty())
            .hasOngoingPayment(false)
            .invoiceStatus(InvoiceStatus.DRAFT)
            .build();
    }

    private AccountPaymentLinkResponse baseAccountResponse() {
        return ImmutableAccountPaymentLinkResponse.builder()
            .connectAccountId(INTEGRATION_ID)
            .accountId(ACCOUNT_ID)
            .isCustomerEnrolledInAutoPay(false)
            .supportedPaymentTypes(SUPPORTED_PAYMENT_TYPES)
            .enrolledPaymentMethod(Optional.empty())
            .build();
    }

    @Test
    void testUnEnrollFromAutomaticPaymentsCallsServiceCorrectlyForInvoices() {
        when(invoicePaymentManagementLinkService.getLinkById(LINK_ID)).thenReturn(buildLink());
        when(invoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice());

        linkBasedPaymentService.unEnrollFromAutomaticPayments(LINK_ID, PaymentManagementLinkType.INVOICE);

        verify(accountAutomaticPaymentMethodService, times(1)).removeActiveMethodsForAccount(ACCOUNT_ID);
    }

    @Test
    void testUnEnrollFromAutomaticPaymentsCallsServiceCorrectlyForAccountLinks() {
        when(accountPaymentManagementLinkService.getLinkById(LINK_ID)).thenReturn(buildAccountLink());

        linkBasedPaymentService.unEnrollFromAutomaticPayments(LINK_ID, PaymentManagementLinkType.ACCOUNT);

        verify(accountAutomaticPaymentMethodService, times(1)).removeActiveMethodsForAccount(ACCOUNT_ID);
    }

    @Test
    void testGetPaymentManagementLinkReturnsInvoiceLink() {
        InvoicePaymentManagementLink stub = buildLink();

        when(invoicePaymentManagementLinkService.getLinkById(LINK_ID)).thenReturn(stub);

        PaymentManagementLink result = linkBasedPaymentService.getPaymentManagementLink(LINK_ID, PaymentManagementLinkType.INVOICE);

        assertThat(result).isEqualTo(stub);
    }

    @Test
    void testCreateAccountPaymentLinkResponseRethrowsExceptionIfNotFound() {
        ObjectNotFoundException ex = new ObjectNotFoundException(BillyObjectType.ACCOUNT_PAYMENT_MANAGEMENT_LINK, ACCOUNT_LINK_ID);
        when(accountPaymentManagementLinkService.getLinkById(ACCOUNT_LINK_ID)).thenThrow(ex);

        assertThatThrownBy(() -> linkBasedPaymentService.createAccountPaymentLinkResponse(ACCOUNT_LINK_ID)).isEqualTo(ex);
    }

    @Test
    void testCreateAccountPaymentLinkThrowsObjectNotFoundExceptionIfNoIntegration() {
        AccountPaymentManagementLink link = buildAccountLink();
        when(accountPaymentManagementLinkService.getLinkById(ACCOUNT_LINK_ID)).thenReturn(link);
        when(paymentIntegrationGetService.getCompletedStripeConnectIntegration()).thenReturn(Optional.empty());

        assertThatThrownBy(() -> linkBasedPaymentService.createAccountPaymentLinkResponse(ACCOUNT_LINK_ID)).isInstanceOf(
            ObjectNotFoundException.class
        );
    }

    @Test
    void testCreateAccountPaymentLinkWithoutActiveAutoPay() {
        setupMocksForSuccess();

        when(accountAutomaticPaymentMethodGetService.getActiveMethodDetailsForAccount(ACCOUNT_ID)).thenReturn(
            ImmutableAccountAutomaticPaymentMethodDetails.builder().build()
        );

        AccountPaymentLinkResponse response = linkBasedPaymentService.createAccountPaymentLinkResponse(LINK_ID);

        AccountPaymentLinkResponse expected = ImmutableAccountPaymentLinkResponse.builder()
            .from(baseAccountResponse())
            .isCustomerEnrolledInAutoPay(false)
            .build();

        assertThat(response).isEqualTo(expected);
    }

    @Test
    void testCreateAccountPaymentLinkWithActiveAutoPay() {
        setupMocksForSuccess();

        PaymentProviderPaymentMethod paymentProviderPaymentMethod = buildPaymentMethod();
        AccountPaymentMethod accountPaymentMethod = buildAccountPaymentMethod();
        AccountAutomaticPaymentMethodDetails accountAutomaticPaymentMethodDetails = ImmutableAccountAutomaticPaymentMethodDetails.builder()
            .accountAutomaticPaymentMethod(Optional.of(buildAccountAutomaticPaymentMethod()))
            .accountPaymentMethod(Optional.of(accountPaymentMethod))
            .paymentProviderPaymentMethod(Optional.of(paymentProviderPaymentMethod))
            .build();
        when(accountAutomaticPaymentMethodGetService.getActiveMethodDetailsForAccount(ACCOUNT_ID)).thenReturn(accountAutomaticPaymentMethodDetails);

        AccountPaymentLinkResponse response = linkBasedPaymentService.createAccountPaymentLinkResponse(LINK_ID);

        AccountPaymentLinkResponse expected = ImmutableAccountPaymentLinkResponse.builder()
            .from(baseAccountResponse())
            .isCustomerEnrolledInAutoPay(true)
            .enrolledPaymentMethod(paymentProviderPaymentMethod)
            .enrolledAccountPaymentMethod(accountPaymentMethod)
            .build();

        assertThat(response).isEqualTo(expected);
    }

    private PaymentProviderPaymentMethod buildPaymentMethod() {
        PaymentProviderPaymentMethod paymentProviderPaymentMethod = new PaymentProviderPaymentMethod();
        paymentProviderPaymentMethod.setPaymentMethodId(PAYMENT_METHOD_ID.toString());
        return paymentProviderPaymentMethod;
    }

    private AccountPaymentMethod buildAccountPaymentMethod() {
        AccountPaymentMethod accountPaymentMethod = new AccountPaymentMethod();
        accountPaymentMethod.setAccountId(ACCOUNT_ID);
        accountPaymentMethod.setPaymentMethodId(PAYMENT_METHOD_ID.toString());
        accountPaymentMethod.setStatus(AccountPaymentMethodStatus.ACTIVE);
        return accountPaymentMethod;
    }

    private void setupMocksForSuccess() {
        when(invoicePaymentManagementLinkService.getLinkById(LINK_ID)).thenReturn(buildLink());
        when(accountPaymentManagementLinkService.getLinkById(LINK_ID)).thenReturn(buildAccountLink());
        when(paymentIntegrationGetService.getCompletedStripeConnectIntegration()).thenReturn(Optional.of(getPaymentStripeConnectIntegration()));
        when(invoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice());
        when(invoiceSettlementService.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(getInvoiceBalance(INVOICE_BALANCE));
        when(invoiceSettlementService.findPendingPaymentCollectionAttemptForInvoice(INVOICE_NUMBER)).thenReturn(Optional.empty());
        when(accountGetService.getAccount(ACCOUNT_ID)).thenReturn(account());
        when(tenantService.getCurrentTenantInfo()).thenReturn(tenant());
    }

    private static Account account() {
        Account account = new Account();
        account.setAccountId(ACCOUNT_ID);
        account.setName(ACCOUNT_NAME);
        account.setSupportedPaymentTypes(SUPPORTED_PAYMENT_TYPES);
        return account;
    }

    private static TenantInfo tenant() {
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId(TENANT_ID);
        tenantInfo.setName(TENANT_NAME);
        return tenantInfo;
    }

    private static Invoice getInvoice() {
        return new Invoice.InvoiceBuilder()
            .currency(CURRENCY_CODE)
            .invoiceNumber(INVOICE_NUMBER)
            .customerAccountId(ACCOUNT_ID)
            .subscriptionId(SUBSCRIPTION_ID)
            .dueDate(Instant.ofEpochSecond(NOW))
            .createInvoice();
    }

    private static InvoiceBalance getInvoiceBalance(BigDecimal balance) {
        return new InvoiceBalance(UUID.randomUUID(), ACCOUNT_ID, INVOICE_NUMBER.getNumber(), balance, CURRENCY_CODE, Instant.now());
    }

    private static PaymentStripeConnectIntegration getPaymentStripeConnectIntegration() {
        PaymentStripeConnectIntegration paymentIntegration = new PaymentStripeConnectIntegration();
        paymentIntegration.setConnectAccountId(INTEGRATION_ID);
        return paymentIntegration;
    }

    private InvoicePaymentManagementLink buildLink() {
        return ImmutableInvoicePaymentManagementLink.builder()
            .linkId(LINK_ID)
            .invoiceNumber(INVOICE_NUMBER.getNumber())
            .tenantId(TENANT_ID)
            .entityId(EntityFixture.ENTITY_1_ID)
            .isDeleted(false)
            .id(UUID.randomUUID())
            .build();
    }

    private AccountPaymentManagementLink buildAccountLink() {
        return ImmutableAccountPaymentManagementLink.builder()
            .linkId(ACCOUNT_LINK_ID)
            .accountId(ACCOUNT_ID)
            .tenantId(TENANT_ID)
            .isDeleted(false)
            .id(UUID.randomUUID())
            .build();
    }

    private AccountAutomaticPaymentMethod buildAccountAutomaticPaymentMethod() {
        return ImmutableAccountAutomaticPaymentMethod.builder()
            .accountPaymentMethodId(ACCOUNT_PAYMENT_METHOD_ID)
            .id(UUID.randomUUID())
            .accountId(ACCOUNT_ID)
            .tenantId(TENANT_ID)
            .isDeleted(false)
            .build();
    }
}
