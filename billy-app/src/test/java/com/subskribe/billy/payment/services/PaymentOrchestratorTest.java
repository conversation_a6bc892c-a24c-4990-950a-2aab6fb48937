package com.subskribe.billy.payment.services;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.payment.model.ImmutablePaymentAttemptMetadata;
import com.subskribe.billy.payment.model.PaymentAttempt;
import com.subskribe.billy.payment.model.PaymentAttemptMetadata;
import com.subskribe.billy.payment.model.PaymentAttemptState;
import com.subskribe.billy.payment.task.PaymentRetryQueuedTaskProcessor;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class PaymentOrchestratorTest {

    private PaymentGetService paymentGetService;
    private PaymentOrchestrator orchestrator;

    @BeforeEach
    public void setup() {
        paymentGetService = mock(PaymentGetService.class);
        // Add other required constructor params as per PaymentOrchestrator's definition
        orchestrator = new PaymentOrchestrator(
            null,
            null,
            null,
            paymentGetService,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );
    }

    @Test
    public void testNoPaymentAttemptsThrowsInvalidInputException() {
        when(paymentGetService.getPaymentAttemptsByPaymentId("PAY-1")).thenReturn(List.of());

        assertThrows(InvalidInputException.class, () ->
            orchestrator.validatePaymentAttemptStateForRetry(
                PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
                "PAY-1",
                UUID.randomUUID().toString()
            )
        );
    }

    @Test
    public void testMismatchedLastAttemptIdThrowsForAutomaticRetry() {
        UUID lastAttemptId = UUID.randomUUID();
        UUID dbAttemptId = UUID.randomUUID();

        PaymentAttemptMetadata.AttemptRetryMetadata retryMetadata = new PaymentAttemptMetadata.AttemptRetryMetadata(
            PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_FIRST_ATTEMPT,
            "Retry",
            Optional.of(1)
        );

        PaymentAttemptMetadata metadata = ImmutablePaymentAttemptMetadata.builder().retryMetadata(retryMetadata).build();

        PaymentAttempt lastAttempt = PaymentAttempt.builder()
            .id(dbAttemptId)
            .state(PaymentAttemptState.FAILED)
            .attemptNumber(1)
            .attemptedOn(Instant.now())
            .metadata(metadata)
            .build();

        when(paymentGetService.getPaymentAttemptsByPaymentId("PAY-2")).thenReturn(List.of(lastAttempt));

        assertThrows(ServiceFailureException.class, () ->
            orchestrator.validatePaymentAttemptStateForRetry(
                PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
                "PAY-2",
                lastAttemptId.toString()
            )
        );
    }

    @Test
    public void testMismatchedLastAttemptIdReturnsRetriedAfterManualAttemptForAutomaticRetry() {
        UUID lastAttemptId = UUID.randomUUID();
        UUID dbAttemptId = UUID.randomUUID();

        PaymentAttemptMetadata.AttemptRetryMetadata retryMetadata = new PaymentAttemptMetadata.AttemptRetryMetadata(
            PaymentOrchestrator.PaymentAttemptType.MANUAL_RETRY_ATTEMPT,
            "Retry",
            Optional.of(1)
        );

        PaymentAttemptMetadata metadata = ImmutablePaymentAttemptMetadata.builder().retryMetadata(retryMetadata).build();

        PaymentAttempt lastAttempt = PaymentAttempt.builder()
            .id(dbAttemptId)
            .state(PaymentAttemptState.FAILED)
            .attemptNumber(1)
            .attemptedOn(Instant.now())
            .metadata(metadata)
            .build();

        when(paymentGetService.getPaymentAttemptsByPaymentId("PAY-2")).thenReturn(List.of(lastAttempt));

        Optional<PaymentRetryQueuedTaskProcessor.PaymentRetryResult> result = orchestrator.validatePaymentAttemptStateForRetry(
            PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
            "PAY-2",
            lastAttemptId.toString()
        );

        assertTrue(result.isPresent());
        assertEquals(PaymentRetryQueuedTaskProcessor.RETRY_ACTION.RETRIED_AFTER_MANUAL_ATTEMPT, result.get().retryAction());
        assertNotNull(result.get().detail());
        assertTrue(result.get().manualPaymentAttemptId().isPresent());
        assertEquals(dbAttemptId, UUID.fromString(result.get().manualPaymentAttemptId().get()));
    }

    @Test
    public void testMismatchedLastAttemptIdThrowsForManualRetry() {
        UUID lastAttemptId = UUID.randomUUID();
        UUID dbAttemptId = UUID.randomUUID();

        PaymentAttemptMetadata.AttemptRetryMetadata retryMetadata = new PaymentAttemptMetadata.AttemptRetryMetadata(
            PaymentOrchestrator.PaymentAttemptType.MANUAL_RETRY_ATTEMPT,
            "Manual Retry",
            Optional.empty()
        );

        PaymentAttemptMetadata metadata = ImmutablePaymentAttemptMetadata.builder().retryMetadata(retryMetadata).build();

        PaymentAttempt lastAttempt = PaymentAttempt.builder()
            .id(dbAttemptId)
            .state(PaymentAttemptState.FAILED)
            .attemptNumber(1)
            .attemptedOn(Instant.now())
            .metadata(metadata)
            .build();

        when(paymentGetService.getPaymentAttemptsByPaymentId("PAY-3")).thenReturn(List.of(lastAttempt));

        assertThrows(InvalidInputException.class, () ->
            orchestrator.validatePaymentAttemptStateForRetry(
                PaymentOrchestrator.PaymentAttemptType.MANUAL_RETRY_ATTEMPT,
                "PAY-3",
                lastAttemptId.toString()
            )
        );
    }

    @Test
    public void testLastAttemptNotFailedThrowsServiceFailureException() {
        UUID attemptId = UUID.randomUUID();

        PaymentAttemptMetadata.AttemptRetryMetadata retryMetadata = new PaymentAttemptMetadata.AttemptRetryMetadata(
            PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
            "Retry",
            Optional.of(1)
        );

        PaymentAttemptMetadata metadata = ImmutablePaymentAttemptMetadata.builder().retryMetadata(retryMetadata).build();

        PaymentAttempt lastAttempt = PaymentAttempt.builder()
            .id(attemptId)
            .state(PaymentAttemptState.SUCCEEDED)
            .attemptNumber(1)
            .attemptedOn(Instant.now())
            .metadata(metadata)
            .build();

        when(paymentGetService.getPaymentAttemptsByPaymentId("PAY-4")).thenReturn(List.of(lastAttempt));

        assertThrows(ServiceFailureException.class, () ->
            orchestrator.validatePaymentAttemptStateForRetry(
                PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
                "PAY-4",
                attemptId.toString()
            )
        );
    }

    @Test
    public void testNonTerminalAttemptThrowsServiceFailureException() {
        UUID attemptId = UUID.randomUUID();

        PaymentAttemptMetadata.AttemptRetryMetadata retryMetadata = new PaymentAttemptMetadata.AttemptRetryMetadata(
            PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
            "Retry",
            Optional.of(1)
        );

        PaymentAttemptMetadata metadata = ImmutablePaymentAttemptMetadata.builder().retryMetadata(retryMetadata).build();

        PaymentAttempt lastAttempt = PaymentAttempt.builder()
            .id(attemptId)
            .state(PaymentAttemptState.FAILED)
            .attemptNumber(1)
            .attemptedOn(Instant.now())
            .metadata(metadata)
            .build();

        PaymentAttempt nonTerminalAttempt = PaymentAttempt.builder()
            .id(UUID.randomUUID())
            .state(PaymentAttemptState.CREATED)
            .attemptNumber(2)
            .attemptedOn(Instant.now())
            .metadata(metadata)
            .build();

        when(paymentGetService.getPaymentAttemptsByPaymentId("PAY-5")).thenReturn(List.of(lastAttempt, nonTerminalAttempt));

        assertThrows(ServiceFailureException.class, () ->
            orchestrator.validatePaymentAttemptStateForRetry(
                PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
                "PAY-5",
                attemptId.toString()
            )
        );
    }

    @Test
    public void testValidRetryReturnsEmptyOptional() {
        UUID attemptId = UUID.randomUUID();

        PaymentAttemptMetadata.AttemptRetryMetadata retryMetadata = new PaymentAttemptMetadata.AttemptRetryMetadata(
            PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
            "Retry",
            Optional.of(1)
        );

        PaymentAttemptMetadata metadata = ImmutablePaymentAttemptMetadata.builder().retryMetadata(retryMetadata).build();

        PaymentAttempt lastAttempt = PaymentAttempt.builder()
            .id(attemptId)
            .state(PaymentAttemptState.FAILED)
            .attemptNumber(1)
            .attemptedOn(Instant.now())
            .metadata(metadata)
            .build();

        when(paymentGetService.getPaymentAttemptsByPaymentId("PAY-6")).thenReturn(List.of(lastAttempt));

        Optional<PaymentRetryQueuedTaskProcessor.PaymentRetryResult> result = orchestrator.validatePaymentAttemptStateForRetry(
            PaymentOrchestrator.PaymentAttemptType.AUTOMATIC_RETRY_ATTEMPT,
            "PAY-6",
            attemptId.toString()
        );

        assertTrue(result.isEmpty());
    }
}
