package com.subskribe.billy.dataimport.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.subskribe.billy.exception.InvalidInputException;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class StringParserTest {

    @Test
    public void parseSingleKeyValuePair() {
        final String input = "key:value";

        Map<String, String> result = StringParser.parseKeyValuePairs(input);

        assertEquals(1, result.size());
        assertEquals("value", result.get("key"));
        assertEquals(input, StringParser.buildKeyValueString(result));
    }

    @Test
    public void emptyInputShouldReturnEmptyMap() {
        final String input = "";
        Map<String, String> result = StringParser.parseKeyValuePairs(input);

        assertEquals(0, result.size());
        assertEquals(input, StringParser.buildKeyValueString(result));
    }

    @Test
    public void parseKeyValuePairs() {
        final String input = "key1:value1|key2:value2|key3:value3";
        Map<String, String> result = StringParser.parseKeyValuePairs(input);

        assertEquals(3, result.size());
        assertEquals("value1", result.get("key1"));
        assertEquals("value2", result.get("key2"));
        assertEquals("value3", result.get("key3"));
        assertEquals(input, StringParser.buildKeyValueString(result));
    }

    @Test
    public void parseMalformedInput() {
        List<String> inputs = List.of("|", "key|value", "key1:value|key2");

        inputs.forEach(input -> assertThrows(InvalidInputException.class, () -> StringParser.parseKeyValuePairs(input)));
    }
}
