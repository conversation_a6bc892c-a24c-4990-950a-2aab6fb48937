package com.subskribe.billy.dataimport.schema.catalog;

import com.subskribe.billy.dataimport.schema.BaseSchemaTest;
import org.junit.jupiter.api.Test;

public class CatalogDomainSchemaTest extends BaseSchemaTest {

    @Test
    void allCatalogDomainColumnsArePresentInSchemaJson() throws Exception {
        validateAllColumnsArePresentInSchema("flatfile/catalogSchema.json", CatalogDomainColumns.CATALOG_COLUMNS);
    }
}
