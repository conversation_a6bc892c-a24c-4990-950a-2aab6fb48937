package com.subskribe.billy.dataimport.processors;

import static com.subskribe.billy.TestConstants.ZONE_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.aws.s3.S3ClientProvider;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldProxy;
import com.subskribe.billy.dataimport.model.DataImport;
import com.subskribe.billy.dataimport.model.ImportDomain;
import com.subskribe.billy.dataimport.model.ImportOperation;
import com.subskribe.billy.dataimport.model.ImportStatus;
import com.subskribe.billy.dataimport.schema.order.OrderDomainCreateSchema;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.document.model.DocumentConfiguration;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.invoice.SellingPriceCalculator;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.DiscountCalculator;
import com.subskribe.billy.invoice.service.InvoiceAmountCalculator;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.invoice.service.processor.OneTimeInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PrepaidInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.RecurringInvoiceProcessor;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.ChangeOrderService;
import com.subskribe.billy.order.services.MissingOrderChargesService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PriceTier;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.enums.PercentDerivedFrom;
import com.subskribe.billy.shared.enums.SigningOrder;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.model.TenantSettingSeal;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.user.service.UserService;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Currency;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.UUID;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.collect.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

public class OrderDomainImportProcessorTest {

    private static final String BUCKET = "coco-bucket";
    private static final String OBJECT = "spicy-sauce";
    private static final String IMPORT_ID = "its-raining-tacos";

    private static final String TEST_OPPORTUNITY_ID = "5003000000D8cuI";
    private static final String TEST_OPPORTUNITY_NAME = "The golden ticket";
    private static final String TEST_ORDER_NAME = "order name";
    private static final String TEST_TENANT = "biggie-smalls";
    private static final String TEST_ACCOUNT_ID = "ACCT-DDPGQV7";
    private static final String TEST_CONTACT_ID = "CONT-E03P85G";
    private static final String TEST_PURCHASE_ORDER_NUMBER = "PO#8675309";
    private static final TimeZone DEFAULT_TIMEZONE = TimeZone.getTimeZone("America/Los_Angeles");
    private static final Currency DEFAULT_CURRENCY = Currency.getInstance("USD");

    private static final String ORDER_IMPORT_DIRECTORY_PATH_FORMAT = "com/subskribe/billy/test/order_domain_import/%s";

    private static final String IMPORT_CREATE_CSV_FILENAME = "order_create_import.csv";
    private static final String EXPECTED_CREATE_CSV_FILENAME = "order_create_expected.csv";

    @Mock
    private S3ClientProvider s3ClientProvider;

    @Mock
    private S3Client s3Client;

    @Mock
    private AccountGetService accountGetService;

    @Mock
    private SubscriptionGetService subscriptionGetService;

    private final EntityGetService entityGetService = EntityGetServiceFixture.entityGetServiceFixture();

    @Mock
    private OrderGetService orderGetService;

    @Mock
    private ProductCatalogGetService productCatalogGetService;

    @Mock
    private MetricsService metricsService;

    @Mock
    private ProductCatalogService catalogService;

    @Mock
    private ProductCatalogGetService catalogGetService;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private OrderService orderService;

    @Mock
    private ChangeOrderService changeOrderService;

    @Mock
    private ProrationConfigurationGetService prorationConfigurationGetService;

    @Mock
    private EntityContextResolver entityContextResolver;

    @Mock
    private TenantSettingService tenantSettingService;

    @Mock
    private SubscriptionBillingPeriodService subscriptionBillingPeriodService;

    @Mock
    private RateCardService rateCardService;

    @Mock
    private RecurringInvoiceProcessor recurringInvoiceProcessor;

    @Mock
    private PrepaidInvoiceProcessor prepaidInvoiceProcessor;

    @Mock
    private OneTimeInvoiceProcessor oneTimeInvoiceProcessor;

    @Mock
    private CustomFieldProxy customFieldProxy;

    @Mock
    private DiscountService discountService;

    @Mock
    private MissingOrderChargesService missingOrderChargesService;

    @Mock
    private CurrencyConversionRateGetService currencyConversionRateGetService;

    @Mock
    private UserService userService;

    @Mock
    private FeatureService featureService;

    @Mock
    private DiscountCalculator discountCalculator;

    private OrderDomainImportProcessor orderImportProcessor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(s3ClientProvider.getS3Client()).thenReturn(s3Client);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TEST_TENANT);
        when(billyConfiguration.getDocumentConfiguration()).thenReturn(mockDocumentConfig());
        TenantSetting tenantSetting = new TenantSetting(
            UUID.randomUUID(),
            DEFAULT_TIMEZONE,
            "",
            List.of("USD"),
            PercentDerivedFrom.LIST_AMOUNT,
            TenantSettingSeal.ON,
            null,
            SigningOrder.ACCOUNT_FIRST,
            null,
            false,
            false,
            false,
            false,
            null
        );
        when(tenantSettingService.getTenantSetting()).thenReturn(tenantSetting);
        when(tenantSettingService.getTenantSettingInternal()).thenReturn(tenantSetting);
        OrderDomainCreateSchema orderCreateSchema = new OrderDomainCreateSchema(
            accountGetService,
            subscriptionGetService,
            entityGetService,
            orderGetService,
            productCatalogGetService,
            metricsService,
            tenantSettingService,
            customFieldProxy,
            discountService,
            entityContextResolver,
            rateCardService
        );

        var sellingPriceCalculator = new SellingPriceCalculator(
            subscriptionBillingPeriodService,
            tenantSettingService,
            recurringInvoiceProcessor,
            prepaidInvoiceProcessor,
            oneTimeInvoiceProcessor
        );

        when(prorationConfigurationGetService.resolveProrationConfig(any(Order.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.CALENDAR_DAYS, ProrationConfig.Mode.EXACT_DAYS)
        );

        InvoiceAmountCalculator invoiceAmountCalculator = new InvoiceAmountCalculator(
            rateCardService,
            currencyConversionRateGetService,
            discountCalculator,
            featureService
        );

        orderImportProcessor = new OrderDomainImportProcessor(
            s3ClientProvider,
            tenantIdProvider,
            orderCreateSchema,
            orderService,
            billyConfiguration,
            catalogService,
            catalogGetService,
            sellingPriceCalculator,
            prorationConfigurationGetService,
            entityContextResolver,
            changeOrderService,
            subscriptionGetService,
            invoiceAmountCalculator,
            metricsService,
            tenantSettingService,
            customFieldProxy,
            missingOrderChargesService,
            userService,
            featureService
        );
    }

    @Test
    public void calculateOrderLineItemPriceOverride() {
        var zonedStartDate = ZonedDateTime.of(2022, 11, 1, 0, 0, 0, 0, ZONE_ID);
        var startDate = zonedStartDate.toInstant();
        var endDate = zonedStartDate.plusYears(1).toInstant();

        when(subscriptionBillingPeriodService.getBillingPeriods(any(), any(), any(), any(), any(), any(), any())).thenReturn(
            List.of(new BillingPeriod(startDate, endDate, endDate, new Recurrence(Cycle.YEAR, 1)))
        );

        when(recurringInvoiceProcessor.previewInvoiceItems(any())).thenReturn(List.of(getInvoiceItem(new BigDecimal("120000"))));

        OrderLineItem item = new OrderLineItem();
        item.setQuantity(3000);
        item.setAmount(BigDecimal.valueOf(150000));
        item.setEffectiveDate(startDate);
        item.setEndDate(endDate);

        Charge charge = getRecurringBlockCharge();
        charge.setIsListPriceEditable(true);
        charge.setPlanId("PLAN-EFP5PRD");

        when(productCatalogGetService.getPlan(anyString())).thenReturn(mockPlan("PLAN-EFP5PRD", charge.getChargeId()));

        Order order = makeMockOrder();
        order.setBillingCycle(new Recurrence(Cycle.YEAR, 1));

        // price override = 150,000 / 120,000 = 1.25
        orderImportProcessor.calculateItemPriceAndDiscount(item, charge, order, item.getEffectiveDate(), item.getEndDate());

        assertTrue(Numbers.equals(item.getListPriceOverrideRatio(), new BigDecimal("1.25")));
    }

    @Test
    public void sellPriceGreaterThanListPriceWhenPriceOverrideNotAllowed() {
        var zonedStartDate = ZonedDateTime.of(2022, 11, 1, 0, 0, 0, 0, ZONE_ID);
        var startDate = zonedStartDate.toInstant();
        var endDate = zonedStartDate.plusYears(1).toInstant();

        when(subscriptionBillingPeriodService.getBillingPeriods(any(), any(), any(), any(), any(), any(), any())).thenReturn(
            List.of(new BillingPeriod(startDate, endDate, endDate, new Recurrence(Cycle.YEAR, 1)))
        );

        when(recurringInvoiceProcessor.previewInvoiceItems(any())).thenReturn(List.of(getInvoiceItem(new BigDecimal("120000"))));

        OrderLineItem item = new OrderLineItem();
        item.setQuantity(3000);
        item.setAmount(BigDecimal.valueOf(150000));
        item.setEffectiveDate(startDate);
        item.setEndDate(endDate);

        Charge charge = getRecurringBlockCharge();
        charge.setIsListPriceEditable(false);
        charge.setPlanId("PLAN-EFP5PRD");

        when(productCatalogGetService.getPlan(anyString())).thenReturn(mockPlan("PLAN-EFP5PRD", charge.getChargeId()));

        Order order = makeMockOrder();
        order.setBillingCycle(new Recurrence(Cycle.YEAR, 1));

        // price override = 150,000 / 120,000 = 1.25
        assertThrows(InvalidInputException.class, () ->
            orderImportProcessor.calculateItemPriceAndDiscount(item, charge, order, item.getEffectiveDate(), item.getEndDate())
        );
    }

    @Test
    public void calculateOrderLineItemDiscountFor1YearSubscription() {
        var zonedStartDate = ZonedDateTime.of(2022, 11, 1, 0, 0, 0, 0, ZONE_ID);
        var startDate = zonedStartDate.toInstant();
        var endDate = zonedStartDate.plusYears(1).toInstant();

        when(subscriptionBillingPeriodService.getBillingPeriods(any(), any(), any(), any(), any(), any(), any())).thenReturn(
            List.of(new BillingPeriod(startDate, endDate, endDate, new Recurrence(Cycle.YEAR, 1)))
        );

        when(recurringInvoiceProcessor.previewInvoiceItems(any())).thenReturn(List.of(getInvoiceItem(new BigDecimal("120000"))));

        OrderLineItem item = new OrderLineItem();
        item.setQuantity(3000);
        item.setEffectiveDate(startDate);
        item.setEndDate(endDate);

        Charge charge = getRecurringBlockCharge();
        charge.setPlanId("PLAN-EFP5PRD");

        when(productCatalogGetService.getPlan(anyString())).thenReturn(mockPlan("PLAN-EFP5PRD", charge.getChargeId()));

        Order order = makeMockOrder();
        order.setBillingCycle(new Recurrence(Cycle.YEAR, 1));

        // selling total of 15,000. List price for quantity = 3000: $10,000 (monthly block price) x 12 = 120,000. (120,000 - 15,000) / 120,000 = 0.875
        var discountAmount = orderImportProcessor.calculateItemDiscount(order, item, BigDecimal.valueOf(15000), charge, item.getEffectiveDate());

        assertTrue(Numbers.equals(discountAmount, new BigDecimal("0.875")));
    }

    @Test
    public void calculateOrderLineItemDiscountFor3YearSubscription() {
        var zonedStartDate = ZonedDateTime.of(2022, 11, 1, 0, 0, 0, 0, ZONE_ID);
        var startDate = zonedStartDate.toInstant();
        var endDate = zonedStartDate.plusYears(3).toInstant();

        when(subscriptionBillingPeriodService.getBillingPeriods(any(), any(), any(), any(), any(), any(), any())).thenReturn(
            List.of(
                new BillingPeriod(
                    startDate,
                    zonedStartDate.plusYears(1).toInstant(),
                    zonedStartDate.plusYears(1).toInstant(),
                    new Recurrence(Cycle.YEAR, 1)
                ),
                new BillingPeriod(
                    zonedStartDate.plusYears(1).toInstant(),
                    zonedStartDate.plusYears(2).toInstant(),
                    zonedStartDate.plusYears(2).toInstant(),
                    new Recurrence(Cycle.YEAR, 1)
                ),
                new BillingPeriod(zonedStartDate.plusYears(2).toInstant(), endDate, endDate, new Recurrence(Cycle.YEAR, 1))
            )
        );

        when(recurringInvoiceProcessor.previewInvoiceItems(any())).thenReturn(List.of(getInvoiceItem(new BigDecimal("360000"))));

        OrderLineItem item = new OrderLineItem();
        item.setQuantity(3000);
        item.setEffectiveDate(startDate);
        item.setEndDate(endDate);

        Charge charge = getRecurringBlockCharge();
        charge.setPlanId("PLAN-EFP5PRD");

        when(productCatalogGetService.getPlan(anyString())).thenReturn(mockPlan("PLAN-EFP5PRD", charge.getChargeId()));

        Order order = makeMockOrder();
        order.setBillingCycle(new Recurrence(Cycle.YEAR, 1));

        // selling total of 45,000. List price for quantity = 3000: $10,000 (monthly block price) x 12 x 3 = 360,000. (360,000 - 45,000) / 360,000 = 0.875
        var discountAmount = orderImportProcessor.calculateItemDiscount(order, item, BigDecimal.valueOf(45000), charge, item.getEffectiveDate());

        assertTrue(Numbers.equals(discountAmount, new BigDecimal("0.875")));
    }

    @Test
    public void whenCorrectCreateCsvIsPresent_thenOrderProcessingWorksCorrectly() throws Exception {
        when(s3Client.getObject(any(GetObjectRequest.class), any(ResponseTransformer.class))).thenReturn(getFileStream(IMPORT_CREATE_CSV_FILENAME));
        when(accountGetService.getAccountByExternalId("EXT-" + TEST_ACCOUNT_ID)).thenReturn(Optional.empty());
        when(accountGetService.getAccount(TEST_ACCOUNT_ID)).thenReturn(mockAccount());
        when(accountGetService.getAccount("ACCT-MISSING")).thenThrow(new ObjectNotFoundException(BillyObjectType.ACCOUNT, "ACCT-MISSING"));
        when(accountGetService.getContact(TEST_CONTACT_ID)).thenReturn(mockAccountContact());
        when(orderService.addOrder(any(Order.class), any(Boolean.class))).thenReturn(makeMockOrder());
        when(productCatalogGetService.getChargeByChargeId("CHRG-AMS861E")).thenReturn(mockCharge("PLAN-EFP5PRD", "CHRG-AMS861E"));
        when(productCatalogGetService.getChargeByChargeId("CHRG-D34DER7")).thenReturn(mockCharge("PLAN-EFP5PRD", "CHRG-D34DER7"));
        when(productCatalogGetService.getChargeByChargeId("CHRG-FF79LER")).thenReturn(mockCharge("PLAN-YZCVF0N", "CHRG-FF79LER"));

        when(productCatalogGetService.getPlan("PLAN-EFP5PRD")).thenReturn(mockPlan("PLAN-EFP5PRD", "CHRG-AMS861E"));
        when(productCatalogGetService.getPlan("PLAN-YZCVF0N")).thenReturn(mockPlan("PLAN-YZCVF0N", "CHRG-FF79LER"));

        Metrics metrics = new Metrics(
            BigDecimal.valueOf(1000000.00),
            BigDecimal.valueOf(1000000.00),
            BigDecimal.ZERO,
            BigDecimal.valueOf(1000000.00),
            BigDecimal.valueOf(1000000.00),
            BigDecimal.valueOf(1000000.00),
            BigDecimal.valueOf(1000000.00),
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            null
        );
        when(metricsService.getOrderMetrics(anyString(), any())).thenReturn(metrics);

        DataImport orderImport = makeMockOrderImport();

        ArgumentCaptor<PutObjectRequest> putCapture = ArgumentCaptor.forClass(PutObjectRequest.class);
        ArgumentCaptor<RequestBody> bodyCapture = ArgumentCaptor.forClass(RequestBody.class);
        orderImportProcessor.processImport(orderImport);

        verify(s3Client, times(1)).putObject(putCapture.capture(), bodyCapture.capture());
        assertEquals(BUCKET, putCapture.getValue().bucket());
        assertEquals(String.format("%s/%s-result", TEST_TENANT, IMPORT_ID), putCapture.getValue().key());

        RequestBody csvResponseBody = bodyCapture.getValue();
        String output = IOUtils.toString(csvResponseBody.contentStreamProvider().newStream(), StandardCharsets.UTF_8);
        assertEquals(getFileContents(), output.replaceAll("\\r", StringUtils.EMPTY));
    }

    private static Plan mockPlan(String planId, String chargeId) {
        Plan plan = new Plan();
        plan.setPlanId(planId);
        plan.setCharges(List.of(mockCharge(planId, chargeId)));
        plan.setCurrency(DEFAULT_CURRENCY);
        return plan;
    }

    private static Charge mockCharge(String planId, String chargeId) {
        Charge charge = new Charge();
        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.BLOCK);
        charge.setRecurrence(new Recurrence(Cycle.MONTH, 1));
        charge.setAmount(new BigDecimal("10.00"));
        charge.setChargeId(chargeId);
        charge.setPlanId(planId);
        return charge;
    }

    private static Charge getRecurringBlockCharge() {
        Charge charge = new Charge();
        charge.setChargeId(UUID.randomUUID().toString());
        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.BLOCK);
        charge.setRecurrence(new Recurrence(Cycle.MONTH, 1));
        charge.setPriceTiers(
            List.of(
                PriceTier.of(50L, new BigDecimal("1000")),
                PriceTier.of(500L, new BigDecimal("2500")),
                PriceTier.of(2500L, new BigDecimal("5000")),
                PriceTier.of(10000L, new BigDecimal("10000"))
            )
        );
        return charge;
    }

    private static Order makeMockOrder() {
        Order order = new Order();
        order.setOrderId("ORD-OUTPUT");
        order.setEntityId(EntityFixture.ENTITY_1_ID);
        order.setSfdcOpportunityId(TEST_OPPORTUNITY_ID);
        order.setSfdcOpportunityName(TEST_OPPORTUNITY_NAME);
        order.setName(TEST_ORDER_NAME);
        order.setOrderType(OrderType.NEW);
        order.setAccountId(TEST_ACCOUNT_ID);
        order.setBillingContactId(TEST_CONTACT_ID);
        order.setShippingContactId(TEST_CONTACT_ID);
        order.setStartDate(testDateToInstant("11/12/2021"));
        order.setEndDate(testDateToInstant("02/10/2022"));
        order.setTotalAmount(new BigDecimal("100.00"));
        order.setPurchaseOrderNumber(TEST_PURCHASE_ORDER_NUMBER);
        order.setCurrency(DEFAULT_CURRENCY);

        order.addLineItem(mockOrderLineItem("PLAN-EFP5PRD", "CHRG-AMS861E", 2, new BigDecimal("10.00")));
        order.addLineItem(mockOrderLineItem("PLAN-EFP5PRD", "CHRG-D34DER7", 3, new BigDecimal("20.00")));
        order.addLineItem(mockOrderLineItem("PLAN-YZCVF0N", "CHRG-FF79LER", 10, new BigDecimal("4.00")));
        return order;
    }

    private static OrderLineItem mockOrderLineItem(String planId, String chargeId, long quantity, BigDecimal unitPrice) {
        OrderLineItem lineItem = new OrderLineItem();
        lineItem.setPlanId(planId);
        lineItem.setChargeId(chargeId);
        lineItem.setAction(ActionType.ADD);
        lineItem.setQuantity(quantity);
        lineItem.setSellUnitPrice(unitPrice);
        lineItem.setQuantity(quantity);
        lineItem.setAmount(unitPrice.multiply(new BigDecimal(quantity)));
        return lineItem;
    }

    private static Account mockAccount() {
        Account account = new Account();
        account.setAccountId(TEST_ACCOUNT_ID);
        account.setName("Test Account");
        account.setDescription("Order Import Account");
        account.setPhoneNumber("************");
        account.setExternalId("EXT-" + TEST_ACCOUNT_ID);
        account.setCurrency(Currency.getInstance("USD"));
        return account;
    }

    private static AccountContact mockAccountContact() {
        AccountContact contact = new AccountContact();
        contact.setAccountId(TEST_ACCOUNT_ID);
        contact.setContactId(TEST_CONTACT_ID);
        return contact;
    }

    private static DataImport makeMockOrderImport() {
        DataImport dataImport = new DataImport();
        dataImport.setFilePointer(String.format("s3://%s/%s", BUCKET, OBJECT));
        dataImport.setImportId(IMPORT_ID);
        dataImport.setOperation(ImportOperation.CREATE);
        dataImport.setStatus(ImportStatus.PROCESSING);
        dataImport.setDomain(ImportDomain.ORDER);
        return dataImport;
    }

    private DocumentConfiguration mockDocumentConfig() {
        DocumentConfiguration documentConfiguration = new DocumentConfiguration();
        documentConfiguration.setImportS3Bucket(BUCKET);
        return documentConfiguration;
    }

    private static Instant testDateToInstant(String date) {
        SimpleDateFormat importDateformat = new SimpleDateFormat("MM/dd/yyyy");
        importDateformat.setLenient(false);
        importDateformat.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            return importDateformat.parse(date).toInstant();
        } catch (ParseException e) {
            throw new IllegalArgumentException(String.format("cannot parse date %s", date));
        }
    }

    private static InputStream getFileStream(String filename) throws IOException {
        URL resource = Thread.currentThread().getContextClassLoader().getResource(String.format(ORDER_IMPORT_DIRECTORY_PATH_FORMAT, filename));
        return Objects.requireNonNull(resource).openStream();
    }

    private static String getFileContents() throws IOException {
        InputStream input = getFileStream(EXPECTED_CREATE_CSV_FILENAME);
        return new String(input.readAllBytes(), StandardCharsets.UTF_8);
    }

    private InvoiceItem getInvoiceItem(BigDecimal amount) {
        return new InvoiceItem.InvoiceItemBuilder().amount(amount).createInvoiceItem();
    }
}
