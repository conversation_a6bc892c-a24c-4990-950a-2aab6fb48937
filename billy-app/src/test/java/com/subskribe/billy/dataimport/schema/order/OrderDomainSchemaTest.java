package com.subskribe.billy.dataimport.schema.order;

import com.subskribe.billy.dataimport.schema.BaseSchemaTest;
import org.junit.jupiter.api.Test;

public class OrderDomainSchemaTest extends BaseSchemaTest {

    @Test
    void allOrderDomainColumnsArePresentInSchemaJson() throws Exception {
        validateAllColumnsArePresentInSchema("flatfile/orderSchema.json", OrderDomainColumns.ORDER_HEADERS);
    }
}
