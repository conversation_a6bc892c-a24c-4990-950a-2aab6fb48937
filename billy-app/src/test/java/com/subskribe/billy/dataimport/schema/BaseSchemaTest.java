package com.subskribe.billy.dataimport.schema;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.io.InputStream;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class BaseSchemaTest {

    protected void validateAllColumnsArePresentInSchema(String schemaJsonFile, List<String> javaColumns) throws Exception {
        // Load the JSON schema
        InputStream is = getClass().getClassLoader().getResourceAsStream(schemaJsonFile);
        ObjectMapper mapper = JacksonProvider.defaultMapper();
        JsonNode root = mapper.readTree(is);

        // Collect all field keys from all sheets
        Set<String> jsonFieldKeys = new HashSet<>();
        for (JsonNode sheet : root.get("sheets")) {
            for (JsonNode field : sheet.get("fields")) {
                jsonFieldKeys.add(field.get("key").asText());
            }
        }

        // Check that every Java column is present in the JSON field keys
        assertThat(jsonFieldKeys).containsAll(javaColumns);
    }
}
