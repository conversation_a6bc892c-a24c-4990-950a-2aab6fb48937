package com.subskribe.billy.dataimport.processors;

import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.AccountService;
import com.subskribe.billy.aws.s3.S3ClientProvider;
import com.subskribe.billy.crm.CrmIntegrationService;
import com.subskribe.billy.crm.CrmType;
import com.subskribe.billy.customfield.CustomFieldProxy;
import com.subskribe.billy.customfield.model.CustomFieldParentType;
import com.subskribe.billy.dataimport.model.DataImport;
import com.subskribe.billy.dataimport.model.ImportOperation;
import com.subskribe.billy.dataimport.model.ImportStatus;
import com.subskribe.billy.dataimport.schema.account.AccountDomainCreateSchema;
import com.subskribe.billy.dataimport.schema.account.AccountDomainUpdateSchema;
import com.subskribe.billy.document.model.DocumentConfiguration;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.salesforce.model.account.SalesforceAccount;
import com.subskribe.billy.salesforce.service.SalesforceGetService;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Currency;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

public class AccountDomainImportProcessorTest {

    private static final String BUCKET = "swimming-rama";
    private static final String OBJECT = "spicy-sauce";

    private static final String ACCOUNT_IMPORT_DIRECTORY_PATH_FORMAT = "com/subskribe/billy/test/account_domain_import/%s";

    private static final String IMPORT_CREATE_CSV_FILENAME = "account_import_create.csv";
    private static final String EXPECTED_CREATE_CSV_FILENAME = "expected_account_create_output.csv";

    private static final String IMPORT_CREATE_WITH_CRM_ID_CSV_FILENAME = "account_create_import_with_crm_id.csv";
    private static final String EXPECTED_CREATE_WITH_CRM_ID_CSV_FILENAME = "expected_account_create_with_crm_id_output.csv";
    private static final String ACCOUNT_CRM_ID = "0011U00000TFV7MQAX";

    private static final String IMPORT_UPDATE_CSV_FILENAME = "account_import_update.csv";
    private static final String EXPECTED_UPDATE_CSV_FILENAME = "expected_account_update_output.csv";

    private static final String ACCOUNT_DESC = "Disco Never Died";
    private static final String TEST_TENANT = "rajini-kant";
    private static final String IMPORT_ID = "yipe-kaye-die-hard";
    private static final String CONTACT_PHONE = "**********";

    @Mock
    private S3ClientProvider s3ClientProvider;

    @Mock
    private S3Client s3Client;

    @Mock
    private AccountService accountService;

    @Mock
    private AccountGetService accountGetService;

    @Mock
    private SalesforceGetService salesforceGetService;

    @Mock
    private CrmIntegrationService crmIntegrationService;

    @Mock
    private HubSpotService hubSpotService;

    @Mock
    private CustomFieldProxy customFieldProxy;

    private final EntityGetService entityGetService = EntityGetServiceFixture.entityGetServiceFixture();

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private BillyConfiguration billyConfiguration;

    private AccountDomainImportProcessor processor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(s3ClientProvider.getS3Client()).thenReturn(s3Client);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TEST_TENANT);
        when(billyConfiguration.getDocumentConfiguration()).thenReturn(mockDocumentConfig());
        when(crmIntegrationService.getCrmIntegrationType()).thenReturn(Optional.of(CrmType.SALESFORCE));
        when(customFieldProxy.getCustomFields(any(CustomFieldParentType.class), nullable(String.class))).thenReturn(null);

        AccountDomainCreateSchema createSchema = new AccountDomainCreateSchema(customFieldProxy, entityGetService);
        AccountDomainUpdateSchema updateSchema = new AccountDomainUpdateSchema(customFieldProxy, entityGetService);

        processor = new AccountDomainImportProcessor(
            s3ClientProvider,
            accountService,
            accountGetService,
            tenantIdProvider,
            salesforceGetService,
            crmIntegrationService,
            hubSpotService,
            customFieldProxy,
            billyConfiguration,
            createSchema,
            updateSchema
        );
    }

    @Test
    public void whenCorrectCreateCsvIsPresent_thenProcessorWorksCorrectly() throws Exception {
        when(s3Client.getObject(any(GetObjectRequest.class), any(ResponseTransformer.class))).thenReturn(getFileStream(IMPORT_CREATE_CSV_FILENAME));
        when(accountService.addAccount(any(Account.class), any(AccountAddress.class))).thenReturn(mockAccount("123", "Disco"));
        when(accountService.addContact(any(AccountContact.class), anyBoolean(), anyBoolean())).thenReturn(mockContact("123", "SuperMan"));

        ArgumentCaptor<PutObjectRequest> putCapture = ArgumentCaptor.forClass(PutObjectRequest.class);
        ArgumentCaptor<RequestBody> bodyCapture = ArgumentCaptor.forClass(RequestBody.class);
        processor.processImport(makeMockImport(ImportOperation.CREATE));

        verify(s3Client, times(1)).putObject(putCapture.capture(), bodyCapture.capture());

        Assertions.assertThat(putCapture.getValue().bucket()).isEqualTo(BUCKET);
        Assertions.assertThat(putCapture.getValue().key()).isEqualTo(String.format("%s/%s-result", TEST_TENANT, IMPORT_ID));

        RequestBody csvResponseBody = bodyCapture.getValue();
        String output = IOUtils.toString(csvResponseBody.contentStreamProvider().newStream(), StandardCharsets.UTF_8);
        Assertions.assertThat(output.replaceAll("\\r", StringUtils.EMPTY)).isEqualTo(getFileContents(EXPECTED_CREATE_CSV_FILENAME));
    }

    @Test
    public void whenAccountCrmIdIsPresent_thenProcessorWorksCorrectly() throws Exception {
        when(salesforceGetService.getAccountById(ACCOUNT_CRM_ID, true))
            .thenThrow(new IllegalStateException("The tenant does not have a completed salesforce integration"))
            .thenThrow(new ObjectNotFoundException(BillyObjectType.SFDC_ACCOUNT, ACCOUNT_CRM_ID))
            .thenReturn(Optional.of(mock(SalesforceAccount.class)));

        when(s3Client.getObject(any(GetObjectRequest.class), any(ResponseTransformer.class))).thenReturn(
            getFileStream(IMPORT_CREATE_WITH_CRM_ID_CSV_FILENAME)
        );

        Account mockAccount = mockAccount("123", "Disco");
        mockAccount.setCrmId(ACCOUNT_CRM_ID);
        when(accountService.addAccount(any(Account.class), any(AccountAddress.class))).thenReturn(mockAccount);

        ArgumentCaptor<PutObjectRequest> putCapture = ArgumentCaptor.forClass(PutObjectRequest.class);
        ArgumentCaptor<RequestBody> bodyCapture = ArgumentCaptor.forClass(RequestBody.class);
        processor.processImport(makeMockImport(ImportOperation.CREATE));

        verify(s3Client, times(1)).putObject(putCapture.capture(), bodyCapture.capture());

        Assertions.assertThat(putCapture.getValue().bucket()).isEqualTo(BUCKET);
        Assertions.assertThat(putCapture.getValue().key()).isEqualTo(String.format("%s/%s-result", TEST_TENANT, IMPORT_ID));
        verify(salesforceGetService, times(3)).getAccountById(ACCOUNT_CRM_ID, true);

        RequestBody csvResponseBody = bodyCapture.getValue();
        String output = IOUtils.toString(csvResponseBody.contentStreamProvider().newStream(), StandardCharsets.UTF_8);
        Assertions.assertThat(output.replaceAll("\\r", StringUtils.EMPTY)).isEqualTo(getFileContents(EXPECTED_CREATE_WITH_CRM_ID_CSV_FILENAME));
    }

    @Test
    public void whenCorrectUpdateCsvIsPresent_thenProcessorWorksCorrectly() throws Exception {
        when(s3Client.getObject(any(GetObjectRequest.class), any(ResponseTransformer.class))).thenReturn(getFileStream(IMPORT_UPDATE_CSV_FILENAME));
        when(accountService.deleteAccount(any(String.class))).thenReturn(mockAccount("123", "Disco"));

        ArgumentCaptor<PutObjectRequest> putCapture = ArgumentCaptor.forClass(PutObjectRequest.class);
        ArgumentCaptor<RequestBody> bodyCapture = ArgumentCaptor.forClass(RequestBody.class);
        processor.processImport(makeMockImport(ImportOperation.UPDATE));

        verify(s3Client, times(1)).putObject(putCapture.capture(), bodyCapture.capture());
        verify(accountService).deleteAccount("ACCT-DELETE");
        verify(accountService).deleteContact("CONTACT-DELETE");

        Assertions.assertThat(putCapture.getValue().bucket()).isEqualTo(BUCKET);
        Assertions.assertThat(putCapture.getValue().key()).isEqualTo(String.format("%s/%s-result", TEST_TENANT, IMPORT_ID));

        RequestBody csvResponseBody = bodyCapture.getValue();
        String output = IOUtils.toString(csvResponseBody.contentStreamProvider().newStream(), StandardCharsets.UTF_8);
        Assertions.assertThat(output.replaceAll("\\r", StringUtils.EMPTY)).isEqualTo(getFileContents(EXPECTED_UPDATE_CSV_FILENAME));
    }

    private static DataImport makeMockImport(ImportOperation importOperation) {
        DataImport dataImport = new DataImport();
        dataImport.setFilePointer(String.format("s3://%s/%s", BUCKET, OBJECT));
        dataImport.setImportId(IMPORT_ID);
        dataImport.setOperation(importOperation);
        dataImport.setStatus(ImportStatus.PROCESSING);
        return dataImport;
    }

    private static Account mockAccount(String id, String name) {
        Account account = new Account();
        account.setAccountId("ACCT-" + id);
        account.setName(name);
        account.setDescription(ACCOUNT_DESC);
        account.setPhoneNumber("************");
        account.setExternalId("EXTR-" + id);
        account.setCurrency(Currency.getInstance("USD"));
        return account;
    }

    private static AccountContact mockContact(String id, String name) {
        AccountContact contact = new AccountContact();
        contact.setAccountId("ACCT-" + id);
        contact.setExternalId("EXTR-" + id);
        contact.setContactId("CONTACT-" + id);
        contact.setFirstName(name);
        contact.setLastName(name);
        contact.setPhoneNumber(CONTACT_PHONE);
        contact.setEmail("CONTACT-" + id + "@space.com");
        AccountAddress address = new AccountAddress();
        address.setStreetAddressLine1("Line1");
        address.setStreetAddressLine2("Line2");
        address.setCity("Anacortes");
        address.setState("WA");
        address.setZipcode("98221");
        address.setCountry("US");
        contact.setAddress(address);
        return contact;
    }

    private DocumentConfiguration mockDocumentConfig() {
        DocumentConfiguration documentConfiguration = new DocumentConfiguration();
        documentConfiguration.setImportS3Bucket(BUCKET);
        return documentConfiguration;
    }

    private static InputStream getFileStream(String filename) throws IOException {
        URL resource = Thread.currentThread().getContextClassLoader().getResource(String.format(ACCOUNT_IMPORT_DIRECTORY_PATH_FORMAT, filename));
        return Objects.requireNonNull(resource).openStream();
    }

    private static String getFileContents(String filename) throws IOException {
        InputStream input = getFileStream(filename);
        return new String(input.readAllBytes(), StandardCharsets.UTF_8);
    }
}
