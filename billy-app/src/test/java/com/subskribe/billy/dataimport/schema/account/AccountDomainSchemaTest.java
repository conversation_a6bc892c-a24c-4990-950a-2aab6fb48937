package com.subskribe.billy.dataimport.schema.account;

import com.subskribe.billy.dataimport.schema.BaseSchemaTest;
import org.junit.jupiter.api.Test;

public class AccountDomainSchemaTest extends BaseSchemaTest {

    @Test
    void allAccountDomainColumnsArePresentInSchemaJson() throws Exception {
        validateAllColumnsArePresentInSchema("flatfile/accountSchema.json", AccountDomainColumns.ACCOUNT_HEADERS);
    }
}
