package com.subskribe.billy.shared.render;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.order.document.temporal.TemplatePeriod;
import com.subskribe.billy.productcatalog.model.UnitOfMeasure;
import com.subskribe.billy.productcatalog.model.UnitOfMeasureStatus;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.shared.enums.Cycle;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.TimeZone;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class DocumentRenderFormatterTest {

    private static final TimeZone DEFAULT_TZ = TimeZone.getTimeZone("America/Los_Angeles");
    private static final String USD = "USD";

    public static final int JAN_1_2024_EPOCH_SECONDS = 1704096000;
    public static final int JAN_1_2025_EPOCH_SECONDS = 1735718400;
    public static final int JAN_1_2026_EPOCH_SECONDS = 1767254400;

    @Test
    void testDefaultCurrencyFormat() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        String withDecimals = documentFormatter.currencyFormat(new BigDecimal("10.525"), USD);
        String withOutDecimals = documentFormatter.currencyFormat(new BigDecimal("10"), USD);

        assertThat(withDecimals).endsWith("$10.53");
        assertThat(withOutDecimals).endsWith("$10.00");
    }

    @Test
    void testDynamicPrecisionCurrencyFormat() {
        BigDecimal price = new BigDecimal("10.12345");
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        String precisionNull = documentFormatter.currencyFormat(price, USD, null);
        String precision0 = documentFormatter.currencyFormat(price, USD, "0");
        String precision1 = documentFormatter.currencyFormat(price, USD, "1");
        String precision2 = documentFormatter.currencyFormat(price, USD, "2");
        String precision3 = documentFormatter.currencyFormat(price, USD, "3");
        String precision4 = documentFormatter.currencyFormat(price, USD, "4");
        String precision5 = documentFormatter.currencyFormat(price, USD, "5");
        String precision6 = documentFormatter.currencyFormat(price, USD, "6");

        assertEquals("$10.12", precisionNull); // default to 2
        assertEquals("$10.12", precision0); // default to 2
        assertEquals("$10.12", precision1); // default to 2
        assertEquals("$10.12", precision2);
        assertEquals("$10.123", precision3);
        assertEquals("$10.1235", precision4);
        assertEquals("$10.12345", precision5);
        assertEquals("$10.12", precision6); // default to 2
    }

    @Test
    void testCurrencyFormatWithSetFractionalDigits() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        String threeFractionalDigits = documentFormatter.currencyFormat(new BigDecimal("10.525"), "USD", 5);
        String fiveFractionalDigits = documentFormatter.currencyFormat(new BigDecimal("10.52525"), "USD", 5);
        String zeroFractionalDigits = documentFormatter.currencyFormat(new BigDecimal("10"), "USD", 5);
        String sixFractionalDigits = documentFormatter.currencyFormat(new BigDecimal("10.525255"), "USD", 5);

        assertThat(threeFractionalDigits).endsWith("$10.525");
        assertThat(fiveFractionalDigits).endsWith("$10.52525");
        assertThat(zeroFractionalDigits).endsWith("$10.00");
        assertThat(sixFractionalDigits).endsWith("$10.52526");
    }

    @Test
    public void testUOMNameDisplay() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        assertThat(documentFormatter.formatUnitOfMeasure(getUnitOfMeasureJson("GB"))).isEqualTo("GB");
        assertThat(documentFormatter.formatUnitOfMeasure(getUnitOfMeasureJson("Per Unit"))).isEqualTo("Per Unit");
        assertThat(documentFormatter.formatUnitOfMeasure(getUnitOfMeasureJson("Per Month Per Unit"))).isEqualTo("Per Month Per Unit");
    }

    @Test
    public void germanLocaleUSDCurrencyFormat() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("de-DE");

        String formattedString = documentFormatter.currencyFormat(new BigDecimal("10525"), "USD");
        assertThat(formattedString).isEqualTo("10.525,00 $");

        formattedString = documentFormatter.currencyFormat(new BigDecimal("10.25"), "USD");
        assertThat(formattedString).isEqualTo("10,25 $");

        formattedString = documentFormatter.currencyFormat(new BigDecimal("10.25"), null);
        assertThat(formattedString).isEqualTo("10,25 $");

        formattedString = documentFormatter.currencyFormat(new BigDecimal("10.25"), "EUR");
        assertThat(formattedString).isEqualTo("10,25 €");
    }

    @Test
    public void setInvalidLocaleString() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        Assertions.assertThrows(IllegalArgumentException.class, () -> documentFormatter.updateLocale(""));
        Assertions.assertThrows(IllegalArgumentException.class, () -> documentFormatter.updateLocale(null));
        Assertions.assertThrows(InvalidInputException.class, () -> documentFormatter.updateLocale("invalid"));
    }

    @Test
    public void defaultDateFormat() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        String formattedDate = documentFormatter.dateFormat(Instant.ofEpochSecond(JAN_1_2024_EPOCH_SECONDS)); //Jan 1, 2024
        assertThat(formattedDate).isEqualTo("Jan 1, 2024");
    }

    @Test
    public void testDefaultDateFormatInGermanLocale() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("de-DE");
        String formattedDate = documentFormatter.dateFormat(Instant.ofEpochSecond(JAN_1_2024_EPOCH_SECONDS));
        assertThat(formattedDate).isEqualTo("01.01.2024");
    }

    @Test
    public void customDateFormat() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.setDateFormatPattern("MMMM dd yyyy");
        String formattedDate = documentFormatter.dateFormat(Instant.ofEpochSecond(JAN_1_2024_EPOCH_SECONDS));
        assertThat(formattedDate).isEqualTo("January 01 2024");
    }

    @Test
    public void customDateFormatGermanLocale() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("de-DE");
        documentFormatter.setDateFormatPattern("MMMM dd yyyy");
        String formattedDate = documentFormatter.dateFormat(Instant.ofEpochSecond(JAN_1_2024_EPOCH_SECONDS));
        assertThat(formattedDate).isEqualTo("Januar 01 2024");
    }

    @Test
    public void testBillingCycle() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("en-US");

        String month = documentFormatter.formatBillingCycle(Cycle.MONTH);
        String quarter = documentFormatter.formatBillingCycle(Cycle.QUARTER);
        String semiAnnual = documentFormatter.formatBillingCycle(Cycle.SEMI_ANNUAL);
        String year = documentFormatter.formatBillingCycle(Cycle.YEAR);
        String paidInFull = documentFormatter.formatBillingCycle(Cycle.PAID_IN_FULL);

        assertThat(month).isEqualTo("Monthly");
        assertThat(quarter).isEqualTo("Quarterly");
        assertThat(semiAnnual).isEqualTo("Semi-Annual");
        assertThat(year).isEqualTo("Yearly");
        assertThat(paidInFull).isEqualTo("Paid In Full");
    }

    @Test
    public void testBillingCycleCanadaLocale() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("en-CA");

        String month = documentFormatter.formatBillingCycle(Cycle.MONTH);
        String quarter = documentFormatter.formatBillingCycle(Cycle.QUARTER);
        String semiAnnual = documentFormatter.formatBillingCycle(Cycle.SEMI_ANNUAL);
        String year = documentFormatter.formatBillingCycle(Cycle.YEAR);
        String paidInFull = documentFormatter.formatBillingCycle(Cycle.PAID_IN_FULL);

        assertThat(month).isEqualTo("Monthly");
        assertThat(quarter).isEqualTo("Quarterly");
        assertThat(semiAnnual).isEqualTo("Semi-Annual");
        assertThat(year).isEqualTo("Yearly");
        assertThat(paidInFull).isEqualTo("Paid In Full");
    }

    @Test
    public void testBillingCycleGermanLocale() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("de-DE");

        String monthInGerman = documentFormatter.formatBillingCycle(Cycle.MONTH);
        String quarterInGerman = documentFormatter.formatBillingCycle(Cycle.QUARTER);
        String semiAnnualInGerman = documentFormatter.formatBillingCycle(Cycle.SEMI_ANNUAL);
        String yearInGerman = documentFormatter.formatBillingCycle(Cycle.YEAR);

        // Unsupported translation
        String paidInFullInGerman = documentFormatter.formatBillingCycle(Cycle.PAID_IN_FULL);

        assertThat(monthInGerman).isEqualTo("Monatlich");
        assertThat(quarterInGerman).isEqualTo("Vierteljährlich");
        assertThat(semiAnnualInGerman).isEqualTo("Halbjährlich");
        assertThat(yearInGerman).isEqualTo("Jährlich");

        // Should not be translated
        assertThat(paidInFullInGerman).isEqualTo(Cycle.PAID_IN_FULL.getPeriodicityName());
    }

    @Test
    public void testBillingCycleRecurrence() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("en-US");

        String yearly = documentFormatter.formatBillingCycle(new RecurrenceJson(Cycle.YEAR, 1));
        String quarterly = documentFormatter.formatBillingCycle(new RecurrenceJson(Cycle.MONTH, 3));
        String sixMonths = documentFormatter.formatBillingCycle(new RecurrenceJson(Cycle.MONTH, 6));

        assertThat(yearly).isEqualTo("Yearly");
        assertThat(quarterly).isEqualTo("Quarterly");
        assertThat(sixMonths).isEqualTo("Every 6 Months");
    }

    @Test
    public void testBillingCycleRecurrenceCanadaLocale() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("en-CA");

        String yearly = documentFormatter.formatBillingCycle(new RecurrenceJson(Cycle.YEAR, 1));
        String quarterly = documentFormatter.formatBillingCycle(new RecurrenceJson(Cycle.MONTH, 3));
        String sixMonths = documentFormatter.formatBillingCycle(new RecurrenceJson(Cycle.MONTH, 6));

        assertThat(yearly).isEqualTo("Yearly");
        assertThat(quarterly).isEqualTo("Quarterly");
        assertThat(sixMonths).isEqualTo("Every 6 Months");
    }

    @Test
    public void testBillingCycleRecurrenceGermanLocale() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("de-DE");

        String yearly = documentFormatter.formatBillingCycle(new RecurrenceJson(Cycle.YEAR, 1));
        String quarterly = documentFormatter.formatBillingCycle(new RecurrenceJson(Cycle.MONTH, 3));
        String sixMonths = documentFormatter.formatBillingCycle(new RecurrenceJson(Cycle.MONTH, 6));

        // Unsupported translation
        String paidInFullInGerman = documentFormatter.formatBillingCycle(new RecurrenceJson(Cycle.PAID_IN_FULL, 1));

        assertThat(yearly).isEqualTo("Jährlich");
        assertThat(quarterly).isEqualTo("Vierteljährlich");
        assertThat(sixMonths).isEqualTo("Alle 6 Monate");

        // Should not be translated
        assertThat(paidInFullInGerman).isEqualTo("Paid In Full");
    }

    @Test
    public void testPaymentTerm() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("en-US");

        String net0 = documentFormatter.formatPaymentTerm(new PaymentTerm("NET0"));
        String net30 = documentFormatter.formatPaymentTerm(new PaymentTerm("NET30"));
        String net45 = documentFormatter.formatPaymentTerm(new PaymentTerm("NET45"));

        assertThat(net0).isEqualTo("Net 0");
        assertThat(net30).isEqualTo("Net 30");
        assertThat(net45).isEqualTo("Net 45");
    }

    @Test
    public void testPaymentTermCanadaLocale() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("en-CA");

        String net0 = documentFormatter.formatPaymentTerm(new PaymentTerm("NET0"));
        String net30 = documentFormatter.formatPaymentTerm(new PaymentTerm("NET30"));
        String net45 = documentFormatter.formatPaymentTerm(new PaymentTerm("NET45"));

        assertThat(net0).isEqualTo("Net 0");
        assertThat(net30).isEqualTo("Net 30");
        assertThat(net45).isEqualTo("Net 45");
    }

    @Test
    public void testPaymentTermGermanLocale() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("de-DE");

        String net0 = documentFormatter.formatPaymentTerm(new PaymentTerm("NET0"));
        String net30 = documentFormatter.formatPaymentTerm(new PaymentTerm("NET30"));
        String net45 = documentFormatter.formatPaymentTerm(new PaymentTerm("NET45"));

        assertThat(net0).isEqualTo("Netto 0");
        assertThat(net30).isEqualTo("Netto 30");
        assertThat(net45).isEqualTo("Netto 45");
    }

    @Test
    public void testTermLength() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("en-US");

        String oneYearByPeriod = documentFormatter.formatTermLength(null, JAN_1_2024_EPOCH_SECONDS, JAN_1_2025_EPOCH_SECONDS, DEFAULT_TZ);
        String twoYearsByPeriod = documentFormatter.formatTermLength(null, JAN_1_2024_EPOCH_SECONDS, JAN_1_2026_EPOCH_SECONDS, DEFAULT_TZ);
        String oneMonth = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.MONTH, 1),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twoMonths = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.MONTH, 2),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twelveMonths = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.MONTH, 12),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String oneYear = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.YEAR, 1),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twoYears = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.YEAR, 2),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twelveYears = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.YEAR, 12),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );

        assertThat(oneYearByPeriod).isEqualTo("1.00 Year");
        assertThat(twoYearsByPeriod).isEqualTo("2.00 Years");
        assertThat(oneMonth).isEqualTo("1 Month");
        assertThat(twoMonths).isEqualTo("2 Months");
        assertThat(twelveMonths).isEqualTo("12 Months");
        assertThat(oneYear).isEqualTo("1 Year");
        assertThat(twoYears).isEqualTo("2 Years");
        assertThat(twelveYears).isEqualTo("12 Years");
    }

    @Test
    public void testTermLengthCanadaLocale() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("en-CA");

        String oneYearByPeriod = documentFormatter.formatTermLength(null, JAN_1_2024_EPOCH_SECONDS, JAN_1_2025_EPOCH_SECONDS, DEFAULT_TZ);
        String twoYearsByPeriod = documentFormatter.formatTermLength(null, JAN_1_2024_EPOCH_SECONDS, JAN_1_2026_EPOCH_SECONDS, DEFAULT_TZ);
        String oneMonth = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.MONTH, 1),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twoMonths = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.MONTH, 2),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twelveMonths = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.MONTH, 12),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String oneYear = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.YEAR, 1),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twoYears = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.YEAR, 2),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twelveYears = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.YEAR, 12),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );

        assertThat(oneYearByPeriod).isEqualTo("1.00 Year");
        assertThat(twoYearsByPeriod).isEqualTo("2.00 Years");
        assertThat(oneMonth).isEqualTo("1 Month");
        assertThat(twoMonths).isEqualTo("2 Months");
        assertThat(twelveMonths).isEqualTo("12 Months");
        assertThat(oneYear).isEqualTo("1 Year");
        assertThat(twoYears).isEqualTo("2 Years");
        assertThat(twelveYears).isEqualTo("12 Years");
    }

    @Test
    public void testTermLengthGermanLocale() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        documentFormatter.updateLocale("de-DE");

        String oneYearByPeriod = documentFormatter.formatTermLength(null, JAN_1_2024_EPOCH_SECONDS, JAN_1_2025_EPOCH_SECONDS, DEFAULT_TZ);
        String twoYearsByPeriod = documentFormatter.formatTermLength(null, JAN_1_2024_EPOCH_SECONDS, JAN_1_2026_EPOCH_SECONDS, DEFAULT_TZ);
        String oneMonth = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.MONTH, 1),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twoMonths = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.MONTH, 2),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twelveMonths = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.MONTH, 12),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String oneYear = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.YEAR, 1),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twoYears = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.YEAR, 2),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );
        String twelveYears = documentFormatter.formatTermLength(
            new RecurrenceJson(Cycle.YEAR, 12),
            JAN_1_2024_EPOCH_SECONDS,
            JAN_1_2025_EPOCH_SECONDS,
            DEFAULT_TZ
        );

        assertThat(oneYearByPeriod).isEqualTo("1.00 Jahr");
        assertThat(twoYearsByPeriod).isEqualTo("2.00 Jahre");
        assertThat(oneMonth).isEqualTo("1 Monat");
        assertThat(twoMonths).isEqualTo("2 Monate");
        assertThat(twelveMonths).isEqualTo("12 Monate");
        assertThat(oneYear).isEqualTo("1 Jahr");
        assertThat(twoYears).isEqualTo("2 Jahre");
        assertThat(twelveYears).isEqualTo("12 Jahre");
    }

    @Test
    public void testCustomDateFormat() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);

        assertEquals("2024 01", documentFormatter.dateFormat("yyyy MM", Instant.ofEpochSecond(JAN_1_2024_EPOCH_SECONDS)));
        assertEquals("2024 Jan", documentFormatter.dateFormat("yyyy MMM", Instant.ofEpochSecond(JAN_1_2024_EPOCH_SECONDS)));
        assertEquals("Jan 1 2024", documentFormatter.dateFormat("MMM d yyyy", Instant.ofEpochSecond(JAN_1_2024_EPOCH_SECONDS)));
    }

    @Test
    public void testDateRange() {
        var documentFormatter = new DocumentRenderFormatter(DEFAULT_TZ);
        TemplatePeriod period = documentFormatter.dateRangeToPeriod(JAN_1_2024_EPOCH_SECONDS, JAN_1_2025_EPOCH_SECONDS);
        assertEquals(1, period.getYears());
        assertNull(period.getMonths());

        period = documentFormatter.dateRangeToPeriod(
            JAN_1_2024_EPOCH_SECONDS,
            ZonedDateTime.of(2024, 2, 10, 0, 0, 0, 0, DEFAULT_TZ.toZoneId()).toInstant().getEpochSecond()
        );
        assertNull(period.getYears());
        assertEquals(1, period.getMonths());
        assertEquals(9, period.getDays());
    }

    private UnitOfMeasure getUnitOfMeasureJson(String name) {
        return new UnitOfMeasure(UUID.randomUUID(), name, null, UnitOfMeasureStatus.ACTIVE);
    }
}
