package com.subskribe.billy.shared.enums;

import static com.subskribe.billy.shared.enums.Cycle.MONTH;
import static com.subskribe.billy.shared.enums.Cycle.PAID_IN_FULL;
import static com.subskribe.billy.shared.enums.Cycle.QUARTER;
import static com.subskribe.billy.shared.enums.Cycle.SEMI_ANNUAL;
import static com.subskribe.billy.shared.enums.Cycle.YEAR;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class CycleTest {

    @Test
    public void testCycleCompareTo() {
        assertTrue(MONTH.compareTo(QUARTER) < 0);
        assertTrue(MONTH.compareTo(SEMI_ANNUAL) < 0);
        assertTrue(MONTH.compareTo(YEAR) < 0);
        assertTrue(MONTH.compareTo(PAID_IN_FULL) < 0);

        assertTrue(QUARTER.compareTo(MONTH) > 0);
        assertTrue(QUARTER.compareTo(SEMI_ANNUAL) < 0);
        assertTrue(QUARTER.compareTo(YEAR) < 0);
        assertTrue(QUARTER.compareTo(PAID_IN_FULL) < 0);

        assertTrue(SEMI_ANNUAL.compareTo(MONTH) > 0);
        assertTrue(SEMI_ANNUAL.compareTo(QUARTER) > 0);
        assertTrue(SEMI_ANNUAL.compareTo(YEAR) < 0);
        assertTrue(SEMI_ANNUAL.compareTo(PAID_IN_FULL) < 0);

        assertTrue(YEAR.compareTo(MONTH) > 0);
        assertTrue(YEAR.compareTo(QUARTER) > 0);
        assertTrue(YEAR.compareTo(SEMI_ANNUAL) > 0);
        assertTrue(YEAR.compareTo(PAID_IN_FULL) < 0);

        assertTrue(PAID_IN_FULL.compareTo(MONTH) > 0);
        assertTrue(PAID_IN_FULL.compareTo(QUARTER) > 0);
        assertTrue(PAID_IN_FULL.compareTo(SEMI_ANNUAL) > 0);
        assertTrue(PAID_IN_FULL.compareTo(YEAR) > 0);
    }
}
