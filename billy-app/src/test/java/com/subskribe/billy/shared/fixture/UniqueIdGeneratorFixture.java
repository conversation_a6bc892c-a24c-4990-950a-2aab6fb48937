package com.subskribe.billy.shared.fixture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.shared.UniqueIdGenerator;
import com.subskribe.billy.tenant.fixtures.TenantIdProviderFixture;

public class UniqueIdGeneratorFixture {

    public static UniqueIdGenerator getDefault() {
        var realUniqueIdGenerator = new UniqueIdGenerator(mock(DSLContextProvider.class), TenantIdProviderFixture.getDefault());
        var uniqueIdGenerator = spy(realUniqueIdGenerator);
        doNothing().when(uniqueIdGenerator).validateUniqueId(any(), any());
        return uniqueIdGenerator;
    }
}
