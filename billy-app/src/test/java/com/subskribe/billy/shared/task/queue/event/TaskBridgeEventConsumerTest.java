package com.subskribe.billy.shared.task.queue.event;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.Mockito.verify;

import com.subskribe.billy.event.consumer.EventConsumerConfiguration;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.streams.Stream;
import com.subskribe.billy.exception.handling.ExceptionHandlingStrategy;
import com.subskribe.billy.exception.handling.TryForeverExceptionHandlingStrategy;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class TaskBridgeEventConsumerTest {

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private EventToTaskBridge eventToTaskBridge;

    @Mock
    private Event event;

    private TaskBridgeEventConsumer consumer;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        consumer = new TaskBridgeEventConsumer(tenantIdProvider, eventToTaskBridge);
    }

    @Test
    void tenantContextAwareOnEventShouldDelegateToEventToTaskBridge() {
        consumer.tenantContextAwareOnEvent(event);

        verify(eventToTaskBridge).handleEvent(event);
    }

    @Test
    void getConfigurationShouldReturnCorrectConfiguration() {
        EventConsumerConfiguration config = consumer.getConfiguration();

        assertEquals(TaskBridgeEventConsumer.TASK_BRIDGE_CONSUMER_NAME, config.getCanonicalConsumerName());

        assertThat(config.getHandlesEventsFromStream()).containsExactlyInAnyOrderElementsOf(Set.of(Stream.values()));

        ExceptionHandlingStrategy strategy = config.getExceptionHandlingStrategy();
        assertInstanceOf(TryForeverExceptionHandlingStrategy.class, strategy);
        assertEquals(TryForeverExceptionHandlingStrategy.DEFAULT_TRY_FOREVER_STRATEGY, strategy);
    }
}
