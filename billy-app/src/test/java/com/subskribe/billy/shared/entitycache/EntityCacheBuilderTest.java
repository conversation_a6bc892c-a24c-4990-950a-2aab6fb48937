package com.subskribe.billy.shared.entitycache;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class EntityCacheBuilderTest {

    @Mock
    private ProductCatalogGetService productCatalogGetService;

    private EntityCacheBuilder entityCacheBuilder;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        entityCacheBuilder = new EntityCacheBuilder(productCatalogGetService);
    }

    @Test
    public void buildEntityCacheWithoutSeedData() {
        final String chargeId = "CHRG-123";
        EntityCache<String, Charge> chargeEntityCache = entityCacheBuilder.getChargeEntityCache(null);
        chargeEntityCache.get(chargeId);
        verify(productCatalogGetService, times(1)).getChargeByChargeId(chargeId);
    }

    @Test
    public void buildEntityCacheWithInitialSeedData() {
        final String chargeId = "CHRG-123";
        Map<String, Charge> chargeMap = Map.of(chargeId, new Charge());
        when(productCatalogGetService.getChargeMapByChargeIds(List.of(chargeId), true)).thenReturn(chargeMap);

        EntityCache<String, Charge> chargeEntityCache = entityCacheBuilder.getChargeEntityCache(List.of(chargeId));
        chargeEntityCache.get(chargeId);
        verify(productCatalogGetService, times(0)).getChargeByChargeId(chargeId);
    }
}
