package com.subskribe.billy.shared.pecuniary;

import java.math.BigDecimal;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class DiscountTest {

    @Test
    void testEqualsSameValues() {
        String name = "name";
        var discountAmount = new BigDecimal("10.10");
        var discountPercent = new BigDecimal("0.1123231");

        Discount d1 = new Discount(name, discountPercent, discountAmount);
        Discount d2 = new Discount(name, discountPercent, discountAmount);

        Assertions.assertThat(d1.equals(d2)).isTrue();
    }

    @Test
    void testEqualsSameValueDifferentPrecision() {
        String name = "name";
        var discountAmount = new BigDecimal("10.10");

        Discount d1 = new Discount(name, new BigDecimal("0.11"), discountAmount);
        Discount d2 = new Discount(name, new BigDecimal("0.11000"), discountAmount);

        Assertions.assertThat(d1.equals(d2)).isTrue();
    }

    @Test
    void testEqualsDifferentPercent() {
        String name = "name";
        var discountAmount = new BigDecimal("10.10");

        Discount d1 = new Discount(name, new BigDecimal("0.12"), discountAmount);
        Discount d2 = new Discount(name, new BigDecimal("0.11"), discountAmount);

        Assertions.assertThat(d1.equals(d2)).isFalse();
    }

    @Test
    void testEqualsEmptyAmount() {
        String name = "name";

        Discount d1 = new Discount(name, new BigDecimal("0.11"), null);
        Discount d2 = new Discount(name, new BigDecimal("0.11000"), null);

        Assertions.assertThat(d1.equals(d2)).isTrue();
    }

    @Test
    void testEqualsOneNullPercent() {
        String name = "name";

        Discount d1 = new Discount(name, new BigDecimal("0.11"), null);
        Discount d2 = new Discount(name, null, null);

        Assertions.assertThat(d1.equals(d2)).isFalse();
    }
}
