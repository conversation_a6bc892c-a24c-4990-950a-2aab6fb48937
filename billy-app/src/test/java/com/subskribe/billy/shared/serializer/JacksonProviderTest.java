package com.subskribe.billy.shared.serializer;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.subskribe.billy.shared.modules.InstantModule;
import org.junit.jupiter.api.Test;

class JacksonProviderTest {

    @Test
    void testDefaultMapper() {
        ObjectMapper desiredMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .registerModule(new Jdk8Module())
            .registerModule(new InstantModule())
            .registerModule(new GuavaModule())
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ObjectMapper mapper = JacksonProvider.defaultMapper();

        assertMapperEquals(mapper, desiredMapper);
    }

    @Test
    void testEmptyFieldExcludingMapper() {
        ObjectMapper desiredMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .registerModule(new Jdk8Module())
            .registerModule(new InstantModule())
            .registerModule(new GuavaModule())
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultPropertyInclusion(JsonInclude.Include.NON_EMPTY);
        ObjectMapper mapper = JacksonProvider.emptyFieldExcludingMapper();

        assertMapperEquals(mapper, desiredMapper);
    }

    private void assertMapperEquals(ObjectMapper actual, ObjectMapper desired) {
        assertThat(actual.getDeserializationConfig().getDeserializationFeatures()).isEqualTo(
            desired.getDeserializationConfig().getDeserializationFeatures()
        );
        assertThat(actual.getSerializationConfig().getSerializationFeatures()).isEqualTo(desired.getSerializationConfig().getSerializationFeatures());
        assertThat(actual.getRegisteredModuleIds()).isEqualTo(desired.getRegisteredModuleIds());
    }
}
