package com.subskribe.billy.shared.task.queue.scheduler;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.subskribe.billy.shared.task.queue.db.QueuedTaskDAO;
import com.subskribe.billy.shared.task.queue.db.QueuedTaskSelectionDAO;
import com.subskribe.billy.shared.task.queue.distribution.TaskDistributor;
import com.subskribe.billy.shared.task.queue.metrics.TaskMetricPublisher;
import com.subskribe.billy.shared.task.queue.model.DefaultTaskOrder;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;

class TaskSchedulerTest {

    @Mock
    private QueuedTaskSelectionDAO queuedTaskSelectionDAO;

    @Mock
    private QueuedTaskDAO queuedTaskDAO;

    @Mock
    private TaskDistributor taskDistributor;

    @Mock
    private TaskMetricPublisher taskMetricPublisher;

    @Captor
    private ArgumentCaptor<List<QueuedTask>> tasksCaptor;

    private TaskScheduler taskScheduler;
    private AutoCloseable mocks;

    @BeforeEach
    void setUp() {
        mocks = openMocks(this);

        taskScheduler = new TaskScheduler(queuedTaskSelectionDAO, queuedTaskDAO, taskDistributor, taskMetricPublisher);
    }

    @Test
    void testSchedulerReturnsEarlyIfNoCapacity() {
        when(taskDistributor.getTotalCapacity()).thenReturn(0);

        taskScheduler.scheduleTasks();

        verify(queuedTaskDAO, never()).setTasksToInProgress(anyList());
    }

    @Test
    void testSchedulerPrioritizesTaskWithOrder() {
        when(taskDistributor.getTotalCapacity()).thenReturn(1);
        QueuedTask queuedTaskWithOrder = QueuedTaskStubs.buildTask();
        QueuedTask queuedTaskWithoutOrder = ImmutableQueuedTask.builder().from(QueuedTaskStubs.buildTask()).taskOrder(Optional.empty()).build();
        setupTasks(List.of(queuedTaskWithoutOrder, queuedTaskWithoutOrder, queuedTaskWithoutOrder, queuedTaskWithoutOrder, queuedTaskWithOrder));

        taskScheduler.scheduleTasks();

        List<QueuedTask> tasks = getDistributedTasks();

        assertThat(tasks).containsExactly(queuedTaskWithOrder);
    }

    @Test
    void testSchedulerDistributesTasksEvenlyAcrossTenants() {
        when(taskDistributor.getTotalCapacity()).thenReturn(3);
        QueuedTask tenant1Task1 = getTaskForTenant("tenant1");
        QueuedTask tenant1Task2 = copyTask(tenant1Task1);
        QueuedTask tenant1Task3 = copyTask(tenant1Task1);
        QueuedTask tenant2Task1 = getTaskForTenant("tenant2");
        QueuedTask tenant2Task2 = copyTask(tenant1Task2);
        QueuedTask tenant3Task1 = getTaskForTenant("tenant3");
        setupTasks(List.of(tenant1Task1, tenant1Task2, tenant1Task3, tenant2Task1, tenant2Task2, tenant3Task1));

        taskScheduler.scheduleTasks();

        List<QueuedTask> tasks = getDistributedTasks();
        assertThat(tasks).containsExactly(tenant1Task1, tenant2Task1, tenant3Task1);
    }

    @Test
    void testSchedulerPicksTenantsWithTheOldestTask() {
        when(taskDistributor.getTotalCapacity()).thenReturn(2);
        QueuedTask tenant1Task1 = ImmutableQueuedTask.builder().from(getTaskForTenant("tenant1")).taskOrder(new DefaultTaskOrder("t1", 50L)).build();
        QueuedTask tenant2Task1 = ImmutableQueuedTask.builder().from(getTaskForTenant("tenant2")).taskOrder(new DefaultTaskOrder("t2", 45L)).build();
        QueuedTask tenant3Task1 = ImmutableQueuedTask.builder().from(getTaskForTenant("tenant3")).taskOrder(new DefaultTaskOrder("t3", 40L)).build();
        setupTasks(List.of(tenant1Task1, tenant2Task1, tenant3Task1));

        taskScheduler.scheduleTasks();

        List<QueuedTask> tasks = getDistributedTasks();
        assertThat(tasks).containsExactly(tenant3Task1, tenant2Task1);
    }

    @Test
    void testOnlySelectedTasksAreSetToInProgress() {
        when(taskDistributor.getTotalCapacity()).thenReturn(1);
        QueuedTask tenant1Task1 = getTaskForTenant("tenant1");
        QueuedTask tenant1Task2 = copyTask(tenant1Task1);
        QueuedTask tenant1Task3 = copyTask(tenant1Task1);
        setupTasks(List.of(tenant1Task1, tenant1Task2, tenant1Task3));

        taskScheduler.scheduleTasks();

        List<QueuedTask> tasks = getDistributedTasks();
        assertThat(tasks).containsExactly(tenant1Task1);
        verify(queuedTaskDAO, times(1)).setTasksToInProgress(List.of(tenant1Task1.getTaskId()));
    }

    @Test
    void testTasksAreNotDistributedIfSettingInProgressFails() {
        when(taskDistributor.getTotalCapacity()).thenReturn(1);
        QueuedTask tenant1Task1 = getTaskForTenant("tenant1");
        setupTasks(List.of(tenant1Task1));
        doThrow(new RuntimeException("Failed to set tasks to in progress")).when(queuedTaskDAO).setTasksToInProgress(anyList());

        assertThatThrownBy(() -> taskScheduler.scheduleTasks()).isInstanceOf(RuntimeException.class);

        verify(taskDistributor, never()).distribute(anyList());
    }

    @AfterEach
    void tearDown() throws Exception {
        mocks.close();
    }

    private List<QueuedTask> getDistributedTasks() {
        verify(taskDistributor, times(1)).distribute(tasksCaptor.capture());
        return tasksCaptor.getValue();
    }

    private void setupTasks(List<QueuedTask> toReturn) {
        when(queuedTaskSelectionDAO.getTasksReadyToRunImmediately()).thenReturn(toReturn);
    }

    private QueuedTask getTaskForTenant(String tenant) {
        return ImmutableQueuedTask.builder().from(QueuedTaskStubs.buildTask()).tenantId(tenant).build();
    }

    private QueuedTask copyTask(QueuedTask task) {
        return ImmutableQueuedTask.builder().from(task).taskId(UUID.randomUUID().toString()).build();
    }
}
