package com.subskribe.billy.shared.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.Test;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

public class ConditionalMapperTest {

    @Test
    public void testConditionalMapper() {
        TestMapper mapper = Mappers.getMapper(TestMapper.class);

        var source = new MappingSourceObject("", "", "   ");
        var target = mapper.sourceToTarget(source);

        assertEquals("", target.getA());
        assertNull(target.getB());
        assertNull(target.getC());
    }

    @Mapper
    public interface TestMapper extends BaseMapper {
        @Mapping(target = "b", qualifiedBy = EmptyStringToNull.class)
        @Mapping(target = "c", qualifiedBy = BlankStringToNull.class)
        MappingTargetObject sourceToTarget(MappingSourceObject sourceObject);
    }

    public static class MappingSourceObject {

        final String a;
        final String b;
        final String c;

        public MappingSourceObject(String a, String b, String c) {
            this.a = a;
            this.b = b;
            this.c = c;
        }

        public String getA() {
            return a;
        }

        public String getB() {
            return b;
        }

        public String getC() {
            return c;
        }
    }

    public static class MappingTargetObject {

        final String a;
        final String b;
        final String c;

        public MappingTargetObject(String a, String b, String c) {
            this.a = a;
            this.b = b;
            this.c = c;
        }

        public String getA() {
            return a;
        }

        public String getB() {
            return b;
        }

        public String getC() {
            return c;
        }
    }
}
