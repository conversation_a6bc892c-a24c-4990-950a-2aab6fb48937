package com.subskribe.billy.shared.formatter;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.math.BigDecimal;
import org.junit.jupiter.api.Test;

public class NumberFormatterTest {

    @Test
    public void formatQuantities() {
        assertEquals("1.00", NumberFormatter.formatQuantityValue(new BigDecimal("1")));
        assertEquals("1.12", NumberFormatter.formatQuantityValue(new BigDecimal("1.123")));
        assertEquals("1.12", NumberFormatter.formatQuantityValue(new BigDecimal("1.125"))); // truncating, not rounding
        assertEquals("12,345.00", NumberFormatter.formatQuantityValue(new BigDecimal("12345.00")));
        assertEquals("123,456,789.00", NumberFormatter.formatQuantityValue(new BigDecimal("123456789.00")));
    }
}
