package com.subskribe.billy.shared.sanitizers;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class HtmlSanitizerTest {

    @Test
    public void classIsAllowed() {
        String inputHtml = "<div class=\"alpha beta\"> Test </div>";
        String actualHtml = HtmlSanitizer.sanitize(inputHtml);
        Assertions.assertThat(actualHtml).isEqualTo(inputHtml);
    }

    @Test
    public void javascriptTagNotAllowed() {
        String inputHtml = "<script>var x = 5;</script>";
        String expectedHtml = "";
        String actualHtml = HtmlSanitizer.sanitize(inputHtml);
        Assertions.assertThat(actualHtml).isEqualTo(expectedHtml);
    }

    @Test
    public void styleTagIsAllowed() {
        String inputHtml = "<style> .div { color: blue; } </style>";
        String actualHtml = HtmlSanitizer.sanitize(inputHtml);
        Assertions.assertThat(actualHtml).isEqualTo(inputHtml);
    }

    @Test
    public void inlineStyleIsAllowed() {
        String inputHtml = "<div style=\"color: blue; background-color: red; \"></div>";
        // strips spacing and ending semicolon
        String expectedHtml = "<div style=\"color:blue;background-color:red\"></div>";
        String actualHtml = HtmlSanitizer.sanitize(inputHtml);
        Assertions.assertThat(actualHtml).isEqualTo(expectedHtml);
    }
}
