package com.subskribe.billy.shared.temporal;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Optional;
import java.util.TimeZone;
import org.junit.jupiter.api.Test;

public class DateTimeParserTest {

    private final TimeZone utcTimeZone = TimeZone.getTimeZone("UTC");
    private final TimeZone estTimeZone = TimeZone.getTimeZone("America/New_York");

    @Test
    public void testParseDateFromString_PositiveCases() {
        // Test MM/DD/YYYY format
        String dateString1 = "1/15/2024";
        Optional<Instant> result1 = DateTimeParser.parseDateFromString(dateString1, utcTimeZone);
        assertThat(result1).as("Valid date in MM/DD/YYYY format should be parsed successfully").isPresent();
        assertThat(result1.get())
            .as("Parsed date should match expected date")
            .isEqualTo(LocalDate.of(2024, 1, 15).atStartOfDay(ZoneId.of("UTC")).toInstant());

        // Test MM/DD/YY format
        String dateString2 = "5/20/24";
        Optional<Instant> result2 = DateTimeParser.parseDateFromString(dateString2, utcTimeZone);
        assertThat(result2).as("Valid date in MM/DD/YY format should be parsed successfully").isPresent();
        assertThat(result2.get())
            .as("Parsed date should match expected date")
            .isEqualTo(LocalDate.of(2024, 5, 20).atStartOfDay(ZoneId.of("UTC")).toInstant());

        // Test with non-UTC timezone
        String dateString3 = "12/31/2023";
        Optional<Instant> result3 = DateTimeParser.parseDateFromString(dateString3, estTimeZone);
        assertThat(result3).as("Valid date with EST timezone should be parsed successfully").isPresent();
        assertThat(result3.get())
            .as("Parsed date should match expected date with correct timezone")
            .isEqualTo(LocalDate.of(2023, 12, 31).atStartOfDay(ZoneId.of("America/New_York")).toInstant());

        // Test single-digit day and month
        String dateString4 = "2/3/2024";
        Optional<Instant> result4 = DateTimeParser.parseDateFromString(dateString4, utcTimeZone);
        assertThat(result4).as("Valid date with single-digit day and month should be parsed successfully").isPresent();
        assertThat(result4.get())
            .as("Parsed date should match expected date")
            .isEqualTo(LocalDate.of(2024, 2, 3).atStartOfDay(ZoneId.of("UTC")).toInstant());

        // Java does smart parse here so that we can get it right
        Optional<Instant> result5 = DateTimeParser.parseDateFromString("2/31/2024", utcTimeZone);
        assertThat(result5).as("Valid date with single-digit day and month should be parsed successfully").isPresent();
        assertThat(result5.get())
            .as("Parsed date should match expected date")
            .isEqualTo(LocalDate.of(2024, 2, 29).atStartOfDay(ZoneId.of("UTC")).toInstant());
    }

    @Test
    public void testParseDateFromString_NegativeCases() {
        // Test null input
        Optional<Instant> result1 = DateTimeParser.parseDateFromString(null, utcTimeZone);
        assertThat(result1).as("Null input should return empty Optional").isEmpty();

        // Test empty string
        Optional<Instant> result2 = DateTimeParser.parseDateFromString("", utcTimeZone);
        assertThat(result2).as("Empty string should return empty Optional").isEmpty();

        // Test blank string
        Optional<Instant> result3 = DateTimeParser.parseDateFromString("   ", utcTimeZone);
        assertThat(result3).as("Blank string should return empty Optional").isEmpty();

        // Test invalid date format
        Optional<Instant> result4 = DateTimeParser.parseDateFromString("2024-01-15", utcTimeZone);
        assertThat(result4).as("Invalid date format (YYYY-MM-DD) should return empty Optional").isEmpty();

        // Test invalid date values
        Optional<Instant> result5 = DateTimeParser.parseDateFromString("13/45/2024", utcTimeZone);
        assertThat(result5).as("Invalid date (month 13, day 45) should return empty Optional").isEmpty();

        // Test non-date string
        Optional<Instant> result7 = DateTimeParser.parseDateFromString("not-a-date", utcTimeZone);
        assertThat(result7).as("Non-date string should return empty Optional").isEmpty();
    }
}
