package com.subskribe.billy.shared.csv;

import static java.util.Map.entry;

import java.util.Map;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class SecureCSVPrinterTest {

    private static final Map<String, String> EXPECTED_MAP = Map.ofEntries(
        entry("=HYPERLINK(\"www.google.com\", \"evil\")", "\t=HYPERLINK(\"www.google.com\", \"evil\")"),
        entry("@foo.com", "\<EMAIL>"),
        entry("+200", "\t+200"),
        entry("-200", "-200"),
        entry("-20.00", "-20.00"),
        entry("-1", "-1"),
        entry("-2+3", "\t-2+3"),
        entry("-2.0-3", "\t-2.0-3"),
        entry("nothing", "nothing"),
        entry("", "")
    );

    @Test
    public void specialCharTreatmentWorksAsExpected() {
        EXPECTED_MAP.forEach((value, expected) -> {
            Object normalized = SecureCSVPrinter.treatObjectIfRequired(value);
            Assertions.assertThat(normalized).isInstanceOf(String.class);
            Assertions.assertThat(normalized).isEqualTo(expected);
        });
    }
}
