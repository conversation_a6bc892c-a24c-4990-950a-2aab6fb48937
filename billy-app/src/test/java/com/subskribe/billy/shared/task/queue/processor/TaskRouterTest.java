package com.subskribe.billy.shared.task.queue.processor;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.shared.task.queue.model.ImmutableProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTask;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskResult;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import org.glassfish.hk2.api.IterableProvider;
import org.junit.jupiter.api.Test;

class TaskRouterTest {

    @Test
    void taskRouterFailsOnStartupIfConflictingProcessorsExist() {
        IterableProvider<TaskProcessor> taskProcessors = buildIterable(
            List.of(buildTaskProcessorMock("testModule"), buildTaskProcessorMock("testModule"))
        );
        assertThatThrownBy(() -> new TaskRouter(taskProcessors))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageStartingWith("Multiple task processors for module");
    }

    @Test
    void taskRouterRoutesToTheCorrectProcessorAndReturnsResponse() {
        TaskProcessor taskProcessor = buildTaskProcessorMock("moduleOne");
        IterableProvider<TaskProcessor> taskProcessors = buildIterable(List.of(taskProcessor, buildTaskProcessorMock("moduleTwo")));
        QueuedTask task = ImmutableQueuedTask.builder().from(QueuedTaskStubs.buildTask()).module(new TaskModule("moduleOne")).build();
        TaskResult expected = ImmutableTaskResult.builder().successful(false).retryable(false).backoffUntil(Instant.now().plusSeconds(30)).build();
        when(taskProcessor.process(task)).thenReturn(expected);
        TaskRouter taskRouter = new TaskRouter(taskProcessors);

        assertThat(taskRouter.routeTask(task).taskResultSupplier().get()).isEqualTo(expected);
    }

    @Test
    void taskRouterReturnsNotRetryableFailureIfProcessorNotFound() {
        IterableProvider<TaskProcessor> taskProcessors = buildIterable(
            List.of(buildTaskProcessorMock("moduleOne"), buildTaskProcessorMock("moduleTwo"))
        );
        QueuedTask task = ImmutableQueuedTask.builder().from(QueuedTaskStubs.buildTask()).module(new TaskModule("moduleFive")).build();

        TaskResult expected = ImmutableTaskResult.builder().successful(false).retryable(false).build();

        TaskRouter taskRouter = new TaskRouter(taskProcessors);

        assertThat(taskRouter.routeTask(task).taskResultSupplier().get()).isEqualTo(expected);
    }

    private IterableProvider<TaskProcessor> buildIterable(List<TaskProcessor> taskProcessors) {
        IterableProvider<TaskProcessor> provider = mock(IterableProvider.class);
        when(provider.iterator()).thenReturn(taskProcessors.iterator());
        when(provider.iterator()).thenReturn(taskProcessors.iterator());
        return provider;
    }

    private TaskProcessor buildTaskProcessorMock(String module) {
        TaskProcessor taskProcessor = mock(TaskProcessor.class);
        when(taskProcessor.getConfiguration()).thenReturn(
            ImmutableProcessorConfiguration.builder().taskModule(new TaskModule(module)).taskTimeout(Duration.ofSeconds(60)).build()
        );
        return taskProcessor;
    }
}
