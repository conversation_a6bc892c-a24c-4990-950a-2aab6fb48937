package com.subskribe.billy.shared.temporal;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeParseException;
import java.util.TimeZone;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

/*
                                                         c=====e
                                                            H
   ____________                                         _,,_H__
  (__((__((___()                                       //|     |
 (__((__((___()()_____________________________________// |     |
(__((__((___()()()------------------------------------'  |_____|
    modifying these set of tests to pass can have serious repercussions
    please clarify before modifying to succeed in case of failure
*/
public class DateTimeConverterTest {

    @Test
    public void instantToLocalDateTimeConversionIsInUTC() {
        Instant expected = Instant.now();
        LocalDateTime localDateTime = DateTimeConverter.instantToLocalDateTime(expected);
        Instant actual = localDateTime.toInstant(ZoneOffset.UTC);
        Assertions.assertThat(actual).isEqualTo(expected);
    }

    @Test
    public void localDateTimeToInstantConversionIsUTCOffset() {
        Instant expected = Instant.now();
        ZonedDateTime zonedInUtc = ZonedDateTime.ofInstant(expected, ZoneId.of("UTC"));
        Instant actual = DateTimeConverter.localDateTimeToInstant(zonedInUtc.toLocalDateTime());
        Assertions.assertThat(actual).isEqualTo(expected);
    }

    @Test
    public void convertSameDateTimeFromPacificToEastern() {
        Instant pacificInstant = Instant.ofEpochSecond(1704096000); // 2024-01-01 00:00:00 GMT-08:00
        Instant easternInstant = Instant.ofEpochSecond(1704085200); // 2024-01-01 00:00:00 GMT-05:00

        Instant convertedInstant = DateTimeConverter.convertSameDateTime(
            pacificInstant,
            ZoneId.of("America/Los_Angeles"),
            ZoneId.of("America/New_York")
        );

        Assertions.assertThat(convertedInstant).isEqualTo(easternInstant);
    }

    @Test
    public void convertSameDateTimeFromPacificToUtc() {
        Instant pacificInstant = Instant.ofEpochSecond(1704096000); // 2024-01-01 00:00:00 GMT-08:00
        Instant easternInstant = Instant.ofEpochSecond(1704067200); // 2024-01-01 00:00:00 UTC

        Instant convertedInstant = DateTimeConverter.convertSameDateTime(pacificInstant, ZoneId.of("America/Los_Angeles"), ZoneId.of("UTC"));

        Assertions.assertThat(convertedInstant).isEqualTo(easternInstant);
    }

    @Test
    public void parseDateWithValidInput() {
        String dateTime = "2023-10-01";
        String format = "yyyy-MM-dd";
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Instant expected = Instant.parse("2023-10-01T00:00:00Z");
        Instant actual = DateTimeConverter.parseDate(dateTime, format, timeZone);
        Assertions.assertThat(actual).isEqualTo(expected);
    }

    @Test
    public void parseDateWithPSTTimeZone() {
        String dateTime = "2023-10-01";
        String format = "yyyy-MM-dd";
        TimeZone timeZone = TimeZone.getTimeZone("America/Los_Angeles");
        Instant expected = Instant.parse("2023-10-01T07:00:00Z");
        Instant actual = DateTimeConverter.parseDate(dateTime, format, timeZone);
        Assertions.assertThat(actual).isEqualTo(expected);
    }

    @Test
    public void parseDateWithInvalidFormat() {
        String dateTime = "2023-10-01";
        String format = "dd-MM-yyyy";
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Assertions.assertThatThrownBy(() -> DateTimeConverter.parseDate(dateTime, format, timeZone)).isInstanceOf(DateTimeParseException.class);
    }

    @Test
    void nanosToSecondsRoundedUp_ExactSecondIsReturned() {
        long nanos = 1_000_000_000L;
        long expected = 1L;
        long actual = DateTimeConverter.nanosToSecondsRoundedUp(nanos);
        Assertions.assertThat(actual).isEqualTo(expected);
    }

    @Test
    void nanosToSecondsRoundedUp_SingleNanoRoundedUp() {
        long nanos = 1_000_000_001L;
        long expected = 2L;
        long actual = DateTimeConverter.nanosToSecondsRoundedUp(nanos);
        Assertions.assertThat(actual).isEqualTo(expected);
    }

    @Test
    void nanosToSecondsRoundedUp_QuarterOfSecondRoundedUp() {
        long nanos = 1_250_000_000L;
        long expected = 2L;
        long actual = DateTimeConverter.nanosToSecondsRoundedUp(nanos);
        Assertions.assertThat(actual).isEqualTo(expected);
    }

    @Test
    void nanosToSecondsRoundedUp_ThreeQuartersOfSecondRoundedUp() {
        long nanos = 1_750_000_000L;
        long expected = 2L;
        long actual = DateTimeConverter.nanosToSecondsRoundedUp(nanos);
        Assertions.assertThat(actual).isEqualTo(expected);
    }
}
