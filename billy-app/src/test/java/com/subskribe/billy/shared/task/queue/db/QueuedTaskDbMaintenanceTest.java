package com.subskribe.billy.shared.task.queue.db;

import static com.subskribe.billy.jooq.default_schema.Tables.QUEUED_TASK;
import static com.subskribe.billy.jooq.default_schema.Tables.QUEUED_TASK_ARCHIVE;
import static com.subskribe.billy.jooq.default_schema.Tables.QUEUED_TASK_EXECUTION;
import static com.subskribe.billy.jooq.default_schema.Tables.QUEUED_TASK_EXECUTION_ARCHIVE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.jooq.default_schema.tables.records.QueuedTaskArchiveRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.QueuedTaskExecutionArchiveRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.QueuedTaskExecutionRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.QueuedTaskRecord;
import com.subskribe.billy.shared.task.queue.config.TaskQueueConfiguration;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.test.WithDb;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@AllowNonRlsDataAccess
class QueuedTaskDbMaintenanceTest extends WithDb {

    private QueuedTaskDbMaintenance queuedTaskDbMaintenance;
    private Clock clock;

    private static final String TENANT_ID = UUID.randomUUID().toString();

    @BeforeEach
    void setUp() {
        clock = Clock.fixed(Instant.now(), ZoneId.of("UTC"));
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        TaskQueueConfiguration taskQueueConfiguration = new TaskQueueConfiguration();
        when(billyConfiguration.getTaskQueueConfiguration()).thenReturn(taskQueueConfiguration);
        taskQueueConfiguration.setArchiveTaskAfterDays(14);
        queuedTaskDbMaintenance = new QueuedTaskDbMaintenance(dslContextProvider, clock, billyConfiguration);
    }

    @Test
    void testOnlyOldRecordsAreArchived() {
        DSLContext dslContext = dslContextProvider.get();
        QueuedTaskRecord twoDaysBeforeCutoff = addTaskStubsForDate(clock.instant().minus(Duration.ofDays(16)));
        QueuedTaskRecord oneDayBeforeCutoff = addTaskStubsForDate(clock.instant().minus(Duration.ofDays(15)));
        QueuedTaskRecord oneDayAfterCutoff = addTaskStubsForDate(clock.instant().minus(Duration.ofDays(13)));
        assertThat(dslContext.fetchCount(QUEUED_TASK)).isEqualTo(3);
        assertThat(dslContext.fetchCount(QUEUED_TASK_EXECUTION)).isEqualTo(15);

        queuedTaskDbMaintenance.performMaintenances();

        assertThat(dslContext.fetchCount(QUEUED_TASK)).isEqualTo(1);
        assertThat(dslContext.fetchCount(QUEUED_TASK_EXECUTION)).isEqualTo(5);

        assertThat(dslContext.fetchCount(QUEUED_TASK_ARCHIVE)).isEqualTo(2);
        assertThat(dslContext.fetchCount(QUEUED_TASK_EXECUTION_ARCHIVE)).isEqualTo(10);

        assertThat(Set.of(dslContext.selectFrom(QUEUED_TASK).fetch().map(QueuedTaskRecord::getTaskId))).containsOnly(
            List.of(oneDayAfterCutoff.getTaskId())
        );
        assertThat(new HashSet<>(dslContext.selectFrom(QUEUED_TASK_EXECUTION).fetch().map(QueuedTaskExecutionRecord::getTaskId))).containsOnly(
            oneDayAfterCutoff.getTaskId()
        );

        assertThat(new HashSet<>(dslContext.selectFrom(QUEUED_TASK_ARCHIVE).fetch().map(QueuedTaskArchiveRecord::getTaskId))).containsOnly(
            twoDaysBeforeCutoff.getTaskId(),
            oneDayBeforeCutoff.getTaskId()
        );
        assertThat(
            new HashSet<>(dslContext.selectFrom(QUEUED_TASK_EXECUTION_ARCHIVE).fetch().map(QueuedTaskExecutionArchiveRecord::getTaskId))
        ).containsOnly(twoDaysBeforeCutoff.getTaskId(), oneDayBeforeCutoff.getTaskId());
    }

    private QueuedTaskRecord addTaskStubsForDate(Instant date) {
        LocalDateTime localDateTime = DateTimeConverter.instantToLocalDateTime(date);
        QueuedTaskRecord queuedTaskRecord = new QueuedTaskRecord(
            UUID.randomUUID(),
            UUID.randomUUID().toString(),
            "module",
            "type",
            "data",
            JSONB.jsonb("{}"),
            TENANT_ID,
            "ENT-12345",
            new String[] { "ENT-12345" },
            TENANT_ID,
            System.currentTimeMillis(),
            null,
            null,
            "SUCCESS",
            null,
            null,
            localDateTime,
            localDateTime,
            null,
            null
        );
        dslContextProvider.get().insertInto(QUEUED_TASK).set(queuedTaskRecord).execute();
        List<QueuedTaskExecutionRecord> toInsert = IntStream.range(0, 5)
            .mapToObj(i ->
                new QueuedTaskExecutionRecord(
                    UUID.randomUUID(),
                    TENANT_ID,
                    UUID.randomUUID().toString(),
                    queuedTaskRecord.getTaskId(),
                    Short.parseShort("30"),
                    "COMPLETED",
                    "test",
                    null,
                    null,
                    null
                )
            )
            .collect(Collectors.toList());
        dslContextProvider.get().batchInsert(toInsert).execute();
        return queuedTaskRecord;
    }
}
