package com.subskribe.billy.shared.task.queue.retry;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.Duration;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

class ExponentialBackoffTest {

    @ParameterizedTest
    @CsvSource({ "-10,1", "-1,1", "0,1", "1,1", "2,2", "3,4", "4,8", "5,16" })
    void testAttempts(int attempts, long expectedDelay) {
        ExponentialBackoff backoff = new ExponentialBackoff(Duration.ofSeconds(1), Duration.ofSeconds(60));
        Duration delay = backoff.backoff(attempts);
        assertThat(delay).isEqualTo(Duration.ofSeconds(expectedDelay));
    }

    @ParameterizedTest
    @CsvSource({ "1,1,4", "2,4,4", "3,16,4", "4,64,4", "5,256,4", "2,4,4", "5,16,0", "5,16,-1", "5,16,1" })
    void testAttemptsWithCustomFactor(int attempts, long expectedDelay, int delayFactor) {
        ExponentialBackoff backoff = new ExponentialBackoff(Duration.ofSeconds(1), Duration.ofSeconds(999), delayFactor);
        Duration delay = backoff.backoff(attempts);
        assertThat(delay).isEqualTo(Duration.ofSeconds(expectedDelay));
    }

    @ParameterizedTest
    @CsvSource({ "1,1", "2,2", "3,4", "4,8", "5,10" })
    void testMaxDelay(int attempts, long expectedDelay) {
        ExponentialBackoff backoff = new ExponentialBackoff(Duration.ofSeconds(1), Duration.ofSeconds(10));
        Duration delay = backoff.backoff(attempts);
        assertThat(delay).isEqualTo(Duration.ofSeconds(expectedDelay));
    }
}
