package com.subskribe.billy.shared.utility;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class BillyStringUtilsTest {

    @Test
    public void testOptionalIfNotBlank() {
        assertTrue(BillyStringUtils.optionalIfNotBlank("test").isPresent());
        assertFalse(BillyStringUtils.optionalIfNotBlank("").isPresent());
        assertFalse(BillyStringUtils.optionalIfNotBlank("  ").isPresent());
        assertFalse(BillyStringUtils.optionalIfNotBlank(null).isPresent());
    }
}
