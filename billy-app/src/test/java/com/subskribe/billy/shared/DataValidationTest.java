package com.subskribe.billy.shared;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class DataValidationTest {

    @Test
    public void emailIsValid() {
        // valid emails
        assertTrue(DataValidation.isEmailValid("<EMAIL>"));
        assertTrue(DataValidation.isEmailValid("<EMAIL>"));
        assertTrue(DataValidation.isEmailValid("<EMAIL>"));
        assertTrue(DataValidation.isEmailValid("<EMAIL>"));
        assertTrue(DataValidation.isEmailValid("<EMAIL>"));
        assertTrue(DataValidation.isEmailValid("weird01!#$%&'*+-/=?^_`{|}~.<EMAIL>"));
        assertTrue(DataValidation.isEmailValid("<EMAIL>"));
        // invalid emails
        assertFalse(DataValidation.isEmailValid("<EMAIL>"));
        assertFalse(DataValidation.isEmailValid("@nouser.com"));
        assertFalse(DataValidation.isEmailValid("nodomain@"));
        assertFalse(DataValidation.isEmailValid("user@hostname"));
        assertFalse(DataValidation.isEmailValid(".<EMAIL>"));
        assertFalse(DataValidation.isEmailValid("doubleat@@domain.com"));
        assertFalse(DataValidation.isEmailValid("us:<EMAIL>"));
        assertFalse(DataValidation.isEmailValid("us;<EMAIL>"));
        assertFalse(DataValidation.isEmailValid("us,<EMAIL>"));
        assertFalse(DataValidation.isEmailValid("us<<EMAIL>"));
        assertFalse(DataValidation.isEmailValid("us[<EMAIL>"));
    }
}
