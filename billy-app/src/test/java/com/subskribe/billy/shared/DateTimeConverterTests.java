package com.subskribe.billy.shared;

import com.subskribe.billy.shared.temporal.DateTimeConverter;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.TimeZone;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class DateTimeConverterTests {

    @Test
    public void getStartOfCurrentDay() {
        // Arrange
        var timeZone = TimeZone.getTimeZone("US/Pacific");
        var localDateTime = LocalDateTime.of(2021, 11, 11, 10, 23, 30, 500000);
        var instantAtTimeZone = localDateTime.atZone(timeZone.toZoneId()).toInstant();
        long expectedStartOfDayEpoch = 1636617600;

        var startOfDayInstant = DateTimeConverter.getStartOfCurrentDay(instantAtTimeZone, timeZone);
        Assertions.assertThat(expectedStartOfDayEpoch).isEqualTo(startOfDayInstant.getEpochSecond());
    }

    @Test
    public void testZonedStartOfDay() {
        Instant instant = DateTimeConverter.startOfDayToInstant(TimeZone.getTimeZone("US/Pacific"));
        LocalDateTime zoned = LocalDateTime.ofInstant(instant, TimeZone.getTimeZone("US/Pacific").toZoneId());
        Assertions.assertThat(zoned.getHour()).isZero();
        Assertions.assertThat(zoned.getMinute()).isZero();
        Assertions.assertThat(zoned.getSecond()).isZero();
        Assertions.assertThat(zoned.getNano()).isZero();
    }
}
