package com.subskribe.billy.shared.lock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.testcontainers.containers.localstack.LocalStackContainer.Service.DYNAMODB;

import com.amazonaws.services.dynamodbv2.LockItem;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.dynamodb.DynamoDbClientProvider;
import com.subskribe.billy.idempotency.model.DynamoDbConfiguration;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.data.Offset;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.utility.DockerImageName;
import software.amazon.awssdk.services.dynamodb.model.AttributeDefinition;
import software.amazon.awssdk.services.dynamodb.model.BillingMode;
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest;
import software.amazon.awssdk.services.dynamodb.model.KeySchemaElement;
import software.amazon.awssdk.services.dynamodb.model.KeyType;
import software.amazon.awssdk.services.dynamodb.model.ScalarAttributeType;

@Disabled("These tests take a long time to run and are disabled in CI")
class DynamoDBLockProviderTest {

    private static final LocalStackContainer LOCAL_STACK_CONTAINER = new LocalStackContainer(
        DockerImageName.parse("localstack/localstack:3.2.0")
    ).withServices(DYNAMODB);
    private static final String LOCK_TABLE_NAME = RandomStringUtils.randomAlphabetic(10);

    private static DynamoDBLockProvider dynamoDBLockProvider;

    @BeforeAll
    static void beforeAll() {
        LOCAL_STACK_CONTAINER.start();
        DynamoDbConfiguration dynamoDbConfiguration = new DynamoDbConfiguration();
        dynamoDbConfiguration.setRegion(LOCAL_STACK_CONTAINER.getRegion());
        dynamoDbConfiguration.setDbUrl(LOCAL_STACK_CONTAINER.getEndpointOverride(DYNAMODB).toString());
        dynamoDbConfiguration.setLockTable(LOCK_TABLE_NAME);
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        when(billyConfiguration.getDynamoDbConfiguration()).thenReturn(dynamoDbConfiguration);

        DynamoDbClientProvider dynamoDbClientProvider = new DynamoDbClientProvider(billyConfiguration);
        dynamoDbClientProvider.getDynamoDbClient().createTable(createTableRequest());
        dynamoDBLockProvider = new DynamoDBLockProvider(dynamoDbClientProvider, billyConfiguration);
    }

    @Test
    void testLockWaitsForCorrectDuration() throws Exception {
        String lockPartitionKey = UUID.randomUUID().toString();
        // Get lock first
        LockItem lockItem = dynamoDBLockProvider.acquireLockBlocking(lockPartitionKey);
        // Trying to get the same lock again should fail after timeout
        Instant start = Instant.now();
        Optional<LockItem> secondLock = dynamoDBLockProvider.tryAcquireLock(lockPartitionKey, 3, 30);
        Instant end = Instant.now();
        lockItem.close();
        assertThat(secondLock).isEmpty();
        assertThat(end.getEpochSecond() - start.getEpochSecond()).isCloseTo(30, Offset.offset(2L));
    }

    @Test
    void testTimeoutDoesNotTakeEffectIfShorterThanLeaseDuration() throws Exception {
        String lockPartitionKey = UUID.randomUUID().toString();
        // Get lock first
        LockItem lockItem = dynamoDBLockProvider.acquireLockBlocking(lockPartitionKey);
        // Trying to get the same lock again should fail after timeout
        Instant start = Instant.now();
        // Timeout of 5 seconds should not take effect, since it's shorter than the 20 second lease duration
        Optional<LockItem> secondLock = dynamoDBLockProvider.tryAcquireLock(lockPartitionKey, 1, 5);
        Instant end = Instant.now();
        lockItem.close();
        assertThat(secondLock).isEmpty();
        assertThat(end.getEpochSecond() - start.getEpochSecond()).isCloseTo(20, Offset.offset(2L));
    }

    @Test
    void testLockIsHeldUntilClosed() throws Exception {
        String lockPartitionKey = UUID.randomUUID().toString();
        Optional<LockItem> lock = dynamoDBLockProvider.tryAcquireLock(lockPartitionKey, 1, 20);
        assertThat(lock).isPresent();
        LockItem lockItem = lock.get();
        assertThat(lockItem.isExpired()).isFalse();
        // Sleep for 2 minutes. Arbitrarily larger than the lease duration, timeout, etc.
        Thread.sleep(120 * 1000);
        // Lock should still be active
        assertThat(lockItem.isExpired()).isFalse();
        // Trying to get a new lock should still fail
        Optional<LockItem> secondLock = dynamoDBLockProvider.tryAcquireLock(lockPartitionKey, 1, 20);
        assertThat(secondLock).isEmpty();
        lockItem.close();
        // Trying to get a new lock should now work
        secondLock = dynamoDBLockProvider.tryAcquireLock(lockPartitionKey, 1, 5);
        assertThat(secondLock).isPresent();
        secondLock.get().close();
    }

    private static CreateTableRequest createTableRequest() {
        return CreateTableRequest.builder()
            .tableName(LOCK_TABLE_NAME)
            .attributeDefinitions(AttributeDefinition.builder().attributeName("key").attributeType(ScalarAttributeType.S).build())
            .keySchema(KeySchemaElement.builder().attributeName("key").keyType(KeyType.HASH).build())
            .billingMode(BillingMode.PAY_PER_REQUEST)
            .build();
    }
}
