package com.subskribe.billy.shared.document;

import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.order.document.OrderDocumentJson;
import com.subskribe.billy.order.document.OrderTemplateData;
import java.util.List;
import java.util.TimeZone;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class TemplateScriptRunnerTest {

    private static final String SCRIPT_WITH_SYSTEM_NOT_ALLOWED =
        """
        System.exit()
        """;

    private static final String SCRIPTS_WITH_BLACKLISTED_IMPORT =
        """
          import java.java.io.File
          int x = 20
        """;

    private static final String OK_SCRIPT =
        """
        int test = 10
        scriptData.put("test", test)
        """;

    private static final String SCRIPT_WITH_DISALLOWED_START_IMPORT =
        """
        import java.lang.*
        int test = 10
        templateData.addData("test", test)
        """;

    @Test
    void scriptWithSystemKeywordIsRejected() {
        OrderTemplateData orderTemplateData = getOrderTemplateData();
        ScriptRunResult result = TemplateScriptRunner.runScript(List.of(SCRIPT_WITH_SYSTEM_NOT_ALLOWED), orderTemplateData);

        Assertions.assertThat(result.numberOfScriptsSuccessfullyRan()).isZero();
        Assertions.assertThat(result.scriptData()).isEmpty();
    }

    @Test
    void scriptWithBlacklistedImportIsRejected() {
        OrderTemplateData orderTemplateData = getOrderTemplateData();
        ScriptRunResult result = TemplateScriptRunner.runScript(List.of(SCRIPTS_WITH_BLACKLISTED_IMPORT, OK_SCRIPT), orderTemplateData);

        Assertions.assertThat(result.numberOfScriptsSuccessfullyRan()).isOne();
        Assertions.assertThat(result.scriptData().get("test")).isEqualTo(10);
    }

    @Test
    void emptyScriptIsRejected() {
        OrderTemplateData orderTemplateData = getOrderTemplateData();
        ScriptRunResult result = TemplateScriptRunner.runScript(List.of(StringUtils.EMPTY), orderTemplateData);

        Assertions.assertThat(result.numberOfScriptsSuccessfullyRan()).isZero();
        Assertions.assertThat(result.scriptData()).isEmpty();
    }

    @Test
    void scriptWithBlacklistedStarImportIsRejected() {
        OrderTemplateData orderTemplateData = getOrderTemplateData();
        ScriptRunResult result = TemplateScriptRunner.runScript(List.of(SCRIPT_WITH_DISALLOWED_START_IMPORT), orderTemplateData);

        Assertions.assertThat(result.numberOfScriptsSuccessfullyRan()).isZero();
        Assertions.assertThat(result.scriptData()).isEmpty();
    }

    private static OrderTemplateData getOrderTemplateData() {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");
        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);
        return new OrderTemplateData(orderDocumentJson);
    }
}
