package com.subskribe.billy.shared.webhook;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.Partitioned;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import com.subskribe.billy.shared.task.queue.scheduler.TaskDispatcher;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.ws.rs.core.MultivaluedHashMap;
import org.glassfish.hk2.api.IterableProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class IncomingWebhookTaskDispatcherTest {

    private static final IncomingWebhookType TEST_WEBHOOK_TYPE = new IncomingWebhookType("test-webhook");
    private static final String TEST_TENANT_ID = "test-tenant-id";
    private static final String TEST_PAYLOAD = "{\"data\":\"test\"}";

    @Mock
    private IncomingWebhookProcessor mockProcessor;

    @Mock
    private ObjectMapper mockObjectMapper;

    @Mock
    private TaskDispatcher mockTaskDispatcher;

    @Mock
    private WebhookDataStore mockWebhookDataStore;

    @Mock
    private IterableProvider<IncomingWebhookProcessor> mockProcessorProvider;

    private IncomingWebhookTaskDispatcher dispatcher;
    private ImmutableIncomingWebhook testWebhook;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        testWebhook = ImmutableIncomingWebhook.builder()
            .webhookType(TEST_WEBHOOK_TYPE)
            .payload(TEST_PAYLOAD)
            .receivedOn(System.currentTimeMillis())
            .headers(new MultivaluedHashMap<>())
            .build();

        Map<IncomingWebhookType, IncomingWebhookProcessor> processorMap = new HashMap<>();
        processorMap.put(TEST_WEBHOOK_TYPE, mockProcessor);

        when(mockProcessor.getWebhookType()).thenReturn(TEST_WEBHOOK_TYPE);
        when(mockProcessor.validate(any())).thenReturn(true);

        dispatcher = new IncomingWebhookTaskDispatcher(processorMap, mockObjectMapper, mockTaskDispatcher, mockWebhookDataStore);
    }

    @Test
    void constructorShouldThrowExceptionOnDuplicateProcessors() {
        IncomingWebhookProcessor processor1 = mock(IncomingWebhookProcessor.class);
        IncomingWebhookProcessor processor2 = mock(IncomingWebhookProcessor.class);

        when(processor1.getWebhookType()).thenReturn(TEST_WEBHOOK_TYPE);
        when(processor2.getWebhookType()).thenReturn(TEST_WEBHOOK_TYPE);

        when(mockProcessorProvider.iterator()).thenReturn(List.of(processor1, processor2).iterator());

        assertThatThrownBy(() -> new IncomingWebhookTaskDispatcher(mockProcessorProvider, mockTaskDispatcher, mockWebhookDataStore))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("Multiple processors for incoming webhook type");
    }

    @Test
    void dispatchShouldThrowExceptionWhenNoProcessorFound() {
        IncomingWebhookType unknownType = new IncomingWebhookType("unknown");
        ImmutableIncomingWebhook unknownWebhook = ImmutableIncomingWebhook.builder()
            .webhookType(unknownType)
            .payload(TEST_PAYLOAD)
            .receivedOn(System.currentTimeMillis())
            .headers(new MultivaluedHashMap<>())
            .build();

        assertThatThrownBy(() -> dispatcher.dispatch(unknownWebhook))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("No processor found for incoming webhook type " + unknownType.name());
    }

    @Test
    void dispatchShouldThrowExceptionWhenTenantNotFoundAndNotIgnored() {
        when(mockProcessor.findTenantId(testWebhook)).thenReturn(new IncomingWebhookTenantLookupResult(Optional.empty(), false));

        assertThatThrownBy(() -> dispatcher.dispatch(testWebhook))
            .isInstanceOf(ServiceFailureException.class)
            .hasMessageContaining("Could not find tenant for incoming webhook type " + TEST_WEBHOOK_TYPE.name());
    }

    @Test
    void dispatchShouldReturnSilentlyWhenTenantNotFoundAndIgnored() {
        when(mockProcessor.findTenantId(testWebhook)).thenReturn(new IncomingWebhookTenantLookupResult(Optional.empty(), true));

        dispatcher.dispatch(testWebhook);

        verify(mockProcessor).validate(testWebhook);
        verify(mockTaskDispatcher, never()).scheduleTask(any());
    }

    @Test
    void dispatchShouldThrowExceptionWhenValidationFails() {
        when(mockProcessor.validate(testWebhook)).thenReturn(false);

        assertThatThrownBy(() -> dispatcher.dispatch(testWebhook))
            .isInstanceOf(WebhookValidationFailedException.class)
            .hasMessage("Could not validate incoming webhook");

        verify(mockProcessor, never()).findTenantId(any());
        verify(mockTaskDispatcher, never()).scheduleTask(any());
    }

    @Test
    void dispatchShouldThrowExceptionWhenValidationThrowsException() {
        Exception testException = new RuntimeException("Test validation error");
        when(mockProcessor.validate(testWebhook)).thenThrow(testException);

        assertThatThrownBy(() -> dispatcher.dispatch(testWebhook))
            .isInstanceOf(WebhookValidationFailedException.class)
            .hasMessage("Encountered exception when validating incoming webhook")
            .hasCause(testException);

        verify(mockProcessor, never()).findTenantId(any());
        verify(mockTaskDispatcher, never()).scheduleTask(any());
    }

    @Test
    void dispatchShouldStoreWebhookPayloadAndThrowExceptionOnTaskSerializationFailure() throws JsonProcessingException {
        when(mockProcessor.findTenantId(testWebhook)).thenReturn(new IncomingWebhookTenantLookupResult(Optional.of(TEST_TENANT_ID), false));

        String storageKey = "test-storage-key";
        when(mockWebhookDataStore.save(TEST_TENANT_ID, testWebhook)).thenReturn(storageKey);

        when(mockObjectMapper.writeValueAsString(any(PersistedWebhook.class))).thenThrow(new JsonProcessingException("Test exception") {});

        assertThatThrownBy(() -> dispatcher.dispatch(testWebhook))
            .isInstanceOf(ServiceFailureException.class)
            .hasMessageContaining("Failed to serialize incoming webhook");

        verify(mockProcessor).validate(testWebhook);
        verify(mockWebhookDataStore).save(TEST_TENANT_ID, testWebhook);
        verify(mockTaskDispatcher, never()).scheduleTask(any());
    }

    @Test
    void dispatchShouldStoreWebhookPayloadAndScheduleTaskWithPersistedWebhook() throws JsonProcessingException {
        when(mockProcessor.findTenantId(testWebhook)).thenReturn(new IncomingWebhookTenantLookupResult(Optional.of(TEST_TENANT_ID), false));

        String storageKey = "test-storage-key";
        when(mockWebhookDataStore.save(TEST_TENANT_ID, testWebhook)).thenReturn(storageKey);

        String serializedPersistedWebhook = "serialized persisted webhook";

        ArgumentCaptor<PersistedWebhook> persistedWebhookCaptor = ArgumentCaptor.forClass(PersistedWebhook.class);
        when(mockObjectMapper.writeValueAsString(persistedWebhookCaptor.capture())).thenReturn(serializedPersistedWebhook);

        dispatcher.dispatch(testWebhook);

        verify(mockProcessor).validate(testWebhook);
        verify(mockWebhookDataStore).save(TEST_TENANT_ID, testWebhook);

        PersistedWebhook capturedPersistedWebhook = ImmutablePersistedWebhook.builder()
            .webhookType(TEST_WEBHOOK_TYPE)
            .externalStorageKey(storageKey)
            .receivedOn(testWebhook.getReceivedOn())
            .build();
        assertThat(persistedWebhookCaptor.getValue()).isEqualTo(capturedPersistedWebhook);

        ArgumentCaptor<QueuedTaskRequest> taskCaptor = ArgumentCaptor.forClass(QueuedTaskRequest.class);
        verify(mockTaskDispatcher).scheduleTask(taskCaptor.capture());

        QueuedTaskRequest capturedRequest = taskCaptor.getValue();

        String partitionKey = TEST_TENANT_ID + "-" + TEST_WEBHOOK_TYPE.name();
        QueuedTaskRequest expectedRequest = ImmutableQueuedTaskRequest.builder()
            .module(new TaskModule(IncomingWebhookRouter.MODULE_NAME))
            .type(new TaskType(IncomingWebhookRouter.TASK_TYPE))
            .taskData(serializedPersistedWebhook)
            .tenantId(TEST_TENANT_ID)
            .taskOrder(Partitioned.with(partitionKey))
            .build();

        assertThat(capturedRequest).isEqualTo(expectedRequest);
    }
}
