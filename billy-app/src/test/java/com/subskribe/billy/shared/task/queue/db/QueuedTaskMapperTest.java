package com.subskribe.billy.shared.task.queue.db;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.jooq.default_schema.tables.records.QueuedTaskRecord;
import com.subskribe.billy.shared.task.queue.model.DefaultTaskOrder;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTask;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskParent;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskStatus;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import org.jooq.JSONB;
import org.junit.jupiter.api.Test;

class QueuedTaskMapperTest {

    private final QueuedTaskMapper queuedTaskMapper = new QueuedTaskMapper();
    private static final Instant NOW = Instant.ofEpochSecond(1704067200);
    private static final LocalDateTime NOW_LOCAL = LocalDateTime.ofEpochSecond(1704067200, 0, ZoneOffset.UTC);
    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final String ENTITY_ID = UUID.randomUUID().toString();
    private static final String PARENT_ID = UUID.randomUUID().toString();
    private static final String TASK_ID = UUID.randomUUID().toString();

    @Test
    void testConvertFullRecord() {
        QueuedTaskRecord record = buildFullRecord();
        QueuedTask expectedResult = buildFullTask();
        assertThat(queuedTaskMapper.toQueuedTask(record)).isEqualTo(expectedResult);
    }

    @Test
    void testConvertRecordWithNoOptionals() {
        QueuedTaskRecord record = buildRecordWithNoOptionals();
        QueuedTask expectedResult = buildTaskWithNoOptionals();

        assertThat(queuedTaskMapper.toQueuedTask(record)).isEqualTo(expectedResult);
    }

    @Test
    void testConvertToFullRecord() {
        QueuedTaskRecord expectedResult = buildFullRecord();
        QueuedTask task = buildFullTask();

        assertThat(queuedTaskMapper.toRecord(task)).isEqualTo(expectedResult);
    }

    @Test
    void testConvertToRecordWithNoOptionals() {
        QueuedTaskRecord expectedResult = buildRecordWithNoOptionals();
        QueuedTask task = buildTaskWithNoOptionals();

        assertThat(queuedTaskMapper.toRecord(task)).isEqualTo(expectedResult);
    }

    private QueuedTaskRecord buildFullRecord() {
        QueuedTaskRecord record = new QueuedTaskRecord();
        record.setTaskId(TASK_ID);
        record.setTaskStatus("WAITING");
        record.setCreatedOn(NOW_LOCAL);
        record.setLastStartedOn(NOW_LOCAL.minusSeconds(1));
        record.setCompletedOn(NOW_LOCAL.minusSeconds(2));
        record.setDelayedUntil(NOW_LOCAL.minusSeconds(3));
        record.setTaskModule("TestModule");
        record.setTaskType("TestType");
        record.setTaskData("TestTaskData");
        record.setTaskMetadata(JSONB.valueOf("{\"key\": \"value\"}"));
        record.setTenantId(TENANT_ID);
        record.setEntityIds(EntityFixture.ALL_ENTITY_IDS.toArray(new String[0]));
        record.setEntityId(ENTITY_ID);
        record.setPartitionKey("TestPartitionKey");
        record.setSequenceNumber(100L);
        record.setParentType("TestParentType");
        record.setParentId(PARENT_ID);
        record.setFailureReason("Failure Reason");
        return record;
    }

    private QueuedTaskRecord buildRecordWithNoOptionals() {
        QueuedTaskRecord record = new QueuedTaskRecord();
        record.setTaskId(TASK_ID);
        record.setTaskStatus("WAITING");
        record.setCreatedOn(NOW_LOCAL);
        record.setLastStartedOn(NOW_LOCAL.minusSeconds(1));
        record.setCompletedOn(NOW_LOCAL.minusSeconds(2));
        record.setDelayedUntil(NOW_LOCAL.minusSeconds(3));
        record.setTaskModule("TestModule");
        record.setTaskType("TestType");
        record.setTaskData("TestTaskData");
        record.setTaskMetadata(JSONB.valueOf("{}"));
        record.setTenantId(TENANT_ID);
        record.setEntityIds(new String[0]);
        return record;
    }

    private QueuedTask buildFullTask() {
        return ImmutableQueuedTask.builder()
            .taskId(TASK_ID)
            .status(TaskStatus.WAITING)
            .createdOn(NOW)
            .lastStartedOn(NOW.minusSeconds(1))
            .completedOn(NOW.minusSeconds(2))
            .delayedUntil(NOW.minusSeconds(3))
            .module(new TaskModule("TestModule"))
            .type(new TaskType("TestType"))
            .taskData("TestTaskData")
            .taskMetadata(Map.of("key", "value"))
            .tenantId(TENANT_ID)
            .entityIds(EntityFixture.ALL_ENTITY_IDS)
            .entityId(ENTITY_ID)
            .taskOrder(new DefaultTaskOrder("TestPartitionKey", 100L))
            .parent(ImmutableTaskParent.of("TestParentType", PARENT_ID))
            .failureReason("Failure Reason")
            .build();
    }

    private QueuedTask buildTaskWithNoOptionals() {
        return ImmutableQueuedTask.builder()
            .taskId(TASK_ID)
            .status(TaskStatus.WAITING)
            .createdOn(NOW)
            .lastStartedOn(NOW.minusSeconds(1))
            .completedOn(NOW.minusSeconds(2))
            .delayedUntil(NOW.minusSeconds(3))
            .module(new TaskModule("TestModule"))
            .type(new TaskType("TestType"))
            .taskData("TestTaskData")
            .taskMetadata(Map.of())
            .tenantId(TENANT_ID)
            .entityIds(Set.of())
            .build();
    }
}
