package com.subskribe.billy.shared.task.queue.processor;

import static com.subskribe.billy.shared.task.queue.processor.TaskExecutor.THREADS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityContextProviderFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.shared.infra.RuntimeInfoProvider;
import com.subskribe.billy.shared.task.queue.db.TaskExecutionDAO;
import com.subskribe.billy.shared.task.queue.distribution.CapacityManager;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskExecution;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskResult;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs;
import com.subskribe.billy.shared.task.queue.model.TaskConstants;
import com.subskribe.billy.shared.task.queue.model.TaskExecution;
import com.subskribe.billy.shared.task.queue.model.TaskExecutionStatus;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.retry.TaskBackoffResultBuilder;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.TransactionalCallable;
import org.jooq.impl.DefaultConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

@Disabled("Thread.sleep not working reliably enough - will switch to actually mocking the thread pool for more control")
class TaskExecutorTest {

    private TaskRouter taskRouter;
    private TaskResultHandler taskResultHandler;
    private Clock clock;
    private TaskExecutionDAO taskExecutionDAO;
    private CapacityManager capacityManager;
    private final Map<String, TrackedTaskExecution> trackedTaskExecutions = new ConcurrentHashMap<>();

    private TaskExecutor taskExecutor;

    private static final Instant NOW = Instant.now();
    private static final TaskResult SUCCESS = ImmutableTaskResult.builder().successful(true).retryable(false).build();
    private static final TaskResult TIMED_OUT = ImmutableTaskResult.builder()
        .successful(false)
        .retryable(true)
        .failureReason(TaskConstants.TIMED_OUT_FAILURE_REASON)
        .build();
    private static final TaskResult UNEXPECTED_FAIL = ImmutableTaskResult.builder()
        .successful(false)
        .retryable(false)
        .failureReason(TaskConstants.LEAKED_EXCEPTION_FAILURE_REASON)
        .build();
    private static final Configuration JOOQ_CONFIGURATION_STUB = new DefaultConfiguration();

    @BeforeEach
    void setUp() {
        taskRouter = mock(TaskRouter.class);
        taskResultHandler = mock(TaskResultHandler.class);
        clock = mock(Clock.class);
        taskExecutionDAO = mock(TaskExecutionDAO.class);
        EntityContextProvider entityContextProvider = EntityContextProviderFixture.buildAllEntitiesContext();
        EntityGetService entityGetService = EntityGetServiceFixture.entityGetServiceFixture();
        capacityManager = mock(CapacityManager.class);
        EventPublishingService eventPublishingService = mock(EventPublishingService.class);
        DSLContextProvider dslContextProvider = mock(DSLContextProvider.class);
        DSLContext dslContext = mock(DSLContext.class);
        TaskBackoffResultBuilder taskBackoffResultBuilder = mock(TaskBackoffResultBuilder.class);
        taskExecutor = new TaskExecutor(
            taskRouter,
            taskResultHandler,
            clock,
            taskExecutionDAO,
            capacityManager,
            entityContextProvider,
            entityGetService,
            eventPublishingService,
            dslContextProvider,
            taskBackoffResultBuilder,
            trackedTaskExecutions
        );
        when(dslContextProvider.get(anyString())).thenReturn(dslContext);
        when(clock.instant()).thenReturn(NOW);
        when(dslContext.transactionResult(any(TransactionalCallable.class))).thenAnswer(invocation ->
            invocation.getArgument(0, TransactionalCallable.class).run(JOOQ_CONFIGURATION_STUB)
        );
    }

    @Test
    void testTaskExecutionRecordSavedCorrectly() {
        QueuedTask queuedTask = QueuedTaskStubs.buildTask();
        TaskExecution taskExecution = buildTaskExecution(queuedTask);
        when(taskRouter.routeTask(queuedTask)).thenReturn(new RoutedTask(() -> SUCCESS, Duration.ofSeconds(1)));
        when(taskExecutionDAO.saveExecution(eq(JOOQ_CONFIGURATION_STUB), any(TaskExecution.class))).thenReturn(taskExecution);

        taskExecutor.executeTask(queuedTask);

        ArgumentCaptor<TaskExecution> captor = ArgumentCaptor.forClass(TaskExecution.class);
        verify(taskExecutionDAO, times(1)).saveExecution(eq(JOOQ_CONFIGURATION_STUB), captor.capture());
        assertThat(captor.getAllValues()).hasSize(1);
        assertThat(captor.getValue()).usingRecursiveComparison().ignoringFields("taskExecutionId").isEqualTo(taskExecution);
    }

    @Test
    void testSuccessfulTaskHandledAndExecutionUpdated() throws Exception {
        QueuedTask queuedTask = QueuedTaskStubs.buildTask();
        TaskExecution taskExecution = buildTaskExecution(queuedTask);
        when(taskRouter.routeTask(queuedTask)).thenReturn(new RoutedTask(supplierForSuccessAfterDelay(50), Duration.ofSeconds(1)));
        when(taskExecutionDAO.saveExecution(eq(JOOQ_CONFIGURATION_STUB), any(TaskExecution.class))).thenReturn(taskExecution);
        TaskExecution expectedCompletedUpdate = ImmutableTaskExecution.builder().from(taskExecution).status(TaskExecutionStatus.COMPLETED).build();

        taskExecutor.executeTask(queuedTask);
        assertThat(trackedTaskExecutions).containsKey(taskExecution.getTaskExecutionId());
        Thread.sleep(100);

        verify(taskResultHandler, times(1)).handleTaskResult(queuedTask, SUCCESS);
        verify(taskExecutionDAO, times(1)).updateExecutionStatus(expectedCompletedUpdate);
    }

    @Test
    void testFailedTaskHandledAndExecutionUpdated() throws Exception {
        QueuedTask queuedTask = QueuedTaskStubs.buildTask();
        TaskExecution taskExecution = buildTaskExecution(queuedTask);
        when(taskRouter.routeTask(queuedTask)).thenReturn(new RoutedTask(supplierForException(), Duration.ofSeconds(1)));
        when(taskExecutionDAO.saveExecution(eq(JOOQ_CONFIGURATION_STUB), any(TaskExecution.class))).thenReturn(taskExecution);
        TaskExecution expectedCompletedUpdate = ImmutableTaskExecution.builder()
            .from(taskExecution)
            .status(TaskExecutionStatus.COMPLETED)
            .failureReason(TaskConstants.LEAKED_EXCEPTION_FAILURE_REASON)
            .build();

        taskExecutor.executeTask(queuedTask);
        Thread.sleep(50);

        verify(taskResultHandler, times(1)).handleTaskResult(queuedTask, UNEXPECTED_FAIL);
        verify(taskExecutionDAO, times(1)).updateExecutionStatus(expectedCompletedUpdate);
    }

    @Test
    void testThreadIsCleanedUpForLongRunningTask() throws Exception {
        QueuedTask queuedTask = QueuedTaskStubs.buildTask();
        TaskExecution taskExecution = buildTaskExecution(queuedTask);
        when(taskRouter.routeTask(queuedTask)).thenReturn(new RoutedTask(supplierForSuccessAfterDelay(5000), Duration.ofMillis(500)));
        when(taskExecutionDAO.saveExecution(eq(JOOQ_CONFIGURATION_STUB), any(TaskExecution.class))).thenReturn(taskExecution);
        TaskExecution expectedCompletedUpdate = ImmutableTaskExecution.builder()
            .from(taskExecution)
            .status(TaskExecutionStatus.TIMED_OUT)
            .failureReason(TaskConstants.TIMED_OUT_FAILURE_REASON)
            .build();
        when(clock.instant()).thenReturn(NOW).thenReturn(NOW.plusSeconds(5));

        taskExecutor.executeTask(queuedTask);
        Thread.sleep(50);
        assertThat(trackedTaskExecutions).containsKey(taskExecution.getTaskExecutionId());

        taskExecutor.performThreadMonitoring();

        assertThat(trackedTaskExecutions).doesNotContainKey(taskExecution.getTaskExecutionId());
        verify(taskResultHandler, times(1)).handleTaskResult(queuedTask, TIMED_OUT);
        verify(taskExecutionDAO, times(1)).updateExecutionStatus(expectedCompletedUpdate);
    }

    @Test
    void testCapacityReportedCorrectly() {
        QueuedTask queuedTask = QueuedTaskStubs.buildTask();
        TaskExecution taskExecution = buildTaskExecution(queuedTask);
        when(taskRouter.routeTask(queuedTask)).thenReturn(new RoutedTask(supplierForSuccessAfterDelay(500), Duration.ofMillis(500)));
        when(taskExecutionDAO.saveExecution(eq(JOOQ_CONFIGURATION_STUB), any(TaskExecution.class))).thenReturn(taskExecution);

        taskExecutor.executeTask(queuedTask);
        assertThat(trackedTaskExecutions).containsKey(taskExecution.getTaskExecutionId());

        taskExecutor.reportCapacity();
        verify(capacityManager, times(1)).reportCapacity(RuntimeInfoProvider.getHostIdentifier(), THREADS - 1);
    }

    @Test
    void testPerformMaintenanceTasksRecoversFromFailures() throws Exception {
        ScheduledExecutorService ses = Executors.newScheduledThreadPool(1);
        doThrow(new RuntimeException("Capacity Manager Failed")).when(capacityManager).reportCapacity(anyString(), anyInt());
        ses.scheduleWithFixedDelay(() -> taskExecutor.performMaintenanceTasks(), 0, 100, TimeUnit.MILLISECONDS);
        Thread.sleep(1000);
        ses.shutdown();
        verify(capacityManager, atLeast(2)).reportCapacity(anyString(), anyInt());
    }

    private TaskExecution buildTaskExecution(QueuedTask queuedTask) {
        return ImmutableTaskExecution.builder()
            .taskExecutionId(UUID.randomUUID().toString())
            .taskId(queuedTask.getTaskId())
            .timeoutInSeconds(1)
            .status(TaskExecutionStatus.EXECUTING)
            .workerName(RuntimeInfoProvider.getHostIdentifier())
            .tenantId(queuedTask.getTenantId())
            .build();
    }

    private Supplier<TaskResult> supplierForSuccessAfterDelay(long millisDelay) {
        return () -> {
            try {
                Thread.sleep(millisDelay);
            } catch (InterruptedException e) {
                return SUCCESS;
            }
            return SUCCESS;
        };
    }

    private Supplier<TaskResult> supplierForException() {
        return () -> {
            throw new RuntimeException("Task failed");
        };
    }
}
