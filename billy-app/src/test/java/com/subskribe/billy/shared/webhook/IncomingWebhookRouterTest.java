package com.subskribe.billy.shared.webhook;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.task.queue.model.ImmutableProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskResult;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.MultivaluedHashMap;
import org.glassfish.hk2.api.IterableProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class IncomingWebhookRouterTest {

    private static final IncomingWebhookType TEST_TYPE = new IncomingWebhookType("test-webhook");
    private static final IncomingWebhookType UNKNOWN_TYPE = new IncomingWebhookType("unknown-webhook");

    @Mock
    private IncomingWebhookProcessor mockProcessor;

    @Mock
    private QueuedTask mockTask;

    @Mock
    private IterableProvider<IncomingWebhookProcessor> mockProcessorProvider;

    @Mock
    private WebhookDataStore webhookDataStore;

    private ObjectMapper objectMapper;
    private IncomingWebhookRouter router;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        objectMapper = JacksonProvider.defaultMapper();
        Map<IncomingWebhookType, IncomingWebhookProcessor> processorMap = new HashMap<>();
        processorMap.put(TEST_TYPE, mockProcessor);

        when(mockProcessor.getWebhookType()).thenReturn(TEST_TYPE);

        router = new IncomingWebhookRouter(processorMap, objectMapper, webhookDataStore);
    }

    @Test
    void constructorShouldThrowExceptionOnDuplicateProcessors() {
        IncomingWebhookProcessor processor1 = mock(IncomingWebhookProcessor.class);
        IncomingWebhookProcessor processor2 = mock(IncomingWebhookProcessor.class);

        when(processor1.getWebhookType()).thenReturn(TEST_TYPE);
        when(processor2.getWebhookType()).thenReturn(TEST_TYPE);

        when(mockProcessorProvider.iterator()).thenReturn(List.of(processor1, processor2).iterator());

        assertThatThrownBy(() -> new IncomingWebhookRouter(mockProcessorProvider, webhookDataStore))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("Multiple processors for incoming webhook type");
    }

    @Test
    void processShouldReturnFailureForInvalidTaskType() {
        when(mockTask.getType()).thenReturn(new TaskType("invalid-type"));

        TaskResult result = router.process(mockTask);

        TaskResult expectedResult = ImmutableTaskResult.builder().successful(false).retryable(false).failureReason("Invalid task type").build();

        assertThat(result).isEqualTo(expectedResult);
        verify(mockProcessor, never()).process(any());
    }

    @Test
    void processShouldReturnFailureWhenDeserializationFails() throws Exception {
        when(mockTask.getType()).thenReturn(new TaskType("incoming-webhook"));
        String invalidJson = "invalid json";
        when(mockTask.getTaskData()).thenReturn(invalidJson);

        TaskResult result = router.process(mockTask);

        TaskResult expectedResult = ImmutableTaskResult.builder()
            .successful(false)
            .retryable(false)
            .failureReason("Failed to deserialize incoming webhook")
            .build();

        assertThat(result).isEqualTo(expectedResult);
        verify(mockProcessor, never()).process(any());
        verify(webhookDataStore, never()).getByKey(any());
    }

    @Test
    void processShouldReturnFailureWhenNoProcessorFound() throws Exception {
        long receivedTime = System.currentTimeMillis();
        String storageKey = "test-storage-key";
        PersistedWebhook persistedWebhook = ImmutablePersistedWebhook.builder()
            .webhookType(UNKNOWN_TYPE)
            .externalStorageKey(storageKey)
            .receivedOn(receivedTime)
            .build();

        String persistedWebhookJson = objectMapper.writeValueAsString(persistedWebhook);
        when(mockTask.getType()).thenReturn(new TaskType("incoming-webhook"));
        when(mockTask.getTaskData()).thenReturn(persistedWebhookJson);

        TaskResult result = router.process(mockTask);

        TaskResult expectedResult = ImmutableTaskResult.builder()
            .successful(false)
            .retryable(false)
            .failureReason("No processor found for webhook type unknown-webhook")
            .build();

        assertThat(result).isEqualTo(expectedResult);
        verify(mockProcessor, never()).process(any());
    }

    @Test
    void processShouldReturnSuccessOnSuccessfulProcessing() throws Exception {
        long receivedTime = System.currentTimeMillis();
        String storageKey = "test-storage-key";
        String webhookBody = "test payload";
        MultivaluedHashMap<String, String> headers = new MultivaluedHashMap<>();

        PersistedWebhook persistedWebhook = ImmutablePersistedWebhook.builder()
            .webhookType(TEST_TYPE)
            .externalStorageKey(storageKey)
            .receivedOn(receivedTime)
            .build();

        WebhookPayload webhookPayload = ImmutableWebhookPayload.builder().body(webhookBody).headers(headers).build();

        String persistedWebhookJson = objectMapper.writeValueAsString(persistedWebhook);
        when(mockTask.getType()).thenReturn(new TaskType("incoming-webhook"));
        when(mockTask.getTaskData()).thenReturn(persistedWebhookJson);
        when(webhookDataStore.getByKey(storageKey)).thenReturn(webhookPayload);

        TaskResult result = router.process(mockTask);

        TaskResult expectedResult = ImmutableTaskResult.builder().successful(true).build();

        assertThat(result).isEqualTo(expectedResult);

        IncomingWebhook expectedWebhook = ImmutableIncomingWebhook.builder()
            .webhookType(TEST_TYPE)
            .payload(webhookBody)
            .receivedOn(receivedTime)
            .headers(headers)
            .build();

        verify(mockProcessor).process(expectedWebhook);
        verify(webhookDataStore).getByKey(storageKey);
    }

    @Test
    void processShouldReturnFailureWhenProcessorThrowsException() throws Exception {
        long receivedTime = System.currentTimeMillis();
        String storageKey = "test-storage-key";
        PersistedWebhook persistedWebhook = ImmutablePersistedWebhook.builder()
            .webhookType(TEST_TYPE)
            .externalStorageKey(storageKey)
            .receivedOn(receivedTime)
            .build();

        WebhookPayload webhookPayload = ImmutableWebhookPayload.builder().body("test payload").headers(new MultivaluedHashMap<>()).build();

        String persistedWebhookJson = objectMapper.writeValueAsString(persistedWebhook);
        when(mockTask.getType()).thenReturn(new TaskType("incoming-webhook"));
        when(mockTask.getTaskData()).thenReturn(persistedWebhookJson);
        when(webhookDataStore.getByKey(storageKey)).thenReturn(webhookPayload);

        doThrow(new RuntimeException("Processing error")).when(mockProcessor).process(any(IncomingWebhook.class));

        TaskResult result = router.process(mockTask);

        TaskResult expectedResult = ImmutableTaskResult.builder()
            .successful(false)
            .retryable(false)
            .failureReason("Failed to process incoming webhook")
            .build();

        assertThat(result).isEqualTo(expectedResult);
        verify(webhookDataStore).getByKey(storageKey);
    }

    @Test
    void getConfigurationShouldReturnCorrectConfiguration() {
        var config = router.getConfiguration();

        var expectedConfig = ImmutableProcessorConfiguration.builder()
            .taskModule(new TaskModule("incoming-webhook"))
            .taskTimeout(Duration.ofSeconds(120))
            .build();

        assertThat(config).isEqualTo(expectedConfig);
    }
}
