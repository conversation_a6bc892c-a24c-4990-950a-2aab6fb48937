package com.subskribe.billy.shared.task.queue.retry;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.shared.task.queue.db.TenantTaskExecutionDAO;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskResult;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.TaskExecution;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TaskBackoffResultBuilderTest {

    private TenantTaskExecutionDAO tenantTaskExecutionDAO;

    private Clock clock;

    private TaskBackoffResultBuilder taskBackoffResultBuilder;

    private BackoffProvider backoffProvider;

    private QueuedTask queuedTask;

    private static final String TASK_ID = UUID.randomUUID().toString();
    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final long NOW = Instant.now().getEpochSecond();

    @BeforeEach
    void setUp() {
        tenantTaskExecutionDAO = mock(TenantTaskExecutionDAO.class);
        clock = mock(Clock.class);
        taskBackoffResultBuilder = new TaskBackoffResultBuilder(tenantTaskExecutionDAO, clock);
        backoffProvider = mock(BackoffProvider.class);

        queuedTask = mock(QueuedTask.class);
        when(queuedTask.getTaskId()).thenReturn(TASK_ID);
        when(queuedTask.getTenantId()).thenReturn(TENANT_ID);
    }

    @Test
    void testMaximumRetriesExceeded() {
        ImmutableTaskBackoff backoff = ImmutableTaskBackoff.builder()
            .task(queuedTask)
            .maxAttempts(3)
            .backoffProvider(backoffProvider)
            .failureReason("test")
            .build();
        when(tenantTaskExecutionDAO.findAllExecutionsForTaskId(TASK_ID, TENANT_ID)).thenReturn(
            List.of(mock(TaskExecution.class), mock(TaskExecution.class), mock(TaskExecution.class))
        );

        TaskResult taskResult = taskBackoffResultBuilder.withBackoff(backoff);

        TaskResult expectedTaskResult = ImmutableTaskResult.builder().successful(false).retryable(false).failureReason("test").build();

        assertThat(taskResult).isEqualTo(expectedTaskResult);
    }

    @Test
    void testMaximumRetriesNotExceeded() {
        ImmutableTaskBackoff backoff = ImmutableTaskBackoff.builder()
            .task(queuedTask)
            .maxAttempts(3)
            .backoffProvider(backoffProvider)
            .failureReason("test")
            .build();
        when(tenantTaskExecutionDAO.findAllExecutionsForTaskId(TASK_ID, TENANT_ID)).thenReturn(
            List.of(mock(TaskExecution.class), mock(TaskExecution.class))
        );
        when(backoffProvider.backoff(2)).thenReturn(Duration.ofSeconds(10));
        when(clock.instant()).thenReturn(Instant.ofEpochSecond(NOW));

        TaskResult taskResult = taskBackoffResultBuilder.withBackoff(backoff);

        TaskResult expectedTaskResult = ImmutableTaskResult.builder()
            .successful(false)
            .retryable(true)
            .backoffUntil(Instant.ofEpochSecond(NOW + 10))
            .failureReason("test")
            .build();

        assertThat(taskResult).isEqualTo(expectedTaskResult);
    }
}
