package com.subskribe.billy.shared.task.queue.processor;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.shared.task.queue.db.QueuedTaskDAO;
import com.subskribe.billy.shared.task.queue.event.TaskPartitionKey;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTask;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskResult;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.model.TaskStatus;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.RandomStringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.impl.DefaultConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TaskResultHandlerTest {

    private QueuedTaskDAO queuedTaskDAO;
    private Configuration configuration;
    private EventPublishingService eventPublishingService;

    private TaskResultHandler taskResultHandler;

    @BeforeEach
    void setUp() {
        DSLContextProvider dslContextProvider = mock(DSLContextProvider.class);
        queuedTaskDAO = mock(QueuedTaskDAO.class);
        configuration = new DefaultConfiguration();
        eventPublishingService = mock(EventPublishingService.class);

        taskResultHandler = new TaskResultHandler(dslContextProvider, queuedTaskDAO, eventPublishingService);
    }

    @Test
    void testReturnEarlyIfTaskNotFound() {
        QueuedTask task = setupTask();
        TaskResult taskResult = ImmutableTaskResult.builder().successful(true).retryable(false).build();
        when(queuedTaskDAO.getTaskById(configuration, task.getTaskId())).thenReturn(Optional.empty());

        taskResultHandler.handleTaskResult(configuration, task, taskResult);

        verify(queuedTaskDAO, never()).updateTaskWithResult(any(), any());
    }

    @Test
    void testReturnEarlyIfTaskAlreadyInTerminalStatus() {
        QueuedTask task = setupTask();
        QueuedTask finishedTask = ImmutableQueuedTask.builder().from(task).status(TaskStatus.SUCCESS).build();
        TaskResult taskResult = ImmutableTaskResult.builder().successful(true).retryable(false).build();
        when(queuedTaskDAO.getTaskById(configuration, task.getTaskId())).thenReturn(Optional.of(finishedTask));

        taskResultHandler.handleTaskResult(configuration, task, taskResult);

        verify(queuedTaskDAO, never()).updateTaskWithResult(any(), any());
    }

    @Test
    void testSuccessResult() {
        QueuedTask task = setupTask();
        TaskResult taskResult = ImmutableTaskResult.builder().successful(true).retryable(false).build();
        QueuedTask expectedUpdate = ImmutableQueuedTask.builder().from(task).status(TaskStatus.SUCCESS).build();

        taskResultHandler.handleTaskResult(configuration, task, taskResult);

        verify(queuedTaskDAO, times(1)).updateTaskWithResult(configuration, expectedUpdate);
        verify(queuedTaskDAO, never()).delayPartitionKey(any(), any(), any(), any());
        verifyEventPublished(expectedUpdate);
    }

    @Test
    void testMetadataIsMerged() {
        QueuedTask task = ImmutableQueuedTask.builder().from(setupTask()).taskMetadata(Map.of("key1", "1", "key2", "2")).build();
        when(queuedTaskDAO.getTaskById(configuration, task.getTaskId())).thenReturn(Optional.of(task));
        TaskResult taskResult = ImmutableTaskResult.builder().successful(true).retryable(false).metadata(Map.of("key2", "10", "key3", "3")).build();
        QueuedTask expectedUpdate = ImmutableQueuedTask.builder()
            .from(task)
            .status(TaskStatus.SUCCESS)
            .taskMetadata(Map.of("key1", "1", "key2", "10", "key3", "3"))
            .build();

        taskResultHandler.handleTaskResult(configuration, task, taskResult);

        verify(queuedTaskDAO, times(1)).updateTaskWithResult(configuration, expectedUpdate);
        verify(queuedTaskDAO, never()).delayPartitionKey(any(), any(), any(), any());
        verifyEventPublished(expectedUpdate);
    }

    @Test
    void testFailureResultWithNoRetry() {
        String failureReason = RandomStringUtils.randomAlphabetic(100);
        QueuedTask task = setupTask();
        TaskResult taskResult = ImmutableTaskResult.builder().successful(false).retryable(false).failureReason(failureReason).build();
        QueuedTask expectedUpdate = ImmutableQueuedTask.builder().from(task).status(TaskStatus.FAILURE).failureReason(failureReason).build();

        taskResultHandler.handleTaskResult(configuration, task, taskResult);

        verify(queuedTaskDAO, times(1)).updateTaskWithResult(configuration, expectedUpdate);
        verify(queuedTaskDAO, never()).delayPartitionKey(any(), any(), any(), any());
        verifyEventPublished(expectedUpdate);
    }

    @Test
    void testFailureResultWithRetryWithoutBackoff() {
        QueuedTask task = setupTask();
        TaskResult taskResult = ImmutableTaskResult.builder().successful(false).retryable(true).build();
        QueuedTask expectedUpdate = ImmutableQueuedTask.builder().from(task).status(TaskStatus.WAITING).delayedUntil(Optional.empty()).build();

        taskResultHandler.handleTaskResult(configuration, task, taskResult);

        verify(queuedTaskDAO, times(1)).updateTaskWithResult(configuration, expectedUpdate);
        verify(queuedTaskDAO, never()).delayPartitionKey(any(), any(), any(), any());
        verifyNoInteractions(eventPublishingService);
    }

    @Test
    void testFailureResultWithRetryWithBackoffWithoutTaskOrder() {
        QueuedTask task = ImmutableQueuedTask.builder()
            .from(QueuedTaskStubs.buildTask())
            .status(TaskStatus.WAITING)
            .taskOrder(Optional.empty())
            .delayedUntil(Instant.now().minusSeconds(5))
            .build();
        when(queuedTaskDAO.getTaskById(configuration, task.getTaskId())).thenReturn(Optional.of(task));
        Instant now = Instant.now();
        TaskResult taskResult = ImmutableTaskResult.builder().successful(false).retryable(true).backoffUntil(now).build();
        QueuedTask expectedUpdate = ImmutableQueuedTask.builder().from(task).status(TaskStatus.DELAYED).delayedUntil(now).build();

        taskResultHandler.handleTaskResult(configuration, task, taskResult);

        verify(queuedTaskDAO, times(1)).updateTaskWithResult(configuration, expectedUpdate);
        verify(queuedTaskDAO, never()).delayPartitionKey(any(), any(), any(), any());
        verifyNoInteractions(eventPublishingService);
    }

    @Test
    void testFailureResultWithRetryWithBackoffWithoutTaskOrderWithExistingTaskHavingLongerDelay() {
        Instant existingDelay = Instant.now().plus(10, ChronoUnit.HOURS);
        QueuedTask task = ImmutableQueuedTask.builder()
            .from(QueuedTaskStubs.buildTask())
            .status(TaskStatus.WAITING)
            .taskOrder(Optional.empty())
            .delayedUntil(Optional.of(existingDelay))
            .build();
        when(queuedTaskDAO.getTaskById(configuration, task.getTaskId())).thenReturn(Optional.of(task));
        Instant now = Instant.now();
        TaskResult taskResult = ImmutableTaskResult.builder().successful(false).retryable(true).backoffUntil(now).build();
        QueuedTask expectedUpdate = ImmutableQueuedTask.builder().from(task).status(TaskStatus.DELAYED).delayedUntil(existingDelay).build();

        taskResultHandler.handleTaskResult(configuration, task, taskResult);

        verify(queuedTaskDAO, times(1)).updateTaskWithResult(configuration, expectedUpdate);
        verify(queuedTaskDAO, never()).delayPartitionKey(any(), any(), any(), any());
        verifyNoInteractions(eventPublishingService);
    }

    @Test
    void testFailureResultWithRetryWithBackoffWithTaskOrder() {
        QueuedTask task = setupTask();
        Instant now = Instant.now();
        TaskResult taskResult = ImmutableTaskResult.builder().successful(false).retryable(true).backoffUntil(now).build();
        QueuedTask expectedUpdate = ImmutableQueuedTask.builder().from(task).status(TaskStatus.DELAYED).delayedUntil(now).build();

        taskResultHandler.handleTaskResult(configuration, task, taskResult);

        verify(queuedTaskDAO, times(1)).updateTaskWithResult(configuration, expectedUpdate);
        verify(queuedTaskDAO, times(1)).delayPartitionKey(configuration, task.getTenantId(), task.getTaskOrder().orElseThrow().partitionKey(), now);
        verifyNoInteractions(eventPublishingService);
    }

    private void verifyEventPublished(QueuedTask task) {
        verify(eventPublishingService, times(1)).publishEventInTransaction(
            any(DSLContext.class),
            eq(EventType.TASK_COMPLETED),
            eq(task.getTenantId()),
            eq(task.getEntityIds()),
            eq(TaskPartitionKey.fromTask(task)),
            eq(UncheckedObjectMapper.defaultMapper().writeValueAsBytes(task))
        );
    }

    private QueuedTask setupTask() {
        QueuedTask task = ImmutableQueuedTask.builder().from(QueuedTaskStubs.buildTask()).status(TaskStatus.WAITING).build();
        when(queuedTaskDAO.getTaskById(configuration, task.getTaskId())).thenReturn(Optional.of(task));
        return task;
    }
}
