package com.subskribe.billy.shared.document;

import com.subskribe.billy.shared.numbers.NumberGenerators;
import java.util.Arrays;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class TemplateMathFunctionsTest {

    @Test
    void testRound() {
        Function<String, String> function = TemplateMathFunctions.round();
        Assertions.assertThat(function.apply("10.123, 2")).isEqualTo("10.12");
        Assertions.assertThat(function.apply("10.125, 2")).isEqualTo("10.13");
        Assertions.assertThat(function.apply("10.40, 0")).isEqualTo("10");
        Assertions.assertThat(function.apply("10.50, 0")).isEqualTo("11");
        assertInputValidation(function);
    }

    @Test
    void testPercent() {
        Function<String, String> function = TemplateMathFunctions.percent();
        Assertions.assertThat(function.apply("10, 100")).isEqualTo("10");
        Assertions.assertThat(function.apply("10%, 100")).isEqualTo("10");
        Assertions.assertThat(function.apply("10.5%, 100")).isEqualTo("10.5");
        assertInputValidation(function);
    }

    @Test
    void testSubtraction() {
        Function<String, String> function = TemplateMathFunctions.subtract();
        Assertions.assertThat(function.apply("110, 100")).isEqualTo("10");
        Assertions.assertThat(function.apply("110.00, 100")).isEqualTo("10");
        Assertions.assertThat(function.apply("10, 100")).isEqualTo("-90");
        Assertions.assertThat(function.apply("100, 25.5")).isEqualTo("74.5");
        assertInputValidation(function);
    }

    @Test
    void testAddition() {
        Function<String, String> function = TemplateMathFunctions.add();
        Assertions.assertThat(function.apply("110, 100")).isEqualTo("210");
        Assertions.assertThat(function.apply("110.00, 100")).isEqualTo("210");
        Assertions.assertThat(function.apply("100, 25.5")).isEqualTo("125.5");
        assertInputValidation(function);
    }

    @Test
    void testMultiply() {
        Function<String, String> function = TemplateMathFunctions.multiply();
        Assertions.assertThat(function.apply("110, 100")).isEqualTo("11000");
        Assertions.assertThat(function.apply("110.00, 100")).isEqualTo("11000");
        Assertions.assertThat(function.apply("100, 25.5")).isEqualTo("2550");
        assertInputValidation(function);
    }

    @Test
    void testDivide() {
        Function<String, String> function = TemplateMathFunctions.divide();
        Assertions.assertThat(function.apply("100, 10")).isEqualTo("10");
        Assertions.assertThat(function.apply("100.00, 10")).isEqualTo("10");
        Assertions.assertThat(function.apply("10, 100")).isEqualTo("0.1");
        Assertions.assertThat(function.apply("100, 0")).isEqualTo("Division by zero");
        assertInputValidation(function);
    }

    @Test
    void testSum() {
        Function<String, String> function = TemplateMathFunctions.sum();
        Assertions.assertThat(function.apply("110, 100, 50")).isEqualTo("260");
        Assertions.assertThat(function.apply("110.00, 100, 50")).isEqualTo("260");
        Assertions.assertThat(function.apply("100, 25.5, 10")).isEqualTo("135.5");
        Assertions.assertThat(function.apply("100, 25.5, 10,")).isEqualTo("135.5");
        Assertions.assertThat(function.apply("")).isEmpty();
        Assertions.assertThat(function.apply("abc")).isEqualTo("At least 2 operands are required");
        Assertions.assertThat(function.apply("abc, 100")).isEqualTo("Invalid Number: abc, 100");

        int[] ints = NumberGenerators.getRandomInts(51, 1, 100);
        String input = Arrays.stream(ints).mapToObj(String::valueOf).collect(Collectors.joining(","));
        Assertions.assertThat(function.apply(input)).isEqualTo("Maximum of 50 operants allowed");
    }

    @Test
    void testIsNumber() {
        Assertions.assertThat(TemplateMathFunctions.isNumber("100")).isTrue();
        Assertions.assertThat(TemplateMathFunctions.isNumber("-100")).isTrue();
        Assertions.assertThat(TemplateMathFunctions.isNumber("100.50")).isTrue();
        Assertions.assertThat(TemplateMathFunctions.isNumber("-100.50")).isTrue();
        Assertions.assertThat(TemplateMathFunctions.isNumber("abc")).isFalse();
        Assertions.assertThat(TemplateMathFunctions.isNumber("$100")).isFalse();
    }

    @Test
    void testGreaterThan() {
        Function<String, Boolean> gt = TemplateMathFunctions.gt();

        Assertions.assertThat(gt.apply("100, 10")).isTrue();
        Assertions.assertThat(gt.apply("10, 100")).isFalse();
        Assertions.assertThat(gt.apply("10, 10")).isFalse();
        Assertions.assertThat(gt.apply("10, -10")).isTrue();
    }

    @Test
    void testGreaterThanOrEquals() {
        Function<String, Boolean> gte = TemplateMathFunctions.gte();

        Assertions.assertThat(gte.apply("100, 10")).isTrue();
        Assertions.assertThat(gte.apply("10, 100")).isFalse();
        Assertions.assertThat(gte.apply("10, 10")).isTrue();
        Assertions.assertThat(gte.apply("10, -10")).isTrue();
        Assertions.assertThat(gte.apply("-10, -10")).isTrue();
    }

    @Test
    void testLessThan() {
        Function<String, Boolean> lt = TemplateMathFunctions.lt();

        Assertions.assertThat(lt.apply("10, 100")).isTrue();
        Assertions.assertThat(lt.apply("100, 10")).isFalse();
        Assertions.assertThat(lt.apply("10, 10")).isFalse();
        Assertions.assertThat(lt.apply("-10, 10")).isTrue();
    }

    @Test
    void testLessThanOrEquals() {
        Function<String, Boolean> lte = TemplateMathFunctions.lte();

        Assertions.assertThat(lte.apply("10, 100")).isTrue();
        Assertions.assertThat(lte.apply("100, 10")).isFalse();
        Assertions.assertThat(lte.apply("10, 10")).isTrue();
        Assertions.assertThat(lte.apply("-10, 10")).isTrue();
        Assertions.assertThat(lte.apply("-10, -10")).isTrue();
    }

    @Test
    void testEquals() {
        Function<String, Boolean> eq = TemplateMathFunctions.eq();

        Assertions.assertThat(eq.apply("10, 10")).isTrue();
        Assertions.assertThat(eq.apply("100, 10")).isFalse();
        Assertions.assertThat(eq.apply("10, 10")).isTrue();
        Assertions.assertThat(eq.apply("10.00, 10")).isTrue();
        Assertions.assertThat(eq.apply("-10, -10")).isTrue();
    }

    void assertInputValidation(Function<String, String> function) {
        Assertions.assertThat(function.apply("")).isEmpty();
        Assertions.assertThat(function.apply("abc")).isEqualTo("Invalid Expression: abc");
        Assertions.assertThat(function.apply("abc, 100")).isEqualTo("Invalid Number: abc, 100");
    }
}
