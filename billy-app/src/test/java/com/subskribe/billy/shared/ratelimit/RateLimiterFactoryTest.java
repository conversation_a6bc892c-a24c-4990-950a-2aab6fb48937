package com.subskribe.billy.shared.ratelimit;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.test.WithDb;
import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.BucketConfiguration;
import java.time.Duration;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class RateLimiterFactoryTest extends WithDb {

    private RateLimiterFactory rateLimiterFactory;

    private static final BucketConfiguration bucketConfiguration = BucketConfiguration.builder()
        .addLimit(Bandwidth.builder().capacity(2).refillIntervally(2, Duration.ofMinutes(1)).build())
        .build();

    @BeforeEach
    void setUp() {
        rateLimiterFactory = new RateLimiterFactory(dslContextProvider.getManagedDatasource());
    }

    @Test
    void testBucketWithStaticConfigurationWorks() {
        String rateLimitKey = UUID.randomUUID().toString();
        Bucket bucket = rateLimiterFactory.buildBucket(rateLimitKey, bucketConfiguration);

        assertThat(bucket.tryConsume(2)).isTrue();
        assertThat(bucket.tryConsume(1)).isFalse();
    }
}
