package com.subskribe.billy.shared.webhook;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.aws.s3.S3ClientProvider;
import com.subskribe.billy.exception.ServiceFailureException;
import java.io.IOException;
import java.util.Map;
import javax.ws.rs.core.MultivaluedHashMap;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

class S3WebhookDataStoreTest {

    private static final String TEST_TENANT_ID = "test-tenant-id";
    private static final String TEST_BUCKET_NAME = "test-webhook-bucket";
    private static final long TEST_TIMESTAMP = 1623456789000L;

    @Mock
    private S3ClientProvider mockS3ClientProvider;

    @Mock
    private S3Client mockS3Client;

    @Mock
    private ObjectMapper mockObjectMapper;

    @Mock
    private ResponseInputStream<GetObjectResponse> mockResponseInputStream;

    private S3WebhookDataStore webhookDataStore;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockS3ClientProvider.getS3Client()).thenReturn(mockS3Client);

        webhookDataStore = new S3WebhookDataStore(mockS3ClientProvider, TEST_BUCKET_NAME, mockObjectMapper);
    }

    @Test
    void saveShouldStoreWebhookPayloadToS3AndReturnKey() throws Exception {
        byte[] serializedBytes = "test-serialized-content".getBytes();
        MultivaluedHashMap<String, String> headers = new MultivaluedHashMap<>(Map.of("header1", "value1"));
        IncomingWebhook testWebhook = ImmutableIncomingWebhook.builder()
            .webhookType(new IncomingWebhookType("test-type"))
            .payload("test payload")
            .receivedOn(TEST_TIMESTAMP)
            .headers(headers)
            .build();
        WebhookPayload expectedPayload = ImmutableWebhookPayload.builder().body(testWebhook.getPayload()).headers(headers).build();
        when(mockObjectMapper.writeValueAsBytes(expectedPayload)).thenReturn(serializedBytes);
        when(mockS3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class))).thenReturn(PutObjectResponse.builder().build());

        String result = webhookDataStore.save(TEST_TENANT_ID, testWebhook);

        assertThat(result).startsWith(TEST_TENANT_ID + "/" + TEST_TIMESTAMP + "-");
        assertThat(result).endsWith(".json");

        ArgumentCaptor<PutObjectRequest> requestCaptor = ArgumentCaptor.forClass(PutObjectRequest.class);
        ArgumentCaptor<RequestBody> bodyCaptor = ArgumentCaptor.forClass(RequestBody.class);
        verify(mockS3Client).putObject(requestCaptor.capture(), bodyCaptor.capture());

        PutObjectRequest capturedRequest = requestCaptor.getValue();
        assertThat(capturedRequest.bucket()).isEqualTo(TEST_BUCKET_NAME);
        assertThat(capturedRequest.key()).isEqualTo(result);
    }

    @Test
    void saveShouldThrowServiceFailureExceptionWhenJsonProcessingFails() throws Exception {
        IncomingWebhook testWebhook = ImmutableIncomingWebhook.builder()
            .webhookType(new IncomingWebhookType("test-type"))
            .payload("test payload")
            .receivedOn(TEST_TIMESTAMP)
            .headers(new MultivaluedHashMap<>())
            .build();

        when(mockObjectMapper.writeValueAsBytes(any(WebhookPayload.class))).thenThrow(new JsonProcessingException("Serialization failed") {});

        assertThatThrownBy(() -> webhookDataStore.save(TEST_TENANT_ID, testWebhook))
            .isInstanceOf(ServiceFailureException.class)
            .hasMessageContaining("Failed to serialize webhook payload for storage");
    }

    @Test
    void getByKeyShouldRetrieveWebhookPayloadFromS3() throws Exception {
        String testKey = "test-key";
        WebhookPayload expectedPayload = ImmutableWebhookPayload.builder().body("test body").headers(new MultivaluedHashMap<>()).build();

        when(mockS3Client.getObject(any(GetObjectRequest.class))).thenReturn(mockResponseInputStream);

        when(mockObjectMapper.readValue(mockResponseInputStream, WebhookPayload.class)).thenReturn(expectedPayload);

        WebhookPayload result = webhookDataStore.getByKey(testKey);

        ArgumentCaptor<GetObjectRequest> requestCaptor = ArgumentCaptor.forClass(GetObjectRequest.class);
        verify(mockS3Client).getObject(requestCaptor.capture());

        GetObjectRequest capturedRequest = requestCaptor.getValue();
        assertThat(capturedRequest.bucket()).isEqualTo(TEST_BUCKET_NAME);
        assertThat(capturedRequest.key()).isEqualTo(testKey);

        verify(mockObjectMapper).readValue(mockResponseInputStream, WebhookPayload.class);

        assertThat(result).isEqualTo(expectedPayload);
    }

    @Test
    void getByKeyShouldThrowServiceFailureExceptionWhenDeserializationFails() throws Exception {
        String testKey = "test-key";
        //
        when(mockS3Client.getObject(any(GetObjectRequest.class))).thenReturn(mockResponseInputStream);

        when(mockObjectMapper.readValue(mockResponseInputStream, WebhookPayload.class)).thenThrow(new IOException("Deserialization failed"));

        assertThatThrownBy(() -> webhookDataStore.getByKey(testKey))
            .isInstanceOf(ServiceFailureException.class)
            .hasMessageContaining("Failed to deserialize webhook payload from storage");
    }
}
