package com.subskribe.billy.shared.temporal;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Optional;
import java.util.TimeZone;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class PeriodTest {

    private static final Instant NOW = Instant.now();

    private static final Instant TEN_SECONDS_FROM_NOW = NOW.plusSeconds(10);

    private static final Instant TWENTY_SECONDS_FROM_NOW = NOW.plusSeconds(20);

    private static final Instant THIRTY_SECONDS_FROM_NOW = NOW.plusSeconds(30);

    private static final TimeZone UTC_TIME_ZONE = TimeZone.getTimeZone("UTC");

    private static final Instant FIXED_INSTANT = Instant.parse("2023-01-15T08:00:00.000Z");

    @Test
    public void testNotOverlappingSecondBeforeFirst() {
        Period first = Period.between(TWENTY_SECONDS_FROM_NOW, THIRTY_SECONDS_FROM_NOW);
        Period second = Period.between(NOW, TEN_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isEmpty();
    }

    @Test
    public void testOverlapSecondEndOnFirstStart() {
        Period first = Period.between(TEN_SECONDS_FROM_NOW, THIRTY_SECONDS_FROM_NOW);
        Period second = Period.between(NOW, TEN_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isEmpty();
    }

    @Test
    public void testOverlapSecondStartBeforeFirst() {
        Period first = Period.between(TEN_SECONDS_FROM_NOW, THIRTY_SECONDS_FROM_NOW);
        Period second = Period.between(NOW, TWENTY_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(first.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(second.getEnd());
    }

    @Test
    public void testOverlapSecondStartBeforeFirstAndEndTogether() {
        Period first = Period.between(TEN_SECONDS_FROM_NOW, TWENTY_SECONDS_FROM_NOW);
        Period second = Period.between(NOW, TWENTY_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(first.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(first.getEnd()).isEqualTo(second.getEnd());
    }

    @Test
    public void testOverlapFirstPeriodInsideSecond() {
        Period first = Period.between(TEN_SECONDS_FROM_NOW, TWENTY_SECONDS_FROM_NOW);
        Period second = Period.between(NOW, THIRTY_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(first.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(first.getEnd());
    }

    @Test
    public void testOverlapStartTogetherAndSecondEndBeforeFirst() {
        Period first = Period.between(NOW, THIRTY_SECONDS_FROM_NOW);
        Period second = Period.between(NOW, TEN_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(first.getStart()).isEqualTo(second.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(second.getEnd());
    }

    @Test
    public void testOverlapFirstEqualSecond() {
        Period first = Period.between(NOW, THIRTY_SECONDS_FROM_NOW);
        Period second = Period.between(NOW, THIRTY_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(first.getStart()).isEqualTo(second.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(first.getEnd()).isEqualTo(second.getEnd());
    }

    @Test
    public void testOverlapStartTogetherAndSecondEndAfterFirst() {
        Period first = Period.between(NOW, TWENTY_SECONDS_FROM_NOW);
        Period second = Period.between(NOW, THIRTY_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(first.getStart()).isEqualTo(second.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(first.getEnd());
    }

    @Test
    public void testOverlapSecondPeriodInsideFirst() {
        Period first = Period.between(NOW, THIRTY_SECONDS_FROM_NOW);
        Period second = Period.between(TEN_SECONDS_FROM_NOW, TWENTY_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(second.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(second.getEnd());
    }

    @Test
    public void testOverlapFirstStartBeforeSecondAndEndTogether() {
        Period first = Period.between(NOW, TWENTY_SECONDS_FROM_NOW);
        Period second = Period.between(TEN_SECONDS_FROM_NOW, TWENTY_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(second.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(first.getEnd()).isEqualTo(second.getEnd());
    }

    @Test
    public void testOverlapFirstStartBeforeSecond() {
        Period first = Period.between(NOW, TWENTY_SECONDS_FROM_NOW);
        Period second = Period.between(TEN_SECONDS_FROM_NOW, THIRTY_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(second.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(first.getEnd());
    }

    @Test
    public void testOverlapFirstEndOnSecondStart() {
        Period first = Period.between(NOW, TEN_SECONDS_FROM_NOW);
        Period second = Period.between(TEN_SECONDS_FROM_NOW, TWENTY_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isEmpty();
    }

    @Test
    public void testNotOverlappingFirstBeforeSecond() {
        Period first = Period.between(NOW, TEN_SECONDS_FROM_NOW);
        Period second = Period.between(TWENTY_SECONDS_FROM_NOW, THIRTY_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isEmpty();
    }

    @Test
    public void testOverlapSecondStartAndEndOnFirstStart() {
        Period first = Period.between(NOW, THIRTY_SECONDS_FROM_NOW);
        Period second = Period.between(NOW, NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(first.getStart()).isEqualTo(second.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(second.getEnd());
    }

    @Test
    public void testOverlapSecondStartAndEndOnFirstEnd() {
        Period first = Period.between(NOW, TEN_SECONDS_FROM_NOW);
        Period second = Period.between(TEN_SECONDS_FROM_NOW, TEN_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isEmpty();
    }

    @Test
    public void testOverlapFirstStartAndEndOnSecondStart() {
        Period first = Period.between(NOW, NOW);
        Period second = Period.between(NOW, TEN_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isPresent();
        assertThat(overlap.get().getStart()).isEqualTo(first.getStart()).isEqualTo(second.getStart());
        assertThat(overlap.get().getEnd()).isEqualTo(first.getEnd());
    }

    @Test
    public void testOverlapFistStartAndEndOnSecondEnd() {
        Period first = Period.between(TEN_SECONDS_FROM_NOW, TEN_SECONDS_FROM_NOW);
        Period second = Period.between(NOW, TEN_SECONDS_FROM_NOW);

        Optional<Period> overlap = Period.overlapOf(first, second);

        assertThat(overlap).isEmpty();
    }

    @Test
    public void testInstantPeriodIsValid() {
        Period period = Period.between(NOW, NOW);

        assertThat(period.isValid()).isTrue();
    }

    @Test
    public void testToDurationInYearsFor18Months() {
        Instant plus18Months = FIXED_INSTANT.atZone(UTC_TIME_ZONE.toZoneId()).plusMonths(18).toInstant();

        BigDecimal value = Period.toDurationInYears(FIXED_INSTANT, plus18Months, UTC_TIME_ZONE);
        Assertions.assertEquals(1.5, value.doubleValue());
    }

    @Test
    public void testToDurationInYearsFor19Months() {
        Instant plus19Months = FIXED_INSTANT.atZone(UTC_TIME_ZONE.toZoneId()).plusMonths(19).toInstant();

        BigDecimal value = Period.toDurationInYears(FIXED_INSTANT, plus19Months, UTC_TIME_ZONE);
        Assertions.assertEquals(1.58, value.doubleValue());
    }

    @Test
    public void testToDurationInYearsFor2Years1Month() {
        Instant plus25Months = FIXED_INSTANT.atZone(UTC_TIME_ZONE.toZoneId()).plusYears(2).plusMonths(1).toInstant();

        BigDecimal value = Period.toDurationInYears(FIXED_INSTANT, plus25Months, UTC_TIME_ZONE);
        Assertions.assertEquals(2.08, value.doubleValue());
    }

    @Test
    public void testDurationInMonthsRounded() {
        ZonedDateTime JAN_1_2020 = ZonedDateTime.of(2020, 1, 1, 0, 0, 0, 0, UTC_TIME_ZONE.toZoneId());
        ZonedDateTime DEC_17_2020 = ZonedDateTime.of(2020, 12, 17, 0, 0, 0, 0, UTC_TIME_ZONE.toZoneId());
        ZonedDateTime JAN_1_2021 = ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, UTC_TIME_ZONE.toZoneId());
        ZonedDateTime JAN_15_2021 = ZonedDateTime.of(2021, 1, 15, 0, 0, 0, 0, UTC_TIME_ZONE.toZoneId());
        ZonedDateTime JAN_17_2021 = ZonedDateTime.of(2021, 1, 17, 0, 0, 0, 0, UTC_TIME_ZONE.toZoneId());

        Assertions.assertEquals(12L, Period.toDurationInMonthsRounded(JAN_1_2020.toInstant(), DEC_17_2020.toInstant(), UTC_TIME_ZONE));
        Assertions.assertEquals(12L, Period.toDurationInMonthsRounded(JAN_1_2020.toInstant(), JAN_1_2021.toInstant(), UTC_TIME_ZONE));
        Assertions.assertEquals(12L, Period.toDurationInMonthsRounded(JAN_1_2020.toInstant(), JAN_15_2021.toInstant(), UTC_TIME_ZONE));
        Assertions.assertEquals(13L, Period.toDurationInMonthsRounded(JAN_1_2020.toInstant(), JAN_17_2021.toInstant(), UTC_TIME_ZONE));
    }

    @Test
    public void testGetPeriodInYearsString_NormalCase() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2022-01-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        String result = Period.getPeriodInYearsString(start, end, timeZone);

        Assertions.assertEquals("2 Years", result);
    }

    @Test
    public void testGetPeriodInYearsString_SingleYear() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2021-01-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        String result = Period.getPeriodInYearsString(start, end, timeZone);

        Assertions.assertEquals("1 Year", result);
    }

    @Test
    public void testGetPeriodInYearsString_PartialYear() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2020-07-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        String result = Period.getPeriodInYearsString(start, end, timeZone);

        Assertions.assertEquals("0.5 Year", result);
    }

    @Test
    public void testGetPeriodInYearsString_DifferentTimeZone() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2021-01-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("America/New_York");

        String result = Period.getPeriodInYearsString(start, end, timeZone);

        Assertions.assertEquals("1 Year", result);
    }

    @Test
    public void testGetPeriodInYearsString_NullInputs() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2021-01-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        Assertions.assertNull(Period.getPeriodInYearsString(null, end, timeZone));
        Assertions.assertNull(Period.getPeriodInYearsString(start, null, timeZone));
        Assertions.assertNull(Period.getPeriodInYearsString(start, end, null));
    }

    @Test
    public void testGetPeriodInYearsString_EndDateBeforeStartDate() {
        Instant start = Instant.parse("2021-01-01T00:00:00Z");
        Instant end = Instant.parse("2020-01-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        String result = Period.getPeriodInYearsString(start, end, timeZone);

        Assertions.assertEquals("-1 Year", result);
    }

    @Test
    public void testGetPeriodInMonthsString_NormalCase() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2020-04-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        String result = Period.getPeriodInMonthsString(start, end, timeZone);

        Assertions.assertEquals("3 Months", result);
    }

    @Test
    public void testGetPeriodInMonthsString_SingleMonth() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2020-02-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        String result = Period.getPeriodInMonthsString(start, end, timeZone);

        Assertions.assertEquals("1 Month", result);
    }

    @Test
    public void testGetPeriodInMonthsString_LongPeriod() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2021-01-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        String result = Period.getPeriodInMonthsString(start, end, timeZone);

        Assertions.assertEquals("12 Months", result);
    }

    @Test
    public void testGetPeriodInMonthsString_DifferentTimeZone() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2020-02-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("Asia/Tokyo");

        String result = Period.getPeriodInMonthsString(start, end, timeZone);

        Assertions.assertEquals("1 Month", result);
    }

    @Test
    public void testGetPeriodInMonthsString_NullInputs() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2020-02-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        Assertions.assertNull(Period.getPeriodInMonthsString(null, end, timeZone));
        Assertions.assertNull(Period.getPeriodInMonthsString(start, null, timeZone));
        Assertions.assertNull(Period.getPeriodInMonthsString(start, end, null));
    }

    @Test
    public void testGetPeriodInMonthsString_EndDateBeforeStartDate() {
        Instant start = Instant.parse("2020-02-01T00:00:00Z");
        Instant end = Instant.parse("2020-01-01T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        String result = Period.getPeriodInMonthsString(start, end, timeZone);

        Assertions.assertEquals("-1 Month", result);
    }

    @Test
    public void testGetPeriodInMonthsString_PartialMonth() {
        Instant start = Instant.parse("2020-01-01T00:00:00Z");
        Instant end = Instant.parse("2020-01-15T00:00:00Z");
        TimeZone timeZone = TimeZone.getTimeZone("UTC");

        String result = Period.getPeriodInMonthsString(start, end, timeZone);

        Assertions.assertEquals("0 Month", result);
    }
}
