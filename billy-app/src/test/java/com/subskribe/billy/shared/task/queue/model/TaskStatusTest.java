package com.subskribe.billy.shared.task.queue.model;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class TaskStatusTest {

    @Test
    void testIsTerminal() {
        assertTrue(TaskStatus.SUCCESS.isTerminal());
        assertTrue(TaskStatus.FAILURE.isTerminal());
        assertFalse(TaskStatus.WAITING.isTerminal());
        assertFalse(TaskStatus.IN_PROGRESS.isTerminal());
        assertFalse(TaskStatus.DELAYED.isTerminal());
    }
}
