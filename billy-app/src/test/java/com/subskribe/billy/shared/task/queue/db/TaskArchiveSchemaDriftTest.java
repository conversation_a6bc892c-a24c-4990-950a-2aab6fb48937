package com.subskribe.billy.shared.task.queue.db;

import static com.subskribe.billy.jooq.default_schema.Tables.QUEUED_TASK;
import static com.subskribe.billy.jooq.default_schema.Tables.QUEUED_TASK_ARCHIVE;
import static com.subskribe.billy.jooq.default_schema.Tables.QUEUED_TASK_EXECUTION;
import static com.subskribe.billy.jooq.default_schema.Tables.QUEUED_TASK_EXECUTION_ARCHIVE;
import static org.assertj.core.api.Assertions.assertThat;

import java.util.Objects;
import org.jooq.Field;
import org.junit.jupiter.api.Test;

public class TaskArchiveSchemaDriftTest {

    @Test
    void testNoSchemaDriftForQueuedTaskArchive() {
        QUEUED_TASK.fieldStream().forEach(field -> compareFields(field, QUEUED_TASK_ARCHIVE.field(field.getName())));
    }

    @Test
    void testNoSchemaDriftForQueuedTaskExecutionArchive() {
        QUEUED_TASK_EXECUTION.fieldStream().forEach(field -> compareFields(field, QUEUED_TASK_EXECUTION_ARCHIVE.field(field.getName())));
    }

    private static void compareFields(Field<?> a, Field<?> b) {
        Objects.requireNonNull(a);
        Objects.requireNonNull(b);
        assertThat(a.getName()).isEqualTo(b.getName());
        assertThat(a.getDataType()).isEqualTo(b.getDataType());
    }
}
