package com.subskribe.billy.shared.document;

import com.subskribe.billy.order.document.OrderDocumentJson;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class TemplateScriptParserTest {

    private static final String TEMPLATE_WITH_NO_SCRIPTS =
        """
        {{#paymentLink}}
        <br/>
        <div>
            <h3 class="section-header">Automatic Payments</h3>
            <p>
                To make automatic payments, add a payment instrument as your primary payment method.<br/>
                <a href="{{paymentLink}}">Submit Payment Details</a>
            </p>
        </div>
        {{/paymentLink}}
        """;

    private static final String TEMPLATE_WITH_ONE_SCRIPT =
        """
        {{#paymentLink}}
        <br/>
        <div>
            <h3 class="section-header">Automatic Payments</h3>
            <p>
                To make automatic payments, add a payment instrument as your primary payment method.<br/>
                <a href="{{paymentLink}}">Submit Payment Details</a>
            </p>
        </div>
        {{/paymentLink}}

        {{#formattedBeforeSignatureSection}} {{{formattedBeforeSignatureSection}}} {{/formattedBeforeSignatureSection}}

        <groovy>
          templateData.addData("numberOfLineItems", templateData.lineItems.size())
          def orderOwnerEmail = ""
          if (templateData.orderOwner) {
            String firstName = templateData.orderOwner.split(" ")[0]
            orderOwnerEmail = firstName.toUpperCase() + "@subskribe.com"
          }
          List<String> myList = new ArrayList<>();
          templateData.addData("orderOwnerEmail", orderOwnerEmail)
        </groovy>
        """;

    private static final String TEMPLATE_WITH_MULTIPLE_SCRIPTS =
        """
        <groovy>
          templateData.addData("author", "Seyed")
        </groovy>
        {{#paymentLink}}
        <br/>
        <div>
            <h3 class="section-header">Automatic Payments</h3>
            <p>
                To make automatic payments, add a payment instrument as your primary payment method.<br/>
                <a href="{{paymentLink}}">Submit Payment Details</a>
            </p>
        </div>
        {{/paymentLink}}

        {{#formattedBeforeSignatureSection}} {{{formattedBeforeSignatureSection}}} {{/formattedBeforeSignatureSection}}

        <groovy>
          templateData.addData("numberOfLineItems", templateData.lineItems.size())
          def orderOwnerEmail = ""
          if (templateData.orderOwner) {
            String firstName = templateData.orderOwner.split(" ")[0]
            orderOwnerEmail = firstName.toUpperCase() + "@subskribe.com"
          }
          List<String> myList = new ArrayList<>();
          templateData.addData("orderOwnerEmail", orderOwnerEmail)
        </groovy>
        """;

    private static final String TEMPLATE_WITH_EMPTY_SCRIPT =
        """
        <groovy>
        </groovy>
        {{#paymentLink}}
        <br/>
        <div>
            <h3 class="section-header">Automatic Payments</h3>
            <p>
                To make automatic payments, add a payment instrument as your primary payment method.<br/>
                <a href="{{paymentLink}}">Submit Payment Details</a>
            </p>
        </div>
        {{/paymentLink}}

        {{#formattedBeforeSignatureSection}} {{{formattedBeforeSignatureSection}}} {{/formattedBeforeSignatureSection}}
        """;

    @Test
    void parseTemplateWithNoScripts() {
        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setDocumentMasterTemplateContent(TEMPLATE_WITH_NO_SCRIPTS);
        List<String> scripts = TemplateScriptParser.getGroovyScripts(orderDocumentJson);

        Assertions.assertThat(scripts).isEmpty();
        Assertions.assertThat(orderDocumentJson.getDocumentMasterTemplateContent()).isEqualTo(TEMPLATE_WITH_NO_SCRIPTS);
    }

    @Test
    void parseTemplateWithOneScript() {
        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setDocumentMasterTemplateContent(TEMPLATE_WITH_ONE_SCRIPT);
        List<String> scripts = TemplateScriptParser.getGroovyScripts(orderDocumentJson);

        Assertions.assertThat(scripts.size()).isEqualTo(1);
        Assertions.assertThat(orderDocumentJson.getDocumentMasterTemplateContent()).isNotEqualTo(TEMPLATE_WITH_ONE_SCRIPT);
        Assertions.assertThat(orderDocumentJson.getDocumentMasterTemplateContent()).doesNotContain("<groovy>");
        Assertions.assertThat(orderDocumentJson.getDocumentMasterTemplateContent()).doesNotContain("</groovy>");
    }

    @Test
    void parseTemplateWithMultipleScripts() {
        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setDocumentMasterTemplateContent(TEMPLATE_WITH_MULTIPLE_SCRIPTS);
        List<String> scripts = TemplateScriptParser.getGroovyScripts(orderDocumentJson);

        Assertions.assertThat(scripts.size()).isEqualTo(2);
        Assertions.assertThat(orderDocumentJson.getDocumentMasterTemplateContent()).isNotEqualTo(TEMPLATE_WITH_MULTIPLE_SCRIPTS);
        Assertions.assertThat(orderDocumentJson.getDocumentMasterTemplateContent()).doesNotContain("<groovy>");
        Assertions.assertThat(orderDocumentJson.getDocumentMasterTemplateContent()).doesNotContain("</groovy>");
    }

    @Test
    void parseTemplateWithEmptyScript() {
        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setDocumentMasterTemplateContent(TEMPLATE_WITH_EMPTY_SCRIPT);
        List<String> scripts = TemplateScriptParser.getGroovyScripts(orderDocumentJson);

        Assertions.assertThat(scripts).isEmpty();
        Assertions.assertThat(orderDocumentJson.getDocumentMasterTemplateContent()).isNotEqualTo(TEMPLATE_WITH_EMPTY_SCRIPT);
        Assertions.assertThat(orderDocumentJson.getDocumentMasterTemplateContent()).doesNotContain("<groovy>");
        Assertions.assertThat(orderDocumentJson.getDocumentMasterTemplateContent()).doesNotContain("</groovy>");
    }
}
