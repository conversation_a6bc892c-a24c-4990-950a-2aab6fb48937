package com.subskribe.billy.shared.task.queue.model;

import static com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs.buildTask;
import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import org.junit.jupiter.api.Test;

class QueuedTaskTest {

    @Test
    void testJsonRoundTrip() throws Exception {
        ObjectMapper objectMapper = JacksonProvider.defaultMapper();
        QueuedTask queuedTask = buildTask();
        String jsonValue = objectMapper.writeValueAsString(queuedTask);
        QueuedTask deserialized = objectMapper.readValue(jsonValue, QueuedTask.class);
        assertThat(deserialized).isEqualTo(queuedTask);
    }
}
