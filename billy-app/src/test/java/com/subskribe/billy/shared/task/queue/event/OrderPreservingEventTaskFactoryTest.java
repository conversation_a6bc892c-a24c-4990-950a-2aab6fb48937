package com.subskribe.billy.shared.task.queue.event;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.shared.task.queue.model.EventSourcedTaskOrderRequest;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.Partitioned;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class OrderPreservingEventTaskFactoryTest {

    private static final String TENANT_ID = "test-tenant-id";
    private static final String CUSTOM_PARTITION_KEY = "custom-partition-key";
    private static final long SEQUENCE_NUMBER = 42L;
    private static final QueuedTaskRequest TASK_REQUEST = ImmutableQueuedTaskRequest.builder()
        .module(new TaskModule("test"))
        .type(new TaskType("test"))
        .taskData("{}")
        .tenantId(TENANT_ID)
        .entityId("test")
        .build();

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private Event event;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        when(event.getSequenceNumber()).thenReturn(SEQUENCE_NUMBER);
    }

    @Test
    void createTaskWithEmptyTaskOrderShouldAddTaskOrderWithEventSequenceNumber() {
        OrderPreservingEventTaskFactory factory = new TestFactory(tenantIdProvider, TASK_REQUEST);
        QueuedTaskRequest result = factory.createTask(event);

        assertThat(result.getTaskOrder()).isPresent();

        String expectedPartitionKey = TENANT_ID + "-" + "TestFactory";
        QueuedTaskRequest expectedTask = ImmutableQueuedTaskRequest.builder()
            .from(TASK_REQUEST)
            .taskOrder(EventSourcedTaskOrderRequest.of(expectedPartitionKey, event))
            .build();
        assertThat(result).isEqualTo(expectedTask);
    }

    @Test
    void createTaskWithExistingTaskOrderShouldPreservePartitionKeyButUseEventSequenceNumber() {
        QueuedTaskRequest requestWithPartitionKey = ImmutableQueuedTaskRequest.builder()
            .from(TASK_REQUEST)
            .taskOrder(Partitioned.with(CUSTOM_PARTITION_KEY))
            .build();

        when(event.getType()).thenReturn(EventType.ORDER_SUBMITTED);

        OrderPreservingEventTaskFactory factory = new TestFactory(tenantIdProvider, requestWithPartitionKey);
        QueuedTaskRequest result = factory.createTask(event);

        assertThat(result.getTaskOrder()).isPresent();

        QueuedTaskRequest expectedTask = ImmutableQueuedTaskRequest.builder()
            .from(TASK_REQUEST)
            .taskOrder(EventSourcedTaskOrderRequest.of(CUSTOM_PARTITION_KEY, event))
            .build();
        assertThat(result).isEqualTo(expectedTask);
    }

    private static class TestFactory extends OrderPreservingEventTaskFactory {

        private final QueuedTaskRequest stub;

        protected TestFactory(TenantIdProvider tenantIdProvider, QueuedTaskRequest stub) {
            super(tenantIdProvider);
            this.stub = stub;
        }

        @Override
        public QueuedTaskRequest createTaskForEvent(Event event) {
            return stub;
        }

        @Override
        public Set<EventType> getEventTypes() {
            return Set.of(EventType.ORDER_SUBMITTED);
        }
    }
}
