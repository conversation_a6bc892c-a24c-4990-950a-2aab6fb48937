package com.subskribe.billy.shared.pecuniary;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.model.ImmutableCurrencyConversionRate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ForeignExchangeUtilsTest {

    private final FeatureService featureService = mock(FeatureService.class);

    @BeforeEach
    void setUp() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(true);
    }

    @Test
    public void testApplyExchangeRateWithNoRounding() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UNNECESSARY)
            .build();
        validateExchangeRates(
            conversionRate,
            Map.of(
                BigDecimal.valueOf(15555.55),
                "23488.88050", // 15555.55 * 1.51 = 23488.8805
                BigDecimal.valueOf(-15555.55),
                "-23488.88050" // -15555.55 * 1.51 = -23488.8805
            )
        );
    }

    // Test cases for rounding up
    @Test
    public void testApplyExchangeRateWithRoundUp1000() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("1000"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("24000.00000");

        originalAmount = originalAmount.negate();
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("-24000.00000");

        originalAmount = BigDecimal.valueOf(10);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 10 * 1.51 = 15.10
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("1000.00000");

        originalAmount = originalAmount.negate();
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 10 * 1.51 = 15.10
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("-1000.00000");
    }

    @Test
    public void testApplyExchangeRateWithRoundUp100() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("100"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23500.00000");

        originalAmount = BigDecimal.valueOf(0.85);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        //0.85 * 1.51 = 1.2835
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("100.00000");
    }

    @Test
    public void testApplyExchangeRateWithRoundUp10() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("10"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23490.00000");

        originalAmount = BigDecimal.valueOf(0.085);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        //0.085 * 1.51 = 0.12835
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("10.00000");
    }

    @Test
    public void testApplyExchangeRateWithRoundUp1() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("1"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23489.00000");
    }

    @Test
    public void testApplyExchangeRateWithRoundUpPoint1() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("0.1"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.90000");
    }

    @Test
    public void testApplyExchangeRateWithRoundUpPointZero1() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("0.01"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.89000");
    }

    // Test cases for rounding down
    @Test
    public void testApplyExchangeRateWithRoundDown1000() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("1000"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23000.00000");

        originalAmount = BigDecimal.valueOf(10);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 10 * 1.51 = 15.10
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("0000.00000");
    }

    @Test
    public void testApplyExchangeRateWithRoundDown100() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("100"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23400.00000");

        originalAmount = BigDecimal.valueOf(0.85);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        //0.85 * 1.51 = 1.2835
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("0.00000");
    }

    @Test
    public void testApplyExchangeRateWithRoundDown10() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("10"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23480.00000");

        originalAmount = BigDecimal.valueOf(0.085);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        //0.085 * 1.51 = 0.12835
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("0.00000");
    }

    @Test
    public void testApplyExchangeRateWithRoundDown1() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("1"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.00000");
    }

    @Test
    public void testApplyExchangeRateWithRoundDownPoint1() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("0.1"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.80000");
    }

    @Test
    public void testApplyExchangeRateWithRoundDownPointZero1() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("0.01"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.88000");
    }

    private void validateExchangeRates(CurrencyConversionRate conversionRate, Map<BigDecimal, String> expectedResults) {
        for (Map.Entry<BigDecimal, String> entry : expectedResults.entrySet()) {
            BigDecimal originalAmount = entry.getKey();
            BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
            Assertions.assertThat(convertedAmount).isEqualByComparingTo(entry.getValue());
        }
    }

    // -- FF OFF --

    @Test
    public void testApplyExchangeRateWithNoRounding_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UNNECESSARY)
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.88");
    }

    // Test cases for rounding up
    @Test
    public void testApplyExchangeRateWithRoundUp1000_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("1000"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("24000.00");

        originalAmount = BigDecimal.valueOf(10);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 10 * 1.51 = 15.10
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("15.10");
    }

    @Test
    public void testApplyExchangeRateWithRoundUp100_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("100"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23500.00");

        originalAmount = BigDecimal.valueOf(0.85);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        //0.85 * 1.51 = 1.2835
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("1.28");
    }

    @Test
    public void testApplyExchangeRateWithRoundUp10_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("10"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23490.00");

        originalAmount = BigDecimal.valueOf(0.085);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        //0.085 * 1.51 = 0.12835
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("0.13");
    }

    @Test
    public void testApplyExchangeRateWithRoundUp1_FF_OFF() {
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("1"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23489.00");
    }

    @Test
    public void testApplyExchangeRateWithRoundUpPoint1_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("0.1"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.90");
    }

    @Test
    public void testApplyExchangeRateWithRoundUpPointZero1_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.UP)
            .roundingPrecision(new BigDecimal("0.01"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.88");
    }

    // Test cases for rounding down
    @Test
    public void testApplyExchangeRateWithRoundDown1000_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("1000"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23000.00");

        originalAmount = BigDecimal.valueOf(10);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 10 * 1.51 = 15.10
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("15.10");
    }

    @Test
    public void testApplyExchangeRateWithRoundDown100_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("100"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23400.00");

        originalAmount = BigDecimal.valueOf(0.85);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        //0.85 * 1.51 = 1.2835
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("1.28");
    }

    @Test
    public void testApplyExchangeRateWithRoundDown10_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("10"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23480.00");

        originalAmount = BigDecimal.valueOf(0.085);
        convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        //0.085 * 1.51 = 0.12835
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("0.13");
    }

    @Test
    public void testApplyExchangeRateWithRoundDown1_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("1"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.00");
    }

    @Test
    public void testApplyExchangeRateWithRoundDownPoint1_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("0.1"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.80");
    }

    @Test
    public void testApplyExchangeRateWithRoundDownPointZero1_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        CurrencyConversionRate conversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("AUD")
            .conversionRate(BigDecimal.valueOf(1.51))
            .effectiveDate(1612137600000L)
            .roundingTreatment(RoundingMode.DOWN)
            .roundingPrecision(new BigDecimal("0.01"))
            .build();
        BigDecimal originalAmount = BigDecimal.valueOf(15555.55);
        BigDecimal convertedAmount = ForeignExchangeUtils.applyExchangeRate(originalAmount, conversionRate, featureService);
        // 15555.55 * 1.51 = 23488.8805
        Assertions.assertThat(convertedAmount).isEqualByComparingTo("23488.88000");
    }
}
