package com.subskribe.billy.shared.task.queue.scheduler;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.event.EventStubber;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.StreamPartitionKey;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.shared.task.queue.db.TaskDispatchDAO;
import com.subskribe.billy.shared.task.queue.event.TaskPartitionKey;
import com.subskribe.billy.shared.task.queue.model.DefaultTaskOrder;
import com.subskribe.billy.shared.task.queue.model.EventBasedTaskOrder;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTask;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.Partitioned;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs;
import com.subskribe.billy.shared.task.queue.model.TaskOrder;
import com.subskribe.billy.shared.task.queue.model.TaskStatus;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Clock;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.TransactionalCallable;
import org.jooq.impl.DefaultConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class DefaultDispatcherTest {

    private TaskDispatchDAO taskDispatchDAO;
    private EventPublishingService eventPublishingService;
    private ArgumentCaptor<TransactionalCallable<?>> transactionalCallableCaptor;
    private DSLContext dslContext;

    private DefaultDispatcher dispatcher;

    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final Instant NOW = Instant.now();
    private static final Configuration JOOQ_CONFIGURATION_STUB = new DefaultConfiguration();

    @BeforeEach
    void setUp() {
        DSLContextProvider dslContextProvider = mock(DSLContextProvider.class);
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        taskDispatchDAO = mock(TaskDispatchDAO.class);
        eventPublishingService = mock(EventPublishingService.class);
        dslContext = mock(DSLContext.class);
        Clock clock = mock(Clock.class);
        transactionalCallableCaptor = ArgumentCaptor.forClass(TransactionalCallable.class);

        when(clock.instant()).thenReturn(NOW);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);
        when(dslContextProvider.get(QueuedTaskStubs.buildRequest().getTenantId())).thenReturn(dslContext);

        dispatcher = new DefaultDispatcher(dslContextProvider, tenantIdProvider, taskDispatchDAO, eventPublishingService, clock);
    }

    @Test
    void testSaveTaskWithNoPartitionKeyAndSequenceNumber() throws Throwable {
        QueuedTaskRequest request = ImmutableQueuedTaskRequest.builder().from(QueuedTaskStubs.buildRequest()).taskOrder(Optional.empty()).build();
        QueuedTask expectedReturn = QueuedTaskStubs.buildTask();
        ArgumentCaptor<QueuedTask> queuedTaskArgumentCaptor = ArgumentCaptor.forClass(QueuedTask.class);
        Event event = EventStubber.eventStub();
        when(taskDispatchDAO.insertTask(eq(JOOQ_CONFIGURATION_STUB), queuedTaskArgumentCaptor.capture())).thenReturn(expectedReturn);
        when(
            eventPublishingService.publishEventInTransaction(any(DSLContext.class), any(), any(), any(), any(StreamPartitionKey.class), any())
        ).thenReturn(event);
        when(taskDispatchDAO.updateTaskOrder(any(), any(), any())).thenReturn(expectedReturn);

        dispatcher.scheduleTask(request);

        verify(dslContext).transactionResult(transactionalCallableCaptor.capture());
        assertThat(transactionalCallableCaptor.getAllValues()).hasSize(1);

        QueuedTask result = (QueuedTask) transactionalCallableCaptor.getValue().run(JOOQ_CONFIGURATION_STUB);

        assertThat(result).isEqualTo(expectedReturn);
        verify(eventPublishingService, times(1)).publishEventInTransaction(
            any(DSLContext.class),
            eq(EventType.TASK_SCHEDULED),
            eq(request.getTenantId()),
            eq(request.getEntityIds()),
            eq(TaskPartitionKey.fromTask(expectedReturn)),
            eq(UncheckedObjectMapper.defaultMapper().writeValueAsBytes(expectedReturn))
        );
        verify(taskDispatchDAO, times(1)).updateTaskOrder(
            eq(JOOQ_CONFIGURATION_STUB),
            eq(expectedReturn),
            eq(new EventBasedTaskOrder(queuedTaskArgumentCaptor.getValue().getTaskId(), event))
        );
    }

    @Test
    void testSaveTaskWithDelayedStart() throws Throwable {
        QueuedTaskRequest request = ImmutableQueuedTaskRequest.builder()
            .from(QueuedTaskStubs.buildRequest())
            .delayedUntil(NOW.plusSeconds(60))
            .taskOrder(Optional.empty())
            .build();
        QueuedTask expectedReturn = QueuedTaskStubs.buildTask();
        ArgumentCaptor<QueuedTask> queuedTaskArgumentCaptor = ArgumentCaptor.forClass(QueuedTask.class);
        Event event = EventStubber.eventStub();
        when(taskDispatchDAO.insertTask(eq(JOOQ_CONFIGURATION_STUB), queuedTaskArgumentCaptor.capture())).thenReturn(expectedReturn);
        when(
            eventPublishingService.publishEventInTransaction(any(DSLContext.class), any(), any(), any(), any(StreamPartitionKey.class), any())
        ).thenReturn(event);
        when(taskDispatchDAO.updateTaskOrder(any(), any(), any())).thenReturn(expectedReturn);

        dispatcher.scheduleTask(request);

        verify(dslContext).transactionResult(transactionalCallableCaptor.capture());
        assertThat(transactionalCallableCaptor.getAllValues()).hasSize(1);

        QueuedTask result = (QueuedTask) transactionalCallableCaptor.getValue().run(JOOQ_CONFIGURATION_STUB);

        assertThat(result).isEqualTo(expectedReturn);
        assertThat(queuedTaskArgumentCaptor.getValue().getDelayedUntil()).contains(NOW.plusSeconds(60));
    }

    @Test
    void testSaveTaskWithPartitionKeyButNoSequenceNumber() throws Throwable {
        QueuedTaskRequest request = ImmutableQueuedTaskRequest.builder()
            .from(QueuedTaskStubs.buildRequest())
            .taskOrder(Partitioned.with("TestPartitionKey"))
            .build();
        QueuedTask expectedReturn = QueuedTaskStubs.buildTask();
        QueuedTask expectedTaskSentToDAO = buildExpectedSaveRequest(request, new DefaultTaskOrder("TestPartitionKey", Long.MAX_VALUE));
        ArgumentCaptor<QueuedTask> queuedTaskArgumentCaptor = ArgumentCaptor.forClass(QueuedTask.class);
        Event event = EventStubber.eventStub();
        when(taskDispatchDAO.insertTask(eq(JOOQ_CONFIGURATION_STUB), queuedTaskArgumentCaptor.capture())).thenReturn(expectedReturn);
        when(
            eventPublishingService.publishEventInTransaction(any(DSLContext.class), any(), any(), any(), any(StreamPartitionKey.class), any())
        ).thenReturn(event);
        when(taskDispatchDAO.updateTaskOrder(any(), any(), any())).thenReturn(expectedReturn);

        dispatcher.scheduleTask(request);

        verify(dslContext).transactionResult(transactionalCallableCaptor.capture());
        assertThat(transactionalCallableCaptor.getAllValues()).hasSize(1);

        QueuedTask result = (QueuedTask) transactionalCallableCaptor.getValue().run(JOOQ_CONFIGURATION_STUB);

        assertThat(result).isEqualTo(expectedReturn);
        assertThat(queuedTaskArgumentCaptor.getAllValues())
            .hasSize(1)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("taskId")
            .containsExactly(expectedTaskSentToDAO);
        verify(eventPublishingService, times(1)).publishEventInTransaction(
            any(DSLContext.class),
            eq(EventType.TASK_SCHEDULED),
            eq(request.getTenantId()),
            eq(request.getEntityIds()),
            eq(TaskPartitionKey.fromTask(expectedReturn)),
            eq(UncheckedObjectMapper.defaultMapper().writeValueAsBytes(expectedReturn))
        );
        verify(taskDispatchDAO, times(1)).updateTaskOrder(
            eq(JOOQ_CONFIGURATION_STUB),
            eq(expectedReturn),
            eq(new EventBasedTaskOrder("TestPartitionKey", event))
        );
    }

    private static ImmutableQueuedTask buildExpectedSaveRequest(QueuedTaskRequest request, TaskOrder expectedOrder) {
        return ImmutableQueuedTask.builder()
            .from(request)
            .taskId(UUID.randomUUID().toString())
            .status(TaskStatus.WAITING)
            .createdOn(NOW)
            .taskOrder(expectedOrder)
            .build();
    }
}
