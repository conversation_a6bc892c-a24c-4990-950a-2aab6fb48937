package com.subskribe.billy.shared.task.queue.distribution;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.shared.task.queue.db.CapacityDAO;
import java.time.Clock;
import java.time.Instant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class CapacityManagerTest {

    private CapacityDAO capacityDAO;
    private Clock clock;

    private CapacityManager capacityManager;

    @BeforeEach
    void setUp() {
        capacityDAO = mock(CapacityDAO.class);
        clock = mock(Clock.class);

        capacityManager = new CapacityManager(capacityDAO, clock);
    }

    @Test
    void testReportCapacityCallsDao() {
        String workerName = "worker";
        int capacity = 10;

        capacityManager.reportCapacity(workerName, capacity);

        verify(capacityDAO, times(1)).reportCapacity(workerName, capacity);
    }

    @Test
    void getAvailableCapacity() {
        long now = 1704067200;
        long expected = 1704067200 - 30;
        when(clock.instant()).thenReturn(Instant.ofEpochSecond(now));
        when(capacityDAO.getAvailableCapacity(Instant.ofEpochSecond(expected))).thenReturn(99);

        int availableCapacity = capacityManager.getAvailableCapacity();

        assertThat(availableCapacity).isEqualTo(99);
    }
}
