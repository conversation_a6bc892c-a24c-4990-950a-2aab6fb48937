package com.subskribe.billy.shared.pecuniary;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.subskribe.billy.exception.InvalidInputException;
import java.math.BigDecimal;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class NumbersTest {

    @Test
    public void testScaledDivisionHasCorrectScale() {
        BigDecimal result = Numbers.scaledDivide(BigDecimal.ONE, BigDecimal.valueOf(3));
        Assertions.assertThat(result).hasScaleOf(Numbers.CALCULATION_SCALE);
    }

    @Test
    public void testScaledNumberWithDifferentPrecisionHasCorrectScale() {
        BigDecimal zeroWith10Scale = Numbers.makeCalculationScale(BigDecimal.ZERO);
        assertThrows(IllegalArgumentException.class, () -> Numbers.scaledDivide(BigDecimal.ONE, zeroWith10Scale));
    }

    @Test
    public void testScaledDivisionRoundsCorrectly() {
        BigDecimal result = Numbers.scaledDivide(BigDecimal.valueOf(166.666666666666666), BigDecimal.valueOf(100));
        Assertions.assertThat(result).hasScaleOf(Numbers.CALCULATION_SCALE);
        Assertions.assertThat(result).isEqualByComparingTo("1.6666666667");
    }

    @Test
    public void testScaledPercentRoundsCorrectly() {
        BigDecimal result = Numbers.scaledPercent(BigDecimal.valueOf(0.00006666666666), BigDecimal.valueOf(100));
        Assertions.assertThat(result).hasScaleOf(Numbers.CALCULATION_SCALE);
        Assertions.assertThat(result).isEqualByComparingTo("0.00006667");
    }

    @Test
    public void testScaledPercentHandlingDivisionByZeroCorrectly() {
        BigDecimal result = Numbers.scaledPercentHandlingZeros(BigDecimal.TEN, BigDecimal.valueOf(0));
        Assertions.assertThat(result).isEqualByComparingTo("100");

        result = Numbers.scaledPercentHandlingZeros(BigDecimal.valueOf(-10), BigDecimal.valueOf(0));
        Assertions.assertThat(result).isEqualByComparingTo(BigDecimal.valueOf(-100));

        result = Numbers.scaledPercentHandlingZeros(BigDecimal.valueOf(0.00006666666666), BigDecimal.valueOf(100));
        Assertions.assertThat(result).hasScaleOf(Numbers.CALCULATION_SCALE);
        Assertions.assertThat(result).isEqualByComparingTo("0.00006667");

        result = Numbers.scaledPercentHandlingZeros(BigDecimal.valueOf(-0.00006666666666), BigDecimal.valueOf(100));
        Assertions.assertThat(result).hasScaleOf(Numbers.CALCULATION_SCALE);
        Assertions.assertThat(result).isEqualByComparingTo("-0.00006667");
    }

    @Test
    public void testIsZero() {
        BigDecimal first = BigDecimal.valueOf(0, 7);
        Assertions.assertThat(Numbers.isZero(first)).isTrue();

        Assertions.assertThat(Numbers.isZero(BigDecimal.ZERO)).isTrue();

        BigDecimal second = BigDecimal.valueOf(1, 7);
        Assertions.assertThat(Numbers.isZero(second)).isFalse();
    }

    @Test
    public void testIsNegative() {
        BigDecimal first = new BigDecimal("-0.000000000000000000007");
        Assertions.assertThat(Numbers.isNegative(first)).isTrue();

        Assertions.assertThat(Numbers.isNegative(BigDecimal.ZERO)).isFalse();

        Assertions.assertThat(Numbers.isNegative(new BigDecimal("0.000005"))).isFalse();
        Assertions.assertThat(Numbers.isNegative(new BigDecimal("-0.000005"))).isTrue();
        Assertions.assertThat(Numbers.isNegative(new BigDecimal("-10.000"))).isTrue();
        Assertions.assertThat(Numbers.isNegative(new BigDecimal("10.000"))).isFalse();
    }

    @Test
    public void testInputAmountValidation() {
        BigDecimal largeNumber = new BigDecimal("1000000000000");
        assertThrows(IllegalArgumentException.class, () -> Numbers.validateInputAmountScale(largeNumber));

        BigDecimal largerNumber = new BigDecimal("1001001001001");
        assertThrows(IllegalArgumentException.class, () -> Numbers.validateInputAmountScale(largerNumber));

        BigDecimal preciseNumber = new BigDecimal("123.123456");
        assertThrows(IllegalArgumentException.class, () -> Numbers.validateInputAmountScale(preciseNumber));

        BigDecimal negativeNumber = new BigDecimal("-123.123456");
        assertThrows(IllegalArgumentException.class, () -> Numbers.validateInputAmountScale(negativeNumber));

        BigDecimal smallNumber = new BigDecimal("0.123456");
        assertThrows(IllegalArgumentException.class, () -> Numbers.validateInputAmountScale(smallNumber));

        BigDecimal successfulNumber = new BigDecimal("123456789012.12345");
        assertDoesNotThrow(() -> Numbers.validateInputAmountScale(successfulNumber));
    }

    @Test
    public void testInputRatioValidation() {
        BigDecimal thousand = new BigDecimal("1000");
        assertThrows(InvalidInputException.class, () -> Numbers.validateInputRatioScale(thousand));

        BigDecimal largeNumberValid = new BigDecimal("1.1234567890");
        assertDoesNotThrow(() -> Numbers.validateInputRatioScale(largeNumberValid));

        BigDecimal largeNumberInvalid = new BigDecimal("1.12345678901");
        assertThrows(InvalidInputException.class, () -> Numbers.validateInputRatioScale(largeNumberInvalid));

        BigDecimal preciseNumberValid = new BigDecimal("123.1234567890");
        assertDoesNotThrow(() -> Numbers.validateInputRatioScale(preciseNumberValid));

        BigDecimal preciseNumberInvalid = new BigDecimal("123.12345678901");
        assertThrows(InvalidInputException.class, () -> Numbers.validateInputRatioScale(preciseNumberInvalid));

        BigDecimal negativeNumberValid = new BigDecimal("-123.1234567890");
        assertDoesNotThrow(() -> Numbers.validateInputRatioScale(negativeNumberValid));

        BigDecimal negativeNumberInvalid = new BigDecimal("-123.12345678901");
        assertThrows(InvalidInputException.class, () -> Numbers.validateInputRatioScale(negativeNumberInvalid));

        BigDecimal smallNumberValid = new BigDecimal("0.1234567890");
        assertDoesNotThrow(() -> Numbers.validateInputRatioScale(smallNumberValid));

        BigDecimal smallNumberInvalid = new BigDecimal("0.12345678901");
        assertThrows(InvalidInputException.class, () -> Numbers.validateInputRatioScale(smallNumberInvalid));

        BigDecimal successfulNumber = new BigDecimal("123.123456");
        assertDoesNotThrow(() -> Numbers.validateInputRatioScale(successfulNumber));
    }

    @Test
    public void testBigDecimalEqualsComparison() {
        Assertions.assertThat(Numbers.equals(new BigDecimal("100"), new BigDecimal("100.00"))).isTrue();
        Assertions.assertThat(Numbers.equals(new BigDecimal("100"), new BigDecimal("100"))).isTrue();
        Assertions.assertThat(Numbers.equals(new BigDecimal("100"), null)).isFalse();
        Assertions.assertThat(Numbers.equals(null, new BigDecimal("100"))).isFalse();
    }
}
