package com.subskribe.billy.shared.task.queue.event;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.scheduler.TaskDispatcher;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.glassfish.hk2.api.IterableProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class EventToTaskBridgeTest {

    @Mock
    private TaskDispatcher taskDispatcher;

    @Mock
    private IterableProvider<EventSourcedTaskFactory> factoriesProvider;

    @Mock
    private EventSourcedTaskFactory invoiceEventFactory;

    @Mock
    private EventSourcedTaskFactory orderEventFactory;

    @Mock
    private EventSourcedTaskFactory multiEventTypeFactory;

    @Mock
    private Event event;

    @Mock
    private QueuedTaskRequest taskRequest;

    private EventToTaskBridge bridge;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(invoiceEventFactory.getEventTypes()).thenReturn(Set.of(EventType.INVOICE_POSTED));
        when(orderEventFactory.getEventTypes()).thenReturn(Set.of(EventType.ORDER_SUBMITTED));
        when(multiEventTypeFactory.getEventTypes()).thenReturn(Set.of(EventType.INVOICE_POSTED, EventType.PAYMENT_PROCESSED));
    }

    @Test
    void constructorShouldCreateCorrectMapping() {
        mockFactoriesProvider(invoiceEventFactory, orderEventFactory);

        bridge = new EventToTaskBridge(taskDispatcher, factoriesProvider);

        Map<EventType, List<EventSourcedTaskFactory>> taskFactories = getTaskFactoriesMap();

        assertThat(taskFactories).hasSize(2);
        assertThat(taskFactories.get(EventType.INVOICE_POSTED)).hasSize(1);
        assertThat(taskFactories.get(EventType.INVOICE_POSTED).get(0)).isEqualTo(invoiceEventFactory);
        assertThat(taskFactories.get(EventType.ORDER_SUBMITTED)).hasSize(1);
        assertThat(taskFactories.get(EventType.ORDER_SUBMITTED).get(0)).isEqualTo(orderEventFactory);
    }

    @Test
    void constructorShouldHandleMultipleFactoriesForSameEventType() {
        mockFactoriesProvider(invoiceEventFactory, multiEventTypeFactory);

        bridge = new EventToTaskBridge(taskDispatcher, factoriesProvider);

        Map<EventType, List<EventSourcedTaskFactory>> taskFactories = getTaskFactoriesMap();

        assertThat(taskFactories).hasSize(2);
        assertThat(taskFactories.get(EventType.INVOICE_POSTED)).hasSize(2);
        assertThat(taskFactories.get(EventType.INVOICE_POSTED)).containsExactlyInAnyOrder(invoiceEventFactory, multiEventTypeFactory);
        assertThat(taskFactories.get(EventType.PAYMENT_PROCESSED)).hasSize(1);
        assertThat(taskFactories.get(EventType.PAYMENT_PROCESSED).get(0)).isEqualTo(multiEventTypeFactory);
    }

    @Test
    void handleEventShouldDispatchTasksForMatchingEventType() {
        mockFactoriesProvider(invoiceEventFactory, orderEventFactory);
        bridge = new EventToTaskBridge(taskDispatcher, factoriesProvider);

        when(event.getType()).thenReturn(EventType.INVOICE_POSTED);
        when(invoiceEventFactory.createTask(event)).thenReturn(taskRequest);

        bridge.handleEvent(event);

        verify(invoiceEventFactory).createTask(event);
        verify(taskDispatcher).scheduleTask(taskRequest);
        verify(orderEventFactory, never()).createTask(any());
    }

    @Test
    void handleEventShouldDispatchTasksForAllMatchingFactories() {
        mockFactoriesProvider(invoiceEventFactory, multiEventTypeFactory);
        bridge = new EventToTaskBridge(taskDispatcher, factoriesProvider);

        when(event.getType()).thenReturn(EventType.INVOICE_POSTED);

        QueuedTaskRequest taskRequest1 = mock(QueuedTaskRequest.class);
        QueuedTaskRequest taskRequest2 = mock(QueuedTaskRequest.class);

        when(invoiceEventFactory.createTask(event)).thenReturn(taskRequest1);
        when(multiEventTypeFactory.createTask(event)).thenReturn(taskRequest2);

        bridge.handleEvent(event);

        verify(invoiceEventFactory).createTask(event);
        verify(multiEventTypeFactory).createTask(event);
        verify(taskDispatcher).scheduleTask(taskRequest1);
        verify(taskDispatcher).scheduleTask(taskRequest2);
    }

    @Test
    void handleEventShouldHandleExceptionWhenTaskCreationFails() {
        mockFactoriesProvider(invoiceEventFactory, multiEventTypeFactory);
        bridge = new EventToTaskBridge(taskDispatcher, factoriesProvider);

        when(event.getType()).thenReturn(EventType.INVOICE_POSTED);
        when(event.getId()).thenReturn("test-event-id");

        doThrow(new RuntimeException("Task creation failed")).when(invoiceEventFactory).createTask(event);
        when(multiEventTypeFactory.createTask(event)).thenReturn(taskRequest);

        bridge.handleEvent(event);

        verify(taskDispatcher).scheduleTask(taskRequest);
        verify(taskDispatcher, times(1)).scheduleTask(any());
    }

    @Test
    void handleEventShouldDoNothingWhenNoMatchingFactories() {
        mockFactoriesProvider(invoiceEventFactory, orderEventFactory);
        bridge = new EventToTaskBridge(taskDispatcher, factoriesProvider);

        when(event.getType()).thenReturn(EventType.PAYMENT_PROCESSED);

        bridge.handleEvent(event);

        verify(invoiceEventFactory, never()).createTask(any());
        verify(orderEventFactory, never()).createTask(any());
        verify(taskDispatcher, never()).scheduleTask(any());
    }

    @Test
    void handleEventShouldDoNothingWhenFactoriesListIsNull() {
        mockFactoriesProvider(invoiceEventFactory, orderEventFactory);
        bridge = new EventToTaskBridge(taskDispatcher, factoriesProvider);

        when(event.getType()).thenReturn(null);

        bridge.handleEvent(event);

        verify(invoiceEventFactory, never()).createTask(any());
        verify(orderEventFactory, never()).createTask(any());
        verify(taskDispatcher, never()).scheduleTask(any());
    }

    private void mockFactoriesProvider(EventSourcedTaskFactory... factories) {
        when(factoriesProvider.spliterator()).thenReturn(Arrays.asList(factories).spliterator());
    }

    @SuppressWarnings("unchecked")
    private Map<EventType, List<EventSourcedTaskFactory>> getTaskFactoriesMap() {
        try {
            var field = EventToTaskBridge.class.getDeclaredField("taskFactories");
            field.setAccessible(true);
            return (Map<EventType, List<EventSourcedTaskFactory>>) field.get(bridge);
        } catch (Exception e) {
            throw new RuntimeException("Failed to access taskFactories field", e);
        }
    }
}
