package com.subskribe.billy.shared.temporal;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.invoice.number.RandomNumberGenerator;
import java.time.Instant;
import java.time.Month;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class DateTimeCalculatorTest {

    private static final TimeZone TIME_ZONE = TimeZone.getTimeZone("America/Los_Angeles");
    private static final ZoneId ZONE_ID = TIME_ZONE.toZoneId();

    private static final TimeZone EAST_COAST_TIME_ZONE = TimeZone.getTimeZone("America/New_York");

    private static final ZoneId EAST_COAST_ZONE_ID = EAST_COAST_TIME_ZONE.toZoneId();

    @Test
    public void testMonthPeriodsBetweenWithWholeMonths() {
        ZonedDateTime start = ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZONE_ID);
        ZonedDateTime end = ZonedDateTime.of(2022, 4, 1, 0, 0, 0, 0, ZONE_ID);

        List<Period> periods = DateTimeCalculator.getMonthPeriods(start.toInstant(), end.toInstant(), ZONE_ID, false);

        assertEquals(3, periods.size());
        assertEquals(start.toInstant(), periods.get(0).getStart());
        assertEquals(start.plusMonths(1).toInstant(), periods.get(1).getStart());
        assertEquals(start.plusMonths(2).toInstant(), periods.get(2).getStart());
        assertEquals(end.toInstant(), periods.get(2).getEnd());
    }

    @Test
    public void testWholeMonthDurations() {
        ZonedDateTime start = ZonedDateTime.of(2022, 1, 31, 0, 0, 0, 0, ZONE_ID);
        ZonedDateTime end1 = start.plusMonths(1);
        ZonedDateTime end2 = start.plusMonths(4);

        var duration1 = DateTimeCalculator.getDurationBetween(start.toInstant(), end1.toInstant(), TIME_ZONE);
        var duration2 = DateTimeCalculator.getDurationBetween(start.toInstant(), end2.toInstant(), TIME_ZONE);

        assertEquals(1, duration1.getMonths());
        assertEquals(0, duration1.getDays());
        assertEquals(0, duration1.getSeconds());

        assertEquals(4, duration2.getMonths());
        assertEquals(0, duration2.getDays());
        assertEquals(0, duration2.getSeconds());
    }

    @Test
    public void testDaysOnlyDuration() {
        ZonedDateTime start = ZonedDateTime.of(2022, 1, 31, 0, 0, 0, 0, ZONE_ID);
        ZonedDateTime end = start.plusDays(10);

        var duration = DateTimeCalculator.getDurationBetween(start.toInstant(), end.toInstant(), TIME_ZONE);

        assertEquals(0, duration.getMonths());
        assertEquals(10, duration.getDays());
        assertEquals(0, duration.getSeconds());
    }

    @Test
    public void testMixedDuration() {
        ZonedDateTime start = ZonedDateTime.of(2022, 1, 31, 0, 0, 0, 0, ZONE_ID);
        ZonedDateTime end = start.plusMonths(4).plusDays(10).plusSeconds(15000);

        var duration = DateTimeCalculator.getDurationBetween(start.toInstant(), end.toInstant(), TIME_ZONE);

        assertEquals(4, duration.getMonths());
        assertEquals(10, duration.getDays());
        assertEquals(15000, duration.getSeconds());
    }

    @Test
    public void testGetMonthStartWorksAsExpectedForZoneId() {
        ZonedDateTime start = ZonedDateTime.of(2022, 3, 31, 12, 15, 33, 0, ZONE_ID);
        Instant monthStart = DateTimeCalculator.getStartOfMonth(start.toInstant(), ZONE_ID);
        ZonedDateTime monthStartZoned = ZonedDateTime.ofInstant(monthStart, ZONE_ID);
        Assertions.assertThat(monthStartZoned.getMonth()).isEqualByComparingTo(Month.MARCH);
        Assertions.assertThat(monthStartZoned.getDayOfMonth()).isEqualTo(1);
        Assertions.assertThat(monthStartZoned.getHour()).isEqualTo(0);
        Assertions.assertThat(monthStartZoned.getMinute()).isEqualTo(0);
        Assertions.assertThat(monthStartZoned.getSecond()).isEqualTo(0);
    }

    @Test
    public void testGetMonthStartWorksAsExpectedForEastCoastZoneId() {
        // test for leap year for this time zone
        ZonedDateTime start = ZonedDateTime.of(2024, 2, 29, 12, 15, 33, 0, EAST_COAST_ZONE_ID);
        Instant monthStart = DateTimeCalculator.getStartOfMonth(start.toInstant(), EAST_COAST_ZONE_ID);
        ZonedDateTime monthStartZoned = ZonedDateTime.ofInstant(monthStart, EAST_COAST_ZONE_ID);
        Assertions.assertThat(monthStartZoned.getMonth()).isEqualByComparingTo(Month.FEBRUARY);
        Assertions.assertThat(monthStartZoned.getDayOfMonth()).isEqualTo(1);
        Assertions.assertThat(monthStartZoned.getHour()).isEqualTo(0);
        Assertions.assertThat(monthStartZoned.getMinute()).isEqualTo(0);
        Assertions.assertThat(monthStartZoned.getSecond()).isEqualTo(0);
    }

    @Test
    public void testPlusMonthsWorkingAsExpected() {
        // again check for leap year :)
        ZonedDateTime start = ZonedDateTime.of(2024, 1, 31, 23, 59, 59, 0, ZONE_ID);
        Instant plusMonth = DateTimeCalculator.plusMonths(ZONE_ID, start.toInstant(), 1);
        ZonedDateTime plusMonthZoned = ZonedDateTime.ofInstant(plusMonth, ZONE_ID);
        Assertions.assertThat(plusMonthZoned.getMonth()).isEqualByComparingTo(Month.FEBRUARY);
        Assertions.assertThat(plusMonthZoned.getDayOfMonth()).isEqualTo(29);
    }

    @Test
    public void testMinusMonthsWorkingAsExpected() {
        // again check for leap year :)
        ZonedDateTime start = ZonedDateTime.of(2024, 2, 29, 23, 59, 59, 0, ZONE_ID);
        Instant plusMonth = DateTimeCalculator.minusMonths(ZONE_ID, start.toInstant(), 3);
        ZonedDateTime plusMonthZoned = ZonedDateTime.ofInstant(plusMonth, ZONE_ID);
        Assertions.assertThat(plusMonthZoned.getMonth()).isEqualByComparingTo(Month.NOVEMBER);
        Assertions.assertThat(plusMonthZoned.getDayOfMonth()).isEqualTo(29);
    }

    @Test
    public void testPlusYearsWorkingAsExpected() {
        ZonedDateTime start = ZonedDateTime.of(2024, 2, 29, 23, 59, 59, 0, ZONE_ID);
        Instant plusYears = DateTimeCalculator.plusYears(ZONE_ID, start.toInstant(), 1);
        ZonedDateTime plusYearZoned = ZonedDateTime.ofInstant(plusYears, ZONE_ID);
        Assertions.assertThat(plusYearZoned.getMonth()).isEqualByComparingTo(Month.FEBRUARY);
        Assertions.assertThat(plusYearZoned.getDayOfMonth()).isEqualTo(28);
        Assertions.assertThat(plusYearZoned.getYear()).isEqualTo(2025);
    }

    @Test
    public void testDaysBetweenForSeconds() {
        ZonedDateTime start = ZonedDateTime.of(2022, 2, 28, 0, 0, 0, 0, ZONE_ID);
        ZonedDateTime end = ZonedDateTime.of(2022, 2, 28, 0, 0, 10, 0, ZONE_ID);
        var daysReturned = DateTimeCalculator.getDaysBetween(start.toInstant(), end.toInstant(), TIME_ZONE);
        Assertions.assertThat(daysReturned).isEqualTo(0);
    }

    // Daylight Saving Time begins on Sunday, March 13, 2022 at 2:00 A.M. On Saturday night,
    // clocks are set forward one hour (i.e., losing one hour) to “spring forward.”
    @Test
    public void testDaysBetweenForDaylightSpringForward() {
        ZonedDateTime start = ZonedDateTime.of(2022, 3, 12, 8, 0, 0, 0, ZONE_ID);
        ZonedDateTime end = ZonedDateTime.of(2022, 3, 14, 8, 30, 0, 0, ZONE_ID);
        var daysReturned = DateTimeCalculator.getDaysBetween(start.toInstant(), end.toInstant(), TIME_ZONE);
        var secondsElapsed = (end.toEpochSecond() - start.toEpochSecond());
        // even if number of hours are less than 48, there are 2 days as per clock
        Assertions.assertThat(secondsElapsed).isLessThan(48 * 3600);
        Assertions.assertThat(daysReturned).isEqualTo(2);
    }

    // Daylight Saving Time ends on Sunday, November 6, 2022, at 2:00 A.M.
    // On Saturday night, clocks are set back one hour (i.e., gaining one hour) to “fall back.”
    @Test
    public void testDaysBetweenForDaylightFallback() {
        ZonedDateTime start = ZonedDateTime.of(2022, 11, 5, 8, 0, 0, 0, ZONE_ID);
        ZonedDateTime end = ZonedDateTime.of(2022, 11, 7, 7, 30, 0, 0, ZONE_ID);
        var daysReturned = DateTimeCalculator.getDaysBetween(start.toInstant(), end.toInstant(), TIME_ZONE);
        var secondsElapsed = (end.toEpochSecond() - start.toEpochSecond());
        // even if number of hours are more than 48, only 1 day is completed as per clock
        Assertions.assertThat(secondsElapsed).isGreaterThan(48 * 3600);
        Assertions.assertThat(daysReturned).isEqualTo(1);
    }

    @Test
    public void testDaysBetweenForRandomDays() {
        var randomNumberGenerator = new RandomNumberGenerator();
        long daysAdded = randomNumberGenerator.generate(5); // random number from 1 to 99999
        ZonedDateTime start = ZonedDateTime.of(2022, 2, 28, 0, 0, 0, 0, ZONE_ID);
        ZonedDateTime end = start.plusDays(daysAdded);
        var daysReturned = DateTimeCalculator.getDaysBetween(start.toInstant(), end.toInstant(), TIME_ZONE);
        Assertions.assertThat(daysReturned).isEqualTo(daysAdded);
    }

    @Test
    public void testRepresentZonedTimeInJavaDate() {
        ZonedDateTime zonedDateTime = ZonedDateTime.of(2022, 3, 1, 10, 20, 30, 0, ZONE_ID);
        Date outputDate = DateTimeCalculator.representZonedTimeInJavaDate(zonedDateTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(outputDate);
        Assertions.assertThat(calendar.get(Calendar.YEAR)).isEqualTo(zonedDateTime.getYear());
        Assertions.assertThat(calendar.get(Calendar.MONTH)).isEqualTo(zonedDateTime.getMonth().getValue() - 1);
        Assertions.assertThat(calendar.get(Calendar.DAY_OF_MONTH)).isEqualTo(zonedDateTime.getDayOfMonth());
        Assertions.assertThat(calendar.get(Calendar.HOUR)).isEqualTo(zonedDateTime.getHour());
        Assertions.assertThat(calendar.get(Calendar.MINUTE)).isEqualTo(zonedDateTime.getMinute());
        Assertions.assertThat(calendar.get(Calendar.SECOND)).isEqualTo(zonedDateTime.getSecond());
    }

    @Test
    public void testNormalizedDaysBetweenForPartialDayBefore() {
        ZonedDateTime start = ZonedDateTime.of(2025, 3, 15, 0, 0, 0, 0, ZONE_ID);
        ZonedDateTime end = ZonedDateTime.of(2025, 3, 14, 12, 0, 0, 0, ZONE_ID);
        var daysReturned = DateTimeCalculator.getNormalizedDaysBetween(start.toInstant(), end.toInstant(), TIME_ZONE, ChronoUnit.DAYS);
        Assertions.assertThat(daysReturned).isEqualTo(-1);
    }

    @Test
    public void testNormalizedDaysBetweenForPartialDayAfter() {
        ZonedDateTime start = ZonedDateTime.of(2025, 3, 15, 0, 0, 0, 0, ZONE_ID);
        ZonedDateTime end = ZonedDateTime.of(2025, 3, 15, 12, 0, 0, 0, ZONE_ID);
        var daysReturned = DateTimeCalculator.getNormalizedDaysBetween(start.toInstant(), end.toInstant(), TIME_ZONE, ChronoUnit.DAYS);
        Assertions.assertThat(daysReturned).isEqualTo(0);
    }
}
