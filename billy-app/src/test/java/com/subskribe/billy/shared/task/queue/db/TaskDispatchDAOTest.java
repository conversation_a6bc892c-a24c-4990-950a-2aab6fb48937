package com.subskribe.billy.shared.task.queue.db;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.shared.task.queue.model.DefaultTaskOrder;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs;
import com.subskribe.billy.test.WithDb;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class TaskDispatchDAOTest extends WithDb {

    private static final String TENANT_ID = "27b59431-0cc7-4c87-883b-be345e9bd525";

    private TaskDispatchDAO taskDispatchDAO;

    @BeforeAll
    public void setUp() throws IOException {
        taskDispatchDAO = new TaskDispatchDAO(new QueuedTaskMapper());
    }

    @Test
    void testInsertTask() {
        QueuedTask queuedTask = ImmutableQueuedTask.builder()
            .from(QueuedTaskStubs.buildTask())
            .tenantId(TENANT_ID)
            .taskId(UUID.randomUUID().toString())
            .build();

        QueuedTask result = dslContextProvider
            .get(TENANT_ID)
            .transactionResult(configuration -> taskDispatchDAO.insertTask(configuration, queuedTask));

        assertThat(result).usingRecursiveComparison().ignoringFields("createdOn").isEqualTo(queuedTask);
    }

    @Test
    void updateTaskOrder() {
        QueuedTask queuedTask = ImmutableQueuedTask.builder()
            .from(QueuedTaskStubs.buildTask())
            .tenantId(TENANT_ID)
            .taskId(UUID.randomUUID().toString())
            .build();
        QueuedTask expectedTask = ImmutableQueuedTask.builder()
            .from(queuedTask)
            .tenantId(TENANT_ID)
            .taskId(queuedTask.getTaskId())
            .taskOrder(new DefaultTaskOrder("TestPartitionKey", 1111L))
            .build();

        QueuedTask result = dslContextProvider
            .get(TENANT_ID)
            .transactionResult(configuration -> {
                QueuedTask savedTask = taskDispatchDAO.insertTask(configuration, queuedTask);
                return taskDispatchDAO.updateTaskOrder(configuration, savedTask, new DefaultTaskOrder("TestPartitionKey", 1111L));
            });

        assertThat(result).usingRecursiveComparison().ignoringFields("createdOn").isEqualTo(expectedTask);
    }
}
