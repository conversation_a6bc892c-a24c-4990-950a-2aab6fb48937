package com.subskribe.billy.shared.config;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.configuration.BillyConfigurationValidator;
import com.subskribe.billy.foreignexchange.model.FxProviderType;
import com.subskribe.billy.tenant.config.TenantScopedConfigProvider;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.regions.Region;

@SuppressWarnings("ALL")
public class BillyConfigurationTest extends ConfigurationTestBase {

    private static final String E2E_TENANT_ID = "7911efdc-331d-4c7f-95d4-152fadd06f5a";
    private static final String LOCAL_CONFIG = "config-local.conf";
    private static final String CI_CONFIG = "config-ci.conf";
    private static final String DEV2_CONFIG = "config-dev2.conf";
    private static final String DEVOPS1_CONFIG = "config-devops1.conf";
    private static final String SANDBOX_CONFIG = "config-sandbox.conf";
    private static final String PROD_CONFIG = "config-prod.conf";
    private static final String QA_01_CONFIG = "config-qa01.conf";

    @Test
    public void testCIConfig() {
        BillyConfiguration ciConfig = TypeSafeDynamicConfigLoader.load(CI_CONFIG);

        verifyDatabase(ciConfig, "*******************************************", 25, 25);
        verifyReadOnlyDatabase(ciConfig, "*******************************************", 5);

        verifyDynamoDb(ciConfig, Region.US_EAST_2, "http://localstack:4566");

        verifyApprovalFlowConfiguration(ciConfig, true, false, true, true, true, true, true, true, "<EMAIL>");

        verifyDunningConfiguration(ciConfig, false, false, 1, "<EMAIL>", false);

        Assertions.assertThat(ciConfig.getDlqConfiguration().getEnabled()).isFalse();

        Assertions.assertThat(ciConfig.getQuartzUsageAggregationConfiguration().getEnabled()).isFalse();

        verifyBulkInvoiceRunQuartzConfiguration(ciConfig, false, 600);

        Assertions.assertThat(ciConfig.getUsageAggregationConfiguration().getRawUsageAggregationBatchSize()).isEqualTo(1000);
        // no NTP drift for local and e2e testing
        Assertions.assertThat(ciConfig.getUsageAggregationConfiguration().getNtpDriftFactor()).isEqualTo(Duration.parse("PT0S"));

        verifyLocalDocument(
            ciConfig,
            Region.US_EAST_2,
            "subskribe-test-us-east-2-order-documents",
            "subskribe-test-us-east-2-invoice-documents",
            "subskribe-test-us-east-2-credit-memo-documents",
            "subskribe-test-us-east-2-import-documents",
            "subskribe-test-us-east-2-approval-matrix-imports",
            "http://gotenberg:3000",
            "http://localstack:4566",
            "defaultQuote.mustache",
            "default.css",
            "defaultQuoteContainer.mustache",
            "defaultInvoice.mustache",
            "subskribe-test-us-east-2-crm-field-mapping",
            "subskribe-test-us-east-2-intelligent-sales-room",
            "subskribe-test-us-east-2-webhook-payload"
        );

        verifySecretsManager(
            ciConfig,
            Region.US_WEST_2,
            "LOCAL_CI_DATABASE_SECRET",
            "dataDogApiKey",
            "RLS_ENCRYPTION_KEY_LOCAL_CI",
            "OPEN_AI_CLIENT_API_KEY",
            "PANDA_DOC_API_KEY",
            "TAX_JAR_API_KEY",
            "FLATFILE_API_KEY"
        );

        verifyFlyway(ciConfig, List.of("db/migration", "db/demo_tenant_migration", "db/e2e_tenant_migration"));

        Assertions.assertThat(ciConfig.getQuartzConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(ciConfig.getQuartzConfiguration().isClustered()).isFalse();

        Assertions.assertThat(ciConfig.getQuartzQueueConfiguration().getQueueUrl()).isEqualTo(
            "https://sqs.us-east-2.amazonaws.com/085981900979/CI-Quartz-Queue.fifo"
        );

        verifyNonLocalSns(ciConfig, Region.US_EAST_2);

        verifyIdempotency(ciConfig, "LOCAL_IDEMPOTENCY_TABLE");

        Assertions.assertThat(ciConfig.getAvalaraConfiguration().isCommitTaxTransactions()).isFalse();

        verifySesConfig(ciConfig, true, true, true, "http://localstack:4566");

        Assertions.assertThat(ciConfig.getDocumentConfiguration().isEnabled()).isTrue();

        Assertions.assertThat(ciConfig.getSiteUrl()).isEqualTo("http://localdev.subskribe.net:3000");

        verifyStripe(ciConfig, "http://localdev.subskribe.net:3000/callback/stripe", true);

        verifyIntervalBasedPaymentJobConfiguration(ciConfig, 0, 30);

        verifyAuditLogConfiguration(ciConfig, true, 15, ChronoUnit.DAYS, false);

        Assertions.assertThat(ciConfig.getQuartzPaymentReconciliationJobConfiguration().getEnabled()).isTrue();

        Assertions.assertThat(ciConfig.getApiUrl()).isEqualTo("http://localdev.subskribe.net:3000/api/backend");

        verifyIpBasedRateLimitingConfiguration(ciConfig, false);

        verifyInvoiceEmailConfiguration(ciConfig, true, true);

        verifyCreditMemoEmailConfiguration(ciConfig, true, true, "<EMAIL>");

        verifyMemcachedConfiguration(ciConfig, "memcached");

        verifyAttachmentsConfiguration(ciConfig);

        verifyMetricsUpdaterQuartzJobConfiguration(ciConfig, true, 10);

        Assertions.assertThat(ciConfig.getFixedAmountDiscountConfiguration().isEnabled()).isTrue();

        validateAwsAppConfigSettings(ciConfig, Region.US_WEST_2);

        verifyRequestLoggingConfiguration(ciConfig, true, "access-audit-log");
        verifyLookerConfig(
            ciConfig,
            "subskribe.cloud.looker.com",
            "dev2",
            "dev2",
            "_ci",
            "dev2-shared",
            "dev2-private",
            true,
            "subskribe-reporting-config-ci"
        );
        verifyCustomPaymentTypeConfiguration(ciConfig);

        Assertions.assertThat(ciConfig.getInternalLoginConfiguration().isEnabled()).isTrue();
        Assertions.assertThat(ciConfig.getInternalLoginConfiguration().getGoogleClientId()).isNotBlank();

        Assertions.assertThat(ciConfig.getQuartzApiKeyGarbageCollectorJobConfiguration().getEnabled()).isFalse();

        Assertions.assertThat(ciConfig.getEmailLoginLinkConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(ciConfig.getEmailLoginLinkConfiguration().getFromEmailAddress()).isEqualTo("<EMAIL>");

        verifyDosFilterConfiguration(ciConfig, false, 600, 10000);

        verifyQueryTimeoutConfiguration(ciConfig, true, 60);
        verifyNotificationConfiguration(ciConfig, false, true, 10, "unconfigured", "http://localhost:8080/", true, "<EMAIL>");

        verifyConfigCanBeSerialized(ciConfig);

        // TODO: event pump for local will be enabled once CI client config is figured out
        verifyEventPumpConfig(ciConfig, false, 250);

        Assertions.assertThat(ciConfig.getTenantNameCacheConfiguration().getEnabled()).isFalse();

        Assertions.assertThat(ciConfig.getCrmConfiguration().getSyncToCrm()).isTrue();

        Assertions.assertThat(ciConfig.getFxConfiguration().getFxProviderType()).isEqualTo(FxProviderType.TEST_RANDOM);

        verifySearchIndexerConfiguration(ciConfig, false);

        verifyLogHeartbeatConfiguration(ciConfig, false, 30);

        Assertions.assertThat(ciConfig.getTenantSealProtectionConfiguration().getEnabled()).isTrue();

        verifyRateLimitConfiguration(ciConfig, false, 1000);

        verifyKinesisConfiguration(ciConfig, true, "http://localstack:4566", Region.US_EAST_2, true, true);

        verifyTaskQueueConfiguration(
            ciConfig,
            true,
            true,
            60,
            15,
            250,
            Region.US_EAST_2,
            "http://localstack:4566/queue/us-east-2/000000000000/billy-task-queue"
        );

        Assertions.assertThat(ciConfig.getLambdaConfiguration().isLocal()).isTrue();
        Assertions.assertThat(ciConfig.getLambdaConfiguration().getEndpointOverride()).isEqualTo("http://localstack:4566");
        Assertions.assertThat(ciConfig.getLambdaConfiguration().getFunctionNamePrefix()).isEqualTo("local");

        verifyBulkRevenueRecognitionQuartzConfiguration(ciConfig, true, 1);

        verifyElectronicSignatureConfiguration(ciConfig, false);
    }

    @Test
    public void testDevops1Config() {
        BillyConfiguration devops1Config = TypeSafeDynamicConfigLoader.load(DEVOPS1_CONFIG);

        verifyNonLocalCognito(devops1Config, "devops1", Region.CA_CENTRAL_1, "085981900979");

        verifyDynamoDb(devops1Config, Region.CA_CENTRAL_1, UNCONFIGURED);

        verifyApprovalFlowConfiguration(devops1Config, true, true, true, true, true, true, true, true, "<EMAIL>");

        verifyUsageAggregationQuartzConfiguration(devops1Config);

        verifyBulkInvoiceRunQuartzConfiguration(devops1Config, true, 600);

        verifyUsageAggregationConfiguration(devops1Config);

        verifyDatabase(devops1Config, "**********************************************************************************************", 15, 50);
        verifyReadOnlyDatabase(
            devops1Config,
            "*************************************************************************************************",
            10
        );

        verifyNonLocalDlq(devops1Config, Region.CA_CENTRAL_1, "https://sqs.ca-central-1.amazonaws.com/085981900979/devops1-sqs-billy_DLQ");

        verifyNonLocalDocument(
            devops1Config,
            Region.CA_CENTRAL_1,
            "billy-devops1-orders-bucket",
            "billy-devops1-invoices-bucket",
            "billy-devops1-credit-memos-bucket",
            "billy-devops1-import-documents",
            "billy-devops1-approval-matrix-imports",
            "http://gotenberg7:3000",
            "billy-devops1-crm-field-mapping",
            "billy-devops1-intelligent-sales-room",
            "billy-devops1-webhook-payload"
        );

        verifyNonLocalElasticSearch(
            devops1Config,
            Region.CA_CENTRAL_1,
            "https://vpc-devops1-billy-search-domain-c4erdcltx5g5m4vgnk7zhzh4di.ca-central-1.es.amazonaws.com"
        );

        verifyFlyway(devops1Config, List.of("db/migration", "db/tenant_bootstrap_devops1"));

        verifyCsvMetrics(devops1Config);

        verifyNonLocalNotification(
            devops1Config,
            "https://devops1.subskribe.net/",
            "arn:aws:sns:ca-central-1:085981900979:devops1-sns-billy_TenantNotifications"
        );

        verifyNonLocalQuartz(
            devops1Config,
            false,
            "https://sqs.ca-central-1.amazonaws.com/085981900979/devops1-sqs-billy_Quartz.fifo",
            Region.CA_CENTRAL_1,
            false
        );

        verifyQuartzInvoiceGenerator(devops1Config, 0, 5, 0);

        verifySalesForceConfig(devops1Config, "https://devops1.subskribe.net/api/backend/sfdc");

        verifyNonLocalSns(devops1Config, Region.CA_CENTRAL_1);

        verifyIdempotency(devops1Config, "DEVOPS1_IDEMPOTENCY_TABLE");

        verifySecretsManager(
            devops1Config,
            Region.CA_CENTRAL_1,
            "devops1dbclusterSecret61CCA-8RWxFibXJXIw",
            "dataDogApiKey",
            "RLS_ENCRYPTION_KEY_DEVOPS1",
            "OPEN_AI_CLIENT_API_KEY",
            "PANDA_DOC_API_KEY",
            "TAX_JAR_API_KEY",
            "FLATFILE_API_KEY"
        );

        verifyAvalara(devops1Config);

        Assertions.assertThat(devops1Config.getDocumentConfiguration().isEnabled()).isTrue();

        Assertions.assertThat(devops1Config.getSiteUrl()).isEqualTo("https://devops1.subskribe.net");

        verifyStripe(devops1Config, "https://devops1.subskribe.net/callback/stripe", false);

        verifyDefaultPaymentJobConfiguration(devops1Config, false);

        verifyAuditLogConfiguration(devops1Config, true, 15, ChronoUnit.DAYS, false);

        verifyIntervalBasedPaymentReconciliationJobConfiguration(devops1Config, 60, 0, false);

        Assertions.assertThat(devops1Config.getApiUrl()).isEqualTo("https://devops1.subskribe.net/api/backend");

        verifyIpBasedRateLimitingConfiguration(devops1Config, true);

        verifyInvoiceEmailConfiguration(devops1Config, true, true);

        verifyCreditMemoEmailConfiguration(devops1Config, true, true, "<EMAIL>");

        verifyMemcachedConfiguration(devops1Config, "devops1-billy-memcached.ezegpa.cfg.cac1.cache.amazonaws.com");

        verifyMetricsUpdaterQuartzJobConfiguration(devops1Config, true, 10);

        Assertions.assertThat(devops1Config.getFixedAmountDiscountConfiguration().isEnabled()).isFalse();

        validateAwsAppConfigSettings(devops1Config, Region.CA_CENTRAL_1);

        verifyRevenueRecognitionQuartzJobConfiguration(devops1Config, true, 300);

        verifyRequestLoggingConfiguration(devops1Config, true, "billy-devops1-access-audit-log");

        verifyEventPumpConfig(devops1Config, true, 250);

        verifyLookerConfig(
            devops1Config,
            "subskribe.cloud.looker.com",
            "devops1",
            "devops1",
            "_devops1",
            "devops1-shared",
            "devops1-private",
            true,
            "subskribe-reporting-config-devops1"
        );

        verifyEmailNotificationQueueConfiguration(
            devops1Config,
            false,
            Region.CA_CENTRAL_1,
            "https://sqs.ca-central-1.amazonaws.com/085981900979/devops1-email-notification-queue"
        );

        verifyCustomPaymentTypeConfiguration(devops1Config);

        Assertions.assertThat(devops1Config.getInternalLoginConfiguration().isEnabled()).isTrue();
        Assertions.assertThat(devops1Config.getInternalLoginConfiguration().getGoogleClientId()).isNotBlank();

        Assertions.assertThat(devops1Config.getQuartzApiKeyGarbageCollectorJobConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(devops1Config.getQuartzApiKeyGarbageCollectorJobConfiguration().getIntervalInMinutes()).isEqualTo(15);

        Assertions.assertThat(devops1Config.getEmailLoginLinkConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(devops1Config.getEmailLoginLinkConfiguration().getFromEmailAddress()).isEqualTo("<EMAIL>");

        verifyDosFilterConfiguration(devops1Config, true, 600, 10000);

        verifyQueryTimeoutConfiguration(devops1Config, true, 60);

        verifyNotificationConfiguration(
            devops1Config,
            true,
            true,
            10,
            "arn:aws:sns:ca-central-1:085981900979:devops1-sns-billy_TenantNotifications",
            "https://devops1.subskribe.net/",
            false,
            "<EMAIL>"
        );

        verifyConfigCanBeSerialized(devops1Config);

        Assertions.assertThat(devops1Config.getTenantNameCacheConfiguration().getEnabled()).isTrue();

        Assertions.assertThat(devops1Config.getCrmConfiguration().getSyncToCrm()).isTrue();

        Assertions.assertThat(devops1Config.getFxConfiguration().getFxProviderType()).isEqualTo(FxProviderType.SYSTEM);

        verifySearchIndexerConfiguration(devops1Config, true);

        verifyLogHeartbeatConfiguration(devops1Config, true, 30);

        Assertions.assertThat(devops1Config.getTenantSealProtectionConfiguration().getEnabled()).isFalse();

        verifyRateLimitConfiguration(devops1Config, true, 2000);

        verifyKinesisConfiguration(devops1Config, false, null, Region.CA_CENTRAL_1, false, false);

        verifyTaskQueueConfiguration(
            devops1Config,
            true,
            true,
            60,
            15,
            250,
            Region.CA_CENTRAL_1,
            "https://sqs.ca-central-1.amazonaws.com/085981900979/devops1-billy-queued-task"
        );

        verifySesConfig(devops1Config, true, true, false, null);

        Assertions.assertThat(devops1Config.getLambdaConfiguration().isLocal()).isFalse();
        Assertions.assertThat(devops1Config.getLambdaConfiguration().getRegion()).isEqualTo(Region.CA_CENTRAL_1);
        Assertions.assertThat(devops1Config.getLambdaConfiguration().getEndpointOverride()).isNull();
        Assertions.assertThat(devops1Config.getLambdaConfiguration().getFunctionNamePrefix()).isEqualTo("devops1");

        verifyBulkRevenueRecognitionQuartzConfiguration(devops1Config, true, 1);
        verifyElectronicSignatureConfiguration(devops1Config, false);
    }

    @Test
    public void testDev2Config() {
        BillyConfiguration dev2Config = TypeSafeDynamicConfigLoader.load(DEV2_CONFIG);

        verifyNonLocalCognito(dev2Config, "dev2", Region.US_WEST_2, "085981900979");

        verifyApprovalFlowConfiguration(dev2Config, true, true, true, true, true, true, true, true, "<EMAIL>");

        verifyDunningConfiguration(dev2Config, true, false, 30, "<EMAIL>", true);

        verifyDynamoDb(dev2Config, Region.US_WEST_2, UNCONFIGURED);

        verifyEnabledUsageAggregationConfiguration(dev2Config);

        verifyBulkInvoiceRunQuartzConfiguration(dev2Config, true, 120);

        verifyDatabase(dev2Config, "********************************************************************************************", 15, 50);
        verifyReadOnlyDatabase(dev2Config, "***********************************************************************************************", 10);

        verifyNonLocalDlq(dev2Config, Region.US_WEST_2, "https://sqs.us-west-2.amazonaws.com/085981900979/dev-sqs-billy_DLQ");

        verifyNonLocalDocument(
            dev2Config,
            Region.US_WEST_2,
            "billy-dev-orders-bucket",
            "billy-dev-invoices-bucket",
            "billy-dev-credit-memos-bucket",
            "billy-dev-import-documents",
            "billy-dev-approval-matrix-imports",
            "http://gotenberg7:3000",
            "billy-dev-crm-field-mapping",
            "billy-dev-intelligent-sales-room",
            "billy-dev-webhook-payload"
        );

        verifyNonLocalElasticSearch(
            dev2Config,
            Region.US_WEST_2,
            "https://vpc-dev-billy-search-domain-3l3v6repi2dobus4ofsvuwcqwe.us-west-2.es.amazonaws.com"
        );

        verifyFlyway(dev2Config, List.of("db/migration", "db/tenant_bootstrap_dev2"));

        verifyDataDogMetrics(dev2Config, 5);

        verifyNonLocalNotification(dev2Config, "https://dev2.subskribe.net/", "arn:aws:sns:us-west-2:085981900979:dev-sns-billy_TenantNotifications");

        verifyNonLocalQuartz(dev2Config, true, "https://sqs.us-west-2.amazonaws.com/085981900979/dev-sqs-billy_Quartz.fifo", Region.US_WEST_2, false);

        verifyQuartzInvoiceGenerator(dev2Config, 0, 5, 0);

        verifySalesForceConfig(dev2Config, "https://dev2.subskribe.net/api/backend/sfdc");

        verifyNonLocalSns(dev2Config, Region.US_WEST_2);

        verifyIdempotency(dev2Config, "DEV2_IDEMPOTENCY_TABLE");

        verifySecretsManager(
            dev2Config,
            Region.US_EAST_1,
            "devdbclusterSecretE9ED1FF3-uCXckael3Eei",
            "dataDogApiKey",
            "RLS_ENCRYPTION_KEY_DEV2",
            "OPEN_AI_CLIENT_API_KEY",
            "PANDA_DOC_API_KEY",
            "TAX_JAR_API_KEY",
            "FLATFILE_API_KEY"
        );

        verifyAvalara(dev2Config);

        Assertions.assertThat(dev2Config.getDocumentConfiguration().isEnabled()).isTrue();

        Assertions.assertThat(dev2Config.getSiteUrl()).isEqualTo("https://dev2.subskribe.net");

        verifyStripe(dev2Config, "https://dev2.subskribe.net/callback/stripe", true);

        verifyIntervalBasedPaymentJobConfiguration(dev2Config, 1, 0);

        verifyAuditLogConfiguration(dev2Config, true, 2, ChronoUnit.WEEKS, false);

        verifyIntervalBasedPaymentReconciliationJobConfiguration(dev2Config, 1, 0, true);

        Assertions.assertThat(dev2Config.getApiUrl()).isEqualTo("https://dev2.subskribe.net/api/backend");

        verifyIpBasedRateLimitingConfiguration(dev2Config, true);

        verifyInvoiceEmailConfiguration(dev2Config, true, true);

        verifyCreditMemoEmailConfiguration(dev2Config, true, true, "<EMAIL>");

        verifyMemcachedConfiguration(dev2Config, "dev-billy-memcached.us8bta.cfg.usw2.cache.amazonaws.com");

        verifyMetricsUpdaterQuartzJobConfiguration(dev2Config, true, 10);

        Assertions.assertThat(dev2Config.getFixedAmountDiscountConfiguration().isEnabled()).isTrue();

        validateAwsAppConfigSettings(dev2Config, Region.US_WEST_2);

        verifyRevenueRecognitionQuartzJobConfiguration(dev2Config, true, 300);

        verifyRequestLoggingConfiguration(dev2Config, true, "billy-dev-access-audit-log");

        verifyEventPumpConfig(dev2Config, true, 250);

        verifyLookerConfig(
            dev2Config,
            "subskribe.cloud.looker.com",
            "dev2",
            "dev2",
            "_dev2",
            "dev2-shared",
            "dev2-private",
            true,
            "subskribe-reporting-config-dev"
        );

        verifyEmailNotificationQueueConfiguration(
            dev2Config,
            true,
            Region.US_WEST_2,
            "https://sqs.us-west-2.amazonaws.com/085981900979/dev-email-notification-queue"
        );
        verifyCustomPaymentTypeConfiguration(dev2Config);

        Assertions.assertThat(dev2Config.getInternalLoginConfiguration().isEnabled()).isTrue();
        Assertions.assertThat(dev2Config.getInternalLoginConfiguration().getGoogleClientId()).isNotBlank();

        Assertions.assertThat(dev2Config.getQuartzApiKeyGarbageCollectorJobConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(dev2Config.getQuartzApiKeyGarbageCollectorJobConfiguration().getIntervalInMinutes()).isEqualTo(15);

        Assertions.assertThat(dev2Config.getEmailLoginLinkConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(dev2Config.getEmailLoginLinkConfiguration().getFromEmailAddress()).isEqualTo("<EMAIL>");

        verifyDosFilterConfiguration(dev2Config, true, 600, 10000);

        verifyQueryTimeoutConfiguration(dev2Config, true, 60);

        verifyNotificationConfiguration(
            dev2Config,
            true,
            true,
            10,
            "arn:aws:sns:us-west-2:085981900979:dev-sns-billy_TenantNotifications",
            "https://dev2.subskribe.net/",
            false,
            "<EMAIL>"
        );

        verifyConfigCanBeSerialized(dev2Config);

        Assertions.assertThat(dev2Config.getTenantNameCacheConfiguration().getEnabled()).isTrue();

        Assertions.assertThat(dev2Config.getCrmConfiguration().getSyncToCrm()).isTrue();

        Assertions.assertThat(dev2Config.getFxConfiguration().getFxProviderType()).isEqualTo(FxProviderType.SYSTEM);

        verifySearchIndexerConfiguration(dev2Config, true);

        verifyLogHeartbeatConfiguration(dev2Config, true, 30);

        Assertions.assertThat(dev2Config.getTenantSealProtectionConfiguration().getEnabled()).isFalse();

        verifyRateLimitConfiguration(dev2Config, true, 2000);

        verifyKinesisConfiguration(dev2Config, false, null, Region.US_WEST_2, false, false);

        verifyTaskQueueConfiguration(
            dev2Config,
            true,
            true,
            60,
            15,
            250,
            Region.US_WEST_2,
            "https://sqs.us-west-2.amazonaws.com/085981900979/dev-billy-queued-task"
        );

        verifySesConfig(dev2Config, true, true, false, null);

        Assertions.assertThat(dev2Config.getOpenAIConfiguration().getAssistantId()).isEqualTo("asst_T0yH3e6JJv7N2taMwcq01gdM");

        Assertions.assertThat(dev2Config.getLambdaConfiguration().isLocal()).isFalse();
        Assertions.assertThat(dev2Config.getLambdaConfiguration().getRegion()).isEqualTo(Region.US_WEST_2);
        Assertions.assertThat(dev2Config.getLambdaConfiguration().getEndpointOverride()).isNull();
        Assertions.assertThat(dev2Config.getLambdaConfiguration().getFunctionNamePrefix()).isEqualTo("dev2");

        verifyBulkRevenueRecognitionQuartzConfiguration(dev2Config, true, 1);
        verifyElectronicSignatureConfiguration(dev2Config, false);
    }

    @Test
    public void testQa01Config() {
        BillyConfiguration qa01Config = TypeSafeDynamicConfigLoader.load(QA_01_CONFIG);

        verifyNonLocalCognito(qa01Config, "qa01", Region.US_EAST_2, "542672998005");

        verifyApprovalFlowConfiguration(qa01Config, true, true, true, true, true, true, true, true, "<EMAIL>");

        verifyDunningConfiguration(qa01Config, true, true, 30, "<EMAIL>", true);

        verifyDynamoDb(qa01Config, Region.US_EAST_2, UNCONFIGURED);

        verifyUsageAggregationQuartzConfiguration(qa01Config);

        verifyUsageAggregationConfiguration(qa01Config);

        verifyBulkInvoiceRunQuartzConfiguration(qa01Config, true, 120);

        verifyDatabase(qa01Config, "*********************************************************************************************", 15, 50);
        verifyReadOnlyDatabase(qa01Config, "************************************************************************************************", 10);

        verifyNonLocalDlq(qa01Config, Region.US_EAST_2, "https://sqs.us-east-2.amazonaws.com/542672998005/qa01-sqs-billy_DLQ");

        verifyNonLocalDocument(
            qa01Config,
            Region.US_EAST_2,
            "billy-qa01-orders-bucket",
            "billy-qa01-invoices-bucket",
            "billy-qa01-credit-memos-bucket",
            "billy-qa01-import-documents",
            "billy-qa01-approval-matrix-imports",
            "http://gotenberg7:3000",
            "billy-qa01-crm-field-mapping",
            "billy-qa01-intelligent-sales-room",
            "billy-qa01-webhook-payload"
        );

        verifyNonLocalElasticSearch(
            qa01Config,
            Region.US_EAST_2,
            "https://vpc-qa01-billy-search-domain-2wyighzi2vdpshralvvocsyfxm.us-east-2.es.amazonaws.com"
        );

        verifyFlyway(qa01Config, List.of("db/migration", "db/tenant_bootstrap_qa01"));

        verifyDataDogMetrics(qa01Config, 5);

        verifyNonLocalNotification(
            qa01Config,
            "https://qa01.subskribe.net/",
            "arn:aws:sns:us-east-2:542672998005:qa01-sns-billy_TenantNotifications"
        );

        verifyNonLocalQuartz(
            qa01Config,
            true,
            "https://sqs.us-east-2.amazonaws.com/542672998005/qa01-sqs-billy_Quartz.fifo",
            Region.US_EAST_2,
            false
        );

        verifyQuartzInvoiceGenerator(qa01Config, 0, 5, 0);

        verifySalesForceConfig(qa01Config, "https://qa01.subskribe.net/api/backend/sfdc");

        verifyNonLocalSns(qa01Config, Region.US_EAST_2);

        verifyIdempotency(qa01Config, "QA01_IDEMPOTENCY_TABLE");

        verifySecretsManager(
            qa01Config,
            Region.US_EAST_2,
            "qa01dbclusterSecret9EF5BE03-1pxSuA1p31SX",
            "qa01/dataDogApiKey",
            "RLS_ENCRYPTION_KEY_QA",
            "OPEN_AI_CLIENT_API_KEY",
            "PANDA_DOC_API_KEY",
            "TAX_JAR_API_KEY",
            "FLATFILE_API_KEY"
        );

        verifyAvalara(qa01Config);

        Assertions.assertThat(qa01Config.getDocumentConfiguration().isEnabled()).isTrue();

        Assertions.assertThat(qa01Config.getSiteUrl()).isEqualTo("https://qa01.subskribe.net");

        verifyStripe(qa01Config, "https://qa01.subskribe.net/callback/stripe", true);

        Assertions.assertThat(qa01Config.getQuartzPaymentJobConfiguration().getEnabled()).isTrue();

        verifyAuditLogConfiguration(qa01Config, true, 15, ChronoUnit.DAYS, false);

        Assertions.assertThat(qa01Config.getQuartzPaymentReconciliationJobConfiguration().getEnabled()).isTrue();

        Assertions.assertThat(qa01Config.getApiUrl()).isEqualTo("https://qa01.subskribe.net/api/backend");

        verifyIpBasedRateLimitingConfiguration(qa01Config, true);

        verifyInvoiceEmailConfiguration(qa01Config, true, true);

        verifyCreditMemoEmailConfiguration(qa01Config, true, true, "<EMAIL>");

        verifyMemcachedConfiguration(qa01Config, "qa01-billy-memcached.gppnrg.cfg.use2.cache.amazonaws.com");

        verifyMetricsUpdaterQuartzJobConfiguration(qa01Config, true, 10);

        Assertions.assertThat(qa01Config.getFixedAmountDiscountConfiguration().isEnabled()).isFalse();

        validateAwsAppConfigSettings(qa01Config, Region.US_EAST_2);

        verifyRevenueRecognitionQuartzJobConfiguration(qa01Config, true, 300);

        verifyRequestLoggingConfiguration(qa01Config, true, "billy-qa01-access-audit-log");

        verifyLookerConfig(
            qa01Config,
            "subskribe.cloud.looker.com",
            "qa01",
            "qa01",
            "_qa01",
            "qa01-shared",
            "qa01-private",
            true,
            "subskribe-reporting-config-qa01"
        );

        verifyEventPumpConfig(qa01Config, true, 250);

        verifyEmailNotificationQueueConfiguration(
            qa01Config,
            true,
            Region.US_EAST_2,
            "https://sqs.us-east-2.amazonaws.com/542672998005/qa01-email-notification-queue"
        );
        verifyCustomPaymentTypeConfiguration(qa01Config);

        Assertions.assertThat(qa01Config.getInternalLoginConfiguration().isEnabled()).isTrue();
        Assertions.assertThat(qa01Config.getInternalLoginConfiguration().getGoogleClientId()).isNotBlank();

        Assertions.assertThat(qa01Config.getQuartzApiKeyGarbageCollectorJobConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(qa01Config.getQuartzApiKeyGarbageCollectorJobConfiguration().getIntervalInMinutes()).isEqualTo(15);

        Assertions.assertThat(qa01Config.getEmailLoginLinkConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(qa01Config.getEmailLoginLinkConfiguration().getFromEmailAddress()).isEqualTo("<EMAIL>");

        verifyDosFilterConfiguration(qa01Config, true, 600, 10000);

        verifyQueryTimeoutConfiguration(qa01Config, true, 60);

        verifyNotificationConfiguration(
            qa01Config,
            true,
            true,
            10,
            "arn:aws:sns:us-east-2:542672998005:qa01-sns-billy_TenantNotifications",
            "https://qa01.subskribe.net/",
            false,
            "<EMAIL>"
        );

        verifyConfigCanBeSerialized(qa01Config);

        Assertions.assertThat(qa01Config.getTenantNameCacheConfiguration().getEnabled()).isTrue();

        Assertions.assertThat(qa01Config.getCrmConfiguration().getSyncToCrm()).isTrue();

        Assertions.assertThat(qa01Config.getFxConfiguration().getFxProviderType()).isEqualTo(FxProviderType.SYSTEM);

        verifySearchIndexerConfiguration(qa01Config, true);

        verifyLogHeartbeatConfiguration(qa01Config, true, 30);

        Assertions.assertThat(qa01Config.getTenantSealProtectionConfiguration().getEnabled()).isFalse();

        verifyRateLimitConfiguration(qa01Config, true, 1000);

        verifyKinesisConfiguration(qa01Config, false, null, Region.US_EAST_2, false, false);

        verifyTaskQueueConfiguration(
            qa01Config,
            true,
            true,
            60,
            15,
            250,
            Region.US_EAST_2,
            "https://sqs.us-east-2.amazonaws.com/542672998005/qa01-billy-queued-task"
        );

        verifySesConfig(qa01Config, true, true, false, null);

        Assertions.assertThat(qa01Config.getOpenAIConfiguration().getAssistantId()).isEqualTo("asst_ycuZId2z6a9p2DL9cPsiRLBj");

        Assertions.assertThat(qa01Config.getLambdaConfiguration().isLocal()).isFalse();
        Assertions.assertThat(qa01Config.getLambdaConfiguration().getRegion()).isEqualTo(Region.US_EAST_2);
        Assertions.assertThat(qa01Config.getLambdaConfiguration().getEndpointOverride()).isNull();
        Assertions.assertThat(qa01Config.getLambdaConfiguration().getFunctionNamePrefix()).isEqualTo("qa01");

        verifyBulkRevenueRecognitionQuartzConfiguration(qa01Config, true, 1);
        verifyElectronicSignatureConfiguration(qa01Config, false);
    }

    @Test
    public void testSandboxConfig() {
        BillyConfiguration sandboxConfig = TypeSafeDynamicConfigLoader.load(SANDBOX_CONFIG);

        verifyNonLocalCognito(sandboxConfig, "sandbox", Region.US_EAST_2, "085981900979");

        verifyApprovalFlowConfiguration(sandboxConfig, true, true, false, true, true, true, true, true, "<EMAIL>");

        verifyDunningConfiguration(sandboxConfig, true, true, 30, "<EMAIL>", true);

        verifyDynamoDb(sandboxConfig, Region.US_EAST_2, UNCONFIGURED);

        verifyUsageAggregationQuartzConfiguration(sandboxConfig);

        verifyUsageAggregationConfiguration(sandboxConfig);

        verifyBulkInvoiceRunQuartzConfiguration(sandboxConfig, true, 180);

        verifyDatabase(sandboxConfig, "************************************************************************************************", 15, 50);
        verifyReadOnlyDatabase(
            sandboxConfig,
            "***************************************************************************************************",
            10
        );

        verifyNonLocalDlq(sandboxConfig, Region.US_EAST_2, "https://sqs.us-east-2.amazonaws.com/085981900979/sandbox-sqs-billy_DLQ");

        verifyNonLocalDocument(
            sandboxConfig,
            Region.US_EAST_2,
            "billy-sandbox-orders-bucket",
            "billy-sandbox-invoices-bucket",
            "billy-sandbox-credit-memos-bucket",
            "billy-sandbox-import-documents",
            "billy-sandbox-approval-matrix-imports",
            "http://gotenberg7:3000",
            "billy-sandbox-crm-field-mapping",
            "billy-sandbox-intelligent-sales-room",
            "billy-sandbox-webhook-payload"
        );

        verifyNonLocalElasticSearch(
            sandboxConfig,
            Region.US_EAST_2,
            "https://vpc-sandbox-billy-search-domain-2j4ms6mm65mqgznj776azotd3u.us-east-2.es.amazonaws.com"
        );

        verifyDataDogMetrics(sandboxConfig, 5);

        verifyNonLocalNotification(
            sandboxConfig,
            "https://sandbox.subskribe.net/",
            "arn:aws:sns:us-east-2:085981900979:sandbox-sns-billy_TenantNotifications"
        );

        verifyNonLocalQuartz(
            sandboxConfig,
            true,
            "https://sqs.us-east-2.amazonaws.com/085981900979/sandbox-sqs-billy_Quartz.fifo",
            Region.US_EAST_2,
            false
        );

        verifyQuartzInvoiceGenerator(sandboxConfig, 0, 15, 0);

        verifySalesForceConfig(sandboxConfig, "https://sandbox.subskribe.net/api/backend/sfdc");

        verifySecretsManager(
            sandboxConfig,
            Region.US_EAST_2,
            "sandboxdbclusterSecret59C67-bRUELd4Z5pv6",
            "sandbox/dataDogApiKey",
            "RLS_ENCRYPTION_KEY_SANDBOX",
            "OPEN_AI_CLIENT_API_KEY",
            "PANDA_DOC_API_KEY",
            "TAX_JAR_API_KEY",
            "FLATFILE_API_KEY"
        );

        verifyNonLocalSns(sandboxConfig, Region.US_EAST_2);

        verifyIdempotency(sandboxConfig, "SANDBOX_IDEMPOTENCY_TABLE");

        verifyFlyway(sandboxConfig, List.of("db/migration", "db/tenant_bootstrap_sandbox"));

        verifySesConfig(sandboxConfig, true, true, false, null);

        verifyAvalara(sandboxConfig);

        Assertions.assertThat(sandboxConfig.getDocumentConfiguration().isEnabled()).isTrue();

        Assertions.assertThat(sandboxConfig.getSiteUrl()).isEqualTo("https://sandbox.subskribe.net");

        verifyStripe(sandboxConfig, "https://sandbox.subskribe.net/callback/stripe", true);

        verifyIntervalBasedPaymentJobConfiguration(sandboxConfig, 1, 0);

        verifyAuditLogConfiguration(sandboxConfig, true, 52, ChronoUnit.WEEKS, false);

        verifyIntervalBasedPaymentReconciliationJobConfiguration(sandboxConfig, 1, 0, true);

        Assertions.assertThat(sandboxConfig.getApiUrl()).isEqualTo("https://sandbox.subskribe.net/api/backend");

        verifyIpBasedRateLimitingConfiguration(sandboxConfig, true);

        verifyInvoiceEmailConfiguration(sandboxConfig, true, true);

        verifyCreditMemoEmailConfiguration(sandboxConfig, true, true, "<EMAIL>");

        verifyMemcachedConfiguration(sandboxConfig, "sandbox-billy-memcached.vj6gb9.cfg.use2.cache.amazonaws.com");

        verifyMetricsUpdaterQuartzJobConfiguration(sandboxConfig, true, 10);

        Assertions.assertThat(sandboxConfig.getFixedAmountDiscountConfiguration().isEnabled()).isFalse();

        validateAwsAppConfigSettings(sandboxConfig, Region.US_EAST_2);

        verifyRevenueRecognitionQuartzJobConfiguration(sandboxConfig, true, 1800);

        verifyRequestLoggingConfiguration(sandboxConfig, true, "billy-sandbox-access-audit-log");

        verifyLookerConfig(
            sandboxConfig,
            "subskribe.cloud.looker.com",
            "sandbox",
            "sandbox",
            "_sandbox",
            "Standard Reports (Sandbox)",
            "Custom Reports (Sandbox)",
            true,
            "subskribe-reporting-config-sandbox"
        );

        verifyEventPumpConfig(sandboxConfig, true, 250);

        verifyEmailNotificationQueueConfiguration(
            sandboxConfig,
            true,
            Region.US_EAST_2,
            "https://sqs.us-east-2.amazonaws.com/085981900979/sandbox-email-notification-queue"
        );
        verifyCustomPaymentTypeConfiguration(sandboxConfig);

        Assertions.assertThat(sandboxConfig.getInternalLoginConfiguration().isEnabled()).isTrue();
        Assertions.assertThat(sandboxConfig.getInternalLoginConfiguration().getGoogleClientId()).isNotBlank();

        Assertions.assertThat(sandboxConfig.getQuartzApiKeyGarbageCollectorJobConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(sandboxConfig.getQuartzApiKeyGarbageCollectorJobConfiguration().getIntervalInMinutes()).isEqualTo(15);

        Assertions.assertThat(sandboxConfig.getEmailLoginLinkConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(sandboxConfig.getEmailLoginLinkConfiguration().getFromEmailAddress()).isEqualTo("<EMAIL>");

        verifyDosFilterConfiguration(sandboxConfig, true, 600, 10000);

        verifyQueryTimeoutConfiguration(sandboxConfig, true, 60);

        verifyNotificationConfiguration(
            sandboxConfig,
            true,
            true,
            10,
            "arn:aws:sns:us-east-2:085981900979:sandbox-sns-billy_TenantNotifications",
            "https://sandbox.subskribe.net/",
            false,
            "<EMAIL>"
        );

        verifyConfigCanBeSerialized(sandboxConfig);

        Assertions.assertThat(sandboxConfig.getTenantNameCacheConfiguration().getEnabled()).isTrue();

        Assertions.assertThat(sandboxConfig.getCrmConfiguration().getSyncToCrm()).isTrue();

        Assertions.assertThat(sandboxConfig.getFxConfiguration().getFxProviderType()).isEqualTo(FxProviderType.SYSTEM);

        verifySearchIndexerConfiguration(sandboxConfig, true);

        verifyLogHeartbeatConfiguration(sandboxConfig, true, 30);

        Assertions.assertThat(sandboxConfig.getTenantSealProtectionConfiguration().getEnabled()).isFalse();

        verifyRateLimitConfiguration(sandboxConfig, true, 1000);

        verifyKinesisConfiguration(sandboxConfig, false, null, Region.US_EAST_2, false, false);

        verifyTaskQueueConfiguration(
            sandboxConfig,
            true,
            true,
            60,
            15,
            250,
            Region.US_EAST_2,
            "https://sqs.us-east-2.amazonaws.com/085981900979/sandbox-billy-queued-task"
        );

        Assertions.assertThat(sandboxConfig.getOpenAIConfiguration().getAssistantId()).isEqualTo("asst_PXcxyPiCvEzrVGGguHYIfUgu");

        Assertions.assertThat(sandboxConfig.getLambdaConfiguration().isLocal()).isFalse();
        Assertions.assertThat(sandboxConfig.getLambdaConfiguration().getRegion()).isEqualTo(Region.US_EAST_2);
        Assertions.assertThat(sandboxConfig.getLambdaConfiguration().getEndpointOverride()).isNull();
        Assertions.assertThat(sandboxConfig.getLambdaConfiguration().getFunctionNamePrefix()).isEqualTo("sandbox");

        verifyBulkRevenueRecognitionQuartzConfiguration(sandboxConfig, true, 1);
        verifyElectronicSignatureConfiguration(sandboxConfig, false);
    }

    @Test
    public void testProdConfig() {
        BillyConfiguration prodConfig = TypeSafeDynamicConfigLoader.load(PROD_CONFIG);

        Assertions.assertThat(prodConfig.getCognitoConfiguration().getRegion()).isEqualTo(Region.US_EAST_2);
        Assertions.assertThat(prodConfig.getCognitoConfiguration().getUserPoolNameForTenantPrefix()).isEqualTo("Tenant-prod-");
        Assertions.assertThat(prodConfig.getCognitoConfiguration().getCallbackUrl()).isEqualTo("https://app.subskribe.com/api/auth/signin");
        Assertions.assertThat(prodConfig.getCognitoConfiguration().getSignOutUrl()).isEqualTo("https://app.subskribe.com/api/auth/signout");
        Assertions.assertThat(prodConfig.getCognitoConfiguration().getUiLoginUrl()).isEqualTo("https://app.subskribe.com/login");
        Assertions.assertThat(prodConfig.getCognitoConfiguration().getEmailIdentityArn()).isEqualTo(
            "arn:aws:ses:us-east-1:137933513575:identity/subskribe.com"
        );
        Assertions.assertThat(prodConfig.getCognitoConfiguration().getFromEmailAddress()).isEqualTo("<EMAIL>");
        Assertions.assertThat(prodConfig.getCognitoConfiguration().getUserPoolNameForTenantPrefix()).isEqualTo("Tenant-prod-");
        Assertions.assertThat(prodConfig.getCognitoConfiguration().getShouldInviteNewUsers()).isTrue();

        verifyApprovalFlowConfiguration(prodConfig, true, true, false, true, true, true, true, true, "<EMAIL>");

        verifyDunningConfiguration(prodConfig, true, true, 30, "<EMAIL>", true);

        verifyDynamoDb(prodConfig, Region.US_EAST_2, UNCONFIGURED);

        verifyUsageAggregationQuartzConfiguration(prodConfig);

        verifyBulkInvoiceRunQuartzConfiguration(prodConfig, true, 600);

        verifyUsageAggregationConfiguration(prodConfig);

        verifyDatabase(prodConfig, "*********************************************************************************************", 15, 50);
        verifyReadOnlyDatabase(prodConfig, "************************************************************************************************", 10);

        verifyNonLocalDlq(prodConfig, Region.US_EAST_2, "https://sqs.us-east-2.amazonaws.com/137933513575/prod-sqs-billy_DLQ");

        verifyNonLocalDocument(
            prodConfig,
            Region.US_EAST_2,
            "billy-prod-orders-bucket",
            "billy-prod-invoices-bucket",
            "billy-prod-credit-memos-bucket",
            "billy-prod-import-documents",
            "billy-prod-approval-matrix-imports",
            "http://gotenberg7:3000",
            "billy-prod-crm-field-mapping",
            "billy-prod-intelligent-sales-room",
            "billy-prod-webhook-payload"
        );

        verifyNonLocalElasticSearch(
            prodConfig,
            Region.US_EAST_2,
            "https://vpc-prod-billy-search-domain-23l2lqmi7n6xlzywcgnwgukfwa.us-east-2.es.amazonaws.com"
        );

        // metrics
        verifyDataDogMetrics(prodConfig, 5);

        verifyNonLocalNotification(prodConfig, "https://app.subskribe.com/", "arn:aws:sns:us-east-2:137933513575:prod-sns-billy_TenantNotifications");

        // quartz
        verifyNonLocalQuartz(
            prodConfig,
            true,
            "https://sqs.us-east-2.amazonaws.com/137933513575/prod-sqs-billy_Quartz.fifo",
            Region.US_EAST_2,
            false
        );

        verifyQuartzInvoiceGenerator(prodConfig, 0, 15, 0);

        verifySalesForceConfig(prodConfig, "https://app.subskribe.com/api/backend/sfdc");

        verifySecretsManager(
            prodConfig,
            Region.US_EAST_2,
            "proddbclusterSecret623536FF-hKfY4svPiNZ0",
            "dataDogApiKey",
            "RLS_ENCRYPTION_KEY_PROD",
            "OPEN_AI_CLIENT_API_KEY",
            "PANDA_DOC_API_KEY",
            "TAX_JAR_API_KEY",
            "FLATFILE_API_KEY"
        );

        verifyNonLocalSns(prodConfig, Region.US_EAST_2);

        verifyIdempotency(prodConfig, "PROD_IDEMPOTENCY_TABLE");

        verifyFlyway(prodConfig, List.of("db/migration", "db/tenant_bootstrap_prod"));

        verifyAvalara(prodConfig);

        Assertions.assertThat(prodConfig.getDocumentConfiguration().isEnabled()).isTrue();

        Assertions.assertThat(prodConfig.getSiteUrl()).isEqualTo("https://app.subskribe.com");

        verifyStripe(prodConfig, "https://app.subskribe.com/callback/stripe", true);

        verifyIntervalBasedPaymentJobConfiguration(prodConfig, 60, 0);

        verifyAuditLogConfiguration(prodConfig, true, 260, ChronoUnit.WEEKS, false);

        verifyIntervalBasedPaymentReconciliationJobConfiguration(prodConfig, 60, 0, true);

        Assertions.assertThat(prodConfig.getApiUrl()).isEqualTo("https://app.subskribe.com/api/backend");

        verifyIpBasedRateLimitingConfiguration(prodConfig, true);

        verifyInvoiceEmailConfiguration(prodConfig, true, false);

        verifyCreditMemoEmailConfiguration(prodConfig, true, false, "<EMAIL>");

        verifyMemcachedConfiguration(prodConfig, "prod-billy-memcached.izxnsr.cfg.use2.cache.amazonaws.com");

        verifyMetricsUpdaterQuartzJobConfiguration(prodConfig, true, 1);

        Assertions.assertThat(prodConfig.getFixedAmountDiscountConfiguration().isEnabled()).isFalse();

        validateAwsAppConfigSettings(prodConfig, Region.US_EAST_2);

        verifyRevenueRecognitionQuartzJobConfiguration(prodConfig, true, 1800);

        verifyRequestLoggingConfiguration(prodConfig, true, "billy-prod-access-audit-log");

        verifyEventPumpConfig(prodConfig, true, 250);

        verifyLookerConfig(
            prodConfig,
            "subskribe.cloud.looker.com",
            "prod",
            "prod",
            "",
            "Standard Reports",
            "Custom Reports",
            false,
            "subskribe-reporting-config-prod"
        );

        verifyEmailNotificationQueueConfiguration(
            prodConfig,
            true,
            Region.US_EAST_2,
            "https://sqs.us-east-2.amazonaws.com/137933513575/prod-email-notification-queue"
        );
        verifyCustomPaymentTypeConfiguration(prodConfig);

        Assertions.assertThat(prodConfig.getInternalLoginConfiguration().isEnabled()).isTrue();

        Assertions.assertThat(prodConfig.getQuartzApiKeyGarbageCollectorJobConfiguration().getEnabled()).isTrue();

        Assertions.assertThat(prodConfig.getEmailLoginLinkConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(prodConfig.getEmailLoginLinkConfiguration().getFromEmailAddress()).isEqualTo("<EMAIL>");

        verifyDosFilterConfiguration(prodConfig, true, 600, 10000);

        verifyQueryTimeoutConfiguration(prodConfig, true, 60);

        verifyNotificationConfiguration(
            prodConfig,
            true,
            true,
            10,
            "arn:aws:sns:us-east-2:137933513575:prod-sns-billy_TenantNotifications",
            "https://app.subskribe.com/",
            false,
            "<EMAIL>"
        );

        verifyConfigCanBeSerialized(prodConfig);

        Assertions.assertThat(prodConfig.getTenantNameCacheConfiguration().getEnabled()).isTrue();

        Assertions.assertThat(prodConfig.getCrmConfiguration().getSyncToCrm()).isTrue();

        Assertions.assertThat(prodConfig.getFxConfiguration().getFxProviderType()).isEqualTo(FxProviderType.SYSTEM);

        verifySearchIndexerConfiguration(prodConfig, true);

        verifyLogHeartbeatConfiguration(prodConfig, true, 30);

        Assertions.assertThat(prodConfig.getTenantSealProtectionConfiguration().getEnabled()).isFalse();

        verifyRateLimitConfiguration(prodConfig, true, 1000);

        verifyKinesisConfiguration(prodConfig, false, null, Region.US_EAST_2, false, false);

        verifyTaskQueueConfiguration(
            prodConfig,
            true,
            true,
            60,
            15,
            250,
            Region.US_EAST_2,
            "https://sqs.us-east-2.amazonaws.com/137933513575/prod-billy-queued-task"
        );

        verifySesConfig(prodConfig, false, false, false, null);

        Assertions.assertThat(prodConfig.getOpenAIConfiguration().getAssistantId()).isEqualTo("asst_kOcZkYTLR6VnCfIGP9jEui7w");

        Assertions.assertThat(prodConfig.getLambdaConfiguration().isLocal()).isFalse();
        Assertions.assertThat(prodConfig.getLambdaConfiguration().getRegion()).isEqualTo(Region.US_EAST_2);
        Assertions.assertThat(prodConfig.getLambdaConfiguration().getEndpointOverride()).isNull();
        Assertions.assertThat(prodConfig.getLambdaConfiguration().getFunctionNamePrefix()).isEqualTo("prod");

        verifyBulkRevenueRecognitionQuartzConfiguration(prodConfig, true, 1);
        verifyElectronicSignatureConfiguration(prodConfig, false);
    }

    @Test
    public void testLocalConfigWithoutTenantScopedConfig() {
        BillyConfiguration localConfig = TypeSafeDynamicConfigLoader.load(LOCAL_CONFIG);
        testLocalConfig(localConfig, false);
    }

    @Test
    public void testLocalConfigWithTenantScopedConfig() {
        BillyConfiguration localConfig = TypeSafeDynamicConfigLoader.load(LOCAL_CONFIG);
        BillyConfiguration tenantScopedBillyConfiguration = TenantScopedConfigProvider.provideCombined(localConfig, E2E_TENANT_ID);
        testLocalConfig(tenantScopedBillyConfiguration, true);
    }

    private void testLocalConfig(BillyConfiguration localConfig, boolean isTenantScoped) {
        verifyApprovalFlowConfiguration(localConfig, true, false, true, true, true, true, true, true, "<EMAIL>");

        verifyDynamoDb(localConfig, Region.US_EAST_2, "http://localhost:4566");
        Assertions.assertThat(localConfig.getDlqConfiguration().getEnabled()).isFalse();
        Assertions.assertThat(localConfig.getQuartzUsageAggregationConfiguration().getEnabled()).isFalse();
        Assertions.assertThat(localConfig.getUsageAggregationConfiguration().getRawUsageAggregationBatchSize()).isEqualTo(1000);
        Assertions.assertThat(localConfig.getUsageAggregationConfiguration().getNtpDriftFactor()).isEqualTo(Duration.parse("PT0S"));
        verifyLocalDocument(
            localConfig,
            Region.US_EAST_2,
            "order-documents",
            "invoice-documents",
            "credit-memo-documents",
            "import-documents",
            "approval-matrix-imports",
            "http://localhost:3001",
            "http://localhost:4566",
            isTenantScoped ? "e2eQuote.mustache" : "defaultQuote.mustache",
            isTenantScoped ? "e2eQuote.css" : "default.css",
            "defaultQuoteContainer.mustache",
            "defaultInvoice.mustache",
            "crm-field-mapping",
            "intelligent-sales-room",
            "webhook-payload"
        );
        verifySecretsManager(
            localConfig,
            Region.US_WEST_2,
            "LOCAL_CI_DATABASE_SECRET",
            "dataDogApiKey",
            "RLS_ENCRYPTION_KEY_LOCAL_CI",
            "OPEN_AI_CLIENT_API_KEY",
            "PANDA_DOC_API_KEY",
            "TAX_JAR_API_KEY",
            "FLATFILE_API_KEY"
        );
        Assertions.assertThat(localConfig.getSecretsManagerConfiguration().getRegion()).isEqualTo(Region.US_WEST_2);
        Assertions.assertThat(localConfig.getSecretsManagerConfiguration().getRlsEncryptionKeyName()).isEqualTo("RLS_ENCRYPTION_KEY_LOCAL_CI");
        Assertions.assertThat(localConfig.getQuartzConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(localConfig.getQuartzConfiguration().isClustered()).isFalse();
        Assertions.assertThat(localConfig.getQuartzQueueConfiguration().getQueueUrl()).isEqualTo(
            "http://localhost:4566/queue/us-east-1/000000000000/Local-Quartz-Queue.fifo"
        );
        verifyLocalSns(localConfig, "unconfigured");
        verifyIdempotency(localConfig, "LOCAL_IDEMPOTENCY_TABLE");
        Assertions.assertThat(localConfig.getAvalaraConfiguration().isCommitTaxTransactions()).isFalse();
        verifySesConfig(localConfig, true, true, true, "http://localhost:4566");
        Assertions.assertThat(localConfig.getDocumentConfiguration().isEnabled()).isTrue();
        Assertions.assertThat(localConfig.getSiteUrl()).isEqualTo("http://localdev.subskribe.net:3000");
        verifyStripe(localConfig, "http://localdev.subskribe.net:3000/callback/stripe", true);
        verifyIntervalBasedPaymentJobConfiguration(localConfig, 0, 30);
        verifyAuditLogConfiguration(localConfig, true, 15, ChronoUnit.DAYS, false);
        Assertions.assertThat(localConfig.getQuartzPaymentReconciliationJobConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(localConfig.getApiUrl()).isEqualTo("http://localdev.subskribe.net:3000/api/backend");
        verifyIpBasedRateLimitingConfiguration(localConfig, false);
        verifyBulkInvoiceRunQuartzConfiguration(localConfig, false, 60);
        verifyMetricsUpdaterQuartzJobConfiguration(localConfig, true, 1);
        verifyDunningConfiguration(localConfig, false, false, 1, "<EMAIL>", true);

        validateAwsAppConfigSettings(localConfig, Region.US_WEST_2);
        verifyCustomPaymentTypeConfiguration(localConfig);

        Assertions.assertThat(localConfig.getQuartzApiKeyGarbageCollectorJobConfiguration().getEnabled()).isFalse();

        Assertions.assertThat(localConfig.getEmailLoginLinkConfiguration().getEnabled()).isTrue();

        verifyNotificationConfiguration(localConfig, false, true, 10, "unconfigured", "http://localhost:8080/", true, "<EMAIL>");

        verifyConfigCanBeSerialized(localConfig);

        Assertions.assertThat(localConfig.getTenantNameCacheConfiguration().getEnabled()).isFalse();

        Assertions.assertThat(localConfig.getFxConfiguration().getFxProviderType()).isEqualTo(FxProviderType.TEST_RANDOM);

        verifyLogHeartbeatConfiguration(localConfig, false, 30);

        Assertions.assertThat(localConfig.getTenantSealProtectionConfiguration().getEnabled()).isTrue();

        verifyKinesisConfiguration(localConfig, true, "http://localhost:4566", Region.US_EAST_2, true, true);

        verifyTaskQueueConfiguration(
            localConfig,
            true,
            false,
            60,
            15,
            500,
            Region.US_EAST_2,
            "http://localhost:4566/queue/us-east-2/000000000000/billy-task-queue"
        );

        Assertions.assertThat(localConfig.getLambdaConfiguration().isLocal()).isTrue();
        Assertions.assertThat(localConfig.getLambdaConfiguration().getEndpointOverride()).isEqualTo("http://localhost:4566");

        verifyBulkRevenueRecognitionQuartzConfiguration(localConfig, true, 1);
    }

    @Test
    public void validateBillyConfigurationClassesDontUsePrimitiveValues() throws Exception {
        Assertions.assertThat(BillyConfigurationValidator.validateBillyConfigurationClassesHaveNoPrimitiveValues())
            .as("Only boxed primitives are allowed in configuration classes for correctness reasons")
            .isTrue();
    }

    private void verifyEnabledUsageAggregationConfiguration(BillyConfiguration dev2Config) {
        Assertions.assertThat(dev2Config.getQuartzUsageAggregationConfiguration().getIntervalInSeconds()).isEqualTo(60);
        Assertions.assertThat(dev2Config.getQuartzUsageAggregationConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(dev2Config.getUsageAggregationConfiguration().getRawUsageAggregationBatchSize()).isEqualTo(1000);
        Assertions.assertThat(dev2Config.getUsageAggregationConfiguration().getNtpDriftFactor()).isEqualTo(Duration.parse("PT60S"));
    }

    private void validateAwsAppConfigSettings(BillyConfiguration config, Region region) {
        var envName = config.getEnvName();
        if ((config.isDev() && !config.getEnvName().startsWith("devops")) || config.isLocalOrCi()) {
            envName = "dev";
        }

        var applicationTemplate = String.format("billy-%s-appconfig-app", envName);
        var configurationProfileTemplate = String.format("billy-%s-feature-flags", envName);
        var environmentTemplate = String.format("billy-%s-appconfig-env", envName);

        Assertions.assertThat(config.getAppConfigConfiguration().getApplication()).isEqualTo(applicationTemplate);
        Assertions.assertThat(config.getAppConfigConfiguration().getConfigurationProfile()).isEqualTo(configurationProfileTemplate);
        Assertions.assertThat(config.getAppConfigConfiguration().getEnvironment()).isEqualTo(environmentTemplate);
        Assertions.assertThat(config.getAppConfigConfiguration().getRegion()).isEqualTo(region);
    }

    private void verifyElectronicSignatureConfiguration(BillyConfiguration configuration, boolean expectedValue) {
        Assertions.assertThat(configuration.getElectronicSignatureConfiguration().isIgnoreSignatureVerificationForResellers()).isEqualTo(
            expectedValue
        );
    }
}
