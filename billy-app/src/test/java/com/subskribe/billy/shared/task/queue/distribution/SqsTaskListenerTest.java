package com.subskribe.billy.shared.task.queue.distribution;

import static com.subskribe.billy.shared.task.queue.config.TaskQueueConstants.SQS_LISTENER_LONG_POLL_TIMEOUT_SECONDS;
import static com.subskribe.billy.shared.task.queue.config.TaskQueueConstants.SQS_LISTENER_VISIBILITY_TIMEOUT_SECONDS;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.model.QueueConfiguration;
import com.subskribe.billy.aws.sqs.SqsClientProvider;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.task.queue.config.TaskQueueConfiguration;
import com.subskribe.billy.shared.task.queue.config.TaskQueueSqsConfiguration;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs;
import com.subskribe.billy.shared.task.queue.processor.TaskExecutor;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.DeleteMessageBatchRequest;
import software.amazon.awssdk.services.sqs.model.DeleteMessageBatchRequestEntry;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageResponse;

class SqsTaskListenerTest {

    private SqsClient sqsClient;
    private ObjectMapper objectMapper;
    private QueueConfiguration queueConfiguration;
    private TaskExecutor taskExecutor;

    private SqsTaskListener sqsTaskListener;

    @BeforeEach
    void setUp() {
        sqsClient = mock(SqsClient.class);
        objectMapper = spy(JacksonProvider.defaultMapper());
        queueConfiguration = new TaskQueueSqsConfiguration(Region.US_EAST_1, "http://queue");
        taskExecutor = mock(TaskExecutor.class);
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        TaskQueueConfiguration taskQueueConfiguration = mock(TaskQueueConfiguration.class);
        SqsClientProvider sqsClientProvider = mock(SqsClientProvider.class);
        when(sqsClientProvider.setupClientForQueue(queueConfiguration)).thenReturn(sqsClient);
        when(billyConfiguration.getTaskQueueConfiguration()).thenReturn(taskQueueConfiguration);
        when(taskQueueConfiguration.getQueueConfiguration()).thenReturn(queueConfiguration);

        sqsTaskListener = new SqsTaskListener(sqsClientProvider, billyConfiguration, taskExecutor, objectMapper);
    }

    @Test
    void testNoMessagesPulledAfterShutdown() throws Exception {
        FieldUtils.writeField(sqsTaskListener, "active", false, true);
        sqsTaskListener.tryPollForMessages();

        verifyNoInteractions(sqsClient);
    }

    @Test
    void testMessagesSentToExecutorAndDeletedEvenWhenFailed() throws Exception {
        QueuedTask task1 = QueuedTaskStubs.buildTask();
        QueuedTask task2 = QueuedTaskStubs.buildTask();
        List<Message> messages = setupReceiveMessage(10, List.of(task1, task2));
        doThrow(new RuntimeException()).when(taskExecutor).executeTask(task1);
        when(taskExecutor.provideCapacity()).thenReturn(10);

        sqsTaskListener.tryPollForMessages();

        List<DeleteMessageBatchRequestEntry> expectedDeleteRequest = List.of(
            DeleteMessageBatchRequestEntry.builder().id(messages.get(0).messageId()).receiptHandle(messages.get(0).receiptHandle()).build(),
            DeleteMessageBatchRequestEntry.builder().id(messages.get(1).messageId()).receiptHandle(messages.get(1).receiptHandle()).build()
        );
        DeleteMessageBatchRequest deleteMessageBatchRequest = DeleteMessageBatchRequest.builder()
            .queueUrl(queueConfiguration.getQueueUrl())
            .entries(expectedDeleteRequest)
            .build();

        verify(sqsClient, times(1)).deleteMessageBatch(deleteMessageBatchRequest);
    }

    private List<Message> setupReceiveMessage(int capacity, List<QueuedTask> tasks) {
        ReceiveMessageRequest receiveMessageRequest = ReceiveMessageRequest.builder()
            .queueUrl(queueConfiguration.getQueueUrl())
            .maxNumberOfMessages(capacity)
            .waitTimeSeconds(SQS_LISTENER_LONG_POLL_TIMEOUT_SECONDS)
            .visibilityTimeout(SQS_LISTENER_VISIBILITY_TIMEOUT_SECONDS)
            .build();
        List<Message> messages = tasks
            .stream()
            .map(task -> {
                try {
                    return Message.builder()
                        .messageId(UUID.randomUUID().toString())
                        .receiptHandle(UUID.randomUUID().toString())
                        .body(objectMapper.writeValueAsString(task))
                        .build();
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            })
            .toList();
        when(sqsClient.receiveMessage(receiveMessageRequest)).thenReturn(ReceiveMessageResponse.builder().messages(messages).build());
        return messages;
    }
}
