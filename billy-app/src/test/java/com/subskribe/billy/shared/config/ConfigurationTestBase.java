package com.subskribe.billy.shared.config;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatNoException;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import io.dropwizard.metrics.CsvReporterFactory;
import io.dropwizard.metrics.DatadogReporterFactory;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import org.assertj.core.api.Assertions;
import software.amazon.awssdk.regions.Region;

@SuppressWarnings("ALL")
public class ConfigurationTestBase {

    public static final String UNCONFIGURED = null;

    protected void verifyDynamoDb(BillyConfiguration config, Region region, String dbUrl) {
        Assertions.assertThat(config.getDynamoDbConfiguration().getRegion()).isEqualTo(region);
        Assertions.assertThat(config.getDynamoDbConfiguration().getDbUrl()).isEqualTo(dbUrl);
    }

    protected void verifyNonLocalCognito(BillyConfiguration config, String envName, Region region, String accountId) {
        Assertions.assertThat(config.getCognitoConfiguration().getRegion()).isEqualTo(region);
        Assertions.assertThat(config.getCognitoConfiguration().getUserPoolNameForTenantPrefix()).isEqualTo(String.format("Tenant-%s-", envName));
        Assertions.assertThat(config.getCognitoConfiguration().getCallbackUrl()).isEqualTo(
            String.format("https://%s.subskribe.net/api/auth/signin", envName)
        );
        Assertions.assertThat(config.getCognitoConfiguration().getSignOutUrl()).isEqualTo(
            String.format("https://%s.subskribe.net/api/auth/signout", envName)
        );
        Assertions.assertThat(config.getCognitoConfiguration().getUiLoginUrl()).isEqualTo(String.format("https://%s.subskribe.net/login", envName));
        Assertions.assertThat(config.getCognitoConfiguration().getEmailIdentityArn()).isEqualTo(
            String.format("arn:aws:ses:us-east-1:%s:identity/subskribe.net", accountId)
        );
        Assertions.assertThat(config.getCognitoConfiguration().getFromEmailAddress()).isEqualTo("<EMAIL>");
        Assertions.assertThat(config.getCognitoConfiguration().getShouldInviteNewUsers()).isTrue();
    }

    protected void verifyDatabase(BillyConfiguration config, String dbUrl, int maxPerUser, int maxTotalConnections) {
        Assertions.assertThat(config.getDbcpConfiguration().getDriverClassName()).isEqualTo("org.postgresql.Driver");
        Assertions.assertThat(config.getDbcpConfiguration().getUrl()).isEqualTo(dbUrl);
        Assertions.assertThat(config.getDbcpConfiguration().isTestWhileIdle()).isEqualTo(true);
        Assertions.assertThat(config.getDbcpConfiguration().isTestOnBorrow()).isEqualTo(true);
        Assertions.assertThat(config.getDbcpConfiguration().getTimeBetweenEvictionRunsMillis()).isEqualTo(10000);
        Assertions.assertThat(config.getDbcpConfiguration().getMinEvictableIdleTimeMillis()).isEqualTo(30000);
        Assertions.assertThat(config.getDbcpConfiguration().getConnectionProperties()).isEqualTo(Map.of("charSet", "UTF-8"));
        Assertions.assertThat(config.getDbcpConfiguration().getValidationQuery()).isEqualTo("/* Billy Health Check */ SELECT 1");
        Assertions.assertThat(config.getDbcpConfiguration().getValidationQueryTimeout()).isEqualTo(3);
        Assertions.assertThat(config.getDbcpConfiguration().getMaxPerUser()).isEqualTo(maxPerUser);
        Assertions.assertThat(config.getDbcpConfiguration().getMaxPerUserIdle()).isEqualTo(maxPerUser);
        Assertions.assertThat(config.getDbcpConfiguration().getMinPerUserIdle()).isEqualTo(0);
        Assertions.assertThat(config.getDbcpConfiguration().getMaxTotalActive()).isEqualTo(maxTotalConnections);
        Assertions.assertThat(config.getDbcpConfiguration().getReadOnly()).isEqualTo(false);
    }

    protected void verifyReadOnlyDatabase(BillyConfiguration config, String dbUrl, int maxTotalActive) {
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getDriverClassName()).isEqualTo("org.postgresql.Driver");
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getUrl()).isEqualTo(dbUrl);
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().isTestWhileIdle()).isEqualTo(true);
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().isTestOnBorrow()).isEqualTo(true);
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getTimeBetweenEvictionRunsMillis()).isEqualTo(10000);
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getMinEvictableIdleTimeMillis()).isEqualTo(30000);
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getConnectionProperties()).isEqualTo(Map.of("charSet", "UTF-8"));
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getValidationQuery()).isEqualTo("/* Billy Health Check */ SELECT 1");
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getValidationQueryTimeout()).isEqualTo(3);
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getMaxPerUser()).isEqualTo(3);
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getMaxPerUserIdle()).isEqualTo(1);
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getMinPerUserIdle()).isEqualTo(0);
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getMaxTotalActive()).isEqualTo(maxTotalActive);
        Assertions.assertThat(config.getDbcpReadOnlyConfiguration().getReadOnly()).isEqualTo(true);
    }

    protected void verifyNonLocalDlq(BillyConfiguration config, Region region, String queueUrl) {
        Assertions.assertThat(config.getDlqConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(config.getDlqConfiguration().getRegion()).isEqualTo(region);
        Assertions.assertThat(config.getDlqConfiguration().getQueueUrl()).isEqualTo(queueUrl);
    }

    protected void verifyNonLocalDocument(
        BillyConfiguration config,
        Region region,
        String orderBucket,
        String invoiceBucket,
        String creditMemoBucket,
        String importBucket,
        String approvalMatrixImportsBucket,
        String gotenbergUrl,
        String crmFieldMappingBucket,
        String intelligentSalesRoomBucket,
        String webhookPayloadBucket
    ) {
        Assertions.assertThat(config.getDocumentConfiguration().isLocal()).isFalse();
        Assertions.assertThat(config.getDocumentConfiguration().getGotenbergUrl()).isEqualTo(gotenbergUrl);
        Assertions.assertThat(config.getDocumentConfiguration().getS3Region()).isEqualTo(region.toString());
        Assertions.assertThat(config.getDocumentConfiguration().getInvoiceS3Bucket()).isEqualTo(invoiceBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getCreditMemoS3Bucket()).isEqualTo(creditMemoBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getOrderS3Bucket()).isEqualTo(orderBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getImportS3Bucket()).isEqualTo(importBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getApprovalMatrixS3Bucket()).isEqualTo(approvalMatrixImportsBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getS3Url()).isEqualTo(getS3Url(region));
        Assertions.assertThat(config.getDocumentConfiguration().getCrmFieldMappingS3Bucket()).isEqualTo(crmFieldMappingBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getIntelligentSalesRoomS3Bucket()).isEqualTo(intelligentSalesRoomBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getWebhookPayloadS3Bucket()).isEqualTo(webhookPayloadBucket);
    }

    protected void verifyLocalDocument(
        BillyConfiguration config,
        Region region,
        String orderBucket,
        String invoiceBucket,
        String creditMemoBucket,
        String importBucket,
        String approvalMatrixImportsBucket,
        String gotenbergUrl,
        String s3Url,
        String orderFormTemplateFileName,
        String orderFormTemplateCssFileName,
        String orderFormTemplateContainerFileName,
        String invoiceTemplateFileName,
        String crmFieldMappingBucket,
        String intelligentSalesRoomBucket,
        String webhookPayloadBucket
    ) {
        Assertions.assertThat(config.getDocumentConfiguration().isLocal()).isTrue();
        Assertions.assertThat(config.getDocumentConfiguration().getGotenbergUrl()).isEqualTo(gotenbergUrl);
        Assertions.assertThat(config.getDocumentConfiguration().getS3Region()).isEqualTo(region.toString());
        Assertions.assertThat(config.getDocumentConfiguration().getInvoiceS3Bucket()).isEqualTo(invoiceBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getCreditMemoS3Bucket()).isEqualTo(creditMemoBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getOrderS3Bucket()).isEqualTo(orderBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getImportS3Bucket()).isEqualTo(importBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getApprovalMatrixS3Bucket()).isEqualTo(approvalMatrixImportsBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getS3Url()).isEqualTo(s3Url);
        Assertions.assertThat(config.getDocumentConfiguration().getOrderFormTemplateFileName()).isEqualTo(orderFormTemplateFileName);
        Assertions.assertThat(config.getDocumentConfiguration().getOrderFormTemplateCssFileName()).isEqualTo(orderFormTemplateCssFileName);
        Assertions.assertThat(config.getDocumentConfiguration().getOrderFormTemplateContainerFileName()).isEqualTo(
            orderFormTemplateContainerFileName
        );
        Assertions.assertThat(config.getDocumentConfiguration().getInvoiceTemplateFileName()).isEqualTo(invoiceTemplateFileName);
        Assertions.assertThat(config.getDocumentConfiguration().getCrmFieldMappingS3Bucket()).isEqualTo(crmFieldMappingBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getIntelligentSalesRoomS3Bucket()).isEqualTo(intelligentSalesRoomBucket);
        Assertions.assertThat(config.getDocumentConfiguration().getWebhookPayloadS3Bucket()).isEqualTo(webhookPayloadBucket);
    }

    protected void verifyDataDogMetrics(BillyConfiguration config, int frequencySeconds) {
        Assertions.assertThat(config.getMetricsFactory().getFrequency().toSeconds()).isEqualTo(frequencySeconds);
        Assertions.assertThat(config.getMetricsFactory().getReporters().size()).isEqualTo(1);
        Assertions.assertThat(config.getMetricsFactory().getReporters().get(0)).isInstanceOf(DatadogReporterFactory.class);
    }

    protected void verifyCsvMetrics(BillyConfiguration config) {
        Assertions.assertThat(config.getMetricsFactory().getFrequency().toMinutes()).isEqualTo(1);
        Assertions.assertThat(config.getMetricsFactory().getReporters().size()).isEqualTo(1);
        Assertions.assertThat(config.getMetricsFactory().getReporters().get(0)).isInstanceOf(CsvReporterFactory.class);
    }

    protected void verifyNonLocalElasticSearch(BillyConfiguration config, Region region, String esEndpoint) {
        Assertions.assertThat(config.getElasticSearchConfiguration().isLocalES()).isFalse();
        Assertions.assertThat(config.getElasticSearchConfiguration().getRegion()).isEqualTo(region);
        Assertions.assertThat(config.getElasticSearchConfiguration().getEndpoint()).isEqualTo(esEndpoint);
        Assertions.assertThat(config.getElasticSearchConfiguration().getSlowQueryThreshold()).isEqualTo(150L);
    }

    protected void verifyNonLocalNotification(BillyConfiguration config, String siteUrl, String topicArn) {
        Assertions.assertThat(config.getNotificationConfiguration().getSiteUrl()).isEqualTo(siteUrl);
        Assertions.assertThat(config.getNotificationConfiguration().getSnsTopicArn()).isEqualTo(topicArn);
    }

    protected void verifyNonLocalQuartz(
        BillyConfiguration config,
        boolean clearOnStartup,
        String queueUrl,
        Region region,
        boolean isPollerJobEnabled
    ) {
        Assertions.assertThat(config.getQuartzConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(config.getQuartzConfiguration().isClustered()).isTrue();
        Assertions.assertThat(config.getQuartzConfiguration().getPropertiesFile()).isEqualTo("quartz.properties");
        Assertions.assertThat(config.getQuartzConfiguration().getClearOnStartup()).isEqualTo(clearOnStartup);

        Assertions.assertThat(config.getQuartzQueueConfiguration().getQueueUrl()).isEqualTo(queueUrl);
        Assertions.assertThat(config.getQuartzQueueConfiguration().getEnabled()).isEqualTo(isPollerJobEnabled);
        Assertions.assertThat(config.getQuartzQueueConfiguration().getRegion()).isEqualTo(region);
    }

    protected void verifyQuartzInvoiceGenerator(BillyConfiguration config, int hourInterval, int minuteInterval, int secondInterval) {
        Assertions.assertThat(config.getQuartzInvoiceGeneratorConfiguration().getEnabled()).isTrue();
        Assertions.assertThat(config.getQuartzInvoiceGeneratorConfiguration().getIntervalInHours()).isEqualTo(hourInterval);
        Assertions.assertThat(config.getQuartzInvoiceGeneratorConfiguration().getIntervalInMinutes()).isEqualTo(minuteInterval);
        Assertions.assertThat(config.getQuartzInvoiceGeneratorConfiguration().getIntervalInSeconds()).isEqualTo(secondInterval);
    }

    protected void verifySalesForceConfig(BillyConfiguration config, String redirectUrl) {
        Assertions.assertThat(config.getSalesforceConfiguration().getSubskribeRedirectUrl()).isEqualTo(redirectUrl);
        Assertions.assertThat(config.getSalesforceConfiguration().getApiVersion()).isEqualTo("v55.0");
    }

    protected void verifyUsageAggregationQuartzConfiguration(BillyConfiguration configuration) {
        Assertions.assertThat(configuration.getQuartzUsageAggregationConfiguration().getIntervalInSeconds()).isEqualTo(300);
        Assertions.assertThat(configuration.getQuartzUsageAggregationConfiguration().getEnabled()).isTrue();
    }

    protected void verifyBulkInvoiceRunQuartzConfiguration(BillyConfiguration configuration, boolean enabled, long intervalInSeconds) {
        Assertions.assertThat(configuration.getQuartzBulkInvoiceRunConfiguration().getIntervalInSeconds()).isEqualTo(intervalInSeconds);
        Assertions.assertThat(configuration.getQuartzBulkInvoiceRunConfiguration().getEnabled()).isEqualTo(enabled);
    }

    protected void verifyUsageAggregationConfiguration(BillyConfiguration configuration) {
        Assertions.assertThat(configuration.getUsageAggregationConfiguration().getRawUsageAggregationBatchSize()).isEqualTo(1000);
        Assertions.assertThat(configuration.getUsageAggregationConfiguration().getNtpDriftFactor()).isEqualTo(Duration.parse("PT1M"));
    }

    protected void verifySecretsManager(
        BillyConfiguration config,
        Region region,
        String dbSecret,
        String ddSecret,
        String rlsSecret,
        String openAiSecret,
        String pandaDocSecret,
        String taxJarSecret,
        String flatfileSecret
    ) {
        Assertions.assertThat(config.getSecretsManagerConfiguration().getRegion()).isEqualTo(region);
        Assertions.assertThat(config.getSecretsManagerConfiguration().getDatabaseSecretName()).isEqualTo(dbSecret);
        Assertions.assertThat(config.getSecretsManagerConfiguration().getDatadogApiKeySecretName()).isEqualTo(ddSecret);
        Assertions.assertThat(config.getSecretsManagerConfiguration().getRlsEncryptionKeyName()).isEqualTo(rlsSecret);
        Assertions.assertThat(config.getSecretsManagerConfiguration().getOpenAiClientApiKeySecretName()).isEqualTo(openAiSecret);
        Assertions.assertThat(config.getSecretsManagerConfiguration().getPandaDocApiKeySecretName()).isEqualTo(pandaDocSecret);
        Assertions.assertThat(config.getSecretsManagerConfiguration().getTaxJarApiKeySecretName()).isEqualTo(taxJarSecret);
        Assertions.assertThat(config.getSecretsManagerConfiguration().getFlatfileApiKeySecretName()).isEqualTo(flatfileSecret);
    }

    protected void verifyNonLocalSns(BillyConfiguration config, Region region) {
        Assertions.assertThat(config.getSnsConfiguration().isLocal()).isFalse();
        Assertions.assertThat(config.getSnsConfiguration().getRegion()).isEqualTo(region);
    }

    protected void verifyLocalSns(BillyConfiguration config, String region) {
        Assertions.assertThat(config.getSnsConfiguration().isLocal()).isTrue();
        Assertions.assertThat(config.getSnsConfiguration().getRegion()).isEqualTo(Region.of(region));
    }

    protected void verifyIdempotency(BillyConfiguration config, String idempotencyTable) {
        Assertions.assertThat(config.getIdempotencyConfiguration().getIdempotencyTableName()).isEqualTo(idempotencyTable);
        Assertions.assertThat(config.getIdempotencyConfiguration().getDefaultBackOffTimeMs()).isEqualTo(500);
        Assertions.assertThat(config.getIdempotencyConfiguration().getRecordTTLInHours().toHours()).isEqualTo(4);
    }

    protected void verifyFlyway(BillyConfiguration config, List<String> locations) {
        Assertions.assertThat(config.getFlywayFactory().getBaselineDescription()).isEqualTo("<< Flyway Baseline >>");
        Assertions.assertThat(config.getFlywayFactory().isCleanDisabled()).isTrue();
        Assertions.assertThat(config.getFlywayFactory().isIgnoreMissingMigrations()).isTrue();
        Assertions.assertThat(config.getFlywayFactory().getMetaDataTableName()).isEqualTo("flyway_schema_history");
        Assertions.assertThat(config.getFlywayFactory().getSqlMigrationSuffixes()).isEqualTo(List.of(".sql"));
        Assertions.assertThat(config.getFlywayFactory().getLocations()).isEqualTo(locations);
    }

    protected void verifySesConfig(
        BillyConfiguration config,
        boolean prependEnvironmentName,
        boolean internalOnly,
        boolean isLocal,
        String endpointOverride
    ) {
        Assertions.assertThat(config.getSesConfiguration().getRegion()).isEqualTo(Region.US_EAST_1);
        Assertions.assertThat(config.getSesConfiguration().getPrependEnvironmentName()).isEqualTo(prependEnvironmentName);
        Assertions.assertThat(config.getSesConfiguration().isLocal()).isEqualTo(isLocal);
        Assertions.assertThat(config.getSesConfiguration().getEndpointOverride()).isEqualTo(endpointOverride);
    }

    protected void verifyAvalara(BillyConfiguration config) {
        Assertions.assertThat(config.getAvalaraConfiguration().isCommitTaxTransactions()).isTrue();
    }

    protected void verifyMemcachedConfiguration(BillyConfiguration config, String cacheEndpoint) {
        Assertions.assertThat(config.getMemcachedConfiguration().getHostname()).isEqualTo(cacheEndpoint);
        Assertions.assertThat(config.getMemcachedConfiguration().getPort()).isEqualTo(11211);
    }

    protected void verifyStripe(BillyConfiguration config, String redirectUri, boolean enabled) {
        Assertions.assertThat(config.getStripeConfiguration().getEnabled()).isEqualTo(enabled);
        Assertions.assertThat(config.getStripeConfiguration().getRedirectUri()).isEqualTo(redirectUri);
    }

    protected void verifyDefaultPaymentJobConfiguration(BillyConfiguration configuration, boolean isEnabled) {
        Assertions.assertThat(configuration.getQuartzPaymentJobConfiguration().getEnabled()).isEqualTo(isEnabled);
        Assertions.assertThat(configuration.getQuartzPaymentJobConfiguration().getIsCron()).isTrue();
        Assertions.assertThat(configuration.getQuartzPaymentJobConfiguration().getHourOfDayForCronJob()).isEqualTo(18);
        Assertions.assertThat(configuration.getQuartzPaymentJobConfiguration().getMinuteOfHourForCronJob()).isEqualTo(0);
    }

    protected void verifyIntervalBasedPaymentJobConfiguration(BillyConfiguration configuration, int intervalInMinutes, int intervalInSeconds) {
        Assertions.assertThat(configuration.getQuartzPaymentJobConfiguration().getEnabled()).isEqualTo(true);
        Assertions.assertThat(configuration.getQuartzPaymentJobConfiguration().getIsCron()).isFalse();
        Assertions.assertThat(configuration.getQuartzPaymentJobConfiguration().getIntervalInMinutes()).isEqualTo(intervalInMinutes);
        Assertions.assertThat(configuration.getQuartzPaymentJobConfiguration().getIntervalInSeconds()).isEqualTo(intervalInSeconds);
    }

    protected void verifyIntervalBasedPaymentReconciliationJobConfiguration(
        BillyConfiguration configuration,
        int intervalInMinutes,
        int intervalInSeconds,
        boolean isEnabled
    ) {
        Assertions.assertThat(configuration.getQuartzPaymentReconciliationJobConfiguration().getEnabled()).isEqualTo(isEnabled);
        Assertions.assertThat(configuration.getQuartzPaymentReconciliationJobConfiguration().getIsCron()).isFalse();
        Assertions.assertThat(configuration.getQuartzPaymentReconciliationJobConfiguration().getIntervalInMinutes()).isEqualTo(intervalInMinutes);
        Assertions.assertThat(configuration.getQuartzPaymentReconciliationJobConfiguration().getIntervalInSeconds()).isEqualTo(intervalInSeconds);
    }

    protected void verifyAuditLogConfiguration(
        BillyConfiguration config,
        boolean enabled,
        int retention,
        ChronoUnit chronoUnit,
        boolean alwaysSetSessionParams
    ) {
        Assertions.assertThat(config.getAuditLogConfiguration().isBillyAuditOn()).isEqualTo(enabled);
        Assertions.assertThat(config.getAuditLogConfiguration().getPartitionDuration()).isEqualTo(1);
        Assertions.assertThat(config.getAuditLogConfiguration().getCreateAhead()).isEqualTo(2);
        Assertions.assertThat(config.getAuditLogConfiguration().getRetention()).isEqualTo(retention);
        Assertions.assertThat(config.getAuditLogConfiguration().getChronoUnit()).isEqualTo(chronoUnit);
        Assertions.assertThat(config.getAuditLogConfiguration().getAlwaysSetSessionParams()).isEqualTo(alwaysSetSessionParams);
    }

    private static String getS3Url(Region region) {
        return String.format("s3.%s.amazonaws.com", region);
    }

    protected void verifyIpBasedRateLimitingConfiguration(BillyConfiguration configuration, boolean isEnabled) {
        Assertions.assertThat(configuration.getIpBasedRequestRateLimiterConfiguration().getEnabled()).isEqualTo(isEnabled);
        Assertions.assertThat(configuration.getIpBasedRequestRateLimiterConfiguration().getMaxNumberOfRequestsPerMinute()).isEqualTo(5);
    }

    protected void verifyInvoiceEmailConfiguration(BillyConfiguration configuration, boolean isEnabled, boolean internalyOnly) {
        var invoiceEmailConfiguration = configuration.getInvoiceEmailConfiguration();
        Assertions.assertThat(invoiceEmailConfiguration.isEnabled()).isEqualTo(isEnabled);
        Assertions.assertThat(invoiceEmailConfiguration.getInternalOnly()).isEqualTo(internalyOnly);
    }

    protected void verifyCreditMemoEmailConfiguration(
        BillyConfiguration configuration,
        boolean isEnabled,
        boolean internalyOnly,
        String fromEmailAddress
    ) {
        var creditMemoEmailConfiguration = configuration.getCreditMemoEmailConfiguration();
        Assertions.assertThat(creditMemoEmailConfiguration.isEnabled()).isEqualTo(isEnabled);
        Assertions.assertThat(creditMemoEmailConfiguration.getInternalOnly()).isEqualTo(internalyOnly);
        Assertions.assertThat(creditMemoEmailConfiguration.getFromEmailAddress()).isEqualTo(fromEmailAddress);
    }

    protected void verifyAttachmentsConfiguration(BillyConfiguration configuration) {
        var attachmentsConfiguration = configuration.getAttachmentConfiguration();
        Assertions.assertThat(attachmentsConfiguration.getMaxFilesPerAccount()).isEqualTo(10);
    }

    protected void verifyMetricsUpdaterQuartzJobConfiguration(BillyConfiguration configuration, boolean enabled, long intervalInMins) {
        Assertions.assertThat(configuration.getQuartzMetricsUpdaterConfiguration().getIntervalInMinutes()).isEqualTo(intervalInMins);
        Assertions.assertThat(configuration.getQuartzMetricsUpdaterConfiguration().getEnabled()).isEqualTo(enabled);
    }

    protected void verifyRevenueRecognitionQuartzJobConfiguration(BillyConfiguration configuration, boolean enabled, long intervalInSeconds) {
        Assertions.assertThat(configuration.getQuartzRevenueRecognitionJobConfiguration().getIntervalInSeconds()).isEqualTo(intervalInSeconds);
        Assertions.assertThat(configuration.getQuartzRevenueRecognitionJobConfiguration().getEnabled()).isEqualTo(enabled);
    }

    protected void verifyApprovalFlowConfiguration(
        BillyConfiguration configuration,
        boolean isEnabled,
        boolean emailEnabled,
        boolean internalOnly,
        boolean sendEmailToApprovers,
        boolean sendEmailToOwners,
        boolean sendApprovalSlackNotifications,
        boolean sendApprovalSlackCommonNotifications,
        boolean sendApprovalSlackPrivateNotifications,
        String emailAddress
    ) {
        Assertions.assertThat(configuration.getApprovalFlowConfiguration().isEnabled()).isEqualTo(isEnabled);
        Assertions.assertThat(configuration.getApprovalFlowConfiguration().isEmailEnabled()).isEqualTo(emailEnabled);
        Assertions.assertThat(configuration.getApprovalFlowConfiguration().getSendEmailToApprovers()).isEqualTo(sendEmailToApprovers);
        Assertions.assertThat(configuration.getApprovalFlowConfiguration().getSendEmailToOwners()).isEqualTo(sendEmailToOwners);
        Assertions.assertThat(configuration.getApprovalFlowConfiguration().getSendApprovalSlackNotifications()).isEqualTo(
            sendApprovalSlackNotifications
        );
        Assertions.assertThat(configuration.getApprovalFlowConfiguration().getSendApprovalSlackCommonChannelNotifications()).isEqualTo(
            sendApprovalSlackCommonNotifications
        );
        Assertions.assertThat(configuration.getApprovalFlowConfiguration().getSendApprovalSlackPrivateNotifications()).isEqualTo(
            sendApprovalSlackPrivateNotifications
        );
        Assertions.assertThat(configuration.getApprovalFlowConfiguration().isEmailInternalOnly()).isEqualTo(internalOnly);
        Assertions.assertThat(configuration.getApprovalFlowConfiguration().getFromEmailAddress()).isEqualTo(emailAddress);
    }

    protected void verifyDunningConfiguration(
        BillyConfiguration configuration,
        boolean isEnabled,
        boolean isJobProcessingEnabled,
        int intervalInMinutes,
        String fromEmailAddress
    ) {
        Assertions.assertThat(configuration.getQuartzDunningConfiguration().getEnabled()).isEqualTo(isEnabled);
        Assertions.assertThat(configuration.getQuartzDunningConfiguration().getJobProcessingEnabled()).isEqualTo(isJobProcessingEnabled);
        Assertions.assertThat(configuration.getQuartzDunningConfiguration().getIntervalInMinutes()).isEqualTo(intervalInMinutes);
        Assertions.assertThat(configuration.getQuartzDunningConfiguration().getFromEmailAddress()).isEqualTo(fromEmailAddress);
    }

    protected void verifyRequestLoggingConfiguration(BillyConfiguration configuration, boolean enabled, String bucketName) {
        Assertions.assertThat(configuration.getRequestLoggingConfiguration().getRequestLoggingEnabled()).isEqualTo(enabled);
        Assertions.assertThat(configuration.getRequestLoggingConfiguration().getRequestLogS3BucketName()).isEqualTo(bucketName);
    }

    protected void verifyEventPumpConfig(BillyConfiguration configuration, boolean isEnabled, long delayBetweenRunsMs) {
        Assertions.assertThat(configuration.getEventPumpConfiguration().getEnabled()).isEqualTo(isEnabled);
        Assertions.assertThat(configuration.getEventPumpConfiguration().getDelayBetweenRunsMs()).isEqualTo(delayBetweenRunsMs);
    }

    protected void verifyLookerConfig(
        BillyConfiguration configuration,
        String baseUrl,
        String userRolePrefix,
        String userGroupPrefix,
        String tenantModelSuffix,
        String sharedFolderRoot,
        String tenantFolderRoot,
        boolean allowDevelopers,
        String exportBucketName
    ) {
        Assertions.assertThat(configuration.getLookerConfiguration().getBaseUrl()).isEqualTo(baseUrl);
        Assertions.assertThat(configuration.getLookerConfiguration().getUserRolePrefix()).isEqualTo(userRolePrefix);
        Assertions.assertThat(configuration.getLookerConfiguration().getUserGroupPrefix()).isEqualTo(userGroupPrefix);
        Assertions.assertThat(configuration.getLookerConfiguration().getTenantModelSuffix()).isEqualTo(tenantModelSuffix);
        Assertions.assertThat(configuration.getLookerConfiguration().getSharedFolderRoot()).isEqualTo(sharedFolderRoot);
        Assertions.assertThat(configuration.getLookerConfiguration().getTenantFolderRoot()).isEqualTo(tenantFolderRoot);
        Assertions.assertThat(configuration.getLookerConfiguration().getAllowDevelopers()).isEqualTo(allowDevelopers);
        Assertions.assertThat(configuration.getLookerConfiguration().getConfigExportBucketName()).isEqualTo(exportBucketName);
        Assertions.assertThat(configuration.getLookerConfiguration().getConfigExportJob().getEnabled()).isEqualTo(true);
        Assertions.assertThat(configuration.getLookerConfiguration().getConfigExportJob().getCronExpression()).isEqualTo("0 30 * ? * * *");
    }

    protected void verifyEmailNotificationQueueConfiguration(BillyConfiguration configuration, boolean enabled, Region region, String queueUrl) {
        Assertions.assertThat(configuration.getEmailNotificationQueueConfiguration().getEnabled()).isEqualTo(enabled);
        Assertions.assertThat(configuration.getEmailNotificationQueueConfiguration().getRegion()).isEqualTo(region);
        Assertions.assertThat(configuration.getEmailNotificationQueueConfiguration().getQueueUrl()).isEqualTo(queueUrl);
    }

    protected void verifyCustomPaymentTypeConfiguration(BillyConfiguration configuration) {
        Assertions.assertThat(configuration.getCustomPaymentTypeConfiguration().getIncludeInvoicePaymentType()).isFalse();
    }

    protected void verifyDosFilterConfiguration(
        BillyConfiguration configuration,
        boolean enabled,
        int requestTimeoutInSeconds,
        int maxRequestsPerSecond
    ) {
        Assertions.assertThat(configuration.getDosFilterConfiguration().getEnabled()).isEqualTo(enabled);
        Assertions.assertThat(configuration.getDosFilterConfiguration().getRequestTimeoutInSeconds()).isEqualTo(requestTimeoutInSeconds);
        Assertions.assertThat(configuration.getDosFilterConfiguration().getMaxRequestsPerSecond()).isEqualTo(maxRequestsPerSecond);
    }

    protected void verifyQueryTimeoutConfiguration(BillyConfiguration configuration, boolean enabled, int queryTimeoutInSeconds) {
        Assertions.assertThat(configuration.getQueryTimeoutConfiguration().getEnabled()).isEqualTo(enabled);
        Assertions.assertThat(configuration.getQueryTimeoutConfiguration().getQueryTimeoutInSeconds()).isEqualTo(queryTimeoutInSeconds);
    }

    protected void verifySubscriptionChargeChangeProcessorJobConfiguration(BillyConfiguration configuration, boolean enabled, String cronExpression) {
        Assertions.assertThat(configuration.getQuartzSubscriptionChargeChangeProcessorJobConfiguration().getEnabled()).isEqualTo(enabled);
        Assertions.assertThat(configuration.getQuartzSubscriptionChargeChangeProcessorJobConfiguration().getCronExpression()).isEqualTo(
            cronExpression
        );
    }

    protected void verifyNotificationConfiguration(
        BillyConfiguration configuration,
        boolean slackEnabled,
        boolean webhooksEnabled,
        long timeout,
        String topicArn,
        String siteUrl,
        boolean allowLocalUrls,
        String emailFromAddress
    ) {
        Assertions.assertThat(configuration.getNotificationConfiguration().getSlackEnabled()).isEqualTo(slackEnabled);
        Assertions.assertThat(configuration.getNotificationConfiguration().getWebhooksEnabled()).isEqualTo(webhooksEnabled);
        Assertions.assertThat(configuration.getNotificationConfiguration().getHttpCallTimeoutInSeconds()).isEqualTo(timeout);
        Assertions.assertThat(configuration.getNotificationConfiguration().getSnsTopicArn()).isEqualTo(topicArn);
        Assertions.assertThat(configuration.getNotificationConfiguration().getSiteUrl()).isEqualTo(siteUrl);
        Assertions.assertThat(configuration.getNotificationConfiguration().getAllowLocalUrls()).isEqualTo(allowLocalUrls);
        Assertions.assertThat(configuration.getNotificationConfiguration().getEmailFromAddress()).isEqualTo(emailFromAddress);
    }

    protected void verifyConfigCanBeSerialized(BillyConfiguration configuration) {
        assertThatNoException().isThrownBy(() -> JacksonProvider.defaultMapper().writeValueAsString(configuration));
    }

    protected void verifySearchIndexerConfiguration(BillyConfiguration configuration, boolean threadEnabled) {
        Assertions.assertThat(configuration.getSearchUpdaterThreadConfiguration().getEnabled()).isEqualTo(threadEnabled);
    }

    protected void verifyLogHeartbeatConfiguration(BillyConfiguration configuration, boolean enabled, int frequencyInSeconds) {
        Assertions.assertThat(configuration.getLogHeartbeatConfiguration().getEnabled()).isEqualTo(enabled);
        Assertions.assertThat(configuration.getLogHeartbeatConfiguration().getFrequencyInSeconds()).isEqualTo(frequencyInSeconds);
    }

    protected void verifyRateLimitConfiguration(BillyConfiguration configuration, boolean enabled, int maxCallsPerMinute) {
        Assertions.assertThat(configuration.getRateLimiterConfiguration().isEnabled()).isEqualTo(enabled);
        Assertions.assertThat(configuration.getRateLimiterConfiguration().getMaxTenantCallsPerMinute()).isEqualTo(maxCallsPerMinute);
    }

    protected void verifyKinesisConfiguration(
        BillyConfiguration configuration,
        boolean useEndpointOverride,
        String endpointOverride,
        Region region,
        Boolean forceShared
    ) {
        Assertions.assertThat(configuration.getKinesisConfiguration().getUseEndpointOverride()).isEqualTo(useEndpointOverride);
        Assertions.assertThat(configuration.getKinesisConfiguration().getEndpointOverride()).isEqualTo(endpointOverride);
        Assertions.assertThat(configuration.getKinesisConfiguration().getRegion()).isEqualTo(region);
        Assertions.assertThat(configuration.getKinesisConfiguration().getForceSharedCapacityMode()).isEqualTo(forceShared);
    }

    protected void verifyTaskQueueConfiguration(
        BillyConfiguration configuration,
        boolean enabled,
        boolean useQueue,
        int scheduledAndNotExecutedTaskTimeoutSeconds,
        int rescheduleTimedOutTaskAfterSeconds,
        long schedulerDelayBetweenRunsMs,
        Region region,
        String queueUrl
    ) {
        Assertions.assertThat(configuration.getTaskQueueConfiguration().getEnabled()).isEqualTo(enabled);
        Assertions.assertThat(configuration.getTaskQueueConfiguration().isUseQueue()).isEqualTo(useQueue);
        Assertions.assertThat(configuration.getTaskQueueConfiguration().getScheduledAndNotExecutedTaskTimeoutSeconds()).isEqualTo(
            scheduledAndNotExecutedTaskTimeoutSeconds
        );
        Assertions.assertThat(configuration.getTaskQueueConfiguration().getRescheduleTimedOutTaskAfterSeconds()).isEqualTo(
            rescheduleTimedOutTaskAfterSeconds
        );
        Assertions.assertThat(configuration.getTaskQueueConfiguration().getSchedulerDelayBetweenRunsMs()).isEqualTo(schedulerDelayBetweenRunsMs);
        Assertions.assertThat(configuration.getTaskQueueConfiguration().getArchiveTaskAfterDays()).isEqualTo(14);
        Assertions.assertThat(configuration.getTaskQueueConfiguration().getQueueConfiguration().getRegion()).isEqualTo(region);
        Assertions.assertThat(configuration.getTaskQueueConfiguration().getQueueConfiguration().getQueueUrl()).isEqualTo(queueUrl);
    }

    protected void verifySubscriptionLifecycleNotificiationConfiguration(BillyConfiguration configuration) {
        var config = configuration.getQuartzSubscriptionLifecycleNotificationsConfiguration();
        Assertions.assertThat(config.getEnabled()).isEqualTo(true);
        Assertions.assertThat(config.getIntervalInMinutes()).isEqualTo(30);
        Assertions.assertThat(config.getHoursBeforeEvent()).isEqualTo(1);
    }

    protected void verifyBulkRevenueRecognitionQuartzConfiguration(BillyConfiguration configuration, boolean enabled, long intervalInMinutes) {
        Assertions.assertThat(configuration.getQuartzBulkRevenueRecognitionConfiguration().getIntervalInMinutes()).isEqualTo(intervalInMinutes);
        Assertions.assertThat(configuration.getQuartzBulkRevenueRecognitionConfiguration().getEnabled()).isEqualTo(enabled);
    }
}
