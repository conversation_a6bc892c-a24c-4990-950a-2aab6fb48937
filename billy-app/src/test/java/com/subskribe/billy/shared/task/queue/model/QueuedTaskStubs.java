package com.subskribe.billy.shared.task.queue.model;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

public class QueuedTaskStubs {

    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final String ENTITY_ID = UUID.randomUUID().toString();
    private static final String PARENT_ID = UUID.randomUUID().toString();
    private static final String TASK_ID = UUID.randomUUID().toString();

    public static QueuedTask buildTask() {
        return ImmutableQueuedTask.builder()
            .taskId(TASK_ID)
            .status(TaskStatus.WAITING)
            .createdOn(Instant.ofEpochSecond(1704067200))
            .lastStartedOn(Instant.ofEpochSecond(1704067300))
            .completedOn(Instant.ofEpochSecond(1704067400))
            .delayedUntil(Instant.ofEpochSecond(1704067500))
            .module(new TaskModule("TestModule"))
            .type(new TaskType("TestType"))
            .taskData("TestTaskData")
            .taskMetadata(Map.of("key", "value"))
            .tenantId(TENANT_ID)
            .entityId(ENTITY_ID)
            .taskOrder(new DefaultTaskOrder("TestPartitionKey", 100L))
            .parent(ImmutableTaskParent.of("TestParentType", PARENT_ID))
            .build();
    }

    public static QueuedTaskRequest buildRequest() {
        return ImmutableQueuedTaskRequest.builder()
            .module(new TaskModule("TestModule"))
            .type(new TaskType("TestType"))
            .taskData("TestTaskData")
            .taskMetadata(Map.of("key", "value"))
            .tenantId(TENANT_ID)
            .entityId(ENTITY_ID)
            .taskOrder(Partitioned.with("TestPartitionKey"))
            .parent(ImmutableTaskParent.of("TestParentType", PARENT_ID))
            .build();
    }
}
