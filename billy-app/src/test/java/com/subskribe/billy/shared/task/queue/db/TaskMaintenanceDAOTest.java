package com.subskribe.billy.shared.task.queue.db;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.shared.infra.RuntimeInfoProvider;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTask;
import com.subskribe.billy.shared.task.queue.model.ImmutableTaskExecution;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs;
import com.subskribe.billy.shared.task.queue.model.TaskExecution;
import com.subskribe.billy.shared.task.queue.model.TaskExecutionStatus;
import com.subskribe.billy.shared.task.queue.model.TaskStatus;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.WithDb;
import java.time.Clock;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@AllowNonRlsDataAccess
class TaskMaintenanceDAOTest extends WithDb {

    private TaskDispatchDAO taskDispatchDAO;
    private TaskMaintenanceDAO taskMaintenanceDAO;
    private TaskExecutionDAO taskExecutionDAO;
    private Clock clock;

    private static final String TENANT_ID = "27b59431-0cc7-4c87-883b-be345e9bd525";

    @BeforeEach
    void setUp() {
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        clock = mock(Clock.class);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        taskDispatchDAO = new TaskDispatchDAO(new QueuedTaskMapper());
        taskMaintenanceDAO = new TaskMaintenanceDAO(dslContextProvider, new QueuedTaskMapper(), clock);
        taskExecutionDAO = new TaskExecutionDAO(dslContextProvider);
    }

    @Test
    void testOnlyCorrectTaskExecutionAreSetToWaiting() {
        TaskExecution taskExecutionToClean = ImmutableTaskExecution.builder().from(buildTaskExecution()).timeoutInSeconds(10).build();
        TaskExecution taskExecutionToNotClean = ImmutableTaskExecution.builder().from(buildTaskExecution()).timeoutInSeconds(60).build();
        TaskExecution savedTaskExecutionToClean = dslContextProvider
            .get(taskExecutionToClean.getTenantId())
            .transactionResult(configuration -> taskExecutionDAO.saveExecution(configuration, taskExecutionToClean));
        dslContextProvider
            .get(taskExecutionToClean.getTenantId())
            .transaction(configuration -> taskExecutionDAO.saveExecution(configuration, taskExecutionToNotClean));

        TaskExecution expectedCleanedExecution = ImmutableTaskExecution.builder()
            .from(savedTaskExecutionToClean)
            .status(TaskExecutionStatus.TIMED_OUT)
            .build();

        Instant now = savedTaskExecutionToClean.getCreatedOn().orElseThrow().plusSeconds(30);
        when(clock.instant()).thenReturn(now);

        List<TaskExecution> cleanedTaskExecutions = dslContextProvider
            .get()
            .transactionResult(configuration -> taskMaintenanceDAO.setTaskExecutionsToTimedOutIfTimeoutExceeded(configuration, 10, 100));

        assertThat(cleanedTaskExecutions)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("updatedOn")
            .containsExactly(expectedCleanedExecution);
    }

    @Test
    void testOnlyCorrectQueuedTaskRowsAreSetToWaiting() {
        Instant cutoffTime = Instant.now();
        QueuedTask taskToClean = ImmutableQueuedTask.builder()
            .from(QueuedTaskStubs.buildTask())
            .taskId(UUID.randomUUID().toString())
            .status(TaskStatus.IN_PROGRESS)
            .lastStartedOn(cutoffTime.minusSeconds(1))
            .build();
        QueuedTask taskNotToClean = ImmutableQueuedTask.builder()
            .from(QueuedTaskStubs.buildTask())
            .taskId(UUID.randomUUID().toString())
            .status(TaskStatus.IN_PROGRESS)
            .lastStartedOn(cutoffTime.plusSeconds(1))
            .build();

        QueuedTask savedToCleanTask = dslContextProvider
            .get(TENANT_ID)
            .transactionResult(configuration -> taskDispatchDAO.insertTask(configuration, taskToClean));
        dslContextProvider.get(TENANT_ID).transactionResult(configuration -> taskDispatchDAO.insertTask(configuration, taskNotToClean));
        QueuedTask expectedCleanedTask = ImmutableQueuedTask.builder().from(savedToCleanTask).status(TaskStatus.WAITING).build();

        List<QueuedTask> cleanedTasks = taskMaintenanceDAO.setTasksWithNoExecutionsBackToWaitingIfScheduledBeforeTime(cutoffTime);

        assertThat(cleanedTasks).containsExactly(expectedCleanedTask);
    }

    private TaskExecution buildTaskExecution() {
        return ImmutableTaskExecution.builder()
            .taskExecutionId(UUID.randomUUID().toString())
            .taskId(UUID.randomUUID().toString())
            .timeoutInSeconds(1)
            .status(TaskExecutionStatus.EXECUTING)
            .workerName(RuntimeInfoProvider.getHostIdentifier())
            .tenantId(TENANT_ID)
            .build();
    }
}
