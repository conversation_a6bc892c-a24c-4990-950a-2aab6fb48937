package com.subskribe.billy.shared.task.queue.distribution;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.model.QueueConfiguration;
import com.subskribe.billy.aws.sqs.SqsClientProvider;
import com.subskribe.billy.shared.task.queue.config.TaskQueueConfiguration;
import com.subskribe.billy.shared.task.queue.config.TaskQueueSqsConfiguration;
import com.subskribe.billy.shared.task.queue.exception.TaskDistributionException;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.GetQueueAttributesRequest;
import software.amazon.awssdk.services.sqs.model.GetQueueAttributesResponse;
import software.amazon.awssdk.services.sqs.model.QueueAttributeName;
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequest;
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequestEntry;

class SqsBackedTaskDistributorTest {

    private SqsClient sqsClient;
    private CapacityManager capacityManager;
    private QueueConfiguration queueConfiguration;
    private ObjectMapper objectMapper;

    private SqsBackedTaskDistributor sqsBackedTaskDistributor;

    @BeforeEach
    void setUp() {
        sqsClient = mock(SqsClient.class);
        capacityManager = mock(CapacityManager.class);
        objectMapper = mock(ObjectMapper.class);
        queueConfiguration = new TaskQueueSqsConfiguration(Region.US_EAST_1, "http://queue");
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        TaskQueueConfiguration taskQueueConfiguration = mock(TaskQueueConfiguration.class);
        SqsClientProvider sqsClientProvider = mock(SqsClientProvider.class);
        when(sqsClientProvider.setupClientForQueue(queueConfiguration)).thenReturn(sqsClient);
        when(billyConfiguration.getTaskQueueConfiguration()).thenReturn(taskQueueConfiguration);
        when(taskQueueConfiguration.getQueueConfiguration()).thenReturn(queueConfiguration);

        sqsBackedTaskDistributor = new SqsBackedTaskDistributor(sqsClientProvider, billyConfiguration, capacityManager, objectMapper);
    }

    @Test
    void testTaskSerializationErrorResultsInTaskDistributionException() throws Exception {
        QueuedTask queuedTask = QueuedTaskStubs.buildTask();
        when(objectMapper.writeValueAsString(queuedTask)).thenThrow(mock(JsonProcessingException.class));

        assertThatThrownBy(() -> sqsBackedTaskDistributor.distribute(List.of(queuedTask))).isInstanceOf(TaskDistributionException.class);
    }

    @Test
    void testMessageIsSent() throws Exception {
        String mockJson1 = RandomStringUtils.randomAlphanumeric(100);
        String mockJson2 = RandomStringUtils.randomAlphanumeric(100);
        QueuedTask task1 = QueuedTaskStubs.buildTask();
        QueuedTask task2 = ImmutableQueuedTask.builder().from(task1).taskId(UUID.randomUUID().toString()).build();
        when(objectMapper.writeValueAsString(task1)).thenReturn(mockJson1);
        when(objectMapper.writeValueAsString(task2)).thenReturn(mockJson2);

        SendMessageBatchRequestEntry expectedEntry1 = SendMessageBatchRequestEntry.builder().id(task1.getTaskId()).messageBody(mockJson1).build();
        SendMessageBatchRequestEntry expectedEntry2 = SendMessageBatchRequestEntry.builder().id(task2.getTaskId()).messageBody(mockJson2).build();
        SendMessageBatchRequest expectedRequest = SendMessageBatchRequest.builder()
            .queueUrl(queueConfiguration.getQueueUrl())
            .entries(expectedEntry1, expectedEntry2)
            .build();

        sqsBackedTaskDistributor.distribute(List.of(task1, task2));

        verify(sqsClient, times(1)).sendMessageBatch(expectedRequest);
    }

    @Test
    void testGetTotalCapacityUsesQueueSizeAndCapacityManager() {
        setupQueueSize(2);
        when(capacityManager.getAvailableCapacity()).thenReturn(10);

        int totalCapacity = sqsBackedTaskDistributor.getTotalCapacity();

        assertThat(totalCapacity).isEqualTo(8);
    }

    @Test
    void testGetTotalCapacityReturnsZeroIfNegative() {
        setupQueueSize(7);
        when(capacityManager.getAvailableCapacity()).thenReturn(3);

        int totalCapacity = sqsBackedTaskDistributor.getTotalCapacity();

        assertThat(totalCapacity).isEqualTo(0);
    }

    private void setupQueueSize(int size) {
        GetQueueAttributesRequest expectedRequest = GetQueueAttributesRequest.builder()
            .queueUrl(queueConfiguration.getQueueUrl())
            .attributeNames(QueueAttributeName.APPROXIMATE_NUMBER_OF_MESSAGES)
            .build();
        GetQueueAttributesResponse response = GetQueueAttributesResponse.builder()
            .attributes(Map.of(QueueAttributeName.APPROXIMATE_NUMBER_OF_MESSAGES, Integer.toString(size)))
            .build();
        when(sqsClient.getQueueAttributes(expectedRequest)).thenReturn(response);
    }
}
