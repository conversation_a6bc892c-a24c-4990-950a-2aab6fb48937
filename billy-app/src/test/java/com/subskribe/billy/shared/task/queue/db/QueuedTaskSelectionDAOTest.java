package com.subskribe.billy.shared.task.queue.db;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.shared.task.queue.model.DefaultTaskOrder;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskStubs;
import com.subskribe.billy.shared.task.queue.model.TaskStatus;
import com.subskribe.billy.test.WithDb;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@AllowNonRlsDataAccess
class QueuedTaskSelectionDAOTest extends WithDb {

    private TaskDispatchDAO taskDispatchDAO;
    private QueuedTaskSelectionDAO queuedTaskSelectionDAO;

    private long testSequenceNumberCounter = 0;
    private static final QueuedTask BASE_QUEUED_TASK = QueuedTaskStubs.buildTask();

    @BeforeEach
    void setUp() {
        QueuedTaskMapper queuedTaskMapper = new QueuedTaskMapper();
        taskDispatchDAO = new TaskDispatchDAO(queuedTaskMapper);
        queuedTaskSelectionDAO = new QueuedTaskSelectionDAO(dslContextProvider, queuedTaskMapper);
    }

    @SuppressWarnings("unused")
    @Test
    // This test is a bit verbose, but it's important to make sure that the query is selecting the correct tasks. With this
    // we can validate with a combination of tasks in all possible states, in one shot
    void testCorrectTasksAreSelected() {
        // Tenant 1 has a current running task for partition3
        QueuedTask tenant1RunningTask1 = generateAndSaveTestTask("tenant1", "partition3", TaskStatus.IN_PROGRESS, "t1rt1", Optional.empty());
        // Tenant 2 has a current running task for partition1
        QueuedTask tenant2RunningTask1 = generateAndSaveTestTask("tenant2", "partition1", TaskStatus.IN_PROGRESS, "t2rt1", Optional.empty());

        // Tenant 1 has more than 10 waiting tasks spread across partition keys
        QueuedTask tenant1WaitingTask1 = generateAndSaveWaitingTask("tenant1", "partition1", "t1wt1");
        QueuedTask tenant1WaitingTask2 = generateAndSaveWaitingTask("tenant1", "partition1", "t1wt2");
        QueuedTask tenant1WaitingTask3 = generateAndSaveWaitingTask("tenant1", "partition1", "t1wt3");
        QueuedTask tenant1WaitingTask4 = generateAndSaveWaitingTask("tenant1", "partition3", "t1wt4");
        QueuedTask tenant1WaitingTask5 = generateAndSaveDelayedTask("tenant1", "partition4", "t1wt5", Instant.now().plusSeconds(100));
        QueuedTask tenant1WaitingTask6 = generateAndSaveDelayedTask("tenant1", "partition5", "t1wt6", Instant.now().minusSeconds(100));
        QueuedTask tenant1WaitingTask7 = generateAndSaveWaitingTask("tenant1", "partition6", "t1wt7");
        QueuedTask tenant1WaitingTask8 = generateAndSaveWaitingTask("tenant1", "partition7", "t1wt8");
        QueuedTask tenant1WaitingTask9 = generateAndSaveWaitingTask("tenant1", "partition8", "t1wt9");
        QueuedTask tenant1WaitingTask10 = generateAndSaveWaitingTask("tenant1", "partition9", "t1wt10");
        QueuedTask tenant1WaitingTask11 = generateAndSaveWaitingTask("tenant1", "partition10", "t1wt11");
        QueuedTask tenant1WaitingTask12 = generateAndSaveWaitingTask("tenant1", "partition11", "t1wt12");
        QueuedTask tenant1WaitingTask13 = generateAndSaveWaitingTask("tenant1", "partition12", "t1wt13");
        QueuedTask tenant1WaitingTask14 = generateAndSaveWaitingTask("tenant1", "partition2", "t1wt14");
        QueuedTask tenant1WaitingTask15 = generateAndSaveWaitingTask("tenant1", "partition2", "t1wt15");
        QueuedTask tenant1WaitingTask16 = generateAndSaveWaitingTask("tenant1", "partition2", "t1wt16");

        // Tenant 2 has one waiting task for partition1, one for partition2, and two delayed tasks for partition3 and partition3, one of which is delayed to the future
        QueuedTask tenant2WaitingTask1 = generateAndSaveWaitingTask("tenant2", "partition1", "t2wt1");
        QueuedTask tenant2WaitingTask2 = generateAndSaveWaitingTask("tenant2", "partition2", "t2wt2");
        QueuedTask tenant2WaitingTask3 = generateAndSaveTestTask(
            "tenant2",
            "partition3",
            TaskStatus.WAITING,
            "t2wt3",
            Optional.of(Instant.now().minusSeconds(10))
        );
        QueuedTask tenant2WaitingTask4 = generateAndSaveTestTask(
            "tenant2",
            "partition4",
            TaskStatus.WAITING,
            "t2wt4",
            Optional.of(Instant.now().plusSeconds(10))
        );

        // The query should give us the following tasks:
        // - For tenant1, nothing for partition 3 since it's running. Only one from partition one. Nothing from partition4
        // because it's delayed to the future. Then one from each of the next partitions in list until we hit the maximum of 10.
        // - For tenant2, nothing for partition 1 since it's running, one from partition 2, and one from partition 3, since the delayed tasks delay until is in the past.
        List<QueuedTask> tasksReadyToRunImmediately = queuedTaskSelectionDAO.getTasksReadyToRunImmediately();
        List<QueuedTask> expectedTasks = List.of(
            tenant1WaitingTask1,
            tenant1WaitingTask6,
            tenant1WaitingTask7,
            tenant1WaitingTask8,
            tenant1WaitingTask9,
            tenant1WaitingTask10,
            tenant1WaitingTask11,
            tenant1WaitingTask12,
            tenant1WaitingTask13,
            tenant1WaitingTask14,
            tenant2WaitingTask2,
            tenant2WaitingTask3
        );

        assertThat(tasksReadyToRunImmediately.stream().map(QueuedTask::getTaskId).toList()).containsExactlyInAnyOrderElementsOf(
            expectedTasks.stream().map(QueuedTask::getTaskId).toList()
        );
    }

    private long getTestSequenceNumber() {
        return ++testSequenceNumberCounter;
    }

    private QueuedTask generateAndSaveWaitingTask(String tenantId, String partitionKey, String taskId) {
        return generateAndSaveTestTask(tenantId, partitionKey, TaskStatus.WAITING, taskId, Optional.empty());
    }

    @SuppressWarnings("SameParameterValue")
    private QueuedTask generateAndSaveDelayedTask(String tenantId, String partitionKey, String taskId, Instant delayedUntil) {
        return generateAndSaveTestTask(tenantId, partitionKey, TaskStatus.DELAYED, taskId, Optional.of(delayedUntil));
    }

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    private QueuedTask generateAndSaveTestTask(
        String tenantId,
        String partitionKey,
        TaskStatus taskStatus,
        String taskId,
        Optional<Instant> delayedUntil
    ) {
        QueuedTask queuedTask = ImmutableQueuedTask.builder()
            .from(BASE_QUEUED_TASK)
            .taskId(taskId)
            .tenantId(tenantId)
            .taskOrder(new DefaultTaskOrder(partitionKey, getTestSequenceNumber()))
            .status(taskStatus)
            .delayedUntil(delayedUntil)
            .build();
        return dslContextProvider.get().transactionResult(configuration -> taskDispatchDAO.insertTask(configuration, queuedTask));
    }
}
