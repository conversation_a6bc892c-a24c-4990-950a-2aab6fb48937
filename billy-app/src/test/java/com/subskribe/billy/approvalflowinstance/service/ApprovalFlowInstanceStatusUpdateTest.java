package com.subskribe.billy.approvalflowinstance.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.approvalflow.model.ApprovalRuleConditions;
import com.subskribe.billy.approvalflow.model.ApprovalTransitionRule;
import com.subskribe.billy.approvalflow.model.ApproverType;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstance;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceData;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceGroup;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceState;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceStatus;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceWorkflowStatus;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderResellerService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.template.model.DocumentCustomContent;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderGetService;
import com.subskribe.billy.template.services.DocumentCustomContentGetService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.OrderTermsService;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.User;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.UUID;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class ApprovalFlowInstanceStatusUpdateTest {

    private Order order;

    private static final String TEST_CHARGE_1_ID = "TEST_CHARGE_1_ID";
    private static final String TEST_CHARGE_2_ID = "TEST_CHARGE_2_ID";
    private static final String TEST_PLAN_ID = "TEST_PLAN_ID";

    private static final String orderLineItemId1 = UUID.randomUUID().toString();
    private static final String orderLineItemId2 = UUID.randomUUID().toString();

    private static final Metrics order_metrics = new Metrics(
        BigDecimal.valueOf(1000000.00),
        BigDecimal.valueOf(1000000.00),
        BigDecimal.ZERO,
        BigDecimal.valueOf(1000000.00),
        BigDecimal.valueOf(1000000.00),
        BigDecimal.valueOf(1000000.00),
        BigDecimal.valueOf(1000000.00),
        BigDecimal.ZERO,
        BigDecimal.ZERO,
        null
    );

    private static final BigDecimal deltaArrPercent = BigDecimal.valueOf(20);

    private final Map<String, Metrics> orderLineMetrics = Map.of(orderLineItemId1, order_metrics, orderLineItemId2, order_metrics);

    private static final String ORDER_DISCOUNT_GREATER_THAN_50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
    private static final String ORDER_DISCOUNT_GREATER_THAN_50_CONDITION = Base64.encodeBase64String(
        ORDER_DISCOUNT_GREATER_THAN_50.getBytes(StandardCharsets.UTF_8)
    );
    private OrderApprovalFlowEvaluator orderApprovalFlowEvaluator;

    private static final TimeZone UTC_TIME_ZONE = TimeZone.getTimeZone(ZoneOffset.UTC);
    private Optional<User> adminApprovalFlowBypassByUser = Optional.empty();
    private User adminUser;

    @BeforeEach
    public void testSetUp() {
        DocumentCustomContentGetService mockDocumentCustomContentGetService = mock(DocumentCustomContentGetService.class);
        ProductCatalogGetService mockProductCatalogGetService = mock(ProductCatalogGetService.class);
        DocumentTemplateGetService mockDocumentTemplateGetService = mock(DocumentTemplateGetService.class);
        CustomTemplateUpdatedOnOrderGetService mockCustomTemplateUpdatedOnOrderGetService = mock(CustomTemplateUpdatedOnOrderGetService.class);
        AccountGetService mockAccountGetService = mock(AccountGetService.class);
        orderApprovalFlowEvaluator = new OrderApprovalFlowEvaluator(
            mock(OrderGetService.class),
            mock(MetricsService.class),
            mockDocumentCustomContentGetService,
            mockProductCatalogGetService,
            mockDocumentTemplateGetService,
            mock(OrderTermsService.class),
            mockCustomTemplateUpdatedOnOrderGetService,
            mock(CustomFieldService.class),
            mock(CompositeOrderGetService.class),
            mock(OpportunityGetService.class),
            mockAccountGetService,
            mock(OrderResellerService.class)
        );

        adminApprovalFlowBypassByUser = Optional.empty();
        adminUser = new User();
        adminUser.setRole(Role.ADMIN);
        adminUser.setDisplayName("ADMIN USER");

        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setName("default");
        discountDetail.setPercent(BigDecimal.valueOf(0.4));

        OrderLineItem orderLineItem1 = new OrderLineItem();
        orderLineItem1.setChargeId(TEST_CHARGE_1_ID);
        orderLineItem1.setPlanId(TEST_PLAN_ID);
        orderLineItem1.setOrderLineId(orderLineItemId1);

        OrderLineItem orderLineItem2 = new OrderLineItem();
        orderLineItem2.setChargeId(TEST_CHARGE_2_ID);
        orderLineItem2.setPlanId(TEST_PLAN_ID);
        orderLineItem2.setOrderLineId(orderLineItemId2);

        order = new Order();
        order.setOrderType(OrderType.NEW);
        order.setLineItems(List.of(orderLineItem1, orderLineItem2));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(166666.67));
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(1000.00));
        order.getLineItems().get(0).setDiscounts(List.of(discountDetail));
        order.getLineItems().get(1).setDiscounts(List.of());
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(100000.00));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(1000.00));
        order.getLineItems().get(0).setOrderLineId(UUID.randomUUID().toString());
        order.getLineItems().get(1).setOrderLineId(UUID.randomUUID().toString());
        order.setLineItemsNetEffect(order.getLineItems());

        order.setPaymentTerm(PaymentTerm.NET30);
        order.setBillingCycle(new Recurrence(Cycle.MONTH, 1));
        order.setTotalAmount(BigDecimal.valueOf(1000000.00));
        order.setTotalListAmount(BigDecimal.valueOf(1500000.00));
        order.setTermLength(new Recurrence(Cycle.MONTH, 12));
        order.setDocumentCustomContent(new DocumentCustomContent(UUID.randomUUID().toString(), "orderId", "title", "NotNull"));
        order.setAutoRenew(true);

        DocumentCustomContent documentCustomContent = new DocumentCustomContent(UUID.randomUUID().toString(), "orderId", "title", "NotNull");
        when(mockDocumentCustomContentGetService.getDocumentCustomContentByOrderId(any())).thenReturn(Optional.of(documentCustomContent));
        Plan plan = new Plan();
        plan.setProductId("TEST_PRODUCT_ID");
        when(mockProductCatalogGetService.getPlan(any())).thenReturn(plan);

        Charge charge1 = new Charge();
        charge1.setChargeId(TEST_CHARGE_1_ID);
        charge1.setType(ChargeType.RECURRING);

        Charge charge2 = new Charge();
        charge2.setChargeId(TEST_CHARGE_2_ID);
        charge2.setType(ChargeType.RECURRING);

        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(Map.of(TEST_CHARGE_1_ID, charge1, TEST_CHARGE_2_ID, charge2));

        when(mockDocumentTemplateGetService.getDocumentTemplatesByTemplateIds(any())).thenReturn(List.of());
        when(mockCustomTemplateUpdatedOnOrderGetService.getUpdatedTemplatesOnOrder(any())).thenReturn(List.of());
    }

    @Test
    public void testAutoApproveIfNoApprovalFlowsExist() {
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup = new ApprovalFlowInstanceGroup();
        approvalFlowInstanceGroup.setApprovalFlowInstances(List.of());
        orderApprovalFlowEvaluator.evaluateOrderApprovalFlowStatus(
            order,
            approvalFlowInstanceGroup,
            order_metrics,
            deltaArrPercent,
            orderLineMetrics,
            UTC_TIME_ZONE,
            adminApprovalFlowBypassByUser
        );

        assertEquals(ApprovalFlowInstanceStatus.APPROVED, approvalFlowInstanceGroup.getApprovalStatus());
        assertEquals(ApprovalFlowInstanceWorkflowStatus.COMPLETED, approvalFlowInstanceGroup.getWorkflowStatus());
    }

    @Test
    public void testAutoApproveForOrdersWhichDontTriggerApprovalFlows() {
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup = new ApprovalFlowInstanceGroup();
        approvalFlowInstanceGroup.setApprovalFlowInstances(
            List.of(getApprovalFlowInstance(ORDER_DISCOUNT_GREATER_THAN_50_CONDITION, List.of("managerId")))
        );
        orderApprovalFlowEvaluator.evaluateOrderApprovalFlowStatus(
            order,
            approvalFlowInstanceGroup,
            order_metrics,
            deltaArrPercent,
            orderLineMetrics,
            UTC_TIME_ZONE,
            adminApprovalFlowBypassByUser
        );

        assertEquals(ApprovalFlowInstanceStatus.APPROVED, approvalFlowInstanceGroup.getApprovalStatus());
        assertEquals(ApprovalFlowInstanceWorkflowStatus.COMPLETED, approvalFlowInstanceGroup.getWorkflowStatus());
    }

    @Test
    public void testInProgressWorkflowForPendingApprovalForNullConditions() {
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup = new ApprovalFlowInstanceGroup();
        approvalFlowInstanceGroup.setApprovalFlowInstances(List.of(getApprovalFlowInstance(null, List.of("managerId"))));
        orderApprovalFlowEvaluator.evaluateOrderApprovalFlowStatus(
            order,
            approvalFlowInstanceGroup,
            order_metrics,
            deltaArrPercent,
            orderLineMetrics,
            UTC_TIME_ZONE,
            adminApprovalFlowBypassByUser
        );

        assertEquals(ApprovalFlowInstanceStatus.AWAITING_APPROVAL, approvalFlowInstanceGroup.getApprovalStatus());
        assertEquals(ApprovalFlowInstanceWorkflowStatus.IN_PROGRESS, approvalFlowInstanceGroup.getWorkflowStatus());
    }

    @Test
    public void testInProgressWorkflowForPendingApprovalForEmptyConditions() {
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup = new ApprovalFlowInstanceGroup();
        approvalFlowInstanceGroup.setApprovalFlowInstances(List.of(getApprovalFlowInstance(StringUtils.EMPTY, List.of("managerId"))));
        orderApprovalFlowEvaluator.evaluateOrderApprovalFlowStatus(
            order,
            approvalFlowInstanceGroup,
            order_metrics,
            deltaArrPercent,
            orderLineMetrics,
            UTC_TIME_ZONE,
            adminApprovalFlowBypassByUser
        );

        assertEquals(ApprovalFlowInstanceStatus.AWAITING_APPROVAL, approvalFlowInstanceGroup.getApprovalStatus());
        assertEquals(ApprovalFlowInstanceWorkflowStatus.IN_PROGRESS, approvalFlowInstanceGroup.getWorkflowStatus());
    }

    @Test
    public void testInProgressWorkflowForPendingApprovalConditions() {
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup = new ApprovalFlowInstanceGroup();
        approvalFlowInstanceGroup.setApprovalFlowInstances(
            List.of(getApprovalFlowInstance(ORDER_DISCOUNT_GREATER_THAN_50_CONDITION, List.of("managerId")))
        );

        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(2000000));
        orderApprovalFlowEvaluator.evaluateOrderApprovalFlowStatus(
            order,
            approvalFlowInstanceGroup,
            order_metrics,
            deltaArrPercent,
            orderLineMetrics,
            UTC_TIME_ZONE,
            adminApprovalFlowBypassByUser
        );

        assertEquals(ApprovalFlowInstanceStatus.AWAITING_APPROVAL, approvalFlowInstanceGroup.getApprovalStatus());
        assertEquals(ApprovalFlowInstanceWorkflowStatus.IN_PROGRESS, approvalFlowInstanceGroup.getWorkflowStatus());

        // calling update status again still puts it in the same state
        orderApprovalFlowEvaluator.evaluateOrderApprovalFlowStatus(
            order,
            approvalFlowInstanceGroup,
            order_metrics,
            deltaArrPercent,
            orderLineMetrics,
            UTC_TIME_ZONE,
            adminApprovalFlowBypassByUser
        );

        assertEquals(ApprovalFlowInstanceStatus.AWAITING_APPROVAL, approvalFlowInstanceGroup.getApprovalStatus());
        assertEquals(ApprovalFlowInstanceWorkflowStatus.IN_PROGRESS, approvalFlowInstanceGroup.getWorkflowStatus());
    }

    @Test
    public void testInProgressWorkflowForPendingApprovalWhenAnotherGroupApprovalIsNeeded() {
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup = new ApprovalFlowInstanceGroup();
        approvalFlowInstanceGroup.setApprovalFlowInstances(
            List.of(getApprovalFlowInstance(ORDER_DISCOUNT_GREATER_THAN_50_CONDITION, List.of("managerId", "dealDesk")))
        );
        approvalFlowInstanceGroup
            .getApprovalFlowInstances()
            .get(0)
            .getApprovalFlowData()
            .getStates()
            .get(0)
            .setStatus(ApprovalFlowInstanceStatus.APPROVED);

        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(2000000));
        orderApprovalFlowEvaluator.evaluateOrderApprovalFlowStatus(
            order,
            approvalFlowInstanceGroup,
            order_metrics,
            deltaArrPercent,
            orderLineMetrics,
            UTC_TIME_ZONE,
            adminApprovalFlowBypassByUser
        );

        assertEquals(ApprovalFlowInstanceStatus.AWAITING_APPROVAL, approvalFlowInstanceGroup.getApprovalStatus());
        assertEquals(ApprovalFlowInstanceWorkflowStatus.IN_PROGRESS, approvalFlowInstanceGroup.getWorkflowStatus());

        List<ApprovalFlowInstanceState> states = approvalFlowInstanceGroup.getApprovalFlowInstances().get(0).getApprovalFlowData().getStates();
        ApprovalFlowInstanceState managerState = states.stream().filter(state -> "managerId".equals(state.getApprovalGroupId())).toList().get(0);
        assertEquals(ApprovalFlowInstanceStatus.APPROVED, managerState.getStatus());
        ApprovalFlowInstanceState dealDeskState = states.stream().filter(state -> "dealDesk".equals(state.getApprovalGroupId())).toList().get(0);
        assertEquals(ApprovalFlowInstanceStatus.AWAITING_APPROVAL, dealDeskState.getStatus());
    }

    @Test
    public void testApprovalFlowAdminByPass() {
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup = new ApprovalFlowInstanceGroup();
        approvalFlowInstanceGroup.setApprovalFlowInstances(
            List.of(getApprovalFlowInstance(ORDER_DISCOUNT_GREATER_THAN_50_CONDITION, List.of("managerId", "dealDesk")))
        );
        approvalFlowInstanceGroup
            .getApprovalFlowInstances()
            .get(0)
            .getApprovalFlowData()
            .getStates()
            .get(0)
            .setStatus(ApprovalFlowInstanceStatus.APPROVED);

        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(2000000));

        orderApprovalFlowEvaluator.evaluateOrderApprovalFlowStatus(
            order,
            approvalFlowInstanceGroup,
            order_metrics,
            deltaArrPercent,
            orderLineMetrics,
            UTC_TIME_ZONE,
            Optional.of(adminUser)
        );

        assertEquals(ApprovalFlowInstanceStatus.APPROVED, approvalFlowInstanceGroup.getApprovalStatus());
        assertEquals(ApprovalFlowInstanceWorkflowStatus.COMPLETED, approvalFlowInstanceGroup.getWorkflowStatus());
        assertEquals("Approvals were bypassed by admin: ADMIN USER", approvalFlowInstanceGroup.getNote());
    }

    @Test
    public void testWorkFlowApprovedIfAllStatesAreApproved() {
        ApprovalFlowInstanceGroup approvalFlowInstanceGroup = new ApprovalFlowInstanceGroup();
        approvalFlowInstanceGroup.setApprovalFlowInstances(
            List.of(getApprovalFlowInstance(ORDER_DISCOUNT_GREATER_THAN_50_CONDITION, List.of("managerId", "dealDesk")))
        );
        approvalFlowInstanceGroup
            .getApprovalFlowInstances()
            .get(0)
            .getApprovalFlowData()
            .getStates()
            .forEach(state -> state.setStatus(ApprovalFlowInstanceStatus.APPROVED));

        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(2000000));
        orderApprovalFlowEvaluator.evaluateOrderApprovalFlowStatus(
            order,
            approvalFlowInstanceGroup,
            order_metrics,
            deltaArrPercent,
            orderLineMetrics,
            UTC_TIME_ZONE,
            adminApprovalFlowBypassByUser
        );

        assertEquals(ApprovalFlowInstanceStatus.APPROVED, approvalFlowInstanceGroup.getApprovalStatus());
        assertEquals(ApprovalFlowInstanceWorkflowStatus.COMPLETED, approvalFlowInstanceGroup.getWorkflowStatus());
    }

    private ApprovalFlowInstance getApprovalFlowInstance(String condition, List<String> approvalGroupIds) {
        ApprovalFlowInstance approvalFlowInstance = new ApprovalFlowInstance();

        ApprovalFlowInstanceData data = getApprovalFlowInstanceData(condition, approvalGroupIds);
        approvalFlowInstance.setApprovalFlowId(data.getApprovalFlowId());
        approvalFlowInstance.setApprovalFlowData(data);

        return approvalFlowInstance;
    }

    private ApprovalFlowInstanceData getApprovalFlowInstanceData(String condition, List<String> approvalGroupIds) {
        ApprovalFlowInstanceData approvalFlowInstanceData = new ApprovalFlowInstanceData();

        List<ApprovalFlowInstanceState> states = getApprovalFlowInstanceStates(approvalGroupIds);
        List<ApprovalTransitionRule> transitionRules = new ArrayList<>();

        String startingState = "start";
        for (var state : states) {
            ApprovalTransitionRule transitionRule = new ApprovalTransitionRule();
            transitionRule.setTransitionRuleId("rule-" + UUID.randomUUID());
            transitionRule.setRuleConditions(new ApprovalRuleConditions(condition, null));
            transitionRule.setFromState(startingState);
            transitionRule.setToState(state.getApprovalStateId());
            startingState = state.getApprovalStateId();

            transitionRules.add(transitionRule);
        }

        approvalFlowInstanceData.setStates(states);
        approvalFlowInstanceData.setTransitionRules(transitionRules);
        return approvalFlowInstanceData;
    }

    private List<ApprovalFlowInstanceState> getApprovalFlowInstanceStates(List<String> approvalGroupIds) {
        List<ApprovalFlowInstanceState> approvalFlowInstanceStates = new ArrayList<>();
        for (var approvalGroupId : approvalGroupIds) {
            var state = new ApprovalFlowInstanceState();
            state.setApprovalStateId("state-" + UUID.randomUUID());
            state.setApproverId(approvalGroupId);
            state.setApprovalGroupId(approvalGroupId);
            state.setApproverType(ApproverType.USER);
            state.setStatus(ApprovalFlowInstanceStatus.INACTIVE);
            approvalFlowInstanceStates.add(state);
        }
        return approvalFlowInstanceStates;
    }
}
