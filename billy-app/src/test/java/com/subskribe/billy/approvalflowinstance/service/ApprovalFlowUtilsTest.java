package com.subskribe.billy.approvalflowinstance.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HexFormat;
import java.util.Set;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;

public class ApprovalFlowUtilsTest {

    @Test
    public void testValidContentHash() throws NoSuchAlgorithmException {
        String content = "This is a test string";
        String expectedHash = generateExpectedHash(content);
        String actualHash = ApprovalFlowUtils.getPredefinedTermHash(content);
        assertEquals(expectedHash, actualHash);
    }

    @Test
    void testEmptyContentHash() throws NoSuchAlgorithmException {
        String content = "";
        String expectedHash = generateExpectedHash(content);
        String actualHash = ApprovalFlowUtils.getPredefinedTermHash(content);
        assertEquals(expectedHash, actualHash);
    }

    @Test
    void testNullContentThrowsNullPointerException() {
        assertThrows(NullPointerException.class, () -> ApprovalFlowUtils.getPredefinedTermHash(null));
    }

    @Test
    public void testOrderCondition() {
        String orderCondition = "{\"and\":[{\"==\":[{\"var\":\"orderType\"},\"AMENDMENT\"]}]}";
        JSONObject orderConditionJsonObject = new JSONObject(orderCondition);
        Set<String> approvalFlowConditionVariables = ApprovalFlowUtils.extractApprovalFlowConditionVariables(orderConditionJsonObject);
        assertEquals(1, approvalFlowConditionVariables.size());
        assertTrue(approvalFlowConditionVariables.contains(ApprovalFlowUtils.ORDER_CONDITION_ORDER_TYPE_VARIABLE));
    }

    @Test
    public void testOrderLineCondition() {
        String orderLineCondition =
            "{\"and\":[{\"==\":[{\"var\":\"orderLineActionType\"},\"ADD\"]},{\"==\":[{\"var\":\"chargeType\"},\"RECURRING\"]}]}";
        JSONObject orderLineConditionJsonObject = new JSONObject(orderLineCondition);
        Set<String> approvalFlowConditionVariables = ApprovalFlowUtils.extractApprovalFlowConditionVariables(orderLineConditionJsonObject);
        assertEquals(2, approvalFlowConditionVariables.size());
        assertTrue(approvalFlowConditionVariables.contains(ApprovalFlowUtils.ORDER_LINE_CONDITION_ACTION_TYPE_VARIABLE));
        assertTrue(approvalFlowConditionVariables.contains(ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_TYPE_VARIABLE));
    }

    @Test
    public void testOrderAndOrderLineConditionsCombined() {
        String orderAndOrderLineCombinedConditions =
            "{\"and\":[{\"and\":[{\"==\":[{\"var\":\"billingCycle\"},\"MONTH\"]},{\"==\":[{\"var\":\"orderType\"},\"NEW\"]}]},{\"and\":[{\"==\":[{\"var\":\"orderLineActionType\"},\"ADD\"]},{\"==\":[{\"var\":\"chargeType\"},\"RECURRING\"]}]}]}";
        JSONObject orderAndOrderLineCombinedConditionsJsonObject = new JSONObject(orderAndOrderLineCombinedConditions);
        Set<String> approvalFlowConditionVariables = ApprovalFlowUtils.extractApprovalFlowConditionVariables(
            orderAndOrderLineCombinedConditionsJsonObject
        );
        assertEquals(4, approvalFlowConditionVariables.size());
        assertTrue(approvalFlowConditionVariables.contains(ApprovalFlowUtils.ORDER_CONDITION_BILLING_CYCLE_VARIABLE));
        assertTrue(approvalFlowConditionVariables.contains(ApprovalFlowUtils.ORDER_CONDITION_ORDER_TYPE_VARIABLE));
        assertTrue(approvalFlowConditionVariables.contains(ApprovalFlowUtils.ORDER_LINE_CONDITION_ACTION_TYPE_VARIABLE));
        assertTrue(approvalFlowConditionVariables.contains(ApprovalFlowUtils.ORDER_LINE_CONDITION_CHARGE_TYPE_VARIABLE));
    }

    private String generateExpectedHash(String content) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = messageDigest.digest(content.getBytes(StandardCharsets.UTF_8));
        return HexFormat.of().formatHex(hashBytes);
    }
}
