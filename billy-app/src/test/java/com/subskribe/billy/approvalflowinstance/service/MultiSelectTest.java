package com.subskribe.billy.approvalflowinstance.service;

import io.github.jamsesso.jsonlogic.JsonLogic;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class MultiSelectTest {

    private final JsonLogic jsonLogic = new JsonLogic();

    @Test
    public void multiSelectCustomOperationExample() throws Exception {
        // Register an operation.
        jsonLogic.addOperation("checkOperation", args -> {
            if (args.length == 2 && args[1] instanceof List<?> vals) {
                return vals.contains(args[0]);
            }
            return false;
        });

        Map<String, Object> data = new HashMap<>();
        data.put("matchOn", "a");
        data.put("fieldVals", List.of("a", "b", "c"));
        // Evaluate the result.
        Boolean result = (Boolean) jsonLogic.apply("{\"checkOperation\": [{\"var\":\"matchOn\"}, {\"var\":\"fieldVals\"}]}", data);
        Assertions.assertThat(result).isTrue();

        data.put("matchOn", "f");
        result = (Boolean) jsonLogic.apply("{\"checkOperation\": [{\"var\":\"matchOn\"}, {\"var\":\"fieldVals\"}]}", data);
        Assertions.assertThat(result).isFalse();
    }
}
