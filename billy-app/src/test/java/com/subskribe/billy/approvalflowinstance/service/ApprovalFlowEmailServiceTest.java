package com.subskribe.billy.approvalflowinstance.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceStatus;
import com.subskribe.billy.shared.enums.OrderType;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class ApprovalFlowEmailServiceTest {

    @Test
    public void getApprovalFlowEmailSubject() {
        String accountName = UUID.randomUUID().toString();
        String orderId = UUID.randomUUID().toString();

        String subject = ApprovalFlowEmailService.getSubject(ApprovalFlowInstanceStatus.AWAITING_APPROVAL, accountName, OrderType.NEW, orderId);
        assertEquals("Action Required: New Order (" + orderId + ") for " + accountName, subject);

        subject = ApprovalFlowEmailService.getSubject(ApprovalFlowInstanceStatus.APPROVED, accountName, OrderType.AMENDMENT, orderId);
        assertEquals("Approved: Amendment Order (" + orderId + ") for " + accountName, subject);

        assertThrows(IllegalArgumentException.class, () ->
            ApprovalFlowEmailService.getSubject(ApprovalFlowInstanceStatus.CANCELLED, accountName, OrderType.AMENDMENT, orderId)
        );
    }
}
