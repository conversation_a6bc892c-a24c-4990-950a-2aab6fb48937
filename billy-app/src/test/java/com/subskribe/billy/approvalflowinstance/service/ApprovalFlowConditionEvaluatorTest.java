package com.subskribe.billy.approvalflowinstance.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.approvalflow.model.ApprovalRuleConditions;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowContext;
import com.subskribe.billy.approvalflowinstance.model.ImmutableApprovalFlowContext;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldSource;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.entitycache.EntityCache;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.TenantDiscount;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.template.model.DocumentCustomContent;
import com.subskribe.billy.template.model.DocumentTemplate;
import graphql.annotations.processor.util.Base64;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ApprovalFlowConditionEvaluatorTest {

    private static final String TEST_CHARGE_1_ID = "TEST_CHARGE_1_ID";
    private static final String TEST_CHARGE_2_ID = "TEST_CHARGE_2_ID";
    private static final String TEST_DISCOUNT_CHARGE = "TEST_CHARGE_3_ID";
    private static final String TEST_PLAN_ID = "TEST_PLAN_ID";
    private static final Plan PLAN = new Plan();
    private static final String TEST_PRODUCT_ID = "TEST_PRODUCT_ID";
    private static final String TEST_PRODUCT_NAME = "test";
    private static final String TEST_OPPORTUNITY = "TEST-OPPORTUNITY";

    private static final Product PRODUCT = new Product(
        UUID.randomUUID(),
        EntityFixture.ALL_ENTITY_IDS,
        TEST_PRODUCT_ID,
        true,
        TEST_PRODUCT_NAME,
        TEST_PRODUCT_NAME,
        "test",
        "TEST_PRODUCT_SKU",
        null,
        Instant.now(),
        null
    );
    private static final ProductCategory PRODUCT_CATEGORY = new ProductCategory();
    private static final String TEST_PRODUCT_CATEGORY_ID = "TEST_PRODUCT_CATEGORY_ID";
    private static final TenantDiscount TENANT_DISCOUNT = new TenantDiscount();
    private static final String TEST_PREDEFINED_DISCOUNT_ID = "TEST_PREDEFINED_DISCOUNT_ID";
    private static final String ACCOUNT_PICKLIST_CUSTOM_FIELD_NAME = "account_picklist_custom_field";
    private static final String ACCOUNT_PICKLIST_CUSTOM_FIELD_VALUE = "account-picklist-1";
    private static final String ACCOUNT_STRING_CUSTOM_FIELD_NAME = "account_string_custom_field";
    private static final String ACCOUNT_STRING_CUSTOM_FIELD_VALUE = "account-string-1";
    private static final String OPPORTUNITY_PICKLIST_CUSTOM_FIELD_NAME = "opportunity_picklist_custom_field";
    private static final String OPPORTUNITY_PICKLIST_CUSTOM_FIELD_VALUE = "opportunity-picklist-1";
    private static final String OPPORTUNITY_STRING_CUSTOM_FIELD_NAME = "opportunity_string_custom_field";
    private static final String OPPORTUNITY_STRING_CUSTOM_FIELD_VALUE = "opportunity-string-1";
    private static final String ORDER_PICKLIST_CUSTOM_FIELD_NAME = "order_picklist_custom_field";
    private static final String ORDER_PICKLIST_CUSTOM_FIELD_VALUE = "order-picklist-1";
    private static final String ORDER_STRING_CUSTOM_FIELD_NAME = "order_string_custom_field";
    private static final String ORDER_STRING_CUSTOM_FIELD_VALUE = "order-string-1";
    private static final String ORDER_ID = UUID.randomUUID().toString();
    private static final String ACCOUNT_ID = UUID.randomUUID().toString();
    private static final String ORDER_LINE_1_ID = UUID.randomUUID().toString();
    private static final String ORDER_LINE_2_ID = UUID.randomUUID().toString();
    private static final String ORDER_LINE_PICKLIST_CUSTOM_FIELD_NAME = "orderLine_picklist_custom_field";
    private static final String ORDER_LINE_PICKLIST_CUSTOM_FIELD_VALUE = "orderline-picklist-1";
    private static final String ORDER_LINE_STRING_CUSTOM_FIELD_NAME = "orderLine_string_custom_field";
    private static final String ORDER_LINE_STRING_CUSTOM_FIELD_VALUE = "orderline-string-1";
    private static final String PLAN_PICKLIST_CUSTOM_FIELD_NAME = "plan_picklist_custom_field";
    private static final String PLAN_PICKLIST_CUSTOM_FIELD_VALUE = "plan-picklist-1";
    private static final String PLAN_STRING_CUSTOM_FIELD_NAME = "plan_string_custom_field";
    private static final String PLAN_STRING_CUSTOM_FIELD_VALUE = "plan-string-1";
    private static final String CHARGE_PICKLIST_CUSTOM_FIELD_NAME = "charge_picklist_custom_field";
    private static final String CHARGE_PICKLIST_CUSTOM_FIELD_VALUE = "charge-picklist-1";
    private static final String CHARGE_STRING_CUSTOM_FIELD_NAME = "charge_string_custom_field";
    private static final String CHARGE_STRING_CUSTOM_FIELD_VALUE = "charge-string-1";

    private static final Metrics ORDER_METRICS = new Metrics(
        BigDecimal.valueOf(1000000.00),
        BigDecimal.valueOf(1000000.00),
        BigDecimal.ZERO,
        BigDecimal.valueOf(1000000.00),
        BigDecimal.valueOf(1000000.00),
        BigDecimal.valueOf(1000000.00),
        BigDecimal.valueOf(1000000.00),
        BigDecimal.ZERO,
        BigDecimal.valueOf(200000),
        null
    );

    private static final BigDecimal DELTA_ARR_PERCENT = BigDecimal.valueOf(25);

    private static final Map<String, Metrics> ORDER_LINE_METRICS = new HashMap<>();
    private static final DocumentCustomContent DOCUMENT_CUSTOM_CONTENT = new DocumentCustomContent(
        UUID.randomUUID().toString(),
        "orderId",
        "title",
        "NotNull"
    );
    private List<DocumentTemplate> predefinedTermsOnOrder;
    private Set<String> modifiedPredefinedTermsOnOrder;
    private EntityCache<String, Plan> planEntityCache;
    private EntityCache<String, Product> productEntityCache;
    private Order order;
    private static final TimeZone UTC_TIME_ZONE = TimeZone.getTimeZone(ZoneOffset.UTC);
    private Map<String, Charge> chargeMap;
    private CustomField customFieldsOnAccount;
    private CustomField customFieldsOnOrder;
    private CustomField customFieldsOnOpportunity;
    private Map<String, CustomField> customFieldsOnOrderLineMap;
    private Map<String, CustomField> customFieldsOnChargesMap;
    private Map<String, CustomField> customFieldsOnPlansMap;
    private ApprovalFlowContext initialApprovalFlowContext;
    private ApprovalFlowContext approvalFlowContext;

    @BeforeEach
    public void testSetUp() {
        ProductCatalogGetService mockProductCatalogGetService = mock(ProductCatalogGetService.class);
        planEntityCache = EntityCache.of(mockProductCatalogGetService::getPlan);
        productEntityCache = EntityCache.of(mockProductCatalogGetService::getProduct);

        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setName("default");
        discountDetail.setPercent(BigDecimal.valueOf(0.4));

        order = new Order();
        order.setOrderType(OrderType.NEW);
        order.setLineItems(List.of(new OrderLineItem(), new OrderLineItem()));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(166666.67));
        order.getLineItems().get(0).setDiscounts(List.of(discountDetail));
        order.getLineItems().get(0).setChargeId(TEST_CHARGE_1_ID);
        order.getLineItems().get(0).setPlanId(TEST_PLAN_ID);
        order.getLineItems().get(0).setQuantity(10000);
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(100000.00));
        order.getLineItems().get(0).setOrderLineId(ORDER_LINE_1_ID);
        order.getLineItems().get(0).setAction(ActionType.ADD);

        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(1000.00));
        order.getLineItems().get(1).setDiscounts(List.of());
        order.getLineItems().get(1).setChargeId(TEST_CHARGE_2_ID);
        order.getLineItems().get(1).setPlanId(TEST_PLAN_ID);
        order.getLineItems().get(1).setQuantity(100);
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(1000.00));
        order.getLineItems().get(1).setOrderLineId(ORDER_LINE_2_ID);
        order.getLineItems().get(1).setAction(ActionType.ADD);
        order.setLineItemsNetEffect(order.getLineItems());

        order.setPaymentTerm(PaymentTerm.NET30);
        order.setBillingCycle(new Recurrence(Cycle.MONTH, 1));
        order.setTotalAmount(BigDecimal.valueOf(1000000.00));
        order.setTotalListAmount(BigDecimal.valueOf(3000000.00));
        order.setTotalListAmountBeforeOverride(BigDecimal.ZERO);
        order.setTermLength(new Recurrence(Cycle.MONTH, 12));
        order.setDocumentCustomContent(new DocumentCustomContent(UUID.randomUUID().toString(), "orderId", "title", "NotNull"));
        order.setAutoRenew(true);
        order.setSfdcOpportunityId(TEST_OPPORTUNITY);

        PLAN.setPlanId(TEST_PLAN_ID);
        PLAN.setProductId(TEST_PRODUCT_ID);

        PRODUCT.setProductId(TEST_PRODUCT_ID);
        PRODUCT_CATEGORY.setProductCategoryId(TEST_PRODUCT_CATEGORY_ID);
        PRODUCT.setProductCategory(PRODUCT_CATEGORY);

        TENANT_DISCOUNT.setId(TEST_PREDEFINED_DISCOUNT_ID);
        order.setPredefinedDiscounts(List.of(TENANT_DISCOUNT));

        ORDER_LINE_METRICS.put(
            ORDER_LINE_1_ID,
            new Metrics(
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.ZERO,
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                null
            )
        );
        ORDER_LINE_METRICS.put(
            ORDER_LINE_2_ID,
            new Metrics(
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.ZERO,
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                null
            )
        );

        Charge charge1 = new Charge();
        charge1.setChargeId(TEST_CHARGE_1_ID);
        charge1.setType(ChargeType.RECURRING);
        charge1.setCreditable(false);

        Charge charge2 = new Charge();
        charge2.setChargeId(TEST_CHARGE_2_ID);
        charge2.setType(ChargeType.ONE_TIME);
        charge2.setCreditable(true);

        Charge discountCharge = new Charge();
        discountCharge.setChargeId(TEST_DISCOUNT_CHARGE);
        discountCharge.setType(ChargeType.ONE_TIME);
        discountCharge.setIsDiscount(true);

        chargeMap = Map.of(TEST_CHARGE_1_ID, charge1, TEST_CHARGE_2_ID, charge2, TEST_DISCOUNT_CHARGE, discountCharge);

        when(mockProductCatalogGetService.getPlan(any())).thenReturn(PLAN);
        when(mockProductCatalogGetService.getProduct(any())).thenReturn(PRODUCT);

        customFieldsOnAccount = null;
        customFieldsOnOpportunity = null;
        customFieldsOnOrder = null;

        initialApprovalFlowContext = ImmutableApprovalFlowContext.builder()
            .predefinedTermsOnOrder(List.of())
            .modifiedPredefinedTermsOnOrder(Set.of())
            .timeZone(UTC_TIME_ZONE)
            .opportunityId(order.getSfdcOpportunityId())
            .chargeMap(chargeMap)
            .customFieldOnAccount(customFieldsOnAccount)
            .customFieldsOnOrder(customFieldsOnOrder)
            .customFieldsOnOpportunity(customFieldsOnOpportunity)
            .customFieldOnOrderLinesMap(new HashMap<>())
            .customFieldsOnPlansMap(new HashMap<>())
            .customFieldsOnChargesMap(new HashMap<>())
            .planIds(List.of(TEST_PLAN_ID))
            .chargeIds(List.of(TEST_CHARGE_1_ID, TEST_CHARGE_2_ID))
            .productIds(List.of(TEST_PRODUCT_ID))
            .isReseller(false)
            .build();
    }

    private static Map<String, CustomFieldValue> getCustomFieldMap(String id, String customFieldName, String customFieldValue) {
        Map<String, CustomFieldValue> customFieldMap = new HashMap<>();
        customFieldMap.put(
            id,
            new CustomFieldValue(
                CustomFieldType.PICKLIST,
                customFieldName,
                customFieldName,
                null,
                List.of(customFieldValue),
                List.of(customFieldValue),
                false,
                CustomFieldSource.USER,
                null
            )
        );
        return customFieldMap;
    }

    private void validateOrderLevelCondition(String orderCondition, boolean expectedResult, ApprovalFlowContext approvalFlowContext) {
        JSONObject condition = new JSONObject(orderCondition);
        boolean conditionEvalResult = OrderApprovalFlowEvaluator.checkIfOrderLevelConditionApplies(
            order,
            condition,
            ORDER_METRICS,
            DELTA_ARR_PERCENT,
            approvalFlowContext
        );
        assertEquals(expectedResult, conditionEvalResult);

        ApprovalRuleConditions approvalRuleConditions = new ApprovalRuleConditions(Base64.toBase64(orderCondition), null);
        conditionEvalResult = OrderApprovalFlowEvaluator.checkApprovalFlowConditions(
            order,
            approvalRuleConditions,
            ORDER_METRICS,
            DELTA_ARR_PERCENT,
            ORDER_LINE_METRICS,
            planEntityCache,
            productEntityCache,
            approvalFlowContext
        );
        assertEquals(expectedResult, conditionEvalResult);
    }

    private void validateOrderLineLevelCondition(String orderLineCondition, boolean expectedResult, ApprovalFlowContext approvalFlowContext) {
        JSONObject condition = new JSONObject(orderLineCondition);
        boolean conditionEvalResult = OrderApprovalFlowEvaluator.checkIfOrderLineConditionApplies(
            order,
            condition,
            ORDER_LINE_METRICS,
            planEntityCache,
            productEntityCache,
            approvalFlowContext
        );
        assertEquals(expectedResult, conditionEvalResult);

        ApprovalRuleConditions approvalRuleConditions = new ApprovalRuleConditions(null, Base64.toBase64(orderLineCondition));
        conditionEvalResult = OrderApprovalFlowEvaluator.checkApprovalFlowConditions(
            order,
            approvalRuleConditions,
            ORDER_METRICS,
            DELTA_ARR_PERCENT,
            ORDER_LINE_METRICS,
            planEntityCache,
            productEntityCache,
            approvalFlowContext
        );
        assertEquals(expectedResult, conditionEvalResult);
    }

    private DocumentTemplate getPredefinedTerm() {
        DocumentTemplate predefinedTerm = new DocumentTemplate();
        predefinedTerm.setId(UUID.randomUUID().toString());
        predefinedTerm.setName("Test PredefinedTerm");
        predefinedTerm.setTemplateId(UUID.randomUUID().toString());
        return predefinedTerm;
    }

    @Test
    public void TestNullConditionsReturnTrue() {
        ApprovalRuleConditions approvalRuleConditions = new ApprovalRuleConditions(null, null);
        boolean conditionEvalResult = OrderApprovalFlowEvaluator.checkApprovalFlowConditions(
            order,
            approvalRuleConditions,
            ORDER_METRICS,
            DELTA_ARR_PERCENT,
            ORDER_LINE_METRICS,
            planEntityCache,
            productEntityCache,
            initialApprovalFlowContext
        );
        assertTrue(conditionEvalResult);
    }

    @Test
    public void testOrderResellerCheck() {
        String isResellerCondition = "{'==': [{'var': 'isReseller'}, true]}";

        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withIsReseller(true);
        validateOrderLevelCondition(isResellerCondition, true, approvalFlowContext);

        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withIsReseller(false);
        validateOrderLevelCondition(isResellerCondition, false, approvalFlowContext);
    }

    @Test
    public void testOrderTotalDiscountGreaterThan50ConditionCheckReturnsTrue() {
        // orderDiscount is 39.6%
        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTotalDiscountGreaterThan50ConditionWithPriceOverrideCheckReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(1000000.00));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(4000000.00)); // with price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(2000000.00)); // without price override
        order.getLineItems().get(0).setListPriceOverrideRatio(new BigDecimal("2.0"));

        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(1000000.00));
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(4000000.00)); // with price override
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(2000000.00)); // without price override
        order.getLineItems().get(1).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTotalDiscountGreaterThan50ConditionWithPriceOverrideUnderCheckReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(1000000.00));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(1500000.00)); // with price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(3000000.00)); // without price override
        order.getLineItems().get(0).setListPriceOverrideRatio(new BigDecimal("0.5"));

        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderDiscountWhenSellPriceIsOverListPriceBeforeOverrideReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(4000000.00));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(4000000.00)); // with price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(2000000.00)); // before price override
        order.getLineItems().get(0).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 20]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderDiscountWhenSellPriceIsOverListPriceBeforeOverrideButHasDiscountReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(3000000.00));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(4000000.00)); // with price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(2000000.00)); // before price override
        order.getLineItems().get(0).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 20]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderDiscountWhenNegativeSellPriceIsOverListPriceBeforeOverrideButHasDiscountReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(-3000000.00));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(-4000000.00)); // with price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(-2000000.00)); // before price override
        order.getLineItems().get(0).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String orderDiscountGreaterThanOrEqual20 = "{'>=': [{'var': 'orderDiscount'}, 20]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual20, false, initialApprovalFlowContext);
    }

    // todo: remove test after migration
    @Test
    public void testOrderTotalDiscountGreaterThan50ConditionWithPriceOverrideNullCheckReturnsTrue() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(3000000.00)); // no price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.ZERO); // before migration
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(3000000.00)); // no price override
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.ZERO); // before migration

        // orderDiscount is 0%
        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTotalDiscountGreaterThan50ConditionCheckWithNegativeAmountsReturnsTrue() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(-500000));

        // orderDiscount is 66%
        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTotalDiscountGreaterThan50ConditionCheckReturnsFalse() {
        order.setTotalListAmount(BigDecimal.valueOf(1500000));
        order.setTotalListAmountBeforeOverride(BigDecimal.valueOf(1500000));

        // orderDiscount is 33%
        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTotalDiscountGreaterThan50ConditionCheckWithNegativeAmountsReturnsFalse() {
        order.setTotalListAmount(BigDecimal.valueOf(-1500000));
        order.setTotalListAmountBeforeOverride(BigDecimal.valueOf(-1500000));
        order.setTotalAmount(BigDecimal.valueOf(-1000000));

        // orderDiscount is 33%
        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTotalDiscountGreaterThan50ConditionCheckWithZeroTotalListAmountReturnsFalse() {
        order.setTotalListAmount(BigDecimal.ZERO);
        order.setTotalListAmountBeforeOverride(BigDecimal.ZERO);
        order.setTotalAmount(BigDecimal.ZERO);

        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTotalDiscountGreaterThan50ConditionCheckWithZeroDiscountAmountReturnsFalse() {
        order.setTotalListAmount(BigDecimal.valueOf(1500000));
        order.setTotalListAmountBeforeOverride(BigDecimal.valueOf(1500000));
        order.setTotalAmount(BigDecimal.valueOf(1500000));

        // orderDiscount is 0%
        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTotalDiscountGreaterThan50ConditionCheckWithZeroDiscountAmountWithNegativeValuesReturnsFalse() {
        order.setTotalListAmount(BigDecimal.valueOf(-1500000));
        order.setTotalListAmountBeforeOverride(BigDecimal.valueOf(-1500000));
        order.setTotalAmount(BigDecimal.valueOf(-1500000));

        // orderDiscount is 0%
        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTotalDiscountWithGreatThan100PercentDiscount() {
        order.setTotalListAmount(BigDecimal.valueOf(1000000));
        order.setTotalListAmountBeforeOverride(BigDecimal.valueOf(100000));
        order.setTotalAmount(BigDecimal.valueOf(2000000));

        // orderDiscount is 0%
        String orderDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'orderDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetDiscountGreaterThan50_withDiscountLines() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(100000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(100000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(100000));

        var discountCharge = chargeMap.get(TEST_DISCOUNT_CHARGE);
        discountCharge.setType(ChargeType.RECURRING);
        discountCharge.setRecurrence(new Recurrence(Cycle.YEAR, 1));

        order.getLineItems().get(1).setChargeId(TEST_DISCOUNT_CHARGE);
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(-60000));
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(-60000));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(-60000));
        order.getLineItems().get(1).setQuantity(1);

        // orderDiscount is 66%
        String orderDiscountGreatThanEqualTo50 = "{'>=': [{'var': 'recurringDiscount'}, 50]}";
        validateOrderLevelCondition(orderDiscountGreatThanEqualTo50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountGreaterThan50ConditionCheckReturnsTrue() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(1500000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(1500000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(500000));

        // recurringDiscount is 66%
        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 50]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountGreaterThan50ConditionWithPriceOverrideCheckReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(1000000.00));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(4000000.00)); // with price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(2000000.00)); // without price override
        order.getLineItems().get(0).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 50]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountGreaterThan50ConditionWithPriceOverrideUnderCheckReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(1000000.00));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(1500000.00)); // with price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(3000000.00)); // without price override
        order.getLineItems().get(0).setListPriceOverrideRatio(new BigDecimal("0.5"));

        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 50]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountWhenSellPriceIsOverListPriceBeforeOverrideReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(4000000.00));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(4000000.00)); // with price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(2000000.00)); // before price override
        order.getLineItems().get(0).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 20]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountWhenSellPriceIsOverListPriceBeforeOverrideButHasDiscountReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(3000000.00));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(4000000.00)); // with price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(2000000.00)); // before price override
        order.getLineItems().get(0).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 20]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountWhenNegativeSellPriceIsOverListPriceBeforeOverrideButHasDiscountReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(-3000000.00));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(-4000000.00)); // with price override
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(-2000000.00)); // before price override
        order.getLineItems().get(0).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 20]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountGreaterThan50ConditionCheckWithNegativeAmountsReturnsTrue() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(-500000));

        // recurringDiscount is 66%
        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 50]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountGreaterThan50ConditionCheckReturnsFalse() {
        // recurringDiscount is 40%
        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 50]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountGreaterThan50ConditionCheckWithNegativeAmountsReturnsFalse() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(-1000000));

        // recurringDiscount is 33%
        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 50]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountGreaterThan50ConditionCheckWithZeroListAmountReturnsFalse() {
        order.getLineItems().get(0).setListAmount(BigDecimal.ZERO);
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.ZERO);
        order.getLineItems().get(0).setAmount(BigDecimal.ZERO);

        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 50]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountGreaterThan50ConditionCheckWithZeroDiscountAmountReturnsFalse() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(1500000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(1500000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(1500000));

        // recurringDiscount is 0%
        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 50]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetRecurringDiscountGreaterThan50ConditionCheckWithZeroDiscountAmountWithNegativeValuesReturnsFalse() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(-1500000));

        // recurringDiscount is 0%
        String recurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'recurringDiscount'}, 50]}";
        validateOrderLevelCondition(recurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountGreaterThan50ConditionCheckReturnsTrue() {
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(1500000));
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(1500000));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(500000));

        // nonRecurringDiscount is 66%
        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 50]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountGreaterThan50ConditionWithPriceOverrideCheckReturnsTrue() {
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(1000000.00));
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(4000000.00)); // with price override
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(2000000.00)); // without price override
        order.getLineItems().get(1).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 50]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountGreaterThan50ConditionWithPriceOverrideUnderCheckReturnsTrue() {
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(1000000.00));
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(1500000.00)); // with price override
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(3000000.00)); // without price override
        order.getLineItems().get(1).setListPriceOverrideRatio(new BigDecimal("0.5"));

        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 50]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountWhenSellPriceIsOverListPriceBeforeOverrideReturnsTrue() {
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(4000000.00));
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(4000000.00)); // with price override
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(2000000.00)); // before price override
        order.getLineItems().get(1).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 20]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountWhenSellPriceIsOverListPriceBeforeOverrideButHasDiscountReturnsTrue() {
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(3000000.00));
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(4000000.00)); // with price override
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(2000000.00)); // before price override
        order.getLineItems().get(1).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 20]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountWhenNegativeSellPriceIsOverListPriceBeforeOverrideButHasDiscountReturnsTrue() {
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(-3000000.00));
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(-4000000.00)); // with price override
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(-2000000.00)); // before price override
        order.getLineItems().get(1).setListPriceOverrideRatio(new BigDecimal("2.0"));

        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 20]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountGreaterThan50ConditionCheckWithNegativeAmountsReturnsTrue() {
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(-500000));

        // nonRecurringDiscount is 66%
        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 50]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountGreaterThan50ConditionCheckReturnsFalse() {
        // nonRecurringDiscount is 0%
        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 50]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountGreaterThan50ConditionCheckWithNegativeAmountsReturnsFalse() {
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(-1000000));

        // nonRecurringDiscount is 33%
        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 50]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountGreaterThan50ConditionCheckWithZeroListAmountReturnsFalse() {
        order.getLineItems().get(1).setListAmount(BigDecimal.ZERO);
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.ZERO);
        order.getLineItems().get(1).setAmount(BigDecimal.ZERO);

        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 50]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountGreaterThan50ConditionCheckWithZeroDiscountAmountReturnsFalse() {
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(1500000));
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(1500000));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(1500000));

        // nonRecurringDiscount is 0%
        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 50]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineNetNonRecurringDiscountGreaterThan50ConditionCheckWithZeroDiscountAmountWithNegativeValuesReturnsFalse() {
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(-1500000));

        // nonRecurringDiscount is 0%
        String nonRecurringDiscountGreaterThanOrEqual50 = "{'>=': [{'var': 'nonRecurringDiscount'}, 50]}";
        validateOrderLevelCondition(nonRecurringDiscountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testPaymentTermEqualsNet30ConditionCheckReturnsTrue() {
        String paymentTermEqualNet30 = "{'==': [{'var': 'paymentTerm'}, 'NET30']}";
        validateOrderLevelCondition(paymentTermEqualNet30, true, initialApprovalFlowContext);
    }

    @Test
    public void testPaymentTermNotEqualsNet30ConditionCheckReturnsFalse() {
        String paymentTermEqualNet30 = "{'!=': [{'var': 'paymentTerm'}, 'NET30']}";
        validateOrderLevelCondition(paymentTermEqualNet30, false, initialApprovalFlowContext);
    }

    @Test
    public void testBillingEqualsQuarterlyConditionCheckReturnsFalse() {
        String billingCycleEqualsQuarterly = "{'==': [{'var': 'billingCycle'}, 'QUARTER']}";
        validateOrderLevelCondition(billingCycleEqualsQuarterly, false, initialApprovalFlowContext);
    }

    @Test
    public void testBillingEqualsMonthlyConditionCheckReturnsTrue() {
        String billingCycleEqualsMonthly = "{'==': [{'var': 'billingCycle'}, 'MONTH']}";
        validateOrderLevelCondition(billingCycleEqualsMonthly, true, initialApprovalFlowContext);
    }

    @Test
    public void testTcvEqualsMillionConditionCheckReturnsTrue() {
        String tcvGreaterOrEqualsMillion = "{'>=': [{'var': 'tcv'}, 1000000.00]}";
        validateOrderLevelCondition(tcvGreaterOrEqualsMillion, true, initialApprovalFlowContext);
    }

    @Test
    public void testTcvEquals2MillionConditionCheckReturnsFalse() {
        String tcvGreaterOrEqualsMillion = "{'>=': [{'var': 'tcv'}, 2000000.00]}";
        validateOrderLevelCondition(tcvGreaterOrEqualsMillion, false, initialApprovalFlowContext);
    }

    @Test
    public void testChargeEqualsTestChargeConditionCheckReturnsTrue() {
        String testChargeIdCondition = "{'==': [{'var': 'chargeId'}, 'TEST_CHARGE_1_ID']}";
        validateOrderLineLevelCondition(testChargeIdCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testChargeNotEqualsTestChargeConditionCheckReturnsFalse() {
        String testChargeIdCondition = "{'==': [{'var': 'chargeId'}, 'RANDOM_CHARGE_ID']}";
        validateOrderLineLevelCondition(testChargeIdCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testQuantityGreaterThan200ConditionCheckReturnsTrue() {
        String testQuantityCondition = "{'>': [{'var': 'quantity'}, 200]}";
        validateOrderLineLevelCondition(testQuantityCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testQuantityLessThan100ConditionCheckReturnsFalse() {
        String testQuantityCondition = "{'<': [{'var': 'quantity'}, 100]}";
        validateOrderLineLevelCondition(testQuantityCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testDiscountGreaterThan30ConditionCheckReturnsTrue() {
        String discountGreaterThanOrEqual30 = "{'>=': [{'var': 'discount'}, 30]}";
        validateOrderLineLevelCondition(discountGreaterThanOrEqual30, true, initialApprovalFlowContext);
    }

    @Test
    public void testDiscountGreaterThan30WithPriceOverrideConditionCheckReturnsFalse() {
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(100000L));

        String discountGreaterThanOrEqual50 = "{'>=': [{'var': 'discount'}, 30]}";
        validateOrderLineLevelCondition(discountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testDiscountGreaterThan30ConditionCheckReturnsFalse() {
        String discountGreaterThanOrEqual50 = "{'>=': [{'var': 'discount'}, 50]}";
        validateOrderLineLevelCondition(discountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testDiscountGreaterThan50ConditionCheckReturnsFalse() {
        String discountGreaterThanOrEqual50 = "{'>=': [{'var': 'discount'}, 50]}";
        validateOrderLineLevelCondition(discountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testDiscountGreaterThanOrEquals40ConditionCheckReturnsTrue() {
        String discountGreaterThanOrEqual40 = "{'>=': [{'var': 'discount'}, 40]}";
        validateOrderLineLevelCondition(discountGreaterThanOrEqual40, true, initialApprovalFlowContext);
    }

    @Test
    public void testDiscountGreaterThan30WithPriceOverrideNoDiscountReturnsFalse() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(200000L));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(200000L));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(100000L));

        String discountGreaterThanOrEqual50 = "{'>=': [{'var': 'discount'}, 30]}";
        validateOrderLineLevelCondition(discountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testDiscountGreaterThan30WithPriceOverrideUnderSellAmountReturnsFalse() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(150000L));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(200000L));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(100000L));

        String discountGreaterThanOrEqual50 = "{'>=': [{'var': 'discount'}, 30]}";
        validateOrderLineLevelCondition(discountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testDiscountGreaterThan30WithPriceOverrideUnderSellAmountAndNegativesReturnsFalse() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(-150000L));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(-200000L));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(-100000L));

        String discountGreaterThanOrEqual50 = "{'>=': [{'var': 'discount'}, 30]}";
        validateOrderLineLevelCondition(discountGreaterThanOrEqual50, false, initialApprovalFlowContext);
    }

    @Test
    public void testDiscountGreaterThan30WithPriceOverrideReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(50000L));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(200000L));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(100000L));

        String discountGreaterThanOrEqual50 = "{'>=': [{'var': 'discount'}, 30]}";
        validateOrderLineLevelCondition(discountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testDiscountGreaterThan30WithPriceOverrideAndNegativesReturnsTrue() {
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(-50000L));
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(-200000L));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(-100000L));

        String discountGreaterThanOrEqual50 = "{'>=': [{'var': 'discount'}, 30]}";
        validateOrderLineLevelCondition(discountGreaterThanOrEqual50, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTypeNewConditionCheckReturnsTrue() {
        String orderTypeEqualsNew = "{'==': [{'var': 'orderType'}, 'NEW']}";
        validateOrderLevelCondition(orderTypeEqualsNew, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderTypeCancelConditionCheckReturnsFalseForNewOrders() {
        String orderTypeEqualsCancel = "{'==': [{'var': 'orderType'}, 'CANCEL']}";
        validateOrderLevelCondition(orderTypeEqualsCancel, false, initialApprovalFlowContext);
    }

    @Test
    public void testAverageArrGreaterThanOrEqualToMillionConditionCheckReturnsTrue() {
        String averageArrGreaterThanOrEqualMillion = "{'>=': [{'var': 'averageArr'}, 1000000]}";
        validateOrderLevelCondition(averageArrGreaterThanOrEqualMillion, true, initialApprovalFlowContext);
    }

    @Test
    public void testAverageArrGreaterThanMillionConditionCheckReturnsFalse() {
        String averageArrGreaterThanMillion = "{'>': [{'var': 'averageArr'}, 1000000]}";
        validateOrderLevelCondition(averageArrGreaterThanMillion, false, initialApprovalFlowContext);
    }

    @Test
    public void testExitArrGreaterThanOrEqualToMillionConditionCheckReturnsTrue() {
        String exitArrGreaterThanOrEqualMillion = "{'>=': [{'var': 'exitArr'}, 1000000]}";
        validateOrderLevelCondition(exitArrGreaterThanOrEqualMillion, true, initialApprovalFlowContext);
    }

    @Test
    public void testExitArrGreaterThanMillionConditionCheckReturnsFalse() {
        String exitArrGreaterThanMillion = "{'>': [{'var': 'exitArr'}, 1000000]}";
        validateOrderLevelCondition(exitArrGreaterThanMillion, false, initialApprovalFlowContext);
    }

    @Test
    public void testEntryArrGreaterThanOrEqualToMillionConditionCheckReturnsTrue() {
        String entryArrGreaterThanOrEqualMillion = "{'>=': [{'var': 'entryArr'}, 1000000]}";
        validateOrderLevelCondition(entryArrGreaterThanOrEqualMillion, true, initialApprovalFlowContext);
    }

    @Test
    public void testEntryArrGreaterThanMillionConditionCheckReturnsFalse() {
        String entryArrGreaterThanMillion = "{'>': [{'var': 'entryArr'}, 1000000]}";
        validateOrderLevelCondition(entryArrGreaterThanMillion, false, initialApprovalFlowContext);
    }

    @Test
    public void testTermLengthGreaterThanOrEqual12ConditionCheckReturnsTrue() {
        String termLengthGreaterThanOrEqual12 = "{'>=': [{'var': 'termLength'}, 12]}";
        validateOrderLevelCondition(termLengthGreaterThanOrEqual12, true, initialApprovalFlowContext);
    }

    @Test
    public void testTermLengthGreaterThan12ConditionCheckReturnsFalse() {
        String termLengthGreaterThan12 = "{'>': [{'var': 'termLength'}, 12]}";
        validateOrderLevelCondition(termLengthGreaterThan12, false, initialApprovalFlowContext);
    }

    @Test
    public void testTermLengthGreaterThanOrEqual24MonthsWithOrderTermLengthNullConditionCheckReturnsTrue() {
        order.setTermLength(null);
        ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneOffset.UTC);
        order.setStartDate(zonedDateTime.toInstant());
        order.setEndDate(zonedDateTime.plusYears(2).toInstant());

        String termLengthGreaterThanOrEqual24 = "{'>=': [{'var': 'termLength'}, 24]}";
        validateOrderLevelCondition(termLengthGreaterThanOrEqual24, true, initialApprovalFlowContext);
    }

    @Test
    public void testTermLengthGreaterThanOrEqual36MonthsWithOrderTermLengthNullConditionCheckReturnsFalse() {
        order.setTermLength(null);
        ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneOffset.UTC);
        order.setStartDate(zonedDateTime.toInstant());
        order.setEndDate(zonedDateTime.plusYears(2).toInstant());

        String termLengthGreaterThanOrEqual36 = "{'>=': [{'var': 'termLength'}, 36]}";
        validateOrderLevelCondition(termLengthGreaterThanOrEqual36, false, initialApprovalFlowContext);
    }

    @Test
    public void testCustomTermNotNullConditionCheckReturnsTrue() {
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomTerms(DOCUMENT_CUSTOM_CONTENT.getContent());
        String customTermNotNull = "{'==': [{'var': 'customTerms'}, 'NotNull']}";
        validateOrderLevelCondition(customTermNotNull, true, approvalFlowContext);
    }

    @Test
    public void testCustomTermNullConditionCheckReturnsFalse() {
        String customTermNull = "{'==': [{'var': 'customTerms'}, '']}";
        validateOrderLevelCondition(customTermNull, false, initialApprovalFlowContext);
    }

    @Test
    public void testDeltaArrEqualToTwoHundredThousandConditionCheckReturnsTrue() {
        String deltaArrEqualsTwoHundredThousand = "{'==': [{'var': 'deltaArr'}, 200000]}";
        validateOrderLevelCondition(deltaArrEqualsTwoHundredThousand, true, initialApprovalFlowContext);
    }

    @Test
    public void testDeltaArrGreaterThanZeroConditionCheckReturnsFalse() {
        String deltaArrGreaterThanTwoHundredThousand = "{'>': [{'var': 'deltaArr'}, 200000]}";
        validateOrderLevelCondition(deltaArrGreaterThanTwoHundredThousand, false, initialApprovalFlowContext);
    }

    @Test
    public void testDeltaArrPercentEqualsConditionCheckReturnsTrue() {
        String deltaArrPercentEqualTo25Percent = "{'==': [{'var': 'deltaArrPercent'}, 25]}";
        validateOrderLevelCondition(deltaArrPercentEqualTo25Percent, true, initialApprovalFlowContext);
    }

    @Test
    public void testDeltaArrPercentGreaterConditionCheckReturnsFalse() {
        String deltaArrPercentGreaterThan25Percent = "{'>': [{'var': 'deltaArrPercent'}, 25]}";
        validateOrderLevelCondition(deltaArrPercentGreaterThan25Percent, false, initialApprovalFlowContext);
    }

    @Test
    public void testAutoRenewTrueConditionCheckReturnsTrue() {
        String autoRenewEqualToTrue = "{'==': [{'var': 'autoRenew'}, 'true']}";
        validateOrderLevelCondition(autoRenewEqualToTrue, true, initialApprovalFlowContext);
    }

    @Test
    public void testAutoRenewTrueConditionCheckReturnsFalse() {
        String autoRenewEqualToFalse = "{'!=': [{'var': 'autoRenew'}, 'false']}";
        validateOrderLevelCondition(autoRenewEqualToFalse, false, initialApprovalFlowContext);
    }

    @Test
    public void testOpportunityIdEmptyConditionCheckReturnsFalse() {
        String opportunityIdEmpty = "{'and':[{'!':{'var':'opportunityId'}}]}";
        validateOrderLevelCondition(opportunityIdEmpty, false, initialApprovalFlowContext);
    }

    @Test
    public void testOpportunityIdNotEmptyConditionCheckReturnsTrue() {
        String opportunityIdNotEmpty = "{'and':[{'!!':{'var':'opportunityId'}}]}";
        validateOrderLevelCondition(opportunityIdNotEmpty, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineTotalGreaterThan200ConditionCheckReturnsTrue() {
        String testOrderLineTotalCondition = "{'>': [{'var': 'orderLineTotal'}, 200]}";
        validateOrderLineLevelCondition(testOrderLineTotalCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineTotalLessThan100ConditionCheckReturnsFalse() {
        String testOrderLineTotalCondition = "{'<': [{'var': 'orderLineTotal'}, 100]}";
        validateOrderLineLevelCondition(testOrderLineTotalCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineEntryArrLessThan200ConditionCheckReturnsTrue() {
        String testOrderLineTotalCondition = "{'<': [{'var': 'orderLineEntryArr'}, 200]}";
        validateOrderLineLevelCondition(testOrderLineTotalCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineEntryArrMoreThan100ConditionCheckReturnsFalse() {
        String testOrderLineTotalCondition = "{'>': [{'var': 'orderLineEntryArr'}, 100]}";
        validateOrderLineLevelCondition(testOrderLineTotalCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testPlanEqualsTestPlanConditionCheckReturnsTrue() {
        String testPlanIdCondition = "{'==': [{'var': 'planId'}, 'TEST_PLAN_ID']}";
        validateOrderLineLevelCondition(testPlanIdCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testPlanNotEqualsTestPlanConditionCheckReturnsFalse() {
        String testPlanIdCondition = "{'==': [{'var': 'planId'}, 'SOME_PLAN_ID']}";
        validateOrderLineLevelCondition(testPlanIdCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testProductEqualsTestProductConditionCheckReturnsTrue() {
        String testProductIdCondition = "{'==': [{'var': 'productId'}, 'TEST_PRODUCT_ID']}";
        validateOrderLineLevelCondition(testProductIdCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testProductNotEqualsTestProductConditionCheckReturnsFalse() {
        String testProductIdCondition = "{'==': [{'var': 'productId'}, 'SOME_PRODUCT_ID']}";
        validateOrderLineLevelCondition(testProductIdCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testProductCategoryEqualsTestProductCategoryConditionCheckReturnsTrue() {
        String testProductCategoryIdCondition = "{'==': [{'var': 'productCategoryId'}, 'TEST_PRODUCT_CATEGORY_ID']}";
        validateOrderLineLevelCondition(testProductCategoryIdCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testProductCategoryNotEqualsTestProductCategoryConditionCheckReturnsFalse() {
        String testProductCategoryIdCondition = "{'==': [{'var': 'productCategoryId'}, 'SOME_PRODUCT_CATEGORY_ID']}";
        validateOrderLineLevelCondition(testProductCategoryIdCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testLineItemWithPriceOverrideCheckReturnsTrue() {
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(100000.00));

        String testLineItemHasOverrideCondition = "{'==': [{'var': 'priceOverride'}, 'true']}";
        validateOrderLineLevelCondition(testLineItemHasOverrideCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testLineItemWithPriceOverrideCheckReturnsFalse() {
        String testLineItemHasOverrideCondition = "{'==': [{'var': 'priceOverride'}, 'true']}";
        validateOrderLineLevelCondition(testLineItemHasOverrideCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testLineItemWithArrOverrideCheckReturnsFalse() {
        String testLineItemHasOverrideCondition = "{'==': [{'var': 'arrOverride'}, 'true']}";
        validateOrderLineLevelCondition(testLineItemHasOverrideCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testLineItemWithArrOverrideCheckReturnsTrue() {
        order.getLineItems().get(0).setArrOverride(BigDecimal.valueOf(100000.00));

        String testLineItemHasOverrideCondition = "{'==': [{'var': 'arrOverride'}, 'true']}";
        validateOrderLineLevelCondition(testLineItemHasOverrideCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testPredefinedDiscountEqualsTestPredefinedDiscountConditionCheckReturnsTrue() {
        String testPredefinedDiscountCondition = "{'==': [{'var': 'predefinedDiscounts'}, 'TEST_PREDEFINED_DISCOUNT_ID']}";
        validateOrderLevelCondition(testPredefinedDiscountCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testPredefinedDiscountEqualsTestPredefinedDiscountConditionCheckReturnsFalse() {
        String testPredefinedDiscountCondition = "{'==': [{'var': 'predefinedDiscounts'}, 'SOME_PREDEFINED_DISCOUNT_ID']}";
        validateOrderLevelCondition(testPredefinedDiscountCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testPredefinedDiscountNotEqualsTestPredefinedDiscountConditionCheckReturnsTrue() {
        String testPredefinedDiscountCondition = "{'!=': [{'var': 'predefinedDiscounts'}, 'SOME_PREDEFINED_DISCOUNT_ID']}";
        validateOrderLevelCondition(testPredefinedDiscountCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testPredefinedDiscountNotOneOfTestPredefinedDiscountConditionCheckReturnsTrue() {
        String testPredefinedDiscountCondition =
            "{'!':{'in': [{'var': 'predefinedDiscounts'}, ['SOME_PREDEFINED_DISCOUNT_ID', 'SOME_OTHER_PREDEFINED_DISCOUNT_ID']]}}";
        validateOrderLevelCondition(testPredefinedDiscountCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testPredefinedDiscountIsOneOfTestPredefinedDiscountConditionCheckReturnsTrue() {
        String testPredefinedDiscountCondition =
            "{'in': [{'var': 'predefinedDiscounts'}, ['TEST_PREDEFINED_DISCOUNT_ID', 'SOME_PREDEFINED_DISCOUNT_ID']]}";
        validateOrderLevelCondition(testPredefinedDiscountCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testPredefinedTermsModifiedIsTrueConditionWhenFalseCheckReturnsFalse() {
        predefinedTermsOnOrder = List.of(getPredefinedTerm());
        String predefinedTermsModifiedCondition = "{'==': [{'var': 'predefinedTermsModified'}, true]}";
        validateOrderLevelCondition(predefinedTermsModifiedCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testPredefinedTermsModifiedIsTrueConditionWhenFalseWithEmptySetCheckReturnsFalse() {
        predefinedTermsOnOrder = List.of(getPredefinedTerm());
        modifiedPredefinedTermsOnOrder = Set.of();
        String predefinedTermsModifiedCondition = "{'==': [{'var': 'predefinedTermsModified'}, true]}";
        validateOrderLevelCondition(predefinedTermsModifiedCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testPredefinedTermsModifiedIsTrueWhenTrueConditionCheckReturnsTrue() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTermsOnOrder = List.of(getPredefinedTerm(), predefinedTerm);
        modifiedPredefinedTermsOnOrder = Set.of(predefinedTerm.getTemplateId());
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        String predefinedTermsModifiedCondition = "{'==': [{'var': 'predefinedTermsModified'}, true]}";
        validateOrderLevelCondition(predefinedTermsModifiedCondition, true, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsModifiedIsFalseWhenTrueConditionCheckReturnsFalse() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTermsOnOrder = List.of(predefinedTerm);
        modifiedPredefinedTermsOnOrder = Set.of(predefinedTerm.getTemplateId());
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);

        String predefinedTermsModifiedCondition = "{'==': [{'var': 'predefinedTermsModified'}, false]}";
        validateOrderLevelCondition(predefinedTermsModifiedCondition, false, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsModifiedIsFalseWhenModifiedListIsEmptyConditionCheckReturnsFalse() {
        predefinedTermsOnOrder = List.of(getPredefinedTerm());
        modifiedPredefinedTermsOnOrder = Set.of();
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        String predefinedTermsModifiedCondition = "{'==': [{'var': 'predefinedTermsModified'}, false]}";
        validateOrderLevelCondition(predefinedTermsModifiedCondition, true, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsModifiedIsFalseConditionWhenFalseCheckReturnsTrue() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTermsOnOrder = List.of(getPredefinedTerm(), predefinedTerm);
        modifiedPredefinedTermsOnOrder = Set.of(predefinedTerm.getTemplateId());
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        String predefinedTermsModifiedCondition = "{'==': [{'var': 'predefinedTermsModified'}, false]}";
        validateOrderLevelCondition(predefinedTermsModifiedCondition, true, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsOutdatedIsTrueConditionWhenFalseCheckReturnsFalse() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTerm.setHasNewerVersion(false);
        predefinedTermsOnOrder = List.of(predefinedTerm);
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withPredefinedTermsOnOrder(predefinedTermsOnOrder);
        String predefinedTermsOutdatedCondition = "{'==': [{'var': 'predefinedTermsOutdated'}, true]}";
        validateOrderLevelCondition(predefinedTermsOutdatedCondition, false, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsOutdatedIsTrueWhenTrueConditionCheckReturnsTrue() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTerm.setHasNewerVersion(true);
        predefinedTermsOnOrder = List.of(predefinedTerm);
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withPredefinedTermsOnOrder(predefinedTermsOnOrder);
        String predefinedTermsOutdatedCondition = "{'==': [{'var': 'predefinedTermsOutdated'}, true]}";
        validateOrderLevelCondition(predefinedTermsOutdatedCondition, true, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsOutdatedIsFalseWhenTrueConditionCheckReturnsFalse() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTerm.setHasNewerVersion(true);
        predefinedTermsOnOrder = List.of(predefinedTerm);
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withPredefinedTermsOnOrder(predefinedTermsOnOrder);
        String predefinedTermsOutdatedCondition = "{'==': [{'var': 'predefinedTermsOutdated'}, false]}";
        validateOrderLevelCondition(predefinedTermsOutdatedCondition, false, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsOutdatedIsFalseConditionWhenFalseCheckReturnsTrue() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTerm.setHasNewerVersion(false);
        predefinedTermsOnOrder = List.of(predefinedTerm);
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withPredefinedTermsOnOrder(predefinedTermsOnOrder);
        String predefinedTermsOutdatedCondition = "{'==': [{'var': 'predefinedTermsOutdated'}, false]}";
        validateOrderLevelCondition(predefinedTermsOutdatedCondition, true, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsNameConditionCheckReturnsTrue() {
        DocumentTemplate term1 = getPredefinedTerm();
        DocumentTemplate term2 = getPredefinedTerm();
        term2.setName(UUID.randomUUID().toString());
        predefinedTermsOnOrder = List.of(term1, term2);
        modifiedPredefinedTermsOnOrder = Set.of();
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        String predefinedTermsModifiedCondition = "{'==': [{'var': 'predefinedTermName'}, 'Test PredefinedTerm']}";
        validateOrderLevelCondition(predefinedTermsModifiedCondition, true, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsNameConditionCheckReturnsFalse() {
        predefinedTermsOnOrder = List.of();
        modifiedPredefinedTermsOnOrder = Set.of();
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        String predefinedTermsModifiedCondition = "{'==': [{'var': 'predefinedTermName'}, 'Test PredefinedTerm']}";
        validateOrderLevelCondition(predefinedTermsModifiedCondition, false, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsNameConditionCheckForMultipleTermsAndNotFoundReturnsFalse() {
        DocumentTemplate term1 = getPredefinedTerm();
        DocumentTemplate term2 = getPredefinedTerm();
        term2.setName(UUID.randomUUID().toString());
        predefinedTermsOnOrder = List.of(term1, term2);
        modifiedPredefinedTermsOnOrder = Set.of(term1.getTemplateId());
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        String predefinedTermsModifiedCondition = "{'==': [{'var': 'predefinedTermName'}, 'random term']}";
        validateOrderLevelCondition(predefinedTermsModifiedCondition, false, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsNameConditionCheckWhenNotModifiedReturnsFalse() {
        DocumentTemplate term1 = getPredefinedTerm();
        DocumentTemplate term2 = getPredefinedTerm();
        term2.setName(UUID.randomUUID().toString());
        predefinedTermsOnOrder = List.of(term1, term2);
        modifiedPredefinedTermsOnOrder = Set.of(term2.getTemplateId());
        String predefinedTermsModifiedCondition =
            "{'and':[{'==':[{'var':'predefinedTermsModified'},true]},{'==':[{'var':'predefinedTermName'},'Test PredefinedTerm']}]}}";
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        validateOrderLevelCondition(predefinedTermsModifiedCondition, false, approvalFlowContext);
    }

    @Test
    public void testPredefinedTermsNameConditionCheckWhenModifiedReturnsTrue() {
        DocumentTemplate term1 = getPredefinedTerm();
        DocumentTemplate term2 = getPredefinedTerm();
        term2.setName(UUID.randomUUID().toString());
        predefinedTermsOnOrder = List.of(term1, term2);
        modifiedPredefinedTermsOnOrder = Set.of(term1.getTemplateId());
        String predefinedTermsModifiedCondition =
            "{'and':[{'==':[{'var':'predefinedTermsModified'},true]},{'==':[{'var':'predefinedTermName'},'Test PredefinedTerm']}]}}";
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        validateOrderLevelCondition(predefinedTermsModifiedCondition, true, approvalFlowContext);
    }

    @Test
    public void testAccountCustomFieldPicklistPresentReturnsTrue() {
        String accountCustomFieldPicklistCondition = "{'and':[{'in':[{'var':'CF_ACCOUNT;account_picklist_custom_field'},['account-picklist-1']]}]}";
        customFieldsOnAccount = new CustomField(
            getCustomFieldMap(ACCOUNT_ID, ACCOUNT_PICKLIST_CUSTOM_FIELD_NAME, ACCOUNT_PICKLIST_CUSTOM_FIELD_VALUE)
        );

        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldOnAccount(customFieldsOnAccount);
        validateOrderLevelCondition(accountCustomFieldPicklistCondition, true, approvalFlowContext);
    }

    @Test
    public void testAccountCustomFieldStringPresentReturnsTrue() {
        String accountCustomFieldStringCondition = "{'and':[{'in':[{'var':'CF_ACCOUNT;account_string_custom_field'},['account-string-1']]}]}";
        customFieldsOnAccount = new CustomField(getCustomFieldMap(ACCOUNT_ID, ACCOUNT_STRING_CUSTOM_FIELD_NAME, ACCOUNT_STRING_CUSTOM_FIELD_VALUE));
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldOnAccount(customFieldsOnAccount);
        validateOrderLevelCondition(accountCustomFieldStringCondition, true, approvalFlowContext);
    }

    @Test
    public void testOpportunityCustomFieldPicklistPresentReturnsTrue() {
        String opportunityCustomFieldPicklistCondition =
            "{'and':[{'in':[{'var':'CF_OPPORTUNITY;opportunity_picklist_custom_field'},['opportunity-picklist-1']]}]}";

        customFieldsOnOpportunity = new CustomField(
            getCustomFieldMap(TEST_OPPORTUNITY, OPPORTUNITY_PICKLIST_CUSTOM_FIELD_NAME, OPPORTUNITY_PICKLIST_CUSTOM_FIELD_VALUE)
        );
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldsOnOpportunity(
            customFieldsOnOpportunity
        );

        validateOrderLevelCondition(opportunityCustomFieldPicklistCondition, true, approvalFlowContext);
    }

    @Test
    public void testOpportunityCustomFieldStringPresentReturnsTrue() {
        String opportunityCustomFieldStringCondition =
            "{'and':[{'in':[{'var':'CF_OPPORTUNITY;opportunity_string_custom_field'},['opportunity-string-1']]}]}";

        customFieldsOnOpportunity = new CustomField(
            getCustomFieldMap(TEST_OPPORTUNITY, OPPORTUNITY_STRING_CUSTOM_FIELD_NAME, OPPORTUNITY_STRING_CUSTOM_FIELD_VALUE)
        );
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldsOnOpportunity(
            customFieldsOnOpportunity
        );

        validateOrderLevelCondition(opportunityCustomFieldStringCondition, true, approvalFlowContext);
    }

    @Test
    public void testOrderCustomFieldPicklistPresentReturnsTrue() {
        String orderCustomFieldPicklistCondition = "{'and':[{'in':[{'var':'CF_ORDER;order_picklist_custom_field'},['order-picklist-1']]}]}";
        customFieldsOnOrder = new CustomField(getCustomFieldMap(ORDER_ID, ORDER_PICKLIST_CUSTOM_FIELD_NAME, ORDER_PICKLIST_CUSTOM_FIELD_VALUE));
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldsOnOrder(customFieldsOnOrder);

        validateOrderLevelCondition(orderCustomFieldPicklistCondition, true, approvalFlowContext);
    }

    @Test
    public void testOrderCustomFieldStringPresentReturnsTrue() {
        String orderCustomFieldStringCondition = "{'and':[{'in':[{'var':'CF_ORDER;order_string_custom_field'},['order-string-1']]}]}";
        customFieldsOnOrder = new CustomField(getCustomFieldMap(ORDER_ID, ORDER_STRING_CUSTOM_FIELD_NAME, ORDER_STRING_CUSTOM_FIELD_VALUE));
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldsOnOrder(customFieldsOnOrder);

        validateOrderLevelCondition(orderCustomFieldStringCondition, true, approvalFlowContext);
    }

    @Test
    public void testOrderLineCustomFieldPicklistPresentReturnsTrue() {
        String orderLineCustomFieldPicklistCondition =
            "{'and':[{'in':[{'var':'CF_ORDER_ITEM;orderLine_picklist_custom_field'},['orderline-picklist-1']]}]}";
        customFieldsOnOrderLineMap = new HashMap<>();
        customFieldsOnOrderLineMap.put(
            ORDER_LINE_1_ID,
            new CustomField(getCustomFieldMap(ORDER_LINE_1_ID, ORDER_LINE_PICKLIST_CUSTOM_FIELD_NAME, ORDER_LINE_PICKLIST_CUSTOM_FIELD_VALUE))
        );
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldOnOrderLinesMap(
            customFieldsOnOrderLineMap
        );

        validateOrderLineLevelCondition(orderLineCustomFieldPicklistCondition, true, approvalFlowContext);
    }

    @Test
    public void testOrderLineCustomFieldStringPresentReturnsTrue() {
        String orderLineCustomFieldStringCondition =
            "{'and':[{'in':[{'var':'CF_ORDER_ITEM;orderLine_string_custom_field'},['orderline-string-1']]}]}";
        customFieldsOnOrderLineMap = new HashMap<>();
        customFieldsOnOrderLineMap.put(
            ORDER_LINE_1_ID,
            new CustomField(getCustomFieldMap(ORDER_LINE_1_ID, ORDER_LINE_STRING_CUSTOM_FIELD_NAME, ORDER_LINE_STRING_CUSTOM_FIELD_VALUE))
        );
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldOnOrderLinesMap(
            customFieldsOnOrderLineMap
        );
        validateOrderLineLevelCondition(orderLineCustomFieldStringCondition, true, approvalFlowContext);
    }

    @Test
    public void testPlanCustomFieldPicklistPresentReturnsTrue() {
        String planCustomFieldPicklistCondition = "{'and':[{'in':[{'var':'CF_PLAN;plan_picklist_custom_field'},['plan-picklist-1']]}]}";
        customFieldsOnPlansMap = new HashMap<>();
        customFieldsOnPlansMap.put(
            TEST_PLAN_ID,
            new CustomField(getCustomFieldMap(TEST_PLAN_ID, PLAN_PICKLIST_CUSTOM_FIELD_NAME, PLAN_PICKLIST_CUSTOM_FIELD_VALUE))
        );
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldsOnPlansMap(customFieldsOnPlansMap);
        validateOrderLineLevelCondition(planCustomFieldPicklistCondition, true, approvalFlowContext);
    }

    @Test
    public void testPlanCustomFieldStringPresentReturnsTrue() {
        String planCustomFieldStringCondition = "{'and':[{'in':[{'var':'CF_PLAN;plan_string_custom_field'},['plan-string-1']]}]}";
        customFieldsOnPlansMap = new HashMap<>();
        customFieldsOnPlansMap.put(
            TEST_PLAN_ID,
            new CustomField(getCustomFieldMap(TEST_PLAN_ID, PLAN_STRING_CUSTOM_FIELD_NAME, PLAN_STRING_CUSTOM_FIELD_VALUE))
        );
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldsOnPlansMap(customFieldsOnPlansMap);
        validateOrderLineLevelCondition(planCustomFieldStringCondition, true, approvalFlowContext);
    }

    @Test
    public void testChargeCustomFieldPicklistPresentReturnsTrue() {
        String chargeCustomFieldPicklistCondition = "{'and':[{'in':[{'var':'CF_CHARGE;charge_picklist_custom_field'},['charge-picklist-1']]}]}";
        customFieldsOnChargesMap = new HashMap<>();
        customFieldsOnChargesMap.put(
            TEST_CHARGE_1_ID,
            new CustomField(getCustomFieldMap(TEST_CHARGE_1_ID, CHARGE_PICKLIST_CUSTOM_FIELD_NAME, CHARGE_PICKLIST_CUSTOM_FIELD_VALUE))
        );
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldsOnChargesMap(customFieldsOnChargesMap);
        validateOrderLineLevelCondition(chargeCustomFieldPicklistCondition, true, approvalFlowContext);
    }

    @Test
    public void testChargeCustomFieldStringPresentReturnsTrue() {
        String chargeCustomFieldStringCondition = "{'and':[{'in':[{'var':'CF_CHARGE;charge_string_custom_field'},['charge-string-1']]}]}";
        customFieldsOnChargesMap = new HashMap<>();
        customFieldsOnChargesMap.put(
            TEST_CHARGE_1_ID,
            new CustomField(getCustomFieldMap(TEST_CHARGE_1_ID, CHARGE_STRING_CUSTOM_FIELD_NAME, CHARGE_STRING_CUSTOM_FIELD_VALUE))
        );
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext).withCustomFieldsOnChargesMap(customFieldsOnChargesMap);
        validateOrderLineLevelCondition(chargeCustomFieldStringCondition, true, approvalFlowContext);
    }

    @Test
    public void testOrderLineIsCreditableReturnsFalse() {
        order.getLineItems().get(0).setAction(ActionType.REMOVE);
        order.getLineItems().get(1).setAction(ActionType.ADD);

        String orderLineWithIsCreditableConditionFalse =
            "{'and':[{'==':[{'var':'orderLineActionType'},'REMOVE']},{'==':[{'var':'chargeType'},'ONE_TIME']},{'==':[{'var':'chargeIsCreditable'},true]}]}";
        validateOrderLineLevelCondition(orderLineWithIsCreditableConditionFalse, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineIsCreditableReturnsTrue() {
        order.getLineItems().get(1).setAction(ActionType.REMOVE);

        String orderLineWithIsCreditableConditionTrue =
            "{'and':[{'==':[{'var':'orderLineActionType'},'REMOVE']},{'==':[{'var':'chargeType'},'ONE_TIME']},{'==':[{'var':'chargeIsCreditable'},true]}]}";
        validateOrderLineLevelCondition(orderLineWithIsCreditableConditionTrue, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineListUnitPriceForCustomCharge() {
        String listUnitPriceCondition = "{'>=': [{'var': 'listUnitPrice'}, 50]}";

        order.getLineItems().get(1).setListUnitPrice(new BigDecimal("40"));
        validateOrderLineLevelCondition(listUnitPriceCondition, false, initialApprovalFlowContext);

        order.getLineItems().get(1).setListUnitPrice(new BigDecimal("60"));
        validateOrderLineLevelCondition(listUnitPriceCondition, true, initialApprovalFlowContext);

        order.getLineItems().get(1).setListUnitPrice(null);
        validateOrderLineLevelCondition(listUnitPriceCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderLineItemCustomPricing() {
        String customChargeCondition = "{'==': [{'var': 'isCustomPricing'}, true]}";

        validateOrderLineLevelCondition(customChargeCondition, false, initialApprovalFlowContext);

        var customCharge = chargeMap.get(TEST_CHARGE_1_ID);
        customCharge.setCustom(true);
        validateOrderLineLevelCondition(customChargeCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderConditionPlanIdFoundInOrderLines() {
        String containsPlanIdOrderCondition = "{'and':[{'in':['TEST_PLAN_ID',{'var':'planIds'}]}]}";

        validateOrderLevelCondition(containsPlanIdOrderCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderConditionPlanIdNotFoundInOrderLines() {
        String containsPlanIdOrderCondition = "{'and':[{'in':['TEST_PLAN_ID_NEW',{'var':'planIds'}]}]}";

        validateOrderLevelCondition(containsPlanIdOrderCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderConditionChargeIdFoundInOrderLines() {
        String containsChargeIdOrderCondition = "{'and':[{'in':['TEST_CHARGE_1_ID',{'var':'chargeIds'}]}]}";

        validateOrderLevelCondition(containsChargeIdOrderCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderConditionChargeIdsNotFoundInOrderLines() {
        String containsChargeIdOrderCondition = "{'and':[{'in':['TEST_CHARGE_ID_NEW',{'var':'chargeIds'}]}]}";

        validateOrderLevelCondition(containsChargeIdOrderCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testOrderConditionProductIdFoundInOrderLines() {
        String containsProductIdOrderCondition = "{'and':[{'in':['TEST_PRODUCT_ID',{'var':'productIds'}]}]}";

        validateOrderLevelCondition(containsProductIdOrderCondition, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderConditionProductIdsNotFoundInOrderLines() {
        String containsProductIdOrderCondition = "{'and':[{'in':['TEST_PRODUCT_ID_NEW',{'var':'productIds'}]}]}";

        validateOrderLevelCondition(containsProductIdOrderCondition, false, initialApprovalFlowContext);
    }

    @Test
    public void testMaxOrderLineDiscountPercentConditionFalse() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(1000000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(1000000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(750000)); // 25% discount
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(1500000));
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(1500000));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(1000000)); // 33% discount

        String maxOrderLineDiscountPercent = "{'<=': [{'var': 'maxOrderLineDiscountPercent'}, 32]}";
        validateOrderLevelCondition(maxOrderLineDiscountPercent, false, initialApprovalFlowContext);
    }

    @Test
    public void testMaxOrderLineDiscountPercentConditionTrue() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(1000000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(1000000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(750000)); // 25% discount
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(1500000));
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(1500000));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(1000000)); // 33% discount

        String maxOrderLineDiscountPercent = "{'>=': [{'var': 'maxOrderLineDiscountPercent'}, 32]}";
        validateOrderLevelCondition(maxOrderLineDiscountPercent, true, initialApprovalFlowContext);
    }

    @Test
    public void testMaxOrderLineDiscountPercentConditionPercentOverrideFalse() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(1000000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(1000000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(750000)); // 25% discount
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(1500000));
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(1000000));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(1000000)); // 0% discount

        String maxOrderLineDiscountPercent = "{'>=': [{'var': 'maxOrderLineDiscountPercent'}, 32]}";
        validateOrderLevelCondition(maxOrderLineDiscountPercent, false, initialApprovalFlowContext);
    }

    @Test
    public void testMaxOrderLineDiscountPercentConditionNegativeDiscountTrue() {
        order.getLineItems().get(0).setListAmount(BigDecimal.valueOf(-1000000));
        order.getLineItems().get(0).setListAmountBeforeOverride(BigDecimal.valueOf(-1000000));
        order.getLineItems().get(0).setAmount(BigDecimal.valueOf(-750000)); // 25% discount
        order.getLineItems().get(1).setListAmount(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(1).setListAmountBeforeOverride(BigDecimal.valueOf(-1500000));
        order.getLineItems().get(1).setAmount(BigDecimal.valueOf(-1000000)); // 33% discount

        String maxOrderLineDiscountPercent = "{'>=': [{'var': 'maxOrderLineDiscountPercent'}, 32]}";
        validateOrderLevelCondition(maxOrderLineDiscountPercent, true, initialApprovalFlowContext);
    }

    @Test
    public void testOrderConditionPredefinedTermFoundOnOrder() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTerm.setName("TEST_PREDEFINED_TERM_NAME");
        predefinedTermsOnOrder = List.of(getPredefinedTerm(), predefinedTerm);
        modifiedPredefinedTermsOnOrder = Set.of(predefinedTerm.getTemplateId());
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        String predefinedTermsExistsOnOrderCondition = "{'and':[{'in':['TEST_PREDEFINED_TERM_NAME',{'var':'predefinedTermNamesOnOrder'}]}]}";
        validateOrderLevelCondition(predefinedTermsExistsOnOrderCondition, true, approvalFlowContext);
    }

    @Test
    public void testOrderConditionPredefinedTermNotFoundOnOrder() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTermsOnOrder = List.of(getPredefinedTerm(), predefinedTerm);
        modifiedPredefinedTermsOnOrder = Set.of(predefinedTerm.getTemplateId());
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        String predefinedTermsExistsOnOrderCondition = "{'and':[{'in':['NOT_FOUND_TERM_NAME',{'var':'predefinedTermNamesOnOrder'}]}]}";
        validateOrderLevelCondition(predefinedTermsExistsOnOrderCondition, false, approvalFlowContext);
    }

    @Test
    public void testOrderConditionPredefinedTermDoesNotContainOnOrder() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTermsOnOrder = List.of(getPredefinedTerm(), predefinedTerm);
        modifiedPredefinedTermsOnOrder = Set.of(predefinedTerm.getTemplateId());
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        String predefinedTermsDoesNotExistOnOrderCondition = "{'and':[{'!':{'in':['NOT_FOUND_TERM_NAME',{'var':'predefinedTermNamesOnOrder'}]}}]}";
        validateOrderLevelCondition(predefinedTermsDoesNotExistOnOrderCondition, true, approvalFlowContext);
    }

    @Test
    public void testOrderConditionPredefinedTermDoesNotContainButFoundOnOrder() {
        DocumentTemplate predefinedTerm = getPredefinedTerm();
        predefinedTerm.setName("TEST_PREDEFINED_TERM_NAME");
        predefinedTermsOnOrder = List.of(getPredefinedTerm(), predefinedTerm);
        modifiedPredefinedTermsOnOrder = Set.of(predefinedTerm.getTemplateId());
        approvalFlowContext = ImmutableApprovalFlowContext.copyOf(initialApprovalFlowContext)
            .withPredefinedTermsOnOrder(predefinedTermsOnOrder)
            .withModifiedPredefinedTermsOnOrder(modifiedPredefinedTermsOnOrder);
        String predefinedTermsDoesNotExistOnOrderCondition =
            "{'and':[{'!':{'in':['TEST_PREDEFINED_TERM_NAME',{'var':'predefinedTermNamesOnOrder'}]}}]}";
        validateOrderLevelCondition(predefinedTermsDoesNotExistOnOrderCondition, false, approvalFlowContext);
    }
}
