package com.subskribe.billy.approvalflowinstance.event;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceService;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.order.OrderJsonStub;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.nio.ByteBuffer;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class ApprovalEventConsumerTest {

    private static final String TENANT_ID = UUID.randomUUID().toString();

    private final ApprovalFlowInstanceService approvalFlowInstanceServiceMock = mock(ApprovalFlowInstanceService.class);

    private final TenantIdProvider tenantIdProviderMock = mock(TenantIdProvider.class);

    private final ApprovalEventConsumer approvalEventConsumer = new ApprovalEventConsumer(tenantIdProviderMock, approvalFlowInstanceServiceMock);

    @Test
    public void processOrderSubmittedEvent() {
        OrderJson orderJson = OrderJsonStub.createOrderJson();
        byte[] bytes = UncheckedObjectMapper.defaultMapper().writeValueAsBytes(orderJson);
        Event event = Event.builder().type(EventType.ORDER_APPROVAL_FLOWS_EVALUATED).tenantId(TENANT_ID).payload(ByteBuffer.wrap(bytes)).build();

        approvalEventConsumer.tenantContextAwareOnEvent(event);

        verify(approvalFlowInstanceServiceMock).sendApprovalNotificationsForOrder(orderJson.getId());
    }
}
