package com.subskribe.billy.approvalflowinstance.service;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.approvalflow.model.ApprovalRuleConditions;
import com.subskribe.billy.approvalflow.model.ApprovalTransitionRule;
import com.subskribe.billy.approvalflow.model.ApproverType;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstance;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceData;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceState;
import com.subskribe.billy.approvalflowinstance.model.ApprovalFlowInstanceStatus;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SmartApprovalFlowTest {

    private static final String STATE_ZERO = "state0";
    private static final String STATE_ONE = "state1";
    private static final String APPROVER_ID_1 = "USER-3MN8P9B";
    private static final String APPROVER_ID_2 = "USER-TEST-ADMIN";
    private static final String TRANSITION_RULE_1 = "transition0";
    private static final String TRANSITION_RULE_2 = "transition1";
    private static final String ORDER_CONDITION_1 = "eyJhbmQiOlt7Ij09IjpbeyJ2YXIiOiJwYXltZW50VGVybSJ9LCJORVQ5MCJdfV19"; //NET90
    private static final String ORDER_CONDITION_2 = "eyJhbmQiOlt7Ij09IjpbeyJ2YXIiOiJwYXltZW50VGVybSJ9LCJORVQzMCJdfV19"; //NET30
    private static final String ORDER_LINE_CONDITION_1 =
        "InsiYW5kIjpbeyI9PSI6W3sidmFyIjoib3JkZXJMaW5lQWN0aW9uVHlwZSJ9LCJBREQiXX0seyI9PSI6W3sidmFyIjoiY2hhcmdlVHlwZSJ9LCJSRUNVUlJJTkciXX1dfSI7";
    private static final String ORDER_LINE_CONDITION_2 =
        "InsiYW5kIjpbeyI9PSI6W3sidmFyIjoib3JkZXJMaW5lQWN0aW9uVHlwZSJ9LCJBREQiXX0seyI9PSI6W3sidmFyIjoiY2hhcmdlVHlwZSJ9LCJPTkVfVElNRSJdfV19Ijs=";

    private ApprovalFlowInstance previousApprovalFlowInstance;
    private ApprovalFlowInstanceData previousApprovalFlowInstanceData;
    private ApprovalFlowInstance currentApprovalFlowInstance;
    private ApprovalFlowInstanceData currentApprovalFlowInstanceData;

    @BeforeEach
    void setup() {
        previousApprovalFlowInstance = mock(ApprovalFlowInstance.class);
        previousApprovalFlowInstanceData = mock(ApprovalFlowInstanceData.class);
        currentApprovalFlowInstance = mock(ApprovalFlowInstance.class);
        currentApprovalFlowInstanceData = mock(ApprovalFlowInstanceData.class);

        when(previousApprovalFlowInstance.getApprovalFlowData()).thenReturn(previousApprovalFlowInstanceData);
        when(currentApprovalFlowInstance.getApprovalFlowData()).thenReturn(currentApprovalFlowInstanceData);
    }

    @Test
    void shouldReturnTrueWhenPreviousInstanceIsApproved() {
        when(previousApprovalFlowInstance.getStatus()).thenReturn(ApprovalFlowInstanceStatus.APPROVED);
        boolean result = ApprovalFlowInstanceService.isPartiallyOrCompletelyApproved(previousApprovalFlowInstance);
        assertTrue(result);
    }

    @Test
    void shouldReturnTrueWhenAwaitingApprovalAndNotInInitialState() {
        when(previousApprovalFlowInstance.getStatus()).thenReturn(ApprovalFlowInstanceStatus.AWAITING_APPROVAL);
        when(previousApprovalFlowInstance.getActiveStateId()).thenReturn("state1");
        boolean result = ApprovalFlowInstanceService.isPartiallyOrCompletelyApproved(previousApprovalFlowInstance);
        assertTrue(result);
    }

    @Test
    void shouldReturnFalseWhenAwaitingApprovalInInitialState() {
        when(previousApprovalFlowInstance.getStatus()).thenReturn(ApprovalFlowInstanceStatus.AWAITING_APPROVAL);
        when(previousApprovalFlowInstance.getActiveStateId()).thenReturn(STATE_ZERO);
        boolean result = ApprovalFlowInstanceService.isPartiallyOrCompletelyApproved(previousApprovalFlowInstance);
        assertFalse(result);
    }

    @Test
    void testDifferentNumberOfStatesShouldReturnTrue() {
        ApprovalFlowInstanceState state0 = mockState(STATE_ZERO);
        ApprovalFlowInstanceState state1 = mockState(STATE_ONE);
        when(currentApprovalFlowInstanceData.getStates()).thenReturn(List.of(state0));
        when(previousApprovalFlowInstanceData.getStates()).thenReturn(List.of(state0, state1));

        assertTrue(ApprovalFlowInstanceService.haveApprovalFlowStatesChanged(currentApprovalFlowInstance, previousApprovalFlowInstance));
    }

    @Test
    void testStateMissingInPreviousShouldReturnTrue() {
        ApprovalFlowInstanceState state0 = mockState(STATE_ZERO);
        ApprovalFlowInstanceState state1 = mockState(STATE_ONE);
        when(currentApprovalFlowInstanceData.getStates()).thenReturn(List.of(state0, state1));
        when(previousApprovalFlowInstanceData.getStates()).thenReturn(List.of(state0));

        assertTrue(ApprovalFlowInstanceService.haveApprovalFlowStatesChanged(currentApprovalFlowInstance, previousApprovalFlowInstance));
    }

    @Test
    void testApproverIdChangedShouldReturnTrue() {
        ApprovalFlowInstanceState currentState = mockState(STATE_ZERO, APPROVER_ID_1, ApproverType.USER);
        ApprovalFlowInstanceState previousState = mockState(STATE_ZERO, APPROVER_ID_2, ApproverType.USER);

        when(currentApprovalFlowInstanceData.getStates()).thenReturn(List.of(currentState));
        when(previousApprovalFlowInstanceData.getStates()).thenReturn(List.of(previousState));

        assertTrue(ApprovalFlowInstanceService.haveApprovalFlowStatesChanged(currentApprovalFlowInstance, previousApprovalFlowInstance));
    }

    @Test
    void testApproverTypeChangedShouldReturnTrue() {
        ApprovalFlowInstanceState currentState = mockState(STATE_ZERO, APPROVER_ID_1, ApproverType.USER);
        ApprovalFlowInstanceState previousState = mockState(STATE_ZERO, APPROVER_ID_1, ApproverType.USER_GROUP);

        when(currentApprovalFlowInstanceData.getStates()).thenReturn(List.of(currentState));
        when(previousApprovalFlowInstanceData.getStates()).thenReturn(List.of(previousState));

        assertTrue(ApprovalFlowInstanceService.haveApprovalFlowStatesChanged(currentApprovalFlowInstance, previousApprovalFlowInstance));
    }

    @Test
    void testNoChangeShouldReturnFalse() {
        ApprovalFlowInstanceState currentState = mockState(STATE_ZERO, APPROVER_ID_1, ApproverType.USER);
        ApprovalFlowInstanceState previousState = mockState(STATE_ZERO, APPROVER_ID_1, ApproverType.USER);

        when(currentApprovalFlowInstanceData.getStates()).thenReturn(List.of(currentState));
        when(previousApprovalFlowInstanceData.getStates()).thenReturn(List.of(previousState));

        assertFalse(ApprovalFlowInstanceService.haveApprovalFlowStatesChanged(currentApprovalFlowInstance, previousApprovalFlowInstance));
    }

    // Test for approval flow conditions
    @Test
    void testDifferentNumberOfTransitionRulesShouldReturnTrue() {
        ApprovalTransitionRule rule1 = mockTransitionRule(TRANSITION_RULE_1);
        ApprovalTransitionRule rule2 = mockTransitionRule(TRANSITION_RULE_2);

        when(currentApprovalFlowInstanceData.getTransitionRules()).thenReturn(List.of(rule1));
        when(previousApprovalFlowInstanceData.getTransitionRules()).thenReturn(List.of(rule1, rule2));

        assertTrue(ApprovalFlowInstanceService.haveApprovalFlowConditionsChanged(currentApprovalFlowInstance, previousApprovalFlowInstance));
    }

    @Test
    void testTransitionRuleMissingInPreviousShouldReturnTrue() {
        ApprovalTransitionRule rule1 = mockTransitionRule(TRANSITION_RULE_1);
        ApprovalTransitionRule rule2 = mockTransitionRule(TRANSITION_RULE_2);
        when(currentApprovalFlowInstanceData.getTransitionRules()).thenReturn(List.of(rule1, rule2));
        when(previousApprovalFlowInstanceData.getTransitionRules()).thenReturn(List.of(rule1));

        assertTrue(ApprovalFlowInstanceService.haveApprovalFlowConditionsChanged(currentApprovalFlowInstance, previousApprovalFlowInstance));
    }

    @Test
    void testConditionsMismatchShouldReturnTrue() {
        ApprovalTransitionRule currentRule = mockTransitionRule(
            TRANSITION_RULE_1,
            new ApprovalRuleConditions(ORDER_CONDITION_1, ORDER_LINE_CONDITION_1)
        );
        ApprovalTransitionRule previousRule = mockTransitionRule(TRANSITION_RULE_1, new ApprovalRuleConditions(ORDER_CONDITION_1, ""));

        when(currentApprovalFlowInstanceData.getTransitionRules()).thenReturn(List.of(currentRule));
        when(previousApprovalFlowInstanceData.getTransitionRules()).thenReturn(List.of(previousRule));

        assertTrue(ApprovalFlowInstanceService.haveApprovalFlowConditionsChanged(currentApprovalFlowInstance, previousApprovalFlowInstance));
    }

    @Test
    void testNoChangeInConditionsShouldReturnFalse() {
        ApprovalRuleConditions approvalRuleConditions = new ApprovalRuleConditions(ORDER_CONDITION_1, ORDER_LINE_CONDITION_1);

        ApprovalTransitionRule currentRule = mockTransitionRule(TRANSITION_RULE_1, approvalRuleConditions);
        ApprovalTransitionRule previousRule = mockTransitionRule(TRANSITION_RULE_1, approvalRuleConditions);

        when(currentApprovalFlowInstanceData.getTransitionRules()).thenReturn(List.of(currentRule));
        when(previousApprovalFlowInstanceData.getTransitionRules()).thenReturn(List.of(previousRule));

        assertFalse(ApprovalFlowInstanceService.haveApprovalFlowConditionsChanged(currentApprovalFlowInstance, previousApprovalFlowInstance));
    }

    @Test
    void bothNullConditionsShouldReturnFalse() {
        assertFalse(ApprovalFlowInstanceService.conditionsHaveChanged(null, null));
    }

    @Test
    void currentNullPreviousNotNullShouldReturnTrue() {
        ApprovalRuleConditions previous = new ApprovalRuleConditions(ORDER_CONDITION_1, ORDER_LINE_CONDITION_1);

        assertTrue(ApprovalFlowInstanceService.conditionsHaveChanged(null, previous));
    }

    @Test
    void currentNotNullPreviousNullShouldReturnTrue() {
        ApprovalRuleConditions current = new ApprovalRuleConditions(ORDER_CONDITION_1, ORDER_LINE_CONDITION_1);

        assertTrue(ApprovalFlowInstanceService.conditionsHaveChanged(current, null));
    }

    @Test
    void differentOrderConditionShouldReturnTrue() {
        ApprovalRuleConditions current = new ApprovalRuleConditions(ORDER_CONDITION_1, ORDER_LINE_CONDITION_1);
        ApprovalRuleConditions previous = new ApprovalRuleConditions(ORDER_CONDITION_2, ORDER_LINE_CONDITION_1);

        assertTrue(ApprovalFlowInstanceService.conditionsHaveChanged(current, previous));
    }

    @Test
    void differentOrderLineConditionShouldReturnTrue() {
        ApprovalRuleConditions current = new ApprovalRuleConditions(ORDER_CONDITION_1, ORDER_LINE_CONDITION_1);
        ApprovalRuleConditions previous = new ApprovalRuleConditions(ORDER_CONDITION_1, ORDER_LINE_CONDITION_2);

        assertTrue(ApprovalFlowInstanceService.conditionsHaveChanged(current, previous));
    }

    @Test
    void sameConditions_ShouldReturnFalse() {
        ApprovalRuleConditions current = new ApprovalRuleConditions(ORDER_CONDITION_1, ORDER_LINE_CONDITION_1);
        ApprovalRuleConditions previous = new ApprovalRuleConditions(ORDER_CONDITION_1, ORDER_LINE_CONDITION_1);
        assertFalse(ApprovalFlowInstanceService.conditionsHaveChanged(current, previous));
    }

    // Helper method to mock ApprovalFlowInstanceState
    private ApprovalFlowInstanceState mockState(String id) {
        return mockState(id, APPROVER_ID_2, ApproverType.USER);
    }

    private ApprovalFlowInstanceState mockState(String id, String approverId, ApproverType approverType) {
        ApprovalFlowInstanceState state = mock(ApprovalFlowInstanceState.class);
        when(state.getApprovalStateId()).thenReturn(id);
        when(state.getApproverId()).thenReturn(approverId);
        when(state.getApproverType()).thenReturn(approverType);
        return state;
    }

    // Helper methods to mock ApprovalTransitionRule
    private ApprovalTransitionRule mockTransitionRule(String id) {
        return mockTransitionRule(id, new ApprovalRuleConditions(ORDER_CONDITION_1, ORDER_LINE_CONDITION_1));
    }

    private ApprovalTransitionRule mockTransitionRule(String id, ApprovalRuleConditions conditions) {
        ApprovalTransitionRule rule = mock(ApprovalTransitionRule.class);
        when(rule.getTransitionRuleId()).thenReturn(id);
        when(rule.getRuleConditions()).thenReturn(conditions);
        return rule;
    }
}
