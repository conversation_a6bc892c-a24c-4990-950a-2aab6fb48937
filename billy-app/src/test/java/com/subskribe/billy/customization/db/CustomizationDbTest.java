package com.subskribe.billy.customization.db;

import static org.mockito.Mockito.when;

import com.subskribe.billy.customization.model.CustomizationContext;
import com.subskribe.billy.customization.model.CustomizationDefinition;
import com.subskribe.billy.customization.model.ImmutableCustomizationDefinitionInput;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.WithDb;
import java.io.IOException;
import java.util.Optional;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CustomizationDbTest extends WithDb {

    private static final String TENANT_ID = "27b59431-0cc7-4c87-883b-be345e9bd525";

    @Mock
    private TenantIdProvider tenantIdProvider;

    private CustomizationDefinitionDAO customizationDefinitionDAO;

    @BeforeAll
    public void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        customizationDefinitionDAO = new CustomizationDefinitionDAO(dslContextProvider, tenantIdProvider);
    }

    @Test
    @Order(1)
    public void whenNoCustomizationsArePresent_thenGetLatestCustomizationReturnsEmpty() {
        Optional<CustomizationDefinition> customizationDefinitionOptional = customizationDefinitionDAO.getLatestCustomizationDefinition(
            CustomizationContext.SELECTION_CUSTOMIZATION
        );
        Assertions.assertThat(customizationDefinitionOptional).isEmpty();
    }

    @Test
    @Order(2)
    public void whenAddingFirstCustomizationVersion_thenCustomizationSucceeds() {
        ImmutableCustomizationDefinitionInput input = ImmutableCustomizationDefinitionInput.builder()
            .customizationContext(CustomizationContext.SELECTION_CUSTOMIZATION)
            .zeppaScript(
                """
                selectionCustomization {}
                """
            )
            .build();
        CustomizationDefinition stored = customizationDefinitionDAO.upsertCustomizationDefinition(input, 0, "test");
        Optional<CustomizationDefinition> customizationDefinitionOptional = customizationDefinitionDAO.getLatestCustomizationDefinition(
            CustomizationContext.SELECTION_CUSTOMIZATION
        );
        Assertions.assertThat(customizationDefinitionOptional).isPresent();
        CustomizationDefinition latest = customizationDefinitionOptional.get();

        Assertions.assertThat(stored).isEqualTo(latest);
        Assertions.assertThat(latest.getVersion()).isEqualTo(1);
        Assertions.assertThat(latest.getCreatedOn()).isNotNull();
        Assertions.assertThat(latest.getCustomizationContext()).isEqualByComparingTo(CustomizationContext.SELECTION_CUSTOMIZATION);
        Assertions.assertThat(latest.getZeppaScript()).isEqualTo(input.getZeppaScript());
    }

    @Test
    @Order(3)
    public void whenUpdatingCustomizationWithWrongVersion_thenUpdateFails() {
        ImmutableCustomizationDefinitionInput input = ImmutableCustomizationDefinitionInput.builder()
            .customizationContext(CustomizationContext.SELECTION_CUSTOMIZATION)
            .zeppaScript(
                """
                selectionCustomization {
                    rule {
                    }
                }
                """
            )
            .build();

        Assertions.assertThatThrownBy(() -> customizationDefinitionDAO.upsertCustomizationDefinition(input, 0, "test"))
            .isInstanceOf(ConflictingStateException.class)
            .hasMessageContaining("To update customization, you must pass the current expected version of 1");

        // updating to the correct version succeeds
        CustomizationDefinition updated = customizationDefinitionDAO.upsertCustomizationDefinition(input, 1, "test");
        Assertions.assertThat(updated).isNotNull();
        Assertions.assertThat(updated.getVersion()).isEqualTo(2);
        Assertions.assertThat(updated.getZeppaScript()).isEqualTo(input.getZeppaScript());

        // the latest should be the same as the updated
        Optional<CustomizationDefinition> latest = customizationDefinitionDAO.getLatestCustomizationDefinition(
            CustomizationContext.SELECTION_CUSTOMIZATION
        );
        Assertions.assertThat(latest).isPresent();
        Assertions.assertThat(latest.get()).isEqualTo(updated);
    }
}
