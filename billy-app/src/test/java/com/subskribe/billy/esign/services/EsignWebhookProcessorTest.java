package com.subskribe.billy.esign.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.esign.db.EsignDAO;
import com.subskribe.billy.esign.model.ElectronicSignature;
import com.subskribe.billy.esign.model.ElectronicSignatureProvider;
import com.subskribe.billy.esign.model.pandadoc.PandaDocWebhookPayload;
import com.subskribe.billy.esign.model.pandadoc.PandaDocWebhookPayloadData;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.webhook.ImmutableIncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhookTenantLookupResult;
import com.subskribe.billy.shared.webhook.IncomingWebhookType;
import java.util.Optional;
import javax.ws.rs.core.MultivaluedHashMap;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class EsignWebhookProcessorTest {

    @Mock
    private EsignDAO esignDAO;

    @Mock
    private EsignService esignService;

    private EsignWebhookProcessor processor;
    private final ObjectMapper objectMapper = JacksonProvider.defaultMapper();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        processor = new EsignWebhookProcessor(esignDAO, esignService);
    }

    @Test
    void testGetWebhookType() {
        IncomingWebhookType webhookType = processor.getWebhookType();
        assertThat(webhookType).isEqualTo(EsignWebhookProcessor.WEBHOOK_TYPE);
        assertThat(webhookType.name()).isEqualTo("pandadoc");
    }

    @Test
    void testFindTenantIdWhenSignatureExists() throws JsonProcessingException {
        String documentId = "doc-123";
        String tenantId = "tenant-123";

        PandaDocWebhookPayload payload = createSamplePayload(documentId);
        String payloadJson = objectMapper.writeValueAsString(payload);

        IncomingWebhook incomingWebhook = createWebhook(payloadJson);

        ElectronicSignature signature = new ElectronicSignature();
        signature.setTenantId(tenantId);
        signature.setExternalDocumentId(documentId);

        when(esignDAO.getSignatureByDocumentId(ElectronicSignatureProvider.PANDADOC, documentId)).thenReturn(Optional.of(signature));

        IncomingWebhookTenantLookupResult result = processor.findTenantId(incomingWebhook);

        IncomingWebhookTenantLookupResult expectedResult = new IncomingWebhookTenantLookupResult(Optional.of(tenantId), true);
        assertThat(result).isEqualTo(expectedResult);

        verify(esignDAO).getSignatureByDocumentId(ElectronicSignatureProvider.PANDADOC, documentId);
    }

    @Test
    void testFindTenantIdWhenSignatureDoesNotExist() throws JsonProcessingException {
        String documentId = "doc-123";

        PandaDocWebhookPayload payload = createSamplePayload(documentId);
        String payloadJson = objectMapper.writeValueAsString(payload);

        IncomingWebhook incomingWebhook = createWebhook(payloadJson);

        when(esignDAO.getSignatureByDocumentId(ElectronicSignatureProvider.PANDADOC, documentId)).thenReturn(Optional.empty());

        IncomingWebhookTenantLookupResult result = processor.findTenantId(incomingWebhook);

        IncomingWebhookTenantLookupResult expectedResult = new IncomingWebhookTenantLookupResult(Optional.empty(), true);
        assertThat(result).isEqualTo(expectedResult);

        verify(esignDAO).getSignatureByDocumentId(ElectronicSignatureProvider.PANDADOC, documentId);
    }

    @Test
    void testProcessWebhook() throws JsonProcessingException {
        String documentId = "doc-123";

        PandaDocWebhookPayload payload = createSamplePayload(documentId);
        String payloadJson = objectMapper.writeValueAsString(payload);

        IncomingWebhook incomingWebhook = createWebhook(payloadJson);

        processor.process(incomingWebhook);

        ArgumentCaptor<PandaDocWebhookPayload> payloadCaptor = ArgumentCaptor.forClass(PandaDocWebhookPayload.class);
        verify(esignService).updateStatusFromWebhook(payloadCaptor.capture(), eq(false));

        PandaDocWebhookPayload capturedPayload = payloadCaptor.getValue();
        assertThat(capturedPayload.getData().getId()).isEqualTo(documentId);
    }

    @Test
    void testSerializePayloadWithInvalidJson() {
        String invalidJson = "{invalid-json}";
        IncomingWebhook incomingWebhook = createWebhook(invalidJson);

        assertThatThrownBy(() -> processor.findTenantId(incomingWebhook))
            .isInstanceOf(ServiceFailureException.class)
            .hasMessage("Unable to parse Esign webhook payload.");
    }

    private PandaDocWebhookPayload createSamplePayload(String documentId) {
        PandaDocWebhookPayload payload = new PandaDocWebhookPayload();
        PandaDocWebhookPayloadData data = new PandaDocWebhookPayloadData();
        data.setId(documentId);
        payload.setData(data);
        return payload;
    }

    private IncomingWebhook createWebhook(String payload) {
        return ImmutableIncomingWebhook.builder()
            .webhookType(EsignWebhookProcessor.WEBHOOK_TYPE)
            .payload(payload)
            .receivedOn(System.currentTimeMillis())
            .headers(new MultivaluedHashMap<>())
            .build();
    }
}
