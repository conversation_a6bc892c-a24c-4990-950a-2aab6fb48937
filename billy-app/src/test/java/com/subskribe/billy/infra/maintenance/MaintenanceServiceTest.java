package com.subskribe.billy.infra.maintenance;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.infra.maintenance.db.ScheduledMaintenanceDAO;
import com.subskribe.billy.infra.maintenance.model.ImmutableScheduledMaintenance;
import com.subskribe.billy.infra.maintenance.model.ScheduledMaintenance;
import java.time.Clock;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MaintenanceServiceTest {

    private MaintenanceService maintenanceService;
    private ScheduledMaintenanceDAO scheduledMaintenanceDAO;
    private Clock clock;

    private Instant now;

    @BeforeEach
    void setUp() {
        scheduledMaintenanceDAO = mock(ScheduledMaintenanceDAO.class);
        clock = mock(Clock.class);
        maintenanceService = new MaintenanceService(scheduledMaintenanceDAO, clock);
        now = setupNow();
    }

    @Test
    void testGlobalMaintenanceActiveNowForLoggedInTenant() {
        ScheduledMaintenance maintenance = buildMaintenance(now, -120, 120, null);
        setupDao(now, List.of(maintenance));

        Optional<ScheduledMaintenance> result = maintenanceService.getActiveMaintenance(Optional.of("tenant_id"));

        assertThat(result).isPresent().contains(maintenance);
    }

    @Test
    void testGlobalMaintenanceNotYetActiveForLoggedInTenant() {
        ScheduledMaintenance maintenance = buildMaintenance(now, 60, 120, null);
        setupDao(now, List.of(maintenance));

        Optional<ScheduledMaintenance> result = maintenanceService.getActiveMaintenance(Optional.of("tenant_id"));

        assertThat(result).isEmpty();
    }

    @Test
    void testGlobalMaintenanceExpiredForLoggedInTenant() {
        ScheduledMaintenance maintenance = buildMaintenance(now, -60, -15, null);
        setupDao(now, List.of(maintenance));

        Optional<ScheduledMaintenance> result = maintenanceService.getActiveMaintenance(Optional.of("tenant_id"));

        assertThat(result).isEmpty();
    }

    @Test
    void testGlobalMaintenanceActiveNowForNoLoggedInTenant() {
        ScheduledMaintenance maintenance = buildMaintenance(now, -120, 120, null);
        setupDao(now, List.of(maintenance));

        Optional<ScheduledMaintenance> result = maintenanceService.getActiveMaintenance(Optional.empty());

        assertThat(result).isPresent().contains(maintenance);
    }

    @Test
    void testGlobalMaintenanceNotYetActiveForNoLoggedInTenant() {
        ScheduledMaintenance maintenance = buildMaintenance(now, 60, 120, null);
        setupDao(now, List.of(maintenance));

        Optional<ScheduledMaintenance> result = maintenanceService.getActiveMaintenance(Optional.empty());

        assertThat(result).isEmpty();
    }

    @Test
    void testGlobalMaintenanceExpiredForNoLoggedInTenant() {
        ScheduledMaintenance maintenance = buildMaintenance(now, -60, -15, null);
        setupDao(now, List.of(maintenance));

        Optional<ScheduledMaintenance> result = maintenanceService.getActiveMaintenance(Optional.empty());

        assertThat(result).isEmpty();
    }

    private Instant setupNow() {
        Instant now = Instant.now();
        when(clock.instant()).thenReturn(now);
        return now;
    }

    private ScheduledMaintenance buildMaintenance(Instant now, int startOffset, int endOffset, String tenantId) {
        return ImmutableScheduledMaintenance.builder()
            .from(baseMaintenance())
            .startedOn(now.plus(startOffset, ChronoUnit.SECONDS))
            .expiresOn(now.plus(endOffset, ChronoUnit.SECONDS))
            .tenantId(tenantId)
            .build();
    }

    private void setupDao(Instant now, List<ScheduledMaintenance> activeMaintenances) {
        when(scheduledMaintenanceDAO.findAllMaintenancesActiveBetween(now.minus(1, ChronoUnit.MINUTES), now.plus(1, ChronoUnit.MINUTES))).thenReturn(
            activeMaintenances
        );
    }

    private static ScheduledMaintenance baseMaintenance() {
        return ImmutableScheduledMaintenance.builder()
            .addedBy("<EMAIL>")
            .id(UUID.randomUUID())
            .expiresOn(Instant.now())
            .startedOn(Instant.now())
            .reason("test")
            .tenantId("tenant_id")
            .build();
    }
}
