package com.subskribe.billy.infra;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class SystemUtilsTest {

    private SystemDataProvider systemDataProvider;

    @BeforeEach
    void setUp() {
        systemDataProvider = new SystemDataProvider();
    }

    // We will only test the existence of Optionals, not actual values (hard to mock)
    @Test
    void testWithHome() {
        // POSIX specs say that the HOME env var should always exist
        Optional<String> result = systemDataProvider.getValueOfEnvironmentVariable("HOME");
        assertThat(result).isPresent();
    }

    @Test
    void testWithNonExistingVariable() {
        Optional<String> result = systemDataProvider.getValueOfEnvironmentVariable("ThisShouldNeverExist");
        assertThat(result).isEmpty();
    }
}
