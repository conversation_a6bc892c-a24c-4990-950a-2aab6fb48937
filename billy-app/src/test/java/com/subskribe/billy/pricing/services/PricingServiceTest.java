package com.subskribe.billy.pricing.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.model.ImmutableCurrencyConversionRate;
import com.subskribe.billy.pricing.model.PriceModel;
import com.subskribe.billy.productcatalog.model.PriceTier;
import com.subskribe.billy.productcatalog.ratecard.RateCardTestData;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.pecuniary.ForeignExchangeUtils;
import com.subskribe.billy.shared.pecuniary.Numbers;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class PricingServiceTest {

    private static final List<PriceTier> PRICE_TIERS = List.of(
        PriceTier.of(3L, BigDecimal.valueOf(5)),
        PriceTier.of(5L, BigDecimal.valueOf(7)),
        PriceTier.of(null, BigDecimal.valueOf(11))
    );

    private static final BigDecimal CONVERSION_RATE = BigDecimal.valueOf(1.51);
    private static final CurrencyConversionRate currencyConversionRate = ImmutableCurrencyConversionRate.builder()
        .fromCurrency("USD")
        .toCurrency("AUD")
        .conversionRate(CONVERSION_RATE)
        .effectiveDate(1612137600000L)
        .roundingTreatment(RoundingMode.UNNECESSARY)
        .build();

    private PriceModel getPriceModel(ChargeModel chargeModel) {
        return new PriceModel(chargeModel, PRICE_TIERS, BigDecimal.valueOf(3), currencyConversionRate);
    }

    private final FeatureService featureService = mock(FeatureService.class);

    @BeforeEach
    void setUp() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(true);
    }

    @Test
    public void testCalculateLargeTotal() {
        var model = new PriceModel(ChargeModel.PER_UNIT, null, BigDecimal.valueOf(1000), currencyConversionRate);
        var result = PricingService.calculateCostWithQuantity(model, 200000000, featureService);
        BigDecimal expected = new BigDecimal("200000000000").multiply(CONVERSION_RATE).setScale(Numbers.PRICE_DISPLAY_SCALE);
        assertEquals(expected, result); // 302000000000.00000
    }

    @Test
    public void testCalculateLargeTotal_FF_OFF() {
        when(featureService.isEnabled(Feature.FX_ROUNDING_V2)).thenReturn(false);
        var model = new PriceModel(ChargeModel.PER_UNIT, null, BigDecimal.valueOf(1000), currencyConversionRate);
        var result = PricingService.calculateCostWithQuantity(model, 200000000, featureService);
        assertEquals(new BigDecimal("200000000000").multiply(CONVERSION_RATE), result);
    }

    @Test
    public void testPricingServiceWithRateCard() {
        RateCard rateCard = RateCardTestData.makeTestRateCard();
        var model = new PriceModel(
            ChargeModel.RATE_CARD_LOOKUP,
            rateCard,
            RateCardTestData.ATTRIBUTE_REFERENCES_COMBINATION_TWO,
            currencyConversionRate
        );
        var result = PricingService.calculateCostWithQuantity(model, 100, featureService);
        var expectedAmount = ForeignExchangeUtils.applyExchangeRate(
            BigDecimal.valueOf(100).multiply(RateCardTestData.ATTRIBUTE_REFERENCES_TWO_PRICE),
            currencyConversionRate,
            featureService
        );
        assertEquals(expectedAmount.intValue(), result.intValue());
    }

    @Test
    public void testPricingServiceWithRateCardAttributeNotPresent() {
        RateCard rateCard = RateCardTestData.makeTestRateCard();
        var model = new PriceModel(
            ChargeModel.RATE_CARD_LOOKUP,
            rateCard,
            RateCardTestData.ATTRIBUTE_REFERENCES_COMBINATION_NOT_PRESENT,
            currencyConversionRate
        );
        var result = PricingService.calculateCostWithQuantity(model, 100, featureService);
        Assertions.assertThat(result.compareTo(BigDecimal.ZERO)).isEqualTo(0);
    }

    @Test
    public void testCalculateCostWithQuantityFlatFee() {
        var model = getPriceModel(ChargeModel.FLAT_FEE);
        BigDecimal result;

        result = PricingService.calculateCostWithQuantity(model, 0, featureService);
        assertEquals(BigDecimal.valueOf(3 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, -1, featureService);
        assertEquals(BigDecimal.valueOf(-3 * 1.51).intValue(), result.intValue());
    }

    @Test
    public void testCalculateCostWithQuantityFlatUnit() {
        var model = getPriceModel(ChargeModel.PER_UNIT);
        BigDecimal result;

        result = PricingService.calculateCostWithQuantity(model, 0, featureService);
        assertEquals(0, result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 2, featureService);
        assertEquals(BigDecimal.valueOf(6 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, -2, featureService);
        assertEquals(BigDecimal.valueOf(-6 * 1.51).intValue(), result.intValue());
    }

    @Test
    public void testCalculateCostWithQuantityVolume() {
        var model = getPriceModel(ChargeModel.VOLUME);
        BigDecimal result;

        result = PricingService.calculateCostWithQuantity(model, 0, featureService);
        assertEquals(0, result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 2, featureService);
        assertEquals(BigDecimal.valueOf(2 * 5 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 3, featureService);
        assertEquals(BigDecimal.valueOf(3 * 5 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 4, featureService);
        assertEquals(BigDecimal.valueOf(4 * 7 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 100, featureService);
        assertEquals(BigDecimal.valueOf(100 * 11 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, -100, featureService);
        assertEquals(BigDecimal.valueOf(-100 * 11 * 1.51).intValue(), result.intValue());
    }

    @Test
    public void testCalculateCostWithQuantityTiered() {
        var model = getPriceModel(ChargeModel.TIERED);
        BigDecimal result;

        result = PricingService.calculateCostWithQuantity(model, 0, featureService);
        assertEquals(0, result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 2, featureService);
        assertEquals(BigDecimal.valueOf(2 * 5 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, -2, featureService);
        assertEquals(BigDecimal.valueOf(-2 * 5 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 3, featureService);
        assertEquals(BigDecimal.valueOf(3 * 5 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 4, featureService);
        assertEquals(BigDecimal.valueOf((3 * 5 + 7) * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 5, featureService);
        assertEquals(BigDecimal.valueOf((3 * 5 + 2 * 7) * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 999, featureService);
        assertEquals(BigDecimal.valueOf((3 * 5 + 2 * 7 + (999 - 3 - 2) * 11) * 1.51).intValue(), result.intValue());
    }

    @Test
    public void testCalculateCostWithQuantityBlock() {
        var model = getPriceModel(ChargeModel.BLOCK);
        BigDecimal result;

        result = PricingService.calculateCostWithQuantity(model, 0, featureService);
        assertEquals(0, result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 2, featureService);
        assertEquals(BigDecimal.valueOf(5 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, -2, featureService);
        assertEquals(BigDecimal.valueOf(-5 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 3, featureService);
        assertEquals(BigDecimal.valueOf(5 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 4, featureService);
        assertEquals(BigDecimal.valueOf(7 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 5, featureService);
        assertEquals(BigDecimal.valueOf(7 * 1.51).intValue(), result.intValue());

        result = PricingService.calculateCostWithQuantity(model, 999, featureService);
        assertEquals(BigDecimal.valueOf(11 * 1.51).intValue(), result.intValue());
    }
}
