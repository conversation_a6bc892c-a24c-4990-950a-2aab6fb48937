package com.subskribe.billy.event;

import com.subskribe.billy.event.model.DefaultStreamPartitionKey;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventStatus;
import com.subskribe.billy.event.model.EventType;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;

public class EventStubber {

    public static final String TENANT_ID = UUID.randomUUID().toString();
    public static final String ACCOUNT_ID = RandomStringUtils.randomAlphanumeric(10);

    public static Event eventStub() {
        return Event.builder()
            .id(UUID.randomUUID().toString())
            .tenantId(UUID.randomUUID().toString())
            .partitionKey(new DefaultStreamPartitionKey(String.format("%s/%s", TENANT_ID, ACCOUNT_ID)))
            .generatedTime(Instant.now())
            .type(EventType.INVOICE_POSTED)
            .sequenceNumber(10L)
            .payload(ByteBuffer.wrap("INV-000001".getBytes(StandardCharsets.UTF_8)))
            .status(EventStatus.ENQUEUED)
            .md5("foo")
            .build();
    }
}
