package com.subskribe.billy.event.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.kinesis.KinesisWriterProvider;
import com.subskribe.billy.event.EventTestBase;
import com.subskribe.billy.event.consumer.EventConsumer;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.streams.kinesis.KinesisStreamWriter;
import com.subskribe.billy.exception.TenantNotFoundException;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class EventPumpingServiceTest extends EventTestBase {

    @Mock
    private EventConsumer eventConsumer;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private KinesisWriterProvider kinesisClientProvider;

    @Mock
    private KinesisStreamWriter kinesisStreamWriter;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Captor
    private ArgumentCaptor<Event> onMessageCaptor;

    @Captor
    private ArgumentCaptor<List<Event>> markStreamedCaptor;

    private EventPumpingService eventPumpingService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        eventPumpingService = new EventPumpingService(eventConsumer, eventDAO, tenantIdProvider, billyConfiguration, kinesisClientProvider);

        when(kinesisClientProvider.getKinesisStreamWriter()).thenReturn(kinesisStreamWriter);
        when(kinesisClientProvider.getStreamNamePrefix()).thenReturn("unit-test");
        when(tenantIdProvider.provideTenantIdString()).thenThrow(TenantNotFoundException.class);
    }

    @Test
    public void whenNoEventsArePresent_thenNoneArePumped() {
        when(eventDAO.readEnqueuedEventsInOrder(EventPumpingService.EVENTS_TO_STREAM_PER_BATCH)).thenReturn(List.of());
        eventPumpingService.readEventsAndPump();
        verify(eventConsumer, never()).onEvent(any());
        verify(eventDAO, never()).markEventsStreamed(any());
    }

    @Test
    public void whenEventsArePresent_thenTheyAreProcessedCorrectly() {
        Event mockEventOne = mockEvent(TEST_TENANT_ONE);
        Event mockEventTwo = mockEvent(TEST_TENANT_TWO);
        when(eventDAO.readEnqueuedEventsInOrder(EventPumpingService.EVENTS_TO_STREAM_PER_BATCH)).thenReturn(List.of(mockEventOne, mockEventTwo));

        eventPumpingService.readEventsAndPump();
        // since there are two tenants the onEvents will be called twice
        // once for the TEST_TENANT_ONE and once for TEST_TENANT_TWO
        verify(eventConsumer, times(2)).onEvent(onMessageCaptor.capture());
        verify(eventDAO, times(1)).markEventsStreamed(markStreamedCaptor.capture());

        List<Event> onMessageAllEventsList = onMessageCaptor.getAllValues();
        List<Event> onMarkStreamedList = markStreamedCaptor.getValue();

        Assertions.assertThat(onMessageAllEventsList).hasSize(2);
        Assertions.assertThat(onMarkStreamedList).hasSize(2);

        // tenant one on message
        Event tenantOneOnMessage = onMessageAllEventsList.get(0);
        Assertions.assertThat(tenantOneOnMessage).isNotNull();
        Assertions.assertThat(tenantOneOnMessage.getId()).isEqualTo(mockEventOne.getId());

        Event tenantTwoOnMessage = onMessageAllEventsList.get(1);
        Assertions.assertThat(tenantTwoOnMessage).isNotNull();
        Assertions.assertThat(tenantTwoOnMessage.getId()).isEqualTo(mockEventTwo.getId());
    }
}
