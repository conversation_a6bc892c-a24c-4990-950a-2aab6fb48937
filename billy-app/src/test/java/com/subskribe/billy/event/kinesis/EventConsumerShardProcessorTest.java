package com.subskribe.billy.event.kinesis;

import static org.mockito.Mockito.times;

import com.subskribe.billy.event.EventTestBase;
import com.subskribe.billy.event.consumer.EventConsumer;
import com.subskribe.billy.event.consumer.EventConsumerConfiguration;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.streams.kinesis.EventConsumerShardProcessor;
import com.subskribe.billy.exception.handling.FixedRetryCountExceptionHandlingStrategy;
import com.subskribe.billy.shared.enums.MessageLevel;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import software.amazon.kinesis.lifecycle.events.InitializationInput;
import software.amazon.kinesis.lifecycle.events.ProcessRecordsInput;
import software.amazon.kinesis.processor.RecordProcessorCheckpointer;
import software.amazon.kinesis.retrieval.KinesisClientRecord;

public class EventConsumerShardProcessorTest extends EventTestBase {

    private static final RuntimeException[] EXCEPTIONS_THROWN_ONCE = { new RuntimeException("Whoops1!!") };

    @Mock
    InitializationInput mockInitializationInput;

    @Mock
    ProcessRecordsInput processRecordsInput;

    @Mock
    KinesisClientRecord kinesisClientRecord;

    @Mock
    RecordProcessorCheckpointer mockRecordProcessorCheckpointer;

    @Mock
    EventConsumer mockEventConsumer;

    @Mock
    EventConsumerConfiguration mockEventConsumerConfiguration;

    @Captor
    ArgumentCaptor<Event> eventArgumentCaptor;

    Event mockEvent;

    EventConsumerShardProcessor eventConsumerShardProcessor;

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        Mockito.when(mockInitializationInput.shardId()).thenReturn("shard-00001");
        Mockito.when(mockEventConsumer.getConfiguration()).thenReturn(mockEventConsumerConfiguration);
        Mockito.when(mockEventConsumerConfiguration.getExceptionHandlingStrategy()).thenReturn(
            FixedRetryCountExceptionHandlingStrategy.using(2, Duration.ofMillis(10), MessageLevel.INFO)
        );
        mockEvent = mockEvent(TEST_TENANT_ONE);
        Mockito.when(processRecordsInput.records()).thenReturn(List.of(kinesisClientRecord));
        Mockito.when(processRecordsInput.checkpointer()).thenReturn(mockRecordProcessorCheckpointer);
        byte[] payloadBytes = JacksonProvider.defaultMapper().writeValueAsBytes(mockEvent);
        Mockito.when(kinesisClientRecord.data()).thenReturn(ByteBuffer.wrap(payloadBytes).asReadOnlyBuffer());
        eventConsumerShardProcessor = new EventConsumerShardProcessor(mockEventConsumer, "test-stream");
    }

    @Test
    public void whenEventConsumerThrowsTheRespectiveExceptionHandlingStrategyIsUsed() {
        Mockito.doThrow(EXCEPTIONS_THROWN_ONCE).doAnswer(invocation -> null).when(mockEventConsumer).onEvent(Mockito.any());
        eventConsumerShardProcessor.processRecords(processRecordsInput);
        Mockito.verify(mockEventConsumer, times(2)).onEvent(eventArgumentCaptor.capture());

        // the same event should have been delivered twice
        List<Event> eventArgumentCaptures = eventArgumentCaptor.getAllValues();
        Assertions.assertThat(eventArgumentCaptures).hasSize(2);
        // same event should be delivered twice
        Assertions.assertThat(eventArgumentCaptures.get(0).getId()).isEqualTo(eventArgumentCaptures.get(1).getId());
        Event eventDelivered = eventArgumentCaptures.get(0);
        Assertions.assertThat(eventDelivered.getType()).isEqualByComparingTo(mockEvent.getType());
        Assertions.assertThat(eventDelivered.getSequenceNumber()).isEqualByComparingTo(mockEvent.getSequenceNumber());
        Assertions.assertThat(eventDelivered.getPartitionKey().getKey()).isEqualTo(mockEvent.getPartitionKey().getKey());
    }
}
