package com.subskribe.billy.event.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.event.EventTestBase;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventInput;
import com.subskribe.billy.event.model.EventStatus;
import com.subskribe.billy.event.model.EventTransactionContext;
import com.subskribe.billy.exception.ServiceFailureException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import org.assertj.core.api.Assertions;
import org.jooq.DSLContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class EventPublishingServiceTest extends EventTestBase {

    @Mock
    private DSLContext dslContext;

    private EventPublishingService eventPublishingService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(eventDAO.writeEvent(any(), any())).thenReturn(mockEvent(TEST_TENANT_ONE));
        eventPublishingService = new EventPublishingService(eventDAO);
    }

    @Test
    public void whenBadMetadataIsPassed_thenCallFailsAsExpected() {
        EventInput eventInput = testBadEventInput();
        EventTransactionContext context = eventTransactionContext(dslContext);

        Assertions.assertThatThrownBy(() -> eventPublishingService.publishEventTransactionally(context, eventInput))
            .isInstanceOf(ServiceFailureException.class)
            .hasMessageContaining("metadata entries are allowed, found");
    }

    @Test
    public void whenEventPublishIsCalled_thenItPublishesEventCorrectly() {
        EventInput eventInput = testEventInput();
        EventTransactionContext context = eventTransactionContext(dslContext);

        ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        ArgumentCaptor<DSLContext> contextArgumentCaptor = ArgumentCaptor.forClass(DSLContext.class);

        eventPublishingService.publishEventTransactionally(context, eventInput);

        verify(eventDAO, times(1)).writeEvent(contextArgumentCaptor.capture(), eventArgumentCaptor.capture());

        Assertions.assertThat(contextArgumentCaptor.getValue()).isSameAs(dslContext);

        // now verify the event being passed in
        Event daoInput = eventArgumentCaptor.getValue();
        Assertions.assertThat(daoInput).isNotNull();
        Assertions.assertThat(daoInput.getId()).isNotEmpty();
        Assertions.assertThat(daoInput.getGeneratedTime()).isNotNull();
        Assertions.assertThat(daoInput.getStatus()).isEqualByComparingTo(EventStatus.ENQUEUED);

        // now make sure the EventInput is faithfully echoed
        Assertions.assertThat(daoInput.getTenantId()).isEqualTo(eventInput.getTenantId());
        Assertions.assertThat(daoInput.getType()).isEqualTo(eventInput.getType());
        Assertions.assertThat(daoInput.getPartitionKey().getKey()).isEqualTo(eventInput.getPartitionKey().getKey());
        Assertions.assertThat(daoInput.getMetadata()).isNull();
        ByteBuffer compareTo = ByteBuffer.wrap(TEST_INVOICE_ID.getBytes(StandardCharsets.UTF_8));
        Assertions.assertThat(daoInput.getPayload()).isEqualByComparingTo(compareTo);
    }
}
