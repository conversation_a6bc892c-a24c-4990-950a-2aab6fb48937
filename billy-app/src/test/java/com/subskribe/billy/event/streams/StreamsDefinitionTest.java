package com.subskribe.billy.event.streams;

import java.util.Arrays;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class StreamsDefinitionTest {

    @Test
    public void assertThatRequiredStreamsArePresentInRightConfiguration() {
        Assertions.assertThat(Stream.values()).hasSize(6);
        List<String> expectedLogicalNames = Arrays.stream(Stream.values()).map(stream -> stream.name().replace('_', '-').toLowerCase()).toList();
        List<String> actualLogicalNames = Arrays.stream(Stream.values()).map(Stream::getLogicalStreamName).toList();
        Assertions.assertThat(expectedLogicalNames).isEqualTo(actualLogicalNames);

        Assertions.assertThat(Stream.values()).contains(
            Stream.CATALOG_DOMAIN_STREAM,
            Stream.ORDER_DOMAIN_STREAM,
            Stream.SUBSCRIPTION_DOMAIN_STREAM,
            Stream.BILLING_DOMAIN_STREAM,
            Stream.REVENUE_DOMAIN_STREAM,
            Stream.TASK_DOMAIN_STREAM
        );
    }
}
