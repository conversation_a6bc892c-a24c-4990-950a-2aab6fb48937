package com.subskribe.billy.event.db;

import com.subskribe.billy.event.model.DefaultStreamPartitionKey;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventStatus;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.test.WithDb;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

public class EventingDbTest extends WithDb {

    private static final String TENANT_ID = "27b59431-0cc7-4c87-883b-be345e9bd525";

    private EventDAO eventDAO;

    @BeforeAll
    public void setUp() throws IOException {
        eventDAO = new EventDAO(dslContextProvider);
    }

    @Test
    public void queryingForPublishedEventsWorksAsExpected() {
        Event subscriptionCreated = makeEvent(Instant.now(), EventType.SUBSCRIPTION_CREATED);
        Event invoicePosted = makeEvent(Instant.now(), EventType.INVOICE_POSTED);
        Event invoicePostedV2 = makeEvent(Instant.now(), EventType.INVOICE_POSTED_V2);

        Event invoicePostedPast = makeEvent(Instant.now().minus(3, ChronoUnit.HOURS), EventType.INVOICE_POSTED);
        Event invoicePostedV2Past = makeEvent(Instant.now().minus(3, ChronoUnit.HOURS), EventType.INVOICE_POSTED_V2);

        dslContextProvider
            .get(TENANT_ID)
            .transaction(configuration -> {
                DSLContext dslContext = DSL.using(configuration);
                eventDAO.writeEvent(dslContext, subscriptionCreated);
                eventDAO.writeEvent(dslContext, invoicePosted);
                eventDAO.writeEvent(dslContext, invoicePostedV2);

                eventDAO.writeEvent(dslContext, invoicePostedPast);
                eventDAO.writeEvent(dslContext, invoicePostedV2Past);
            });

        List<Event> eventsInPast = eventDAO.getEventsOfTypeInDurationPast(
            TENANT_ID,
            Set.of(EventType.SUBSCRIPTION_CREATED, EventType.ORDER_SUBMITTED),
            Duration.of(5, ChronoUnit.MINUTES)
        );
        Assertions.assertThat(eventsInPast).hasSize(1);
        Assertions.assertThat(eventsInPast.get(0).getId()).isEqualTo(subscriptionCreated.getId());

        eventsInPast = eventDAO.getEventsOfTypeInDurationPast(
            TENANT_ID,
            Set.of(EventType.INVOICE_POSTED, EventType.INVOICE_POSTED_V2),
            Duration.of(5, ChronoUnit.MINUTES)
        );
        Assertions.assertThat(eventsInPast).hasSize(2);
        Assertions.assertThat(
            eventsInPast.stream().allMatch(event -> event.getId().equals(invoicePosted.getId()) || event.getId().equals(invoicePostedV2.getId()))
        ).isTrue();
    }

    private static Event makeEvent(Instant generateTime, EventType eventType) {
        return Event.builder()
            .id(UUID.randomUUID().toString())
            .tenantId(TENANT_ID)
            .partitionKey(new DefaultStreamPartitionKey("Test"))
            .generatedTime(generateTime)
            .type(eventType)
            .status(EventStatus.ENQUEUED)
            .payload(ByteBuffer.wrap("Test".getBytes(StandardCharsets.UTF_8)))
            .build();
    }
}
