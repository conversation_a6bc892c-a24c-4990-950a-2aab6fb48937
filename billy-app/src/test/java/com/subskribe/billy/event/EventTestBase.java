package com.subskribe.billy.event;

import com.subskribe.billy.event.db.EventDAO;
import com.subskribe.billy.event.model.DefaultStreamPartitionKey;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventInput;
import com.subskribe.billy.event.model.EventStatus;
import com.subskribe.billy.event.model.EventTransactionContext;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.StreamPartitionKey;
import com.subskribe.billy.event.model.TenantAccountPartitionKey;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.jooq.DSLContext;
import org.mockito.Mock;

public class EventTestBase {

    protected static final String TEST_TENANT_ONE = "quark-gluon-plasma";

    protected static final String TEST_TENANT_TWO = "electron-proton-plasma";
    protected static final String TEST_ACCOUNT = "quantum-erasure";

    protected static final String TEST_INVOICE_ID = "INV-A1B2C3D4";

    @Mock
    protected EventDAO eventDAO;

    protected EventTransactionContext eventTransactionContext(DSLContext context) {
        return EventTransactionContext.using(context);
    }

    protected EventInput testEventInput() {
        StreamPartitionKey key = TenantAccountPartitionKey.builder().withTenantId(TEST_TENANT_ONE).withAccountId(TEST_ACCOUNT).build();
        return EventInput.builder()
            .withType(EventType.INVOICE_POSTED)
            .withPartitionKey(key)
            .withTenantId(TEST_TENANT_ONE)
            .withPayload(TEST_INVOICE_ID.getBytes(StandardCharsets.UTF_8))
            .build();
    }

    protected EventInput testBadEventInput() {
        StreamPartitionKey key = TenantAccountPartitionKey.builder().withTenantId(TEST_TENANT_ONE).withAccountId(TEST_ACCOUNT).build();
        Map<String, String> metadata = IntStream.range(0, 50).boxed().collect(Collectors.toMap(String::valueOf, String::valueOf));
        return EventInput.builder()
            .withType(EventType.INVOICE_POSTED)
            .withPartitionKey(key)
            .withTenantId(TEST_TENANT_ONE)
            .withPayload(TEST_INVOICE_ID.getBytes(StandardCharsets.UTF_8))
            .withMetadata(metadata)
            .build();
    }

    protected Event mockEvent(String tenantId) {
        StreamPartitionKey key = new DefaultStreamPartitionKey(String.format("%s/%s", tenantId, TEST_ACCOUNT));
        return Event.builder()
            .id(UUID.randomUUID().toString())
            .tenantId(tenantId)
            .partitionKey(key)
            .generatedTime(Instant.now())
            .type(EventType.INVOICE_POSTED)
            .sequenceNumber(10L)
            .payload(ByteBuffer.wrap(TEST_INVOICE_ID.getBytes(StandardCharsets.UTF_8)))
            .status(EventStatus.ENQUEUED)
            .md5("foo")
            .build();
    }
}
