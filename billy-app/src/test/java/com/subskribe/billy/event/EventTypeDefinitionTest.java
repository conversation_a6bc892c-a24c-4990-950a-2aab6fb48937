package com.subskribe.billy.event;

import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.streams.Stream;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class EventTypeDefinitionTest {

    /*
    If you find yourself making changes to this map, please think carefully:
    - If you are simply adding a new event type, you should be good to go.
    - Renaming, removing or changing the stream of an event type can have a significant impact. In most of those cases,
    you will lose ordering guarantees, which might have unexpected downstream effects
   */
    private static final Map<Stream, Set<EventType>> EXPECTED_EVENT_TYPES_BY_STREAM = Map.of(
        Stream.BILLING_DOMAIN_STREAM,
        Set.of(
            EventType.INVOICE_POSTED,
            EventType.INVOICE_POSTED_V2,
            EventType.PAYMENT_PROCESSED,
            EventType.PAYMENT_ATTEMPT_FAILED,
            EventType.PAYMENT_RETRIES_EXHAUSTED,
            EventType.CREDIT_MEMO_POSTED,
            EventType.PAYMENT_VOIDED,
            EventType.INVOICE_VOIDED,
            EventType.INVOICE_VOIDED_V2,
            EventType.REALIZED_GAIN_LOSS_POSTED
        ),
        Stream.REVENUE_DOMAIN_STREAM,
        Set.of(EventType.REVENUE_RECOGNIZED),
        Stream.ORDER_DOMAIN_STREAM,
        Set.of(
            EventType.ORDER_SUBMITTED,
            EventType.ORDER_EXECUTED,
            EventType.ORDER_APPROVAL_FLOWS_EVALUATED,
            EventType.ESIGNATURE_COMPLETED,
            EventType.ORDER_APPROVED,
            EventType.ORDER_REVERTED_TO_DRAFT,
            EventType.ESIGNATURE_VOIDED
        ),
        Stream.SUBSCRIPTION_DOMAIN_STREAM,
        Set.of(
            EventType.SUBSCRIPTION_CREATED,
            EventType.SUBSCRIPTION_UPDATED,
            EventType.SUBSCRIPTION_ACTIVATING,
            EventType.SUBSCRIPTION_ACTIVATED,
            EventType.SUBSCRIPTION_CANCELLING,
            EventType.SUBSCRIPTION_CANCELLED,
            EventType.SUBSCRIPTION_EXPIRING,
            EventType.SUBSCRIPTION_EXPIRED,
            EventType.SUBSCRIPTION_CHARGE_CHANGE,
            EventType.SUBSCRIPTION_DELETED
        ),
        Stream.TASK_DOMAIN_STREAM,
        Set.of(
            EventType.TASK_SCHEDULED,
            EventType.TASK_COMPLETED,
            EventType.TASK_EXECUTING,
            EventType.SALESFORCE_SYNC_FAILED,
            EventType.HUBSPOT_SYNC_FAILED
        )
    );

    private static final int NUMBER_OF_EVENT_TYPES = EXPECTED_EVENT_TYPES_BY_STREAM.values().stream().mapToInt(Set::size).sum();

    private static final Map<Stream, List<EventType>> ACTUAL_EVENT_TYPES_BY_STREAM = Arrays.stream(EventType.values()).collect(
        Collectors.groupingBy(EventType::getStream)
    );

    @Test
    void assertThatEventTypesAreDefinedAsExpected() {
        Assertions.assertThat(EventType.values()).hasSize(NUMBER_OF_EVENT_TYPES);
        Assertions.assertThat(ACTUAL_EVENT_TYPES_BY_STREAM).hasSize(EXPECTED_EVENT_TYPES_BY_STREAM.size());
        Assertions.assertThat(ACTUAL_EVENT_TYPES_BY_STREAM.keySet()).containsExactlyInAnyOrderElementsOf(EXPECTED_EVENT_TYPES_BY_STREAM.keySet());
        EXPECTED_EVENT_TYPES_BY_STREAM.forEach((key, value) ->
            Assertions.assertThat(ACTUAL_EVENT_TYPES_BY_STREAM.get(key)).containsExactlyInAnyOrderElementsOf(value)
        );
    }
}
