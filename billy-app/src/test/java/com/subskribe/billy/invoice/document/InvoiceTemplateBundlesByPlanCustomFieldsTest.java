package com.subskribe.billy.invoice.document;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import org.junit.jupiter.api.Test;

public class InvoiceTemplateBundlesByPlanCustomFieldsTest {

    Instant startDate = Instant.parse("2020-01-01T00:00:00Z");
    Instant endDate = Instant.parse("2021-01-01T00:00:00Z");
    DocumentRenderFormatter formatter = new DocumentRenderFormatter(TimeZone.getTimeZone("UTC"));
    String currencyCode = "USD";

    @Test
    public void buildLineItemBundlesWithoutType() {
        List<String> options = List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        String typeKey = UUID.randomUUID().toString();

        List<InvoiceTemplateLineItem> lineItems = List.of(
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), options, 10L, BigDecimal.TEN, startDate, endDate),
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), options, 10L, BigDecimal.TEN, startDate, endDate),
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), options, 10L, BigDecimal.TEN, startDate, endDate)
        );

        InvoiceTemplateBundles bundles = InvoiceTemplateBundles.buildItemBundles(
            getInvoiceDocumentJson(lineItems),
            InvoiceTemplateBundleBy.PLAN_CUSTOM_FIELD,
            typeKey,
            lineItems,
            formatter,
            currencyCode
        );

        assertEquals(0, bundles.getBundles().size());
        assertEquals(3, bundles.getUnbundledItems().size());
        assertEquals("$30.00", bundles.getAmount());
    }

    @Test
    public void buildLineItemBundlesWithPlanCustomFieldSingleBundle() {
        List<String> options = List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        String typeKey = UUID.randomUUID().toString();

        List<InvoiceTemplateLineItem> lineItems = List.of(
            getLineItemWithPlanCustomField(typeKey, options.get(0), options, 10L, BigDecimal.TEN, startDate, endDate),
            getLineItemWithPlanCustomField(typeKey, options.get(0), options, 10L, BigDecimal.TEN, startDate, endDate),
            getLineItemWithPlanCustomField(typeKey, options.get(0), options, 10L, BigDecimal.TEN, startDate, endDate)
        );

        InvoiceTemplateBundles bundles = InvoiceTemplateBundles.buildItemBundles(
            getInvoiceDocumentJson(lineItems),
            InvoiceTemplateBundleBy.PLAN_CUSTOM_FIELD,
            typeKey,
            lineItems,
            formatter,
            currencyCode
        );

        assertEquals(1, bundles.getBundles().size());
        assertEquals(3, bundles.getBundles().get(0).getLineItems().size());
        assertEquals(options.get(0), bundles.getBundles().get(0).getBundleName());
        assertEquals(0, bundles.getUnbundledItems().size());
        assertEquals("$30.00", bundles.getAmount());
    }

    @Test
    public void buildLineItemBundlesWithPlanCustomFieldMultipleBundles() {
        List<String> options = List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        String typeKey = UUID.randomUUID().toString();

        List<InvoiceTemplateLineItem> lineItems = List.of(
            getLineItemWithPlanCustomField(typeKey, options.get(0), options, 10L, BigDecimal.TEN, startDate, endDate),
            getLineItemWithPlanCustomField(typeKey, options.get(0), options, 10L, BigDecimal.TEN, startDate, endDate),
            getLineItemWithPlanCustomField(typeKey, options.get(1), options, 10L, BigDecimal.TEN, startDate, endDate)
        );

        InvoiceTemplateBundles bundles = InvoiceTemplateBundles.buildItemBundles(
            getInvoiceDocumentJson(lineItems),
            InvoiceTemplateBundleBy.PLAN_CUSTOM_FIELD,
            typeKey,
            lineItems,
            formatter,
            currencyCode
        );

        assertEquals(2, bundles.getBundles().size());

        assertEquals(2, bundles.getBundles().get(0).getLineItems().size());
        assertEquals(options.get(0), bundles.getBundles().get(0).getBundleName());

        assertEquals(1, bundles.getBundles().get(1).getLineItems().size());
        assertEquals(options.get(1), bundles.getBundles().get(1).getBundleName());

        assertEquals(0, bundles.getUnbundledItems().size());
        assertEquals("$30.00", bundles.getAmount());
    }

    @Test
    public void buildLineItemBundlesWithPlanCustomFieldMultipleBundlesAndUnbundled() {
        List<String> options = List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        String typeKey = UUID.randomUUID().toString();

        List<InvoiceTemplateLineItem> lineItems = List.of(
            getLineItemWithPlanCustomField(typeKey, options.get(0), options, 10L, BigDecimal.TEN, startDate, endDate),
            getLineItemWithPlanCustomField(typeKey, options.get(1), options, 10L, BigDecimal.TEN, startDate, endDate),
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), options, 10L, BigDecimal.TEN, startDate, endDate)
        );

        InvoiceTemplateBundles bundles = InvoiceTemplateBundles.buildItemBundles(
            getInvoiceDocumentJson(lineItems),
            InvoiceTemplateBundleBy.PLAN_CUSTOM_FIELD,
            typeKey,
            lineItems,
            formatter,
            currencyCode
        );

        assertEquals(2, bundles.getBundles().size());

        assertEquals(1, bundles.getBundles().get(0).getLineItems().size());
        assertEquals(options.get(0), bundles.getBundles().get(0).getBundleName());

        assertEquals(1, bundles.getBundles().get(1).getLineItems().size());
        assertEquals(options.get(1), bundles.getBundles().get(1).getBundleName());

        assertEquals(1, bundles.getUnbundledItems().size());
        assertEquals("$30.00", bundles.getAmount());
    }

    private InvoiceDocumentJson getInvoiceDocumentJson(List<InvoiceTemplateLineItem> lineItems) {
        InvoiceDocumentJson json = new InvoiceDocumentJson();
        Map<String, CustomField> planCustomFields = new HashMap<>();
        for (InvoiceTemplateLineItem lineItem : lineItems) {
            planCustomFields.put(lineItem.getPlan().getId(), new CustomField(lineItem.getPlan().getCustomFields()));
        }

        json.setPlanCustomFields(planCustomFields);
        return json;
    }

    private InvoiceTemplateLineItem getLineItemWithPlanCustomField(
        String customFieldName,
        String selection,
        List<String> options,
        Long quantity,
        BigDecimal amount,
        Instant startDate,
        Instant endDate
    ) {
        PlanJson plan = new PlanJson();
        plan.setId(UUID.randomUUID().toString());
        plan.setName(UUID.randomUUID().toString());
        plan.setCustomFields(
            Map.of(
                customFieldName,
                new CustomFieldValue(
                    CustomFieldType.PICKLIST,
                    customFieldName,
                    customFieldName,
                    selection,
                    List.of(selection),
                    options,
                    false,
                    null,
                    null
                )
            )
        );

        InvoiceItem item = getInvoiceItem(quantity, amount, startDate, endDate);

        return new InvoiceTemplateLineItem(item, List.of(item), formatter, currencyCode, null, null, Map.of(), Map.of(), null, plan, null);
    }

    InvoiceItem getInvoiceItem(Long quantity, BigDecimal amount, Instant startDate, Instant endDate) {
        InvoiceItem.InvoiceItemBuilder builder = InvoiceItem.InvoiceItemBuilder.builder();
        builder.quantity(quantity);
        builder.amount(amount);
        builder.periodStartDate(startDate);
        builder.periodEndDate(endDate);
        return builder.createInvoiceItem();
    }
}
