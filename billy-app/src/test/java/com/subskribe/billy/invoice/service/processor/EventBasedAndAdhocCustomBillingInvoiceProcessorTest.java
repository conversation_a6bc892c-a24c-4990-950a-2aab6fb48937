package com.subskribe.billy.invoice.service.processor;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.service.processor.model.InvoiceGenerationInput;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.BillingTerm;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class EventBasedAndAdhocCustomBillingInvoiceProcessorTest {

    @Test
    public void getOrderLinesByEventBased() {
        EventBasedAndAdhocCustomBillingInvoiceProcessor processor = orderLineId -> List.of();

        Charge eventBasedCharge = new Charge();
        eventBasedCharge.setChargeId(UUID.randomUUID().toString());
        eventBasedCharge.setEventBased(true);

        Charge nonEventBasedCharge = new Charge();
        nonEventBasedCharge.setChargeId(UUID.randomUUID().toString());
        nonEventBasedCharge.setEventBased(false);

        Map<String, Charge> chargeMap = Map.of(
            eventBasedCharge.getChargeId(),
            eventBasedCharge,
            nonEventBasedCharge.getChargeId(),
            nonEventBasedCharge
        );

        OrderLineItem eventBasedOrderLine = new OrderLineItem();
        eventBasedOrderLine.setOrderLineId(UUID.randomUUID().toString());
        eventBasedOrderLine.setChargeId(eventBasedCharge.getChargeId());

        OrderLineItem nonEventBasedOrderLine = new OrderLineItem();
        nonEventBasedOrderLine.setOrderLineId(UUID.randomUUID().toString());
        nonEventBasedOrderLine.setChargeId(nonEventBasedCharge.getChargeId());

        List<OrderLineItem> orderLineItems = List.of(eventBasedOrderLine, nonEventBasedOrderLine);

        List<OrderLineItem> eventBasedOrderLines = processor.getEventBasedOrderLines(orderLineItems, chargeMap);
        InvoiceGenerationInput invoiceGenerationInput = mock(InvoiceGenerationInput.class);
        when(invoiceGenerationInput.getCustomBillingInvoicingInput()).thenReturn(null);
        List<OrderLineItem> nonEventBasedOrderLines = processor.getNonEventBasedAndNonAdhocCustomBillingOrderLines(
            orderLineItems,
            chargeMap,
            invoiceGenerationInput
        );

        Assertions.assertEquals(1, eventBasedOrderLines.size());
        Assertions.assertEquals(eventBasedOrderLine.getOrderLineId(), eventBasedOrderLines.get(0).getOrderLineId()); // should contain only order line with event based charge

        Assertions.assertEquals(1, nonEventBasedOrderLines.size());
        Assertions.assertEquals(nonEventBasedOrderLine.getOrderLineId(), nonEventBasedOrderLines.get(0).getOrderLineId()); // should contain only order line without event based charge
    }

    @Test
    public void getUnbilledInvoices() {
        String subscriptionId = UUID.randomUUID().toString();
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setOrderLineId(UUID.randomUUID().toString());

        Instant now = Instant.now();
        Instant targetDate = now.plusSeconds(7200);

        // already billed invoice item with period start date of now()
        InvoiceItem billedInvoiceItem = getInvoiceItem(now);

        // second invoice item with period start date of now() + 1 hour
        InvoiceItem unBilledInvoiceItem = getInvoiceItem(now.plusSeconds(3600));

        EventBasedAndAdhocCustomBillingInvoiceProcessor processor = orderLineId -> List.of(billedInvoiceItem);

        // both items are memoized
        MemoizedInvoiceLineItem billedMemoizedInvoiceLineItem = MemoizedInvoiceLineItem.from(subscriptionId, billedInvoiceItem, BillingTerm.UP_FRONT);
        MemoizedInvoiceLineItem unBilledMemoizedInvoiceLineItem = MemoizedInvoiceLineItem.from(
            subscriptionId,
            unBilledInvoiceItem,
            BillingTerm.UP_FRONT
        );
        var memoizedInvoiceLineItems = List.of(billedMemoizedInvoiceLineItem, unBilledMemoizedInvoiceLineItem);

        // getting unbilled item with target date past the period start date of both invoice items
        List<InvoiceItem> unbilledItemsWithFutureTargetDate = processor.getUnbilledMemoizedLineItems(
            memoizedInvoiceLineItems,
            orderLineItem,
            targetDate
        );

        // get unbilled item with target date before the period start date of unbilled item
        List<InvoiceItem> unbilledItemsWithPastTargetDate = processor.getUnbilledMemoizedLineItems(memoizedInvoiceLineItems, orderLineItem, now);

        Assertions.assertEquals(1, unbilledItemsWithFutureTargetDate.size());
        Assertions.assertEquals(unBilledInvoiceItem.getPeriodStartDate(), unbilledItemsWithFutureTargetDate.get(0).getPeriodStartDate());

        Assertions.assertEquals(0, unbilledItemsWithPastTargetDate.size());
    }

    private InvoiceItem getInvoiceItem(Instant periodStart) {
        String chargeId = UUID.randomUUID().toString();
        String subscriptionChargeId = UUID.randomUUID().toString();
        String subscriptionChargeGroupId = UUID.randomUUID().toString();
        String orderId = UUID.randomUUID().toString();
        String orderItemId = UUID.randomUUID().toString();
        String entityId = UUID.randomUUID().toString();
        return new InvoiceItem.InvoiceItemBuilder()
            .entityId(entityId)
            .periodStartDate(periodStart)
            .planId(UUID.randomUUID().toString())
            .chargeId(chargeId)
            .subscriptionChargeId(subscriptionChargeId)
            .subscriptionChargeGroupId(subscriptionChargeGroupId)
            .orderId(orderId)
            .orderLineItemId(orderItemId)
            .listAmount(BigDecimal.ZERO)
            .amount(BigDecimal.ZERO)
            .discountAmount(BigDecimal.ZERO)
            .quantity(1L)
            .periodEndDate(periodStart.plusSeconds(100000))
            .createInvoiceItem();
    }
}
