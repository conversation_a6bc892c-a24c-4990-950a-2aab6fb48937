package com.subskribe.billy.invoice;

import static com.subskribe.billy.TestConstants.TIME_ZONE;
import static com.subskribe.billy.TestConstants.ZONE_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.upload.ProrationVersion;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Optional;
import java.util.Random;
import java.util.TimeZone;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

public class ProrationCalculatorTest {

    private static final Recurrence DAILY = new Recurrence(Cycle.DAY, 1);
    private static final Recurrence THIRTY_DAYS = new Recurrence(Cycle.DAY, 30);
    private static final Recurrence MONTHLY = new Recurrence(Cycle.MONTH, 1);
    private static final Recurrence QUARTERLY = new Recurrence(Cycle.QUARTER, 1);
    private static final Recurrence SEMI_ANNUAL = new Recurrence(Cycle.SEMI_ANNUAL, 1);
    private static final Recurrence YEARLY = new Recurrence(Cycle.YEAR, 1);
    private static final ProrationConfig NORMALIZED_CONFIG = new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.NORMALIZED);

    private static final ProrationConfig CALENDAR_DAYS_EXACT_DAYS_CONFIG = new ProrationConfig(
        ProrationConfig.Scheme.CALENDAR_DAYS,
        ProrationConfig.Mode.EXACT_DAYS
    );

    private static final ProrationConfig CALENDAR_DAYS_EXACT_CONFIG = new ProrationConfig(
        ProrationConfig.Scheme.CALENDAR_DAYS,
        ProrationConfig.Mode.EXACT
    );

    private static final ZonedDateTime JAN_1_2022 = ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZONE_ID);
    private static final ZonedDateTime JAN_1_2023 = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, ZONE_ID);
    private static final ZonedDateTime JAN_1_2024 = ZonedDateTime.of(2024, 1, 1, 0, 0, 0, 0, ZONE_ID);

    @Test
    public void dayCycleNotCompatibleWithNormalizedProration() {
        var start = JAN_1_2022.toInstant();
        var cycleEnd = JAN_1_2022.plusDays(30).toInstant();
        var end = JAN_1_2022.plusDays(15).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, cycleEnd, cycleEnd, THIRTY_DAYS);
        Period itemPeriod = Period.between(start, end);

        InvariantCheckFailedException ex = assertThrows(InvariantCheckFailedException.class, () ->
            ProrationCalculator.getProrationRatio(billingPeriod, itemPeriod, NORMALIZED_CONFIG, TIME_ZONE, ProrationVersion.V2)
        );

        assertTrue(ex.getMessage().contains("incompatible with normalized proration"));
    }

    @Test
    public void dayCycleProrationOverLongerPeriod() {
        var start = JAN_1_2022.toInstant();
        var cycleEnd = JAN_1_2022.plusDays(30).toInstant();
        var end = JAN_1_2022.plusDays(31).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, cycleEnd, cycleEnd, THIRTY_DAYS);
        Period itemPeriod = Period.between(start, end);

        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_CONFIG, "1.000");
    }

    @Test
    public void dayCycleProrationOverSamePeriod() {
        var start = JAN_1_2022.toInstant();
        var end = JAN_1_2022.plusDays(30).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, end, end, THIRTY_DAYS);
        Period itemPeriod = Period.between(start, end);

        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_CONFIG, "1.000");
    }

    @Test
    public void dayCycleProration() {
        var start = JAN_1_2022.toInstant();
        var cycleEnd = JAN_1_2022.plusDays(30).toInstant();
        var end = JAN_1_2022.plusDays(15).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, cycleEnd, cycleEnd, THIRTY_DAYS);
        Period itemPeriod = Period.between(start, end);

        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_CONFIG, "0.500");
    }

    @Test
    public void dayCycleProrationPartialDay() {
        var start = JAN_1_2022.toInstant();
        var cycleEnd = JAN_1_2022.plusDays(1).toInstant();
        var end = JAN_1_2022.plusSeconds(3600).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, cycleEnd, cycleEnd, DAILY);
        Period itemPeriod = Period.between(start, end);

        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_CONFIG, "0.042");
    }

    @Test
    public void quarterYearPeriodProratesCorrectlyOverFullYear_NORMALIZED() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.plusMonths(3).toInstant());

        // 3 months / 12 months
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, NORMALIZED_CONFIG, "0.2500000000");
    }

    @Test
    public void oneMonthPeriodProratesCorrectlyOverFullYear_CALENDAR_DAYS_EXACT_DAYS() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.plusMonths(1).toInstant());

        // 31 days / 365
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "0.0849315068");
    }

    @Test
    public void oneMonthAnd12HourPeriodProratesCorrectlyOverFullYear_CALENDAR_DAYS_EXACT_DAYS() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.plusMonths(1).plusHours(12).toInstant());
        BigDecimal v1ProrationRatio = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            CALENDAR_DAYS_EXACT_DAYS_CONFIG,
            TIME_ZONE,
            Optional.empty()
        );

        BigDecimal v2ProrationRatio = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            CALENDAR_DAYS_EXACT_DAYS_CONFIG,
            TIME_ZONE,
            ProrationVersion.V2
        );

        // 31 days / 365 = 0.0849315068 (rounded)
        Assertions.assertThat(v1ProrationRatio).isEqualByComparingTo("0.0849315068");

        // (31 days + ~12 hours / 24 hours) / 365 = 0.0863013699 (rounded)
        Assertions.assertThat(v2ProrationRatio).isEqualByComparingTo("0.0863013699");
    }

    @Test
    public void testGetPeriodProrationRatio_UsesV1ForNonCustomBillingCycle() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.plusMonths(1).plusHours(12).toInstant());
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        // billing cycle month
        Optional<Recurrence> billingCycleMonth = Optional.of(new Recurrence(Cycle.MONTH, 1));
        BigDecimal resultMonth = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            CALENDAR_DAYS_EXACT_DAYS_CONFIG,
            timeZone,
            billingCycleMonth
        );
        BigDecimal expectedRatioMonth = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            CALENDAR_DAYS_EXACT_DAYS_CONFIG,
            timeZone,
            ProrationVersion.V1
        );
        Assertions.assertThat(resultMonth).isEqualByComparingTo(expectedRatioMonth);
        // 31 days / 365 = 0.0849315068 (rounded)
        Assertions.assertThat(expectedRatioMonth).isEqualByComparingTo("0.0849315068");

        // billing cycle empty
        Optional<Recurrence> billingCycleEmpty = Optional.empty();
        BigDecimal resultEmpty = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            CALENDAR_DAYS_EXACT_DAYS_CONFIG,
            timeZone,
            billingCycleEmpty
        );
        BigDecimal expectedRatioEmpty = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            CALENDAR_DAYS_EXACT_DAYS_CONFIG,
            timeZone,
            ProrationVersion.V1
        );
        Assertions.assertThat(resultEmpty).isEqualByComparingTo(expectedRatioEmpty);
        // 31 days / 365 = 0.0849315068 (rounded)
        Assertions.assertThat(expectedRatioEmpty).isEqualByComparingTo("0.0849315068");

        // billing cycle custom
        Optional<Recurrence> billingCycleCustom = Optional.of(new Recurrence(Cycle.CUSTOM, 1));
        BigDecimal resultCustom = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            CALENDAR_DAYS_EXACT_DAYS_CONFIG,
            timeZone,
            billingCycleCustom
        );
        BigDecimal expectedRatioCustom = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            CALENDAR_DAYS_EXACT_DAYS_CONFIG,
            timeZone,
            ProrationVersion.V2
        );
        Assertions.assertThat(resultCustom).isEqualByComparingTo(expectedRatioCustom);
        // (31 days + ~12 hours / 24 hours) / 365 = 0.0863013699 (rounded)
        Assertions.assertThat(expectedRatioCustom).isEqualByComparingTo("0.0863013699");
    }

    @Test
    public void oneHundredDaysProrateOverFullYear_CALENDAR_DAYS_EXACT_DAYS() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), ZonedDateTime.of(2022, 4, 11, 0, 0, 0, 0, ZONE_ID).toInstant());

        // 100 days / 365
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "0.2739726027");
    }

    @Test
    public void partialDaysProrateOverFullYear_CALENDAR_DAYS_EXACT_DAYS() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), ZonedDateTime.of(2022, 4, 11, 10, 0, 0, 0, ZONE_ID).toInstant());
        BigDecimal v1ProrationRatio = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            CALENDAR_DAYS_EXACT_DAYS_CONFIG,
            TIME_ZONE,
            Optional.empty()
        );

        BigDecimal v2ProrationRatio = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            CALENDAR_DAYS_EXACT_DAYS_CONFIG,
            TIME_ZONE,
            ProrationVersion.V2
        );

        // 100 days / 365 = 0.2739726027 (rounded)
        Assertions.assertThat(v1ProrationRatio).isEqualByComparingTo("0.2739726027");

        // (100 days + ~10 hours / 24 hours) / 365 = 0.2751141553 (rounded)
        Assertions.assertThat(v2ProrationRatio).isEqualByComparingTo("0.2751141553");
    }

    @Test
    public void variablePeriodProratesCorrectlyOverFullYear() {
        ZonedDateTime JAN_31_2022 = ZonedDateTime.of(2022, 1, 31, 0, 0, 0, 0, ZONE_ID);
        ZonedDateTime JAN_31_2023 = ZonedDateTime.of(2023, 1, 31, 0, 0, 0, 0, ZONE_ID);
        ZonedDateTime MAR_16_2022 = ZonedDateTime.of(2022, 3, 16, 0, 0, 0, 0, ZONE_ID);
        Period prorationPeriod = Period.between(JAN_31_2022.toInstant(), JAN_31_2023.toInstant());
        Period prorateUsingPeriod = Period.between(MAR_16_2022.toInstant(), JAN_31_2023.toInstant());

        // 321 days / 365 = 0.8794520548
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "0.8794520548");
    }

    @Test
    public void oneMonthPeriodProratesCorrectlyOverFullYear() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.plusMonths(1).toInstant());

        // 1 months is 1/12 year
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, NORMALIZED_CONFIG, "0.0833333333");
    }

    @Test
    public void twoMonthsPeriodProratesCorrectlyOverFullYear() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.plusMonths(2).toInstant(), JAN_1_2022.plusMonths(4).toInstant());

        // 2 months is 2/12 = 1/6 of a year
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, NORMALIZED_CONFIG, "0.1666666667");
    }

    @Test
    public void quarterYearPlusHalfMonthPeriodProratesCorrectlyOverFullYear() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.plusMonths(3).plusDays(15).toInstant());

        // 3 months + 15 days of April is 3.5/12
        BigDecimal expected = Numbers.scaledDivide(BigDecimal.valueOf(3.5), BigDecimal.valueOf(12));
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, NORMALIZED_CONFIG, expected.toString());
    }

    @Test
    public void quarterYearPlusOverHalfMonthPeriodProratesCorrectlyOverFullYear() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.plusMonths(3).plusDays(17).toInstant());

        // 3 months + 17 days of April is 3.5666666667/12
        BigDecimal expected = Numbers.scaledDivide(BigDecimal.valueOf(3.5666666667), BigDecimal.valueOf(12));
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, NORMALIZED_CONFIG, expected.toString());
    }

    @Test
    public void quarterYearPlusHalfMonthAndHalfDayPeriodProratesCorrectlyOverFullYear() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.plusMonths(3).plusDays(15).plusSeconds(43200).toInstant());

        // 3 months + 15 days + 43200 seconds (1/2 a day) of April which has 30 days is 3.5166666667/12
        BigDecimal expected = Numbers.scaledDivide(BigDecimal.valueOf(3.5166666667), BigDecimal.valueOf(12));
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, NORMALIZED_CONFIG, expected.toString());
    }

    @Test
    public void oneMonthPeriodProratesCorrectlyOverYearAnd15Days() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.plusDays(15).toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.plusMonths(1).toInstant());

        // 1 month period prorates over 12 months + 0.5 month which is 1/12.5
        BigDecimal expected = Numbers.scaledDivide(BigDecimal.valueOf(1), BigDecimal.valueOf(12.5));
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, NORMALIZED_CONFIG, expected.toString());
    }

    @Test
    public void oneAndHalfMonthPeriodProratesCorrectlyOverYearAnd15Days() {
        Period prorationPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2023.plusDays(15).toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.plusMonths(1).plusDays(15).toInstant());

        // 1 month and 15 days period prorates over 12 months + 0.5 month which is 1.5/12.5 (15 days is 1/2 a month)
        BigDecimal expected = Numbers.scaledDivide(BigDecimal.valueOf(1.5), BigDecimal.valueOf(12.5));
        assertProrationRatios(prorationPeriod, prorateUsingPeriod, NORMALIZED_CONFIG, expected.toString());
    }

    @Test
    public void singleMonthInSemiAnnualBillingProration() {
        var start = JAN_1_2022.toInstant();
        var periodEnd = JAN_1_2022.plusYears(1).toInstant();
        var itemEnd = JAN_1_2022.plusMonths(1).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, SEMI_ANNUAL);
        Period itemPeriod = Period.between(start, itemEnd);

        BigDecimal prorationRatio = ProrationCalculator.getProrationRatio(
            billingPeriod,
            itemPeriod,
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.NORMALIZED),
            TIME_ZONE
        );

        // 1 month out of 6
        assertEquals(new BigDecimal("0.167"), prorationRatio.setScale(3, RoundingMode.HALF_UP));
    }

    @Test
    public void singleMonthInQuarterlyBillingProration_NORMALIZED() {
        var start = JAN_1_2022.toInstant();
        var periodEnd = JAN_1_2022.plusMonths(3).toInstant();
        var itemEnd = JAN_1_2022.plusMonths(1).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, QUARTERLY);
        Period itemPeriod = Period.between(start, itemEnd);

        // 1 month out of 3
        assertProrationRatios(
            billingPeriod,
            itemPeriod,
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.NORMALIZED),
            "0.333"
        );
    }

    @Test
    public void singleMonthInQuarterlyBillingProration_EXACT_DAYS() {
        var start = JAN_1_2022.toInstant();
        var periodEnd = JAN_1_2022.plusMonths(3).toInstant();
        var itemEnd = JAN_1_2022.plusMonths(1).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, QUARTERLY);
        Period itemPeriod = Period.between(start, itemEnd);

        // 31 days out of 90 days
        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "0.344");
    }

    @Test
    public void exact6MonthsProration() {
        var start = JAN_1_2022.toInstant();
        var periodEnd = JAN_1_2022.plusYears(1).toInstant();
        var itemEnd = JAN_1_2022.plusMonths(6).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, YEARLY);
        Period itemPeriod = Period.between(start, itemEnd);

        // 181 / 365
        assertProrationRatios(billingPeriod, itemPeriod, new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT), "0.496");
    }

    @Test
    public void normalized6MonthsProration() {
        var start = JAN_1_2022.toInstant();
        var periodEnd = JAN_1_2022.plusYears(1).toInstant();
        var itemEnd = JAN_1_2022.plusMonths(6).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, YEARLY);
        Period itemPeriod = Period.between(start, itemEnd);

        // 6 months / 12 months
        assertProrationRatios(
            billingPeriod,
            itemPeriod,
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.NORMALIZED),
            "0.500"
        );
    }

    @Test
    public void normalized6MonthsAnd15DaysProration() {
        var start = JAN_1_2022.toInstant();
        var periodEnd = JAN_1_2022.plusYears(1).toInstant();
        var itemEnd = JAN_1_2022.plusMonths(6).plusDays(15).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, YEARLY);
        Period itemPeriod = Period.between(start, itemEnd);

        // 6.5 months / 12 months
        assertProrationRatios(
            billingPeriod,
            itemPeriod,
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.NORMALIZED),
            "0.542"
        );
    }

    @Test
    public void normalized6MonthsAnd15DaysAnd12HoursProration() {
        var start = JAN_1_2022.toInstant();
        var periodEnd = JAN_1_2022.plusYears(1).toInstant();
        var itemEnd = JAN_1_2022.plusMonths(6).plusDays(15).plusHours(12).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, YEARLY);
        Period itemPeriod = Period.between(start, itemEnd);

        // 6.5 months / 12 months + ~12 hours / 8760 hours
        assertProrationRatios(
            billingPeriod,
            itemPeriod,
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.NORMALIZED),
            "0.543"
        );
    }

    @Test
    public void billingPeriodDurationSameAsOrderLineItemPeriodDuration_CALENDAR_DAYS_EXACT() {
        var start = JAN_1_2022.toInstant();
        var end = JAN_1_2022.plusDays(31).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, end, end, MONTHLY);
        Period itemPeriod = Period.between(start, end);

        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_CONFIG, "1.000");
    }

    @Test
    public void billingPeriodDurationSameAsOrderLineItemPeriodDuration_CALENDAR_DAYS_EXACT_DAYS() {
        var start = JAN_1_2022.toInstant();
        var end = JAN_1_2022.plusDays(31).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, end, end, MONTHLY);
        Period itemPeriod = Period.between(start, end);

        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "1.000");
    }

    @Test
    public void orderLineItemPeriodMoreThanFixedDays_EXACT() {
        var start = JAN_1_2022.toInstant();
        var end = JAN_1_2022.plusDays(31).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, end, end, MONTHLY);
        Period itemPeriod = Period.between(start, end);

        assertProrationRatios(billingPeriod, itemPeriod, new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT), "1.000");
    }

    @Test
    public void orderLineItemDurationHalfOfFixedDaysMonthly_EXACT() {
        var start = JAN_1_2022.toInstant();
        var periodEnd = JAN_1_2022.plusDays(31).toInstant();
        var itemEnd = JAN_1_2022.plusDays(15).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, MONTHLY);
        Period itemPeriod = Period.between(start, itemEnd);

        assertProrationRatios(billingPeriod, itemPeriod, new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT), "0.500");
    }

    @Test
    public void orderLineItemDurationRatioOfLongMonth_EXACT() {
        var start = JAN_1_2022.toInstant();
        var periodEnd = JAN_1_2022.plusMonths(1).toInstant(); // Jan has 31 days
        var itemEnd = JAN_1_2022.plusDays(15).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, MONTHLY);
        Period itemPeriod = Period.between(start, itemEnd);

        // 15 / 31
        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_CONFIG, "0.484");
    }

    @Test
    public void orderLineItemDurationRatioOfFeb_EXACT() {
        var start = ZonedDateTime.of(2022, 2, 1, 0, 0, 0, 0, ZONE_ID);
        var periodEnd = start.plusMonths(1); // Feb has 28 days
        var itemEnd = start.plusDays(15);

        BillingPeriod billingPeriod = new BillingPeriod(start.toInstant(), periodEnd.toInstant(), periodEnd.toInstant(), MONTHLY);
        Period itemPeriod = Period.between(start.toInstant(), itemEnd.toInstant());

        // 15 / 28
        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_CONFIG, "0.536");
    }

    @Test
    public void orderLineItemDurationRatioOfLongMonth_EXACT_DAYS() {
        var start = JAN_1_2022.toInstant();
        var periodEnd = JAN_1_2022.plusMonths(1).toInstant(); // Jan has 31 days
        var itemEnd = JAN_1_2022.plusDays(15).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, MONTHLY);
        Period itemPeriod = Period.between(start, itemEnd);

        // 15 / 31
        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "0.484");
    }

    @Test
    public void orderLineItemDurationRatioOfYear_CALENDAR_DAYS_EXACT() {
        var start = JAN_1_2024.toInstant();
        var periodEnd = JAN_1_2024.plusYears(1).toInstant(); // 2024 is a leap year with 366 days
        var itemEnd = JAN_1_2024.plusDays(100).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, YEARLY);
        Period itemPeriod = Period.between(start, itemEnd);

        // 100 / 366
        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_CONFIG, "0.273");
    }

    @Test
    public void twoHundredDaysRatioOfYear_CALENDAR_DAYS_EXACT_DAYS() {
        var start = JAN_1_2024.toInstant();
        var periodEnd = JAN_1_2024.plusYears(1).toInstant(); // 2024 is a leap year with 366 days
        var itemEnd = JAN_1_2024.plusDays(200).toInstant();

        BillingPeriod billingPeriod = new BillingPeriod(start, periodEnd, periodEnd, YEARLY);
        Period itemPeriod = Period.between(start, itemEnd);

        // 200 / 366
        assertProrationRatios(billingPeriod, itemPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "0.546");
    }

    @Test
    public void testProrationWithInstantProrateOnPeriod() {
        Period prorateOnPeriod = Period.between(JAN_1_2023.toInstant(), JAN_1_2023.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2024.toInstant());

        assertProrationRatios(prorateOnPeriod, prorateUsingPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "1");
    }

    @Test
    public void testProrationWithInstantProrateOnPeriod_StartDateInstant() {
        Period prorateOnPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2022.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2024.toInstant());

        assertProrationRatios(prorateOnPeriod, prorateUsingPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "1");
    }

    @Test
    public void testProrationWithInstantProrateOnPeriod_EndDateInstant() {
        Period prorateOnPeriod = Period.between(JAN_1_2024.toInstant(), JAN_1_2024.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2024.toInstant());

        assertProrationRatios(prorateOnPeriod, prorateUsingPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "0");
    }

    @Test
    public void testProrationWithInstantProrateUsingPeriod() {
        Period prorateOnPeriod = Period.between(JAN_1_2022.toInstant(), JAN_1_2024.toInstant());
        Period prorateUsingPeriod = Period.between(JAN_1_2023.toInstant(), JAN_1_2023.toInstant());

        assertProrationRatios(prorateOnPeriod, prorateUsingPeriod, CALENDAR_DAYS_EXACT_DAYS_CONFIG, "0");
    }

    @Test
    public void calculateServicePeriodFromAmountExactStartOfPeriod() {
        Period orderLinePeriod = Period.between(JAN_1_2023.toInstant(), JAN_1_2024.toInstant());
        Instant start = JAN_1_2023.toInstant();
        BigDecimal totalAmount = new BigDecimal("100");
        BigDecimal serviceAmount = new BigDecimal("50");

        assertReverseProrationFromServiceAmount(orderLinePeriod, start, serviceAmount, totalAmount, CALENDAR_DAYS_EXACT_CONFIG);
        assertReverseProrationFromServiceAmount(orderLinePeriod, start, serviceAmount, totalAmount, CALENDAR_DAYS_EXACT_DAYS_CONFIG);
        assertReverseProrationFromServiceAmount(orderLinePeriod, start, serviceAmount, totalAmount, NORMALIZED_CONFIG);
    }

    @Test
    public void calculateServicePeriodFromAmountExactMiddleOfPeriod() {
        Period orderLinePeriod = Period.between(JAN_1_2023.toInstant(), JAN_1_2024.toInstant());
        Instant start = JAN_1_2023.plusMonths(3).toInstant();
        BigDecimal totalAmount = new BigDecimal("100");
        BigDecimal serviceAmount = new BigDecimal("50");

        assertReverseProrationFromServiceAmount(orderLinePeriod, start, serviceAmount, totalAmount, CALENDAR_DAYS_EXACT_CONFIG);
        assertReverseProrationFromServiceAmount(orderLinePeriod, start, serviceAmount, totalAmount, CALENDAR_DAYS_EXACT_DAYS_CONFIG);
        assertReverseProrationFromServiceAmount(orderLinePeriod, start, serviceAmount, totalAmount, NORMALIZED_CONFIG);
    }

    @Test
    public void calculateServicePeriodFullMonths() {
        Period denominator = Period.between(JAN_1_2023.toInstant(), JAN_1_2024.toInstant());
        Instant start = JAN_1_2023.toInstant();
        Instant servicePeriodEnd = JAN_1_2023.plusMonths(3).toInstant();
        Period numerator = Period.between(start, servicePeriodEnd);
        BigDecimal totalAmount = new BigDecimal("100");

        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_DAYS_CONFIG);
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_CONFIG);
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, NORMALIZED_CONFIG);
    }

    @Test
    public void calculateServicePeriodWithFractionalDay() {
        Period denominator = Period.between(JAN_1_2023.toInstant(), JAN_1_2024.toInstant());
        Instant start = JAN_1_2023.toInstant();
        Instant servicePeriodEnd = JAN_1_2023.plusMonths(3).plusHours(12).toInstant();
        Period numerator = Period.between(start, servicePeriodEnd);
        BigDecimal totalAmount = new BigDecimal("100");

        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_DAYS_CONFIG);
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_CONFIG);
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, NORMALIZED_CONFIG);
    }

    @Test
    public void calculateServicePeriodWithFractionalMonthAndDay() {
        Period denominator = Period.between(JAN_1_2023.toInstant(), JAN_1_2024.toInstant());
        Instant start = JAN_1_2023.toInstant();
        Instant servicePeriodEnd = JAN_1_2023.plusMonths(3).plusDays(10).plusHours(12).toInstant();
        Period numerator = Period.between(start, servicePeriodEnd);
        BigDecimal totalAmount = new BigDecimal("100");

        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_DAYS_CONFIG);
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_CONFIG);
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, NORMALIZED_CONFIG);
    }

    @Test
    public void calculateServicePeriodWithFractionalMonthShiftedStart() {
        Period denominator = Period.between(JAN_1_2023.toInstant(), JAN_1_2024.toInstant());
        Instant start = JAN_1_2023.plusMonths(2).toInstant();
        Instant servicePeriodEnd = JAN_1_2023.plusMonths(4).plusDays(15).toInstant();
        Period numerator = Period.between(start, servicePeriodEnd);
        BigDecimal totalAmount = new BigDecimal("100");

        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_DAYS_CONFIG);
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_CONFIG);
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, NORMALIZED_CONFIG);
    }

    @Test
    public void calculateServicePeriodWithFractionalMonthAndDayShiftedStart() {
        Period denominator = Period.between(JAN_1_2023.toInstant(), JAN_1_2024.toInstant());
        Instant start = JAN_1_2023.plusMonths(2).plusDays(5).plusHours(3).toInstant();
        Instant servicePeriodEnd = JAN_1_2023.plusMonths(4).plusDays(15).plusHours(12).toInstant();
        Period numerator = Period.between(start, servicePeriodEnd);
        BigDecimal totalAmount = new BigDecimal("100");

        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_DAYS_CONFIG);
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_CONFIG);
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, NORMALIZED_CONFIG);
    }

    @Test
    @Disabled("Disabled for now since there are random failures. Need more investigation.")
    public void calculateServicePeriodRandomized() {
        Period denominator = Period.between(JAN_1_2023.toInstant(), JAN_1_2024.toInstant());
        BigDecimal totalAmount = new BigDecimal("100");
        Random random = new Random();

        for (int i = 0; i < 10; i++) {
            int incrementStartMonthsBy = random.nextInt(7);
            int incrementEndMonthBy = incrementStartMonthsBy + random.nextInt(6);

            int incrementStartDayBy = random.nextInt(15);
            int incrementEndDayBy = incrementStartDayBy + random.nextInt(14);

            int incrementStartHourBy = random.nextInt(13);
            int incrementEndHourBy = incrementStartHourBy + random.nextInt(12);

            Instant start = JAN_1_2023.plusMonths(incrementStartMonthsBy).plusDays(incrementStartDayBy).plusHours(incrementStartHourBy).toInstant();
            Instant servicePeriodEnd = JAN_1_2023.plusMonths(incrementEndMonthBy)
                .plusDays(incrementEndDayBy)
                .plusHours(incrementEndHourBy)
                .toInstant();
            Period numerator = Period.between(start, servicePeriodEnd);

            String message = String.format("start: %s, end: %s", start, servicePeriodEnd);

            assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_DAYS_CONFIG, message);
            assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, CALENDAR_DAYS_EXACT_CONFIG, message);
            assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, NORMALIZED_CONFIG, message);
        }
    }

    private void assertReverseProrationFromServiceAmount(
        Period denominator,
        Instant serviceStart,
        BigDecimal serviceAmount,
        BigDecimal totalAmount,
        ProrationConfig prorationConfig
    ) {
        Period calculatedServicePeriod = ProrationCalculator.calculateServicePeriod(
            denominator,
            serviceStart,
            serviceAmount,
            totalAmount,
            prorationConfig,
            TIME_ZONE
        );

        BigDecimal prorationRatio = ProrationCalculator.getPeriodProrationRatio(
            denominator,
            calculatedServicePeriod,
            prorationConfig,
            TIME_ZONE,
            ProrationVersion.V2
        );
        BigDecimal proratedAmount = prorationRatio.multiply(totalAmount);

        assertEquals(serviceAmount.setScale(10, RoundingMode.HALF_UP), proratedAmount.setScale(10, RoundingMode.HALF_UP));
    }

    private void assertReverseProrationFromServicePeriod(
        Period denominator,
        Period numerator,
        BigDecimal totalAmount,
        ProrationConfig prorationConfig
    ) {
        assertReverseProrationFromServicePeriod(denominator, numerator, totalAmount, prorationConfig, null);
    }

    private void assertReverseProrationFromServicePeriod(
        Period denominator,
        Period numerator,
        BigDecimal totalAmount,
        ProrationConfig prorationConfig,
        String message
    ) {
        BigDecimal prorationRatio = ProrationCalculator.getPeriodProrationRatio(
            denominator,
            numerator,
            prorationConfig,
            TIME_ZONE,
            ProrationVersion.V2
        );

        BigDecimal serviceAmount = totalAmount.multiply(prorationRatio);

        Period calculatedServicePeriod = ProrationCalculator.calculateServicePeriod(
            denominator,
            numerator.getStart(),
            serviceAmount,
            totalAmount,
            prorationConfig,
            TIME_ZONE
        );

        if (StringUtils.isEmpty(message)) {
            assertEquals(numerator.getEnd(), calculatedServicePeriod.getEnd());
        } else {
            assertEquals(numerator.getEnd(), calculatedServicePeriod.getEnd(), message);
        }
    }

    private void assertProrationRatios(Period prorationPeriod, Period prorateUsingPeriod, ProrationConfig prorationConfig, String expectedRatio) {
        BigDecimal v1ProrationRatio = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            prorationConfig,
            TIME_ZONE,
            ProrationVersion.V1
        );

        BigDecimal v2ProrationRatio = ProrationCalculator.getPeriodProrationRatio(
            prorationPeriod,
            prorateUsingPeriod,
            prorationConfig,
            TIME_ZONE,
            ProrationVersion.V2
        );

        Assertions.assertThat(v1ProrationRatio).isEqualByComparingTo(v2ProrationRatio);
        Assertions.assertThat(v1ProrationRatio).isEqualByComparingTo(expectedRatio);
    }

    private void assertProrationRatios(
        BillingPeriod billingPeriod,
        Period orderLineItemPeriod,
        ProrationConfig prorationConfig,
        String expectedRatio
    ) {
        BigDecimal v1ProrationRatio = ProrationCalculator.getProrationRatio(
            billingPeriod,
            orderLineItemPeriod,
            prorationConfig,
            TIME_ZONE,
            ProrationVersion.V1
        );

        BigDecimal v2ProrationRatio = ProrationCalculator.getProrationRatio(
            billingPeriod,
            orderLineItemPeriod,
            prorationConfig,
            TIME_ZONE,
            ProrationVersion.V2
        );

        Assertions.assertThat(v1ProrationRatio.setScale(3, RoundingMode.HALF_UP)).isEqualByComparingTo(
            v2ProrationRatio.setScale(3, RoundingMode.HALF_UP)
        );
        Assertions.assertThat(v1ProrationRatio.setScale(3, RoundingMode.HALF_UP)).isEqualByComparingTo(expectedRatio);
    }
}
