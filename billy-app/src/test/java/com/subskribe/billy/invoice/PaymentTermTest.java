package com.subskribe.billy.invoice;

import com.subskribe.billy.invoice.model.PaymentTerm;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class PaymentTermTest {

    @Test
    public void paymentTermMustStartWithNet() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> new PaymentTerm(" NET30"));
        Assertions.assertThrows(IllegalArgumentException.class, () -> new PaymentTerm("Not30"));
        Assertions.assertThrows(IllegalArgumentException.class, () -> new PaymentTerm("30"));
    }

    @Test
    public void paymentTermMustContainNumber() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> new PaymentTerm("NET"));
        Assertions.assertThrows(IllegalArgumentException.class, () -> new PaymentTerm("NETzero"));
        Assertions.assertThrows(IllegalArgumentException.class, () -> new PaymentTerm("NET9three"));
    }

    @Test
    public void paymentTermCannotBeNegative() {
        Assertions.assertDoesNotThrow(() -> new PaymentTerm("NET0"));
        Assertions.assertThrows(IllegalArgumentException.class, () -> new PaymentTerm("NET-1"));
        Assertions.assertThrows(IllegalArgumentException.class, () -> new PaymentTerm("NET-20"));
    }

    @Test
    public void paymentTermCannotBeGreaterThanMax() {
        Assertions.assertDoesNotThrow(() -> new PaymentTerm("NET365"));
        Assertions.assertThrows(IllegalArgumentException.class, () -> new PaymentTerm("NET366"));
        Assertions.assertThrows(IllegalArgumentException.class, () -> new PaymentTerm("NET1000"));
    }

    @Test
    public void paymentTermStartsWithNetIsSuccessful() {
        Assertions.assertDoesNotThrow(() -> new PaymentTerm("NET30"));
        Assertions.assertDoesNotThrow(() -> new PaymentTerm("Net30"));
        Assertions.assertDoesNotThrow(() -> new PaymentTerm("net30"));
        Assertions.assertDoesNotThrow(() -> new PaymentTerm("Net 30"));
    }

    @Test
    public void paymentTermGettersAreCorrect() {
        PaymentTerm net0 = new PaymentTerm("NET0");
        Assertions.assertEquals(0, net0.getPaymentDueInDays());
        Assertions.assertEquals("Net 0", net0.getDisplayName());
        Assertions.assertEquals("NET0", net0.name());
        Assertions.assertEquals("NET0", net0.toString());
        Assertions.assertEquals(PaymentTerm.NET0, net0);

        PaymentTerm net30 = new PaymentTerm("NET30");
        Assertions.assertEquals(30, net30.getPaymentDueInDays());
        Assertions.assertEquals("Net 30", net30.getDisplayName());
        Assertions.assertEquals("NET30", net30.name());
        Assertions.assertEquals("NET30", net30.toString());
        Assertions.assertEquals(PaymentTerm.NET30, net30);

        PaymentTerm net69 = new PaymentTerm("NET69");
        Assertions.assertEquals(69, net69.getPaymentDueInDays());
        Assertions.assertEquals("Net 69", net69.getDisplayName());
        Assertions.assertEquals("NET69", net69.name());
        Assertions.assertEquals("NET69", net69.toString());
        Assertions.assertEquals(new PaymentTerm("Net 69"), net69);

        PaymentTerm net145 = new PaymentTerm("NET145");
        Assertions.assertEquals(145, net145.getPaymentDueInDays());
        Assertions.assertEquals("Net 145", net145.getDisplayName());
        Assertions.assertEquals("NET145", net145.name());
        Assertions.assertEquals("NET145", net145.toString());
        Assertions.assertEquals(new PaymentTerm("Net 145"), net145);
    }

    @Test
    public void fromDueInDaysWorksAsExpected() {
        org.assertj.core.api.Assertions.assertThat(PaymentTerm.fromDueInDays(-1)).isEmpty();
        org.assertj.core.api.Assertions.assertThat(PaymentTerm.fromDueInDays(400)).isEmpty();
        org.assertj.core.api.Assertions.assertThat(PaymentTerm.fromDueInDays(60)).isPresent();
        PaymentTerm net45 = PaymentTerm.fromDueInDays(45).orElseThrow();
        org.assertj.core.api.Assertions.assertThat(net45).isEqualTo(new PaymentTerm("NET45"));
    }
}
