package com.subskribe.billy.invoice.document;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.invoice.model.InvoiceItem;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class InvoiceTemplateItemSorterTest {

    @Test
    void sortLineItemsByOrderLineOrder() {
        Map<String, Integer> sortRank = Map.of("3", 1, "2", 2, "1", 3);
        List<InvoiceTemplateLineItem> lineItems = new ArrayList<>();
        lineItems.add(createLineItem("1"));
        lineItems.add(createLineItem("2"));
        lineItems.add(createLineItem("3"));

        List<InvoiceTemplateLineItem> sortedLineItems = InvoiceTemplateItemSorter.sortLineItems(sortRank, lineItems);
        assertEquals("3", sortedLineItems.get(0).getLineItem().getOrderLineItemId());
        assertEquals("2", sortedLineItems.get(1).getLineItem().getOrderLineItemId());
        assertEquals("1", sortedLineItems.get(2).getLineItem().getOrderLineItemId());
    }

    @Test
    void sortLineItemsByOrderLineOrderWithSameRank() {
        Map<String, Integer> sortRank = Map.of("3", 1, "2", 2, "1", 2);
        List<InvoiceTemplateLineItem> lineItems = new ArrayList<>();
        lineItems.add(createLineItem("1"));
        lineItems.add(createLineItem("2"));
        lineItems.add(createLineItem("3"));

        List<InvoiceTemplateLineItem> sortedLineItems = InvoiceTemplateItemSorter.sortLineItems(sortRank, lineItems);
        assertEquals("3", sortedLineItems.get(0).getLineItem().getOrderLineItemId());
        // preserves existing order
        assertEquals("1", sortedLineItems.get(1).getLineItem().getOrderLineItemId());
        assertEquals("2", sortedLineItems.get(2).getLineItem().getOrderLineItemId());
    }

    private InvoiceTemplateLineItem createLineItem(String orderLineItemId) {
        InvoiceItem.InvoiceItemBuilder builder = InvoiceItem.InvoiceItemBuilder.builder();
        builder.orderLineItemId(orderLineItemId);

        return new InvoiceTemplateLineItem(builder.createInvoiceItem(), null, null, null, null, null, null, null, null, null, null);
    }
}
