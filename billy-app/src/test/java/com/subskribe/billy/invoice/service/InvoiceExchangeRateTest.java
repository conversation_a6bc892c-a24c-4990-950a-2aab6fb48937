package com.subskribe.billy.invoice.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.fixture.AccountFixture;
import com.subskribe.billy.account.fixture.AccountGetServiceFixture;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.foreignexchange.fixture.ExchangeRateServicesFixture;
import com.subskribe.billy.foreignexchange.fixture.TransactionalExchangeRateGetServiceFixture;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateService;
import com.subskribe.billy.invoice.db.InvoiceConfigDAO;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceChargeInclusionOption;
import com.subskribe.billy.invoice.model.InvoiceGenerationMethod;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.model.TenantInvoiceConfig;
import com.subskribe.billy.invoice.number.InvoiceNumberGenerator;
import com.subskribe.billy.invoice.number.RandomNumberGenerator;
import com.subskribe.billy.invoice.number.SequenceInvoiceNumberGenerator;
import com.subskribe.billy.invoice.service.processor.OneTimeInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PercentOfInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PrepaidInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.RecurringInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.UsageInvoiceProcessor;
import com.subskribe.billy.invoice.tax.model.TaxCalculationInput;
import com.subskribe.billy.invoice.tax.model.TaxTransaction;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderSource;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.order.services.OrderCustomBillingService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.payment.processor.service.PaymentProcessorJobQueueService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.revrec.services.RevenueRecognitionJobService;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.contactfetcher.ContactGroupOrUserFetcher;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.pecuniary.TenantDiscount;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.model.SubscriptionState;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionImpl;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantIdProviderFixture;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.test.utilities.ChargeUtils;
import com.subskribe.billy.test.utilities.MockChargeBuilder;
import com.subskribe.billy.usage.service.UsageStatisticsService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Month;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Currency;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class InvoiceExchangeRateTest {

    private final OrderGetService mockOrderGetService = mock(OrderGetService.class);

    private final ProductCatalogGetService mockProductCatalogGetService = mock(ProductCatalogGetService.class);

    private final SubscriptionGetService mockSubscriptionGetService = mock(SubscriptionGetService.class);

    private final AccountGetService mockAccountGetService = AccountGetServiceFixture.getDefault();

    private final InvoiceDAO mockInvoiceDao = mock(InvoiceDAO.class);

    private final InvoiceConfigDAO mockInvoiceConfigDAO = mock(InvoiceConfigDAO.class);

    private final TenantIdProvider mockTenantIdProvider = TenantIdProviderFixture.getDefault();

    private final DSLContextProvider mockDslContextProvider = mock(DSLContextProvider.class);

    private final UsageStatisticsService mockUsageStatisticsService = mock(UsageStatisticsService.class);

    private final InvoiceDocumentGenerationTrackerService mockDocumentGenerationTrackerService = mock(InvoiceDocumentGenerationTrackerService.class);

    private final TaxRateGetService mockTaxRateGetService = mock(TaxRateGetService.class);

    private final TaxService mockTaxService = mock(TaxService.class);

    private final CacheService mockCacheService = mock(CacheService.class);

    private final InvoiceEventService invoiceEventService = mock(InvoiceEventService.class);

    public final EmailContactListService emailContactListService = mock(EmailContactListService.class);

    public final ContactGroupOrUserFetcher contactGroupOrUserFetcher = mock(ContactGroupOrUserFetcher.class);

    public final RateCardService rateCardService = mock(RateCardService.class);

    public final FeatureService featureService = FeatureServiceFixture.allEnabled();

    private final EntityGetService mockEntityGetService = EntityGetServiceFixture.entityGetServiceFixture();

    private final TransactionalExchangeRateService transactionalExchangeRateService = mock(TransactionalExchangeRateService.class);

    private final OrderCustomBillingService mockOrderCustomBillingService = mock(OrderCustomBillingService.class);

    public final ReportingJobQueueService reportingJobQueueService = mock(ReportingJobQueueService.class);

    private final CurrencyConversionRateGetService mockCurrencyConversionRateGetService = mock(CurrencyConversionRateGetService.class);

    private final CustomFieldService mockCustomFieldService = mock(CustomFieldService.class);

    private final DiscountCalculator discountCalculator = new DiscountCalculator(featureService);

    protected UsageInvoiceProcessor usageInvoiceProcessor;

    private static final String SUBSCRIPTION_ID = UUID.randomUUID().toString();

    private static final String EXTERNAL_ID = UUID.randomUUID().toString();

    private static final String TEST_PLAN_ID = "test_plan_id";

    private static final String TEST_PRODUCT_ID = "PROD-BALROG";

    private static final String TEST_PRODUCT_SKU = "SKU-SARUMAN";

    private static final String CHARGE_ID = "CHRG-1234567";

    private static final UUID PLAN_ID = UUID.randomUUID();

    private static final String ORDER_ID = UUID.randomUUID().toString();

    private static final TenantId TENANT_ID = new TenantId("test_tenant_id");

    private static final String ENTITY_ID = "test_entity_id";

    private static final int BILLING_CYCLES = 12;

    private static final long QUANTITY = 10;

    private static final BigDecimal UNIT_PRICE = BigDecimal.valueOf(15.35);

    private static final LocalDate JAN_1_2020 = LocalDate.of(2020, Month.JANUARY, 1);

    private static final Instant START_DATE = getInstant(JAN_1_2020);

    private static final Instant END_DATE = getInstant(JAN_1_2020.plusMonths(BILLING_CYCLES));

    private static final Recurrence TERM_LENGTH = new Recurrence(Cycle.MONTH, BILLING_CYCLES);

    protected static final TimeZone TIME_ZONE = TimeZone.getTimeZone("UTC");

    private final TenantSettingService tenantSettingService = TenantSettingServiceFixture.withTimeZone(TIME_ZONE.getID());

    private static final String ORDER_LINE_ITEM_ID = UUID.randomUUID().toString();

    private static final Currency TEST_CURRENCY = Currency.getInstance(Locale.US);

    private static final BigDecimal EUR_USD_EXCHANGE_RATE = BigDecimal.valueOf(1.123);

    private static final Optional<InvoiceGenerationMethod> invoiceGenerationMethod = Optional.empty();

    private final TaxTransaction mockTaxTransaction = TaxTransaction.builder()
        .transactionCode("dummy")
        .computedTaxByLineNo(Map.of())
        .totalTax(BigDecimal.ZERO)
        .build();

    private final InvoiceConfigurationService invoiceConfigurationService = new InvoiceConfigurationService(
        mockInvoiceDao,
        mockInvoiceConfigDAO,
        mockCacheService
    );

    private final ProrationConfigurationGetService prorationConfigurationGetService = mock(ProrationConfigurationGetService.class);

    private InvoiceServiceInternal invoiceService;

    @BeforeEach
    void beforeEach() {
        // Return the same invoice sent to the DAO
        when(mockInvoiceDao.addInvoice(any())).then(i -> i.getArgument(0));
        when(emailContactListService.getEmailAccountContactIdsForSubscription(any())).thenReturn(new ArrayList<>());

        //NOTE: no tax testing is happening here, so we can return a dummy TaxTransaction
        when(mockTaxService.createTaxTransaction(any(TaxCalculationInput.class))).thenReturn(Optional.of(mockTaxTransaction));

        // setup for tax line item
        setupMockProductGet();
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, getRecurrence(Cycle.MONTH))
        );
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(START_DATE, END_DATE));
        when(mockSubscriptionGetService.getSubscriptionChargeBySubscriptionChargeId(anyString())).thenReturn(getTestSubscriptionCharge());

        when(prorationConfigurationGetService.resolveProrationConfig(any(Subscription.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT)
        );
        when(prorationConfigurationGetService.resolveProrationConfig(any(Order.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT)
        );
        setMockInvoiceConfig();

        PercentOfChargeInvoiceHelper mockPercentOfChargeHelper = mock(PercentOfChargeInvoiceHelper.class);
        PercentOfInvoiceProcessor mockPercentOfProcessor = mock(PercentOfInvoiceProcessor.class);

        AccountingPeriodService mockAccountingPeriodService = mock(AccountingPeriodService.class);
        when(mockAccountingPeriodService.getCurrentAccountingPeriod()).thenReturn(Optional.empty());

        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(
            Map.of(CHARGE_ID, new MockChargeBuilder().withChargeId(CHARGE_ID).withPlanId(PLAN_ID).build())
        );

        Plan plan = new Plan();
        plan.setId(PLAN_ID);
        when(mockProductCatalogGetService.getPlansByIds(any())).thenReturn(List.of(plan));

        var subscriptionBillingPeriodService = new SubscriptionBillingPeriodService(
            mockSubscriptionGetService,
            tenantSettingService,
            featureService,
            prorationConfigurationGetService
        );

        PaymentProcessorJobQueueService mockPaymentProcessorJobQueueService = mock(PaymentProcessorJobQueueService.class);
        RevenueRecognitionJobService mockRevenueRecognitionJobService = mock(RevenueRecognitionJobService.class);

        InvoiceAmountCalculator invoiceAmountCalculator = new InvoiceAmountCalculator(
            rateCardService,
            mockCurrencyConversionRateGetService,
            discountCalculator,
            featureService
        );
        RatingService ratingService = new RatingService(invoiceAmountCalculator, rateCardService);

        RecurringInvoiceProcessor recurringInvoiceProcessor = new RecurringInvoiceProcessor(
            invoiceAmountCalculator,
            subscriptionBillingPeriodService,
            featureService,
            mockInvoiceDao
        );

        OneTimeInvoiceProcessor oneTimeInvoiceProcessor = new OneTimeInvoiceProcessor(
            mockSubscriptionGetService,
            mockInvoiceDao,
            invoiceAmountCalculator,
            featureService
        );

        PrepaidInvoiceProcessor prepaidInvoiceProcessor = new PrepaidInvoiceProcessor(
            mockInvoiceDao,
            invoiceAmountCalculator,
            subscriptionBillingPeriodService
        );

        usageInvoiceProcessor = new UsageInvoiceProcessor(
            subscriptionBillingPeriodService,
            invoiceAmountCalculator,
            featureService,
            recurringInvoiceProcessor,
            prepaidInvoiceProcessor,
            mockInvoiceDao,
            mockUsageStatisticsService,
            ratingService,
            mockCurrencyConversionRateGetService
        );

        // set up exchange rate service
        ExchangeRateServicesFixture.mockExchangeRate(transactionalExchangeRateService, "EUR", "USD", EUR_USD_EXCHANGE_RATE);

        invoiceService = new InvoiceServiceInternal(
            mockOrderGetService,
            mockSubscriptionGetService,
            mockAccountGetService,
            mockProductCatalogGetService,
            mockTenantIdProvider,
            mockInvoiceDao,
            new InvoiceNumberGenerator(
                mockInvoiceDao,
                invoiceConfigurationService,
                mockEntityGetService,
                new RandomNumberGenerator(),
                new SequenceInvoiceNumberGenerator(mockInvoiceDao)
            ),
            mockDslContextProvider,
            mockTaxService,
            mockTaxRateGetService,
            mockDocumentGenerationTrackerService,
            tenantSettingService,
            mockPercentOfChargeHelper,
            mockPercentOfProcessor,
            mockPaymentProcessorJobQueueService,
            invoiceEventService,
            subscriptionBillingPeriodService,
            prorationConfigurationGetService,
            mockRevenueRecognitionJobService,
            mockAccountingPeriodService,
            emailContactListService,
            contactGroupOrUserFetcher,
            featureService,
            recurringInvoiceProcessor,
            oneTimeInvoiceProcessor,
            prepaidInvoiceProcessor,
            usageInvoiceProcessor,
            reportingJobQueueService,
            Clock.systemUTC(),
            transactionalExchangeRateService,
            mockOrderCustomBillingService,
            mockEntityGetService,
            mockCustomFieldService
        );
    }

    // when functional currency is same as transaction currency,
    // the functional amounts should be the same as transaction amounts
    // entity currency is USD, set subscription currency to USD
    @Test
    public void sameFunctionalAndTransactionCurrencies() {
        var start = getInstant(JAN_1_2020);
        var end = getInstant(JAN_1_2020.plusMonths(18));
        var billingCycle = getRecurrence(Cycle.PAID_IN_FULL);
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(start, end, billingCycle, "USD"));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(getTestOrders(start, end, billingCycle));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.MONTH))))
        );

        // monthly charge for 18 months should be 18 * monthly price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(18)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                start,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        Assertions.assertThat(invoice.getSubTotal()).isEqualTo(expectedAmount);
        Assertions.assertThat(invoice.getFunctionalSubTotal()).isEqualTo(expectedAmount);
        Assertions.assertThat(invoice.getFunctionalTotal()).isEqualTo(expectedAmount);
        Assertions.assertThat(invoice.getFunctionalTotalDiscount()).isZero();
        Assertions.assertThat(invoice.getFunctionalTaxTotal()).isZero();
        Assertions.assertThat(invoice.getExchangeRateId()).isNull();
        Assertions.assertThat(invoice.getExchangeRate()).isNull();
        Assertions.assertThat(invoice.getExchangeRateDate()).isNull();

        Assertions.assertThat(invoice.getInvoiceItems()).hasSize(1);
        var invoiceItem = invoice.getInvoiceItems().get(0);
        Assertions.assertThat(invoiceItem.getFunctionalAmount()).isEqualTo(expectedAmount);
        Assertions.assertThat(invoiceItem.getFunctionalListAmount()).isEqualTo(expectedAmount);
        Assertions.assertThat(invoiceItem.getFunctionalDiscountAmount()).isZero();
        Assertions.assertThat(invoiceItem.getFunctionalTaxAmount()).isZero();
    }

    // different currencies, but no exchange rate available
    // when exchange rate is not available, the invoice creation should fail
    // entity currency is USD, set subscription currency to INR
    // there is no exchange rate available for INR to USD
    @Test
    public void noExchangeRateAvailable() {
        var start = getInstant(JAN_1_2020);
        var end = getInstant(JAN_1_2020.plusMonths(18));
        var billingCycle = getRecurrence(Cycle.PAID_IN_FULL);
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(start, end, billingCycle, "INR"));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(getTestOrders(start, end, billingCycle));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.MONTH))))
        );

        Assertions.assertThatThrownBy(() ->
            invoiceService.generateInvoice(
                SUBSCRIPTION_ID,
                start,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
        )
            .isInstanceOf(ConflictingStateException.class)
            .hasMessage("Exchange rate not found for invoice date");
    }

    // different functional and transactional currencies
    // use the available exchange rate to convert the transactional currency to functional currency
    // entity currency is USD, set subscription currency to EUR
    @Test
    public void differentFunctionalAndTransactionCurrencies() {
        var start = getInstant(JAN_1_2020);
        var end = getInstant(JAN_1_2020.plusMonths(18));
        var billingCycle = getRecurrence(Cycle.PAID_IN_FULL);
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(start, end, billingCycle, "EUR"));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(getTestOrders(start, end, billingCycle));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.MONTH))))
        );

        // monthly charge for 18 months should be 18 * monthly price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(18)));
        var expectedFunctionalAmount = roundToTwoPlaces(expectedAmount.multiply(EUR_USD_EXCHANGE_RATE));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                start,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        Assertions.assertThat(invoice.getSubTotal()).isEqualTo(expectedAmount);
        Assertions.assertThat(invoice.getFunctionalSubTotal()).isEqualTo(expectedFunctionalAmount);
        Assertions.assertThat(invoice.getFunctionalTotal()).isEqualTo(expectedFunctionalAmount);
        Assertions.assertThat(invoice.getFunctionalTotalDiscount()).isZero();
        Assertions.assertThat(invoice.getFunctionalTaxTotal()).isZero();
        Assertions.assertThat(invoice.getExchangeRateId()).isNotBlank();
        Assertions.assertThat(invoice.getExchangeRate()).isEqualTo(EUR_USD_EXCHANGE_RATE);
        Assertions.assertThat(invoice.getExchangeRateDate()).isEqualTo(
            Instant.ofEpochSecond(TransactionalExchangeRateGetServiceFixture.DEFAULT_EFFECTIVE_DATE)
        );

        Assertions.assertThat(invoice.getInvoiceItems()).hasSize(1);
        var invoiceItem = invoice.getInvoiceItems().get(0);
        Assertions.assertThat(invoiceItem.getFunctionalAmount()).isEqualTo(expectedFunctionalAmount);
        Assertions.assertThat(invoiceItem.getFunctionalListAmount()).isEqualTo(expectedFunctionalAmount);
        Assertions.assertThat(invoiceItem.getFunctionalDiscountAmount()).isZero();
        Assertions.assertThat(invoiceItem.getFunctionalTaxAmount()).isZero();
    }

    private void setMockInvoiceConfig() {
        TenantInvoiceConfig tenantInvoiceConfig = new TenantInvoiceConfig("INV", "SEQUENCE", 1L, 4);
        when(mockCacheService.get(any(), eq(CacheType.INVOICE_CONFIG_BY_ID), any(), any(), eq(TenantInvoiceConfig.class))).thenReturn(
            tenantInvoiceConfig
        );
    }

    private static BigDecimal expectedOnePeriodPerUnitSubTotal(BigDecimal prorateAmount) {
        return BigDecimal.valueOf(QUANTITY).multiply(UNIT_PRICE).multiply(prorateAmount);
    }

    private static Subscription getTestSubscription(Instant startDate, Instant endDate, Recurrence billingCycle, String currency) {
        var subscriptionEntity = new SubscriptionEntity(
            UUID.randomUUID(),
            SUBSCRIPTION_ID,
            EXTERNAL_ID,
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            AccountFixture.ACCOUNT_1_ID,
            null,
            AccountFixture.CONTACT_1_ID,
            AccountFixture.CONTACT_1_ID,
            Currency.getInstance(currency),
            PaymentTerm.NET30,
            SubscriptionState.ACTIVE,
            startDate,
            endDate,
            startDate,
            null,
            TERM_LENGTH,
            billingCycle,
            BillingTerm.UP_FRONT,
            null,
            null,
            List.of(ORDER_ID),
            null,
            false,
            false,
            startDate,
            Instant.now(),
            1,
            START_DATE,
            END_DATE,
            SubscriptionDurationModel.TERMED
        );
        return new SubscriptionImpl(subscriptionEntity, List.of(getTestSubscriptionCharge()));
    }

    private static Subscription getTestSubscription(Instant startDate, Instant endDate) {
        return getTestSubscription(startDate, endDate, getRecurrence(Cycle.MONTH), "USD");
    }

    private static SubscriptionCharge getTestSubscriptionCharge() {
        return new SubscriptionCharge(
            UUID.randomUUID(),
            CHARGE_ID,
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            SUBSCRIPTION_ID,
            TENANT_ID.getRequiredId(),
            AccountFixture.ACCOUNT_1_ID,
            UUID.randomUUID(),
            List.of(ORDER_LINE_ITEM_ID),
            START_DATE,
            END_DATE,
            1,
            false,
            null,
            null,
            null,
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ZERO,
            null,
            null,
            null,
            1,
            START_DATE,
            END_DATE,
            0,
            null
        );
    }

    private static Charge getRecurringPerUnitCharge(Recurrence recurrence) {
        return new MockChargeBuilder().withChargeId(CHARGE_ID).withAmount(UNIT_PRICE).recurring(recurrence).build();
    }

    private static List<Order> getTestOrders(Instant startDate, Instant endDate, Recurrence recurrence) {
        return getTestOrders(startDate, endDate, OrderStatus.SUBMITTED, recurrence, Collections.emptyMap());
    }

    private static List<Order> getTestOrders(
        Instant startDate,
        Instant endDate,
        OrderStatus status,
        Recurrence recurrence,
        Map<String, BigDecimal> tenantDiscounts
    ) {
        var orderId = UUID.randomUUID().toString();
        OrderLineItem orderLineItem = getOrderLineItem(orderId, startDate, endDate, tenantDiscounts);
        Order order = getOrderDetails(orderId, List.of(orderLineItem), startDate, endDate, status, recurrence, tenantDiscounts);
        return List.of(order);
    }

    private static Order getOrderDetails(
        String orderId,
        List<OrderLineItem> orderLineItems,
        Instant startDate,
        Instant endDate,
        OrderStatus status,
        Recurrence recurrence,
        Map<String, BigDecimal> tenantDiscountsMap
    ) {
        List<TenantDiscount> tenantDiscounts = getTenantDiscounts(tenantDiscountsMap);

        return new Order(
            AutoGenerate.getNewUuid(),
            null,
            orderId,
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            null,
            AccountFixture.ACCOUNT_1_ID,
            null,
            OrderType.NEW,
            TEST_CURRENCY,
            PaymentTerm.NET30,
            null,
            SUBSCRIPTION_ID,
            AccountFixture.CONTACT_1_ID,
            AccountFixture.CONTACT_1_ID,
            tenantDiscounts,
            orderLineItems,
            orderLineItems,
            startDate,
            endDate,
            START_DATE,
            TERM_LENGTH,
            recurrence,
            BillingTerm.UP_FRONT,
            BigDecimal.valueOf(150),
            BigDecimal.valueOf(150),
            null,
            BigDecimal.valueOf(150),
            status,
            null,
            Instant.now(),
            Instant.now(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            0,
            null,
            0,
            null,
            false,
            false,
            null,
            null,
            null,
            true,
            null,
            null,
            OrderSource.USER,
            OrderStartDateType.FIXED,
            null,
            Optional.empty(),
            SubscriptionDurationModel.TERMED,
            null
        );
    }

    private static OrderLineItem getOrderLineItem(String orderId, Instant startDate, Instant endDate, Map<String, BigDecimal> tenantDiscountsMap) {
        List<TenantDiscountLineItem> tenantDiscounts = getTenantDiscountLineItems(tenantDiscountsMap);
        var totalDiscount = DiscountCalculator.calculateDiscountAmounts(UNIT_PRICE, List.of(), tenantDiscounts, QUANTITY, true);

        return new OrderLineItem(
            AutoGenerate.getNewUuid(),
            ORDER_LINE_ITEM_ID,
            null,
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            orderId,
            1,
            false,
            ActionType.ADD,
            TEST_PLAN_ID,
            UUID.randomUUID().toString(),
            null,
            null,
            null,
            CHARGE_ID,
            null,
            null,
            QUANTITY,
            UNIT_PRICE.multiply(BigDecimal.valueOf(2)),
            UNIT_PRICE,
            UNIT_PRICE.multiply(BigDecimal.valueOf(QUANTITY)),
            UNIT_PRICE.multiply(BigDecimal.valueOf(2 * QUANTITY)),
            UNIT_PRICE.multiply(BigDecimal.valueOf(2 * QUANTITY)),
            totalDiscount.getTotalDiscountAmount(),
            null,
            tenantDiscounts,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            startDate,
            endDate,
            List.of(),
            null,
            null,
            Instant.now(),
            Instant.now()
        );
    }

    private static List<TenantDiscount> getTenantDiscounts(Map<String, BigDecimal> discounts) {
        List<TenantDiscount> tenantDiscounts = new ArrayList<>();

        for (var entry : discounts.entrySet()) {
            var discount = new TenantDiscount();
            discount.setId(entry.getKey());
            discount.setPercent(entry.getValue());
            tenantDiscounts.add(discount);
        }
        return tenantDiscounts;
    }

    private static List<TenantDiscountLineItem> getTenantDiscountLineItems(Map<String, BigDecimal> discounts) {
        List<TenantDiscountLineItem> tenantDiscounts = new ArrayList<>();

        for (var entry : discounts.entrySet()) {
            var discount = new TenantDiscountLineItem();
            discount.setId(entry.getKey());
            discount.setPercent(entry.getValue());
            tenantDiscounts.add(discount);
        }
        return tenantDiscounts;
    }

    private static Instant getInstant(LocalDate date) {
        return date.atStartOfDay(InvoiceServiceInternal.DEFAULT_TIME_ZONE).toInstant();
    }

    private static BigDecimal roundToTwoPlaces(BigDecimal value) {
        return value.setScale(2, RoundingMode.HALF_EVEN);
    }

    private void setupMockProductGet() {
        Plan plan = new Plan();
        plan.setProductId(TEST_PRODUCT_ID);
        plan.setCurrency(TEST_CURRENCY);
        when(mockProductCatalogGetService.getPlansFromChargeIds(ArgumentMatchers.anyList())).thenReturn(List.of(plan));
        var name = "test";
        Product product = new Product(
            UUID.randomUUID(),
            EntityFixture.ALL_ENTITY_IDS,
            TEST_PRODUCT_ID,
            true,
            name,
            name,
            "test",
            TEST_PRODUCT_SKU,
            null,
            Instant.now(),
            null
        );
        when(mockProductCatalogGetService.getProduct(TEST_PRODUCT_ID)).thenReturn(product);
    }

    private static Recurrence getRecurrence(Cycle cycle) {
        return new Recurrence(cycle, 1);
    }
}
