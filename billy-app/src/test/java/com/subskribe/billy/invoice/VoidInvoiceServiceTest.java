package com.subskribe.billy.invoice;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceDocumentGenerationTrackerService;
import com.subskribe.billy.invoice.service.InvoiceEventService;
import com.subskribe.billy.invoice.service.VoidInvoiceService;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.invoicesettlement.model.InvoiceBalance;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationStatus;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationType;
import com.subskribe.billy.invoicesettlement.services.InvoiceSettlementService;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class VoidInvoiceServiceTest {

    private static final TenantId TENANT_ID = new TenantId("Test_Tenant_Id");
    private final TenantIdProvider mockTenantIdProvider = mock(TenantIdProvider.class);
    private final DSLContextProvider mockDslContextProvider = mock(DSLContextProvider.class);
    private final InvoiceSettlementService mockInvoiceSettlementService = mock(InvoiceSettlementService.class);
    private final PaymentGetService mockPaymentGetService = mock(PaymentGetService.class);
    private final InvoiceDAO mockInvoiceDao = mock(InvoiceDAO.class);
    private final FeatureService mockFeatureService = mock(FeatureService.class);
    private final InvoiceDocumentGenerationTrackerService mockDocumentGenerationTrackerService = mock(InvoiceDocumentGenerationTrackerService.class);
    private final TaxService taxService = mock(TaxService.class);
    private final InvoiceEventService mockInvoiceEventService = mock(InvoiceEventService.class);
    private final ProductCatalogGetService mockProductCatalogGetService = mock(ProductCatalogGetService.class);
    private final RevenueRecognitionGetService mockRevenueRecognitionGetService = mock(RevenueRecognitionGetService.class);
    private final AccountingPeriodService mockAccountingPeriodService = mock(AccountingPeriodService.class);
    private final ReportingJobQueueService mockReportingJobQueueService = mock(ReportingJobQueueService.class);

    private VoidInvoiceService voidInvoiceService;
    private static final Invoice.Number INVOICE_NUMBER = new Invoice.Number("INV-0001");
    private static final String CURRENCY_CODE = "USD";
    private static final BigDecimal INVOICE_AMOUNT = BigDecimal.TEN;

    @BeforeEach
    void beforeEach() {
        when(mockTenantIdProvider.provide()).thenReturn(TENANT_ID);
        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID.getRequiredId());
        when(mockAccountingPeriodService.inClosedAccountingPeriod(any(), any())).thenReturn(false);

        when(mockFeatureService.isEnabled(Feature.VOID_INVOICE)).thenReturn(true);
        when(mockInvoiceDao.getInvoicesBySubscription(any(), any(), any())).thenReturn(null);
        when(mockInvoiceSettlementService.getSettlementApplications(INVOICE_NUMBER)).thenReturn(null);

        InvoiceBalance invoiceBalance = createInvoiceBalance(INVOICE_NUMBER, INVOICE_AMOUNT);
        when(mockInvoiceSettlementService.getAndCompareInvoiceBalance(INVOICE_NUMBER, INVOICE_AMOUNT)).thenReturn(invoiceBalance);
        when(mockInvoiceSettlementService.updateInvoiceBalance(any(), any(), any(), any(Instant.class))).thenReturn(invoiceBalance);

        Invoice invoice = createInvoice(INVOICE_NUMBER, InvoiceStatus.POSTED, INVOICE_AMOUNT);
        when(mockInvoiceDao.getInvoiceByInvoiceNumber(INVOICE_NUMBER)).thenReturn(invoice);
        when(mockInvoiceDao.voidInvoice(any(), any(), any(), any(), any())).thenReturn(invoice);
        when(mockInvoiceDao.getInvoiceCountForSubscriptionAfterSpecificDate(any(), any(), any())).thenReturn(List.of());

        voidInvoiceService = new VoidInvoiceService(
            mockTenantIdProvider,
            mockDslContextProvider,
            mockInvoiceSettlementService,
            mockPaymentGetService,
            mockInvoiceDao,
            mockFeatureService,
            mockDocumentGenerationTrackerService,
            taxService,
            mockProductCatalogGetService,
            mockAccountingPeriodService,
            mockRevenueRecognitionGetService,
            mockReportingJobQueueService,
            mockInvoiceEventService
        );
    }

    @Test
    public void verifyThrowsExceptionIfFeatureFlagIsTurnedOff() {
        when(mockFeatureService.isEnabled(Feature.VOID_INVOICE)).thenReturn(false);
        assertThrows(UnsupportedOperationException.class, () -> voidInvoiceService.voidInvoice(INVOICE_NUMBER, BigDecimal.ZERO, Instant.now()));
    }

    @Test
    public void verifyVoidInvoiceThrowsExceptionIfInvoiceNumberIsNull() {
        assertThrows(IllegalArgumentException.class, () -> voidInvoiceService.voidInvoice(null, BigDecimal.ZERO, Instant.now()));
    }

    @Test
    public void verifyVoidInvoiceThrowsExceptionIfInvoiceBalanceIsNull() {
        assertThrows(IllegalArgumentException.class, () -> voidInvoiceService.voidInvoice(INVOICE_NUMBER, null, Instant.now()));
    }

    @Test
    public void verifyVoidInvoiceThrowsExceptionIfVoidInvoiceDateIsNull() {
        assertThrows(IllegalArgumentException.class, () -> voidInvoiceService.voidInvoice(INVOICE_NUMBER, BigDecimal.ZERO, null));
    }

    @Test
    public void verifyVoidInvoiceThrowsIfInvoiceIsNotPosted() {
        Invoice invoice = createInvoice(INVOICE_NUMBER, InvoiceStatus.PAID, INVOICE_AMOUNT);
        when(mockInvoiceDao.getInvoiceByInvoiceNumber(INVOICE_NUMBER)).thenReturn(invoice);

        assertThrows(IllegalStateException.class, () -> voidInvoiceService.voidInvoice(INVOICE_NUMBER, BigDecimal.ZERO, Instant.now()));
    }

    @Test
    public void verifyVoidInvoiceThrowsIfVoidInvoiceDateIsBeforeInvoiceDate() {
        assertThrows(InvalidInputException.class, () ->
            voidInvoiceService.voidInvoice(INVOICE_NUMBER, BigDecimal.ZERO, Instant.now().minus(2, ChronoUnit.SECONDS))
        );
    }

    @Test
    public void verifyVoidInvoiceThrowsIfVoidDateIsInClosedAccountingPeriod() {
        when(mockAccountingPeriodService.inClosedAccountingPeriod(any(), any())).thenReturn(true);
        assertThrows(InvalidInputException.class, () -> voidInvoiceService.voidInvoice(INVOICE_NUMBER, BigDecimal.ZERO, Instant.now()));
    }

    @Test
    public void verifyVoidInvoiceThrowsIfSubscriptionHasNewInvoicesAfterTheCurrentOne() {
        createInvoice(new Invoice.Number(UUID.randomUUID().toString()), InvoiceStatus.DRAFT, INVOICE_AMOUNT);
        when(mockInvoiceDao.getInvoiceCountForSubscriptionAfterSpecificDate(any(), any(), any())).thenReturn(List.of("INV-002"));

        assertThrows(IllegalStateException.class, () -> voidInvoiceService.voidInvoice(INVOICE_NUMBER, BigDecimal.ZERO, Instant.now()));
    }

    @Test
    public void verifyVoidInvoiceThrowsIfInvoiceHasCreditMemosApplied() {
        SettlementApplication settlementApplication = createSettlementApplication(
            INVOICE_NUMBER,
            null,
            "CREDIT_MEMO_NUMBER",
            BigDecimal.TEN,
            SettlementApplicationType.CREDIT,
            Instant.now(),
            SettlementApplicationStatus.APPLIED_PAYMENT
        );
        when(mockInvoiceSettlementService.getSettlementApplications(INVOICE_NUMBER)).thenReturn(List.of(settlementApplication));
        when(mockInvoiceSettlementService.hasAppliedCredit(INVOICE_NUMBER)).thenReturn(true);

        assertThrows(IllegalStateException.class, () -> voidInvoiceService.voidInvoice(INVOICE_NUMBER, BigDecimal.ZERO, Instant.now()));
    }

    @Test
    public void verifyVoidInvoiceThrowsIfInvoiceHasNonVoidedPaymentApplied() {
        String paymentId = UUID.randomUUID().toString();
        SettlementApplication settlementApplication = createSettlementApplication(
            INVOICE_NUMBER,
            paymentId,
            null,
            BigDecimal.TEN,
            SettlementApplicationType.PAYMENT,
            Instant.now(),
            SettlementApplicationStatus.APPLIED_PAYMENT
        );
        when(mockInvoiceSettlementService.getSettlementApplications(INVOICE_NUMBER)).thenReturn(List.of(settlementApplication));

        InvoiceBalance invoiceBalance = createInvoiceBalance(INVOICE_NUMBER, INVOICE_AMOUNT);
        when(mockInvoiceSettlementService.getAndCompareInvoiceBalance(any(), any())).thenReturn(invoiceBalance);

        Payment payment = new Payment();
        payment.setPaymentId(paymentId);
        when(mockPaymentGetService.getPaymentsByPaymentIds(List.of(paymentId))).thenReturn(List.of(payment));

        assertThrows(IllegalStateException.class, () -> voidInvoiceService.voidInvoice(INVOICE_NUMBER, BigDecimal.ZERO, Instant.now()));
    }

    @Test
    public void verifyVoidInvoiceThrowsIfInvoiceAmountIsDifferent() {
        String paymentId = UUID.randomUUID().toString();
        SettlementApplication settlementApplication = createSettlementApplication(
            INVOICE_NUMBER,
            paymentId,
            null,
            BigDecimal.TEN,
            SettlementApplicationType.PAYMENT,
            Instant.now(),
            SettlementApplicationStatus.APPLIED_PAYMENT
        );
        when(mockInvoiceSettlementService.getSettlementApplications(INVOICE_NUMBER)).thenReturn(List.of(settlementApplication));

        InvoiceBalance invoiceBalance = createInvoiceBalance(INVOICE_NUMBER, INVOICE_AMOUNT);
        when(mockInvoiceSettlementService.getAndCompareInvoiceBalance(any(), any())).thenReturn(invoiceBalance);

        Payment payment = new Payment();
        payment.setPaymentId(paymentId);
        when(mockPaymentGetService.getPaymentsByPaymentIds(List.of(paymentId))).thenReturn(List.of(payment));

        assertThrows(IllegalStateException.class, () -> voidInvoiceService.voidInvoice(INVOICE_NUMBER, BigDecimal.valueOf(5), Instant.now()));
    }

    private InvoiceBalance createInvoiceBalance(Invoice.Number invoiceNumber, BigDecimal amount) {
        return new InvoiceBalance(UUID.randomUUID(), null, invoiceNumber.getNumber(), amount, CURRENCY_CODE, Instant.now());
    }

    private SettlementApplication createSettlementApplication(
        Invoice.Number invoiceNumber,
        String paymentId,
        String creditMemoNumber,
        BigDecimal amount,
        SettlementApplicationType applicationType,
        Instant appliedOn,
        SettlementApplicationStatus settlementApplicationStatus
    ) {
        return SettlementApplication.builder()
            .id(UUID.randomUUID())
            .entityId(EntityFixture.ENTITY_1_ID)
            .invoiceNumber(invoiceNumber.getNumber())
            .paymentId(paymentId)
            .creditMemoNumber(creditMemoNumber)
            .applicationType(applicationType)
            .amount(amount)
            .appliedOn(appliedOn)
            .createdOn(appliedOn)
            .status(settlementApplicationStatus)
            .build();
    }

    private static Invoice createInvoice(Invoice.Number invoiceNumber, InvoiceStatus invoiceStatus, BigDecimal total) {
        Invoice.InvoiceBuilder invoiceBuilder = new Invoice.InvoiceBuilder();
        invoiceBuilder.invoiceNumber(invoiceNumber);
        invoiceBuilder.status(invoiceStatus);
        invoiceBuilder.total(total);
        invoiceBuilder.currency(CURRENCY_CODE);
        invoiceBuilder.invoiceDate(Instant.now());
        invoiceBuilder.createdOn(Instant.now());
        invoiceBuilder.entityId(EntityFixture.ENTITY_1_ID);
        return invoiceBuilder.createInvoice();
    }
}
