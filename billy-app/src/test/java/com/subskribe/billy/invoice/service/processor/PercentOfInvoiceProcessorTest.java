package com.subskribe.billy.invoice.service.processor;

import static com.subskribe.billy.invoice.service.processor.PercentOfInvoiceProcessor.RemainderDistributionMethod.DISTRIBUTE_TO_FIRST_ITEM;
import static com.subskribe.billy.invoice.service.processor.PercentOfInvoiceProcessor.RemainderDistributionMethod.DISTRIBUTE_TO_GREATEST_DIFFERENCE;
import static com.subskribe.billy.invoice.service.processor.PercentOfInvoiceProcessor.RemainderDistributionMethod.DISTRIBUTE_TO_LAST_ITEM;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.fixture.InvoiceItemFixture;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.service.PercentOfChargeInvoiceHelper;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class PercentOfInvoiceProcessorTest {

    private PercentOfInvoiceProcessor processor;
    private FeatureService featureService;

    @BeforeEach
    void setUp() {
        InvoiceDAO invoiceDAO = mock(InvoiceDAO.class);
        RecurringInvoiceProcessor recurringInvoiceProcessor = mock(RecurringInvoiceProcessor.class);
        PercentOfChargeInvoiceHelper percentOfChargeHelper = mock(PercentOfChargeInvoiceHelper.class);
        SubscriptionBillingPeriodService subscriptionBillingPeriodService = mock(SubscriptionBillingPeriodService.class);
        featureService = mock(FeatureService.class);

        processor = new PercentOfInvoiceProcessor(
            invoiceDAO,
            recurringInvoiceProcessor,
            percentOfChargeHelper,
            subscriptionBillingPeriodService,
            featureService
        );
    }

    @Test
    void testDistributeSellAmount_PositiveRemainder_DistributeToLastItem() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItems(new BigDecimal("100.00"), new BigDecimal("200.00"), new BigDecimal("150.00"));

        distributeSellAmount(items, new BigDecimal("450.01"), DISTRIBUTE_TO_LAST_ITEM);

        assertEquals(new BigDecimal("100.00"), items.get(0).getAmount());
        assertEquals(new BigDecimal("200.00"), items.get(1).getAmount());
        assertEquals(new BigDecimal("150.01"), items.get(2).getAmount());
    }

    @Test
    void testDistributeSellAmount_NegativeRemainder_DistributeToLastItem() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItems(new BigDecimal("100.00"), new BigDecimal("200.00"), new BigDecimal("150.00"));

        distributeSellAmount(items, new BigDecimal("449.99"), DISTRIBUTE_TO_LAST_ITEM);

        assertEquals(new BigDecimal("100.00"), items.get(0).getAmount());
        assertEquals(new BigDecimal("200.00"), items.get(1).getAmount());
        assertEquals(new BigDecimal("149.99"), items.get(2).getAmount());
    }

    @Test
    void testDistributeSellAmount_PositiveRemainder_DistributeToFirstItem() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItems(new BigDecimal("100.00"), new BigDecimal("200.00"), new BigDecimal("150.00"));

        distributeSellAmount(items, new BigDecimal("450.05"), DISTRIBUTE_TO_FIRST_ITEM);

        assertEquals(new BigDecimal("100.05"), items.get(0).getAmount());
        assertEquals(new BigDecimal("200.00"), items.get(1).getAmount());
        assertEquals(new BigDecimal("150.00"), items.get(2).getAmount());
    }

    @Test
    void testDistributeSellAmount_PositiveRemainder_DistributeToGreatestDifference() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItems(new BigDecimal("100.00"), new BigDecimal("300.00"), new BigDecimal("150.00"));

        distributeSellAmount(items, new BigDecimal("550.02"), DISTRIBUTE_TO_GREATEST_DIFFERENCE);

        assertEquals(new BigDecimal("100.00"), items.get(0).getAmount());
        assertEquals(new BigDecimal("300.02"), items.get(1).getAmount());
        assertEquals(new BigDecimal("150.00"), items.get(2).getAmount());
    }

    @Test
    void testDistributeSellAmount_ZeroRemainder_NoChange() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItems(new BigDecimal("100.00"), new BigDecimal("200.00"), new BigDecimal("150.00"));

        distributeSellAmount(items, new BigDecimal("450.00"), DISTRIBUTE_TO_LAST_ITEM);

        assertEquals(new BigDecimal("100.00"), items.get(0).getAmount());
        assertEquals(new BigDecimal("200.00"), items.get(1).getAmount());
        assertEquals(new BigDecimal("150.00"), items.get(2).getAmount());
    }

    @Test
    void testDistributeListAmount_PositiveRemainder_DistributeToLastItem() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItemsWithListAmount(new BigDecimal("120.00"), new BigDecimal("240.00"), new BigDecimal("180.00"));

        distributeListAmount(items, new BigDecimal("540.03"), DISTRIBUTE_TO_LAST_ITEM);

        assertEquals(new BigDecimal("120.00"), items.get(0).getListAmount());
        assertEquals(new BigDecimal("240.00"), items.get(1).getListAmount());
        assertEquals(new BigDecimal("180.03"), items.get(2).getListAmount());
    }

    @Test
    void testDistributeListAmount_NegativeRemainder_DistributeToFirstItem() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItemsWithListAmount(new BigDecimal("120.00"), new BigDecimal("240.00"), new BigDecimal("180.00"));

        distributeListAmount(items, new BigDecimal("539.95"), DISTRIBUTE_TO_FIRST_ITEM);

        assertEquals(new BigDecimal("119.95"), items.get(0).getListAmount());
        assertEquals(new BigDecimal("240.00"), items.get(1).getListAmount());
        assertEquals(new BigDecimal("180.00"), items.get(2).getListAmount());
    }

    @Test
    void testDistributeListAmount_PositiveRemainder_DistributeToGreatestDifference() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItemsWithListAmount(new BigDecimal("100.00"), new BigDecimal("350.00"), new BigDecimal("150.00"));

        distributeListAmount(items, new BigDecimal("600.07"), DISTRIBUTE_TO_GREATEST_DIFFERENCE);

        assertEquals(new BigDecimal("100.00"), items.get(0).getListAmount());
        assertEquals(new BigDecimal("350.07"), items.get(1).getListAmount());
        assertEquals(new BigDecimal("150.00"), items.get(2).getListAmount());
    }

    @Test
    void testDistributeListAmount_ZeroRemainder_NoChange() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItemsWithListAmount(new BigDecimal("120.00"), new BigDecimal("240.00"), new BigDecimal("180.00"));

        distributeListAmount(items, new BigDecimal("540.00"), DISTRIBUTE_TO_LAST_ITEM);

        assertEquals(new BigDecimal("120.00"), items.get(0).getListAmount());
        assertEquals(new BigDecimal("240.00"), items.get(1).getListAmount());
        assertEquals(new BigDecimal("180.00"), items.get(2).getListAmount());
    }

    @Test
    void testDistributeSellAmount_SingleItem() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItems(new BigDecimal("100.00"));

        distributeSellAmount(items, new BigDecimal("100.03"), DISTRIBUTE_TO_LAST_ITEM);

        assertEquals(new BigDecimal("100.03"), items.get(0).getAmount());
    }

    @Test
    void testDistributeListAmount_SingleItem() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItemsWithListAmount(new BigDecimal("120.00"));

        distributeListAmount(items, new BigDecimal("119.97"), DISTRIBUTE_TO_FIRST_ITEM);

        assertEquals(new BigDecimal("119.97"), items.get(0).getListAmount());
    }

    @Test
    void testDistributeSellAmount_NegativeAmount() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItems(new BigDecimal("-100.00"), new BigDecimal("-200.00"), new BigDecimal("-150.00"));

        distributeSellAmount(items, new BigDecimal("-450.01"), DISTRIBUTE_TO_LAST_ITEM);

        assertEquals(new BigDecimal("-100.00"), items.get(0).getAmount());
        assertEquals(new BigDecimal("-200.00"), items.get(1).getAmount());
        assertEquals(new BigDecimal("-150.01"), items.get(2).getAmount());
    }

    @Test
    void testDistributeListAmount_NegativeAmount() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItemsWithListAmount(
            new BigDecimal("-120.00"),
            new BigDecimal("-240.00"),
            new BigDecimal("-180.00")
        );

        distributeListAmount(items, new BigDecimal("-540.03"), DISTRIBUTE_TO_LAST_ITEM);

        assertEquals(new BigDecimal("-120.00"), items.get(0).getListAmount());
        assertEquals(new BigDecimal("-240.00"), items.get(1).getListAmount());
        assertEquals(new BigDecimal("-180.03"), items.get(2).getListAmount());
    }

    @Test
    void testDiscountAmountEqualsListMinusSellAmount_AfterSellDistribution() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItems(new BigDecimal("100.00"), new BigDecimal("200.00"), new BigDecimal("150.00"));

        distributeSellAmount(items, new BigDecimal("450.01"), DISTRIBUTE_TO_LAST_ITEM);

        for (InvoiceItem item : items) {
            BigDecimal expectedDiscount = item.getListAmount().subtract(item.getAmount());
            assertEquals(expectedDiscount, item.getDiscountAmount(), "Discount amount should equal list amount minus sell amount");
        }
    }

    @Test
    void testDiscountAmountEqualsListMinusSellAmount_AfterListDistribution() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItemsWithListAmount(new BigDecimal("120.00"), new BigDecimal("240.00"), new BigDecimal("180.00"));

        distributeListAmount(items, new BigDecimal("540.03"), DISTRIBUTE_TO_LAST_ITEM);

        for (InvoiceItem item : items) {
            BigDecimal expectedDiscount = item.getListAmount().subtract(item.getAmount());
            assertEquals(expectedDiscount, item.getDiscountAmount(), "Discount amount should equal list amount minus sell amount");
        }
    }

    @Test
    void testDiscountAmountEqualsListMinusSellAmount_WithRemainderDistribution() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItems(new BigDecimal("100.00"), new BigDecimal("300.00"), new BigDecimal("150.00"));

        distributeSellAmount(items, new BigDecimal("550.02"), DISTRIBUTE_TO_GREATEST_DIFFERENCE);

        for (InvoiceItem item : items) {
            BigDecimal expectedDiscount = item.getListAmount().subtract(item.getAmount());
            assertEquals(
                expectedDiscount,
                item.getDiscountAmount(),
                "Discount amount should equal list amount minus sell amount even with remainder distribution"
            );
        }
    }

    @Test
    void testDiscountAmountEqualsListMinusSellAmount_WithNegativeAmounts() throws Exception {
        when(featureService.isEnabled(Feature.PERCENT_OF_DISTRIBUTION)).thenReturn(true);

        List<InvoiceItem> items = createTestInvoiceItems(new BigDecimal("-100.00"), new BigDecimal("-200.00"), new BigDecimal("-150.00"));

        distributeSellAmount(items, new BigDecimal("-450.01"), DISTRIBUTE_TO_LAST_ITEM);

        for (InvoiceItem item : items) {
            BigDecimal expectedDiscount = item.getListAmount().subtract(item.getAmount());
            assertEquals(
                expectedDiscount,
                item.getDiscountAmount(),
                "Discount amount should equal list amount minus sell amount even with negative amounts"
            );
        }
    }

    private List<InvoiceItem> createTestInvoiceItems(BigDecimal... amounts) {
        List<InvoiceItem> items = new ArrayList<>();
        for (int i = 0; i < amounts.length; i++) {
            InvoiceItem item = InvoiceItemFixture.getDefault()
                .amount(amounts[i])
                .listAmount(amounts[i].add(new BigDecimal("20.00")))
                .discountAmount(new BigDecimal("20.00"))
                .createInvoiceItem();
            items.add(item);
        }
        return items;
    }

    private List<InvoiceItem> createTestInvoiceItemsWithListAmount(BigDecimal... listAmounts) {
        List<InvoiceItem> items = new ArrayList<>();
        for (int i = 0; i < listAmounts.length; i++) {
            BigDecimal sellAmount = listAmounts[i].subtract(new BigDecimal("20.00"));
            InvoiceItem item = InvoiceItemFixture.getDefault()
                .amount(sellAmount)
                .listAmount(listAmounts[i])
                .discountAmount(new BigDecimal("20.00"))
                .createInvoiceItem();
            items.add(item);
        }
        return items;
    }

    private void distributeSellAmount(List<InvoiceItem> items, BigDecimal expectedAmount, Object distributionMethod) throws Exception {
        Method method =
            PercentOfInvoiceProcessor.class.getDeclaredMethod("distributeSellAmount", List.class, BigDecimal.class, getDistributionMethodClass());
        method.setAccessible(true);
        method.invoke(processor, items, expectedAmount, distributionMethod);
    }

    private void distributeListAmount(List<InvoiceItem> items, BigDecimal expectedListAmount, Object distributionMethod) throws Exception {
        Method method =
            PercentOfInvoiceProcessor.class.getDeclaredMethod("distributeListAmount", List.class, BigDecimal.class, getDistributionMethodClass());
        method.setAccessible(true);
        method.invoke(processor, items, expectedListAmount, distributionMethod);
    }

    private Class<?> getDistributionMethodClass() throws Exception {
        Class<?>[] innerClasses = PercentOfInvoiceProcessor.class.getDeclaredClasses();
        for (Class<?> innerClass : innerClasses) {
            if (innerClass.getSimpleName().equals("RemainderDistributionMethod")) {
                return innerClass;
            }
        }
        throw new IllegalStateException("RemainderDistributionMethod enum not found");
    }
}
