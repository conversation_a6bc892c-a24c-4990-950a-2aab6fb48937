package com.subskribe.billy.invoice.fixture;

import com.subskribe.billy.account.fixture.AccountFixture;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.PaymentTerm;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

public class InvoiceFixture {

    public static Invoice.InvoiceBuilder getDefault() {
        InvoiceItem invoiceItem = InvoiceItemFixture.getDefault().createInvoiceItem();
        List<InvoiceItem> invoiceItems = List.of(invoiceItem);
        AccountContact billingContact = AccountFixture.getDefaultContact();
        return new Invoice.InvoiceBuilder()
            .invoiceNumber(new Invoice.Number("INV-123456"))
            .subscriptionId("SUB-1234567")
            .entityId(EntityFixture.ENTITY_1_ID)
            .currency("USD")
            .status(InvoiceStatus.POSTED)
            .invoiceDate(Instant.parse("2024-07-29T07:00:00Z"))
            .dueDate(Instant.parse("2024-08-29T07:00:00Z"))
            .paymentTerm(PaymentTerm.NET45)
            .invoiceItems(invoiceItems)
            .subTotal(invoiceItem.getAmount())
            .total(invoiceItem.getAmount())
            .totalDiscount(BigDecimal.ZERO)
            .billingContact(billingContact);
    }
}
