package com.subskribe.billy.invoice.number;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.invoice.db.InvoiceConfigDAO;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceNumberPrefix;
import com.subskribe.billy.invoice.model.InvoiceNumberScheme;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.TenantInvoiceConfig;
import com.subskribe.billy.invoice.service.InvoiceConfigurationService;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.WithDb;
import org.jooq.DSLContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class InvoiceNumberGeneratorTest extends WithDb {

    private static final String TENANT_ID_STRING_SEQUENCE = "31f7e098-14db-4497-8310-aa8a1602afd0";
    private static final TenantId TENANT_ID_SEQUENCE = new TenantId(TENANT_ID_STRING_SEQUENCE);
    private static final String INVOICE_CONFIG_ID_SEQUENCE = "6017dbca-2271-4152-a132-00e60e907fa9";

    private static final String TENANT_ID_STRING_RANDOM = "27b59431-0cc7-4c87-883b-be345e9bd525";
    private static final TenantId TENANT_ID_RANDOM = new TenantId(TENANT_ID_STRING_RANDOM);
    private static final String INVOICE_CONFIG_ID_RANDOM = "9b58a259-0138-47e3-856d-b2a5ae7886ba";

    private static final int INVOICE_NUMBER_LENGTH_SEQUENCE = 8;

    private static final int INVOICE_NUMBER_LENGTH_RANDOM = 5;

    private static final String INVOICE_NUMBER_PREFIX_DRAFT = InvoiceNumberPrefix.getDraftPrefix();

    private static final String INVOICE_NUMBER_PREFIX_SEQUENCE = "MY-SEQ-";

    private static final String INVOICE_NUMBER_PREFIX_RANDOM = "MY-RANDOM-INV-";

    private static final int INVOICE_NUMBER_SEQUENCE_STARTING_NUMBER = 12345;

    private static final String ENTITY_ID = "entity_id";

    private TenantIdProvider mockTenantIdProvider;

    private final InvoiceConfigDAO mockInvoiceConfigDAO = mock(InvoiceConfigDAO.class);

    private final FeatureService mockFeatureService = mock(FeatureService.class);

    private CacheService mockCacheService;

    private InvoiceConfigurationService invoiceConfigurationService;

    private final EntityGetService mockEntityGetService = EntityGetServiceFixture.entityGetServiceFixture();

    private InvoiceNumberGenerator invoiceNumberGenerator;

    @BeforeEach
    public void initDao() {
        mockTenantIdProvider = mock(TenantIdProvider.class);
        mockCacheService = mock(CacheService.class);
        InvoiceDAO invoiceDAO = new InvoiceDAO(dslContextProvider, mockTenantIdProvider, mockFeatureService);
        invoiceConfigurationService = new InvoiceConfigurationService(invoiceDAO, mockInvoiceConfigDAO, mockCacheService);
        RandomNumberGenerator randomNumberGenerator = new RandomNumberGenerator();
        SequenceInvoiceNumberGenerator sequenceInvoiceNumberGenerator = new SequenceInvoiceNumberGenerator(invoiceDAO);
        invoiceNumberGenerator = new InvoiceNumberGenerator(
            invoiceDAO,
            invoiceConfigurationService,
            mockEntityGetService,
            randomNumberGenerator,
            sequenceInvoiceNumberGenerator
        );
    }

    @Test
    public void generateDraftInvoiceNumber() {
        setUpTenantIdProvider(InvoiceNumberScheme.SEQUENCE);
        setupInvoiceConfig(INVOICE_CONFIG_ID_SEQUENCE);

        String invoiceNumber = invoiceNumberGenerator.generateDraft(ENTITY_ID).getNumber();
        assertThat(invoiceNumber).startsWith(INVOICE_NUMBER_PREFIX_DRAFT);
        assertThat(invoiceNumber).hasSize(INVOICE_NUMBER_PREFIX_DRAFT.length() + INVOICE_NUMBER_LENGTH_SEQUENCE);
    }

    @Test
    public void generateSequenceInvoiceNumber() {
        setUpTenantIdProvider(InvoiceNumberScheme.SEQUENCE);
        DSLContext dslContext = dslContextProvider.get(TENANT_ID_STRING_SEQUENCE);
        setupInvoiceConfig(INVOICE_CONFIG_ID_SEQUENCE);

        // first invoice number should be the one in the tenant invoice config
        Invoice.Number invoiceNumber = dslContext.transactionResult(configuration ->
            invoiceNumberGenerator.generate(configuration, ENTITY_ID, InvoiceStatus.POSTED, false)
        );
        assertThat(invoiceNumber.getNumber()).startsWith(INVOICE_NUMBER_PREFIX_SEQUENCE);
        assertThat(invoiceNumber.getNumber()).endsWith(String.valueOf(INVOICE_NUMBER_SEQUENCE_STARTING_NUMBER));
        assertThat(invoiceNumber.getNumber()).hasSize(INVOICE_NUMBER_PREFIX_SEQUENCE.length() + INVOICE_NUMBER_LENGTH_SEQUENCE);

        // Now get the next number --> should be sequential.
        Invoice.Number secondInvoiceNumber = dslContext.transactionResult(configuration ->
            invoiceNumberGenerator.generate(configuration, ENTITY_ID, InvoiceStatus.POSTED, false)
        );
        assertThat(secondInvoiceNumber.getNumber()).startsWith(INVOICE_NUMBER_PREFIX_SEQUENCE);
        assertThat(secondInvoiceNumber.getNumber()).endsWith(String.valueOf(INVOICE_NUMBER_SEQUENCE_STARTING_NUMBER + 1));
        assertThat(secondInvoiceNumber.getNumber()).hasSize(INVOICE_NUMBER_PREFIX_SEQUENCE.length() + INVOICE_NUMBER_LENGTH_SEQUENCE);
    }

    @Test
    public void generateRandomInvoiceNumber() {
        setUpTenantIdProvider(InvoiceNumberScheme.PSEUDO_RANDOM);
        setupInvoiceConfig(INVOICE_CONFIG_ID_RANDOM);

        Invoice.Number invoiceNumber = dslContextProvider
            .get(TENANT_ID_STRING_RANDOM)
            .transactionResult(configuration -> invoiceNumberGenerator.generate(configuration, ENTITY_ID, InvoiceStatus.POSTED, false));
        assertThat(invoiceNumber.getNumber()).startsWith(INVOICE_NUMBER_PREFIX_RANDOM);
        assertThat(invoiceNumber.getNumber()).hasSize(INVOICE_NUMBER_PREFIX_RANDOM.length() + INVOICE_NUMBER_LENGTH_RANDOM);
    }

    private void setUpTenantIdProvider(InvoiceNumberScheme invoiceNumberScheme) {
        if (invoiceNumberScheme == InvoiceNumberScheme.SEQUENCE) {
            when(mockTenantIdProvider.provide()).thenReturn(TENANT_ID_SEQUENCE);
            when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID_SEQUENCE.getRequiredId());
        } else {
            when(mockTenantIdProvider.provide()).thenReturn(TENANT_ID_RANDOM);
            when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID_RANDOM.getRequiredId());
        }
    }

    private void setupInvoiceConfig(String invoiceConfigId) {
        TenantInvoiceConfig tenantInvoiceConfig = invoiceConfigurationService.getInvoiceConfigById(invoiceConfigId);
        when(mockCacheService.get(any(), eq(CacheType.INVOICE_CONFIG_BY_ID), any(), any(), eq(TenantInvoiceConfig.class))).thenReturn(
            tenantInvoiceConfig
        );
    }
}
