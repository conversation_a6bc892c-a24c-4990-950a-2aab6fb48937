package com.subskribe.billy.invoice.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.shared.pecuniary.Numbers;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;

class WeightedDistributionCalculatorTest {

    @Test
    void getWeightedDistributionAmounts_correctlyDistributesAmounts() {
        LinkedHashMap<String, BigDecimal> orderLineAmounts = new LinkedHashMap<>();
        orderLineAmounts.put("line_1", new BigDecimal("2.00"));
        orderLineAmounts.put("line_2", new BigDecimal("3.00"));
        BigDecimal totalAmount = new BigDecimal("5.00");
        String currencyCode = "USD";
        BigDecimal weightRatio = new BigDecimal("1.00");

        LinkedHashMap<String, BigDecimal> result = WeightedDistributionCalculator.getWeightedDistributionAmounts(
            orderLineAmounts,
            totalAmount,
            currencyCode,
            weightRatio
        );

        assertEquals(new BigDecimal("2.00"), result.get("line_1"));
        assertEquals(new BigDecimal("3.00"), result.get("line_2"));
    }

    @Test
    void getWeightedDistributionAmounts_correctlyDistributesAmounts_2() {
        LinkedHashMap<String, BigDecimal> orderLineAmounts = new LinkedHashMap<>();
        orderLineAmounts.put("line_1", new BigDecimal("15.55")); // 0.25 * 15.55 = 3.8875 = 3.89
        orderLineAmounts.put("line_2", new BigDecimal("22.45")); // 0.25 * 22.45 = 5.6125 = 5.61
        orderLineAmounts.put("line_3", new BigDecimal("12.33")); // 0.25 * 12.33 = 3.0825 = 3.08
        orderLineAmounts.put("line_4", new BigDecimal("10.61")); // 0.25 * 10.61 = 2.6525 = 2.65, Total = 15.23
        BigDecimal totalAmount = new BigDecimal("15.24"); // 0.25 * 60.94 = 15.235 = 15.24
        String currencyCode = "USD";
        BigDecimal weightRatio = new BigDecimal("0.25");

        LinkedHashMap<String, BigDecimal> result = WeightedDistributionCalculator.getWeightedDistributionAmounts(
            orderLineAmounts,
            totalAmount,
            currencyCode,
            weightRatio
        );

        assertEquals(new BigDecimal("3.90"), result.get("line_1")); // Adjust 1 extra cent to match order line total
        assertEquals(new BigDecimal("5.61"), result.get("line_2"));
        assertEquals(new BigDecimal("3.08"), result.get("line_3"));
        assertEquals(new BigDecimal("2.65"), result.get("line_4"));
    }

    @Test
    void getWeightedDistributionAmounts_handlesRoundingArtifacts() {
        LinkedHashMap<String, BigDecimal> orderLineAmounts = new LinkedHashMap<>();
        orderLineAmounts.put("line_1", new BigDecimal("2.333"));
        orderLineAmounts.put("line_2", new BigDecimal("2.667"));
        BigDecimal totalAmount = new BigDecimal("5.00");
        String currencyCode = "USD";
        BigDecimal weightRatio = new BigDecimal("1.00");

        LinkedHashMap<String, BigDecimal> result = WeightedDistributionCalculator.getWeightedDistributionAmounts(
            orderLineAmounts,
            totalAmount,
            currencyCode,
            weightRatio
        );

        assertEquals(new BigDecimal("2.33"), result.get("line_1"));
        assertEquals(new BigDecimal("2.67"), result.get("line_2"));
    }

    @Test
    void handleRoundingArtifacts_correctlyAdjustsAmounts() {
        Map<String, BigDecimal> roundedAmounts = new LinkedHashMap<>();
        roundedAmounts.put("line_1", new BigDecimal("2.33"));
        roundedAmounts.put("line_2", new BigDecimal("2.67"));
        BigDecimal totalRoundedAmount = new BigDecimal("5.00");
        BigDecimal totalAmount = new BigDecimal("5.00");
        String currencyCode = "USD";

        LinkedHashMap<String, BigDecimal> result = WeightedDistributionCalculator.handleRoundingArtifacts(
            roundedAmounts,
            totalRoundedAmount,
            totalAmount,
            currencyCode,
            false
        );

        assertEquals(new BigDecimal("2.33"), result.get("line_1"));
        assertEquals(new BigDecimal("2.67"), result.get("line_2"));
    }

    @Test
    void handleRoundingArtifacts_handlesPositiveRoundingDifference() {
        Map<String, BigDecimal> roundedAmounts = new LinkedHashMap<>();
        roundedAmounts.put("line_1", new BigDecimal("2.34"));
        roundedAmounts.put("line_2", new BigDecimal("2.67"));
        BigDecimal totalRoundedAmount = new BigDecimal("5.01");
        BigDecimal totalAmount = new BigDecimal("5.00");
        String currencyCode = "USD";

        LinkedHashMap<String, BigDecimal> result = WeightedDistributionCalculator.handleRoundingArtifacts(
            roundedAmounts,
            totalRoundedAmount,
            totalAmount,
            currencyCode,
            false
        );

        assertEquals(new BigDecimal("2.33"), result.get("line_1"));
        assertEquals(new BigDecimal("2.67"), result.get("line_2"));
    }

    @Test
    void handleRoundingArtifacts_handlesNegativeRoundingDifference() {
        Map<String, BigDecimal> roundedAmounts = new LinkedHashMap<>();
        roundedAmounts.put("line_1", new BigDecimal("2.32"));
        roundedAmounts.put("line_2", new BigDecimal("2.67"));
        BigDecimal totalRoundedAmount = new BigDecimal("4.99");
        BigDecimal totalAmount = new BigDecimal("5.00");
        String currencyCode = "USD";

        LinkedHashMap<String, BigDecimal> result = WeightedDistributionCalculator.handleRoundingArtifacts(
            roundedAmounts,
            totalRoundedAmount,
            totalAmount,
            currencyCode,
            false
        );

        assertEquals(new BigDecimal("2.33"), result.get("line_1"));
        assertEquals(new BigDecimal("2.67"), result.get("line_2"));
    }

    @Test
    void adjustRoundedAmounts_noAdjustmentNeeded() {
        Map<String, BigDecimal> roundedAmounts = new LinkedHashMap<>();
        roundedAmounts.put("line_1", new BigDecimal("3.50"));
        roundedAmounts.put("line_2", new BigDecimal("4.50"));
        LinkedHashMap<String, BigDecimal> roundingAdjustedAmounts = new LinkedHashMap<>();
        BigDecimal absoluteRoundingDifference = BigDecimal.ZERO;
        BigDecimal lowestDenomination = new BigDecimal("0.01");
        BigDecimal amountToAdjust = new BigDecimal("0.01");

        BigDecimal result = WeightedDistributionCalculator.adjustRoundedAmounts(
            roundedAmounts,
            roundingAdjustedAmounts,
            absoluteRoundingDifference,
            lowestDenomination,
            amountToAdjust
        );

        assertEquals(BigDecimal.ZERO, result);
        assertEquals(roundingAdjustedAmounts.size(), 0);
    }

    @Test
    void adjustRoundedAmounts_positiveAdjustment() {
        Map<String, BigDecimal> roundedAmounts = new LinkedHashMap<>();
        roundedAmounts.put("line_1", new BigDecimal("5.00"));
        roundedAmounts.put("line_2", new BigDecimal("3.00"));
        LinkedHashMap<String, BigDecimal> roundingAdjustedAmounts = new LinkedHashMap<>();
        BigDecimal absoluteRoundingDifference = new BigDecimal("0.02");
        BigDecimal lowestDenomination = new BigDecimal("0.01");
        BigDecimal amountToAdjust = new BigDecimal("0.01");

        BigDecimal result = WeightedDistributionCalculator.adjustRoundedAmounts(
            roundedAmounts,
            roundingAdjustedAmounts,
            absoluteRoundingDifference,
            lowestDenomination,
            amountToAdjust
        );

        assertTrue(Numbers.equals(result, BigDecimal.ZERO));
        assertEquals(new BigDecimal("4.99"), roundingAdjustedAmounts.get("line_1"));
        assertEquals(new BigDecimal("2.99"), roundingAdjustedAmounts.get("line_2"));
    }

    @Test
    void adjustRoundedAmounts_negativeAdjustment() {
        Map<String, BigDecimal> roundedAmounts = new LinkedHashMap<>();
        roundedAmounts.put("line_1", new BigDecimal("2.00"));
        roundedAmounts.put("line_2", new BigDecimal("3.00"));
        roundedAmounts.put("line_3", new BigDecimal("5.00"));
        roundedAmounts.put("line_4", new BigDecimal("1.00"));
        LinkedHashMap<String, BigDecimal> roundingAdjustedAmounts = new LinkedHashMap<>();
        BigDecimal absoluteRoundingDifference = new BigDecimal("0.03");
        BigDecimal lowestDenomination = new BigDecimal("0.01");
        BigDecimal amountToAdjust = new BigDecimal("-0.01");

        BigDecimal result = WeightedDistributionCalculator.adjustRoundedAmounts(
            roundedAmounts,
            roundingAdjustedAmounts,
            absoluteRoundingDifference,
            lowestDenomination,
            amountToAdjust
        );

        assertTrue(Numbers.equals(result, BigDecimal.ZERO));
        assertEquals(new BigDecimal("2.01"), roundingAdjustedAmounts.get("line_1"));
        assertEquals(new BigDecimal("3.01"), roundingAdjustedAmounts.get("line_2"));
        assertEquals(new BigDecimal("5.01"), roundingAdjustedAmounts.get("line_3"));
        assertEquals(null, roundingAdjustedAmounts.get("line_4"));
    }

    @Test
    void adjustRoundedAmounts_invalidLowestDenomination() {
        Map<String, BigDecimal> roundedAmounts = new LinkedHashMap<>();
        roundedAmounts.put("line_1", new BigDecimal("1.50"));
        roundedAmounts.put("line_2", new BigDecimal("2.50"));
        LinkedHashMap<String, BigDecimal> roundingAdjustedAmounts = new LinkedHashMap<>();
        BigDecimal absoluteRoundingDifference = new BigDecimal("0.02");
        BigDecimal lowestDenomination = new BigDecimal("0.02"); // Invalid
        BigDecimal amountToAdjust = new BigDecimal("0.01");

        assertThrows(InvariantCheckFailedException.class, () ->
            WeightedDistributionCalculator.adjustRoundedAmounts(
                roundedAmounts,
                roundingAdjustedAmounts,
                absoluteRoundingDifference,
                lowestDenomination,
                amountToAdjust
            )
        );
    }
}
