package com.subskribe.billy.invoice.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.invoice.model.BillingSchedule;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.TimeZone;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class BillingScheduleServiceTest {

    @Mock
    private OrderGetService orderGetService;

    @Mock
    private SubscriptionGetService subscriptionGetService;

    @Mock
    private ProductCatalogGetService productCatalogGetService;

    @Mock
    private TenantSettingService tenantSettingService;

    @Mock
    private InvoiceService invoiceService;

    @Mock
    private InvoiceRetrievalService invoiceRetrievalService;

    @Mock
    private ProrationConfigurationGetService prorationConfigurationGetService;

    @Mock
    private DSLContextProvider dslContextProvider;

    @Mock
    private TenantIdProvider tenantIdProvide;

    private BillingScheduleService billingScheduleService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        billingScheduleService = new BillingScheduleService(
            orderGetService,
            subscriptionGetService,
            productCatalogGetService,
            tenantSettingService,
            invoiceService,
            invoiceRetrievalService,
            prorationConfigurationGetService,
            dslContextProvider,
            tenantIdProvide
        );
    }

    @Test
    void validateBillingEventInput() {
        Instant lineStartDate = Instant.now();
        Instant lineEndDate = DateTimeCalculator.plusYears(TimeZone.getTimeZone("UTC").toZoneId(), lineStartDate, 1);
        OrderLineItem orderLineItem = getOrderLineItem(ActionType.ADD);
        orderLineItem.setEffectiveDate(lineStartDate);
        orderLineItem.setEndDate(lineEndDate);

        Charge charge = new Charge();
        charge.setEventBased(true);

        when(productCatalogGetService.getChargeByChargeId(any())).thenReturn(charge);

        BillingSchedule billingSchedule = new BillingSchedule(
            UUID.randomUUID().toString(),
            orderLineItem.getOrderLineId(),
            BigDecimal.TEN,
            List.of()
        );

        // valid input
        assertDoesNotThrow(() -> billingScheduleService.validateEventInput(lineStartDate, BigDecimal.ONE, orderLineItem, billingSchedule));

        // triggerOn before line start date
        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            billingScheduleService.validateEventInput(lineStartDate.minusSeconds(1), BigDecimal.ONE, orderLineItem, billingSchedule)
        );
        assertTrue(exception.getMessage().contains("billing event must be between effective date and end date"));

        // triggerOn after line end date
        exception = assertThrows(InvalidInputException.class, () ->
            billingScheduleService.validateEventInput(lineEndDate.plusSeconds(1), BigDecimal.ONE, orderLineItem, billingSchedule)
        );
        assertTrue(exception.getMessage().contains("billing event must be between effective date and end date"));

        // $0 amount
        exception = assertThrows(InvalidInputException.class, () ->
            billingScheduleService.validateEventInput(lineStartDate, BigDecimal.ZERO, orderLineItem, billingSchedule)
        );
        assertTrue(exception.getMessage().contains("billing event amount cannot be zero"));

        // negative amount + positive remaining amount
        exception = assertThrows(InvalidInputException.class, () ->
            billingScheduleService.validateEventInput(lineStartDate, BigDecimal.ONE.negate(), orderLineItem, billingSchedule)
        );
        assertTrue(exception.getMessage().contains("billing event amount must be greater than zero"));

        // positive amount + negative remaining amount
        BillingSchedule negativeAmountBillingSchedule = new BillingSchedule(
            UUID.randomUUID().toString(),
            orderLineItem.getOrderLineId(),
            BigDecimal.TEN.negate(),
            List.of()
        );
        exception = assertThrows(InvalidInputException.class, () ->
            billingScheduleService.validateEventInput(lineStartDate, BigDecimal.ONE, orderLineItem, negativeAmountBillingSchedule)
        );
        assertTrue(exception.getMessage().contains("billing event amount must be less than zero"));

        // too many decimals in amount
        exception = assertThrows(InvalidInputException.class, () ->
            billingScheduleService.validateEventInput(lineStartDate, new BigDecimal("1.123"), orderLineItem, billingSchedule)
        );
        assertTrue(exception.getMessage().contains("billing event amount must have at most 2 decimal places"));

        // charge not event-based
        charge.setEventBased(false);
        exception = assertThrows(InvalidInputException.class, () ->
            billingScheduleService.validateEventInput(lineStartDate, BigDecimal.ONE, orderLineItem, billingSchedule)
        );
        assertTrue(exception.getMessage().contains("charge must be event based"));
    }

    @Test
    void subscriptionChargeHasSingleAddOrderLineItem() {
        var orderLineItem = getOrderLineItem(ActionType.ADD);
        List<String> orderLineIds = List.of(orderLineItem.getOrderLineId());
        List<OrderLineItem> orderLineItems = List.of(orderLineItem);

        SubscriptionCharge subscriptionCharge = getSubscriptionCharge(orderLineIds);
        when(orderGetService.getOrderLineItemsByOrderLineItemIds(orderLineIds)).thenReturn(orderLineItems);

        OrderLineItem item = billingScheduleService.getGetOrderLineFromSubscriptionCharge(subscriptionCharge);

        assertEquals(orderLineItem.getOrderLineId(), item.getOrderLineId());
    }

    @Test
    void subscriptionChargeHasSingleRenewalOrderLineItem() {
        var orderLineItem = getOrderLineItem(ActionType.RENEWAL);
        List<String> orderLineIds = List.of(orderLineItem.getOrderLineId());
        List<OrderLineItem> orderLineItems = List.of(orderLineItem);

        SubscriptionCharge subscriptionCharge = getSubscriptionCharge(orderLineIds);
        when(orderGetService.getOrderLineItemsByOrderLineItemIds(orderLineIds)).thenReturn(orderLineItems);

        OrderLineItem item = billingScheduleService.getGetOrderLineFromSubscriptionCharge(subscriptionCharge);

        assertEquals(orderLineItem.getOrderLineId(), item.getOrderLineId());
    }

    @Test
    void subscriptionChargeHasSingleRestructureOrderLineItem() {
        var orderLineItem = getOrderLineItem(ActionType.RESTRUCTURE);
        List<String> orderLineIds = List.of(orderLineItem.getOrderLineId());
        List<OrderLineItem> orderLineItems = List.of(orderLineItem);

        SubscriptionCharge subscriptionCharge = getSubscriptionCharge(orderLineIds);
        when(orderGetService.getOrderLineItemsByOrderLineItemIds(orderLineIds)).thenReturn(orderLineItems);

        OrderLineItem item = billingScheduleService.getGetOrderLineFromSubscriptionCharge(subscriptionCharge);

        assertEquals(orderLineItem.getOrderLineId(), item.getOrderLineId());
    }

    @Test
    void subscriptionChargeHasSingleUpdateOrderLineItem() {
        var orderLineItem = getOrderLineItem(ActionType.UPDATE);
        List<String> orderLineIds = List.of(orderLineItem.getOrderLineId());
        List<OrderLineItem> orderLineItems = List.of(orderLineItem);

        SubscriptionCharge subscriptionCharge = getSubscriptionCharge(orderLineIds);
        when(orderGetService.getOrderLineItemsByOrderLineItemIds(orderLineIds)).thenReturn(orderLineItems);

        assertThrows(InvariantCheckFailedException.class, () -> billingScheduleService.getGetOrderLineFromSubscriptionCharge(subscriptionCharge));
    }

    @Test
    void subscriptionChargeHasNoOrderLineItems() {
        SubscriptionCharge subscriptionCharge = getSubscriptionCharge(List.of());
        when(orderGetService.getOrderLineItemsByOrderLineItemIds(any())).thenReturn(List.of());

        assertThrows(InvariantCheckFailedException.class, () -> billingScheduleService.getGetOrderLineFromSubscriptionCharge(subscriptionCharge));
    }

    private OrderLineItem getOrderLineItem(ActionType actionType) {
        var orderLineItem = new OrderLineItem();
        orderLineItem.setOrderLineId(UUID.randomUUID().toString());
        orderLineItem.setAction(actionType);
        return orderLineItem;
    }

    private SubscriptionCharge getSubscriptionCharge(List<String> orderLineItemIds) {
        return new SubscriptionCharge(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            orderLineItemIds,
            null,
            null,
            0,
            false,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            1,
            null,
            null,
            0,
            null
        );
    }
}
