package com.subskribe.billy.invoice.service.processor;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.invoice.model.ImmutableMemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Currency;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

class InvoiceProcessorUtilityTest {

    private static final Set<ZonedDateTime> uniquePeriodStartDates = new HashSet<>();

    @Test
    void adjustMemoizedLinesAmountToOrderLineAmount_noAdjustmentNeeded() {
        // Setup
        Order order = new Order();
        order.setOrderId("order_1");
        order.setCurrency(Currency.getInstance("USD"));

        Map<String, List<MemoizedInvoiceLineItem>> memoizedInvoiceLinesByOrderLine = new HashMap<>();
        List<MemoizedInvoiceLineItem> memoizedItems = new ArrayList<>();
        var memoizedItem = createMemoizedInvoiceLineItem("line_1", BigDecimal.valueOf(50.00));
        memoizedItems.add(memoizedItem);
        memoizedInvoiceLinesByOrderLine.put("line_1", memoizedItems);

        Map<String, BigDecimal> orderLineIdToAmountMap = new HashMap<>();
        orderLineIdToAmountMap.put("line_1", BigDecimal.valueOf(50.00));

        // Execute
        Map<String, List<MemoizedInvoiceLineItem>> result = InvoiceProcessorUtility.adjustMemoizedLinesAmountToOrderLineAmount(
            order,
            memoizedInvoiceLinesByOrderLine,
            orderLineIdToAmountMap
        );

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void adjustMemoizedLinesAmountToOrderLineAmount_adjustmentNeeded() {
        // Setup
        Order order = new Order();
        order.setOrderId("order_2");
        order.setCurrency(Currency.getInstance("USD"));

        Map<String, List<MemoizedInvoiceLineItem>> memoizedInvoiceLinesByOrderLine = new HashMap<>();
        List<MemoizedInvoiceLineItem> memoizedItems = new ArrayList<>();
        var memoizedItem1 = createMemoizedInvoiceLineItem("line_1", BigDecimal.valueOf(30.00));
        var memoizedItem2 = createMemoizedInvoiceLineItem("line_2", BigDecimal.valueOf(20.00));
        memoizedItems.add(memoizedItem1);
        memoizedItems.add(memoizedItem2);
        memoizedInvoiceLinesByOrderLine.put("line_1", memoizedItems);

        Map<String, BigDecimal> orderLineIdToAmountMap = new HashMap<>();
        orderLineIdToAmountMap.put("line_1", BigDecimal.valueOf(51.02));

        // Execute
        Map<String, List<MemoizedInvoiceLineItem>> result = InvoiceProcessorUtility.adjustMemoizedLinesAmountToOrderLineAmount(
            order,
            memoizedInvoiceLinesByOrderLine,
            orderLineIdToAmountMap
        );

        // Assert
        BigDecimal adjustedTotal = result.get("line_1").stream().map(MemoizedInvoiceLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(BigDecimal.valueOf(51.02), adjustedTotal);
    }

    @Test
    void adjustMemoizedLinesAmountToOrderLineAmount_adjustmentNeeded2() {
        // Setup
        Order order = new Order();
        order.setOrderId("order_3");
        order.setCurrency(Currency.getInstance("USD"));

        Map<String, List<MemoizedInvoiceLineItem>> memoizedInvoiceLinesByOrderLine = new HashMap<>();
        List<MemoizedInvoiceLineItem> memoizedItems = new ArrayList<>();
        var memoizedItem = createMemoizedInvoiceLineItem("line_1", BigDecimal.valueOf(30.00));
        memoizedItems.add(memoizedItem);
        memoizedInvoiceLinesByOrderLine.put("line_1", memoizedItems);

        Map<String, BigDecimal> orderLineIdToAmountMap = new HashMap<>();
        orderLineIdToAmountMap.put("line_1", BigDecimal.valueOf(30.05));

        var adjustMemoizedLinesAmountToOrderLineAmount = InvoiceProcessorUtility.adjustMemoizedLinesAmountToOrderLineAmount(
            order,
            memoizedInvoiceLinesByOrderLine,
            orderLineIdToAmountMap
        );

        // Assert
        assertEquals(1, adjustMemoizedLinesAmountToOrderLineAmount.size());
        List<MemoizedInvoiceLineItem> adjustedItems = adjustMemoizedLinesAmountToOrderLineAmount.get("line_1");
        assertEquals(1, adjustedItems.size());
        MemoizedInvoiceLineItem adjustedItem = adjustedItems.get(0);
        assertEquals(BigDecimal.valueOf(30.05), adjustedItem.getAmount());
    }

    @Test
    void handleOrderLineAndMemoizedLineAmountDifference_noAdjustmentNeeded() {
        // Setup
        Order order = new Order();
        order.setOrderId("order_1");
        order.setCurrency(Currency.getInstance("USD"));

        List<OrderLineItem> orderLineItems = new ArrayList<>();
        orderLineItems.add(createOrderLineItem("line_1", BigDecimal.valueOf(50.00)));
        order.setLineItemsNetEffect(orderLineItems);

        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems = new ArrayList<>();
        memoizedInvoiceLineItems.add(createMemoizedInvoiceLineItem("line_1", BigDecimal.valueOf(50.00)));

        // Execute
        Map<String, List<MemoizedInvoiceLineItem>> result = InvoiceProcessorUtility.handleOrderLineAndMemoizedLineAmountDifference(
            order,
            orderLineItems,
            memoizedInvoiceLineItems,
            List.of(),
            List.of()
        );

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    @Disabled("Flaky test, needs investigation")
    void handleOrderLineAndMemoizedLineAmountDifference_adjustmentNeeded() {
        // Setup
        Order order = new Order();
        order.setOrderId("order_2");
        order.setCurrency(Currency.getInstance("USD"));

        List<OrderLineItem> orderLineItems = new ArrayList<>();
        orderLineItems.add(createOrderLineItem("line_1", BigDecimal.valueOf(101.06)));
        orderLineItems.add(createOrderLineItem("line_2", BigDecimal.valueOf(202.08)));
        orderLineItems.add(createOrderLineItem("line_3", BigDecimal.valueOf(303.10)));
        orderLineItems.add(createOrderLineItem("line_4", BigDecimal.valueOf(404.12)));
        order.setLineItemsNetEffect(orderLineItems);

        List<MemoizedInvoiceLineItem> memoizedInvoiceLineItems = new ArrayList<>();
        memoizedInvoiceLineItems.add(createMemoizedInvoiceLineItem("line_1", BigDecimal.valueOf(50.00)));
        memoizedInvoiceLineItems.add(createMemoizedInvoiceLineItem("line_1", BigDecimal.valueOf(50.00)));
        memoizedInvoiceLineItems.add(createMemoizedInvoiceLineItem("line_2", BigDecimal.valueOf(100.00)));
        memoizedInvoiceLineItems.add(createMemoizedInvoiceLineItem("line_2", BigDecimal.valueOf(100.00)));
        memoizedInvoiceLineItems.add(createMemoizedInvoiceLineItem("line_3", BigDecimal.valueOf(150.00)));
        memoizedInvoiceLineItems.add(createMemoizedInvoiceLineItem("line_3", BigDecimal.valueOf(150.00)));
        memoizedInvoiceLineItems.add(createMemoizedInvoiceLineItem("line_4", BigDecimal.valueOf(200.00)));
        memoizedInvoiceLineItems.add(createMemoizedInvoiceLineItem("line_4", BigDecimal.valueOf(200.00)));

        // Execute
        Map<String, List<MemoizedInvoiceLineItem>> result = InvoiceProcessorUtility.handleOrderLineAndMemoizedLineAmountDifference(
            order,
            orderLineItems,
            memoizedInvoiceLineItems,
            List.of(),
            List.of()
        );

        // Assert
        assertEquals(4, result.size());
        assertEquals(
            BigDecimal.valueOf(101.06),
            result.get("line_1").stream().map(MemoizedInvoiceLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        assertEquals(
            BigDecimal.valueOf(202.08),
            result.get("line_2").stream().map(MemoizedInvoiceLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        assertEquals(
            BigDecimal.valueOf(303.10).setScale(2),
            result.get("line_3").stream().map(MemoizedInvoiceLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        assertEquals(
            BigDecimal.valueOf(404.12),
            result.get("line_4").stream().map(MemoizedInvoiceLineItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
        );
    }

    private MemoizedInvoiceLineItem createMemoizedInvoiceLineItem(String orderLineItemId, BigDecimal amount) {
        ZonedDateTime periodStartDateZoned = getNewPeriodStartDate();
        while (uniquePeriodStartDates.contains(periodStartDateZoned)) {
            periodStartDateZoned = getNewPeriodStartDate();
            uniquePeriodStartDates.add(periodStartDateZoned);
        }
        var periodStartDate = periodStartDateZoned.toInstant();
        return ImmutableMemoizedInvoiceLineItem.builder()
            .id(UUID.randomUUID())
            .amount(amount)
            .subscriptionId("subscription_1")
            .chargeId("charge_1")
            .subscriptionChargeId("subscriptionCharge_1")
            .subscriptionChargeGroupId("subscriptionChargeGroup_1")
            .orderId("order_1")
            .orderLineItemId(orderLineItemId)
            .listAmount(BigDecimal.valueOf(100.00))
            .discountAmount(BigDecimal.valueOf(10.00))
            .quantity(1L)
            .periodStartDate(periodStartDate)
            .periodEndDate(periodStartDate.plus(30, ChronoUnit.DAYS))
            .triggerOn(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant())
            .build();
    }

    private ZonedDateTime getNewPeriodStartDate() {
        return LocalDate.of(2025, 1, 1).plusDays(new Random().nextInt(200)).plusDays(new Random().nextInt(50)).atStartOfDay(ZoneId.systemDefault());
    }

    private OrderLineItem createOrderLineItem(String orderLineId, BigDecimal amount) {
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setOrderLineId(orderLineId);
        orderLineItem.setAmount(amount);
        return orderLineItem;
    }
}
