package com.subskribe.billy.invoice.service.processor;

import com.subskribe.billy.invoice.model.RatingInput;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.test.utilities.MockChargeBuilder;
import com.subskribe.billy.usage.model.CreditBucket;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class UsageInvoiceProcessorTest {

    private static final TimeZone TIME_ZONE = TimeZone.getTimeZone("UTC");

    @Test
    public void buildRatingInputWithPrepaidPeriod() {
        Charge charge = getPerUnitUsageCharge();
        charge.setDrawdown(true);

        Instant now = Instant.now();
        Period period = Period.between(DateTimeCalculator.minusMonths(TIME_ZONE.toZoneId(), now, 12), now);
        Period usagePeriod = Period.between(DateTimeCalculator.minusMonths(TIME_ZONE.toZoneId(), now, 3), now);
        List<CreditBucket> creditBuckets = List.of(createCreditBucket(500L, period));

        RatingInput ratingInput = UsageInvoiceProcessor.buildDrawdownRatingInput(charge, 100L, usagePeriod, creditBuckets, Optional.empty());
        Assertions.assertThat(creditBuckets.get(0).getRemainingQuantity()).isEqualTo(400L);
        Assertions.assertThat(ratingInput.getQuantityByAttributeReferences().get(null)).isEqualTo(0L);

        creditBuckets.get(0).setRemainingQuantity(500L);
        ratingInput = UsageInvoiceProcessor.buildDrawdownRatingInput(charge, 500L, usagePeriod, creditBuckets, Optional.empty());
        Assertions.assertThat(creditBuckets.get(0).getRemainingQuantity()).isEqualTo(0L);
        Assertions.assertThat(ratingInput.getQuantityByAttributeReferences().get(null)).isEqualTo(0L);

        creditBuckets.get(0).setRemainingQuantity(100L);
        ratingInput = UsageInvoiceProcessor.buildDrawdownRatingInput(charge, 500L, usagePeriod, creditBuckets, Optional.empty());
        Assertions.assertThat(creditBuckets.get(0).getRemainingQuantity()).isEqualTo(0L);
        Assertions.assertThat(ratingInput.getQuantityByAttributeReferences().get(null)).isEqualTo(400L);
    }

    @Test
    public void buildRatingInputWithMultiplePrepaidDrawdownPeriods() {
        Charge charge = getPerUnitUsageCharge();
        charge.setDrawdown(true);

        Instant now = Instant.now();
        Instant oneYearAgo = DateTimeCalculator.minusMonths(TIME_ZONE.toZoneId(), now, 12);
        Instant nineMonthsAgo = DateTimeCalculator.minusMonths(TIME_ZONE.toZoneId(), now, 9);
        Instant sixMonthsAgo = DateTimeCalculator.minusMonths(TIME_ZONE.toZoneId(), now, 6);
        Period period = Period.between(oneYearAgo, now);
        Period period2 = Period.between(oneYearAgo, sixMonthsAgo);
        Period usagePeriod = Period.between(oneYearAgo, nineMonthsAgo);
        List<CreditBucket> creditBuckets = List.of(createCreditBucket(500L, period), createCreditBucket(300L, period2));

        RatingInput ratingInput = UsageInvoiceProcessor.buildDrawdownRatingInput(charge, 100L, usagePeriod, creditBuckets, Optional.empty());
        Assertions.assertThat(creditBuckets.get(0).getRemainingQuantity()).isEqualTo(500L);
        Assertions.assertThat(creditBuckets.get(1).getRemainingQuantity()).isEqualTo(200L);
        Assertions.assertThat(ratingInput.getQuantityByAttributeReferences().get(null)).isEqualTo(0L);

        creditBuckets.get(0).setRemainingQuantity(500L);
        creditBuckets.get(1).setRemainingQuantity(300L);
        ratingInput = UsageInvoiceProcessor.buildDrawdownRatingInput(charge, 500L, usagePeriod, creditBuckets, Optional.empty());
        Assertions.assertThat(creditBuckets.get(0).getRemainingQuantity()).isEqualTo(300L);
        Assertions.assertThat(creditBuckets.get(1).getRemainingQuantity()).isEqualTo(0L);
        Assertions.assertThat(ratingInput.getQuantityByAttributeReferences().get(null)).isEqualTo(0L);

        creditBuckets.get(0).setRemainingQuantity(500L);
        creditBuckets.get(1).setRemainingQuantity(300L);
        ratingInput = UsageInvoiceProcessor.buildDrawdownRatingInput(charge, 800L, usagePeriod, creditBuckets, Optional.empty());
        Assertions.assertThat(creditBuckets.get(0).getRemainingQuantity()).isEqualTo(0L);
        Assertions.assertThat(creditBuckets.get(1).getRemainingQuantity()).isEqualTo(0L);
        Assertions.assertThat(ratingInput.getQuantityByAttributeReferences().get(null)).isEqualTo(0L);

        creditBuckets.get(0).setRemainingQuantity(500L);
        creditBuckets.get(1).setRemainingQuantity(300L);
        ratingInput = UsageInvoiceProcessor.buildDrawdownRatingInput(charge, 1000L, usagePeriod, creditBuckets, Optional.empty());
        Assertions.assertThat(creditBuckets.get(0).getRemainingQuantity()).isEqualTo(0L);
        Assertions.assertThat(creditBuckets.get(1).getRemainingQuantity()).isEqualTo(0L);
        Assertions.assertThat(ratingInput.getQuantityByAttributeReferences().get(null)).isEqualTo(200L);
    }

    @Test
    public void buildRatingInputFiltersDrawdownPeriodsCorrectly() {
        Charge charge = getPerUnitUsageCharge();
        charge.setDrawdown(true);

        Instant now = Instant.now();
        Instant oneYearAgo = DateTimeCalculator.minusMonths(TIME_ZONE.toZoneId(), now, 12);
        Instant sixMonthsAgo = DateTimeCalculator.minusMonths(TIME_ZONE.toZoneId(), now, 6);
        Period period = Period.between(oneYearAgo, now);
        Period period2 = Period.between(oneYearAgo, sixMonthsAgo);
        Period usagePeriod = Period.between(sixMonthsAgo, now);
        List<CreditBucket> creditBuckets = List.of(createCreditBucket(500L, period), createCreditBucket(300L, period2));

        RatingInput ratingInput = UsageInvoiceProcessor.buildDrawdownRatingInput(charge, 100L, usagePeriod, creditBuckets, Optional.empty());
        Assertions.assertThat(creditBuckets.get(0).getRemainingQuantity()).isEqualTo(400L);
        Assertions.assertThat(creditBuckets.get(1).getRemainingQuantity()).isEqualTo(300L);
        Assertions.assertThat(ratingInput.getQuantityByAttributeReferences().get(null)).isEqualTo(0L);

        creditBuckets.get(0).setRemainingQuantity(500L);
        creditBuckets.get(1).setRemainingQuantity(300L);
        ratingInput = UsageInvoiceProcessor.buildDrawdownRatingInput(charge, 500L, usagePeriod, creditBuckets, Optional.empty());
        Assertions.assertThat(creditBuckets.get(0).getRemainingQuantity()).isEqualTo(0L);
        Assertions.assertThat(creditBuckets.get(1).getRemainingQuantity()).isEqualTo(300L);
        Assertions.assertThat(ratingInput.getQuantityByAttributeReferences().get(null)).isEqualTo(0L);

        creditBuckets.get(0).setRemainingQuantity(500L);
        creditBuckets.get(1).setRemainingQuantity(300L);
        ratingInput = UsageInvoiceProcessor.buildDrawdownRatingInput(charge, 800L, usagePeriod, creditBuckets, Optional.empty());
        Assertions.assertThat(creditBuckets.get(0).getRemainingQuantity()).isEqualTo(0L);
        Assertions.assertThat(creditBuckets.get(1).getRemainingQuantity()).isEqualTo(300L);
        Assertions.assertThat(ratingInput.getQuantityByAttributeReferences().get(null)).isEqualTo(300L);
    }

    private static Charge getPerUnitUsageCharge() {
        return new MockChargeBuilder().withChargeType(ChargeType.USAGE).withChargeModel(ChargeModel.PER_UNIT).build();
    }

    private static CreditBucket createCreditBucket(long quantity, Period period) {
        return new CreditBucket(null, "id", null, null, quantity, quantity, period.getStart(), period.getEnd());
    }
}
