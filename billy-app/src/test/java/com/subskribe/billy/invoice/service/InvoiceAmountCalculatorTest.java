package com.subskribe.billy.invoice.service;

import static com.subskribe.billy.TestConstants.TIME_ZONE;
import static com.subskribe.billy.TestConstants.ZONE_ID;

import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.fixtures.OrderData;
import com.subskribe.billy.fixtures.ProductCatalogData;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.invoice.model.InvoiceItemAmounts;
import com.subskribe.billy.invoice.model.ListAmount;
import com.subskribe.billy.invoice.model.ListUnitPrice;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class InvoiceAmountCalculatorTest {

    private static final ZonedDateTime START_DATE = ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZONE_ID);

    private static final ZonedDateTime END_DATE = START_DATE.plusMonths(1);

    private static final Recurrence MONTHLY_RECURRENCE = new Recurrence(Cycle.MONTH, 1);
    private static final Recurrence YEARLY_RECURRENCE = new Recurrence(Cycle.YEAR, 1);

    @Mock
    private RateCardService rateCardService;

    @Mock
    private CurrencyConversionRateGetService currencyConversionRateGetService;

    private final FeatureService featureService = FeatureServiceFixture.allEnabled();

    private final DiscountCalculator discountCalculator = new DiscountCalculator(featureService);

    private InvoiceAmountCalculator invoiceAmountCalculator;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        invoiceAmountCalculator = new InvoiceAmountCalculator(rateCardService, currencyConversionRateGetService, discountCalculator, featureService);
    }

    @Test
    public void testMonthlyRecurringChargeWithMonthlyBilling() {
        var orderLineItem = OrderData.createOrderLineItem("", "", "", "", "", "", 10, START_DATE.toLocalDateTime(), END_DATE.toLocalDateTime());
        var charge = ProductCatalogData.createCharge(UUID.randomUUID(), UUID.randomUUID(), MONTHLY_RECURRENCE, BigDecimal.TEN);
        BillingPeriod billingPeriod = new BillingPeriod(START_DATE.toInstant(), END_DATE.toInstant(), END_DATE.toInstant(), MONTHLY_RECURRENCE);
        Period overlapPeriod = Period.between(START_DATE.toInstant(), END_DATE.toInstant());

        var amount = invoiceAmountCalculator
            .calculateRecurringItemListAmount(orderLineItem, charge, billingPeriod, overlapPeriod, TIME_ZONE, getProrationConfig())
            .getListAmount();

        // 10 units at $10 each
        Assertions.assertEquals(new BigDecimal("100.000"), amount.setScale(3, RoundingMode.HALF_UP));
    }

    @Test
    public void testYearlyRecurrenceChargeWithMonthlyBilling() {
        var orderLineItem = OrderData.createOrderLineItem("", "", "", "", "", "", 10, START_DATE.toLocalDateTime(), END_DATE.toLocalDateTime());
        var charge = ProductCatalogData.createCharge(UUID.randomUUID(), UUID.randomUUID(), YEARLY_RECURRENCE, BigDecimal.TEN);
        BillingPeriod billingPeriod = new BillingPeriod(START_DATE.toInstant(), END_DATE.toInstant(), END_DATE.toInstant(), MONTHLY_RECURRENCE);
        Period overlapPeriod = Period.between(START_DATE.toInstant(), END_DATE.toInstant());

        var amount = invoiceAmountCalculator
            .calculateRecurringItemListAmount(orderLineItem, charge, billingPeriod, overlapPeriod, TIME_ZONE, getProrationConfig())
            .getListAmount();

        // 10 units at $10 each
        Assertions.assertEquals(new BigDecimal("8.333"), amount.setScale(3, RoundingMode.HALF_UP));
    }

    @Test
    public void testMonthlyRecurringChargeWithYearlyBilling() {
        var orderLineItem = OrderData.createOrderLineItem("", "", "", "", "", "", 10, START_DATE.toLocalDateTime(), END_DATE.toLocalDateTime());
        var charge = ProductCatalogData.createCharge(UUID.randomUUID(), UUID.randomUUID(), MONTHLY_RECURRENCE, BigDecimal.TEN);
        BillingPeriod billingPeriod = new BillingPeriod(START_DATE.toInstant(), END_DATE.toInstant(), END_DATE.toInstant(), YEARLY_RECURRENCE);
        Period overlapPeriod = Period.between(START_DATE.toInstant(), END_DATE.toInstant());

        var amount = invoiceAmountCalculator
            .calculateRecurringItemListAmount(orderLineItem, charge, billingPeriod, overlapPeriod, TIME_ZONE, getProrationConfig())
            .getListAmount();

        // 10 units at $10 each scaled by 12
        Assertions.assertEquals(new BigDecimal("1200.000"), amount.setScale(3, RoundingMode.HALF_UP));
    }

    @Test
    public void testGetCustomListUnitPrice() {
        var charge = ProductCatalogData.createCustomPricingCharge(UUID.randomUUID(), UUID.randomUUID(), ChargeType.RECURRING, MONTHLY_RECURRENCE);
        var orderLineItem = OrderData.createOrderLineItem(
            "",
            "",
            "",
            "",
            "",
            charge.getChargeId(),
            10,
            START_DATE.toLocalDateTime(),
            END_DATE.toLocalDateTime()
        );
        orderLineItem.setListUnitPrice(new BigDecimal("20.000"));

        BigDecimal listUnitPrice = invoiceAmountCalculator.getListUnitPrice(charge, orderLineItem).getListUnitPrice().orElse(null);

        // custom charge only uses listUnitPrice
        Assertions.assertEquals(new BigDecimal("20.000"), listUnitPrice);
    }

    @Test
    public void testBasicItemCalculation() {
        BigDecimal listUnitPrice = new BigDecimal("10.00");
        BigDecimal listAmount = new BigDecimal("100.00");
        InvoiceItemAmounts itemAmounts = invoiceAmountCalculator.getInvoiceItemAmountsOld(List.of(), List.of(), listAmount, listUnitPrice, 10, null);

        Assertions.assertEquals(new BigDecimal("10.00000"), itemAmounts.getListUnitPrice());
        Assertions.assertNull(itemAmounts.getListUnitPriceBeforeOverride());
        Assertions.assertEquals(new BigDecimal("100.00"), itemAmounts.getListAmount());
        Assertions.assertNull(itemAmounts.getListAmountBeforeOverride());
        Assertions.assertEquals(new BigDecimal("100.00"), itemAmounts.getSellAmount());
    }

    @Test
    public void testBasicPriceOverride() {
        BigDecimal listUnitPrice = new BigDecimal("10.00");
        BigDecimal listAmount = new BigDecimal("100.00");
        InvoiceItemAmounts itemAmounts = invoiceAmountCalculator.getInvoiceItemAmountsOld(
            List.of(getDiscount("0.1")),
            List.of(),
            listAmount,
            listUnitPrice,
            10,
            new BigDecimal("1.5")
        );

        Assertions.assertEquals(new BigDecimal("15.00000"), itemAmounts.getListUnitPrice());
        Assertions.assertEquals(new BigDecimal("10.00000"), itemAmounts.getListUnitPriceBeforeOverride());
        Assertions.assertEquals(new BigDecimal("13.50000"), itemAmounts.getSellUnitPrice());
        Assertions.assertEquals(new BigDecimal("150.00"), itemAmounts.getListAmount());
        Assertions.assertEquals(new BigDecimal("100.00"), itemAmounts.getListAmountBeforeOverride());
        Assertions.assertEquals(new BigDecimal("135.00"), itemAmounts.getSellAmount());
    }

    @Test
    public void testRoundingArtifactWithOverride() {
        BigDecimal listUnitPrice = new BigDecimal("0.0013");
        BigDecimal listAmount = new BigDecimal("0.013");
        InvoiceItemAmounts itemAmounts = invoiceAmountCalculator.getInvoiceItemAmountsOld(
            List.of(),
            List.of(),
            listAmount,
            listUnitPrice,
            10,
            new BigDecimal("1.2")
        );

        Assertions.assertEquals(new BigDecimal("0.00156"), itemAmounts.getListUnitPrice());
        Assertions.assertEquals(new BigDecimal("0.00130"), itemAmounts.getListUnitPriceBeforeOverride());
        Assertions.assertEquals(new BigDecimal("0.02"), itemAmounts.getListAmount());
        Assertions.assertEquals(new BigDecimal("0.01"), itemAmounts.getListAmountBeforeOverride());
        Assertions.assertEquals(new BigDecimal("0.02"), itemAmounts.getSellAmount());
    }

    @Test
    public void testListAmountPrecisionWithPriceOverride() {
        BigDecimal listUnitPrice = new BigDecimal("0.00001");
        BigDecimal listAmount = new BigDecimal("10.00");
        InvoiceItemAmounts itemAmounts = invoiceAmountCalculator.getInvoiceItemAmountsOld(
            List.of(),
            List.of(),
            listAmount,
            listUnitPrice,
            100_000,
            new BigDecimal("1.1")
        );

        // Some precision can be lost with listUnitPrice as long as the listAmount is valid
        Assertions.assertEquals(new BigDecimal("0.00001"), itemAmounts.getListUnitPrice());
        Assertions.assertEquals(new BigDecimal("0.00001"), itemAmounts.getListUnitPriceBeforeOverride());
        Assertions.assertEquals(new BigDecimal("0.00001"), itemAmounts.getSellUnitPrice());
        Assertions.assertEquals(new BigDecimal("11.00"), itemAmounts.getListAmount());
        Assertions.assertEquals(new BigDecimal("10.00"), itemAmounts.getListAmountBeforeOverride());
        Assertions.assertEquals(new BigDecimal("11.00"), itemAmounts.getSellAmount());
    }

    @Test
    public void testListPriceOverrideRoundsProperly() {
        BigDecimal listUnitPrice = new BigDecimal("17.5");
        BigDecimal listAmount = new BigDecimal("17.5");
        InvoiceItemAmounts itemAmounts = invoiceAmountCalculator.getInvoiceItemAmountsOld(
            List.of(getDiscount("0.5")),
            List.of(),
            listAmount,
            listUnitPrice,
            1,
            new BigDecimal("1.14285714")
        );

        Assertions.assertEquals(new BigDecimal("20.00000"), itemAmounts.getListUnitPrice());
        Assertions.assertEquals(new BigDecimal("17.50000"), itemAmounts.getListUnitPriceBeforeOverride());
        Assertions.assertEquals(new BigDecimal("10.00000"), itemAmounts.getSellUnitPrice());
        Assertions.assertEquals(new BigDecimal("20.00"), itemAmounts.getListAmount());
        Assertions.assertEquals(new BigDecimal("17.50"), itemAmounts.getListAmountBeforeOverride());
        Assertions.assertEquals(new BigDecimal("10.00"), itemAmounts.getSellAmount());
    }

    @Test
    public void testBasicItemCalculationNew() {
        ListUnitPrice listUnitPrice = ListUnitPrice.of(Optional.of(new BigDecimal("10.00")));
        ListAmount listAmount = ListAmount.of(new BigDecimal("100.00"));
        InvoiceItemAmounts itemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(List.of(), List.of(), listAmount, listUnitPrice, 10, null);

        Assertions.assertEquals(new BigDecimal("10.00000"), itemAmounts.getListUnitPrice());
        Assertions.assertNull(itemAmounts.getListUnitPriceBeforeOverride());
        Assertions.assertEquals(new BigDecimal("100.00"), itemAmounts.getListAmount());
        Assertions.assertNull(itemAmounts.getListAmountBeforeOverride());
        Assertions.assertEquals(new BigDecimal("100.00"), itemAmounts.getSellAmount());
    }

    @Test
    public void testBasicPriceOverrideNew() {
        ListUnitPrice listUnitPrice = ListUnitPrice.of(Optional.of(new BigDecimal("10.00")));
        ListAmount listAmount = ListAmount.of(new BigDecimal("100.00"));
        InvoiceItemAmounts itemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(
            List.of(getDiscount("0.1")),
            List.of(),
            listAmount,
            listUnitPrice,
            10,
            new BigDecimal("1.5")
        );

        Assertions.assertEquals(new BigDecimal("15.00000"), itemAmounts.getListUnitPrice());
        Assertions.assertEquals(new BigDecimal("10.00000"), itemAmounts.getListUnitPriceBeforeOverride());
        Assertions.assertEquals(new BigDecimal("13.50000"), itemAmounts.getSellUnitPrice());
        Assertions.assertEquals(new BigDecimal("150.00"), itemAmounts.getListAmount());
        Assertions.assertEquals(new BigDecimal("100.00"), itemAmounts.getListAmountBeforeOverride());
        Assertions.assertEquals(new BigDecimal("135.00"), itemAmounts.getSellAmount());
    }

    @Test
    public void testRoundingArtifactWithOverrideNew() {
        ListUnitPrice listUnitPrice = ListUnitPrice.of(Optional.of(new BigDecimal("0.0013")));
        ListAmount listAmount = ListAmount.of(new BigDecimal("0.013"));
        InvoiceItemAmounts itemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(
            List.of(),
            List.of(),
            listAmount,
            listUnitPrice,
            10,
            new BigDecimal("1.2")
        );

        Assertions.assertEquals(new BigDecimal("0.00156"), itemAmounts.getListUnitPrice());
        Assertions.assertEquals(new BigDecimal("0.00130"), itemAmounts.getListUnitPriceBeforeOverride());
        Assertions.assertEquals(new BigDecimal("0.02"), itemAmounts.getListAmount());
        Assertions.assertEquals(new BigDecimal("0.01"), itemAmounts.getListAmountBeforeOverride());
        Assertions.assertEquals(new BigDecimal("0.02"), itemAmounts.getSellAmount());
    }

    @Test
    public void testListAmountPrecisionWithPriceOverrideNew() {
        ListUnitPrice listUnitPrice = ListUnitPrice.of(Optional.of(new BigDecimal("0.00001")));
        ListAmount listAmount = ListAmount.of(new BigDecimal("10.00"));
        InvoiceItemAmounts itemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(
            List.of(),
            List.of(),
            listAmount,
            listUnitPrice,
            100_000,
            new BigDecimal("1.1")
        );

        // Some precision can be lost with listUnitPrice as long as the listAmount is valid
        Assertions.assertEquals(new BigDecimal("0.00001"), itemAmounts.getListUnitPrice());
        Assertions.assertEquals(new BigDecimal("0.00001"), itemAmounts.getListUnitPriceBeforeOverride());
        Assertions.assertEquals(new BigDecimal("0.00001"), itemAmounts.getSellUnitPrice());
        Assertions.assertEquals(new BigDecimal("11.00"), itemAmounts.getListAmount());
        Assertions.assertEquals(new BigDecimal("10.00"), itemAmounts.getListAmountBeforeOverride());
        Assertions.assertEquals(new BigDecimal("11.00"), itemAmounts.getSellAmount());
    }

    @Test
    public void testListPriceOverrideRoundsProperlyNew() {
        ListUnitPrice listUnitPrice = ListUnitPrice.of(Optional.of(new BigDecimal("17.5")));
        ListAmount listAmount = ListAmount.of(new BigDecimal("17.5"));
        InvoiceItemAmounts itemAmounts = invoiceAmountCalculator.getInvoiceItemAmounts(
            List.of(getDiscount("0.5")),
            List.of(),
            listAmount,
            listUnitPrice,
            1,
            new BigDecimal("1.14285714")
        );

        Assertions.assertEquals(new BigDecimal("20.00000"), itemAmounts.getListUnitPrice());
        Assertions.assertEquals(new BigDecimal("17.50000"), itemAmounts.getListUnitPriceBeforeOverride());
        Assertions.assertEquals(new BigDecimal("10.00000"), itemAmounts.getSellUnitPrice());
        Assertions.assertEquals(new BigDecimal("20.00"), itemAmounts.getListAmount());
        Assertions.assertEquals(new BigDecimal("17.50"), itemAmounts.getListAmountBeforeOverride());
        Assertions.assertEquals(new BigDecimal("10.00"), itemAmounts.getSellAmount());
    }

    private DiscountDetail getDiscount(String ratio) {
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setPercent(new BigDecimal(ratio));
        return discountDetail;
    }

    private static ProrationConfig getProrationConfig() {
        return new ProrationConfig();
    }
}
