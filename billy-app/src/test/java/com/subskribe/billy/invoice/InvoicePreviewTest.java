package com.subskribe.billy.invoice;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.model.ImmutableCurrencyConversionRate;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateService;
import com.subskribe.billy.invoice.db.InvoiceConfigDAO;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.number.InvoiceNumberGenerator;
import com.subskribe.billy.invoice.number.RandomNumberGenerator;
import com.subskribe.billy.invoice.number.SequenceInvoiceNumberGenerator;
import com.subskribe.billy.invoice.service.DiscountCalculator;
import com.subskribe.billy.invoice.service.InvoiceAmountCalculator;
import com.subskribe.billy.invoice.service.InvoiceConfigurationService;
import com.subskribe.billy.invoice.service.InvoiceDocumentGenerationTrackerService;
import com.subskribe.billy.invoice.service.InvoiceEventService;
import com.subskribe.billy.invoice.service.InvoiceServiceInternal;
import com.subskribe.billy.invoice.service.PercentOfChargeInvoiceHelper;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.invoice.service.RatingService;
import com.subskribe.billy.invoice.service.processor.OneTimeInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PercentOfInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PrepaidInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.RecurringInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.UsageInvoiceProcessor;
import com.subskribe.billy.invoice.tax.model.TaxCalculationInput;
import com.subskribe.billy.invoice.tax.model.TaxTransaction;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.order.EvergreenUtils;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.model.CustomBillingPeriod;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.ImmutableCustomBillingPeriod;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderSource;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.order.services.OrderCustomBillingService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.payment.processor.service.PaymentProcessorJobQueueService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PriceTier;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.revrec.services.RevenueRecognitionJobService;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.contactfetcher.ContactGroupOrUserFetcher;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.pecuniary.TenantDiscount;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.model.SubscriptionState;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionImpl;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.test.utilities.ChargeUtils;
import com.subskribe.billy.test.utilities.MockChargeBuilder;
import com.subskribe.billy.usage.service.UsageStatisticsService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Month;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Currency;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;

public class InvoicePreviewTest {

    private final OrderGetService mockOrderGetService = mock(OrderGetService.class);
    private final ProductCatalogGetService mockProductCatalogGetService = mock(ProductCatalogGetService.class);
    private final SubscriptionGetService mockSubscriptionGetService = mock(SubscriptionGetService.class);
    private final AccountGetService mockAccountGetService = mock(AccountGetService.class);
    private final InvoiceDAO mockInvoiceDao = mock(InvoiceDAO.class);
    private final OrderDAO mockOrderDAO = mock(OrderDAO.class);
    private final InvoiceConfigDAO mockInvoiceConfigDAO = mock(InvoiceConfigDAO.class);
    private final TenantIdProvider mockTenantIdProvider = mock(TenantIdProvider.class);
    private final DSLContextProvider mockDslContextProvider = mock(DSLContextProvider.class);
    private final UsageStatisticsService mockUsageStatisticsService = mock(UsageStatisticsService.class);
    private final InvoiceDocumentGenerationTrackerService mockDocumentGenerationTrackerService = mock(InvoiceDocumentGenerationTrackerService.class);
    private final TaxRateGetService mockTaxRateGetService = mock(TaxRateGetService.class);
    private final TaxService mockTaxService = mock(TaxService.class);
    private final CacheService mockCacheService = mock(CacheService.class);
    private final TenantSettingService tenantSettingService = TenantSettingServiceFixture.getDefault();
    private final PaymentProcessorJobQueueService mockPaymentProcessorJobQueue = mock(PaymentProcessorJobQueueService.class);
    private final PercentOfChargeInvoiceHelper mockPercentOfChargeHelper = mock(PercentOfChargeInvoiceHelper.class);
    private final PercentOfInvoiceProcessor mockPercentOfProcessor = mock(PercentOfInvoiceProcessor.class);
    private final CustomFieldService mockCustomFieldService = mock(CustomFieldService.class);

    private final AccountingPeriodService MOCK_ACCOUNTING_PERIOD_SERVICE = mock(AccountingPeriodService.class);

    private final InvoiceEventService invoiceEventService = mock(InvoiceEventService.class);
    private final RevenueRecognitionJobService mockRevenueRecognitionJobService = mock(RevenueRecognitionJobService.class);
    private final EmailContactListService emailContactListService = mock(EmailContactListService.class);
    private final ContactGroupOrUserFetcher contactGroupOrUserFetcher = mock(ContactGroupOrUserFetcher.class);
    private final FeatureService mockFeatureService = mock(FeatureService.class);
    private final EntityGetService mockEntityGetService = EntityGetServiceFixture.entityGetServiceFixture();
    private final RateCardService rateCardService = mock(RateCardService.class);
    private final RatingService ratingService = mock(RatingService.class);

    private final ReportingJobQueueService reportingJobQueueService = mock(ReportingJobQueueService.class);

    private final CurrencyConversionRateGetService mockCurrencyConversionRateGetService = mock(CurrencyConversionRateGetService.class);

    private final DiscountCalculator discountCalculator = new DiscountCalculator(mockFeatureService);

    private static final String SUBSCRIPTION_ID = UUID.randomUUID().toString();
    private static final String EXTERNAL_ID = UUID.randomUUID().toString();
    private static final String TEST_PLAN_ID = "test_plan_id";
    private static final String TEST_PRODUCT_ID = "PROD-BALROG";
    private static final String TEST_PRODUCT_SKU = "SKU-SARUMAN";
    private static final String CHARGE_ID = "CHRG-1234567";
    private static final String ORDER_ID = UUID.randomUUID().toString();
    private static final String ACCOUNT_ID = UUID.randomUUID().toString();
    private static final String SHIPPING_CONTACT_ID = UUID.randomUUID().toString();
    private static final String BILLING_CONTACT_ID = UUID.randomUUID().toString();
    private static final TenantId TENANT_ID = new TenantId("test_tenant_id");
    private static final String ENTITY_ID = "test_entity_id";
    private static final int BILLING_CYCLES = 12;
    private static final BigDecimal UNIT_PRICE = new BigDecimal("15.35");
    private static final long QUANTITY = 10;
    private static final BigDecimal AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_PER_UNIT = new BigDecimal("153.50");
    private static final BigDecimal AMOUNT_FOR_MONTHLY_BILLING_YEARLY_PER_UNIT = new BigDecimal("12.79");
    private static final List<PriceTier> PRICE_TIERS = List.of(
        PriceTier.of(3L, BigDecimal.valueOf(10)),
        PriceTier.of(6L, BigDecimal.valueOf(8)),
        PriceTier.of(12L, BigDecimal.valueOf(6)),
        PriceTier.of(null, BigDecimal.valueOf(4))
    );
    private static final BigDecimal AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_BLOCK = new BigDecimal("6.00");
    private static final BigDecimal AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_VOLUME = new BigDecimal("60.00");
    private static final BigDecimal AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_TIERED = new BigDecimal("78.00");
    private static final BigDecimal AMOUNT_FOR_MONTHLY_BILLING_YEARLY_BLOCK = new BigDecimal("0.50");
    private static final BigDecimal AMOUNT_FOR_MONTHLY_BILLING_YEARLY_VOLUME = new BigDecimal("5.00");
    private static final BigDecimal AMOUNT_FOR_MONTHLY_BILLING_YEARLY_TIERED = new BigDecimal("6.50");
    private static final BigDecimal AMOUNT_FOR_YEARLY_BILLING_MONTHLY_BLOCK = new BigDecimal("72.00");
    private static final BigDecimal AMOUNT_FOR_YEARLY_BILLING_MONTHLY_VOLUME = new BigDecimal("720.00");
    private static final BigDecimal AMOUNT_FOR_YEARLY_BILLING_MONTHLY_TIERED = new BigDecimal("936.00");
    private static final LocalDate JAN_1_2020 = LocalDate.of(2020, Month.JANUARY, 1);
    private static final Instant START_DATE = getInstant(JAN_1_2020);
    private static final Instant END_DATE = getInstant(JAN_1_2020.plusMonths(BILLING_CYCLES));
    private static final Recurrence TERM_LENGTH = new Recurrence(Cycle.MONTH, BILLING_CYCLES);
    private static final String ORDER_LINE_ITEM_ID = UUID.randomUUID().toString();
    private static final Currency TEST_CURRENCY = Currency.getInstance(Locale.US);

    private static final BigDecimal CONVERSION_RATE = BigDecimal.valueOf(1.51);
    private static final CurrencyConversionRate currencyConversionRate = ImmutableCurrencyConversionRate.builder()
        .fromCurrency("USD")
        .toCurrency("AUD")
        .conversionRate(CONVERSION_RATE)
        .effectiveDate(1612137600000L)
        .roundingTreatment(RoundingMode.UNNECESSARY)
        .build();

    private final ProrationConfigurationGetService prorationConfigurationGetService = mock(ProrationConfigurationGetService.class);

    private final TaxTransaction mockTaxTransaction = TaxTransaction.builder()
        .transactionCode("dummy")
        .computedTaxByLineNo(Map.of())
        .totalTax(BigDecimal.ZERO)
        .build();
    private final InvoiceConfigurationService invoiceConfigurationService = new InvoiceConfigurationService(
        mockInvoiceDao,
        mockInvoiceConfigDAO,
        mockCacheService
    );

    private InvoiceServiceInternal invoiceService;

    @BeforeEach
    void beforeEach() {
        when(mockAccountGetService.getAccount(ACCOUNT_ID)).thenReturn(getTestAccount());
        when(mockAccountGetService.getContact(SHIPPING_CONTACT_ID)).thenReturn(getTestContact());

        // Return the same invoice sent to the DAO
        when(mockInvoiceDao.addInvoice(any())).then(i -> i.getArgument(0));
        when(mockTenantIdProvider.provide()).thenReturn(TENANT_ID);
        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID.getRequiredId());

        //NOTE: no tax testing is happening here, so we can return a dummy TaxTransaction
        when(mockTaxService.createTaxTransaction(any(TaxCalculationInput.class))).thenReturn(Optional.of(mockTaxTransaction));

        // setup for tax line item
        setupMockProductGet();
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, getRecurrence(Cycle.MONTH))
        );
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(START_DATE, END_DATE));

        when(prorationConfigurationGetService.resolveProrationConfig(any(Subscription.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT)
        );
        when(prorationConfigurationGetService.resolveProrationConfig(any(Order.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT)
        );

        when(MOCK_ACCOUNTING_PERIOD_SERVICE.getCurrentAccountingPeriod()).thenReturn(Optional.empty());

        when(mockFeatureService.isEnabled(Feature.FLEXIBLE_BILLING_ANCHOR_DATE)).thenReturn(false);
        when(mockFeatureService.isEnabled(Feature.NEGOTIATED_PRICE_TIERS)).thenReturn(true);

        when(mockCurrencyConversionRateGetService.getCurrencyConversionRateById(any())).thenReturn(Optional.of(currencyConversionRate));

        var subscriptionBillingPeriodService = new SubscriptionBillingPeriodService(
            mockSubscriptionGetService,
            tenantSettingService,
            mockFeatureService,
            prorationConfigurationGetService
        );

        InvoiceAmountCalculator invoiceAmountCalculator = new InvoiceAmountCalculator(
            rateCardService,
            mockCurrencyConversionRateGetService,
            discountCalculator,
            mockFeatureService
        );

        RecurringInvoiceProcessor recurringInvoiceProcessor = new RecurringInvoiceProcessor(
            invoiceAmountCalculator,
            subscriptionBillingPeriodService,
            mockFeatureService,
            mockInvoiceDao
        );

        OneTimeInvoiceProcessor oneTimeInvoiceProcessor = new OneTimeInvoiceProcessor(
            mockSubscriptionGetService,
            mockInvoiceDao,
            invoiceAmountCalculator,
            mockFeatureService
        );

        PrepaidInvoiceProcessor prepaidInvoiceProcessor = new PrepaidInvoiceProcessor(
            mockInvoiceDao,
            invoiceAmountCalculator,
            subscriptionBillingPeriodService
        );

        UsageInvoiceProcessor usageInvoiceProcessor = new UsageInvoiceProcessor(
            subscriptionBillingPeriodService,
            invoiceAmountCalculator,
            mockFeatureService,
            recurringInvoiceProcessor,
            prepaidInvoiceProcessor,
            mockInvoiceDao,
            mockUsageStatisticsService,
            ratingService,
            mockCurrencyConversionRateGetService
        );

        OrderCustomBillingService orderCustomBillingService = new OrderCustomBillingService(
            mockFeatureService,
            mockProductCatalogGetService,
            subscriptionBillingPeriodService,
            tenantSettingService,
            mockOrderDAO
        );

        invoiceService = new InvoiceServiceInternal(
            mockOrderGetService,
            mockSubscriptionGetService,
            mockAccountGetService,
            mockProductCatalogGetService,
            mockTenantIdProvider,
            mockInvoiceDao,
            new InvoiceNumberGenerator(
                mockInvoiceDao,
                invoiceConfigurationService,
                mockEntityGetService,
                new RandomNumberGenerator(),
                new SequenceInvoiceNumberGenerator(mockInvoiceDao)
            ),
            mockDslContextProvider,
            mockTaxService,
            mockTaxRateGetService,
            mockDocumentGenerationTrackerService,
            tenantSettingService,
            mockPercentOfChargeHelper,
            mockPercentOfProcessor,
            mockPaymentProcessorJobQueue,
            invoiceEventService,
            subscriptionBillingPeriodService,
            prorationConfigurationGetService,
            mockRevenueRecognitionJobService,
            MOCK_ACCOUNTING_PERIOD_SERVICE,
            emailContactListService,
            contactGroupOrUserFetcher,
            mockFeatureService,
            recurringInvoiceProcessor,
            oneTimeInvoiceProcessor,
            prepaidInvoiceProcessor,
            usageInvoiceProcessor,
            reportingJobQueueService,
            Clock.systemUTC(),
            mock(TransactionalExchangeRateService.class),
            orderCustomBillingService,
            mockEntityGetService,
            mockCustomFieldService
        );

        when(mockSubscriptionGetService.getSubscriptionChargeBySubscriptionChargeId(any())).thenReturn(getTestSubscriptionCharge());
    }

    @Test
    public void testMonthlyBillingForMonthlyPerUnit() {
        var monthlyPerUnitCharge = getRecurringPerUnitCharge(getRecurrence(Cycle.MONTH));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(monthlyPerUnitCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(BILLING_CYCLES);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_PER_UNIT);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_PER_UNIT);
        });
    }

    @Test
    public void testMonthlyBillingForYearlyPerUnit() {
        var yearlyPerUnitCharge = getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(yearlyPerUnitCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(BILLING_CYCLES);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_YEARLY_PER_UNIT);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_YEARLY_PER_UNIT);
        });
    }

    @Test
    public void testMonthlyBillingForMonthlyBlock() {
        var monthlyBlockCharge = getRecurringBlockCharge(getRecurrence(Cycle.MONTH));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(monthlyBlockCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(BILLING_CYCLES);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_BLOCK);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_BLOCK);
        });
    }

    @Test
    public void testMonthlyBillingForMonthlyVolume() {
        var monthlyVolumeCharge = getRecurringVolumeCharge(getRecurrence(Cycle.MONTH));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(monthlyVolumeCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(BILLING_CYCLES);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_VOLUME);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_VOLUME);
        });
    }

    @Test
    public void testMonthlyBillingForMonthlyTiered() {
        var monthlyTieredCharge = getRecurringTieredCharge(getRecurrence(Cycle.MONTH));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(monthlyTieredCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(BILLING_CYCLES);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_TIERED);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_MONTHLY_TIERED);
        });
    }

    @Test
    public void testMonthlyBillingForYearlyBlock() {
        var yearlyBlockCharge = getRecurringBlockCharge(getRecurrence(Cycle.YEAR));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(yearlyBlockCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(BILLING_CYCLES);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_YEARLY_BLOCK);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_YEARLY_BLOCK);
        });
    }

    @Test
    public void testMonthlyBillingForYearlyVolume() {
        var yearlyVolumeCharge = getRecurringVolumeCharge(getRecurrence(Cycle.YEAR));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(yearlyVolumeCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(BILLING_CYCLES);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_YEARLY_VOLUME);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_YEARLY_VOLUME);
        });
    }

    @Test
    public void testMonthlyBillingForYearlyTiered() {
        var yearlyTieredCharge = getRecurringTieredCharge(getRecurrence(Cycle.YEAR));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(yearlyTieredCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(BILLING_CYCLES);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_YEARLY_TIERED);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_MONTHLY_BILLING_YEARLY_TIERED);
        });
    }

    @Test
    public void testYearlyBillingForMonthlyBlock() {
        var monthlyBlockCharge = getRecurringBlockCharge(getRecurrence(Cycle.MONTH));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.YEAR)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(monthlyBlockCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(1);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_YEARLY_BILLING_MONTHLY_BLOCK);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_YEARLY_BILLING_MONTHLY_BLOCK);
        });
    }

    @Test
    public void testYearlyBillingForMonthlyVolume() {
        var monthlyVolumeCharge = getRecurringVolumeCharge(getRecurrence(Cycle.MONTH));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.YEAR)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(monthlyVolumeCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(1);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_YEARLY_BILLING_MONTHLY_VOLUME);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_YEARLY_BILLING_MONTHLY_VOLUME);
        });
    }

    @Test
    public void testYearlyBillingForMonthlyTiered() {
        var monthlyTieredCharge = getRecurringTieredCharge(getRecurrence(Cycle.MONTH));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.YEAR)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(monthlyTieredCharge))
        );

        // Generate Previews
        List<InvoicePreview> invoicePreviews = invoiceService.previewInvoicesByBillingPeriods(ORDER_ID);
        assertThat(invoicePreviews.size()).isEqualTo(1);

        invoicePreviews.forEach(invoicePreview -> {
            int generatedInvoiceItemSize = invoicePreview.getInvoiceItems().size();
            assertThat(generatedInvoiceItemSize).isEqualTo(1);
            BigDecimal generatedInvoiceAmount = invoicePreview.getTotal();
            BigDecimal generatedInvoiceItemAmount = invoicePreview.getInvoiceItems().get(0).getAmount();
            assertThat(generatedInvoiceAmount).isEqualTo(AMOUNT_FOR_YEARLY_BILLING_MONTHLY_TIERED);
            assertThat(generatedInvoiceItemAmount).isEqualTo(AMOUNT_FOR_YEARLY_BILLING_MONTHLY_TIERED);
        });
    }

    @Test
    public void testNullChargeRecurrencePreviewForPrepaid() {
        Charge prepaidCharge = getPrepaidCharge(BigDecimal.TEN, null);

        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.YEAR)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(prepaidCharge))
        );

        // Generate Preview
        var invoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);
        BigDecimal generatedInvoiceItemAmount = invoicePreview.getTotal();
        // 10 * $10 = $100
        assertThat(generatedInvoiceItemAmount).isEqualTo(new BigDecimal("100.00"));
    }

    @Test
    public void testNullChargeRecurrencePreviewForPrepaidNonAligning() {
        Charge prepaidCharge = getPrepaidCharge(BigDecimal.TEN, null);

        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, getInstant(LocalDate.of(2021, Month.JULY, 1)), OrderStatus.DRAFT, getRecurrence(Cycle.YEAR)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(prepaidCharge))
        );

        // Generate Preview
        var invoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);
        BigDecimal generatedInvoiceItemAmount = invoicePreview.getTotal();
        // 10 * $10 = $100
        assertThat(generatedInvoiceItemAmount).isEqualTo(new BigDecimal("100.00"));
    }

    @Test
    public void testQuarterlyChargeRecurrencePreviewForPrepaid() {
        Charge prepaidCharge = getPrepaidCharge(BigDecimal.TEN, getRecurrence(Cycle.QUARTER));

        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.YEAR)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(prepaidCharge))
        );

        // Generate Preview
        var invoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);
        BigDecimal generatedInvoiceItemAmount = invoicePreview.getTotal();
        // 10 * $10  * 4 quarters = $400
        assertThat(generatedInvoiceItemAmount).isEqualTo(new BigDecimal("400.00"));
    }

    @Test
    public void testYearlyChargeRecurrencePreviewForPrepaidWithIncompletePeriod() {
        Charge prepaidCharge = getPrepaidCharge(BigDecimal.TEN, getRecurrence(Cycle.YEAR));

        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, getInstant(LocalDate.of(2021, Month.JULY, 1)), OrderStatus.DRAFT, getRecurrence(Cycle.YEAR)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(prepaidCharge))
        );

        // Generate Preview
        var invoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);
        BigDecimal generatedInvoiceItemAmount = invoicePreview.getTotal();
        // 10 * $10  * 2 years = $200
        assertThat(generatedInvoiceItemAmount).isEqualTo(new BigDecimal("200.00"));
    }

    @Test
    public void testYearlyChargeRecurrencePreviewForPrepaidWithPartialOverlaps() {
        Charge prepaidCharge = getPrepaidCharge(BigDecimal.TEN, getRecurrence(Cycle.YEAR));

        OrderLineItem orderLineItem = getOrderLineItem(
            ORDER_ID,
            getInstant(LocalDate.of(2020, Month.JULY, 1)),
            getInstant(LocalDate.of(2021, Month.JULY, 1)),
            Collections.emptyMap()
        );
        Order testOrder = getOrderDetails(
            ORDER_ID,
            List.of(orderLineItem),
            START_DATE,
            getInstant(LocalDate.of(2021, Month.JULY, 1)),
            OrderStatus.DRAFT,
            getRecurrence(Cycle.YEAR),
            Collections.emptyMap()
        );
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(testOrder);
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(prepaidCharge))
        );

        // Generate Preview
        var invoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);
        BigDecimal generatedInvoiceItemAmount = invoicePreview.getTotal();
        // 10 * $10  * 2 years = $200
        assertThat(generatedInvoiceItemAmount).isEqualTo(new BigDecimal("200.00"));
    }

    @Test
    public void testYearlyChargeRecurrencePreviewForPrepaidWithBillingAnchorDate() {
        Charge prepaidCharge = getPrepaidCharge(BigDecimal.TEN, getRecurrence(Cycle.YEAR));

        OrderLineItem orderLineItem = getOrderLineItem(
            ORDER_ID,
            START_DATE,
            getInstant(LocalDate.of(2022, Month.JANUARY, 1)),
            Collections.emptyMap()
        );
        Order testOrder = getOrderDetails(
            ORDER_ID,
            List.of(orderLineItem),
            START_DATE,
            getInstant(LocalDate.of(2022, Month.JANUARY, 1)),
            OrderStatus.DRAFT,
            getRecurrence(Cycle.YEAR),
            Collections.emptyMap()
        );
        testOrder.setBillingAnchorDate(getInstant(LocalDate.of(2020, Month.JULY, 1)));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(testOrder);
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(prepaidCharge))
        );
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenThrow(
            new ObjectNotFoundException(BillyObjectType.SUBSCRIPTION, SUBSCRIPTION_ID)
        );

        // Generate Preview
        var invoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);
        BigDecimal generatedInvoiceItemAmount = invoicePreview.getTotal();
        // 10 * $10  * 3 billing cycles = $300
        assertThat(generatedInvoiceItemAmount).isEqualTo(new BigDecimal("300.00"));
    }

    @Test
    public void testNullChargeRecurrencePreviewForPrepaidWithBillingAnchorDate() {
        Charge prepaidCharge = getPrepaidCharge(BigDecimal.TEN, null);

        OrderLineItem orderLineItem = getOrderLineItem(
            ORDER_ID,
            START_DATE,
            getInstant(LocalDate.of(2022, Month.JANUARY, 1)),
            Collections.emptyMap()
        );
        Order testOrder = getOrderDetails(
            ORDER_ID,
            List.of(orderLineItem),
            START_DATE,
            getInstant(LocalDate.of(2022, Month.JANUARY, 1)),
            OrderStatus.DRAFT,
            getRecurrence(Cycle.YEAR),
            Collections.emptyMap()
        );
        testOrder.setBillingAnchorDate(getInstant(LocalDate.of(2020, Month.JULY, 1)));
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(testOrder);
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(prepaidCharge))
        );
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenThrow(
            new ObjectNotFoundException(BillyObjectType.SUBSCRIPTION, SUBSCRIPTION_ID)
        );

        // Generate Preview
        var invoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);
        BigDecimal generatedInvoiceItemAmount = invoicePreview.getTotal();
        // 10 * $10 = $100
        assertThat(generatedInvoiceItemAmount).isEqualTo(new BigDecimal("100.00"));
    }

    @Test
    public void testYearlyChargeRecurrencePreviewForRecurringWithCustomBilling() {
        Charge prepaidCharge = getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR));

        OrderLineItem orderLineItem = getOrderLineItem(
            ORDER_ID,
            START_DATE,
            getInstant(LocalDate.of(2022, Month.JANUARY, 1)),
            Collections.emptyMap()
        );
        Order testOrder = getOrderDetails(
            ORDER_ID,
            List.of(orderLineItem),
            START_DATE,
            getInstant(LocalDate.of(2022, Month.JANUARY, 1)),
            OrderStatus.DRAFT,
            getRecurrence(Cycle.CUSTOM),
            Collections.emptyMap()
        );
        testOrder.setBillingCycle(new Recurrence(Cycle.CUSTOM, 1));
        testOrder.setExternalSubscriptionId("SUB-1234567");
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(testOrder);
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(prepaidCharge))
        );
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenThrow(
            new ObjectNotFoundException(BillyObjectType.SUBSCRIPTION, SUBSCRIPTION_ID)
        );

        CustomBillingPeriod customBillingPeriod1 = ImmutableCustomBillingPeriod.builder()
            .amount(BigDecimal.valueOf(100))
            .triggerInstant(getInstant(LocalDate.of(2021, Month.SEPTEMBER, 1)))
            .recurrenceWithCount(new CustomBillingSchedule.CustomBillingRecurrence(new RecurrenceJson(Cycle.PAID_IN_FULL, 1), 1))
            .build();
        CustomBillingPeriod customBillingPeriod2 = ImmutableCustomBillingPeriod.builder()
            .amount(BigDecimal.valueOf(53.5))
            .triggerInstant(getInstant(LocalDate.of(2021, Month.NOVEMBER, 1)))
            .recurrenceWithCount(new CustomBillingSchedule.CustomBillingRecurrence(new RecurrenceJson(Cycle.PAID_IN_FULL, 1), 1))
            .build();
        CustomBillingSchedule customBillingSchedule = new CustomBillingSchedule(
            UUID.randomUUID().toString(),
            CustomBillingSchedule.Version.V1,
            testOrder.getOrderId(),
            List.of(orderLineItem.getOrderLineId()),
            List.of(customBillingPeriod1, customBillingPeriod2),
            false,
            Instant.now().getEpochSecond(),
            Instant.now().getEpochSecond()
        );
        testOrder.setCustomBillingSchedule(customBillingSchedule);
        when(mockOrderGetService.getCustomBillingScheduleForOrder(ORDER_ID, true)).thenReturn(customBillingSchedule);
        when(mockFeatureService.isEnabled(Feature.CUSTOM_BILLING)).thenReturn(true);
        when(mockSubscriptionGetService.getSubscription("SUB-1234567")).thenReturn(
            getTestSubscription(START_DATE, getInstant(LocalDate.of(2022, Month.JANUARY, 1)))
        );
        orderLineItem.setSubscriptionChargeGroupId("SUB-1234567");
        // Generate Preview
        var invoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);
        var invoicePreviewForOrderLineCalculation = invoiceService.previewInvoiceByOrderPeriodForOrderLinesCalculation(testOrder);
        BigDecimal generatedInvoiceItemAmount = invoicePreview.getTotal();
        // Invoice amount = sell price 15.35 * 10 = 153.50
        assertThat(generatedInvoiceItemAmount).isEqualTo(new BigDecimal("153.50"));
        // Order line amount = 30.70* 10 = 307.00
        assertThat(invoicePreviewForOrderLineCalculation.getTotal()).isEqualTo(new BigDecimal("307.00"));
    }

    @Test
    public void testMonthlyChargePreviewByMonthlyBillingCycle() {
        Charge monthlyPerUnitCharge = getRecurringPerUnitCharge(getRecurrence(Cycle.MONTH));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(monthlyPerUnitCharge))
        );

        Order order = getTestEvergreenOrders(START_DATE, START_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0);
        InvoicePreview invoicePreview = invoiceService.previewInvoiceByOrderBillingCycle(order);
        assertThat(invoicePreview.getTotal()).isEqualTo(new BigDecimal("153.50"));

        Order order2 = getTestEvergreenOrders(START_DATE, START_DATE.plus(15, ChronoUnit.DAYS), OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0);
        InvoicePreview invoicePreview2 = invoiceService.previewInvoiceByOrderBillingCycle(order2);
        assertThat(invoicePreview2.getTotal()).isEqualTo(new BigDecimal("153.50"));

        Order order3 = getTestEvergreenOrders(START_DATE.plus(15, ChronoUnit.DAYS), START_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0);
        InvoicePreview invoicePreview3 = invoiceService.previewInvoiceByOrderBillingCycle(order3);
        assertThat(invoicePreview3.getTotal()).isEqualTo(new BigDecimal("153.50"));
    }

    @Test
    public void testYearlyChargePreviewByMonthlyBillingCycle() {
        Charge yearlyPerUnitCharge = getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(yearlyPerUnitCharge))
        );

        Order order = getTestEvergreenOrders(START_DATE, START_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0);
        InvoicePreview invoicePreview = invoiceService.previewInvoiceByOrderBillingCycle(order);
        assertThat(invoicePreview.getTotal()).isEqualTo(new BigDecimal("12.79"));

        Order order2 = getTestEvergreenOrders(START_DATE, START_DATE.plus(15, ChronoUnit.DAYS), OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0);
        InvoicePreview invoicePreview2 = invoiceService.previewInvoiceByOrderBillingCycle(order2);
        assertThat(invoicePreview2.getTotal()).isEqualTo(new BigDecimal("12.79"));

        Order order3 = getTestEvergreenOrders(START_DATE.plus(15, ChronoUnit.DAYS), START_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0);
        InvoicePreview invoicePreview3 = invoiceService.previewInvoiceByOrderBillingCycle(order3);
        assertThat(invoicePreview3.getTotal()).isEqualTo(new BigDecimal("12.79"));
    }

    @Test
    public void testMonthlyChargePreviewByYearlyBillingCycle() {
        Charge monthlyPerUnitCharge = getRecurringPerUnitCharge(getRecurrence(Cycle.MONTH));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(monthlyPerUnitCharge))
        );

        Order order = getTestEvergreenOrders(START_DATE, START_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.YEAR)).get(0);
        InvoicePreview invoicePreview = invoiceService.previewInvoiceByOrderBillingCycle(order);
        assertThat(invoicePreview.getTotal()).isEqualTo(new BigDecimal("1842.00"));

        Order order2 = getTestEvergreenOrders(START_DATE, START_DATE.plus(15, ChronoUnit.DAYS), OrderStatus.DRAFT, getRecurrence(Cycle.YEAR)).get(0);
        InvoicePreview invoicePreview2 = invoiceService.previewInvoiceByOrderBillingCycle(order2);
        assertThat(invoicePreview2.getTotal()).isEqualTo(new BigDecimal("1842.00"));

        Order order3 = getTestEvergreenOrders(START_DATE.plus(15, ChronoUnit.DAYS), START_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.YEAR)).get(0);
        InvoicePreview invoicePreview3 = invoiceService.previewInvoiceByOrderBillingCycle(order3);
        assertThat(invoicePreview3.getTotal()).isEqualTo(new BigDecimal("1842.00"));
    }

    private Account getTestAccount() {
        var account = new Account();
        account.setAccountId(ACCOUNT_ID);
        return account;
    }

    private AccountContact getTestContact() {
        var contact = new AccountContact();
        contact.setContactId(SHIPPING_CONTACT_ID);
        contact.setAddress(new AccountAddress());
        return contact;
    }

    // todo: reuse the test fixture methods from invoice service test
    private Subscription getTestSubscription(Instant startDate, Instant endDate, Recurrence billingCycle) {
        var subscriptionEntity = new SubscriptionEntity(
            UUID.randomUUID(),
            SUBSCRIPTION_ID,
            EXTERNAL_ID,
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            ACCOUNT_ID,
            null,
            SHIPPING_CONTACT_ID,
            BILLING_CONTACT_ID,
            Currency.getInstance("USD"),
            PaymentTerm.NET30,
            SubscriptionState.ACTIVE,
            startDate,
            endDate,
            startDate,
            null,
            TERM_LENGTH,
            billingCycle,
            BillingTerm.UP_FRONT,
            null,
            null,
            List.of(ORDER_ID),
            null,
            false,
            false,
            startDate,
            Instant.now(),
            1,
            START_DATE,
            END_DATE,
            SubscriptionDurationModel.TERMED
        );
        return new SubscriptionImpl(subscriptionEntity, List.of(getTestSubscriptionCharge()));
    }

    private Subscription getTestSubscription(Instant startDate, Instant endDate) {
        return getTestSubscription(startDate, endDate, getRecurrence(Cycle.MONTH));
    }

    // todo: reuse the test fixture methods from invoice service test
    private SubscriptionCharge getTestSubscriptionCharge() {
        return new SubscriptionCharge(
            UUID.randomUUID(),
            CHARGE_ID,
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            SUBSCRIPTION_ID,
            TENANT_ID.getRequiredId(),
            ACCOUNT_ID,
            UUID.randomUUID(),
            null,
            START_DATE,
            END_DATE,
            1,
            false,
            null,
            null,
            null,
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ZERO,
            null,
            null,
            null,
            1,
            START_DATE,
            END_DATE,
            0,
            null
        );
    }

    private Charge getRecurringPerUnitCharge(Recurrence recurrence) {
        return new MockChargeBuilder().recurring(recurrence).withChargeId(CHARGE_ID).withAmount(UNIT_PRICE).build();
    }

    private Charge getChargeWithTiers(ChargeType chargeType, ChargeModel chargeModel, Recurrence recurrence) {
        return new MockChargeBuilder()
            .withChargeId(CHARGE_ID)
            .withChargeType(chargeType)
            .withChargeModel(chargeModel)
            .recurring(recurrence)
            .withPriceTiers(PRICE_TIERS)
            .build();
    }

    private Charge getRecurringTieredCharge(Recurrence recurrence) {
        return getChargeWithTiers(ChargeType.RECURRING, ChargeModel.TIERED, recurrence);
    }

    private Charge getRecurringVolumeCharge(Recurrence recurrence) {
        return getChargeWithTiers(ChargeType.RECURRING, ChargeModel.VOLUME, recurrence);
    }

    private Charge getRecurringBlockCharge(Recurrence recurrence) {
        return getChargeWithTiers(ChargeType.RECURRING, ChargeModel.BLOCK, recurrence);
    }

    private Charge getPrepaidCharge(BigDecimal amount, Recurrence recurrence) {
        return new MockChargeBuilder()
            .recurring(recurrence)
            .withChargeType(ChargeType.PREPAID)
            .withChargeModel(ChargeModel.PER_UNIT)
            .withChargeId(CHARGE_ID)
            .withAmount(amount)
            .build();
    }

    private List<Order> getTestOrders(Instant startDate, Instant endDate, Recurrence recurrence) {
        return getTestOrders(startDate, endDate, OrderStatus.SUBMITTED, recurrence, Collections.emptyMap());
    }

    private List<Order> getTestOrders(Instant startDate, Instant endDate, OrderStatus status, Recurrence recurrence) {
        return getTestOrders(startDate, endDate, status, recurrence, Collections.emptyMap());
    }

    private List<Order> getTestEvergreenOrders(Instant startDate, Instant billingAnchorDate, OrderStatus status, Recurrence recurrence) {
        var orderId = UUID.randomUUID().toString();
        OrderLineItem orderLineItem = getOrderLineItem(orderId, startDate, EvergreenUtils.EVERGREEN_SENTINEL_END_DATE, Collections.emptyMap());
        Order order = getOrderDetails(
            orderId,
            List.of(orderLineItem),
            startDate,
            EvergreenUtils.EVERGREEN_SENTINEL_END_DATE,
            billingAnchorDate,
            status,
            recurrence,
            Collections.emptyMap(),
            SubscriptionDurationModel.EVERGREEN
        );
        return List.of(order);
    }

    private List<Order> getTestOrders(
        Instant startDate,
        Instant endDate,
        OrderStatus status,
        Recurrence recurrence,
        Map<String, BigDecimal> tenantDiscounts
    ) {
        var orderId = UUID.randomUUID().toString();
        OrderLineItem orderLineItem = getOrderLineItem(orderId, startDate, endDate, tenantDiscounts);
        Order order = getOrderDetails(
            orderId,
            List.of(orderLineItem),
            startDate,
            endDate,
            START_DATE,
            status,
            recurrence,
            tenantDiscounts,
            SubscriptionDurationModel.TERMED
        );
        return List.of(order);
    }

    private Order getOrderDetails(
        String orderId,
        List<OrderLineItem> orderLineItems,
        Instant startDate,
        Instant endDate,
        OrderStatus status,
        Recurrence recurrence,
        Map<String, BigDecimal> tenantDiscountsMap
    ) {
        return getOrderDetails(
            orderId,
            orderLineItems,
            startDate,
            endDate,
            START_DATE,
            status,
            recurrence,
            tenantDiscountsMap,
            SubscriptionDurationModel.TERMED
        );
    }

    private Order getOrderDetails(
        String orderId,
        List<OrderLineItem> orderLineItems,
        Instant startDate,
        Instant endDate,
        Instant billingAnchorDate,
        OrderStatus status,
        Recurrence recurrence,
        Map<String, BigDecimal> tenantDiscountsMap,
        SubscriptionDurationModel subscriptionDurationModel
    ) {
        List<TenantDiscount> tenantDiscounts = getTenantDiscounts(tenantDiscountsMap);

        return new Order(
            AutoGenerate.getNewUuid(),
            null,
            orderId,
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            null,
            ACCOUNT_ID,
            null,
            OrderType.NEW,
            TEST_CURRENCY,
            PaymentTerm.NET30,
            null,
            null,
            SHIPPING_CONTACT_ID,
            BILLING_CONTACT_ID,
            tenantDiscounts,
            orderLineItems,
            orderLineItems,
            startDate,
            endDate,
            billingAnchorDate,
            TERM_LENGTH,
            recurrence,
            BillingTerm.UP_FRONT,
            BigDecimal.valueOf(150),
            BigDecimal.valueOf(150),
            null,
            BigDecimal.valueOf(150),
            status,
            null,
            Instant.now(),
            Instant.now(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            0,
            null,
            0,
            null,
            false,
            false,
            null,
            null,
            null,
            true,
            null,
            null,
            OrderSource.USER,
            OrderStartDateType.FIXED,
            null,
            Optional.empty(),
            subscriptionDurationModel,
            null
        );
    }

    private OrderLineItem getOrderLineItem(String orderId, Instant startDate, Instant endDate, Map<String, BigDecimal> tenantDiscountsMap) {
        List<TenantDiscountLineItem> tenantDiscounts = getTenantDiscountLineItems(tenantDiscountsMap);
        var totalDiscount = DiscountCalculator.calculateDiscountAmounts(UNIT_PRICE, List.of(), tenantDiscounts, QUANTITY, true);

        return new OrderLineItem(
            AutoGenerate.getNewUuid(),
            ORDER_LINE_ITEM_ID,
            null,
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            orderId,
            1,
            false,
            ActionType.ADD,
            TEST_PLAN_ID,
            UUID.randomUUID().toString(),
            null,
            null,
            null,
            CHARGE_ID,
            null,
            null,
            QUANTITY,
            UNIT_PRICE.multiply(BigDecimal.valueOf(2)),
            UNIT_PRICE,
            UNIT_PRICE.multiply(BigDecimal.valueOf(QUANTITY)),
            UNIT_PRICE.multiply(BigDecimal.valueOf(2 * QUANTITY)),
            UNIT_PRICE.multiply(BigDecimal.valueOf(2 * QUANTITY)),
            totalDiscount.getTotalDiscountAmount(),
            null,
            tenantDiscounts,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            startDate,
            endDate,
            List.of(),
            null,
            null,
            Instant.now(),
            Instant.now()
        );
    }

    private List<TenantDiscount> getTenantDiscounts(Map<String, BigDecimal> discounts) {
        List<TenantDiscount> tenantDiscounts = new ArrayList<>();

        for (var entry : discounts.entrySet()) {
            var discount = new TenantDiscount();
            discount.setId(entry.getKey());
            discount.setPercent(entry.getValue());
            tenantDiscounts.add(discount);
        }
        return tenantDiscounts;
    }

    private List<TenantDiscountLineItem> getTenantDiscountLineItems(Map<String, BigDecimal> discounts) {
        List<TenantDiscountLineItem> tenantDiscounts = new ArrayList<>();

        for (var entry : discounts.entrySet()) {
            var discount = new TenantDiscountLineItem();
            discount.setId(entry.getKey());
            discount.setPercent(entry.getValue());
            tenantDiscounts.add(discount);
        }
        return tenantDiscounts;
    }

    private static Instant getInstant(LocalDate date) {
        return date.atStartOfDay(InvoiceServiceInternal.DEFAULT_TIME_ZONE).toInstant();
    }

    private void setupMockProductGet() {
        Plan plan = new Plan();
        plan.setProductId(TEST_PRODUCT_ID);
        plan.setCurrency(TEST_CURRENCY);
        when(mockProductCatalogGetService.getPlansFromChargeIds(ArgumentMatchers.anyList())).thenReturn(List.of(plan));
        var name = "test";
        Product product = new Product(
            UUID.randomUUID(),
            EntityFixture.ALL_ENTITY_IDS,
            TEST_PRODUCT_ID,
            true,
            name,
            name,
            "test",
            TEST_PRODUCT_SKU,
            null,
            Instant.now(),
            null
        );
        when(mockProductCatalogGetService.getProduct(TEST_PRODUCT_ID)).thenReturn(product);
    }

    private Recurrence getRecurrence(Cycle cycle) {
        return new Recurrence(cycle, 1);
    }
}
