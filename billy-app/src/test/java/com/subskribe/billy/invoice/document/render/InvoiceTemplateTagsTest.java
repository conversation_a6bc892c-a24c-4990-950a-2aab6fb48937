package com.subskribe.billy.invoice.document.render;

import static org.mockito.Mockito.mock;

import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.document.service.TemplateLambdaInvoker;
import com.subskribe.billy.graphql.invoice.InvoiceDetail;
import com.subskribe.billy.invoice.document.InvoiceDocumentJson;
import com.subskribe.billy.invoice.document.InvoiceTemplateData;
import com.subskribe.billy.invoice.document.InvoiceTemplateToHtmlConverter;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationStatus;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationType;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.template.model.DocumentMasterTemplate;
import com.subskribe.billy.template.services.TemplateScriptService;
import com.subskribe.billy.tenant.model.TenantInfo;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import java.util.stream.Stream;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class InvoiceTemplateTagsTest {

    private final TemplateLambdaInvoker mockTemplateLambdaInvoker = mock(TemplateLambdaInvoker.class);

    private final TemplateScriptService mockTemplateScriptService = mock(TemplateScriptService.class);

    private InvoiceTemplateToHtmlConverter converter;

    private static final List<String> ROOT_STRING_TAGS = List.of(
        "status",
        "currency",
        "tenantName",
        "tenantId",
        "tenantEmail",
        "invoiceNumber",
        "purchaseOrderNumber",
        "accountReceivableName",
        "accountReceivableEmail",
        "paymentLink",
        "paymentTerm",
        "billingCycle",
        "termLength",
        "note",
        "totalCreditAmount",
        "totalPaymentAmount"
    );

    private static final List<String> ROOT_PHONE_TAGS = List.of("tenantPhoneNumber", "billingContactPhoneNumber", "shippingContactPhoneNumber");

    private static final List<String> ROOT_DECIMAL_TAGS = List.of("amountDue", "subtotal", "taxAmount", "total", "totalDiscount");

    private static final List<String> ROOT_DATE_TAGS = List.of(
        "dueDate",
        "postedDate",
        "invoiceDate",
        "subscriptionStartDate",
        "subscriptionEndDate"
    );

    private static final List<String> ROOT_BOOLEAN_TAGS = List.of("isDraft", "isVoided", "isPaid", "hasTax", "autoRenew");

    // Tags which are non-scalar (objects/lists) or derived within invoice template data are not tested and excluded
    private static final List<String> EXCLUDED_TAGS = List.of(
        "base64EncodedLogo",
        "shippingContact",
        "billingContact",
        "tenantAddress",
        "shippingAddress",
        "billingAddress",
        "resellerAccount",
        "lineItems",
        "account",
        "sharedStartDate",
        "sharedEndDate",
        "preferredPaymentType",
        "customContent",
        "plans",
        "settlementApplications",
        "payments",
        "creditMemos",
        "orderIds",
        "customFields",
        "orderLineSortedItems",
        "subscriptionCustomFields",
        "chargeAggregatedLineItems",
        "invoiceDocumentJson",
        "termLengthInYears",
        "termLengthInMonths"
    );

    private static final List<String> ROOT_TAGS = Stream.of(ROOT_STRING_TAGS, ROOT_DECIMAL_TAGS, ROOT_PHONE_TAGS, ROOT_DATE_TAGS, ROOT_BOOLEAN_TAGS)
        .flatMap(List::stream)
        .toList();

    private static final Map<String, BigDecimal> sampleFormattedNumbers = Map.of(
        "$0.01",
        new BigDecimal("0.01"),
        "$500.00",
        new BigDecimal("500.00"),
        "$999,999.99",
        new BigDecimal("999999.99"),
        "$125.25",
        new BigDecimal("125.25")
    );

    private static final Map<String, String> sampleFormattedPhones = Map.of(
        "(*************",
        "12345671234",
        "1231231234",
        "************",
        "(*************",
        "5675671234",
        "abracadabra",
        "abracadabra"
    );

    private static final Map<String, RecurrenceJson> sampleFormattedBillingCycles = Map.of(
        "Yearly",
        new RecurrenceJson(Cycle.YEAR, 1),
        "Quarterly",
        new RecurrenceJson(Cycle.QUARTER, 1),
        "Monthly",
        new RecurrenceJson(Cycle.MONTH, 1),
        "Semi-Annual",
        new RecurrenceJson(Cycle.SEMI_ANNUAL, 1),
        "Paid In Full",
        new RecurrenceJson(Cycle.PAID_IN_FULL, 1),
        "Every 2 Months",
        new RecurrenceJson(Cycle.MONTH, 2)
    );

    private static final Map<String, RecurrenceJson> sampleFormattedTermLength = Map.of(
        "1 Month",
        new RecurrenceJson(Cycle.MONTH, 1),
        "1 Year",
        new RecurrenceJson(Cycle.YEAR, 1),
        "2 Months",
        new RecurrenceJson(Cycle.MONTH, 2),
        "3 Years",
        new RecurrenceJson(Cycle.YEAR, 3)
    );

    private static final Map<String, Long> sampleFormattedDates = Map.of(
        "Nov 2, 2023",
        1698953000L,
        "Oct 14, 2023",
        1697300000L,
        "Apr 17, 2019",
        1555500000L
    );

    @BeforeEach
    public void beforeEach() {
        converter = new InvoiceTemplateToHtmlConverter(mockTemplateLambdaInvoker, mockTemplateScriptService);
    }

    @Test
    // ensure all tags retrieved from InvoiceTemplateData are accounted for
    // if new tags are introduced they must be added to unit tests, or this test case will fail
    public void allTagsCovered() {
        List<String> availableRootTags = getRootTags();
        List<String> missingTags = new ArrayList<>(availableRootTags);
        missingTags.removeAll(ROOT_TAGS);
        missingTags.removeAll(EXCLUDED_TAGS);
        Assertions.assertThat(missingTags).isEmpty();
    }

    // inject minimum amount of fields and generate invoice document
    // include only those without which invoice document generation would fail
    @Test
    public void minimumInputHtml() {
        var invoiceDetail = new InvoiceDetail();
        var document = new InvoiceDocumentJson();
        Map<String, String> tagValues = getMinimumTagValues();

        // tenant
        var tenantInfo = new TenantInfo();
        document.setTenantInfo(tenantInfo);

        // account
        var account = new AccountJson();
        account.setCurrency(SupportedCurrency.getDefaultCurrency().getCurrencyCode());
        invoiceDetail.setCustomerAccount(account);

        // contacts
        invoiceDetail.setBillingContact(new AccountContactJson());
        invoiceDetail.setShippingContact(new AccountContactJson());

        // invoice
        invoiceDetail.setTaxTotal(BigDecimal.ZERO);
        invoiceDetail.setPaymentTerm(new PaymentTerm(tagValues.get("paymentTerm")).name());

        // document
        var accountReceivable = new AccountContact();
        document.setAccountReceivableContact(accountReceivable);
        document.setInvoiceDetail(invoiceDetail);
        document.setBillingCycle(new RecurrenceJson(Cycle.YEAR, 1));
        document.setTimeZone(TimeZone.getTimeZone("UTC"));

        Instant now = Instant.now();

        SettlementApplication paymentApplication = SettlementApplication.builder()
            .id(UUID.randomUUID())
            .applicationType(SettlementApplicationType.PAYMENT)
            .amount(new BigDecimal(10))
            .appliedOn(now)
            .createdOn(now)
            .status(SettlementApplicationStatus.APPLIED_PAYMENT)
            .build();

        SettlementApplication creditMemoApplication = SettlementApplication.builder()
            .id(UUID.randomUUID())
            .applicationType(SettlementApplicationType.CREDIT)
            .amount(new BigDecimal(20))
            .appliedOn(now)
            .createdOn(now)
            .build();

        document.setSettlementApplications(List.of(paymentApplication, creditMemoApplication));

        // Add document template
        var masterTemplate = new DocumentMasterTemplate();
        masterTemplate.setContent(generateTemplate());
        document.setMasterTemplate(masterTemplate);

        // generate html
        String generatedHtml = converter.generateHtml(document);
        String expectedHtml = generateExpectedHtml(tagValues);
        Assertions.assertThat(generatedHtml).isEqualTo(expectedHtml);
    }

    // inject random values of fields and generate document
    @Test
    public void randomInputHtml() {
        var invoiceDetail = new InvoiceDetail();
        var document = new InvoiceDocumentJson();
        var tagValues = getRandomTagValues();

        // tenant
        var tenantInfo = new TenantInfo();
        tenantInfo.setName(tagValues.get("tenantName"));
        tenantInfo.setTenantId(tagValues.get("tenantId"));
        tenantInfo.setEmail(tagValues.get("tenantEmail"));
        tenantInfo.setPhoneNumber(tagValues.get("tenantPhoneNumber"));
        document.setTenantInfo(tenantInfo);

        // account
        var account = new AccountJson();
        account.setCurrency(SupportedCurrency.getDefaultCurrency().getCurrencyCode());
        invoiceDetail.setCustomerAccount(account);
        invoiceDetail.setDueDate(sampleFormattedDates.get(tagValues.get("dueDate")));
        invoiceDetail.setPostedDate(sampleFormattedDates.get(tagValues.get("postedDate")));
        invoiceDetail.setInvoiceDate(sampleFormattedDates.get(tagValues.get("invoiceDate")));
        invoiceDetail.setNote(tagValues.get("note"));

        // contacts
        var billingContact = new AccountContactJson();
        billingContact.setPhoneNumber(tagValues.get("billingContactPhoneNumber"));
        invoiceDetail.setBillingContact(billingContact);
        var shippingContact = new AccountContactJson();
        shippingContact.setPhoneNumber(tagValues.get("shippingContactPhoneNumber"));
        invoiceDetail.setShippingContact(shippingContact);

        // invoice
        invoiceDetail.setStatus(InvoiceStatus.valueOf(tagValues.get("status").toUpperCase()));
        invoiceDetail.setPaymentTerm(new PaymentTerm(tagValues.get("paymentTerm")).name());
        invoiceDetail.setInvoiceNumber(tagValues.get("invoiceNumber"));
        invoiceDetail.setTaxTotal(sampleFormattedNumbers.get(tagValues.get("taxAmount")));
        invoiceDetail.setTotal(sampleFormattedNumbers.get(tagValues.get("total")));
        invoiceDetail.setTotalDiscount(sampleFormattedNumbers.get(tagValues.get("totalDiscount")));
        invoiceDetail.setSubTotal(sampleFormattedNumbers.get(tagValues.get("subtotal")));
        invoiceDetail.setPurchaseOrderNumber(tagValues.get("purchaseOrderNumber"));

        // account receivable
        var accountReceivable = new AccountContact();
        var firstName = getRandomString();
        var lastName = getRandomString();
        accountReceivable.setFirstName(firstName);
        accountReceivable.setLastName(lastName);
        tagValues.put("accountReceivableName", String.format("%s %s", firstName, lastName));
        accountReceivable.setEmail(tagValues.get("accountReceivableEmail"));
        document.setAccountReceivableContact(accountReceivable);
        document.setInvoiceDetail(invoiceDetail);

        // document aggregated values
        document.setAmountDue(sampleFormattedNumbers.get(tagValues.get("amountDue")));
        document.setSubscriptionStartDate(Instant.ofEpochSecond(sampleFormattedDates.get(tagValues.get("subscriptionStartDate"))));
        document.setSubscriptionEndDate(Instant.ofEpochSecond(sampleFormattedDates.get(tagValues.get("subscriptionEndDate"))));
        document.setPaymentLink(tagValues.get("paymentLink"));
        document.setAutoRenew(Boolean.parseBoolean(tagValues.get("autoRenew")));
        document.setBillingCycle(sampleFormattedBillingCycles.get(tagValues.get("billingCycle")));
        document.setTermLength(sampleFormattedTermLength.get(tagValues.get("termLength")));
        document.setTimeZone(TimeZone.getTimeZone("UTC"));

        // Add document template
        var masterTemplate = new DocumentMasterTemplate();
        masterTemplate.setContent(generateTemplate());
        document.setMasterTemplate(masterTemplate);

        // generate html
        String generatedHtml = converter.generateHtml(document);
        String expectedHtml = generateExpectedHtml(tagValues);
        Assertions.assertThat(generatedHtml).isEqualTo(expectedHtml);
    }

    // get all get* is* methods defined on InvoiceTemplateData and change them to tags
    private List<String> getRootTags() {
        Class<?> invoiceTemplateDataClass = InvoiceTemplateData.class;
        Method[] methods = invoiceTemplateDataClass.getDeclaredMethods();
        List<String> tagNames = new ArrayList<>();
        for (Method method : methods) {
            if (java.lang.reflect.Modifier.isPublic(method.getModifiers())) {
                String methodName = method.getName();
                String tagName = StringUtils.EMPTY;
                if (methodName.startsWith("get")) {
                    tagName = methodName.substring(3);
                } else if (methodName.startsWith("is")) {
                    tagName = methodName.substring(2);
                }
                if (StringUtils.isNotBlank(tagName)) {
                    tagName = tagName.substring(0, 1).toLowerCase() + tagName.substring(1);
                    tagNames.add(tagName);
                }
            }
        }
        return tagNames;
    }

    Map<String, String> getMinimumTagValues() {
        Map<String, String> tagValues = new HashMap<>();
        for (var tagName : ROOT_BOOLEAN_TAGS) {
            tagValues.put(tagName, "false");
        }
        tagValues.put("taxAmount", "$0.00");
        tagValues.put("paymentTerm", "Net 0");
        tagValues.put("billingCycle", "Yearly");
        tagValues.put("totalPaymentAmount", "$10.00");
        tagValues.put("totalCreditAmount", "$20.00");
        tagValues.put("currency", "USD");
        return tagValues;
    }

    Map<String, String> getRandomTagValues() {
        Map<String, String> tagValues = new HashMap<>();

        for (var tagName : ROOT_STRING_TAGS) {
            tagValues.put(tagName, getRandomString());
        }

        for (var tagName : ROOT_DATE_TAGS) {
            tagValues.put(tagName, getRandomDateKey());
        }

        for (var tagName : ROOT_DECIMAL_TAGS) {
            tagValues.put(tagName, getRandomNumberKey());
        }

        for (var tagName : ROOT_PHONE_TAGS) {
            tagValues.put(tagName, getRandomPhoneKey());
        }

        setRandomStatusFlags(tagValues);

        tagValues.put("billingCycle", getRandomBillingCycle());
        tagValues.put("termLength", getRandomTermLength());
        tagValues.put("hasTax", "true");
        int randomDueDays = RandomUtils.nextInt(0, 366);
        tagValues.put("paymentTerm", "Net " + randomDueDays);
        tagValues.put("autoRenew", getRandomBoolean().toString());
        tagValues.put("totalPaymentAmount", "$0.00");
        tagValues.put("totalCreditAmount", "$0.00");
        tagValues.put("currency", "USD");

        return tagValues;
    }

    private void setRandomStatusFlags(Map<String, String> tagValues) {
        List<String> statusValues = Arrays.stream(InvoiceStatus.values()).map(value -> StringUtils.capitalize(value.name().toLowerCase())).toList();
        int randomIndex = RandomUtils.nextInt(0, statusValues.size());
        String status = statusValues.get(randomIndex);
        tagValues.put("status", status);
        tagValues.put("isDraft", "Draft".equals(status) ? "true" : "false");
        tagValues.put("isVoided", "Voided".equals(status) ? "true" : "false");
        tagValues.put("isPaid", "Paid".equals(status) ? "true" : "false");
    }

    String getRandomString() {
        return RandomStringUtils.randomAlphanumeric(10);
    }

    Boolean getRandomBoolean() {
        return RandomUtils.nextBoolean();
    }

    String getRandomNumberKey() {
        int sampleSize = sampleFormattedNumbers.size();
        int randomIndex = RandomUtils.nextInt(0, sampleSize);
        return sampleFormattedNumbers.keySet().stream().toList().get(randomIndex);
    }

    String getRandomPhoneKey() {
        int sampleSize = sampleFormattedPhones.size();
        int randomIndex = RandomUtils.nextInt(0, sampleSize);
        return sampleFormattedPhones.keySet().stream().toList().get(randomIndex);
    }

    String getRandomDateKey() {
        int sampleSize = sampleFormattedDates.size();
        int randomIndex = RandomUtils.nextInt(0, sampleSize);
        return sampleFormattedDates.keySet().stream().toList().get(randomIndex);
    }

    String getRandomBillingCycle() {
        int sampleSize = sampleFormattedBillingCycles.size();
        int randomIndex = RandomUtils.nextInt(0, sampleSize);
        return sampleFormattedBillingCycles.keySet().stream().toList().get(randomIndex);
    }

    String getRandomTermLength() {
        int sampleSize = sampleFormattedTermLength.size();
        int randomIndex = RandomUtils.nextInt(0, sampleSize);
        return sampleFormattedTermLength.keySet().stream().toList().get(randomIndex);
    }

    // generate template with one tag per line
    private String generateTemplate() {
        StringBuilder sb = new StringBuilder();
        for (var tagName : ROOT_TAGS) {
            sb.append(String.format("%s: {{%s}}%s", tagName, tagName, StringUtils.LF));
        }
        return sb.toString();
    }

    // build expected outcome using the tag values map
    private String generateExpectedHtml(Map<String, String> tagValues) {
        StringBuilder sb = new StringBuilder();
        for (var tagName : ROOT_TAGS) {
            String tagValue = tagValues.getOrDefault(tagName, StringUtils.EMPTY);
            sb.append(String.format("%s: %s%s", tagName, tagValue, StringUtils.LF));
        }
        return sb.toString();
    }
}
