package com.subskribe.billy.invoice.service.processor.model;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class InvoiceProcessorModesTest {

    @Test
    public void verifyProcessorModeIndexes() {
        Assertions.assertThat(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS.getBitSetIndex()).isEqualTo(1);
    }

    @Test
    public void verifyProcessorModesFlip() {
        InvoiceProcessorModes modes = new InvoiceProcessorModes();
        Assertions.assertThat(modes.isModeSet(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS)).isFalse();
        // now set the mode
        modes.setMode(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS);
        Assertions.assertThat(modes.isModeSet(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS)).isTrue();

        // now unset the mode
        modes.unsetMode(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS);
        Assertions.assertThat(modes.isModeSet(ProcessorMode.USAGE_INCLUDE_BILLING_PERIOD_IN_PROGRESS)).isFalse();
    }
}
