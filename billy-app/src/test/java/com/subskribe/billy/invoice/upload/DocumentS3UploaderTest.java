package com.subskribe.billy.invoice.upload;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.s3.S3ClientProvider;
import com.subskribe.billy.shared.config.TypeSafeDynamicConfigLoader;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

@Tag("integration")
class DocumentS3UploaderTest {

    private static final String FILE_NAME = "integration-test.pdf";

    private static final String CONFIG_NAME = "config-ci.conf";

    private static final BillyConfiguration CONFIG = TypeSafeDynamicConfigLoader.load(CONFIG_NAME);

    private static final S3ClientProvider S3_CLIENT_PROVIDER = new S3ClientProvider(CONFIG);

    private static final DocumentS3Uploader DOCUMENT_S3_UPLOADER = new DocumentS3Uploader(S3_CLIENT_PROVIDER);

    // todo: can this test be enabled now that config issue is resolved. Was disabled by <PERSON> in 2/10/21
    @Test
    @Disabled
    public void upload() {
        DOCUMENT_S3_UPLOADER.upload(CONFIG.getDocumentConfiguration().getInvoiceS3Bucket(), FILE_NAME, new byte[0]);
    }
}
