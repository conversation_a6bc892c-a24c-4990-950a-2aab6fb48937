package com.subskribe.billy.invoice;

import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.shared.invariants.InvariantChecker;
import java.math.BigDecimal;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class InvoiceInvariantCheckerTest {

    private static final String TEST_ACCOUNT_ID = "ACCT-TEST";
    private static final String TEST_SUBSCRIPTION_ID = "SUB-TEST";
    private static final Invoice.Number TEST_INVOICE_NUMBER = new Invoice.Number("INV-0000001");

    private final InvariantChecker<Invoice> invoiceInvariantChecker = new InvoiceInvariantChecker();

    @Test
    public void verifyNullCallThrowsServiceFailure() {
        Assertions.assertThatThrownBy(() -> invoiceInvariantChecker.check(null))
            .isInstanceOf(ServiceFailureException.class)
            .hasMessageContaining("Invoice input cannot be null");
    }

    @Test
    void testAmountSignParityWorksAsExpected() {
        Invoice.InvoiceBuilder baseInvoiceBuilder = baseInvoiceBuilder();
        InvoiceItem.InvoiceItemBuilder baseItemBuilder = baseItemBuilder("line-1");

        // amount positive tax negative
        baseItemBuilder.amount(new BigDecimal("10.0"));
        baseItemBuilder.taxAmount(new BigDecimal("-1.0"));

        InvoiceItem baseItem = baseItemBuilder.createInvoiceItem();
        baseInvoiceBuilder.invoiceItems(List.of(baseItem));

        String message = String.format(
            "line %s amount %s and tax amount %s signs do not match",
            baseItem.getInvoiceLineNumber(),
            baseItem.getAmount().toPlainString(),
            baseItem.getTaxAmount().toPlainString()
        );
        Assertions.assertThatThrownBy(() -> invoiceInvariantChecker.checkAndThrow(baseInvoiceBuilder.createInvoice()))
            .isInstanceOf(InvariantCheckFailedException.class)
            .hasMessageContaining(message);

        // amount negative tax positive
        baseItemBuilder.amount(new BigDecimal("-10.0"));
        baseItemBuilder.taxAmount(new BigDecimal("3.0"));

        baseItem = baseItemBuilder.createInvoiceItem();
        baseInvoiceBuilder.invoiceItems(List.of(baseItem));

        message = String.format(
            "line %s amount %s and tax amount %s signs do not match",
            baseItem.getInvoiceLineNumber(),
            baseItem.getAmount().toPlainString(),
            baseItem.getTaxAmount().toPlainString()
        );
        Assertions.assertThatThrownBy(() -> invoiceInvariantChecker.checkAndThrow(baseInvoiceBuilder.createInvoice()))
            .isInstanceOf(InvariantCheckFailedException.class)
            .hasMessageContaining(message);

        // amount positive and tax 0
        baseItemBuilder.amount(new BigDecimal("10.0"));
        baseItemBuilder.taxAmount(BigDecimal.ZERO);

        baseItem = baseItemBuilder.createInvoiceItem();
        baseInvoiceBuilder.invoiceItems(List.of(baseItem));

        Assertions.assertThatCode(() -> invoiceInvariantChecker.checkAndThrow(baseInvoiceBuilder.createInvoice())).doesNotThrowAnyException();

        // amount zero tax positive
        // TODO: see if this is valid case
        baseItemBuilder.amount(BigDecimal.ZERO);
        baseItemBuilder.taxAmount(new BigDecimal("5.0"));

        baseItem = baseItemBuilder.createInvoiceItem();
        baseInvoiceBuilder.invoiceItems(List.of(baseItem));

        Assertions.assertThatCode(() -> invoiceInvariantChecker.checkAndThrow(baseInvoiceBuilder.createInvoice())).doesNotThrowAnyException();

        // amount null
        // tax present
        baseItemBuilder.amount(null);
        baseItemBuilder.taxAmount(new BigDecimal("2.0"));

        baseItem = baseItemBuilder.createInvoiceItem();
        baseInvoiceBuilder.invoiceItems(List.of(baseItem));

        Assertions.assertThatCode(() -> invoiceInvariantChecker.checkAndThrow(baseInvoiceBuilder.createInvoice())).doesNotThrowAnyException();

        // amount present
        // tax null
        baseItemBuilder.amount(new BigDecimal("10.0"));
        baseItemBuilder.taxAmount(null);

        baseItem = baseItemBuilder.createInvoiceItem();
        baseInvoiceBuilder.invoiceItems(List.of(baseItem));

        Assertions.assertThatCode(() -> invoiceInvariantChecker.checkAndThrow(baseInvoiceBuilder.createInvoice())).doesNotThrowAnyException();
    }

    public Invoice.InvoiceBuilder baseInvoiceBuilder() {
        Invoice.InvoiceBuilder builder = new Invoice.InvoiceBuilder();
        builder.customerAccountId(TEST_ACCOUNT_ID);
        builder.subscriptionId(TEST_SUBSCRIPTION_ID);
        builder.invoiceNumber(TEST_INVOICE_NUMBER);
        builder.currency("USD");
        return builder;
    }

    public InvoiceItem.InvoiceItemBuilder baseItemBuilder(String lineNum) {
        InvoiceItem.InvoiceItemBuilder builder = new InvoiceItem.InvoiceItemBuilder();
        builder.invoiceLineNumber(lineNum);
        return builder;
    }
}
