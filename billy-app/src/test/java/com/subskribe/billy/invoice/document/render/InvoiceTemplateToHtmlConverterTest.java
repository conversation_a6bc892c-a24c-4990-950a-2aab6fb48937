package com.subskribe.billy.invoice.document.render;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.mock;

import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.document.service.TemplateLambdaInvoker;
import com.subskribe.billy.graphql.invoice.InvoiceDetail;
import com.subskribe.billy.invoice.document.InvoiceDocumentJson;
import com.subskribe.billy.invoice.document.InvoiceTemplateToHtmlConverter;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.template.services.TemplateScriptService;
import com.subskribe.billy.tenant.model.TenantInfo;
import java.util.Optional;
import java.util.TimeZone;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class InvoiceTemplateToHtmlConverterTest {

    private final TemplateLambdaInvoker mockTemplateLambdaInvoker = mock(TemplateLambdaInvoker.class);

    private final TemplateScriptService mockTemplateScriptService = mock(TemplateScriptService.class);

    private InvoiceTemplateToHtmlConverter converter;

    @BeforeEach
    public void beforeEach() {
        converter = new InvoiceTemplateToHtmlConverter(mockTemplateLambdaInvoker, mockTemplateScriptService);
    }

    @Test
    public void defaultHtml() {
        var document = new InvoiceDocumentJson();
        document.setTenantInfo(new TenantInfo());
        var invoiceDetail = new InvoiceDetail();
        var account = new AccountJson();
        account.setCurrency(SupportedCurrency.getDefaultCurrency().getCurrencyCode());
        invoiceDetail.setCustomerAccount(account);
        invoiceDetail.setBillingContact(new AccountContactJson());
        invoiceDetail.setShippingContact(new AccountContactJson());
        invoiceDetail.setPaymentTerm(PaymentTerm.NET30.name());
        document.setInvoiceDetail(invoiceDetail);
        document.setLogoContent(Optional.empty());
        document.setTimeZone(TimeZone.getTimeZone("UTC"));
        var html = converter.generateHtml(document);
        assertFalse(html.isEmpty());
    }
}
