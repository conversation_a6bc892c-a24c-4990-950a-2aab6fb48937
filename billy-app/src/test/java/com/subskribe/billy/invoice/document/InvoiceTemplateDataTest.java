package com.subskribe.billy.invoice.document;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.graphql.invoice.InvoiceDetail;
import com.subskribe.billy.graphql.invoice.InvoiceItemDetail;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.resources.json.account.AccountJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.Period;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class InvoiceTemplateDataTest {

    private static final String DEFAULT_TIME_ZONE = "America/Los_Angeles";

    private static final String DEFAULT_CURRENCY = "USD";

    @Test
    public void reducedLineItems() {
        LocalDateTime now = LocalDateTime.now();
        String subscriptionChargeId1 = RandomStringUtils.randomAlphanumeric(10);
        String subscriptionChargeId2 = RandomStringUtils.randomAlphanumeric(10);
        Period billingPeriod = Period.between(now.toInstant(ZoneOffset.UTC), now.plusMonths(1).toInstant(ZoneOffset.UTC));
        PlanJson planJson = getPlanJson(RandomStringUtils.randomAlphanumeric(10), RandomStringUtils.randomAlphanumeric(10));
        ChargeDetail chargeDetail = getChargeDetail();
        InvoiceItemDetail item1 = getInvoiceItemDetails(
            "1",
            10,
            BigDecimal.valueOf(100),
            billingPeriod,
            planJson,
            chargeDetail,
            subscriptionChargeId1
        );
        InvoiceItemDetail item2 = getInvoiceItemDetails(
            "2",
            -10,
            BigDecimal.valueOf(-100),
            billingPeriod,
            planJson,
            chargeDetail,
            subscriptionChargeId1
        );
        InvoiceItemDetail item3 = getInvoiceItemDetails(
            "3",
            20,
            BigDecimal.valueOf(200),
            billingPeriod,
            planJson,
            chargeDetail,
            subscriptionChargeId2
        );
        AccountJson accountJson = new AccountJson();
        accountJson.setCurrency(DEFAULT_CURRENCY);
        InvoiceDetail invoiceDetail = new InvoiceDetail();
        invoiceDetail.setCustomerAccount(accountJson);
        invoiceDetail.setInvoiceItems(List.of(item1, item2, item3));
        InvoiceDocumentJson invoiceDocumentJson = new InvoiceDocumentJson();
        invoiceDocumentJson.setInvoiceDetail(invoiceDetail);
        invoiceDocumentJson.setTimeZone(TimeZone.getTimeZone(DEFAULT_TIME_ZONE));
        invoiceDocumentJson.setProductsMap(Map.of(planJson.getProductId(), new ProductJson()));
        invoiceDocumentJson.setChargeCustomFields(Map.of());
        invoiceDocumentJson.setOrderLineMetrics(Map.of());
        InvoiceTemplateData invoiceTemplateData = new InvoiceTemplateData(invoiceDocumentJson);

        List<InvoiceTemplateLineItem> lineItems = invoiceTemplateData.getLineItems();
        List<InvoiceTemplateLineItem> chargeAggregatedLineItemsLineItems = invoiceTemplateData.getChargeAggregatedLineItems();

        assertEquals(2, lineItems.size());
        assertEquals(
            new BigDecimal("200.00"),
            lineItems.stream().map(InvoiceTemplateLineItem::getRawAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        assertEquals(1, chargeAggregatedLineItemsLineItems.size());
        assertEquals(new BigDecimal("200.00"), chargeAggregatedLineItemsLineItems.get(0).getRawAmount());

        // re-run again
        lineItems = invoiceTemplateData.getLineItems();
        chargeAggregatedLineItemsLineItems = invoiceTemplateData.getChargeAggregatedLineItems();

        assertEquals(
            new BigDecimal("200.00"),
            lineItems.stream().map(InvoiceTemplateLineItem::getRawAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        assertEquals(new BigDecimal("200.00"), chargeAggregatedLineItemsLineItems.get(0).getRawAmount());
    }

    @Test
    public void invoiceTemplatePlans() {
        LocalDateTime now = LocalDateTime.now();
        Period billingPeriod = Period.between(now.toInstant(ZoneOffset.UTC), now.plusMonths(1).toInstant(ZoneOffset.UTC));
        PlanJson planJson1 = getPlanJson(RandomStringUtils.randomAlphanumeric(10), RandomStringUtils.randomAlphanumeric(10));
        PlanJson planJson2 = getPlanJson(RandomStringUtils.randomAlphanumeric(10), RandomStringUtils.randomAlphanumeric(10));
        ChargeDetail chargeDetail1 = getChargeDetail();
        ChargeDetail chargeDetail2 = getChargeDetail();
        ChargeDetail chargeDetail3 = getChargeDetail();
        InvoiceItemDetail item1 = getInvoiceItemDetails(
            "1",
            10,
            BigDecimal.valueOf(100),
            billingPeriod,
            planJson1,
            chargeDetail1,
            RandomStringUtils.randomAlphanumeric(10)
        );
        InvoiceItemDetail item2 = getInvoiceItemDetails(
            "2",
            15,
            BigDecimal.valueOf(150),
            billingPeriod,
            planJson1,
            chargeDetail2,
            RandomStringUtils.randomAlphanumeric(10)
        );
        InvoiceItemDetail item3 = getInvoiceItemDetails(
            "3",
            20,
            BigDecimal.valueOf(300),
            billingPeriod,
            planJson2,
            chargeDetail3,
            RandomStringUtils.randomAlphanumeric(10)
        );
        AccountJson accountJson = new AccountJson();
        accountJson.setCurrency(DEFAULT_CURRENCY);
        InvoiceDetail invoiceDetail = new InvoiceDetail();
        invoiceDetail.setCustomerAccount(accountJson);
        invoiceDetail.setInvoiceItems(List.of(item1, item2, item3));
        InvoiceDocumentJson invoiceDocumentJson = new InvoiceDocumentJson();
        invoiceDocumentJson.setInvoiceDetail(invoiceDetail);
        invoiceDocumentJson.setTimeZone(TimeZone.getTimeZone(DEFAULT_TIME_ZONE));
        invoiceDocumentJson.setProductsMap(Map.of(planJson1.getProductId(), new ProductJson(), planJson2.getProductId(), new ProductJson()));
        invoiceDocumentJson.setChargeCustomFields(Map.of());
        invoiceDocumentJson.setOrderLineMetrics(Map.of());
        InvoiceTemplateData invoiceTemplateData = new InvoiceTemplateData(invoiceDocumentJson);

        List<InvoiceTemplatePlan> plans = invoiceTemplateData.getPlans();
        InvoiceTemplatePlan planTemplate1 = plans.stream().filter(plan -> plan.getId().equals(planJson1.getId())).findFirst().orElseThrow();
        InvoiceTemplatePlan planTemplate2 = plans.stream().filter(plan -> plan.getId().equals(planJson2.getId())).findFirst().orElseThrow();

        assertEquals(2, plans.size());
        assertEquals("$250.00", planTemplate1.getAmount());
        assertEquals(planJson1.getName(), planTemplate1.getName());
        assertEquals(2, planTemplate1.getLineItems().size());
        assertEquals("", planTemplate1.getSharedQuantity());
        assertFalse(planTemplate1.hasSingleItem());

        assertEquals("$300.00", planTemplate2.getAmount());
        assertEquals(1, planTemplate2.getLineItems().size());
        assertEquals("20", planTemplate2.getSharedQuantity());
        assertEquals("$15.00", planTemplate2.getPlanSellUnitPrice());
        assertTrue(planTemplate2.hasSingleItem());
    }

    private PlanJson getPlanJson(String name, String description) {
        PlanJson planJson = new PlanJson();
        planJson.setId(RandomStringUtils.randomAlphanumeric(10));
        planJson.setName(name);
        planJson.setDescription(description);
        planJson.setProductId(RandomStringUtils.randomAlphanumeric(10));
        return planJson;
    }

    private ChargeDetail getChargeDetail() {
        ChargeDetail chargeDetail = new ChargeDetail();
        chargeDetail.setId(RandomStringUtils.randomAlphanumeric(10));
        chargeDetail.setDrawdown(false);
        chargeDetail.setRecurrence(new RecurrenceJson(Cycle.YEAR, 1));
        return chargeDetail;
    }

    private InvoiceItemDetail getInvoiceItemDetails(
        String invoiceLineNumber,
        long quantity,
        BigDecimal amount,
        Period billingPeriod,
        PlanJson planJson,
        ChargeDetail chargeDetail,
        String subscriptionChargeId
    ) {
        InvoiceItemDetail invoiceItemDetail = new InvoiceItemDetail();
        invoiceItemDetail.setInvoiceLineNumber(invoiceLineNumber);
        invoiceItemDetail.setSubscriptionChargeId(RandomStringUtils.randomAlphanumeric(10));
        invoiceItemDetail.setQuantity(quantity);
        invoiceItemDetail.setAmount(amount);
        invoiceItemDetail.setPlan(planJson);
        invoiceItemDetail.setCharge(chargeDetail);
        invoiceItemDetail.setPeriodStartDate(billingPeriod.getStart().getEpochSecond());
        invoiceItemDetail.setPeriodEndDate(billingPeriod.getEnd().getEpochSecond());
        invoiceItemDetail.setSubscriptionChargeId(subscriptionChargeId);
        invoiceItemDetail.setSellUnitPrice(Numbers.scaledDivide(amount, BigDecimal.valueOf(quantity)));
        return invoiceItemDetail;
    }
}
