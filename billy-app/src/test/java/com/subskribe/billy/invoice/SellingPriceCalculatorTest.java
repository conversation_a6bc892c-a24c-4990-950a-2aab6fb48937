package com.subskribe.billy.invoice;

import static com.subskribe.billy.TestConstants.TIME_ZONE;
import static com.subskribe.billy.TestConstants.ZONE_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.invoice.service.processor.OneTimeInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PrepaidInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.RecurringInvoiceProcessor;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class SellingPriceCalculatorTest {

    private static final Recurrence YEARLY = new Recurrence(Cycle.YEAR, 1);

    private static final ZonedDateTime JAN_1_2022 = ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZONE_ID);

    private final TenantSettingService mockTenantSettingService = TenantSettingServiceFixture.getDefault();

    @Mock
    private SubscriptionGetService mockSubscriptionGetService;

    @Mock
    private RecurringInvoiceProcessor recurringInvoiceProcessor;

    @Mock
    private PrepaidInvoiceProcessor prepaidInvoiceProcessor;

    @Mock
    private OneTimeInvoiceProcessor oneTimeInvoiceProcessor;

    @Mock
    private ProrationConfigurationGetService prorationConfigurationGetService;

    @Mock
    private FeatureService featureService;

    private SellingPriceCalculator sellingPriceCalculator;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(featureService.isEnabled(Feature.FLEXIBLE_BILLING_ANCHOR_DATE)).thenReturn(false);
        SubscriptionBillingPeriodService subscriptionBillingService = new SubscriptionBillingPeriodService(
            mockSubscriptionGetService,
            mockTenantSettingService,
            featureService,
            prorationConfigurationGetService
        );
        sellingPriceCalculator = new SellingPriceCalculator(
            subscriptionBillingService,
            mockTenantSettingService,
            recurringInvoiceProcessor,
            prepaidInvoiceProcessor,
            oneTimeInvoiceProcessor
        );
    }

    @Test
    public void calculateNegativeDiscount() {
        Instant itemStart = JAN_1_2022.toInstant();
        Instant itemEnd = JAN_1_2022.plusYears(3).toInstant();

        var itemAmount = new BigDecimal("9000");

        // per billing cycle list amount = 300 * 5 = 1500.
        var listPrice = new BigDecimal("1500");
        Long quantity = 5L;

        var soldOrderLineItem = new SellingPriceCalculator.SoldOrderLineItem()
            .withOrderLineItemAmount(itemAmount)
            .withQuantity(quantity)
            .withBillingCycleListPrice(listPrice)
            .withBillingCycle(YEARLY)
            .withBillingAnchorDate(itemStart)
            .withLineItemStart(itemStart)
            .withLineItemEnd(itemEnd);

        var prorationConfig = new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT);
        BigDecimal discountPercent = sellingPriceCalculator.calculateDiscountPercent(
            soldOrderLineItem,
            prorationConfig,
            TIME_ZONE,
            getCharge(ChargeType.RECURRING)
        );

        assertEquals(new BigDecimal("-1.000"), discountPercent.setScale(3, RoundingMode.HALF_UP));
    }

    @Test
    public void calculateGreaterThan100PercentDiscount() {
        Instant itemStart = JAN_1_2022.toInstant();
        Instant itemEnd = JAN_1_2022.plusYears(3).toInstant();

        var itemAmount = new BigDecimal("-4500");

        // per billing cycle list amount = 300 * 5 = 1500.
        var listPrice = new BigDecimal("1500");
        Long quantity = 5L;

        var soldOrderLineItem = new SellingPriceCalculator.SoldOrderLineItem()
            .withOrderLineItemAmount(itemAmount)
            .withQuantity(quantity)
            .withBillingCycleListPrice(listPrice)
            .withBillingCycle(YEARLY)
            .withBillingAnchorDate(itemStart)
            .withLineItemStart(itemStart)
            .withLineItemEnd(itemEnd);

        var prorationConfig = new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT);
        BigDecimal discountPercent = sellingPriceCalculator.calculateDiscountPercent(
            soldOrderLineItem,
            prorationConfig,
            TIME_ZONE,
            getCharge(ChargeType.RECURRING)
        );

        assertEquals(new BigDecimal("2.000"), discountPercent.setScale(3, RoundingMode.HALF_UP));
    }

    @Test
    public void calculate100PercentDiscount() {
        Instant itemStart = JAN_1_2022.toInstant();
        Instant itemEnd = JAN_1_2022.plusYears(3).toInstant();

        var itemAmount = new BigDecimal("0");

        // per billing cycle list amount = 300 * 5 = 1500.
        var listPrice = new BigDecimal("1500");
        Long quantity = 5L;

        var soldOrderLineItem = new SellingPriceCalculator.SoldOrderLineItem()
            .withOrderLineItemAmount(itemAmount)
            .withQuantity(quantity)
            .withBillingCycleListPrice(listPrice)
            .withBillingCycle(YEARLY)
            .withBillingAnchorDate(itemStart)
            .withLineItemStart(itemStart)
            .withLineItemEnd(itemEnd);

        var prorationConfig = new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT);
        BigDecimal discountPercent = sellingPriceCalculator.calculateDiscountPercent(
            soldOrderLineItem,
            prorationConfig,
            TIME_ZONE,
            getCharge(ChargeType.RECURRING)
        );

        assertEquals(new BigDecimal("1.000"), discountPercent.setScale(3, RoundingMode.HALF_UP));
    }

    @Test
    public void calculateDiscountPercentWithNormalizedProration() {
        for (var testData : getNormalizedProrationTestData()) {
            Instant itemStart = testData.getStart().toInstant();
            Instant itemEnd = testData.getEnd().toInstant();
            BigDecimal itemAmount = BigDecimal.valueOf(testData.getSoldAmount());
            BigDecimal listPrice = BigDecimal.valueOf(testData.getListPrice());
            ChargeType chargeType = testData.getChargeType();

            var soldOrderLineItem = new SellingPriceCalculator.SoldOrderLineItem()
                .withOrderLineItemAmount(itemAmount)
                .withQuantity(testData.getQuantity())
                .withBillingCycleListPrice(listPrice)
                .withBillingCycle(YEARLY)
                .withBillingAnchorDate(itemStart)
                .withLineItemStart(itemStart)
                .withLineItemEnd(itemEnd);

            var prorationConfig = new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.NORMALIZED);
            BigDecimal discountPercent = sellingPriceCalculator.calculateDiscountPercent(
                soldOrderLineItem,
                prorationConfig,
                TIME_ZONE,
                getCharge(chargeType)
            );

            assertEquals(testData.getExpectedDiscount(), discountPercent.setScale(5, RoundingMode.HALF_UP));
        }
    }

    @Test
    public void calculateDiscountForAmendment() {
        Instant subscriptionStart = JAN_1_2022.toInstant();

        // 1.5 year amendment in a 2 year subscription
        Instant itemStart = JAN_1_2022.plusMonths(6).toInstant(); // item starts July 1, 2022
        Instant itemEnd = JAN_1_2022.plusYears(2).toInstant(); // item ends Jan 1, 2024

        // 50% discount: (1500 * 0.5) * (1 + 184 / 365)
        var itemAmount = new BigDecimal("1128.08");

        // per billing cycle list amount = 300 * 5 = 1500.
        var listPrice = new BigDecimal("1500");
        Long quantity = 5L;

        var soldOrderLineItem = new SellingPriceCalculator.SoldOrderLineItem()
            .withOrderLineItemAmount(itemAmount)
            .withQuantity(quantity)
            .withBillingCycleListPrice(listPrice)
            .withBillingCycle(YEARLY)
            .withBillingAnchorDate(subscriptionStart)
            .withSubscriptionStart(subscriptionStart)
            .withLineItemStart(itemStart)
            .withLineItemEnd(itemEnd);

        var prorationConfig = new ProrationConfig(ProrationConfig.Scheme.CALENDAR_DAYS, ProrationConfig.Mode.EXACT_DAYS);
        BigDecimal discountPercent = sellingPriceCalculator.calculateDiscountPercent(
            soldOrderLineItem,
            prorationConfig,
            TIME_ZONE,
            getCharge(ChargeType.RECURRING)
        );

        assertEquals(new BigDecimal("0.50000"), discountPercent.setScale(5, RoundingMode.HALF_UP));
    }

    private Charge getCharge(ChargeType chargeType) {
        Charge charge = new Charge();
        charge.setType(chargeType);
        return charge;
    }

    private List<SellingPriceTestData> getNormalizedProrationTestData() {
        return List.of(
            new SellingPriceTestData(
                ZonedDateTime.of(2021, 2, 28, 0, 0, 0, 0, ZONE_ID),
                ZonedDateTime.of(2022, 2, 28, 0, 0, 0, 0, ZONE_ID),
                120000L,
                100000L,
                1L,
                new BigDecimal("0.16667"),
                ChargeType.RECURRING
            ),
            new SellingPriceTestData(
                ZonedDateTime.of(2021, 7, 1, 0, 0, 0, 0, ZONE_ID),
                ZonedDateTime.of(2022, 7, 1, 0, 0, 0, 0, ZONE_ID),
                60000L,
                30000L,
                1L,
                new BigDecimal("0.50000"),
                ChargeType.RECURRING
            ),
            new SellingPriceTestData(
                ZonedDateTime.of(2021, 4, 23, 0, 0, 0, 0, ZONE_ID),
                ZonedDateTime.of(2022, 4, 23, 0, 0, 0, 0, ZONE_ID),
                36000L,
                30000L,
                30L,
                new BigDecimal("0.16667"),
                ChargeType.RECURRING
            ),
            // 1.5 year subscription with 20% discount
            new SellingPriceTestData(
                ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZONE_ID),
                ZonedDateTime.of(2022, 7, 1, 0, 0, 0, 0, ZONE_ID),
                36000L,
                43200L,
                30L,
                new BigDecimal("0.20000"),
                ChargeType.RECURRING
            ),
            // 6.5 months subscription with 20% discount
            new SellingPriceTestData(
                ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZONE_ID),
                ZonedDateTime.of(2021, 7, 16, 0, 0, 0, 0, ZONE_ID),
                12000L,
                5200L,
                10L,
                new BigDecimal("0.20000"),
                ChargeType.RECURRING
            ),
            // 2.5 year subscription with 46.667% discount
            new SellingPriceTestData(
                ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZONE_ID),
                ZonedDateTime.of(2023, 7, 1, 0, 0, 0, 0, ZONE_ID),
                1500L,
                2000L,
                5L,
                new BigDecimal("0.46667"),
                ChargeType.RECURRING
            ),
            // 3 year subscription with 55.556% discount
            new SellingPriceTestData(
                ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZONE_ID),
                ZonedDateTime.of(2024, 1, 1, 0, 0, 0, 0, ZONE_ID),
                1500L,
                2000L,
                5L,
                new BigDecimal("0.55556"),
                ChargeType.RECURRING
            ),
            // 1.5 year subscription of ONE_TIME charge and 50% discount
            new SellingPriceTestData(
                ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZONE_ID),
                ZonedDateTime.of(2022, 7, 1, 0, 0, 0, 0, ZONE_ID),
                1500L,
                750L,
                5L,
                new BigDecimal("0.50000"),
                ChargeType.ONE_TIME
            )
        );
    }

    private static class SellingPriceTestData {

        private final ZonedDateTime start;
        private final ZonedDateTime end;
        private final Long listPrice;
        private final Long soldAmount;
        private final Long quantity;
        private final BigDecimal expectedDiscount;
        private final ChargeType chargeType;

        public SellingPriceTestData(
            ZonedDateTime start,
            ZonedDateTime end,
            Long listPrice,
            Long soldAmount,
            Long quantity,
            BigDecimal expectedDiscount,
            ChargeType chargeType
        ) {
            this.start = start;
            this.end = end;
            this.listPrice = listPrice;
            this.soldAmount = soldAmount;
            this.quantity = quantity;
            this.expectedDiscount = expectedDiscount;
            this.chargeType = chargeType;
        }

        public ZonedDateTime getStart() {
            return start;
        }

        public ZonedDateTime getEnd() {
            return end;
        }

        public Long getListPrice() {
            return listPrice;
        }

        public Long getSoldAmount() {
            return soldAmount;
        }

        public Long getQuantity() {
            return quantity;
        }

        public BigDecimal getExpectedDiscount() {
            return expectedDiscount;
        }

        public ChargeType getChargeType() {
            return chargeType;
        }
    }
}
