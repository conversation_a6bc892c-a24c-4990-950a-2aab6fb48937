package com.subskribe.billy.invoice.service;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.foreignexchange.service.RealizedGainLossService;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.resources.json.invoice.InvoiceJson;
import com.subskribe.billy.resources.json.invoice.InvoiceJsonMapper;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.jooq.Configuration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class InvoiceEventServiceTest {

    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final String INVOICE_NUMBER = "INV-000111";

    private InvoiceEventService invoiceEventService;
    private EventPublishingService eventPublishingService;
    private final EntityGetService entityGetService = EntityGetServiceFixture.entityGetServiceFixture();
    private InvoiceJsonMapper invoiceJsonMapper;
    private ObjectMapper objectMapper;
    private Configuration configuration;

    @BeforeEach
    void setUp() {
        eventPublishingService = mock(EventPublishingService.class);
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        RealizedGainLossService realizedGainLossService = mock(RealizedGainLossService.class);
        FeatureService featureService = mock(FeatureService.class);
        invoiceJsonMapper = mock(InvoiceJsonMapper.class);
        objectMapper = mock(ObjectMapper.class);
        configuration = mock(Configuration.class);

        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        invoiceEventService = new InvoiceEventService(
            eventPublishingService,
            tenantIdProvider,
            realizedGainLossService,
            entityGetService,
            featureService,
            invoiceJsonMapper,
            objectMapper
        );
    }

    @Test
    void testV1IsPublished() {
        Invoice invoice = getInvoice();

        invoiceEventService.publishEventInTransaction(configuration, invoice);

        verify(eventPublishingService, times(1)).publishEventInTransaction(
            configuration,
            EventType.INVOICE_POSTED,
            TENANT_ID,
            invoice.getEntityId(),
            invoice.getCustomerAccountId(),
            INVOICE_NUMBER.getBytes(StandardCharsets.UTF_8)
        );
    }

    @Test
    void testV2IsPublished() throws Exception {
        String mockJson = "{\"invoiceNumber\":\"INV-000111\"}";
        Invoice invoice = getInvoice();
        InvoiceJson invoiceJson = getInvoiceJson();
        when(invoiceJsonMapper.invoiceToJson(invoice)).thenReturn(invoiceJson);
        when(objectMapper.writeValueAsBytes(invoiceJson)).thenReturn(mockJson.getBytes(StandardCharsets.UTF_8));

        invoiceEventService.publishEventInTransaction(configuration, invoice);

        verify(eventPublishingService, times(1)).publishEventInTransaction(
            configuration,
            EventType.INVOICE_POSTED_V2,
            TENANT_ID,
            invoice.getEntityId(),
            invoice.getCustomerAccountId(),
            mockJson.getBytes(StandardCharsets.UTF_8)
        );
    }

    @Test
    void testV1VoidIsPublished() {
        Invoice invoice = getInvoice();

        invoiceEventService.publishVoidEventInTransaction(configuration, invoice);

        verify(eventPublishingService, times(1)).publishEventInTransaction(
            configuration,
            EventType.INVOICE_VOIDED,
            TENANT_ID,
            invoice.getEntityId(),
            invoice.getCustomerAccountId(),
            INVOICE_NUMBER.getBytes(StandardCharsets.UTF_8)
        );
    }

    @Test
    void testV2VoidIsPublished() throws Exception {
        String mockJson = "{\"invoiceNumber\":\"INV-000111\"}";
        Invoice invoice = getInvoice();
        InvoiceJson invoiceJson = getInvoiceJson();
        when(invoiceJsonMapper.invoiceToJson(invoice)).thenReturn(invoiceJson);
        when(objectMapper.writeValueAsBytes(invoiceJson)).thenReturn(mockJson.getBytes(StandardCharsets.UTF_8));

        invoiceEventService.publishVoidEventInTransaction(configuration, invoice);

        verify(eventPublishingService, times(1)).publishEventInTransaction(
            configuration,
            EventType.INVOICE_VOIDED_V2,
            TENANT_ID,
            invoice.getEntityId(),
            invoice.getCustomerAccountId(),
            mockJson.getBytes(StandardCharsets.UTF_8)
        );
    }

    private Invoice getInvoice() {
        return new Invoice.InvoiceBuilder()
            .invoiceNumber(new Invoice.Number(INVOICE_NUMBER))
            .subscriptionId(RandomStringUtils.randomAlphanumeric(10))
            .customerAccountId(RandomStringUtils.randomAlphanumeric(10))
            .status(InvoiceStatus.POSTED)
            .total(BigDecimal.TEN)
            .currency("USD")
            .createInvoice();
    }

    private InvoiceJson getInvoiceJson() {
        InvoiceJson invoiceJson = new InvoiceJson();
        invoiceJson.setInvoiceNumber(INVOICE_NUMBER);
        return invoiceJson;
    }
}
