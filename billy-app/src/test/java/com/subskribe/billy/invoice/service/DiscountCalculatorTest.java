package com.subskribe.billy.invoice.service;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.invoice.model.DiscountResult;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;

class DiscountCalculatorTest {

    private final FeatureService featureService = FeatureServiceFixture.allEnabled();

    private DiscountCalculator discountCalculator;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        discountCalculator = new DiscountCalculator(featureService);
    }

    @Test
    public void calculateForNegativeAmountWithFixedDiscount() {
        BigDecimal discountAmount = new BigDecimal("5.15");
        BigDecimal listAmount = new BigDecimal("-10.5");
        DiscountResult resultForSingle1 = discountCalculator.calculateDiscountAmounts(
            listAmount,
            List.of(getFixedAmountDiscount(discountAmount)),
            null,
            -1
        );

        DiscountResult resultForSingle2 = discountCalculator.calculateDiscountAmounts(
            listAmount,
            List.of(getFixedAmountDiscount(discountAmount)),
            null,
            1
        );

        assertEquals(discountAmount.negate(), resultForSingle1.getTotalDiscountAmount());
        assertEquals(discountAmount.negate(), resultForSingle2.getTotalDiscountAmount());
    }

    @Test
    public void calculateForNegativeAmountWithPercentDiscount() {
        BigDecimal listAmount = new BigDecimal("-10.5");
        DiscountResult result = discountCalculator.calculateDiscountAmounts(listAmount, List.of(getPercentDiscount(new BigDecimal("0.25"))), null);

        assertEquals(1, result.getLineItemDiscountAmounts().size());
        assertEquals(0, result.getTenantDiscounts().size());
        assertEquals(new BigDecimal("-2.62"), result.getTotalDiscountAmount());
    }

    @Test
    public void calculateSellingAmountForMultipleFixedAmountDiscounts() {
        BigDecimal discountAmount = new BigDecimal("4.5");
        BigDecimal listAmount = new BigDecimal("10.5");
        DiscountResult resultForSingle = discountCalculator.calculateDiscountAmounts(
            listAmount,
            List.of(getFixedAmountDiscount(discountAmount), getFixedAmountDiscount(discountAmount)),
            null
        );
        DiscountResult resultForTen = discountCalculator.calculateDiscountAmounts(
            listAmount.multiply(BigDecimal.TEN),
            List.of(getFixedAmountDiscount(discountAmount), getFixedAmountDiscount(discountAmount)),
            null,
            10
        );

        assertEquals(Numbers.makeCurrencyScale(discountAmount.multiply(BigDecimal.valueOf(2))), resultForSingle.getTotalDiscountAmount());
        assertEquals(
            Numbers.makeCurrencyScale(discountAmount.multiply(BigDecimal.TEN).multiply(BigDecimal.valueOf(2))),
            resultForTen.getTotalDiscountAmount()
        );
    }

    @Test
    public void calculateSellingAmountForFixedAmountDiscountGreaterThanListPrice() {
        BigDecimal discountAmount = new BigDecimal("11.0");
        BigDecimal listAmount = new BigDecimal("10.50");
        DiscountResult resultForSingle = discountCalculator.calculateDiscountAmounts(
            listAmount,
            List.of(getFixedAmountDiscount(discountAmount)),
            null
        );
        DiscountResult resultForTen = discountCalculator.calculateDiscountAmounts(
            listAmount.multiply(BigDecimal.TEN),
            List.of(getFixedAmountDiscount(discountAmount)),
            null,
            10
        );

        assertEquals(1, resultForSingle.getLineItemDiscountAmounts().size());
        assertEquals(listAmount, resultForSingle.getTotalDiscountAmount()); // max discount amount is the list amount
        assertEquals(listAmount.multiply(BigDecimal.TEN), resultForTen.getTotalDiscountAmount());
    }

    @Test
    public void calculateSellingAmountForFixedAmountDiscount() {
        BigDecimal discountAmount = new BigDecimal("5.15");
        BigDecimal listAmount = new BigDecimal("10.5");
        DiscountResult resultForSingle = discountCalculator.calculateDiscountAmounts(
            listAmount,
            List.of(getFixedAmountDiscount(discountAmount)),
            null
        );
        DiscountResult resultForTen = discountCalculator.calculateDiscountAmounts(
            listAmount.multiply(BigDecimal.TEN),
            List.of(getFixedAmountDiscount(discountAmount)),
            null,
            10
        );

        assertEquals(1, resultForSingle.getLineItemDiscountAmounts().size());
        assertEquals(discountAmount, resultForSingle.getTotalDiscountAmount());
        assertEquals(discountAmount.multiply(BigDecimal.TEN), resultForTen.getTotalDiscountAmount());
        assertEquals(new BigDecimal(".49"), Numbers.makeCurrencyScale(resultForSingle.getTotalDiscountPercent()));
        assertEquals(new BigDecimal(".49"), Numbers.makeCurrencyScale(resultForTen.getTotalDiscountPercent()));
    }

    @Test
    public void calculateMixedDiscounts() {
        BigDecimal discountAmount = new BigDecimal("5");
        BigDecimal discountPercent = new BigDecimal("0.2");
        BigDecimal listAmount = new BigDecimal("12");
        long quantity = 10L;

        DiscountResult percentFirstResult = discountCalculator.calculateDiscountAmounts(
            listAmount,
            List.of(getPercentDiscount(discountPercent), getFixedAmountDiscount(discountAmount)),
            null
        );

        DiscountResult fixedAmountFirstResult = discountCalculator.calculateDiscountAmounts(
            listAmount,
            List.of(getFixedAmountDiscount(discountAmount), getPercentDiscount(discountPercent)),
            null
        );

        DiscountResult fixedAmountFirstWithQuantityResult = discountCalculator.calculateDiscountAmounts(
            listAmount.multiply(BigDecimal.valueOf(quantity)),
            List.of(getFixedAmountDiscount(discountAmount), getPercentDiscount(discountPercent)),
            null,
            quantity
        );

        assertEquals(new BigDecimal("7.40"), percentFirstResult.getTotalDiscountAmount()); // 12 - (12 * 0.8 -5)
        assertEquals(new BigDecimal("6.40"), fixedAmountFirstResult.getTotalDiscountAmount()); // 12 - ((12 - 5) * 0.8)
        assertEquals(new BigDecimal("64.00"), fixedAmountFirstWithQuantityResult.getTotalDiscountAmount()); // (120 - ((120 - 5 * 10) * 0.8))
    }

    @Test
    public void calculateSellingAmountForSingleDiscount() {
        BigDecimal listAmount = new BigDecimal("10.5");
        DiscountResult result = discountCalculator.calculateDiscountAmounts(listAmount, List.of(getPercentDiscount(new BigDecimal("0.25"))), null);

        assertEquals(1, result.getLineItemDiscountAmounts().size());
        assertEquals(0, result.getTenantDiscounts().size());
        assertEquals(new BigDecimal("2.62"), result.getTotalDiscountAmount());
        assertEquals(new BigDecimal("0.25"), Numbers.makeCurrencyScale(result.getTotalDiscountPercent()));
    }

    @Test
    public void calculateSellingAmountForMultipleDiscounts() {
        BigDecimal listAmount = new BigDecimal("10");
        DiscountResult result = discountCalculator.calculateDiscountAmounts(
            listAmount,
            List.of(
                getPercentDiscount(new BigDecimal("0.33333")),
                getPercentDiscount(new BigDecimal("0.33333")),
                getPercentDiscount(new BigDecimal("0.33333"))
            ),
            null
        );

        assertEquals(3, result.getLineItemDiscountAmounts().size());
        assertEquals(0, result.getTenantDiscounts().size());
        assertEquals(Numbers.makeCurrencyScale(new BigDecimal("7.03")), result.getTotalDiscountAmount());
        assertEquals(Numbers.makeCalculationScale(new BigDecimal("0.70")), result.getTotalDiscountPercent());
    }

    @Test
    public void calculateSellingPriceWithLineAndTenantDiscounts() {
        BigDecimal listAmount = new BigDecimal("10");
        DiscountResult result = discountCalculator.calculateDiscountAmounts(
            listAmount,
            List.of(getPercentDiscount(new BigDecimal("0.33333"))),
            List.of(getTenantDiscount(new BigDecimal("0.33333")), getTenantDiscount(new BigDecimal("0.33333")))
        );

        BigDecimal totalLineDiscountAmount = getSum(result.getLineItemDiscountAmounts());
        BigDecimal totalTenantDiscountAmount = getSum(
            result.getTenantDiscounts().stream().map(TenantDiscountLineItem::getAmount).collect(Collectors.toList())
        );

        assertEquals(1, result.getLineItemDiscountAmounts().size());
        assertEquals(2, result.getTenantDiscounts().size());
        assertEquals(Numbers.makeCurrencyScale(new BigDecimal("7.03")), result.getTotalDiscountAmount());
        assertEquals(totalLineDiscountAmount.add(totalTenantDiscountAmount), result.getTotalDiscountAmount());
    }

    @Test
    public void discountRatioWorksAsExpectedWhenListAmountIsZero() {
        BigDecimal discountRatio = DiscountCalculator.calculateDiscountRatio(BigDecimal.valueOf(10), BigDecimal.ZERO);
        Assertions.assertThat(discountRatio).isZero();
    }

    @Test
    public void singleDiscountWithQuantityWithRounding() {
        BigDecimal listAmount = new BigDecimal("157.5");
        DiscountResult result = discountCalculator.calculateDiscountAmounts(listAmount, List.of(getPercentDiscount(new BigDecimal("0.33"))), null);

        // 33% of 157.5 = 51.975. 157.5 - 51.975 = 105.525 = 105.53 rounded. Discount amount = 157.5 - 105.53 = 51.97
        assertEquals(new BigDecimal("51.97"), result.getLineItemDiscountAmounts().get(0));
        assertEquals(new BigDecimal("51.97"), result.getTotalDiscountAmount());
    }

    @Test
    public void multipleDiscountWithRounding() {
        BigDecimal listAmount = new BigDecimal("39999");
        DiscountResult result = discountCalculator.calculateDiscountAmounts(
            listAmount,
            List.of(getPercentDiscount(new BigDecimal("0.10"))),
            List.of(getTenantDiscount(new BigDecimal("0.15")))
        );

        // 10% of 39999 = 3999.90
        assertEquals(new BigDecimal("3999.90"), result.getLineItemDiscountAmounts().get(0));

        // 15% of 39999 - 3999.90 = 35999.1 x 0.15 = 5399.865. Starting list amount = 39999 - 3999.90 = 35999.10. 35999.10 - 5399.865 = 30600.235 = 30600.24 rounded => discount = 35999.10 - 30600.24 = 5398.86
        assertEquals(new BigDecimal("5399.86"), result.getTenantDiscounts().get(0).getAmount());
        assertEquals(new BigDecimal("9399.76"), result.getTotalDiscountAmount());
    }

    private static DiscountDetail getPercentDiscount(BigDecimal percent) {
        var discount = new DiscountDetail();
        discount.setPercent(percent);
        return discount;
    }

    private static DiscountDetail getFixedAmountDiscount(BigDecimal amount) {
        var discount = new DiscountDetail();
        discount.setDiscountAmount(amount);
        return discount;
    }

    private static TenantDiscountLineItem getTenantDiscount(BigDecimal percent) {
        var tenantDiscount = new TenantDiscountLineItem();
        tenantDiscount.setId(UUID.randomUUID().toString());
        tenantDiscount.setPercent(percent);
        return tenantDiscount;
    }

    private static BigDecimal getSum(List<BigDecimal> numbers) {
        return numbers.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
