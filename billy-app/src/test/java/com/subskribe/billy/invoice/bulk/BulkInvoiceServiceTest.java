package com.subskribe.billy.invoice.bulk;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.RETURNS_MOCKS;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.entity.fixtures.EntityContextResolverFixture;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.invoice.automated.service.AutomatedInvoiceRuleGetService;
import com.subskribe.billy.invoice.bulk.db.BulkInvoiceDAO;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRun;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunExclusions;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunItem;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunPhase;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunSelector;
import com.subskribe.billy.invoice.bulk.model.BulkInvoiceRunStatus;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceRunIdGenerator;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceSelectorService;
import com.subskribe.billy.invoice.bulk.service.BulkInvoiceService;
import com.subskribe.billy.invoice.exception.InvoiceAlreadyExistsException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceChargeInclusionOption;
import com.subskribe.billy.invoice.model.InvoiceGenerationCheckResult;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.shared.pagination.PageRequest;
import com.subskribe.billy.shared.pagination.PageResult;
import com.subskribe.billy.shared.task.queue.scheduler.TaskDispatcher;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionImpl;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

@Disabled("The tests are for the old BIR flow. They need to be updated with the revamp flow.")
public class BulkInvoiceServiceTest {

    private final BulkInvoiceDAO mockBulkInvoiceDao = mock(BulkInvoiceDAO.class);

    private final EntityContextResolver entityContextResolver = EntityContextResolverFixture.build();

    private final SubscriptionGetService mockSubscriptionGetService = mock(SubscriptionGetService.class);

    private final TenantSettingService mockTenantSettingService = mock(TenantSettingService.class);

    private final InvoiceService mockInvoiceService = mock(InvoiceService.class);

    private final BulkInvoiceSelectorService mockSelectorService = mock(BulkInvoiceSelectorService.class);

    private final AccountingPeriodService mockAccountPeriodService = mock(AccountingPeriodService.class);

    private final BulkInvoiceRunIdGenerator mockBulkInvoiceRunIdGenerator = mock(BulkInvoiceRunIdGenerator.class);

    private final FeatureService mockFeatureService = mock(FeatureService.class);

    private final AutomatedInvoiceRuleGetService mockAutomatedInvoiceRuleGetService = mock(AutomatedInvoiceRuleGetService.class);

    private final TaskDispatcher mockTaskDispatcher = mock(TaskDispatcher.class);

    private final TenantIdProvider mockTenantIdProvider = mock(TenantIdProvider.class);

    private final InvoiceRetrievalService mockInvoiceRetrievalService = mock(InvoiceRetrievalService.class);

    private final BulkInvoiceService bulkInvoiceService = new BulkInvoiceService(
        mockBulkInvoiceDao,
        entityContextResolver,
        mockSubscriptionGetService,
        mockTenantSettingService,
        mockInvoiceService,
        mockSelectorService,
        mockAccountPeriodService,
        mockBulkInvoiceRunIdGenerator,
        mockFeatureService,
        mockAutomatedInvoiceRuleGetService,
        mockTaskDispatcher,
        mockTenantIdProvider,
        mockInvoiceRetrievalService
    );

    private static final UUID RUN_ID = UUID.randomUUID();

    private static final String RUN_NAME = "bulk invoice run name";

    private static final String RUN_DESCRIPTION = "bulk invoice run description";

    private static final String ACCOUNT_PREFIX = "ACCT-";

    private static final String SUBSCRIPTION_PREFIX = "SUB-";

    private final List<String> mockSubscriptionIdExclusionList = mock(List.class);

    private BulkInvoiceRun getBulkInvoiceRun(BulkInvoiceRunPhase phase, BulkInvoiceRunStatus status) {
        BulkInvoiceRun bulkInvoiceRun = new BulkInvoiceRun();
        bulkInvoiceRun.setId(RUN_ID.toString());
        bulkInvoiceRun.setName(RUN_NAME);
        bulkInvoiceRun.setDescription(RUN_DESCRIPTION);
        bulkInvoiceRun.setStatus(status);
        bulkInvoiceRun.setPhase(phase);
        bulkInvoiceRun.setChargeInclusionOption(InvoiceChargeInclusionOption.INCLUDE_USAGE);
        bulkInvoiceRun.setTargetDate(Instant.now().getEpochSecond());
        return bulkInvoiceRun;
    }

    private BulkInvoiceRun getBulkInvoiceRun(BulkInvoiceRunPhase phase) {
        return getBulkInvoiceRun(phase, BulkInvoiceRunStatus.PROCESSING);
    }

    private List<BulkInvoiceRunItem> getBulkInvoiceRunItems(int size) {
        return IntStream.of(size).mapToObj(this::createBulkInvoiceRunItem).collect(Collectors.toList());
    }

    private BulkInvoiceRunItem createBulkInvoiceRunItem(int num) {
        BulkInvoiceRunItem runItem = new BulkInvoiceRunItem();
        runItem.setRunId(RUN_ID.toString());
        runItem.setAccountId(ACCOUNT_PREFIX + num);
        runItem.setSubscriptionId(SUBSCRIPTION_PREFIX + num);
        return runItem;
    }

    @BeforeEach
    public void setup() {
        SubscriptionEntity subscriptionEntity = new SubscriptionEntity();
        Subscription mockSubscription = new SubscriptionImpl(subscriptionEntity, null);
        when(mockSubscriptionGetService.getSubscription(any())).thenReturn(mockSubscription);
    }

    @Test
    public void getBulkInvoiceRunFailsIfRunDoesNotExist() {
        when(mockBulkInvoiceDao.getBulkInvoiceRun(RUN_ID)).thenReturn(Optional.empty());
        assertThrows(ObjectNotFoundException.class, () -> bulkInvoiceService.getBulkInvoiceRun(RUN_ID));
    }

    @Test
    public void getInvoiceRunItemsFailsIfNoBulkInvoiceRunExists() {
        when(mockBulkInvoiceDao.getBulkInvoiceRun(RUN_ID)).thenReturn(Optional.empty());
        assertThrows(ObjectNotFoundException.class, () -> bulkInvoiceService.getBulkInvoiceRunItems(RUN_ID));
    }

    @Test
    public void getInvoiceRunItemsFailsIfInvoicesAreNotGeneratedOrPosted() {
        when(mockBulkInvoiceDao.getBulkInvoiceRun(RUN_ID)).thenReturn(Optional.of(getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATING)));
        assertThrows(IllegalStateException.class, () -> bulkInvoiceService.getBulkInvoiceRunItems(RUN_ID));
    }

    @Test
    public void postInvoicesExclusionListContainsMoreElementsThanInvoiceRunItemsMaximum() {
        when(mockSubscriptionIdExclusionList.size()).thenReturn(1000);
        assertThrows(IllegalArgumentException.class, () -> bulkInvoiceService.postInvoicesForBulkInvoiceRun(RUN_ID, mockSubscriptionIdExclusionList));
    }

    @Test
    public void readBulkInvoiceRunExecutesIfRunIsAlreadyProcessingAndInvoicesGenerated() {
        BulkInvoiceRun bulkInvoiceRun = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATED);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.of(bulkInvoiceRun));
        bulkInvoiceService.processInvoiceRunForTenant();
        verify(mockBulkInvoiceDao, never()).moveInvoiceRunToProcessing(bulkInvoiceRun);
    }

    @Test
    public void readBulkInvoiceRunExecutesIfRunIsAlreadyProcessingAndInvoicesPosted() {
        BulkInvoiceRun bulkInvoiceRun = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_POSTED);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.of(bulkInvoiceRun));
        bulkInvoiceService.processInvoiceRunForTenant();
        verify(mockBulkInvoiceDao, never()).moveInvoiceRunToProcessing(any(BulkInvoiceRun.class));
        verify(mockBulkInvoiceDao).forceUpdateInvoiceRunToSuccess(bulkInvoiceRun, BulkInvoiceRunPhase.INVOICES_POSTED);
    }

    @Test
    public void readBulkInvoiceRunDoesNothingIfNoCreatedRunExists() {
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.empty());
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.CREATED)).thenReturn(Optional.empty());

        bulkInvoiceService.processInvoiceRunForTenant();

        verify(mockBulkInvoiceDao, never()).moveInvoiceRunToProcessing(any(BulkInvoiceRun.class));
    }

    @Test
    public void invoiceRunItemsFailIfInvoiceGenerationFails() {
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.empty());
        BulkInvoiceRun bulkInvoiceRunNotStarted = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICE_GENERATION_NOT_STARTED);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.CREATED)).thenReturn(Optional.of(bulkInvoiceRunNotStarted));
        BulkInvoiceRun bulkInvoiceRunGenerating = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATING);
        when(mockBulkInvoiceDao.moveInvoiceRunToProcessing(bulkInvoiceRunNotStarted)).thenReturn(bulkInvoiceRunGenerating);

        List<BulkInvoiceRunItem> runItems = getBulkInvoiceRunItems(10);
        when(mockBulkInvoiceDao.getInvoiceRunItemsByRunId(RUN_ID.toString())).thenReturn(runItems);
        when(
            mockInvoiceService.generateInvoice(any(String.class), any(Instant.class), any(), any(InvoiceChargeInclusionOption.class), any(), any())
        ).thenReturn(Optional.empty());

        bulkInvoiceService.processInvoiceRunForTenant();

        runItems.forEach(runItem -> assertFalse(runItem.isDraftInvoiceGenerated()));
        verify(mockBulkInvoiceDao, times(runItems.size())).forceUpdateInvoiceRunItemToFailed(any(BulkInvoiceRunItem.class), any(String.class));
    }

    @Test
    public void generateDraftInvoiceChecksReturnsNoInvoices() {
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.empty());
        BulkInvoiceRun bulkInvoiceRunNotStarted = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICE_GENERATION_NOT_STARTED);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.CREATED)).thenReturn(Optional.of(bulkInvoiceRunNotStarted));
        BulkInvoiceRun bulkInvoiceRunGenerating = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATING);
        when(mockBulkInvoiceDao.moveInvoiceRunToProcessing(bulkInvoiceRunNotStarted)).thenReturn(bulkInvoiceRunGenerating);
        BulkInvoiceRunSelector selector = new BulkInvoiceRunSelector(new BulkInvoiceRunExclusions(List.of()));
        when(mockSelectorService.getDefaultBulkInvoiceRunSelector()).thenReturn(selector);

        List<BulkInvoiceRunItem> runItems = new ArrayList<>();
        when(mockBulkInvoiceDao.getInvoiceRunItemsByRunId(RUN_ID.toString())).thenReturn(runItems);
        when(mockSubscriptionGetService.getAllSubscriptionsWithFuzzyPagination(any(PageRequest.class), any(Set.class))).thenReturn(
            PageResult.withResultAndNextToken(List.of(), null)
        );

        bulkInvoiceService.processInvoiceRunForTenant();

        verify(mockBulkInvoiceDao).forceUpdateInvoiceRunToFailed(bulkInvoiceRunGenerating, "No Invoices to generate for the provided target date");
    }

    @Test
    public void generateDraftInvoiceChecksReturnsSomeInvoices() {
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.empty());
        BulkInvoiceRun bulkInvoiceRunNotStarted = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICE_GENERATION_NOT_STARTED);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.CREATED)).thenReturn(Optional.of(bulkInvoiceRunNotStarted));
        BulkInvoiceRun bulkInvoiceRunGenerating = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATING);
        when(mockBulkInvoiceDao.moveInvoiceRunToProcessing(bulkInvoiceRunNotStarted)).thenReturn(bulkInvoiceRunGenerating);
        BulkInvoiceRunSelector selector = new BulkInvoiceRunSelector(new BulkInvoiceRunExclusions(List.of()));
        when(mockSelectorService.getDefaultBulkInvoiceRunSelector()).thenReturn(selector);

        List<BulkInvoiceRunItem> runItems = new ArrayList<>();
        when(mockBulkInvoiceDao.getInvoiceRunItemsByRunId(RUN_ID.toString())).thenReturn(runItems);
        String subscription1 = SUBSCRIPTION_PREFIX + "1111111";
        String subscription2 = SUBSCRIPTION_PREFIX + "2222222";
        when(mockSubscriptionGetService.getAllSubscriptionsWithFuzzyPagination(any(PageRequest.class), any(Set.class))).thenReturn(
            PageResult.withResultAndNextToken(List.of(subscription1, subscription2), null)
        );

        InvoiceGenerationCheckResult invoiceGeneratedResult = mock(InvoiceGenerationCheckResult.class, RETURNS_MOCKS);
        when(
            mockInvoiceService.performInvoiceGenerationCheck(eq(subscription1), any(Instant.class), any(InvoiceChargeInclusionOption.class))
        ).thenReturn(invoiceGeneratedResult);
        when(invoiceGeneratedResult.isInvoiceToBeGenerated()).thenReturn(true);
        when(invoiceGeneratedResult.getExistingDraftInvoice()).thenReturn(Optional.empty());

        InvoiceGenerationCheckResult existingDraftInvoiceResult = mock(InvoiceGenerationCheckResult.class, RETURNS_MOCKS);
        when(
            mockInvoiceService.performInvoiceGenerationCheck(eq(subscription2), any(Instant.class), any(InvoiceChargeInclusionOption.class))
        ).thenReturn(existingDraftInvoiceResult);
        when(existingDraftInvoiceResult.isInvoiceToBeGenerated()).thenReturn(false);
        Invoice mockInvoice = mock(Invoice.class, RETURNS_MOCKS);
        when(mockInvoice.getCreatedOn()).thenReturn(Instant.now());
        when(existingDraftInvoiceResult.getExistingDraftInvoice()).thenReturn(Optional.of(mockInvoice));

        bulkInvoiceService.processInvoiceRunForTenant();

        ArgumentCaptor<List<BulkInvoiceRunItem>> runItemsCaptor = ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<String> entityIdCaptor = ArgumentCaptor.forClass(String.class);
        verify(mockBulkInvoiceDao).insertInvoiceRunItemsInTransaction(runItemsCaptor.capture(), entityIdCaptor.capture());
        assertEquals(1, runItemsCaptor.getValue().size());
        verify(mockBulkInvoiceDao).forceUpdateInvoiceRunPhaseTo(bulkInvoiceRunGenerating, BulkInvoiceRunPhase.INVOICES_GENERATED);
    }

    @Test
    public void createdRunGetsMovedToProcessing() {
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.empty());
        BulkInvoiceRun bulkInvoiceRunNotStarted = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICE_GENERATION_NOT_STARTED);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.CREATED)).thenReturn(Optional.of(bulkInvoiceRunNotStarted));
        BulkInvoiceRun bulkInvoiceRunGenerating = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATING);
        when(mockBulkInvoiceDao.moveInvoiceRunToProcessing(bulkInvoiceRunNotStarted)).thenReturn(bulkInvoiceRunGenerating);

        List<BulkInvoiceRunItem> runItems = getBulkInvoiceRunItems(10);
        when(mockBulkInvoiceDao.getInvoiceRunItemsByRunId(RUN_ID.toString())).thenReturn(runItems);
        when(
            mockInvoiceService.generateInvoice(any(String.class), any(Instant.class), any(), any(InvoiceChargeInclusionOption.class), any(), any())
        ).thenReturn(Optional.of(mock(Invoice.class)));

        bulkInvoiceService.processInvoiceRunForTenant();

        verify(mockBulkInvoiceDao).moveInvoiceRunToProcessing(bulkInvoiceRunNotStarted);
        verify(mockBulkInvoiceDao, times(runItems.size())).forceUpdateRunItemWithDraftInvoice(any(BulkInvoiceRunItem.class), any(Invoice.class));
        runItems.forEach(runItem -> assertFalse(runItem.isDraftInvoiceGenerated()));
        verify(mockBulkInvoiceDao).forceUpdateInvoiceRunPhaseTo(bulkInvoiceRunGenerating, BulkInvoiceRunPhase.INVOICES_GENERATED);
    }

    @Test
    public void draftInvoiceAlreadyExistsForSubscriptionButIsNotRetrieved() {
        BulkInvoiceRun bulkInvoiceRun = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATING);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.of(bulkInvoiceRun));

        List<BulkInvoiceRunItem> runItems = getBulkInvoiceRunItems(1);
        when(mockBulkInvoiceDao.getInvoiceRunItemsByRunId(RUN_ID.toString())).thenReturn(runItems);
        when(
            mockInvoiceService.generateInvoice(any(String.class), any(Instant.class), any(), any(InvoiceChargeInclusionOption.class), any(), any())
        ).thenThrow(InvoiceAlreadyExistsException.class);
        when(mockInvoiceService.getDraftInvoiceForSubscription(any(String.class))).thenReturn(Optional.empty());

        bulkInvoiceService.processInvoiceRunForTenant();

        verify(mockBulkInvoiceDao, never()).forceUpdateRunItemWithDraftInvoice(any(BulkInvoiceRunItem.class), any(Invoice.class));
    }

    @Test
    public void existingDraftInvoiceIsUsedForBulkInvoiceRunItem() {
        BulkInvoiceRun bulkInvoiceRun = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATING);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.of(bulkInvoiceRun));

        List<BulkInvoiceRunItem> runItems = getBulkInvoiceRunItems(1);
        when(mockBulkInvoiceDao.getInvoiceRunItemsByRunId(RUN_ID.toString())).thenReturn(runItems);
        when(
            mockInvoiceService.generateInvoice(any(String.class), any(Instant.class), any(), any(InvoiceChargeInclusionOption.class), any(), any())
        ).thenThrow(InvoiceAlreadyExistsException.class);
        Invoice mockInvoice = mock(Invoice.class);
        when(mockInvoiceService.getDraftInvoiceForSubscription(any(String.class))).thenReturn(Optional.of(mockInvoice));

        bulkInvoiceService.processInvoiceRunForTenant();

        verify(mockBulkInvoiceDao).forceUpdateRunItemWithDraftInvoice(runItems.get(0), mockInvoice);
    }

    @Test
    public void postingDraftInvoicesSucceeds() {
        BulkInvoiceRun bulkInvoiceRunPosting = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_POSTING);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.of(bulkInvoiceRunPosting));

        BulkInvoiceRunItem runItem = createBulkInvoiceRunItem(1);
        runItem.setDraftInvoiceNumber("DRAFT-123");
        runItem.setExcludedForPosting(false);
        List<BulkInvoiceRunItem> runItems = List.of(runItem);
        when(mockBulkInvoiceDao.getInvoiceRunItemsByRunId(RUN_ID.toString())).thenReturn(runItems);

        Invoice mockInvoice = mock(Invoice.class);
        when(mockInvoiceService.postInvoice(any(Invoice.Number.class))).thenReturn(mockInvoice);
        when(mockInvoice.getInvoiceNumber()).thenReturn(mock(Invoice.Number.class));

        bulkInvoiceService.processInvoiceRunForTenant();

        verify(mockBulkInvoiceDao).forceUpdateRunItemWithPostedInvoiceNumber(eq(runItem), any(Invoice.Number.class));
        verify(mockInvoiceService, never()).deleteInvoice(any(Invoice.Number.class));
        verify(mockBulkInvoiceDao, never()).moveInvoiceRunToProcessing(bulkInvoiceRunPosting);
        verify(mockBulkInvoiceDao).forceUpdateInvoiceRunToSuccess(bulkInvoiceRunPosting, BulkInvoiceRunPhase.INVOICES_POSTED);
    }

    @Test
    public void postingDraftInvoicesExcludesInvoice() {
        BulkInvoiceRun bulkInvoiceRunPosting = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_POSTING);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.of(bulkInvoiceRunPosting));

        BulkInvoiceRunItem runItem = createBulkInvoiceRunItem(1);
        runItem.setDraftInvoiceNumber("DRAFT-123");
        runItem.setExcludedForPosting(true);
        List<BulkInvoiceRunItem> runItems = List.of(runItem);
        when(mockBulkInvoiceDao.getInvoiceRunItemsByRunId(RUN_ID.toString())).thenReturn(runItems);

        bulkInvoiceService.processInvoiceRunForTenant();

        verify(mockInvoiceService).deleteInvoice(any(Invoice.Number.class));
        verify(mockBulkInvoiceDao, never()).forceUpdateRunItemWithPostedInvoiceNumber(eq(runItem), any(Invoice.Number.class));
        verify(mockBulkInvoiceDao, never()).moveInvoiceRunToProcessing(bulkInvoiceRunPosting);
        verify(mockBulkInvoiceDao).forceUpdateInvoiceRunToSuccess(bulkInvoiceRunPosting, BulkInvoiceRunPhase.INVOICES_POSTED);
    }

    @Test
    public void postingDraftInvoicesExcludesInvoiceFailsIfInvoiceInIllegalState() {
        BulkInvoiceRun bulkInvoiceRunPosting = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_POSTING);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.of(bulkInvoiceRunPosting));

        BulkInvoiceRunItem runItem = createBulkInvoiceRunItem(1);
        runItem.setDraftInvoiceNumber("DRAFT-123");
        runItem.setExcludedForPosting(true);
        List<BulkInvoiceRunItem> runItems = List.of(runItem);
        when(mockBulkInvoiceDao.getInvoiceRunItemsByRunId(RUN_ID.toString())).thenReturn(runItems);
        IllegalStateException illegalStateException = new IllegalStateException("illegal state");
        when(mockInvoiceService.deleteInvoice(any(Invoice.Number.class))).thenThrow(illegalStateException);

        bulkInvoiceService.processInvoiceRunForTenant();

        verify(mockBulkInvoiceDao).forceUpdateInvoiceRunItemToFailed(eq(runItem), any(String.class));
        verify(mockBulkInvoiceDao, never()).forceUpdateRunItemWithPostedInvoiceNumber(eq(runItem), any(Invoice.Number.class));
        verify(mockBulkInvoiceDao, never()).moveInvoiceRunToProcessing(bulkInvoiceRunPosting);
        // run succeeds even if invoice run item fails
        verify(mockBulkInvoiceDao).forceUpdateInvoiceRunToSuccess(bulkInvoiceRunPosting, BulkInvoiceRunPhase.INVOICES_POSTED);
    }

    @Test
    public void postingDraftInvoicesFailsWhenNotFound() {
        BulkInvoiceRun bulkInvoiceRunPosting = getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_POSTING);
        when(mockBulkInvoiceDao.getInvoiceRunInSingletonStatus(BulkInvoiceRunStatus.PROCESSING)).thenReturn(Optional.of(bulkInvoiceRunPosting));

        BulkInvoiceRunItem runItem = createBulkInvoiceRunItem(1);
        runItem.setDraftInvoiceNumber("DRAFT-123");
        runItem.setExcludedForPosting(false);
        List<BulkInvoiceRunItem> runItems = List.of(runItem);
        when(mockBulkInvoiceDao.getInvoiceRunItemsByRunId(RUN_ID.toString())).thenReturn(runItems);
        ObjectNotFoundException objectNotFoundException = mock(ObjectNotFoundException.class);
        when(mockInvoiceService.postInvoice(any(Invoice.Number.class))).thenThrow(objectNotFoundException);
        when(objectNotFoundException.getMessage()).thenReturn("not found");

        bulkInvoiceService.processInvoiceRunForTenant();

        verify(mockBulkInvoiceDao).forceUpdateInvoiceRunItemToFailed(eq(runItem), any(String.class));
        verify(mockInvoiceService, never()).deleteInvoice(any(Invoice.Number.class));
        verify(mockBulkInvoiceDao, never()).moveInvoiceRunToProcessing(bulkInvoiceRunPosting);
        // run succeeds even if invoice run item fails
        verify(mockBulkInvoiceDao).forceUpdateInvoiceRunToSuccess(bulkInvoiceRunPosting, BulkInvoiceRunPhase.INVOICES_POSTED);
    }

    @Test
    public void postInvoicesExcludesRunItems() {
        UUID runId = UUID.fromString(getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATED).getId());
        List<String> exclusionList = List.of(SUBSCRIPTION_PREFIX + "1111111");

        bulkInvoiceService.postInvoicesForBulkInvoiceRun(runId, exclusionList);

        verify(mockBulkInvoiceDao).postInvoicesForBulkInvoiceRun(runId, exclusionList);
    }

    @Test
    public void postInvoicesWithNoErrorsIfExclusionListIsNull() {
        UUID runId = UUID.fromString(getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATED).getId());

        bulkInvoiceService.postInvoicesForBulkInvoiceRun(runId, null);

        verify(mockBulkInvoiceDao).postInvoicesForBulkInvoiceRun(eq(runId), any(List.class));
    }

    @Test
    public void postInvoicesWithTooLargeExclusionListThrowsError() {
        UUID runId = UUID.fromString(getBulkInvoiceRun(BulkInvoiceRunPhase.INVOICES_GENERATED).getId());
        List<String> exclusionList = mock(List.class);
        when(exclusionList.size()).thenReturn(1000);

        assertThrows(IllegalArgumentException.class, () -> bulkInvoiceService.postInvoicesForBulkInvoiceRun(runId, exclusionList));
    }
}
