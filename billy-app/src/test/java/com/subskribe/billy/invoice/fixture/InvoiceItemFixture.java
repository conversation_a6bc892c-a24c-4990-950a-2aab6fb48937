package com.subskribe.billy.invoice.fixture;

import com.subskribe.billy.invoice.model.InvoiceItem;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

public class InvoiceItemFixture {

    public static InvoiceItem.InvoiceItemBuilder getDefault() {
        return new InvoiceItem.InvoiceItemBuilder()
            .id(new InvoiceItem.Id(UUID.randomUUID().toString()))
            .invoiceLineNumber("1")
            .planId("PLAN-123456")
            .chargeId("CHRG-123456")
            .amount(new BigDecimal("1234"))
            .functionalAmount(new BigDecimal("1234"))
            .taxAmount(BigDecimal.ZERO)
            .functionalTaxAmount(BigDecimal.ZERO)
            .discountAmount(BigDecimal.ZERO)
            .functionalDiscountAmount(BigDecimal.ZERO)
            .quantity(45L)
            .periodStartDate(Instant.parse("2024-07-29T07:00:00Z"))
            .periodEndDate(Instant.parse("2024-08-29T07:00:00Z"));
    }
}
