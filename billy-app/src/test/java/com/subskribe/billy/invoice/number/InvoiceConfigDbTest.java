package com.subskribe.billy.invoice.number;

import static com.subskribe.billy.jooq.default_schema.tables.TenantInvoiceConfiguration.TENANT_INVOICE_CONFIGURATION;
import static com.subskribe.billy.jooq.default_schema.tables.TenantInvoiceNumberSequence.TENANT_INVOICE_NUMBER_SEQUENCE;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.entity.model.NumberConfig;
import com.subskribe.billy.entity.model.NumberScheme;
import com.subskribe.billy.invoice.db.InvoiceConfigDAO;
import com.subskribe.billy.jooq.default_schema.tables.records.TenantInvoiceConfigurationRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.TenantInvoiceNumberSequenceRecord;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.WithDb;
import java.io.IOException;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.jooq.DSLContext;
import org.jooq.exception.IntegrityConstraintViolationException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class InvoiceConfigDbTest extends WithDb {

    private static final String TENANT_ID = "27b59431-0cc7-4c87-883b-be345e9bd525";
    private static final String INVOICE_CONFIG_ID_1 = UUID.randomUUID().toString();
    private static final String INVOICE_CONFIG_ID_2 = UUID.randomUUID().toString();

    private final TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);

    private DSLContext tenantDSLContext;

    @BeforeAll
    public void setUp() throws IOException {
        tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(TENANT_ID, dslContextProvider);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
    }

    // a valid insert to invoice config table should SUCCEED
    @Test
    @Order(1)
    public void testInsertConfigWorks() {
        TenantInvoiceConfigurationRecord record = new TenantInvoiceConfigurationRecord();
        record.setTenantId(TENANT_ID);
        record.setInvoiceConfigId(INVOICE_CONFIG_ID_1);
        var savedRecord = tenantDSLContext.insertInto(TENANT_INVOICE_CONFIGURATION).set(record).returning().fetchOne();
        Assertions.assertThat(savedRecord.getTenantId()).isEqualTo(TENANT_ID);
        Assertions.assertThat(savedRecord.getId()).isNotNull();
        Assertions.assertThat(savedRecord.getInvoiceConfigId()).isEqualTo(INVOICE_CONFIG_ID_1);
        Assertions.assertThat(savedRecord.getCreatedOn()).isNotNull();
        Assertions.assertThat(savedRecord.getUpdatedOn()).isNotNull();
    }

    // insert to invoice config table with duplicate tenant id, but different invoice config id should SUCCEED
    @Test
    @Order(2)
    public void testConfigWithSameTenantWorks() {
        TenantInvoiceConfigurationRecord record = new TenantInvoiceConfigurationRecord();
        record.setTenantId(TENANT_ID);
        record.setInvoiceConfigId(INVOICE_CONFIG_ID_2);
        var savedRecord = tenantDSLContext.insertInto(TENANT_INVOICE_CONFIGURATION).set(record).returning().fetchOne();

        Assertions.assertThat(savedRecord.getTenantId()).isEqualTo(TENANT_ID);
        Assertions.assertThat(savedRecord.getId()).isNotNull();
        Assertions.assertThat(savedRecord.getInvoiceConfigId()).isEqualTo(INVOICE_CONFIG_ID_2);
        Assertions.assertThat(savedRecord.getCreatedOn()).isNotNull();
        Assertions.assertThat(savedRecord.getUpdatedOn()).isNotNull();
    }

    // insert to invoice config table with duplicate invoice config id should FAIL
    @Test
    @Order(2)
    public void testConfigWithSameInvoiceConfigIdFails() {
        TenantInvoiceConfigurationRecord record = new TenantInvoiceConfigurationRecord();
        record.setTenantId(TENANT_ID);
        record.setInvoiceConfigId(INVOICE_CONFIG_ID_1);

        Assertions.assertThatThrownBy(() -> tenantDSLContext.insertInto(TENANT_INVOICE_CONFIGURATION).set(record).returning().fetchOne())
            .isInstanceOf(IntegrityConstraintViolationException.class)
            .hasMessageContaining("duplicate key value violates unique constraint");
    }

    // insert to sequence table with valid reference with invoice config id should SUCCEED
    @Test
    @Order(2)
    public void testInvoiceSequenceInsert() {
        TenantInvoiceNumberSequenceRecord record = new TenantInvoiceNumberSequenceRecord();
        record.setTenantId(TENANT_ID);
        record.setInvoiceConfigId(INVOICE_CONFIG_ID_1);
        record.setNextInvoiceNumber(33L);
        var savedRecord = tenantDSLContext.insertInto(TENANT_INVOICE_NUMBER_SEQUENCE).set(record).returning().fetchOne();

        Assertions.assertThat(savedRecord.getTenantId()).isEqualTo(TENANT_ID);
        Assertions.assertThat(savedRecord.getId()).isNotNull();
        Assertions.assertThat(savedRecord.getInvoiceConfigId()).isEqualTo(INVOICE_CONFIG_ID_1);
        Assertions.assertThat(savedRecord.getNextInvoiceNumber()).isEqualTo(33L);
        Assertions.assertThat(savedRecord.getCreatedOn()).isNotNull();
        Assertions.assertThat(savedRecord.getUpdatedOn()).isNotNull();
    }

    // insert to sequence table with invalid reference of invoice config id should FAIL
    @Test
    public void testInvoiceSequenceWrongInvoiceConfigFails() {
        TenantInvoiceNumberSequenceRecord record = new TenantInvoiceNumberSequenceRecord();
        record.setTenantId(TENANT_ID);
        record.setInvoiceConfigId("wrong invoice config id");
        record.setNextInvoiceNumber(1L);

        Assertions.assertThatThrownBy(() -> tenantDSLContext.insertInto(TENANT_INVOICE_NUMBER_SEQUENCE).set(record).returning().fetchOne())
            .isInstanceOf(IntegrityConstraintViolationException.class)
            .hasMessageContaining("not present in table \"tenant_invoice_configuration\"");
    }

    // insert to sequence table with duplicate invoice config id should FAIL
    @Test
    @Order(3)
    public void testInvoiceSequenceDuplicateInvoiceConfigFails() {
        TenantInvoiceNumberSequenceRecord record = new TenantInvoiceNumberSequenceRecord();
        record.setTenantId(TENANT_ID);
        record.setInvoiceConfigId(INVOICE_CONFIG_ID_1);
        record.setNextInvoiceNumber(1L);

        Assertions.assertThatThrownBy(() -> tenantDSLContext.insertInto(TENANT_INVOICE_NUMBER_SEQUENCE).set(record).returning().fetchOne())
            .isInstanceOf(IntegrityConstraintViolationException.class)
            .hasMessageContaining("duplicate key value violates unique constraint");
    }

    // create pseudo random invoice config, this should not create any sequence record
    // update invoice config to be sequential number, there should be a sequence record created
    @Test
    public void testInvoiceSequenceCreationOnInvoiceConfigUpdate() {
        InvoiceConfigDAO invoiceConfigDAO = new InvoiceConfigDAO(tenantIdProvider, dslContextProvider);
        NumberConfig invoiceConfig = new NumberConfig(null, NumberScheme.PSEUDO_RANDOM, "ABC-", 4, 1L);

        // create pseudo number config -> there should be no sequence record
        var createdInvoiceConfig = invoiceConfigDAO.createInvoiceConfig(invoiceConfig);
        Assertions.assertThat(createdInvoiceConfig).isNotNull();
        Assertions.assertThat(createdInvoiceConfig.configId()).isNotBlank();
        var optionalSequence = invoiceConfigDAO.getInvoiceNumberSequenceByConfigId(createdInvoiceConfig.configId());
        Assertions.assertThat(optionalSequence).isEmpty();

        // update number config to sequence -> there should be sequence record present now
        NumberConfig updateRequest = new NumberConfig(createdInvoiceConfig.configId(), NumberScheme.SEQUENCE, "ABC-", 4, 1L);
        invoiceConfigDAO.updateInvoiceConfig(updateRequest);
        optionalSequence = invoiceConfigDAO.getInvoiceNumberSequenceByConfigId(createdInvoiceConfig.configId());
        Assertions.assertThat(optionalSequence).isPresent();
        Assertions.assertThat(optionalSequence.get().getInvoiceConfigId()).isEqualTo(createdInvoiceConfig.configId());
        Assertions.assertThat(optionalSequence.get().getNextInvoiceNumber()).isEqualTo(1L);
    }
}
