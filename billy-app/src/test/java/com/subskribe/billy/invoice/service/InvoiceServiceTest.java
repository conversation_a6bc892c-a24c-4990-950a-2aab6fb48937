package com.subskribe.billy.invoice.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityContextProviderFixture;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.EntityNotFoundException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.NegativeInvoicePostException;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateService;
import com.subskribe.billy.invoice.db.InvoiceConfigDAO;
import com.subskribe.billy.invoice.db.InvoiceDAO;
import com.subskribe.billy.invoice.exception.InvoiceAlreadyExistsException;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceChargeInclusionOption;
import com.subskribe.billy.invoice.model.InvoiceGenerationCheckResult;
import com.subskribe.billy.invoice.model.InvoiceGenerationMethod;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.MemoizedInvoiceLineItem;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.model.TenantInvoiceConfig;
import com.subskribe.billy.invoice.number.InvoiceNumberGenerator;
import com.subskribe.billy.invoice.number.RandomNumberGenerator;
import com.subskribe.billy.invoice.number.SequenceInvoiceNumberGenerator;
import com.subskribe.billy.invoice.service.processor.OneTimeInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PercentOfInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.PrepaidInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.RecurringInvoiceProcessor;
import com.subskribe.billy.invoice.service.processor.UsageInvoiceProcessor;
import com.subskribe.billy.invoice.tax.model.TaxCalculationInput;
import com.subskribe.billy.invoice.tax.model.TaxTransaction;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderSource;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.order.services.OrderCustomBillingService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.payment.processor.service.PaymentProcessorJobQueueService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PriceTier;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.revrec.services.RevenueRecognitionJobService;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.contactfetcher.ContactGroupOrUserFetcher;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.pecuniary.TenantDiscount;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.model.SubscriptionState;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionImpl;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.test.utilities.ChargeUtils;
import com.subskribe.billy.test.utilities.MockChargeBuilder;
import com.subskribe.billy.usage.service.UsageStatisticsService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Month;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Currency;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.function.Predicate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;

@SuppressWarnings("OptionalUsedAsFieldOrParameterType")
public class InvoiceServiceTest {

    private final OrderGetService mockOrderGetService = mock(OrderGetService.class);

    private final ProductCatalogGetService mockProductCatalogGetService = mock(ProductCatalogGetService.class);

    private final SubscriptionGetService mockSubscriptionGetService = mock(SubscriptionGetService.class);

    private final AccountGetService mockAccountGetService = mock(AccountGetService.class);

    private final InvoiceDAO mockInvoiceDao = mock(InvoiceDAO.class);

    private final InvoiceConfigDAO mockInvoiceConfigDAO = mock(InvoiceConfigDAO.class);

    private final TenantIdProvider mockTenantIdProvider = mock(TenantIdProvider.class);

    private final DSLContextProvider mockDslContextProvider = mock(DSLContextProvider.class);

    private final UsageStatisticsService mockUsageStatisticsService = mock(UsageStatisticsService.class);

    private final InvoiceDocumentGenerationTrackerService mockDocumentGenerationTrackerService = mock(InvoiceDocumentGenerationTrackerService.class);

    private final TaxRateGetService mockTaxRateGetService = mock(TaxRateGetService.class);

    private final TaxService mockTaxService = mock(TaxService.class);

    private final CacheService mockCacheService = mock(CacheService.class);

    private final InvoiceEventService invoiceEventService = mock(InvoiceEventService.class);

    public final EmailContactListService emailContactListService = mock(EmailContactListService.class);

    public final ContactGroupOrUserFetcher contactGroupOrUserFetcher = mock(ContactGroupOrUserFetcher.class);

    public final RateCardService rateCardService = mock(RateCardService.class);

    public final FeatureService featureService = mock(FeatureService.class);

    private final EntityGetService mockEntityGetService = EntityGetServiceFixture.entityGetServiceFixture();

    private final EntityContextProvider mockEntityContextProvider = EntityContextProviderFixture.buildAllEntitiesContext();

    public final ReportingJobQueueService reportingJobQueueService = mock(ReportingJobQueueService.class);

    private final DiscountCalculator discountCalculator = new DiscountCalculator(featureService);

    private final CurrencyConversionRateGetService mockCurrencyConversionRateGetService = mock(CurrencyConversionRateGetService.class);

    private final CustomFieldService mockCustomFieldService = mock(CustomFieldService.class);

    protected UsageInvoiceProcessor usageInvoiceProcessor;

    private static final String SUBSCRIPTION_ID = UUID.randomUUID().toString();

    private static final String EXTERNAL_ID = UUID.randomUUID().toString();

    private static final String TEST_PLAN_ID = "test_plan_id";

    private static final String TEST_PRODUCT_ID = "PROD-BALROG";

    private static final String TEST_PRODUCT_SKU = "SKU-SARUMAN";

    private static final String CHARGE_ID = "CHRG-1234567";

    private static final UUID PLAN_ID = UUID.randomUUID();

    private static final String ORDER_ID = UUID.randomUUID().toString();

    private static final String ACCOUNT_ID = UUID.randomUUID().toString();

    private static final String SHIPPING_CONTACT_ID = UUID.randomUUID().toString();

    private static final String BILLING_CONTACT_ID = UUID.randomUUID().toString();

    private static final TenantId TENANT_ID = new TenantId("test_tenant_id");

    private static final String ENTITY_ID = "test_entity_id";

    private static final int BILLING_CYCLES = 12;

    private static final long QUANTITY = 10;

    private static final BigDecimal UNIT_PRICE = BigDecimal.valueOf(15.35);

    private static final LocalDate JAN_16_2020 = LocalDate.of(2020, Month.JANUARY, 16);

    private static final LocalDate JAN_1_2020 = LocalDate.of(2020, Month.JANUARY, 1);

    private static final Instant START_DATE = getInstant(JAN_1_2020);

    private static final Instant END_DATE = getInstant(JAN_1_2020.plusMonths(BILLING_CYCLES));

    private static final Recurrence TERM_LENGTH = new Recurrence(Cycle.MONTH, BILLING_CYCLES);

    protected static final TimeZone TIME_ZONE = TimeZone.getTimeZone("UTC");

    private final TenantSettingService tenantSettingService = TenantSettingServiceFixture.withTimeZone(TIME_ZONE.getID());

    private static final String ORDER_LINE_ITEM_ID = UUID.randomUUID().toString();

    private static final Currency TEST_CURRENCY = Currency.getInstance(Locale.US);

    private static final Optional<InvoiceGenerationMethod> invoiceGenerationMethod = Optional.empty();

    private AccountingPeriodService mockAccountingPeriodService;

    private final TaxTransaction mockTaxTransaction = TaxTransaction.builder()
        .transactionCode("dummy")
        .computedTaxByLineNo(Map.of())
        .totalTax(BigDecimal.ZERO)
        .build();

    private final InvoiceItem lastInvoiceItem = new InvoiceItem.InvoiceItemBuilder()
        .orderLineItemId(ORDER_LINE_ITEM_ID)
        .chargeId(CHARGE_ID)
        .periodStartDate(getInstant(JAN_1_2020.minusMonths(BILLING_CYCLES)))
        .periodEndDate(START_DATE)
        .createInvoiceItem();

    private final InvoiceConfigurationService invoiceConfigurationService = new InvoiceConfigurationService(
        mockInvoiceDao,
        mockInvoiceConfigDAO,
        mockCacheService
    );

    private final ProrationConfigurationGetService prorationConfigurationGetService = mock(ProrationConfigurationGetService.class);

    private InvoiceServiceInternal invoiceService;

    @BeforeEach
    void beforeEach() {
        when(featureService.isEnabled(any())).thenReturn(true);
        when(mockAccountGetService.getAccount(ACCOUNT_ID)).thenReturn(getTestAccount());
        when(mockAccountGetService.getContact(SHIPPING_CONTACT_ID)).thenReturn(getTestContact(SHIPPING_CONTACT_ID));
        when(mockAccountGetService.getContact(BILLING_CONTACT_ID)).thenReturn(getTestContact(BILLING_CONTACT_ID));

        // Return the same invoice sent to the DAO
        when(mockInvoiceDao.addInvoice(any())).then(i -> i.getArgument(0));
        when(mockTenantIdProvider.provide()).thenReturn(TENANT_ID);
        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID.getRequiredId());
        when(emailContactListService.getEmailAccountContactIdsForSubscription(any())).thenReturn(new ArrayList<>());

        //NOTE: no tax testing is happening here, so we can return a dummy TaxTransaction
        when(mockTaxService.createTaxTransaction(any(TaxCalculationInput.class))).thenReturn(Optional.of(mockTaxTransaction));

        // setup for tax line item
        setupMockProductGet();
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, getRecurrence(Cycle.MONTH))
        );
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(START_DATE, END_DATE));
        when(mockSubscriptionGetService.getSubscriptionChargeBySubscriptionChargeId(anyString())).thenReturn(getTestSubscriptionCharge());

        when(prorationConfigurationGetService.resolveProrationConfig(any(Subscription.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT)
        );
        when(prorationConfigurationGetService.resolveProrationConfig(any(Order.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT)
        );
        setMockInvoiceConfig();

        PercentOfChargeInvoiceHelper mockPercentOfChargeHelper = mock(PercentOfChargeInvoiceHelper.class);
        PercentOfInvoiceProcessor mockPercentOfProcessor = mock(PercentOfInvoiceProcessor.class);

        mockAccountingPeriodService = mock(AccountingPeriodService.class);
        when(mockAccountingPeriodService.getCurrentAccountingPeriod()).thenReturn(Optional.empty());

        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(
            Map.of(CHARGE_ID, new MockChargeBuilder().withChargeId(CHARGE_ID).withPlanId(PLAN_ID).build())
        );

        Plan plan = new Plan();
        plan.setId(PLAN_ID);
        when(mockProductCatalogGetService.getPlansByIds(any())).thenReturn(List.of(plan));

        var subscriptionBillingPeriodService = new SubscriptionBillingPeriodService(
            mockSubscriptionGetService,
            tenantSettingService,
            featureService,
            prorationConfigurationGetService
        );

        PaymentProcessorJobQueueService mockPaymentProcessorJobQueueService = mock(PaymentProcessorJobQueueService.class);
        RevenueRecognitionJobService mockRevenueRecognitionJobService = mock(RevenueRecognitionJobService.class);

        InvoiceAmountCalculator invoiceAmountCalculator = new InvoiceAmountCalculator(
            rateCardService,
            mockCurrencyConversionRateGetService,
            discountCalculator,
            featureService
        );

        RatingService ratingService = new RatingService(invoiceAmountCalculator, rateCardService);

        RecurringInvoiceProcessor recurringInvoiceProcessor = new RecurringInvoiceProcessor(
            invoiceAmountCalculator,
            subscriptionBillingPeriodService,
            featureService,
            mockInvoiceDao
        );

        OneTimeInvoiceProcessor oneTimeInvoiceProcessor = new OneTimeInvoiceProcessor(
            mockSubscriptionGetService,
            mockInvoiceDao,
            invoiceAmountCalculator,
            featureService
        );

        PrepaidInvoiceProcessor prepaidInvoiceProcessor = new PrepaidInvoiceProcessor(
            mockInvoiceDao,
            invoiceAmountCalculator,
            subscriptionBillingPeriodService
        );

        usageInvoiceProcessor = new UsageInvoiceProcessor(
            subscriptionBillingPeriodService,
            invoiceAmountCalculator,
            featureService,
            recurringInvoiceProcessor,
            prepaidInvoiceProcessor,
            mockInvoiceDao,
            mockUsageStatisticsService,
            ratingService,
            mockCurrencyConversionRateGetService
        );

        invoiceService = new InvoiceServiceInternal(
            mockOrderGetService,
            mockSubscriptionGetService,
            mockAccountGetService,
            mockProductCatalogGetService,
            mockTenantIdProvider,
            mockInvoiceDao,
            new InvoiceNumberGenerator(
                mockInvoiceDao,
                invoiceConfigurationService,
                mockEntityGetService,
                new RandomNumberGenerator(),
                new SequenceInvoiceNumberGenerator(mockInvoiceDao)
            ),
            mockDslContextProvider,
            mockTaxService,
            mockTaxRateGetService,
            mockDocumentGenerationTrackerService,
            tenantSettingService,
            mockPercentOfChargeHelper,
            mockPercentOfProcessor,
            mockPaymentProcessorJobQueueService,
            invoiceEventService,
            subscriptionBillingPeriodService,
            prorationConfigurationGetService,
            mockRevenueRecognitionJobService,
            mockAccountingPeriodService,
            emailContactListService,
            contactGroupOrUserFetcher,
            featureService,
            recurringInvoiceProcessor,
            oneTimeInvoiceProcessor,
            prepaidInvoiceProcessor,
            usageInvoiceProcessor,
            reportingJobQueueService,
            Clock.systemUTC(),
            mock(TransactionalExchangeRateService.class),
            mock(OrderCustomBillingService.class),
            mockEntityGetService,
            mockCustomFieldService
        );
    }

    @Test
    public void getInvoicePreview() {
        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH)).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge()))
        );

        var orderInvoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);

        assertEquals(expectedTermSubTotal(), roundToTwoPlaces(orderInvoicePreview.getTotal()));
    }

    @Test
    public void getInvoicePreviewAmountWithRounding() {
        Map<String, BigDecimal> tenantDiscounts = new HashMap<>();
        BigDecimal discountPercent = new BigDecimal("0.33333");
        tenantDiscounts.put("1", discountPercent);

        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.YEAR), tenantDiscounts).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge()))
        );

        var orderInvoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);

        // 1842 list price * 0.66667 = 1228.00614 -> 1228.01 rounded to nearest cents
        BigDecimal expectedAmount = roundToTwoPlaces(expectedTermSubTotal().multiply(BigDecimal.ONE.subtract(discountPercent)));

        assertEquals(expectedAmount, orderInvoicePreview.getTotal());
    }

    @Test
    public void getInvoicePreviewWithTenantDiscount() {
        Map<String, BigDecimal> tenantDiscounts = new HashMap<>();
        tenantDiscounts.put("1", new BigDecimal("0.1"));

        when(mockOrderGetService.getOrderByOrderId(ORDER_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, OrderStatus.DRAFT, getRecurrence(Cycle.MONTH), tenantDiscounts).get(0)
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge()))
        );

        var orderInvoicePreview = invoiceService.previewInvoiceByOrderPeriod(ORDER_ID);

        // should be 0.9 * normal amount with 10% discount
        BigDecimal expectedAmount = roundToTwoPlaces(expectedTermSubTotal().multiply(BigDecimal.valueOf(0.9)));

        assertEquals(expectedAmount, orderInvoicePreview.getTotal());
    }

    @Test
    public void expiredSubscriptionShouldBeEligibleForInvoiceGeneration() {
        var start = getInstant(JAN_1_2020);
        var end = getInstant(JAN_1_2020.plusMonths(12));
        var billingCycle = getRecurrence(Cycle.YEAR);
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(start, end, billingCycle));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(getTestOrders(start, end, billingCycle));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR))))
        );

        InvoiceGenerationCheckResult result = invoiceService.performInvoiceGenerationCheck(
            SUBSCRIPTION_ID,
            getInstant(JAN_1_2020.plusMonths(13)),
            InvoiceChargeInclusionOption.getChargeInclusionOption(true, true)
        );

        assertTrue(result.isInvoiceToBeGenerated());
        assertTrue(result.getExistingDraftInvoice().isEmpty());
    }

    @Test
    public void pendingSubscriptionShouldNotBeEligibleForInvoiceGeneration() {
        var start = getInstant(JAN_1_2020);
        var end = getInstant(JAN_1_2020.plusMonths(12));
        var billingCycle = getRecurrence(Cycle.YEAR);
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(start, end, billingCycle));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(getTestOrders(start, end, billingCycle));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR))))
        );

        InvoiceGenerationCheckResult result = invoiceService.performInvoiceGenerationCheck(
            SUBSCRIPTION_ID,
            getInstant(JAN_1_2020.minusMonths(1)),
            InvoiceChargeInclusionOption.getChargeInclusionOption(true, true)
        );

        assertFalse(result.isInvoiceToBeGenerated());
        assertTrue(result.getExistingDraftInvoice().isEmpty());
    }

    @Test
    public void subscriptionWithOpenDraftInvoiceShouldNotBeEligibleForInvoiceGeneration() {
        var start = getInstant(JAN_1_2020);
        var end = getInstant(JAN_1_2020.plusMonths(12));
        var billingCycle = getRecurrence(Cycle.YEAR);
        var existingDraftInvoiceNumber = new Invoice.Number("123");
        var existingDraftInvoice = new Invoice.InvoiceBuilder().invoiceNumber(existingDraftInvoiceNumber).currency("USD").createInvoice();
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(start, end, billingCycle));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(getTestOrders(start, end, billingCycle));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR))))
        );
        doThrow(new InvoiceAlreadyExistsException("draft invoice exists")).when(mockInvoiceDao).checkForDraftInvoice(SUBSCRIPTION_ID);
        when(mockInvoiceDao.getDraftInvoiceForSubscription(SUBSCRIPTION_ID)).thenReturn(Optional.of(existingDraftInvoice));

        InvoiceGenerationCheckResult result = invoiceService.performInvoiceGenerationCheck(
            SUBSCRIPTION_ID,
            end,
            InvoiceChargeInclusionOption.getChargeInclusionOption(true, true)
        );

        assertFalse(result.isInvoiceToBeGenerated());
    }

    @Test
    public void subscriptionWithAllInvoicesGeneratedShouldNotBeEligibleForInvoiceGeneration() {
        var start = getInstant(JAN_1_2020);
        var end = getInstant(JAN_1_2020.plusMonths(12));
        var billingCycle = getRecurrence(Cycle.YEAR);
        var testOrder = getTestOrders(start, end, billingCycle);
        var orderLineItemId = testOrder.get(0).getLineItems().get(0).getOrderLineId();
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(start, end, billingCycle));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(testOrder);
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR))))
        );
        when(mockInvoiceDao.getAllOrderLineIdsWithInvoiceItemsPastThreshold(any(), any())).thenReturn(Set.of(orderLineItemId));

        InvoiceGenerationCheckResult result = invoiceService.performInvoiceGenerationCheck(
            SUBSCRIPTION_ID,
            end,
            InvoiceChargeInclusionOption.getChargeInclusionOption(true, true)
        );

        assertFalse(result.isInvoiceToBeGenerated());
        assertTrue(result.getExistingDraftInvoice().isEmpty());
    }

    @Test
    public void generateInvoicePaidInFullBillingCycleMonthlyCharge() {
        var start = getInstant(JAN_1_2020);
        var end = getInstant(JAN_1_2020.plusMonths(18));
        var billingCycle = getRecurrence(Cycle.PAID_IN_FULL);
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(start, end, billingCycle));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(getTestOrders(start, end, billingCycle));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.MONTH))))
        );

        // monthly charge for 18 months should be 18 * monthly price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(18)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                start,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateInvoicePaidInFullBillingCycleYearlyCharge() {
        var start = getInstant(JAN_1_2020);
        var end = getInstant(JAN_1_2020.plusYears(3));
        var billingCycle = getRecurrence(Cycle.PAID_IN_FULL);
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(start, end, billingCycle));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(getTestOrders(start, end, billingCycle));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR))))
        );

        // yearly charge for 3 years should be 3 * yearly price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(3)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                start,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateInvoicePaidInFullBillingCycleYearlyChargeWithProration() {
        var start = getInstant(JAN_1_2020);
        var end = getInstant(JAN_1_2020.plusMonths(6));
        var billingCycle = getRecurrence(Cycle.PAID_IN_FULL);
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(start, end, billingCycle));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(getTestOrders(start, end, billingCycle));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR))))
        );
        setMockInvoiceConfig();
        when(prorationConfigurationGetService.resolveProrationConfig(any(Subscription.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.CALENDAR_DAYS, ProrationConfig.Mode.NORMALIZED)
        );

        // yearly charge for 6 months should be 1/2 yearly price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(0.5)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                start,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateInvoiceYearlyBillingCycleSemiAnnualCharge() {
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(
            getTestSubscription(START_DATE, END_DATE, getRecurrence(Cycle.YEAR))
        );
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, getRecurrence(Cycle.YEAR))
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.SEMI_ANNUAL))))
        );

        // semi-annual charge for one year should be 2 * semi-annual price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(2)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateInvoiceSemiAnnualBillingCycleMonthlyCharge() {
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(
            getTestSubscription(START_DATE, END_DATE, getRecurrence(Cycle.SEMI_ANNUAL))
        );
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, getRecurrence(Cycle.SEMI_ANNUAL))
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.MONTH))))
        );

        // monthly charge for semi annual should be 3 * per month price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(6)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateInvoiceSemiAnnualBillingCycleYearlyCharge() {
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(
            getTestSubscription(START_DATE, END_DATE, getRecurrence(Cycle.SEMI_ANNUAL))
        );
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, getRecurrence(Cycle.SEMI_ANNUAL))
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR))))
        );

        // yearly charge for semi annual period should be 1/2 the yearly price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(0.50)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateInvoiceYearlyBillingCycleQuarterlyCharge() {
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(
            getTestSubscription(START_DATE, END_DATE, getRecurrence(Cycle.YEAR))
        );
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, getRecurrence(Cycle.YEAR))
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.QUARTER))))
        );

        // quarterly charge for one year should be 4 * per quarter price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(4)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateInvoiceQuarterlyBillingCycleMonthlyCharge() {
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(
            getTestSubscription(START_DATE, END_DATE, getRecurrence(Cycle.QUARTER))
        );
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, getRecurrence(Cycle.QUARTER))
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.MONTH))))
        );

        // monthly charge for one quarter should be 3 * per month price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(3)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateInvoiceQuarterlyBillingCycleYearlyCharge() {
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(
            getTestSubscription(START_DATE, END_DATE, getRecurrence(Cycle.QUARTER))
        );
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, getRecurrence(Cycle.QUARTER))
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge(getRecurrence(Cycle.YEAR))))
        );

        // yearly charge for one quarter should be 1/4 the yearly price
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(0.25)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateInvoiceYearlyBillingCycleMonthlyCharge() {
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(
            getTestSubscription(START_DATE, END_DATE, getRecurrence(Cycle.YEAR))
        );
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, END_DATE, getRecurrence(Cycle.YEAR))
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge()))
        );

        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(12)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateProratedInvoiceFixedDays() {
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(START_DATE, getInstant(JAN_16_2020)));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, getInstant(JAN_16_2020), getRecurrence(Cycle.MONTH))
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge()))
        );

        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(0.5)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void generateProratedInvoiceCalendarDays() {
        when(mockSubscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(getTestSubscription(START_DATE, getInstant(JAN_16_2020)));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(SUBSCRIPTION_ID)).thenReturn(
            getTestOrders(START_DATE, getInstant(JAN_16_2020), getRecurrence(Cycle.MONTH))
        );
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge()))
        );

        when(prorationConfigurationGetService.resolveProrationConfig(any(Subscription.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.CALENDAR_DAYS, ProrationConfig.Mode.EXACT)
        );

        double prorationRatio = 0.48387; // consumed 15 days out of 31 calendar days
        var expectedAmount = roundToTwoPlaces(expectedOnePeriodPerUnitSubTotal(BigDecimal.valueOf(prorationRatio)));
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedAmount, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
    }

    @Test
    public void calculateUsagePerUnitInvoiceAmount() {
        BigDecimal usageQuantity = BigDecimal.valueOf(10);
        when(mockUsageStatisticsService.getUsageSumByAttributeForChargeGroupId(any(), any(), any())).thenReturn(mapOfQuantity(usageQuantity));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(
                List.of(new MockChargeBuilder().withChargeId(CHARGE_ID).withAmount(UNIT_PRICE).usage().monthly().build())
            )
        );

        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                getInstant(JAN_1_2020.plusMonths(1)),
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        var expectedAmount = UNIT_PRICE.multiply(usageQuantity);

        validateOnePeriodInvoice(invoice, expectedAmount);
    }

    @Test
    public void calculateUsageVolumeInvoiceAmount() {
        BigDecimal usageQuantity = BigDecimal.valueOf(6);
        when(mockUsageStatisticsService.getUsageSumByAttributeForChargeGroupId(any(), any(), any())).thenReturn(mapOfQuantity(usageQuantity));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getChargeWithTiers(ChargeType.USAGE, ChargeModel.VOLUME)))
        );

        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                getInstant(JAN_1_2020.plusMonths(1)),
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        var expectedAmount = new BigDecimal("8.00").multiply(usageQuantity);

        validateOnePeriodInvoice(invoice, expectedAmount);
    }

    @Test
    public void calculateUsageTieredInvoiceAmount() {
        when(mockUsageStatisticsService.getUsageSumByAttributeForChargeGroupId(any(), any(), any())).thenReturn(mapOfQuantity(new BigDecimal(6L)));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getChargeWithTiers(ChargeType.USAGE, ChargeModel.TIERED)))
        );

        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                getInstant(JAN_1_2020.plusMonths(1)),
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        // amount = 5 * 10 + 1 * 8
        var expectedAmount = new BigDecimal("58.00");

        validateOnePeriodInvoice(invoice, expectedAmount);
    }

    @Test
    public void getGenerateInvoiceForFlatFee() {
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringFlatCharge()))
        );

        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();
        validateOnePeriodInvoice(invoice, UNIT_PRICE);
    }

    @Test
    public void getGenerateInvoiceForPerUnit() {
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge()))
        );
        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();
        validateOnePeriodInvoice(invoice, expectedOnePeriodPerUnitSubTotal(BigDecimal.ONE));
    }

    @Test
    public void getGenerateInvoiceForVolume() {
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringVolumeCharge()))
        );

        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        var expectedSubTotal = new BigDecimal("80.00"); // quantity = 10 x $8 (second tier)
        validateOnePeriodInvoice(invoice, expectedSubTotal);
    }

    @Test
    public void getGenerateInvoiceForTiered() {
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringTieredCharge()))
        );

        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        // quantity = 5 x $10 (first tier) + 5 x $8 (second tier)
        var expectedSubTotal = new BigDecimal("90.00");
        validateOnePeriodInvoice(invoice, expectedSubTotal);
    }

    @Test
    public void generateAllInvoices() {
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringPerUnitCharge()))
        );

        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                END_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        assertEquals(expectedTermSubTotal(), roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(SUBSCRIPTION_ID, invoice.getSubscriptionId());
    }

    @Test
    public void getGenerateInvoiceMoreThanOnePreviousInvoice() {
        InvoiceItem secondLastInvoiceItem = new InvoiceItem.InvoiceItemBuilder()
            .orderLineItemId(ORDER_LINE_ITEM_ID)
            .chargeId(CHARGE_ID)
            .periodStartDate(getInstant(JAN_1_2020.minusMonths(BILLING_CYCLES)))
            .periodEndDate(getInstant(JAN_1_2020.minusMonths(1)))
            .createInvoiceItem();
        when(mockInvoiceDao.getAllInvoiceItemsForSubscription(SUBSCRIPTION_ID)).thenReturn(List.of(lastInvoiceItem, secondLastInvoiceItem));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringFlatCharge()))
        );

        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        validateOnePeriodInvoice(invoice, UNIT_PRICE);
    }

    @Test
    public void getGenerateInvoiceForFirstTime() {
        when(mockInvoiceDao.getAllInvoiceItemsForSubscription(SUBSCRIPTION_ID)).thenReturn(List.of());
        when(mockProductCatalogGetService.getChargeMapByChargeIds(List.of(CHARGE_ID))).thenReturn(
            ChargeUtils.getChargeMapFromCharges(List.of(getRecurringFlatCharge()))
        );

        Invoice invoice = invoiceService
            .generateInvoice(
                SUBSCRIPTION_ID,
                START_DATE,
                Optional.empty(),
                InvoiceChargeInclusionOption.INCLUDE_USAGE,
                invoiceGenerationMethod,
                Optional.empty()
            )
            .orElseThrow();

        validateOnePeriodInvoice(invoice, UNIT_PRICE);
    }

    @Test
    public void testIncludeChargeType() {
        assertEquals(InvoiceChargeInclusionOption.INCLUDE_USAGE, InvoiceChargeInclusionOption.getChargeInclusionOption(true, true));
        assertEquals(InvoiceChargeInclusionOption.EXCLUDE_USAGE, InvoiceChargeInclusionOption.getChargeInclusionOption(false, true));
        assertEquals(InvoiceChargeInclusionOption.ONLY_USAGE, InvoiceChargeInclusionOption.getChargeInclusionOption(true, false));
        assertThrows(IllegalArgumentException.class, () -> InvoiceChargeInclusionOption.getChargeInclusionOption(false, false));
    }

    @Test
    public void postInvoiceWithNegativeAmount() {
        Invoice invoice = new Invoice.InvoiceBuilder().status(InvoiceStatus.DRAFT).total(BigDecimal.TEN.negate()).currency("USD").createInvoice();
        when(mockInvoiceDao.getInvoiceByInvoiceNumber(any())).thenReturn(invoice);

        assertThrows(NegativeInvoicePostException.class, () -> invoiceService.postInvoice(new Invoice.Number("123")));
    }

    @Test
    public void postInvoiceBeforeCurrentAccountingPeriod() {
        Instant accountingPeriodStart = getInstant(JAN_1_2020);
        Invoice invoice = new Invoice.InvoiceBuilder()
            .entityId(EntityFixture.ENTITY_1_ID)
            .subscriptionId(SUBSCRIPTION_ID)
            .customerAccountId(ACCOUNT_ID)
            .status(InvoiceStatus.DRAFT)
            .total(BigDecimal.TEN)
            .currency("USD")
            .invoiceDate(accountingPeriodStart.minusSeconds(1))
            .shippingContact(getTestContact(SHIPPING_CONTACT_ID))
            .billingContact(getTestContact(BILLING_CONTACT_ID))
            .createInvoice();
        when(mockInvoiceDao.getInvoiceByInvoiceNumber(any())).thenReturn(invoice);
        when(mockAccountingPeriodService.inClosedAccountingPeriod(any(), any())).thenReturn(true);
        when(mockEntityContextProvider.provideEntityContext())
            .thenReturn(EntityContextProviderFixture.ALL_ENTITIES_CONTEXT)
            .thenThrow(EntityNotFoundException.class);

        assertThrows(IllegalStateException.class, () -> invoiceService.postInvoice(new Invoice.Number("123")));
    }

    @Test
    public void postInvoiceWithInvalidDueDate() {
        Instant now = Instant.now();
        Invoice invoice = new Invoice.InvoiceBuilder()
            .entityId(EntityFixture.ENTITY_1_ID)
            .subscriptionId(SUBSCRIPTION_ID)
            .status(InvoiceStatus.DRAFT)
            .total(BigDecimal.TEN)
            .currency("USD")
            .invoiceDate(now)
            .dueDate(now.minusSeconds(1))
            .shippingContact(getTestContact(SHIPPING_CONTACT_ID))
            .billingContact(getTestContact(BILLING_CONTACT_ID))
            .createInvoice();
        when(mockInvoiceDao.getInvoiceByInvoiceNumber(any())).thenReturn(invoice);
        when(mockEntityContextProvider.provideEntityContext())
            .thenReturn(EntityContextProviderFixture.ALL_ENTITIES_CONTEXT)
            .thenThrow(EntityNotFoundException.class);

        assertThrows(InvalidInputException.class, () -> invoiceService.postInvoice(new Invoice.Number("123")));
    }

    @Test
    void sortInvoiceItemsBySubscriptionChargeRank() {
        InvoiceItem invoiceItem1 = new InvoiceItem.InvoiceItemBuilder()
            .chargeId("chargeId1")
            .subscriptionChargeId("subscriptionChargeId1")
            .createInvoiceItem();
        InvoiceItem invoiceItem2 = new InvoiceItem.InvoiceItemBuilder()
            .chargeId("chargeId2")
            .subscriptionChargeId("subscriptionChargeId2")
            .createInvoiceItem();
        InvoiceItem invoiceItem3 = new InvoiceItem.InvoiceItemBuilder()
            .chargeId("chargeId3")
            .subscriptionChargeId("subscriptionChargeId3")
            .createInvoiceItem();

        when(mockSubscriptionGetService.getSubscriptionChargeBySubscriptionChargeId("subscriptionChargeId1")).thenReturn(
            getTestSubscriptionCharge(3)
        );
        when(mockSubscriptionGetService.getSubscriptionChargeBySubscriptionChargeId("subscriptionChargeId2")).thenReturn(
            getTestSubscriptionCharge(2)
        );
        when(mockSubscriptionGetService.getSubscriptionChargeBySubscriptionChargeId("subscriptionChargeId3")).thenReturn(
            getTestSubscriptionCharge(1)
        );

        List<InvoiceItem> invoiceItems = List.of(invoiceItem2, invoiceItem1, invoiceItem3);
        List<InvoiceItem> sortedInvoiceItems = invoiceService.sortInvoiceItemsBySubscriptionChargeRank(invoiceItems);

        // The order should be based on the rank of the subscription charge
        assertEquals(invoiceItem1, sortedInvoiceItems.get(2));
        assertEquals(invoiceItem2, sortedInvoiceItems.get(1));
        assertEquals(invoiceItem3, sortedInvoiceItems.get(0));
    }

    @Test
    void sortInvoiceItemsBySubscriptionChargeRankWithNullSubscriptionChargeId() {
        InvoiceItem invoiceItem1 = new InvoiceItem.InvoiceItemBuilder().chargeId("chargeId1").subscriptionChargeId(null).createInvoiceItem();
        InvoiceItem invoiceItem2 = new InvoiceItem.InvoiceItemBuilder().chargeId("chargeId2").subscriptionChargeId(null).createInvoiceItem();
        InvoiceItem invoiceItem3 = new InvoiceItem.InvoiceItemBuilder().chargeId("chargeId3").subscriptionChargeId(null).createInvoiceItem();

        List<InvoiceItem> invoiceItems = List.of(invoiceItem2, invoiceItem1, invoiceItem3);
        List<InvoiceItem> sortedInvoiceItems = invoiceService.sortInvoiceItemsBySubscriptionChargeRank(invoiceItems);

        // If subscriptionChargeId is null, the order should be preserved
        assertEquals(invoiceItem1, sortedInvoiceItems.get(1));
        assertEquals(invoiceItem2, sortedInvoiceItems.get(0));
        assertEquals(invoiceItem3, sortedInvoiceItems.get(2));
    }

    @Test
    void testMemoizedInvoiceLineItemPredicate() {
        MemoizedInvoiceLineItem nonBackfilledItem = mock(MemoizedInvoiceLineItem.class);
        when(nonBackfilledItem.getIsBackfilled()).thenReturn(false);

        MemoizedInvoiceLineItem backfilledItem = mock(MemoizedInvoiceLineItem.class);
        when(backfilledItem.getIsBackfilled()).thenReturn(true);

        when(featureService.isEnabled(Feature.USE_BACKFILLED_MEMOIZATION)).thenReturn(false);
        Predicate<MemoizedInvoiceLineItem> predicate = invoiceService.filterBackfilledMemoizedLinesPredicate(false);

        assertTrue(predicate.test(nonBackfilledItem));
        Predicate<MemoizedInvoiceLineItem> finalPredicate = predicate;
        assertThrows(IllegalStateException.class, () -> finalPredicate.test(backfilledItem));

        // enable usage of backfilled memoized lines
        when(featureService.isEnabled(Feature.USE_BACKFILLED_MEMOIZATION)).thenReturn(true);
        assertTrue(predicate.test(backfilledItem));

        // This is to test the behaviour during backfill, for backfill flow it should return backfilled lines
        when(featureService.isEnabled(Feature.USE_BACKFILLED_MEMOIZATION)).thenReturn(false);
        predicate = invoiceService.filterBackfilledMemoizedLinesPredicate(true);

        assertTrue(predicate.test(nonBackfilledItem));
        assertTrue(predicate.test(backfilledItem));
    }

    private void setMockInvoiceConfig() {
        TenantInvoiceConfig tenantInvoiceConfig = new TenantInvoiceConfig("INV", "SEQUENCE", 1L, 4);
        when(mockCacheService.get(any(), eq(CacheType.INVOICE_CONFIG_BY_ID), any(), any(), eq(TenantInvoiceConfig.class))).thenReturn(
            tenantInvoiceConfig
        );
    }

    private void validateOnePeriodInvoice(Invoice invoice, BigDecimal expectedSubTotal) {
        assertEquals(SUBSCRIPTION_ID, invoice.getSubscriptionId());
        var invoiceItem = invoice.getInvoiceItems().get(0);
        assertEquals(CHARGE_ID, invoiceItem.getChargeId());
        assertEquals(expectedSubTotal, roundToTwoPlaces(invoice.getSubTotal()));
        assertEquals(1, invoice.getInvoiceItems().size());
        assertEquals(InvoiceStatus.DRAFT, invoice.getStatus());
    }

    private static BigDecimal expectedOnePeriodPerUnitSubTotal(BigDecimal prorateAmount) {
        return BigDecimal.valueOf(QUANTITY).multiply(UNIT_PRICE).multiply(prorateAmount);
    }

    private static BigDecimal expectedTermSubTotal() {
        return roundToTwoPlaces(BigDecimal.valueOf(QUANTITY).multiply(UNIT_PRICE).multiply(BigDecimal.valueOf(BILLING_CYCLES)));
    }

    private static Account getTestAccount() {
        var account = new Account();
        account.setAccountId(ACCOUNT_ID);
        return account;
    }

    private static AccountContact getTestContact(String contactId) {
        var contact = new AccountContact();
        contact.setContactId(contactId);
        contact.setAddress(new AccountAddress());
        contact.setAccountId(ACCOUNT_ID);
        return contact;
    }

    private static Subscription getTestSubscription(Instant startDate, Instant endDate, Recurrence billingCycle) {
        var subscriptionEntity = new SubscriptionEntity(
            UUID.randomUUID(),
            SUBSCRIPTION_ID,
            EXTERNAL_ID,
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            ACCOUNT_ID,
            null,
            SHIPPING_CONTACT_ID,
            BILLING_CONTACT_ID,
            Currency.getInstance("USD"),
            PaymentTerm.NET30,
            SubscriptionState.ACTIVE,
            startDate,
            endDate,
            startDate,
            null,
            TERM_LENGTH,
            billingCycle,
            BillingTerm.UP_FRONT,
            null,
            null,
            List.of(ORDER_ID),
            null,
            false,
            false,
            startDate,
            Instant.now(),
            1,
            START_DATE,
            END_DATE,
            SubscriptionDurationModel.TERMED
        );
        return new SubscriptionImpl(subscriptionEntity, List.of(getTestSubscriptionCharge()));
    }

    private static Subscription getTestSubscription(Instant startDate, Instant endDate) {
        return getTestSubscription(startDate, endDate, getRecurrence(Cycle.MONTH));
    }

    private static SubscriptionCharge getTestSubscriptionCharge() {
        return new SubscriptionCharge(
            UUID.randomUUID(),
            CHARGE_ID,
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            SUBSCRIPTION_ID,
            TENANT_ID.getRequiredId(),
            ACCOUNT_ID,
            UUID.randomUUID(),
            List.of(ORDER_LINE_ITEM_ID),
            START_DATE,
            END_DATE,
            1,
            false,
            null,
            null,
            null,
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ZERO,
            null,
            null,
            null,
            1,
            START_DATE,
            END_DATE,
            0,
            null
        );
    }

    private static SubscriptionCharge getTestSubscriptionCharge(int rank) {
        return new SubscriptionCharge(
            UUID.randomUUID(),
            CHARGE_ID,
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            SUBSCRIPTION_ID,
            TENANT_ID.getRequiredId(),
            ACCOUNT_ID,
            UUID.randomUUID(),
            List.of(ORDER_LINE_ITEM_ID),
            START_DATE,
            END_DATE,
            1,
            false,
            null,
            null,
            null,
            BigDecimal.ONE,
            BigDecimal.ONE,
            BigDecimal.ZERO,
            null,
            null,
            null,
            1,
            START_DATE,
            END_DATE,
            rank,
            null
        );
    }

    private static Charge getRecurringPerUnitCharge() {
        return getRecurringPerUnitCharge(getRecurrence(Cycle.MONTH));
    }

    private static Charge getRecurringPerUnitCharge(Recurrence recurrence) {
        return new MockChargeBuilder().withChargeId(CHARGE_ID).withAmount(UNIT_PRICE).recurring(recurrence).build();
    }

    private static Charge getRecurringFlatCharge() {
        return new MockChargeBuilder().withChargeId(CHARGE_ID).withAmount(UNIT_PRICE).recurring().monthly().flatFee().build();
    }

    private static Charge getChargeWithTiers(ChargeType chargeType, ChargeModel chargeModel) {
        return new MockChargeBuilder()
            .withChargeId(CHARGE_ID)
            .withChargeType(chargeType)
            .monthly()
            .withChargeModel(chargeModel)
            .withPriceTiers(getPriceTiers())
            .build();
    }

    private static Charge getRecurringTieredCharge() {
        return getChargeWithTiers(ChargeType.RECURRING, ChargeModel.TIERED);
    }

    private static Charge getRecurringVolumeCharge() {
        return getChargeWithTiers(ChargeType.RECURRING, ChargeModel.VOLUME);
    }

    private static List<PriceTier> getPriceTiers() {
        return List.of(PriceTier.of(5L, BigDecimal.valueOf(10)), PriceTier.of(10L, BigDecimal.valueOf(8)), PriceTier.of(null, BigDecimal.valueOf(7)));
    }

    private List<Order> getTestOrders(Instant startDate, Instant endDate, Recurrence recurrence) {
        return getTestOrders(startDate, endDate, OrderStatus.SUBMITTED, recurrence, Collections.emptyMap());
    }

    private List<Order> getTestOrders(Instant startDate, Instant endDate, OrderStatus status, Recurrence recurrence) {
        return getTestOrders(startDate, endDate, status, recurrence, Collections.emptyMap());
    }

    private List<Order> getTestOrders(
        Instant startDate,
        Instant endDate,
        OrderStatus status,
        Recurrence recurrence,
        Map<String, BigDecimal> tenantDiscounts
    ) {
        var orderId = UUID.randomUUID().toString();
        OrderLineItem orderLineItem = getOrderLineItem(orderId, startDate, endDate, tenantDiscounts);
        Order order = getOrderDetails(orderId, List.of(orderLineItem), startDate, endDate, status, recurrence, tenantDiscounts);
        return List.of(order);
    }

    private static Order getOrderDetails(
        String orderId,
        List<OrderLineItem> orderLineItems,
        Instant startDate,
        Instant endDate,
        OrderStatus status,
        Recurrence recurrence,
        Map<String, BigDecimal> tenantDiscountsMap
    ) {
        List<TenantDiscount> tenantDiscounts = getTenantDiscounts(tenantDiscountsMap);

        return new Order(
            AutoGenerate.getNewUuid(),
            null,
            orderId,
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            null,
            ACCOUNT_ID,
            null,
            OrderType.NEW,
            TEST_CURRENCY,
            PaymentTerm.NET30,
            null,
            SUBSCRIPTION_ID,
            SHIPPING_CONTACT_ID,
            BILLING_CONTACT_ID,
            tenantDiscounts,
            orderLineItems,
            orderLineItems,
            startDate,
            endDate,
            START_DATE,
            TERM_LENGTH,
            recurrence,
            BillingTerm.UP_FRONT,
            BigDecimal.valueOf(150),
            BigDecimal.valueOf(150),
            null,
            BigDecimal.valueOf(150),
            status,
            null,
            Instant.now(),
            Instant.now(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            0,
            null,
            0,
            null,
            false,
            false,
            null,
            null,
            null,
            true,
            null,
            null,
            OrderSource.USER,
            OrderStartDateType.FIXED,
            null,
            Optional.empty(),
            SubscriptionDurationModel.TERMED,
            null
        );
    }

    private OrderLineItem getOrderLineItem(String orderId, Instant startDate, Instant endDate, Map<String, BigDecimal> tenantDiscountsMap) {
        List<TenantDiscountLineItem> tenantDiscounts = getTenantDiscountLineItems(tenantDiscountsMap);
        var totalDiscount = DiscountCalculator.calculateDiscountAmounts(UNIT_PRICE, List.of(), tenantDiscounts, QUANTITY, true);

        return new OrderLineItem(
            AutoGenerate.getNewUuid(),
            ORDER_LINE_ITEM_ID,
            null,
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            orderId,
            1,
            false,
            ActionType.ADD,
            TEST_PLAN_ID,
            UUID.randomUUID().toString(),
            null,
            null,
            null,
            CHARGE_ID,
            null,
            null,
            QUANTITY,
            UNIT_PRICE.multiply(BigDecimal.valueOf(2)),
            UNIT_PRICE,
            UNIT_PRICE.multiply(BigDecimal.valueOf(QUANTITY)),
            UNIT_PRICE.multiply(BigDecimal.valueOf(2 * QUANTITY)),
            UNIT_PRICE.multiply(BigDecimal.valueOf(2 * QUANTITY)),
            totalDiscount.getTotalDiscountAmount(),
            null,
            tenantDiscounts,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            startDate,
            endDate,
            List.of(),
            null,
            null,
            Instant.now(),
            Instant.now()
        );
    }

    private static List<TenantDiscount> getTenantDiscounts(Map<String, BigDecimal> discounts) {
        List<TenantDiscount> tenantDiscounts = new ArrayList<>();

        for (var entry : discounts.entrySet()) {
            var discount = new TenantDiscount();
            discount.setId(entry.getKey());
            discount.setPercent(entry.getValue());
            tenantDiscounts.add(discount);
        }
        return tenantDiscounts;
    }

    private static List<TenantDiscountLineItem> getTenantDiscountLineItems(Map<String, BigDecimal> discounts) {
        List<TenantDiscountLineItem> tenantDiscounts = new ArrayList<>();

        for (var entry : discounts.entrySet()) {
            var discount = new TenantDiscountLineItem();
            discount.setId(entry.getKey());
            discount.setPercent(entry.getValue());
            tenantDiscounts.add(discount);
        }
        return tenantDiscounts;
    }

    private static Instant getInstant(LocalDate date) {
        return date.atStartOfDay(InvoiceServiceInternal.DEFAULT_TIME_ZONE).toInstant();
    }

    private static BigDecimal roundToTwoPlaces(BigDecimal value) {
        return value.setScale(2, RoundingMode.HALF_EVEN);
    }

    private static Map<AttributeReferences, BigDecimal> mapOfQuantity(BigDecimal quantity) {
        HashMap<AttributeReferences, BigDecimal> value = new HashMap<>();
        value.put(AttributeReferences.NULL_ATTRIBUTE_REFERENCES_KEY, quantity);
        return Collections.unmodifiableMap(value);
    }

    private void setupMockProductGet() {
        Plan plan = new Plan();
        plan.setProductId(TEST_PRODUCT_ID);
        plan.setCurrency(TEST_CURRENCY);
        when(mockProductCatalogGetService.getPlansFromChargeIds(ArgumentMatchers.anyList())).thenReturn(List.of(plan));
        var name = "test";
        Product product = new Product(
            UUID.randomUUID(),
            EntityFixture.ALL_ENTITY_IDS,
            TEST_PRODUCT_ID,
            true,
            name,
            name,
            "test",
            TEST_PRODUCT_SKU,
            null,
            Instant.now(),
            null
        );
        when(mockProductCatalogGetService.getProduct(TEST_PRODUCT_ID)).thenReturn(product);
    }

    private static Recurrence getRecurrence(Cycle cycle) {
        return new Recurrence(cycle, 1);
    }
}
