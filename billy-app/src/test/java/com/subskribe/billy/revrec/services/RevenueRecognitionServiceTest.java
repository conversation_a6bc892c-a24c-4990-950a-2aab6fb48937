package com.subskribe.billy.revrec.services;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.model.AccountingPeriod;
import com.subskribe.billy.accounting.model.AccountingPeriodStatus;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.invoice.SellingPriceCalculator;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoRetrievalService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.revrec.db.RecognitionEventDAO;
import com.subskribe.billy.revrec.db.RecognitionRuleDAO;
import com.subskribe.billy.revrec.db.RecognitionScheduleDAO;
import com.subskribe.billy.revrec.db.RecognitionTransactionDAO;
import com.subskribe.billy.revrec.mapper.TransactionLineItemMapper;
import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.revrec.model.RecognitionSchedule;
import com.subskribe.billy.revrec.model.RecognitionTransaction;
import com.subskribe.billy.revrec.model.SyntheticRevenueProjection;
import com.subskribe.billy.revrec.model.TransactionLineItem;
import com.subskribe.billy.revrec.model.TransactionType;
import com.subskribe.billy.shared.enums.RecognitionSource;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.tenantjob.service.TenantJobGetService;
import com.subskribe.billy.test.utilities.MockChargeBuilder;
import com.subskribe.billy.test.utilities.MockOrderLineBuilder;
import com.subskribe.billy.test.utilities.MockRecognitionRuleBuilder;
import com.subskribe.billy.time.TimeZoneService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class RevenueRecognitionServiceTest {

    private static final ZoneId ZONE_ID = TimeZoneService.getDefaultTimeZone().toZoneId();
    private static final String SUBSCRIPTION_ID = "SUB-1234567";
    private static final UUID RAMP_GROUP_ID = UUID.randomUUID();

    private RevenueRecognitionService revenueRecognitionService;

    @Mock
    private FeatureService featureService;

    @Mock
    private PlatformFeatureService platformFeatureService;

    @Mock
    private RecognitionRuleDAO recognitionRuleDAO;

    @Mock
    private RecognitionRuleIdGenerator recognitionRuleIdGenerator;

    @Mock
    private RecognitionScheduleDAO recognitionScheduleDAO;

    @Mock
    private RecognitionScheduleIdGenerator recognitionScheduleIdGenerator;

    @Mock
    private RecognitionTransactionDAO recognitionTransactionDAO;

    @Mock
    private RecognitionEventDAO recognitionEventDAO;

    @Mock
    private RecognitionTransactionIdGenerator recognitionTransactionIdGenerator;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private DSLContextProvider dslContextProvider;

    @Mock
    private ProductCatalogGetService productCatalogGetService;

    @Mock
    private InvoiceService invoiceService;

    @Mock
    private InvoiceRetrievalService invoiceRetrievalService;

    @Mock
    private AccountingPeriodService accountingPeriodService;

    @Mock
    private RevenueRecognitionEventService revenueRecognitionEventService;

    private final TenantSettingService tenantSettingService = TenantSettingServiceFixture.getDefault();

    @Mock
    private OrderGetService orderGetService;

    @Mock
    private SubscriptionGetService subscriptionGetService;

    @Mock
    private EventPublishingService eventPublishingService;

    @Mock
    private CreditMemoRetrievalService creditMemoRetrievalService;

    @Mock
    private SellingPriceCalculator sellingPriceCalculator;

    @Mock
    private ProrationConfigurationGetService prorationConfigurationGetService;

    @Mock
    private EntityContextResolver entityContextResolver;

    @Mock
    private Subscription subscription;

    @Mock
    private TenantJobDispatcherService tenantJobDispatcherService;

    @Mock
    private TenantJobGetService tenantJobGetService;

    @Mock
    private EntityContextProvider entityContextProvider;

    private TransactionLineItemMapper transactionLineItemMapper;

    @Captor
    ArgumentCaptor<RecognitionSchedule> recognitionScheduleArgumentCaptor;

    @Captor
    ArgumentCaptor<List<RecognitionTransaction>> recognitionTransactionsArgumentCaptor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        OrderLineItem mockDebookOrderLineItem = mockDebookOrderLineItem();
        when(orderGetService.getOrderLineItemByOrderLineItemId(any())).thenReturn(Optional.of(mockDebookOrderLineItem));
        when(subscription.getSubscriptionId()).thenReturn(SUBSCRIPTION_ID);
        when(subscriptionGetService.getSubscription(any())).thenReturn(subscription);

        RevenueRecognitionGetService revenueRecognitionGetService = new RevenueRecognitionGetService(
            recognitionRuleDAO,
            recognitionTransactionDAO,
            recognitionScheduleDAO,
            recognitionEventDAO,
            accountingPeriodService,
            productCatalogGetService,
            dslContextProvider,
            tenantIdProvider,
            invoiceRetrievalService,
            orderGetService,
            creditMemoRetrievalService
        );

        revenueRecognitionService = new RevenueRecognitionService(
            recognitionRuleDAO,
            recognitionRuleIdGenerator,
            recognitionScheduleDAO,
            recognitionScheduleIdGenerator,
            recognitionTransactionDAO,
            recognitionEventDAO,
            recognitionTransactionIdGenerator,
            dslContextProvider,
            tenantIdProvider,
            productCatalogGetService,
            invoiceService,
            invoiceRetrievalService,
            accountingPeriodService,
            featureService,
            platformFeatureService,
            revenueRecognitionGetService,
            revenueRecognitionEventService,
            tenantSettingService,
            orderGetService,
            subscriptionGetService,
            eventPublishingService,
            creditMemoRetrievalService,
            sellingPriceCalculator,
            prorationConfigurationGetService,
            entityContextResolver,
            tenantJobDispatcherService,
            tenantJobGetService,
            entityContextProvider
        );
        transactionLineItemMapper = Mappers.getMapper(TransactionLineItemMapper.class);
    }

    @Test
    public void validateOrderBasedSchedule_rampWithNoUpsell() {
        AccountingPeriod currentPeriod = mockAccountingPeriod(Instant.parse("2022-01-01T08:00:00.00Z"), Instant.parse("2022-01-31T08:00:00.00Z"));
        RecognitionRule recognitionRule = new MockRecognitionRuleBuilder().build();
        Instant startDate = Instant.parse("2022-01-15T08:00:00.00Z");
        Instant endDate = Instant.parse("2023-01-15T08:00:00.00Z");
        List<Integer> rampQuantities = List.of(10, 10, 10, 10);
        List<BigDecimal> rampAmounts = Stream.of(100, 100, 100, 100).map(BigDecimal::new).collect(Collectors.toList());
        Instant effectiveDate = Instant.parse("2022-01-15T08:00:00.00Z");
        List<OrderLineItem> rampLineItems = mockRampLineItems(rampQuantities, rampAmounts, effectiveDate);
        Charge charge = new MockChargeBuilder().withRecognitionRuleId(recognitionRule.getRuleId()).build();

        // Set up mock calls
        List<AccountingPeriod> accountingPeriods = mockAccountingPeriods(startDate, endDate);
        when(accountingPeriodService.generateAccountingPeriods(any())).thenReturn(accountingPeriods);
        when(accountingPeriodService.getCurrentAccountingPeriod(any())).thenReturn(Optional.of(currentPeriod));
        when(accountingPeriodService.getAccountingPeriodByInstant(any())).thenReturn(currentPeriod);
        when(orderGetService.getOrderByOrderId(any())).thenReturn(mockOrder(rampLineItems));
        when(productCatalogGetService.getChargeByChargeId(any())).thenReturn(charge);
        when(recognitionRuleDAO.getRecognitionRuleByRuleId(any())).thenReturn(Optional.of(recognitionRule));
        when(accountingPeriodService.getAccountingPeriodsByAccountingPeriodIds(any())).thenReturn(accountingPeriods);
        var spyRevenueRecognitionService = spy(revenueRecognitionService);
        doReturn(null).when(spyRevenueRecognitionService).insertScheduleAndTransactions(any(), any());
        spyRevenueRecognitionService.generateRecognitionSchedulesForSourceTransaction(TransactionType.ORDER, "ORD-0001");
        verify(spyRevenueRecognitionService, times(1)).insertScheduleAndTransactions(
            recognitionScheduleArgumentCaptor.capture(),
            recognitionTransactionsArgumentCaptor.capture()
        );

        RecognitionSchedule capturedSchedule = recognitionScheduleArgumentCaptor.getValue();
        BigDecimal expectedScheduleRevenue = new BigDecimal("400.00");
        Assertions.assertThat(capturedSchedule.getTotalRevenue().setScale(2, RoundingMode.DOWN)).isEqualTo(expectedScheduleRevenue);

        List<RecognitionTransaction> capturedTransactions = recognitionTransactionsArgumentCaptor.getValue();
        BigDecimal sumOfTransactions = capturedTransactions
            .stream()
            .map(RecognitionTransaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .setScale(2, RoundingMode.DOWN);
        Assertions.assertThat(sumOfTransactions).isEqualTo(expectedScheduleRevenue);

        List<BigDecimal> capturedTransactionAmounts = capturedTransactions.stream().map(RecognitionTransaction::getAmount).toList();
        List<BigDecimal> expectedTransactionAmounts = Stream.of(
            "18.28",
            "33.33",
            "33.33",
            "33.33",
            "33.33",
            "33.33",
            "33.33",
            "33.33",
            "33.33",
            "33.33",
            "33.33",
            "33.33",
            "15.09"
        )
            .map(BigDecimal::new)
            .toList();
        Assertions.assertThat(capturedTransactionAmounts).containsExactlyInAnyOrderElementsOf(expectedTransactionAmounts);
    }

    @Test
    public void validateOrderBasedSchedule_rampWithIncreasedQuantityAndAmount() {
        AccountingPeriod currentPeriod = mockAccountingPeriod(Instant.parse("2022-01-01T08:00:00.00Z"), Instant.parse("2022-01-31T08:00:00.00Z"));
        RecognitionRule recognitionRule = new MockRecognitionRuleBuilder().build();
        Instant startDate = Instant.parse("2022-01-15T08:00:00.00Z");
        Instant endDate = Instant.parse("2023-01-15T08:00:00.00Z");
        List<Integer> rampQuantities = List.of(10, 10, 20, 20);
        List<BigDecimal> rampAmounts = Stream.of(100, 200, 250, 350).map(BigDecimal::new).collect(Collectors.toList());
        Instant effectiveDate = Instant.parse("2022-01-15T08:00:00.00Z");
        List<OrderLineItem> rampLineItems = mockRampLineItems(rampQuantities, rampAmounts, effectiveDate);
        Charge charge = new MockChargeBuilder().withRecognitionRuleId(recognitionRule.getRuleId()).build();

        // Set up mock calls
        List<AccountingPeriod> accountingPeriods = mockAccountingPeriods(startDate, endDate);
        when(accountingPeriodService.generateAccountingPeriods(any())).thenReturn(accountingPeriods);
        when(accountingPeriodService.getCurrentAccountingPeriod(any())).thenReturn(Optional.of(currentPeriod));
        when(accountingPeriodService.getAccountingPeriodByInstant(any())).thenReturn(currentPeriod);
        when(orderGetService.getOrderByOrderId(any())).thenReturn(mockOrder(rampLineItems));
        when(productCatalogGetService.getChargeByChargeId(any())).thenReturn(charge);
        when(recognitionRuleDAO.getRecognitionRuleByRuleId(any())).thenReturn(Optional.of(recognitionRule));
        when(accountingPeriodService.getAccountingPeriodsByAccountingPeriodIds(any())).thenReturn(accountingPeriods);
        var spyRevenueRecognitionService = spy(revenueRecognitionService);
        doReturn(null).when(spyRevenueRecognitionService).insertScheduleAndTransactions(any(), any());
        spyRevenueRecognitionService.generateRecognitionSchedulesForSourceTransaction(TransactionType.ORDER, "ORD-0001");
        verify(spyRevenueRecognitionService, times(1)).insertScheduleAndTransactions(
            recognitionScheduleArgumentCaptor.capture(),
            recognitionTransactionsArgumentCaptor.capture()
        );

        RecognitionSchedule capturedSchedule = recognitionScheduleArgumentCaptor.getValue();
        BigDecimal expectedScheduleRevenue = new BigDecimal("900.00");
        Assertions.assertThat(capturedSchedule.getTotalRevenue().setScale(2, RoundingMode.DOWN)).isEqualTo(expectedScheduleRevenue);

        List<RecognitionTransaction> capturedTransactions = recognitionTransactionsArgumentCaptor.getValue();
        BigDecimal sumOfTransactions = capturedTransactions
            .stream()
            .map(RecognitionTransaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .setScale(2, RoundingMode.DOWN);
        Assertions.assertThat(sumOfTransactions).isEqualTo(expectedScheduleRevenue);

        List<BigDecimal> capturedTransactionAmounts = capturedTransactions.stream().map(RecognitionTransaction::getAmount).toList();
        List<BigDecimal> expectedTransactionAmounts = Stream.of(
            "27.42",
            "50.00",
            "50.00",
            "50.00",
            "50.00",
            "50.00",
            "77.42",
            "100.00",
            "100.00",
            "100.00",
            "100.00",
            "100.00",
            "45.16"
        )
            .map(BigDecimal::new)
            .toList();
        Assertions.assertThat(capturedTransactionAmounts).containsExactlyInAnyOrderElementsOf(expectedTransactionAmounts);
    }

    /*
        Create order with these line items - (1) with order-based rule, (2) with invoice based rule (3) no revrec rule
        Keep the quantity same.
        Expected Result: When the order is executed,
        (a) only one schedule should be generated
        (b) the quantity and amount should match the line item 1
     */
    @Test
    public void validateOrderBasedSchedule_mixedBag() {
        AccountingPeriod currentPeriod = mockAccountingPeriod(Instant.parse("2022-01-01T00:00:00.00Z"), Instant.parse("2022-01-31T00:00:00.00Z"));
        Instant startDate = Instant.parse("2022-01-01T00:00:00.00Z");
        Instant endDate = Instant.parse("2023-01-01T00:00:00.00Z");
        Long quantity = 10L;
        BigDecimal expectedAmount = new BigDecimal("1000.00");

        // Line Item 1 - Order Based
        RecognitionRule recognitionRule1 = new MockRecognitionRuleBuilder().withSource(RecognitionSource.ORDER).build();
        Charge mockCharge1 = new MockChargeBuilder().withRecognitionRuleId(recognitionRule1.getRuleId()).build();
        OrderLineItem orderLineItem1 = new MockOrderLineBuilder()
            .withEffectiveDate(startDate)
            .withEndDate(endDate)
            .withQuantity(quantity)
            .withAmount(expectedAmount)
            .withChargeId(mockCharge1.getChargeId())
            .build();

        // Line Item 2 - Invoice Based
        RecognitionRule recognitionRule2 = new MockRecognitionRuleBuilder().withSource(RecognitionSource.INVOICE).build();
        Charge mockCharge2 = new MockChargeBuilder().withRecognitionRuleId(recognitionRule2.getRuleId()).build();
        OrderLineItem orderLineItem2 = new MockOrderLineBuilder()
            .withEffectiveDate(startDate)
            .withEndDate(endDate)
            .withQuantity(quantity)
            .withAmount(BigDecimal.valueOf(2000))
            .withChargeId(mockCharge2.getChargeId())
            .build();

        // Line Item 3 - No RevRec Rule
        Charge mockCharge3 = new MockChargeBuilder().build();
        OrderLineItem orderLineItem3 = new MockOrderLineBuilder()
            .withEffectiveDate(startDate)
            .withEndDate(endDate)
            .withQuantity(quantity)
            .withAmount(BigDecimal.valueOf(3000))
            .withChargeId(mockCharge3.getChargeId())
            .build();

        List<OrderLineItem> orderLineItems = List.of(orderLineItem1, orderLineItem2, orderLineItem3);
        Order order = mockOrder(orderLineItems);

        // Set up mock calls
        List<AccountingPeriod> accountingPeriods = mockAccountingPeriods(startDate, endDate);
        when(accountingPeriodService.generateAccountingPeriods(any())).thenReturn(accountingPeriods);
        when(accountingPeriodService.getCurrentAccountingPeriod(any())).thenReturn(Optional.of(currentPeriod));
        when(accountingPeriodService.getAccountingPeriodByInstant(any())).thenReturn(currentPeriod);
        when(orderGetService.getOrderByOrderId(any())).thenReturn(order);
        when(productCatalogGetService.getChargeByChargeId(mockCharge1.getChargeId())).thenReturn(mockCharge1);
        when(productCatalogGetService.getChargeByChargeId(mockCharge2.getChargeId())).thenReturn(mockCharge2);
        when(productCatalogGetService.getChargeByChargeId(mockCharge3.getChargeId())).thenReturn(mockCharge3);
        when(recognitionRuleDAO.getRecognitionRuleByRuleId(recognitionRule1.getRuleId())).thenReturn(Optional.of(recognitionRule1));
        when(recognitionRuleDAO.getRecognitionRuleByRuleId(recognitionRule2.getRuleId())).thenReturn(Optional.of(recognitionRule2));
        when(accountingPeriodService.getAccountingPeriodsByAccountingPeriodIds(any())).thenReturn(accountingPeriods);
        var spyRevenueRecognitionService = spy(revenueRecognitionService);
        doReturn(null).when(spyRevenueRecognitionService).insertScheduleAndTransactions(any(), any());

        // generate schedule
        spyRevenueRecognitionService.generateRecognitionSchedulesForSourceTransaction(TransactionType.ORDER, order.getOrderId());
        verify(spyRevenueRecognitionService).insertScheduleAndTransactions(
            recognitionScheduleArgumentCaptor.capture(),
            recognitionTransactionsArgumentCaptor.capture()
        );
        RecognitionSchedule capturedSchedule = recognitionScheduleArgumentCaptor.getValue();
        List<RecognitionTransaction> capturedTransactions = recognitionTransactionsArgumentCaptor.getValue();

        Assertions.assertThat(capturedSchedule.getTotalRevenue().setScale(2, RoundingMode.DOWN)).isEqualTo(expectedAmount);

        BigDecimal sumOfTransactions = capturedTransactions
            .stream()
            .map(RecognitionTransaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .setScale(2, RoundingMode.DOWN);
        Assertions.assertThat(sumOfTransactions).isEqualTo(expectedAmount);
    }

    // Todo - move away from months even to partial prorated
    @Test
    public void whenMonthsEvenSchedule_thenTransactionsSumUpToInvoiceItemAmount() {
        AccountingPeriod currentPeriod = mockAccountingPeriod(Instant.parse("2022-01-01T00:00:00.00Z"), Instant.parse("2022-01-31T00:00:00.00Z"));

        RecognitionRule recognitionRule = new MockRecognitionRuleBuilder().withSource(RecognitionSource.INVOICE).monthsEven().build();
        Instant startDate = Instant.parse("2022-01-22T00:00:00.00Z");
        Instant endDate = Instant.parse("2023-01-22T00:00:00.00Z");
        Invoice invoice = mockInvoice(currentPeriod.getStartDate());
        InvoiceItem invoiceItem = mockInvoiceItem("1300", startDate, endDate, invoice.getId());
        List<AccountingPeriod> accountingPeriods = mockAccountingPeriods(startDate, endDate);
        RecognitionSchedule recognitionSchedule = mockRecognitionSchedule(invoiceItem, recognitionRule);
        TransactionLineItem transactionLineItem = transactionLineItemMapper.fromInvoiceItem(invoiceItem);
        when(invoiceRetrievalService.getInvoiceById(any())).thenReturn(invoice);
        List<RecognitionTransaction> recognitionTransactions = revenueRecognitionService.prepareOverTimeSchedule(
            recognitionRule,
            transactionLineItem,
            accountingPeriods,
            recognitionSchedule,
            currentPeriod
        );

        BigDecimal sumOfTransactions = recognitionTransactions
            .stream()
            .map(RecognitionTransaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal transactionAmount = new BigDecimal("100.00");

        Assertions.assertThat(sumOfTransactions).isEqualByComparingTo("1300.00");
        Assertions.assertThat(recognitionTransactions).allMatch(transaction -> transaction.getAmount().compareTo(transactionAmount) == 0);
        Assertions.assertThat(accountingPeriods).hasSize(13);
    }

    @Test
    public void whenSyntheticGenerationIsAppliedOverTime_thenTransactionsAreProducedCorrectly() {
        Instant startDate = Instant.parse("2022-01-22T00:00:00.00Z");
        Instant endDate = Instant.parse("2023-01-22T00:00:00.00Z");
        Invoice invoice = mockInvoice(startDate);
        when(invoiceRetrievalService.getInvoiceById(any())).thenReturn(invoice);
        InvoiceItem invoiceItem = mockInvoiceItem("1200", startDate, endDate, invoice.getId());
        List<AccountingPeriod> testPeriods = mockAccountingPeriods(startDate, endDate);
        testPeriods.forEach(p -> p.setStatus(AccountingPeriodStatus.UPCOMING));
        // remove the last element
        // TODO: this is an artifact of mock method we need exactly 12 accounting periods getting back 13
        testPeriods.remove(testPeriods.size() - 1);
        when(accountingPeriodService.generateSyntheticAccountingPeriod(any(Period.class))).thenReturn(testPeriods);
        when(accountingPeriodService.getAccountingPeriodsByAccountingPeriodIds(any())).thenReturn(testPeriods);
        SyntheticRevenueProjection revenueProjection = revenueRecognitionService.createSyntheticRevenueTransactions(
            invoiceItem,
            new MockRecognitionRuleBuilder().withSource(RecognitionSource.INVOICE).monthsEven().build()
        );

        // simple assertions
        Assertions.assertThat(revenueProjection.getCurrentAccountingPeriod()).isNotNull();
        Assertions.assertThat(revenueProjection.getSyntheticSchedule()).isNotNull();
        Assertions.assertThat(revenueProjection.getSyntheticSchedule().getDeferredRevenue()).isEqualTo("1200.00");

        Assertions.assertThat(revenueProjection.getSyntheticTransactions()).hasSize(12);
        revenueProjection.getSyntheticTransactions().forEach(txn -> Assertions.assertThat(txn.getAmount()).isEqualTo("100.00"));
    }

    @Test
    public void whenSyntheticGenerationIsAppliedOneTime_thenTransactionsAreProducedCorrectly() {
        Instant startDate = Instant.parse("2022-01-22T00:00:00.00Z");
        Instant endDate = Instant.parse("2023-01-22T00:00:00.00Z");
        Invoice invoice = mockInvoice(startDate);
        InvoiceItem invoiceItem = mockInvoiceItem("1200", startDate, endDate, invoice.getId());
        List<AccountingPeriod> testPeriods = mockAccountingPeriods(startDate, endDate);
        testPeriods.forEach(p -> p.setStatus(AccountingPeriodStatus.UPCOMING));
        // remove the last element
        // TODO: this is an artifact of mock method we need exactly 12 accounting periods getting back 13
        testPeriods.remove(testPeriods.size() - 1);
        when(accountingPeriodService.generateSyntheticAccountingPeriod(any(Period.class))).thenReturn(testPeriods);
        SyntheticRevenueProjection revenueProjection = revenueRecognitionService.createSyntheticRevenueTransactions(
            invoiceItem,
            new MockRecognitionRuleBuilder().pointInTime().build()
        );

        // simple assertions
        Assertions.assertThat(revenueProjection.getCurrentAccountingPeriod()).isNotNull();
        Assertions.assertThat(revenueProjection.getSyntheticSchedule()).isNotNull();
        Assertions.assertThat(revenueProjection.getSyntheticSchedule().getDeferredRevenue()).isEqualTo("1200.00");

        Assertions.assertThat(revenueProjection.getSyntheticTransactions()).hasSize(1);
        revenueProjection.getSyntheticTransactions().forEach(txn -> Assertions.assertThat(txn.getAmount()).isEqualTo("1200.00"));
    }

    private static Invoice mockInvoice(Instant invoiceDate) {
        return new Invoice.InvoiceBuilder()
            .id(new Invoice.InternalId(UUID.randomUUID()))
            .invoiceDate(invoiceDate)
            .currency("USD")
            .subscriptionId(SUBSCRIPTION_ID)
            .createInvoice();
    }

    private static OrderLineItem mockDebookOrderLineItem() {
        return new OrderLineItem();
    }

    private static InvoiceItem mockInvoiceItem(String amount, Instant startDate, Instant endDate, Invoice.InternalId invoiceId) {
        return InvoiceItem.InvoiceItemBuilder.builder()
            .id(new InvoiceItem.Id(UUID.randomUUID().toString()))
            .subscriptionChargeId(UUID.randomUUID().toString())
            .subscriptionChargeGroupId(UUID.randomUUID().toString())
            .quantity(1L)
            .amount(new BigDecimal(amount))
            .periodStartDate(startDate)
            .periodEndDate(endDate)
            .invoiceId(invoiceId)
            .createInvoiceItem();
    }

    private Order mockOrder(List<OrderLineItem> lineItems) {
        var order = new Order();
        order.setOrderId("ORD-0001");
        order.setLineItems(lineItems);
        return order;
    }

    private List<OrderLineItem> mockRampLineItems(List<Integer> rampQuantities, List<BigDecimal> rampAmounts, Instant effectiveDate) {
        List<OrderLineItem> orderLineItems = new ArrayList<>();
        for (int i = 0; i < rampQuantities.size(); i++) {
            Instant endDate = DateTimeCalculator.plusMonths(ZONE_ID, effectiveDate, 3);
            OrderLineItem orderLineItem = new OrderLineItem();
            orderLineItem.setOrderLineId(UUID.randomUUID().toString());
            orderLineItem.setEntityId(EntityFixture.ENTITY_1_ID);
            orderLineItem.setEffectiveDate(effectiveDate);
            orderLineItem.setEndDate(endDate);
            orderLineItem.setPlanId("PLAN-0001");
            orderLineItem.setChargeId("CHRG-0001");
            orderLineItem.setRampGroupId(RAMP_GROUP_ID);
            orderLineItem.setSubscriptionChargeGroupId("SUBGRP-0001");
            orderLineItem.setIsRamp(true);
            orderLineItem.setQuantity(rampQuantities.get(i));
            orderLineItem.setAmount(rampAmounts.get(i));
            orderLineItems.add(orderLineItem);
            effectiveDate = endDate;
        }
        return orderLineItems;
    }

    private RecognitionSchedule mockRecognitionSchedule(InvoiceItem invoiceItem, RecognitionRule rule) {
        RecognitionSchedule schedule = new RecognitionSchedule();
        schedule.setScheduleId("RSCH-0001");
        schedule.setTotalRevenue(invoiceItem.getAmount());
        schedule.setStartDate(invoiceItem.getPeriodStartDate());
        schedule.setEndDate(invoiceItem.getPeriodEndDate());
        schedule.setRuleId(rule.getRuleId());
        schedule.setInvoiceLineItemId(UUID.fromString(invoiceItem.getId().id()));
        schedule.setRecognizedRevenue(BigDecimal.ZERO);
        schedule.setDeferredRevenue(invoiceItem.getAmount());
        return schedule;
    }

    private List<AccountingPeriod> mockAccountingPeriods(Instant startDate, Instant endDate) {
        List<AccountingPeriod> accountingPeriods = new ArrayList<>();
        Instant periodStartDate = DateTimeCalculator.getStartOfMonth(startDate, ZONE_ID);
        Instant periodEndDate = DateTimeCalculator.plusMonths(ZONE_ID, periodStartDate, 1);
        while (periodStartDate.isBefore(endDate)) {
            AccountingPeriod accountingPeriod = mockAccountingPeriod(periodStartDate, periodEndDate);
            accountingPeriods.add(accountingPeriod);
            periodStartDate = periodEndDate;
            periodEndDate = DateTimeCalculator.plusMonths(ZONE_ID, periodStartDate, 1);
        }
        return accountingPeriods;
    }

    private AccountingPeriod mockAccountingPeriod(Instant startDate, Instant endDate) {
        AccountingPeriod accountingPeriod = new AccountingPeriod();
        accountingPeriod.setStartDate(startDate);
        accountingPeriod.setEndDate(endDate);
        accountingPeriod.setAccountingPeriodId(String.format("%s_%s", startDate.toString(), endDate.toString()));
        accountingPeriod.setStatus(AccountingPeriodStatus.OPEN);
        return accountingPeriod;
    }
}
