package com.subskribe.billy.revrec.bulk;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.accounting.services.AccountingPeriodStatusUpdateService;
import com.subskribe.billy.accounting.services.JournalEntryService;
import com.subskribe.billy.entity.fixtures.EntityContextResolverFixture;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.revrec.bulk.db.BulkRevenueDAO;
import com.subskribe.billy.revrec.bulk.model.BulkRevenueRecognition;
import com.subskribe.billy.revrec.bulk.model.BulkRevenueRecognitionItem;
import com.subskribe.billy.revrec.bulk.model.BulkRevenueRecognitionPhase;
import com.subskribe.billy.revrec.bulk.model.BulkRevenueRecognitionStatus;
import com.subskribe.billy.revrec.bulk.service.BulkRevenueRecognitionIdGenerator;
import com.subskribe.billy.revrec.bulk.service.BulkRevenueService;
import com.subskribe.billy.revrec.services.RevenueRecognitionService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.model.ImmutableTenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobModule;
import com.subskribe.billy.tenantjob.model.TenantJobObjectModel;
import com.subskribe.billy.tenantjob.model.TenantJobStatus;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import com.subskribe.billy.tenantjob.service.TenantJobGetService;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class BulkRevenueServiceTest {

    private final BulkRevenueDAO mockBulkRevenueDao = mock(BulkRevenueDAO.class);

    private final EntityContextResolver entityContextResolver = EntityContextResolverFixture.build();

    private final TenantSettingService mockTenantSettingService = mock(TenantSettingService.class);

    private final RevenueRecognitionService mockRevenueRecognitionService = mock(RevenueRecognitionService.class);

    private final TenantJobGetService mockTenantJobGetService = mock(TenantJobGetService.class);

    private final AccountingPeriodService mockAccountPeriodService = mock(AccountingPeriodService.class);

    private final AccountingPeriodStatusUpdateService mockAccountPeriodStatusUpdateService = mock(AccountingPeriodStatusUpdateService.class);

    private final JournalEntryService mockJournalEntryService = mock(JournalEntryService.class);

    private final BulkRevenueRecognitionIdGenerator mockBulkRevenueRecognitionIdGenerator = mock(BulkRevenueRecognitionIdGenerator.class);

    private final BulkRevenueService bulkRevenueService = new BulkRevenueService(
        mockBulkRevenueDao,
        entityContextResolver,
        mockTenantSettingService,
        mockRevenueRecognitionService,
        mockTenantJobGetService,
        mockAccountPeriodService,
        mockAccountPeriodStatusUpdateService,
        mockJournalEntryService,
        mockBulkRevenueRecognitionIdGenerator
    );

    private static final UUID BULK_REVENUE_RECOGNITION_ID = UUID.randomUUID();

    private static final String BULK_REVENUE_RECOGNITION_NAME = "bulk revenue recognition name";

    private static final String BULK_REVENUE_RECOGNITION_DESCRIPTION = "bulk revenue recognition description";

    private static final String ACCOUNTING_PERIOD_PREFIX = "ACPR-";

    private BulkRevenueRecognition getBulkRevenueRecognition(BulkRevenueRecognitionPhase phase, BulkRevenueRecognitionStatus status) {
        BulkRevenueRecognition bulkRevenueRecognition = new BulkRevenueRecognition();
        bulkRevenueRecognition.setId(BULK_REVENUE_RECOGNITION_ID.toString());
        bulkRevenueRecognition.setName(BULK_REVENUE_RECOGNITION_NAME);
        bulkRevenueRecognition.setDescription(BULK_REVENUE_RECOGNITION_DESCRIPTION);
        bulkRevenueRecognition.setStatus(status);
        bulkRevenueRecognition.setPhase(phase);
        bulkRevenueRecognition.setTargetDate(Instant.now().getEpochSecond());
        return bulkRevenueRecognition;
    }

    private BulkRevenueRecognition getBulkRevenueRecognition(BulkRevenueRecognitionPhase phase) {
        return getBulkRevenueRecognition(phase, BulkRevenueRecognitionStatus.PROCESSING);
    }

    private List<BulkRevenueRecognitionItem> getBulkRevenueRecognitionItems(int size) {
        return IntStream.of(size).mapToObj(this::createBulkRevenueRecognitionItem).collect(Collectors.toList());
    }

    private BulkRevenueRecognitionItem createBulkRevenueRecognitionItem(int num) {
        BulkRevenueRecognitionItem bulkRevenueRecognitionItem = new BulkRevenueRecognitionItem();
        bulkRevenueRecognitionItem.setBulkRevenueRecognitionId(BULK_REVENUE_RECOGNITION_ID.toString());
        bulkRevenueRecognitionItem.setAccountingPeriodId(ACCOUNTING_PERIOD_PREFIX + num);
        return bulkRevenueRecognitionItem;
    }

    @BeforeEach
    public void setup() {}

    @Test
    public void getBulkRevenueRecognitionFailsIfBulkRevenueRecognitionDoesNotExist() {
        when(mockBulkRevenueDao.getBulkRevenueRecognition(BULK_REVENUE_RECOGNITION_ID)).thenReturn(Optional.empty());
        assertThrows(ObjectNotFoundException.class, () -> bulkRevenueService.getBulkRevenueRecognition(BULK_REVENUE_RECOGNITION_ID));
    }

    @Test
    public void getBulkRevenueRecognitionItemsFailsIfNoBulkRevenueRecognitionExists() {
        when(mockBulkRevenueDao.getBulkRevenueRecognition(BULK_REVENUE_RECOGNITION_ID)).thenReturn(Optional.empty());
        assertThrows(ObjectNotFoundException.class, () -> bulkRevenueService.getBulkRevenueRecognitionItems(BULK_REVENUE_RECOGNITION_ID));
    }

    @Test
    public void getBulkRevenueRecognitionItemsFailsIfRevenuesAreNotRecognizedOrJournalEntriesAreNotCreated() {
        when(mockBulkRevenueDao.getBulkRevenueRecognition(BULK_REVENUE_RECOGNITION_ID)).thenReturn(
            Optional.of(getBulkRevenueRecognition(BulkRevenueRecognitionPhase.REVENUE_RECOGNIZING))
        );
        assertThrows(IllegalStateException.class, () -> bulkRevenueService.getBulkRevenueRecognitionItems(BULK_REVENUE_RECOGNITION_ID));
    }

    @Test
    public void readBulkRevenueRecognitionExecutesIfBulkRevenueRecognitionIsAlreadyProcessingAndRecognized() {
        BulkRevenueRecognition bulkRevenueRecognition = getBulkRevenueRecognition(BulkRevenueRecognitionPhase.REVENUE_RECOGNIZED);
        when(mockBulkRevenueDao.getOldestNonBlockedBulkRevenueRecognitionInProcessing()).thenReturn(Optional.of(bulkRevenueRecognition));
        bulkRevenueService.processRevenueRecognitionForTenant();
        verify(mockBulkRevenueDao, never()).moveRevenueRecognitionToProcessing(bulkRevenueRecognition);
    }

    @Test
    public void readBulkRevenueRecognitionExecutesIfBulkRevenueRecognitionIsAlreadyProcessingAndJournalEntriesCreated() {
        BulkRevenueRecognition bulkRevenueRecognition = getBulkRevenueRecognition(BulkRevenueRecognitionPhase.JOURNAL_ENTRIES_CREATED);
        when(mockBulkRevenueDao.getOldestNonBlockedBulkRevenueRecognitionInProcessing()).thenReturn(Optional.of(bulkRevenueRecognition));
        bulkRevenueService.processRevenueRecognitionForTenant();
        verify(mockBulkRevenueDao, never()).moveRevenueRecognitionToProcessing(bulkRevenueRecognition);
    }

    @Test
    public void readBulkRevenueRecognitionExecutesIfBulkRevenueRecognitionIsAlreadyProcessingAndClosed() {
        BulkRevenueRecognition bulkRevenueRecognition = getBulkRevenueRecognition(BulkRevenueRecognitionPhase.ACCOUNTING_PERIOD_CLOSED);
        when(mockBulkRevenueDao.getOldestNonBlockedBulkRevenueRecognitionInProcessing()).thenReturn(Optional.of(bulkRevenueRecognition));
        bulkRevenueService.processRevenueRecognitionForTenant();
        verify(mockBulkRevenueDao, never()).moveRevenueRecognitionToProcessing(bulkRevenueRecognition);
        verify(mockBulkRevenueDao).forceUpdateRevenueRecognitionToSuccess(
            bulkRevenueRecognition,
            BulkRevenueRecognitionPhase.ACCOUNTING_PERIOD_CLOSED
        );
    }

    @Test
    public void readBulkRevenueRecognitionDoesNothingIfNoCreatedBulkRevenueRecognitionExists() {
        when(mockBulkRevenueDao.getOldestNonBlockedBulkRevenueRecognitionInProcessing()).thenReturn(Optional.empty());
        when(mockBulkRevenueDao.getRevenueRecognitionInSingletonStatus(BulkRevenueRecognitionStatus.CREATED)).thenReturn(Optional.empty());

        bulkRevenueService.processRevenueRecognitionForTenant();

        verify(mockBulkRevenueDao, never()).moveRevenueRecognitionToProcessing(any(BulkRevenueRecognition.class));
    }

    @Test
    public void bulkRevenueRecognitionItemsFailIfRevenueRecognitionFails() {
        when(mockBulkRevenueDao.getOldestNonBlockedBulkRevenueRecognitionInProcessing()).thenReturn(Optional.empty());
        BulkRevenueRecognition bulkRevenueRecognitionNotStarted = getBulkRevenueRecognition(
            BulkRevenueRecognitionPhase.REVENUE_RECOGNITION_NOT_STARTED
        );
        when(mockBulkRevenueDao.getRevenueRecognitionInSingletonStatus(BulkRevenueRecognitionStatus.CREATED)).thenReturn(
            Optional.of(bulkRevenueRecognitionNotStarted)
        );
        BulkRevenueRecognition bulkRevenueRecognitionRecognizing = getBulkRevenueRecognition(BulkRevenueRecognitionPhase.REVENUE_RECOGNIZING);
        when(mockBulkRevenueDao.moveRevenueRecognitionToProcessing(bulkRevenueRecognitionNotStarted)).thenReturn(bulkRevenueRecognitionRecognizing);

        List<BulkRevenueRecognitionItem> bulkRevenueRecognitionItems = getBulkRevenueRecognitionItems(10);
        when(mockBulkRevenueDao.getBulkRevenueRecognitionItemsByBulkRevenueRecognitionId(BULK_REVENUE_RECOGNITION_ID.toString())).thenReturn(
            bulkRevenueRecognitionItems
        );
        doThrow(new RuntimeException()).when(mockRevenueRecognitionService).recognizeRevenueAsync(any(String.class));

        bulkRevenueService.processRevenueRecognitionForTenant();

        bulkRevenueRecognitionItems.forEach(bulkRevenueRecognitionItem -> assertFalse(bulkRevenueRecognitionItem.getIsRecognized()));
        verify(mockBulkRevenueDao, times(bulkRevenueRecognitionItems.size())).forceUpdateBulkRevenueRecognitionItemToFailed(
            any(BulkRevenueRecognitionItem.class),
            any(String.class)
        );
    }

    @Test
    public void createdBulkRevenueRecognitionGetsMovedToProcessing() {
        when(mockBulkRevenueDao.getOldestNonBlockedBulkRevenueRecognitionInProcessing()).thenReturn(Optional.empty());
        BulkRevenueRecognition bulkRevenueRecognitionNotStarted = getBulkRevenueRecognition(
            BulkRevenueRecognitionPhase.REVENUE_RECOGNITION_NOT_STARTED
        );
        when(mockBulkRevenueDao.getRevenueRecognitionInSingletonStatus(BulkRevenueRecognitionStatus.CREATED)).thenReturn(
            Optional.of(bulkRevenueRecognitionNotStarted)
        );
        BulkRevenueRecognition bulkRevenueRecognitionRecognizing = getBulkRevenueRecognition(BulkRevenueRecognitionPhase.REVENUE_RECOGNIZING);
        when(mockBulkRevenueDao.moveRevenueRecognitionToProcessing(bulkRevenueRecognitionNotStarted)).thenReturn(bulkRevenueRecognitionRecognizing);

        List<BulkRevenueRecognitionItem> bulkRevenueRecognitionItems = getBulkRevenueRecognitionItems(10);
        when(mockBulkRevenueDao.getBulkRevenueRecognitionItemsByBulkRevenueRecognitionId(BULK_REVENUE_RECOGNITION_ID.toString())).thenReturn(
            bulkRevenueRecognitionItems
        );

        bulkRevenueService.processRevenueRecognitionForTenant();

        verify(mockBulkRevenueDao).moveRevenueRecognitionToProcessing(bulkRevenueRecognitionNotStarted);
        bulkRevenueRecognitionItems.forEach(bulkRevenueRecognitionItem -> assertFalse(bulkRevenueRecognitionItem.getIsRecognized()));
        verify(mockBulkRevenueDao).forceUpdateRevenueRecognitionPhaseTo(
            bulkRevenueRecognitionRecognizing,
            BulkRevenueRecognitionPhase.REVENUE_RECOGNIZED
        );
    }

    @Test
    public void generatingJournalEntriesSucceeds() {
        BulkRevenueRecognition bulkRevenueRecognitionJournalEntriesCreating = getBulkRevenueRecognition(
            BulkRevenueRecognitionPhase.JOURNAL_ENTRIES_CREATING
        );
        when(mockBulkRevenueDao.getOldestNonBlockedBulkRevenueRecognitionInProcessing()).thenReturn(
            Optional.of(bulkRevenueRecognitionJournalEntriesCreating)
        );

        BulkRevenueRecognitionItem bulkRevenueRecognitionItem = createBulkRevenueRecognitionItem(1);
        bulkRevenueRecognitionItem.setIsRecognized(true);
        List<BulkRevenueRecognitionItem> bulkRevenueRecognitionItems = List.of(bulkRevenueRecognitionItem);
        when(mockBulkRevenueDao.getBulkRevenueRecognitionItemsByBulkRevenueRecognitionId(BULK_REVENUE_RECOGNITION_ID.toString())).thenReturn(
            bulkRevenueRecognitionItems
        );

        bulkRevenueService.processRevenueRecognitionForTenant();

        verify(mockBulkRevenueDao, never()).moveRevenueRecognitionToProcessing(bulkRevenueRecognitionJournalEntriesCreating);
    }

    @Test
    public void generatingJournalEntriesFailsWhenNotFound() {
        BulkRevenueRecognition bulkRevenueRecognitionJournalEntriesCreating = getBulkRevenueRecognition(
            BulkRevenueRecognitionPhase.JOURNAL_ENTRIES_CREATING
        );
        when(mockBulkRevenueDao.getOldestNonBlockedBulkRevenueRecognitionInProcessing()).thenReturn(
            Optional.of(bulkRevenueRecognitionJournalEntriesCreating)
        );

        BulkRevenueRecognitionItem bulkRevenueRecognitionItem = createBulkRevenueRecognitionItem(1);
        bulkRevenueRecognitionItem.setIsRecognized(true);
        List<BulkRevenueRecognitionItem> bulkRevenueRecognitionItems = List.of(bulkRevenueRecognitionItem);
        when(mockBulkRevenueDao.getBulkRevenueRecognitionItemsByBulkRevenueRecognitionId(BULK_REVENUE_RECOGNITION_ID.toString())).thenReturn(
            bulkRevenueRecognitionItems
        );
        ObjectNotFoundException objectNotFoundException = mock(ObjectNotFoundException.class);
        doThrow(objectNotFoundException).when(mockJournalEntryService).writeJournalEntriesForAccountingPeriodAsync(any(String.class));
        when(objectNotFoundException.getMessage()).thenReturn("not found");

        bulkRevenueService.processRevenueRecognitionForTenant();

        verify(mockBulkRevenueDao).forceUpdateBulkRevenueRecognitionItemToFailed(eq(bulkRevenueRecognitionItem), any(String.class));
        verify(mockBulkRevenueDao, never()).moveRevenueRecognitionToProcessing(bulkRevenueRecognitionJournalEntriesCreating);
    }

    @Test
    public void closingAccountingPeriodSucceeds() {
        BulkRevenueRecognition bulkRevenueRecognitionJournalEntriesCreated = getBulkRevenueRecognition(
            BulkRevenueRecognitionPhase.JOURNAL_ENTRIES_CREATED
        );
        when(mockBulkRevenueDao.getOldestNonBlockedBulkRevenueRecognitionInProcessing()).thenReturn(
            Optional.of(bulkRevenueRecognitionJournalEntriesCreated)
        );

        BulkRevenueRecognitionItem bulkRevenueRecognitionItem = createBulkRevenueRecognitionItem(1);
        bulkRevenueRecognitionItem.setIsRecognized(true);
        List<BulkRevenueRecognitionItem> bulkRevenueRecognitionItems = List.of(bulkRevenueRecognitionItem);
        when(mockBulkRevenueDao.getBulkRevenueRecognitionItemsByBulkRevenueRecognitionId(BULK_REVENUE_RECOGNITION_ID.toString())).thenReturn(
            bulkRevenueRecognitionItems
        );

        when(
            mockTenantJobGetService.getLatestJob(TenantJobType.CREATE_JOURNAL_ENTRIES, bulkRevenueRecognitionItem.getAccountingPeriodId())
        ).thenReturn(
            Optional.of(
                ImmutableTenantJob.builder()
                    .module(TenantJobModule.ACCOUNTING)
                    .jobType(TenantJobType.CREATE_JOURNAL_ENTRIES)
                    .objectModel(TenantJobObjectModel.ACCOUNTING_PERIOD)
                    .objectId(bulkRevenueRecognitionItem.getAccountingPeriodId())
                    .status(TenantJobStatus.SUCCESSFUL)
                    .build()
            )
        );

        bulkRevenueService.processRevenueRecognitionForTenant();

        verify(mockBulkRevenueDao).forceUpdateBulkRevenueRecognitionItemWithJournalEntriesGenerated(eq(bulkRevenueRecognitionItem), eq(true));
        verify(mockBulkRevenueDao).forceUpdateBulkRevenueRecognitionItemWithIsClosed(eq(bulkRevenueRecognitionItem), eq(true));
        verify(mockBulkRevenueDao, never()).moveRevenueRecognitionToProcessing(bulkRevenueRecognitionJournalEntriesCreated);
    }

    @Test
    public void closingAccountingPeriodFailsWhenNotFound() {
        BulkRevenueRecognition bulkRevenueRecognitionJournalEntriesCreated = getBulkRevenueRecognition(
            BulkRevenueRecognitionPhase.JOURNAL_ENTRIES_CREATED
        );
        when(mockBulkRevenueDao.getOldestNonBlockedBulkRevenueRecognitionInProcessing()).thenReturn(
            Optional.of(bulkRevenueRecognitionJournalEntriesCreated)
        );

        BulkRevenueRecognitionItem bulkRevenueRecognitionItem = createBulkRevenueRecognitionItem(1);
        bulkRevenueRecognitionItem.setIsRecognized(true);
        List<BulkRevenueRecognitionItem> bulkRevenueRecognitionItems = List.of(bulkRevenueRecognitionItem);
        when(mockBulkRevenueDao.getBulkRevenueRecognitionItemsByBulkRevenueRecognitionId(BULK_REVENUE_RECOGNITION_ID.toString())).thenReturn(
            bulkRevenueRecognitionItems
        );
        ObjectNotFoundException objectNotFoundException = mock(ObjectNotFoundException.class);
        when(mockAccountPeriodStatusUpdateService.closeAccountingPeriod(any(String.class))).thenThrow(objectNotFoundException);
        when(objectNotFoundException.getMessage()).thenReturn("not found");

        when(
            mockTenantJobGetService.getLatestJob(TenantJobType.CREATE_JOURNAL_ENTRIES, bulkRevenueRecognitionItem.getAccountingPeriodId())
        ).thenReturn(
            Optional.of(
                ImmutableTenantJob.builder()
                    .module(TenantJobModule.ACCOUNTING)
                    .jobType(TenantJobType.CREATE_JOURNAL_ENTRIES)
                    .objectModel(TenantJobObjectModel.ACCOUNTING_PERIOD)
                    .objectId(bulkRevenueRecognitionItem.getAccountingPeriodId())
                    .status(TenantJobStatus.SUCCESSFUL)
                    .build()
            )
        );

        bulkRevenueService.processRevenueRecognitionForTenant();

        verify(mockBulkRevenueDao).forceUpdateBulkRevenueRecognitionItemToFailed(eq(bulkRevenueRecognitionItem), any(String.class));
        verify(mockBulkRevenueDao, never()).moveRevenueRecognitionToProcessing(bulkRevenueRecognitionJournalEntriesCreated);
    }
}
