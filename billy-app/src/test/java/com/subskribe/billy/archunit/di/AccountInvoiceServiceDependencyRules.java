package com.subskribe.billy.archunit.di;

import com.subskribe.billy.invoice.service.AccountInvoiceService;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition;

@SuppressWarnings("unused")
public class AccountInvoiceServiceDependencyRules {

    private static final String ACCOUNT_SERVICE_PACKAGE = "com.subskribe.billy.account.services";

    @ArchTest
    public static final ArchRule ACCOUNT_INVOICE_SERVICE_DOES_NOT_DEPEND_ON_ACCOUNT_SERVICES = ArchRuleDefinition.noClasses()
        .that()
        .haveFullyQualifiedName(AccountInvoiceService.class.getName())
        .should()
        .dependOnClassesThat()
        .resideInAPackage(ACCOUNT_SERVICE_PACKAGE);
}
