package com.subskribe.billy.archunit;

import static com.tngtech.archunit.core.domain.JavaClass.Predicates.resideInAPackage;

import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaClass;
import java.util.Set;

public class BillyClassPredicates {

    public static final String RESOURCE_CLASS_NAME_PATTERN = ".*Resource";

    public static final DescribedPredicate<JavaClass> DATABASE_ACCESS_CLASSES = resideInAPackage("com.subskribe.billy..db..").or(
        resideInAPackage("com.subskribe.billy..mapper..")
    );

    public static DescribedPredicate<JavaClass> isInAllowList(Set<String> allowList) {
        return new DescribedPredicate<>("is not in allow list") {
            @Override
            public boolean test(JavaClass input) {
                return allowList.contains(input.getFullName());
            }
        };
    }
}
