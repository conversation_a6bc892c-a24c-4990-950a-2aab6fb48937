package com.subskribe.billy.archunit.json;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.subskribe.billy.archunit.BillyClassPredicates;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaConstructorCall;
import com.tngtech.archunit.core.domain.JavaMethodCall;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import java.util.Set;

public class ObjectMapperRules {

    private static final Set<String> JACKSON_DIRECT_USE_ALLOW_LIST = Set.of(
        "com.subskribe.billy.shared.serializer.JacksonProvider",
        "com.subskribe.billy.report.service.ReportService",
        "com.subskribe.billy.test.BillyTestBase",
        "com.subskribe.billy.BillyApplication",
        "com.subskribe.billy.shared.serializer.JacksonProviderTest",
        "com.subskribe.billy.aws.es.SearchRequestTransformTest"
    );

    private static final Set<String> OBJECT_MAPPER_ALLOWED_METHOD_PREFIXES = Set.of("write", "read", "getTypeFactory", "create");

    @ArchTest
    public static final ArchRule NO_OBJECT_MAPPER_CONSTRUCTOR = noClasses()
        .that(DescribedPredicate.not(BillyClassPredicates.isInAllowList(JACKSON_DIRECT_USE_ALLOW_LIST)))
        .should()
        .callConstructorWhere(
            new DescribedPredicate<>("a new ObjectMapper can be created") {
                @Override
                public boolean test(JavaConstructorCall call) {
                    return call.getTarget().getOwner().isAssignableTo(ObjectMapper.class);
                }
            }
        );

    /*
      Why do we need this rule? ObjectMapper objects are perfectly thread safe, and we should be able to reuse them as
      singletons in as many places as we want. However, they are not immutable, and the thread safety is only guaranteed
      once you create them and their configuration is finalized. This rule is to prevent people from reconfiguring
      our shared ObjectMapper instances, by disallowing calls to any methods that change the ObjectMapper's configuration.
    */
    @ArchTest
    public static final ArchRule CANNOT_CONFIGURE_OBJECTMAPPER = noClasses()
        .that(DescribedPredicate.not(BillyClassPredicates.isInAllowList(JACKSON_DIRECT_USE_ALLOW_LIST)))
        .should()
        .callMethodWhere(
            new DescribedPredicate<>("an existing ObjectMapper can be reconfigured") {
                @Override
                public boolean test(JavaMethodCall call) {
                    return (
                        call.getTarget().getOwner().isAssignableTo(ObjectMapper.class) &&
                        (OBJECT_MAPPER_ALLOWED_METHOD_PREFIXES.stream().noneMatch(call.getTarget().getName()::startsWith))
                    );
                }
            }
        );

    @ArchTest
    public static final ArchRule NO_JSON_MAPPER_BUILDER = noClasses()
        .that(DescribedPredicate.not(BillyClassPredicates.isInAllowList(JACKSON_DIRECT_USE_ALLOW_LIST)))
        .should()
        .callMethodWhere(
            new DescribedPredicate<>(" be called") {
                @Override
                public boolean test(JavaMethodCall call) {
                    return call.getTarget().getOwner().isAssignableTo(JsonMapper.class);
                }
            }
        );
}
