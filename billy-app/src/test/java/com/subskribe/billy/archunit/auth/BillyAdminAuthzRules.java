package com.subskribe.billy.archunit.auth;

import static com.subskribe.billy.archunit.BillyMethodPredicates.resourceMethods;
import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

import com.subskribe.billy.archunit.BillyMethodPredicates;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.user.model.Role;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaMethod;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchCondition;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.ConditionEvents;
import com.tngtech.archunit.lang.SimpleConditionEvent;
import java.util.Arrays;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public class BillyAdminAuthzRules {

    private static final Set<String> ADMIN_OUTSIDE_ADMIN_KNOWN_FAILURES = Set.of(
        "com.subskribe.billy.resources.AuthResource.emailLinkForLogin(com.subskribe.billy.auth.model.BillyAuthPrincipal, java.lang.String)",
        "com.subskribe.billy.resources.HubSpotResource.setupHubSpot(com.subskribe.billy.auth.model.BillyAuthPrincipal)",
        "com.subskribe.billy.resources.HubSpotResource.verifySetup(com.subskribe.billy.auth.model.BillyAuthPrincipal)",
        "com.subskribe.billy.resources.InvoiceResource.modifyInvoiceDueDateForTesting(com.subskribe.billy.auth.model.BillyAuthPrincipal, java.lang.String, java.lang.String)",
        "com.subskribe.billy.resources.OrderResource.backfillCancelOrderLines(com.subskribe.billy.auth.model.BillyAuthPrincipal)",
        "com.subskribe.billy.resources.OrderResource.backfillCancelOrderLinesForLastDayCancels(com.subskribe.billy.auth.model.BillyAuthPrincipal, java.lang.String)",
        "com.subskribe.billy.resources.OrderResource.backfillCancelOrderLinesPreviewCsv(com.subskribe.billy.auth.model.BillyAuthPrincipal)",
        "com.subskribe.billy.resources.OrderResource.backfillOrderExecutionDateAndCrmOpportunityId(com.subskribe.billy.auth.model.BillyAuthPrincipal, java.io.InputStream, org.glassfish.jersey.media.multipart.FormDataBodyPart, java.lang.String, javax.ws.rs.core.UriInfo)",
        "com.subskribe.billy.resources.PlanResource.reactivatePlan(com.subskribe.billy.auth.model.BillyAuthPrincipal, java.lang.String)",
        "com.subskribe.billy.resources.UserResource.resendEmailForExistingUser(com.subskribe.billy.auth.model.BillyAuthPrincipal, java.lang.String)"
    );

    private static final Set<Role> ALLOWED_ROLES = Set.of(Role.BILLY_ADMIN, Role.BILLY_ENGINEER);

    @ArchTest
    public static final ArchRule ADMIN_RESOURCE_METHODS_SHOULD_HAVE_INTERNAL_ALLOW_ROLE_ANNOTATIONS = methods()
        .that()
        .areDeclaredInClassesThat()
        .resideInAPackage("com.subskribe.billy.resources.admin")
        .and()
        .areNotPrivate()
        .should()
        .beAnnotatedWith(AllowRoles.class)
        .andShould(
            new ArchCondition<>(" only have BILLY_ENGINEER or BILLY_ADMIN") {
                @Override
                public void check(JavaMethod item, ConditionEvents events) {
                    Set<Role> roles = Arrays.stream(item.getAnnotationOfType(AllowRoles.class).value()).collect(Collectors.toSet());
                    if (!ALLOWED_ROLES.containsAll(roles)) {
                        String message = String.format("method %s contains roles other than BILLY_ENGINEER or BILLY_ADMIN: %s", item, roles);
                        events.add(SimpleConditionEvent.violated(item, message));
                    }

                    if (roles.isEmpty()) {
                        String message = String.format("method %s has empty AllowRoles", item);
                        events.add(SimpleConditionEvent.violated(item, message));
                    }
                }
            }
        );

    @ArchTest
    public static final ArchRule RESOURCE_METHODS_SHOULD_HAVE_ALLOW_ROLE_ANNOTATIONS = resourceMethods()
        .and(DescribedPredicate.not(BillyMethodPredicates.isInAllowList(ADMIN_OUTSIDE_ADMIN_KNOWN_FAILURES)))
        .and()
        .areDeclaredInClassesThat()
        .resideOutsideOfPackage("com.subskribe.billy.resources.admin")
        .should(
            new ArchCondition<>(" only resource methods in the admin package should be annotated to allow BILLY_ENGINEER or BILLY_ADMIN") {
                @Override
                public void check(JavaMethod item, ConditionEvents events) {
                    Optional<AllowRoles> allowRoles = item.tryGetAnnotationOfType(AllowRoles.class);
                    if (allowRoles.isEmpty()) {
                        return;
                    }
                    Set<Role> roles = Arrays.stream(allowRoles.get().value()).collect(Collectors.toSet());
                    if (roles.contains(Role.BILLY_ADMIN) || roles.contains(Role.BILLY_ENGINEER)) {
                        String message = String.format("method %s contains BILLY_ENGINEER or BILLY_ADMIN: %s", item, roles);
                        events.add(SimpleConditionEvent.violated(item, message));
                    }
                }
            }
        );
}
