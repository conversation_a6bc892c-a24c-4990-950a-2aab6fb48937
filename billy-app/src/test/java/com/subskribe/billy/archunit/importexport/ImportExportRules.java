package com.subskribe.billy.archunit.importexport;

import com.subskribe.billy.shared.annotations.ImportExportClass;
import com.subskribe.billy.shared.annotations.ImportExportField;
import com.tngtech.archunit.core.domain.JavaField;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchCondition;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.ConditionEvents;
import com.tngtech.archunit.lang.SimpleConditionEvent;
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class ImportExportRules {

    @ArchTest
    public static final ArchRule IMPORT_EXPORT_FIELDS_DEFINED = ArchRuleDefinition.fields()
        .that()
        .areNotStatic()
        .and()
        .areDeclaredInClassesThat()
        .areAnnotatedWith(ImportExportClass.class)
        .should()
        .beAnnotatedWith(ImportExportField.class)
        .andShould(
            new ArchCondition<>("have correct import/export annotations") {
                @Override
                public void check(JavaField javaField, ConditionEvents conditionEvents) {
                    var annotation = javaField.getAnnotationOfType(ImportExportField.class);
                    if (BooleanUtils.isTrue(annotation.ignore()) || StringUtils.isNotBlank(annotation.header())) {
                        return;
                    }
                    String message = String.format("field %s is not annotated for import/export", javaField.getName());
                    conditionEvents.add(SimpleConditionEvent.violated(javaField, message));
                }
            }
        );
}
