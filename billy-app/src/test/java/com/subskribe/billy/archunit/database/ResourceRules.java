package com.subskribe.billy.archunit.database;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

import com.tngtech.archunit.core.domain.JavaMethod;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchCondition;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.ConditionEvents;
import com.tngtech.archunit.lang.SimpleConditionEvent;
import java.util.Arrays;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

public class ResourceRules {

    @ArchTest
    public static final ArchRule RESOURCE_METHOD_OVERRIDES_MUST_SPECIFY_JSON_RETURN_TYPE = methods()
        .that()
        .areAnnotatedWith(Produces.class)
        .and()
        .areDeclaredInClassesThat()
        .haveSimpleNameEndingWith("Resource")
        .should(specifyJsonIfOverridingProducesAnnotation());

    private static ArchCondition<JavaMethod> specifyJsonIfOverridingProducesAnnotation() {
        return new ArchCondition<>("have correct @Produces annotations") {
            @Override
            public void check(JavaMethod method, ConditionEvents events) {
                var unsatisfied = Arrays.stream(method.getAnnotationOfType(Produces.class).value()).noneMatch(mediaType ->
                    mediaType.equals(MediaType.APPLICATION_JSON)
                );
                if (unsatisfied) {
                    String message = String.format(
                        "Method %s overrides the @Produces annotation without adding the APPLICATION_JSON return type for error cases",
                        method
                    );
                    events.add(SimpleConditionEvent.violated(method, message));
                }
            }
        };
    }
}
