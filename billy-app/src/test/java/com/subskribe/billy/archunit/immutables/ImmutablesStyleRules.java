package com.subskribe.billy.archunit.immutables;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes;
import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

import com.subskribe.billy.shared.immutables.BillyGqlModelStyle;
import com.subskribe.billy.shared.immutables.BillyModelStyle;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import graphql.annotations.annotationTypes.GraphQLNonNull;
import javax.annotation.Nullable;
import org.immutables.value.Value;

@SuppressWarnings("unused")
public class ImmutablesStyleRules {

    @ArchTest
    public static final ArchRule IMMUTABLES_CLASSES_SHOULD_USE_BILLY_STYLE = classes()
        .that()
        .areAnnotatedWith(Value.Immutable.class)
        .should()
        .beAnnotatedWith(BillyModelStyle.class)
        .orShould()
        .beAnnotatedWith(BillyGqlModelStyle.class);

    @ArchTest
    public static final ArchRule BILLY_STYLE_SHOULD_BE_USED_WITH_IMMUTABLES = classes()
        .that()
        .areAnnotatedWith(BillyModelStyle.class)
        .and()
        .areNotAnnotations()
        .should()
        .beAnnotatedWith(Value.Immutable.class);

    @ArchTest
    public static final ArchRule BILLY_GQL_STYLE_SHOULD_BE_USED_WITH_IMMUTABLES = classes()
        .that()
        .areAnnotatedWith(BillyGqlModelStyle.class)
        .should()
        .beAnnotatedWith(Value.Immutable.class)
        .andShould()
        .notBeAnnotatedWith(BillyModelStyle.class);

    @ArchTest
    public static final ArchRule IMMUTABLE_GQL_FIELDS_SHOULD_BE_NULLABLE_OR_GQL_NON_NULL = methods()
        .that()
        .areDeclaredInClassesThat()
        .areAnnotatedWith(BillyGqlModelStyle.class)
        .should()
        .beAnnotatedWith(GraphQLNonNull.class)
        .orShould()
        .beAnnotatedWith(Nullable.class)
        .orShould()
        .haveName("validate");
}
