package com.subskribe.billy.archunit;

import com.tngtech.archunit.core.domain.JavaAccess;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.lang.ArchCondition;
import com.tngtech.archunit.lang.ConditionEvents;
import com.tngtech.archunit.lang.SimpleConditionEvent;
import java.util.Optional;
import java.util.Set;

public class BillyClassConditions {

    private static final Set<String> DB_CALLER_CLASSES_ALLOW_LIST = Set.of(
        "com.subskribe.billy.entity.db.EntityRlsDbTest",
        "com.subskribe.billy.scheduler.job.MultiEntityAccountReceivableContactBackfillJob"
    );

    public static final ArchCondition<JavaClass> ONLY_BE_ACCESSED_BY_SAME_PRODUCT_AREA = new ArchCondition<>(
        "only be accessed by classes in same product area"
    ) {
        @Override
        public void check(JavaClass javaClass, ConditionEvents events) {
            Optional<String> productArea = BillyClassUtils.getProductArea(javaClass);
            if (productArea.isEmpty()) {
                return;
            }
            for (JavaAccess<?> access : javaClass.getAccessesToSelf()) {
                Optional<String> callerProductArea = BillyClassUtils.getProductArea(access.getOrigin().getOwner());
                String className = access.getOrigin().getOwner().getFullName();
                if (callerProductArea.isPresent() && !productArea.equals(callerProductArea) && !DB_CALLER_CLASSES_ALLOW_LIST.contains(className)) {
                    String message = String.format(
                        "Class %s is accessed from class %s in a different product area",
                        javaClass,
                        access.getOrigin().getOwner()
                    );
                    events.add(SimpleConditionEvent.violated(access, message));
                }
            }
        }
    };
}
