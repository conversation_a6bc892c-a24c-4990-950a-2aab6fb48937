package com.subskribe.billy.archunit.gql;

import static com.tngtech.archunit.lang.conditions.ArchConditions.haveNameMatching;
import static com.tngtech.archunit.lang.conditions.ArchConditions.never;
import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

import com.subskribe.billy.archunit.BillyMethodPredicates;
import com.subskribe.billy.graphql.GqlQuery;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import graphql.annotations.annotationTypes.GraphQLField;
import java.util.Set;

@SuppressWarnings("unused")
public class GqlNamingRules {

    private static final Set<String> GQL_WITH_GET_ALLOW_LIST = Set.of(
        "com.subskribe.billy.graphql.GqlQuery.getInvoiceSettlementApplications(graphql.schema.DataFetchingEnvironment, java.lang.String)",
        "com.subskribe.billy.graphql.GqlQuery.getPayment(graphql.schema.DataFetchingEnvironment, java.lang.String)",
        "com.subskribe.billy.graphql.GqlQuery.getPayment(graphql.schema.DataFetchingEnvironment, java.lang.String, java.lang.String)",
        "com.subskribe.billy.graphql.GqlQuery.getPaymentMethods(graphql.schema.DataFetchingEnvironment, java.lang.String)",
        "com.subskribe.billy.graphql.GqlQuery.getSalesforceAccountById(graphql.schema.DataFetchingEnvironment, java.lang.String)"
    );

    @ArchTest
    public static final ArchRule GQL_QUERIES_SHOULD_NOT_START_WITH_GET = methods()
        .that()
        .areDeclaredIn(GqlQuery.class)
        .and()
        .areAnnotatedWith(GraphQLField.class)
        .and(DescribedPredicate.not(BillyMethodPredicates.isInAllowList(GQL_WITH_GET_ALLOW_LIST)))
        .should(never(haveNameMatching("get.*")));
}
