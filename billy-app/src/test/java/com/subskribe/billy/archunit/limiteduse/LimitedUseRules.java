package com.subskribe.billy.archunit.limiteduse;

import static com.tngtech.archunit.core.domain.JavaAccess.Predicates.target;
import static com.tngtech.archunit.core.domain.properties.CanBeAnnotated.Predicates.annotatedWith;

import com.subskribe.billy.archunit.BillyClassPredicates;
import com.subskribe.billy.shared.architecture.InvoiceModuleLimitedUse;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition;
import java.util.Set;

public class LimitedUseRules {

    private static final String INVOICE_MODULE_PACKAGES = "com.subskribe.billy.invoice.service..";

    private static final Set<String> INVOICE_MODULE_LIMITED_USER_EXCEPTION = Set.of(
        // invoice limited use is allowed for BaseOrderImportProcessor for discount calculation
        "com.subskribe.billy.dataimport.processors.BaseOrderImportProcessor",
        // metrics service ARR calculation is allowed to use invoice limited use methods
        "com.subskribe.billy.metrics.MetricsService",
        // Pricing service tests are allowed to call PricingService::calculateCostWithQuantity
        "com.subskribe.billy.pricing.services.PricingServiceTest"
    );

    @ArchTest
    public static final ArchRule INVOICE_MODULE_LIMITED_USE = ArchRuleDefinition.noClasses()
        .that(
            DescribedPredicate.not(
                BillyClassPredicates.isInAllowList(INVOICE_MODULE_LIMITED_USER_EXCEPTION).or(
                    JavaClass.Predicates.resideInAPackage(INVOICE_MODULE_PACKAGES)
                )
            )
        )
        .should()
        .callMethodWhere(target(annotatedWith(InvoiceModuleLimitedUse.class)));
}
