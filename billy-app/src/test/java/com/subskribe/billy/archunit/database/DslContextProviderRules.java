package com.subskribe.billy.archunit.database;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.constructors;
import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

import com.subskribe.billy.archunit.BillyClassPredicates;
import com.subskribe.billy.di.hk2.providers.AllowAllEntitiesDslContext;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.core.domain.JavaConstructor;
import com.tngtech.archunit.core.domain.JavaMethod;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchCondition;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.ConditionEvents;
import com.tngtech.archunit.lang.SimpleConditionEvent;
import java.util.Set;
import javax.inject.Inject;
import org.jooq.DSLContext;

public class DslContextProviderRules {

    private static final Set<String> DSL_CONTEXT_PROVIDER_GET_ALLOW_LIST = Set.of(
        "com.subskribe.billy.docusign.db.DocuSignDAO",
        "com.subskribe.billy.hubspot.db.HubSpotDAO",
        "com.subskribe.billy.salesforce.db.SalesforceDAO",
        "com.subskribe.billy.search.SearchUtils",
        "com.subskribe.billy.subscription.db.SubscriptionServiceTest",
        "com.subskribe.billy.payment.db.PaymentProviderDAO"
    );

    private static final Set<String> DSL_CONTEXT_INJECTION_ALLOW_LIST = Set.of(
        "com.subskribe.billy.auditlog.AuditlogPartitionManager",
        "com.subskribe.billy.dlq.db.DlqDAO",
        "com.subskribe.billy.health.service.HealthCheckService",
        "com.subskribe.billy.payment.integration.db.PaymentIntegrationDAO",
        "com.subskribe.billy.payment.link.db.PaymentLinkDAO",
        "com.subskribe.billy.payment.processor.db.PaymentProcessorJobQueueDAO",
        "com.subskribe.billy.salesroom.db.SalesRoomLinkDAO",
        "com.subskribe.billy.tenant.db.TenantDAO",
        "com.subskribe.billy.auth.services.TenantCognitoService",
        "com.subskribe.billy.looker.LookerUserStatusSwitcher"
    );

    @ArchTest
    public static final ArchRule DSL_CONTEXT_PROVIDER_GET_SHOULD_ONLY_BE_CALLED_FROM_ANNOTATED_CALLERS = methods()
        .that()
        .areDeclaredIn(DSLContextProvider.class)
        .and()
        .haveFullName("com.subskribe.billy.di.hk2.providers.DSLContextProvider.get()")
        .should(onlyBeCalledByClassesOrMethodsAnnotatedWithAllowNonRlsDataAccess());

    private static ArchCondition<JavaMethod> onlyBeCalledByClassesOrMethodsAnnotatedWithAllowNonRlsDataAccess() {
        return new ArchCondition<>("only be called by methods or classes annotated with @AllowNonRlsDataAccess") {
            @Override
            public void check(JavaMethod method, ConditionEvents events) {
                method
                    .getCallsOfSelf()
                    .stream()
                    .filter(call -> !DSL_CONTEXT_PROVIDER_GET_ALLOW_LIST.contains(call.getOrigin().getOwner().getFullName()))
                    .filter(
                        call ->
                            !call.getOrigin().isAnnotatedWith(AllowNonRlsDataAccess.class) &&
                            !call.getOrigin().getOwner().isAnnotatedWith(AllowNonRlsDataAccess.class)
                    )
                    .forEach(call -> events.add(SimpleConditionEvent.violated(method, call.getDescription())));
            }
        };
    }

    @ArchTest
    public static final ArchRule ALL_ENTITIES_DSL_CONTEXT_SHOULD_BE_CREATED_FROM_ANNOTATED_CALLERS = methods()
        .that()
        .areDeclaredIn(DSLContextProvider.class)
        .and()
        .haveName("getAllEntitiesDSLContext")
        .should(onlyBeCalledByClassesOrMethodsAnnotatedWithAllEntitiesDSLContext());

    private static ArchCondition<JavaMethod> onlyBeCalledByClassesOrMethodsAnnotatedWithAllEntitiesDSLContext() {
        return new ArchCondition<>("only be called by methods or classes annotated with @AllowAllEntitiesDslContext") {
            @Override
            public void check(JavaMethod method, ConditionEvents events) {
                method
                    .getCallsOfSelf()
                    .stream()
                    .filter(
                        call ->
                            !call.getOrigin().isAnnotatedWith(AllowAllEntitiesDslContext.class) &&
                            !call.getOrigin().getOwner().isAnnotatedWith(AllowAllEntitiesDslContext.class)
                    )
                    .forEach(call -> events.add(SimpleConditionEvent.violated(method, call.getDescription())));
            }
        };
    }

    @ArchTest
    public static final ArchRule DSL_CONTEXT_SHOULD_NOT_BE_INJECTED_IN_CONSTRUCTORS = constructors()
        .that()
        .areDeclaredInClassesThat(DescribedPredicate.not(BillyClassPredicates.isInAllowList(DSL_CONTEXT_INJECTION_ALLOW_LIST)))
        .and()
        .areAnnotatedWith(Inject.class)
        .should(notHaveDSLContextParameters());

    private static ArchCondition<JavaConstructor> notHaveDSLContextParameters() {
        return new ArchCondition<>("not have DSLContext parameters") {
            @Override
            public void check(JavaConstructor constructor, ConditionEvents events) {
                constructor
                    .getParameterTypes()
                    .stream()
                    .filter(paramType -> {
                        if (paramType instanceof JavaClass) {
                            return ((JavaClass) paramType).isAssignableTo(DSLContext.class);
                        }
                        return false;
                    })
                    .forEach(paramType ->
                        events.add(
                            SimpleConditionEvent.violated(
                                constructor,
                                "Constructor " + constructor.getFullName() + " annotated with @Inject should not have DSLContext parameter"
                            )
                        )
                    );
            }
        };
    }
}
