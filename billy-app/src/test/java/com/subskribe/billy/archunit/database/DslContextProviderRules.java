package com.subskribe.billy.archunit.database;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

import com.subskribe.billy.di.hk2.providers.AllowAllEntitiesDslContext;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.tngtech.archunit.core.domain.JavaMethod;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchCondition;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.ConditionEvents;
import com.tngtech.archunit.lang.SimpleConditionEvent;
import java.util.Set;

public class DslContextProviderRules {

    private static final Set<String> DSL_CONTEXT_PROVIDER_GET_ALLOW_LIST = Set.of(
        "com.subskribe.billy.docusign.db.DocuSignDAO",
        "com.subskribe.billy.hubspot.db.HubSpotDAO",
        "com.subskribe.billy.salesforce.db.SalesforceDAO",
        "com.subskribe.billy.search.SearchUtils",
        "com.subskribe.billy.subscription.db.SubscriptionServiceTest",
        "com.subskribe.billy.payment.db.PaymentProviderDAO"
    );

    @ArchTest
    public static final ArchRule DSL_CONTEXT_PROVIDER_GET_SHOULD_ONLY_BE_CALLED_FROM_ANNOTATED_CALLERS = methods()
        .that()
        .areDeclaredIn(DSLContextProvider.class)
        .and()
        .haveFullName("com.subskribe.billy.di.hk2.providers.DSLContextProvider.get()")
        .should(onlyBeCalledByClassesOrMethodsAnnotatedWithAllowNonRlsDataAccess());

    private static ArchCondition<JavaMethod> onlyBeCalledByClassesOrMethodsAnnotatedWithAllowNonRlsDataAccess() {
        return new ArchCondition<>("only be called by methods or classes annotated with @AllowNonRlsDataAccess") {
            @Override
            public void check(JavaMethod method, ConditionEvents events) {
                method
                    .getCallsOfSelf()
                    .stream()
                    .filter(call -> !DSL_CONTEXT_PROVIDER_GET_ALLOW_LIST.contains(call.getOrigin().getOwner().getFullName()))
                    .filter(
                        call ->
                            !call.getOrigin().isAnnotatedWith(AllowNonRlsDataAccess.class) &&
                            !call.getOrigin().getOwner().isAnnotatedWith(AllowNonRlsDataAccess.class)
                    )
                    .forEach(call -> events.add(SimpleConditionEvent.violated(method, call.getDescription())));
            }
        };
    }

    @ArchTest
    public static final ArchRule ALL_ENTITIES_DSL_CONTEXT_SHOULD_BE_CREATED_FROM_ANNOTATED_CALLERS = methods()
        .that()
        .areDeclaredIn(DSLContextProvider.class)
        .and()
        .haveName("getAllEntitiesDSLContext")
        .should(onlyBeCalledByClassesOrMethodsAnnotatedWithAllEntitiesDSLContext());

    private static ArchCondition<JavaMethod> onlyBeCalledByClassesOrMethodsAnnotatedWithAllEntitiesDSLContext() {
        return new ArchCondition<>("only be called by methods or classes annotated with @AllowAllEntitiesDslContext") {
            @Override
            public void check(JavaMethod method, ConditionEvents events) {
                method
                    .getCallsOfSelf()
                    .stream()
                    .filter(
                        call ->
                            !call.getOrigin().isAnnotatedWith(AllowAllEntitiesDslContext.class) &&
                            !call.getOrigin().getOwner().isAnnotatedWith(AllowAllEntitiesDslContext.class)
                    )
                    .forEach(call -> events.add(SimpleConditionEvent.violated(method, call.getDescription())));
            }
        };
    }
}
