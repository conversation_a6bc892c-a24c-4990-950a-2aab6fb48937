package com.subskribe.billy.archunit.tenant;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses;

import com.subskribe.billy.archunit.BillyClassPredicates;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaMethodCall;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import java.util.Set;

public class TenantThreadSpawnRules {

    private static final Set<String> THREAD_SPAWNING_ALLOW_LIST = Set.of(
        "com.subskribe.billy.platformfeature.service.PlatformFeatureService",
        "com.subskribe.billy.resources.admin.AdminResource",
        "com.subskribe.billy.shared.tenant.TenantContextInjector",
        "com.subskribe.billy.tenant.services.TenantService"
    );

    @ArchTest
    public static final ArchRule THREAD_SPAWNING_INJECTOR_SHOULD_NOT_BE_USED = noClasses()
        .that(DescribedPredicate.not(BillyClassPredicates.isInAllowList(THREAD_SPAWNING_ALLOW_LIST)))
        .should()
        .callMethodWhere(
            new DescribedPredicate<>("calls thread spawning methods") {
                @Override
                public boolean test(JavaMethodCall javaMethodCall) {
                    return (
                        "TenantContextInjector".equals(javaMethodCall.getTargetOwner().getSimpleName()) &&
                        ("spawnThreadAndCallInTenantContext".equals(javaMethodCall.getName()) ||
                            "spawnThreadAndRunInTenantContext".equals(javaMethodCall.getName()))
                    );
                }
            }
        )
        .because("these methods are currently not safe to use");
}
