package com.subskribe.billy.archunit.exceptions;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes;

import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.core.domain.JavaClasses;
import com.tngtech.archunit.core.importer.ClassFileImporter;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchCondition;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.ConditionEvents;
import com.tngtech.archunit.lang.SimpleConditionEvent;
import javax.ws.rs.ext.ExceptionMapper;

public class ExceptionMapperRules {

    private static final JavaClasses ALL_CLASSES = new ClassFileImporter().importPackages("com.subskribe.billy");

    private static final DescribedPredicate<JavaClass> EXTENDS_RUNTIME_EXCEPTION = new DescribedPredicate<>("extends RuntimeException") {
        @Override
        public boolean test(JavaClass javaClass) {
            return javaClass.isAssignableTo(RuntimeException.class) && !javaClass.isEquivalentTo(RuntimeException.class);
        }
    };

    private static final ArchCondition<JavaClass> HAVE_EXCEPTION_MAPPER = new ArchCondition<>("have a corresponding ExceptionMapper") {
        @Override
        public void check(JavaClass exceptionClass, ConditionEvents events) {
            String expectedMapperName = exceptionClass.getSimpleName() + "Mapper";
            String expectedMapperPackage = "com.subskribe.billy.exception.mappers";
            String expectedMapperFullName = expectedMapperPackage + "." + expectedMapperName;

            boolean mapperExists = ALL_CLASSES.stream()
                .anyMatch(clazz -> clazz.getName().equals(expectedMapperFullName) && clazz.isAssignableTo(ExceptionMapper.class));

            if (!mapperExists) {
                events.add(
                    SimpleConditionEvent.violated(
                        exceptionClass,
                        String.format("Exception %s does not have a corresponding mapper %s", exceptionClass.getName(), expectedMapperFullName)
                    )
                );
            }
        }
    };

    @ArchTest
    public static final ArchRule ALL_RUNTIME_EXCEPTIONS_SHOULD_HAVE_MAPPERS = classes()
        .that(EXTENDS_RUNTIME_EXCEPTION)
        .and()
        .resideInAnyPackage("com.subskribe.billy..")
        .and()
        .areNotMemberClasses()
        .and()
        .areNotAnonymousClasses()
        .should(HAVE_EXCEPTION_MAPPER)
        .because("All RuntimeException subclasses should have corresponding ExceptionMapper implementations for consistent error handling");
}
