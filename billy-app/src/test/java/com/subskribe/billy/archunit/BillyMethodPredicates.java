package com.subskribe.billy.archunit;

import static com.subskribe.billy.archunit.BillyClassPredicates.RESOURCE_CLASS_NAME_PATTERN;
import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaMethod;
import com.tngtech.archunit.lang.syntax.elements.GivenMethodsConjunction;
import io.swagger.annotations.Api;
import java.util.Set;
import javax.ws.rs.Path;

public class BillyMethodPredicates {

    public static DescribedPredicate<JavaMethod> isInAllowList(Set<String> allowList) {
        return new DescribedPredicate<>("is not in allow list") {
            @Override
            public boolean test(JavaMethod input) {
                return allowList.contains(input.getFullName());
            }
        };
    }

    public static DescribedPredicate<JavaMethod> isPublicApiMethod() {
        return new DescribedPredicate<>("is not in allow list") {
            @Override
            public boolean test(JavaMethod input) {
                return input.getAnnotations().stream().anyMatch(annotation -> "javax.ws.rs".equals(annotation.getRawType().getPackageName()));
            }
        };
    }

    public static final Set<String> CLASSES_TO_IGNORE = Set.of("com.subskribe.billy.test.DBMigratorBase$FlywayLoadableResource");

    public static GivenMethodsConjunction resourceMethods() {
        return methods()
            .that()
            .areDeclaredInClassesThat()
            .haveNameMatching(RESOURCE_CLASS_NAME_PATTERN)
            .or()
            .areDeclaredInClassesThat()
            .areAnnotatedWith(Api.class)
            .or()
            .areAnnotatedWith(Path.class)
            .and()
            .areNotPrivate()
            .and()
            .areDeclaredInClassesThat(DescribedPredicate.not(BillyClassPredicates.isInAllowList(CLASSES_TO_IGNORE)));
    }
}
