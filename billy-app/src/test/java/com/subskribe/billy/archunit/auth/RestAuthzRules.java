package com.subskribe.billy.archunit.auth;

import static com.subskribe.billy.archunit.BillyMethodPredicates.resourceMethods;

import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;

public class RestAuthzRules {

    @ArchTest
    public static final ArchRule RESOURCE_METHODS_SHOULD_HAVE_ALLOW_ROLE_ANNOTATIONS = resourceMethods()
        .should()
        .beAnnotatedWith(AllowRoles.class)
        .orShould()
        .beAnnotatedWith(AllowUnauthenticated.class)
        .orShould()
        .beAnnotatedWith(AllowAllRoles.class)
        .orShould()
        .beStatic();
}
