package com.subskribe.billy.archunit.tenant;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes;

import com.subskribe.billy.tenant.TenantIdProvider;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaCall;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;

@SuppressWarnings("unused")
public class TenantContextClearRules {

    @ArchTest
    public static final ArchRule CALLS_TO_SET_TENANT_ID_SHOULD_ALSO_CLEAN_THEM = classes()
        .that(
            new DescribedPredicate<>("call TenantIdProvider#setTenantId") {
                @Override
                public boolean test(JavaClass javaClass) {
                    for (JavaCall<?> javaCall : javaClass.getCodeUnitCallsFromSelf()) {
                        if (
                            javaCall.getTargetOwner().getName().equals(TenantIdProvider.class.getName()) &&
                            "setTenantId".equals(javaCall.getTarget().getName())
                        ) {
                            return true;
                        }
                    }
                    return false;
                }
            }
        )
        .should()
        .callMethod(TenantIdProvider.class, "clearThreadLocal");
}
