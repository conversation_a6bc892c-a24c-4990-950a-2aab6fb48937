package com.subskribe.billy.archunit.database;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes;

import com.subskribe.billy.archunit.BillyClassConditions;
import com.subskribe.billy.archunit.BillyClassPredicates;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import java.util.Set;

@SuppressWarnings("unused")
public class DatabaseAccessRules {

    @ArchTest
    public static final ArchRule DAO_SHOULD_BE_IN_DB_PACKAGE = classes()
        .that()
        .haveNameMatching(".*DAO")
        .should()
        .resideInAPackage("com.subskribe.billy..db..");

    private static final Set<String> DB_PRODUCT_AREA_LEAK_ALLOW_LIST = Set.of(
        "com.subskribe.billy.dlq.db.DlqDAO",
        "com.subskribe.billy.document.db.DocumentDAO",
        "com.subskribe.billy.docusign.mapper.DocuSignMapper",
        "com.subskribe.billy.invoice.db.InvoiceDAO",
        "com.subskribe.billy.tenant.db.TenantSettingDAO",
        "com.subskribe.billy.metrics.db.DatabaseMetricsListenerProvider",
        "com.subskribe.billy.productcatalog.db.ProductCatalogDAO",
        "com.subskribe.billy.shared.mapper.BaseMapper",
        "com.subskribe.billy.shared.mapper.CustomFieldAPIMapper",
        "com.subskribe.billy.shared.mapper.OrderObjectMapper",
        "com.subskribe.billy.subscription.db.SubscriptionDAO",
        "com.subskribe.billy.tenant.mapper.UserRecordMapper",
        "com.subskribe.billy.tenant.mapper.UserRecordMapperImpl",
        "com.subskribe.billy.user.db.UserDAO",
        "com.subskribe.billy.search.db.SearchDAO",
        "com.subskribe.billy.opportunity.db.OpportunityDAO",
        "com.subskribe.billy.approvalflowhierarchy.db.ApprovalFlowHierarchyDao",
        "com.subskribe.billy.usergroup.db.UserGroupDAO",
        "com.subskribe.billy.auth.mapper.ApiKeyDetailMapper",
        "com.subskribe.billy.customererror.db.CustomerErrorLogDAO",
        "com.subskribe.billy.account.crm.contact.mapper.CrmContactMapper",
        "com.subskribe.billy.esign.db.EsignDAO",
        "com.subskribe.billy.entity.db.EntityAuthDAO",
        "com.subskribe.billy.payment.mapper.PaymentRetryConfigMapper"
    );

    @ArchTest
    public static final ArchRule DATABASE_ACCESS_CLASSES_SHOULD_NOT_BE_SHARED_ACROSS_PRODUCT_AREAS = classes()
        .that(BillyClassPredicates.DATABASE_ACCESS_CLASSES)
        .and()
        .resideOutsideOfPackage("com.subskribe.billy.shared.db")
        .and(DescribedPredicate.not(BillyClassPredicates.isInAllowList(DB_PRODUCT_AREA_LEAK_ALLOW_LIST)))
        .should(BillyClassConditions.ONLY_BE_ACCESSED_BY_SAME_PRODUCT_AREA);
}
