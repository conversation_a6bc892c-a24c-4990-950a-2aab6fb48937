package com.subskribe.billy.archunit;

import com.tngtech.archunit.core.domain.JavaClass;
import java.util.Optional;

public class BillyClassUtils {

    public static Optional<String> getProductArea(JavaClass javaClass) {
        if (!javaClass.getPackageName().startsWith("com.subskribe.billy")) {
            return Optional.empty();
        }

        String[] classParts = javaClass.getPackageName().split("\\.");

        if (classParts.length < 4) {
            return Optional.empty();
        }

        return Optional.of(classParts[3]);
    }
}
