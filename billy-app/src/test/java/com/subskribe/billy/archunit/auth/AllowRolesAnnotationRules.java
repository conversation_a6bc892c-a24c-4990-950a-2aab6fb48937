package com.subskribe.billy.archunit.auth;

import static com.subskribe.billy.archunit.BillyClassPredicates.RESOURCE_CLASS_NAME_PATTERN;
import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses;
import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noMethods;

import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;

public class AllowRolesAnnotationRules {

    private static final String GQL_AUTH_CONTEXT_NAME_PATTERN = "GqlAuthContext";

    private static final String CUSTOM_FIELD_PROXY = "CustomFieldProxy";

    @ArchTest
    public static final ArchRule NO_ALLOW_ROLES_ANNOTATION_ON_ANY_CLASS = noClasses()
        .that()
        .haveNameNotMatching(RESOURCE_CLASS_NAME_PATTERN)
        .should()
        .beAnnotatedWith(AllowRoles.class);

    @ArchTest
    public static final ArchRule NO_ALLOW_ROLES_ANNOTATION_ON_NON_RESOURCE_CLASS_METHODS = noMethods()
        .that()
        .areDeclaredInClassesThat()
        .haveNameNotMatching(RESOURCE_CLASS_NAME_PATTERN)
        .and()
        .areDeclaredInClassesThat()
        .haveSimpleNameNotContaining(GQL_AUTH_CONTEXT_NAME_PATTERN)
        .and()
        .areDeclaredInClassesThat()
        .haveSimpleNameNotContaining(CUSTOM_FIELD_PROXY)
        .should()
        .beAnnotatedWith(AllowRoles.class);
}
