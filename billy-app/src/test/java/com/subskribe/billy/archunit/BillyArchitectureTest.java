package com.subskribe.billy.archunit;

import com.subskribe.billy.archunit.accounting.AccountingEventOperationRules;
import com.subskribe.billy.archunit.auth.AllowRolesAnnotationRules;
import com.subskribe.billy.archunit.auth.BillyAdminAuthzRules;
import com.subskribe.billy.archunit.auth.EntityContextRules;
import com.subskribe.billy.archunit.auth.GqlAuthzRules;
import com.subskribe.billy.archunit.auth.RestAuthzRules;
import com.subskribe.billy.archunit.catalog.CatalogValidationRules;
import com.subskribe.billy.archunit.database.DatabaseAccessRules;
import com.subskribe.billy.archunit.database.DslContextProviderRules;
import com.subskribe.billy.archunit.database.ResourceRules;
import com.subskribe.billy.archunit.di.AccountInvoiceServiceDependencyRules;
import com.subskribe.billy.archunit.di.CircularDependencyRules;
import com.subskribe.billy.archunit.gql.GqlNamingRules;
import com.subskribe.billy.archunit.immutables.ImmutablesStyleRules;
import com.subskribe.billy.archunit.importexport.ImportExportRules;
import com.subskribe.billy.archunit.json.GsonRules;
import com.subskribe.billy.archunit.json.ObjectMapperRules;
import com.subskribe.billy.archunit.limiteduse.LimitedUseRules;
import com.subskribe.billy.archunit.log.UseInternalLogger;
import com.subskribe.billy.archunit.resource.ApiDocumentationRules;
import com.subskribe.billy.archunit.tenant.EntityContextClearRules;
import com.subskribe.billy.archunit.tenant.TenantContextClearRules;
import com.subskribe.billy.archunit.tenant.TenantThreadSpawnRules;
import com.tngtech.archunit.junit.AnalyzeClasses;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.junit.ArchTests;

@SuppressWarnings("unused")
@AnalyzeClasses(packages = "com.subskribe.billy")
public class BillyArchitectureTest {

    @ArchTest
    static final ArchTests DATABASE_ACCESS_RULES = ArchTests.in(DatabaseAccessRules.class);

    @ArchTest
    static final ArchTests GQL_NAMING_RULES = ArchTests.in(GqlNamingRules.class);

    @ArchTest
    static final ArchTests DSL_CONTEXT_PROVIDER_RULES = ArchTests.in(DslContextProviderRules.class);

    @ArchTest
    static final ArchTests RESOURCE_RULES = ArchTests.in(ResourceRules.class);

    @ArchTest
    static final ArchTests CIRCULAR_DEPENDENCY_RULES = ArchTests.in(CircularDependencyRules.class);

    @ArchTest
    static final ArchTests ACCOUNT_INVOICE_SERVICE_DEPENDENCY = ArchTests.in(AccountInvoiceServiceDependencyRules.class);

    @ArchTest
    static final ArchTests TENANT_CONTEXT_CLEAR_RULES = ArchTests.in(TenantContextClearRules.class);

    @ArchTest
    static final ArchTests ENTITY_CONTEXT_CLEAR_RULES = ArchTests.in(EntityContextClearRules.class);

    @ArchTest
    static final ArchTests IMMUTABLES_STYLE_RULES = ArchTests.in(ImmutablesStyleRules.class);

    @ArchTest
    static final ArchTests ALLOW_ROLES_ANNOTATION_RULES = ArchTests.in(AllowRolesAnnotationRules.class);

    @ArchTest
    static final ArchTests LIMITED_USE_RULES = ArchTests.in(LimitedUseRules.class);

    @ArchTest
    static final ArchTests OBJECT_MAPPER_RULES = ArchTests.in(ObjectMapperRules.class);

    //todo: uncomment when other exceptions are mapped
    //    @ArchTest
    //    static final ArchTests EXCEPTION_MAPPER_RULES = ArchTests.in(ExceptionMapperRules.class);

    @ArchTest
    static final ArchTests GSON_RULES = ArchTests.in(GsonRules.class);

    @ArchTest
    static final ArchTests CATALOG_VALIDATION_RULES = ArchTests.in(CatalogValidationRules.class);

    @ArchTest
    static final ArchTests GQL_AUTHZ_RULES = ArchTests.in(GqlAuthzRules.class);

    @ArchTest
    static final ArchTests REST_AUTHZ_RULES = ArchTests.in(RestAuthzRules.class);

    @ArchTest
    static final ArchTests ADMIN_AUTHZ_RULES = ArchTests.in(BillyAdminAuthzRules.class);

    @ArchTest
    static final ArchTests API_DOC_RULES = ArchTests.in(ApiDocumentationRules.class);

    @ArchTest
    static final ArchTests DO_NOT_USE_SLF4J = ArchTests.in(UseInternalLogger.class);

    @ArchTest
    static final ArchTests TENANT_THREAD_RULES = ArchTests.in(TenantThreadSpawnRules.class);

    @ArchTest
    static final ArchTests IMPORT_EXPORT_RULES = ArchTests.in(ImportExportRules.class);

    @ArchTest
    static final ArchTests ENTITY_CONTEXT_RULES = ArchTests.in(EntityContextRules.class);

    @ArchTest
    static final ArchTests ACCOUNT_EVENT_OPERATION_RULES = ArchTests.in(AccountingEventOperationRules.class);
}
