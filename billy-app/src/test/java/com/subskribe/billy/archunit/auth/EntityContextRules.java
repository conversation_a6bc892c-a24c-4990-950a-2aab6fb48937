package com.subskribe.billy.archunit.auth;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses;

import com.subskribe.billy.archunit.BillyClassPredicates;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.entity.service.EntityAuthContextResolver;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.core.domain.JavaMethodCall;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import java.util.Set;

public class EntityContextRules {

    private static final DescribedPredicate<JavaClass> JOB_PREDICATE = JavaClass.Predicates.simpleNameEndingWith("Job");

    private static final Set<String> ENTITY_CONTEXT_ALLOW_LIST = Set.of(
        "com.subskribe.billy.accounting.services.AccountingPeriodService",
        "com.subskribe.billy.approvalflow.service.ApprovalFlowService",
        "com.subskribe.billy.order.quotebuilder.service.SlackQuoteBuilderService",
        "com.subskribe.billy.platformfeature.service.PlatformFeatureService",
        "com.subskribe.billy.resources.admin.AdminResource",
        "com.subskribe.billy.revrec.services.RevenueEnablementService",
        "com.subskribe.billy.shared.task.queue.processor.TaskExecutor"
    );

    // Please reach out to ME code owner if you need to add a new class to this list.
    private static final Set<String> ALL_ACCESS_ENTITY_CONTEXT_ALLOW_LIST = Set.of(
        "com.subskribe.billy.order.quotebuilder.service.SlackQuoteBuilderService"
    );

    @ArchTest
    public static final ArchRule NO_CLASSES_SHOULD_CONSTRUCT_ENTITY_CONTEXT = noClasses()
        .that(DescribedPredicate.not(BillyClassPredicates.isInAllowList(ENTITY_CONTEXT_ALLOW_LIST).or(JOB_PREDICATE)))
        .should()
        .callMethodWhere(
            new DescribedPredicate<>("calls buildEntityContextByEntityIds") {
                @Override
                public boolean test(JavaMethodCall javaMethodCall) {
                    return (
                        javaMethodCall.getTargetOwner().getName().equals(EntityContext.class.getName()) &&
                        "buildEntityContextByEntityIds".equals(javaMethodCall.getName())
                    );
                }
            }
        );

    @ArchTest
    public static final ArchRule NO_CLASSES_SHOULD_CONSTRUCT_ALL_ACCESS_ENTITY_CONTEXT = noClasses()
        .that(DescribedPredicate.not(BillyClassPredicates.isInAllowList(ALL_ACCESS_ENTITY_CONTEXT_ALLOW_LIST).or(JOB_PREDICATE)))
        .should()
        .callMethodWhere(
            new DescribedPredicate<>("calls getAllAccessEntityContextForUser") {
                @Override
                public boolean test(JavaMethodCall javaMethodCall) {
                    return (
                        javaMethodCall.getTargetOwner().getName().equals(EntityAuthContextResolver.class.getName()) &&
                        "getAllAccessEntityContextForUser".equals(javaMethodCall.getName())
                    );
                }
            }
        );
}
