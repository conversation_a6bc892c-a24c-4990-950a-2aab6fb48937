package com.subskribe.billy.archunit.accounting;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaMethod;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import java.util.Set;

public class AccountingEventOperationRules {

    private static final Set<String> deleteAccountEventLogMethods = Set.of("deleteAccountingEventLog", "deleteAccountingEventLogs");
    private static final Set<String> adminOperationsMethods = Set.of("deleteVoidedPayment", "deleteVoidedInvoice");

    // Caution: Be very careful when modifying this rule and with deleting accounting event logs in general
    // In deleting accounting event logs, we must ensure that any dependent events are accounted for as well.
    // For example, invoices may have payment and credit memos applied to it. When deleting an invoice, the payment and credit memo events need to be handled first.
    @ArchTest
    public static final ArchRule deleteAccountingMethodsCaller = methods()
        .that(isDeleteAccountEventLogsMethods())
        .should()
        .onlyBeCalled()
        .byMethodsThat(isAdminOperationsMethods());

    public static DescribedPredicate<JavaMethod> isDeleteAccountEventLogsMethods() {
        return new DescribedPredicate<>("is delete account event log methods") {
            @Override
            public boolean test(JavaMethod input) {
                return (
                    "com.subskribe.billy.accounting.services.AccountingService".equals(input.getOwner().getFullName()) &&
                    deleteAccountEventLogMethods.contains(input.getName())
                );
            }
        };
    }

    public static DescribedPredicate<JavaMethod> isAdminOperationsMethods() {
        return new DescribedPredicate<>("is admin operations") {
            @Override
            public boolean test(JavaMethod input) {
                return (
                    "com.subskribe.billy.admin.AdminOperationsService".equals(input.getOwner().getFullName()) &&
                    adminOperationsMethods.contains(input.getName())
                );
            }
        };
    }
}
