package com.subskribe.billy.archunit.di;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses;

import com.subskribe.billy.archunit.BillyClassPredicates;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import java.util.Set;

@SuppressWarnings("unused")
public class CircularDependencyRules {

    private static final Set<String> PROVIDER_USE_ALLOW_LIST = Set.of(
        "com.subskribe.billy.productcatalog.validation.ChargeValidation",
        "com.subskribe.billy.productcatalog.ProductCatalogServiceTest",
        "com.subskribe.billy.productcatalog.validation.ChargeTest",
        "com.subskribe.billy.exception.mappers.RuntimeExceptionMapper"
    );

    @ArchTest
    public static final ArchRule NO_CLASSES_SHOULD_USE_PROVIDER = noClasses()
        .that(DescribedPredicate.not(BillyClassPredicates.isInAllowList(PROVIDER_USE_ALLOW_LIST)))
        .should()
        .dependOnClassesThat(
            new DescribedPredicate<>("are assignable to Provider but are not IterableProvider") {
                @Override
                public boolean test(JavaClass javaClass) {
                    return javaClass.isAssignableTo("javax.inject.Provider") && !javaClass.isAssignableTo("org.glassfish.hk2.api.IterableProvider");
                }
            }
        );
}
