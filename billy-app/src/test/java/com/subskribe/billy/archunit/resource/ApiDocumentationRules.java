package com.subskribe.billy.archunit.resource;

import static com.subskribe.billy.archunit.BillyClassPredicates.RESOURCE_CLASS_NAME_PATTERN;
import static com.subskribe.billy.archunit.BillyMethodPredicates.isPublicApiMethod;
import static com.subskribe.billy.archunit.BillyMethodPredicates.resourceMethods;
import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes;

import com.subskribe.billy.archunit.BillyClassPredicates;
import com.subskribe.billy.archunit.BillyMethodPredicates;
import com.subskribe.billy.shared.annotations.Beta;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Optional;
import java.util.Set;

public class ApiDocumentationRules {

    private static final Set<String> RESOURCES_SHOULD_HAVE_API_ANNOTATION_ALLOW_LIST = Set.of(
        "com.subskribe.billy.resources.DocumentMasterTemplateResource",
        "com.subskribe.billy.resources.EsignResource",
        "com.subskribe.billy.resources.GqlResource",
        "com.subskribe.billy.test.DBMigratorBase$FlywayLoadableResource"
    );

    private static final Set<String> METHODS_SHOULD_BE_DOCUMENTED_ALLOW_LIST = Set.of(
        "com.subskribe.billy.resources.DocumentLinkResource.getOrderDocument(java.lang.String)",
        "com.subskribe.billy.resources.EsignResource.getPandaDocDocument(com.subskribe.billy.auth.model.BillyAuthPrincipal, java.lang.String)",
        "com.subskribe.billy.resources.EsignResource.storePandaDocDocument(com.subskribe.billy.auth.model.BillyAuthPrincipal, java.lang.String, java.lang.String)",
        "com.subskribe.billy.resources.EsignResource.voidPandaDocDocument(com.subskribe.billy.auth.model.BillyAuthPrincipal, java.lang.String)",
        "com.subskribe.billy.resources.GqlResource.post(java.util.Optional, com.subskribe.billy.graphql.GqlRequest, javax.servlet.http.HttpServletRequest)",
        "com.subskribe.billy.resources.TenantJobResource.dispatch(com.subskribe.billy.auth.model.BillyAuthPrincipal, com.subskribe.billy.tenantjob.model.TenantJobModule, com.subskribe.billy.tenantjob.model.TenantJobType, com.subskribe.billy.tenantjob.model.TenantJobObjectModel, java.lang.String)"
    );

    @ArchTest
    public static final ArchRule METHODS_SHOULD_BE_DOCUMENTED = resourceMethods()
        .and(isPublicApiMethod())
        .and()
        .areDeclaredInClassesThat()
        .resideOutsideOfPackage("com.subskribe.billy.resources.admin")
        .and()
        .areDeclaredInClassesThat(
            new DescribedPredicate<>("not hidden api classes") {
                @Override
                public boolean test(JavaClass javaClass) {
                    Optional<Api> api = javaClass.tryGetAnnotationOfType(Api.class);
                    return api.map(value -> !value.hidden()).orElse(true);
                }
            }
        )
        .and(DescribedPredicate.not(BillyMethodPredicates.isInAllowList(METHODS_SHOULD_BE_DOCUMENTED_ALLOW_LIST)))
        .and()
        .areNotAnnotatedWith(Deprecated.class)
        .and()
        .areNotAnnotatedWith(Beta.class)
        .should()
        .beAnnotatedWith(ApiOperation.class);

    @ArchTest
    public static final ArchRule RESOURCES_SHOULD_HAVE_API_ANNOTATION = classes()
        .that()
        .haveNameMatching(RESOURCE_CLASS_NAME_PATTERN)
        .and()
        .resideInAPackage("com.subskribe..")
        .and()
        .areNotAnnotatedWith(Beta.class)
        .and()
        .resideOutsideOfPackage("com.subskribe.billy.resources.admin")
        .and(DescribedPredicate.not(BillyClassPredicates.isInAllowList(RESOURCES_SHOULD_HAVE_API_ANNOTATION_ALLOW_LIST)))
        .should()
        .beAnnotatedWith(Api.class);
}
