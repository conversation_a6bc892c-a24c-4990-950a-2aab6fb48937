package com.subskribe.billy.archunit.log;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses;

import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UseInternalLogger {

    @ArchTest
    public static final ArchRule NO_CLASS_SHOULD_USE_SLF4J = noClasses()
        .that()
        .resideOutsideOfPackage("com.subskribe.billy.shared.logger")
        .and()
        .areNotAssignableTo(UseInternalLogger.class)
        .should()
        .dependOnClassesThat()
        .areAssignableTo(Logger.class)
        .orShould()
        .dependOnClassesThat()
        .areAssignableTo(LoggerFactory.class)
        .because("We use our own logger implementation in com.subskribe.billy.shared.logger");
}
