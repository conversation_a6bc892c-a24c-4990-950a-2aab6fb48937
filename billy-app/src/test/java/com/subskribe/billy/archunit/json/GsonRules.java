package com.subskribe.billy.archunit.json;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses;

import com.subskribe.billy.archunit.BillyClassPredicates;
import com.subskribe.billy.shared.mapper.BaseMapper;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import java.util.Set;

public class GsonRules {

    private static final Set<String> GSON_USE_ALLOW_LIST = Set.of(
        "com.subskribe.billy.aws.es.ElasticSearchService",
        "com.subskribe.billy.cache.CacheService",
        "com.subskribe.billy.customfield.CustomFieldDBMapper",
        "com.subskribe.billy.dlq.service.DLQMessageHandler",
        "com.subskribe.billy.docusign.service.DocuSignAuthService",
        "com.subskribe.billy.email.db.EmailDAO",
        "com.subskribe.billy.email.services.EmailNotificationQueueMessageHandler",
        "com.subskribe.billy.graphql.GqlMutation",
        "com.subskribe.billy.hubspot.service.HubSpotClient",
        "com.subskribe.billy.hubspot.service.HubSpotInstallService",
        "com.subskribe.billy.hubspot.service.HubSpotService",
        "com.subskribe.billy.hubspot.client.HubspotClientConstants",
        "com.subskribe.billy.hubspot.client.HubspotClientFactory",
        "com.subskribe.billy.hubspot.client.HubspotIntegrationClient",
        "com.subskribe.billy.hubspot.client.HubspotApiClient",
        "com.subskribe.billy.notification.service.NotificationService",
        "com.subskribe.billy.order.db.OrderServiceDbTest",
        "com.subskribe.billy.order.services.OrderEventServiceTest",
        "com.subskribe.billy.productcatalog.mapper.PlanRecordMapper",
        "com.subskribe.billy.resources.DemoResource",
        "com.subskribe.billy.resources.PaymentResource",
        "com.subskribe.billy.payment.stripe.webhook.StripeIncomingWebhookProcessor",
        "com.subskribe.billy.resources.json.dunningsetting.DunningSettingJsonMapper",
        "com.subskribe.billy.revrec.services.RevenueRecognitionJobService",
        "com.subskribe.billy.revrec.services.RevenueRecognitionService",
        "com.subskribe.billy.salesforce.service.SalesforceArrTrendService",
        "com.subskribe.billy.salesforce.service.SalesforceBulkApiService",
        "com.subskribe.billy.salesforce.service.SalesforceContactService",
        "com.subskribe.billy.salesforce.service.SalesforceExistentialService",
        "com.subskribe.billy.salesforce.service.SalesforceGetService",
        "com.subskribe.billy.salesforce.service.SalesforceLoginService",
        "com.subskribe.billy.salesforce.service.SalesforceService",
        "com.subskribe.billy.salesforce.service.SalesforceRestService",
        "com.subskribe.billy.scheduler.queue.QuartzQueueMessageHandler",
        "com.subskribe.billy.tenant.settings.accountreceivablecontact.mapper.AccountReceivableContactDAOMapper",
        "com.subskribe.billy.resources.ApprovalFlowNotificationResource",
        "com.subskribe.billy.notification.slack.SlackNotificationService",
        "com.subskribe.billy.payment.stripe.webhook.StripeIncomingWebhookProcessorTest"
    );

    @ArchTest
    public static final ArchRule GSON_SHOULD_NOT_BE_USED = noClasses()
        .that(DescribedPredicate.not(BillyClassPredicates.isInAllowList(GSON_USE_ALLOW_LIST)))
        .and()
        .areNotAssignableTo(BaseMapper.class)
        .should()
        .dependOnClassesThat()
        .areAssignableTo("com.google.gson.Gson");
}
