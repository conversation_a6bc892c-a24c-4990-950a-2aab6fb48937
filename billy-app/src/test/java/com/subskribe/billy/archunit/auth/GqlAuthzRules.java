package com.subskribe.billy.archunit.auth;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

import com.subskribe.billy.auth.authorizers.AllowAllRoles;
import com.subskribe.billy.auth.authorizers.AllowRoles;
import com.subskribe.billy.auth.authorizers.AllowUnauthenticated;
import com.subskribe.billy.graphql.GqlAuthContext;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;

public class GqlAuthzRules {

    @ArchTest
    public static final ArchRule GQL_AUTH_CONTEXT_METHODS_SHOULD_HAVE_ALLOW_ROLE_ANNOTATIONS = methods()
        .that()
        .areDeclaredInClassesThat()
        .areAssignableTo(GqlAuthContext.class)
        .and()
        .areNotPrivate()
        .should()
        .beAnnotatedWith(AllowRoles.class)
        .orShould()
        .beAnnotatedWith(AllowUnauthenticated.class)
        .orShould()
        .beAnnotatedWith(AllowAllRoles.class);
}
