package com.subskribe.billy.archunit.limiteduse;

import static com.tngtech.archunit.core.domain.JavaAccess.Predicates.target;
import static com.tngtech.archunit.core.domain.properties.CanBeAnnotated.Predicates.annotatedWith;

import com.subskribe.billy.resources.LocalAdminResource;
import com.subskribe.billy.security.OnlyAllowAdminCallers;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition;

public class AdminOnlyCallers {

    private static final String ADMIN_RESOURCE_PACKAGE = "com.subskribe.billy.resources.admin";

    @ArchTest
    public static final ArchRule INVOICE_MODULE_LIMITED_USE = ArchRuleDefinition.noClasses()
        .that(
            DescribedPredicate.not(JavaClass.Predicates.resideInAPackage(ADMIN_RESOURCE_PACKAGE)).and(
                // Moving LocalAdminResource to the admin package breaks other rules, so exclude it here
                DescribedPredicate.not(JavaClass.Predicates.assignableTo(LocalAdminResource.class))
            )
        )
        .should()
        .callMethodWhere(target(annotatedWith(OnlyAllowAdminCallers.class)));
}
