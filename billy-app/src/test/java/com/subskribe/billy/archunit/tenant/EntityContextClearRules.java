package com.subskribe.billy.archunit.tenant;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes;

import com.subskribe.billy.shared.tenant.TenantContextInjector;
import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaCall;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;

@SuppressWarnings("unused")
public class EntityContextClearRules {

    @ArchTest
    public static final ArchRule CALLS_TO_SET_ENTITY_CONTEXT_SHOULD_ALSO_CLEAN_THEM = classes()
        .that(
            new DescribedPredicate<>("call TenantContextInjector#setupEntityContext") {
                @Override
                public boolean test(JavaClass javaClass) {
                    for (JavaCall<?> javaCall : javaClass.getCodeUnitCallsFromSelf()) {
                        if (
                            javaCall.getTargetOwner().getName().equals(TenantContextInjector.class.getName()) &&
                            "setupEntityContext".equals(javaCall.getTarget().getName())
                        ) {
                            return true;
                        }
                    }
                    return false;
                }
            }
        )
        .should()
        .callMethod(TenantContextInjector.class, "cleanUpEntityContext");
}
