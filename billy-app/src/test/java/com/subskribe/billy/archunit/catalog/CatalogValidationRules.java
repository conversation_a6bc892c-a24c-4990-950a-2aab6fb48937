package com.subskribe.billy.archunit.catalog;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaMethod;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;

public class CatalogValidationRules {

    @ArchTest
    public static final ArchRule CHARGE_VALIDATE_ONLY_CALLED_BY_VALIDATE_CHARGE = methods()
        .that(isInternalChargeValidate())
        .should()
        .onlyBeCalled()
        .byMethodsThat(isChargeValidationMethod());

    public static DescribedPredicate<JavaMethod> isInternalChargeValidate() {
        return new DescribedPredicate<>("is charge validate") {
            @Override
            public boolean test(JavaMethod input) {
                return "com.subskribe.billy.productcatalog.model.Charge.validate()".equals(input.getFullName());
            }
        };
    }

    public static DescribedPredicate<JavaMethod> isChargeValidationMethod() {
        return new DescribedPredicate<>("is charge validate") {
            @Override
            public boolean test(JavaMethod input) {
                return "com.subskribe.billy.productcatalog.validation.ChargeValidation.validateChargeInternal(com.subskribe.billy.productcatalog.model.Charge)".equals(
                        input.getFullName()
                    );
            }
        };
    }
}
