package com.subskribe.billy.erp.netsuite.mappers;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.NumericNode;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldSource;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.invoice.EmailNotifierContact;
import com.subskribe.billy.graphql.invoice.EmailNotifiersDetail;
import com.subskribe.billy.invoice.model.EmailNotifiersList;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.contactfetcher.ContactType;
import com.subskribe.billy.shared.mapper.CustomFieldAPIMapper;
import com.subskribe.billy.test.BillyTestBase;
import com.subskribe.billy.test.utilities.MockChargeBuilder;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Currency;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

public class EducationPerfectNetsuitePayloadMapperTest extends BillyTestBase {

    private static final String CHARGE_ID_1 = "CHRG-1";
    private static final String ACCOUNT_ID = "ACCT-1";
    private static final String BILLING_CONTACT_ID = "CONT-1";
    private static final String CUSTOMER_ERP_ID = "511";
    private static final String SUBSIDIARY_ID = "SUB-1";
    private static final String ACCOUNT_ADDRESS_ID = "ADDR-1";
    private static final String LINE_ITEM_ID = "LINE_ITEM_1";
    private static final ZoneId ZONE_ID = ZoneId.of("America/Los_Angeles");
    private static final EmailContact EMAIL_CONTACT_1 = new EmailContact("EM-1", ContactType.USER, "<EMAIL>", "name", List.of("*"));
    private static final EmailNotifiersList EMAIL_NOTIFIERS_LIST = new EmailNotifiersList(
        List.of(EMAIL_CONTACT_1.getContactId()),
        List.of(),
        List.of()
    );

    private ProductCatalogGetService productCatalogGetService;
    private OrderGetService orderGetService;
    private EducationPerfectNetsuitePayloadMapper payloadMapper;
    private CustomFieldAPIMapper customFieldMapper;

    @BeforeEach
    public void setup() {
        productCatalogGetService = mock(ProductCatalogGetService.class);
        AccountGetService accountGetService = mock(AccountGetService.class);
        EntityGetService entityGetService = mock(EntityGetService.class);
        EmailContactListService emailContactlistService = mock(EmailContactListService.class);
        orderGetService = mock(OrderGetService.class);
        RateCardService rateCardService = mock(RateCardService.class);
        CustomFieldService customFieldService = mock(CustomFieldService.class);
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        customFieldMapper = Mappers.getMapper(CustomFieldAPIMapper.class);

        when(productCatalogGetService.getChargeByChargeId(CHARGE_ID_1)).thenReturn(chargeBuilder().build());
        when(emailContactlistService.getEmailNotifiersDetail(List.of(EMAIL_CONTACT_1.getContactId()), List.of(), List.of())).thenReturn(
            emailNotifiersDetail()
        );
        when(accountGetService.getAccountAddress(ACCOUNT_ADDRESS_ID)).thenReturn(Optional.of(address()));
        when(billyConfiguration.getSiteUrl()).thenReturn("https://test.subskribe.com");

        when(orderGetService.getOrderLineItemByOrderLineItemId(LINE_ITEM_ID)).thenReturn(Optional.of(lineItem()));

        payloadMapper = new EducationPerfectNetsuitePayloadMapper(
            productCatalogGetService,
            accountGetService,
            entityGetService,
            emailContactlistService,
            orderGetService,
            rateCardService,
            customFieldService
            billyConfiguration
        );
    }

    @Test
    public void testBuildInvoiceRequest_noInvoiceItemChargeErpId() {
        Invoice invoice = invoiceBuilder().createInvoice();

        when(productCatalogGetService.getChargeByChargeId(CHARGE_ID_1)).thenReturn(chargeBuilder().withErpId(null).build());

        assertThatThrownBy(() -> payloadMapper.buildInvoiceRequest(invoice, CUSTOMER_ERP_ID, SUBSIDIARY_ID, ZONE_ID))
            .isInstanceOf(ConflictingStateException.class)
            .hasMessage(String.format("charge %s does not have an erp id", CHARGE_ID_1));
    }

    @Test
    public void testBuildInvoiceRequest_matchesSnapshot() throws IOException {
        Invoice invoice = invoiceBuilder().createInvoice();

        JsonNode invoiceRequest = payloadMapper.buildInvoiceRequest(invoice, CUSTOMER_ERP_ID, SUBSIDIARY_ID, ZONE_ID);
        JsonNode expectedJson = asJson(getClass(), "epInvoiceRequest.json");
        assertThat(expectedJson.equals(this::compare, invoiceRequest)).isTrue();
    }

    @Test
    public void testBuildCustomerRequest_matchesSnapshot() throws IOException {
        Account account = account();
        AccountContact billingContact = account.getContacts().get(0);

        JsonNode customerRequest = payloadMapper.buildCustomerRequest(account, billingContact, SUBSIDIARY_ID);
        JsonNode expectedJson = asJson(getClass(), "epCustomerRequest.json");
        assertThat(expectedJson.equals(this::compare, customerRequest)).isTrue();
    }

    @Test
    public void testYearsCustomField() {
        OrderLineItem lineItem = lineItem();
        lineItem.setCustomFields(customFieldMapper.toCustomFieldEntries(customFields(List.of("7", "8"))));
        when(orderGetService.getOrderLineItemByOrderLineItemId(LINE_ITEM_ID)).thenReturn(Optional.of(lineItem));

        Invoice invoice = invoiceBuilder().createInvoice();
        JsonNode invoiceJson = payloadMapper.buildInvoiceRequest(invoice, CUSTOMER_ERP_ID, SUBSIDIARY_ID, ZONE_ID);

        String description = invoiceJson
            .get(NetsuiteMapperConstants.ITEM)
            .get(NetsuiteMapperConstants.ITEMS)
            .get(0)
            .get(NetsuiteMapperConstants.DESCRIPTION)
            .asText();
        assertThat(description).isEqualTo("Years 7,8");
    }

    @Test
    public void testYearsCustomField_noYears() {
        OrderLineItem lineItem = lineItem();
        lineItem.setCustomFields((customFieldMapper.toCustomFieldEntries(customFields(List.of()))));
        when(orderGetService.getOrderLineItemByOrderLineItemId(LINE_ITEM_ID)).thenReturn(Optional.of(lineItem));

        Invoice invoice = invoiceBuilder().createInvoice();
        JsonNode invoiceJson = payloadMapper.buildInvoiceRequest(invoice, CUSTOMER_ERP_ID, SUBSIDIARY_ID, ZONE_ID);

        String description = invoiceJson
            .get(NetsuiteMapperConstants.ITEM)
            .get(NetsuiteMapperConstants.ITEMS)
            .get(0)
            .get(NetsuiteMapperConstants.DESCRIPTION)
            .asText();
        assertThat(description).isEqualTo("");
    }

    private static EmailNotifiersDetail emailNotifiersDetail() {
        var emailNotifiersDetail = new EmailNotifiersDetail();
        EmailNotifierContact emailNotifierContact = new EmailNotifierContact();
        emailNotifierContact.setId(EMAIL_CONTACT_1.getContactId());
        emailNotifierContact.setEmail(EMAIL_CONTACT_1.getEmail());
        emailNotifiersDetail.setToContacts(List.of(emailNotifierContact));
        return emailNotifiersDetail;
    }

    private static MockChargeBuilder chargeBuilder() {
        return new MockChargeBuilder().withChargeId(CHARGE_ID_1).withAmount(new BigDecimal(100)).withErpId("ERP-1");
    }

    private static InvoiceItem.InvoiceItemBuilder invoiceItemBuilder() {
        return new InvoiceItem.InvoiceItemBuilder()
            .chargeId(CHARGE_ID_1)
            .amount(new BigDecimal("1000"))
            .quantity(10L)
            .orderLineItemId(LINE_ITEM_ID)
            .periodStartDate(Instant.parse("2024-07-29T07:00:00Z"))
            .periodEndDate(Instant.parse("2024-08-29T07:00:00Z"));
    }

    private static Account account() {
        Account account = new Account();
        account.setAccountId(ACCOUNT_ID);
        account.setErpId(CUSTOMER_ERP_ID);
        account.setAddressId(ACCOUNT_ADDRESS_ID);
        account.setName("Test Account");
        account.setPhoneNumber("**********");
        account.setCurrency(Currency.getInstance("USD"));
        account.setContacts(List.of(billingContact()));
        return account;
    }

    private static AccountAddress address() {
        AccountAddress accountAddress = new AccountAddress();
        accountAddress.setAddressId(ACCOUNT_ADDRESS_ID);
        accountAddress.setStreetAddressLine1("123 Main St");
        accountAddress.setStreetAddressLine2("Suite 100");
        accountAddress.setCity("San Francisco");
        accountAddress.setState("CA");
        accountAddress.setZipcode("94107");
        return accountAddress;
    }

    private static AccountContact billingContact() {
        AccountContact accountContact = new AccountContact();
        accountContact.setAccountId(ACCOUNT_ID);
        accountContact.setContactId(BILLING_CONTACT_ID);
        accountContact.setEmail("<EMAIL>");
        return accountContact;
    }

    private static CustomField customFields(List<String> selections) {
        List<CustomFieldEntry> entries = List.of(
            new CustomFieldEntry(
                "CUST-1",
                CustomFieldType.MULTISELECT_PICKLIST,
                "years",
                "Years",
                "7,8",
                selections,
                List.of("7", "8", "9"),
                false,
                CustomFieldSource.USER,
                null
            )
        );
        return CustomField.fromEntries(entries);
    }

    private OrderLineItem lineItem() {
        OrderLineItem lineItem = new OrderLineItem();
        lineItem.setOrderLineId(LINE_ITEM_ID);
        lineItem.setCustomFields(customFieldMapper.toCustomFieldEntries(customFields(List.of())));
        return lineItem;
    }

    private static Invoice.InvoiceBuilder invoiceBuilder() {
        InvoiceItem invoiceItem = invoiceItemBuilder().createInvoiceItem();
        List<InvoiceItem> invoiceItems = List.of(invoiceItem);
        AccountContact billingContact = billingContact();
        return new Invoice.InvoiceBuilder()
            .invoiceNumber(new Invoice.Number("INV-1"))
            .subscriptionId(SUBSIDIARY_ID)
            .currency("USD")
            .status(InvoiceStatus.POSTED)
            .invoiceDate(Instant.parse("2024-07-29T07:00:00Z"))
            .dueDate(Instant.parse("2024-08-29T07:00:00Z"))
            .paymentTerm(PaymentTerm.NET45)
            .invoiceItems(invoiceItems)
            .subTotal(BigDecimal.valueOf(1000.00))
            .total(BigDecimal.valueOf(1000.00))
            .totalDiscount(BigDecimal.valueOf(100.00))
            .billingContact(billingContact)
            .emailNotifiersList(EMAIL_NOTIFIERS_LIST)
            .erpId(null);
    }

    // custom comparator because numbers can turn out differently when serialized
    public int compare(JsonNode o1, JsonNode o2) {
        if (o1.equals(o2)) {
            return 0;
        }
        if ((o1 instanceof NumericNode) && (o2 instanceof NumericNode)) {
            Double d1 = o1.asDouble();
            Double d2 = o2.asDouble();
            if (d1.compareTo(d2) == 0) {
                return 0;
            }
        }
        return 1;
    }
}
