package com.subskribe.billy.erp.netsuite.mappers;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.subskribe.billy.exception.ConflictingStateException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class DefaultNetsuitePayloadMapperTest {

    private DefaultNetsuitePayloadMapper defaultMapper;

    @BeforeEach
    public void setup() {
        defaultMapper = new DefaultNetsuitePayloadMapper();
    }

    @Test
    public void testBuildInvoiceRequest_Fails() {
        assertThatThrownBy(() -> defaultMapper.buildInvoiceRequest(null, null, null, null))
            .isInstanceOf(ConflictingStateException.class)
            .hasMessage("default netsuite payload mapper not implemented");
    }

    @Test
    public void testBuildCreditMemoRequest_Fails() {
        assertThatThrownBy(() -> defaultMapper.buildCreditMemoRequest(null, null, null))
            .isInstanceOf(ConflictingStateException.class)
            .hasMessage("default netsuite payload mapper not implemented");
    }

    @Test
    public void testBuildCustomerRequest_Fails() {
        assertThatThrownBy(() -> defaultMapper.buildCustomerRequest(null, null, null))
            .isInstanceOf(ConflictingStateException.class)
            .hasMessage("default netsuite payload mapper not implemented");
    }
}
