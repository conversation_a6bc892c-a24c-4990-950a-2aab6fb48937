package com.subskribe.billy.account.services;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.db.AccountAddressDAO;
import com.subskribe.billy.account.db.AccountContactDAO;
import com.subskribe.billy.account.db.AccountDAO;
import com.subskribe.billy.account.db.AccountPaymentMethodDAO;
import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityContextProviderFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.invoice.service.AccountInvoiceService;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoRetrievalService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodService;
import com.subskribe.billy.payment.services.PaymentConfigurationService;
import com.subskribe.billy.salesforce.service.SalesforceIntegrationService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.user.service.UserService;
import java.util.UUID;

public class MockAccountServiceBuilder {

    private AccountContactDAO accountContactDAO;
    private final AccountAddressDAO accountAddressDAO;
    private final AccountPaymentMethodDAO accountPaymentMethodDAO;
    private final OrderGetService orderGetService;
    private final CreditMemoRetrievalService creditMemoRetrievalService;
    private DSLContextProvider dslContextProvider;
    private TenantIdProvider tenantIdProvider;
    private final EntityGetService entityGetService = EntityGetServiceFixture.entityGetServiceFixture();
    private final TaxService taxService;
    private TenantSettingService tenantSettingService;
    private final ContactIdGenerator contactIdGenerator;
    private final SubscriptionGetService subscriptionGetService;
    private final AccountGetService accountGetService;
    private PaymentConfigurationService paymentConfigurationService;
    private final AccountInvoiceService accountInvoiceService;
    private final AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService;
    private final AccountCacheInvalidator accountCacheInvalidator;
    private EntityContextResolver entityContextResolver;
    private final EntityContextProvider entityContextProvider;
    private final AccountEventService accountEventService;

    public MockAccountServiceBuilder() {
        accountContactDAO = mock(AccountContactDAO.class);
        accountContactDAO = mock(AccountContactDAO.class);
        accountAddressDAO = mock(AccountAddressDAO.class);
        accountPaymentMethodDAO = mock(AccountPaymentMethodDAO.class);
        orderGetService = mock(OrderGetService.class);
        creditMemoRetrievalService = mock(CreditMemoRetrievalService.class);
        dslContextProvider = mock(DSLContextProvider.class);
        tenantIdProvider = mock(TenantIdProvider.class);
        taxService = mock(TaxService.class);
        tenantSettingService = mock(TenantSettingService.class);
        contactIdGenerator = mock(ContactIdGenerator.class);
        subscriptionGetService = mock(SubscriptionGetService.class);
        accountGetService = mock(AccountGetService.class);
        paymentConfigurationService = mock(PaymentConfigurationService.class);
        accountInvoiceService = mock(AccountInvoiceService.class);
        accountAutomaticPaymentMethodService = mock(AccountAutomaticPaymentMethodService.class);
        accountCacheInvalidator = mock(AccountCacheInvalidator.class);
        entityContextResolver = mock(EntityContextResolver.class);
        accountEventService = mock(AccountEventService.class);
        entityContextProvider = EntityContextProviderFixture.buildAllEntitiesContext();
    }

    public MockAccountServiceBuilder withTenantSettingService(TenantSettingService tenantSettingService) {
        this.tenantSettingService = tenantSettingService;
        return this;
    }

    public MockAccountServiceBuilder withDSLContextProvider(DSLContextProvider dslContextProvider) {
        this.dslContextProvider = dslContextProvider;
        return this;
    }

    public MockAccountServiceBuilder withTenantIdProvider(TenantIdProvider tenantIdProvider) {
        this.tenantIdProvider = tenantIdProvider;
        return this;
    }

    public MockAccountServiceBuilder withPaymentConfigurationService(PaymentConfigurationService paymentConfigurationService) {
        this.paymentConfigurationService = paymentConfigurationService;
        return this;
    }

    public MockAccountServiceBuilder withEntityContextResolver(EntityContextResolver entityContextResolver) {
        this.entityContextResolver = entityContextResolver;
        return this;
    }

    public AccountService build() {
        var accountDAO = new AccountDAO(tenantIdProvider, dslContextProvider);

        var accountIdGenerator = new AccountIdGenerator(accountDAO);
        var accountPaymentMethod = new AccountPaymentMethod();
        accountPaymentMethod.setId(UUID.randomUUID());
        when(accountPaymentMethodDAO.addPaymentMethod(any(), any())).thenReturn(accountPaymentMethod);

        return spy(
            new AccountService(
                accountDAO,
                accountContactDAO,
                accountAddressDAO,
                orderGetService,
                creditMemoRetrievalService,
                dslContextProvider,
                tenantIdProvider,
                entityGetService,
                taxService,
                tenantSettingService,
                accountIdGenerator,
                contactIdGenerator,
                subscriptionGetService,
                accountGetService,
                paymentConfigurationService,
                accountInvoiceService,
                mock(HubSpotIntegrationService.class),
                mock(SalesforceIntegrationService.class),
                mock(TenantJobDispatcherService.class),
                accountAutomaticPaymentMethodService,
                accountCacheInvalidator,
                mock(FeatureService.class),
                mock(UserService.class),
                entityContextResolver,
                entityContextProvider,
                accountEventService
            )
        );
    }
}
