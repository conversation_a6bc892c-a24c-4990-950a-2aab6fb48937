package com.subskribe.billy.account.services;

import static com.subskribe.billy.payment.model.PaymentConfiguration.DEFAULT_SUPPORTED_PAYMENT_TYPES;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.db.AccountAddressDAO;
import com.subskribe.billy.account.db.AccountContactDAO;
import com.subskribe.billy.account.db.AccountDAO;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityContextProviderFixture;
import com.subskribe.billy.entity.fixtures.EntityContextResolverFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.DuplicateAccountException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.fixtures.AccountData;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.invoice.service.AccountInvoiceService;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.invoicesettlement.services.CreditMemoRetrievalService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.payment.model.PaymentConfiguration;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodService;
import com.subskribe.billy.payment.services.PaymentConfigurationService;
import com.subskribe.billy.salesforce.service.SalesforceIntegrationService;
import com.subskribe.billy.shared.enums.TaxExemptionUseCode;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.service.UserService;
import java.util.Currency;
import java.util.Optional;
import org.elasticsearch.common.collect.List;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class AccountServiceTest {

    private static final TenantId TENANT_ID = new TenantId("test_tenant_id");

    @Mock
    private AccountDAO accountDAO;

    @Mock
    private AccountContactDAO accountContactDAO;

    @Mock
    private AccountAddressDAO accountAddressDAO;

    @Mock
    private OrderGetService orderGetService;

    @Mock
    private CreditMemoRetrievalService creditMemoRetrievalService;

    @Mock
    private DSLContextProvider dslContextProvider;

    @Mock
    private TenantIdProvider tenantIdProvider;

    private final EntityGetService entityGetService = EntityGetServiceFixture.entityGetServiceFixture();

    @Mock
    private TaxService taxService;

    @Mock
    private TenantSettingService tenantSettingService;

    @Mock
    private AccountIdGenerator accountIdGenerator;

    @Mock
    private ContactIdGenerator contactIdGenerator;

    @Mock
    private SubscriptionGetService subscriptionGetService;

    @Mock
    private AccountGetService accountGetService;

    @Mock
    private PaymentConfigurationService paymentConfigurationService;

    @Mock
    private AccountEventService accountEventService;

    private AccountService accountService;

    private Account existingAccount;

    private Account newAccount;

    private AccountContact contact;

    @Mock
    private TenantSetting tenantSetting;

    @Mock
    private DSLContext dslContext;

    @Mock
    private AccountInvoiceService accountInvoiceService;

    @Mock
    private AccountAutomaticPaymentMethodService accountAutomaticPaymentMethodService;

    @Mock
    private AccountCacheInvalidator accountCacheInvalidator;

    @Mock
    private FeatureService featureService;

    @Mock
    private UserService userService;

    private final EntityContextResolver entityContextResolver = EntityContextResolverFixture.usingConstructor();

    private final EntityContextProvider entityContextProvider = EntityContextProviderFixture.buildAllEntitiesContext();

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        existingAccount = AccountData.createAccount("ACCT-1111111", "USER-1111111", "ADDR-1111111");
        newAccount = AccountData.createAccount("ACCT-1111111", "USER-1111111", "ADDR-1111111");
        contact = AccountData.createAccountUser("CONT-1111111", existingAccount.getAccountId(), existingAccount.getAddressId());
        existingAccount.setContacts(List.of(contact));

        when(tenantSettingService.getTenantSettingInternal()).thenReturn(tenantSetting);

        when(tenantSetting.getSupportedCurrencies()).thenReturn(List.of("USD", "EUR"));
        when(accountDAO.getAccount(newAccount.getAccountId())).thenReturn(Optional.of(existingAccount));
        when(accountDAO.getAccountByName(newAccount.getName())).thenReturn(Optional.empty());
        when(accountGetService.isAccountInUse(existingAccount.getAccountId())).thenReturn(false);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID.getRequiredId());
        when(dslContextProvider.get(TENANT_ID.getRequiredId())).thenReturn(dslContext);
        when(accountInvoiceService.getDraftInvoicesCountForAccount(newAccount.getAccountId())).thenReturn(0);
        when(paymentConfigurationService.getTenantPaymentConfiguration()).thenReturn(new PaymentConfiguration(DEFAULT_SUPPORTED_PAYMENT_TYPES));
        when(featureService.isEnabled(Feature.NETSUITE_INVOICE_SYNC)).thenReturn(false);
        when(userService.getCurrentUser()).thenReturn(Optional.empty());

        accountService = new AccountService(
            accountDAO,
            accountContactDAO,
            accountAddressDAO,
            orderGetService,
            creditMemoRetrievalService,
            dslContextProvider,
            tenantIdProvider,
            entityGetService,
            taxService,
            tenantSettingService,
            accountIdGenerator,
            contactIdGenerator,
            subscriptionGetService,
            accountGetService,
            paymentConfigurationService,
            accountInvoiceService,
            mock(HubSpotIntegrationService.class),
            mock(SalesforceIntegrationService.class),
            mock(TenantJobDispatcherService.class),
            accountAutomaticPaymentMethodService,
            accountCacheInvalidator,
            featureService,
            userService,
            entityContextResolver,
            entityContextProvider,
            accountEventService
        );
    }

    @Test
    public void testAccountUpdateCurrencyNotSupportedByTenant() {
        newAccount.setCurrency(Currency.getInstance("GBP"));

        assertThatThrownBy(() -> accountService.updateAccount(newAccount))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("currency is not supported");
    }

    @Test
    public void testAccountIdIsNull() {
        newAccount.setAccountId(null);

        assertThatThrownBy(() -> accountService.updateAccount(newAccount))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("account id is required");
    }

    @Test
    public void testExistingAccountIsNull() {
        when(accountDAO.getAccount(newAccount.getAccountId())).thenReturn(Optional.empty());

        assertThatThrownBy(() -> accountService.updateAccount(newAccount))
            .isInstanceOf(ObjectNotFoundException.class)
            .hasMessageContaining("does not exist");
    }

    @Test
    public void testIfNewNameExists_thenThrowError() {
        newAccount.setName("New Account Name");
        when(accountDAO.getAccountByName(newAccount.getName())).thenReturn(Optional.of(new Account()));

        assertThatThrownBy(() -> accountService.updateAccount(newAccount))
            .isInstanceOf(DuplicateAccountException.class)
            .hasMessageContaining("already exists");
    }

    @Test
    public void testIfCurrencyOfUsedAccountIsUpdate_thenThrowError() {
        newAccount.setCurrency(Currency.getInstance("EUR"));
        when(accountGetService.isAccountInUse(existingAccount.getAccountId())).thenReturn(true);

        assertThatThrownBy(() -> accountService.updateAccount(newAccount))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("currency cannot be updated");
    }

    @Test
    public void testIfAccountInUseIsChangedToReseller_thenSucceed() {
        existingAccount.setIsReseller(false);
        newAccount.setIsReseller(true);
        when(accountGetService.isAccountInUse(existingAccount.getAccountId())).thenReturn(true);

        Assertions.assertDoesNotThrow(() -> accountService.upsertAccount(newAccount));
    }

    @Test
    public void testIfContactInUseAndChangedToNonReseller_thenFail() {
        existingAccount.setIsReseller(true);
        newAccount.setIsReseller(false);
        when(accountGetService.isAccountInUse(existingAccount.getAccountId())).thenReturn(false);
        when(accountGetService.isContactInUse(contact.getContactId())).thenReturn(true);

        assertThatThrownBy(() -> accountService.updateAccount(newAccount))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("reseller");
    }

    @Test
    public void testAccountInUseButNoConflict() {
        existingAccount.setIsReseller(true);
        newAccount.setIsReseller(true);
        when(accountGetService.isAccountInUse(existingAccount.getAccountId())).thenReturn(true);

        Assertions.assertDoesNotThrow(() -> accountService.updateAccount(newAccount));
    }

    @Test
    public void testUpdateCrmIdWithExistingIdDoesNotThrow() {
        existingAccount.setCrmId("*************");
        newAccount.setCrmId("000*************");

        Assertions.assertDoesNotThrow(() -> accountService.updateAccount(newAccount));
    }

    @Test
    public void testAddCrmIdInUpdateSucceeds() {
        existingAccount.setCrmId(null);
        newAccount.setCrmId("000*************");

        Assertions.assertDoesNotThrow(() -> accountService.updateAccount(newAccount));
    }

    @Test
    public void testUpdateCrmIdWithAdminRoleSucceeds() {
        existingAccount.setCrmId("*************");
        newAccount.setCrmId("*************");
        setupAdminUser();

        Assertions.assertDoesNotThrow(() -> accountService.updateAccount(newAccount));
    }

    @Test
    public void testIfAccountMutationWithDraftInvoicesPending_thenThrows() {
        newAccount.setTaxExemptionUseCode(TaxExemptionUseCode.E);
        when(accountGetService.isAccountInUse(existingAccount.getAccountId())).thenReturn(true);
        when(accountInvoiceService.getDraftInvoicesCountForAccount(newAccount.getAccountId())).thenReturn(1);

        assertThatThrownBy(() -> accountService.upsertAccount(newAccount))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("DRAFT Invoice(s) pending");
    }

    @Test
    public void testIfAccountMutationWithDraftInvoicesNotPending_thenUpdateSucceeds() {
        newAccount.setTaxExemptionUseCode(TaxExemptionUseCode.E);
        when(accountGetService.isAccountInUse(existingAccount.getAccountId())).thenReturn(true);
        when(accountInvoiceService.getDraftInvoicesCountForAccount(newAccount.getAccountId())).thenReturn(0);

        Assertions.assertDoesNotThrow(() -> accountService.upsertAccount(newAccount));
    }

    private void setupAdminUser() {
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setTenantId("test_tenant_id");
        user.setUserId("USR-12345");
        user.setRole(Role.ADMIN);
        when(userService.getCurrentUser()).thenReturn(Optional.of(user));
    }
}
