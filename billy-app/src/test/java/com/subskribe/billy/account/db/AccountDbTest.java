package com.subskribe.billy.account.db;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.WithDb;
import java.io.IOException;
import java.util.Currency;
import java.util.Set;
import org.apache.commons.lang3.RandomStringUtils;
import org.jooq.DSLContext;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class AccountDbTest extends WithDb {

    private static final String TENANT_ID = "27b59431-0cc7-4c87-883b-be345e9bd525";

    private static final String ACCOUNT_NAME = RandomStringUtils.randomAlphanumeric(10);

    @Mock
    private TenantIdProvider tenantIdProvider;

    private AccountDAO accountDAO;
    private DSLContext tenantDSLContext;

    @BeforeAll
    public void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        accountDAO = new AccountDAO(tenantIdProvider, dslContextProvider);
        tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(TENANT_ID, dslContextProvider);
    }

    @Test
    @Order(1)
    public void addAccountToDb() {
        Account account = getAccount(ACCOUNT_NAME);
        Account savedAccount = tenantDSLContext.transactionResult(configuration -> accountDAO.addAccount(account, configuration));

        assertEquals(ACCOUNT_NAME, savedAccount.getName());
    }

    @Test
    @Order(2)
    public void addAccountWithDuplicateName() {
        Account account = getAccount(ACCOUNT_NAME);
        assertThrows(IllegalStateException.class, () ->
            tenantDSLContext.transactionResult(configuration -> accountDAO.addAccount(account, configuration))
        );
    }

    @Test
    @Order(3)
    public void updateAccountWithDuplicateName() {
        String newAccountName = RandomStringUtils.randomAlphanumeric(10);
        Account account = getAccount(newAccountName);
        Account savedAccount = tenantDSLContext.transactionResult(configuration -> accountDAO.addAccount(account, configuration));

        assertEquals(newAccountName, savedAccount.getName());

        savedAccount.setName(ACCOUNT_NAME);
        assertThrows(IllegalStateException.class, () -> accountDAO.updateAccount(dslContextProvider.get(TENANT_ID).configuration(), savedAccount));
    }

    private Account getAccount(String accountName) {
        Account account = new Account();
        String accountId = "ACCT-" + RandomStringUtils.randomAlphanumeric(6);
        account.setAccountId(accountId);
        account.setEntityIds(Set.of("ENTITY-1"));
        account.setName(accountName);
        account.setCurrency(Currency.getInstance("USD"));
        return account;
    }
}
