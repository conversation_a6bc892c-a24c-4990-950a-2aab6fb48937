package com.subskribe.billy.account.fixture;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;

public class AccountGetServiceFixture {

    public static AccountGetService getDefault() {
        AccountGetService mockAccountGetService = mock(AccountGetService.class);
        Account account = AccountFixture.getDefaultAccount();
        AccountContact accountContact = AccountFixture.getDefaultContact();
        when(mockAccountGetService.getAccount(AccountFixture.ACCOUNT_1_ID)).thenReturn(account);
        when(mockAccountGetService.getContact(AccountFixture.CONTACT_1_ID)).thenReturn(accountContact);
        return mockAccountGetService;
    }
}
