package com.subskribe.billy.account.fixture;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import java.util.Currency;

public class AccountFixture {

    public static final String ACCOUNT_1_ID = "ACCT-1234567";
    public static final String ACCOUNT_1_NAME = "First Account";
    public static final String CONTACT_1_ID = "CONT-1234567";
    public static final String CONTACT_1_FIRST_NAME = "First";
    public static final String CONTACT_1_LAST_NAME = "Contact";

    public static Account getDefaultAccount() {
        return getAccount(ACCOUNT_1_ID, ACCOUNT_1_NAME);
    }

    public static Account getAccount(String accountId, String accountName) {
        return getAccount(accountId, accountName, "USD");
    }

    public static Account getAccount(String accountId, String accountName, String currencyCode) {
        var account = new Account();
        account.setAccountId(accountId);
        account.setName(accountName);
        account.setCurrency(Currency.getInstance(currencyCode));
        return account;
    }

    public static AccountContact getDefaultContact() {
        var contact = new AccountContact();
        contact.setContactId(CONTACT_1_ID);
        contact.setFirstName(CONTACT_1_FIRST_NAME);
        contact.setFirstName(CONTACT_1_LAST_NAME);
        contact.setAddress(new AccountAddress());
        contact.setAccountId(ACCOUNT_1_ID);
        return contact;
    }
}
