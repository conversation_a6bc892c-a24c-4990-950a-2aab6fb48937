package com.subskribe.billy.account.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCollection;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.db.AccountAddressDAO;
import com.subskribe.billy.account.db.AccountContactDAO;
import com.subskribe.billy.account.db.AccountDAO;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.fixtures.AccountData;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.payment.services.AccountAutomaticPaymentMethodGetService;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.jooq.DSLContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class AccountGetServiceTest {

    private static final TenantId TENANT_ID = new TenantId("test_tenant_id");

    @Mock
    private AccountDAO accountDAO;

    @Mock
    private AccountContactDAO accountContactDAO;

    @Mock
    private AccountAddressDAO accountAddressDAO;

    @Mock
    private OrderGetService orderGetService;

    @Mock
    CustomFieldService customFieldService;

    @Mock
    AccountAutomaticPaymentMethodGetService accountAutomaticPaymentMethodGetService;

    @Mock
    CacheService cacheService;

    @Mock
    TenantIdProvider tenantIdProvider;

    @Mock
    EntityContextResolver entityContextResolver;

    @Mock
    FeatureService featureService;

    @Mock
    private TenantSetting tenantSetting;

    @Mock
    private TenantSettingService tenantSettingService;

    @Mock
    private DSLContextProvider dslContextProvider;

    @Mock
    private DSLContext dslContext;

    private AccountGetService accountGetServiceSpy;

    private Account existingAccount;

    private AccountContact contact1;
    private AccountContact contact2;
    private AccountContact contact3;
    private AccountContact contact4;

    private AccountAddress address1;
    private AccountAddress address2;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        existingAccount = AccountData.createAccount("ACCT-1111111", "USER-1111111", "ADDR-1111110");
        address1 = AccountData.createAccountAddress("ADDR-1111111");
        address2 = AccountData.createAccountAddress("ADDR-1111112");

        contact1 = AccountData.createAccountUser("CONT-1111111", existingAccount.getAccountId(), address1.getAddressId());
        contact2 = AccountData.createAccountUser("CONT-1111112", existingAccount.getAccountId(), address2.getAddressId());
        contact3 = AccountData.createAccountUser("CONT-1111113", existingAccount.getAccountId(), address1.getAddressId());
        contact4 = AccountData.createAccountUser("CONT-1111114", existingAccount.getAccountId(), null);

        existingAccount.setContacts(List.of(contact1, contact2, contact3, contact4));

        when(tenantSettingService.getTenantSettingInternal()).thenReturn(tenantSetting);
        when(tenantSetting.getSupportedCurrencies()).thenReturn(List.of("USD", "EUR"));
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID.getRequiredId());
        when(dslContextProvider.get(TENANT_ID.getRequiredId())).thenReturn(dslContext);
        when(featureService.isEnabled(Feature.NETSUITE_INVOICE_SYNC)).thenReturn(false);

        when(accountContactDAO.getContactsInAccount(existingAccount.getAccountId())).thenReturn(List.of(contact1, contact2, contact3, contact4));

        when(accountAddressDAO.getAddresses(Set.of(address1.getAddressId(), address2.getAddressId()))).thenReturn(List.of(address1, address2));

        AccountGetService accountGetService = new AccountGetService(
            accountDAO,
            accountContactDAO,
            accountAddressDAO,
            orderGetService,
            customFieldService,
            accountAutomaticPaymentMethodGetService,
            cacheService,
            tenantIdProvider,
            entityContextResolver
        );

        accountGetServiceSpy = spy(accountGetService);
    }

    @Test
    public void testGetAccountContactsReturnsAllContactsWithAddresses() {
        doReturn(existingAccount).when(accountGetServiceSpy).getAccount(existingAccount.getAccountId());

        List<AccountContact> accountContacts = accountGetServiceSpy.getAccountContacts(existingAccount.getAccountId(), true);

        assertThatCollection(accountContacts).containsAll(existingAccount.getContacts());

        accountContacts.forEach(contact -> {
            if (Objects.equals(contact, contact1)) {
                assertThat(contact.getAddress()).isEqualTo(address1);
            }
            if (Objects.equals(contact, contact2)) {
                assertThat(contact.getAddress()).isEqualTo(address2);
            }
            if (Objects.equals(contact, contact3)) {
                assertThat(contact.getAddress()).isEqualTo(address1);
            }
            if (Objects.equals(contact, contact4)) {
                assertThat(contact.getAddress()).isEqualTo(null);
            }
        });
    }
}
