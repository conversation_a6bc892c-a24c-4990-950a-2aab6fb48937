package com.subskribe.billy.resources.json.subscription;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import nl.jqno.equalsverifier.EqualsVerifier;
import org.junit.jupiter.api.Test;

class SubscriptionJsonTest {

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    @Test
    void testEquals() {
        EqualsVerifier.simple().forClass(SubscriptionJson.class).verify();
    }

    @Test
    void testJsonRoundTrip() throws Exception {
        SubscriptionJson subscriptionJson = SubscriptionJsonStub.createSubscriptionJson();
        String jsonValue = OBJECT_MAPPER.writeValueAsString(subscriptionJson);
        SubscriptionJson deserialized = OBJECT_MAPPER.readValue(jsonValue, SubscriptionJson.class);
        assertThat(deserialized).isEqualTo(subscriptionJson);
    }
}
