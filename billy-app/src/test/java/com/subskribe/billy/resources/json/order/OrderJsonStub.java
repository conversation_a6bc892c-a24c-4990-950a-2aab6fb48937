package com.subskribe.billy.resources.json.order;

import com.subskribe.billy.opportunity.model.OpportunityCrmType;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.shared.DiscountDetailJson;
import com.subskribe.billy.resources.json.shared.TenantDiscountJson;
import com.subskribe.billy.resources.json.shared.TenantDiscountLineItemJson;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.template.model.OrderTerms;
import com.subskribe.billy.template.model.OrderTermsLevelType;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;

public class OrderJsonStub {

    public static OrderJson createOrderJson() {
        OrderTerms orderTerms = new OrderTerms(
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            1,
            UUID.randomUUID().toString(),
            OrderTermsLevelType.ORDER,
            Set.of(UUID.randomUUID().toString())
        );

        TenantDiscountJson tenantDiscountJson = new TenantDiscountJson();
        tenantDiscountJson.setId(UUID.randomUUID().toString());
        tenantDiscountJson.setPercent(new BigDecimal("4.5"));

        DiscountDetailJson discountDetailJson = new DiscountDetailJson();

        discountDetailJson.setName(RandomStringUtils.randomAlphanumeric(10));
        discountDetailJson.setPercent(new BigDecimal("9.5"));
        discountDetailJson.setDiscountAmount(new BigDecimal("19.99"));
        discountDetailJson.setAmount(new BigDecimal("9.99"));

        TenantDiscountLineItemJson tenantDiscountLineItemJson = new TenantDiscountLineItemJson();
        tenantDiscountLineItemJson.setAmount(new BigDecimal("12.34"));
        tenantDiscountLineItemJson.setId(UUID.randomUUID().toString());
        tenantDiscountLineItemJson.setPercent(new BigDecimal("8.5"));

        OrderLineItemJson orderLineItemJson = new OrderLineItemJson();

        orderLineItemJson.setId(UUID.randomUUID().toString());
        orderLineItemJson.setDryRunItem(false);
        orderLineItemJson.setAction(ActionType.ADD);
        orderLineItemJson.setSubscriptionChargeId(UUID.randomUUID().toString());
        orderLineItemJson.setSubscriptionChargeGroupId(UUID.randomUUID().toString());
        orderLineItemJson.setQuantity(5);
        orderLineItemJson.setIsRamp(false);
        orderLineItemJson.setListUnitPrice(new BigDecimal("9.99"));
        orderLineItemJson.setSellUnitPrice(new BigDecimal("91.99"));
        orderLineItemJson.setDiscounts(List.of(discountDetailJson));
        orderLineItemJson.setAmount(new BigDecimal("9.99"));
        orderLineItemJson.setListAmount(new BigDecimal("9.99"));
        orderLineItemJson.setEffectiveDate(1672531200L);
        orderLineItemJson.setEndDate(1672531200L);
        orderLineItemJson.setPredefinedDiscounts(List.of(tenantDiscountLineItemJson));
        orderLineItemJson.setDiscountAmount(new BigDecimal("9.99"));
        orderLineItemJson.setListPriceOverrideRatio(new BigDecimal("9.99"));
        orderLineItemJson.setListAmountBeforeOverride(new BigDecimal("9.99"));
        orderLineItemJson.setListUnitPriceBeforeOverride(new BigDecimal("9.99"));
        orderLineItemJson.setAttributeReferences(
            List.of(
                AttributeReference.builder()
                    .attributeDefinitionId(RandomStringUtils.randomAlphanumeric(10))
                    .attributeValue(RandomStringUtils.randomAlphanumeric(10))
                    .build()
            )
        );

        OrderJson orderJson = new OrderJson();

        orderJson.setSubscriptionId(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setId(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setExternalId(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setName(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setOrderType(OrderType.NEW);
        orderJson.setCurrency("USD");
        orderJson.setPaymentTerm("NET0");
        orderJson.setAccountId(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setSubscriptionTargetVersion(5);
        orderJson.setShippingContactId(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setBillingContactId(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setStartDate(1672531200L);
        orderJson.setEndDate(1672531200L);
        orderJson.setTermLength(new RecurrenceJson(Cycle.MONTH, 6));
        orderJson.setBillingCycle(new RecurrenceJson(Cycle.YEAR, 1));
        orderJson.setTotalListAmount(new BigDecimal("199.99"));
        orderJson.setTotalListAmountBeforeOverride(new BigDecimal("299.99"));
        orderJson.setTotalAmount(new BigDecimal("99.99"));
        orderJson.setBillingTerm(BillingTerm.UP_FRONT);
        orderJson.setBillingAnchorDate(1672531200L);
        orderJson.setStatus(OrderStatus.EXECUTED);
        orderJson.setExecutedOn(1672531200L);
        orderJson.setExecutedOnFormatted("2022-01-01");
        orderJson.setUpdatedOn(1672531200L);
        orderJson.setRampInterval(List.of(1672531200L, 1672531300L));
        orderJson.setOrderFormTemplateIds(List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        orderJson.setOrderTerms(List.of(orderTerms));
        orderJson.setSfdcOpportunityId(UUID.randomUUID().toString());
        orderJson.setSfdcOpportunityName(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setSfdcOpportunityType(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setSfdcOpportunityStage(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setIsPrimaryOrderForSfdcOpportunity(true);
        orderJson.setSfdcOrderCanBeExecuted(true);
        orderJson.setPredefinedDiscounts(List.of(tenantDiscountJson));
        orderJson.setRenewalForSubscriptionId(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setRenewalForSubscriptionVersion(5);
        orderJson.setOwnerId(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setDocumentMasterTemplateId(UUID.randomUUID().toString());
        orderJson.setPurchaseOrderNumber(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setPurchaseOrderRequiredForInvoicing(false);
        orderJson.setAutoRenew(true);
        orderJson.setOpportunityCrmType(OpportunityCrmType.SALESFORCE);
        orderJson.setApprovalSegmentId(UUID.randomUUID().toString());
        orderJson.setAttachmentId(UUID.randomUUID().toString());
        orderJson.setCompositeOrderId(RandomStringUtils.randomAlphanumeric(10));
        orderJson.setExpiresOn(1672531200L);

        orderJson.setLineItems(List.of(orderLineItemJson));
        orderJson.setLineItemsNetEffect(List.of(orderLineItemJson));

        return orderJson;
    }
}
