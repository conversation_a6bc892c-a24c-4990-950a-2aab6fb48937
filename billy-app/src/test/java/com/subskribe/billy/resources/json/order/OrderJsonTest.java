package com.subskribe.billy.resources.json.order;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import nl.jqno.equalsverifier.EqualsVerifier;
import org.junit.jupiter.api.Test;

class OrderJsonTest {

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    @Test
    void testEquals() {
        EqualsVerifier.simple().forClass(OrderJson.class).withIgnoredFields("customBillingEligibleOrderLineIds").verify();
    }

    @Test
    void testJsonRoundTrip() throws Exception {
        OrderJson orderJson = OrderJsonStub.createOrderJson();
        String jsonValue = OBJECT_MAPPER.writeValueAsString(orderJson);
        OrderJson deserialized = OBJECT_MAPPER.readValue(jsonValue, OrderJson.class);
        assertThat(deserialized).isEqualTo(orderJson);
    }
}
