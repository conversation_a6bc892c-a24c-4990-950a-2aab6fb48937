package com.subskribe.billy.resources.json.approvalflow;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import nl.jqno.equalsverifier.EqualsVerifier;
import org.junit.jupiter.api.Test;

class ApprovalFlowJsonTest {

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    @Test
    void testEquals() {
        EqualsVerifier.simple().forClass(ApprovalFlowJson.class).verify();
    }

    @Test
    void testJsonRoundTrip() throws Exception {
        ApprovalFlowJson approvalFlowJson = ApprovalFlowJsonStub.createApprovalFlowJson();
        String jsonValue = OBJECT_MAPPER.writeValueAsString(approvalFlowJson);
        ApprovalFlowJson deserialized = OBJECT_MAPPER.readValue(jsonValue, ApprovalFlowJson.class);
        assertThat(deserialized).isEqualTo(approvalFlowJson);
    }
}
