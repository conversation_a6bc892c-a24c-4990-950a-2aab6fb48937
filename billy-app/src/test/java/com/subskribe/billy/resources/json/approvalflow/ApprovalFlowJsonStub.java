package com.subskribe.billy.resources.json.approvalflow;

import com.subskribe.billy.approvalflow.model.ApprovalFlowStatus;
import com.subskribe.billy.approvalflow.model.ApprovalRuleConditions;
import com.subskribe.billy.approvalflow.model.ApproverType;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;

class ApprovalFlowJsonStub {

    static ApprovalFlowJson createApprovalFlowJson() {
        ApprovalStateActionJson approvalStateActionJson = new ApprovalStateActionJson();

        approvalStateActionJson.setEmailGroupId(UUID.randomUUID().toString());

        ApprovalStateJson approvalStateJson1 = new ApprovalStateJson();

        approvalStateJson1.setId(UUID.randomUUID().toString());
        approvalStateJson1.setName(RandomStringUtils.randomAlphanumeric(10));
        approvalStateJson1.setApprovalGroupId("USER-1111111");
        approvalStateJson1.setApproverId("USER-1111111");
        approvalStateJson1.setApproverType(ApproverType.USER);
        approvalStateJson1.setAction(approvalStateActionJson);

        ApprovalStateJson approvalStateJson2 = new ApprovalStateJson();

        approvalStateJson2.setId(UUID.randomUUID().toString());
        approvalStateJson2.setName(RandomStringUtils.randomAlphanumeric(10));
        approvalStateJson2.setApprovalGroupId("USRG-2222222");
        approvalStateJson2.setApproverId("USRG-2222222");
        approvalStateJson2.setApproverType(ApproverType.USER_GROUP);
        approvalStateJson2.setAction(approvalStateActionJson);

        ApprovalRuleConditions approvalRuleConditions = new ApprovalRuleConditions();

        approvalRuleConditions.setOrderCondition("orderCondition");
        approvalRuleConditions.setOrderLineCondition("orderLineCondition");

        ApprovalTransitionRuleJson approvalTransitionRuleJson1 = new ApprovalTransitionRuleJson();

        approvalTransitionRuleJson1.setId(UUID.randomUUID().toString());
        approvalTransitionRuleJson1.setName(RandomStringUtils.randomAlphanumeric(10));
        approvalTransitionRuleJson1.setFromState("start");
        approvalTransitionRuleJson1.setToState("group1-approval-state");
        approvalTransitionRuleJson1.setRuleConditions(approvalRuleConditions);

        ApprovalTransitionRuleJson approvalTransitionRuleJson2 = new ApprovalTransitionRuleJson();

        approvalTransitionRuleJson2.setId(UUID.randomUUID().toString());
        approvalTransitionRuleJson2.setName(RandomStringUtils.randomAlphanumeric(10));
        approvalTransitionRuleJson2.setFromState("group1-approval-state");
        approvalTransitionRuleJson2.setToState("group2-approval-state");
        approvalTransitionRuleJson2.setRuleConditions(approvalRuleConditions);

        ApprovalFlowJson approvalFlowJson = new ApprovalFlowJson();

        approvalFlowJson.setId(RandomStringUtils.randomAlphanumeric(10));
        approvalFlowJson.setName(RandomStringUtils.randomAlphanumeric(10));
        approvalFlowJson.setDescription(RandomStringUtils.randomAlphanumeric(10));
        approvalFlowJson.setStatus(ApprovalFlowStatus.ACTIVE);

        approvalFlowJson.setStates(List.of(approvalStateJson1, approvalStateJson2));
        approvalFlowJson.setTransitionRules(List.of(approvalTransitionRuleJson1, approvalTransitionRuleJson2));
        approvalFlowJson.setIsSmartApproval(Boolean.FALSE);

        return approvalFlowJson;
    }
}
