package com.subskribe.billy.resources.json.subscription;

import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoice.model.PurchaseOrder;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.resources.json.shared.DiscountJson;
import com.subskribe.billy.resources.json.shared.TenantDiscountJson;
import com.subskribe.billy.resources.json.shared.TenantDiscountLineItemJson;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.subscription.model.SubscriptionState;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;

public class SubscriptionJsonStub {

    public static SubscriptionJson createSubscriptionJson() {
        DiscountJson discountJson = new DiscountJson();
        discountJson.setName(RandomStringUtils.randomAlphanumeric(10));
        discountJson.setPercent(new BigDecimal("4.5"));
        discountJson.setDiscountAmount(new BigDecimal("325.70"));

        TenantDiscountLineItemJson tenantDiscountLineItemJson = new TenantDiscountLineItemJson();
        tenantDiscountLineItemJson.setAmount(new BigDecimal("12.34"));
        tenantDiscountLineItemJson.setId(UUID.randomUUID().toString());
        tenantDiscountLineItemJson.setPercent(new BigDecimal("8.5"));

        SubscriptionChargeJson subscriptionChargeJson = new SubscriptionChargeJson();
        subscriptionChargeJson.setId(UUID.randomUUID().toString());
        subscriptionChargeJson.setGroupId(UUID.randomUUID().toString());
        subscriptionChargeJson.setAccountId(RandomStringUtils.randomAlphanumeric(10));
        subscriptionChargeJson.setChargeId(RandomStringUtils.randomAlphanumeric(10));
        subscriptionChargeJson.setQuantity(12);
        subscriptionChargeJson.setIsRamp(true);
        subscriptionChargeJson.setDiscounts(List.of(discountJson));
        subscriptionChargeJson.setListUnitPrice(new BigDecimal("45"));
        subscriptionChargeJson.setSellUnitPrice(new BigDecimal("37.25"));
        subscriptionChargeJson.setStartDate(**********);
        subscriptionChargeJson.setEndDate(**********);
        subscriptionChargeJson.setPredefinedDiscounts(List.of(tenantDiscountLineItemJson));
        subscriptionChargeJson.setDiscountAmount(new BigDecimal("66.985"));
        subscriptionChargeJson.setOrderLines(List.of(UUID.randomUUID()));

        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setOriginOrderId(UUID.randomUUID().toString());
        purchaseOrder.setAddedOn(**********L);
        purchaseOrder.setPurchaseOrderNumber(RandomStringUtils.randomAlphanumeric(10));

        TenantDiscountJson tenantDiscountJson = new TenantDiscountJson();
        tenantDiscountJson.setId(UUID.randomUUID().toString());
        tenantDiscountJson.setPercent(new BigDecimal("4.5"));

        SubscriptionJson subscriptionJson = new SubscriptionJson();

        subscriptionJson.setId(UUID.randomUUID().toString());
        subscriptionJson.setExternalId(UUID.randomUUID().toString());
        subscriptionJson.setAccountId(RandomStringUtils.randomAlphanumeric(10));
        subscriptionJson.setShippingContactId(RandomStringUtils.randomAlphanumeric(10));
        subscriptionJson.setBillingContactId(RandomStringUtils.randomAlphanumeric(10));
        subscriptionJson.setState(SubscriptionState.ACTIVE);
        subscriptionJson.setStartDate(**********);
        subscriptionJson.setEndDate(**********);
        subscriptionJson.setBillingCycle(new RecurrenceJson(Cycle.MONTH, 12));
        subscriptionJson.setCanceledDate(null);
        subscriptionJson.setTermLength(new RecurrenceJson(Cycle.MONTH, 6));
        subscriptionJson.setPaymentTerm(PaymentTerm.NET30.name());
        subscriptionJson.setBillingTerm(BillingTerm.UP_FRONT);
        subscriptionJson.setCharges(List.of(subscriptionChargeJson));
        subscriptionJson.setOrders(List.of(UUID.randomUUID().toString()));
        subscriptionJson.setPurchaseOrders(List.of(purchaseOrder));
        subscriptionJson.setPurchaseOrderRequiredForInvoicing(true);
        subscriptionJson.setCreationTime(**********);
        subscriptionJson.setRampInterval(List.of(**********L, 1672531201L));
        subscriptionJson.setPredefinedDiscounts(List.of(tenantDiscountJson));
        subscriptionJson.setRenewedFromSubscriptionId(RandomStringUtils.randomAlphanumeric(10));
        subscriptionJson.setRenewedFromDate(Instant.ofEpochSecond(**********L));
        subscriptionJson.setRenewedToSubscriptionId(RandomStringUtils.randomAlphanumeric(10));
        subscriptionJson.setRenewedToDate(Instant.ofEpochSecond(**********L));
        subscriptionJson.setAutoRenew(true);
        subscriptionJson.setEntityId(UUID.randomUUID().toString());

        return subscriptionJson;
    }
}
