package com.subskribe.billy.resources.json.invoice;

import com.subskribe.billy.invoice.model.EmailNotifiersList;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.resources.json.account.AccountAddressJson;
import com.subskribe.billy.resources.json.account.AccountContactJson;
import com.subskribe.billy.resources.json.taxrate.TaxRateJson;
import com.subskribe.billy.shared.enums.Status;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;

public class InvoiceJsonStub {

    public static InvoiceJson buildInvoiceJson() {
        AccountAddressJson accountAddressJson = new AccountAddressJson();

        accountAddressJson.setStreetAddressLine1(RandomStringUtils.randomAlphanumeric(20));
        accountAddressJson.setStreetAddressLine2(RandomStringUtils.randomAlphanumeric(10));
        accountAddressJson.setCity(RandomStringUtils.randomAlphanumeric(10));
        accountAddressJson.setState(RandomStringUtils.randomAlphanumeric(2));
        accountAddressJson.setCountry(RandomStringUtils.randomAlphanumeric(2));
        accountAddressJson.setZipcode(RandomStringUtils.randomAlphanumeric(5));

        AccountContactJson accountContactJson = new AccountContactJson();

        accountContactJson.setId(UUID.randomUUID().toString());
        accountContactJson.setAccountId(RandomStringUtils.randomAlphanumeric(10));
        accountContactJson.setFirstName(RandomStringUtils.randomAlphanumeric(10));
        accountContactJson.setLastName(RandomStringUtils.randomAlphanumeric(10));
        accountContactJson.setEmail(RandomStringUtils.randomAlphanumeric(10));
        accountContactJson.setPhoneNumber(RandomStringUtils.randomAlphanumeric(10));
        accountContactJson.setTitle(RandomStringUtils.randomAlphanumeric(10));
        accountContactJson.setAddress(accountAddressJson);
        accountContactJson.setExternalId(RandomStringUtils.randomAlphanumeric(10));

        TaxRateJson taxRateJson = new TaxRateJson();

        taxRateJson.setId(UUID.randomUUID());
        taxRateJson.setName(RandomStringUtils.randomAlphanumeric(10));
        taxRateJson.setDescription(RandomStringUtils.randomAlphanumeric(100));
        taxRateJson.setTaxPercentage(new BigDecimal("13"));
        taxRateJson.setTaxCode("VAT");
        taxRateJson.setTaxInclusive(true);
        taxRateJson.setStatus(Status.ACTIVE);
        taxRateJson.setInUse(true);

        InvoiceItemJson invoiceItemJson = new InvoiceItemJson();

        invoiceItemJson.setId(UUID.randomUUID().toString());
        invoiceItemJson.setChargeId(RandomStringUtils.randomAlphanumeric(10));
        invoiceItemJson.setOrderId(RandomStringUtils.randomAlphanumeric(10));
        invoiceItemJson.setOrderLineItemId(RandomStringUtils.randomAlphanumeric(10));
        invoiceItemJson.setSubscriptionChargeId(RandomStringUtils.randomAlphanumeric(10));
        invoiceItemJson.setSubscriptionChargeGroupId(RandomStringUtils.randomAlphanumeric(10));
        invoiceItemJson.setListAmount(new BigDecimal("10.00"));
        invoiceItemJson.setDiscountAmount(new BigDecimal("9.99"));
        invoiceItemJson.setAmount(new BigDecimal("10.01"));
        invoiceItemJson.setTaxAmount(new BigDecimal("11.11"));
        invoiceItemJson.setTaxRate(taxRateJson);
        invoiceItemJson.setListUnitPrice(new BigDecimal("10.02"));
        invoiceItemJson.setSellUnitPrice(new BigDecimal("10.03"));
        invoiceItemJson.setQuantity(10L);
        invoiceItemJson.setPeriodStartDate(1672531200L);
        invoiceItemJson.setPeriodEndDate(1672531210L);
        invoiceItemJson.setIsBilled(true);

        EmailNotifiersList emailNotifiersList = new EmailNotifiersList(
            List.of(RandomStringUtils.randomAlphanumeric(10)),
            List.of(RandomStringUtils.randomAlphanumeric(10)),
            List.of(RandomStringUtils.randomAlphanumeric(10))
        );

        InvoiceJson invoiceJson = new InvoiceJson();
        invoiceJson.setAccountId(RandomStringUtils.randomAlphanumeric(10));
        invoiceJson.setInvoiceNumber(RandomStringUtils.randomAlphanumeric(10));
        invoiceJson.setPostedDate(1672531200L);
        invoiceJson.setInvoiceDate(1672531201L);
        invoiceJson.setVoidedDate(1672531202L);
        invoiceJson.setDueDate(1672531203L);
        invoiceJson.setCurrency("USD");
        invoiceJson.setPaymentTerm("NET_30");
        invoiceJson.setTotalDiscount(new BigDecimal("9.99"));
        invoiceJson.setSubTotal(new BigDecimal("10.33"));
        invoiceJson.setTaxTotal(new BigDecimal("11.22"));
        invoiceJson.setTotal(new BigDecimal("19.55"));
        invoiceJson.setSubscriptionId(RandomStringUtils.randomAlphanumeric(10));
        invoiceJson.setInvoiceItems(List.of(invoiceItemJson));
        invoiceJson.setBillingContact(accountContactJson);
        invoiceJson.setShippingContact(accountContactJson);
        invoiceJson.setStatus(InvoiceStatus.POSTED);
        invoiceJson.setTaxTransactionCode(RandomStringUtils.randomAlphanumeric(10));
        invoiceJson.setPurchaseOrderNumber(RandomStringUtils.randomAlphanumeric(10));
        invoiceJson.setPurchaseOrderRequired(false);
        invoiceJson.setNote(RandomStringUtils.randomAlphanumeric(100));
        invoiceJson.setEmailNotifiersList(emailNotifiersList);

        return invoiceJson;
    }
}
