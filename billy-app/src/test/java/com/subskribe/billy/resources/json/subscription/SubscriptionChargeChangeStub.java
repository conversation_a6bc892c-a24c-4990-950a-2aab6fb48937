package com.subskribe.billy.resources.json.subscription;

import com.subskribe.billy.subscription.services.ImmutableSubscriptionChargeChange;
import com.subskribe.billy.subscription.services.SubscriptionChargeChange;
import java.time.Instant;

public class SubscriptionChargeChangeStub {

    public static SubscriptionChargeChange createSubscriptionChargeChange() {
        SubscriptionJson subscriptionJson = SubscriptionJsonStub.createSubscriptionJson();
        return ImmutableSubscriptionChargeChange.builder()
            .changesOn(Instant.now())
            .subscription(subscriptionJson)
            .chargesEnding(subscriptionJson.getCharges())
            .chargesStarting(subscriptionJson.getCharges())
            .build();
    }
}
