package com.subskribe.billy.resources.json.invoice;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import nl.jqno.equalsverifier.EqualsVerifier;
import org.junit.jupiter.api.Test;

class InvoiceJsonTest {

    @Test
    void testEquals() {
        EqualsVerifier.simple().forClass(InvoiceJson.class).verify();
    }

    @Test
    void testJsonRoundtrip() throws Exception {
        ObjectMapper objectMapper = JacksonProvider.defaultMapper();
        InvoiceJson invoiceJson = InvoiceJsonStub.buildInvoiceJson();
        String jsonValue = objectMapper.writeValueAsString(invoiceJson);
        InvoiceJson deserialized = objectMapper.readValue(jsonValue, InvoiceJson.class);
        assertThat(deserialized).isEqualTo(invoiceJson);
    }
}
