package com.subskribe.billy.resources;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.infra.SystemDataProvider;
import com.subskribe.billy.resources.json.version.VersionJson;
import java.util.Optional;
import javax.ws.rs.core.Response;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class VersionResourceTest {

    private VersionResource versionResource;
    private SystemDataProvider systemDataProvider;

    @BeforeEach
    void setUp() {
        systemDataProvider = mock(SystemDataProvider.class);
        versionResource = new VersionResource(systemDataProvider);
    }

    @Test
    void testWithoutEnvironmentVariables() {
        when(systemDataProvider.getValueOfEnvironmentVariable("RELEASE_NAME")).thenReturn(Optional.empty());
        when(systemDataProvider.getValueOfEnvironmentVariable("RELEASED_FROM")).thenReturn(Optional.empty());
        VersionJson expected = new VersionJson("undefined", "undefined", "undefined", "undefined");

        Response response = versionResource.getVersion();

        assertResult(response, expected);
    }

    @Test
    void testWithExistingVariables() {
        when(systemDataProvider.getValueOfEnvironmentVariable("RELEASE_NAME")).thenReturn(Optional.of("test-release"));
        when(systemDataProvider.getValueOfEnvironmentVariable("RELEASED_FROM")).thenReturn(Optional.of("my-job"));
        VersionJson expected = new VersionJson("undefined", "test-release", "my-job", "https://github.com/Subskribe/billy/commits/test-release");

        Response response = versionResource.getVersion();

        assertResult(response, expected);
    }

    private void assertResult(Response response, VersionJson expectedResult) {
        assertThat(response.getEntity()).isEqualTo(expectedResult);
    }
}
