package com.subskribe.billy.banktransactions.services;

import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.banktransactions.model.BankTransactionsUploadCsvSchema;
import com.subskribe.billy.banktransactions.model.ImmutableBankTransactionsUploadData;
import com.subskribe.billy.banktransactions.model.PotentialInvoiceMatch;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class BankTransactionPotentialInvoiceFinderTest {

    BankTransactionPotentialInvoiceFinder bankTransactionPotentialInvoiceFinder;

    @Mock
    TenantSettingService tenantSettingService;

    @Mock
    TenantIdProvider tenantIdProvider;

    @Mock
    TenantSetting tenantSetting;

    @Mock
    Invoice invoice;

    @Mock
    AccountContact accountContact;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        when(tenantIdProvider.provideTenantIdString()).thenReturn("tenantId");
        when(tenantSettingService.getTenantSettingInternal()).thenReturn(tenantSetting);
        when(tenantSetting.getDefaultTimeZone()).thenReturn(TimeZone.getTimeZone(ZoneId.systemDefault()));
        bankTransactionPotentialInvoiceFinder = new BankTransactionPotentialInvoiceFinder(tenantSettingService);
    }

    @Test
    void bestCaseMatch() {
        ImmutableBankTransactionsUploadData bankTransactionsUploadData = ImmutableBankTransactionsUploadData.builder()
            .referenceNumber("INV-00001")
            .payerName("John Doe")
            .transactionAmount("1000")
            .transactionCurrency("USD")
            .transactionDate("11/11/2024 10:00:00")
            .failed(false)
            .transactionType("Deposit")
            .failureReason("")
            .externalTransactionId("EXT-0001")
            .bankAccountId("BANK-0001")
            .paymentType("Check")
            .build();
        when(invoice.getInvoiceNumber()).thenReturn(new Invoice.Number("INV-00001"));
        when(accountContact.getFullName()).thenReturn("John Doe");
        when(invoice.getBillingContact()).thenReturn(accountContact);
        when(invoice.getShippingContact()).thenReturn(accountContact);
        when(invoice.getTotal()).thenReturn(BigDecimal.valueOf(1000));
        Instant invoiceDueDate = dateFromString("11/11/2024 10:00:00").toInstant();
        when(invoice.getDueDate()).thenReturn(invoiceDueDate);

        PotentialInvoiceMatch potentialInvoiceMatch = bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData);
        Assertions.assertNotNull(potentialInvoiceMatch);
        Assertions.assertEquals(potentialInvoiceMatch.getInvoiceNumber(), invoice.getInvoiceNumber().getNumber());
        Assertions.assertEquals(99, potentialInvoiceMatch.getMatchingConfidence());
    }

    @Test
    void onlyNumericPartOfInvoiceNumber() {
        ImmutableBankTransactionsUploadData bankTransactionsUploadData = ImmutableBankTransactionsUploadData.builder()
            .referenceNumber("00001")
            .payerName("John Doe")
            .transactionAmount("1000")
            .transactionCurrency("USD")
            .transactionDate("11/11/2024 10:00:00")
            .failed(false)
            .transactionType("Deposit")
            .failureReason("")
            .externalTransactionId("EXT-0001")
            .bankAccountId("BANK-0001")
            .paymentType("Check")
            .build();
        when(invoice.getInvoiceNumber()).thenReturn(new Invoice.Number("INV-00001"));
        when(accountContact.getFullName()).thenReturn("John Doe");
        when(invoice.getBillingContact()).thenReturn(accountContact);
        when(invoice.getShippingContact()).thenReturn(accountContact);
        when(invoice.getTotal()).thenReturn(BigDecimal.valueOf(1000));
        Instant invoiceDueDate = dateFromString("11/11/2024 10:00:00").toInstant();
        when(invoice.getDueDate()).thenReturn(invoiceDueDate);

        PotentialInvoiceMatch potentialInvoiceMatch = bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData);
        Assertions.assertNotNull(potentialInvoiceMatch);
        Assertions.assertEquals(potentialInvoiceMatch.getInvoiceNumber(), invoice.getInvoiceNumber().getNumber());
        Assertions.assertEquals(69, potentialInvoiceMatch.getMatchingConfidence());
    }

    @Test
    void noPayerNameMatch() {
        ImmutableBankTransactionsUploadData bankTransactionsUploadData = ImmutableBankTransactionsUploadData.builder()
            .referenceNumber("INV-00001")
            .payerName("")
            .transactionAmount("1000")
            .transactionCurrency("USD")
            .transactionDate("11/11/2024 10:00:00")
            .failed(false)
            .transactionType("Deposit")
            .failureReason("")
            .externalTransactionId("EXT-0001")
            .bankAccountId("BANK-0001")
            .paymentType("Check")
            .build();
        when(invoice.getInvoiceNumber()).thenReturn(new Invoice.Number("INV-00001"));
        when(accountContact.getFullName()).thenReturn("John Doe");
        when(invoice.getBillingContact()).thenReturn(accountContact);
        when(invoice.getShippingContact()).thenReturn(accountContact);
        when(invoice.getTotal()).thenReturn(BigDecimal.valueOf(1000));
        Instant invoiceDueDate = dateFromString("11/11/2024 10:00:00").toInstant();
        when(invoice.getDueDate()).thenReturn(invoiceDueDate);

        PotentialInvoiceMatch potentialInvoiceMatch = bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData);
        Assertions.assertNotNull(potentialInvoiceMatch);
        Assertions.assertEquals(potentialInvoiceMatch.getInvoiceNumber(), invoice.getInvoiceNumber().getNumber());
        Assertions.assertEquals(90, potentialInvoiceMatch.getMatchingConfidence());
    }

    @Test
    void noTransactionAmountMatch() {
        ImmutableBankTransactionsUploadData bankTransactionsUploadData = ImmutableBankTransactionsUploadData.builder()
            .referenceNumber("INV-00001")
            .payerName("John Doe")
            .transactionAmount("10000")
            .transactionCurrency("USD")
            .transactionDate("11/11/2024 10:00:00")
            .failed(false)
            .transactionType("Deposit")
            .failureReason("")
            .externalTransactionId("EXT-0001")
            .bankAccountId("BANK-0001")
            .paymentType("Check")
            .build();
        when(invoice.getInvoiceNumber()).thenReturn(new Invoice.Number("INV-00001"));
        when(accountContact.getFullName()).thenReturn("John Doe");
        when(invoice.getBillingContact()).thenReturn(accountContact);
        when(invoice.getShippingContact()).thenReturn(accountContact);
        when(invoice.getTotal()).thenReturn(BigDecimal.valueOf(1000));
        Instant invoiceDueDate = dateFromString("11/11/2024 10:00:00").toInstant();
        when(invoice.getDueDate()).thenReturn(invoiceDueDate);

        PotentialInvoiceMatch potentialInvoiceMatch = bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData);
        Assertions.assertNotNull(potentialInvoiceMatch);
        Assertions.assertEquals(potentialInvoiceMatch.getInvoiceNumber(), invoice.getInvoiceNumber().getNumber());
        Assertions.assertEquals(79, potentialInvoiceMatch.getMatchingConfidence());
    }

    @Test
    void noTransactionDateMatch() {
        ImmutableBankTransactionsUploadData bankTransactionsUploadData = ImmutableBankTransactionsUploadData.builder()
            .referenceNumber("INV-00001")
            .payerName("John Doe")
            .transactionAmount("1000")
            .transactionCurrency("USD")
            .transactionDate("12/11/2024 10:00:00")
            .failed(false)
            .transactionType("Deposit")
            .failureReason("")
            .externalTransactionId("EXT-0001")
            .bankAccountId("BANK-0001")
            .paymentType("Check")
            .build();
        when(invoice.getInvoiceNumber()).thenReturn(new Invoice.Number("INV-00001"));
        when(accountContact.getFullName()).thenReturn("John Doe");
        when(invoice.getBillingContact()).thenReturn(accountContact);
        when(invoice.getShippingContact()).thenReturn(accountContact);
        when(invoice.getTotal()).thenReturn(BigDecimal.valueOf(1000));
        Instant invoiceDueDate = dateFromString("11/11/2024 10:00:00").toInstant();
        when(invoice.getDueDate()).thenReturn(invoiceDueDate);

        PotentialInvoiceMatch potentialInvoiceMatch = bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData);
        Assertions.assertNotNull(potentialInvoiceMatch);
        Assertions.assertEquals(potentialInvoiceMatch.getInvoiceNumber(), invoice.getInvoiceNumber().getNumber());
        Assertions.assertEquals(89, potentialInvoiceMatch.getMatchingConfidence());
    }

    @Test
    void onlyInvoiceNumberMatch() {
        ImmutableBankTransactionsUploadData bankTransactionsUploadData = ImmutableBankTransactionsUploadData.builder()
            .referenceNumber("INV-00001")
            .payerName("")
            .transactionAmount("10000")
            .transactionCurrency("USD")
            .transactionDate("12/11/2024 10:00:00")
            .failed(false)
            .transactionType("Deposit")
            .failureReason("")
            .externalTransactionId("EXT-0001")
            .bankAccountId("BANK-0001")
            .paymentType("Check")
            .build();
        when(invoice.getInvoiceNumber()).thenReturn(new Invoice.Number("INV-00001"));
        when(accountContact.getFullName()).thenReturn("John Doe");
        when(invoice.getBillingContact()).thenReturn(accountContact);
        when(invoice.getShippingContact()).thenReturn(accountContact);
        when(invoice.getTotal()).thenReturn(BigDecimal.valueOf(1000));
        Instant invoiceDueDate = dateFromString("11/11/2024 10:00:00").toInstant();
        when(invoice.getDueDate()).thenReturn(invoiceDueDate);

        PotentialInvoiceMatch potentialInvoiceMatch = bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData);
        Assertions.assertNotNull(potentialInvoiceMatch);
        Assertions.assertEquals(potentialInvoiceMatch.getInvoiceNumber(), invoice.getInvoiceNumber().getNumber());
        Assertions.assertEquals(60, potentialInvoiceMatch.getMatchingConfidence());
    }

    @Test
    void onlyInvoiceNumberNumericMatch() {
        ImmutableBankTransactionsUploadData bankTransactionsUploadData = ImmutableBankTransactionsUploadData.builder()
            .referenceNumber("00001")
            .payerName("")
            .transactionAmount("10000")
            .transactionCurrency("USD")
            .transactionDate("12/11/2024 10:00:00")
            .failed(false)
            .transactionType("Deposit")
            .failureReason("")
            .externalTransactionId("EXT-0001")
            .bankAccountId("BANK-0001")
            .paymentType("Check")
            .build();
        when(invoice.getInvoiceNumber()).thenReturn(new Invoice.Number("INV-00001"));
        when(accountContact.getFullName()).thenReturn("John Doe");
        when(invoice.getBillingContact()).thenReturn(accountContact);
        when(invoice.getShippingContact()).thenReturn(accountContact);
        when(invoice.getTotal()).thenReturn(BigDecimal.valueOf(1000));
        Instant invoiceDueDate = dateFromString("11/11/2024 10:00:00").toInstant();
        when(invoice.getDueDate()).thenReturn(invoiceDueDate);

        PotentialInvoiceMatch potentialInvoiceMatch = bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData);
        Assertions.assertNotNull(potentialInvoiceMatch);
        Assertions.assertEquals(potentialInvoiceMatch.getInvoiceNumber(), invoice.getInvoiceNumber().getNumber());
        Assertions.assertEquals(30, potentialInvoiceMatch.getMatchingConfidence());
    }

    @Test
    void onlyAmountMatch() {
        ImmutableBankTransactionsUploadData bankTransactionsUploadData = ImmutableBankTransactionsUploadData.builder()
            .referenceNumber("INV-00002")
            .payerName("")
            .transactionAmount("1000")
            .transactionCurrency("USD")
            .transactionDate("12/11/2024 10:00:00")
            .failed(false)
            .transactionType("Deposit")
            .failureReason("")
            .externalTransactionId("EXT-0001")
            .bankAccountId("BANK-0001")
            .paymentType("Check")
            .build();
        when(invoice.getInvoiceNumber()).thenReturn(new Invoice.Number("INV-00001"));
        when(accountContact.getFullName()).thenReturn("John Doe");
        when(invoice.getBillingContact()).thenReturn(accountContact);
        when(invoice.getShippingContact()).thenReturn(accountContact);
        when(invoice.getTotal()).thenReturn(BigDecimal.valueOf(1000));
        Instant invoiceDueDate = dateFromString("11/11/2024 10:00:00").toInstant();
        when(invoice.getDueDate()).thenReturn(invoiceDueDate);

        PotentialInvoiceMatch potentialInvoiceMatch = bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData);
        Assertions.assertNotNull(potentialInvoiceMatch);
        Assertions.assertEquals(potentialInvoiceMatch.getInvoiceNumber(), invoice.getInvoiceNumber().getNumber());
        Assertions.assertEquals(20, potentialInvoiceMatch.getMatchingConfidence());
    }

    @Test
    void onlyPayerNameMatch() {
        ImmutableBankTransactionsUploadData bankTransactionsUploadData = ImmutableBankTransactionsUploadData.builder()
            .referenceNumber("INV-00002")
            .payerName("John Doe")
            .transactionAmount("10020")
            .transactionCurrency("USD")
            .transactionDate("12/11/2024 10:00:00")
            .failed(false)
            .transactionType("Deposit")
            .failureReason("")
            .externalTransactionId("EXT-0001")
            .bankAccountId("BANK-0001")
            .paymentType("Check")
            .build();
        when(invoice.getInvoiceNumber()).thenReturn(new Invoice.Number("INV-00001"));
        when(accountContact.getFullName()).thenReturn("John Doe");
        when(invoice.getBillingContact()).thenReturn(accountContact);
        when(invoice.getShippingContact()).thenReturn(accountContact);
        when(invoice.getTotal()).thenReturn(BigDecimal.valueOf(1000));
        Instant invoiceDueDate = dateFromString("11/11/2024 10:00:00").toInstant();
        when(invoice.getDueDate()).thenReturn(invoiceDueDate);

        PotentialInvoiceMatch potentialInvoiceMatch = bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData);
        Assertions.assertNotNull(potentialInvoiceMatch);
        Assertions.assertEquals(potentialInvoiceMatch.getInvoiceNumber(), invoice.getInvoiceNumber().getNumber());
        Assertions.assertEquals(9, potentialInvoiceMatch.getMatchingConfidence());
    }

    @Test
    void worstCaseMatch() {
        ImmutableBankTransactionsUploadData bankTransactionsUploadData = ImmutableBankTransactionsUploadData.builder()
            .referenceNumber("INV-00002")
            .payerName("")
            .transactionAmount("10000")
            .transactionCurrency("USD")
            .transactionDate("12/11/2024 10:00:00")
            .failed(false)
            .transactionType("Deposit")
            .failureReason("")
            .externalTransactionId("EXT-0001")
            .bankAccountId("BANK-0001")
            .paymentType("Check")
            .build();
        when(invoice.getInvoiceNumber()).thenReturn(new Invoice.Number("INV-00001"));
        when(accountContact.getFullName()).thenReturn("John Doe");
        when(invoice.getBillingContact()).thenReturn(accountContact);
        when(invoice.getShippingContact()).thenReturn(accountContact);
        when(invoice.getTotal()).thenReturn(BigDecimal.valueOf(1000));
        Instant invoiceDueDate = dateFromString("11/11/2024 10:00:00").toInstant();
        when(invoice.getDueDate()).thenReturn(invoiceDueDate);

        PotentialInvoiceMatch potentialInvoiceMatch = bankTransactionPotentialInvoiceFinder.findPotentialInvoice(invoice, bankTransactionsUploadData);
        Assertions.assertNotNull(potentialInvoiceMatch);
        Assertions.assertEquals(potentialInvoiceMatch.getInvoiceNumber(), invoice.getInvoiceNumber().getNumber());
        Assertions.assertEquals(0, potentialInvoiceMatch.getMatchingConfidence());
    }

    private Date dateFromString(String sourceTransactionDateStr) {
        return Date.from(ZonedDateTime.parse(sourceTransactionDateStr, tenantUsageTimeFormatter()).toInstant());
    }

    private DateTimeFormatter tenantUsageTimeFormatter() {
        TimeZone tenantTimeZone = tenantSettingService.getTenantSettingInternal().getDefaultTimeZone();
        return DateTimeFormatter.ofPattern(BankTransactionsUploadCsvSchema.BANK_TRANSACTIONS_UPLOAD_DATE_TIME_FORMAT).withZone(
            tenantTimeZone.toZoneId()
        );
    }
}
