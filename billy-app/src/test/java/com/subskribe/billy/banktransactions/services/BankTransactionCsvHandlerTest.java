package com.subskribe.billy.banktransactions.services;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.subskribe.billy.banktransactions.model.BankTransactionsUploadData;
import com.subskribe.billy.payment.model.PaymentBankAccount;
import com.subskribe.billy.payment.services.PaymentBankAccountService;
import com.subskribe.billy.shared.enums.PaymentBankAccountStatus;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.ZoneId;
import java.util.Currency;
import java.util.List;
import java.util.TimeZone;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class BankTransactionCsvHandlerTest {

    BankTransactionCsvHandler bankTransactionCsvHandler;

    @Mock
    TenantSettingService tenantSettingService;

    @Mock
    PaymentBankAccountService paymentBankAccountService;

    @Mock
    TenantIdProvider tenantIdProvider;

    @Mock
    TenantSetting tenantSetting;

    @Mock
    PaymentBankAccount paymentBankAccount;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        when(tenantIdProvider.provideTenantIdString()).thenReturn("tenantId");
        when(tenantSettingService.getTenantSettingInternal()).thenReturn(tenantSetting);
        when(tenantSetting.getDefaultTimeZone()).thenReturn(TimeZone.getTimeZone(ZoneId.systemDefault()));
        when(paymentBankAccountService.getByPaymentBankAccountId(anyString())).thenReturn(paymentBankAccount);
        when(paymentBankAccount.getCurrencyCode()).thenReturn(Currency.getInstance("USD"));
        when(paymentBankAccount.getStatus()).thenReturn(PaymentBankAccountStatus.ACTIVE);
        bankTransactionCsvHandler = new BankTransactionCsvHandler(tenantSettingService, paymentBankAccountService);
    }

    @Test
    void testImportHappyState() {
        String csvContent =
            """
            BankAccountId,ExternalTransactionId,TransactionAmount,TransactionCurrency,TransactionDate,PayerName,ReferenceNumber,TransactionType,PaymentType
            "<addPaymentBankAccountId>","EXT-9001","2400","USD","11/15/2024 10:00:00","Mia Allen","INV-000071","Wire","Deposit"
            "<addPaymentBankAccountId>","EXT-9002","1300","USD","10/15/2024 10:00:00","Mia Allen","Payment for services","ACH","Deposit"
            "<addPaymentBankAccountId>","EXT-9003","2700","USD","10/10/2024 10:00:00","Layla James","","PayPal","Deposit"
            """;

        InputStream inputStream = new ByteArrayInputStream(csvContent.getBytes());
        try {
            List<BankTransactionsUploadData> uploadData = bankTransactionCsvHandler.getBankTransactionsUploadData(inputStream);
            Assertions.assertNotNull(uploadData);
            Assertions.assertEquals(3, uploadData.size());
            Assertions.assertEquals(0, uploadData.stream().filter(BankTransactionsUploadData::isFailed).count());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testImportDateInvalidFormat() {
        String csvContent =
            """
            BankAccountId,ExternalTransactionId,TransactionAmount,TransactionCurrency,TransactionDate,PayerName,ReferenceNumber,TransactionType,PaymentType
            "<addPaymentBankAccountId>","EXT-9001","2400","USD","11/15/2024","Mia Allen","INV-000071","Wire","Deposit"
            "<addPaymentBankAccountId>","EXT-9002","1300","USD","10/15/2024","Mia Allen","Payment for services","ACH","Deposit"
            "<addPaymentBankAccountId>","EXT-9003","2700","USD","10/10/2024","Layla James","","PayPal","Deposit"
            """;

        InputStream inputStream = new ByteArrayInputStream(csvContent.getBytes());
        try {
            List<BankTransactionsUploadData> uploadData = bankTransactionCsvHandler.getBankTransactionsUploadData(inputStream);
            Assertions.assertNotNull(uploadData);
            Assertions.assertEquals(3, uploadData.size());
            Assertions.assertEquals(3, uploadData.stream().filter(BankTransactionsUploadData::isFailed).count());
            Assertions.assertEquals(3, uploadData.stream().filter(bt -> !bt.getFailureReason().isEmpty()).count());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testImportMissingFields() {
        String csvContent =
            """
            BankAccountId,ExternalTransactionId,TransactionAmount,TransactionCurrency,TransactionDate,PayerName,ReferenceNumber,TransactionType,PaymentType
            ,"EXT-9001",,,,"Mia Allen","INV-000071","Wire","Deposit"
            ,"EXT-9002",,,,"Mia Allen","Payment for services","ACH","Deposit"
            ,"EXT-9003",,,,"Layla James","","PayPal","Deposit"
            """;
        InputStream inputStream = new ByteArrayInputStream(csvContent.getBytes());
        try {
            List<BankTransactionsUploadData> uploadData = bankTransactionCsvHandler.getBankTransactionsUploadData(inputStream);
            Assertions.assertNotNull(uploadData);
            Assertions.assertEquals(3, uploadData.size());
            Assertions.assertEquals(3, uploadData.stream().filter(BankTransactionsUploadData::isFailed).count());
            Assertions.assertEquals(3, uploadData.stream().filter(bt -> !bt.getFailureReason().isEmpty()).count());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testImportMissingHeaders() {
        String csvContent =
            """
            BankAccountId,ExternalTransactionId,TransactionAmount,TransactionCurrency,TransactionDate,PayerName,ReferenceNumber
            ,"EXT-9001",,,,"Mia Allen","INV-000071"
            ,"EXT-9002",,,,"Mia Allen","Payment for services"
            ,"EXT-9003",,,,"Layla James","","PayPal"
            """;
        InputStream inputStream = new ByteArrayInputStream(csvContent.getBytes());
        Assertions.assertThrows(IllegalArgumentException.class, () -> bankTransactionCsvHandler.getBankTransactionsUploadData(inputStream));
    }

    @Test
    void testUnsupportedPaymentType() {
        String csvContent =
            """
            BankAccountId,ExternalTransactionId,TransactionAmount,TransactionCurrency,TransactionDate,PayerName,ReferenceNumber,TransactionType,PaymentType
            "<addPaymentBankAccountId>","EXT-9001","2400","USD","11/15/2024 10:00:00","Mia Allen","INV-000071","Wire","Depositor"
            "<addPaymentBankAccountId>","EXT-9002","1300","USD","10/15/2024 10:00:00","Mia Allen","Payment for services","ACH","Depositor"
            "<addPaymentBankAccountId>","EXT-9003","2700","USD","10/10/2024 10:00:00","Layla James","","PayPal","Depositor"
            """;
        InputStream inputStream = new ByteArrayInputStream(csvContent.getBytes());
        try {
            List<BankTransactionsUploadData> uploadData = bankTransactionCsvHandler.getBankTransactionsUploadData(inputStream);
            Assertions.assertNotNull(uploadData);
            Assertions.assertEquals(3, uploadData.size());
            Assertions.assertEquals(3, uploadData.stream().filter(BankTransactionsUploadData::isFailed).count());
            Assertions.assertEquals(3, uploadData.stream().filter(bt -> !bt.getFailureReason().isEmpty()).count());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testBlankPaymentType() {
        String csvContent =
            """
            BankAccountId,ExternalTransactionId,TransactionAmount,TransactionCurrency,TransactionDate,PayerName,ReferenceNumber,TransactionType,PaymentType
            "<addPaymentBankAccountId>","EXT-9001","2400","USD","11/15/2024 10:00:00","Mia Allen","INV-000071","Wire",
            "<addPaymentBankAccountId>","EXT-9002","1300","USD","10/15/2024 10:00:00","Mia Allen","Payment for services","ACH",
            "<addPaymentBankAccountId>","EXT-9003","2700","USD","10/10/2024 10:00:00","Layla James","","PayPal",
            """;
        InputStream inputStream = new ByteArrayInputStream(csvContent.getBytes());
        try {
            List<BankTransactionsUploadData> uploadData = bankTransactionCsvHandler.getBankTransactionsUploadData(inputStream);
            Assertions.assertNotNull(uploadData);
            Assertions.assertEquals(3, uploadData.size());
            Assertions.assertEquals(0, uploadData.stream().filter(BankTransactionsUploadData::isFailed).count());
            Assertions.assertEquals(0, uploadData.stream().filter(bt -> !bt.getFailureReason().isEmpty()).count());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testInactiveBankAccount() {
        String csvContent =
            """
            BankAccountId,ExternalTransactionId,TransactionAmount,TransactionCurrency,TransactionDate,PayerName,ReferenceNumber,TransactionType,PaymentType
            "<addPaymentBankAccountId>","EXT-9001","2400","USD","11/15/2024 10:00:00","Mia Allen","INV-000071","Wire","Depositor"
            "<addPaymentBankAccountId>","EXT-9002","1300","USD","10/15/2024 10:00:00","Mia Allen","Payment for services","ACH","Depositor"
            "<addPaymentBankAccountId>","EXT-9003","2700","USD","10/10/2024 10:00:00","Layla James","","PayPal","Depositor"
            """;
        InputStream inputStream = new ByteArrayInputStream(csvContent.getBytes());
        when(paymentBankAccount.getStatus()).thenReturn(PaymentBankAccountStatus.DEPRECATED);
        try {
            List<BankTransactionsUploadData> uploadData = bankTransactionCsvHandler.getBankTransactionsUploadData(inputStream);
            Assertions.assertNotNull(uploadData);
            Assertions.assertEquals(3, uploadData.size());
            Assertions.assertEquals(3, uploadData.stream().filter(BankTransactionsUploadData::isFailed).count());
            Assertions.assertEquals(3, uploadData.stream().filter(bt -> !bt.getFailureReason().isEmpty()).count());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
