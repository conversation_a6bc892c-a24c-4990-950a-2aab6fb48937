package com.subskribe.billy.smartapproval.service;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowUtils;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

public class SmartApprovalEvaluatorTest {

    private static final String DISCOUNT_ID_1 = "DISC-J9TG62X";
    private static final String DISCOUNT_1_HASH = "7d88232f7a71a3f10fa9190afc5e892e";
    private static final String DISCOUNT_ID_2 = "DISC-MM8JG2J";
    private static final String DISCOUNT_2_HASH = "d2771cc2de1cfeaf40056e1454daca98";
    private static final String RESELLER_ACCOUNT_ID_1 = "ACCT-E3ZJ8HX";
    private static final String RESELLER_ACCOUNT_ID_2 = "ACCT-FHNKE9G";
    private static final String PREDEFINED_TERM_TEMPLATE_ID = "6f44596f-7e67-4682-a740-ef7d267593d7";
    private static final String PREDEFINED_TERM_CONTENT_HASH_1 = "b0b2550144d90b6666f1c9c17fcb88670637d7edbbb1e7387ed9ccf960fdaea2";
    private static final String PREDEFINED_TERM_CONTENT_HASH_2 = "c5266612a0d80ed0e8a5114d40330cb0fbffbaf8ec1820f7cbb9b741d139d7bd";
    private static final String PREDEFINED_TERM_NAME_1 = "Predefined Terms - 1";
    private static final String PREDEFINED_TERM_NAME_2 = "Predefined Terms - 2";

    @Test
    void testCurrentAndPreviousConditionDataNull() {
        assertTrue(SmartApprovalEvaluator.areVariablesEqual(null, null, ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE));
    }

    @Test
    void testOnlyCurrentConditionDataNull() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE, new BigDecimal("144"));

        assertFalse(
            SmartApprovalEvaluator.areVariablesEqual(null, previousApprovalAttributesData, ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE)
        );
    }

    @Test
    void testBigDecimalEqualityIgnoringScale() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE, new BigDecimal("144"));
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE, new BigDecimal("144.000"));

        assertTrue(
            SmartApprovalEvaluator.areVariablesEqual(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE
            )
        );
    }

    @Test
    void testBigDecimalInequalityIgnoringScale() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE, new BigDecimal("144.01"));
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE, new BigDecimal("144.000"));

        assertFalse(
            SmartApprovalEvaluator.areVariablesEqual(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_LINE_CONDITION_ENTRY_ARR_VARIABLE
            )
        );
    }

    @Test
    void testStringEquality() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_ORDER_TYPE_VARIABLE, "New");
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_ORDER_TYPE_VARIABLE, "New");

        assertTrue(
            SmartApprovalEvaluator.areVariablesEqual(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_CONDITION_ORDER_TYPE_VARIABLE
            )
        );
    }

    @Test
    void testStringInequality() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_ORDER_TYPE_VARIABLE, "New");
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_ORDER_TYPE_VARIABLE, "None");

        assertFalse(
            SmartApprovalEvaluator.areVariablesEqual(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_CONDITION_ORDER_TYPE_VARIABLE
            )
        );
    }

    @Test
    void testBooleanEquality() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, true);
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, true);

        assertTrue(
            SmartApprovalEvaluator.areVariablesEqual(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER
            )
        );
    }

    @Test
    void testBooleanInequality() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, true);
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, false);

        assertFalse(
            SmartApprovalEvaluator.areVariablesEqual(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER
            )
        );
    }

    @Test
    void testLongAndBigDecimalEquality() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_AMOUNT_VARIABLE, 144L);
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_AMOUNT_VARIABLE, new BigDecimal("144.0"));

        assertTrue(
            SmartApprovalEvaluator.areVariablesEqual(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_LINE_CONDITION_AMOUNT_VARIABLE
            )
        );
    }

    @Test
    void testDoubleAndBigDecimalEquality() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_AMOUNT_VARIABLE, Double.valueOf("144.0"));
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_AMOUNT_VARIABLE, new BigDecimal("144.0"));

        assertTrue(
            SmartApprovalEvaluator.areVariablesEqual(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_LINE_CONDITION_AMOUNT_VARIABLE
            )
        );
    }

    // Test cases for predefined discount

    @Test
    void bothSnapshotsNullShouldReturnFalse() {
        assertFalse(SmartApprovalEvaluator.predefinedDiscountsChanged(null, null));
    }

    @Test
    void oneSnapshotNullShouldReturnTrue() {
        Map<String, Object> nonNullSnapshot = new HashMap<>();
        nonNullSnapshot.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, Map.of(DISCOUNT_ID_1, DISCOUNT_1_HASH));

        assertTrue(SmartApprovalEvaluator.predefinedDiscountsChanged(nonNullSnapshot, null));
        assertTrue(SmartApprovalEvaluator.predefinedDiscountsChanged(null, nonNullSnapshot));
    }

    @Test
    void predefinedDiscountKeyAddedOrRemovedShouldReturnTrue() {
        Map<String, Object> mapWithKey = new HashMap<>();
        mapWithKey.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, Map.of(DISCOUNT_ID_1, DISCOUNT_1_HASH));

        Map<String, Object> mapWithoutKey = new HashMap<>();

        assertTrue(SmartApprovalEvaluator.predefinedDiscountsChanged(mapWithKey, mapWithoutKey));
        assertTrue(SmartApprovalEvaluator.predefinedDiscountsChanged(mapWithoutKey, mapWithKey));
    }

    @Test
    void bothPredefinedDiscountsNullShouldReturnFalse() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, null);

        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, null);

        assertFalse(SmartApprovalEvaluator.predefinedDiscountsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
    }

    @Test
    void onePredefinedDiscountNullShouldReturnTrue() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, null);

        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, Map.of(DISCOUNT_ID_1, DISCOUNT_1_HASH));

        assertTrue(SmartApprovalEvaluator.predefinedDiscountsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
        assertTrue(SmartApprovalEvaluator.predefinedDiscountsChanged(previousApprovalAttributesData, currentApprovalAttributesData));
    }

    @Test
    void predefinedDiscountsNotMapsShouldReturnTrue() {
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, StringUtils.EMPTY);

        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, Map.of(DISCOUNT_ID_1, DISCOUNT_1_HASH));

        assertTrue(SmartApprovalEvaluator.predefinedDiscountsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
        assertTrue(SmartApprovalEvaluator.predefinedDiscountsChanged(previousApprovalAttributesData, currentApprovalAttributesData));
    }

    @Test
    void predefinedDiscountsDifferInNumberBetweenSubmissionsShouldReturnTrue() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, Map.of(DISCOUNT_ID_1, DISCOUNT_1_HASH));

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(
            ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE,
            Map.of(DISCOUNT_ID_1, DISCOUNT_1_HASH, DISCOUNT_ID_2, DISCOUNT_2_HASH)
        );

        assertTrue(SmartApprovalEvaluator.predefinedDiscountsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
    }

    @Test
    void samePredefinedDiscountChangedInBetweenSubmissionsShouldReturnTrue() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, Map.of(DISCOUNT_ID_1, DISCOUNT_1_HASH));

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE, Map.of(DISCOUNT_ID_1, DISCOUNT_2_HASH));

        assertTrue(SmartApprovalEvaluator.predefinedDiscountsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
    }

    @Test
    void predefinedDiscountsSameValuesShouldReturnFalse() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(
            ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE,
            Map.of(DISCOUNT_ID_1, DISCOUNT_1_HASH, DISCOUNT_ID_2, DISCOUNT_2_HASH)
        );

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(
            ApprovalFlowUtils.ORDER_CONDITION_PREDEFINED_DISCOUNT_VARIABLE,
            Map.of(DISCOUNT_ID_1, DISCOUNT_1_HASH, DISCOUNT_ID_2, DISCOUNT_2_HASH)
        );

        assertFalse(SmartApprovalEvaluator.predefinedDiscountsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
    }

    // Test cases for reseller account detail
    @Test
    void flagChangeShortCircuitsIdCheck() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, true);

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, false);
        assertTrue(
            SmartApprovalEvaluator.hasResellerDetailChanged(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER
            )
        );
        assertTrue(
            SmartApprovalEvaluator.hasResellerDetailChanged(
                previousApprovalAttributesData,
                currentApprovalAttributesData,
                ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER
            )
        );
    }

    @Test
    void resellerAccountIdChangeDetectedWhenFlagUnchanged() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, true);
        previousApprovalAttributesData.put(ApprovalFlowUtils.RESELLER_ACCOUNT_ID, RESELLER_ACCOUNT_ID_1);

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, true);
        previousApprovalAttributesData.put(ApprovalFlowUtils.RESELLER_ACCOUNT_ID, RESELLER_ACCOUNT_ID_2);
        assertTrue(
            SmartApprovalEvaluator.hasResellerDetailChanged(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER
            )
        );
        assertTrue(
            SmartApprovalEvaluator.hasResellerDetailChanged(
                previousApprovalAttributesData,
                currentApprovalAttributesData,
                ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER
            )
        );
    }

    @Test
    void noResellerChangesDetected() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, true);
        previousApprovalAttributesData.put(ApprovalFlowUtils.RESELLER_ACCOUNT_ID, RESELLER_ACCOUNT_ID_1);

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, true);
        currentApprovalAttributesData.put(ApprovalFlowUtils.RESELLER_ACCOUNT_ID, RESELLER_ACCOUNT_ID_1);
        assertFalse(
            SmartApprovalEvaluator.hasResellerDetailChanged(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER
            )
        );
    }

    @Test
    void missingResellerVariableInCurrent() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER, true);
        assertTrue(
            SmartApprovalEvaluator.hasResellerDetailChanged(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_CONDITION_IS_RESELLER
            )
        );
    }

    // Test cases for order line custom pricing
    @Test
    void returnsFalseWhenCustomPricingFlagAndListPriceAreUnchanged() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING, true);
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, new BigDecimal("5.00"));

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING, true);
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, new BigDecimal("5.00"));

        assertFalse(
            SmartApprovalEvaluator.customPricingChangedOnOrderLine(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING
            )
        );
    }

    @Test
    void returnsTrueWhenCustomPricingFlagChanged() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING, false);
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, new BigDecimal("10.00"));

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING, true);
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, new BigDecimal("10.00"));

        assertTrue(
            SmartApprovalEvaluator.customPricingChangedOnOrderLine(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING
            )
        );
    }

    @Test
    void returnsTrueWhenListUnitPriceChanged() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING, true);
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, new BigDecimal("10.00"));

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING, true);
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, new BigDecimal("5.00"));

        assertTrue(
            SmartApprovalEvaluator.customPricingChangedOnOrderLine(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING
            )
        );
    }

    @Test
    void returnsTrueWhenCustomPricingFlagMissingInCurrent() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING, true);
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, new BigDecimal("10.00"));

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, new BigDecimal("10.00"));

        assertTrue(
            SmartApprovalEvaluator.customPricingChangedOnOrderLine(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING
            )
        );
    }

    @Test
    void returnsTrueWhenCustomPricingFlagMissingInPrevious() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, new BigDecimal("10.00"));

        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING, true);
        currentApprovalAttributesData.put(ApprovalFlowUtils.ORDER_LINE_CONDITION_LIST_UNIT_PRICE, new BigDecimal("10.00"));

        assertTrue(
            SmartApprovalEvaluator.customPricingChangedOnOrderLine(
                currentApprovalAttributesData,
                previousApprovalAttributesData,
                ApprovalFlowUtils.ORDER_LINE_CONDITION_IS_CUSTOM_PRICING
            )
        );
    }

    // Test cases for order predefined terms
    @Test
    void returnsFalseWhenBothMapsNull() {
        assertFalse(SmartApprovalEvaluator.predefinedTermsChanged(null, null));
    }

    @Test
    void returnsTrueWhenOneMapNull() {
        assertTrue(SmartApprovalEvaluator.predefinedTermsChanged(null, new HashMap<>()));
        assertTrue(SmartApprovalEvaluator.predefinedTermsChanged(new HashMap<>(), null));
    }

    @Test
    void returnsTrueWhenKeyPresenceChanged() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.PREDEFINED_TERMS_CONTENT_ON_ORDER, new HashMap<>());
        // currentApprovalAttributesData doesn't have the key - PREDEFINED_TERMS_CONTENT_ON_ORDER
        assertTrue(SmartApprovalEvaluator.predefinedTermsChanged(previousApprovalAttributesData, currentApprovalAttributesData));
        assertTrue(SmartApprovalEvaluator.predefinedTermsChanged(currentApprovalAttributesData, previousApprovalAttributesData));

        previousApprovalAttributesData.clear();
        currentApprovalAttributesData.clear();
        previousApprovalAttributesData.put(ApprovalFlowUtils.PREDEFINED_TERMS_NAME_ON_ORDER, new HashMap<>());
        // currentApprovalAttributesData doesn't have the key - PREDEFINED_TERMS_NAME_ON_ORDER
        assertTrue(SmartApprovalEvaluator.predefinedTermsChanged(previousApprovalAttributesData, currentApprovalAttributesData));
        assertTrue(SmartApprovalEvaluator.predefinedTermsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
    }

    @Test
    void returnsFalseWhenBothKeysMissing() {
        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        assertFalse(SmartApprovalEvaluator.predefinedTermsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
    }

    @Test
    void returnsTrueWhenTermContentChanged() {
        Map<String, String> previousPredefinedTermContent = new HashMap<>();
        previousPredefinedTermContent.put(PREDEFINED_TERM_TEMPLATE_ID, PREDEFINED_TERM_CONTENT_HASH_1);
        Map<String, String> currentPredefinedTermContent = new HashMap<>();
        currentPredefinedTermContent.put(PREDEFINED_TERM_TEMPLATE_ID, PREDEFINED_TERM_CONTENT_HASH_2);

        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.PREDEFINED_TERMS_CONTENT_ON_ORDER, previousPredefinedTermContent);
        currentApprovalAttributesData.put(ApprovalFlowUtils.PREDEFINED_TERMS_CONTENT_ON_ORDER, currentPredefinedTermContent);

        assertTrue(SmartApprovalEvaluator.predefinedTermsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
    }

    @Test
    void returnsTrueWhenTermNameChanged() {
        Map<String, String> previousPredefinedTermName = new HashMap<>();
        previousPredefinedTermName.put(PREDEFINED_TERM_TEMPLATE_ID, PREDEFINED_TERM_NAME_1);
        Map<String, String> currentPredefinedTermName = new HashMap<>();
        currentPredefinedTermName.put(PREDEFINED_TERM_TEMPLATE_ID, PREDEFINED_TERM_NAME_2);

        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.PREDEFINED_TERMS_NAME_ON_ORDER, previousPredefinedTermName);
        currentApprovalAttributesData.put(ApprovalFlowUtils.PREDEFINED_TERMS_NAME_ON_ORDER, currentPredefinedTermName);

        assertTrue(SmartApprovalEvaluator.predefinedTermsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
    }

    @Test
    void returnsFalseWhenBothContentAndNameEqual() {
        Map<String, String> predefinedTermContent = new HashMap<>();
        predefinedTermContent.put(PREDEFINED_TERM_TEMPLATE_ID, PREDEFINED_TERM_CONTENT_HASH_1);
        Map<String, String> predefinedTermName = new HashMap<>();
        predefinedTermName.put(PREDEFINED_TERM_TEMPLATE_ID, PREDEFINED_TERM_NAME_1);

        Map<String, Object> previousApprovalAttributesData = new HashMap<>();
        Map<String, Object> currentApprovalAttributesData = new HashMap<>();
        previousApprovalAttributesData.put(ApprovalFlowUtils.PREDEFINED_TERMS_CONTENT_ON_ORDER, new HashMap<>(predefinedTermContent));
        currentApprovalAttributesData.put(ApprovalFlowUtils.PREDEFINED_TERMS_CONTENT_ON_ORDER, new HashMap<>(predefinedTermContent));
        previousApprovalAttributesData.put(ApprovalFlowUtils.PREDEFINED_TERMS_NAME_ON_ORDER, new HashMap<>(predefinedTermName));
        currentApprovalAttributesData.put(ApprovalFlowUtils.PREDEFINED_TERMS_NAME_ON_ORDER, new HashMap<>(predefinedTermName));

        assertFalse(SmartApprovalEvaluator.predefinedTermsChanged(currentApprovalAttributesData, previousApprovalAttributesData));
    }
}
