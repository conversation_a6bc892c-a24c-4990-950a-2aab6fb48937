package com.subskribe.billy.idempotency.service;

import static com.subskribe.billy.idempotency.service.IdempotencyService.IdempotencyResult;
import static com.subskribe.billy.idempotency.service.IdempotencyService.PARTITION_KEY_FORMAT;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.exception.ServiceUnavailableException;
import com.subskribe.billy.idempotency.db.IdempotencyDAO;
import com.subskribe.billy.idempotency.model.IdempotencyConfiguration;
import com.subskribe.billy.idempotency.model.IdempotencyRecord;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class IdempotencyServiceTest {

    private static final long TEST_BACKOFF_MILLIS = 1000;
    private static final Duration TEST_RECORD_TTL_HOURS = Duration.ofHours(3);
    private static final String TEST_TENANT_ID = "subbu_the_gama";

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private IdempotencyConfiguration idempotencyConfiguration;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private IdempotencyDAO idempotencyDao;

    private IdempotencyService service;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(billyConfiguration.getIdempotencyConfiguration()).thenReturn(idempotencyConfiguration);
        when(idempotencyConfiguration.getDefaultBackOffTimeMs()).thenReturn(TEST_BACKOFF_MILLIS);
        when(idempotencyConfiguration.getRecordTTLInHours()).thenReturn(TEST_RECORD_TTL_HOURS);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TEST_TENANT_ID);

        service = new IdempotencyService(billyConfiguration, idempotencyDao, tenantIdProvider);
    }

    @Test
    public void whenEmptyIdempotencyKeyIsUsed_thenIdempotencyTokenContainsEmptyKey() {
        List<IdempotencyToken> exactOnce = new ArrayList<>();
        service
            .<String>begin(Optional.empty())
            .executeOnce(token -> {
                exactOnce.add(token);
                return IdempotencyResult.of("done", "complete");
            })
            .thereAfter(token -> "already_done")
            .end();
        Assertions.assertThat(exactOnce).hasSize(1);
        var token = exactOnce.get(0);
        Assertions.assertThat(token.getIdempotencyKey()).isEmpty();

        // make sure none of the DAO methods are called
        verify(idempotencyDao, never()).getIdempotencyRecord(anyString());
        verify(idempotencyDao, never()).putIfAbsent(any(IdempotencyRecord.class));
        verify(idempotencyDao, never()).clobberIdempotencyRecord(any(IdempotencyRecord.class));
    }

    @Test
    public void whenStringEmptyIdempotencyKeyIsUsed_thenUserErrorIsRaised() {
        Assertions.assertThatThrownBy(() ->
            service
                .<String>begin(Optional.of("  "))
                .executeOnce(token -> IdempotencyResult.of("done", "complete"))
                .thereAfter(token -> "already_done")
                .end()
        ).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void whenIdempotencyKeyIsNotCompleted_thenExecuteOncePathIsCalled() {
        String idempotencyKey = UUID.randomUUID().toString();
        String expectedPartitionKey = String.format(PARTITION_KEY_FORMAT, TEST_TENANT_ID, idempotencyKey);
        List<IdempotencyToken> exactOnce = new ArrayList<>();
        List<Boolean> completedExactOnce = new ArrayList<>();

        when(idempotencyDao.getIdempotencyRecord(expectedPartitionKey)).thenReturn(Optional.empty());

        service
            .<String>begin(Optional.of(idempotencyKey))
            .executeOnce(token -> {
                exactOnce.add(token);
                completedExactOnce.add(token.isCompleted());
                return IdempotencyResult.of("done", "complete");
            })
            .thereAfter(token -> "already_done")
            .end();

        Assertions.assertThat(exactOnce).hasSize(1);
        Assertions.assertThat(completedExactOnce).hasSize(1);
        var token = exactOnce.get(0);
        Assertions.assertThat(token.getIdempotencyKey()).isPresent();
        Assertions.assertThat(token.getIdempotencyKey().get()).isEqualTo(idempotencyKey);

        // token should not be completed
        Assertions.assertThat(completedExactOnce.get(0)).isFalse();
        Assertions.assertThat(token.getIdempotencyRecord().getBackOffInterval()).isEqualByComparingTo(Duration.ofMillis(TEST_BACKOFF_MILLIS));

        verify(idempotencyDao).putIfAbsent(token.getIdempotencyRecord());
        verify(idempotencyDao).clobberIdempotencyRecord(token.getIdempotencyRecord());
    }

    @Test
    public void whenIdempotencyTokenIsCompleted_thenOnlyThereAfterIsCalled() {
        String idempotencyKey = UUID.randomUUID().toString();
        String expectedPartitionKey = String.format(PARTITION_KEY_FORMAT, TEST_TENANT_ID, idempotencyKey);
        List<IdempotencyToken> exactOnce = new ArrayList<>();
        List<IdempotencyToken> thereAfter = new ArrayList<>();

        var idempotencyRecord = IdempotencyRecord.builder()
            .partitionKey(expectedPartitionKey)
            .tenantId(tenantIdProvider.provideTenantIdString())
            .idempotencyKey(idempotencyKey)
            .backOffInterval(Duration.ofMillis(TEST_BACKOFF_MILLIS))
            // created at in the past by 2 hours
            .createdAt(Instant.now().minus(Duration.ofHours(2)))
            .expiresAt(Instant.now().plus(TEST_RECORD_TTL_HOURS).getEpochSecond())
            // created at in the past by 1 hour
            .completedAt(Instant.now().minus(Duration.ofHours(1)))
            .completionReference("done!")
            .build();

        when(idempotencyDao.getIdempotencyRecord(expectedPartitionKey)).thenReturn(Optional.of(idempotencyRecord));

        service
            .<String>begin(Optional.of(idempotencyKey))
            .executeOnce(token -> {
                exactOnce.add(token);
                return IdempotencyResult.of("done", "complete");
            })
            .thereAfter(token -> {
                thereAfter.add(token);
                return "already_done";
            })
            .end();

        Assertions.assertThat(exactOnce).hasSize(0);
        Assertions.assertThat(thereAfter).hasSize(1);
        var token = thereAfter.get(0);
        Assertions.assertThat(token.isCompleted()).isTrue();

        verify(idempotencyDao, never()).putIfAbsent(any(IdempotencyRecord.class));
        verify(idempotencyDao, never()).clobberIdempotencyRecord(any(IdempotencyRecord.class));
    }

    @Test
    public void whenPartialFailureAndInsideBackOffPeriod_thenServiceUnavailableExceptionIsThrown() {
        String idempotencyKey = UUID.randomUUID().toString();
        String expectedPartitionKey = String.format(PARTITION_KEY_FORMAT, TEST_TENANT_ID, idempotencyKey);

        var idempotencyRecord = IdempotencyRecord.builder()
            .partitionKey(expectedPartitionKey)
            .tenantId(tenantIdProvider.provideTenantIdString())
            .idempotencyKey(idempotencyKey)
            // back off specifically set to a really long time
            .backOffInterval(Duration.ofMinutes(5))
            .createdAt(Instant.now())
            .expiresAt(Instant.now().plus(TEST_RECORD_TTL_HOURS).getEpochSecond())
            .build();

        when(idempotencyDao.getIdempotencyRecord(expectedPartitionKey)).thenReturn(Optional.of(idempotencyRecord));

        Assertions.assertThatThrownBy(() ->
            service
                .<String>begin(Optional.of(idempotencyKey))
                .executeOnce(token -> IdempotencyResult.of("done", "complete"))
                .thereAfter(token -> "already_done")
                .end()
        ).isInstanceOf(ServiceUnavailableException.class);

        verify(idempotencyDao, never()).putIfAbsent(any(IdempotencyRecord.class));
        verify(idempotencyDao, never()).clobberIdempotencyRecord(any(IdempotencyRecord.class));
    }

    @Test
    public void whenPartialFailureIsOutsideBackOffPeriod_thenIdempotentOperationIsExecutedAgain() {
        String idempotencyKey = UUID.randomUUID().toString();
        String expectedPartitionKey = String.format(PARTITION_KEY_FORMAT, TEST_TENANT_ID, idempotencyKey);
        List<IdempotencyToken> exactOnce = new ArrayList<>();
        List<Boolean> completedExactOnce = new ArrayList<>();

        var idempotencyRecord = IdempotencyRecord.builder()
            .partitionKey(expectedPartitionKey)
            .tenantId(tenantIdProvider.provideTenantIdString())
            .idempotencyKey(idempotencyKey)
            // back off set to 5 minutes
            .backOffInterval(Duration.ofMinutes(5))
            // created at pushed back by 10 minutes
            .createdAt(Instant.now().minus(Duration.ofMinutes(10)))
            .expiresAt(Instant.now().plus(TEST_RECORD_TTL_HOURS).getEpochSecond())
            .build();

        when(idempotencyDao.getIdempotencyRecord(expectedPartitionKey)).thenReturn(Optional.of(idempotencyRecord));

        service
            .<String>begin(Optional.of(idempotencyKey))
            .executeOnce(token -> {
                exactOnce.add(token);
                completedExactOnce.add(token.isCompleted());
                return IdempotencyResult.of("done", "complete");
            })
            .thereAfter(token -> "already_done")
            .end();

        Assertions.assertThat(exactOnce).hasSize(1);
        var token = exactOnce.get(0);
        Assertions.assertThat(completedExactOnce).hasSize(1);
        Assertions.assertThat(completedExactOnce.get(0)).isFalse();

        verify(idempotencyDao).clobberIdempotencyRecord(token.getIdempotencyRecord());
    }
}
