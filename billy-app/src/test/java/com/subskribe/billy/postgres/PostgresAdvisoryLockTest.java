package com.subskribe.billy.postgres;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import org.assertj.core.api.Assertions;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class PostgresAdvisoryLockTest {

    @Mock
    DSLContext dslContext;

    @Mock
    Result<Record> resultRecord;

    @Mock
    Record mockRecord;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void whenLockCanBeAcquire_thenCorrectResultsAreReturned() {
        when(dslContext.fetch(any(String.class))).thenReturn(resultRecord);
        when(resultRecord.get(0)).thenReturn(mockRecord);
        when(mockRecord.get(0)).thenReturn(Boolean.TRUE);
        Optional<Long> lockHash = PostgresAdvisoryLock.tryAcquireLock(dslContext, "andy_dufresne");
        Assertions.assertThat(lockHash).isNotEmpty();
        verify(dslContext, atLeastOnce()).fetch(any(String.class));
    }

    @Test
    public void whenLockCannotBeAcquire_thenReturnOptionalIsEmpty() {
        when(dslContext.fetch(any(String.class))).thenReturn(resultRecord);
        when(resultRecord.get(0)).thenReturn(mockRecord);
        when(mockRecord.get(0)).thenReturn(Boolean.FALSE);
        Optional<Long> lockHash = PostgresAdvisoryLock.tryAcquireLock(dslContext, "django_unchained");
        Assertions.assertThat(lockHash).isEmpty();
        verify(dslContext, atLeastOnce()).fetch(any(String.class));
    }
}
