package com.subskribe.billy.search.service;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.search.model.BillySearchEntry;
import com.subskribe.billy.search.model.ImmutableBillySearchEntry;
import com.subskribe.billy.search.model.SearchUpdateFilterResult;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class SearchUpdateFilterTest {

    private final ObjectMapper objectMapper = JacksonProvider.defaultMapper();

    private final SearchUpdateFilter searchUpdateFilter = new SearchUpdateFilter();

    @Test
    void testFiltering() throws Exception {
        BillySearchEntry deleteEntryNotSoftDeleted = createBillySearchEntry("DELETE", false, false);
        BillySearchEntry deleteEntrySoftDeleted = createBillySearchEntry("DELETE", true, true);

        BillySearchEntry updateEntrySoftDeletedBeforeAndAfter = createBillySearchEntry("UPDATE", true, true);
        BillySearchEntry updateEntrySoftDeletedBefore = createBillySearchEntry("UPDATE", true, false);
        BillySearchEntry updateEntrySoftDeletedNow = createBillySearchEntry("UPDATE", false, true);

        BillySearchEntry insertEntry = createBillySearchEntry("INSERT", false, false);

        SearchUpdateFilterResult searchUpdateFilterResult = searchUpdateFilter.filterSearchUpdateEntries(
            List.of(
                deleteEntryNotSoftDeleted,
                deleteEntrySoftDeleted,
                updateEntrySoftDeletedBeforeAndAfter,
                updateEntrySoftDeletedBefore,
                updateEntrySoftDeletedNow,
                insertEntry
            )
        );

        assertThat(searchUpdateFilterResult.getRecordsToProcess()).containsExactly(
            deleteEntryNotSoftDeleted,
            updateEntrySoftDeletedBefore,
            updateEntrySoftDeletedNow,
            insertEntry
        );

        assertThat(searchUpdateFilterResult.getRecordsToSkip()).containsExactlyInAnyOrder(
            deleteEntrySoftDeleted,
            updateEntrySoftDeletedBeforeAndAfter
        );
    }

    @Test
    void testRecordWithNoSoftDelete() throws Exception {
        UUID id = UUID.randomUUID();
        String oldValues = objectMapper.writeValueAsString(Map.of("id", id.toString(), "test", "old"));
        String newValues = objectMapper.writeValueAsString(Map.of("id", id.toString(), "test", "new"));
        BillySearchEntry recordWithNoSoftDelete = ImmutableBillySearchEntry.builder()
            .id(UUID.randomUUID())
            .operationType("UPDATE")
            .oldValues(oldValues)
            .newValues(newValues)
            .tenantId(UUID.randomUUID().toString())
            .instanceId(id.toString())
            .instanceType("account")
            .transactionId(1L)
            .retryCount(0)
            .status("PENDING")
            .createdOn(Instant.now())
            .build();

        SearchUpdateFilterResult searchUpdateFilterResult = searchUpdateFilter.filterSearchUpdateEntries(List.of(recordWithNoSoftDelete));

        assertThat(searchUpdateFilterResult.getRecordsToProcess()).containsExactly(recordWithNoSoftDelete);
        assertThat(searchUpdateFilterResult.getRecordsToSkip()).isEmpty();
    }

    @Test
    void testRecordWithNoValues() {
        UUID id = UUID.randomUUID();
        BillySearchEntry recordWithNoValues = ImmutableBillySearchEntry.builder()
            .id(UUID.randomUUID())
            .operationType("UPDATE")
            .tenantId(UUID.randomUUID().toString())
            .instanceId(id.toString())
            .instanceType("account")
            .transactionId(1L)
            .retryCount(0)
            .status("PENDING")
            .createdOn(Instant.now())
            .build();

        SearchUpdateFilterResult searchUpdateFilterResult = searchUpdateFilter.filterSearchUpdateEntries(List.of(recordWithNoValues));

        assertThat(searchUpdateFilterResult.getRecordsToProcess()).containsExactly(recordWithNoValues);
        assertThat(searchUpdateFilterResult.getRecordsToSkip()).isEmpty();
    }

    private BillySearchEntry createBillySearchEntry(String operationType, boolean isOldDeleted, boolean isNewDeleted) throws JsonProcessingException {
        UUID id = UUID.randomUUID();
        String oldValues = "INSERT".equals(operationType) ? null : createValuesJson(id.toString(), isOldDeleted);
        String newValues = "DELETE".equals(operationType) ? null : createValuesJson(id.toString(), isNewDeleted);
        return ImmutableBillySearchEntry.builder()
            .id(id)
            .operationType(operationType)
            .oldValues(oldValues)
            .newValues(newValues)
            .tenantId(UUID.randomUUID().toString())
            .instanceId(id.toString())
            .instanceType("account")
            .transactionId(1L)
            .retryCount(0)
            .status("PENDING")
            .createdOn(Instant.now())
            .build();
    }

    private String createValuesJson(String id, boolean isDeleted) throws JsonProcessingException {
        return objectMapper.writeValueAsString(Map.of("id", id, "is_deleted", isDeleted));
    }
}
