package com.subskribe.billy.search.reindex.db;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.Instant;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class SearchReindexQueueQueryBuilderTest {

    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final String INDEX_NAME = TENANT_ID + "-" + Instant.now().getEpochSecond();

    @Test
    void testTableWithValues() {
        String query = SearchReindexQueueQueryBuilder.with(TENANT_ID, "account", INDEX_NAME).build();
        String expectedQuery =
            "INSERT INTO search_reindex_queue (tenant_id, instance_type, instance_id, operation_type, created_on, old_values, new_values, index_name)\n" +
            "SELECT tenant_id, 'account', id, 'UPDATE', NOW(), null, row_to_json(account), '" +
            INDEX_NAME +
            "' FROM account\n" +
            "WHERE tenant_id = '" +
            TENANT_ID +
            "' AND is_deleted = false";
        assertThat(query).isEqualTo(expectedQuery);
    }

    @Test
    void testTableWithoutValues() {
        String query = SearchReindexQueueQueryBuilder.with(TENANT_ID, "product", INDEX_NAME).build();
        String expectedQuery =
            "INSERT INTO search_reindex_queue (tenant_id, instance_type, instance_id, operation_type, created_on, index_name)\n" +
            "SELECT tenant_id, 'product', id, 'UPDATE', NOW(), '" +
            INDEX_NAME +
            "' FROM product\n" +
            "WHERE tenant_id = '" +
            TENANT_ID +
            "' AND is_deleted = false";
        assertThat(query).isEqualTo(expectedQuery);
    }

    @Test
    void testTableWithoutIsDeleted() {
        String query = SearchReindexQueueQueryBuilder.with(TENANT_ID, "settlement_application", INDEX_NAME).build();
        String expectedQuery =
            "INSERT INTO search_reindex_queue (tenant_id, instance_type, instance_id, operation_type, created_on, index_name)\n" +
            "SELECT tenant_id, 'settlement_application', id, 'UPDATE', NOW(), '" +
            INDEX_NAME +
            "' FROM settlement_application\n" +
            "WHERE tenant_id = '" +
            TENANT_ID +
            "'";
        assertThat(query).isEqualTo(expectedQuery);
    }
}
