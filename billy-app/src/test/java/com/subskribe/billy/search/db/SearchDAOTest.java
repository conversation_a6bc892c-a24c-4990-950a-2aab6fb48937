package com.subskribe.billy.search.db;

import static com.subskribe.billy.jooq.default_schema.tables.BillyGlobalSearch.BILLY_GLOBAL_SEARCH;
import static com.subskribe.billy.search.db.SearchDAO.SUCCESS_STATE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.shared.temporal.DateTimeConverter;
import com.subskribe.billy.test.jooq.MockDSLBuilder;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.UUID;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@AllowNonRlsDataAccess
class SearchDAOTest {

    private SearchDAO searchDAO;
    private DSLContextProvider dslContextProvider;
    private static final Instant NOW = Instant.ofEpochSecond(**********);

    @BeforeEach
    void setUp() {
        dslContextProvider = mock(DSLContextProvider.class);
        Clock clock = Clock.fixed(NOW, ZoneId.of("UTC"));
        searchDAO = new SearchDAO(dslContextProvider, clock);
    }

    @Test
    void testMarkProcessedSearchUpdateRecordAsSuccess() {
        UUID record1 = UUID.randomUUID();
        UUID record2 = UUID.randomUUID();
        LocalDateTime expectedUpdateTime = DateTimeConverter.instantToLocalDateTime(NOW);
        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .update(BILLY_GLOBAL_SEARCH)
                    .set(BILLY_GLOBAL_SEARCH.STATUS, "SUCCESS")
                    .set(BILLY_GLOBAL_SEARCH.UPDATED_ON, expectedUpdateTime)
                    .where(BILLY_GLOBAL_SEARCH.ID.in(List.of(record1, record2)))
            )
            .thenReturn(2)
            .context();

        when(dslContextProvider.get()).thenReturn(dslContext);

        int result = searchDAO.markProcessedSearchUpdateRecordAsSuccess(List.of(record1, record2));

        assertThat(result).isEqualTo(2);
    }

    @Test
    void testDeleteOldSuccessfulRecords() {
        long expectedCutoff = NOW.getEpochSecond() - (30 * 24 * 60 * 60);
        LocalDateTime localDateTime = DateTimeConverter.instantToLocalDateTime(Instant.ofEpochSecond(expectedCutoff));

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .delete(BILLY_GLOBAL_SEARCH)
                    .where(BILLY_GLOBAL_SEARCH.CREATED_ON.le(localDateTime))
                    .and(BILLY_GLOBAL_SEARCH.STATUS.eq(SUCCESS_STATE))
            )
            .thenReturn(10)
            .context();

        when(dslContextProvider.get()).thenReturn(dslContext);

        int result = searchDAO.deleteOldSuccessfulRecords();

        assertThat(result).isEqualTo(10);
    }
}
