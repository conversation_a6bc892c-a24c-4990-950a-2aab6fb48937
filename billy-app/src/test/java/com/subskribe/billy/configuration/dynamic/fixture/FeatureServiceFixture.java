package com.subskribe.billy.configuration.dynamic.fixture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;

public class FeatureServiceFixture {

    // mock the service that enables all flags
    public static FeatureService allEnabled() {
        FeatureService featureService = mock(FeatureService.class);
        when(featureService.isEnabled(any())).thenReturn(true);
        return featureService;
    }

    // mock the service that enables the given flags
    public static FeatureService flagsEnabled(Feature... flags) {
        FeatureService featureService = mock(FeatureService.class);
        for (Feature flag : flags) {
            when(featureService.isEnabled(flag)).thenReturn(true);
        }
        return featureService;
    }
}
