package com.subskribe.billy.configuration.dynamic;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.appconfig.AwsAppConfigurationService;
import com.subskribe.billy.exception.TenantNotFoundException;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.config.TypeSafeDynamicConfigLoader;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

@TestInstance(Lifecycle.PER_CLASS)
class FeatureServiceTest {

    private FeatureService featureService;

    private static final String TENANT_ID_1 = AutoGenerate.getNewId();

    private static final String TENANT_ID_2 = AutoGenerate.getNewId();

    @Mock
    private TenantIdProvider mockTenantIdProvider;

    private Map<String, Boolean> expectedFeatureValues;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);

        BillyConfiguration config = new BillyConfiguration();
        config.setDynamicFeatures(Map.of("feature1", true, "feature2", false, "feature4", false, "feature5", true));

        System.setProperty("localFeatureOverrides", "{feature1: true, feature2: false, feature3: false, feature4: true}");

        AwsAppConfigurationService awsAppConfig = mock(AwsAppConfigurationService.class);
        when(awsAppConfig.getFeatures()).thenReturn(
            Map.of(
                "feature1",
                Map.of("enabled", false),
                "feature2",
                Map.of("enabled", true),
                "feature3",
                Map.of("enabled", true),
                "feature3/" + TENANT_ID_2,
                Map.of("enabled", false),
                "featurewithbadvalue",
                "badvalue"
            )
        );

        featureService = new FeatureService(config, awsAppConfig, mockTenantIdProvider);

        expectedFeatureValues = Map.of(
            "feature1",
            false,
            "feature2",
            true,
            "feature3",
            true,
            "feature3/" + TENANT_ID_2,
            false,
            "feature4",
            true,
            "feature5",
            true,
            "feature6",
            false,
            "featurewithbadvalue",
            false
        );
    }

    @Test
    void testPrecedence() {
        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID_1);
        expectedFeatureValues.forEach((featureName, assertedValue) -> {
            boolean featureValue = featureService.isEnabled(featureName, Optional.empty());
            assertEquals(featureValue, assertedValue, featureName);
        });
    }

    @Test
    void testTenantSpecificConfig() {
        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID_2);
        expectedFeatureValues.forEach((featureName, assertedValue) -> {
            boolean featureValue = featureService.isEnabled(featureName, Optional.empty());
            assertEquals(featureValue, !"feature3".equalsIgnoreCase(featureName) && assertedValue, featureName);
        });
    }

    @Test
    void testTenantSpecificConfigWithTenantIdPassed() {
        expectedFeatureValues.forEach((featureName, assertedValue) -> {
            boolean featureValue = featureService.isEnabled(featureName, Optional.of(TENANT_ID_2));
            assertEquals(featureValue, !"feature3".equalsIgnoreCase(featureName) && assertedValue, featureName);
        });
    }

    @Test
    void testBulkFetch() {
        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID_1);
        featureService
            .getAll()
            .forEach((featureName, featureValue) -> assertEquals(expectedFeatureValues.get(featureName), featureValue, featureName));
    }

    @Test
    void testBulkFetchIncludeTenantIdSpecific() {
        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID_2);
        featureService
            .getAll()
            .forEach((featureName, featureValue) ->
                assertEquals(!"feature3".equalsIgnoreCase(featureName) && expectedFeatureValues.get(featureName), featureValue, featureName)
            );
    }

    @Test
    void testUnauthenticated() {
        when(mockTenantIdProvider.provideTenantIdString()).thenThrow(new TenantNotFoundException("No tenant!!!"));
        expectedFeatureValues.forEach((featureName, assertedValue) -> {
            boolean featureValue = featureService.isEnabled(featureName, Optional.empty());
            assertEquals(featureValue, assertedValue, featureName);
        });
    }

    @Test
    void testDefaultValuesForCiConfig() {
        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID_1);
        BillyConfiguration ciConfig = TypeSafeDynamicConfigLoader.load("config-ci.conf");
        featureService = new FeatureService(ciConfig, mock(AwsAppConfigurationService.class), mockTenantIdProvider);
        assertTrue(featureService.isEnabled(Feature.ACCOUNTING_V1_0));
        // This is currently explicitly set to false for CI
        assertFalse(featureService.isEnabled(Feature.PRODUCT_RULES_DEMO));
    }

    @Test
    void testDefaultValuesForLocalConfig() {
        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID_1);
        BillyConfiguration localConfig = TypeSafeDynamicConfigLoader.load("config-local.conf");
        featureService = new FeatureService(localConfig, mock(AwsAppConfigurationService.class), mockTenantIdProvider);
        assertTrue(featureService.isEnabled(Feature.ACCOUNTING_V1_0));
        // This is currently explicitly set to true only for local config and not CI
        assertTrue(featureService.isEnabled(Feature.PRODUCT_RULES_DEMO));
    }

    @Test
    void testDefaultValuesForNonLocal() {
        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID_1);
        BillyConfiguration prodConfig = TypeSafeDynamicConfigLoader.load("config-prod.conf");
        featureService = new FeatureService(prodConfig, mock(AwsAppConfigurationService.class), mockTenantIdProvider);
        // Default should be false in non-local environments
        assertFalse(featureService.isEnabled(Feature.ACCOUNTING_V1_0));
        assertFalse(featureService.isEnabled(Feature.PRODUCT_RULES_DEMO));
    }
}
