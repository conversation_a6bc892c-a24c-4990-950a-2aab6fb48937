package com.subskribe.billy.di.hk2.providers.auditlog;

import java.util.stream.Stream;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class SqlReadOnlyPredicateTest {

    @ParameterizedTest
    @MethodSource("provideTestQueries")
    void isQueryModifying(String sql, boolean isQueryModifying) {
        Assertions.assertThat(SqlReadOnlyPredicate.isQueryModifying(sql)).isEqualTo(isQueryModifying);
    }

    private static Stream<Arguments> provideTestQueries() {
        return Stream.of(
            Arguments.of("SELECT * FROM table_1", false),
            Arguments.of("select * from table_1", false),
            Arguments.of("SeLeCT * from table_1", false),
            Arguments.of("select updated_on from table_1", false),
            Arguments.of("select deleted_on from table_1", false),
            Arguments.of("insert into table_1 VALUES 1,2,3", true),
            Arguments.of("INSERT INTO table_1 VALUES 1,2,3", true),
            Arguments.of("UPDATE table_1 SET a = b", true),
            Arguments.of("update table_1 SET a = b", true),
            Arguments.of("delete from table_1", true),
            Arguments.of("alter table_1", true),
            Arguments.of("truncate table_1", true),
            Arguments.of("lock table_1", true),
            Arguments.of("unlock table_1", true),
            Arguments.of(
                """
                WITH source AS (
                    SELECT * FROM table_1
                )
                UPDATE table_2""",
                true
            )
        );
    }
}
