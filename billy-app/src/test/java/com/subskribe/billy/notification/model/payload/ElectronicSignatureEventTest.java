package com.subskribe.billy.notification.model.payload;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import nl.jqno.equalsverifier.EqualsVerifier;
import org.junit.jupiter.api.Test;

class ElectronicSignatureEventTest {

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    @Test
    void testEquals() {
        EqualsVerifier.simple().forClass(ElectronicSignatureEvent.class).verify();
    }

    @Test
    void testJsonRoundTrip() throws Exception {
        ElectronicSignatureEvent esignEvent = ElectronicSignatureEventStub.createElectronicSignatureEvent();
        String jsonValue = OBJECT_MAPPER.writeValueAsString(esignEvent);
        ElectronicSignatureEvent deserialized = OBJECT_MAPPER.readValue(jsonValue, ElectronicSignatureEvent.class);
        assertThat(deserialized).isEqualTo(esignEvent);
    }
}
