package com.subskribe.billy.notification.service.email;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.notification.model.ImmutableNotificationInstance;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.notification.model.NotificationInstanceStatus;
import com.subskribe.billy.notification.model.NotificationTarget;
import com.subskribe.billy.notification.model.NotificationTargetType;
import com.subskribe.billy.shared.ratelimit.RateLimiterFactory;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.BucketConfiguration;
import java.time.Duration;
import java.time.Instant;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class EmailNotificationRateLimiterTest {

    private static final NotificationEventType TEST_EVENT_TYPE = NotificationEventType.ORDER_APPROVED;
    private static final int RATE_LIMIT_PER_PERIOD = 20;
    private static final int RATE_LIMIT_PERIOD_MINUTES = 10;

    @Mock
    private RateLimiterFactory rateLimiterFactory;

    @Mock
    private Bucket bucket;

    private EmailNotificationRateLimiter rateLimiter;
    private NotificationInstance notificationInstance;
    private NotificationTarget notificationTarget;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        when(rateLimiterFactory.buildBucket(any(), any())).thenReturn(bucket);

        rateLimiter = new EmailNotificationRateLimiter(rateLimiterFactory);

        notificationInstance = ImmutableNotificationInstance.builder()
            .id(UUID.randomUUID())
            .notificationId(UUID.randomUUID())
            .entityId("ORDER-123")
            .eventType(TEST_EVENT_TYPE)
            .payload("{}")
            .status(NotificationInstanceStatus.ATTEMPTED)
            .deliveryAttempts(1)
            .lastDeliveryAttempt(Instant.now())
            .build();

        notificationTarget = new NotificationTarget(UUID.randomUUID(), "some-arn", NotificationTargetType.EMAIL, "<EMAIL>");
    }

    @Test
    void constructorShouldInitializeBucketConfigurationWithCorrectParameters() {
        ArgumentCaptor<BucketConfiguration> configCaptor = ArgumentCaptor.forClass(BucketConfiguration.class);
        Bucket mockedBucket = mock(Bucket.class);
        when(rateLimiterFactory.buildBucket(any(), configCaptor.capture())).thenReturn(mockedBucket);

        EmailNotificationRateLimiter testRateLimiter = new EmailNotificationRateLimiter(rateLimiterFactory);
        testRateLimiter.checkRateLimitForNotification(notificationTarget, notificationInstance);

        BucketConfiguration capturedConfig = configCaptor.getValue();

        assertThat(capturedConfig.getBandwidths()).hasSize(1);
        assertThat(capturedConfig.getBandwidths()[0].getCapacity()).isEqualTo(RATE_LIMIT_PER_PERIOD);
        assertThat(capturedConfig.getBandwidths()[0].getRefillPeriodNanos()).isEqualTo(Duration.ofMinutes(RATE_LIMIT_PERIOD_MINUTES).toNanos());
        assertThat(capturedConfig.getBandwidths()[0].getRefillTokens()).isEqualTo(RATE_LIMIT_PER_PERIOD);
    }

    @Test
    void checkRateLimitForNotificationShouldReturnTrueWhenRateLimitIsNotExceeded() {
        when(bucket.tryConsume(1)).thenReturn(true);
        String expectedKey = String.format("email-notification-%s-%s", notificationTarget.getNotificationId(), TEST_EVENT_TYPE);

        boolean result = rateLimiter.checkRateLimitForNotification(notificationTarget, notificationInstance);

        assertThat(result).isTrue();

        verify(rateLimiterFactory).buildBucket(eq(expectedKey), any(BucketConfiguration.class));
        verify(bucket).tryConsume(1);
    }

    @Test
    void checkRateLimitForNotificationShouldReturnFalseWhenRateLimitIsExceeded() {
        when(bucket.tryConsume(1)).thenReturn(false);

        boolean result = rateLimiter.checkRateLimitForNotification(notificationTarget, notificationInstance);

        assertThat(result).isFalse();
        verify(bucket).tryConsume(1);
    }
}
