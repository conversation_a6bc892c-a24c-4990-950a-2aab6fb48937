package com.subskribe.billy.notification.event;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.event.EventStubber;
import com.subskribe.billy.event.consumer.EventConsumerConfiguration;
import com.subskribe.billy.event.consumer.ImmutableEventConsumerConfiguration;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.streams.Stream;
import com.subskribe.billy.event.streams.kinesis.ConsumerConsumptionMode;
import com.subskribe.billy.exception.TenantNotFoundException;
import com.subskribe.billy.exception.handling.TryForeverExceptionHandlingStrategy;
import com.subskribe.billy.notification.service.NotificationEventAdapter;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class NotificationEventConsumerTest {

    @Mock
    TenantIdProvider tenantIdProvider;

    @Mock
    NotificationEventAdapter notificationEventAdapter;

    @InjectMocks
    NotificationEventConsumer notificationEventConsumer;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(tenantIdProvider.provideTenantIdString()).thenThrow(new TenantNotFoundException("test"));
    }

    @Test
    void testOnEventPassesEventToAdapter() {
        Event event = EventStubber.eventStub();
        notificationEventConsumer.onEvent(event);
        verify(notificationEventAdapter, times(1)).processEvent(event);
        verify(tenantIdProvider, times(1)).setTenantId(new TenantId(event.getTenantId()));
        verify(tenantIdProvider, times(1)).clearThreadLocal();
    }

    @Test
    void testConfiguration() {
        EventConsumerConfiguration result = notificationEventConsumer.getConfiguration();
        EventConsumerConfiguration expected = ImmutableEventConsumerConfiguration.builder()
            .canonicalConsumerName("notification-consumer")
            .addAllHandlesEventsFromStream(Set.of(Stream.BILLING_DOMAIN_STREAM, Stream.SUBSCRIPTION_DOMAIN_STREAM, Stream.ORDER_DOMAIN_STREAM))
            .exceptionHandlingStrategy(TryForeverExceptionHandlingStrategy.DEFAULT_TRY_FOREVER_STRATEGY)
            .consumerConsumptionMode(ConsumerConsumptionMode.DEDICATED_FAN_OUT)
            .build();

        Assertions.assertEquals(result, expected);
    }
}
