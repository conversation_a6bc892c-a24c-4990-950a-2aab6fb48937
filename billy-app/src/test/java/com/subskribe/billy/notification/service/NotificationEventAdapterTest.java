package com.subskribe.billy.notification.service;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.notification.model.ImmutableNotification;
import com.subskribe.billy.notification.model.Notification;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.payload.ElectronicSignatureEvent;
import com.subskribe.billy.notification.model.payload.ElectronicSignatureEventStub;
import com.subskribe.billy.resources.json.invoice.InvoiceJson;
import com.subskribe.billy.resources.json.invoice.InvoiceJsonStub;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.resources.json.order.OrderJsonStub;
import com.subskribe.billy.resources.json.subscription.SubscriptionChargeChangeStub;
import com.subskribe.billy.resources.json.subscription.SubscriptionJson;
import com.subskribe.billy.resources.json.subscription.SubscriptionJsonStub;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.subscription.services.SubscriptionChargeChange;
import java.nio.ByteBuffer;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NotificationEventAdapterTest {

    private NotificationService notificationService;
    private ObjectMapper objectMapper;

    private NotificationEventAdapter notificationEventAdapter;

    private static final String STUB_JSON = "{\"foo\":\"bar\"}";
    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final String EVENT_ID = UUID.randomUUID().toString();

    @BeforeEach
    void setUp() {
        notificationService = mock(NotificationService.class);
        objectMapper = mock(ObjectMapper.class);

        notificationEventAdapter = new NotificationEventAdapter(notificationService, objectMapper);
    }

    @Test
    void testProcessInvoiceEvent() throws Exception {
        InvoiceJson invoiceJson = InvoiceJsonStub.buildInvoiceJson();
        when(objectMapper.readValue(STUB_JSON, InvoiceJson.class)).thenReturn(invoiceJson);
        Event event = Event.builder()
            .id(EVENT_ID)
            .type(EventType.INVOICE_POSTED_V2)
            .tenantId(TENANT_ID)
            .payload(ByteBuffer.wrap(STUB_JSON.getBytes()))
            .build();

        notificationEventAdapter.processEvent(event);

        Notification expected = createNotification(NotificationEventType.INVOICE_POSTED, invoiceJson.getInvoiceNumber());

        verify(notificationService, times(1)).sendNotification(expected);
    }

    @Test
    void testProcessInvoiceEventWithRealJson() throws Exception {
        NotificationEventAdapter realAdapter = new NotificationEventAdapter(notificationService);
        InvoiceJson invoiceJson = InvoiceJsonStub.buildInvoiceJson();
        String invoiceJsonString = JacksonProvider.defaultMapper().writeValueAsString(invoiceJson);

        Event event = Event.builder()
            .id(EVENT_ID)
            .type(EventType.INVOICE_POSTED_V2)
            .tenantId(TENANT_ID)
            .payload(ByteBuffer.wrap(invoiceJsonString.getBytes()))
            .build();

        realAdapter.processEvent(event);

        Notification expected = ImmutableNotification.builder()
            .notificationEventType(NotificationEventType.INVOICE_POSTED)
            .entityId(invoiceJson.getInvoiceNumber())
            .tenantId(TENANT_ID)
            .messageBody(invoiceJsonString)
            .sourceEventId(EVENT_ID)
            .build();

        verify(notificationService, times(1)).sendNotification(expected);
    }

    @Test
    void testProcessInvoiceVoidedEvent() throws Exception {
        InvoiceJson invoiceJson = InvoiceJsonStub.buildInvoiceJson();
        when(objectMapper.readValue(STUB_JSON, InvoiceJson.class)).thenReturn(invoiceJson);
        Event event = Event.builder()
            .id(EVENT_ID)
            .type(EventType.INVOICE_VOIDED_V2)
            .tenantId(TENANT_ID)
            .payload(ByteBuffer.wrap(STUB_JSON.getBytes()))
            .build();

        notificationEventAdapter.processEvent(event);

        Notification expected = createNotification(NotificationEventType.INVOICE_VOIDED, invoiceJson.getInvoiceNumber());

        verify(notificationService, times(1)).sendNotification(expected);
    }

    @Test
    void testProcessSubscriptionEvent() throws Exception {
        SubscriptionJson subscriptionJson = SubscriptionJsonStub.createSubscriptionJson();
        when(objectMapper.readValue(STUB_JSON, SubscriptionJson.class)).thenReturn(subscriptionJson);
        Event event = Event.builder()
            .id(EVENT_ID)
            .type(EventType.SUBSCRIPTION_CREATED)
            .tenantId(TENANT_ID)
            .payload(ByteBuffer.wrap(STUB_JSON.getBytes()))
            .build();

        notificationEventAdapter.processEvent(event);

        Notification expected = createNotification(NotificationEventType.SUBSCRIPTION_CREATED, subscriptionJson.getId());

        verify(notificationService, times(1)).sendNotification(expected);
    }

    @Test
    void testProcessSubscriptionChargeChangeEvent() throws Exception {
        SubscriptionChargeChange subscriptionChargeChangeJson = SubscriptionChargeChangeStub.createSubscriptionChargeChange();
        when(objectMapper.readValue(STUB_JSON, SubscriptionChargeChange.class)).thenReturn(subscriptionChargeChangeJson);
        Event event = Event.builder()
            .id(EVENT_ID)
            .type(EventType.SUBSCRIPTION_CHARGE_CHANGE)
            .tenantId(TENANT_ID)
            .payload(ByteBuffer.wrap(STUB_JSON.getBytes()))
            .build();

        notificationEventAdapter.processEvent(event);

        Notification expected = createNotification(
            NotificationEventType.SUBSCRIPTION_CHARGE_CHANGE,
            subscriptionChargeChangeJson.getSubscription().getId()
        );

        verify(notificationService, times(1)).sendNotification(expected);
    }

    @Test
    void testProcessOrderEvent() throws Exception {
        OrderJson orderJson = OrderJsonStub.createOrderJson();
        when(objectMapper.readValue(STUB_JSON, OrderJson.class)).thenReturn(orderJson);
        Event event = Event.builder()
            .id(EVENT_ID)
            .type(EventType.ORDER_SUBMITTED)
            .tenantId(TENANT_ID)
            .payload(ByteBuffer.wrap(STUB_JSON.getBytes()))
            .build();

        notificationEventAdapter.processEvent(event);

        Notification expected = createNotification(NotificationEventType.ORDER_SUBMITTED, orderJson.getId());

        verify(notificationService, times(1)).sendNotification(expected);
    }

    @Test
    void testEsignatureCompletedEvent() throws Exception {
        ElectronicSignatureEvent esignEvent = ElectronicSignatureEventStub.createElectronicSignatureEvent();
        when(objectMapper.readValue(STUB_JSON, ElectronicSignatureEvent.class)).thenReturn(esignEvent);
        Event event = Event.builder()
            .id(EVENT_ID)
            .type(EventType.ESIGNATURE_COMPLETED)
            .tenantId(TENANT_ID)
            .payload(ByteBuffer.wrap(STUB_JSON.getBytes()))
            .build();

        notificationEventAdapter.processEvent(event);

        Notification expected = createNotification(NotificationEventType.ESIGNATURE_COMPLETED, esignEvent.getId());

        verify(notificationService, times(1)).sendNotification(expected);
    }

    private static Notification createNotification(NotificationEventType notificationEventType, String entityId) {
        return ImmutableNotification.builder()
            .notificationEventType(notificationEventType)
            .entityId(entityId)
            .tenantId(TENANT_ID)
            .messageBody(STUB_JSON)
            .sourceEventId(EVENT_ID)
            .build();
    }

    @Test
    void testAllEventTypesHaveDeserializer() {
        objectMapper = spy(JacksonProvider.emptyFieldExcludingMapper());
        String invalidJson = "{{";
        // These will always fail since the JSON is invalid, but we're looking for a specific exception, where
        // it would indicate that we can't even find the deserializer
        NotificationEventAdapter.EVENT_TYPE_NOTIFICATION_TYPE_MAP.keySet()
            .forEach(eventType -> {
                Event event = Event.builder().type(eventType).payload(ByteBuffer.wrap(invalidJson.getBytes())).build();
                Assertions.assertThatThrownBy(() -> notificationEventAdapter.processEvent(event)).isNotInstanceOf(InvalidInputException.class);
                verifyNoInteractions(objectMapper);
            });
    }
}
