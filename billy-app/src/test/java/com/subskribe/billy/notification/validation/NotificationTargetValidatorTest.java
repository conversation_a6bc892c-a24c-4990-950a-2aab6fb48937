package com.subskribe.billy.notification.validation;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.Mockito.when;

import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.notification.model.NotificationConfiguration;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationTargetAndSubscriptions;
import com.subskribe.billy.notification.model.NotificationTargetType;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class NotificationTargetValidatorTest {

    @Mock
    private NotificationConfiguration notificationConfiguration;

    private NotificationTargetAndSubscriptions emailTarget;
    private NotificationTargetAndSubscriptions webhookTarget;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        emailTarget = new NotificationTargetAndSubscriptions();
        emailTarget.setName("Test Email Target");
        emailTarget.setNotificationTargetType(NotificationTargetType.EMAIL);
        emailTarget.setNotificationTarget("<EMAIL>");
        emailTarget.setSubscribedEvents(List.of(NotificationEventType.ORDER_EXECUTED));

        webhookTarget = new NotificationTargetAndSubscriptions();
        webhookTarget.setName("Test Webhook Target");
        webhookTarget.setNotificationTargetType(NotificationTargetType.WEBHOOK);
        webhookTarget.setNotificationTarget("https://example.com/webhook");
        webhookTarget.setSubscribedEvents(List.of(NotificationEventType.ORDER_EXECUTED));
    }

    @Test
    void validEmailTargetShouldNotThrowException() {
        assertThatNoException()
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, emailTarget));
    }

    @Test
    void validWebhookTargetShouldNotThrowException() {
        when(notificationConfiguration.getAllowLocalUrls()).thenReturn(false);

        assertThatNoException()
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, webhookTarget));
    }

    @Test
    void nullNameShouldThrowException() {
        emailTarget.setName(null);

        assertThatExceptionOfType(IllegalArgumentException.class)
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, emailTarget))
            .withMessageContaining("name cannot be blank");
    }

    @Test
    void emptyNameShouldThrowException() {
        emailTarget.setName("");

        assertThatExceptionOfType(IllegalArgumentException.class)
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, emailTarget))
            .withMessageContaining("name cannot be blank");
    }

    @Test
    void nullTargetShouldThrowException() {
        emailTarget.setNotificationTarget(null);

        assertThatExceptionOfType(IllegalArgumentException.class)
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, emailTarget))
            .withMessageContaining("notification target cannot be blank");
    }

    @Test
    void emptyTargetShouldThrowException() {
        emailTarget.setNotificationTarget("");

        assertThatExceptionOfType(IllegalArgumentException.class)
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, emailTarget))
            .withMessageContaining("notification target cannot be blank");
    }

    @Test
    void nullTargetTypeShouldThrowException() {
        emailTarget.setNotificationTargetType(null);

        assertThatExceptionOfType(IllegalArgumentException.class)
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, emailTarget))
            .withMessageContaining("notification target type");
    }

    @Test
    void nullSubscribedEventsShouldThrowException() {
        emailTarget.setSubscribedEvents(null);

        assertThatExceptionOfType(IllegalArgumentException.class)
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, emailTarget))
            .withMessageContaining("subscribed events");
    }

    @Test
    void invalidEmailShouldThrowException() {
        emailTarget.setNotificationTarget("not-an-email");

        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, emailTarget))
            .withMessageContaining("Invalid email address");
    }

    @Test
    void invalidUrlShouldThrowException() {
        when(notificationConfiguration.getAllowLocalUrls()).thenReturn(false);
        webhookTarget.setNotificationTarget("not-a-url");

        assertThatExceptionOfType(IllegalArgumentException.class)
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, webhookTarget))
            .withMessageContaining("Expected a valid URL");
    }

    @Test
    void slackTargetTypeShouldValidateUrl() {
        when(notificationConfiguration.getAllowLocalUrls()).thenReturn(false);
        webhookTarget.setNotificationTargetType(NotificationTargetType.SLACK);
        webhookTarget.setNotificationTarget("not-a-url");

        assertThatExceptionOfType(IllegalArgumentException.class)
            .isThrownBy(() -> NotificationTargetValidator.validateNotificationTargetAndSubscriptions(notificationConfiguration, webhookTarget))
            .withMessageContaining("Expected a valid URL");
    }
}
