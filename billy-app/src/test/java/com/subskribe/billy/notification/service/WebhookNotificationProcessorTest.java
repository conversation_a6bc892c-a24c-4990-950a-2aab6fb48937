package com.subskribe.billy.notification.service;

import static com.subskribe.billy.notification.service.NotificationStubs.buildNotificationTarget;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.squareup.okhttp.mockwebserver.MockResponse;
import com.squareup.okhttp.mockwebserver.MockWebServer;
import com.squareup.okhttp.mockwebserver.RecordedRequest;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.notification.model.ImmutableNotificationProcessingResult;
import com.subskribe.billy.notification.model.ImmutableWebhookMessage;
import com.subskribe.billy.notification.model.ImmutableWebhookSignature;
import com.subskribe.billy.notification.model.NotificationConfiguration;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.notification.model.NotificationProcessingResult;
import com.subskribe.billy.notification.model.NotificationTarget;
import com.subskribe.billy.notification.model.NotificationTargetType;
import com.subskribe.billy.notification.model.WebhookMessage;
import com.subskribe.billy.notification.model.WebhookSignature;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class WebhookNotificationProcessorTest {

    private UncheckedObjectMapper objectMapper;
    private WebhookSigningService webhookSigningService;
    private WebhookNotificationProcessor webhookNotificationProcessor;

    private MockWebServer mockWebServer;

    private static final String TENANT_ID = UUID.randomUUID().toString();
    private static final String WEBHOOK_PAYLOAD = "{\"foo2\":\"bar2\"}";

    @BeforeEach
    void setUp() throws Exception {
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        when(billyConfiguration.getNotificationConfiguration()).thenReturn(getNotificationConfiguration());

        objectMapper = mock(UncheckedObjectMapper.class);
        webhookSigningService = mock(WebhookSigningService.class);
        when(webhookSigningService.signPayloadForTarget(any(), any())).thenReturn(Optional.empty());

        mockWebServer = new MockWebServer();
        mockWebServer.start();

        webhookNotificationProcessor = new WebhookNotificationProcessor(billyConfiguration, objectMapper, webhookSigningService);
    }

    private static NotificationConfiguration getNotificationConfiguration() {
        NotificationConfiguration notificationConfiguration = new NotificationConfiguration();
        notificationConfiguration.setHttpCallTimeoutInSeconds(10L);
        notificationConfiguration.setWebhooksEnabled(true);
        return notificationConfiguration;
    }

    @Test
    void testGetTargetType() {
        assertThat(webhookNotificationProcessor.getTargetType()).isEqualTo(NotificationTargetType.WEBHOOK);
    }

    @Test
    void testAllNotificationTypesAreSupported() {
        assertThat(webhookNotificationProcessor.getSupportedEventTypes()).containsExactlyInAnyOrder(NotificationEventType.values());
    }

    @Test
    void testProcessNotificationReturnsFailureResultOn4xx() {
        NotificationTarget notificationTarget = buildNotificationTarget(mockWebServer.url("/").toString(), TENANT_ID);
        NotificationInstance notificationInstance = buildNotificationInstanceAndSetupJsonMock(notificationTarget);

        mockWebServer.enqueue(new MockResponse().setStatus("HTTP/1.1 400 Bad Request"));
        NotificationProcessingResult result = webhookNotificationProcessor.processNotification(notificationTarget, notificationInstance);
        NotificationProcessingResult expected = ImmutableNotificationProcessingResult.builder()
            .isSuccess(false)
            .errorMessage(Optional.of("Bad Request"))
            .statusCode(400)
            .build();
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void testProcessNotificationReturnsFailureResultOn5xx() {
        NotificationTarget notificationTarget = buildNotificationTarget(mockWebServer.url("/").toString(), TENANT_ID);
        NotificationInstance notificationInstance = buildNotificationInstanceAndSetupJsonMock(notificationTarget);

        mockWebServer.enqueue(new MockResponse().setStatus("HTTP/1.1 500 Internal Server Error"));
        NotificationProcessingResult result = webhookNotificationProcessor.processNotification(notificationTarget, notificationInstance);
        NotificationProcessingResult expected = ImmutableNotificationProcessingResult.builder()
            .isSuccess(false)
            .errorMessage(Optional.of("Internal Server Error"))
            .statusCode(500)
            .build();
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void testProcessNotificationReturnsSuccessOn200() {
        NotificationTarget notificationTarget = buildNotificationTarget(mockWebServer.url("/").toString(), TENANT_ID);
        NotificationInstance notificationInstance = buildNotificationInstanceAndSetupJsonMock(notificationTarget);

        mockWebServer.enqueue(new MockResponse().setStatus("HTTP/1.1 200 OK"));
        NotificationProcessingResult result = webhookNotificationProcessor.processNotification(notificationTarget, notificationInstance);
        NotificationProcessingResult expected = ImmutableNotificationProcessingResult.builder().isSuccess(true).statusCode(200).build();
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void testCorrectPayloadSentWithoutSignature() throws Exception {
        NotificationTarget notificationTarget = buildNotificationTarget(mockWebServer.url("/").toString(), TENANT_ID);
        NotificationInstance notificationInstance = buildNotificationInstanceAndSetupJsonMock(notificationTarget);

        mockWebServer.enqueue(new MockResponse().setStatus("HTTP/1.1 200 OK"));

        webhookNotificationProcessor.processNotification(notificationTarget, notificationInstance);

        RecordedRequest request = mockWebServer.takeRequest();

        assertThat(request.getMethod()).isEqualTo("POST");
        assertThat(request.getHeader("Content-Type")).isEqualTo("application/json; charset=utf-8");
        assertThat(request.getHeader("X-Subskribe-Signature")).isEqualTo(null);
        assertThat(request.getBody().readString(StandardCharsets.UTF_8)).isEqualTo(WEBHOOK_PAYLOAD);
    }

    @Test
    void testCorrectSignature() throws Exception {
        NotificationTarget notificationTarget = buildNotificationTarget(mockWebServer.url("/").toString(), TENANT_ID);
        NotificationInstance notificationInstance = buildNotificationInstanceAndSetupJsonMock(notificationTarget);
        WebhookSignature signature = ImmutableWebhookSignature.builder()
            .signature(RandomStringUtils.randomAlphanumeric(32))
            .timestamp(1695401135L)
            .build();

        mockWebServer.enqueue(new MockResponse().setStatus("HTTP/1.1 200 OK"));
        when(webhookSigningService.signPayloadForTarget(notificationTarget.getNotificationId().toString(), WEBHOOK_PAYLOAD)).thenReturn(
            Optional.of(signature)
        );

        webhookNotificationProcessor.processNotification(notificationTarget, notificationInstance);

        RecordedRequest request = mockWebServer.takeRequest();

        assertThat(request.getMethod()).isEqualTo("POST");
        assertThat(request.getHeader("Content-Type")).isEqualTo("application/json; charset=utf-8");
        assertThat(request.getHeader("X-Subskribe-Signature")).isEqualTo("t=" + signature.getTimestamp() + ",s=" + signature.getSignature());
        assertThat(request.getBody().readString(StandardCharsets.UTF_8)).isEqualTo(WEBHOOK_PAYLOAD);
    }

    @Test
    void testProcessNotificationReturnsFailureResultOnBadConnection() {
        NotificationTarget notificationTarget = buildNotificationTarget("http://localhost:9999", TENANT_ID);
        NotificationInstance notificationInstance = buildNotificationInstanceAndSetupJsonMock(notificationTarget);

        NotificationProcessingResult result = webhookNotificationProcessor.processNotification(notificationTarget, notificationInstance);

        assertThat(result.getIsSuccess()).isEqualTo(false);
        assertThat(result.getErrorMessage()).isPresent();
        assertThat(result.getErrorMessage().get()).startsWith("Failed to connect");
    }

    private NotificationInstance buildNotificationInstanceAndSetupJsonMock(NotificationTarget target) {
        NotificationInstance notificationInstance = NotificationStubs.buildNotificationInstance();
        WebhookMessage expectedMessage = ImmutableWebhookMessage.builder()
            .entityId(notificationInstance.getEntityId())
            .entityType(notificationInstance.getEventType().name())
            .event(notificationInstance.getPayload())
            .eventId(notificationInstance.getId().toString())
            .targetId(target.getNotificationId().toString())
            .build();
        when(objectMapper.writeValueAsString(expectedMessage)).thenReturn(WEBHOOK_PAYLOAD);
        return notificationInstance;
    }
}
