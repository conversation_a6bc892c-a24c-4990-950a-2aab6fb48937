package com.subskribe.billy.notification.model.payload;

import com.subskribe.billy.email.model.EmailContact;
import java.util.UUID;

public class ElectronicSignatureEventStub {

    public static ElectronicSignatureEvent createElectronicSignatureEvent() {
        return ImmutableElectronicSignatureEvent.builder()
            .id(UUID.randomUUID().toString())
            .orderId(UUID.randomUUID().toString())
            .initiatedBy("initiatedBy")
            .accountSignatory(new EmailContact())
            .tenantSignatory(new EmailContact())
            .build();
    }
}
