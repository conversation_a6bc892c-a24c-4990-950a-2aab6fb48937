package com.subskribe.billy.notification.service.email;

import static com.subskribe.billy.notification.service.NotificationStubs.buildNotificationInstance;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.notification.model.ImmutableNotificationInstance;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

class NotificationEmailFormatterTest {

    private NotificationEmailFormatter formatter;
    private NotificationInstance orderExecutedInstance;
    private NotificationInstance orderApprovedInstance;
    private static final UUID NOTIFICATION_ID = UUID.randomUUID();
    private static final UncheckedObjectMapper objectMapper = UncheckedObjectMapper.defaultMapper();

    @BeforeEach
    void setUp() throws IOException {
        BillyConfiguration configuration = mock(BillyConfiguration.class);
        when(configuration.getSiteUrl()).thenReturn("https://test.subskribe.net");
        when(configuration.getEnvName()).thenReturn("test");
        formatter = new NotificationEmailFormatter(configuration);

        Order order = new Order();
        order.setOrderId("ORD-123");
        order.setName("Test Order");
        order.setAccountId("ACC-456");
        order.setTotalAmount(new BigDecimal("999.99"));

        orderExecutedInstance = ImmutableNotificationInstance.builder()
            .from(buildNotificationInstance())
            .eventType(NotificationEventType.ORDER_EXECUTED)
            .payload(objectMapper.writeValueAsString(order))
            .notificationId(NOTIFICATION_ID)
            .build();

        orderApprovedInstance = ImmutableNotificationInstance.builder()
            .from(buildNotificationInstance())
            .eventType(NotificationEventType.ORDER_APPROVED)
            .payload(objectMapper.writeValueAsString(order))
            .notificationId(NOTIFICATION_ID)
            .build();
    }

    @Test
    void formatShouldRenderOrderExecutedTemplate() throws Exception {
        FormattedEmailNotification result = formatter.format(orderExecutedInstance);

        assertThat(result.body()).contains(
            "<p>Order ORD-123 has been executed</p>",
            "<li>Name: Test Order</li>",
            "<li>Account ID: ACC-456</li>",
            "<li>Total Amount: 999.99</li>"
        );

        assertThat(result.subject()).contains("Order ORD-123 has been executed");
    }

    @Test
    void formatShouldRenderOrderApprovedTemplate() throws Exception {
        FormattedEmailNotification result = formatter.format(orderApprovedInstance);

        assertThat(result.body()).contains(
            "<p>Order ORD-123 has been approved</p>",
            "<li>Name: Test Order</li>",
            "<li>Account ID: ACC-456</li>",
            "<li>Total Amount: 999.99</li>"
        );

        assertThat(result.subject()).contains("Order ORD-123 has been approved");
    }

    @Test
    void formatShouldRenderLinkWithCorrectUrl() throws Exception {
        FormattedEmailNotification result = formatter.format(orderExecutedInstance);

        assertThat(result.body()).contains("<a href=\"https://test.subskribe.net/orders/ORD-123\">Open order</a>");
    }

    @Test
    void formatShouldThrowExceptionWhenPayloadIsInvalid() {
        NotificationInstance invalidInstance = ImmutableNotificationInstance.builder().from(orderExecutedInstance).payload("{invalid json}").build();

        assertThatThrownBy(() -> formatter.format(invalidInstance))
            .isInstanceOf(NotificationEmailFormattingException.class)
            .hasMessageContaining("Failed to deserialize notification payload");
    }

    @Test
    void formatShouldNotThrowExceptionWhenPayloadIsMissingRequiredFields() throws Exception {
        String incompletePayload = "{\"orderId\":\"ORD-123\"}"; // Missing other fields

        NotificationInstance incompleteInstance = ImmutableNotificationInstance.builder()
            .from(orderExecutedInstance)
            .payload(incompletePayload)
            .build();

        FormattedEmailNotification result = formatter.format(incompleteInstance);

        assertThat(result.body()).contains("<p>Order ORD-123 has been executed</p>", "<li>Name: </li>", "<li>Account ID: </li>");
    }

    // This ensures that we have a template for each supported notification type
    @ParameterizedTest
    @MethodSource("supportedTypes")
    void getTemplateForNotificationTypeShouldNotThrowExceptionForSupportedTypes(NotificationEventType type) {
        assertThatNoException().isThrownBy(() -> formatter.getTemplateForNotificationType(type));
    }

    // This ensures that we have a subject line template for each supported notification type
    @ParameterizedTest
    @MethodSource("supportedTypes")
    void getSubjectForNotificationTypeShouldNotThrowExceptionForSupportedTypes(NotificationEventType type) {
        assertThatNoException().isThrownBy(() -> formatter.getSubjectLineForNotificationType(type));
    }

    static Set<NotificationEventType> supportedTypes() {
        return EmailNotificationProcessor.SUPPORTED_EVENT_TYPES;
    }
}
