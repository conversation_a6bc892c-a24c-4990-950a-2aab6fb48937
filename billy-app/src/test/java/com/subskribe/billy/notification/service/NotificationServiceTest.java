package com.subskribe.billy.notification.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.notification.db.NotificationInstanceDAO;
import com.subskribe.billy.notification.model.ImmutableNotificationProcessorTypeSupport;
import com.subskribe.billy.notification.model.NotificationConfiguration;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationProcessorTypeSupport;
import com.subskribe.billy.notification.model.NotificationTargetType;
import com.subskribe.billy.notification.service.email.EmailNotificationProcessor;
import java.time.Clock;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NotificationServiceTest {

    private FeatureService featureService;

    private NotificationService notificationService;

    @BeforeEach
    void setUp() {
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        when(billyConfiguration.getNotificationConfiguration()).thenReturn(mock(NotificationConfiguration.class));
        NotificationInstanceDAO notificationInstanceDAO = mock(NotificationInstanceDAO.class);
        NotificationTargetService notificationTargetService = mock(NotificationTargetService.class);
        featureService = mock(FeatureService.class);
        when(featureService.isEnabled(Feature.EMAIL_NOTIFICATIONS)).thenReturn(true);
        SlackNotificationProcessor slackNotificationProcessor = mock(SlackNotificationProcessor.class);
        when(slackNotificationProcessor.getTargetType()).thenReturn(NotificationTargetType.SLACK);
        WebhookNotificationProcessor webhookNotificationProcessor = mock(WebhookNotificationProcessor.class);
        when(webhookNotificationProcessor.getTargetType()).thenReturn(NotificationTargetType.WEBHOOK);
        EmailNotificationProcessor emailNotificationProcessor = mock(EmailNotificationProcessor.class);
        when(emailNotificationProcessor.getTargetType()).thenReturn(NotificationTargetType.EMAIL);

        notificationService = new NotificationService(
            notificationInstanceDAO,
            notificationTargetService,
            slackNotificationProcessor,
            webhookNotificationProcessor,
            emailNotificationProcessor,
            billyConfiguration,
            featureService,
            Clock.systemUTC()
        );

        when(slackNotificationProcessor.getSupportedEventTypes()).thenReturn(
            Set.of(NotificationEventType.INVOICE_POSTED, NotificationEventType.SUBSCRIPTION_CREATED)
        );
        when(webhookNotificationProcessor.getSupportedEventTypes()).thenReturn(
            Set.of(NotificationEventType.ORDER_SUBMITTED, NotificationEventType.SUBSCRIPTION_ACTIVATING)
        );
        when(emailNotificationProcessor.getSupportedEventTypes()).thenReturn(
            Set.of(NotificationEventType.ORDER_EXECUTED, NotificationEventType.ORDER_APPROVED)
        );
    }

    @Test
    void testGetSupportedNotificationTypesForTargetTypes() {
        List<NotificationProcessorTypeSupport> result = notificationService.getSupportedNotificationTypesForTargetTypes();
        assertThat(result).containsExactlyInAnyOrderElementsOf(
            List.of(
                ImmutableNotificationProcessorTypeSupport.builder()
                    .targetType("SLACK")
                    .addSupportedTypes("INVOICE_POSTED", "SUBSCRIPTION_CREATED")
                    .build(),
                ImmutableNotificationProcessorTypeSupport.builder()
                    .targetType("WEBHOOK")
                    .addSupportedTypes("ORDER_SUBMITTED", "SUBSCRIPTION_ACTIVATING")
                    .build(),
                ImmutableNotificationProcessorTypeSupport.builder().targetType("EMAIL").addSupportedTypes("ORDER_EXECUTED", "ORDER_APPROVED").build()
            )
        );
    }

    @Test
    void testGetSupportedNotificationTypesForTargetTypesEmailFeatureDisabled() {
        when(featureService.isEnabled(Feature.EMAIL_NOTIFICATIONS)).thenReturn(false);

        List<NotificationProcessorTypeSupport> result = notificationService.getSupportedNotificationTypesForTargetTypes();
        assertThat(result).containsExactlyInAnyOrderElementsOf(
            List.of(
                ImmutableNotificationProcessorTypeSupport.builder()
                    .targetType("SLACK")
                    .addSupportedTypes("INVOICE_POSTED", "SUBSCRIPTION_CREATED")
                    .build(),
                ImmutableNotificationProcessorTypeSupport.builder()
                    .targetType("WEBHOOK")
                    .addSupportedTypes("ORDER_SUBMITTED", "SUBSCRIPTION_ACTIVATING")
                    .build()
            )
        );
    }
}
