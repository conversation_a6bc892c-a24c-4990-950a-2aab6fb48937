package com.subskribe.billy.notification.service.email;

import static com.subskribe.billy.notification.model.NotificationEventType.HUBSPOT_SYNC_FAILED;
import static com.subskribe.billy.notification.model.NotificationEventType.ORDER_APPROVED;
import static com.subskribe.billy.notification.model.NotificationEventType.ORDER_EXECUTED;
import static com.subskribe.billy.notification.model.NotificationEventType.SALESFORCE_SYNC_FAILED;
import static com.subskribe.billy.notification.service.NotificationStubs.buildNotificationInstance;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.email.model.EmailData;
import com.subskribe.billy.email.model.EmailType;
import com.subskribe.billy.email.services.EmailService;
import com.subskribe.billy.notification.model.ImmutableNotificationInstance;
import com.subskribe.billy.notification.model.NotificationConfiguration;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.notification.model.NotificationProcessingResult;
import com.subskribe.billy.notification.model.NotificationTarget;
import com.subskribe.billy.notification.model.NotificationTargetType;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.io.IOException;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import javax.mail.MessagingException;
import org.apache.commons.lang.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class EmailNotificationProcessorTest {

    @Mock
    private NotificationEmailFormatter emailFormatter;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private EmailService emailService;

    @Mock
    private NotificationConfiguration notificationConfiguration;

    @Mock
    private FeatureService featureService;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private EmailNotificationRateLimiter emailNotificationRateLimiter;

    @InjectMocks
    private EmailNotificationProcessor processor;

    @Captor
    private ArgumentCaptor<EmailData> emailDataCaptor;

    private NotificationTarget notificationTarget;
    private NotificationInstance notificationInstance;
    private static final String FROM_EMAIL = "<EMAIL>";
    private static final String EMAIL_SUBJECT = "Test Subject";
    private static final String EMAIL_BODY = "Test Body";
    private static final String TENANT_ID = UUID.randomUUID().toString();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        notificationTarget = new NotificationTarget(
            UUID.randomUUID(),
            RandomStringUtils.randomAlphabetic(10),
            NotificationTargetType.EMAIL,
            "<EMAIL>"
        );

        notificationInstance = ImmutableNotificationInstance.builder().from(buildNotificationInstance()).eventType(ORDER_EXECUTED).build();

        when(billyConfiguration.getNotificationConfiguration()).thenReturn(notificationConfiguration);
        when(notificationConfiguration.getEmailFromAddress()).thenReturn(FROM_EMAIL);
        when(featureService.isEnabled(Feature.EMAIL_NOTIFICATIONS)).thenReturn(true);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        when(emailNotificationRateLimiter.checkRateLimitForNotification(notificationTarget, notificationInstance)).thenReturn(true);
    }

    @Test
    void getTargetTypeShouldReturnEmail() {
        assertThat(processor.getTargetType()).isEqualTo(NotificationTargetType.EMAIL);
    }

    @Test
    void getSupportedEventTypesShouldReturnOrderExecutedAndApproved() {
        Set<NotificationEventType> supportedTypes = processor.getSupportedEventTypes();

        assertThat(supportedTypes).containsExactlyInAnyOrder(ORDER_EXECUTED, ORDER_APPROVED, SALESFORCE_SYNC_FAILED, HUBSPOT_SYNC_FAILED);
    }

    @Test
    void processNotificationShouldReturnSuccessIfFeatureIsDisabledWithoutDoingAnything() {
        when(featureService.isEnabled(Feature.EMAIL_NOTIFICATIONS)).thenReturn(false);

        NotificationInstance unsupportedNotification = ImmutableNotificationInstance.builder()
            .from(notificationInstance)
            .eventType(NotificationEventType.SUBSCRIPTION_CREATED) // Unsupported type
            .build();

        NotificationProcessingResult result = processor.processNotification(notificationTarget, unsupportedNotification);

        assertThat(result.getIsSuccess()).isTrue();

        verifyNoInteractions(emailFormatter);
        verifyNoInteractions(emailService);
    }

    @Test
    void processNotificationShouldReturnFailureWhenEventTypeNotSupported() {
        NotificationInstance unsupportedNotification = ImmutableNotificationInstance.builder()
            .from(notificationInstance)
            .eventType(NotificationEventType.SUBSCRIPTION_CREATED) // Unsupported type
            .build();

        NotificationProcessingResult result = processor.processNotification(notificationTarget, unsupportedNotification);

        assertThat(result.getIsSuccess()).isFalse();
        assertThat(result.getErrorMessage()).isEqualTo(Optional.of("Unsupported"));

        verifyNoInteractions(emailFormatter);
        verifyNoInteractions(emailService);
    }

    @Test
    void processNotificationShouldReturnFailureWhenRateLimitExceeded() {
        when(emailNotificationRateLimiter.checkRateLimitForNotification(notificationTarget, notificationInstance)).thenReturn(false);

        NotificationProcessingResult result = processor.processNotification(notificationTarget, notificationInstance);

        assertThat(result.getIsSuccess()).isFalse();
        assertThat(result.getErrorMessage()).isEqualTo(Optional.of("Rate Limit Exceeded"));
        assertThat(result.getIsRetryable()).isFalse();

        verifyNoInteractions(emailFormatter);
        verifyNoInteractions(emailService);
    }

    @Test
    void processNotificationShouldReturnFailureWhenFormattingFails() throws NotificationEmailFormattingException {
        String errorMessage = "Formatting failed";
        when(emailFormatter.format(any(NotificationInstance.class))).thenThrow(
            new NotificationEmailFormattingException(errorMessage, new RuntimeException("test"))
        );

        NotificationProcessingResult result = processor.processNotification(notificationTarget, notificationInstance);

        assertThat(result.getIsSuccess()).isFalse();
        assertThat(result.getErrorMessage()).isEqualTo(Optional.of(errorMessage));

        verify(emailFormatter).format(notificationInstance);
        verifyNoInteractions(emailService);
    }

    @Test
    void processNotificationShouldReturnSuccessWhenEmailSentSuccessfully()
        throws NotificationEmailFormattingException, IOException, MessagingException {
        FormattedEmailNotification formattedEmail = new FormattedEmailNotification(EMAIL_SUBJECT, EMAIL_BODY);
        when(emailFormatter.format(notificationInstance)).thenReturn(formattedEmail);

        NotificationProcessingResult result = processor.processNotification(notificationTarget, notificationInstance);

        assertThat(result.getIsSuccess()).isTrue();
        assertThat(result.getErrorMessage()).isEmpty();

        verify(emailFormatter).format(notificationInstance);
        verify(emailService).sendEmail(emailDataCaptor.capture());

        EmailData capturedEmailData = emailDataCaptor.getValue();
        assertThat(capturedEmailData.getEmailTemplate()).isEqualTo(EMAIL_BODY);
        assertThat(capturedEmailData.getSubject()).isEqualTo(EMAIL_SUBJECT);
        assertThat(capturedEmailData.getFromEmail()).isEqualTo(FROM_EMAIL);
        assertThat(capturedEmailData.getEmailType()).isEqualTo(EmailType.NOTIFICATION);
        assertThat(capturedEmailData.getRelatedObjectId()).isEqualTo(notificationInstance.getNotificationId().toString());

        assertThat(capturedEmailData.getEmailContacts().getToContacts()).hasSize(1);
    }

    @Test
    void processNotificationShouldReturnFailureWhenIOExceptionOccurs() throws NotificationEmailFormattingException, IOException, MessagingException {
        FormattedEmailNotification formattedEmail = new FormattedEmailNotification(EMAIL_SUBJECT, EMAIL_BODY);
        when(emailFormatter.format(notificationInstance)).thenReturn(formattedEmail);
        String errorMessage = "IO error";
        doThrow(new IOException(errorMessage)).when(emailService).sendEmail(any(EmailData.class));

        NotificationProcessingResult result = processor.processNotification(notificationTarget, notificationInstance);

        assertThat(result.getIsSuccess()).isFalse();
        assertThat(result.getErrorMessage()).isEqualTo(Optional.of(errorMessage));

        verify(emailFormatter).format(notificationInstance);
        verify(emailService).sendEmail(any(EmailData.class));
    }

    @Test
    void processNotificationShouldReturnFailureWhenMessagingExceptionOccurs()
        throws NotificationEmailFormattingException, IOException, MessagingException {
        FormattedEmailNotification formattedEmail = new FormattedEmailNotification(EMAIL_SUBJECT, EMAIL_BODY);
        when(emailFormatter.format(notificationInstance)).thenReturn(formattedEmail);
        String errorMessage = "Messaging error";
        doThrow(new MessagingException(errorMessage)).when(emailService).sendEmail(any(EmailData.class));

        NotificationProcessingResult result = processor.processNotification(notificationTarget, notificationInstance);

        assertThat(result.getIsSuccess()).isFalse();
        assertThat(result.getErrorMessage()).isEqualTo(Optional.of(errorMessage));

        verify(emailFormatter).format(notificationInstance);
        verify(emailService).sendEmail(any(EmailData.class));
    }

    @Test
    void getEmailContactsShouldCreateCorrectContactsFromTarget() throws Exception {
        FormattedEmailNotification formattedEmail = new FormattedEmailNotification(EMAIL_SUBJECT, EMAIL_BODY);
        when(emailFormatter.format(notificationInstance)).thenReturn(formattedEmail);

        processor.processNotification(notificationTarget, notificationInstance);

        verify(emailService).sendEmail(emailDataCaptor.capture());
        EmailData capturedEmailData = emailDataCaptor.getValue();

        assertThat(capturedEmailData.getEmailContacts()).isNotNull();
        assertThat(capturedEmailData.getEmailContacts().getToContacts()).hasSize(1);
        assertThat(capturedEmailData.getEmailContacts().getToContacts().get(0).getEmail()).isEqualTo("<EMAIL>");
    }
}
