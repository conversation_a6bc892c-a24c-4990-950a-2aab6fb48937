package com.subskribe.billy.notification.validation;

import static org.assertj.core.api.Assertions.assertThatException;
import static org.assertj.core.api.Assertions.assertThatNoException;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class NotificationUrlValidatorTest {

    @Test
    void validateUrlAcceptsHttp() {
        assertThatNoException().isThrownBy(() -> NotificationUrlValidator.validateUrl("http://test.com", false));
    }

    @Test
    void validateUrlAcceptsHttps() {
        assertThatNoException().isThrownBy(() -> NotificationUrlValidator.validateUrl("https://test.com", false));
    }

    @Test
    void validateUrlDoesNotAcceptFtp() {
        assertThatException().isThrownBy(() -> NotificationUrlValidator.validateUrl("ftp://test.com", false));
    }

    @Test
    void validateUrlDoesNotAcceptRandomString() {
        assertThatException().isThrownBy(() -> NotificationUrlValidator.validateUrl(RandomStringUtils.randomAlphanumeric(10), false));
    }

    @Test
    void validateUrlDoesNotAcceptMissingSlash() {
        assertThatException().isThrownBy(() -> NotificationUrlValidator.validateUrl("http:/test.com", false));
    }

    @Test
    void validateUrlDoesNotAcceptOnlyDomain() {
        assertThatException().isThrownBy(() -> NotificationUrlValidator.validateUrl("test.com", false));
    }

    @Test
    void validateUrlAcceptsLocalHostIfEnabled() {
        assertThatNoException().isThrownBy(() -> NotificationUrlValidator.validateUrl("http://localhost:8080", true));
    }

    @Test
    void validateUrlDoesNotAcceptLocalHostIfNotEnabled() {
        assertThatException().isThrownBy(() -> NotificationUrlValidator.validateUrl("http://localhost:8080", false));
    }
}
