package com.subskribe.billy.notification.service;

import com.subskribe.billy.jooq.default_schema.tables.records.NotificationRecord;
import com.subskribe.billy.notification.model.ImmutableNotificationInstance;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.notification.model.NotificationInstanceStatus;
import com.subskribe.billy.notification.model.NotificationTarget;
import java.time.Instant;
import java.util.UUID;

public class NotificationStubs {

    private static final String NOTIFICATION_MESSAGE_JSON = "{\"foo\":\"bar\"}";

    public static NotificationInstance buildNotificationInstance() {
        return ImmutableNotificationInstance.builder()
            .id(UUID.randomUUID())
            .notificationId(UUID.randomUUID())
            .entityId("SUB-12345")
            .eventType(NotificationEventType.SUBSCRIPTION_CREATED)
            .payload(NOTIFICATION_MESSAGE_JSON)
            .status(NotificationInstanceStatus.ATTEMPTED)
            .deliveryAttempts(1)
            .lastDeliveryAttempt(Instant.now())
            .build();
    }

    public static NotificationTarget buildNotificationTarget(UUID id) {
        NotificationRecord notificationRecord = new NotificationRecord(
            id,
            UUID.randomUUID().toString(),
            "name",
            "description",
            "topicArn",
            "WEBHOOK",
            "http://test",
            true,
            null,
            null
        );
        return new NotificationTarget(notificationRecord);
    }

    public static NotificationTarget buildNotificationTarget(String baseUrl, String tenantId) {
        NotificationRecord notificationRecord = new NotificationRecord(
            UUID.randomUUID(),
            tenantId,
            "name",
            "description",
            "topicArn",
            "WEBHOOK",
            baseUrl,
            true,
            null,
            null
        );
        return new NotificationTarget(notificationRecord);
    }
}
