package com.subskribe.billy.notification.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.event.model.DefaultStreamPartitionKey;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventStatus;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.notification.db.NotificationDAO;
import com.subskribe.billy.notification.model.ImmutableNotification;
import com.subskribe.billy.notification.model.Notification;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.notification.model.NotificationInstanceStatus;
import com.subskribe.billy.notification.model.NotificationTarget;
import com.subskribe.billy.notification.model.NotificationTargetType;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class TestNotificationServiceTest {

    private static final String TENANT_ID = "test-tenant";
    private static final UUID TARGET_UUID = UUID.randomUUID();
    private static final String TARGET_ID = TARGET_UUID.toString();
    private static final String ENTITY_ID = "test-entity-id";
    private static final String EVENT_ID = "test-event-id";
    private static final String MESSAGE_BODY = "{\"test\":\"payload\"}";
    private static final UUID NOTIFICATION_ID = UUID.randomUUID();

    @Mock
    private NotificationDAO notificationDAO;

    @Mock
    private NotificationEventAdapter notificationEventAdapter;

    @Mock
    private NotificationService notificationService;

    private TestNotificationService testNotificationService;
    private Clock fixedClock;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        fixedClock = Clock.fixed(Instant.parse("2023-01-01T10:00:00Z"), ZoneId.systemDefault());
        testNotificationService = new TestNotificationService(notificationDAO, notificationEventAdapter, notificationService);
    }

    @Test
    void sendTestNotificationShouldThrowExceptionWhenTargetNotFound() {
        when(notificationDAO.getNotificationTargetById(TARGET_UUID)).thenReturn(Optional.empty());
        List<Event> events = Collections.singletonList(createTestEvent(EventType.SUBSCRIPTION_ACTIVATED));

        assertThatThrownBy(() -> testNotificationService.sendTestNotification(TARGET_ID, events))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("Notification target not found");

        verify(notificationDAO).getNotificationTargetById(TARGET_UUID);
        verify(notificationEventAdapter, never()).buildNotificationForEvent(any());
        verify(notificationService, never()).sendNotificationWithProcessor(any(), any());
    }

    @Test
    void sendTestNotificationShouldProcessEventsWhenTargetFound() {
        NotificationTarget target = createTestTarget();
        when(notificationDAO.getNotificationTargetById(TARGET_UUID)).thenReturn(Optional.of(target));

        Event event1 = createTestEvent(EventType.SUBSCRIPTION_ACTIVATED);
        Event event2 = createTestEvent(EventType.SUBSCRIPTION_CHARGE_CHANGE);

        Notification notification1 = createTestNotification(NotificationEventType.SUBSCRIPTION_ACTIVATED);
        Notification notification2 = createTestNotification(NotificationEventType.SUBSCRIPTION_CHARGE_CHANGE);

        when(notificationEventAdapter.buildNotificationForEvent(event1)).thenReturn(Optional.of(notification1));
        when(notificationEventAdapter.buildNotificationForEvent(event2)).thenReturn(Optional.of(notification2));

        List<Event> events = List.of(event1, event2);

        testNotificationService.sendTestNotification(TARGET_ID, events);

        verify(notificationDAO).getNotificationTargetById(TARGET_UUID);
        verify(notificationEventAdapter).buildNotificationForEvent(event1);
        verify(notificationEventAdapter).buildNotificationForEvent(event2);

        ArgumentCaptor<NotificationInstance> instanceCaptor = ArgumentCaptor.forClass(NotificationInstance.class);
        verify(notificationService, times(2)).sendNotificationWithProcessor(eq(target), instanceCaptor.capture());

        List<NotificationInstance> capturedInstances = instanceCaptor.getAllValues();
        assertThat(capturedInstances).hasSize(2);

        NotificationInstance instance1 = capturedInstances.get(0);
        assertThat(instance1.getEventType()).isEqualTo(NotificationEventType.SUBSCRIPTION_ACTIVATED);
        assertThat(instance1.getEntityId()).isEqualTo(ENTITY_ID);
        assertThat(instance1.getStatus()).isEqualTo(NotificationInstanceStatus.CREATED);
        assertThat(instance1.getDeliveryAttempts()).isZero();
        assertThat(instance1.getPayload()).isEqualTo(MESSAGE_BODY);

        NotificationInstance instance2 = capturedInstances.get(1);
        assertThat(instance2.getEventType()).isEqualTo(NotificationEventType.SUBSCRIPTION_CHARGE_CHANGE);
        assertThat(instance2.getEntityId()).isEqualTo(ENTITY_ID);
        assertThat(instance2.getStatus()).isEqualTo(NotificationInstanceStatus.CREATED);
        assertThat(instance2.getDeliveryAttempts()).isZero();
        assertThat(instance2.getPayload()).isEqualTo(MESSAGE_BODY);
    }

    @Test
    void sendTestNotificationShouldFilterEventsWhenNoMatchingNotificationsFound() {
        NotificationTarget target = createTestTarget();
        when(notificationDAO.getNotificationTargetById(TARGET_UUID)).thenReturn(Optional.of(target));

        Event event1 = createTestEvent(EventType.SUBSCRIPTION_ACTIVATED);
        Event event2 = createTestEvent(EventType.SUBSCRIPTION_CHARGE_CHANGE);

        when(notificationEventAdapter.buildNotificationForEvent(any())).thenReturn(Optional.empty());

        List<Event> events = List.of(event1, event2);

        testNotificationService.sendTestNotification(TARGET_ID, events);

        verify(notificationDAO).getNotificationTargetById(TARGET_UUID);
        verify(notificationEventAdapter).buildNotificationForEvent(event1);
        verify(notificationEventAdapter).buildNotificationForEvent(event2);

        verify(notificationService, never()).sendNotificationWithProcessor(any(), any());
    }

    @Test
    void sendTestNotificationShouldFilterSomeEventsWhenSomeMatchingNotificationsFound() {
        NotificationTarget target = createTestTarget();
        when(notificationDAO.getNotificationTargetById(TARGET_UUID)).thenReturn(Optional.of(target));

        Event event1 = createTestEvent(EventType.SUBSCRIPTION_ACTIVATED);
        Event event2 = createTestEvent(EventType.SUBSCRIPTION_CHARGE_CHANGE);

        Notification notification1 = createTestNotification(NotificationEventType.SUBSCRIPTION_ACTIVATED);
        when(notificationEventAdapter.buildNotificationForEvent(event1)).thenReturn(Optional.of(notification1));
        when(notificationEventAdapter.buildNotificationForEvent(event2)).thenReturn(Optional.empty());

        List<Event> events = List.of(event1, event2);

        testNotificationService.sendTestNotification(TARGET_ID, events);

        verify(notificationDAO).getNotificationTargetById(TARGET_UUID);
        verify(notificationEventAdapter).buildNotificationForEvent(event1);
        verify(notificationEventAdapter).buildNotificationForEvent(event2);

        ArgumentCaptor<NotificationInstance> instanceCaptor = ArgumentCaptor.forClass(NotificationInstance.class);
        verify(notificationService, times(1)).sendNotificationWithProcessor(eq(target), instanceCaptor.capture());

        NotificationInstance capturedInstance = instanceCaptor.getValue();
        assertThat(capturedInstance.getEventType()).isEqualTo(NotificationEventType.SUBSCRIPTION_ACTIVATED);
    }

    private NotificationTarget createTestTarget() {
        return new NotificationTarget(TARGET_UUID, "arn", NotificationTargetType.WEBHOOK, "http://test.com");
    }

    private Event createTestEvent(EventType eventType) {
        return Event.builder()
            .id(EVENT_ID)
            .tenantId(TENANT_ID)
            .partitionKey(new DefaultStreamPartitionKey(TENANT_ID))
            .generatedTime(fixedClock.instant())
            .type(eventType)
            .sequenceNumber(0L)
            .payload(ByteBuffer.wrap(MESSAGE_BODY.getBytes(StandardCharsets.UTF_8)))
            .status(EventStatus.STREAMED)
            .build();
    }

    private Notification createTestNotification(NotificationEventType eventType) {
        return ImmutableNotification.builder()
            .notificationEventType(eventType)
            .entityId(ENTITY_ID)
            .tenantId(TENANT_ID)
            .messageBody(MESSAGE_BODY)
            .sourceEventId(EVENT_ID)
            .build();
    }
}
