package com.subskribe.billy.notification.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.auth.apikey.SecureRandomKeyGenerator;
import com.subskribe.billy.notification.model.ImmutableWebhookSignature;
import com.subskribe.billy.notification.model.WebhookSignature;
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class WebhookSigningServiceTest {

    private WebhookSigningKeyService webhookSigningKeyService;
    private String signingKey;

    private WebhookSigningService webhookSigningService;

    private static final Instant now = Instant.now();
    private static final String TARGET_ID = UUID.randomUUID().toString();
    private static final String JSON_PAYLOAD = "{\"foo\":\"bar\"}";

    @BeforeEach
    void setUp() {
        webhookSigningKeyService = mock(WebhookSigningKeyService.class);
        Clock clock = Clock.fixed(now, ZoneId.of("UTC"));
        signingKey = new SecureRandomKeyGenerator().generateKey();

        webhookSigningService = new WebhookSigningService(webhookSigningKeyService, clock);
    }

    @Test
    void testSignPayloadForTargetReturnsEmptyIfNoKeyFound() {
        when(webhookSigningKeyService.getSigningKeyForTarget(TARGET_ID)).thenReturn(Optional.empty());

        Optional<WebhookSignature> result = webhookSigningService.signPayloadForTarget(TARGET_ID, JSON_PAYLOAD);

        assertThat(result).isEmpty();
    }

    @Test
    void testSignPayloadForTargetReturnsCorrectSignature() {
        when(webhookSigningKeyService.getSigningKeyForTarget(TARGET_ID)).thenReturn(Optional.of(signingKey));

        Optional<WebhookSignature> result = webhookSigningService.signPayloadForTarget(TARGET_ID, JSON_PAYLOAD);

        assertThat(result).isPresent();
        WebhookSignature signature = result.get();
        assertThat(signature).isEqualTo(otherSignature());
    }

    // Using a different signing method then we use in code to make sure it matches
    private WebhookSignature otherSignature() {
        String payload = String.format("%s.%s", now.getEpochSecond(), JSON_PAYLOAD);
        String signature = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, signingKey).hmacHex(payload);
        return ImmutableWebhookSignature.builder().signature(signature).timestamp(now.getEpochSecond()).build();
    }
}
