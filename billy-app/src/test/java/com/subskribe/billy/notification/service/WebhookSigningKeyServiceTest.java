package com.subskribe.billy.notification.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.auth.apikey.SecureRandomKeyGenerator;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.aws.secretsmanager.model.SecretType;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.notification.db.NotificationDAO;
import com.subskribe.billy.notification.model.NotificationTargetAndSubscriptions;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class WebhookSigningKeyServiceTest {

    private SecretsService secretsService;
    private SecureRandomKeyGenerator secureRandomKeyGenerator;
    private NotificationDAO notificationDAO;

    private WebhookSigningKeyService webhookSigningKeyService;

    private static final String TENANT_ID = UUID.randomUUID().toString();

    @BeforeEach
    void setUp() {
        secretsService = mock(SecretsService.class);
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        secureRandomKeyGenerator = mock(SecureRandomKeyGenerator.class);
        notificationDAO = mock(NotificationDAO.class);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        webhookSigningKeyService = new WebhookSigningKeyService(secretsService, tenantIdProvider, secureRandomKeyGenerator, notificationDAO);
    }

    @Test
    void testCreateSigningKeyForTargetIdFailsIfTargetNotFound() {
        UUID targetId = UUID.randomUUID();
        when(notificationDAO.getNotificationSubscriptionsById(targetId, true)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> webhookSigningKeyService.createSigningKeyForTargetId(targetId.toString())).isInstanceOf(
            ObjectNotFoundException.class
        );
    }

    @Test
    void testCreateSigningKeyForTargetId() {
        UUID targetId = UUID.randomUUID();
        String key = RandomStringUtils.randomAlphanumeric(32);
        String expectedSecretName = "webhook-" + TENANT_ID + "-" + targetId;
        String expectedDescription = "Webhook signing key for tenant id " + TENANT_ID + " and notification target id " + targetId;
        when(secureRandomKeyGenerator.generateKey()).thenReturn(key);
        when(notificationDAO.getNotificationSubscriptionsById(targetId, true)).thenReturn(
            Optional.of(mock(NotificationTargetAndSubscriptions.class))
        );

        webhookSigningKeyService.createSigningKeyForTargetId(targetId.toString());

        verify(secretsService, times(1)).createSecret(expectedSecretName, key, expectedDescription);
    }

    @Test
    void testGetSigningKeyForTargetDelegatesToSecretServiceWithCaching() {
        String targetId = UUID.randomUUID().toString();
        String key = RandomStringUtils.randomAlphanumeric(32);
        String expectedSecretName = "webhook-" + TENANT_ID + "-" + targetId;

        when(secretsService.retrieveSecretOptional(expectedSecretName, SecretType.WEBHOOK_SIGNING_KEY)).thenReturn(Optional.of(key));

        Optional<String> firstResult = webhookSigningKeyService.getSigningKeyForTarget(targetId);
        Optional<String> secondResult = webhookSigningKeyService.getSigningKeyForTarget(targetId);

        assertThat(firstResult).isPresent();
        assertThat(secondResult).isPresent();
        assertThat(key).isEqualTo(firstResult.get()).isEqualTo(secondResult.get());

        verify(secretsService, times(1)).retrieveSecretOptional(expectedSecretName, SecretType.WEBHOOK_SIGNING_KEY);
    }

    @Test
    void testDeleteSigningKeyForTargetThrowsIfTargetNotFound() {
        UUID targetId = UUID.randomUUID();
        when(notificationDAO.getNotificationSubscriptionsById(targetId, true)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> webhookSigningKeyService.deleteSigningKeyForTarget(targetId.toString())).isInstanceOf(ObjectNotFoundException.class);
    }

    @Test
    void testDeleteSigningKeyForTargetDelegatesToSecretsService() {
        UUID targetId = UUID.randomUUID();
        String expectedSecretName = "webhook-" + TENANT_ID + "-" + targetId;
        when(notificationDAO.getNotificationSubscriptionsById(targetId, true)).thenReturn(
            Optional.of(mock(NotificationTargetAndSubscriptions.class))
        );

        webhookSigningKeyService.deleteSigningKeyForTarget(targetId.toString());

        verify(secretsService, times(1)).deleteSecret(expectedSecretName);
    }

    @Test
    void testDeleteSigningKeyForTargetDoesNotRethrowExceptions() {
        UUID targetId = UUID.randomUUID();
        String expectedSecretName = "webhook-" + TENANT_ID + "-" + targetId;
        when(notificationDAO.getNotificationSubscriptionsById(targetId, true)).thenReturn(
            Optional.of(mock(NotificationTargetAndSubscriptions.class))
        );

        doThrow(new RuntimeException("test")).when(secretsService).deleteSecret(expectedSecretName);

        assertThatNoException().isThrownBy(() -> webhookSigningKeyService.deleteSigningKeyForTarget(targetId.toString()));
    }

    @Test
    void testGetOrCreateDoesCreateThrowsIfTargetNotFound() {
        UUID targetId = UUID.randomUUID();

        when(notificationDAO.getNotificationSubscriptionsById(targetId, true)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> webhookSigningKeyService.getOrCreateSigningKeyForTargetId(targetId.toString())).isInstanceOf(
            ObjectNotFoundException.class
        );
    }

    @Test
    void testGetOrCreateDoesCreateIfNotExists() {
        UUID targetId = UUID.randomUUID();
        String key = RandomStringUtils.randomAlphanumeric(32);
        String expectedSecretName = "webhook-" + TENANT_ID + "-" + targetId;
        String expectedDescription = "Webhook signing key for tenant id " + TENANT_ID + " and notification target id " + targetId;
        when(secureRandomKeyGenerator.generateKey()).thenReturn(key);
        when(secretsService.retrieveSecretOptional(expectedSecretName, SecretType.WEBHOOK_SIGNING_KEY)).thenReturn(Optional.empty());
        when(notificationDAO.getNotificationSubscriptionsById(targetId, true)).thenReturn(
            Optional.of(mock(NotificationTargetAndSubscriptions.class))
        );

        String result = webhookSigningKeyService.getOrCreateSigningKeyForTargetId(targetId.toString());

        assertThat(result).isEqualTo(key);
        verify(secretsService, times(1)).createSecret(expectedSecretName, key, expectedDescription);
    }

    @Test
    void testGetOrCreateReturnsExistingIfExists() {
        UUID targetId = UUID.randomUUID();
        String key = RandomStringUtils.randomAlphanumeric(32);
        String expectedSecretName = "webhook-" + TENANT_ID + "-" + targetId;
        when(secretsService.retrieveSecretOptional(expectedSecretName, SecretType.WEBHOOK_SIGNING_KEY)).thenReturn(Optional.of(key));
        when(notificationDAO.getNotificationSubscriptionsById(targetId, true)).thenReturn(
            Optional.of(mock(NotificationTargetAndSubscriptions.class))
        );

        String result = webhookSigningKeyService.getOrCreateSigningKeyForTargetId(targetId.toString());

        assertThat(result).isEqualTo(key);

        verify(secretsService, never()).createSecret(anyString(), anyString(), anyString());
    }
}
