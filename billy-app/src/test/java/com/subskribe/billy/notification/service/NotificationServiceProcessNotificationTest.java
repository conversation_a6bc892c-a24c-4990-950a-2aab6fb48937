package com.subskribe.billy.notification.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.notification.db.NotificationInstanceDAO;
import com.subskribe.billy.notification.model.ImmutableNotificationDeliveryAttempt;
import com.subskribe.billy.notification.model.ImmutableNotificationInstance;
import com.subskribe.billy.notification.model.ImmutableNotificationProcessingResult;
import com.subskribe.billy.notification.model.NotificationConfiguration;
import com.subskribe.billy.notification.model.NotificationDeliveryAttempt;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.notification.model.NotificationInstanceStatus;
import com.subskribe.billy.notification.model.NotificationProcessingResult;
import com.subskribe.billy.notification.model.NotificationTarget;
import com.subskribe.billy.notification.model.NotificationTargetType;
import com.subskribe.billy.notification.service.email.EmailNotificationProcessor;
import java.time.Clock;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class NotificationServiceProcessNotificationTest {

    @Mock
    private NotificationInstanceDAO notificationInstanceDAO;

    @Mock
    private NotificationTargetService notificationTargetService;

    @Mock
    private SlackNotificationProcessor slackNotificationProcessor;

    @Mock
    private WebhookNotificationProcessor webhookNotificationProcessor;

    @Mock
    private EmailNotificationProcessor emailNotificationProcessor;

    @Mock
    private NotificationConfiguration notificationConfiguration;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private FeatureService featureService;

    @Mock
    private Clock clock;

    @Captor
    private ArgumentCaptor<NotificationDeliveryAttempt> deliveryAttemptCaptor;

    private NotificationService notificationService;

    private NotificationTarget notificationTarget;
    private NotificationInstance notificationInstance;
    private static final UUID NOTIFICATION_ID = UUID.randomUUID();
    private static final UUID INSTANCE_ID = UUID.randomUUID();
    private static final Instant NOW = Instant.now();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        when(billyConfiguration.getNotificationConfiguration()).thenReturn(notificationConfiguration);
        when(notificationConfiguration.getNotificationRetryIntervals()).thenReturn(List.of(5, 15, 60));

        when(slackNotificationProcessor.getTargetType()).thenReturn(NotificationTargetType.SLACK);
        when(webhookNotificationProcessor.getTargetType()).thenReturn(NotificationTargetType.WEBHOOK);
        when(emailNotificationProcessor.getTargetType()).thenReturn(NotificationTargetType.EMAIL);
        when(clock.instant()).thenReturn(NOW);

        notificationService = new NotificationService(
            notificationInstanceDAO,
            notificationTargetService,
            slackNotificationProcessor,
            webhookNotificationProcessor,
            emailNotificationProcessor,
            billyConfiguration,
            featureService,
            clock
        );

        notificationTarget = new NotificationTarget(NOTIFICATION_ID, "topic-arn", NotificationTargetType.EMAIL, "<EMAIL>");

        notificationInstance = ImmutableNotificationInstance.builder()
            .id(INSTANCE_ID)
            .notificationId(NOTIFICATION_ID)
            .entityId("ORDER-123")
            .eventType(NotificationEventType.ORDER_EXECUTED)
            .payload("{\"orderId\":\"ORDER-123\"}")
            .status(NotificationInstanceStatus.CREATED)
            .deliveryAttempts(0)
            .build();
    }

    @Test
    void shouldProcessNotificationSuccessfullyAndMarkDelivered() {
        NotificationProcessingResult successResult = ImmutableNotificationProcessingResult.builder().isSuccess(true).statusCode(200).build();
        when(emailNotificationProcessor.processNotification(notificationTarget, notificationInstance)).thenReturn(successResult);

        notificationService.processNotification(notificationTarget, notificationInstance);

        NotificationInstance updatedInstance = ImmutableNotificationInstance.builder()
            .from(notificationInstance)
            .status(NotificationInstanceStatus.DELIVERED)
            .deliveryAttempts(1)
            .lastDeliveryAttempt(NOW)
            .build();
        NotificationDeliveryAttempt expectedAttempt = ImmutableNotificationDeliveryAttempt.builder()
            .id(UUID.randomUUID())
            .notificationInstanceId(notificationInstance.getId())
            .httpStatusCode(200)
            .isSuccess(true)
            .build();

        verify(notificationInstanceDAO).logNotificationDeliveryAttempt(eq(updatedInstance), deliveryAttemptCaptor.capture());
        assertThat(deliveryAttemptCaptor.getValue()).usingRecursiveComparison().ignoringFields("id").isEqualTo(expectedAttempt);
    }

    @Test
    void shouldProcessNotificationWithFailureAndMarkAttempted() {
        NotificationProcessingResult failureResult = ImmutableNotificationProcessingResult.builder()
            .isSuccess(false)
            .isRetryable(true)
            .errorMessage("Connection timeout")
            .statusCode(504)
            .build();
        when(emailNotificationProcessor.processNotification(notificationTarget, notificationInstance)).thenReturn(failureResult);

        notificationService.processNotification(notificationTarget, notificationInstance);

        NotificationInstance updatedInstance = ImmutableNotificationInstance.builder()
            .from(notificationInstance)
            .status(NotificationInstanceStatus.ATTEMPTED)
            .deliveryAttempts(1)
            .lastDeliveryAttempt(NOW)
            .build();
        NotificationDeliveryAttempt expectedAttempt = ImmutableNotificationDeliveryAttempt.builder()
            .id(UUID.randomUUID())
            .notificationInstanceId(notificationInstance.getId())
            .httpStatusCode(504)
            .isSuccess(false)
            .errorMessage("Connection timeout")
            .build();

        verify(notificationInstanceDAO).logNotificationDeliveryAttempt(eq(updatedInstance), deliveryAttemptCaptor.capture());

        assertThat(deliveryAttemptCaptor.getValue()).usingRecursiveComparison().ignoringFields("id").isEqualTo(expectedAttempt);
    }

    @Test
    void shouldProcessNotificationWithNonRetryableFailureAndMarkFailed() {
        NotificationProcessingResult nonRetryableFailure = ImmutableNotificationProcessingResult.builder()
            .isSuccess(false)
            .isRetryable(false)
            .errorMessage("Invalid format")
            .build();
        when(emailNotificationProcessor.processNotification(notificationTarget, notificationInstance)).thenReturn(nonRetryableFailure);

        notificationService.processNotification(notificationTarget, notificationInstance);

        NotificationInstance updatedInstance = ImmutableNotificationInstance.builder()
            .from(notificationInstance)
            .status(NotificationInstanceStatus.FAILED)
            .deliveryAttempts(1)
            .lastDeliveryAttempt(NOW)
            .build();
        NotificationDeliveryAttempt expectedAttempt = ImmutableNotificationDeliveryAttempt.builder()
            .id(UUID.randomUUID())
            .notificationInstanceId(notificationInstance.getId())
            .isSuccess(false)
            .errorMessage("Invalid format")
            .build();

        verify(notificationInstanceDAO).logNotificationDeliveryAttempt(eq(updatedInstance), deliveryAttemptCaptor.capture());
        assertThat(deliveryAttemptCaptor.getValue()).usingRecursiveComparison().ignoringFields("id").isEqualTo(expectedAttempt);
    }

    @Test
    void shouldMarkFailedWhenMaxAttemptsReached() {
        NotificationInstance instanceWithMaxAttempts = ImmutableNotificationInstance.builder().from(notificationInstance).deliveryAttempts(3).build();

        NotificationProcessingResult failureResult = ImmutableNotificationProcessingResult.builder()
            .isSuccess(false)
            .isRetryable(true)
            .errorMessage("Connection timeout")
            .build();
        when(emailNotificationProcessor.processNotification(notificationTarget, instanceWithMaxAttempts)).thenReturn(failureResult);

        notificationService.processNotification(notificationTarget, instanceWithMaxAttempts);

        NotificationInstance updatedInstance = ImmutableNotificationInstance.builder()
            .from(notificationInstance)
            .status(NotificationInstanceStatus.FAILED)
            .deliveryAttempts(4)
            .lastDeliveryAttempt(NOW)
            .build();
        NotificationDeliveryAttempt expectedAttempt = ImmutableNotificationDeliveryAttempt.builder()
            .id(UUID.randomUUID())
            .notificationInstanceId(notificationInstance.getId())
            .isSuccess(false)
            .errorMessage("Connection timeout")
            .build();

        verify(notificationInstanceDAO).logNotificationDeliveryAttempt(eq(updatedInstance), deliveryAttemptCaptor.capture());
        assertThat(deliveryAttemptCaptor.getValue()).usingRecursiveComparison().ignoringFields("id").isEqualTo(expectedAttempt);
    }
}
