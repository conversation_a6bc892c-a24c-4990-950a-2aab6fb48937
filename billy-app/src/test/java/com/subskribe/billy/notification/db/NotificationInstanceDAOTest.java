package com.subskribe.billy.notification.db;

import static com.subskribe.billy.jooq.default_schema.Tables.NOTIFICATION_INSTANCE;
import static com.subskribe.billy.jooq.default_schema.tables.NotificationDeliveryAttempt.NOTIFICATION_DELIVERY_ATTEMPT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.jooq.impl.DSL.noCondition;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.jooq.default_schema.tables.records.NotificationDeliveryAttemptRecord;
import com.subskribe.billy.jooq.default_schema.tables.records.NotificationInstanceRecord;
import com.subskribe.billy.notification.model.ImmutableNotificationDeliveryAttempt;
import com.subskribe.billy.notification.model.ImmutableNotificationInstance;
import com.subskribe.billy.notification.model.NotificationEventType;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.notification.model.NotificationInstanceStatus;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.jooq.MockDSLBuilder;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.groovy.util.Maps;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NotificationInstanceDAOTest {

    private DSLContextProvider dslContextProvider;
    private Clock clock;
    private static final Instant NOW = Instant.now();
    private static final String TENANT_ID = UUID.randomUUID().toString();

    private NotificationInstanceDAO notificationInstanceDAO;

    @BeforeEach
    void setUp() {
        dslContextProvider = mock(DSLContextProvider.class);
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        clock = Clock.fixed(NOW, ZoneId.of("UTC"));

        notificationInstanceDAO = new NotificationInstanceDAO(dslContextProvider, tenantIdProvider, clock);

        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
    }

    @Test
    void getNotificationsOverdueForRetry() {
        Map<Integer, Integer> attemptsToInternalMap = Maps.of(1, 10, 2, 30);
        LocalDateTime firstInterval = LocalDateTime.now(clock).minusMinutes(10);
        LocalDateTime secondInterval = LocalDateTime.now(clock).minusMinutes(30);
        NotificationInstanceRecord expectedRecord = buildInstanceRecord();

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .select()
                    .from(NOTIFICATION_INSTANCE)
                    .where(NOTIFICATION_INSTANCE.TENANT_ID.eq(TENANT_ID))
                    .and(NOTIFICATION_INSTANCE.STATUS.eq(NotificationInstanceStatus.ATTEMPTED.name()))
                    .and(
                        noCondition()
                            .or(NOTIFICATION_INSTANCE.DELIVERY_ATTEMPTS.eq(1).and(NOTIFICATION_INSTANCE.LAST_DELIVERY_ATTEMPT.le(firstInterval)))
                            .or(NOTIFICATION_INSTANCE.DELIVERY_ATTEMPTS.eq(2).and(NOTIFICATION_INSTANCE.LAST_DELIVERY_ATTEMPT.le(secondInterval)))
                    )
            )
            .thenReturn(expectedRecord)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        List<NotificationInstance> result = notificationInstanceDAO.getNotificationsOverdueForRetry(attemptsToInternalMap);

        assertThat(result).isEqualTo(List.of(buildInstance(expectedRecord)));
    }

    @Test
    void testGetNotificationInstanceByIdReturnsFoundObject() {
        String instanceId = UUID.randomUUID().toString();
        NotificationInstanceRecord record = buildInstanceRecord();

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .selectFrom(com.subskribe.billy.jooq.default_schema.tables.NotificationInstance.NOTIFICATION_INSTANCE)
                    .where(com.subskribe.billy.jooq.default_schema.tables.NotificationInstance.NOTIFICATION_INSTANCE.TENANT_ID.eq(TENANT_ID))
                    .and(com.subskribe.billy.jooq.default_schema.tables.NotificationInstance.NOTIFICATION_INSTANCE.ID.eq(UUID.fromString(instanceId)))
            )
            .thenReturn(record)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        Optional<NotificationInstance> found = notificationInstanceDAO.getNotificationInstanceById(instanceId);

        assertThat(found).isPresent().contains(buildInstance(record));
    }

    @Test
    void testGetNotificationInstanceByIdReturnsEmptyOptionalIfNotFound() {
        String instanceId = UUID.randomUUID().toString();

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .selectFrom(com.subskribe.billy.jooq.default_schema.tables.NotificationInstance.NOTIFICATION_INSTANCE)
                    .where(com.subskribe.billy.jooq.default_schema.tables.NotificationInstance.NOTIFICATION_INSTANCE.TENANT_ID.eq(TENANT_ID))
                    .and(com.subskribe.billy.jooq.default_schema.tables.NotificationInstance.NOTIFICATION_INSTANCE.ID.eq(UUID.fromString(instanceId)))
            )
            .thenReturnNothingFor(com.subskribe.billy.jooq.default_schema.tables.NotificationInstance.NOTIFICATION_INSTANCE)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        Optional<NotificationInstance> found = notificationInstanceDAO.getNotificationInstanceById(instanceId);

        assertThat(found).isEmpty();
    }

    @Test
    void testGetDeliveryAttempts() {
        String instanceId = UUID.randomUUID().toString();
        NotificationDeliveryAttemptRecord record = buildDeliveryAttemptRecord();

        DSLContext dslContext = new MockDSLBuilder()
            .when(
                DSL.using(SQLDialect.POSTGRES)
                    .selectFrom(NOTIFICATION_DELIVERY_ATTEMPT)
                    .where(NOTIFICATION_DELIVERY_ATTEMPT.TENANT_ID.eq(TENANT_ID))
                    .and(NOTIFICATION_DELIVERY_ATTEMPT.NOTIFICATION_INSTANCE_ID.eq(UUID.fromString(instanceId)))
            )
            .thenReturn(record)
            .context();
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        List<com.subskribe.billy.notification.model.NotificationDeliveryAttempt> found = notificationInstanceDAO.getNotificationDeliveryAttempts(
            instanceId
        );

        assertThat(found).containsExactlyInAnyOrderElementsOf(List.of(buildDeliveryAttempt(record)));
    }

    private NotificationInstanceRecord buildInstanceRecord() {
        NotificationInstanceRecord notificationInstanceRecord = new NotificationInstanceRecord();
        notificationInstanceRecord.setId(UUID.randomUUID());
        notificationInstanceRecord.setNotificationId(UUID.randomUUID());
        notificationInstanceRecord.setEntityId(RandomStringUtils.randomAlphanumeric(10));
        notificationInstanceRecord.setEventType(NotificationEventType.INVOICE_POSTED.name());
        notificationInstanceRecord.setPayload(RandomStringUtils.randomAlphanumeric(100));
        notificationInstanceRecord.setStatus(NotificationInstanceStatus.ATTEMPTED.name());
        notificationInstanceRecord.setDeliveryAttempts(1);
        notificationInstanceRecord.setLastDeliveryAttempt(LocalDateTime.now(clock));
        return notificationInstanceRecord;
    }

    private NotificationDeliveryAttemptRecord buildDeliveryAttemptRecord() {
        NotificationDeliveryAttemptRecord notificationDeliveryAttemptRecord = new NotificationDeliveryAttemptRecord();
        notificationDeliveryAttemptRecord.setNotificationInstanceId(UUID.randomUUID());
        notificationDeliveryAttemptRecord.setId(UUID.randomUUID());
        notificationDeliveryAttemptRecord.setTenantId(TENANT_ID);
        notificationDeliveryAttemptRecord.setErrorMessage("error");
        notificationDeliveryAttemptRecord.setHttpStatusCode(500);
        notificationDeliveryAttemptRecord.setIsSuccess(false);
        return notificationDeliveryAttemptRecord;
    }

    private com.subskribe.billy.notification.model.NotificationDeliveryAttempt buildDeliveryAttempt(NotificationDeliveryAttemptRecord record) {
        return ImmutableNotificationDeliveryAttempt.builder()
            .id(record.getId())
            .notificationInstanceId(record.getNotificationInstanceId())
            .httpStatusCode(record.getHttpStatusCode())
            .errorMessage(record.getErrorMessage())
            .isSuccess(record.getIsSuccess())
            .build();
    }

    private NotificationInstance buildInstance(NotificationInstanceRecord withRecord) {
        return ImmutableNotificationInstance.builder()
            .notificationId(withRecord.getNotificationId())
            .id(withRecord.getId())
            .entityId(withRecord.getEntityId())
            .eventType(NotificationEventType.INVOICE_POSTED)
            .payload(withRecord.getPayload())
            .status(NotificationInstanceStatus.ATTEMPTED)
            .deliveryAttempts(1)
            .lastDeliveryAttempt(NOW)
            .build();
    }
}
