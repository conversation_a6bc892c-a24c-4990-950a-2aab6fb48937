package com.subskribe.billy.notification.service;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.notification.db.NotificationDAO;
import com.subskribe.billy.notification.db.NotificationInstanceDAO;
import com.subskribe.billy.notification.model.ImmutableNotificationInstance;
import com.subskribe.billy.notification.model.NotificationConfiguration;
import com.subskribe.billy.notification.model.NotificationInstance;
import com.subskribe.billy.notification.model.NotificationInstanceStatus;
import com.subskribe.billy.notification.model.NotificationTarget;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NotificationRetryServiceTest {

    private NotificationInstanceDAO notificationInstanceDAO;

    private NotificationDAO notificationDAO;

    private NotificationService notificationService;

    private NotificationRetryService notificationRetryService;

    @BeforeEach
    void setUp() {
        notificationInstanceDAO = mock(NotificationInstanceDAO.class);
        notificationDAO = mock(NotificationDAO.class);
        notificationService = mock(NotificationService.class);
        NotificationConfiguration notificationConfiguration = buildNotificationConfiguration(List.of(1, 10));

        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);

        when(billyConfiguration.getNotificationConfiguration()).thenReturn(notificationConfiguration);

        notificationRetryService = new NotificationRetryService(notificationInstanceDAO, notificationDAO, notificationService, billyConfiguration);
    }

    @Test
    void testNotificationIsMarkedAsFailedIfTargetNoLongerExists() {
        NotificationInstance notificationInstance = NotificationStubs.buildNotificationInstance();
        when(notificationInstanceDAO.getNotificationsOverdueForRetry(expectedIntervalMap())).thenReturn(List.of(notificationInstance));
        when(notificationDAO.getNotificationTargetsByIdSet(Set.of(notificationInstance.getNotificationId()))).thenReturn(Set.of());

        notificationRetryService.retryFailedNotifications();

        ImmutableNotificationInstance expectedInstance = ImmutableNotificationInstance.builder()
            .from(notificationInstance)
            .status(NotificationInstanceStatus.FAILED)
            .build();

        verify(notificationInstanceDAO, times(1)).updateNotificationInstance(expectedInstance);
        verifyNoInteractions(notificationService);
    }

    @Test
    void testNotificationIsDelegatedToNotificationServiceIfTargetIsFound() {
        NotificationInstance notificationInstance = NotificationStubs.buildNotificationInstance();
        NotificationTarget notificationTarget = NotificationStubs.buildNotificationTarget(notificationInstance.getNotificationId());
        when(notificationInstanceDAO.getNotificationsOverdueForRetry(expectedIntervalMap())).thenReturn(List.of(notificationInstance));
        when(notificationDAO.getNotificationTargetsByIdSet(Set.of(notificationInstance.getNotificationId()))).thenReturn(Set.of(notificationTarget));

        notificationRetryService.retryFailedNotifications();

        verify(notificationService, times(1)).processNotification(notificationTarget, notificationInstance);
        verify(notificationInstanceDAO, never()).updateNotificationInstance(any(NotificationInstance.class));
    }

    @Test
    void testManualRetryThrowsObjectNotFoundExceptionIfInstanceNotFound() {
        String instanceId = UUID.randomUUID().toString();
        when(notificationInstanceDAO.getNotificationInstanceById(instanceId)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> notificationRetryService.manualRetry(instanceId))
            .isInstanceOf(ObjectNotFoundException.class)
            .hasMessageContaining(instanceId);
    }

    @Test
    void testManualRetryThrowsObjectNotFoundExceptionIfTargetNotFound() {
        String instanceId = UUID.randomUUID().toString();
        NotificationInstance notificationInstance = NotificationStubs.buildNotificationInstance();

        when(notificationInstanceDAO.getNotificationInstanceById(instanceId)).thenReturn(Optional.of(notificationInstance));
        when(notificationDAO.getNotificationTargetById(notificationInstance.getNotificationId())).thenReturn(Optional.empty());

        assertThatThrownBy(() -> notificationRetryService.manualRetry(instanceId))
            .isInstanceOf(ObjectNotFoundException.class)
            .hasMessageContaining(notificationInstance.getNotificationId().toString());
    }

    @Test
    void testManualRetryDelegatesToNotificationServiceCorrectly() {
        String instanceId = UUID.randomUUID().toString();
        NotificationInstance notificationInstance = NotificationStubs.buildNotificationInstance();
        NotificationTarget notificationTarget = NotificationStubs.buildNotificationTarget(notificationInstance.getNotificationId());

        when(notificationInstanceDAO.getNotificationInstanceById(instanceId)).thenReturn(Optional.of(notificationInstance));
        when(notificationDAO.getNotificationTargetById(notificationInstance.getNotificationId())).thenReturn(Optional.of(notificationTarget));

        notificationRetryService.manualRetry(instanceId);

        verify(notificationService, times(1)).processNotification(notificationTarget, notificationInstance);
    }

    private Map<Integer, Integer> expectedIntervalMap() {
        return Map.of(1, 1, 2, 10);
    }

    private NotificationConfiguration buildNotificationConfiguration(List<Integer> intervals) {
        NotificationConfiguration notificationConfiguration = new NotificationConfiguration();
        notificationConfiguration.setNotificationRetryIntervals(intervals);
        return notificationConfiguration;
    }
}
