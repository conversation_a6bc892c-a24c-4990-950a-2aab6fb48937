package com.subskribe.billy.hubspot;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.hubspot.model.HubSpotPaymentFrequency;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.shared.enums.Cycle;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

public class HubSpotFieldMappingTest {

    private static final String HUBSPOT_MONTHLY = "Monthly";
    private static final String HUBSPOT_QUARTERLY = "Quarterly";
    private static final String HUBSPOT_ANNUALLY = "Annual";
    private static final Cycle CYCLE_MONTHLY = Cycle.MONTH;
    private static final Cycle CYCLE_QUARTERLY = Cycle.QUARTER;
    private static final Cycle CYCLE_ANNUALLY = Cycle.YEAR;

    private static final PaymentTerm NET_30_PAYMENT_TERM = new PaymentTerm("NET30");
    private static final String NET_30_HUBSPOT_NAME = "Net30";

    public static final String PROPER_INVOICE_NUMBER = "INV-123456";
    public static final String QBO_APPENDED_INVOICE_NUMBER = "QBO-INV-123456";
    public static final String QA_APPENDED_INVOICE_NUMBER = "QA-INV-123456";

    @Test
    public void testHubSpotNameToCycle() {
        assertEquals(CYCLE_MONTHLY, HubSpotPaymentFrequency.hubSpotNameToCycle(HUBSPOT_MONTHLY));
        assertEquals(CYCLE_QUARTERLY, HubSpotPaymentFrequency.hubSpotNameToCycle(HUBSPOT_QUARTERLY));
        assertEquals(CYCLE_ANNUALLY, HubSpotPaymentFrequency.hubSpotNameToCycle(HUBSPOT_ANNUALLY));
        assertEquals(CYCLE_ANNUALLY, HubSpotPaymentFrequency.hubSpotNameToCycle(null));
    }

    @Test
    public void testPaymentTermsToHubSpotName() {
        assertEquals(NET_30_HUBSPOT_NAME, StringUtils.deleteWhitespace(NET_30_PAYMENT_TERM.getDisplayName()));
    }

    @Test
    public void testParseInvoiceName() {
        assertEquals(PROPER_INVOICE_NUMBER, HubSpotService.formatInvoiceNumber(QBO_APPENDED_INVOICE_NUMBER));
        assertEquals(PROPER_INVOICE_NUMBER, HubSpotService.formatInvoiceNumber(QA_APPENDED_INVOICE_NUMBER));
        assertEquals(PROPER_INVOICE_NUMBER, HubSpotService.formatInvoiceNumber(PROPER_INVOICE_NUMBER));
    }
}
