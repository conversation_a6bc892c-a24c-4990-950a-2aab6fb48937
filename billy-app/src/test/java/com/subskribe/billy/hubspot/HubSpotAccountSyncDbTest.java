package com.subskribe.billy.hubspot;

import static com.subskribe.billy.payment.model.PaymentConfiguration.DEFAULT_SUPPORTED_PAYMENT_TYPES;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.account.services.MockAccountServiceBuilder;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.compositeorder.model.CancelAndRestructureOrderDataAggregator;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.service.CrmFieldMappingService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.exception.ModelValidationException;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.graphql.order.OrderDataAggregator;
import com.subskribe.billy.hubspot.client.HubspotClientFactory;
import com.subskribe.billy.hubspot.db.HubSpotDAO;
import com.subskribe.billy.hubspot.model.HubSpotCompany;
import com.subskribe.billy.hubspot.model.HubSpotCompanyProperties;
import com.subskribe.billy.hubspot.model.HubSpotIntegration;
import com.subskribe.billy.hubspot.service.HubSpotGetService;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.hubspot.service.HubSpotJobQueueService;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.payment.model.PaymentConfiguration;
import com.subskribe.billy.payment.services.PaymentConfigurationService;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.lock.DynamoDBLockProvider;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.test.WithDb;
import java.io.IOException;
import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class HubSpotAccountSyncDbTest extends WithDb {

    private static final String TENANT_ID = "test tenant 1 id";
    private static final String COMPANY_NAME = "ABC Inc.";

    @Mock
    private HubSpotGetService hubSpotGetService;

    @Mock
    private OrderGetService orderGetService;

    @Mock
    private FeatureService featureService;

    @Mock
    private ProductCatalogGetService productCatalogGetService;

    @Mock
    private SubscriptionGetService subscriptionGetService;

    @Mock
    private HubSpotIntegrationService hubSpotIntegrationService;

    @Mock
    HubSpotIntegration hubSpotIntegration;

    @Mock
    private AccountGetService accountGetService;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private HubSpotDAO hubSpotDAO;

    @Mock
    private OrderDataAggregator orderDataAggregator;

    @Mock
    private OpportunityService opportunityService;

    @Mock
    private MetricsService metricsService;

    @Mock
    private CustomFieldService customFieldService;

    @Mock
    private PaymentConfigurationService paymentConfigurationService;

    @Mock
    private CompositeOrderGetService compositeOrderGetService;

    @Mock
    private CancelAndRestructureOrderDataAggregator cancelAndRestructureOrderDataAggregator;

    @Mock
    private DynamoDBLockProvider dynamoDBLockProvider;

    @Mock
    private EntityContextResolver entityContextResolver;

    @Mock
    private HubspotClientFactory hubspotClientFactory;

    @Mock
    private SecretsService secretsService;

    @Mock
    private RateCardService rateCardService;

    @Mock
    private CrmFieldMappingService crmFieldMappingService;

    @Mock
    private HubSpotJobQueueService hubSpotJobQueueService;

    @Mock
    private CurrencyConversionRateGetService currencyConversionRateGetService;

    private HubSpotService hubSpotService;

    @BeforeAll
    public void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        when(hubSpotIntegrationService.getIntegrationForTenant()).thenReturn(hubSpotIntegration);
        when(hubSpotDAO.getIntegrationByTenantId(any())).thenReturn(Optional.of(hubSpotIntegration));
        when(paymentConfigurationService.getTenantPaymentConfiguration()).thenReturn(new PaymentConfiguration(DEFAULT_SUPPORTED_PAYMENT_TYPES));
        BillyConfiguration billyConfiguration = new BillyConfiguration();

        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        var tenantSettingService = TenantSettingServiceFixture.getDefault();

        when(entityContextResolver.resolveInputEntityIdsForGlobalObject(any())).thenReturn(Set.of(EntityContext.ALL_ACCESS_ID));

        var accountService = new MockAccountServiceBuilder()
            .withTenantSettingService(tenantSettingService)
            .withTenantIdProvider(tenantIdProvider)
            .withDSLContextProvider(dslContextProvider)
            .withPaymentConfigurationService(paymentConfigurationService)
            .withEntityContextResolver(entityContextResolver)
            .build();

        hubSpotService = new HubSpotService(
            orderGetService,
            accountService,
            accountGetService,
            tenantIdProvider,
            hubSpotDAO,
            hubSpotGetService,
            hubSpotIntegrationService,
            featureService,
            orderDataAggregator,
            mock(OpportunityGetService.class),
            opportunityService,
            metricsService,
            subscriptionGetService,
            customFieldService,
            productCatalogGetService,
            billyConfiguration,
            tenantSettingService,
            compositeOrderGetService,
            cancelAndRestructureOrderDataAggregator,
            dynamoDBLockProvider,
            hubspotClientFactory,
            secretsService,
            rateCardService,
            crmFieldMappingService,
            hubSpotJobQueueService,
            currencyConversionRateGetService
        );
    }

    @Test
    public void testNameMissing() {
        Exception exception = assertThrows(ModelValidationException.class, () -> {
            HubSpotCompany company = getMockCompany();
            hubSpotService.createAccountForHubSpotCompanyIfNeeded(company);
        });
        Assertions.assertTrue(exception.getMessage().contains("name"));
    }

    @Test
    public void testSyncAccountSuccessful() {
        HubSpotCompany company = getMockCompany();
        company.getProperties().setName(COMPANY_NAME);
        Account account = hubSpotService.createAccountForHubSpotCompanyIfNeeded(company);
        assertThat(account.getAccountId()).isNotBlank();
    }

    public HubSpotCompany getMockCompany() {
        HubSpotCompany hubSpotCompany = new HubSpotCompany();
        hubSpotCompany.setId(UUID.randomUUID().toString());
        hubSpotCompany.setProperties(new HubSpotCompanyProperties());
        hubSpotCompany.setCreatedAt(Instant.now().toString());
        hubSpotCompany.setUpdatedAt(Instant.now().toString());
        hubSpotCompany.setArchived(false);
        return hubSpotCompany;
    }
}
