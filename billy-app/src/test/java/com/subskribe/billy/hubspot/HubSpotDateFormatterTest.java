package com.subskribe.billy.hubspot;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.time.Instant;
import java.util.TimeZone;
import org.junit.jupiter.api.Test;

public class HubSpotDateFormatterTest {

    private final HubSpotFormatter dateFormatter = new HubSpotFormatter(TimeZone.getTimeZone("US/Pacific"));

    private final String startDateString = dateFormatter.dateFormat(Instant.ofEpochSecond(1704096000)); // 01-01-2024 @ 00:00:00

    private final String endDateString = dateFormatter.endDateFormat(Instant.ofEpochSecond(1735675199)); // 12-31-2024 @ 23:59:59

    @Test
    public void testParseYear() {
        assertEquals("2024", dateFormatter.parseYear(startDateString));
        assertEquals("2024", dateFormatter.parseYear(endDateString));
    }
}
