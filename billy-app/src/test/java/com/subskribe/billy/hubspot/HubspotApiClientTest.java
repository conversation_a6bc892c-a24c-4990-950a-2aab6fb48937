package com.subskribe.billy.hubspot;

import static javax.ws.rs.core.Response.Status.BAD_REQUEST;
import static javax.ws.rs.core.Response.Status.CONFLICT;
import static javax.ws.rs.core.Response.Status.INTERNAL_SERVER_ERROR;
import static javax.ws.rs.core.Response.Status.NOT_FOUND;
import static javax.ws.rs.core.Response.Status.TOO_MANY_REQUESTS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.hubspot.client.HubspotApiClient;
import com.subskribe.billy.hubspot.model.HubspotRateLimitExceededException;
import java.util.Optional;
import okhttp3.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class HubspotApiClientTest {

    private static final String TEST_INVALID_RESPONSE_BODY = "test";
    private static final String TEST_RESPONSE_BODY = "{ status: 'error', message: 'resource not found' }";
    private static final String TEST_VALIDATION_ERROR_RESPONSE_BODY =
        "{ status: 'error', message: 'invalid property option', category: 'VALIDATION_ERROR' }";
    private static final String HUBSPOT_ERROR_CODE_RESPONSE_FORMAT = "Got response code: %s";
    private static final String HUBSPOT_VALIDATION_ERROR_MESSAGE = "HubSpot validation error";

    private Response responseMock;

    @BeforeEach
    public void setup() {
        responseMock = mock(Response.class);
    }

    @Test
    public void testThrowIfHubSpotRequestFailsWithValidationErrors() {
        int statusCode = CONFLICT.getStatusCode();
        boolean success = false;
        when(responseMock.code()).thenReturn(statusCode);
        try {
            HubspotApiClient.throwIfHubSpotRequestFails(responseMock, Optional.of(TEST_VALIDATION_ERROR_RESPONSE_BODY));
        } catch (ConflictingStateException e) {
            Assertions.assertTrue(e.getMessage().contains(HUBSPOT_VALIDATION_ERROR_MESSAGE));
            success = true;
        } catch (Exception ignored) {}
        Assertions.assertTrue(success);
    }

    @Test
    public void testThrowIfHubSpotRequestFailsWithInvalidResponseBody() {
        int statusCode = CONFLICT.getStatusCode();
        boolean success = false;
        when(responseMock.code()).thenReturn(statusCode);
        try {
            HubspotApiClient.throwIfHubSpotRequestFails(responseMock, Optional.of(TEST_INVALID_RESPONSE_BODY));
        } catch (ConflictingStateException e) {
            Assertions.assertTrue(e.getMessage().contains(String.format(HUBSPOT_ERROR_CODE_RESPONSE_FORMAT, statusCode)));
            success = true;
        } catch (Exception ignored) {}
        Assertions.assertTrue(success);
    }

    @Test
    public void testThrowIfHubSpotRequestFailsWithConflict() {
        int statusCode = CONFLICT.getStatusCode();
        boolean success = false;
        when(responseMock.code()).thenReturn(statusCode);
        try {
            HubspotApiClient.throwIfHubSpotRequestFails(responseMock, Optional.of(TEST_RESPONSE_BODY));
        } catch (ConflictingStateException e) {
            Assertions.assertTrue(e.getMessage().contains(String.format(HUBSPOT_ERROR_CODE_RESPONSE_FORMAT, statusCode)));
            success = true;
        } catch (Exception ignored) {}
        Assertions.assertTrue(success);
    }

    @Test
    public void testThrowIfHubSpotRequestFailsWithNotFound() {
        int statusCode = NOT_FOUND.getStatusCode();
        boolean success = false;
        when(responseMock.code()).thenReturn(statusCode);
        try {
            HubspotApiClient.throwIfHubSpotRequestFails(responseMock, Optional.of(TEST_RESPONSE_BODY));
        } catch (ObjectNotFoundException e) {
            Assertions.assertTrue(e.getMessage().contains(String.format(HUBSPOT_ERROR_CODE_RESPONSE_FORMAT, statusCode)));
            success = true;
        } catch (Exception ignored) {}
        Assertions.assertTrue(success);
    }

    @Test
    public void testThrowIfHubSpotRequestFailsWithBadRequest() {
        int statusCode = BAD_REQUEST.getStatusCode();
        boolean success = false;
        when(responseMock.code()).thenReturn(statusCode);
        try {
            HubspotApiClient.throwIfHubSpotRequestFails(responseMock, Optional.of(TEST_RESPONSE_BODY));
        } catch (InvalidInputException e) {
            Assertions.assertTrue(e.getMessage().contains(String.format(HUBSPOT_ERROR_CODE_RESPONSE_FORMAT, statusCode)));
            success = true;
        } catch (Exception ignored) {}
        Assertions.assertTrue(success);
    }

    @Test
    public void testThrowIfHubSpotRequestFailsWithTooManyRequests() {
        int statusCode = TOO_MANY_REQUESTS.getStatusCode();
        boolean success = false;
        when(responseMock.code()).thenReturn(statusCode);
        try {
            HubspotApiClient.throwIfHubSpotRequestFails(responseMock, Optional.of(TEST_RESPONSE_BODY));
        } catch (HubspotRateLimitExceededException e) {
            success = true;
        } catch (Exception ignored) {}
        Assertions.assertTrue(success);
    }

    @Test
    public void testThrowIfHubSpotRequestFailsWithInternalServerError() {
        int statusCode = INTERNAL_SERVER_ERROR.getStatusCode();
        boolean success = false;
        when(responseMock.code()).thenReturn(statusCode);
        try {
            HubspotApiClient.throwIfHubSpotRequestFails(responseMock, Optional.of(TEST_RESPONSE_BODY));
        } catch (IllegalStateException e) {
            Assertions.assertTrue(e.getMessage().contains(String.format(HUBSPOT_ERROR_CODE_RESPONSE_FORMAT, statusCode)));
            success = true;
        } catch (Exception ignored) {}
        Assertions.assertTrue(success);
    }

    @Test
    public void testThrowIfHubSpotRequestFailsWithUnexpectedStatusCode() {
        int statusCode = 418;
        boolean success = false;
        when(responseMock.code()).thenReturn(statusCode);
        try {
            HubspotApiClient.throwIfHubSpotRequestFails(responseMock, Optional.of(TEST_RESPONSE_BODY));
        } catch (ServiceFailureException e) {
            Assertions.assertTrue(e.getMessage().contains(String.format(HUBSPOT_ERROR_CODE_RESPONSE_FORMAT, statusCode)));
            success = true;
        } catch (Exception ignored) {}
        Assertions.assertTrue(success);
    }

    @Test
    public void testNoExceptionForSuccessfulResponse() {
        when(responseMock.code()).thenReturn(200);
        try {
            HubspotApiClient.throwIfHubSpotRequestFails(responseMock, Optional.of("Success"));
        } catch (Exception e) {
            Assertions.fail("Should not throw any exception for status code: " + 200);
        }
    }
}
