package com.subskribe.billy.hubspot;

import static com.subskribe.billy.hubspot.service.HubSpotService.SUBSKRIBE_CREATE_ORDER_URL_FORMAT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.subskribe.billy.hubspot.model.HubSpotCompany;
import com.subskribe.billy.hubspot.model.HubSpotCompanyCard;
import com.subskribe.billy.hubspot.model.HubSpotCompanyCardResult;
import com.subskribe.billy.hubspot.model.HubSpotDeal;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.shared.enums.CompositeOrderType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class HubSpotCardTest {

    private HubSpotDeal deal = new HubSpotDeal();
    private HubSpotCompany company = new HubSpotCompany();
    private static final String TEST_DEAL_ID = "1234567890";
    private static final String TEST_COMPANY_ID = "0987654321";
    private static final String SITE_URL = "http://localhost:8080/";

    @BeforeEach
    void setUp() {
        deal = new HubSpotDeal();
        deal.setId(TEST_DEAL_ID);
        company = new HubSpotCompany();
        company.setId(TEST_COMPANY_ID);
    }

    @Test
    public void testCancelAndRestructure() {
        HubSpotCompanyCard hubSpotCompanyCard = HubSpotService.getCompositeOrderCard(
            CompositeOrderType.CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE,
            deal,
            company,
            SITE_URL
        );
        HubSpotCompanyCardResult cardContent = hubSpotCompanyCard.results().stream().findFirst().orElse(null);
        assertNotNull(cardContent);
        assertEquals(
            cardContent.link(),
            String.format(
                SUBSKRIBE_CREATE_ORDER_URL_FORMAT,
                SITE_URL,
                TEST_COMPANY_ID,
                TEST_DEAL_ID,
                CompositeOrderType.CANCEL_SINGLE_SUBSCRIPTION_AND_RESTRUCTURE.getCompositeOrderName()
            )
        );
    }

    @Test
    public void testAmendAndRenew() {
        HubSpotCompanyCard hubSpotCompanyCard = HubSpotService.getCompositeOrderCard(
            CompositeOrderType.UPSELL_AND_EARLY_RENEWAL,
            deal,
            company,
            SITE_URL
        );
        HubSpotCompanyCardResult cardContent = hubSpotCompanyCard.results().stream().findFirst().orElse(null);
        assertNotNull(cardContent);
        assertEquals(
            cardContent.link(),
            String.format(
                SUBSKRIBE_CREATE_ORDER_URL_FORMAT,
                SITE_URL,
                TEST_COMPANY_ID,
                TEST_DEAL_ID,
                CompositeOrderType.UPSELL_AND_EARLY_RENEWAL.getCompositeOrderName()
            )
        );
    }
}
