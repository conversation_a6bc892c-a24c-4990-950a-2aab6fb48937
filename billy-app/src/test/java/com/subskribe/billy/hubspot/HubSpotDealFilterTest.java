package com.subskribe.billy.hubspot;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.hubspot.model.HubSpotDeal;
import com.subskribe.billy.hubspot.model.HubSpotDealProperties;
import com.subskribe.billy.hubspot.service.HubSpotService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class HubSpotDealFilterTest {

    private HubSpotDeal deal = new HubSpotDeal();
    private HubSpotDealProperties properties = new HubSpotDealProperties();
    private static final String NEW_DEAL_TYPE = "newbusiness";
    private static final String AMENDMENT_DEAL_TYPE = "Expansion";
    private static final String SUBSCRIPTION_ID = "SUB-12345";

    @BeforeEach
    void setUp() {
        deal = new HubSpotDeal();
        properties = new HubSpotDealProperties();
    }

    @Test
    public void testNewDealWithSubscriptionId() {
        properties.setDealType(NEW_DEAL_TYPE);
        properties.setSubscriptionId(SUBSCRIPTION_ID);
        deal.setProperties(properties);
        HubSpotService.dealHasSubscriptionIdIfNeeded(deal);

        assertTrue(HubSpotService.dealHasSubscriptionIdIfNeeded(deal));
    }

    @Test
    public void testAmendmentDealWithSubscriptionId() {
        properties.setDealType(AMENDMENT_DEAL_TYPE);
        properties.setSubscriptionId(SUBSCRIPTION_ID);
        deal.setProperties(properties);
        HubSpotService.dealHasSubscriptionIdIfNeeded(deal);

        assertTrue(HubSpotService.dealHasSubscriptionIdIfNeeded(deal));
    }

    @Test
    public void testNewDealWithoutSubscriptionId() {
        properties.setDealType(NEW_DEAL_TYPE);
        properties.setSubscriptionId(null);
        deal.setProperties(properties);
        HubSpotService.dealHasSubscriptionIdIfNeeded(deal);

        assertTrue(HubSpotService.dealHasSubscriptionIdIfNeeded(deal));
    }

    @Test
    public void testAmendmentDealWithoutSubscriptionId() {
        properties.setDealType(AMENDMENT_DEAL_TYPE);
        properties.setSubscriptionId(null);
        deal.setProperties(properties);
        HubSpotService.dealHasSubscriptionIdIfNeeded(deal);

        assertFalse(HubSpotService.dealHasSubscriptionIdIfNeeded(deal));
    }
}
