package com.subskribe.billy.metrics;

import static com.subskribe.billy.test.utilities.ChargeUtils.getChargeMapFromCharges;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.compositeorder.service.CompositeOrderGetService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.DiscountCalculator;
import com.subskribe.billy.invoice.service.InvoiceAmountCalculator;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.metrics.model.LineItemMetrics;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.TimeSeriesElement;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.pecuniary.Numbers;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Period;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionImpl;
import com.subskribe.billy.subscription.services.SubscriptionOrderService;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.test.utilities.MockChargeBuilder;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class MetricsServiceTest {

    private static final String ACCOUNT_ID = "account123";
    private static final TimeZone TIME_ZONE = TimeZone.getTimeZone("UTC");
    private static final ZonedDateTime JAN_1_2022 = ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, TIME_ZONE.toZoneId());

    private final AccountGetService mockAccountGetService = mock(AccountGetService.class);
    private final OrderGetService mockOrderGetService = mock(OrderGetService.class);
    private final ProductCatalogGetService mockProductCatalogGetService = mock(ProductCatalogGetService.class);
    private final SubscriptionGetService mockSubscriptionGetService = mock(SubscriptionGetService.class);
    private final TenantSettingService mockTenantSettingService = TenantSettingServiceFixture.getDefault();
    private final ProrationConfigurationGetService mockProrationConfigurationGetService = mock(ProrationConfigurationGetService.class);
    private final RateCardService rateCardService = mock(RateCardService.class);
    private final InvoiceService mockInvoiceService = mock(InvoiceService.class);
    private final CompositeOrderGetService compositeOrderGetService = mock(CompositeOrderGetService.class);
    private final FeatureService featureService = FeatureServiceFixture.allEnabled();
    private final CurrencyConversionRateGetService currencyConversionRateGetService = mock(CurrencyConversionRateGetService.class);
    private final SubscriptionOrderService subscriptionOrderService = mock(SubscriptionOrderService.class);
    private final DiscountCalculator discountCalculator = new DiscountCalculator(featureService);

    private MetricsService metricsService;

    @BeforeEach
    void beforeEach() {
        when(mockProrationConfigurationGetService.resolveProrationConfig(any(Subscription.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.getDefault(), ProrationConfig.Mode.getDefault())
        );
        when(mockProrationConfigurationGetService.resolveProrationConfig(any(Order.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.getDefault(), ProrationConfig.Mode.getDefault())
        );
        when(mockAccountGetService.getAccount(any())).thenReturn(getTestAccount());
        when(featureService.isEnabled(Feature.FLEXIBLE_BILLING_ANCHOR_DATE)).thenReturn(false);
        when(featureService.isEnabled(Feature.ORDER_LINE_ARR_OVERRIDE)).thenReturn(true);
        var subscriptionBillingPeriodService = new SubscriptionBillingPeriodService(
            mockSubscriptionGetService,
            mockTenantSettingService,
            featureService,
            mockProrationConfigurationGetService
        );
        InvoiceAmountCalculator invoiceAmountCalculator = new InvoiceAmountCalculator(
            rateCardService,
            currencyConversionRateGetService,
            discountCalculator,
            featureService
        );
        metricsService = new MetricsService(
            mockAccountGetService,
            mockOrderGetService,
            mockProductCatalogGetService,
            mockSubscriptionGetService,
            mockTenantSettingService,
            mockProrationConfigurationGetService,
            subscriptionBillingPeriodService,
            featureService,
            invoiceAmountCalculator,
            compositeOrderGetService,
            mockInvoiceService,
            subscriptionOrderService
        );
    }

    @Test
    public void recurrenceToYearMultiplier() {
        assertEquals(new BigDecimal("12.**********"), MetricsService.getRecurrenceToYearMultiplier(new Recurrence(Cycle.MONTH, 1)));
        assertEquals(new BigDecimal("4.**********"), MetricsService.getRecurrenceToYearMultiplier(new Recurrence(Cycle.QUARTER, 1)));
        assertEquals(new BigDecimal("2.**********"), MetricsService.getRecurrenceToYearMultiplier(new Recurrence(Cycle.SEMI_ANNUAL, 1)));
        assertEquals(new BigDecimal("12.**********"), MetricsService.getRecurrenceToYearMultiplier(new Recurrence(Cycle.DAY, 30)));
        assertThrows(UnsupportedOperationException.class, () -> MetricsService.getRecurrenceToYearMultiplier(new Recurrence(Cycle.PAID_IN_FULL, 1)));
        assertThrows(UnsupportedOperationException.class, () -> MetricsService.getRecurrenceToYearMultiplier(new Recurrence(Cycle.CUSTOM, 1)));
    }

    @Test
    public void annualOrderTotalWithAnnualInvoiceItems() {
        String orderId = "order123";
        Instant start = JAN_1_2022.toInstant();
        Instant end = JAN_1_2022.plusYears(3).toInstant();
        Order order = getOrder(orderId, null, List.of(), start, end, OrderType.NEW);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        Recurrence recurrence = new Recurrence(Cycle.YEAR, 1);

        List<BillingPeriod> billingPeriods = getAnnualBillingPeriods(JAN_1_2022, 3);
        List<InvoiceItem> invoiceItems = getAnnualInvoiceItems(
            JAN_1_2022,
            List.of(BigDecimal.valueOf(1), BigDecimal.valueOf(2), BigDecimal.valueOf(3))
        );
        InvoicePreview invoicePreview = new InvoicePreview(
            orderId,
            new BillingPeriod(start, end, end, recurrence),
            List.of(),
            BigDecimal.TEN,
            BigDecimal.TEN,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.TEN,
            invoiceItems
        );

        when(mockInvoiceService.getBillingPeriods(order)).thenReturn(billingPeriods);
        when(mockInvoiceService.previewInvoiceByOrderPeriod(order, false, false)).thenReturn(invoicePreview);

        Map<Period, BigDecimal> annualTotals = metricsService.getOrderAnnualTotal(orderId);
        BigDecimal total = annualTotals.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        assertEquals(3, annualTotals.size());
        assertEquals(new BigDecimal("6.00"), total);
    }

    @Test
    public void annualOrderTotalWithMultiyearItem() {
        String orderId = "order123";
        Instant start = JAN_1_2022.toInstant();
        Instant end = JAN_1_2022.plusYears(3).toInstant();
        Order order = getOrder(orderId, null, List.of(), start, end, OrderType.NEW);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        Recurrence recurrence = new Recurrence(Cycle.YEAR, 1);

        List<BillingPeriod> billingPeriods = getAnnualBillingPeriods(JAN_1_2022, 3);
        List<InvoiceItem> invoiceItems = getAnnualInvoiceItems(
            JAN_1_2022,
            List.of(BigDecimal.valueOf(1), BigDecimal.valueOf(2), BigDecimal.valueOf(3))
        );
        invoiceItems.add(
            new InvoiceItem.InvoiceItemBuilder().amount(BigDecimal.valueOf(4)).periodStartDate(start).periodEndDate(end).createInvoiceItem()
        );

        InvoicePreview invoicePreview = new InvoicePreview(
            orderId,
            new BillingPeriod(start, end, end, recurrence),
            List.of(),
            BigDecimal.TEN,
            BigDecimal.TEN,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.TEN,
            invoiceItems
        );

        when(mockInvoiceService.getBillingPeriods(order)).thenReturn(billingPeriods);
        when(mockInvoiceService.previewInvoiceByOrderPeriod(order, false, false)).thenReturn(invoicePreview);

        Map<Period, BigDecimal> annualTotals = metricsService.getOrderAnnualTotal(orderId);
        BigDecimal total = annualTotals.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        assertEquals(3, annualTotals.size());
        assertEquals(new BigDecimal("5.00"), annualTotals.values().iterator().next()); // first year contains 1 + 4
        assertEquals(new BigDecimal("10.00"), total);
    }

    @Test
    public void newOneYearRecurringOrder() {
        String orderId = "order123";
        String chargeId = "charge123";
        String planId = "plan123";
        BigDecimal totalAmount = new BigDecimal("100.00");
        Instant orderStart = JAN_1_2022.toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(12).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        OrderLineItem orderLineItem = getOrderLineItem(planId, chargeId, orderId, totalAmount, 10, orderStart, orderEnd, false, null);
        Order order = getOrder(orderId, null, List.of(orderLineItem), orderStart, orderEnd, OrderType.NEW);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrdersByOrderIds(List.of(orderId))).thenReturn(List.of(order));
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));

        Metrics metrics = metricsService.getOrderMetrics(orderId, orderStart);

        assertEquals(totalAmount, metrics.getTcv());
        assertEquals(totalAmount, metrics.getRecurringTotal());
        Assertions.assertThat(metrics.getNonRecurringTotal()).isZero();
        assertEquals(totalAmount, metrics.getEntryArr());
        assertEquals(totalAmount, metrics.getArr());
        assertEquals(totalAmount, metrics.getExitArr());
        assertEquals(2, metrics.getArrTrend().size());
    }

    @Test
    public void newOneYearOrderWithOneTimeCharge() {
        String orderId = "order123";
        String chargeId = "charge123";
        String planId = "plan123";
        BigDecimal totalAmount = new BigDecimal("100.00");
        Instant orderStart = JAN_1_2022.toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(12).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.ONE_TIME, BigDecimal.TEN, false);
        OrderLineItem orderLineItem = getOrderLineItem(planId, chargeId, orderId, totalAmount, 10, orderStart, orderEnd, false, null);
        Order order = getOrder(orderId, null, List.of(orderLineItem), orderStart, orderEnd, OrderType.NEW);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));

        Metrics metrics = metricsService.getOrderMetrics(orderId, orderStart);

        assertEquals(totalAmount, metrics.getTcv());
        Assertions.assertThat(metrics.getRecurringTotal()).isZero();
        assertEquals(totalAmount, metrics.getNonRecurringTotal());
        Assertions.assertThat(metrics.getEntryArr()).isZero();
        Assertions.assertThat(metrics.getArr()).isZero();
        Assertions.assertThat(metrics.getExitArr()).isZero();
        assertEquals(0, metrics.getArrTrend().size());
    }

    @Test
    public void newOneYearRecurringWithPercentOfOrder() {
        String orderId = "order123";
        String planId = "plan123";
        String recurringChargeId = "charge1";
        String percentOfChargeId = "charge2";
        BigDecimal recurringAmount = new BigDecimal("100.00");
        BigDecimal percentOfAmount = new BigDecimal("25.00");
        Instant orderStart = JAN_1_2022.toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(12).toInstant();

        Charge recurringCharge = getPerUnitCharge(recurringChargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        Charge percentOfCharge = getPerUnitCharge(percentOfChargeId, ChargeType.PERCENTAGE_OF, BigDecimal.ONE, true);
        OrderLineItem recurringOrderLineItem = getOrderLineItem(
            planId,
            recurringChargeId,
            orderId,
            recurringAmount,
            10,
            orderStart,
            orderEnd,
            false,
            null
        );
        OrderLineItem percentOfOrderLineItem = getOrderLineItem(
            planId,
            percentOfChargeId,
            orderId,
            percentOfAmount,
            1,
            orderStart,
            orderEnd,
            false,
            null
        );
        Order order = getOrder(orderId, null, List.of(recurringOrderLineItem, percentOfOrderLineItem), orderStart, orderEnd, OrderType.NEW);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrdersByOrderIds(List.of(orderId))).thenReturn(List.of(order));
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(
            getChargeMapFromCharges(List.of(recurringCharge, percentOfCharge))
        );

        Metrics metrics = metricsService.getOrderMetrics(orderId, orderStart);

        assertEquals(recurringAmount.add(percentOfAmount), metrics.getTcv());
        assertEquals(recurringAmount.add(percentOfAmount), metrics.getRecurringTotal());
        Assertions.assertThat(metrics.getNonRecurringTotal()).isZero();
        assertEquals(recurringAmount.add(percentOfAmount), metrics.getEntryArr());
        assertEquals(recurringAmount.add(percentOfAmount), metrics.getArr());
        assertEquals(recurringAmount.add(percentOfAmount), metrics.getExitArr());
        assertEquals(2, metrics.getArrTrend().size());
    }

    @Test
    public void newThreeYearRecurringOrder() {
        String orderId = "order123";
        String planId = "plan123";
        String chargeId = "charge123";
        BigDecimal totalAmount = new BigDecimal("300.00");
        BigDecimal expectedARR = new BigDecimal("100.00");
        Instant orderStart = JAN_1_2022.toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(36).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        OrderLineItem orderLineItem = getOrderLineItem(planId, chargeId, orderId, totalAmount, 10, orderStart, orderEnd, false, null);
        Order order = getOrder(orderId, null, List.of(orderLineItem), orderStart, orderEnd, OrderType.NEW);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrdersByOrderIds(List.of(orderId))).thenReturn(List.of(order));
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));

        Metrics metrics = metricsService.getOrderMetrics(orderId, orderStart);

        assertEquals(totalAmount, metrics.getTcv());
        assertEquals(totalAmount, metrics.getRecurringTotal());
        Assertions.assertThat(metrics.getNonRecurringTotal()).isZero();
        assertEquals(expectedARR, metrics.getEntryArr());
        assertEquals(expectedARR, metrics.getArr());
        assertEquals(expectedARR, metrics.getExitArr());
        assertEquals(2, metrics.getArrTrend().size());
    }

    @Test
    public void newThreeYearRecurringOrderAndArrOverride() {
        String orderId = "order123";
        String planId = "plan123";
        String chargeId = "charge123";
        BigDecimal totalAmount = new BigDecimal("300.00");
        BigDecimal expectedARRWithOverride = new BigDecimal("200.00");
        Instant orderStart = JAN_1_2022.toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(36).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        OrderLineItem orderLineItem = getOrderLineItem(
            planId,
            chargeId,
            orderId,
            totalAmount,
            10,
            orderStart,
            orderEnd,
            false,
            expectedARRWithOverride
        );
        Order order = getOrder(orderId, null, List.of(orderLineItem), orderStart, orderEnd, OrderType.NEW);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrdersByOrderIds(List.of(orderId))).thenReturn(List.of(order));
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));

        Metrics metrics = metricsService.getOrderMetrics(orderId, orderStart);

        assertEquals(totalAmount, metrics.getTcv());
        assertEquals(totalAmount, metrics.getRecurringTotal());
        Assertions.assertThat(metrics.getNonRecurringTotal()).isZero();
        assertEquals(expectedARRWithOverride, metrics.getEntryArr());
        assertEquals(expectedARRWithOverride, metrics.getArr());
        assertEquals(expectedARRWithOverride, metrics.getExitArr());
        assertEquals(2, metrics.getArrTrend().size());
    }

    @Test
    public void newThreeYearRecurringOrderWithRamp() {
        String orderId = "order123";
        String planId = "plan123";
        String chargeId = "charge123";
        Instant orderStart = JAN_1_2022.toInstant();
        Instant firstStep = JAN_1_2022.plusMonths(12).toInstant();
        Instant secondStep = JAN_1_2022.plusMonths(24).toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(36).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        OrderLineItem orderLineItem1 = getOrderLineItem(planId, chargeId, orderId, new BigDecimal("100.00"), 10, orderStart, firstStep, true, null);
        OrderLineItem orderLineItem2 = getOrderLineItem(planId, chargeId, orderId, new BigDecimal("200.00"), 20, firstStep, secondStep, true, null);
        OrderLineItem orderLineItem3 = getOrderLineItem(planId, chargeId, orderId, new BigDecimal("300.00"), 30, secondStep, orderEnd, true, null);

        Map<String, OrderLineItem> orderLineItemMap = new HashMap<>() {
            {
                put(orderLineItem1.getOrderLineId(), orderLineItem1);
                put(orderLineItem2.getOrderLineId(), orderLineItem2);
                put(orderLineItem3.getOrderLineId(), orderLineItem3);
            }
        };

        Order order = getOrder(orderId, null, new ArrayList<>(orderLineItemMap.values()), orderStart, orderEnd, OrderType.NEW);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrderLineItemByOrderLineItemId(anyString())).thenAnswer(invocation ->
            Optional.of(orderLineItemMap.get(invocation.getArgument(0, String.class)))
        );
        when(mockOrderGetService.getOrdersByOrderIds(List.of(orderId))).thenReturn(List.of(order));
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));
        when(mockProductCatalogGetService.getChargeByChargeId(charge.getChargeId())).thenReturn(charge);

        Metrics orderMetrics = metricsService.getOrderMetrics(orderId, firstStep);
        LineItemMetrics orderLine2Metrics = metricsService.getMetricsForOrderLine(orderLineItem2.getOrderLineId());

        assertEquals(new BigDecimal("600.00"), orderMetrics.getTcv());
        assertEquals(new BigDecimal("600.00"), orderMetrics.getRecurringTotal());
        Assertions.assertThat(orderMetrics.getNonRecurringTotal()).isZero();
        assertEquals(new BigDecimal("100.00"), orderMetrics.getEntryArr());
        assertEquals(new BigDecimal("200.00"), orderMetrics.getArr());
        assertEquals(new BigDecimal("300.00"), orderMetrics.getExitArr());
        assertEquals(new BigDecimal("200.00"), orderMetrics.getAverageArr());
        assertEquals(4, orderMetrics.getArrTrend().size());

        assertEquals(new BigDecimal("100.00"), orderLine2Metrics.deltaArr());
        assertEquals(new BigDecimal("200.00"), orderLine2Metrics.arr());
    }

    @Test
    public void newThreeYearRecurringOrderWithEqualRamps() {
        String orderId = "order123";
        String planId = "plan123";
        String chargeId = "charge123";
        Instant orderStart = JAN_1_2022.toInstant();
        Instant firstStep = JAN_1_2022.plusMonths(12).toInstant();
        Instant secondStep = JAN_1_2022.plusMonths(24).toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(36).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        OrderLineItem orderLineItem1 = getOrderLineItem(planId, chargeId, orderId, new BigDecimal("200.00"), 20, orderStart, firstStep, true, null);
        OrderLineItem orderLineItem2 = getOrderLineItem(planId, chargeId, orderId, new BigDecimal("200.00"), 20, firstStep, secondStep, true, null);
        OrderLineItem orderLineItem3 = getOrderLineItem(planId, chargeId, orderId, new BigDecimal("200.00"), 20, secondStep, orderEnd, true, null);

        Map<String, OrderLineItem> orderLineItemMap = new HashMap<>() {
            {
                put(orderLineItem1.getOrderLineId(), orderLineItem1);
                put(orderLineItem2.getOrderLineId(), orderLineItem2);
                put(orderLineItem3.getOrderLineId(), orderLineItem3);
            }
        };

        Order order = getOrder(orderId, null, new ArrayList<>(orderLineItemMap.values()), orderStart, orderEnd, OrderType.NEW);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrderLineItemByOrderLineItemId(anyString())).thenAnswer(invocation ->
            Optional.of(orderLineItemMap.get(invocation.getArgument(0, String.class)))
        );
        when(mockOrderGetService.getOrdersByOrderIds(List.of(orderId))).thenReturn(List.of(order));
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));
        when(mockProductCatalogGetService.getChargeByChargeId(charge.getChargeId())).thenReturn(charge);

        Metrics orderMetrics = metricsService.getOrderMetrics(orderId, firstStep);

        assertEquals(new BigDecimal("600.00"), orderMetrics.getTcv());
        assertEquals(new BigDecimal("600.00"), orderMetrics.getRecurringTotal());
        Assertions.assertThat(orderMetrics.getNonRecurringTotal()).isZero();
        assertEquals(new BigDecimal("200.00"), orderMetrics.getAverageArr());
        assertEquals(2, orderMetrics.getArrTrend().size());
    }

    @Test
    public void recurringAmendmentOrder() {
        String orderId = "order123";
        String planId = "plan123";
        String chargeId = "charge123";
        String subscriptionId = "sub123";
        BigDecimal totalAmount = new BigDecimal("200.00");
        BigDecimal expectedARR = new BigDecimal("100.00");
        Instant orderStart = JAN_1_2022.toInstant();
        Instant amendmentStart = JAN_1_2022.plusMonths(12).toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(36).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        OrderLineItem newOrderLineItem = getOrderLineItem(planId, chargeId, orderId, totalAmount, 10, amendmentStart, orderEnd, false, null);
        Order order = getOrder(orderId, subscriptionId, List.of(newOrderLineItem), amendmentStart, orderEnd, OrderType.AMENDMENT);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrdersByOrderIds(List.of(orderId))).thenReturn(List.of(order));
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));
        when(mockSubscriptionGetService.getSubscription(subscriptionId)).thenReturn(getSubscription(subscriptionId, orderStart, orderEnd));

        Metrics metrics = metricsService.getOrderMetrics(orderId, amendmentStart);

        assertEquals(totalAmount, metrics.getTcv());
        assertEquals(totalAmount, metrics.getRecurringTotal());
        Assertions.assertThat(metrics.getNonRecurringTotal()).isZero();
        assertEquals(expectedARR, metrics.getEntryArr());
        assertEquals(expectedARR, metrics.getArr());
        assertEquals(expectedARR, metrics.getExitArr());
        assertEquals(2, metrics.getArrTrend().size());
    }

    @Test
    public void recurringAmendmentOrderUnevenDays() {
        String orderId = "order123";
        String planId = "plan123";
        String chargeId = "charge123";
        String subscriptionId = "sub123";
        BigDecimal totalAmount = new BigDecimal("161.00"); // not exact amount with proration, but metrics calculation shouldn't care about this for ARR values
        BigDecimal expectedARR = new BigDecimal("100.00");
        Instant orderStart = JAN_1_2022.plusMonths(16).plusDays(10).toInstant();
        Instant amendmentStart = JAN_1_2022.plusMonths(16).plusDays(10).toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(36).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        OrderLineItem newOrderLineItem = getOrderLineItem(planId, chargeId, orderId, totalAmount, 10, amendmentStart, orderEnd, false, null);
        Order order = getOrder(orderId, subscriptionId, List.of(newOrderLineItem), amendmentStart, orderEnd, OrderType.AMENDMENT);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrdersByOrderIds(List.of(orderId))).thenReturn(List.of(order));
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));
        when(mockSubscriptionGetService.getSubscription(subscriptionId)).thenReturn(getSubscription(subscriptionId, orderStart, orderEnd));

        Metrics metrics = metricsService.getOrderMetrics(orderId, amendmentStart);

        assertEquals(totalAmount, metrics.getTcv());
        assertEquals(totalAmount, metrics.getRecurringTotal());
        Assertions.assertThat(metrics.getNonRecurringTotal()).isZero();
        assertEquals(expectedARR, metrics.getEntryArr());
        assertEquals(expectedARR, metrics.getArr());
        assertEquals(expectedARR, metrics.getExitArr());
        assertEquals(2, metrics.getArrTrend().size());
    }

    @Test
    public void recurringAmendmentOrderUnevenDaysWithOverride() {
        String orderId = "order123";
        String planId = "plan123";
        String chargeId = "charge123";
        String subscriptionId = "sub123";
        BigDecimal totalAmount = new BigDecimal("161.00"); // not exact amount with proration, but metrics calculation shouldn't care about this for ARR values
        BigDecimal expectedARR = new BigDecimal("100.00");
        Instant orderStart = JAN_1_2022.plusMonths(16).plusDays(10).toInstant();
        Instant amendmentStart = JAN_1_2022.plusMonths(16).plusDays(10).toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(36).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        OrderLineItem newOrderLineItem = getOrderLineItem(planId, chargeId, orderId, totalAmount, 10, amendmentStart, orderEnd, false, null);
        Order order = getOrder(orderId, subscriptionId, List.of(newOrderLineItem), amendmentStart, orderEnd, OrderType.AMENDMENT);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrdersByOrderIds(List.of(orderId))).thenReturn(List.of(order));
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));
        when(mockSubscriptionGetService.getSubscription(subscriptionId)).thenReturn(getSubscription(subscriptionId, orderStart, orderEnd));

        // with no override
        Metrics metrics = metricsService.getOrderMetrics(orderId, amendmentStart);

        assertEquals(totalAmount, metrics.getTcv());
        assertEquals(totalAmount, metrics.getRecurringTotal());
        Assertions.assertThat(metrics.getNonRecurringTotal()).isZero();
        assertEquals(expectedARR, metrics.getEntryArr());
        assertEquals(expectedARR, metrics.getArr());
        assertEquals(expectedARR, metrics.getExitArr());
        assertEquals(2, metrics.getArrTrend().size());

        // with override
        BigDecimal expectedArrWithOverride = new BigDecimal("161.00");
        newOrderLineItem.setArrOverride(expectedArrWithOverride);
        Metrics metricsWithOverride = metricsService.getOrderMetrics(orderId, amendmentStart);

        assertEquals(totalAmount, metricsWithOverride.getTcv());
        assertEquals(totalAmount, metricsWithOverride.getRecurringTotal());
        Assertions.assertThat(metricsWithOverride.getNonRecurringTotal()).isZero();
        assertEquals(expectedArrWithOverride, metricsWithOverride.getEntryArr());
        assertEquals(expectedArrWithOverride, metricsWithOverride.getArr());
        assertEquals(expectedArrWithOverride, metricsWithOverride.getExitArr());
        assertEquals(2, metricsWithOverride.getArrTrend().size());
    }

    @Test
    public void recurringUnevenAmendmentOrder() {
        String orderId = "order123";
        String planId = "plan123";
        String chargeId = "charge123";
        String subscriptionId = "sub123";
        BigDecimal totalAmount = new BigDecimal("1100.00");
        Instant orderStart = JAN_1_2022.toInstant();
        Instant amendmentStart = JAN_1_2022.plusMonths(1).toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(12).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        OrderLineItem newOrderLineItem = getOrderLineItem(planId, chargeId, orderId, totalAmount, 120, amendmentStart, orderEnd, false, null);
        Order order = getOrder(orderId, subscriptionId, List.of(newOrderLineItem), amendmentStart, orderEnd, OrderType.AMENDMENT);
        when(mockOrderGetService.getOrderByOrderId(orderId)).thenReturn(order);
        when(mockOrderGetService.getOrdersByOrderIds(List.of(orderId))).thenReturn(List.of(order));
        when(mockOrderGetService.getOrders(ACCOUNT_ID, Set.of(orderId))).thenReturn(List.of(order));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));
        when(mockSubscriptionGetService.getSubscription(subscriptionId)).thenReturn(getSubscription(subscriptionId, orderStart, orderEnd));

        // with normalized proration
        Metrics normalizedMetrics = metricsService.getOrderMetrics(orderId, amendmentStart);

        // with exact proration
        when(mockProrationConfigurationGetService.resolveProrationConfig(any(Order.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT)
        );
        when(mockProrationConfigurationGetService.resolveProrationConfig(any(Subscription.class))).thenReturn(
            new ProrationConfig(ProrationConfig.Scheme.FIXED_DAYS, ProrationConfig.Mode.EXACT)
        );
        Metrics exactMetrics = metricsService.getOrderMetrics(orderId, amendmentStart);

        assertEquals(totalAmount, normalizedMetrics.getTcv());
        assertEquals(totalAmount, normalizedMetrics.getRecurringTotal());
        Assertions.assertThat(normalizedMetrics.getNonRecurringTotal()).isZero();
        assertEquals(totalAmount, exactMetrics.getTcv());
        assertEquals(totalAmount, exactMetrics.getRecurringTotal());
        Assertions.assertThat(exactMetrics.getNonRecurringTotal()).isZero();
        assertEquals(new BigDecimal("1200.00"), normalizedMetrics.getArr());
        assertEquals(new BigDecimal("1200.00"), exactMetrics.getArr());
        assertEquals(2, normalizedMetrics.getArrTrend().size());
        assertEquals(2, exactMetrics.getArrTrend().size());
    }

    @Test
    public void subscriptionWithAmendment() {
        String newOrderId = "order1";
        String amendOrderId = "order2";
        String subscriptionId = "sub1";
        String planId = "plan123";
        String chargeId = "charge123";
        Instant orderStart = JAN_1_2022.toInstant();
        Instant amendmentStart = JAN_1_2022.plusMonths(12).toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(36).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, BigDecimal.TEN, true);
        OrderLineItem newOrderLineItem = getOrderLineItem(
            planId,
            chargeId,
            newOrderId,
            new BigDecimal("300.00"),
            10,
            orderStart,
            orderEnd,
            false,
            null
        );
        OrderLineItem amendmentOrderDebookLineItem = getOrderLineItem(
            planId,
            chargeId,
            amendOrderId,
            new BigDecimal("-200.00"),
            -10,
            amendmentStart,
            orderEnd,
            false,
            null
        );
        OrderLineItem amendmentOrderRebookLineItem = getOrderLineItem(
            planId,
            chargeId,
            amendOrderId,
            new BigDecimal("400.00"),
            20,
            amendmentStart,
            orderEnd,
            false,
            null
        );
        Order newOrder = getOrder(newOrderId, null, List.of(newOrderLineItem), orderStart, orderEnd, OrderType.NEW);
        Order amendOrder = getOrder(
            amendOrderId,
            subscriptionId,
            List.of(amendmentOrderDebookLineItem, amendmentOrderRebookLineItem),
            amendmentStart,
            orderEnd,
            OrderType.AMENDMENT
        );
        when(mockOrderGetService.getOrderByOrderId(newOrderId)).thenReturn(newOrder);
        when(mockOrderGetService.getOrderByOrderId(amendOrderId)).thenReturn(amendOrder);
        when(mockOrderGetService.getOrdersByOrderIds(any())).thenReturn(List.of(newOrder, amendOrder));
        when(mockOrderGetService.getOrders(any(), Optional.ofNullable(any()))).thenReturn(List.of(newOrder, amendOrder));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(subscriptionId)).thenReturn(List.of(newOrder, amendOrder));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));
        when(mockSubscriptionGetService.getSubscription(subscriptionId)).thenReturn(getSubscription(subscriptionId, orderStart, orderEnd));

        Metrics metrics = metricsService.getSubscriptionMetrics(subscriptionId, orderStart);

        assertEquals(new BigDecimal("500.00"), metrics.getTcv());
        assertEquals(new BigDecimal("500.00"), metrics.getRecurringTotal());
        Assertions.assertThat(metrics.getNonRecurringTotal()).isZero();
        assertEquals(new BigDecimal("100.00"), metrics.getEntryArr());
        assertEquals(new BigDecimal("100.00"), metrics.getArr());
        assertEquals(new BigDecimal("200.00"), metrics.getExitArr());
        assertEquals(new BigDecimal("166.67"), metrics.getAverageArr());
        assertEquals(3, metrics.getArrTrend().size());
    }

    @Test
    public void subscriptionWithAmendmentAndArrOverride() {
        String newOrderId = "order1";
        String amendOrderId = "order2";
        String subscriptionId = "sub1";
        String planId = "plan123";
        String chargeId = "charge123";
        Instant orderStart = JAN_1_2022.toInstant();
        Instant amendmentStart = JAN_1_2022.plusMonths(12).toInstant();
        Instant orderEnd = JAN_1_2022.plusMonths(36).toInstant();

        Charge charge = getPerUnitCharge(chargeId, ChargeType.RECURRING, new BigDecimal("9.99"), true);
        OrderLineItem newOrderLineItem = getOrderLineItem(
            planId,
            chargeId,
            newOrderId,
            new BigDecimal("300.00"),
            10,
            orderStart,
            orderEnd,
            false,
            null
        );
        OrderLineItem amendmentOrderDebookLineItem = getOrderLineItem(
            planId,
            chargeId,
            amendOrderId,
            new BigDecimal("-200.00"),
            -10,
            amendmentStart,
            orderEnd,
            false,
            null
        );
        OrderLineItem amendmentOrderRebookLineItem = getOrderLineItem(
            planId,
            chargeId,
            amendOrderId,
            new BigDecimal("400.00"),
            20,
            amendmentStart,
            orderEnd,
            false,
            null
        );
        Order newOrder = getOrder(newOrderId, null, List.of(newOrderLineItem), orderStart, orderEnd, OrderType.NEW);
        Order amendOrder = getOrder(
            amendOrderId,
            subscriptionId,
            List.of(amendmentOrderDebookLineItem, amendmentOrderRebookLineItem),
            amendmentStart,
            orderEnd,
            OrderType.AMENDMENT
        );
        when(mockOrderGetService.getOrderByOrderId(newOrderId)).thenReturn(newOrder);
        when(mockOrderGetService.getOrderByOrderId(amendOrderId)).thenReturn(amendOrder);
        when(mockOrderGetService.getOrdersByOrderIds(any())).thenReturn(List.of(newOrder, amendOrder));
        when(mockOrderGetService.getOrders(any(), Optional.ofNullable(any()))).thenReturn(List.of(newOrder, amendOrder));
        when(mockOrderGetService.getExecutedOrdersBySubscriptionId(subscriptionId)).thenReturn(List.of(newOrder, amendOrder));
        when(mockProductCatalogGetService.getChargeMapByChargeIds(any())).thenReturn(getChargeMapFromCharges(List.of(charge)));
        when(mockSubscriptionGetService.getSubscription(subscriptionId)).thenReturn(getSubscription(subscriptionId, orderStart, orderEnd));

        Metrics metrics = metricsService.getSubscriptionMetrics(subscriptionId, orderStart);

        assertEquals(new BigDecimal("500.00"), metrics.getTcv());
        assertEquals(new BigDecimal("500.00"), metrics.getRecurringTotal());
        Assertions.assertThat(metrics.getNonRecurringTotal()).isZero();
        assertEquals(new BigDecimal("99.90"), metrics.getEntryArr());
        assertEquals(new BigDecimal("99.90"), metrics.getArr());
        assertEquals(new BigDecimal("199.80"), metrics.getExitArr());
        assertEquals(new BigDecimal("166.50"), metrics.getAverageArr());
        assertEquals(3, metrics.getArrTrend().size());

        newOrderLineItem.setArrOverride(new BigDecimal("100.00"));
        amendmentOrderDebookLineItem.setArrOverride(new BigDecimal("-100.00"));
        amendmentOrderRebookLineItem.setArrOverride(new BigDecimal("200.00"));
        Metrics metricsWithOverrides = metricsService.getSubscriptionMetrics(subscriptionId, orderStart);

        assertEquals(new BigDecimal("500.00"), metricsWithOverrides.getTcv());
        assertEquals(new BigDecimal("500.00"), metricsWithOverrides.getRecurringTotal());
        Assertions.assertThat(metricsWithOverrides.getNonRecurringTotal()).isZero();
        assertEquals(new BigDecimal("100.00"), metricsWithOverrides.getEntryArr());
        assertEquals(new BigDecimal("100.00"), metricsWithOverrides.getArr());
        assertEquals(new BigDecimal("200.00"), metricsWithOverrides.getExitArr());
        assertEquals(new BigDecimal("166.67"), metricsWithOverrides.getAverageArr());
        assertEquals(3, metricsWithOverrides.getArrTrend().size());
    }

    @Test
    public void mergeContinuousEquivalentArrTrendElementsInMiddle() {
        var now = Instant.now();
        List<TimeSeriesElement<BigDecimal>> arrTrend = List.of(
            new TimeSeriesElement<>(now, new BigDecimal("1.00")),
            new TimeSeriesElement<>(now.plusSeconds(1000), new BigDecimal("2.00")),
            new TimeSeriesElement<>(now.plusSeconds(2000), new BigDecimal("2")),
            new TimeSeriesElement<>(now.plusSeconds(2000), new BigDecimal("2")),
            new TimeSeriesElement<>(now.plusSeconds(3000), new BigDecimal("3.00"))
        );

        var mergedArrTrend = metricsService.mergeContinuousEquivalentSegments(arrTrend);

        Assertions.assertThat(mergedArrTrend.size()).isEqualTo(3);
        Assertions.assertThat(mergedArrTrend.get(0).getInstant()).isEqualTo(now);
        Assertions.assertThat(mergedArrTrend.get(1).getInstant()).isEqualTo(now.plusSeconds(1000));
        Assertions.assertThat(Numbers.equals(mergedArrTrend.get(1).getData(), new BigDecimal("2"))).isTrue();
        Assertions.assertThat(mergedArrTrend.get(2).getInstant()).isEqualTo(now.plusSeconds(3000)); // should skip the previous 3rd element
        Assertions.assertThat(Numbers.equals(mergedArrTrend.get(2).getData(), new BigDecimal("3"))).isTrue();
    }

    @Test
    public void mergeContinuousEquivalentArrTrendElementsUntilEnd() {
        var now = Instant.now();
        List<TimeSeriesElement<BigDecimal>> arrTrend = List.of(
            new TimeSeriesElement<>(now, new BigDecimal("1.00")),
            new TimeSeriesElement<>(now.plusSeconds(1000), new BigDecimal("2.00")),
            new TimeSeriesElement<>(now.plusSeconds(2000), new BigDecimal("2")),
            new TimeSeriesElement<>(now.plusSeconds(2000), new BigDecimal("2")),
            new TimeSeriesElement<>(now.plusSeconds(3000), new BigDecimal("2.0")),
            new TimeSeriesElement<>(now.plusSeconds(4000), BigDecimal.ZERO)
        );

        var mergedArrTrend = metricsService.mergeContinuousEquivalentSegments(arrTrend);

        Assertions.assertThat(mergedArrTrend.size()).isEqualTo(3);
        Assertions.assertThat(mergedArrTrend.get(0).getInstant()).isEqualTo(now);
        Assertions.assertThat(mergedArrTrend.get(1).getInstant()).isEqualTo(now.plusSeconds(1000));
        Assertions.assertThat(Numbers.equals(mergedArrTrend.get(1).getData(), new BigDecimal("2"))).isTrue();
        Assertions.assertThat(mergedArrTrend.get(2).getInstant()).isEqualTo(now.plusSeconds(4000)); // should skip the previous 3rd element
        Assertions.assertThat(Numbers.equals(mergedArrTrend.get(2).getData(), new BigDecimal("0"))).isTrue();
    }

    private static Account getTestAccount() {
        Account account = new Account();
        account.setAccountId(ACCOUNT_ID);
        return account;
    }

    private Order getOrder(
        String orderId,
        String subscriptionId,
        List<OrderLineItem> orderLineItems,
        Instant start,
        Instant end,
        OrderType orderType
    ) {
        Order order = new Order();

        order.setAccountId(ACCOUNT_ID);
        order.setOrderId(orderId);
        order.setExternalSubscriptionId(subscriptionId);
        order.setLineItemsNetEffect(orderLineItems);
        order.setStartDate(start);
        order.setEndDate(end);
        order.setBillingAnchorDate(start);
        order.setOrderType(orderType);
        order.setStatus(OrderStatus.EXECUTED);

        return order;
    }

    private Subscription getSubscription(String subscriptionId, Instant start, Instant end) {
        SubscriptionEntity subscriptionEntity = new SubscriptionEntity();
        subscriptionEntity.setSubscriptionId(subscriptionId);
        subscriptionEntity.setStartDate(start);
        subscriptionEntity.setEndDate(end);
        subscriptionEntity.setBillingAnchorDate(start);
        return new SubscriptionImpl(subscriptionEntity, null);
    }

    private OrderLineItem getOrderLineItem(
        String planId,
        String chargeId,
        String orderId,
        BigDecimal totalAmount,
        long quantity,
        Instant start,
        Instant end,
        boolean isRamp,
        BigDecimal arrOverride
    ) {
        var orderLineItem = new OrderLineItem();

        orderLineItem.setOrderLineId(UUID.randomUUID().toString());
        orderLineItem.setPlanId(planId);
        orderLineItem.setChargeId(chargeId);
        orderLineItem.setOrderId(orderId);
        orderLineItem.setAmount(totalAmount);
        orderLineItem.setQuantity(quantity);
        orderLineItem.setEffectiveDate(start);
        orderLineItem.setEndDate(end);
        orderLineItem.setIsRamp(isRamp);
        orderLineItem.setArrOverride(arrOverride);

        return orderLineItem;
    }

    private static Charge getPerUnitCharge(String chargeId, ChargeType chargeType, BigDecimal amount, boolean shouldTrackArr) {
        return new MockChargeBuilder()
            .withChargeId(chargeId)
            .withAmount(amount)
            .withChargeType(chargeType)
            .shouldTrackArr(shouldTrackArr)
            .yearly()
            .build();
    }

    private List<BillingPeriod> getAnnualBillingPeriods(ZonedDateTime start, int count) {
        List<BillingPeriod> billingPeriods = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            billingPeriods.add(
                new BillingPeriod(start.toInstant(), start.plusYears(1).toInstant(), start.plusYears(1).toInstant(), new Recurrence(Cycle.YEAR, 1))
            );
            start = start.plusYears(1);
        }

        return billingPeriods;
    }

    private List<InvoiceItem> getAnnualInvoiceItems(ZonedDateTime start, List<BigDecimal> amounts) {
        List<InvoiceItem> invoiceItems = new ArrayList<>();

        for (BigDecimal amount : amounts) {
            invoiceItems.add(
                new InvoiceItem.InvoiceItemBuilder()
                    .amount(amount)
                    .periodStartDate(start.toInstant())
                    .periodEndDate(start.plusYears(1).toInstant())
                    .createInvoiceItem()
            );
            start = start.plusYears(1);
        }

        return invoiceItems;
    }
}
