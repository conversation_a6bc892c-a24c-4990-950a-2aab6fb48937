package com.subskribe.billy.metrics.datadog.trace;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import datadog.trace.api.DDTags;
import io.opentracing.Span;
import io.opentracing.Tracer;
import io.opentracing.log.Fields;
import io.opentracing.tag.Tag;
import io.opentracing.tag.Tags;
import java.util.Map;
import java.util.UUID;
import org.aopalliance.intercept.MethodInvocation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatcher;
import org.slf4j.MDC;

class MethodTraceInterceptorTest {

    private MethodTraceInterceptor interceptor;
    private Tracer tracer;

    @BeforeEach
    void setUp() {
        tracer = mock(Tracer.class);
        interceptor = new MethodTraceInterceptor(() -> tracer);
    }

    @Test
    void uninstrumentedCallsAreIgnored() throws Throwable {
        MethodInvocation methodInvocation = setupInvocationStub(new UninstrumentedUtil());

        interceptor.invoke(methodInvocation);

        verifyNoInteractions(tracer);
        verify(methodInvocation, times(1)).proceed();
    }

    @Test
    void annotatedMethodsAreInstrumented() throws Throwable {
        assertInstrumentationWithClass(new MethodInstrumentedUtil(), "MethodInstrumentedUtil.testMethod");
    }

    @Test
    void annotatedClassesAreInstrumented() throws Throwable {
        assertInstrumentationWithClass(new ClassInstrumentedUtil(), "ClassInstrumentedUtil.testMethod");
    }

    @Test
    void suffixMatchingClassesAreInstrumented() throws Throwable {
        assertInstrumentationWithClass(new SomeTestService(), "SomeTestService.testMethod");
    }

    @Test
    void disabledClassesAreNotInstrumented() throws Throwable {
        assertNoInstrumentationWithClass(new ClassDisabledUtil(), "ClassDisabledUtil.testMethod");
    }

    @Test
    void disabledMethodsAreNotInstrumented() throws Throwable {
        assertNoInstrumentationWithClass(new MethodDisabledUtil(), "MethodDisabledUtil.testMethod");
    }

    @Test
    void tenantIdAddedAsTag() throws Throwable {
        String tenantId = UUID.randomUUID().toString();
        MDC.put(BillyAuthPrincipal.TENANT_ID_LOG_FIELD, tenantId);
        Span span = assertInstrumentationWithClass(new SomeTestService(), "SomeTestService.testMethod");
        verify(span, times(1)).setTag(BillyAuthPrincipal.TENANT_ID_LOG_FIELD, tenantId);
    }

    @Test
    void exceptionsLoggedAndRethrown() throws Throwable {
        RuntimeException toThrow = new RuntimeException("test exception");
        MethodInvocation methodInvocation = setupInvocationStub(new SomeTestService());
        Tracer.SpanBuilder builder = mock(Tracer.SpanBuilder.class);
        Span span = mock(Span.class);
        when(tracer.buildSpan(MethodTraceInterceptor.OPERATION_NAME)).thenReturn(builder);
        when(builder.withTag(DDTags.RESOURCE_NAME, "SomeTestService.testMethod")).thenReturn(builder);
        when(builder.start()).thenReturn(span);
        when(methodInvocation.proceed()).thenThrow(toThrow);

        assertThatExceptionOfType(RuntimeException.class).isThrownBy(() -> interceptor.invoke(methodInvocation));

        verify(builder, times(1)).start();
        verify(tracer, times(1)).activateSpan(span);
        verify(methodInvocation, times(1)).proceed();
        verify(span, times(1)).finish();
        verify(span, times(1)).setTag(
            argThat((ArgumentMatcher<Tag<Boolean>>) s -> s.getKey().equals(Tags.ERROR.getKey())), // BooleanTag does not implement equals()
            eq(true)
        );
        verify(span, times(1)).log(Map.of(Fields.ERROR_OBJECT, toThrow));
    }

    private Span assertInstrumentationWithClass(TestClass invocationTarget, String expectedResourceName) throws Throwable {
        MethodInvocation methodInvocation = setupInvocationStub(invocationTarget);
        Tracer.SpanBuilder builder = mock(Tracer.SpanBuilder.class);
        Span span = mock(Span.class);
        when(tracer.buildSpan(MethodTraceInterceptor.OPERATION_NAME)).thenReturn(builder);
        when(builder.withTag(DDTags.RESOURCE_NAME, expectedResourceName)).thenReturn(builder);
        when(builder.start()).thenReturn(span);

        interceptor.invoke(methodInvocation);

        verify(builder, times(1)).start();
        verify(tracer, times(1)).activateSpan(span);
        verify(methodInvocation, times(1)).proceed();
        verify(span, times(1)).finish();
        return span;
    }

    private Span assertNoInstrumentationWithClass(TestClass invocationTarget, String expectedResourceName) throws Throwable {
        MethodInvocation methodInvocation = setupInvocationStub(invocationTarget);
        Tracer.SpanBuilder builder = mock(Tracer.SpanBuilder.class);
        Span span = mock(Span.class);
        when(tracer.buildSpan(MethodTraceInterceptor.OPERATION_NAME)).thenReturn(builder);
        when(builder.withTag(DDTags.RESOURCE_NAME, expectedResourceName)).thenReturn(builder);
        when(builder.start()).thenReturn(span);

        interceptor.invoke(methodInvocation);

        verify(builder, never()).start();
        verify(tracer, never()).activateSpan(span);
        verify(methodInvocation, times(1)).proceed();
        verify(span, never()).finish();
        return span;
    }

    private MethodInvocation setupInvocationStub(TestClass testClass) throws Exception {
        MethodInvocation methodInvocation = mock(MethodInvocation.class);
        when(methodInvocation.getMethod()).thenReturn(testClass.getClass().getMethod("testMethod"));
        return methodInvocation;
    }

    interface TestClass {
        void testMethod();
    }

    private static class UninstrumentedUtil implements TestClass {

        public void testMethod() {}
    }

    private static class MethodInstrumentedUtil implements TestClass {

        @Trace
        public void testMethod() {}
    }

    @Trace
    private static class ClassInstrumentedUtil implements TestClass {

        public void testMethod() {}
    }

    private static class MethodDisabledUtil implements TestClass {

        @DisableTrace
        public void testMethod() {}
    }

    @DisableTrace
    private static class ClassDisabledUtil implements TestClass {

        public void testMethod() {}
    }

    private static class SomeTestService implements TestClass {

        public void testMethod() {}
    }
}
