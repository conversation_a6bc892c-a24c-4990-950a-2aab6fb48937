package com.subskribe.billy.log;

import static com.subskribe.billy.resources.GqlResource.GQL_ERROR_REQUEST_CONTEXT_PROPERTY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.auth.model.UserPrincipal;
import com.subskribe.billy.graphql.user.GqlErrorCategory;
import com.subskribe.billy.resources.GqlResource;
import com.subskribe.billy.security.audit.AuditedClassStub;
import com.subskribe.billy.security.audit.AuditedMethodStub;
import com.subskribe.billy.security.audit.NonAuditedClassStub;
import com.subskribe.billy.tracing.RequestIdProvider;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import graphql.ErrorType;
import graphql.GraphQLError;
import graphql.GraphqlErrorBuilder;
import graphql.execution.ExecutionStepInfo;
import graphql.execution.ResultPath;
import graphql.language.Field;
import graphql.language.SourceLocation;
import graphql.schema.DataFetchingEnvironment;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerResponseContext;
import javax.ws.rs.container.ResourceInfo;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.SecurityContext;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.glassfish.jersey.internal.MapPropertiesDelegate;
import org.glassfish.jersey.server.ContainerRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class RequestResponseLogFilterTest {

    private LogStorageProvider logStorageProvider;
    private RequestLoggingConfiguration requestLoggingConfiguration;
    private ResourceInfo resourceInfo;
    private CurrentUserProvider currentUserProvider;

    private RequestResponseLogFilter filter;

    private static final MultivaluedMap<String, String> STUB_HEADERS = new MultivaluedHashMap<>(
        Map.of("headerKey1", "headerValue1", "headerKey2", "headerValue2")
    );

    @BeforeEach
    void setUp() throws Exception {
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        currentUserProvider = mock(CurrentUserProvider.class);
        requestLoggingConfiguration = mock(RequestLoggingConfiguration.class);
        when(billyConfiguration.getRequestLoggingConfiguration()).thenReturn(requestLoggingConfiguration);
        when(requestLoggingConfiguration.getRequestLoggingEnabled()).thenReturn(true);

        logStorageProvider = mock(LogStorageProvider.class);
        when(logStorageProvider.storeLogItem(any())).thenReturn("storageLocation");

        resourceInfo = mock(ResourceInfo.class);
        setupNonAuditedResource();

        filter = new RequestResponseLogFilter(logStorageProvider, billyConfiguration, currentUserProvider);
        filter.resourceInfo = resourceInfo;
    }

    @Test
    void testCorrectPayloadSentToStorageForAuditedCall() throws Exception {
        TestContext testContext = setupRequestResponse();

        doReturn(AuditedClassStub.class).when(resourceInfo).getResourceClass();
        doReturn(AuditedClassStub.class.getDeclaredMethod("nonAuditedMethod")).when(resourceInfo).getResourceMethod();

        filter.filter(testContext.requestContext);
        filter.filter(testContext.requestContext, testContext.responseContext);

        verifyLogWithReason(testContext, RequestResponseLogReason.AUDITED);
    }

    @Test
    void testCorrectPayloadSentToStorageWhenOnlyMethodIsAudited() throws Exception {
        TestContext testContext = setupRequestResponse();

        doReturn(AuditedMethodStub.class).when(resourceInfo).getResourceClass();
        doReturn(AuditedMethodStub.class.getDeclaredMethod("auditedMethod")).when(resourceInfo).getResourceMethod();

        filter.filter(testContext.requestContext);
        filter.filter(testContext.requestContext, testContext.responseContext);

        verify(logStorageProvider, times(1)).storeLogItem(any(LogItem.class));
    }

    @Test
    void testCorrectPayloadSentToStorageForServerError() throws Exception {
        TestContext testContext = setupRequestResponse(500);

        filter.filter(testContext.requestContext);
        filter.filter(testContext.requestContext, testContext.responseContext);

        verifyLogWithReason(testContext, RequestResponseLogReason.REQUEST_FAILED);
    }

    @Test
    void testCorrectPayloadSentToStorageForGqlServerError() throws Exception {
        TestContext testContext = setupRequestResponse(200);

        doReturn(GqlResource.class).when(resourceInfo).getResourceClass();
        doReturn(GqlResource.class.getDeclaredMethods()[0]).when(resourceInfo).getResourceMethod();

        List<GraphQLError> errors = new ArrayList<>();
        Map<String, Object> extensions = Map.of(GqlErrorCategory.GQL_ERROR_CATEGORY, GqlErrorCategory.SERVER_ERROR);
        errors.add(
            GraphqlErrorBuilder.newError(createDataFetchingEnvironment())
                .extensions(extensions)
                .errorType(ErrorType.ExecutionAborted)
                .message("testMessage")
                .build()
        );

        testContext.requestContext.setProperty(GQL_ERROR_REQUEST_CONTEXT_PROPERTY, errors);

        filter.filter(testContext.requestContext);
        filter.filter(testContext.requestContext, testContext.responseContext);

        verifyLogWithReason(testContext, RequestResponseLogReason.REQUEST_FAILED);
    }

    @Test
    void testCorrectPayloadSentToStorageForBadRequest() throws Exception {
        TestContext testContext = setupRequestResponse(400);

        filter.filter(testContext.requestContext);
        filter.filter(testContext.requestContext, testContext.responseContext);

        verifyLogWithReason(testContext, RequestResponseLogReason.BAD_REQUEST);
    }

    private void verifyLogWithReason(TestContext testContext, RequestResponseLogReason requestFailed) {
        verify(logStorageProvider, times(1)).storeLogItem(
            new RequestResponseLogItem(
                testContext.requestId,
                STUB_HEADERS,
                "/test/resource",
                testContext.inputBody,
                testContext.responseEntity,
                "<EMAIL>",
                requestFailed
            )
        );
    }

    @Test
    void testInputStreamStillReadableAfterProcessing() throws Exception {
        TestContext testContext = setupRequestResponse();

        filter.filter(testContext.requestContext);
        filter.filter(testContext.requestContext, testContext.responseContext);

        String requestStreamString = IOUtils.toString(testContext.requestContext.getEntityStream(), StandardCharsets.UTF_8);

        assertThat(requestStreamString).isEqualTo(testContext.inputBody);
    }

    @Test
    void testRequestIgnoredIfConfigurationIsTurnedOff() throws Exception {
        TestContext testContext = setupRequestResponse();
        ContainerRequestContext requestSpy = spy(testContext.requestContext);

        when(requestLoggingConfiguration.getRequestLoggingEnabled()).thenReturn(false);

        filter.filter(requestSpy);
        filter.filter(requestSpy, testContext.responseContext);

        verify(testContext.responseContext, never()).getEntity();
    }

    @Test
    void testRequestIgnoredIfClassAndMethodAreNotAudited() throws Exception {
        TestContext testContext = setupRequestResponse();
        ContainerRequestContext requestSpy = spy(testContext.requestContext);

        filter.filter(requestSpy);
        filter.filter(requestSpy, testContext.responseContext);

        verify(testContext.responseContext, never()).getEntity();
    }

    @Test
    void testRequestIgnoredIfClassIsNull() throws Exception {
        TestContext testContext = setupRequestResponse();
        ContainerRequestContext requestSpy = spy(testContext.requestContext);

        doReturn(null).when(resourceInfo).getResourceClass();

        filter.filter(requestSpy);
        filter.filter(requestSpy, testContext.responseContext);

        verify(testContext.responseContext, never()).getEntity();
    }

    @Test
    void testRequestIgnoredIfMethodIsNull() throws Exception {
        TestContext testContext = setupRequestResponse();
        ContainerRequestContext requestSpy = spy(testContext.requestContext);

        doReturn(null).when(resourceInfo).getResourceMethod();

        filter.filter(requestSpy);
        filter.filter(requestSpy, testContext.responseContext);

        verify(testContext.responseContext, never()).getEntity();
    }

    private TestContext setupRequestResponse() {
        return setupRequestResponse(200);
    }

    // Stubbing a servlet request is not easy!
    private TestContext setupRequestResponse(int statusCode) {
        SecurityContext securityContext = mock(SecurityContext.class);
        doReturn(Optional.of(stubPrincipal())).when(currentUserProvider).getBillyAuthPrincipal();
        String inputBody = RandomStringUtils.randomAlphabetic(100);
        InputStream inputStream = new ByteArrayInputStream(inputBody.getBytes(StandardCharsets.UTF_8));
        String requestId = RandomStringUtils.randomAlphabetic(10);

        Object responseEntity = Map.of("responseKey1", "responseValue1", "responseKey2", "responseValue2");

        ContainerRequestContext requestContext = new ContainerRequest(
            URI.create("http://localhost:3000"),
            URI.create("test/resource"),
            "POST",
            securityContext,
            new MapPropertiesDelegate(new HashMap<>(Map.of(RequestIdProvider.REQUEST_ID_FIELD_NAME, requestId))),
            null
        );

        requestContext.setEntityStream(inputStream);
        STUB_HEADERS.forEach((key, value) -> requestContext.getHeaders().addAll(key, value));

        ContainerResponseContext containerResponseContext = mock(ContainerResponseContext.class);
        when(containerResponseContext.getEntity()).thenReturn(responseEntity);
        when(containerResponseContext.getStatusInfo()).thenReturn(Response.Status.fromStatusCode(statusCode));

        return new TestContext(inputBody, inputStream, responseEntity, STUB_HEADERS, requestId, requestContext, containerResponseContext);
    }

    private void setupNonAuditedResource() throws Exception {
        doReturn(NonAuditedClassStub.class).when(resourceInfo).getResourceClass();
        doReturn(NonAuditedClassStub.class.getDeclaredMethod("nonAuditedMethod")).when(resourceInfo).getResourceMethod();
    }

    private BillyAuthPrincipal stubPrincipal() {
        UserPrincipal userPrincipal = new UserPrincipal(
            "id",
            "clientId",
            "ADMIN",
            "<EMAIL>",
            "USER-TEST",
            "tenantId",
            EntityContext.builder().build(),
            false
        );
        return new BillyAuthPrincipal(userPrincipal);
    }

    private static class TestContext {

        final String inputBody;
        final InputStream inputStream;
        final Object responseEntity;
        final MultivaluedMap<String, String> headers;
        final String requestId;

        ContainerRequestContext requestContext;
        ContainerResponseContext responseContext;

        TestContext(
            String inputBody,
            InputStream inputStream,
            Object responseEntity,
            MultivaluedMap<String, String> headers,
            String requestId,
            ContainerRequestContext requestContext,
            ContainerResponseContext responseContext
        ) {
            this.inputBody = inputBody;
            this.inputStream = inputStream;
            this.responseEntity = responseEntity;
            this.headers = headers;
            this.requestId = requestId;
            this.requestContext = requestContext;
            this.responseContext = responseContext;
        }
    }

    private DataFetchingEnvironment createDataFetchingEnvironment() {
        DataFetchingEnvironment dataFetchingEnvironment = mock(DataFetchingEnvironment.class);
        Field field = mock(Field.class);
        ExecutionStepInfo executionStepInfo = mock(ExecutionStepInfo.class);
        when(dataFetchingEnvironment.getField()).thenReturn(field);
        when(dataFetchingEnvironment.getExecutionStepInfo()).thenReturn(executionStepInfo);
        when(field.getSourceLocation()).thenReturn(SourceLocation.EMPTY);
        when(executionStepInfo.getPath()).thenReturn(ResultPath.rootPath());
        return dataFetchingEnvironment;
    }
}
