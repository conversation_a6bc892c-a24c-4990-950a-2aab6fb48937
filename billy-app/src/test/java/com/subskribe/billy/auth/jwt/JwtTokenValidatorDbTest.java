package com.subskribe.billy.auth.jwt;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.core.type.TypeReference;
import com.subskribe.billy.auth.db.AuthTenantCognitoDAO;
import com.subskribe.billy.auth.model.AuthTenantCognito;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.ClientIdJwtTokens;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.auth.model.UserAuthCacheFields;
import com.subskribe.billy.auth.services.TenantCognitoService;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.entity.db.EntityAuthDAO;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.service.EntityAuthContextResolver;
import com.subskribe.billy.exception.EntityNotFoundException;
import com.subskribe.billy.tenant.fixtures.TenantFixture;
import com.subskribe.billy.test.WithDb;
import com.subskribe.billy.user.fixture.UserFixture;
import com.subskribe.billy.user.model.User;
import java.io.IOException;
import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.regions.Region;

public class JwtTokenValidatorDbTest extends WithDb {

    private static final String ACCESS_TOKEN = "access token";
    private static final String ID_TOKEN = "id token";
    private static final String CLIENT_ID = "client id";
    private static final String USER_POOL_ID = "user pool id";
    private static final String DOMAIN_NAME = "domain name";

    private final CacheService cacheService = mock(CacheService.class);
    private final TenantCognitoService tenantCognitoService = mock(TenantCognitoService.class);
    private JWTTokenValidator jwtTokenValidator;

    @BeforeAll
    @AllowNonRlsDataAccess
    public void setUp() throws IOException {
        EntityFixture.initDb(dslContextProvider);
        AuthTenantCognito authTenantCognito = insertAuthTenantCognito();
        when(tenantCognitoService.getCognitoInfoByClientId(CLIENT_ID)).thenReturn(Optional.of(authTenantCognito));
        UserFixture.initDb(dslContextProvider);
        EntityAuthDAO entityAuthDAO = new EntityAuthDAO(dslContextProvider);
        EntityAuthContextResolver entityAuthContextResolver = new EntityAuthContextResolver(dslContextProvider, cacheService, entityAuthDAO);
        JWTTokenValidator realJwtTokenValidator = new JWTTokenValidator(
            Region.US_EAST_2,
            tenantCognitoService,
            dslContextProvider.get(),
            cacheService,
            entityAuthContextResolver
        );
        jwtTokenValidator = spy(realJwtTokenValidator);
        setupJwtTokenValidation();
        setupCache();
    }

    @AllowNonRlsDataAccess
    private AuthTenantCognito insertAuthTenantCognito() {
        var authTenantCognito = new AuthTenantCognito(
            UUID.randomUUID(),
            TenantFixture.TENANT_1_ID,
            CLIENT_ID,
            USER_POOL_ID,
            DOMAIN_NAME,
            false,
            Instant.now(),
            Instant.now()
        );
        var authTenantCognitoDAO = new AuthTenantCognitoDAO();
        authTenantCognitoDAO.addTenantCognitoInformation(dslContextProvider.get(), authTenantCognito);
        return authTenantCognito;
    }

    private void setupJwtTokenValidation() {
        doReturn(Optional.of(CLIENT_ID)).when(jwtTokenValidator).getClientIdFromToken(any());
        doReturn(Optional.of(USER_POOL_ID)).when(jwtTokenValidator).getUserPoolId(any());

        DecodedJWT decodedAccessToken = mock(DecodedJWT.class);
        when(decodedAccessToken.getSubject()).thenReturn(ACCESS_TOKEN);
        doReturn(Optional.of(decodedAccessToken)).when(jwtTokenValidator).decodeAndVerifyAccessToken(any(), any());

        Claim idTokenClaim = mock(Claim.class);
        when(idTokenClaim.asString()).thenReturn("<EMAIL>");
        DecodedJWT decodedIdToken = mock(DecodedJWT.class);
        when(decodedIdToken.getClaim("email")).thenReturn(idTokenClaim);
        doReturn(Optional.of(decodedIdToken)).when(jwtTokenValidator).decodeAndVerifyIdToken(any(), any());
    }

    @AllowNonRlsDataAccess
    private void setupCache() {
        User user = UserFixture.getDefault();
        UserAuthCacheFields userAuthCacheFields = jwtTokenValidator.getUserAuthInfoByEmail(dslContextProvider.get(), user.getEmail());
        when(
            cacheService.getSerialized(
                eq(TenantFixture.TENANT_1_ID),
                eq(CacheType.EMAIL_TO_USER_AUTH_V2),
                any(),
                any(),
                eq(UserAuthCacheFields.class)
            )
        ).thenReturn(userAuthCacheFields);
        when(cacheService.getSerialized(eq(TenantFixture.TENANT_1_ID), eq(CacheType.TENANT_USER_AUTH), any(), any(), eq(User.class))).thenReturn(
            user
        );
        when(
            cacheService.getSerialized(
                eq(TenantFixture.TENANT_1_ID),
                eq(CacheType.TENANT_ENTITY_IDS),
                eq(TenantFixture.TENANT_1_ID),
                any(),
                any(TypeReference.class)
            )
        ).thenReturn(Set.of(EntityFixture.ENTITY_1_ID, EntityFixture.ENTITY_2_ID));
    }

    // when invalid entity, it should return entity context of []
    @Test
    public void testInvalidEntityId() {
        String entityId = "invalid entity id";
        ClientIdJwtTokens clientIdJwtTokens = new ClientIdJwtTokens(ACCESS_TOKEN, ID_TOKEN, Optional.of(entityId));
        Assertions.assertThatThrownBy(() -> jwtTokenValidator.validate(clientIdJwtTokens))
            .isInstanceOf(EntityNotFoundException.class)
            .hasMessage(EntityContext.USER_OR_API_HAS_NO_ENTITY_ACCESS);
    }

    // when "entity 1", it should return entity context of [ entity 1 ]
    @Test
    public void testValidEntityId() {
        String entityId = EntityFixture.ENTITY_1_ID;
        ClientIdJwtTokens clientIdJwtTokens = new ClientIdJwtTokens(ACCESS_TOKEN, ID_TOKEN, Optional.of(entityId));
        Optional<BillyAuthPrincipal> optionalBillyAuthPrincipal = jwtTokenValidator.validate(clientIdJwtTokens);
        Assertions.assertThat(optionalBillyAuthPrincipal).isNotEmpty();
        BillyAuthPrincipal billyAuthPrincipal = optionalBillyAuthPrincipal.get();
        EntityContext entityContext = billyAuthPrincipal.getEntityContext();
        Assertions.assertThat(entityContext.hasFullSelection()).isFalse();
        Assertions.assertThat(entityContext.hasFullAuthorization()).isFalse();
        Assertions.assertThat(entityContext.getSelectedIds()).containsExactly(entityId);
        Assertions.assertThat(entityContext.getAuthorizedIds()).containsExactlyInAnyOrder(UserFixture.ENTITY_IDS.toArray(new String[0]));
    }

    // when "entity 2", it should return entity context of [ entity 2 ]
    @Test
    public void testValidAltEntityId() {
        String entityId = EntityFixture.ENTITY_2_ID;
        ClientIdJwtTokens clientIdJwtTokens = new ClientIdJwtTokens(ACCESS_TOKEN, ID_TOKEN, Optional.of(entityId));
        Optional<BillyAuthPrincipal> optionalBillyAuthPrincipal = jwtTokenValidator.validate(clientIdJwtTokens);
        Assertions.assertThat(optionalBillyAuthPrincipal).isNotEmpty();
        BillyAuthPrincipal billyAuthPrincipal = optionalBillyAuthPrincipal.get();
        EntityContext entityContext = billyAuthPrincipal.getEntityContext();
        Assertions.assertThat(entityContext.hasFullSelection()).isFalse();
        Assertions.assertThat(entityContext.hasFullAuthorization()).isFalse();
        Assertions.assertThat(entityContext.getSelectedIds()).containsExactly(entityId);
        Assertions.assertThat(entityContext.getAuthorizedIds()).containsExactlyInAnyOrder(UserFixture.ENTITY_IDS.toArray(new String[0]));
    }

    // when "*", it should return entity context of [ entity 1, entity 2 ]
    @Test
    public void testAllEntityId() {
        String entityId = EntityContext.ALL_ACCESS_ID;
        ClientIdJwtTokens clientIdJwtTokens = new ClientIdJwtTokens(ACCESS_TOKEN, ID_TOKEN, Optional.of(entityId));
        Optional<BillyAuthPrincipal> optionalBillyAuthPrincipal = jwtTokenValidator.validate(clientIdJwtTokens);
        Assertions.assertThat(optionalBillyAuthPrincipal).isNotEmpty();
        BillyAuthPrincipal billyAuthPrincipal = optionalBillyAuthPrincipal.get();
        EntityContext entityContext = billyAuthPrincipal.getEntityContext();
        Assertions.assertThat(entityContext.hasFullSelection()).isTrue();
        Assertions.assertThat(entityContext.hasFullAuthorization()).isFalse();
        Assertions.assertThat(entityContext.getSelectedIds()).containsExactly(UserFixture.ENTITY_IDS.toArray(new String[0]));
        Assertions.assertThat(entityContext.getAuthorizedIds()).containsExactlyInAnyOrder(UserFixture.ENTITY_IDS.toArray(new String[0]));
    }
}
