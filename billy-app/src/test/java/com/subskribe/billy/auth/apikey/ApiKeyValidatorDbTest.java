package com.subskribe.billy.auth.apikey;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.core.type.TypeReference;
import com.subskribe.billy.auth.apikey.db.ApiKeyDAO;
import com.subskribe.billy.auth.fixture.ApiKeyFixture;
import com.subskribe.billy.auth.model.ApiKeyContext;
import com.subskribe.billy.auth.model.ApiKeySecretValue;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.entity.db.EntityAuthDAO;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.service.EntityAuthContextResolver;
import com.subskribe.billy.exception.EntityNotFoundException;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.tenant.fixtures.TenantFixture;
import com.subskribe.billy.test.WithDb;
import com.subskribe.billy.user.fixture.UserFixture;
import com.subskribe.billy.user.model.User;
import java.io.IOException;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

public class ApiKeyValidatorDbTest extends WithDb {

    public static final UUID API_KEY_ID_ENT_1 = UUID.randomUUID();
    public static final UUID API_KEY_ID_ENT_2 = UUID.randomUUID();
    public static final UUID API_KEY_ID_ENT_ALL = UUID.randomUUID();
    public static final UUID API_KEY_ID_ENT_INVALID = UUID.randomUUID();

    private final CacheService cacheService = mock(CacheService.class);
    private final SecretsService secretsService = mock(SecretsService.class);
    private final ApiKeyRsaKeyPairProvider apiKeyRsaKeyPairProvider = mock(ApiKeyRsaKeyPairProvider.class);
    private ApiKeyValidator apiKeyValidator;

    @BeforeAll
    public void setUp() throws IOException {
        EntityFixture.initDb(dslContextProvider);
        ApiKeyDAO apiKeyDAO = new ApiKeyDAO(dslContextProvider);
        EntityAuthDAO entityAuthDAO = new EntityAuthDAO(dslContextProvider);
        when(secretsService.validateApiKeyV3(any(), any(), any())).thenReturn(true);
        EntityAuthContextResolver entityAuthContextResolver = new EntityAuthContextResolver(dslContextProvider, cacheService, entityAuthDAO);
        ApiKeyValidator realApiKeyValidator = new ApiKeyValidator(secretsService, apiKeyRsaKeyPairProvider, apiKeyDAO, entityAuthContextResolver);
        apiKeyValidator = spy(realApiKeyValidator);
        UserFixture.initDb(dslContextProvider);

        setupCache();
        doReturn(Optional.of(new TenantId(TenantFixture.TENANT_1_ID))).when(apiKeyValidator).getTenantIdFromToken(any());
        insertApiKey(API_KEY_ID_ENT_1, EntityFixture.ENTITY_1_ID);
        insertApiKey(API_KEY_ID_ENT_2, EntityFixture.ENTITY_2_ID);
        insertApiKey(API_KEY_ID_ENT_ALL, EntityContext.ALL_ACCESS_ID);
        insertApiKey(API_KEY_ID_ENT_INVALID, "invalid entity id");
    }

    private void insertApiKey(UUID apiKeyId, String entityId) {
        var apiKeyMetaData = ApiKeyFixture.builder().id(apiKeyId).entityId(entityId).build();
        ApiKeyFixture.insertApiKey(dslContextProvider, apiKeyMetaData);
    }

    private void setupCache() {
        User user = UserFixture.getDefaultFromDB(dslContextProvider);
        when(cacheService.getSerialized(any(), eq(CacheType.TENANT_USER_AUTH), any(), any(), eq(User.class))).thenReturn(user);
        when(
            cacheService.getSerialized(
                eq(TenantFixture.TENANT_1_ID),
                eq(CacheType.TENANT_ENTITY_IDS),
                eq(TenantFixture.TENANT_1_ID),
                any(),
                any(TypeReference.class)
            )
        ).thenReturn(Set.of(EntityFixture.ENTITY_1_ID, EntityFixture.ENTITY_2_ID));
    }

    // make `verifyJwtApiToken` return a valid jwt token with the given api key id
    // when the provided api key secret value is used
    private void setupJwtTokenValidation(ApiKeySecretValue apiKeySecretValue, String apiKeyId) {
        Claim apiSecretClaim = mock(Claim.class);
        doReturn("api secret").when(apiSecretClaim).asString();
        Claim keyIdClaim = mock(Claim.class);
        doReturn(apiKeyId).when(keyIdClaim).asString();
        DecodedJWT decodedJWT = mock(DecodedJWT.class);
        doReturn(apiSecretClaim).when(decodedJWT).getClaim(ApiKeyGenerator.API_KEY_CLAIM_NAME);
        doReturn(keyIdClaim).when(decodedJWT).getClaim(ApiKeyGenerator.KEY_ID_CLAIM_NAME);
        doReturn(Optional.of(decodedJWT)).when(apiKeyValidator).verifyJwtApiToken(eq(new TenantId(TenantFixture.TENANT_1_ID)), eq(apiKeySecretValue));
    }

    // api key: "invalid", header: ""; selectedIds = []
    @Test
    public void testInvalidKey() {
        var apiKeySecret = new ApiKeySecretValue("invalid key");
        setupJwtTokenValidation(apiKeySecret, API_KEY_ID_ENT_INVALID.toString());
        Assertions.assertThatThrownBy(() -> apiKeyValidator.validate(apiKeySecret, "invalid header"))
            .isInstanceOf(EntityNotFoundException.class)
            .hasMessage(EntityContext.USER_OR_API_HAS_NO_ENTITY_ACCESS);
    }

    // api key: "ent-1", header: "invalid"; selectedIds = []
    @Test
    public void testValidKeyInvalidHeader() {
        var apiKeySecret = new ApiKeySecretValue("valid key invalid header");
        setupJwtTokenValidation(apiKeySecret, API_KEY_ID_ENT_1.toString());
        Assertions.assertThatThrownBy(() -> apiKeyValidator.validate(apiKeySecret, "invalid header"))
            .isInstanceOf(EntityNotFoundException.class)
            .hasMessage(EntityContext.USER_OR_API_HAS_NO_ENTITY_ACCESS);
    }

    // api key: "ent-1", header: ""; selectedIds = [ ent-1 ]
    @Test
    public void testValidEntityId() {
        var apiKeySecret = new ApiKeySecretValue("secret 1");
        setupJwtTokenValidation(apiKeySecret, API_KEY_ID_ENT_1.toString());
        Optional<ApiKeyContext> optionalApiKeyContext = apiKeyValidator.validate(apiKeySecret, "");
        Assertions.assertThat(optionalApiKeyContext).isPresent();
        Assertions.assertThat(optionalApiKeyContext.get().getEntityContext()).isNotNull();
        EntityContext entityContext = optionalApiKeyContext.get().getEntityContext();
        Assertions.assertThat(entityContext.getSelectedIds()).isNotEmpty();
        Assertions.assertThat(entityContext.getSelectedIds()).containsExactly(EntityFixture.ENTITY_1_ID);
    }

    // api key: "ent-1", header: "*"; selectedIds = [ ent-1 ]
    @Test
    public void testAllEntityHeader() {
        var apiKeySecret = new ApiKeySecretValue("secret 1");
        setupJwtTokenValidation(apiKeySecret, API_KEY_ID_ENT_1.toString());
        Optional<ApiKeyContext> optionalApiKeyContext = apiKeyValidator.validate(apiKeySecret, EntityContext.ALL_ACCESS_ID);
        Assertions.assertThat(optionalApiKeyContext).isPresent();
        Assertions.assertThat(optionalApiKeyContext.get().getEntityContext()).isNotNull();
        EntityContext entityContext = optionalApiKeyContext.get().getEntityContext();
        Assertions.assertThat(entityContext.getSelectedIds()).isNotEmpty();
        Assertions.assertThat(entityContext.getSelectedIds()).containsExactly(EntityFixture.ENTITY_1_ID);
    }

    // api key: "ent-2", header: ""; selectedIds = [ ent-2 ]
    @Test
    public void testValidAltEntityId() {
        var apiKeySecret = new ApiKeySecretValue("secret 2");
        setupJwtTokenValidation(apiKeySecret, API_KEY_ID_ENT_2.toString());
        Optional<ApiKeyContext> optionalApiKeyContext = apiKeyValidator.validate(apiKeySecret, "");
        Assertions.assertThat(optionalApiKeyContext).isPresent();
        Assertions.assertThat(optionalApiKeyContext.get().getEntityContext()).isNotNull();
        EntityContext entityContext = optionalApiKeyContext.get().getEntityContext();
        Assertions.assertThat(entityContext.getSelectedIds()).isNotEmpty();
        Assertions.assertThat(entityContext.getSelectedIds()).containsExactly(EntityFixture.ENTITY_2_ID);
    }

    // api key: "*", header: ""; selectedIds = [ent-1, ent-2]
    @Test
    public void testAllEntityKey() {
        var apiKeySecret = new ApiKeySecretValue("all entity secret");
        setupJwtTokenValidation(apiKeySecret, API_KEY_ID_ENT_ALL.toString());
        Optional<ApiKeyContext> optionalApiKeyContext = apiKeyValidator.validate(apiKeySecret, "");
        Assertions.assertThat(optionalApiKeyContext).isPresent();
        Assertions.assertThat(optionalApiKeyContext.get().getEntityContext()).isNotNull();
        EntityContext entityContext = optionalApiKeyContext.get().getEntityContext();
        Assertions.assertThat(entityContext.getSelectedIds()).isNotEmpty();
        Assertions.assertThat(entityContext.getSelectedIds()).containsExactlyInAnyOrder(EntityFixture.ENTITY_1_ID, EntityFixture.ENTITY_2_ID);
    }

    // api key: "*", header: "ent-2"; selectedIds = [ent-2]
    @Test
    public void testAllEntityWithHeader() {
        var apiKeySecret = new ApiKeySecretValue("all entity with header");
        setupJwtTokenValidation(apiKeySecret, API_KEY_ID_ENT_ALL.toString());
        Optional<ApiKeyContext> optionalApiKeyContext = apiKeyValidator.validate(apiKeySecret, EntityFixture.ENTITY_2_ID);
        Assertions.assertThat(optionalApiKeyContext).isPresent();
        Assertions.assertThat(optionalApiKeyContext.get().getEntityContext()).isNotNull();
        EntityContext entityContext = optionalApiKeyContext.get().getEntityContext();
        Assertions.assertThat(entityContext.getSelectedIds()).isNotEmpty();
        Assertions.assertThat(entityContext.getSelectedIds()).containsExactly(EntityFixture.ENTITY_2_ID);
    }
}
