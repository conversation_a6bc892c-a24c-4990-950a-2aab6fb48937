package com.subskribe.billy.auth;

import static org.mockito.Mockito.when;

import com.subskribe.billy.auth.services.TenantRlsEncryptionService;
import com.subskribe.billy.aws.secretsmanager.SecretsService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class TenantRlsEncryptionTest {

    // base64 encoded 256 bit randomly generated key for testing
    // warning: do not change this since unit tests depend on this for their encrypted passwords
    public static final String TENANT_RLS_ENCRYPTION_KEY = "hNSR/A+uvQWNrS/WmqlB5nH43Esao3LbVD+YkEhxwc0=";

    @Mock
    private SecretsService secretsService;

    @Test
    void testDecrypt() {
        MockitoAnnotations.openMocks(this);
        when(secretsService.getTenantRlsKeySecret()).thenReturn(TENANT_RLS_ENCRYPTION_KEY);
        TenantRlsEncryptionService tenantRlsEncryptionService = new TenantRlsEncryptionService(secretsService);
        tenantRlsEncryptionService.decryptPassword("2i2mnEFb2ACYlrRknsO0959uEUJgwgPyZAXlmb19hGJ4+i3aCB5SokSCGF8NYaC+");
    }

    @Test
    public void testEncryptAndDecryptTenantRlsPassword() {
        MockitoAnnotations.openMocks(this);
        when(secretsService.getTenantRlsKeySecret()).thenReturn(TENANT_RLS_ENCRYPTION_KEY);
        TenantRlsEncryptionService tenantRlsEncryptionService = new TenantRlsEncryptionService(secretsService);

        String password = tenantRlsEncryptionService.generatePassword();
        String encryptedPassword = tenantRlsEncryptionService.encryptPassword(password);
        String decryptedPassword = tenantRlsEncryptionService.decryptPassword(encryptedPassword);

        Assertions.assertThat(decryptedPassword).isEqualTo(password);
        Assertions.assertThat(encryptedPassword).isNotEqualTo(password);

        for (int i = 0; i < 100; i++) {
            EncryptionThread encryptionThread = new EncryptionThread(tenantRlsEncryptionService);
            encryptionThread.start();

            decryptedPassword = tenantRlsEncryptionService.decryptPassword(encryptedPassword);
            Assertions.assertThat(password).isEqualTo(decryptedPassword);
        }
    }

    // TODO: Create start pistol
    private static class EncryptionThread extends Thread {

        private final TenantRlsEncryptionService tenantRlsEncryptionService;

        public EncryptionThread(TenantRlsEncryptionService tenantRlsEncryptionService) {
            this.tenantRlsEncryptionService = tenantRlsEncryptionService;
        }

        public void run() {
            String password = tenantRlsEncryptionService.generatePassword();
            String encryptedPassword = tenantRlsEncryptionService.encryptPassword(password);
            String decryptedPassword = tenantRlsEncryptionService.decryptPassword(encryptedPassword);

            Assertions.assertThat(decryptedPassword).isEqualTo(password);
            Assertions.assertThat(encryptedPassword).isNotEqualTo(password);
        }
    }
}
