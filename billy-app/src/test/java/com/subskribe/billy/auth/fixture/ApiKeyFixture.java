package com.subskribe.billy.auth.fixture;

import com.subskribe.billy.auth.apikey.db.ApiKeyDAO;
import com.subskribe.billy.auth.model.ApiKeyMetaData;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.tenant.fixtures.TenantFixture;
import com.subskribe.billy.user.fixture.UserFixture;
import com.subskribe.billy.user.model.Role;
import java.time.Instant;
import java.util.UUID;

public class ApiKeyFixture {

    public static final UUID API_KEY_ID = UUID.randomUUID();

    public static void initDb(DSLContextProvider dslContextProvider) {
        insertApiKey(dslContextProvider, getDefault());
    }

    public static void insertApiKey(DSLContextProvider dslContextProvider, ApiKeyMetaData apiKeyMetaData) {
        ApiKeyDAO apiKeyDAO = new ApiKeyDAO(dslContextProvider);
        apiKeyDAO.saveApiKeyMetadata(apiKeyMetaData);
    }

    public static ApiKeyMetaData getDefault() {
        return builder().build();
    }

    public static ApiKeyMetaData.Builder builder() {
        return ApiKeyMetaData.builder()
            .id(API_KEY_ID)
            .tenantId(TenantFixture.TENANT_1_ID)
            .role(Role.ADMIN.name())
            .generatedBy("generatedBy")
            .vaultKeyId("vaultKeyId")
            .entityId(EntityFixture.ENTITY_1_ID)
            .isActive(true)
            .userId(UserFixture.USER_ID)
            .lastLetters("lastLetters")
            .expiresOn(Instant.now().plusSeconds(3600))
            .createdOn(Instant.now())
            .updatedOn(Instant.now());
    }
}
