package com.subskribe.billy.aws.es;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.subskribe.billy.aws.es.model.AndSearchQuery;
import com.subskribe.billy.aws.es.model.ImmutableAndSearchQuery;
import com.subskribe.billy.aws.es.model.ImmutableSearchField;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.test.BillyTestBase;
import java.util.Map;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class SearchRequestTransformTest extends BillyTestBase {

    private static final ObjectMapper TEST_MAPPER = JacksonProvider.defaultMapper()
        .copy()
        .configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true);

    @Test
    public void testBasicTransformationWorksAsExpected() throws Exception {
        AndSearchQuery andSearchQuery = ImmutableAndSearchQuery.builder()
            .tableName("subscription")
            .addFields(
                ImmutableSearchField.builder().fieldName("account_name").fieldValue("acme").build(),
                ImmutableSearchField.builder().fieldName("state").fieldValue("ACTIVE").build()
            )
            .addOutputFields("subscription_id", "orders", "account_name")
            .build();
        Map<String, Object> searchQueryMap = SearchRequestTransform.toElasticSearchQuery(andSearchQuery);
        String output = TEST_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(searchQueryMap);
        String expected = asString(getClass(), "and_query_one.json");
        Assertions.assertThat(output).isEqualTo(expected);
    }

    @Test
    public void testFlattenTransformationWorksAsExpected() throws Exception {
        AndSearchQuery andSearchQuery = ImmutableAndSearchQuery.builder()
            .tableName("subscription")
            .addFields(
                ImmutableSearchField.builder().fieldName("account_name").fieldValue("Acme Inc").build(),
                ImmutableSearchField.builder().fieldName("state").fieldValue("ACTIVE").build()
            )
            .addOutputFields("subscription_id", "orders", "account_name")
            .build();
        Map<String, Object> searchQueryMap = SearchRequestTransform.toElasticSearchQuery(andSearchQuery);
        String output = TEST_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(searchQueryMap);
        String expected = asString(getClass(), "and_query_two.json");
        Assertions.assertThat(output).isEqualTo(expected);
    }
}
