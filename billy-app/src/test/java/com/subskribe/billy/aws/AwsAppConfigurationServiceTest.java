package com.subskribe.billy.aws;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.google.common.base.Ticker;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.appconfig.AwsAppConfigurationService;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.http.SdkHttpResponse;
import software.amazon.awssdk.services.appconfigdata.AppConfigDataClient;
import software.amazon.awssdk.services.appconfigdata.model.GetLatestConfigurationRequest;
import software.amazon.awssdk.services.appconfigdata.model.GetLatestConfigurationResponse;
import software.amazon.awssdk.services.appconfigdata.model.StartConfigurationSessionRequest;
import software.amazon.awssdk.services.appconfigdata.model.StartConfigurationSessionResponse;

public class AwsAppConfigurationServiceTest {

    static final long TTL_SECONDS = 3L;

    /**
     * Used to control the cache's sense of when its ttl has expired.
     */
    static class TestTicker extends Ticker {

        public long elapsedNanos = 0L;

        @Override
        public long read() {
            return elapsedNanos;
        }
    }

    static class MockAppConfigDataClient implements AppConfigDataClient {

        // return the status code and payload (sdkbytes) appropriate for this invocation. this allows
        // a test to verify that after an initial success a subsequent failure
        // is correctly handled
        private final List<Pair<String, Integer>> invocationStates;
        private int invocationNumber;

        public MockAppConfigDataClient(List<Pair<String, Integer>> invocationStates) {
            this.invocationStates = invocationStates;
            invocationNumber = 0;
        }

        @Override
        public String serviceName() {
            return "";
        }

        @Override
        public void close() {}

        @Override
        public GetLatestConfigurationResponse getLatestConfiguration(GetLatestConfigurationRequest request)
            throws AwsServiceException, SdkClientException {
            invocationNumber += 1;
            if (invocationNumber >= invocationStates.size()) {
                invocationNumber = invocationStates.size();
            }

            GetLatestConfigurationResponse.Builder builder = GetLatestConfigurationResponse.builder();
            builder.sdkHttpResponse(
                new SdkHttpResponse() {
                    @Override
                    public Optional<String> statusText() {
                        return Optional.empty();
                    }

                    @Override
                    public int statusCode() {
                        return invocationStates.get(invocationNumber - 1).getRight();
                    }

                    @Override
                    public Map<String, List<String>> headers() {
                        return null;
                    }

                    @Override
                    public Builder toBuilder() {
                        return null;
                    }
                }
            );

            return builder
                .configuration(SdkBytes.fromUtf8String(invocationStates.get(invocationNumber - 1).getLeft()))
                .contentType("application/json")
                .nextPollConfigurationToken("testtoken")
                .nextPollIntervalInSeconds(1)
                .build();
        }

        @Override
        public StartConfigurationSessionResponse startConfigurationSession(StartConfigurationSessionRequest request)
            throws AwsServiceException, SdkClientException {
            return StartConfigurationSessionResponse.builder().initialConfigurationToken("faketoken").build();
        }
    }

    static class AwsAppConfigurationServiceTestImpl extends AwsAppConfigurationService {

        public AwsAppConfigurationServiceTestImpl(BillyConfiguration config, AppConfigDataClient client, Ticker ticker) {
            super(config, client, ticker);
        }
    }

    AwsAppConfigurationServiceTestImpl setup(Ticker ticker, List<Pair<String, Integer>> invocationStates) {
        System.setProperty("aws.region", "us-west-2");

        var config = new BillyConfiguration();
        config.getAppConfigConfiguration().setEnabled(true);
        config.getAppConfigConfiguration().setApplication("billy-test-app");
        config.getAppConfigConfiguration().setEnvironment("billy-test-env");
        config.getAppConfigConfiguration().setConfigurationProfile("billy-test-features");
        config.getAppConfigConfiguration().setRegion("us-west-2");
        config.getAppConfigConfiguration().setInitialDownloadAttempts(2);
        config.getAppConfigConfiguration().setInitialSleepTimeMs(1L);
        config.getAppConfigConfiguration().setTtlSeconds(TTL_SECONDS); // rely on force invalidation for testing

        return new AwsAppConfigurationServiceTestImpl(config, new MockAppConfigDataClient(invocationStates), ticker);
    }

    @Test
    void testStartupSuccess() {
        var awsAppConfig = setup(new TestTicker(), List.of(Pair.of("{feature1:true, feature2:false}", 200)));

        Map<String, Object> features = awsAppConfig.getFeatures();
        assertEquals(Boolean.TRUE, features.get("feature1"));
        assertEquals(Boolean.FALSE, features.get("feature2"));
        assertNull(features.get("feature3"));
    }

    @Test
    void testStartupFailure() {
        boolean success = true;
        try {
            setup(new TestTicker(), List.of(Pair.of("", 400), Pair.of("", 400)));
        } catch (Exception e) {
            success = false;
        }
        assertFalse(success);
    }

    @Test
    void testStartupRetrySuccess() {
        boolean success = true;
        try {
            setup(new TestTicker(), List.of(Pair.of("", 400), Pair.of("{feature1: true}", 200)));
        } catch (Exception e) {
            success = false;
        }
        assert (success);
    }

    @Test
    void testFetchSuccess() {
        var ticker = new TestTicker();
        var awsAppConfig = setup(
            ticker,
            List.of(Pair.of("{feature1:true, feature2:false}", 200), Pair.of("{feature1:false, feature2:true, feature3:false}", 200))
        );
        assertEquals(Boolean.TRUE, awsAppConfig.getFeatures().get("feature1"));
        assertEquals(Boolean.FALSE, awsAppConfig.getFeatures().get("feature2"));
        assertNull(awsAppConfig.getFeatures().get("feature3"));

        ticker.elapsedNanos = TimeUnit.NANOSECONDS.convert(TTL_SECONDS + 1, TimeUnit.SECONDS);

        assertEquals(Boolean.FALSE, awsAppConfig.getFeatures().get("feature1"));
        assertEquals(Boolean.TRUE, awsAppConfig.getFeatures().get("feature2"));
        assertEquals(Boolean.FALSE, awsAppConfig.getFeatures().get("feature3"));
    }

    @Test
    void testFetchFailure() {
        var ticker = new TestTicker();
        var awsAppConfig = setup(
            ticker,
            List.of(Pair.of("{feature1:true, feature2:false}", 200), Pair.of("{feature1:false, feature2:true, feature3:false}", 400))
        );
        assertEquals(Boolean.TRUE, awsAppConfig.getFeatures().get("feature1"));
        assertEquals(Boolean.FALSE, awsAppConfig.getFeatures().get("feature2"));
        assertNull(awsAppConfig.getFeatures().get("feature3"));

        ticker.elapsedNanos = TimeUnit.NANOSECONDS.convert(TTL_SECONDS + 1, TimeUnit.SECONDS);

        // after failure the old values should be returned
        assertEquals(Boolean.TRUE, awsAppConfig.getFeatures().get("feature1"));
        assertEquals(Boolean.FALSE, awsAppConfig.getFeatures().get("feature2"));
        assertNull(awsAppConfig.getFeatures().get("feature3"));
    }
}
