package com.subskribe.billy.db;

import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.subskribe.billy.di.hk2.providers.AllowNonRlsDataAccess;
import com.subskribe.billy.test.WithDb;
import org.jooq.exception.DataAccessException;
import org.junit.jupiter.api.Test;
import org.postgresql.util.PSQLException;

@AllowNonRlsDataAccess
public class DatabaseQueryTimeoutTest extends WithDb {

    @Test
    void testQueryAboveTimeoutTimesOut() {
        assertThatThrownBy(() -> dslContextProvider.get().execute("SELECT pg_sleep(3);"))
            .isInstanceOf(DataAccessException.class)
            .hasCauseInstanceOf(PSQLException.class)
            .hasMessageContaining("ERROR: canceling statement due to user request");
    }

    @Test
    void testQueryBelowTimeoutDoesNotTimeOut() {
        assertThatCode(() -> dslContextProvider.get().execute("SELECT pg_sleep(1);")).doesNotThrowAnyException();
    }
}
