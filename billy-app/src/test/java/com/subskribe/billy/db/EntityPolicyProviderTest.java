package com.subskribe.billy.db;

import static com.subskribe.billy.jooq.default_schema.tables.AccountOrder.ACCOUNT_ORDER;
import static com.subskribe.billy.jooq.default_schema.tables.Subscription.SUBSCRIPTION;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.auth.model.PrincipalType;
import com.subskribe.billy.auth.model.UserPrincipal;
import com.subskribe.billy.di.hk2.providers.EntityPolicyProvider;
import com.subskribe.billy.entity.EntityConfiguration;
import com.subskribe.billy.entity.fixtures.EntityPolicyProviderFixture;
import com.subskribe.billy.exception.EntityNotFoundException;
import com.subskribe.billy.tracing.RequestIdProvider;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import java.util.Optional;
import java.util.Set;
import org.assertj.core.api.Assertions;
import org.jooq.PolicyProvider;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.slf4j.MDC;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EntityPolicyProviderTest {

    private static final Set<String> ALL_ENTITY_IDS = Set.of("ENT-1", "ENT-2");
    private static final Set<String> PARTIAL_ENTITY_IDS = Set.of("ENT-1");

    private static final EntityContext FULL_ACCESS_ENTITY_CONTEXT = EntityContext.builder()
        .selectedIds(ALL_ENTITY_IDS)
        .authorizedIds(ALL_ENTITY_IDS)
        .hasFullSelection(true)
        .hasFullAuthorization(true)
        .build();
    private static final CurrentUserProvider FULL_ACCESS_USER_PROVIDER = buildCurrentUserProvider(FULL_ACCESS_ENTITY_CONTEXT);

    private static final BillyConfiguration RLS_ENABLED_BILLY_CONFIG = buildBillyConfig(true);
    private static final BillyConfiguration RLS_DISABLED_BILLY_CONFIG = buildBillyConfig(false);

    @BeforeAll
    public static void init() {}

    // current user has full entity access and RLS configuration is enabled,
    // then the policy provider should be empty (ie, do not apply filtering by entity)
    @Test
    void testFullAccessEntityProvider() {
        EntityPolicyProvider entityPolicyProvider = new EntityPolicyProvider(FULL_ACCESS_USER_PROVIDER, RLS_ENABLED_BILLY_CONFIG);
        Optional<PolicyProvider> optionalPolicyProvider = entityPolicyProvider.provide();
        assertThat(optionalPolicyProvider).isEmpty();
    }

    // if billy auth principal has no entities under non-request context,
    // then the policy provider should be empty (ie, do not apply filtering by entity)
    @Test
    @Order(1)
    void testEmptyContextEntityProviderForNonRequestContext() {
        EntityPolicyProvider entityPolicyProvider = EntityPolicyProviderFixture.build(Set.of(), PrincipalType.NONE);
        Optional<PolicyProvider> optionalPolicyProvider = entityPolicyProvider.provide();
        assertThat(optionalPolicyProvider).isEmpty();
    }

    // if billy auth principal has no entities under request context,
    // then the policy provider should throw an exception
    @Test
    @Order(2)
    void testEmptyContextEntityProviderForRequestContext() {
        MDC.put(RequestIdProvider.REQUEST_ID_FIELD_NAME, "test-request-id");
        EntityPolicyProvider entityPolicyProvider = EntityPolicyProviderFixture.build(Set.of(), PrincipalType.USER);
        Assertions.assertThatThrownBy(entityPolicyProvider::provide)
            .isInstanceOf(EntityNotFoundException.class)
            .hasMessageContaining(EntityContext.USER_OR_API_HAS_NO_ENTITY_ACCESS);
    }

    // when current user provider is present but RLS configuration is disabled,
    // then the policy provider should be empty (ie, do not apply filtering by entity)
    @Test
    void testEntityRlsDisabled() {
        EntityPolicyProvider entityPolicyProvider = new EntityPolicyProvider(FULL_ACCESS_USER_PROVIDER, RLS_DISABLED_BILLY_CONFIG);
        Optional<PolicyProvider> optionalPolicyProvider = entityPolicyProvider.provide();
        assertThat(optionalPolicyProvider).isEmpty();
    }

    // when RLS configuration is enabled but current user provider is null,
    // then the policy provider should be empty (ie, do not apply filtering by entity)
    @Test
    void testMissingUserProvider() {
        EntityPolicyProvider entityPolicyProvider = new EntityPolicyProvider(null, RLS_ENABLED_BILLY_CONFIG);
        Optional<PolicyProvider> optionalPolicyProvider = entityPolicyProvider.provide();
        assertThat(optionalPolicyProvider).isEmpty();
    }

    // when entity context has full authorization and full selection,
    // the policy provider should be null (ie, has full access to all entities)
    @Test
    void testFullAuthorizationAndFullSelection() {
        EntityPolicyProvider entityPolicyProvider = new EntityPolicyProvider(null, null);
        Optional<PolicyProvider> policyProvider = entityPolicyProvider.buildPolicyProviderByEntityContext(FULL_ACCESS_ENTITY_CONTEXT);
        assertThat(policyProvider).isEmpty();
    }

    // when entity context has full authorization but partial selection,
    // there should be valid policy provider with entity filtering
    @Test
    void testFullAuthorizationAndPartialSelection() {
        EntityContext entityContext = EntityContext.builder()
            .selectedIds(PARTIAL_ENTITY_IDS)
            .authorizedIds(ALL_ENTITY_IDS)
            .hasFullSelection(false)
            .hasFullAuthorization(true)
            .build();
        EntityPolicyProvider entityPolicyProvider = new EntityPolicyProvider(null, null);
        Optional<PolicyProvider> policyProvider = entityPolicyProvider.buildPolicyProviderByEntityContext(entityContext);
        assertThat(policyProvider).isPresent();
        assertPolicyProviderWithSingleEntity(policyProvider.get());
    }

    // when entity context has partial authorization but full selection,
    // there should be valid policy provider with entity filtering
    @Test
    void testPartialAuthorizationAndFullSelection() {
        EntityContext entityContext = EntityContext.builder()
            .selectedIds(PARTIAL_ENTITY_IDS)
            .authorizedIds(PARTIAL_ENTITY_IDS)
            .hasFullSelection(true)
            .hasFullAuthorization(false)
            .build();
        EntityPolicyProvider entityPolicyProvider = new EntityPolicyProvider(null, null);
        Optional<PolicyProvider> policyProvider = entityPolicyProvider.buildPolicyProviderByEntityContext(entityContext);
        assertThat(policyProvider).isPresent();
        assertPolicyProviderWithSingleEntity(policyProvider.get());
    }

    // validate multiple entity ids for all tables
    // this also tests when entity context has partial authorization and partial selection
    @Test
    void testMultipleEntityIds() {
        EntityContext entityContext = EntityContext.builder()
            .selectedIds(ALL_ENTITY_IDS)
            .authorizedIds(ALL_ENTITY_IDS)
            .hasFullSelection(false)
            .hasFullAuthorization(false)
            .build();
        EntityPolicyProvider entityPolicyProvider = new EntityPolicyProvider(null, null);
        Optional<PolicyProvider> policyProvider = entityPolicyProvider.buildPolicyProviderByEntityContext(entityContext);
        assertThat(policyProvider).isPresent();
        assertPolicyProviderWithMultipleEntities(policyProvider.get());
    }

    void assertPolicyProviderWithSingleEntity(PolicyProvider policyProvider) {
        assertThat(policyProvider).isNotNull();
        var orderPolicy = policyProvider.provide(ACCOUNT_ORDER);
        assertThat(orderPolicy).isNotNull();
        assertThat(orderPolicy).hasSize(1);
        var condition = orderPolicy.get(0).condition().toString();
        assertThat(condition).contains("\"account_order\".\"entity_id\"", "in", "'ENT-1'");
    }

    void assertPolicyProviderWithMultipleEntities(PolicyProvider policyProvider) {
        assertThat(policyProvider).isNotNull();
        var orderPolicy = policyProvider.provide(ACCOUNT_ORDER);
        assertThat(orderPolicy).isNotNull();
        assertThat(orderPolicy).hasSize(1);
        var condition = orderPolicy.get(0).condition().toString().replace("\n", "");
        assertThat(condition).contains("\"account_order\".\"entity_id\"", "in", "'ENT-1'", "'ENT-2'");
        var subscriptionPolicy = policyProvider.provide(SUBSCRIPTION);
        assertThat(subscriptionPolicy).isNotNull();
        assertThat(subscriptionPolicy).hasSize(1);
        condition = subscriptionPolicy.get(0).condition().toString().replace("\n", "");
        assertThat(condition).contains("\"subscription\".\"entity_id\"", "in", "'ENT-1'", "'ENT-2'");
    }

    private static CurrentUserProvider buildCurrentUserProvider(EntityContext entityContext) {
        CurrentUserProvider currentUserProvider = mock(CurrentUserProvider.class);
        UserPrincipal userPrincipal = mock(UserPrincipal.class);
        BillyAuthPrincipal billyAuthPrincipal = mock(BillyAuthPrincipal.class);
        when(billyAuthPrincipal.getEntityContext()).thenReturn(entityContext);
        when(currentUserProvider.provideOptional()).thenReturn(Optional.of(userPrincipal));
        when(currentUserProvider.getBillyAuthPrincipal()).thenReturn(Optional.of(billyAuthPrincipal));
        when(userPrincipal.getEntityContext()).thenReturn(entityContext);
        return currentUserProvider;
    }

    private static BillyConfiguration buildBillyConfig(boolean rlsEnabled) {
        var billyConfiguration = mock(BillyConfiguration.class);
        var entityConfiguration = new EntityConfiguration();
        entityConfiguration.setRlsEnabled(rlsEnabled);
        when(billyConfiguration.getEntityConfiguration()).thenReturn(entityConfiguration);
        return billyConfiguration;
    }
}
