package com.subskribe.billy.currency;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.Currency;
import org.junit.jupiter.api.Test;

class SupportedCurrencyTest {

    @Test
    void makeSureEachCurrencyPrecision() {
        SupportedCurrency.CURRENCIES.stream()
            .map(Currency::getCurrencyCode)
            .forEach(c -> assertDoesNotThrow(() -> SupportedCurrency.getCurrencyPrecision(c)));
    }

    @Test
    void makeSureEachCurrencyHasMinimumStripeAmount() {
        SupportedCurrency.CURRENCIES.stream()
            .map(Currency::getCurrencyCode)
            .forEach(c -> assertThrows(IllegalStateException.class, () -> SupportedCurrency.validateAmountForStripe(0L, c)));
    }
}
