package com.subskribe.billy.email.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.model.SesConfiguration;
import com.subskribe.billy.email.db.EmailDAO;
import com.subskribe.billy.email.model.EmailContact;
import com.subskribe.billy.email.model.EmailContacts;
import com.subskribe.billy.email.model.EmailData;
import com.subskribe.billy.email.model.EmailSetting;
import com.subskribe.billy.email.model.EmailType;
import com.subskribe.billy.email.model.ImmutableEmailData;
import com.subskribe.billy.email.model.ImmutableEmailSetting;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.util.List;
import java.util.Optional;
import javax.mail.Message;
import javax.mail.internet.MimeMessage;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class EmailServiceTest {

    @Mock
    private EmailDAO emailDAO;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private TenantSettingService tenantSettingService;

    @Mock
    private EmailValidationService emailValidationService;

    @Mock
    private EmailSettingService emailSettingService;

    @Mock
    private EmailTransport emailTransport;

    private EmailService emailService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        emailService = new EmailService(
            emailDAO,
            billyConfiguration,
            tenantSettingService,
            emailValidationService,
            emailSettingService,
            emailTransport
        );
    }

    @Test
    void testEnvironmentIsPrependedWhenConfigurationEnabled() {
        SesConfiguration sesConfiguration = mock(SesConfiguration.class);
        String subject = RandomStringUtils.randomAlphanumeric(40);
        when(sesConfiguration.getPrependEnvironmentName()).thenReturn(true);
        when(billyConfiguration.getEnvName()).thenReturn("dev2");
        when(billyConfiguration.getSesConfiguration()).thenReturn(sesConfiguration);

        String result = emailService.prependSubjectWithEnvironment(subject);

        assertThat(result).isEqualTo("[DEV2] " + subject);
    }

    @Test
    void testEnvironmentIsNotPrependedWhenConfigurationDisabled() {
        SesConfiguration sesConfiguration = mock(SesConfiguration.class);
        String subject = RandomStringUtils.randomAlphanumeric(40);
        when(sesConfiguration.getPrependEnvironmentName()).thenReturn(false);
        when(billyConfiguration.getEnvName()).thenReturn("dev2");
        when(billyConfiguration.getSesConfiguration()).thenReturn(sesConfiguration);

        String result = emailService.prependSubjectWithEnvironment(subject);

        assertThat(result).isEqualTo(subject);
    }

    @Test
    @SuppressWarnings("all")
    void testNoDuplicateRecipients() {
        EmailSetting emailSetting = ImmutableEmailSetting.builder().ccEmail("<EMAIL>").ccEmailType(EmailType.INVOICE_POSTED).build();
        when(emailSettingService.getEmailSettingForEmailType(any(), any())).thenReturn(Optional.of(emailSetting));
        when(emailValidationService.isEmailValid(any())).thenReturn(true);

        String subject = RandomStringUtils.randomAlphanumeric(40);

        String emailHtmlBody = "<html><body><p>Hello World</p></body></html>";
        EmailData emailData = getEmailData(subject);
        Optional<String> globalBccEmail = Optional.of("<EMAIL>");
        MimeMessage mimeMessage;
        try {
            mimeMessage = emailService.getMimeMessage(emailHtmlBody, emailData, globalBccEmail);
            assertThat(mimeMessage.getRecipients(Message.RecipientType.TO).length).isEqualTo(1);
            assertThat(mimeMessage.getRecipients(Message.RecipientType.CC).length).isEqualTo(1);
            assertThat(mimeMessage.getRecipients(Message.RecipientType.BCC).length).isEqualTo(1);
        } catch (Exception e) {
            e.printStackTrace();
            fail();
        }
    }

    private EmailData getEmailData(String subject) {
        EmailContacts emailContacts = new EmailContacts();
        emailContacts.setToContacts(List.of(new EmailContact("<EMAIL>")));
        emailContacts.setCcContacts(List.of(new EmailContact("<EMAIL>")));
        emailContacts.setBccContacts(List.of(new EmailContact("<EMAIL>")));

        return ImmutableEmailData.builder()
            .emailTemplate("emailTemplate")
            .emailContacts(emailContacts)
            .fromEmail("<EMAIL>")
            .subject(subject)
            .emailType(EmailType.INVOICE_POSTED)
            .relatedObjectId("ORD-123456")
            .build();
    }
}
