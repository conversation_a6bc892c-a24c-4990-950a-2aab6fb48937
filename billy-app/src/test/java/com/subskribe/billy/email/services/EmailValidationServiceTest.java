package com.subskribe.billy.email.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.aws.model.SesConfiguration;
import com.subskribe.billy.user.service.UserService;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class EmailValidationServiceTest {

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private UserService userService;

    @Mock
    private SesConfiguration sesConfiguration;

    private EmailValidationService emailValidationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(billyConfiguration.getSesConfiguration()).thenReturn(sesConfiguration);
        when(userService.getAllNormalizedUserEmailAddresses()).thenReturn(Set.of("<EMAIL>"));
        when(sesConfiguration.getInternalOnly()).thenReturn(true);
        emailValidationService = new EmailValidationService(billyConfiguration, userService);
    }

    @Test
    void testInvalidEmail() {
        String invalidEmail = "invalid-email";
        verifyNoInteractions(userService);
        assertThat(emailValidationService.isEmailValid(invalidEmail)).isFalse();
    }

    @Test
    void testNonExistingExternalEmailWhenInternalOnly() {
        String externalEmail = "<EMAIL>";
        assertThat(emailValidationService.isEmailValid(externalEmail)).isFalse();
    }

    @Test
    void testNonExistingExternalEmailWhenInternalOnlyCaseInsensitive() {
        String externalEmail = "<EMAIL>";
        assertThat(emailValidationService.isEmailValid(externalEmail)).isFalse();
    }

    @Test
    void testSubskribeEmailWhenInternalOnly() {
        String internalEmail = "<EMAIL>";
        assertThat(emailValidationService.isEmailValid(internalEmail)).isTrue();
    }

    @Test
    void testValidExternalEmailWhenInternalOnly() {
        String validEmail = "<EMAIL>";
        assertThat(emailValidationService.isEmailValid(validEmail)).isTrue();
    }

    @Test
    void testNonExistingExternalEmailWhenNotInternalOnly() {
        when(sesConfiguration.getInternalOnly()).thenReturn(false);
        String externalEmail = "<EMAIL>";
        assertThat(emailValidationService.isEmailValid(externalEmail)).isTrue();
    }

    @Test
    void testSubskribeEmailWhenNotInternalOnly() {
        when(sesConfiguration.getInternalOnly()).thenReturn(false);
        String internalEmail = "<EMAIL>";
        assertThat(emailValidationService.isEmailValid(internalEmail)).isTrue();
    }

    @Test
    void testValidExternalEmailWhenNotInternalOnly() {
        when(sesConfiguration.getInternalOnly()).thenReturn(false);
        String validEmail = "<EMAIL>";
        assertThat(emailValidationService.isEmailValid(validEmail)).isTrue();
    }
}
