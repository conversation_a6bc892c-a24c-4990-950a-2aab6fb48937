package com.subskribe.billy.user.mapper;

import com.subskribe.billy.auth.model.UserAuthInfo;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.jooq.default_schema.tables.records.UserAuthInfoViewRecord;
import com.subskribe.billy.tenant.fixtures.TenantFixture;
import com.subskribe.billy.user.fixture.UserFixture;
import com.subskribe.billy.user.model.Role;
import java.util.Set;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

public class UserAuthMapperTest {

    @Test
    public void testUserAuthMapper() {
        UserAuthInfo userAuthInfo = mockUserAuthInfo();
        UserAuthMapper userAuthMapper = Mappers.getMapper(UserAuthMapper.class);
        UserAuthInfoViewRecord record = userAuthMapper.toRecord(userAuthInfo);
        UserAuthInfo mappedUserAuthInfo = userAuthMapper.fromRecord(record);
        Assertions.assertThat(mappedUserAuthInfo).isEqualTo(userAuthInfo);
    }

    @Test
    public void testEmptyEntityIds() {
        UserAuthInfo userAuthInfo = mockUserAuthInfo();
        userAuthInfo.setEntityIds(Set.of());
        UserAuthMapper userAuthMapper = Mappers.getMapper(UserAuthMapper.class);
        UserAuthInfoViewRecord record = userAuthMapper.toRecord(userAuthInfo);
        UserAuthInfo mappedUserAuthInfo = userAuthMapper.fromRecord(record);
        Assertions.assertThat(mappedUserAuthInfo).isEqualTo(userAuthInfo);
    }

    @Test
    public void testNullEntityIds() {
        UserAuthInfo userAuthInfo = mockUserAuthInfo();
        userAuthInfo.setEntityIds(Set.of());
        UserAuthMapper userAuthMapper = Mappers.getMapper(UserAuthMapper.class);
        UserAuthInfoViewRecord record = userAuthMapper.toRecord(userAuthInfo);
        record.setEntityIds(null);
        UserAuthInfo mappedUserAuthInfo = userAuthMapper.fromRecord(record);
        Assertions.assertThat(mappedUserAuthInfo).isEqualTo(userAuthInfo);
    }

    private UserAuthInfo mockUserAuthInfo() {
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setClientId(UUID.randomUUID().toString());
        userAuthInfo.setDomainName(UUID.randomUUID().toString());
        userAuthInfo.setUserPoolId(UUID.randomUUID().toString());
        userAuthInfo.setRole(Role.ADMIN.name());
        userAuthInfo.setEmail(UUID.randomUUID().toString());
        userAuthInfo.setEntityIds(EntityFixture.ALL_ENTITY_IDS);
        userAuthInfo.setTenantId(TenantFixture.TENANT_1_ID);
        userAuthInfo.setUserId(UserFixture.USER_ID);
        return userAuthInfo;
    }
}
