package com.subskribe.billy.user.fixture;

import static com.subskribe.billy.tenant.fixtures.TenantFixture.TENANT_1_ID;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.shared.enums.Status;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantIdProviderFixture;
import com.subskribe.billy.user.db.UserDAO;
import com.subskribe.billy.user.model.Role;
import com.subskribe.billy.user.model.User;
import java.util.Optional;
import java.util.Set;

public class UserFixture {

    public static final String USER_ID = "USER-TEST1";
    public static final Set<String> ENTITY_IDS = Set.of(EntityFixture.ENTITY_1_ID, EntityFixture.ENTITY_2_ID);
    public static final String USER_EMAIL = "<EMAIL>";
    public static final TenantIdProvider DEFAULT_TENANT_ID_PROVIDER = TenantIdProviderFixture.getDefault();

    public static void initDb(DSLContextProvider dslContextProvider) {
        UserDAO userDAO = new UserDAO(dslContextProvider, DEFAULT_TENANT_ID_PROVIDER);
        userDAO.addUser(getDefault());
    }

    public static User getDefaultFromDB(DSLContextProvider dslContextProvider) {
        UserDAO userDAO = new UserDAO(dslContextProvider, DEFAULT_TENANT_ID_PROVIDER);
        return userDAO.getUser(USER_ID, Optional.empty()).orElseThrow();
    }

    public static User getDefault() {
        User user = new User();
        user.setTenantId(TENANT_1_ID);
        user.setUserId(USER_ID);
        user.setDisplayName("test user");
        user.setEmail(USER_EMAIL);
        user.setNormalizedEmail(USER_EMAIL);
        user.setRole(Role.ADMIN);
        user.setState(Status.ACTIVE);
        user.setEntityIds(ENTITY_IDS);
        return user;
    }
}
