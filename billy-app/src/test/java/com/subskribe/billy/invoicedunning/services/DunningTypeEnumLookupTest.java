package com.subskribe.billy.invoicedunning.services;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.invoicedunning.model.DunningReminderType;
import java.util.Map;
import org.junit.jupiter.api.Test;

public class DunningTypeEnumLookupTest {

    @Test
    void testDunningEnumLookup() {
        Map<DunningReminderType, Boolean> dunningTypeEnabledMap = Map.of(
            DunningReminderType.MANUAL,
            true,
            DunningReminderType.WEEK_BEFORE_DUE_DATE,
            false
        );

        assertThat(dunningTypeEnabledMap.get(DunningReminderType.MANUAL)).isTrue();
        assertThat(dunningTypeEnabledMap.get(DunningReminderType.WEEK_BEFORE_DUE_DATE)).isFalse();
    }
}
