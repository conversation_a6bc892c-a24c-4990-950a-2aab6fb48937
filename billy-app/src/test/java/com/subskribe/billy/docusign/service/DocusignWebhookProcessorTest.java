package com.subskribe.billy.docusign.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.docusign.model.DocuSignEventData;
import com.subskribe.billy.docusign.model.DocuSignEventType;
import com.subskribe.billy.docusign.model.DocuSignWebhookPayload;
import com.subskribe.billy.esign.db.EsignDAO;
import com.subskribe.billy.esign.model.ElectronicSignature;
import com.subskribe.billy.esign.model.ElectronicSignatureProvider;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.webhook.ImmutableIncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhook;
import com.subskribe.billy.shared.webhook.IncomingWebhookTenantLookupResult;
import com.subskribe.billy.shared.webhook.IncomingWebhookType;
import java.util.Optional;
import java.util.UUID;
import javax.ws.rs.core.MultivaluedHashMap;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DocusignWebhookProcessorTest {

    private static final String TEST_ENVELOPE_ID = "12345-67890-abcde-fghij";
    private static final String TEST_TENANT_ID = "test-tenant-id";
    private static final UUID TEST_SIGNATURE_ID = UUID.randomUUID();

    @Mock
    private EsignDAO esignDAO;

    @Mock
    private DocuSignService docuSignService;

    private DocusignWebhookProcessor processor;

    private ElectronicSignature testSignature;
    private IncomingWebhook testWebhook;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        processor = new DocusignWebhookProcessor(esignDAO, docuSignService);

        testSignature = new ElectronicSignature();
        testSignature.setId(TEST_SIGNATURE_ID);
        testSignature.setTenantId(TEST_TENANT_ID);
        testSignature.setExternalDocumentId(TEST_ENVELOPE_ID);
        testSignature.setDocumentProvider(ElectronicSignatureProvider.DOCUSIGN);

        DocuSignWebhookPayload testPayload = new DocuSignWebhookPayload();
        DocuSignEventData data = new DocuSignEventData();
        data.setEnvelopeId(TEST_ENVELOPE_ID);
        testPayload.setData(data);
        testPayload.setEvent(DocuSignEventType.RECIPIENT_COMPLETED);

        String testPayloadJson = JacksonProvider.defaultMapper().writeValueAsString(testPayload);

        testWebhook = ImmutableIncomingWebhook.builder()
            .webhookType(DocusignWebhookProcessor.WEBHOOK_TYPE)
            .payload(testPayloadJson)
            .receivedOn(System.currentTimeMillis())
            .headers(new MultivaluedHashMap<>())
            .build();
    }

    @Test
    void testGetWebhookType() {
        IncomingWebhookType webhookType = processor.getWebhookType();
        assertThat(webhookType).isEqualTo(DocusignWebhookProcessor.WEBHOOK_TYPE);
        assertThat(webhookType.name()).isEqualTo("docusign");
    }

    @Test
    void testFindTenantIdWhenSignatureExists() {
        when(esignDAO.getSignatureByDocumentId(ElectronicSignatureProvider.DOCUSIGN, TEST_ENVELOPE_ID)).thenReturn(Optional.of(testSignature));

        IncomingWebhookTenantLookupResult result = processor.findTenantId(testWebhook);

        IncomingWebhookTenantLookupResult expectedResult = new IncomingWebhookTenantLookupResult(Optional.of(TEST_TENANT_ID), true);
        assertThat(result).isEqualTo(expectedResult);

        verify(esignDAO).getSignatureByDocumentId(ElectronicSignatureProvider.DOCUSIGN, TEST_ENVELOPE_ID);
    }

    @Test
    void testFindTenantIdWhenSignatureDoesNotExist() {
        when(esignDAO.getSignatureByDocumentId(ElectronicSignatureProvider.DOCUSIGN, TEST_ENVELOPE_ID)).thenReturn(Optional.empty());

        IncomingWebhookTenantLookupResult result = processor.findTenantId(testWebhook);

        IncomingWebhookTenantLookupResult expectedResult = new IncomingWebhookTenantLookupResult(Optional.empty(), true);
        assertThat(result).isEqualTo(expectedResult);

        verify(esignDAO).getSignatureByDocumentId(ElectronicSignatureProvider.DOCUSIGN, TEST_ENVELOPE_ID);
    }

    @Test
    void testProcessWhenSignatureExists() {
        when(esignDAO.getSignatureByDocumentId(ElectronicSignatureProvider.DOCUSIGN, TEST_ENVELOPE_ID)).thenReturn(Optional.of(testSignature));

        processor.process(testWebhook);

        verify(esignDAO).getSignatureByDocumentId(ElectronicSignatureProvider.DOCUSIGN, TEST_ENVELOPE_ID);
        verify(docuSignService).processWebhookEvent(any(DocuSignWebhookPayload.class), eq(testSignature));
    }

    @Test
    void testProcessWhenSignatureDoesNotExist() {
        when(esignDAO.getSignatureByDocumentId(ElectronicSignatureProvider.DOCUSIGN, TEST_ENVELOPE_ID)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> processor.process(testWebhook))
            .isInstanceOf(ObjectNotFoundException.class)
            .hasFieldOrPropertyWithValue("objectName", BillyObjectType.ESIGNATURE.getName())
            .hasFieldOrPropertyWithValue("objectId", TEST_ENVELOPE_ID);

        verify(esignDAO).getSignatureByDocumentId(ElectronicSignatureProvider.DOCUSIGN, TEST_ENVELOPE_ID);
    }

    @Test
    void testSerializePayloadWithInvalidJson() {
        String invalidJson = "{invalid-json}";
        IncomingWebhook invalidWebhook = ImmutableIncomingWebhook.builder()
            .webhookType(DocusignWebhookProcessor.WEBHOOK_TYPE)
            .payload(invalidJson)
            .receivedOn(System.currentTimeMillis())
            .headers(new MultivaluedHashMap<>())
            .build();

        IncomingWebhookTenantLookupResult result = processor.findTenantId(invalidWebhook);

        IncomingWebhookTenantLookupResult expectedResult = new IncomingWebhookTenantLookupResult(Optional.empty(), true);
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testFullObjectEqualityInProcessing() {
        when(esignDAO.getSignatureByDocumentId(ElectronicSignatureProvider.DOCUSIGN, TEST_ENVELOPE_ID)).thenReturn(Optional.of(testSignature));

        DocuSignService mockDocuSignService = mock(DocuSignService.class);
        DocusignWebhookProcessor processorWithMockService = new DocusignWebhookProcessor(esignDAO, mockDocuSignService);

        processorWithMockService.process(testWebhook);

        verify(mockDocuSignService).processWebhookEvent(any(DocuSignWebhookPayload.class), eq(testSignature));
    }
}
