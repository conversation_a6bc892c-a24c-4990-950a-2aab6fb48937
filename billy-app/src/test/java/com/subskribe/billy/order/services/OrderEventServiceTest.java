package com.subskribe.billy.order.services;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.fixtures.OrderData;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.resources.json.order.OrderMapper;
import com.subskribe.billy.resources.json.subscription.SubscriptionJsonMapper;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.services.SubscriptionImpl;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.jooq.DSLContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class OrderEventServiceTest {

    private OrderEventService orderEventService;

    private EventPublishingService eventPublishingService;
    private UncheckedObjectMapper objectMapper;
    private SubscriptionJsonMapper subscriptionJsonMapper;
    private OrderMapper orderMapper;
    private DSLContext transactionContext;

    @BeforeEach
    void setUp() {
        eventPublishingService = mock(EventPublishingService.class);
        objectMapper = mock(UncheckedObjectMapper.class);
        subscriptionJsonMapper = spy(Mappers.getMapper(SubscriptionJsonMapper.class));
        orderMapper = spy(Mappers.getMapper(OrderMapper.class));
        transactionContext = mock(DSLContext.class);

        orderEventService = new OrderEventService(eventPublishingService, objectMapper, subscriptionJsonMapper, orderMapper);
    }

    @Test
    void orderEventSentForSubmitted() {
        String mockJson = "{'status':'SUBMITTED'}";
        Order order = createOrder(OrderStatus.SUBMITTED);
        when(objectMapper.writeValueAsBytes(orderMapper.orderToJson(order))).thenReturn(mockJson.getBytes());

        orderEventService.publishEventsForOrderUpdate(order, transactionContext);

        verify(eventPublishingService, times(1)).publishEventInTransaction(
            transactionContext,
            EventType.ORDER_SUBMITTED,
            order.getTenantId(),
            order.getEntityId(),
            order.getAccountId(),
            mockJson.getBytes()
        );
    }

    @Test
    void orderEventSentForExecuted() {
        String mockJson = "{'status':'EXECUTED'}";
        Order order = createOrder(OrderStatus.EXECUTED);
        when(objectMapper.writeValueAsBytes(orderMapper.orderToJson(order))).thenReturn(mockJson.getBytes());

        orderEventService.publishEventsForOrderUpdate(order, transactionContext);

        verify(eventPublishingService, times(1)).publishEventInTransaction(
            transactionContext,
            EventType.ORDER_EXECUTED,
            order.getTenantId(),
            order.getEntityId(),
            order.getAccountId(),
            mockJson.getBytes()
        );
    }

    @Test
    void subscriptionEventSentForCreated() {
        String mockJson = "{'status':'CREATED'}";
        Subscription subscription = createSubscription();
        when(objectMapper.writeValueAsBytes(subscriptionJsonMapper.subscriptionToJson(subscription))).thenReturn(mockJson.getBytes());

        orderEventService.publishEventsForSubscriptionCreated(subscription, transactionContext);

        verify(eventPublishingService, times(1)).publishEventInTransaction(
            transactionContext,
            EventType.SUBSCRIPTION_CREATED,
            subscription.getTenantId(),
            subscription.getEntityId(),
            subscription.getAccountId(),
            mockJson.getBytes()
        );
    }

    private Order createOrder(OrderStatus status) {
        Order order = OrderData.createOrder(
            "orderId",
            "tenantId",
            "entityId",
            "subscriptionId",
            "accountId",
            "contactId",
            List.of(createLineItem()),
            LocalDateTime.ofEpochSecond(**********, 0, ZoneOffset.UTC),
            LocalDateTime.ofEpochSecond(**********, 0, ZoneOffset.UTC)
        );
        order.setStatus(status);
        return order;
    }

    private static OrderLineItem createLineItem() {
        return OrderData.createOrderLineItem(
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            RandomStringUtils.randomAlphanumeric(10),
            RandomStringUtils.randomAlphanumeric(10),
            RandomStringUtils.randomAlphanumeric(10),
            RandomStringUtils.randomAlphanumeric(10),
            10L,
            LocalDateTime.ofEpochSecond(**********, 0, ZoneOffset.UTC),
            LocalDateTime.ofEpochSecond(**********, 0, ZoneOffset.UTC)
        );
    }

    private Subscription createSubscription() {
        var subscriptionEntity = new SubscriptionEntity();
        subscriptionEntity.setSubscriptionId(UUID.randomUUID().toString());
        subscriptionEntity.setTenantId(UUID.randomUUID().toString());
        subscriptionEntity.setAccountId(RandomStringUtils.randomAlphanumeric(10));
        subscriptionEntity.setStartDate(Instant.ofEpochSecond(**********));
        subscriptionEntity.setEndDate(Instant.ofEpochSecond(**********));
        subscriptionEntity.setBillingAnchorDate(Instant.ofEpochSecond(**********));
        subscriptionEntity.setCreationTime(Instant.ofEpochSecond(**********));
        subscriptionEntity.setBillingTerm(BillingTerm.UP_FRONT);
        subscriptionEntity.setBillingCycle(new Recurrence(Cycle.MONTH, 1));
        return new SubscriptionImpl(subscriptionEntity, List.of());
    }
}
