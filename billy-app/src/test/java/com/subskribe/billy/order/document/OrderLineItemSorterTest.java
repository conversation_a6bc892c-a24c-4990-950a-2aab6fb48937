package com.subskribe.billy.order.document;

import static com.subskribe.billy.TestConstants.CURRENCY;
import static com.subskribe.billy.TestConstants.TIME_ZONE;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldSource;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.productcatalog.model.ProductCategory;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.shared.document.TemplateCharge;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

class OrderLineItemSorterTest {

    private static final String ORDER_BY_CUSTOM_FIELD_NAME = "rank";

    @Test
    void sortByChargeCustomFieldAllIntegerRankValues() {
        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithCustomField("3", getCustomField("3")),
            getLineItemWithCustomField("1", getCustomField("1")),
            getLineItemWithCustomField("2", getCustomField("2")),
            getLineItemWithCustomField("4", getCustomField("10"))
        );

        List<OrderTemplateLineItem> sortedLineItems = OrderLineItemSorter.sortByChargeCustomField(ORDER_BY_CUSTOM_FIELD_NAME, lineItems);

        assertEquals("1", sortedLineItems.get(0).getOrderLineItemDetail().getId());
        assertEquals("2", sortedLineItems.get(1).getOrderLineItemDetail().getId());
        assertEquals("3", sortedLineItems.get(2).getOrderLineItemDetail().getId());
        assertEquals("4", sortedLineItems.get(3).getOrderLineItemDetail().getId());
    }

    @Test
    void sortByChargeCustomFieldAllStringRankValues() {
        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithCustomField("3", getCustomField("a")),
            getLineItemWithCustomField("1", getCustomField("1")),
            getLineItemWithCustomField("2", getCustomField("2")),
            getLineItemWithCustomField("4", getCustomField("b"))
        );

        List<OrderTemplateLineItem> sortedLineItems = OrderLineItemSorter.sortByChargeCustomField(ORDER_BY_CUSTOM_FIELD_NAME, lineItems);

        assertEquals("1", sortedLineItems.get(0).getOrderLineItemDetail().getId());
        assertEquals("2", sortedLineItems.get(1).getOrderLineItemDetail().getId());
        assertEquals("3", sortedLineItems.get(2).getOrderLineItemDetail().getId());
        assertEquals("4", sortedLineItems.get(3).getOrderLineItemDetail().getId());
    }

    @Test
    void sortByChargeCustomFieldMixedRankValues() {
        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithCustomField("3", getCustomField("3")),
            getLineItemWithCustomField("1", getCustomField("1")),
            getLineItemWithCustomField("2", getCustomField("2"))
        );

        List<OrderTemplateLineItem> sortedLineItems = OrderLineItemSorter.sortByChargeCustomField(ORDER_BY_CUSTOM_FIELD_NAME, lineItems);

        assertEquals("1", sortedLineItems.get(0).getOrderLineItemDetail().getId());
        assertEquals("2", sortedLineItems.get(1).getOrderLineItemDetail().getId());
        assertEquals("3", sortedLineItems.get(2).getOrderLineItemDetail().getId());
    }

    @Test
    void sortByOrderItemCustomFieldWithoutOrderByFieldName() {
        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithCustomField("3", getCustomField("3")),
            getLineItemWithCustomField("1", getCustomField("1")),
            getLineItemWithCustomField("2", getCustomField("2"))
        );

        List<OrderTemplateLineItem> sortedLineItems = OrderLineItemSorter.sortByChargeCustomField(null, lineItems);

        assertEquals("3", sortedLineItems.get(0).getOrderLineItemDetail().getId());
        assertEquals("1", sortedLineItems.get(1).getOrderLineItemDetail().getId());
        assertEquals("2", sortedLineItems.get(2).getOrderLineItemDetail().getId());
    }

    @Test
    void sortByChargeCustomFieldWithAllBlankCustomFieldValues() {
        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithCustomField("3", getCustomField(null)),
            getLineItemWithCustomField("1", getCustomField(null)),
            getLineItemWithCustomField("2", getCustomField(null))
        );

        List<OrderTemplateLineItem> sortedLineItems = OrderLineItemSorter.sortByChargeCustomField(ORDER_BY_CUSTOM_FIELD_NAME, lineItems);

        assertEquals("3", sortedLineItems.get(0).getOrderLineItemDetail().getId());
        assertEquals("1", sortedLineItems.get(1).getOrderLineItemDetail().getId());
        assertEquals("2", sortedLineItems.get(2).getOrderLineItemDetail().getId());
    }

    @Test
    void emptyOrderBySortedAtBottom() {
        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithCustomField("3", getCustomField(null), 1),
            getLineItemWithCustomField("1", getCustomField(null), 2),
            getLineItemWithCustomField("2", getCustomField("2")),
            getLineItemWithCustomField("4", getCustomField("4"))
        );

        List<OrderTemplateLineItem> sortedLineItems = OrderLineItemSorter.sortByChargeCustomField(ORDER_BY_CUSTOM_FIELD_NAME, lineItems);

        assertEquals("2", sortedLineItems.get(0).getOrderLineItemDetail().getId());
        assertEquals("4", sortedLineItems.get(1).getOrderLineItemDetail().getId());
        assertEquals("3", sortedLineItems.get(2).getOrderLineItemDetail().getId());
        assertEquals("1", sortedLineItems.get(3).getOrderLineItemDetail().getId());
    }

    @Test
    public void sortByGivenPlanIds() {
        String planId1 = "planId1";
        String planId2 = "planId2";
        String planId3 = "planId3";
        String planId4 = "planId4";
        String planId5 = "planId5";

        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithPlanId(planId5, 1),
            getLineItemWithPlanId(planId4, 2),
            getLineItemWithPlanId(planId3, 3),
            getLineItemWithPlanId(planId2, 4),
            getLineItemWithPlanId(planId1, 5)
        );

        List<OrderTemplateLineItem> sortedLineItems = OrderLineItemSorter.sortBy(
            OrderTemplateItemSortBy.PLAN_ID,
            List.of(planId1, planId2, planId3),
            lineItems
        );

        // expected sort order: 1, 2, 3, 5, 4
        assertEquals(planId1, sortedLineItems.get(0).getOrderLineItemDetail().getPlan().getId());
        assertEquals(planId2, sortedLineItems.get(1).getOrderLineItemDetail().getPlan().getId());
        assertEquals(planId3, sortedLineItems.get(2).getOrderLineItemDetail().getPlan().getId());
        assertEquals(planId5, sortedLineItems.get(3).getOrderLineItemDetail().getPlan().getId());
        assertEquals(planId4, sortedLineItems.get(4).getOrderLineItemDetail().getPlan().getId());

        sortedLineItems = OrderLineItemSorter.sortBy(OrderTemplateItemSortBy.PLAN_ID, List.of(planId2, planId1, planId3), lineItems);

        // expected sort order: 2, 1, 3, 5, 4
        assertEquals(planId2, sortedLineItems.get(0).getOrderLineItemDetail().getPlan().getId());
        assertEquals(planId1, sortedLineItems.get(1).getOrderLineItemDetail().getPlan().getId());
        assertEquals(planId3, sortedLineItems.get(2).getOrderLineItemDetail().getPlan().getId());
        assertEquals(planId5, sortedLineItems.get(3).getOrderLineItemDetail().getPlan().getId());
        assertEquals(planId4, sortedLineItems.get(4).getOrderLineItemDetail().getPlan().getId());
    }

    @Test
    public void sortByGivenProductCategoryIds() {
        String category1 = "category1";
        String category2 = "category2";
        String category3 = "category3";

        List<String> orderLineIds = List.of("line0", "line1", "line2", "line3", "line4", "line5", "line6");

        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithProductCategoryId(orderLineIds.get(0), category1, 7),
            getLineItemWithProductCategoryId(orderLineIds.get(1), null, 6),
            getLineItemWithProductCategoryId(orderLineIds.get(2), category1, 5),
            getLineItemWithProductCategoryId(orderLineIds.get(3), StringUtils.EMPTY, 4),
            getLineItemWithProductCategoryId(orderLineIds.get(4), category2, 3),
            getLineItemWithProductCategoryId(orderLineIds.get(5), category3, 2),
            getLineItemWithProductCategoryId(orderLineIds.get(6), category3, 1)
        );

        List<OrderTemplateLineItem> sortedLineItems = OrderLineItemSorter.sortBy(
            OrderTemplateItemSortBy.PRODUCT_CATEGORY_ID,
            List.of(category1, category2),
            lineItems
        );

        // expected sort order: line2, line0, line4, line6, line5, line3, line1
        assertEquals(orderLineIds.get(2), sortedLineItems.get(0).getOrderLineItemDetail().getId());
        assertEquals(orderLineIds.get(0), sortedLineItems.get(1).getOrderLineItemDetail().getId());
        assertEquals(orderLineIds.get(4), sortedLineItems.get(2).getOrderLineItemDetail().getId());
        assertEquals(orderLineIds.get(6), sortedLineItems.get(3).getOrderLineItemDetail().getId());
        assertEquals(orderLineIds.get(5), sortedLineItems.get(4).getOrderLineItemDetail().getId());
        assertEquals(orderLineIds.get(3), sortedLineItems.get(5).getOrderLineItemDetail().getId());
        assertEquals(orderLineIds.get(1), sortedLineItems.get(6).getOrderLineItemDetail().getId());
    }

    private CustomField getCustomField(String value) {
        CustomFieldValue customFieldValue = new CustomFieldValue(
            CustomFieldType.STRING,
            ORDER_BY_CUSTOM_FIELD_NAME,
            null,
            value,
            List.of(),
            List.of(),
            false,
            CustomFieldSource.USER,
            null
        );
        return new CustomField(Map.of(UUID.randomUUID().toString(), customFieldValue));
    }

    private OrderTemplateLineItem getLineItemWithProductCategoryId(String orderLineItemId, String productCategoryId, Integer rank) {
        var orderLineItemDetail = new OrderLineItemDetail();

        orderLineItemDetail.setRank(rank);
        orderLineItemDetail.setId(orderLineItemId);
        ProductCategory productCategory = new ProductCategory();
        productCategory.setProductCategoryId(productCategoryId);
        ProductJson productJson = new ProductJson();
        productJson.setProductCategory(productCategory);
        productJson.setProductCategoryId(productCategoryId);

        return new OrderTemplateLineItem(
            orderLineItemDetail,
            productJson,
            null,
            getTemplateCharge(null),
            BigDecimal.ZERO,
            new CustomField(Map.of()),
            new DocumentRenderFormatter(TIME_ZONE),
            CURRENCY,
            List.of(),
            TimeZone.getTimeZone(ZoneOffset.UTC)
        );
    }

    private OrderTemplateLineItem getLineItemWithPlanId(String planId, Integer rank) {
        var orderLineItemDetail = new OrderLineItemDetail();
        var plan = new PlanJson();
        plan.setId(planId);
        orderLineItemDetail.setRank(rank);
        orderLineItemDetail.setPlan(plan);

        return new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getTemplateCharge(null),
            BigDecimal.ZERO,
            new CustomField(Map.of()),
            new DocumentRenderFormatter(TIME_ZONE),
            CURRENCY,
            List.of(),
            TimeZone.getTimeZone(ZoneOffset.UTC)
        );
    }

    private OrderTemplateLineItem getLineItemWithCustomField(String id, CustomField customField) {
        return getLineItemWithCustomField(id, customField, 0);
    }

    private OrderTemplateLineItem getLineItemWithCustomField(String id, CustomField customField, Integer rank) {
        var orderLineItemDetail = new OrderLineItemDetail();
        orderLineItemDetail.setId(id);
        orderLineItemDetail.setRank(rank);
        return new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getTemplateCharge(customField),
            BigDecimal.ZERO,
            new CustomField(Map.of()),
            new DocumentRenderFormatter(TIME_ZONE),
            CURRENCY,
            List.of(),
            TimeZone.getTimeZone(ZoneOffset.UTC)
        );
    }

    private TemplateCharge getTemplateCharge(CustomField customField) {
        return new TemplateCharge(customField, new DocumentRenderFormatter(TIME_ZONE), CURRENCY, Cycle.YEAR, new ChargeJson(), null);
    }
}
