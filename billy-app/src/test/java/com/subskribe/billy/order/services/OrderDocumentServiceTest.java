package com.subskribe.billy.order.services;

import com.subskribe.billy.esign.model.ElectronicSignatureProvider;
import java.util.regex.Pattern;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class OrderDocumentServiceTest {

    private static final String PANDADOC_TWO =
        """

        <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word"
          xmlns="http://www.w3.org/TR/REC-html40">

        <head>
          <title>Quote</title>
          <script>
            window.addEventListener("error", function (e) {
              const ele = e.target;
              const url = ele.tagName === "LINK" ? ele.href : ele.src;
              console.error(url + " failed loading.");
              window.stop();
            }, true);
          </script>
          <script src="paged.polyfill.js"></script>
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
          <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400&display=swap" rel="stylesheet" />
          <style>
            body {
              margin: 0;
              font-family: 'Roboto Slab', serif;
            }

            body,
            table {
              font-weight: 300;
              font-size: 8pt;
            }

            .header {
              margin-top: 1em;
            }

            .brand-name {
              color: #5350ed;
            }

            .bold {
              font-weight: bold;
            }

            table {
              flex: 1;
              width: 100%;
            }

            caption,
            th,
            td {
              text-align: left;
            }

            .align-left {
              text-align: left;
            }

            .align-right {
              text-align: right;
            }

            .align-center {
              text-align: center;
            }

            .padding-left-2 {
              padding-left: 2em;
            }

            tr.align-center td,
            tr.align-center th {
              text-align: center;
            }

            .line-items-table {
              text-align: left;
              border-collapse: collapse;
            }

            .contact-table {
              text-align: left;
              vertical-align: top;
              border-collapse: collapse;
            }

            .line-items-table-caption-date-interval {
              font-weight: bold;
              margin: 0.25em 0 0.5em;
              text-align: left;
            }

            .line-items-table-head {
              padding-bottom: 4pt;
            }

            .line-items-table-body {
              line-height: 1.25rem;
            }

            .avoid-breaks {
              page-break-inside: avoid;
              page-break-after: avoid;
            }

            .contact-table-head {
              padding-bottom: 1px solid;
            }

            .contact-item {
              padding: 0px;
              margin: 0px;
              vertical-align: top;
            }

            .table-number {
              padding-left: 1em;
              text-align: right;
            }

            .total {
              text-align: right;
            }

            .amount-due-label {
              text-align: right;
              padding-right: 1rem;
              font-weight: bold;
            }

            .subTotal-label {
              text-align: right;
              padding-right: 1rem;
            }

            .total-label {
              text-align: right;
              padding-right: 1rem;
              font-weight: bold;
            }

            .taxTotal-label {
              text-align: right;
              padding-right: 1rem;
            }

            .section-header {
              font-weight: bold;
              text-transform: uppercase;
              border-bottom: solid 2px #5350ed;
              padding-bottom: 4pt;
            }

            label {
              padding-right: 1em;
            }

            .draft-watermark {
              margin-top: 0;
              margin-bottom: 0;
              color: #d6dde4;
            }

            .indented {
              text-indent: 3em;
            }

            .logo {
              max-height: 5em;
              width: auto;
            }

            .signature-section {
              break-inside: avoid;
              width: 100%;
              font-size: 8pt;
            }

            .provider-gap-block {
              width: 100%;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: start;

              border-bottom: 1px solid #000;
              height: 60px;
              font-size: 16px;
              padding-top: 10px;
            }

            .provider-gap-block--signature {
              height: 60px;
            }

            .signature-anchor-text {
              color: white;
              text-align: center;
              display: flex;
            }

            /* WYSIWYG (quill) editor specific styles */

            .ql-align-center {
              text-align: center;
            }

            .ql-align-right {
              text-align: right;
            }

            .ql-align-justify {
              text-align: justify;
            }

            .ql-indent-1 {
              padding-left: 3em;
            }

            .ql-indent-2 {
              padding-left: 6em;
            }

            .ql-indent-3 {
              padding-left: 9em;
            }

            .ql-indent-4 {
              padding-left: 12em;
            }

            .ql-indent-5 {
              padding-left: 15em;
            }

            .ql-indent-6 {
              padding-left: 18em;
            }

            .ql-indent-7 {
              padding-left: 21em;
            }

            .ql-indent-8 {
              padding-left: 24em;
            }

            .alpha-table {
              border-spacing: 0;
              border-collapse: collapse;
              margin-right: auto;
            }

            .alpha-tr {
              height: 12pt;
            }

            .alpha-td {
              border-right-style: solid;
              padding: 5pt 5pt 5pt 5pt;
              border-bottom-color: #000000;
              border-top-width: 1pt;
              border-right-width: 1pt;
              border-left-color: #000000;
              vertical-align: center;
              text-align: center;
              border-right-color: #000000;
              border-left-width: 1pt;
              border-top-style: solid;
              border-left-style: solid;
              border-bottom-width: 1pt;
              border-top-color: #000000;
              border-bottom-style: solid;
            }

            .alpha-table-text {
              color: #000000;
              text-decoration: none;
              vertical-align: baseline;
              font-style: normal;

              padding-top: 0pt;
              padding-bottom: 0pt;
              line-height: 1;
              text-align: center;
            }

            .alpha-table-body-text {
              font-weight: 400;
            }

            .alpha-table-head-text {
              font-weight: 700;
            }

            .alpha-table-head {
              background-color: #daebfd;
            }

            /* PDF page - styles and settings */
            @media print {
              @page {
                margin: 6mm 10mm 10mm 10mm;
                width: 210mm;
                height: 297mm;

                @top-right {
                  content: ' ';
                  background-color: #5350ed;
                }

                @top-left-corner {
                  content: ' ';
                  background-color: #5350ed;
                }

                @top-right-corner {
                  content: ' ';
                  background-color: #5350ed;
                }

                @bottom-right {
                  content: 'Page ' counter(page) ' of ' counter(pages);
                  font-size: 12px;
                }
              }
            }
          </style>
        </head>

        <body>
          <table width="100%">
            <tr>
              <td>
                <h1 style="margin-top: 0">eSign test</h1>
                <div></div>
                <div><EMAIL></div>
              </td>
              <td style="text-align: right">
                <div><label>Order ID</label> ORD-X3NKKDC</div>
                <div><label>Auto Renew</label> &#10003;</div>
              </td>
            </tr>
          </table>

          <div>
            <h3 class="section-header">Customer Information</h3>
            <div>
              <table class="contact-table">
                <thead class="contact-table-head">
                  <tr>
                    <th class="">
                        Bill to\s
                    </th>
                    <th class="">
                        Ship to\s
                    </th>
                  </tr>
                </thead>
                <tbody class="">
                  <tr>
                    <td class="contact-item">
                       eSignV3Test<br />
                      Sumit K<br />
                      635, S Ellis St<br />
         Chandler, AZ
                      65535<br />
                      United States<br />
                      <EMAIL><br />
                      <br />
                    </td>
                    <td class="contact-item">
                      eSignV3Test<br />
                      Sumit K<br />
                      635, S Ellis St<br />
         Chandler, AZ
                      65535<br />
                      United States<br />
                      <EMAIL><br />
                      <br />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <br />

          <div>
            <h3 class="section-header">Contract Terms</h3>

            <table class="line-items-table">
              <thead class="line-items-table-head">
                <tr>
                  <th class="">Start Date</th>
                  <th class="">End Date</th>
                  <th class="">Billing Cycle</th>
                  <th class="">Payment Term</th>
                  <th>Preferred Payment Type</th>
                </tr>
              </thead>
              <tbody class="">
                <tr>
                  <td class="">Feb 20, 2024</td>
                  <td class="">Feb 19, 2025</td>
                  <td class="">Yearly</td>
                  <td class="">Net 30</td>
                  <td class="">ACH, Credit Card, Check, Wire Transfer</td>
                </tr>
              </tbody>
            </table>
          </div>

          <br />

          <div>
            <h3 class="section-header">Contract Items</h3>
            <table class="alpha-table">
              <thead>
                <tr class="alpha-tr">
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-head-text">Plan Name</span></p>
                  </td>
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-head-text">Charge Name</span></p>
                  </td>
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-head-text">Start Date</span></p>
                  </td>
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-head-text">End Date</span></p>
                  </td>
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-head-text">Quantity</span></p>
                  </td>
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p>
                      <span class="alpha-table-text alpha-table-head-text"> Total </span>
                    </p>
                  </td>
                </tr>
              </thead>
              <tbody>
                <tr class="alpha-tr">
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-body-text">EMS Recurring</span></p>
                  </td>
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-body-text">EMS Recurring</span></p>
                  </td>
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-body-text">Feb 20, 2024</span></p>
                  </td>
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-body-text">Feb 19, 2025</span></p>
                  </td>
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-body-text">0 </span></p>
                  </td>
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p>
                      <span class="alpha-table-text table-number"> $0.00 </span>
                    </p>
                  </td>
                </tr>
                <tr>
                  <td>&nbsp;</td>
                </tr>
              </tbody>
            </table>
            <div class="total">
              <label class="total-label">Grand Total</label>
              <span class="table-number">$0.00</span>
            </div>

          </div>

          <div>
          </div>

         <div class="terms-section-container"></div>
          <div class="signature-section">
            <table width="100%">
              <tr>
                <th colspan="2" class="section-header">eSignV3Test</th>
                <th width="10%" class="section-header"></th>
                <th colspan="2" class="section-header">eSign test</th>
              </tr>

              <tr>
                <td>&nbsp</td>
              </tr>

              <tr>
                <th>
                  <div>Signature:</div>
                </th>
                <th>
                  <div class="provider-gap-block provider-gap-block--signature">
                    <div class="signature-anchor-text">
                      <div>_&#123;s:s1________&#125;_</div>
                    </div>
                  </div>
                </th>
                <th width="10%"></th>
                <th>
                  <div>Signature:</div>
                </th>
                <th>
                  <div class="provider-gap-block provider-gap-block--signature">
                    <div class="signature-anchor-text">_&#123;s:s2________&#125;_</div>
                  </div>
                </th>
              </tr>

              <tr>
                <th>
                  <div>Name:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_&#123;t:s1____________&#125;_</div>
                  </div>
                </th>
                <th width="10%"></th>
                <th>
                  <div>Name:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_&#123;t:s2____________&#125;_</div>
                  </div>
                </th>
              </tr>

              <tr>
                <th>
                  <div>Date:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_&#123;d:s1______&#125;_</div>
                  </div>
                </th>
                <th width="10%"></th>
                <th>
                  <div>Date:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_&#123;d:s2______&#125;_</div>
                  </div>
                </th>
              </tr>

              <tr>
                <th>
                  <div>Title:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_&#123;t:s1______&#125;_</div>
                  </div>
                </th>
                <th width="10%"></th>
                <th>
                  <div>Title:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_&#123;t:s2______&#125;_</div>
                  </div>
                </th>
              </tr>
            </table>
          </div>

        </body>

        </html>
        """;

    private static final String PANDADOC_ONE_MULTI_BLOCK_WITH_HTML_ENTITIES =
        """

        <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word"
          xmlns="http://www.w3.org/TR/REC-html40">

        <head>
          <title>Quote</title>
          <script>
            window.addEventListener("error", function (e) {
              const ele = e.target;
              const url = ele.tagName === "LINK" ? ele.href : ele.src;
              console.error(url + " failed loading.");
              window.stop();
            }, true);
          </script>
          <script src="paged.polyfill.js"></script>
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
          <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400&display=swap" rel="stylesheet" />
          <style>
            body {
              margin: 0;
              font-family: 'Roboto Slab', serif;
            }

            body,
            table {
              font-weight: 300;
              font-size: 8pt;
            }

            .header {
              margin-top: 1em;
            }

            .brand-name {
              color: #5350ed;
            }

            .bold {
              font-weight: bold;
            }

            table {
              flex: 1;
              width: 100%;
            }

            caption,
            th,
            td {
              text-align: left;
            }

            .align-left {
              text-align: left;
            }

            .align-right {
              text-align: right;
            }

            .align-center {
              text-align: center;
            }

            .padding-left-2 {
              padding-left: 2em;
            }

            tr.align-center td,
            tr.align-center th {
              text-align: center;
            }

            .line-items-table {
              text-align: left;
              border-collapse: collapse;
            }

            .contact-table {
              text-align: left;
              vertical-align: top;
              border-collapse: collapse;
            }

            .line-items-table-caption-date-interval {
              font-weight: bold;
              margin: 0.25em 0 0.5em;
              text-align: left;
            }

            .line-items-table-head {
              padding-bottom: 4pt;
            }

            .line-items-table-body {
              line-height: 1.25rem;
            }

            .avoid-breaks {
              page-break-inside: avoid;
              page-break-after: avoid;
            }

            .contact-table-head {
              padding-bottom: 1px solid;
            }

            .contact-item {
              padding: 0px;
              margin: 0px;
              vertical-align: top;
            }

            .table-number {
              padding-left: 1em;
              text-align: right;
            }

            .total {
              text-align: right;
            }

            .amount-due-label {
              text-align: right;
              padding-right: 1rem;
              font-weight: bold;
            }

            .subTotal-label {
              text-align: right;
              padding-right: 1rem;
            }

            .total-label {
              text-align: right;
              padding-right: 1rem;
              font-weight: bold;
            }

            .taxTotal-label {
              text-align: right;
              padding-right: 1rem;
            }

            .section-header {
              font-weight: bold;
              text-transform: uppercase;
              border-bottom: solid 2px #5350ed;
              padding-bottom: 4pt;
            }

            label {
              padding-right: 1em;
            }

            .draft-watermark {
              margin-top: 0;
              margin-bottom: 0;
              color: #d6dde4;
            }

            .indented {
              text-indent: 3em;
            }

            .logo {
              max-height: 5em;
              width: auto;
            }

            .signature-section {
              break-inside: avoid;
              width: 100%;
              font-size: 8pt;
            }

            .provider-gap-block {
              width: 100%;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: start;

              border-bottom: 1px solid #000;
              height: 60px;
              font-size: 16px;
              padding-top: 10px;
            }

            .provider-gap-block--signature {
              height: 60px;
            }

            .signature-anchor-text {
              color: white;
              text-align: center;
              display: flex;
            }

            /* WYSIWYG (quill) editor specific styles */

            .ql-align-center {
              text-align: center;
            }

            .ql-align-right {
              text-align: right;
            }

            .ql-align-justify {
              text-align: justify;
            }

            .ql-indent-1 {
              padding-left: 3em;
            }

            .ql-indent-2 {
              padding-left: 6em;
            }

            .ql-indent-3 {
              padding-left: 9em;
            }

            .ql-indent-4 {
              padding-left: 12em;
            }

            .ql-indent-5 {
              padding-left: 15em;
            }

            .ql-indent-6 {
              padding-left: 18em;
            }

            .ql-indent-7 {
              padding-left: 21em;
            }

            .ql-indent-8 {
              padding-left: 24em;
            }

            .alpha-table {
              border-spacing: 0;
              border-collapse: collapse;
              margin-right: auto;
            }

            .alpha-tr {
              height: 12pt;
            }

            .alpha-td {
              border-right-style: solid;
              padding: 5pt 5pt 5pt 5pt;
              border-bottom-color: #000000;
              border-top-width: 1pt;
              border-right-width: 1pt;
              border-left-color: #000000;
              vertical-align: center;
              text-align: center;
              border-right-color: #000000;
              border-left-width: 1pt;
              border-top-style: solid;
              border-left-style: solid;
              border-bottom-width: 1pt;
              border-top-color: #000000;
              border-bottom-style: solid;
            }

            .alpha-table-text {
              color: #000000;
              text-decoration: none;
              vertical-align: baseline;
              font-style: normal;

              padding-top: 0pt;
              padding-bottom: 0pt;
              line-height: 1;
              text-align: center;
            }

            .alpha-table-body-text {
              font-weight: 400;
            }

            .alpha-table-head-text {
              font-weight: 700;
            }

            .alpha-table-head {
              background-color: #daebfd;
            }

            /* PDF page - styles and settings */
            @media print {
              @page {
                margin: 6mm 10mm 10mm 10mm;
                width: 210mm;
                height: 297mm;

                @top-right {
                  content: ' ';
                  background-color: #5350ed;
                }

                @top-left-corner {
                  content: ' ';
                  background-color: #5350ed;
                }

                @top-right-corner {
                  content: ' ';
                  background-color: #5350ed;
                }

                @bottom-right {
                  content: 'Page ' counter(page) ' of ' counter(pages);
                  font-size: 12px;
                }
              }
            }
          </style>
        </head>

        <body>
          <table width="100%">
            <tr>
              <td>
                <h1 style="margin-top: 0">eSign test</h1>
                <div></div>
                <div><EMAIL></div>
              </td>
              <td style="text-align: right">
                <div><label>Order ID</label> ORD-X3NKKDC</div>
                <div><label>Auto Renew</label> &#10003;</div>
              </td>
            </tr>
          </table>

          <div>
            <h3 class="section-header">Customer Information</h3>
            <div>
              <table class="contact-table">
                <thead class="contact-table-head">
                  <tr>
                    <th class="">
                        Bill to\s
                    </th>
                    <th class="">
                        Ship to\s
                    </th>
                  </tr>
                </thead>
                <tbody class="">
                  <tr>
                    <td class="contact-item">
                       eSignV3Test<br />
                      Sumit K<br />
                      635, S Ellis St<br />
         Chandler, AZ
                      65535<br />
                      United States<br />
                      <EMAIL><br />
                      <br />
                    </td>
                    <td class="contact-item">
                      eSignV3Test<br />
                      Sumit K<br />
                      635, S Ellis St<br />
         Chandler, AZ
                      65535<br />
                      United States<br />
                      <EMAIL><br />
                      <br />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <br />

          <div>
            <h3 class="section-header">Contract Terms</h3>

            <table class="line-items-table">
              <thead class="line-items-table-head">
                <tr>
                  <th class="">Start Date</th>
                  <th class="">End Date</th>
                  <th class="">Billing Cycle</th>
                  <th class="">Payment Term</th>
                  <th>Preferred Payment Type</th>
                </tr>
              </thead>
              <tbody class="">
                <tr>
                  <td class="">Feb 20, 2024</td>
                  <td class="">Feb 19, 2025</td>
                  <td class="">Yearly</td>
                  <td class="">Net 30</td>
                  <td class="">ACH, Credit Card, Check, Wire Transfer</td>
                </tr>
              </tbody>
            </table>
          </div>

          <br />

          <div>
            <h3 class="section-header">Contract Items</h3>
            <table class="alpha-table">
              <thead>
                <tr class="alpha-tr">
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-head-text">Plan Name</span></p>
                  </td>
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-head-text">Charge Name</span></p>
                  </td>
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-head-text">Start Date</span></p>
                  </td>
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-head-text">End Date</span></p>
                  </td>
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-head-text">Quantity</span></p>
                  </td>
                  <td class="alpha-td alpha-table-head" colspan="1" rowspan="1">
                    <p>
                      <span class="alpha-table-text alpha-table-head-text"> Total </span>
                    </p>
                  </td>
                </tr>
              </thead>
              <tbody>
                <tr class="alpha-tr">
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-body-text">EMS Recurring</span></p>
                  </td>
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-body-text">EMS Recurring</span></p>
                  </td>
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-body-text">Feb 20, 2024</span></p>
                  </td>
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-body-text">Feb 19, 2025</span></p>
                  </td>
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p><span class="alpha-table-text alpha-table-body-text">0 </span></p>
                  </td>
                  <td class="alpha-td" colspan="1" rowspan="1">
                    <p>
                      <span class="alpha-table-text table-number"> $0.00 </span>
                    </p>
                  </td>
                </tr>
                <tr>
                  <td>&nbsp;</td>
                </tr>
              </tbody>
            </table>
            <div class="total">
              <label class="total-label">Grand Total</label>
              <span class="table-number">$0.00</span>
            </div>

          </div>

          <div>
          </div>

         <div class="terms-section-container"></div>
          <div class="signature-section">
            <table width="100%">
              <tr>
                <th colspan="2" class="section-header">eSignV3Test</th>
                <th width="10%" class="section-header"></th>
                <th colspan="2" class="section-header">eSign test</th>
              </tr>

              <tr>
                <td>&nbsp</td>
              </tr>

              <tr>
                <th>
                  <div>Signature:</div>
                </th>
                <th>
                  <div class="provider-gap-block provider-gap-block--signature">
                    <div class="signature-anchor-text">
                      <div>_&#123;s:s1________&#125;_</div>
                    </div>
                  </div>
                </th>
              </tr>

              <tr>
                <th>
                  <div>Name:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_&#123;t:s1____________&#125;_</div>
                  </div>
                </th>
              </tr>

              <tr>
                <th>
                  <div>Date:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_&#123;d:s1______&#125;_</div>
                  </div>
                </th>
              </tr>

              <tr>
                <th>
                  <div>Title:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_&#123;t:s1______&#125;_</div>
                  </div>
                </th>
              </tr>
            </table>
          </div>

                   <div class="terms-section-container"></div>
          <div class="signature-section">
            <table width="100%">
              <tr>
                <th colspan="2" class="section-header">eSignV3Test</th>
                <th width="10%" class="section-header"></th>
                <th colspan="2" class="section-header">eSign test</th>
              </tr>

              <tr>
                <td>&nbsp</td>
              </tr>

              <tr>
                <th>
                  <div>Signature:</div>
                </th>
                <th>
                  <div class="provider-gap-block provider-gap-block--signature">
                    <div class="signature-anchor-text">
                      <div>_{s:s1________}_</div>
                    </div>
                  </div>
                </th>
              </tr>

              <tr>
                <th>
                  <div>Name:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_{t:s1____________}_</div>
                  </div>
                </th>
              </tr>

              <tr>
                <th>
                  <div>Date:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_{d:s1______}_</div>
                  </div>
                </th>
              </tr>

              <tr>
                <th>
                  <div>Title:</div>
                </th>
                <th>
                  <div class="provider-gap-block">
                    <div class="signature-anchor-text">_{t:s1______}_</div>
                  </div>
                </th>
              </tr>
            </table>
          </div>

        </body>

        </html>
        """;

    private static final String DOCUSIGN_TWO =
        """
            <html
              xmlns:o="urn:schemas-microsoft-com:office:office"
              xmlns:w="urn:schemas-microsoft-com:office:word"
              xmlns="http://www.w3.org/TR/REC-html40"
            >
             \s
            <head>
              <title>Quote</title>
              <script src="paged.polyfill.js"></script>
              <link rel="preconnect" href="https://fonts.googleapis.com" />
              <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
              <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400&display=swap" rel="stylesheet" />
              <style>
               \s
            body {
              margin: 0;
              font-family: "Roboto Slab", serif;
              font-size: 8pt;
            }

            body,
            table {
              font-weight: 300;
            }

            .header {
              margin-top: 1em;
            }

            .brand-name {
              color: #5350ed;
            }

            .bold {
              font-weight: bold;
            }

            table {
              flex: 1;
              width: 100%;
            }

            caption,
            th,
            td {
              text-align: left;
            }
            .align-left {
              text-align: left;
            }
            .align-right {
              text-align: right;
            }
            .align-center {
              text-align: center;
            }

            .padding-left-2 {
              padding-left: 2em;
            }

            tr.align-center td,
            tr.align-center th {
              text-align: center;
            }

            .line-items-table {
              text-align: left;
              border-collapse: collapse;
            }

            .contact-table {
              text-align: left;
              vertical-align: top;
              border-collapse: collapse;
            }
            .line-items-table-caption-date-interval {
              font-weight: bold;
              margin: 0.25em 0 0.5em;
              text-align: left;
            }

            .line-items-table-head {
              background-color: #daebfd;
              border-bottom: 1px solid;
            }

            .line-items-table-body {
              line-height: 1.25rem;
            }

            .avoid-breaks {
              page-break-inside: avoid;
              page-break-after: avoid;
            }

            .contact-table-head {
              background-color: #daebfd;
              border-bottom: 1px solid;
            }

            .contact-item {
              padding: 0px;
              margin: 0px;
              vertical-align: top;
            }

            .table-number {
              padding-left: 1em;
              text-align: right;
            }

            .total {
              text-align: right;
            }

            .amount-due-label {
              text-align: right;
              padding-right: 1rem;
              font-weight: bold;
            }

            .subTotal-label {
              text-align: right;
              padding-right: 1rem;
            }

            .total-label {
              text-align: right;
              padding-right: 1rem;
              font-weight: bold;
            }

            .taxTotal-label {
              text-align: right;
              padding-right: 1rem;
            }

            .section-header {
              font-weight: bold;
              text-transform: uppercase;
              border-bottom: solid 4px #5350ed;
            }

            label {
              padding-right: 1em;
            }

            .draft-watermark {
              margin-top: 0;
              margin-bottom: 0;
              color: #d6dde4;
            }

            .indented {
              text-indent: 3em;
            }

            .logo {
              max-height: 5em;
              width: auto;
            }

            .signature-section {
              border-collapse: collapse;
              break-inside: avoid;
              width: 100%;
            }

            .signature-block {
              display: flex;
              padding: 8px 0;
            }

            .signature-anchor-text {
              width: 100%;
              margin: 0 4px;
              color: white;
              text-align: center;
              border-bottom: 1px solid #000;
            }

            /* WYSIWYG (quill) editor specific styles */

            .ql-align-center {
              text-align: center;
            }

            .ql-align-right {
              text-align: right;
            }

            .ql-align-justify {
              text-align: justify;
            }

            .ql-indent-1 {
              padding-left: 3em;
            }

            .ql-indent-2 {
              padding-left: 6em;
            }

            .ql-indent-3 {
              padding-left: 9em;
            }

            .ql-indent-4 {
              padding-left: 12em;
            }

            .ql-indent-5 {
              padding-left: 15em;
            }

            .ql-indent-6 {
              padding-left: 18em;
            }

            .ql-indent-7 {
              padding-left: 21em;
            }

            .ql-indent-8 {
              padding-left: 24em;
            }

            /* PDF page - styles and settings */
            @media print {
              @page {
                margin: 6mm 10mm 10mm 10mm;
                width: 210mm;
                height: 297mm;
                @top-right {
                  content: ' ';
                  background-color: #5350ed;
                }
                @top-left-corner {
                  content: ' ';
                  background-color: #5350ed;
                }
                @top-right-corner {
                  content: ' ';
                  background-color: #5350ed;
                }
                @bottom-right {
                  content: 'Page ' counter(page) ' of ' counter(pages);
                  font-size: 12px;
                }
              }
            }
         \s
              </style>
            </head>
         \s
              <body>
               \s
         \s

                <div>
            <p class="section-header">Customer Information</p>
            <div>
             \s
            <table class="contact-table">
              <thead class="contact-table-head">
                <tr>
                  <th class="">Bill to</th>
        <th class="">Ship to</th>
                </tr>
              </thead>
              <tbody class="">
                <tr>
                  <td class="contact-item">Apple Inc<br />
              Hermoine Granger<br />
              London School of Business<br />
         London,\s
              91234<br />
              United Kingdom<br />
              <EMAIL><br />
              (912) 345-6780<br /></td>
        <td class="contact-item">Apple Inc<br />
              Harry Potter<br />
              Hogwarts school of Magic<br />
         London,\s
              95012<br />
              United Kingdom<br />
              <EMAIL><br />
              ***********<br /></td>
                </tr>
              </tbody>
            </table>
         \s
            </div>
          </div>

                <br />

                <div>
            <p class="section-header">Contract Terms</p>
           \s
              <table class="line-items-table">
               \s
              </table>
           \s
          </div>

                <br />

                <div>
                 \s
            <p class="section-header">Contract Items</p>
            <table class="line-items-table">
              <tr>
                <td class="bold">Dec 18, 2023 - Dec 17, 2024</td>
              </tr>
              <tr class="line-items-table-head">
                <th class="align-right">Quantity</th>
        <th class="">Quantity And Unit Of Measure</th>
        <th class="align-right">Start Date</th>
        <th class="">Unit Of Measure</th>
        <th class="align-right">Unit Price</th>
        <th class="align-right">Yearly Amount</th>
                <th class="table-number">Total</th>
              </tr>
              <tr>
                <td></td>
              </tr>
              <tr>
                <td class="align-right">1</td>
        <td class="">1 </td>
        <td class="align-right">Dec 18, 2023</td>
        <td class=""></td>
        <td class="align-right">$99.00</td>
        <td class="align-right"></td>
                <td class="table-number">$99.00</td>
              </tr>
              <tr>
                <td class="align-right">0</td>
        <td class="">0 GB</td>
        <td class="align-right">Dec 18, 2023</td>
        <td class="">GB</td>
        <td class="align-right">$0.00</td>
        <td class="align-right"></td>
                <td class="table-number">$0.00</td>
              </tr>
              <tr>
                <td>&nbsp;</td>
              </tr>
            </table>
            <div class="total">
              <label class="total-label">Grand Total</label>
              <span class="table-number">$99.00</span>
            </div>
           \s
           \s
                </div>

                <div>
          </div>

               \s
         <div class="terms-section-container"></div>  \s
            <br />
            <div>
              <p class="section-header">Automatic Payments</p>
              <p>
                To make automatic payments, add a payment instrument as your primary payment method.<br />
                <a href="https://dev2.subskribe.net/automatic-payment/018279d0-f29c-48bc-8374-fb57344017dd">Submit Payment Details</a>
              </p>
            </div>
          \s
           \s

            <div class="signature-section">
              <table width="100%">
                <tr>
                  <th colspan="2" class="section-header">Subskribe Test</th>
                  <th width="20%" class="section-header"></th>
                  <th colspan="2" class="section-header">Apple Inc</th>
                </tr>

                <tr>
                  <td>&nbsp</td>
                </tr>

                <tr>
                  <th>
                    <div>Signature:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**sign_here_1**</div>
                  </th>
                  <th width="20%"></th>
                  <th>
                    <div>Signature:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**sign_here_2**</div>
                  </th>
                </tr>

                <tr>
                  <th>
                    <div>Name:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**full_name_1**</div>
                  </th>
                  <th width="20%"></th>
                  <th>
                    <div>Name:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**full_name_2**</div>
                  </th>
                </tr>

                <tr>
                  <th>
                    <div>Date:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**date_signed_1**</div>
                  </th>
                  <th width="20%"></th>
                  <th>
                    <div>Date:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**date_signed_2**</div>
                  </th>
                </tr>

                <tr>
                  <th>
                    <div>Title:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**signer_title_1**</div>
                  </th>
                  <th width="20%"></th>
                  <th>
                    <div>Title:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**signer_title_2**</div>
                  </th>
                </tr>
              </table>
            </div>

         \s
               \s
            <script>
              const LOCALE = "en-US";
              const CURRENCY = "USD";
              const CURRENCY_FORMATTER = new Intl.NumberFormat(LOCALE, { style: 'currency', currency: CURRENCY });

              function roundToTwoDecimal(number) {
                return (Math.round((number + Number.EPSILON) * 100) / 100);
              }

              function localizeNumber(number) {
                return number.toLocaleString(LOCALE, {minimumFractionDigits: 2})
              }

              function formatNumber(number) {
                return localizeNumber(roundToTwoDecimal(number));
              }

              function formatPercent(number) {
                return formatNumber(number * 100) + '%';
              }

              function formatAmount(number) {
                return CURRENCY_FORMATTER.format(number);
              }

              function formatPercentValues() {
                const elements = document.querySelectorAll('.format-percent');
                elements.forEach(element => {
                    const originalValue = parseFloat(element.textContent);
                    element.textContent = formatPercent(originalValue);
                });
              }

              function formatAmounts() {
                const elements = document.querySelectorAll('.format-amount');
                elements.forEach(element => {
                    const originalValue = parseFloat(element.textContent);
                    element.textContent = formatAmount(originalValue);
                });
              }

              formatPercentValues();
              formatAmounts();
            </script>
         \s
              </body>
            </html>
         \s
        """;

    private static final String DOCUSIGN_ONE_MULTI_BLOCK =
        """
            <html
              xmlns:o="urn:schemas-microsoft-com:office:office"
              xmlns:w="urn:schemas-microsoft-com:office:word"
              xmlns="http://www.w3.org/TR/REC-html40"
            >
             \s
            <head>
              <title>Quote</title>
              <script src="paged.polyfill.js"></script>
              <link rel="preconnect" href="https://fonts.googleapis.com" />
              <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
              <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400&display=swap" rel="stylesheet" />
              <style>
               \s
            body {
              margin: 0;
              font-family: "Roboto Slab", serif;
              font-size: 8pt;
            }

            body,
            table {
              font-weight: 300;
            }

            .header {
              margin-top: 1em;
            }

            .brand-name {
              color: #5350ed;
            }

            .bold {
              font-weight: bold;
            }

            table {
              flex: 1;
              width: 100%;
            }

            caption,
            th,
            td {
              text-align: left;
            }
            .align-left {
              text-align: left;
            }
            .align-right {
              text-align: right;
            }
            .align-center {
              text-align: center;
            }

            .padding-left-2 {
              padding-left: 2em;
            }

            tr.align-center td,
            tr.align-center th {
              text-align: center;
            }

            .line-items-table {
              text-align: left;
              border-collapse: collapse;
            }

            .contact-table {
              text-align: left;
              vertical-align: top;
              border-collapse: collapse;
            }
            .line-items-table-caption-date-interval {
              font-weight: bold;
              margin: 0.25em 0 0.5em;
              text-align: left;
            }

            .line-items-table-head {
              background-color: #daebfd;
              border-bottom: 1px solid;
            }

            .line-items-table-body {
              line-height: 1.25rem;
            }

            .avoid-breaks {
              page-break-inside: avoid;
              page-break-after: avoid;
            }

            .contact-table-head {
              background-color: #daebfd;
              border-bottom: 1px solid;
            }

            .contact-item {
              padding: 0px;
              margin: 0px;
              vertical-align: top;
            }

            .table-number {
              padding-left: 1em;
              text-align: right;
            }

            .total {
              text-align: right;
            }

            .amount-due-label {
              text-align: right;
              padding-right: 1rem;
              font-weight: bold;
            }

            .subTotal-label {
              text-align: right;
              padding-right: 1rem;
            }

            .total-label {
              text-align: right;
              padding-right: 1rem;
              font-weight: bold;
            }

            .taxTotal-label {
              text-align: right;
              padding-right: 1rem;
            }

            .section-header {
              font-weight: bold;
              text-transform: uppercase;
              border-bottom: solid 4px #5350ed;
            }

            label {
              padding-right: 1em;
            }

            .draft-watermark {
              margin-top: 0;
              margin-bottom: 0;
              color: #d6dde4;
            }

            .indented {
              text-indent: 3em;
            }

            .logo {
              max-height: 5em;
              width: auto;
            }

            .signature-section {
              border-collapse: collapse;
              break-inside: avoid;
              width: 100%;
            }

            .signature-block {
              display: flex;
              padding: 8px 0;
            }

            .signature-anchor-text {
              width: 100%;
              margin: 0 4px;
              color: white;
              text-align: center;
              border-bottom: 1px solid #000;
            }

            /* WYSIWYG (quill) editor specific styles */

            .ql-align-center {
              text-align: center;
            }

            .ql-align-right {
              text-align: right;
            }

            .ql-align-justify {
              text-align: justify;
            }

            .ql-indent-1 {
              padding-left: 3em;
            }

            .ql-indent-2 {
              padding-left: 6em;
            }

            .ql-indent-3 {
              padding-left: 9em;
            }

            .ql-indent-4 {
              padding-left: 12em;
            }

            .ql-indent-5 {
              padding-left: 15em;
            }

            .ql-indent-6 {
              padding-left: 18em;
            }

            .ql-indent-7 {
              padding-left: 21em;
            }

            .ql-indent-8 {
              padding-left: 24em;
            }

            /* PDF page - styles and settings */
            @media print {
              @page {
                margin: 6mm 10mm 10mm 10mm;
                width: 210mm;
                height: 297mm;
                @top-right {
                  content: ' ';
                  background-color: #5350ed;
                }
                @top-left-corner {
                  content: ' ';
                  background-color: #5350ed;
                }
                @top-right-corner {
                  content: ' ';
                  background-color: #5350ed;
                }
                @bottom-right {
                  content: 'Page ' counter(page) ' of ' counter(pages);
                  font-size: 12px;
                }
              }
            }
         \s
              </style>
            </head>
         \s
              <body>
               \s
         \s

                <div>
            <p class="section-header">Customer Information</p>
            <div>
             \s
            <table class="contact-table">
              <thead class="contact-table-head">
                <tr>
                  <th class="">Bill to</th>
        <th class="">Ship to</th>
                </tr>
              </thead>
              <tbody class="">
                <tr>
                  <td class="contact-item">Apple Inc<br />
              Hermoine Granger<br />
              London School of Business<br />
         London,\s
              91234<br />
              United Kingdom<br />
              <EMAIL><br />
              (912) 345-6780<br /></td>
        <td class="contact-item">Apple Inc<br />
              Harry Potter<br />
              Hogwarts school of Magic<br />
         London,\s
              95012<br />
              United Kingdom<br />
              <EMAIL><br />
              ***********<br /></td>
                </tr>
              </tbody>
            </table>
         \s
            </div>
          </div>

                <br />

                <div>
            <p class="section-header">Contract Terms</p>
           \s
              <table class="line-items-table">
               \s
              </table>
           \s
          </div>

                <br />

                <div>
                 \s
            <p class="section-header">Contract Items</p>
            <table class="line-items-table">
              <tr>
                <td class="bold">Dec 18, 2023 - Dec 17, 2024</td>
              </tr>
              <tr class="line-items-table-head">
                <th class="align-right">Quantity</th>
        <th class="">Quantity And Unit Of Measure</th>
        <th class="align-right">Start Date</th>
        <th class="">Unit Of Measure</th>
        <th class="align-right">Unit Price</th>
        <th class="align-right">Yearly Amount</th>
                <th class="table-number">Total</th>
              </tr>
              <tr>
                <td></td>
              </tr>
              <tr>
                <td class="align-right">1</td>
        <td class="">1 </td>
        <td class="align-right">Dec 18, 2023</td>
        <td class=""></td>
        <td class="align-right">$99.00</td>
        <td class="align-right"></td>
                <td class="table-number">$99.00</td>
              </tr>
              <tr>
                <td class="align-right">0</td>
        <td class="">0 GB</td>
        <td class="align-right">Dec 18, 2023</td>
        <td class="">GB</td>
        <td class="align-right">$0.00</td>
        <td class="align-right"></td>
                <td class="table-number">$0.00</td>
              </tr>
              <tr>
                <td>&nbsp;</td>
              </tr>
            </table>
            <div class="total">
              <label class="total-label">Grand Total</label>
              <span class="table-number">$99.00</span>
            </div>
           \s
           \s
                </div>

                <div>
          </div>

               \s
         <div class="terms-section-container"></div>  \s
            <br />
            <div>
              <p class="section-header">Automatic Payments</p>
              <p>
                To make automatic payments, add a payment instrument as your primary payment method.<br />
                <a href="https://dev2.subskribe.net/automatic-payment/018279d0-f29c-48bc-8374-fb57344017dd">Submit Payment Details</a>
              </p>
            </div>
          \s
           \s

            <div class="signature-section">
              <table width="100%">
                <tr>
                  <th colspan="2" class="section-header">Subskribe Test</th>
                  <th width="20%" class="section-header"></th>
                  <th colspan="2" class="section-header">Apple Inc</th>
                </tr>

                <tr>
                  <td>&nbsp</td>
                </tr>

                <tr>
                  <th>
                    <div>Signature:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**sign_here_1**</div>
                  </th>
                </tr>

                <tr>
                  <th>
                    <div>Name:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**full_name_1**</div>
                  </th>
                </tr>

                <tr>
                  <th>
                    <div>Date:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**date_signed_1**</div>
                  </th>
                </tr>

                <tr>
                  <th>
                    <div>Title:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**signer_title_1**</div>
                  </th>
                </tr>
              </table>
            </div>

            <div class="signature-section">
              <table width="100%">
                <tr>
                  <th colspan="2" class="section-header">Subskribe Test</th>
                  <th width="20%" class="section-header"></th>
                  <th colspan="2" class="section-header">Apple Inc</th>
                </tr>

                <tr>
                  <td>&nbsp</td>
                </tr>

                <tr>
                  <th>
                    <div>Signature:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**sign_here_1**</div>
                  </th>
                </tr>

                <tr>
                  <th>
                    <div>Name:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**full_name_1**</div>
                  </th>
                </tr>

                <tr>
                  <th>
                    <div>Date:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**date_signed_1**</div>
                  </th>
                </tr>

                <tr>
                  <th>
                    <div>Title:</div>
                  </th>
                  <th>
                    <div class="signature-anchor-text">**signer_title_1**</div>
                  </th>
                </tr>
              </table>
            </div>

         \s
               \s
            <script>
              const LOCALE = "en-US";
              const CURRENCY = "USD";
              const CURRENCY_FORMATTER = new Intl.NumberFormat(LOCALE, { style: 'currency', currency: CURRENCY });

              function roundToTwoDecimal(number) {
                return (Math.round((number + Number.EPSILON) * 100) / 100);
              }

              function localizeNumber(number) {
                return number.toLocaleString(LOCALE, {minimumFractionDigits: 2})
              }

              function formatNumber(number) {
                return localizeNumber(roundToTwoDecimal(number));
              }

              function formatPercent(number) {
                return formatNumber(number * 100) + '%';
              }

              function formatAmount(number) {
                return CURRENCY_FORMATTER.format(number);
              }

              function formatPercentValues() {
                const elements = document.querySelectorAll('.format-percent');
                elements.forEach(element => {
                    const originalValue = parseFloat(element.textContent);
                    element.textContent = formatPercent(originalValue);
                });
              }

              function formatAmounts() {
                const elements = document.querySelectorAll('.format-amount');
                elements.forEach(element => {
                    const originalValue = parseFloat(element.textContent);
                    element.textContent = formatAmount(originalValue);
                });
              }

              formatPercentValues();
              formatAmounts();
            </script>
         \s
              </body>
            </html>
         \s
        """;

    @Test
    void pandaDocTwo() {
        Assertions.assertThat(
            OrderDocumentService.getNumberOfSignatureBlocksForOrderHtml(PANDADOC_TWO, ElectronicSignatureProvider.PANDADOC).orElseThrow()
        ).isEqualTo(2);
    }

    @Test
    void pandaDocOneMultiBlockWithHtmlEntities() {
        Assertions.assertThat(
            OrderDocumentService.getNumberOfSignatureBlocksForOrderHtml(
                PANDADOC_ONE_MULTI_BLOCK_WITH_HTML_ENTITIES,
                ElectronicSignatureProvider.PANDADOC
            ).orElseThrow()
        ).isEqualTo(1);
    }

    @Test
    void pandaDocZero() {
        // DocuSign form will not have any PandaDoc signature blocks
        Assertions.assertThat(
            OrderDocumentService.getNumberOfSignatureBlocksForOrderHtml(DOCUSIGN_TWO, ElectronicSignatureProvider.PANDADOC).orElseThrow()
        ).isEqualTo(0);
    }

    @Test
    void docuSignTwo() {
        // New pattern returns the correct result
        Assertions.assertThat(
            OrderDocumentService.getNumberOfSignatureBlocksForOrderHtml(DOCUSIGN_TWO, ElectronicSignatureProvider.DOCUSIGN).orElseThrow()
        ).isEqualTo(2);
        // Old pattern returns the wrong result
        Assertions.assertThat(Pattern.compile(".*\\*\\*sign_here_\\d*\\*\\*.*", Pattern.DOTALL).matcher(DOCUSIGN_TWO).results().count()).isEqualTo(1);
    }

    @Test
    void docuSignOneMultiBlock() {
        Assertions.assertThat(
            OrderDocumentService.getNumberOfSignatureBlocksForOrderHtml(DOCUSIGN_ONE_MULTI_BLOCK, ElectronicSignatureProvider.DOCUSIGN).orElseThrow()
        ).isEqualTo(1);
    }

    @Test
    void docuSignZero() {
        // PandaDoc form will not have any DocuSign signature blocks
        Assertions.assertThat(
            OrderDocumentService.getNumberOfSignatureBlocksForOrderHtml(PANDADOC_TWO, ElectronicSignatureProvider.DOCUSIGN).orElseThrow()
        ).isEqualTo(0);
    }
}
