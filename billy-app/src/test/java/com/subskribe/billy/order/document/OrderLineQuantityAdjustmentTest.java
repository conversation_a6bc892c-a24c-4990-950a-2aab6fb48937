package com.subskribe.billy.order.document;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.shared.document.TemplateCharge;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import org.junit.jupiter.api.Test;

public class OrderLineQuantityAdjustmentTest {

    DocumentRenderFormatter formatter = new DocumentRenderFormatter(TimeZone.getTimeZone("UTC"));

    @Test
    public void checkQuantityDoesNotChangeWithDifferentCustomField() {
        String typeKey = UUID.randomUUID().toString();

        OrderTemplateLineItem orderTemplateLineItem = getLineItemWithChargeCustomField(typeKey, "0.25", List.of(), 2L);

        OrderTemplateData.updateLineItemQuantityBasedOnChargeCustomField(UUID.randomUUID().toString(), orderTemplateLineItem);

        assertEquals("2", orderTemplateLineItem.getQuantity());
    }

    @Test
    public void checkWhetherQuantityIsUpdatedBasedOnChargeCustomField() {
        String typeKey = UUID.randomUUID().toString();

        OrderTemplateLineItem orderTemplateLineItem = getLineItemWithChargeCustomField(typeKey, "0.25", List.of(), 4L);

        OrderTemplateData.updateLineItemQuantityBasedOnChargeCustomField(typeKey, orderTemplateLineItem);

        assertEquals("1", orderTemplateLineItem.getQuantity());
    }

    @Test
    public void checkWhetherQuantityIsUpdatedBasedOnChargeCustomFieldForDecimals() {
        String typeKey = UUID.randomUUID().toString();

        OrderTemplateLineItem orderTemplateLineItem = getLineItemWithChargeCustomField(typeKey, "0.25", List.of(), 2L);

        OrderTemplateData.updateLineItemQuantityBasedOnChargeCustomField(typeKey, orderTemplateLineItem);

        assertEquals("0.5", orderTemplateLineItem.getQuantity());
    }

    private OrderTemplateLineItem getLineItemWithChargeCustomField(String customFieldName, String selection, List<String> options, Long quantity) {
        OrderLineItemDetail orderLineItemDetail = new OrderLineItemDetail();
        orderLineItemDetail.setQuantity(quantity);

        CustomFieldEntry cfn = new CustomFieldEntry(
            UUID.randomUUID().toString(),
            CustomFieldType.PICKLIST,
            customFieldName,
            customFieldName,
            selection,
            List.of(selection),
            options,
            false,
            null,
            null
        );
        CustomFieldValue customFieldValue = CustomFieldEntry.toValue(cfn);
        CustomField customField = new CustomField(Map.of(customFieldName, customFieldValue));
        TemplateCharge templateCharge = new TemplateCharge(customField, formatter, null, null, new ChargeDetail());

        return new OrderTemplateLineItem(orderLineItemDetail, null, null, templateCharge, null, null, formatter, null, null, null);
    }
}
