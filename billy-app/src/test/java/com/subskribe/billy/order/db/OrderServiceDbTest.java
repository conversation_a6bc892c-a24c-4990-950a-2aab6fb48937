package com.subskribe.billy.order.db;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.core.type.TypeReference;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceService;
import com.subskribe.billy.attachments.service.AttachmentsService;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipGetService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customization.service.CustomizationService;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.document.service.DocumentLinkService;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityContextProviderFixture;
import com.subskribe.billy.entity.fixtures.EntityContextResolverFixture;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.esign.model.ElectronicSignature;
import com.subskribe.billy.esign.model.ElectronicSignatureStatus;
import com.subskribe.billy.esign.services.EsignService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.fixtures.AccountData;
import com.subskribe.billy.fixtures.MockInvoicePreviewService;
import com.subskribe.billy.fixtures.ProductCatalogData;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.hubspot.service.HubSpotJobQueueService;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.services.MissingOrderChargesService;
import com.subskribe.billy.order.services.OrderAttributesUpdateService;
import com.subskribe.billy.order.services.OrderCustomBillingService;
import com.subskribe.billy.order.services.OrderDocumentTemplateService;
import com.subskribe.billy.order.services.OrderEventService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderIdGenerator;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.order.services.OrderValidationService;
import com.subskribe.billy.paymentterms.model.PaymentTermSettings;
import com.subskribe.billy.paymentterms.services.PaymentTermSettingsService;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.revrec.services.RevenueRecognitionJobService;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.salesroom.service.SalesRoomLinkService;
import com.subskribe.billy.shared.config.TypeSafeDynamicConfigLoader;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.db.SubscriptionDAO;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionEventService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionIdGenerator;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderGetService;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderService;
import com.subskribe.billy.template.services.DocumentCustomContentGetService;
import com.subskribe.billy.template.services.DocumentCustomContentService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.OrderTermsService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.test.WithDb;
import com.subskribe.billy.test.utilities.MockChargeBuilder;
import com.subskribe.billy.user.service.UserService;
import com.subskribe.billy.validation.helpers.OrderValidation;
import com.subskribe.billy.validation.helpers.SubscriptionValidation;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class OrderServiceDbTest extends WithDb {

    private static final String TENANT_ID = "test tenant 1 id";
    private static final String ACCOUNT_ID = "c00cf41d-2b30-49d6-ae8f-a6913e3a4b68";
    private static final String USER_ID = "8625d494-**************-fe7487aae7da";
    private static final String ADDRESS_ID = "ffb3c876-4f4f-419d-a99a-d0881acbeea8";

    // test plans
    private static final String TEST_PRODUCT_ID = "Tenant 1 Product 1";
    private static final String TEST_SKU = "Tenant SKU";
    private static final UUID PLAN_ID_1 = UUID.fromString("363db8f3-0c2d-46bd-a52a-0a204291d5bc");
    private static final String EXTERNAL_PLAN_ID_1 = "Tenant 1 Plan 1";
    private static final UUID PLAN1_CHARGE1_ID = UUID.fromString("c8a848d9-5395-421e-b0fb-bc2c6cb2fb4a");
    private static final UUID PLAN1_CHARGE2_ID = UUID.fromString("ff313d39-0ac7-4e6c-b711-776f7833f10d");

    private static final UUID PLAN_ID_2 = UUID.fromString("8c2d5447-b362-49e6-9717-099080ee8f90");
    private static final String EXTERNAL_PLAN_ID_2 = "Tenant 1 Plan 2";
    private static final UUID PLAN2_CHARGE1_ID = UUID.fromString("69fe4219-8f79-41b9-ae0e-fb51917e21d8");

    private static final UUID PLAN_ID_3 = UUID.fromString("d7994f4c-8008-49c8-9c15-ab028aa426b8");
    private static final String EXTERNAL_PLAN_ID_3 = "Tenant 1 Plan 3";
    private static final UUID PLAN3_CHARGE1_ID = UUID.fromString("e9f98431-f248-4fbc-be5e-eff37473392a");

    private static final TenantIdProvider MOCK_TENANT_ID_PROVIDER = mock(TenantIdProvider.class);
    private static final EntityContextProvider MOCK_ENTITY_CONTEXT_PROVIDER = EntityContextProviderFixture.buildSingleEntityContext();
    private static final EntityContextResolver MOCK_ENTITY_ID_RESOLVER = EntityContextResolverFixture.build();
    private static final ProductCatalogGetService MOCK_PRODUCT_CATALOG_GET_SVC = mock(ProductCatalogGetService.class);
    private static final AccountGetService MOCK_ACCOUNT_GET_SERVICE = mock(AccountGetService.class);
    private static final EmailContactListService MOCK_EMAIL_CONTACT_LIST_SERVICE = mock(EmailContactListService.class);
    private static final DocumentTemplateGetService MOCK_DOCUMENT_TEMPLATE_GET_SERVICE = mock(DocumentTemplateGetService.class);
    private static final OpportunityService MOCK_OPPORTUNITY_SERVICE = mock(OpportunityService.class);
    private static final OrderTermsService MOCK_ORDER_TERMS_SERVICE = mock(OrderTermsService.class);
    private static final DiscountService MOCK_DISCOUNT_SERVICE = mock(DiscountService.class);
    private static final ApprovalFlowInstanceService MOCK_APPROVAL_FLOW_INSTANCE_SERVICE = mock(ApprovalFlowInstanceService.class);
    private static final UserService MOCK_USER_SERVICE = mock(UserService.class);
    private static final SalesforceJobQueueService MOCK_SALESFORCE_JOB_QUEUE_SERVICE = mock(SalesforceJobQueueService.class);
    private static final BillyConfiguration MOCK_BILLY_CONFIGURATION = TypeSafeDynamicConfigLoader.load("common-local.conf");

    private static final FeatureService MOCK_FEATURE_SERVICE = mock(FeatureService.class);
    private static final PlatformFeatureService MOCK_PLATFORM_FEATURE_SERVICE = mock(PlatformFeatureService.class);

    private static final TenantSettingService MOCK_TENANT_SETTING_SERVICE = TenantSettingServiceFixture.getDefault();

    private static final RevenueRecognitionJobService MOCK_REVENUE_RECOGNITION_JOB_SERVICE = mock(RevenueRecognitionJobService.class);

    private static final CustomFieldService MOCK_CUSTOM_FIELD_SERVICE = mock(CustomFieldService.class);

    private static final DocumentCustomContentService MOCK_DOCUMENT_CUSTOM_CONTENT_SERVICE = mock(DocumentCustomContentService.class);
    private static final DocumentCustomContentGetService MOCK_DOCUMENT_CUSTOM_CONTENT_GET_SERVICE = mock(DocumentCustomContentGetService.class);

    private static final AttachmentsService MOCK_ATTACHMENTS_SERVICE = mock(AttachmentsService.class);

    private static final PaymentTermSettingsService MOCK_PAYMENT_TERM_SETTINGS_SERVICE = mock(PaymentTermSettingsService.class);

    private static final ReportingJobQueueService MOCK_REPORTING_JOB_QUEUE_SERVICE = mock(ReportingJobQueueService.class);

    private static final CatalogRelationshipGetService MOCK_CATALOG_RELATIONSHIP_GET_SERVICE = mock(CatalogRelationshipGetService.class);
    private static final TaxService MOCK_TAX_SERVICE = mock(TaxService.class);
    private static final TaxRateGetService MOCK_TAX_RATE_GET_SERVICE = mock(TaxRateGetService.class);

    private static final CustomizationService MOCK_CUSTOMIZATION_SERVICE = mock(CustomizationService.class);
    private final MissingOrderChargesService MOCK_MISSING_ORDER_CHARGE_SERVICE = mock(MissingOrderChargesService.class);
    private final MetricsService MOCK_METRICS_SERVICE = mock(MetricsService.class);
    private final CrmService MOCK_CRM_SERVICE = mock(CrmService.class);
    private final CurrencyConversionRateGetService MOCK_CURRENCY_CONVERSION_GET_SERVICE = mock(CurrencyConversionRateGetService.class);
    private final EsignService MOCK_ESIGN_SERVICE = mock(EsignService.class);
    private final OrderCustomBillingService MOCK_ORDER_CUSTOM_BILLING_SERVICE = mock(OrderCustomBillingService.class);
    private final OrderDocumentTemplateService MOCK_ORDER_DOCUMENT_TEMPLATE_SERVICE = mock(OrderDocumentTemplateService.class);
    private final CustomTemplateUpdatedOnOrderGetService MOCK_CUSTOM_TEMPLATE_UPDATED_ON_ORDER_GET_SERVICE = mock(
        CustomTemplateUpdatedOnOrderGetService.class
    );
    private final CustomTemplateUpdatedOnOrderService MOCK_CUSTOM_TEMPLATE_UPDATED_ON_ORDER_SERVICE = mock(CustomTemplateUpdatedOnOrderService.class);
    private final HubSpotJobQueueService MOCK_HUBSPOT_JOB_QUEUE_SERVICE = mock(HubSpotJobQueueService.class);

    private OrderService orderService;
    private OrderGetService orderGetService;
    private OrderServiceDbTest.TestData testData;
    private String orderId;

    private SubscriptionGetService subscriptionGetService;

    @BeforeAll
    public void init() throws IOException, URISyntaxException {
        EntityFixture.initDb(dslContextProvider);
        createServices();

        testData = asType(getClass(), "newOrderTestData.json", new TypeReference<>() {});

        Product product = ProductCatalogData.createProduct(TEST_PRODUCT_ID, TEST_SKU);

        // Add mocks for bulk get plans and charges
        Plan plan1 = ProductCatalogData.createPlan(
            PLAN_ID_1,
            EXTERNAL_PLAN_ID_1,
            TEST_PRODUCT_ID,
            List.of(ProductCatalogData.createCharge(PLAN1_CHARGE1_ID, PLAN_ID_1), ProductCatalogData.createCharge(PLAN1_CHARGE2_ID, PLAN_ID_1))
        );
        Plan plan2 = ProductCatalogData.createPlan(
            PLAN_ID_2,
            EXTERNAL_PLAN_ID_2,
            TEST_PRODUCT_ID,
            List.of(ProductCatalogData.createCharge(PLAN2_CHARGE1_ID, PLAN_ID_2))
        );
        Plan plan3 = ProductCatalogData.createPlan(
            PLAN_ID_3,
            EXTERNAL_PLAN_ID_3,
            TEST_PRODUCT_ID,
            List.of(ProductCatalogData.createCharge(PLAN3_CHARGE1_ID, PLAN_ID_3))
        );

        List<Plan> planList = List.of(plan1, plan2, plan3);
        Map<String, Charge> chargeMap = MockChargeBuilder.getMockChargeMap(planList);
        when(MOCK_PRODUCT_CATALOG_GET_SVC.getPlansByIds(anyList())).thenReturn(planList);
        when(MOCK_PRODUCT_CATALOG_GET_SVC.getPlansByPlanIds(anyList())).thenReturn(planList);
        when(MOCK_PRODUCT_CATALOG_GET_SVC.getChargeMapByChargeIds(anyList())).thenReturn(chargeMap);
        when(MOCK_PRODUCT_CATALOG_GET_SVC.getProduct(any())).thenReturn(product);
        when(MOCK_TENANT_ID_PROVIDER.provideTenantIdString()).thenReturn(TENANT_ID);
        when(MOCK_ACCOUNT_GET_SERVICE.getAccount(ACCOUNT_ID)).thenReturn(AccountData.createAccount(ACCOUNT_ID, USER_ID, ADDRESS_ID));
        when(MOCK_ACCOUNT_GET_SERVICE.getContact(USER_ID)).thenReturn(AccountData.createAccountUser(USER_ID, ACCOUNT_ID, ADDRESS_ID));
        when(MOCK_OPPORTUNITY_SERVICE.upsertOpportunityForOrderIfNeeded(any(), any(), any(), anyBoolean())).thenReturn(Optional.empty());
        PaymentTermSettings paymentTermSettings = new PaymentTermSettings();
        paymentTermSettings.setCustomPaymentTermsAllowed(false);
        paymentTermSettings.setDefaultPaymentTerms(PaymentTermSettingsService.DEFAULT_PAYMENT_TERMS);
        when(MOCK_PAYMENT_TERM_SETTINGS_SERVICE.getPaymentTermSettings()).thenReturn(paymentTermSettings);
        when(MOCK_CUSTOM_FIELD_SERVICE.getCustomFields(any(), anyString())).thenReturn(new CustomField(new HashMap<>()));
    }

    private void createServices() {
        var subscriptionDao = new SubscriptionDAO(MOCK_TENANT_ID_PROVIDER, dslContextProvider);
        var subscriptionIdGenerator = new SubscriptionIdGenerator(subscriptionDao, dslContextProvider, MOCK_TENANT_ID_PROVIDER);

        subscriptionGetService = new SubscriptionGetService(subscriptionDao, MOCK_TENANT_ID_PROVIDER, dslContextProvider, MOCK_CUSTOM_FIELD_SERVICE);

        SubscriptionService subscriptionService = new SubscriptionService(
            subscriptionDao,
            dslContextProvider,
            MOCK_TENANT_ID_PROVIDER,
            MOCK_ACCOUNT_GET_SERVICE,
            subscriptionIdGenerator,
            subscriptionGetService,
            mock(SalesforceJobQueueService.class),
            mock(HubSpotJobQueueService.class),
            MOCK_EMAIL_CONTACT_LIST_SERVICE,
            MOCK_CUSTOM_FIELD_SERVICE,
            mock(CrmService.class),
            mock(SubscriptionEventService.class),
            orderGetService,
            mock(HubSpotService.class)
        );

        var sqlOrderDao = new OrderDAO(MOCK_TENANT_ID_PROVIDER, dslContextProvider, MOCK_FEATURE_SERVICE);
        orderGetService = new OrderGetService(
            sqlOrderDao,
            dslContextProvider,
            MOCK_TENANT_ID_PROVIDER,
            MOCK_DISCOUNT_SERVICE,
            mock(OpportunityGetService.class),
            MOCK_ORDER_TERMS_SERVICE,
            MOCK_CUSTOM_FIELD_SERVICE,
            MOCK_DOCUMENT_CUSTOM_CONTENT_GET_SERVICE,
            MOCK_CUSTOM_TEMPLATE_UPDATED_ON_ORDER_GET_SERVICE
        );
        var orderIdGenerator = new OrderIdGenerator(dslContextProvider, MOCK_TENANT_ID_PROVIDER, sqlOrderDao);

        OrderValidationService orderValidationService = new OrderValidationService(MOCK_ACCOUNT_GET_SERVICE, MOCK_TAX_SERVICE);

        orderService = new OrderService(
            sqlOrderDao,
            MOCK_ACCOUNT_GET_SERVICE,
            MOCK_PRODUCT_CATALOG_GET_SVC,
            subscriptionService,
            new MockInvoicePreviewService(),
            dslContextProvider,
            MOCK_TENANT_ID_PROVIDER,
            MOCK_ENTITY_CONTEXT_PROVIDER,
            MOCK_ENTITY_ID_RESOLVER,
            orderIdGenerator,
            MOCK_EMAIL_CONTACT_LIST_SERVICE,
            MOCK_DOCUMENT_TEMPLATE_GET_SERVICE,
            MOCK_DOCUMENT_CUSTOM_CONTENT_SERVICE,
            mock(OpportunityGetService.class),
            MOCK_OPPORTUNITY_SERVICE,
            MOCK_ORDER_TERMS_SERVICE,
            orderGetService,
            orderValidationService,
            MOCK_DISCOUNT_SERVICE,
            MOCK_APPROVAL_FLOW_INSTANCE_SERVICE,
            MOCK_USER_SERVICE,
            MOCK_SALESFORCE_JOB_QUEUE_SERVICE,
            MOCK_FEATURE_SERVICE,
            MOCK_PLATFORM_FEATURE_SERVICE,
            MOCK_TENANT_SETTING_SERVICE,
            MOCK_BILLY_CONFIGURATION,
            MOCK_REVENUE_RECOGNITION_JOB_SERVICE,
            MOCK_CUSTOM_FIELD_SERVICE,
            MOCK_ATTACHMENTS_SERVICE,
            MOCK_PAYMENT_TERM_SETTINGS_SERVICE,
            MOCK_REPORTING_JOB_QUEUE_SERVICE,
            subscriptionGetService,
            mock(DocumentLinkService.class),
            MOCK_CATALOG_RELATIONSHIP_GET_SERVICE,
            mock(RateCardService.class),
            mock(OrderEventService.class),
            mock(SubscriptionBillingPeriodService.class),
            MOCK_TAX_SERVICE,
            MOCK_TAX_RATE_GET_SERVICE,
            mock(SalesRoomLinkService.class),
            MOCK_CUSTOMIZATION_SERVICE,
            MOCK_MISSING_ORDER_CHARGE_SERVICE,
            MOCK_METRICS_SERVICE,
            MOCK_CRM_SERVICE,
            MOCK_CURRENCY_CONVERSION_GET_SERVICE,
            MOCK_ESIGN_SERVICE,
            MOCK_ORDER_CUSTOM_BILLING_SERVICE,
            mock(OrderAttributesUpdateService.class),
            MOCK_ORDER_DOCUMENT_TEMPLATE_SERVICE,
            MOCK_CUSTOM_TEMPLATE_UPDATED_ON_ORDER_GET_SERVICE,
            MOCK_CUSTOM_TEMPLATE_UPDATED_ON_ORDER_SERVICE,
            MOCK_HUBSPOT_JOB_QUEUE_SERVICE
        );
    }

    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    private static class TestData {

        Order draftOrderV1;
        Order draftOrderV2;
        Order submitOrder;
        SubscriptionTestData expectedSubscriptionV1;
    }

    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    private static class SubscriptionTestData {

        SubscriptionEntity subscriptionEntity;
        List<SubscriptionCharge> charges;
    }

    @BeforeEach
    public void setUp() {
        MOCK_BILLY_CONFIGURATION.getTenantSealProtectionConfiguration().setEnabled(false);
        // make sure even if the feature is enabled accidentally, the tests fail
        when(MOCK_TENANT_SETTING_SERVICE.isSealed()).thenReturn(false);
    }

    @Test
    @org.junit.jupiter.api.Order(0)
    public void testOrderCreationFailsIfTenantSealIfOff() {
        MOCK_BILLY_CONFIGURATION.getTenantSealProtectionConfiguration().setEnabled(true);
        when(MOCK_TENANT_SETTING_SERVICE.isSealed()).thenReturn(false);
        var inputOrder = testData.draftOrderV1;
        Assertions.assertThatThrownBy(() -> orderService.addOrder(inputOrder, false))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("Tenant settings need to be finalized/sealed");
        MOCK_BILLY_CONFIGURATION.getTenantSealProtectionConfiguration().setEnabled(false);
    }

    @Test
    @org.junit.jupiter.api.Order(1)
    public void testDraftOrder() {
        var inputOrder = testData.draftOrderV1;
        var draftOrder = orderService.addOrder(inputOrder, false);
        orderId = draftOrder.getOrderId();

        testData.draftOrderV1.getLineItems().forEach(li -> li.setAction(ActionType.ADD));
        OrderValidation.validateOrder(testData.draftOrderV1, draftOrder);
    }

    @Test
    @org.junit.jupiter.api.Order(2)
    public void testUpdateDraftOrderRemoveAndAddLines() {
        var savedOrder = orderGetService.getOrderByOrderId(orderId);
        updateDraftOrderIds(testData.draftOrderV2, savedOrder);

        var updateOrder = testData.draftOrderV2;
        var updatedDraftOrder = orderService.updateOrder(updateOrder, false);
        assertEquals(updatedDraftOrder.getOrderId(), orderId);

        testData.draftOrderV2.getLineItems().forEach(li -> li.setAction(ActionType.ADD));
        OrderValidation.validateOrder(testData.draftOrderV2, updatedDraftOrder);
    }

    @Test
    @org.junit.jupiter.api.Order(3)
    public void testUpdateDraftOrderLinesAndSubmit() {
        var savedOrder = orderGetService.getOrderByOrderId(orderId);

        // update the order first
        updateDraftOrderIds(testData.submitOrder, savedOrder);
        var updateOrderCopy = testData.submitOrder;
        updateOrderCopy.setEndDate(null);
        orderService.updateOrder(updateOrderCopy, false);

        savedOrder = orderGetService.getOrderByOrderId(testData.submitOrder.getOrderId());
        OrderValidation.validateOrder(testData.submitOrder, savedOrder);

        updateDraftOrderIds(testData.submitOrder, savedOrder);
        orderService.updateOrderStatus(testData.submitOrder.getOrderId(), OrderStatus.SUBMITTED);
        savedOrder = orderGetService.getOrderByOrderId(orderId);

        // check order is the same expect state changed to submitted
        testData.submitOrder.setStatus(OrderStatus.SUBMITTED);
        OrderValidation.validateOrder(testData.submitOrder, savedOrder);
    }

    @Test
    @org.junit.jupiter.api.Order(4)
    public void testUpdateOrderStateToApproved() {
        orderService.updateOrderStatusInternal(testData.submitOrder.getOrderId(), OrderStatus.APPROVED, Optional.empty());
        var savedOrder = orderGetService.getOrderByOrderId(testData.submitOrder.getOrderId());

        testData.submitOrder.setStatus(OrderStatus.APPROVED);
        OrderValidation.validateOrder(testData.submitOrder, savedOrder);
    }

    @Test
    @org.junit.jupiter.api.Order(5)
    public void testUpdateOrderStateToExecutedFailsIfThereIsActiveEsign() {
        var activeElectronicSignature = new ElectronicSignature();
        activeElectronicSignature.setStatus(ElectronicSignatureStatus.PARTIALLY_SIGNED);
        when(MOCK_ESIGN_SERVICE.getElectronicSignatureByOrderId(testData.submitOrder.getOrderId())).thenReturn(activeElectronicSignature);
        Assertions.assertThatThrownBy(() -> orderService.updateOrderStatus(testData.submitOrder.getOrderId(), OrderStatus.EXECUTED))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("has an active esign status");
        // remove mock
        when(MOCK_ESIGN_SERVICE.getElectronicSignatureByOrderId(testData.submitOrder.getOrderId())).thenThrow(
            new ObjectNotFoundException(BillyObjectType.ESIGNATURE, testData.submitOrder.getOrderId())
        );
    }

    @Test
    @org.junit.jupiter.api.Order(6)
    public void testUpdateOrderStateToExecuted() {
        orderService.updateOrderStatus(testData.submitOrder.getOrderId(), OrderStatus.EXECUTED);
        var savedOrder = orderGetService.getOrderByOrderId(testData.submitOrder.getOrderId());

        testData.submitOrder.setStatus(OrderStatus.EXECUTED);
        testData.submitOrder.setExternalSubscriptionId(savedOrder.getExternalSubscriptionId());
        OrderValidation.validateOrder(testData.submitOrder, savedOrder);
        updateDraftOrderIds(testData.submitOrder, savedOrder);
    }

    @Test
    @org.junit.jupiter.api.Order(7)
    public void testCreatedSubscription() {
        var subscription = subscriptionGetService.getSubscription(testData.submitOrder.getExternalSubscriptionId());
        SubscriptionValidation.validateEntity(testData.expectedSubscriptionV1.subscriptionEntity, subscription);
        SubscriptionValidation.validateCharges(testData.expectedSubscriptionV1.charges, subscription.getCharges());
    }

    private void updateDraftOrderIds(Order draftOrder, Order actualOrder) {
        draftOrder.setId(actualOrder.getId());
        draftOrder.setOrderId(actualOrder.getOrderId());
        draftOrder.setExternalSubscriptionId(actualOrder.getExternalSubscriptionId());
        draftOrder.setSubscriptionUuid(actualOrder.getSubscriptionUuid());
        draftOrder.setStatus(OrderStatus.DRAFT);

        Map<String, OrderLineItem> chargeIdLineIdMap = actualOrder
            .getLineItems()
            .stream()
            .collect(Collectors.toMap(OrderLineItem::getChargeId, Function.identity()));

        draftOrder
            .getLineItems()
            .forEach(li -> {
                li.setOrderId(actualOrder.getOrderId());
                li.setId(chargeIdLineIdMap.get(li.getChargeId()) == null ? null : chargeIdLineIdMap.get(li.getChargeId()).getId());
                li.setOrderLineId(chargeIdLineIdMap.get(li.getChargeId()) == null ? null : chargeIdLineIdMap.get(li.getChargeId()).getOrderLineId());
            });
    }
}
