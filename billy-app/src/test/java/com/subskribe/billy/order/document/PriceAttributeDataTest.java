package com.subskribe.billy.order.document;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class PriceAttributeDataTest {

    @Test
    void nameWithUnderscore() {
        PriceAttributeData priceAttributeData = new PriceAttributeData("name_with_underscore", "value");
        assertEquals("name with underscore", priceAttributeData.normalizedName());
    }
}
