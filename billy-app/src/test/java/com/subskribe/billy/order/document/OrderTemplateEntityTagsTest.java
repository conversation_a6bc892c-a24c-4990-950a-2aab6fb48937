package com.subskribe.billy.order.document;

import static org.mockito.Mockito.mock;

import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.document.service.TemplateLambdaInvoker;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.model.ImmutableEntity;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.template.services.TemplateScriptService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class OrderTemplateEntityTagsTest {

    private OrderTemplateToHtmlConverter converter;

    private final TemplateLambdaInvoker mockTemplateLambdaInvoker = mock(TemplateLambdaInvoker.class);

    private final TemplateScriptService mockTemplateScriptService = mock(TemplateScriptService.class);

    @BeforeEach
    public void beforeEach() {
        converter = new OrderTemplateToHtmlConverter(mockTemplateLambdaInvoker, mockTemplateScriptService);
    }

    // do not set entity and ensure that the template is rendered correctly
    @Test
    public void testNullEntity() {
        var orderDocumentJson = new OrderDocumentJson();
        var orderDetail = new OrderDetail();
        var entityTags = getEntityTagValuesForNullEntity();
        orderDetail.setId(entityTags.get("orderId"));
        orderDetail.setLineItems(List.of());
        orderDocumentJson.setOrderDetail(orderDetail);
        orderDocumentJson.setDocumentMasterTemplateIsFullHtml(true);
        orderDocumentJson.setDocumentMasterTemplateContent(generateTemplate(entityTags));
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));

        // generate html
        String generatedHtml = converter.generateHtml(orderDocumentJson);
        String expectedHtml = generateExpectedHtml(entityTags);
        Assertions.assertThat(generatedHtml).isEqualTo(expectedHtml);
    }

    // do not set entity contacts and ensure that the template is rendered correctly
    @Test
    public void testNullEntityContacts() {
        var orderDocumentJson = new OrderDocumentJson();
        Entity entity = EntityFixture.getEntityFixture();
        Map<String, String> entityTagValues = getEntityTagValuesForNullContacts();

        // Add document template
        orderDocumentJson.setOrderDetail(new OrderDetail());
        orderDocumentJson.setDocumentMasterTemplateIsFullHtml(true);
        orderDocumentJson.setDocumentMasterTemplateContent(generateTemplate(entityTagValues));
        orderDocumentJson.setEntity(entity);
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));

        // generate html
        String generatedHtml = converter.generateHtml(orderDocumentJson);
        String expectedHtml = generateExpectedHtml(entityTagValues);
        Assertions.assertThat(generatedHtml).isEqualTo(expectedHtml);
    }

    // verify all entity related tags
    @Test
    public void testEntityFields() {
        var orderDocumentJson = new OrderDocumentJson();
        Map<String, String> entityTagValues = getEntityTagValues();
        AccountContact companyContact = mockEntityContact(entityTagValues);
        Entity entity = ImmutableEntity.copyOf(EntityFixture.getEntityFixture()).withCompanyContact(companyContact);

        // Add document template
        orderDocumentJson.setOrderDetail(new OrderDetail());
        orderDocumentJson.setDocumentMasterTemplateIsFullHtml(true);
        orderDocumentJson.setDocumentMasterTemplateContent(generateTemplate(entityTagValues));
        orderDocumentJson.setEntity(entity);
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));

        // generate html
        String generatedHtml = converter.generateHtml(orderDocumentJson);
        String expectedHtml = generateExpectedHtml(entityTagValues);
        Assertions.assertThat(generatedHtml).isEqualTo(expectedHtml);
    }

    private AccountContact mockEntityContact(Map<String, String> entityTagValues) {
        AccountContact companyContact = new AccountContact();
        companyContact.setPhoneNumber(entityTagValues.get("entityPhoneNumber"));
        companyContact.setEmail(entityTagValues.get("entityEmail"));
        AccountAddress accountAddress = new AccountAddress();
        accountAddress.setStreetAddressLine1(entityTagValues.get("entityAddress.streetAddressLine1"));
        accountAddress.setStreetAddressLine2(entityTagValues.get("entityAddress.streetAddressLine2"));
        accountAddress.setZipcode(entityTagValues.get("entityAddress.zipcode"));
        accountAddress.setCity(entityTagValues.get("entityAddress.city"));
        accountAddress.setState(entityTagValues.get("entityAddress.state"));
        accountAddress.setCountry(entityTagValues.get("entityAddress.country"));
        companyContact.setAddress(accountAddress);
        return companyContact;
    }

    private Map<String, String> getEntityTagValuesForNullEntity() {
        Map<String, String> tagValues = new HashMap<>();
        tagValues.put("orderId", "12345");
        tagValues.put("entityName", "");
        tagValues.put("entityEmail", "");
        tagValues.put("entityPhoneNumber", "");
        tagValues.put("entityAddress.streetAddressLine1", "");
        tagValues.put("entityAddress.streetAddressLine2", "");
        tagValues.put("entityAddress.zipcode", "");
        tagValues.put("entityAddress.city", "");
        tagValues.put("entityAddress.state", "");
        tagValues.put("entityAddress.country", "");
        return tagValues;
    }

    private Map<String, String> getEntityTagValuesForNullContacts() {
        Map<String, String> tagValues = new HashMap<>();
        Entity entity = EntityFixture.getEntityFixture();
        tagValues.put("entityName", entity.getName());
        tagValues.put("entityEmail", "");
        tagValues.put("entityPhoneNumber", "");
        tagValues.put("entityAddress.streetAddressLine1", "");
        tagValues.put("entityAddress.streetAddressLine2", "");
        tagValues.put("entityAddress.zipcode", "");
        tagValues.put("entityAddress.city", "");
        tagValues.put("entityAddress.state", "");
        tagValues.put("entityAddress.country", "");
        return tagValues;
    }

    private Map<String, String> getEntityTagValues() {
        Map<String, String> tagValues = new HashMap<>();
        Entity entity = EntityFixture.getEntityFixture();
        tagValues.put("entityName", entity.getName());
        tagValues.put("entityEmail", "<EMAIL>");
        tagValues.put("entityPhoneNumber", "************");
        tagValues.put("entityAddress.streetAddressLine1", "123 Main St");
        tagValues.put("entityAddress.streetAddressLine2", "Suite 100");
        tagValues.put("entityAddress.zipcode", "12345");
        tagValues.put("entityAddress.city", "Anytown");
        tagValues.put("entityAddress.state", "CA");
        tagValues.put("entityAddress.country", "USA");
        return tagValues;
    }

    // generate template with one tag per line
    private String generateTemplate(Map<String, String> tagValues) {
        StringBuilder sb = new StringBuilder();
        for (var tagName : tagValues.keySet()) {
            sb.append(String.format("%s: {{%s}}%s", tagName, tagName, StringUtils.LF));
        }
        return sb.toString();
    }

    // build expected outcome using the tag values map
    private String generateExpectedHtml(Map<String, String> tagValues) {
        StringBuilder sb = new StringBuilder();
        for (var tagName : tagValues.keySet()) {
            String tagValue = tagValues.getOrDefault(tagName, StringUtils.EMPTY);
            sb.append(String.format("%s: %s%s", tagName, tagValue, StringUtils.LF));
        }
        return sb.toString();
    }
}
