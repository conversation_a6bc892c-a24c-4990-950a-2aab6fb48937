package com.subskribe.billy.order.quotebuilder;

import static org.junit.Assert.assertThrows;

import com.subskribe.billy.order.quotebuilder.model.ImmutableUserGuidedSellingConversationContext;
import com.subskribe.billy.order.quotebuilder.model.UserGuidedSellingConversationContext;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

@SuppressWarnings("DataFlowIssue")
public class UserGuidedSellingConversationContextTest {

    @Test
    public void shouldNotAllowNullValues() {
        // Test for null value for String fields
        assertThrows(NullPointerException.class, () ->
            ImmutableUserGuidedSellingConversationContext.builder()
                .slackUserId(null) // Null value for userId
                .channelId("C08G20S9RS8")
                .teamId("T017910MK7V")
                .tenantId("f35504c0-1b8b-4739-a466-caab3f6f6c7e")
                .threadTs("1742904615.924259")
                .processedEventIds(Set.of("Ev08KA2GAQ3V", "Ev08K0N50RML"))
                .token("VEGPfp6XuRVdefYiM6BBjS0r")
                .subskribeUserId("USER-3MN8P9B")
                .build()
        );

        // Test for null value for Set
        assertThrows(NullPointerException.class, () ->
            ImmutableUserGuidedSellingConversationContext.builder()
                .slackUserId("U04HGBUQL3X")
                .channelId("C08G20S9RS8")
                .teamId("T017910MK7V")
                .tenantId("f35504c0-1b8b-4739-a466-caab3f6f6c7e")
                .threadTs("1742904615.924259")
                .processedEventIds(null) // Null value for processedEventIds
                .token("VEGPfp6XuRVdefYiM6BBjS0r")
                .subskribeUserId("USER-3MN8P9B")
                .build()
        );
    }

    @Test
    public void shouldCreateContextSuccessfully() {
        String slackUserId = "U04HGBUQL3X";
        String channelId = "C08G20S9RS8";
        String teamId = "T017910MK7V";
        String tenantId = "f35504c0-1b8b-4739-a466-caab3f6f6c7e";
        String threadTs = "1742904615.924259";
        Set<String> processedEventIds = Set.of("Ev08KA2GAQ3V", "Ev08K0N50RML");
        String token = "VEGPfp6XuRVdefYiM6BBjS0q";
        String subskribeUserId = "USER-3MN8P9B";

        UserGuidedSellingConversationContext userGuidedSellingConversationContext = ImmutableUserGuidedSellingConversationContext.builder()
            .slackUserId(slackUserId)
            .channelId(channelId)
            .teamId(teamId)
            .tenantId(tenantId)
            .threadTs(threadTs)
            .processedEventIds(processedEventIds)
            .token(token)
            .subskribeUserId(subskribeUserId)
            .build();

        Assertions.assertNotNull(userGuidedSellingConversationContext);
        Assertions.assertEquals(slackUserId, userGuidedSellingConversationContext.getSlackUserId());
        Assertions.assertEquals(channelId, userGuidedSellingConversationContext.getChannelId());
        Assertions.assertEquals(teamId, userGuidedSellingConversationContext.getTeamId());
        Assertions.assertEquals(tenantId, userGuidedSellingConversationContext.getTenantId());
        Assertions.assertEquals(threadTs, userGuidedSellingConversationContext.getThreadTs());
        Assertions.assertEquals(processedEventIds, userGuidedSellingConversationContext.getProcessedEventIds());
        Assertions.assertEquals(token, userGuidedSellingConversationContext.getToken());
        Assertions.assertEquals(subskribeUserId, userGuidedSellingConversationContext.getSubskribeUserId());
    }
}
