package com.subskribe.billy.order;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class OrderLineUtilsTest {

    private static final long ORDER_START_DATE = 1640995200; // 2022-01-01 00:00:00 UTC

    @Test
    public void getSortedRampOrderLinesWithPlanAndCharge() {
        String rampPlanId = "ramp plan";
        String rampChargeId = "ramp charge";
        int rampItemSize = 3;
        List<OrderLineItem> lineItems = generateRandomRampLineItems(rampItemSize, rampPlanId, rampChargeId, null);
        OrderLineItem targetLineItem = lineItems.get(1); // target 2nd item
        lineItems.addAll(generateRandomNonRampLineItems(3));

        Order order = new Order();
        order.setLineItemsNetEffect(lineItems);

        List<OrderLineItem> sortedRampItems = OrderLineUtils.getSortedRampItems(order, targetLineItem);

        assertEquals(rampItemSize, sortedRampItems.size());

        assertLineItemsOrdered(sortedRampItems);
    }

    @Test
    public void getSortedRampOrderLinesWithGroupId() {
        int rampItemSize = 3;
        List<OrderLineItem> lineItems = generateRandomRampLineItems(rampItemSize, "planId", "chargeId", "groupId");
        OrderLineItem targetLineItem = lineItems.get(1); // target 2nd item
        lineItems.addAll(generateRandomNonRampLineItems(3));

        Order order = new Order();
        order.setLineItemsNetEffect(lineItems);

        List<OrderLineItem> sortedRampItems = OrderLineUtils.getSortedRampItems(order, targetLineItem);

        assertEquals(rampItemSize, sortedRampItems.size());

        assertLineItemsOrdered(sortedRampItems);
    }

    @Test
    public void getPreviousRampItem() {
        String rampPlanId = "ramp plan";
        String rampChargeId = "ramp charge";
        int rampItemSize = 3;
        List<OrderLineItem> lineItems = generateRandomRampLineItems(rampItemSize, rampPlanId, rampChargeId, null);
        OrderLineItem expectedLineItem = lineItems.get(0);
        OrderLineItem targetLineItem = lineItems.get(1); // target 2nd item
        lineItems.addAll(generateRandomNonRampLineItems(3));

        Order order = new Order();
        order.setLineItemsNetEffect(lineItems);

        Optional<OrderLineItem> previousLineItem = OrderLineUtils.getPreviousOrderLineItemInRamp(order, targetLineItem);

        assertTrue(previousLineItem.isPresent());
        assertEquals(expectedLineItem.getOrderLineId(), previousLineItem.get().getOrderLineId());
    }

    @Test
    public void getPreviousRampItemOfFirstRampItem() {
        String rampPlanId = "ramp plan";
        String rampChargeId = "ramp charge";
        int rampItemSize = 3;
        List<OrderLineItem> lineItems = generateRandomRampLineItems(rampItemSize, rampPlanId, rampChargeId, null);
        OrderLineItem targetLineItem = lineItems.get(0); // target first item
        lineItems.addAll(generateRandomNonRampLineItems(3));

        Order order = new Order();
        order.setLineItemsNetEffect(lineItems);

        Optional<OrderLineItem> previousLineItem = OrderLineUtils.getPreviousOrderLineItemInRamp(order, targetLineItem);

        assertTrue(previousLineItem.isEmpty());
    }

    @Test
    public void getPreviousRampItemOfNonRampItem() {
        String rampPlanId = "ramp plan";
        String rampChargeId = "ramp charge";
        int rampItemSize = 3;
        List<OrderLineItem> lineItems = generateRandomRampLineItems(rampItemSize, rampPlanId, rampChargeId, null);
        lineItems.addAll(generateRandomNonRampLineItems(3));
        OrderLineItem targetLineItem = lineItems.get(5); // get non ramped item

        Order order = new Order();
        order.setLineItemsNetEffect(lineItems);

        Optional<OrderLineItem> previousLineItem = OrderLineUtils.getPreviousOrderLineItemInRamp(order, targetLineItem);

        assertTrue(previousLineItem.isEmpty());
    }

    private static void assertLineItemsOrdered(List<OrderLineItem> sortedRampItems) {
        Iterator<OrderLineItem> iterator = sortedRampItems.iterator();

        OrderLineItem current, previous = iterator.next();

        while (iterator.hasNext()) {
            current = iterator.next();
            assertTrue(current.getEffectiveDate().isAfter(previous.getEffectiveDate()));
            previous = current;
        }
    }

    private List<OrderLineItem> generateRandomRampLineItems(int size, String planId, String chargeId, String subscriptionChargeGroupId) {
        List<OrderLineItem> orderLineItems = new ArrayList<>(size);
        long effectiveDate = ORDER_START_DATE;
        for (int i = 0; i < size; i++) {
            OrderLineItem orderLineItem = new OrderLineItem();
            orderLineItem.setOrderLineId(UUID.randomUUID().toString());
            orderLineItem.setEffectiveDate(Instant.ofEpochSecond(effectiveDate));
            orderLineItem.setPlanId(planId);
            orderLineItem.setChargeId(chargeId);
            orderLineItem.setSubscriptionChargeGroupId(subscriptionChargeGroupId);
            orderLineItem.setIsRamp(true);

            effectiveDate += new Random().nextInt(30000000);
            orderLineItems.add(orderLineItem);
        }

        return orderLineItems;
    }

    private List<OrderLineItem> generateRandomNonRampLineItems(int number) {
        List<OrderLineItem> orderLineItems = new ArrayList<>(number);
        long effectiveDate = ORDER_START_DATE;
        for (int i = 0; i < number; i++) {
            OrderLineItem orderLineItem = new OrderLineItem();
            orderLineItem.setOrderLineId(UUID.randomUUID().toString());
            orderLineItem.setEffectiveDate(Instant.ofEpochSecond(effectiveDate));
            orderLineItem.setPlanId(UUID.randomUUID().toString());
            orderLineItem.setChargeId(UUID.randomUUID().toString());
            orderLineItem.setSubscriptionChargeGroupId(null);
            orderLineItem.setIsRamp(true);

            effectiveDate += new Random().nextInt(30000000);
            orderLineItems.add(orderLineItem);
        }

        return orderLineItems;
    }
}
