package com.subskribe.billy.order.document;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class OrderTemplateBundlesByPlanCustomFieldsTest {

    Instant startDate = Instant.parse("2020-01-01T00:00:00Z");
    Instant endDate = Instant.parse("2021-01-01T00:00:00Z");
    DocumentRenderFormatter formatter = new DocumentRenderFormatter(TimeZone.getTimeZone("UTC"));
    String currencyCode = "USD";

    @Test
    public void buildLineItemBundlesWithoutType() {
        String typeKey = UUID.randomUUID().toString();

        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), List.of(), startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), List.of(), startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), List.of(), startDate.getEpochSecond(), endDate.getEpochSecond())
        );

        OrderTemplateBundles bundles = OrderTemplateBundles.buildItemBundles(null, typeKey, lineItems, formatter, currencyCode);

        assertEquals(0, bundles.getBundles().size());
        assertEquals(3, bundles.getUnbundledItems().size());
        assertEquals("$300.00", bundles.getAmount());
    }

    @Test
    public void buildLineItemBundlesDifferentDatesWithoutType() {
        String typeKey = UUID.randomUUID().toString();

        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), List.of(), startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithPlanCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                List.of(),
                startDate.plus(50, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            ),
            getLineItemWithPlanCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                List.of(),
                startDate.plus(100, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            )
        );

        OrderTemplateBundles bundles = OrderTemplateBundles.buildItemBundlesByRampSegments(null, typeKey, lineItems, formatter, currencyCode);

        assertEquals(0, bundles.getBundles().size());
        assertEquals(3, bundles.getUnbundledItems().size());
        assertEquals("$300.00", bundles.getAmount());
    }

    @Test
    public void buildItemBundlesByPlanCustomField() {
        List<String> options = List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        String typeKey = UUID.randomUUID().toString();

        List<OrderTemplateLineItem> lineItems = List.of(
            // items in first bundle
            getLineItemWithPlanCustomField(typeKey, options.get(0), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithPlanCustomField(typeKey, options.get(0), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            // items in second bundle
            getLineItemWithPlanCustomField(typeKey, options.get(1), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            // items not in bundle
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), options, startDate.getEpochSecond(), endDate.getEpochSecond())
        );

        OrderTemplateBundles bundles = OrderTemplateBundles.buildItemBundles(
            OrderTemplateBundles.OrderTemplateBundleBy.PLAN_CUSTOM_FIELD,
            typeKey,
            lineItems,
            formatter,
            currencyCode
        );

        assertEquals(2, bundles.getBundles().size());
        assertEquals(2, bundles.getBundles().get(0).getItems().size());
        assertEquals(options.get(0), bundles.getBundles().get(0).getBundleName());
        assertEquals(1, bundles.getBundles().get(1).getItems().size());
        assertEquals(options.get(1), bundles.getBundles().get(1).getBundleName());
        assertEquals(3, bundles.getUnbundledItems().size());
        assertEquals("$600.00", bundles.getAmount());
    }

    @Test
    public void buildItemBundlesByPlanCustomFieldWithDifferentDates() {
        List<String> options = List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        String typeKey = UUID.randomUUID().toString();

        List<OrderTemplateLineItem> lineItems = List.of(
            // items in first bundle
            getLineItemWithPlanCustomField(typeKey, options.get(0), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            // items in second bundle
            getLineItemWithPlanCustomField(
                typeKey,
                options.get(0),
                options,
                startDate.plus(100, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            ),
            // items in third bundle
            getLineItemWithPlanCustomField(
                typeKey,
                options.get(1),
                options,
                startDate.plus(100, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            ),
            // items not in bundle
            getLineItemWithPlanCustomField(typeKey, UUID.randomUUID().toString(), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithPlanCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                options,
                startDate.plus(60, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            ),
            getLineItemWithPlanCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                options,
                startDate.plus(100, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            )
        );

        OrderTemplateBundles bundles = OrderTemplateBundles.buildItemBundlesByRampSegments(
            OrderTemplateBundles.OrderTemplateBundleBy.PLAN_CUSTOM_FIELD,
            typeKey,
            lineItems,
            formatter,
            currencyCode
        );

        assertEquals(3, bundles.getBundles().size());
        assertEquals(1, bundles.getBundles().get(0).getItems().size());
        assertEquals(options.get(0), bundles.getBundles().get(0).getBundleName());
        assertEquals(1, bundles.getBundles().get(1).getItems().size());
        assertEquals(options.get(0), bundles.getBundles().get(1).getBundleName());
        assertEquals(1, bundles.getBundles().get(2).getItems().size());
        assertEquals(options.get(1), bundles.getBundles().get(2).getBundleName());
        assertEquals(3, bundles.getUnbundledItems().size());
        assertEquals("$600.00", bundles.getAmount());
    }

    private OrderTemplateLineItem getLineItemWithPlanCustomField(
        String customFieldName,
        String selection,
        List<String> options,
        Long startDate,
        Long endDate
    ) {
        OrderLineItemDetail orderLineItemDetail = new OrderLineItemDetail();
        orderLineItemDetail.setEffectiveDate(startDate);
        orderLineItemDetail.setEndDate(endDate);
        orderLineItemDetail.setAmount(BigDecimal.valueOf(100));

        PlanJson plan = new PlanJson();
        plan.setCustomFields(
            Map.of(
                customFieldName,
                new CustomFieldValue(
                    CustomFieldType.PICKLIST,
                    customFieldName,
                    customFieldName,
                    selection,
                    List.of(selection),
                    options,
                    false,
                    null,
                    null
                )
            )
        );
        orderLineItemDetail.setPlan(plan);
        return new OrderTemplateLineItem(orderLineItemDetail, null, null, null, null, null, formatter, "USD", null, null);
    }
}
