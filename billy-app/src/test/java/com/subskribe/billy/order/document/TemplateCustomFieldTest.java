package com.subskribe.billy.order.document;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldSource;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class TemplateCustomFieldTest {

    private final String CUSTOM_FIELD_NAME = UUID.randomUUID().toString();

    @Test
    public void customFieldHasTruthyValue() {
        CustomField customField = new CustomField(Map.of(UUID.randomUUID().toString(), getCustomFieldValue("yes")));
        Map<String, TemplateCustomField> customFieldsMap = TemplateCustomField.getCustomFieldsMap(customField);
        Assertions.assertTrue(customFieldsMap.get(CUSTOM_FIELD_NAME).isTrue());

        customField = new CustomField(Map.of(UUID.randomUUID().toString(), getCustomFieldValue("y")));
        customFieldsMap = TemplateCustomField.getCustomFieldsMap(customField);
        Assertions.assertTrue(customFieldsMap.get(CUSTOM_FIELD_NAME).isTrue());

        customField = new CustomField(Map.of(UUID.randomUUID().toString(), getCustomFieldValue("true")));
        customFieldsMap = TemplateCustomField.getCustomFieldsMap(customField);
        Assertions.assertTrue(customFieldsMap.get(CUSTOM_FIELD_NAME).isTrue());

        customField = new CustomField(Map.of(UUID.randomUUID().toString(), getCustomFieldValue("NO")));
        customFieldsMap = TemplateCustomField.getCustomFieldsMap(customField);
        Assertions.assertFalse(customFieldsMap.get(CUSTOM_FIELD_NAME).isTrue());
    }

    @Test
    public void customFieldHasValue() {
        String cfValue = UUID.randomUUID().toString();
        CustomField customField = new CustomField(Map.of(UUID.randomUUID().toString(), getCustomFieldValue(cfValue)));
        Map<String, TemplateCustomField> customFieldsMap = TemplateCustomField.getCustomFieldsMap(customField);

        Assertions.assertTrue(customFieldsMap.get(CUSTOM_FIELD_NAME).hasValue().get(cfValue));
        Assertions.assertNull(customFieldsMap.get(CUSTOM_FIELD_NAME).hasValue().get(UUID.randomUUID().toString()));
    }

    @Test
    public void customFieldHasSelection() {
        String cfSelection = UUID.randomUUID().toString();
        CustomField customField = new CustomField(
            Map.of(UUID.randomUUID().toString(), getCustomFieldValue(CustomFieldType.PICKLIST, null, List.of(cfSelection)))
        );
        Map<String, TemplateCustomField> customFieldsMap = TemplateCustomField.getCustomFieldsMap(customField);

        Assertions.assertTrue(customFieldsMap.get(CUSTOM_FIELD_NAME).hasSelection().get(cfSelection));
        Assertions.assertNull(customFieldsMap.get(CUSTOM_FIELD_NAME).hasSelection().get(UUID.randomUUID().toString()));
    }

    @Test
    public void customFieldHasAnySelection() {
        String cfSelection1 = UUID.randomUUID().toString();
        String cfSelection2 = UUID.randomUUID().toString();
        CustomField customField = new CustomField(
            Map.of(UUID.randomUUID().toString(), getCustomFieldValue(CustomFieldType.MULTISELECT_PICKLIST, null, List.of(cfSelection1, cfSelection2)))
        );
        Map<String, TemplateCustomField> customFieldsMap = TemplateCustomField.getCustomFieldsMap(customField);

        Assertions.assertTrue(customFieldsMap.get(CUSTOM_FIELD_NAME).hasSelection().get(cfSelection1));
        Assertions.assertTrue(customFieldsMap.get(CUSTOM_FIELD_NAME).hasSelection().get(cfSelection2));
        Assertions.assertNull(customFieldsMap.get(CUSTOM_FIELD_NAME).hasSelection().get(UUID.randomUUID().toString()));
    }

    private CustomFieldValue getCustomFieldValue(String value) {
        return getCustomFieldValue(CustomFieldType.STRING, value, List.of());
    }

    private CustomFieldValue getCustomFieldValue(CustomFieldType type, String value, List<String> selections) {
        return new CustomFieldValue(type, CUSTOM_FIELD_NAME, CUSTOM_FIELD_NAME, value, selections, selections, false, CustomFieldSource.USER, null);
    }
}
