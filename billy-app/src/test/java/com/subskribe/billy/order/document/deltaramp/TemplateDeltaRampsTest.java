package com.subskribe.billy.order.document.deltaramp;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.TimeZone;
import org.junit.jupiter.api.Test;

class TemplateDeltaRampsTest {

    private final TimeZone timeZone = TimeZone.getTimeZone("UTC");
    private final ZonedDateTime JAN_1_2024 = ZonedDateTime.of(2024, 1, 1, 0, 0, 0, 0, timeZone.toZoneId());

    @Test
    void testRampDeltaItemsWithDiscount() {
        // unit price of $100 with 10% discount
        BigDecimal listUnitPrice = BigDecimal.valueOf(100);
        BigDecimal discountRatio = BigDecimal.valueOf(0.1);
        String chargeId = "chargeId";

        // increasing by 5 quantity per ramp
        var item10 = getLineItemDetail(JAN_1_2024.toInstant(), JAN_1_2024.plusYears(1).toInstant(), chargeId, listUnitPrice, discountRatio, 10);
        var item15 = getLineItemDetail(
            JAN_1_2024.plusYears(1).toInstant(),
            JAN_1_2024.plusYears(2).toInstant(),
            chargeId,
            listUnitPrice,
            discountRatio,
            15
        );
        var item20 = getLineItemDetail(
            JAN_1_2024.plusYears(2).toInstant(),
            JAN_1_2024.plusYears(3).toInstant(),
            chargeId,
            listUnitPrice,
            discountRatio,
            20
        );
        List<OrderLineItemDetail> lineItems = List.of(item10, item15, item20);

        TemplateDeltaRamps templateDeltaRamps = new TemplateDeltaRamps(
            lineItems,
            JAN_1_2024.plusYears(3).toInstant().getEpochSecond(),
            new DocumentRenderFormatter(timeZone),
            "USD",
            timeZone,
            true
        );

        List<DeltaRampDateGroup> deltaRampDateGroups = templateDeltaRamps.getRampDeltaItemDateGroups();

        assertEquals(3, deltaRampDateGroups.size());
        assertEquals(10, deltaRampDateGroups.get(0).getRampDeltaItems().get(0).getDeltaQuantity());
        assertEquals(5, deltaRampDateGroups.get(1).getRampDeltaItems().get(0).getDeltaQuantity());
        assertEquals(5, deltaRampDateGroups.get(2).getRampDeltaItems().get(0).getDeltaQuantity());

        assertEquals(new BigDecimal("2700.00"), deltaRampDateGroups.get(0).getRampDeltaItems().get(0).getUnformattedTotal()); // 10 x 100 x (1 - 0.1) x 3 = 2700
        assertEquals(new BigDecimal("900.00"), deltaRampDateGroups.get(1).getRampDeltaItems().get(0).getUnformattedTotal()); // 5 x 100 x (1 - 0.1) x 2 = 900
        assertEquals(new BigDecimal("450.00"), deltaRampDateGroups.get(2).getRampDeltaItems().get(0).getUnformattedTotal()); // 5 x 100 x (1 - 0.1) x 1 = 450

        assertEquals(3, deltaRampDateGroups.get(0).getRampDeltaItems().get(0).getPeriod().getYears());
        assertEquals(2, deltaRampDateGroups.get(1).getRampDeltaItems().get(0).getPeriod().getYears());
        assertEquals(1, deltaRampDateGroups.get(2).getRampDeltaItems().get(0).getPeriod().getYears());
    }

    @Test
    void testRampDeltaItemsWithVaryingDiscount() {
        // unit price of $100 with 10% discount
        BigDecimal listUnitPrice = BigDecimal.valueOf(100);
        BigDecimal discountRatio1 = BigDecimal.valueOf(0.5);
        BigDecimal discountRatio2 = BigDecimal.valueOf(0.1);
        String chargeId = "chargeId";

        // increasing by 5 quantity per ramp
        var item10 = getLineItemDetail(JAN_1_2024.toInstant(), JAN_1_2024.plusYears(1).toInstant(), chargeId, listUnitPrice, discountRatio1, 10);
        var item15 = getLineItemDetail(
            JAN_1_2024.plusYears(1).toInstant(),
            JAN_1_2024.plusYears(2).toInstant(),
            chargeId,
            listUnitPrice,
            discountRatio2,
            15
        );
        var item20 = getLineItemDetail(
            JAN_1_2024.plusYears(2).toInstant(),
            JAN_1_2024.plusYears(3).toInstant(),
            chargeId,
            listUnitPrice,
            discountRatio2,
            20
        );
        List<OrderLineItemDetail> lineItems = List.of(item10, item15, item20);

        TemplateDeltaRamps templateDeltaRamps = new TemplateDeltaRamps(
            lineItems,
            JAN_1_2024.plusYears(3).toInstant().getEpochSecond(),
            new DocumentRenderFormatter(timeZone),
            "USD",
            timeZone,
            true
        );

        List<DeltaRampDateGroup> deltaRampDateGroups = templateDeltaRamps.getRampDeltaItemDateGroups();

        assertEquals(3, deltaRampDateGroups.size());
        assertEquals(10, deltaRampDateGroups.get(0).getRampDeltaItems().get(0).getDeltaQuantity());
        assertEquals(15, deltaRampDateGroups.get(1).getRampDeltaItems().get(0).getDeltaQuantity());
        assertEquals(5, deltaRampDateGroups.get(2).getRampDeltaItems().get(0).getDeltaQuantity());

        assertEquals(new BigDecimal("500.00"), deltaRampDateGroups.get(0).getRampDeltaItems().get(0).getUnformattedTotal()); // 10 x 100 x (1 - 0.5) x 1 = 2700
        assertEquals(new BigDecimal("2700.00"), deltaRampDateGroups.get(1).getRampDeltaItems().get(0).getUnformattedTotal()); // 15 x 100 x (1 - 0.1) x 2 = 900
        assertEquals(new BigDecimal("450.00"), deltaRampDateGroups.get(2).getRampDeltaItems().get(0).getUnformattedTotal()); // 5 x 100 x (1 - 0.1) x 1 = 450

        assertEquals(1, deltaRampDateGroups.get(0).getRampDeltaItems().get(0).getPeriod().getYears());
        assertEquals(2, deltaRampDateGroups.get(1).getRampDeltaItems().get(0).getPeriod().getYears());
        assertEquals(1, deltaRampDateGroups.get(2).getRampDeltaItems().get(0).getPeriod().getYears());
    }

    @Test
    void testRampDeltaItemsWithVaryingDiscountSameQuantity() {
        // unit price of $100 with 10% discount
        BigDecimal listUnitPrice = BigDecimal.valueOf(100);
        BigDecimal discountRatio1 = BigDecimal.valueOf(0.5);
        BigDecimal discountRatio2 = BigDecimal.valueOf(0.1);
        String chargeId = "chargeId";

        var item1 = getLineItemDetail(JAN_1_2024.toInstant(), JAN_1_2024.plusYears(1).toInstant(), chargeId, listUnitPrice, discountRatio1, 1);
        var item2 = getLineItemDetail(
            JAN_1_2024.plusYears(1).toInstant(),
            JAN_1_2024.plusYears(2).toInstant(),
            chargeId,
            listUnitPrice,
            discountRatio2,
            1
        );
        var item3 = getLineItemDetail(
            JAN_1_2024.plusYears(2).toInstant(),
            JAN_1_2024.plusYears(3).toInstant(),
            chargeId,
            listUnitPrice,
            discountRatio2,
            1
        );
        List<OrderLineItemDetail> lineItems = List.of(item1, item2, item3);

        TemplateDeltaRamps templateDeltaRamps = new TemplateDeltaRamps(
            lineItems,
            JAN_1_2024.plusYears(3).toInstant().getEpochSecond(),
            new DocumentRenderFormatter(timeZone),
            "USD",
            timeZone,
            true
        );

        List<DeltaRampDateGroup> deltaRampDateGroups = templateDeltaRamps.getRampDeltaItemDateGroups();

        assertEquals(2, deltaRampDateGroups.size());
        assertEquals(1, deltaRampDateGroups.get(0).getRampDeltaItems().get(0).getDeltaQuantity());
        assertEquals(1, deltaRampDateGroups.get(1).getRampDeltaItems().get(0).getDeltaQuantity());

        assertEquals(new BigDecimal("50.00"), deltaRampDateGroups.get(0).getRampDeltaItems().get(0).getUnformattedTotal()); // 1 x 100 x (1 - 0.5) x 1 = 2700
        assertEquals(new BigDecimal("180.00"), deltaRampDateGroups.get(1).getRampDeltaItems().get(0).getUnformattedTotal()); // 1 x 100 x (1 - 0.1) x 2 = 2700

        assertEquals(1, deltaRampDateGroups.get(0).getRampDeltaItems().get(0).getPeriod().getYears());
        assertEquals(2, deltaRampDateGroups.get(1).getRampDeltaItems().get(0).getPeriod().getYears());
    }

    private OrderLineItemDetail getLineItemDetail(
        Instant effectiveDate,
        Instant endDate,
        String chargeId,
        BigDecimal listUnitPrice,
        BigDecimal discountRatio,
        long quantity
    ) {
        OrderLineItemDetail item = new OrderLineItemDetail();
        item.setIsRamp(true);
        item.setEffectiveDate(effectiveDate.getEpochSecond());
        item.setEndDate(endDate.getEpochSecond());

        BigDecimal listAmount = listUnitPrice.multiply(BigDecimal.valueOf(quantity));
        BigDecimal amount = listAmount.multiply(BigDecimal.ONE.subtract(discountRatio));
        item.setAmount(amount);
        item.setListAmount(listAmount);
        item.setQuantity(quantity);
        item.setSellUnitPrice(listUnitPrice.multiply(BigDecimal.ONE.subtract(discountRatio)));

        ChargeJson charge = new ChargeJson();
        charge.setId(chargeId);
        charge.setType(ChargeType.RECURRING);
        item.setCharge(charge);

        return item;
    }
}
