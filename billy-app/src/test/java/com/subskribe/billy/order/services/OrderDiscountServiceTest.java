package com.subskribe.billy.order.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class OrderDiscountServiceTest {

    private Order order;
    private OrderLineItem orderLineItem;
    private Map<String, Charge> chargeMap;
    private Charge usageCharge;
    private Charge recurringCharge;

    @BeforeEach
    void setUp() {
        order = new Order();
        orderLineItem = new OrderLineItem();
        orderLineItem.setId(UUID.randomUUID());
        orderLineItem.setChargeId("charge-1");

        // Create usage charge
        usageCharge = new Charge();
        usageCharge.setChargeId("charge-1");
        usageCharge.setType(ChargeType.USAGE);
        usageCharge.setIsDiscount(false); // Not a discount charge

        // Create recurring charge for comparison
        recurringCharge = new Charge();
        recurringCharge.setChargeId("charge-2");
        recurringCharge.setType(ChargeType.RECURRING);
        recurringCharge.setIsDiscount(false); // Not a discount charge

        chargeMap = new HashMap<>();
        chargeMap.put("charge-1", usageCharge);
        chargeMap.put("charge-2", recurringCharge);
    }

    @Test
    void testNoDiscounts_shouldReturnZero() {
        // Given: No discounts
        orderLineItem.setDiscounts(Collections.emptyList());
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then
        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    void testSingleDiscountFromDiscounts_shouldCalculateCorrectly() {
        // Given: Single 10% discount
        List<DiscountDetail> discounts = new ArrayList<>();
        DiscountDetail discount = new DiscountDetail();
        discount.setPercent(new BigDecimal("0.10")); // 10%
        discounts.add(discount);
        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then
        assertEquals(new BigDecimal("10.00"), result);
    }

    @Test
    void testSingleDiscountFromPredefinedDiscounts_shouldCalculateCorrectly() {
        // Given: Single 15% predefined discount
        List<TenantDiscountLineItem> predefinedDiscounts = new ArrayList<>();
        TenantDiscountLineItem tenantDiscount = new TenantDiscountLineItem();
        tenantDiscount.setPercent(new BigDecimal("0.15")); // 15%
        predefinedDiscounts.add(tenantDiscount);
        orderLineItem.setDiscounts(Collections.emptyList());
        orderLineItem.setPredefinedDiscounts(predefinedDiscounts);

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then
        assertEquals(new BigDecimal("15.00"), result);
    }

    @Test
    void testTwoDiscountsFromSameSource_shouldUseCompoundingLogic() {
        // Given: Two discounts of 10% and 20% from discounts source
        // Expected: 10% + (90% * 20%) = 10% + 18% = 28%
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail discount1 = new DiscountDetail();
        discount1.setPercent(new BigDecimal("0.10")); // 10%
        discounts.add(discount1);

        DiscountDetail discount2 = new DiscountDetail();
        discount2.setPercent(new BigDecimal("0.20")); // 20%
        discounts.add(discount2);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then
        assertEquals(new BigDecimal("28.00"), result);
    }

    @Test
    void testTwoDiscountsFromPredefinedDiscounts_shouldUseCompoundingLogic() {
        // Given: Two predefined discounts of 10% and 20%
        // Expected: 10% + (90% * 20%) = 10% + 18% = 28%
        List<TenantDiscountLineItem> predefinedDiscounts = new ArrayList<>();

        TenantDiscountLineItem discount1 = new TenantDiscountLineItem();
        discount1.setPercent(new BigDecimal("0.10")); // 10%
        predefinedDiscounts.add(discount1);

        TenantDiscountLineItem discount2 = new TenantDiscountLineItem();
        discount2.setPercent(new BigDecimal("0.20")); // 20%
        predefinedDiscounts.add(discount2);

        orderLineItem.setDiscounts(Collections.emptyList());
        orderLineItem.setPredefinedDiscounts(predefinedDiscounts);

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then
        assertEquals(new BigDecimal("28.00"), result);
    }

    @Test
    void testMixedDiscountSources_shouldUseCompoundingLogic() {
        // Given: One discount (15%) and one predefined discount (25%)
        // Expected: 15% + (85% * 25%) = 15% + 21.25% = 36.25%
        List<DiscountDetail> discounts = new ArrayList<>();
        DiscountDetail discount = new DiscountDetail();
        discount.setPercent(new BigDecimal("0.15")); // 15%
        discounts.add(discount);

        List<TenantDiscountLineItem> predefinedDiscounts = new ArrayList<>();
        TenantDiscountLineItem tenantDiscount = new TenantDiscountLineItem();
        tenantDiscount.setPercent(new BigDecimal("0.25")); // 25%
        predefinedDiscounts.add(tenantDiscount);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(predefinedDiscounts);

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then
        assertEquals(new BigDecimal("36.25"), result.setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    void testThreeDiscounts_shouldUseSequentialCompounding() {
        // Given: Three discounts: 10%, 20%, 30%
        // Expected:
        // Step 1: 10%
        // Step 2: 10% + (90% * 20%) = 10% + 18% = 28%
        // Step 3: 28% + (72% * 30%) = 28% + 21.6% = 49.6%
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail discount1 = new DiscountDetail();
        discount1.setPercent(new BigDecimal("0.10")); // 10%
        discounts.add(discount1);

        DiscountDetail discount2 = new DiscountDetail();
        discount2.setPercent(new BigDecimal("0.20")); // 20%
        discounts.add(discount2);

        List<TenantDiscountLineItem> predefinedDiscounts = new ArrayList<>();
        TenantDiscountLineItem discount3 = new TenantDiscountLineItem();
        discount3.setPercent(new BigDecimal("0.30")); // 30%
        predefinedDiscounts.add(discount3);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(predefinedDiscounts);

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then
        assertEquals(new BigDecimal("49.60"), result.setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    void testZeroDiscounts_shouldBeFiltered() {
        // Given: Discounts with 0% should be filtered out
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail zeroDiscount = new DiscountDetail();
        zeroDiscount.setPercent(BigDecimal.ZERO);
        discounts.add(zeroDiscount);

        DiscountDetail validDiscount = new DiscountDetail();
        validDiscount.setPercent(new BigDecimal("0.15")); // 15%
        discounts.add(validDiscount);

        List<TenantDiscountLineItem> predefinedDiscounts = new ArrayList<>();
        TenantDiscountLineItem zeroPredefinedDiscount = new TenantDiscountLineItem();
        zeroPredefinedDiscount.setPercent(BigDecimal.ZERO);
        predefinedDiscounts.add(zeroPredefinedDiscount);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(predefinedDiscounts);

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then
        assertEquals(new BigDecimal("15.00"), result);
    }

    @Test
    void testNullDiscounts_shouldBeFiltered() {
        // Given: Discounts with null percentages should be filtered out
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail nullDiscount = new DiscountDetail();
        nullDiscount.setPercent(null);
        discounts.add(nullDiscount);

        DiscountDetail validDiscount = new DiscountDetail();
        validDiscount.setPercent(new BigDecimal("0.20")); // 20%
        discounts.add(validDiscount);

        List<TenantDiscountLineItem> predefinedDiscounts = new ArrayList<>();
        TenantDiscountLineItem nullPredefinedDiscount = new TenantDiscountLineItem();
        nullPredefinedDiscount.setPercent(null);
        predefinedDiscounts.add(nullPredefinedDiscount);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(predefinedDiscounts);

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then
        assertEquals(new BigDecimal("20.00"), result);
    }

    @Test
    void testNonUsageCharge_shouldUseExistingLogic() {
        // Given: Non-usage charge should use existing calculateOrderLineNetDiscountPercent logic
        orderLineItem.setChargeId("charge-2"); // recurring charge

        // Set required fields for the existing logic to work properly
        orderLineItem.setListAmount(new BigDecimal("100.00"));
        orderLineItem.setAmount(new BigDecimal("90.00")); // 10% discount applied

        List<DiscountDetail> discounts = new ArrayList<>();
        DiscountDetail discount = new DiscountDetail();
        discount.setPercent(new BigDecimal("0.10")); // 10%
        discounts.add(discount);
        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When: This should delegate to the existing method, not our new logic
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: The existing logic calculates discount percentage based on list amount vs actual amount
        // With listAmount=100 and amount=90, discount should be 10%
        assertEquals(new BigDecimal("10.00"), result);
    }

    @Test
    void testChargeNotFound_shouldThrowException() {
        // Given: Order line item with charge not in charge map
        orderLineItem.setChargeId("non-existent-charge");

        // When & Then
        assertThrows(ConflictingStateException.class, () -> OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap)
        );
    }

    @Test
    void testHighPrecisionCalculation_shouldRoundCorrectly() {
        // Given: Discounts that result in high precision calculations
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail discount1 = new DiscountDetail();
        discount1.setPercent(new BigDecimal("0.333333333")); // 1/3
        discounts.add(discount1);

        DiscountDetail discount2 = new DiscountDetail();
        discount2.setPercent(new BigDecimal("0.166666667")); // 1/6
        discounts.add(discount2);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: Should be rounded to 2 decimal places
        // Expected: 33.33% + (66.67% * 16.67%) = 33.33% + 11.11% = 44.44%
        assertEquals(new BigDecimal("44.44"), result.setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    void testExampleFromRequirement_shouldCalculateCorrectly() {
        // Given: The specific example from the requirement - 10% and 20% discounts
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail discount1 = new DiscountDetail();
        discount1.setPercent(new BigDecimal("0.10")); // 10%
        discounts.add(discount1);

        DiscountDetail discount2 = new DiscountDetail();
        discount2.setPercent(new BigDecimal("0.20")); // 20%
        discounts.add(discount2);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: 10% + (90% * 20%) = 10% + 18% = 28%
        assertEquals(new BigDecimal("28.00"), result);
    }
}
