package com.subskribe.billy.order.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.fixtures.OrderData;
import com.subskribe.billy.fixtures.ProductCatalogData;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderGetService;
import com.subskribe.billy.template.services.DocumentCustomContentGetService;
import com.subskribe.billy.template.services.OrderTermsService;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.jooq.DSLContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class OrderGetServiceTest {

    private static final String TEST_PRODUCT_ID = "test_product_id";
    private static final UUID PLAN_ID = UUID.randomUUID();
    private static final String EXTERNAL_PLAN_ID = "test_plan_id";
    private static final UUID CHARGE_ID = UUID.randomUUID();
    private static final String ORDER_ID = UUID.randomUUID().toString();
    private static final String ACCOUNT_ID = UUID.randomUUID().toString();
    private static final String ACCOUNT_USER_ID = UUID.randomUUID().toString();
    private static final TenantId TENANT_ID = new TenantId("test_tenant_id");
    private static final String ENTITY_ID = "test_entity_id";
    private static final String EXTERNAL_SUBSCRIPTION_ID = "test_subscription_id";
    private static final LocalDateTime START_DATE = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
    private static final LocalDateTime END_DATE = LocalDateTime.of(2023, 1, 1, 0, 0, 0);

    private PaginationQueryParams mockPaginationQueryParameter;

    private OrderGetService orderGetService;

    private Order orderFromDB;

    @BeforeEach
    void testSetup() {
        TenantIdProvider mockTenantIdProvider = mock(TenantIdProvider.class);
        DSLContext mockDslContext = mock(DSLContext.class);
        DSLContextProvider mockDslContextProvider = mock(DSLContextProvider.class);
        mockPaginationQueryParameter = mock(PaginationQueryParams.class);
        OrderDAO mockOrderDao = mock(OrderDAO.class);
        ProductCatalogGetService mockProductCatalogGetService = mock(ProductCatalogGetService.class);
        CustomFieldService mockCustomFieldService = mock(CustomFieldService.class);
        DocumentCustomContentGetService mockDocumentCustomContentGetService = mock(DocumentCustomContentGetService.class);
        OrderTermsService mockOrderTermsService = mock(OrderTermsService.class);
        CustomTemplateUpdatedOnOrderGetService mockCustomTemplateUpdatedOnOrderGetService = mock(CustomTemplateUpdatedOnOrderGetService.class);
        DiscountService mockDiscountService = mock(DiscountService.class);

        var lineItem = OrderData.createOrderLineItem(
            UUID.randomUUID().toString(),
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            ORDER_ID,
            EXTERNAL_PLAN_ID,
            CHARGE_ID.toString(),
            10L,
            START_DATE,
            END_DATE
        );
        orderFromDB = OrderData.createOrder(
            ORDER_ID,
            TENANT_ID.getRequiredId(),
            ENTITY_ID,
            EXTERNAL_SUBSCRIPTION_ID,
            ACCOUNT_ID,
            ACCOUNT_USER_ID,
            List.of(lineItem),
            START_DATE,
            END_DATE
        );

        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID.getRequiredId());
        when(mockDslContextProvider.get(TENANT_ID.getRequiredId())).thenReturn(mockDslContext);
        when(mockOrderDao.getOrders(mockPaginationQueryParameter, Optional.empty())).thenReturn(List.of(orderFromDB));
        when(mockOrderDao.getOrderByOrderId(mockDslContext, ORDER_ID)).thenReturn(Optional.of(orderFromDB));
        when(mockOrderDao.getOrdersByAccountId(mockDslContext, ACCOUNT_ID)).thenReturn(List.of(orderFromDB));
        when(mockOrderDao.getOrdersByExternalSubscriptionId(mockDslContext, EXTERNAL_SUBSCRIPTION_ID, Optional.of(OrderStatus.EXECUTED))).thenReturn(
            List.of(orderFromDB)
        );

        var charge = ProductCatalogData.createCharge(CHARGE_ID, PLAN_ID);
        Plan plan = ProductCatalogData.createPlan(PLAN_ID, EXTERNAL_PLAN_ID, TEST_PRODUCT_ID, List.of(charge));
        when(mockProductCatalogGetService.getPlansByIds(List.of(PLAN_ID))).thenReturn(List.of(plan));
        orderGetService = new OrderGetService(
            mockOrderDao,
            mockDslContextProvider,
            mockTenantIdProvider,
            mockDiscountService,
            mock(OpportunityGetService.class),
            mockOrderTermsService,
            mockCustomFieldService,
            mockDocumentCustomContentGetService,
            mockCustomTemplateUpdatedOnOrderGetService
        );
    }

    @Test
    public void getOrders() {
        var orders = orderGetService.getOrders(mockPaginationQueryParameter, Optional.empty());
        assertEquals(1, orders.size());
        validateOrder(orders.get(0), orderFromDB);
    }

    @Test
    public void getOrderByOrderId() {
        var order = orderGetService.getOrderByOrderId(ORDER_ID);
        validateOrder(order, orderFromDB);
    }

    @Test
    public void getOrdersByAccountId() {
        var orders = orderGetService.getOrdersByAccountId(ACCOUNT_ID);
        assertEquals(1, orders.size());
        validateOrder(orders.get(0), orderFromDB);
    }

    @Test
    public void getOrdersBySubscriptionId() {
        var orders = orderGetService.getExecutedOrdersBySubscriptionId(EXTERNAL_SUBSCRIPTION_ID);
        assertEquals(1, orders.size());
        validateOrder(orders.get(0), orderFromDB);
    }

    private void validateOrder(Order expected, Order actual) {
        assertEquals(expected.getId(), actual.getId());
        assertEquals(expected.getAccountId(), actual.getAccountId());
        assertEquals(expected.getTenantId(), actual.getTenantId());
        assertEquals(expected.getSubscriptionUuid(), actual.getSubscriptionUuid());
        assertEquals(expected.getBillingContactId(), actual.getBillingContactId());
        assertEquals(expected.getShippingContactId(), actual.getShippingContactId());
        assertEquals(expected.getBillingCycle(), actual.getBillingCycle());
        assertEquals(expected.getCurrency(), actual.getCurrency());
        assertEquals(expected.getExternalSubscriptionId(), actual.getExternalSubscriptionId());
        assertEquals(expected.getStartDate(), actual.getStartDate());
        assertEquals(expected.getEndDate(), actual.getEndDate());
        assertEquals(1, actual.getLineItems().size());
        assertEquals(expected.getCreatedOn(), actual.getCreatedOn());
        assertEquals(expected.getUpdatedOn(), actual.getUpdatedOn());

        validateOrderLineItem(expected.getLineItems().get(0), actual.getLineItems().get(0));
    }

    private void validateOrderLineItem(OrderLineItem expected, OrderLineItem actual) {
        assertEquals(expected.getId(), actual.getId());
        assertEquals(expected.getOrderId(), actual.getOrderId());
        assertEquals(expected.getTenantId(), actual.getTenantId());
        assertEquals(expected.getExternalSubscriptionChargeId(), actual.getExternalSubscriptionChargeId());
        assertEquals(expected.getSubscriptionChargeId(), actual.getSubscriptionChargeId());
        assertEquals(expected.getChargeId(), actual.getChargeId());
        assertEquals(expected.getQuantity(), actual.getQuantity());
        assertEquals(expected.getAmount(), actual.getAmount());
        assertEquals(expected.getEffectiveDate(), actual.getEffectiveDate());
        assertEquals(expected.getEndDate(), actual.getEndDate());
        assertEquals(expected.getCreatedOn(), actual.getCreatedOn());
        assertEquals(expected.getUpdatedOn(), actual.getUpdatedOn());
        assertEquals(expected.getPlanId(), actual.getPlanId());
    }
}
