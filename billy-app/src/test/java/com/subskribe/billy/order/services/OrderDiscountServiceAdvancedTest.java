package com.subskribe.billy.order.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.TenantDiscountLineItem;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Advanced test cases for compound discount calculation logic in OrderDiscountService.
 * These tests cover edge cases, boundary conditions, and complex scenarios.
 */
class OrderDiscountServiceAdvancedTest {

    private OrderLineItem orderLineItem;
    private Map<String, Charge> chargeMap;

    @BeforeEach
    void setUp() {
        orderLineItem = new OrderLineItem();
        orderLineItem.setId(UUID.randomUUID());
        orderLineItem.setChargeId("usage-charge");

        Charge usageCharge = new Charge();
        usageCharge.setChargeId("usage-charge");
        usageCharge.setType(ChargeType.USAGE);
        usageCharge.setIsDiscount(false); // Not a discount charge

        chargeMap = new HashMap<>();
        chargeMap.put("usage-charge", usageCharge);
    }

    @Test
    void testMaximumDiscount_shouldNotExceed100Percent() {
        // Given: Multiple very high discounts that could theoretically exceed 100%
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail discount1 = new DiscountDetail();
        discount1.setPercent(new BigDecimal("0.90")); // 90%
        discounts.add(discount1);

        DiscountDetail discount2 = new DiscountDetail();
        discount2.setPercent(new BigDecimal("0.80")); // 80%
        discounts.add(discount2);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: 90% + (10% * 80%) = 90% + 8% = 98% (should be less than 100%)
        assertEquals(new BigDecimal("98.00"), result);
        assertTrue(result.compareTo(new BigDecimal("100.00")) < 0, "Discount should not exceed 100%");
    }

    @Test
    void testVerySmallDiscounts_shouldHandlePrecisionCorrectly() {
        // Given: Very small discount percentages
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail discount1 = new DiscountDetail();
        discount1.setPercent(new BigDecimal("0.001")); // 0.1%
        discounts.add(discount1);

        DiscountDetail discount2 = new DiscountDetail();
        discount2.setPercent(new BigDecimal("0.002")); // 0.2%
        discounts.add(discount2);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: 0.1% + (99.9% * 0.2%) = 0.1% + 0.1998% ≈ 0.30%
        assertEquals(new BigDecimal("0.30"), result.setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    void testFiftyPercentDiscounts_shouldCalculateCorrectly() {
        // Given: Two 50% discounts
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail discount1 = new DiscountDetail();
        discount1.setPercent(new BigDecimal("0.50")); // 50%
        discounts.add(discount1);

        DiscountDetail discount2 = new DiscountDetail();
        discount2.setPercent(new BigDecimal("0.50")); // 50%
        discounts.add(discount2);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: 50% + (50% * 50%) = 50% + 25% = 75%
        assertEquals(new BigDecimal("75.00"), result);
    }

    @Test
    void testManySmallDiscounts_shouldCompoundCorrectly() {
        // Given: Many small discounts (5% each)
        List<DiscountDetail> discounts = new ArrayList<>();
        List<TenantDiscountLineItem> predefinedDiscounts = new ArrayList<>();

        // Add 10 discounts of 5% each
        for (int i = 0; i < 5; i++) {
            DiscountDetail discount = new DiscountDetail();
            discount.setPercent(new BigDecimal("0.05")); // 5%
            discounts.add(discount);

            TenantDiscountLineItem tenantDiscount = new TenantDiscountLineItem();
            tenantDiscount.setPercent(new BigDecimal("0.05")); // 5%
            predefinedDiscounts.add(tenantDiscount);
        }

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(predefinedDiscounts);

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: With 10 × 5% discounts, compound effect should be:
        // Each step: current + (1-current) * 0.05
        // This should result in approximately 40.13% total discount
        assertTrue(result.compareTo(new BigDecimal("35.00")) > 0, "Should be greater than 35%");
        assertTrue(result.compareTo(new BigDecimal("45.00")) < 0, "Should be less than 45%");
    }

    @Test
    void testIdenticalDiscounts_shouldCompoundCorrectly() {
        // Given: Multiple identical discounts
        List<DiscountDetail> discounts = new ArrayList<>();

        // Add three 20% discounts
        for (int i = 0; i < 3; i++) {
            DiscountDetail discount = new DiscountDetail();
            discount.setPercent(new BigDecimal("0.20")); // 20%
            discounts.add(discount);
        }

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then:
        // Step 1: 20%
        // Step 2: 20% + (80% * 20%) = 20% + 16% = 36%
        // Step 3: 36% + (64% * 20%) = 36% + 12.8% = 48.8%
        assertEquals(new BigDecimal("48.80"), result.setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    void testExtremelyHighPrecisionValues() {
        // Given: Discounts with extreme precision
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail discount1 = new DiscountDetail();
        discount1.setPercent(new BigDecimal("0.123456789012345"));
        discounts.add(discount1);

        DiscountDetail discount2 = new DiscountDetail();
        discount2.setPercent(new BigDecimal("0.987654321098765"));
        discounts.add(discount2);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: Should handle high precision and round to 2 decimal places
        assertEquals(2, result.scale());
        assertTrue(result.compareTo(BigDecimal.ZERO) >= 0);
        assertTrue(result.compareTo(new BigDecimal("100.00")) < 0);
    }

    @Test
    void testMixedValidAndInvalidDiscounts() {
        // Given: Mix of valid discounts and invalid/edge case discounts
        List<DiscountDetail> discounts = new ArrayList<>();
        List<TenantDiscountLineItem> predefinedDiscounts = new ArrayList<>();

        // Valid discount
        DiscountDetail validDiscount = new DiscountDetail();
        validDiscount.setPercent(new BigDecimal("0.15"));
        discounts.add(validDiscount);

        // Zero discount (should be filtered)
        DiscountDetail zeroDiscount = new DiscountDetail();
        zeroDiscount.setPercent(BigDecimal.ZERO);
        discounts.add(zeroDiscount);

        // Null discount (should be filtered)
        DiscountDetail nullDiscount = new DiscountDetail();
        nullDiscount.setPercent(null);
        discounts.add(nullDiscount);

        // Valid predefined discount
        TenantDiscountLineItem validTenantDiscount = new TenantDiscountLineItem();
        validTenantDiscount.setPercent(new BigDecimal("0.25"));
        predefinedDiscounts.add(validTenantDiscount);

        // Invalid predefined discount (should be filtered)
        TenantDiscountLineItem invalidTenantDiscount = new TenantDiscountLineItem();
        invalidTenantDiscount.setPercent(BigDecimal.ZERO);
        predefinedDiscounts.add(invalidTenantDiscount);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(predefinedDiscounts);

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: Should only consider the two valid discounts (15% and 25%)
        // 15% + (85% * 25%) = 15% + 21.25% = 36.25%
        assertEquals(new BigDecimal("36.25"), result.setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    void testSingleMaximumDiscount() {
        // Given: Single 100% discount
        List<DiscountDetail> discounts = new ArrayList<>();

        DiscountDetail discount = new DiscountDetail();
        discount.setPercent(BigDecimal.ONE); // 100%
        discounts.add(discount);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then
        assertEquals(new BigDecimal("100.00"), result.setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    void testDiscountOrder_shouldNotMatter() {
        // Given: Same discounts in different orders
        List<DiscountDetail> discounts1 = new ArrayList<>();
        discounts1.add(createDiscount("0.10"));
        discounts1.add(createDiscount("0.20"));
        discounts1.add(createDiscount("0.30"));

        List<DiscountDetail> discounts2 = new ArrayList<>();
        discounts2.add(createDiscount("0.30"));
        discounts2.add(createDiscount("0.10"));
        discounts2.add(createDiscount("0.20"));

        // When: Calculate with first order
        orderLineItem.setDiscounts(discounts1);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());
        BigDecimal result1 = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // When: Calculate with second order
        orderLineItem.setDiscounts(discounts2);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());
        BigDecimal result2 = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: Results should be the same regardless of order
        assertEquals(result1, result2, "Discount order should not affect final result");
    }

    @Test
    void testRoundingConsistency() {
        // Given: Discounts that test rounding behavior
        List<DiscountDetail> discounts = new ArrayList<>();

        // These specific values are chosen to test rounding edge cases
        discounts.add(createDiscount("0.333")); // 33.3%
        discounts.add(createDiscount("0.334")); // 33.4%

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(Collections.emptyList());

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: Should be consistently rounded to 2 decimal places
        assertEquals(2, result.scale());

        // Calculate expected: 33.3% + (66.7% * 33.4%) = 33.3% + 22.278% = 55.578% ≈ 55.58%
        assertEquals(new BigDecimal("55.58"), result.setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    void testBoundaryValues() {
        // Given: Discounts at boundary values
        List<DiscountDetail> discounts = new ArrayList<>();
        List<TenantDiscountLineItem> predefinedDiscounts = new ArrayList<>();

        // Minimum positive value
        discounts.add(createDiscount("0.01")); // 1%

        // Just under 100%
        TenantDiscountLineItem tenantDiscount = new TenantDiscountLineItem();
        tenantDiscount.setPercent(new BigDecimal("0.99")); // 99%
        predefinedDiscounts.add(tenantDiscount);

        orderLineItem.setDiscounts(discounts);
        orderLineItem.setPredefinedDiscounts(predefinedDiscounts);

        // When
        BigDecimal result = OrderDiscountService.calculateSingleOrderLineNetDiscountPercent(orderLineItem, chargeMap);

        // Then: 1% + (99% * 99%) = 1% + 98.01% = 99.01%
        assertEquals(new BigDecimal("99.01"), result);
    }

    private DiscountDetail createDiscount(String percent) {
        DiscountDetail discount = new DiscountDetail();
        discount.setPercent(new BigDecimal(percent));
        return discount;
    }
}
