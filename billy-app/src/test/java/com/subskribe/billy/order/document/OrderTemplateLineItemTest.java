package com.subskribe.billy.order.document;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldSource;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.resources.json.plan.PriceTierJson;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.shared.document.TemplateCharge;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import java.util.function.Function;
import org.junit.jupiter.api.Test;

public class OrderTemplateLineItemTest {

    private static final String CURRENCY_CODE = "USD";

    private static final String TIME_ZONE = "US/Los_Angeles";

    private static final String CHARGE_CUSTOM_FIELD_NAME = "chargeCustomFieldName";

    private static final String CHARGE_CUSTOM_FIELD_VALUE = "chargeCustomFieldValue";

    private static final String CHARGE_CUSTOM_FIELD_LABEL = "Charge Custom Field";

    private static final String ORDER_LINE_ID = UUID.randomUUID().toString();

    private static final BigDecimal LINE_YEARLY_AMOUNT = BigDecimal.valueOf(1000);

    private final DocumentRenderFormatter documentRenderFormatter = new DocumentRenderFormatter(TimeZone.getTimeZone(TIME_ZONE));

    @Test
    public void customDiscountAmount() {
        OrderLineItemDetail orderLineItemDetail = getOrderLineItemDetail(null, 10L);
        orderLineItemDetail.setListAmount(new BigDecimal("1000"));
        orderLineItemDetail.setListUnitPrice(new BigDecimal("100"));
        orderLineItemDetail.setAmount(new BigDecimal("500"));
        orderLineItemDetail.setDiscountAmount(new BigDecimal("500"));

        OrderTemplateLineItem orderLineItem = new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getOrderTemplateCharge(),
            getOrderLineMetrics().getEntryArr(),
            getCustomField(),
            documentRenderFormatter,
            CURRENCY_CODE,
            List.of(),
            TimeZone.getTimeZone(TIME_ZONE)
        );

        Function<String, Void> setCustomListUnitPrice = orderLineItem.setCustomListUnitPrice();

        // custom list price of 200 * 10 quantity = 2000 - 500 sell amount = 1500 discount
        setCustomListUnitPrice.apply("200");
        assertEquals("1500", orderLineItem.getCustomDiscountAmount());

        setCustomListUnitPrice.apply("100");
        assertEquals("500", orderLineItem.getCustomDiscountAmount());

        setCustomListUnitPrice.apply("50");
        assertEquals("500", orderLineItem.getCustomDiscountAmount());
    }

    @Test
    public void renderBlockQuantityRange() {
        var priceTiers = getPriceTiers();
        ChargeDetail chargeDetail = getChargeDetail(ChargeModel.BLOCK, priceTiers);

        OrderLineItemDetail orderLineItemDetail = getOrderLineItemDetail(chargeDetail, 30L);
        OrderTemplateLineItem newOrderLineItem = new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getOrderTemplateCharge(),
            getOrderLineMetrics().getEntryArr(),
            getCustomField(),
            documentRenderFormatter,
            CURRENCY_CODE,
            List.of(),
            TimeZone.getTimeZone(TIME_ZONE)
        );
        String blockQuantityDisplay = newOrderLineItem.getBlockQuantity();
        assertEquals("21 - 30", blockQuantityDisplay);
    }

    @Test
    public void renderBlockQuantityRangeForFirstTier() {
        var priceTiers = getPriceTiers();
        ChargeDetail chargeDetail = getChargeDetail(ChargeModel.BLOCK, priceTiers);

        OrderLineItemDetail orderLineItemDetail = getOrderLineItemDetail(chargeDetail, 10L);
        OrderTemplateLineItem newOrderLineItem = new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getOrderTemplateCharge(),
            getOrderLineMetrics().getEntryArr(),
            getCustomField(),
            documentRenderFormatter,
            CURRENCY_CODE,
            List.of(),
            TimeZone.getTimeZone(TIME_ZONE)
        );
        String blockQuantityDisplay = newOrderLineItem.getBlockQuantity();
        assertEquals("0 - 10", blockQuantityDisplay);
    }

    @Test
    public void renderBlockQuantityRangeForLastTier() {
        var priceTiers = getPriceTiers();
        ChargeDetail chargeDetail = getChargeDetail(ChargeModel.BLOCK, priceTiers);

        OrderLineItemDetail orderLineItemDetail = getOrderLineItemDetail(chargeDetail, 100L);
        OrderTemplateLineItem newOrderLineItem = new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getOrderTemplateCharge(),
            getOrderLineMetrics().getEntryArr(),
            getCustomField(),
            documentRenderFormatter,
            CURRENCY_CODE,
            List.of(),
            TimeZone.getTimeZone(TIME_ZONE)
        );
        String blockQuantityDisplay = newOrderLineItem.getBlockQuantity();
        assertEquals("41 - any", blockQuantityDisplay);
    }

    @Test
    public void renderBlockQuantityRangeForLastTierWithMaxQuantity() {
        var priceTiers = getPriceTiers();
        ChargeDetail chargeDetail = getChargeDetail(ChargeModel.BLOCK, priceTiers);
        chargeDetail.setMaxQuantity(50L);

        OrderLineItemDetail orderLineItemDetail = getOrderLineItemDetail(chargeDetail, 100L);
        OrderTemplateLineItem newOrderLineItem = new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getOrderTemplateCharge(),
            getOrderLineMetrics().getEntryArr(),
            getCustomField(),
            documentRenderFormatter,
            CURRENCY_CODE,
            List.of(),
            TimeZone.getTimeZone(TIME_ZONE)
        );
        String blockQuantityDisplay = newOrderLineItem.getBlockQuantity();
        assertEquals("41 - 50", blockQuantityDisplay);
    }

    @Test
    public void perUnitReturnsQuantity() {
        var priceTiers = getPriceTiers();
        ChargeDetail chargeDetail = getChargeDetail(ChargeModel.PER_UNIT, priceTiers);

        OrderLineItemDetail orderLineItemDetail = getOrderLineItemDetail(chargeDetail, 100L);
        OrderTemplateLineItem newOrderLineItem = new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getOrderTemplateCharge(),
            getOrderLineMetrics().getEntryArr(),
            getCustomField(),
            documentRenderFormatter,
            CURRENCY_CODE,
            List.of(),
            TimeZone.getTimeZone(TIME_ZONE)
        );
        String blockQuantityDisplay = newOrderLineItem.getBlockQuantity();
        assertEquals("100", blockQuantityDisplay);
    }

    @Test
    public void flatFeeReturnsOneForAnyQuantity() {
        var priceTiers = getPriceTiers();
        ChargeDetail chargeDetail = getChargeDetail(ChargeModel.FLAT_FEE, priceTiers);

        OrderLineItemDetail orderLineItemDetail = getOrderLineItemDetail(chargeDetail, 10L);
        OrderTemplateLineItem newOrderLineItem = new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getOrderTemplateCharge(),
            getOrderLineMetrics().getEntryArr(),
            getCustomField(),
            documentRenderFormatter,
            CURRENCY_CODE,
            List.of(),
            TimeZone.getTimeZone(TIME_ZONE)
        );
        String blockQuantityDisplay = newOrderLineItem.getBlockQuantity();
        assertEquals("1", blockQuantityDisplay);
    }

    @Test
    public void getChargeCustomField() {
        var priceTiers = getPriceTiers();
        ChargeDetail chargeDetail = getChargeDetail(ChargeModel.FLAT_FEE, priceTiers);

        OrderLineItemDetail orderLineItemDetail = getOrderLineItemDetail(chargeDetail, 10L);
        OrderTemplateLineItem newOrderLineItem = new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getOrderTemplateCharge(),
            getOrderLineMetrics().getEntryArr(),
            getCustomField(),
            documentRenderFormatter,
            CURRENCY_CODE,
            List.of(),
            TimeZone.getTimeZone(TIME_ZONE)
        );
        String customFieldValue = newOrderLineItem.getCharge().getCustomFields().get(CHARGE_CUSTOM_FIELD_NAME).getValue();

        assertEquals(CHARGE_CUSTOM_FIELD_VALUE, customFieldValue);
    }

    @Test
    public void roundedDiscountLambdaTest() {
        OrderLineItemDetail orderLineItemDetail = getOrderLineItemDetail(null, 10L);
        orderLineItemDetail.setListAmount(new BigDecimal("900"));
        orderLineItemDetail.setAmount(new BigDecimal("600"));

        OrderTemplateLineItem orderLineItem = new OrderTemplateLineItem(
            orderLineItemDetail,
            new ProductJson(),
            null,
            getOrderTemplateCharge(),
            getOrderLineMetrics().getEntryArr(),
            getCustomField(),
            documentRenderFormatter,
            CURRENCY_CODE,
            List.of(),
            TimeZone.getTimeZone(TIME_ZONE)
        );

        var lineDiscountFunction = orderLineItem.getRoundedLineItemDiscountPercent();
        assertEquals("33.33", lineDiscountFunction.apply("2"));
        assertEquals("33", lineDiscountFunction.apply("0"));
        assertEquals("33.333", lineDiscountFunction.apply("3"));
        assertEquals("33.33", lineDiscountFunction.apply(null));
        assertEquals("33.33", lineDiscountFunction.apply("not a number"));
    }

    private OrderLineItemDetail getOrderLineItemDetail(ChargeDetail chargeDetail, long quantity) {
        OrderLineItemDetail orderLineItemDetail = new OrderLineItemDetail();
        orderLineItemDetail.setId(ORDER_LINE_ID);
        orderLineItemDetail.setChargeDetail(chargeDetail);
        orderLineItemDetail.setQuantity(quantity);

        return orderLineItemDetail;
    }

    private Metrics getOrderLineMetrics() {
        return new Metrics(
            LINE_YEARLY_AMOUNT,
            LINE_YEARLY_AMOUNT,
            BigDecimal.ZERO,
            LINE_YEARLY_AMOUNT,
            LINE_YEARLY_AMOUNT,
            LINE_YEARLY_AMOUNT,
            LINE_YEARLY_AMOUNT,
            LINE_YEARLY_AMOUNT,
            LINE_YEARLY_AMOUNT,
            null
        );
    }

    private TemplateCharge getOrderTemplateCharge() {
        ChargeDetail chargeDetail = getChargeDetail(ChargeModel.BLOCK, getPriceTiers());
        CustomFieldValue value = new CustomFieldValue(
            CustomFieldType.STRING,
            CHARGE_CUSTOM_FIELD_NAME,
            CHARGE_CUSTOM_FIELD_LABEL,
            CHARGE_CUSTOM_FIELD_VALUE,
            List.of(),
            List.of(),
            false,
            CustomFieldSource.USER,
            null
        );
        return new TemplateCharge(
            new CustomField(Map.of(UUID.randomUUID().toString(), value)),
            documentRenderFormatter,
            CURRENCY_CODE,
            null,
            chargeDetail
        );
    }

    private CustomField getCustomField() {
        return new CustomField(new HashMap<>());
    }

    private ChargeDetail getChargeDetail(ChargeModel chargeModel, List<PriceTierJson> tiers) {
        ChargeDetail chargeDetail = new ChargeDetail();
        chargeDetail.setChargeModel(chargeModel);
        chargeDetail.setPriceTiers(tiers);
        return chargeDetail;
    }

    private List<PriceTierJson> getPriceTiers() {
        return List.of(
            new PriceTierJson("10", new BigDecimal("10"), null),
            new PriceTierJson("20", new BigDecimal("20"), null),
            new PriceTierJson("30", new BigDecimal("30"), null),
            new PriceTierJson("40", new BigDecimal("40"), null),
            new PriceTierJson("INF", new BigDecimal("50"), null)
        );
    }
}
