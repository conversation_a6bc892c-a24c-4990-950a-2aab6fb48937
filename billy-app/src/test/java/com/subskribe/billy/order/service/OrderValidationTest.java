package com.subskribe.billy.order.service;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.approvalflowinstance.service.ApprovalFlowInstanceService;
import com.subskribe.billy.attachments.service.AttachmentsService;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipGetService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.crm.service.CrmService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.customization.service.CustomizationService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.discount.services.DiscountService;
import com.subskribe.billy.document.service.DocumentLinkService;
import com.subskribe.billy.email.services.EmailContactListService;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.esign.services.EsignService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.fixtures.AccountGetServiceMockSetup;
import com.subskribe.billy.fixtures.OrderData;
import com.subskribe.billy.fixtures.ProductCatalogGetServiceMockData;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.hubspot.service.HubSpotJobQueueService;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.invoice.service.InvoiceService;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.metrics.MetricsService;
import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.opportunity.service.OpportunityGetService;
import com.subskribe.billy.opportunity.service.OpportunityService;
import com.subskribe.billy.order.EvergreenUtils;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.model.CreateOrderContext;
import com.subskribe.billy.order.model.CustomBillingPeriod;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.ImmutableCreateOrderContext;
import com.subskribe.billy.order.model.ImmutableCustomBillingPeriod;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.order.services.MissingOrderChargesService;
import com.subskribe.billy.order.services.OrderAttributesUpdateService;
import com.subskribe.billy.order.services.OrderCustomBillingService;
import com.subskribe.billy.order.services.OrderDocumentTemplateService;
import com.subskribe.billy.order.services.OrderEventService;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderIdGenerator;
import com.subskribe.billy.order.services.OrderService;
import com.subskribe.billy.order.services.OrderValidationService;
import com.subskribe.billy.paymentterms.model.PaymentTermSettings;
import com.subskribe.billy.paymentterms.services.PaymentTermSettingsService;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.TaxRateGetService;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.revrec.services.RevenueRecognitionJobService;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.salesroom.service.SalesRoomLinkService;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionLifecycleScheduleService;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderGetService;
import com.subskribe.billy.template.services.CustomTemplateUpdatedOnOrderService;
import com.subskribe.billy.template.services.DocumentCustomContentService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.OrderTermsService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import com.subskribe.billy.user.service.UserService;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class OrderValidationTest {

    private static final String ENTITY_ID = "ENTITY-001";

    private PaymentTermSettingsService mockPaymentTermSettingsService;
    private ProductCatalogGetServiceMockData productCatalogGetServiceMockData;

    private OrderService mockOrderService;

    private AccountGetService accountGetService;

    private OrderCustomBillingService orderCustomBillingService;

    @BeforeEach
    void setUp() {
        mockPaymentTermSettingsService = mock(PaymentTermSettingsService.class);
        PaymentTermSettings paymentTermSettings = new PaymentTermSettings();
        paymentTermSettings.setCustomPaymentTermsAllowed(false);
        paymentTermSettings.setDefaultPaymentTerms(PaymentTermSettingsService.DEFAULT_PAYMENT_TERMS);
        productCatalogGetServiceMockData = new ProductCatalogGetServiceMockData();
        when(mockPaymentTermSettingsService.getPaymentTermSettings()).thenReturn(paymentTermSettings);
        TenantSettingService tenantSettingService = TenantSettingServiceFixture.getDefault();
        TaxService mockTaxService = mock(TaxService.class);
        accountGetService = AccountGetServiceMockSetup.get();
        OrderValidationService orderValidationService = new OrderValidationService(accountGetService, mockTaxService);
        orderCustomBillingService = mock(OrderCustomBillingService.class);
        mockOrderService = new OrderService(
            mock(OrderDAO.class),
            accountGetService,
            productCatalogGetServiceMockData.getMockGetService(),
            mock(SubscriptionService.class),
            mock(InvoiceService.class),
            mock(DSLContextProvider.class),
            mock(TenantIdProvider.class),
            mock(EntityContextResolver.class),
            mock(OrderIdGenerator.class),
            mock(EmailContactListService.class),
            mock(DocumentTemplateGetService.class),
            mock(DocumentCustomContentService.class),
            mock(OpportunityGetService.class),
            mock(OpportunityService.class),
            mock(OrderTermsService.class),
            mock(OrderGetService.class),
            orderValidationService,
            mock(DiscountService.class),
            mock(ApprovalFlowInstanceService.class),
            mock(UserService.class),
            mock(SalesforceJobQueueService.class),
            mock(FeatureService.class),
            mock(PlatformFeatureService.class),
            tenantSettingService,
            mock(BillyConfiguration.class),
            mock(RevenueRecognitionJobService.class),
            mock(HubSpotIntegrationService.class),
            mock(TenantJobDispatcherService.class),
            mock(CustomFieldService.class),
            mock(AttachmentsService.class),
            mockPaymentTermSettingsService,
            mock(ReportingJobQueueService.class),
            mock(SubscriptionGetService.class),
            mock(DocumentLinkService.class),
            mock(CatalogRelationshipGetService.class),
            mock(RateCardService.class),
            mock(OrderEventService.class),
            mock(SubscriptionBillingPeriodService.class),
            mock(SubscriptionLifecycleScheduleService.class),
            mock(TaxService.class),
            mock(TaxRateGetService.class),
            mock(SalesRoomLinkService.class),
            mock(CustomizationService.class),
            mock(MissingOrderChargesService.class),
            mock(MetricsService.class),
            mock(CrmService.class),
            mock(CurrencyConversionRateGetService.class),
            mock(EsignService.class),
            orderCustomBillingService,
            mock(OrderAttributesUpdateService.class),
            mock(OrderDocumentTemplateService.class),
            mock(CustomTemplateUpdatedOnOrderGetService.class),
            mock(CustomTemplateUpdatedOnOrderService.class),
            mock(EvergreenUtils.class),
            mock(HubSpotJobQueueService.class)
        );
    }

    @Test
    public void testSaaSOrderValidation() {
        var planIds = List.of("SAAS-PLAN");
        var order = createOrderWithPlans(planIds);
        mockOrderService.validateOrder(order, makeCreateContext(planIds), false);
    }

    @Test
    public void testPrepaidOrderValidation() {
        var planIds = List.of("PREPAID-100");
        var order = createOrderWithPlans(planIds);
        mockOrderService.validateOrder(order, makeCreateContext(planIds), false);
    }

    @Test
    public void testMultipleDrawdowns() {
        Assertions.assertDoesNotThrow(() -> {
            var planIds = List.of("PREPAID-100", "PREPAID-200");
            var order = createOrderWithPlans(planIds);
            mockOrderService.validateOrder(order, makeCreateContext(planIds), false);
        });

        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> {
                var planIds = List.of("PREPAID-100");
                var order = createOrderWithPlans(planIds);
                var lineItems = new ArrayList<>(order.getLineItems());
                // duplicate line items
                lineItems.addAll(new ArrayList<>(lineItems));
                order.setLineItems(lineItems);
                mockOrderService.validateOrder(order, makeCreateContext(planIds), false);
            })
            .withMessageContaining("cannot have multiple line items for drawdown charge");
    }

    @Test
    public void testPaymentTermsNull() {
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            var planIds = List.of("SAAS-PLAN");
            var order = createOrderWithPlans(planIds);
            order.setPaymentTerm(null);
            mockOrderService.validateOrder(order, makeCreateContext(planIds), false);
        });

        Assertions.assertTrue(exception.getMessage().contains("payment term"));
    }

    @Test
    public void testCustomPaymentTermNotSupported() {
        PaymentTermSettings paymentTermSettings = new PaymentTermSettings();
        paymentTermSettings.setDefaultPaymentTerms(PaymentTermSettingsService.DEFAULT_PAYMENT_TERMS);
        paymentTermSettings.setCustomPaymentTermsAllowed(false);
        when(mockPaymentTermSettingsService.getPaymentTermSettings()).thenReturn(paymentTermSettings);
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            var planIds = List.of("SAAS-PLAN");
            var order = createOrderWithPlans(planIds);
            order.setPaymentTerm(new PaymentTerm("NET69"));
            mockOrderService.validateOrder(order, makeCreateContext(planIds), false);
        });

        Assertions.assertTrue(exception.getMessage().contains("payment term"));
    }

    @Test
    public void testOrderDryRunWithNoLineItems() {
        List<String> planIds = List.of();
        var order = createOrderWithPlans(planIds);
        order.setLineItems(null);
        mockOrderService.validateOrder(order, makeCreateContext(planIds), true);
    }

    @Test
    public void testOrderWithNoLineItemsFails() {
        List<String> planIds = List.of();
        var order = createOrderWithPlans(planIds);
        order.setLineItems(null);
        assertThrows(InvalidInputException.class, () -> mockOrderService.validateOrder(order, makeCreateContext(planIds), false));
    }

    @Test
    public void testAmendmentOrderWithExecutionDateStartDateTypeFails() {
        List<String> planIds = List.of();
        var order = createOrderWithPlans(planIds);
        order.setOrderType(OrderType.AMENDMENT);
        order.setStartDateType(OrderStartDateType.EXECUTION_DATE);
        assertThrows(InvalidInputException.class, () -> mockOrderService.validateOrder(order, makeCreateContext(planIds), false));
    }

    @Test
    public void testOrderWithExecutionDateStartDateTypeAndMonthlyCycleFails() {
        List<String> planIds = List.of();
        var order = createOrderWithPlans(planIds);
        order.setStartDateType(OrderStartDateType.EXECUTION_DATE);
        order.setEndDate(null);
        order.setTermLength(new Recurrence(Cycle.MONTH, 1));
        assertThrows(InvalidInputException.class, () -> mockOrderService.validateOrder(order, makeCreateContext(planIds), false));
    }

    @Test
    public void testOrderWithExecutionDateStartDateTypeAndNonEmptyRampIntervalFails() {
        List<String> planIds = List.of();
        var order = createOrderWithPlans(planIds);
        order.setStartDateType(OrderStartDateType.EXECUTION_DATE);
        order.setEndDate(null);
        order.setTermLength(new Recurrence(Cycle.YEAR, 1));
        order.setRampInterval(List.of(Instant.now()));
        assertThrows(InvalidInputException.class, () -> mockOrderService.validateOrder(order, makeCreateContext(planIds), false));
    }

    @Test
    void validateAndGetBillingCycleForCustomBilling_Success() {
        // Given
        Order order = mock(Order.class);
        RecurrenceJson recurrenceJson = new RecurrenceJson(Cycle.PAID_IN_FULL, 1);

        CustomBillingSchedule.CustomBillingRecurrence recurrenceWithCount = new CustomBillingSchedule.CustomBillingRecurrence(recurrenceJson, 1);

        CustomBillingPeriod customBillingPeriod = ImmutableCustomBillingPeriod.builder().recurrenceWithCount(recurrenceWithCount).build();

        CustomBillingSchedule customBillingSchedule = new CustomBillingSchedule(
            "custom-schedule-id",
            CustomBillingSchedule.Version.V1,
            order.getOrderId(),
            List.of(),
            List.of(customBillingPeriod),
            false,
            Instant.now().toEpochMilli(),
            Instant.now().toEpochMilli()
        );

        when(order.getBillingCycle()).thenReturn(new Recurrence(Cycle.CUSTOM, 1));
        when(order.getCustomBillingSchedule()).thenReturn(customBillingSchedule);
        when(order.getBillingAnchorDate()).thenReturn(null);
        when(order.getStartDate()).thenReturn(Instant.now());
        when(orderCustomBillingService.shouldUseCustomBillingInvoicingInput(order)).thenReturn(true);

        // When
        Recurrence result = mockOrderService.validateAndGetBillingCycleForCustomBilling(order);

        // Then
        Assertions.assertEquals(Cycle.PAID_IN_FULL, result.getCycle());
        Assertions.assertEquals(1, result.getStep().intValue());
    }

    @Test
    void validateAndGetBillingCycleForCustomBilling_NotEligibleForCustomBilling_ReturnsPaidInFull() {
        // Given
        Order order = mock(Order.class);
        when(orderCustomBillingService.shouldUseCustomBillingInvoicingInput(order)).thenReturn(false);

        // When
        Recurrence result = mockOrderService.validateAndGetBillingCycleForCustomBilling(order);

        // Then
        Assertions.assertEquals(Cycle.PAID_IN_FULL, result.getCycle());
        Assertions.assertEquals(1, result.getStep());
    }

    public Order createOrderWithPlans(List<String> planIds) {
        var startDate = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
        var endDate = LocalDateTime.of(2023, 1, 1, 0, 0, 0);
        List<OrderLineItem> lineItems = new ArrayList<>();

        for (String planId : planIds) {
            createLineItemsWithPlanId(startDate, endDate, lineItems, planId);
        }
        return OrderData.createOrder("ORDER-001", "TENANT-001", ENTITY_ID, "SUB-001", "OKTA-ACCT", "OKTA-USER-JOHN", lineItems, startDate, endDate);
    }

    private void createLineItemsWithPlanId(LocalDateTime startDate, LocalDateTime endDate, List<OrderLineItem> lineItems, String planId) {
        Plan plan = productCatalogGetServiceMockData.getPlan(planId);
        List<Charge> charges = plan.getCharges();
        for (Charge charge : charges) {
            createLineItemWithCharge(startDate, endDate, lineItems, plan, charge);
        }
    }

    private void createLineItemWithCharge(LocalDateTime startDate, LocalDateTime endDate, List<OrderLineItem> lineItems, Plan plan, Charge charge) {
        OrderLineItem lineItem = OrderData.createOrderLineItem(
            UUID.randomUUID().toString(),
            "TENANT-001",
            "ENTITY-001",
            "ORDER-001",
            plan.getPlanId(),
            charge.getChargeId(),
            10L,
            startDate,
            endDate
        );
        lineItems.add(lineItem);
    }

    CreateOrderContext makeCreateContext(List<String> planIds) {
        List<Plan> plans = productCatalogGetServiceMockData.getMockGetService().getPlansByPlanIds(planIds);
        plans.forEach(plan -> plan.getCharges().forEach(charge -> charge.setEntityIds(Set.of(ENTITY_ID))));

        List<Charge> charges = plans.stream().map(Plan::getCharges).flatMap(Collection::stream).toList();

        return ImmutableCreateOrderContext.builder()
            .orderAccount(accountGetService.getAccount(AccountGetServiceMockSetup.TEST_ACCOUNT_ID))
            .plansInOrder(plans)
            .chargesInOrder(charges)
            .build();
    }
}
