package com.subskribe.billy.order.document;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.shared.document.TemplateCharge;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import org.junit.jupiter.api.Test;

public class OrderLineChargeRollupTest {

    DocumentRenderFormatter formatter = new DocumentRenderFormatter(TimeZone.getTimeZone("UTC"));

    @Test
    public void checkWhetherQuantityIsUpdatedBasedOnChargeCustomField() {
        String typeKey = UUID.randomUUID().toString();
        String chargeToRollUp = "CHRG-" + UUID.randomUUID();
        String mainCharge = "CHRG-" + UUID.randomUUID();

        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithChargeCustomField(UUID.randomUUID().toString(), UUID.randomUUID().toString(), mainCharge, 4000000L),
            getLineItemWithChargeCustomField(typeKey, mainCharge, chargeToRollUp, 1000000L),
            getLineItemWithChargeCustomField(UUID.randomUUID().toString(), UUID.randomUUID().toString(), UUID.randomUUID().toString(), 2L)
        );

        OrderTemplateData.rollUpChargeWithinBundleBasedOnCustomField(lineItems, typeKey);
        assertEquals("5,000,000", lineItems.get(0).getQuantity()); // 4 million + 1 million
        assertEquals("$20.00", lineItems.get(0).getAmount()); // 10 + 10
        assertEquals("$40.00", lineItems.get(0).getListAmount()); // 20 + 20
        assertEquals("$200.00", lineItems.get(0).getYearlyAmount()); // 100 + 100

        assertEquals("0", lineItems.get(1).getQuantity());
        assertEquals("$0.00", lineItems.get(1).getAmount());
        assertEquals("$0.00", lineItems.get(1).getListAmount());
        assertEquals("$0.00", lineItems.get(1).getYearlyAmount());

        assertEquals("2", lineItems.get(2).getQuantity());
        assertEquals("$10.00", lineItems.get(2).getAmount());
        assertEquals("$20.00", lineItems.get(2).getListAmount());
        assertEquals("$100.00", lineItems.get(2).getYearlyAmount());
    }

    private OrderTemplateLineItem getLineItemWithChargeCustomField(String customFieldName, String selection, String chargeId, Long quantity) {
        OrderLineItemDetail orderLineItemDetail = new OrderLineItemDetail();
        orderLineItemDetail.setQuantity(quantity);
        orderLineItemDetail.setAmount(BigDecimal.valueOf(10));
        orderLineItemDetail.setListAmount(BigDecimal.valueOf(20));
        orderLineItemDetail.setAnnualizedAmount(BigDecimal.valueOf(100));

        CustomFieldEntry cfn = new CustomFieldEntry(
            UUID.randomUUID().toString(),
            CustomFieldType.STRING,
            customFieldName,
            customFieldName,
            selection,
            List.of(selection),
            List.of(),
            false,
            null,
            null
        );
        CustomFieldValue customFieldValue = CustomFieldEntry.toValue(cfn);
        CustomField customField = new CustomField(Map.of(customFieldName, customFieldValue));
        ChargeJson charge = new ChargeJson();
        charge.setId(chargeId);
        TemplateCharge templateCharge = new TemplateCharge(customField, formatter, "USD", null, charge, null);

        return new OrderTemplateLineItem(orderLineItemDetail, null, null, templateCharge, null, null, formatter, "USD", null, null);
    }
}
