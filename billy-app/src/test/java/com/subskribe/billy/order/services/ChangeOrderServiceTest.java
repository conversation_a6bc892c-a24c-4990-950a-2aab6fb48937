package com.subskribe.billy.order.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.InvariantCheckFailedException;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.shared.enums.ActionType;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class ChangeOrderServiceTest {

    @Test
    void checkForRebaseConflicts() {
        List<OrderLineItem> itemsWithNoneAction = List.of(getOrderLineItems(ActionType.NONE, null));
        ChangeOrderService.checkForRebaseConflicts(itemsWithNoneAction, Set.of());

        List<OrderLineItem> itemsWithNoneAndAddActions = List.of(getOrderLineItems(ActionType.NONE, null), getOrderLineItems(ActionType.ADD, null));
        ChangeOrderService.checkForRebaseConflicts(itemsWithNoneAndAddActions, Set.of());

        List<OrderLineItem> itemsWithUpdateActionAndNullSubscriptionChargeId = List.of(
            getOrderLineItems(ActionType.UPDATE, null),
            getOrderLineItems(ActionType.ADD, null)
        );
        assertThrows(InvariantCheckFailedException.class, () ->
            ChangeOrderService.checkForRebaseConflicts(itemsWithUpdateActionAndNullSubscriptionChargeId, Set.of())
        );

        String externalSubscriptionChargeId = UUID.randomUUID().toString();
        List<OrderLineItem> itemsWithUpdateActionAndExistingSubscriptionChargeId = List.of(
            getOrderLineItems(ActionType.UPDATE, externalSubscriptionChargeId),
            getOrderLineItems(ActionType.ADD, null)
        );
        ChangeOrderService.checkForRebaseConflicts(itemsWithUpdateActionAndExistingSubscriptionChargeId, Set.of(externalSubscriptionChargeId));

        List<OrderLineItem> itemsWithUpdateActionAndMissingSubscriptionChargeId = List.of(
            getOrderLineItems(ActionType.UPDATE, UUID.randomUUID().toString()),
            getOrderLineItems(ActionType.ADD, null)
        );
        assertThrows(InvalidInputException.class, () ->
            ChangeOrderService.checkForRebaseConflicts(itemsWithUpdateActionAndMissingSubscriptionChargeId, Set.of())
        );
    }

    @Test
    public void removeRemovedSubscriptionChargeItems() {
        List<OrderLineItem> itemsWithNoneAction = List.of(getOrderLineItems(ActionType.NONE, null));
        List<OrderLineItem> updatedItems = ChangeOrderService.removeRemovedSubscriptionChargeItems(itemsWithNoneAction, Set.of());
        assertEquals(itemsWithNoneAction.size(), updatedItems.size());

        List<OrderLineItem> itemsWithNoneAndAddActions = List.of(getOrderLineItems(ActionType.NONE, null), getOrderLineItems(ActionType.ADD, null));
        updatedItems = ChangeOrderService.removeRemovedSubscriptionChargeItems(itemsWithNoneAndAddActions, Set.of());
        assertEquals(itemsWithNoneAndAddActions.size(), updatedItems.size());

        String externalSubscriptionChargeId = UUID.randomUUID().toString();
        List<OrderLineItem> itemsWithUpdateAction = List.of(
            getOrderLineItems(ActionType.NONE, externalSubscriptionChargeId),
            getOrderLineItems(ActionType.UPDATE, externalSubscriptionChargeId)
        );
        updatedItems = ChangeOrderService.removeRemovedSubscriptionChargeItems(itemsWithUpdateAction, Set.of(externalSubscriptionChargeId));
        assertEquals(itemsWithUpdateAction.size(), updatedItems.size());

        updatedItems = ChangeOrderService.removeRemovedSubscriptionChargeItems(itemsWithUpdateAction, Set.of());
        assertEquals(0, updatedItems.size());
    }

    private OrderLineItem getOrderLineItems(ActionType action, String externalSubscriptionChargeId) {
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setAction(action);
        orderLineItem.setBaseExternalSubscriptionChargeId(externalSubscriptionChargeId);
        return orderLineItem;
    }
}
