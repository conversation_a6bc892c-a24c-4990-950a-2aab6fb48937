package com.subskribe.billy.order.quotebuilder;

import com.subskribe.billy.order.quotebuilder.model.SlackQuoteBuilderEvent;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.test.BillyTestBase;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class SlackQuoteBuilderEventTest extends BillyTestBase {

    @Test
    void assertSlackAppMessageEvent1IsDeserializable() throws Exception {
        String serialized = asString(getClass(), "bot_message_event_payload_1.json");
        SlackQuoteBuilderEvent slackQuoteBuilderEvent = JacksonProvider.defaultMapper().readValue(serialized, SlackQuoteBuilderEvent.class);
        Assertions.assertThat(slackQuoteBuilderEvent).isNotNull();
        Assertions.assertThat(slackQuoteBuilderEvent.getEvent()).isNotNull();
        Assertions.assertThat(slackQuoteBuilderEvent.getEvent().getBotId()).isNotNull();
        Assertions.assertThat(slackQuoteBuilderEvent.getEvent().getBotProfile()).isNotNull();
    }

    @Test
    void assertSlackAppMessageEvent2IsDeserializable() throws Exception {
        String serialized = asString(getClass(), "bot_message_event_payload_2.json");
        SlackQuoteBuilderEvent slackQuoteBuilderEvent = JacksonProvider.defaultMapper().readValue(serialized, SlackQuoteBuilderEvent.class);
        Assertions.assertThat(slackQuoteBuilderEvent).isNotNull();
        Assertions.assertThat(slackQuoteBuilderEvent.getEvent()).isNotNull();
        Assertions.assertThat(slackQuoteBuilderEvent.getEvent().getBotId()).isNotNull();
        Assertions.assertThat(slackQuoteBuilderEvent.getEvent().getBotProfile()).isNotNull();
    }

    @Test
    void assertSlackUserMessageEventIsDeserializable() throws Exception {
        String serialized = asString(getClass(), "user_message_event_payload.json");
        SlackQuoteBuilderEvent slackQuoteBuilderEvent = JacksonProvider.defaultMapper().readValue(serialized, SlackQuoteBuilderEvent.class);
        Assertions.assertThat(slackQuoteBuilderEvent).isNotNull();
        Assertions.assertThat(slackQuoteBuilderEvent.getEvent()).isNotNull();
        Assertions.assertThat(slackQuoteBuilderEvent.getEvent().getBotId()).isNull();
        Assertions.assertThat(slackQuoteBuilderEvent.getEvent().getBotProfile()).isNull();
    }
}
