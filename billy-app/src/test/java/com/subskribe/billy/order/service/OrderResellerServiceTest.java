package com.subskribe.billy.order.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.model.AccountStub;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.order.services.OrderResellerService;
import java.util.Optional;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

public class OrderResellerServiceTest {

    @Rule
    public MockitoRule mockitoRule = MockitoJUnit.rule();

    @Mock
    private OrderGetService orderGetService;

    @Mock
    private AccountGetService accountGetService;

    @Mock
    private Order order;

    @Mock
    private AccountStub account;

    @Mock
    private AccountContact billingContact;

    private OrderResellerService orderResellerService;

    private static final String VALID_ORDER_ID = "order-123";
    private static final String ACCOUNT_ID = "account-123";
    private static final String RESELLER_ACCOUNT_ID = "reseller-account-123";
    private static final String BILLING_CONTACT_ID = "contact-123";

    @Before
    public void setUp() {
        orderResellerService = new OrderResellerService(orderGetService, accountGetService);

        when(order.getAccountId()).thenReturn(ACCOUNT_ID);
        when(account.getAccountId()).thenReturn(RESELLER_ACCOUNT_ID);
    }

    @Test
    public void testIsResellerOrderById_ValidResellerOrder() {
        when(orderGetService.getOrderByOrderId(VALID_ORDER_ID)).thenReturn(order);
        when(order.getBillingContactId()).thenReturn(BILLING_CONTACT_ID);
        when(accountGetService.getContact(BILLING_CONTACT_ID)).thenReturn(billingContact);
        when(billingContact.getAccountId()).thenReturn(RESELLER_ACCOUNT_ID);
        when(accountGetService.getAccountStub(RESELLER_ACCOUNT_ID)).thenReturn(Optional.of(account));

        boolean result = orderResellerService.isResellerOrder(VALID_ORDER_ID);

        assertTrue(result);
        verify(orderGetService).getOrderByOrderId(VALID_ORDER_ID);
        verify(accountGetService).getContact(BILLING_CONTACT_ID);
        verify(accountGetService).getAccountStub(RESELLER_ACCOUNT_ID);
    }

    @Test
    public void testIsResellerOrderById_NonResellerOrder() {
        when(orderGetService.getOrderByOrderId(VALID_ORDER_ID)).thenReturn(order);
        when(order.getBillingContactId()).thenReturn(BILLING_CONTACT_ID);
        when(accountGetService.getContact(BILLING_CONTACT_ID)).thenReturn(billingContact);
        when(billingContact.getAccountId()).thenReturn(ACCOUNT_ID);

        boolean result = orderResellerService.isResellerOrder(VALID_ORDER_ID);

        assertFalse(result);
        verify(orderGetService).getOrderByOrderId(VALID_ORDER_ID);
        verify(accountGetService).getContact(BILLING_CONTACT_ID);
        verify(accountGetService, never()).getAccountStub(anyString());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testIsResellerOrderById_BlankOrderId() {
        orderResellerService.isResellerOrder("");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testIsResellerOrderById_NullOrderId() {
        orderResellerService.isResellerOrder((String) null);
    }

    @Test
    public void testIsResellerOrder_ValidResellerOrder() {
        when(order.getBillingContactId()).thenReturn(BILLING_CONTACT_ID);
        when(accountGetService.getContact(BILLING_CONTACT_ID)).thenReturn(billingContact);
        when(billingContact.getAccountId()).thenReturn(RESELLER_ACCOUNT_ID);
        when(accountGetService.getAccountStub(RESELLER_ACCOUNT_ID)).thenReturn(Optional.of(account));
        boolean result = orderResellerService.isResellerOrder(order);

        assertTrue(result);
        verify(accountGetService).getContact(BILLING_CONTACT_ID);
        verify(accountGetService).getAccountStub(RESELLER_ACCOUNT_ID);
    }

    @Test
    public void testIsResellerOrder_NonResellerOrder() {
        when(order.getBillingContactId()).thenReturn(BILLING_CONTACT_ID);
        when(accountGetService.getContact(BILLING_CONTACT_ID)).thenReturn(billingContact);
        when(billingContact.getAccountId()).thenReturn(ACCOUNT_ID);

        boolean result = orderResellerService.isResellerOrder(order);

        assertFalse(result);
        verify(accountGetService).getContact(BILLING_CONTACT_ID);
        verify(accountGetService, never()).getAccountStub(anyString());
    }

    @Test
    public void testGetResellerAccount_ValidResellerOrder() {
        when(order.getBillingContactId()).thenReturn(BILLING_CONTACT_ID);
        when(accountGetService.getContact(BILLING_CONTACT_ID)).thenReturn(billingContact);
        when(billingContact.getAccountId()).thenReturn(RESELLER_ACCOUNT_ID);
        when(accountGetService.getAccountStub(RESELLER_ACCOUNT_ID)).thenReturn(Optional.of(account));
        Optional<AccountStub> result = orderResellerService.getResellerAccount(order);

        assertTrue(result.isPresent());
        assertEquals(account, result.get());
        verify(accountGetService).getContact(BILLING_CONTACT_ID);
        verify(accountGetService).getAccountStub(RESELLER_ACCOUNT_ID);
    }

    @Test
    public void testGetResellerAccount_NonResellerOrder() {
        when(order.getBillingContactId()).thenReturn(BILLING_CONTACT_ID);
        when(accountGetService.getContact(BILLING_CONTACT_ID)).thenReturn(billingContact);
        when(billingContact.getAccountId()).thenReturn(ACCOUNT_ID);

        Optional<AccountStub> result = orderResellerService.getResellerAccount(order);

        assertFalse(result.isPresent());
        verify(accountGetService).getContact(BILLING_CONTACT_ID);
        verify(accountGetService, never()).getAccountStub(anyString());
    }

    @Test
    public void testGetResellerAccount_NullBillingContactId() {
        when(order.getBillingContactId()).thenReturn(null);

        Optional<AccountStub> result = orderResellerService.getResellerAccount(order);

        assertFalse(result.isPresent());
        verify(accountGetService, never()).getContact(anyString());
        verify(accountGetService, never()).getAccountStub(anyString());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetResellerAccount_NullOrder() {
        orderResellerService.getResellerAccount(null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetResellerAccount_BlankAccountId() {
        when(order.getAccountId()).thenReturn("");

        orderResellerService.getResellerAccount(order);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetResellerAccount_NullAccountId() {
        when(order.getAccountId()).thenReturn(null);

        orderResellerService.getResellerAccount(order);
    }
}
