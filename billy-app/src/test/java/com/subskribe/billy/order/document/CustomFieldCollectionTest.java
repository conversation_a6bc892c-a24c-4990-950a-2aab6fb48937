package com.subskribe.billy.order.document;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldSource;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import java.util.List;
import java.util.Map;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class CustomFieldCollectionTest {

    @Test
    public void testAnyNonEmpty() {
        CustomField customFields1 = new CustomField(Map.of("plan 1", getCustomFieldValue("name 1", "value 1")));
        CustomField customFields2 = new CustomField(Map.of("plan 2", getCustomFieldValue("name 2", "")));
        CustomField customFields3 = new CustomField(Map.of("plan 3", getCustomFieldValue("name 3", null)));
        Map<String, CustomField> customFieldsByName = Map.of("plan 1", customFields1, "plan 2", customFields2, "plan 3", customFields3);
        var customFieldCollectionMap = CustomFieldCollection.getCustomFieldsMap(customFieldsByName);
        Assertions.assertThat(customFieldCollectionMap.get("name 1").anyHasValue()).isTrue();
        Assertions.assertThat(customFieldCollectionMap.get("name 2").anyHasValue()).isFalse();
        Assertions.assertThat(customFieldCollectionMap.get("name 3").anyHasValue()).isFalse();
    }

    @Test
    public void testStringValues() {
        CustomField customFields1 = new CustomField(Map.of("plan 1", getCustomFieldValue("name 1", "value 1")));
        CustomField customFields2 = new CustomField(Map.of("plan 2", getCustomFieldValue("name 1", "value 2")));
        CustomField customFields3 = new CustomField(Map.of("plan 3", getCustomFieldValue("name 3", "value 3")));
        Map<String, CustomField> customFieldsByName = Map.of("plan 1", customFields1, "plan 2", customFields2, "plan 3", customFields3);
        var customFieldCollectionMap = CustomFieldCollection.getCustomFieldsMap(customFieldsByName);
        Assertions.assertThat(customFieldCollectionMap.get("name 1").hasValue().get("value 1")).isTrue();
        Assertions.assertThat(customFieldCollectionMap.get("name 1").hasValue().get("value 2")).isTrue();
        Assertions.assertThat(customFieldCollectionMap.get("name 1").hasValue().get("value 3")).isNull();
        Assertions.assertThat(customFieldCollectionMap.get("name 1").isTrue()).isFalse();
        Assertions.assertThat(customFieldCollectionMap.get("name 2")).isNull();
        Assertions.assertThat(customFieldCollectionMap.get("name 3").hasValue().get("value 1")).isNull();
        Assertions.assertThat(customFieldCollectionMap.get("name 3").hasValue().get("value 2")).isNull();
        Assertions.assertThat(customFieldCollectionMap.get("name 3").hasValue().get("value 3")).isTrue();
    }

    @Test
    public void testDuplicateValues() {
        CustomField customFields1 = new CustomField(Map.of("plan 1", getCustomFieldValue("name 1", "value 1")));
        CustomField customFields2 = new CustomField(Map.of("plan 2", getCustomFieldValue("name 1", "value 1")));
        Map<String, CustomField> customFieldsByName = Map.of("plan 1", customFields1, "plan 2", customFields2);
        var customFieldCollectionMap = CustomFieldCollection.getCustomFieldsMap(customFieldsByName);
        Assertions.assertThat(customFieldCollectionMap.get("name 1").hasValue().get("value 1")).isTrue();
        Assertions.assertThat(customFieldCollectionMap.get("name 1").hasValue().get("value 1")).isTrue();
    }

    @Test
    public void testTruthyValues() {
        CustomField customFields1 = new CustomField(Map.of("plan 1", getCustomFieldValue("name 1", "true")));
        CustomField customFields2 = new CustomField(Map.of("plan 2", getCustomFieldValue("name 1", "false")));
        CustomField customFields3 = new CustomField(Map.of("plan 3", getCustomFieldValue("name 3", "yes")));
        CustomField customFields4 = new CustomField(Map.of("plan 4", getCustomFieldValue("name 4", "anything")));
        Map<String, CustomField> customFieldsByName = Map.of(
            "plan 1",
            customFields1,
            "plan 2",
            customFields2,
            "plan 3",
            customFields3,
            "plan 4",
            customFields4
        );
        var customFieldCollectionMap = CustomFieldCollection.getCustomFieldsMap(customFieldsByName);
        Assertions.assertThat(customFieldCollectionMap.get("name 1").isTrue()).isTrue();
        Assertions.assertThat(customFieldCollectionMap.get("name 2")).isNull();
        Assertions.assertThat(customFieldCollectionMap.get("name 3").isTrue()).isTrue();
        Assertions.assertThat(customFieldCollectionMap.get("name 4").isTrue()).isFalse();
    }

    private CustomFieldValue getCustomFieldValue(String name, String value) {
        return getCustomFieldValue(CustomFieldType.STRING, name, value, List.of());
    }

    private CustomFieldValue getCustomFieldValue(CustomFieldType type, String name, String value, List<String> selections) {
        return new CustomFieldValue(type, name, name, value, selections, selections, false, CustomFieldSource.USER, null);
    }
}
