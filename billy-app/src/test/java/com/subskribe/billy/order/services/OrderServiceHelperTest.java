package com.subskribe.billy.order.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.discount.FixedAmountDiscountConfiguration;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.tax.service.TaxService;
import com.subskribe.billy.metrics.model.Metrics;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.pecuniary.DiscountDetail;
import com.subskribe.billy.shared.pecuniary.Numbers;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class OrderServiceHelperTest {

    private Order order;
    private AccountGetService mockAccountGetService;
    private OrderValidationService orderValidationService;

    private static final String ACCOUNT_ID = "ACCT-1";
    private static final String CONTACT_ID = "CONT-1";

    @BeforeEach
    void testSetup() {
        order = new Order();
        order.setAccountId(ACCOUNT_ID);
        order.setBillingContactId(CONTACT_ID);
        order.setShippingContactId(CONTACT_ID);
        order.setStatus(OrderStatus.DRAFT);

        Account account = new Account();
        account.setAccountId(ACCOUNT_ID);
        mockAccountGetService = mock(AccountGetService.class);
        when(mockAccountGetService.getAccount(ACCOUNT_ID)).thenReturn(account);

        TaxService mockTaxService = mock(TaxService.class);
        orderValidationService = new OrderValidationService(mockAccountGetService, mockTaxService);
    }

    @Test
    public void consolidateOrderLineItemsByBaseExternalSubscriptionChargeId() {
        List<OrderLineItem> orderLineItemList = new ArrayList<>();
        String ORDER_LINE_1_ID = UUID.randomUUID().toString();
        String ORDER_LINE_2_ID = UUID.randomUUID().toString();
        String TEST_CHARGE_ID = UUID.randomUUID().toString();

        OrderLineItem orderLineItem1 = new OrderLineItem();
        orderLineItem1.setBaseExternalSubscriptionChargeId(TEST_CHARGE_ID);
        orderLineItem1.setQuantity(-10000);
        orderLineItem1.setAmount(BigDecimal.valueOf(100000.00));
        orderLineItem1.setListAmount(BigDecimal.valueOf(-100000.00));
        orderLineItem1.setDiscountAmount(orderLineItem1.getListAmount().subtract(orderLineItem1.getAmount()));
        orderLineItem1.setOrderLineId(ORDER_LINE_1_ID);
        orderLineItem1.setAction(ActionType.UPDATE);
        orderLineItemList.add(orderLineItem1);

        OrderLineItem orderLineItem2 = new OrderLineItem();
        orderLineItem2.setBaseExternalSubscriptionChargeId(TEST_CHARGE_ID);
        orderLineItem2.setQuantity(20000);
        orderLineItem2.setAmount(BigDecimal.valueOf(100000.00));
        orderLineItem2.setListAmount(BigDecimal.valueOf(200000.00));
        orderLineItem2.setDiscountAmount(orderLineItem2.getListAmount().subtract(orderLineItem2.getAmount()));
        orderLineItem2.setOrderLineId(ORDER_LINE_2_ID);
        orderLineItem2.setAction(ActionType.UPDATE);
        orderLineItemList.add(orderLineItem2);

        Map<String, Metrics> orderLineMetrics = new HashMap<>();
        orderLineMetrics.put(
            ORDER_LINE_1_ID,
            new Metrics(
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.ZERO,
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                null
            )
        );

        orderLineMetrics.put(
            ORDER_LINE_2_ID,
            new Metrics(
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.ZERO,
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(100.00),
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                null
            )
        );

        assertEquals(2, orderLineItemList.size());
        assertEquals(2, orderLineMetrics.size());

        orderLineItemList = OrderServiceHelper.consolidateOrderLineItemsByBaseExternalSubscriptionChargeId(orderLineItemList, orderLineMetrics);

        assertEquals(1, orderLineItemList.size());
        assertEquals(1, orderLineMetrics.size());
        assertTrue(Numbers.equals(BigDecimal.valueOf(-100000), orderLineItemList.get(0).getDiscountAmount()));
    }

    @Test
    public void canOrderContactsBeNullReturnsTrueIfOrderStatusIsDraft() {
        assertTrue(orderValidationService.canOrderContactsBeNullable(order));
    }

    @Test
    public void canOrderContactsBeNullReturnsTrueIfOrderStatusIsNull() {
        order.setStatus(null);
        assertTrue(orderValidationService.canOrderContactsBeNullable(order));
    }

    @Test
    public void canOrderContactsBeNullReturnsTrueIfOrderStatusIsSubmitted() {
        order.setStatus(OrderStatus.SUBMITTED);
        assertTrue(orderValidationService.canOrderContactsBeNullable(order));
    }

    @Test
    public void canOrderContactsBeNullReturnsTrueIfOrderStatusIsApproved() {
        order.setStatus(OrderStatus.APPROVED);
        assertTrue(orderValidationService.canOrderContactsBeNullable(order));
    }

    @Test
    public void isAnyOrderContactBlankReturnsFalseIfBothBillingAndShippingContactsArePresent() {
        assertFalse(orderValidationService.isAnyOrderContactBlank(order));
    }

    @Test
    public void isAnyOrderContactBlankReturnsTrueIfBillingContactIsNull() {
        order.setBillingContactId(null);
        assertTrue(orderValidationService.isAnyOrderContactBlank(order));
    }

    @Test
    public void isAnyOrderContactBlankReturnsTrueIfShippingContactIsNull() {
        order.setShippingContactId(null);
        assertTrue(orderValidationService.isAnyOrderContactBlank(order));
    }

    @Test
    public void isAnyOrderContactBlankReturnsTrueIfBothBillingAndShippingContactsAreNull() {
        order.setBillingContactId(null);
        order.setShippingContactId(null);
        assertTrue(orderValidationService.isAnyOrderContactBlank(order));
    }

    @Test
    public void validateAccountDetailsThrowsIfAccountIsNull() {
        order.setAccountId(null);
        assertThrows(IllegalArgumentException.class, () -> orderValidationService.validateAccountDetails(order));
    }

    @Test
    public void validateAccountDetailsThrowsIfBillingAndShippingContactsAreMissingForExecutedOrder() {
        order.setBillingContactId(null);
        order.setShippingContactId(null);
        order.setStatus(OrderStatus.EXECUTED);
        assertThrows(IllegalArgumentException.class, () -> orderValidationService.validateAccountDetails(order));
    }

    @Test
    public void validateCrmIdMissingOnCompositeOrdersDoesntThrow() {
        order.setCompositeOrderId("CORD-12345");
        order.setSfdcOpportunityId(null);
        OrderServiceHelper.checkCrmIdIsEmptyForCompositeOrders(order);

        order.setSfdcOpportunityId(StringUtils.EMPTY);
        OrderServiceHelper.checkCrmIdIsEmptyForCompositeOrders(order);
    }

    @Test
    public void validateCrmIdPresentOnCompositeOrdersThrows() {
        order.setCompositeOrderId("CORD-12345");
        order.setSfdcOpportunityId("OPP-123");
        assertThrows(IllegalArgumentException.class, () -> OrderServiceHelper.checkCrmIdIsEmptyForCompositeOrders(order));
    }

    @Test
    public void filterZeroPercentDiscounts() {
        var orderLineItem = new OrderLineItem();

        List<DiscountDetail> discountDetails = new ArrayList<>();
        var discount1 = new DiscountDetail();
        discount1.setPercent(BigDecimal.ZERO);
        discountDetails.add(discount1);
        var discount2 = new DiscountDetail();
        discount2.setPercent(new BigDecimal("0.1"));
        discountDetails.add(discount2);

        orderLineItem.setDiscounts(discountDetails);

        var discountConfig = new FixedAmountDiscountConfiguration();
        discountConfig.setEnabled(false);

        OrderDiscountService.validateDiscountList(orderLineItem, discountConfig);

        assertEquals(1, orderLineItem.getDiscounts().size());
    }

    @Test
    public void validateDraftOrderWithoutContacts() {
        Order order = new Order();
        order.setStatus(OrderStatus.DRAFT);

        orderValidationService.validateContactsOnOrder(order);
        Mockito.verify(mockAccountGetService, never()).getAccount(any());
    }

    @Test
    public void validateSubmittedOrderWithoutContacts() {
        Order order = new Order();
        order.setStatus(OrderStatus.SUBMITTED);

        assertThrows(InvalidInputException.class, () -> orderValidationService.validateContactsOnOrder(order));
        Mockito.verify(mockAccountGetService, never()).getAccount(any());
    }
}
