package com.subskribe.billy.order.document;

import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.shared.document.OrderTemplateDataJson;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.test.BillyTestBase;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class OrderTemplateDataToJsonMapperTest extends BillyTestBase {

    @Test
    void mapOrderTemplateDataToJson() throws Exception {
        OrderTemplateData orderTemplateData = new OrderTemplateData(getOrderDocumentJson());
        OrderTemplateDataJson orderTemplateDataJson = OrderTemplateDataToJsonMapper.mapOrderTemplateDataToJson(orderTemplateData);

        Assertions.assertThat(orderTemplateDataJson.getOrder().getId()).isEqualTo(orderTemplateData.getOrderId());
        Assertions.assertThat(orderTemplateDataJson.getAccount()).isEqualTo(orderTemplateData.getOrderDocumentJson().getOrderDetail().getAccount());

        for (OrderLineItemDetail lineItem : orderTemplateData.getOrderDocumentJson().getOrderDetail().getLineItems()) {
            Assertions.assertThat(orderTemplateDataJson.planByPlanId().get(lineItem.getPlan().getId())).isNotNull();
            Assertions.assertThat(orderTemplateDataJson.planByPlanId().get(lineItem.getPlan().getId()).getId()).isEqualTo(lineItem.getPlan().getId());

            Assertions.assertThat(orderTemplateDataJson.chargeByChargeId().get(lineItem.getCharge().getId())).isNotNull();
            Assertions.assertThat(orderTemplateDataJson.chargeByChargeId().get(lineItem.getCharge().getId()).getId()).isEqualTo(
                lineItem.getCharge().getId()
            );
        }

        assertOrderTemplateDataJsonIsSerializable(orderTemplateDataJson);
    }

    private void assertOrderTemplateDataJsonIsSerializable(OrderTemplateDataJson orderTemplateDataJson) throws Exception {
        String serialized = JacksonProvider.emptyFieldExcludingMapper().writeValueAsString(orderTemplateDataJson);
        Assertions.assertThat(serialized).isNotEmpty();
    }

    private OrderDocumentJson getOrderDocumentJson() throws Exception {
        return JacksonProvider.emptyFieldExcludingMapper().readValue(getSerializedOrderDocumentJson(), OrderDocumentJson.class);
    }

    private String getSerializedOrderDocumentJson() throws Exception {
        return asString(getClass(), "order_document_json.json");
    }
}
