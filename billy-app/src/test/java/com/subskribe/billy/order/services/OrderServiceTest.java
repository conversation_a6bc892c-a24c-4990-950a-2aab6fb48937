package com.subskribe.billy.order.services;

import static com.subskribe.billy.TestConstants.TIME_ZONE;
import static com.subskribe.billy.TestConstants.ZONE_ID;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Recurrence;
import java.time.Instant;
import java.time.ZonedDateTime;
import org.junit.jupiter.api.Test;

class OrderServiceTest {

    private static final Instant startDate = ZonedDateTime.of(2025, 1, 1, 0, 0, 0, 0, ZONE_ID).toInstant();

    @Test
    public void calculateEndDateFromTermLength() {
        Order order = getOrder(new Recurrence(Cycle.YEAR, 1));
        OrderService.validateAndSetOrderEndDateFromTermLength(order, TIME_ZONE);
        assertEquals(DateTimeCalculator.plusYears(ZONE_ID, startDate, 1), order.getEndDate());

        order = getOrder(new Recurrence(Cycle.MONTH, 3));
        OrderService.validateAndSetOrderEndDateFromTermLength(order, TIME_ZONE);
        assertEquals(DateTimeCalculator.plusMonths(ZONE_ID, startDate, 3), order.getEndDate());

        order = getOrder(new Recurrence(Cycle.DAY, 1095));
        OrderService.validateAndSetOrderEndDateFromTermLength(order, TIME_ZONE);
        assertEquals(DateTimeCalculator.plusDays(ZONE_ID, startDate, 1095), order.getEndDate());
    }

    @Test
    public void incompatibleTermLengthAndEndDate() {
        Order order = getOrder(new Recurrence(Cycle.YEAR, 1));
        order.setEndDate(DateTimeCalculator.plusMonths(ZONE_ID, startDate, 13));
        assertThrows(InvalidInputException.class, () -> OrderService.validateAndSetOrderEndDateFromTermLength(order, TIME_ZONE));
    }

    @Test
    public void invalidTermLengths() {
        Order order = getOrder(new Recurrence(Cycle.YEAR, 0));
        assertThrows(InvalidInputException.class, () -> OrderService.validateAndSetOrderEndDateFromTermLength(order, TIME_ZONE));

        order.setTermLength(new Recurrence(Cycle.CUSTOM, 1));
        assertThrows(UnsupportedOperationException.class, () -> OrderService.validateAndSetOrderEndDateFromTermLength(order, TIME_ZONE));

        order.setTermLength(new Recurrence(Cycle.PAID_IN_FULL, 1));
        assertThrows(UnsupportedOperationException.class, () -> OrderService.validateAndSetOrderEndDateFromTermLength(order, TIME_ZONE));
    }

    private Order getOrder(Recurrence termLength) {
        Order order = new Order();
        order.setStartDate(startDate);
        order.setTermLength(termLength);
        return order;
    }
}
