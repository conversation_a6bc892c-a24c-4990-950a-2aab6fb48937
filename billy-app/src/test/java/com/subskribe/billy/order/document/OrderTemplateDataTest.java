package com.subskribe.billy.order.document;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.graphql.order.OrderDetail;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.graphql.productcatalog.ChargeDetail;
import com.subskribe.billy.graphql.shared.PredefinedDiscountLineItemDetail;
import com.subskribe.billy.productcatalog.priceattribute.model.ImmutablePriceAttribute;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttributeType;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanJson;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.resources.json.shared.DiscountDetailJson;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class OrderTemplateDataTest {

    @Test
    public void whenNullAttributeReferencesArePresent_thenEmptyPriceAttributeData() {
        OrderLineItemDetail orderLineItemDetail = new OrderLineItemDetail();
        List<PriceAttributeData> priceAttributeData = OrderTemplateData.getPriceAttributeData(Map.of(), orderLineItemDetail);
        Assertions.assertThat(priceAttributeData).isNotNull();
        Assertions.assertThat(priceAttributeData).isEmpty();
    }

    @Test
    public void whenPriceAttributeMapHasNoData_thenEmptyNameInPriceAttributeData() {
        OrderLineItemDetail orderLineItemDetail = new OrderLineItemDetail();
        String attributeId = RandomStringUtils.randomAlphanumeric(5).toUpperCase();
        String attributeValue = RandomStringUtils.randomAlphanumeric(5).toUpperCase();
        orderLineItemDetail.setAttributeReferences(
            List.of(AttributeReference.builder().attributeDefinitionId(attributeId).attributeValue(attributeValue).build())
        );
        List<PriceAttributeData> priceAttributeData = OrderTemplateData.getPriceAttributeData(Map.of(), orderLineItemDetail);
        Assertions.assertThat(priceAttributeData).hasSize(1);
        Assertions.assertThat(priceAttributeData.get(0).name()).isEmpty();
        Assertions.assertThat(priceAttributeData.get(0).value()).isEqualTo(attributeValue);
    }

    @Test
    public void whenPriceAttributeMapHasData_thenPriceAttributeDataIsCorrect() {
        OrderLineItemDetail orderLineItemDetail = new OrderLineItemDetail();
        String attributeId = RandomStringUtils.randomAlphanumeric(5).toUpperCase();
        String attributeValue = RandomStringUtils.randomAlphanumeric(5).toUpperCase();
        orderLineItemDetail.setAttributeReferences(
            List.of(AttributeReference.builder().attributeDefinitionId(attributeId).attributeValue(attributeValue).build())
        );
        Map<String, PriceAttribute> priceAttributeMap = Map.of(
            attributeId,
            ImmutablePriceAttribute.builder()
                .id(attributeId)
                .name("test-name")
                .type(PriceAttributeType.PRICE_TABLE_RESOLVING)
                .values(List.of(attributeValue))
                .build()
        );
        List<PriceAttributeData> priceAttributeData = OrderTemplateData.getPriceAttributeData(priceAttributeMap, orderLineItemDetail);
        Assertions.assertThat(priceAttributeData).hasSize(1);
        Assertions.assertThat(priceAttributeData.get(0).name()).isEqualTo("test-name");
        Assertions.assertThat(priceAttributeData.get(0).value()).isEqualTo(attributeValue);
    }

    @Test
    public void testFormatNumber() {
        OrderTemplateData orderTemplateData = getOrderTemplateData();
        Function<String, String> formatNumber = orderTemplateData.formatNumber();
        assertEquals("1,000,000", formatNumber.apply("1000000"));
        assertEquals("1,000", formatNumber.apply("1000"));
        assertEquals("100", formatNumber.apply("100"));
        assertEquals("not a number", formatNumber.apply("not a number"));
    }

    @Test
    public void testOrderTemplatePlanDataInDifferentTemplateLines() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithLines();
        for (OrderTemplateLineItem orderTemplateLineItem : orderTemplateData.getLineItems()) {
            validateLineItemOrderTemplatePlanIsNotNull(orderTemplateLineItem);
        }

        List<OrderTemplateLineItem> formattedLineItems = orderTemplateData
            .getFormattedDateToLineItems()
            .values()
            .stream()
            .flatMap(List::stream)
            .toList();
        for (OrderTemplateLineItem orderTemplateLineItem : formattedLineItems) {
            validateLineItemOrderTemplatePlanIsNotNull(orderTemplateLineItem);
        }

        List<OrderTemplateLineItem> executedLineItems = orderTemplateData.getExecutedLineItems();
        for (OrderTemplateLineItem orderTemplateLineItem : executedLineItems) {
            validateLineItemOrderTemplatePlanIsNotNull(orderTemplateLineItem);
        }
    }

    @Test
    public void testOrderTemplateOpportunityCustomFieldsIsNotNull() {
        OrderTemplateData orderTemplateData = getOrderTemplateData();
        assertNotNull(orderTemplateData.getOpportunityCustomFields());
    }

    @Test
    public void testOrderTemplateOpportunityCustomFieldsPopulatedAsNeeded() {
        OrderTemplateData orderTemplateData = getOrderTemplateData();
        orderTemplateData.getOrderDocumentJson().setOpportunityCustomFields(getCustomField());
        assertNotNull(orderTemplateData.getOpportunityCustomFields());
        assertEquals(2, orderTemplateData.getOpportunityCustomFields().size());
    }

    @Test
    public void checkOrderLineActiveOnDateReturnsTrueForDateMatchingLineItemEffectiveDate() {
        OrderLineItemDetail lineItemDetail = getOrderLineDetail(ChargeType.RECURRING, null, null);
        lineItemDetail.setEffectiveDate(1000L);
        lineItemDetail.setEndDate(2000L);

        assertTrue(OrderTemplateData.checkOrderLineItemActiveOnDate(lineItemDetail, 1000L));
    }

    @Test
    public void checkOrderLineActiveOnDateReturnsTrueForDateWithinLineItemDates() {
        OrderLineItemDetail lineItemDetail = getOrderLineDetail(ChargeType.RECURRING, null, null);
        lineItemDetail.setEffectiveDate(1000L);
        lineItemDetail.setEndDate(2000L);

        assertTrue(OrderTemplateData.checkOrderLineItemActiveOnDate(lineItemDetail, 1500L));
    }

    @Test
    public void checkOrderLineActiveOnDateReturnsFalseForDateBeforeLineItemStartDate() {
        OrderLineItemDetail lineItemDetail = getOrderLineDetail(ChargeType.RECURRING, null, null);
        lineItemDetail.setEffectiveDate(1000L);
        lineItemDetail.setEndDate(2000L);

        assertFalse(OrderTemplateData.checkOrderLineItemActiveOnDate(lineItemDetail, 500L));
    }

    @Test
    public void checkOrderLineActiveOnDateReturnsFalseForDateOnLineItemEndDate() {
        OrderLineItemDetail lineItemDetail = getOrderLineDetail(ChargeType.RECURRING, null, null);
        lineItemDetail.setEffectiveDate(1000L);
        lineItemDetail.setEndDate(2000L);

        assertFalse(OrderTemplateData.checkOrderLineItemActiveOnDate(lineItemDetail, 2000L));
    }

    @Test
    public void checkOrderLineActiveOnDateReturnsFalseForDateAfterLineItemEndDate() {
        OrderLineItemDetail lineItemDetail = getOrderLineDetail(ChargeType.RECURRING, null, null);
        lineItemDetail.setEffectiveDate(1000L);
        lineItemDetail.setEndDate(2000L);

        assertFalse(OrderTemplateData.checkOrderLineItemActiveOnDate(lineItemDetail, 2001L));
    }

    @Test
    public void checkGetPercentOfChargeNetPercentForRecurringLineShowsEmpty() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithLines();
        for (OrderTemplateLineItem orderTemplateLineItem : orderTemplateData.getLineItems()) {
            assertTrue(orderTemplateLineItem.getPercentOfChargePercent().isEmpty());
            assertTrue(orderTemplateLineItem.getPercentOfChargeNetPercent().isEmpty());
        }
    }

    @Test
    public void checkGetPercentOfChargeNetPercentWithoutDiscountShowsFullPercent() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLine(null, null);
        assertEquals("10", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfChargeNetPercentWithLineDiscountShowsCorrect() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLine(BigDecimal.valueOf(0.5), null);
        assertEquals("5", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfChargeNetPercentWithPredefinedDiscountShowsCorrect() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLine(null, BigDecimal.valueOf(0.5));
        assertEquals("5", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfChargeNetPercentWithBothDiscountsShowsCorrect() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLine(BigDecimal.valueOf(0.5), BigDecimal.valueOf(0.5));
        assertEquals("2.5", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfChargePercentForPercentageOfChargeShowsCorrect() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLine(null, null);
        assertEquals("10", orderTemplateData.getLineItems().get(0).getPercentOfChargePercent());
    }

    @Test
    public void checkGetPercentOfChargePercentWithDiscountsRemainsSame() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLine(BigDecimal.valueOf(0.5), BigDecimal.valueOf(0.5));
        assertEquals("10", orderTemplateData.getLineItems().get(0).getPercentOfChargePercent());
    }

    @Test
    public void checkGetPercentOfEffectivePercentWithPriceOverrideShowsCorrect() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLineAndPriceOverride(BigDecimal.valueOf(1.5), null, null);
        assertEquals("15", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfEffectivePercentWithPriceOverrideAndLineDiscountShowsCorrect() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLineAndPriceOverride(
            BigDecimal.valueOf(1.5),
            BigDecimal.valueOf(0.2),
            null
        );
        assertEquals("12", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfEffectivePercentWithPriceOverrideAndPredefinedDiscountShowsCorrect() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLineAndPriceOverride(
            BigDecimal.valueOf(0.5),
            null,
            BigDecimal.valueOf(0.3)
        );
        assertEquals("3.5", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfEffectivePercentWithPriceOverrideAndBothDiscountsShowsCorrect() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLineAndPriceOverride(
            BigDecimal.valueOf(2.0),
            BigDecimal.valueOf(0.25),
            BigDecimal.valueOf(0.1)
        );
        assertEquals("13.5", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfChargePercentWithPriceOverrideRemainsSame() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLineAndPriceOverride(BigDecimal.valueOf(1.5), null, null);
        assertEquals("10", orderTemplateData.getLineItems().get(0).getPercentOfChargePercent());
    }

    @Test
    public void checkGetPercentOfEffectivePercentWithZeroPriceOverrideShowsZero() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLineAndPriceOverride(BigDecimal.ZERO, null, null);
        assertEquals("0", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfEffectivePercentWithNullPriceOverrideShowsOriginal() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLineAndPriceOverride(null, null, null);
        assertEquals("10", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfEffectivePercentWithFractionalPriceOverrideShowsCorrect() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLineAndPriceOverride(BigDecimal.valueOf(0.25), null, null);
        assertEquals("2.5", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void checkGetPercentOfEffectivePercentWithHighDiscountAndPriceOverrideShowsCorrect() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPercentOfChargeLineAndPriceOverride(
            BigDecimal.valueOf(3.0),
            BigDecimal.valueOf(0.9),
            null
        );
        assertEquals("3", orderTemplateData.getLineItems().get(0).getPercentOfChargeNetPercent());
    }

    @Test
    public void testGetOrderProducts_withValidLineItems_returnsCorrectProducts() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithLines();

        List<OrderTemplateProduct> products = orderTemplateData.getOrderProducts();

        assertNotNull(products);
        assertTrue(products.size() > 0);

        for (OrderTemplateProduct product : products) {
            assertNotNull(product.getProductId());
            assertNotNull(product.getProductName());
            assertNotNull(product.getProductPlans());
            assertTrue(product.getProductPlans().size() > 0);
        }
    }

    @Test
    public void testGetOrderProducts_withEmptyLineItems_returnsEmptyList() {
        OrderTemplateData orderTemplateData = getOrderTemplateData();

        List<OrderTemplateProduct> products = orderTemplateData.getOrderProducts();

        assertNotNull(products);
        assertTrue(products.isEmpty());
    }

    @Test
    public void testGetOrderProducts_groupsLineItemsByProduct() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithMultipleProductsAndPlans();

        List<OrderTemplateProduct> products = orderTemplateData.getOrderProducts();

        assertNotNull(products);
        assertEquals(2, products.size()); // We create 2 unique products

        OrderTemplateProduct product1 = products.stream().filter(p -> p.getProductId().equals("PRODUCT-1")).findFirst().orElseThrow();
        assertEquals("Product 1", product1.getProductName());
        assertEquals(2, product1.getProductPlans().size()); // 2 plans for product 1

        OrderTemplateProduct product2 = products.stream().filter(p -> p.getProductId().equals("PRODUCT-2")).findFirst().orElseThrow();
        assertEquals("Product 2", product2.getProductName());
        assertEquals(1, product2.getProductPlans().size()); // 1 plan for product 2
    }

    @Test
    public void testGetOrderProductsForExecutedLineItems_withValidExecutedLineItems_returnsCorrectProducts() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithExecutedLineItems();

        List<OrderTemplateProduct> products = orderTemplateData.getOrderProductsForExecutedLineItems();

        assertNotNull(products);
        assertTrue(products.size() > 0);

        for (OrderTemplateProduct product : products) {
            assertNotNull(product.getProductId());
            assertNotNull(product.getProductName());
            assertNotNull(product.getProductPlans());
            assertTrue(product.getProductPlans().size() > 0);
        }
    }

    @Test
    public void testGetOrderProductsForExecutedLineItems_withEmptyExecutedLineItems_returnsEmptyList() {
        OrderTemplateData orderTemplateData = getOrderTemplateData();

        List<OrderTemplateProduct> products = orderTemplateData.getOrderProductsForExecutedLineItems();

        assertNotNull(products);
        assertTrue(products.isEmpty());
    }

    @Test
    public void testGetOrderProductsForPostSubscriptionLineItems_withValidPostSubscriptionLineItems_returnsCorrectProducts() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithPostSubscriptionLineItems();

        List<OrderTemplateProduct> products = orderTemplateData.getOrderProductsForPostSubscriptionLineItems();

        assertNotNull(products);
        assertTrue(products.size() > 0);

        for (OrderTemplateProduct product : products) {
            assertNotNull(product.getProductId());
            assertNotNull(product.getProductName());
            assertNotNull(product.getProductPlans());
            assertTrue(product.getProductPlans().size() > 0);
        }
    }

    @Test
    public void testGetOrderProductsForPostSubscriptionLineItems_withEmptyPostSubscriptionLineItems_returnsEmptyList() {
        OrderTemplateData orderTemplateData = getOrderTemplateData();

        List<OrderTemplateProduct> products = orderTemplateData.getOrderProductsForPostSubscriptionLineItems();

        assertNotNull(products);
        assertTrue(products.isEmpty());
    }

    @Test
    public void testGetOrderProducts_preservesProductAndPlanMetadata() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithDetailedProducts();

        List<OrderTemplateProduct> products = orderTemplateData.getOrderProducts();

        assertNotNull(products);
        assertEquals(1, products.size());

        OrderTemplateProduct product = products.get(0);
        assertEquals("PRODUCT-DETAILED", product.getProductId());
        assertEquals("Detailed Product", product.getProductName());
        assertEquals("SKU-123", product.getSku());
        assertEquals("This is a detailed product description", product.getProductDescription());

        List<OrderTemplatePlan> plans = product.getProductPlans();
        assertEquals(1, plans.size());
        OrderTemplatePlan plan = plans.get(0);
        assertEquals("Detailed Plan", plan.getName());
        assertEquals("This is a detailed plan description", plan.getDescription());
    }

    @Test
    public void testGetOrderProducts_handlesProductWithDisplayNameAndPlanWithDisplayName() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithDisplayNames();

        List<OrderTemplateProduct> products = orderTemplateData.getOrderProducts();

        assertNotNull(products);
        assertEquals(1, products.size());

        OrderTemplateProduct product = products.get(0);
        assertEquals("Display Product Name", product.getProductName());

        List<OrderTemplatePlan> plans = product.getProductPlans();
        assertEquals(1, plans.size());
        OrderTemplatePlan plan = plans.get(0);
        assertEquals("Display Plan Name", plan.getName());
    }

    @Test
    public void testAllThreeMethodsReturnConsistentStructure() {
        OrderTemplateData orderTemplateData = getOrderTemplateDataWithAllLineItemTypes();

        List<OrderTemplateProduct> regularProducts = orderTemplateData.getOrderProducts();
        List<OrderTemplateProduct> executedProducts = orderTemplateData.getOrderProductsForExecutedLineItems();
        List<OrderTemplateProduct> postSubProducts = orderTemplateData.getOrderProductsForPostSubscriptionLineItems();

        assertNotNull(regularProducts);
        assertNotNull(executedProducts);
        assertNotNull(postSubProducts);

        assertTrue(regularProducts.size() > 0);
        assertTrue(executedProducts.size() > 0);
        assertTrue(postSubProducts.size() > 0);

        verifyProductListStructure(regularProducts);
        verifyProductListStructure(executedProducts);
        verifyProductListStructure(postSubProducts);
    }

    private void verifyProductListStructure(List<OrderTemplateProduct> products) {
        for (OrderTemplateProduct product : products) {
            assertNotNull(product.getProductId());
            assertNotNull(product.getProductName());
            assertNotNull(product.getProductPlans());
            assertTrue(product.getProductPlans().size() > 0);

            for (OrderTemplatePlan plan : product.getProductPlans()) {
                assertNotNull(plan.getId());
                assertNotNull(plan.getName());
                assertNotNull(plan.getPlanLineItems());
                assertTrue(plan.getPlanLineItems().size() > 0);
            }
        }
    }

    private static void validateLineItemOrderTemplatePlanIsNotNull(OrderTemplateLineItem orderTemplateLineItem) {
        assertNotNull(orderTemplateLineItem.getPlan());
        assertNotNull(orderTemplateLineItem.getPlan().getPlanLineItems());
        assertEquals(1, orderTemplateLineItem.getPlan().getPlanLineItems().size());
    }

    private static OrderTemplateData getOrderTemplateData() {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");
        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);
        return new OrderTemplateData(orderDocumentJson);
    }

    private static OrderTemplateData getOrderTemplateDataWithLines() {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");
        orderDetail.setLineItems(
            List.of(
                getOrderLineDetail(ChargeType.RECURRING, null, null),
                getOrderLineDetail(ChargeType.RECURRING, null, null),
                getOrderLineDetail(ChargeType.RECURRING, null, null)
            )
        );
        orderDetail.setLineItemsNetEffect(orderDetail.getLineItems());

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);

        List<String> productIds = orderDetail.getLineItems().stream().map(lineItem -> lineItem.getPlan().getProductId()).distinct().toList();
        Map<String, ProductJson> productJsonMap = productIds
            .stream()
            .collect(
                Collectors.toMap(Function.identity(), id -> {
                    ProductJson productJson = new ProductJson();
                    productJson.setId(id);
                    productJson.setName(RandomStringUtils.randomAlphanumeric(30));
                    productJson.setDescription(RandomStringUtils.randomAlphanumeric(50));
                    return productJson;
                })
            );
        orderDocumentJson.setProducts(productJsonMap);

        return new OrderTemplateData(orderDocumentJson);
    }

    private static OrderTemplateData getOrderTemplateDataWithPercentOfChargeLine(
        BigDecimal lineDiscountPercent,
        BigDecimal predefinedDiscountPercent
    ) {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");
        orderDetail.setLineItems(List.of(getOrderLineDetail(ChargeType.PERCENTAGE_OF, lineDiscountPercent, predefinedDiscountPercent)));
        orderDetail.setLineItemsNetEffect(orderDetail.getLineItems());

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);

        List<String> productIds = orderDetail.getLineItems().stream().map(lineItem -> lineItem.getPlan().getProductId()).distinct().toList();
        Map<String, ProductJson> productJsonMap = productIds
            .stream()
            .collect(
                Collectors.toMap(Function.identity(), id -> {
                    ProductJson productJson = new ProductJson();
                    productJson.setId(id);
                    productJson.setName(RandomStringUtils.randomAlphanumeric(30));
                    productJson.setDescription(RandomStringUtils.randomAlphanumeric(50));
                    return productJson;
                })
            );
        orderDocumentJson.setProducts(productJsonMap);

        return new OrderTemplateData(orderDocumentJson);
    }

    private static OrderTemplateData getOrderTemplateDataWithPercentOfChargeLineAndPriceOverride(
        BigDecimal priceOverrideRatio,
        BigDecimal lineDiscountPercent,
        BigDecimal predefinedDiscountPercent
    ) {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");
        OrderLineItemDetail lineDetail = getOrderLineDetail(ChargeType.PERCENTAGE_OF, lineDiscountPercent, predefinedDiscountPercent);
        lineDetail.setListPriceOverrideRatio(priceOverrideRatio);
        orderDetail.setLineItems(List.of(lineDetail));
        orderDetail.setLineItemsNetEffect(orderDetail.getLineItems());

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);

        List<String> productIds = orderDetail.getLineItems().stream().map(lineItem -> lineItem.getPlan().getProductId()).distinct().toList();
        Map<String, ProductJson> productJsonMap = productIds
            .stream()
            .collect(
                Collectors.toMap(Function.identity(), id -> {
                    ProductJson productJson = new ProductJson();
                    productJson.setId(id);
                    productJson.setName(RandomStringUtils.randomAlphanumeric(30));
                    productJson.setDescription(RandomStringUtils.randomAlphanumeric(50));
                    return productJson;
                })
            );
        orderDocumentJson.setProducts(productJsonMap);

        return new OrderTemplateData(orderDocumentJson);
    }

    private static OrderLineItemDetail getOrderLineDetail(
        ChargeType chargeType,
        BigDecimal lineDiscountPercent,
        BigDecimal predefinedDiscountPercent
    ) {
        OrderLineItemDetail orderLineItemDetail = new OrderLineItemDetail();
        orderLineItemDetail.setQuantity(1);
        orderLineItemDetail.setEffectiveDate(1735689600L);
        orderLineItemDetail.setEndDate(1767225600L);

        PlanJson planJson = getPlanJson(chargeType);
        orderLineItemDetail.setPlan(planJson);
        orderLineItemDetail.setCharge(planJson.getCharges().get(0));
        orderLineItemDetail.setChargeDetail(getChargeDetailFromChargeJson(planJson.getCharges().get(0)));

        orderLineItemDetail.setAmount(BigDecimal.TEN);
        orderLineItemDetail.setSellUnitPrice(BigDecimal.TEN);
        orderLineItemDetail.setListUnitPrice(BigDecimal.TEN);

        if (lineDiscountPercent != null) {
            DiscountDetailJson discountDetailJson = new DiscountDetailJson();
            discountDetailJson.setPercent(lineDiscountPercent);
            orderLineItemDetail.setDiscounts(List.of(discountDetailJson));
        } else {
            orderLineItemDetail.setDiscounts(List.of());
        }

        if (predefinedDiscountPercent != null) {
            PredefinedDiscountLineItemDetail predefinedDiscount = new PredefinedDiscountLineItemDetail();
            predefinedDiscount.setPercent(predefinedDiscountPercent);
            orderLineItemDetail.setPredefinedDiscounts(List.of(predefinedDiscount));
        } else {
            orderLineItemDetail.setPredefinedDiscounts(List.of());
        }

        return orderLineItemDetail;
    }

    private static PlanJson getPlanJson(ChargeType chargeType) {
        PlanJson planJson = new PlanJson();
        planJson.setId("PLAN-" + RandomStringUtils.randomAlphanumeric(7).toUpperCase());
        planJson.setName(RandomStringUtils.randomAlphanumeric(30));
        planJson.setDescription(RandomStringUtils.randomAlphanumeric(50));
        planJson.setCurrency("USD");
        planJson.setCharges(List.of(getChargeJson(chargeType)));
        planJson.setProductId("PRODUCT-" + RandomStringUtils.randomAlphanumeric(7).toUpperCase());
        return planJson;
    }

    private static ChargeDetail getChargeDetailFromChargeJson(ChargeJson chargeJson) {
        ChargeDetail chargeDetail = new ChargeDetail();
        chargeDetail.setId(chargeJson.getId());
        chargeDetail.setName(chargeJson.getName());
        chargeDetail.setDescription(chargeJson.getDescription());
        chargeDetail.setAmount(chargeJson.getAmount());
        chargeDetail.setType(chargeJson.getType());
        chargeDetail.setChargeModel(chargeJson.getChargeModel());
        chargeDetail.setBillingCycle(chargeJson.getBillingCycle());
        chargeDetail.setPercent(chargeJson.getPercent());
        return chargeDetail;
    }

    private static ChargeJson getChargeJson(ChargeType chargeType) {
        ChargeJson chargeJson = new ChargeJson();
        chargeJson.setId("CHARGE-" + RandomStringUtils.randomAlphanumeric(7).toUpperCase());
        chargeJson.setName(RandomStringUtils.randomAlphanumeric(30));
        chargeJson.setDescription(RandomStringUtils.randomAlphanumeric(50));
        chargeJson.setAmount(BigDecimal.TEN);
        if (chargeType == ChargeType.RECURRING) {
            chargeJson.setType(ChargeType.RECURRING);
        } else if (chargeType == chargeType.PERCENTAGE_OF) {
            chargeJson.setType(ChargeType.PERCENTAGE_OF);
            chargeJson.setPercent(BigDecimal.TEN);
        } else {
            chargeJson.setType(ChargeType.RECURRING);
        }

        chargeJson.setChargeModel(ChargeModel.PER_UNIT);
        chargeJson.setBillingCycle(BillingCycle.YEAR);
        return chargeJson;
    }

    private static CustomField getCustomField() {
        Map<String, CustomFieldValue> customFieldValues = Map.of(
            "customField1",
            new CustomFieldValue(
                CustomFieldType.PICKLIST,
                "customField1",
                "Field1",
                "ON",
                List.of("ON", "OFF"),
                List.of("ON", "OFF"),
                false,
                null,
                null
            ),
            "customField2",
            new CustomFieldValue(
                CustomFieldType.PICKLIST,
                "customField2",
                "Field2",
                "ON",
                List.of("ON", "OFF"),
                List.of("ON", "OFF"),
                false,
                null,
                null
            )
        );
        return new CustomField(customFieldValues);
    }

    private static OrderTemplateData getOrderTemplateDataWithMultipleProductsAndPlans() {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");

        List<OrderLineItemDetail> lineItems = List.of(
            createOrderLineDetailWithProductAndPlan("PRODUCT-1", "PLAN-1A", ChargeType.RECURRING),
            createOrderLineDetailWithProductAndPlan("PRODUCT-1", "PLAN-1B", ChargeType.RECURRING),
            createOrderLineDetailWithProductAndPlan("PRODUCT-2", "PLAN-2A", ChargeType.RECURRING)
        );

        orderDetail.setLineItems(lineItems);
        orderDetail.setLineItemsNetEffect(lineItems);

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);

        orderDocumentJson.setLineItemCustomFields(Map.of());
        orderDocumentJson.setChargeCustomFields(Map.of());

        Map<String, ProductJson> productJsonMap = Map.of(
            "PRODUCT-1",
            createProductJson("PRODUCT-1", "Product 1", "SKU-1", "Description 1"),
            "PRODUCT-2",
            createProductJson("PRODUCT-2", "Product 2", "SKU-2", "Description 2")
        );
        orderDocumentJson.setProducts(productJsonMap);

        return new OrderTemplateData(orderDocumentJson);
    }

    private static OrderTemplateData getOrderTemplateDataWithExecutedLineItems() {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");
        orderDetail.setLineItems(List.of());
        orderDetail.setLineItemsNetEffect(List.of());

        List<OrderLineItemDetail> executedLineItems = List.of(
            createOrderLineDetailWithProductAndPlan("PRODUCT-EXE", "PLAN-EXE", ChargeType.RECURRING)
        );

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);
        orderDocumentJson.setExecutedLineItemsForSubscription(executedLineItems);

        orderDocumentJson.setLineItemCustomFields(Map.of());
        orderDocumentJson.setChargeCustomFields(Map.of());
        orderDocumentJson.setExecutedLineItemCustomFields(Map.of());
        orderDocumentJson.setExecutedLineItemChargeCustomFields(Map.of());

        Map<String, ProductJson> productJsonMap = Map.of(
            "PRODUCT-EXE",
            createProductJson("PRODUCT-EXE", "Executed Product", "SKU-EXE", "Executed Description")
        );
        orderDocumentJson.setProducts(productJsonMap);

        return new OrderTemplateData(orderDocumentJson);
    }

    private static OrderTemplateData getOrderTemplateDataWithPostSubscriptionLineItems() {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");

        List<OrderLineItemDetail> lineItems = List.of(createOrderLineDetailWithProductAndPlan("PRODUCT-POST", "PLAN-POST", ChargeType.RECURRING));

        orderDetail.setLineItems(lineItems);
        orderDetail.setLineItemsNetEffect(List.of());

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);

        orderDocumentJson.setLineItemCustomFields(Map.of());
        orderDocumentJson.setChargeCustomFields(Map.of());

        Map<String, ProductJson> productJsonMap = Map.of(
            "PRODUCT-POST",
            createProductJson("PRODUCT-POST", "Post Subscription Product", "SKU-POST", "Post Subscription Description")
        );
        orderDocumentJson.setProducts(productJsonMap);

        return new OrderTemplateData(orderDocumentJson);
    }

    private static OrderTemplateData getOrderTemplateDataWithDetailedProducts() {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");

        OrderLineItemDetail lineItem = createOrderLineDetailWithProductAndPlan("PRODUCT-DETAILED", "PLAN-DETAILED", ChargeType.RECURRING);
        PlanJson plan = lineItem.getPlan();
        plan.setName("Detailed Plan");
        plan.setDescription("This is a detailed plan description");

        orderDetail.setLineItems(List.of(lineItem));
        orderDetail.setLineItemsNetEffect(List.of(lineItem));

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);

        orderDocumentJson.setLineItemCustomFields(Map.of());
        orderDocumentJson.setChargeCustomFields(Map.of());

        ProductJson detailedProduct = createProductJson("PRODUCT-DETAILED", "Detailed Product", "SKU-123", "This is a detailed product description");
        Map<String, ProductJson> productJsonMap = Map.of("PRODUCT-DETAILED", detailedProduct);
        orderDocumentJson.setProducts(productJsonMap);

        return new OrderTemplateData(orderDocumentJson);
    }

    private static OrderTemplateData getOrderTemplateDataWithDisplayNames() {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");

        OrderLineItemDetail lineItem = createOrderLineDetailWithProductAndPlan("PRODUCT-DISPLAY", "PLAN-DISPLAY", ChargeType.RECURRING);
        PlanJson plan = lineItem.getPlan();
        plan.setName("Plan Name");
        plan.setDisplayName("Display Plan Name");

        orderDetail.setLineItems(List.of(lineItem));
        orderDetail.setLineItemsNetEffect(List.of(lineItem));

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);

        orderDocumentJson.setLineItemCustomFields(Map.of());
        orderDocumentJson.setChargeCustomFields(Map.of());

        ProductJson product = createProductJson("PRODUCT-DISPLAY", "Product Name", "SKU-DISPLAY", "Display Description");
        product.setDisplayName("Display Product Name");
        Map<String, ProductJson> productJsonMap = Map.of("PRODUCT-DISPLAY", product);
        orderDocumentJson.setProducts(productJsonMap);

        return new OrderTemplateData(orderDocumentJson);
    }

    private static OrderTemplateData getOrderTemplateDataWithAllLineItemTypes() {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setCurrency("USD");

        List<OrderLineItemDetail> regularLineItems = List.of(
            createOrderLineDetailWithProductAndPlan("PRODUCT-REG", "PLAN-REG", ChargeType.RECURRING)
        );
        orderDetail.setLineItems(regularLineItems);
        orderDetail.setLineItemsNetEffect(regularLineItems);

        List<OrderLineItemDetail> executedLineItems = List.of(
            createOrderLineDetailWithProductAndPlan("PRODUCT-EXE-ALL", "PLAN-EXE-ALL", ChargeType.RECURRING)
        );

        OrderDocumentJson orderDocumentJson = new OrderDocumentJson();
        orderDocumentJson.setTimeZone(TimeZone.getTimeZone("UTC"));
        orderDocumentJson.setOrderDetail(orderDetail);
        orderDocumentJson.setExecutedLineItemsForSubscription(executedLineItems);

        orderDocumentJson.setLineItemCustomFields(Map.of());
        orderDocumentJson.setChargeCustomFields(Map.of());
        orderDocumentJson.setExecutedLineItemCustomFields(Map.of());
        orderDocumentJson.setExecutedLineItemChargeCustomFields(Map.of());

        Map<String, ProductJson> productJsonMap = Map.of(
            "PRODUCT-REG",
            createProductJson("PRODUCT-REG", "Regular Product", "SKU-REG", "Regular Description"),
            "PRODUCT-EXE-ALL",
            createProductJson("PRODUCT-EXE-ALL", "Executed Product All", "SKU-EXE-ALL", "Executed Description All")
        );
        orderDocumentJson.setProducts(productJsonMap);

        return new OrderTemplateData(orderDocumentJson);
    }

    private static OrderLineItemDetail createOrderLineDetailWithProductAndPlan(String productId, String planId, ChargeType chargeType) {
        OrderLineItemDetail orderLineItemDetail = new OrderLineItemDetail();
        orderLineItemDetail.setId("LINE-ITEM-" + productId + "-" + planId); // Set unique ID
        orderLineItemDetail.setQuantity(1);
        orderLineItemDetail.setEffectiveDate(1735689600L);
        orderLineItemDetail.setEndDate(1767225600L);
        orderLineItemDetail.setAmount(BigDecimal.TEN);
        orderLineItemDetail.setSellUnitPrice(BigDecimal.TEN);
        orderLineItemDetail.setListUnitPrice(BigDecimal.TEN);
        orderLineItemDetail.setDiscounts(List.of());
        orderLineItemDetail.setPredefinedDiscounts(List.of());

        PlanJson planJson = new PlanJson();
        planJson.setId(planId);
        planJson.setName("Plan " + planId);
        planJson.setDescription("Description for " + planId);
        planJson.setCurrency("USD");
        planJson.setProductId(productId);

        ChargeJson chargeJson = getChargeJson(chargeType);
        planJson.setCharges(List.of(chargeJson));

        orderLineItemDetail.setPlan(planJson);
        orderLineItemDetail.setCharge(chargeJson);
        orderLineItemDetail.setChargeDetail(getChargeDetailFromChargeJson(chargeJson));

        return orderLineItemDetail;
    }

    private static ProductJson createProductJson(String productId, String name, String sku, String description) {
        ProductJson productJson = new ProductJson();
        productJson.setId(productId);
        productJson.setName(name);
        productJson.setSku(sku);
        productJson.setDescription(description);
        return productJson;
    }
}
