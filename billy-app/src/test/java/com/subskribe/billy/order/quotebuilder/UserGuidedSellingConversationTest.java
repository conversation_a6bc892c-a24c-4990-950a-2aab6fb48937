package com.subskribe.billy.order.quotebuilder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.ai.service.bedrock.BedrockHelper;
import com.subskribe.ai.service.model.Message;
import com.subskribe.billy.order.quotebuilder.model.ImmutableUserGuidedSellingConversation;
import com.subskribe.billy.order.quotebuilder.model.UserGuidedSellingConversation;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.services.bedrockruntime.model.ConversationRole;

public class UserGuidedSellingConversationTest {

    @Test
    public void testSerDeUserGuidedSellingConversation() throws IOException {
        ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();
        String initialBlurb = "I need a quote for an enterprise account that wants our platform and analytics. They need advanced support.";
        //Serialisation test
        List<Message> messages = new ArrayList<>();
        messages.add(
            BedrockHelper.modelMessageWithRole(
                ConversationRole.ASSISTANT,
                "I'll help you gather information for a quote. You need Advanced Support, Platform, and Analytics. How many users"
            )
        );
        messages.add(BedrockHelper.modelMessageWithRole(ConversationRole.USER, "We need 1000000 users"));
        UserGuidedSellingConversation userGuidedSellingConversation = ImmutableUserGuidedSellingConversation.builder()
            .initialBlurb(initialBlurb)
            .slackUserId("U04HGBUQL3X")
            .channelId("C08G20S9RS8")
            .teamId("T017910MK7V")
            .tenantId("f35504c0-1b8b-4739-a466-caab3f6f6c7e")
            .threadTs("**********.924259")
            .messagesSoFar(messages)
            .subskribeUserId("USER-3MN8P9B")
            .build();

        String serialisedConversation = OBJECT_MAPPER.writeValueAsString(userGuidedSellingConversation);

        //Deserialization test
        UserGuidedSellingConversation deserializedConversation = OBJECT_MAPPER.readValue(
            serialisedConversation.getBytes(),
            UserGuidedSellingConversation.class
        );
        Assertions.assertThat(deserializedConversation).isNotNull();
        Assertions.assertThat(deserializedConversation.getInitialBlurb()).isEqualTo(initialBlurb);
        Assertions.assertThat(deserializedConversation.getSlackUserId()).isEqualTo("U04HGBUQL3X");
        Assertions.assertThat(deserializedConversation.getChannelId()).isEqualTo("C08G20S9RS8");
        Assertions.assertThat(deserializedConversation.getTeamId()).isEqualTo("T017910MK7V");
        Assertions.assertThat(deserializedConversation.getTenantId()).isEqualTo("f35504c0-1b8b-4739-a466-caab3f6f6c7e");
        Assertions.assertThat(deserializedConversation.getThreadTs()).isEqualTo("**********.924259");
        Assertions.assertThat(deserializedConversation.getMessagesSoFar()).isNotEmpty();
        Assertions.assertThat(deserializedConversation.getSubskribeUserId()).isEqualTo("USER-3MN8P9B");
        Assertions.assertThat(deserializedConversation.getMessagesSoFar().size()).isEqualTo(2);
    }
}
