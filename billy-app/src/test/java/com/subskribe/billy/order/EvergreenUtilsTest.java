package com.subskribe.billy.order;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import java.time.Instant;
import java.util.List;
import java.util.TimeZone;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class EvergreenUtilsTest {

    private static final TimeZone timeZone = TimeZone.getTimeZone("UTC");

    @Mock
    private FeatureService mockFeatureService;

    private EvergreenUtils evergreenUtils;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockFeatureService.isEnabled(Feature.EVERGREEN)).thenReturn(true);
        evergreenUtils = new EvergreenUtils(mockFeatureService);
    }

    @Test
    public void validStartDateAndRampIntervals() {
        Order order = getOrder(Instant.now(), List.of());
        evergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone);
    }

    @Test
    public void orderStartDateTooFarInFuture() {
        Instant startDate = DateTimeCalculator.plusYears(timeZone.toZoneId(), Instant.now(), 6);
        Order order = getOrder(startDate, null);
        assertThrows(InvalidInputException.class, () -> evergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone));

        // allowed if not evergreen order
        order.setSubscriptionDurationModel(null);
        evergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone);
    }

    @Test
    public void orderRampIntervalTooFarInFuture() {
        Instant orderStartDate = Instant.now();
        Instant lastRampInterval = DateTimeCalculator.plusYears(timeZone.toZoneId(), orderStartDate, 11);
        Order order = getOrder(orderStartDate, List.of(lastRampInterval));
        assertThrows(InvalidInputException.class, () -> evergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone));

        // allowed if not evergreen order
        order.setSubscriptionDurationModel(null);
        evergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone);
    }

    private Order getOrder(Instant startDate, List<Instant> rampIntervals) {
        Order order = new Order();
        order.setSubscriptionDurationModel(SubscriptionDurationModel.EVERGREEN);
        order.setStartDate(startDate);
        order.setRampInterval(rampIntervals);
        return order;
    }
}
