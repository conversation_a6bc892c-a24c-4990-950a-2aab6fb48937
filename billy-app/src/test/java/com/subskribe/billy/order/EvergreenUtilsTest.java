package com.subskribe.billy.order;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import java.time.Instant;
import java.util.List;
import java.util.TimeZone;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class EvergreenUtilsTest {

    private static final TimeZone timeZone = TimeZone.getTimeZone("UTC");

    @Mock
    private FeatureService mockFeatureService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockFeatureService.isEnabled(Feature.EVERGREEN)).thenReturn(true);
    }

    @Test
    public void validStartDateAndRampIntervals() {
        Order order = getOrder(Instant.now(), List.of());
        EvergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone, mockFeatureService);
    }

    @Test
    public void orderStartDateTooFarInFuture() {
        Instant startDate = DateTimeCalculator.plusYears(timeZone.toZoneId(), Instant.now(), 6);
        Order order = getOrder(startDate, null);
        assertThrows(InvalidInputException.class, () -> EvergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone, mockFeatureService));

        // allowed if not evergreen order
        order.setSubscriptionDurationModel(null);
        EvergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone, mockFeatureService);
    }

    @Test
    public void orderRampIntervalTooFarInFuture() {
        Instant orderStartDate = Instant.now();
        Instant lastRampInterval = DateTimeCalculator.plusYears(timeZone.toZoneId(), orderStartDate, 11);
        Order order = getOrder(orderStartDate, List.of(lastRampInterval));
        assertThrows(InvalidInputException.class, () -> EvergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone, mockFeatureService));

        // allowed if not evergreen order
        order.setSubscriptionDurationModel(null);
        EvergreenUtils.validateEvergreenStartDateAndRamps(order, timeZone, mockFeatureService);
    }

    @Test
    public void validBillingCycleShouldSucceed() {
        Order dailyOrder = getOrder(Instant.now(), List.of());
        dailyOrder.setBillingCycle(new Recurrence(Cycle.DAY, 1));
        EvergreenUtils.validateEvergreenOrder(dailyOrder, mockFeatureService);

        Order monthlyOrder = getOrder(Instant.now(), List.of());
        monthlyOrder.setBillingCycle(new Recurrence(Cycle.MONTH, 1));
        EvergreenUtils.validateEvergreenOrder(monthlyOrder, mockFeatureService);

        Order quarterlyOrder = getOrder(Instant.now(), List.of());
        quarterlyOrder.setBillingCycle(new Recurrence(Cycle.QUARTER, 1));
        EvergreenUtils.validateEvergreenOrder(quarterlyOrder, mockFeatureService);

        Order semiAnnualOrder = getOrder(Instant.now(), List.of());
        semiAnnualOrder.setBillingCycle(new Recurrence(Cycle.SEMI_ANNUAL, 1));
        EvergreenUtils.validateEvergreenOrder(semiAnnualOrder, mockFeatureService);

        Order yearlyOrder = getOrder(Instant.now(), List.of());
        yearlyOrder.setBillingCycle(new Recurrence(Cycle.YEAR, 1));
        EvergreenUtils.validateEvergreenOrder(yearlyOrder, mockFeatureService);
    }

    @Test
    public void invalidBillingCycleShouldThrow() {
        Order paidInFullOrder = getOrder(Instant.now(), List.of());
        paidInFullOrder.setBillingCycle(new Recurrence(Cycle.PAID_IN_FULL, 1));
        assertThrows(InvalidInputException.class, () -> EvergreenUtils.validateEvergreenOrder(paidInFullOrder, mockFeatureService));

        Order customOrder = getOrder(Instant.now(), List.of());
        customOrder.setBillingCycle(new Recurrence(Cycle.CUSTOM, 1));
        assertThrows(InvalidInputException.class, () -> EvergreenUtils.validateEvergreenOrder(customOrder, mockFeatureService));
    }

    private Order getOrder(Instant startDate, List<Instant> rampIntervals) {
        Order order = new Order();
        order.setSubscriptionDurationModel(SubscriptionDurationModel.EVERGREEN);
        order.setStartDate(startDate);
        order.setRampInterval(rampIntervals);
        return order;
    }
}
