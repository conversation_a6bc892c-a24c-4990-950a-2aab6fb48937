package com.subskribe.billy.order.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.model.CustomBillingPeriod;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.ImmutableCustomBillingPeriod;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.resources.json.plan.RecurrenceJson;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.services.SubscriptionBillingPeriodService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class OrderCustomBillingServiceTest {

    private OrderCustomBillingService orderCustomBillingService;
    private Order order;
    private Map<String, Charge> chargeMap;

    @BeforeEach
    void testSetup() {
        FeatureService featureService = mock(FeatureService.class);
        ProductCatalogGetService productCatalogGetService = mock(ProductCatalogGetService.class);
        SubscriptionBillingPeriodService subscriptionBillingPeriodService = mock(SubscriptionBillingPeriodService.class);
        TenantSettingService tenantSettingService = mock(TenantSettingService.class);
        OrderDAO orderDAO = mock(OrderDAO.class);

        orderCustomBillingService = new OrderCustomBillingService(
            featureService,
            productCatalogGetService,
            subscriptionBillingPeriodService,
            tenantSettingService,
            orderDAO
        );

        order = new Order();
        order.setBillingCycle(new Recurrence(Cycle.CUSTOM, 1));
        order.setStartDate(ZonedDateTime.of(2024, 1, 1, 0, 0, 0, 0, ZoneId.of("America/Los_Angeles")).toInstant());
        order.setEndDate(ZonedDateTime.of(2024, 12, 31, 23, 59, 59, 0, ZoneId.of("America/Los_Angeles")).toInstant());
        order.setAutoRenew(true);
        order.setName("Sample Order");
        chargeMap = Map.of(
            "charge1",
            createCharge("charge1", ChargeType.ONE_TIME),
            "charge2",
            createCharge("charge2", ChargeType.RECURRING),
            "charge3",
            createCharge("charge3", ChargeType.USAGE)
        );
    }

    @Test
    void validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts_WithValidInputs_ShouldPass() {
        // Arrange
        CustomBillingSchedule schedule = createValidAdhocCustomBillingSchedule();

        // Act & Assert
        assertDoesNotThrow(() -> orderCustomBillingService.validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts(schedule, "USD"));
    }

    @Test
    void validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts_WithNullAmount_ShouldThrowException() {
        // Arrange
        CustomBillingSchedule schedule = createCustomBillingScheduleWithNullAmount();

        // Act & Assert
        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            orderCustomBillingService.validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts(schedule, "USD")
        );
        assertTrue(
            exception.getMessage().contains("[Currency : USD] - amount cannot be empty for adhoc custom billing schedule!"),
            "Exception message should mention invalid amount"
        );
    }

    @Test
    void validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts_WithZeroAmount_ShouldThrowException() {
        // Arrange
        CustomBillingSchedule schedule = createCustomBillingScheduleWithZeroAmount();

        // Act & Assert
        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            orderCustomBillingService.validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts(schedule, "USD")
        );
        assertTrue(
            exception.getMessage().contains("amount cannot be zero for adhoc custom billing schedule"),
            "Exception message should mention invalid amount"
        );
    }

    @Test
    void validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts_WithMixedPositiveAndNegativeAmounts_ShouldThrowException() {
        // Arrange
        CustomBillingSchedule schedule = createCustomBillingScheduleWithMixedAmounts();

        // Act & Assert
        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            orderCustomBillingService.validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts(schedule, "USD")
        );
        assertTrue(
            exception.getMessage().contains("Custom billing schedule amounts should be either all positive or all negative"),
            "Exception message should mention mixed amounts"
        );
    }

    @Test
    void validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts_WithDuplicateTriggerInstants_ShouldThrowException() {
        // Arrange
        CustomBillingSchedule schedule = createCustomBillingScheduleWithDuplicateTriggerInstants();

        // Act & Assert
        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            orderCustomBillingService.validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts(schedule, "USD")
        );
        assertTrue(exception.getMessage().contains("Invoice trigger date"), "Exception message should mention duplicate trigger date");
    }

    @Test
    void validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts_shouldThrowExceptionForInvalidCurrencyScale() {
        // Given
        var customBillingPeriod = ImmutableCustomBillingPeriod.builder()
            .recurrenceWithCount(new CustomBillingSchedule.CustomBillingRecurrence(new RecurrenceJson(Cycle.PAID_IN_FULL, 1), 1))
            .amount(new BigDecimal("100.123")) // Invalid scale for USD (3 decimals)
            .triggerInstant(Instant.now())
            .build();

        var customBillingSchedule = new CustomBillingSchedule(
            "id",
            CustomBillingSchedule.Version.V1,
            "orderId",
            List.of("1"),
            List.of(customBillingPeriod),
            false,
            1L,
            1L
        );

        // When & Then
        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            orderCustomBillingService.validateAdhocCustomBillingScheduleTriggerInstantsAndAmounts(customBillingSchedule, "USD")
        );

        assertThat(exception.getMessage()).contains(
            "[Currency : USD] - amount 100.123 should have correct currency precision for adhoc custom billing schedule!"
        );
    }

    @Test
    void testGetOrderLinesEligibleForCustomBilling_withEligibleCharges() {
        order.setLineItems(
            List.of(
                createOrderLineItem("lineItem1", "charge1", ActionType.ADD),
                createOrderLineItem("lineItem2", "charge2", ActionType.ADD),
                createOrderLineItem("lineItem3", "charge3", ActionType.ADD)
            )
        );

        List<OrderLineItem> eligibleOrderLines = orderCustomBillingService.getOrderLineItemsEligibleForCustomBilling(order, chargeMap);

        assertEquals(2, eligibleOrderLines.size());
        assertEquals("lineItem1", eligibleOrderLines.get(0).getOrderLineId());
    }

    @Test
    void testGetOrderLinesEligibleForCustomBilling_withNoEligibleCharges() {
        order.setLineItems(
            List.of(createOrderLineItem("lineItem1", "charge2", ActionType.ADD), createOrderLineItem("lineItem2", "charge3", ActionType.ADD))
        );

        List<OrderLineItem> eligibleOrderLines = orderCustomBillingService.getOrderLineItemsEligibleForCustomBilling(order, chargeMap);

        assertEquals(1, eligibleOrderLines.size());
    }

    @Test
    void testGetOrderLinesEligibleForCustomBilling_forAmendment() {
        order.setOrderType(OrderType.AMENDMENT);
        order.setLineItems(
            List.of(
                createOrderLineItem("lineItem1", "charge1", ActionType.ADD),
                createOrderLineItem("lineItem2", "charge3", ActionType.ADD),
                createOrderLineItem("lineItem3", "charge2", ActionType.REMOVE)
            )
        );
        order.setLineItemsNetEffect(order.getLineItems());

        List<OrderLineItem> eligibleOrderLines = orderCustomBillingService.getOrderLineItemsEligibleForCustomBilling(order, chargeMap);

        assertEquals(1, eligibleOrderLines.size());
        assertEquals("lineItem1", eligibleOrderLines.get(0).getOrderLineId());
    }

    @Test
    void testGetOrderLinesEligibleForCustomBilling_forCancellation() {
        order.setOrderType(OrderType.CANCEL);
        order.setLineItems(
            List.of(
                createOrderLineItem("lineItem1", "charge2", ActionType.REMOVE),
                createOrderLineItem("lineItem2", "charge3", ActionType.REMOVE),
                createOrderLineItem("lineItem3", "charge1", ActionType.REMOVE)
            )
        );
        order.setLineItemsNetEffect(order.getLineItems());

        List<OrderLineItem> eligibleOrderLines = orderCustomBillingService.getOrderLineItemsEligibleForCustomBilling(order, chargeMap);

        assertEquals(0, eligibleOrderLines.size());
    }

    @Test
    void testGetOrderLinesEligibleForCustomBilling_forRestructure() {
        order.setOrderType(OrderType.RESTRUCTURE);
        order.setLineItems(
            List.of(
                createOrderLineItem("lineItem1", "charge1", ActionType.RESTRUCTURE),
                createOrderLineItem("lineItem2", "charge2", ActionType.RESTRUCTURE),
                createOrderLineItem("lineItem3", "charge3", ActionType.REMOVE),
                createOrderLineItem("lineItem4", "charge2", ActionType.ADD)
            )
        );
        order.setLineItemsNetEffect(order.getLineItems());

        List<OrderLineItem> eligibleOrderLines = orderCustomBillingService.getOrderLineItemsEligibleForCustomBilling(order, chargeMap);

        assertEquals(3, eligibleOrderLines.size());
        assertEquals("lineItem1", eligibleOrderLines.get(0).getOrderLineId());
        assertEquals("lineItem2", eligibleOrderLines.get(1).getOrderLineId());
        assertEquals("lineItem4", eligibleOrderLines.get(2).getOrderLineId());
    }

    @Test
    void testCustomBillingTriggerInstantsAndAmountValidationsDontRunDuringDryRun() {
        order.setOrderType(OrderType.NEW);
        order.setLineItems(
            List.of(
                createOrderLineItem("lineItem1", "charge1", ActionType.ADD),
                createOrderLineItem("lineItem2", "charge2", ActionType.ADD),
                createOrderLineItem("lineItem3", "charge3", ActionType.ADD)
            )
        );
        var customBillingSchedule = createCustomBillingScheduleWithDuplicateTriggerInstants();
        order.setOrderId(customBillingSchedule.orderId());
        CustomBillingSchedule customBillingScheduleValidated = orderCustomBillingService.validateCustomBillingInput(
            order,
            chargeMap,
            createCustomBillingScheduleWithDuplicateTriggerInstants(),
            true
        );
        assertThat(customBillingScheduleValidated).isNotNull();
    }

    private Charge createCharge(String chargeId, ChargeType chargeType) {
        Charge charge = mock(Charge.class);
        when(charge.getChargeId()).thenReturn(chargeId);
        when(charge.getType()).thenReturn(chargeType);
        when(charge.getBillingCycle()).thenReturn(BillingCycle.DEFAULT);
        return charge;
    }

    private OrderLineItem createOrderLineItem(String orderLineId, String chargeId, ActionType actionType) {
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setOrderLineId(orderLineId);
        orderLineItem.setChargeId(chargeId);
        orderLineItem.setAction(actionType);
        orderLineItem.setAmount(BigDecimal.TEN);
        return orderLineItem;
    }

    private CustomBillingSchedule createValidAdhocCustomBillingSchedule() {
        List<CustomBillingPeriod> periods = Arrays.asList(
            createCustomBillingPeriod(BigDecimal.TEN, Instant.parse("2024-01-01T00:00:00Z")),
            createCustomBillingPeriod(BigDecimal.TEN, Instant.parse("2024-02-01T00:00:00Z"))
        );
        return new CustomBillingSchedule(
            "id",
            CustomBillingSchedule.Version.V1,
            "orderId",
            List.of("line1", "line2"),
            periods,
            false,
            System.currentTimeMillis(),
            System.currentTimeMillis()
        );
    }

    private CustomBillingSchedule createCustomBillingScheduleWithNullAmount() {
        List<CustomBillingPeriod> periods = List.of(createCustomBillingPeriod(null, Instant.parse("2024-01-01T00:00:00Z")));
        return new CustomBillingSchedule(
            "id",
            CustomBillingSchedule.Version.V1,
            "orderId",
            List.of("line1"),
            periods,
            false,
            System.currentTimeMillis(),
            System.currentTimeMillis()
        );
    }

    private CustomBillingSchedule createCustomBillingScheduleWithZeroAmount() {
        List<CustomBillingPeriod> periods = List.of(createCustomBillingPeriod(BigDecimal.ZERO, Instant.parse("2024-01-01T00:00:00Z")));
        return new CustomBillingSchedule(
            "id",
            CustomBillingSchedule.Version.V1,
            "orderId",
            List.of("line1"),
            periods,
            false,
            System.currentTimeMillis(),
            System.currentTimeMillis()
        );
    }

    private CustomBillingSchedule createCustomBillingScheduleWithMixedAmounts() {
        List<CustomBillingPeriod> periods = Arrays.asList(
            createCustomBillingPeriod(BigDecimal.TEN, Instant.parse("2024-01-01T00:00:00Z")),
            createCustomBillingPeriod(BigDecimal.valueOf(-5), Instant.parse("2024-02-01T00:00:00Z"))
        );
        return new CustomBillingSchedule(
            "id",
            CustomBillingSchedule.Version.V1,
            "orderId",
            List.of("line1", "line2"),
            periods,
            false,
            System.currentTimeMillis(),
            System.currentTimeMillis()
        );
    }

    private CustomBillingSchedule createCustomBillingScheduleWithDuplicateTriggerInstants() {
        Instant sameInstant = Instant.parse("2024-01-01T00:00:00Z");
        List<CustomBillingPeriod> periods = Arrays.asList(
            createCustomBillingPeriod(BigDecimal.TEN, sameInstant),
            createCustomBillingPeriod(BigDecimal.TEN, sameInstant)
        );
        return new CustomBillingSchedule(
            "id",
            CustomBillingSchedule.Version.V1,
            "orderId",
            List.of("line1", "line2"),
            periods,
            false,
            System.currentTimeMillis(),
            System.currentTimeMillis()
        );
    }

    private CustomBillingPeriod createCustomBillingPeriod(BigDecimal amount, Instant triggerInstant) {
        return ImmutableCustomBillingPeriod.builder()
            .amount(amount)
            .triggerInstant(triggerInstant)
            .recurrenceWithCount(new CustomBillingSchedule.CustomBillingRecurrence(new RecurrenceJson(Cycle.PAID_IN_FULL, null), 1))
            .build();
    }
}
