package com.subskribe.billy.order.document;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.customfield.model.CustomFieldType;
import com.subskribe.billy.customfield.model.CustomFieldValue;
import com.subskribe.billy.graphql.customfield.CustomFieldEntry;
import com.subskribe.billy.graphql.order.OrderLineItemDetail;
import com.subskribe.billy.shared.render.DocumentRenderFormatter;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class OrderTemplateBundlesByOrderLineCustomFieldsTest {

    Instant startDate = Instant.parse("2020-01-01T00:00:00Z");
    Instant endDate = Instant.parse("2021-01-01T00:00:00Z");
    DocumentRenderFormatter formatter = new DocumentRenderFormatter(TimeZone.getTimeZone("UTC"));
    String currencyCode = "USD";

    @Test
    public void buildLineItemBundlesWithoutType() {
        String typeKey = UUID.randomUUID().toString();

        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithOrderLineCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                List.of(),
                startDate.getEpochSecond(),
                endDate.getEpochSecond()
            ),
            getLineItemWithOrderLineCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                List.of(),
                startDate.getEpochSecond(),
                endDate.getEpochSecond()
            ),
            getLineItemWithOrderLineCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                List.of(),
                startDate.getEpochSecond(),
                endDate.getEpochSecond()
            )
        );

        OrderTemplateBundles bundles = OrderTemplateBundles.buildItemBundles(null, typeKey, lineItems, formatter, currencyCode);

        assertEquals(0, bundles.getBundles().size());
        assertEquals(3, bundles.getUnbundledItems().size());
        assertEquals("$30,000.00", bundles.getAmount());
    }

    @Test
    public void buildLineItemBundlesWithoutTypeAndDifferentDates() {
        String typeKey = UUID.randomUUID().toString();

        List<OrderTemplateLineItem> lineItems = List.of(
            getLineItemWithOrderLineCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                List.of(),
                startDate.getEpochSecond(),
                endDate.getEpochSecond()
            ),
            getLineItemWithOrderLineCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                List.of(),
                startDate.plus(60, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            ),
            getLineItemWithOrderLineCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                List.of(),
                startDate.plus(100, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            )
        );

        OrderTemplateBundles bundles = OrderTemplateBundles.buildItemBundlesByRampSegments(null, typeKey, lineItems, formatter, currencyCode);

        assertEquals(0, bundles.getBundles().size());
        assertEquals(3, bundles.getUnbundledItems().size());
        assertEquals("$30,000.00", bundles.getAmount());
    }

    @Test
    public void buildItemBundlesByOrderLineCustomField() {
        List<String> options = List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        String typeKey = UUID.randomUUID().toString();

        List<OrderTemplateLineItem> lineItems = List.of(
            // items in first bundle
            getLineItemWithOrderLineCustomField(typeKey, options.get(0), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithOrderLineCustomField(typeKey, options.get(0), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            // items in second bundle
            getLineItemWithOrderLineCustomField(typeKey, options.get(1), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            // items not in bundle
            getLineItemWithOrderLineCustomField(typeKey, UUID.randomUUID().toString(), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithOrderLineCustomField(typeKey, UUID.randomUUID().toString(), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithOrderLineCustomField(typeKey, UUID.randomUUID().toString(), options, startDate.getEpochSecond(), endDate.getEpochSecond())
        );

        OrderTemplateBundles bundles = OrderTemplateBundles.buildItemBundles(
            OrderTemplateBundles.OrderTemplateBundleBy.ORDER_ITEM_CUSTOM_FIELD,
            typeKey,
            lineItems,
            formatter,
            "USD"
        );

        assertEquals(2, bundles.getBundles().size());
        assertEquals(2, bundles.getBundles().get(0).getItems().size());
        assertEquals(options.get(0), bundles.getBundles().get(0).getBundleName());
        assertEquals(1, bundles.getBundles().get(1).getItems().size());
        assertEquals(options.get(1), bundles.getBundles().get(1).getBundleName());
        assertEquals(3, bundles.getUnbundledItems().size());
        assertEquals("$60,000.00", bundles.getAmount());
    }

    @Test
    public void buildItemBundlesByOrderLineCustomFieldWithDifferentDates() {
        List<String> options = List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        String typeKey = UUID.randomUUID().toString();

        List<OrderTemplateLineItem> lineItems = List.of(
            // items in first bundle
            getLineItemWithOrderLineCustomField(typeKey, options.get(0), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithOrderLineCustomField(
                typeKey,
                options.get(0),
                options,
                startDate.plus(60, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            ),
            // items in second bundle
            getLineItemWithOrderLineCustomField(typeKey, options.get(1), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            // items not in bundle
            getLineItemWithOrderLineCustomField(typeKey, UUID.randomUUID().toString(), options, startDate.getEpochSecond(), endDate.getEpochSecond()),
            getLineItemWithOrderLineCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                options,
                startDate.plus(60, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            ),
            getLineItemWithOrderLineCustomField(
                typeKey,
                UUID.randomUUID().toString(),
                options,
                startDate.plus(100, ChronoUnit.DAYS).getEpochSecond(),
                endDate.getEpochSecond()
            )
        );

        OrderTemplateBundles bundles = OrderTemplateBundles.buildItemBundlesByRampSegments(
            OrderTemplateBundles.OrderTemplateBundleBy.ORDER_ITEM_CUSTOM_FIELD,
            typeKey,
            lineItems,
            formatter,
            "USD"
        );

        assertEquals(3, bundles.getBundles().size());
        assertEquals(1, bundles.getBundles().get(0).getItems().size());
        assertEquals(options.get(0), bundles.getBundles().get(0).getBundleName());
        assertEquals(1, bundles.getBundles().get(1).getItems().size());
        assertEquals(options.get(0), bundles.getBundles().get(1).getBundleName());
        assertEquals(1, bundles.getBundles().get(2).getItems().size());
        assertEquals(options.get(1), bundles.getBundles().get(2).getBundleName());
        assertEquals(3, bundles.getUnbundledItems().size());
        assertEquals("$60,000.00", bundles.getAmount());
    }

    private OrderTemplateLineItem getLineItemWithOrderLineCustomField(
        String customFieldName,
        String selection,
        List<String> options,
        Long startDate,
        Long endDate
    ) {
        OrderLineItemDetail orderLineItemDetail = new OrderLineItemDetail();
        orderLineItemDetail.setEffectiveDate(startDate);
        orderLineItemDetail.setEndDate(endDate);
        orderLineItemDetail.setAmount(BigDecimal.valueOf(10000));

        CustomFieldEntry cfn = new CustomFieldEntry(
            UUID.randomUUID().toString(),
            CustomFieldType.PICKLIST,
            customFieldName,
            customFieldName,
            selection,
            List.of(selection),
            options,
            false,
            null,
            null
        );

        orderLineItemDetail.setCustomFields(List.of(cfn));

        CustomFieldValue customFieldValue = CustomFieldEntry.toValue(cfn);
        return new OrderTemplateLineItem(
            orderLineItemDetail,
            null,
            null,
            null,
            null,
            new CustomField(Map.of(customFieldValue.getName(), customFieldValue)),
            formatter,
            "USD",
            null,
            null
        );
    }
}
