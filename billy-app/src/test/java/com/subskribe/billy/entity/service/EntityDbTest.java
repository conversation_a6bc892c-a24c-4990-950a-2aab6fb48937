package com.subskribe.billy.entity.service;

import static com.subskribe.billy.jooq.default_schema.tables.Entity.ENTITY;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.db.EntityDAO;
import com.subskribe.billy.entity.fixtures.EntityContextProviderFixture;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.model.EntityPatchRequest;
import com.subskribe.billy.entity.model.ImmutableEntity;
import com.subskribe.billy.entity.model.NumberConfig;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.InvoiceConfigurationService;
import com.subskribe.billy.jooq.default_schema.tables.records.EntityRecord;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.revrec.services.RevenueEnablementService;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.WithDb;
import java.io.IOException;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.jooq.DSLContext;
import org.jooq.exception.IntegrityConstraintViolationException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * Test the database operations, constraints and mapper conversions
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EntityDbTest extends WithDb {

    private static final String TENANT_ID = "27b59431-0cc7-4c87-883b-be345e9bd525";
    private static final String ENTITY_NAME_1 = "test-entity-name-1";
    private static final String ENTITY_NAME_2 = "test-entity-name-2";
    private static final String ENTITY_NAME_3 = "test-entity-name-3";
    private static final String ENTITY_NAME_4 = "test-entity-name-4";
    private static final String ENTITY_NAME_4_PADDED = "  test-entity-name-4   ";
    private static final String ENTITY_NAME_5_SPACES = "   ";
    private static final String ENTITY_NAME_6 = "test-entity-name-6";
    private static final String ENTITY_NAME_7 = "test-entity-name-7";
    private static final String ENTITY_NAME_8 = "test-entity-name-8";
    private static final String ENTITY_ID = "test-entity-id";
    private static final String INVOICE_CONFIG_ID = "test-invoice-config-id";
    private static final String FUNCTIONAL_CURRENCY = "USD";
    private static final String WIRE_INSTRUCTION = "test-wire-instruction";
    private static final String UPDATED_WIRE_INSTRUCTION = "updated-wire-instruction";
    private static final String FUNCTIONAL_CURRENCY_2 = "EUR";

    @Mock
    private TenantIdProvider tenantIdProvider;

    private final EntityContextProvider entityContextProvider = EntityContextProviderFixture.buildAllEntitiesContext();

    @Mock
    private CacheService cacheService;

    @Mock
    private PlatformFeatureService platformFeatureService;

    @Mock
    private InvoiceConfigurationService invoiceConfigurationService;

    @Mock
    private OrderGetService orderGetService;

    @Mock
    private AccountingPeriodService accountingPeriodService;

    @Mock
    private RevenueEnablementService revenueEnablementService;

    @Mock
    private FeatureService featureService;

    private EntityGetService entityGetService;
    private EntityDAO entityDAO;
    private EntityService entityService;
    private DSLContext tenantDSLContext;

    @BeforeAll
    public void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        when(featureService.isEnabled(any())).thenReturn(true);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        entityDAO = new EntityDAO(dslContextProvider, tenantIdProvider);
        EntityIdGenerator entityIdGenerator = new EntityIdGenerator(entityDAO);
        entityGetService = new EntityGetService(entityDAO, tenantIdProvider, entityContextProvider, cacheService, featureService);
        entityService = new EntityService(
            entityDAO,
            entityGetService,
            entityIdGenerator,
            tenantIdProvider,
            entityContextProvider,
            invoiceConfigurationService,
            orderGetService,
            accountingPeriodService,
            cacheService,
            platformFeatureService,
            featureService,
            revenueEnablementService
        );
        var defaultInvoiceConfig = NumberConfig.getDefaultInvoiceConfig();
        var createdConfig = new NumberConfig(
            INVOICE_CONFIG_ID,
            defaultInvoiceConfig.scheme(),
            defaultInvoiceConfig.prefix(),
            defaultInvoiceConfig.length(),
            defaultInvoiceConfig.nextSequenceNumber()
        );
        when(invoiceConfigurationService.createInvoiceConfig(any())).thenReturn(createdConfig);
        tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(TENANT_ID, dslContextProvider);
    }

    // should not be able to create an empty entity as per immutable annotations
    @Test
    public void testAddEntity_allNulls() {
        Assertions.assertThatThrownBy(() -> {
            Entity entity = ImmutableEntity.builder().build();
            entityService.addEntity(entity);
        })
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("required attributes are not set")
            .hasMessageContaining("name")
            .hasMessageContaining("prorationMode")
            .hasMessageContaining("prorationScheme")
            .hasMessageContaining("functionalCurrency");
    }

    // should not be able to create entity with blank name
    @Test
    public void testAddEntity_withBlankName() {
        Assertions.assertThatThrownBy(() -> {
            Entity entity = ImmutableEntity.builder()
                .name(StringUtils.EMPTY)
                .prorationMode(ProrationConfig.Mode.NORMALIZED)
                .prorationScheme(ProrationConfig.Scheme.FIXED_DAYS)
                .functionalCurrency(FUNCTIONAL_CURRENCY)
                .build();
            entityService.addEntity(entity);
        })
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("name cannot be blank");
    }

    // should not be able to create entity with blank currency
    @Test
    public void testAddEntity_withBlankCurrency() {
        Assertions.assertThatThrownBy(() -> {
            Entity entity = ImmutableEntity.builder()
                .displayId("display-id-3")
                .name(ENTITY_NAME_3)
                .prorationMode(ProrationConfig.Mode.NORMALIZED)
                .prorationScheme(ProrationConfig.Scheme.FIXED_DAYS)
                .functionalCurrency(StringUtils.EMPTY)
                .build();
            entityService.addEntity(entity);
        })
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("currency cannot be blank");
    }

    @Test
    @Order(1)
    public void testAddEntity_withAllFields() {
        Entity entity = ImmutableEntity.builder()
            .tenantId(TENANT_ID)
            .displayId("display-id-1")
            .name(ENTITY_NAME_1)
            .prorationMode(ProrationConfig.Mode.EXACT)
            .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
            .functionalCurrency(FUNCTIONAL_CURRENCY)
            .wireInstruction(WIRE_INSTRUCTION)
            .build();
        Entity createdEntity = entityService.addEntity(entity);
        Assertions.assertThat(createdEntity.getEntityId()).startsWith("ENT-");
        Assertions.assertThat(createdEntity.getEntityId()).hasSizeGreaterThan(10);
        Assertions.assertThat(createdEntity.getName()).isEqualTo(ENTITY_NAME_1);
        Assertions.assertThat(createdEntity.getFunctionalCurrency()).isEqualTo(FUNCTIONAL_CURRENCY);
        Assertions.assertThat(createdEntity.getWireInstruction()).isEqualTo(WIRE_INSTRUCTION);
        Assertions.assertThat(createdEntity.getProrationMode()).isEqualTo(ProrationConfig.Mode.EXACT);
        Assertions.assertThat(createdEntity.getProrationScheme()).isEqualTo(ProrationConfig.Scheme.CALENDAR_DAYS);
    }

    // when same name is used twice, it should throw an error
    @Test
    @Order(2)
    public void testAddEntity_duplicateName() {
        Assertions.assertThatThrownBy(() -> {
            Entity entity = ImmutableEntity.builder()
                .displayId("display-id-" + RandomStringUtils.randomAlphanumeric(5))
                .name(ENTITY_NAME_1)
                .prorationMode(ProrationConfig.Mode.EXACT)
                .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
                .functionalCurrency(FUNCTIONAL_CURRENCY)
                .build();
            entityService.addEntity(entity);
        })
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("entity present with name");
    }

    // when the name has leading and trailing spaces, it should be trimmed
    @Test
    public void testAddEntity_nameFieldTrimming() {
        Entity entity = ImmutableEntity.builder()
            .tenantId(TENANT_ID)
            .displayId("display-id-4")
            .name(ENTITY_NAME_4_PADDED)
            .prorationMode(ProrationConfig.Mode.EXACT)
            .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
            .functionalCurrency(FUNCTIONAL_CURRENCY)
            .build();
        Entity createdEntity = entityService.addEntity(entity);
        Assertions.assertThat(createdEntity.getName()).isEqualTo(ENTITY_NAME_4);
    }

    // when the name is only spaces, it should not be allowed
    @Test
    public void testAddEntity_nameFieldSpaces() {
        Assertions.assertThatThrownBy(() -> {
            Entity entity = ImmutableEntity.builder()
                .tenantId(TENANT_ID)
                .name(ENTITY_NAME_5_SPACES)
                .prorationMode(ProrationConfig.Mode.EXACT)
                .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
                .functionalCurrency(FUNCTIONAL_CURRENCY)
                .build();
            entityService.addEntity(entity);
        })
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("name cannot be blank");
    }

    // db insert - null value for "entity_id" field should not be allowed
    @Test
    public void testAddEntityRecord_nullEntityId() {
        Assertions.assertThatThrownBy(() -> {
            EntityRecord record = new EntityRecord();
            record.setTenantId(TENANT_ID);
            tenantDSLContext.insertInto(ENTITY).set(record).returning().fetchOne();
        })
            .isInstanceOf(IntegrityConstraintViolationException.class)
            .hasMessageContaining("null value in column \"entity_id\" of relation \"entity\" violates not-null constraint");
    }

    // db insert - null value for "name" field should not be allowed
    @Test
    public void testAddEntityRecord_nullName() {
        Assertions.assertThatThrownBy(() -> {
            EntityRecord record = new EntityRecord();
            record.setTenantId(TENANT_ID);
            record.setEntityId(StringUtils.EMPTY);
            tenantDSLContext.insertInto(ENTITY).set(record).returning().fetchOne();
        })
            .isInstanceOf(IntegrityConstraintViolationException.class)
            .hasMessageContaining("null value in column \"name\" of relation \"entity\" violates not-null constraint");
    }

    // db insert - blank tenant id should error out due to foreign key constraint
    @Test
    public void testAddEntityRecord_blankTenantId() {
        Assertions.assertThatThrownBy(() -> {
            EntityRecord record = new EntityRecord();
            record.setTenantId(StringUtils.EMPTY);
            record.setEntityId(StringUtils.EMPTY);
            record.setDisplayId(StringUtils.EMPTY);
            record.setName(StringUtils.EMPTY);
            record.setProrationScheme(StringUtils.EMPTY);
            record.setProrationMode(StringUtils.EMPTY);
            record.setInvoiceConfigId(StringUtils.EMPTY);
            tenantDSLContext.insertInto(ENTITY).set(record).returning().fetchOne();
        })
            .isInstanceOf(IntegrityConstraintViolationException.class)
            .hasMessageContaining("violates foreign key constraint");
    }

    // db insert - valid tenant id with blank values in other fields should work
    @Test
    public void testAddEntityRecord_minimumValues() {
        EntityRecord record = new EntityRecord();
        record.setTenantId(TENANT_ID);
        record.setEntityId(StringUtils.EMPTY);
        record.setDisplayId(StringUtils.EMPTY);
        record.setName(StringUtils.EMPTY);
        record.setProrationScheme(StringUtils.EMPTY);
        record.setProrationMode(StringUtils.EMPTY);
        record.setInvoiceConfigId(StringUtils.EMPTY);
        EntityRecord savedRecord = tenantDSLContext.insertInto(ENTITY).set(record).returning().fetchOne();
        Assertions.assertThat(savedRecord.getId()).isNotNull();
        Assertions.assertThat(savedRecord.getName()).isBlank();
        Assertions.assertThat(savedRecord.getTenantId()).isEqualTo(TENANT_ID);
        Assertions.assertThat(savedRecord.getEntityId()).isBlank();
        Assertions.assertThat(savedRecord.getCreatedOn()).isNotNull();
        Assertions.assertThat(savedRecord.getUpdatedOn()).isNotNull();
    }

    // db insert - when duplicate entity id is inserted, it should throw an error
    @Test
    public void testAddEntityRecord_duplicateEntityId() {
        EntityRecord record1 = new EntityRecord();
        record1.setTenantId(TENANT_ID);
        record1.setEntityId(ENTITY_ID);
        record1.setDisplayId("display-id-1");
        record1.setName(RandomStringUtils.randomAlphanumeric(8));
        record1.setProrationScheme(StringUtils.EMPTY);
        record1.setProrationMode(StringUtils.EMPTY);
        record1.setInvoiceConfigId(StringUtils.EMPTY);
        EntityRecord savedRecord = tenantDSLContext.insertInto(ENTITY).set(record1).returning().fetchOne();
        Assertions.assertThat(savedRecord.getId()).isNotNull();
        EntityRecord record2 = new EntityRecord();
        record2.setTenantId(TENANT_ID);
        record2.setEntityId(ENTITY_ID);
        record2.setDisplayId("display-id-2");
        record2.setName(RandomStringUtils.randomAlphanumeric(8));
        record2.setProrationScheme(StringUtils.EMPTY);
        record2.setProrationMode(StringUtils.EMPTY);
        record2.setInvoiceConfigId(StringUtils.EMPTY);
        Assertions.assertThatThrownBy(() -> tenantDSLContext.insertInto(ENTITY).set(record2).returning().fetchOne())
            .isInstanceOf(IntegrityConstraintViolationException.class)
            .hasMessageContaining("duplicate key value violates unique constraint");
    }

    @Test
    public void testGetEntityById() {
        Entity entity = ImmutableEntity.builder()
            .tenantId(TENANT_ID)
            .displayId("display-id-2")
            .name(ENTITY_NAME_2)
            .prorationMode(ProrationConfig.Mode.EXACT)
            .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
            .functionalCurrency(FUNCTIONAL_CURRENCY)
            .wireInstruction(WIRE_INSTRUCTION)
            .build();
        Entity createdEntity = entityService.addEntity(entity);
        String entityId = createdEntity.getEntityId();
        Assertions.assertThat(entityId).isNotBlank();
        Entity retrievedEntity = entityGetService.getEntityFromDb(entityId);
        Assertions.assertThat(retrievedEntity).isNotNull();
        Assertions.assertThat(retrievedEntity.getEntityId()).isEqualTo(entityId);
        Assertions.assertThat(retrievedEntity.getId()).isEqualTo(createdEntity.getId());
        Assertions.assertThat(retrievedEntity.getName()).isEqualTo(ENTITY_NAME_2);
        Assertions.assertThat(retrievedEntity.getTenantId()).isEqualTo(TENANT_ID);
        Assertions.assertThat(retrievedEntity.getProrationMode()).isEqualTo(ProrationConfig.Mode.EXACT);
        Assertions.assertThat(retrievedEntity.getProrationScheme()).isEqualTo(ProrationConfig.Scheme.CALENDAR_DAYS);
        Assertions.assertThat(retrievedEntity.getFunctionalCurrency()).isEqualTo(FUNCTIONAL_CURRENCY);
        Assertions.assertThat(retrievedEntity.getWireInstruction()).isEqualTo(WIRE_INSTRUCTION);
    }

    @Test
    public void testUpdateEntity_VerifyUpdate() {
        // create entity
        Entity entity = ImmutableEntity.builder()
            .tenantId(TENANT_ID)
            .displayId("display-id-6")
            .name(ENTITY_NAME_6)
            .prorationMode(ProrationConfig.Mode.EXACT)
            .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
            .functionalCurrency(FUNCTIONAL_CURRENCY)
            .wireInstruction(WIRE_INSTRUCTION)
            .build();
        Entity returnedEntity = entityService.addEntity(entity);
        String entityId = returnedEntity.getEntityId();
        Assertions.assertThat(entityId).isNotBlank();
        Entity createdEntity = entityGetService.getEntityFromDb(entityId);
        Assertions.assertThat(createdEntity).isNotNull();
        Assertions.assertThat(createdEntity).isEqualTo(returnedEntity);
        Assertions.assertThat(createdEntity.getEntityId()).isEqualTo(entityId);
        Assertions.assertThat(createdEntity.getName()).isEqualTo(ENTITY_NAME_6);
        Assertions.assertThat(createdEntity.getTenantId()).isEqualTo(TENANT_ID);
        Assertions.assertThat(createdEntity.getProrationMode()).isEqualTo(ProrationConfig.Mode.EXACT);
        Assertions.assertThat(createdEntity.getProrationScheme()).isEqualTo(ProrationConfig.Scheme.CALENDAR_DAYS);
        Assertions.assertThat(createdEntity.getFunctionalCurrency()).isEqualTo(FUNCTIONAL_CURRENCY);
        Assertions.assertThat(createdEntity.getWireInstruction()).isEqualTo(WIRE_INSTRUCTION);

        EntityPatchRequest updateInputEntity = new EntityPatchRequest(
            entityId,
            null,
            UUID.randomUUID().toString(),
            ProrationConfig.Scheme.FIXED_DAYS,
            ProrationConfig.Mode.NORMALIZED,
            null,
            null,
            null,
            null,
            FUNCTIONAL_CURRENCY_2,
            UPDATED_WIRE_INSTRUCTION,
            null,
            null,
            null
        );
        Entity updateReturnedEntity = entityDAO.patchEntity(updateInputEntity);
        Assertions.assertThat(updateReturnedEntity).isNotNull();
        Assertions.assertThat(updateReturnedEntity.getEntityId()).isEqualTo(entityId);
        Entity updatedEntity = entityGetService.getEntityFromDb(entityId);
        Assertions.assertThat(updatedEntity).isNotNull();
        Assertions.assertThat(updatedEntity).isEqualTo(updateReturnedEntity);
        // these fields should be ignored by update
        Assertions.assertThat(updatedEntity.getId()).isEqualTo(createdEntity.getId());
        Assertions.assertThat(updatedEntity.getEntityId()).isEqualTo(createdEntity.getEntityId());
        Assertions.assertThat(updatedEntity.getTenantId()).isEqualTo(createdEntity.getTenantId());
        // these fields should be updated
        Assertions.assertThat(updatedEntity.getName()).isEqualTo(updateInputEntity.name());
        Assertions.assertThat(updatedEntity.getProrationMode()).isEqualTo(updateInputEntity.prorationMode());
        Assertions.assertThat(updatedEntity.getProrationScheme()).isEqualTo(updateInputEntity.prorationScheme());
        Assertions.assertThat(updatedEntity.getFunctionalCurrency()).isEqualTo(updateInputEntity.functionalCurrency());
        Assertions.assertThat(updatedEntity.getWireInstruction()).isEqualTo(updateInputEntity.wireInstruction());
    }

    @Test
    public void testUpdateEntity_VerifyPatchName() {
        // create entity
        Entity entity = ImmutableEntity.builder()
            .tenantId(TENANT_ID)
            .displayId("display-id-7")
            .name(ENTITY_NAME_7)
            .prorationMode(ProrationConfig.Mode.EXACT)
            .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
            .functionalCurrency(FUNCTIONAL_CURRENCY)
            .build();
        Entity createdEntity = entityService.addEntity(entity);
        String entityId = createdEntity.getEntityId();
        Assertions.assertThat(entityId).isNotBlank();

        // patch entity
        EntityPatchRequest patchInputEntity = new EntityPatchRequest(
            entityId,
            null,
            UUID.randomUUID().toString(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );
        Entity updateReturnedEntity = entityDAO.patchEntity(patchInputEntity);
        Assertions.assertThat(updateReturnedEntity).isNotNull();
        Assertions.assertThat(updateReturnedEntity.getEntityId()).isEqualTo(entityId);
        Entity updatedEntity = entityGetService.getEntityFromDb(entityId);
        Assertions.assertThat(updatedEntity).isNotNull();
        Assertions.assertThat(updatedEntity).isEqualTo(updateReturnedEntity);

        // these fields should be ignored by update
        Assertions.assertThat(updatedEntity.getId()).isEqualTo(createdEntity.getId());
        Assertions.assertThat(updatedEntity.getEntityId()).isEqualTo(createdEntity.getEntityId());
        Assertions.assertThat(updatedEntity.getTenantId()).isEqualTo(createdEntity.getTenantId());
        Assertions.assertThat(updatedEntity.getProrationMode()).isEqualTo(createdEntity.getProrationMode());
        Assertions.assertThat(updatedEntity.getProrationScheme()).isEqualTo(createdEntity.getProrationScheme());
        Assertions.assertThat(updatedEntity.getFunctionalCurrency()).isEqualTo(createdEntity.getFunctionalCurrency());

        // these fields should be updated
        Assertions.assertThat(updatedEntity.getName()).isEqualTo(patchInputEntity.name());
    }

    @Test
    public void testUpdateEntity_VerifyPatchProration() {
        // create entity
        Entity entity = ImmutableEntity.builder()
            .tenantId(TENANT_ID)
            .displayId("display-id-8")
            .name(ENTITY_NAME_8)
            .prorationMode(ProrationConfig.Mode.EXACT)
            .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
            .functionalCurrency(FUNCTIONAL_CURRENCY)
            .build();
        Entity createdEntity = entityService.addEntity(entity);
        String entityId = createdEntity.getEntityId();
        Assertions.assertThat(entityId).isNotBlank();

        // patch entity
        EntityPatchRequest patchInputEntity = new EntityPatchRequest(
            entityId,
            null,
            null,
            ProrationConfig.Scheme.FIXED_DAYS,
            ProrationConfig.Mode.NORMALIZED,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );
        Entity updateReturnedEntity = entityDAO.patchEntity(patchInputEntity);
        Assertions.assertThat(updateReturnedEntity).isNotNull();
        Assertions.assertThat(updateReturnedEntity.getEntityId()).isEqualTo(entityId);
        Entity updatedEntity = entityGetService.getEntityFromDb(entityId);
        Assertions.assertThat(updatedEntity).isNotNull();
        Assertions.assertThat(updatedEntity).isEqualTo(updateReturnedEntity);

        // these fields should be ignored by update
        Assertions.assertThat(updatedEntity.getId()).isEqualTo(createdEntity.getId());
        Assertions.assertThat(updatedEntity.getEntityId()).isEqualTo(createdEntity.getEntityId());
        Assertions.assertThat(updatedEntity.getTenantId()).isEqualTo(createdEntity.getTenantId());
        Assertions.assertThat(updatedEntity.getName()).isEqualTo(createdEntity.getName());
        Assertions.assertThat(updatedEntity.getFunctionalCurrency()).isEqualTo(createdEntity.getFunctionalCurrency());

        // these fields should be updated
        Assertions.assertThat(updatedEntity.getProrationMode()).isEqualTo(patchInputEntity.prorationMode());
        Assertions.assertThat(updatedEntity.getProrationScheme()).isEqualTo(patchInputEntity.prorationScheme());
    }
}
