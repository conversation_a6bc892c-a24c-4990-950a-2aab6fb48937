package com.subskribe.billy.entity;

import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.service.EntityAuthContextResolver;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantIdProviderFixture;
import com.subskribe.billy.user.fixture.UserFixture;
import com.subskribe.billy.user.model.User;
import java.util.Set;
import javax.ws.rs.ForbiddenException;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mock;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class UserEntityTaggingTest {

    private static final String CREATE_ERROR_MESSAGE = "Unauthorized to assign given entities to the specified object.";

    private final FeatureService featureService = FeatureServiceFixture.allEnabled();

    private final TenantIdProvider tenantIdProvider = TenantIdProviderFixture.getDefault();

    @Mock
    private EntityContextProvider entityContextProvider;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private EntityGetService entityGetService;

    @Mock
    private EntityAuthContextResolver entityAuthContextResolver;

    private EntityContextResolver entityContextResolver;

    @BeforeAll
    public void setUp() {
        openMocks(this);
        entityContextResolver = new EntityContextResolver(
            entityContextProvider,
            entityGetService,
            billyConfiguration,
            featureService,
            entityAuthContextResolver,
            tenantIdProvider
        );
    }

    // when grantor has full access, any entity ids can be assigned to the user,
    // except in case of invalid entity ids
    @Test
    public void testFullAccessUser() {
        EntityContext entityContext = EntityContext.builder()
            .hasFullSelection(true)
            .hasFullAuthorization(true)
            .selectedIds(EntityFixture.ALL_ENTITY_IDS)
            .authorizedIds(EntityFixture.ALL_ENTITY_IDS)
            .build();
        when(entityContextProvider.provideEntityContext()).thenReturn(entityContext);
        User validUser = UserFixture.getDefault();

        // user with default (2 entities) access works
        User user = UserFixture.getDefault();
        entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds());
        entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds());

        // user with all 3 entities access works
        user.setEntityIds(EntityFixture.ALL_ENTITY_IDS);
        entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds());
        entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds());

        // user with all * access works
        user.setEntityIds(Set.of(EntityContext.ALL_ACCESS_ID));
        entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds());
        entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds());

        // user with invalid entity id should fail
        user.setEntityIds(Set.of("INVALID_ENTITY_ID"));
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds()))
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);
    }

    // when grantor has access to all of tenant's entities explicitly but not using wildcard,
    // user cannot be created with wildcard access
    @Test
    public void testExplicitTenantEntitiesAccess() {
        EntityContext entityContext = EntityContext.builder()
            .hasFullSelection(true)
            .hasFullAuthorization(false)
            .selectedIds(EntityFixture.ALL_ENTITY_IDS)
            .authorizedIds(EntityFixture.ALL_ENTITY_IDS)
            .build();
        when(entityContextProvider.provideEntityContext()).thenReturn(entityContext);
        User validUser = UserFixture.getDefault();

        // user with default (2 entities) access works
        User user = UserFixture.getDefault();
        entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds());
        entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds());

        // user with all 3 entities access works
        user.setEntityIds(EntityFixture.ALL_ENTITY_IDS);
        entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds());
        entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds());

        // user with all * access fails
        user.setEntityIds(Set.of(EntityContext.ALL_ACCESS_ID));
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds()))
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);
        Assertions.assertThatThrownBy(() ->
            entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds())
        )
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);

        // user with invalid entity id should fail
        user.setEntityIds(Set.of("INVALID_ENTITY_ID"));
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds()))
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);
    }

    // when grantor has access to some of the entities,
    // user can be created only with those entities
    @Test
    public void testPartialTenantEntitiesAccess() {
        EntityContext entityContext = EntityContext.builder()
            .hasFullSelection(true)
            .hasFullAuthorization(false)
            .selectedIds(UserFixture.ENTITY_IDS)
            .authorizedIds(UserFixture.ENTITY_IDS)
            .build();
        when(entityContextProvider.provideEntityContext()).thenReturn(entityContext);
        User validUser = UserFixture.getDefault();

        // user with default (2 entities) access works
        User user = UserFixture.getDefault();
        entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds());
        entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds());

        // user with one entity within the grantor's list should work
        user.setEntityIds(Set.of(EntityFixture.ENTITY_1_ID));
        entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds());
        entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds());

        // user with entity id outside of the grantor's list should fail
        user.setEntityIds(Set.of(EntityFixture.ENTITY_3_ID));
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds()))
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);
        Assertions.assertThatThrownBy(() ->
            entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds())
        )
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);

        // user with all 3 entities access should fail
        user.setEntityIds(EntityFixture.ALL_ENTITY_IDS);
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds()))
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);
        Assertions.assertThatThrownBy(() ->
            entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds())
        )
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);

        // user with all * access fails
        user.setEntityIds(Set.of(EntityContext.ALL_ACCESS_ID));
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds()))
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);
        Assertions.assertThatThrownBy(() ->
            entityContextResolver.resolveInputEntityIdsForGlobalObjectForUpdate(user.getEntityIds(), validUser.getEntityIds())
        )
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);

        // user with invalid entity id should fail
        user.setEntityIds(Set.of("INVALID_ENTITY_ID"));
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(user.getEntityIds()))
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining(CREATE_ERROR_MESSAGE);
    }
}
