package com.subskribe.billy.entity.db;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.model.EntityJson;
import com.subskribe.billy.entity.model.ImmutableEntity;
import com.subskribe.billy.entity.model.NumberConfig;
import com.subskribe.billy.entity.model.NumberScheme;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.resources.json.entity.EntityJsonMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

public class EntityMapperTest {

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    private static final String TENANT_ID = "27b59431-0cc7-4c87-883b-be345e9bd525";
    private static final String ENTITY_ID = "test-entity-id";
    private static final String ENTITY_NAME = "test-entity-name-1";
    private static final String INVOICE_CONFIG_ID = UUID.randomUUID().toString();
    private static final String FUNCTIONAL_CURRENCY = "USD";
    private static final String WIRE_INSTRUCTION = "test-wire-instruction";
    private static final String INV_PREFIX = "ABC-";
    private static final String NUMBER_CONFIG_PREFIX = "ABC-";
    private static final int NUMBER_CONFIG_LENGTH = 23;

    @Test
    public void testJsonMapper() {
        EntityJsonMapper entityJsonMapper = Mappers.getMapper(EntityJsonMapper.class);
        NumberConfig invoiceConfig = new NumberConfig(INVOICE_CONFIG_ID, NumberScheme.PSEUDO_RANDOM, INV_PREFIX, 10, 1L);
        Entity entity = ImmutableEntity.builder()
            .name(ENTITY_NAME)
            .prorationMode(ProrationConfig.Mode.EXACT)
            .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
            .functionalCurrency(FUNCTIONAL_CURRENCY)
            .wireInstruction(WIRE_INSTRUCTION)
            .invoiceConfig(invoiceConfig)
            .companyContact(mockAccountContact())
            .accountReceivableContact(mockAccountContact())
            .build();
        EntityJson entityJson = entityJsonMapper.toJson(entity);
        NumberConfig mappedInvoiceConfig = entityJson.getInvoiceConfig();
        Assertions.assertThat(invoiceConfig).isEqualTo(mappedInvoiceConfig);
        Entity reverseMappedEntity = entityJsonMapper.fromJson(entityJson);
        Assertions.assertThat(reverseMappedEntity).isEqualTo(entity);
        Assertions.assertThat(reverseMappedEntity.getInvoiceConfig()).isPresent();
        Assertions.assertThat(reverseMappedEntity.getInvoiceConfig().get().configId()).isEqualTo(INVOICE_CONFIG_ID);
        Assertions.assertThat(reverseMappedEntity.getInvoiceConfig().get().prefix()).isEqualTo(INV_PREFIX);
    }

    @Test
    public void testSerialization() throws JsonProcessingException {
        Entity entity = ImmutableEntity.builder()
            .id(UUID.randomUUID())
            .tenantId(TENANT_ID)
            .entityId(ENTITY_ID)
            .name(ENTITY_NAME)
            .prorationMode(ProrationConfig.Mode.EXACT)
            .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
            .functionalCurrency(FUNCTIONAL_CURRENCY)
            .wireInstruction(WIRE_INSTRUCTION)
            .invoiceConfig(new NumberConfig(INVOICE_CONFIG_ID, NumberScheme.PSEUDO_RANDOM, NUMBER_CONFIG_PREFIX, NUMBER_CONFIG_LENGTH, 1L))
            .invoiceConfigId(UUID.randomUUID().toString())
            .companyContact(mockAccountContact())
            .accountReceivableContact(mockAccountContact())
            .build();
        String serialized = OBJECT_MAPPER.writeValueAsString(entity);
        Entity deserializedEntity = OBJECT_MAPPER.readValue(serialized, Entity.class);
        Assertions.assertThat(deserializedEntity).isEqualTo(entity);
        Assertions.assertThat(deserializedEntity.getInvoiceConfig()).isPresent();
        Assertions.assertThat(deserializedEntity.getInvoiceConfig().get().prefix()).isEqualTo(NUMBER_CONFIG_PREFIX);
    }

    private AccountContact mockAccountContact() {
        AccountContact accountContact = new AccountContact();
        accountContact.setFirstName("John");
        accountContact.setLastName("Locke");
        accountContact.setEmail("<EMAIL>");
        AccountAddress accountAddress = new AccountAddress();
        accountAddress.setStreetAddressLine1("123 Main St");
        accountAddress.setStreetAddressLine2("Apt 201");
        accountAddress.setCity("Los Angeles");
        accountAddress.setState("CA");
        accountAddress.setZipcode("90001");
        accountAddress.setCountry("US");
        accountContact.setAddress(accountAddress);
        return accountContact;
    }
}
