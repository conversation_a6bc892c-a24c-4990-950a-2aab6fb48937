package com.subskribe.billy.entity.service;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantIdProviderFixture;
import java.util.Set;
import javax.ws.rs.ForbiddenException;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mock;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class EntityContextResolverGlobalTest {

    private final EntityContextProvider entityContextProvider = mock(EntityContextProvider.class);

    private final EntityGetService entityGetService = mock(EntityGetService.class);

    private final FeatureService featureService = FeatureServiceFixture.allEnabled();

    private final TenantIdProvider tenantIdProvider = TenantIdProviderFixture.getDefault();

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private EntityAuthContextResolver entityAuthContextResolver;

    private EntityContextResolver entityContextResolver;

    @BeforeAll
    public void setUp() {
        entityContextResolver = new EntityContextResolver(
            entityContextProvider,
            entityGetService,
            billyConfiguration,
            featureService,
            entityAuthContextResolver,
            tenantIdProvider
        );
    }

    // selected ids = []; request ids = []; => resolved ids = []
    @Test
    public void testAllEmpty() {
        when(entityContextProvider.provideEntityContext()).thenReturn(EntityContext.builder().build());
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(Set.of());
        Assertions.assertThat(resolvedEntityIds).isEmpty();
    }

    // full selection = true; full authorization = true; request ids = null; => resolved ids = [*]
    @Test
    public void testNullRequest() {
        EntityContext entityContext = EntityContext.builder().hasFullAuthorization(true).hasFullSelection(true).build();
        when(entityContextProvider.provideEntityContext()).thenReturn(entityContext);
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(null);
        Assertions.assertThat(resolvedEntityIds).containsExactly(EntityContext.ALL_ACCESS_ID);
    }

    // selected ids = []; request ids = [ ent 1 ]; => error
    @Test
    public void testEmptySelected() {
        when(entityContextProvider.provideEntityContext()).thenReturn(EntityContext.builder().build());
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(Set.of(EntityFixture.ENTITY_1_ID)))
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining("Unauthorized to assign given entities to the specified object.");
    }

    // selected ids = [ent 1]; request ids = [ ent 1 ]; => resolved ids = [ent 1]
    @Test
    public void testSingleEntity() {
        when(entityContextProvider.provideEntityContext()).thenReturn(
            EntityContext.builder().selectedIds(Set.of(EntityFixture.ENTITY_1_ID)).authorizedIds(Set.of(EntityFixture.ENTITY_1_ID)).build()
        );
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(Set.of(EntityFixture.ENTITY_1_ID));
        Assertions.assertThat(resolvedEntityIds).containsExactly(EntityFixture.ENTITY_1_ID);
    }

    // selected ids = [ent 1, ent 2]; request ids = [ ent 2 ]; => resolved ids = [ent 2]
    @Test
    public void testSingleEntityInMultiple() {
        when(entityContextProvider.provideEntityContext()).thenReturn(
            EntityContext.builder()
                .selectedIds(Set.of(EntityFixture.ENTITY_1_ID, EntityFixture.ENTITY_2_ID))
                .authorizedIds(Set.of(EntityFixture.ENTITY_1_ID, EntityFixture.ENTITY_2_ID))
                .build()
        );
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(Set.of(EntityFixture.ENTITY_2_ID));
        Assertions.assertThat(resolvedEntityIds).containsExactly(EntityFixture.ENTITY_2_ID);
    }

    // selected ids = [ent 1, ent 2]; request ids = [ ent 3 ]; => error
    @Test
    public void testSingleEntityNotInMultiple() {
        when(entityContextProvider.provideEntityContext()).thenReturn(
            EntityContext.builder().selectedIds(Set.of(EntityFixture.ENTITY_1_ID, EntityFixture.ENTITY_2_ID)).build()
        );
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(Set.of(EntityFixture.ENTITY_3_ID)))
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining("Unauthorized to assign given entities to the specified object.");
    }

    // full selection = true; full authorization = true; request ids = [*]; => resolved ids = [*]
    @Test
    public void testAllAccess() {
        EntityContext entityContext = EntityContext.builder().hasFullAuthorization(true).hasFullSelection(true).build();
        when(entityContextProvider.provideEntityContext()).thenReturn(entityContext);
        Set<String> resolvedEntityIds = entityContextResolver.resolveInputEntityIdsForGlobalObject(Set.of(EntityContext.ALL_ACCESS_ID));
        Assertions.assertThat(resolvedEntityIds).containsExactly(EntityContext.ALL_ACCESS_ID);
    }

    // full selection = true; full authorization = false; request ids = [*]; => error
    @Test
    public void testAllAccessInsufficientAuthorization() {
        EntityContext entityContext = EntityContext.builder().hasFullAuthorization(false).hasFullSelection(true).build();
        when(entityContextProvider.provideEntityContext()).thenReturn(entityContext);
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(Set.of(EntityContext.ALL_ACCESS_ID)))
            .isInstanceOf(ForbiddenException.class)
            .hasMessageContaining("Unauthorized to assign given entities to the specified object.");
    }

    // full selection = false; full authorization = true; request ids = [*]; => resolved ids = [*]
    @Test
    public void testAllAccessInsufficientSelection() {
        EntityContext entityContext = EntityContext.builder().hasFullAuthorization(true).hasFullSelection(false).build();
        when(entityContextProvider.provideEntityContext()).thenReturn(entityContext);
        Assertions.assertThatThrownBy(() -> entityContextResolver.resolveInputEntityIdsForGlobalObject(Set.of(EntityContext.ALL_ACCESS_ID))
        ).isInstanceOf(ForbiddenException.class);
    }
}
