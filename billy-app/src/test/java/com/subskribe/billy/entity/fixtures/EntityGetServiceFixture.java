package com.subskribe.billy.entity.fixtures;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.service.EntityGetService;
import java.util.List;
import java.util.TimeZone;

public class EntityGetServiceFixture {

    protected static final TimeZone TIME_ZONE = TimeZone.getTimeZone("UTC");

    public static EntityGetService entityGetServiceFixture() {
        Entity entityFixture = EntityFixture.getEntityFixture();
        return entityGetServiceFixture(entityFixture);
    }

    public static EntityGetService entityGetServiceFixture(Entity entityFixture) {
        EntityGetService entityGetService = mock(EntityGetService.class);
        when(entityGetService.getEntityById(any())).thenReturn(entityFixture);

        List<Entity> entityFixtures = EntityFixture.getEntityFixtures();
        when(entityGetService.getEntities()).thenReturn(entityFixtures);
        when(entityGetService.getEntitiesByTenantId(anyString())).thenReturn(entityFixtures);
        return entityGetService;
    }
}
