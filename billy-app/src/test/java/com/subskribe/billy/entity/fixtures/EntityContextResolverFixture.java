package com.subskribe.billy.entity.fixtures;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.entity.service.EntityAuthContextResolver;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.tenant.fixtures.TenantIdProviderFixture;

public class EntityContextResolverFixture {

    public static EntityContextResolver build() {
        EntityContextResolver entityContextResolver = mock(EntityContextResolver.class);
        when(entityContextResolver.resolveInputEntityIdForIsolatedObject(anyString())).thenReturn(EntityFixture.ENTITY_1_ID);
        when(entityContextResolver.resolveInputEntityIdForIsolatedObjectForUpdate(anyString(), anyString())).thenReturn(EntityFixture.ENTITY_1_ID);
        return entityContextResolver;
    }

    public static EntityContextResolver usingConstructor() {
        return new EntityContextResolver(
            EntityContextProviderFixture.buildAllEntitiesContext(),
            EntityGetServiceFixture.entityGetServiceFixture(),
            new BillyConfiguration(),
            FeatureServiceFixture.allEnabled(),
            mock(EntityAuthContextResolver.class),
            TenantIdProviderFixture.getDefault()
        );
    }
}
