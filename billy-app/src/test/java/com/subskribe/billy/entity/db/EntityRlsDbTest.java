package com.subskribe.billy.entity.db;

import com.subskribe.billy.account.db.AccountDAO;
import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityContextProviderFixture;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.fixtures.EntityPolicyProviderFixture;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.entity.service.EntityIdGenerator;
import com.subskribe.billy.entity.service.EntityService;
import com.subskribe.billy.fixtures.AccountData;
import com.subskribe.billy.fixtures.OrderData;
import com.subskribe.billy.invoice.service.InvoiceConfigurationService;
import com.subskribe.billy.order.db.OrderDAO;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.services.OrderGetService;
import com.subskribe.billy.resources.shared.PaginationQueryParams;
import com.subskribe.billy.revrec.services.RevenueEnablementService;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantFixture;
import com.subskribe.billy.tenant.fixtures.TenantIdProviderFixture;
import com.subskribe.billy.test.WithDb;
import com.subskribe.billy.tracing.RequestIdProvider;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.assertj.core.api.Assertions;
import org.jooq.DSLContext;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.MDC;

/**
 * Test the database operations, constraints and mapper conversions
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EntityRlsDbTest extends WithDb {

    private final TenantIdProvider tenantIdProvider = TenantIdProviderFixture.withTenantId(TenantFixture.TENANT_2_ID);
    private final EntityGetService entityGetService = EntityGetServiceFixture.entityGetServiceFixture();
    private final EntityContextProvider entityContextProvider = EntityContextProviderFixture.buildAllEntitiesContext();

    @Mock
    private CacheService cacheService;

    @Mock
    private InvoiceConfigurationService invoiceConfigurationService;

    @Mock
    private OrderGetService orderGetService;

    @Mock
    private RevenueEnablementService revenueEnablementService;

    private final FeatureService featureService = FeatureServiceFixture.allEnabled();

    private AccountDAO accountDAO;
    private OrderDAO orderDAO;
    private EntityService entityService;
    private DSLContext tenantDSLContext;

    private Account savedAccount;
    private final List<String> entityIds = new ArrayList<>();

    @BeforeAll
    public void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        EntityDAO entityDAO = new EntityDAO(dslContextProvider, tenantIdProvider);
        orderDAO = new OrderDAO(tenantIdProvider, dslContextProvider, featureService);
        accountDAO = new AccountDAO(tenantIdProvider, dslContextProvider);
        entityService = new EntityService(
            entityDAO,
            entityGetService,
            new EntityIdGenerator(entityDAO),
            tenantIdProvider,
            entityContextProvider,
            invoiceConfigurationService,
            orderGetService,
            cacheService,
            featureService,
            revenueEnablementService
        );
        tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(TenantFixture.TENANT_2_ID, dslContextProvider);
    }

    @Test
    @org.junit.jupiter.api.Order(1)
    public void testAddEntity1() {
        Entity entity = EntityFixture.getEntityBuilder().entityId(null).build();
        Entity createdEntity = entityService.addEntity(entity);
        Assertions.assertThat(createdEntity.getEntityId()).isNotBlank();
        entityIds.add(createdEntity.getEntityId());
    }

    @Test
    @org.junit.jupiter.api.Order(1)
    public void testAddEntity2() {
        Entity entity = EntityFixture.getEntityBuilder().entityId(null).name("entity-2").displayId("ENT-2").build();
        Entity createdEntity = entityService.addEntity(entity);
        Assertions.assertThat(createdEntity.getEntityId()).isNotBlank();
        entityIds.add(createdEntity.getEntityId());
    }

    @Test
    @org.junit.jupiter.api.Order(2)
    public void addAccount() {
        Account account = AccountData.createAccount("account-1", null, null);
        account.setEntityIds(EntityFixture.ALL_ENTITY_IDS);
        savedAccount = tenantDSLContext.transactionResult(configuration -> accountDAO.addAccount(account, configuration));
    }

    @Test
    @org.junit.jupiter.api.Order(3)
    public void addOrder1() {
        Order order = OrderData.createOrder(
            "order-1",
            TenantFixture.TENANT_2_ID,
            entityIds.get(0),
            null,
            savedAccount.getAccountId(),
            null,
            List.of(),
            OrderData.START_DATE,
            OrderData.END_DATE
        );
        orderDAO.addOrder(tenantDSLContext, order);
    }

    @Test
    @org.junit.jupiter.api.Order(3)
    public void addOrder2() {
        Order order = OrderData.createOrder(
            "order-2",
            TenantFixture.TENANT_2_ID,
            entityIds.get(1),
            null,
            savedAccount.getAccountId(),
            null,
            List.of(),
            OrderData.START_DATE,
            OrderData.END_DATE
        );
        orderDAO.addOrder(tenantDSLContext, order);
    }

    @Test
    @org.junit.jupiter.api.Order(4)
    public void getOrders() {
        var orders = orderDAO.getOrders(new PaginationQueryParams(null, 10), Optional.empty());
        Assertions.assertThat(orders).hasSize(2);
    }

    @Test
    @org.junit.jupiter.api.Order(5)
    public void getOrder1WithRLS() {
        var entityPolicyProvider = EntityPolicyProviderFixture.buildWithEntityIds(Set.of(entityIds.get(0)));
        dslContextProvider.setEntityPolicyProvider(entityPolicyProvider);
        var orders = orderDAO.getOrders(new PaginationQueryParams(null, 10), Optional.empty());
        Assertions.assertThat(orders).hasSize(1);
        Assertions.assertThat(orders.get(0).getEntityId()).isEqualTo(entityIds.get(0));
    }

    @Test
    @org.junit.jupiter.api.Order(6)
    public void getOrder2WithRLS() {
        var entityPolicyProvider = EntityPolicyProviderFixture.buildWithEntityIds(Set.of(entityIds.get(1)));
        dslContextProvider.setEntityPolicyProvider(entityPolicyProvider);
        var orders = orderDAO.getOrders(new PaginationQueryParams(null, 10), Optional.empty());
        Assertions.assertThat(orders).hasSize(1);
        Assertions.assertThat(orders.get(0).getEntityId()).isEqualTo(entityIds.get(1));
    }

    // test the RLS with request context
    @Test
    @org.junit.jupiter.api.Order(7)
    public void getOrder2WithRequestContext() {
        MDC.put(RequestIdProvider.REQUEST_ID_FIELD_NAME, "test-request-id");
        var entityPolicyProvider = EntityPolicyProviderFixture.buildWithEntityIds(Set.of(entityIds.get(1)));
        dslContextProvider.setEntityPolicyProvider(entityPolicyProvider);
        var orders = orderDAO.getOrders(new PaginationQueryParams(null, 10), Optional.empty());
        Assertions.assertThat(orders).hasSize(1);
        Assertions.assertThat(orders.get(0).getEntityId()).isEqualTo(entityIds.get(1));
    }
}
