package com.subskribe.billy.entity.fixtures;

import static com.subskribe.billy.jooq.default_schema.tables.Entity.ENTITY;
import static com.subskribe.billy.tenant.fixtures.TenantFixture.TENANT_1_ID;

import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.model.ImmutableEntity;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.jooq.default_schema.tables.records.EntityRecord;
import com.subskribe.billy.shared.tenant.TenantDSLContextProvider;
import java.util.List;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import org.jooq.DSLContext;

public class EntityFixture {

    public static final String ENTITY_1_ID = "ENT-1234567";
    public static final String ENTITY_1_DISPLAY_ID = "ENT-1";
    public static final String ENTITY_2_ID = "ENT-2345678";
    public static final String ENTITY_2_DISPLAY_ID = "ENT-2";
    public static final String ENTITY_3_ID = "ENT-3456789";
    public static final String ENTITY_3_DISPLAY_ID = "ENT-3";
    public static final Set<String> ALL_ENTITY_IDS = Set.of(ENTITY_1_ID, ENTITY_2_ID, ENTITY_3_ID);
    protected static final TimeZone TIME_ZONE = TimeZone.getTimeZone("UTC");
    protected static final ProrationConfig.Scheme PRORATION_SCHEME = ProrationConfig.Scheme.CALENDAR_DAYS;
    protected static final ProrationConfig.Mode PRORATION_MODE = ProrationConfig.Mode.NORMALIZED;
    protected static final String INVOICE_CONFIG_ID = UUID.randomUUID().toString();

    public static void initDb(DSLContextProvider dslContextProvider) {
        DSLContext tenantDSLContext = TenantDSLContextProvider.getTenantDslContext(TENANT_1_ID, dslContextProvider);
        insertEntity(tenantDSLContext, getEntityFixture());
        insertEntity(tenantDSLContext, getEntityBuilder().entityId(ENTITY_2_ID).name("test entity 2").build());
    }

    public static void insertEntity(DSLContext dslContext, Entity entity) {
        EntityRecord record = new EntityRecord();
        record.setTenantId(TENANT_1_ID);
        record.setEntityId(entity.getEntityId());
        record.setDisplayId(entity.getDisplayId());
        record.setTimezone(entity.getTimezone().getID());
        record.setProrationScheme(entity.getProrationScheme().name());
        record.setProrationMode(entity.getProrationMode().name());
        record.setInvoiceConfigId(entity.getInvoiceConfigId());
        record.setName(entity.getName());
        dslContext.insertInto(ENTITY).set(record).returning().fetchOne();
    }

    public static List<Entity> getEntityFixtures() {
        return List.of(
            getEntityBuilder(ENTITY_1_ID, ENTITY_1_DISPLAY_ID).build(),
            getEntityBuilder(ENTITY_2_ID, ENTITY_2_DISPLAY_ID).build(),
            getEntityBuilder(ENTITY_3_ID, ENTITY_3_DISPLAY_ID).build()
        );
    }

    public static Entity getEntityFixture() {
        return getEntityBuilder().build();
    }

    public static Entity getEntityFixtureEur() {
        return getEntityBuilder().functionalCurrency("EUR").build();
    }

    public static ImmutableEntity.Builder getEntityBuilder() {
        return getEntityBuilder(ENTITY_1_ID, ENTITY_1_DISPLAY_ID);
    }

    public static ImmutableEntity.Builder getEntityBuilder(String entityId, String displayId) {
        return ImmutableEntity.builder()
            .name("test entity")
            .entityId(entityId)
            .displayId(displayId)
            .timezone(TIME_ZONE)
            .prorationScheme(PRORATION_SCHEME)
            .prorationMode(PRORATION_MODE)
            .invoiceConfigId(INVOICE_CONFIG_ID)
            .functionalCurrency("USD");
    }
}
