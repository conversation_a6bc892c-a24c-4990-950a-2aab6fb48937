package com.subskribe.billy.entity.fixtures;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.entity.EntityContextProvider;
import java.util.Set;
import java.util.TimeZone;

public class EntityContextProviderFixture {

    public static final EntityContext ALL_ENTITIES_CONTEXT = EntityContext.builder()
        .selectedIds(EntityFixture.ALL_ENTITY_IDS)
        .authorizedIds(EntityFixture.ALL_ENTITY_IDS)
        .hasFullSelection(true)
        .hasFullAuthorization(true)
        .build();

    public static final EntityContext SINGLE_ENTITY_CONTEXT = EntityContext.builder()
        .selectedIds(Set.of(EntityFixture.ENTITY_1_ID))
        .authorizedIds(Set.of(EntityFixture.ENTITY_1_ID))
        .hasFullSelection(false)
        .hasFullAuthorization(false)
        .build();

    protected static final TimeZone TIME_ZONE = TimeZone.getTimeZone("UTC");

    public static EntityContextProvider buildAllEntitiesContext() {
        EntityContextProvider entityContextProvider = mock(EntityContextProvider.class);
        when(entityContextProvider.provideEntityContext()).thenReturn(ALL_ENTITIES_CONTEXT);
        when(entityContextProvider.provideAllEntitiesContext()).thenReturn(ALL_ENTITIES_CONTEXT);
        return entityContextProvider;
    }

    public static EntityContextProvider buildSingleEntityContext() {
        EntityContextProvider entityContextProvider = mock(EntityContextProvider.class);
        when(entityContextProvider.provideSelected()).thenReturn(EntityFixture.ENTITY_1_ID);
        when(entityContextProvider.provideEntityContext()).thenReturn(SINGLE_ENTITY_CONTEXT);
        when(entityContextProvider.provideAllEntitiesContext()).thenReturn(ALL_ENTITIES_CONTEXT);
        when(entityContextProvider.provideScopedEntityContext(eq(Set.of(EntityFixture.ENTITY_1_ID)))).thenReturn(SINGLE_ENTITY_CONTEXT);
        return entityContextProvider;
    }
}
