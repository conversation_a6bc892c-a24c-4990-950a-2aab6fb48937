package com.subskribe.billy.entity.fixtures;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.auth.model.PrincipalType;
import com.subskribe.billy.auth.model.UserPrincipal;
import com.subskribe.billy.di.hk2.providers.EntityPolicyProvider;
import com.subskribe.billy.entity.EntityConfiguration;
import com.subskribe.billy.user.provider.CurrentUserProvider;
import java.util.Optional;
import java.util.Set;

public class EntityPolicyProviderFixture {

    public static EntityPolicyProvider build(Set<String> entityIds, PrincipalType principalType) {
        CurrentUserProvider currentUserProvider = mock(CurrentUserProvider.class);
        UserPrincipal userPrincipal = mock(UserPrincipal.class);
        EntityContext entityContext = EntityContext.builder()
            .selectedIds(entityIds)
            .authorizedIds(entityIds)
            .hasFullSelection(false)
            .hasFullAuthorization(false)
            .build();
        when(userPrincipal.getEntityContext()).thenReturn(entityContext);
        when(currentUserProvider.provideOptional()).thenReturn(Optional.of(userPrincipal));
        BillyAuthPrincipal billyAuthPrincipal = mock(BillyAuthPrincipal.class);
        when(billyAuthPrincipal.getEntityContext()).thenReturn(entityContext);
        when((billyAuthPrincipal.getPrincipalType())).thenReturn(principalType);
        when(currentUserProvider.getBillyAuthPrincipal()).thenReturn(Optional.of(billyAuthPrincipal));
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        var entityConfiguration = new EntityConfiguration();
        entityConfiguration.setRlsEnabled(true);
        when(billyConfiguration.getEntityConfiguration()).thenReturn(entityConfiguration);
        return new EntityPolicyProvider(currentUserProvider, billyConfiguration);
    }

    public static EntityPolicyProvider buildWithEntityIds(Set<String> entityIds) {
        return build(entityIds, PrincipalType.USER);
    }
}
