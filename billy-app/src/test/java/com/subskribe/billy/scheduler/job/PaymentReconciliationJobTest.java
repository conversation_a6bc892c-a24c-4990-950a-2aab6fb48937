package com.subskribe.billy.scheduler.job;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.payment.integration.service.PaymentIntegrationGetService;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.payment.services.PaymentReconciliationService;
import com.subskribe.billy.scheduler.model.QuartzPaymentReconciliationJobConfiguration;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.Tenant;
import com.subskribe.billy.tenant.services.TenantService;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

class PaymentReconciliationJobTest {

    private static final String TENANT_ID = "tenant-id";

    private static final TenantIdProvider TENANT_ID_PROVIDER = new TenantIdProvider();

    private final PaymentReconciliationJob paymentReconciliationJob = new PaymentReconciliationJob();

    private AutoCloseable closeable;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private QuartzPaymentReconciliationJobConfiguration paymentReconciliationJobConfiguration;

    @Mock
    private TenantService tenantService;

    @Mock
    private PaymentIntegrationGetService paymentIntegrationGetService;

    @Mock
    private PaymentGetService paymentGetService;

    @Mock
    private PaymentReconciliationService paymentReconciliationService;

    @Mock
    private JobExecutionContext jobContext;

    @BeforeEach
    public void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
        setUpConfiguration();
        setUpTenantService();
        setUpPaymentIntegrationService();
        setUpJob();
    }

    @AfterEach
    public void tearDown() throws Exception {
        closeable.close();
    }

    @Test
    public void whenPaymentIntegrationNotEnabled_thenJobShouldNotGetPaymentsToReconcile() throws JobExecutionException {
        when(paymentIntegrationGetService.hasIntegration(any())).thenReturn(false);

        paymentReconciliationJob.execute(jobContext);

        verify(paymentGetService, never()).getPaymentsToReconcile(anyInt());
    }

    private void setUpConfiguration() {
        when(billyConfiguration.getQuartzPaymentReconciliationJobConfiguration()).thenReturn(paymentReconciliationJobConfiguration);
        when(paymentReconciliationJobConfiguration.getEnabled()).thenReturn(true);
    }

    private void setUpTenantService() {
        Tenant tenant = new Tenant(null, TENANT_ID, null, null, null, null, false, false, null, null);
        when(tenantService.getCachedTenants(true)).thenReturn(List.of());
        when(tenantService.getCachedTenants(false)).thenReturn(List.of(tenant));
    }

    private void setUpPaymentIntegrationService() {
        when(paymentIntegrationGetService.hasIntegration(any())).thenReturn(true);
    }

    private void setUpJob() {
        paymentReconciliationJob.setBillyConfiguration(billyConfiguration);
        paymentReconciliationJob.setTenantService(tenantService);
        paymentReconciliationJob.setTenantIdProvider(TENANT_ID_PROVIDER);
        paymentReconciliationJob.setPaymentIntegrationGetService(paymentIntegrationGetService);
        paymentReconciliationJob.setPaymentGetService(paymentGetService);
        paymentReconciliationJob.setPaymentReconciliationService(paymentReconciliationService);
    }
}
