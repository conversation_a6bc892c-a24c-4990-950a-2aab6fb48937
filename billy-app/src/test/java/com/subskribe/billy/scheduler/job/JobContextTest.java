package com.subskribe.billy.scheduler.job;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.quartz.JobDetail;
import org.slf4j.MDC;

class JobContextTest {

    private static final String JOB_ID_FIELD_NAME = "job_id";
    private static final long FIXED_MILLIS = 1620000000000L;

    private JobContext jobContext;

    @BeforeEach
    void setUp() {
        Clock fixedClock = Clock.fixed(Instant.ofEpochMilli(FIXED_MILLIS), ZoneId.systemDefault());
        jobContext = new JobContext(fixedClock);
        MDC.clear();
    }

    @AfterEach
    void tearDown() {
        MDC.clear();
    }

    @Test
    void startShouldSetJobIdInMDC() {
        JobDetail jobDetail = mock(JobDetail.class);
        TestJob testJob = new TestJob();
        when(jobDetail.getJobClass()).thenReturn((Class) testJob.getClass());

        jobContext.start(jobDetail);

        String jobId = MDC.get(JOB_ID_FIELD_NAME);
        assertNotNull(jobId);
        assertEquals("JOB-TestJob-1620000000000", jobId);
    }

    @Test
    void startShouldTrimJobIdIfNeeded() {
        JobDetail jobDetail = mock(JobDetail.class);
        var testJob = new ThisIsAVeryUnnecessarilyLengthilyNamedTestJobClassForTestingPurposesOnly();
        when(jobDetail.getJobClass()).thenReturn((Class) testJob.getClass());

        jobContext.start(jobDetail);

        String jobId = MDC.get(JOB_ID_FIELD_NAME);
        assertNotNull(jobId);
        assertEquals("JOB-ThisIsAVeryUnnecessarilyLengthilyNamedTestJobClassForTesting", jobId);
    }

    @Test
    void isJobContextShouldReturnFalseWhenNoJobIdInMDC() {
        assertFalse(jobContext.isJobContext());
    }

    @Test
    void isJobContextShouldReturnTrueWhenJobIdIsInMDC() {
        JobDetail jobDetail = mock(JobDetail.class);
        when(jobDetail.getJobClass()).thenReturn((Class) TestJob.class);

        jobContext.start(jobDetail);

        assertTrue(jobContext.isJobContext());
    }

    @Test
    void getJobIdShouldReturnNullWhenNoJobIdInMDC() {
        assertNull(jobContext.getJobId());
    }

    @Test
    void getJobIdShouldReturnJobIdWhenJobIdIsInMDC() {
        JobDetail jobDetail = mock(JobDetail.class);
        when(jobDetail.getJobClass()).thenReturn((Class) TestJob.class);
        jobContext.start(jobDetail);

        String jobId = jobContext.getJobId();

        assertNotNull(jobId);
        assertEquals("JOB-TestJob-1620000000000", jobId);
    }

    @Test
    void cleanShouldRemoveJobIdFromMDC() {
        JobDetail jobDetail = mock(JobDetail.class);
        when(jobDetail.getJobClass()).thenReturn((Class) TestJob.class);

        jobContext.start(jobDetail);
        assertNotNull(MDC.get(JOB_ID_FIELD_NAME)); // Ensure job ID is set

        jobContext.clean();
        assertNull(MDC.get(JOB_ID_FIELD_NAME)); // Ensure job ID is removed
    }

    @Test
    void cleanShouldDoNothingWhenNoJobIdInMDC() {
        assertNull(MDC.get(JOB_ID_FIELD_NAME)); // Ensure no job ID is set initially

        jobContext.clean();
        assertNull(MDC.get(JOB_ID_FIELD_NAME)); // Ensure no exception and still null
    }

    // Empty test job classes for testing
    private static class TestJob {}

    private static class ThisIsAVeryUnnecessarilyLengthilyNamedTestJobClassForTestingPurposesOnly {}
}
