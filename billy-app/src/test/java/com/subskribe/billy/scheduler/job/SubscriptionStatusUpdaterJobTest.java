package com.subskribe.billy.scheduler.job;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.hubspot.service.HubSpotService;
import com.subskribe.billy.infra.maintenance.MaintenanceService;
import com.subskribe.billy.salesforce.service.SalesforceJobQueueService;
import com.subskribe.billy.scheduler.model.QuartzSubscriptionStatusUpdaterConfiguration;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.model.SubscriptionState;
import com.subskribe.billy.subscription.services.SubscriptionGetService;
import com.subskribe.billy.subscription.services.SubscriptionService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.model.Tenant;
import com.subskribe.billy.tenant.services.TenantService;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class SubscriptionStatusUpdaterJobTest {

    private static final String TENANT_ID = "tenant-id";

    private static final TenantIdProvider TENANT_ID_PROVIDER = new TenantIdProvider();

    private static final String SUBSCRIPTION_ID_PREFIX = "SUBSCRIPTION_ID_";

    private static final String SUBSCRIPTION_ID_WITH_CORRECT_STATE = "SUBSCRIPTION_ID_CORRECT_STATE";

    private static final Map<String, UUID> SUBSCRIPTION_ID_TO_INTERNAL_ID_MAP = new HashMap<>();

    private static final int JOB_INTERVAL_GAP_IN_MINUTES = 30;

    private static final int DAY_IN_SECONDS = 24 * 60 * 60;

    private static final int MONTH_IN_SECONDS = 30 * DAY_IN_SECONDS;

    private static final Instant TWO_DAYS_AGO = Instant.now().minusSeconds(2 * DAY_IN_SECONDS);

    private static final Instant A_MONTH_AGO = Instant.now().minusSeconds(MONTH_IN_SECONDS);

    private static final Instant FOUR_DAYS_FROM_NOW = Instant.now().plusSeconds(4 * DAY_IN_SECONDS);

    private static final Instant THREE_MONTHS_FROM_NOW = Instant.now().plusSeconds(3 * MONTH_IN_SECONDS);

    private static final SubscriptionEntity SUBSCRIPTION_WITH_ALREADY_CORRECT_STATUS = generateSubscriptionWithAlreadyCorrectState();

    private static final SubscriptionEntity CANCELLED_SUBSCRIPTION = generateCancelledSubscriptionEntity();

    private static final SubscriptionEntity PENDING_CANCELLATION_SUBSCRIPTION = generatePendingCancellationSubscriptionEntity();

    private static final SubscriptionEntity ACTIVE_SUBSCRIPTION = generateActiveSubscriptionEntity();

    private static final SubscriptionEntity EXPIRED_SUBSCRIPTION = generateExpiredSubscriptionEntity();

    private static final SubscriptionEntity PENDING_SUBSCRIPTION = generatePendingSubscriptionEntity();

    private static final Set<SubscriptionEntity> ALL_RECENTLY_UPDATED_SUBSCRIPTIONS = Set.of(
        CANCELLED_SUBSCRIPTION,
        ACTIVE_SUBSCRIPTION,
        SUBSCRIPTION_WITH_ALREADY_CORRECT_STATUS
    );

    private static final Set<SubscriptionEntity> ALL_SUBSCRIPTIONS_WITH_WRONG_STATUSES = Set.of(
        PENDING_CANCELLATION_SUBSCRIPTION,
        EXPIRED_SUBSCRIPTION,
        PENDING_SUBSCRIPTION
    );

    private final SubscriptionStatusUpdaterJob subscriptionStatusUpdaterJob = new SubscriptionStatusUpdaterJob();

    @Mock
    private TenantService tenantService;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private SubscriptionService subscriptionService;

    @Mock
    private SubscriptionGetService subscriptionGetService;

    @Mock
    private SalesforceJobQueueService salesforceJobQueueService;

    @Mock
    private QuartzSubscriptionStatusUpdaterConfiguration subscriptionStatusUpdaterConfiguration;

    @Mock
    private HubSpotService hubSpotService;

    @Mock
    private JobExecutionContext jobContext;

    @Mock
    private MaintenanceService maintenanceService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(maintenanceService.getActiveMaintenance(Optional.of(TENANT_ID))).thenReturn(Optional.empty());
        setUpConfiguration();
        setUpTenantService();
        setUpSubscriptionGetService();
        setUpUnit();
    }

    @Test
    @Order(1)
    public void shouldNotRunWhenDisabled() throws JobExecutionException {
        when(subscriptionStatusUpdaterConfiguration.getEnabled()).thenReturn(false);
        subscriptionStatusUpdaterJob.execute(jobContext);
        verify(tenantService, never()).getCachedTenants(false);
        verify(tenantService, never()).getCachedTenants(true);
    }

    @Test
    @Order(2)
    public void executeJob() throws JobExecutionException {
        subscriptionStatusUpdaterJob.execute(jobContext);
        /*
        This for loop intentionally goes over ALL the states of a subscription. This would force us to make sure this job runs correctly and supports
        a new state when it is added in the future.
         */
        for (SubscriptionState subscriptionState : SubscriptionState.values()) {
            String subscriptionId = getSubscriptionIdForState(subscriptionState);
            UUID id = SUBSCRIPTION_ID_TO_INTERNAL_ID_MAP.get(subscriptionId);
            verify(subscriptionService, times(1)).updateSubscriptionEntityState(id, subscriptionState);
        }
        UUID idWithCorrectState = SUBSCRIPTION_ID_TO_INTERNAL_ID_MAP.get(SUBSCRIPTION_ID_WITH_CORRECT_STATE);
        verify(subscriptionService, never()).updateSubscriptionEntityState(idWithCorrectState, SubscriptionState.ACTIVE);
    }

    private void setUpConfiguration() {
        when(billyConfiguration.getQuartzSubscriptionStatusUpdaterConfiguration()).thenReturn(subscriptionStatusUpdaterConfiguration);
        when(subscriptionStatusUpdaterConfiguration.getEnabled()).thenReturn(true);
        when(subscriptionStatusUpdaterConfiguration.getIntervalInMinutes()).thenReturn(JOB_INTERVAL_GAP_IN_MINUTES);
    }

    private void setUpTenantService() {
        Tenant tenant = new Tenant(null, TENANT_ID, null, null, null, null, false, false, null, null);
        when(tenantService.getCachedTenants(true)).thenReturn(List.of());
        when(tenantService.getCachedTenants(false)).thenReturn(List.of(tenant));
    }

    private void setUpSubscriptionGetService() {
        when(subscriptionGetService.getAllRecentlyUpdatedSubscriptionsForTenant(JOB_INTERVAL_GAP_IN_MINUTES + 1)).thenReturn(
            ALL_RECENTLY_UPDATED_SUBSCRIPTIONS
        );
        when(subscriptionGetService.getAllSubscriptionsWhichNeedStatusUpdateForTenant()).thenReturn(ALL_SUBSCRIPTIONS_WITH_WRONG_STATUSES);
    }

    private void setUpUnit() {
        subscriptionStatusUpdaterJob.setTenantIdProvider(TENANT_ID_PROVIDER);
        subscriptionStatusUpdaterJob.setBillyConfiguration(billyConfiguration);
        subscriptionStatusUpdaterJob.setTenantService(tenantService);
        subscriptionStatusUpdaterJob.setSubscriptionService(subscriptionService);
        subscriptionStatusUpdaterJob.setSubscriptionGetService(subscriptionGetService);
        subscriptionStatusUpdaterJob.setSalesforceJobQueueService(salesforceJobQueueService);
        subscriptionStatusUpdaterJob.setMaintenanceService(maintenanceService);
        subscriptionStatusUpdaterJob.setHubSpotService(hubSpotService);
    }

    private static SubscriptionEntity generateCancelledSubscriptionEntity() {
        return generateSubscriptionEntity(SubscriptionState.CANCELLED, A_MONTH_AGO, TWO_DAYS_AGO, THREE_MONTHS_FROM_NOW);
    }

    private static SubscriptionEntity generatePendingCancellationSubscriptionEntity() {
        return generateSubscriptionEntity(SubscriptionState.PENDING_CANCELLATION, A_MONTH_AGO, FOUR_DAYS_FROM_NOW, THREE_MONTHS_FROM_NOW);
    }

    private static SubscriptionEntity generatePendingSubscriptionEntity() {
        return generateSubscriptionEntity(SubscriptionState.PENDING, FOUR_DAYS_FROM_NOW, null, THREE_MONTHS_FROM_NOW);
    }

    private static SubscriptionEntity generateExpiredSubscriptionEntity() {
        return generateSubscriptionEntity(SubscriptionState.EXPIRED, A_MONTH_AGO, null, TWO_DAYS_AGO);
    }

    private static SubscriptionEntity generateActiveSubscriptionEntity() {
        return generateSubscriptionEntity(SubscriptionState.ACTIVE, A_MONTH_AGO, null, FOUR_DAYS_FROM_NOW);
    }

    private static SubscriptionEntity generateSubscriptionWithAlreadyCorrectState() {
        SubscriptionEntity subscriptionEntity = generateSubscriptionEntity(SubscriptionState.ACTIVE, A_MONTH_AGO, null, FOUR_DAYS_FROM_NOW);
        subscriptionEntity.setState(SubscriptionState.ACTIVE);
        subscriptionEntity.setSubscriptionId(SUBSCRIPTION_ID_WITH_CORRECT_STATE);
        SUBSCRIPTION_ID_TO_INTERNAL_ID_MAP.put(SUBSCRIPTION_ID_WITH_CORRECT_STATE, subscriptionEntity.getId());
        return subscriptionEntity;
    }

    private static SubscriptionEntity generateSubscriptionEntity(
        SubscriptionState subscriptionState,
        Instant startDate,
        Instant cancelledDate,
        Instant endDate
    ) {
        SubscriptionEntity subscriptionEntity = new SubscriptionEntity();
        UUID id = AutoGenerate.getNewUuid();
        subscriptionEntity.setId(id);
        String subscriptionId = getSubscriptionIdForState(subscriptionState);
        subscriptionEntity.setSubscriptionId(subscriptionId);
        SUBSCRIPTION_ID_TO_INTERNAL_ID_MAP.put(subscriptionId, id);
        subscriptionEntity.setCanceledDate(cancelledDate);
        subscriptionEntity.setStartDate(startDate);
        subscriptionEntity.setEndDate(endDate);
        return subscriptionEntity;
    }

    private static String getSubscriptionIdForState(SubscriptionState subscriptionState) {
        if (subscriptionState == null) {
            return SUBSCRIPTION_ID_PREFIX + "NULL";
        }

        return SUBSCRIPTION_ID_PREFIX + subscriptionState.name();
    }
}
