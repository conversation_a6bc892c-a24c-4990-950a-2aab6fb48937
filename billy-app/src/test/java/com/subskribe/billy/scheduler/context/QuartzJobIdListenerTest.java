package com.subskribe.billy.scheduler.context;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.scheduler.job.JobContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

class QuartzJobIdListenerTest {

    private QuartzJobIdListener listener;

    @Mock
    private JobContext jobContext;

    @Mock
    private JobExecutionContext jobExecutionContext;

    @Mock
    private JobDetail jobDetail;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        listener = new QuartzJobIdListener(jobContext);
        when(jobExecutionContext.getJobDetail()).thenReturn(jobDetail);
    }

    @Test
    void getNameShouldReturnListenerName() {
        assertThat(listener.getName()).isEqualTo("QuartzJobIdListener");
    }

    @Test
    void jobToBeExecutedShouldStartJobContext() {
        listener.jobToBeExecuted(jobExecutionContext);

        verify(jobContext).start(jobDetail);
    }

    @Test
    void jobExecutionVetoedShouldCleanJobContext() {
        listener.jobExecutionVetoed(jobExecutionContext);

        verify(jobContext).clean();
    }

    @Test
    void jobWasExecutedShouldCleanJobContext() {
        JobExecutionException exception = null;

        listener.jobWasExecuted(jobExecutionContext, exception);

        verify(jobContext).clean();
    }

    @Test
    void jobWasExecutedWithExceptionShouldStillCleanJobContext() {
        JobExecutionException exception = new JobExecutionException("Test exception");

        listener.jobWasExecuted(jobExecutionContext, exception);

        verify(jobContext).clean();
    }
}
