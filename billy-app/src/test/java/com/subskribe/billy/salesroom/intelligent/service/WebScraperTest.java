package com.subskribe.billy.salesroom.intelligent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.exception.InvalidInputException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import javax.net.ssl.SSLHandshakeException;
import org.jooq.tools.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.MockedConstruction;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

class WebScraperTest {

    private static final String TEST_URL = "https://example.com";

    private static final String TEST_HTML = "<html><head><title>Test</title></head><body>Test content</body></html>";

    private final WebScraper webScraper = new WebScraper();

    @Test
    void testExtractHtmlSuccess() throws IOException {
        Document realDocument = Jsoup.parse(TEST_HTML);
        String expectedHtml = realDocument.html();

        try (MockedStatic<Jsoup> jsoupMock = Mockito.mockStatic(Jsoup.class)) {
            Connection connectionMock = mock(Connection.class);

            jsoupMock.when(() -> Jsoup.connect(TEST_URL)).thenReturn(connectionMock);
            doReturn(connectionMock).when(connectionMock).userAgent(anyString());
            doReturn(connectionMock).when(connectionMock).timeout(any(Integer.class));
            doReturn(connectionMock).when(connectionMock).followRedirects(any(Boolean.class));
            doReturn(realDocument).when(connectionMock).get();

            String result = webScraper.extractHtml(TEST_URL);

            assertEquals(expectedHtml, result);
        }
    }

    @Test
    void testExtractHtmlEmptyUrl() {
        try {
            webScraper.extractHtml(StringUtils.EMPTY);
            Assertions.fail();
        } catch (Exception e) {
            assertTrue(e instanceof InvalidInputException);
        }
    }

    @Test
    void testExtractHtmlInvalidUrl() {
        assertThrows(IOException.class, () -> webScraper.extractHtml("not a url"));
    }

    @Test
    @SuppressWarnings("unused")
    void testExtractHtmlJsoupExceptionFallbackSuccess() throws Exception {
        Document realDocument = Jsoup.parse(TEST_HTML);
        String expectedHtml = realDocument.html();

        try (MockedStatic<Jsoup> jsoupMock = Mockito.mockStatic(Jsoup.class)) {
            Connection connectionMock = mock(Connection.class);
            jsoupMock.when(() -> Jsoup.connect(TEST_URL)).thenReturn(connectionMock);
            doReturn(connectionMock).when(connectionMock).userAgent(anyString());
            doReturn(connectionMock).when(connectionMock).timeout(any(Integer.class));
            doReturn(connectionMock).when(connectionMock).followRedirects(any(Boolean.class));
            doThrow(new IOException("Jsoup error")).when(connectionMock).get();

            try (
                MockedConstruction<URL> mockUrlConstruction = Mockito.mockConstruction(URL.class, (mockUrl, context) -> {
                    HttpURLConnection httpConnectionMock = mock(HttpURLConnection.class);
                    doReturn(httpConnectionMock).when(mockUrl).openConnection();
                    doReturn(HttpURLConnection.HTTP_OK).when(httpConnectionMock).getResponseCode();

                    ByteArrayInputStream inputStream = new ByteArrayInputStream(TEST_HTML.getBytes());
                    doReturn(inputStream).when(httpConnectionMock).getInputStream();
                })
            ) {
                jsoupMock.when(() -> Jsoup.parse(any(InputStream.class), anyString(), anyString())).thenReturn(realDocument);

                String result = webScraper.extractHtml(TEST_URL);

                assertEquals(expectedHtml, result);
            }
        }
    }

    @Test
    void testExtractHtmlSSLException() throws Exception {
        try (MockedStatic<Jsoup> jsoupMock = Mockito.mockStatic(Jsoup.class)) {
            Connection connectionMock = mock(Connection.class);
            jsoupMock.when(() -> Jsoup.connect(TEST_URL)).thenReturn(connectionMock);
            when(connectionMock.userAgent(anyString())).thenReturn(connectionMock);
            when(connectionMock.timeout(any(Integer.class))).thenReturn(connectionMock);
            when(connectionMock.followRedirects(any(Boolean.class))).thenReturn(connectionMock);
            when(connectionMock.get()).thenThrow(new SSLHandshakeException("SSL error"));

            try {
                webScraper.extractHtml(TEST_URL);
            } catch (IOException e) {
                assertTrue(e.getMessage().contains("Failed to establish secure connection"));
            }
        }
    }

    @Test
    void testIsUrlAccessibleSuccess() {
        try (
            MockedConstruction<URL> ignored = Mockito.mockConstruction(URL.class, (mock, context) -> {
                HttpURLConnection connectionMock = mock(HttpURLConnection.class);
                when(mock.openConnection()).thenReturn(connectionMock);
                when(connectionMock.getResponseCode()).thenReturn(HttpURLConnection.HTTP_OK);
            })
        ) {
            boolean result = webScraper.isUrlAccessible(TEST_URL);
            assertTrue(result);
        }
    }

    @Test
    void testIsUrlAccessibleNotFound() {
        try (
            MockedConstruction<URL> ignored = Mockito.mockConstruction(URL.class, (mock, context) -> {
                HttpURLConnection connectionMock = mock(HttpURLConnection.class);
                when(mock.openConnection()).thenReturn(connectionMock);
                when(connectionMock.getResponseCode()).thenReturn(HttpURLConnection.HTTP_NOT_FOUND);
            })
        ) {
            boolean result = webScraper.isUrlAccessible(TEST_URL);

            assertFalse(result);
        }
    }

    @Test
    void testIsUrlAccessibleException() {
        try (
            MockedConstruction<URL> ignored = Mockito.mockConstruction(URL.class, (mock, context) ->
                when(mock.openConnection()).thenThrow(new IOException("Connection error"))
            )
        ) {
            boolean result = webScraper.isUrlAccessible(TEST_URL);

            assertFalse(result);
        }
    }

    @Test
    void testIsImageUrlSuccess() {
        try (
            MockedConstruction<URL> ignored = Mockito.mockConstruction(URL.class, (mock, context) -> {
                HttpURLConnection connectionMock = mock(HttpURLConnection.class);
                when(mock.openConnection()).thenReturn(connectionMock);
                when(connectionMock.getResponseCode()).thenReturn(HttpURLConnection.HTTP_OK);
                when(connectionMock.getContentType()).thenReturn("image/jpeg");
            })
        ) {
            boolean result = webScraper.isImageUrl(TEST_URL);

            assertTrue(result);
        }
    }

    @Test
    void testIsImageUrlNotImage() {
        try (
            MockedConstruction<URL> ignored = Mockito.mockConstruction(URL.class, (mock, context) -> {
                HttpURLConnection connectionMock = mock(HttpURLConnection.class);
                when(mock.openConnection()).thenReturn(connectionMock);
                when(connectionMock.getResponseCode()).thenReturn(HttpURLConnection.HTTP_OK);
                when(connectionMock.getContentType()).thenReturn("text/html");
            })
        ) {
            boolean result = webScraper.isImageUrl(TEST_URL);

            assertFalse(result);
        }
    }

    @Test
    void testExtractCssFromUrlSuccess() throws Exception {
        Document documentMock = mock(Document.class);

        try (MockedStatic<Jsoup> jsoupMock = Mockito.mockStatic(Jsoup.class)) {
            Connection connectionMock = mock(Connection.class);
            jsoupMock.when(() -> Jsoup.connect(TEST_URL)).thenReturn(connectionMock);
            when(connectionMock.userAgent(anyString())).thenReturn(connectionMock);
            when(connectionMock.timeout(any(Integer.class))).thenReturn(connectionMock);
            when(connectionMock.followRedirects(any(Boolean.class))).thenReturn(connectionMock);
            when(connectionMock.get()).thenReturn(documentMock);

            org.jsoup.select.Elements styleElements = new org.jsoup.select.Elements();
            org.jsoup.nodes.Element styleElement = mock(org.jsoup.nodes.Element.class);
            when(styleElement.html()).thenReturn("body { color: #333333; }");
            styleElements.add(styleElement);
            when(documentMock.select("style")).thenReturn(styleElements);

            org.jsoup.select.Elements linkElements = new org.jsoup.select.Elements();
            when(documentMock.select("link[rel=stylesheet]")).thenReturn(linkElements);

            String result = webScraper.extractCssFromUrl(TEST_URL);

            assertEquals("body { color: #333333; }\n", result);
        }
    }
}
