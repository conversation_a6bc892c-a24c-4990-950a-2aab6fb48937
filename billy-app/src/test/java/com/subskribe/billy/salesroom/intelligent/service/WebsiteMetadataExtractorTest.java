package com.subskribe.billy.salesroom.intelligent.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.ai.service.bedrock.BedrockClientProvider;
import com.subskribe.billy.salesroom.intelligent.model.IntelligentSalesRoomWebsiteMetadata;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeAsyncClient;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelRequest;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelResponse;

class WebsiteMetadataExtractorTest {

    private static final String TEST_URL = "https://example.com";
    private static final String TEST_HTML = "<html><head><title>Test</title></head><body>Test content</body></html>";
    private static final String TEST_CSS = "body { color: #333333; }";

    private BedrockRuntimeAsyncClient mockBedrockClient;

    private WebScraper webScraper;

    private WebsiteMetadataExtractor websiteMetadataExtractor;

    @BeforeEach
    void setUp() throws IOException {
        mockBedrockClient = mock(BedrockRuntimeAsyncClient.class);
        BedrockClientProvider mockBedrockClientProvider = mock(BedrockClientProvider.class);
        when(mockBedrockClientProvider.getBedrockRuntimeAsyncClient()).thenReturn(mockBedrockClient);
        webScraper = mock(WebScraper.class);
        when(webScraper.extractHtml(anyString())).thenReturn(TEST_HTML);
        when(webScraper.extractCssFromUrl(anyString())).thenReturn(TEST_CSS);
        when(webScraper.isUrlAccessible(anyString())).thenReturn(true);
        when(webScraper.isImageUrl(anyString())).thenReturn(true);

        websiteMetadataExtractor = new WebsiteMetadataExtractor(webScraper, mockBedrockClientProvider);
    }

    @SuppressWarnings("unchecked")
    private void clearMetadataCache() throws Exception {
        java.lang.reflect.Field cacheField = WebsiteMetadataExtractor.class.getDeclaredField("METADATA_CACHE");
        cacheField.setAccessible(true);
        com.google.common.cache.Cache<String, IntelligentSalesRoomWebsiteMetadata> cache = (com.google.common.cache.Cache<
                String,
                IntelligentSalesRoomWebsiteMetadata
            >) cacheField.get(null);
        cache.invalidateAll();
    }

    @Test
    void testExtractMetadataSuccessfulExtraction() throws Exception {
        clearMetadataCache();
        String claudeResponse = createClaudeResponse("/logo.png", "https://example.com/logo.png", "#333333", "#666666", "Arial, sans-serif", "16px");
        mockBedrockResponse(claudeResponse);

        Optional<IntelligentSalesRoomWebsiteMetadata> result = websiteMetadataExtractor.extractMetadata(TEST_URL);

        assertTrue(result.isPresent());
        IntelligentSalesRoomWebsiteMetadata metadata = result.get();
        assertTrue(metadata.complete());

        assertEquals("/logo.png", metadata.logo().url());
        assertEquals("https://example.com/logo.png", metadata.logo().absoluteUrl());
        assertEquals(0.95, metadata.logo().confidence(), 0.001);

        assertEquals("#333333", metadata.primary().color());
        assertEquals("header background", metadata.primary().location());
        assertEquals(0.9, metadata.primary().confidence(), 0.001);

        assertEquals("#666666", metadata.secondary().color());
        assertEquals("buttons and links", metadata.secondary().location());
        assertEquals(0.8, metadata.secondary().confidence(), 0.001);

        assertEquals("Arial, sans-serif", metadata.fontFamily().value());
        assertEquals(0.85, metadata.fontFamily().confidence(), 0.001);

        assertEquals("16px", metadata.fontSize().value());
        assertEquals(0.7, metadata.fontSize().confidence(), 0.001);
    }

    @Test
    void testExtractMetadataPartialExtraction() throws Exception {
        clearMetadataCache();

        String claudeResponse =
            """
            {
              "content": [{
                "text": "{\
              \\"logo\\": null,\
              \\"primary\\": {\\"color\\": \\"#333333\\", \\"confidence\\": 0.9, \\"location\\": \\"header background\\"},\
              \\"secondary\\": {\\"color\\": \\"#666666\\", \\"confidence\\": 0.8, \\"location\\": \\"buttons and links\\"},\
              \\"fontFamily\\": {\\"value\\": \\"Arial, sans-serif\\", \\"confidence\\": 0.85},\
              \\"fontSize\\": {\\"value\\": \\"16px\\", \\"confidence\\": 0.7}\
            }"
              }]
            }
            """;

        mockBedrockResponse(claudeResponse);

        Optional<IntelligentSalesRoomWebsiteMetadata> result = websiteMetadataExtractor.extractMetadata(TEST_URL);

        assertTrue(result.isPresent());
        IntelligentSalesRoomWebsiteMetadata metadata = result.get();

        assertFalse(metadata.complete());
        assertNull(metadata.logo());

        assertEquals("#333333", metadata.primary().color());
        assertEquals("#666666", metadata.secondary().color());
        assertEquals("Arial, sans-serif", metadata.fontFamily().value());
        assertEquals("16px", metadata.fontSize().value());
    }

    @Test
    void testExtractMetadataLogoNotAccessible() throws Exception {
        clearMetadataCache();

        String claudeResponse = createClaudeResponse("/logo.png", "https://example.com/logo.png", "#333333", "#666666", "Arial, sans-serif", "16px");
        mockBedrockResponse(claudeResponse);

        when(webScraper.isUrlAccessible("https://example.com/logo.png")).thenReturn(false);

        Optional<IntelligentSalesRoomWebsiteMetadata> result = websiteMetadataExtractor.extractMetadata(TEST_URL);

        assertTrue(result.isPresent());
        IntelligentSalesRoomWebsiteMetadata metadata = result.get();

        assertFalse(metadata.complete());
        assertNull(metadata.logo());
    }

    @Test
    void testExtractMetadataLogoNotImage() throws Exception {
        clearMetadataCache();

        String claudeResponse = createClaudeResponse("/logo.png", "https://example.com/logo.png", "#333333", "#666666", "Arial, sans-serif", "16px");
        mockBedrockResponse(claudeResponse);

        when(webScraper.isUrlAccessible("https://example.com/logo.png")).thenReturn(false);

        Optional<IntelligentSalesRoomWebsiteMetadata> result = websiteMetadataExtractor.extractMetadata(TEST_URL);

        assertTrue(result.isPresent());
        IntelligentSalesRoomWebsiteMetadata metadata = result.get();

        assertFalse(metadata.complete());
        assertNull(metadata.logo());
    }

    @Test
    void testExtractMetadataInvalidColorFormat() throws Exception {
        clearMetadataCache();

        String claudeResponse =
            """
            {
              "content": [{
                "text": "{\
              \\"logo\\": {\\"url\\": \\"/logo.png\\", \\"absoluteUrl\\": \\"https://example.com/logo.png\\", \\"confidence\\": 0.95},\
              \\"primary\\": {\\"color\\": \\"invalid-color\\", \\"confidence\\": 0.9, \\"location\\": \\"header background\\"},\
              \\"secondary\\": {\\"color\\": \\"#666666\\", \\"confidence\\": 0.8, \\"location\\": \\"buttons and links\\"},\
              \\"fontFamily\\": {\\"value\\": \\"Arial, sans-serif\\", \\"confidence\\": 0.85},\
              \\"fontSize\\": {\\"value\\": \\"16px\\", \\"confidence\\": 0.7}\
            }"
              }]
            }
            """;

        mockBedrockResponse(claudeResponse);

        Optional<IntelligentSalesRoomWebsiteMetadata> result = websiteMetadataExtractor.extractMetadata(TEST_URL);

        assertTrue(result.isPresent());
        IntelligentSalesRoomWebsiteMetadata metadata = result.get();

        assertFalse(metadata.complete());
        assertNull(metadata.primary());
    }

    @Test
    void testExtractMetadataWebScraperException() throws Exception {
        clearMetadataCache();

        when(webScraper.extractHtml(TEST_URL)).thenThrow(new IOException("Failed to extract HTML"));

        Optional<IntelligentSalesRoomWebsiteMetadata> result = websiteMetadataExtractor.extractMetadata(TEST_URL);

        assertFalse(result.isPresent());
    }

    @Test
    void testExtractMetadataBedrockClientException() throws Exception {
        clearMetadataCache();

        CompletableFuture<InvokeModelResponse> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Bedrock API error"));
        when(mockBedrockClient.invokeModel(any(InvokeModelRequest.class))).thenReturn(failedFuture);

        Optional<IntelligentSalesRoomWebsiteMetadata> result = websiteMetadataExtractor.extractMetadata(TEST_URL);

        assertFalse(result.isPresent());
    }

    @Test
    void testExtractMetadataInvalidJson() throws Exception {
        clearMetadataCache();

        String claudeResponse = "{\"content\": [{\"text\": \"This is not a valid JSON response.\"}]}";
        mockBedrockResponse(claudeResponse);

        Optional<IntelligentSalesRoomWebsiteMetadata> result = websiteMetadataExtractor.extractMetadata(TEST_URL);

        assertFalse(result.isPresent());
    }

    private void mockBedrockResponse(String claudeResponse) {
        InvokeModelResponse mockResponse = mock(InvokeModelResponse.class);
        when(mockResponse.body()).thenReturn(SdkBytes.fromString(claudeResponse, StandardCharsets.UTF_8));

        CompletableFuture<InvokeModelResponse> future = CompletableFuture.completedFuture(mockResponse);
        when(mockBedrockClient.invokeModel(any(InvokeModelRequest.class))).thenReturn(future);
    }

    private String createClaudeResponse(
        String logoUrl,
        String logoAbsoluteUrl,
        String primaryColor,
        String secondaryColor,
        String fontFamily,
        String fontSize
    ) {
        return String.format(
            """
            {
              "content": [{
                "text": "{\
              \\"logo\\": {\\"url\\": \\"%s\\", \\"absoluteUrl\\": \\"%s\\", \\"confidence\\": 0.95},\
              \\"primary\\": {\\"color\\": \\"%s\\", \\"confidence\\": 0.9, \\"location\\": \\"header background\\"},\
              \\"secondary\\": {\\"color\\": \\"%s\\", \\"confidence\\": 0.8, \\"location\\": \\"buttons and links\\"},\
              \\"fontFamily\\": {\\"value\\": \\"%s\\", \\"confidence\\": 0.85},\
              \\"fontSize\\": {\\"value\\": \\"%s\\", \\"confidence\\": 0.7}\
            }"
              }]
            }
            """,
            logoUrl,
            logoAbsoluteUrl,
            primaryColor,
            secondaryColor,
            fontFamily,
            fontSize
        );
    }
}
