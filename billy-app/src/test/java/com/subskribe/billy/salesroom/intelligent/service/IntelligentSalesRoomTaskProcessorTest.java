package com.subskribe.billy.salesroom.intelligent.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.resources.json.order.OrderJson;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.ProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class IntelligentSalesRoomTaskProcessorTest {

    private static final String TENANT_ID = "test-tenant-id";

    private static final String ORDER_ID = "test-order-id";

    private static final String ENTITY_ID = "test-entity-id";

    private static final TaskModule TASK_MODULE = new TaskModule("intelligent-sales-room");

    private static final TaskType SCHEDULE_TASK_TYPE = new TaskType("intelligent-sales-room-status-update-based-on-order");

    private static final Duration TIMEOUT = Duration.ofMinutes(1);

    private static final String EVENT_TYPE_TASK_METADATA_KEY = "eventType";

    @Mock
    private IntelligentSalesRoomService intelligentSalesRoomService;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private Event event;

    @Mock
    private QueuedTask queuedTask;

    private IntelligentSalesRoomTaskProcessor taskProcessor;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        taskProcessor = new IntelligentSalesRoomTaskProcessor(intelligentSalesRoomService, tenantIdProvider);
        objectMapper = JacksonProvider.defaultMapper();
    }

    @Test
    void createTaskForEventWithOrderSubmittedEventShouldCreateTask() throws JsonProcessingException {
        OrderJson orderJson = createSampleOrderJson();
        String payload = objectMapper.writeValueAsString(orderJson);
        ByteBuffer byteBuffer = ByteBuffer.wrap(payload.getBytes(StandardCharsets.UTF_8));

        when(event.getType()).thenReturn(EventType.ORDER_SUBMITTED);
        when(event.getPayload()).thenReturn(byteBuffer);
        when(event.getPayloadAsString()).thenReturn(payload);

        QueuedTaskRequest result = taskProcessor.createTaskForEvent(event);

        assertThat(result).isEqualTo(
            ImmutableQueuedTaskRequest.builder()
                .module(TASK_MODULE)
                .type(SCHEDULE_TASK_TYPE)
                .taskData(ORDER_ID)
                .tenantId(TENANT_ID)
                .entityId(ENTITY_ID)
                .taskMetadata(Map.of(EVENT_TYPE_TASK_METADATA_KEY, EventType.ORDER_SUBMITTED.name()))
                .build()
        );
    }

    @Test
    void createTaskForEventWithOrderApprovedEventShouldCreateTask() throws JsonProcessingException {
        OrderJson orderJson = createSampleOrderJson();
        String payload = objectMapper.writeValueAsString(orderJson);
        ByteBuffer byteBuffer = ByteBuffer.wrap(payload.getBytes(StandardCharsets.UTF_8));

        when(event.getType()).thenReturn(EventType.ORDER_APPROVED);
        when(event.getPayload()).thenReturn(byteBuffer);
        when(event.getPayloadAsString()).thenReturn(payload);

        QueuedTaskRequest result = taskProcessor.createTaskForEvent(event);

        assertThat(result).isEqualTo(
            ImmutableQueuedTaskRequest.builder()
                .module(TASK_MODULE)
                .type(SCHEDULE_TASK_TYPE)
                .taskData(ORDER_ID)
                .tenantId(TENANT_ID)
                .entityId(ENTITY_ID)
                .taskMetadata(Map.of(EVENT_TYPE_TASK_METADATA_KEY, EventType.ORDER_APPROVED.name()))
                .build()
        );
    }

    @Test
    void createTaskForEventWithOrderRevertedToDraftEventShouldCreateTask() throws JsonProcessingException {
        OrderJson orderJson = createSampleOrderJson();
        String payload = objectMapper.writeValueAsString(orderJson);
        ByteBuffer byteBuffer = ByteBuffer.wrap(payload.getBytes(StandardCharsets.UTF_8));

        when(event.getType()).thenReturn(EventType.ORDER_REVERTED_TO_DRAFT);
        when(event.getPayload()).thenReturn(byteBuffer);
        when(event.getPayloadAsString()).thenReturn(payload);

        QueuedTaskRequest result = taskProcessor.createTaskForEvent(event);

        assertThat(result).isEqualTo(
            ImmutableQueuedTaskRequest.builder()
                .module(TASK_MODULE)
                .type(SCHEDULE_TASK_TYPE)
                .taskData(ORDER_ID)
                .tenantId(TENANT_ID)
                .entityId(ENTITY_ID)
                .taskMetadata(Map.of(EVENT_TYPE_TASK_METADATA_KEY, EventType.ORDER_REVERTED_TO_DRAFT.name()))
                .build()
        );
    }

    @Test
    void createTaskForEventWithESignatureVoidedEventShouldCreateTask() throws JsonProcessingException {
        OrderJson orderJson = createSampleOrderJson();
        String payload = objectMapper.writeValueAsString(orderJson);
        ByteBuffer byteBuffer = ByteBuffer.wrap(payload.getBytes(StandardCharsets.UTF_8));

        when(event.getType()).thenReturn(EventType.ESIGNATURE_VOIDED);
        when(event.getPayload()).thenReturn(byteBuffer);
        when(event.getPayloadAsString()).thenReturn(payload);

        QueuedTaskRequest result = taskProcessor.createTaskForEvent(event);

        assertThat(result).isEqualTo(
            ImmutableQueuedTaskRequest.builder()
                .module(TASK_MODULE)
                .type(SCHEDULE_TASK_TYPE)
                .taskData(ORDER_ID)
                .tenantId(TENANT_ID)
                .entityId(ENTITY_ID)
                .taskMetadata(Map.of(EVENT_TYPE_TASK_METADATA_KEY, EventType.ESIGNATURE_VOIDED.name()))
                .build()
        );
    }

    @Test
    void createTaskForEventWithUnsupportedEventTypeShouldThrowException() {
        when(event.getType()).thenReturn(EventType.INVOICE_POSTED);

        InvalidInputException exception = assertThrows(InvalidInputException.class, () -> taskProcessor.createTaskForEvent(event));

        assertEquals("Unsupported event type: INVOICE_POSTED", exception.getMessage());
    }

    @Test
    void createTaskForEventWithInvalidJsonPayloadShouldThrowException() {
        when(event.getType()).thenReturn(EventType.ORDER_APPROVED);
        when(event.getPayloadAsString()).thenReturn("invalid-json");

        ServiceFailureException exception = assertThrows(ServiceFailureException.class, () -> taskProcessor.createTaskForEvent(event));

        assertEquals("Failed to parse order from event payload", exception.getMessage());
    }

    @Test
    void getEventTypesShouldReturnCorrectEventTypes() {
        Set<EventType> eventTypes = taskProcessor.getEventTypes();

        assertEquals(5, eventTypes.size());
        assertTrue(eventTypes.contains(EventType.ORDER_SUBMITTED));
        assertTrue(eventTypes.contains(EventType.ORDER_APPROVED));
        assertTrue(eventTypes.contains(EventType.ORDER_REVERTED_TO_DRAFT));
        assertTrue(eventTypes.contains(EventType.ESIGNATURE_VOIDED));
    }

    @Test
    void processWithOrderSubmittedEventShouldCallServiceAndReturnSuccess() {
        when(queuedTask.getTaskData()).thenReturn(ORDER_ID);
        when(queuedTask.getTaskMetadata()).thenReturn(Map.of(EVENT_TYPE_TASK_METADATA_KEY, EventType.ORDER_SUBMITTED.name()));

        TaskResult result = taskProcessor.process(queuedTask);

        verify(intelligentSalesRoomService).handleOrderSubmitted(ORDER_ID);
        assertTrue(result.isSuccessful());
    }

    @Test
    void processWithOrderApprovedEventShouldCallServiceAndReturnSuccess() {
        when(queuedTask.getTaskData()).thenReturn(ORDER_ID);
        when(queuedTask.getTaskMetadata()).thenReturn(Map.of(EVENT_TYPE_TASK_METADATA_KEY, EventType.ORDER_APPROVED.name()));

        TaskResult result = taskProcessor.process(queuedTask);

        verify(intelligentSalesRoomService).handleOrderApproved(ORDER_ID);
        assertTrue(result.isSuccessful());
    }

    @Test
    void processWithOrderRevertedToDraftEventShouldCallServiceAndReturnSuccess() {
        when(queuedTask.getTaskData()).thenReturn(ORDER_ID);
        when(queuedTask.getTaskMetadata()).thenReturn(Map.of(EVENT_TYPE_TASK_METADATA_KEY, EventType.ORDER_REVERTED_TO_DRAFT.name()));

        TaskResult result = taskProcessor.process(queuedTask);

        verify(intelligentSalesRoomService).handleOrderReverted(ORDER_ID);
        assertTrue(result.isSuccessful());
    }

    @Test
    void processWithESignatureVoidedEventShouldCallServiceAndReturnSuccess() {
        when(queuedTask.getTaskData()).thenReturn(ORDER_ID);
        when(queuedTask.getTaskMetadata()).thenReturn(Map.of(EVENT_TYPE_TASK_METADATA_KEY, EventType.ESIGNATURE_VOIDED.name()));

        TaskResult result = taskProcessor.process(queuedTask);

        verify(intelligentSalesRoomService).handleESignatureVoided(ORDER_ID);
        assertTrue(result.isSuccessful());
    }

    @Test
    void processWithInvalidEventTypeShouldReturnFailure() {
        when(queuedTask.getTaskData()).thenReturn(ORDER_ID);
        when(queuedTask.getTaskMetadata()).thenReturn(Map.of(EVENT_TYPE_TASK_METADATA_KEY, EventType.INVOICE_POSTED.name()));

        TaskResult result = taskProcessor.process(queuedTask);

        assertFalse(result.isSuccessful());
        assertEquals("Invalid event type", result.getFailureReason().orElse(null));
    }

    @Test
    void processWithMissingMetadataShouldReturnFailure() {
        when(queuedTask.getTaskData()).thenReturn(ORDER_ID);
        when(queuedTask.getTaskMetadata()).thenReturn(Map.of());

        TaskResult result = taskProcessor.process(queuedTask);

        assertFalse(result.isSuccessful());
        assertTrue(result.getFailureReason().isPresent());
    }

    @Test
    void processWithServiceExceptionShouldReturnFailure() {
        when(queuedTask.getTaskData()).thenReturn(ORDER_ID);
        when(queuedTask.getTaskMetadata()).thenReturn(Map.of(EVENT_TYPE_TASK_METADATA_KEY, EventType.ORDER_APPROVED.name()));
        doThrow(new RuntimeException("Service error")).when(intelligentSalesRoomService).handleOrderApproved(ORDER_ID);

        TaskResult result = taskProcessor.process(queuedTask);

        assertFalse(result.isSuccessful());
        assertEquals("Service error", result.getFailureReason().orElse(null));
    }

    @Test
    void getConfigurationShouldReturnCorrectConfiguration() {
        ProcessorConfiguration config = taskProcessor.getConfiguration();

        assertEquals(TASK_MODULE, config.getTaskModule());
        assertEquals(TIMEOUT, config.getTaskTimeout());
    }

    private OrderJson createSampleOrderJson() {
        OrderJson order = new OrderJson();
        order.setId(ORDER_ID);
        order.setEntityId(ENTITY_ID);
        return order;
    }
}
