package com.subskribe.billy.tenant.fixtures;

import com.subskribe.billy.TestConstants;
import com.subskribe.billy.shared.enums.PercentDerivedFrom;
import com.subskribe.billy.shared.enums.SigningOrder;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.model.TenantSettingSeal;
import java.util.TimeZone;
import java.util.UUID;
import org.elasticsearch.common.collect.List;

public class TenantSettingFixture {

    public static TenantSetting getDefault() {
        return new TenantSetting(
            UUID.randomUUID(),
            TestConstants.TIME_ZONE,
            null,
            List.of(TestConstants.CURRENCY),
            PercentDerivedFrom.LIST_AMOUNT,
            TenantSettingSeal.ON,
            null,
            SigningOrder.ACCOUNT_FIRST,
            null,
            false,
            false,
            false,
            false,
            null
        );
    }

    public static TenantSetting withTimeZone(String timeZone) {
        return new TenantSetting(
            UUID.randomUUID(),
            TimeZone.getTimeZone(timeZone),
            null,
            List.of(TestConstants.CURRENCY),
            PercentDerivedFrom.LIST_AMOUNT,
            TenantSettingSeal.ON,
            null,
            SigningOrder.ACCOUNT_FIRST,
            null,
            false,
            false,
            false,
            false,
            null
        );
    }
}
