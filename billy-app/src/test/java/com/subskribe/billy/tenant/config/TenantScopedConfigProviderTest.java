package com.subskribe.billy.tenant.config;

import com.subskribe.billy.BillyConfiguration;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class TenantScopedConfigProviderTest {

    private static final String E2E_TENANT_ID = "7911efdc-331d-4c7f-95d4-152fadd06f5a";

    private static final String BEAMERY_SANDBOX_TENANT_ID = "83f21b24-5599-4ec8-a609-9016fe0d791b";

    private static final String MONOGRAPH_PROD_TENANT_ID = "3287940f-31dc-4352-a5ed-5e2f022b7b5e";

    private static final String DEV2_TEST_TENANT_ID = "76654b07-0717-446e-8319-ddfa229dabac";

    private static final String CORDANCE_SECOND_SANDBOX_TENANT = "da58cb72-76f0-4f0d-b239-6048922a4978";

    private static final String CORDANCE_PRODUCTION_TENANT = "62f48df6-c8ac-42ee-b0a7-d6322fd9a92b";

    private static final String VIVI_SANDBOX_TENANT = "1e3bb8a3-78e7-4354-a010-8f8701898686";

    private static final String VIVI_PRODUCTION_TENANT = "5678b2ef-dd12-402e-9f0b-a5e5c57b8a26";

    private static final String EDUCATION_PERFECT_STAGING_TENANT = "b070f44b-c993-43d3-b2c6-b58f33746790";

    @Test
    public void e2eTenantConfigOverride() {
        BillyConfiguration e2eConfig = TenantScopedConfigProvider.provideCombined(new BillyConfiguration(), E2E_TENANT_ID);

        Assertions.assertThat(e2eConfig.getDocumentConfiguration().getOrderFormTemplateFileName()).isEqualTo("e2eQuote.mustache");
        Assertions.assertThat(e2eConfig.getDocumentConfiguration().getOrderFormTemplateCssFileName()).isEqualTo("e2eQuote.css");
    }

    @Test
    public void beamerySandboxConfigOverride() {
        BillyConfiguration beameryConfig = TenantScopedConfigProvider.provideCombined(new BillyConfiguration(), BEAMERY_SANDBOX_TENANT_ID);

        Assertions.assertThat(beameryConfig.getSalesforceConfiguration().getShouldSyncProductCategory()).isTrue();
    }

    @Test
    public void monographProdConfigOverride() {
        BillyConfiguration monographConfig = TenantScopedConfigProvider.provideCombined(new BillyConfiguration(), MONOGRAPH_PROD_TENANT_ID);

        Assertions.assertThat(monographConfig.getCustomPaymentTypeConfiguration().getIncludeInvoicePaymentType()).isTrue();
    }

    @Test
    public void testDev2TenantConfigOverride() {
        BillyConfiguration dev2TestConfig = TenantScopedConfigProvider.provideCombined(new BillyConfiguration(), DEV2_TEST_TENANT_ID);

        Assertions.assertThat(dev2TestConfig.getCrmConfiguration().getSyncToCrm()).isFalse();
    }

    @Test
    public void testCordanceSecondSandboxTenant() {
        BillyConfiguration cordanceSandboxConfig = TenantScopedConfigProvider.provideCombined(
            new BillyConfiguration(),
            CORDANCE_SECOND_SANDBOX_TENANT
        );

        Assertions.assertThat(cordanceSandboxConfig.getCrmConfiguration().getSyncToCrm()).isFalse();
    }

    @Test
    public void testCordanceProductionTenant() {
        BillyConfiguration cordanceProductionConfig = TenantScopedConfigProvider.provideCombined(
            new BillyConfiguration(),
            CORDANCE_PRODUCTION_TENANT
        );

        Assertions.assertThat(cordanceProductionConfig.getCrmConfiguration().getSyncToCrm()).isFalse();
        Assertions.assertThat(cordanceProductionConfig.getInvoiceEmailConfiguration().isEnabled()).isFalse();
    }

    @Test
    public void testViviSandboxTenant() {
        BillyConfiguration viviSandboxConfig = TenantScopedConfigProvider.provideCombined(new BillyConfiguration(), VIVI_SANDBOX_TENANT);

        Assertions.assertThat(viviSandboxConfig.getHubSpotConfiguration().getExcludeSubscriptionNote()).isTrue();
        Assertions.assertThat(viviSandboxConfig.getUiConfiguration().getOrderItemCustomFields()).contains("Class");
    }

    @Test
    public void testViviProdTenant() {
        BillyConfiguration viviSandboxConfig = TenantScopedConfigProvider.provideCombined(new BillyConfiguration(), VIVI_PRODUCTION_TENANT);

        Assertions.assertThat(viviSandboxConfig.getHubSpotConfiguration().getExcludeSubscriptionNote()).isTrue();
        Assertions.assertThat(viviSandboxConfig.getUiConfiguration().getOrderItemCustomFields()).contains("Class");
    }

    @Test
    public void testEducationPerfectStagingTenant() {
        BillyConfiguration educationPerfectConfig = TenantScopedConfigProvider.provideCombined(
            new BillyConfiguration(),
            EDUCATION_PERFECT_STAGING_TENANT
        );

        Assertions.assertThat(educationPerfectConfig.getUiConfiguration().getOrderItemCustomFields()).contains("years");
    }
}
