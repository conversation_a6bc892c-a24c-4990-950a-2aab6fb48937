package com.subskribe.billy.tenant.services;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.auth.apikey.ApiKeyService;
import com.subskribe.billy.auth.services.TenantCognitoService;
import com.subskribe.billy.auth.services.TenantRlsEncryptionService;
import com.subskribe.billy.aws.cognito.CognitoService;
import com.subskribe.billy.entity.model.Entity;
import com.subskribe.billy.entity.model.ImmutableEntity;
import com.subskribe.billy.entity.service.EntityService;
import com.subskribe.billy.hubspot.service.HubSpotIntegrationService;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoicesettlement.services.TenantCreditMemoConfigurationService;
import com.subskribe.billy.payment.services.PaymentConfigurationService;
import com.subskribe.billy.paymentterms.services.PaymentTermSettingsService;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.salesforce.service.SalesforceIntegrationService;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.enums.PercentDerivedFrom;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.db.TenantDAO;
import com.subskribe.billy.tenant.model.Tenant;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.model.TenantSettingSeal;
import com.subskribe.billy.tenantjob.model.ImmutableTenantJob;
import com.subskribe.billy.tenantjob.model.TenantJob;
import com.subskribe.billy.tenantjob.model.TenantJobModule;
import com.subskribe.billy.tenantjob.model.TenantJobObjectModel;
import com.subskribe.billy.tenantjob.model.TenantJobType;
import com.subskribe.billy.tenantjob.service.TenantJobDispatcherService;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class TenantServiceTest {

    @Mock
    private TenantDAO tenantDAO;

    @Mock
    private CognitoService cognitoService;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private SalesforceIntegrationService salesforceIntegrationService;

    @Mock
    private TenantCognitoService tenantCognitoService;

    @Mock
    private ApiKeyService apiKeyService;

    @Mock
    private TenantRlsEncryptionService rlsEncryptionService;

    @Mock
    private TenantCreditMemoConfigurationService tenantCreditMemoConfigurationService;

    @Mock
    private TenantSettingService tenantSettingService;

    @Mock
    private EntityService entityService;

    @Mock
    private PaymentConfigurationService paymentConfigurationService;

    @Mock
    private HubSpotIntegrationService hubSpotIntegrationService;

    @Mock
    private PaymentTermSettingsService paymentTermSettingsService;

    @Mock
    private PlatformFeatureService platformFeatureService;

    @Mock
    private TenantJobDispatcherService tenantJobDispatcherService;

    private TenantService tenantService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        tenantService = new TenantService(
            tenantDAO,
            cognitoService,
            tenantIdProvider,
            salesforceIntegrationService,
            tenantCognitoService,
            apiKeyService,
            rlsEncryptionService,
            tenantCreditMemoConfigurationService,
            tenantSettingService,
            entityService,
            paymentConfigurationService,
            hubSpotIntegrationService,
            paymentTermSettingsService,
            platformFeatureService,
            tenantJobDispatcherService
        );
    }

    @Test
    public void testDefaultPlatformFeaturesAreEnabled() {
        Tenant tenant = createTenant();
        String tenantId = AutoGenerate.getNewId();
        setupMocks(tenantId, tenant);

        tenantService.createTenant(tenant);

        verify(platformFeatureService, times(1)).enableSystemPlatformFeatureForTenant(PlatformFeature.BILLING, tenant.getTenantId());
    }

    @Test
    public void testTaskDispatched() {
        Tenant tenant = createTenant();
        String tenantId = AutoGenerate.getNewId();
        setupMocks(tenantId, tenant);

        tenantService.createTenant(tenant);

        TenantJob expectedJob = ImmutableTenantJob.builder()
            .module(TenantJobModule.TENANT_MANAGEMENT)
            .jobType(TenantJobType.TENANT_CREATED)
            .objectModel(TenantJobObjectModel.TENANT)
            .objectId(tenantId)
            .partitionKey(tenantId)
            .build();
        verify(tenantJobDispatcherService, times(1)).dispatch(expectedJob, Optional.of(tenantId));
    }

    private void setupMocks(String tenantId, Tenant tenant) {
        when(tenantIdProvider.generateTenantId()).thenReturn(tenantId);
        when(tenantDAO.addTenant(any(), any(), any())).thenReturn(tenant);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(tenantId);
        when(entityService.createDefaultEntityForNewTenant(any())).thenReturn(createEntity());
    }

    private static Tenant createTenant() {
        Tenant tenant = new Tenant(null, null, "TEST TENANT", "<EMAIL>", "+**********", null, false, false, null, null);

        TenantSetting tenantSetting = new TenantSetting(
            null,
            TimeZone.getTimeZone("US/Pacific"),
            null,
            List.of("USD", "EUR"),
            PercentDerivedFrom.SELL_AMOUNT,
            TenantSettingSeal.OFF,
            null,
            null,
            null,
            false,
            false,
            false,
            false,
            null
        );
        tenant.setTenantSetting(tenantSetting);

        return tenant;
    }

    private static Entity createEntity() {
        return ImmutableEntity.builder()
            .entityId("ENT-123")
            .name("entity")
            .functionalCurrency("USD")
            .prorationMode(ProrationConfig.Mode.NORMALIZED)
            .prorationScheme(ProrationConfig.Scheme.CALENDAR_DAYS)
            .invoiceConfig(Optional.empty())
            .build();
    }
}
