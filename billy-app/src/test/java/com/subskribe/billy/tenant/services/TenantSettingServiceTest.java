package com.subskribe.billy.tenant.services;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.account.services.AccountGetService;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.foreignexchange.service.CurrencyConversionRateGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.PercentDerivedFrom;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.db.TenantSettingDAO;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.model.TenantSettingSeal;
import com.subskribe.billy.tenant.model.TenantUiCustomization;
import com.subskribe.billy.validation.Validator;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import org.apache.commons.io.IOUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class TenantSettingServiceTest {

    private static final String TEST_TENANT_ID = "TENANT ID";
    private static final String CUSTOMIZATION_CONFIG_BASE_PATH = "com/subskribe/billy/ui/";

    @Mock
    private TenantSettingDAO tenantSettingDAO;

    @Mock
    private AccountGetService accountGetService;

    @Mock
    ProductCatalogGetService productCatalogGetService;

    @Mock
    TenantIdProvider tenantIdProvider;

    @Mock
    CacheService cacheService;

    @Mock
    BillyConfiguration billyConfiguration;

    @Mock
    CurrencyConversionRateGetService currencyConversionRateGetService;

    @Mock
    FeatureService featureService;

    private TenantSettingService tenantSettingService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        Mockito.when(tenantSettingDAO.getTenantSetting()).thenReturn(Optional.empty());
        tenantSettingService = new TenantSettingService(
            tenantSettingDAO,
            accountGetService,
            productCatalogGetService,
            tenantIdProvider,
            cacheService,
            currencyConversionRateGetService,
            billyConfiguration,
            featureService
        );
    }

    /*
   before changing the assertions on the default tenant setting for the test to pass
   make sure to confirm why these default settings are in place, changing the default
   may have serious implications for the on-boarding behaviour of a tenant expected or otherwise
                                                         c=====e
                                                            H
   ____________                                         _,,_H__
  (__((__((___()                                       //|     |
 (__((__((___()()_____________________________________// |     |
(__((__((___()()()------------------------------------'  |_____|
    */
    @Test
    public void whenDefaultSettingsAreReturnedTheyMatchExpectations() {
        TenantSetting tenantSetting = tenantSettingService.getTenantSettingFromDB();
        Assertions.assertThat(tenantSetting).isNotNull();
        Assertions.assertThat(tenantSetting.getDefaultTimeZone()).isEqualTo(TimeZone.getTimeZone("US/Pacific"));
        Assertions.assertThat(tenantSetting.getSupportedCurrencies()).isEqualTo(List.of(SupportedCurrency.getDefaultCurrency().getCurrencyCode()));
        Assertions.assertThat(tenantSetting.getPercentDerivedFrom()).isEqualTo(PercentDerivedFrom.LIST_AMOUNT);
        Assertions.assertThat(tenantSetting.getTenantSettingSeal()).isEqualTo(TenantSettingSeal.OFF);
    }

    @Test
    public void whenUnsealCalledOutsideOfLocalCIThenServiceErrorsCorrectly() {
        Mockito.when(billyConfiguration.isLocalOrCi()).thenReturn(false);
        Assertions.assertThatThrownBy(() -> tenantSettingService.updateTenantSettingSealForTest(TenantSettingSeal.OFF, TEST_TENANT_ID))
            .isInstanceOf(UnsupportedOperationException.class)
            .hasMessageContaining("not allowed");
    }

    @Test
    public void whenSealCalledOutsideOfLocalCIThenServiceErrorsCorrectly() {
        Mockito.when(billyConfiguration.isLocalOrCi()).thenReturn(false);
        Assertions.assertThatThrownBy(() -> tenantSettingService.updateTenantSettingSealForTest(TenantSettingSeal.ON, TEST_TENANT_ID))
            .isInstanceOf(UnsupportedOperationException.class)
            .hasMessageContaining("not allowed");
    }

    @Test
    public void testTenantUiCustomizationConfigurationValidations() throws IOException {
        var objectMapper = JacksonProvider.defaultMapper();
        ClassLoader classloader = Thread.currentThread().getContextClassLoader();

        String validFileName = CUSTOMIZATION_CONFIG_BASE_PATH + "ui-customization.json";
        try (InputStream sourceStream = classloader.getResourceAsStream(validFileName)) {
            Validator.checkNonNullInternal(sourceStream, "Unable to find the resourceFile: " + validFileName);
            String inputJson = IOUtils.toString(sourceStream, StandardCharsets.UTF_8);
            TenantUiCustomization tenantUiCustomization = objectMapper.readValue(inputJson, TenantUiCustomization.class);
            Assertions.assertThat(tenantUiCustomization).isNotNull();
            Assertions.assertThat(tenantUiCustomization.customizations().get(0).configType()).isEqualTo(
                TenantUiCustomization.Customization.ConfigType.state
            );
            Assertions.assertThat(tenantUiCustomization.version()).isEqualTo(TenantUiCustomization.Version.V1_TENANT_LEVEL);
        }

        String invalidFileName1 = CUSTOMIZATION_CONFIG_BASE_PATH + "ui-customization-invalid1.json";
        try (InputStream sourceStream = classloader.getResourceAsStream(invalidFileName1)) {
            Validator.checkNonNullInternal(sourceStream, "Unable to find the resourceFile: " + invalidFileName1);
            String inputJson = IOUtils.toString(sourceStream, StandardCharsets.UTF_8);
            TenantUiCustomization tenantUiCustomization = objectMapper.readValue(inputJson, TenantUiCustomization.class);
            Assertions.assertThatThrownBy(() -> tenantSettingService.validateConfig(tenantUiCustomization))
                .isInstanceOf(InvalidInputException.class)
                .hasMessageContaining("Hidden and required can't be both true");
        }

        String invalidFileName2 = CUSTOMIZATION_CONFIG_BASE_PATH + "ui-customization-invalid2.json";
        try (InputStream sourceStream = classloader.getResourceAsStream(invalidFileName2)) {
            Validator.checkNonNullInternal(sourceStream, "Unable to find the resourceFile: " + invalidFileName2);
            String inputJson = IOUtils.toString(sourceStream, StandardCharsets.UTF_8);
            TenantUiCustomization tenantUiCustomization = objectMapper.readValue(inputJson, TenantUiCustomization.class);
            Assertions.assertThatThrownBy(() -> tenantSettingService.validateConfig(tenantUiCustomization))
                .isInstanceOf(InvalidInputException.class)
                .hasMessageContaining("Backend check should be empty for ui field required=false");
        }

        // This test is originally handled by @Valid annotation in http request path
        String invalidFileName3 = CUSTOMIZATION_CONFIG_BASE_PATH + "ui-customization-invalid3.json";
        try (InputStream sourceStream = classloader.getResourceAsStream(invalidFileName3)) {
            Validator.checkNonNullInternal(sourceStream, "Unable to find the resourceFile: " + invalidFileName3);
            String inputJson = IOUtils.toString(sourceStream, StandardCharsets.UTF_8);
            TenantUiCustomization tenantUiCustomization = objectMapper.readValue(inputJson, TenantUiCustomization.class);
            Assertions.assertThatThrownBy(() -> tenantSettingService.validateConfig(tenantUiCustomization))
                .isInstanceOf(InvalidInputException.class)
                .hasMessageContaining("Backend conditions can't be null");
        }

        String invalidFileName4 = CUSTOMIZATION_CONFIG_BASE_PATH + "ui-customization-invalid4.json";
        try (InputStream sourceStream = classloader.getResourceAsStream(invalidFileName4)) {
            Validator.checkNonNullInternal(sourceStream, "Unable to find the resourceFile: " + invalidFileName4);
            String inputJson = IOUtils.toString(sourceStream, StandardCharsets.UTF_8);
            TenantUiCustomization tenantUiCustomization = objectMapper.readValue(inputJson, TenantUiCustomization.class);
            Assertions.assertThatThrownBy(() -> tenantSettingService.validateConfig(tenantUiCustomization))
                .isInstanceOf(InvalidInputException.class)
                .hasMessageContaining("Backend can't be null if ui field is either hidden or required");
        }
    }

    @Test
    public void testUpdateDocxSettings_ValidParameters() {
        // Given
        Boolean isDocxAdminOnly = true;
        Boolean isDocxPasswordProtected = false;
        Mockito.when(tenantIdProvider.provideTenantIdString()).thenReturn(TEST_TENANT_ID);

        // When
        tenantSettingService.updateDocxSettings(isDocxAdminOnly, isDocxPasswordProtected);

        // Then
        Mockito.verify(tenantSettingDAO).updateDocxSettings(isDocxAdminOnly, isDocxPasswordProtected, TEST_TENANT_ID);
        Mockito.verify(cacheService).invalidateKey(TEST_TENANT_ID, CacheType.TENANT_SETTING, TEST_TENANT_ID);
    }

    @Test
    public void testUpdateDocxSettings_NullIsDocxAdminOnly_ThrowsException() {
        // Given
        Boolean isDocxAdminOnly = null;
        Boolean isDocxPasswordProtected = true;

        // When & Then
        Assertions.assertThatThrownBy(() -> tenantSettingService.updateDocxSettings(isDocxAdminOnly, isDocxPasswordProtected))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("isDocxAdminOnly");
    }

    @Test
    public void testUpdateDocxSettings_NullIsDocxPasswordProtected_ThrowsException() {
        // Given
        Boolean isDocxAdminOnly = true;
        Boolean isDocxPasswordProtected = null;

        // When & Then
        Assertions.assertThatThrownBy(() -> tenantSettingService.updateDocxSettings(isDocxAdminOnly, isDocxPasswordProtected))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("isDocxPasswordProtected");
    }

    @Test
    public void testUpdateDocxSettings_BothParametersNull_ThrowsException() {
        // Given
        Boolean isDocxAdminOnly = null;
        Boolean isDocxPasswordProtected = null;

        // When & Then
        Assertions.assertThatThrownBy(() -> tenantSettingService.updateDocxSettings(isDocxAdminOnly, isDocxPasswordProtected))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("isDocxAdminOnly");
    }

    @Test
    public void testUpdateDocxSettings_BothParametersFalse() {
        // Given
        Boolean isDocxAdminOnly = false;
        Boolean isDocxPasswordProtected = false;
        Mockito.when(tenantIdProvider.provideTenantIdString()).thenReturn(TEST_TENANT_ID);

        // When
        tenantSettingService.updateDocxSettings(isDocxAdminOnly, isDocxPasswordProtected);

        // Then
        Mockito.verify(tenantSettingDAO).updateDocxSettings(isDocxAdminOnly, isDocxPasswordProtected, TEST_TENANT_ID);
        Mockito.verify(cacheService).invalidateKey(TEST_TENANT_ID, CacheType.TENANT_SETTING, TEST_TENANT_ID);
    }

    @Test
    public void testUpdateDocxSettings_BothParametersTrue() {
        // Given
        Boolean isDocxAdminOnly = true;
        Boolean isDocxPasswordProtected = true;
        Mockito.when(tenantIdProvider.provideTenantIdString()).thenReturn(TEST_TENANT_ID);

        // When
        tenantSettingService.updateDocxSettings(isDocxAdminOnly, isDocxPasswordProtected);

        // Then
        Mockito.verify(tenantSettingDAO).updateDocxSettings(isDocxAdminOnly, isDocxPasswordProtected, TEST_TENANT_ID);
        Mockito.verify(cacheService).invalidateKey(TEST_TENANT_ID, CacheType.TENANT_SETTING, TEST_TENANT_ID);
    }
}
