package com.subskribe.billy.tenant.fixtures;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantSettingService;

public class TenantSettingServiceFixture {

    public static TenantSettingService getDefault() {
        TenantSetting tenantSetting = TenantSettingFixture.getDefault();
        return get(tenantSetting);
    }

    public static TenantSettingService withTimeZone(String timeZone) {
        TenantSetting tenantSetting = TenantSettingFixture.withTimeZone(timeZone);
        return get(tenantSetting);
    }

    public static TenantSettingService get(TenantSetting tenantSetting) {
        TenantSettingService tenantSettingService = mock(TenantSettingService.class);
        when(tenantSettingService.getTenantSetting()).thenReturn(tenantSetting);
        when(tenantSettingService.getTenantSettingInternal()).thenReturn(tenantSetting);
        when(tenantSettingService.getTenantSettingInternal(any(String.class))).thenReturn(tenantSetting);
        return tenantSettingService;
    }
}
