package com.subskribe.billy.tenant.fixtures;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.tenant.TenantIdProvider;

public class TenantIdProviderFixture {

    public static TenantIdProvider getDefault() {
        return withTenantId(TenantFixture.TENANT_1_ID);
    }

    public static TenantIdProvider withTenantId(String tenantId) {
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(tenantId);
        return tenantIdProvider;
    }
}
