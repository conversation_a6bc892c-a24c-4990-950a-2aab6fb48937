package com.subskribe.billy.cron;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.subskribe.billy.exception.InvalidInputException;
import org.junit.jupiter.api.Test;

class CronExpressionValidatorTest {

    @Test
    void shouldRejectEmptyCronExpression() {
        assertThrows(InvalidInputException.class, () -> CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(""));
    }

    @Test
    void shouldRejectNullCronExpression() {
        assertThrows(InvalidInputException.class, () -> CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(null));
    }

    @Test
    void shouldThrowExceptionForInvalidCronExpression() {
        String invalidCron = "not a valid cron";

        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(invalidCron)
        );

        assertTrue(exception.getMessage().contains("Failed to parse cron expression"));
    }

    @Test
    void shouldThrowExceptionForCronWithInvalidQuestionMarkUsage() {
        // Using ? in both day-of-month and day-of-week positions is invalid in Quartz
        String cronWithInvalidQuestionMark = "0 0 9 ? * ?";

        InvalidInputException exception = assertThrows(InvalidInputException.class, () ->
            CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronWithInvalidQuestionMark)
        );

        assertTrue(exception.getMessage().contains("Failed to parse cron expression"));
    }

    @Test
    void testEveryMinute() {
        // Every minute - Quartz format
        String cronExpression = "0 * * * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertTrue(result, "Every minute should be more frequent than 12 hours");
    }

    @Test
    void testEveryFiveMinutes() {
        // Every 5 minutes - Quartz format
        String cronExpression = "0 */5 * * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertTrue(result, "Every 5 minutes should be more frequent than 12 hours");
    }

    @Test
    void testEveryHour() {
        // Every hour - Quartz format
        String cronExpression = "0 0 * * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertTrue(result, "Every hour should be more frequent than 12 hours");
    }

    @Test
    void testEveryTwoHours() {
        // Every 2 hours - Quartz format
        String cronExpression = "0 0 */2 * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertTrue(result, "Every 2 hours should be more frequent than 12 hours");
    }

    @Test
    void testEverySixHours() {
        // Every 6 hours - Quartz format
        String cronExpression = "0 0 */6 * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertTrue(result, "Every 6 hours should be more frequent than 12 hours");
    }

    @Test
    void testEveryTwelveHours() {
        // Every 12 hours - Quartz format
        String cronExpression = "0 0 */12 * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertFalse(result, "Every 12 hours should NOT be more frequent than 12 hours");
    }

    @Test
    void testDailyAtMidnight() {
        // Daily at midnight - Quartz format
        String cronExpression = "0 0 0 * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertFalse(result, "Daily at midnight should NOT be more frequent than 12 hours");
    }

    @Test
    void testDailyAt9am() {
        // Daily at 9am - Quartz format
        String cronExpression = "0 0 9 * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertFalse(result, "Daily at 9am should NOT be more frequent than 12 hours");
    }

    @Test
    void testWeekly() {
        // Weekly on Monday at midnight - Quartz format
        String cronExpression = "0 0 0 ? * MON";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertFalse(result, "Weekly on Monday should NOT be more frequent than 12 hours");
    }

    @Test
    void testRealSubskribeCron1() {
        // Daily at 9am with specific format - Quartz format
        String cronExpression = "0 0 9 * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertFalse(result, "Daily at 9am should NOT be more frequent than 12 hours");
    }

    @Test
    void testRealSubskribeCron2() {
        // Daily at 1am with specific format - Quartz format
        String cronExpression = "0 0 1 * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertFalse(result, "Daily at 1am should NOT be more frequent than 12 hours");
    }

    @Test
    void testRealSubskribeCron3() {
        // Daily at 6:15am with specific format - Quartz format
        String cronExpression = "0 15 6 * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertFalse(result, "Daily at 6:15am should NOT be more frequent than 12 hours");
    }

    @Test
    void testBadSubskribeCron4() {
        // Every 5 minutes with specific format - Quartz format
        String cronExpression = "0 */5 * * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertTrue(result, "Every 5 minutes should be more frequent than 12 hours");
    }

    @Test
    void testExactlyOnBoundaryCase() {
        // Exactly 12 hours (boundary case) - Quartz format
        String cronExpression = "0 0 0,12 * * ?";
        boolean result = CronExpressionValidator.isMoreFrequentThanConfiguredMaxHours(cronExpression);
        assertFalse(result, "Exactly 12 hours should NOT be more frequent than 12 hours");
    }
}
