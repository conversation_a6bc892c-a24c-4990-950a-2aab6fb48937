package com.subskribe.billy.invoicesettlement.services;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.AccountPaymentMethod;
import com.subskribe.billy.account.services.AccountPaymentMethodGetService;
import com.subskribe.billy.account.services.AccountPaymentMethodService;
import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.dlq.service.DLQService;
import com.subskribe.billy.entity.EntityContextProvider;
import com.subskribe.billy.entity.fixtures.EntityContextProviderFixture;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.ConflictingStateException;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.foreignexchange.service.RealizedGainLossService;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoice.model.InvoiceStatus;
import com.subskribe.billy.invoice.service.InvoiceEventService;
import com.subskribe.billy.invoice.service.InvoiceRetrievalService;
import com.subskribe.billy.invoicesettlement.db.InvoiceSettlementDAO;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.model.CreditMemoBalance;
import com.subskribe.billy.invoicesettlement.model.CreditMemoStatus;
import com.subskribe.billy.invoicesettlement.model.InvoiceBalance;
import com.subskribe.billy.invoicesettlement.model.PaymentBalance;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationStatus;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationType;
import com.subskribe.billy.payment.model.ImmutablePaymentMetadata;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.payment.model.PaymentAttempt;
import com.subskribe.billy.payment.model.PaymentAttemptState;
import com.subskribe.billy.payment.model.PaymentBankAccount;
import com.subskribe.billy.payment.model.PaymentLifecycleType;
import com.subskribe.billy.payment.model.PaymentMetadata;
import com.subskribe.billy.payment.services.PaymentBankAccountService;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.payment.services.PaymentService;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.scheduler.service.QuartzSchedulerService;
import com.subskribe.billy.shared.Constants;
import com.subskribe.billy.shared.enums.PaymentBankAccountStatus;
import com.subskribe.billy.shared.enums.PaymentState;
import com.subskribe.billy.shared.enums.PaymentType;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Currency;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.TransactionalCallable;
import org.jooq.impl.DefaultConfiguration;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

public class InvoiceSettlementServiceTest {

    private static final String ACCOUNT_ID = UUID.randomUUID().toString();
    private static final Invoice.Number INVOICE_NUMBER = new Invoice.Number("INV-123");
    private static final String PAYMENT_ID = "PAY-********";
    private static final String CREDIT_MEMO_NUMBER = "CM-123";
    private static final String TENANT_ID = "Tenant-123";
    private static final UUID NEGATED_SETTLEMENT_ID = UUID.randomUUID();
    private static final String PAYMENT_BANK_ACCOUNT_ID_USD = "BANK-1234567";
    private static final String PAYMENT_BANK_ACCOUNT_ID_EUR = "BANK-7654321";
    private static final String ENTITY_ID_EUR = "ENT-EUR-7654321";

    private final RealizedGainLossService realizedGainLossService = mock(RealizedGainLossService.class);

    private InvoiceSettlementDAO mockInvoiceSettlementDao;
    private InvoiceRetrievalService mockInvoiceRetrievalService;
    private PaymentService mockPaymentService;
    private CreditMemoRetrievalService mockCreditMemoRetrievalService;
    private EventPublishingService mockEventPublishingService;
    private DSLContext mockDslContext;
    private AccountingPeriodService mockAccountingPeriodService;
    private InvoiceSettlementService invoiceSettlementService;
    private Payment payment;
    private Configuration mockConfiguration;
    private FeatureService mockFeatureService;
    private PaymentGetService mockPaymentGetService;

    private final ArgumentCaptor<TransactionalCallable> jooqTransactionCaptor = ArgumentCaptor.forClass(TransactionalCallable.class);

    @BeforeEach
    public void setup() {
        mockEventPublishingService = mock(EventPublishingService.class);
        mockAccountingPeriodService = mock(AccountingPeriodService.class);
        mockDslContext = mock(DSLContext.class);
        mockInvoiceSettlementDao = mock(InvoiceSettlementDAO.class);
        mockInvoiceRetrievalService = mock(InvoiceRetrievalService.class);
        mockPaymentService = mock(PaymentService.class);
        mockCreditMemoRetrievalService = mock(CreditMemoRetrievalService.class);
        mockConfiguration = new DefaultConfiguration();
        mockFeatureService = mock(FeatureService.class);
        mockPaymentGetService = mock(PaymentGetService.class);
        PaymentBankAccountService mockPaymentBankAccountService = mock(PaymentBankAccountService.class);
        EntityGetService mockEntityGetService = mock(EntityGetService.class);
        EntityContextProvider mockEntityContextProvider = EntityContextProviderFixture.buildSingleEntityContext();
        TenantSettingService mockTenantSettingService = TenantSettingServiceFixture.getDefault();
        DSLContextProvider mockDslContextProvider = mock(DSLContextProvider.class);
        AccountPaymentMethodService mockAccountPaymentMethodService = mock(AccountPaymentMethodService.class);
        AccountPaymentMethodGetService mockAccountPaymentMethodGetService = mock(AccountPaymentMethodGetService.class);
        CreditMemoService mockCreditMemoService = mock(CreditMemoService.class);
        TenantIdProvider mockTenantIdProvider = mock(TenantIdProvider.class);
        RefundService mockRefundService = mock(RefundService.class);
        DLQService mockDlqService = mock(DLQService.class);
        QuartzSchedulerService mockQuartzSchedulerService = mock(QuartzSchedulerService.class);
        PlatformFeatureService mockPlatformFeatureService = mock(PlatformFeatureService.class);
        InvoiceEventService invoiceEventService = mock(InvoiceEventService.class);

        when(mockDslContextProvider.get(any())).thenReturn(mockDslContext);
        when(mockDslContext.configuration()).thenReturn(mockConfiguration);

        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        AccountPaymentMethod accountPaymentMethod = mock(AccountPaymentMethod.class);
        when(accountPaymentMethod.getId()).thenReturn(UUID.randomUUID());
        when(mockAccountPaymentMethodService.addAccountPaymentMethod(any(AccountPaymentMethod.class))).thenReturn(accountPaymentMethod);

        PaymentBankAccount paymentBankAccountEur = mock(PaymentBankAccount.class);
        when(paymentBankAccountEur.getCurrencyCode()).thenReturn(Currency.getInstance("EUR"));
        when(paymentBankAccountEur.getEntityIds()).thenReturn(Set.of(EntityFixture.getEntityFixture().getEntityId()));
        PaymentBankAccount paymentBankAccountUsd = mock(PaymentBankAccount.class);
        when(paymentBankAccountUsd.getCurrencyCode()).thenReturn(Currency.getInstance("USD"));
        when(paymentBankAccountUsd.getEntityIds()).thenReturn(Set.of(EntityFixture.getEntityFixture().getEntityId()));
        when(mockPaymentBankAccountService.getByPaymentBankAccountId(PAYMENT_BANK_ACCOUNT_ID_USD)).thenReturn(paymentBankAccountUsd);
        when(mockPaymentBankAccountService.getByPaymentBankAccountId(PAYMENT_BANK_ACCOUNT_ID_EUR)).thenReturn(paymentBankAccountEur);
        when(paymentBankAccountUsd.getStatus()).thenReturn(PaymentBankAccountStatus.ACTIVE);
        when(paymentBankAccountEur.getStatus()).thenReturn(PaymentBankAccountStatus.ACTIVE);
        when(paymentBankAccountUsd.canBeUsedForPayment()).thenReturn(true);
        when(paymentBankAccountEur.canBeUsedForPayment()).thenReturn(true);
        when(paymentBankAccountUsd.canBeDeleted()).thenReturn(false);
        when(paymentBankAccountEur.canBeDeleted()).thenReturn(false);
        when(mockEntityGetService.getEntityById(ENTITY_ID_EUR)).thenReturn(EntityFixture.getEntityFixtureEur());
        when(mockEntityGetService.getEntityById(EntityFixture.ENTITY_1_ID)).thenReturn(EntityFixture.getEntityFixture());
        when(mockFeatureService.isEnabled(Feature.PAYMENT_WITH_BANK_ACCOUNT)).thenReturn(true);

        invoiceSettlementService = new InvoiceSettlementService(
            mockInvoiceSettlementDao,
            mockInvoiceRetrievalService,
            mockPaymentService,
            mockCreditMemoRetrievalService,
            mockCreditMemoService,
            mockTenantIdProvider,
            mockDslContextProvider,
            mockEventPublishingService,
            mockAccountPaymentMethodGetService,
            mockAccountPaymentMethodService,
            mockTenantSettingService,
            mockQuartzSchedulerService,
            mockDlqService,
            mockAccountingPeriodService,
            mockRefundService,
            mockPaymentGetService,
            realizedGainLossService,
            mockFeatureService,
            mockEntityContextProvider,
            mockPaymentBankAccountService,
            mockEntityGetService,
            mockPlatformFeatureService,
            invoiceEventService
        );

        payment = new Payment();
        payment.setPaymentId(PAYMENT_ID);
        payment.setAmount(BigDecimal.TEN);
        payment.setCustomerAccountId(ACCOUNT_ID);
    }

    @Test
    public void successfulPaymentApplication() {
        Instant now = Instant.now();
        SettlementApplication savedSettlementApplication = SettlementApplication.builder()
            .id(UUID.randomUUID())
            .entityId(EntityFixture.ENTITY_1_ID)
            .customerAccountId(ACCOUNT_ID)
            .invoiceNumber(INVOICE_NUMBER.getNumber())
            .paymentId(PAYMENT_ID)
            .applicationType(SettlementApplicationType.PAYMENT)
            .amount(BigDecimal.TEN)
            .note("note")
            .appliedOn(now)
            .createdOn(now)
            .status(SettlementApplicationStatus.APPLIED_PAYMENT)
            .build();

        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockInvoiceSettlementDao.getPaymentBalance(PAYMENT_ID)).thenReturn(Optional.of(getPaymentBalance(BigDecimal.TEN)));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));
        when(mockInvoiceSettlementDao.upsertSettlementApplication(any(), any(), any(), any())).thenReturn(savedSettlementApplication);
        when(mockPaymentService.addPayment(any(), any())).thenReturn(payment);

        invoiceSettlementService.addAndApplyManualPayment(
            INVOICE_NUMBER,
            BigDecimal.TEN,
            null,
            PaymentType.CHECK,
            Instant.now().getEpochSecond(),
            BigDecimal.TEN,
            "",
            BigDecimal.ZERO,
            PAYMENT_BANK_ACCOUNT_ID_USD
        );

        setupTransaction();

        verify(mockInvoiceSettlementDao).upsertSettlementApplication(any(), eq(TENANT_ID), any(), any());

        verify(mockEventPublishingService).publishEventInTransaction(
            any(Configuration.class),
            eq(EventType.PAYMENT_PROCESSED),
            any(),
            eq(EntityFixture.ENTITY_1_ID),
            eq(ACCOUNT_ID),
            any()
        );
    }

    @Test
    public void applyPaymentWithNoteTooLongShouldFail() {
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.ONE)));
        when(mockInvoiceSettlementDao.getPaymentBalance(PAYMENT_ID)).thenReturn(Optional.of(getPaymentBalance(BigDecimal.TEN)));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));

        assertThrows(IllegalArgumentException.class, () ->
            invoiceSettlementService.addAndApplyManualPayment(
                INVOICE_NUMBER,
                BigDecimal.ONE,
                null,
                PaymentType.CHECK,
                Instant.now().getEpochSecond(),
                BigDecimal.TEN,
                RandomStringUtils.randomAlphanumeric(Constants.DEFAULT_MAX_STRING_LENGTH + 1),
                BigDecimal.ZERO,
                PAYMENT_BANK_ACCOUNT_ID_USD
            )
        );
    }

    @Test
    public void applyPaymentWithBankFeeNegativeOrGreaterThanPaymentAmountShouldFail() {
        Instant accountingPeriodStart = Instant.now().minusSeconds(1000);

        // Make sure the invoice date is at least 1 whole day before accountingPeriodStart - 1 second
        Instant invoiceDate = accountingPeriodStart.plus(2, ChronoUnit.DAYS);
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.ONE)));
        when(mockInvoiceSettlementDao.getPaymentBalance(PAYMENT_ID)).thenReturn(Optional.of(getPaymentBalance(BigDecimal.TEN)));
        when(mockAccountingPeriodService.inClosedAccountingPeriod(any(), any())).thenReturn(true);

        assertThrows(InvalidInputException.class, () ->
            invoiceSettlementService.addAndApplyPayment(
                getInvoice(InvoiceStatus.POSTED, invoiceDate, null),
                BigDecimal.ONE,
                UUID.randomUUID(),
                PaymentState.SUCCEED,
                BigDecimal.TEN,
                accountingPeriodStart.minusSeconds(1),
                RandomStringUtils.randomAlphanumeric(Constants.DEFAULT_MAX_STRING_LENGTH),
                PaymentLifecycleType.OFFLINE,
                BigDecimal.valueOf(12),
                PAYMENT_BANK_ACCOUNT_ID_USD
            )
        );

        assertThrows(InvalidInputException.class, () ->
            invoiceSettlementService.addAndApplyPayment(
                getInvoice(InvoiceStatus.POSTED, invoiceDate, ENTITY_ID_EUR),
                BigDecimal.ONE,
                UUID.randomUUID(),
                PaymentState.SUCCEED,
                BigDecimal.TEN,
                accountingPeriodStart.minusSeconds(1),
                RandomStringUtils.randomAlphanumeric(Constants.DEFAULT_MAX_STRING_LENGTH),
                PaymentLifecycleType.OFFLINE,
                BigDecimal.valueOf(-12),
                PAYMENT_BANK_ACCOUNT_ID_USD
            )
        );
    }

    @Test
    public void applyPaymentWithDateEarlierThanCurrentAccountingPeriod() {
        Instant accountingPeriodStart = Instant.now().minusSeconds(1000);

        // Make sure the invoice date is at least 1 whole day before accountingPeriodStart - 1 second
        Instant invoiceDate = accountingPeriodStart.minus(2, ChronoUnit.DAYS);
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.ONE)));
        when(mockInvoiceSettlementDao.getPaymentBalance(PAYMENT_ID)).thenReturn(Optional.of(getPaymentBalance(BigDecimal.TEN)));
        when(mockAccountingPeriodService.inClosedAccountingPeriod(any(), any())).thenReturn(true);

        assertThrows(IllegalStateException.class, () ->
            invoiceSettlementService.addAndApplyPayment(
                getInvoice(InvoiceStatus.POSTED, invoiceDate, null),
                BigDecimal.ONE,
                UUID.randomUUID(),
                PaymentState.SUCCEED,
                BigDecimal.TEN,
                accountingPeriodStart.minusSeconds(1),
                RandomStringUtils.randomAlphanumeric(Constants.DEFAULT_MAX_STRING_LENGTH),
                PaymentLifecycleType.OFFLINE,
                BigDecimal.valueOf(5),
                PAYMENT_BANK_ACCOUNT_ID_USD
            )
        );
    }

    @Test
    public void insufficientInvoiceBalanceShouldFail() {
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.ONE)));
        when(mockInvoiceSettlementDao.getPaymentBalance(PAYMENT_ID)).thenReturn(Optional.of(getPaymentBalance(BigDecimal.TEN)));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));

        assertThrows(IllegalArgumentException.class, () ->
            invoiceSettlementService.addAndApplyManualPayment(
                INVOICE_NUMBER,
                BigDecimal.ONE,
                null,
                PaymentType.CHECK,
                Instant.now().getEpochSecond(),
                BigDecimal.TEN,
                "",
                BigDecimal.ZERO,
                PAYMENT_BANK_ACCOUNT_ID_USD
            )
        );
    }

    @Test
    public void paymentFromADifferentAccountShouldFail() {
        var paymentBalance = new PaymentBalance(UUID.randomUUID(), UUID.randomUUID().toString(), PAYMENT_ID, BigDecimal.TEN, null);
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockInvoiceSettlementDao.getPaymentBalance(PAYMENT_ID)).thenReturn(Optional.of(paymentBalance));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));

        payment.setCustomerAccountId(UUID.randomUUID().toString());
        Assertions.assertDoesNotThrow(() ->
            invoiceSettlementService.addAndApplyManualPayment(
                INVOICE_NUMBER,
                BigDecimal.TEN,
                null,
                PaymentType.CHECK,
                Instant.now().getEpochSecond(),
                BigDecimal.TEN,
                "",
                BigDecimal.ZERO,
                PAYMENT_BANK_ACCOUNT_ID_USD
            )
        );
        payment.setCustomerAccountId(ACCOUNT_ID);
    }

    @Test
    public void paymentFromABankAccountWithCurrencyNotEqualsToEitherInvoiceOrEntityFunctionalCurShouldFail() {
        var paymentBalance = new PaymentBalance(UUID.randomUUID(), UUID.randomUUID().toString(), PAYMENT_ID, BigDecimal.TEN, null);
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockInvoiceSettlementDao.getPaymentBalance(PAYMENT_ID)).thenReturn(Optional.of(paymentBalance));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));

        Assertions.assertDoesNotThrow(() ->
            invoiceSettlementService.addAndApplyManualPayment(
                INVOICE_NUMBER,
                BigDecimal.TEN,
                null,
                PaymentType.CHECK,
                Instant.now().getEpochSecond(),
                BigDecimal.TEN,
                "",
                BigDecimal.ZERO,
                PAYMENT_BANK_ACCOUNT_ID_USD
            )
        );

        assertThrows(InvalidInputException.class, () ->
            invoiceSettlementService.addAndApplyManualPayment(
                INVOICE_NUMBER,
                BigDecimal.TEN,
                null,
                PaymentType.CHECK,
                Instant.now().getEpochSecond(),
                BigDecimal.TEN,
                "",
                BigDecimal.ZERO,
                PAYMENT_BANK_ACCOUNT_ID_EUR
            )
        );
    }

    @Test
    public void paymentAppliedToDraftInvoiceShouldFail() {
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockInvoiceSettlementDao.getPaymentBalance(PAYMENT_ID)).thenReturn(Optional.of(getPaymentBalance(BigDecimal.TEN)));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.DRAFT));

        //This will result in NPE as invoice date for DRAFT invoice is set to NULL and invoice date is compared to payment date.
        assertThrows(NullPointerException.class, () ->
            invoiceSettlementService.addAndApplyManualPayment(
                INVOICE_NUMBER,
                BigDecimal.TEN,
                null,
                PaymentType.CHECK,
                Instant.now().getEpochSecond(),
                BigDecimal.TEN,
                "",
                BigDecimal.ZERO,
                PAYMENT_BANK_ACCOUNT_ID_USD
            )
        );
    }

    @Test
    public void applyPaymentWithMismatchingInvoiceAmount() {
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockInvoiceSettlementDao.getPaymentBalance(PAYMENT_ID)).thenReturn(Optional.of(getPaymentBalance(BigDecimal.TEN)));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));

        assertThrows(ConflictingStateException.class, () ->
            invoiceSettlementService.addAndApplyManualPayment(
                INVOICE_NUMBER,
                BigDecimal.ONE,
                null,
                PaymentType.CHECK,
                Instant.now().getEpochSecond(),
                BigDecimal.TEN,
                "",
                BigDecimal.ZERO,
                PAYMENT_BANK_ACCOUNT_ID_USD
            )
        );
    }

    @Test
    public void successfulCreditMemoApplication() {
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockCreditMemoRetrievalService.getCreditMemoBalance(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemoBalance(BigDecimal.TEN));
        when(mockCreditMemoRetrievalService.getCreditMemoByNumber(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemo(CreditMemoStatus.POSTED));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));

        invoiceSettlementService.applyCredit(INVOICE_NUMBER, BigDecimal.TEN, CREDIT_MEMO_NUMBER, BigDecimal.TEN, "");

        setupTransaction();
        verify(mockInvoiceSettlementDao).upsertSettlementApplication(any(SettlementApplication.class), eq(TENANT_ID), any(), any());
    }

    private void setupTransaction() {
        verify(mockDslContext).transactionResult(jooqTransactionCaptor.capture());
        try {
            jooqTransactionCaptor.getValue().run(mockConfiguration);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void insufficientInvoiceBalanceShouldFailCreditMemo() {
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.ONE)));
        when(mockCreditMemoRetrievalService.getCreditMemoBalance(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemoBalance(BigDecimal.TEN));
        when(mockCreditMemoRetrievalService.getCreditMemoByNumber(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemo(CreditMemoStatus.POSTED));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));

        assertThrows(IllegalArgumentException.class, () ->
            invoiceSettlementService.applyCredit(INVOICE_NUMBER, BigDecimal.ONE, CREDIT_MEMO_NUMBER, BigDecimal.TEN, "")
        );
    }

    @Test
    public void insufficientCreditMemoBalanceShouldFail() {
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockCreditMemoRetrievalService.getCreditMemoBalance(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemoBalance(BigDecimal.ONE));
        when(mockCreditMemoRetrievalService.getCreditMemoByNumber(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemo(CreditMemoStatus.POSTED));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));

        assertThrows(IllegalArgumentException.class, () ->
            invoiceSettlementService.applyCredit(INVOICE_NUMBER, BigDecimal.TEN, CREDIT_MEMO_NUMBER, BigDecimal.TEN, "")
        );
    }

    @Test
    public void creditMemoFromADifferentAccountShouldFail() {
        var creditMemoBalance = new CreditMemoBalance(UUID.randomUUID(), UUID.randomUUID().toString(), CREDIT_MEMO_NUMBER, BigDecimal.TEN, null);
        when(mockCreditMemoRetrievalService.getCreditMemoByNumber(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemo(CreditMemoStatus.POSTED));
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockCreditMemoRetrievalService.getCreditMemoBalance(CREDIT_MEMO_NUMBER)).thenReturn(creditMemoBalance);
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));

        assertThrows(IllegalArgumentException.class, () ->
            invoiceSettlementService.applyCredit(INVOICE_NUMBER, BigDecimal.TEN, CREDIT_MEMO_NUMBER, BigDecimal.TEN, "")
        );
    }

    @Test
    public void creditAppliedToDraftInvoiceShouldFail() {
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockInvoiceSettlementDao.getCreditMemoBalance(CREDIT_MEMO_NUMBER)).thenReturn(Optional.of(getCreditMemoBalance(BigDecimal.TEN)));
        when(mockCreditMemoRetrievalService.getCreditMemoByNumber(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemo(CreditMemoStatus.POSTED));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.DRAFT));

        assertThrows(IllegalArgumentException.class, () ->
            invoiceSettlementService.applyCredit(INVOICE_NUMBER, BigDecimal.TEN, CREDIT_MEMO_NUMBER, BigDecimal.TEN, "")
        );
    }

    @Test
    public void creditAppliedFromDraftCreditMemoShouldFail() {
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockInvoiceSettlementDao.getCreditMemoBalance(CREDIT_MEMO_NUMBER)).thenReturn(Optional.of(getCreditMemoBalance(BigDecimal.TEN)));
        when(mockCreditMemoRetrievalService.getCreditMemoByNumber(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemo(CreditMemoStatus.POSTED));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.DRAFT));

        assertThrows(IllegalArgumentException.class, () ->
            invoiceSettlementService.applyCredit(INVOICE_NUMBER, BigDecimal.TEN, CREDIT_MEMO_NUMBER, BigDecimal.TEN, "")
        );
    }

    @Test
    public void applyCreditWithMismatchingInvoiceAmount() {
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockInvoiceSettlementDao.getCreditMemoBalance(CREDIT_MEMO_NUMBER)).thenReturn(Optional.of(getCreditMemoBalance(BigDecimal.TEN)));
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));

        assertThrows(ConflictingStateException.class, () ->
            invoiceSettlementService.applyCredit(INVOICE_NUMBER, BigDecimal.ONE, CREDIT_MEMO_NUMBER, BigDecimal.TEN, "")
        );
    }

    @Test
    public void unapplyCreditShouldSucceed() {
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.PAID));
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.ZERO)));
        when(mockCreditMemoRetrievalService.getCreditMemoByNumber(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemo(CreditMemoStatus.CLOSED));
        when(mockCreditMemoRetrievalService.getCreditMemoBalance(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemoBalance(BigDecimal.ZERO));

        SettlementApplication settlementToUnapply = getSettlementToUnapply(NEGATED_SETTLEMENT_ID, SettlementApplicationType.CREDIT, null);
        when(mockInvoiceSettlementDao.getSettlementApplication(NEGATED_SETTLEMENT_ID)).thenReturn(settlementToUnapply);
        when(mockInvoiceSettlementDao.getCreditSettlementsWithoutUnapplication(INVOICE_NUMBER)).thenReturn(List.of(settlementToUnapply));

        invoiceSettlementService.unapplyCredit(INVOICE_NUMBER, BigDecimal.ZERO, CREDIT_MEMO_NUMBER, BigDecimal.ZERO, NEGATED_SETTLEMENT_ID, "");

        setupTransaction();
        verify(mockInvoiceSettlementDao).upsertSettlementApplication(any(SettlementApplication.class), eq(TENANT_ID), any(), any());
    }

    @Test
    public void unapplyCreditForAlreadyUnappliedSettlementShouldFail() {
        when(mockInvoiceRetrievalService.getInvoice(INVOICE_NUMBER)).thenReturn(getInvoice(InvoiceStatus.POSTED));
        when(mockInvoiceSettlementDao.getInvoiceBalance(INVOICE_NUMBER)).thenReturn(Optional.of(getInvoiceBalance(BigDecimal.TEN)));
        when(mockCreditMemoRetrievalService.getCreditMemoByNumber(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemo(CreditMemoStatus.POSTED));
        when(mockCreditMemoRetrievalService.getCreditMemoBalance(CREDIT_MEMO_NUMBER)).thenReturn(getCreditMemoBalance(BigDecimal.TEN));

        SettlementApplication settlementToUnapply = getSettlementToUnapply(NEGATED_SETTLEMENT_ID, SettlementApplicationType.CREDIT, null);
        when(mockInvoiceSettlementDao.getSettlementApplication(NEGATED_SETTLEMENT_ID)).thenReturn(settlementToUnapply);
        when(mockInvoiceSettlementDao.getCreditSettlementsWithoutUnapplication(INVOICE_NUMBER)).thenReturn(List.of());

        assertThrows(ConflictingStateException.class, () ->
            invoiceSettlementService.unapplyCredit(INVOICE_NUMBER, BigDecimal.TEN, CREDIT_MEMO_NUMBER, BigDecimal.TEN, NEGATED_SETTLEMENT_ID, "")
        );
    }

    @Test
    void handleScheduledPaymentRetriesForInvoice_featureDisabled_shouldDoNothing() {
        when(mockFeatureService.isEnabled(Feature.PAYMENT_RETRY)).thenReturn(false);

        invoiceSettlementService.handleScheduledPaymentRetriesForInvoice(mockDslContext, INVOICE_NUMBER);

        // No interactions expected when feature is disabled
        verify(mockInvoiceSettlementDao, never()).getSettlementApplications(any());
    }

    @Test
    void handleScheduledPaymentRetriesForInvoice_noInProgressSettlements_shouldDoNothing() {
        when(mockFeatureService.isEnabled(Feature.PAYMENT_RETRY)).thenReturn(true);
        when(mockInvoiceSettlementDao.getSettlementApplications(INVOICE_NUMBER)).thenReturn(List.of());

        invoiceSettlementService.handleScheduledPaymentRetriesForInvoice(mockDslContext, INVOICE_NUMBER);

        verify(mockInvoiceSettlementDao).getSettlementApplications(INVOICE_NUMBER);
        // No further interactions expected
    }

    @Test
    void handleScheduledPaymentRetriesForInvoice_inProgressSettlements_noPaymentAttempts_shouldDoNothing() {
        when(mockFeatureService.isEnabled(Feature.PAYMENT_RETRY)).thenReturn(true);

        SettlementApplication inProgress = getAttemptingSettlementApplication();

        Payment payment = getInitiatedPayment();

        when(mockInvoiceSettlementDao.getSettlementApplications(INVOICE_NUMBER)).thenReturn(List.of(inProgress));
        when(mockPaymentGetService.getPaymentByPaymentId(PAYMENT_ID)).thenReturn(payment);
        when(mockPaymentGetService.getPaymentAttemptsByPaymentId(PAYMENT_ID)).thenReturn(List.of());

        invoiceSettlementService.handleScheduledPaymentRetriesForInvoice(mockDslContext, INVOICE_NUMBER);

        verify(mockInvoiceSettlementDao).getSettlementApplications(INVOICE_NUMBER);
        verify(mockPaymentGetService).getPaymentAttemptsByPaymentId(PAYMENT_ID);
    }

    @Test
    void handleScheduledPaymentRetriesForInvoice_inProgressSettlements_withNonTerminalPaymentAttempt_shouldThrow() {
        when(mockFeatureService.isEnabled(Feature.PAYMENT_RETRY)).thenReturn(true);

        SettlementApplication inProgress = getAttemptingSettlementApplication();
        Payment payment = getInitiatedPayment();

        PaymentAttempt nonTerminalAttempt = mock(PaymentAttempt.class);
        when(nonTerminalAttempt.getState()).thenReturn(PaymentAttemptState.PROCESSING);

        when(mockInvoiceSettlementDao.getSettlementApplications(INVOICE_NUMBER)).thenReturn(List.of(inProgress));
        when(mockPaymentGetService.getPaymentByPaymentId(PAYMENT_ID)).thenReturn(payment);
        when(mockPaymentGetService.getPaymentAttemptsByPaymentId(PAYMENT_ID)).thenReturn(List.of(nonTerminalAttempt));

        assertThrows(ConflictingStateException.class, () ->
            invoiceSettlementService.handleScheduledPaymentRetriesForInvoice(mockDslContext, INVOICE_NUMBER)
        );
    }

    @Test
    void handleScheduledPaymentRetriesForInvoice_inProgressSettlements_withAllTerminalPaymentAttempts_shouldAllow() {
        when(mockFeatureService.isEnabled(Feature.PAYMENT_RETRY)).thenReturn(true);

        SettlementApplication inProgress = getAttemptingSettlementApplication();
        Payment payment = getInitiatedPayment();

        PaymentAttempt terminalAttempt = mock(PaymentAttempt.class);
        when(terminalAttempt.getState()).thenReturn(PaymentAttemptState.FAILED);

        when(mockInvoiceSettlementDao.getSettlementApplications(INVOICE_NUMBER)).thenReturn(List.of(inProgress));
        when(mockPaymentGetService.getPaymentByPaymentId(PAYMENT_ID)).thenReturn(payment);
        when(mockPaymentGetService.getPaymentAttemptsByPaymentId(PAYMENT_ID)).thenReturn(List.of(terminalAttempt));

        invoiceSettlementService.handleScheduledPaymentRetriesForInvoice(mockDslContext, INVOICE_NUMBER);

        verify(mockInvoiceSettlementDao).getSettlementApplications(INVOICE_NUMBER);
        verify(mockPaymentGetService).getPaymentAttemptsByPaymentId(PAYMENT_ID);
    }

    @Test
    void handleScheduledPaymentRetriesForInvoice_inProgressSettlements_withNoPaymentMetadata_shouldAllow() {
        when(mockFeatureService.isEnabled(Feature.PAYMENT_RETRY)).thenReturn(true);

        SettlementApplication inProgress = getAttemptingSettlementApplication();
        Payment payment = getInitiatedPayment();
        payment.setMetadata(null); // No metadata

        PaymentAttempt firstAttempt = mock(PaymentAttempt.class);
        when(firstAttempt.getState()).thenReturn(PaymentAttemptState.FAILED);

        when(mockInvoiceSettlementDao.getSettlementApplications(INVOICE_NUMBER)).thenReturn(List.of(inProgress));
        when(mockPaymentGetService.getPaymentByPaymentId(PAYMENT_ID)).thenReturn(payment);
        when(mockPaymentGetService.getPaymentAttemptsByPaymentId(PAYMENT_ID)).thenReturn(List.of(firstAttempt));

        invoiceSettlementService.handleScheduledPaymentRetriesForInvoice(mockDslContext, INVOICE_NUMBER);

        verify(mockInvoiceSettlementDao, times(1)).updatePendingSettlementApplication(
            eq(inProgress.getId()),
            eq(SettlementApplicationStatus.FAILED),
            any(),
            eq(Optional.of(mockDslContext))
        );
    }

    private InvoiceBalance getInvoiceBalance(BigDecimal amount) {
        return new InvoiceBalance(
            UUID.randomUUID(),
            ACCOUNT_ID,
            INVOICE_NUMBER.getNumber(),
            amount,
            SupportedCurrency.getDefaultCurrency().getCurrencyCode(),
            null
        );
    }

    private PaymentBalance getPaymentBalance(BigDecimal amount) {
        return new PaymentBalance(UUID.randomUUID(), ACCOUNT_ID, PAYMENT_ID, amount, null);
    }

    private CreditMemoBalance getCreditMemoBalance(BigDecimal amount) {
        return new CreditMemoBalance(UUID.randomUUID(), ACCOUNT_ID, CREDIT_MEMO_NUMBER, amount, null);
    }

    private CreditMemo getCreditMemo(CreditMemoStatus status) {
        return new CreditMemo(
            UUID.randomUUID(),
            null,
            null,
            BigDecimal.TEN,
            TENANT_ID,
            EntityFixture.ENTITY_1_ID,
            null,
            null,
            "USD",
            status,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );
    }

    private Invoice getInvoice(InvoiceStatus status, Instant invoiceDate, String entityId) {
        return new Invoice.InvoiceBuilder()
            .id(new Invoice.InternalId(UUID.randomUUID()))
            .entityId(entityId != null ? entityId : EntityFixture.ENTITY_1_ID)
            .invoiceNumber(INVOICE_NUMBER)
            .createdOn(Instant.now())
            .invoiceDate(invoiceDate)
            .postedDate(status == InvoiceStatus.POSTED ? Instant.now() : null)
            .dueDate(status == InvoiceStatus.POSTED ? Instant.now().plus(30, ChronoUnit.DAYS) : null)
            .currency("USD")
            .subTotal(BigDecimal.TEN)
            .taxTotal(BigDecimal.ZERO)
            .total(BigDecimal.TEN)
            .subscriptionId("")
            .customerAccountId(ACCOUNT_ID)
            .status(status)
            .createInvoice();
    }

    private Invoice getInvoice(InvoiceStatus status) {
        Instant invoiceDate = status == InvoiceStatus.POSTED ? Instant.now() : null;
        return getInvoice(status, invoiceDate, null);
    }

    private SettlementApplication getSettlementToUnapply(UUID id, SettlementApplicationType type, SettlementApplicationStatus status) {
        return SettlementApplication.builder()
            .id(id)
            .entityId(EntityFixture.ENTITY_1_ID)
            .customerAccountId(ACCOUNT_ID)
            .invoiceNumber(INVOICE_NUMBER.getNumber())
            .paymentId(PAYMENT_ID)
            .creditMemoNumber(CREDIT_MEMO_NUMBER)
            .applicationType(type)
            .amount(BigDecimal.TEN)
            .note("note")
            .appliedOn(Instant.now())
            .createdOn(Instant.now())
            .status(status)
            .build();
    }

    private static SettlementApplication getAttemptingSettlementApplication() {
        return SettlementApplication.builder()
            .id(UUID.randomUUID())
            .status(SettlementApplicationStatus.ATTEMPTING_PAYMENT_COLLECTION)
            .applicationType(SettlementApplicationType.PAYMENT)
            .paymentId(PAYMENT_ID)
            .build();
    }

    private static Payment getInitiatedPayment() {
        Payment payment = new Payment();
        payment.setPaymentId(PAYMENT_ID);
        payment.setAmount(BigDecimal.TEN);
        payment.setCustomerAccountId(ACCOUNT_ID);
        payment.setPaymentMethodId(UUID.randomUUID());
        PaymentMetadata metadata = ImmutablePaymentMetadata.builder()
            .retryMetadata(new PaymentMetadata.PaymentRetryMetadata(UUID.randomUUID(), UUID.randomUUID(), Optional.empty()))
            .build();
        payment.setMetadata(metadata);
        payment.setState(PaymentState.INITIATED);
        return payment;
    }
}
