package com.subskribe.billy.invoicesettlement.fixture;

import com.subskribe.billy.account.fixture.AccountFixture;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationStatus;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationType;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

public class SettlementApplicationFixture {

    public static SettlementApplication.Builder getDefault() {
        Instant now = Instant.now();
        return SettlementApplication.builder()
            .id(UUID.randomUUID())
            .entityId(EntityFixture.ENTITY_1_ID)
            .customerAccountId(AccountFixture.ACCOUNT_1_ID)
            .invoiceNumber("INV-123456")
            .paymentId("PAY-123456")
            .applicationType(SettlementApplicationType.PAYMENT)
            .amount(BigDecimal.TEN)
            .note("note")
            .appliedOn(now)
            .createdOn(now)
            .status(SettlementApplicationStatus.APPLIED_PAYMENT);
    }
}
