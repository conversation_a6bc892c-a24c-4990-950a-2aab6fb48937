package com.subskribe.billy.invoicesettlement.services;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.services.AccountingPeriodService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.invoicesettlement.db.RefundDAO;
import com.subskribe.billy.invoicesettlement.model.CreditMemo;
import com.subskribe.billy.invoicesettlement.model.CreditMemoBalance;
import com.subskribe.billy.invoicesettlement.model.CreditMemoStatus;
import com.subskribe.billy.invoicesettlement.model.Refund;
import com.subskribe.billy.payment.services.PaymentGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class RefundServiceTest {

    private CreditMemoRetrievalService mockCmRetrievalService;
    private static final String FAKE_CM_NUMBER = "CM-0001";
    private static final String FAKE_PAYMENT_ID = UUID.randomUUID().toString();
    private static final String TENANT_ID = "Tenant-123";
    private static final String ENTITY_ID = "Entity-123";

    private RefundService refundService;

    @BeforeEach
    void setUp() {
        mockCmRetrievalService = mock(CreditMemoRetrievalService.class);
        AccountingPeriodService mockAccountingPeriodService = mock(AccountingPeriodService.class);
        RefundDAO mockRefundDao = mock(RefundDAO.class);
        PaymentGetService mockPaymentGetService = mock(PaymentGetService.class);
        RefundIdGenerator mockIdGenerator = mock(RefundIdGenerator.class);
        TenantSettingService mockTenantSettingService = mock(TenantSettingService.class);
        TenantIdProvider mockTenantIdProvider = mock(TenantIdProvider.class);
        DSLContextProvider mockDslContextProvider = mock(DSLContextProvider.class);
        EventPublishingService mockEventPublishingService = mock(EventPublishingService.class);

        when(mockCmRetrievalService.getCreditMemoByNumber(anyString())).thenReturn(getCreditMemo());
        when(mockAccountingPeriodService.inClosedAccountingPeriod(any(), any())).thenReturn(false);

        refundService = new RefundService(
            mockRefundDao,
            mockCmRetrievalService,
            mockPaymentGetService,
            mockIdGenerator,
            mockTenantSettingService,
            mockAccountingPeriodService,
            mockTenantIdProvider,
            mockDslContextProvider,
            mockEventPublishingService
        );
    }

    @Test
    public void applyingRefundWithNullAmountShouldFail() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> refundService.createAndApplyRefund(createRefund(null, FAKE_PAYMENT_ID)));
    }

    @Test
    public void applyingRefundWithNegativeAmountShouldFail() {
        Assertions.assertThrows(IllegalArgumentException.class, () ->
            refundService.createAndApplyRefund(createRefund(BigDecimal.valueOf(-1), FAKE_PAYMENT_ID))
        );
    }

    @Test
    public void applyingRefundWithAmountOverCreditMemoBalanceShouldFail() {
        when(mockCmRetrievalService.getCreditMemoBalance(FAKE_CM_NUMBER)).thenReturn(
            new CreditMemoBalance(UUID.randomUUID(), "", FAKE_CM_NUMBER, BigDecimal.valueOf(1000), Instant.now())
        );
        Assertions.assertThrows(IllegalArgumentException.class, () ->
            refundService.createAndApplyRefund(createRefund(BigDecimal.valueOf(1001), FAKE_PAYMENT_ID))
        );
    }

    private Refund createRefund(BigDecimal amount, String paymentId) {
        var refund = new Refund();
        refund.setCreditMemoNumber(FAKE_CM_NUMBER);
        refund.setPaymentId(paymentId);
        refund.setAmount(amount);
        return refund;
    }

    private CreditMemo getCreditMemo() {
        return new CreditMemo(
            UUID.randomUUID(),
            null,
            null,
            BigDecimal.TEN,
            TENANT_ID,
            ENTITY_ID,
            null,
            null,
            null,
            CreditMemoStatus.POSTED,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );
    }
}
