package com.subskribe.billy.invoicesettlement.mapper;

import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationStatus;
import com.subskribe.billy.invoicesettlement.model.SettlementApplicationType;
import com.subskribe.billy.jooq.default_schema.tables.records.SettlementApplicationRecord;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

public class SettlementApplicationMapperTest {

    @Test
    public void testRecordMapper() {
        SettlementApplication settlementApplication = mockSettlementApplication();
        SettlementApplicationRecordMapper settlementApplicationRecordMapper = Mappers.getMapper(SettlementApplicationRecordMapper.class);
        SettlementApplicationRecord record = settlementApplicationRecordMapper.settlementApplicationToRecord(settlementApplication);
        SettlementApplication mappedSettlementApplication = settlementApplicationRecordMapper.recordToSettlementApplication(record);
        Assertions.assertThat(mappedSettlementApplication).isEqualTo(settlementApplication);
    }

    private SettlementApplication mockSettlementApplication() {
        return SettlementApplication.builder()
            .id(UUID.randomUUID())
            .entityId("ENTITY-1234567")
            .customerAccountId("ACCOUNT-1234567")
            .invoiceNumber("INV-1234567")
            .paymentId("PAY-1234567")
            .creditMemoNumber("CM-1234567")
            .applicationType(SettlementApplicationType.PAYMENT)
            .amount(new BigDecimal("123456.**********"))
            .note("This is a test note.")
            .appliedOn(Instant.now())
            .exchangeRateId("EXR-1234567")
            .exchangeRate(new BigDecimal("123456.**********"))
            .exchangeRateDate(Instant.now())
            .functionalAmount(new BigDecimal("123456.**********"))
            .createdOn(Instant.now())
            .status(SettlementApplicationStatus.APPLIED_PAYMENT)
            .negatedSettlementId(UUID.randomUUID())
            .build();
    }
}
