package com.subskribe.billy.paymentterms.services;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.paymentterms.model.PaymentTermSettings;
import java.util.List;
import org.junit.jupiter.api.Test;

public class PaymentTermSettingsServiceTest {

    @Test
    public void testDefaultPaymentTermIsNull() {
        PaymentTermSettings settings = new PaymentTermSettings();
        settings.setCustomPaymentTermsAllowed(true);
        settings.setDefaultPaymentTerms(List.of(PaymentTerm.NET30, PaymentTerm.NET45, new PaymentTerm("NET55")));
        settings.setDefaultPaymentTerm(null);

        assertThrows(IllegalArgumentException.class, () -> PaymentTermSettingsService.validatePaymentTermSettings(settings));
    }

    @Test
    public void testDefaultPaymentTermsIsNullOrEmpty() {
        PaymentTermSettings settings = new PaymentTermSettings();
        settings.setCustomPaymentTermsAllowed(true);
        settings.setDefaultPaymentTerms(null);
        settings.setDefaultPaymentTerm(PaymentTerm.NET30);

        assertThrows(IllegalArgumentException.class, () -> PaymentTermSettingsService.validatePaymentTermSettings(settings));

        settings.setDefaultPaymentTerms(List.of());
        assertThrows(IllegalArgumentException.class, () -> PaymentTermSettingsService.validatePaymentTermSettings(settings));
    }

    @Test
    public void testCustomPaymentTermsAllowedIsNull() {
        PaymentTermSettings settings = new PaymentTermSettings();
        settings.setCustomPaymentTermsAllowed(null);
        settings.setDefaultPaymentTerms(List.of(PaymentTerm.NET30, PaymentTerm.NET45, PaymentTerm.NET60));
        settings.setDefaultPaymentTerm(PaymentTerm.NET30);

        assertThrows(IllegalArgumentException.class, () -> PaymentTermSettingsService.validatePaymentTermSettings(settings));

        settings.setCustomPaymentTermsAllowed(false);
        assertDoesNotThrow(() -> PaymentTermSettingsService.validatePaymentTermSettings(settings));
    }

    @Test
    public void testDefaultPaymentTermInDefaultPaymentTerms() {
        PaymentTermSettings settings = new PaymentTermSettings();
        settings.setCustomPaymentTermsAllowed(true);
        settings.setDefaultPaymentTerms(List.of(PaymentTerm.NET30, PaymentTerm.NET45, new PaymentTerm("NET55")));
        settings.setDefaultPaymentTerm(new PaymentTerm("NET99"));

        assertThrows(InvalidInputException.class, () -> PaymentTermSettingsService.validatePaymentTermSettings(settings));

        settings.setDefaultPaymentTerm(new PaymentTerm("NET55"));
        assertDoesNotThrow(() -> PaymentTermSettingsService.validatePaymentTermSettings(settings));
    }
}
