package com.subskribe.billy.reportingmetrics;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.metricsreporting.service.ReportingJobQueueService;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.enums.OrderType;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CustomOrderSortTest {

    private Order order1;
    private Order order2;
    private List<Order> orderList;

    @BeforeEach
    void TestSetup() {
        order1 = new Order();
        order1.setName("1");
        order1.setOrderType(OrderType.NEW);
        order1.setStartDate(Instant.now().minus(10, ChronoUnit.DAYS));
        order1.setExecutedOn(Instant.now());

        order2 = new Order();
        order2.setName("2");
        order2.setOrderType(OrderType.NEW);
        order2.setStartDate(Instant.now());
        order2.setExecutedOn(Instant.now());

        orderList = new ArrayList<>();
        orderList.add(order1);
        orderList.add(order2);
    }

    @Test
    void TestNewOrdersSort() {
        // two new orders should be sorted by their order start dates
        ReportingJobQueueService.customSortOrders(orderList);
        assertEquals("1", orderList.get(0).getName());
        assertEquals("2", orderList.get(1).getName());
    }

    @Test
    void TestNewOrdersSortWithReversedDate() {
        // two new orders should be sorted by their order start dates
        order1.setStartDate(Instant.now().plus(10, ChronoUnit.DAYS));

        ReportingJobQueueService.customSortOrders(orderList);
        assertEquals("2", orderList.get(0).getName());
        assertEquals("1", orderList.get(1).getName());
    }

    @Test
    void TestNewOrderShouldBeSortedFirstBeforeAmendment() {
        // two new orders should be sorted by their order start dates
        order1.setOrderType(OrderType.AMENDMENT);

        ReportingJobQueueService.customSortOrders(orderList);
        assertEquals("2", orderList.get(0).getName());
        assertEquals("1", orderList.get(1).getName());
    }

    @Test
    void TestNewOrderShouldBeSortedFirstBeforeCancel() {
        // two new orders should be sorted by their order start dates
        order1.setOrderType(OrderType.CANCEL);

        ReportingJobQueueService.customSortOrders(orderList);
        assertEquals("2", orderList.get(0).getName());
        assertEquals("1", orderList.get(1).getName());
    }

    @Test
    void TestAmendmentOrderShouldBeSortedFirstBeforeCancel() {
        // two new orders should be sorted by their order start dates
        order1.setOrderType(OrderType.CANCEL);

        order2.setOrderType(OrderType.AMENDMENT);

        ReportingJobQueueService.customSortOrders(orderList);
        assertEquals("2", orderList.get(0).getName());
        assertEquals("1", orderList.get(1).getName());
    }

    @Test
    void TestAmendmentsOrderSortByStartDates() {
        // two new orders should be sorted by their order start dates
        order1.setOrderType(OrderType.AMENDMENT);
        order1.setStartDate(Instant.now().plus(10, ChronoUnit.DAYS));

        order2.setOrderType(OrderType.AMENDMENT);

        ReportingJobQueueService.customSortOrders(orderList);
        assertEquals("2", orderList.get(0).getName());
        assertEquals("1", orderList.get(1).getName());
    }

    @Test
    void TestAmendmentsOrderSortByExecutionDates() {
        // two new orders should be sorted by their order start dates
        order1.setOrderType(OrderType.AMENDMENT);
        order1.setStartDate(order2.getStartDate());
        order1.setExecutedOn(Instant.now().plus(1, ChronoUnit.DAYS));

        order2.setOrderType(OrderType.AMENDMENT);

        ReportingJobQueueService.customSortOrders(orderList);
        assertEquals("2", orderList.get(0).getName());
        assertEquals("1", orderList.get(1).getName());
    }
}
