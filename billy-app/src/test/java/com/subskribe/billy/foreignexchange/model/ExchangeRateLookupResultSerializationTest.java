package com.subskribe.billy.foreignexchange.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.TimeZone;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class ExchangeRateLookupResultSerializationTest {

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    @Test
    public void testSerialization() throws JsonProcessingException {
        ExchangeRateLookupResult exchangeRateLookupResult = mockExchangeRateLookupResult();
        String serialized = OBJECT_MAPPER.writeValueAsString(exchangeRateLookupResult);
        ExchangeRateLookupResult deserialized = OBJECT_MAPPER.readValue(serialized, ExchangeRateLookupResult.class);
        Assertions.assertThat(deserialized).isEqualTo(exchangeRateLookupResult);
    }

    private ExchangeRateLookupResult mockExchangeRateLookupResult() {
        Instant effectiveDate = DateTimeCalculator.getStartOfDay(Instant.now(), TimeZone.getDefault().toZoneId());
        return ImmutableExchangeRateLookupResult.builder()
            .sourceCurrency("USD")
            .targetCurrency("GBP")
            .effectiveDate(effectiveDate)
            .timeZone(TimeZone.getDefault())
            .exchangeRate(BigDecimal.valueOf(1.23))
            .exchangeSource(ExchangeSource.QUODD)
            .build();
    }
}
