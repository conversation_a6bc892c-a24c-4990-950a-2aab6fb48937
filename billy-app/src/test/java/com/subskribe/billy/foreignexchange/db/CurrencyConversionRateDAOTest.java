package com.subskribe.billy.foreignexchange.db;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.model.ImmutableCurrencyConversionRate;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantIdProviderFixture;
import com.subskribe.billy.test.WithDb;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class CurrencyConversionRateDAOTest extends WithDb {

    private CurrencyConversionRateDAO currencyConversionRateDAO;
    private final TenantIdProvider tenantIdProvider = TenantIdProviderFixture.getDefault();
    private CurrencyConversionRate insertedRecord;

    @BeforeAll
    public void setUp() {
        currencyConversionRateDAO = new CurrencyConversionRateDAO(dslContextProvider, tenantIdProvider);
    }

    @Test
    @org.junit.jupiter.api.Order(1)
    void testAddCurrencyConversionRate() {
        CurrencyConversionRate currencyConversionRate = ImmutableCurrencyConversionRate.builder()
            .fromCurrency("USD")
            .toCurrency("GBP")
            .conversionRate(new BigDecimal("0.**********")) //Scale is set as 10 in database
            .effectiveDate(Instant.now().getEpochSecond())
            .roundingTreatment(RoundingMode.UNNECESSARY)
            .isOverridden(Boolean.FALSE)
            .build();

        insertedRecord = currencyConversionRateDAO.addCurrencyConversionRate(currencyConversionRate);
        assertThat(insertedRecord).usingRecursiveComparison().ignoringFields("id", "updatedBy").isEqualTo(currencyConversionRate);
    }

    @Test
    @org.junit.jupiter.api.Order(2)
    void testUpdateCurrencyConversionRate() {
        CurrencyConversionRate currencyConversionRate = ImmutableCurrencyConversionRate.builder()
            .from(insertedRecord)
            .conversionRate(new BigDecimal("0.7900000000")) //Scale is set as 10 in database
            .build();

        CurrencyConversionRate updatedRecord = currencyConversionRateDAO.updateCurrencyConversionRate(currencyConversionRate);
        assertThat(updatedRecord)
            .usingRecursiveComparison()
            .ignoringFields("id", "updatedBy", "conversionRate", "isOverridden")
            .isEqualTo(currencyConversionRate);
        assertThat(updatedRecord).usingRecursiveComparison().ignoringFields("id", "updatedBy", "isOverridden").isEqualTo(insertedRecord);
    }
}
