package com.subskribe.billy.foreignexchange.service;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.aws.secretsmanager.SecretsService;
import com.subskribe.billy.aws.secretsmanager.model.SecretType;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.foreignexchange.model.QuoddDateParams;
import java.time.Instant;
import java.util.TimeZone;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class QuoddDateParamsTest {

    private final CacheService cacheService = mock(CacheService.class);
    private final SecretsService secretsService = mock(SecretsService.class);

    private QuoddExchangeRateProvider quoddExchangeRateProvider;

    @BeforeEach
    void setUp() {
        quoddExchangeRateProvider = new QuoddExchangeRateProvider(secretsService, cacheService);
        when(secretsService.retrieveStaticSecret(SecretType.QUODD_API_KEY)).thenReturn("DUMMY_API_KEY");
    }

    @Test
    public void testUTCDateParams() {
        Instant instant = Instant.parse("2021-08-16T00:00:00Z"); // 00:00 UTC
        var dateParams = quoddExchangeRateProvider.getDateParams(instant, TimeZone.getTimeZone("UTC"), "FROM", "TO");
        Assertions.assertThat(dateParams.getStartDate()).isEqualTo("8/14/2021");
        Assertions.assertThat(dateParams.getEndDate()).isEqualTo("8/15/2021");
        Assertions.assertThat(dateParams.getFixingTime()).isEqualTo("23:00");
        Assertions.assertThat(dateParams.getFromCurrency()).isEqualTo("FROM");
        Assertions.assertThat(dateParams.getToCurrency()).isEqualTo("TO");
    }

    @Test
    public void testPSTDateParams() {
        Instant instant = Instant.parse("2021-08-15T07:00:00Z"); // 00:00 PST
        QuoddDateParams dateParams = quoddExchangeRateProvider.getDateParams(instant, TimeZone.getTimeZone("America/Los_Angeles"), "DUMMY", "DUMMY");
        Assertions.assertThat(dateParams.getStartDate()).isEqualTo("8/14/2021");
        Assertions.assertThat(dateParams.getEndDate()).isEqualTo("8/15/2021");
        Assertions.assertThat(dateParams.getFixingTime()).isEqualTo("06:00");
    }

    @Test
    public void testPDTDateParams() {
        Instant instant = Instant.parse("2021-12-15T08:00:00Z"); // 00:00 PST
        QuoddDateParams dateParams = quoddExchangeRateProvider.getDateParams(instant, TimeZone.getTimeZone("America/Los_Angeles"), "DUMMY", "DUMMY");
        Assertions.assertThat(dateParams.getStartDate()).isEqualTo("12/14/2021");
        Assertions.assertThat(dateParams.getEndDate()).isEqualTo("12/15/2021");
        Assertions.assertThat(dateParams.getFixingTime()).isEqualTo("07:00");
    }

    @Test
    public void testPSTDateParamsAM() {
        Instant instant = Instant.parse("2021-12-16T08:05:00Z"); // 00:05 PST
        QuoddDateParams dateParams = quoddExchangeRateProvider.getDateParams(instant, TimeZone.getTimeZone("America/Los_Angeles"), "DUMMY", "DUMMY");
        Assertions.assertThat(dateParams.getStartDate()).isEqualTo("12/15/2021");
        Assertions.assertThat(dateParams.getEndDate()).isEqualTo("12/16/2021");
        Assertions.assertThat(dateParams.getFixingTime()).isEqualTo("07:00");
    }

    @Test
    public void testPSTDateParams11PM() {
        Instant instant = Instant.parse("2021-08-16T06:00:00Z"); // 23:00 PST
        QuoddDateParams dateParams = quoddExchangeRateProvider.getDateParams(instant, TimeZone.getTimeZone("America/Los_Angeles"), "DUMMY", "DUMMY");
        Assertions.assertThat(dateParams.getStartDate()).isEqualTo("8/14/2021");
        Assertions.assertThat(dateParams.getEndDate()).isEqualTo("8/15/2021");
        Assertions.assertThat(dateParams.getFixingTime()).isEqualTo("06:00");
    }

    @Test
    public void testISTDateParams() {
        Instant instant = Instant.parse("2021-08-15T18:30:00Z"); // 00:00 IST next day
        QuoddDateParams dateParams = quoddExchangeRateProvider.getDateParams(instant, TimeZone.getTimeZone("Asia/Kolkata"), "DUMMY", "DUMMY");
        Assertions.assertThat(dateParams.getStartDate()).isEqualTo("8/14/2021");
        Assertions.assertThat(dateParams.getEndDate()).isEqualTo("8/15/2021");
        Assertions.assertThat(dateParams.getFixingTime()).isEqualTo("17:30");
    }
}
