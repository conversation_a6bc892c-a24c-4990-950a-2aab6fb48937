package com.subskribe.billy.foreignexchange.service;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.foreignexchange.fixture.TransactionalExchangeRateServiceFixture;
import java.time.Instant;
import java.util.TimeZone;
import java.util.stream.Stream;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class ExchangeRateEffectiveDateTest {

    @Test
    public void testJobRunTimeInPST() {
        // set clock at 7/7/21 11:05 pm PST (+7:00)
        Instant instant = Instant.parse("2021-07-08T06:05:00Z");
        String expectedDate = "2021-07-08";
        String timezone = "America/Los_Angeles";
        var transactionalExchangeRateService = TransactionalExchangeRateServiceFixture.withTimeZoneAndClock(timezone, instant);

        String actualDate = transactionalExchangeRateService.getEffectiveDateForDailyRefresh();
        assertEquals(expectedDate, actualDate);
    }

    @Test
    public void testJobRunTimeInUTC() {
        // set clock at 7/7/21 11:05 pm UTC
        Instant instant = Instant.parse("2021-07-07T23:05:00Z");
        String expectedDate = "2021-07-08";
        String timezone = "UTC";
        var transactionalExchangeRateService = TransactionalExchangeRateServiceFixture.withTimeZoneAndClock(timezone, instant);

        String actualDate = transactionalExchangeRateService.getEffectiveDateForDailyRefresh();
        assertEquals(expectedDate, actualDate);
    }

    @Test
    public void testJobRunTimeInIST() {
        // set clock at 7/7/21 11:05 pm IST (-5:30)
        Instant instant = Instant.parse("2021-07-07T17:35:00Z");
        String expectedDate = "2021-07-08";
        String timezone = "UTC";
        var transactionalExchangeRateService = TransactionalExchangeRateServiceFixture.withTimeZoneAndClock(timezone, instant);

        String actualDate = transactionalExchangeRateService.getEffectiveDateForDailyRefresh();
        assertEquals(expectedDate, actualDate);
    }

    @Test
    public void testJobRunTime_AfterMidnightTimeInPST() {
        // set clock at 7/8/21 00:05 am PST (+7:00)
        Instant instant = Instant.parse("2021-07-08T07:05:00Z");
        String expectedDate = "2021-07-09";
        String timezone = "America/Los_Angeles";
        var transactionalExchangeRateService = TransactionalExchangeRateServiceFixture.withTimeZoneAndClock(timezone, instant);

        String actualDate = transactionalExchangeRateService.getEffectiveDateForDailyRefresh();
        assertEquals(expectedDate, actualDate);
    }

    // test for valid effective date for the given as of date
    // when "as of" date is in the future, effective date should be today's date
    // in other cases, effective date should be start of the day of the "as of" date
    @ParameterizedTest
    @MethodSource("provideEffectiveDateParams")
    void testValidEffectiveDate_ByCombinations(String nowTime, String asOfTime, String effectiveTime) {
        Instant now = Instant.parse(nowTime);
        Instant asOfDate = Instant.parse(asOfTime);
        Instant expectedEffectiveDate = Instant.parse(effectiveTime);
        String timezone = "America/Los_Angeles";
        var transactionalExchangeRateService = TransactionalExchangeRateServiceFixture.withTimeZoneAndClock(timezone, now);
        var actualEffectiveDate = transactionalExchangeRateService.getEffectiveDateForAsOfDate(asOfDate, TimeZone.getTimeZone(timezone));
        Assertions.assertThat(actualEffectiveDate).isEqualTo(expectedEffectiveDate);
    }

    // nowTime, asOfTime, effectiveTime, isValid
    private static Stream<Arguments> provideEffectiveDateParams() {
        return Stream.of(
            Arguments.of("2023-10-02T12:00:00Z", "2023-10-02T13:00:00Z", "2023-10-02T07:00:00Z"), // as of date on same day as current date (monday)
            Arguments.of("2023-10-08T12:00:00Z", "2023-10-08T13:00:00Z", "2023-10-08T07:00:00Z"), // as of date on same day as current date (sunday)
            Arguments.of("2023-12-12T12:00:00Z", "2023-12-12T13:00:00Z", "2023-12-12T08:00:00Z"), // as of date on same day as current date with daylight saving
            Arguments.of("2023-10-03T12:00:00Z", "2023-10-03T07:00:00Z", "2023-10-03T07:00:00Z"), // as of date is exactly the start of the current date -> as of day / current day
            Arguments.of("2023-10-03T07:00:00Z", "2023-10-03T07:00:00Z", "2023-10-03T07:00:00Z"), // as of date = start of day = now -> as of day / current day
            Arguments.of("2023-10-03T12:00:00Z", "2023-10-03T06:59:59Z", "2023-10-02T07:00:00Z"), // as of date is one second prior to the start of the current date -> previous day
            Arguments.of("2023-10-08T06:05:00Z", "2023-10-08T07:00:00Z", "2023-10-08T07:00:00Z"), // at 11:05 pm, as of date is tomorrow -> tomorrow's date
            Arguments.of("2023-10-08T06:05:00Z", "2023-10-12T07:00:00Z", "2023-10-08T07:00:00Z"), // at 11:05 pm, as of date is in the future -> tomorrow's date
            Arguments.of("2023-10-08T06:05:00Z", "2023-10-08T06:05:00Z", "2023-10-07T07:00:00Z"), // at 11:05 pm, as of date is today -> today's date
            Arguments.of("2023-10-08T07:05:00Z", "2023-10-12T13:00:00Z", "2023-10-08T07:00:00Z"), // at 00:05am, as of date is in the future -> current date
            Arguments.of("2023-10-01T12:00:00Z", "2023-09-05T13:00:00Z", "2023-09-05T07:00:00Z") // as of date is in the past -> as of date
        );
    }
}
