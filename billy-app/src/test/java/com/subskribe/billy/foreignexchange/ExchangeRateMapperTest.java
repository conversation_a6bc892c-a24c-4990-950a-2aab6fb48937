package com.subskribe.billy.foreignexchange;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.foreignexchange.mapper.TransactionalExchangeRateMapper;
import com.subskribe.billy.foreignexchange.model.ExchangeSource;
import com.subskribe.billy.foreignexchange.model.ImmutableTransactionalExchangeRate;
import com.subskribe.billy.foreignexchange.model.TransactionalExchangeRate;
import com.subskribe.billy.jooq.default_schema.tables.records.TransactionalExchangeRateRecord;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

public class ExchangeRateMapperTest {

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    @Test
    public void testDatabaseMapper() {
        TransactionalExchangeRate exchangeRate = mockTransactionalExchangeRate();
        TransactionalExchangeRateMapper exchangeRateMapper = Mappers.getMapper(TransactionalExchangeRateMapper.class);
        TransactionalExchangeRateRecord record = exchangeRateMapper.toRecord(exchangeRate);
        TransactionalExchangeRate mappedExchangeRate = exchangeRateMapper.fromRecord(record);
        Assertions.assertThat(mappedExchangeRate).isEqualTo(exchangeRate);
    }

    @Test
    public void testSerialization() throws JsonProcessingException {
        TransactionalExchangeRate exchangeRate = mockTransactionalExchangeRate();
        String serialized = OBJECT_MAPPER.writeValueAsString(exchangeRate);
        TransactionalExchangeRate deserializedEntity = OBJECT_MAPPER.readValue(serialized, TransactionalExchangeRate.class);
        Assertions.assertThat(deserializedEntity).isEqualTo(exchangeRate);
    }

    private TransactionalExchangeRate mockTransactionalExchangeRate() {
        return ImmutableTransactionalExchangeRate.builder()
            .id(UUID.randomUUID().toString())
            .fromCurrency("USD")
            .toCurrency("GBP")
            .effectiveDate(Instant.now().getEpochSecond())
            .exchangeRate(new BigDecimal("123456.7890123456")) // numeric(16,10)
            .exchangeSource(ExchangeSource.QUODD)
            .updatedBy("USER-1234567")
            .isOverridden(Boolean.FALSE)
            .build();
    }
}
