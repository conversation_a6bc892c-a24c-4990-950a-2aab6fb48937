package com.subskribe.billy.foreignexchange.fixture;

import static org.mockito.Mockito.mock;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.foreignexchange.db.TransactionalExchangeRateDAO;
import com.subskribe.billy.foreignexchange.service.ExchangeRateProvider;
import com.subskribe.billy.foreignexchange.service.RandomExchangeRateProvider;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateService;
import com.subskribe.billy.shared.fixture.ClockFixture;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import java.time.Instant;

public class TransactionalExchangeRateServiceFixture {

    public static TransactionalExchangeRateService withTimeZoneAndClock(String timeZone, Instant clockInstant) {
        return new TransactionalExchangeRateService(
            FeatureServiceFixture.allEnabled(),
            new BillyConfiguration(),
            TenantSettingServiceFixture.withTimeZone(timeZone),
            EntityGetServiceFixture.entityGetServiceFixture(),
            mock(ExchangeRateProvider.class),
            new RandomExchangeRateProvider(),
            ClockFixture.get(clockInstant),
            mock(TransactionalExchangeRateDAO.class)
        );
    }
}
