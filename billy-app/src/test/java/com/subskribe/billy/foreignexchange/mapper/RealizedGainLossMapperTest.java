package com.subskribe.billy.foreignexchange.mapper;

import com.subskribe.billy.foreignexchange.model.ImmutableRealizedGainLoss;
import com.subskribe.billy.foreignexchange.model.RealizedGainLoss;
import com.subskribe.billy.foreignexchange.model.RealizedGainLossSource;
import com.subskribe.billy.jooq.default_schema.tables.records.RealizedGainLossRecord;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

public class RealizedGainLossMapperTest {

    @Test
    public void testDatabaseMapper() {
        RealizedGainLoss realizedGainLoss = mockRealizedGainLoss();
        RealizedGainLossMapper realizedGainLossMapper = Mappers.getMapper(RealizedGainLossMapper.class);
        RealizedGainLossRecord record = realizedGainLossMapper.toRecord(realizedGainLoss);
        RealizedGainLoss mappedRealizedGainLoss = realizedGainLossMapper.fromRecord(record);
        Assertions.assertThat(mappedRealizedGainLoss).isEqualTo(realizedGainLoss);
    }

    private RealizedGainLoss mockRealizedGainLoss() {
        return ImmutableRealizedGainLoss.builder()
            .id(UUID.randomUUID().toString())
            .tenantId(UUID.randomUUID().toString())
            .source(RealizedGainLossSource.SETTLEMENT)
            .invoiceNumber(UUID.randomUUID().toString())
            .settlementId(UUID.randomUUID().toString())
            .accountingDate(Instant.now())
            .transactionCurrency("USD")
            .functionalCurrency("GBP")
            .invoiceExchangeRate(new BigDecimal("123456.**********")) // numeric(16,10)
            .sourceExchangeRate(new BigDecimal("123456.**********")) // numeric(16,10)
            .sourceAmount(new BigDecimal("123456.**********")) // numeric(16,10)
            .realizedGainLossAmount(new BigDecimal("123456.**********")) // numeric(16,10)
            .createdOn(Instant.now())
            .updatedOn(Instant.now())
            .build();
    }
}
