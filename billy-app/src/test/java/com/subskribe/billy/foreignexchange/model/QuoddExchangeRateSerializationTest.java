package com.subskribe.billy.foreignexchange.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class QuoddExchangeRateSerializationTest {

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    @Test
    public void testSerialization() throws JsonProcessingException {
        QuoddExchangeRate quoddExchangeRate = mockQuoddExchangeRate();
        String serialized = OBJECT_MAPPER.writeValueAsString(quoddExchangeRate);
        QuoddExchangeRate deserializedEntity = OBJECT_MAPPER.readValue(serialized, QuoddExchangeRate.class);
        Assertions.assertThat(deserializedEntity).isEqualTo(quoddExchangeRate);
    }

    private QuoddExchangeRate mockQuoddExchangeRate() {
        return ImmutableQuoddExchangeRate.builder()
            .baseCurrency("USD")
            .quoteCurrency("GBP")
            .symbol("USDGBP")
            .startDate("2021-01-01")
            .startTime("00:00:00")
            .endDate("2021-01-01")
            .endTime("23:59:59")
            .open(new BigDecimal("1.2345"))
            .high(new BigDecimal("1.2345"))
            .low(new BigDecimal("1.2345"))
            .close(new BigDecimal("1.2345"))
            .average(new BigDecimal("1.2345"))
            .outcome("SUCCESS")
            .message(Optional.empty())
            .identity(UUID.randomUUID().toString())
            .delay(BigDecimal.TEN)
            .build();
    }
}
