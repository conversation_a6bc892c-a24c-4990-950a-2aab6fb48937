package com.subskribe.billy.foreignexchange.fixture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.subskribe.billy.foreignexchange.model.ExchangeSource;
import com.subskribe.billy.foreignexchange.model.ImmutableTransactionalExchangeRate;
import com.subskribe.billy.foreignexchange.model.TransactionalExchangeRate;
import com.subskribe.billy.foreignexchange.service.TransactionalExchangeRateGetService;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

public class TransactionalExchangeRateGetServiceFixture {

    public static final Long DEFAULT_EFFECTIVE_DATE = 978287400L; // 2001-01-01T00:00:00Z

    public static void mockExchangeRate(
        TransactionalExchangeRateGetService transactionalExchangeRateGetService,
        String fromCurrency,
        String toCurrency,
        BigDecimal exchangeRate
    ) {
        when(transactionalExchangeRateGetService.getExchangeRateAsOf(eq(fromCurrency), eq(toCurrency), any())).thenReturn(
            Optional.of(getExchangeRate(fromCurrency, toCurrency, exchangeRate))
        );
    }

    public static TransactionalExchangeRate getExchangeRate(String fromCurrency, String toCurrency, BigDecimal exchangeRate) {
        return ImmutableTransactionalExchangeRate.builder()
            .id(UUID.randomUUID().toString())
            .fromCurrency(fromCurrency)
            .toCurrency(toCurrency)
            .effectiveDate(DEFAULT_EFFECTIVE_DATE)
            .exchangeRate(exchangeRate)
            .exchangeSource(ExchangeSource.QUODD)
            .updatedBy(Optional.empty())
            .isOverridden(false)
            .build();
    }
}
