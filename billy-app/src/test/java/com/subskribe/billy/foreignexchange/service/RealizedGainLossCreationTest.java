package com.subskribe.billy.foreignexchange.service;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.foreignexchange.db.RealizedGainLossDAO;
import com.subskribe.billy.foreignexchange.model.ImmutableRealizedGainLossInput;
import com.subskribe.billy.foreignexchange.model.RealizedGainLoss;
import com.subskribe.billy.foreignexchange.model.RealizedGainLossInput;
import com.subskribe.billy.invoice.fixture.InvoiceFixture;
import com.subskribe.billy.invoice.model.Invoice;
import com.subskribe.billy.invoicesettlement.fixture.SettlementApplicationFixture;
import com.subskribe.billy.invoicesettlement.model.SettlementApplication;
import com.subskribe.billy.payment.fixture.PaymentFixture;
import com.subskribe.billy.payment.model.Payment;
import com.subskribe.billy.shared.UniqueIdGenerator;
import com.subskribe.billy.shared.fixture.ClockFixture;
import com.subskribe.billy.shared.fixture.UniqueIdGeneratorFixture;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantIdProviderFixture;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.Instant;
import java.util.Optional;
import org.assertj.core.api.Assertions;
import org.jooq.Configuration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

public class RealizedGainLossCreationTest {

    private final FeatureService featureService = FeatureServiceFixture.allEnabled();
    private final UniqueIdGenerator uniqueIdGenerator = UniqueIdGeneratorFixture.getDefault();
    private final TenantIdProvider tenantIdProvider = TenantIdProviderFixture.getDefault();
    private final EntityGetService entityGetService = EntityGetServiceFixture.entityGetServiceFixture();
    private final EventPublishingService eventPublishingService = mock(EventPublishingService.class);
    private final Clock clock = ClockFixture.get(Instant.now());
    private final RealizedGainLossDAO realizedGainLossDAO = mock(RealizedGainLossDAO.class);

    private RealizedGainLossService realizedGainLossService;

    @BeforeEach
    public void setUp() {
        realizedGainLossService = new RealizedGainLossService(
            featureService,
            uniqueIdGenerator,
            tenantIdProvider,
            entityGetService,
            eventPublishingService,
            clock,
            realizedGainLossDAO
        );
    }

    // when both payment and credit memo are missing, an exception should be thrown
    @Test
    public void testAddRealizedGainLossInTransaction_BothPaymentCreditMemoMissing() {
        Invoice invoice = InvoiceFixture.getDefault().createInvoice();
        SettlementApplication settlementApplication = SettlementApplicationFixture.getDefault().build();
        RealizedGainLossInput input = ImmutableRealizedGainLossInput.builder().invoice(invoice).settlementApplication(settlementApplication).build();

        Assertions.assertThatThrownBy(() -> realizedGainLossService.addRealizedGainLossInTransaction(mock(Configuration.class), input))
            .isInstanceOf(InvalidInputException.class)
            .hasMessage("Either payment or credit memo must be present");
    }

    // when invoice is in functional currency, no realized gain/loss should be added
    @Test
    public void testAddRealizedGainLossInTransaction_InvoiceInFunctionalCurrency() {
        Invoice invoice = InvoiceFixture.getDefault().createInvoice();
        SettlementApplication settlementApplication = SettlementApplicationFixture.getDefault().build();
        Payment payment = PaymentFixture.getDefault().build();
        RealizedGainLossInput input = ImmutableRealizedGainLossInput.builder()
            .invoice(invoice)
            .settlementApplication(settlementApplication)
            .payment(Optional.of(payment))
            .build();

        realizedGainLossService.addRealizedGainLossInTransaction(mock(Configuration.class), input);

        verify(realizedGainLossDAO, never()).addRealizedGainLossInTransaction(any(), any());
    }

    // when realized gain/loss is zero, no realized gain/loss should be added
    @Test
    public void testAddRealizedGainLossInTransaction_RealizedGainLossIsZero() {
        Invoice invoice = InvoiceFixture.getDefault().exchangeRate(BigDecimal.valueOf(1.1)).currency("EUR").createInvoice();
        SettlementApplication settlementApplication = SettlementApplicationFixture.getDefault()
            .amount(BigDecimal.valueOf(100))
            .functionalAmount(BigDecimal.valueOf(110))
            .exchangeRate(BigDecimal.valueOf(1.1))
            .build();
        Payment payment = PaymentFixture.getDefault().build();
        RealizedGainLossInput input = ImmutableRealizedGainLossInput.builder()
            .invoice(invoice)
            .settlementApplication(settlementApplication)
            .payment(Optional.of(payment))
            .build();

        realizedGainLossService.addRealizedGainLossInTransaction(mock(Configuration.class), input);

        verify(realizedGainLossDAO, never()).addRealizedGainLossInTransaction(any(), any());
    }

    // when realized gain/loss is not zero, realized gain/loss should be added
    // realized gain/loss amount = (settlement amount) x (settlement exchange rate - invoice exchange rate)
    @Test
    public void testAddRealizedGainLossInTransaction_Success() {
        Invoice invoice = InvoiceFixture.getDefault().exchangeRate(BigDecimal.valueOf(1.1)).currency("EUR").createInvoice();
        SettlementApplication settlementApplication = SettlementApplicationFixture.getDefault()
            .amount(BigDecimal.valueOf(100))
            .functionalAmount(BigDecimal.valueOf(120))
            .exchangeRate(BigDecimal.valueOf(1.2))
            .build();
        Payment payment = PaymentFixture.getDefault().build();
        RealizedGainLossInput input = ImmutableRealizedGainLossInput.builder()
            .invoice(invoice)
            .settlementApplication(settlementApplication)
            .payment(Optional.of(payment))
            .build();

        when(realizedGainLossDAO.addRealizedGainLossInTransaction(any(), any())).then(invocation -> invocation.getArgument(1));

        realizedGainLossService.addRealizedGainLossInTransaction(mock(Configuration.class), input);

        // verify the realized gain/loss amount is $10 (= $120 - $110)
        verify(realizedGainLossDAO, times(1)).addRealizedGainLossInTransaction(any(), any());
        ArgumentCaptor<RealizedGainLoss> realizedGainLossAmountCaptor = ArgumentCaptor.forClass(RealizedGainLoss.class);
        verify(realizedGainLossDAO).addRealizedGainLossInTransaction(any(), realizedGainLossAmountCaptor.capture());
        RealizedGainLoss realizedGainLoss = realizedGainLossAmountCaptor.getValue();
        Assertions.assertThat(realizedGainLoss.getId()).startsWith("RLGL-");
        Assertions.assertThat(realizedGainLoss.getRealizedGainLossAmount().orElseThrow()).isEqualByComparingTo(new BigDecimal("10.00"));
    }
}
