package com.subskribe.billy.foreignexchange.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.MemcachedClientProvider;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.foreignexchange.db.CurrencyConversionRateDAO;
import com.subskribe.billy.foreignexchange.model.CurrencyConversionRate;
import com.subskribe.billy.foreignexchange.model.ImmutableCurrencyConversionRate;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import java.util.concurrent.TimeoutException;
import net.rubyeye.xmemcached.MemcachedClient;
import net.rubyeye.xmemcached.exception.MemcachedException;
import org.apache.commons.lang.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class CurrencyConversionRateGetServiceTest {

    @Mock
    private CurrencyConversionRateDAO currencyConversionRateDAO;

    @Mock
    private MemcachedClientProvider clientProvider;

    @Mock
    private MemcachedClient memcachedClient;

    @Mock
    private TenantIdProvider tenantIdProvider;

    private CurrencyConversionRateGetService currencyConversionRateGetService;

    private final String tenantId = RandomStringUtils.randomAlphabetic(10);

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        when(clientProvider.getClient()).thenReturn(memcachedClient);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(tenantId);
        currencyConversionRateGetService = new CurrencyConversionRateGetService(
            FeatureServiceFixture.allEnabled(),
            currencyConversionRateDAO,
            new CacheService(clientProvider),
            tenantIdProvider
        );
    }

    @Test
    public void getCurrencyConversionRateFromCache() throws InterruptedException, TimeoutException, MemcachedException {
        String id = RandomStringUtils.randomAlphabetic(10);
        CurrencyConversionRate storedRate = createCurrencyConversionRate(id);

        ArgumentCaptor<String> cacheKeyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> cacheValueCaptor = ArgumentCaptor.forClass(String.class);

        // first invocation, cache miss
        when(memcachedClient.get(any(String.class))).thenReturn(null);
        when(currencyConversionRateDAO.getCurrencyConversionRateById(id, Optional.empty())).thenReturn(Optional.of(storedRate));
        Optional<CurrencyConversionRate> rate = currencyConversionRateGetService.getCurrencyConversionRateById(id);

        verify(memcachedClient, times(1)).set(cacheKeyCaptor.capture(), anyInt(), cacheValueCaptor.capture());
        assertEquals(id, rate.orElseThrow().getId());

        // subsequent invocations, cache hit
        when(memcachedClient.get(cacheKeyCaptor.getValue())).thenReturn(cacheValueCaptor.getValue());
        Optional<CurrencyConversionRate> cachedRate1 = currencyConversionRateGetService.getCurrencyConversionRateById(id);
        Optional<CurrencyConversionRate> cachedRate2 = currencyConversionRateGetService.getCurrencyConversionRateById(id);

        assertEquals(id, cachedRate1.orElseThrow().getId());
        assertEquals(id, cachedRate2.orElseThrow().getId());

        // DB should be only called once
        verify(currencyConversionRateDAO, times(1)).getCurrencyConversionRateById(id, Optional.empty());
    }

    private CurrencyConversionRate createCurrencyConversionRate(String id) {
        return new ImmutableCurrencyConversionRate(
            id,
            "USD",
            "EUR",
            new BigDecimal("1.2"),
            123456789L,
            "user",
            false,
            RoundingMode.HALF_UP,
            BigDecimal.ONE
        );
    }
}
