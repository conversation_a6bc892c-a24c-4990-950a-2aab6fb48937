package com.subskribe.billy.foreignexchange.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.DateTimeCalculator;
import java.time.Instant;
import java.util.TimeZone;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class QuoddDateParamsSerializationTest {

    private static final ObjectMapper OBJECT_MAPPER = JacksonProvider.defaultMapper();

    @Test
    public void testSerialization() throws JsonProcessingException {
        QuoddDateParams quoddDateParams = mockQuoddDateParams();
        String serialized = OBJECT_MAPPER.writeValueAsString(quoddDateParams);
        QuoddDateParams deserializedEntity = OBJECT_MAPPER.readValue(serialized, QuoddDateParams.class);
        Assertions.assertThat(deserializedEntity).isEqualTo(quoddDateParams);
    }

    private QuoddDateParams mockQuoddDateParams() {
        Instant effectiveDate = DateTimeCalculator.getStartOfDay(Instant.now(), TimeZone.getDefault().toZoneId());
        return ImmutableQuoddDateParams.builder()
            .effectiveDate(effectiveDate)
            .timeZone(TimeZone.getDefault())
            .fromCurrency("USD")
            .toCurrency("GBP")
            .startDate("2021-01-01")
            .endDate("2021-01-01")
            .fixingTime("23:00")
            .build();
    }
}
