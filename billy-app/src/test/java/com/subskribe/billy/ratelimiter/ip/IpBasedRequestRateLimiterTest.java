package com.subskribe.billy.ratelimiter.ip;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.cache.CacheService;
import com.subskribe.billy.cache.CacheType;
import com.subskribe.billy.ratelimiter.config.IpBasedRequestRateLimiterConfiguration;
import javax.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class IpBasedRequestRateLimiterTest {

    private static final int MAX_NUMBER_OF_REQUESTS = 5;

    private static final String IP = "MY_IP";

    @Mock
    private IpBasedRequestRateLimiterConfiguration ipBasedRequestRateLimiterConfiguration;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private HttpServletRequest request;

    @Mock
    private CacheService cacheService;

    private IpBasedRequestRateLimiter ipBasedRequestRateLimiter;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(billyConfiguration.getIpBasedRequestRateLimiterConfiguration()).thenReturn(ipBasedRequestRateLimiterConfiguration);
        when(ipBasedRequestRateLimiterConfiguration.getMaxNumberOfRequestsPerMinute()).thenReturn(MAX_NUMBER_OF_REQUESTS);
        when(ipBasedRequestRateLimiterConfiguration.getEnabled()).thenReturn(true);
        when(
            cacheService.get(
                any(String.class),
                eq(CacheType.AUTH_INFO_IP_CALLED),
                any(String.class),
                eq(IpBasedRequestRateLimiter.CACHE_LOADER),
                eq(Boolean.class)
            )
        ).thenReturn(false, true);
        ipBasedRequestRateLimiter = new IpBasedRequestRateLimiter(cacheService, billyConfiguration);
        ipBasedRequestRateLimiter.setHttpServletRequestForTesting(request);
        when(request.getHeader(IpBasedRequestRateLimiter.IP_HEADER)).thenReturn(IP);
    }

    @Test
    public void shouldNotRejectWhenDisabled() {
        when(ipBasedRequestRateLimiterConfiguration.getEnabled()).thenReturn(false);
        makeRequests(1, true);
        verify(request, never()).getHeader(any());
    }

    @Test
    public void shouldNotRejectIfRequestIsNotProvided() {
        ipBasedRequestRateLimiter.setHttpServletRequestForTesting(null);
        makeRequests(MAX_NUMBER_OF_REQUESTS + 1, true);
        verify(request, never()).getHeader(any());
    }

    @Test
    public void shouldNotRejectIfIpHeaderIsNotProvided() {
        when(request.getHeader(IpBasedRequestRateLimiter.IP_HEADER)).thenReturn(null);
        int numberOfRequests = MAX_NUMBER_OF_REQUESTS + 1;
        makeRequests(numberOfRequests, true);
        verify(request, times(2 * numberOfRequests)).getHeader(IpBasedRequestRateLimiter.IP_HEADER);
    }

    @Test
    public void callsBelowThresholdShouldNotBeRejected() {
        Assertions.assertDoesNotThrow(() -> makeRequests(MAX_NUMBER_OF_REQUESTS, true));
    }

    @Test
    public void callsAboveThresholdShouldBeRejected() {
        Assertions.assertThrows(IllegalStateException.class, () -> makeRequests(MAX_NUMBER_OF_REQUESTS + 1, true));
    }

    @Test
    public void callsAboveThresholdShouldNotBeRejectedIfTheySucceed() {
        Assertions.assertDoesNotThrow(() -> makeRequests(2 * MAX_NUMBER_OF_REQUESTS, false));
    }

    private void makeRequests(int number, boolean markAsBadRequest) {
        for (int i = 0; i < number; i++) {
            ipBasedRequestRateLimiter.checkRequest();
            if (markAsBadRequest) {
                ipBasedRequestRateLimiter.markBadRequest();
            }
        }
    }
}
