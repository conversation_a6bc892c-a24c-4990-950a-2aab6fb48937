package com.subskribe.billy.ratelimiter.filter;

import static javax.ws.rs.core.Response.Status.TOO_MANY_REQUESTS;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.auth.model.ApiKeyContext;
import com.subskribe.billy.auth.model.ApiKeyMetaData;
import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import com.subskribe.billy.auth.model.EntityContext;
import com.subskribe.billy.ratelimiter.config.RateLimiterConfiguration;
import com.subskribe.billy.user.model.Role;
import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerResponseContext;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.SecurityContext;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class RateLimiterFilterTest {

    private static final int MAX_NUMBER_OF_CALLS = 5;

    private static final String TENANT_ID_1 = "TENANT_ID_1";

    private static final ApiKeyMetaData API_KEY_META_DATA = new ApiKeyMetaData(
        UUID.randomUUID(),
        UUID.randomUUID().toString(),
        Role.ADMIN.name(),
        "generatedBy",
        "vaultKeyId",
        "entityId",
        true,
        "note",
        "deactivatedBy",
        "userId",
        "lastLetters",
        Instant.now(),
        Instant.now(),
        Instant.now()
    );

    private static final ApiKeyContext API_KEY_CONTEXT = new ApiKeyContext(
        UUID.randomUUID().toString(),
        API_KEY_META_DATA,
        EntityContext.builder().build()
    );

    private static final BillyAuthPrincipal AUTH_PRINCIPAL_1 = new BillyAuthPrincipal(
        TENANT_ID_1,
        API_KEY_CONTEXT,
        Optional.empty(),
        new BillyConfiguration()
    );

    @Mock
    private RateLimiterConfiguration rateLimiterConfiguration;

    @Mock
    private BillyConfiguration billyConfiguration;

    @Mock
    private MetricRegistry metricRegistry;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private ContainerRequestContext requestContext;

    @Mock
    private ContainerResponseContext responseContext;

    @Mock
    private Counter rejectCounter;

    @Mock
    private DistributedRateLimiter distributedRateLimiter;

    @Captor
    private ArgumentCaptor<Response> responseArgumentCaptor;

    private RateLimiterFilter rateLimiterFilter;

    private MultivaluedMap<String, Object> headers;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(billyConfiguration.getRateLimiterConfiguration()).thenReturn(rateLimiterConfiguration);
        when(rateLimiterConfiguration.getMaxTenantCallsPerMinute()).thenReturn(MAX_NUMBER_OF_CALLS);
        when(rateLimiterConfiguration.isEnabled()).thenReturn(true);
        when(requestContext.getSecurityContext()).thenReturn(securityContext);
        headers = new MultivaluedHashMap<>();
        when(responseContext.getHeaders()).thenReturn(headers);
        when(metricRegistry.counter(any(String.class))).thenReturn(rejectCounter);
        rateLimiterFilter = new RateLimiterFilter(billyConfiguration, metricRegistry, distributedRateLimiter);
    }

    @Test
    public void shouldNotFilterWhenDisabled() throws IOException {
        when(rateLimiterConfiguration.isEnabled()).thenReturn(false);
        makeCalls(1);
        verify(securityContext, never()).getUserPrincipal();
        verify(requestContext, never()).abortWith(any());
    }

    @Test
    public void callWithNoAuthentication() throws IOException {
        when(securityContext.getUserPrincipal()).thenReturn(null);
        makeCalls(1);
        verify(requestContext, never()).abortWith(any());
    }

    @Test
    public void callsShouldNotBeFilteredIfLimiterImplReturnsTrue() throws IOException {
        when(securityContext.getUserPrincipal()).thenReturn(AUTH_PRINCIPAL_1);
        when(distributedRateLimiter.tryConsume(TENANT_ID_1)).thenReturn(new RateLimitCheckResult(true, 0, 0));
        makeCalls(MAX_NUMBER_OF_CALLS);
        verify(requestContext, never()).abortWith(any());
    }

    @Test
    public void headersShouldBeSetOnAllowedRequests() throws IOException {
        when(securityContext.getUserPrincipal()).thenReturn(AUTH_PRINCIPAL_1);
        when(distributedRateLimiter.tryConsume(TENANT_ID_1)).thenReturn(new RateLimitCheckResult(true, 50, 500));
        Map<String, String> expectedPropertiesSet = Map.of("RateLimit-Remaining", "500", "RateLimit-Reset", "50");
        when(requestContext.getProperty("RATE_LIMIT_HEADERS")).thenReturn(expectedPropertiesSet);
        makeCalls(1);
        verify(requestContext, times(1)).setProperty("RATE_LIMIT_HEADERS", expectedPropertiesSet);
        Assertions.assertThat(headers).containsEntry("RateLimit-Remaining", List.of("500"));
        Assertions.assertThat(headers).containsEntry("RateLimit-Reset", List.of("50"));
    }

    @Test
    public void headersShouldBeSetOnBlockedRequests() throws IOException {
        when(securityContext.getUserPrincipal()).thenReturn(AUTH_PRINCIPAL_1);
        when(distributedRateLimiter.tryConsume(TENANT_ID_1)).thenReturn(new RateLimitCheckResult(false, 20, 0));
        Map<String, String> expectedPropertiesSet = Map.of("RateLimit-Remaining", "0", "RateLimit-Reset", "20", "Retry-After", "20");
        when(requestContext.getProperty("RATE_LIMIT_HEADERS")).thenReturn(expectedPropertiesSet);
        makeCalls(1);
        Assertions.assertThat(headers).containsEntry("RateLimit-Remaining", List.of("0"));
        Assertions.assertThat(headers).containsEntry("RateLimit-Reset", List.of("20"));
        Assertions.assertThat(headers).containsEntry("Retry-After", List.of("20"));
    }

    @Test
    public void callsShouldNotBeFilteredIfLimiterImplReturnsFalse() throws IOException {
        when(securityContext.getUserPrincipal()).thenReturn(AUTH_PRINCIPAL_1);
        when(distributedRateLimiter.tryConsume(TENANT_ID_1))
            .thenReturn(new RateLimitCheckResult(true, 0, 0))
            .thenReturn(new RateLimitCheckResult(false, 0, 0))
            .thenReturn(new RateLimitCheckResult(false, 0, 0));
        makeCalls(3);
        verify(rejectCounter, times(2)).inc();
        verify(requestContext, times(2)).abortWith(responseArgumentCaptor.capture());
        Assertions.assertThat(responseArgumentCaptor.getValue().getStatus()).isEqualTo(TOO_MANY_REQUESTS.getStatusCode());
    }

    private void makeCalls(int numberOfCalls) throws IOException {
        for (int i = 0; i < numberOfCalls; i++) {
            rateLimiterFilter.filter(requestContext);
            rateLimiterFilter.filter(requestContext, responseContext);
        }
    }
}
