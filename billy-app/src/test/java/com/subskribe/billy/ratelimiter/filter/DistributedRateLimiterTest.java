package com.subskribe.billy.ratelimiter.filter;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.ratelimiter.config.RateLimiterConfiguration;
import com.subskribe.billy.shared.ratelimit.RateLimiterFactory;
import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.BucketConfiguration;
import io.github.bucket4j.ConsumptionProbe;
import java.time.Duration;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DistributedRateLimiterTest {

    private static final String TENANT_ID = UUID.randomUUID().toString();

    private Bucket bucket;

    private DistributedRateLimiter distributedRateLimiter;

    @BeforeEach
    void setUp() {
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        RateLimiterConfiguration rateLimiterConfiguration = new RateLimiterConfiguration();
        rateLimiterConfiguration.setMaxTenantCallsPerMinute(10);
        when(billyConfiguration.getRateLimiterConfiguration()).thenReturn(rateLimiterConfiguration);
        RateLimiterFactory rateLimiterFactory = mock(RateLimiterFactory.class);
        BucketConfiguration expectedBucketConfig = BucketConfiguration.builder()
            .addLimit(Bandwidth.builder().capacity(10).refillIntervally(10, Duration.ofMinutes(1)).build())
            .build();
        bucket = mock(Bucket.class);
        when(rateLimiterFactory.buildBucket("request/global/" + TENANT_ID, expectedBucketConfig)).thenReturn(bucket);
        distributedRateLimiter = new DistributedRateLimiter(rateLimiterFactory, billyConfiguration);
    }

    @Test
    void testRejectedWithShorterRefill() {
        ConsumptionProbe consumptionProbe = ConsumptionProbe.rejected(0, 2_000_000_000L, 3_000_000_000L);
        when(bucket.tryConsumeAndReturnRemaining(1)).thenReturn(consumptionProbe);

        RateLimitCheckResult result = distributedRateLimiter.tryConsume(TENANT_ID);

        assertThat(result).isEqualTo(new RateLimitCheckResult(false, 2, 0));
    }

    @Test
    void testRejectedWithShorterReset() {
        ConsumptionProbe consumptionProbe = ConsumptionProbe.rejected(0, 4_000_000_000L, 3_000_000_000L);
        when(bucket.tryConsumeAndReturnRemaining(1)).thenReturn(consumptionProbe);

        RateLimitCheckResult result = distributedRateLimiter.tryConsume(TENANT_ID);

        assertThat(result).isEqualTo(new RateLimitCheckResult(false, 3, 0));
    }

    @Test
    void testAccepted() {
        ConsumptionProbe consumptionProbe = ConsumptionProbe.consumed(50, 2_000_000_000L);
        when(bucket.tryConsumeAndReturnRemaining(1)).thenReturn(consumptionProbe);

        RateLimitCheckResult result = distributedRateLimiter.tryConsume(TENANT_ID);

        assertThat(result).isEqualTo(new RateLimitCheckResult(true, 2, 50));
    }
}
