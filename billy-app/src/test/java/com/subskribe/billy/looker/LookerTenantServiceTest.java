package com.subskribe.billy.looker;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.looker.client.LookerClient;
import com.subskribe.billy.looker.db.LookerTenantDAO;
import com.subskribe.billy.looker.model.ImmutableLookerTenant;
import com.subskribe.billy.looker.model.LookerTenant;
import com.subskribe.billy.looker.util.LookerNameResolver;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class LookerTenantServiceTest {

    private LookerTenantDAO lookerTenantDAO;

    private LookerTenantService lookerTenantService;

    @BeforeEach
    void setUp() {
        LookerTenantGroupService lookerTenantGroupService = mock(LookerTenantGroupService.class);
        LookerNameResolver lookerNameResolver = mock(LookerNameResolver.class);
        lookerTenantDAO = mock(LookerTenantDAO.class);
        LookerClient lookerClient = mock(LookerClient.class);
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        when(billyConfiguration.getLookerConfiguration()).thenReturn(LookerTestStubs.createLookerConfigurationStub());

        lookerTenantService = new LookerTenantService(
            lookerTenantGroupService,
            lookerNameResolver,
            lookerTenantDAO,
            billyConfiguration,
            lookerClient
        );
    }

    @Test
    void testGetLookerTenantReturnsExistingTenant() {
        LookerTenant lookerTenant = stubTenant();
        when(lookerTenantDAO.getLookerTenantForTenantId(LookerTestStubs.DEFAULT_TENANT.getTenantId())).thenReturn(Optional.of(lookerTenant));

        LookerTenant result = lookerTenantService.getLookerTenant(LookerTestStubs.DEFAULT_TENANT);

        assertThat(result).isEqualTo(lookerTenant);
    }

    //TODO: Add tests for creation path

    private LookerTenant stubTenant() {
        return ImmutableLookerTenant.builder()
            .id(UUID.randomUUID())
            .tenantId(UUID.randomUUID().toString())
            .lookerTenantName(RandomStringUtils.randomAlphabetic(10))
            .modelSetId("11")
            .clientRoleId("22")
            .developerRoleId("33")
            .groupId("44")
            .build();
    }
}
