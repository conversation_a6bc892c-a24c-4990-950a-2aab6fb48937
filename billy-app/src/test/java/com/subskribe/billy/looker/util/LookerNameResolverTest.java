package com.subskribe.billy.looker.util;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.looker.LookerConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class LookerNameResolverTest {

    private LookerConfiguration lookerConfiguration;
    private LookerNameResolver lookerTenantNameGenerator;

    @BeforeEach
    void setUp() {
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);
        lookerConfiguration = mock(LookerConfiguration.class);

        lookerTenantNameGenerator = new LookerNameResolver(billyConfiguration);

        when(billyConfiguration.getLookerConfiguration()).thenReturn(lookerConfiguration);
        when(lookerConfiguration.getTenantModelSuffix()).thenReturn("");
    }

    @Test
    void generateLookerTenantName_AlreadyCleanTenantName() {
        assertThat(lookerTenantNameGenerator.generateLookerTenantName("Tenant Name")).isEqualTo("Tenant_Name");
    }

    @Test
    void generateLookerTenantName_NonAlphaNumericCharactersDropped() {
        assertThat(lookerTenantNameGenerator.generateLookerTenantName("Tenant!@$%^&*().][/'Name")).isEqualTo("TenantName");
    }

    @Test
    void generateLookerTenantName_SuffixAppended() {
        when(lookerConfiguration.getTenantModelSuffix()).thenReturn("_dev2");
        assertThat(lookerTenantNameGenerator.generateLookerTenantName("Tenant Name")).isEqualTo("Tenant_Name_dev2");
    }
}
