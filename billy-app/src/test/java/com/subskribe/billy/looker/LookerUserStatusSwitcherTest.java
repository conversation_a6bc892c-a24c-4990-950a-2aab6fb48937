package com.subskribe.billy.looker;

import static com.subskribe.billy.looker.LookerTestStubs.DEFAULT_TENANT;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.subskribe.billy.looker.client.LookerClient;
import com.subskribe.billy.looker.db.LookerUserDAO;
import com.subskribe.billy.looker.model.ImmutableLookerUserMapping;
import com.subskribe.billy.looker.model.LookerUserMapping;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class LookerUserStatusSwitcherTest {

    private LookerClient lookerClient;
    private LookerUserDAO lookerUserDAO;

    private LookerUserStatusSwitcher lookerUserStatusSwitcher;

    private static final String USER_ID = "user-id";
    private static final String LOOKER_USER_ID = "55";

    @BeforeEach
    void setUp() {
        lookerClient = mock(LookerClient.class);
        lookerUserDAO = mock(LookerUserDAO.class);
        lookerUserStatusSwitcher = new LookerUserStatusSwitcher(lookerClient, lookerUserDAO);
    }

    @Test
    void testDisabledUserFlippedIfFound() throws Exception {
        when(lookerUserDAO.getLookerUserForUserId(USER_ID, false)).thenReturn(Optional.of(stubLookerUserMapping(false)));
        lookerUserStatusSwitcher.changeUserStatusIfExists(USER_ID, true);

        verify(lookerClient, times(1)).changeUserStatus(LOOKER_USER_ID, true);
        verify(lookerUserDAO, times(1)).disableLookerUser(USER_ID, LOOKER_USER_ID);
    }

    @Test
    void testDisabledUserNotFlippedIfNotFound() {
        when(lookerUserDAO.getLookerUserForUserId(USER_ID, false)).thenReturn(Optional.empty());
        lookerUserStatusSwitcher.changeUserStatusIfExists(USER_ID, true);

        verifyNoInteractions(lookerClient);
        verify(lookerUserDAO, never()).disableLookerUser(anyString(), anyString());
    }

    @Test
    void testEnabledUserFlippedIfFound() throws Exception {
        when(lookerUserDAO.getLookerUserForUserId(USER_ID, true)).thenReturn(Optional.of(stubLookerUserMapping(true)));
        lookerUserStatusSwitcher.changeUserStatusIfExists(USER_ID, false);

        verify(lookerClient, times(1)).changeUserStatus(LOOKER_USER_ID, false);
        verify(lookerUserDAO, times(1)).disableLookerUser(USER_ID, LOOKER_USER_ID);
    }

    @Test
    void testEnabledUserNotFlippedIfNotFound() {
        when(lookerUserDAO.getLookerUserForUserId(USER_ID, true)).thenReturn(Optional.empty());
        lookerUserStatusSwitcher.changeUserStatusIfExists(USER_ID, false);

        verifyNoInteractions(lookerClient);
        verify(lookerUserDAO, never()).disableLookerUser(anyString(), anyString());
    }

    private LookerUserMapping stubLookerUserMapping(boolean isDisabled) {
        return ImmutableLookerUserMapping.builder()
            .lookerUserId(LOOKER_USER_ID)
            .tenantId(DEFAULT_TENANT.getTenantId())
            .userId(USER_ID)
            .isDisabled(isDisabled)
            .build();
    }
}
