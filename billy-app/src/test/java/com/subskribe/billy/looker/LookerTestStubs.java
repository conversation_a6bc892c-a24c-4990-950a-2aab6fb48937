package com.subskribe.billy.looker;

import com.subskribe.billy.tenant.model.Tenant;
import java.util.UUID;

public class LookerTestStubs {

    static final String LOOKER_USER_ID = "99";
    static final String LOOKER_ROLE_ID = "88";
    static final String LOOKER_GROUP_ID = "77";
    static final String MODEL_SET_ID = "111";
    static final String CLIENT_ROLE_ID = "122";
    static final String DEV_ROLE_ID = "133";
    static final String USER_ID = "USR-12345";
    static final String TIME_ZONE = "America/Los_Angeles";
    static final String BASE_URL = "https://test.subskribe.com";

    static final Tenant DEFAULT_TENANT = new Tenant(
        UUID.randomUUID().toString(),
        UUID.randomUUID().toString(),
        "Test Tenant",
        null,
        null,
        null,
        true,
        false,
        null,
        null
    );

    static LookerConfiguration createLookerConfigurationStub() {
        LookerConfiguration lookerConfiguration = new LookerConfiguration();
        lookerConfiguration.setBaseUrl("https://test.subskribe.com");
        lookerConfiguration.setUserRolePrefix("local");
        lookerConfiguration.setUserGroupPrefix("local");
        lookerConfiguration.setSharedFolderRoot("local-shared");
        lookerConfiguration.setTenantFolderRoot("local-private");
        lookerConfiguration.setAllowDevelopers(true);
        return lookerConfiguration;
    }
}
