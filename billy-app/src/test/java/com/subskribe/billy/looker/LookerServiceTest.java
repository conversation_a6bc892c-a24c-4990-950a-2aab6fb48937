package com.subskribe.billy.looker;

import static com.subskribe.billy.looker.LookerTestStubs.BASE_URL;
import static com.subskribe.billy.looker.LookerTestStubs.CLIENT_ROLE_ID;
import static com.subskribe.billy.looker.LookerTestStubs.DEFAULT_TENANT;
import static com.subskribe.billy.looker.LookerTestStubs.DEV_ROLE_ID;
import static com.subskribe.billy.looker.LookerTestStubs.LOOKER_GROUP_ID;
import static com.subskribe.billy.looker.LookerTestStubs.LOOKER_ROLE_ID;
import static com.subskribe.billy.looker.LookerTestStubs.LOOKER_USER_ID;
import static com.subskribe.billy.looker.LookerTestStubs.MODEL_SET_ID;
import static com.subskribe.billy.looker.LookerTestStubs.TIME_ZONE;
import static com.subskribe.billy.looker.LookerTestStubs.USER_ID;
import static com.subskribe.billy.looker.LookerTestStubs.createLookerConfigurationStub;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.looker.client.ImmutableLookerUser;
import com.subskribe.billy.looker.client.LookerClient;
import com.subskribe.billy.looker.client.LookerClientException;
import com.subskribe.billy.looker.client.LookerGroup;
import com.subskribe.billy.looker.client.LookerRole;
import com.subskribe.billy.looker.client.LookerUser;
import com.subskribe.billy.looker.client.LookerUserAttribute;
import com.subskribe.billy.looker.client.LookerUserCreateRequest;
import com.subskribe.billy.looker.client.LookerUserCredentialsEmail;
import com.subskribe.billy.looker.db.LookerUserDAO;
import com.subskribe.billy.looker.model.ImmutableLookerTenant;
import com.subskribe.billy.looker.model.ImmutableLookerUserMapping;
import com.subskribe.billy.looker.model.LookerTenant;
import com.subskribe.billy.looker.model.LookerUserMapping;
import com.subskribe.billy.looker.util.LookerNameResolver;
import com.subskribe.billy.tenant.model.TenantSetting;
import com.subskribe.billy.tenant.services.TenantService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.user.model.User;
import com.subskribe.billy.user.service.UserService;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class LookerServiceTest {

    private LookerClient lookerClient;
    private UserService userService;
    private TenantSettingService tenantSettingService;
    private TenantService tenantService;
    private BillyConfiguration billyConfiguration;
    private LookerUserDAO lookerUserDAO;
    private LookerNameResolver lookerNameResolver;

    private LookerService lookerService;

    private static final LookerUserCreateRequest DEFAULT_REQUEST = new LookerUserCreateRequest("<EMAIL>", false);

    @BeforeEach
    void setUp() {
        lookerClient = mock(LookerClient.class);
        billyConfiguration = mock(BillyConfiguration.class);
        userService = mock(UserService.class);
        tenantSettingService = mock(TenantSettingService.class);
        tenantService = mock(TenantService.class);
        lookerUserDAO = mock(LookerUserDAO.class);
        lookerNameResolver = mock(LookerNameResolver.class);
        LookerTenantService lookerTenantService = mock(LookerTenantService.class);

        LookerConfiguration lookerConfiguration = createLookerConfigurationStub();
        when(billyConfiguration.getLookerConfiguration()).thenReturn(lookerConfiguration);
        when(billyConfiguration.getSiteUrl()).thenReturn(BASE_URL);
        when(lookerTenantService.getLookerTenant(DEFAULT_TENANT)).thenReturn(setupLookerTenant());

        lookerService = new LookerService(
            lookerClient,
            billyConfiguration,
            userService,
            tenantSettingService,
            tenantService,
            lookerUserDAO,
            lookerTenantService,
            lookerNameResolver
        );
    }

    @Test
    void createUserThrowsObjectNotFoundExceptionWhenUserNotFound() {
        when(userService.findUserByEmailFromAllTenants(DEFAULT_REQUEST.getEmail())).thenReturn(Optional.empty());

        assertThatThrownBy(() -> lookerService.createLookerUser(DEFAULT_REQUEST)).isInstanceOf(ObjectNotFoundException.class);
    }

    @Test
    void createUserThrowsIllegalArgumentExceptionWhenDeveloperUserNotAllowed() {
        LookerUserCreateRequest request = new LookerUserCreateRequest("<EMAIL>", true);
        LookerConfiguration lookerConfiguration = createLookerConfigurationStub();
        lookerConfiguration.setAllowDevelopers(false);
        when(billyConfiguration.getLookerConfiguration()).thenReturn(lookerConfiguration);

        assertThatThrownBy(() -> lookerService.createLookerUser(request)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    void testClientUserAddedToCorrectRole() throws Exception {
        setupTestUser();
        setupDefaultLookerUser();

        lookerService.createLookerUser(DEFAULT_REQUEST);

        verify(lookerClient, times(1)).addUserToRole(LOOKER_USER_ID, List.of(LOOKER_ROLE_ID, CLIENT_ROLE_ID));
    }

    @Test
    void tesInternalMappingCreated() throws Exception {
        setupTestUser();
        setupDefaultLookerUser();

        lookerService.createLookerUser(DEFAULT_REQUEST);

        LookerUserMapping expectedMapping = ImmutableLookerUserMapping.builder()
            .lookerUserId(LOOKER_USER_ID)
            .userId(USER_ID)
            .isDisabled(false)
            .tenantId(DEFAULT_TENANT.getTenantId())
            .build();
        verify(lookerUserDAO, times(1)).createLookerUser(expectedMapping, DEFAULT_TENANT.getTenantId());
    }

    @Test
    void testClientUserAddedToCorrectGroup() throws Exception {
        setupTestUser();
        setupDefaultLookerUser();

        lookerService.createLookerUser(DEFAULT_REQUEST);

        verify(lookerClient, times(1)).addUserToGroup(LOOKER_USER_ID, LOOKER_GROUP_ID);
    }

    @Test
    void testDeveloperUserAddedToCorrectRole() throws Exception {
        User testUser = setupTestUser();
        LookerUser lookerUser = setupDefaultLookerUser();
        LookerRole lookerRole = new LookerRole(LOOKER_ROLE_ID, "local-developer");
        ImmutableLookerUser expectedCreateRequest = ImmutableLookerUser.builder()
            .email(DEFAULT_REQUEST.getEmail())
            .locale("en")
            .disabled(false)
            .build();
        LookerUserCreateRequest lookerUserCreateRequest = new LookerUserCreateRequest("<EMAIL>", true);

        when(userService.findUserByEmailFromAllTenants(lookerUserCreateRequest.getEmail())).thenReturn(Optional.of(testUser));
        when(lookerClient.createUser(expectedCreateRequest)).thenReturn(lookerUser);
        when(lookerNameResolver.findRoleName(true)).thenReturn("local-developer");
        when(lookerClient.findRole("local-developer")).thenReturn(lookerRole);

        lookerService.createLookerUser(lookerUserCreateRequest);

        verify(lookerClient, times(1)).addUserToRole(LOOKER_USER_ID, List.of(LOOKER_ROLE_ID, DEV_ROLE_ID));
    }

    @Test
    void testUserIsDeletedIfCredentialsCannotBeCreated() throws Exception {
        User user = setupTestUser();
        setupDefaultLookerUser();

        when(lookerClient.setUserCredentialsEmail(LOOKER_USER_ID, new LookerUserCredentialsEmail(user.getEmail()))).thenThrow(
            new LookerClientException("something failed")
        );

        assertThatThrownBy(() -> lookerService.createLookerUser(DEFAULT_REQUEST)).isInstanceOf(ServiceFailureException.class);

        verify(lookerClient, times(1)).deleteUser(LOOKER_USER_ID);
    }

    @Test
    void testUserAttributesAreSet() throws Exception {
        User testUser = setupTestUser();
        setupDefaultLookerUser();

        lookerService.createLookerUser(DEFAULT_REQUEST);

        verify(lookerClient, times(1)).setUserAttributes(
            LOOKER_USER_ID,
            List.of(
                new LookerUserAttribute("14", testUser.getTenantId()),
                new LookerUserAttribute("15", TIME_ZONE),
                new LookerUserAttribute("5", TIME_ZONE),
                new LookerUserAttribute("16", BASE_URL)
            )
        );
    }

    private LookerUser setupDefaultLookerUser() throws Exception {
        LookerUser lookerUser = ImmutableLookerUser.builder()
            .id(LOOKER_USER_ID)
            .email(DEFAULT_REQUEST.getEmail())
            .locale("en")
            .disabled(false)
            .build();
        LookerRole lookerRole = new LookerRole(LOOKER_ROLE_ID, "local-client");
        LookerGroup lookerGroup = new LookerGroup(LOOKER_GROUP_ID, "local-" + DEFAULT_TENANT.getTenantId());
        ImmutableLookerUser expectedCreateRequest = ImmutableLookerUser.builder()
            .email(DEFAULT_REQUEST.getEmail())
            .locale("en")
            .disabled(false)
            .build();

        when(lookerClient.createUser(expectedCreateRequest)).thenReturn(lookerUser);

        when(lookerNameResolver.findRoleName(false)).thenReturn("local-client");
        when(lookerClient.findRole("local-client")).thenReturn(lookerRole);
        when(lookerClient.findGroup("local-" + DEFAULT_TENANT.getTenantId())).thenReturn(Optional.of(lookerGroup));

        return lookerUser;
    }

    private User setupTestUser() {
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setTenantId(DEFAULT_TENANT.getTenantId());
        user.setUserId(USER_ID);

        TenantSetting tenantSetting = mock(TenantSetting.class);
        when(tenantSetting.getDefaultTimeZone()).thenReturn(TimeZone.getTimeZone(TIME_ZONE));
        when(tenantSettingService.getTenantSettingInternal(user.getTenantId())).thenReturn(tenantSetting);
        when(tenantService.getTenant(user.getTenantId())).thenReturn(DEFAULT_TENANT);

        when(userService.findUserByEmailFromAllTenants(DEFAULT_REQUEST.getEmail())).thenReturn(Optional.of(user));
        return user;
    }

    private LookerTenant setupLookerTenant() {
        return ImmutableLookerTenant.builder()
            .tenantId(DEFAULT_TENANT.getTenantId())
            .lookerTenantName(DEFAULT_TENANT.getName())
            .modelSetId(MODEL_SET_ID)
            .clientRoleId(CLIENT_ROLE_ID)
            .developerRoleId(DEV_ROLE_ID)
            .groupId(LOOKER_GROUP_ID)
            .build();
    }
}
