package com.subskribe.billy.looker;

import static com.subskribe.billy.looker.LookerTestStubs.BASE_URL;
import static com.subskribe.billy.looker.LookerTestStubs.DEFAULT_TENANT;
import static com.subskribe.billy.looker.LookerTestStubs.createLookerConfigurationStub;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.looker.client.ImmutableLookerFolder;
import com.subskribe.billy.looker.client.LookerClient;
import com.subskribe.billy.looker.client.LookerContentAccess;
import com.subskribe.billy.looker.client.LookerFolder;
import com.subskribe.billy.looker.client.LookerGroup;
import com.subskribe.billy.looker.client.LookerPermissionType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class LookerTenantGroupServiceTest {

    private LookerClient lookerClient;

    private LookerTenantGroupService lookerTenantGroupService;

    @BeforeEach
    void setUp() {
        lookerClient = mock(LookerClient.class);
        BillyConfiguration billyConfiguration = mock(BillyConfiguration.class);

        LookerConfiguration lookerConfiguration = createLookerConfigurationStub();
        when(billyConfiguration.getLookerConfiguration()).thenReturn(lookerConfiguration);
        when(billyConfiguration.getSiteUrl()).thenReturn(BASE_URL);

        lookerTenantGroupService = new LookerTenantGroupService(lookerClient, billyConfiguration);
    }

    @Test
    void setupTenantCreatesGroupAndFolders() throws Exception {
        String groupName = "local-" + DEFAULT_TENANT.getTenantId();
        LookerGroup createdGroup = new LookerGroup("300", groupName);
        LookerFolder localShared = ImmutableLookerFolder.builder().id("100").name("local-shared").parentId("1").contentMetadataId("200").build();
        LookerFolder localPrivate = ImmutableLookerFolder.builder().id("101").name("local-private").parentId("1").contentMetadataId("202").build();
        LookerFolder newFolder = ImmutableLookerFolder.builder().name(DEFAULT_TENANT.getName()).parentId("59").build();
        LookerFolder createdFolder = ImmutableLookerFolder.builder()
            .id("151")
            .name(DEFAULT_TENANT.getName())
            .parentId("59")
            .contentMetadataId("251")
            .build();
        when(lookerClient.createGroup(groupName)).thenReturn(createdGroup);
        when(lookerClient.findFolderByName("local-shared")).thenReturn(localShared);
        when(lookerClient.findFolderByName("local-private")).thenReturn(localPrivate);
        when(lookerClient.createFolder(newFolder)).thenReturn(createdFolder);

        lookerTenantGroupService.setupTenantUserGroup(DEFAULT_TENANT);

        verify(lookerClient, times(1)).grantFolderAccessToGroup(new LookerContentAccess("1", LookerPermissionType.VIEW, "300"));
        verify(lookerClient, times(1)).grantFolderAccessToGroup(new LookerContentAccess("200", LookerPermissionType.VIEW, "300"));
        verify(lookerClient, times(1)).grantFolderAccessToGroup(new LookerContentAccess("251", LookerPermissionType.EDIT, "300"));
        verify(lookerClient, times(1)).disableAccessInheritance("251");
        LookerFolder expectedUpdate = ImmutableLookerFolder.builder()
            .id("151")
            .name(DEFAULT_TENANT.getName())
            .parentId("101")
            .contentMetadataId("251")
            .build();
        verify(lookerClient, times(1)).updateFolder(expectedUpdate);
    }
}
