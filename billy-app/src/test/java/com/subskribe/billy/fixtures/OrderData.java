package com.subskribe.billy.fixtures;

import com.subskribe.billy.invoice.model.PaymentTerm;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.order.model.OrderSource;
import com.subskribe.billy.order.model.OrderStartDateType;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.enums.ActionType;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.OrderStatus;
import com.subskribe.billy.shared.enums.OrderType;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.model.SubscriptionDurationModel;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Currency;
import java.util.List;
import java.util.Optional;

public class OrderData {

    public static final LocalDateTime START_DATE = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
    public static final LocalDateTime END_DATE = LocalDateTime.of(2025, 1, 1, 0, 0, 0);

    public static Order createOrder(
        String orderId,
        String tenantId,
        String entityId,
        String subscriptionId,
        String accountId,
        String contactId,
        List<OrderLineItem> lineItems,
        LocalDateTime startDate,
        LocalDateTime endDate
    ) {
        return new Order(
            AutoGenerate.getNewUuid(),
            null,
            orderId,
            tenantId,
            entityId,
            null,
            accountId,
            null,
            OrderType.NEW,
            Currency.getInstance("USD"),
            PaymentTerm.NET30,
            null,
            subscriptionId,
            contactId,
            contactId,
            null,
            lineItems,
            lineItems,
            startDate.toInstant(ZoneOffset.UTC),
            endDate.toInstant(ZoneOffset.UTC),
            startDate.toInstant(ZoneOffset.UTC),
            null,
            new Recurrence(Cycle.YEAR, 1),
            BillingTerm.UP_FRONT,
            BigDecimal.TEN,
            BigDecimal.TEN,
            null,
            BigDecimal.TEN,
            OrderStatus.DRAFT,
            "test-user",
            Instant.now(),
            Instant.now(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            0,
            null,
            0,
            null,
            false,
            false,
            null,
            null,
            null,
            true,
            null,
            null,
            OrderSource.USER,
            OrderStartDateType.FIXED,
            null,
            Optional.empty(),
            SubscriptionDurationModel.TERMED,
            null
        );
    }

    public static OrderLineItem createOrderLineItem(
        String orderLineItemId,
        String tenantId,
        String entityId,
        String orderId,
        String planId,
        String chargeId,
        long quantity,
        LocalDateTime startDate,
        LocalDateTime endDate
    ) {
        return new OrderLineItem(
            AutoGenerate.getNewUuid(),
            orderLineItemId,
            null,
            tenantId,
            entityId,
            orderId,
            1,
            false,
            ActionType.ADD,
            planId,
            null,
            null,
            null,
            null,
            chargeId,
            null,
            null,
            quantity,
            BigDecimal.valueOf(2),
            BigDecimal.ONE,
            BigDecimal.valueOf(100),
            BigDecimal.valueOf(200),
            BigDecimal.valueOf(100),
            BigDecimal.valueOf(0),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            startDate.toInstant(ZoneOffset.UTC),
            endDate.toInstant(ZoneOffset.UTC),
            List.of(),
            null,
            null,
            Instant.now(),
            Instant.now()
        );
    }
}
