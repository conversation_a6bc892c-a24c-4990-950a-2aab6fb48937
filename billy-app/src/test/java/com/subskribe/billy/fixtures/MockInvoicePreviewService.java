package com.subskribe.billy.fixtures;

import com.subskribe.billy.invoice.model.InvoiceItemPreview;
import com.subskribe.billy.invoice.model.InvoicePreview;
import com.subskribe.billy.invoice.service.InvoiceServiceInternal;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MockInvoicePreviewService extends InvoiceServiceInternal {

    public MockInvoicePreviewService() {
        super(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );
    }

    @Override
    public InvoicePreview previewInvoiceByOrderPeriod(Order order, boolean skipAmendPercentOf, boolean forOrderLineCalculation) {
        return previewInvoiceByOrderPeriod(order);
    }

    @Override
    public InvoicePreview previewInvoiceByOrderPeriod(Order order) {
        List<InvoiceItemPreview> previewLineItems = new ArrayList<>();
        order
            .getLineItems()
            .forEach(li ->
                previewLineItems.add(
                    new InvoiceItemPreview(
                        li.getOrderLineId(),
                        BigDecimal.valueOf(1000),
                        null,
                        BigDecimal.ZERO,
                        BigDecimal.ZERO,
                        null,
                        BigDecimal.ZERO,
                        BigDecimal.ZERO,
                        BigDecimal.ZERO,
                        Map.of(),
                        Map.of()
                    )
                )
            );
        BillingPeriod orderPeriod = new BillingPeriod(order.getStartDate(), order.getEndDate(), order.getEndDate(), order.getBillingCycle());
        var total = BigDecimal.valueOf(previewLineItems.size() * 1000L);
        return new InvoicePreview(
            order.getOrderId(),
            orderPeriod,
            previewLineItems,
            total,
            total,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            total,
            List.of()
        );
    }

    @Override
    public void triggerOrderExecutionInvoiceDependencies(String orderId) {
        // this is exclusively for unit testing we do nothing here
    }
}
