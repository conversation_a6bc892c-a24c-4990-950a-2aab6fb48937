package com.subskribe.billy.fixtures;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.temporal.Recurrence;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import org.mockito.stubbing.Answer;

@SuppressWarnings("rawtypes,unchecked")
public class ProductCatalogGetServiceMockData {

    private final Map<String, Plan> plans;
    private final Map<String, Charge> charges;
    private final ProductCatalogGetService mockGetService;

    public ProductCatalogGetServiceMockData() {
        plans = new HashMap<>();
        charges = new HashMap<>();

        // Add Fixture Objects
        addSaasProduct();
        addBroadbandProduct();
        for (var planEntry : plans.entrySet()) {
            for (var charge : planEntry.getValue().getCharges()) {
                charges.put(charge.getChargeId(), charge);
            }
        }

        ProductCatalogGetService mockProductCatalogGetService = mock(ProductCatalogGetService.class);

        // Override Database Methods - with Custom Logic
        when(mockProductCatalogGetService.getChargesByChargeId(anyList())).thenAnswer(
            (Answer) invocation -> {
                Object[] args = invocation.getArguments();
                List<String> chargeIds = (List<String>) args[0];
                return chargeIds.stream().distinct().map(charges::get).collect(Collectors.toList());
            }
        );
        when(mockProductCatalogGetService.getPlansByPlanIds(anyList())).thenAnswer(
            (Answer) invocation -> {
                Object[] args = invocation.getArguments();
                List<String> planIds = (List<String>) args[0];
                return planIds.stream().distinct().map(plans::get).collect(Collectors.toList());
            }
        );

        mockGetService = mockProductCatalogGetService;
    }

    public Plan getPlan(String planId) {
        return plans.get(planId);
    }

    /*
        Product Id: "SAAS-PROD"
        Product: "SaaS Subscription"
        Plan: "SaaS Plan"
        Charges (each) : Recurring Monthly Charge of $10
     */
    private void addSaasProduct() {
        var name = "SaaS Subscription";
        var product = new Product(
            UUID.randomUUID(),
            EntityFixture.ALL_ENTITY_IDS,
            "SAAS-PROD",
            false,
            name,
            name,
            "SaaS Subscription (Product)",
            null,
            null,
            Instant.now(),
            null
        );

        // Plain Vanilla SaaS Subscription Plan
        var planId = UUID.randomUUID();
        var externalPlanId = "SAAS-PLAN";
        var charge = ProductCatalogData.createCharge(UUID.randomUUID(), planId, new Recurrence(Cycle.MONTH, 1), BigDecimal.TEN);
        var planCharges = List.of(charge);
        var plan = ProductCatalogData.createPlan(planId, externalPlanId, product.getProductId(), planCharges);
        plans.put(externalPlanId, plan);
    }

    /*
        Product Id: "PREPAID-INTERNET"
        Product: "Prepaid Broadband Service"
        Plans: "100 Mbps", "200 Mbps"
        Charges (each) : "Prepaid" + "Usage" with Drawdown
     */
    private void addBroadbandProduct() {
        var name = "Prepaid Internet Service";
        var product = new Product(
            UUID.randomUUID(),
            EntityFixture.ALL_ENTITY_IDS,
            "PREPAID-INTERNET",
            false,
            name,
            name,
            "Prepaid Internet Service (Product)",
            null,
            null,
            Instant.now(),
            null
        );

        // 100 Mbps Plan
        var planId = UUID.randomUUID();
        var externalPlanId = "PREPAID-100";
        var chargePrepaid = ProductCatalogData.createCharge(UUID.randomUUID(), planId);
        var chargeDrawdown = ProductCatalogData.createDrawdownCharge(UUID.randomUUID(), planId);
        var planCharges = List.of(chargePrepaid, chargeDrawdown);
        var plan = ProductCatalogData.createPlan(planId, externalPlanId, product.getProductId(), planCharges);
        plans.put(externalPlanId, plan);

        // 200 Mbps Plan
        planId = UUID.randomUUID();
        externalPlanId = "PREPAID-200";
        chargePrepaid = ProductCatalogData.createCharge(UUID.randomUUID(), planId);

        // chargeDrawdown = ProductCatalogData.createCharge(UUID.randomUUID(), planId, new Recurrence(Cycle.YEAR, 1), new BigDecimal(149), true );
        chargeDrawdown = ProductCatalogData.createDrawdownCharge(UUID.randomUUID(), planId);

        planCharges = List.of(chargePrepaid, chargeDrawdown);
        plan = ProductCatalogData.createPlan(planId, externalPlanId, product.getProductId(), planCharges);
        plans.put(externalPlanId, plan);
    }

    public ProductCatalogGetService getMockGetService() {
        return mockGetService;
    }
}
