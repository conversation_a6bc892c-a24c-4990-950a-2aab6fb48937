package com.subskribe.billy.fixtures;

import com.subskribe.billy.currency.SupportedCurrency;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.temporal.Recurrence;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public class ProductCatalogData {

    public static Product createProduct(String productId, String sku) {
        return new Product(
            UUID.randomUUID(),
            Set.of(),
            productId,
            true,
            "Sample Product",
            "Sample Name",
            "Sample Name Desc",
            sku,
            null,
            Instant.now(),
            "Test"
        );
    }

    public static Plan createPlan(UUID id, String planId, String productId, List<Charge> charges) {
        var plan = new Plan();
        plan.setId(id);
        plan.setPlanId(planId);
        plan.setEntityIds(EntityFixture.ALL_ENTITY_IDS);
        plan.setName("Test Plan");
        plan.setDescription("Test Plan description");
        plan.setStatus(PlanStatus.ACTIVE);
        plan.setProductId(productId);
        plan.setCharges(charges);
        plan.setCurrency(SupportedCurrency.getDefaultCurrency());
        return plan;
    }

    public static Charge createCharge(UUID chargeId, UUID planUuid) {
        return createCharge(chargeId, planUuid, new Recurrence(Cycle.YEAR, 1), BigDecimal.TEN);
    }

    public static Charge createCharge(UUID chargeId, UUID planUuid, Recurrence recurrence, BigDecimal amount) {
        return createCharge(chargeId, planUuid, recurrence, amount, false);
    }

    public static Charge createDrawdownCharge(UUID chargeId, UUID planUuid) {
        return createCharge(chargeId, planUuid, ChargeType.USAGE, new Recurrence(Cycle.MONTH, 1), BigDecimal.TEN, true, false);
    }

    private static Charge createCharge(UUID chargeId, UUID planUuid, Recurrence recurrence, BigDecimal amount, boolean isDrawDown) {
        return createCharge(chargeId, planUuid, ChargeType.RECURRING, recurrence, amount, isDrawDown, false);
    }

    public static Charge createCustomPricingCharge(UUID chargeId, UUID planUuid, ChargeType chargeType, Recurrence recurrence) {
        return createCharge(chargeId, planUuid, chargeType, recurrence, null, false, true);
    }

    private static Charge createCharge(
        UUID chargeUuid,
        UUID planUuid,
        ChargeType chargeType,
        Recurrence recurrence,
        BigDecimal amount,
        boolean isDrawDown,
        boolean isCustomPricing
    ) {
        return new Charge(
            chargeUuid,
            EntityFixture.ALL_ENTITY_IDS,
            chargeUuid.toString(),
            planUuid,
            planUuid.toString(),
            "Charge Name",
            "Charge Display Name",
            "Charge Description",
            amount,
            chargeType,
            ChargeModel.PER_UNIT,
            recurrence,
            null,
            null,
            null,
            null,
            null,
            isDrawDown,
            null,
            null,
            isCustomPricing,
            false,
            true,
            false,
            null,
            null,
            List.of(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            false,
            false,
            null,
            BillingCycle.DEFAULT,
            BillingTerm.UP_FRONT,
            null,
            null,
            null
        );
    }
}
