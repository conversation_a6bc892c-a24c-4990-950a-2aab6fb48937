package com.subskribe.billy.fixtures;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.account.services.AccountGetService;
import java.util.HashMap;
import java.util.Map;

public class AccountGetServiceMockSetup {

    public static final String TEST_ACCOUNT_ID = "OKTA-ACCT";

    public static AccountGetService get() {
        Map<String, AccountContact> users = new HashMap<>();
        Map<String, Account> accounts = new HashMap<>();
        // Add Fixture Objects
        addOktaAccount(users, accounts);

        // Create Mock Class
        AccountGetService mockAccountGetService = mock(AccountGetService.class);

        // Override Database Methods
        for (var userEntry : users.entrySet()) {
            var user = userEntry.getValue();
            when(mockAccountGetService.getContact(user.getContactId())).thenReturn(user);
        }
        for (var accountEntry : accounts.entrySet()) {
            var account = accountEntry.getValue();
            when(mockAccountGetService.getAccount(account.getAccountId())).thenReturn(account);
        }

        // Return Mock Class
        return mockAccountGetService;
    }

    private static void addOktaAccount(Map<String, AccountContact> users, Map<String, Account> accounts) {
        var user = AccountData.createAccountUser("OKTA-USER-JOHN", TEST_ACCOUNT_ID, "OKTA-ADDR");
        var account = AccountData.createAccount(TEST_ACCOUNT_ID, "OKTA-USER-JOHN", "OKTA-ADDR");
        accounts.put(account.getAccountId(), account);
        users.put(user.getContactId(), user);
    }
}
