package com.subskribe.billy.fixtures;

import com.subskribe.billy.account.model.Account;
import com.subskribe.billy.account.model.AccountAddress;
import com.subskribe.billy.account.model.AccountContact;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.shared.AutoGenerate;
import com.subskribe.billy.shared.enums.Status;
import java.time.Instant;
import java.util.Currency;

public class AccountData {

    public static AccountContact createAccountUser(String userId, String accountId, String addressId) {
        return new AccountContact(
            AutoGenerate.getNewUuid(),
            userId,
            accountId,
            null,
            "First Name",
            "Last Name",
            "<EMAIL>",
            true,
            "**********",
            "title",
            addressId,
            createAccountAddress(addressId),
            Status.ACTIVE,
            Instant.now(),
            Instant.now()
        );
    }

    public static AccountAddress createAccountAddress(String addressId) {
        return new AccountAddress(
            AutoGenerate.getNewUuid(),
            addressId,
            "street address line 1",
            "street address line 2",
            "Maui",
            "Hawaii",
            "USA",
            "34223",
            Instant.now(),
            Instant.now()
        );
    }

    public static Account createAccount(String accountId, String userId, String addressId) {
        Account account = new Account();
        account.setId(AutoGenerate.getNewUuid());
        account.setAccountId(accountId);
        account.setEntityIds(EntityFixture.ALL_ENTITY_IDS);
        account.setName("Account Name");
        account.setDescription("test account");
        account.setPrimaryContactId(userId);
        account.setPhoneNumber("**********");
        account.setAddressId(addressId);
        account.setContacts(null);
        account.setCrmId(null);
        account.setCrmType(null);
        account.setCurrency(Currency.getInstance("USD"));
        account.setCreatedOn(Instant.now());
        account.setUpdatedOn(Instant.now());
        return account;
    }
}
