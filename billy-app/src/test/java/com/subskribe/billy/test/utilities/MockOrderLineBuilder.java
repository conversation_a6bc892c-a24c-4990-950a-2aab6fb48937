package com.subskribe.billy.test.utilities;

import com.subskribe.billy.order.model.OrderLineItem;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

public class MockOrderLineBuilder {

    private final OrderLineItem orderLineItem;

    public MockOrderLineBuilder() {
        orderLineItem = new OrderLineItem();
        orderLineItem.setEffectiveDate(Instant.now());
        orderLineItem.setQuantity(1);
        orderLineItem.setAmount(BigDecimal.valueOf(1000));
        orderLineItem.setOrderLineId(UUID.randomUUID().toString());
        orderLineItem.setPlanId(UUID.randomUUID().toString());
        orderLineItem.setChargeId(UUID.randomUUID().toString());
    }

    public MockOrderLineBuilder withAmount(BigDecimal amount) {
        orderLineItem.setAmount(amount);
        return this;
    }

    public MockOrderLineBuilder withQuantity(Long quantity) {
        orderLineItem.setQuantity(quantity);
        return this;
    }

    public MockOrderLineBuilder withIsRamp(Boolean isRamp) {
        orderLineItem.setIsRamp(isRamp);
        return this;
    }

    public MockOrderLineBuilder withEffectiveDate(Instant effectiveDate) {
        orderLineItem.setEffectiveDate(effectiveDate);
        return this;
    }

    public MockOrderLineBuilder withEndDate(Instant endDate) {
        orderLineItem.setEndDate(endDate);
        return this;
    }

    public MockOrderLineBuilder withPlanId(String planId) {
        orderLineItem.setPlanId(planId);
        return this;
    }

    public MockOrderLineBuilder withChargeId(String chargeId) {
        orderLineItem.setChargeId(chargeId);
        return this;
    }

    public OrderLineItem build() {
        return orderLineItem;
    }
}
