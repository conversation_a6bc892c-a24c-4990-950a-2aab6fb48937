package com.subskribe.billy.test.jooq;

import static java.util.Objects.requireNonNull;

import org.jooq.DSLContext;
import org.jooq.Query;
import org.jooq.Record;
import org.jooq.SQLDialect;
import org.jooq.TableLike;
import org.jooq.impl.DSL;
import org.jooq.tools.jdbc.MockConnection;

/**
 * This class is used to provide access and ease building a mocked {@link DSLContext} that
 * you can use in your unit tests.
 * Copied from <a href="https://github.com/SoylentBob/jooq-mock">this repo</a> (Apache License 2.0)
 * and modified slightly
 */
public class MockDSLBuilder {

    private final SQLDialect dialect;
    private final MockDSLDataProvider dataProvider;

    /**
     * Creates a new instance.
     */
    public MockDSLBuilder() {
        this(SQLDialect.POSTGRES);
    }

    /**
     * Creates a new instance.
     *
     * @param dialect The {@link SQLDialect} that should be used by the resulting {@link DSLContext}.
     *                This parameter may not be null.
     */
    public MockDSLBuilder(SQLDialect dialect) {
        this.dialect = requireNonNull(dialect, "The dialect may not be null");
        dataProvider = new MockDSLDataProvider(dialect);
    }

    public MockDSLBuilderWhen when(Query query) {
        return new MockDSLBuilderWhen(query, dataProvider, this);
    }

    public <Q extends Query & TableLike<R>, R extends Record> MockDSLBuilderWhenTableLike<Q, R> whenSelectedFieldsQuery(Q query) {
        return new MockDSLBuilderWhenTableLike<>(query, dataProvider, this);
    }

    /**
     * Builds the prepared {@link DSLContext}.
     *
     * @return The {@link DSLContext}
     */
    public DSLContext context() {
        return DSL.using(new MockConnection(dataProvider), dialect);
    }
}
