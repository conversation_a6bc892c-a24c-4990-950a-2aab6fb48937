package com.subskribe.billy.test;

import com.subskribe.billy.test.WithDb.DbMigrator;
import java.io.File;
import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.Location;
import org.flywaydb.core.api.resource.LoadableResource;

public abstract class DBMigratorBase extends BillyTestBase implements DbMigrator {

    protected final Path tempDir;
    protected final Class<? extends WithDb> testClass;

    private static final Map<String, String> PLACEHOLDERS = Map.of("billyAuditOn", "true", "entityRls", "false");

    public DBMigratorBase(Path tempDir, Class<? extends WithDb> testClass) {
        this.tempDir = tempDir;
        this.testClass = testClass;
    }

    protected abstract void doMigrate() throws Exception;

    @Override
    public void migrate(String jdbcUrl, Class<? extends WithDb> testClass) throws Exception {
        List<FlywayLoadableResource> initScripts = new LinkedList<>(loadSchema());
        initScripts.addAll(loadTestData(tempDir, testClass));
        Path root = Files.createDirectories(tempDir.resolve("migration"));
        for (FlywayLoadableResource res : initScripts) {
            Files.writeString(root.resolve(res.getFilename()), res.getSql(), StandardOpenOption.CREATE);
        }
        Flyway flyway = Flyway.configure()
            .dataSource(jdbcUrl, userName(), password())
            .locations(new Location("filesystem:" + root))
            .placeholders(PLACEHOLDERS)
            .load();

        flyway.migrate();
    }

    protected abstract String userName();

    protected abstract String password();

    private List<FlywayLoadableResource> loadSchema() throws IOException {
        List<FlywayLoadableResource> schemas = new LinkedList<>();
        URL url = ClassLoader.getSystemResource("db/migration");
        File[] scripts = new File(url.getPath()).listFiles();
        if (scripts == null) {
            throw new IllegalStateException("Was not able to load migration scripts.");
        }
        Arrays.sort(scripts);
        for (File sqlFile : scripts) {
            FlywayLoadableResource res = makeLoadableResource(sqlFile);
            if (StringUtils.isNotEmpty(res.getSql())) {
                schemas.add(res);
            }
        }
        return schemas;
    }

    abstract FlywayLoadableResource makeLoadableResource(File sqlFile) throws IOException;

    protected List<FlywayLoadableResource> loadTestData(Path tempDir, Class<? extends WithDb> testClass) throws IOException, URISyntaxException {
        List<FlywayLoadableResource> afterMigrate = new LinkedList<>();
        // Load common test setup data
        afterMigrate.add(new FlywayLoadableResource(tempDir, ClassLoader.getSystemResource("com/subskribe/billy/test/dbinit_common.sql").toURI(), 1));
        // Load test-specific data
        URL testInitScript = testClass.getResource(testClass.getSimpleName().replace("Test", "Res") + "/" + "dbinit.sql");
        if (testInitScript != null) {
            afterMigrate.add(new FlywayLoadableResource(tempDir, testInitScript.toURI(), 2));
        }
        return afterMigrate;
    }

    protected static class FlywayLoadableResource extends LoadableResource {

        protected final File res;
        protected final String sql;

        public FlywayLoadableResource(File res) throws IOException {
            this.res = res;
            sql = sqlFromFile(res);
        }

        protected String sqlFromFile(File res) throws IOException {
            return Files.readString(res.toPath());
        }

        public FlywayLoadableResource(Path tempDir, URI uri, int index) throws IOException {
            sql = Files.readString(Paths.get(uri));
            res = tempDir.resolve("afterMigrate__" + index + ".sql").toFile();
        }

        public String getSql() {
            return sql;
        }

        @Override
        public Reader read() {
            return new StringReader(sql);
        }

        @Override
        public String getAbsolutePath() {
            return res.getAbsolutePath();
        }

        @Override
        public String getAbsolutePathOnDisk() {
            return res.getAbsolutePath();
        }

        @Override
        public String getFilename() {
            return res.getName();
        }

        @Override
        public String getRelativePath() {
            return res.getName();
        }
    }
}
