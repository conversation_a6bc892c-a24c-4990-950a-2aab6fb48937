package com.subskribe.billy.test.utilities;

import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PriceTier;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.temporal.Recurrence;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.RandomStringUtils;

public class MockChargeBuilder {

    private final Charge charge;

    public MockChargeBuilder() {
        charge = new Charge();
        charge.setId(UUID.randomUUID());
        charge.setEntityIds(EntityFixture.ALL_ENTITY_IDS);
        charge.setChargeId(String.format("CHRG-%s", RandomStringUtils.randomAlphanumeric(7).toUpperCase()));
        charge.setName(UUID.randomUUID().toString());
        charge.setPlanUuid(UUID.randomUUID());
        charge.setChargeModel(ChargeModel.PER_UNIT);
        charge.setType(ChargeType.RECURRING);
        charge.setRecurrence(new Recurrence(Cycle.MONTH, 1));
        charge.setAmount(BigDecimal.valueOf(100));
        charge.setBillingCycle(BillingCycle.DEFAULT);
    }

    public MockChargeBuilder withChargeId(String chargeId) {
        charge.setChargeId(chargeId);
        return this;
    }

    public MockChargeBuilder withPlanId(UUID planId) {
        charge.setPlanUuid(planId);
        return this;
    }

    public MockChargeBuilder withName(String name) {
        charge.setName(name);
        return this;
    }

    public MockChargeBuilder recurring() {
        charge.setType(ChargeType.RECURRING);
        charge.setShouldTrackArr(true);
        return this;
    }

    public MockChargeBuilder recurring(Recurrence recurrence) {
        charge.setType(ChargeType.RECURRING);
        charge.setRecurrence(recurrence);
        charge.setShouldTrackArr(true);
        return this;
    }

    public MockChargeBuilder percentOf(BigDecimal percent) {
        charge.setType(ChargeType.PERCENTAGE_OF);
        charge.setRecurrence(null);
        charge.setAmount(null);
        charge.setPercent(percent);
        charge.setShouldTrackArr(true);
        return this;
    }

    public MockChargeBuilder usage() {
        charge.setType(ChargeType.USAGE);
        return this;
    }

    public MockChargeBuilder prepaid(Recurrence recurrence) {
        charge.setType(ChargeType.PREPAID);
        charge.setRecurrence(recurrence);
        charge.setShouldTrackArr(true);
        return this;
    }

    public MockChargeBuilder oneTime() {
        charge.setType(ChargeType.ONE_TIME);
        charge.setRecurrence(null);
        charge.setShouldTrackArr(false);
        return this;
    }

    public MockChargeBuilder withChargeType(ChargeType chargeType) {
        charge.setType(chargeType);
        return this;
    }

    public MockChargeBuilder monthly() {
        charge.setRecurrence(new Recurrence(Cycle.MONTH, 1));
        return this;
    }

    public MockChargeBuilder yearly() {
        charge.setRecurrence(new Recurrence(Cycle.YEAR, 1));
        return this;
    }

    public MockChargeBuilder flatFee() {
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        return this;
    }

    public MockChargeBuilder withChargeModel(ChargeModel chargeModel) {
        charge.setChargeModel(chargeModel);
        return this;
    }

    public MockChargeBuilder withPriceTiers(List<PriceTier> priceTiers) {
        charge.setPriceTiers(priceTiers);
        return this;
    }

    public MockChargeBuilder withAmount(BigDecimal amount) {
        charge.setAmount(amount);
        return this;
    }

    public MockChargeBuilder withRecognitionRuleId(String recognitionRuleId) {
        charge.setRecognitionRuleId(recognitionRuleId);
        return this;
    }

    public MockChargeBuilder withBillingCycle(BillingCycle billingCycle) {
        charge.setBillingCycle(billingCycle);
        return this;
    }

    public MockChargeBuilder shouldTrackArr(boolean shouldTrackArr) {
        charge.setShouldTrackArr(shouldTrackArr);
        return this;
    }

    public MockChargeBuilder withErpId(String erpId) {
        charge.setErpId(erpId);
        return this;
    }

    public Charge build() {
        return charge;
    }

    public static Map<String, Charge> getMockChargeMap(List<Plan> plans) {
        return plans.stream().map(Plan::getCharges).flatMap(List::stream).collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
    }
}
