package com.subskribe.billy.test;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.BillyConfiguration;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.di.hk2.providers.TenantCredentials;
import com.subskribe.billy.di.hk2.providers.TenantDbCredentialProvider;
import com.subskribe.billy.di.hk2.providers.auditlog.AuditLogConfiguration;
import com.subskribe.billy.jooq.QueryTimeoutConfiguration;
import com.subskribe.billy.shared.db.DBCPConfiguration;
import com.subskribe.billy.shared.db.DBCPFactory;
import io.dropwizard.setup.Environment;
import java.nio.file.Path;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.jooq.DSLContext;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.io.TempDir;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.testcontainers.containers.PostgreSQLContainer;

@SuppressWarnings("rawtypes")
@TestInstance(Lifecycle.PER_CLASS)
@Execution(ExecutionMode.SAME_THREAD)
public abstract class WithDb extends BillyTestBase {

    private DBMigratorBase migrator;

    public interface DbMigrator {
        void setup() throws Exception;

        void migrate(String jdbcUrl, Class<? extends WithDb> testClass) throws Exception;

        void cleanup() throws Exception;
    }

    private static final BillyConfiguration MOCK_BILLY_CONFIG = mock(BillyConfiguration.class);
    private static final TenantDbCredentialProvider MOCK_DB_CREDENTIAL_PROVIDER = mock(TenantDbCredentialProvider.class);

    protected DSLContextProvider dslContextProvider;

    @SuppressWarnings("resource")
    @BeforeAll
    public void initDbTestBase(@TempDir Path tempDir) throws Exception {
        Environment env = new Environment("TestWithDb");

        PostgreSQLContainer postgreSQLContainer = new PostgreSQLContainer<>("postgres:16.4").withDatabaseName("postgres");
        postgreSQLContainer.start();
        String jdbcUrl = postgreSQLContainer.getJdbcUrl();
        migrator = new PostgresMigrator(
            tempDir,
            jdbcUrl,
            getClass(),
            getClass().getSimpleName(),
            postgreSQLContainer.getUsername(),
            postgreSQLContainer.getPassword()
        );
        migrator.setup();
        migrator.doMigrate();
        DBCPFactory factory = getDbcpFactory(jdbcUrl, postgreSQLContainer, false);
        DBCPFactory readOnlyFactory = getDbcpFactory(jdbcUrl, postgreSQLContainer, true);

        QueryTimeoutConfiguration queryTimeoutConfiguration = new QueryTimeoutConfiguration();
        queryTimeoutConfiguration.setEnabled(true);
        queryTimeoutConfiguration.setQueryTimeoutInSeconds(2);

        AuditLogConfiguration auditLogConfiguration = new AuditLogConfiguration();
        auditLogConfiguration.setAlwaysSetSessionParams(false);

        when(MOCK_BILLY_CONFIG.getDataSourceFactory()).thenReturn(factory);
        when(MOCK_BILLY_CONFIG.getReadOnlyDataSourceFactory()).thenReturn(readOnlyFactory);
        when(MOCK_BILLY_CONFIG.getQueryTimeoutConfiguration()).thenReturn(queryTimeoutConfiguration);
        when(MOCK_BILLY_CONFIG.getAuditLogConfiguration()).thenReturn(auditLogConfiguration);
        when(MOCK_DB_CREDENTIAL_PROVIDER.getCredentialsForTenant(anyString(), any(DSLContext.class))).thenReturn(
            new TenantCredentials(postgreSQLContainer.getUsername(), postgreSQLContainer.getPassword())
        );
        dslContextProvider = new DSLContextProvider(env, MOCK_BILLY_CONFIG, getClass().getSimpleName(), MOCK_DB_CREDENTIAL_PROVIDER);
    }

    private static DBCPFactory getDbcpFactory(String jdbcUrl, PostgreSQLContainer postgreSQLContainer, boolean readOnly) {
        DBCPFactory factory = new DBCPFactory();
        factory.setDbcpConfiguration(getDbcpConfiguration(jdbcUrl, postgreSQLContainer, readOnly));
        return factory;
    }

    @NotNull
    private static DBCPConfiguration getDbcpConfiguration(String jdbcUrl, PostgreSQLContainer postgreSQLContainer, boolean readOnly) {
        DBCPConfiguration dbcpConfiguration = new DBCPConfiguration();
        dbcpConfiguration.setUrl(jdbcUrl);
        dbcpConfiguration.setUsername(postgreSQLContainer.getUsername());
        dbcpConfiguration.setPassword(postgreSQLContainer.getPassword());
        dbcpConfiguration.setDriverClassName("org.postgresql.Driver");
        dbcpConfiguration.setValidationQuery("SELECT 1");
        dbcpConfiguration.setConnectionProperties(Map.of("charSet", "UTF-8"));
        dbcpConfiguration.setTestWhileIdle(true);
        dbcpConfiguration.setTestOnBorrow(true);
        dbcpConfiguration.setTimeBetweenEvictionRunsMillis(10000L);
        dbcpConfiguration.setMinEvictableIdleTimeMillis(30000);
        dbcpConfiguration.setValidationQueryTimeout(3);
        dbcpConfiguration.setMaxPerUser(30);
        dbcpConfiguration.setMaxTotalActive(30);
        dbcpConfiguration.setMaxPerUserIdle(30);
        dbcpConfiguration.setMinPerUserIdle(0);
        dbcpConfiguration.setReadOnly(readOnly);
        return dbcpConfiguration;
    }

    @AfterAll
    public void shutdownBase() throws Exception {
        // Managed data source needs to be stopped
        dslContextProvider.stop();
        migrator.cleanup();
    }
}
