package com.subskribe.billy.test.utilities;

import com.subskribe.billy.revrec.model.RecognitionRule;
import com.subskribe.billy.shared.enums.RecognitionDateAlignment;
import com.subskribe.billy.shared.enums.RecognitionDistributionMethod;
import com.subskribe.billy.shared.enums.RecognitionSource;
import com.subskribe.billy.shared.enums.RecognitionType;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;

public class MockRecognitionRuleBuilder {

    private final RecognitionRule recognitionRule;

    public MockRecognitionRuleBuilder() {
        recognitionRule = new RecognitionRule();
        recognitionRule.setId(UUID.randomUUID());
        recognitionRule.setRuleId(String.format("RCRL-%s", RandomStringUtils.randomAlphanumeric(7).toUpperCase()));
        recognitionRule.setName(UUID.randomUUID().toString());
        recognitionRule.setSource(RecognitionSource.ORDER);
        recognitionRule.setRecognitionType(RecognitionType.OVER_TIME);
        recognitionRule.setDistributionMethod(RecognitionDistributionMethod.MONTHS_PARTIAL_PRORATED);
        recognitionRule.setCatchupRequired(true);
    }

    public MockRecognitionRuleBuilder withRuleId(String ruleId) {
        recognitionRule.setRuleId(ruleId);
        return this;
    }

    public MockRecognitionRuleBuilder withSource(RecognitionSource recognitionSource) {
        recognitionRule.setSource(recognitionSource);
        return this;
    }

    public MockRecognitionRuleBuilder partialProrated() {
        recognitionRule.setRecognitionType(RecognitionType.OVER_TIME);
        recognitionRule.setDistributionMethod(RecognitionDistributionMethod.MONTHS_PARTIAL_PRORATED);
        return this;
    }

    public MockRecognitionRuleBuilder daysProrated() {
        recognitionRule.setRecognitionType(RecognitionType.OVER_TIME);
        recognitionRule.setDistributionMethod(RecognitionDistributionMethod.DAYS);
        return this;
    }

    @Deprecated
    public MockRecognitionRuleBuilder monthsEven() {
        recognitionRule.setRecognitionType(RecognitionType.OVER_TIME);
        recognitionRule.setDistributionMethod(RecognitionDistributionMethod.MONTHS_EVEN);
        return this;
    }

    public MockRecognitionRuleBuilder pointInTime() {
        recognitionRule.setRecognitionType(RecognitionType.POINT_IN_TIME);
        recognitionRule.setDistributionMethod(null);
        if (recognitionRule.getRecognitionDateAlignment() == null) {
            recognitionRule.setRecognitionDateAlignment(RecognitionDateAlignment.INVOICE_START_DATE);
        }
        return this;
    }

    public MockRecognitionRuleBuilder withDateAlignment(RecognitionDateAlignment recognitionDateAlignment) {
        recognitionRule.setRecognitionDateAlignment(recognitionDateAlignment);
        return this;
    }

    public RecognitionRule build() {
        return recognitionRule;
    }
}
