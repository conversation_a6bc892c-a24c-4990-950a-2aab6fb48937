package com.subskribe.billy.test;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Objects;
import org.junit.jupiter.api.TestInfo;

@SuppressWarnings("unused")
public abstract class BillyTestBase {

    private static final ObjectMapper MAPPER = JacksonProvider.emptyFieldExcludingMapper()
        .copy()
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);

    protected String asString(Class<?> testClass, String resource) throws IOException, URISyntaxException {
        return Files.readString(Paths.get(Objects.requireNonNull(testClass.getResource(resource)).toURI()));
    }

    protected JsonNode asJson(Class<?> testClass, String resource) throws IOException {
        return MAPPER.readTree(testClass.getResource(resource));
    }

    protected <T> T asType(Class<?> testClass, String resource, TypeReference<T> type) throws IOException, URISyntaxException {
        String res = testClass.getSimpleName().replace("Test", "Res") + "/" + resource;
        return MAPPER.readValue(asString(testClass, res), type);
    }

    protected String asString(TestInfo ti, Class<?> testClass, String resource) throws IOException, URISyntaxException {
        String res = ti.getTestClass().map(c -> c.getSimpleName().replace("Test", "Res") + "/" + resource).orElse(resource);
        return Files.readString(Paths.get(Objects.requireNonNull(testClass.getResource(res)).toURI()));
    }

    protected <T> T asInstance(TestInfo ti, Class<?> testClass, String resource, Class<T> cls) throws IOException, URISyntaxException {
        return MAPPER.readValue(asString(ti, testClass, resource), cls);
    }

    protected <T> T asType(TestInfo ti, Class<?> testClass, String resource, TypeReference<T> type) throws IOException, URISyntaxException {
        return MAPPER.readValue(asString(ti, testClass, resource), type);
    }
}
