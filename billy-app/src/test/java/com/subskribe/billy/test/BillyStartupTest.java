package com.subskribe.billy.test;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.BillyApplication;
import com.subskribe.billy.BillyConfiguration;
import io.dropwizard.testing.ConfigOverride;
import io.dropwizard.testing.junit5.DropwizardAppExtension;
import io.dropwizard.testing.junit5.DropwizardExtensionsSupport;
import javax.ws.rs.core.Response;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(DropwizardExtensionsSupport.class)
@Tag("integration")
public class BillyStartupTest {

    private static final String CONFIG_PATH = "config-local.conf";

    private static final DropwizardAppExtension<BillyConfiguration> EXT = new DropwizardAppExtension<>(
        BillyApplication.class,
        CONFIG_PATH,
        ConfigOverride.config("server.applicationConnectors[0].port", "0"),
        ConfigOverride.config("server.adminConnectors[0].port", "0")
    );

    @Test
    public void testUnauthorizedAccessToApi() {
        String uri = String.format("http://localhost:%d/products", EXT.getLocalPort());
        Response response = EXT.client().target(uri).request().get();
        assertThat(response.getStatus()).isEqualTo(Response.Status.UNAUTHORIZED.getStatusCode());
    }
}
