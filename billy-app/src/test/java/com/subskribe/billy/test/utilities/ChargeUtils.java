package com.subskribe.billy.test.utilities;

import com.subskribe.billy.productcatalog.model.Charge;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ChargeUtils {

    public static Map<String, Charge> getChargeMapFromCharges(List<Charge> charges) {
        return charges.stream().collect(Collectors.toMap(Charge::getChargeId, Function.identity()));
    }
}
