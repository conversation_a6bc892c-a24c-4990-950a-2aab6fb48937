package com.subskribe.billy.test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

public class PostgresMigrator extends DBMigratorBase {

    private final String postgresUrl;
    private final String dbName;
    private final String userName;
    private final String password;

    public PostgresMigrator(Path tempDir, String postgresUrl, Class<? extends WithDb> testClass, String dbName, String userName, String password) {
        super(tempDir, testClass);
        this.postgresUrl = postgresUrl;
        this.dbName = dbName;
        this.userName = userName;
        this.password = password;
    }

    @Override
    protected void doMigrate() throws Exception {
        super.migrate(postgresUrl + dbName, testClass);
    }

    @Override
    protected String userName() {
        return userName;
    }

    @Override
    protected String password() {
        return password;
    }

    @Override
    FlywayLoadableResource makeLoadableResource(File sqlFile) throws IOException {
        return new FlywayLoadableResource(sqlFile);
    }

    @Override
    public void setup() throws SQLException {
        try (Connection c = DriverManager.getConnection(postgresUrl, userName, password)) {
            try (Statement st = c.createStatement()) {
                st.addBatch("DROP DATABASE IF EXISTS \"" + dbName + "\"");
                st.addBatch("CREATE DATABASE \"" + dbName + "\"");
                st.executeBatch();
            }
        }
    }

    @Override
    public void cleanup() throws SQLException {
        try (Connection c = DriverManager.getConnection(postgresUrl, userName(), password())) {
            try (Statement statement = c.createStatement()) {
                statement.executeUpdate("DROP DATABASE IF EXISTS \"" + dbName + "\"");
            }
        }
    }
}
