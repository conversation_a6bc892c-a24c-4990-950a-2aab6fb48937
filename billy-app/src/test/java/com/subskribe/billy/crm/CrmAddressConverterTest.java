package com.subskribe.billy.crm;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class CrmAddressConverterTest {

    private static final String[] USA_WORK = {
        "USA",
        "United States of America",
        "united STATES",
        "The United States",
        "The United States of America",
    };
    private static final String[] UK_WORK = { "United Kingdom", "Great Britain", "THE united KINGDOM" };

    @Test
    public void convertUSA() {
        for (String workingUsaString : USA_WORK) {
            Assertions.assertEquals("US", CrmAddressConverter.getCountryCode(workingUsaString));
        }
    }

    @Test
    public void convertUK() {
        for (String workingUkString : UK_WORK) {
            Assertions.assertEquals("GB", CrmAddressConverter.getCountryCode(workingUkString));
        }
    }

    @Test
    public void convertRepublicOfKorea() {
        Assertions.assertEquals("KR", CrmAddressConverter.getCountryCode("Republic of Korea"));
        Assertions.assertEquals("KR", CrmAddressConverter.getCountryCode("Korea, Republic of"));
    }
}
