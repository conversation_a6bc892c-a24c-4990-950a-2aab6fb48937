package com.subskribe.billy.exception.handling;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.shared.enums.MessageLevel;
import java.time.Duration;
import java.util.concurrent.Callable;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class ExceptionHandlingStrategyTest {

    private static final Duration TEST_SLEEP_DURATION = Duration.ofMillis(100);

    private static final int TEST_ATTEMPT_COUNT = 3;

    private static final MessageLevel TEST_MESSAGE_LEVEL = MessageLevel.ERROR;

    private static final RuntimeException[] EXCEPTIONS_THROWN_THRICE = {
        new RuntimeException("Whoops1!!"),
        new RuntimeException("Whoops2!!"),
        new RuntimeException("Whoops3!!"),
    };

    private static final RuntimeException[] EXCEPTIONS_THROWN_TWICE = { new RuntimeException("Whoops1!!"), new RuntimeException("Whoops2!!") };

    @Mock
    Runnable mockRunnable;

    @Mock
    Callable<String> mockCallable;

    ExceptionHandlingStrategy tryForeverExceptionHandlingStrategy;

    ExceptionHandlingStrategy fixedRetryCountExceptionHandlingStrategy;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        tryForeverExceptionHandlingStrategy = TryForeverExceptionHandlingStrategy.using(TEST_SLEEP_DURATION, TEST_MESSAGE_LEVEL);
        fixedRetryCountExceptionHandlingStrategy = FixedRetryCountExceptionHandlingStrategy.using(
            TEST_ATTEMPT_COUNT,
            TEST_SLEEP_DURATION,
            TEST_MESSAGE_LEVEL
        );
    }

    @Test
    public void whenTryForEverStrategyIsUsed_thenIndeedTheTrialHappensUntilCallSucceeds() throws Exception {
        doThrow(EXCEPTIONS_THROWN_THRICE).doAnswer(invocation -> null).when(mockRunnable).run();

        tryForeverExceptionHandlingStrategy.handle(mockRunnable);
        verify(mockRunnable, times(4)).run();

        // now the callable
        when(mockCallable.call()).thenThrow(EXCEPTIONS_THROWN_THRICE).thenReturn("Foo!!");
        String retVal = tryForeverExceptionHandlingStrategy.handle(mockCallable);
        verify(mockCallable, times(4)).call();
        Assertions.assertThat(retVal).isEqualTo("Foo!!");
    }

    @Test
    public void whenFixRetryStrategyIsUsed_thenRetryHappensOnlyUntilThingsSucceed() throws Exception {
        doThrow(EXCEPTIONS_THROWN_TWICE).doAnswer(invocation -> null).when(mockRunnable).run();

        Assertions.assertThatCode(() -> fixedRetryCountExceptionHandlingStrategy.handle(mockRunnable)).doesNotThrowAnyException();
        verify(mockRunnable, times(3)).run();

        when(mockCallable.call()).thenThrow(EXCEPTIONS_THROWN_TWICE).thenReturn("Foo!!");
        Assertions.assertThatCode(() -> {
            String retVal = fixedRetryCountExceptionHandlingStrategy.handle(mockCallable);
            Assertions.assertThat(retVal).isEqualTo("Foo!!");
        }).doesNotThrowAnyException();
        verify(mockCallable, times(3)).call();
    }

    @Test
    public void whenFixRetryStrategyIsUsed_thenRetryHappensAndNeverSucceeds() throws Exception {
        doThrow(EXCEPTIONS_THROWN_THRICE).doAnswer(invocation -> null).when(mockRunnable).run();

        Assertions.assertThatCode(() -> fixedRetryCountExceptionHandlingStrategy.handle(mockRunnable))
            .hasCauseExactlyInstanceOf(RuntimeException.class)
            .hasMessageContaining("Whoops");
        verify(mockRunnable, times(3)).run();

        when(mockCallable.call()).thenThrow(EXCEPTIONS_THROWN_THRICE).thenReturn("Foo!!");

        Assertions.assertThatCode(() -> {
            String retVal = fixedRetryCountExceptionHandlingStrategy.handle(mockCallable);
            Assertions.assertThat(retVal).isEqualTo("Foo!!");
        })
            .hasCauseExactlyInstanceOf(RuntimeException.class)
            .hasMessageContaining("Whoops");

        verify(mockCallable, times(3)).call();
    }
}
