package com.subskribe.billy.filter.http;

import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.core.UriInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class LargeRequestLoggingFilterTest {

    private LargeRequestLoggingFilter filter;

    @BeforeEach
    void setUp() {
        filter = new LargeRequestLoggingFilter();
    }

    @Test
    void testSmallRequestsDoNotThrowExceptions() {
        ContainerRequestContext containerRequestContext = baseContext();
        when(containerRequestContext.getLength()).thenReturn(LargeRequestLoggingFilter.LOGGING_THRESHOLD - 1);

        assertThatNoException().isThrownBy(() -> filter.filter(containerRequestContext));
    }

    @Test
    void testLargeRequestsDoNotThrowExceptions() {
        ContainerRequestContext containerRequestContext = baseContext();
        when(containerRequestContext.getLength()).thenReturn(LargeRequestLoggingFilter.LOGGING_THRESHOLD + 1);

        assertThatNoException().isThrownBy(() -> filter.filter(containerRequestContext));
    }

    private ContainerRequestContext baseContext() {
        ContainerRequestContext containerRequestContext = mock(ContainerRequestContext.class);
        UriInfo uriInfo = mock(UriInfo.class);
        when(uriInfo.getPath()).thenReturn("/graphql");
        when(containerRequestContext.getUriInfo()).thenReturn(uriInfo);
        return containerRequestContext;
    }
}
