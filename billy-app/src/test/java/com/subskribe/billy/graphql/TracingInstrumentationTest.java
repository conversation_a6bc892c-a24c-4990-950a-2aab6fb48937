package com.subskribe.billy.graphql;

import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.subskribe.billy.auth.model.BillyAuthPrincipal;
import datadog.trace.api.DDTags;
import graphql.ErrorType;
import graphql.ExecutionResult;
import graphql.GraphQLError;
import graphql.GraphqlErrorBuilder;
import graphql.execution.instrumentation.InstrumentationContext;
import graphql.execution.instrumentation.parameters.InstrumentationExecutionParameters;
import io.opentracing.Scope;
import io.opentracing.Span;
import io.opentracing.Tracer;
import io.opentracing.log.Fields;
import io.opentracing.tag.Tag;
import io.opentracing.tag.Tags;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatcher;
import org.slf4j.MDC;

class TracingInstrumentationTest {

    private TracingInstrumentation instrumentation;
    private Tracer tracer;

    @BeforeEach
    void setUp() {
        tracer = mock(Tracer.class);
        instrumentation = new TracingInstrumentation(() -> tracer);
    }

    @Test
    void noInstrumentationWhenOperationNameIsNull() {
        InstrumentationExecutionParameters parameters = stubParameters(null);
        instrumentation.beginExecution(parameters);

        verifyNoInteractions(tracer);
    }

    @Test
    void noInstrumentationWhenOperationNameIsEmpty() {
        InstrumentationExecutionParameters parameters = stubParameters(StringUtils.EMPTY);
        instrumentation.beginExecution(parameters);

        verifyNoInteractions(tracer);
    }

    @Test
    void instrumentationPerformedWithNoErrors() {
        String resource = RandomStringUtils.randomAlphabetic(10);
        String tenantId = UUID.randomUUID().toString();
        MDC.put(BillyAuthPrincipal.TENANT_ID_LOG_FIELD, tenantId);
        InstrumentationExecutionParameters parameters = stubParameters(resource);

        Tracer.SpanBuilder builder = mock(Tracer.SpanBuilder.class);
        Span span = mock(Span.class);
        Scope scope = mock(Scope.class);
        when(tracer.buildSpan(TracingInstrumentation.OPERATION_NAME)).thenReturn(builder);
        when(builder.withTag(DDTags.SERVICE_NAME, TracingInstrumentation.GQL_SERVICE_NAME)).thenReturn(builder);
        when(builder.withTag(DDTags.RESOURCE_NAME, resource)).thenReturn(builder);
        when(builder.start()).thenReturn(span);
        when(tracer.activateSpan(span)).thenReturn(scope);

        InstrumentationContext<ExecutionResult> result = instrumentation.beginExecution(parameters);
        result.onCompleted(mock(ExecutionResult.class), null);

        verify(builder, times(1)).start();
        verify(tracer, times(1)).activateSpan(span);
        verify(span, times(1)).finish();
        verify(span, times(1)).setTag(BillyAuthPrincipal.TENANT_ID_LOG_FIELD, tenantId);
        verify(scope, times(1)).close();
    }

    @Test
    void instrumentationPerformedWithThrowable() {
        Throwable toThrow = new RuntimeException("test exception");
        String resource = RandomStringUtils.randomAlphabetic(10);
        String tenantId = UUID.randomUUID().toString();
        MDC.put(BillyAuthPrincipal.TENANT_ID_LOG_FIELD, tenantId);
        InstrumentationExecutionParameters parameters = stubParameters(resource);

        Tracer.SpanBuilder builder = mock(Tracer.SpanBuilder.class);
        Scope scope = mock(Scope.class);
        Span span = mock(Span.class);
        when(tracer.buildSpan(TracingInstrumentation.OPERATION_NAME)).thenReturn(builder);
        when(builder.withTag(DDTags.SERVICE_NAME, TracingInstrumentation.GQL_SERVICE_NAME)).thenReturn(builder);
        when(builder.withTag(DDTags.RESOURCE_NAME, resource)).thenReturn(builder);
        when(builder.start()).thenReturn(span);
        when(tracer.activateSpan(span)).thenReturn(scope);

        InstrumentationContext<ExecutionResult> result = instrumentation.beginExecution(parameters);
        result.onCompleted(mock(ExecutionResult.class), toThrow);

        verify(builder, times(1)).start();
        verify(tracer, times(1)).activateSpan(span);
        verify(span, times(1)).finish();
        verify(scope, times(1)).close();
        verify(span, times(1)).setTag(BillyAuthPrincipal.TENANT_ID_LOG_FIELD, tenantId);
        verify(span, times(1)).setTag(
            argThat((ArgumentMatcher<Tag<Boolean>>) s -> s.getKey().equals(Tags.ERROR.getKey())), // BooleanTag does not implement equals()
            eq(true)
        );
        verify(span, times(1)).log(Map.of(Fields.ERROR_OBJECT, toThrow));
    }

    @Test
    void instrumentationPerformedWithGraphqlErrors() {
        String resource = RandomStringUtils.randomAlphabetic(10);
        String tenantId = UUID.randomUUID().toString();
        MDC.put(BillyAuthPrincipal.TENANT_ID_LOG_FIELD, tenantId);
        InstrumentationExecutionParameters parameters = stubParameters(resource);

        Tracer.SpanBuilder builder = mock(Tracer.SpanBuilder.class);
        Scope scope = mock(Scope.class);
        Span span = mock(Span.class);
        when(tracer.buildSpan(TracingInstrumentation.OPERATION_NAME)).thenReturn(builder);
        when(builder.withTag(DDTags.SERVICE_NAME, TracingInstrumentation.GQL_SERVICE_NAME)).thenReturn(builder);
        when(builder.withTag(DDTags.RESOURCE_NAME, resource)).thenReturn(builder);
        when(builder.start()).thenReturn(span);
        when(tracer.activateSpan(span)).thenReturn(scope);

        GraphQLError error1 = GraphqlErrorBuilder.newError().errorType(ErrorType.ValidationError).message("test message 1").build();
        GraphQLError error2 = GraphqlErrorBuilder.newError().errorType(ErrorType.OperationNotSupported).message("test message 2").build();
        ExecutionResult executionResult = mock(ExecutionResult.class);
        when(executionResult.getErrors()).thenReturn(List.of(error1, error2));

        InstrumentationContext<ExecutionResult> result = instrumentation.beginExecution(parameters);
        result.onCompleted(executionResult, null);

        verify(builder, times(1)).start();
        verify(tracer, times(1)).activateSpan(span);
        verify(span, times(1)).finish();
        verify(scope, times(1)).close();
        verify(span, times(1)).setTag(BillyAuthPrincipal.TENANT_ID_LOG_FIELD, tenantId);
        verify(span, times(1)).setTag(
            argThat((ArgumentMatcher<Tag<Boolean>>) s -> s.getKey().equals(Tags.ERROR.getKey())), // BooleanTag does not implement equals()
            eq(true)
        );
        verify(span, times(1)).setTag("graphql.error.0", "Graphql error: [ValidationError] test message 1");
        verify(span, times(1)).setTag("graphql.error.1", "Graphql error: [OperationNotSupported] test message 2");
    }

    private InstrumentationExecutionParameters stubParameters(String operationName) {
        InstrumentationExecutionParameters input = mock(InstrumentationExecutionParameters.class);
        when(input.getOperation()).thenReturn(operationName);
        return input;
    }
}
