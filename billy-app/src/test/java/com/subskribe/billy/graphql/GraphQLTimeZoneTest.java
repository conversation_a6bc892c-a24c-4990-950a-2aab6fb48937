package com.subskribe.billy.graphql;

import com.subskribe.billy.exception.InvalidInputException;
import graphql.language.StringValue;
import java.util.TimeZone;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class GraphQLTimeZoneTest {

    private static final String TIMEZONE_STRING_VALID = "America/Los_Angeles";
    private static final TimeZone TIMEZONE_OBJECT_VALID = TimeZone.getTimeZone(TIMEZONE_STRING_VALID);
    private static final String TIMEZONE_STRING_INVALID = "America/San_Francisco";

    @Test
    void testParseValue_Valid() {
        GraphQLTimeZoneCoercing graphQLTimeZoneCoercing = new GraphQLTimeZoneCoercing();
        Object parsedValue = graphQLTimeZoneCoercing.parseValue(TIMEZONE_STRING_VALID);
        Assertions.assertThat(parsedValue).isInstanceOf(TimeZone.class);
        Assertions.assertThat(parsedValue).isEqualTo(TIMEZONE_OBJECT_VALID);
    }

    @Test
    void testParseValue_Invalid() {
        GraphQLTimeZoneCoercing graphQLTimeZoneCoercing = new GraphQLTimeZoneCoercing();
        Assertions.assertThatThrownBy(() -> graphQLTimeZoneCoercing.parseValue(TIMEZONE_STRING_INVALID))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining(TIMEZONE_STRING_INVALID);
    }

    @Test
    void testParseLiteral_Valid() {
        GraphQLTimeZoneCoercing graphQLTimeZoneCoercing = new GraphQLTimeZoneCoercing();
        Object parsedValue = graphQLTimeZoneCoercing.parseLiteral(new StringValue(TIMEZONE_STRING_VALID));
        Assertions.assertThat(parsedValue).isInstanceOf(TimeZone.class);
        Assertions.assertThat(parsedValue).isEqualTo(TIMEZONE_OBJECT_VALID);
    }

    @Test
    void testParseLiteral_Invalid() {
        GraphQLTimeZoneCoercing graphQLTimeZoneCoercing = new GraphQLTimeZoneCoercing();
        Assertions.assertThatThrownBy(() -> graphQLTimeZoneCoercing.parseLiteral(new StringValue(TIMEZONE_STRING_INVALID)))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining(TIMEZONE_STRING_INVALID);
    }

    @Test
    void testSerialize() {
        GraphQLTimeZoneCoercing graphQLTimeZoneCoercing = new GraphQLTimeZoneCoercing();
        Object serializedValue = graphQLTimeZoneCoercing.serialize(TIMEZONE_OBJECT_VALID);
        Assertions.assertThat(serializedValue).isInstanceOf(String.class);
        Assertions.assertThat(serializedValue).isEqualTo(TIMEZONE_STRING_VALID);
    }
}
