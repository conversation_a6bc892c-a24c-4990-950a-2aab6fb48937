package com.subskribe.billy.productcatalog.formula;

import static com.subskribe.billy.productcatalog.formula.FormulaService.FormulaBinding.newBinding;

import com.subskribe.billy.exception.InvalidInputException;
import java.math.BigDecimal;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class Exp4jFormulaServiceTest {

    private final FormulaService formulaService = new Exp4jFormulaService();

    @Test
    public void testBasicExpressionsAreValid() {
        Assertions.assertThat(formulaService.validateFormula("2+3", newBinding())).isTrue();
        Assertions.assertThat(formulaService.evaluateFormula("2+3", newBinding())).isEqualTo(BigDecimal.valueOf(5.0));

        Assertions.assertThat(formulaService.validateFormula("a+log(10)", newBinding())).isFalse();
        Assertions.assertThatThrownBy(() -> formulaService.evaluateFormula("a + log(10)", newBinding()))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("Unknown function or variable");

        Assertions.assertThat(formulaService.validateFormula("a+log(10)", newBinding().add("a", BigDecimal.valueOf(10)))).isTrue();
        Assertions.assertThat(formulaService.evaluateFormula("a + log10(10)", newBinding().add("a", BigDecimal.valueOf(10)))).isEqualTo("11.0");

        FormulaService.FormulaBinding binding = newBinding().add("x", BigDecimal.valueOf(20)).add("y", BigDecimal.valueOf(5));
        Assertions.assertThat(formulaService.evaluateFormula("x*x + y*y +  log(x*y)", binding)).isEqualTo("429.6051701859881");
    }
}
