package com.subskribe.billy.productcatalog.ratecard.model;

import com.subskribe.billy.usage.model.UsageAggregationEqualityTest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class AttributeReferencesModelTest {

    @Test
    public void whenAttributeReferencesAreNotPresent_thenEqualityIsNotAffected() {
        AttributeReferences references = AttributeReferences.wrap(List.of());
        Assertions.assertThat(references).isNull();

        references = AttributeReferences.wrap(null);
        Assertions.assertThat(references).isNull();
    }

    @Test
    public void whenAttributeReferencesAreOutOfOrder_theyGetOrderedProperly() {
        AttributeReferences referencesOne = AttributeReferences.wrap(UsageAggregationEqualityTest.ORDER_ONE_ATTRIBUTE_REFERENCE);
        AttributeReferences referencesTwo = AttributeReferences.wrap(UsageAggregationEqualityTest.ORDER_TWO_ATTRIBUTE_REFERENCE);

        Assertions.assertThat(referencesOne).isNotNull();
        Assertions.assertThat(referencesTwo).isNotNull();

        Assertions.assertThat(referencesOne).isEqualTo(referencesTwo);
        Assertions.assertThat(referencesOne.getReferencesInOrder()).isEqualTo(referencesTwo.getReferencesInOrder());
    }

    @Test
    public void whenAttributeReferencesAreOutOfOrderShuffled_theyGetOrderedProperly() {
        List<AttributeReference> shuffledOne = new ArrayList<>(UsageAggregationEqualityTest.ORDER_ONE_ATTRIBUTE_REFERENCE);
        Collections.shuffle(shuffledOne);
        List<AttributeReference> shuffledTwo = new ArrayList<>(UsageAggregationEqualityTest.ORDER_TWO_ATTRIBUTE_REFERENCE);
        Collections.shuffle(shuffledTwo);
        AttributeReferences referencesOne = AttributeReferences.wrap(shuffledOne);
        AttributeReferences referencesTwo = AttributeReferences.wrap(shuffledTwo);

        Assertions.assertThat(referencesOne).isNotNull();
        Assertions.assertThat(referencesTwo).isNotNull();

        Assertions.assertThat(referencesOne).isEqualTo(referencesTwo);
        Assertions.assertThat(referencesOne.getReferencesInOrder()).isEqualTo(referencesTwo.getReferencesInOrder());
    }

    @Test
    public void whenAttributeReferencesAreOutOfOrderShuffledAndNotEqual_theirCanonicalComparisonWorks() {
        List<AttributeReference> shuffledOne = new ArrayList<>(UsageAggregationEqualityTest.ORDER_ONE_ATTRIBUTE_REFERENCE);
        Collections.shuffle(shuffledOne);
        List<AttributeReference> shuffledTwo = new ArrayList<>(UsageAggregationEqualityTest.ORDER_THREE_ATTRIBUTE_REFERENCE);
        Collections.shuffle(shuffledTwo);
        AttributeReferences referencesOne = AttributeReferences.wrap(shuffledOne);
        AttributeReferences referencesTwo = AttributeReferences.wrap(shuffledTwo);

        Assertions.assertThat(referencesOne).isNotNull();
        Assertions.assertThat(referencesTwo).isNotNull();

        Assertions.assertThat(referencesOne).isNotEqualTo(referencesTwo);
        Assertions.assertThat(referencesOne.getReferencesInOrder()).isNotEqualTo(referencesTwo.getReferencesInOrder());
    }
}
