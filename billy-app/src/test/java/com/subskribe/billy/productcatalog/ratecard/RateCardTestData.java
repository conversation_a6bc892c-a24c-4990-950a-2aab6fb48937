package com.subskribe.billy.productcatalog.ratecard;

import com.subskribe.billy.productcatalog.ratecard.model.AttributeReference;
import com.subskribe.billy.productcatalog.ratecard.model.AttributeReferences;
import com.subskribe.billy.productcatalog.ratecard.model.PriceTableElement;
import com.subskribe.billy.productcatalog.ratecard.model.RateCard;
import java.math.BigDecimal;
import java.util.List;

public class RateCardTestData {

    public static final String TEST_RATE_CARD_ID = "RCRD-123ABC1";
    public static final String TEST_RATE_CARD_NAME = "Sample rate card";
    public static final String TEST_RATE_CARD_DESCRIPTION = "Sample rate card description";
    public static final String TEST_RATE_CARD_CURRENCY = "USD";

    public static final String SERVICE_ATTRIBUTE_DEFINITION_ID = "PATTRB-123ABC1";
    public static final String TIER_ATTRIBUTE_DEFINITION_ID = "PATTRB-123ABC2";

    public static final String SERVICE_ATTRIBUTE_VALUE_S3 = "S3";
    public static final String SERVICE_ATTRIBUTE_VALUE_EC2 = "EC2";
    public static final String SERVICE_ATTRIBUTE_VALUE_DYNAMO = "Dynamo";

    public static final String TIER_ATTRIBUTE_VALUE_ONE = "Tier1";
    public static final String TIER_ATTRIBUTE_VALUE_TWO = "Tier2";
    public static final String TIER_ATTRIBUTE_VALUE_THREE = "Tier3";

    public static final AttributeReferences ATTRIBUTE_REFERENCES_COMBINATION_ONE = AttributeReferences.wrap(
        List.of(
            AttributeReference.builder().attributeDefinitionId(SERVICE_ATTRIBUTE_DEFINITION_ID).attributeValue(SERVICE_ATTRIBUTE_VALUE_S3).build(),
            AttributeReference.builder().attributeDefinitionId(TIER_ATTRIBUTE_DEFINITION_ID).attributeValue(TIER_ATTRIBUTE_VALUE_ONE).build()
        )
    );

    public static final AttributeReferences ATTRIBUTE_REFERENCES_COMBINATION_TWO = AttributeReferences.wrap(
        List.of(
            AttributeReference.builder().attributeDefinitionId(SERVICE_ATTRIBUTE_DEFINITION_ID).attributeValue(SERVICE_ATTRIBUTE_VALUE_EC2).build(),
            AttributeReference.builder().attributeDefinitionId(TIER_ATTRIBUTE_DEFINITION_ID).attributeValue(TIER_ATTRIBUTE_VALUE_TWO).build()
        )
    );

    public static final AttributeReferences ATTRIBUTE_REFERENCES_COMBINATION_THREE = AttributeReferences.wrap(
        List.of(
            AttributeReference.builder()
                .attributeDefinitionId(SERVICE_ATTRIBUTE_DEFINITION_ID)
                .attributeValue(SERVICE_ATTRIBUTE_VALUE_DYNAMO)
                .build(),
            AttributeReference.builder().attributeDefinitionId(TIER_ATTRIBUTE_DEFINITION_ID).attributeValue(TIER_ATTRIBUTE_VALUE_THREE).build()
        )
    );

    public static final AttributeReferences ATTRIBUTE_REFERENCES_COMBINATION_NOT_PRESENT = AttributeReferences.wrap(
        List.of(
            AttributeReference.builder().attributeDefinitionId(SERVICE_ATTRIBUTE_DEFINITION_ID).attributeValue(SERVICE_ATTRIBUTE_VALUE_EC2).build(),
            AttributeReference.builder().attributeDefinitionId(TIER_ATTRIBUTE_DEFINITION_ID).attributeValue(TIER_ATTRIBUTE_VALUE_ONE).build()
        )
    );

    public static final BigDecimal ATTRIBUTE_REFERENCES_ONE_PRICE = new BigDecimal("0.225");
    public static final BigDecimal ATTRIBUTE_REFERENCES_TWO_PRICE = new BigDecimal("0.325");
    public static final BigDecimal ATTRIBUTE_REFERENCES_THREE_PRICE = new BigDecimal("0.425");

    public static final List<PriceTableElement> TEST_PRICE_TABLE_ELEMENT = List.of(
        PriceTableElement.builder()
            .attributeReferences(ATTRIBUTE_REFERENCES_COMBINATION_ONE.getReferencesInOrder())
            .price(ATTRIBUTE_REFERENCES_ONE_PRICE)
            .build(),
        PriceTableElement.builder()
            .attributeReferences(ATTRIBUTE_REFERENCES_COMBINATION_TWO.getReferencesInOrder())
            .price(ATTRIBUTE_REFERENCES_TWO_PRICE)
            .build(),
        PriceTableElement.builder()
            .attributeReferences(ATTRIBUTE_REFERENCES_COMBINATION_THREE.getReferencesInOrder())
            .price(ATTRIBUTE_REFERENCES_THREE_PRICE)
            .build()
    );

    public static RateCard makeTestRateCard() {
        return RateCard.builder()
            .id(TEST_RATE_CARD_ID)
            .name(TEST_RATE_CARD_NAME)
            .description(TEST_RATE_CARD_DESCRIPTION)
            .currencyCode(TEST_RATE_CARD_CURRENCY)
            .priceTable(TEST_PRICE_TABLE_ELEMENT)
            .build();
    }
}
