package com.subskribe.billy.productcatalog.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;

import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.productcatalog.db.ChargeDAO;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.validation.CatalogValidation;
import com.subskribe.billy.productcatalog.validation.ChargeValidation;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.utilities.MockChargeBuilder;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ChargeServiceTest {

    private ChargeService chargeService;

    @BeforeEach
    public void setUp() {
        ChargeDAO chargeDAO = mock(ChargeDAO.class);
        ChargeValidation chargeValidation = mock(ChargeValidation.class);
        ProductCatalogGetService productCatalogGetService = mock(ProductCatalogGetService.class);
        FeatureService featureService = mock(FeatureService.class);
        CatalogEventService catalogEventService = mock(CatalogEventService.class);
        CatalogValidation catalogValidation = mock(CatalogValidation.class);
        TenantIdProvider tenantIdProvider = mock(TenantIdProvider.class);
        DSLContextProvider dslContextProvider = mock(DSLContextProvider.class);
        CatalogRelationshipService catalogRelationshipService = mock(CatalogRelationshipService.class);
        AccountingService accountingService = mock(AccountingService.class);
        AccountingGetService accountingGetService = mock(AccountingGetService.class);
        CustomFieldService customFieldService = mock(CustomFieldService.class);
        RateCardService rateCardService = mock(RateCardService.class);

        chargeService = new ChargeService(
            chargeDAO,
            chargeValidation,
            productCatalogGetService,
            featureService,
            catalogEventService,
            catalogValidation,
            tenantIdProvider,
            dslContextProvider,
            catalogRelationshipService,
            accountingService,
            accountingGetService,
            customFieldService,
            rateCardService
        );
    }

    @Test
    public void testSortCharges_AllChargeTypes() throws Exception {
        // Create charges of different types
        Charge recurringCharge = new MockChargeBuilder().recurring().withName("Recurring").build();
        Charge usageCharge = new MockChargeBuilder().usage().withName("Usage").build();
        Charge percentOfCharge = new MockChargeBuilder().percentOf(java.math.BigDecimal.TEN).withName("PercentOf").build();
        Charge oneTimeCharge = new MockChargeBuilder().oneTime().withName("OneTime").build();

        List<Charge> unsortedCharges = Arrays.asList(
            percentOfCharge, // Should be last (priority 3)
            usageCharge, // Should be second to last (priority 2)
            recurringCharge, // Should be first (priority 1)
            oneTimeCharge // Should be first (priority 1)
        );

        List<Charge> sortedCharges = invokeSortCharges(unsortedCharges);

        // Verify the correct order: all non-usage/non-percentage charges first, then usage, then percentage_of
        assertEquals(4, sortedCharges.size());

        // First two should be recurring and one-time (both priority 1) - order within same priority is stable
        assertEquals(ChargeType.RECURRING, sortedCharges.get(0).getType());
        assertEquals(ChargeType.ONE_TIME, sortedCharges.get(1).getType());

        // Third should be usage (priority 2)
        assertEquals(ChargeType.USAGE, sortedCharges.get(2).getType());

        // Fourth should be percentage_of (priority 3)
        assertEquals(ChargeType.PERCENTAGE_OF, sortedCharges.get(3).getType());
    }

    @Test
    public void testSortCharges_SameType() throws Exception {
        // Create multiple charges of the same type
        Charge recurring1 = new MockChargeBuilder().recurring().withName("Recurring1").build();
        Charge recurring2 = new MockChargeBuilder().recurring().withName("Recurring2").build();
        Charge recurring3 = new MockChargeBuilder().recurring().withName("Recurring3").build();

        List<Charge> charges = Arrays.asList(recurring1, recurring2, recurring3);
        List<Charge> sortedCharges = invokeSortCharges(charges);

        // Order should be maintained for charges of the same type (stable sort)
        assertEquals(3, sortedCharges.size());
        assertEquals("Recurring1", sortedCharges.get(0).getName());
        assertEquals("Recurring2", sortedCharges.get(1).getName());
        assertEquals("Recurring3", sortedCharges.get(2).getName());
    }

    @Test
    public void testSortCharges_TransitivityProperty() throws Exception {
        // Test transitivity: if A < B and B < C, then A < C
        Charge recurringCharge = new MockChargeBuilder().recurring().withName("Recurring").build();
        Charge usageCharge = new MockChargeBuilder().usage().withName("Usage").build();
        Charge percentOfCharge = new MockChargeBuilder().percentOf(java.math.BigDecimal.TEN).withName("PercentOf").build();

        // Test all permutations to ensure transitivity is maintained
        List<List<Charge>> permutations = Arrays.asList(
            Arrays.asList(recurringCharge, usageCharge, percentOfCharge),
            Arrays.asList(recurringCharge, percentOfCharge, usageCharge),
            Arrays.asList(usageCharge, recurringCharge, percentOfCharge),
            Arrays.asList(usageCharge, percentOfCharge, recurringCharge),
            Arrays.asList(percentOfCharge, recurringCharge, usageCharge),
            Arrays.asList(percentOfCharge, usageCharge, recurringCharge)
        );

        for (List<Charge> permutation : permutations) {
            List<Charge> sortedCharges = invokeSortCharges(permutation);

            // All permutations should result in the same sorted order
            assertEquals(ChargeType.RECURRING, sortedCharges.get(0).getType());
            assertEquals(ChargeType.USAGE, sortedCharges.get(1).getType());
            assertEquals(ChargeType.PERCENTAGE_OF, sortedCharges.get(2).getType());
        }
    }

    @Test
    public void testSortCharges_EmptyList() throws Exception {
        List<Charge> emptyCharges = Arrays.asList();
        List<Charge> sortedCharges = invokeSortCharges(emptyCharges);

        assertEquals(0, sortedCharges.size());
    }

    @Test
    public void testSortCharges_SingleCharge() throws Exception {
        Charge singleCharge = new MockChargeBuilder().usage().withName("Single").build();
        List<Charge> charges = Arrays.asList(singleCharge);
        List<Charge> sortedCharges = invokeSortCharges(charges);

        assertEquals(1, sortedCharges.size());
        assertEquals(ChargeType.USAGE, sortedCharges.get(0).getType());
    }

    @Test
    public void testSortCharges_MixedTypesWithMultiples() throws Exception {
        // Create a more complex scenario with multiple charges of each type
        Charge recurring1 = new MockChargeBuilder().recurring().withName("Recurring1").build();
        Charge recurring2 = new MockChargeBuilder().recurring().withName("Recurring2").build();
        Charge usage1 = new MockChargeBuilder().usage().withName("Usage1").build();
        Charge usage2 = new MockChargeBuilder().usage().withName("Usage2").build();
        Charge percentOf1 = new MockChargeBuilder().percentOf(java.math.BigDecimal.TEN).withName("PercentOf1").build();
        Charge oneTime1 = new MockChargeBuilder().oneTime().withName("OneTime1").build();

        List<Charge> unsortedCharges = Arrays.asList(percentOf1, usage2, recurring1, usage1, oneTime1, recurring2);

        List<Charge> sortedCharges = invokeSortCharges(unsortedCharges);

        assertEquals(6, sortedCharges.size());

        // First three should be priority 1 charges (recurring and one-time) in original order
        assertEquals("Recurring1", sortedCharges.get(0).getName());
        assertEquals("OneTime1", sortedCharges.get(1).getName());
        assertEquals("Recurring2", sortedCharges.get(2).getName());

        // Next two should be usage charges (priority 2) in original order
        assertEquals("Usage2", sortedCharges.get(3).getName());
        assertEquals("Usage1", sortedCharges.get(4).getName());

        // Last should be percentage_of charge (priority 3)
        assertEquals("PercentOf1", sortedCharges.get(5).getName());
    }

    @Test
    public void testGetChargeSortPriority() throws Exception {
        // Test the priority assignment directly
        Method getChargeSortPriorityMethod = ChargeService.class.getDeclaredMethod("getChargeSortPriority", ChargeType.class);
        getChargeSortPriorityMethod.setAccessible(true);

        assertEquals(1, getChargeSortPriorityMethod.invoke(chargeService, ChargeType.RECURRING));
        assertEquals(1, getChargeSortPriorityMethod.invoke(chargeService, ChargeType.ONE_TIME));
        assertEquals(1, getChargeSortPriorityMethod.invoke(chargeService, ChargeType.PREPAID));
        assertEquals(2, getChargeSortPriorityMethod.invoke(chargeService, ChargeType.USAGE));
        assertEquals(3, getChargeSortPriorityMethod.invoke(chargeService, ChargeType.PERCENTAGE_OF));
    }

    // Helper method to invoke the private sortCharges method
    @SuppressWarnings("unchecked")
    private List<Charge> invokeSortCharges(List<Charge> charges) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        Method sortChargesMethod = ChargeService.class.getDeclaredMethod("sortCharges", List.class);
        sortChargesMethod.setAccessible(true);
        return (List<Charge>) sortChargesMethod.invoke(chargeService, charges);
    }
}
