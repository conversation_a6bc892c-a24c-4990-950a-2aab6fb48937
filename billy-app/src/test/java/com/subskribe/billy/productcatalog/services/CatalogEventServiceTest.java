package com.subskribe.billy.productcatalog.services;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.TenantPartitionKey;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.fixtures.ProductCatalogData;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.resources.json.plan.ChargeJson;
import com.subskribe.billy.resources.json.plan.PlanMapper;
import com.subskribe.billy.resources.json.product.ProductJson;
import com.subskribe.billy.resources.json.product.ProductMapper;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.math.BigDecimal;
import java.util.UUID;
import org.jooq.Configuration;
import org.jooq.impl.DefaultConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class CatalogEventServiceTest {

    private static final String TENANT_ID = "test-tenant-id";
    private static final Configuration JOOQ_CONFIGURATION = new DefaultConfiguration();

    @Mock
    private EventPublishingService mockEventPublishingService;

    @Mock
    private TenantIdProvider mockTenantIdProvider;

    @Mock
    private PlanMapper mockPlanMapper;

    @Mock
    private ProductMapper mockProductMapper;

    @Mock
    private UncheckedObjectMapper mockObjectMapper;

    private CatalogEventService catalogEventService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);

        catalogEventService = new CatalogEventService(
            mockEventPublishingService,
            mockTenantIdProvider,
            mockPlanMapper,
            mockProductMapper,
            mockObjectMapper
        );
    }

    @Test
    void sendChargeEventShouldPublishEventWithCorrectParameters() {
        Charge charge = createTestCharge();
        ChargeJson chargeJson = createTestChargeJson();
        byte[] serializedJson = "serialized-charge-json".getBytes();
        EventType eventType = EventType.CHARGE_CREATED;

        when(mockPlanMapper.chargeToJson(charge)).thenReturn(chargeJson);
        when(mockObjectMapper.writeValueAsBytes(chargeJson)).thenReturn(serializedJson);

        catalogEventService.sendChargeEvent(charge, eventType, JOOQ_CONFIGURATION);

        verify(mockPlanMapper).chargeToJson(charge);
        verify(mockTenantIdProvider).provideTenantIdString();
        verify(mockObjectMapper).writeValueAsBytes(chargeJson);
        verify(mockEventPublishingService).publishEventInTransaction(
            eq(JOOQ_CONFIGURATION),
            eq(eventType),
            eq(TENANT_ID),
            eq(charge.getEntityIds()),
            eq(new TenantPartitionKey(TENANT_ID)),
            eq(serializedJson)
        );
    }

    @Test
    void sendProductEventShouldPublishEventWithCorrectParameters() {
        Product product = createTestProduct();
        ProductJson productJson = createTestProductJson();
        byte[] serializedJson = "serialized-product-json".getBytes();
        EventType eventType = EventType.PRODUCT_CREATED;

        when(mockProductMapper.productToJson(product)).thenReturn(productJson);
        when(mockObjectMapper.writeValueAsBytes(productJson)).thenReturn(serializedJson);

        catalogEventService.sendProductEvent(product, eventType, JOOQ_CONFIGURATION);

        verify(mockProductMapper).productToJson(product);
        verify(mockTenantIdProvider).provideTenantIdString();
        verify(mockObjectMapper).writeValueAsBytes(productJson);
        verify(mockEventPublishingService).publishEventInTransaction(
            eq(JOOQ_CONFIGURATION),
            eq(eventType),
            eq(TENANT_ID),
            eq(product.getEntityIds()),
            eq(new TenantPartitionKey(TENANT_ID)),
            eq(serializedJson)
        );
    }

    private Charge createTestCharge() {
        UUID chargeId = UUID.randomUUID();
        UUID planId = UUID.randomUUID();
        return ProductCatalogData.createCharge(chargeId, planId);
    }

    private ChargeJson createTestChargeJson() {
        ChargeJson chargeJson = mock(ChargeJson.class);
        when(chargeJson.getName()).thenReturn("Test Charge");
        when(chargeJson.getAmount()).thenReturn(BigDecimal.valueOf(100.0));
        return chargeJson;
    }

    private Product createTestProduct() {
        return ProductCatalogData.createProduct("test-product-id", "TEST-SKU");
    }

    private ProductJson createTestProductJson() {
        ProductJson productJson = mock(ProductJson.class);
        when(productJson.getName()).thenReturn("Test Product");
        when(productJson.getDisplayName()).thenReturn("Test Product Display Name");
        return productJson;
    }
}
