package com.subskribe.billy.productcatalog.services;

import static com.subskribe.billy.productcatalog.services.PriceTiersHelper.validatePriceTiersOrReturnError;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.subskribe.billy.productcatalog.mapper.PlanRecordMapper;
import com.subskribe.billy.productcatalog.model.PriceTier;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

public class PriceTiersHelperTest {

    private static final List<PriceTier> priceTiers = List.of(
        PriceTier.of(5L, new BigDecimal("10")),
        PriceTier.of(10L, new BigDecimal("8")),
        PriceTier.of(null, new BigDecimal("5"))
    );

    @Test
    public void validateMinimumPriceTiers() {
        Optional<String> error = validatePriceTiersOrReturnError(List.of(), Optional.empty(), Optional.empty());
        Assertions.assertThat(error).isPresent().hasValue("Price tiers must have at least 2 entries");

        error = validatePriceTiersOrReturnError(List.of(PriceTier.of(5L, new BigDecimal("10"))), Optional.empty(), Optional.empty());
        Assertions.assertThat(error).isPresent().hasValue("Price tiers must have at least 2 entries");

        error = validatePriceTiersOrReturnError(
            List.of(PriceTier.of(5L, new BigDecimal("10")), PriceTier.of(null, new BigDecimal("8"))),
            Optional.empty(),
            Optional.empty()
        );
        Assertions.assertThat(error).isEmpty();
    }

    @Test
    public void validateFinalTier() {
        Optional<String> error = validatePriceTiersOrReturnError(
            List.of(PriceTier.of(5L, new BigDecimal("10")), PriceTier.of(10L, new BigDecimal("8")), PriceTier.of(15L, new BigDecimal("5"))),
            Optional.empty(),
            Optional.empty()
        );
        Assertions.assertThat(error).isPresent().hasValue("Last price tier must be unbounded, but was 15");
    }

    @Test
    public void validateMaxQuantity() {
        Optional<String> error = validatePriceTiersOrReturnError(priceTiers, Optional.empty(), Optional.empty());
        Assertions.assertThat(error).isEmpty();

        error = validatePriceTiersOrReturnError(priceTiers, Optional.empty(), Optional.of(8L));
        Assertions.assertThat(error).isPresent().hasValue("Price tier with quantity 10 cannot be above maximum quantity 8");

        error = validatePriceTiersOrReturnError(priceTiers, Optional.empty(), Optional.of(10L));
        Assertions.assertThat(error).isPresent().hasValue("Price tier with quantity 10 cannot be above maximum quantity 10");

        error = validatePriceTiersOrReturnError(priceTiers, Optional.empty(), Optional.of(15L));
        Assertions.assertThat(error).isEmpty();
    }

    @Test
    public void validateMinQuantity() {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );

        Optional<String> error = validatePriceTiersOrReturnError(priceTiers, Optional.empty(), Optional.empty());
        Assertions.assertThat(error).isEmpty();

        error = validatePriceTiersOrReturnError(priceTiers, Optional.of(6L), Optional.empty());
        Assertions.assertThat(error).isPresent().hasValue("First price tier quantity 5 cannot be below minimum quantity 6");

        error = validatePriceTiersOrReturnError(priceTiers, Optional.of(2L), Optional.empty());
        Assertions.assertThat(error).isEmpty();

        error = validatePriceTiersOrReturnError(priceTiers, Optional.of(5L), Optional.empty());
        Assertions.assertThat(error).isEmpty();
    }

    @Test
    public void validateSerializeDeserialize() throws JsonProcessingException {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );
        var mapper = Mappers.getMapper(PlanRecordMapper.class);

        var serialized = mapper.serializePriceTiers(priceTiers);
        var deserialized = mapper.deserializePriceTiers(serialized);

        var priceTiersSanitized = List.of(
            PriceTier.of(5L, new BigDecimal("10.00000")),
            PriceTier.of(10L, new BigDecimal("8.00000")),
            PriceTier.of(null, new BigDecimal("5.00000"))
        );
        Assertions.assertThat(deserialized).isEqualTo(priceTiersSanitized);
    }
}
