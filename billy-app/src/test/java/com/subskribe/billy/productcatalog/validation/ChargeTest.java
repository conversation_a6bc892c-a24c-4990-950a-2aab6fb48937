package com.subskribe.billy.productcatalog.validation;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

import com.subskribe.billy.anrok.service.AnrokIntegrationGetService;
import com.subskribe.billy.avalara.service.AvalaraIntegrationGetService;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ModelValidationException;
import com.subskribe.billy.exception.ServiceFailureException;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.PriceTier;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.UnitOfMeasureService;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.shared.Base30StringGen;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.enums.PercentDerivedFrom;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.taxjar.service.TaxJarIntegrationGetService;
import com.subskribe.billy.tenant.services.TenantSettingService;
import com.subskribe.billy.test.utilities.MockChargeBuilder;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;
import javax.inject.Provider;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

public class ChargeTest {

    private static final Recurrence MONTHLY_RECURRENCE = new Recurrence(Cycle.MONTH, 1);

    private static final List<PriceTier> PRICE_TIERS = List.of(
        PriceTier.of(5L, new BigDecimal("10")),
        PriceTier.of(10L, new BigDecimal("8")),
        PriceTier.of(null, new BigDecimal("5"))
    );

    private static final List<ChargeType> NON_CREDITABLE_CHARGES = List.of(
        ChargeType.RECURRING,
        ChargeType.PREPAID,
        ChargeType.USAGE,
        ChargeType.PERCENTAGE_OF
    );

    private static final List<ChargeType> NON_DISCOUNT_CHARGE_TYPES = Arrays.stream(ChargeType.values())
        .filter(c -> c != ChargeType.RECURRING && c != ChargeType.ONE_TIME)
        .toList();

    private static final List<ChargeModel> NON_DISCOUNT_CHARGE_MODELS = Arrays.stream(ChargeModel.values())
        .filter(c -> !ChargeValidation.DISCOUNT_CHARGE_MODEL_PREDICATE.test(c))
        .toList();

    @Mock
    private FeatureService mockFeatureService;

    private ChargeValidation chargeValidation;

    @BeforeEach
    public void setup() {
        TenantSettingService mockTenantSettingService = mock(TenantSettingService.class);
        UnitOfMeasureService mockUnitOfMeasureService = mock(UnitOfMeasureService.class);
        Provider<RevenueRecognitionGetService> mockRevenueRecognitionGetServiceProvider = mock(Provider.class);
        Provider<PlatformFeatureService> mockPlatformFeatureServiceProvider = mock(Provider.class);
        AvalaraIntegrationGetService mockAvalaraIntegrationGetService = mock(AvalaraIntegrationGetService.class);
        mockFeatureService = mock(FeatureService.class);
        RateCardService mockRateCardService = mock(RateCardService.class);
        TaxJarIntegrationGetService mockTaxJarIntegrationGetService = mock(TaxJarIntegrationGetService.class);
        AnrokIntegrationGetService mockAnrokIntegrationGetService = mock(AnrokIntegrationGetService.class);

        chargeValidation = new ChargeValidation(
            mockTenantSettingService,
            mockUnitOfMeasureService,
            mockRevenueRecognitionGetServiceProvider,
            mockPlatformFeatureServiceProvider,
            mockAvalaraIntegrationGetService,
            mockFeatureService,
            mockRateCardService,
            mockTaxJarIntegrationGetService,
            mockAnrokIntegrationGetService
        );
    }

    @Test
    public void chargeWithEmptyNameIsInvalid() {
        Charge charge = getCharge("", BigDecimal.ONE, ChargeType.RECURRING, ChargeModel.PER_UNIT, MONTHLY_RECURRENCE, null);

        assertThrows(ModelValidationException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void prepaidChargeWithoutRecurrenceIsValid() {
        Charge charge = getCharge("name", BigDecimal.ONE, ChargeType.PREPAID, ChargeModel.PER_UNIT, null, null);
        chargeValidation.validateChargeInternal(charge);
    }

    @Test
    public void recurringChargeWithoutRecurrenceIsInvalid() {
        Charge charge = getCharge("name", BigDecimal.ONE, ChargeType.RECURRING, ChargeModel.PER_UNIT, null, null);

        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void perUnitChargeWithoutAmountIsInvalid() {
        Charge charge = getCharge("name", null, ChargeType.RECURRING, ChargeModel.PER_UNIT, MONTHLY_RECURRENCE, null);

        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void amountWithMoreThan5DecimalsIsInvalid() {
        Charge charge = getCharge("name", new BigDecimal("1.000001"), ChargeType.RECURRING, ChargeModel.PER_UNIT, MONTHLY_RECURRENCE, null);

        assertThrows(IllegalArgumentException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void priceTierContainingAmountWithMoreThan5DecimalsIsInvalid() {
        var priceTiers = List.of(
            PriceTier.of(5L, BigDecimal.ZERO),
            PriceTier.of(10L, new BigDecimal("1.000001")),
            PriceTier.of(null, BigDecimal.ZERO)
        );

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        assertThrows(IllegalArgumentException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void trailingZerosInAmountDoNotCountTowardsPrecision() {
        Charge charge = getCharge("name", new BigDecimal("1.00001000"), ChargeType.RECURRING, ChargeModel.PER_UNIT, MONTHLY_RECURRENCE, null);
        chargeValidation.validateChargeInternal(charge);
    }

    @Test
    public void trailingZerosInPriceTierAmountsDoNotCountTowardsPrecision() {
        var priceTiers = List.of(
            PriceTier.of(5L, BigDecimal.ZERO),
            PriceTier.of(10L, new BigDecimal("1.00001000")),
            PriceTier.of(null, BigDecimal.ZERO)
        );

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        chargeValidation.validateChargeInternal(charge);
    }

    @Test
    public void validChargeModel() {
        var priceTiers = List.of(PriceTier.of(5L, BigDecimal.ZERO), PriceTier.of(10L, BigDecimal.ZERO), PriceTier.of(null, BigDecimal.ZERO));

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        chargeValidation.validateChargeInternal(charge);
    }

    @Test
    public void outOfOrderEntriesShouldBeInvalid() {
        var priceTiers = List.of(PriceTier.of(10L, BigDecimal.ZERO), PriceTier.of(5L, BigDecimal.ZERO), PriceTier.of(null, BigDecimal.ZERO));

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void lastEntryMustBeNull() {
        var priceTiers = List.of(PriceTier.of(5L, BigDecimal.ZERO), PriceTier.of(10L, BigDecimal.ZERO), PriceTier.of(20L, BigDecimal.ZERO));

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void moreThanOneEmptyUntilQuantityIsInvalid() {
        var priceTiers = List.of(
            PriceTier.of(5L, BigDecimal.ZERO),
            PriceTier.of(10L, BigDecimal.ZERO),
            PriceTier.of(null, BigDecimal.ZERO),
            PriceTier.of(null, BigDecimal.ZERO)
        );

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void volumeChargeModelWithoutPriceTiersIsInvalid() {
        Charge charge = getValidVolumeRecurringCharge(null);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void usageWithDrawdownIsValid() {
        Charge charge = getValidPerUnitUsageCharge();
        charge.setDrawdown(true);
        chargeValidation.validateChargeInternal(charge);
    }

    @Test
    public void usageWithMinimumCommit() {
        Charge charge = getValidPerUnitUsageCharge();
        charge.setMinimumCommitBaseChargeId("CHRG-5555555");
        chargeValidation.validateChargeInternal(charge);

        charge.setOverageBaseChargeId("CHRG-1234567");
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void recurringChargeWithDrawdownIsInvalid() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setDrawdown(true);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void volumeChargeModelWithEmptyPriceTiersIsInvalid() {
        Charge charge = getValidVolumeRecurringCharge(List.of());
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void blockUnitPrice() {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("20")),
            PriceTier.of(null, new BigDecimal("50"))
        );

        Charge charge = getBlockRecurringCharge(priceTiers);

        assertEquals(new BigDecimal("20.00000"), charge.getListUnitPriceByQuantity(10L, Optional.empty()));
        assertEquals(new BigDecimal("50.00000"), charge.getListUnitPriceByQuantity(20L, Optional.empty()));
    }

    @Test
    public void volumeTierUnitPriceWithNegativeQuantity() {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );

        Charge charge = getValidVolumeRecurringCharge(priceTiers);

        BigDecimal unitPrice = charge.getListUnitPriceByQuantity(-10L, Optional.empty());
        assertEquals(new BigDecimal("8"), unitPrice);
    }

    @Disabled("Enable test once backfill is complete")
    @Test
    public void percentOfPerUnitWithNoDerivedFromFails() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setType(ChargeType.PERCENTAGE_OF);
        charge.setRecurrence(null);
        charge.setAmount(null);
        charge.setPercent(new BigDecimal("0.5"));
        charge.setPercentDerivedFrom(null);
        charge.setMaxQuantity(10L);
        assertThrows(ServiceFailureException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void usageWithQuantityConstraintsIsInvalid() {
        Charge charge = getValidPerUnitUsageCharge();
        charge.setMaxQuantity(10L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void flatFeeWithQuantityConstraintsIsInvalid() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        charge.setMinQuantity(2L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void percentOfPerUnitWithQuantityConstraintsIsValid() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setType(ChargeType.PERCENTAGE_OF);
        charge.setRecurrence(null);
        charge.setAmount(null);
        charge.setPercent(new BigDecimal("0.5"));
        charge.setPercentDerivedFrom(PercentDerivedFrom.LIST_AMOUNT);
        charge.setMaxQuantity(10L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void percentOfFlatFeeWithQuantityConstraintsIsInvalid() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setType(ChargeType.PERCENTAGE_OF);
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        charge.setRecurrence(null);
        charge.setAmount(null);
        charge.setPercent(new BigDecimal("0.5"));
        charge.setMaxQuantity(10L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void minQuantityNegative() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setMinQuantity(-1L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void maxQuantityNegative() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setMaxQuantity(-1L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void defaultQuantityNegative() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setDefaultQuantity(-1L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void minQuantityGreaterThanMaxQuantity() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setMinQuantity(10L);
        charge.setMaxQuantity(5L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void minQuantityGreaterThanDefaultQuantity() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setMinQuantity(10L);
        charge.setDefaultQuantity(5L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void defaultQuantityGreaterThanMaxQuantity() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setDefaultQuantity(10L);
        charge.setMaxQuantity(5L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void volumeWithMaxQuantity() {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setMaxQuantity(10L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setMaxQuantity(15L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void volumeWithMinQuantity() {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setMinQuantity(-1L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setMinQuantity(6L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setMinQuantity(2L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setMinQuantity(5L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void volumeWithDefaultQuantity() {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        charge.setDefaultQuantity(1L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setDefaultQuantity(16L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setDefaultQuantity(5L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void volumeWithDefaultMinAndMaxQuantity() {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        charge.setMinQuantity(2L);
        charge.setMaxQuantity(15L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setDefaultQuantity(1L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setDefaultQuantity(16L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setDefaultQuantity(5L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void blockWithMaxQuantity() {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );

        Charge charge = getBlockRecurringCharge(priceTiers);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setMaxQuantity(10L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setMaxQuantity(15L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void tieredWithDefaultMinAndMaxQuantity() {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        charge.setChargeModel(ChargeModel.TIERED);
        charge.setMinQuantity(2L);
        charge.setMaxQuantity(15L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setDefaultQuantity(1L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setDefaultQuantity(16L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setDefaultQuantity(5L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void tieredZeroUnitPrice() {
        var priceTiers = List.of(PriceTier.of(5L, new BigDecimal("0")), PriceTier.of(null, new BigDecimal("8")));

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        charge.setChargeModel(ChargeModel.TIERED);
        assertEquals(BigDecimal.ZERO, charge.getListUnitPriceByQuantity(0L, Optional.empty()));
        assertEquals(BigDecimal.ZERO, charge.getListUnitPriceByQuantity(5L, Optional.empty()));
        assertNull(charge.getListUnitPriceByQuantity(9L, Optional.empty()));

        priceTiers = List.of(PriceTier.of(5L, new BigDecimal("2")), PriceTier.of(10L, new BigDecimal("2")), PriceTier.of(null, new BigDecimal("5")));

        charge.setPriceTiers(priceTiers);
        assertEquals(new BigDecimal("2"), charge.getListUnitPriceByQuantity(5L, Optional.empty()));
        assertEquals(new BigDecimal("2"), charge.getListUnitPriceByQuantity(10L, Optional.empty()));
        assertNull(charge.getListUnitPriceByQuantity(11L, Optional.empty()));
    }

    @Test
    public void oneTimeChargeWithRecurrenceFails() {
        Charge charge = getCharge(
            makeTestCharge(),
            new BigDecimal("1000.0"),
            ChargeType.ONE_TIME,
            ChargeModel.PER_UNIT,
            new Recurrence(Cycle.MONTH, 6),
            null
        );
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void oneTimeChargeWithLifetime() {
        Charge charge = getCharge(makeTestCharge(), new BigDecimal("1000.0"), ChargeType.ONE_TIME, ChargeModel.PER_UNIT, null, null);

        charge.setDurationInMonths(null);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setDurationInMonths(1L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setDurationInMonths(6L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setDurationInMonths(12L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setDurationInMonths(120L);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        // months cannot be 0
        charge.setDurationInMonths(0L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        // cannot have negative months
        charge.setDurationInMonths(-1L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        // limit of 120 months (10 years)
        charge.setDurationInMonths(150L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.RECURRING);
        charge.setDurationInMonths(6L);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void customChargeValid() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setCustom(true);
        charge.setAmount(null);

        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.PER_UNIT);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.ONE_TIME);
        charge.setChargeModel(ChargeModel.PER_UNIT);
        charge.setRecurrence(null);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.ONE_TIME);
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        charge.setRecurrence(null);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void customChargeInvalid() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setCustom(true);

        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.PER_UNIT);
        charge.setAmount(new BigDecimal(100L));
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.PERCENTAGE_OF);
        charge.setChargeModel(ChargeModel.PER_UNIT);
        charge.setAmount(null);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.USAGE);
        charge.setChargeModel(ChargeModel.PER_UNIT);
        charge.setAmount(null);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.VOLUME);
        charge.setPriceTiers(PRICE_TIERS);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.ONE_TIME);
        charge.setChargeModel(ChargeModel.BLOCK);
        charge.setRecurrence(null);
        charge.setPriceTiers(PRICE_TIERS);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void customChargeCannotHaveListPriceEditable() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setCustom(true);
        charge.setIsListPriceEditable(true);
        assertThrows(InvalidInputException.class, () -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void percentOfWithListPriceEditableSucceeds() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setType(ChargeType.PERCENTAGE_OF);
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        charge.setRecurrence(null);
        charge.setAmount(null);
        charge.setPercent(new BigDecimal("0.5"));
        charge.setIsListPriceEditable(true);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void tieredWithListPriceEditableSucceeds() {
        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );

        Charge charge = getValidVolumeRecurringCharge(priceTiers);
        charge.setIsListPriceEditable(true);

        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.TIERED);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.USAGE);
        charge.setChargeModel(ChargeModel.PER_UNIT);
        charge.setPriceTiers(null);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.USAGE);
        charge.setChargeModel(ChargeModel.RATE_CARD_LOOKUP);
        charge.setPriceTiers(null);
        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
            .withMessageContaining("Rate card usage charges cannot have price override");

        charge.setType(ChargeType.PREPAID);
        charge.setChargeModel(ChargeModel.PER_UNIT);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void testIsListPriceEditableCombinationsAreValid() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setIsListPriceEditable(true);

        var priceTiers = List.of(
            PriceTier.of(5L, new BigDecimal("10")),
            PriceTier.of(10L, new BigDecimal("8")),
            PriceTier.of(null, new BigDecimal("5"))
        );

        // non-recurring
        charge.setRecurrence(null);

        charge.setType(ChargeType.ONE_TIME);
        charge.setChargeModel(ChargeModel.PER_UNIT);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.ONE_TIME);
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        // recurring
        charge.setRecurrence(MONTHLY_RECURRENCE);

        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.PER_UNIT);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.BLOCK);
        charge.setPriceTiers(priceTiers);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.VOLUME);
        charge.setPriceTiers(priceTiers);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void chargeTypeValidationFailsOnNonUsageCharges() {
        List<Charge> invalidChargeList = Stream.of(ChargeType.PREPAID, ChargeType.PERCENTAGE_OF)
            .map(type -> {
                Charge charge = getValidPerUnitUsageCharge();
                charge.setType(type);
                charge.setChargeModel(ChargeModel.RATE_CARD_LOOKUP);
                charge.setAmount(null);
                charge.setRateCardId("RCRD-" + RandomStringUtils.randomAlphanumeric(7).toUpperCase());
                return charge;
            })
            .toList();

        invalidChargeList.forEach(charge ->
            assertThatExceptionOfType(InvalidInputException.class)
                .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
                .withMessageContaining("RATE_CARD_LOOKUP can be used with only")
                .withMessageContaining("USAGE")
                .withMessageContaining("RECURRING")
                .withMessageContaining("ONE_TIME")
        );
    }

    @Test
    public void whenRateCardIdIsMissingThenValidationFails() {
        Charge charge = getValidPerUnitUsageCharge();
        charge.setType(ChargeType.USAGE);
        charge.setChargeModel(ChargeModel.RATE_CARD_LOOKUP);
        charge.setAmount(null);
        charge.setRateCardId("");

        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
            .withMessageContaining("RATE_CARD_LOOKUP rate card id should be referenced");
    }

    @Test
    public void whenUsageChargeHasUpFrontBillingTerm_thenFail() {
        Charge charge = getValidPerUnitUsageCharge();
        charge.setBillingTerm(BillingTerm.UP_FRONT);

        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
            .withMessageContaining("billing term");
    }

    @Test
    public void whenOneTimeChargeHasInArrearsBillingTerm_thenFail() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setType(ChargeType.ONE_TIME);
        charge.setRecurrence(null);
        charge.setBillingTerm(BillingTerm.IN_ARREARS);

        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
            .withMessageContaining("billing term");
    }

    @Test
    public void whenPrepaidChargeHasInArrearsBillingTerm_thenFail() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setType(ChargeType.PREPAID);
        charge.setRecurrence(null);
        charge.setBillingTerm(BillingTerm.IN_ARREARS);

        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
            .withMessageContaining("billing term");
    }

    @Test
    public void prepaidChargeRecurrenceValidation() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setType(ChargeType.PREPAID);
        charge.setRecurrence(null);

        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setRecurrence(new Recurrence(Cycle.YEAR, 1));
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setRecurrence(new Recurrence(Cycle.QUARTER, 1));
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setRecurrence(new Recurrence(Cycle.MONTH, 1));
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setRecurrence(new Recurrence(Cycle.SEMI_ANNUAL, 1));
        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
            .withMessageContaining("SEMI_ANNUAL")
            .withMessageContaining("charge recurrence must be one of");

        charge.setRecurrence(new Recurrence(Cycle.PAID_IN_FULL, 1));
        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
            .withMessageContaining("PAID_IN_FULL")
            .withMessageContaining("charge recurrence must be one of");
    }

    @Test
    public void recurringChargeSupportsAnyBillingTerm() {
        Charge charge = getValidPerUnitRecurringCharge();

        charge.setBillingTerm(BillingTerm.UP_FRONT);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setBillingTerm(BillingTerm.IN_ARREARS);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void percentOfChargeCanHaveAnyBillingTerm() {
        Charge charge = getValidPercentOfCharge();

        charge.setBillingTerm(BillingTerm.IN_ARREARS);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setBillingTerm(BillingTerm.IN_ARREARS);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void whenUsageRateCardHasQuantityOverage_thenFail() {
        Charge charge = getValidPerUnitUsageCharge();
        charge.setChargeModel(ChargeModel.RATE_CARD_LOOKUP);
        charge.setRateCardId("RATE-1234567");
        charge.setOverageBaseChargeId("CHRG-1234567");
        charge.setAmount(null);

        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
            .withMessageContaining("rate card")
            .withMessageContaining("quantity overage");

        charge.setOverageBaseChargeId(null);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    // all billing cycles are allowed for a recurring charge
    @Test
    public void testBillingCyclesForRecurring() {
        Charge charge = getValidPerUnitRecurringCharge();
        for (var billingCycle : BillingCycle.values()) {
            charge.setBillingCycle(billingCycle);
            assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
        }
    }

    // all billing cycles are allowed for a percent of charge
    @Test
    public void testBillingCyclesForPercentOf() {
        Charge charge = getValidPercentOfCharge();
        for (var billingCycle : BillingCycle.values()) {
            charge.setBillingCycle(billingCycle);
            if (billingCycle == BillingCycle.CHARGE_RECURRENCE) {
                assertThatExceptionOfType(InvalidInputException.class)
                    .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
                    .withMessageContaining("Charge recurrence billing cycle");
            } else {
                assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
            }
        }
    }

    // only default billing cycle is allowed for prepaid
    @Test
    public void testBillingCyclesForOneTime() {
        Charge charge = getValidOneTimeCharge();
        for (var billingCycle : BillingCycle.values()) {
            charge.setBillingCycle(billingCycle);
            if (billingCycle == BillingCycle.DEFAULT) {
                assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
            } else if (billingCycle == BillingCycle.CHARGE_RECURRENCE) {
                assertThatExceptionOfType(InvalidInputException.class)
                    .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
                    .withMessageContaining("Charge recurrence billing cycle");
            } else {
                assertThatExceptionOfType(InvalidInputException.class)
                    .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
                    .withMessageContaining(billingCycle.name())
                    .withMessageContaining("allowed");
            }
        }
    }

    // only default billing cycle is allowed for usage charge
    @Test
    public void testBillingCyclesForUsage() {
        Charge charge = getValidPerUnitUsageCharge();
        for (var billingCycle : BillingCycle.values()) {
            charge.setBillingCycle(billingCycle);
            if (billingCycle == BillingCycle.DEFAULT) {
                assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
            } else if (billingCycle == BillingCycle.CHARGE_RECURRENCE) {
                assertThatExceptionOfType(InvalidInputException.class)
                    .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
                    .withMessageContaining("Charge recurrence billing cycle");
            } else {
                assertThatExceptionOfType(InvalidInputException.class)
                    .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
                    .withMessageContaining(billingCycle.name())
                    .withMessageContaining("allowed");
            }
        }
    }

    @Test
    public void testOneTimeChargeCreditable() {
        Charge charge = getValidOneTimeCharge();
        charge.setCreditable(true);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void testNonOTCCreditable() {
        Charge charge = getCharge(makeTestCharge(), new BigDecimal("1000.0"), ChargeType.ONE_TIME, ChargeModel.PER_UNIT, null, null);
        charge.setCreditable(false);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        NON_CREDITABLE_CHARGES.forEach(type -> {
            charge.setType(type);
            charge.setCreditable(true);
            assertThatExceptionOfType(InvalidInputException.class)
                .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
                .withMessageContaining("Only one time charges can be marked creditable");
        });
    }

    @Test
    public void testDiscountValidation_succeeds() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setIsDiscount(true);
        charge.setAmount(new BigDecimal("-1000.00"));

        charge.setType(ChargeType.RECURRING);
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));

        charge.setType(ChargeType.ONE_TIME);
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        charge.setRecurrence(null);
        assertDoesNotThrow(() -> chargeValidation.validateChargeInternal(charge));
    }

    @Test
    public void testDiscountNegativeAmountValidation() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setIsDiscount(true);
        charge.setAmount(new BigDecimal("1000.00"));

        charge.setType(ChargeType.ONE_TIME);
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        charge.setRecurrence(null);
        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
            .withMessageContaining("amount for discount charge cannot be positive");
    }

    @Test
    public void testDiscountValidation_throws() {
        Charge charge = getValidPerUnitRecurringCharge();
        charge.setIsDiscount(true);
        charge.setAmount(new BigDecimal("-1000.00"));

        NON_DISCOUNT_CHARGE_MODELS.forEach(model -> {
            charge.setChargeModel(model);
            assertThatExceptionOfType(InvalidInputException.class)
                .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
                .withMessageContaining("Charge can be a discount only if charge model is FLAT_FEE or PER_UNIT");
        });

        NON_DISCOUNT_CHARGE_MODELS.forEach(model -> {
            charge.setChargeModel(model);
            assertThatExceptionOfType(InvalidInputException.class)
                .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
                .withMessageContaining("Charge can be a discount only if charge model is FLAT_FEE or PER_UNIT");
        });

        // validate charge types
        NON_DISCOUNT_CHARGE_TYPES.forEach(type -> {
            charge.setType(type);
            charge.setChargeModel(ChargeModel.FLAT_FEE);
            assertThatExceptionOfType(InvalidInputException.class)
                .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
                .withMessageContaining("Discount charges can only be ONE_TIME or RECURRING");
        });

        // validate event based charges
        charge.setType(ChargeType.ONE_TIME);
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        charge.setRecurrence(null);
        charge.setEventBased(true);
        assertThatExceptionOfType(InvalidInputException.class)
            .isThrownBy(() -> chargeValidation.validateChargeInternal(charge))
            .withMessageContaining("Discount charges cannot be event based");
        charge.setEventBased(false);
    }

    public static String makeTestCharge() {
        return String.format("CHRG-%s", RandomStringUtils.randomAlphanumeric(7).toUpperCase());
    }

    private Charge getBlockRecurringCharge(List<PriceTier> priceTiers) {
        return getCharge("name", null, ChargeType.RECURRING, ChargeModel.BLOCK, MONTHLY_RECURRENCE, priceTiers);
    }

    private Charge getValidVolumeRecurringCharge(List<PriceTier> priceTiers) {
        return getCharge("name", BigDecimal.ONE, ChargeType.RECURRING, ChargeModel.VOLUME, MONTHLY_RECURRENCE, priceTiers);
    }

    private Charge getValidPerUnitRecurringCharge() {
        return getCharge("name", BigDecimal.ONE, ChargeType.RECURRING, ChargeModel.PER_UNIT, MONTHLY_RECURRENCE, null);
    }

    private Charge getValidPercentOfCharge() {
        return new MockChargeBuilder().percentOf(BigDecimal.ONE).build();
    }

    private Charge getValidOneTimeCharge() {
        return new MockChargeBuilder().oneTime().withAmount(BigDecimal.ONE).build();
    }

    private Charge getValidPerUnitUsageCharge() {
        return getCharge("name", BigDecimal.ONE, ChargeType.USAGE, ChargeModel.PER_UNIT, MONTHLY_RECURRENCE, null);
    }

    private Charge getCharge(
        String name,
        BigDecimal amount,
        ChargeType type,
        ChargeModel chargeModel,
        Recurrence recurrence,
        List<PriceTier> priceTiers
    ) {
        return new Charge(
            UUID.randomUUID(),
            EntityFixture.ALL_ENTITY_IDS,
            makeTestCharge(),
            UUID.randomUUID(),
            String.format("PLAN-%s", Base30StringGen.generate(7)),
            name,
            name,
            "",
            amount,
            type,
            chargeModel,
            recurrence,
            null,
            priceTiers,
            UUID.randomUUID(),
            UUID.randomUUID(),
            "",
            false,
            null,
            null,
            false,
            false,
            true,
            false,
            null,
            null,
            List.of(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            false,
            false,
            null,
            BillingCycle.DEFAULT,
            null,
            null,
            null,
            null
        );
    }
}
