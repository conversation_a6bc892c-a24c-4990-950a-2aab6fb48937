package com.subskribe.billy.productcatalog;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.accounting.services.AccountingGetService;
import com.subskribe.billy.accounting.services.AccountingService;
import com.subskribe.billy.anrok.service.AnrokIntegrationGetService;
import com.subskribe.billy.avalara.service.AvalaraIntegrationGetService;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipGetService;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipService;
import com.subskribe.billy.catalogrelationship.service.CatalogRelationshipValidator;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.configuration.dynamic.fixture.FeatureServiceFixture;
import com.subskribe.billy.customfield.CustomFieldService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.entity.fixtures.EntityGetServiceFixture;
import com.subskribe.billy.entity.service.EntityContextResolver;
import com.subskribe.billy.entity.service.EntityGetService;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.platformfeature.model.PlatformFeature;
import com.subskribe.billy.platformfeature.service.PlatformFeatureService;
import com.subskribe.billy.productcatalog.db.ProductCatalogDAO;
import com.subskribe.billy.productcatalog.model.Charge;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.model.PlanStatus;
import com.subskribe.billy.productcatalog.model.PriceTier;
import com.subskribe.billy.productcatalog.model.Product;
import com.subskribe.billy.productcatalog.ratecard.service.RateCardService;
import com.subskribe.billy.productcatalog.services.PlanIdGenerator;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.productcatalog.services.ProductCatalogService;
import com.subskribe.billy.productcatalog.services.ProductCategoryIdGenerator;
import com.subskribe.billy.productcatalog.services.ProductIdGenerator;
import com.subskribe.billy.productcatalog.services.UnitOfMeasureService;
import com.subskribe.billy.productcatalog.validation.CatalogValidation;
import com.subskribe.billy.productcatalog.validation.ChargeValidation;
import com.subskribe.billy.revrec.services.RevenueRecognitionGetService;
import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.shared.tenant.TenantId;
import com.subskribe.billy.taxjar.service.TaxJarIntegrationGetService;
import com.subskribe.billy.template.services.DocumentTemplateGetService;
import com.subskribe.billy.template.services.PlanTermsService;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Currency;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Provider;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.TransactionalCallable;
import org.jooq.impl.DefaultConfiguration;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

public class ProductCatalogServiceTest {

    private static final String PRODUCT_ID = "PRD-123";
    private static final TenantId TENANT_ID = new TenantId("test_tenant_id");

    private ProductCatalogDAO mockProductCatalogDao;
    private DSLContext mockDslContext;

    private static final Configuration JOOQ_CONFIGURATION_STUB = new DefaultConfiguration();

    private ProductCatalogService productCatalogService;

    private AvalaraIntegrationGetService mockAvalaraIntegrationGetService;

    private TaxJarIntegrationGetService mockTaxJarIntegrationGetService;

    private AnrokIntegrationGetService mockAnrokIntegrationGetService;

    private ChargeValidation chargeValidation;

    private CatalogValidation catalogValidation;

    private ArgumentCaptor<TransactionalCallable> jooqTransactionCaptor;

    @BeforeEach
    void setup() {
        mockDslContext = mock(DSLContext.class);
        mockProductCatalogDao = mock(ProductCatalogDAO.class);
        ProductCatalogGetService mockProductCatalogGetService = mock(ProductCatalogGetService.class);
        PlanIdGenerator mockPlanIdGenerator = mock(PlanIdGenerator.class);
        TenantSettingService mockTenantSettingService = TenantSettingServiceFixture.getDefault();
        UnitOfMeasureService mockUnitOfMeasureService = mock(UnitOfMeasureService.class);
        Provider<RevenueRecognitionGetService> mockRevenueRecognitionGetServiceProvider = mock(Provider.class);
        Provider<PlatformFeatureService> mockPlatformFeatureServiceProvider = mock(Provider.class);
        PlatformFeatureService mockPlatformFeatureService = mock(PlatformFeatureService.class);
        FeatureService mockFeatureService = mock(FeatureService.class);
        DocumentTemplateGetService mockDocumentTemplateGetService = mock(DocumentTemplateGetService.class);
        TenantIdProvider mockTenantIdProvider = mock(TenantIdProvider.class);
        DSLContextProvider mockDslContextProvider = mock(DSLContextProvider.class);
        CatalogRelationshipService mockCatalogRelationshipService = mock(CatalogRelationshipService.class);
        CatalogRelationshipGetService mockCatalogRelationshipGetService = mock(CatalogRelationshipGetService.class);
        ProductIdGenerator mockProductIdGenerator = mock(ProductIdGenerator.class);
        ProductCategoryIdGenerator mockProductCategoryIdGenerator = mock(ProductCategoryIdGenerator.class);
        PlanTermsService mockPlanTermsService = mock(PlanTermsService.class);
        mockAvalaraIntegrationGetService = mock(AvalaraIntegrationGetService.class);
        RateCardService mockRateCardService = mock(RateCardService.class);
        AccountingService mockAccountingService = mock(AccountingService.class);
        AccountingGetService mockAccountingGetService = mock(AccountingGetService.class);
        mockTaxJarIntegrationGetService = mock(TaxJarIntegrationGetService.class);
        EntityContextResolver mockEntityContextResolver = mock(EntityContextResolver.class);
        EntityGetService entityGetService = EntityGetServiceFixture.entityGetServiceFixture();
        FeatureService featureService = FeatureServiceFixture.allEnabled();
        mockAnrokIntegrationGetService = mock(AnrokIntegrationGetService.class);
        CustomFieldService customFieldService = mock(CustomFieldService.class);

        when(mockPlanIdGenerator.generate()).thenReturn("PLAN-123");
        var productName = "test product";
        Product product = new Product(
            UUID.randomUUID(),
            EntityFixture.ALL_ENTITY_IDS,
            PRODUCT_ID,
            false,
            productName,
            productName,
            null,
            null,
            null,
            Instant.now(),
            null
        );
        when(mockProductCatalogDao.getProduct(PRODUCT_ID)).thenReturn(Optional.of(product));
        when(mockPlatformFeatureServiceProvider.get()).thenReturn(mockPlatformFeatureService);
        when(mockPlatformFeatureService.getFeatureEnablement(PlatformFeature.REVENUE_RECOGNITION)).thenReturn(Optional.empty());
        // return the passed argument when calling resolveInputEntityIdsForGlobalObject
        when(mockEntityContextResolver.resolveInputEntityIdsForGlobalObject(any())).thenAnswer(invocation -> invocation.getArgument(0));

        chargeValidation = new ChargeValidation(
            mockTenantSettingService,
            mockUnitOfMeasureService,
            mockRevenueRecognitionGetServiceProvider,
            mockPlatformFeatureServiceProvider,
            mockAvalaraIntegrationGetService,
            mockFeatureService,
            mockRateCardService,
            mockTaxJarIntegrationGetService,
            mockAnrokIntegrationGetService
        );

        CatalogRelationshipValidator catalogRelationshipValidator = new CatalogRelationshipValidator(
            mockCatalogRelationshipGetService,
            mockProductCatalogGetService,
            mockFeatureService
        );

        catalogValidation = new CatalogValidation(
            chargeValidation,
            mockTenantSettingService,
            mockDocumentTemplateGetService,
            mockProductCatalogGetService,
            catalogRelationshipValidator,
            mockFeatureService
        );

        when(mockTenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID.getRequiredId());
        when(mockDslContextProvider.get(TENANT_ID.getRequiredId())).thenReturn(mockDslContext);

        productCatalogService = new ProductCatalogService(
            mockProductCatalogDao,
            mockProductIdGenerator,
            mockProductCategoryIdGenerator,
            mockPlanIdGenerator,
            catalogValidation,
            mockCatalogRelationshipService,
            mockDslContextProvider,
            mockPlanTermsService,
            mockTenantIdProvider,
            mockProductCatalogGetService,
            mockRateCardService,
            mockAccountingService,
            mockAccountingGetService,
            entityGetService,
            mockEntityContextResolver,
            featureService,
            mockDocumentTemplateGetService,
            customFieldService
        );
        jooqTransactionCaptor = ArgumentCaptor.forClass(TransactionalCallable.class);
    }

    @Test
    public void addPrepaidDrawdownPlan() {
        Charge drawdownCharge = getDrawdownCharge();
        Charge prepaidCharge = getPrepaidCharge();

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(drawdownCharge, prepaidCharge));

        when(mockProductCatalogDao.addPlanAndCharges(any(), any())).thenReturn(plan);
        when(mockDslContext.transactionResult(jooqTransactionCaptor.capture())).thenReturn(plan);

        productCatalogService.addPlanAndCharges(plan);

        verify(mockDslContext).transactionResult(jooqTransactionCaptor.capture());

        try {
            jooqTransactionCaptor.getValue().run(JOOQ_CONFIGURATION_STUB);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }

        verify(mockProductCatalogDao, times(1)).addPlanAndCharges(JOOQ_CONFIGURATION_STUB, plan);
    }

    @Test
    public void addPrepaidDrawdownPlanWithYearlyPrepaid() {
        Charge drawdownCharge = getDrawdownCharge();
        Charge prepaidCharge = getPrepaidCharge();
        prepaidCharge.setRecurrence(new Recurrence(Cycle.YEAR, 1));

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(drawdownCharge, prepaidCharge));

        when(mockProductCatalogDao.addPlanAndCharges(any(), any())).thenReturn(plan);
        when(mockDslContext.transactionResult(jooqTransactionCaptor.capture())).thenReturn(plan);

        productCatalogService.addPlanAndCharges(plan);

        verify(mockDslContext).transactionResult(jooqTransactionCaptor.capture());

        try {
            jooqTransactionCaptor.getValue().run(JOOQ_CONFIGURATION_STUB);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }

        verify(mockProductCatalogDao, times(1)).addPlanAndCharges(JOOQ_CONFIGURATION_STUB, plan);
    }

    @Test
    public void addPrepaidDrawdownPlanWithShorterPrepaidCycle_throws() {
        Charge drawdownCharge = getDrawdownCharge();
        drawdownCharge.setRecurrence(new Recurrence(Cycle.QUARTER, 1));
        Charge prepaidCharge = getPrepaidCharge();
        prepaidCharge.setRecurrence(new Recurrence(Cycle.MONTH, 1));

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(drawdownCharge, prepaidCharge));

        when(mockProductCatalogDao.addPlanAndCharges(any(), any())).thenReturn(plan);

        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("must be greater or equal to usage drawdown charge recurrence");
    }

    @Test
    public void validateMinimumCommitPlan() {
        Charge minimumCommitCharge = getCustomPricingCharge(ChargeType.RECURRING, ChargeModel.FLAT_FEE);
        minimumCommitCharge.setChargeId("CHRG-9999999");
        minimumCommitCharge.setRecurrence(new Recurrence(Cycle.MONTH, 1));
        minimumCommitCharge.setBillingTerm(BillingTerm.IN_ARREARS);
        minimumCommitCharge.setBillingCycle(BillingCycle.DEFAULT);
        Charge usageCharge = getUsageCharge();
        usageCharge.setChargeId("CHRG-5555555");
        usageCharge.setMinimumCommitBaseChargeId(minimumCommitCharge.getChargeId());

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(minimumCommitCharge, usageCharge));

        Assertions.assertDoesNotThrow(() -> catalogValidation.validatePlanAndCharges(plan));

        usageCharge.setMinimumCommitBaseChargeId("CHRG-3333333");
        assertThatThrownBy(() -> catalogValidation.validatePlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("Invalid base charge for minimum commit charge");
        usageCharge.setMinimumCommitBaseChargeId(minimumCommitCharge.getChargeId());

        minimumCommitCharge.setType(ChargeType.ONE_TIME);
        assertThatThrownBy(() -> catalogValidation.validatePlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("Minimum commit base charge must be recurring flat fee");
        minimumCommitCharge.setType(ChargeType.RECURRING);

        minimumCommitCharge.setChargeModel(ChargeModel.PER_UNIT);
        assertThatThrownBy(() -> catalogValidation.validatePlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("Minimum commit base charge must be recurring flat fee");
        minimumCommitCharge.setChargeModel(ChargeModel.FLAT_FEE);

        minimumCommitCharge.setRecurrence(new Recurrence(Cycle.YEAR, 1));
        assertThatThrownBy(() -> catalogValidation.validatePlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("minimum commit and usage charges must have same price cycles");
        minimumCommitCharge.setRecurrence(new Recurrence(Cycle.MONTH, 1));

        minimumCommitCharge.setBillingCycle(BillingCycle.MONTH);
        Assertions.assertDoesNotThrow(() -> catalogValidation.validatePlanAndCharges(plan));
        minimumCommitCharge.setBillingCycle(BillingCycle.DEFAULT);

        minimumCommitCharge.setBillingCycle(BillingCycle.YEAR);
        assertThatThrownBy(() -> catalogValidation.validatePlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("billing cycle");
        minimumCommitCharge.setBillingCycle(BillingCycle.DEFAULT);

        Assertions.assertDoesNotThrow(() -> catalogValidation.validatePlanAndCharges(plan));
    }

    @Test
    public void validateTaxRateRequired() {
        Charge charge = getPrepaidCharge();

        when(mockAvalaraIntegrationGetService.hasAvalaraIntegration()).thenReturn(false);
        Assertions.assertDoesNotThrow(() -> chargeValidation.validateCharge(charge));

        when(mockAvalaraIntegrationGetService.hasAvalaraIntegration()).thenReturn(true);
        assertThatThrownBy(() -> chargeValidation.validateCharge(charge)).isInstanceOf(InvalidInputException.class).hasMessageContaining("Tax rate");

        when(mockTaxJarIntegrationGetService.getTaxJarIntegration()).thenReturn(Optional.empty());
        assertThatThrownBy(() -> chargeValidation.validateCharge(charge)).isInstanceOf(InvalidInputException.class).hasMessageContaining("Tax rate");

        when(mockAnrokIntegrationGetService.getAnrokIntegration()).thenReturn(Optional.empty());
        assertThatThrownBy(() -> chargeValidation.validateCharge(charge)).isInstanceOf(InvalidInputException.class).hasMessageContaining("Tax rate");

        charge.setTaxRateId(UUID.randomUUID());
        Assertions.assertDoesNotThrow(() -> chargeValidation.validateCharge(charge));
    }

    @Test
    public void validateChargeBillingCycle() {
        Charge charge = getRecurringCharge();

        charge.setBillingCycle(BillingCycle.CHARGE_RECURRENCE);
        Assertions.assertDoesNotThrow(() -> chargeValidation.validateCharge(charge));

        charge.setType(ChargeType.USAGE);
        assertThatThrownBy(() -> chargeValidation.validateCharge(charge))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("Charge recurrence billing cycle");

        charge.setType(ChargeType.ONE_TIME);
        assertThatThrownBy(() -> chargeValidation.validateCharge(charge))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("Charge recurrence billing cycle");
    }

    @Test
    public void whenTwoChargesAreAddedToPlanWithPercentOf_thenValidationFailsAsExpected() {
        Charge prepaidCharge = getPrepaidCharge();
        Charge percentOfCharge = getPercentOfCharge();

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(percentOfCharge, prepaidCharge));
        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("No other charges are allowed");
    }

    @Test
    public void whenRecurrenceIsPresentForPercentCharge_thenValidationFailsAsExpected() {
        Charge percentOfCharge = getPercentOfCharge();
        percentOfCharge.setRecurrence(new Recurrence(Cycle.YEAR, 1));

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(percentOfCharge));
        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("recurrence is not required");
    }

    @Test
    public void whenWrongChargeModelIsPresentForPercentCharge_thenValidationFailsAsExpected() {
        Charge percentOfCharge = getPercentOfCharge();
        percentOfCharge.setChargeModel(ChargeModel.VOLUME);

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(percentOfCharge));
        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("only Per Unit or Flat Fee charge model");
    }

    @Test
    public void whenPriceIsPresentForPercentOfCharge_thenValidationFailsAsExpected() {
        Charge percentOfCharge = getPercentOfCharge();
        percentOfCharge.setAmount(BigDecimal.valueOf(100));

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(percentOfCharge));
        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("no notion of price or price tiers");
    }

    @Test
    public void whenWrongValuePercentIsSpecified_thenValidationFailsAsExpected() {
        Charge percentOfCharge = getPercentOfCharge();
        percentOfCharge.setPercent(BigDecimal.valueOf(101));

        Plan plan1 = makeSamplePlan();
        plan1.addCharges(List.of(percentOfCharge));
        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan1))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("greater than or equal to 0 and lesser than 100");

        percentOfCharge = getPercentOfCharge();
        percentOfCharge.setPercent(BigDecimal.valueOf(0));

        Plan plan2 = makeSamplePlan();
        plan2.addCharges(List.of(percentOfCharge));
        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan2))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("greater than or equal to 0 and lesser than 100");
    }

    @Test
    public void whenValidPercentOfChargeIsAdded_thenPlanWithChargeIsAddedSuccessfully() {
        Charge percentOfCharge = getPercentOfCharge();
        percentOfCharge.setPercent(BigDecimal.valueOf(66));

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(percentOfCharge));

        when(mockProductCatalogDao.addPlanAndCharges(any(), any())).thenReturn(plan);
        when(mockDslContext.transactionResult(jooqTransactionCaptor.capture())).thenReturn(plan);

        productCatalogService.addPlanAndCharges(plan);

        verify(mockDslContext).transactionResult(jooqTransactionCaptor.capture());

        try {
            jooqTransactionCaptor.getValue().run(JOOQ_CONFIGURATION_STUB);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }

        verify(mockProductCatalogDao, times(1)).addPlanAndCharges(JOOQ_CONFIGURATION_STUB, plan);
    }

    @Test
    public void addPrepaidDrawdownWithNonPerUnitChargeModel() {
        Charge drawdownCharge = getDrawdownCharge();
        drawdownCharge.setChargeModel(ChargeModel.VOLUME);
        Charge prepaidCharge = getPrepaidCharge();

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(drawdownCharge, prepaidCharge));
        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("\"Per Unit\" charge model is supported");
    }

    @Test
    public void addPrepaidDrawdownPlanBillingInArrears() {
        Charge drawdownCharge = getDrawdownCharge();
        Charge prepaidCharge = getPrepaidCharge();
        prepaidCharge.setBillingTerm(BillingTerm.IN_ARREARS);

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(drawdownCharge, prepaidCharge));
        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("billing term");
    }

    @Test
    public void addPrepaidDrawdownPlanWithTooManyDrawdownCharges() {
        Charge drawdownCharge1 = getDrawdownCharge();
        Charge drawdownCharge2 = getDrawdownCharge();
        Charge prepaidCharge = getPrepaidCharge();

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(drawdownCharge1, drawdownCharge2, prepaidCharge));

        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("Only one drawdown charge allowed per plan");
    }

    @Test
    public void addDrawdownChargeWithoutPrepaidCharge() {
        Charge drawdownCharge = getDrawdownCharge();

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(drawdownCharge));

        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("Prepaid charge is required for a plan containing a drawdown charge");
    }

    @Test
    public void addCustomChargeWithAmountFails() {
        Charge customCharge = getCustomPricingCharge(ChargeType.ONE_TIME, ChargeModel.FLAT_FEE);
        customCharge.setAmount(BigDecimal.TEN);

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(customCharge));

        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessage("custom pricing charge cannot have a predefined amount");
    }

    @Test
    public void addCustomChargeWithWrongTypeFails() {
        Charge customCharge = getCustomPricingCharge(ChargeType.USAGE, ChargeModel.FLAT_FEE);

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(customCharge));

        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("custom pricing charge can only be");
    }

    @Test
    public void addCustomChargeWithWrongModelFails() {
        Charge customCharge = getCustomPricingCharge(ChargeType.ONE_TIME, ChargeModel.BLOCK);

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(customCharge));

        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessage("custom pricing charge can only be have a flat fee or per unit model");
    }

    @Test
    public void addCustomChargeWithPriceTierFails() {
        Charge customCharge = getCustomPricingCharge(ChargeType.ONE_TIME, ChargeModel.PER_UNIT);
        customCharge.setPriceTiers(List.of(PriceTier.of(5L, BigDecimal.ZERO), PriceTier.of(10L, BigDecimal.ZERO)));

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(customCharge));

        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessage("custom pricing charge cannot have price tiers");
    }

    @Test
    public void addCustomChargeWithPercentFails() {
        Charge customCharge = getCustomPricingCharge(ChargeType.ONE_TIME, ChargeModel.PER_UNIT);
        customCharge.setPercent(BigDecimal.TEN);

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(customCharge));

        assertThatThrownBy(() -> productCatalogService.addPlanAndCharges(plan))
            .isInstanceOf(InvalidInputException.class)
            .hasMessage("custom pricing charge cannot have percent");
    }

    @Test
    public void addCustomChargeWithOneTimePerUnitSucceeds() {
        Charge customCharge = getCustomPricingCharge(ChargeType.ONE_TIME, ChargeModel.PER_UNIT);

        Plan plan = makeSamplePlan();
        plan.addCharges(List.of(customCharge));

        when(mockProductCatalogDao.addPlanAndCharges(any(), any())).thenReturn(plan);
        when(mockDslContext.transactionResult(jooqTransactionCaptor.capture())).thenReturn(plan);

        productCatalogService.addPlanAndCharges(plan);

        verify(mockDslContext).transactionResult(jooqTransactionCaptor.capture());

        try {
            jooqTransactionCaptor.getValue().run(JOOQ_CONFIGURATION_STUB);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }

        verify(mockProductCatalogDao, times(1)).addPlanAndCharges(JOOQ_CONFIGURATION_STUB, plan);
    }

    private Plan makeSamplePlan() {
        Plan plan = new Plan();
        plan.setName("test plan");
        plan.setProductId(PRODUCT_ID);
        plan.setCurrency(Currency.getInstance("USD"));
        plan.setStatus(PlanStatus.ACTIVE);
        return plan;
    }

    private static Charge getRecurringCharge() {
        Charge charge = new Charge();
        charge.setName("recurring charge");
        charge.setChargeModel(ChargeModel.PER_UNIT);
        charge.setType(ChargeType.RECURRING);
        charge.setAmount(BigDecimal.ONE);
        charge.setRecurrence(new Recurrence(Cycle.MONTH, 1));
        return charge;
    }

    private static Charge getPrepaidCharge() {
        Charge charge = new Charge();
        charge.setName("prepaid charge");
        charge.setChargeModel(ChargeModel.PER_UNIT);
        charge.setType(ChargeType.PREPAID);
        charge.setDrawdown(false);
        charge.setAmount(BigDecimal.ONE);
        return charge;
    }

    private static Charge getUsageCharge() {
        Charge charge = new Charge();
        charge.setName("usage charge");
        charge.setChargeModel(ChargeModel.PER_UNIT);
        charge.setType(ChargeType.USAGE);
        charge.setAmount(BigDecimal.ZERO);
        charge.setRecurrence(new Recurrence(Cycle.MONTH, 1));
        return charge;
    }

    private static Charge getDrawdownCharge() {
        Charge charge = getUsageCharge();
        charge.setName("drawdown charge");
        charge.setDrawdown(true);
        return charge;
    }

    private static Charge getPercentOfCharge() {
        Charge charge = new Charge();
        charge.setName("percent of charge");
        charge.setChargeModel(ChargeModel.FLAT_FEE);
        charge.setType(ChargeType.PERCENTAGE_OF);
        charge.setDrawdown(false);
        return charge;
    }

    private static Charge getCustomPricingCharge(ChargeType chargeType, ChargeModel chargeModel) {
        Charge charge = new Charge();
        charge.setName("custom pricing charge");
        charge.setType(chargeType);
        charge.setChargeModel(chargeModel);
        charge.setCustom(true);
        return charge;
    }
}
