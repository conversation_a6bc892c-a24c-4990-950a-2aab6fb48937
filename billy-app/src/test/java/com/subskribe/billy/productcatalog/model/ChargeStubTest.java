package com.subskribe.billy.productcatalog.model;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.shared.enums.BillingCycle;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.ChargeModel;
import com.subskribe.billy.shared.enums.ChargeType;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.temporal.Recurrence;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import nl.jqno.equalsverifier.EqualsVerifier;
import org.junit.jupiter.api.Test;

public class ChargeStubTest {

    @Test
    public void chargeStubEqualsVerifier() {
        EqualsVerifier.simple().forClass(ChargeStub.class).verify();
    }

    @Test
    public void testChargeModel() {
        ChargeStub charge = defaultChargeStub().build();
        assertThat(charge.getBillingCycle()).isEqualTo(BillingCycle.DEFAULT);

        // prepaid charges without recurrence do not override billing cycle
        charge = copy(charge).type(ChargeType.PREPAID).build();
        assertThat(charge.getBillingCycle()).isEqualTo(BillingCycle.DEFAULT);

        // prepaid charges with recurrence override billing cycle
        charge = copy(charge).type(ChargeType.PREPAID).recurrence(new Recurrence(Cycle.MONTH, 1)).build();
        assertThat(charge.getBillingCycle()).isEqualTo(BillingCycle.MONTH);
    }

    static ImmutableChargeStub.Builder defaultChargeStub() {
        return ImmutableChargeStub.builder()
            .id(UUID.randomUUID())
            .chargeId("chargeId")
            .planId("PLAN-1234567")
            .planUuid(UUID.randomUUID())
            .type(ChargeType.ONE_TIME)
            .chargeModel(ChargeModel.PER_UNIT)
            .name("chargeName")
            .description("chargeDescription")
            .amount(new BigDecimal(100))
            .billingTerm(BillingTerm.UP_FRONT)
            .billingCycleInternal(BillingCycle.DEFAULT)
            .shouldTrackArr(false)
            .isDrawdown(false)
            .isCustom(false)
            .isListPriceEditable(false)
            .isCreditable(false)
            .isDiscount(false)
            .isEventBased(false)
            .isRenewable(false)
            .isEventBased(false)
            .isRenewable(false)
            .createdOn(Instant.now())
            .updatedOn(Instant.now());
    }

    static ImmutableChargeStub.Builder copy(ChargeStub charge) {
        return ImmutableChargeStub.builder().from(charge);
    }
}
