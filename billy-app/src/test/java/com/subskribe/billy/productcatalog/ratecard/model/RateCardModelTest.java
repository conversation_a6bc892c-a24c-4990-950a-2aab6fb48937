package com.subskribe.billy.productcatalog.ratecard.model;

import com.subskribe.billy.productcatalog.priceattribute.model.ImmutablePriceAttribute;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttribute;
import com.subskribe.billy.productcatalog.priceattribute.model.PriceAttributeType;
import com.subskribe.billy.productcatalog.ratecard.RateCardTestData;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import nl.jqno.equalsverifier.EqualsVerifier;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class RateCardModelTest {

    @Test
    public void rateCardPriceResolutionWorksAsExpected() {
        RateCard rateCard = RateCardTestData.makeTestRateCard();

        Optional<BigDecimal> priceOne = rateCard.resolvePrice(RateCardTestData.ATTRIBUTE_REFERENCES_COMBINATION_ONE);
        Optional<BigDecimal> priceTwo = rateCard.resolvePrice(RateCardTestData.ATTRIBUTE_REFERENCES_COMBINATION_TWO);
        Optional<BigDecimal> priceThree = rateCard.resolvePrice(RateCardTestData.ATTRIBUTE_REFERENCES_COMBINATION_THREE);
        Optional<BigDecimal> priceNotPresent = rateCard.resolvePrice(RateCardTestData.ATTRIBUTE_REFERENCES_COMBINATION_NOT_PRESENT);

        Assertions.assertThat(priceOne).isPresent();
        Assertions.assertThat(priceTwo).isPresent();
        Assertions.assertThat(priceThree).isPresent();
        Assertions.assertThat(priceNotPresent).isEmpty();

        Assertions.assertThat(priceOne.get().compareTo(RateCardTestData.ATTRIBUTE_REFERENCES_ONE_PRICE)).isEqualTo(0);
        Assertions.assertThat(priceTwo.get().compareTo(RateCardTestData.ATTRIBUTE_REFERENCES_TWO_PRICE)).isEqualTo(0);
        Assertions.assertThat(priceThree.get().compareTo(RateCardTestData.ATTRIBUTE_REFERENCES_THREE_PRICE)).isEqualTo(0);
    }

    @Test
    public void rateCardSerDeWorksAsExpected() throws Exception {
        RateCard rateCard = RateCardTestData.makeTestRateCard();
        String rateCardString = JacksonProvider.defaultMapper().writeValueAsString(rateCard);
        RateCard loaded = JacksonProvider.defaultMapper().readValue(rateCardString, RateCard.class);
        Assertions.assertThat(rateCard).isEqualTo(loaded);
    }

    @Test
    public void verifyRateCardEquals() {
        EqualsVerifier.simple().forClass(RateCard.class).verify();
        EqualsVerifier.simple().forClass(AttributeReference.class).verify();
        EqualsVerifier.simple().forClass(PriceTableElement.class).verify();
        EqualsVerifier.simple().forClass(ImmutablePriceAttribute.class).verify();
    }

    @Test
    public void priceAttributeSerDeWorksAsExpected() throws Exception {
        PriceAttribute priceAttribute = makePriceAttribute();
        String priceAttributeString = JacksonProvider.defaultMapper().writeValueAsString(priceAttribute);
        PriceAttribute loaded = JacksonProvider.defaultMapper().readValue(priceAttributeString, PriceAttribute.class);
        Assertions.assertThat(priceAttribute).isEqualTo(loaded);
    }

    private static PriceAttribute makePriceAttribute() {
        String id = RandomStringUtils.randomAlphanumeric(7).toUpperCase();
        return ImmutablePriceAttribute.builder()
            .id(id)
            .name("Name")
            .type(PriceAttributeType.PRICE_TABLE_RESOLVING)
            .values(List.of("Boom", "Bust"))
            .build();
    }
}
