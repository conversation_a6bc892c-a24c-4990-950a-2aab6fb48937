package com.subskribe.billy.salesforce;

import com.subskribe.billy.salesforce.model.SalesforcePackage;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class SalesforcePackageTest {

    private static final String DURABLE_ID = "subskribe";
    private static final SalesforcePackage VERSION_1_9 = new SalesforcePackage(DURABLE_ID, "1", "9");
    private static final SalesforcePackage VERSION_1_10 = new SalesforcePackage(DURABLE_ID, "1", "10");
    private static final SalesforcePackage VERSION_1_11 = new SalesforcePackage(DURABLE_ID, "1", "11");
    private static final SalesforcePackage VERSION_2_1 = new SalesforcePackage(DURABLE_ID, "2", "1");

    @Test
    public void testComparePackage() {
        Assertions.assertThat(VERSION_1_10.compareTo(VERSION_1_10)).isEqualTo(0);
        Assertions.assertThat(VERSION_1_9.compareTo(VERSION_1_10)).isEqualTo(-1);
        Assertions.assertThat(VERSION_1_10.compareTo(VERSION_1_9)).isEqualTo(1);
        Assertions.assertThat(VERSION_1_10.compareTo(VERSION_1_11)).isEqualTo(-1);
        Assertions.assertThat(VERSION_1_10.compareTo(VERSION_2_1)).isEqualTo(-1);
        Assertions.assertThat(VERSION_2_1.compareTo(VERSION_1_9)).isEqualTo(1);
    }
}
