package com.subskribe.billy.salesforce;

import com.opencsv.bean.HeaderColumnNameMappingStrategy;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import com.opencsv.exceptions.CsvRequiredFieldEmptyException;
import com.subskribe.billy.io.CSVMapper;
import com.subskribe.billy.salesforce.model.subscription.SubskribeSalesforceSubscriptionLine;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.List;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class CSVTest {

    @Test
    public void SerDeSubscriptionLine() throws CsvRequiredFieldEmptyException, CsvDataTypeMismatchException, IOException {
        var id = "ID";
        var chargeId = "chargeId";
        var chargeName = "chargeName";
        var currency = "USD";
        var lineEndDate = "2022-01-01";
        var lineStartDate = "2021-01-01";
        var planId = "planId";
        var planName = "planName";
        var productCategory = "productCategory";
        var productId = "productId";
        var productName = "productName";
        var quantity = 42;
        var sellUnitPrice = BigDecimal.valueOf(10);
        var status = "open";
        var subscriptionCrmId = "crmId";
        var endDate = "2022-01-01";
        var startDate = "2021-01-01";
        var termLengthInYears = BigDecimal.ONE;
        var discountPercent = BigDecimal.valueOf(13.5);

        var subscriptionLine = new SubskribeSalesforceSubscriptionLine();
        subscriptionLine.setId(id);
        subscriptionLine.setChargeId(chargeId);
        subscriptionLine.setChargeName(chargeName);
        subscriptionLine.setCurrency(currency);
        subscriptionLine.setLineEndDate(lineEndDate);
        subscriptionLine.setLineStartDate(lineStartDate);
        subscriptionLine.setPlanId(planId);
        subscriptionLine.setPlanName(planName);
        subscriptionLine.setProductCategory(productCategory);
        subscriptionLine.setProductId(productId);
        subscriptionLine.setProductName(productName);
        subscriptionLine.setQuantity(BigDecimal.valueOf(quantity));
        subscriptionLine.setSellUnitPrice(sellUnitPrice);
        subscriptionLine.setStatus(status);
        subscriptionLine.setSubscriptionCrmId(subscriptionCrmId);
        subscriptionLine.setEndDate(endDate);
        subscriptionLine.setStartDate(startDate);
        subscriptionLine.setTermLengthInYears(termLengthInYears);
        subscriptionLine.setDiscountPercent(discountPercent);

        var mappingStrategy = new HeaderColumnNameMappingStrategy<SubskribeSalesforceSubscriptionLine>();
        mappingStrategy.setType(SubskribeSalesforceSubscriptionLine.class);
        var byteArr = CSVMapper.objectToCsvByteArray(List.of(subscriptionLine), mappingStrategy);

        var lines = CSVMapper.csvByteArrayToObject(byteArr, SubskribeSalesforceSubscriptionLine.class);
        Assertions.assertThat(lines).hasSize(1);

        var receivedLine = lines.get(0);
        Assertions.assertThat(subscriptionLine.salesforceEquals(receivedLine)).isTrue();
    }

    @Test
    public void SerDeSubscriptionLineWithDroppedFields()
        throws CsvRequiredFieldEmptyException, CsvDataTypeMismatchException, IOException, NoSuchFieldException {
        var id = "ID";
        var chargeId = "chargeId";
        var chargeName = "chargeName";
        var currency = "USD";
        var lineEndDate = "2022-01-01";
        var lineStartDate = "2021-01-01";
        var planId = "planId";
        var planName = "planName";
        var productCategory = "productCategory";
        var productId = "productId";
        var productName = "productName";
        var quantity = 42;
        var sellUnitPrice = BigDecimal.valueOf(10);
        var status = "open";
        var subscriptionCrmId = "crmId";
        var endDate = "2022-01-01";
        var startDate = "2021-01-01";
        var termLengthInYears = BigDecimal.ONE;
        var discountPercent = BigDecimal.valueOf(13.5);

        var subscriptionLine = new SubskribeSalesforceSubscriptionLine();
        subscriptionLine.setId(id);
        subscriptionLine.setChargeId(chargeId);
        subscriptionLine.setChargeName(chargeName);
        subscriptionLine.setCurrency(currency);
        subscriptionLine.setLineEndDate(lineEndDate);
        subscriptionLine.setLineStartDate(lineStartDate);
        subscriptionLine.setPlanId(planId);
        subscriptionLine.setPlanName(planName);
        subscriptionLine.setProductCategory(productCategory);
        subscriptionLine.setProductId(productId);
        subscriptionLine.setProductName(productName);
        subscriptionLine.setQuantity(BigDecimal.valueOf(quantity));
        subscriptionLine.setSellUnitPrice(sellUnitPrice);
        subscriptionLine.setStatus(status);
        subscriptionLine.setSubscriptionCrmId(subscriptionCrmId);
        subscriptionLine.setEndDate(endDate);
        subscriptionLine.setStartDate(startDate);
        subscriptionLine.setTermLengthInYears(termLengthInYears);
        subscriptionLine.setDiscountPercent(discountPercent);

        var mappingStrategy = new HeaderColumnNameMappingStrategy<SubskribeSalesforceSubscriptionLine>();
        mappingStrategy.setType(SubskribeSalesforceSubscriptionLine.class);
        mappingStrategy.ignoreFields(getIgnoredCurrencyMap());
        var byteArr = CSVMapper.objectToCsvByteArray(List.of(subscriptionLine), mappingStrategy);

        var lines = CSVMapper.csvByteArrayToObject(byteArr, SubskribeSalesforceSubscriptionLine.class);
        Assertions.assertThat(lines).hasSize(1);

        var receivedLine = lines.get(0);
        Assertions.assertThat(receivedLine.getCurrency()).isNullOrEmpty();
        Assertions.assertThat(subscriptionLine.salesforceEquals(receivedLine)).isTrue();
    }

    private MultiValuedMap<Class<?>, Field> getIgnoredCurrencyMap() throws NoSuchFieldException {
        var toReturn = new ArrayListValuedHashMap<Class<?>, Field>();
        var field = SubskribeSalesforceSubscriptionLine.class.getDeclaredField("currency");
        toReturn.putAll(SubskribeSalesforceSubscriptionLine.class, List.of(field));
        return toReturn;
    }
}
