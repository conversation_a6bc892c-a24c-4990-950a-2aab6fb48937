package com.subskribe.billy.salesforce.model.customfield;

import nl.jqno.equalsverifier.EqualsVerifier;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class SalesforceCustomFieldTest {

    @Test
    void testEquals() {
        EqualsVerifier.simple().forClass(SalesforceCustomField.class).verify();
    }

    @Test
    void testSalesforceEquals() {
        SalesforceCustomField salesforceCustomFieldObject1 = new SalesforceCustomField();
        salesforceCustomFieldObject1.setQualifiedApiName("qualifiedApiName");
        salesforceCustomFieldObject1.setDataType("dataType");

        SalesforceCustomField salesforceCustomFieldObject2 = new SalesforceCustomField();
        salesforceCustomFieldObject2.setQualifiedApiName("qualifiedApiName");
        salesforceCustomFieldObject2.setDataType("dataType");

        SalesforceCustomField salesforceCustomFieldObject3 = new SalesforceCustomField();
        salesforceCustomFieldObject3.setQualifiedApiName("qualifiedApiName3");
        salesforceCustomFieldObject3.setDataType("dataType3");

        Assertions.assertThat(salesforceCustomFieldObject1.salesforceEquals(salesforceCustomFieldObject2)).isTrue();
        Assertions.assertThat(salesforceCustomFieldObject1.salesforceEquals(salesforceCustomFieldObject3)).isFalse();
    }
}
