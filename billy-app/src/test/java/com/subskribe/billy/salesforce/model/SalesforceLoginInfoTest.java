package com.subskribe.billy.salesforce.model;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import nl.jqno.equalsverifier.EqualsVerifier;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class SalesforceLoginInfoTest {

    @Test
    void testEquals() {
        EqualsVerifier.simple().forClass(SalesforceLoginInfo.class).verify();
    }

    @Test
    void testJsonRoundtrip() throws Exception {
        ObjectMapper objectMapper = JacksonProvider.defaultMapper();
        SalesforceLoginInfo salesforceLoginInfo = new SalesforceLoginInfo();
        salesforceLoginInfo.setAccessToken(RandomStringUtils.random(50));
        salesforceLoginInfo.setRefreshToken(RandomStringUtils.random(50));
        salesforceLoginInfo.setIsMultiCurrency(true);
        salesforceLoginInfo.setInstanceUrl(RandomStringUtils.random(50));
        String jsonValue = objectMapper.writeValueAsString(salesforceLoginInfo);
        SalesforceLoginInfo deserialized = objectMapper.readValue(jsonValue, SalesforceLoginInfo.class);
        assertThat(deserialized).isEqualTo(salesforceLoginInfo);
    }
}
