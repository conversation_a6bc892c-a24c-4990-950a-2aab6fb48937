package com.subskribe.billy.validation.helpers;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.shared.pecuniary.Discount;
import java.util.Comparator;
import java.util.function.Function;
import java.util.stream.Collectors;

public class OrderValidation {

    public static void validateOrder(Order expected, Order actual) {
        if (expected == null && actual == null) {
            return;
        }

        assertNotNull(expected);
        assertNotNull(actual);

        assertEquals(expected.getAccountId(), actual.getAccountId());
        assertEquals(expected.getTenantId(), actual.getTenantId());
        assertEquals(expected.getBillingContactId(), actual.getBillingContactId());
        assertEquals(expected.getShippingContactId(), actual.getShippingContactId());
        assertEquals(expected.getTermLength(), actual.getTermLength());
        assertEquals(expected.getBillingCycle(), actual.getBillingCycle());
        assertEquals(expected.getBillingTerm(), actual.getBillingTerm());
        assertEquals(expected.getPaymentTerm(), actual.getPaymentTerm());
        assertEquals(expected.getCurrency(), actual.getCurrency());
        assertEquals(expected.getExternalSubscriptionId(), actual.getExternalSubscriptionId());
        assertEquals(expected.getStatus(), actual.getStatus());
        assertEquals(expected.getStartDate(), actual.getStartDate());
        // end date should be set as it is missing in input
        assertNotNull(actual.getEndDate());
        assertEquals(expected.getLineItems().size(), actual.getLineItems().size());
        assertEquals(expected.getSubscriptionTargetVersion(), actual.getSubscriptionTargetVersion());
        assertEquals(expected.getTotalAmount().doubleValue(), actual.getTotalAmount().doubleValue());
        assertEquals(expected.getTotalListAmount().doubleValue(), actual.getTotalListAmount().doubleValue());
        assertEquals(expected.getPurchaseOrderNumber(), actual.getPurchaseOrderNumber());
        assertEquals(expected.getPurchaseOrderRequiredForInvoicing(), actual.getPurchaseOrderRequiredForInvoicing());

        var expectedLineItemMap = expected.getLineItems().stream().collect(Collectors.groupingBy(OrderLineItem::getChargeId));
        var actualLineItemMap = actual.getLineItems().stream().collect(Collectors.groupingBy(OrderLineItem::getChargeId));
        expectedLineItemMap
            .keySet()
            .forEach(c -> {
                assertEquals(expectedLineItemMap.get(c).size(), actualLineItemMap.get(c).size());

                // order lineItems by quantity
                expectedLineItemMap.get(c).sort(Comparator.comparing(OrderLineItem::getQuantity));
                actualLineItemMap.get(c).sort(Comparator.comparing(OrderLineItem::getQuantity));

                for (int i = 0; i < expectedLineItemMap.get(c).size(); i++) {
                    validateOrderLineItem(expectedLineItemMap.get(c).get(i), actualLineItemMap.get(c).get(i));
                }
            });
    }

    private static void validateOrderLineItem(OrderLineItem expected, OrderLineItem actual) {
        if (expected == null && actual == null) {
            return;
        }

        assertNotNull(expected);
        assertNotNull(actual);

        assertEquals(expected.getAction(), actual.getAction());
        assertEquals(expected.getTenantId(), actual.getTenantId());
        assertEquals(expected.getPlanId(), actual.getPlanId());
        assertEquals(expected.getChargeId(), actual.getChargeId());
        assertEquals(expected.getQuantity(), actual.getQuantity());

        assertEquals(expected.getSellUnitPrice().doubleValue(), actual.getSellUnitPrice().doubleValue());
        assertEquals(expected.getListUnitPrice().doubleValue(), actual.getListUnitPrice().doubleValue());
        assertEquals(expected.getAmount().doubleValue(), actual.getAmount().doubleValue());
        assertEquals(expected.getListAmount().doubleValue(), actual.getListAmount().doubleValue());
        assertEquals(expected.getArrOverride(), actual.getArrOverride());

        assertEquals(expected.getEffectiveDate(), actual.getEffectiveDate());
        assertEquals(expected.getEndDate(), actual.getEndDate());

        if (!(actual.getDiscounts() == null && expected.getDiscounts() == null)) {
            var expectedDiscountMap = expected.getDiscounts().stream().collect(Collectors.toMap(Discount::getName, Function.identity()));
            var actualDiscountMap = actual.getDiscounts().stream().collect(Collectors.toMap(Discount::getName, Function.identity()));
            expectedDiscountMap.keySet().forEach(k -> assertEquals(expectedDiscountMap.get(k).getPercent(), actualDiscountMap.get(k).getPercent()));
        }
    }
}
