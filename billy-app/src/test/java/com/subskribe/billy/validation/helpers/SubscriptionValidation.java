package com.subskribe.billy.validation.helpers;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.subskribe.billy.shared.pecuniary.Discount;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class SubscriptionValidation {

    public static void validateEntity(SubscriptionEntity expected, Subscription actual) {
        if (expected == null && actual == null) {
            return;
        }

        assertNotNull(expected);
        assertNotNull(actual);

        assertEquals(expected.getTenantId(), actual.getTenantId());
        assertEquals(expected.getAccountId(), actual.getAccountId());
        assertEquals(expected.getBillingTerm(), actual.getBillingTerm());
        assertEquals(expected.getPaymentTerm(), actual.getPaymentTerm());
        assertEquals(expected.getState(), actual.getState());
        assertEquals(expected.getStartDate(), actual.getStartDate());
        assertEquals(expected.getEndDate(), actual.getEndDate());
        assertEquals(expected.getBillingAnchorDate(), actual.getBillingAnchorDate());
        assertEquals(expected.getTermLength().getCycle(), actual.getTermLength().getCycle());
        assertEquals(expected.getTermLength().getStep(), actual.getTermLength().getStep());
        assertEquals(expected.getBillingCycle().getCycle(), actual.getBillingCycle().getCycle());
        assertEquals(expected.getBillingCycle().getStep(), actual.getBillingCycle().getStep());
        assertNotNull(actual.getCreationTime());
        assertEquals(expected.getVersion(), actual.getVersion());
    }

    public static void validateCharges(List<SubscriptionCharge> expectedCharges, List<SubscriptionCharge> actualCharges) {
        if (expectedCharges == null && actualCharges == null) {
            return;
        }

        assertNotNull(expectedCharges);
        assertNotNull(actualCharges);

        var expectedChargesMap = expectedCharges.stream().collect(Collectors.groupingBy(SubscriptionCharge::getChargeId));
        var actualChargesMap = actualCharges.stream().collect(Collectors.groupingBy(SubscriptionCharge::getChargeId));

        expectedChargesMap
            .keySet()
            .forEach(c -> {
                var expectedChargesList = expectedChargesMap.get(c);
                var actualChargesList = actualChargesMap.get(c);
                assertEquals(expectedChargesList.size(), actualChargesList.size());

                for (int i = 0; i < expectedChargesList.size(); i++) {
                    validateCharge(expectedChargesList.get(i), actualChargesList.get(i));
                }
            });
    }

    private static void validateCharge(SubscriptionCharge expected, SubscriptionCharge actual) {
        if (expected == null && actual == null) {
            return;
        }
        assertNotNull(expected);
        assertNotNull(actual);

        assertEquals(expected.getVersion(), actual.getVersion());
        assertEquals(expected.getChargeId(), actual.getChargeId());
        assertEquals(expected.getAccountId(), actual.getAccountId());
        assertEquals(expected.getTenantId(), actual.getTenantId());
        assertEquals(expected.getStartDate(), actual.getStartDate());
        assertEquals(expected.getEndDate(), actual.getEndDate());
        assertEquals(expected.getQuantity(), actual.getQuantity());
        if (!(actual.getDiscounts() == null && expected.getDiscounts() == null)) {
            var expectedDiscountMap = expected.getDiscounts().stream().collect(Collectors.toMap(Discount::getName, Function.identity()));
            var actualDiscountMap = actual.getDiscounts().stream().collect(Collectors.toMap(Discount::getName, Function.identity()));
            expectedDiscountMap.keySet().forEach(k -> assertEquals(expectedDiscountMap.get(k).getPercent(), actualDiscountMap.get(k).getPercent()));
        }
    }
}
