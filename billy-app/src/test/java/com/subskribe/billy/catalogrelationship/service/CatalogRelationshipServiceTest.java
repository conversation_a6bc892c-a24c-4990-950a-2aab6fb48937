package com.subskribe.billy.catalogrelationship.service;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.subskribe.billy.catalogrelationship.db.CatalogRelationshipDAO;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationship;
import com.subskribe.billy.catalogrelationship.model.CatalogRelationshipType;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.productcatalog.model.Plan;
import com.subskribe.billy.productcatalog.services.ProductCatalogGetService;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.util.Currency;
import org.jooq.Configuration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CatalogRelationshipServiceTest {

    private CatalogRelationshipIdGenerator mockCatalogRelationshipIdGenerator;
    private ProductCatalogGetService mockProductCatalogGetService;
    private CatalogRelationshipGetService mockCatalogRelationshipGetService;
    private CatalogRelationshipService catalogRelationshipService;
    private Configuration mockConfiguration;

    @BeforeEach
    void setup() {
        mockConfiguration = mock(Configuration.class);
        DSLContextProvider mockDSLContextProvider = mock(DSLContextProvider.class);
        TenantIdProvider mockTenantIdProvider = mock(TenantIdProvider.class);
        mockCatalogRelationshipIdGenerator = mock(CatalogRelationshipIdGenerator.class);
        mockProductCatalogGetService = mock(ProductCatalogGetService.class);
        mockCatalogRelationshipGetService = mock(CatalogRelationshipGetService.class);
        FeatureService mockFeatureService = mock(FeatureService.class);
        CatalogRelationshipDAO mockCatalogRelationshipDAO = mock(CatalogRelationshipDAO.class);
        CatalogRelationshipValidator mockCatalogRelationshipValidator = new CatalogRelationshipValidator(
            mockCatalogRelationshipGetService,
            mockProductCatalogGetService,
            mockFeatureService
        );
        catalogRelationshipService = new CatalogRelationshipService(
            mockDSLContextProvider,
            mockTenantIdProvider,
            mockCatalogRelationshipIdGenerator,
            mockProductCatalogGetService,
            mockFeatureService,
            mockCatalogRelationshipDAO,
            mockCatalogRelationshipGetService,
            mockCatalogRelationshipValidator
        );
    }

    @Test
    public void whenPercentOfToPlanCurrencyDoesNotMatchFromPlanCurrency_thenValidationFailsAsExpected() {
        String fromPlanId = "PLAN-1111111";
        String toPlanId = "PLAN-2222222";
        CatalogRelationship relationship = getPercentOfCatalogRelationship(fromPlanId, toPlanId);
        when(mockCatalogRelationshipIdGenerator.generate()).thenReturn("REL-1234567");

        Plan fromPlan = mock(Plan.class);
        Plan toPlan = mock(Plan.class);
        when(mockProductCatalogGetService.getPlan(fromPlanId)).thenReturn(fromPlan);
        when(mockProductCatalogGetService.getPlan(toPlanId)).thenReturn(toPlan);

        when(fromPlan.getPlanId()).thenReturn(fromPlanId);
        when(toPlan.getPlanId()).thenReturn(toPlanId);
        when(mockProductCatalogGetService.isPlanInUse(fromPlanId)).thenReturn(false);
        when(mockProductCatalogGetService.isPlanInUse(toPlanId)).thenReturn(false);

        when(fromPlan.getCurrency()).thenReturn(Currency.getInstance("USD"));
        when(toPlan.getCurrency()).thenReturn(Currency.getInstance("GBP"));

        assertThatThrownBy(() -> catalogRelationshipService.addCatalogRelationship(relationship))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("different currency");
    }

    @Test
    public void whenPercentOfToPlanIsSameAsFromPlan_thenValidationFailsAsExpected() {
        CatalogRelationship relationship = getPercentOfCatalogRelationship("PLAN-1111111", "PLAN-1111111");
        when(mockCatalogRelationshipIdGenerator.generate()).thenReturn("REL-1234567");

        assertThatThrownBy(() -> catalogRelationshipService.addCatalogRelationship(relationship))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("cannot be the same");
    }

    @Test
    public void whenAddRelationshipFromPlanAndToPlanAreInUse_thenValidationFailsAsExpected() {
        String fromPlanId = "PLAN-1111111";
        String toPlanId = "PLAN-2222222";
        CatalogRelationship relationship = getPercentOfCatalogRelationship(fromPlanId, toPlanId);
        when(mockCatalogRelationshipIdGenerator.generate()).thenReturn("REL-1234567");

        Currency usd = Currency.getInstance("USD");
        Plan fromPlan = mock(Plan.class);
        when(fromPlan.getCurrency()).thenReturn(usd);
        Plan toPlan = mock(Plan.class);
        when(toPlan.getCurrency()).thenReturn(usd);
        when(mockProductCatalogGetService.getPlan(fromPlanId)).thenReturn(fromPlan);
        when(mockProductCatalogGetService.getPlan(toPlanId)).thenReturn(toPlan);
        when(fromPlan.getPlanId()).thenReturn(fromPlanId);
        when(toPlan.getPlanId()).thenReturn(toPlanId);
        when(mockProductCatalogGetService.isPlanInUse(fromPlanId)).thenReturn(true);
        when(mockProductCatalogGetService.isPlanInUse(toPlanId)).thenReturn(true);

        assertThatThrownBy(() -> catalogRelationshipService.addCatalogRelationship(relationship))
            .isInstanceOf(InvalidInputException.class)
            .hasMessageContaining("both plans are already in use");
    }

    @Test
    public void whenDeleteRelationshipDoesNotExist_thenValidationFailsAsExpected() {
        String relationshipId = "REL-1234567";
        when(mockCatalogRelationshipGetService.getCatalogRelationshipById(relationshipId)).thenThrow(ObjectNotFoundException.class);

        assertThatThrownBy(() -> catalogRelationshipService.deleteCatalogRelationship(relationshipId)).isInstanceOf(ObjectNotFoundException.class);
    }

    @Test
    public void whenDeleteRelationshipFromPlanAndToPlanAreInUse_thenValidationFailsAsExpected() {
        String relationshipId = "REL-1234567";
        String fromPlanId = "PLAN-1111111";
        String toPlanId = "PLAN-2222222";
        CatalogRelationship relationship = new CatalogRelationship();
        relationship.setCatalogRelationshipType(CatalogRelationshipType.IS_PERCENT_OF);
        relationship.setRelationshipId(relationshipId);
        relationship.setFromPlanId(fromPlanId);
        relationship.setToPlanId(toPlanId);
        when(mockCatalogRelationshipGetService.getCatalogRelationshipById(relationshipId)).thenReturn(relationship);

        when(mockProductCatalogGetService.isPlanInUse(fromPlanId)).thenReturn(true);
        when(mockProductCatalogGetService.isPlanInUse(toPlanId)).thenReturn(true);

        assertThatThrownBy(() -> catalogRelationshipService.deleteCatalogRelationship(mockConfiguration, relationship))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("are both already in use");
    }

    private static CatalogRelationship getPercentOfCatalogRelationship(String fromPlanId, String toPlanId) {
        CatalogRelationship relationship = new CatalogRelationship();
        relationship.setFromProductId("PROD-1111111");
        relationship.setFromPlanId(fromPlanId);
        relationship.setToProductId("PROD-2222222");
        relationship.setToPlanId(toPlanId);
        relationship.setCatalogRelationshipType(CatalogRelationshipType.IS_PERCENT_OF);
        return relationship;
    }
}
