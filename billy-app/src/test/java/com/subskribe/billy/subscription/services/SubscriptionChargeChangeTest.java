package com.subskribe.billy.subscription.services;

import static org.assertj.core.api.Assertions.assertThat;

import com.subskribe.billy.resources.json.subscription.SubscriptionJson;
import com.subskribe.billy.resources.json.subscription.SubscriptionJsonStub;
import com.subskribe.billy.shared.serializer.UncheckedObjectMapper;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.junit.jupiter.api.Test;

class SubscriptionChargeChangeTest {

    @Test
    void testJsonRoundtrip() {
        SubscriptionJson subscriptionJson = SubscriptionJsonStub.createSubscriptionJson();
        SubscriptionChargeChange subscriptionChargeChange = ImmutableSubscriptionChargeChange.builder()
            .subscription(SubscriptionJsonStub.createSubscriptionJson())
            .changesOn(Instant.now().truncatedTo(ChronoUnit.SECONDS))
            .chargesEnding(List.of(subscriptionJson.getCharges().get(0)))
            .chargesStarting(List.of(subscriptionJson.getCharges().get(0)))
            .build();
        String json = UncheckedObjectMapper.defaultMapper().writeValueAsString(subscriptionChargeChange);
        SubscriptionChargeChange deserialized = UncheckedObjectMapper.defaultMapper().readValue(json, SubscriptionChargeChange.class);
        assertThat(deserialized).isEqualTo(subscriptionChargeChange);
    }
}
