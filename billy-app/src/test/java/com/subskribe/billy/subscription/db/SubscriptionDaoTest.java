package com.subskribe.billy.subscription.db;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.core.type.TypeReference;
import com.subskribe.billy.entity.fixtures.EntityFixture;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.shared.pecuniary.Discount;
import com.subskribe.billy.shared.traits.VersionReference;
import com.subskribe.billy.shared.traits.VersionReference.ReferenceType;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.model.SubscriptionChargeEntity;
import com.subskribe.billy.subscription.model.SubscriptionChargeMapEntity;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.tenant.TenantIdProvider;
import com.subskribe.billy.test.WithDb;
import java.io.IOException;
import java.net.URISyntaxException;
import java.time.Instant;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

@TestMethodOrder(OrderAnnotation.class)
public class SubscriptionDaoTest extends WithDb {

    private SubscriptionDAO subscriptionDao;
    private List<SubscriptionTestData> testData;
    private static final TenantIdProvider MOCK_TENANT_ID_PROVIDER = mock(TenantIdProvider.class);
    private static final String TENANT_ID = "test tenant 1 id";

    @BeforeAll
    public void initDao() throws IOException, URISyntaxException {
        EntityFixture.initDb(dslContextProvider);
        when(MOCK_TENANT_ID_PROVIDER.provideTenantIdString()).thenReturn(TENANT_ID);
        subscriptionDao = new SubscriptionDAO(MOCK_TENANT_ID_PROVIDER, dslContextProvider);
        testData = asType(getClass(), "basic_sub.json", new TypeReference<>() {});
    }

    @JsonAutoDetect(fieldVisibility = Visibility.ANY)
    private static class SubscriptionTestData {

        SubscriptionEntity subscription;
        List<SubscriptionChargeEntity> charges;
        List<SubscriptionChargeMapEntity> chargesMap;
    }

    @Test
    @Order(1)
    public void testAddSubscription() {
        for (SubscriptionTestData td : testData) {
            subscriptionDao.addSubscription(dslContextProvider.get(td.subscription.getTenantId()), td.subscription, td.charges, td.chargesMap);
        }
    }

    @Test
    @Order(2)
    public void testGetSubscription() {
        for (SubscriptionTestData td : testData) {
            var dslContext = dslContextProvider.get(td.subscription.getTenantId());
            validate(
                td.subscription,
                subscriptionDao.getSubscription(dslContext, new VersionReference<>(td.subscription.getSubscriptionId(), ReferenceType.CURRENT))
            );
        }
    }

    @Test
    @Order(3)
    public void testGetSubscriptionAt() {
        SubscriptionTestData td = testData.get(0);
        var dslContext = dslContextProvider.get(td.subscription.getTenantId());
        validate(
            td.subscription,
            subscriptionDao.getSubscriptionAt(dslContext, td.subscription.getSubscriptionId(), Instant.parse("2020-03-31T08:00:00Z"))
        );

        assertThrows(ObjectNotFoundException.class, () ->
            subscriptionDao.getSubscriptionAt(dslContext, td.subscription.getSubscriptionId(), Instant.parse("2020-01-31T08:00:00Z"))
        );
    }

    @Test
    @Order(4)
    public void testGetSubscriptionCharge() {
        for (SubscriptionTestData td : testData) {
            var dslContext = dslContextProvider.get(td.subscription.getTenantId());
            var resultCharges = subscriptionDao.getSubscriptionCharges(dslContext, td.subscription.getId());
            var resultChargesMap = resultCharges.stream().collect(Collectors.toMap(SubscriptionCharge::getId, Function.identity()));
            for (SubscriptionChargeEntity charge : td.charges) {
                validate(charge, resultChargesMap.get(charge.getId()));
            }
        }
    }

    private void validate(SubscriptionEntity expected, SubscriptionEntity actual) {
        assertEquals(expected.getSubscriptionId(), actual.getSubscriptionId());
        assertEquals(expected.getTenantId(), actual.getTenantId());
        assertEquals(expected.getAccountId(), actual.getAccountId());
        assertEquals(expected.getState(), actual.getState());
        assertEquals(expected.getStartDate(), actual.getStartDate());
        assertEquals(expected.getEndDate(), actual.getEndDate());
        assertEquals(expected.getTermLength().getCycle(), actual.getTermLength().getCycle());
        assertEquals(expected.getTermLength().getStep(), actual.getTermLength().getStep());
        assertEquals(expected.getBillingCycle().getCycle(), actual.getBillingCycle().getCycle());
        assertEquals(expected.getBillingCycle().getStep(), actual.getBillingCycle().getStep());
        assertNotNull(actual.getCreationTime());
        assertEquals(expected.getVersion(), actual.getVersion());
        assertEquals(expected.getVersionStart(), actual.getVersionStart());
    }

    private void validate(SubscriptionCharge expected, SubscriptionCharge actual) {
        assertEquals(expected.getChargeId(), actual.getChargeId());
        assertEquals(expected.getSubscriptionChargeId(), actual.getSubscriptionChargeId());
        assertEquals(expected.getSubscriptionId(), actual.getSubscriptionId());
        assertEquals(expected.getAccountId(), actual.getAccountId());
        assertEquals(expected.getTenantId(), actual.getTenantId());
        assertEquals(expected.getStartDate(), actual.getStartDate());
        assertEquals(expected.getEndDate(), actual.getEndDate());
        assertEquals(expected.getQuantity(), actual.getQuantity());
        if (!(actual.getDiscounts() == null && expected.getDiscounts() == null)) {
            var expectedDiscountMap = expected.getDiscounts().stream().collect(Collectors.toMap(Discount::getName, Function.identity()));
            var actualDiscountMap = actual.getDiscounts().stream().collect(Collectors.toMap(Discount::getName, Function.identity()));
            expectedDiscountMap.keySet().forEach(k -> assertEquals(expectedDiscountMap.get(k).getPercent(), actualDiscountMap.get(k).getPercent()));
        }
        assertEquals(expected.getOrderLines(), actual.getOrderLines());
    }
}
