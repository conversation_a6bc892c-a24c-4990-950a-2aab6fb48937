package com.subskribe.billy.subscription.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.model.TaskStatus;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import com.subskribe.billy.shared.task.queue.retry.TaskBackoffResultBuilder;
import com.subskribe.billy.subscription.db.SubscriptionChargeChangeScheduleDAO;
import com.subskribe.billy.subscription.db.SubscriptionStatusChangeScheduleDAO;
import com.subskribe.billy.subscription.model.ImmutableSubscriptionChargeChangeSchedule;
import com.subskribe.billy.subscription.model.ImmutableSubscriptionStatusChangeSchedule;
import com.subskribe.billy.subscription.model.SubscriptionChargeChangeSchedule;
import com.subskribe.billy.subscription.model.SubscriptionStatusChangeSchedule;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class SubscriptionChangeTaskProcessorTest {

    @Mock
    private SubscriptionStatusChangeScheduleDAO subscriptionStatusChangeScheduleDAO;

    @Mock
    private SubscriptionChargeChangeScheduleDAO subscriptionChargeChangeScheduleDAO;

    @Mock
    private SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService;

    @Mock
    private TaskBackoffResultBuilder taskBackoffResultBuilder;

    @Mock
    private FeatureService featureService;

    private SubscriptionChangeTaskProcessor processor;

    private static final String TENANT_ID = "test-tenant";
    private static final String SUBSCRIPTION_ID = "test-subscription";
    private static final UUID CHANGE_ID = UUID.randomUUID();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        processor = new SubscriptionChangeTaskProcessor(
            subscriptionStatusChangeScheduleDAO,
            subscriptionChargeChangeScheduleDAO,
            subscriptionLifecycleScheduleService,
            taskBackoffResultBuilder,
            featureService
        );
        when(featureService.isEnabled(Feature.USE_TASKS_FOR_SUBSCRIPTION_CHANGES)).thenReturn(true);
    }

    @Test
    void processStatusChange_WhenChangeIdNotValidUUID_ThrowsException() {
        SubscriptionStatusChangeSchedule change = createStatusChangeSchedule();
        QueuedTask task = ImmutableQueuedTask.builder().from(createStatusChangeTask()).taskData("invalid-uuid").build();

        assertThatThrownBy(() -> processor.process(task)).isInstanceOf(IllegalArgumentException.class).hasMessageContaining("invalid-uuid");
    }

    @Test
    void processStatusChange_WhenChangeExists_ShouldProcessAndReturnSuccess() {
        SubscriptionStatusChangeSchedule change = createStatusChangeSchedule();
        QueuedTask task = createStatusChangeTask();

        when(subscriptionStatusChangeScheduleDAO.getChangeById(CHANGE_ID)).thenReturn(Optional.of(change));

        TaskResult result = processor.process(task);

        assertThat(result.isSuccessful()).isTrue();
        verify(subscriptionLifecycleScheduleService).processStatusChange(change);
    }

    @Test
    void processStatusChange_WhenChangeDoesNotExist_ShouldReturnSuccessWithoutProcessing() {
        QueuedTask task = createStatusChangeTask();

        when(subscriptionStatusChangeScheduleDAO.getChangeById(CHANGE_ID)).thenReturn(Optional.empty());

        TaskResult result = processor.process(task);

        assertThat(result.isSuccessful()).isTrue();
        verify(subscriptionLifecycleScheduleService, never()).processStatusChange(any());
    }

    @Test
    void processChargeChange_WhenChangeExists_ShouldProcessAndReturnSuccess() {
        SubscriptionChargeChangeSchedule change = createChargeChangeSchedule();
        QueuedTask task = createChargeChangeTask();

        when(subscriptionChargeChangeScheduleDAO.getChangeById(CHANGE_ID)).thenReturn(Optional.of(change));

        TaskResult result = processor.process(task);

        assertThat(result.isSuccessful()).isTrue();
        verify(subscriptionLifecycleScheduleService).processChange(change);
    }

    @Test
    void processChargeChange_WhenChangeDoesNotExist_ShouldReturnSuccessWithoutProcessing() {
        QueuedTask task = createChargeChangeTask();

        when(subscriptionChargeChangeScheduleDAO.getChangeById(CHANGE_ID)).thenReturn(Optional.empty());

        TaskResult result = processor.process(task);

        assertThat(result.isSuccessful()).isTrue();
        verify(subscriptionLifecycleScheduleService, never()).processChange(any());
    }

    @Test
    void process_WithUnsupportedTaskType_ShouldReturnFailureWithoutRetry() {
        QueuedTask task = createUnsupportedTypeTask();

        TaskResult result = processor.process(task);

        assertThat(result.isSuccessful()).isFalse();
        assertThat(result.isRetryable()).isFalse();
        assertThat(result.getFailureReason().orElse("")).contains("Unsupported task type");
        verify(taskBackoffResultBuilder, never()).withBackoff(any());
    }

    @Test
    void process_WhenProcessingThrowsException_ShouldRetryWithBackoff() {
        QueuedTask task = createStatusChangeTask();
        TaskResult expectedResult = mock(TaskResult.class);

        when(subscriptionStatusChangeScheduleDAO.getChangeById(CHANGE_ID)).thenThrow(new RuntimeException("Test exception"));
        when(taskBackoffResultBuilder.withBackoff(any())).thenReturn(expectedResult);

        TaskResult result = processor.process(task);

        assertThat(result).isEqualTo(expectedResult);
        verify(taskBackoffResultBuilder).withBackoff(any());
    }

    @Test
    void getConfiguration_ShouldReturnCorrectTaskModuleAndTimeout() {
        var config = processor.getConfiguration();

        assertThat(config.getTaskModule()).isEqualTo(new TaskModule("subscription-change"));
        assertThat(config.getTaskTimeout()).isNotNull();
    }

    private QueuedTask createStatusChangeTask() {
        return ImmutableQueuedTask.builder()
            .taskId(UUID.randomUUID().toString())
            .tenantId(TENANT_ID)
            .taskData(CHANGE_ID.toString())
            .type(SubscriptionChangeTaskProcessor.STATUS_CHANGE_TASK_TYPE)
            .module(SubscriptionChangeTaskProcessor.TASK_MODULE)
            .status(TaskStatus.WAITING)
            .build();
    }

    private QueuedTask createChargeChangeTask() {
        return ImmutableQueuedTask.builder()
            .taskId(UUID.randomUUID().toString())
            .tenantId(TENANT_ID)
            .taskData(CHANGE_ID.toString())
            .type(SubscriptionChangeTaskProcessor.CHARGE_CHANGE_TASK_TYPE)
            .module(SubscriptionChangeTaskProcessor.TASK_MODULE)
            .status(TaskStatus.WAITING)
            .build();
    }

    private QueuedTask createUnsupportedTypeTask() {
        return ImmutableQueuedTask.builder()
            .taskId(UUID.randomUUID().toString())
            .tenantId(TENANT_ID)
            .taskData(CHANGE_ID.toString())
            .type(new TaskType("unsupported-type"))
            .module(SubscriptionChangeTaskProcessor.TASK_MODULE)
            .status(TaskStatus.WAITING)
            .build();
    }

    private SubscriptionStatusChangeSchedule createStatusChangeSchedule() {
        return ImmutableSubscriptionStatusChangeSchedule.builder()
            .id(UUID.fromString(CHANGE_ID.toString()))
            .tenantId(TENANT_ID)
            .subscriptionId(SUBSCRIPTION_ID)
            .subscriptionVersion(1)
            .changesOn(Instant.now())
            .isProcessed(false)
            .isDeleted(false)
            .changeEventType(EventType.SUBSCRIPTION_ACTIVATING)
            .build();
    }

    private SubscriptionChargeChangeSchedule createChargeChangeSchedule() {
        return ImmutableSubscriptionChargeChangeSchedule.builder()
            .id(UUID.fromString(CHANGE_ID.toString()))
            .tenantId(TENANT_ID)
            .subscriptionId(SUBSCRIPTION_ID)
            .subscriptionVersion(1)
            .changesOn(Instant.now())
            .isProcessed(false)
            .isDeleted(false)
            .chargeIdsStarting("")
            .chargeIdsEnding("")
            .build();
    }
}
