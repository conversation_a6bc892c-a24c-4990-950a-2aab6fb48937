package com.subskribe.billy.subscription.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.model.TenantAccountPartitionKey;
import com.subskribe.billy.exception.InvalidInputException;
import com.subskribe.billy.resources.json.subscription.SubscriptionJson;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.task.queue.model.ImmutableQueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.Partitioned;
import com.subskribe.billy.shared.task.queue.model.ProcessorConfiguration;
import com.subskribe.billy.shared.task.queue.model.QueuedTask;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.model.TaskModule;
import com.subskribe.billy.shared.task.queue.model.TaskResult;
import com.subskribe.billy.shared.task.queue.model.TaskType;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class SubscriptionLifecycleTaskProcessorTest {

    private static final String TENANT_ID = "test-tenant-id";
    private static final String SUBSCRIPTION_ID = "test-subscription-id";
    private static final String ENTITY_ID = "test-entity-id";
    private static final TaskModule TASK_MODULE = new TaskModule("subscription-lifecycle");
    private static final TaskType SCHEDULE_TASK_TYPE = new TaskType("schedule-new-subscription-events");
    private static final Duration TIMEOUT = Duration.ofMinutes(2);

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private SubscriptionLifecycleScheduleService subscriptionLifecycleScheduleService;

    @Mock
    private Event event;

    private SubscriptionLifecycleTaskProcessor eventHandler;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        eventHandler = new SubscriptionLifecycleTaskProcessor(tenantIdProvider, subscriptionLifecycleScheduleService);
        objectMapper = JacksonProvider.defaultMapper();
    }

    @Test
    void createTaskForEventWithValidSubscriptionCreatedEventShouldCreateTask() throws JsonProcessingException {
        SubscriptionJson subscriptionJson = createSampleSubscriptionJson();
        String payload = objectMapper.writeValueAsString(subscriptionJson);
        ByteBuffer byteBuffer = ByteBuffer.wrap(payload.getBytes(StandardCharsets.UTF_8));

        when(event.getType()).thenReturn(EventType.SUBSCRIPTION_CREATED);
        when(event.getPayload()).thenReturn(byteBuffer);
        when(event.getPayloadAsString()).thenReturn(payload);

        QueuedTaskRequest result = eventHandler.createTaskForEvent(event);

        assertThat(result).isEqualTo(
            ImmutableQueuedTaskRequest.builder()
                .module(TASK_MODULE)
                .type(SCHEDULE_TASK_TYPE)
                .taskData(SUBSCRIPTION_ID)
                .tenantId(TENANT_ID)
                .entityId(ENTITY_ID)
                .taskOrder(Partitioned.with(TenantAccountPartitionKey.builder().withTenantId(TENANT_ID).withAccountId("ACCT-12345").build().getKey()))
                .build()
        );
    }

    @Test
    void createTaskForEventWithUnsupportedEventTypeShouldThrowException() {
        when(event.getType()).thenReturn(EventType.INVOICE_POSTED);

        InvalidInputException exception = assertThrows(InvalidInputException.class, () -> eventHandler.createTaskForEvent(event));

        assertEquals("Unsupported event type: INVOICE_POSTED", exception.getMessage());
    }

    @Test
    void getEventTypesShouldReturnCorrectEventTypes() {
        Set<EventType> eventTypes = eventHandler.getEventTypes();

        assertEquals(1, eventTypes.size());
        assertTrue(eventTypes.contains(EventType.SUBSCRIPTION_CREATED));
    }

    @Test
    void processShouldScheduleChangesWhenFeatureEnabledAndTaskTypeSupported() {
        QueuedTask mockTask = mock(QueuedTask.class);
        when(mockTask.getTaskData()).thenReturn(SUBSCRIPTION_ID);
        when(mockTask.getType()).thenReturn(SCHEDULE_TASK_TYPE);

        TaskResult result = eventHandler.process(mockTask);

        assertTrue(result.isSuccessful());
        verify(subscriptionLifecycleScheduleService).scheduleNewChanges(SUBSCRIPTION_ID);
    }

    @Test
    void processShouldFailWhenTaskTypeNotSupported() {
        QueuedTask mockTask = mock(QueuedTask.class);
        when(mockTask.getTaskData()).thenReturn(SUBSCRIPTION_ID);
        when(mockTask.getType()).thenReturn(new TaskType("unsupported-task-type"));

        TaskResult result = eventHandler.process(mockTask);

        assertThat(result.isSuccessful()).isFalse();
        verify(subscriptionLifecycleScheduleService, never()).scheduleNewChanges(SUBSCRIPTION_ID);
    }

    @Test
    void getConfigurationShouldReturnCorrectConfiguration() {
        ProcessorConfiguration config = eventHandler.getConfiguration();

        assertEquals(TASK_MODULE, config.getTaskModule());
        assertEquals(TIMEOUT, config.getTaskTimeout());
    }

    private SubscriptionJson createSampleSubscriptionJson() {
        SubscriptionJson subscription = new SubscriptionJson();
        subscription.setId(SUBSCRIPTION_ID);
        subscription.setEntityId(ENTITY_ID);
        subscription.setAccountId("ACCT-12345");
        return subscription;
    }
}
