package com.subskribe.billy.subscription.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.invoice.model.InvoiceItem;
import com.subskribe.billy.invoice.model.ProrationConfig;
import com.subskribe.billy.invoice.service.ProrationConfigurationGetService;
import com.subskribe.billy.order.model.CustomBillingSchedule;
import com.subskribe.billy.order.model.Order;
import com.subskribe.billy.order.model.OrderLineItem;
import com.subskribe.billy.shared.enums.BillingTerm;
import com.subskribe.billy.shared.enums.Cycle;
import com.subskribe.billy.shared.serializer.JacksonProvider;
import com.subskribe.billy.shared.temporal.BillingPeriod;
import com.subskribe.billy.shared.temporal.Recurrence;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.tenant.fixtures.TenantSettingServiceFixture;
import com.subskribe.billy.tenant.services.TenantSettingService;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Comparator;
import java.util.Currency;
import java.util.List;
import java.util.TimeZone;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SubscriptionBillingPeriodServiceTest {

    private static final TimeZone TIME_ZONE = TimeZone.getTimeZone("PST");
    private static final ZoneId ZONE_ID = TIME_ZONE.toZoneId();

    private static final ZonedDateTime JAN_1_2020 = ZonedDateTime.of(2020, 1, 1, 10, 40, 10, 0, ZONE_ID);

    private static final ZonedDateTime JAN_31_2020 = ZonedDateTime.of(2020, 1, 31, 12, 0, 0, 0, ZONE_ID);

    private static final ZonedDateTime JAN_1_2021 = JAN_1_2020.plusYears(1);

    private static final ZonedDateTime JAN_1_2022 = JAN_1_2021.plusYears(1);

    private static final ZonedDateTime JAN_1_2023 = JAN_1_2022.plusYears(1);

    private static final ZonedDateTime JAN_15_2020 = ZonedDateTime.of(2020, 1, 15, 10, 40, 10, 0, ZONE_ID);

    private static final ZonedDateTime JUNE_15_2020 = ZonedDateTime.of(2020, 6, 15, 10, 40, 10, 0, ZONE_ID);

    private static final Recurrence MONTHLY_RECURRENCE = new Recurrence(Cycle.MONTH, 1);

    private static final Recurrence QUARTERLY_RECURRENCE = new Recurrence(Cycle.MONTH, 3);

    private static final Recurrence YEARLY_RECURRENCE = new Recurrence(Cycle.YEAR, 1);

    private static final Recurrence THIRTY_DAY_RECURRENCE = new Recurrence(Cycle.DAY, 30);

    private static final BillingTerm UP_FRONT = BillingTerm.UP_FRONT;

    private final TenantSettingService tenantSettingService = TenantSettingServiceFixture.getDefault();
    private final FeatureService featureService = mock(FeatureService.class);
    private final ProrationConfigurationGetService prorationConfigurationGetService = mock(ProrationConfigurationGetService.class);

    private SubscriptionBillingPeriodService subscriptionBillingPeriodService;

    @BeforeEach
    void setup() {
        when(featureService.isEnabled(Feature.FLEXIBLE_BILLING_ANCHOR_DATE)).thenReturn(false);
        subscriptionBillingPeriodService = new SubscriptionBillingPeriodService(
            mock(SubscriptionGetService.class),
            tenantSettingService,
            featureService,
            prorationConfigurationGetService
        );
    }

    @Test
    public void daysBillingPeriod() {
        ZonedDateTime endDate = JAN_1_2020.plusDays(300);
        var billingPeriodsOver300Days = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            endDate.toInstant(),
            JAN_1_2020.toInstant(),
            endDate.toInstant(),
            ZONE_ID,
            THIRTY_DAY_RECURRENCE,
            UP_FRONT
        );

        assertEquals(10, billingPeriodsOver300Days.size());
        assertTrue(validBillingPeriods(billingPeriodsOver300Days));

        var billingPeriodsOverFullYear = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            THIRTY_DAY_RECURRENCE,
            UP_FRONT
        );

        assertEquals(13, billingPeriodsOverFullYear.size()); // 30 x 12 full periods + partial period
        assertEquals(JAN_1_2020.plusDays(360).toInstant(), billingPeriodsOverFullYear.get(12).getStart());
        assertTrue(validBillingPeriods(billingPeriodsOverFullYear));
    }

    @Test
    public void daysBillingPeriodWithBillingAnchorDate() {
        ZonedDateTime billingAnchorDate = JAN_1_2020.plusDays(6);
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            billingAnchorDate.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            THIRTY_DAY_RECURRENCE,
            UP_FRONT
        );

        assertEquals(13, billingPeriods.size());
        assertEquals(billingAnchorDate.toInstant(), billingPeriods.get(0).getEnd());
        assertTrue(validBillingPeriods(billingPeriods));
    }

    @Test
    public void paidInFullBillingPeriod() {
        Subscription subscription = getMockSubscription(
            JAN_31_2020.toInstant(),
            JAN_31_2020.plusYears(1).toInstant(),
            new Recurrence(Cycle.PAID_IN_FULL, 1)
        );
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(subscription, TIME_ZONE, subscription.getEndDate());

        Assertions.assertThat(billingPeriods.size()).isOne();
        var billingPeriod = billingPeriods.get(0);
        assertEquals(JAN_31_2020.toInstant(), billingPeriod.getStart());
        assertEquals(JAN_31_2020.plusYears(1).toInstant(), billingPeriod.getEnd());
    }

    @Test
    public void endOfMonthBillingPeriod() {
        Subscription subscription = getMockSubscription(JAN_31_2020.toInstant(), JAN_31_2020.plusYears(1).toInstant(), MONTHLY_RECURRENCE);
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(subscription, TIME_ZONE, subscription.getEndDate());

        assertEquals(12, billingPeriods.size());
        assertEquals(JAN_31_2020.plusMonths(1).toInstant(), billingPeriods.get(1).getStart());
        assertEquals(JAN_31_2020.plusMonths(2).toInstant(), billingPeriods.get(2).getStart());
        assertEquals(subscription.getEndDate(), billingPeriods.get(11).getEnd());
    }

    @Test
    public void getNextPeriodStart() {
        ZonedDateTime now = ZonedDateTime.now(ZONE_ID);
        ZonedDateTime endDate = now.plusYears(1);

        ZonedDateTime nextMonth = now.plusMonths(1);

        Subscription subscription = getMockSubscription(now.toInstant(), endDate.toInstant(), MONTHLY_RECURRENCE);
        Instant nextBillingPeriodStart = subscriptionBillingPeriodService.getNextBillingPeriodStart(subscription, TimeZone.getTimeZone(ZONE_ID));
        assertEquals(nextBillingPeriodStart, nextMonth.toInstant());
    }

    @Test
    public void getNextPeriodInLastPeriod() {
        ZonedDateTime now = ZonedDateTime.now(ZONE_ID);
        ZonedDateTime startDate = now.minusYears(1);

        Subscription subscription = getMockSubscription(startDate.toInstant(), now.toInstant(), MONTHLY_RECURRENCE);
        Instant nextBillingPeriodStart = subscriptionBillingPeriodService.getNextBillingPeriodStart(subscription, TimeZone.getTimeZone(ZONE_ID));

        assertEquals(nextBillingPeriodStart, now.toInstant());
    }

    @Test
    public void firstPartialPeriod() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            UP_FRONT
        );

        var firstPeriod = billingPeriods.get(0);
        var secondPeriod = billingPeriods.get(1);

        assertEquals(13, billingPeriods.size());
        assertEquals(JAN_1_2020, getPeriodStart(firstPeriod));
        assertEquals(JAN_15_2020, getPeriodEnd(firstPeriod));

        assertEquals(JAN_15_2020, getPeriodStart(secondPeriod));
    }

    @Test
    public void firstPartialPeriod_InArrears() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2020.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.IN_ARREARS
        );
        assertEquals(0, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.IN_ARREARS
        );
        var firstPeriod = billingPeriods.get(0);
        var secondPeriod = billingPeriods.get(1);

        assertEquals(13, billingPeriods.size());
        assertEquals(JAN_1_2020, getPeriodStart(firstPeriod));
        assertEquals(JAN_15_2020, getPeriodEnd(firstPeriod));

        assertEquals(JAN_15_2020, getPeriodStart(secondPeriod));
    }

    @Test
    public void quarterlyCyclesForOneYear() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            QUARTERLY_RECURRENCE,
            UP_FRONT
        );

        assertEquals(4, billingPeriods.size());
        assertTrue(validBillingPeriods(billingPeriods));
    }

    @Test
    public void monthlyCyclesForOneYear() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            UP_FRONT
        );

        assertEquals(12, billingPeriods.size());
        assertTrue(validBillingPeriods(billingPeriods));
    }

    @Test
    public void sixBillingCycles() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_1_2020.toInstant(),
            JUNE_15_2020.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            UP_FRONT
        );

        assertEquals(6, billingPeriods.size());
        assertTrue(validBillingPeriods(billingPeriods));
    }

    @Test
    public void billingPeriodStartEndDates() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            UP_FRONT
        );

        assertEquals(JAN_1_2020, getPeriodStart(billingPeriods.get(0)));
        assertEquals(JAN_1_2020.plusMonths(1), getPeriodStart(billingPeriods.get(1)));
    }

    @Test
    public void billingPeriod_PaidInFull_UpFront() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_1_2020.toInstant(),
            JAN_1_2020.toInstant(),
            ZONE_ID,
            new Recurrence(Cycle.PAID_IN_FULL, 1),
            BillingTerm.UP_FRONT
        );
        assertEquals(1, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            new Recurrence(Cycle.PAID_IN_FULL, 1),
            BillingTerm.UP_FRONT
        );
        assertEquals(1, billingPeriods.size());
    }

    @Test
    public void billingPeriod_PaidInFull_InArrears() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_1_2020.toInstant(),
            JAN_1_2020.toInstant(),
            ZONE_ID,
            new Recurrence(Cycle.PAID_IN_FULL, 1),
            BillingTerm.IN_ARREARS
        );
        assertEquals(0, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            new Recurrence(Cycle.PAID_IN_FULL, 1),
            BillingTerm.IN_ARREARS
        );
        assertEquals(1, billingPeriods.size());
    }

    @Test
    public void billingPeriod_PaidInFull_InArrears_WithBillingAnchorDate() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2020.toInstant(),
            ZONE_ID,
            new Recurrence(Cycle.PAID_IN_FULL, 1),
            BillingTerm.IN_ARREARS
        );
        assertEquals(0, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            new Recurrence(Cycle.PAID_IN_FULL, 1),
            BillingTerm.IN_ARREARS
        );
        assertEquals(2, billingPeriods.size());
    }

    @Test
    public void billingPeriod_PaidInFull_UpFront_WithBillingAnchorDate() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2020.toInstant(),
            ZONE_ID,
            new Recurrence(Cycle.PAID_IN_FULL, 1),
            BillingTerm.UP_FRONT
        );
        assertEquals(1, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            new Recurrence(Cycle.PAID_IN_FULL, 1),
            BillingTerm.UP_FRONT
        );
        assertEquals(2, billingPeriods.size());
    }

    @Test
    public void billingPeriod_Monthly_InArrears_BillingAnchorDate() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2020.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.IN_ARREARS
        );
        assertEquals(0, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_15_2020.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.IN_ARREARS
        );
        assertEquals(1, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2020.plusMonths(1).toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.IN_ARREARS
        );
        assertEquals(1, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2020.plusMonths(2).toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.IN_ARREARS
        );
        assertEquals(2, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.IN_ARREARS
        );
        assertEquals(13, billingPeriods.size());
    }

    @Test
    public void billingPeriod_Monthly_UpFront_BillingAnchorDate() {
        var billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2020.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.UP_FRONT
        );
        assertEquals(1, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_15_2020.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.UP_FRONT
        );
        assertEquals(2, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2020.plusMonths(1).toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.UP_FRONT
        );
        assertEquals(2, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2020.plusMonths(2).toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.UP_FRONT
        );
        assertEquals(3, billingPeriods.size());

        billingPeriods = subscriptionBillingPeriodService.getBillingPeriods(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            JAN_15_2020.toInstant(),
            JAN_1_2021.toInstant(),
            ZONE_ID,
            MONTHLY_RECURRENCE,
            BillingTerm.UP_FRONT
        );
        assertEquals(13, billingPeriods.size());
    }

    @Test
    public void testOverLappingPeriods() {
        var billingPeriodStart = JAN_1_2020.plusMonths(3);
        var billingPeriodEnd = billingPeriodStart.plusYears(1);
        var overlapPeriod = SubscriptionBillingPeriodService.getItemOverlapPeriod(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            new BillingPeriod(billingPeriodStart.toInstant(), billingPeriodEnd.toInstant(), billingPeriodEnd.toInstant(), YEARLY_RECURRENCE)
        );

        assertEquals(billingPeriodStart.toInstant(), overlapPeriod.getStart());
        assertEquals(JAN_1_2021.toInstant(), overlapPeriod.getEnd());
    }

    @Test
    public void testNonOverLappingPeriods() {
        var billingPeriodStart = JAN_1_2020.plusYears(2);
        var billingPeriodEnd = billingPeriodStart.plusYears(1);
        var overlapPeriod = SubscriptionBillingPeriodService.getItemOverlapPeriod(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            new BillingPeriod(billingPeriodStart.toInstant(), billingPeriodEnd.toInstant(), billingPeriodEnd.toInstant(), YEARLY_RECURRENCE)
        );

        assertEquals(JAN_1_2021.toInstant(), overlapPeriod.getStart());
        assertEquals(JAN_1_2021.toInstant(), overlapPeriod.getEnd());
    }

    @Test
    public void testContagiousPeriods() {
        var billingPeriodStart = JAN_1_2021;
        var billingPeriodEnd = billingPeriodStart.plusYears(1);
        var overlapPeriod = SubscriptionBillingPeriodService.getItemOverlapPeriod(
            JAN_1_2020.toInstant(),
            JAN_1_2021.toInstant(),
            new BillingPeriod(billingPeriodStart.toInstant(), billingPeriodEnd.toInstant(), billingPeriodEnd.toInstant(), YEARLY_RECURRENCE)
        );

        assertEquals(JAN_1_2021.toInstant(), overlapPeriod.getStart());
        assertEquals(JAN_1_2021.toInstant(), overlapPeriod.getEnd());
    }

    @Test
    public void testGenerateInvoiceItemsForAdhocCustomBilling() throws JsonProcessingException {
        String customBillingScheduleJson =
            """
            {
                "version": "V1",
                "orderId": "order_123",
                "orderLines": [
                    "line_1",
                    "line_2",
                    "line_3_1",
                    "line_3_2",
                    "line_3_3"
                ],
                "schedules": [
                    {
                        "amount": 50000.00,
                        "triggerInstant": 1767188976,
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "PAID_IN_FULL",
                                "step": 1
                            },
                            "count": 1
                        }
                    },
                    {
                        "amount": 10000.00,
                        "triggerInstant": 1782913776,
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "PAID_IN_FULL",
                                "step": 1
                            },
                            "count": 1
                        }
                    },
                    {
                        "amount": 40000.00,
                        "triggerInstant": **********,
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "PAID_IN_FULL",
                                "step": 1
                            },
                            "count": 1
                        }
                    }
                ]
            }
            """;
        CustomBillingSchedule customBillingSchedule = JacksonProvider.defaultMapper()
            .readValue(customBillingScheduleJson, CustomBillingSchedule.class);

        Order order = mock(Order.class);
        String orderId = "order_123";
        when(order.getOrderId()).thenReturn(orderId);
        when(order.getStartDate()).thenReturn(JAN_1_2020.toInstant());
        when(order.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(order.getBillingAnchorDate()).thenReturn(JAN_1_2020.toInstant());
        when(order.getBillingCycle()).thenReturn(new Recurrence(Cycle.CUSTOM, 1));
        when(order.getBillingTerm()).thenReturn(BillingTerm.UP_FRONT);
        when(order.getExternalSubscriptionId()).thenReturn("SUB-123");
        when(order.getCurrency()).thenReturn(Currency.getInstance("USD"));

        OrderLineItem orderLineItem1 = mock(OrderLineItem.class);
        when(orderLineItem1.getOrderLineId()).thenReturn("line_1");
        when(orderLineItem1.getAmount()).thenReturn(new BigDecimal("50000"));
        when(orderLineItem1.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem1.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(orderLineItem1.getIsRamp()).thenReturn(false);
        when(orderLineItem1.getChargeId()).thenReturn("charge_1");
        when(orderLineItem1.getExternalSubscriptionChargeId()).thenReturn("sub_charge_1");
        when(orderLineItem1.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_1");
        when(orderLineItem1.getOrderId()).thenReturn(orderId);

        OrderLineItem orderLineItem2 = mock(OrderLineItem.class);
        when(orderLineItem2.getOrderLineId()).thenReturn("line_2");
        when(orderLineItem2.getAmount()).thenReturn(new BigDecimal("30000"));
        when(orderLineItem2.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem2.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(orderLineItem2.getIsRamp()).thenReturn(false);
        when(orderLineItem2.getChargeId()).thenReturn("charge_2");
        when(orderLineItem2.getExternalSubscriptionChargeId()).thenReturn("sub_charge_2");
        when(orderLineItem2.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_2");
        when(orderLineItem2.getOrderId()).thenReturn(orderId);

        UUID rampGroupId = UUID.randomUUID();
        String rampSubscriptionChargeId = "ramp_sub_charge_1";

        OrderLineItem orderLineItem3_1 = mock(OrderLineItem.class);
        when(orderLineItem3_1.getOrderLineId()).thenReturn("line_3_1");
        when(orderLineItem3_1.getAmount()).thenReturn(new BigDecimal("4000"));
        when(orderLineItem3_1.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem3_1.getEndDate()).thenReturn(JAN_1_2021.toInstant());
        when(orderLineItem3_1.getIsRamp()).thenReturn(true);
        when(orderLineItem3_1.getRampGroupId()).thenReturn(rampGroupId);
        when(orderLineItem3_1.getChargeId()).thenReturn("charge_3");
        when(orderLineItem3_1.getExternalSubscriptionChargeId()).thenReturn(rampSubscriptionChargeId);
        when(orderLineItem3_1.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_ramp");
        when(orderLineItem3_1.getOrderId()).thenReturn(orderId);

        OrderLineItem orderLineItem3_2 = mock(OrderLineItem.class);
        when(orderLineItem3_2.getOrderLineId()).thenReturn("line_3_2");
        when(orderLineItem3_2.getAmount()).thenReturn(new BigDecimal("6000"));
        when(orderLineItem3_2.getEffectiveDate()).thenReturn(JAN_1_2021.toInstant());
        when(orderLineItem3_2.getEndDate()).thenReturn(JAN_1_2022.toInstant());
        when(orderLineItem3_2.getIsRamp()).thenReturn(true);
        when(orderLineItem3_2.getRampGroupId()).thenReturn(rampGroupId);
        when(orderLineItem3_2.getChargeId()).thenReturn("charge_3");
        when(orderLineItem3_2.getExternalSubscriptionChargeId()).thenReturn(rampSubscriptionChargeId);
        when(orderLineItem3_2.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_ramp");
        when(orderLineItem3_2.getOrderId()).thenReturn(orderId);

        OrderLineItem orderLineItem3_3 = mock(OrderLineItem.class);
        when(orderLineItem3_3.getOrderLineId()).thenReturn("line_3_3");
        when(orderLineItem3_3.getAmount()).thenReturn(new BigDecimal("10000"));
        when(orderLineItem3_3.getEffectiveDate()).thenReturn(JAN_1_2022.toInstant());
        when(orderLineItem3_3.getEndDate()).thenReturn(JAN_1_2023.toInstant());
        when(orderLineItem3_3.getIsRamp()).thenReturn(true);
        when(orderLineItem3_3.getRampGroupId()).thenReturn(rampGroupId);
        when(orderLineItem3_3.getChargeId()).thenReturn("charge_3");
        when(orderLineItem3_3.getExternalSubscriptionChargeId()).thenReturn(rampSubscriptionChargeId);
        when(orderLineItem3_3.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_ramp");
        when(orderLineItem3_3.getOrderId()).thenReturn(orderId);

        List<OrderLineItem> orderLineItems = List.of(orderLineItem1, orderLineItem2, orderLineItem3_1, orderLineItem3_2, orderLineItem3_3);
        when(order.getLineItems()).thenReturn(orderLineItems);
        when(order.getLineItemsNetEffect()).thenReturn(orderLineItems);

        when(prorationConfigurationGetService.resolveProrationConfig(order)).thenReturn(new ProrationConfig());

        List<List<InvoiceItem>> invoiceItems = subscriptionBillingPeriodService.generateInvoiceItemsForAdhocCustomBilling(
            customBillingSchedule,
            order,
            TIME_ZONE,
            false
        );

        // 100k 3Y order, with order lines 50, 30, 20. 20 is ramped yearly for 3 years.
        // Billing schedule is 50, 10, 40 with 3 trigger dates.
        // Invoice amounts should be split in a weighted fashion of billing schedule amount per period.
        // Example reference - https://docs.google.com/spreadsheets/d/16YcsjDHrDCb_KZkkCxk4ZgkOWvybT_pfT5ZnutyLCVQ/edit?gid=0#gid=0
        assertEquals(3, invoiceItems.size());

        List<InvoiceItem> firstPeriod = invoiceItems.get(0);
        firstPeriod.sort(Comparator.comparing(InvoiceItem::getOrderLineItemId));
        assertEquals(4, firstPeriod.size());
        matchServicePeriodAndAmount(new BigDecimal("25000.00"), LocalDate.of(2020, 1, 1), LocalDate.of(2021, 7, 1), firstPeriod.get(0));
        matchServicePeriodAndAmount(new BigDecimal("15000.00"), LocalDate.of(2020, 1, 1), LocalDate.of(2021, 7, 1), firstPeriod.get(1));
        matchServicePeriodAndAmount(new BigDecimal("4000.00"), LocalDate.of(2020, 1, 1), LocalDate.of(2021, 1, 1), firstPeriod.get(2));
        matchServicePeriodAndAmount(new BigDecimal("6000.00"), LocalDate.of(2021, 1, 1), LocalDate.of(2022, 1, 1), firstPeriod.get(3));

        List<InvoiceItem> secondPeriod = invoiceItems.get(1);
        secondPeriod.sort(Comparator.comparing(InvoiceItem::getOrderLineItemId));
        assertEquals(3, secondPeriod.size());
        matchServicePeriodAndAmount(new BigDecimal("5000.00"), LocalDate.of(2021, 7, 1), LocalDate.of(2021, 10, 20), secondPeriod.get(0));
        matchServicePeriodAndAmount(new BigDecimal("3000.00"), LocalDate.of(2021, 7, 1), LocalDate.of(2021, 10, 20), secondPeriod.get(1));
        matchServicePeriodAndAmount(new BigDecimal("2000.00"), LocalDate.of(2022, 1, 1), LocalDate.of(2022, 3, 13), secondPeriod.get(2));

        List<InvoiceItem> thirdPeriod = invoiceItems.get(2);
        thirdPeriod.sort(Comparator.comparing(InvoiceItem::getOrderLineItemId));
        assertEquals(3, secondPeriod.size());
        matchServicePeriodAndAmount(new BigDecimal("20000.00"), LocalDate.of(2021, 10, 20), LocalDate.of(2023, 1, 1), thirdPeriod.get(0));
        matchServicePeriodAndAmount(new BigDecimal("12000.00"), LocalDate.of(2021, 10, 20), LocalDate.of(2023, 1, 1), thirdPeriod.get(1));
        matchServicePeriodAndAmount(new BigDecimal("8000.00"), LocalDate.of(2022, 3, 13), LocalDate.of(2023, 1, 1), thirdPeriod.get(2));
    }

    /**
     * 4 order lines with each amounting to $3.75. Total = $15
     * Custom billing segments of 30% 40% and 30% distribution of $15, i.e. $4.5, $6, $4.5
     * Segment 1 - Total amt 4.5
     * Order line amt = 0.3 * 3.75 = 1.125 each
     * Invoice lines with default rounding = 1.13, Total amt = 4.52, additional 2 cents
     * Segment 2 - Total amt 6
     * Order line amt = 0.4 * 3.75 = 1.5 each, no rounding artifacts
     * Segment 3 - Total amt 4.5
     * Order line amt = 0.3 * 3.75 = 1.125 each
     * Invoice lines with default rounding = 1.13, Total amt = 4.52, additional 2 cents
     * Resulting invoices --
     * Segment 1 - 1.12, 1.12, 1.13, 1.13
     * Segment 2 - 1.5, 1.5, 1.5, 1.5
     * Segment 3 - 1.13, 1.13, 1.12, 1.12
     * @throws JsonProcessingException
     */
    @Test
    public void testGenerateInvoiceItemsForAdhocCustomBilling_roundingAdjustments() throws JsonProcessingException {
        String customBillingScheduleJson =
            """
            {
                "version": "V1",
                "orderId": "order_456",
                "orderLines": [
                    "line_1",
                    "line_2",
                    "line_3",
                    "line_4"
                ],
                "schedules": [
                    {
                        "amount": 4.5,
                        "triggerInstant": 1767188976,
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "PAID_IN_FULL",
                                "step": 1
                            },
                            "count": 1
                        }
                    },
                    {
                        "amount": 6.0,
                        "triggerInstant": 1782913776,
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "PAID_IN_FULL",
                                "step": 1
                            },
                            "count": 1
                        }
                    },
                    {
                        "amount": 4.5,
                        "triggerInstant": **********,
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "PAID_IN_FULL",
                                "step": 1
                            },
                            "count": 1
                        }
                    }
                ]
            }
            """;
        CustomBillingSchedule customBillingSchedule = JacksonProvider.defaultMapper()
            .readValue(customBillingScheduleJson, CustomBillingSchedule.class);

        Order order = mock(Order.class);
        String orderId = "order_456";
        when(order.getOrderId()).thenReturn(orderId);
        when(order.getStartDate()).thenReturn(JAN_1_2020.toInstant());
        when(order.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(order.getBillingAnchorDate()).thenReturn(JAN_1_2020.toInstant());
        when(order.getBillingCycle()).thenReturn(new Recurrence(Cycle.CUSTOM, 1));
        when(order.getBillingTerm()).thenReturn(BillingTerm.UP_FRONT);
        when(order.getExternalSubscriptionId()).thenReturn("SUB-456");
        when(order.getCurrency()).thenReturn(Currency.getInstance("USD"));

        OrderLineItem orderLineItem1 = mock(OrderLineItem.class);
        when(orderLineItem1.getOrderLineId()).thenReturn("line_1");
        when(orderLineItem1.getAmount()).thenReturn(new BigDecimal("3.75"));
        when(orderLineItem1.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem1.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(orderLineItem1.getIsRamp()).thenReturn(false);
        when(orderLineItem1.getChargeId()).thenReturn("charge_1");
        when(orderLineItem1.getExternalSubscriptionChargeId()).thenReturn("sub_charge_1");
        when(orderLineItem1.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_1");
        when(orderLineItem1.getOrderId()).thenReturn(orderId);

        OrderLineItem orderLineItem2 = mock(OrderLineItem.class);
        when(orderLineItem2.getOrderLineId()).thenReturn("line_2");
        when(orderLineItem2.getAmount()).thenReturn(new BigDecimal("3.75"));
        when(orderLineItem2.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem2.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(orderLineItem2.getIsRamp()).thenReturn(false);
        when(orderLineItem2.getChargeId()).thenReturn("charge_2");
        when(orderLineItem2.getExternalSubscriptionChargeId()).thenReturn("sub_charge_2");
        when(orderLineItem2.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_2");
        when(orderLineItem2.getOrderId()).thenReturn(orderId);

        OrderLineItem orderLineItem3 = mock(OrderLineItem.class);
        when(orderLineItem3.getOrderLineId()).thenReturn("line_3");
        when(orderLineItem3.getAmount()).thenReturn(new BigDecimal("3.75"));
        when(orderLineItem3.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem3.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(orderLineItem3.getIsRamp()).thenReturn(false);
        when(orderLineItem3.getChargeId()).thenReturn("charge_3");
        when(orderLineItem3.getExternalSubscriptionChargeId()).thenReturn("sub_charge_3");
        when(orderLineItem3.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_3");
        when(orderLineItem3.getOrderId()).thenReturn(orderId);

        OrderLineItem orderLineItem4 = mock(OrderLineItem.class);
        when(orderLineItem4.getOrderLineId()).thenReturn("line_4");
        when(orderLineItem4.getAmount()).thenReturn(new BigDecimal("3.75"));
        when(orderLineItem4.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem4.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(orderLineItem4.getIsRamp()).thenReturn(false);
        when(orderLineItem4.getChargeId()).thenReturn("charge_4");
        when(orderLineItem4.getExternalSubscriptionChargeId()).thenReturn("sub_charge_4");
        when(orderLineItem4.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_4");
        when(orderLineItem4.getOrderId()).thenReturn(orderId);

        List<OrderLineItem> orderLineItems = List.of(orderLineItem1, orderLineItem2, orderLineItem3, orderLineItem4);
        when(order.getLineItems()).thenReturn(orderLineItems);
        when(order.getLineItemsNetEffect()).thenReturn(orderLineItems);

        when(prorationConfigurationGetService.resolveProrationConfig(order)).thenReturn(new ProrationConfig());

        List<List<InvoiceItem>> invoiceItems = subscriptionBillingPeriodService.generateInvoiceItemsForAdhocCustomBilling(
            customBillingSchedule,
            order,
            TIME_ZONE,
            false
        );

        assertEquals(3, invoiceItems.size());

        List<InvoiceItem> firstPeriod = invoiceItems.get(0);
        firstPeriod.sort(Comparator.comparing(InvoiceItem::getOrderLineItemId));
        assertEquals(4, firstPeriod.size());
        matchServicePeriodAndAmount(new BigDecimal("1.12"), LocalDate.of(2020, 1, 1), LocalDate.of(2020, 11, 24), firstPeriod.get(0));
        matchServicePeriodAndAmount(new BigDecimal("1.12"), LocalDate.of(2020, 1, 1), LocalDate.of(2020, 11, 24), firstPeriod.get(1));
        matchServicePeriodAndAmount(new BigDecimal("1.13"), LocalDate.of(2020, 1, 1), LocalDate.of(2020, 11, 26), firstPeriod.get(2));
        matchServicePeriodAndAmount(new BigDecimal("1.13"), LocalDate.of(2020, 1, 1), LocalDate.of(2020, 11, 26), firstPeriod.get(3));

        List<InvoiceItem> secondPeriod = invoiceItems.get(1);
        secondPeriod.sort(Comparator.comparing(InvoiceItem::getOrderLineItemId));
        assertEquals(4, secondPeriod.size());
        matchServicePeriodAndAmount(new BigDecimal("1.50"), LocalDate.of(2020, 11, 24), LocalDate.of(2022, 2, 5), secondPeriod.get(0));
        matchServicePeriodAndAmount(new BigDecimal("1.50"), LocalDate.of(2020, 11, 24), LocalDate.of(2022, 2, 5), secondPeriod.get(1));
        matchServicePeriodAndAmount(new BigDecimal("1.50"), LocalDate.of(2020, 11, 26), LocalDate.of(2022, 2, 8), secondPeriod.get(2));
        matchServicePeriodAndAmount(new BigDecimal("1.50"), LocalDate.of(2020, 11, 26), LocalDate.of(2022, 2, 8), secondPeriod.get(3));

        List<InvoiceItem> thirdPeriod = invoiceItems.get(2);
        thirdPeriod.sort(Comparator.comparing(InvoiceItem::getOrderLineItemId));
        assertEquals(4, thirdPeriod.size());
        matchServicePeriodAndAmount(new BigDecimal("1.13"), LocalDate.of(2022, 2, 5), LocalDate.of(2023, 1, 1), thirdPeriod.get(0));
        matchServicePeriodAndAmount(new BigDecimal("1.13"), LocalDate.of(2022, 2, 5), LocalDate.of(2023, 1, 1), thirdPeriod.get(1));
        matchServicePeriodAndAmount(new BigDecimal("1.12"), LocalDate.of(2022, 2, 8), LocalDate.of(2023, 1, 1), thirdPeriod.get(2));
        matchServicePeriodAndAmount(new BigDecimal("1.12"), LocalDate.of(2022, 2, 8), LocalDate.of(2023, 1, 1), thirdPeriod.get(3));
    }

    /**
     * 4 order lines with each amounting to $3.75. Total = $15
     * 4th line is ramped yearly with amounts 1.25, 1.75 and 0.75
     * Custom billing segments of 30% 40% and 30% distribution of $15, i.e. $4.5, $6, $4.5
     * Segment 1 - Total amt 4.5
     * Order line amt = 0.3 * 3.75 = 1.125 each
     * Invoice lines with default rounding = 1.13, Total amt = 4.52, additional 2 cents
     * Segment 2 - Total amt 6
     * Order line amt = 0.4 * 3.75 = 1.5 each, no rounding artifacts
     * Segment 3 - Total amt 4.5
     * Order line amt = 0.3 * 3.75 = 1.125 each
     * Invoice lines with default rounding = 1.13, Total amt = 4.52, additional 2 cents
     * Resulting invoices --
     * Segment 1 - 1.12, 1.12, 1.13, 1.13
     * Segment 2 - 1.5, 1.5, 1.5, 0.12, 1.38
     * Segment 3 - 1.13, 1.13, 1.12, 0.37, 0.75
     * @throws JsonProcessingException
     */
    @Test
    public void testGenerateInvoiceItemsForAdhocCustomBilling_roundingAdjustments_withRampGroup() throws JsonProcessingException {
        String customBillingScheduleJson =
            """
            {
                "version": "V1",
                "orderId": "order_456",
                "orderLines": [
                    "line_1",
                    "line_2",
                    "line_3",
                    "line_4_1",
                    "line_4_2",
                    "line_4_3"
                ],
                "schedules": [
                    {
                        "amount": 4.5,
                        "triggerInstant": 1767188976,
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "PAID_IN_FULL",
                                "step": 1
                            },
                            "count": 1
                        }
                    },
                    {
                        "amount": 6.0,
                        "triggerInstant": 1782913776,
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "PAID_IN_FULL",
                                "step": 1
                            },
                            "count": 1
                        }
                    },
                    {
                        "amount": 4.5,
                        "triggerInstant": **********,
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "PAID_IN_FULL",
                                "step": 1
                            },
                            "count": 1
                        }
                    }
                ]
            }
            """;
        CustomBillingSchedule customBillingSchedule = JacksonProvider.defaultMapper()
            .readValue(customBillingScheduleJson, CustomBillingSchedule.class);

        Order order = mock(Order.class);
        String orderId = "order_456";
        when(order.getOrderId()).thenReturn(orderId);
        when(order.getStartDate()).thenReturn(JAN_1_2020.toInstant());
        when(order.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(order.getBillingAnchorDate()).thenReturn(JAN_1_2020.toInstant());
        when(order.getBillingCycle()).thenReturn(new Recurrence(Cycle.CUSTOM, 1));
        when(order.getBillingTerm()).thenReturn(BillingTerm.UP_FRONT);
        when(order.getExternalSubscriptionId()).thenReturn("SUB-456");
        when(order.getCurrency()).thenReturn(Currency.getInstance("USD"));

        OrderLineItem orderLineItem1 = mock(OrderLineItem.class);
        when(orderLineItem1.getOrderLineId()).thenReturn("line_1");
        when(orderLineItem1.getAmount()).thenReturn(new BigDecimal("3.75"));
        when(orderLineItem1.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem1.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(orderLineItem1.getIsRamp()).thenReturn(false);
        when(orderLineItem1.getChargeId()).thenReturn("charge_1");
        when(orderLineItem1.getExternalSubscriptionChargeId()).thenReturn("sub_charge_1");
        when(orderLineItem1.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_1");
        when(orderLineItem1.getOrderId()).thenReturn(orderId);

        OrderLineItem orderLineItem2 = mock(OrderLineItem.class);
        when(orderLineItem2.getOrderLineId()).thenReturn("line_2");
        when(orderLineItem2.getAmount()).thenReturn(new BigDecimal("3.75"));
        when(orderLineItem2.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem2.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(orderLineItem2.getIsRamp()).thenReturn(false);
        when(orderLineItem2.getChargeId()).thenReturn("charge_2");
        when(orderLineItem2.getExternalSubscriptionChargeId()).thenReturn("sub_charge_2");
        when(orderLineItem2.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_2");
        when(orderLineItem2.getOrderId()).thenReturn(orderId);

        OrderLineItem orderLineItem3 = mock(OrderLineItem.class);
        when(orderLineItem3.getOrderLineId()).thenReturn("line_3");
        when(orderLineItem3.getAmount()).thenReturn(new BigDecimal("3.75"));
        when(orderLineItem3.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem3.getEndDate()).thenReturn(JAN_1_2020.plusYears(3).toInstant());
        when(orderLineItem3.getIsRamp()).thenReturn(false);
        when(orderLineItem3.getChargeId()).thenReturn("charge_3");
        when(orderLineItem3.getExternalSubscriptionChargeId()).thenReturn("sub_charge_3");
        when(orderLineItem3.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_3");
        when(orderLineItem3.getOrderId()).thenReturn(orderId);

        UUID rampGroupId = UUID.randomUUID();
        String rampSubscriptionChargeId = "ramp_sub_charge_4";

        OrderLineItem orderLineItem4_1 = mock(OrderLineItem.class);
        when(orderLineItem4_1.getOrderLineId()).thenReturn("line_4_1");
        when(orderLineItem4_1.getAmount()).thenReturn(new BigDecimal("1.25"));
        when(orderLineItem4_1.getEffectiveDate()).thenReturn(JAN_1_2020.toInstant());
        when(orderLineItem4_1.getEndDate()).thenReturn(JAN_1_2021.toInstant());
        when(orderLineItem4_1.getIsRamp()).thenReturn(true);
        when(orderLineItem4_1.getRampGroupId()).thenReturn(rampGroupId);
        when(orderLineItem4_1.getChargeId()).thenReturn("charge_4");
        when(orderLineItem4_1.getExternalSubscriptionChargeId()).thenReturn(rampSubscriptionChargeId);
        when(orderLineItem4_1.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_ramp");
        when(orderLineItem4_1.getOrderId()).thenReturn(orderId);

        OrderLineItem orderLineItem4_2 = mock(OrderLineItem.class);
        when(orderLineItem4_2.getOrderLineId()).thenReturn("line_4_2");
        when(orderLineItem4_2.getAmount()).thenReturn(new BigDecimal("1.75"));
        when(orderLineItem4_2.getEffectiveDate()).thenReturn(JAN_1_2021.toInstant());
        when(orderLineItem4_2.getEndDate()).thenReturn(JAN_1_2022.toInstant());
        when(orderLineItem4_2.getIsRamp()).thenReturn(true);
        when(orderLineItem4_2.getRampGroupId()).thenReturn(rampGroupId);
        when(orderLineItem4_2.getChargeId()).thenReturn("charge_4");
        when(orderLineItem4_2.getExternalSubscriptionChargeId()).thenReturn(rampSubscriptionChargeId);
        when(orderLineItem4_2.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_ramp");
        when(orderLineItem4_2.getOrderId()).thenReturn(orderId);

        OrderLineItem orderLineItem4_3 = mock(OrderLineItem.class);
        when(orderLineItem4_3.getOrderLineId()).thenReturn("line_4_3");
        when(orderLineItem4_3.getAmount()).thenReturn(new BigDecimal("0.75"));
        when(orderLineItem4_3.getEffectiveDate()).thenReturn(JAN_1_2022.toInstant());
        when(orderLineItem4_3.getEndDate()).thenReturn(JAN_1_2023.toInstant());
        when(orderLineItem4_3.getIsRamp()).thenReturn(true);
        when(orderLineItem4_3.getRampGroupId()).thenReturn(rampGroupId);
        when(orderLineItem4_3.getChargeId()).thenReturn("charge_4");
        when(orderLineItem4_3.getExternalSubscriptionChargeId()).thenReturn(rampSubscriptionChargeId);
        when(orderLineItem4_3.getSubscriptionChargeGroupId()).thenReturn("sub_charge_group_ramp");
        when(orderLineItem4_3.getOrderId()).thenReturn(orderId);

        List<OrderLineItem> orderLineItems = List.of(
            orderLineItem1,
            orderLineItem2,
            orderLineItem3,
            orderLineItem4_1,
            orderLineItem4_2,
            orderLineItem4_3
        );
        when(order.getLineItems()).thenReturn(orderLineItems);
        when(order.getLineItemsNetEffect()).thenReturn(orderLineItems);

        when(prorationConfigurationGetService.resolveProrationConfig(order)).thenReturn(new ProrationConfig());

        List<List<InvoiceItem>> invoiceItems = subscriptionBillingPeriodService.generateInvoiceItemsForAdhocCustomBilling(
            customBillingSchedule,
            order,
            TIME_ZONE,
            false
        );

        assertEquals(3, invoiceItems.size());

        List<InvoiceItem> firstPeriod = invoiceItems.get(0);
        firstPeriod.sort(Comparator.comparing(InvoiceItem::getOrderLineItemId));
        assertEquals(4, firstPeriod.size());
        matchServicePeriodAndAmount(new BigDecimal("1.12"), LocalDate.of(2020, 1, 1), LocalDate.of(2020, 11, 24), firstPeriod.get(0));
        matchServicePeriodAndAmount(new BigDecimal("1.12"), LocalDate.of(2020, 1, 1), LocalDate.of(2020, 11, 24), firstPeriod.get(1));
        matchServicePeriodAndAmount(new BigDecimal("1.13"), LocalDate.of(2020, 1, 1), LocalDate.of(2020, 11, 26), firstPeriod.get(2));
        matchServicePeriodAndAmount(new BigDecimal("1.13"), LocalDate.of(2020, 1, 1), LocalDate.of(2020, 11, 26), firstPeriod.get(3));

        List<InvoiceItem> secondPeriod = invoiceItems.get(1);
        secondPeriod.sort(Comparator.comparing(InvoiceItem::getOrderLineItemId));
        assertEquals(5, secondPeriod.size());
        matchServicePeriodAndAmount(new BigDecimal("1.50"), LocalDate.of(2020, 11, 24), LocalDate.of(2022, 2, 5), secondPeriod.get(0));
        matchServicePeriodAndAmount(new BigDecimal("1.50"), LocalDate.of(2020, 11, 24), LocalDate.of(2022, 2, 5), secondPeriod.get(1));
        matchServicePeriodAndAmount(new BigDecimal("1.50"), LocalDate.of(2020, 11, 26), LocalDate.of(2022, 2, 8), secondPeriod.get(2));
        matchServicePeriodAndAmount(new BigDecimal("0.12"), LocalDate.of(2020, 11, 26), LocalDate.of(2021, 1, 1), secondPeriod.get(3));
        matchServicePeriodAndAmount(new BigDecimal("1.38"), LocalDate.of(2021, 1, 1), LocalDate.of(2021, 10, 15), secondPeriod.get(4));

        List<InvoiceItem> thirdPeriod = invoiceItems.get(2);
        thirdPeriod.sort(Comparator.comparing(InvoiceItem::getOrderLineItemId));
        assertEquals(5, thirdPeriod.size());
        matchServicePeriodAndAmount(new BigDecimal("1.13"), LocalDate.of(2022, 2, 5), LocalDate.of(2023, 1, 1), thirdPeriod.get(0));
        matchServicePeriodAndAmount(new BigDecimal("1.13"), LocalDate.of(2022, 2, 5), LocalDate.of(2023, 1, 1), thirdPeriod.get(1));
        matchServicePeriodAndAmount(new BigDecimal("1.12"), LocalDate.of(2022, 2, 8), LocalDate.of(2023, 1, 1), thirdPeriod.get(2));
        matchServicePeriodAndAmount(new BigDecimal("0.37"), LocalDate.of(2021, 10, 15), LocalDate.of(2022, 1, 1), thirdPeriod.get(3));
        matchServicePeriodAndAmount(new BigDecimal("0.75"), LocalDate.of(2022, 1, 1), LocalDate.of(2023, 1, 1), thirdPeriod.get(4));
    }

    @Test
    public void testGenerateBillingPeriodsForRecurrenceCustomBilling() throws JsonProcessingException {
        String customBillingScheduleJson =
            """
            {
                "version": "V1",
                "orderId": "ORD-306DJFM",
                "orderLines": [
                    "5fad26c9-82a8-4033-8587-cac2c2e65f09"
                ],
                "schedules": [
                    {
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "QUARTER",
                                "step": 1
                            },
                            "count": 4
                        }
                    },
                    {
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "YEAR",
                                "step": 1
                            },
                            "count": 1
                        }
                    },
                    {
                        "recurrenceWithCount": {
                            "recurrence": {
                                "cycle": "MONTH",
                                "step": 1
                            },
                            "count": 12
                        }
                    }
                ]
            }
            """;

        CustomBillingSchedule customBillingSchedule = JacksonProvider.defaultMapper()
            .readValue(customBillingScheduleJson, CustomBillingSchedule.class);
        List<List<BillingPeriod>> billingPeriods = subscriptionBillingPeriodService.generateBillingPeriodsForRecurrenceCustomBilling(
            customBillingSchedule,
            Instant.parse("2024-01-01T14:00:00Z"),
            Instant.parse("2027-01-01T14:00:00Z"),
            Instant.parse("2027-01-01T14:00:00Z"),
            TIME_ZONE.toZoneId(),
            BillingTerm.UP_FRONT
        );

        assertEquals(3, billingPeriods.size());

        List<BillingPeriod> firstPeriod = billingPeriods.get(0);
        assertEquals(4, firstPeriod.size());
        matchBillingPeriod(firstPeriod.get(0), LocalDate.of(2024, 1, 1), LocalDate.of(2024, 4, 1));
        matchBillingPeriod(firstPeriod.get(1), LocalDate.of(2024, 4, 1), LocalDate.of(2024, 7, 1));
        matchBillingPeriod(firstPeriod.get(2), LocalDate.of(2024, 7, 1), LocalDate.of(2024, 10, 1));
        matchBillingPeriod(firstPeriod.get(3), LocalDate.of(2024, 10, 1), LocalDate.of(2025, 1, 1));

        List<BillingPeriod> secondPeriod = billingPeriods.get(1);
        assertEquals(1, secondPeriod.size());
        matchBillingPeriod(secondPeriod.get(0), LocalDate.of(2025, 1, 1), LocalDate.of(2026, 1, 1));

        List<BillingPeriod> thirdPeriod = billingPeriods.get(2);
        assertEquals(12, thirdPeriod.size());
        matchBillingPeriod(thirdPeriod.get(0), LocalDate.of(2026, 1, 1), LocalDate.of(2026, 2, 1));
        matchBillingPeriod(thirdPeriod.get(1), LocalDate.of(2026, 2, 1), LocalDate.of(2026, 3, 1));
        matchBillingPeriod(thirdPeriod.get(2), LocalDate.of(2026, 3, 1), LocalDate.of(2026, 4, 1));
        matchBillingPeriod(thirdPeriod.get(3), LocalDate.of(2026, 4, 1), LocalDate.of(2026, 5, 1));
        matchBillingPeriod(thirdPeriod.get(4), LocalDate.of(2026, 5, 1), LocalDate.of(2026, 6, 1));
        matchBillingPeriod(thirdPeriod.get(5), LocalDate.of(2026, 6, 1), LocalDate.of(2026, 7, 1));
        matchBillingPeriod(thirdPeriod.get(6), LocalDate.of(2026, 7, 1), LocalDate.of(2026, 8, 1));
        matchBillingPeriod(thirdPeriod.get(7), LocalDate.of(2026, 8, 1), LocalDate.of(2026, 9, 1));
        matchBillingPeriod(thirdPeriod.get(8), LocalDate.of(2026, 9, 1), LocalDate.of(2026, 10, 1));
        matchBillingPeriod(thirdPeriod.get(9), LocalDate.of(2026, 10, 1), LocalDate.of(2026, 11, 1));
        matchBillingPeriod(thirdPeriod.get(10), LocalDate.of(2026, 11, 1), LocalDate.of(2026, 12, 1));
        matchBillingPeriod(thirdPeriod.get(11), LocalDate.of(2026, 12, 1), LocalDate.of(2027, 1, 1));
    }

    private void matchServicePeriodAndAmount(BigDecimal amount, LocalDate startDate, LocalDate endDate, InvoiceItem invoiceItem) {
        assertEquals(amount, invoiceItem.getAmount());
        assertEquals(startDate, invoiceItem.getPeriodStartDate().atZone(ZONE_ID).toLocalDate());
        assertEquals(endDate, invoiceItem.getPeriodEndDate().atZone(ZONE_ID).toLocalDate());
    }

    private void matchBillingPeriod(BillingPeriod period, LocalDate startDate, LocalDate endDate) {
        assertEquals(startDate, period.getStart().atZone(ZONE_ID).toLocalDate());
        assertEquals(endDate, period.getEnd().atZone(ZONE_ID).toLocalDate());
    }

    private ZonedDateTime getPeriodStart(BillingPeriod billingPeriod) {
        return ZonedDateTime.ofInstant(billingPeriod.getStart(), ZONE_ID);
    }

    private ZonedDateTime getPeriodEnd(BillingPeriod billingPeriod) {
        return ZonedDateTime.ofInstant(billingPeriod.getEnd(), ZONE_ID);
    }

    private boolean validBillingPeriods(List<BillingPeriod> billingPeriods) {
        BillingPeriod prevPeriod = null;

        for (var billingPeriod : billingPeriods) {
            if (!billingPeriod.getStart().isBefore(billingPeriod.getEnd())) {
                return false;
            }

            if (prevPeriod != null && !prevPeriod.getEnd().equals(billingPeriod.getStart())) {
                return false;
            }

            prevPeriod = billingPeriod;
        }

        return true;
    }

    private Subscription getMockSubscription(Instant startDate, Instant endDate, Recurrence billingCycle) {
        var subscriptionEntity = new SubscriptionEntity();
        subscriptionEntity.setStartDate(startDate);
        subscriptionEntity.setEndDate(endDate);
        subscriptionEntity.setBillingAnchorDate(startDate);
        subscriptionEntity.setBillingTerm(BillingTerm.UP_FRONT);
        subscriptionEntity.setBillingCycle(billingCycle);
        return new SubscriptionImpl(subscriptionEntity, List.of());
    }
}
