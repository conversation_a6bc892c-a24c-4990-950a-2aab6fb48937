package com.subskribe.billy.subscription.services;

import static com.subskribe.billy.subscription.services.SubscriptionChangeTaskProcessor.CHARGE_CHANGE_TASK_TYPE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.subskribe.billy.configuration.dynamic.Feature;
import com.subskribe.billy.configuration.dynamic.FeatureService;
import com.subskribe.billy.customfield.model.CustomField;
import com.subskribe.billy.di.hk2.providers.DSLContextProvider;
import com.subskribe.billy.event.model.Event;
import com.subskribe.billy.event.model.EventType;
import com.subskribe.billy.event.service.EventPublishingService;
import com.subskribe.billy.exception.ObjectNotFoundException;
import com.subskribe.billy.notification.service.TestNotificationService;
import com.subskribe.billy.shared.enums.BillyObjectType;
import com.subskribe.billy.shared.task.queue.model.QueuedTaskRequest;
import com.subskribe.billy.shared.task.queue.scheduler.TaskDispatcher;
import com.subskribe.billy.subscription.Subscription;
import com.subskribe.billy.subscription.SubscriptionCharge;
import com.subskribe.billy.subscription.db.SubscriptionChargeChangeScheduleDAO;
import com.subskribe.billy.subscription.db.SubscriptionStatusChangeScheduleDAO;
import com.subskribe.billy.subscription.model.ImmutableSubscriptionChargeChangeSchedule;
import com.subskribe.billy.subscription.model.ImmutableSubscriptionStatusChangeSchedule;
import com.subskribe.billy.subscription.model.SubscriptionChargeChangeSchedule;
import com.subskribe.billy.subscription.model.SubscriptionEntity;
import com.subskribe.billy.subscription.model.SubscriptionStatusChangeSchedule;
import com.subskribe.billy.tenant.TenantIdProvider;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.TransactionalRunnable;
import org.jooq.impl.DSL;
import org.jooq.impl.DefaultConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class SubscriptionLifecycleScheduleServiceTest {

    private static final String TENANT_ID = "test-tenant";
    private static final String ENTITY_ID = "test-entity";
    private static final String SUBSCRIPTION_ID = "test-subscription";
    private static final String ACCOUNT_ID = "test-account";
    private static final String CHARGE_ID_1 = "charge-1";
    private static final String CHARGE_ID_2 = "charge-2";

    @Mock
    private SubscriptionGetService subscriptionGetService;

    @Mock
    private SubscriptionChargeChangeScheduleDAO subscriptionChargeChangeScheduleDAO;

    @Mock
    private SubscriptionStatusChangeScheduleDAO subscriptionStatusChangeScheduleDAO;

    @Mock
    private DSLContextProvider dslContextProvider;

    @Mock
    private TenantIdProvider tenantIdProvider;

    @Mock
    private EventPublishingService eventPublishingService;

    @Mock
    private SubscriptionEventService subscriptionEventService;

    @Mock
    private TestNotificationService testNotificationService;

    @Mock
    private DSLContext dslContext;

    @Mock
    private FeatureService featureService;

    @Mock
    private TaskDispatcher taskDispatcher;

    private Configuration configuration;
    private Clock fixedClock;
    private SubscriptionLifecycleScheduleService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        configuration = new DefaultConfiguration();
        fixedClock = Clock.fixed(Instant.parse("2023-01-01T10:00:00Z"), ZoneId.systemDefault());

        when(tenantIdProvider.provideTenantIdString()).thenReturn(TENANT_ID);
        when(dslContextProvider.get(TENANT_ID)).thenReturn(dslContext);

        doAnswer(invocation -> {
            TransactionalRunnable runnable = invocation.getArgument(0);
            runnable.run(configuration);
            return null;
        })
            .when(dslContext)
            .transaction(any(TransactionalRunnable.class));

        when(subscriptionChargeChangeScheduleDAO.createSchedules(anyList(), any())).thenAnswer(invocation -> {
            List<SubscriptionChargeChangeSchedule> schedules = invocation.getArgument(0);
            return schedules.stream().map(s -> ImmutableSubscriptionChargeChangeSchedule.builder().from(s).id(UUID.randomUUID()).build()).toList();
        });

        service = new SubscriptionLifecycleScheduleService(
            subscriptionGetService,
            subscriptionChargeChangeScheduleDAO,
            subscriptionStatusChangeScheduleDAO,
            dslContextProvider,
            tenantIdProvider,
            eventPublishingService,
            fixedClock,
            subscriptionEventService,
            testNotificationService,
            taskDispatcher,
            featureService
        );
    }

    @Test
    void scheduleNewChargeChanges_WithExistingRecords_ShouldDoNothing() {
        List<SubscriptionCharge> charges = List.of(
            createCharge(CHARGE_ID_1, fixedClock.instant().plusSeconds(3600), fixedClock.instant().plusSeconds(7200))
        );
        Subscription subscription = createSubscription(2, charges);
        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);

        when(subscriptionChargeChangeScheduleDAO.hasUnprocessedRecordsForVersion(SUBSCRIPTION_ID, 2)).thenReturn(true);

        // When
        service.scheduleNewChanges(SUBSCRIPTION_ID);

        // Then
        verify(subscriptionChargeChangeScheduleDAO, never()).createSchedules(anyList(), any());
    }

    @Test
    void scheduleNewChargeChanges_WithFutureChanges_ShouldCreateSchedules() {
        // Given
        Instant now = fixedClock.instant();
        Instant futureStart = now.plusSeconds(3600);
        Instant futureEnd = now.plusSeconds(7200);
        SubscriptionCharge charge = createCharge(CHARGE_ID_1, futureStart, futureEnd);
        Subscription subscription = createSubscription(1, List.of(charge));
        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);

        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);
        when(subscriptionChargeChangeScheduleDAO.hasUnprocessedRecordsForVersion(SUBSCRIPTION_ID, 1)).thenReturn(false);

        // When
        service.scheduleNewChanges(SUBSCRIPTION_ID);

        // Then
        ArgumentCaptor<List<SubscriptionChargeChangeSchedule>> schedulesCaptor = ArgumentCaptor.forClass(List.class);
        verify(subscriptionChargeChangeScheduleDAO).createSchedules(schedulesCaptor.capture(), any());

        List<SubscriptionChargeChangeSchedule> capturedSchedules = schedulesCaptor.getValue();
        assertThat(capturedSchedules).hasSize(2);
        assertThat(capturedSchedules.get(0).getChargeIdsStarting()).isEqualTo(CHARGE_ID_1);
        assertThat(capturedSchedules.get(1).getChargeIdsEnding()).isEqualTo(CHARGE_ID_1);

        verify(subscriptionChargeChangeScheduleDAO).deleteUnprocessedSchedulesForOtherVersions(eq(SUBSCRIPTION_ID), eq(1), any());
    }

    @Test
    void scheduleNewChargeChanges_WithPastChanges_ShouldIgnoreThem() {
        // Given
        Instant now = fixedClock.instant();
        Instant pastStart = now.minusSeconds(3600);
        Instant pastEnd = now.minusSeconds(1800);

        SubscriptionCharge charge = createCharge(CHARGE_ID_1, pastStart, pastEnd);
        Subscription subscription = createSubscription(1, List.of(charge));
        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);
        when(subscriptionChargeChangeScheduleDAO.hasUnprocessedRecordsForVersion(SUBSCRIPTION_ID, 1)).thenReturn(false);

        service.scheduleNewChanges(SUBSCRIPTION_ID);

        verify(subscriptionChargeChangeScheduleDAO, never()).createSchedules(any(), any());
    }

    @Test
    void processScheduledChargeChanges_WithOverdueChanges_ShouldProcessThemAll() {
        // Given
        UUID changeId = UUID.randomUUID();
        SubscriptionChargeChangeSchedule change = createChangeSchedule(changeId, SUBSCRIPTION_ID, 1);

        List<SubscriptionCharge> charges = List.of(
            createCharge(CHARGE_ID_1, fixedClock.instant().minusSeconds(3600), fixedClock.instant().plusSeconds(3600)),
            createCharge(CHARGE_ID_2, fixedClock.instant().minusSeconds(3600), fixedClock.instant().plusSeconds(3600))
        );
        Subscription subscription = createSubscription(1, charges);

        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);
        when(subscriptionChargeChangeScheduleDAO.getUnprocessedOverdueChanges()).thenReturn(List.of(change));
        // When
        service.processScheduledChargeChanges();

        // Then
        verify(eventPublishingService).publishEventInTransaction(
            any(DSLContext.class),
            eq(EventType.SUBSCRIPTION_CHARGE_CHANGE),
            eq(TENANT_ID),
            eq(ENTITY_ID),
            eq(ACCOUNT_ID),
            any(byte[].class)
        );

        verify(subscriptionChargeChangeScheduleDAO).markAsProcessed(eq(changeId), any(DSLContext.class));
    }

    @Test
    void processScheduledChargeChanges_SubscriptionNotFound_ShouldSkipProcessing() {
        // Given
        UUID changeId = UUID.randomUUID();
        SubscriptionChargeChangeSchedule change = createChangeSchedule(changeId, SUBSCRIPTION_ID, 1);

        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenThrow(
            new ObjectNotFoundException(BillyObjectType.SUBSCRIPTION, "Not found")
        );
        when(subscriptionChargeChangeScheduleDAO.getUnprocessedOverdueChanges()).thenReturn(List.of(change));

        // When
        service.processScheduledChargeChanges();

        // Then
        verify(subscriptionChargeChangeScheduleDAO, never()).markAsProcessed(any(), any());
    }

    @Test
    void processScheduledChargeChanges_VersionMismatch_ShouldSkipProcessing() {
        // Given
        UUID changeId = UUID.randomUUID();
        SubscriptionChargeChangeSchedule change = createChangeSchedule(changeId, SUBSCRIPTION_ID, 1);

        Subscription subscription = mock(Subscription.class);
        when(subscription.getSubscriptionId()).thenReturn(SUBSCRIPTION_ID);
        when(subscription.getVersion()).thenReturn(2); // Different version

        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);
        when(subscriptionChargeChangeScheduleDAO.getUnprocessedOverdueChanges()).thenReturn(List.of(change));

        // When
        service.processScheduledChargeChanges();

        // Then
        verify(subscriptionChargeChangeScheduleDAO, never()).markAsProcessed(any(), any());
    }

    @Test
    void deleteSchedulesForSubscriptionVersion_ShouldCallDAO() {
        // Given
        Subscription subscription = mock(Subscription.class);
        when(subscription.getSubscriptionId()).thenReturn(SUBSCRIPTION_ID);
        when(subscription.getVersion()).thenReturn(1);

        try (MockedStatic<DSL> mockedDSL = Mockito.mockStatic(DSL.class)) {
            mockedDSL.when(() -> DSL.using(configuration)).thenReturn(dslContext);

            // When
            service.deleteSchedulesForSubscriptionVersion(configuration, subscription);

            // Then
            verify(subscriptionChargeChangeScheduleDAO).deleteSchedulesForVersion(eq(SUBSCRIPTION_ID), eq(1), any(DSLContext.class));
        }
    }

    @Test
    void processScheduledStatusChanges_WithOverdueChanges_ShouldProcessThemAll() {
        UUID changeId = UUID.randomUUID();

        SubscriptionStatusChangeSchedule change = createStatusChangeSchedule(changeId, SUBSCRIPTION_ID, 1, EventType.SUBSCRIPTION_ACTIVATED);
        List<SubscriptionCharge> charges = new ArrayList<>();
        Subscription subscription = createSubscription(1, charges);

        when(subscriptionStatusChangeScheduleDAO.getUnprocessedOverdueChanges()).thenReturn(List.of(change));
        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);

        service.processScheduledStatusChanges();

        verify(subscriptionEventService).publishSubscriptionEvent(eq(subscription), any(DSLContext.class), eq(EventType.SUBSCRIPTION_ACTIVATED));
        verify(subscriptionStatusChangeScheduleDAO).markAsProcessed(eq(changeId), any(DSLContext.class));
    }

    @Test
    void processScheduledStatusChanges_SubscriptionNotFound_ShouldSkipProcessing() {
        UUID changeId = UUID.randomUUID();

        SubscriptionStatusChangeSchedule change = createStatusChangeSchedule(changeId, SUBSCRIPTION_ID, 1, EventType.SUBSCRIPTION_ACTIVATED);

        when(subscriptionStatusChangeScheduleDAO.getUnprocessedOverdueChanges()).thenReturn(List.of(change));
        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenThrow(
            new ObjectNotFoundException(BillyObjectType.SUBSCRIPTION, "Not found")
        );

        service.processScheduledStatusChanges();

        verify(subscriptionEventService, never()).publishSubscriptionEvent(any(), any(), any());
        verify(subscriptionStatusChangeScheduleDAO, never()).markAsProcessed(any(), any());
    }

    @Test
    void processScheduledStatusChanges_VersionMismatch_ShouldSkipProcessing() {
        UUID changeId = UUID.randomUUID();

        SubscriptionStatusChangeSchedule change = createStatusChangeSchedule(changeId, SUBSCRIPTION_ID, 1, EventType.SUBSCRIPTION_ACTIVATED);

        Subscription subscription = mock(Subscription.class);
        when(subscription.getSubscriptionId()).thenReturn(SUBSCRIPTION_ID);
        when(subscription.getVersion()).thenReturn(2); // Different version

        when(subscriptionStatusChangeScheduleDAO.getUnprocessedOverdueChanges()).thenReturn(List.of(change));
        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);

        service.processScheduledStatusChanges();

        verify(subscriptionEventService, never()).publishSubscriptionEvent(any(), any(), any());
        verify(subscriptionStatusChangeScheduleDAO, never()).markAsProcessed(any(), any());
    }

    @Test
    @SuppressWarnings("unchecked")
    void scheduleNewChargeChanges_WithFeatureEnabled_ShouldScheduleTasks() {
        Instant now = fixedClock.instant();
        Instant futureStart = now.plusSeconds(3600);
        Instant futureEnd = now.plusSeconds(7200);
        SubscriptionCharge charge = createCharge(CHARGE_ID_1, futureStart, futureEnd);
        Subscription subscription = createSubscription(1, List.of(charge));
        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);

        when(subscriptionChargeChangeScheduleDAO.hasUnprocessedRecordsForVersion(SUBSCRIPTION_ID, 1)).thenReturn(false);
        when(featureService.isEnabled(Feature.USE_TASKS_FOR_SUBSCRIPTION_CHANGES)).thenReturn(true);

        service.scheduleNewChanges(SUBSCRIPTION_ID);

        ArgumentCaptor<List<QueuedTaskRequest>> tasksCaptor = ArgumentCaptor.forClass(List.class);
        verify(taskDispatcher, times(2)).scheduleTasks(tasksCaptor.capture(), any());
        assertThat(
            tasksCaptor
                .getAllValues()
                .stream()
                .flatMap(Collection::stream)
                .filter(t -> t.getType().equals(CHARGE_CHANGE_TASK_TYPE))
                .collect(Collectors.toList())
        ).hasSize(2);
    }

    @Test
    void scheduleNewChargeChanges_WithFeatureDisabled_ShouldNotScheduleTasks() {
        Instant now = fixedClock.instant();
        Instant futureStart = now.plusSeconds(3600);
        Instant futureEnd = now.plusSeconds(7200);
        SubscriptionCharge charge = createCharge(CHARGE_ID_1, futureStart, futureEnd);
        Subscription subscription = createSubscription(1, List.of(charge));
        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);

        when(subscriptionChargeChangeScheduleDAO.hasUnprocessedRecordsForVersion(SUBSCRIPTION_ID, 1)).thenReturn(false);
        when(featureService.isEnabled(Feature.USE_TASKS_FOR_SUBSCRIPTION_CHANGES)).thenReturn(false);

        service.scheduleNewChanges(SUBSCRIPTION_ID);

        verify(taskDispatcher, never()).scheduleTasks(anyList(), any());
    }

    @Test
    void sendTestEventsForSubscription_ShouldSendBothChargeAndStatusEvents() {
        String notificationTargetId = "test-target-id";
        UUID changeId1 = UUID.randomUUID();
        UUID changeId2 = UUID.randomUUID();

        List<SubscriptionCharge> charges = List.of(createCharge(CHARGE_ID_1, fixedClock.instant(), fixedClock.instant().plusSeconds(3600)));
        Subscription subscription = createSubscription(1, charges);

        SubscriptionChargeChangeSchedule chargeChange = createChangeSchedule(changeId1, SUBSCRIPTION_ID, 1);
        SubscriptionStatusChangeSchedule statusChange = createStatusChangeSchedule(changeId2, SUBSCRIPTION_ID, 1, EventType.SUBSCRIPTION_ACTIVATED);

        when(subscriptionGetService.getSubscription(SUBSCRIPTION_ID)).thenReturn(subscription);
        when(subscriptionChargeChangeScheduleDAO.getChangesForSubscriptionId(SUBSCRIPTION_ID)).thenReturn(List.of(chargeChange));
        when(subscriptionStatusChangeScheduleDAO.getChangesForSubscriptionId(SUBSCRIPTION_ID)).thenReturn(List.of(statusChange));

        service.sendTestEventsForSubscription(SUBSCRIPTION_ID, notificationTargetId);

        ArgumentCaptor<List<Event>> eventsCaptor = ArgumentCaptor.forClass(List.class);
        verify(testNotificationService).sendTestNotification(eq(notificationTargetId), eventsCaptor.capture());

        List<Event> capturedEvents = eventsCaptor.getValue();
        assertThat(capturedEvents).hasSize(2);

        assertThat(capturedEvents.get(0).getType()).isEqualTo(EventType.SUBSCRIPTION_CHARGE_CHANGE);
        assertThat(capturedEvents.get(1).getType()).isEqualTo(EventType.SUBSCRIPTION_ACTIVATED);
    }

    private SubscriptionStatusChangeSchedule createStatusChangeSchedule(UUID id, String subscriptionId, int version, EventType eventType) {
        return ImmutableSubscriptionStatusChangeSchedule.builder()
            .id(id)
            .tenantId(TENANT_ID)
            .subscriptionId(subscriptionId)
            .subscriptionVersion(version)
            .changesOn(fixedClock.instant())
            .changeEventType(eventType)
            .isProcessed(false)
            .isDeleted(false)
            .build();
    }

    private SubscriptionCharge createCharge(String id, Instant start, Instant end) {
        SubscriptionCharge charge = mock(SubscriptionCharge.class);
        when(charge.getSubscriptionChargeId()).thenReturn(id);
        when(charge.getStartDate()).thenReturn(start);
        when(charge.getEndDate()).thenReturn(end);
        when(charge.getCustomFields()).thenReturn(new CustomField(Map.of()));
        return charge;
    }

    private SubscriptionChargeChangeSchedule createChangeSchedule(UUID id, String subscriptionId, int version) {
        return ImmutableSubscriptionChargeChangeSchedule.builder()
            .id(id)
            .tenantId(TENANT_ID)
            .subscriptionId(subscriptionId)
            .subscriptionVersion(version)
            .changesOn(fixedClock.instant().plusSeconds(3600))
            .chargeIdsStarting(CHARGE_ID_1)
            .chargeIdsEnding(CHARGE_ID_2)
            .isProcessed(false)
            .isDeleted(false)
            .build();
    }

    private Subscription createSubscription(int version, List<SubscriptionCharge> charges) {
        SubscriptionEntity subscriptionEntity = new SubscriptionEntity();
        subscriptionEntity.setSubscriptionId(SUBSCRIPTION_ID);
        subscriptionEntity.setVersion(version);
        subscriptionEntity.setTenantId(TENANT_ID);
        subscriptionEntity.setStartDate(fixedClock.instant().minusSeconds(3600));
        subscriptionEntity.setEndDate(fixedClock.instant().plus(Duration.ofDays(300)));
        subscriptionEntity.setCreationTime(fixedClock.instant().minusSeconds(3600));
        subscriptionEntity.setAccountId(ACCOUNT_ID);
        subscriptionEntity.setEntityId(ENTITY_ID);

        return new SubscriptionImpl(subscriptionEntity, charges);
    }
}
